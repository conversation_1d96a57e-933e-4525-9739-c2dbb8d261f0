/**
 * Or51990のEntity
 * GUI00905_課題立案様式タイトルマスタ
 *
 * <AUTHOR>
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 課題立案様式タイトルマスタ情報保存入力Entity
 */
export interface IssuesPlanningStyleTitleMasterUpdateInEntity extends InWebEntity {
  /**
   * 課題立案様式タイトルマスタリスト
   */
  issuesPlanningStyleTitleMasterList: IssuesPlanningStyleTitleMasterInfoList[]
}

/**
 * 課題立案様式タイトルマスタ情報保存出力Entity
 */
export interface IssuesPlanningStyleTitleMasterUpdateOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * エラー区分
     */
    errKbn: string
  }
}

/**
 * 課題立案様式タイトルマスタ情報
 */
export interface IssuesPlanningStyleTitleMasterInfoList {
  /**
   * 事業所ID
   */
  svJigyoId: string

  /**
   * マスタヘッダID
   */
  free1Id: string

  /**
   * フリーID
   */
  dmyFree1Id: string

  /**
   * 機能区分
   */
  kinouKbn: string

  /**
   * 様式区分
   */
  youshikiKbn: string

  /**
   * 帳票タイトル
   */
  titleKnj: string

  /**
   * 行の分割数（上）
   */
  columnCount: string

  /**
   * 初期表示の行の分割数（上）
   */
  initColumnCount: string

  /**
   * 表示順
   */
  sort: string

  /**
   * 印刷文字サイズ
   */
  fontSize: string

  /**
   * 用紙サイズ
   */
  youshiSize: string

  /**
   * 自由に使用
   */
  freeKbn: string

  /**
   * 切替区分
   */
  kirikaeKbn: string

  /**
   * 使用フラグ
   */
  useFlg: string

  /**
   * 適用フラグ
   */
  tekiyouFlg: string

  /**
   * 他適用フラグ
   */
  otherTekiyouFlg: string

  /**
   * アップデートフラグ
   */
  defUpdateFlg: string

  /**
   * アップデートフラグ1
   */
  defUpdateFlg1: string

  /**
   * 更新区分
   */
  updateKbn: string
}
