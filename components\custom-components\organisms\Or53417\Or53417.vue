<script setup lang="ts">
/**
 * Or53417:有機体:モーダル（画面/特殊コンポーネント）
 * GUI00627_［アセスメント（インターライ）マスタ］画面
 *
 * @description
 * アセスメント（インターライ）マスタ
 *
 * <AUTHOR>
 */
import { onMounted, reactive, watch, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or53417Const } from './Or53417.constants'

import type { Or53417StateType } from './Or53417.type'
import { useScreenOneWayBind, useSetupChildProps, useScreenStore } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or53417OnewayType } from '~/types/cmn/business/components/Or53417Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { Or10320Logic } from '~/components/custom-components/organisms/Or10320/Or10320.logic'
import { Or10320Const } from '~/components/custom-components/organisms/Or10320/Or10320.constants'
import type { Or10320OnewayType } from '~/types/cmn/business/components/Or10320Type'
import { Or27746Logic } from '~/components/custom-components/organisms/Or27746/Or27746.logic'
import { Or27746Const } from '~/components/custom-components/organisms/Or27746/Or27746.constants'
import type { Or27746Type } from '~/types/cmn/business/components/Or27746Type'
import type { InitialSetupMasterInfo } from '~/repositories/cmn/entities/MedicationMasterEntity'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or53417OnewayType
}

// 引継情報を取得する
const props = defineProps<Props>()

// 子コンポーネント用変数
const or21814 = ref({ uniqueCpId: '' })
const or10320 = ref({ uniqueCpId: '' })
const or27746 = ref({ uniqueCpId: '' })

// ナビゲーション制御領域のいずれかの編集フラグがON
useSystemCommonsStore().setShowEditDiscardDialog(useScreenStore().isEditNavControl())
/**
 * 入力の変更フラゲ
 */
const isEdit = computed(() => {
  let editFlg = false
  switch (local.mo00043.id) {
    case 'assessmentInterRAI':
      editFlg = useScreenStore().isEditByUniqueCpId(or10320.value.uniqueCpId)
      break
    case 'diseaseName':
      // TODO
      break
    case 'medicine':
      editFlg = useScreenStore().isEditByUniqueCpId(or27746.value.uniqueCpId)
      break
    case 'medicineUnit':
      // TODO
      break
  }
  return editFlg
})
/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or53417Const.DEFAULT.IS_OPEN,
})

// 情報ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '920px',
  persistent: true,
  showCloseBtn: false,
  mo01344Oneway: {
    name: 'Or53417',
    toolbarTitle: t('label.assessment-interRAI-master'),
    toolbarName: 'Or53417ToolBar',
    // ツールバータイトルの左寄せ
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'pa-2 pt-0',
  } as Mo01344OnewayType,
})

// 保存権限（モック）
const saveAuthority = ref('F')

// 記録画面に力が入ってい
const inputChangeFlag = ref(false)
// 処理区分（noSaveAuthority:保存権限なし、close:画面閉じる、tabClick:タブ押下）
const processCategory = ref('')
const or27746Type = ref<Or27746Type>({
  type: '1',
} as Or27746Type)

const local = reactive({
  mo00043Transfer: { id: 'assessmentInterRAI' } as Mo00043Type,
  mo00043: { id: '' } as Mo00043Type,
  /** 親画面.事業者ID */
  svJigyoId: props.onewayModelValue.svJigyoId,
  /** 親画面.施設ID */
  shisetuId: props.onewayModelValue.shisetuId,
})

/**
 * 最大薬剤階層
 */
let localIconnitialSetupMasterInfo: InitialSetupMasterInfo = {} as InitialSetupMasterInfo

const localOneway = reactive({
  or53417: {
    ...props.onewayModelValue,
  },
  mo00043Oneway: {
    tabItems: [
      {
        id: 'assessmentInterRAI',
        title: t('label.assessment-interRAI'),
        tooltipText: t('label.assessment-interRAI'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'diseaseName',
        title: t('label.disease-name'),
        tooltipText: t('label.disease-name'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'medicine',
        title: t('label.medicine'),
        tooltipText: t('label.medicine'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'medicineUnit',
        title: t('label.medicine-unit'),
        tooltipText: t('label.medicine-unit'),
        tooltipLocation: 'bottom',
      },
    ],
  } as Mo00043OnewayType,
  or10320OneWay: {
    shisetuId: '1',
    svJigyoId: '1',
  } as Or10320OnewayType,
  mo00611Oneway: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.save'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.save'),
  } as Mo00609OnewayType,
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or53417StateType>({
  cpId: Or53417Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or53417Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(() => {
  or27746Type.value.type = props.onewayModelValue.type
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or10320Const.CP_ID(1)]: or10320.value,
  [Or27746Const.CP_ID(1)]: or27746.value
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)


/**
 * 保存権限がない場合、確認メッセージを表示する
 *
 * @param errormsg - Message
 */
const showOr21814MsgTwoBtn = (errormsg: string) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 保存権限がある場合、確認メッセージを表示する
 *
 * @param confirmMsg - エラー内容
 */
const showOr21814Msg = (confirmMsg: string) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: confirmMsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * タブ「病名」押下、タブ「薬剤」押下、タブ「薬剤単位タブ」押下
 */
const tabClick = () => {
  if (local.mo00043Transfer.id !== local.mo00043.id) {
    // 該当画面に変更がある場合
    if (inputChangeFlag.value) {
      // 保存権限がある場合
      if (saveAuthority.value === 'T') {
        // 処理区分
        processCategory.value = 'tabClick'
        // 以下のメッセージを表示(q_cmn_10430)
        showOr21814Msg(t('message.q-cmn-10430'))
      } else {
        // 処理区分
        processCategory.value = 'noSaveAuthority'
        // 保存権限がない場合
        showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
      }
    } else {
      // 画面のタブに切替
      tabTitleChange()

      // ・処理終了
      return
    }
  }
}

/**
 * 画面のタイトルに切替
 */
const tabTitleChange = () => {
  local.mo00043.id = local.mo00043Transfer.id
  if (local.mo00043.id === 'assessmentInterRAI') {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle = t('label.assessment-interRAI-master')
  } else if (local.mo00043.id === 'diseaseName') {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle = t('label.disease-name-master')
  } else if (local.mo00043.id === 'medicine') {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle = t('label.medicine-master-hierarchy', [
      localIconnitialSetupMasterInfo.maxDrugLevel,
    ])
  } else if (local.mo00043.id === 'medicineUnit') {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle = t('label.medicine-unit-master')
  }
}

/**
 * AC002_「×ボタン」押下
 * AC008_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOnewayBind領域のフラグを更新する。
 */
const close = () =>  {
  // 該当画面に変更がある場合
  if (inputChangeFlag.value) {
    // 処理区分
    processCategory.value = 'close'
    // 以下のメッセージを表示(q_cmn_10430)
    showOr21814Msg(t('message.q-cmn-10430'))
  } else {
    setState({ isOpen: false })
  }
}

/**
 * AC009_「保存ボタン」押下
 * 「保存」ボタン押下
 */
const save = () => {
  if (!isEdit.value) {
    return
  }
  switch (local.mo00043.id) {
    // アセスメント（インターライ）
    case 'assessmentInterRAI':
      // Or10320のダイアログ状態を更新する
      Or10320Logic.state.set({
        uniqueCpId: or10320.value.uniqueCpId,
        state: {
          executeFlag: 'save',
        },
      })
      break
    // 病名
    case 'diseaseName':
      // TODO 本タブ担当実装コードを追加します
      break
    // 薬剤
    case 'medicine':
      // Or27746のダイアログ状態を更新する
      Or27746Logic.state.set({
        uniqueCpId: or27746.value.uniqueCpId,
        state: {
          executeFlag: 'save',
        },
      })
      break
    // 薬剤単位
    case 'medicineUnit':
      // TODO 本タブ担当実装コードを追加します
      break
    default:
      break
  }
}

/**
 * 薬剤マスタ戻り値
 *
 * @param iconnitialSetupMasterInfo - 初期設定マスタ
 */
const or27746UpdateModelValue = (iconnitialSetupMasterInfo: InitialSetupMasterInfo) => {
  if (iconnitialSetupMasterInfo) {
    localIconnitialSetupMasterInfo = {
      ...localIconnitialSetupMasterInfo,
      ...iconnitialSetupMasterInfo,
    } as InitialSetupMasterInfo
    tabClick()
  }
}

/**
 * 保存後
 */
const saveEnd = () => {
  setState({ isOpen: false })
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #toolbarRight>
      <base-mo00009
        class="mr-2"
        :oneway-model-value="{ btnIcon: 'close' }"
        @click="close"
      >
      </base-mo00009>
    </template>
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043Transfer"
        :oneway-model-value="localOneway.mo00043Oneway"
        style="padding-left: 0 !important"
        @click="tabClick"
      >
      </base-mo00043>
      <!-- タブ switch -->
      <c-v-window v-model="local.mo00043.id" style="height: 500px">
        <!-- タブ：アセスメント（インターライ）-->
        <c-v-window-item value="assessmentInterRAI">
          <g-custom-or-10320
            v-bind="or10320"
            :oneway-model-value="localOneway.or10320OneWay"
            @save-end="saveEnd"
          />
        </c-v-window-item>
        <!-- タブ：病名 -->
        <c-v-window-item value="diseaseName">
          <span>{{ t('label.disease-name') }}</span>
        </c-v-window-item>
        <!-- タブ：薬剤 -->
        <c-v-window-item value="medicine">
          <g-custom-or-27746
            v-bind="or27746"
            v-model="or27746Type"
            @update:model-value="or27746UpdateModelValue"
            @save-end="saveEnd"
          ></g-custom-or-27746>
        </c-v-window-item>
        <!-- タブ：薬剤単位 -->
        <c-v-window-item value="medicineUnit">
          <span>{{ t('label.medicine-unit') }}</span>
        </c-v-window-item>
      </c-v-window>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611Oneway"
          class="mx-2"
          @click="close"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00611Oneway.tooltipText"
            :text="localOneway.mo00611Oneway.tooltipText"
            :location="localOneway.mo00611Oneway.tooltipLocation"
            activator="parent"
          />
        </base-mo00611>

        <!-- 保存ボタン -->
        <base-mo00609
          v-bind="localOneway.mo00609Oneway"
          @click="save"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00609Oneway.tooltipText"
            :text="localOneway.mo00609Oneway.tooltipText"
            :location="localOneway.mo00609Oneway.tooltipLocation"
            activator="parent"
          />
        </base-mo00609>
      </c-v-row>
    </template>
    <g-base-or21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    >
    </g-base-or21814>
  </base-mo00024>
</template>
<style scoped lang="scss">
.content {
  border-top: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  margin: 8px 0;
  :deep(.v-col) {
    padding: 0px !important;
  }
  :deep(.v-row) {
    margin: 0;
  }
}
</style>
