import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * TeX0012:［アセスメント］画面（居宅）テンプレート
 * GUI00794_［アセスメント］画面（居宅）（1）
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace TeX0012Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('TeX0012', seq)

  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * GUI01300
     */
    export const GUI = 'GUI01300'
    /**
     * LINK_AUTH
     */
    export const LINK_AUTH = '/care-manager/hospitalization-info-offer-document'
    /**
     *メニュー２名称
     */
    export const MENU2_NAME = '[mnu2][3GK]情報連携'
    /**
     *メニュー３名称
     */
    export const MENU3_NAME = '[mnu3][3GK]入院時情報提供書'
    /**
     * 成功ステータスコード
     */
    export const SUCCESS = 'success'
    /**
     * 計画対象期間管理フラグ 0:管理しない
     */
    export const PLANNING_PERIOD_NO_MANAGE = '0'

    /**
     * 計画対象期間管理フラグ 1:管理する
     */
    export const PLANNING_PERIOD_MANAGE = '1'

    /**
     *CANCEL
     */
    export const DIALOG_RESULT_CANCEL = 'cancel'
    /**
     *YES
     */
    export const DIALOG_RESULT_YES = 'yes'
    /**
     *NO
     */
    export const DIALOG_RESULT_NO = 'no'
    /**
     * モード 12
     */
    export const SELECT_MODE_12 = '12'
    /**
     * フィルターフラグ 1
     */
    export const SELECT_MODE_1 = '1'
    /**
     * 画面モード 通常
     */
    export const MODE_NORMAL = 'normal'

    /**
     * 画面モード 複写
     */
    export const MODE_COPY = 'copy'

    /**
     * 期間処理区分 ダイアログ選択 当履歴
     */
    export const PERIOD_KBN_OPEN = '0'

    /**
     * 期間処理区分 前履歴
     */
    export const PERIOD_KBN_PREV = '1'

    /**
     * 期間処理区分 後履歴
     */
    export const PERIOD_KBN_NEXT = '2'

    /**
     * 履歴処理区分 ダイアログ選択 当履歴
     */
    export const HISTORY_KBN_OPEN = '0'

    /**
     * 履歴処理区分 前履歴
     */
    export const HISTORY_KBN_PREV = '1'

    /**
     * 履歴処理区分 後履歴
     */
    export const HISTORY_KBN_NEXT = '2'

    /**
     * 更新区分 新規
     */
    export const UPDATE_KBN_C = 'C'

    /**
     * 更新区分 更新
     */
    export const UPDATE_KBN_U = 'U'

    /**
     * 更新区分 削除
     */
    export const UPDATE_KBN_D = 'D'

    /**
     * 削除処理区分 "0"：削除処理なし
     */
    export const DELETE_PROCESS_KBN_NORMAL = '0'

    /**
     * 削除処理区分 "1"：現在表示している画面のみ削除する
     */
    export const DELETE_PROCESS_KBN_TAB = '1'

    /**
     * 削除処理区分 "2"：表示している画面を履歴ごと削除する
     */
    export const DELETE_PROCESS_KBN_ALL = '2'
  }
  /**
   *创建一个类型映射
   */
  export const typeMap: Record<string, string> = {
    hospKnjHead: 'Mo00045Type',
    sickYmd: 'Mo00020Type',
    tantoShokuId: 'string',
    shienJigyoId: 'string',
    tantoKnj: 'Mo00020Type',
    telHead: 'Mo00030Type',
    fax: 'Mo00030Type',
    teikyouYmd: 'Mo00020Type',
    houseKbn: 'string',
    houseFloor: 'Mo00038Type',
    houseRoomFloor: 'Mo00038Type',
    elevatorUmu: 'string',
    houseTokkiKnj: 'Mo00045Type',
    nyuuinYokaiKbnKakutei: 'string',
    yokaiKbn: 'string',
    ninteiStartYmd: 'Mo00020Type',
    ninteiEndYmd: 'Mo00020Type',
    ninteiShinseiFlg: 'Mo00018Type',
    ninteiShinseiYmd: 'Mo00020Type',
    ninteiKbnHenkou: 'Mo00018Type',
    kbnHenkouYmd: 'Mo00020Type',
    ninteiMishinseiFlg: 'Mo00018Type',
    shogaiJiritsuCd: 'string',
    ninchiJiritsuCd: 'string',
    ishiHandanFlg: 'Mo00018Type',
    careHandanFlg: 'Mo00018Type',
    futanWariFlg: 'Mo00018Type',
    futanWariai: 'Mo00038Type',
    shogaiNintei: 'string',
    shintaiShogaiKbn: 'Mo00018Type',
    chitekiShogaiKbn: 'Mo00018Type',
    seishinShogaiKbn: 'Mo00018Type',
    nenkin1Umu: 'Mo00018Type',
    nenkin2Umu: 'Mo00018Type',
    nenkin3Umu: 'Mo00018Type',
    nenkin4Umu: 'Mo00018Type',
    nenkin5Umu: 'Mo00018Type',
    nenkinMemoKnj: 'Mo00045Type',
    setaiKousei1: 'Mo00018Type',
    setaiKousei2: 'Mo00018Type',
    setaiKousei3: 'Mo00018Type',
    setaiKousei4: 'Mo00018Type',
    setaiKousei5: 'Mo00018Type',
    setaiKouseiMemoKnj: 'Mo00045Type',
    kaigoMainKakutei: 'Mo00045Type',
    kaigoMainZcode: 'Mo00040Type',
    kaigoMainAge: 'Mo00038Type',
    kaigoMainKousei: 'string',
    kaigoMainTel: 'Mo00045Type',
    keyPerson: 'Mo00045Type',
    keyPersonZcode: 'Mo00040Type',
    keyPersonAge: 'Mo00038Type',
    keyPersonRenrakuTel: 'Mo00030Type',
    keyPersonTel: 'Mo00045Type',
    userInfo: 'Mo00046Type',
    userSeikatureki: 'Mo00046Type',
    userIkouKnj: 'Mo00046Type',
    userIkouCksFlg: 'Mo00018Type',
    kazokuIkouKnj: 'Mo00046Type',
    kazokuIkouCksFlg: 'Mo00018Type',
    serviceRiyoKbn: 'Mo00018Type',
    serviceRiyoKbn2: 'Mo00018Type',
    serviceRiyoMemoKnj: 'Mo00045Type',
    zaitakuYokenKnj: 'Mo00046Type',
    kazokuKaigo1Umu: 'Mo00018Type',
    kazokuKaigo3Umu: 'Mo00018Type',
    kazokuKaigo2Umu: 'Mo00018Type',
    kazokuKaigo7Umu: 'Mo00018Type',
    kazokuKaigo7Ninzu: 'Mo00030Type',
    kazokuKaigo6Umu: 'Mo00018Type',
    kazokuKaigoMemoKnj: 'Mo00045Type',
    setaiHairyo: 'string',
    setaiHairyoMemoKnj: 'Mo00045Type',
    taiingoKaigoMain: 'string',
    taiingoKaigoMainName: 'Mo00045Type',
    taiingoKaigoMainZcode: 'Mo00040Type',
    taiingoKaigoMainAge: 'Mo00038Type',
    kazokuKaigo8Umu: 'Mo00018Type',
    kazokuKaigo8Kbn: 'string',
    kazokuKaigo9Umu: 'Mo00018Type',
    kazokuKaigo4Umu: 'Mo00018Type',
    gyakutaiUmu: 'string',
    gyakutaiUmuMemoKnj: 'Mo00045Type',
    tokkiKnj: 'Mo00046Type',
    hospConfSanka: 'Mo00018Type',
    leavConfSanka: 'Mo00018Type',
    leavConfMemoKnj: 'Mo00045Type',
    leavHoumonDoukou: 'Mo00018Type',
    /** 内服薬 */
    drugUmu: 'string',
    /* 内服薬メモ */
    drugMemoKnj: 'Mo00045Type',
    /* 居宅療養管理指導 */
    ryouyouKanriUmu: 'string',
    /* 居宅療養管理指導メモ */
    ryouyouKanriMemoKnj: 'Mo00045Type',
    /* 薬剤管理 */
    drugKanriKbn: 'string',
    /* 薬剤管理（管理者） */
    drugKanriKanrisya: 'Mo00045Type',
    /* 薬剤管理（管理方法） */
    drugKanriHouhou: 'Mo00045Type',
    /* 服薬状況 */
    drugJyoukyou: 'string',
    /* お薬に関する、特記事項 */
    drugTokkiKnj: 'Mo00046Type',

    /** 医療機関名 */
    hospKnj: 'Mo00045Type',
    /** 電話番号 */
    hospTel: 'Mo00030Type',
    /** 医師名フリガナ */
    doctorKana: 'Mo00045Type',
    /** 医師名 */
    doctorKnj: 'Mo00045Type',
    /** 診察方法 */
    hospHouhou: 'Mo00045Type',
    /** 診察頻度（回数） */
    hospKaisu: 'Mo00038Type',
    /** 診察頻度（単位） */
    hospTani: 'string',

    /** 麻痺の状況 */
    mahiKbn: 'string',
    /** 褥瘡の有無 */
    jyokusouUmu: 'string',
    /** ADL移動（自立） */
    adlIdou1: 'Mo00018Type',
    /** ADL移動（見守り） */
    adlIdou2: 'Mo00018Type',
    /** ADL移動（一部介助） */
    adlIdou3: 'Mo00018Type',
    /** ADL移動（全介助） */
    adlIdou4: 'Mo00018Type',
    /** ADL移乗（自立） */
    adlIjyou1: 'Mo00018Type',
    /** ADL移乗（見守り） */
    adlIjyou2: 'Mo00018Type',
    /** ADL移乗（一部介助） */
    adlIjyou3: 'Mo00018Type',
    /** ADL移乗（全介助） */
    adlIjyou4: 'Mo00018Type',
    /** ADL更衣（自立） */
    adlKoui1: 'Mo00018Type',
    /** ADL更衣（見守り） */
    adlKoui2: 'Mo00018Type',
    /** ADL更衣（一部介助） */
    adlKoui3: 'Mo00018Type',
    /** ADL更衣（全介助） */
    adlKoui4: 'Mo00018Type',
    /** ADL起居動作（自立） */
    adlKikyo1: 'Mo00018Type',
    /** ADL起居動作（見守り） */
    adlKikyo2: 'Mo00018Type',
    /** ADL起居動作（一部介助） */
    adlKikyo3: 'Mo00018Type',
    /** ADL起居動作（全介助） */
    adlKikyo4: 'Mo00018Type',
    /** ADL整容（自立） */
    adlSeiyou1: 'Mo00018Type',
    /** ADL整容（見守り） */
    adlSeiyou2: 'Mo00018Type',
    /** ADL整容（一部介助） */
    adlSeiyou3: 'Mo00018Type',
    /** ADL整容（全介助） */
    adlSeiyou4: 'Mo00018Type',
    /** ADL入浴（自立） */
    adlNyuuyoku1: 'Mo00018Type',
    /** ADL入浴（見守り） */
    adlNyuuyoku2: 'Mo00018Type',
    /** ADL入浴（一部介助） */
    adlNyuuyoku3: 'Mo00018Type',
    /** ADL入浴（全介助） */
    adlNyuuyoku4: 'Mo00018Type',
    /** ADL食事（自立） */
    adlShokuji1: 'Mo00018Type',
    /** ADL食事（見守り） */
    adlShokuji2: 'Mo00018Type',
    /** ADL食事（一部介助） */
    adlShokuji3: 'Mo00018Type',
    /** ADL食事（全介助） */
    adlShokuji4: 'Mo00018Type',
    /** 排尿（自立） */
    hainyou1: 'Mo00018Type',
    /** 排尿（見守り） */
    hainyou2: 'Mo00018Type',
    /** 排尿（一部介助） */
    hainyou3: 'Mo00018Type',
    /** 排尿（全介助） */
    hainyou4: 'Mo00018Type',
    /** 排便（自立） */
    haiben1: 'Mo00018Type',
    /** 排便（見守り） */
    haiben2: 'Mo00018Type',
    /** 排便（一部介助） */
    haiben3: 'Mo00018Type',
    /** 排便（全介助） */
    haiben4: 'Mo00018Type',
    /** ADL移動（室内）（杖） */
    adlIdouShitsunai1: 'Mo00018Type',
    /** ADL移動（室内）（歩行器） */
    adlIdouShitsunai2: 'Mo00018Type',
    /** ADL移動（室内）（車いす） */
    adlIdouShitsunai3: 'Mo00018Type',
    /** ADL移動（室内）（その他） */
    adlIdouShitsunai4: 'Mo00018Type',
    /** ADL移動手段（杖） */
    adlIdouShudan1: 'Mo00018Type',
    /** ADL移動手段（歩行器） */
    adlIdouShudan2: 'Mo00018Type',
    /** ADL移動手段（車いす） */
    adlIdouShudan3: 'Mo00018Type',
    /** ADL移動手段（その他） */
    adlIdouShudan4: 'Mo00018Type',
    /** 食事回数 */
    shokujiCnt: 'Mo00038Type',
    /** 食事回数（朝） */
    shokujiCntMorn: 'Mo01408Type',
    /** 食事回数（昼） */
    shokujiCntNoon: 'Mo01408Type',
    /** 食事回数（夜） */
    shokujiCntEven: 'Mo01408Type',
    /** 食事制限 */
    shokujiSeigen: 'string',
    /** 食事制限メモ */
    shokujiSeigenMemoKnj: 'Mo00045Type',
    /** 食事形態（普通） */
    shokujiKeitai1: 'Mo00018Type',
    /** 食事形態（きざみ） */
    shokujiKeitai2: 'Mo00018Type',
    /** 食事形態（嚥下障害食） */
    shokujiKeitai3: 'Mo00018Type',
    /** 食事形態（ミキサー） */
    shokujiKeitai4: 'Mo00018Type',
    /** 水分制限 */
    suibunSeigen: 'string',
    /** 水分制限メモ */
    suibunSeigenMemoKnj: 'Mo00045Type',
    /** 摂取方法（経口） */
    sesshuHouhou1: 'Mo00018Type',
    /** 摂取方法（経管栄養） */
    sesshuHouhou2: 'Mo00018Type',
    /** 水分とろみ */
    suibunToromi: 'string',
    /** UDF等の食形態区分 */
    udfShokujiKeitaiKbn: 'Mo00045Type',
    /** 嚥下機能 */
    engeUmu: 'string',
    /** 義歯 */
    gishiUmu: 'string',
    /** 口腔清潔 */
    koukuuCare: 'string',
    /** 口臭 */
    koushuuUmu: 'string',
    /** ポータブルトイレ */
    portableToiletUmu: 'string',
    /** オムツ/パッド */
    omutsuPadUmu: 'string',
    /** 睡眠の状態 */
    sleepJyoutai: 'string',
    /** 睡眠の状態メモ */
    sleepMemoKnj: 'Mo00045Type',
    /** 視力 */
    eyesightKbn: 'string',
    /** 喫煙量 */
    smoking: 'Mo00030Type',
    /** 飲酒量 */
    alcohol: 'Mo00030Type',
    /** 喫煙有無 */
    smokingUmu: 'string',
    /** 飲酒有無 */
    alcoholUmu: 'string',
    /** メガネ */
    glassesUmu: 'string',
    /** メガネメモ */
    glassesMemoKnj: 'Mo00045Type',
    /** 聴力 */
    hearingKbn: 'string',
    /** 補聴器 */
    hearingAidUmu: 'string',
    /** 言語 */
    languageAbility: 'string',
    /** 意思疎通 */
    comnKbn: 'string',
    /** コミュニケーションに関する特記事項 */
    comnTokkiKnj: 'Mo00046Type',
    /** 精神面における療養上の問題（なし） */
    ryouyou1Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（幻視幻聴） */
    ryouyou2Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（興奮） */
    ryouyou3Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（焦燥不穏） */
    ryouyou4Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（妄想） */
    ryouyou5Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（暴力/攻撃性） */
    ryouyou6Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（介護への抵抗） */
    ryouyou7Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（不眠） */
    ryouyou8Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（昼夜逆転） */
    ryouyou9Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（徘徊） */
    ryouyou10Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（危険行為） */
    ryouyou11Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（不潔行為） */
    ryouyou12Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（その他） */
    ryouyou13Umu: 'Mo00018Type',
    /** 精神面における療養上の問題（その他メモ） */
    ryouyouMemoKnj: 'Mo00045Type',
    /** 疾患歴（なし） */
    sick1Umu: 'Mo00018Type',
    /** 疾患歴（悪性腫瘍） */
    sick2Umu: 'Mo00018Type',
    /** 疾患歴（認知症） */
    sick3Umu: 'Mo00018Type',
    /** 疾患歴（急性呼吸器感染症） */
    sick4Umu: 'Mo00018Type',
    /** 疾患歴（脳血管障害） */
    sick5Umu: 'Mo00018Type',
    /** 疾患歴（骨折） */
    sick6Umu: 'Mo00018Type',
    /** 疾患歴（その他） */
    sick7Umu: 'Mo00018Type',
    /** 疾患歴（その他）メモ */
    sickMemoKnj: 'Mo00045Type',
    /** 最近半年間での入院 */
    nyuuinUmu: 'string',
    /** 入院理由 */
    nyuuinRiyu: 'Mo00045Type',
    /** 最近半年間での入院開始日 */
    nyuuinStartYmd: 'Mo00020Type',
    /** 最近半年間での入院終了日 */
    nyuuinEndYmd: 'Mo00020Type',
    /** 入院頻度 */
    nyuuinHindo: 'string',
    /** 医療処置（なし） */
    shochi1Umu: 'Mo00018Type',
    /** 医療処置（点滴） */
    shochi2Umu: 'Mo00018Type',
    /** 医療処置（酸素療法） */
    shochi3Umu: 'Mo00018Type',
    /** 医療処置（喀痰吸引） */
    shochi4Umu: 'Mo00018Type',
    /** 医療処置（気管切開） */
    shochi5Umu: 'Mo00018Type',
    /** 医療処置（胃ろう） */
    shochi6Umu: 'Mo00018Type',
    /** 医療処置（経鼻栄養） */
    shochi7Umu: 'Mo00018Type',
    /** 医療処置（経腸栄養） */
    shochi8Umu: 'Mo00018Type',
    /** 医療処置（褥瘡） */
    shochi9Umu: 'Mo00018Type',
    /** 医療処置（尿道カテーテル） */
    shochi10Umu: 'Mo00018Type',
    /** 医療処置（尿路ストーマ） */
    shochi11Umu: 'Mo00018Type',
    /** 医療処置（消化管ストーマ） */
    shochi12Umu: 'Mo00018Type',
    /** 医療処置（痛みコントロール） */
    shochi13Umu: 'Mo00018Type',
    /** 医療処置（排便コントロール） */
    shochi14Umu: 'Mo00018Type',
    /** 医療処置（自己注射） */
    shochi15Umu: 'Mo00018Type',
    /** 医療処置（自己注射）メモ */
    shochi15MemoKnj: 'Mo00045Type',
    /** 医療処置（その他） */
    shochi16Umu: 'Mo00018Type',
    /** 医療処置（その他）メモ */
    shochi16MemoKnj: 'Mo00045Type',
    /** 褥瘡メモ */
    jyokusouMemoKnj: 'Mo00045Type',
    /** 義歯区分 */
    gishiKbn: 'string',
    /** 眠剤の使用 */
    sleepDrugUmu: 'string',
  }
}
