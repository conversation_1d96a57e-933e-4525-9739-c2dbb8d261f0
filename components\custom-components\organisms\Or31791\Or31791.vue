<script setup lang="ts">
/**
 * Or31791:有機体:アセスメント(インターライ)画面F
 * GUI00769_アセスメント(インターライ)画面F
 *
 * @description
 * GUI00769_アセスメント(インターライ)画面F
 *
 * <AUTHOR> DAO DINH DUONG
 */
import { useI18n } from 'vue-i18n'
import { reactive, onMounted, ref, computed, watch } from 'vue'
import { cloneDeep, keys, get, isEqual } from 'lodash'
import type { Or10362Type } from '../Or10362/Or10362.type'
import type { Or31791StateType, Or31791TwoWayType, SubForm, ButtonGroup } from './Or31791.type'
import { Or31791Const } from './Or31791.constants'
import { Or31791Logic } from './Or31791.logic'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Or31791OnewayType } from '~/types/cmn/business/components/Or31791Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenInitFlg,
  useScreenStore,
  useScreenTwoWayBind,
  useValidation,
} from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type {
  AssessmentInterRAIFSelectEntity,
  AssessmentInterRAIFSelectOutEntity,
  AssessmentInterRAIFUpdateEntity,
  AssessmentInterRAIFUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIFEntity'
import type {
  AssessmentInterRAIAHistorySelectEntity,
  AssessmentInterRAIAHistorySelectOutEntity,
} from '~/repositories/cmn/entities/assessmentInterRAIAHistoryEntity'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { TransmitParam } from '~/types/cmn/business/components/TeX0003Type'
import type { Or30981OnewayType } from '~/types/cmn/business/components/Or30981Type'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo01355OnewayType } from '@/types/business/components/Mo01355Type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import { Or30981Const } from '~/components/custom-components/organisms/Or30981/Or30981.constants'

import type {
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'

/**
 * i18nの初期化関数です。
 */
const { t } = useI18n()
const { byteLength } = useValidation()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or31791OnewayType
  uniqueCpId: string
}
const screenStore = useScreenStore()

/**
 * Propsを定義します。
 */
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

/**
 * 子コンポーネント用変数（Or21814）
 */
const or21814 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })
const or30981 = ref([
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
])

/**
 * モーダルのModelValue
 */
const modelValue = ref<Or10362Type>({ isOpen: false, title: '' })
/**
 * 画面の双方向バインド用ref
 */
const { refValue } = useScreenTwoWayBind<Or31791TwoWayType[]>({
  cpId: Or31791Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})
/**
 * アセスメント種別リスト
 */
const listAssessmentKind = ref<{ value: string; label: string }[]>([])
/**
 * 汎用コード取得API実行
 *
 * @returns 戻り値はPromise<void>です。
 */
const systemCodeSelect = async (): Promise<void> => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  listAssessmentKind.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND)
}
/**
 * 画面共通情報
 *
 * @type {TransmitParam} 型はTransmitParamです。
 */
const commomInfo: TransmitParam = {
  svJigyoKnj: '',
  historyUpdateKbn: '',
  tableCreateKbn: '',
  executeFlag: '',
  kikanKanriFlg: '',
  userId: '',
  svJigyoId: '',
  kijunbiYmd: '',
  sakuseiId: '',
  raiId: '',
  deleteBtnValue: '',
  historyInfo: {} as HistoryInfoEntity,
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  assType: '',
  yokaiKbn: '',
  subInfoB: '',
  syubetsuId: '',
}

/**
 * AC023_「調査アセスメント種別」選択変更
 */
const surveyAssessmentKind = ref<string>('')

/**
 * 現在フォーカスしている部分のkeyです(デフォルト値は0)
 */
const currentFocusSectionKey = ref<number>(0)

/**
 * 現在フォーカスされている入力欄のキー(デフォルト値は0)
 */
const localOneway = reactive({
  assessmentType: '',
  or10362Oneway: {
    width: '600px',
  },
  // メモ入力イコンボタン
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  //メモ入力イコンボタンtoolTip
  btnTooltip: {
    memoInputIconBtn: t('tooltip.assessment-home-6-1-servey-ledger-import'),
    auxiliaryInputDialogBtn: t('tooltip.assessment-home-6-1-servey-ledger-import'),
  },
  //「ダイアログ回答ボタンを開く」
  answerIconBtn: {
    btnIcon: 'message',
    name: 'openDialogAnswerBtn',
    density: 'default',
  } as Mo00009OnewayType,
  // メモ入力内容
  mo00046MemoInputOnewayType: {
    name: 'memoInput',
    isVerticalLabel: true,
    showItemLabel: false,
    disabled: false,
    hideDetails: true,
    noResize: true,
    rows: 16,
    maxRows: '16',
    maxlength: '4000',
  } as Mo00046OnewayType,
  mo00038OnewayTextField: {
    min: 0,
    max: 9,
    showItemLabel: false,
    isVerticalLabel: false,
    mo00045Oneway: {
      width: '60px',
      isVerticalLabel: false,
      showItemLabel: false,
      maxLength: '1',
    } as Mo00045OnewayType,
  } as Mo00038OnewayType,
  mo00611BtnOneway: {
    width: '400px',
    class: 'button-style',
  } as Mo00611OnewayType,
  // 補助入力イコンボタン
  auxiliaryInputDialogBtn: {
    btnIcon: 'comment',
    name: 'auxiliaryInput',
    density: 'compact',
  } as Mo00009OnewayType,
  mo00045OOnewayType: {
    showItemLabel: false,
    // type: 'number',
    // hideDetails: false,
  } as Mo00045OnewayType,
  mo00039Oneway: {
    showItemLabel: false,
    hideDetails: true,
    checkOff: true,
  } as Mo00039OnewayType,
  or30981OnewayType: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * 活性/非活性制御(デフォルト: false)
     */
    disabled: false,
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      maxLength: '4000',
      rules: [byteLength(4000)],
      noResize: true,
      autoGrow: false,
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * モーダルでカラーピッカー
     */
    mo01355Oneway: {} as Mo01355OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  or51775Oneway: {
    title: t('label.memo'),
    screenId: 'GUI00769',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
const currentTypeMemo = ref('')
/**
 * AC027_「本人のケアの目標入力支援アイコンボタン」押下
 *
 * @param type - 画面項目区分
 */
const careTargetInputSupportIconClick = (type: string) => {
  currentTypeMemo.value = type
  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}
/**************************************************
 * Pinia
 **************************************************/

useScreenOneWayBind<Or31791StateType>({
  cpId: Or31791Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 画面の双方向バインドModelValueの更新
     *
     * @param value - 画面の双方向バインドModelValue
     */
    param: (value) => {
      if (value) {
        commomInfo.historyUpdateKbn = value.historyUpdateKbn
        commomInfo.tableCreateKbn = value.tableCreateKbn
        commomInfo.raiId = value.raiId
        commomInfo.svJigyoKnj = value.svJigyoKnj
        commomInfo.kikanKanriFlg = value.kikanKanriFlg
        commomInfo.planPeriodInfo = value.planPeriodInfo
        commomInfo.historyInfo = value.historyInfo
        commomInfo.userId = value.userId
        commomInfo.svJigyoId = value.svJigyoId
        commomInfo.sakuseiId = value.sakuseiId
        commomInfo.kijunbiYmd = value.kijunbiYmd
      }
      // 調査アセスメント種別設定
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          void onOpen21814()
          break
        // 新規
        case 'add':
          void clearData()
          break
        // 複写
        case 'copy':
          void onCopy()
          break
        // 削除
        case 'delete':
          void onDel()
          break
        // データ再取得
        case 'getData':
          void getInitDataInfo()
          break
        default:
          break
      }
    },
  },
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or30981Const.CP_ID(0)]: or30981.value[0],
  [Or30981Const.CP_ID(1)]: or30981.value[1],
  [Or30981Const.CP_ID(2)]: or30981.value[2],
  [Or30981Const.CP_ID(3)]: or30981.value[3],
  [Or30981Const.CP_ID(4)]: or30981.value[4],
  [Or30981Const.CP_ID(5)]: or30981.value[5],
  [Or30981Const.CP_ID(6)]: or30981.value[6],
  [Or30981Const.CP_ID(7)]: or30981.value[7],
})

/**
 * 画面初期化フラグ
 */
const isInit = useScreenInitFlg()
onMounted(async () => {
  if (isInit) {
    await systemCodeSelect()
  }
})

/**
 *  画面データをクリア
 */
const clearData = () => {
  refValue.value!.forEach((item) => {
    item.memoInputvalue = { content: '', fontSize: '', fontColor: '' }
    item.subFormList = item.subFormList.map((subItem) => {
      return {
        ...subItem,
        value: '',
      }
    })
  })
}
/**
 * 画面初期情報取得
 *
 * @returns 戻り値はPromise<void>です。
 */
const getInitDataInfo = async (): Promise<void> => {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentInterRAIFSelectEntity = {
    raiId: commomInfo.raiId,
  }
  const resData: AssessmentInterRAIFSelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIFInitSelect',
    inputData
  )

  // 画面情報を設定
  if (resData) {
    const subInfoFInfo = resData.data.subInfoF[0] as Record<string, unknown>
    const dataMapped = refValue.value!.map((item) => {
      const memoColor = subInfoFInfo[item.key + 'MemoColor']
      const memoFontSize = `${subInfoFInfo[item.key + 'MemoFont'] as string}`
      item.memoInputvalue = {
        content: subInfoFInfo[item.key + 'MemoKnj'] as string,
        fontSize: memoFontSize,
        fontColor: memoColor as string,
      }
      item.subFormList = item.subFormList.map((subItem) => {
        return {
          ...subItem,
          value: String(subInfoFInfo[item.key + subItem.key.toLocaleUpperCase()]),
        }
      })

      return item
    })
    // アセスメント種別
    surveyAssessmentKind.value = subInfoFInfo.assType as string
    localOneway.assessmentType =
      (listAssessmentKind.value as { value: string; label: string }[]).find(
        (item) => item.value === surveyAssessmentKind.value
      )?.label ?? ''
    changeBackgoundColorWithAssesmentType()
    Or31791Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: cloneDeep(dataMapped),
      isInit: true,
    })
    // watch(
    //   () => surveyAssessmentKind.value,
    //   async () => {
    //     await nextTick()
    //     changeBackgoundColorWithAssesmentType()
    //   }
    // )
  }
}
/**
 * 画面初期情報取得APIのレスポンスデータ型
 */
type AssessmentHistoryInfo = Record<string, unknown> | undefined
/**
 * 画面初期情報取得
 *
 * @returns 戻り値はPromise<AssessmentHistoryInfo>です。
 */
const getHistoryInfo = async (): Promise<AssessmentHistoryInfo> => {
  const inputData: AssessmentInterRAIAHistorySelectEntity = {
    raiId: commomInfo.historyInfo.raiId,
  }
  const resData: AssessmentInterRAIAHistorySelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIAHistorySelect',
    inputData
  )
  if (resData.statusCode === 'success') {
    return resData.data
  }
}
/**
 * 「保存ボタン」押下き確認ダイアログ
 *
 * @param message -情報を提示します
 */
const showOr21814Msg = (message: string): void => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: message,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}
/** 操作区分 */
const deleteKbn = ref<string>('')
/**
 *  保存ボタン押下、取得したインターライ方式履歴情報
 */
const onOpen21814 = async () => {
  const historyInfo = await getHistoryInfo()
  if (historyInfo) {
    // 選定アセスメント種別 > 0   AC003-2-2
    if (Number(historyInfo?.capType) > 0) {
      // 検討アセスメント種別 > 0
      if (Number(historyInfo?.plnType) > 0) {
        // msgDialog11267.value.mo00024.isOpen = true
        deleteKbn.value = '1'
        showOr21814Msg(t('message.i-cmn-11267'))
      }
      // 検討アセスメント種別 <= 0   AC003-2-3
      if (Number(historyInfo?.plnType) <= 0) {
        deleteKbn.value = '0'
        showOr21814Msg(t('message.i-cmn-11266'))
      }
    } else {
      deleteKbn.value = ''
      void onSave()
    }
  }
}
/**
 * 保存ボタン押下
 */
const onSave = async () => {
  // 更新データ作成
  const inputData: AssessmentInterRAIFUpdateEntity = {
    /** 法人ID */
    houjinId: '',
    /** 施設ID */
    shisetuId: '',
    /** 利用者ID */
    userId: commomInfo.userId,
    /** 事業者ID */
    svJigyoId: commomInfo.svJigyoId,
    /** サブ区分 */
    subKbn: Or31791Const.DEFAULT.screenCode,
    /** 更新区分 */
    updateKbn: Or31791Const.DEFAULT.typeUpdate,
    /**  履歴更新区分 */
    historyUpdateKbn: Or31791Const.DEFAULT.typeUpdate,
    /** 操作区分 */
    deleteKbn: deleteKbn.value,
    /** 基準日 */
    kijunbiYmd: commomInfo.kijunbiYmd,
    /** 作成者ID */
    sakuseiId: commomInfo.sakuseiId,
    /**  アセスメントID */
    raiId: commomInfo.historyInfo.raiId,
  }

  // 入力値処理です
  refValue.value!.forEach((item) => {
    item.subFormList.forEach((subItem) => {
      inputData[item.key + subItem.key.toLocaleUpperCase()] = subItem.value
    })
    inputData[item.key + 'MemoKnj'] = item.memoInputvalue.content
    inputData[item.key + 'MemoColor'] = item.memoInputvalue.fontColor
    inputData[item.key + 'MemoFont'] = item.memoInputvalue.fontSize
  })

  const resData: AssessmentInterRAIFUpdateOutEntity = await ScreenRepository.update(
    'assessmentInterRAIFInsert',
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === 'success') {
    console.log(inputData, 'inputData')
    // 画面情報再取得
  }
}
/**
 * AC005_「複写ボタン」押下
 */
const onCopy = () => {
  // 更新データ作成
  const inputData: AssessmentInterRAIFUpdateEntity = {
    /** 法人ID */
    houjinId: '',
    /** 施設ID */
    shisetuId: '',
    /** 利用者ID */
    userId: commomInfo.userId,
    /** 事業者ID */
    svJigyoId: commomInfo.svJigyoId,
    /** サブ区分 */
    subKbn: Or31791Const.DEFAULT.screenCode,
    /** 更新区分 */
    updateKbn: Or31791Const.DEFAULT.typeCreate,
    /**  履歴更新区分 */
    historyUpdateKbn: Or31791Const.DEFAULT.typeCreate,
    /** 操作区分 */
    deleteKbn: deleteKbn.value,
    /** 基準日 */
    kijunbiYmd: commomInfo.kijunbiYmd,
    /** 作成者ID */
    sakuseiId: commomInfo.sakuseiId,
    /**  アセスメントID */
    raiId: commomInfo.historyInfo.raiId,
  }
  // 入力値処理です
  refValue.value!.forEach((item) => {
    item.subFormList.forEach((subItem) => {
      inputData[item.key + subItem.key.toLocaleUpperCase()] = subItem.value
    })
    inputData[item.key + 'MemoKnj'] = item.memoInputvalue.content
    inputData[item.key + 'MemoColor'] = item.memoInputvalue.fontColor
    inputData[item.key + 'MemoFont'] = item.memoInputvalue.fontSize
  })
  console.log('dataCopy', inputData)
  // TODO GUI00807 アセスメント複写画面未作成
}

/**
 * AC011_「削除」押下
 */
const onDel = () => {
  // TODO 画面「A」タブに対し、更新区分を「D:削除」にする。
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      void onSave()
    }
  }
)

/**
 * アセスメント種別に応じて背景色を変更
 */
const changeBackgoundColorWithAssesmentType = (): void => {
  refValue.value!.forEach((item: Or31791TwoWayType) => {
    item.disableMemoInput = false
    item.subFormList.forEach((subItem: SubForm) => {
      subItem.disable = false
    })
    item.buttonGroupList.forEach((subItem: ButtonGroup) => {
      subItem.disable = false
    })
  })
  function disableAssessment(kind: '1' | '2' | '3') {
    const memoIndexes = Or31791Const.DEFAULT[`GrayBackgroundMemoInputIndexsAssesmentType${kind}`]
    const inputIndexesObj = Or31791Const.DEFAULT[`GrayBackgroundInputIndexsAssesmentType${kind}`]
    memoIndexes.forEach((index: number) => {
      const item = refValue.value![index]
      item.disableMemoInput = true
      item.subFormList.forEach((sub) => (sub.disable = true))
      item.buttonGroupList.forEach((btn) => (btn.disable = true))
    })

    const parent = refValue.value![inputIndexesObj.parentIndex]
    inputIndexesObj.index.forEach((index: number) => {
      parent.subFormList[index].disable = true
      parent.buttonGroupList[index].disable = true
    })
  }

  if (['1', '2', '3'].includes(surveyAssessmentKind.value)) {
    disableAssessment(surveyAssessmentKind.value as '1' | '2' | '3')
  }
}

const handleConfirmText = (data: Or51775ConfirmType) => {
  if (data.type === '0') {
    refValue.value!.find((item) => item.key === currentTypeMemo.value)!.memoInputvalue.content +=
      data.value
  } else if (data.type === '1') {
    refValue.value!.find((item) => item.key === currentTypeMemo.value)!.memoInputvalue.content =
      data.value
  }
}
// function getDifferentFields(obj1: any, obj2: any): string[] {
//   const allKeys = new Set([...keys(obj1), ...keys(obj2)])
//   const differentKeys: string[] = []

//   allKeys.forEach((key) => {
//     if (!isEqual(get(obj1, key), get(obj2, key))) {
//       differentKeys.push(key)
//     }
//   })

//   return differentKeys
// }
// const test = () => {
//   console.log(
//     getDifferentFields(screenStore.getCpTwoWayInitialValue(props.uniqueCpId)[0], refValue.value[0])
//   )
//   console.log(
//     screenStore.getCpTwoWayInitialValue(props.uniqueCpId)[0].memoInputvalue,
//     refValue.value[0].memoInputvalue
//   )
// }
// const item = {items : [{label: '1mfm', value: '1'}, {label: '1mf3m', value: '2'}]}
</script>
<template>
  <c-v-sheet class="container">
    <!-- <base-mo01372 :oneway-model-value="item"></base-mo01372> -->
    <!-- <button @click="test">click</button> -->
    <c-v-row class="pt-2">
      <div style="width: 1080px">
        <!-- アセスメント種別タイトル -->
        <div class="pr-4">
          <c-v-row class="assessment-type-title justify-end">
            {{ localOneway.assessmentType }}
          </c-v-row>
          <div class="section-title">
            {{ t('label.psychosocial-aspects') }}
          </div>
        </div>
        <!-- マトリクス領域です -->
        <div>
          <div
            v-for="(item, key) in refValue!"
            :key="key"
          >
            <div class="sub-title">{{ item.leftContentLabel }}</div>
            <div class="f-container">
              <div
                v-if="item.rightContentSubTitle"
                class="sub-f-title"
              >
                {{ item.rightContentSubTitle }}
              </div>
              <div v-if="!item.radioType">
                <div class="info-select">
                  <div
                    v-for="bntLabel in item.buttonGroupList"
                    :key="bntLabel.btnValue"
                  >
                    {{ bntLabel.btnLabel }}
                  </div>
                </div>
                <div
                  v-for="(subListItem, subListItemKey) in item.subFormList"
                  :key="subListItemKey"
                  no-gutters
                  class="select-container mt-3"
                >
                  <div>
                    {{ subListItem.label }}
                  </div>
                  <div>
                    <div class="select-style">
                      <base-at-select
                        v-model="subListItem.value"
                        :items="item.buttonGroupList"
                        item-title="btnLabel"
                        item-value="btnValue"
                        hide-details="true"
                        :class="subListItem.disable ? 'disabled' : ''"
                        style="width: 381px"
                      >
                      </base-at-select>
                    </div>
                  </div>
                </div>
              </div>
              <base-mo00039
                v-else-if="!item.multipleRadio"
                v-model="item.subFormList[0].value"
                :oneway-model-value="localOneway.mo00039Oneway"
                :class="['custom-radio', 'mt-2']"
                :style="`width: ${item?.contentWidth ?? ''}`"
              >
                <base-at-radio
                  v-for="radio in item.buttonGroupList"
                  :key="radio.btnValue"
                  :radio-label="radio.btnLabel"
                  :value="radio.btnValue"
                  :class="{
                    isChecked: item.subFormList[0].value === radio.btnValue,
                    disabled: radio.disable,
                  }"
                  :style="`width: ${radio.width}`"
                />
              </base-mo00039>
              <div v-else>
                <div
                  v-for="(subListItem, subListItemKey) in item.subFormList"
                  :key="subListItemKey"
                  no-gutters
                  :class="subListItemKey !== 0 ? 'mt-3' : ''"
                  class="select-container"
                >
                  <div>
                    {{ subListItem.label }}
                  </div>
                  <base-mo00039
                    v-model="subListItem.value"
                    :oneway-model-value="localOneway.mo00039Oneway"
                    :class="['custom-radio']"
                    :style="`width: ${item?.contentWidth ?? ''}`"
                  >
                    <base-at-radio
                      v-for="radio in item.buttonGroupList"
                      :key="radio.btnValue"
                      :radio-label="radio.btnLabel"
                      :value="radio.btnValue"
                      :class="{
                        isChecked: subListItem.value === radio.btnValue,
                        disabled: subListItem.disable,
                      }"
                      :style="`width: ${radio.width}`"
                    />
                  </base-mo00039>
                </div>
              </div>
              <div class="mt-3">
                <g-custom-or-30981
                  v-model="item.memoInputvalue"
                  v-bind="or30981[key]"
                  :oneway-model-value="{
                    ...localOneway.or30981OnewayType,
                    disabled: item.disableMemoInput,
                  }"
                  @on-click-edit-btn="careTargetInputSupportIconClick(item.key)"
                />
              </div>
            </div>
          </div>
          <!-- interRAIロゴ -->
          <c-v-row
            class="mt-4"
            no-gutters
          >
            <c-v-col cols="12">
              <c-v-img
                width="133"
                aspect-ratio="16/9"
                cover
                :src="InterRAI"
                style="float: right"
              ></c-v-img>
            </c-v-col>
          </c-v-row>
        </div>
      </div>
    </c-v-row>
  </c-v-sheet>
  <!-- ドラッグ可能なダイアログボックスE -->
  <g-custom-or-10362
    v-model="modelValue"
    :oneway-model-value="localOneway.or10362Oneway"
  >
    <template #cardItem>
      <c-v-row class="mb-2">
        <c-v-col cols="12">
          {{ refValue![currentFocusSectionKey].rightContentSubTitle }}
        </c-v-col>
        <c-v-col
          v-if="refValue![currentFocusSectionKey].btnGroupType !== 'doubleRow'"
          cols="7"
          class="bordered-red"
          name="3"
        >
          <c-v-row
            v-for="(subItem, subKey) in refValue![currentFocusSectionKey].buttonGroupList"
            :key="subKey"
            class="mb-2"
          >
            <base-mo00611
              :oneway-model-value="{
                ...localOneway.mo00611BtnOneway,
                btnLabel: subItem.btnLabel,
              }"
            />
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
  </g-custom-or-10362>
  <g-base-or21814 v-bind="or21814" />
  <!-- GUI00937_入力支援［ケアマネ］ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleConfirmText"
  />
</template>

<style scoped lang="scss">
.assessment-type-title {
  font-size: 24px;
}
.section-title {
  display: flex;
  align-items: center;
  width: 1080px;
  background-color: #fff;
  padding-left: 24px;
  height: 73px;
  color: #333333;
  font-size: 18px;
  font-weight: 700;
}
.sub-title {
  display: flex;
  align-items: center;
  height: 48px;
  font-size: 17px;
  color: #333333;
  font-weight: 700;
  padding-left: 24px;
  background-color: #e6e6e6;
}
.f-container {
  background-color: #fff;
  padding: 24px 24px 24px 48px;
  color: #333333;
  .sub-f-title {
    padding-left: 8px;
    font-weight: 700;
  }
  .info-select {
    border: #333333 1px solid;
    padding: 8px 16px;
    font-weight: 700;
    margin-top: 8px;
    line-height: 16px;
  }
  .select-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.input-container {
  position: relative;
  .button-square {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 9;
  }
  textarea {
    height: 127px !important;
  }
}
.container {
  padding: 0 8px 8px 8px;
  background-color: transparent;
  overflow: hidden;
}
.button-style {
  display: flex;
  justify-content: flex-start;
  padding-left: 8px;
}

.border-top-none {
  border-top: none !important;
}

.divider {
  height: 16px;
}

.bordered {
  border: 1px rgb(var(--v-theme-black-200)) solid;
}

.bordered-left-none {
  border-left: none;
}

.bg-white {
  background-color: rgb(var(--v-theme-white));
}

.multiline {
  white-space: pre-line;
}

.v-row {
  margin: 0px;
}

.v-col {
  padding: 8px;
}

.toGray {
  background-color: rgb(var(--v-theme-black-536)) !important;
}
.disabled {
  background-color: #f2f2f2;
}
:deep(.custom-radio) {
  .mr-1 {
    margin: 0 !important;
  }
  &.inline {
    .v-radio {
      min-width: 180px;
    }
  }
  .v-selection-control-group {
    gap: 16px;
  }
  .v-radio {
    padding: 0 8px;
    border: 1px solid #8a8a8a;
    border-radius: 4px;
    .v-label {
      width: 100%;
    }
    &.isChecked {
      background-color: #ecf3fd;
      border: 1px solid #0760e6;
    }
  }
}
:deep(.select-container) {
  .mr-2 {
    margin: 0 !important;
  }
}
</style>
