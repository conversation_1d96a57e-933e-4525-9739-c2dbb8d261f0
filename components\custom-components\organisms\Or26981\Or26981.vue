<script setup lang="ts">
/**
 * Or26981：有機体：委託の場合：計画作成事業者・事業所名及び所在地（連絡先）
 * GUI01090_基本的な事項
 *
 * @description
 * Or26981：有機体：委託の場合：計画作成事業者・事業所名及び所在地（連絡先）
 *
 * <AUTHOR> 朱征宇
 */
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26257Logic } from '../Or26257/Or26257.logic'
import { Or26257Const } from '../Or26257/Or26257.constants'
import { Or26981Const } from './Or26981.constants'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { useScreenTwoWayBind, useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo01298OnewayType } from '~/types/business/components/Mo01298Type'
import type { Or26981OnewayType, Or26981Type } from '~/types/cmn/business/components/Or26981Type'
import type { Or26257OnewayType, Or26257Type } from '~/types/cmn/business/components/Or26257Type'
import { useValidation } from '@/utils/useValidation'

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const { t } = useI18n()
const { byteLength } = useValidation()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or26981OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

const local = reactive({
  // 職員基本
  or26257: {} as Or26257Type,
})

const localOneway = reactive({
  or26981Oneway: {
    ...props.onewayModelValue,
  },
  // 業者セクション
  title2: {
    anchorPoint: '',
    color: 'rgb(var(--v-theme-background))',
    title: t('label.occasion-of-commission-name-and-location-of-the-contractor-firm'),
  } as Mo01298OnewayType,
  // 委託の場合の作成事業者センター
  itkJigyoKnjOneway: {
    maxlength: '85',
    width: '776px',
    showItemLabel: false,
    isVerticalLabel: false,
    rules: [byteLength(84)],
  } as Mo00045OnewayType,
  // 委託-計画作成者ラベル
  mo00615OnewayCommissionAuthor: {
    itemLabel: t('label.plan-author'),
  } as Mo00615OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00615OnewayType,
  // 委託先の作成者
  itakuShokuKnjOneway: {
    width: '200px',
    showItemLabel: false,
    isVerticalLabel: false,
  } as Mo00045OnewayType,
  // 職員検索画面
  or26257oneway: {} as Or26257OnewayType,
})

const or26257 = ref({ uniqueCpId: '' })

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or26981Type>({
  cpId: Or26981Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or26257Const.CP_ID(1)]: or26257.value,
})

// ダイアログ表示フラグ
const showDialogOr26257 = computed(() => {
  // Or26257のダイアログ開閉状態
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ボタン押下時の処理(Or26257)
 */
function onClickOr26257() {
  // 画面.委託先の作成者（職員ID）
  localOneway.or26257oneway.shokuinId = localOneway.or26981Oneway.itakuShokuId
  // 職員検索方式: 「12:職員検索（条件なし）択一」
  localOneway.or26257oneway.selectMode = Or26257Const.DEFAULT.SELECT_MODE_12
  // 親画面.事業所ＩＤ
  localOneway.or26257oneway.defSvJigyoId = localOneway.or26981Oneway.svJigyoId
  // 親画面.適用事業所ＩＤ
  localOneway.or26257oneway.svJigyoIdList = localOneway.or26981Oneway.jigyoIdList.map((x) => {
    return { svJigyoId: x }
  })
  // 共通情報.システム年月日(YYYY/MM/DD)
  localOneway.or26257oneway.kijunYmd = systemCommonsStore.getSystemDate ?? ''
  // 未設定フラグ: 「1:未選択あり」
  localOneway.or26257oneway.misetteiFlg = Or26257Const.DEFAULT.MISETTEI_FLG_OK
  // 表示列: shokushu_id
  localOneway.or26257oneway.hyoujiColumnList = [
    {
      hyoujiColumn: 'shokushu_id',
    },
  ]
  // Or26257のダイアログ開閉状態を更新する
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 委託先の作成者変更
 *
 * @param data - 職員基本
 */
function userChange(data: Or26257Type) {
  // 職員ID
  refValue.value!.itakuShokuId = data.shokuin.chkShokuId
  // 作成者
  refValue.value!.itakuShokuKnj = {
    value: data.shokuin.shokuin1Knj + ' ' + data.shokuin.shokuin2Knj,
  }
}
</script>

<template>
  <c-v-sheet class="view">
    <c-v-row class="operationArea">
      <!-- 事業者セクション -->
      <base-mo01298 :oneway-model-value="localOneway.title2" />
    </c-v-row>
    <c-v-row class="mt-2">
      <!-- 委託の場合の作成事業者センター -->
      <base-mo00045
        v-model="refValue!.itkJigyoKnj"
        :oneway-model-value="localOneway.itkJigyoKnjOneway"
      />
    </c-v-row>
    <c-v-row
      no-gutters
      class="mt-2"
    >
      <c-v-col
        cols="auto"
        class="display-col"
      >
        <!-- 委託作成者ラベル -->
        <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayCommissionAuthor" />
        <base-mo00009
          class="display-icon mr-2"
          :oneway-model-value="localOneway.mo00009Oneway"
          @click="onClickOr26257"
        />
        <!-- 委託先の作成者 -->
        <base-mo00045
          v-model="refValue!.itakuShokuKnj"
          :oneway-model-value="localOneway.itakuShokuKnjOneway"
          @focus="onClickOr26257"
        />
      </c-v-col>
    </c-v-row>
    <g-custom-or-26257
      v-if="showDialogOr26257"
      v-bind="or26257"
      v-model="local.or26257"
      :oneway-model-value="localOneway.or26257oneway"
      @update:model-value="userChange"
  /></c-v-sheet>
</template>

<style scoped lang="scss">
.view {
  min-width: 520px;
  display: flex;
  margin: 0px;
  padding: 0px;
  flex-direction: column;

  .v-row {
    margin: unset;
  }

  .operationArea {
    background-color: rgb(var(--v-theme-background));
    flex: 0 0 auto;

    .v-col {
      padding-left: 0;
      padding-right: 0;
      padding-top: 0;
      padding-bottom: 8px;
    }

    .v-col:last-child {
      text-align: right;
    }
  }
}
.display-col {
  display: flex;
}
.display-icon {
  top: 2px;
}
</style>
