<script setup lang="ts">
/**
 * Or51990:課題立案様式タイトルマスタモーダル
 * GUI00905_課題立案様式タイトルマスタ
 *
 * @description
 * 課題立案様式タイトルマスタモーダル
 *
 * <AUTHOR>
 */

import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  IssuesPlanningStyleTitleMasterUpdateInEntity,
  IssuesPlanningStyleTitleMasterUpdateOutEntity,
} from '~/repositories/cmn/entities/IssuesPlanningStyleTitleMasterUpdateEntity'
import type { Or51989Type } from '~/types/cmn/business/components/Or51989Type'
import type { Or51990OnewayType } from '~/types/cmn/business/components/Or51990Type'
import type {
  IssuesPlanningStyleTitleMasterInitSelectInEntity,
  IssuesPlanningStyleTitleMasterInitSelectOutEntity,
} from '../../../../repositories/cmn/entities/IssuesPlanningStyleTitleMasterInitSelectEntity'
import { Or27616Const } from '../Or27616/Or27616.constants'
import { Or51989Const } from '../Or51989/Or51989.constants'
import { OrX0097Logic } from '../OrX0097/OrX0097.logic'
import type { OrX0097Param } from '../OrX0097/OrX0097.type'
import { Or51990Const } from './Or51990.constants'
import { Or51990Logic } from './Or51990.logic'
import type { AsyncFunction, Or51990StateType } from './Or51990.type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// ローカル
const local = reactive({
  or51989: {
    editFlg: false,
    delBtnDisabled: false,
    focusIndex: '',
    issuesPlanningStyleTitleMasterList: [],
  } as Or51989Type,
  or51990: {
    youshikiKbn: '',
    kinouKbn: '',
    svJigyoId: '',
  } as Or51990OnewayType,

  focusIndex: '',
})

//課題立案区分一覧および操作ボタン
const or27616 = ref({ uniqueCpId: '' })
// 課題立案区分一覧
const or51989 = ref({ uniqueCpId: '' })
// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: '' })
//ダイアログ状態
const isClose = ref(false)

const isChangeTab = ref(false)

// Or51989 Ref
const or51989Ref = ref<{
  tableValidation(): AsyncFunction
  createRow(): AsyncFunction
  copyRow(): AsyncFunction
  deleteRow(): AsyncFunction
  init(): AsyncFunction
}>()

// Or27616 Ref
const or27616Ref = ref<{ delBtnDisable(disabled: boolean): AsyncFunction }>()

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or27616Const.CP_ID(1)]: or27616.value,
  [Or51989Const.CP_ID(1)]: or51989.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
})

/**
 * 保存ボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21813Msg(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 行追加ボタン
 */
function onAddItem() {
  or51989Ref.value?.createRow()
}
/**
 * 行複写ボタン
 */
function onCloneItem() {
  or51989Ref.value?.copyRow()
}
/**
 * 行削除ボタン
 */
function onDelete() {
  or51989Ref.value?.deleteRow()
}

useScreenOneWayBind<Or51990StateType>({
  cpId: Or51990Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (!value?.executeFlag) {
        return
      }
      local.or51990.svJigyoId = value.svJigyoId
      local.or51990.kinouKbn = value.kinouKbn
      local.or51990.youshikiKbn = value.youshikiKbn
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          if (value.isClose) {
            isClose.value = value.isClose
          }
          if (value.isChangeTab) {
            isChangeTab.value = value.isChangeTab
          }
          void save()
          break
        // データ再取得
        case 'getData':
          void init()
          break
        default:
          break
      }
      // Or51990のダイアログ状態を更新する
      Or51990Logic.state.set({
        uniqueCpId: props.uniqueCpId,
        state: {
          param: {
            executeFlag: '',
            isClose: false,
          } as OrX0097Param,
        },
      })
    },
  },
})
/** 初期情報取得 */
const init = async () => {
  // 課題立案区分マスタ情報取得(IN)
  const inputData: IssuesPlanningStyleTitleMasterInitSelectInEntity = {
    youshikiKbn: local.or51990.youshikiKbn,
    kinouKbn: local.or51990.kinouKbn,
    svJigyoId: local.or51990.svJigyoId,
  }
  // 課題立案区分マスタ初期情報取得
  const res: IssuesPlanningStyleTitleMasterInitSelectOutEntity = await ScreenRepository.select(
    'issuesPlanningStyleTitleSelect',
    inputData
  )
  if (
    (res.statusCode === Or51990Const.DEFAULT.STATUS_CODE_SUCCESS_200 ||
      res.statusCode === ResBodyStatusCode.SUCCESS) &&
    res.data.issuesPlanningStyleTitleMasterList.length > 0
  ) {
    // 画面情報を設定
    local.or51989.issuesPlanningStyleTitleMasterList = res.data.issuesPlanningStyleTitleMasterList
    local.or51989.issuesPlanningStyleTitleMasterList.forEach((item) => {
      item.svJigyoId = local.or51990.svJigyoId
    })
    await nextTick()
  }
  or51989Ref.value?.init()
}

/**
 * 保存
 *
 */
async function save() {
  await nextTick()
  local.focusIndex = ''
  //様式名入力が空白の場合
  const blankArray = local.or51989.issuesPlanningStyleTitleMasterList.filter((data, index) => {
    if (!local.focusIndex && data.titleKnj === '' && data.updateKbn !== UPDATE_KBN.DELETE) {
      local.focusIndex = index + ''
      return data
    }
  })
  if (blankArray.length > 0) {
    showOr21813Msg(t('message.e-cmn-41723'))
    return
  }
  //適用チェックボックスがチェックオンの個数をチェック
  const checkBoxCount = local.or51989.issuesPlanningStyleTitleMasterList.filter(
    (item) => item.tekiyouFlg === '1'
  ).length
  //適用チェックが0の場合
  if (checkBoxCount === 0) {
    showOr21813Msg(t('message.e-cmn-40140'))
    return
  }
  //適用チェックが1以上の場合
  if (checkBoxCount > 1) {
    showOr21813Msg(t('message.e-cmn-40159'))
    return
  }
  // 変更がある場合、処理継続
  if (local.focusIndex) {
    return
  }
  const param: IssuesPlanningStyleTitleMasterUpdateInEntity = {
    issuesPlanningStyleTitleMasterList: local.or51989.issuesPlanningStyleTitleMasterList,
  }
  // 情報保存
  const res: IssuesPlanningStyleTitleMasterUpdateOutEntity = await ScreenRepository.update(
    'issuesPlanningStyleTitleUpdate',
    param
  )

  if (
    res.statusCode === Or51990Const.DEFAULT.STATUS_CODE_SUCCESS_200 ||
    res.statusCode === ResBodyStatusCode.SUCCESS
  ) {
    if (res.data.errKbn === '1') {
      showOr21813Msg(t('message.e-cmn-40157'))
      return
    }
    if (isClose.value) {
      OrX0097Logic.state.set({
        uniqueCpId: props.parentUniqueCpId,
        state: {
          isOpen: false,
        },
      })
    } else if (isChangeTab.value) {
      isChangeTab.value = false
      return
    } else {
      await init()
    }
  }
}

/**
 * 行削除ボタン活性状態を監視
 *
 * @description
 * 活性状態を監視
 */
watch(
  () => local.or51989.delBtnDisabled,
  (newValue) => {
    or27616Ref.value?.delBtnDisable(!newValue)
  }
)
/**
 * Or21813のイベントを監視
 *
 * @description
 * またOr21813のボタン押下フラグをリセットする。
 */
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  () => {
    local.or51989.focusIndex = local.focusIndex
  }
)
</script>

<template>
  <!-- 課題立案様式タイトルマスタ一覧および操作ボタン -->
  <div class="head-btn-margin">
    <g-custom-or-27616
      ref="or27616Ref"
      v-bind="or27616"
      @on-add-item="onAddItem"
      @on-clone-item="onCloneItem"
      @on-delete="onDelete"
    />
  </div>
  <!-- 課題立案様式タイトルマスタ一覧 -->
  <g-custom-or-51989
    ref="or51989Ref"
    v-bind="or51989"
    v-model="local.or51989"
    :parent-unique-cp-id="props.uniqueCpId"
    :oneway-model-value="local.or51990"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
.head-btn-margin {
  margin-bottom: 8px !important;
}
</style>
