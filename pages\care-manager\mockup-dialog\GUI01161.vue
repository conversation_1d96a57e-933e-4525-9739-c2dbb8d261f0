<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or26835Const } from '~/components/custom-components/organisms/Or26835/Or26835.constants'
import { Or26835Logic } from '~/components/custom-components/organisms/Or26835/Or26835.logic'
import type { Or26835OnewayType, Or26835Type } from '~/types/cmn/business/components/Or26835Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo01336OnewayType } from '~/types/business/components/Mo01336Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01161'
// ルーティング
const routing = 'GUI01161/pinia'
// 画面物理名
const screenName = 'GUI01161'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or26835 = ref({ uniqueCpId: Or26835Const.CP_ID(0) })
/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01161' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or26835Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or26835.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01161',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26835Const.CP_ID(0) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or26835Const.CP_ID(0)]: or26835.value,
})

// ダイアログ表示フラグ
const showDialogOr26835 = computed(() => {
  // Or26835のダイアログ開閉状態
  return Or26835Logic.state.get(or26835.value.uniqueCpId)?.isOpen ?? false
})

// 双方向バインド初期化
const local = reactive({
  Or26835: {
    // 日数
    day: '0',
    // 短期退所日情報リスト
    tankiTeisyobiList: [],
  } as Or26835Type,
})

const or26835Data: Or26835OnewayType = {
  // 事業所ID
  shienId: '1',
  // 利用者ID
  userId: '1',
  // 提供年月
  yymmYm: '2025/06',
}

const mockData: Or26835Type = {
  day: '0',
  tankiTeisyobiList: [
    {
      svJigyoId: 'JGY001',
      jigyoKnj: { value: '介護サービス事業所A' } as Mo01337OnewayType,
      day01: '1',
      day02: '0',
      day03: '1',
      day04: '0',
      day05: '1',
      day06: '0',
      day07: '1',
      day08: '0',
      day09: '1',
      day10: '0',
      day11: '1',
      day12: '0',
      day13: '1',
      day14: '0',
      day15: '1',
      day16: '0',
      day17: '1',
      day18: '0',
      day19: '1',
      day20: '0',
      day21: '1',
      day22: '0',
      day23: '1',
      day24: '0',
      day25: '1',
      day26: '0',
      day27: '1',
      day28: '0',
      day29: '1',
      day30: '0',
      day31: '0',
      dmyTotal: { value: 15 } as Mo01336OnewayType,
      shienId: 'SUP001',
      userId: 'USR001',
      yymmYm: '202509',
      oldSvJigyoId: 'OLDJGY001',
      modifiedCnt: '2',
      updateKbn: '01',
      tableIndex: 0,
    },
    {
      svJigyoId: 'JGY002',
      jigyoKnj: { value: '介護サービス事業所B' } as Mo01337OnewayType,
      day01: '0',
      day02: '1',
      day03: '0',
      day04: '1',
      day05: '0',
      day06: '1',
      day07: '0',
      day08: '1',
      day09: '0',
      day10: '1',
      day11: '0',
      day12: '1',
      day13: '0',
      day14: '1',
      day15: '0',
      day16: '1',
      day17: '0',
      day18: '1',
      day19: '0',
      day20: '1',
      day21: '0',
      day22: '1',
      day23: '0',
      day24: '1',
      day25: '0',
      day26: '1',
      day27: '0',
      day28: '1',
      day29: '0',
      day30: '1',
      day31: '0',
      dmyTotal: { value: 16 } as Mo01336OnewayType,
      shienId: 'SUP002',
      userId: 'USR002',
      yymmYm: '202509',
      oldSvJigyoId: 'OLDJGY002',
      modifiedCnt: '1',
      updateKbn: '02',
      tableIndex: 1,
    },
  ],
};


const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const localClick = reactive({
  shienId: { value: '1' } as Mo00045Type,
  userId: { value: '1' } as Mo00045Type,
  yymmYm: { value: '2025/06' } as Mo00045Type,
})
onMounted(() => {
    local.Or26835 = mockData
    jsonInput.value = JSON.stringify(local.Or26835, null, 2);
})

/**
 *  ボタン押下時の処理(Or26835)
 */
function onClickOr26835() {
  // Or26835のダイアログ開閉状態を更新する
  Or26835Logic.state.set({
    uniqueCpId: or26835.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function onClick() {
  or26835Data.shienId = localClick.shienId.value
  or26835Data.userId = localClick.userId.value
  or26835Data.yymmYm = localClick.yymmYm.value
  Or26835Logic.state.set({
    uniqueCpId: or26835.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const jsonInput = ref('');

const updateObject = () => {
  const parsed = JSON.parse(jsonInput.value) as Or26835Type;
  if (parsed) {
    local.Or26835 = parsed;
  }
};
const jsonString = computed(() => {
  return JSON.stringify(local.Or26835, null, 2);
});
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/06/16 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26835()"
        >GUI01161_短期退所日登録
      </v-btn>
      <g-custom-or-26835
        v-if="showDialogOr26835"
        v-bind="or26835"
        v-model="local.Or26835"
        :oneway-model-value="or26835Data"
        :parent-unique-cp-id="pageComponent.uniqueCpId"
      />
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/06/16 ADD END-->
        <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">事業所ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localClick.shienId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">職員ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localClick.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">提供年月</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localClick.yymmYm"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="json-input-area">
        <h3>JSON編集</h3>
        <textarea
          v-model="jsonInput"
          placeholder="JSONを入力してください"
          rows="10"
        ></textarea>
        <v-btn  @click="updateObject">
          確定して更新JSON編集
        </v-btn>
      </div>
  <div class="pl-2 pt-5">
    <v-btn @click="onClick()"> GUI01161 疎通起動 </v-btn>
  </div>
   <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↓前画面へ返却値--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
     <div>
      <pre>{{ jsonString }}</pre>
    </div>
</template>
<style>
.json-input-area textarea {
  width: 1600px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: monospace;
}

.update-btn {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.update-btn:hover {
  background-color: #359e75;
}
</style>

