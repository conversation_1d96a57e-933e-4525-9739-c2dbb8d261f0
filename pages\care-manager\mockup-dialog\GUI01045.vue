<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  reactive,
} from '#imports'
import { Or27730Const } from '~/components/custom-components/organisms/Or27730/Or27730.constants'
import { Or27730Logic } from '~/components/custom-components/organisms/Or27730/Or27730.logic'
import type { cks52List, cks54List, cks56List } from '~/types/cmn/business/components/Or05349Type'
import type { History, NichiJyou } from '~/types/cmn/business/components/OrX0066Type'
import type { Or27730OnewayType } from '~/types/cmn/business/components/Or27730Type'
import type { Or35921Type } from '~/types/cmn/business/components/Or35921Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 * SH 2025/02/12 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI01045'
// ルーティング
const routing = 'GUI01045/pinia'
// 画面物理名
const screenName = 'GUI01045'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27730 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01045' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27730Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27730.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI01045',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27730Const.CP_ID(0) }],
})
Or27730Logic.initialize(init.childCpIds.Or27730.uniqueCpId)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27730Const.CP_ID(0)]: or27730.value,
})

// ダイアログ表示フラグ
const showDialogOr27730CksFlg1 = computed(() => {
  // Or27730 cks_flg=1 のダイアログ開閉状態
  return Or27730Logic.state.get(or27730.value.uniqueCpId)?.isOpen ?? false
})

// or22730 onewayModelValue
const or22730OnewayModel: Or27730OnewayType = {
  cksFlg: '1',
  wscksFlg: 1,
  userId: '1',
  yoKaigoDo: '1',
  cks51List: [] as History[],
  cks52List: [] as cks52List[],
  cks54List: [] as NichiJyou[],
  newCks52List: [] as cks52List[],
  newCks54List: [] as NichiJyou[],
  shuruihyoujiFlg: false,
  defSvJigyoCd: '1',
  jigyoShowFlg: 1,
  komokuShowFlg: false,
  contentShowFlg: false,
  validPeriod: '1',
}

const Or35921Model: Or35921Type = {
  orignValidId: 1,
  cks52List: [
    {
      ks51Id: '3',
      ks52Id: '1',
      youbi: '10A',
      kaishiJikan: '00:00',
      shuuryouJikan: '05:30',
      naiyouKnj: '管理者 三郎 の内容',
      fontSize: '14',
      dispMode: '1',
      alignment: '0',
      svShuruiCd: '',
      svItemCd: '',
      svJigyoId: '',
      svShuruiKnj: '',
      svItemKnj: '',
      svJigyoKnj: 'サービス事業者名称0033',
      svJigyoRyaku: '',
      fontColor: '#333333',
      backColor: '#E6E6E6',
      timeKbn: '2',
      igaiKbn: '',
      igaiMoji: '',
      igaiDate: '',
      igaiWeek: '',
      svTani: '',
      fygId: '',
      wakugaiFlg: '',
      cks56List: [
        {
          ks56Id: '1',
          ks52Id: '1',
          shokushuKnj: '',
          shokushuId: '',
        },
      ],
    },
  ] as cks52List[],
  cks54List: [
    {
      ks51Id: '3',
      ks54Id: '1',
      seq: '001',
      nichijoKnj: '日常02',
    },
    {
      ks51Id: '3',
      ks54Id: '2',
      seq: '002',
      nichijoKnj: '日常03',
    },
    {
      ks51Id: '3',
      ks54Id: '3',
      seq: '003',
      nichijoKnj: '日常04',
    },
  ] as cks54List[],
  cks56List: [
    {
      ks56Id: '1',
      ks52Id: '1',
      shokushuKnj: '',
      shokushuId: '',
    },
  ] as cks56List[],
}

/**
 *  ボタン押下時の処理(Or27730)
 *
 * @param cksFlg - 計画書様式
 */
function onClickOr27730(cksFlg: string) {
  // 引継情報.計画書様式を設定する。
  or22730OnewayModel.cksFlg = cksFlg
  // Or27730のダイアログ開閉状態を更新する
  Or27730Logic.state.set({
    uniqueCpId: or27730.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 利用者ID
  cksFlg: { value: '1' } as Mo00045Type,
})

/** GUI04473 疎通起動  */
function Or27730onClick() {
  or22730OnewayModel.cksFlg = local.cksFlg.value
  // Or07212のダイアログ開閉状態を更新する
  Or27730Logic.state.set({
    uniqueCpId: or27730.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/02/17 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27730('1')"
        >GUI01045_週間計画パターンタイトル_(計画書様式=施設,cks_flg=1)
      </v-btn>
      <g-custom-or-27730
        v-if="showDialogOr27730CksFlg1"
        v-bind="or27730"
        :or35921-model="Or35921Model"
        :oneway-model-value="or22730OnewayModel"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27730('2')"
        >GUI01045_週間計画パターンタイトル_(計画書様式=居宅,cks_flg=2)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/02/17 ADD END-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">サービス区分</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.cksFlg"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="Or27730onClick"> GUI01045 疎通起動 </v-btn>
  </div>
</template>
