import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * TeX0002:［アセスメント］画面（居宅）テンプレート
 * GUI00794_［アセスメント］画面（居宅）（1）
 * GUI00795_［アセスメント］画面（居宅）（2）
 * GUI00796_［アセスメント］画面（居宅）（3）
 * GUI00797_［アセスメント］画面（居宅）（4）
 * GUI00798_［アセスメント］画面（居宅）（5）
 * GUI00799_［アセスメント］画面（居宅）（6①）
 * GUI00800_［アセスメント］画面（居宅）（6②）
 * GUI00801_［アセスメント］画面（居宅）（6③-④）
 * GUI00802_［アセスメント］画面（居宅）（6⑤）
 * GUI00803_［アセスメント］画面（居宅）（6⑥）
 * GUI00804_［アセスメント］画面（居宅）（6医）
 * GUI00805_［アセスメント］画面（居宅）（7まとめ）
 * GUI00806_［アセスメント］画面（居宅）（7スケジュール）
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace TeX0002Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('TeX0002', seq)

  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * 計画対象期間管理フラグ 0:管理しない
     */
    export const PLANNING_PERIOD_NO_MANAGE = '0'

    /**
     * 計画対象期間管理フラグ 1:管理する
     */
    export const PLANNING_PERIOD_MANAGE = '1'

    /**
     * 改定フラグ H21/4
     */
    export const KAITEI_FLG_H21 = '4'
    /**
     * 改定フラグ R3/4
     */
    export const KAITEI_FLG_R34 = '5'

    /**
     * 画面モード 通常
     */
    export const MODE_NORMAL = 'normal'

    /**
     * 画面モード 複写
     */
    export const MODE_COPY = 'copy'

    /**
     * 履歴ID デフォルト値
     */
    export const HISTORY_ID_DEFAULT = '0'

    /**
     * 期間処理区分 ダイアログ選択 当履歴
     */
    export const PERIOD_KBN_OPEN = 'open'

    /**
     * 期間処理区分 前履歴
     */
    export const PERIOD_KBN_PREV = 'prev'

    /**
     * 期間処理区分 後履歴
     */
    export const PERIOD_KBN_NEXT = 'next'

    /**
     * 履歴処理区分 ダイアログ選択 当履歴
     */
    export const HISTORY_KBN_OPEN = 'open'

    /**
     * 履歴処理区分 前履歴
     */
    export const HISTORY_KBN_PREV = 'prev'

    /**
     * 履歴処理区分 後履歴
     */
    export const HISTORY_KBN_NEXT = 'next'

    /**
     * 更新区分 更新なし
     */
    export const UPDATE_KBN_N = 'N'

    /**
     * 更新区分 新規
     */
    export const UPDATE_KBN_C = 'C'

    /**
     * 更新区分 更新
     */
    export const UPDATE_KBN_U = 'U'

    /**
     * 更新区分 削除
     */
    export const UPDATE_KBN_D = 'D'

    /**
     * 削除処理区分 "0"：削除処理なし
     */
    export const DELETE_PROCESS_KBN_NORMAL = '0'

    /**
     * 削除処理区分 "1"：現在表示している画面のみ削除する
     */
    export const DELETE_PROCESS_KBN_TAB = '1'

    /**
     * 削除処理区分 "2"：表示している画面を履歴ごと削除する
     */
    export const DELETE_PROCESS_KBN_ALL = '2'

    /**
     * 計画期間管理取得エラー区分 1:前の計画対象期間がない
     */
    export const PLAN_PERIOD_ERROR_KBN_1 = '1'

    /**
     * 計画期間管理取得エラー区分 2:次の計画対象期間がない
     */
    export const PLAN_PERIOD_ERROR_KBN_2 = '2'

    /**
     * 職員検索画面 フィルターフラグ 1: する
     */
    export const AUTHOR_SELECT_FILTER_FLG_1 = '1'

    /**
     * モード 12
     */
    export const SELECT_MODE_12 = '12'

    /**
     * 未設定フラグ 未設定
     */
    export const MISETTEI_FLG_OK = '1'

    /**
     * 画面更新区分 0:通常
     */
    export const EVENT_KBN_NORMAL = '0'

    /**
     * 画面更新区分 1:新規
     */
    export const EVENT_KBN_CREATE = '1'

    /**
     * 画面更新区分 2:削除
     */
    export const EVENT_KBN_DELETE = '2'

    /**
     * 画面更新区分 3:利用者変更
     */
    export const EVENT_KBN_USER_CHANGE = '3'

    /**
     * 画面更新区分 4:事業所変更
     */
    export const EVENT_KBN_OFFICE_CHANGE = '4'

    /**
     * 画面更新区分 5:計画期間切替
     */
    export const EVENT_KBN_PLAN_PERIOD_CHANGE = '5'

    /**
     * 画面更新区分 6:履歴切替
     */
    export const EVENT_KBN_HISTORY_CHANGE = '6'

    /**
     * 画面更新区分 7:印刷
     */
    export const EVENT_KBN_PRINT = '7'

    /**
     * 画面更新区分 8:マスタ
     */
    export const EVENT_KBN_MASTER = '8'

    /**
     * 画面更新区分 9:タブ変更
     */
    export const EVENT_KBN_TAB_CHANGE = '9'

    /**
     * 画面更新区分 99:保存
     */
    export const EVENT_KBN_SAVE = '99'

    /**
     * 更新エラー区分 1:e-文書の履歴保存のチェックエラー
     */
    export const UPDATE_ERR_KBN_1 = '1'

    /**
     * 更新エラー区分 2:e文書出力エラー
     */
    export const UPDATE_ERR_KBN_2 = '2'

    /**
     * VISIOフラグ 0: 使用しない
     */
    export const VISIO_FLG_INVALID = '0'

    /**
     * VISIOフラグ 1: 使用する
     */
    export const VISIO_FLG_VALID = '1'

    /**
     * e-文書の履歴保存フラグ 1: 保存する
     */
    export const E_DOCUMENT_SAVE_FLG = '1'
    /**
     * クラムID 生活全般の解決すべき課題
     */
    export const COLUMN_ID_KADAI = '1'
    /**
     * クラムID 長期目標
     */
    export const COLUMN_ID_CHOUKI = '2'
    /**
     * クラムID 短期目標
     */
    export const COLUMN_ID_TANKI = '3'

    /**
     * 入力支援大分類コード 602
     */
    export const INPUT_SUPPORT_DAI_BUNRUI_CD_602 = '602'
    /**
     * 入力支援大分類コード 820
     */
    export const INPUT_SUPPORT_DAI_BUNRUI_CD_820 = '820'
    /**
     * 入力支援中分類コード 1
     */
    export const INPUT_SUPPORT_CHU_BUNRUI_CD_1 = '1'
    /**
     * 入力支援中分類コード 2
     */
    export const INPUT_SUPPORT_CHU_BUNRUI_CD_2 = '2'
    /**
     * 入力支援中分類コード 4
     */
    export const INPUT_SUPPORT_CHU_BUNRUI_CD_4 = '4'
    /**
     * 入力支援中分類コード 26
     */
    export const INPUT_SUPPORT_CHU_BUNRUI_CD_26 = '26'
    /**
     * 入力支援中分類コード 26
     */
    export const INPUT_SUPPORT_CHU_BUNRUI_CD_29 = '29'
    /**
     * 入力支援小分類コード 0
     */
    export const INPUT_SUPPORT_SHOU_BUNRUI_CD_0 = '0'
    /**
     * 入力支援小分類コード 1
     */
    export const INPUT_SUPPORT_SHOU_BUNRUI_CD_1 = '1'
    /**
     * 入力支援小分類コード 2
     */
    export const INPUT_SUPPORT_SHOU_BUNRUI_CD_2 = '2'
    /**
     * 入力支援テーブル名 cpn_tuc_gdl_kadai
     */
    export const INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL_KADAI = 'cpn_tuc_gdl_kadai'
    /**
     * 入力支援テーブル名 cpn_tuc_gdl5_kan11_h21
     */
    export const INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL5_KAN11_H21 = 'cpn_tuc_gdl5_kan11_h21'
    /**
     * 入力支援テーブル名 cpn_tuc_gdl5_kan11_r3
     */
    export const INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL5_KAN11_R3 = 'cpn_tuc_gdl5_kan11_r3'
    /**
     * 入力支援テーブル名 cpn_tuc_gdl5_kan11_h21
     */
    export const INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL4_KAN15_H21 = 'cpn_tuc_gdl4_kan15_h21'
    /**
     * 入力支援テーブル名 cpn_tuc_gdl5_kan11_r3
     */
    export const INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL5_KAN15_R3 = 'cpn_tuc_gdl5_kan15_r3'
    /**
     * 入力支援クラム名 kadai_knj
     */
    export const INPUT_SUPPORT_COLUMN_NAME_KADAI_KNJ = 'kadai_knj'
    /**
     * 入力支援クラム名 chouki_knj
     */
    export const INPUT_SUPPORT_COLUMN_NAME_CHOUKI_KNJ = 'chouki_knj'
    /**
     * 入力支援クラム名 tanki_knj
     */
    export const INPUT_SUPPORT_COLUMN_NAME_TANKI_KNJ = 'tanki_knj'
    /**
     * 入力支援クラム名 houhou_knj
     */
    export const INPUT_SUPPORT_COLUMN_NAME_HOUHOU_KNJ = 'houhou_knj'
    /**
     * 入力支援クラム名 memo1_knj
     */
    export const INPUT_SUPPORT_COLUMN_NAME_MEMO1_KNJ = 'memo1_knj'
    /**
     * 入力支援クラム名 memo2_knj
     */
    export const INPUT_SUPPORT_COLUMN_NAME_MEMO2_KNJ = 'memo2_knj'
    /**
     * 入力支援クラム名 memo3_knj
     */
    export const INPUT_SUPPORT_COLUMN_NAME_MEMO3_KNJ = 'memo3_knj'
    /**
     * 特記区分 1: 体位変換・起居
     */
    export const ITEM_KBN_POSITION_CHANGE_RISING_AND_SLEEPING =
      'POSITION_CHANGE_RISING_AND_SLEEPING'
    /**
     * 特記区分 2: 入浴
     */
    export const ITEM_KBN_BATHING = 'BATHING'
    /**
     * 特記区分 3: コミュニケーション
     */
    export const ITEM_KBN_COMMUNICATION = 'COMMUNICATION'
  }
}
