<script setup lang="ts">
/**
 * Or06632:有機体:(日課表)日課表イメージ
 * GUI00989_日課表イメージ
 *
 * @description
 * 日課表イメージ）メイン画面の処理
 *
 * <AUTHOR> 呉李彪
 */
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isDate, isUndefined } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { OrX0007Const } from '../OrX0007/OrX0007.constants'
import { OrX0008Const } from '../OrX0008/OrX0008.constants'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { OrX0010Const } from '../OrX0010/OrX0010.constants'
import { Or15843Const } from '../Or15843/Or15843.constants'
import { Or06632Const } from '../Or06632/Or06632.constants'
import type { Or06632Type, TokkiKnjInfo } from '../Or06632/Or06632.type'
import { OrX0114Const } from '../OrX0114/OrX0114.constants'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0008Logic } from '../OrX0008/OrX0008.logic'
import { OrX0114Logic } from '../OrX0114/OrX0114.logic'
import { Or28153Const } from '../Or28153/Or28153.constants'
import { Or28153Logic } from '../Or28153/Or28153.logic'
import { Or36014Const } from '../Or36014/Or36014.constants'
import { Or36014Logic } from '../Or36014/Or36014.logic'
import { Or28401Logic } from '../Or28401/Or28401.logic'
import { Or28401Const } from '../Or28401/Or28401.constants'
import type { OrX0007RefFunc } from '../Or31308/Or31308.type'
import { Or28646Const } from '../Or28646/Or28646.constants'
import { Or28646Logic } from '../Or28646/Or28646.logic'
import { Or10826Const } from '../Or10826/Or10826.constants'
import { Or10826Logic } from '../Or10826/Or10826.logic'
import { Or10827Const } from '../Or10827/Or10827.constants'
import { Or10827Logic } from '../Or10827/Or10827.logic'
import { Or30564Const } from '../Or30564/Or30564.constants'
import type { Or01533Type } from '../Or01533/Or01533.type'
import { OrX0009Logic } from '../OrX0009/OrX0009.logic'
import { OrX0010Logic } from '../OrX0010/OrX0010.logic'
import { Or15843Logic } from '../Or15843/Or15843.logic'
import {
  computed,
  useCommonProps,
  useScreenInitFlg,
  useScreenTwoWayBind,
  useSetupChildProps,
  useScreenUtils,
  useScreenStore,
  useSystemCommonsStore,
  useColorUtils,
  useJigyoList,
  hasRegistAuth,
} from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or27539Const } from '~/components/custom-components/organisms/Or27539/Or27539.constants'
import { Or27539Logic } from '~/components/custom-components/organisms/Or27539/Or27539.logic'
import { Or28528Const } from '~/components/custom-components/organisms/Or28528/Or28528.constants'
import { Or28528Logic } from '~/components/custom-components/organisms/Or28528/Or28528.logic'
import { Or28220Const } from '~/components/custom-components/organisms/Or28220/Or28220.constants'
import { Or28220Logic } from '~/components/custom-components/organisms/Or28220/Or28220.logic'
import { Or28221Const } from '~/components/custom-components/organisms/Or28221/Or28221.constants'
import { Or28221Logic } from '~/components/custom-components/organisms/Or28221/Or28221.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import type { OrX0007Type, OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import type { OrX0008Type, OrX0008OnewayType, RirekiInfo } from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009Type, OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0010Type, OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import type { Or27539OnewayType, Or27539Type } from '~/types/cmn/business/components/Or27539Type'
import type { Or28528OnewayType, Or28528Type } from '~/types/cmn/business/components/Or28528Type'
import type { Or28220OnewayType, Or28220Type } from '~/types/cmn/business/components/Or28220Type'
import type { Or28221OnewayType, Or28221Type } from '~/types/cmn/business/components/Or28221Type'
import type { OrX0114OnewayType, OrX0114Type, SvType  } from '~/types/cmn/business/components/OrX0114Type'
import type { Or15843OnewayType, Or15843Type } from '~/types/cmn/business/components/Or15843Type'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { Or00094TwowayType } from '~/components/base-components/organisms/Or00094/Or00094.type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type {
  DailyScheduleImageInitSelectOutEntity,
  IHistory,
  DailyScheduleImageInitSelectOutData,
  DailyScheduleImageInitMasterInfo
} from '~/repositories/cmn/entities/DailyScheduleImageInitSelectEntity'
import type {
  DailyScheduleImageUpdateInEntity,
} from '~/repositories/cmn/entities/DailyScheduleImageUpdateEntity'
import type {
  GyoumuComSelectInEntity, IKikanComInfo
} from '~/repositories/cmn/entities/GyoumuComSelectEntity'
import type {
  Or21814OnewayType,
  Or21814EventType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { useGyoumuCom } from '~/utils/useGyoumuCom'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import {  SPACE_WAVE,UPDATE_KBN } from '~/constants/classification-constants'
import type { GyoumuComUpdateOutEntity } from '~/repositories/cmn/entities/GyoumuComUpdateEntity'
import type { DblClickResult, OrX0099SegmentedEvent } from '~/types/cmn/business/components/OrX0099Type'
import type { Or28153Type, Or28153OnewayType } from '~/types/cmn/business/components/Or28153Type'
import type { Or36014OnewayType, Or36014Type } from '~/types/cmn/business/components/Or36014Type'
import type { DailyScheduleImageCopyReturnSelectInEntity, DailyScheduleImageCopyReturnSelectOutEntity } from '~/repositories/cmn/entities/DailyScheduleImageCopyReturnSelectEitity'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { PrintOnewayEntity, userList } from '~/repositories/cmn/entities/PrintSelectEntity'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Or21815StateType, Or21815EventType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or28646Type, WeekTableImportType } from '~/types/cmn/business/components/Or28646Type'
import type { Or10826OneWayType } from '~/types/cmn/business/components/Or10826Type'
import type { Or10827OnewayType } from '~/types/cmn/business/components/Or10827Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'

const { convertDecimalToHex, convertHexToDecimal } = useColorUtils()
// route共有情報
const cmnRouteCom = useCmnRouteCom()
const { t } = useI18n()
const { setChildCpBinds } = useScreenUtils()
/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())
//  システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const gyoumuCom = useGyoumuCom()
/**
 * jigyoListWatch
 */
const { jigyoListWatch } = useJigyoList()
/**
 * useUserListInfo
 */
const { syscomUserSelectWatchFunc } = useUserListInfo()
/**************************************************
 * 変数定義
 **************************************************/
const defaultOneway = reactive({
  or06632: {
    // 操作区分
    operaFlg: Or06632Const.ACTION_TYPE.INIT,
    // パッケージプラン改訂フラグ
    kaiteiFlg: '',
    // Input監視フラグ
    setInputComponentsFlg: false,
    // 遷移保存確認区分(0:否、1:要)
    moveSaveConfirmFlg: Or06632Const.COMFIRM_SAVE_FLG_0,
    // 削除再検索用履歴ページング番号
    rirekiPagingNo: '',
    // 履歴ＩＤ
    rirekiId: '0',
    /** 初回フラ*/
    onMountedOpenFlg: '',
    /** 検索用利用者ID */
    searchSelfUserID: '',
    /** 新選択された利用者ID */
    newSelfUserID:  '',
    /** 検索用利用者ID対応のINDEX（複帰用）*/
    searchSelUserIndex: 0,
    // 表示用「日課表詳細」リスト
    dailyInfoList: [],
    // 「内容情報」リスト
    naiyoList: [],
    // 「日課表特記事項」
    tokkiKnj: {},
    // 表示用「日課表詳細」リスト
    dailyList: [],
    // 日課表サービス例
    svInfoList:[],
    // 表示用「日課表サービス例」リスト
    svList: [],
    // 「日課表サービス例（日常）」リスト
    svDailyList: [],
    // 日課表サービス例（介護）」リスト
    svCaregiveList: [],
    // 「日課表サービス例（受託居宅）」リスト
    svEntrustedList: [],
    // 業務結果コード
    resultCd:'',
    // 種別ＩＤ
    syubetuId: '',
    // 期間管理フラグ
    kikanFlg: '',
    // 事業名（略称）
    svJigyoRyakuKnj: '',
    // 表示用「計画対象期間」情報
    kikanObj: {} as IKikanComInfo,
    // 表示用「履歴」情報
    rirekiObj: {} as IHistory,
    //初期設定マスタの情報
    initMasterObj: {
      // パッケージプラン改訂フラグ
      pkaiteiFlg: '0',
      // 日課表：メモ表示:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日
      pdayMemoFlg: '0',
      // 日課表：時間表示
      pdayTimeFlg: '1',
      // 日課表：右クリック
      pdayRbtnFlg: '1',
      // ケアプラン方式
      cpnFlg: '',
      // 敬称オプション
      keishoFlg: '',
      // 敬称
      keishoKnj: ''
    },
    kesuTorikomiFlg: '0',
    kadaiTorikomiFlg: '0',
    showFlg: true,
  } as Or06632Type,
  //期間データ
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as OrX0007OnewayType,
  //履歴
  orX0008Oneway: { createData: {} as RirekiInfo, screenID:'GUI01000' } as OrX0008OnewayType,
  //作成者
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
    isDisabled: false,
  } as OrX0009OnewayType,
  //作成日
  orX0010Oneway: { isDisabled: false , totalWidth: '113px'} as OrX0010OnewayType,
  //ケース番号
  or15843Oneway: { isRequired: false, showItemLabel: true, width: '106' } as Or15843OnewayType,
  // GUI00948 実施計画～①複写画面
  or36014: { rirekiId: '0' } as Or36014Type,
 })
// 表示中タブ名
const local = reactive({
  or06632: {} as Or06632Type,
  // 期間データ
  orX0007: {PlanTargetPeriodUpdateFlg: '' , planTargetPeriodId :''} as OrX0007Type,
  //履歴
  orX0008: {
    createId: '0',
    createUpateFlg: '',
  } as OrX0008Type,
  // 作成者
  orX0009: {staffId: '', staffName: ''} as OrX0009Type,
  // 作成日
  orX0010: {value: ''} as OrX0010Type,
  //ケース番号
  or15843: {    value: '',} as Or15843Type,
  // 日課表入力
  or27539: {} as Or27539Type,
  // 日課表
  or28153: {} as Or28153Type,
  // 日常生活活動等
  or28528: { rowNum: 0, totalCount: 0, items: [] } as Or28528Type,
  // 自立支援に関する処遇
  or28220: { rowNum: 0, totalCount: 0, items: [] } as Or28220Type,
  // 介護(介護予防)サービス
  or28221: { rowNum: 0, totalCount: 0, items: [] } as Or28221Type,
  // 特記事項、サービス例エリアタブ
  orX0114: { tokkiKnj: { value: '' }, sonotaKnj: {value :''}, svList: [] } as OrX0114Type,
  mo00043: { id: Or06632Const.TAB.TAB_ID_DAILY_TABLE_IMAGE } as Mo00043Type,
  mo00043Transfer: { id: Or06632Const.TAB.TAB_ID_DAILY_TABLE_IMAGE } as Mo00043Type,
  mo00043Change: { id: Or06632Const.TAB.TAB_ID_DAILY_TABLE_IMAGE } as Mo00043Type,
  clickEdUUid: '',
  or36014: {
    ...defaultOneway.or36014,
  } as Or36014Type,
  or28646: {} as Or28646Type,
  or01533: {} as Or01533Type,
  currentTabName: '',
  hasViewAuthflg: false,
})
const localOneway = reactive({
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or06632Const.TAB.TAB_ID_DAILY_TABLE_IMAGE, title: t('label.daily-table-image') },
      {
        id: Or06632Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES,
        title: t('label.daily-life-activities'),
      },
      {
        id: Or06632Const.TAB.TAB_ID_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT,
        title: t('label.treatment-related-to-self-reliance-support'),
      },
      {
        id: Or06632Const.TAB.TAB_ID_NURSING_PREVENTIVE_CARE_SERVICES,
        title: t('label.nursing-preventive-care-services'),
      },
      {
        id: Or06632Const.TAB.TAB_ID_SPECIAL_NOTES_SERVICES_EXAMOLES,
        title: t('label.special-notes-services-examples'),
      },
    ],
    tabClass:''
  } as Mo00043OnewayType,
  // 期間データ
  orX0007Oneway: {
    ...defaultOneway.orX0007Oneway,
  } as OrX0007OnewayType,
  // 履歴選択
  orX0008Oneway: {
    ...defaultOneway.orX0008Oneway,
  } as OrX0008OnewayType,
  // 作成者
  orX0009Oneway: {
    ...defaultOneway.orX0009Oneway,
  } as OrX0009OnewayType,
  // 作成日
  orX0010Oneway: {
    ...defaultOneway.orX0010Oneway,
  } as OrX0010OnewayType,
  // ケース番号
  or15843Oneway: {
    ...defaultOneway.or15843Oneway,
  } as Or15843OnewayType,
  // 日課表入力
  or27539Oneway: {} as Or27539OnewayType,
  or28153Oneway :{showFlg : false} as Or28153OnewayType,
  // 日常生活活動等
  or28528Oneway: {} as Or28528OnewayType,
  // 自立支援に関する処遇
  or28220Oneway: {} as Or28220OnewayType,
  // 介護(介護予防)サービス
  or28221Oneway: {} as Or28221OnewayType,
  // 特記事項、サービス例エリアタブ
  orX0114Oneway: {} as OrX0114OnewayType,
  Or00094Twoway: { selectValueArray: [Or06632Const.STR_ALL] } as Or00094TwowayType,
  // 文字サイズ
  letterSize: { radioItemsList: [] as CodeType[] },
  // 文字位置
  letterPosition: { radioItemsList: [] as CodeType[] },
  // 時間表示
  timeDisplay: { radioItemsList: [] as CodeType[] },
  // GUI00948 実施計画～①複写画面
  or36014Oneway: {} as Or36014OnewayType,
  or28401Oneway: {} as PrintOnewayEntity<DailyScheduleImageInitMasterInfo>,
  or28646Data: {} as WeekTableImportType,
  or10826Oneway: {} as Or10826OneWayType,
  or10827Oneway: {} as Or10827OnewayType,
  // 週間取込
  mo00611Oneway: {
    btnLabel: t('label.week-import'),
    tooltipText: t('tooltip.week-import')
  } as Mo00611OnewayType,
  // パターン
  mo00611OnewayPattern: {
    btnLabel: t('label.pattern'),
    tooltipText: t('tooltip.week-table-pattern-btn'),
    width: '78px',
    minWidth:'78px'
  } as Mo00611OnewayType,
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})

const orHeadLine = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const or11871 = ref({ uniqueCpId: '' })
// 期間データ
const orX0007_1 = ref({ uniqueCpId: '' })
// 履歴選択
const orX0008_1 = ref({ uniqueCpId: '' })
// 作成者
const orX0009_1 = ref({ uniqueCpId: '' })
// 作成日
const orX0010_1 = ref({ uniqueCpId: '' })
// ケース番号
const or15843_1 = ref({ uniqueCpId: '' })
const or28153_1 = ref({ uniqueCpId: '' })
// 日常生活活動等
const or28528_1 = ref({ uniqueCpId: '' })
// 自立支援に関する処遇
const or28220_1 = ref({ uniqueCpId: '' })
// 介護(介護予防)サービス
const or28221_1 = ref({ uniqueCpId: '' })
// 特記事項、サービス例エリアタブ
const orX0114_1 = ref({ uniqueCpId: '' })
const or36014_1 = ref({ uniqueCpId: '' })
// 日課表入力
const or27539_1 = ref({ uniqueCpId: '' })
const or28401_1 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
/** 事業所選択プルダウン */
const or41179_1 = ref({ uniqueCpId: '' })
// 週間表取込
const or28646_1 = ref({ uniqueCpId: '' })
const or10826_1 = ref({ uniqueCpId: '' })
const or10827_1 = ref({ uniqueCpId: '' })
const orX0007Ref = ref<OrX0007RefFunc | null>(null)
const orX0114Ref = ref<{
  isValid: () => Promise<boolean>
}>()
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [OrX0007Const.CP_ID(1)]: orX0007_1.value,
  [OrX0008Const.CP_ID(1)]: orX0008_1.value,
  [OrX0009Const.CP_ID(1)]: orX0009_1.value,
  [OrX0010Const.CP_ID(1)]: orX0010_1.value,
  [Or15843Const.CP_ID(1)]: or15843_1.value,
  [Or27539Const.CP_ID(1)]: or27539_1.value,
  [Or28153Const.CP_ID(1)]: or28153_1.value,
  // 日常生活活動等
  [Or28528Const.CP_ID(1)]: or28528_1.value,
  // 自立支援に関する処遇
  [Or28220Const.CP_ID(1)]: or28220_1.value,
  // 介護(介護予防)サービス
  [Or28221Const.CP_ID(1)]: or28221_1.value,
  // 特記事項、サービス例エリアタブ
  [OrX0114Const.CP_ID(1)]: orX0114_1.value,
  [Or28401Const.CP_ID(1)]: or28401_1.value,
  [Or36014Const.CP_ID(1)]: or36014_1.value,
  [Or21813Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
  [Or41179Const.CP_ID(1)]: or41179_1.value,
  // 週間表取込
  [Or28646Const.CP_ID(1)]: or28646_1.value,
  [Or10826Const.CP_ID(1)]: or10826_1.value,
  [Or10827Const.CP_ID(1)]: or10827_1.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
})

Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.daily-table'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: true,
    tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    showMasterBtn: true,
    showOptionMenuDelete: true,
    tooltipTextSaveBtn: t('tooltip.care-plan2-save-btn'),
  },
})

const isInit = useScreenInitFlg()
onMounted(async () => {
  // システム共有情報
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or06632Const.STR_ALL })
  })

  // 初期情報取得
  if (isInit) {
    // システム共有情報利用者設定
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: local.or06632.searchSelfUserID,
        },
        verticalLayout: true,
        labelClassVertical: 'mb-1 text-center',
        selectClassVertical: 'mt-1'
      },
    })
    await initData()
  }
})

// ダイアログ表示フラグ
const showDialogOr27539 = computed(() => {
  // Or27539のダイアログ開閉状態
  return Or27539Logic.state.get(or27539_1.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr36014 = computed(() => {
  return Or36014Logic.state.get(or36014_1.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr28401 = computed(() => {
  return Or28401Logic.state.get(or28401_1.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { favoriteEventFlg: false },
      })
    }
    if (newValue.saveEventFlg) {
      await _save()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { saveEventFlg: false },
      })
    }
    if (newValue.createEventFlg) {
      await _create()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { createEventFlg: false },
      })
    }
    if (newValue.printEventFlg) {
     await _print()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { printEventFlg: false },
      })
    }
    if (newValue.masterEventFlg) {
      await _master()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { masterEventFlg: false },
      })
    }
    if (newValue.deleteEventFlg) {
      await _delete()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { deleteEventFlg: false },
      })
    }
    if (newValue.copyEventFlg) {
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { copyEventFlg: false },
      })
    }
  }
)
/**
 * 事業所選択更新の監視
 */
// ★事業所選択監視関数を実行
jigyoListWatch(or41179_1.value.uniqueCpId, (newJigyoId: string) => {
  void callbackFuncJigyo(newJigyoId)
})
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function callbackFuncJigyo(newJigyoId: string) {
  const oldJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  await gyoumuCom.doComLogicChangeSvJigyo(
    newJigyoId,
    oldJigyoId,
    local.or06632,
    isEdit.value,
    await hasRegistAuth(Or06632Const.LINK_AUTH),
    showConfirmMessageBox,
    showWarnMessageBox,
    _save,
    getCarePlanTable,
    setCommonPlanPeriod,
    setSvJigyoId,
    setSelectSelUserIndex
  )
}
/**
 * Input の 監視
 */
watch(
  () => local.or06632.setInputComponentsFlg,
  (newValue) => {
    if (newValue) {
      //Input
      setChildCpBinds(props.uniqueCpId, {
        OrX0007_1: {
          twoWayValue: {
            planTargetPeriodId: local.orX0007.planTargetPeriodId,
            PlanTargetPeriodUpdateFlg: local.orX0007.PlanTargetPeriodUpdateFlg,
          },
        },
        OrX0008_1: {
          twoWayValue: {
            createId: local.orX0008.createId,
            createUpdateFlg: local.orX0008.createUpateFlg,
          },
        },
        OrX0009_1: {
          twoWayValue: {
            staffId: local.orX0009.staffId,
            staffName: local.orX0009.staffName,
          },
        },
        OrX0010_1: {
          twoWayValue: {
            value: local.orX0010.value,
          },
          oneWayState: {
            isRequired: true
          }
        },
        // ケース番号
        Or15843_1: {
          twoWayValue: {
            value: local.or15843.value,
          },
        },
        [Or28153Const.CP_ID(1)]: {
          twoWayValue: {
            dailyInfoList:local.or28153.dailyInfoList,
          },
        },
        [Or28528Const.CP_ID(1)]: {
          twoWayValue: {
            items:local.or28528.items,
          },
        },
        [Or28220Const.CP_ID(1)]: {
          twoWayValue: {
            items: local.or28220.items,
          },
        },
        [Or28221Const.CP_ID(1)]: {
          twoWayValue: {
            items: local.or28221.items,
          },
        },
        [OrX0114Const.CP_ID(1)]: {
          oneWayState: localOneway.orX0114Oneway,
          twoWayValue: local.orX0114,
        },
      })
      local.or06632.setInputComponentsFlg = false
    }
  }
)

/**
 * 初期化処理
 */
const initData = async () => {
  //操作区分 ＝ 0:初期化
  local.or06632.operaFlg = Or06632Const.ACTION_TYPE.INIT
  local.hasViewAuthflg = true
  // 汎用コードマスタデータ
  await initCodes()
  //データ取得
  await getCarePlanTable()
}

/**
 * 事業所設定
 *
 * @param jigyoId - 設定の事業者ID
 */
const setSvJigyoId = (jigyoId: string) => {
  systemCommonsStore.setSvJigyoId(jigyoId)
  Or41179Logic.data.set({
    uniqueCpId: or41179_1.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    } as Mo00040Type,
  })
}

/**
 * 保存処理を行う。
 */
async function saveApiData() {
  // 履歴情報
  const rirekiObj = local.or06632.rirekiObj
  // 履歴更新区分の設定
  let rireki_updateKbn = ''
  // 操作区分が3:削除の場合、保存用情報.履歴IDが空白以外の場合
  if (local.or06632.operaFlg === Or06632Const.OPERA_FLG_3 && rirekiObj.rirekiId) {
    rireki_updateKbn = UPDATE_KBN.DELETE
  } else if (local.or06632.operaFlg === Or06632Const.OPERA_FLG_1 || !rirekiObj.rirekiId || rirekiObj.rirekiId === '0') {
    rireki_updateKbn = UPDATE_KBN.CREATE
  } else {
    rireki_updateKbn = UPDATE_KBN.UPDATE
  }

  // 計画対象期間情報
  const kikanObj = local.or06632.kikanObj
  const inputParam: DailyScheduleImageUpdateInEntity = {
    ...gyoumuCom.getGyoumuComUpdateInEntity(
      local.or06632.operaFlg,
      local.or06632.kikanFlg,
      local.or06632.syubetuId,
      cmnRouteCom.getPlanPeriod()?.kikanId
    ),
    rirekiId: local.or06632.rirekiId,
    kaiteiFlg:local.or06632.kaiteiFlg ?? '2',
    kikanObj: {
      ...kikanObj,
      updateKbn: rireki_updateKbn,
    },
    tokkiKnjInfo: {} as TokkiKnjInfo,
    detailsList: [],
    svList:[],
    //初期設定マスタの情報
    initMasterObj: local.or06632.initMasterObj,
  }

  if (local.or06632.dailyInfoList) {
    local.or06632.dailyInfoList.forEach((item) => {
       inputParam.detailsList?.push({
        // カウンタ
        id: item.id,
        /** 区分 */
        dataKbn: item.dataKbn,
        /** 開始時間 */
        startTime: item.startTime,
        /** 終了時間 */
        endTime: item.endTime,
        /** 内容CD */
        naiyoCd: item.naiyoCd,
        /** 日常処遇サービスメモ */
        memoKnj: item.memoKnj,
        /** 担当者 */
        tantoKnj: item.tantoKnj,
        /** 文字サイズ */
        fontSize: item.fontSize,
        /** 文字位置 */
        alignment: item.alignment,
        /** 文字カラー */
        fontColor: item.fontColor ? convertHexToDecimal(item.fontColor) : '',
        /** 背景カラー */
        backColor: item.fontColor ? convertHexToDecimal(item.backColor): '',
        /** 時間表示区分 */
        timeKbn: item.timeKbn,
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
        /** 更新区分 */
        updateKbn: item.updateKbn,
       })
     })
  }
  inputParam.tokkiKnjInfo = {
      // ＩＤ
      id: local.or06632.rirekiId,
      /** 作成日 */
      createYmd: localOneway.orX0009Oneway.createData?.createDate,
      // 作成者
      shokuId: localOneway.orX0009Oneway.createData?.createId?.toString(),
      // ケースNo.
      caseNo: local.or15843.value,
      // 特記事項
      tokkiKnj: local.or06632.tokkiKnj.tokkiKnj,
      // 随時実施するその他のサービス.
      sonotaKnj: local.or06632.tokkiKnj.sonotaKnj,
      // 改訂フラグ
      kaiteiFlg: local.or06632.kaiteiFlg ?? '2',
      // 更新回数
      modifiedCnt: local.or06632.tokkiKnj.modifiedCnt,
      // 更新区分
      updateKbn: rireki_updateKbn,
  }
  if (local.or06632.svInfoList) {
    local.or06632.svInfoList.forEach((item) => {
       inputParam.svList?.push({
        // カウンタ
        svId: item.svId,
        /** 区分 */
        dataKbn: item.dataKbn,
        /** 適用フラグ */
        tekiyoFlg: item.tekiyoFlg,
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
        /** 内容CD */
        updateKbn: item.updateKbn,
       })
     })
  }
  //期間管理フラグが0:期間管理しない場合
  if (local.or06632.kikanFlg === '0') {
    //表示用「計画対象期間」情報.計画期間IDが空白の場合
    if (kikanObj.sc1Id === '' || kikanObj.sc1Id === '0') {
      inputParam.kikanObj = {
        sc1Id: kikanObj?.sc1Id ?? '',
        //画面の作成日
        startYmd: String(OrX0010Logic.data.get(orX0010_1.value.uniqueCpId)?.value),
        endYmd: kikanObj?.endYmd ?? '',
        modifiedCnt: kikanObj?.modifiedCnt ?? '',
        updateKbn: UPDATE_KBN.CREATE,
      }
    }
  }  //画面情報の保存処理を行う。
  const ret: GyoumuComUpdateOutEntity = await ScreenRepository.update('dailyScheduleImageUpdate', inputParam)
  return ret
}
/**
 * メッセージを表示する
 *
 * @param messageId - メッセージ
 */
 const showMessageBox = async (messageId: string) => {
  await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.error'),
    // ダイアログテキスト
    dialogText: t(messageId),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.ok'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
 }

 /**
  * メッセージを表示する
  */
const showConfirmMessgeBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}
/**
 * 共通情報.計画期間を設定
 *
 * @param kikanObj - 計画期間
 */
 const setCommonPlanPeriod = (kikanObj: IKikanComInfo) => {
  cmnRouteCom.setPlanPeriod({
    kikanId: kikanObj.sc1Id,
    kikanIndex: kikanObj.pagingNo!,
    startYmd: kikanObj.startYmd!,
    endYmd: kikanObj.endYmd!,
  })
 }

/**
 * 計画期間変更の監視
 */
 watch(
  () => OrX0007Logic.data.get(orX0007_1.value.uniqueCpId),
   async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }

    const planID = newValue.planTargetPeriodId
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg

    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }
    //ポップアップ画面での返回値.計画期間ＩＤが0より大きい場合
    if (planUpdateFlg === Or06632Const.ACTION_UPDATE_FLG_0 && Number(planID) > 0) {
      //共通情報.計画期間ＩＤ = 返回値.計画期間ＩＤ
      setCommonPlanPeriod({ ...local.or06632.kikanObj, sc1Id: String(planID) })
      //共通情報.履歴ＩＤ ＝ 0
      local.or06632.rirekiId = '0'
      //操作区分 ＝ K3:計画対象期間Open
      local.or06632.operaFlg = Or06632Const.ACTION_TYPE.K3
      //データ再取得
      await getCarePlanTable()
    } else if (planUpdateFlg === Or06632Const.ACTION_UPDATE_FLG_1) {
      //操作区分
      local.or06632.operaFlg = Or06632Const.ACTION_TYPE.K1
      //共通情報.履歴ＩＤ ＝ 0
      local.or06632.rirekiId = '0'
      //遷移保存確認区分
      local.or06632.moveSaveConfirmFlg = isEdit.value ? Or06632Const.COMFIRM_SAVE_FLG_1 : Or06632Const.COMFIRM_SAVE_FLG_0
      //データ再取得
      await getCarePlanTable()
    } else if (planUpdateFlg === Or06632Const.ACTION_UPDATE_FLG_2) {
      //操作区分
      local.or06632.operaFlg = Or06632Const.ACTION_TYPE.K2
      //共通情報.履歴ＩＤ ＝ 0
      local.or06632.rirekiId = '0'
      //遷移保存確認区分
      local.or06632.moveSaveConfirmFlg = isEdit.value ? Or06632Const.COMFIRM_SAVE_FLG_1 : Or06632Const.COMFIRM_SAVE_FLG_0
      //データ再取得
      await getCarePlanTable()
    }
    OrX0007Logic.data.set({
      uniqueCpId: orX0007_1.value.uniqueCpId,
      value: { planTargetPeriodId: '', PlanTargetPeriodUpdateFlg: '' },
    })
  },
  { deep: true }
 )

 /**
  * AC018-3履歴変更の監視
  */
watch(
  () => OrX0008Logic.data.get(orX0008_1.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }

    const createUpateFlg = newValue.createUpateFlg

    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }

    if (createUpateFlg === Or06632Const.ACTION_UPDATE_FLG_0) {
      //共通情報.履歴ＩＤ = 返回値.履歴ＩＤ
      local.or06632.rirekiId = String(newValue.createId)
      //操作区分
      local.or06632.operaFlg = Or06632Const.ACTION_TYPE.R3
      // 表示用「履歴」情報を設定する
      local.or06632.rirekiObj.shokuId = newValue.rirekiObj?.staffId ?? ''
      local.or06632.rirekiObj.shokuKnj = newValue.rirekiObj?.staffName ?? ''
      local.or06632.rirekiObj.createYmd = newValue.rirekiObj?.createDate ?? ''
      local.or06632.rirekiObj.pagingNo = String(newValue.rirekiObj?.currentIndex ?? 0)
      local.or06632.rirekiObj.pagingCnt = String(newValue.rirekiObj?.totalCount ?? 0)
      //データ再取得
      await getCarePlanTable()
    } else if (createUpateFlg === Or06632Const.ACTION_UPDATE_FLG_1) {
      //操作区分
      local.or06632.operaFlg = Or06632Const.ACTION_TYPE.R1
      //遷移保存確認区分
      local.or06632.moveSaveConfirmFlg = isEdit.value ? Or06632Const.COMFIRM_SAVE_FLG_1 : Or06632Const.COMFIRM_SAVE_FLG_0
      //データ再取得
      await getCarePlanTable()
    } else if (createUpateFlg === Or06632Const.ACTION_UPDATE_FLG_2) {
      //操作区分
      local.or06632.operaFlg = Or06632Const.ACTION_TYPE.R2
      //遷移保存確認区分
      local.or06632.moveSaveConfirmFlg = isEdit.value ? Or06632Const.COMFIRM_SAVE_FLG_1 : Or06632Const.COMFIRM_SAVE_FLG_0
      //データ再取得
      await getCarePlanTable()
    }
    OrX0008Logic.data.set({
      uniqueCpId: orX0008_1.value.uniqueCpId,
      value: {
        createId: '',
        createUpateFlg: '',
        rirekiObj: undefined,
      },
    })
  },
  { deep: true }
)

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // コード区分マスタID: 文字サイズ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DAILY_LETTER_SIZE },
    // コード区分マスタID: 文字位置
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DAILY_LETTER_POSITION },
    // コード区分マスタID: 時間表示
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TIME_DISPLAY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 文字サイズ
  localOneway.letterSize.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DAILY_LETTER_SIZE
  )
  // 文字位置
  localOneway.letterPosition.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DAILY_LETTER_POSITION
  )
  // 時間表示
  localOneway.timeDisplay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TIME_DISPLAY
  )
}

/**
 * 画面表示のデータを取得
 */
const getCarePlanTable = async () => {
  const inputParam: GyoumuComSelectInEntity = {
    ...gyoumuCom.getGyoumuComSelectInEntity(
      local.or06632.operaFlg,
      local.or06632.moveSaveConfirmFlg,
      local.or06632.rirekiPagingNo,
      cmnRouteCom.getPlanPeriod()?.kikanId
    ),
    rirekiId:
    local.or06632.rirekiId && local.or06632.rirekiId !== '' ? local.or06632.rirekiId : '0',
  }

  const ret: DailyScheduleImageInitSelectOutEntity = await ScreenRepository.select(
    'dailyScheduleImageInitSelect',
    inputParam
  )

    //API戻り値の設定前の処理
  await gyoumuCom.doComLogicSelectApi(
    ret,
    await hasRegistAuth(Or06632Const.LINK_AUTH),
    showMessageBox,
    showConfirmMessgeBox,
    showWarnMessageBox,
    _save,
    getCarePlanTable,
    local.or06632,
    {
      tokkiKnjInfo: ret.data.tokkiKnjInfo ?? {},
      naiyoList: ret.data.naiyoList ?? [],
      dailyList: ret.data.dailyList ?? [],
      svList: ret.data.svList ?? [],
      svDailyList: ret.data.svDailyList ?? [],
      svCaregiveList: ret.data.svCaregiveList ?? [],
      svEntrustedList: ret.data.svEntrustedList ?? [],
      initMasterObj: ret.data.initMasterObj ?? {},
    } as DailyScheduleImageInitSelectOutData,
    setCommonPlanPeriod
  )

  // 画面コントロール表示設定
  initOr06632(ret)
  // 日常生活活動等
  initGui00991(ret)
  // 自立支援に関する処遇
  initGui00992(ret)
  // 介護(介護予防)サービス
  initGui00993(ret)
  // 特記事項、サービス例エリアタブ
  initGui00994()
  local.or06632.setInputComponentsFlg = true
}

/**
 * 画面コントロール表示設定
 *
 * @param ret - apiデータ
 */
function initOr06632(ret: DailyScheduleImageInitSelectOutEntity) {
  //APIからの初期データ
  const kikanObj = local.or06632.kikanObj
  const rirekiObj = local.or06632.rirekiObj
  //種別ID
  localOneway.orX0007Oneway.kindId = local.or06632.syubetuId
  if (kikanObj.pagingFlg !== Or06632Const.PLAN_TARGET_PERIOD_0) {
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = kikanObj.pagingNo
      ? Number(kikanObj.pagingNo)
      : 0
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = kikanObj.pagingCnt
      ? Number(kikanObj.pagingCnt)
      : 0
    if (kikanObj.startYmd) {
      //表示用「計画対象期間」情報.開始日 + " ～ " + 表示用「計画対象期間」情報.終了日
      localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriod = kikanObj.startYmd
        .concat(SPACE_WAVE)
        .concat(kikanObj.endYmd ?? '')
    }
    localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId = Number(kikanObj.sc1Id)
    local.orX0007.planTargetPeriodId = kikanObj.sc1Id
    local.or06632.showFlg = true
    localOneway.or28153Oneway.showFlg = true
  } else {
    //表示用「計画対象期間」情報.ページング区分が0:なしの場合
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = 0
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = 0
    local.orX0007.planTargetPeriodId = ''
    local.or06632.showFlg = false
    localOneway.or28153Oneway.showFlg = false
  }
  //orX0008 履歴
  localOneway.orX0008Oneway.sc1Id = kikanObj.sc1Id
  localOneway.orX0008Oneway.officeId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.orX0008Oneway.useId = systemCommonsStore.getUserSelectSelfId() ?? ''
  localOneway.orX0008Oneway.createData.createDate = rirekiObj.createYmd
  localOneway.orX0008Oneway.createData.createId = rirekiObj.rirekiId
    ? rirekiObj.rirekiId
    : '0'
  localOneway.orX0008Oneway.createData.currentIndex = rirekiObj.pagingNo
    ? Number(rirekiObj.pagingNo)
    : 0
  localOneway.orX0008Oneway.createData.staffId = rirekiObj.shokuId ? rirekiObj.shokuId : '0'
  localOneway.orX0008Oneway.createData.staffName = rirekiObj.shokuKnj
  localOneway.orX0008Oneway.createData.totalCount = rirekiObj.pagingCnt
    ? Number(rirekiObj.pagingCnt)
    : 0
    local.orX0008.createId = rirekiObj.rirekiId ? rirekiObj.rirekiId : '0'

  //orX0009 作成者
  localOneway.orX0009Oneway.createData = {
    createDate: rirekiObj.createYmd,
    createId: Number(rirekiObj.rirekiId),
    currentIndex: Number(rirekiObj.pagingNo),
    staffId: Number(rirekiObj.shokuId),
    staffName: rirekiObj.shokuKnj,
    totalCount: Number(rirekiObj.pagingCnt),
  }

  local.orX0009.staffId = rirekiObj.shokuId
  local.orX0009.staffName = rirekiObj.shokuKnj

  //orX0010 作成日
  local.orX0010.value = rirekiObj.createYmd
  //共通情報を設定する
  setCommonPlanPeriod(kikanObj)
  //共通情報.履歴ＩＤ ＝ 表示用「実施計画②履歴」情報.履歴ＩＤ
  local.or06632.rirekiId = rirekiObj.rirekiId ? rirekiObj.rirekiId : '0'
  local.or15843.value = rirekiObj.caseNo
  //改訂フラグ ＝ 表示用「実施計画②履歴」情報の改訂フラグ（2:H21/4、1:旧様式）

  // 初期設定マスタの情報
  local.or06632.initMasterObj = {
    // パッケージプラン改訂フラグ
    pkaiteiFlg: ret.data.initMasterObj.pkaiteiFlg,
    // 日課表：メモ表示
    pdayMemoFlg: ret.data.initMasterObj.pdayMemoFlg,
    // 日課表：時間表示
    pdayTimeFlg: ret.data.initMasterObj.pdayTimeFlg,
    //日課表：右クリック
    pdayRbtnFlg: ret.data.initMasterObj.pdayRbtnFlg,
    // ケアプラン方式
    cpnFlg: '',
    // 敬称オプション
    keishoFlg: '',
    // 敬称
    keishoKnj: ''
  }
  local.or06632.kaiteiFlg = local.or06632.initMasterObj?.pkaiteiFlg ?? ''
  // 表示中タブ
  local.currentTabName = Or06632Const.DATA_TABLE_KBN_ALL
  local.or06632.dailyInfoList = [];
  // 「内容情報」リスト
  ret.data.dailyList.forEach((item) => {
    local.or06632.dailyInfoList.push({
      // カウンタ
      id: item.id,
      uuId: uuidv4(),
      // 開始時間
      startTime: item.startTime,
      // 終了時間
      endTime: item.endTime,
      // 区分
      dataKbn: item.dataKbn,
      // 内容CD
      naiyoCd: item.naiyoCd ,
      // 内容
      naiyoKnj: item.naiyoKnj ,
      // 日常処遇サービスメモ
      memoKnj:  item.memoKnj ,
      // 担当者
      tantoKnj: item.tantoKnj ,
      // 文字サイズ
      fontSize: item.fontSize ,
      // 文字サイズ
      fontSizeTitle: item.fontSize ,
      // 文字位置
      alignment: item.alignment ,
      // 文字カラー
      fontColor: convertDecimalToHex(Number(item.fontColor)),
      // 背景カラー
      backColor: convertDecimalToHex(Number(item.backColor)),
      // 時間表示区分
      timeKbn:  item.timeKbn ,
      /** 内容担当更新区分 */
      updateKbn: '',
      /** 更新回数 */
      modifiedCnt: item.modifiedCnt,
      zIndex:1000,
    })
  })

  if (local.or06632.tokkiKnjInfo) {
    local.or06632.tokkiKnj = {
      rirekiId: local.or06632.rirekiId,
      tokkiKnj: local.or06632.tokkiKnjInfo.tokkiKnj,
      sonotaKnj: local.or06632.tokkiKnjInfo.sonotaKnj,
      kaiteiFlg: local.or06632.tokkiKnjInfo.kaiteiFlg,
      modifiedCnt : local.or06632.tokkiKnjInfo.modifiedCnt,
    } as TokkiKnjInfo
  }
  local.or06632.svInfoList = [];
  if (ret.data.svList) {
    ret.data.svList.forEach((item) => {
      local.or06632.svInfoList.push({
        svId: item.svId,
        dataKbn: item.dataKbn,
        tekiyoFlg: item.tekiyoFlg,
        modifiedCnt: item.modifiedCnt,
        updateKbn: ''
      })
    })
  }
}

/**
 * 日常生活活動等 initData
 *
 * @param ret - apiデータ
 */
function initGui00991(ret: DailyScheduleImageInitSelectOutEntity) {
  // 操作区分
  localOneway.or28528Oneway.operaFlg = Or06632Const.OPERA_FLG_1
  //  表示用「計画対象期間」情報.ページング区分
  localOneway.or28528Oneway.pagingFlg = ret.data.kikanObj.pagingFlg?? ''
  // 文字サイズ
  localOneway.or28528Oneway.letterSize = localOneway.letterSize

  // 文字位置
  localOneway.or28528Oneway.letterPosition = localOneway.letterPosition
  // 文字サイズ
  localOneway.or28528Oneway.timeDisplay = localOneway.timeDisplay
  // 初期設定マスタの情報
  localOneway.or28528Oneway.initMasterObj = {
    // パッケージプラン改訂フラグ
    pkaiteiFlg: ret.data.initMasterObj.pkaiteiFlg,
    // 日課表：メモ表示
    pdayMemoFlg: ret.data.initMasterObj.pdayMemoFlg,
    // 日課表：時間表示
    pdayTimeFlg: ret.data.initMasterObj.pdayTimeFlg,
    //日課表：右クリック
    pdayRbtnFlg: ret.data.initMasterObj.pdayRbtnFlg,
    // ケアプラン方式
    cpnFlg: ret.data.initMasterObj.cpnFlg,
    // 敬称オプション
    keishoFlg: ret.data.initMasterObj.keishoFlg,
    // 敬称
    keishoKnj: ret.data.initMasterObj.keishoKnj,
  }
  initGui00991TableData();
}

function initGui00991TableData() {
  // 「内容情報」リスト
  localOneway.or28528Oneway.naiyoList = []
  local.or06632.naiyoList
    .filter((item) => item.dataKbn === Or06632Const.DATA_KBN_DAILY_LIFE)
    .forEach((item) => {
      localOneway.or28528Oneway.naiyoList.push({
        // 内容CD
        naiyoCd: item.naiyoCd,
        //区分
        dataKbn: item.dataKbn,
        // 内容
        naiyoKnj: item.naiyoKnj,
        // 表示順
        seq: item.seq,
      })
    })

  local.or28528.items = []
  local.or06632.dailyInfoList
    .filter((item) => item.dataKbn === Or06632Const.DATA_KBN_DAILY_LIFE)
    .forEach((item) => {
      local.or28528.items.push({
        // カウンタ
        id: item.uuId,
        dataId: item.id,
        // 開始時刻
        startTime: { value:item.startTime },
        // 終了時刻
        endTime: { value:item.endTime },
        // 区分
        dataKbn: Or06632Const.DATA_KBN_DAILY_LIFE,
        // 内容CD
        naiyoCd: { modelValue: item.naiyoCd },
        // 内容
        naiyoKnj: { value: item.naiyoKnj },
        // 日常処遇サービスメモ
        memoKnj: { value: item.memoKnj },
        // 担当者
        tantoKnj: { value: item.tantoKnj },
        // 文字サイズ
        fontSize: { modelValue: item.fontSize },
        // 文字サイズ
        fontSizeTitle: { value: item.fontSize },
        // 文字位置
        alignment: { modelValue: item.alignment },
        // 文字カラー
        fontColor: item.fontColor,
        // 背景カラー
        backColor: item.backColor,
        // 時間表示区分
        timeKbn: { modelValue: item.timeKbn },
        /** 内容担当更新区分 */
        updateKbn: '',
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
      })
    })
}
/**
 * 日常生活活動等変更の監視
 */
watch(
  () => Or28528Logic.data.get(or28528_1.value.uniqueCpId),
  (newValue) => {
    if (local.or06632.setInputComponentsFlg || !newValue) {
      return
    }
    newValue.items.forEach((item) => {
      const oldData = local.or06632.dailyInfoList.find((newItem) => newItem.uuId === item.id)
      if (oldData) {
        if (oldData.updateKbn === UPDATE_KBN.CREATE && item.updateKbn === UPDATE_KBN.DELETE) {
          local.or06632.dailyInfoList = local.or06632.dailyInfoList.filter(
            (item) => item.uuId !== oldData.uuId
          )
        } else {
          // 開始時間
          oldData.startTime = item.startTime.value
          // 終了時間
          oldData.endTime = item.endTime.value
          // 内容CD
          oldData.naiyoCd = item.naiyoCd.modelValue
          // 内容
          oldData.naiyoKnj = item.naiyoKnj.value
          // 日常処遇サービスメモ
          oldData.memoKnj = item.memoKnj.value
          // 担当者
          oldData.tantoKnj = item.tantoKnj.value
          // 文字サイズ
          oldData.fontSize = item.fontSize.modelValue
          // 文字位置
          oldData.alignment = item.alignment.modelValue
          // 文字カラー
          oldData.fontColor = item.fontColor
          // 背景カラー
          oldData.backColor = item.backColor
          // 時間表示区分
          oldData.timeKbn = item.timeKbn.modelValue
          // 更新回数
          oldData.modifiedCnt = item.modifiedCnt
          // 内容担当更新区分
          oldData.updateKbn = item.updateKbn
        }
      } else {
        local.or06632.dailyInfoList.push({
          // カウンタ
          id: '',
          uuId: item.id,
          // 区分
          dataKbn: Or06632Const.DATA_KBN_DAILY_LIFE,
          // 開始時間
          startTime: item.startTime.value,
          // 終了時間
          endTime: item.endTime.value,
          // 内容CD
          naiyoCd: item.naiyoCd.modelValue,
          // 内容
          naiyoKnj: item.naiyoKnj.value,
          // 日常処遇サービスメモ
          memoKnj: item.memoKnj.value,
          // 担当者
          tantoKnj: item.tantoKnj.value,
          // 文字サイズ
          fontSize: item.fontSize.modelValue,
          // 文字サイズ
          fontSizeTitle: item.fontSize.modelValue,
          // 文字位置
          alignment: item.alignment.modelValue,
          // 文字カラー
          fontColor: item.fontColor,
          // 背景カラー
          backColor: item.backColor,
          // 時間表示区分
          timeKbn: item.timeKbn.modelValue,
          // 更新回数
          modifiedCnt: '',
          // 内容担当更新区分
          updateKbn: UPDATE_KBN.CREATE,
          zIndex:1000,
        })
      }
    })
    set28153()
  }
)
/**
 * 自立支援に関する処遇 initData
 *
 * @param ret - apiデータ
 */
function initGui00992(ret: DailyScheduleImageInitSelectOutEntity) {
  // 介護(自立支援に関する処遇)サービス
  // 操作区分
  localOneway.or28220Oneway.operaFlg = Or06632Const.OPERA_FLG_1
  //  表示用「計画対象期間」情報.ページング区分
  localOneway.or28220Oneway.pagingFlg = ret.data.kikanObj.pagingFlg?? ''
  // 文字サイズ
  localOneway.or28220Oneway.letterSize = localOneway.letterSize
  // 文字位置
  localOneway.or28220Oneway.letterPosition = localOneway.letterPosition
  // 文字サイズ
  localOneway.or28220Oneway.timeDisplay = localOneway.timeDisplay
  // 初期設定マスタの情報
  localOneway.or28220Oneway.initMasterObj = {
    // パッケージプラン改訂フラグ
    pkaiteiFlg: ret.data.initMasterObj.pkaiteiFlg,
    // 日課表：メモ表示
    pdayMemoFlg: ret.data.initMasterObj.pdayMemoFlg,
    // 日課表：時間表示
    pdayTimeFlg: ret.data.initMasterObj.pdayTimeFlg,
    //日課表：右クリック
    pdayRbtnFlg: ret.data.initMasterObj.pdayRbtnFlg,
    // ケアプラン方式
    cpnFlg: ret.data.initMasterObj.cpnFlg,
    // 敬称オプション
    keishoFlg: ret.data.initMasterObj.keishoFlg,
    // 敬称
    keishoKnj: ret.data.initMasterObj.keishoKnj,
  }
  initGui00992TableData();
}

function initGui00992TableData() {
  // 「内容情報」リスト
  localOneway.or28220Oneway.naiyoList = []
  if (local.or06632.naiyoList) {
    local.or06632.naiyoList
    .filter(
      (item) => item.dataKbn === Or06632Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
    )
    .forEach((item) => {
      localOneway.or28220Oneway.naiyoList.push({
        // 内容CD
        naiyoCd: item.naiyoCd,
        //区分
        dataKbn: item.dataKbn,
        // 内容
        naiyoKnj: item.naiyoKnj,
        // 表示順
        seq: item.seq,
      })
    })
  }

  local.or28220.items = []
  local.or06632.dailyInfoList
    .filter(
      (item) => item.dataKbn === Or06632Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
    )
    .forEach((item) => {
      local.or28220.items.push({
        // カウンタ
        id: item.uuId,
        dataId: item.id,
        // 開始時間
        startTime: {value:item.startTime},
        // 終了時間
        endTime: {value:'24:15'},
        // 区分
        dataKbn: Or06632Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT,
        // 内容CD
        naiyoCd: { modelValue: item.naiyoCd },
        // 内容
        naiyoKnj: { value: item.naiyoKnj },
        // 日常処遇サービスメモ
        memoKnj: { value: item.memoKnj },
        // 担当者
        tantoKnj: { value: item.tantoKnj },
        // 文字サイズ
        fontSize: { modelValue: item.fontSize },
        // 文字サイズ
        fontSizeTitle: { value: item.fontSize },
        // 文字位置
        alignment: { modelValue: item.alignment },
        // 文字カラー
        fontColor: item.fontColor,
        // 背景カラー
        backColor: item.backColor,
        // 時間表示区分
        timeKbn: { modelValue: item.timeKbn },
        /** 内容担当更新区分 */
        updateKbn: '',
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
      })
    })
}

/**
 *  自立支援に関する処遇等変更の監視
 */
watch(
  () => Or28220Logic.data.get(or28220_1.value.uniqueCpId),
  (newValue) => {
    if (local.or06632.setInputComponentsFlg || !newValue) {
      return
    }
    newValue.items.forEach((item) => {
      const oldData = local.or06632.dailyInfoList.find((newItem) => newItem.uuId === item.id)
      if (oldData) {
        if (oldData.updateKbn === UPDATE_KBN.CREATE && item.updateKbn === UPDATE_KBN.DELETE) {
          local.or06632.dailyInfoList = local.or06632.dailyInfoList.filter(
            (item) => item.uuId !== oldData.uuId
          )
        } else {
          // 開始時間
          oldData.startTime = item.startTime.value
          // 終了時間
          oldData.endTime = item.endTime.value
          // 内容CD
          oldData.naiyoCd = item.naiyoCd.modelValue
          // 内容
          oldData.naiyoKnj = item.naiyoKnj.value
          // 日常処遇サービスメモ
          oldData.memoKnj = item.memoKnj.value
          // 担当者
          oldData.tantoKnj = item.tantoKnj.value
          // 文字サイズ
          oldData.fontSize = item.fontSize.modelValue
          // 文字位置
          oldData.alignment = item.alignment.modelValue
          // 文字カラー
          oldData.fontColor = item.fontColor
          // 背景カラー
          oldData.backColor = item.backColor
          // 時間表示区分
          oldData.timeKbn = item.timeKbn.modelValue
          // 更新回数
          oldData.modifiedCnt = item.modifiedCnt
          // 内容担当更新区分
          oldData.updateKbn = item.updateKbn
        }
      } else {
        local.or06632.dailyInfoList.push({
          // カウンタ
          id: '',
          uuId: item.id,
          // 区分
          dataKbn: Or06632Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT,
          // 開始時間
          startTime: item.startTime.value,
          // 終了時間
          endTime: item.endTime.value,
          // 内容CD
          naiyoCd: item.naiyoCd.modelValue,
          // 内容
          naiyoKnj: item.naiyoKnj.value,
          // 日常処遇サービスメモ
          memoKnj: item.memoKnj.value,
          // 担当者
          tantoKnj: item.tantoKnj.value,
          // 文字サイズ
          fontSize: item.fontSize.modelValue,
          // 文字サイズ
          fontSizeTitle: item.fontSize.modelValue,
          // 文字位置
          alignment: item.alignment.modelValue,
          // 文字カラー
          fontColor: item.fontColor,
          // 背景カラー
          backColor: item.backColor,
          // 時間表示区分
          timeKbn: item.timeKbn.modelValue,
          // 更新回数
          modifiedCnt: '',
          // 内容担当更新区分
          updateKbn: UPDATE_KBN.CREATE,
          zIndex:1000,
        })
      }
    })
    set28153()
  }
)

/**
 * 介護(介護予防)サービス initData
 *
 * @param ret - apiデータ
 */
function initGui00993(ret: DailyScheduleImageInitSelectOutEntity) {
  // 介護(介護予防)サービス
  // 操作区分
  localOneway.or28221Oneway.operaFlg = Or06632Const.OPERA_FLG_1
  //  表示用「計画対象期間」情報.ページング区分
  localOneway.or28221Oneway.pagingFlg = ret.data.kikanObj.pagingFlg?? ''
  // 文字サイズ
  localOneway.or28221Oneway.letterSize = localOneway.letterSize
  // 文字位置
  localOneway.or28221Oneway.letterPosition = localOneway.letterPosition
  // 文字サイズ
  localOneway.or28221Oneway.timeDisplay = localOneway.timeDisplay
  // 初期設定マスタの情報
  localOneway.or28221Oneway.initMasterObj = {
    // パッケージプラン改訂フラグ
    pkaiteiFlg: ret.data.initMasterObj.pkaiteiFlg,
    // 日課表：メモ表示
    pdayMemoFlg: ret.data.initMasterObj.pdayMemoFlg,
    // 日課表：時間表示
    pdayTimeFlg: ret.data.initMasterObj.pdayTimeFlg,
    //日課表：右クリック
    pdayRbtnFlg: ret.data.initMasterObj.pdayRbtnFlg,
    // ケアプラン方式
    cpnFlg: ret.data.initMasterObj.cpnFlg,
    // 敬称オプション
    keishoFlg: ret.data.initMasterObj.keishoFlg,
    // 敬称
    keishoKnj: ret.data.initMasterObj.keishoKnj,
  }

  initGui00993TableData();
}

function initGui00993TableData() {
  // 「内容情報」リスト
  localOneway.or28221Oneway.naiyoList = []
  if (local.or06632.naiyoList) {
    local.or06632.naiyoList
    .filter((item) => item.dataKbn === Or06632Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES)
    .forEach((item) => {
      localOneway.or28221Oneway.naiyoList.push({
        // 内容CD
        naiyoCd: item.naiyoCd,
        //区分
        dataKbn: item.dataKbn,
        // 内容
        naiyoKnj: item.naiyoKnj,
        // 表示順
        seq: item.seq,
      })
    })
  }
  local.or28221.items = []
  local.or06632.dailyInfoList
    .filter((item) => item.dataKbn === Or06632Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES)
    .forEach((item) => {
      local.or28221.items.push({
        // カウンタ
        id: item.uuId,
        dataId: item.id,
        // 開始時間
        startTime: {value:item.startTime},
        // 終了時間
        endTime: {value:item.endTime},
        // 区分
        dataKbn: Or06632Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES,
        // 内容CD
        naiyoCd: { modelValue: item.naiyoCd },
        // 内容
        naiyoKnj: { value: item.naiyoKnj },
        // 日常処遇サービスメモ
        memoKnj: { value: item.memoKnj },
        // 担当者
        tantoKnj: { value: item.tantoKnj },
        // 文字サイズ
        fontSize: { modelValue: item.fontSize },
        // 文字サイズ
        fontSizeTitle: { value: item.fontSize },
        // 文字位置
        alignment: { modelValue: item.alignment },
        // 文字カラー
        fontColor: item.fontColor,
        // 背景カラー
        backColor: item.backColor,
        // 時間表示区分
        timeKbn: { modelValue: item.timeKbn },
        /** 内容担当更新区分 */
        updateKbn: '',
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
      })
    })
}

/**
 * 介護(介護予防)サービスの監視
 */
watch(
  () => Or28221Logic.data.get(or28221_1.value.uniqueCpId),
  (newValue) => {
    if (local.or06632.setInputComponentsFlg || !newValue) {
      return
    }
    newValue.items.forEach((item) => {
      const oldData = local.or06632.dailyInfoList.find((newItem) => newItem.uuId === item.id)
      if (oldData) {
        if (oldData.updateKbn === UPDATE_KBN.CREATE && item.updateKbn === UPDATE_KBN.DELETE) {
          local.or06632.dailyInfoList = local.or06632.dailyInfoList.filter(
            (item) => item.uuId !== oldData.uuId
          )
        } else {
          // 開始時間
          oldData.startTime = item.startTime.value
          // 終了時間
          oldData.endTime = item.endTime.value
          // 内容CD
          oldData.naiyoCd = item.naiyoCd.modelValue
          // 内容
          oldData.naiyoKnj = item.naiyoKnj.value
          // 日常処遇サービスメモ
          oldData.memoKnj = item.memoKnj.value
          // 担当者
          oldData.tantoKnj = item.tantoKnj.value
          // 文字サイズ
          oldData.fontSize = item.fontSize.modelValue
          // 文字位置
          oldData.alignment = item.alignment.modelValue
          // 文字カラー
          oldData.fontColor = item.fontColor
          // 背景カラー
          oldData.backColor = item.backColor
          // 時間表示区分
          oldData.timeKbn = item.timeKbn.modelValue
          // 更新回数
          oldData.modifiedCnt = item.modifiedCnt
          // 内容担当更新区分
          oldData.updateKbn = item.updateKbn
        }
      } else {
        local.or06632.dailyInfoList.push({
          // カウンタ
          id: '',
          uuId: item.id,
          // 区分
          dataKbn: Or06632Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES,
          // 開始時間
          startTime: item.startTime.value,
          // 終了時間
          endTime: item.endTime.value,
          // 内容CD
          naiyoCd: item.naiyoCd.modelValue,
          // 内容
          naiyoKnj: item.naiyoKnj.value,
          // 日常処遇サービスメモ
          memoKnj: item.memoKnj.value,
          // 担当者
          tantoKnj: item.tantoKnj.value,
          // 文字サイズ
          fontSize: item.fontSize.modelValue,
          // 文字サイズ
          fontSizeTitle: item.fontSize.modelValue,
          // 文字位置
          alignment: item.alignment.modelValue,
          // 文字カラー
          fontColor: item.fontColor,
          // 背景カラー
          backColor: item.backColor,
          // 時間表示区分
          timeKbn: item.timeKbn.modelValue,
          // 更新回数
          modifiedCnt: '',
          // 内容担当更新区分
          updateKbn: UPDATE_KBN.CREATE,
          zIndex:1000,
        })
      }
    })
    set28153()
  }
)

function initGui00994() {
  localOneway.orX0114Oneway.initMasterObj = local.or06632.initMasterObj
  if (local.or06632.tokkiKnj) {
    local.orX0114.tokkiKnj = { value: local.or06632.tokkiKnj.tokkiKnj ?? ''}
    local.orX0114.sonotaKnj =  { value: local.or06632.tokkiKnj.sonotaKnj ?? ''}
  }
  local.orX0114.svList = getSvList()
}
/**
 * 「日課表サービス例」リストを取得
 */
function getSvList() {
  const svList: SvType[] = []
  const list = [
    ...local.or06632.svDailyList!,
    ...local.or06632.svCaregiveList!,
    ...local.or06632.svEntrustedList!,
  ]
  for (const element of list) {
    const data = local.or06632.svList!
      .filter((x) => x.dataKbn === element.dataKbn && x.svId === element.svId)
      ?.at(0)
    svList.push({
      svId: element.svId,
      dataKbn: element.dataKbn,
      mo00018: {
        modelValue: data?.tekiyoFlg === '1',
      },
      mo00018Oneway: {
        checkboxLabel: element.svKnj,
        showItemLabel: true,
        isVerticalLabel: false,
        customClass: new CustomClass({
          outerClass: '',
          labelClass: 'ma-1',
        }),
      },
      modifiedCnt: data? data.modifiedCnt : '',
      updateKbn: data ? UPDATE_KBN.NONE : UPDATE_KBN.CREATE
    })
  }
  return svList
}

/**
 * 日課表サービス例の監視
 */
 watch(
  () => OrX0114Logic.data.get(orX0114_1.value.uniqueCpId)?.tokkiKnj,
  (newValue) => {
    if (local.or06632.setInputComponentsFlg || !newValue) {
      return
    }
    local.or06632.tokkiKnj.tokkiKnj = newValue.value
    if (local.or06632.operaFlg === Or06632Const.ACTION_TYPE.INIT) {
      local.or06632.tokkiKnj.updateKbn = UPDATE_KBN.UPDATE
    }
  }
)
/**
 * 日課表サービス例の監視
 */
 watch(
  () => OrX0114Logic.data.get(orX0114_1.value.uniqueCpId)?.sonotaKnj,
  (newValue) => {
    if (local.or06632.setInputComponentsFlg || !newValue) {
      return
    }
    local.or06632.tokkiKnj.sonotaKnj = newValue.value
    if (local.or06632.operaFlg === Or06632Const.ACTION_TYPE.INIT) {
      local.or06632.tokkiKnj.updateKbn = UPDATE_KBN.UPDATE
    }
  }
)
/**
 * 日課表サービス例の監視
 */
 watch(
  () => OrX0114Logic.data.get(orX0114_1.value.uniqueCpId)?.svList,
  (newValue) => {
    if (local.or06632.setInputComponentsFlg || !newValue) {
      return
    }
    for (const element of newValue) {
      const oldData = local.or06632.svInfoList
        .filter((x) => x.dataKbn === element.dataKbn && x.svId === element.svId)
        ?.at(0)
      if (!oldData) {
        local.or06632.svInfoList.push({
          svId: element.svId,
          dataKbn: element.dataKbn,
          tekiyoFlg: element.mo00018.modelValue ? '1' : '0',
          modifiedCnt: element.modifiedCnt,
          updateKbn: UPDATE_KBN.CREATE
        })
      } else {
        const tekiyoFlg = element.mo00018.modelValue ? '1' : '0'
        if (tekiyoFlg !== oldData.tekiyoFlg && oldData.updateKbn !== UPDATE_KBN.CREATE) {
          oldData.tekiyoFlg = tekiyoFlg
          oldData.updateKbn = UPDATE_KBN.UPDATE
        } else if (tekiyoFlg !== oldData.tekiyoFlg && oldData.updateKbn === UPDATE_KBN.CREATE) {
          oldData.tekiyoFlg = tekiyoFlg
        }
      }
    }
    if (local.or06632.operaFlg === Or06632Const.ACTION_TYPE.INIT) {
      local.or06632.tokkiKnj.updateKbn = UPDATE_KBN.UPDATE
    }
  }
)

/**
 * 保存時処理
 */
async function _save() {
  // 期間管理フラグが「1:管理する」、かつ、計画対象期間リストが0件の場合
  if (local.or06632.kikanFlg === Or06632Const.PLAN_TARGET_PERIOD_1 && local.or06632.kikanObj.pagingFlg === Or06632Const.PAGING_FLAG_0) {
    return false
  }
  if (!(await orX0114Ref.value?.isValid() ?? true)) {
    return false
  }
  //保存処理を行う。
  await gyoumuCom.doComLogicSave(
    local.or06632,
    isEdit.value,
    showMessageBox,
    getCarePlanTable,
    saveApiData,
    setCommonPlanPeriod
  )
  return true
}

/**
 * 新規作成時処理
 */
async function _create() {
  // 操作区分が3:削除の場合、処理終了
  if (local.or06632.operaFlg === Or06632Const.OPERA_FLG_3) {
    return
  }

  // 期間管理フラグが1:期間管理する、かつ、表示用「計画対象期間」情報.ページング区分が0:なしの場合、メッセージを表示(i.cmn.11300)
  if (local.or06632.kikanFlg === Or06632Const.PLAN_TARGET_PERIOD_1
    && local.or06632.kikanObj.pagingFlg === Or06632Const.PAGING_FLAG_0) {
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11300'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // GUI00070 対象期間画面をポップアップで起動する。
    if (orX0007Ref.value?.onClickDialog) {
      await orX0007Ref.value.onClickDialog()
    }
    return
  } else if (!local.or06632.rirekiId || local.or06632.rirekiId === '0') {
    // 共通情報.履歴IDが空白または0の場合 i.cmn.11265
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.implementation-plan1')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  } else {
    //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
    if (!(await checkEditBySave())) {
      return
    }
    // 下記の項目を設定する
    local.or06632.rirekiId = '0'
    local.or06632.operaFlg = Or06632Const.OPERA_FLG_1
    // 画面項目を再検索する
    await getCarePlanTable()
  }
}

/**
 * 複写ボタン
 */
 function onCreateCopy() {
  // 下記の条件がいずれか満たす場合、
  //・操作区分 = 3:削除の場合
  //・表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    local.or06632.operaFlg === Or06632Const.OPERA_FLG_3 ||
    local.or06632.kikanObj.pagingFlg === '0'
  ) {
    return
  }

  // GUI00948 実施計画～①複写画面をポップアップで起動する。
  localOneway.or36014Oneway = {
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList as string[],
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    syubetuId: local.or06632.syubetuId ?? '',
    kikanFlg: local.or06632.kikanFlg ?? '',
    pkaiteiFlg: local.or06632.initMasterObj?.pkaiteiFlg ?? '',
    operaFlg: Or06632Const.OPERA_FLG_2,
    pagingFlg: local.or06632.kikanObj.pagingFlg ?? '',
    initMasterObj: local.or06632.initMasterObj,
  }
  Or36014Logic.state.set({
    uniqueCpId: or36014_1.value.uniqueCpId,
    state: { isOpen: true },
  })
 }

 /**
  * GUI00948 実施計画～①複写返却情報がある場合
  *
  * @param newValue - ポップアップ返却情報
  */
async function handleOr36014Confirm(newValue: Or36014Type) {
  if (newValue?.rirekiId) {
    // 複写情報を取得する。
    const ret: DailyScheduleImageCopyReturnSelectOutEntity = await ScreenRepository.select(
      'dailyScheduleImageCopyReturnSelect',
      { rirekiId: newValue.rirekiId } as DailyScheduleImageCopyReturnSelectInEntity
    )
    // API戻り値
    const retData = ret.data
    // 既存データを削除する
    const or06632DailyInfoData = local.or06632.dailyInfoList.filter(item => !isUndefined(item.id) && item.id !== '' && item.updateKbn !== UPDATE_KBN.CREATE)
    or06632DailyInfoData.forEach(item => item.updateKbn = UPDATE_KBN.DELETE)
    //複写用情報を本画面に設定する。
    local.or06632.rirekiObj.kaiteiFlg = '2'
    // 複写用情報を本画面に設定する。
    // 「内容情報」リスト
    local.or06632.naiyoList = retData.naiyoList ?? []
    // 複写用「日課表詳細」リスト
    local.or06632.dailyInfoList = []
    if (retData.dailyList) {
      retData.dailyList.forEach(item => {
        local.or06632.dailyInfoList.push({
          // カウンタ
          id: '',
          uuId: uuidv4(),
          // 区分
          dataKbn: item.dataKbn,
          // 開始時間
          startTime: item.startTime,
          // 終了時間
          endTime: item.endTime,
          // 内容CD
          naiyoCd: item.naiyoCd,
          // 内容
          naiyoKnj: '',
          // item
          memoKnj: item.memoKnj,
          // 担当者
          tantoKnj: item.tantoKnj,
          // 文字サイズ
          fontSize: item.fontSize,
          // 文字サイズ
          fontSizeTitle: item.fontSize,
          // 文字位置
          alignment: item.alignment,
          // 文字カラー
          fontColor: convertDecimalToHex(Number(item.fontColor)),
          // 背景カラー
          backColor: convertDecimalToHex(Number(item.backColor)),
          // 時間表示区分
          timeKbn: item.timeKbn,
          // 内容担当更新区分
          updateKbn:UPDATE_KBN.CREATE,
          // 更新回数
          modifiedCnt: '',
          zIndex:1000,
        })
      })
    }
    if (or06632DailyInfoData) {
      or06632DailyInfoData.forEach(item => {
        local.or06632.dailyInfoList.push({
          ...item
        })
      })
    }
    initGui00991TableData();
    Or28528Logic.data.set({
      uniqueCpId: or28528_1.value.uniqueCpId,
      value: {
        items:local.or28528.items,
      },
    })
    initGui00992TableData();
    Or28220Logic.data.set({
      uniqueCpId: or28220_1.value.uniqueCpId,
      value: {
        items:local.or28220.items,
      },
    })
    initGui00993TableData();
    Or28221Logic.data.set({
      uniqueCpId: or28221_1.value.uniqueCpId,
      value: {
        items:local.or28221.items,
      },
    })

    // 複写用「日課表サービス例」リスト
    const or06632SvInfoData = local.or06632.svInfoList.filter(item => !isUndefined(item.svId) && item.svId !== '' && item.updateKbn !== UPDATE_KBN.CREATE)
    or06632SvInfoData.forEach(item => item.updateKbn = UPDATE_KBN.DELETE)
    // 複写用「日課表特記事項」情報
    local.or06632.tokkiKnj.tokkiKnj = ''
    local.or06632.tokkiKnj.sonotaKnj = ''
    local.orX0114.tokkiKnj = { value: '' }
    local.orX0114.sonotaKnj =  { value: '' }
    if (retData.tokkiKnjInfo) {
      local.or06632.tokkiKnj.tokkiKnj = retData.tokkiKnjInfo.tokkiKnj
      local.or06632.tokkiKnj.sonotaKnj = retData.tokkiKnjInfo.sonotaKnj
      local.or06632.tokkiKnj.kaiteiFlg = '2'
      local.orX0114.tokkiKnj = { value: local.or06632.tokkiKnj.tokkiKnj }
      local.orX0114.sonotaKnj = { value: local.or06632.tokkiKnj.sonotaKnj }
      if (local.or06632.operaFlg === Or06632Const.ACTION_TYPE.INIT) {
        local.or06632.tokkiKnj.updateKbn = UPDATE_KBN.UPDATE
      }
    }

    local.or06632.svInfoList = [];
    local.or06632.svList = [];
    local.orX0114.svList = [];
    if (retData.svList) {
      retData.svList.forEach((item) => {
        local.or06632.svInfoList.push({
          svId: item.svId,
          dataKbn: item.dataKbn,
          tekiyoFlg: item.tekiyoFlg,
          modifiedCnt: '',
          updateKbn: UPDATE_KBN.CREATE
        })
        local.or06632.svList?.push({
          svId: item.svId,
          dataKbn: item.dataKbn,
          tekiyoFlg: item.tekiyoFlg,
          modifiedCnt: '',
        })
      })
      local.orX0114.svList = getSvList()
    }
    if (or06632SvInfoData) {
      or06632SvInfoData.forEach((item) => {
        local.or06632.svInfoList.push({
          ...item
        })
      })
    }
    OrX0114Logic.data.set({
      uniqueCpId: orX0114_1.value.uniqueCpId,
      value: {
        tokkiKnj: local.orX0114.tokkiKnj,
        sonotaKnj:local.orX0114.sonotaKnj,
        svList: local.orX0114.svList,
      },
    })

    set28153()
  }
}

/**
 * 「印刷設定アイコンボタン」押下の処理
 */
async function _print() {
  if (isEdit.value) {
    const dialogResult = await showConfirmMessgeBox()
    if (dialogResult?.firstBtnClickFlg === true) {
      await _save()
    } else if (dialogResult?.thirdBtnClickFlg === true) {
      return
    }
  }

  // GUI00953［印刷設定］画面をポップアップで起動する
  openPrintSetting()
}

/**
 * GUI01001［印刷設定］画面をポップアップで起動する
 */
function openPrintSetting() {
  // 共通情報.法人ID
  localOneway.or28401Oneway.houjinId = systemCommonsStore.getHoujinId ?? ''
  // 共通情報.施設ID
  localOneway.or28401Oneway.shisetuId = systemCommonsStore.getShisetuId ?? ''
  // 共通情報.事業所ID
  localOneway.or28401Oneway.svJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  // 共通情報.事業所名
  localOneway.or28401Oneway.jigyoKnj = systemCommonsStore.getJigyoSelectInfoList.find(
      (x) => x.svJigyoId === systemCommonsStore.getSvJigyoId
    )?.jigyoRyakuKnj ?? ''
  // 共通情報.事業所CD
  localOneway.or28401Oneway.jigyoCd = systemCommonsStore.getSvJigyoCd ?? ''
  // 共通情報.適用事業所ID（※リスト）
  localOneway.or28401Oneway.svJigyoIdList = systemCommonsStore.getSvJigyoIdList as string[]
  // TODO 共通情報.担当ケアマネID
  localOneway.or28401Oneway.tantoShokuId = '0'
  // 共通情報.職員ID ※ログイン情報
  localOneway.or28401Oneway.shokuId = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
  // 共通情報.利用者ID ※リストに格納する
  localOneway.or28401Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  // 共通情報.開始(YYYY/MM/DD)
  localOneway.or28401Oneway.startDate = systemCommonsStore.getStartDate ?? ''
  // 共通情報.終了(YYYY/MM/DD)
  localOneway.or28401Oneway.endDate = systemCommonsStore.getEndDate ?? ''
  // 共通情報.システム年月日(YYYY/MM/DD)
  localOneway.or28401Oneway.sysYmd = systemCommonsStore.getSystemDate ?? ''
  // 共通情報.履歴ID
  localOneway.or28401Oneway.rirekiId = local.or06632.rirekiId ?? ''
  // 共通情報.ユーザリスト
  localOneway.or28401Oneway.userList =
    systemCommonsStore.getUserSelectUserList()?.map((x) => {
      return {
        userId: x.selfId,
        nameKnj: x.nameSei + ' ' + x.nameKanaMei,
        userNumber: x.selfId,
        sex: String(x.gender),
      } as userList
    }) ?? []
  // 共通情報.50音（※リスト）
  localOneway.or28401Oneway.gojuuOnKana = systemCommonsStore.getUserSelectFilterInitials() as string[]
  // 初期設定マスタの情報
  localOneway.or28401Oneway.initMasterObj = local.or06632.initMasterObj
  // セクション名 = "（Ⅱ）実施計画～①"
  localOneway.or28401Oneway.sectionName = Or06632Const.SECTION_NAME
  // システムコード: "71101"
  localOneway.or28401Oneway.systemCode = Or06632Const.SYSTEM_CODE
  // 計画期間管理フラグ
  localOneway.or28401Oneway.kikanFlg = local.or06632.kikanFlg

  // GUI00953［印刷設定］画面
  Or28401Logic.state.set({
    uniqueCpId: or28401_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC030_カレンダーをダブルクリック
 *
 * @param data - ダブルクリックイベントの戻りパラメータ
 */
function handleDoubleClick(data: DblClickResult) {
  local.clickEdUUid = ''
  if (isUndefined(data.rowIndex) || data.rowIndex < 0) {
    const event = data.event
    if (!event) {
      return
    }
    const dailyData = local.or06632.dailyInfoList.find((newItem) => newItem.uuId === event.customId)
    if (!dailyData) {
      return
    }
    const startTime = new Date(event.segmentStart)
    const endTime = new Date(event.segmentEnd)
    localOneway.or27539Oneway = {
      // 区分
      category: dailyData.dataKbn,
      // 内容(ボタン)
      contentsButton: '',
      // 担当(ボタン)
      chargeButton: '',
      // 開始時間
      startTime: `${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}`,
      // 終了時間
      endTime: `${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}`,
      // 日常処遇サービスメモ
      memoKnj: dailyData.memoKnj,
      // 担当者
      tantoKnj: dailyData.tantoKnj,
      // 文字サイズ
      fontSize: dailyData.fontSize,
      // 文字位置
      alignment: dailyData.alignment,
      // 文字色
      fontColor: dailyData.fontColor,
      // 背景カラー
      backColor: dailyData.backColor,
      // 時間表示区分
      timeKbn: dailyData.timeKbn,
      // 内容
      contents: dailyData.naiyoCd,
    }
    local.clickEdUUid = dailyData.uuId
  } else {
    localOneway.or27539Oneway = {
      // 区分
      category:
        data.lineIndex === 0
          ? Or06632Const.DATA_KBN_DAILY_LIFE
          : data.lineIndex === 1 || data.lineIndex === 2
            ? Or06632Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
            : Or06632Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES,
      // 内容(ボタン)
      contentsButton:'',
      // 担当(ボタン)
      chargeButton:'',
      // 開始時間
      startTime: Or06632Const.FIRST_SLOTS[data.rowIndex].time,
      // 終了時間
      endTime: Or06632Const.FIRST_SLOTS[data.rowIndex + 1].time,
      // 日常処遇サービスメモ
      memoKnj: '',
      // 担当者
      tantoKnj:'',
      // 文字サイズ
      fontSize: Or06632Const.FONT_SIZE,
      // 文字位置
      alignment: Or06632Const.ALIGNMENT,
      // 文字色
      fontColor: Or06632Const.FONT_COLOR,
      // 背景カラー
      backColor:  Or06632Const.BACK_COLOR,
      // 時間表示区分
      timeKbn: local.or06632.initMasterObj?.pdayTimeFlg,
      // 内容
      contents: '',
    }
  }

    if (data.lineIndex === 0) {
      localOneway.or27539Oneway.contentsButton = Or06632Const.DAILY_TABLE_IMAGE
    } else if (data.lineIndex === 1 || data.lineIndex === 2) {
      localOneway.or27539Oneway.contentsButton = Or06632Const.TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
      localOneway.or27539Oneway.chargeButton = Or06632Const.OFFER_MANAGER_OCCUPATION
    } else if (data.lineIndex === 3 || data.lineIndex === 4) {
      localOneway.or27539Oneway.contentsButton = Or06632Const.NURSING_PREVENTIVE_CARE_SERVICES
      localOneway.or27539Oneway.chargeButton = Or06632Const.OFFER_MANAGER_OCCUPATION_ACCEPTANCE_HOME_SERVIES
    }
    Or27539Logic.state.set({
      uniqueCpId: or27539_1.value.uniqueCpId,
      state: { isOpen: true },
    })
}
/**
 * 日課情報オブジェクトドラッグ＆ドロップ
 *
 * @param event - 日課情報オブジェクトドラッグ＆ドロップ
 */
function handelMousedown(event: OrX0099SegmentedEvent) {
  const or06632Data = local.or06632.dailyInfoList.find((item) => item.uuId === event.customId)
  const startTime = new Date(stringToDate(event.start))
  const endTime = new Date(stringToDate(event.end))
  if (or06632Data) {
    or06632Data.startTime = `${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}`
    if (endTime.getDay() > startTime.getDay() && endTime.getMinutes() > 0) {
      or06632Data.endTime = `${(endTime.getHours() + 24).toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}`
    } else if (endTime.getDay() > startTime.getDay()) {
      or06632Data.endTime = `${(endTime.getHours() + 24).toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}`
    } else {
      or06632Data.endTime = `${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}`
    }
    or06632Data.zIndex = event.zIndex ?? 1000
    or06632Data.updateKbn = (or06632Data.updateKbn !== UPDATE_KBN.CREATE && or06632Data.updateKbn !==UPDATE_KBN.DELETE) ? UPDATE_KBN.UPDATE : or06632Data.updateKbn
  }

  if (event.headerIndex === 0) {
    const uuIddata = local.or28528.items.find((item) => item.id === event.customId)
    if (uuIddata) {
      // 開始時間
      uuIddata.startTime = or06632Data ? { value: or06632Data.startTime } : {value:''}
      // 終了時間
      uuIddata.endTime = or06632Data ? { value: or06632Data.endTime } : { value: '' }
      uuIddata.updateKbn = (uuIddata.updateKbn !== UPDATE_KBN.CREATE && uuIddata.updateKbn !==UPDATE_KBN.DELETE) ? UPDATE_KBN.UPDATE : uuIddata.updateKbn
    }
    Or28528Logic.data.set({
      uniqueCpId: or28528_1.value.uniqueCpId,
      value: {
        items:local.or28528.items,
      },
    })
  } else if (event.headerIndex === 1 || event.headerIndex === 2) {
    const uuIddata = local.or28220.items.find((item) => item.id === event.customId)
    if (uuIddata) {
      // 開始時間
      uuIddata.startTime = or06632Data ? { value: or06632Data.startTime } : {value:''}
      // 終了時間
      uuIddata.endTime = or06632Data ? { value: or06632Data.endTime } : { value: '' }
      uuIddata.updateKbn = (uuIddata.updateKbn !== UPDATE_KBN.CREATE && uuIddata.updateKbn !==UPDATE_KBN.DELETE) ? UPDATE_KBN.UPDATE : uuIddata.updateKbn
    }

    Or28220Logic.data.set({
      uniqueCpId: or28220_1.value.uniqueCpId,
      value: {
        items:local.or28220.items,
      },
    })
  } else {
    const uuIddata = local.or28221.items.find((item) => item.id === event.customId)
    if (uuIddata) {
      // 開始時間
      uuIddata.startTime = or06632Data ? { value: or06632Data.startTime } : {value:''}
      // 終了時間
      uuIddata.endTime = or06632Data ? { value: or06632Data.endTime } : { value: '' }
      uuIddata.updateKbn = (uuIddata.updateKbn !== UPDATE_KBN.CREATE && uuIddata.updateKbn !==UPDATE_KBN.DELETE) ? UPDATE_KBN.UPDATE : uuIddata.updateKbn
    }

    Or28221Logic.data.set({
      uniqueCpId: or28221_1.value.uniqueCpId,
      value: {
        items:local.or28221.items,
      },
    })
  }
}

/**
 * string to date
 *
 * @param date - date
 */
function stringToDate(date: string) {
  const returnDate = new Date(date)
  if (returnDate !== undefined && returnDate !== null && !isDate(returnDate)) return returnDate

  const arrayOne = date.split(' ')
  if (arrayOne === undefined || arrayOne === null || arrayOne.length < 2) {
    return new Date()
  }
  const arrayTwo = arrayOne[0].split('-')
  if (arrayTwo === undefined || arrayTwo === null || arrayTwo.length < 3) {
    return new Date()
  }
  const arrayThree = arrayOne[1].split(':')
  if (arrayThree === undefined || arrayThree === null || arrayThree.length < 2) {
    return new Date()
  }

  const year = arrayTwo[0]
  const month = arrayTwo[1]
  let day = Number(arrayTwo[2])
  let hour = Number(arrayThree[0])
  const minutes = Number(arrayThree[1])
  if ((hour === 24 && minutes > 0) || hour > 24) {
    day = day + 1
    hour = hour - 24
  }
  const returnDateString = year + '-' + month + '-' + String(day).padStart(2, '0') + ' ' + String(hour).padStart(2, '0') + ':' + String(minutes).padStart(2, '0') + ':' + '00'
  return new Date(returnDateString)
}

/**
 * 日課表入力の監視
 */
watch(
  () => local.or27539,
  (newValue) => {
    if (newValue) {
      const or06632Data = local.or06632.dailyInfoList.find((item) => item.uuId === local.clickEdUUid)
      let uuid = local.clickEdUUid
      if (or06632Data) {
        // 開始時間
        or06632Data.startTime = newValue.startTime
        // 終了時間
        or06632Data.endTime = newValue.endTime
        // 内容CD
        or06632Data.naiyoCd = newValue.contents
        // 内容
        or06632Data.memoKnj = newValue.memoKnj ? newValue.memoKnj : ''
        // 日常処遇サービスメモ
        or06632Data.tantoKnj = newValue.tantoKnj ? newValue.tantoKnj : ''
        // 担当者
        or06632Data.fontSize = newValue.fontSize
        // 文字サイズ
        or06632Data.fontSizeTitle = newValue.fontSize
        // 文字サイズ
        or06632Data.alignment = newValue.alignment
        // 文字カラー
        or06632Data.fontColor = newValue.fontColor
        // 背景カラー
        or06632Data.backColor = newValue.backColor
        // 時間表示区分
        or06632Data.timeKbn = newValue.timeKbn
        // 内容担当更新区分
        or06632Data.updateKbn = (or06632Data.updateKbn === UPDATE_KBN.CREATE ? UPDATE_KBN.CREATE : UPDATE_KBN.UPDATE)
      } else {
        uuid = uuidv4()
        local.or06632.dailyInfoList.push({
          // カウンタ
          id: '',
          uuId: uuid,
          // 区分
          dataKbn: localOneway.or27539Oneway.category === '1'
              ? Or06632Const.DATA_KBN_DAILY_LIFE
              : localOneway.or27539Oneway.category === '2'
                ? Or06632Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
                : Or06632Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES,
          // 開始時間
          startTime: newValue.startTime,
          // 終了時間
          endTime: newValue.endTime,
          // 内容CD
          naiyoCd: newValue.contents,
          // 内容
          naiyoKnj: '',
          // 日常処遇サービスメモ
          memoKnj: newValue.memoKnj ? newValue.memoKnj : '',
          // 担当者
          tantoKnj: newValue.tantoKnj ? newValue.tantoKnj : '',
          // 文字サイズ
          fontSize: newValue.fontSize,
          // 文字サイズ
          fontSizeTitle: newValue.fontSize,
          // 文字位置
          alignment: newValue.alignment,
          // 文字カラー
          fontColor: newValue.fontColor,
          // 背景カラー
          backColor: newValue.backColor,
          // 時間表示区分
          timeKbn: newValue.timeKbn,
          // 内容担当更新区分
          updateKbn:UPDATE_KBN.CREATE,
          // 更新回数
          modifiedCnt: '',
          zIndex:1000,
        })
      }
      if (localOneway.or27539Oneway.category === '1') {
        const or28528Old = local.or28528.items.find((item) => item.id === uuid)
        if (or28528Old) {
          // 開始時間
          or28528Old.startTime ={ value : newValue.startTime}
          // 終了時間
          or28528Old.endTime = { value: newValue.endTime }
          // 内容CD
          or28528Old.naiyoCd = { modelValue: newValue.contents }
          or28528Old.naiyoKnj = { value: '' }
          // 内容
          or28528Old.memoKnj = { value: newValue.memoKnj ? newValue.memoKnj : '' }
          // 日常処遇サービスメモ
          or28528Old.tantoKnj = { value: newValue.tantoKnj ? newValue.tantoKnj : '' }
          // 担当者
          or28528Old.fontSize = { modelValue: newValue.fontSize }
          // 文字サイズ
          or28528Old.fontSizeTitle = { value: newValue.fontSize }
          // 文字サイズ
          or28528Old.alignment = { modelValue: newValue.alignment }
          // 文字カラー
          or28528Old.fontColor = newValue.fontColor
          // 背景カラー
          or28528Old.backColor = newValue.backColor
          // 時間表示区分
          or28528Old.timeKbn = { modelValue: newValue.timeKbn }
          // 内容担当更新区分
          or28528Old.updateKbn = UPDATE_KBN.UPDATE
        } else {
          local.or28528.items.push({
            // カウンタ
            id: uuid,
            dataId: '',
            // 開始時間
            startTime: {value : newValue.startTime} ,
            // 終了時間
            endTime: {value : newValue.endTime},
            // 区分
            dataKbn: Or06632Const.DATA_KBN_DAILY_LIFE,
            // 内容CD
            naiyoCd: { modelValue: newValue.contents },
            // 内容
            naiyoKnj: { value: '' },
            // 日常処遇サービスメモ
            memoKnj: { value: newValue.memoKnj ? newValue.memoKnj : '' },
            // 担当者
            tantoKnj: { value: newValue.tantoKnj ? newValue.tantoKnj : '' },
            // 文字サイズ
            fontSize: { modelValue: newValue.fontSize },
            // 文字サイズ
            fontSizeTitle: { value: newValue.fontSize },
            // 文字位置
            alignment: { modelValue: newValue.alignment },
            // 文字カラー
            fontColor: newValue.fontColor,
            // 背景カラー
            backColor: newValue.backColor,
            // 時間表示区分
            timeKbn: { modelValue: newValue.timeKbn },
            /** 内容担当更新区分 */
            updateKbn: UPDATE_KBN.CREATE,
            /** 更新回数 */
            modifiedCnt: '',
          })
        }
        Or28528Logic.data.set({
          uniqueCpId: or28528_1.value.uniqueCpId,
          value: {
            items:local.or28528.items,
          },
        })
      } else if (localOneway.or27539Oneway.category === '2') {
        const or28220Old = local.or28220.items.find((item) => item.id === uuid)
        if (or28220Old) {
          // 開始時間
          or28220Old.startTime = { value: newValue.startTime }
          // 終了時間
          or28220Old.endTime = { value:newValue.endTime}
          // 内容CD
          or28220Old.naiyoCd = { modelValue: newValue.contents }
          or28220Old.naiyoKnj = { value: '' }
          // 内容
          or28220Old.memoKnj = { value: newValue.memoKnj ? newValue.memoKnj : '' }
          // 日常処遇サービスメモ
          or28220Old.tantoKnj = { value: newValue.tantoKnj ? newValue.tantoKnj : '' }
          // 担当者
          or28220Old.fontSize = { modelValue: newValue.fontSize }
          // 文字サイズ
          or28220Old.fontSizeTitle = { value: newValue.fontSize }
          // 文字サイズ
          or28220Old.alignment = { modelValue: newValue.alignment }
          // 文字カラー
          or28220Old.fontColor = newValue.fontColor
          // 背景カラー
          or28220Old.backColor = newValue.backColor
          // 時間表示区分
          or28220Old.timeKbn = { modelValue: newValue.timeKbn }
          // 内容担当更新区分
          or28220Old.updateKbn = UPDATE_KBN.UPDATE
        } else {
          local.or28220.items.push({
            // カウンタ
            id: uuid,
            dataId: '',
            // 開始時間
            startTime: {value:newValue.startTime},
            // 終了時間
            endTime: {value:newValue.endTime},
            // 区分
            dataKbn: Or06632Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT,
            // 内容CD
            naiyoCd: { modelValue: newValue.contents },
            // 内容
            naiyoKnj: { value: '' },
            // 日常処遇サービスメモ
            memoKnj: { value: newValue.memoKnj ? newValue.memoKnj : '' },
            // 担当者
            tantoKnj: { value: newValue.tantoKnj ? newValue.tantoKnj : '' },
            // 文字サイズ
            fontSize: { modelValue: newValue.fontSize },
            // 文字サイズ
            fontSizeTitle: { value: newValue.fontSize },
            // 文字位置
            alignment: { modelValue: newValue.alignment },
            // 文字カラー
            fontColor: newValue.fontColor,
            // 背景カラー
            backColor: newValue.backColor,
            // 時間表示区分
            timeKbn: { modelValue: newValue.timeKbn },
            /** 内容担当更新区分 */
            updateKbn: UPDATE_KBN.CREATE,
            /** 更新回数 */
            modifiedCnt: '',
          })
        }
        Or28220Logic.data.set({
          uniqueCpId: or28220_1.value.uniqueCpId,
          value: {
            items:local.or28220.items,
          },
        })
      } else {
        const or28221Old = local.or28221.items.find((item) => item.id === uuid)
        if (or28221Old) {
          // 開始時間
          or28221Old.startTime = {value:newValue.startTime}
          // 終了時間
          or28221Old.endTime = {value:newValue.endTime}
          // 内容CD
          or28221Old.naiyoCd = { modelValue: newValue.contents }
          or28221Old.naiyoKnj = { value: '' }
          // 内容
          or28221Old.memoKnj = { value: newValue.memoKnj ? newValue.memoKnj : '' }
          // 日常処遇サービスメモ
          or28221Old.tantoKnj = { value: newValue.tantoKnj ? newValue.tantoKnj : '' }
          // 担当者
          or28221Old.fontSize = { modelValue: newValue.fontSize }
          // 文字サイズ
          or28221Old.fontSizeTitle = { value: newValue.fontSize }
          // 文字サイズ
          or28221Old.alignment = { modelValue: newValue.alignment }
          // 文字カラー
          or28221Old.fontColor = newValue.fontColor
          // 背景カラー
          or28221Old.backColor = newValue.backColor
          // 時間表示区分
          or28221Old.timeKbn = { modelValue: newValue.timeKbn }
          // 内容担当更新区分
          or28221Old.updateKbn = UPDATE_KBN.UPDATE
        } else {
          local.or28221.items.push({
            // カウンタ
            id: uuid,
            dataId: '',
            // 開始時間
            startTime: {value:newValue.startTime},
            // 終了時間
            endTime: {value:newValue.endTime},
            // 区分
            dataKbn: Or06632Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES,
            // 内容CD
            naiyoCd: { modelValue: newValue.contents },
            // 内容
            naiyoKnj: { value: '' },
            // 日常処遇サービスメモ
            memoKnj: { value: newValue.memoKnj ? newValue.memoKnj : '' },
            // 担当者
            tantoKnj: { value: newValue.tantoKnj ? newValue.tantoKnj : '' },
            // 文字サイズ
            fontSize: { modelValue: newValue.fontSize },
            // 文字サイズ
            fontSizeTitle: { value: newValue.fontSize },
            // 文字位置
            alignment: { modelValue: newValue.alignment },
            // 文字カラー
            fontColor: newValue.fontColor,
            // 背景カラー
            backColor: newValue.backColor,
            // 時間表示区分
            timeKbn: { modelValue: newValue.timeKbn },
            /** 内容担当更新区分 */
            updateKbn: UPDATE_KBN.CREATE,
            /** 更新回数 */
            modifiedCnt: '',
          })
        }
        Or28221Logic.data.set({
          uniqueCpId: or28221_1.value.uniqueCpId,
          value: {
            items:local.or28221.items,
          },
        })
      }
      set28153()
    }
  }
)

/**
 * 日課表詳細の監視
 */
watch(
  () => local.or06632.dailyInfoList,
  (newValue) => {
    if (local.or06632.setInputComponentsFlg || !newValue) {
      return
    }
    set28153()
  }
)

function set28153() {
  local.or28153.dailyInfoList = local.or06632.dailyInfoList
  Or28153Logic.data.set({
    uniqueCpId: or28153_1.value.uniqueCpId,
    value: {
      dailyInfoList: local.or28153.dailyInfoList
    }
  })
}
// タブ切替
watch(
  () => local.mo00043Transfer.id,
  async (newValue) => {
    if (local.mo00043.id !== newValue) {
      local.mo00043Change.id = newValue
      local.mo00043Transfer.id = local.mo00043.id
      // 特記事項、サービス例タブ
      if (local.mo00043.id === Or06632Const.TAB.TAB_ID_DAILY_TABLE_IMAGE) {
        local.mo00043.id = newValue
        local.mo00043Transfer.id = newValue
        local.currentTabName = Or06632Const.DATA_TABLE_KBN_ALL
      }

      // 日常
      if (local.mo00043.id === Or06632Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES
        && local.or28528.items && local.or28528.items.length > 0
      ) {
        for (const dailyinfo of local.or28528.items) {
          if (!await checkTime(dailyinfo.startTime.value, dailyinfo.endTime.value)) {
            return;
          }
        }
        local.mo00043.id = newValue
        local.mo00043Transfer.id = newValue
        local.currentTabName = Or06632Const.DATA_TABLE_KBN_DAILY_LIFE
      }

      // 処遇
      if (local.mo00043.id === Or06632Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES
        && local.or28220.items && local.or28220.items.length > 0
      ) {
        for (const dailyinfo of local.or28220.items) {
          if (!await checkTime(dailyinfo.startTime.value, dailyinfo.endTime.value)) {
            return;
          }
        }
        local.mo00043.id = newValue
        local.mo00043Transfer.id = newValue
        local.currentTabName = Or06632Const.DATA_TABLE_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
      }

      // 介護
      if (local.mo00043.id === Or06632Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES
        && local.or28221.items && local.or28221.items.length > 0
      ) {
        for (const dailyinfo of local.or28221.items) {
          if (!await checkTime(dailyinfo.startTime.value, dailyinfo.endTime.value)) {
            return;
          }
        }
        local.mo00043.id = newValue
        local.mo00043Transfer.id = newValue
        local.currentTabName = Or06632Const.DATA_TABLE_KBN_NURSING_PREVENTIVE_CARE_SERVICES
      }

      // 特記事項、サービス例タブ
      if (local.mo00043.id === Or06632Const.TAB.TAB_ID_SPECIAL_NOTES_SERVICES_EXAMOLES) {
        local.mo00043.id = newValue
        local.mo00043Transfer.id = newValue
        local.currentTabName = Or06632Const.DATA_TABLE_KBN_SV
      }
    }

  }
)
/**
 * 時間帯チェック処理を行う。
 *
 * @param startTime - 開始時刻
 *
 * @param endTime - 終了時刻
 */
async function checkTime(startTime:string, endTime:string) {
  const hourStart = startTime.split(':')[0]
  const minutesStart = startTime.split(':')[1]
  const hourEnd = endTime.split(':')[0]
  const minutesEnd = endTime.split(':')[1]
  // 画面.時間帯_終了時(HH)が24以降の場合、且つ、画面.時間帯_終了分(MM)が15より後ろの場合
  // e.cmn.40734
  if (Number(hourStart) >= 24 && Number(minutesStart) >= 15) {
    await openErrorDialog(t('message.e-cmn-40735'))
    return false
  }
  // 画面.時間帯_開始時(HH)が24以降の場合、且つ、 画面.時間帯_開始分(MM)が14より後ろの場合
  // e.cmn.40733
  if (Number(hourStart) >= 24 && Number(minutesStart) >= 14) {
    await openErrorDialog(t('message.e-cmn-40777'))
    return false
  }
  // 画面.時間帯_開始時間(HHMM)が画面.時間帯_終了時間(HHMM)より後ろの場合 e.cmn.40740
  if (Number(hourStart) > Number(hourEnd)
    || (Number(hourStart) === Number(hourEnd) && Number(minutesStart) > Number(minutesEnd))) {
    await openErrorDialog(t('message.e-cmn-40777'))
    return false
  }
  // 画面.時間帯_開始時間(HHMM)が画面.時間帯_終了時間(HHMM)の場合 e.cmn.40735
    if (Number(hourStart) === Number(hourEnd) && Number(minutesStart) === Number(minutesEnd)) {
    await openErrorDialog(t('message.e-cmn-40777'))
    return false
  }
  return true;
}

/**
 *ログを押下
 */
 function onClickLog() {
  // TODO 共通処理 e-文書法対象機能の電子ファイル保存設定を取得する。
  // TODO 共通処理 e-文章法対象の帳票のXPSフォルダをエクスプローラで開く。
}
/**
 * 利用者選択監視関数に渡すコールバック関数（サブ01の例）
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 */
 const callbackFuncSub01: () => void = () => {
  void initData()
}
/**
 * 利用者設定
 *
 * @param index - index
 */
const setSelectSelUserIndex = (index: number) => {
  //利用者設定
  Or00249Logic.data.set({
    uniqueCpId: or00249.value.uniqueCpId,
    value: {
      selectUserIndex: index,
    },
  })
}

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackFuncSub01)
/**
 * ダイアログ表示フラグ
 */
const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ダイアログ表示フラグ
 */
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ダイアログ表示フラグ
 */
const showDialogOr21813 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理
 */
async function showGUI00998() {
  console.log('ボタン押下時の処理');
  // 操作区分が3:削除の場合
  // 期間管理フラグが「1:管理する」、かつ、計画対象期間リストが0件の場合
  // 処理終了にする。
  if (local.or06632.operaFlg === Or06632Const.OPERA_FLG_3
    ||
    (local.or06632.kikanFlg === Or06632Const.PLAN_TARGET_PERIOD_1
      && local.or06632.kikanObj.pagingFlg === Or06632Const.PAGING_FLAG_0)) {
    return;
  }
  // 日常
  if (local.or28528.items && local.or28528.items.length > 0) {
    for (const dailyinfo of local.or28528.items) {
      if (!await checkTime(dailyinfo.startTime.value, dailyinfo.endTime.value)) {
        return;
      }
    }
  }
  // 処遇
  if (local.or28220.items && local.or28220.items.length > 0) {
    for (const dailyinfo of local.or28220.items) {
      if (!await checkTime(dailyinfo.startTime.value, dailyinfo.endTime.value)) {
        return;
      }
    }
  }
  // 介護
  if (local.or28221.items && local.or28221.items.length > 0) {
    for (const dailyinfo of local.or28221.items) {
      if (!await checkTime(dailyinfo.startTime.value, dailyinfo.endTime.value)) {
        return;
      }
    }
  }

  // 引継情報.計画書様式を設定する。
  localOneway.or28646Data.kikanFlg = local.or06632.kikanFlg
  localOneway.or28646Data.shisetuId = systemCommonsStore.getShisetuId ?? ''
  localOneway.or28646Data.svJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.or28646Data.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  localOneway.or28646Data.syubetsuId = '1'
  // Or28646のダイアログ開閉状態を更新する
  Or28646Logic.state.set({
    uniqueCpId: or28646_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}
// ダイアログ表示フラグ
const showDialogOr28646 = computed(() => {
  // Or28646のダイアログ開閉状態
  return Or28646Logic.state.get(or28646_1.value.uniqueCpId)?.isOpen ?? false
})


/**
 * 取込を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => local.or28646,
  () => {
    console.log('local.or28646.weekTableDetailList', local.or28646.weekTableDetailList);

    local.or06632.dailyInfoList = [];
    // 「内容情報」リスト
    local.or28646.weekTableDetailList.forEach((item) => {
      local.or06632.dailyInfoList.push({
        // カウンタ
        id: '',
        uuId: uuidv4(),
        // 開始時間
        startTime: item.kaishiJikan,
        // 終了時間
        endTime: item.shuuryouJikan,
        // 区分
        dataKbn: item.dispMode,
        // 内容CD
        naiyoCd: item.naiyoCd ,
        // 内容
        naiyoKnj: item.contents ,
        // 日常処遇サービスメモ
        memoKnj:  item.memoKnj ,
        // 担当者
        tantoKnj: '' ,
        // 文字サイズ
        fontSize: item.fontSize ,
        // 文字サイズ
        fontSizeTitle: item.fontSize ,
        // 文字位置
        alignment: item.alignment ,
        // 文字カラー
        fontColor: convertDecimalToHex(Number(item.fontColor)),
        // 背景カラー
        backColor: convertDecimalToHex(Number(item.backColor)),
        // 時間表示区分
        timeKbn:  item.timeKbn ,
        /** 内容担当更新区分 */
        updateKbn: UPDATE_KBN.CREATE,
        /** 更新回数 */
        modifiedCnt: '',
        zIndex:1000,
      })
    })
    initGui00991TableData();
    Or28528Logic.data.set({
      uniqueCpId: or28528_1.value.uniqueCpId,
      value: {
        items:local.or28528.items,
      },
    })
    initGui00992TableData();
    Or28220Logic.data.set({
      uniqueCpId: or28220_1.value.uniqueCpId,
      value: {
        items:local.or28220.items,
      },
    })
    initGui00993TableData();
    Or28221Logic.data.set({
      uniqueCpId: or28221_1.value.uniqueCpId,
      value: {
        items:local.or28221.items,
      },
    })
  }
)

/**
 * Or10826ダイアログを開く処理
 * - ボタン押下時に呼び出される
 * - 子コンポーネントのバインドデータを設定し、ダイアログを開く
 *
 */
async function showGUI00990() {
  // 操作区分が3:削除の場合
  // 期間管理フラグが「1:管理する」、かつ、計画対象期間リストが0件の場合
  // 処理終了にする。
  if (local.or06632.operaFlg === Or06632Const.OPERA_FLG_3
    ||
    (local.or06632.kikanFlg === Or06632Const.PLAN_TARGET_PERIOD_1
      && local.or06632.kikanObj.pagingFlg === Or06632Const.PAGING_FLAG_0)) {
    return;
  }
  // 日常
  if (local.or28528.items && local.or28528.items.length > 0) {
    for (const dailyinfo of local.or28528.items) {
      if (!await checkTime(dailyinfo.startTime.value, dailyinfo.endTime.value)) {
        return;
      }
    }
  }
  // 処遇
  if (local.or28220.items && local.or28220.items.length > 0) {
    for (const dailyinfo of local.or28220.items) {
      if (!await checkTime(dailyinfo.startTime.value, dailyinfo.endTime.value)) {
        return;
      }
    }
  }
  // 介護
  if (local.or28221.items && local.or28221.items.length > 0) {
    for (const dailyinfo of local.or28221.items) {
      if (!await checkTime(dailyinfo.startTime.value, dailyinfo.endTime.value)) {
        return;
      }
    }
  }
  localOneway.or10826Oneway.mstKbn  = '1'
  // Or10826のダイアログを開く
  Or10826Logic.state.set({
    uniqueCpId: or10826_1.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 取込を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => local.or01533,
  () => {
    local.or06632.dailyInfoList = [];
    // 「内容情報」リスト
    local.or01533.dailyInfoList.forEach((item) => {
      local.or06632.dailyInfoList.push({
        // カウンタ
        id: '',
        uuId: uuidv4(),
        // 開始時間
        startTime: item.startTime,
        // 終了時間
        endTime: item.endTime,
        // 区分
        dataKbn: item.dataKbn,
        // 内容CD
        naiyoCd: item.naiyoCd ,
        // 内容
        naiyoKnj: item.naiyoKnj ,
        // 日常処遇サービスメモ
        memoKnj:  item.memoKnj ,
        // 担当者
        tantoKnj: item.tantoKnj ,
        // 文字サイズ
        fontSize: item.fontSize ,
        // 文字サイズ
        fontSizeTitle: item.fontSize ,
        // 文字位置
        alignment: item.alignment ,
        // 文字カラー
        fontColor: convertDecimalToHex(Number(item.fontColor)),
        // 背景カラー
        backColor: convertDecimalToHex(Number(item.backColor)),
        // 時間表示区分
        timeKbn:  item.timeKbn ,
        /** 内容担当更新区分 */
        updateKbn: UPDATE_KBN.CREATE,
        /** 更新回数 */
        modifiedCnt: '',
        zIndex:1000,
      })
    })
    initGui00991TableData();
    Or28528Logic.data.set({
      uniqueCpId: or28528_1.value.uniqueCpId,
      value: {
        items:local.or28528.items,
      },
    })
    initGui00992TableData();
    Or28220Logic.data.set({
      uniqueCpId: or28220_1.value.uniqueCpId,
      value: {
        items:local.or28220.items,
      },
    })
    initGui00993TableData();
    Or28221Logic.data.set({
      uniqueCpId: or28221_1.value.uniqueCpId,
      value: {
        items:local.or28221.items,
      },
    })

    // 複写用「日課表サービス例」リスト
    const or06632SvInfoData = local.or06632.svInfoList.filter(item => !isUndefined(item.svId) && item.svId !== '' && item.updateKbn !== UPDATE_KBN.CREATE)
    or06632SvInfoData.forEach(item => item.updateKbn = UPDATE_KBN.DELETE)
    // 複写用「日課表特記事項」情報
    local.or06632.tokkiKnj.tokkiKnj = ''
    local.or06632.tokkiKnj.sonotaKnj = ''
    local.orX0114.tokkiKnj = { value: '' }
    local.orX0114.sonotaKnj =  { value: '' }
    if (local.or06632.tokkiKnjInfo) {
      local.or06632.tokkiKnj.tokkiKnj = local.or06632.tokkiKnjInfo.tokkiKnj
      local.or06632.tokkiKnj.sonotaKnj = local.or06632.tokkiKnjInfo.sonotaKnj
      local.or06632.tokkiKnj.kaiteiFlg = '2'
      local.orX0114.tokkiKnj = { value: local.or06632.tokkiKnj.tokkiKnj }
      local.orX0114.sonotaKnj = { value: local.or06632.tokkiKnj.sonotaKnj }
      if (local.or06632.operaFlg === Or06632Const.ACTION_TYPE.INIT) {
        local.or06632.tokkiKnj.updateKbn = UPDATE_KBN.UPDATE
      }
    }

    local.or06632.svInfoList = [];
    local.or06632.svList = [];
    local.orX0114.svList = [];
    if (local.or06632.svList) {
      local.or06632.svList.forEach((item) => {
        local.or06632.svInfoList.push({
          svId: item.svId,
          dataKbn: item.dataKbn,
          tekiyoFlg: item.tekiyoFlg,
          modifiedCnt: '',
          updateKbn: UPDATE_KBN.CREATE
        })
        local.or06632.svList?.push({
          svId: item.svId,
          dataKbn: item.dataKbn,
          tekiyoFlg: item.tekiyoFlg,
          modifiedCnt: '',
        })
      })
      local.orX0114.svList = getSvList()
    }
    if (or06632SvInfoData) {
      or06632SvInfoData.forEach((item) => {
        local.or06632.svInfoList.push({
          ...item
        })
      })
    }
    OrX0114Logic.data.set({
      uniqueCpId: orX0114_1.value.uniqueCpId,
      value: {
        tokkiKnj: local.orX0114.tokkiKnj,
        sonotaKnj:local.orX0114.sonotaKnj,
        svList: local.orX0114.svList,
      },
    })
    set28153()
  }
)

// ダイアログ表示フラグ
const showDialogOr10827 = computed(() => {
  // Or10827のダイアログ開閉状態
  return Or10827Logic.state.get(or10827_1.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 「マスタ他設定アイコンボタン」押下
 */
async function _master() {
  if (
    !(await gyoumuCom.checkEdit(
      isEdit.value,
      await hasRegistAuth(Or06632Const.LINK_AUTH),
      showConfirmMessageBox,
      showWarnMessageBox,
      _save
    ))
  ) {
    return
  }
  //ポップアップで起動
  showMasterDialog()
  //ポップアップ画面が画面閉じる場合
  await getMasterDialogResult()
  //共通情報.計画期間ＩＤ
  setCommonPlanPeriod({ ...local.or06632.kikanObj, sc1Id: '0' })
  //共通情報.履歴ＩＤ
  local.or06632.rirekiId = '0'
  //操作区分
  local.or06632.operaFlg = Or06632Const.OPERA_FLG_0
}


/**
 * マスタ他のダイアログを表示
 */
function showMasterDialog() {
  //共通からの事業所IDリスト
  const svjigyoIdList: string[] = systemCommonsStore.getSvJigyoIdList as string[]
  //施設ID
  localOneway.or10827Oneway.shisetuId = systemCommonsStore.getShisetuId ?? ''
  //事業所ID
  localOneway.or10827Oneway.svJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.or10827Oneway.svJigyoIdList = svjigyoIdList
  // Or10827のダイアログ開閉状態を更新する
  Or10827Logic.state.set({
    uniqueCpId: or10827_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * ポップアップ画面が画面閉じる場合
 */
const getMasterDialogResult = () => {
  return new Promise((resolve) => {
    watch(
      () => Or10827Logic.state.get(or10827_1.value.uniqueCpId)?.isOpen,
      (newValue) => {
        resolve(newValue)
      },
      { once: true }
    )
  })
}
/**
 * ダイアログ表示フラグ
 */
const showDialogOr10826 = computed(() => {
  return Or10826Logic.state.get(or10826_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「削除」押下
 */
async function _delete() {
  //下記の条件がいずれか満たす場合、
  //・操作区分 = 3:削除の場合
  //・表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    local.or06632.operaFlg === Or06632Const.OPERA_FLG_3 ||
    local.or06632.kikanObj.pagingFlg === Or06632Const.PAGING_FLAG_0
  ) {
    return
  }
  const dialogResult = await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11326', [local.orX0010.value, t('label.daily-table')]),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
  if (dialogResult?.secondBtnClickFlg === true) {
    return
  }
  //操作区分 = 3:削除
  local.or06632.operaFlg = Or30564Const.OPERA_FLAG_3
  //画面項目の非活性・非表示設定
  OrX0009Logic.state.set({
    uniqueCpId: orX0009_1.value.uniqueCpId,
    state: {
      isDisabled: true,
    },
  })
  OrX0010Logic.state.set({
    uniqueCpId: orX0010_1.value.uniqueCpId,
    state: {
      isDisabled: true,
    },
  })
  Or15843Logic.state.set({
    uniqueCpId: or15843_1.value.uniqueCpId,
    state: {
      isDisabled: true,
    },
  })
  localOneway.or28153Oneway.showFlg = false
}

/**
 * 保存時処理(checkEdit含めて保存処理)
 * False: 処理中止（取消、いいえ）と保存権限がない時の取消操作、
 * True：処理継続（保存と保存しない）と保存権限がない時の処理継続
 */
async function checkEditBySave(): Promise<boolean> {
  //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
  if (
    !(await gyoumuCom.checkEdit(
      isEdit.value,
      await hasRegistAuth(Or06632Const.LINK_AUTH),
      showConfirmMessageBox,
      showWarnMessageBox,
      _save
    ))
  ) {
    return false
  }
  return true
}

/**
 * メッセージを表示する
 */
const showConfirmMessageBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * メッセージを表示する
 */
const showWarnMessageBox = async () => {
  return await openWarnDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.w-com-10006'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * メッセージを表示する
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openWarnDialog(state: Or21815StateType): Promise<Or21815EventType | undefined> {
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * errorダイアログを閉じたタイミングで結果を返却
 *
 * @param msg - msg
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
 async function openErrorDialog(msg: string): Promise<string> {
  // 選択行削除確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: 'blank',
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'blank',
    },
  })

  /**
   * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
   *
   * @returns ダイアログの選択結果（yes, no）
   */
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813_1.value.uniqueCpId)

        let result = Or06632Const.DEFAULT.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = Or06632Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <c-v-sheet class="view">
    <!-- Or11871：有機体：画面メニューエリア -->
     <div class="sticky-header">
      <g-base-or11871 v-bind="or11871">

      <template #createItems>
        <c-v-list-item
          :title="t('btn.copy')"
          @click="onCreateCopy()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.week-table-pattern-btn')"
          />
        </c-v-list-item>
      </template>
      <template #customButtons>
          <!-- パターン -->
          <base-mo00611
            class="mr-0 pa-0"
            style="height:32px"
            :oneway-model-value="localOneway.mo00611OnewayPattern"
            @click="showGUI00990()"
          >
          </base-mo00611>
      </template>
      <template #optionMenuItems>
        <!-- ログ -->
        <c-v-list-item
          :title="t('btn.log')"
          prepend-icon="open_in_browser"
          @click="onClickLog()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-show-log-btn')"
          />
        </c-v-list-item>
      </template>
    </g-base-or11871>
    </div>
    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col
        cols="auto"
        class="hidden-scroll h-100 user-select"
      >
        <!-- （利用者基本）利用者選択の表示 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <c-v-col class="hidden-scroll h-100 pl-0 ml-0 pr-0">
        <c-v-sheet class="d-flex flex-column h-100">
          <c-v-container class="pl-0 pr-0" style="padding-bottom: 50px;">
            <c-v-row class="second-row-area pl-6">
              <c-v-col
                cols="auto"
              >
                <!-- 事業所 -->
                <g-base-or-41179 v-bind="or41179_1" width="224"/>
              </c-v-col>
              <c-v-col
                cols="auto"
              >
                <!-- 計画対象期間 -->
                <g-custom-orX0007
                  v-bind="orX0007_1"
                  :oneway-model-value="localOneway.orX0007Oneway"
                  :unique-cp-id="orX0007_1.uniqueCpId"
                  :parent-method="checkEditBySave"
                />
              </c-v-col>
              <!-- 作成日 -->
              <c-v-col cols="auto" >
                <g-custom-orX0010
                  v-if="local.or06632.showFlg"
                  v-bind="orX0010_1"
                  :oneway-model-value="localOneway.orX0010Oneway"
                  :unique-cp-id="orX0010_1.uniqueCpId"
                />
              </c-v-col>
              <!-- 作成者 -->
              <c-v-col cols="auto">
                <g-custom-orX0009
                  v-if="local.or06632.showFlg"
                  v-bind="orX0009_1"
                  :oneway-model-value="localOneway.orX0009Oneway"
                  :unique-cp-id="orX0009_1.uniqueCpId"
                  :parent-method="checkEditBySave"
                />
              </c-v-col>
              <!-- 履歴 -->
              <c-v-col cols="auto" >
                <g-custom-orX0008
                  v-if="local.or06632.showFlg"
                  v-bind="orX0008_1"
                  :oneway-model-value="localOneway.orX0008Oneway"
                  :unique-cp-id="orX0008_1.uniqueCpId"
                  :parent-method="_save"
                />
              </c-v-col>
              <!-- ケース番号 -->
              <c-v-col cols="auto" >
                <g-custom-or15843
                  v-if="local.or06632.showFlg"
                  v-bind="or15843_1"
                  :oneway-model-value="localOneway.or15843Oneway"
                  :unique-cp-id="or15843_1.uniqueCpId"
                  width="106px"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row no-gutters>
              <c-v-col>
                <base-mo00043
                  v-model="local.mo00043"
                  :oneway-model-value="localOneway.mo00043OneWay"
                ></base-mo00043>
              </c-v-col>
            </c-v-row>
            <c-v-window
              v-model="local.mo00043.id"
            >
              <c-v-window-item value="dailyTableImage" class="pl-6">
                <g-custom-or-28153
                  v-bind="or28153_1"
                  :has-view-authflg="local.hasViewAuthflg"
                  :oneway-model-value="localOneway.or28153Oneway"
                  @double-click="handleDoubleClick"
                  @mouse-down="handelMousedown"
                  @click-gui00998="showGUI00998()"
                ></g-custom-or-28153>
              </c-v-window-item>
              <!--日常生活活動等-->
              <c-v-window-item
                value="dailyLifeActivities"  class="pl-6"
              >
                <g-custom-Or28528
                  v-bind="or28528_1"
                  :oneway-model-value="localOneway.or28528Oneway"
                  :unique-cp-id="or28528_1.uniqueCpId"
                  :has-view-authflg="local.hasViewAuthflg"
                  @click-gui00998="showGUI00998()"
                />
              </c-v-window-item>
              <!-- 自立支援に関する処遇 -->
              <c-v-window-item
                value="treatmentRelatedToSelfRelianceSupport"  class="pl-6"
              >
                <g-custom-Or28220
                  v-bind="or28220_1"
                  :oneway-model-value="localOneway.or28220Oneway"
                  :unique-cp-id="or28220_1.uniqueCpId"
                  :has-view-authflg="local.hasViewAuthflg"
                  @click-gui00998="showGUI00998()"
                />
              </c-v-window-item>
              <!--介護(介護予防)サービス-->
              <c-v-window-item
                value="nursingPreventiveCareServices"  class="pl-6"
              >
                <g-custom-Or28221
                  v-bind="or28221_1"
                  :oneway-model-value="localOneway.or28221Oneway"
                  :unique-cp-id="or28221_1.uniqueCpId"
                  :has-view-authflg="local.hasViewAuthflg"
                  @click-gui00998="showGUI00998()"
                />
              </c-v-window-item>
              <!--GUI00994_特記事項、サービス例-->
              <c-v-window-item
                value="specialNotesServicesExamples"
                class="pl-6"
              >
                <g-custom-or-x-0114
                  ref="orX0114Ref"
                  :oneway-model-value="localOneway.orX0114Oneway"
                  v-bind="orX0114_1"
                />
              </c-v-window-item>
            </c-v-window>
            <c-v-row no-gutters>
              <g-custom-or-27539
                v-if="showDialogOr27539"
                :key="or27539_1"
                v-bind="or27539_1"
                v-model="local.or27539"
                :unique-cp-id="or27539_1.uniqueCpId"
                :oneway-model-value="localOneway.or27539Oneway"
              />
              <g-custom-or-36014
                v-if="showDialogOr36014"
                v-bind="or36014_1"
                :oneway-model-value="localOneway.or36014Oneway"
                :unique-cp-id="or36014_1.uniqueCpId"
                @update:model-value="handleOr36014Confirm"
              />
                <!-- 印刷設定画面 -->
              <g-custom-or-28401
                v-if="showDialogOr28401"
                v-bind="or28401_1"
                :oneway-model-value="localOneway.or28401Oneway"
                :unique-cp-id="or28401_1.uniqueCpId"
              />
              <g-custom-or-28646
                v-if="showDialogOr28646"
                v-bind="or28646_1"
                v-model="local.or28646"
                :oneway-model-value="localOneway.or28646Data"
                :unique-cp-id="or28646_1.uniqueCpId"
              />
              <g-custom-or-10826
                v-if="showDialogOr10826"
                v-bind="or10826_1"
                v-model="local.or01533"
                :oneway-model-value="localOneway.or10826Oneway"
              />
              <g-custom-or-10827
                v-if="showDialogOr10827"
                v-bind="or10827_1"
                :oneway-model-value="localOneway.or10827Oneway"
                :unique-cp-id="or10827_1.uniqueCpId"
              />
              <!-- スロットの使用例 -->
              <g-base-or21815
                v-if="showDialogOr21815"
                v-bind="or21815_1"
              >
              </g-base-or21815>
              <!-- スロットの使用例 -->
              <g-base-or21814
                v-if="showDialogOr21814"
                v-bind="or21814_1"
              >
              </g-base-or21814>
              <!-- スロットの使用例 -->
              <g-base-or21813
                v-if="showDialogOr21813"
                v-bind="or21813_1"
              >
              </g-base-or21813>
            </c-v-row>
          </c-v-container>
        </c-v-sheet>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
</template>

<style scoped lang="scss">
$margin-organism: 24px; // オーガニズム間のマージン
$margin-common: 48px; // 一般的なマージン


.divider-class {
  border-width: thin;
  margin: 8px 0px;
}
.divider-noLine-class {
  border: none;
  margin: 32px 0px;
  border-color: white;
}
:deep(.v-input__control) {
  background-color: #fff;
}
.h-95 {
  width: 95px
}
.h-135 {
  width: 135px
}
.h-162 {
  width: 162px
}
.view {
  display: flex;
  flex-direction: column;
  // 背景色：テーマの背景色を使用
  background-color: rgb(var(--v-theme-background)) !important;
  // 幅：内容で広く
  min-height: max-content !important;
  // 画面トップの固定ヘッダー
  .sticky-header {
    position: sticky; // スクロール時に固定
    top: $margin-common; // トップからの距離
    z-index: 999; // 他の要素よりも上に表示
    background: rgb(var(--v-theme-background)); // テーマの背景色
    width: 100%; // 全幅
  }
}
// 利用者一覧選択領域領域
:deep(.user-select > .v-card) {
  width:224px !important;
  padding: 0px 0px 0px 23px !important; // パディング設定
  width: max-content !important; // 幅を自動調整
  margin-top: 16px !important; // 上部マージン
  // パンディング設定
  .v-card-actions {
    width: max-content !important; // 幅を自動調整
    padding: 8px !important; // パディング設定
    padding-left: 0px !important; // パディング設定
  }
  // 選択領域の幅設定
  .user-info-section-container-style {
    width: 260px; // 幅を260pxに設定
  }
}

.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
}
.content {
  padding: 5px 0px;
  overflow-x: auto;
}
.second-row-area {
  .second-row-group {
    display: flex;
    gap: 8px;
  }
}
.button-area {
  margin-top: 8px;
  margin-bottom: 8px;
}

:deep(.v-sheet) {
  background-color: transparent !important;
  margin: 0 !important;
}
.tab-area {
  padding-left:24px !important;
}

</style>
