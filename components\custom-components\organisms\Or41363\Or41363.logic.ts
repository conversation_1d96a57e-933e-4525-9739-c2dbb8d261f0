import { Or41363Const } from './Or41363.constants'
import type { Or41363EventType, Or41363StateType } from './Or41363.type'
import {
  useTwoWayBindAccessor,
  useInitialize,
  useOneWayBindAccessor,
  useEventStatusAccessor,
} from '~/composables/useComponentLogic'
import type { Mo01334Type } from '~/types/business/components/Mo01334Type'
import { Or41364Const } from '~/components/custom-components/organisms/Or41364/Or41364.constants'
import { Or41364Logic } from '~/components/custom-components/organisms/Or41364/Or41364.logic'

/**
 * Or41363:有機体:（職員管理）検索結果表示用一覧
 * 処理ロジック
 */
export namespace Or41363Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize<Mo01334Type>({
      cpId: Or41363Const.CP_ID(0),
      uniqueCpId,

      // editFlgNecessity: false,

      initTwoWayValue: {
        value: '',
        values: [],
      },
      initOneWayState: {
        items: Or41363Const.DEFAULT.ITEMS,
      },
      childCps: [{ cpId: Or41364Const.CP_ID(1) }],
    })

    Or41364Logic.initialize(childCpIds[Or41364Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
    }
  }

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Mo01334Type>(Or41363Const.CP_ID(0))

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or41363StateType>(Or41363Const.CP_ID(0))

  /**
   * EventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useEventStatusAccessor<Or41363EventType>(Or41363Const.CP_ID(0))
}
