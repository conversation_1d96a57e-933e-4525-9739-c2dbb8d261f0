<script setup lang="ts">
/**
 * Or33216:［フェースシート（パッケージプラン）］①画面
 * GUI00635_［フェースシート（パッケージプラン）］①画面
 *
 * @description
 * ［フェースシート（パッケージプラン）］②画面
 *
 * <AUTHOR>
 */
import { cloneDeep } from 'lodash'
import { computed, onBeforeMount, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import { Or28389Const } from '~/components/custom-components/organisms/Or28389/Or28389.constants'
import { Or28389Logic } from '~/components/custom-components/organisms/Or28389/Or28389.logic'
import { Or33216Const } from '~/components/custom-components/organisms/Or33216/Or33216.constants'
import { Or51773Const } from '~/components/custom-components/organisms/Or51773/Or51773.constants'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  FaceSheetPackage1PreviousAddressSelectInEntity,
  FaceSheetPackage1PreviousAddressSelectOutEntity,
} from '~/repositories/cmn/entities/FaceSheetPackage1PreviousAddressSelectEntity'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo01266OnewayType } from '~/types/business/components/Mo01266Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { Or26257OnewayType, Or26257Type } from '~/types/cmn/business/components/Or26257Type'
import type { Or28389OnewayType, Or28389Type } from '~/types/cmn/business/components/Or28389Type'
import type { Or33216OnewayType, Or33216Type } from '~/types/cmn/business/components/Or33216Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { OrX0157OnewayType } from '~/types/cmn/business/components/OrX0157Type'
import { CustomClass } from '~/types/CustomClassType'

const { t } = useI18n()
// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or33216OnewayType
}
// ①インターフェース定義
const props = defineProps<Props>()

// ②デフォルト定義
/** セレクトボックス */
const mo00040OnewayType = {
  showItemLabel: false,
  customClass: new CustomClass({ outerClass: '' }),
} as Mo00040OnewayType
/** テキストフィールド */
const mo00045OnewayType = {
  customClass: {
    outerClass: '',
    labelClass: 'mb-1',
  },
  showItemLabel: false,
} as Mo00045OnewayType
/** フォームラベル */
const mo00615OnewayType = {
  showItemLabel: true,
  showRequiredLabel: false,
  customClass: {
    outerStyle: 'background: none',
  },
} as Mo00615OnewayType
/** 入力補助付きテキストエリア */
const orX0156OnewayType = {
  showItemLabel: false,
  noResize: true,
  showDividerLineFlg: false,
} as OrX0156OnewayType
/** 入力支援付きテキストフィールド */
const orX0157OnewayType = {
  showEditBtnFlg: true,
  editBtnClass: 'custom-edit-btn',
  text: {
    orX0157InputOneway: {
      customClass: {
        outerClass: '',
        labelClass: 'mb-1',
      },
    },
  },
} as OrX0157OnewayType
/** 優先度1リンクボタン */
const mo01266OnewayType = {
  to: '',
  color: '#214d97',
  minWidth: '0px',
} as unknown as Mo01266OnewayType

// ③双方向バインド用の内部変数
const { refValue } = useScreenTwoWayBind<Or33216Type>({
  cpId: Or33216Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// ローカルTwoWayBind
const local = reactive({
  // 汎用コード取得
  commonCode: {
    selectCdKbn442List: [] as CodeType[],
    selectCdKbn443List: [] as CodeType[],
    selectCdKbn444List: [] as CodeType[],
  },

  or51775: { modelValue: '' } as Or51775Type,
  or28389: { kankeishaList: [] } as Or28389Type,
  or26257: {} as Or26257Type,
})

// ④片方向バインド用の内部変数
const localOneway = reactive({
  or33216: {
    ...cloneDeep(Or33216Const.DEFAULT.ONE_WAY),
    ...props.onewayModelValue,
  },

  // 入所措置の端緒テキストエリア
  orX0156NyushoSochiMatter: {
    ...orX0156OnewayType,
    maxlength: '448',
    rows: '8',
    maxRows: '8',
  } as OrX0156OnewayType,

  // 生活歴テキストエリア
  orX0156SeikaturekiMatter: {
    ...orX0156OnewayType,
    maxlength: '528',
    rows: '8',
    maxRows: '8',
  } as OrX0156OnewayType,

  // 前住所
  orX0157ZenAddress: {
    ...orX0157OnewayType,
    text: {
      orX0157InputOneway: {
        ...orX0157OnewayType.text?.orX0157InputOneway,
        itemLabel: t('label.pre-address'),
        showItemLabel: true,
        maxlength: '70',
        isVerticalLabel: true,
        width: '673px',
      },
    },
  } as OrX0157OnewayType,
  // 措置の実施機関
  orX0157AgencyForMeasures: {
    ...orX0157OnewayType,
    text: {
      orX0157InputOneway: {
        ...orX0157OnewayType.text?.orX0157InputOneway,
        itemLabel: t('label.agency-for-measures'),
        showItemLabel: true,
        maxlength: '28',
        isVerticalLabel: true,
        width: '305px',
      },
    },
  } as OrX0157OnewayType,
  // 担当CW氏名
  orX0157ChargeCwName: {
    ...orX0157OnewayType,
    inputReadonly: true,
    text: {
      orX0157InputOneway: {
        ...orX0157OnewayType.text?.orX0157InputOneway,
        itemLabel: t('label.charge-cw-name'),
        showItemLabel: true,
        maxlength: '16',
        isVerticalLabel: true,
        width: '227px',
      },
    },
  } as OrX0157OnewayType,
  // 住居の状況
  orX0157ResidenceStatus: {
    ...orX0157OnewayType,
    text: {
      orX0157InputOneway: {
        showItemLabel: false,
        maxlength: '38',
        width: '286px',
      },
    },
  } as OrX0157OnewayType,
  // 退所する場合の帰来先の有無
  orX0157ExistenceOfReturn: {
    ...orX0157OnewayType,
    text: {
      orX0157InputOneway: {
        showItemLabel: false,
        maxlength: '40',
        width: '286px',
      },
    },
  } as OrX0157OnewayType,

  // 家族知人等の状況
  mo01266StatusOfFamilyAcquaintances: {
    ...mo01266OnewayType,
    btnLabel: t('label.status-of-family-acquaintances'),
  } as Mo01266OnewayType,
  // 家族構成図
  mo01266FamilyStructureDiagramOther: {
    ...mo01266OnewayType,
    btnLabel: t('label.family-structure-diagram-other'),
  } as Mo01266OnewayType,

  // ファイル選択
  mo00611FileSelect: {
    btnLabel: t('label.file-select'),
    labelColor: '#869fca',
    width: '114px',
  } as Mo00611OnewayType,

  // 身元引受人は続柄に☆
  mo00615guarantorRelationshipMark: {
    ...mo00615OnewayType,
    itemLabel: t('label.guarantor-relationship-mark'),
  } as Mo00615OnewayType,
  // 氏名
  mo00615Name: {
    ...mo00615OnewayType,
    itemLabel: t('label.name'),
  } as Mo00615OnewayType,
  // 続柄
  mo00615Relationship: {
    ...mo00615OnewayType,
    itemLabel: t('label.relationship'),
  } as Mo00615OnewayType,
  // 住所
  mo00615Address: {
    ...mo00615OnewayType,
    itemLabel: t('label.address'),
  } as Mo00615OnewayType,
  // 連絡先(TEL等)
  mo00615ContactAddressTel: {
    ...mo00615OnewayType,
    itemLabel: t('label.contact-address-tel'),
  } as Mo00615OnewayType,
  // 順位
  mo00615Order: {
    ...mo00615OnewayType,
    itemLabel: t('label.order'),
  } as Mo00615OnewayType,
  // ＊入所者が頼りにしている者に※
  mo00615ToThoseWhoAreDependentOn: {
    ...mo00615OnewayType,
    itemLabel: t('label.to-those-who-are-dependent-on'),
  } as Mo00615OnewayType,
  // 出生から成人期、成人期から壮年期、壮年期以降等ライフステージごとの視点で記載すること
  mo00615DescriptionComment: {
    ...mo00615OnewayType,
    itemLabel: t('label.description-comment'),
  } as Mo00615OnewayType,
  // 住居の状況
  mo00615ResidenceStatus: {
    ...mo00615OnewayType,
    itemLabel: t('label.residence-status'),
  } as Mo00615OnewayType,
  // 退所する場合の帰来先の有無
  mo00615ExistenceOfReturn: {
    ...mo00615OnewayType,
    itemLabel: t('label.existence-of-return'),
  } as Mo00615OnewayType,

  // 入所年月日テキストフィールド
  mo00020NyushoYmdInput: {
    ...mo00045OnewayType,
    itemLabel: t('label.admission-date'),
    showItemLabel: true,
    maxlength: '10',
    width: '136px',
  } as Mo00020OnewayType,

  // 氏名1テキストフィールド
  mo00045Kazoku1Input: {
    ...mo00045OnewayType,
    maxlength: '16',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 氏名2テキストフィールド
  mo00045Kazoku2Input: {
    ...mo00045OnewayType,
    maxlength: '16',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 氏名3テキストフィールド
  mo00045Kazoku3Input: {
    ...mo00045OnewayType,
    maxlength: '16',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 氏名4テキストフィールド
  mo00045Kazoku4Input: {
    ...mo00045OnewayType,
    maxlength: '16',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 氏名5テキストフィールド
  mo00045Kazoku5Input: {
    ...mo00045OnewayType,
    maxlength: '16',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 氏名6テキストフィールド
  mo00045Kazoku6Input: {
    ...mo00045OnewayType,
    maxlength: '16',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 氏名7テキストフィールド
  mo00045Kazoku7Input: {
    ...mo00045OnewayType,
    maxlength: '16',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 氏名8テキストフィールド
  mo00045Kazoku8Input: {
    ...mo00045OnewayType,
    maxlength: '16',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,

  // 住所1テキストフィールド
  mo00045KzokuAddr1Input: {
    ...mo00045OnewayType,
    maxlength: '30',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 住所2テキストフィールド
  mo00045KzokuAddr2Input: {
    ...mo00045OnewayType,
    maxlength: '30',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 住所3テキストフィールド
  mo00045KzokuAddr3Input: {
    ...mo00045OnewayType,
    maxlength: '30',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 住所4テキストフィールド
  mo00045KzokuAddr4Input: {
    ...mo00045OnewayType,
    maxlength: '30',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 住所5テキストフィールド
  mo00045KzokuAddr5Input: {
    ...mo00045OnewayType,
    maxlength: '30',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 住所6テキストフィールド
  mo00045KzokuAddr6Input: {
    ...mo00045OnewayType,
    maxlength: '30',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 住所7テキストフィールド
  mo00045KzokuAddr7Input: {
    ...mo00045OnewayType,
    maxlength: '30',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 住所8テキストフィールド
  mo00045KzokuAddr8Input: {
    ...mo00045OnewayType,
    maxlength: '30',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,

  // 連絡先(TEL等)1テキストフィールド
  mo00045KazokuRenraku1Input: {
    ...mo00045OnewayType,
    maxlength: '14',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 連絡先(TEL等)2テキストフィールド
  mo00045KazokuRenraku2Input: {
    ...mo00045OnewayType,
    maxlength: '14',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 連絡先(TEL等)3テキストフィールド
  mo00045KazokuRenraku3Input: {
    ...mo00045OnewayType,
    maxlength: '14',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 連絡先(TEL等)4テキストフィールド
  mo00045KazokuRenraku4Input: {
    ...mo00045OnewayType,
    maxlength: '14',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 連絡先(TEL等)5テキストフィールド
  mo00045KazokuRenraku5Input: {
    ...mo00045OnewayType,
    maxlength: '14',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 連絡先(TEL等)6テキストフィールド
  mo00045KazokuRenraku6Input: {
    ...mo00045OnewayType,
    maxlength: '14',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 連絡先(TEL等)7テキストフィールド
  mo00045KazokuRenraku7Input: {
    ...mo00045OnewayType,
    maxlength: '14',
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00045OnewayType,
  // 連絡先(TEL等)8テキストフィールド
  mo00045KazokuRenraku8Input: {
    ...mo00045OnewayType,
    customClass: new CustomClass({ outerClass: '' }),
    maxlength: '14',
  } as Mo00045OnewayType,

  // 身元引受人1~8プルダウン
  mo00040Mimoto: {
    ...mo00040OnewayType,
    items: [
      {
        value: Or33216Const.MIMOTO_FLAG_FALSE,
        title: '',
      },
      {
        value: Or33216Const.MIMOTO_FLAG_TRUE,
        title: t('label.star'),
      },
    ],
  } as Mo00040OnewayType,
  // 続柄1~8プルダウン
  mo00040KzokuSelect: {
    ...mo00040OnewayType,
    customClass: new CustomClass({ outerClass: '', itemStyle: 'min-Width:168px' }),
    items: props.onewayModelValue.zokugaraList,
  } as Mo00040OnewayType,

  // 順位1~8テキストフィールド
  mo00038KazokuYusenInput: {
    mo00045Oneway: {
      ...mo00045OnewayType,
      maxlength: '4',
      customClass: new CustomClass({ outerClass: '' }),
    },
    min: -999,
    max: 9999,
    isEditCamma: false,
  } as Mo00038OnewayType,

  // 福祉事務所ラジオボタン
  mo00039WelfareOfficeRadio: {
    name: 'welfare-office',
    showItemLabel: true,
    itemLabel: t('label.welfare-office'),
    customClass: {
      outerClass: '',
      outerStyle: undefined,
      labelClass: 'mb-1',
      labelStyle: undefined,
      itemClass: undefined,
      itemStyle: undefined,
    },
    items: [],
  } as Mo00039OnewayType,
  // 住居の状況区分ラジオボタン
  mo00039ResidenceStatusRadio: {
    name: 'residence-status',
    showItemLabel: false,
    customClass: {
      outerClass: '',
      outerStyle: undefined,
      labelClass: undefined,
      labelStyle: undefined,
      itemClass: undefined,
      itemStyle: undefined,
    },
    items: [],
  } as Mo00039OnewayType,
  // 帰来先の有無区分ラジオボタン
  mo00039ExistenceOfReturnRadio: {
    name: 'existence-of-return',
    showItemLabel: false,
    customClass: {
      outerClass: '',
      outerStyle: undefined,
      labelClass: undefined,
      labelStyle: undefined,
      itemClass: undefined,
      itemStyle: undefined,
    },
    items: [],
  } as Mo00039OnewayType,

  // GUI00937 共通入力支援画面
  or51775: {
    title: '',
    screenId: Or33216Const.SCREEN_ID,
    bunruiId: '',
    t1Cd: '2010',
    t2Cd: '',
    t3Cd: '0',
    tableName: 'cpn_tuc_syp_face21',
    columnName: '',
    assessmentMethod: systemCommonsStore.getAuthzOtherCaremanager?.assessmentMethod ?? '',
    inputContents: '',
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    mode: '',
  } as Or51775OnewayType,

  // GUI00642 親族・関係者選択画面
  or28389: { userId: systemCommonsStore.getUserSelectSelfId() ?? '' } as Or28389OnewayType,

  // GUI00220 職員検索画面
  or26257: {
    sysCdKbn: '', // システム略称
    secAccountAllFlg: '0',
    svJigyoIdList: [],
    shokuinId: '',
    gsysCd: '', // システムコード
    selectMode: '12',
    kijunYmd: systemCommonsStore.getSystemDate ?? '',
    defSvJigyoId: '',
    filterDwFlg: '1',
    koyouState: '0',
    areaFlg: '0',
    hyoujiColumnList: [{ hyoujiColumn: 'shokushu_id' }],
    misetteiFlg: '1',
    otherRead: '', // 他職員参照権限
    refStopFlg: '0',
    syoriFlg: '', // 処理フラグ
    menu1Id: '', // メニュー１ID
    kensuFlg: '0',
    shokuinIdList: [], // 職員IDリスト
  } as Or26257OnewayType,
})

// ⑤ウォッチャー
watch(
  () => props.onewayModelValue,
  (newValue) => {
    Object.assign(localOneway.or33216, newValue)
    localOneway.mo00040KzokuSelect.items = newValue.zokugaraList
  },
  { deep: true, immediate: true }
)

/**************************************************
 * Emit
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル
const or21814 = ref({ uniqueCpId: '' })
const or28389 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' }) // Or26257:有機体:職員検索モーダル

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or28389Const.CP_ID(1)]: or28389.value,
  [Or26257Const.CP_ID(1)]: or26257.value,
})

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 確認ダイアログの表示状態を返すComputed
 */
const showDialogOr21814 = computed(() => {
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 親族・関係者選択モーダルの表示状態を返すComputed
 */
const showDialogOr28389 = computed(() => {
  return Or28389Logic.state.get(or28389.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 職員検索モーダルの表示状態を返すComputed
 */
const showDialogOr26257 = computed(() => {
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})
// 家族知人等の状況buttonの表示状態を返すComputed
const showKazokuZokuBtn = computed(() => {
  const hyoujiFlg = props.onewayModelValue.hyoujiFlgMap?.find((hyoujiFlg) => {
    return hyoujiFlg.mapKey === Or33216Const.FLAG_KAZOKU
  })
  if (hyoujiFlg && hyoujiFlg.mapValue === Or33216Const.FLAG_KAZOKU_ENABLE) {
    return true
  } else {
    return false
  }
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 福祉事務所区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_WELFARE_OFFICE },
    // 住居の状況区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_RESIDENCE_STATUS },
    // 帰来先の有無区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_RETURN_STATUS },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 福祉事務所区分取得
  local.commonCode.selectCdKbn442List =
    CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_WELFARE_OFFICE) ?? []
  localOneway.mo00039WelfareOfficeRadio.items?.splice(0)
  local.commonCode.selectCdKbn442List.forEach((item) => {
    localOneway.mo00039WelfareOfficeRadio.items?.push({
      label: item.label,
      value: item.value as unknown as number,
    })
  })

  // 住居の状況区分
  local.commonCode.selectCdKbn443List =
    CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_RESIDENCE_STATUS) ?? []
  localOneway.mo00039ResidenceStatusRadio.items?.splice(0)
  local.commonCode.selectCdKbn443List.forEach((item) => {
    localOneway.mo00039ResidenceStatusRadio.items?.push({
      label: item.label,
      value: item.value as unknown as number,
    })
  })

  // 帰来先の有無区分
  local.commonCode.selectCdKbn444List =
    CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_RETURN_STATUS) ?? []
  localOneway.mo00039ExistenceOfReturnRadio.items?.splice(0)
  local.commonCode.selectCdKbn444List.reverse().forEach((item) => {
    localOneway.mo00039ExistenceOfReturnRadio.items?.push({
      label: item.label,
      value: item.value as unknown as number,
    })
  })
}

/**
 * 前住所ボタンクリック
 */
const zenAddressBtnClick = () => {
  // 確認ダイアログのpiniaを設定
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11427'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'destroy1',
      // 第2ボタンボタンラベル
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 前住所確認ダイアログ閉じる後の処理
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.firstBtnClickFlg === true) {
      const inputData: FaceSheetPackage1PreviousAddressSelectInEntity = {
        defSvJigyoId: systemCommonsStore.getSvJigyoId ?? '',
        userid: systemCommonsStore.getUserSelectSelfId() ?? '',
        appYmd: systemCommonsStore.getSystemDate ?? '',
      }

      const resData: FaceSheetPackage1PreviousAddressSelectOutEntity =
        await ScreenRepository.select('FaceSheetPackage1PreviousAddressSelect', inputData)

      refValue.value!.zenAddressKnj.value = resData.data.userAddress ?? ''
    }
  }
)

/**
 * 担当CW氏名アイコンボタンクリック
 */
const tantoBtnClick = () => {
  const svJigyoIdList: { svJigyoId: string }[] = []
  systemCommonsStore.getSvJigyoIdList.forEach((item) => {
    svJigyoIdList.push({ svJigyoId: item })
  })
  localOneway.or26257.svJigyoIdList = svJigyoIdList
  localOneway.or26257.kijunYmd = systemCommonsStore.getSystemDate ?? ''
  localOneway.or26257.defSvJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.or26257.shokuinIdList = refValue.value!.tantoCwId
    ? [Number(refValue.value!.tantoCwId)]
    : []

  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 職員検索モーダル
 */
watch(
  () => local.or26257,
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    refValue.value!.tantoCwId = newValue.shokuin.chkShokuId
    refValue.value!.tantoCw.value = `${newValue.shokuin.shokuin1Knj ?? ''} ${newValue.shokuin.shokuin2Knj ?? ''}`
  }
)

/**
 *  措置の実施機関入力支援アイコンボタンクリック
 */
const kikanNameBtnClick = () => {
  // GUI00937 共通入力支援画面（措置の実施機関）をポップアップで起動する
  localOneway.or51775.title = t('label.agency-for-measures')
  localOneway.or51775.t2Cd = '1'
  localOneway.or51775.columnName = 'kikan_name_knj'
  localOneway.or51775.inputContents = refValue.value!.kikanNameKnj.value ?? ''
  localOneway.or51775.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  local.or51775.modelValue = refValue.value!.kikanNameKnj.value ?? ''

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  入所措置の端緒入力支援アイコンボタンクリック
 */
const nyushoSochiMo00009BtnClick = () => {
  // GUI00937 共通入力支援画面（入所措置の端緒）をポップアップで起動する。
  localOneway.or51775.title = t('label.beginning-of-admission')
  localOneway.or51775.t2Cd = '2'
  localOneway.or51775.columnName = 'nyusho_sochi_knj'
  localOneway.or51775.inputContents = refValue.value!.nyushoSochiKnj.value ?? ''
  localOneway.or51775.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  local.or51775.modelValue = refValue.value!.nyushoSochiKnj?.value ?? ''

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  生活歴入力支援アイコンボタンクリック
 */
const seikaturekiMo00009BtnClick = () => {
  // GUI00937 共通入力支援画面（生活歴）をポップアップで起動する。
  localOneway.or51775.title = t('label.life-history')
  localOneway.or51775.t2Cd = '3'
  localOneway.or51775.columnName = 'seikatureki_knj'
  localOneway.or51775.inputContents = refValue.value!.seikaturekiKnj.value ?? ''
  localOneway.or51775.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  local.or51775.modelValue = refValue.value!.seikaturekiKnj?.value ?? ''

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 住居の状況その他アイコンボタンクリック
 */
const jukyoSonotaMo00009BtnClick = () => {
  // GUI00937 共通入力支援画面（住居の状況）をポップアップで起動する。
  localOneway.or51775.title = t('label.residence-status-other')
  localOneway.or51775.t2Cd = '4'
  localOneway.or51775.columnName = 'jukyo_sonota_knj'
  localOneway.or51775.inputContents = refValue.value!.jukyoSonotaKnj.value ?? ''
  localOneway.or51775.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  local.or51775.modelValue = refValue.value!.jukyoSonotaKnj?.value ?? ''

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 帰来先の有無入力支援アイコンボタンクリック
 */
const kiraisakiAriMo00009BtnClick = () => {
  // GUI00937 共通入力支援画面（帰来先の有無）をポップアップで起動する。
  localOneway.or51775.title = t('label.existence-of-return')
  localOneway.or51775.t2Cd = '5'
  localOneway.or51775.columnName = 'kiraisaki_ari_knj'
  localOneway.or51775.inputContents = refValue.value!.kiraisakiAriKnj.value ?? ''
  localOneway.or51775.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  local.or51775.modelValue = refValue.value!.kiraisakiAriKnj?.value ?? ''

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or51773Const.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }

  switch (localOneway.or51775.title) {
    case t('label.agency-for-measures'):
      // 措置の実施機関
      refValue.value!.kikanNameKnj.value = setOrAppendValue(
        refValue.value!.kikanNameKnj.value ?? '',
        data
      )
      break
    case t('label.beginning-of-admission'):
      // 入所処置の端緒
      refValue.value!.nyushoSochiKnj.value = setOrAppendValue(
        refValue.value!.nyushoSochiKnj.value ?? '',
        data
      )
      break
    case t('label.life-history'):
      // 生活歴
      refValue.value!.seikaturekiKnj.value = setOrAppendValue(
        refValue.value!.seikaturekiKnj.value ?? '',
        data
      )
      break
    case t('label.residence-status-other'):
      // 住居の状況その他
      refValue.value!.jukyoSonotaKnj.value = setOrAppendValue(
        refValue.value!.jukyoSonotaKnj.value ?? '',
        data
      )
      break
    case t('label.existence-of-return'):
      // 退所する場合の帰来先の有無
      refValue.value!.kiraisakiAriKnj.value = setOrAppendValue(
        refValue.value!.kiraisakiAriKnj.value ?? '',
        data
      )
      break
    default:
      break
  }

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * 家族知人等の状況アイコンボタンクリック
 */
const kazokuZokuBtnClick = () => {
  localOneway.or28389.userId = systemCommonsStore.getUserSelectSelfId() ?? ''

  Or28389Logic.state.set({
    uniqueCpId: or28389.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 親族・関係者選択ポップアップ
 */
watch(
  () => local.or28389,
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    interface SelectItem {
      title: string
      value: string
    }

    let index = 0
    newValue.kankeishaList?.forEach((item) => {
      const selectItems: SelectItem[] | undefined = localOneway.mo00040KzokuSelect.items?.filter(
        (selectItem) => (selectItem as SelectItem).title === item.zokugara
      )

      switch (index) {
        case 0: {
          // 家族氏名1
          refValue.value!.kazoku1Knj = { value: item.nameknj }
          // 身元引受人フラグ1
          refValue.value!.mimoto1F = {
            modelValue:
              selectItems !== undefined && selectItems.length > 0
                ? Or33216Const.MIMOTO_FLAG_TRUE
                : Or33216Const.MIMOTO_FLAG_FALSE,
          }
          // 家族続柄1
          refValue.value!.kazokuZoku1Cd = {
            modelValue: selectItems?.[0]?.value ?? '',
          }
          // 家族住所1
          refValue.value!.kzokuAddr1Knj = {
            value: item.jusho,
          }
          // 家族連絡先1
          refValue.value!.kazokuRenraku1 = {
            value: item.renrakusaki,
          }
          // 家族順位1
          refValue.value!.kazokuYusen1 = {
            mo00045: { value: String(item.yuusenno) },
          }
          break
        }
        case 1: {
          // 家族氏名2
          refValue.value!.kazoku2Knj = { value: item.nameknj }
          // 身元引受人フラグ2
          refValue.value!.mimoto2F = {
            modelValue:
              selectItems !== undefined && selectItems.length > 0
                ? Or33216Const.MIMOTO_FLAG_TRUE
                : Or33216Const.MIMOTO_FLAG_FALSE,
          }
          // 家族続柄2
          refValue.value!.kazokuZoku2Cd = {
            modelValue: selectItems?.[0]?.value ?? '',
          }
          // 家族住所2
          refValue.value!.kzokuAddr2Knj = {
            value: item.jusho,
          }
          // 家族連絡先2
          refValue.value!.kazokuRenraku2 = {
            value: item.renrakusaki,
          }
          // 家族順位2
          refValue.value!.kazokuYusen2 = {
            mo00045: { value: String(item.yuusenno) },
          }
          break
        }
        case 2: {
          // 家族氏名3
          refValue.value!.kazoku3Knj = { value: item.nameknj }
          // 身元引受人フラグ3
          refValue.value!.mimoto3F = {
            modelValue:
              selectItems !== undefined && selectItems.length > 0
                ? Or33216Const.MIMOTO_FLAG_TRUE
                : Or33216Const.MIMOTO_FLAG_FALSE,
          }
          // 家族続柄3
          refValue.value!.kazokuZoku3Cd = {
            modelValue: selectItems?.[0]?.value ?? '',
          }
          // 家族住所3
          refValue.value!.kzokuAddr3Knj = {
            value: item.jusho,
          }
          // 家族連絡先3
          refValue.value!.kazokuRenraku3 = {
            value: item.renrakusaki,
          }
          // 家族順位3
          refValue.value!.kazokuYusen3 = {
            mo00045: { value: String(item.yuusenno) },
          }
          break
        }
        case 3: {
          // 家族氏名4
          refValue.value!.kazoku4Knj = { value: item.nameknj }
          // 身元引受人フラグ4
          refValue.value!.mimoto4F = {
            modelValue:
              selectItems !== undefined && selectItems.length > 0
                ? Or33216Const.MIMOTO_FLAG_TRUE
                : Or33216Const.MIMOTO_FLAG_FALSE,
          }
          // 家族続柄4
          refValue.value!.kazokuZoku4Cd = {
            modelValue: selectItems?.[0]?.value ?? '',
          }
          // 家族住所4
          refValue.value!.kzokuAddr4Knj = {
            value: item.jusho,
          }
          // 家族連絡先4
          refValue.value!.kazokuRenraku4 = {
            value: item.renrakusaki,
          }
          // 家族順位4
          refValue.value!.kazokuYusen4 = {
            mo00045: { value: String(item.yuusenno) },
          }
          break
        }
        case 4: {
          // 家族氏名5
          refValue.value!.kazoku5Knj = { value: item.nameknj }
          // 身元引受人フラグ5
          refValue.value!.mimoto5F = {
            modelValue:
              selectItems !== undefined && selectItems.length > 0
                ? Or33216Const.MIMOTO_FLAG_TRUE
                : Or33216Const.MIMOTO_FLAG_FALSE,
          }
          // 家族続柄5
          refValue.value!.kazokuZoku5Cd = {
            modelValue: selectItems?.[0]?.value ?? '',
          }
          // 家族住所5
          refValue.value!.kzokuAddr5Knj = {
            value: item.jusho,
          }
          // 家族連絡先5
          refValue.value!.kazokuRenraku5 = {
            value: item.renrakusaki,
          }
          // 家族順位5
          refValue.value!.kazokuYusen5 = {
            mo00045: { value: String(item.yuusenno) },
          }
          break
        }
        case 5: {
          // 家族氏名6
          refValue.value!.kazoku6Knj = { value: item.nameknj }
          // 身元引受人フラグ6
          refValue.value!.mimoto6F = {
            modelValue:
              selectItems !== undefined && selectItems.length > 0
                ? Or33216Const.MIMOTO_FLAG_TRUE
                : Or33216Const.MIMOTO_FLAG_FALSE,
          }
          // 家族続柄6
          refValue.value!.kazokuZoku6Cd = {
            modelValue: selectItems?.[0]?.value ?? '',
          }
          // 家族住所6
          refValue.value!.kzokuAddr6Knj = {
            value: item.jusho,
          }
          // 家族連絡先6
          refValue.value!.kazokuRenraku6 = {
            value: item.renrakusaki,
          }
          // 家族順位6
          refValue.value!.kazokuYusen6 = {
            mo00045: { value: String(item.yuusenno) },
          }
          break
        }
        case 6: {
          // 家族氏名7
          refValue.value!.kazoku7Knj = { value: item.nameknj }
          // 身元引受人フラグ7
          refValue.value!.mimoto7F = {
            modelValue:
              selectItems !== undefined && selectItems.length > 0
                ? Or33216Const.MIMOTO_FLAG_TRUE
                : Or33216Const.MIMOTO_FLAG_FALSE,
          }
          // 家族続柄7
          refValue.value!.kazokuZoku7Cd = {
            modelValue: selectItems?.[0]?.value ?? '',
          }
          // 家族住所7
          refValue.value!.kzokuAddr7Knj = {
            value: item.jusho,
          }
          // 家族連絡先7
          refValue.value!.kazokuRenraku7 = {
            value: item.renrakusaki,
          }
          // 家族順位7
          refValue.value!.kazokuYusen7 = {
            mo00045: { value: String(item.yuusenno) },
          }
          break
        }
        case 7: {
          // 家族氏名8
          refValue.value!.kazoku8Knj = { value: item.nameknj }
          // 身元引受人フラグ8
          refValue.value!.mimoto8F = {
            modelValue:
              selectItems !== undefined && selectItems.length > 0
                ? Or33216Const.MIMOTO_FLAG_TRUE
                : Or33216Const.MIMOTO_FLAG_FALSE,
          }
          // 家族続柄8
          refValue.value!.kazokuZoku8Cd = {
            modelValue: selectItems?.[0]?.value ?? '',
          }
          // 家族住所8
          refValue.value!.kzokuAddr8Knj = {
            value: item.jusho,
          }
          // 家族連絡先8
          refValue.value!.kazokuRenraku8 = {
            value: item.renrakusaki,
          }
          // 家族順位8
          refValue.value!.kazokuYusen8 = {
            mo00045: { value: String(item.yuusenno) },
          }
          break
        }
        default:
          break
      }
      index++
    })
  },
  { deep: true }
)

onBeforeMount(async () => {
  // 汎用コード取得
  await initCodes()
})
</script>

<template>
  <c-v-container
    fluid
    class="container pa-0 elevation-1"
  >
    <c-v-row class="pa-4 ga-2 px-12 pt-6">
      <c-v-col
        cols="auto"
        class="pa-0"
      >
        <!-- 前住所 -->
        <g-custom-or-x0157
          v-model="refValue!.zenAddressKnj"
          :oneway-model-value="localOneway.orX0157ZenAddress"
          @on-click-edit-btn="zenAddressBtnClick"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row class="ga-8 px-12 pt-0 pb-6">
      <c-v-col
        cols="auto"
        class="pa-0"
      >
        <!-- 入所年月日 -->
        <base-mo00020
          v-model="refValue!.nyushoYmd"
          :oneway-model-value="localOneway.mo00020NyushoYmdInput"
          class="custom-input"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="pa-0"
      >
        <!-- 措置の実施機関 -->
        <g-custom-or-x0157
          v-model="refValue!.kikanNameKnj"
          :oneway-model-value="localOneway.orX0157AgencyForMeasures"
          @on-click-edit-btn="kikanNameBtnClick"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="pa-0"
      >
        <!-- 福祉事務所 -->
        <base-mo00039
          v-model="refValue!.kikanKukuKbn"
          :oneway-model-value="localOneway.mo00039WelfareOfficeRadio"
          class="custom-radio-group"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="pa-0"
      >
        <!-- 担当CW氏名 -->
        <g-custom-or-x0157
          v-model="refValue!.tantoCw"
          :oneway-model-value="localOneway.orX0157ChargeCwName"
          @on-click-edit-btn="tantoBtnClick"
        />
      </c-v-col>
    </c-v-row>
    <!-- 家族知人等の状況セクション -->
    <c-v-row class="custom-section">
      <c-v-col class="pa-0 px-6">
        <!-- 家族知人等の状況リンク -->
        <base-mo01266
          :oneway-model-value="{
            ...localOneway.mo01266StatusOfFamilyAcquaintances,
            disabled: !showKazokuZokuBtn,
          }"
          class="custom-link-btn pa-0"
          @click="kazokuZokuBtnClick"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row>
      <c-v-col
        cols="12"
        class="pa-4 px-12 pt-6"
      >
        <!-- 身元引受人は続柄に☆ -->
        <base-mo00615
          :oneway-model-value="localOneway.mo00615guarantorRelationshipMark"
          class="label-text-grey"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row>
      <!-- 家族知人等の状況 ヘッダー -->
      <!-- 氏名 -->
      <c-v-col
        cols="auto"
        class="w-name custom-sub-section border border-s-0 pa-0 px-3 d-flex align-center"
      >
        <base-mo00615 :oneway-model-value="localOneway.mo00615Name" />
      </c-v-col>
      <!-- 続柄 -->
      <c-v-col
        cols="auto"
        class="w-zoku custom-sub-section border border-s-0 pa-0 px-3 d-flex align-center"
      >
        <base-mo00615 :oneway-model-value="localOneway.mo00615Relationship" />
      </c-v-col>
      <!-- 住所 -->
      <c-v-col
        cols="auto"
        class="w-addr custom-sub-section border border-s-0 pa-0 px-3 d-flex align-center"
      >
        <base-mo00615 :oneway-model-value="localOneway.mo00615Address" />
      </c-v-col>
      <!-- 連絡先 -->
      <c-v-col
        cols="auto"
        class="w-tel custom-sub-section border border-s-0 pa-0 px-3 d-flex align-center"
      >
        <base-mo00615 :oneway-model-value="localOneway.mo00615ContactAddressTel" />
      </c-v-col>
      <!-- 順位 -->
      <c-v-col
        cols="auto"
        class="w-order custom-sub-section border border-s-0 border-e-0 pa-0 px-3 d-flex align-center"
      >
        <base-mo00615 :oneway-model-value="localOneway.mo00615Order" />
      </c-v-col>
      <!-- 家族知人1 -->
      <c-v-col
        cols="auto"
        class="w-name border border-t-0 border-s-0 pa-0"
      >
        <!-- 氏名 -->
        <base-mo00045
          v-model="refValue!.kazoku1Knj"
          :oneway-model-value="localOneway.mo00045Kazoku1Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-zoku border border-t-0 border-s-0 pa-0 d-flex justify-space-between ga-2"
      >
        <!-- 続柄 -->
        <base-mo00040
          v-model="refValue!.kazokuZoku1Cd"
          :oneway-model-value="localOneway.mo00040KzokuSelect"
          class="input-hide-border"
        />
        <!-- 身元引受人 -->
        <base-mo00040
          v-model="refValue!.mimoto1F"
          :oneway-model-value="localOneway.mo00040Mimoto"
          class="input-hide-border w-100"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-addr border border-t-0 border-s-0 pa-0"
      >
        <!-- 住所 -->
        <base-mo00045
          v-model="refValue!.kzokuAddr1Knj"
          :oneway-model-value="localOneway.mo00045KzokuAddr1Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-tel border border-t-0 border-s-0 pa-0"
      >
        <!-- 連絡先 -->
        <base-mo00045
          v-model="refValue!.kazokuRenraku1"
          :oneway-model-value="localOneway.mo00045KazokuRenraku1Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-order border border-t-0 border-s-0 border-e-0 pa-0"
      >
        <!-- 順位 -->
        <base-mo00038
          v-model="refValue!.kazokuYusen1"
          :oneway-model-value="localOneway.mo00038KazokuYusenInput"
          class="input-hide-border"
        />
      </c-v-col>
      <!-- 家族知人2 -->
      <c-v-col
        cols="auto"
        class="w-name border border-t-0 border-s-0 pa-0"
      >
        <!-- 氏名 -->
        <base-mo00045
          v-model="refValue!.kazoku2Knj"
          :oneway-model-value="localOneway.mo00045Kazoku2Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-zoku border border-t-0 border-s-0 pa-0 d-flex justify-space-between ga-2"
      >
        <!-- 続柄 -->
        <base-mo00040
          v-model="refValue!.kazokuZoku2Cd"
          :oneway-model-value="localOneway.mo00040KzokuSelect"
          class="input-hide-border"
        />
        <!-- 身元引受人 -->
        <base-mo00040
          v-model="refValue!.mimoto2F"
          :oneway-model-value="localOneway.mo00040Mimoto"
          class="input-hide-border w-100"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-addr border border-t-0 border-s-0 pa-0"
      >
        <!-- 住所 -->
        <base-mo00045
          v-model="refValue!.kzokuAddr2Knj"
          :oneway-model-value="localOneway.mo00045KzokuAddr2Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-tel border border-t-0 border-s-0 pa-0"
      >
        <!-- 連絡先 -->
        <base-mo00045
          v-model="refValue!.kazokuRenraku2"
          :oneway-model-value="localOneway.mo00045KazokuRenraku2Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-order border border-t-0 border-s-0 border-e-0 pa-0"
      >
        <!-- 順位 -->
        <base-mo00038
          v-model="refValue!.kazokuYusen2"
          :oneway-model-value="localOneway.mo00038KazokuYusenInput"
          class="input-hide-border"
        />
      </c-v-col>
      <!-- 家族知人3 -->
      <c-v-col
        cols="auto"
        class="w-name border border-t-0 border-s-0 pa-0"
      >
        <!-- 氏名 -->
        <base-mo00045
          v-model="refValue!.kazoku3Knj"
          :oneway-model-value="localOneway.mo00045Kazoku3Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-zoku border border-t-0 border-s-0 pa-0 d-flex justify-space-between ga-2"
      >
        <!-- 続柄 -->
        <base-mo00040
          v-model="refValue!.kazokuZoku3Cd"
          :oneway-model-value="localOneway.mo00040KzokuSelect"
          class="input-hide-border"
        />
        <!-- 身元引受人 -->
        <base-mo00040
          v-model="refValue!.mimoto3F"
          :oneway-model-value="localOneway.mo00040Mimoto"
          class="input-hide-border w-100"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-addr border border-t-0 border-s-0 pa-0"
      >
        <!-- 住所 -->
        <base-mo00045
          v-model="refValue!.kzokuAddr3Knj"
          :oneway-model-value="localOneway.mo00045KzokuAddr3Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-tel border border-t-0 border-s-0 pa-0"
      >
        <!-- 連絡先 -->
        <base-mo00045
          v-model="refValue!.kazokuRenraku3"
          :oneway-model-value="localOneway.mo00045KazokuRenraku3Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-order border border-t-0 border-s-0 border-e-0 pa-0"
      >
        <!-- 順位 -->
        <base-mo00038
          v-model="refValue!.kazokuYusen3"
          :oneway-model-value="localOneway.mo00038KazokuYusenInput"
          class="input-hide-border"
        />
      </c-v-col>
      <!-- 家族知人4 -->
      <c-v-col
        cols="auto"
        class="w-name border border-t-0 border-s-0 pa-0"
      >
        <!-- 氏名 -->
        <base-mo00045
          v-model="refValue!.kazoku4Knj"
          :oneway-model-value="localOneway.mo00045Kazoku4Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-zoku border border-t-0 border-s-0 pa-0 d-flex justify-space-between ga-2"
      >
        <!-- 続柄 -->
        <base-mo00040
          v-model="refValue!.kazokuZoku4Cd"
          :oneway-model-value="localOneway.mo00040KzokuSelect"
          class="input-hide-border"
        />
        <!-- 身元引受人 -->
        <base-mo00040
          v-model="refValue!.mimoto4F"
          :oneway-model-value="localOneway.mo00040Mimoto"
          class="input-hide-border w-100"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-addr border border-t-0 border-s-0 pa-0"
      >
        <!-- 住所 -->
        <base-mo00045
          v-model="refValue!.kzokuAddr4Knj"
          :oneway-model-value="localOneway.mo00045KzokuAddr4Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-tel border border-t-0 border-s-0 pa-0"
      >
        <!-- 連絡先 -->
        <base-mo00045
          v-model="refValue!.kazokuRenraku4"
          :oneway-model-value="localOneway.mo00045KazokuRenraku4Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-order border border-t-0 border-s-0 border-e-0 pa-0"
      >
        <!-- 順位 -->
        <base-mo00038
          v-model="refValue!.kazokuYusen4"
          :oneway-model-value="localOneway.mo00038KazokuYusenInput"
          class="input-hide-border"
        />
      </c-v-col>
      <!-- 家族知人5 -->
      <c-v-col
        cols="auto"
        class="w-name border border-t-0 border-s-0 pa-0"
      >
        <!-- 氏名 -->
        <base-mo00045
          v-model="refValue!.kazoku5Knj"
          :oneway-model-value="localOneway.mo00045Kazoku5Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-zoku border border-t-0 border-s-0 pa-0 d-flex justify-space-between ga-2"
      >
        <!-- 続柄 -->
        <base-mo00040
          v-model="refValue!.kazokuZoku5Cd"
          :oneway-model-value="localOneway.mo00040KzokuSelect"
          class="input-hide-border"
        />
        <!-- 身元引受人 -->
        <base-mo00040
          v-model="refValue!.mimoto5F"
          :oneway-model-value="localOneway.mo00040Mimoto"
          class="input-hide-border w-100"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-addr border border-t-0 border-s-0 pa-0"
      >
        <!-- 住所 -->
        <base-mo00045
          v-model="refValue!.kzokuAddr5Knj"
          :oneway-model-value="localOneway.mo00045KzokuAddr5Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-tel border border-t-0 border-s-0 pa-0"
      >
        <!-- 連絡先 -->
        <base-mo00045
          v-model="refValue!.kazokuRenraku5"
          :oneway-model-value="localOneway.mo00045KazokuRenraku5Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-order border border-t-0 border-s-0 border-e-0 pa-0"
      >
        <!-- 順位 -->
        <base-mo00038
          v-model="refValue!.kazokuYusen5"
          :oneway-model-value="localOneway.mo00038KazokuYusenInput"
          class="input-hide-border"
        />
      </c-v-col>
      <!-- 家族知人6 -->
      <c-v-col
        cols="auto"
        class="w-name border border-t-0 border-s-0 pa-0"
      >
        <!-- 氏名 -->
        <base-mo00045
          v-model="refValue!.kazoku6Knj"
          :oneway-model-value="localOneway.mo00045Kazoku6Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-zoku border border-t-0 border-s-0 pa-0 d-flex justify-space-between ga-2"
      >
        <!-- 続柄 -->
        <base-mo00040
          v-model="refValue!.kazokuZoku6Cd"
          :oneway-model-value="localOneway.mo00040KzokuSelect"
          class="input-hide-border"
        />
        <!-- 身元引受人 -->
        <base-mo00040
          v-model="refValue!.mimoto6F"
          :oneway-model-value="localOneway.mo00040Mimoto"
          class="input-hide-border w-100"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-addr border border-t-0 border-s-0 pa-0"
      >
        <!-- 住所 -->
        <base-mo00045
          v-model="refValue!.kzokuAddr6Knj"
          :oneway-model-value="localOneway.mo00045KzokuAddr6Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-tel border border-t-0 border-s-0 pa-0"
      >
        <!-- 連絡先 -->
        <base-mo00045
          v-model="refValue!.kazokuRenraku6"
          :oneway-model-value="localOneway.mo00045KazokuRenraku6Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-order border border-t-0 border-s-0 border-e-0 pa-0"
      >
        <!-- 順位 -->
        <base-mo00038
          v-model="refValue!.kazokuYusen6"
          :oneway-model-value="localOneway.mo00038KazokuYusenInput"
          class="input-hide-border"
        />
      </c-v-col>
      <!-- 家族知人7 -->
      <c-v-col
        cols="auto"
        class="w-name border border-t-0 border-s-0 pa-0"
      >
        <!-- 氏名 -->
        <base-mo00045
          v-model="refValue!.kazoku7Knj"
          :oneway-model-value="localOneway.mo00045Kazoku7Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-zoku border border-t-0 border-s-0 pa-0 d-flex justify-space-between ga-2"
      >
        <!-- 続柄 -->
        <base-mo00040
          v-model="refValue!.kazokuZoku7Cd"
          :oneway-model-value="localOneway.mo00040KzokuSelect"
          class="input-hide-border"
        />
        <!-- 身元引受人 -->
        <base-mo00040
          v-model="refValue!.mimoto7F"
          :oneway-model-value="localOneway.mo00040Mimoto"
          class="input-hide-border w-100"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-addr border border-t-0 border-s-0 pa-0"
      >
        <!-- 住所 -->
        <base-mo00045
          v-model="refValue!.kzokuAddr7Knj"
          :oneway-model-value="localOneway.mo00045KzokuAddr7Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-tel border border-t-0 border-s-0 pa-0"
      >
        <!-- 連絡先 -->
        <base-mo00045
          v-model="refValue!.kazokuRenraku7"
          :oneway-model-value="localOneway.mo00045KazokuRenraku7Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-order border border-t-0 border-s-0 border-e-0 pa-0"
      >
        <!-- 順位 -->
        <base-mo00038
          v-model="refValue!.kazokuYusen7"
          :oneway-model-value="localOneway.mo00038KazokuYusenInput"
          class="input-hide-border"
        />
      </c-v-col>
      <!-- 家族知人8 -->
      <c-v-col
        cols="auto"
        class="w-name border border-t-0 border-s-0 pa-0"
      >
        <!-- 氏名 -->
        <base-mo00045
          v-model="refValue!.kazoku8Knj"
          :oneway-model-value="localOneway.mo00045Kazoku8Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-zoku border border-t-0 border-s-0 pa-0 d-flex justify-space-between ga-2"
      >
        <!-- 続柄 -->
        <base-mo00040
          v-model="refValue!.kazokuZoku8Cd"
          :oneway-model-value="localOneway.mo00040KzokuSelect"
          class="input-hide-border"
        />
        <!-- 身元引受人 -->
        <base-mo00040
          v-model="refValue!.mimoto8F"
          :oneway-model-value="localOneway.mo00040Mimoto"
          class="input-hide-border w-100"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-addr border border-t-0 border-s-0 pa-0"
      >
        <!-- 住所 -->
        <base-mo00045
          v-model="refValue!.kzokuAddr8Knj"
          :oneway-model-value="localOneway.mo00045KzokuAddr8Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-tel border border-t-0 border-s-0 pa-0"
      >
        <!-- 連絡先 -->
        <base-mo00045
          v-model="refValue!.kazokuRenraku8"
          :oneway-model-value="localOneway.mo00045KazokuRenraku8Input"
          class="input-hide-border"
        />
      </c-v-col>
      <c-v-col
        cols="auto"
        class="w-order border border-t-0 border-s-0 border-e-0 pa-0"
      >
        <!-- 順位 -->
        <base-mo00038
          v-model="refValue!.kazokuYusen8"
          :oneway-model-value="localOneway.mo00038KazokuYusenInput"
          class="input-hide-border"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row class="pt-6" />
    <!-- 家族構成図セクション -->
    <c-v-row class="custom-section">
      <c-v-col class="pa-0 px-6">
        <!-- 家族構成図リンク -->
        <base-mo01266
          :oneway-model-value="localOneway.mo01266FamilyStructureDiagramOther"
          class="custom-link-btn pa-0"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row>
      <!-- '家族構成内容 -->
      <!-- 課題 #107836、#123175 -->
      <!-- 図を作成する機能が、中期の開発対象外と確認取れました -->
      <c-v-col
        cols="12"
        class="pa-0 px-12 pt-6"
      >
        <c-v-row class="graph-template" />
      </c-v-col>
      <c-v-col
        cols="12"
        class="pa-0 px-12 pt-4 pb-6"
      >
        <!-- 入所者が頼りにしている者 -->
        <base-mo00615
          :oneway-model-value="localOneway.mo00615ToThoseWhoAreDependentOn"
          class="label-text-grey"
        />
      </c-v-col>
    </c-v-row>
    <!-- 入所措置の端緒セクション -->
    <c-v-row class="custom-section-black">
      <base-mo01341
        :oneway-model-value="{
          anchorPoint: 'beginning-of-admission',
          title: t('label.beginning-of-admission'),
          color: 'rgb(var(--v-theme-black-100))',
        }"
      />
    </c-v-row>
    <c-v-row>
      <c-v-col
        cols="12"
        class="px-12 py-6"
      >
        <!-- 入所措置の端緒 -->
        <g-custom-or-x0156
          v-model="refValue!.nyushoSochiKnj"
          :oneway-model-value="localOneway.orX0156NyushoSochiMatter"
          @on-click-edit-btn="nyushoSochiMo00009BtnClick"
        />
      </c-v-col>
    </c-v-row>
    <!-- 生活歴セクション -->
    <c-v-row class="custom-section-black">
      <base-mo01341
        :oneway-model-value="{
          anchorPoint: 'life-history',
          title: t('label.life-history'),
          color: 'rgb(var(--v-theme-black-100))',
        }"
      />
    </c-v-row>
    <c-v-col
      cols="12"
      class="pa-0 px-12 pt-6"
    >
      <!--出生から成人期、成人期から壮年期、壮年期以降等ライフステージごとの視点で記載すること-->
      <base-mo00615
        :oneway-model-value="localOneway.mo00615DescriptionComment"
        class="label-text-grey"
      />
    </c-v-col>
    <c-v-row>
      <c-v-col
        cols="12"
        class="pa-2 px-12 pb-6"
      >
        <!-- 生活歴 -->
        <g-custom-or-x0156
          v-model="refValue!.seikaturekiKnj"
          :oneway-model-value="localOneway.orX0156SeikaturekiMatter"
          @on-click-edit-btn="seikaturekiMo00009BtnClick"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row class="divider" />
    <!-- 住居の状況セクション -->
    <c-v-row>
      <c-v-col
        cols="12"
        class="pa-1 px-12 pt-6"
      >
        <!-- 住居の状況タイトル -->
        <base-mo00615 :oneway-model-value="localOneway.mo00615ResidenceStatus" />
      </c-v-col>
      <c-v-col
        cols="12"
        class="pa-0 px-12 d-flex ga-4"
      >
        <!-- 住居の状況 -->
        <base-mo00039
          v-model="refValue!.jukyoKbn"
          :oneway-model-value="localOneway.mo00039ResidenceStatusRadio"
          class="custom-radio-group"
        />
        <g-custom-or-x0157
          v-model="refValue!.jukyoSonotaKnj"
          :oneway-model-value="localOneway.orX0157ResidenceStatus"
          @on-click-edit-btn="jukyoSonotaMo00009BtnClick"
        />
      </c-v-col>
    </c-v-row>
    <!-- 退所する場合の帰来先の有無セクション -->
    <c-v-row>
      <c-v-col
        cols="12"
        class="pa-1 px-12 pt-6"
      >
        <!-- 退所する場合の帰来先の有無タイトル -->
        <base-mo00615 :oneway-model-value="localOneway.mo00615ExistenceOfReturn" />
      </c-v-col>
      <c-v-col
        cols="12"
        class="pa-0 px-12 d-flex ga-4"
      >
        <!-- 帰来先有 -->
        <base-mo00039
          v-model="refValue!.kiraisakiUmu"
          :oneway-model-value="localOneway.mo00039ExistenceOfReturnRadio"
          class="custom-radio-group"
        />
        <g-custom-or-x0157
          v-model="refValue!.kiraisakiAriKnj"
          :oneway-model-value="localOneway.orX0157ExistenceOfReturn"
          @on-click-edit-btn="kiraisakiAriMo00009BtnClick"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row class="dummy-area" />
  </c-v-container>

  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="local.or51775"
    :oneway-model-value="localOneway.or51775"
    @confirm="handleOr51775Confirm"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or28389:(GUI00642共通)親族・関係者選択モーダル -->
  <g-custom-or28389
    v-if="showDialogOr28389"
    v-bind="or28389"
    v-model="local.or28389"
    :oneway-model-value="localOneway.or28389"
  />
  <!-- Or26257:有機体:職員検索モーダル -->
  <g-custom-or26257
    v-if="showDialogOr26257"
    v-bind="or26257"
    v-model="local.or26257"
    :oneway-model-value="localOneway.or26257"
  />
</template>

<style lang="scss" scoped>
.container {
  min-width: 1080px;
  max-width: 1080px;
  margin: unset;
  overflow-x: auto;

  background-color: rgb(var(--v-theme-secondaryBackground));

  :deep(.v-row) {
    margin: 0px;
  }

  :deep(.custom-edit-btn) {
    background-color: #ebf2fd !important;
  }

  .custom-section {
    height: 48px;
    border: 0.5px solid #0000001a;
    background-color: rgba(var(--v-theme-blue-700), 0.08);

    display: flex;
    align-items: center;
  }

  .custom-section-black {
    height: 48px;
    border: 1px solid #dedede;
    background-color: rgb(var(--v-theme-black-100));

    display: flex;
    align-items: center;

    :deep(.section-border-all-sides) {
      border: none;
    }

    :deep(.v-toolbar .v-toolbar-title) {
      margin: 0 !important;
      padding: 0px 24px !important;
      font-size: 16px;
    }
  }

  .custom-sub-section {
    min-height: 32px;
    background-color: rgb(var(--v-theme-blue-100));

    :deep(.item-label) {
      font-size: 13px;
    }
  }

  .custom-link-btn {
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: -0.64px;
    font-weight: 700;
  }

  .custom-radio-group {
    :deep(.v-selection-control-group.v-selection-control-group--inline) {
      gap: 16px;
    }
    :deep(.v-selection-control) {
      margin-right: 0px !important;
      padding-right: 8px;
    }
    :deep(.v-selection-control.v-selection-control--dirty) {
      background-color: rgba(var(--v-theme-blue-700), 0.16);
      border-radius: 100px;
    }
  }

  .custom-input {
    :deep(.v-field__outline__start),
    :deep(.v-field__outline__end) {
      border-color: #cccccc;
      opacity: 1;
    }

    :deep(.v-input--focused .v-field__outline__start),
    :deep(.v-input--focused .v-field__outline__end) {
      border-color: rgb(var(--v-theme-key));
    }
  }

  .graph-template {
    height: 371px;
    border-radius: 10px;
    background-color: #f0f0f0;
  }

  .dummy-area {
    height: 260px;
  }
}

.divider {
  height: 1px;
  background-color: #00000029;
}

.input-hide-border {
  :deep(.v-field:not(.v-field--focused) .v-field__outline) {
    display: none;
  }
  :deep(input) {
    padding: 8px;
  }
  :deep(.v-field__input) {
    padding-inline-start: 12px;
  }
}

.label-text-grey {
  :deep(.item-label) {
    color: rgb(var(--v-theme-subText));
  }
}

.w-name {
  width: 20%;
}
.w-zoku {
  width: 23%;
}
.w-addr {
  width: 32%;
}
.w-tel {
  width: 15%;
}
.w-order {
  width: 10%;
}
</style>
