<script setup lang="ts">
/**
 * Or26979：有機体：担当地域包括支援センター
 * GUI01090_基本的な事項
 *
 * @description
 * Or26979：有機体：担当地域包括支援センター
 *
 * <AUTHOR> 朱征宇
 */
import { reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26979Const } from './Or26979.constants'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { useCommonProps, useScreenTwoWayBind } from '#imports'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo01298OnewayType } from '~/types/business/components/Mo01298Type'
import type { Or26979Type } from '~/types/cmn/business/components/Or26979Type'
import { useValidation } from '@/utils/useValidation'

const { t } = useI18n()
const { byteLength } = useValidation()
/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())

const localOneway = reactive({
  // 担当地域セクション
  title1: {
    anchorPoint: '',
    color: 'rgb(var(--v-theme-background))',
    title: t('label.charge-regional-comprehensive-support-center'),
  } as Mo01298OnewayType,
  // 担当地域包括支援センター
  chiJigyoKnjOneway: {
    maxlength: '85',
    width: '776px',
    showItemLabel: false,
    isVerticalLabel: false,
    rules: [byteLength(84)],
  } as Mo00045OnewayType,
  // 計画作成者
  centerShokuKnjOneway: {
    maxlength: '25',
    width: '200px',
    showItemLabel: false,
    isVerticalLabel: false,
    rules: [byteLength(44)],
  } as Mo00045OnewayType,
  // 計画作成者ラベル
  mo00615OnewayPlanAuthor: {
    itemLabel: t('label.plan-author'),
  } as Mo00615OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or26979Type>({
  cpId: Or26979Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
</script>

<template>
  <c-v-sheet class="view">
    <c-v-row class="operationArea">
      <!-- 担当地域セクション -->
      <base-mo01298 :oneway-model-value="localOneway.title1" />
    </c-v-row>
    <c-v-row class="mt-2">
      <!-- 担当地域包括支援センター -->
      <base-mo00045
        v-model="refValue!.chiJigyoKnj"
        :oneway-model-value="localOneway.chiJigyoKnjOneway"
      />
    </c-v-row>
    <c-v-row
      no-gutters
      class="mt-2"
    >
      <c-v-col
        cols="auto"
        class="display-col"
      >
        <!-- 計画作成者ラベル -->
        <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayPlanAuthor" />
        <!-- 計画作成者 -->
        <base-mo00045
          v-model="refValue!.centerShokuKnj"
          :oneway-model-value="localOneway.centerShokuKnjOneway"
        />
      </c-v-col>
      <c-v-col cols="auto"> </c-v-col>
    </c-v-row>
  </c-v-sheet>
</template>

<style scoped lang="scss">
.view {
  min-width: 520px;
  display: flex;
  margin: 0px;
  padding: 0px;
  flex-direction: column;

  .v-row {
    margin: unset;
  }

  .operationArea {
    background-color: rgb(var(--v-theme-background));
    flex: 0 0 auto;

    .v-col {
      padding-left: 0;
      padding-right: 0;
      padding-top: 0;
      padding-bottom: 8px;
    }

    .v-col:last-child {
      text-align: right;
    }
  }
}
.display-col {
  display: flex;
}
</style>
