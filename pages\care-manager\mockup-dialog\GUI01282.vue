<script setup lang="ts">
/**
 * GUI01282_［履歴選択］画面 認定調査
 *
 * @description
 * ［履歴選択］画面 認定調査
 *
 * <AUTHOR> 宋鹏飞
 */
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import type { Or26323OnewayType,Or26323Type1,Or26323Type2 } from '~/types/cmn/business/components/Or26323Type'
import { Or26323Const } from '~/components/custom-components/organisms/Or26323/Or26323.constants'
import { Or26323Logic } from '~/components/custom-components/organisms/Or26323/Or26323.logic'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * KMD 宋鹏飞 2025/05/09 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI01282'
// ルーティング
const routing = 'GUI01282/pinia'
// 画面物理名
const screenName = 'GUI01282'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or26323 = ref({ uniqueCpId: Or26323Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する

const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01282' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or26323Logic.initialize(pageComponent.uniqueCpId)
}

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
// 子コンポーネントのユニークIDを設定する
or26323.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01282',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26323Const.CP_ID(0) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or26323Const.CP_ID(0)]: or26323.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOr26323 = computed(() => {
  // Or26323のダイアログ開閉状態
  return Or26323Logic.state.get(or26323.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or26323Data: Or26323OnewayType = {
  // 事業者ID
  svJigyoId: '1',
  // 利用者ID
  userId: '1',
  // 計画期間ID
  sc1Id: '1',
  // モード
  mode: '0',
  // 履歴ID
  plan1Id: '3',
}

// const or26323Type1 = ref<Or26323Type1>({
//   cschId: '',
// })

// const Or26323Type2 = ref<Or26323Type2>({
//   rirekiId: '',
//   createYmd: '',
// })


const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 事業者ID
  svJigyoId: { value: '1' } as Mo00045Type,
  // 利用者ID
  userId: { value: '1' } as Mo00045Type,
  // 計画期間ID
  sc1Id: { value: '1' } as Mo00045Type,
  // モード
  mode: { value: '0' } as Mo00045Type,
  // 戻り値
  selectData1: {} as Or26323Type1,
  selectData2: {} as Or26323Type2,
})

/**
 *  ボタン押下時の処理

 * @param option - linkを区別する
 */
function or26323OnClick(option: string) {
  or26323Data.mode = option
  // Or26323のダイアログ開閉状態を更新する
  Or26323Logic.state.set({
    uniqueCpId: or26323.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/** GUI01282 疎通起動  */
function onClickOr26323() {
  or26323Data.svJigyoId = local.svJigyoId.value
  or26323Data.userId = local.userId.value
  or26323Data.sc1Id = local.sc1Id.value
  or26323Data.mode = local.mode.value
  // Or28349のダイアログ開閉状態を更新する
  Or26323Logic.state.set({
    uniqueCpId: or26323.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 履歴選択画面
 *
 * @param data - 履歴選択画面
 */
const or26323Change1 = (data: Or26323Type1) => {

  local.selectData1 = data
}
const or26323Change2 = (data: Or26323Type2) => {

  local.selectData2 = data
}
/**************************************************
 * 履歴選択画面
 * KMD 宋鹏飞 2025/05/12 ADD END
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or26323OnClick('1')"
        >初期表示（引継情報.処理モードが1の場合）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or26323OnClick('0')"
        >初期表示（引継情報.処理モードが0の場合）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-26323
    v-if="showDialogOr26323"
    v-bind="or26323"
    :oneway-model-value="or26323Data"
    @update:model-value1="or26323Change1"
    @update:model-value2="or26323Change2"
  />
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">事業者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.svJigyoId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">計画期間ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.sc1Id"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">モード</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.mode"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr26323"> GUI01282 疎通起動 </v-btn>
  </div>
  <div class="pt-5 w-25 pl-5">
    <div> 戻り値 </div>
    <div> モード=0 {{local.selectData1}} </div>
    <div> モード!=0 {{local.selectData2}} </div>
  </div>
</template>
