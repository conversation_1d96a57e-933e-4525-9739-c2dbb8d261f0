import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 計画転送・実績取込（提供事業所）一覧情報検索処理
 */
/** エンティティ */
export interface PlanTransferAchievementImportListSelectInEntity extends InWebEntity {
  /** 支援事業所ID */
  shienId: string
  /** 処理年月 */
  shoriYm: string
  /** 適用事業所IDの配列 */
  tekiyoJigyoId: string
  /** 担当ケアマネID */
  tantoId: string
}

/**
 * 取得出力エンティティ
 */
export interface PlanTransferAchievementImportListSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /* 事業所リスト */
    jigyoList: [
      {
        /** 事業所選択 */
        jigyoSel: string
        /** 事業所番号 */
        jigyoNumber: string
        /** 事業名 */
        jigyoKnj: string
        /** 略称 */
        ruakuKnj: string
        /** 計画 */
        keikaku: string
        /** 実績 */
        jisseki: string
        /** サービス事業者ID */
        svJigyoId: string
      },
    ]
    /* 利用者リスト */
    userList: [
      {
        /** 利用者選択 */
        userSel: string
        /** 氏名（姓） */
        name1Knj: string
        /** 氏名（名） */
        name2Knj: string
        /** 利用者番号 */
        selfId: string
        /** フリガナ（姓） */
        name1Kana: string
        /** フリガナ（名） */
        name2Kana: string
        /** 利用者ID */
        id: string
      },
    ]
  }
}
