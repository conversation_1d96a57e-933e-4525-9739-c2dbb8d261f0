<script setup lang="ts">
/**
 * Or36245
 * GUI01213_シミュレーション
 *
 * @description
 * シミュレーション（画面コンポーネント）
 *
 * <AUTHOR> 董永強
 */
import { onMounted, reactive, ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { OrX0004Const } from '../OrX0004/OrX0004.constants'
import { OrX0005Const } from '../OrX0005/OrX0005.constants'
import { OrX0014Const } from '../OrX0014/OrX0014.constants'
import { OrX0042Const } from '../OrX0042/OrX0042.constants'
import { Or36245Const } from './Or36245.constants'
import type {
  DayOfWeek,
  UsingSlipDetailInfo,
  UsingSlipOthrtTable,
  TimeModelValue,
} from './Or36245.type'
import { Or21828Const } from '~/components/base-components/organisms/Or21828/Or21828.constants'
import { Or21830Const } from '~/components/base-components/organisms/Or21830/Or21830.constants'
import { useSetupChildProps } from '~/composables/useComponentVue'
import { useScreenStore, useSystemCommonsStore } from '#imports'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00037OnewayType } from '~/types/business/components/Mo00037Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type {
  SimInitInfoSelectInEntity,
  SimInitInfoSelectOutEntity,
  RiyouInfo,
  SumData,
  KbnShikyuuGendoData,
  SyuruiGendoData,
  RiyouBeppyo,
  RiyourshaData,
  Riyou,
} from '~/repositories/cmn/entities/SimInitInfoSelectEntity'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo01278OnewayType } from '~/types/business/components/Mo01278Type'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import type { Or36245OnewayType, Or36245Type } from '~/types/cmn/business/components/Or36245Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or27736Const } from '~/components/custom-components/organisms/Or27736/Or27736.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or27736OnewayType } from '~/types/cmn/business/components/Or27736Type'
import { Or27736Logic } from '~/components/custom-components/organisms/Or27736/Or27736.logic'
import { Or21735Logic } from '~/components/base-components/organisms/Or21735/Or21735.logic'
import { Or21737Logic } from '~/components/base-components/organisms/Or21737/Or21737.logic'
import { Or21738Logic } from '~/components/base-components/organisms/Or21738/Or21738.logic'
import { Or27016Const } from '~/components/custom-components/organisms/Or27016/Or27016.constants'
import { Or27016Logic } from '~/components/custom-components/organisms/Or27016/Or27016.logic'
import type { MeisaiList, Or27016OnewayType } from '~/types/cmn/business/components/Or27016Type'
import { Or35745Const } from '~/components/custom-components/organisms/Or35745/Or35745.constants'
import { Or35745Logic } from '~/components/custom-components/organisms/Or35745/Or35745.logic'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or27852Logic } from '~/components/custom-components/organisms/Or27852/Or27852.logic'
import { Or27852Const } from '~/components/custom-components/organisms/Or27852/Or27852.constants'
import { Or27856Const } from '~/components/custom-components/organisms/Or27856/Or27856.constants'
import { Or27856Logic } from '~/components/custom-components/organisms/Or27856/Or27856.logic'
import type {
  Or27856SelectInfoType,
  Or27856Type,
} from '~/types/cmn/business/components/Or27856Type'
import type { Or27856OutputItem } from '~/components/custom-components/organisms/Or27856/Or27856.type'
import type {
  UseSlipOtherDtlInfoRecalculationSelectInEntity,
  UseSlipOtherDtlInfoRecalculationSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipOtherDtlInfoRecalculationSelectEntity'
import { Or28534Const } from '~/components/custom-components/organisms/Or28534/Or28534.constants'
import { Or28534Logic } from '~/components/custom-components/organisms/Or28534/Or28534.logic'
import type { Or28534Type, Or28534OnewayType } from '~/types/cmn/business/components/Or28534Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type {
  UseSlipInfoNumberOfTimesModifiedBefCheckSelectInEntity,
  UseSlipInfoNumberOfTimesModifiedBefCheckSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoNumberOfTimesModifiedBefCheckSelectEntity'
import type { SimUpdateInEntity } from '~/repositories/cmn/entities/SimUpdateEntity'
import type {
  UseSlipInfoRecalculationBef2SelectInEntity,
  UseSlipInfoRecalculationBef2SelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoRecalculationBef2SelectEntity'
import type {
  SimulationInfoUpdateConfirmSelectInEntity,
  SimulationInfoUpdateConfirmSelectOutEntity,
} from '~/repositories/cmn/entities/SimulationInfoUpdateConfirmSelectEntity'
import type {
  SimNewTreatmentSelectInEntity,
  SimNewTreatmentSelectOutEntity,
} from '~/repositories/cmn/entities/SimNewTreatmentSelectEntity'
import type { SimDeleteInEntity } from '~/repositories/cmn/entities/SimDeleteEntity'
import type {
  UseSlipInfoDuplicateRowAftProcSelectInEntity,
  UseSlipInfoDuplicateRowAftProcSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoDuplicateRowAftProcSelectEntity'
import type {
  UseSlipInfoDeleteRowAftProcSelectInEntity,
  UseSlipInfoDeleteRowAftProcSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoDeleteRowAftProcSelectEntity'
import type {
  UseSlipInfoDeleteBefSelectInEntity,
  UseSlipInfoDeleteBefSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoDeleteBefSelectEntity'
import type {
  UseSlipInfoSortSelectInEntity,
  UseSlipInfoSortSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoSortSelectEntity'
import type {
  UseSlipInfoPredictionLendingChangeRowAftProcSelectInEntity,
  UseSlipInfoPredictionLendingChangeRowAftProcSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoPredictionLendingChangeRowAftProcSelectEntity'
import type {
  UseSlipInfoNumberOfTimesModifiedBefSelectInEntity,
  UseSlipInfoNumberOfTimesModifiedBefSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoNumberOfTimesModifiedBefSelectEntity'
import { Or57151Const } from '~/components/custom-components/organisms/Or57151/Or57151.constants'
import { Or57151Logic } from '~/components/custom-components/organisms/Or57151/Or57151.logic'
import type { Or57151OnewayType } from '~/types/cmn/business/components/Or57151Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type {
  SimReCalculationCheckSelectInEntity,
  SimReCalculationCheckSelectOutEntity,
} from '~/repositories/cmn/entities/SimReCalculationCheckSelectEntity'
import type {
  SimReCalculationSelectInEntity,
  SimReCalculationSelectOutEntity,
} from '~/repositories/cmn/entities/SimReCalculationSelectEntity'
import type { OrX0165OnewayType } from '~/types/cmn/business/components/OrX0165Type'
import type { Mo01343OnewayType, Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import { Gui00048Const } from '~/components/custom-components/organisms/Gui00048/Gui00048.constants'
import { Gui00048Logic } from '~/components/custom-components/organisms/Gui00048/Gui00048.logic'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
/** props */
interface Props {
  modelValue: Or36245Type
  onewayModelValue: Or36245OnewayType
  uniqueCpId: string
}

// 引継情報を取得する
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  mo00043: { id: Or36245Const.TAB_ID_USING_SLIP } as Mo00043Type,
  or36245: {
    ...props.modelValue,
  } as Or36245Type,
  offeryYearMonth: {
    value: '',
  },
  levelOfCare: {
    modelValue: '',
  },
  createDate: {
    value: '',
  },
  categoryPaymentLimitAmount: {
    mo00045: {
      value: '',
    },
  },
  title: {
    value: '',
  },
  paymentRate: {
    mo00045: {
      value: '',
    },
  },
  selectedUsingSlipItemIndex: -1,
  selectedColumnIndex: -1,
  insuredIdBgColor: 'transparent',
  usingSlipInfo: {
    riyourshaData: [
      {
        rCompBase: '',
      },
    ] as RiyourshaData[],
    kbnShikyuuGendoData: [
      {
        dmyKbnGendo: '0',
        tKGendo: '',
      },
    ] as KbnShikyuuGendoData[],
    syuruiGendoData: [{}] as SyuruiGendoData[],
    sumData: [] as SumData[],
  } as RiyouInfo,
  usingSlipList: [] as UsingSlipDetailInfo[],
  selectedUsingSlipOtherTableItemIndex: -1,
  usingSlipOthrtTableList: [] as UsingSlipOthrtTable[],
  or27736: {
    periodStartDate: { value: '' },
    periodEndDate: { value: '' },
  },
  or27016: {
    // モード
    provideYm: '',
    // 利用票リスト
    meisaiList: [] as MeisaiList[],
  },
  or27856: {
    outputItem: {} as Or27856OutputItem,
  } as Or27856Type,
  or28534: {
    yymmYm: '',
    userid: '',
  } as Or28534Type,
  or27852: { processFlg: '' },
  mo01343: {
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
    value: '',
  } as Mo01343Type,
})

const localOneway = reactive({
  or36245: {
    ...props.onewayModelValue,
  },
  // 新規
  mo00611AddNewOneway: {
    name: 'copyBtn',
    btnLabel: t('btn.add-new'),
    labelColor: 'rgb(var(--v-theme-key))',
    width: '70px',
    class: 'btn-add-new',
    tooltipText: t('tooltip.care-plan2-new-btn'),
  } as Mo00611OnewayType,
  // 再計算
  mo00611RecomputeOneway: {
    name: 'recomputeBtn',
    btnLabel: t('btn.recompute'),
    width: '70px',
    class: 'btn-recompute',
    tooltipText: t('tooltip.recompute'),
  } as Mo00611OnewayType,
  mo00043Oneway: {
    tabItems: [
      {
        id: 'usingSlip',
        title: t('label.using-slip'),
        tooltipText: t('label.using-slip'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'usingSlipOtherTable',
        title: t('label.using-slip-other-table'),
        tooltipText: t('label.using-slip-other-table'),
        tooltipLocation: 'bottom',
      },
    ],
  } as Mo00043OnewayType,
  // 提供年月
  orX0165Plan: {
    label: t('label.offer-ym'),
    isRequired: true,
    pageLabelFontWeight: 'normal',
  } as OrX0165OnewayType,
  mo01338Oneway: {
    value: t('label.offer-ym'),
  } as Mo01338OnewayType,
  mo00037Oneway: {} as Mo00037OnewayType,
  backmo00009Oneway: {
    variant: 'text',
    color: 'black-800',
    btnIcon: 'chevron_left',
  } as Mo00009OnewayType,
  forwardmo00009Oneway: {
    variant: 'text',
    color: 'black-800',
    btnIcon: 'chevron_right',
  } as Mo00009OnewayType,
  mo00020Oneway: {
    showItemLabel: false,
    customClass: new CustomClass({ outerClass: 'offery-ym' }),
  } as Mo00020OnewayType,
  // 作成日
  mo00020YmdOneway: {
    itemLabel: t('label.create-date'),
    showItemLabel: true,
    isVerticalLabel: true,
    isRequired: true,
    hideDetails: true,
    disabled: false,
    width: '125',
  } as Mo00020OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  mo01338InsurOneway: {
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-2 align-center mt-1',
    }),
  } as Mo01338OnewayType,
  // 介護度
  mo00040LevelOfCareOneway: {
    showItemLabel: true,
    itemLabel: t('label.level-of-care'),
    isRequired: true,
    hideDetails: true,
    width: '140px',
    customClass: new CustomClass({
      outerClass: 'mr-0',
    }),
    items: [
      { label: '', value: '' },
      { label: '非該当', value: '1' },
      { label: '事業対象者', value: '2' },
      { label: '要支援１', value: '3' },
      { label: '要支援２', value: '4' },
      { label: '経過的要介護', value: '5' },
      { label: '要介護１', value: '6' },
      { label: '要介護２', value: '7' },
      { label: '要介護３', value: '8' },
      { label: '要介護４', value: '9' },
      { label: '要介護５', value: '10' },
    ],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  mo01338LevelOfCareOneway: {
    itemLabel: t('label.level-of-care'),
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-4 align-center mt-1 inline-block',
      labelClass: 'inline-block',
    }),
  } as Mo01338OnewayType,
  mo00009LevelOfCareOneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338LevelOfCareTextOneway: {
    itemLabel: '',
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-4 align-center mt-1 inline-block',
      labelClass: 'inline-block',
    }),
  } as Mo01338OnewayType,
  // 区分支給限度額
  m00038Oneway: {
    mo00045Oneway: {
      name: 'tKGendo',
      itemLabel: t('label.category-payment-limit-amount'),
      isRequired: true,
      isVerticalLabel: true,
      width: '147px',
      hideDetails: true,
      maxLength: '7',
    },
    isEditCamma: true,
  } as Mo00038OnewayType,
  // タイトル
  mo00045TitleOneway: {
    name: 'title',
    itemLabel: t('label.title'),
    width: '147px',
    hideDetails: true,
    isVerticalLabel: true,
    maxLength: '100',
  } as Mo00045OnewayType,
  // 給付率
  m00038PaymentRateOneway: {
    mo00045Oneway: {
      name: 'paymentRate',
      itemLabel: t('label.payment-rate'),
      isRequired: false,
      isVerticalLabel: true,
      width: '79px',
      hideDetails: true,
      maxLength: '3',
    },
    isEditCamma: false,
    min: -99,
    max: 999,
  } as Mo00038OnewayType,
  // 修正
  mo00611Oneway: {
    name: 'editBtn',
    btnLabel: t('btn.edit'),
    width: '52px',
    minWidth: '52px',
    tooltipText: t('tooltip.update'),
  } as Mo00611OnewayType,
  // 下アイコンボタン
  mo00009OnewayDown: {
    btnIcon: 'keyboard_arrow_down',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
    tooltipText: t('tooltip.move-next'),
  } as Mo00009OnewayType,
  // 上アイコンボタン
  mo00009OnewayUp: {
    btnIcon: 'keyboard_arrow_up',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
    tooltipText: t('tooltip.move-pre'),
  } as Mo00009OnewayType,
  // 修正
  mo00615EditOneway: {
    itemLabel: t('label.edit'),
    customClass: new CustomClass({ outerClass: 'mr-2' }),
  } as Mo00615OnewayType,
  mo00009EditOneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
    tooltipText: t('tooltip.update'),
  } as Mo00009OnewayType,
  mo00018TblScheduleOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: true,
    checkboxLabel: t('label.sched'),
    customClass: new CustomClass({ outerClass: 'ma-0 pa-0' }),
  } as Mo00018OnewayType,
  mo00018TblAchievementsOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: true,
    checkboxLabel: t('label.achv'),
    customClass: new CustomClass({ outerClass: 'ma-0 pa-0' }),
  } as Mo00018OnewayType,
  // 提供時間時間テキスト
  mo01274Oneway: {
    type: 'time',
  } as Mo01274OnewayType,
  // 提供時間プルダウン
  mo01282Oneway: {
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo01282OnewayType,
  // 予定合計プルダウン
  mo01282DropdownOneway: {
    itemTitle: 'label',
    itemValue: 'value',
    items: [
      { label: '', value: Or36245Const.PREFIX_ZERO },
      { label: Or36245Const.DEFAULT_Y_TOTAL, value: Or36245Const.DEFAULT_Y_TOTAL },
    ],
  } as Mo01282OnewayType,
  // カレンダーアイコンボタン
  mo00009CalendarOneway: {
    btnIcon: 'calendar_today',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
    size: '20',
  } as Mo00009OnewayType,
  // 予定/実施回数
  mo01278Oneway: {
    min: -9,
    max: 99,
    maxLength: '2',
  } as Mo01278OnewayType,
  mo01272Oneway: {
    name: 'time',
    width: '105px',
    hideDetails: true,
    showItemLabel: false,
    customClass: new CustomClass({ itemClass: 'ma-0' }),
  } as Mo00045OnewayType,
  // 種類限度外/区分支給限度外単位数
  mo01278TensuOverOneway: {
    isEditCamma: false,
    maxLength: '5',
  } as Mo01278OnewayType,
  // 全選チェックボックス
  mo00018CheckAllOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: '',
  } as Mo00018OnewayType,
  // 選択チェックボックス
  mo00018CheckOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    customClass: new CustomClass({ outerClass: 'ma-0 pa-0' }),
  } as Mo00018OnewayType,
  // 有効期間外サービス検索ダイアログ
  or27736Oneway: {} as Or27736OnewayType,
  // カレンダー入力ダイアログ
  or27016Oneway: {} as Or27016OnewayType,
  // シミュレーション雛形選択ダイアログ
  or28534Oneway: {} as Or28534OnewayType,
  // 認定期間中の短期入所利用日数ダイアログ
  or27856Oneway: {} as Or27856SelectInfoType,
  // 印刷設定ダイアログ
  or57151Oneway: {} as Or57151OnewayType,
  // カレンダー
  mo01343Oneway: {} as Mo01343OnewayType,
})

const or11871 = ref({ uniqueCpId: Or11871Const.CP_ID })
const or21828 = ref({ uniqueCpId: Or21828Const.CP_ID })
const or21830 = ref({ uniqueCpId: Or21830Const.CP_ID })
const orX0004 = ref({ uniqueCpId: OrX0004Const.CP_ID(0) })
const orX0014 = ref({ uniqueCpId: OrX0014Const.CP_ID(0) })
const orX0005 = ref({ uniqueCpId: OrX0005Const.CP_ID(0) })
const orX0042 = ref({ uniqueCpId: OrX0042Const.CP_ID(0) })
const or21735 = ref({ uniqueCpId: Or21735Const.CP_ID(0) })
const or21737 = ref({ uniqueCpId: Or21737Const.CP_ID(0) })
const or21738 = ref({ uniqueCpId: Or21738Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or27736 = ref({ uniqueCpId: Or27736Const.CP_ID(0) })
const or27016 = ref({ uniqueCpId: Or27016Const.CP_ID(0) })
const or35745 = ref({ uniqueCpId: Or35745Const.CP_ID(0) })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(0) })
const or27852 = ref({ uniqueCpId: Or27852Const.CP_ID(0) })
const or27856 = ref({ uniqueCpId: Or27856Const.CP_ID(0) })
const or28534 = ref({ uniqueCpId: Or28534Const.CP_ID(0) })
const or57151 = ref({ uniqueCpId: Or57151Const.CP_ID(0) })
const or41179 = ref({ uniqueCpId: '' })
const gui00048 = ref({ uniqueCpId: '' })

const screenStore = useScreenStore()
const screen = screenStore.screen()
const pageComponent = screen.supplement.pageComponent
gui00048.value.uniqueCpId = uuidv4()
// 画面コンポーネント配下に年月選択を追加
screenStore.setCpStructure({
  cpId: pageComponent.cpId,
  uniqueCpId: pageComponent.uniqueCpId,
  innerComponents: {
    [gui00048.value.uniqueCpId]: {
      cpId: Gui00048Const.CP_ID(1),
    },
  },
})
// 年月選択の初期化
Gui00048Logic.initialize(gui00048.value.uniqueCpId)
// TODO mock権限 start
// 保存権限
let hasRegist: boolean = await hasRegistAuth(Or36245Const.ROUTING)
hasRegist = true
// TODO mock権限 end
// 編集フラグ
const isEdit = computed(() => {
  return isEditNavControl([props.uniqueCpId])
})
// 電子保存の3原則
const electronicSavePrinciple = ref(false)
// 新規計算フラグ
const newComputeFlg = ref(true)
// 利用票明細一覧hover
const hoverdIndex = ref(-1)
// システム共有領域の状態管理
const systemCommonsStore = useSystemCommonsStore()
const useSlipTableBaseColumnWidth = [125, 153, 80, 44]
const useSlipOtherTableColumnWidth = ref<number[]>([
  120, 98, 171, 82, 235, 65, 126, 52, 110, 110, 128, 128, 110, 110, 74, 110, 110, 160,
])
const listTable1ColumnWidth = ref<number[]>([264, 160, 160])
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or21828Const.CP_ID]: or21828.value,
  [Or21830Const.CP_ID]: or21830.value,
  [OrX0004Const.CP_ID(0)]: orX0004.value,
  [OrX0014Const.CP_ID(0)]: orX0014.value,
  [OrX0005Const.CP_ID(0)]: orX0005.value,
  [OrX0042Const.CP_ID(0)]: orX0042.value,
  [Or21735Const.CP_ID(0)]: or21735.value,
  [Or21737Const.CP_ID(0)]: or21737.value,
  [Or21738Const.CP_ID(0)]: or21738.value,
  [Or21814Const.CP_ID(2)]: or21814.value,
  [Or27016Const.CP_ID(1)]: or27016.value,
  [Or35745Const.CP_ID(1)]: or35745.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or27852Const.CP_ID(0)]: or27852.value,
  [Or27856Const.CP_ID(0)]: or27856.value,
  [Or28534Const.CP_ID(0)]: or28534.value,
  [Or57151Const.CP_ID(0)]: or57151.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
})

onMounted(async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 福祉用具貸与フラグ（予定）、福祉用具貸与フラグ（実績）
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_WELFARE_EQUIP_LENDING },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // コード取得
  // 福祉用具貸与フラグ（予定）、福祉用具貸与フラグ（実績）
  localOneway.mo01282Oneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_WELFARE_EQUIP_LENDING
  )

  // 事業所選択プルダウンの検索条件設定
  if (systemCommonsStore.getUserSelectSelfId()) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }
})

// 画面メニュー
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    showFavorite: false,
    showViewSelect: false,
    showCreateBtn: false,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    viewSelectItems: [],
    showMasterBtn: true,
    showOptionMenuDelete: false,
    showSaveBtn: true,
    tooltipTextSaveBtn: t('tooltip.save'),
    tooltipTextPrintBtn: t('tooltip.care-plan2-print-setting-btn'),
    tooltipTextMasterBtn: '',
    tooltipTextOptionMenuBtn: '',
  },
})

// 提供年月情報
const offeryYearMonthDayList = computed(() => {
  const offeryYearMonthDays = [] as DayOfWeek[]
  const offeryYearMonth = new Date(local.offeryYearMonth.value)
  const offeryYear = offeryYearMonth.getFullYear()
  const offeryMonth = offeryYearMonth.getMonth()
  const firstDay = new Date(offeryYear, offeryMonth, 1).getDate()
  const lastDay = new Date(offeryYear, offeryMonth + 1, 0).getDate()
  for (let i = firstDay; i <= lastDay; i++) {
    const dayOfWeek = getDayOfWeek(`${offeryYear}/${offeryMonth + 1}'/'${i}`)
    offeryYearMonthDays.push({
      day: i,
      dayOfWeek: dayOfWeek,
      color: local.usingSlipInfo.weekColor[i - 1],
    } as DayOfWeek)
  }
  return offeryYearMonthDays
})

// 利用票明細一覧v-resizable-grid
const useSlipTableColumnWidth = computed(() => {
  const yearMonthColumns = new Array(offeryYearMonthDayList.value.length).fill(24) as number[]
  return [...useSlipTableBaseColumnWidth, ...yearMonthColumns, 40, 55]
})

// 利用票別表 給付管理単位数 表示/非表示
const showBenefitManagement = computed(() => {
  if (local.offeryYearMonth.value === Or36245Const.BASE_OFFER_YEAR_MONTH) {
    return true
  } else {
    const baseDate = new Date(Or36245Const.BASE_OFFER_YEAR_MONTH)
    const offeryYearMonth = new Date(local.offeryYearMonth.value)
    return offeryYearMonth > baseDate
  }
})

// 利用票別表 福祉用具貸与の場合のみ 表示/非表示
const showOnlyWelfareEquipment = computed(() => {
  if (local.offeryYearMonth.value === Or36245Const.OFFER_YEAR_MONTH_2025_04) {
    return true
  } else {
    const baseDate = new Date(Or36245Const.OFFER_YEAR_MONTH_2025_04)
    const offeryYearMonth = new Date(local.offeryYearMonth.value)
    return offeryYearMonth > baseDate
  }
})

// 利用票別表列フッターcolumn数
const riyouBeppyoSumCol = computed(() => {
  if (showBenefitManagement.value && showOnlyWelfareEquipment.value) {
    return 23
  } else if (showBenefitManagement.value && !showOnlyWelfareEquipment.value) {
    return 21
  } else {
    return 20
  }
})

// 割り振りの残り単位数
const allocationRestUnitNumber = computed(() => {
  let allocationRestUnitNumber = 0
  // 区分支給限度情報.区分限度 > 0の場合
  if (parseInt(local.usingSlipInfo.kbnShikyuuGendoData[0].dmyKbnGendo) > 0) {
    // 区分支給限度情報.区分支給限度_基準内の合計 - 区分支給限度情報.区分限度
    allocationRestUnitNumber =
      parseInt(local.usingSlipInfo.kbnShikyuuGendoData[0].cKTensuSum) -
      parseInt(local.usingSlipInfo.kbnShikyuuGendoData[0].dmyKbnGendo)
  }
  return allocationRestUnitNumber
})

// （列フッター）割り振りの残り単位数 表示/非表示
const showAllocationRestUnitNumber = computed(() => {
  let showFlg = true
  const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
  // 区分支給限度情報.区分限度 > 0の場合
  if (parseInt(kbnShikyuuGendoData.dmyKbnGendo) > 0) {
    // 区分支給限度情報.区分限度 >= 区分支給限度情報.サービス単位/金額の合計の場合
    if (parseInt(kbnShikyuuGendoData.dmyKbnGendo) > parseInt(kbnShikyuuGendoData.cSvTensuSum)) {
      // 割り振りの残り単位数が非表示にする
      showFlg = false
    } else {
      // 区分支給限度情報.区分限度 < 区分支給限度情報.サービス単位/金額の合計の場合
      // 区分支給限度情報.区分限度 = 区分支給限度情報.区分支給限度_基準内の合計の場合
      if (kbnShikyuuGendoData.dmyKbnGendo === kbnShikyuuGendoData.cKTensuSum) {
        // 割り振りの残り単位数が非表示にする
        showFlg = false
      } else {
        // 区分支給限度情報.区分限度 <> 区分支給限度情報.区分支給限度_基準内の合計の場合
        // 割り振りの残り単位数が表示にする
        showFlg = true
      }
    }
  } else {
    // 区分支給限度情報.区分限度 <= 0の場合
    // 割り振りの残り単位数が非表示にする
    showFlg = false
  }
  return showFlg
})

// （列フッター）計_サービス単位/金額の色
const cSvTensuColor = computed(() => {
  let color = Or36245Const.COLOR_DEFAULT
  const riyourshaData = local.usingSlipInfo.riyourshaData[0]
  const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
  // 利用者情報.計算フラグ = nullの場合
  if (isEmpty(riyourshaData.rCompBase)) {
    // 利用者情報.サービス単位数（実績） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuJ) > parseInt(kbnShikyuuGendoData.tKGendo)) {
      // 計_サービス単位/金額の色は「FF0000」を設定
      color = Or36245Const.COLOR_RED
    }
  } else {
    // 利用者情報.計算フラグ <> nullの場合
    // 利用者情報.サービス単位数（予定） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuY) > parseInt(kbnShikyuuGendoData.tKGendo)) {
      // 計_サービス単位/金額の色は「FF0000」を設定
      color = Or36245Const.COLOR_RED
    }
  }
  return color
})

// （列フッター）計_給付管理単位数の色
const cKyufukanriTenColor = computed(() => {
  let color = Or36245Const.COLOR_DEFAULT
  const riyourshaData = local.usingSlipInfo.riyourshaData[0]
  const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
  const cKyufukanriTenTotal = getUsingSlipOtherTableTotal(Or36245Const.KEY_C_KYUFUKANRI_TEN)
  // 利用者情報.計算フラグ = nullの場合
  if (isEmpty(riyourshaData.rCompBase)) {
    // 利用者情報.給付管理単位数 > 利用者情報.区分支給限度基準額の場合
    if (cKyufukanriTenTotal > parseInt(kbnShikyuuGendoData.tKGendo)) {
      // 計_給付管理単位数の色は「FF0000」を設定
      color = Or36245Const.COLOR_RED
    }
  } else {
    // 利用者情報.計算フラグ <> nullの場合
    // 利用者情報.給付管理単位数 > 利用者情報.区分支給限度基準額の場合
    if (cKyufukanriTenTotal > parseInt(kbnShikyuuGendoData.tKGendo)) {
      // 計_給付管理単位数の色は「FF0000」を設定
      color = Or36245Const.COLOR_RED
    }
  }
  return color
})

// （列フッター）計_利用者負担_保険/事業 （列フッター）計_利用者負担_全額負担 の色
const hutanHHutanJColor = computed(() => {
  let color = Or36245Const.COLOR_DEFAULT
  const riyourshaData = local.usingSlipInfo.riyourshaData[0]
  // 利用者負担_保険/事業の合計
  const cHutanH = getUsingSlipOtherTableTotal(Or36245Const.KEY_C_HUTAN_H)
  // 利用者負担_全額負担の合計
  const hutanJ = getUsingSlipOtherTableTotal(Or36245Const.KEY_HUTAN_J)
  // 希望額 <> NULL、かつ、(利用者負担_保険/事業の合計 + 利用者負担_全額負担の合計) > 希望額の場合
  if (
    !isEmpty(riyourshaData.dmyKibouGaku) &&
    cHutanH + hutanJ > parseInt(riyourshaData.dmyKibouGaku)
  ) {
    // 計_利用者負担_保険/事業の色は「FF00FF」を設定
    color = Or36245Const.COLOR_RED
  }
  return color
})

// 確認ダイアログ
const showDialogOr21814 = computed(() => {
  // Or26814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 警告ダイアログ
const showDialogOr21815 = computed(() => {
  // Or26815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

// 有効期間外サービス検索
const showDialogOr27736 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or27736Logic.state.get(or27736.value.uniqueCpId)?.isOpen ?? false
})

// カレンダー入力ダイアログ表示フラグ
const showDialogOr27016 = computed(() => {
  // Or27016のダイアログ開閉状態
  return Or27016Logic.state.get(or27016.value.uniqueCpId)?.isOpen ?? false
})

// 計画複写ダイアログ表示フラグ
const showDialogOr35745 = computed(() => {
  // Or27016のダイアログ開閉状態
  return Or35745Logic.state.get(or35745.value.uniqueCpId)?.isOpen ?? false
})

// 確認画面ダイアログ表示フラグ
const showDialogOr27852 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or27852Logic.state.get(or27852.value.uniqueCpId)?.isOpen ?? false
})

// シミュレーション雛形選択ダイアログ表示フラグ
const showDialogOr28534 = computed(() => {
  // Or28534のダイアログ開閉状態
  return Or28534Logic.state.get(or28534.value.uniqueCpId)?.isOpen ?? false
})

// 認定期間中の短期入所利用日数ダイアログ表示フラグ
const showDialogOr27856 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or27856Logic.state.get(or27856.value.uniqueCpId)?.isOpen ?? false
})

// 印刷設定ダイアログ表示フラグ
const showDialogOr57151 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or57151Logic.state.get(or57151.value.uniqueCpId)?.isOpen ?? false
})

// 年月選択ダイアログ表示フラグ
const showDialogGui0048 = computed(() => {
  // GuiD0048のダイアログ開閉状態
  return Gui00048Logic.state.get(gui00048.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 「保存ボタン」押下
    if (newValue.saveEventFlg) {
      // 保存処理を行う
      await usingSlipSave()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { saveEventFlg: false },
      })
    }
    // 「印刷ボタン」押下
    if (newValue.printEventFlg) {
      // 印刷処理を行う
      await usingSlipPrint()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { printEventFlg: false },
      })
    }
  }
)

/**
 * タブ切替
 */
watch(
  () => local.mo00043.id,
  async (newValue) => {
    // 「利用票」タブ押下
    if (newValue === Or36245Const.TAB_ID_USING_SLIP) {
      // 画面項目の値を再設定する
      await useSlipInitInfoSelect()
    } else {
      // 「利用票別表」タブ押下
      // 画面入力データの変更がある場合
      if (isEdit.value) {
        // 再計算処理
        await recompute()
        // 新規計算フラグを「false」に設定する
        newComputeFlg.value = false
      }
      // 画面項目の値を再設定する
      await useSlipInitInfoSelect()
    }
  }
)

/**
 * 「事業所選択」閉じる
 */
watch(
  () => Or41179Logic.data.get(or41179.value.uniqueCpId)?.modelValue,
  async () => {
    debugger
    if (isEmpty(localOneway.or36245.userId)) {
      localOneway.or36245.userId = systemCommonsStore.getUserSelectSelfId()
    }
    // 利用票情報情報取得
    await useSlipInitInfoSelect()
  }
)

/**
 * 「提供年月」変更
 */
watch(
  () => local.offeryYearMonth,
  async () => {
    // 画面入力データに変更がある場合
    if (isEdit.value) {
      // 新規処理を行う
      await usingSlipAddNew()
    } else {
      // 再検索処理
      await useSlipInitInfoSelect()
    }
  }
)

/**
 * 「介護度」変更
 */
watch(
  () => localOneway.mo01338LevelOfCareTextOneway.itemLabel,
  () => {
    // 画面.要介護度 = 21の場合
    if (localOneway.mo01338LevelOfCareTextOneway.itemLabel === Or36245Const.LEVELOF_CARE_12) {
      // 画面.区分支給限度額 = 区分支給限度基準額(変更用)
      local.categoryPaymentLimitAmount.mo00045.value = getDisplayAmount(
        local.usingSlipInfo.kbnShikyuuGendoData[0].tKGendo
      )
    }
  }
)

/**
 * 計画複写閉じる
 */
watch(
  () => Or35745Logic.state.get(or35745.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      const copyFlg = Or35745Logic.state.get(or35745.value.uniqueCpId)?.inParam?.copyFlg
      // 複写結果 = 1 の場合
      if (copyFlg === Or36245Const.USING_SLIP_COPY_RESULT_1) {
        // 利用票の初期情報を取得する。
        await useSlipInitInfoSelect()
      }
    }
  }
)

/**
 * 有効期間外サービス検索閉じる
 */
watch(
  () => Or27736Logic.state.get(or27736.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 再検索処理
      await useSlipInitInfoSelect()
    }
  }
)

// 「行追加ボタン」押下
watch(
  () => Or21735Logic.event.get(or21735.value.uniqueCpId),
  async () => {
    // 利用者情報.実績済みフラグ = 1の場合
    if (local.usingSlipInfo.riyourshaData[0].yoteiZumiFlg === Or36245Const.SCHED_FLG_OK) {
      await openConfirmDialog(t('message.i-cmn-10681'))
      // 処理終了
      return false
    }
    // 利用者情報.予定済みフラグ = 1の場合
    if (local.usingSlipInfo.riyourshaData[0].jissekiZumiFlg === Or36245Const.ACHV_FLG_OK) {
      await openConfirmDialog(t('message.i-cmn-10682'))
      // 処理終了
      return false
    }
    // TODO GUI01151_サービス選択画面をポップアップで起動する
  }
)

// 「行複写ボタン」押下
watch(
  () => Or21737Logic.event.get(or21737.value.uniqueCpId),
  async () => {
    // 利用者情報.実績済みフラグ = 1の場合
    if (local.usingSlipInfo.riyourshaData[0].yoteiZumiFlg === Or36245Const.SCHED_FLG_OK) {
      await openConfirmDialog(t('message.i-cmn-10681'))
      // 処理終了
      return false
    }
    // 利用者情報.予定済みフラグ = 1の場合
    if (local.usingSlipInfo.riyourshaData[0].jissekiZumiFlg === Or36245Const.ACHV_FLG_OK) {
      await openConfirmDialog(t('message.i-cmn-10682'))
      // 処理終了
      return false
    }
    // 画面.利用者明細情報の件数 > 0の場合
    if (local.usingSlipList.length > 0) {
      let rowCount = 1
      // 選択行の親レコード番号 <> 0 且つ 利用票明細情報には、選択行の親レコード番号と同じデータがある場合
      const selectedItem = local.usingSlipList[local.selectedUsingSlipItemIndex]
      const result = local.usingSlipList.filter(
        (item, index) =>
          index !== local.selectedUsingSlipItemIndex && item.oyaLineNo === selectedItem.oyaLineNo
      )
      if (selectedItem.oyaLineNo !== Or36245Const.DEFAULT_PARENT_NO && result.length > 0) {
        rowCount = result.length
      }
      if (rowCount > 1) {
        // GUI01175_確認ダイアログをポップアップで起動する
        Or27852Logic.state.set({
          uniqueCpId: or27852.value.uniqueCpId,
          state: { isOpen: true },
        })
      } else {
        // 行複写処理
        await lineCopy(Or36245Const.PROCESS_FLG_1)
      }
    }
  }
)

// 「行削除ボタン」押下
watch(
  () => Or21738Logic.event.get(or21738.value.uniqueCpId),
  async () => {
    // 利用者情報.実績済みフラグ = 1の場合
    if (local.usingSlipInfo.riyourshaData[0].yoteiZumiFlg === Or36245Const.SCHED_FLG_OK) {
      await openConfirmDialog(t('message.i-cmn-10681'))
      // 処理終了
      return false
    }
    // 利用者情報.予定済みフラグ = 1の場合
    if (local.usingSlipInfo.riyourshaData[0].jissekiZumiFlg === Or36245Const.ACHV_FLG_OK) {
      await openConfirmDialog(t('message.i-cmn-10682'))
      // 処理終了
      return false
    }
    // 画面.利用者情報.予定済みフラグ = 1 or 削除の行.予定済みフラグ = 1の場合
    const selectedItem = local.usingSlipList[local.selectedUsingSlipItemIndex]
    if (
      local.usingSlipInfo.riyourshaData[0].yoteiZumiFlg === Or36245Const.SCHED_FLG_OK ||
      selectedItem.yoteiZumiFlg.modelValue
    ) {
      await openConfirmDialog(t('message.i-cmn-10066'))
      // 処理終了
      return false
    }
    // 行削除の前のチェック処理を行う
    const deleteBefSelectInputData: UseSlipInfoDeleteBefSelectInEntity = {
      // 利用票明細情報：画面.利用票明細情報
      riyouList: getRiyouList(),
      // 提供年月：画面.提供年月
      teikyouYm: local.offeryYearMonth.value,
      // 削除行idx：選択された利用票明細情報のインデックス
      delIdx: local.selectedUsingSlipItemIndex.toString(),
    }
    const deleteBefSelectRet: UseSlipInfoDeleteBefSelectOutEntity = await ScreenRepository.select(
      'useSlipInfoDeleteBefSelect',
      deleteBefSelectInputData
    )
    const ret = await openConfirmDialog(
      t('message.i-cmn-11290', [
        deleteBefSelectRet.data.parentFlag === Or36245Const.PARENT_FLAG_1
          ? t('label.now-and-related-row')
          : t('label.now-row'),
      ]),
      true
    )
    if (ret === Or36245Const.CONFIRM_BTN_YES) {
      // 利用票画面行削除後処理を行う
      const deleteRowAftProcSelecInputData: UseSlipInfoDeleteRowAftProcSelectInEntity = {
        // 利用票明細情報：画面.利用票明細情報
        riyouList: getRiyouList(),
        // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
        riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
      }
      const deleteRowAftProcSelecRet: UseSlipInfoDeleteRowAftProcSelectOutEntity =
        await ScreenRepository.select(
          'useSlipInfoDeleteRowAftProcSelect',
          deleteRowAftProcSelecInputData
        )
      // 入力フォームの各項目へ設定する
      setUsingSlipInputForm(deleteRowAftProcSelecRet.data.riyou895Info[0].riyouList)
    }
  }
)

/**
 * カレンダー入力画面閉じる
 */
watch(
  () => Or27016Logic.state.get(or27016.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 返却情報.提供年月 = "9999/99"の場合-
      if (local.or27016.provideYm === Or36245Const.MAX_YEAR_MONTH) {
        // 返却情報.利用票明細情報から予定回数と実績回数を取得して、画面.利用票明細情報にセットする
        for (let i = 0; i < local.or27016.meisaiList.length; i++) {
          const meisaiItem = local.or27016.meisaiList[i]
          const riyouItem = local.usingSlipList[i]
          riyouItem.yDay01.value = meisaiItem.yday01
          riyouItem.yDay02.value = meisaiItem.yday02
          riyouItem.yDay03.value = meisaiItem.yday03
          riyouItem.yDay04.value = meisaiItem.yday04
          riyouItem.yDay05.value = meisaiItem.yday05
          riyouItem.yDay06.value = meisaiItem.yday06
          riyouItem.yDay07.value = meisaiItem.yday07
          riyouItem.yDay08.value = meisaiItem.yday08
          riyouItem.yDay09.value = meisaiItem.yday09
          riyouItem.yDay10.value = meisaiItem.yday10
          riyouItem.yDay11.value = meisaiItem.yday11
          riyouItem.yDay12.value = meisaiItem.yday12
          riyouItem.yDay13.value = meisaiItem.yday13
          riyouItem.yDay14.value = meisaiItem.yday14
          riyouItem.yDay15.value = meisaiItem.yday15
          riyouItem.yDay16.value = meisaiItem.yday16
          riyouItem.yDay17.value = meisaiItem.yday17
          riyouItem.yDay18.value = meisaiItem.yday18
          riyouItem.yDay19.value = meisaiItem.yday19
          riyouItem.yDay20.value = meisaiItem.yday20
          riyouItem.yDay21.value = meisaiItem.yday21
          riyouItem.yDay22.value = meisaiItem.yday22
          riyouItem.yDay23.value = meisaiItem.yday23
          riyouItem.yDay24.value = meisaiItem.yday24
          riyouItem.yDay25.value = meisaiItem.yday25
          riyouItem.yDay26.value = meisaiItem.yday26
          riyouItem.yDay27.value = meisaiItem.yday27
          riyouItem.yDay28.value = meisaiItem.yday28
          riyouItem.yDay29.value = meisaiItem.yday29
          riyouItem.yDay30.value = meisaiItem.yday30
          riyouItem.yDay31.value = meisaiItem.yday31
          riyouItem.jDay01.value = meisaiItem.jday01
          riyouItem.jDay02.value = meisaiItem.jday02
          riyouItem.jDay03.value = meisaiItem.jday03
          riyouItem.jDay04.value = meisaiItem.jday04
          riyouItem.jDay05.value = meisaiItem.jday05
          riyouItem.jDay06.value = meisaiItem.jday06
          riyouItem.jDay07.value = meisaiItem.jday07
          riyouItem.jDay08.value = meisaiItem.jday08
          riyouItem.jDay09.value = meisaiItem.jday09
          riyouItem.jDay10.value = meisaiItem.jday10
          riyouItem.jDay11.value = meisaiItem.jday11
          riyouItem.jDay12.value = meisaiItem.jday12
          riyouItem.jDay13.value = meisaiItem.jday13
          riyouItem.jDay14.value = meisaiItem.jday14
          riyouItem.jDay15.value = meisaiItem.jday15
          riyouItem.jDay16.value = meisaiItem.jday16
          riyouItem.jDay17.value = meisaiItem.jday17
          riyouItem.jDay18.value = meisaiItem.jday18
          riyouItem.jDay19.value = meisaiItem.jday19
          riyouItem.jDay20.value = meisaiItem.jday20
          riyouItem.jDay21.value = meisaiItem.jday21
          riyouItem.jDay22.value = meisaiItem.jday22
          riyouItem.jDay23.value = meisaiItem.jday23
          riyouItem.jDay24.value = meisaiItem.jday24
          riyouItem.jDay25.value = meisaiItem.jday25
          riyouItem.jDay26.value = meisaiItem.jday26
          riyouItem.jDay27.value = meisaiItem.jday27
          riyouItem.jDay28.value = meisaiItem.jday28
          riyouItem.jDay29.value = meisaiItem.jday29
          riyouItem.jDay30.value = meisaiItem.jday30
          riyouItem.jDay31.value = meisaiItem.jday31
        }
      }
      // 利用票画面行削除後処理を行う
      const inputData: UseSlipInfoDeleteRowAftProcSelectInEntity = {
        // 利用票明細情報：画面.利用票明細情報
        riyouList: getRiyouList(),
        // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
        riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
      }
      const response: UseSlipInfoDeleteRowAftProcSelectOutEntity = await ScreenRepository.select(
        'useSlipInfoDeleteRowAftProcSelect',
        inputData
      )
      // 入力フォームの各項目へ設定する
      setUsingSlipInputForm(response.data.riyou895Info[0].riyouList)
    }
  }
)

/**
 * 確認画面閉じる
 */
watch(
  () => Or27852Logic.state.get(or27852.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 処理フラグ <> 3の場合
      if (local.or27852.processFlg !== Or36245Const.PROCESS_FLG_3) {
        // 行複写処理
        await lineCopy(local.or27852.processFlg)
      }
    }
  }
)

/**
 * シミュレーション雛形選択画面閉じる
 */
watch(
  () => Or28534Logic.state.get(or28534.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // ポップアップ返却情報.提供年月 = "" or ポップアップ返却情報.利用者ID = 0の場合
      if (isEmpty(local.or28534.yymmYm) || isEmpty(local.or28534.userid)) {
        // 処理終了
        return false
      }
      // ポップアップ返却情報.提供年月 = 画面.提供年月 且つ 親画面.利用者ID <> ポップアップ返却情報.利用者IDの場合
      if (
        local.or28534.yymmYm === local.offeryYearMonth.value &&
        localOneway.or36245.userId !== local.or28534.userid
      ) {
        const retData = {
          ...local.or36245,
          // 親画面.利用者ID = ポップアップ返却情報.利用者ID
          userId: local.or28534.userid,
        } as Or36245Type
        // 情報値戻り
        emit('update:modelValue', retData)
        // 再検索処理
        await useSlipInitInfoSelect()
      }
      // ポップアップ返却情報.提供年月 <> 画面.提供年月の場合
      if (local.or28534.yymmYm !== local.offeryYearMonth.value) {
        // 画面.提供年月 = ポップアップ返却情報.提供年月
        local.offeryYearMonth.value = local.or28534.yymmYm
        const retData = {
          ...local.or36245,
          // 親画面.利用者ID = ポップアップ返却情報.利用者ID
          userId: local.or28534.userid,
        } as Or36245Type
        // 情報値戻り
        emit('update:modelValue', retData)
      }
    }
  }
)

/**
 * 認定期間中の短期入所利用日数画面閉じる
 */
watch(
  () => Or27856Logic.state.get(or27856.value.uniqueCpId)?.isOpen,
  (newValue) => {
    if (!newValue) {
      if (!isEmpty(local.or27856.outputItem)) {
        // 短期入所利用日数.前月迄 = ポップアップ返却情報.前月まで
        local.usingSlipInfo.sumData[0].zenGetuSum = local.or27856.outputItem.upToPrevMonth
        // 短期入所利用日数.当月 = ポップアップ返却情報.当月の計画
        local.usingSlipInfo.sumData[0].cmnTucPlanTTougetu =
          local.or27856.outputItem.planForThisMonth
        // 短期入所利用日数.累積 = ポップアップ返却情報.累計
        local.usingSlipInfo.sumData[0].computeRuikeiSum = local.or27856.outputItem.accumulation
        // 合計表示欄情報.月またぎ前月分日数=ポップアップ返却情報.継続日数
        local.usingSlipInfo.sumData[0].ov30Zengetu = local.or27856.outputItem.numOfDays
      }
    }
  }
)

// 年月選択閉じる
watch(
  () => Gui00048Logic.state.get(gui00048.value.uniqueCpId)?.isOpen,
  (newValue) => {
    if (!newValue) {
      const yearMonth =
        Gui00048Logic.event.get(gui00048.value.uniqueCpId)?.confirmParams?.startDate ?? ''
      if (!isEmpty(yearMonth)) {
        const [year, month] = yearMonth.split('/').map(Number)
        const selectedMonth = month.toString().padStart(2, Or36245Const.PREFIX_ZERO)
        local.offeryYearMonth.value = `${year}/${selectedMonth}`
        localOneway.orX0165Plan.pageLabel = `${year}/${selectedMonth}`
      }
    }
  }
)

/**
 * 指定された日付文字列から曜日を取得する
 *
 * @param dateString - 日付を表す文字列（例: "YYYY-MM-DD"形式）
 *
 * @returns 曜日の文字列表現（例: "(月)"、"(火)"など）。無効な日付の場合、空文字を返す。
 */
const getDayOfWeek = (dateString: string): string => {
  const date = new Date(dateString)
  const daysOfWeek = [
    t('label.day-short-sunday'),
    t('label.day-short-monday'),
    t('label.day-short-tuesday'),
    t('label.day-short-wednesday'),
    t('label.day-short-thursday'),
    t('label.day-short-friday'),
    t('label.day-short-saturday'),
  ]

  if (isNaN(date.getTime())) {
    return ''
  }

  const dayIndex = date.getDay()
  return daysOfWeek[dayIndex]
}

/**
 * 年月の加減処理
 *
 * @param b - 加減フラグ
 */
const addMonth = (b: boolean) => {
  // YYYY/MM形式の文字列をDateオブジェクトに変換
  const [year, month] = local.offeryYearMonth.value.split('/').map(Number)
  const date = new Date(year, month - 1)

  if (b) {
    date.setMonth(date.getMonth() + 1)
  } else {
    date.setMonth(date.getMonth() - 1)
  }

  // 加減後の年月をYYYY/MM形式で返す
  const newYear = date.getFullYear()
  const newMonth = (date.getMonth() + 1).toString().padStart(2, '0')
  local.offeryYearMonth.value = `${newYear}/${newMonth}`
  localOneway.orX0165Plan.pageLabel = `${newYear}/${newMonth}`
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
const selectUsingSlipRow = (index: number) => {
  local.selectedUsingSlipItemIndex = index
}

/**
 * 列選択
 *
 * @param day - 選択した列のindex
 */
const selectColumn = (day: number) => {
  local.selectedColumnIndex = day
}

/**
 * 列選択クリア
 */
const cleanSelectedColumn = () => {
  local.selectedColumnIndex = 0
}

/**
 * 予定/実施回数の背景色
 *
 * @param index - index
 *
 * @param day -day
 *
 * @param schedAchvFlg -予定/実績
 */
const getDayBgColor = (index: number, day: DayOfWeek, schedAchvFlg: string) => {
  // 行選択
  if (local.selectedUsingSlipItemIndex === index) {
    return Or36245Const.COLOR_SELECTED_ROW
  } else {
    // 列選択
    if (local.selectedColumnIndex === day.day) {
      return Or36245Const.COLOR_SELECTED_COL
    } else {
      if (hoverdIndex.value === index) {
        return ''
      } else {
        if (schedAchvFlg === Or36245Const.SCHED_FLG_OK) {
          return local.usingSlipList[index].dmyBgColorY[day.day - 1]
        } else {
          return local.usingSlipList[index].dmyBgColorJ[day.day - 1]
        }
      }
    }
  }
}

/**
 * 「修正」押下
 */
const editLine = async () => {
  // 利用者情報.実績済みフラグ = 1の場合
  if (local.usingSlipInfo.riyourshaData[0].yoteiZumiFlg === Or36245Const.SCHED_FLG_OK) {
    await openConfirmDialog(t('message.i-cmn-11360'))
    // 処理終了
    return false
  }
  // 利用者情報.予定済みフラグ = 1の場合
  if (local.usingSlipInfo.riyourshaData[0].jissekiZumiFlg === Or36245Const.ACHV_FLG_OK) {
    await openConfirmDialog(t('message.i-cmn-11361'))
    // 処理終了
    return false
  }
  // サービス選択画面のパラメータを取得する
  // TODO GUI01151_サービス選択画面をポップアップで起動する
}

/**
 * 提供時間_開始 表示／非表示
 *
 * @param time -時間
 */
const showOfferHoursTime = (time: string) => {
  return (
    time !== Or36245Const.SERVICE_TIME_99 &&
    time !== Or36245Const.SERVICE_TIME_88 &&
    time !== Or36245Const.SERVICE_TIME_77
  )
}

/**
 * 福祉用具貸与_予定 表示／非表示
 *
 * @param item -利用票明細情報
 */
const showOfferHoursDropdown = (item: UsingSlipDetailInfo) => {
  return (
    item.svtype === Or36245Const.SERVICE_TYPECODE_17 ||
    item.svtype === Or36245Const.SERVICE_TYPECODE_67
  )
}

/**
 * 予定回数合計取得
 *
 * @param item -利用票明細情報
 */
const getSchedTotal = (item: UsingSlipDetailInfo): number => {
  // (利用票明細情報.サービス種別コード = "17" or 利用票明細情報.サービス種別コード = "67") and
  // (利用票明細情報.福祉用具貸与フラグ（予定） = null or 利用票明細情報.福祉用具貸与フラグ（予定） = 0 or 利用票明細情報.福祉用具貸与フラグ（予定） = 2)の場合
  if (
    (item.svtype === Or36245Const.SERVICE_TYPECODE_17 ||
      item.svtype === Or36245Const.SERVICE_TYPECODE_67) &&
    (isEmpty(item.yRentalF.modelValue) ||
      item.yRentalF.modelValue === Or36245Const.WELFARE_EQUIP_LENDING_0 ||
      item.yRentalF.modelValue === Or36245Const.WELFARE_EQUIP_LENDING_2)
  ) {
    // 予定回数合計 = 利用票の初期情報.利用票明細情報.予定合計
    return parseInt(item.yTotal.modelValue)
  } else {
    // 上記以外の場合
    // 予定回数合計 = 予定回数(01)から予定回数(31)まで合計する
    let schedTotal = 0
    const dayList = offeryYearMonthDayList.value.map((item) =>
      getSchedAchvDayName(Or36245Const.PREFIX_SCHED, item.day)
    )
    // 予定回数1～予定回数31の合計
    for (const key in item) {
      if (dayList.includes(key)) {
        const value = item[key as keyof typeof item] as TimeModelValue
        schedTotal += parseInt(value.value)
      }
    }
    return schedTotal
  }
}

/**
 * 予定金額合計取得
 *
 * @param item -利用票明細情報
 */
const getSchedAmountTotal = (item: UsingSlipDetailInfo) => {
  // 利用票明細情報.合計表示フラグ = 0の場合
  if (item.dmySvTaniVisible === Or36245Const.DMY_SV_TANI_VISIBLE_NG) {
    // 予定金額合計が非表示にする
    return ''
  }
  // 予定単位回数を計算する
  // 予定単位回数
  let schedNuitNumber = 0
  // 予定回数合計
  const schedTotal = getSchedTotal(item)
  // 算定回数
  const calculationNumber = parseInt(item.dmyOneOfMonth)
  // 予定回数合計 > 0 and 利用票明細情報.算定回数 > 0 and 予定回数合計 > 利用票明細情報.算定回数の場合、
  if (schedTotal > 0 && calculationNumber > 0 && schedTotal > calculationNumber) {
    // 予定単位回数 = 利用票明細情報.算定回数
    schedNuitNumber = calculationNumber
  } else {
    // 上記以外の場合
    // 予定単位回数 = 予定回数合計
    schedNuitNumber = schedTotal
  }
  // 予定金額合計を取得する
  let schedAmountTotal = 0
  // 利用票明細情報.サービス種別コード <> "17" and 利用票明細情報.サービス種別コード <> "67"の場合、
  if (
    item.svtype !== Or36245Const.SERVICE_TYPECODE_17 &&
    item.svtype !== Or36245Const.SERVICE_TYPECODE_67
  ) {
    // 予定金額合計 = 利用票明細情報.単価（予定） * 予定単位回数
    schedAmountTotal = parseInt(item.dmy0SvTani) * schedNuitNumber
  } else {
    // 利用票明細情報.小数点以下の端数処理フラグ = 1の場合
    if (item.dmyRHasu === Or36245Const.DECIMAL_PROCESS_FLG_1) {
      // 切り捨て
      // 予定金額合計 = 利用票明細情報.単価（予定） * 予定単位回数
      schedAmountTotal = parseInt(item.dmy0SvTani) * schedNuitNumber
    } else if (item.dmyRHasu === Or36245Const.DECIMAL_PROCESS_FLG_2) {
      // 利用票明細情報.小数点以下の端数処理フラグ = 2の場合
      // 切り上げ
      // 予定金額合計 = 利用票明細情報.単価（予定） * 予定単位回数
      schedAmountTotal = Math.ceil(parseFloat(item.dmy0SvTani)) * schedNuitNumber
    } else {
      // 四捨五入
      // 予定金額合計 = 利用票明細情報.単価（予定） * 予定単位回数
      schedAmountTotal = Math.round(parseFloat(item.dmy0SvTani)) * schedNuitNumber
    }
  }
  return schedAmountTotal
}

/**
 * 予定回数合計プルダウン 表示／非表示
 *
 * @param item -利用票明細情報
 */
const showSchedTotalDropdown = (item: UsingSlipDetailInfo) => {
  // (利用票明細情報.サービス種別コード = "17" or 利用票明細情報.サービス種別コード = "67") and
  // (利用票明細情報.福祉用具貸与フラグ（予定） = null or 利用票明細情報.福祉用具貸与フラグ（予定） = 0 or 利用票明細情報.福祉用具貸与フラグ（予定） = 2)の場合
  if (
    (item.svtype === Or36245Const.SERVICE_TYPECODE_17 ||
      item.svtype === Or36245Const.SERVICE_TYPECODE_67) &&
    (isEmpty(item.yRentalF) ||
      item.yRentalF.modelValue === Or36245Const.WELFARE_EQUIP_LENDING_0 ||
      item.yRentalF.modelValue === Or36245Const.WELFARE_EQUIP_LENDING_2)
  ) {
    // 表示する
    return true
  } else {
    // 非表示にする
    return false
  }
}

/**
 * 提供時間 keydown
 *
 * @param event - event
 */
const handleKeydown = (event: KeyboardEvent) => {
  // サービス時間 = null  or ""の場合
  if (event.key === Or36245Const.KEYBOARD_BACKSPACE || event.key === Or36245Const.KEYBOARD_DELETE) {
    // 処理終了
    event.preventDefault()
  }
}

/**
 * 利用票画面初期情報取得
 */
const useSlipInitInfoSelect = async () => {
  // 利用票画面初期情報取得
  const inputData: SimInitInfoSelectInEntity = {
    /** 事業所ID */
    defSvJigyoId: Or41179Logic.data.get(or41179.value.uniqueCpId)?.modelValue ?? '',
    /** 利用者ID */
    userId: localOneway.or36245.userId,
    /** 提供年月 */
    appYm: local.offeryYearMonth.value,
    /** 事業者グループ適用ID */
    tekiyoFlg: localOneway.or36245.officeGroupApplyId,
    /** 適用事業所ＩＤリスト */
    jigyoList: localOneway.or36245.applyOfficeIdList.map((item) => {
      return {
        jigyoId: item,
      }
    }),
  }
  const response: SimInitInfoSelectOutEntity = await ScreenRepository.select(
    'simInitInfoSelect',
    inputData
  )
  // 入力フォームの各項目設定
  setInputForm(response.data.riyouInfo[0])
}

/**
 * 利用者情報設定
 *
 * @param riyouInfo -利用票画面情報
 */
const setRiyourshaData = (riyouInfo: RiyouInfo) => {
  // 利用者情報
  const riyourshaData = riyouInfo.riyourshaData[0]
  local.usingSlipInfo = riyouInfo
  // 提供年月
  local.offeryYearMonth.value = riyouInfo.teikyouYm
  localOneway.orX0165Plan.pageLabel = riyouInfo.teikyouYm
  // 介護度
  local.levelOfCare.modelValue = riyourshaData.dmyKaigodo
  localOneway.mo01338LevelOfCareTextOneway.itemLabel = riyourshaData.dmyKaigodo
  // 利用者情報.予定済みフラグ = 1 OR 利用者情報.実績済みフラグ = 1の場合
  if (
    riyourshaData.yoteiZumiFlg === Or36245Const.SCHED_FLG_OK ||
    riyourshaData.jissekiZumiFlg === Or36245Const.ACHV_FLG_OK
  ) {
    // 介護度選択アイコンボタン非活性にする
    localOneway.mo00009LevelOfCareOneway.disabled = true
    // 作成年月日が入力不可
    localOneway.mo00020YmdOneway.disabled = true
  } else {
    // 介護度選択アイコンボタン活性にする
    localOneway.mo00009LevelOfCareOneway.disabled = false
    // 作成年月日が入力可
    localOneway.mo00020YmdOneway.disabled = false
  }
  // 区分支給限度額
  local.categoryPaymentLimitAmount.mo00045.value = getDisplayAmount(riyourshaData.dmyShikyuGaku)
  // 利用者情報.要介護度 = 21の場合
  if (riyourshaData.dmyKaigodo === Or36245Const.LEVELOF_CARE_12) {
    // 活性にする
    if (localOneway.m00038Oneway.mo00045Oneway) {
      localOneway.m00038Oneway.mo00045Oneway.disabled = false
    }
  } else {
    // 以外の場合 非活性にする
    if (localOneway.m00038Oneway.mo00045Oneway) {
      localOneway.m00038Oneway.mo00045Oneway.disabled = true
    }
  }
  // タイトル = 利用者情報.タイトル
  local.title.value = riyourshaData.title
  // 作成日
  local.createDate.value = riyourshaData.createYmd
  // 給付率
  local.paymentRate.mo00045.value = riyourshaData.simWaribikiRitu
}

/**
 * 入力フォームの各項目設定
 *
 * @param riyouInfo -利用票画面情報
 */
const setInputForm = (riyouInfo: RiyouInfo) => {
  // 利用者情報設定
  setRiyourshaData(riyouInfo)
  // 利用票明細情報入力フォームの各項目設定
  setUsingSlipInputForm(riyouInfo.riyouList)
  // 別表明細情報入力フォームの各項目設定
  setUsingSlipOtherTableInputForm(riyouInfo.riyouBeppyoList)
}

/**
 * 利用票明細情報入力フォームの各項目設定
 *
 * @param riyouList -利用票明細情報
 */
const setUsingSlipInputForm = (riyouList: Riyou[]) => {
  // 利用票明細一覧
  local.usingSlipList.length = 0
  for (const riyou of riyouList) {
    const usingSlipItem = {
      ...riyou,
      yoteiZumiFlg: {
        modelValue: riyou.yoteiZumiFlg === Or36245Const.SCHED_FLG_OK,
      },
      jissekiZumiFlg: {
        modelValue: riyou.jissekiZumiFlg === Or36245Const.ACHV_FLG_OK,
      },
      svStartTime: {
        value: riyou.svStartTime,
      },
      svEndTime: {
        value: riyou.svEndTime,
      },
      yRentalF: {
        modelValue: riyou.yRentalF,
      },
      jRentalF: {
        modelValue: riyou.jRentalF,
      },
      yDay01: {
        value: riyou.yDay01,
      },
      yDay02: {
        value: riyou.yDay02,
      },
      yDay03: {
        value: riyou.yDay03,
      },
      yDay04: {
        value: riyou.yDay04,
      },
      yDay05: {
        value: riyou.yDay05,
      },
      yDay06: {
        value: riyou.yDay06,
      },
      yDay07: {
        value: riyou.yDay07,
      },
      yDay08: {
        value: riyou.yDay08,
      },
      yDay09: {
        value: riyou.yDay09,
      },
      yDay10: {
        value: riyou.yDay10,
      },
      yDay11: {
        value: riyou.yDay11,
      },
      yDay12: {
        value: riyou.yDay12,
      },
      yDay13: {
        value: riyou.yDay13,
      },
      yDay14: {
        value: riyou.yDay14,
      },
      yDay15: {
        value: riyou.yDay15,
      },
      yDay16: {
        value: riyou.yDay16,
      },
      yDay17: {
        value: riyou.yDay17,
      },
      yDay18: {
        value: riyou.yDay18,
      },
      yDay19: {
        value: riyou.yDay19,
      },
      yDay20: {
        value: riyou.yDay20,
      },
      yDay21: {
        value: riyou.yDay21,
      },
      yDay22: {
        value: riyou.yDay22,
      },
      yDay23: {
        value: riyou.yDay23,
      },
      yDay24: {
        value: riyou.yDay24,
      },
      yDay25: {
        value: riyou.yDay25,
      },
      yDay26: {
        value: riyou.yDay26,
      },
      yDay27: {
        value: riyou.yDay27,
      },
      yDay28: {
        value: riyou.yDay28,
      },
      yDay29: {
        value: riyou.yDay29,
      },
      yDay30: {
        value: riyou.yDay30,
      },
      yDay31: {
        value: riyou.yDay31,
      },
      jDay01: {
        value: riyou.jDay01,
      },
      jDay02: {
        value: riyou.jDay02,
      },
      jDay03: {
        value: riyou.jDay03,
      },
      jDay04: {
        value: riyou.jDay04,
      },
      jDay05: {
        value: riyou.jDay05,
      },
      jDay06: {
        value: riyou.jDay06,
      },
      jDay07: {
        value: riyou.jDay07,
      },
      jDay08: {
        value: riyou.jDay08,
      },
      jDay09: {
        value: riyou.jDay09,
      },
      jDay10: {
        value: riyou.jDay10,
      },
      jDay11: {
        value: riyou.jDay11,
      },
      jDay12: {
        value: riyou.jDay12,
      },
      jDay13: {
        value: riyou.jDay13,
      },
      jDay14: {
        value: riyou.jDay14,
      },
      jDay15: {
        value: riyou.jDay15,
      },
      jDay16: {
        value: riyou.jDay16,
      },
      jDay17: {
        value: riyou.jDay17,
      },
      jDay18: {
        value: riyou.jDay18,
      },
      jDay19: {
        value: riyou.jDay19,
      },
      jDay20: {
        value: riyou.jDay20,
      },
      jDay21: {
        value: riyou.jDay21,
      },
      jDay22: {
        value: riyou.jDay22,
      },
      jDay23: {
        value: riyou.jDay23,
      },
      jDay24: {
        value: riyou.jDay24,
      },
      jDay25: {
        value: riyou.jDay25,
      },
      jDay26: {
        value: riyou.jDay26,
      },
      jDay27: {
        value: riyou.jDay27,
      },
      jDay28: {
        value: riyou.jDay28,
      },
      jDay29: {
        value: riyou.jDay29,
      },
      jDay30: {
        value: riyou.jDay30,
      },
      jDay31: {
        value: riyou.jDay31,
      },
      yTotal: {
        modelValue: Or36245Const.DEFAULT_Y_TOTAL,
      },
    } as UsingSlipDetailInfo
    local.usingSlipList.push(usingSlipItem)
  }
}

/**
 * 別表明細情報入力フォームの各項目設定
 *
 * @param riyouBeppyoList -別表明細情報
 */
const setUsingSlipOtherTableInputForm = (riyouBeppyoList: RiyouBeppyo[]) => {
  // 利用票別表明細一覧
  local.usingSlipOthrtTableList.length = 0
  for (const riyouBeppyo of riyouBeppyoList) {
    const usingSlipOthrtTable = {
      ...riyouBeppyo,
      cScode: riyouBeppyo.cScode.slice(0, 6),
      sTensuOver: {
        value: riyouBeppyo.sTensuOver,
      },
      kTensuOver: {
        value: riyouBeppyo.kTensuOver,
      },
    } as UsingSlipOthrtTable
    local.usingSlipOthrtTableList.push(usingSlipOthrtTable)
  }
}

/**
 * 保存処理
 */
const usingSlipSave = async () => {
  // 画面入力データの変更がなし場合
  if (isEdit.value === false) {
    await openConfirmDialog(t('message.i-cmn-21800'))
    // 処理終了
    return Or36245Const.CONFIRM_BTN_NO
  }
  // 限度額
  let limitAmount = 0
  // 画面.別表明細情報がある 且つ
  // 画面.区分支給限度.区分支給限度基準額(単位) > 0 且つ
  // 画面.区分支給限度.サービス単位/金額の合計 > 0 の場合
  if (
    local.usingSlipOthrtTableList.length > 0 &&
    parseInt(local.usingSlipInfo.kbnShikyuuGendoData[0].tKGendo) > 0 &&
    getUsingSlipOtherTableTotal(Or36245Const.KEY_C_SV_TENSU) > 0
  ) {
    // 限度額 = 画面.区分支給限度.種類支給限度_超過の合計 + 画面.区分支給限度.区分支給限度_超過の合計 + 画面.区分支給限度.区分支給限度_基準内の合計
    limitAmount =
      getUsingSlipOtherTableTotal(Or36245Const.KEY_C_S_TENSU_OVER) +
      getUsingSlipOtherTableTotal(Or36245Const.KEY_C_K_TENSU_OVER) +
      getUsingSlipOtherTableTotal(Or36245Const.KEY_C_K_TENSU)
    // 画面.区分支給限度.サービス単位/金額の合計 = 限度額 且つ
    // 画面.区分支給限度.区分支給限度基準額(単位) >= 画面.区分支給限度.区分支給限度_基準内の合計 以外の場合
    if (
      !(
        getUsingSlipOtherTableTotal(Or36245Const.KEY_C_SV_TENSU) === limitAmount &&
        parseInt(local.usingSlipInfo.kbnShikyuuGendoData[0].tKGendo) >=
          getUsingSlipOtherTableTotal(Or36245Const.KEY_C_K_TENSU)
      )
    ) {
      await openConfirmDialog(t('message.i-cmn-10504'))
      // 処理終了
      return Or36245Const.CONFIRM_BTN_NO
    }
  }
  // 利用票明細情報がない場合
  if (local.usingSlipList.length === 0) {
    await openConfirmDialog(t('message.i-cmn-10510'))
    // 処理終了
    return Or36245Const.CONFIRM_BTN_NO
  }
  // 利用者情報.介護度 = 0 or null の場合
  const levelOfCare = localOneway.mo01338LevelOfCareTextOneway.itemLabel
  if (isEmpty(levelOfCare) || levelOfCare === Or36245Const.LEVELOF_CARE_0) {
    await openConfirmDialog(t('message.i-cmn-10683'))
    // 処理終了
    return Or36245Const.CONFIRM_BTN_NO
  }
  // 再計算処理
  const ret = await recompute()
  // 新規計算フラグを「false」に設定する
  newComputeFlg.value = false
  // 再計算処理が異常終了の場合
  if (ret !== Or36245Const.CONFIRM_BTN_YES) {
    // 処理を終了する
    return Or36245Const.CONFIRM_BTN_NO
  }
  // シミュレーションの保存処理を行う
  const inputData: SimUpdateInEntity = {
    // 事業所ＩＤ：親画面.事業所ＩＤ
    defSvJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 提供月（日）：提供月（日）
    teikyouYmD: local.usingSlipInfo.teikyouYmD,
    // 利用者情報：利用者情報
    riyourshaData: local.usingSlipInfo.riyourshaData,
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 別表明細情報：画面.別表明細情報
    riyouBeppyoList: getRiyouBeppyoList(),
    // 種類別限度情報：種類別限度情報
    syuruiGendoData: local.usingSlipInfo.syuruiGendoData,
    // 合計表示欄情報：画面.合計表示欄情報
    sumData: local.usingSlipInfo.sumData,
    // 短期入所連続利用30日超過情報：短期入所連続利用30日超過情報
    planOv30List: local.usingSlipInfo.planOv30List,
    // 親画面.利用者ID = 利用票明細情報.利用者ID
    modifiedCnt: local.usingSlipInfo.riyourshaData[0].modifiedCnt,
  }
  await ScreenRepository.update('simUpdate', inputData)
  return Or36245Const.CONFIRM_BTN_YES
}

/**
 * 「再計算ボタン」押下
 */
const recompute = async () => {
  // 利用者情報.介護度 = 0 or null の場合
  const levelOfCare = localOneway.mo01338LevelOfCareTextOneway.itemLabel
  if (isEmpty(levelOfCare) || levelOfCare === Or36245Const.LEVELOF_CARE_0) {
    await openConfirmDialog(t('message.i-cmn-10683'))
    // 処理終了
    return false
  }
  // 利用票明細情報からサービス事業者ID配列を取得
  const svJigyoIdList = local.usingSlipList
    .map((item) => item.svJigyoId)
    .filter((item) => isEmpty(item))
  // サービス事業者ID配列にすべて値がNULLの場合
  if (svJigyoIdList.length === local.usingSlipList.length) {
    await openConfirmDialog(t('message.i-cmn-10508'))
    // 処理終了
    return Or36245Const.CONFIRM_BTN_YES
  }
  // シミュレーションの再計算前のチェック処理を行う
  const recalculationBefInputData: SimReCalculationCheckSelectInEntity = {
    // 利用者情報：画面.利用者情報
    riyourshaData: local.usingSlipInfo.riyourshaData,
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
    riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
  }
  const recalculationBefRet: SimReCalculationCheckSelectOutEntity = await ScreenRepository.select(
    'simReCalculationCheckSelect',
    recalculationBefInputData
  )
  // リターンコード
  let retCode = recalculationBefRet.data.riyou1003Info[0].retCode
  // 対象行
  let targetRow = -1
  // リターンコード = 1 の場合
  if (retCode === Or36245Const.PROCESS_FLG_1) {
    await openConfirmDialog(t('message.i-cmn-10065'))
  } else if (retCode === Or36245Const.PROCESS_FLG_2) {
    // リターンコード = 2 の場合
    // 取得した表示メッセージリストで、順番にメッセージを呼び出す
    for (const message of recalculationBefRet.data.riyou1003Info[0].hyojiMsgList) {
      const ret = await openConfirmDialog(message.msgText, true)
      // いいえ
      if (ret === Or36245Const.CONFIRM_BTN_NO) {
        // 該当メッセージ情報.対象行を対応する明細行を選択状態にする。処理を異常終了する。
        local.selectedUsingSlipItemIndex = parseInt(message.msgRowIndex)
        // 処理を異常終了する
        return Or36245Const.CONFIRM_BTN_NO
      }
    }
  } else if (retCode === Or36245Const.PROCESS_FLG_6) {
    // リターンコード = 6 の場合
    await openWarningDialog(t('message.w-cmn-20097'))
  } else {
    // 利用票画面情報再計算前利用票明細データチェック及び補正2を行う
    const recalculationBef2InputData: UseSlipInfoRecalculationBef2SelectInEntity = {
      // 利用者情報：画面.利用者情報
      riyourshaData: local.usingSlipInfo.riyourshaData,
      // 利用票明細情報：画面.利用票明細情報
      riyouList: getRiyouList(),
      // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
      riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
      // 再計算前引数情報：返却情報.再計算前引数情報
      calcBefObj: recalculationBefRet.data.riyou1003Info[0].calcBefObj,
    }
    const recalculationBef2Ret: UseSlipInfoRecalculationBef2SelectOutEntity =
      await ScreenRepository.select(
        'useSlipInfoRecalculationBef2Select',
        recalculationBef2InputData
      )
    // リターンコード
    retCode = recalculationBef2Ret.data.retCode
    // 対象行
    targetRow = parseInt(recalculationBef2Ret.data.rowIndex)
    if (retCode === Or36245Const.PROCESS_FLG_3) {
      // 処理結果 = 3 の場合
      await openConfirmDialog(t('message.i-cmn-11336'))
    } else if (retCode === Or36245Const.PROCESS_FLG_4) {
      // 処理結果 = 4 の場合
      await openConfirmDialog(t('message.i-cmn-11337'))
    } else if (retCode === Or36245Const.PROCESS_FLG_5) {
      // 処理結果 = 5 の場合
      await openConfirmDialog(recalculationBef2Ret.data.msg)
    } else if (retCode === Or36245Const.PROCESS_FLG_7) {
      // 処理結果 = 7 の場合
      await openConfirmDialog(t('message.i-cmn-10355'))
    } else if (retCode === Or36245Const.PROCESS_FLG_8) {
      // 返却情報.リターンコード = 8 の場合
      // メッセージ
      const msg = recalculationBef2Ret.data.msg
      // 返却情報.メッセージの先頭２桁が"3:" ro "4:" or "7:"の場合
      if (
        msg.startsWith(Or36245Const.MESSAGE_PREFIX_3) ||
        msg.startsWith(Or36245Const.MESSAGE_PREFIX_4) ||
        msg.startsWith(Or36245Const.MESSAGE_PREFIX_7)
      ) {
        // 返却情報.対象行を「0」に設定する。
        targetRow = 0
      } else {
        // 返却情報.メッセージの先頭２桁が"3:" ro "4:" or "7:" が以外の場合
        await openConfirmDialog(t('message.i-cmn-10356'))
      }
    } else if (retCode === Or36245Const.PROCESS_FLG_9) {
      // 返却情報.リターンコード = 9 の場合
      await openConfirmDialog(t('message.i-cmn-10357'))
    }
  }
  // 返却情報.リターンコード<>0 AND 返却情報.対象行>0 の場合
  if (retCode !== Or36245Const.PROCESS_FLG_0 && targetRow > 0) {
    // 返却情報.表示メッセージリスト.対象行を対応する明細行を選択状態にする。
    local.selectedUsingSlipItemIndex = targetRow
  }

  // 利用票画面情報再計算
  const recomputeInputData: SimReCalculationSelectInEntity = {
    // 新規計算フラグ：TRUE
    fullCalc: Or36245Const.FULL_CALC_TRUE,
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 提供月（日）：画面退避情報.提供月（日）
    teikyouYmD: local.usingSlipInfo.teikyouYmD,
    // 利用者ＩＤ
    userid: localOneway.or36245.userId,
    // 事業所ＩＤ
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    // 適用事業所ＩＤリスト：親画面.適用事業所id配列
    svJigyoIdList: localOneway.or36245.applyOfficeIdList.map((item) => {
      return {
        svJigyoId: item,
      }
    }),
    // 利用者情報：画面.利用者情報
    riyourshaData: local.usingSlipInfo.riyourshaData,
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 別表明細情報：画面.別表明細情報
    riyouBeppyoList: getRiyouBeppyoList(),
    // 算定確認情報：画面.算定確認情報
    riyouKakuninList: local.usingSlipInfo.riyouKakuninList,
    // 種類別限度情報：画面.種類別限度情報
    syuruiGendoData: local.usingSlipInfo.syuruiGendoData,
    // 合計表示欄情報：画面.合計表示欄情報
    sumData: local.usingSlipInfo.sumData,
    // 公費集計欄情報：画面.公費集計欄情報
    kohiList: local.usingSlipInfo.kohiList,
    // 利用料集計欄情報
    riyouryouList: [],
    // 社福軽減集計欄情報：画面.社福軽減集計欄情報
    syafukuList: [],
    // 短期入所連続利用30日超過情報：画面退避情報.短期入所連続利用30日超過情報
    planOv30List: local.usingSlipInfo.planOv30List,
    // 短期利用日数保持情報：画面退避情報.短期利用日数保持情報
    svplanShortList: local.usingSlipInfo.svplanShortList,
    // 提供事業所毎小計情報：画面退避情報.提供事業所毎小計情報
    servicePointList: local.usingSlipInfo.servicePointList,
    // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
    riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
  }
  const recomputeRet: SimReCalculationSelectOutEntity = await ScreenRepository.select(
    'simReCalculationSelect',
    recomputeInputData
  )
  const riyou703Info = recomputeRet.data.riyou703Info[0]
  // 利用票明細情報入力フォームの各項目設定
  setUsingSlipInputForm(riyou703Info.riyouList)
  // 別表明細情報入力フォームの各項目設定
  setUsingSlipOtherTableInputForm(riyou703Info.riyouBeppyoList)
  return Or36245Const.CONFIRM_BTN_YES
}

/**
 * 「新規ボタン」押下
 */
const usingSlipAddNew = async () => {
  // 画面入力データの変更がある場合
  if (isEdit.value) {
    // シミュレーションの更新確認を行う
    const confirmInputData: SimulationInfoUpdateConfirmSelectInEntity = {
      // 機能ID：親画面.機能ID
      functionId: localOneway.or36245.functionId,
      // 適用事業所ID ：親画面.適用事業所リスト
      svJigyoIdList: localOneway.or36245.applyOfficeIdList,
      // 職員ID ：親画面.職員ID
      shokuinId: localOneway.or36245.staffId,
      // ログイン番号：親画面.ログイン番号
      loginCd: localOneway.or36245.loginCd,
      // ログインユーザタイプ：親画面.ログインユーザタイプ
      loginUserType: localOneway.or36245.loginUserType,
      // 電子カルテ連携フラグ：親画面.電子カルテ連携フラグ
      emrLinkFlag: localOneway.or36245.emrLinkFlag,
      // システムコード：親画面.システムコード
      systemCd: localOneway.or36245.sysCode,
    }
    const confirmRet: SimulationInfoUpdateConfirmSelectOutEntity = await ScreenRepository.select(
      'simulationInfoUpdateConfirmSelect',
      confirmInputData
    )
    // リターンコード
    const retCode = confirmRet.data.retCode
    // 戻り値 = 1の場合
    if (retCode === Or36245Const.PROCESS_FLG_1) {
      // 保存処理を行う
      if ((await usingSlipSave()) !== Or36245Const.CONFIRM_BTN_YES) {
        // 異常終了
        return false
      }
    } else if (retCode === Or36245Const.PROCESS_FLG_3) {
      // 戻り値 = 3の場合
      // 異常終了
      return false
    }
    // 利用票明細情報がある場合
    if (local.usingSlipList.length > 0) {
      // 利用票明細情報をループする
      for (const usingSlipItem of local.usingSlipList) {
        // 該当行.サービス事業者ID = nullの場合
        if (isEmpty(usingSlipItem.svJigyoId)) {
          // 0にする
          usingSlipItem.svJigyoId = Or36245Const.SV_JIGYO_ID_0
        }
        // 該当行.サービス項目ID = nullの場合
        if (isEmpty(usingSlipItem.svItemCd)) {
          // 0にする
          usingSlipItem.svItemCd = Or36245Const.SV_ITEM_CD_0
        }
        // 該当行.サービス項目ID  = 0 or 該当行.サービス項目ID <= 0の場合
        if (
          usingSlipItem.svItemCd === Or36245Const.SV_ITEM_CD_0 ||
          parseInt(usingSlipItem.svItemCd) <= 0
        ) {
          await openWarningDialog(t('message.w.cmn.20824'))
          // 処理終了
          return false
        }
      }
    }
    // 新規処理を行う
    const insertInputData: SimNewTreatmentSelectInEntity = {
      // 利用者ＩＤ
      userId: localOneway.or36245.userId,
      // 提供年月：画面.提供年月
      teikyouYm: local.offeryYearMonth.value,
      // 提供月（日）：提供月（日）
      teikyouYmD: local.usingSlipInfo.teikyouYmD,
      // 利用票明細情報：画面.利用票明細情報
      riyouList: getRiyouList(),
      // 別表明細情報：画面.別表明細情報
      riyouBeppyoList: getRiyouBeppyoList(),
      // 区分支給限度情報：画面.区分支給限度情報
      kbnShikyuuGendoData: local.usingSlipInfo.kbnShikyuuGendoData,
      // 利用票画面処理用構造体：利用票画面処理用構造体
      riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
    }
    const insertRet: SimNewTreatmentSelectOutEntity = await ScreenRepository.select(
      'simNewTreatmentSelect',
      insertInputData
    )
    const retData = {
      ...local.or36245,
      // 親画面.利用者ID＝0
      userId: Or36245Const.DEFAULT_USER_ID,
    } as Or36245Type
    // 情報値戻り
    emit('update:modelValue', retData)
    // 画面項目の値を再設定する。
    setUsingSlipInputForm(insertRet.data.riyouList)
    setUsingSlipOtherTableInputForm(insertRet.data.riyouBeppyoList)
  }
}

/**
 * 印刷処理
 */
const usingSlipPrint = async () => {
  // 電子保存の3原則が適用しない AND 画面入力データの変更がある場合
  if (electronicSavePrinciple.value === false && isEdit.value) {
    const ret = await openConfirmDialog(t('message.i-cmn-10430'), true)
    // はい
    if (ret === Or36245Const.CONFIRM_BTN_YES) {
      // 保存処理実行
      const saveResult = await usingSlipSave()
      if (saveResult !== Or36245Const.CONFIRM_BTN_YES) {
        // 更新異常の場合、処理終了
        return false
      }
    } else if (ret === Or36245Const.CONFIRM_BTN_NO) {
      // いいえ：ダイアログ画面を閉じる、処理続き
    } else {
      // キャンセル：処理終了
      return false
    }
  }
  localOneway.or57151Oneway = {
    // 提供年月：画面.提供年月
    teiYm: local.offeryYearMonth.value,
    // セクション名："シミュレーション印刷"
    sectionName: Or36245Const.SECTION_NAME,
    // デフォルト値：1
    // 自事業所ID：親画面.事業者ID
    // 利用者ID：親画面.利用者ID
    userId: localOneway.or36245.userId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.or36245.facilityId,
    // 法人ID：親画面.法人ID
    houjinId: localOneway.or36245.corporationId,
  } as Or57151OnewayType
  // GUI01147 印刷設定画面をポップアップで起動する。
  Or57151Logic.state.set({
    uniqueCpId: or57151.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「有効期間外サービスボタン」押下
 */
const validPeriodOtherThanService = async () => {
  // 画面入力データの変更がある場合
  if (isEdit.value) {
    const ret = await openConfirmDialog(t('message.i-cmn-10430'), true)
    // はい
    if (ret === Or36245Const.CONFIRM_BTN_YES) {
      // 保存処理実行
      const saveResult = await usingSlipSave()
      if (saveResult !== Or36245Const.CONFIRM_BTN_YES) {
        // 更新異常の場合、処理終了
        return false
      }
    } else if (ret === Or36245Const.CONFIRM_BTN_NO) {
      // いいえ：ダイアログ画面を閉じる、処理続き
    } else {
      // キャンセル：処理終了
      return false
    }
  }
  // GUI01048_有効期間外サービス検索をポップアップで起動する
  localOneway.or27736Oneway = {
    officeId: systemCommonsStore.getSvJigyoId ?? '',
    shokuinId: localOneway.or36245.staffId,
    sysCd: localOneway.or36245.sysCode,
    kinouId: localOneway.or36245.functionId,
    kenFlg: '',
    jgyouLst: [],
    showCsvBtn: true,
    periodStartDate: {
      value: '',
    },
    periodEndDate: {
      value: '',
    },
  } as Or27736OnewayType
  // Or27736のダイアログ開閉状態を更新する
  Or27736Logic.state.set({
    uniqueCpId: or27736.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「雛形選択」ボタン押下
 */
const prototypeSelect = async () => {
  // 画面入力データの変更がある場合
  if (isEdit.value) {
    // シミュレーションの更新確認を行う
    const confirmInputData: SimulationInfoUpdateConfirmSelectInEntity = {
      // 機能ID：親画面.機能ID
      functionId: localOneway.or36245.functionId,
      // 適用事業所ID ：親画面.適用事業所リスト
      svJigyoIdList: localOneway.or36245.applyOfficeIdList,
      // 職員ID ：親画面.職員ID
      shokuinId: localOneway.or36245.staffId,
      // ログイン番号：親画面.ログイン番号
      loginCd: localOneway.or36245.loginCd,
      // ログインユーザタイプ：親画面.ログインユーザタイプ
      loginUserType: localOneway.or36245.loginUserType,
      // 電子カルテ連携フラグ：親画面.電子カルテ連携フラグ
      emrLinkFlag: localOneway.or36245.emrLinkFlag,
      // システムコード：親画面.システムコード
      systemCd: localOneway.or36245.sysCode,
    }
    const confirmRet: SimulationInfoUpdateConfirmSelectOutEntity = await ScreenRepository.select(
      'simulationInfoUpdateConfirmSelect',
      confirmInputData
    )
    // リターンコード
    const retCode = confirmRet.data.retCode
    // 戻り値 = 1の場合
    if (retCode === Or36245Const.PROCESS_FLG_1) {
      // 保存処理を行う
      if ((await usingSlipSave()) !== Or36245Const.CONFIRM_BTN_YES) {
        // 異常終了
        return false
      }
    } else if (retCode === Or36245Const.PROCESS_FLG_3) {
      // 戻り値 = 3の場合
      // 異常終了
      return false
    }
    const retData = {
      ...local.or36245,
      // 親画面.提供年月 = 画面.提供年月
      offerYearMonth: local.offeryYearMonth.value,
    } as Or36245Type
    // 情報値戻り
    emit('update:modelValue', retData)
    localOneway.or28534Oneway = {
      shienId: localOneway.or36245.shienId,
      yymmYm: local.offeryYearMonth.value,
      ymIndicatedFlg: Or36245Const.YM_INDICATED_FLG,
    } as Or28534OnewayType
    // GUI01214_シミュレーション雛形選択画面をポップアップで起動する
    Or28534Logic.state.set({
      uniqueCpId: or28534.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * 「削除」ボタン押下
 */
const usingSlipDelete = async () => {
  // 利用者情報がない場合
  if (local.usingSlipInfo.riyourshaData.length === 0) {
    // 処理を終了する。
    return false
  }
  // 利用者情報.実績済みフラグ = 1の場合
  if (local.usingSlipInfo.riyourshaData[0].yoteiZumiFlg === Or36245Const.SCHED_FLG_OK) {
    await openConfirmDialog(t('message.i-cmn-10681'))
    // 処理終了
    return false
  }
  // 利用者情報.予定済みフラグ = 1の場合
  if (local.usingSlipInfo.riyourshaData[0].jissekiZumiFlg === Or36245Const.ACHV_FLG_OK) {
    await openConfirmDialog(t('message.i-cmn-10682'))
    // 処理終了
    return false
  }
  // 削除確認画面ダイアログを表示する
  const yearMonth = new Date(local.offeryYearMonth.value)
  // "利用票 " + 提供年月（YY年MM月）
  const messageArgs = `${t('label.using-slip')} ${yearMonth.getFullYear()}${Or36245Const.YEAR}${yearMonth.getMonth() + 1}${Or36245Const.MONTH}`
  const ret = await openConfirmDialog(t('message.i-cmn-11290', [messageArgs]), true)
  // はい
  if (ret === Or36245Const.CONFIRM_BTN_YES) {
    // シミュレーションの削除処理を実行する。
    const inputData: SimDeleteInEntity = {
      // 事業所ＩＤ：親画面.事業所ＩＤ
      defSvJigyoId: systemCommonsStore.getSvJigyoId ?? '',
      // 利用者ＩＤ：親画面.利用者ID
      userid: localOneway.or36245.userId,
      // 提供年月：画面.提供年月
      teikyouYm: local.offeryYearMonth.value,
    }
    await ScreenRepository.update('simDelete', inputData)
    // 再検索処理
    await useSlipInitInfoSelect()
  }
}

/**
 * 利用票明細一覧ソート
 *
 * @param sortItem -ソート項目
 */
const usingSlipSort = async (sortItem: string) => {
  // 利用票・別表のソート処理
  const inputData: UseSlipInfoSortSelectInEntity = {
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // ソート項目
    sortItem: sortItem,
  }
  const response: UseSlipInfoSortSelectOutEntity = await ScreenRepository.select(
    'useSlipInfoSortSelect',
    inputData
  )
  // 入力フォームの各項目へ設定する
  setUsingSlipInputForm(response.data.riyou1110Info[0].riyouList)
}

/**
 * 「提供時間(サービス開始時間)」変更入力
 *
 * @param index -index
 */
const offerHoursStartTimeChange = (index: number) => {
  if (local.usingSlipList[index].oyaLineNo !== Or36245Const.DEFAULT_PARENT_NO) {
    local.usingSlipList
      .filter((item) => item.oyaLineNo === local.usingSlipList[index].oyaLineNo)
      .forEach((item) => {
        item.dmyStartTime = local.usingSlipList[index].svStartTime.value
      })
  }
}

/**
 * 「提供時間(サービス終了時間)」変更入力
 *
 * @param index -index
 */
const offerHoursEndTimeChange = (index: number) => {
  if (local.usingSlipList[index].oyaLineNo !== Or36245Const.DEFAULT_PARENT_NO) {
    local.usingSlipList
      .filter((item) => item.oyaLineNo === local.usingSlipList[index].oyaLineNo)
      .forEach((item) => {
        item.dmyEndTime = local.usingSlipList[index].svEndTime.value
      })
  }
}

/**
 * 「予定貸与フラグ」変更
 *
 * @param index -index
 */
const schedCodeChange = async (index: number) => {
  const item = local.usingSlipList[index]
  item.schedChangeFlg = true
  let schedTotal = 1
  // 福祉用具貸与フラグ（予定） = 1：日割 or 3：30日割 or 4：31日割 の場合
  if (
    item.yRentalF.modelValue === Or36245Const.WELFARE_EQUIP_LENDING_1 ||
    item.yRentalF.modelValue === Or36245Const.WELFARE_EQUIP_LENDING_3 ||
    item.yRentalF.modelValue === Or36245Const.WELFARE_EQUIP_LENDING_4
  ) {
    const dayList = offeryYearMonthDayList.value.map((item) =>
      getSchedAchvDayName(Or36245Const.PREFIX_SCHED, item.day)
    )
    // 予定回数1～予定回数31の合計
    for (const key in item) {
      if (dayList.includes(key)) {
        const value = item[key as keyof typeof item] as TimeModelValue
        schedTotal += parseInt(value.value)
      }
    }
  }
  item.schedChangeValue = schedTotal
  // 利用票画面予実貸与フラグ変更後の再設定処理を行う
  const inputData: UseSlipInfoPredictionLendingChangeRowAftProcSelectInEntity = {
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 提供月（日）：画面退避情報.提供月（日）
    teikyouYmD: local.usingSlipInfo.teikyouYmD,
  }
  const response: UseSlipInfoPredictionLendingChangeRowAftProcSelectOutEntity =
    await ScreenRepository.select('useSlipInfoPredictionLendingChangeRowAftProcSelect', inputData)
  // 入力フォームの各項目へ設定する
  setUsingSlipInputForm(response.data.riyou897Info[0].riyouList)
}

/**
 * 「予定カレンダーアイコンボタン」押下
 *
 * @param index -index
 */
const schedCalendarHandler = async (index: number) => {
  // 利用者情報.実績済みフラグ = 1の場合
  if (local.usingSlipInfo.riyourshaData[0].yoteiZumiFlg === Or36245Const.SCHED_FLG_OK) {
    await openConfirmDialog(t('message.i-cmn-11368'))
    // 処理終了
    return false
  }
  // 利用者情報.予定済みフラグ = 1の場合
  if (local.usingSlipInfo.riyourshaData[0].jissekiZumiFlg === Or36245Const.ACHV_FLG_OK) {
    await openConfirmDialog(t('message.i-cmn-11369'))
    // 処理終了
    return false
  }
  // GUI01158_カレンダー入力画面をポップアップで起動する
  localOneway.or27016Oneway = {
    // 予定・実績
    scheduleOrReal: Or36245Const.SCHEDULE_OR_REAL,
    // 予定済
    scheduled: Or36245Const.SCHED_FLG_NG,
    // 提供年月
    provideYm: local.offeryYearMonth.value,
    // ロケーション
    location: localOneway.or36245.local,
    // カレント行
    currentRow: index.toString(),
    // 変更日
    updateDate: local.usingSlipInfo.teikyouYmD,
    // 日付配列
    days: [],
    // 選択状態配列
    sel: [],
    // 処理モード
    mode: '',
  }
  // Or27487のダイアログ開閉状態を更新する
  Or27016Logic.state.set({
    uniqueCpId: or27016.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「予定数」変更入力
 *
 * @param index -index
 *
 * @param day -day
 */
const schedDayChange = async (index: number, day: number) => {
  const selectedItem = local.usingSlipList[index]
  const proItem = getSchedAchvDayName(Or36245Const.PREFIX_SCHED, day)
  // 予定回数が""を入力する場合
  const schedDay = selectedItem[proItem as keyof typeof selectedItem] as TimeModelValue
  if (isEmpty(schedDay.value)) {
    schedDay.value = Or36245Const.DEFAULT_KAISU
  }
  // 回数入力変更前のチェック情報を取得する
  const inputData: UseSlipInfoNumberOfTimesModifiedBefCheckSelectInEntity = {
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 選択の行番号：選択の行番号
    lineNo: local.selectedUsingSlipItemIndex.toString(),
    // 選択の項目名：選択の項目名
    proItem: proItem,
    // 選択の項目の値：選択の項目の値
    proValue: schedDay.value,
    // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
    riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
  }
  const resp: UseSlipInfoNumberOfTimesModifiedBefCheckSelectOutEntity =
    await ScreenRepository.select('useSlipInfoNumberOfTimesModifiedBefCheckSelect', inputData)
  // 返却情報.メッセージ有無フラグ = 1の場合
  if (resp.data.riyou1109Info[0].messageFlag === Or36245Const.HAS_MESSAGE_FLG_1) {
    await openConfirmDialog(t('message.i-cmn-10073'))
    // 処理終了
    return false
  } else if (resp.data.riyou1109Info[0].messageFlag === Or36245Const.HAS_MESSAGE_FLG_2) {
    // 返却情報.メッセージ有無フラグ = 2の場合
    await openConfirmDialog(t('message.i-cmn-10064'))
    // 処理終了
    return false
  } else {
    // 返却情報.メッセージ有無フラグ <>1 and 返却情報.メッセージ有無フラグ <> 2
    // 処理対象 = 利用票明細情報で、親レコード番号 = 選択行の親レコード番号 and 合成識別区分 <> "3"のデータ、１件目が処理対象として（親サービスの中の１件目）
    const processTarget = local.usingSlipList.find(
      (item) =>
        item.oyaLineNo === selectedItem.oyaLineNo &&
        item.gouseiSikKbn === Or36245Const.GOUSEI_SIK_KBN_3
    )
    // 予定回数
    let targetSchedDay = ''
    if (processTarget !== undefined) {
      targetSchedDay = (processTarget[proItem as keyof typeof processTarget] as TimeModelValue)
        .value
    }
    // 加算サービスフラグ = true and 処理対象.予定回数 = 0の場合
    if (
      resp.data.riyou1109Info[0].kasanService === Or36245Const.KASAN_SERVICE_1 &&
      targetSchedDay === Or36245Const.DEFAULT_KAISU
    ) {
      await openConfirmDialog(t('message.i-cmn-10069'))
      // 処理終了
      return false
    }
    // 入力フォームの各項目へ設定
    setUsingSlipInputForm(resp.data.riyou1109Info[0].riyouList)
  }
}

/**
 * 「予定数」ダブルクリック
 *
 * @param index -index
 *
 * @param day -day
 *
 * @param event -Event
 */
const doubleSchedDayClick = async (index: number, day: number, event: Event) => {
  const proItem = getSchedAchvDayName(Or36245Const.PREFIX_SCHED, day)
  let rowNum = 0
  const selectedItem = local.usingSlipList[index]
  // 処理対象 = 利用票明細情報で、親レコード番号 = 選択行の親レコード番号 and 合成識別区分 <> "3"のデータ、１件目が処理対象として（親サービスの中の１件目）
  const processTarget = local.usingSlipList.find(
    (item) =>
      item.oyaLineNo === selectedItem.oyaLineNo &&
      item.gouseiSikKbn === Or36245Const.GOUSEI_SIK_KBN_3
  )
  // 選択の予定回数
  const schedDay = selectedItem[proItem as keyof typeof selectedItem] as TimeModelValue
  // 回数ダブルクリック変更前のチェック情報を取得する
  const inputData: UseSlipInfoNumberOfTimesModifiedBefSelectInEntity = {
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 選択の行番号：選択の行番号
    lineNo: local.selectedUsingSlipItemIndex.toString(),
    // 選択の項目名：選択の項目名
    proItem: proItem,
    // 選択の項目の値：選択の項目の値
    proValue: schedDay.value,
    // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
    riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
  }
  const resp: UseSlipInfoNumberOfTimesModifiedBefSelectOutEntity = await ScreenRepository.select(
    'useSlipInfoNumberOfTimesModifiedBefSelect',
    inputData
  )
  // 選択の予定回数 = 0 and 一時変数.行数 = 選択の行番号 and 返却情報.加算サービスフラグ = true
  if (
    schedDay.value === Or36245Const.DEFAULT_KAISU &&
    rowNum === index &&
    resp.data.riyou898Info[0].kasanService === Or36245Const.KASAN_SERVICE_1
  ) {
    let targetSchedDay = ''
    if (processTarget !== undefined) {
      targetSchedDay = (processTarget[proItem as keyof typeof processTarget] as TimeModelValue)
        .value
      // 一時変数.行数 = 処理対象の行番号
      rowNum = local.usingSlipList.findIndex((item) => item.scode === processTarget.scode)
    }
    // 処理対象.予定回数 = 0の場合
    if (targetSchedDay === '0') {
      await openConfirmDialog(t('message.i-cmn-10069'))
      // 処理終了
      return false
    }
  }
  // 選択の予定回数 = 0 and 一時変数.行数 > 0 and 選択行のサービス種別コード = "14" or "64" and 処理対象.サービスコード <> "00000000"の場合
  if (
    schedDay.value === Or36245Const.DEFAULT_KAISU &&
    rowNum > 0 &&
    (selectedItem.svtype === Or36245Const.SERVICE_TYPECODE_14 ||
      selectedItem.svtype === Or36245Const.SERVICE_TYPECODE_64) &&
    processTarget?.scode !== Or36245Const.SERVICE_CD_DEFAULT
  ) {
    // 選択行の予定回数 = 返却情報.提供時間より得た回数
    schedDay.value = resp.data.riyou898Info[0].kaisu
  }
  // 選択の予定回数 = 0 and 一時変数.行数 <= 0の場合
  if (schedDay.value === Or36245Const.DEFAULT_KAISU && rowNum <= 0) {
    // 画面.選択行の予定回数 = 1
    schedDay.value = Or36245Const.KAISU_1
  }
  // 選択の予定回数 <> 0の場合
  if (schedDay.value !== Or36245Const.DEFAULT_KAISU) {
    // 画面.選択行の予定回数 = 0
    schedDay.value = Or36245Const.DEFAULT_KAISU
  }
  // 返却情報.メッセージ有無フラグ = 1の場合
  if (resp.data.riyou898Info[0].messageFlag === Or36245Const.HAS_MESSAGE_FLG_1) {
    await openConfirmDialog(t('message.i-cmn-10073'))
    // 処理終了
    return false
  }
  // 入力フォームの各項目へ設定
  setUsingSlipInputForm(resp.data.riyou898Info[0].riyouList)
  event.stopPropagation()
}

/**
 * 親子関係にあるサービスのサービス事業者項目とサービス内容項目の背景色
 *
 * @param item -利用票明細情報
 */
const isParentRow = (item: UsingSlipDetailInfo) => {
  if (local.selectedUsingSlipItemIndex >= 0) {
    const selectedItem = local.usingSlipList[local.selectedUsingSlipItemIndex]
    if (selectedItem.oyaLineNo) {
      return (
        item.oyaLineNo !== Or36245Const.DEFAULT_PARENT_NO &&
        item.oyaLineNo === selectedItem.oyaLineNo
      )
    } else {
      return false
    }
  }
}

/**
 * 行複写処理
 *
 * @param processFlg -処理フラグ
 */
const lineCopy = async (processFlg: string) => {
  // 行複写処理を行う
  const inputData: UseSlipInfoDuplicateRowAftProcSelectInEntity = {
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 処理フラグ：一時変数.処理フラグ
    proFlag: processFlg,
    // 選択行インデックス：選択された利用票明細情報のインデックス
    selIdxList: local.selectedUsingSlipItemIndex.toString(),
  }
  const response: UseSlipInfoDuplicateRowAftProcSelectOutEntity = await ScreenRepository.select(
    'useSlipInfoDuplicateRowAftProcSelect',
    inputData
  )
  // 入力フォームの各項目へ設定する。
  setUsingSlipInputForm(response.data.riyou892Info[0].riyouList)
}

/**
 * 利用票別表行選択
 *
 * @param index - 選択した行のindex
 */
const selectUsingSlipOtherTableRow = (index: number) => {
  local.selectedUsingSlipOtherTableItemIndex = index
}

/**
 * 利用票別表合計
 *
 * @param key -合計フィールド
 */
const getUsingSlipOtherTableTotal = (key: string) => {
  let total = 0
  if (local.usingSlipOthrtTableList.length > 0) {
    for (const usingSlipOthrtTable of local.usingSlipOthrtTableList) {
      const value = usingSlipOthrtTable[key as keyof typeof usingSlipOthrtTable] as string
      total += parseInt(value)
    }
  }
  return total
}

/**
 * 単位数/割引後_率％/割引後_単位数/回数 表示/非表示
 *
 * @param item -別表明細情報
 */
const showTensuCellValue = (item: RiyouBeppyo) => {
  return (
    item.svType !== Or36245Const.SERVICE_TYPECODE_17 &&
    item.svType !== Or36245Const.SERVICE_TYPECODE_67 &&
    !item.svType.startsWith(Or36245Const.SERVICE_TYPECODE_PREFIX_3320) &&
    !item.svType.startsWith(Or36245Const.SERVICE_TYPECODE_PREFIX_3519)
  )
}

/**
 * 利用票別表 種類支給限度_超過 活性/非活性
 *
 * @param item -別表明細情報
 */
const sTensuOverDisabled = (item: RiyouBeppyo) => {
  let disabled = false
  // 利用者情報
  const riyourshaData = local.usingSlipInfo.riyourshaData[0]
  // 利用者情報.計算フラグ = nullの場合
  if (isEmpty(riyourshaData.rCompBase)) {
    // 利用者情報.サービス単位数（実績） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuJ) > parseInt(riyourshaData.dmyShikyuGaku)) {
      const sumData = local.usingSlipInfo.sumData[0]
      const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
      // 合計表示欄情報.種類限度基準超過 > 0の場合
      if (parseInt(sumData.hShuOver) > 0) {
        // 別表明細情報.合計フラグ = 1 AND
        // 別表明細情報.加算フラグ = 0 AND
        // 別表明細情報.30超フラグ = 0 AND
        // 種類支給限度_基準内の合計 >0の場合
        if (
          item.totalF === Or36245Const.TOTAL_FLG_TOTAL &&
          item.kasanF === Or36245Const.KASAN_FLG_USUALLY &&
          item.ov30Fl === Or36245Const.OV30_FLG_USUALLY &&
          parseInt(kbnShikyuuGendoData.cSTensuSum) > 0
        ) {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = false
        } else {
          // 明細.種類支給限度_超過は入力不可にする
          disabled = true
        }
      } else {
        // 合計表示欄情報.種類限度基準超過 <= 0の場合
        // 別表明細情報.合計フラグ = 1 AND
        // 別表明細情報.加算フラグ = 0 AND
        // サービス単位/金額 = 種類支給限度_超過 + 種類支給限度_基準内
        // 種類支給限度_基準内の合計 > 0 AND
        // 種類支給限度_超過の合計 <> 0の場合
        if (
          item.totalF === Or36245Const.TOTAL_FLG_TOTAL &&
          item.kasanF === Or36245Const.KASAN_FLG_USUALLY &&
          parseInt(kbnShikyuuGendoData.cSvTensuSum) ===
            parseInt(kbnShikyuuGendoData.cSTensuOverSum) +
              parseInt(kbnShikyuuGendoData.cSTensuSum) &&
          parseInt(kbnShikyuuGendoData.cSTensuSum) > 0 &&
          parseInt(kbnShikyuuGendoData.cSTensuOverSum) !== 0
        ) {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = true
        } else {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = false
        }
      }
    }
  } else {
    // 利用者情報.計算フラグ <> nullの場合
    // 利用者情報.サービス単位数（予定） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuY) > parseInt(riyourshaData.dmyShikyuGaku)) {
      // 明細.種類支給限度_超過が入力不可にする
      disabled = true
    }
  }
  return disabled
}

/**
 * 利用票別表 区分支給限度_超過 活性/非活性
 *
 * @param item -別表明細情報
 */
const kTensuOverDisabled = (item: RiyouBeppyo) => {
  let disabled = false
  // 利用者情報
  const riyourshaData = local.usingSlipInfo.riyourshaData[0]
  // 利用者情報.計算フラグ = nullの場合
  if (isEmpty(riyourshaData.rCompBase)) {
    // 利用者情報.サービス単位数（実績） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuJ) > parseInt(riyourshaData.dmyShikyuGaku)) {
      const sumData = local.usingSlipInfo.sumData[0]
      const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
      // 合計表示欄情報.超過_区分支給限度 > 0の場合
      if (parseInt(sumData.kbnOverSum) > 0) {
        // 別表明細情報.合計フラグ = 1 AND
        // 別表明細情報.加算フラグ = 0 AND
        // 別表明細情報.30超フラグ = 0の場合
        if (
          item.totalF === Or36245Const.TOTAL_FLG_TOTAL &&
          item.kasanF === Or36245Const.KASAN_FLG_USUALLY &&
          item.ov30Fl === Or36245Const.OV30_FLG_USUALLY
        ) {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = false
        } else {
          // 明細.種類支給限度_超過は入力不可にする
          disabled = true
        }
      } else {
        // 合計表示欄情報.超過_区分支給限度 <= 0の場合
        // 別表明細情報.合計フラグ = 1 AND
        // 別表明細情報.加算フラグ = 0 AND
        // 区分支給限度_超過の合計 <> 0の場合
        if (
          item.totalF === Or36245Const.TOTAL_FLG_TOTAL &&
          item.kasanF === Or36245Const.KASAN_FLG_USUALLY &&
          parseInt(kbnShikyuuGendoData.cKTensuOverSum) !== 0
        ) {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = true
        } else {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = false
        }
      }
    }
  } else {
    // 利用者情報.計算フラグ <> nullの場合
    // 利用者情報.サービス単位数（予定） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuY) > parseInt(riyourshaData.dmyShikyuGaku)) {
      // 明細.種類支給限度_超過が入力不可にする
      disabled = true
    }
  }
  return disabled
}

/**
 * 単位数単価 表示/非表示
 *
 * @param item -別表明細情報
 */
const showTanka = (item: RiyouBeppyo) => {
  let showFlg = true
  //   別表明細情報.加算フラグ = 1（合計行）の場合
  if (item.totalF === Or36245Const.TOTAL_FLG_TOTAL) {
    // 別表明細情報.給付率差異フラグ=1 or 別表明細情報.給付率差異フラグ=3
    if (
      item.kyufuDiffF === Or36245Const.KYUFU_DIFF_FLG_1 ||
      item.kyufuDiffF === Or36245Const.KYUFU_DIFF_FLG_3
    ) {
      // 明細.単位数単価が表示にする
      showFlg = true
    } else {
      // 上記以外の場合
      // 明細.単位数単価が非表示にする
      showFlg = false
    }
  } else {
    // 別表明細情報.加算フラグ <> 1（合計行）の場合
    // 明細.単位数単価が非表示にする
    showFlg = false
  }
  return showFlg
}

/**
 * 給付率(％) 表示/非表示
 *
 * @param item -別表明細情報
 */
const showKyufuRitu = (item: RiyouBeppyo) => {
  let showFlg = true
  // 別表明細情報.総合サービス区分 <> 2の場合
  if (item.cSougouKbn !== Or36245Const.SOUGOU_KBN_2) {
    // 別表明細情報.合計フラグ = 1の場合
    if (item.totalF === Or36245Const.TOTAL_FLG_TOTAL) {
      // 別表明細情報.給付率差異フラグ = 1の場合
      if (item.kyufuDiffF === Or36245Const.KYUFU_DIFF_FLG_1) {
        // 明細.給付率(％)が非表示にする
        showFlg = false
      } else {
        // 別表明細情報.給付率差異フラグ <> 1の場合
        // 明細.給付率(％)が表示にする
        showFlg = true
      }
    } else {
      // 別表明細情報.合計フラグ <> 1の場合
      // 別表明細情報.給付率差異フラグ = 0 or 別表明細情報.給付率差異フラグ = 2の場合
      if (
        item.kyufuDiffF === Or36245Const.KYUFU_DIFF_FLG_0 ||
        item.kyufuDiffF === Or36245Const.KYUFU_DIFF_FLG_2
      ) {
        // 明細.給付率(％)が非表示にする
        showFlg = false
      } else {
        // 別表明細情報.給付率差異フラグ <> 0 and 別表明細情報.給付率差異フラグ <> 2の場合
        // 明細.給付率(％)が表示にする
        showFlg = true
      }
    }
  } else {
    // 別表明細情報.総合サービス区分 = 2の場合
    // 明細.給付率(％)が非表示にする
    showFlg = false
  }
  return showFlg
}

/**
 * 保険/事業費請求額 表示/非表示
 *
 * @param item -別表明細情報
 */
const showHKyufugaku = (item: RiyouBeppyo) => {
  let showFlg = true
  // 別表明細情報.合計フラグ = 1の場合
  if (item.totalF === Or36245Const.TOTAL_FLG_TOTAL) {
    // 明細.保険/事業費請求額が表示にする
    showFlg = true
  } else {
    // 利用票別表の初期情報.別表明細情報.合計フラグ <> 1の場合
    // 別表明細情報.給付率差異フラグ = 1の場合
    if (item.kyufuDiffF === Or36245Const.KYUFU_DIFF_FLG_1) {
      //   明細.保険/事業費請求額が表示にする
      showFlg = true
    } else {
      // 別表明細情報.給付率差異フラグ <> 1の場合
      // 明細.保険/事業費請求額が非表示にする
      showFlg = false
    }
  }
  return showFlg
}

/**
 * 利用者負担_保険/事業 表示/非表示
 *
 * @param item -別表明細情報
 */
const showHutanH = (item: RiyouBeppyo) => {
  let showFlg = true
  // 別表明細情報.合計フラグ = 1の場合
  if (item.totalF === Or36245Const.TOTAL_FLG_TOTAL) {
    showFlg = true
    // 明細.利用者負担_保険/事業が表示にする
  } else {
    // 別表明細情報.合計フラグ <> 1の場合
    // 別表明細情報.総合サービス区分 = 2の場合
    if (item.cSougouKbn === Or36245Const.SOUGOU_KBN_2) {
      // 別表明細情報.給付率差異フラグ = 2の場合
      if (item.kyufuDiffF === Or36245Const.KYUFU_DIFF_FLG_2) {
        // 明細.利用者負担_保険/事業が非表示にする
        showFlg = false
      } else {
        // 別表明細情報.給付率差異フラグ  <> 2の場合
        // 明細.利用者負担_保険/事業が表示にする
        showFlg = true
      }
    } else {
      // 別表明細情報.総合サービス区分 <> 2の場合
      //  明細.利用者負担_保険/事業が非表示にする
      showFlg = false
    }
  }
  return showFlg
}

/**
 * 種類支給限度/区分支給限度 超過 変更入力
 *
 * @param index -index
 *
 * @param tensuOverFlg -超過フラグ
 */
const tensuOverChanged = async (index: number, tensuOverFlg: string) => {
  const selectedItem = local.usingSlipOthrtTableList[index]
  // 種類支給限度_超過
  if (tensuOverFlg === Or36245Const.TENSU_OVER_FLG_S) {
    const sTensuOver = parseInt(selectedItem.sTensuOver.value)
    if (sTensuOver < 0) {
      await openConfirmDialog(t('message.i-cmn-10509'))
      selectedItem.sTensuOver.value = local.usingSlipInfo.riyouBeppyoList[index].sTensuOver
      // 処理終了
      return false
    }
  } else {
    const kTensuOver = parseInt(selectedItem.kTensuOver.value)
    if (kTensuOver < 0) {
      await openConfirmDialog(t('message.i-cmn-10509'))
      selectedItem.kTensuOver.value = local.usingSlipInfo.riyouBeppyoList[index].kTensuOver
      // 処理終了
      return false
    }
  }
  // 利用票・別表の別表明細入力後の再計算処理を行う
  const inputData: UseSlipOtherDtlInfoRecalculationSelectInEntity = {
    lineNo: index.toString(),
    proItem: tensuOverFlg,
    riyouBeppyoList: getRiyouBeppyoList(),
    kbnShikyuuGendoData: local.usingSlipInfo.kbnShikyuuGendoData,
  }
  const response: UseSlipOtherDtlInfoRecalculationSelectOutEntity = await ScreenRepository.select(
    'useSlipOtherDtlInfoRecalculationSelect',
    inputData
  )
  // 返却情報.再計算フラグ = 1の場合
  if (response.data.calcFlag === Or36245Const.CALC_FLAG_1) {
    // 再計算処理、画面を再表示する。
    await recompute()
  }
  // 新規計算フラグを「false」に設定する
  newComputeFlg.value = false
}

/**
 * 累積_短期入所利用日数の色
 *
 * @param item -合計表示欄情報
 */
const getComputeRuikeiSumColor = (item: SumData) => {
  let color = Or36245Const.COLOR_DEFAULT
  const jissekiDays = parseInt(local.usingSlipInfo.riyourshaData[0].jissekiDays)
  const computeRuikeiSum = parseInt(item.computeRuikeiSum.replace(',', ''))
  // 合計表示欄情報.累積_短期入所利用日数 > 実績日数 AND 実績日数 > 0の場合
  if (computeRuikeiSum > jissekiDays && jissekiDays > 0) {
    // 画面項目.合計表示欄の累積の色は「FF0000」を設定
    color = Or36245Const.COLOR_RED
  }
  return color
}

/**
 * 「短期入所利用日数ボタン」押下
 */
const shortTermadmissionUsingDay = () => {
  if (!hasRegist) {
    return false
  }
  localOneway.or27856Oneway = {
    shienId: local.usingSlipInfo.riyourshaData[0].shienId,
    userId: local.usingSlipInfo.riyourshaData[0].userid,
    upToPrevMonth: local.usingSlipInfo.sumData[0].zenGetuSum,
    planForThisMonth: local.usingSlipInfo.sumData[0].cmnTucPlanTTougetu,
    accumulation: local.usingSlipInfo.sumData[0].computeRuikeiSum,
    // 月またぎ前月分日数
    teiYm: local.offeryYearMonth.value,
    teiYmD: local.usingSlipInfo.teikyouYmD,
    hokenNo: local.usingSlipInfo.riyourshaData[0].cmnTucPlanHHokenNoHidden,
    hokenCd: local.usingSlipInfo.riyourshaData[0].cmnTucPlanKHokenCdHidden,
    dmyKikanFr: local.usingSlipInfo.riyourshaData[0].dmyKikanFr,
    screenKbn: Or36245Const.SCREEN_KBN,
  } as Or27856SelectInfoType
  // GUI01162_認定期間中の短期入所利用日数画面をポップアップで起動する
  Or27856Logic.state.set({
    uniqueCpId: or27856.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 画面利用票明細情報取得
 */
const getRiyouList = () => {
  const riyouList = [] as Riyou[]
  for (const usingSlip of local.usingSlipList) {
    const riyou = {
      ...usingSlip,
      yoteiZumiFlg: usingSlip.yoteiZumiFlg ? Or36245Const.SCHED_FLG_OK : Or36245Const.SCHED_FLG_NG,
      jissekiZumiFlg: usingSlip.jissekiZumiFlg.modelValue
        ? Or36245Const.ACHV_FLG_OK
        : Or36245Const.ACHV_FLG_NG,
      svStartTime: usingSlip.svStartTime.value,
      svEndTime: usingSlip.svEndTime.value,
      yRentalF: usingSlip.yRentalF.modelValue,
      jRentalF: usingSlip.jRentalF.modelValue,
      yDay01: usingSlip.yDay01.value,
      yDay02: usingSlip.yDay02.value,
      yDay03: usingSlip.yDay03.value,
      yDay04: usingSlip.yDay04.value,
      yDay05: usingSlip.yDay05.value,
      yDay06: usingSlip.yDay06.value,
      yDay07: usingSlip.yDay07.value,
      yDay08: usingSlip.yDay08.value,
      yDay09: usingSlip.yDay09.value,
      yDay10: usingSlip.yDay10.value,
      yDay11: usingSlip.yDay11.value,
      yDay12: usingSlip.yDay12.value,
      yDay13: usingSlip.yDay13.value,
      yDay14: usingSlip.yDay14.value,
      yDay15: usingSlip.yDay15.value,
      yDay16: usingSlip.yDay16.value,
      yDay17: usingSlip.yDay17.value,
      yDay18: usingSlip.yDay18.value,
      yDay19: usingSlip.yDay19.value,
      yDay20: usingSlip.yDay20.value,
      yDay21: usingSlip.yDay21.value,
      yDay22: usingSlip.yDay22.value,
      yDay23: usingSlip.yDay23.value,
      yDay24: usingSlip.yDay24.value,
      yDay25: usingSlip.yDay25.value,
      yDay26: usingSlip.yDay26.value,
      yDay27: usingSlip.yDay27.value,
      yDay28: usingSlip.yDay28.value,
      yDay29: usingSlip.yDay29.value,
      yDay30: usingSlip.yDay30.value,
      yDay31: usingSlip.yDay31.value,
      jDay01: usingSlip.jDay01.value,
      jDay02: usingSlip.jDay02.value,
      jDay03: usingSlip.jDay03.value,
      jDay04: usingSlip.jDay04.value,
      jDay05: usingSlip.jDay05.value,
      jDay06: usingSlip.jDay06.value,
      jDay07: usingSlip.jDay07.value,
      jDay08: usingSlip.jDay08.value,
      jDay09: usingSlip.jDay09.value,
      jDay10: usingSlip.jDay10.value,
      jDay11: usingSlip.jDay11.value,
      jDay12: usingSlip.jDay12.value,
      jDay13: usingSlip.jDay13.value,
      jDay14: usingSlip.jDay14.value,
      jDay15: usingSlip.jDay15.value,
      jDay16: usingSlip.jDay16.value,
      jDay17: usingSlip.jDay17.value,
      jDay18: usingSlip.jDay18.value,
      jDay19: usingSlip.jDay19.value,
      jDay20: usingSlip.jDay20.value,
      jDay21: usingSlip.jDay21.value,
      jDay22: usingSlip.jDay22.value,
      jDay23: usingSlip.jDay23.value,
      jDay24: usingSlip.jDay24.value,
      jDay25: usingSlip.jDay25.value,
      jDay26: usingSlip.jDay26.value,
      jDay27: usingSlip.jDay27.value,
      jDay28: usingSlip.jDay28.value,
      jDay29: usingSlip.jDay29.value,
      jDay30: usingSlip.jDay30.value,
      jDay31: usingSlip.jDay31.value,
      yTotal: usingSlip.yTotal.modelValue,
    }
    riyouList.push(riyou)
  }
  return riyouList
}

/**
 * 画面別表明細情報取得
 */
const getRiyouBeppyoList = () => {
  const riyouBeppyoList = [] as RiyouBeppyo[]
  for (const usingSlipOthrtTable of local.usingSlipOthrtTableList) {
    const riyouBeppyo = {
      ...usingSlipOthrtTable,
      sTensuOver: usingSlipOthrtTable.sTensuOver.value,
      kTensuOver: usingSlipOthrtTable.kTensuOver.value,
    }
    riyouBeppyoList.push(riyouBeppyo)
  }
  return riyouBeppyoList
}

/**
 * 金額ディスプレイ
 *
 * @param amount -金額
 */
const getDisplayAmount = (amount: string) => {
  let displayAmount = ''
  if (!isEmpty(amount)) {
    const amountNum = parseInt(amount)
    if (!isNaN(amountNum)) {
      displayAmount = amountNum.toLocaleString()
    }
  }
  return displayAmount
}

/**
 * 予定/実績回数name取得
 *
 * @param prefix -予定/実績
 *
 * @param day -day
 */
const getSchedAchvDayName = (prefix: string, day: number) => {
  return prefix + day.toString().padStart(2, Or36245Const.PREFIX_ZERO)
}

/**
 * 年月選択
 */
const openCalendar = () => {
  // 年月選択画面をポップアップで起動する。
  Gui00048Logic.state.set({
    uniqueCpId: gui00048.value.uniqueCpId,
    state: {
      isOpen: true, // 開閉フラグ
      startDate: local.offeryYearMonth.value,
    },
  })
}

/**
 * 利用票明細一覧ヘーダの背景色
 *
 * @param day -day
 */
const getHeadBgColor = (day: DayOfWeek) => {
  if (local.selectedColumnIndex === day.day) {
    return Or36245Const.COLOR_SELECTED_COL
  } else {
    return local.usingSlipInfo.weekBgColor[day.day - 1]
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param dialogText -ダイアログテキスト
 *
 * @param isChoose - ダイアログ種類
 */
const openConfirmDialog = (dialogText: string, isChoose = false) => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.confirm'),
      dialogText: dialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: isChoose ? t('btn.yes') : t('btn.ok'),
      secondBtnType: isChoose ? 'normal3' : 'blank',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        let result = 'no' as 'yes' | 'cancel'
        if (event?.firstBtnClickFlg) {
          result = Or36245Const.CONFIRM_BTN_YES
        }
        if (event?.thirdBtnClickFlg) {
          result = Or36245Const.CONFIRM_BTN_CANCEL
        }
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログをオープンする
 *
 * @param dialogText - ダイアログテキスト
 */
const openWarningDialog = (dialogText: string) => {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: dialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        resolve(Or36245Const.CONFIRM_BTN_YES)
      }
    )
  })
}

/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
const isEditNavControl = (components: string[]) => {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()
  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }
  return false
}
</script>

<template>
  <c-v-sheet class="view pl-2 pr-2">
    <c-v-row
      no-gutters
      class="pa-2"
    >
      <!-- 利用票 画面タイトル -->
      <c-v-col class="title-area">
        <c-v-row
          align-center
          justify-start
          no-gutters
        >
          <h1 class="pl-3">
            {{ t('label.simulation') }}
          </h1>
          <!-- Or21828：有機体：お気に入りアイコンボタン -->
          <g-base-or21828
            v-bind="or21828"
            class="px-2"
          />
        </c-v-row>
      </c-v-col>
      <c-v-col cols="auto">
        <!-- Or11871：有機体：画面メニューエリア -->
        <g-base-or11871
          v-bind="or11871"
          class="top-menu"
        >
          <template #customButtons>
            <div class="d-flex">
              <!-- 新規 -->
              <base-mo00611
                :oneway-model-value="localOneway.mo00611AddNewOneway"
                @click="usingSlipAddNew"
              />
              <c-v-divider
                vertical
                class="ma-2"
              />
              <!-- 再計算 -->
              <base-mo00611
                :oneway-model-value="localOneway.mo00611RecomputeOneway"
                @click="recompute"
              />
            </div>
          </template>
          <!-- マスタ設定 -->
          <template #optionMasterItems>
            <!-- 有効期間外サービス -->
            <c-v-list-item
              :title="t('label.valid-period-other-than-service')"
              @click="validPeriodOtherThanService"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.valid-period-other-than-service')"
              />
            </c-v-list-item>
          </template>
          <template #optionMenuItems>
            <!-- 雛形選択 -->
            <c-v-list-item
              :disabled="!hasRegist"
              :title="t('label.prototype-select')"
              @click="prototypeSelect"
            >
              <template #prepend>
                <base-at-icon
                  icon="folder fill"
                  style="opacity: 0"
                />
              </template>
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.prototype-select')"
              />
            </c-v-list-item>
            <c-v-divider />
            <!-- 削除 -->
            <c-v-list-item
              :disabled="!hasRegist"
              :title="t('btn.delete')"
              prepend-icon="delete"
              @click="usingSlipDelete"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('btn.delete')"
              />
            </c-v-list-item>
          </template>
        </g-base-or11871>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="content-area pl-4"
    >
      <c-v-col class="hidden-scroll h-100">
        <c-v-sheet class="content">
          <c-v-container style="max-width: unset; padding: 0">
            <c-v-row class="ma-0 mt-1">
              <c-v-col
                cols="auto"
                class="pa-0"
              >
                <!-- 事業所 -->
                <g-base-or-41179
                  v-bind="or41179"
                  class="office-dropdown"
                />
              </c-v-col>
              <c-v-col
                cols="auto"
                class="pa-0 ml-4"
              >
                <!-- 提供年月 -->
                <g-custom-or-x0165
                  :oneway-model-value="localOneway.orX0165Plan"
                  class="offer-year-month"
                  @on-click-edit-btn="openCalendar"
                  @on-click-pre-btn="addMonth(false)"
                  @on-click-post-btn="addMonth(true)"
                />
              </c-v-col>
              <c-v-col
                cols="auto"
                class="pa-0 pl-6"
              >
                <!-- 介護度 -->
                <base-mo00040
                  v-model="local.levelOfCare"
                  :oneway-model-value="localOneway.mo00040LevelOfCareOneway"
                  class="level-of-care"
                  style="background-color: #ffffff"
                />
              </c-v-col>
              <c-v-col
                cols="auto"
                class="pa-0 pl-6"
              >
                <!-- 区分支給限度額 -->
                <base-mo00038
                  v-model="local.categoryPaymentLimitAmount"
                  :oneway-model-value="localOneway.m00038Oneway"
                  style="background-color: #ffffff"
                />
              </c-v-col>
              <c-v-col
                cols="auto"
                class="pa-0 pl-4"
              >
                <!-- タイトル -->
                <base-mo00045
                  v-model="local.title"
                  :oneway-model-value="localOneway.mo00045TitleOneway"
                  style="background-color: #ffffff"
                />
              </c-v-col>
              <c-v-col
                cols="auto"
                class="pa-0 pl-4"
              >
                <!-- 作成日 -->
                <base-mo00020
                  :model-value="local.createDate"
                  :oneway-model-value="localOneway.mo00020YmdOneway"
                  style="background-color: #ffffff"
                />
              </c-v-col>
              <c-v-col
                cols="auto"
                class="pa-0 pl-6"
              >
                <!-- 給付率 -->
                <base-mo00038
                  v-model="local.paymentRate"
                  :oneway-model-value="localOneway.m00038PaymentRateOneway"
                  style="background-color: #ffffff"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row no-gutters>
              <base-mo00043
                v-model="local.mo00043"
                :oneway-model-value="localOneway.mo00043Oneway"
                class="mt-5 w-100"
                style="padding: 0 !important"
              >
              </base-mo00043>
            </c-v-row>
            <c-v-row
              no-gutters
              class="mt-6"
            >
              <c-v-col>
                <c-v-window v-model="local.mo00043.id">
                  <!-- 利用票 -->
                  <c-v-window-item :value="Or36245Const.TAB_ID_USING_SLIP">
                    <c-v-row>
                      <c-v-col cols="auto">
                        <c-v-row
                          class="ma-0"
                          style="display: block"
                        >
                          <div class="flex-col">
                            <div class="line3-flex-box">
                              <!-- 行追加ボタン: Or21735 -->
                              <div>
                                <g-base-or-21735 v-bind="or21735" />
                                <c-v-tooltip
                                  activator="parent"
                                  location="bottom"
                                  :text="$t('tooltip.care-plan2-newline-btn')"
                                ></c-v-tooltip>
                              </div>
                              <!-- 行複写ボタン: Or21737 -->
                              <div>
                                <g-base-or-21737 v-bind="or21737" />
                                <c-v-tooltip
                                  activator="parent"
                                  location="bottom"
                                  :text="$t('tooltip.care-plan2-cpyline-btn')"
                                />
                              </div>
                              <!-- 行削除ボタン: Or21738 -->
                              <div>
                                <g-base-or-21738 v-bind="or21738" />
                                <c-v-tooltip
                                  activator="parent"
                                  location="bottom"
                                  :text="$t('tooltip.care-plan2-deleteline-btn')"
                                />
                              </div>
                              <div style="margin-left: auto">
                                <!-- 修正 -->
                                <base-mo00611
                                  :oneway-model-value="localOneway.mo00611Oneway"
                                  @click="editLine"
                                />
                              </div>
                            </div>
                          </div>
                          <!-- 利用票明細一覧 -->
                          <c-v-data-table
                            v-resizable-grid="{ columnWidths: useSlipTableColumnWidth }"
                            class="table-header overflow-y-auto mt-4 table-wrapper tbl-using-slip"
                            hide-default-footer
                            fixed-header
                            :items-per-page="-1"
                            :items="local.usingSlipList"
                            return-object
                            hide-no-data
                          >
                            <template #headers>
                              <tr>
                                <!-- サービス事業者 -->
                                <th
                                  rowspan="2"
                                  class="pl-2 pr-2"
                                  style="background-color: #dbeefe !important"
                                  @click="usingSlipSort(Or36245Const.SORT_COL_DMY_JIGYO_NAME_KNJ)"
                                >
                                  {{ t('label.service-office-table') }}
                                </th>
                                <!-- サービス内容 -->
                                <th
                                  rowspan="2"
                                  class="pl-2 pr-2"
                                  style="background-color: #dbeefe !important"
                                >
                                  {{ t('label.service-type-contents') }}
                                </th>
                                <!-- 提供時間 -->
                                <th
                                  rowspan="2"
                                  class="pa-0 text-center"
                                  style="background-color: #dbeefe !important"
                                  @click="usingSlipSort(Or36245Const.SORT_COL_SV_START_TIME)"
                                >
                                  {{ t('label.offer') }}<br />{{ t('label.hours') }}
                                </th>
                                <!-- 日 -->
                                <th
                                  class="pl-2 pr-2 text-center"
                                  style="background-color: #dbeefe !important"
                                >
                                  {{ t('label.day-short-sunday') }}
                                </th>
                                <!-- 1~31ラベル -->
                                <th
                                  v-for="day in offeryYearMonthDayList"
                                  :key="day.day"
                                  class="pa-0 text-center"
                                  :style="[
                                    'color:' + day.color + '!important',
                                    'background:' + getHeadBgColor(day) + '!important',
                                  ]"
                                >
                                  {{ day.day }}
                                </th>
                                <!-- 合計 -->
                                <th
                                  rowspan="2"
                                  class="pl-2 pr-2 text-center"
                                  style="text-align: center; background-color: #ffffff !important"
                                >
                                  {{ t('label.total') }}
                                </th>
                                <!-- 予定金額総合計 -->
                                <th
                                  class="pl-2 pr-2 text-right"
                                  style="background-color: #ffffff !important"
                                >
                                  {{ local.usingSlipInfo.yoteiAllSum }}
                                </th>
                              </tr>
                              <tr>
                                <!-- 曜 -->
                                <th
                                  class="pl-2 pr-2 text-center"
                                  style="
                                    background-color: #dbeefe !important;
                                    border-top: none !important;
                                  "
                                >
                                  {{ t('label.day-short-week') }}
                                </th>
                                <!-- 日~火ラベル -->
                                <th
                                  v-for="day in offeryYearMonthDayList"
                                  :key="day.day"
                                  class="pa-0 text-center"
                                  style="border-top: none !important"
                                  :style="[
                                    'color:' + day.color + '!important',
                                    'background:' + getHeadBgColor(day) + '!important',
                                  ]"
                                >
                                  {{ day.dayOfWeek }}
                                </th>
                                <th
                                  class="pl-2 pr-2"
                                  style="
                                    background-color: #ffffff !important;
                                    border-top: none !important;
                                  "
                                ></th>
                              </tr>
                            </template>
                            <template #item="{ item, index }">
                              <tr
                                :class="{
                                  'select-row': local.selectedUsingSlipItemIndex === index,
                                  hoverd: hoverdIndex === index,
                                }"
                                @click="selectUsingSlipRow(index)"
                                @mouseover="hoverdIndex = index"
                                @mouseout="hoverdIndex = -1"
                              >
                                <td
                                  :class="['pa-2', isParentRow(item) ? 'select-row' : '']"
                                  :style="[
                                    index === local.usingSlipList.length - 1
                                      ? 'border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important'
                                      : '',
                                  ]"
                                  rowspan="2"
                                  @dblclick="editLine"
                                >
                                  {{ item.dmyJigyoNameKnj }}
                                </td>
                                <td
                                  :class="['pa-2', isParentRow(item) ? 'select-row' : '']"
                                  :style="[
                                    index === local.usingSlipList.length - 1
                                      ? 'border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important'
                                      : '',
                                  ]"
                                  rowspan="2"
                                  @dblclick="editLine"
                                >
                                  {{ item.dmyFormalnameKnj }}
                                </td>
                                <td style="padding: 0 !important">
                                  <base-mo01274
                                    v-if="showOfferHoursTime(item.svStartTime.value)"
                                    v-model="item.svStartTime"
                                    :disabled="
                                      item.yoteiZumiFlg.modelValue || item.jissekiZumiFlg.modelValue
                                    "
                                    :oneway-model-value="localOneway.mo01274Oneway"
                                    class="offer-hour-time"
                                    style="outline: none !important; text-align: center"
                                    @keydown="handleKeydown"
                                    @change="offerHoursStartTimeChange(index)"
                                  />
                                  <base-mo01282
                                    v-if="showOfferHoursDropdown(item)"
                                    v-model="item.yRentalF"
                                    :oneway-model-value="localOneway.mo01282Oneway"
                                    :disabled="
                                      item.yoteiZumiFlg.modelValue || item.jissekiZumiFlg.modelValue
                                    "
                                    style="outline: none !important; text-align: center"
                                    @change="schedCodeChange(index)"
                                  />
                                </td>
                                <td class="pa-0 text-center">
                                  <base-mo00009
                                    v-if="false"
                                    :oneway-model-value="localOneway.mo00009CalendarOneway"
                                    style="opacity: var(--v-medium-emphasis-opacity)"
                                    @click="schedCalendarHandler(index)"
                                  />
                                </td>
                                <td
                                  v-for="day in offeryYearMonthDayList"
                                  :key="day.day"
                                  style="padding: 0 !important"
                                >
                                  <base-mo01278
                                    v-model="
                                      item[getSchedAchvDayName(Or36245Const.PREFIX_SCHED, day.day)]
                                    "
                                    :oneway-model-value="localOneway.mo01278Oneway"
                                    :style="[
                                      'padding: 0px !important',
                                      'text-align: center !important',
                                      'border: none',
                                      'border-radius: 0',
                                      'outline: none !important',
                                      'background:' +
                                        getDayBgColor(index, day, Or36245Const.SCHED_FLG_OK),
                                    ]"
                                    :disabled="item.yoteiZumiFlg.modelValue"
                                    @focus="selectColumn(day.day)"
                                    @blur="cleanSelectedColumn"
                                    @update:model-value="schedDayChange(index, day.day)"
                                    @dblclick="doubleSchedDayClick(index, day.day, $event)"
                                  />
                                </td>
                                <td style="padding: 0 !important">
                                  <div
                                    v-if="!showSchedTotalDropdown(item)"
                                    class="pl-2 pr-2 text-right"
                                  >
                                    {{ getSchedTotal(item) }}
                                  </div>
                                  <base-mo01282
                                    v-if="showSchedTotalDropdown(item)"
                                    v-model="item.yTotal"
                                    :oneway-model-value="localOneway.mo01282DropdownOneway"
                                    @change="schedCodeChange(index)"
                                  />
                                </td>
                                <td class="pl-2 pr-2 text-right">
                                  {{ getSchedAmountTotal(item) }}
                                </td>
                              </tr>
                              <tr
                                :class="{
                                  'select-row': local.selectedUsingSlipItemIndex === index,
                                  hoverd: hoverdIndex === index,
                                }"
                                @click="selectUsingSlipRow(index)"
                                @mouseover="hoverdIndex = index"
                                @mouseout="hoverdIndex = -1"
                              >
                                <td
                                  style="
                                    padding: 0 !important;
                                    border-top: 1px dashed rgb(var(--v-theme-black-200)) !important;
                                  "
                                >
                                  <base-mo01274
                                    v-if="showOfferHoursTime(item.svEndTime.value)"
                                    v-model="item.svEndTime"
                                    :oneway-model-value="localOneway.mo01274Oneway"
                                    :disabled="
                                      item.yoteiZumiFlg.modelValue || item.jissekiZumiFlg.modelValue
                                    "
                                    class="offer-hour-time"
                                    style="outline: none !important; text-align: center"
                                    @keydown="handleKeydown"
                                    @change="offerHoursEndTimeChange(index)"
                                  />
                                </td>
                                <td
                                  class="pa-0"
                                  style="
                                    border-top: 1px dashed rgb(var(--v-theme-black-200)) !important;
                                  "
                                ></td>
                                <td
                                  v-for="day in offeryYearMonthDayList"
                                  :key="day.day"
                                  style="
                                    padding: 0 !important;
                                    border-top: 1px dashed rgb(var(--v-theme-black-200)) !important;
                                  "
                                >
                                  <base-mo01278
                                    :model-value="{ value: '' }"
                                    :oneway-model-value="localOneway.mo01278Oneway"
                                    :style="[
                                      'padding: 0px !important',
                                      'text-align: center !important',
                                      'border: none',
                                      'border-radius: 0',
                                      'outline: none !important',
                                      'background:' +
                                        getDayBgColor(index, day, Or36245Const.ACHV_FLG_OK),
                                    ]"
                                    disabled
                                  />
                                </td>
                                <td
                                  class="pl-2 pr-2"
                                  style="
                                    border-top: 1px dashed rgb(var(--v-theme-black-200)) !important;
                                  "
                                ></td>
                                <td
                                  class="pl-2 pr-2"
                                  style="
                                    border-top: 1px dashed rgb(var(--v-theme-black-200)) !important;
                                  "
                                ></td>
                              </tr>
                            </template>
                          </c-v-data-table>
                        </c-v-row>
                      </c-v-col>
                    </c-v-row>
                    <c-v-divider class="my-6" />
                  </c-v-window-item>
                  <!-- 利用票別表 -->
                  <c-v-window-item :value="Or36245Const.TAB_ID_USING_SLIP_OTHER_TABLE">
                    <c-v-row no-gutters>
                      <c-v-col>
                        <!-- 利用票別表一覧 -->
                        <c-v-data-table
                          v-resizable-grid="{ columnWidths: useSlipOtherTableColumnWidth }"
                          class="overflow-y-auto w-100 h-100 table-wrapper tbl-using-slip-other-table"
                          fixed-header
                          fixed-footer
                          hide-default-footer
                          :items-per-page="-1"
                          :items="local.usingSlipOthrtTableList"
                          hover
                          hide-no-data
                        >
                          <template #headers>
                            <tr>
                              <!-- 事業所名 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 120px"
                              >
                                {{ t('label.office-name') }}
                              </th>
                              <!-- 事業所番号 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 98px"
                              >
                                {{ t('label.office-number') }}
                              </th>
                              <!-- サービス内容/種類 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 171px"
                              >
                                {{ t('label.service-contents') }}<br />/{{ t('label.type') }}
                              </th>
                              <!-- ｻｰﾋﾞｽｺｰﾄ -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 82px"
                              >
                                {{ t('label.service') }}<br />
                                {{ t('label.code') }}
                              </th>
                              <!-- 福祉用具貸与の場合のみ -->
                              <th
                                v-if="showOnlyWelfareEquipment"
                                colspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 235px; background-color: #dbeefe !important"
                              >
                                {{ t('label.only-welfare-equipment') }}
                              </th>
                              <!-- 単位数 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 65px"
                              >
                                {{ t('label.unit-number') }}
                              </th>
                              <!-- 割引後 -->
                              <th
                                colspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 126px"
                              >
                                {{ t('label.discount-after') }}
                              </th>
                              <!-- 回数 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 52px"
                              >
                                {{ t('label.number-of-times') }}
                              </th>
                              <!-- サービス単位/金額 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 110px"
                              >
                                {{ t('label.services') }}{{ t('label.unit') }}/<br />
                                {{ t('label.amount-1') }}
                              </th>
                              <!-- 給付管理単位数 -->
                              <th
                                v-if="showBenefitManagement"
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 110px"
                              >
                                {{ t('label.benefit-management') }}<br />
                                {{ t('label.unit-number') }}
                              </th>
                              <!-- 種類支給限度 -->
                              <th
                                colspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 128px"
                              >
                                {{ t('label.type-payment-limit') }}
                              </th>
                              <!-- 区分支給限 -->
                              <th
                                colspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 128px"
                              >
                                {{ t('label.category-payment-limit') }}
                              </th>
                              <!-- 単位数単価 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 110px"
                              >
                                {{ t('label.unit-number') }}<br />{{
                                  t('label.using-slip-unit-price')
                                }}
                              </th>
                              <!-- 費用総額保険/事業対象分 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 110px"
                              >
                                {{
                                  t('label.total-fee') +
                                  t('label.insurance-project').substring(0, 2)
                                }}<br />
                                {{
                                  t('label.insurance-project').substring(2, 4) +
                                  t('label.target-division')
                                }}
                              </th>
                              <!-- 給付率(％) -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 74px"
                              >
                                {{ t('label.benefit') }}
                                {{ t('label.rate') }}<br />
                                ({{ t('label.percent-icon') }})
                              </th>
                              <!-- 保険/事業費請求額 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 110px"
                              >
                                {{ t('label.usgin-slip-insurance') }}/<br />
                                {{ t('label.project-fee') }}
                                {{ t('label.request-amount') }}
                              </th>
                              <!-- 定額利用負担単価金額 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 110px"
                              >
                                {{ t('label.fixed-amount') }}{{ t('label.using-burden') }}<br />
                                {{ t('label.unit-price-amount') }}
                              </th>
                              <!-- 利用者負担 -->
                              <th
                                colspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 160px"
                              >
                                {{ t('label.user-burden') }}
                              </th>
                            </tr>
                            <tr>
                              <!-- 用具名称 -->
                              <th
                                v-if="showOnlyWelfareEquipment"
                                class="pl-2 pr-2"
                                style="min-width: 125px; background-color: #dbeefe !important"
                              >
                                {{ t('label.table-maintenance-header-21') }}
                              </th>
                              <!-- TAISコード -->
                              <th
                                v-if="showOnlyWelfareEquipment"
                                class="pl-2 pr-2"
                                style="min-width: 110px; background-color: #dbeefe !important"
                              >
                                {{ t('label.table-maintenance-header-20') }}
                              </th>
                              <!-- 割引後_率 -->
                              <th
                                class="pl-2 pr-2"
                                style="min-width: 63px"
                              >
                                {{ t('label.rate') }}{{ t('label.percent-icon') }}
                              </th>
                              <!-- 割引後_単位数 -->
                              <th
                                class="pl-2 pr-2"
                                style="min-width: 63px"
                              >
                                {{ t('label.unit-number') }}
                              </th>
                              <!-- 種類支給限度_超過 -->
                              <th
                                class="pl-2 pr-2"
                                style="min-width: 64px"
                              >
                                {{ t('label.exceeded') }}
                              </th>
                              <!-- 種類支給限度_基準内 -->
                              <th
                                class="pl-2 pr-2"
                                style="min-width: 64px"
                              >
                                {{ t('label.criterion') }}
                              </th>
                              <!-- 区分支給限度_超過 -->
                              <th
                                class="pl-2 pr-2"
                                style="min-width: 64px"
                              >
                                {{ t('label.exceeded') }}
                              </th>
                              <!-- 区分支給限度_基準内 -->
                              <th
                                class="pl-2 pr-2"
                                style="min-width: 64px"
                              >
                                {{ t('label.criterion') }}
                              </th>
                              <!-- 利用者負担_保険/事業 -->
                              <th
                                class="pl-2 pr-2"
                                style="min-width: 80px"
                              >
                                {{ t('label.insurance-project') }}
                              </th>
                              <!-- 利用者負担_全額負担 -->
                              <th
                                class="pl-2 pr-2"
                                style="min-width: 80px"
                              >
                                {{ t('label.total-burden') }}
                              </th>
                            </tr>
                          </template>
                          <template #item="{ item, index }">
                            <tr
                              :class="{
                                'select-row': local.selectedUsingSlipOtherTableItemIndex === index,
                              }"
                              @click="selectUsingSlipOtherTableRow(index)"
                            >
                              <td class="pl-2 pr-2">
                                {{ item.dmyJigyoNameKnj }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ item.dmyJigyoNumber }}
                              </td>
                              <td class="pl-2 pr-2">
                                {{ item.dmyFormalnameKnj }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ item.cScode }}
                              </td>
                              <td
                                v-if="showOnlyWelfareEquipment"
                                class="pl-2 pr-2"
                              >
                                {{ showTensuCellValue(item) ? item.fygShouhinKnj : '' }}
                              </td>
                              <td
                                v-if="showOnlyWelfareEquipment"
                                class="pl-2 pr-2"
                              >
                                {{ showTensuCellValue(item) ? item.fygTekiyouCode : '' }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ showTensuCellValue(item) ? item.tensu : '' }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ showTensuCellValue(item) ? item.waribikiRitu : '' }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ showTensuCellValue(item) ? item.waribikiTen : '' }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ showTensuCellValue(item) ? item.kaisu : '' }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ item.svTensu }}
                              </td>
                              <td
                                v-if="showBenefitManagement"
                                class="pl-2 pr-2 text-right"
                              >
                                {{ item.benefitManagement }}
                              </td>
                              <td style="padding: 0px !important">
                                <base-mo01278
                                  v-model="item.sTensuOver"
                                  :oneway-model-value="localOneway.mo01278TensuOverOneway"
                                  style="width: 65px; border-radius: 0"
                                  :disabled="sTensuOverDisabled(item)"
                                  @update:model-value="
                                    tensuOverChanged(index, Or36245Const.TENSU_OVER_FLG_S)
                                  "
                                />
                              </td>
                              <td class="pl-2 pr-2">
                                {{ item.sTensu }}
                              </td>
                              <td style="padding: 0px !important">
                                <base-mo01278
                                  v-model="item.kTensuOver"
                                  :oneway-model-value="localOneway.mo01278TensuOverOneway"
                                  style="width: 65px; border-radius: 0"
                                  :disabled="kTensuOverDisabled(item)"
                                  @update:model-value="
                                    tensuOverChanged(index, Or36245Const.TENSU_OVER_FLG_T)
                                  "
                                />
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ item.kTensu }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ showTanka(item) ? item.tanka : '' }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ showTanka(item) ? item.hiyouSougaku : '' }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ showKyufuRitu(item) ? item.kyufuRitu : '' }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ showHKyufugaku(item) ? item.hKyufugaku : '' }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{
                                  item.cSougouKbn === Or36245Const.SOUGOU_KBN_2
                                    ? item.tHutanTanka
                                    : ''
                                }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{ showHutanH(item) ? item.hutanH : '' }}
                              </td>
                              <td class="pl-2 pr-2 text-right">
                                {{
                                  item.totalF === Or36245Const.TOTAL_FLG_TOTAL ? item.hutanJ : ''
                                }}
                              </td>
                            </tr>
                          </template>
                          <template #tfoot>
                            <tr class="fixed-footer bottom-height">
                              <td
                                :colspan="riyouBeppyoSumCol"
                                style="border: none !important"
                              >
                                &nbsp;
                              </td>
                            </tr>
                            <tr class="fixed-footer">
                              <!-- （列フッター）割り振りの残り単位数 -->
                              <td
                                class="pl-2 pr-2"
                                :style="[
                                  showAllocationRestUnitNumber ? '' : 'border: none !important',
                                ]"
                              >
                                <div v-if="showAllocationRestUnitNumber">
                                  {{ t('label.table-maintenance-header-14') }}
                                </div>
                              </td>
                              <td
                                :style="[
                                  showAllocationRestUnitNumber ? '' : 'border: none !important',
                                ]"
                              >
                                <div v-if="showAllocationRestUnitNumber">
                                  {{ allocationRestUnitNumber }}
                                </div>
                              </td>
                              <!-- （列フッター）区分支給限度額基準(単位) -->
                              <td
                                :colspan="showOnlyWelfareEquipment ? 5 : 3"
                                class="pl-2 pr-2 text-right"
                                style="border: none !important"
                              >
                                {{ t('label.category-payment-limit-base-amount-unit') }}
                              </td>
                              <td
                                colspan="2"
                                class="pl-2 pr-2"
                              >
                                {{ local.usingSlipInfo.kbnShikyuuGendoData[0].tKGendo }}
                              </td>
                              <!-- （列フッター）計 -->
                              <td
                                class="pl-2 pr-2"
                                style="border: none !important"
                              >
                                {{ t('label.using-slip-total-amount') }}
                              </td>
                              <!-- （列フッター）計_サービス単位/金額 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="['color: ' + cSvTensuColor]"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36245Const.KEY_C_SV_TENSU) }}
                              </td>
                              <!-- （列フッター）計_給付管理単位数 -->
                              <td
                                v-if="showBenefitManagement"
                                class="pl-2 pr-2 text-right"
                                :style="['color: ' + cKyufukanriTenColor]"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36245Const.KEY_C_KYUFUKANRI_TEN) }}
                              </td>
                              <!-- （列フッター）計_種類支給限度_超過 -->
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36245Const.KEY_C_S_TENSU_OVER) }}
                              </td>
                              <!-- （列フッター）計_種類支給限度_基準内 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36245Const.KEY_C_S_TENSU_) }}
                              </td>
                              <!-- （列フッター）計_区分支給限度_超過 -->
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36245Const.KEY_C_K_TENSU_OVER) }}
                              </td>
                              <!-- （列フッター）計_区分支給限度_基準内 -->
                              <td class="pl-2 pr-2 text-right">
                                {{ getUsingSlipOtherTableTotal(Or36245Const.KEY_C_K_TENSU) }}
                              </td>
                              <!-- スラッシュ -->
                              <td
                                class="pa-0"
                                style="border: none !important"
                              ></td>
                              <!-- （列フッター）計_費用総額保険/事業対象分 -->
                              <td class="pl-2 pr-2 text-right">
                                {{ getUsingSlipOtherTableTotal(Or36245Const.KEY_C_HIYOU_SOUGAKU) }}
                              </td>
                              <!-- スラッシュ -->
                              <td
                                class="pa-0"
                                style="border: none !important"
                              ></td>
                              <!-- （列フッター）計_保険/事業費請求額 -->
                              <td class="pl-2 pr-2 text-right">
                                {{ getUsingSlipOtherTableTotal(Or36245Const.KEY_C_H_KYUFUGAKU) }}
                              </td>
                              <!-- スラッシュ -->
                              <td
                                class="pa-0"
                                style="border: none !important"
                              ></td>
                              <!-- （列フッター）計_利用者負担_保険/事業 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="['color: ' + hutanHHutanJColor]"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36245Const.KEY_C_HUTAN_H) }}
                              </td>
                              <!-- （列フッター）計_利用者負担_全額負担 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="['color: ' + hutanHHutanJColor]"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36245Const.KEY_HUTAN_J) }}
                              </td>
                            </tr>
                          </template>
                        </c-v-data-table>
                      </c-v-col>
                    </c-v-row>
                    <c-v-divider class="mt-9 mb-4" />
                  </c-v-window-item>
                </c-v-window>
              </c-v-col>
            </c-v-row>
            <c-v-row
              no-gutters
              class="list-table"
            >
              <!--短期入所利用日数 -->
              <c-v-col
                cols="auto"
                class="pr-2"
              >
                <c-v-data-table
                  v-resizable-grid="{ columnWidths: listTable1ColumnWidth }"
                  class="table-wrapper"
                  hide-default-footer
                  :items="local.usingSlipInfo.sumData"
                  fixed-header
                  hover
                  :items-per-page="-1"
                  hide-no-data
                >
                  <template #headers>
                    <tr>
                      <th
                        colspan="3"
                        class="pl-2 pr-2"
                        style="
                          background-color: #dbeefe !important;
                          cursor: pointer;
                          color: #214d97;
                          text-decoration: underline;
                        "
                      >
                        <div
                          class="d-flex"
                          style="align-items: center"
                        >
                          <div
                            style="color: rgb(var(--v-theme-key)) !important"
                            @click="shortTermadmissionUsingDay"
                          >
                            {{ t('label.short-termadmission-using-day') }}
                          </div>
                          <c-v-tooltip
                            activator="parent"
                            location="bottom"
                            :text="$t('tooltip.short-termadmission-using-day')"
                          ></c-v-tooltip>
                        </div>
                      </th>
                      <th
                        colspan="2"
                        class="pl-2 pr-2"
                        style="border-bottom: unset !important"
                      >
                        <div class="d-flex text-center">
                          {{ t('label.category-payment-limit') }}
                        </div>
                      </th>
                      <th
                        colspan="2"
                        class="pl-2 pr-2"
                        style="border-bottom: unset !important"
                      >
                        <div class="d-flex text-center">{{ t('label.user-burden-amount') }}</div>
                      </th>
                    </tr>
                    <tr>
                      <th class="pl-2 pr-2">{{ t('label.pre-month') }}</th>
                      <th class="pl-2 pr-2">{{ t('label.current-month') }}</th>
                      <th class="pl-2 pr-2">{{ t('label.accumulation') }}</th>
                      <th class="pl-2 pr-2">{{ t('label.exceeded') }}</th>
                      <th class="pl-2 pr-2">{{ t('label.criterion') }}</th>
                      <th class="pl-2 pr-2">{{ t('label.insurance') }}</th>
                      <th class="pl-2 pr-2">{{ t('label.full-amount') }}</th>
                    </tr>
                  </template>
                  <template #item="{ item }">
                    <tr>
                      <td class="pl-2 pr-2 text-right">{{ item.zenGetuSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.cmnTucPlanTTougetu }}</td>
                      <td
                        class="pl-2 pr-2 text-right"
                        :style="['color: ' + getComputeRuikeiSumColor(item)]"
                      >
                        {{ item.computeRuikeiSum }}
                      </td>
                      <td class="pl-2 pr-2 text-right">{{ item.kbnOverSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.kbnKijunSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.hknHutanSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.jihHutanSum }}</td>
                    </tr>
                  </template>
                </c-v-data-table>
              </c-v-col>
            </c-v-row>
          </c-v-container>
        </c-v-sheet>
      </c-v-col>
    </c-v-row>
    <!-- <g-base-or00051 /> -->
    <!-- 確認ダイアログ -->
    <g-base-or-21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    />
    <!-- 警告ダイアログ -->
    <g-base-or-21815
      v-if="showDialogOr21815"
      v-bind="or21815"
    />
    <!-- 有効期間外サービス検索 -->
    <g-custom-or-27736
      v-if="showDialogOr27736"
      v-bind="or27736"
      :oneway-model-value="localOneway.or27736Oneway"
      :unique-cp-id="or27736.uniqueCpId"
      :parent-cp-id="props.uniqueCpId"
    />
    <!-- カレンダー入力ダイアログ -->
    <g-custom-or-27016
      v-if="showDialogOr27016"
      v-bind="or27016"
      v-model="local.or27016"
      :oneway-model-value="localOneway.or27016Oneway"
    />
    <!-- 計画複写ダイアログ -->
    <g-custom-or35745
      v-if="showDialogOr35745"
      v-bind="or35745"
    />
    <!-- 確認画面 -->
    <g-custom-or-27852
      v-if="showDialogOr27852"
      v-bind="or27852"
    />
    <!-- シミュレーション雛形選択 -->
    <g-custom-or-28534
      v-if="showDialogOr28534"
      v-bind="or28534"
      v-model="local.or28534"
      :oneway-model-value="localOneway.or28534Oneway"
    />
    <!-- 認定期間中の短期入所利用日数 -->
    <g-custom-or-27856
      v-if="showDialogOr27856"
      v-bind="or27856"
      v-model="local.or27856"
      :oneway-model-value="localOneway.or27856Oneway"
    />
    <!-- 印刷設定 -->
    <g-custom-or-57151
      v-if="showDialogOr57151"
      v-bind="or57151"
      :oneway-model-value="localOneway.or57151Oneway"
      :unique-cp-id="or57151.uniqueCpId"
      :parent-cp-id="props.uniqueCpId"
    />
    <!-- 年月選択 -->
    <g-custom-gui-00048
      v-if="showDialogGui0048"
      v-bind="gui00048"
    />
  </c-v-sheet>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
  // padding-bottom: 172px;
}

.second-row {
  margin-top: 0px;
  align-items: baseline;
}

:deep(.v-sheet) {
  background-color: transparent !important;
}

:deep(.top-menu) {
  padding: 0 !important;
}

:deep(.top-menu .v-divider:nth-child(3)) {
  display: none;
}

.btn-add-new {
  border-color: rgb(var(--v-theme-key)) !important;
}

:deep(.offery-ym .d-flex) {
  width: 130px !important;
}

:deep(.offery-ym) {
  background-color: #ffffff !important;
}

:deep(.lbl1) {
  height: 33px;
  margin-left: -12px;
}

:deep(.lbl2) {
  height: 33px;
  font-size: 12px;
  margin: 0;
  line-height: 33px;
  color: rgb(var(--v-theme-orange-400));
}

:deep(.inline-block) {
  display: inline-block !important;
}

.btn-insur-select {
  position: relative;
  top: 18px;
  left: -5px;
}

.flex-col {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex-shrink: 0;
}

.line3-flex-box {
  display: flex;
  column-gap: 8px;
  align-items: center;
  flex-shrink: 0;
  flex-wrap: wrap;
  width: 100%;
}

.tbl-title-18 {
  width: 18%;
}

.tbl-title-33 {
  width: 30%;
}

.tbl-title-50 {
  width: 50%;
}

:deep(.tbl-using-slip) {
  width: auto;
}

.offer-hour-time::-webkit-calendar-picker-indicator {
  display: none;
}

:deep(.txt:disabled) {
  background: unset !important;
  opacity: 0.6 !important;
}

:deep(.v-list-item__prepend) {
  width: 25px;
}

:deep(.offer-hour-time) {
  padding: 0 13px !important;
  text-align: center;
}

.full-width-field-select,
.full-width-field {
  border-radius: 0 !important;
}

.full-width-field-select,
.full-width-field {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

.list-table :deep(.table-wrapper .v-table__wrapper td) {
  background-color: #ffffff !important;
}

.list-table :deep(.v-table__wrapper td:first-child) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.list-table :deep(.v-table__wrapper td:not(:first-child)) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.list-table :deep(.v-table__wrapper td:last-child) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.tbl-using-slip-other-table {
  min-height: 252px;
}

.fixed-footer {
  position: sticky;
  bottom: 0;
}

.fixed-footer td {
  height: 32px;
  background-color: #ffffff !important;
}

.line {
  width: 100%;
  height: 31px;
  background: linear-gradient(
    to bottom right,
    transparent calc(50% - 1px),
    rgb(var(--v-theme-black-200)) 50%,
    transparent calc(50% + 1px)
  );
}

.width-1 {
  width: 8.3333333333%;
  max-width: 8.3333333333%;
  min-width: 8.3333333333%;
}

.font-red {
  color: red;
}

.hoverd {
  background-color: rgba(var(--v-border-color), var(--v-hover-opacity)) !important;
}

.tbl-using-slip.table-wrapper
  .v-table__wrapper
  tr:not(.row-selected):not(.select-row)
  td:not(:has(input, textarea, select)) {
  background-color: unset;
}

.office-dropdown {
  display: block;
}

.office-dropdown :deep(.v-col:first-child) {
  margin-top: 0 !important;
}

.office-dropdown :deep(.v-col:last-child > .v-sheet) {
  background-color: #ffffff !important;
}

:deep(.offer-year-month .v-row) {
  margin: 4px 0 !important;
}

:deep(.offer-year-month .page-label) {
  width: 86px;
  justify-content: center;
}

.level-of-care :deep(.v-row) {
  margin: 4px 0px;
}

.bottom-height {
  bottom: page-data-table.$table-row-height;
}

.tbl-using-slip-other-table :deep(.v-table__wrapper > table > thead > tr > th) {
  background-color: #dbeefe !important;
}

.tbl-using-slip-other-table :deep(.v-table__wrapper > table > tbody > tr > td) {
  background-color: unset !important;
}

:deep(.v-table__wrapper th) {
  font-weight: unset !important;
}
</style>
