<script setup lang="ts">
/**
 * Or30426:検討表(アセスメント（インターライ））画面
 * GUI00864_［検討表］画面（［アセスメント（インターライ）］画面）
 *
 * @description
 * 検討表(アセスメント（インターライ））画面
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */

import { onMounted, reactive, ref, watch, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { Or30425Const } from '../../organisms/Or30425/Or30425.constants'
import { Or30426Const } from './Or30426.constants'
import type { AsyncFunction, KentohyoKadai } from './Or30426.type'
import type { kentouhyoInfoUpdateInEntity } from '~/repositories/cmn/entities/KentouhyoInfoEntity'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useSetupChildProps, useScreenStore, useScreenTwoWayBind } from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { ParamComfirm } from '~/components/custom-components/organisms/Or33456/Or33456.type'
import { CustomClass } from '~/types/CustomClassType'
import type {
  Or30426OnewayType,
  HistoryInfo,
  PlanPeriodInfo,
} from '~/types/cmn/business/components/Or30426Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or10320OnewayType } from '~/types/cmn/business/components/Or10320Type'
import { Or10320Logic } from '~/components/custom-components/organisms/Or10320/Or10320.logic'
import { Or10320Const } from '~/components/custom-components/organisms/Or10320/Or10320.constants'
import type { Or10279OneWayType } from '~/types/cmn/business/components/Or10279Type'
import { Or10279Const } from '~/components/custom-components/organisms/Or10279/Or10279.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import type { OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import { OrX0007Logic } from '~/components/custom-components/organisms/OrX0007/OrX0007.logic'
import type { OrX0008OnewayType } from '~/types/cmn/business/components/OrX0008Type'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import { OrX0008Const } from '~/components/custom-components/organisms/OrX0008/OrX0008.constants'
import { OrX0008Logic } from '~/components/custom-components/organisms/OrX0008/OrX0008.logic'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import { OrX0009Logic } from '~/components/custom-components/organisms/OrX0009/OrX0009.logic'
import { OrX0010Const } from '~/components/custom-components/organisms/OrX0010/OrX0010.constants'
import { OrX0010Logic } from '~/components/custom-components/organisms/OrX0010/OrX0010.logic'
import type { OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import type { PlanTargetPeriodDataType } from '~/types/cmn/PlanTargetPeriodDataType'
import { Gui00070Const } from '~/components/custom-components/organisms/Gui00070/Gui00070.constants'
import type {
  HistorySelectInfoType,
  Or10929Type,
  Or10929OnewayType,
} from '~/types/cmn/business/components/Or10929Type'
import { Or10929Const } from '~/components/custom-components/organisms/Or10929/Or10929.constants'
import type { HistorySelectTableDataItem } from '~/components/custom-components/organisms/Or10929/Or10929.type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'

import { useUserListInfo } from '~/utils/useUserListInfo'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or30424Const } from '~/components/custom-components/organisms/Or30424/Or30424.constants'
import { Or30424Logic } from '~/components/custom-components/organisms/Or30424/Or30424.logic'
import { Or30425Logic } from '~/components/custom-components/organisms/Or30425/Or30425.logic'
import type { Or30424OnewayType } from '~/types/cmn/business/components/Or30424Type'
import type {
  KentouhyoInfoSelectInEntity,
  KentohyoMondai,
  KentouhyoInfoSelectOutEntity,
} from '~/repositories/cmn/entities/KentouhyoInfoSelectEntity'
import type {
  RirekiJoho,
  planPeriodInfo,
  kentouhyoCommonInfoSelectInEntity,
  kentouhyoCommonInfoSelectOutEntity,
} from '~/repositories/cmn/entities/kentouhyoCommonInfoSelectEntity'
import type { Mo00028OnewayType, Mo00028Type } from '~/types/business/components/Mo00028Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import { useJigyoList } from '~/utils/useJigyoList'
import { Or55904Const } from '~/components/custom-components/organisms/Or55904/Or55904.constants'
import { Or55904Logic } from '~/components/custom-components/organisms/Or55904/Or55904.logic'
import type { Or55904Param } from '~/components/custom-components/organisms/Or55904/Or55904.type'
import type { Or30425Type } from '~/types/cmn/business/components/Or30425Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or30426OnewayType
  uniqueCpId: string
}
const screenStore = useScreenStore()
/**
 *defineProps
 */
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * useScreenUtils
 */
// const { setChildCpBinds } = useScreenUtils()
const { jigyoListWatch } = useJigyoList()

/**
 *
 * useI18n
 */
const { t } = useI18n()

/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()

/**
 * 利用者一覧変更監視
 */
const { syscomUserSelectWatchFunc } = useUserListInfo()

/**
 * Or11871コンポーネントのユニークID
 */
const or11871 = ref({ uniqueCpId: '' })
/**
 * Or00248コンポーネントのユニークID
 */
const or00248 = ref({ uniqueCpId: '' })
/**
 * OrHeadLineコンポーネントのユニークID
 */
const orHeadLine = ref({ uniqueCpId: '' })
/**
 * Or00249コンポーネントのユニークID
 */
const or00249 = ref({ uniqueCpId: '' })
/**
 * Or10320コンポーネントのユニークID
 */
const or10320 = ref({ uniqueCpId: '' })
/**
 * Or10279コンポーネントのユニークID
 */
const or10279 = ref({ uniqueCpId: Or10279Const.CP_ID(1) })
/**
 * OrX0007コンポーネントのユニークID
 */
const orX0007 = ref({ uniqueCpId: '' })
/**
 * OrX0008コンポーネントのユニークID
 */
const orX0008 = ref({ uniqueCpId: '' })
/**
 * OrX0009コンポーネントのユニークID
 */
const orX0009 = ref({ uniqueCpId: '' })
const orX0010 = ref({ uniqueCpId: '' })
/**
 * Gui00070コンポーネントのユニークID
 */
const gui00070 = ref({ uniqueCpId: '' })
/**
 * Or10929コンポーネントのユニークID
 */
const or10929 = ref({ uniqueCpId: '' })
/**
 * Or21814コンポーネントのユニークID
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * Or21815コンポーネントのユニークID
 */
const or21815 = ref({ uniqueCpId: '' })

/**
 * Or30424コンポーネントのユニークID
 */
const or30424 = ref({ uniqueCpId: '' })
/**
 * Or30425コンポーネントのユニークID
 */
const or30425 = ref({ uniqueCpId: '' })
const or55904 = ref({ uniqueCpId: '' })
/**
 * 編集状態を判定するcomputed
 */
const or41179 = ref({ uniqueCpId: '' })

const isEdit = computed(() => {
  return (
    screenStore.getCpNavControl(or30424.value.uniqueCpId) ??
    screenStore.getCpNavControl(orX0010.value.uniqueCpId) ??
    screenStore.getCpNavControl(or30425.value.uniqueCpId)
  )
})
/**
 * 計画期間
 */
const planPeriodShow = ref<boolean>(true)
/**
 * 履歴
 */
const historyShow = ref<boolean>(true)
/**
 * 作成者
 */
const authorShow = ref<boolean>(true)
/**
 * 基準日
 */
const baseDateShow = ref<boolean>(true)

/**
 * お気に入りに該当機能
 */
const favorite = ref<boolean>(false)

/**
 * ローカルTwoway
 */
const local = reactive({
  itemSelected: {} as KentohyoMondai,
  or30424: {
    kentohyoMondaiList: [],
    selectedItemIndex: -1,
    focusRow: '',
  } as unknown as Or30424OnewayType,
  or30425: {
    kentohyoKadaiList: [],
    delBtnDisabled: false,
  } as Or30425Type,
  or30426: {
    kentohyoKadaiList: [] as KentohyoKadai[],
    setInputComponentsFlg: false,
  },
  // 作成日
  createDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // フラグ
  flag: {
    // 期間管理
    periodManage: '1',
    // 計画期間が登録されていない
    periodManageRegistration: false,
  },
  // 二回目新規ボタン押下State
  addBtnState: false,
  isSoftDelete: false,
  // 事業所ID
  officeId: '1',
  // 計画対象期間情報ID
  planPeriodId: 1,
  // 計画対象期間情報
  planPeriod: {} as PlanPeriodInfo,
  // 履歴情報ID
  historyId: 1,
  // 履歴情報
  history: {} as HistoryInfo,
  // 計画期間総件数
  totalCount: 0,
  // 認知能力
  degreeAbility: '1',
  // 法人ID
  houjinId: '',
  // 施設ID
  shisetuId: '',
  // 事業者ID
  svJigyoId: '',
  // アセスメントID
  raiId: '',
  // 基準日
  kijunbiYmd: '',
  // 作成者ID
  sakuseiId: '',
  historyModifiedCnt: '',
  updateKbn: '',
  modified_cnt: '',
})
/**
 * or26184コンポーネントの状態
 */
const or26184 = ref({
  selectedIndex: computed(() => {
    return local.or30424.selectedItemIndex
  }),
  totalLine: 0,
})

/**
 * ローカルOneway
 */
const localOneway = reactive({
  memoInputLabelBtn: {
    // デフォルト値を設定
    value: t('label.assessment-memo'),
    customClass: new CustomClass({
      outerClass: 'd-flex align-end',
    }),
  } as Mo01338OnewayType,
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  mo00009Scale: {
    btnIcon: 'height',
    density: 'compact',
    size: '36px',
  },
  //事業所
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as OrX0007OnewayType,
  // 履歴
  orX0008Oneway: {
    screenID: 'GUI00874',
    officeId: '',
    sc1Id: '',
    useId: '',
    plan1Id: '',
    mode: '',
  } as OrX0008OnewayType,
  //作成者
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
    isDisabled: false,
  } as OrX0009OnewayType,
  //作成日
  orX0010Oneway: {} as OrX0010OnewayType,
  // or10320単方向バインド
  or10320Oneway: {
    facilityId: '',
    jigyoId: '',
    tekiyouOfficeIdList: [],
    tekiyouOfficeId: '',
    jigyoGrouptekiyouId: '',
  } as unknown as Or10320OnewayType,
  Or10279Model: {
    staffId: systemCommonsStore.getStaffId,
    baseDate: systemCommonsStore.getSystemDate,
    officeId: systemCommonsStore.getStaffId,
  } as Or10279OneWayType,
  or10929Oneway: {
    historySelectInfo: {} as HistorySelectInfoType,
  } as Or10929OnewayType,
  mo00028OnewayType: {
    panelTitle: t('label.issues-and-goal'),
    customClass: new CustomClass({
      itemClass: 'bold text-center w-100',
    }),
  } as Mo00028OnewayType,
  mo00028: {
    panel: ref(1),
  } as Mo00028Type,
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    width: '90px',
  } as Mo00611OnewayType,
  //行挿入ボタン
  mo00611OnewayAddRow: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    width: '90px',
  } as Mo00611OnewayType,
  // 行複写ボタン(課題)
  mo00611OnewayCopyIssues: {
    btnLabel: t('btn.duplicate-row') + '（' + t('label.issues') + '）',
    prependIcon: 'file_copy',
    width: '143px',
  } as Mo00611OnewayType,
  // 行複写ボタン
  mo00611OnewayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    width: '90px',
  } as Mo00611OnewayType,
  // 行削除ボタン
  mo01265Oneway: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    width: '90px',
  } as Mo01265OnewayType,
  btnAddItemTooltip: {
    memoInputIconBtn: t('tooltip.add-row'),
    auxiliaryInputDialogBtn: t('tooltip.add-row'),
  },
  btnAddRowTooltip: {
    memoInputIconBtn: t('tooltip.insert-row'),
    auxiliaryInputDialogBtn: t('tooltip.insert-row'),
  },
  btnDuplicateRowTooltip: {
    memoInputIconBtn: t('tooltip.duplicate-row'),
    auxiliaryInputDialogBtn: t('tooltip.duplicate-row'),
  },
  btnDuplicateRowIssuesTooltip: {
    memoInputIconBtn: t('tooltip.duplicate-row-issue'),
    auxiliaryInputDialogBtn: t('tooltip.duplicate-row-issue'),
  },
  btnDeleteRowTooltip: {
    memoInputIconBtn: t('tooltip.delete-row'),
    auxiliaryInputDialogBtn: t('tooltip.delete-row'),
  },
})
const listHistory = ref<RirekiJoho[]>([])
const listPeriod = ref<planPeriodInfo[]>([])
/**
 * e-文書法対象機能の電子ファイル保存設定区分
 */
const electronicFileSaveSettingscategoryFlag = true

/**
 *  Or10320双方向バインド
 */
const _or10320Type = ref<Or10320OnewayType>({
  shisetuId: '',
  svJigyoId: '',
})
const isPerioidsEmpty = computed(() => listPeriod.value.length === 0)
/**
 * Or30424の参照
 */
const or30424Ref = ref<{
  init(): AsyncFunction
  onResizeRow(): AsyncFunction
}>()
// Or30425 Ref
/**
 * Or30425の参照
 */
const or30425Ref = ref<{
  tableValidation(): AsyncFunction
  createRow(): AsyncFunction
  insertRow(): AsyncFunction
  copyRow(): AsyncFunction
  copyRow2Field(): AsyncFunction
  deleteRow(): AsyncFunction
  init(): AsyncFunction
}>()

/**
 * or10929Type
 */
const or10929Type = ref<Or10929Type>({
  historySelectDataList: [],
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or30426Const.STR_ALL })
  })

  // コントロール設定
  await init()
})

/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [OrX0007Const.CP_ID(1)]: orX0007.value,
  [OrX0008Const.CP_ID(1)]: orX0008.value,
  [OrX0009Const.CP_ID(1)]: orX0009.value,
  [OrX0010Const.CP_ID(1)]: orX0010.value,
  [Gui00070Const.CP_ID(0)]: gui00070.value,
  [Or10929Const.CP_ID(1)]: or10929.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or10320Const.CP_ID(1)]: or10320.value,
  [Or30424Const.CP_ID(0)]: or30424.value,
  [Or30425Const.CP_ID(0)]: or30425.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [Or55904Const.CP_ID(0)]: or55904.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
  [Or30424Const.CP_ID(0)]: or30424.value,
  [Or30425Const.CP_ID(0)]: or30425.value,
})
// 画面状態管理用のPiniaストア

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.consider-table'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: true,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
  },
})
const callbackFuncJigyo = async (newJigyoId: string) => {
  systemCommonsStore.setSvJigyoId(newJigyoId)
  await getCommonData()
}
// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, () => {
  void callbackFuncJigyo
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */
const callbackUserChange = async (newSelfId: string) => {
  systemCommonsStore.setUserId(newSelfId)
  await getCommonData()
  await getDataTable()
}
// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(() => {
  void callbackUserChange
})
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      favoriteIconClick()
      setOr11871Event({ favoriteEventFlg: false })
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await _update()
      setOr11871Event({ saveEventFlg: false })
    }
    if (newValue.createEventFlg) {
      // 実行無効制御
      if (!local.isSoftDelete) {
        // 新規ボタンが押下された場合、新規作成処理を実行する
        void addBtnClick()
        setOr11871Event({ createEventFlg: false })
      }
    }
    if (newValue.printEventFlg) {
      // 実行無効制御
      if (!local.isSoftDelete) {
        // 印刷ボタンが押下された場合、印刷設定画面を表示する
        void printSettingIconClick()
        setOr11871Event({ printEventFlg: false })
      }
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      void masterIconClick()
      setOr11871Event({ masterEventFlg: false })
    }
  }
)
/**
 * 削除確認ダイアログを表示する
 *
 * @param param - 確認ダイアログのパラメータ
 */
const openPopupComfirm = (param: ParamComfirm) => {
  return new Promise((resolve) => {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        isOpen: true,
        dialogText: param.message,
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: param?.firstBtnLabel ?? t('btn.yes'),
        secondBtnType: param?.secondBtnType ?? 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnLabel: t('btn.cancel'),
        thirdBtnType: param?.thirdBtnType ?? 'blank',
      },
    })
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        if (event?.firstBtnClickFlg) {
          if (param.excuteFunction) {
            void param.excuteFunction()
          }
          resolve(true)
        }
        if (event?.secondBtnClickFlg) {
          if (param.excuteFunction1) {
            void param.excuteFunction1()
          }
        }
        if (event?.thirdBtnClickFlg || event?.closeBtnClickFlg) {
          if (param.excuteFunction2) {
            void param.excuteFunction2()
          }
        }
        resolve(false)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
      },
      { once: true }
    )
  })
}
/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg
    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }
    const index = listPeriod.value.findIndex((item) => item.sc1Id === newValue.planTargetPeriodId)
    const isLast = index === listPeriod.value.length - 1
    const isFirst = index === 0
    const handleChangePreiod = async () => {
      const openErrMess = async (message: string) => {
        const param: ParamComfirm = {
          message,
          firstBtnLabel: 'OK',
          secondBtnType: 'blank',
        }
        await openPopupComfirm(param)
      }
      if (planUpdateFlg === '0') {
        handleLogicx0007(index)
      } else if (planUpdateFlg === '1') {
        if (isFirst) {
          await openErrMess(t('message.i-cmn-11262'))
          return
        }
        handleLogicx0007(index - 1)
      } else if (planUpdateFlg === '2') {
        if (isLast) {
          await openErrMess(t('message.i-cmn-11263'))
          return
        }
        handleLogicx0007(index + 1)
      }
      await getCommonData(true)
    }
    if (isEdit.value && planUpdateFlg !== '0') {
      if ((isLast && planUpdateFlg === '2') || (isFirst && planUpdateFlg === '1')) return
      const saveAndNext = async () => {
        await _update(false)
        await handleChangePreiod()
      }
      const param: ParamComfirm = {
        message: t('message.i-ldg-10430'),
        excuteFunction1: void handleChangePreiod,
        excuteFunction: void saveAndNext,
        thirdBtnType: 'normal3',
      }
      await openPopupComfirm(param)
    } else {
      await handleChangePreiod()
    }
  },
  { deep: true }
)

/**
 * AC017_「履歴-前へアイコンボタン」押下
 * AC018_「履歴-次へアイコンボタン」押下
 * 履歴変更の監視
 */
watch(
  () => OrX0008Logic.data.get(orX0008.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) return

    const createId = String(newValue.createId)
    const createUpateFlg = newValue.createUpateFlg
    if (!createUpateFlg || (listHistory.value.length === 1 && !listHistory.value[0].rirekiId))
      return
    const index = listHistory.value.findIndex((item) => item.rirekiId === createId)
    const isLast = index === listHistory.value.length - 1
    const isFirst = index === 0
    const handleChangeHistory = async () => {
      if (createUpateFlg === '0') {
        handleLogicx0008(index)
      } else if (createUpateFlg === '1') {
        if (isFirst) {
          return
        }
        handleLogicx0008(index - 1)
      } else if (createUpateFlg === '2') {
        if (isLast) {
          return
        }
        handleLogicx0008(index + 1)
      }
      await getDataTable()
    }

    const isCreateMode = local.updateKbn === 'C'

    if (isCreateMode) {
      if (createUpateFlg === '2') return

      await handleChangeHistory()
      listHistory.value.pop()

      localOneway.orX0008Oneway.createData = {
        ...localOneway.orX0008Oneway.createData,
        totalCount: listHistory.value.length,
      }

      local.updateKbn = ''
      return
    }

    if (isEdit.value && createUpateFlg !== '0') {
      if ((isLast && createUpateFlg === '2') || (isFirst && createUpateFlg === '1')) return
      const saveAndNext = async () => {
        await _update(false)
        await handleChangeHistory()
      }

      const param: ParamComfirm = {
        message: t('message.i-ldg-10430'),
        excuteFunction1: void handleChangeHistory,
        excuteFunction: void saveAndNext,
        thirdBtnType: 'normal3',
      }
      await openPopupComfirm(param)
    } else {
      await handleChangeHistory()
    }
  },
  { deep: true }
)

watch(
  () => local.or30424.selectedItemIndex,
  async (newValue) => {
    local.itemSelected = local.or30424.kentohyoMondaiList[newValue]
    if (local.itemSelected) {
      local.or30425.kentohyoKadaiList = local.or30426.kentohyoKadaiList.filter(
        (i: KentohyoKadai) => i.raiId === local.itemSelected.raiId
      )
      Or30425Logic.data.set({
        uniqueCpId: or30425.value.uniqueCpId,
        value: {
          kentohyoKadaiList: local.or30425.kentohyoKadaiList,
          delBtnDisabled: false,
        },
        isInit: true,
      })
      // setChildCpBinds(props.uniqueCpId, {
      //   Or30425: {
      //     twoWayValue: {
      //       kentohyoKadaiList: local.or30425.kentohyoKadaiList,
      //       raiId: local.itemSelected.raiId,
      //     },
      //   },
      // })
      await nextTick()
      or30425Ref.value?.init()
    }
  }
)
watch(
  () => local.or30425.kentohyoKadaiList,
  (newValue: KentohyoKadai[]) => {
    if (!newValue.length) return
    local.or30426.kentohyoKadaiList = local.or30426.kentohyoKadaiList
      .filter((item) => item.raiId !== local.itemSelected.raiId)
      .concat(newValue)
  },
  { deep: true }
)
// ダイアログ表示フラグ
const showDialogOr55904 = computed(() => {
  // Or55904のダイアログ開閉状態
  return Or55904Logic.state.get(or55904.value.uniqueCpId)?.isOpen ?? false
})

/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 *  AC001_初期表示
 */
const init = async () => {
  await getCommonData()
  await getDataTable()
  local.or30426.setInputComponentsFlg = true
}
/**
 * 画面コントロール表示設定
 *
 * @param index - インデックス
 */
const handleLogicx0007 = (index: number) => {
  localOneway.orX0007Oneway.planTargetPeriodData = {
    orX0115Oneway: {
      sc1Id: Number(listPeriod.value[index].sc1Id),
    },
    planTargetPeriodId: Number(listPeriod.value[index].sc1Id),
    planTargetPeriod: listPeriod.value[index]?.startYmd + ' ~ ' + listPeriod.value[index]?.endYmd,
    currentIndex: index + 1,
    totalCount: listPeriod.value.length,
  } as PlanTargetPeriodDataType
  OrX0007Logic.data.set({
    uniqueCpId: orX0007.value.uniqueCpId,
    value: {
      planTargetPeriodId: listPeriod.value[index].sc1Id,
      PlanTargetPeriodUpdateFlg: '',
    },
  })
}
const handleLogicx0008 = (index: number) => {
  localOneway.orX0008Oneway.createData = {
    createId: listHistory.value[index]?.rirekiId ?? '',
    createDate: listHistory.value[index]?.sakuseiYmd ?? '',
    staffId: listHistory.value[index]?.chkShokuId ?? '',
    staffName: listHistory.value[index]?.shokuinKnj ?? '',
    currentIndex: index + 1,
    totalCount: listHistory.value.length,
  }
  localOneway.orX0008Oneway.plan1Id = listHistory.value[index]?.rirekiId ?? ''
  localOneway.orX0008Oneway.officeId = systemCommonsStore.getSvJigyoId ?? ''
  OrX0010Logic.data.set({
    uniqueCpId: orX0010.value.uniqueCpId,
    value: {
      value: listHistory.value[index]?.sakuseiYmd ?? '',
      mo01343: {} as unknown as Mo01343Type,
    },
    isInit: true,
  })
  localOneway.orX0009Oneway.createData = {
    createDate: listHistory.value[index]?.sakuseiYmd ?? '',
    createId: Number(listHistory.value[index]?.rirekiId),
    totalCount: listHistory.value.length,
    currentIndex: index,
    staffName: listHistory.value[index]?.shokuinKnj ?? '',
    staffId: Number(listHistory.value[index]?.chkShokuId ?? ''),
  }
  localOneway.orX0008Oneway.sc1Id =
    OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? ''
  localOneway.orX0008Oneway.useId = systemCommonsStore.getUserId ?? ''
  OrX0008Logic.data.set({
    uniqueCpId: orX0008.value.uniqueCpId,
    value: {
      createId: listHistory.value[index]?.rirekiId,
      createUpateFlg: '',
      rirekiObj: {
        createId: listHistory.value[index]?.rirekiId,
        createDate: listHistory.value[index]?.sakuseiYmd ?? '',
        staffId: listHistory.value[index]?.chkShokuId ?? '',
        staffName: listHistory.value[index]?.shokuinKnj ?? '',
        currentIndex: index + 1,
        totalCount: listHistory.value.length,
      },
    },
    isInit: true,
  })
  OrX0009Logic.data.set({
    uniqueCpId: orX0009.value.uniqueCpId,
    value: {
      staffId: listHistory.value[index]?.chkShokuId ?? '',
      staffName: listHistory.value[index]?.shokuinKnj ?? '',
    },
    isInit: true,
  })
}
/**
 * 共通情報取得
 *
 * @param isChangePeriod - xxxx
 */
const getCommonData = async (isChangePeriod = false) => {
  // バックエンドAPIから初期情報取得
  const inputData: kentouhyoCommonInfoSelectInEntity = {
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList.map((item) => item) ?? [],
    userId: systemCommonsStore.getUserId ?? '',
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
    shokuinId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
  }

  const result: kentouhyoCommonInfoSelectOutEntity = await ScreenRepository.select(
    'kentouhyoCommonInfoSelect',
    inputData
  )
  // 履歴変更【共通処理】/初期情報取得

  // 画面項目設定
  if (result.data) {
    if (result.data.planPeriodInfo?.length && !isChangePeriod) {
      listPeriod.value = result.data.planPeriodInfo
      handleLogicx0007(listPeriod.value.length - 1)
    }
    if (result.data.rirekiJoho?.length) {
      listHistory.value = result.data.rirekiJoho
      handleLogicx0008(listHistory.value.length - 1)
    }
  }
}

/**
 * AC002_「お気に入りアイコンボタン」押下
 */
const favoriteIconClick = () => {
  // TODO お気に入りに該当機能がない場合
  if (favorite.value) {
    // 「アセスメント(インターライ)」をお気に入り機能に追加する。
  }
  // お気に入りに該当機能がある場合
  else {
    // 「アセスメント(インターライ)」をお気に入り機能から削除する。
  }
}

/**
 * AC003_「保存ボタン」押下
 *
 * @param isRunInit - 初期化を実行するかどうかのフラグ
 */
const _update = async (isRunInit = true) => {
  // 画面入力データに変更がない場合
  if (isPerioidsEmpty.value) return
  if (local.updateKbn !== 'C') {
    if (!isEdit.value && !local.isSoftDelete) {
      const param: ParamComfirm = {
        message: t('message.i-cmn-21800'),
        firstBtnLabel: t('OK'),
        secondBtnType: 'blank',
      }
      await openPopupComfirm(param)
      return false
    }
    if (isEdit.value) {
      local.updateKbn = 'U'
    }
    if (local.isSoftDelete) {
      local.updateKbn = 'D'
    }
  } else if (local.isSoftDelete) {
    local.isSoftDelete = false
    return
  }
  // アセスメント(インターライ)画面履歴の最新情報を取得する
  const params: kentouhyoInfoUpdateInEntity = {
    rirekiId: 0,
    created_user: {
      staffId: 0,
      staffName: '',
    },
    created: 0,
    shokuinId: Number(systemCommonsStore.getCurrentUser.chkShokuId),
    plnType: 0,
    modified_cnt: Number(local.historyModifiedCnt),
    deleteKbn: local.isSoftDelete ? 1 : 0,
    updateKbn: local.updateKbn,
    kadaiList: local.or30424.kentohyoMondaiList,
    mondaiList: local.or30426.kentohyoKadaiList,
  }
  const res = await ScreenRepository.insert('kentouhyoInfoUpdate', params)
  if (res.statusCode === '200') {
    if (local.updateKbn === 'D') {
      let currentIndexHistory = listHistory.value.findIndex(
        (item) => item.rirekiId === OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId
      )
      listHistory.value.splice(currentIndexHistory, 1)
      if (currentIndexHistory === listHistory.value.length) {
        currentIndexHistory = listHistory.value.length - 1
      }
      handleLogicx0008(currentIndexHistory)
      await getDataTable()
      local.isSoftDelete = false
    } else if (local.updateKbn === 'C' && isRunInit) {
      await getDataTable()
    } else if (local.updateKbn === 'U' && isRunInit) {
      await getDataTable()
    }
    local.updateKbn = ''
  }
}

/**
 * AC004_「新規ボタン」押下
 */
const addBtnClick = async () => {
  // 二回目新規ボタン押下する場合
  if (local.addBtnState) {
    await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.assessment')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  }

  // 共通情報.基準日
  OrX0010Logic.data.set({
    uniqueCpId: orX0010.value.uniqueCpId,
    value: { value: systemCommonsStore.getSystemDate ?? '', mo01343: {} as unknown as Mo01343Type },
    isInit: true,
  })
  local.addBtnState = true
  // 期間管理フラグが「1:管理する」
  if (local.flag.periodManage === Or30426Const.NUMBER_1) {
    // 計画期間情報.期間総件数 = 0（期間なし）
    if (local.totalCount) {
      const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.error'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11300'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.ok'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (Or30426Const.STR_YES === dialogResult) {
        // TODO AC013を実行
      }
    }
  }

  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case Or30426Const.STR_YES:
        // AC003(保存処理)を実行し
        await _update()
        // 処理続き
        AllSubAdd()

        // 画面情報を下記に設定する
        // TODO 履歴-ページング = 履歴の最大値+1/履歴の最大値+1
        break
      case 'no':
        // 処理続き
        AllSubAdd()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }
}

/**
 * サブ画面の新規操作
 */
const AllSubAdd = () => {
  // アセスメント(インターライ)画面「A ~ V」の新規情報を取得する。
}

/**
 * アセスメント(インターライ)画面履歴の最新情報を取得する
 */
const getDataTable = async () => {
  if (!historyShow.value && !authorShow.value && !baseDateShow.value) {
    return
  }

  // 予定マスタ情報取得(IN)
  const param: KentouhyoInfoSelectInEntity = {
    raiId: OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId ?? '0',
    youshikiFlg: '0',
    userid: systemCommonsStore.getUserId ?? '',
  }
  // 予定マスタ初期情報取得
  const result: KentouhyoInfoSelectOutEntity = await ScreenRepository.select(
    'kentouhyoInfoSelect',
    param
  )
  local.or30424.kentohyoMondaiList = result.data.kentohyoMondaiList
  local.or30426.kentohyoKadaiList = result.data.kentohyoKadaiList
  or26184.value.totalLine = result.data.kentohyoMondaiList.length
  Or30424Logic.data.set({
    uniqueCpId: or30424.value.uniqueCpId,
    value: local.or30424,
    isInit: true,
  })
  await nextTick()
  or30424Ref.value?.init()
  local.or30424.selectedItemIndex = 0
}

/**
 * AC005_「複写ボタン」押下
 */
const copyBtnClick = () => {
  // 実行無効制御
  if (local.isSoftDelete) {
    return
  }
}

/**
 * AC006_「印刷設定アイコンボタン」押下
 */
const printSettingIconClick = async () => {
  if (local.isSoftDelete || isPerioidsEmpty.value) return
  const openPrint = () => {
    Or55904Logic.state.set({
      uniqueCpId: or55904.value.uniqueCpId,
      state: {
        isOpen: true,
        param: {
          shokuId: OrX0009Logic.data.get(orX0009.value.uniqueCpId)?.staffId ?? '',
          prtNo: '',
          svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
          shisetuId: systemCommonsStore.getShisetuId ?? '',
          tantoId: '',
          syubetsuId: systemCommonsStore.getSyubetu ?? '',
          sectionName: '',
          userId: systemCommonsStore.getUserId ?? '',
          assessmentId: OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId,
          svJigyoKnj: systemCommonsStore.getSvJigyoCd ?? '',
          processYmd: OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? '',
          parentUserIdSelectDataFlag: false,
        } as unknown as Or55904Param,
      },
    })
  }
  const handleSaveAndOpenPrint = async () => {
    await _update()
    openPrint()
  }
  console.log('isEdit.value', isEdit.value)
  if (isEdit.value) {
    const param: ParamComfirm = {
      excuteFunction: void handleSaveAndOpenPrint,
      excuteFunction1: openPrint,
      message: t('message.i-cmn-10430'),
    }
    await openPopupComfirm(param)
  } else {
    openPrint()
  }
}

/**
 * AC007_「マスタ他設定アイコンボタン」押下
 */
const masterIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case Or30426Const.STR_YES:
        // AC003(保存処理)を実行し
        await _update()

        // 処理続き
        await getDataTable()
        break
      case 'no':
        // 処理続き
        await getDataTable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // Or10320のダイアログ開閉状態を更新する
  Or10320Logic.state.set({
    uniqueCpId: or10320.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC009_「ログ」押下
 */
const logClick = () => {
  // TODO 共通処理 e-文書法対象機能の電子ファイル保存設定を取得する。
  // TODO 共通処理 e-文章法対象の帳票のXPSフォルダをエクスプローラで開く。
  // 内部QA票：27
}

/**
 * AC011_「削除」押下
 */
const deleteClick = async () => {
  if (isPerioidsEmpty.value) return
  const softDelete = () => {
    local.isSoftDelete = true
  }
  const param: ParamComfirm = {
    excuteFunction: softDelete,
    message: t('message.i-cmn-11326', [
      `[${OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? ''}]`,
      t('label.consider-table'),
    ]),
  }
  if (local.isSoftDelete) return
  await openPopupComfirm(param)
}
/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 *
 * @returns ダイアログの選択結果（'yes', 'no', 'cancel' など）
 */
const openWarningDialog = (uniqueCpId: string, state: Or21815StateType) => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)

        let result = Or30426Const.STR_YES

        if (event?.firstBtnClickFlg) {
          result = Or30426Const.STR_YES
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * AC016_選択前の履歴から変更
 *
 * @param selectItem - 選択の履歴
 */
const historySelectChange = async (selectItem: HistorySelectTableDataItem | undefined) => {
  // 選択前の履歴から変更がない場合
  if (!selectItem) {
    // 処理終了にする。
    return
  }
  // 選択前の履歴から変更がある場合
  else {
    // アセスメント(インターライ)画面履歴変更処理
    local.historyId = parseInt(selectItem.raiId)

    await getDataTable()
  }
}

/**
 * 行追加ボタン押下時の処理
 */
function onAddRow() {
  if (!local.isSoftDelete) {
    or30425Ref.value?.createRow()
  }
}
/**
 * 行挿入ボタン押下時の処理
 */
function onInsertRow() {
  if (!local.isSoftDelete) {
    or30425Ref.value?.insertRow()
  }
}
/**
 * 行複写ボタン押下時の処理
 */
function onCloneItem() {
  if (!local.isSoftDelete) {
    or30425Ref.value?.copyRow()
  }
}
/**
 * 行複写（課題）ボタン押下時の処理
 */
function onTwoCloneItem() {
  if (!local.isSoftDelete) {
    or30425Ref.value?.copyRow2Field()
  }
}
/**
 * 行削除ボタン押下時の処理
 */
function onDelete() {
  if (!local.isSoftDelete) {
    or30425Ref.value?.deleteRow()
  }
}
/**
 * 表示順変更アセスメント画面をポップアップで起動する
 */
function _onClickOrSort() {
  // TODO GUI00633_表示順変更アセスメント
}
</script>

<template>
  <c-v-sheet class="view backgroundColor">
    <g-base-or11871 v-bind="or11871">
      <template #createItems>
        <c-v-list-item
          :title="t('btn.copy')"
          @click="copyBtnClick"
        />
      </template>
      <template #optionMenuItems>
        <c-v-list-item
          :title="
            t('label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-delete')
          "
          prepend-icon="delete"
          @click="deleteClick"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.delete-data')"
          />
        </c-v-list-item>
      </template>
    </g-base-or11871>

    <div
      no-gutters
      class="main-Content d-flex"
    >
      <!-- ナビゲーションエリア -->
      <div>
        <!-- 利用者選択一覧 -->
        <g-base-or00248
          v-bind="or00248"
          class="ml-6"
        />
      </div>

      <!-- コンテンツエリア -->
      <div class="main-right h-100">
        <!-- 上段 -->
        <c-v-row
          no-gutters
          class="top"
        >
          <c-v-col
            cols="12 pl-6"
            name="header"
          >
            <c-v-row
              no-gutters
              class="d-flex align-end ga-6"
            >
              <c-v-col
                cols="auto office-select"
                style="width: 203px"
              >
                <g-base-or-41179
                  v-bind="or41179"
                  :width="'203'"
                />
              </c-v-col>
              <!-- 計画対象期間 -->
              <c-v-col
                v-if="planPeriodShow"
                cols="auto"
                class="planPeriod"
              >
                <g-custom-orX0007
                  v-bind="orX0007"
                  :oneway-model-value="localOneway.orX0007Oneway"
                  :unique-cp-id="orX0007.uniqueCpId"
                />
              </c-v-col>
              <!-- 基準日 -->
              <c-v-col
                v-if="baseDateShow"
                :class="local.isSoftDelete ? 'disabled-event' : ''"
                cols="auto"
              >
                <g-custom-or-x0010
                  class="custom-required"
                  v-bind="orX0010"
                  :oneway-model-value="{
                    ...localOneway.orX0010Oneway,
                    isDisabled: local.isSoftDelete,
                  }"
                  :unique-cp-id="orX0010.uniqueCpId"
                />
              </c-v-col>
              <!-- 作成者 -->
              <c-v-col
                v-if="authorShow"
                cols="auto"
                style="align-content: center"
              >
                <g-custom-orX0009
                  v-bind="orX0009"
                  :class="local.isSoftDelete ? 'disabled-event' : ''"
                  :oneway-model-value="localOneway.orX0009Oneway"
                  :unique-cp-id="orX0009.uniqueCpId"
                />
              </c-v-col>
              <!-- 履歴 -->
              <c-v-col
                v-if="historyShow"
                cols="auto"
              >
                <g-custom-orX0008
                  v-bind="orX0008"
                  :oneway-model-value="localOneway.orX0008Oneway"
                  :unique-cp-id="orX0008.uniqueCpId"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>

          <c-v-col
            v-show="!local.isSoftDelete"
            cols="12"
          >
            <hr class="my-6" />
            <!-- main content -->
            <div class="pl-6">
              <g-custom-or-30424
                ref="or30424Ref"
                v-bind="or30424"
                v-model="local.or30424"
                :parent-unique-cp-id="props.uniqueCpId"
              />
            </div>
            <hr class="my-6" />
            <div
              class="pt-0 custom-padding ml-6"
              style="width: 1040px"
            >
              <div
                id="issues_and_goal"
                class="mb-6 header-title"
              >
                {{ t('label.issues-and-goal') }}
              </div>
              <div
                class="d-flex align-center justify-space-between mb-4"
                name="buttons-area"
              >
                <div
                  name="left-buttons"
                  class="d-flex align-center"
                >
                  <base-mo00611
                    :oneway-model-value="localOneway.mo00611OnewayAdd"
                    class="mx-1 ml-0 pl-0"
                    @click="onAddRow"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :text="localOneway.btnAddItemTooltip.memoInputIconBtn"
                    />
                  </base-mo00611>
                  <base-mo00611
                    :oneway-model-value="localOneway.mo00611OnewayAddRow"
                    class="mx-1"
                    @click="onInsertRow"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :text="localOneway.btnAddRowTooltip.memoInputIconBtn"
                    />
                  </base-mo00611>
                  <base-mo00611
                    :oneway-model-value="localOneway.mo00611OnewayCopy"
                    class="mx-1"
                    @click="onCloneItem"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :text="localOneway.btnDuplicateRowTooltip.memoInputIconBtn"
                    />
                  </base-mo00611>
                  <base-mo00611
                    :oneway-model-value="localOneway.mo00611OnewayCopyIssues"
                    class="mx-1"
                    @click="onTwoCloneItem"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :text="localOneway.btnDuplicateRowIssuesTooltip.memoInputIconBtn"
                    />
                  </base-mo00611>
                  <base-mo01265
                    :oneway-model-value="localOneway.mo01265Oneway"
                    class="mx-1"
                    @click="onDelete"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :text="localOneway.btnDeleteRowTooltip.memoInputIconBtn"
                    />
                  </base-mo01265>
                </div>
                <div
                  name="right-buttons"
                  class="d-flex align-center"
                >
                  <c-v-rows class="d-flex justify-end align-center">
                    <base-mo00611
                      :oneway-model-value="{
                        btnLabel: t('label.display-order'),
                        width: '65px',
                        minWidth: '65px',
                      }"
                      class="mr-6"
                    >
                      <c-v-tooltip
                        activator="parent"
                        location="bottom"
                        :text="t('label.display-order')"
                      />
                    </base-mo00611>
                    {{ local.or30425.kentohyoKadaiList.length }}件
                  </c-v-rows>
                </div>
              </div>

              <div class="mb-6">
                <g-custom-or-30425
                  ref="or30425Ref"
                  v-bind="or30425"
                  v-model="local.or30425"
                  :parent-unique-cp-id="props.uniqueCpId"
                />
              </div>
            </div>
          </c-v-col>
        </c-v-row>
      </div>
    </div>

  </c-v-sheet>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- メッセージ -->
  <g-base-or-21815 v-bind="or21815" />

  <!-- GUI00788 ［アセスメント（インターライ）CSV出力］画面-->
  <g-custom-or-10279
    v-bind="or10279"
    :oneway-model-value="localOneway.Or10279Model"
  />

  <!-- GUI00070 対象期間画面 -->
  <g-custom-gui-00070 v-bind="gui00070" />

  <!-- GUI00792 ［履歴選択］画面 -->
  <g-custom-or-10929
    v-bind="or10929"
    v-model="or10929Type"
    :oneway-model-value="localOneway.or10929Oneway"
    @update:model-value="historySelectChange"
  />
  <g-custom-or-55904
    v-if="showDialogOr55904"
    v-bind="or55904"
  />
</template>

<style scoped lang="scss">
:deep(.pl-4) {
  padding-left: 0px !important;
}

.view {
  background-color: transparent;
}

.main-Content {
  display: flex;
  flex-grow: 1;
  height: 100%;

  .main-right {
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-left: -24px;

    .top {
      flex: 0 1 auto;

      :deep(.v-sheet) {
        background-color: transparent !important;
      }
    }
    .middleContent {
      min-height: 0;
    }
    .footer {
      flex: 0 1 auto;
    }
  }
}

.planPeriod {
  :first-child {
    align-items: center;
  }
}

.backgroundColor {
  background: rgb(var(--v-theme-background));
}

.tabItems {
  border-top: thin solid rgb(var(--v-theme-form));
  margin-top: 8px;
  margin-bottom: 8px;
}
:deep(.v-expansion-panel-text__wrapper) {
  padding-left: 8px;
  padding-right: 8px;
}
:deep(.v-expansion-panel-title) {
  font-weight: bold;
  font-size: 14px !important;
}
.disabled-event {
  pointer-events: none;
}

.container {
  background-color: transparent;
  padding: 0px !important;

  :has(.contentItem) {
    :deep(.item-label) {
      color: #0000ff;
      font-size: 12px !important;
      font-weight: normal !important;
    }
  }
}
.g30424-wrapper {
  overflow: none;
  padding-bottom: 10px;
  display: flex;
  flex-direction: column;
}
:deep(.jigyo) {
  flex-direction: column;
  position: relative;
  bottom: 3px;
  .v-input {
    width: 203px !important;
  }
  .v-field.v-field--appended {
    background-color: #fff;
  }
}
:deep(.custom-required) {
  .item-label::after {
    content: '必須';
    color: #ed6809;
    font-size: 12px;
    padding-left: 4px;
  }
}

:deep(.office-select) {
  .v-col:has(.item-label) {
    margin: 0 !important;
    line-height: 20px !important;
    margin-bottom: 4px !important;
    .item-label {
      line-height: 20px !important;
    }
  }
}

:deep(.planPeriod) {
  .v-col:has(.item-label) {
    line-height: 20px !important;
    .item-label {
      line-height: 20px !important;
    }
  }
}

:deep(.custom-required) {
  .v-col:has(.item-label) {
    line-height: 20px !important;
    // height: 20px !important;
    .item-label {
      line-height: 20px !important;
    }
  }
}

.header-title {
  font-size: 18px !important;
  font-weight: bold !important;
}
</style>
