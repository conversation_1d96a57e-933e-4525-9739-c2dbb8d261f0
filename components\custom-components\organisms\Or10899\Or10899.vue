<script setup lang="ts">
/**
 * Or10899:有機体:予防計画書マスタモーダル
 * GUI01086_予防計画書マスタ
 *
 * @description
 * 予防計画書マスタモーダル
 *
 * <AUTHOR> 朱征宇
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or17631Const } from '../Or17631/Or17631.constants'
import { Or17630Const } from '../Or17630/Or17630.constants'
import { Or17629Const } from '../Or17629/Or17629.constants'
import { Or17628Const } from '../Or17628/Or17628.constants'
import { Or17635Const } from '../Or17635/Or17635.constants'
import { Or17634Const } from '../Or17634/Or17634.constants'
import { Or17633Const } from '../Or17633/Or17633.constants'
import { Or17631Logic } from '../Or17631/Or17631.logic'
import { Or17630Logic } from '../Or17630/Or17630.logic'
import { Or17629Logic } from '../Or17629/Or17629.logic'
import { Or17628Logic } from '../Or17628/Or17628.logic'
import { Or17635Logic } from '../Or17635/Or17635.logic'
import { Or17634Logic } from '../Or17634/Or17634.logic'
import { Or17633Logic } from '../Or17633/Or17633.logic'
import type { Or10899StateType, Or10899Type } from './Or10899.type'
import { Or10899Const } from './Or10899.constants'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import {
  hasRegistAuth,
  useGyoumuCom,
  useScreenOneWayBind,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { useScreenUtils } from '~/utils/useScreenUtils'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or10899OneWayType } from '~/types/cmn/business/components/Or10899Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type {
  PreventionPlanInEntity,
  PreventionPlanOutEntity,
} from '~/repositories/cmn/entities/PreventionPlanInitSelectEntity'
import { CustomClass } from '~/types/CustomClassType'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  PreventionPlanBunrui3,
  PreventionPlanInsertInEntity,
} from '~/repositories/cmn/entities/PreventionPlanInsertEntity'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Or17631OneWayType } from '~/types/cmn/business/components/Or17631Type'
import type { Or17630OneWayType } from '~/types/cmn/business/components/Or17630Type'
import type { Or17629OneWayType } from '~/types/cmn/business/components/Or17629Type'
import type { Or17635OneWayType } from '~/types/cmn/business/components/Or17635Type'
import type { Or17634OneWayType } from '~/types/cmn/business/components/Or17634Type'
import type { Or17633OneWayType } from '~/types/cmn/business/components/Or17633Type'
import type { Or17628OneWayType } from '~/types/cmn/business/components/Or17628Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type {
  Or21814OnewayType,
  Or21814EventType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type {
  Or21815StateType,
  Or21815EventType,
} from '~/components/base-components/organisms/Or21815/Or21815.type'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10899OneWayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const local = reactive({
  or10899: {
    youshiki: '',
    youshikiBunrui3: '',
    youshikiModifiedCnt: '',
    kikanKanri: '',
    kikanKanriBunrui3: '',
    kikanKanriModifiedCnt: '',
    kikanCalendar: '',
    kikanCalendarBunrui3: '',
    kikanCalendarModifiedCnt: '',
    hokenServiceJigyo: '',
    hokenServiceJigyoBunrui3: '',
    hokenServiceJigyoModifiedCnt: '',
    hokenServiceKasan: '',
    hokenServiceKasanBunrui3: '',
    hokenServiceKasanModifiedCnt: '',
    hindo: '',
    hindoBunrui3: '',
    hindoModifiedCnt: '',
    printFont: '',
    printFontBunrui3: '',
    printFontModifiedCnt: '',
  } as Or10899Type,
  mo00040: {} as Mo00040Type,
})

const localOneway = reactive({
  or10899OneWay: {
    ...props.onewayModelValue,
  },
  // 計画表様式入力
  or17631: { youshiki: '' },
  // 期間の管理入力
  or17630: { kikanKanri: '' },
  // 期間のカレンダー取込入力
  or17629: { kikanCalendar: '' },
  // 保険サービス取込（事業所名）入力
  or17635: { hokenServiceJigyo: '' },
  // 保険サービス取込（加算サービス）入力
  or17634: { hokenServiceKasan: '' },
  // 頻度取込入力
  or17633: { hindo: '' },
  // 印刷フォント入力
  or17628: { printFont: '' },
  // 計画表様式入力
  or17631OneWay: { radioItemsList: [] as CodeType[] } as Or17631OneWayType,
  // 期間の管理入力
  or17630OneWay: { radioItemsList: [] as CodeType[] } as Or17630OneWayType,
  // 期間のカレンダー取込入力
  or17629OneWay: { radioItemsList: [] as CodeType[] } as Or17629OneWayType,
  // 保険サービス取込（事業所名）入力
  or17635OneWay: { radioItemsList: [] as CodeType[] } as Or17635OneWayType,
  // 保険サービス取込（加算サービス）入力
  or17634OneWay: { radioItemsList: [] as CodeType[] } as Or17634OneWayType,
  // 頻度取込入力
  or17633OneWay: { radioItemsList: [] as CodeType[] } as Or17633OneWayType,
  // 印刷フォント入力
  or17628OneWay: { radioItemsList: [] as CodeType[] } as Or17628OneWayType,

  // 閉じるコンポーネント,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609UpdateOneway: {
    btnLabel: t('btn.save'),
    // disabled: !(await hasRegistAuth(Or10899Const.LINK_AUTH)),
  } as Mo00609OnewayType,
  //予防計画書ダイアログ
  mo00024Oneway: {
    width: '810px',
    height: '610px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10899',
      toolbarTitle: t('label.prevention-care-plan-master'),
      toolbarName: 'Or10899ToolBar',
      font: '24px',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'card-text pa-2',
    },
  } as Mo00024OnewayType,
  mo01338Oneway: {
    value: t('label.schedule-template'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Twoway: {
    value: t('label.period-management'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Threeway: {
    value: t('label.period-calendar-import'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Fourway: {
    value: t('label.insurance-service-import-business-name-new-line'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Fiveway: {
    value: t('label.insurance-service-import-additional-services-new-line'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Sixway: {
    value: t('label.frequency-import'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Sevenway: {
    value: t('label.print-font'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Eightway: {
    value: t('label.preserved-by-each-office'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-7',
      itemClass: 'ml-0 align-left',
      itemStyle: 'color:  rgb(var(--v-theme-black-500));',
    }),
  } as Mo01338OnewayType,
  mo00040Oneway: {
    showItemLabel: false,
    itemLabel: t('label.office-selection'),
    itemTitle: 'jigyoKnj',
    itemValue: 'jigyoId',
    width: '180px',
    items: [],
  } as Mo00040OnewayType,
})

const or17631_1 = ref({ uniqueCpId: '' })
const or17630_1 = ref({ uniqueCpId: '' })
const or17629_1 = ref({ uniqueCpId: '' })
const or17635_1 = ref({ uniqueCpId: '' })
const or17634_1 = ref({ uniqueCpId: '' })
const or17633_1 = ref({ uniqueCpId: '' })
const or17628_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10899Const.DEFAULT.IS_OPEN,
})
/**
 * gyoumuCom
 */
const gyoumuCom = useGyoumuCom()
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10899StateType>({
  cpId: Or10899Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10899Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or17631Const.CP_ID(1)]: or17631_1.value,
  [Or17630Const.CP_ID(1)]: or17630_1.value,
  [Or17629Const.CP_ID(1)]: or17629_1.value,
  [Or17635Const.CP_ID(1)]: or17635_1.value,
  [Or17634Const.CP_ID(1)]: or17634_1.value,
  [Or17633Const.CP_ID(1)]: or17633_1.value,
  [Or17628Const.CP_ID(1)]: or17628_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
})

onMounted(async () => {
  // 共通関数より汎用コード一覧を取得する
  await initCodes()
  // 初期情報取得
  await init()
})

/**
 * 初期情報取得
 *
 */
async function init() {
  //予防計画書初期情報取得(IN)
  const inputData: PreventionPlanInEntity = {
    shisetuId: localOneway.or10899OneWay.preventionPlanMasterInData.shisetuId,
    svJigyoId: localOneway.or10899OneWay.preventionPlanMasterInData.jigyoId,
    svJigyoIdList: localOneway.or10899OneWay.preventionPlanMasterInData.svJigyoIdList,
  }

  //予防計画書初期情報取得
  const ret: PreventionPlanOutEntity = await ScreenRepository.select(
    'preventionCarePlanMasterInitSelect',
    inputData
  )
  // 戻り値はテーブルデータとして処理されます
  localOneway.or17631.youshiki = ret.data.preventionCarePlanMasterInfo.youshiki
  localOneway.or17630.kikanKanri = ret.data.preventionCarePlanMasterInfo.kikanKanri
  localOneway.or17629.kikanCalendar = ret.data.preventionCarePlanMasterInfo.kikanCalendar
  localOneway.or17635.hokenServiceJigyo = ret.data.preventionCarePlanMasterInfo.hokenServiceJigyo
  localOneway.or17634.hokenServiceKasan = ret.data.preventionCarePlanMasterInfo.hokenServiceKasan
  localOneway.or17633.hindo = ret.data.preventionCarePlanMasterInfo.hindo
  localOneway.or17628.printFont = ret.data.preventionCarePlanMasterInfo.printFont

  local.or10899.youshiki = ret.data.preventionCarePlanMasterInfo.youshiki
  local.or10899.youshikiBunrui3 = ret.data.preventionCarePlanMasterInfo.youshikiBunrui3
  local.or10899.youshikiModifiedCnt = ret.data.preventionCarePlanMasterInfo.youshikiModifiedCnt
  local.or10899.kikanKanri = ret.data.preventionCarePlanMasterInfo.kikanKanri
  local.or10899.kikanKanriBunrui3 = ret.data.preventionCarePlanMasterInfo.kikanKanriBunrui3
  local.or10899.kikanKanriModifiedCnt = ret.data.preventionCarePlanMasterInfo.kikanKanriModifiedCnt
  local.or10899.kikanCalendar = ret.data.preventionCarePlanMasterInfo.kikanCalendar
  local.or10899.kikanCalendarBunrui3 = ret.data.preventionCarePlanMasterInfo.kikanCalendarBunrui3
  local.or10899.kikanCalendarModifiedCnt =
    ret.data.preventionCarePlanMasterInfo.kikanCalendarModifiedCnt
  local.or10899.hokenServiceJigyo = ret.data.preventionCarePlanMasterInfo.hokenServiceJigyo
  local.or10899.hokenServiceJigyoBunrui3 =
    ret.data.preventionCarePlanMasterInfo.hokenServiceJigyoBunrui3
  local.or10899.hokenServiceJigyoModifiedCnt =
    ret.data.preventionCarePlanMasterInfo.hokenServiceJigyoModifiedCnt
  local.or10899.hokenServiceKasan = ret.data.preventionCarePlanMasterInfo.hokenServiceKasan
  local.or10899.hokenServiceKasanBunrui3 =
    ret.data.preventionCarePlanMasterInfo.hokenServiceKasanBunrui3
  local.or10899.hokenServiceKasanModifiedCnt =
    ret.data.preventionCarePlanMasterInfo.hokenServiceKasanModifiedCnt
  local.or10899.hindo = ret.data.preventionCarePlanMasterInfo.hindo
  local.or10899.hindoBunrui3 = ret.data.preventionCarePlanMasterInfo.hindoBunrui3
  local.or10899.hindoModifiedCnt = ret.data.preventionCarePlanMasterInfo.hindoModifiedCnt
  local.or10899.printFont = ret.data.preventionCarePlanMasterInfo.printFont
  local.or10899.printFontBunrui3 = ret.data.preventionCarePlanMasterInfo.printFontBunrui3
  local.or10899.printFontModifiedCnt = ret.data.preventionCarePlanMasterInfo.printFontModifiedCnt

  // 事業所
  if (ret.data.svJigyoListInfo) {
    localOneway.mo00040Oneway.items = ret.data.svJigyoListInfo
    local.mo00040.modelValue = localOneway.or10899OneWay.preventionPlanMasterInData.jigyoId
  }

  setChildCpBinds(props.uniqueCpId, {
    [Or17631Const.CP_ID(1)]: {
      twoWayValue: {
        youshiki: localOneway.or17631.youshiki,
      },
    },
    [Or17630Const.CP_ID(1)]: {
      twoWayValue: {
        kikanKanri: localOneway.or17630.kikanKanri,
      },
    },
    [Or17629Const.CP_ID(1)]: {
      twoWayValue: {
        kikanCalendar: localOneway.or17629.kikanCalendar,
      },
    },
    [Or17635Const.CP_ID(1)]: {
      twoWayValue: {
        hokenServiceJigyo: localOneway.or17635.hokenServiceJigyo,
      },
    },
    [Or17634Const.CP_ID(1)]: {
      twoWayValue: {
        hokenServiceKasan: localOneway.or17634.hokenServiceKasan,
      },
    },
    [Or17633Const.CP_ID(1)]: {
      twoWayValue: {
        hindo: localOneway.or17633.hindo,
      },
    },
    [Or17628Const.CP_ID(1)]: {
      twoWayValue: {
        printFont: localOneway.or17628.printFont,
      },
    },
  })
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 計画表様式
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_YOUSHIKI },
    // 期間の管理
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PERIOD_MANAGEMENT },
    // 期間のカレンダー取込
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PERIOD_CALENDAR_IMPORT },
    // 保険サービス取込（事業所名）
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_INSURANCE_SERVICE_IMPORT_BUSINESS_NAME },
    // 保険サービス取込（加算サービス）
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_HOKENSERVICEKASAN },
    // 頻度取込
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FREQUENCY_IMPORT },
    // 印刷フォント
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINTFONT },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 計画表様式
  localOneway.or17631OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_YOUSHIKI
  )

  // 期間の管理
  localOneway.or17630OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PERIOD_MANAGEMENT
  ).map((x) => {
    return { ...x, value: x.value.toString() }
  })

  // 期間のカレンダー取込
  localOneway.or17629OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PERIOD_CALENDAR_IMPORT
  )

  // 保険サービス取込（事業所名）
  localOneway.or17635OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_INSURANCE_SERVICE_IMPORT_BUSINESS_NAME
  ).map((x) => {
    return { ...x, value: x.value.toString() }
  })

  // 保険サービス取込（加算サービス）
  localOneway.or17634OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_HOKENSERVICEKASAN
  )

  // 頻度取込
  localOneway.or17633OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_FREQUENCY_IMPORT
  ).map((x) => {
    return { ...x, value: x.value.toString() }
  })

  // 印刷フォント
  localOneway.or17628OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRINTFONT
  )
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    mo00024.value.isOpen = true
    // 画面データに変更がない場合、ダイアログを閉じます
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 事業所を監視
 *
 * @description
 */
watch(
  () => local.mo00040,
  async (newValue) => {
    if (
      newValue?.modelValue &&
      newValue?.modelValue !== localOneway.or10899OneWay.preventionPlanMasterInData.jigyoId
    ) {
      if (
        !(await gyoumuCom.checkEdit(
          isEdit.value,
          await hasRegistAuth(Or10899Const.LINK_AUTH),
          showConfirmMessageBox,
          showWarnMessageBox,
          insert,
          caceljigyoId
        ))
      ) {
        return
      }
      localOneway.or10899OneWay.preventionPlanMasterInData.jigyoId = newValue.modelValue
      await init()
    }
  }
)

function caceljigyoId() {
  local.mo00040.modelValue = localOneway.or10899OneWay.preventionPlanMasterInData.jigyoId
}

/**
 * 実行保存ロジックを実行し、画面を初期化します
 */
async function insert() {
  //変更されている項目がないため、保存を行うことは出来ません。
  // 項目を入力変更してから、再度保存を行ってください。
  if (!(await gyoumuCom.checkEditBySave(isEdit.value, showMessageBox))) {
    return
  }
  await save()
  await init()
}
/**
 * メッセージを表示する
 *
 * @param messageId - メッセージ
 */
const showMessageBox = async (messageId: string) => {
  await openInfoDialog({
    dialogText: t(messageId),
    dialogTitle: t('label.top-btn-title'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
}

/**
 * 保存処理
 */
async function save() {
  const yobouKeikakushoList = [] as PreventionPlanBunrui3[]
  // 計画表様式
  if (
    !local.or10899.youshikiBunrui3 ||
    local.or10899.youshikiBunrui3 === '' ||
    local.or10899.youshiki !== Or17631Logic.data.get(or17631_1.value.uniqueCpId)?.youshiki
  ) {
    const preventionPlanBunrui3 = {
      bunrui3Id: Or10899Const.DEFAULT.YOUSHIKI,
      intValue: Or17631Logic.data.get(or17631_1.value.uniqueCpId)?.youshiki,
      modifiedCnt: local.or10899.youshikiModifiedCnt,
    } as PreventionPlanBunrui3
    yobouKeikakushoList.push(preventionPlanBunrui3)
  }

  // 期間の管理
  if (
    !local.or10899.kikanKanriBunrui3 ||
    local.or10899.kikanKanriBunrui3 === '' ||
    local.or10899.kikanKanri !== Or17630Logic.data.get(or17630_1.value.uniqueCpId)?.kikanKanri
  ) {
    const preventionPlanBunrui3 = {
      bunrui3Id: Or10899Const.DEFAULT.KIKAN_KANRI,
      intValue: Or17630Logic.data.get(or17630_1.value.uniqueCpId)?.kikanKanri,
      modifiedCnt: local.or10899.kikanKanriModifiedCnt,
    } as PreventionPlanBunrui3
    yobouKeikakushoList.push(preventionPlanBunrui3)
  }

  // 期間のカレンダー取込
  if (
    !local.or10899.kikanCalendarBunrui3 ||
    local.or10899.kikanCalendarBunrui3 === '' ||
    local.or10899.kikanCalendar !== Or17629Logic.data.get(or17629_1.value.uniqueCpId)?.kikanCalendar
  ) {
    const preventionPlanBunrui3 = {
      bunrui3Id: Or10899Const.DEFAULT.KIKAN_CALENDAR,
      intValue: Or17629Logic.data.get(or17629_1.value.uniqueCpId)?.kikanCalendar,
      modifiedCnt: local.or10899.kikanCalendarModifiedCnt,
    } as PreventionPlanBunrui3
    yobouKeikakushoList.push(preventionPlanBunrui3)
  }

  // 保険サービス取込（事業所名）
  if (
    !local.or10899.hokenServiceJigyoBunrui3 ||
    local.or10899.hokenServiceJigyoBunrui3 === '' ||
    local.or10899.hokenServiceJigyo !==
      Or17635Logic.data.get(or17635_1.value.uniqueCpId)?.hokenServiceJigyo
  ) {
    const preventionPlanBunrui3 = {
      bunrui3Id: Or10899Const.DEFAULT.HOKEN_SERVICE_JIGYO,
      intValue: Or17635Logic.data.get(or17635_1.value.uniqueCpId)?.hokenServiceJigyo,
      modifiedCnt: local.or10899.hokenServiceJigyoModifiedCnt,
    } as PreventionPlanBunrui3
    yobouKeikakushoList.push(preventionPlanBunrui3)
  }

  // 保険サービス取込（加算サービス）
  if (
    !local.or10899.hokenServiceKasanBunrui3 ||
    local.or10899.hokenServiceKasanBunrui3 === '' ||
    local.or10899.hokenServiceKasan !==
      Or17634Logic.data.get(or17634_1.value.uniqueCpId)?.hokenServiceKasan
  ) {
    const preventionPlanBunrui3 = {
      bunrui3Id: Or10899Const.DEFAULT.HOKEN_SERVICE_KASAN,
      intValue: Or17634Logic.data.get(or17634_1.value.uniqueCpId)?.hokenServiceKasan,
      modifiedCnt: local.or10899.hokenServiceKasanModifiedCnt,
    } as PreventionPlanBunrui3
    yobouKeikakushoList.push(preventionPlanBunrui3)
  }

  // 頻度取込
  if (
    !local.or10899.hindoBunrui3 ||
    local.or10899.hindoBunrui3 === '' ||
    local.or10899.hindo !== Or17633Logic.data.get(or17633_1.value.uniqueCpId)?.hindo
  ) {
    const preventionPlanBunrui3 = {
      bunrui3Id: Or10899Const.DEFAULT.HINDO,
      intValue: Or17633Logic.data.get(or17633_1.value.uniqueCpId)?.hindo,
      modifiedCnt: local.or10899.hindoModifiedCnt,
    } as PreventionPlanBunrui3
    yobouKeikakushoList.push(preventionPlanBunrui3)
  }

  // 印刷フォント
  if (
    !local.or10899.printFontBunrui3 ||
    local.or10899.printFontBunrui3 === '' ||
    local.or10899.printFont !== Or17628Logic.data.get(or17628_1.value.uniqueCpId)?.printFont
  ) {
    const preventionPlanBunrui3 = {
      bunrui3Id: Or10899Const.DEFAULT.PRINT_FONT,
      intValue: Or17628Logic.data.get(or17628_1.value.uniqueCpId)?.printFont,
      modifiedCnt: local.or10899.printFontModifiedCnt,
    } as PreventionPlanBunrui3
    yobouKeikakushoList.push(preventionPlanBunrui3)
  }
  if (
    yobouKeikakushoList.length <= 0 &&
    localOneway.or10899OneWay.preventionPlanMasterInData.jigyoId === local.mo00040.modelValue
  ) {
    return
  }
  const param: PreventionPlanInsertInEntity = {
    shisetuId: localOneway.or10899OneWay.preventionPlanMasterInData.shisetuId,
    svJigyoId: localOneway.or10899OneWay.preventionPlanMasterInData.jigyoId,
    yobouKeikakushoList: yobouKeikakushoList,
  }

  // 保存ロジックを実行します
  await ScreenRepository.insert('preventionCarePlanMasterUpdate', param)
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  if (
    !(await gyoumuCom.checkEdit(
      isEdit.value,
      await hasRegistAuth(Or10899Const.LINK_AUTH),
      showConfirmMessageBox,
      showWarnMessageBox,
      save
    ))
  ) {
    return
  }
  _cleanEditFlag()
  setState({ isOpen: false })
}

/**
 * EditFlag初期化処理（isInit: true）
 *
 */
function _cleanEditFlag() {
  useScreenStore().setCpNavControl({
    cpId: Or17631Const.CP_ID(1),
    uniqueCpId: or17631_1.value.uniqueCpId,
    editFlg: false,
  })
  useScreenStore().setCpNavControl({
    cpId: Or17630Const.CP_ID(1),
    uniqueCpId: or17630_1.value.uniqueCpId,
    editFlg: false,
  })
  useScreenStore().setCpNavControl({
    cpId: Or17629Const.CP_ID(1),
    uniqueCpId: or17629_1.value.uniqueCpId,
    editFlg: false,
  })
  useScreenStore().setCpNavControl({
    cpId: Or17635Const.CP_ID(1),
    uniqueCpId: or17635_1.value.uniqueCpId,
    editFlg: false,
  })
  useScreenStore().setCpNavControl({
    cpId: Or17634Const.CP_ID(1),
    uniqueCpId: or17634_1.value.uniqueCpId,
    editFlg: false,
  })
  useScreenStore().setCpNavControl({
    cpId: Or17633Const.CP_ID(1),
    uniqueCpId: or17633_1.value.uniqueCpId,
    editFlg: false,
  })
  useScreenStore().setCpNavControl({
    cpId: Or17628Const.CP_ID(1),
    uniqueCpId: or17628_1.value.uniqueCpId,
    editFlg: false,
  })
}
/**
 * メッセージを表示する
 */
const showConfirmMessageBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * 確認ダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,

      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}
/**
 * メッセージを表示する
 */
const showWarnMessageBox = async () => {
  return await openWarnDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.w-com-10006'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
}
/**
 * メッセージを表示する
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openWarnDialog(state: Or21815StateType): Promise<Or21815EventType | undefined> {
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet class="view">
        <c-v-row class="operationArea">
          <c-v-col
            cols="auto"
            class="ma-1 pt-1 mr-4"
          >
            <base-at-label :value="t('label.office-selection')" />
          </c-v-col>
          <c-v-col>
            <base-mo00040
              v-model="local.mo00040"
              :oneway-model-value="localOneway.mo00040Oneway"
              v-bind="{ ...$attrs }"
            />
          </c-v-col>
        </c-v-row>

        <c-v-row class="subSection">
          <c-v-col class="sectionHeader">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Oneway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or17631
              v-bind="or17631_1"
              :oneway-model-value="localOneway.or17631OneWay"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Twoway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or17630
              v-bind="or17630_1"
              :oneway-model-value="localOneway.or17630OneWay"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Threeway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or17629
              v-bind="or17629_1"
              :oneway-model-value="localOneway.or17629OneWay"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Fourway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or17635
              v-bind="or17635_1"
              :oneway-model-value="localOneway.or17635OneWay"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Fiveway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or17634
              v-bind="or17634_1"
              :oneway-model-value="localOneway.or17634OneWay"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Sixway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or17633
              v-bind="or17633_1"
              :oneway-model-value="localOneway.or17633OneWay"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Sevenway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or17628
              v-bind="or17628_1"
              :oneway-model-value="localOneway.or17628OneWay"
            />
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
      <c-v-row
        no-gutters
        class="label-comment"
      >
        <c-v-col style="padding: 0px 8px 0px 8px !important">
          <base-mo01338 :oneway-model-value="localOneway.mo01338Eightway" /> </c-v-col
      ></c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.screen-close')"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609UpdateOneway"
          class="ml-2"
          @click="insert"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.save')"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- データ変更確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <g-base-or21815 v-bind="or21815_1" />
</template>

<style scoped>
.view {
  min-height: 280px;
  min-width: 520px;
  display: flex;
  margin: 0px;
  padding: 0px;
  flex-direction: column;

  .v-row {
    margin: unset;
  }

  .operationArea {
    flex: 0 0 auto;

    .v-col {
      padding-left: 0;
      padding-right: 0;
      padding-top: 0;
      padding-bottom: 8px;
    }

    .v-col:last-child {
      text-align: right;
    }
  }

  .subSection {
    border: solid thin rgb(var(--v-theme-light));
    .sectionHeader {
      display: flex;
      flex-direction: column;
      min-width: 100px;
      max-width: 165px;
      justify-content: center;
      align-items: left;
      padding: 0;
      --v-theme-overlay-multiplier: var(--v-theme-background-overlay-multiplier);
      background-color: rgb(var(--v-theme-background)) !important;
      color: rgb(var(--v-theme-on-background)) !important;
      font-weight: bold;

      span {
        width: 100%;
        padding-left: 8px;
        font-size: 14px;
      }

      small {
        width: 100%;
        padding-left: 8px;
        font-weight: normal;
      }
    }
  }

  .sectionContent {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 8px !important;

    .v-sheet {
      width: 100%;

      :deep(.radio-group) {
        width: 100%;

        .v-col-auto {
          width: 100%;
        }
      }
    }
  }
}

.buttonArea {
  button:not(:nth-child(1)) {
    margin-left: 8px;
  }
}
.label-comment {
  color: rgb(var(--v-theme-black-536)) !important;
  margin: 0px 0px 0px 0px;
  height: 20px;
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: 130px;
}
</style>
