import { getSequencedCpId } from '~/utils/useScreenUtils'
/**
 * Or01997:有機体:［アセスメント］画面（居宅）（7まとめ）
 * GUI00805_［アセスメント］画面（居宅）（7まとめ）
 *
 * @description
 * ［アセスメント］画面（居宅）（7まとめ）
 *
 * <AUTHOR>
 */

export namespace Or01997Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or01997', seq)

  export namespace DEFAULT {
    /**
     * 基本動作区分ID
     */
    export const TAB_ID = '12'

    /**
     * 改定フラグ H21/4
     */
    export const KAITEI_FLG_H21 = '4'
    /**
     * 改定フラグ R3/4
     */
    export const KAITEI_FLG_R34 = '5'

    /**
     * 複写フラグ
     */
    export const MODE_COPY = 'copy'

    /**
     * 入力支援画面返却値タイプ：「0：追加」
     */
    export const INPUT_SUPPORT_ADD = '0'

    /**
     * 入力支援画面返却値タイプ；「1：上書き」
     */
    export const INPUT_SUPPORT_OVERWRITE = '1'

    /**
     * デフォルト値
     */
    export const DEFAULT_VALUE = '0'
  }
}
