<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or53417Logic } from '~/components/custom-components/organisms/Or53417/Or53417.logic'
import { Or53417Const } from '~/components/custom-components/organisms/Or53417/Or53417.constants'
import type { Or53417OnewayType } from '~/types/cmn/business/components/Or53417Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'


definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00627'
// ルーティング
const routing = 'GUI00627/pinia'
// 画面物理名
const screenName = 'GUI00627'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or53417 = ref({ uniqueCpId: '' })

const mo00039OneWay = ref({
  name: '',
  showItemLabel: false,
  inline: false,
  items: [
    {
      label: 'GUI00627_アセスメント（インターライ）マスタ',
      value: '0',
    },
  ],
} as Mo00039OnewayType)

const mo00039Type = ref<string>('0')

const mo00046OneWayType = ref({
  showItemLabel: false,
} as Mo00046OnewayType)

const or53417Data = ref({
  shisetuId: '1',
  svJigyoId: '1',
  type: '3'
}as Or53417OnewayType)

const info = ref<{ value: string }>({
  value: JSON.stringify(or53417Data.value, null, 2),
})

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00627' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00627',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or53417Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or53417Const.CP_ID(1)]: or53417.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or53417Logic.initialize(or53417.value.uniqueCpId)
}

/**
 *  ボタン押下時の処理(Or53417)
 *
 */
function onClickOr53417() {
  // Or53417のダイアログ開閉状態を更新する
  Or53417Logic.state.set({
    uniqueCpId: or53417.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr53417 = computed(() => {
  // Or53417のダイアログ開閉状態
  return Or53417Logic.state.get(or53417.value.uniqueCpId)?.isOpen ?? false
})

function onClick() {
  or53417Data.value = JSON.parse(info.value.value) as Or53417OnewayType
  Or53417Logic.state.set({
    uniqueCpId: or53417.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr53417"
        >GUI00627_アセスメント（インターライ）マスタ
      </v-btn>
      <g-custom-or-53417
        v-if="showDialogOr53417"
        v-bind="or53417"
        :oneway-model-value="or53417Data"
      />
    </c-v-col>
  </c-v-row>

  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row
    no-gutters
    class="pb-2 pl-2"
  >
    <base-mo00039
      v-model="mo00039Type"
      :oneway-model-value="mo00039OneWay"
      class="bg-transparent"
    >
    </base-mo00039>
  </c-v-row>
  <c-v-row
    no-gutters
    class="content pb-2"
  >
    <div class="w-25 h-75 pl-2">
      <div>データ</div>
      <base-mo00046
        v-model="info"
        :oneway-model-value="mo00046OneWayType"
      />
    </div>
    <div class="pl-2 pt-5">
      <v-btn @click="onClick()"> GUI00627_ 疎通起動 </v-btn>
    </div>
  </c-v-row>
</template>
<style scoped lang="scss">
.content {
  background: rgb(var(--v-theme-background));
}
.bg-transparent {
  background: transparent !important;
}
</style>
