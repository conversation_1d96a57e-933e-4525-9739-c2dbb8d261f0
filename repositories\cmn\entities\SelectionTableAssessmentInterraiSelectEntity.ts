import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 選択テーブルアセスメントInterRAIのエンティティを表します。
 */
export interface SelectionTableAssessmentInterraiInEntity extends InWebEntity {
  /**
   * RAIの識別子を表します。
   */
  raiId: string
}

/**
 * 選択テーブルアセスメントInterRAIの出力エンティティを表します。
 */
export interface SelectionTableAssessmentInterraiOutEntity extends OutWebEntity {
  /**
   * data
   */
  data: {
    cpnTucRaiList: {
      raiId: string
      scaleBmi: string
      scaleDrs: string
      scalePs: string
      scaleCps: string
      scaleCpsNm: string
      scaleAdlh: string
      scaleAdlhNm: string
      cap1: string
      cap1Nm: string
      cap2: string
      cap2Nm: string
      cap3: string
      cap3Nm: string
      cap4: string
      cap4Nm: string
      cap5: string
      cap5Nm: string
      cap6: string
      cap6Nm: string
      cap7: string
      cap7Nm: string
      cap8: string
      cap8Nm: string
      cap9: string
      cap9Nm: string
      cap10: string
      cap10Nm: string
      cap11: string
      cap11Nm: string
      cap12: string
      cap12Nm: string
      cap13: string
      cap13Nm: string
      cap14: string
      cap14Nm: string
      cap15: string
      cap15Nm: string
      cap16: string
      cap16Nm: string
      cap17: string
      cap17Nm: string
      cap18: string
      cap18Nm: string
      cap19: string
      cap19Nm: string
      cap20: string
      cap20Nm: string
      cap21: string
      cap21Nm: string
      cap22: string
      cap22Nm: string
      cap23: string
      cap23Nm: string
      cap24: string
      cap24Nm: string
      cap25: string
      cap25Nm: string
      cap26: string
      cap26Nm: string
      cap27: string
      cap27Nm: string
    }
  }
}

/**
 * InterRAI Screen Dの入力エンティティの更新を表します。
 */
export interface AssessmentInterRAIScreenVInEntityUpdate extends InWebEntity {
  /**
   * アセスメントタイプ
   */
  assType: string
  /**
   * 削除区分
   */
  deleteKbn: string
  /**
   * 履歴修正回数
   */
  historyModifiedCnt: string
  /**
   * 履歴更新区分
   */
  historyUpdateKbn: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 基準日
   */
  kijunbiYmd: string
  /**
   * アセスメントID
   */
  raiId: string
  /**
   * 作成者ID
   */
  sakuseiId: string
  /**
   * SC1 ID
   */
  sc1Id: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * サブ区分
   */
  subKbn: string
  /**
   * サービス事業ID
   */
  svJigyoId: string
  /**
   * 更新区分
   */
  updateKbn: string
  /**
   * ユーザーID
   */
  userId: string
}

/**
 * InterRAI Screen Dの出力エンティティの更新を表します。
 */
export interface AssessmentInterRAIScreenVOutEntityUpdate extends OutWebEntity {
  /**
   * ステータス
   * 処理結果のステータスを表します。
   */
  status: string
}
