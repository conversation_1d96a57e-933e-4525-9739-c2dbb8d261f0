<script setup lang="ts">
import type { ComputedRef } from 'vue'
import { ref, reactive, computed, watch, onMounted} from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import { Or28455Const } from '../Or28455/Or28455.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or28455Logic } from '../Or28455/Or28455.logic'
import { Or27835Logic } from '../Or27835/Or27835.logic'
import { Or27835Const } from '../Or27835/Or27835.constants'
import { Or31308Const } from '../Or31308/Or31308.constants'
import { OrX0107Const } from './OrX0107.constants'
import type { OrX0107StateType } from './OrX0107.type'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01278OnewayType } from '~/types/business/components/Mo01278Type'
import { DIALOG_BTN, UPDATE_KBN } from '~/constants/classification-constants'
import type {
  OrX0107Type,
  GamenRirekiOtherInfo,
  GamenDataInfo,
  OrX0107OnewayType,
} from '~/types/cmn/business/components/OrX0107Type'
import {
  useSystemCommonsStore,
  useScreenTwoWayBind,
  useSetupChildProps,
  useGyoumuCom,
  useScreenOneWayBind,
} from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { OrX0002OnewayType } from '~/types/cmn/business/components/OrX0002Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or28455OnewayType, Or28455Type } from '~/types/cmn/business/components/Or28455Type'
import type { Or27835Type, Or27835OnewayType } from '~/types/cmn/business/components/Or27835Type'
import { Or50429Logic } from '~/components/custom-components/organisms/Or50429/Or50429.logic'
import { Or50429Const } from '~/components/custom-components/organisms/Or50429/Or50429.constants'
import type { Or50429OneWayType, Or50429Type } from '~/types/cmn/business/components/Or50429Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { OrX0163OnewayType, OrX0163Type } from '~/types/cmn/business/components/OrX0163Type'
import type { OrX0156OnewayType, OrX0156Type } from '~/types/cmn/business/components/OrX0156Type'
import type { OrX0185OnewayType, OrX0185Type } from '~/types/cmn/business/components/OrX0185Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo01354Items, Mo01354OnewayType, Mo01354Type } from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { useValidation } from '@/utils/useValidation'
import type { OrX0218OnewayType } from '~/types/cmn/business/components/OrX0218Type'
/**
 * OrX0107:有機体:操作ボタンエリア、テーブルエリア
 * GUI00946_実施計画～③
 *
 * @description
 * 操作ボタンエリア、テーブルエリア
 *
 * <AUTHOR>
 */

/**
 * 国際化対応の翻訳関数（i18n）を取得
 */
const { t } = useI18n()
const { byteLength } = useValidation()
const form = ref<VForm>()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}
/**
 * 引継情報を取得する
 */
const props = defineProps<Props>()
/**
 *システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 *共有処理
 */
const gyoumuCom = useGyoumuCom()

/**
 *各コンポーネントに渡すonewayデータ
 */
const localOneway = reactive({
  orX0107Oneway: {} as OrX0107OnewayType,
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    tooltipText: t('tooltip.care-plan2-newline-btn'),
    tooltipLocation: 'bottom',
    minWidth: '89px',
  } as Mo00611OnewayType,
  mo00611OnewayInsert: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    tooltipText: t('tooltip.care-plan2-insertline-btn'),
    tooltipLocation: 'bottom',
    minWidth: '89px',
  } as Mo00611OnewayType,
  mo00611OnewayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    tooltipText: t('tooltip.care-plan2-cpyline-btn'),
    tooltipLocation: 'bottom',
    minWidth: '89px',
    disabled: true,
  } as Mo00611OnewayType,
  mo00611OnewayDelete: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    color: 'error',
    labelColor: 'error',
    tooltipText: t('tooltip.care-plan2-deleteline-btn'),
    tooltipLocation: 'bottom',
    minWidth: '89px',
    disabled: true,
  } as Mo00611OnewayType,
  // 行削除の確認ダイアログ
  orX0002DeleteLineOneway: {
    message: t('message.i-cmn-10219'),
  } as OrX0002OnewayType,
  // 表示順
  mo00611OnewayOrder: {
    btnLabel: t('btn.display-order'),
    disabled: false,
    tooltipText: t('tooltip.care-plan2-display-order-icon-btn'),
    tooltipLocation: 'bottom',
    minWidth: '65px',
  } as Mo00611OnewayType,
  // データ-ページング
  mo01338Oneway: {
    unit: t('label.item'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-1',
      itemClass: 'ml-4 align-center',
    }),
  } as Mo01338OnewayType,
  // GUI00937 入力支援［ケアマネ］画面
  or51775Oneway: {} as Or51775OnewayType,
  // GUI00949_課題・目標取込
  or28455Oneway: {} as Or28455OnewayType,
  // GUI00967 表示順変更実施計画～③(支援内容)画面
  or27835Oneway: {} as Or27835OnewayType,
  // GUI00957 サービス種別入力支援画面
  or50429Oneway: { serviceKind: '' } as Or50429OneWayType,
  // No
  mo01278Oneway: {
    max: 99,
    min: -9,
    maxLength: '2',
    rules: [byteLength(2)],
    isEditCamma: false,
  } as Mo01278OnewayType,
  // 総合的課題
  orX0185Oneway: {
    height: '82px',
    maxlength: '32000',
    rules: [byteLength(32000)],
    contentStyle:'padding:8px'
  } as OrX0185OnewayType,
  // 目標
  orX0185OnewayMokuhyo: {
    height: '331px',
    maxlength: '32000',
    rules: [byteLength(32000)],
    contentStyle:'padding:8px'
  } as OrX0185OnewayType,
  // 支援計画テキストエリア
  orX0163OnewayShienKeikaku: {
    height: '287px',
    width: '284px',
    maxlength: '32000',
    rules: [byteLength(32000)],
    contentStyle:'padding:8px'
  } as OrX0163OnewayType,
  // 一覧項目
  orX0163Oneway: {
    height:'50px',
    maxlength: '32000',
    rules: [byteLength(32000)],
    contentStyle:'padding:8px'
  } as OrX0163OnewayType,
  orX0218Oneway: {
    showEditHelperBtnFlg: true,
    showKikanBtnFlg: false,
    showYmdBtnFlg: true,
    showMonthDayBtnFlg: true,
    height:'50px',
    maxlength: '32000',
    rules: [byteLength(32000)],
    contentStyle:'padding:8px',
  } as OrX0218OnewayType,
  // 処遇実施上の留意点
  orX0156Oneway: {
    itemLabel: t('label.service-implementation-considerations'),
    showItemLabel: true,
    isVerticalLabel: true,
    showDividerLineFlg: false,
    rows: '2',
    maxlength: '32000',
    rules: [byteLength(32000)],
    customClass: new CustomClass({ labelClass: 'service-label pt-6 pb-4' }),
  } as OrX0156OnewayType,
  // 障害者等支援加算ラジオボタン
  mo00039OnewaySupport: {
    // デフォルト値の設定
    name: 'special-support',
    itemLabel: t('label.special-support'),
    inline: true,
    disabled:false,
    items: [],
    customClass: new CustomClass({ outerClass: 'd-flex align-center' }),
  } as Mo00039OnewayType,
  mo01354Oneway: {
    headers: OrX0107Const.INIT_HEADERS,
    columnMinWidth: { columnWidths: [40,512,208, 208, 208] } as ResizableGridBinding,
    height: '100%',
  } as Mo01354OnewayType,
  // ラベル
  mo00615Oneway: {
    itemLabel: t('label.support_naiyo'),
    customClass: new CustomClass({
      outerStyle: 'padding: 0; min-height: 32px; display: flex; align-items: center;',
      labelClass: 'ma-0 pa-0',
    }),
    itemLabelFontWeight: 'normal',
    itemLabelFontColor: undefined,
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size: 13px; line-height: 16px; padding: 0;',
    }),
  } as Mo00615OnewayType,
})

/**
 *ローカル状態管理
 */
const local = reactive({
  orX0002DeleteLineDialog: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
  orX0107: { ymdKbn: '' } as OrX0107StateType,
  // GUI00949_課題・目標取込
  or28455: { items: [], operationKbn: '' } as Or28455Type,
  // GUI00937 入力支援［ケアマネ］画面
  or51775: {} as Or51775Type,
  // GUI00967 表示順変更実施計画～③(支援内容)画面
  or27835: { sortList: [] } as Or27835Type,
  // GUI00957 サービス種別入力支援画面
  or50429: {} as Or50429Type,
  // 処遇実施上の留意点
  orX0156: { value: '' } as OrX0156Type,
  // 表
  mo01354TbData: {
    values: {
      selectedRowId: '',
      selectedRowIds: [],
      items: [] as Mo01354Items[],
    },
  } as Mo01354Type,
})

const or28455_1 = ref({ uniqueCpId: '' })
const or51775_1 = ref({ uniqueCpId: '' })
const or27835_1 = ref({ uniqueCpId: '' })
const or50429_1 = ref({ uniqueCpId: '' })
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or28455Const.CP_ID(1)]: or28455_1.value,
  [Or51775Const.CP_ID(1)]: or51775_1.value,
  [Or27835Const.CP_ID(1)]: or27835_1.value,
  [Or50429Const.CP_ID(1)]: or50429_1.value,
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr28455 = computed(() => {
  // Or28455のダイアログ開閉状態
  return Or28455Logic.state.get(or28455_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr27835 = computed(() => {
  // Or27835のダイアログ開閉状態
  return Or27835Logic.state.get(or27835_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr50429 = computed(() => {
  // Or50429のダイアログ開閉状態
  return Or50429Logic.state.get(or50429_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<OrX0107Type>({
  cpId: OrX0107Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

useScreenOneWayBind<OrX0107OnewayType>({
  cpId: OrX0107Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    // 操作区分
    operaFlg: (value) => {
      if (value) {
        localOneway.orX0107Oneway.operaFlg = value
      }
    },
    // ページング区分
    pagingFlg: (value) => {
      if (value) {
        localOneway.orX0107Oneway.pagingFlg = value
      }
    },
    // 種別ID
    syubetuId: (value) => {
      if (value) {
        localOneway.orX0107Oneway.syubetuId = value
      }
    },
    // 期間管理フラグ(1:期間管理する、0:期間管理しない)
    kikanFlg: (value) => {
      if (value) {
        localOneway.orX0107Oneway.kikanFlg = value
      }
    },
    // 課題取込セキュリティチェック（0:権限なし、1以上:権限あり）
    kadaiTorikomiFlg: (value) => {
      if (value) {
        localOneway.orX0107Oneway.kadaiTorikomiFlg = value
      }
    },
    // 初期設定マスタの情報
    initMasterObj: (value) => {
      if (value) {
        localOneway.orX0107Oneway.initMasterObj = value
        // 期間のカレンダー取込み※0:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日
        const ymdFlg = value.pkkak23YmdFlg;
        localOneway.orX0218Oneway.kikanFlg = ymdFlg === '0' || ymdFlg === '1';
        if (ymdFlg === '1' || ymdFlg === '3') {
          localOneway.orX0218Oneway.dateFormat = 'YYYY年MM月DD日';
          localOneway.orX0218Oneway.mdDateFormat = 'MM月DD日';
        } else {
          localOneway.orX0218Oneway.dateFormat = 'YYYY/MM/DD';
          localOneway.orX0218Oneway.mdDateFormat = 'MM/DD';
        }
      }
    },
    // 実施計画複写
    isCopyMode: (value) => {
      localOneway.orX0107Oneway.isCopyMode = value ?? false
    },
    // 履歴ID
    copyRirekiId: (value) => {
      localOneway.orX0107Oneway.copyRirekiId = value ?? ''
    },
  },
})

onMounted(async () => {
  await initCodes()
})

const filteredDataList = computed(() => {
  return (
    refValue.value?.dataList?.filter(
      (i: GamenDataInfo) => i.updateKbn !== UPDATE_KBN.DELETE
    ) ?? []
  )
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  await CmnSystemCodeRepository.getCodes({
    selectCodeKbnList: [{ mCdKbnId: CmnMCdKbnId.M_DISABILITY_SUPPORT_ADD }],
  })
  localOneway.mo00039OnewaySupport.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_DISABILITY_SUPPORT_ADD
  )
}

/**
 *サブセクション(総合的課題・目標・支援計画)の各項目とサービス実施上の留意点の取得、設定
 */
const rirekiOtherFields: Extract<keyof GamenRirekiOtherInfo, string>[] = [
  'kadaiUndoKnj',
  'kadaiNichijoKnj',
  'kadaiShakaiKnj',
  'kadaiKenkoKnj',
  'mokuhyoKnj',
]
type RirekiOtherComputed = {
  [K in keyof GamenRirekiOtherInfo]: ComputedRef<OrX0185Type>
}
/**
 *サブセクション(総合的課題・目標・支援計画)の各項目の取得、設定
 */
const rirekiOtherBindings = {} as RirekiOtherComputed
rirekiOtherFields.forEach((field: keyof GamenRirekiOtherInfo) => {
  rirekiOtherBindings[field] = computed<OrX0185Type>({
    get() {
      return {
        value: refValue.value?.rirekiOtherData?.[field] ?? '',
      }
    },
    set(newValue: OrX0185Type) {
      if (!refValue.value?.rirekiOtherData) return

      refValue.value.rirekiOtherData[field] = newValue.value
    },
  })
})
/**
 *サブセクション(総合的課題・目標・支援計画)の各項目
 */
const {
  kadaiUndoKnj,
  kadaiNichijoKnj,
  kadaiShakaiKnj,
  kadaiKenkoKnj,
  mokuhyoKnj,
} = rirekiOtherBindings

/**
 * 新規用のidを取得
 */
let newNegativeIdCounter = -1
const getNewNegativeId = (): number => newNegativeIdCounter--

/**
 * 支援計画
 */
const shienKeikakuKnj = computed({
  get(): OrX0163Type {
    return { value: refValue.value?.rirekiOtherData?.shienKeikakuKnj ?? '' }
  },
  set(val: OrX0163Type) {
    refValue.value!.rirekiOtherData.shienKeikakuKnj = String(val.value)
  },
})


/**
 * 処遇実施上の留意点
 */
const ryuiKnjModel = computed({
  get(): OrX0156Type {
    return { value: refValue.value?.rirekiOtherData?.ryuiKnj ?? '' }
  },
  set(val: OrX0156Type) {
    refValue.value!.rirekiOtherData.ryuiKnj = String(val.value)
  },
})

/**
 *障害者等支援加算
 */
const shogaiKasanFlg = computed({
  get: () => refValue.value?.rirekiOtherData?.shogaiKasanFlg ?? '',
  set: (val) => {
    if (refValue.value?.rirekiOtherData) {
      refValue.value.rirekiOtherData.shogaiKasanFlg = val
    }
  },
})


/**
 *一覧に表示レコードの表示順の再設定
 */
const updateSeq = () => {
  filteredDataList.value.forEach((item, index) => {
    const dataItem = refValue.value?.dataList.find(i => i.id === item.id) ?? null
    if (!dataItem) return

    const newSeq = index + 1
    if (dataItem.seq !== newSeq) {
      dataItem.seq = newSeq
      handleChange(dataItem)
    }
  })
}

/**
 *行挿入,行複写,行削除の活性制御
 */
const disableButtons = computed(() => {
  return !local.mo01354TbData.values.selectedRowId
})

/**
 *操作区分 = 3:削除の場合、or 表示用「計画対象期間」情報.ページング区分が0:なしの場合、操作不可
 */
const doFlg = computed(
  (): boolean =>
    localOneway.orX0107Oneway.operaFlg !== Or31308Const.OPERA_FLG_3 &&
    localOneway.orX0107Oneway.pagingFlg !== Or31308Const.PAGING_0
)

/**
 *目標取込制御
 */
const showGoalImport = computed(
  (): boolean => localOneway.orX0107Oneway.kadaiTorikomiFlg === Or31308Const.KADAI_TORIKOMI_1
)

/**
 *実施計画～①複写の制御
 */
const isCopyMode = computed(() => {
  return localOneway.orX0107Oneway.isCopyMode ?? false
})
/**
 * 表示・非表示制御
 */
watch(
  () => isCopyMode.value,
  (newValue) => {
    // 総合的課題
    localOneway.orX0185Oneway.readonly = newValue
    localOneway.orX0185Oneway.showEditBtnFlg = !newValue
    localOneway.orX0185OnewayMokuhyo.readonly = newValue
    localOneway.orX0185OnewayMokuhyo.showEditBtnFlg = !newValue
    localOneway.orX0163OnewayShienKeikaku.readonly = newValue
    localOneway.orX0163OnewayShienKeikaku.showEditBtnFlg = !newValue

    // 一覧明細項目
    localOneway.orX0163Oneway.showEditBtnFlg = !newValue
    localOneway.orX0163Oneway.readonly = newValue

    localOneway.orX0218Oneway.showEditHelperBtnFlg = !newValue
    localOneway.orX0218Oneway.showYmdBtnFlg = !newValue
    localOneway.orX0218Oneway.showMonthDayBtnFlg = !newValue
    localOneway.orX0218Oneway.showCalendarBtnFlg = !newValue
    localOneway.orX0218Oneway.readonly = newValue

    // 処遇実施上の留意点
    localOneway.orX0156Oneway.showEditBtnFlg = !newValue
    localOneway.orX0156Oneway.readonly = newValue
    // 障害者等支援加算ラジオボタンの活性制御
    localOneway.mo00039OnewaySupport.disabled = newValue
  },
  { immediate: true }
)

/**
 *サブセクション(総合的課題・目標・支援計画)の表示制御
 */
const customTableShowFlg = computed(() => {
  if (
    doFlg.value &&
    (!isCopyMode.value || (isCopyMode.value && localOneway.orX0107Oneway.copyRirekiId))
  ) {
    return true
  }
  return false
})

/**
 * 一覧の監視
 */
watch(
  () => filteredDataList.value,
  (newItems) => {
    local.mo01354TbData.values.items = newItems

    if (newItems.length === 0) {
      local.mo01354TbData.values.selectedRowId = ''
      local.mo01354TbData.values.selectedRowIds = []
    } else {
      const firstItemId = newItems[0].id
      const selectedId = local.mo01354TbData.values.selectedRowId
      const hasSelectedId = selectedId !== null && selectedId !== ''

      if (hasSelectedId) {
        const isSelectedIdValid = newItems.some(item => item.id === selectedId)
        if (!isSelectedIdValid) {
          local.mo01354TbData.values.selectedRowId = firstItemId
          local.mo01354TbData.values.selectedRowIds = [firstItemId]
        }
      } else {
        local.mo01354TbData.values.selectedRowId = firstItemId
        local.mo01354TbData.values.selectedRowIds = [firstItemId]
      }
    }
    updateSeq()
  },
  { immediate: true }
)

// 「行削除ボタン」押下メッセージの監視
watch(
  () => local.orX0002DeleteLineDialog.emitType,
  (value) => {
    if (value === DIALOG_BTN.OK) {
      const { selectedRowId } = local.mo01354TbData.values
      if (!selectedRowId || !refValue.value) return

      const currentVisibleItems = filteredDataList.value
      const currentIndex = currentVisibleItems.findIndex(item => item.id === selectedRowId)
      if (currentIndex === -1) return

      const dataItem = refValue.value.dataList.find(i => i.id === selectedRowId)
      if (!dataItem) return

      dataItem.updateKbn = UPDATE_KBN.DELETE

      const newVisibleItems = refValue.value.dataList
        .filter(item => item.updateKbn !== UPDATE_KBN.DELETE)
        .map(item => ({ ...item}))

      // 削除した後データが無し
      let newIndex = -1
      if (newVisibleItems.length > 0) {
        if (currentIndex < newVisibleItems.length) {
          // 次レコードを選択
          newIndex = currentIndex
        } else {
          // 前のレコードを選択
          newIndex = currentIndex - 1
        }
      }

      local.mo01354TbData.values.selectedRowId =
        newIndex >= 0 ? newVisibleItems[newIndex]?.id ?? '' : ''

      local.orX0002DeleteLineDialog.mo00024.isOpen = false
    }
  }
)

/**
 * GUI00967 表示順変更実施計画～③(支援内容)ポップアップ画面で返回
 */
watch(
  () => local.or27835.sortList,
  (newValue) => {
    if (newValue) {
      for (const newItem of newValue) {
        if (newItem.sortBackup) {
          const sortBackup = newItem.sortBackup
          const data = refValue.value!.dataList.find((x) => x.id === sortBackup)
          if (data) {
            data.seq = newItem.sort
            // 更新区分を更新
            handleChange(data)
          }
        }
      }
      refValue.value!.dataList = [...refValue.value!.dataList].sort((a, b) => a.seq - b.seq)
      const visibleItems = filteredDataList.value
      local.mo01354TbData.values.selectedRowId = visibleItems.length > 0 ? visibleItems[0].id : ''
    }
  }
)

/**
 * GUI00949 課題・目標取込画面の返回値がある場合
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => local.or28455,
  (newValue) => {
    if (!newValue) return

    const mapping: Record<string, keyof GamenRirekiOtherInfo> = {
      [OrX0107Const.STR_KADAI_UNDO_KNJ]: 'kadaiUndoKnj',
      [OrX0107Const.STR_KADAI_NICHIJO_KNJ]: 'kadaiNichijoKnj',
      [OrX0107Const.STR_KADAI_SHAKAI_KNJ]: 'kadaiShakaiKnj',
      [OrX0107Const.STR_KADAI_KENKO_KNJ]: 'kadaiKenkoKnj',
      [OrX0107Const.STR_MOKUHYO_KNJ]: 'mokuhyoKnj',
    }

    const key = mapping[local.orX0107.openGUI00949Kbn]

    // 上書の場合、設定対象をクリアする
    if (newValue.operationKbn === OrX0107Const.OPERATION_KBN_1) {
      if (key && refValue.value?.rirekiOtherData) {
        refValue.value.rirekiOtherData[key] = ''
      }
    }

    // 課題
    if (newValue.items.length > 0) {
      // 複数件の場合、空白行を除く、「改行」で連結
      const validIssues = newValue.items
        .map((item) => item.issues)
        .filter((issue) => issue && issue.trim() !== '')
      const combinedIssues = validIssues.join('\n')

      if (key && refValue.value?.rirekiOtherData) {
        const currentVal = refValue.value.rirekiOtherData[key]
        if (!currentVal) {
          refValue.value.rirekiOtherData[key] = combinedIssues
        } else {
          // 設定対象が空白以外の場合、「改行」で連結する
          refValue.value.rirekiOtherData[key] = `${currentVal}\n${combinedIssues}`
        }
      }
    }
  }
)

/**
 * 項目を選択
 *
 * @param selectItemName - 選択されている項目名
 *
 * @param targetColumn - 選択されている項目
 */
function handleFocus(selectItemName: string, targetColumn: number) {
  refValue.value!.selectItemName = selectItemName
  local.orX0107.targetColumn = targetColumn

  let selectItemValue = ''

  // 支援内容一覧
  if (targetColumn >= 7 && targetColumn <= 11) {
    const { selectedRowId } = local.mo01354TbData.values
    if (selectedRowId && refValue.value) {
      const data = filteredDataList.value.find(i => i.id === selectedRowId)
      if (data) {
        switch (targetColumn) {
          case 7:
            selectItemValue = data.bangou.value ?? ''
            break
          case 8:
            selectItemValue = data.svNaiyoKnj.value ?? ''
            break
          case 9:
            selectItemValue = data.svShuKnj.value ?? ''
            break
          case 10:
            selectItemValue = data.jigyoNameKnj.value ?? ''
            break
          case 11:
            selectItemValue = data.kikanKnj.value ?? ''
            break
          default:
            break
        }
      }
    }
  } else {
    // サブセクション(総合的課題・目標・支援計画)
    switch (targetColumn) {
      case 1:
        selectItemValue = refValue.value!.rirekiOtherData.kadaiUndoKnj ?? ''
        break
      case 2:
        selectItemValue = refValue.value!.rirekiOtherData.kadaiNichijoKnj ?? ''
        break
      case 3:
        selectItemValue = refValue.value!.rirekiOtherData.kadaiShakaiKnj ?? ''
        break
      case 4:
        selectItemValue = refValue.value!.rirekiOtherData.kadaiKenkoKnj ?? ''
        break
      case 5:
        selectItemValue = refValue.value!.rirekiOtherData.mokuhyoKnj ?? ''
        break
      case 6:
        selectItemValue = refValue.value!.rirekiOtherData.shienKeikakuKnj ?? ''
        break
      case 12:
        selectItemValue = refValue.value!.rirekiOtherData.ryuiKnj ?? ''
        break
      default:
        break
    }
  }

  refValue.value!.selectItemValue = selectItemValue
}

/**
 * AC010-5「ケース取込」ポップアップ画面での返回値を監視
 *
 * @param newValue - 返回値
 */
function doCaseImport(newValue: string) {
  const targetColumn = local.orX0107.targetColumn ?? -1

  if (targetColumn < 0) {
    return
  }

  // 支援内容一覧
  if (targetColumn >= 7 && targetColumn <= 11) {
    const { selectedRowId } = local.mo01354TbData.values
    if (!selectedRowId || !refValue.value) return

    const data = filteredDataList.value.find(i => i.id === selectedRowId)
    if (!data) return

    // 更新区分を更新
    handleChange(data)
    switch (targetColumn) {
      case 7:
      case 8:
        data.svNaiyoKnj.value = newValue
        break
      case 9:
        data.svShuKnj.value = newValue
        break
      case 10:
        data.jigyoNameKnj.value = newValue
        break
      case 11:
        data.kikanKnj.value = newValue
        break
      default:
        break
    }
  } else {
    // サブセクション(総合的課題・目標・支援計画)
    switch (targetColumn) {
      case 1:
        refValue.value!.rirekiOtherData.kadaiUndoKnj = newValue
        break
      case 2:
        refValue.value!.rirekiOtherData.kadaiNichijoKnj = newValue
        break
      case 3:
        refValue.value!.rirekiOtherData.kadaiShakaiKnj = newValue
        break
      case 4:
        refValue.value!.rirekiOtherData.kadaiKenkoKnj = newValue
        break
      case 5:
        refValue.value!.rirekiOtherData.mokuhyoKnj = newValue
        break
      case 6:
        refValue.value!.rirekiOtherData.shienKeikakuKnj = newValue
        break
      case 12:
        refValue.value!.rirekiOtherData.ryuiKnj = newValue
        break
      default:
        break
    }
  }
}

/**
 *行追加
 */
function addRow() {
  if (!doFlg.value) {
    return
  }
  // 最終に新しい行を追加し、追加行を選択状態とする。
  const newRow: GamenDataInfo = {
    id: getNewNegativeId().toString(),
    bangou: { value: '' },
    svNaiyoKnj: { value: '' },
    svShuKnj: { value: '' },
    jigyoNameKnj: { value: '' },
    kikanKnj: { value: '' },
    modifiedCnt: '0',
    seq: filteredDataList.value.length + 1,
    updateKbn: UPDATE_KBN.CREATE,
  }

  refValue.value?.dataList.push(newRow)
  local.mo01354TbData.values.selectedRowId = newRow.id
}

/**
 *行挿入
 */
function insertRow() {
  const { selectedRowId } = local.mo01354TbData.values
  if (!doFlg.value || !selectedRowId || !refValue.value) {
    return
  }

  const insertPosition = refValue.value.dataList.findIndex(
    (i) => i.id === selectedRowId
  )
  if (insertPosition === -1) return

  const logicalIndex = filteredDataList.value.findIndex(
    item => item.id === selectedRowId
  )
  if (logicalIndex === -1) return

  // 選択行の上に新しい行を追加し、追加行を選択状態とする。
  const newRow: GamenDataInfo = {
    id: getNewNegativeId().toString(),
    bangou: { value: '' },
    svNaiyoKnj: { value: '' },
    svShuKnj: { value: '' },
    jigyoNameKnj: { value: '' },
    kikanKnj: { value: '' },
    modifiedCnt: '0',
    seq: logicalIndex + 1,
    updateKbn: UPDATE_KBN.CREATE,
  }

  // 指定行の前に1行を追加する
  refValue.value.dataList.splice(insertPosition, 0, newRow)
  local.mo01354TbData.values.selectedRowId = newRow.id
}

/**
 *行複写
 */
function copyRow() {
  const { selectedRowId } = local.mo01354TbData.values
  if (!doFlg.value || !selectedRowId || !refValue.value) {
    return
  }
  // 選択対象のインデックス
  const sourcePosition = refValue.value.dataList.findIndex(
    (i) => i.id === selectedRowId
  )
  if (sourcePosition === -1) return

  // copy対象
  const sourceLine = refValue.value.dataList[sourcePosition]
  if (!sourceLine) return

  // 選択対象のseq
  const logicalIndex = filteredDataList.value.findIndex(
    item => item.id === selectedRowId
  )
  if (logicalIndex === -1) return
  const sourceLineSeq = logicalIndex + 1

  // 選択行の情報をコピーして追加行を選択状態とする。
  const cpyLine: GamenDataInfo = {
    id: getNewNegativeId().toString(),
    bangou: { value: sourceLine.bangou.value },
    svNaiyoKnj: { value: sourceLine.svNaiyoKnj.value },
    svShuKnj: { value: sourceLine.svShuKnj.value },
    jigyoNameKnj: { value: sourceLine.jigyoNameKnj.value },
    kikanKnj: { value: sourceLine.kikanKnj.value },
    modifiedCnt: '0',
    seq: sourceLineSeq + 1,
    updateKbn: UPDATE_KBN.CREATE,
  }

  // 指定行の次に1行を追加する
  const insertPosition = sourcePosition + 1

  refValue.value.dataList.splice(insertPosition, 0, cpyLine)

  local.mo01354TbData.values.selectedRowId = cpyLine.id
  updateSeq()
}

/**
 *行削除
 */
function deleteRow() {
  if (!doFlg.value || !local.mo01354TbData.values.selectedRowId) {
    return
  }

  local.orX0002DeleteLineDialog.mo00024.isOpen = true
}

/**
 * 「表示順アイコンボタン」押下
 */
function openGUI00967() {
  const { selectedRowId } = local.mo01354TbData.values
  if (!doFlg.value || !selectedRowId || !refValue.value) {
    return
  }
  const items = filteredDataList.value

  localOneway.or27835Oneway = {
    processType: 'true',
    indexList: items.map((x) => {
      return {
        sort: x.seq,
        number: Number(x.bangou.value),
        serviceContents: x.svNaiyoKnj.value,
        serviceType: x.svShuKnj.value,
        offerOfficeNm: x.jigyoNameKnj.value,
        frequencyPeriod: x.kikanKnj.value,
        sortBackup: x.id,
      }
    }),
  }

  // GUI00967 表示順変更実施計画～③(支援内容)をポップアップで起動する
  Or27835Logic.state.set({
    uniqueCpId: or27835_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「目標アイコンボタン」押下
 *
 * @param titleKey - タイトル
 *
 * @param columnKey - 列のキー
 *
 * @param selectedRowId - index
 */
function openGUI00937(titleKey?: string, columnKey?: string, selectedRowId?: string) {
  if (!doFlg.value || !titleKey || !columnKey) return
  //GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  let _b2CD = OrX0107Const.B2CD_1
  let _tableName = OrX0107Const.TABLE_NAME_231
  let _textValue = ''

  // サービス内容、頻度及び期間
  if (columnKey === OrX0107Const.STR_SV_NAIYO_KNJ || columnKey === OrX0107Const.STR_KIKAN_KNJ) {
    _tableName = OrX0107Const.TABLE_NAME_232
    _b2CD = columnKey === OrX0107Const.STR_SV_NAIYO_KNJ ? OrX0107Const.B2CD_7 : OrX0107Const.B2CD_10

    const targetSelectedRowId = selectedRowId ?? local.mo01354TbData.values.selectedRowId
    const data = filteredDataList.value.find(i => i.id === targetSelectedRowId)
    if (!data) return
    // 選択行を設定
    if (selectedRowId && local.mo01354TbData.values.selectedRowId !== selectedRowId) {
      local.mo01354TbData.values.selectedRowId = selectedRowId
    }
    _textValue = columnKey === OrX0107Const.STR_SV_NAIYO_KNJ ? data.svNaiyoKnj.value ?? '' : data.kikanKnj.value ?? ''

  } else {
    switch (columnKey) {
      // 運動・移動
      case OrX0107Const.STR_KADAI_UNDO_KNJ:
        _b2CD = OrX0107Const.B2CD_1
        _textValue = refValue.value?.rirekiOtherData.kadaiUndoKnj ?? ''
        break
      // 日常生活（家庭生活）
      case OrX0107Const.STR_KADAI_NICHIJO_KNJ:
        _b2CD = OrX0107Const.B2CD_2
        _textValue = refValue.value?.rirekiOtherData.kadaiNichijoKnj ?? ''
        break
      // 社会参加対人関係
      case OrX0107Const.STR_KADAI_SHAKAI_KNJ:
        _b2CD = OrX0107Const.B2CD_3
        _textValue = refValue.value?.rirekiOtherData.kadaiShakaiKnj ?? ''
        break
      // 健康管理
      case OrX0107Const.STR_KADAI_KENKO_KNJ:
        _b2CD = OrX0107Const.B2CD_4
        _textValue = refValue.value?.rirekiOtherData.kadaiKenkoKnj ?? ''
        break
      // 目標
      case OrX0107Const.STR_MOKUHYO_KNJ:
        _b2CD = OrX0107Const.B2CD_5
        _textValue = refValue.value?.rirekiOtherData.mokuhyoKnj ?? ''
        break
      // 支援計画
      case OrX0107Const.STR_SHIEN_KEIKAKU_KNJ:
        _b2CD = OrX0107Const.B2CD_6
        _textValue = refValue.value?.rirekiOtherData.shienKeikakuKnj ?? ''
        break
      // サービス実施上の留意点
      case OrX0107Const.STR_RYUI_KNJ:
        _b2CD = OrX0107Const.B2CD_15
        _textValue = refValue.value?.rirekiOtherData.ryuiKnj ?? ''
        break
      default:
        break
    }
  }


  //GUI00937 入力支援［ケアマネ］画面をポップアップで起動する
  localOneway.or51775Oneway = {
    title: titleKey,
    screenId: '',
    bunruiId: '',
    t1Cd: OrX0107Const.B1CD_2240,
    t2Cd: _b2CD,
    t3Cd: OrX0107Const.B3CD_0,
    tableName: _tableName,
    columnName: columnKey,
    assessmentMethod: '',
    inputContents: _textValue,
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    mode: '',
  }
  local.or51775.modelValue = _textValue
  gyoumuCom.setGUI00937Param(localOneway.or51775Oneway, localOneway.orX0107Oneway.initMasterObj)
  //GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  Or51775Logic.state.set({
    uniqueCpId: or51775_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00937 入力支援［ケアマネ］画面の返回値がある場合
 *
 * @param or51775ConfirmValue - ポップアップ画面での返回値
 */
function handleOr51775Confirm(or51775ConfirmValue: Or51775ConfirmType) {
  if (or51775ConfirmValue) {
    const newValue = or51775ConfirmValue.value ?? ''
    // 返却情報.上書きフラグ(0:本文末に追加、1:本文上書)
    const title = localOneway.or51775Oneway.title
    const mapping: Record<string, keyof GamenRirekiOtherInfo> = {
      // 運動・移動
      [t('label.orX0107-move')]: 'kadaiUndoKnj',
      // 日常生活（家庭生活）
      [t('label.orX0107-daily-family-life-p')]: 'kadaiNichijoKnj',
      // 社会参加対人関係
      [t('label.orX0107-society-p')]: 'kadaiShakaiKnj',
      // 健康管理
      [t('label.orX0107-health')]: 'kadaiKenkoKnj',
      // 目標
      [t('label.goal')]: 'mokuhyoKnj',
      // 支援計画
      [t('label.support_plan')]: 'shienKeikakuKnj',
      // サービス実施上の留意点
      [t('label.service-implementation-considerations')]: 'ryuiKnj',
    }
    const key = mapping[title]
    if (key && refValue.value?.rirekiOtherData) {
      refValue.value.rirekiOtherData[key] =
        or51775ConfirmValue.type === OrX0107Const.OPERATION_KBN_1
          ? newValue
          : refValue.value.rirekiOtherData[key].concat(newValue)
      return
    }

    // サービス内容、頻度及び期間
    const selectedId = local.mo01354TbData.values.selectedRowId
    if (!selectedId) return

    const data = filteredDataList.value.find(i => i.id === selectedId)
    if (!data) return

    const isOverwrite = or51775ConfirmValue.type === OrX0107Const.OPERATION_KBN_1
    const concatValue = (current: string, newVal: string) => isOverwrite ? newVal : current + newVal

    if (data) {
        // 更新区分を更新
        handleChange(data)
        // サービス内容
        if (title === t('label.service-contents')) {
          data.svNaiyoKnj.value = concatValue(data.svNaiyoKnj.value, newValue)
          return
        }
        // 頻度及び期間
        if (title === t('label.frequency-period')) {
          data.kikanKnj.value = concatValue(data.kikanKnj.value, newValue)
        }
      }
  }
}

/**
 * GUI00949 課題・目標取込画面をポップアップで起動する
 *
 * @param key - キー
 */
function openGUI00949(key?: string) {
  if (!doFlg.value || !key) return

  // 取込区分「2:実施計画～③画面の課題取込ボタン押下」
  local.orX0107.openGUI00949Kbn = key

  localOneway.or28455Oneway.issuesOrGoalImportType = {
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    syubetsuId: localOneway.orX0107Oneway.syubetuId ?? '',
    importKbn:
      key === OrX0107Const.STR_MOKUHYO_KNJ ? OrX0107Const.IMPORT_KBN_3 : OrX0107Const.IMPORT_KBN_2,
    kikanFlag: localOneway.orX0107Oneway.kikanFlg ?? '',
  }

  // or28455 GUI00949 課題・目標取込画面をポップアップで起動する。
  Or28455Logic.state.set({
    uniqueCpId: or28455_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「サービス種別アイコンボタン」押下
 * 「提供事業所名アイコンボタン」押下
 * ※支援内容一覧 GUI00957 サービス種別入力支援画面をポップアップで起動する
 *
 * @param serviceKbnId - 検索方式: 1:※1:種別と事業者、2:事業者のみ、8:計画実施
 *
 * @param selectedRowId - index
 */
function openGUI00957(serviceKbnId: string, selectedRowId?:string) {
  //画面.支援内容一覧で行が選択されていない場合
  const targetSelectedRowId = selectedRowId ?? local.mo01354TbData.values.selectedRowId
  if (!targetSelectedRowId) return
  const data = filteredDataList.value.find(i => i.id === targetSelectedRowId)
  if (!data) return

  // 選択行を設定
  if (selectedRowId && local.mo01354TbData.values.selectedRowId !== selectedRowId) {
    local.mo01354TbData.values.selectedRowId = selectedRowId
  }

  // 画面.支援内容一覧での選択行の「サービス種別」
  local.or50429.serviceType = { value: data?.svShuKnj?.value ?? '' }
  // 画面.支援内容一覧での選択行の「提供事業所名」
  local.or50429.offerOffice = { value: data?.jigyoNameKnj?.value ?? '' }

  // 画面表示モード:検索方式
  localOneway.or50429Oneway.screenDisplayMode = serviceKbnId
  // 画面.支援内容一覧での選択行の「サービス種別」
  localOneway.or50429Oneway.serviceKind = data?.svShuKnj?.value ?? ''
  // 画面.支援内容一覧での選択行の「提供事業所名」
  localOneway.or50429Oneway.officeName = data?.jigyoNameKnj?.value ?? ''
  // 画面.支援内容一覧での選択行の「サービス内容」
  localOneway.or50429Oneway.serviceContent = data?.svNaiyoKnj?.value ?? ''

  //GUI00957 サービス種別入力支援画面をポップアップで起動する。
  Or50429Logic.state.set({
    uniqueCpId: or50429_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00957 サービス種別入力支援画面での返回値を監視
 *
 * @param newValue - ポップアップ画面での返回値
 */
function handleOr50429Confirm(newValue: Or50429Type) {
  if (!newValue) return

  const selectedRowId = local.mo01354TbData.values.selectedRowId
  if (!selectedRowId) return
  const data = refValue.value?.dataList.find(i => i.id === selectedRowId)
  if (!data) return

  //返回値.サービス種別が空白ではない場合
  if (newValue.serviceType?.value) {
    //画面.支援内容一覧での選択行の「サービス種別」列に返回値.サービス種別を設定する。
    data.svShuKnj.value = newValue.serviceType.value
  }
  //返回値.提供事業所名が空白ではない場合
  if (newValue.offerOffice?.value) {
    //画面.支援内容一覧での選択行の「提供事業所名」列に返回値.提供事業所名を設定する。
    data.jigyoNameKnj.value = newValue.offerOffice.value
  }
  // 更新区分を更新
  handleChange(data)
}

/**
 * 更新区分を設定
 *
 * @param item - 更新行
 */
function handleChange(item: GamenDataInfo) {
  if (item.updateKbn === UPDATE_KBN.NONE) {
    item.updateKbn = UPDATE_KBN.UPDATE
  }
}

/**
 * 実施計画複写返却情報
 *
 * @param copyDataList - 複写用表示用「課題目標期間内容担当」リスト
 *
 * @param rirekiOtherData - 複写用履歴の関連情報
 */
function updateCopyValue(copyDataList: GamenDataInfo[], rirekiOtherData: GamenRirekiOtherInfo) {
  if (!refValue.value) { return }

  // 選択されている項目名
  refValue.value.selectItemName = ''
  // 選択されている項目値
  refValue.value.selectItemValue = ''

  // 既存データ表示用「支援内容」リストを削除する
  refValue.value.dataList?.forEach((x) => {
    if (x.updateKbn !== UPDATE_KBN.DELETE) {
      x.updateKbn = UPDATE_KBN.DELETE
    }
  })
  local.mo01354TbData.values.selectedRowId = ''
  local.mo01354TbData.values.selectedRowIds = []

  // 複写用表示用「支援内容」リスト
  if (copyDataList?.length > 0) {
    const copyDataArr = copyDataList.map((item) => ({
      ...item,
      id: getNewNegativeId().toString(),
    }))
    refValue.value.dataList.push(...copyDataArr)
  }
  // 表示用「総合的課題・目標・支援計画」情報
  refValue.value.rirekiOtherData = rirekiOtherData
}


/**
 * 削除処理:リストを削除
 *
 */
function deleteAllRow() {
  if (refValue.value) {
    // 選択されている項目名
    refValue.value.selectItemName = ''
    // 選択されている項目値
    refValue.value.selectItemValue = ''

    // 表示用「支援内容」リスト
    refValue.value.dataList?.forEach((x) => {
      if (x.updateKbn !== UPDATE_KBN.DELETE) {
        x.updateKbn = UPDATE_KBN.DELETE
      }
    })
    local.mo01354TbData.values.selectedRowId = ''
    local.mo01354TbData.values.selectedRowIds = []
  }
}
/**
 * バリデーション関数
 */
async function isValid() {
  return (await form.value!.validate()).valid
}

defineExpose({
  doCaseImport,
  updateCopyValue,
  deleteAllRow,
  isValid,
})
</script>
<template>
  <c-v-form
    ref="form"
    class="h-100"
  >
    <div class="d-flex flex-column h-100">
      <div class="flex-grow-1 overflow-y-auto">
        <!-- 総合的課題・目標・支援計画 -->
        <div class="section-wrapper" :class="{ 'pl-6': !isCopyMode, 'pl-2': isCopyMode }">
          <c-v-row
            v-show="customTableShowFlg"
            no-gutters
            class="section-row-wrapper"
          >
            <c-v-col
              cols="auto"
              class="pr-0 custom-tb-width"
            >
              <div class="custom-table-wrapper first-table">
                <table class="custom-table">
                  <colgroup>
                    <col style="width: 126px">
                    <col style="width: 583px">
                    <col style="width: 300px">
                  </colgroup>
                  <thead>
                    <tr style="height:32px">
                      <!--総合的課題-->
                      <th colspan="2">
                        <div >{{ t('label.comprehensive-issues') }}</div>
                      </th>
                      <!--目標-->
                      <th >
                        <div >{{ t('label.goal') }}</div>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <!--body row1-->
                    <tr class="row-height">
                      <!--運動・移動-->
                      <td class="first-col">
                        <div>{{ t('label.orX0107-move') }}</div>
                      </td>
                      <!--運動・移動テキストエリア-->
                      <td>
                        <g-custom-or-x-0185
                          v-model="kadaiUndoKnj"
                          :oneway-model-value="localOneway.orX0185Oneway"
                          style="letter-spacing:-0.56px"
                          @focus="handleFocus(t('label.orX0107-move'), 1)"
                          >
                          <template #menu>
                            <div
                              class="orx0185-footer-menu-item"
                              @click="openGUI00937(t('label.orX0107-move'),OrX0107Const.STR_KADAI_UNDO_KNJ)"
                            >
                              {{ t('label.orX0107-input') }}
                            </div>
                            <div
                              v-show="!isCopyMode && showGoalImport"
                              class="orx0185-footer-menu-item"
                              @click="openGUI00949(OrX0107Const.STR_KADAI_UNDO_KNJ)"
                            >
                              {{ t('label.issues-import') }}
                            </div>
                          </template>
                        </g-custom-or-x-0185>
                      </td>
                      <!--目標テキストエリア-->
                      <td rowspan="4">
                        <g-custom-or-x-0185
                          v-model="mokuhyoKnj"
                          :oneway-model-value="localOneway.orX0185OnewayMokuhyo"
                          style="letter-spacing:-0.56px"
                          @focus="handleFocus(t('label.goal'), 5)"
                          >
                          <template #menu>
                            <div
                              v-show="!isCopyMode"
                              class="orx0185-footer-menu-item"
                              @click="openGUI00937(t('label.goal'), OrX0107Const.STR_MOKUHYO_KNJ)"
                            >
                              {{ t('label.orX0107-input') }}
                            </div>
                            <div
                              v-show="!isCopyMode && showGoalImport"
                              class="orx0185-footer-menu-item"
                              @click="openGUI00949(OrX0107Const.STR_MOKUHYO_KNJ)"
                            >
                              {{ t('label.orX0107-goal-Import') }}
                            </div>
                          </template>
                        </g-custom-or-x-0185>
                      </td>
                    </tr>
                    <!--body row2-->
                    <tr class="row-height">
                      <!--日常生活（家庭生活）ラベル-->
                      <td class="first-col">
                      {{ t('label.orX0107-daily-family-life') }}
                      </td>
                      <!--日常生活（家庭生活）テキストエリア-->
                      <td>
                        <g-custom-or-x-0185
                          v-model="kadaiNichijoKnj"
                          :oneway-model-value="localOneway.orX0185Oneway"
                          style="letter-spacing:-0.56px"
                          @focus="handleFocus(t('label.orX0107-daily-family-life-p'), 2)"
                          >
                          <template #menu>
                            <div
                              v-show="!isCopyMode"
                              class="orx0185-footer-menu-item"
                              @click="openGUI00937(t('label.orX0107-daily-family-life-p'),OrX0107Const.STR_KADAI_NICHIJO_KNJ)"
                            >
                              {{ t('label.orX0107-input') }}
                            </div>
                            <div
                              v-show="!isCopyMode && showGoalImport"
                              class="orx0185-footer-menu-item"
                              @click="openGUI00949(OrX0107Const.STR_KADAI_NICHIJO_KNJ)"
                            >
                              {{ t('label.issues-import') }}
                            </div>
                          </template>
                        </g-custom-or-x-0185>
                      </td>
                    </tr>
                    <!--body row3-->
                    <tr class="row-height">
                      <!--社会参加対人関係ラベル-->
                      <td class="first-col">
                        <div>{{ t('label.orX0107-society') }}</div>
                      </td>
                      <!--社会参加対人関係テキストエリア-->
                      <td>
                        <g-custom-or-x-0185
                          v-model="kadaiShakaiKnj"
                        :oneway-model-value="localOneway.orX0185Oneway"
                        style="letter-spacing:-0.56px"
                          @focus="handleFocus(t('label.orX0107-society-p'), 3)"
                        >
                          <template #menu>
                            <div
                              v-show="!isCopyMode"
                              class="orx0185-footer-menu-item"
                              @click="openGUI00937(t('label.orX0107-society-p'),OrX0107Const.STR_KADAI_SHAKAI_KNJ)"
                            >
                              {{ t('label.orX0107-input') }}
                            </div>
                            <div
                              v-show="!isCopyMode && showGoalImport"
                              class="orx0185-footer-menu-item"
                              @click="openGUI00949(OrX0107Const.STR_KADAI_SHAKAI_KNJ)"
                            >
                              {{ t('label.issues-import') }}
                            </div>
                          </template>
                        </g-custom-or-x-0185>
                      </td>
                    </tr>
                    <!--body row4-->
                    <tr class="row-height">
                      <!--健康管理ラベル-->
                      <td class="first-col">
                        <div>{{ t('label.orX0107-health') }}</div>
                      </td>
                      <!--健康管理テキストエリア-->
                      <td>
                        <g-custom-or-x-0185
                          v-model="kadaiKenkoKnj"
                          :oneway-model-value="localOneway.orX0185Oneway"
                          style="letter-spacing:-0.56px"
                          @focus="handleFocus(t('label.orX0107-health'), 4)"
                          >
                            <template #menu>
                              <div
                                v-show="!isCopyMode"
                                class="orx0185-footer-menu-item"
                                @click="openGUI00937(t('label.orX0107-health'),OrX0107Const.STR_KADAI_KENKO_KNJ)"
                              >
                                {{ t('label.orX0107-input') }}
                              </div>
                              <div
                                v-show="!isCopyMode && showGoalImport"
                                class="orx0185-footer-menu-item"
                                @click="openGUI00949(OrX0107Const.STR_KADAI_KENKO_KNJ)"
                              >
                                {{ t('label.issues-import') }}
                              </div>
                            </template>
                        </g-custom-or-x-0185>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </c-v-col>
            <c-v-col cols="auto" class="pl-0 support-plan-wrapper">
              <div>
                <!-- 支援計画タイトル -->
                <div class="support-plan-item support-plan-header">
                  <div class="label-text">
                    {{ t('label.support_plan') }}
                  </div>
                </div>
                <!-- 支援計画ラベル -->
                <div class="support-plan-item support-plan-label">
                  <span style="letter-spacing:-0.56px">{{ t('label.orX0107-point') }}</span>
                </div>
                <!-- 支援計画テキストエリア -->
                <div class="support-plan-item">
                  <g-custom-or-x-0163
                    v-model="shienKeikakuKnj"
                    :oneway-model-value="localOneway.orX0163OnewayShienKeikaku"
                    style="letter-spacing:-0.56px"
                    @focus="handleFocus(t('label.support_plan'), 6)"
                    @on-click-edit-btn="openGUI00937(t('label.support_plan'), OrX0107Const.STR_SHIEN_KEIKAKU_KNJ)"
                  >
                  </g-custom-or-x-0163>
                </div>
              </div>
            </c-v-col>
          </c-v-row>
        </div>
        <div class="pb-6" style="padding-top:50px">
          <c-v-divider class="divider-class" />
          </div>
        <!-- 行追加、行挿入、行複写、行削除 -->
        <div class="btn-container pb-6" :class="{ 'pl-6': !isCopyMode , 'pl-2': isCopyMode}">
          <c-v-row
            no-gutters
            class="btn-area-class"
          >
            <c-v-col cols="auto">
              <c-v-row
                no-gutters
                class="btn-group-class"
              >
                <!-- 行追加ボタン -->
                <c-v-col cols="auto">
                  <base-mo00611
                    v-if="!isCopyMode"
                    :oneway-model-value="localOneway.mo00611OnewayAdd"
                    @click="addRow"
                  />
                </c-v-col>
                <!-- 行挿入ボタン -->
                <c-v-col cols="auto">
                  <base-mo00611
                    v-if="!isCopyMode"
                    :oneway-model-value="localOneway.mo00611OnewayInsert"
                    :disabled="disableButtons"
                    @click="insertRow"
                  />
                </c-v-col>
                <!-- 行複写ボタン -->
                <c-v-col cols="auto">
                  <base-mo00611
                    v-if="!isCopyMode"
                    :oneway-model-value="localOneway.mo00611OnewayCopy"
                    :disabled="disableButtons"
                    @click="copyRow"
                  />
                </c-v-col>
                <!-- 行削除ボタン -->
                <c-v-col cols="auto">
                  <base-mo00611
                    v-if="!isCopyMode"
                    :oneway-model-value="localOneway.mo00611OnewayDelete"
                    :disabled="disableButtons"
                    @click="deleteRow"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col cols="auto">
              <c-v-row no-gutters>
                <!--表示順ラベル-->
                <base-mo00611
                  v-if="!isCopyMode"
                  :oneway-model-value="localOneway.mo00611OnewayOrder"
                  @click="openGUI00967"
                />
                <!-- データ-ページング -->
                <base-mo01338
                  :oneway-model-value="{
                    ...localOneway.mo01338Oneway,
                    value: String(local.mo01354TbData.values.items.length),
                  }"/>
              </c-v-row>
            </c-v-col>
          </c-v-row>
        </div>
        <!-- 支援内容一覧 table -->
        <c-v-row
          no-gutters
          class="middleContent"
          :class="{ 'pl-6': !isCopyMode , 'pl-2': isCopyMode}"
        >
        <div class="dataList-table-label">
          <base-mo00615
            :oneway-model-value="localOneway.mo00615Oneway"
          />
        </div>
        <div class="dataList-table-container table-header table-wrapper">
          <base-mo01354
            v-model="local.mo01354TbData"
            class="list-wrapper"
            :oneway-model-value="localOneway.mo01354Oneway"
            :hide-default-footer="true"
          >
            <!--No-->
            <template #[`item.key-bangou`]="{ item}">
              <base-mo01278
                v-model="item.bangou"
                class="custom-mo01278"
                :readonly="isCopyMode"
                :oneway-model-value="localOneway.mo01278Oneway"
                @focus="handleFocus(t('label.service-contents'), 7)"
                @change="handleChange(item)"
              />
            </template>
            <!--サービス内容-->
            <template #[`item.key-svNaiyoKnj`]="{ item}">
              <g-custom-or-x-0163
                v-model="item.svNaiyoKnj"
                :oneway-model-value="localOneway.orX0163Oneway"
                style="letter-spacing:-0.56px"
                @focus="handleFocus(t('label.service-contents'), 8)"
                @on-click-edit-btn="openGUI00937(t('label.service-contents'), OrX0107Const.STR_SV_NAIYO_KNJ, item.id)"
                @change="handleChange(item)"
              />
            </template>
            <!--サービス種別-->
            <template #[`item.key-svShuKnj`]="{ item }">
              <g-custom-or-x-0163
                v-model="item.svShuKnj"
                :oneway-model-value="localOneway.orX0163Oneway"
                style="letter-spacing:-0.56px"
                @focus="handleFocus(t('label.service-type'), 9)"
                @on-click-edit-btn="openGUI00957(OrX0107Const.SERVICE_KBN_1, item.id)"
                @change="handleChange(item)"
              />
            </template>
            <!--提供事業所名-->
            <template #[`item.key-jigyoNameKnj`]="{ item}">
              <g-custom-or-x-0163
                v-model="item.jigyoNameKnj"
                :oneway-model-value="localOneway.orX0163Oneway"
                style="letter-spacing:-0.56px"
                @focus="handleFocus(t('label.jimusyo-name'), 10)"
                @on-click-edit-btn="openGUI00957(OrX0107Const.SERVICE_KBN_2, item.id)"
                @change="handleChange(item)"
              />
            </template>
            <!--頻度及び期間-->
            <template #[`item.key-kikanKnj`]="{ item }">
              <g-custom-or-x-0218
                  v-model="item.kikanKnj"
                  :oneway-model-value="localOneway.orX0218Oneway"
                  style="letter-spacing:-0.56px"
                  @on-click-edit-btn="openGUI00937(t('label.frequency-period'), OrX0107Const.STR_KIKAN_KNJ, item.id)"
                  @focus="handleFocus(t('label.frequency-period'), 11)"
                  @change="handleChange(item)"
                />
            </template>
          </base-mo01354>
        </div>
        </c-v-row>
        <div class="py-6">
          <c-v-divider class="divider-class"/>
        </div>
        <!-- 処遇実施上の留意点 -->
         <div :class="{ 'pl-6': !isCopyMode , 'pl-2': isCopyMode}">
          <div class="width-container-ryui ryuiKnj-input">
            <g-custom-or-x-0156
              v-show="doFlg"
              v-model="ryuiKnjModel"
              class="w-100"
              :oneway-model-value="localOneway.orX0156Oneway"
              @focus="handleFocus(t('label.service-implementation-considerations'), 12)"
              @on-click-edit-btn="openGUI00937(t('label.service-implementation-considerations'),OrX0107Const.STR_RYUI_KNJ)"
            />
          </div>
          <div v-show="doFlg" class="width-container-radio pt-4">
            <!--障害者等支援加算ラジオボタン-->
            <base-mo00039
              v-model="shogaiKasanFlg"
              :oneway-model-value="localOneway.mo00039OnewaySupport"
            />
          </div>
         </div>
      </div>
    </div>
  </c-v-form>
  <!-- 「行削除ボタン」押下のメッセージ -->
  <g-custom-or-x-0002
    v-model="local.orX0002DeleteLineDialog"
    :oneway-model-value="localOneway.orX0002DeleteLineOneway"
  ></g-custom-or-x-0002>

  <!--GUI00937 入力支援［ケアマネ］画面-->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775_1"
    v-model="local.or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleOr51775Confirm"
  />

  <!--GUI00949 課題・目標取込画面画面-->
  <g-custom-or-28455
    v-if="showDialogOr28455"
    v-bind="or28455_1"
    v-model="local.or28455"
    :oneway-model-value="localOneway.or28455Oneway"
    :unique-cp-id="or28455_1.uniqueCpId"
  />
  <!--GUI00967 表示順変更実施計画～③(支援内容)-->
  <g-custom-or-27835
    v-if="showDialogOr27835"
    v-bind="or27835_1"
    v-model="local.or27835"
    :oneway-model-value="localOneway.or27835Oneway"
  />
  <!--GUI00957 サービス種別入力支援画面-->
  <g-custom-or-50429
    v-if="showDialogOr50429"
    v-bind="or50429_1"
    v-model="local.or50429"
    :oneway-model-value="localOneway.or50429Oneway"
    @update-model-value="handleOr50429Confirm"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
.section-wrapper {
  height: 395px;
}
.custom-tb-width {
  width: 1009px;
}
.support-plan-wrapper {
  width: 300px;
}

.section-row-wrapper {
  width: 100%;
  margin: 0 auto;
}
.support-plan-item {
  width: 100%;
  box-sizing: border-box;
  border: 1px solid rgb(var(--v-theme-black-200));
  border-top: none;
  background-color: #fff;
}
.support-plan-header {
  height: 33px;
  border-top: 1px solid rgb(var(--v-theme-black-200));
  background-color: rgb(var(--v-theme-blue-100));
}
.support-plan-header .label-text {
  padding-left: 8px;
  font-size: 13px;
  line-height: 32px;
}
.support-plan-label {
  height: 44px;
  padding: 0 12px;
  display: flex;
  align-items: left;
  font-size: 14px;
  background-color: #FAFAFA;
}

.first-table {
  .custom-table-wrapper {
    background-color: transparent;
    overflow: auto;
    display: block;
  }

  .custom-table {
    width: 100%;
    margin: 0 auto;
    border-collapse: collapse;
    table-layout: fixed;
    background-color: rgb(var(--v-theme-surface));

    thead {
      background-color: rgb(var(--v-theme-blue-100)) !important;
    }

    th,
    td {
      border: 1px rgb(var(--v-theme-black-200)) solid !important;
      text-align: left;
      white-space: break-spaces;
      box-sizing: border-box;
    }
    th {
      padding: 0 8px;
      font-size: 13px;
      font-weight: normal;
    }
  }

  .row-height {
    height: 83px !important;
  }
  .first-col {
    background-color: rgb(var(--v-theme-blue-100)) !important;
    text-align: left;
    padding: 0px 12px;
    vertical-align: middle;
    height: 83px;
    font-weight: normal;
  }
}

.btn-area-class {
  justify-content: space-between;
  align-items: baseline;

  .btn-group-class {
    display: flex;
    gap: 8px;
  }

  :deep(.v-btn) {
    --v-btn-height: 32px !important;
    min-height: 32px !important;
    height: 32px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  :deep(.v-btn--size-default) {
    min-height: 32px !important;
    height: 32px !important;
  }
}

.divider-class {
  border-width: thin;
}

.middleContent {
  min-height: 0;
}

.btn-container {
  width: 1178px;
}

.table-header :deep(.v-table__wrapper tr th) {
  padding-left: 8px;
  padding-right: 0px;
  font-weight: normal !important;
  height:32px !important;
  font-size: 13px !important;
}
.table-header :deep(.v-table__wrapper tr td) {
  font-weight: normal !important;
  height:50px !important;
}
.dataList-table-container {
  width: 1178px;
  overflow-x: auto;
  overflow-y: hidden;
  ::v-deep(.v-table) {
    min-width: unset !important;
    table-layout: fixed;
    width: auto !important;
  }
  ::v-deep(.v-data-table__wrapper) {
    overflow: visible !important;
  }
  ::v-deep(.resizable-grid) {
    min-width: unset !important;
    width: auto !important;
  }
}
.dataList-table-label {
  background-color: rgb(var(--v-theme-blue-100)) !important;
  width: 1178px;
  padding-left: 8px;
  background-color: #f9f9f9;
  border: 1px solid #ccc;
  border-bottom: none;
  font-weight: 500;
  font-size: 13px;
  ::v-deep(*) {
    margin: 0 !important;
    padding: 0 !important;
  }
}
.dataList-table-label + .dataList-table-container {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

::v-deep(.custom-mo01278 .full-width-field) {
  padding: 8px !important;
  margin: 0 !important;
  font-size: 13px !important;
  letter-spacing: -0.52px !important;
}

.width-container-ryui {
  width: 1080px;
}

.ryuiKnj-input {
  :deep(.v-sheet) {
    .service-label {
      .item-label {
        font-size: 16px;
        font-weight: bold !important;
      }
    }
  }
}

.width-container-radio {
  width: 249px;
  ::v-deep(.v-input__control) {
    background-color: transparent !important;
  }
}

:deep(.v-sheet) {
  background-color: transparent !important;
}
:deep(.list-wrapper .v-table__wrapper th) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}

</style>
