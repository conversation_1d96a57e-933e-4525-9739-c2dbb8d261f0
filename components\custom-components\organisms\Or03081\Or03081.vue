<script setup lang="ts">
/**
 * Or03081:有機体:食事、排泄等及外出の拡張フォーム
 * GUI00800_［アセスメント］画面（居宅）（6②）
 *
 * @description
 * 食事、排泄等及外出の拡張フォーム
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, toRefs, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or26866Const } from '../Or26866/Or26866.constants'
import { Or26866Logic } from '../Or26866/Or26866.logic'
import type { Mo00045Type } from '../Or00386/Or00386.type'
import type { SelectOptionsType } from './Or03081.type'
import { Or03081Const } from './Or03081.constants'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type {
  Or03081OnewayType,
  Or03081TwoWayType,
} from '~/types/cmn/business/components/Or03081Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { useCmnRouteCom, useScreenStore, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import type { Or26866OnewayType, Or26866Type } from '~/types/cmn/business/components/Or26866Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'

const { t } = useI18n()

/************************************************
 * Props
 ************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  modelValue: Or03081TwoWayType
  onewayModelValue?: Or03081OnewayType
}

const props = defineProps<Props>()

const { getInitialSettingMaster } = useCmnRouteCom()

const { uniqueCpId } = props

const { onewayModelValue } = toRefs(props)

const screenKey = onewayModelValue.value?.key
/**************************************************
 * 算出プロパティ
 **************************************************/
// 家族実施selectOptions
const familyImplementationOptionsComputed = computed(
  () => onewayModelValue.value?.familyImplementationOptions ?? []
)

// サービス実施selectOptions
const serverImplementationOptionsComputed = computed(
  () => onewayModelValue.value?.serverImplementationOptions ?? []
)
// 希望selectOptions
const hopeOptionsComputed = computed(() => onewayModelValue.value?.hopeOptions ?? [])

// 要援助→計画selectOptions
const needAssistanceToPlanOptionsComputed = computed(
  () => onewayModelValue.value?.needAssistanceToPlanOptions ?? []
)

// ダイアログ表示フラグ
const show51755dialog = computed(() => {
  // Or10486のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const show26866dialog = computed(() => {
  // Or10486のダイアログ開閉状態
  return Or26866Logic.state.get(or26866.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/

const defaultOnewayModelValue: Or03081OnewayType = {
  headingTitle: '',
  tableHeaders: [],
  expandCheckboxFormInfo: [],
  commonInfo: {},
}

const or51775 = ref({ uniqueCpId: '' })

const or26866 = ref({ uniqueCpId: '' })

const or26866Type = ref<Or26866Type>({
  rtnContents: '',
})
// 見出しです
const headingTitle: Mo01338OnewayType = {
  /** 値のフォントの太さ */
  valueFontWeight: 'bold',
  /** 値 */
  value: '',
  /** カスタムクラス */
  customClass: new CustomClass({
    outerClass: 'background-color',
    labelClass: 'd-none',
    itemClass: 'v-toolbar-title',
    outerStyle: 'background:transparent',
    itemStyle: 'fontSize:18px',
  }),
}

// Mo012820共通
const commonMo01282OnewayType: Mo01282OnewayType = {
  itemTitle: 'displayName',
  itemValue: 'value',
  width: '118px',
}

// プルダウンの選択肢セットです
const selectOptions: SelectOptionsType = reactive({
  familyImplementationOneway: {
    ...commonMo01282OnewayType,
    items: familyImplementationOptionsComputed,
  },
  serverImplementationOneway: {
    ...commonMo01282OnewayType,
    items: serverImplementationOptionsComputed,
  },
  hopeOneway: {
    ...commonMo01282OnewayType,
    items: hopeOptionsComputed,
  },
  needAssistanceToPlanOneway: {
    ...commonMo01282OnewayType,
    items: needAssistanceToPlanOptionsComputed,
  },
})

const localOneway = {
  or03081: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 自動選択ボタン
  autoSelectBtnOneway: {
    btnLabel: '',
    width: '20px',
    minWidth: '20px',
  } as Mo00611OnewayType,
  orX0156OnewayModelValue: {
    showItemLabel: false,
    showDividerLineFlg: true,
    showEditBtnFlg: true,
    name: 'txtSpecialNoteShouldSolutionIssuesInput',
    isVerticalLabel: true,
    disabled: false,
    noResize: true,
    maxlength: '4000',
  } as OrX0156OnewayType,
  // 特記、解決すべき課題など 選択ボタン
  specialNoteShouldSolutionIssuesBtn: {
    btnIcon: 'edit_square',
    name: 'specialNoteShouldSolutionIssuesBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  // 文章マスタボタン
  sentenceMasterBtn: {
    btnIcon: 'edit_square',
    name: 'sentenceMaster',
    density: 'compact',
  } as Mo00009OnewayType,
  // 有機体:認定調査 特記事項選択単方向バインドのデータ構造
  or26866OnewayModel: {
    svJigyoId: '',
    userId: '',
    ninteiFlg: '',
    dayVer: '',
    sc1Id: '',
    defNo: '',
    defNoArray: ['1', '2'],
    memo: '',
  } as Or26866OnewayType,
  btnTooltip: {
    serveyLedgerImportBtn: t('tooltip.assessment-home-6-1-servey-ledger-import'),
    basicMotionDialogBtn: t('tooltip.assessment-home-6-1-basic-motion'),
    specialNoteShouldSolutionIssuesBtn: t('tooltip.special-note-should-solution-issues'),
    sentenceMasterBtn: t('tooltip.assessment-home-6-1-sentence-master'),
  },
  mo00615OnewayType: {
    showItemLabel: true,
    showRequiredLabel: false,
    customClass: new CustomClass({
      outerClass: 'background-color',
    }),
  } as Mo00615OnewayType,
  mo00018OthersCheckboxOneway: {
    showItemLabel: false,
    checkboxLabel: t('label.other'),
    checked: false,
  } as Mo00018OnewayType,
  mo00045OtherInputOnewayType: {
    showItemLabel: false,
    maxLength: '10',
    width: '137px',
    customClass: new CustomClass({
      outerClass: 'mr-0',
    }),
  },
  // 入力支援［ケアマネ］
  or51775Oneway: {} as Or51775OnewayType,
}

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or26866Const.CP_ID(1)]: or26866.value,
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or03081TwoWayType>({
  cpId: Or03081Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**
 *  特記、解決すべき課題選択ボタンクリック
 *
 */
const specialNoteShouldSolutionIssuesBtnClick = () => {
  localOneway.or26866OnewayModel = {
    ...localOneway.or26866OnewayModel,
    svJigyoId: onewayModelValue.value?.commonInfo.jigyoId ?? '',
    userId: onewayModelValue.value?.commonInfo.userId ?? '',
    ninteiFlg: onewayModelValue.value?.commonInfo.ninteiFormF ?? '',
    dayVer: onewayModelValue.value?.commonInfo.kikanKanriFlg ?? '',
    sc1Id: onewayModelValue.value?.commonInfo.sc1Id ?? '',
    defNo: '0',
    defNoArray: ['2'],
    memo: refValue.value!.textareaValue!.value ?? '',
  }
  // Or26866のダイアログ開閉状態を更新する
  Or26866Logic.state.set({
    uniqueCpId: or26866.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  refValueを更新する
 */
function setRefValue() {
  useScreenStore().setCpTwoWay({
    cpId: Or03081Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/**
 * 文章マスタボタンクリック
 *
 * @param value -
 */
const sentenceMasterBtnClick = (value: Mo00045Type) => {
  const columnName =
    screenKey === 'meal' ? 'memo1_knj' : screenKey === 'excretion' ? 'memo2_knj' : 'memo3_knj'
  localOneway.or51775Oneway = {
    title: t('label.special-notes-article'),
    bunruiId: '',
    screenId: 'GUI0080',
    t1Cd: '602',
    t2Cd: '27',
    t3Cd: '0',
    tableName:
      onewayModelValue.value?.commonInfo.ninteiFormF === '4'
        ? 'cpn_tuc_gdl4_kan12_h21 '
        : 'cpn_tuc_gdl5_kan12_r3',
    columnName,
    assessmentMethod: getInitialSettingMaster()?.cpnFlg ?? '',
    inputContents: value.value,
    userId: onewayModelValue.value?.commonInfo?.userId ?? '',
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const handleConfirm = (data: Or51775ConfirmType) => {
  if (data.type === '1') {
    refValue.value!.textareaValue!.value = data.value
  } else {
    refValue.value!.textareaValue!.value += data.value
  }
}

/**************************************************
 * ウォッチャー
 **************************************************/

/**
 *  PINIAイベント監視
 *
 */
watch(
  () => props.modelValue,
  (newValue) => {
    refValue.value = cloneDeep(newValue)
    void setRefValue()
  },

  { deep: true }
)

watch(
  () => or26866Type.value,
  (newVal) => {
    if (!refValue.value!.textareaValue) return
    refValue.value!.textareaValue.value = newVal.rtnContents
  },
  {
    deep: true,
  }
)
</script>

<template>
  <div class="expand-view mb-2">
    <c-v-row no-gutters>
      <c-v-col cols="12">
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="my-4 ml-6"
          >
            <base-mo01338
              :oneway-model-value="{ ...headingTitle, value: localOneway.or03081.headingTitle }"
            />
          </c-v-col>
          <c-v-col
            class="mb-7"
            cols="8 table-header main-table"
          >
            <c-v-data-table
              :headers="localOneway.or03081.tableHeaders"
              class="list-wrapper"
              hide-default-footer
              :items="refValue!.items"
            >
              <template #headers>
                <tr>
                  <!-- 6-①1-1、1-2関係 -->
                  <th
                    style="width: 217px; font-weight: normal !important"
                    class="pl-2 pr-2"
                    rowspan="2"
                  >
                    <div>
                      {{
                        screenKey === 'meal'
                          ? t('label.assessment-home-6-2-2-1-and-2-4-relation')
                          : screenKey === 'excretion'
                            ? t('label.assessment-home-6-2-2-5-and-2-11-relation')
                            : t('label.assessment-home-6-2-2-12-relation')
                      }}
                    </div>
                  </th>
                  <!-- 援助の現状 -->
                  <th
                    style="font-weight: normal !important"
                    colspan="2"
                    class="currentSituationOfAssistance pl-2 pr-2"
                  >
                    {{ t('label.current-situation-of-assistance') }}
                  </th>
                  <!-- 希望 -->
                  <th
                    style="width: 120px; font-weight: normal !important"
                    class="hope pl-2 pr-2"
                    rowspan="2"
                  >
                    <div>
                      {{ t('label.hope') }}
                    </div>
                  </th>
                  <!-- 要援助→計画 -->
                  <th
                    style="width: 120px; font-weight: normal !important"
                    class="needAssistanceToPlan pl-2 pr-2"
                    rowspan="2"
                  >
                    <div>
                      {{ t('label.need-assistance-to-plan') }}
                    </div>
                  </th>
                </tr>
                <tr>
                  <!-- 援助の現状 家族実施 -->
                  <th
                    style="width: 120px; font-weight: normal !important"
                    class="pl-2 pr-2"
                  >
                    <div>
                      {{ t('label.family-implementation') }}
                    </div>
                  </th>
                  <!-- 援助の現状 サービス実施 -->
                  <th
                    style="width: 120px; font-weight: normal !important"
                    class="pl-2 pr-2"
                  >
                    <div>
                      {{ t('label.server-implementation') }}
                    </div>
                  </th>
                </tr>
              </template>
              <!-- 援助の現状 家族実施 -->
              <template #[`item.familyImplementation`]="{ item }">
                <div class="d-flex align-center flex-row h-100">
                  <base-mo01282
                    v-model="item.familyImplementation"
                    :oneway-model-value="selectOptions.familyImplementationOneway"
                  ></base-mo01282>
                </div>
              </template>
              <!-- 援助の現状 サービス実施 -->
              <template #[`item.serverImplementation`]="{ item }">
                <div class="d-flex align-center flex-row h-100">
                  <base-mo01282
                    v-model="item.serverImplementation"
                    :oneway-model-value="selectOptions.serverImplementationOneway"
                  ></base-mo01282>
                </div>
              </template>
              <!-- 希望 -->
              <template #[`item.hope`]="{ item }">
                <div class="d-flex align-center flex-row h-100">
                  <base-mo01282
                    v-model="item.hope"
                    :oneway-model-value="selectOptions.hopeOneway"
                  ></base-mo01282>
                </div>
              </template>
              <!-- 要援助→計画 -->
              <template #[`item.needAssistanceToPlan`]="{ item }">
                <div
                  :id="uniqueCpId + item?.showArrowClassName"
                  :class="['d-flex align-center flex-row h-100', item?.showArrowClassName ?? '']"
                >
                  <base-mo01282
                    v-model="item.needAssistanceToPlan"
                    :oneway-model-value="selectOptions.needAssistanceToPlanOneway"
                  ></base-mo01282>
                </div>
              </template>
            </c-v-data-table>
          </c-v-col>
        </c-v-row>

        <c-v-row no-gutters>
          <c-v-col cols="12">
            <!-- チェックボックスで拡張されたフォーム -->
            <div
              v-if="!!refValue!.checkboxGroupValue[0]?.leftFormValues.length"
              class="expand-checkbox-form-view d-flex"
            >
              <div
                v-for="(item, key) in localOneway.or03081?.expandCheckboxFormInfo"
                :key="key"
                no-gutters
                class="form-content"
              >
                <div>
                  <c-v-row no-gutters>
                    <c-v-col
                      :class="[
                        key !== localOneway.or03081?.expandCheckboxFormInfo.length - 1
                          ? 'border-right-none'
                          : '',
                        'bordered title-style',
                      ]"
                    >
                      {{ item.headingTitle }}
                    </c-v-col>
                  </c-v-row>
                  <c-v-row no-gutters>
                    <!-- 左です -->
                    <c-v-col cols="auto">
                      <c-v-row no-gutters>
                        <c-v-col
                          v-if="!!item?.leftTitle"
                          class="bordered label-style border-right-none"
                          col="12"
                        >
                          <base-mo00615
                            :oneway-model-value="{
                              ...localOneway.mo00615OnewayType,
                              itemLabel: item.leftTitle,
                            }"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row no-gutters>
                        <c-v-col
                          cols="auto"
                          class="checkbox-group bordered border-right-none"
                          :style="{
                            height: localOneway.or03081?.height,
                          }"
                        >
                          <base-mo00018
                            v-for="(subLeftItem, subLeftKey) in item.leftItems"
                            :key="subLeftKey"
                            v-model="refValue!.checkboxGroupValue[key].leftFormValues[subLeftKey]"
                            :oneway-model-value="subLeftItem"
                          />
                          <!-- その他 チェックボックス -->
                          <template v-if="item.isShowOtherItem">
                            <base-mo00045
                              v-model="refValue!.checkboxGroupValue[key].leftOtherInputValue"
                              :oneway-model-value="localOneway.mo00045OtherInputOnewayType"
                            />
                          </template>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                    <!-- 右です -->
                    <c-v-col cols="auto">
                      <c-v-row no-gutters>
                        <c-v-col
                          v-if="!!item?.leftTitle"
                          :class="[
                            key !== localOneway.or03081?.expandCheckboxFormInfo.length - 1
                              ? 'border-right-none'
                              : '',
                            'bordered label-style',
                          ]"
                          cols="12"
                        >
                          <base-mo00615
                            :oneway-model-value="{
                              ...localOneway.mo00615OnewayType,
                              itemLabel: item.rightTitle,
                            }"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row no-gutters>
                        <c-v-col
                          :class="[
                            key !== localOneway.or03081?.expandCheckboxFormInfo.length - 1
                              ? 'border-right-none'
                              : '',
                            'bordered checkbox-group',
                          ]"
                          :style="{
                            height: localOneway.or03081?.height,
                          }"
                          cols="auto"
                        >
                          <base-mo00018
                            v-for="(subRightItem, subRightKye) in item.rightItems"
                            :key="subRightKye"
                            v-model="refValue!.checkboxGroupValue[key].rightFormValues[subRightKye]"
                            :oneway-model-value="subRightItem"
                          />
                          <!-- その他 チェックボックス -->
                          <template v-if="item.isShowOtherItem">
                            <base-mo00045
                              v-model="refValue!.checkboxGroupValue[key].rightOtherInputValue"
                              :oneway-model-value="localOneway.mo00045OtherInputOnewayType"
                            />
                          </template>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </div>
              </div>
            </div>
          </c-v-col>
        </c-v-row>

        <c-v-row
          class="mt-6"
          no-gutters
        >
          <c-v-col>
            <slot name="or03080" />
          </c-v-col>
        </c-v-row>

        <div class="memo-title-style mb-6">
          【{{ t('label.special-note-should-solution-issues') }}】
        </div>
        <!-- 食事、排泄、外出、解決すべき課題など  -->
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="pb-6 px-12"
          >
            <g-custom-or-x-0156
              v-model="refValue!.textareaValue"
              :oneway-model-value="{
                ...localOneway.orX0156OnewayModelValue,
                ...localOneway.or03081.textAreaSetting,
              }"
              @on-click-edit-btn="sentenceMasterBtnClick(refValue!.textareaValue as Mo00045Type)"
            >
              <template #footer>
                <div class="d-flex align-center cursor-pointer ml-4">
                  <div
                    class="font-color"
                    @click="specialNoteShouldSolutionIssuesBtnClick"
                  >
                    {{ t('label.special-note-certification-investigation') }}
                  </div>
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="localOneway.btnTooltip.specialNoteShouldSolutionIssuesBtn"
                  />
                </div>
              </template>
            </g-custom-or-x-0156>
          </c-v-col>
        </c-v-row>
        <c-v-divider v-if="screenKey !== 'goOut'"></c-v-divider>
      </c-v-col>
    </c-v-row>

    <!-- Or51775ダイアログ -->
    <g-custom-or-51775
      v-if="show51755dialog"
      v-bind="or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="handleConfirm"
    />
    <!-- Or26866ダイアログ -->
    <g-custom-or-26866
      v-if="show26866dialog"
      v-bind="or26866"
      v-model="or26866Type"
      :oneway-model-value="localOneway.or26866OnewayModel"
    />
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/base-data-table.scss';
.expand-view {
  background-color: transparent;
}

:deep(.v-table--density-default) {
  --v-table-row-height: 48px !important;
}

.table-header :deep(.v-table__wrapper tr td) {
  font-size: 14px;
  border-right: 1px solid rgb(var(--v-theme-black-200));
  border-bottom: 1px solid rgb(var(--v-theme-black-200)) !important;
  transition: background-color 0s !important;
}

.list-wrapper :deep(tr:nth-child(2) th:last-child) {
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.list-wrapper :deep(.v-table__wrapper tr th) {
  font-size: 14px;
  background-color: rgb(var(--v-theme-blue-100)) !important;
  padding: 8px 12px !important;
}
.list-wrapper :deep(tr td:first-child) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
  padding: 12px !important;
}
.list-wrapper :deep(tr td:not(:first-child)) {
  padding: 1px;
}

.expand-checkbox-form-view {
  background-color: transparent;
}

.form-content {
  .title-style {
    background-color: rgb(var(--v-theme-blue-100)) !important;
    padding: 8px 12px;
    font-size: 13px;
  }
  .label-style {
    font-size: 13px;
    padding: 8px 12px;
    border-top: none !important;
    :deep(.item-label) {
      font-size: 13px;
      line-height: 13px;
    }
  }
  .checkbox-group {
    width: 167px;
    padding: 12px 13px 17.39px 13px;
    border-top: none !important;
  }
  .bordered {
    border: 1px rgb(var(--v-theme-black-200)) solid;
  }
}
.border-right-none {
  border-right: none !important;
}
:deep(.v-text-field) {
  background-color: #fff;
}
.letter-spacing-1 :deep(.v-label) {
  letter-spacing: -1px !important;
}
.letter-spacing-3 :deep(.v-label) {
  letter-spacing: -3px !important;
}
.memo-title-style {
  border: 1px rgb(var(--v-theme-black-200)) solid;
  background: rgb(var(--v-theme-black-100));
  padding: 13.5px 24px !important;
  height: 48px !important;
  font-size: 16px;
  font-weight: bold;
}
.font-color {
  color: #767676;
}
</style>
