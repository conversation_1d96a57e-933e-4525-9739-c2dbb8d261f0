<script setup lang="ts">
/**
 * OrX0007：有機体：計画対象期間選択（特殊コンポーネント）
 * GUI01004_計画書（１）
 * 計画対象期間選択の処理
 *
 * @description
 * 計画対象期間選択の処理
 *
 * <AUTHOR> 王利峰
 */
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0115Const } from '../OrX0115/OrX0115.constants'
import { OrX0115Logic } from '../OrX0115/OrX0115.logic'
import { OrX0007Const } from './OrX0007.constants'
import { OrX0007Logic } from './OrX0007.logic'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import { useScreenStore, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import type { OrX0007OnewayType, OrX0007Type } from '~/types/cmn/business/components/OrX0007Type'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or12002Const } from '~/components/base-components/organisms/Or12002/Or12002.constants'
import { Or12002Logic } from '~/components/base-components/organisms/Or12002/Or12002.logic'

/**************************************************
 * Props
 **************************************************/
const emit = defineEmits(['update:modelValue'])
interface Props {
  onewayModelValue: OrX0007OnewayType
  uniqueCpId: string
  parentMethod: () => boolean | Promise<boolean>
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
const planTargetPeriodDataIndex = ref<number>(-1)

const localOneway = reactive({
  orX0007Oneway: { ...props.onewayModelValue } as OrX0007OnewayType,
  // 計画対象期間タイトルラベル
  mo00615Oneway: {
    itemLabel: t('label.planning-period'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'pa-0 mb-1' }),
  } as Mo00615OnewayType,
  // 計画対象期間
  mo01338Oneway: {
    value: '',
    itemLabelFontWeight: 'bold',
  } as Mo01338OnewayType,
  // 計画対象期間-前へアイコンボタン
  mo00009Twoway: {
    // デフォルト値の設定
    btnIcon: 'chevron_left',
    density: 'compact',
    class: 'icon-page-btn',
  } as Mo00009OnewayType,
  mo01338Twoway: {
    value: '0 / 0',
    itemLabelFontWeight: 'bold',
  } as Mo01338OnewayType,
  // 計画対象期間-次へアイコンボタン
  mo00009Threeway: {
    // デフォルト値の設定
    btnIcon: 'chevron_right',
    density: 'compact',
    class: 'icon-page-btn',
  } as Mo00009OnewayType,
  orX0115Oneway: {} as OrX0115OnewayType,
  kikanDataExitFlg: '',
})

const orX0115 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or12002 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0115Const.CP_ID(1)]: orX0115.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or12002Const.CP_ID(1)]: or12002.value,
})

// ダイアログ表示フラグ
const showDialogOrX0115 = computed(() => {
  // OrX0115 cks_flg=1 のダイアログ開閉状態
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
useScreenTwoWayBind<OrX0007Type>({
  cpId: OrX0007Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 計画対象期間データ更新の監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.orX0007Oneway = {
      ...newValue,
    }
    init()
  },
  { deep: true }
)

/**
 * データ再取得フラグの監視
 */
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      OrX0007Logic.data.set({
        uniqueCpId: props.uniqueCpId,
        value: {
          planTargetPeriodId: newValue.kikanId,
          PlanTargetPeriodUpdateFlg: '0',
        },
      })
      emit('update:modelValue', OrX0007Logic.data.get(props.uniqueCpId))
    }
  }
)

/**
 * Or12002のイベントを監視
 *
 * @description
 * 自身のEventStatus領域のイベントフラグを更新する。
 * またOr12002のボタン押下フラグをリセットする。
 */
watch(
  () => Or12002Logic.event.get(or12002.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.eventFlg) {
      await onClickDialog()
      // 子コンポーネントのflgをリセットする
      Or12002Logic.event.set({
        uniqueCpId: or12002.value.uniqueCpId,
        events: {
          eventFlg: false,
        },
      })
    }
  }
)

/**
 * 計画期間情報初期化
 */
function init() {
  //種別ID
  localOneway.orX0115Oneway.kindId = localOneway.orX0007Oneway.kindId
  //   ・計画期間が登録されている場合、計画期間リスト1件目.開始日 + " ～ " + 計画期間リスト1件目.終了日
  // ・上記以外の場合、固定文字「計画対象期間アイコンボタンから（改行）期間登録を行ってください」で表示※太字、赤色
  if (
    localOneway.orX0007Oneway.planTargetPeriodData &&
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount > 0
  ) {
    //計画対象期間ID
    localOneway.orX0115Oneway.sc1Id = String(
      localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId
    )
    localOneway.mo01338Oneway.value =
      localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriod!
    localOneway.mo01338Twoway.value =
      localOneway.orX0007Oneway.planTargetPeriodData.currentIndex +
      ' / ' +
      localOneway.orX0007Oneway.planTargetPeriodData.totalCount
    localOneway.mo01338Oneway.itemLabelFontWeight = 'bold'
    planTargetPeriodDataIndex.value = localOneway.orX0007Oneway.planTargetPeriodData.currentIndex
    localOneway.kikanDataExitFlg = ''
  } else {
    localOneway.mo01338Oneway.value = t('label.plan-no-register')
    localOneway.mo01338Oneway.itemLabelFontWeight = undefined
    localOneway.mo01338Twoway.value =
      localOneway.orX0007Oneway.planTargetPeriodData.currentIndex +
      ' / ' +
      localOneway.orX0007Oneway.planTargetPeriodData.totalCount
    localOneway.kikanDataExitFlg = 'none-data'
  }
}
/**
 *  期間選択ボタン押下時の処理
 */
async function onClickDialog() {
  //親画面からの画面入力データに変更があるかの判断と保存処理を呼び出す
  if (!(await props.parentMethod())) {
    return
  }

  //期間選択ダイアログを呼び出す
  OrX0115Logic.state.set({
    uniqueCpId: orX0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  計画対象期間-前へアイコンボタンボタン押下時の処理
 */
function onClickMo00009Twoway() {
  if (
    localOneway.kikanDataExitFlg === 'none-data' ||
    (OrX0007Logic.data.get(props.uniqueCpId)?.PlanTargetPeriodUpdateFlg ?? '') !== ''
  ) {
    return
  }
  //計画期間変更区分：1:選択している期間IDの前
  OrX0007Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      planTargetPeriodId: OrX0007Logic.data.get(props.uniqueCpId)!.planTargetPeriodId,
      PlanTargetPeriodUpdateFlg: '1',
    },
  })
  console.log(
    '計画対象期間-前へアイコンボタンボタン押下時 Edit Flg：',
    useScreenStore().isEditNavControl()
  )
  emit('update:modelValue', OrX0007Logic.data.get(props.uniqueCpId))
}

/**
 *  計画対象期間-計画対象期間-計画対象期間-前へアイコンボタンボタン押下時の処理
 *
 */
function onClickMo00009Threeway() {
  if (
    localOneway.kikanDataExitFlg === 'none-data' ||
    (OrX0007Logic.data.get(props.uniqueCpId)?.PlanTargetPeriodUpdateFlg ?? '') !== ''
  ) {
    return
  }
  //計画期間変更区分設定：2:選択している期間IDの次
  OrX0007Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      planTargetPeriodId: OrX0007Logic.data.get(props.uniqueCpId)!.planTargetPeriodId,
      PlanTargetPeriodUpdateFlg: '2',
    },
  })
  console.log(
    '計画対象期間-計画対象期間-計画対象期間-前へアイコンボタンボタン押下時 Edit Flg：',
    useScreenStore().isEditNavControl()
  )
  emit('update:modelValue', OrX0007Logic.data.get(props.uniqueCpId))
}

defineExpose({
  onClickDialog,
})
</script>

<template>
  <c-v-row no-gutters>
    <!--計画対象期間 label-->
    <c-v-col cols="auto">
      <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway" />
    </c-v-col>
  </c-v-row>
  <c-v-row
    no-gutters
    class="align-center kikan-field"
  >
    <!--計画対象期間 prop click buttun-->
    <g-base-or-12002
      v-bind="or12002"
      :class="['icon-edit-btn']"
    />
    <div :class="['d-flex', 'align-center', 'kikan-label', localOneway.kikanDataExitFlg]">
      <base-at-label
        :value="localOneway.mo01338Oneway.value"
        :font-weight="localOneway.mo01338Oneway.itemLabelFontWeight"
      />
    </div>
    <!-- 計画対象期間-前へアイコンボタン -->
    <base-mo00009
      :oneway-model-value="localOneway.mo00009Twoway"
      @click="onClickMo00009Twoway()"
    />
    <!-- 画対象期間-ページング -->
    <div :class="['d-flex', 'align-center', 'kikan-page-label']">
      <base-at-label
        :value="localOneway.mo01338Twoway.value"
        :font-weight="localOneway.mo01338Twoway.itemLabelFontWeight"
      />
    </div>
    <!--計画対象期間-次へアイコンボタン-->
    <base-mo00009
      :oneway-model-value="localOneway.mo00009Threeway"
      @click="onClickMo00009Threeway"
    />
  </c-v-row>
  <!--GUI00070 対象期間画面-->
  <g-custom-or-x-0115
    v-if="showDialogOrX0115"
    v-bind="orX0115"
    :oneway-model-value="localOneway.orX0115Oneway"
  />
</template>

<style scoped lang="scss">
$border-color: rgb(var(--v-theme-form));
$icon-edit-btn-width: 34px;
$icon-border-radius: 3px;
$default-height: 34px;
.kikan-field {
  border: 1px solid $border-color;
  border-top-left-radius: $icon-border-radius !important;
  border-end-start-radius: $icon-border-radius !important;
  border-top-right-radius: $icon-border-radius !important;
  border-end-end-radius: $icon-border-radius !important;
}
.icon-edit-btn {
  color: rgb(var(--v-theme-light-blue-400));
  width: $icon-edit-btn-width;
  height: $default-height;
}
.kikan-label {
  padding: 0 11px;
  background-color: rgb(var(--v-theme-black-100));
  height: $default-height;
  border-left: 1px solid $border-color;
}
.kikan-page-label {
  padding: 0 13px;
  background-color: rgb(var(--v-theme-black-100));
  height: $default-height;
  border-left: 1px solid $border-color;
  min-width: 67px;
  justify-content: center;
}
.icon-page-btn {
  border-left: 1px solid $border-color;
  border-radius: 0;
  height: $default-height;
  width: $icon-edit-btn-width;
  color: rgb(var(--v-theme-blue-900));
}
.none-data {
  label {
    color: rgb(var(--v-theme-red-700));
    white-space: pre-line;
  }
}

</style>
