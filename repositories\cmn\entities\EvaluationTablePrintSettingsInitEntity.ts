import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * GUI01240_印刷設定
 *
 * @description
 * GUI01240_印刷設定 画面API用エンティティ
 *
 * <AUTHOR>
 */
/**
 * 帳票エンティティ
 */
export interface PrtEntity {
  /**
   * インデックス
   */
  index: string
  /**
   * システム略称
   */
  sysRyaku: string
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * 帳票リスト名
   */
  listName: string
  /**
   * 帳票名
   */
  defPrtTitle: string
  /**
   * 帳票タイトル
   */
  prtTitle: string
  /**
   * セクション番号
   */
  sectionNo: string
  /**
   * 帳票番号
   */
  prtNo: string
  /**
   * プロファイル
   */
  profile: string
  /**
   * 日付表示有無
   */
  prnDate: string
  /**
   * 職員表示有無
   */
  prnshoku: string
  /**
   * オブジェクト名
   */
  dwobject: string
  /**
   * 用紙向き
   */
  prtOrient: string
  /**
   * 用紙サイズ
   */
  prtSize: string
  /**
   * 帳票リスト名
   */
  listTitle: string
  /**
   * 上余白
   */
  mtop: string
  /**
   * 下余白
   */
  mbottom: string
  /**
   * 左余白
   */
  mleft: string
  /**
   * 右余白
   */
  mright: string
  /**
   * ルーラ表示有無
   */
  ruler: string
  /**
   * シリアルフラグ
   */
  serialFlg: string
  /**
   * モードフラグ
   */
  modFlg: string
  /**
   * セクションフラグ
   */
  secFlg: string
  /**
   * 高さ
   */
  serialHeight: string
  /**
   * 印刷行数
   */
  serialPagelen: string
  /**
   * 表示内拡大率
   */
  zoomRate: string
  /**
   * パラメータ01
   */
  param01: string
  /**
   * パラメータ02
   */
  param02: string
  /**
   * パラメータ03
   */
  param03: string
  /**
   * パラメータ04
   */
  param04: string
  /**
   * パラメータ05
   */
  param05: string
  /**
   * パラメータ06
   */
  param06: string
  /**
   * パラメータ07
   */
  param07: string
  /**
   * パラメータ08
   */
  param08: string
  /**
   * パラメータ09
   */
  param09: string
  /**
   * パラメータ10
   */
  param10: string
  /**
   * パラメータ11
   */
  param11: string
  /**
   * パラメータ12
   */
  param12: string
  /**
   * パラメータ13
   */
  param13: string
  /**
   * パラメータ14
   */
  param14: string
  /**
   * パラメータ15
   */
  param15: string
  /**
   * パラメータ16
   */
  param16: string
  /**
   * パラメータ17
   */
  param17: string
  /**
   * パラメータ18
   */
  param18: string
  /**
   * パラメータ19
   */
  param19: string
  /**
   * パラメータ20
   */
  param20: string
  /**
   * パラメータ21
   */
  param21: string
  /**
   * パラメータ22
   */
  param22: string
  /**
   * パラメータ23
   */
  param23: string
  /**
   * パラメータ24
   */
  param24: string
  /**
   * パラメータ25
   */
  param25: string
  /**
   * パラメータ26
   */
  param26: string
  /**
   * パラメータ27
   */
  param27: string
  /**
   * パラメータ28
   */
  param28: string
  /**
   * パラメータ29
   */
  param29: string
  /**
   * パラメータ30
   */
  param30: string
  /**
   * パラメータ31
   */
  param31: string
  /**
   * パラメータ32
   */
  param32: string
  /**
   * パラメータ33
   */
  param33: string
  /**
   * パラメータ34
   */
  param34: string
  /**
   * パラメータ35
   */
  param35: string
  /**
   * パラメータ36
   */
  param36: string
  /**
   * パラメータ37
   */
  param37: string
  /**
   * パラメータ38
   */
  param38: string
  /**
   * パラメータ39
   */
  param39: string
  /**
   * パラメータ40
   */
  param40: string
  /**
   * パラメータ41
   */
  param41: string
  /**
   * パラメータ42
   */
  param42: string
  /**
   * パラメータ43
   */
  param43: string
  /**
   * パラメータ44
   */
  param44: string
  /**
   * パラメータ45
   */
  param45: string
  /**
   * パラメータ46
   */
  param46: string
  /**
   * パラメータ47
   */
  param47: string
  /**
   * パラメータ48
   */
  param48: string
  /**
   * パラメータ49
   */
  param49: string
  /**
   * パラメータ50
   */
  param50: string
  /**
   * 更新回数
   */
  modifiedCnt: string
}

/**
 * 印刷設定情報エンティティ
 */
export interface SysIniInfoEntity {
  /**
   * 氏名伏字設定フラグ
   */
  amikakeFlg: string
  /**
   * 氏名伏字更新回数
   */
  amikakeModifiedCnt: string
  /**
   * 文章管理設定フラグ
   */
  iso9001Flg: string
  /**
   * 文章管理更新回数
   */
  iso9001ModifiedCnt: string
  /**
   * 個人情報設定フラグ
   */
  kojinhogoFlg: string
  /**
   * 個人情報更新回数
   */
  kojinhogoModifiedCnt: string
}

/**
 * 履歴情報エンティティ
 */
export interface HistoryEntity {
  /**
   * 選択
   */
  sel: string
  /**
   * アセスメントID
   */
  assId: string
  /**
   * 担当者名
   */
  shokuinKnj: string
  /**
   * 作成日
   */
  createYmd: string
  /**
   * 様式名称
   */
  youshikiKnj: string
}

/**
 * チェック項目履歴エンティティ
 */
export interface EvaluationTableHistoryEntity {
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * 選択
   */
  sel: string
  /**
   * ヘーダID
   */
  cmoni1Id: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 作成日
   */
  createYmd: string
  /**
   * 作成者
   */
  shokuId: string
  /**
   * 担当者名
   */
  shokuinKnj: string
}

/**
 * 利用者エンティティ
 */
export interface UserEntity {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 利用者名
   */
  userName: string
}

/**
 * 帳票印刷結果情報エンティティ
 */
export interface PrintSubjectHistoryEntity {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 利用者名
   */
  userName: string
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * ヘーダID
   */
  cmoni1Id: string
  /**
   * 作成日
   */
  createYmd: string
  /**
   * 結果
   */
  result: string
}

/**
 * 印刷設定初期情報取得入力エンティティ
 */
export interface EvaluationTablePrintSettingsInitSelectInEntity extends InWebEntity {
  /**
   * システム略称
   */
  sysRyaku: string
  /**
   * セクション名
   */
  sectionName: string
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * システムコード
   */
  gsyscd: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * メニュー２名称
   */
  menu2Knj: string
  /**
   * メニュー３名称
   */
  menu3Knj: string
  /**
   * インデックス(ディフォルト値：0)
   */
  index: string
  /**
   * 個人情報使用フラグ(0：不使用、1：使用)
   */
  kojinhogoUsedFlg: string
  /**
   * 個人情報番号(0：主に日誌以外、1：主に日誌系)
   */
  sectionAddNo: string
}

/**
 * 印刷設定初期情報取得出力エンティティ
 */
export interface EvaluationTablePrintSettingsInitSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 出力帳票印刷情報リスト
     */
    prtList: PrtEntity[]
    /**
     * システムINI情報
     */
    sysIniInfo: SysIniInfoEntity
    /**
     * 担当ケアマネ
     */
    tantoKnj: string
    /**
     * 期間管理フラグ
     */
    kikanFlg: string
    /**
     * 評価表履歴リスト
     */
    evaluationTableHistoryList: EvaluationTableHistoryEntity[]
  }
}

/**
 * 評価表履歴情報を取得する入力エンティティ
 */
export interface EvaluationTablePrintSettingsHistorySelectInEntity extends InWebEntity {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 期間管理フラグ(管理しない／管理する)
   */
  kikanFlg: string
}

/**
 * 評価表履歴情報を取得する出力エンティティ
 */
export interface EvaluationTablePrintSettingsHistorySelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     *  評価表履歴情報リスト
     */
    evaluationTableHistoryList: EvaluationTableHistoryEntity[]
  }
}

/**
 * 印刷対象履歴リストを取得する入力エンティティ
 */
export interface EvaluationTablePrintSettingsSubjectSelectInEntity extends InWebEntity {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 基準日
   */
  kijunbiYmd: string
  /**
   * 利用者リスト
   */
  userList: UserEntity[]
}

/**
 * 印刷対象履歴リストを取得する出力エンティティ
 */
export interface EvaluationTablePrintSettingsSubjectSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 印刷対象履歴リスト
     */
    printSubjectHistoryList: PrintSubjectHistoryEntity[]
  }
}
