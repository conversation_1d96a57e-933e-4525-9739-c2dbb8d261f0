<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { OrX0007Const } from '../OrX0007/OrX0007.constants'
import { OrX0008Const } from '../OrX0008/OrX0008.constants'
import { OrX0129Const } from '../OrX0129/OrX0129.constants'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { OrX0010Const } from '../OrX0010/OrX0010.constants'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0008Logic } from '../OrX0008/OrX0008.logic'
import { Or26846Logic } from '../Or26846/Or26846.logic'
import { Or26846Const } from '../Or26846/Or26846.constants'
import { OrX0129Logic } from '../OrX0129/OrX0129.logic'
import { OrX0010Logic } from '../OrX0010/OrX0010.logic'
import { OrX0009Logic } from '../OrX0009/OrX0009.logic'
import { Or10583Const } from '../Or10583/Or10583.constants'
import { Or10583Logic } from '../Or10583/Or10583.logic'
import { Or10821Const } from '../Or10821/Or10821.constants'
import { Or10821Logic } from '../Or10821/Or10821.logic'
import { Or32447Logic } from '../Or32447/Or32447.logic'
import { Or32447Const } from '../Or32447/Or32447.constants'
import { Or28394Const } from '../Or28394/Or28394.constants'
import { Or28394Logic } from '../Or28394/Or28394.logic'
import type { Or32398StateType, OrX0007RefFunc,OrX0129RefFunc } from './Or32398.type'
import { Or32398Const } from './Or32398.constants'
import type {
  IInitMasterInfo,
  IKikanComInfo,
} from '~/repositories/cmn/entities/GyoumuComSelectEntity'
import type { Or28394OnewayType } from '~/types/cmn/business/components/Or28394Type'
import type { OfficeDataType } from '~/types/cmn/OfficeDataType'
import type { Or26846OneWayType } from '~/types/cmn/business/components/Or26846Type'
import type {
  CarePlanPrintAllInData,
  Or10583OnewayType,
} from '~/types/cmn/business/components/Or10583Type'
import type { Or10821OnewayType, Or10821Type } from '~/types/cmn/business/components/Or10821Type'
import type { NikkaPtn } from '~/repositories/cmn/entities/DailyPlanPatternSettingInitSelectEntity'
import type {
  DailyRoutinePlanInitSelectInEntity,
  DailyRoutinePlanInitSelectOutEntity,
  IHistory,
  ICnikka2DataInfo,
  DailyRoutinePlanInitMasterInfo,
  DailyRoutinePlanInitSelectOutData,
} from '~/repositories/cmn/entities/DailyRoutinePlanInitSelectEntity'
import type { Or32447Type, Or32447OnewayType } from '~/types/cmn/business/components/Or32447Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type {
  DailyRoutinePlanUpdateInEntity,
  ICnikka2DataSave,
} from '~/repositories/cmn/entities/DailyRoutinePlanUpdateEntity'
import type { OrX0007Type, OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import type {
  OrX0008Type,
  OrX0008OnewayType,
  RirekiInfo,
} from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009Type, OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0010Type, OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import type {
  DailyRoutinePlanCopyReturnSelectInEntity,
  DailyRoutinePlanCopyReturnSelectOutEntity,
} from '~/repositories/cmn/entities/DailyRoutinePlanCopyReturnSelectEntity'
import type {
  DailyRoutinePlanPtnTitleCntSelectInEntity,
  DailyRoutinePlanPtnTitleCntSelectOutEntity,
} from '~/repositories/cmn/entities/DailyRoutinePlanPtnTitleCntSelectEntity'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import {
  useCommonProps,
  useScreenInitFlg,
  useScreenStore,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
  useGyoumuCom,
  useJigyoList,
} from '#imports'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { SPACE_WAVE, UPDATE_KBN } from '~/constants/classification-constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type {
  Or21814OnewayType,
  Or21814EventType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { hasRegistAuth, hasPrintAuth } from '~/utils/useCmnAuthz'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type {
  OrX0129Type,
  OrX0129OnewayType,
  GamenCnikka2DataInfo,
} from '~/types/cmn/business/components/OrX0129Type'
import type { GyoumuComUpdateOutEntity } from '~/repositories/cmn/entities/GyoumuComUpdateEntity'
import { useColorUtils } from '~/utils/useColorUtils'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { userList } from '~/repositories/cmn/entities/PrintSelectEntity'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type {
  Or21815StateType,
  Or21815EventType,
} from '~/components/base-components/organisms/Or21815/Or21815.type'
/**
 * Or32398:有機体:日課計画メイン（画面/特殊コンポーネント）
 * GUI01057_日課計画
 *
 * @description
 * 日課計画メイン画面の処理
 *
 * <AUTHOR>
 */
const { t } = useI18n()
// route共有情報
const cmnRouteCom = useCmnRouteCom()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const gyoumuCom = useGyoumuCom()
/** props */
const props = defineProps(useCommonProps())
const { syscomUserSelectWatchFunc } = useUserListInfo()
const { setChildCpBinds, searchUniqCpId } = useScreenUtils()
const { jigyoListWatch } = useJigyoList()
const { convertDecimalToHex, convertHexToDecimal } = useColorUtils()
/**************************************************
 * 共通情報
 **************************************************/
const defaultModelValue = {
  or32398: {
    /** 利用者一覧の選択値 */
    operaFlg: Or32398Const.OPERA_FLG_0,
    svJigyoRyakuKnj: '',
    kihonJokyoFlg: '0',
    kihonCheckListFlg: '0',
    kaiteiFlg: '',
    kikanFlg: '',
    kikanObj: {} as IKikanComInfo,
    rirekiObj: {} as IHistory,
    cnikka2DataList: [] as ICnikka2DataInfo[],
    syubetuId: '',
    moveSaveConfirmFlg: Or32398Const.COMFIRM_SAVE_0,
    rirekiPagingNo: '',
    rirekiId: '0',
    //初期設定マスタの情報
    initMasterObj: {
      // メッセージ表示(1:表示、0:非表示)
      msgFlg: Or32398Const.MSG_0,
      // 取込先(1：取込する、0：取込しない)
      nikkaTorikomisakiFlg: '0',
      // 時間表示(1：先頭に表示する、2：最後に表示する、0：表示しない)
      nikkaTimeFlg: '0',
      // 文字サイズフラグ(0：極小、1：小、2：中、3：大)
      nikkaSizeFlg: '0',
      // 文字位置フラグ(0：左、1：右、2：中央)
      nikkaPosFlg: '0',
      // 背景色
      nikkaBackcolor: '',
      // 文字色
      nikkaFontcolor: '',
      // 計画表様式（予防計画書）(0:A3、1:A4-3枚、2:A4-2枚)
      itakuKkakPrtFlg: '0',
      // 計画書様式(1:施設、2:居宅)
      cksFlg: '1',
      // 保険サービス（事業所名）（予防計画書）(0:正式、1:略称)
      itakuKkakHsJigyoFlg: '0',
      // 頻度取込（予防計画書）(0:選択項目名称 1:週○回/月○回 2:随時)
      itakuKkakHindoFlg: '0',
      // ケアプラン方式(0:未作成、1:包括的、2:居宅GL、3:MDS-HC2.0、4:MDS2.1)
      cpnFlg: '0',
      // 承認欄情報(0：共有する、1：帳票毎保持する)
      shoninFlg: '0',
      // 印刷帳票の敬称オプション(0：変更しない、1：変更する)
      keishoFlg: '0',
      // 印刷帳票の敬称
      keishoKnj: '',
    } as DailyRoutinePlanInitMasterInfo,
    /** 初回フラグ  */
    onMountedOpenFlg: Or32398Const.ONMOUNTED_OPEN_0,
  } as Or32398StateType,
}

const defaultComponents = {
  // 期間データ
  orX0007: { PlanTargetPeriodUpdateFlg: '' } as OrX0007Type,
  // 履歴
  orX0008: {
    createId: '0',
    createUpateFlg: '',
  } as OrX0008Type,
  // 作成者
  orX0009: { staffId: '', staffName: '' } as OrX0009Type,
  // 作成日
  orX0010: { value: '' } as OrX0010Type,
  // 日程カレンダー
  orX0129: {} as OrX0129Type,
  // GUI01061_日課計画複写画面
  or32447: { rirekiId: '' } as Or32447Type,
}

const local = reactive({
  or32398: {
    ...defaultModelValue.or32398,
  } as Or32398StateType,
  // GUI01060 日課計画パターン
  or10821: {} as Or10821Type,
})

const localComponents = reactive({
  orX0007: {
    ...defaultComponents.orX0007,
  } as OrX0007Type,
  orX0008: {
    ...defaultComponents.orX0008,
  } as OrX0008Type,
  orX0009: {
    ...defaultComponents.orX0009,
  } as OrX0009Type,
  orX0010: {
    ...defaultComponents.orX0010,
  } as OrX0010Type,
  orX0129: {
    ...defaultComponents.orX0129,
  } as OrX0129Type,
  or32447: {
    ...defaultComponents.or32447,
  } as Or32447Type,
})

const defaultOneway = reactive({
  // 期間データ
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as OrX0007OnewayType,
  // 履歴
  orX0008Oneway: {
    createData: {} as RirekiInfo,
    screenID: 'GUI01057',
  } as OrX0008OnewayType,
  // 作成者
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
    isDisabled: false,
  } as OrX0009OnewayType,
  // 作成日
  orX0010Oneway: {} as OrX0010OnewayType,
  // 日程カレンダー
  orX0129Oneway: {} as OrX0129OnewayType,
  // GUI01056 日課計画マスタ画面
  or26846Oneway: {
    shisetuId: systemCommonsStore.getShisetuId,
    svJigyoId: systemCommonsStore.getSvJigyoId,
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList,
  } as Or26846OneWayType,
  // GUI00936 計画書一括印刷画面
  or10583Oneway: {} as Or10583OnewayType,
  // GUI01059 日課計画パターン設定画面
  or10821Oneway: {} as Or10821OnewayType,
  // GUI01061_日課計画複写画面
  or32447Oneway: {} as Or32447OnewayType,
  // GUI01065_印刷設定画面
  or28394Oneway: {} as Or28394OnewayType,
})

const localOneway = reactive({
  orX0007Oneway: {
    ...defaultOneway.orX0007Oneway,
  } as OrX0007OnewayType,
  orX0008Oneway: {
    ...defaultOneway.orX0008Oneway,
  } as OrX0008OnewayType,
  orX0009Oneway: {
    ...defaultOneway.orX0009Oneway,
  } as OrX0009OnewayType,
  orX0010Oneway: {
    ...defaultOneway.orX0010Oneway,
  } as OrX0010OnewayType,
  orX0129Oneway: {
    ...defaultOneway.orX0129Oneway,
  } as OrX0129OnewayType,
  or26846Oneway: {
    ...defaultOneway.or26846Oneway,
  } as Or26846OneWayType,
  or10583Oneway: {
    ...defaultOneway.or10583Oneway,
  } as Or10583OnewayType,
  or10821Oneway: {
    ...defaultOneway.or10821Oneway,
  } as Or10821OnewayType,
  or32447Oneway: {
    ...defaultOneway.or32447Oneway,
  } as Or32447OnewayType,
  or28394Oneway: {
    ...defaultOneway.or28394Oneway,
  } as Or28394OnewayType,
})

const or11871 = ref({ uniqueCpId: '' })
const or00248_1 = ref({ uniqueCpId: '' })
const or41179_1 = ref({ uniqueCpId: '' })
const orX0007_1 = ref({ uniqueCpId: '' })
const orX0008_1 = ref({ uniqueCpId: '' })
const orX0009_1 = ref({ uniqueCpId: '' })
const orX0010_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const orX0129_1 = ref({ uniqueCpId: '' })
const or26846_1 = ref({ uniqueCpId: '' })
const or10583_1 = ref({ uniqueCpId: '' })
const or10821_1 = ref({ uniqueCpId: '' })
const or32447_1 = ref({ uniqueCpId: '' })
const or28394_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
const orX0129Ref = ref<OrX0129RefFunc>()
const orX0007Ref = ref<OrX0007RefFunc | null>(null)

// ダイアログ表示フラグ
const showDialogOr26846 = computed(() => {
  return Or26846Logic.state.get(or26846_1.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr10583 = computed(() => {
  return Or10583Logic.state.get(or10583_1.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr10821 = computed(() => {
  return Or10821Logic.state.get(or10821_1.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr32447 = computed(() => {
  return Or32447Logic.state.get(or32447_1.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr28394 = computed(() => {
  return Or28394Logic.state.get(or28394_1.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
const isInit = useScreenInitFlg()

onMounted(async () => {
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      disabledSaveBtn: !(await hasRegistAuth(Or32398Const.LINK_AUTH)), //共通処理の保存権限チェックを行う
      disabledPrintBtn: !(await hasPrintAuth(Or32398Const.LINK_AUTH)), //共通処理の印刷権限チェックを行う
    },
  })
  // 利用者を全選択です。
  const uniqueCpId248 = searchUniqCpId(props.uniqueCpId, Or00248Const.CP_ID(1), 0)
  if (uniqueCpId248) {
    const uniqueCpId94 = searchUniqCpId(uniqueCpId248, Or00094Const.CP_ID(0), 0)
    if (uniqueCpId94) {
      Or00094Logic.state.set({
        uniqueCpId: uniqueCpId94,
        state: {
          dispSettingBtnDisplayFlg: true,
          focusSettingFlg: true,
          focusSettingInitial: [Or32398Const.STR_ALL],
        },
      })
    }
  }
  local.or32398.onMountedOpenFlg = Or32398Const.ONMOUNTED_OPEN_0
  // 事業所選択プルダウンの検索条件設定
  // 検索条件未指定だと、ヘッダーで選択した適用事業所が表示される
  // 検索条件が変更されると、共通処理にて事業所の情報が更新される
  // 更新後やプルダウンの値変更後、変更後の事業所に基づいて処理を行う場合、
  // 関数「 jigyoListWatch 」にコールバック関数を渡す
  local.or32398.searchSelfUserID = systemCommonsStore.getUserSelectSelfId() ?? ''
  // 初期情報取得
  if (isInit) {
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        verticalLayout: true,// 縦並びレイアウト
        labelClassVertical: 'mb-1',
        selectClassVertical: 'mt-1',
        searchCriteria: {
          selfId: local.or32398.searchSelfUserID,
        },
      },
    })
    await initOr32398()
  }
  local.or32398.onMountedOpenFlg = Or32398Const.ONMOUNTED_OPEN_1
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248_1.value,
  [Or41179Const.CP_ID(1)]: or41179_1.value,
  [OrX0007Const.CP_ID(1)]: orX0007_1.value,
  [OrX0008Const.CP_ID(1)]: orX0008_1.value,
  [OrX0009Const.CP_ID(1)]: orX0009_1.value,
  [OrX0010Const.CP_ID(1)]: orX0010_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [OrX0129Const.CP_ID(1)]: orX0129_1.value,
  [Or26846Const.CP_ID(1)]: or26846_1.value,
  [Or10583Const.CP_ID(1)]: or10583_1.value,
  [Or10821Const.CP_ID(1)]: or10821_1.value,
  [Or32447Const.CP_ID(1)]: or32447_1.value,
  [Or28394Const.CP_ID(1)]: or28394_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
})

const or00249 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(or00248_1.value.uniqueCpId, {
  [Or00249Const.CP_ID(0)]: or00249.value,
})

Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.daily-care-plan'),
    showFavorite: true,
    showViewSelect: false,
    showCreateBtn: true,
    tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    viewSelectItems: [],
    showMasterBtn: true,
    showOptionMenuDelete: true,
    showSaveBtn: true,
    tooltipTextSaveBtn: t('tooltip.care-plan2-save-btn'),
  },
})

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    // お気に入りアイコンボタン
    if (newValue.favoriteEventFlg) {
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { favoriteEventFlg: false },
      })
    }
    // 保存ボタンが押下された場合、保存処理を実行する
    if (newValue.saveEventFlg) {
      await _save()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { saveEventFlg: false },
      })
    }
    // 新規ボタンが押下された場合、新規作成処理を実行する
    if (newValue.createEventFlg) {
      await _create()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { createEventFlg: false },
      })
    }
    // 印刷ボタンが押下された場合、印刷設定画面を表示する
    if (newValue.printEventFlg) {
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { printEventFlg: false },
      })
    }
    // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    if (newValue.masterEventFlg) {
      await _master()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { masterEventFlg: false },
      })
    }
    // 削除ボタンが押下された場合、削除処理を実行する
    if (newValue.deleteEventFlg) {
      await _delete()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { deleteEventFlg: false },
      })
    }
    // 複写ボタンが押下された場合、複写処理を実行する
    if (newValue.copyEventFlg) {
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { copyEventFlg: false },
      })
    }
  }
)

/**
 * 事業所選択更新の監視
 */
// ★事業所選択監視関数を実行
jigyoListWatch(or41179_1.value.uniqueCpId, (newJigyoId: string) => {
  void callbackFuncJigyo(newJigyoId)
})

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function callbackFuncJigyo(newJigyoId: string) {
  const oldJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  await gyoumuCom.doComLogicChangeSvJigyo(
    newJigyoId,
    oldJigyoId,
    local.or32398,
    isEdit.value,
    await hasRegistAuth(Or32398Const.LINK_AUTH),
    showConfirmMessageBox,
    showWarnMessageBox,
    _save,
    getDailyRoutinePlanInitData,
    setCommonPlanPeriod,
    setSvJigyoId,
    setSelectSelUserIndex
  )
}

/**
 * 事業所設定
 *
 * @param jigyoId - 設定の事業者ID
 */
const setSvJigyoId = (jigyoId: string) => {
  systemCommonsStore.setSvJigyoId(jigyoId)
  Or41179Logic.data.set({
    uniqueCpId: or41179_1.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    } as Mo00040Type,
  })
}

/**
 * 利用者選択監視関数に渡すコールバック関数（サブ01の例）
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 利用者
 */
const callbackFuncUser = (newSelfId: string) => {
  local.or32398.newSelfUserID = newSelfId
  if (newSelfId !== '') {
    // 事業所選択プルダウンの検索条件を更新
    // 検索条件を変更すると、変更を検知して事業所情報の更新が行われる
    // 更新が完了すると、関数「 jigyoListWatch 」に指定したコールバック関数が実行されます
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: newSelfId,
        },
      },
    })
  }
}

/**
 * 利用者設定
 *
 * @param index - 利用者IDのインデックス
 */
const setSelectSelUserIndex = (index: number) => {
  //利用者設定
  Or00249Logic.data.set({
    uniqueCpId: or00249.value.uniqueCpId,
    value: {
      selectUserIndex: index,
    },
  })
}

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackFuncUser)

/**
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007_1.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    // 計画期間変更区分
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg
    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }
    // 計画期間ID
    const planID = newValue.planTargetPeriodId
    // ポップアップ画面で「確定ボタン」押下、ポップアップ画面での返回値.計画期間IDが0より大きい場合
    if (planUpdateFlg === Or32398Const.ACTION_UPDATE_0 && planID !== '' && Number(planID) > 0) {
      // 共通情報.計画期間ID=返回値.計画期間ID
      setCommonPlanPeriod({ ...local.or32398.kikanObj, sc1Id: planID })
      //共通情報.履歴ID ＝ 0
      local.or32398.rirekiId = '0'
      //操作区分 ＝ K3:計画対象期間Open
      local.or32398.operaFlg = Or32398Const.OPERA_FLG_K3
      //データ再取得フラグ設定
      await getDailyRoutinePlanInitData()
    } else if (planUpdateFlg === Or32398Const.ACTION_UPDATE_1) {
      //「期間-前へ アイコンボタン」押下
      // 下記の項目を設定する
      local.or32398.operaFlg = Or32398Const.OPERA_FLG_K1
      local.or32398.rirekiId = '0'
      //遷移保存確認区分
      local.or32398.moveSaveConfirmFlg = isEdit.value
        ? Or32398Const.COMFIRM_SAVE_1
        : Or32398Const.COMFIRM_SAVE_0
      //データ再取得フラグ設定
      await getDailyRoutinePlanInitData()
    } else if (planUpdateFlg === Or32398Const.ACTION_UPDATE_2) {
      //「期間-次へ アイコンボタン」押下
      local.or32398.operaFlg = Or32398Const.OPERA_FLG_K2
      local.or32398.rirekiId = '0'
      //遷移保存確認区分
      local.or32398.moveSaveConfirmFlg = isEdit.value
        ? Or32398Const.COMFIRM_SAVE_1
        : Or32398Const.COMFIRM_SAVE_0
      //データ再取得フラグ設定
      await getDailyRoutinePlanInitData()
    }
  },
  { deep: true }
)

/**
 * 履歴変更の監視
 * GUI00952 ［履歴選択］画面 日課計画画面をポップアップで起動する
 */
watch(
  () => OrX0008Logic.data.get(orX0008_1.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }

    const createUpateFlg = newValue.createUpateFlg
    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }

    // ポップアップ画面での返回値.履歴IDが0より大きい場合（※ポップアップ画面で「確定ボタン」押下）
    // かつ、返回値.履歴IDが共通情報.履歴IDが違い場合
    if (createUpateFlg === Or32398Const.ACTION_UPDATE_0) {
      //「履歴-選択確認後 アイコンボタン」押下
      const rirekiId = String(newValue.createId)
      if (rirekiId !== local.or32398.rirekiId) {
        local.or32398.rirekiId = rirekiId
        local.or32398.operaFlg = Or32398Const.OPERA_FLG_R3

        // 表示用「日課計画_履歴」情報を設定する
        // 履歴ID
        local.or32398.rirekiObj.rirekiId = rirekiId
        // 作成者ＩＤ
        local.or32398.rirekiObj.shokuId = newValue.rirekiObj?.staffId ?? ''
        // 作成者
        local.or32398.rirekiObj.shokuKnj = newValue.rirekiObj?.staffName ?? ''
        // 作成日
        local.or32398.rirekiObj.createYmd = newValue.rirekiObj?.createDate ?? ''
        // その他のサービス
        local.or32398.rirekiObj.sonotaSvKnj = newValue.rirekiObj?.ryuiKnj ?? ''
        // ページング番号
        local.or32398.rirekiObj.pagingNo = String(newValue.rirekiObj?.currentIndex ?? 0)
        // ページング総数
        local.or32398.rirekiObj.pagingCnt = String(newValue.rirekiObj?.totalCount ?? 0)

        //データ再取得フラグ設定
        await getDailyRoutinePlanInitData()
      }
    } else if (createUpateFlg === Or32398Const.ACTION_UPDATE_1) {
      //「履歴-前へ アイコンボタン」押下
      local.or32398.operaFlg = Or32398Const.OPERA_FLG_R1
      //遷移保存確認区分
      local.or32398.moveSaveConfirmFlg = isEdit.value
        ? Or32398Const.COMFIRM_SAVE_1
        : Or32398Const.COMFIRM_SAVE_0
      //データ再取得フラグ設定
      await getDailyRoutinePlanInitData()
    } else if (createUpateFlg === Or32398Const.ACTION_UPDATE_2) {
      //「履歴-次へ アイコンボタン」押下
      local.or32398.operaFlg = Or32398Const.OPERA_FLG_R2
      //遷移保存確認区分
      local.or32398.moveSaveConfirmFlg = isEdit.value
        ? Or32398Const.COMFIRM_SAVE_1
        : Or32398Const.COMFIRM_SAVE_0
      //データ再取得フラグ設定
      await getDailyRoutinePlanInitData()
    }
  },
  { deep: true }
)

/**
 * 「マスタアイコンボタン」押下
 * GUI01056 日課計画マスタ画面閉じる場合
 */
watch(
  () => Or26846Logic.state.get(or26846_1.value.uniqueCpId),
  async (newValue) => {
    //Undefinedの時戻す
    if (isUndefined(newValue)) {
      return
    }
    // ポップアップ画面が画面閉じる場合
    if (!newValue.isOpen) {
      // ・共通情報.計画期間ID=0
      setCommonPlanPeriod({ ...local.or32398.kikanObj, sc1Id: '0' })
      // ・共通情報.履歴ID＝0
      local.or32398.rirekiId = '0'
      // ・操作区分＝0:初期化
      local.or32398.operaFlg = Or32398Const.OPERA_FLG_0
      // 画面項目を再検索する
      await getDailyRoutinePlanInitData()
    }
  },
  { deep: true }
)

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * メッセージを表示する
 *
 * @param messageId - メッセージ
 */
const showMessageBox = async (messageId: string) => {
  await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t(messageId),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.ok'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
}

/**
 * メッセージを表示する
 */
const showConfirmMessageBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * メッセージを表示する
 */
const showWarnMessageBox = async () => {
  return await openWarnDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.w-com-10006'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
}

/**
 * メッセージを表示する
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
async function openWarnDialog(state: Or21815StateType): Promise<Or21815EventType | undefined> {
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      ...state,

      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * 共通情報.計画期間を設定
 *
 * @param kikanObj - 計画期間
 */
const setCommonPlanPeriod = (kikanObj: IKikanComInfo) => {
  cmnRouteCom.setPlanPeriod({
    kikanId: kikanObj.sc1Id,
    kikanIndex: kikanObj.pagingNo ?? '',
    startYmd: kikanObj.startYmd ?? '',
    endYmd: kikanObj.endYmd ?? '',
  })
}

/**
 * 初期化処理
 */
const initOr32398 = async () => {
  local.or32398.operaFlg = Or32398Const.OPERA_FLG_0
  //データ取得
  await getDailyRoutinePlanInitData()
}

/**
 * 画面表示のデータを取得
 *
 */
const getDailyRoutinePlanInitData = async () => {
  const inputParam: DailyRoutinePlanInitSelectInEntity = {
    ...gyoumuCom.getGyoumuComSelectInEntity(
      /** 操作区分 */
      local.or32398.operaFlg ?? '',
      /** 遷移保存確認区分 */
      local.or32398.moveSaveConfirmFlg ?? '',
      /** 削除再検索用履歴ページング番号 */
      local.or32398.rirekiPagingNo ?? '',
      /** 計画期間ID */
      cmnRouteCom.getPlanPeriod()?.kikanId
    ),
    /** 履歴 */
    rirekiId:
      local.or32398.rirekiId && local.or32398.rirekiId !== '' ? local.or32398.rirekiId : '0',
    /** サービス種類 */
    defSvJigyoCd: systemCommonsStore.getSvJigyoCd ?? '',
  }
  const ret: DailyRoutinePlanInitSelectOutEntity = await ScreenRepository.select(
    'dailyRoutinePlanInitSelect',
    inputParam
  )

  //API戻り値の設定前の処理
  if (
    !(await gyoumuCom.doComLogicSelectApi(
      ret,
      await hasRegistAuth(Or32398Const.LINK_AUTH),
      showMessageBox,
      showConfirmMessageBox,
      showWarnMessageBox,
      _save,
      getDailyRoutinePlanInitData,
      local.or32398,
      {
        cnikka2DataList: ret.data.cnikka2DataList ?? [],
        initMasterObj: ret.data.initMasterObj ?? {},
      } as DailyRoutinePlanInitSelectOutData,
      setCommonPlanPeriod
    ))
  ) {
    return
  }

  //画面初期化のComponents表示フラグ設定、画面初期化のデータ設定
  setFormData(ret)

  setChildCpBinds(props.uniqueCpId, {
    [OrX0007Const.CP_ID(1)]: {
      twoWayValue: {
        planTargetPeriodId: localComponents.orX0007.planTargetPeriodId,
        PlanTargetPeriodUpdateFlg: localComponents.orX0007.PlanTargetPeriodUpdateFlg,
      },
    },
    [OrX0008Const.CP_ID(1)]: {
      twoWayValue: {
        createId: localComponents.orX0008.createId,
        createUpdateFlg: localComponents.orX0008.createUpateFlg,
      },
    },
    [OrX0009Const.CP_ID(1)]: {
      twoWayValue: {
        staffId: localComponents.orX0009.staffId,
        staffName: localComponents.orX0009.staffName,
      },
      oneWayState: {
        isDisabled: local.or32398.operaFlg === Or32398Const.OPERA_FLG_3,
      },
    },
    [OrX0010Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.orX0010.value,
        mo01343: {},
      },
      oneWayState: {
        isDisabled: local.or32398.operaFlg === Or32398Const.OPERA_FLG_3,
        isRequired: true
      },
    },
    [OrX0129Const.CP_ID(1)]: {
      twoWayValue: localComponents.orX0129,
      oneWayState: {
        operaFlg: local.or32398.operaFlg,
        pagingFlg: local.or32398.kikanObj.pagingFlg,
        kikanFlg: local.or32398.kikanFlg,
        syubetuId: local.or32398.syubetuId,
        rirekiId: local.or32398.rirekiId ?? '',
        initMasterObj: local.or32398.initMasterObj,
      },
    },
  })

  // 初期設定マスタの情報.メッセージ表示が「1:表示」、かつ、共通情報.サービス種類が「50010:介護予防支援」の場合
  if (
    local.or32398.onMountedOpenFlg === Or32398Const.ONMOUNTED_OPEN_0 &&
    local.or32398.initMasterObj.msgFlg === Or32398Const.MSG_1 &&
    systemCommonsStore.getSvJigyoCd === Or32398Const.SV_JIGYO_CD_50010
  ) {
    // 以下のメッセージを表示: i.cmn.11276
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11276'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
  }
  //選定の利用者INDEXを一時保存
  local.or32398.searchSelUserIndex =
    Or00249Logic.data.get(or00249.value.uniqueCpId)?.selectUserIndex ?? 0
}

/**
 *
 * 画面コントロール表示設定
 *
 * @param ret - 初期情報
 */
function setFormData(ret: DailyRoutinePlanInitSelectOutEntity) {
  // API戻り値
  const retData = ret.data

  // 初期設定マスタの背景色、文字色を変更
  // グレイ
  local.or32398.initMasterObj.nikkaBackcolor = local.or32398.initMasterObj.nikkaBackcolor
    ? convertDecimalToHex(Number(local.or32398.initMasterObj.nikkaBackcolor))
    : ''
  // ブラック
  local.or32398.initMasterObj.nikkaFontcolor = local.or32398.initMasterObj.nikkaFontcolor
    ? convertDecimalToHex(Number(local.or32398.initMasterObj.nikkaFontcolor))
    : ''

  //基本状況セキュリティチェック
  local.or32398.kihonJokyoFlg = retData.kihonJokyoFlg
  //基本チェックリストセキュリティチェック
  local.or32398.kihonCheckListFlg = retData.kihonCheckListFlg

  const kikanObj = local.or32398.kikanObj
  const rirekiObj = local.or32398.rirekiObj
  //種別ID
  localOneway.orX0007Oneway.kindId = local.or32398.syubetuId
  if (kikanObj.pagingFlg !== Or32398Const.PAGING_0) {
    // ページング番号
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = kikanObj.pagingNo
      ? Number(kikanObj.pagingNo)
      : 0
    // ページング総数
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = kikanObj.pagingCnt
      ? Number(kikanObj.pagingCnt)
      : 0
    //表示用「計画対象期間」情報.開始日 + " ～ " + 表示用「計画対象期間」情報.終了日
    if (kikanObj.startYmd) {
      localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriod = kikanObj.startYmd
        .concat(SPACE_WAVE)
        .concat(kikanObj.endYmd ?? '')
    }
    localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId = Number(kikanObj.sc1Id)
    // 計画期間ID
    localComponents.orX0007.planTargetPeriodId = kikanObj.sc1Id
  } else {
    //表示用「計画対象期間」情報.ページング区分が0:なしの場合
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = 0
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = 0
    localComponents.orX0007.planTargetPeriodId = '0'
  }

  //orX0008 日課計画_履歴
  localOneway.orX0008Oneway.sc1Id = kikanObj.sc1Id
  localOneway.orX0008Oneway.officeId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.orX0008Oneway.useId = systemCommonsStore.getUserSelectSelfId() ?? ''

  localOneway.orX0008Oneway.createData.createDate = rirekiObj.createYmd
  localOneway.orX0008Oneway.createData.createId = rirekiObj.rirekiId ?? '0'
  localOneway.orX0008Oneway.createData.currentIndex = rirekiObj.pagingNo
    ? Number(rirekiObj.pagingNo)
    : 0
  localOneway.orX0008Oneway.createData.staffId = rirekiObj.shokuId ?? '0'
  localOneway.orX0008Oneway.createData.staffName = rirekiObj.shokuKnj
  localOneway.orX0008Oneway.createData.totalCount = rirekiObj.pagingCnt
    ? Number(rirekiObj.pagingCnt)
    : 0
  localComponents.orX0008.createId = rirekiObj.rirekiId ?? '0'

  //orX0009 作成者
  localOneway.orX0009Oneway.createData!.createDate = rirekiObj.createYmd
  localOneway.orX0009Oneway.createData!.createId = Number(rirekiObj.rirekiId)
  localOneway.orX0009Oneway.createData!.currentIndex = Number(rirekiObj.pagingNo)
  localOneway.orX0009Oneway.createData!.staffId = Number(rirekiObj.shokuId)
  localOneway.orX0009Oneway.createData!.staffName = rirekiObj.shokuKnj
  localOneway.orX0009Oneway.createData!.totalCount = Number(rirekiObj.pagingCnt)

  // 作成者
  localComponents.orX0009.staffId = rirekiObj.shokuId
  localComponents.orX0009.staffName = rirekiObj.shokuKnj

  //orX0010 作成日
  localComponents.orX0010.value = rirekiObj.createYmd

  // 日程カレンダー
  let dataList: GamenCnikka2DataInfo[] = []
  let newNikka2Id = -1
  const cnikka2DataList = (local.or32398.cnikka2DataList ?? []) as ICnikka2DataInfo[]
  if (Array.isArray(cnikka2DataList) && cnikka2DataList.length > 0) {
    dataList = cnikka2DataList.map((item) => ({
      ...item,
      // 詳細データID
      nikka2Id: !item.nikka2Id || item.nikka2Id === '' ? String(newNikka2Id--) : item.nikka2Id,
      // 文字色:ブラック
      fontColor: item.fontColor
        ? convertDecimalToHex(Number(item.fontColor))
        : local.or32398.initMasterObj.nikkaFontcolor,
      // 背景色:グレイ
      backColor: item.backColor
        ? convertDecimalToHex(Number(item.backColor))
        : local.or32398.initMasterObj.nikkaBackcolor,
      updateKbn: UPDATE_KBN.NONE,
      zIndex: 0,
    }))
  }
  localComponents.orX0129 = {
    sonotaSvKnj: rirekiObj.sonotaSvKnj,
    cnikka2DataList: dataList,
  } as OrX0129Type

  //共通情報を設定する
  setCommonPlanPeriod(kikanObj)
  //共通情報.履歴ID ＝ 表示用「日課計画_履歴」情報.履歴ID
  local.or32398.rirekiId = rirekiObj.rirekiId
}

/**
 * 新規作成時処理
 */
async function _create() {
  // 操作区分が3:削除の場合、処理終了
  if (local.or32398.operaFlg === Or32398Const.OPERA_FLG_3) {
    return
  }

  // 期間管理フラグが1:期間管理する、かつ、表示用「計画対象期間」情報.ページング区分が0:なしの場合、メッセージを表示(i.cmn.11300)
  if (
    local.or32398.kikanFlg === Or32398Const.KIKAN_1 &&
    local.or32398.kikanObj.pagingFlg === Or32398Const.PAGING_0
  ) {
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40980'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // GUI00070 対象期間画面をポップアップで起動する。
    if (orX0007Ref.value?.onClickDialog) {
      await orX0007Ref.value.onClickDialog()
    }
    return
  } else if (!local.or32398.rirekiId || local.or32398.rirekiId === '0') {
    // 共通情報.履歴IDが空白または0の場合 i.cmn.11265
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.daily-care-plan')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  } else {
    //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
    if (
      !(await checkEditBySave())
    ) {
      return
    }
    // 下記の項目を設定する
    local.or32398.rirekiId = '0'
    local.or32398.operaFlg = Or32398Const.OPERA_FLG_1
    // 画面項目を再検索する
    await getDailyRoutinePlanInitData()
  }
}

/**
 * 複写ボタン
 */
function onCreateCopy() {
  // 下記の条件がいずれか満たす場合、
  //・操作区分 = 3:削除の場合
  //・表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    local.or32398.operaFlg === Or32398Const.OPERA_FLG_3 ||
    local.or32398.kikanObj.pagingFlg === Or32398Const.PAGING_0
  ) {
    return
  }

  // GUI01061_日課計画複写画面をポップアップで起動する。
  localOneway.or32447Oneway = {
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList as string[],
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    syubetuId: local.or32398.syubetuId ?? '',
    kikanFlg: local.or32398.kikanFlg ?? '',
    operaFlg: Or32398Const.OPERA_FLG_2,
    pagingFlg: local.or32398.kikanObj.pagingFlg ?? '',
    initMasterObj: {
      nikkaBackcolor: local.or32398.initMasterObj.nikkaBackcolor,
      nikkaFontcolor: local.or32398.initMasterObj.nikkaFontcolor,
    },
  }
  Or32447Logic.state.set({
    uniqueCpId: or32447_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01061_日課計画複写POP画面返却情報がある場合
 *
 * @param newValue - ポップアップ返却情報
 */
async function handleOr32447Confirm(newValue: Or32447Type) {
  if (newValue?.rirekiId) {
    // GUI01061 日課計画複写画面をポップアップで起動する
    const ret: DailyRoutinePlanCopyReturnSelectOutEntity = await ScreenRepository.select(
      'dailyRoutinePlanCopyReturnSelect',
      { rirekiId: newValue.rirekiId } as DailyRoutinePlanCopyReturnSelectInEntity
    )
    // API戻り値
    const retData = ret.data

    // 日程カレンダー
    let dataList: GamenCnikka2DataInfo[] = []
    const orX0129Data = OrX0129Logic.data.get(orX0129_1.value.uniqueCpId)
    // 既存データ表示用「日課計画_詳細」リストを削除する
    const dataArr =
      orX0129Data?.cnikka2DataList.map((item) => ({
        ...item,
        updateKbn: UPDATE_KBN.DELETE,
        zIndex: 0,
      })) ?? []
    dataList = dataArr

    // orX0129 表示用「日課計画_詳細」リストに戻り値.複写用「日課計画_詳細」リストを設定する
    // 新しい詳細データID
    let newId = dataArr.length === 0 ? -1 : getNewNikka2Id(dataArr)
    // 日程カレンダー
    const copyDataArr =
      retData.cnikka2DataList?.map((item) => ({
        ...item,
        // 詳細データID
        nikka2Id: String(newId--),
        // 日課計画ID
        nikka1Id: local.or32398.rirekiId,
        // 文字カラー
        fontColor: item.fontColor
          ? convertDecimalToHex(Number(item.fontColor))
          : local.or32398.initMasterObj.nikkaFontcolor,
        // 背景カラー
        backColor: item.backColor
          ? convertDecimalToHex(Number(item.backColor))
          : local.or32398.initMasterObj.nikkaBackcolor,
        // 更新回数
        modifiedCnt: '0',
        // 更新区分
        updateKbn: UPDATE_KBN.CREATE,
        zIndex: 0,
      })) ?? []
    if (copyDataArr.length > 0) {
      dataList.push(...copyDataArr)
    }

    // 一覧に設定する
    OrX0129Logic.data.set({
      uniqueCpId: orX0129_1.value.uniqueCpId,
      value: {
        sonotaSvKnj: retData.sonotaSvKnj,
        cnikka2DataList: dataList,
      },
    })
  }
}

/**
 * cnikka2DataListから新しい詳細データIDを取得
 *
 * @param dataList - cnikka2DataList
 *
 * @returns - 新しい詳細データID
 */
function getNewNikka2Id(dataList: { nikka2Id: string }[]): number {
  if (!dataList || dataList.length === 0) {
    return -1
  }

  const ids = dataList.map((item) => parseInt(item.nikka2Id, 10)).filter((id) => !isNaN(id))

  if (ids.length === 0) {
    return -1
  }
  const negativeIds = ids.filter((id) => id < 0)
  if (negativeIds.length > 0) {
    const minNegativeId = Math.min(...negativeIds)
    return minNegativeId - 1
  } else {
    return -1
  }
}

/**
 * 保存時処理(checkEdit含めて保存処理)
 * False: 処理中止（取消、いいえ）と保存権限がない時の取消操作、
 * True：処理継続（保存と保存しない）と保存権限がない時の処理継続
 */
async function checkEditBySave(): Promise<boolean> {
  //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
  if (
    !(await gyoumuCom.checkEdit(
      isEdit.value,
      await hasRegistAuth(Or32398Const.LINK_AUTH),
      showConfirmMessageBox,
      showWarnMessageBox,
      _save
    ))
  ) {
    return false
  }
  return true
}

/**
 * 保存時処理
 */
async function _save() {
  if (!(await orX0129Ref.value?.isValid())) {
    return false
  }
  //保存処理を行う。
  await gyoumuCom.doComLogicSave(
    local.or32398,
    isEdit.value,
    showMessageBox,
    getDailyRoutinePlanInitData,
    saveApiData,
    setCommonPlanPeriod
  )
  return true
}

/**
 * 保存処理を行う。
 */
async function saveApiData() {
  // 表示用「履歴」情報
  const rirekiObj = local.or32398.rirekiObj

  // 表示用「計画対象期間」情報
  const kikanObj = local.or32398.kikanObj
  // 履歴更新区分の設定
  let rireki_updateKbn = ''
  // 操作区分が3:削除の場合、保存用「履歴」情報.履歴IDが空白以外の場合
  if (local.or32398.operaFlg === Or32398Const.OPERA_FLG_3 && rirekiObj.rirekiId) {
    rireki_updateKbn = UPDATE_KBN.DELETE
  } else if (
    local.or32398.operaFlg === Or32398Const.OPERA_FLG_1 ||
    !rirekiObj.rirekiId ||
    rirekiObj.rirekiId === '0'
  ) {
    rireki_updateKbn = UPDATE_KBN.CREATE
  } else {
    rireki_updateKbn = UPDATE_KBN.UPDATE
  }

  const orX0129Data = OrX0129Logic.data.get(orX0129_1.value.uniqueCpId)
  const orX0009Data = OrX0009Logic.data.get(orX0009_1.value.uniqueCpId)
  const inputParam: DailyRoutinePlanUpdateInEntity = {
    ...gyoumuCom.getGyoumuComUpdateInEntity(
      // 操作区分
      local.or32398.operaFlg,
      // 期間管理フラグ（0:期間管理しない、1:期間管理する）
      local.or32398.kikanFlg,
      // 初期情報.種別ID
      local.or32398.syubetuId,
      // 共通情報.計画期間ID
      cmnRouteCom.getPlanPeriod()?.kikanId
    ),
    rirekiId: local.or32398.rirekiId,
    //履歴データ
    rirekiObj: {
      rirekiId: rirekiObj.rirekiId,
      shokuId: orX0009Data?.staffId ?? '',
      shokuKnj: orX0009Data?.staffName ?? '',
      createYmd: String(OrX0010Logic.data.get(orX0010_1.value.uniqueCpId)?.value),
      sonotaSvKnj: String(OrX0129Logic.data.get(orX0129_1.value.uniqueCpId)?.sonotaSvKnj),
      modifiedCnt: rirekiObj.modifiedCnt,
      updateKbn: rireki_updateKbn,
    },
    cnikka2DataList:
      orX0129Data?.cnikka2DataList
        .filter(
          (x) =>
            Number(x.nikka2Id) > 0 || (Number(x.nikka2Id) < 0 && x.updateKbn !== UPDATE_KBN.DELETE)
        )
        .map((item) => {
          return {
            nikka2Id: item.nikka2Id,
            nikka1Id: item.nikka1Id,
            dataKbn: item.dataKbn,
            youbi: item.youbi,
            startTime: item.startTime,
            endTime: item.endTime,
            naiyoKnj: item.naiyoKnj,
            fontSize: item.fontSize,
            alignment: item.alignment,
            // ブラック
            fontColor: convertHexToDecimal(
              item.fontColor ?? local.or32398.initMasterObj.nikkaFontcolor
            ),
            // グレイ
            backColor: convertHexToDecimal(
              item.backColor ?? local.or32398.initMasterObj.nikkaBackcolor
            ),
            timeKbn: item.timeKbn,
            tantoKnj: item.tantoKnj,
            zuijiFlg: item.zuijiFlg,
            wakugaiFlg: item.wakugaiFlg,
            nikkaIdList: item.nikkaIdList,
            modifiedCnt: item.modifiedCnt,
            updateKbn: item.updateKbn,
          } as ICnikka2DataSave
        }) ?? [],
    //初期設定マスタの情報
    initMasterObj: local.or32398.initMasterObj,
  }

  //期間管理フラグが0:期間管理しない場合
  if (local.or32398.kikanFlg === Or32398Const.KIKAN_0) {
    //表示用「計画対象期間」情報.計画期間IDが空白の場合
    if (kikanObj.sc1Id === '') {
      inputParam.kikanObj = {
        sc1Id: kikanObj?.sc1Id ?? '',
        startYmd: kikanObj?.startYmd ?? '',
        endYmd: kikanObj?.endYmd ?? '',
        modifiedCnt: kikanObj?.modifiedCnt ?? '',
        updateKbn: UPDATE_KBN.CREATE,
      }
    }
  }

  //画面情報の保存処理を行う。
  const ret: GyoumuComUpdateOutEntity = await ScreenRepository.update(
    'dailyRoutinePlanUpdate',
    inputParam
  )

  return ret
}

/**
 * 「印刷設定アイコンボタン」押下の処理
 */
async function onPrintClick() {
  // 操作区分 = 3:削除の場合
  if (local.or32398.operaFlg === Or32398Const.OPERA_FLG_3) {
    return
  }
  if (
    !(await checkEditBySave())
  ) {
    return
  }
  // GUI01065 印刷設定画面をポップアップで起動する。
  openPrintSetting()
}

/**
 * GUI01065 印刷設定画面をポップアップで起動する。
 */
function openPrintSetting() {
  // 共通情報.法人ID
  localOneway.or28394Oneway.houjinId = systemCommonsStore.getHoujinId ?? ''
  // 共通情報.施設ID
  localOneway.or28394Oneway.shisetuId = systemCommonsStore.getShisetuId ?? ''
  // 共通情報.事業所ID
  localOneway.or28394Oneway.svJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  // 共通情報.事業所名
  localOneway.or28394Oneway.jigyoKnj =
    systemCommonsStore.getJigyoSelectInfoList.find(
      (x) => x.svJigyoId === systemCommonsStore.getSvJigyoId
    )?.jigyoRyakuKnj ?? ''
  // 共通情報.事業所CD
  localOneway.or28394Oneway.jigyoCd = systemCommonsStore.getSvJigyoCd ?? ''
  // 共通情報.適用事業所ID（※リスト）
  localOneway.or28394Oneway.svJigyoIdList = systemCommonsStore.getSvJigyoIdList as string[]
  // TODO 共通情報.担当ケアマネID
  localOneway.or28394Oneway.tantoShokuId = '0'
  // 共通情報.職員ID ※ログイン情報
  localOneway.or28394Oneway.shokuId = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
  // 共通情報.利用者ID ※リストに格納する
  localOneway.or28394Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  // 共通情報.開始(YYYY/MM/DD)
  localOneway.or28394Oneway.startDate = systemCommonsStore.getStartDate ?? ''
  // 共通情報.終了(YYYY/MM/DD)
  localOneway.or28394Oneway.endDate = systemCommonsStore.getEndDate ?? ''
  // 共通情報.システム年月日(YYYY/MM/DD)
  localOneway.or28394Oneway.sysYmd = systemCommonsStore.getSystemDate ?? ''
  // 共通情報.履歴ID
  localOneway.or28394Oneway.rirekiId = local.or32398.rirekiId ?? ''
  // 共通情報.ユーザリスト
  localOneway.or28394Oneway.userList =
    systemCommonsStore.getUserSelectUserList()?.map((x) => {
      return {
        userId: x.selfId,
        nameKnj: x.nameSei + ' ' + x.nameKanaMei,
        userNumber: x.selfId,
        sex: String(x.gender),
      } as userList
    }) ?? []
  // 共通情報.50音（※リスト）
  localOneway.or28394Oneway.gojuuOnKana =
    systemCommonsStore.getUserSelectFilterInitials() as string[]
  // 初期設定マスタの情報
  localOneway.or28394Oneway.initMasterObj = {
    cpnFlg: local.or32398.initMasterObj.cpnFlg ?? '',
    keishoFlg: local.or32398.initMasterObj.keishoFlg ?? '',
    keishoKnj: local.or32398.initMasterObj.keishoKnj ?? '',
  } as IInitMasterInfo
  // セクション名 = "日課計画表"
  localOneway.or28394Oneway.sectionName = Or32398Const.SECTION_NAME
  // システムコード
  localOneway.or28394Oneway.systemCode = systemCommonsStore.getSystemCode ?? ''
  // 計画期間管理フラグ
  localOneway.or28394Oneway.kikanFlg = local.or32398.kikanFlg
  // 承認欄情報（0：共有する、1：帳票毎保持する）
  localOneway.or28394Oneway.shoninFlg = local.or32398.initMasterObj.shoninFlg ?? ''

  // GUI00953［印刷設定］画面
  Or28394Logic.state.set({
    uniqueCpId: or28394_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 計画書一括印刷ボタン押下時処理
 * GUI00936(Or10583) 計画書一括印刷画面
 *
 */
async function onClickBatchPrintOfPlans() {
  // 操作区分 = 3:削除の場合
  if (local.or32398.operaFlg === Or32398Const.OPERA_FLG_3) {
    return
  }
  // 画面入力データに変更がある場合
  if (
    !(await checkEditBySave())
  ) {
    return
  }
  // GUI00936(Or10583) 計画書一括印刷画面をポップアップで起動する。
  localOneway.or10583Oneway = {
    carePlanPrintAllInData: {
      // 法人ID：共通情報.法人ID
      legalPersonId: systemCommonsStore.getHoujinId ?? '',
      // 施設ID：共通情報.施設ID
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      // 計画書様式：初期設定マスタの情報.計画書様式
      carePlanStyle: local.or32398.initMasterObj.cksFlg ?? '',
      // 職員ID：共通情報.職員ID
      employeeId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      // 事業所ID：共通情報.事業所ID
      officeData: {
        officeId: systemCommonsStore.getSvJigyoId ?? '',
        officeName: '',
      } as OfficeDataType,
      // 事業所CD：共通情報.サービス種類
      jigyoTypeCd: systemCommonsStore.getSvJigyoCd ?? '',
    } as CarePlanPrintAllInData,
    setInputComponentsFlg: true,
  }
  Or10583Logic.state.set({
    uniqueCpId: or10583_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *
 * 削除処理
 *
 */
async function _delete() {
  // 下記の条件がいずれか満たす場合、
  //・操作区分 = 3:削除の場合
  //・表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    local.or32398.operaFlg === Or32398Const.OPERA_FLG_3 ||
    local.or32398.kikanObj.pagingFlg === Or32398Const.PAGING_0
  ) {
    return
  }

  //削除確認（）
  const dialogResult = await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11326', [
      OrX0010Logic.data.get(orX0010_1.value.uniqueCpId)?.value ?? '',
      t('label.daily-care-plan'),
    ]),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
  if (dialogResult?.secondBtnClickFlg === true) {
    return
  }
  // 操作区分3:削除
  local.or32398.operaFlg = Or32398Const.OPERA_FLG_3

  // 表示用「日課計画_詳細」リストを削除して、画面.「日課計画_詳細」一覧に設定する
  const orX0129Data = OrX0129Logic.data.get(orX0129_1.value.uniqueCpId)
  // orX0129一覧
  const updatedList =
    orX0129Data?.cnikka2DataList?.map((item) => ({
      ...item,
      updateKbn: UPDATE_KBN.DELETE,
    })) ?? []

  // 一覧に設定する
  OrX0129Logic.data.set({
    uniqueCpId: orX0129_1.value.uniqueCpId,
    value: {
      sonotaSvKnj: orX0129Data?.sonotaSvKnj ?? '',
      cnikka2DataList: updatedList,
    },
  })

  // 画面項目の非活性・非表示
  setChildCpBinds(props.uniqueCpId, {
    // 作成者
    [OrX0009Const.CP_ID(1)]: {
      oneWayState: {
        isDisabled: true,
      },
    },
    // 作成日
    [OrX0010Const.CP_ID(1)]: {
      oneWayState: {
        isDisabled: true,
      },
    },
    //「日課計画_詳細」
    [OrX0129Const.CP_ID(1)]: {
      oneWayState: {
        operaFlg: local.or32398.operaFlg,
      },
    },
  })
}

/**
 * 「マスタ他設定アイコンボタン」押下の処理
 * GUI01056 日課計画マスタ画面をポップアップで起動する
 */
async function _master() {
  // 画面入力データに変更がある場合
  if (
    !(await checkEditBySave())
  ) {
    return
  }

  // GUI01056 日課計画マスタ画面をポップアップで起動する(Or26846)
  Or26846Logic.state.set({
    uniqueCpId: or26846_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * パターンボタン押下時処理
 * GUI01060 日課計画パターンタイトル画面
 * GUI01059 日課計画パターン設定画面
 */
async function onClickShowPattern() {
  // 下記の条件がいずれか満たす場合、
  //・操作区分 = 3:削除の場合
  //・表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    local.or32398.operaFlg === Or32398Const.OPERA_FLG_3 ||
    local.or32398.kikanObj.pagingFlg === Or32398Const.PAGING_0
  ) {
    return
  }

  // パターン遷移先を判定する
  const ret: DailyRoutinePlanPtnTitleCntSelectOutEntity = await ScreenRepository.select(
    'dailyRoutinePlanPtnTitleCntSelect',
    {
      switchFlg: local.or32398.initMasterObj.cksFlg ?? '',
    } as DailyRoutinePlanPtnTitleCntSelectInEntity
  )

  // データを取得する
  const orX0129Data = OrX0129Logic.data.get(orX0129_1.value.uniqueCpId)
  // 「日課計画_詳細」リスト
  const cnikka2DataList = orX0129Data?.cnikka2DataList ?? []
  let kghKrkDayLePtnList: NikkaPtn[] = []
  if (Array.isArray(cnikka2DataList) && cnikka2DataList.length > 0) {
    kghKrkDayLePtnList = cnikka2DataList.map((item: GamenCnikka2DataInfo) => {
      // 曜日
      const youbis = safeSplitYoubi(item.youbi)
      return {
        // 詳細データID
        nikka2Id: '',
        nikka1Id: '',
        dataKbn: item.dataKbn,
        youbi: item.youbi,
        youbi1: youbis[0],
        youbi2: youbis[1],
        youbi3: youbis[2],
        youbi4: youbis[3],
        youbi5: youbis[4],
        youbi6: youbis[5],
        youbi7: youbis[6],
        naiyoKnj: item.naiyoKnj,
        fontSize: item.fontSize,
        alignment: item.alignment,
        fontColor: item.fontColor,
        backColor: item.backColor,
        timeKbn: item.timeKbn,
        tantoKnj: item.tantoKnj,
        zuijiFlg: item.zuijiFlg,
        wakugaiFlg: item.wakugaiFlg,
        kaishiJikan: item.startTime,
        shuuryouJikan: item.endTime,
        nikkaIdList: item.nikkaIdList,
        modifiedCnt: item.modifiedCnt,
        updateKbn: item.updateKbn,
      } satisfies NikkaPtn
    })
  }

  // GUI01059 日課計画パターン設定画面をポップアップで起動する
  localOneway.or10821Oneway = {
    // 日課計画表データリスト
    kghKrkDayLePtnList: kghKrkDayLePtnList,
    // その他のサービス
    sonotaKnj: orX0129Data?.sonotaSvKnj ?? '',
    // 初期設定マスタの情報
    initMasterObj: local.or32398.initMasterObj,
  }
  Or10821Logic.state.set({
    uniqueCpId: or10821_1.value.uniqueCpId,
    state: {
      isOpen: true,
      tabId:
        ret.data?.ptnTitleCnt === '0' ? Or32398Const.TAB_ID_GUI01060 : Or32398Const.TAB_ID_GUI01059,
      kghKrkDayLePtnList: kghKrkDayLePtnList,
      sonotaKnj: orX0129Data?.sonotaSvKnj ?? '',
      initMasterObj: local.or32398.initMasterObj,
    },
  })
}
/**
 * 曜日を分けて取得
 *
 * @param youbi - 曜日
 *
 * @returns - 曜日1～曜日7
 */
function safeSplitYoubi(youbi: string | null | undefined): string[] {
  const safeValue = youbi?.slice(0, 7) ?? ''
  const result = [...safeValue]
  while (result.length < 7) {
    result.push('0')
  }
  return result
}

/**
 * GUI01060 日課計画パターンタイトル画面の返回値を監視
 * GUI01059 日課計画パターン設定画面の返回値を監視
 *
 * @param returnValue - 画面の返回値
 */
function handleOr10821ReturnData(returnValue: Or10821Type) {
  let sonotaKnj = ''
  let cnikka2DataList: GamenCnikka2DataInfo[] = []
  // 既存データを削除する
  const orX0129Data = OrX0129Logic.data.get(orX0129_1.value.uniqueCpId)
  // 「日課計画_詳細」リスト
  cnikka2DataList = orX0129Data?.cnikka2DataList ?? []
  // 上書の場合
  if (returnValue.operationKbn === Or32398Const.OPERATION_KBN_1) {
    // 行クリア
    if (Array.isArray(cnikka2DataList) && cnikka2DataList.length > 0) {
      cnikka2DataList = cnikka2DataList.map(
        (item) =>
          ({
            ...item,
            updateKbn: UPDATE_KBN.DELETE,
            zIndex: 0,
          }) satisfies GamenCnikka2DataInfo
      )
    }
    sonotaKnj = returnValue.sonotaKnj ?? ''
  } else {
    // 追加の場合:画面.その他のサービスが空の場合、その他のサービス=ポップアップ返却情報.その他のサービス
    sonotaKnj = orX0129Data?.sonotaSvKnj ?? ''
    if (sonotaKnj === '') {
      sonotaKnj = returnValue.sonotaKnj ?? ''
    }
  }

  // 表示用「日課計画_詳細」リストにポップアップ返却情報.日課計画パターン設定詳細リストを設定する
  const newList = returnValue.kghKrkDayLePtnList ?? []
  if (Array.isArray(newList) && newList.length > 0) {
    // 新しい詳細データID
    let newId = cnikka2DataList.length === 0 ? -1 : getNewNikka2Id(cnikka2DataList)
    const dataArr = newList.map((item: NikkaPtn) => {
      return {
        // 詳細データID
        nikka2Id: String(newId--),
        nikka1Id: local.or32398.rirekiId,
        dataKbn: item.dataKbn,
        youbi: item.youbi,
        startTime: item.kaishiJikan,
        endTime: item.shuuryouJikan,
        naiyoKnj: item.naiyoKnj,
        fontSize: item.fontSize,
        alignment: item.alignment,
        fontColor: item.fontColor,
        backColor: item.backColor,
        timeKbn: item.timeKbn,
        tantoKnj: item.tantoKnj,
        zuijiFlg: item.zuijiFlg,
        wakugaiFlg: item.wakugaiFlg,
        nikkaIdList: item.nikkaIdList,
        modifiedCnt: '0',
        updateKbn: UPDATE_KBN.CREATE,
        zIndex: 0,
      } satisfies GamenCnikka2DataInfo
    })
    cnikka2DataList.push(...dataArr)
  }

  // 一覧に設定する
  OrX0129Logic.data.set({
    uniqueCpId: orX0129_1.value.uniqueCpId,
    value: {
      sonotaSvKnj: sonotaKnj,
      cnikka2DataList: cnikka2DataList,
    },
  })
}

/**
 * ログボタン押下時処理
 *
 */
function onClickLog() {
  // TODO 共通処理 e-文書法対象機能の電子ファイル保存設定を取得する
}

/**
 * ダイアログ表示
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,

      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})

// 作成情報表示制御
// 期間管理フラグが1:期間管理する、かつ、表示用「計画対象期間」情報.ページング区分が0:なしの場合、非表示
// 以外の場合、表示
const creatShowFlg = computed(() => {
  if (
    local.or32398.kikanFlg === Or32398Const.KIKAN_1 &&
    local.or32398.kikanObj.pagingFlg === Or32398Const.PAGING_0
  ) {
    return false
  }
  return true
})
</script>

<template>
  <c-v-sheet class="view">
    <!-- Or11871：有機体：画面メニューエリア -->
    <div class="sticky-header">
      <g-base-or11871 v-bind="or11871">
          <template #createItems>
            <c-v-list-item
              :title="t('btn.copy')"
              @click="onCreateCopy()"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.care-plan2-copy-btn')"
              />
            </c-v-list-item>
          </template>
          <template #customButtons>
            <!--「パターン」-->
            <base-at-button
                class="action-button"
                @click="onClickShowPattern()"
              >
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :text="$t('tooltip.week-table-pattern-btn')"
                ></c-v-tooltip>
                {{ t('btn.pattern') }}
            </base-at-button
              >
          </template>

          <template #optionPrintItems>
            <!--「印刷設定」-->
            <c-v-list-item
              :title="t('btn.print-configuration')"
              @click="onPrintClick()"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.care-plan2-print-setting-btn')"
              />
            </c-v-list-item>
            <!--「計画書一括印刷」-->
            <c-v-list-item
              :title="t('btn.batch-printing-of-plans')"
              @click="onClickBatchPrintOfPlans()"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.care-plan2-batch-print-btn')"
              />
            </c-v-list-item>
          </template>
          <template #optionMenuItems>
            <!--「ログ」-->
            <c-v-list-item
              v-show="systemCommonsStore.getEBunshoKbn === Or32398Const.E_BUNSHO_KBN_1"
              :title="t('btn.log')"
              @click="onClickLog()"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.care-plan2-show-log-btn')"
              />
            </c-v-list-item>
          </template>
      </g-base-or11871>
    </div>
    <!-- アクションエリア -->
    <c-v-row
      no-gutters
      class="content-area pl-6"
    >
      <c-v-col
        cols="2"
        class="hidden-scroll h-100 pt-4"
      >
        <!-- （利用者基本）利用者選択の表示 -->
        <g-base-or00248 v-bind="or00248_1" />
      </c-v-col>
      <c-v-col class="hidden-scroll h-100 pl-6" style="padding-bottom:156px;">
          <c-v-row
            no-gutters
            class="view-row pb-6"
          >
            <c-v-col
              cols="auto">
              <!-- 事業所 -->
              <g-base-or-41179 v-bind="or41179_1" width="203"/>
            </c-v-col>
            <c-v-col
              v-show="local.or32398.kikanFlg === Or32398Const.KIKAN_1"
              cols="auto"
            >
              <!-- 計画対象期間 -->
              <g-custom-orX0007
                ref="orX0007Ref"
                v-bind="orX0007_1"
                :oneway-model-value="localOneway.orX0007Oneway"
                :unique-cp-id="orX0007_1.uniqueCpId"
                :parent-method="checkEditBySave"
              />
            </c-v-col>
            <c-v-col
              v-show="creatShowFlg"
              cols="auto"
            >
              <!-- 作成日 -->
              <g-custom-orX0010
                v-bind="orX0010_1"
                :oneway-model-value="localOneway.orX0010Oneway"
                :unique-cp-id="orX0010_1.uniqueCpId"
              />
            </c-v-col>
            <c-v-col
              v-show="creatShowFlg"
              cols="auto"
            >
              <!-- 作成者 -->
              <g-custom-orX0009
                v-bind="orX0009_1"
                :oneway-model-value="localOneway.orX0009Oneway"
                :unique-cp-id="orX0009_1.uniqueCpId"
              />
            </c-v-col>
            <c-v-col
              v-show="creatShowFlg"
              cols="auto"
            >
              <!-- 履歴 -->
              <g-custom-orX0008
                v-bind="orX0008_1"
                :oneway-model-value="localOneway.orX0008Oneway"
                :unique-cp-id="orX0008_1.uniqueCpId"
                :parent-method="checkEditBySave"
              />
            </c-v-col>
          </c-v-row>
          <g-custom-or-x-0129
            ref="orX0129Ref"
            v-bind="orX0129_1"
            :unique-cp-id="orX0129_1.uniqueCpId"
          />
      </c-v-col>
    </c-v-row>
  </c-v-sheet>

  <!-- データ変更確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or21815 v-bind="or21815_1" />

  <!--GUI01056 日課計画マスタ画面-->
  <g-custom-or-26846
    v-if="showDialogOr26846"
    v-bind="or26846_1"
    :oneway-model-value="localOneway.or26846Oneway"
    :unique-cp-id="or26846_1.uniqueCpId"
  />
  <!--GUI00936 計画書一括印刷画面-->
  <g-custom-or-10583
    v-if="showDialogOr10583"
    v-bind="or10583_1"
    :oneway-model-value="localOneway.or10583Oneway"
    :unique-cp-id="or10583_1.uniqueCpId"
  />
  <!--GUI01059 日課計画パターン設定画面-->
  <g-custom-or-10821
    v-if="showDialogOr10821"
    v-bind="or10821_1"
    :oneway-model-value="localOneway.or10821Oneway"
    :unique-cp-id="or10821_1.uniqueCpId"
    @click="handleOr10821ReturnData"
  />

  <!--GUI01061_日課計画複写POP画面-->
  <g-custom-or-32447
    v-if="showDialogOr32447"
    v-bind="or32447_1"
    :oneway-model-value="localOneway.or32447Oneway"
    :unique-cp-id="or32447_1.uniqueCpId"
    @update:model-value="handleOr32447Confirm"
  />
  <!-- GUI01065_印刷設定画面 -->
  <g-custom-or-28394
    v-if="showDialogOr28394"
    v-bind="or28394_1"
    :oneway-model-value="localOneway.or28394Oneway"
    :unique-cp-id="or28394_1.uniqueCpId"
  />
</template>

<style scoped lang="scss">
$margin-organism: 24px; // オーガニズム間のマージン
$margin-common: 48px; // 一般的なマージン
.view {
  display: flex;
  flex-direction: column;
  // 背景色：テーマの背景色を使用
  background-color: rgb(var(--v-theme-background)) !important;
  // 幅：内容で広く
  min-height: max-content !important;
  // 画面トップの固定ヘッダー
  .sticky-header {
    position: sticky; // スクロール時に固定
    top: $margin-common; // トップからの距離
    z-index: 999; // 他の要素よりも上に表示
    background: rgb(var(--v-theme-background)); // テーマの背景色
    width: 100%; // 全幅
  }
}

.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}

.view-row {
  align-items: baseline;
  gap: 24px;
}

.v-btn {
  color: rgb(118, 118, 118) !important;
  background-color: transparent !important;
  border: 1px solid rgb(118, 118, 118) !important;
}
.action-button {
  background-color: #fff !important;
  width: 78px !important;
  padding: 0px !important;
  min-width: auto !important;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
:deep(.v-input__control) {
  background-color: #fff;
}
</style>
