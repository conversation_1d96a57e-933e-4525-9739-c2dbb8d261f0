<script setup lang="ts">
/**
 * OrX0050:カレンダー(0,1,2,···，24)（画面コンポーネント）
 * カレンダー
 *
 * @description
 * 日程カレンダー
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0049Const } from '../OrX0049/OrX0049.constants'
import { OrX0050Const } from './OrX0050.constants'
import type {
  OrX0050CalendarConfig,
  OrX0050InternalCalendarConfig,
  OrX0050CalendarEvent,
  OrX0050SegmentedEvent,
  OrX0050InternalCalendarEvent,
  OrX0050CalendarDblClickResult,
} from './OrX0050.type'
const { t } = useI18n()
/** 入力パラメータ */
interface Props {
  calendarConfig: OrX0050CalendarConfig
}
// 入力パラメータ
const props = defineProps<Props>()
// エミット
const emit = defineEmits(['doubleClickEvent', 'doubleClick', 'update:events'])
// 双方向バインディングの初期値
const defaultModelValue: Props = {
  calendarConfig: {
    events: [],
    cellHeight: OrX0050Const.EVENT_BLOCK_DEFAULT.CELL_HEIGHT,
    zoom: OrX0050Const.EVENT_BLOCK_DEFAULT.ZOOM,
  },
}

const local = reactive({
  calendar: {
    ...defaultModelValue,
    ...props,
  },
  realCalendar: {
    ...defaultModelValue.calendarConfig,
  } as OrX0050InternalCalendarConfig,
})
// 基準日：月曜日
const monday = getCurrentWeekMonday()
// 基準日：日曜日
const sunday = getCurrentWeekSunday()
// デフォルトのz-index
let nextZIndex = OrX0049Const.EVENT_BLOCK_DEFAULT.MAX_Z_INDEX
let rightResizeEvents: OrX0050CalendarEvent[] = []
let lastMouseDownTime = 0
let dragTimer: ReturnType<typeof setTimeout> | null = null
/**
 * 入力された日程に対して、日をまたぐかどうかを判断し、再度処理
 */
const splitEvents = computed<OrX0050SegmentedEvent[]>(() => {
  const segments: OrX0050SegmentedEvent[] = []

  local.realCalendar.events.forEach((event) => {
    let currentStart = new Date(event.start)
    const endDate = new Date(event.end)

    // 日をまたぐイベント処理
    while (currentStart < endDate) {
      const dayEnd = new Date(currentStart)
      dayEnd.setHours(23, 59, 59, 999) // 当日の終了時間

      const segmentEnd = endDate < dayEnd ? endDate : dayEnd
      const dayIndex =
        (currentStart.getDay() + OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SATURDAY) % 7 // 週の並びをマッチング
      const displStart = new Date(event.start)
      displStart.setHours(displStart.getHours() + OrX0050Const.EVENT_BLOCK_DEFAULT.HOURS_FOR_4)
      const displEnd = new Date(event.end)
      displEnd.setHours(displEnd.getHours() + OrX0050Const.EVENT_BLOCK_DEFAULT.HOURS_FOR_4)
      segments.push({
        ...event,
        segmentStart: formatDate(currentStart, false),
        segmentEnd: formatDate(segmentEnd, false),
        dayIndex: dayIndex,
        customId: `${event.id}-${dayIndex}`, // 一意のID生成
        orgParentId: event.id,
        parentId: event.parentId,
      })

      currentStart = new Date(segmentEnd)
      // 開始日を翌日の 0 時設定
      currentStart.setSeconds(currentStart.getSeconds() + 1)
    }
  })
  return segments
})

/**
 * 2 時間を目盛として、タイムライン生成
 */
const timeSlots = computed(() => {
  const slots = [t('label.time-2')]
  if (
    local.calendar.calendarConfig.granularity !== undefined ||
    local.calendar.calendarConfig.granularity !== OrX0050Const.EVENT_BLOCK_DEFAULT.GRANULARITY
  ) {
    for (
      let h = 0;
      h < local.realCalendar.endHour;
      h += local.calendar.calendarConfig.granularity ?? OrX0050Const.EVENT_BLOCK_DEFAULT.GRANULARITY
    ) {
      slots.push(
        `${h.toString().padStart(2, OrX0050Const.STRING_ZERO)}${OrX0050Const.FULL_HOUR_SUFFIX}`
      )
    }
  } else {
    for (
      let h = 0;
      h < local.realCalendar.endHour;
      h += (local.calendar.calendarConfig.granularity ??
        OrX0050Const.EVENT_BLOCK_DEFAULT.GRANULARITY) as number
    ) {
      slots.push(
        `${h.toString().padStart(2, OrX0050Const.STRING_ZERO)}${OrX0050Const.FULL_HOUR_SUFFIX}`
      )
    }
  }

  return slots
})

/**
 * 1 時間を目盛として、時間枠生成
 */
const hourSlots = computed(() => {
  const slots = []
  for (let h = local.realCalendar.startHour; h < local.realCalendar.endHour; h++) {
    slots.push(
      `${h.toString().padStart(2, OrX0050Const.STRING_ZERO)}${OrX0050Const.FULL_HOUR_SUFFIX}`
    )
  }
  return slots
})

/**
 * 日程タイトル生成
 */
const visibleDays = computed(() => {
  const days = []
  for (let i = 0; i < 7; i++) {
    const date = new Date(monday)
    date.setDate(monday.getDate() + i)

    days.push({
      date: date.getDate(),
      weekday: [
        t('label.day-of-week-short-monday'),
        t('label.day-of-week-short-tuesday'),
        t('label.day-of-week-short-wednesday'),
        t('label.day-of-week-short-thursday'),
        t('label.day-of-week-short-friday'),
        t('label.day-of-week-short-saturday'),
        t('label.day-of-week-short-sunday'),
      ][i],
      fullDate: date,
    })
  }
  return days
})
/**
 * ドラッグし、引き伸ばしてから再レンダリング
 *
 * @param event - 日程
 */
const eventStyle = (event: OrX0050SegmentedEvent) => {
  const start = new Date(event.segmentStart)
  const end = new Date(event.segmentEnd)

  // 日程とタイトルの間の距離
  const startMinutes =
    start.getHours() * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HOUR + start.getMinutes()
  const endMinutes =
    end.getHours() * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HOUR + end.getMinutes()
  // 日程のスタイル設定
  const positionStyle = {
    zIndex: event.zIndex ?? 0,
    top: `${startMinutes * (local.realCalendar.cellHeight / OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HOUR)}px`,
    height: `${(endMinutes - startMinutes) * (local.realCalendar.cellHeight / OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HOUR)}px`,
    left: `${event.dayIndex * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH}%`,
    width: `${OrX0050Const.EVENT_BLOCK_DEFAULT.WIDTH}%`,
    background: event.bgColor ?? OrX0050Const.EVENT_BLOCK_DEFAULT.BACKGROUND_COLOR,
    fontSize: event.fontSize ?? OrX0050Const.EVENT_BLOCK_DEFAULT.FONT_SIZE,
    color: event.fontColor ?? OrX0050Const.EVENT_BLOCK_DEFAULT.FONT_COLOR,
    textAlign: event.align ?? OrX0050Const.EVENT_BLOCK_DEFAULT.ALIGN,
    borderLeft: `4px solid ${event.fontColor} !important`,
  }
  event.left = event.dayIndex * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH

  // 日程要素が移動中の透明度設定
  if (dragState.value.isDragging && event.id === dragState.value.currentEvent.id) {
    return {
      ...positionStyle,
      opacity: OrX0050Const.EVENT_BLOCK_DEFAULT.OPACITY,
      zIndex: OrX0050Const.EVENT_BLOCK_DEFAULT.MAX_Z_INDEX,
    }
  }
  return positionStyle as Record<string, string | number>
}

// 日程移動状態管理
const dragState = ref<{
  isDragging: boolean
  originalX: number
  originalY: number
  originalTime: number
  originalDate: Date
  currentEvent: OrX0050SegmentedEvent
  containerRect?: DOMRect | null | { height: number; width: number }
  deltaX: number
}>({
  isDragging: false,
  originalX: 0,
  originalY: 0,
  originalTime: 0,
  originalDate: new Date(),
  currentEvent: {
    segmentStart: '',
    segmentEnd: '',
    dayIndex: 0,
    orgParentId: 0,
    id: 0,
    title: '',
    start: '',
    end: '',
    bgColor: '',
    week: 1,
    customId: '',
    memo: '',
  },
  containerRect: {
    height: 0,
    width: 0,
  },
  deltaX: 0,
})

// 日程引き伸ばし状態管理
const resizeState = ref({
  isResizing: false,
  direction: '' as 'top' | 'bottom' | 'left' | 'right',
  originalY: 0,
  originalX: 0,
  originalTime: 0,
  currentEvent: null as OrX0050SegmentedEvent | null,
  srcWidth: 0,
  mouseEvent: {} as MouseEvent,
  clientWidth: 0,
})

// 日程要素定義
const calendarContainer = ref<Element | null>()
const gridBody = ref<Element | null>()

/**
 * 日程コンポーネント初期化
 */
onMounted(() => {
  init()
})

/**
 * 日程コンポーネント初期化
 */
function init() {
  calendarContainer.value = document.querySelector('.calendar-container')
  gridBody.value = document.querySelector('.grid-body')
  const eventArray: OrX0050InternalCalendarEvent[] = []
  local.calendar.calendarConfig.events.forEach((item) => {
    const monday = getCurrentWeekMonday()
    // 月曜日にweekを追加した後の対応する日付
    monday.setDate(monday.getDate() + item.week - 1)
    const currentDateStr = formatDateWithOutTime(monday)
    const start = new Date(`${currentDateStr} ${item.start}`)
    const end = new Date(`${currentDateStr} ${item.end}`)
    if (end <= start) {
      end.setDate(end.getDate() + 1)
    }
    if (item.period !== undefined) {
      // 期間 を 3 として渡すと、最終的にレンダリングされる日程ブロックの長さは 3 になるはずなので、-1
      eventArray.push({
        ...item,
        start: formatDate(start, false),
        end: formatDate(end, false),
        width: item.period * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH,
        defaultStart: start,
        defaultEnd: end,
        customId: item.id + '',
      })
    } else {
      eventArray.push({
        ...item,
        start: formatDate(start, false),
        end: formatDate(end, false),
        defaultStart: start,
        defaultEnd: end,
        customId: item.id + '',
      })
    }
  })
  local.realCalendar = {
    ...local.calendar.calendarConfig,
    events: eventArray,
    startHour: 0,
    endHour: OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_END_HOUR,
  }
}

/**
 * 日程要素移動イベント
 *
 * @param event - 日程
 *
 * @param e - マウス移動イベント
 */
const startDrag = (event: OrX0050SegmentedEvent, e: MouseEvent) => {
  dragState.value.isDragging = false
  e.preventDefault()
  const rect = calendarContainer.value?.getBoundingClientRect()
  const startDate = new Date(event.start)
  dragState.value = {
    isDragging: true,
    originalX: e.clientX,
    originalY: e.clientY,
    originalTime: startDate.getTime(),
    originalDate: startDate,
    currentEvent: event,
    containerRect: rect,
    deltaX: 0,
  }

  window.addEventListener('mousemove', handleDrag)
  window.addEventListener('mouseup', () => {
    void stopDrag()
  })
}

/**
 * 移動中リアルタイムで新日付
 *
 * @param e - マウスポインタ位置座標
 */
const handleDrag = (e: { clientX: number; clientY: number }) => {
  let deltaX = e.clientX - dragState.value.originalX
  const orgStartDate = new Date(dragState.value.currentEvent.start)
  if (orgStartDate.getDay() === OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SUNDAY && deltaX > 0) {
    deltaX = 0
  }
  const deltaY = e.clientY - dragState.value.originalY
  dragState.value.deltaX = deltaX
  // 新開始時間計算（境界制限を含む）
  const newStartDate = calculateNewDate(deltaX, deltaY)
  const duration =
    new Date(dragState.value.currentEvent?.end).getTime() -
    new Date(dragState.value.currentEvent?.start).getTime()
  // 新終了時間計算（元の継続時間を維持）
  const newEndDate = new Date(newStartDate.getTime() + duration)

  // 日程時間制限処理
  const clampedDates = clampTimeRange(newStartDate, newEndDate)
  const currentEvent = dragState.value.currentEvent
  if (currentEvent.periodStart !== undefined && currentEvent.periodEnd !== undefined) {
    const width = Math.round(
      (dragState.value.currentEvent.width ?? OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH) /
        OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH
    )
    const periodEndDate = new Date(clampedDates.end)
    periodEndDate.setDate(periodEndDate.getDate() + (width - 1))
    if (periodEndDate.getDay() === OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MONDAY) {
      const beforeChangeClampedDatesEnd = new Date(clampedDates.end.getTime())
      beforeChangeClampedDatesEnd.setHours(0, 0, 0, 0)
      clampedDates.end.setDate(clampedDates.end.getDate())
      clampedDates.end.setHours(0, 0, 0, 0)
      clampedDates.start = new Date(beforeChangeClampedDatesEnd.getTime() - duration)
    }
  }
  // 日程左移オーバーフロー防止
  const leftDate = new Date(visibleDays.value[0].fullDate.getTime())
  leftDate.setDate(leftDate.getDate() - 1)
  leftDate.setHours(23, 59, 59, 999)
  if (clampedDates.start <= leftDate) {
    clampedDates.start = new Date(dragState.value.currentEvent?.start)
    clampedDates.end = new Date(dragState.value.currentEvent?.end)
  }

  const startMinutes = clampedDates.start.getMinutes()
  const endMinutes = clampedDates.end.getMinutes()
  // 分が0か30か判断
  if (startMinutes < OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR) {
    clampedDates.start.setMinutes(0)
    clampedDates.start.setSeconds(0)
    clampedDates.start.setMilliseconds(0)
  } else if (startMinutes > OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR) {
    clampedDates.start.setMinutes(OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR)
    clampedDates.start.setSeconds(0)
    clampedDates.start.setMilliseconds(0)
  }
  if (endMinutes < OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR) {
    clampedDates.end.setMinutes(0)
    clampedDates.end.setSeconds(0)
    clampedDates.end.setMilliseconds(0)
  }
  if (endMinutes > OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR) {
    clampedDates.end.setMinutes(OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR)
    clampedDates.end.setSeconds(0)
    clampedDates.end.setMilliseconds(0)
  }
  const sundayEnd = new Date(sunday)
  sundayEnd.setDate(sundayEnd.getDate() + 1)
  sundayEnd.setHours(0, 0, 0, 0)
  if (clampedDates.start > sundayEnd && clampedDates.end > sundayEnd) {
    return
  }
  updateEventPosition(clampedDates.start, clampedDates.end)
}

/**
 * 日程カレンダーの日付範囲超えているか計算
 *
 * @param startDate - 開始日付
 *
 * @param endDate - 終了日付
 */
const clampTimeRange = (startDate: Date, endDate: Date) => {
  const clampedStart = new Date(startDate)
  const clampedEnd = new Date(endDate)

  // 開始日付制限
  if (clampedStart.getHours() < local.realCalendar.startHour) {
    clampedStart.setHours(local.realCalendar.startHour, 0, 0, 0)
    clampedEnd.setTime(clampedStart.getTime() + (endDate.getTime() - startDate.getTime()))
  }

  // 終了日付制限
  if (clampedEnd.getHours() >= local.realCalendar.endHour) {
    clampedEnd.setHours(local.realCalendar.endHour, 0, 0, 0)
    clampedStart.setTime(clampedEnd.getTime() - (endDate.getTime() - startDate.getTime()))
  }
  return { start: clampedStart, end: clampedEnd }
}
/**
 * 移動後新日付計算
 *
 * @param deltaX - マウス X 軸相対距離
 *
 * @param deltaY - マウス Y 軸相対距離
 */
const calculateNewDate = (deltaX: number, deltaY: number) => {
  const { originalDate, containerRect } = dragState.value
  const newDate = new Date(originalDate)

  // 30分単位として新日付を計算
  const pixelsPerHour =
    containerRect!.height / (local.realCalendar.endHour - local.realCalendar.startHour)
  const minuteDelta =
    Math.round(
      ((deltaY / pixelsPerHour) * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HOUR) /
        OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR
    ) * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR
  newDate.setMinutes(originalDate.getMinutes() + minuteDelta)
  if (originalDate.getDate() === newDate.getDate()) {
    const dayWidth = containerRect!.width / OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WEEK
    const dayDelta = Math.round(deltaX / dayWidth)
    newDate.setDate(originalDate.getDate() + dayDelta)
  }

  return newDate
}

/**
 * 移動後の新日付割り当て
 *
 * @param newStart - 移動後の開始日付
 *
 * @param newEnd - 移動後の終了日付
 */
const updateEventPosition = (newStart: Date, newEnd: Date) => {
  const originalEvent = local.realCalendar.events.find(
    (e) => e.id === dragState.value.currentEvent?.orgParentId
  )
  let leftWidth = 0
  //  週の並びをマッチング
  const dayIndex =
    (newStart.getDay() + OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SATURDAY) %
    OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WEEK
  const left = dayIndex * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH
  if (
    dragState.value.currentEvent.width !== undefined &&
    dragState.value.currentEvent.width !== null
  ) {
    leftWidth = Math.round(left + dragState.value.currentEvent.width)
  } else {
    leftWidth = Math.round(
      dragState.value.currentEvent.left ?? 0 + OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH
    )
  }
  if (leftWidth <= OrX0050Const.EVENT_BLOCK_DEFAULT.MAX_WIDTH) {
    if (originalEvent) {
      originalEvent.start = formatDate(newStart, false)
      originalEvent.end = formatDate(newEnd, false)
      if (originalEvent.periodStart !== undefined && originalEvent.periodEnd !== undefined) {
        const width = Math.round(
          (dragState.value.currentEvent.width ?? OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH) /
            OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH
        )
        originalEvent.periodStart = new Date(originalEvent.start)
        const periodEndDate = new Date(originalEvent.end)
        periodEndDate.setDate(periodEndDate.getDate() + (width - 1))
        originalEvent.periodEnd = periodEndDate
        const tmpPeriodEndDate = new Date(periodEndDate.getTime())
        tmpPeriodEndDate.setHours(0, 0, 0, 1)
      }
    }
  }
}

// 移動イベント停止
const stopDrag = () => {
  dragState.value.isDragging = false
  const originalEvent = local.realCalendar.events.find(
    (e) => e.id === dragState.value.currentEvent?.orgParentId
  )
  if (originalEvent) {
    const events = local.realCalendar.events.filter(
      (e) =>
        e.parentId === dragState.value.currentEvent?.parentId &&
        e.id !== dragState.value.currentEvent?.orgParentId
    )
    const originalStart = new Date(originalEvent.start)
    const originalEnd = new Date(originalEvent.end)
    const startDuration = originalStart.getTime() - originalEvent.defaultStart!.getTime()
    const endDuration = originalEnd.getTime() - originalEvent.defaultEnd!.getTime()
    const dayWidth =
      dragState.value.containerRect!.width / OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WEEK
    const dayDelta = Math.round(dragState.value.deltaX / dayWidth)
    const deleteIds: number[] = []
    events.forEach((e) => {
      const start = new Date(e.start)
      start.setTime(start.getTime() + startDuration)
      const end = new Date(e.end)
      end.setTime(end.getTime() + endDuration)
      if (dayDelta !== 0) {
        start.setDate(e.defaultStart!.getDate())
        end.setDate(e.defaultEnd!.getDate())
      }
      if (start >= monday) {
        e.start = formatDate(start, false)
        e.end = formatDate(end, false)
      } else {
        deleteIds.push(e.id)
      }
      e.defaultStart = start
      e.defaultEnd = end
    })
    local.realCalendar.events = local.realCalendar.events.filter(
      (item) => !deleteIds.includes(item.id)
    )
    originalEvent.defaultStart = new Date(originalStart.getTime())
    originalEvent.defaultEnd = new Date(originalEnd.getTime())
  }
  // 前日対応の周间再调整日
  local.realCalendar.events.forEach((item) => {
    const oldStartDate = new Date(item.start)
    const oldEndDate = new Date(item.end)
    const sundayEnd = new Date(sunday)
    sundayEnd.setDate(sundayEnd.getDate() + 1)
    sundayEnd.setHours(0, 0, 0, 0)
    if (oldStartDate >= sundayEnd && oldEndDate >= sundayEnd) {
      const newStartDate = new Date(
        `${formatDateWithOutTime(monday)} ${formatDate(oldStartDate, true)}`
      )
      const newEndDate = new Date(
        `${formatDateWithOutTime(monday)} ${formatDate(oldEndDate, true)}`
      )
      if (oldStartDate.getDay() > 0) {
        newStartDate.setDate(newStartDate.getDate() + oldStartDate.getDay() - 1)
      } else {
        newStartDate.setDate(
          newStartDate.getDate() + OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SATURDAY
        )
      }
      if (oldEndDate.getDay() > 0) {
        newEndDate.setDate(newEndDate.getDate() + oldEndDate.getDay() - 1)
      } else {
        newEndDate.setDate(newEndDate.getDate() + OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SATURDAY)
      }
      item.start = formatDate(newStartDate, false)
      item.end = formatDate(newEndDate, false)
    }
  })
  local.realCalendar.events = removeDuplicates(local.realCalendar.events)
  const zIndexItem = local.realCalendar.events.find(
    (e) => e.id === dragState.value.currentEvent?.orgParentId
  )
  if (zIndexItem) {
    nextZIndex++
    zIndexItem.zIndex = nextZIndex
  }
  window.removeEventListener('mousemove', handleDrag)
  window.removeEventListener('mouseup', () => {
    void stopDrag()
  })
  const updatedEvents = getCalendarData()
  emit('update:events', updatedEvents)
}

/**
 * 重複除外
 *
 * @param arr - アレイ
 */
function removeDuplicates(arr: OrX0050InternalCalendarEvent[]): OrX0050InternalCalendarEvent[] {
  const seenKeys = new Set<string>()

  return arr.filter((obj) => {
    const key = `${obj.start}|${obj.end}|${obj.parentId}`

    // この組み合わせを見たことがない場合は保持し、そうでない場合はフィルタリング
    if (!seenKeys.has(key)) {
      seenKeys.add(key)
      return true
    }
    return false
  })
}

// 日程ダブルクリックイベント
const showEventGui = (event: OrX0050SegmentedEvent) => {
  if (dragTimer) {
    clearTimeout(dragTimer)
    dragTimer = null
  }
  // おやID に基づいて元の日程情報取得
  const orgEventArray = local.realCalendar.events.find((e) => e.id === event.orgParentId)
  emit('doubleClickEvent', orgEventArray)
}

// カレンダーエリアダブルクリックイベント
const handleGridBodyDoubleClick = (day: { weekday: string; fullDate: Date }, hour: string) => {
  const result: OrX0050CalendarDblClickResult = {
    startHour: hour,
    endHour: hour,
    week: { jp: day.weekday, num: day.fullDate.getDay() },
  }
  emit('doubleClick', result)
}
/**
 * 日付書式化 yyyy-MM-dd HH:mm:ss OR HH:mm
 *
 * @param date - Date
 *
 * @param skipYm - 年月表示識別子(true: 表示しない, false: 表示する)
 */
function formatDate(date: Date, skipYm: boolean) {
  if (date === undefined || date === null) return ''
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, OrX0050Const.STRING_ZERO)
  const day = String(date.getDate()).padStart(2, OrX0050Const.STRING_ZERO)
  const hours = String(date.getHours()).padStart(2, OrX0050Const.STRING_ZERO)
  const minutes = String(date.getMinutes()).padStart(2, OrX0050Const.STRING_ZERO)
  const seconds = String(date.getSeconds()).padStart(2, OrX0050Const.STRING_ZERO)
  if (!skipYm) {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } else {
    return `${hours}:${minutes}`
  }
}
/**
 * 日付書式化 yyyy-MM-dd
 *
 * @param date - Date
 */
function formatDateWithOutTime(date: Date) {
  if (date === undefined || date === null) return ''
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, OrX0050Const.STRING_ZERO)
  const day = String(date.getDate()).padStart(2, OrX0050Const.STRING_ZERO)
  return `${year}-${month}-${day}`
}

/**
 * マウス進入日程可移動エリア検出
 *
 * @param event - 日程
 *
 * @param e - マウスイベント
 */
const handleMouseMoveOnEvent = (event: OrX0050SegmentedEvent, e: MouseEvent) => {
  const rect = (e.target as HTMLElement).getBoundingClientRect()
  const mouseY = e.clientY - rect.top

  event.isResizingTop = mouseY < 10
  event.isResizingBottom = mouseY > rect.height - 10
}

/**
 * 日程移動、調整イベント処理分離
 *
 * @param event - 日程
 *
 * @param e - マウスイベント
 */
const handleEventMouseDown = (event: OrX0050SegmentedEvent, e: MouseEvent) => {
  //遅延タイマーを設定する
  if (event.isResizing) {
    e.stopPropagation()
    startResize(event, resizeState.value.direction, e)
    return
  }
  if (dragTimer) {
    clearTimeout(dragTimer)
    dragTimer = null
  }
  const now = Date.now()
  if (now - lastMouseDownTime < 300) {
    lastMouseDownTime = 0 // リセットは後続操作への影響を防止するため
    return // ダブルクリック時にドラッグをトリガーしない
  }
  lastMouseDownTime = now
  dragTimer = setTimeout(() => {
    startDrag(event, e)
    dragTimer = null
  }, 300)
}

/**
 * 日程開始調整イベント
 *
 * @param event - 日程
 *
 * @param direction - 調整方向
 *
 * @param e - マウスイベント
 */
const startResize = (
  event: OrX0050SegmentedEvent,
  direction: 'top' | 'bottom' | 'left' | 'right',
  e: MouseEvent
) => {
  resizeState.value = {
    isResizing: true,
    direction,
    originalY: e.clientY,
    originalX: e.clientX,
    originalTime:
      direction === 'top'
        ? new Date(event.segmentStart).getTime()
        : new Date(event.segmentEnd).getTime(),
    currentEvent: event,
    srcWidth: event.width ?? OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH,
    mouseEvent: e,
    clientWidth: (e.target as HTMLElement).parentElement?.clientWidth ?? 0,
  }
  const rect = calendarContainer.value?.getBoundingClientRect()
  const startDate = new Date(event.start)
  dragState.value = {
    isDragging: false,
    originalX: e.clientX,
    originalY: e.clientY,
    originalTime: startDate.getTime(),
    originalDate: startDate,
    currentEvent: event,
    containerRect: rect,
    deltaX: 0,
  }

  window.addEventListener('mousemove', (e: MouseEvent) => {
    void handleResize(e)
  })
  window.addEventListener('mouseup', () => {
    void stopResize()
  })
}

/**
 * 日程時間調整
 *
 * @param e - マウスイベント
 */
const handleResize = async (e: MouseEvent) => {
  if (!resizeState.value.isResizing || !resizeState.value.currentEvent) return
  e.preventDefault()
  const rect = calendarContainer.value?.getBoundingClientRect()
  const gridBodyRect = gridBody.value?.getBoundingClientRect()
  const pixelsPerHour = rect!.height / (local.realCalendar.endHour - local.realCalendar.startHour)
  const pixelsPerDay = gridBodyRect!.width * 0.1428
  const deltaY = e.clientY - resizeState.value.originalY
  const deltaX = e.clientX - resizeState.value.originalX
  const hourDelta = deltaY / pixelsPerHour
  // 30 分ステップ幅
  const minuteDelta =
    Math.round(
      (hourDelta * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HOUR) /
        OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR
    ) * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR

  const originalEvent = local.realCalendar.events.find(
    (e) => e.id === resizeState.value.currentEvent?.orgParentId
  )

  if (originalEvent) {
    const newDate = new Date(resizeState.value.originalTime)
    newDate.setMinutes(newDate.getMinutes() + minuteDelta)
    const clampedDate = clampTime(newDate)
    if (['top', 'bottom'].includes(resizeState.value.direction)) {
      if (resizeState.value.direction === 'top') {
        // 開始時間終了時間遅くならない
        if (clampedDate.getTime() < new Date(originalEvent.end).getTime()) {
          originalEvent.start = formatDate(clampedDate, false)
          if (originalEvent.periodStart !== undefined && originalEvent.periodEnd !== undefined) {
            // 日程上移オーバーフロー防止
            const leftDate = new Date(visibleDays.value[0].fullDate.getTime())
            leftDate.setDate(leftDate.getDate())
            leftDate.setHours(0, 0, 0, 0)
            if (clampedDate <= leftDate) {
              originalEvent.start = formatDate(leftDate, false)
            }
          }
        }
      } else if ('bottom' === resizeState.value.direction) {
        // 終了時間開始時間早くならない
        if (clampedDate.getTime() > new Date(originalEvent.start).getTime()) {
          if (originalEvent.periodStart !== undefined && originalEvent.periodEnd !== undefined) {
            // 期間終了時間
            const periodEndDate = new Date(originalEvent.periodEnd)
            const days = getDaysBetween(new Date(originalEvent.start), clampedDate)
            const width = Math.round(
              (originalEvent.width ?? OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH) /
                OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH -
                1
            )
            periodEndDate.setDate(originalEvent.periodStart.getDate() + width + days)
            periodEndDate.setHours(
              clampedDate.getHours(),
              clampedDate.getMinutes(),
              clampedDate.getSeconds(),
              clampedDate.getMilliseconds()
            )
            if (periodEndDate.getDay() === OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MONDAY) {
              periodEndDate.setHours(0, 0, 0, 0)
              clampedDate.setHours(0, 0, 0, 0)
              originalEvent.end = formatDate(clampedDate, false)
            } else {
              originalEvent.end = formatDate(clampedDate, false)
            }
            originalEvent.periodEnd = periodEndDate
          } else {
            originalEvent.end = formatDate(clampedDate, false)
          }
        }
      }
    } else {
      if (resizeState.value.direction === 'right') {
        await nextTick()
        const events = local.realCalendar.events.filter(
          (e) => e.parentId === resizeState.value.currentEvent?.parentId
        )
        // events内のstart属性（フォーマット済み日付型）に基づき、ソートを実施願います
        events.sort((a, b) => {
          return new Date(a.start).getTime() - new Date(b.start).getTime()
        })
        const parentWidth = resizeState.value.clientWidth
        const left = resizeState.value.currentEvent.left ?? 0
        const newWidth = parentWidth + deltaX
        const widthDays = Math.ceil(newWidth / pixelsPerDay)
        const finalWidth = widthDays * OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WIDTH
        const leftWidth = left + finalWidth
        if (leftWidth <= OrX0050Const.EVENT_BLOCK_DEFAULT.MAX_WIDTH) {
          // widthDaysは自身を含むため、-1となります。
          const currentDays = widthDays - 1
          let id = 0
          // ローカル環境の最大IDを取得後、必ずインクリメントしてください
          local.realCalendar.events.forEach((e) => {
            id = Math.max(e.id, id)
          })
          const cpyEvents: OrX0050InternalCalendarEvent[] = []
          for (let i = 1; i <= currentDays; i++) {
            const cpyEvent = { ...originalEvent }
            // スケジュールブロックコピー
            const startDate = new Date(cpyEvent.start)
            startDate.setDate(startDate.getDate() + i)
            // resizeState内のstart + currentDayがeventsに存在するか判定し、存在する場合は現在のループをスキップして次回処理に移行してください
            const chksArray = events.find((e) => e.start === formatDate(startDate, false))
            if (!chksArray) {
              id++
              const endDate = new Date(cpyEvent.end)
              endDate.setDate(endDate.getDate() + i)
              cpyEvent.start = formatDate(startDate, false)
              cpyEvent.end = formatDate(endDate, false)
              cpyEvent.id = id
              cpyEvent.defaultStart = startDate
              cpyEvent.defaultEnd = endDate
              const dayIndex =
                (new Date(cpyEvent.start).getDay() +
                  OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SATURDAY) %
                OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_WEEK // 週の並びをマッチング
              cpyEvent.customId = `${cpyEvent.id}-${dayIndex}`
              cpyEvents.push(cpyEvent as OrX0050InternalCalendarEvent)
            }
          }
          rightResizeEvents = cpyEvents
        }
      }
    }
  }
}
/**
 * 2 つ日付間隔日数計算
 *
 * @param date1 - 日付1
 *
 * @param date2 - 日付2
 */
function getDaysBetween(date1: Date, date2: Date) {
  if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
    throw new Error('Invalid date')
  }
  const d1 = new Date(date1.getFullYear(), date1.getMonth(), date1.getDate())
  const d2 = new Date(date2.getFullYear(), date2.getMonth(), date2.getDate())
  // 時間差計算日数に変換
  const timeDiff = d2.getTime() - d1.getTime()
  const dayDiff = Math.abs(timeDiff / OrX0050Const.EVENT_BLOCK_DEFAULT.MILS_PER_DAY)
  return Math.floor(dayDiff)
}

/**
 * 日程カレンダー範囲超過計算
 *
 * @param date - 日付
 */
const clampTime = (date: Date): Date => {
  const clamped = new Date(date)
  // 30 分粒度に合わせる
  let minutes = clamped.getMinutes()
  minutes =
    Math.round(minutes / OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR) *
    OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_MINUTES_HALF_HOUR
  clamped.setMinutes(minutes, 0, 0)
  const hours = clamped.getHours()
  if (hours < local.realCalendar.startHour) {
    clamped.setHours(local.realCalendar.startHour, 0, 0, 0)
  } else if (hours >= local.realCalendar.endHour) {
    clamped.setHours(local.realCalendar.endHour - 1, 59, 59, 999)
  }

  return clamped
}

/**
 * 日程時間調整終了処理
 */
const stopResize = () => {
  resizeState.value.isResizing = false
  if (rightResizeEvents.length !== 0) {
    for (const resizeEvent of rightResizeEvents) {
      local.realCalendar.events.push(resizeEvent as OrX0050InternalCalendarEvent)
    }
  }
  rightResizeEvents = []
  const zIndexItem = local.realCalendar.events.find(
    (e) => e.id === dragState.value.currentEvent?.orgParentId
  )
  if (zIndexItem) {
    nextZIndex++
    zIndexItem.zIndex = nextZIndex
  }
  window.removeEventListener('mousemove', (e: MouseEvent) => {
    void handleResize(e)
  })
  window.removeEventListener('mouseup', () => {
    void stopResize()
  })
  void stopDrag()
}
/**
 * カレンダー内の日程データ取得
 */
function getCalendarData() {
  const datas = JSON.parse(
    JSON.stringify(local.realCalendar.events)
  ) as OrX0050InternalCalendarEvent[]
  const rtnDatas = [] as OrX0050CalendarEvent[]
  for (const data of datas) {
    if (data.periodStart && data.periodEnd) {
      data.period = getDaysBetween(new Date(data.periodStart), new Date(data.periodEnd)) + 1
    } else {
      data.period = 1
    }
    // week
    if (data.start) {
      data.week = new Date(data.start).getDay()
    }
    // 日付部分を除去し、時・分・秒のみを残す
    if (data.start && data.end) {
      data.start = data.start.split(' ')[1]
      data.end = data.end.split(' ')[1]
    }
    rtnDatas.push({
      bgColor: data.bgColor,
      start: data.start,
      end: data.end,
      parentId: data.parentId,
      id: data.id,
      period: data.period,
      week: data.week,
      align: data.align,
      timeAlign: data.timeAlign,
      fontSize: data.fontSize,
      fontColor: data.fontColor,
      title: data.title,
      memo: data.memo,
    })
  }
  return rtnDatas
}

/**
 * 現在の日付に対応する月曜日を取得
 */
function getCurrentWeekMonday() {
  const today = new Date()
  const dayOfWeek = today.getDay()
  const diff =
    dayOfWeek === OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SUNDAY
      ? OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SATURDAY
      : dayOfWeek - 1
  const monday = new Date(today)
  monday.setDate(today.getDate() - diff)
  monday.setHours(0, 0, 0, 0)
  return monday
}
/**
 * 現在の日付に対応する日曜日を取得
 */
function getCurrentWeekSunday() {
  const today = new Date()
  const dayOfWeek = today.getDay()
  const diff =
    dayOfWeek === OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SUNDAY
      ? OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SATURDAY
      : dayOfWeek - 1
  const sunday = new Date(today)
  sunday.setDate(today.getDate() + (OrX0050Const.EVENT_BLOCK_DEFAULT.NUMBER_SATURDAY - diff))
  sunday.setHours(23, 59, 59, 999)
  return sunday
}
/**
 * カレンダーの再編集
 */
async function reRenderCalendar() {
  await nextTick()
  window.removeEventListener('mousemove', handleDrag)
  window.removeEventListener('mouseup', () => {
    void stopDrag()
  })
  window.removeEventListener('mousemove', (e: MouseEvent) => {
    void handleResize(e)
  })
  window.removeEventListener('mouseup', () => {
    void stopResize()
  })
  init()
}

/**
 * 入力するズーム比率監視
 */
watch(
  () => local.calendar.calendarConfig?.zoom,
  (newValue) => {
    local.realCalendar.zoom = newValue
  },
  { deep: true }
)
defineExpose({
  getCalendarData,
  reRenderCalendar,
})
</script>
<template>
  <div
    class="calendar-container"
    :style="{
      '--custom-calendar-cell-height': `${local.realCalendar.cellHeight}px`,
      '--custom-calendar-zoom': `${local.realCalendar.zoom}`,
      '--custom-calendar-granularity': `${local.realCalendar.granularity ?? OrX0050Const.EVENT_BLOCK_DEFAULT.GRANULARITY}`,
    }"
  >
    <!-- タイムライン -->
    <div class="time-axis">
      <div
        v-for="time in timeSlots"
        :key="time"
        class="time-slot"
      >
        {{ time }}
      </div>
    </div>

    <!-- 周期グリッド -->
    <div class="week-grid">
      <!-- 曜日 -->
      <div class="grid-header">
        <div
          v-for="day in visibleDays"
          :key="day.date"
          class="day-header"
        >
          <div class="weekday">{{ day.weekday }}</div>
        </div>
      </div>

      <!-- グリッド -->
      <div class="grid-body">
        <div
          v-for="day in visibleDays"
          :key="day.date"
          class="day-column"
        >
          <div
            v-for="hour in hourSlots"
            :key="hour"
            class="time-cell"
            @dblclick="
              !local.calendar.calendarConfig.readonly && handleGridBodyDoubleClick(day, hour)
            "
          ></div>
        </div>

        <div
          v-for="event in splitEvents"
          :key="event.customId"
          class="calendar-event"
          :style="eventStyle(event)"
          @dblclick.stop="!local.calendar.calendarConfig.readonly && showEventGui(event)"
          @mousedown="
            !local.calendar.calendarConfig.readonly && handleEventMouseDown(event, $event)
          "
          @mousemove="
            !local.calendar.calendarConfig.readonly && handleMouseMoveOnEvent(event, $event)
          "
        >
          <!-- 調整に利用できるエリア -->
          <div
            v-if="!local.calendar.calendarConfig.readonly"
            class="resize-handle-calendar top"
            @mousedown.stop="startResize(event, 'top', $event)"
          ></div>
          <div
            v-if="!local.calendar.calendarConfig.readonly"
            class="resize-handle-calendar bottom"
            @mousedown.stop="startResize(event, 'bottom', $event)"
          ></div>
          <div
            v-if="!local.calendar.calendarConfig.readonly"
            class="resize-handle-calendar right"
            @mousedown.stop="startResize(event, 'right', $event)"
          ></div>
          <div style="overflow: unset !important">
            <!-- 日程内容表示エリア -->
            <div class="d-flex ga-2 calendar-event-text-wrap">
              <div
                v-if="!event.disableIcon"
                class="d-flex ga-2 justify-center"
                :style="`padding-top: ${(parseInt(event.fontSize ?? OrX0050Const.EVENT_BLOCK_DEFAULT.FONT_SIZE) * 1.5 - 14) / 2}px`"
              >
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9.31273 10.0154L10.0154 9.31273L7.4974 6.79473V3.66406H6.4974V7.1999L9.31273 10.0154ZM6.99856 13.3307C6.12256 13.3307 5.29917 13.1645 4.5284 12.8321C3.75762 12.4996 3.08717 12.0485 2.51706 11.4786C1.94695 10.9087 1.49556 10.2385 1.1629 9.46806C0.83034 8.69762 0.664062 7.87445 0.664062 6.99856C0.664062 6.12256 0.830285 5.29917 1.16273 4.5284C1.49517 3.75762 1.94634 3.08717 2.51623 2.51706C3.08612 1.94695 3.75628 1.49556 4.52673 1.1629C5.29717 0.83034 6.12034 0.664062 6.99623 0.664062C7.87223 0.664062 8.69562 0.830284 9.4664 1.16273C10.2372 1.49517 10.9076 1.94634 11.4777 2.51623C12.0478 3.08612 12.4992 3.75628 12.8319 4.52673C13.1645 5.29717 13.3307 6.12034 13.3307 6.99623C13.3307 7.87223 13.1645 8.69562 12.8321 9.4664C12.4996 10.2372 12.0485 10.9076 11.4786 11.4777C10.9087 12.0478 10.2385 12.4992 9.46806 12.8319C8.69762 13.1645 7.87445 13.3307 6.99856 13.3307ZM6.9974 12.3307C8.47517 12.3307 9.73351 11.8113 10.7724 10.7724C11.8113 9.73351 12.3307 8.47517 12.3307 6.9974C12.3307 5.51962 11.8113 4.26128 10.7724 3.2224C9.73351 2.18351 8.47517 1.66406 6.9974 1.66406C5.51962 1.66406 4.26128 2.18351 3.2224 3.2224C2.18351 4.26128 1.66406 5.51962 1.66406 6.9974C1.66406 8.47517 2.18351 9.73351 3.2224 10.7724C4.26128 11.8113 5.51962 12.3307 6.9974 12.3307Z"
                    :fill="event.fontColor"
                  />
                </svg>
              </div>
              <div style="display: flex; flex-direction: column">
                <template
                  v-if="
                    event.timeAlign === undefined ||
                    event.timeAlign === '' ||
                    event.timeAlign === '1'
                  "
                >
                  <div
                    class="calendar-event-text-wrap"
                    style="order: 1"
                  >
                    {{
                      formatDate(new Date(event.start), true) +
                      '～' +
                      formatDate(new Date(event.end), true)
                    }}
                  </div>
                </template>
                <template v-else-if="event.timeAlign === '2'">
                  <div
                    class="calendar-event-text-wrap"
                    style="order: 1; width: 100%; height: 10px"
                  ></div>
                  <div
                    class="calendar-event-text-wrap"
                    style="order: 2"
                  >
                    {{
                      formatDate(new Date(event.start), true) +
                      '～' +
                      formatDate(new Date(event.end), true)
                    }}
                  </div>
                </template>
                <template v-else>
                  <div
                    class="calendar-event-text-wrap"
                    style="order: 1; width: 100%; height: 10px"
                  ></div>
                </template>
                <div
                  class="d-flex ga-2 calendar-event-text-wrap"
                  style="order: 1"
                >
                  {{ event.title }}
                </div>
                <div
                  v-if="event.insuranceBussiness"
                  class="d-flex ga-2 calendar-event-text-wrap align-top"
                  style="order: 1"
                >
                  <div
                    v-if="!event.disableIcon"
                    class="d-flex ga-2 justify-center"
                    :style="`padding-left: 1px;padding-top: ${(parseInt(event.fontSize ?? OrX0050Const.EVENT_BLOCK_DEFAULT.FONT_SIZE) * 1.5 - 14) / 2}px`"
                  >
                    <svg
                      width="12"
                      height="14"
                      viewBox="0 0 12 14"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M5.9974 7.0026C6.36406 7.0026 6.67795 6.87205 6.93906 6.61094C7.20017 6.34983 7.33073 6.03594 7.33073 5.66927C7.33073 5.3026 7.20017 4.98872 6.93906 4.7276C6.67795 4.46649 6.36406 4.33594 5.9974 4.33594C5.63073 4.33594 5.31684 4.46649 5.05573 4.7276C4.79462 4.98872 4.66406 5.3026 4.66406 5.66927C4.66406 6.03594 4.79462 6.34983 5.05573 6.61094C5.31684 6.87205 5.63073 7.0026 5.9974 7.0026ZM5.9974 11.9026C7.35295 10.6582 8.35851 9.5276 9.01406 8.51094C9.66962 7.49427 9.9974 6.59149 9.9974 5.8026C9.9974 4.59149 9.61129 3.59983 8.83906 2.8276C8.06684 2.05538 7.11962 1.66927 5.9974 1.66927C4.87517 1.66927 3.92795 2.05538 3.15573 2.8276C2.38351 3.59983 1.9974 4.59149 1.9974 5.8026C1.9974 6.59149 2.32517 7.49427 2.98073 8.51094C3.63628 9.5276 4.64184 10.6582 5.9974 11.9026ZM5.9974 13.6693C4.20851 12.147 2.8724 10.7332 1.98906 9.4276C1.10573 8.12205 0.664062 6.91372 0.664062 5.8026C0.664062 4.13594 1.20017 2.80816 2.2724 1.81927C3.34462 0.830382 4.58628 0.335938 5.9974 0.335938C7.40851 0.335938 8.65017 0.830382 9.7224 1.81927C10.7946 2.80816 11.3307 4.13594 11.3307 5.8026C11.3307 6.91372 10.8891 8.12205 10.0057 9.4276C9.1224 10.7332 7.78628 12.147 5.9974 13.6693Z"
                        :fill="event.fontColor"
                      />
                    </svg>
                  </div>
                  <div
                    :style="`width: 100%;text-align: ${event.align ?? OrX0050Const.EVENT_BLOCK_DEFAULT.ALIGN}`"
                    class="calendar-event-text-wrap d-flex ga-2 align-center"
                  >
                    {{ event.insuranceBussiness }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <c-v-tooltip
            activator="parent"
            location="bottom"
            content-class="tooltip-warp"
            :width="110"
            open-delay="200"
          >
            <span>{{
              formatDate(new Date(event.start), true) + '～' + formatDate(new Date(event.end), true)
            }}</span>
            <br />
            <div v-if="event.title">
              <span>{{ event.title }}</span>
              <br />
            </div>
            <div v-if="event.insuranceBussiness">
              <span>{{ event.insuranceBussiness }}</span>
            </div>
          </c-v-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
/* カレンダーコンテナ */
.calendar-container {
  width: 900px;
  display: flex;
  position: relative;
  border: 1px solid #ddd;
  border-right: none;
  border-bottom: none;
  zoom: var(--custom-calendar-zoom, 1);
}

.calendar-container * {
  box-sizing: border-box;
}
/* タイムライン */
.time-axis {
  width: 60px;
  outline: 1px solid #d6d6d6;
  background: #f2f2f2;
  flex-shrink: 0;
  position: relative;
  height: calc(var(--custom-calendar-cell-height, 36px) * 24 - 1px + 36px);
}
/* 周期グリッド */
.even-grid {
  width: 35px;
  padding-top: 40px;
  border-right: 1px solid #ddd;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
}
/* タイムライン */
.even-slot {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ddd;
  writing-mode: vertical-rl;
}

.even-slot:last-child {
  border-bottom: none;
}
/* タイムライン */
.time-slot {
  height: calc(var(--custom-calendar-cell-height, 36px) * var(--custom-calendar-granularity));
  /* 2 時間の高さ */
  font-size: 0.9em;
  color: #767676;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-bottom: 1px solid #d6d6d6;
}
.time-slot:first-child {
  height: 36px;
}
.time-slot:last-child {
  border-bottom: none;
}
/* 疑似要素のボーダー削除 */
.time-slot::after {
  content: none;
}
/* 曜日周期グリッド */
.week-grid {
  flex: 1;
  min-width: 0;
}
/* 曜日タイトル */
.grid-header {
  display: flex;
  height: 36px;
  border-bottom: 1px solid #d6d6d6;
  background: #fff;
  box-sizing: border-box;
  border-left: 1px solid #d6d6d6;
  border-right: 1px solid #d6d6d6;
}

.day-header {
  flex: 1;
  text-align: center;
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.day-header + .day-header {
  border-left: 1px solid #d6d6d6;
}

.grid-body {
  position: relative;
  display: flex;
  will-change: transform;
  cursor: pointer;
  border-right: 1px solid #d6d6d6;
  border-left: 1px solid #d6d6d6;
  background: #fff;
}

.day-column {
  flex: 1;
  position: relative;
  min-height: 100%;
}
.day-column + .day-column {
  border-left: 1px solid #d6d6d6;
}
/* タイムセル */
.time-cell {
  height: var(--custom-calendar-cell-height, 36px);
  border-bottom: 1px solid #ddd;
  box-sizing: border-box;
  position: relative;
}

.time-cell:nth-child(2n)::before {
  content: '';
  position: absolute;
  left: -70px;
  width: 70px;
  height: 1px;
  background: transparent;
}

/* 日程 */
.calendar-event {
  position: absolute;
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
  border-radius: 4px;
  padding: 4px 8px;
  overflow: hidden;
  font-size: 14px;
  cursor: move;
  box-sizing: border-box;
  transition: box-shadow 0.2s;
  touch-action: none;
  user-select: none;
  transition: opacity 0.2s;
  text-align: left;
  word-wrap: break-word;
}
/* 日程移動際のスタイル */
.calendar-event.dragging {
  cursor: grabbing;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.resize-handle-calendar {
  position: absolute;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.2s;
}

.resize-handle-calendar:hover {
  opacity: 1;
  background: rgba(33, 150, 243, 0.3);
}

.resize-handle-calendar.top,
.resize-handle-calendar.bottom {
  left: 0;
  right: 0;
  height: 10px;
}

.resize-handle-calendar.left,
.resize-handle-calendar.right {
  top: 0;
  bottom: 0;
  width: 10px;
}
/* 上に調整するハンドル */
.resize-handle-calendar.top {
  top: 0;
  cursor: ns-resize;
}
/* 下に調整するハンドル */
.resize-handle-calendar.bottom {
  bottom: 0;
  cursor: ns-resize;
}
/* 右に調整するハンドル */
.resize-handle-calendar.right {
  right: 0;
  cursor: ew-resize;
}
/* 土曜日 */
.day-header:nth-child(6) {
  background: #eff8ff;
  color: #0b66ed;
}

/* 日曜日 */
.day-header:nth-child(7) {
  background: #fff0f1;
  color: #d90214;
}
.calendar-event-text-wrap {
  word-wrap: break-word;
  word-break: break-all;
}
</style>
