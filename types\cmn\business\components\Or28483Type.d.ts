/**
 * Or28483:1日のスケジュールモーダル
 * GUI01068:日のスケジュール
 *
 * @description
 * 単方向バインドのデータ構造
 *
 * <AUTHOR> PHAM TIEN THANH
 */
export interface Or28483OnewayType {
  /**
   * 基本情報ID
   */
  basicInfoId: string
  /**
   * 画面ID
   */
  screenId: string

  /**
   * 編集許可フラグ
   */
  isAllowEdit?: boolean
}

/**
 * 双方向バインドModelValue
 */
export interface Or28483Type {
  /**
   * 日のスケジュールモーダル
   */
  getDailyScheduleList: dailyScheduleItem[]
}

/**
 * dailyScheduleItem
 */
export interface dailyScheduleItem {
  /**
   * カウンター
   */
  issuingId?: number | null
  /**
   * 選択状態
   */
  isSelected?: boolean
  /**
   * 表示順
   */
  seq?: number | null
  /**
   * 基本情報ID
   */
  khinfoId?: number
  /**
   * チェックボックス
   */
  checkbox?: boolean
  /**
   * 時間
   */
  jikanKnj?: string
  /**
   * 本人
   */
  honinKnj?: string
  /**
   * 家族
   */
  kaigoshaKnj?: string
  /**
   * 更新回数
   */
  modifiedCnt?: string
}
