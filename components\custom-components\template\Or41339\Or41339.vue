<script setup lang="ts">
/**
 * GUI00865:アセスメント(インターライ)画面テンプレート
 *
 * @description
 * アセスメント(インターライ)画面テンプレート
 *
 * <AUTHOR> PHAM HO HAI DANG
 */

import { onMounted, reactive, ref, watch, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { Or00393Logic } from '../../organisms/Or00393/Or00393.logic'
import { Or00398Logic } from '../../organisms/Or00398/Or00398.logic'
import { Or00393Const } from '../../organisms/Or00393/Or00393.constants'
import { Or00398Const } from '../../organisms/Or00398/Or00398.constants'
import { Or28207Const } from '../../organisms/Or28207/Or28207.constants'
import { Or28207Logic } from '../../organisms/Or28207/Or28207.logic'
import { Or30063Const } from '../../organisms/Or30063/Or30063.constants'
import { Or30063Logic } from '../../organisms/Or30063/Or30063.logic'
import { Or41339Const } from './Or41339.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useSetupChildProps, useScreenTwoWayBind } from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or33769Const } from '~/components/custom-components/organisms/Or33769/Or33769.constants'
import { OrX0010Const } from '~/components/custom-components/organisms/OrX0010/OrX0010.constants'
import { OrX0010Logic } from '~/components/custom-components/organisms/OrX0010/OrX0010.logic'
import type { OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'

import type {
  Or41339OnewayType,
  PlanPeriodInfo,
  RirekiJoho,
  TransmitParam,
} from '~/types/cmn/business/components/Or41339Type'
import type { Or10279OneWayType } from '~/types/cmn/business/components/Or10279Type'
import { Or10279Const } from '~/components/custom-components/organisms/Or10279/Or10279.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import type { OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import { OrX0007Logic } from '~/components/custom-components/organisms/OrX0007/OrX0007.logic'
import type { OrX0008OnewayType } from '~/types/cmn/business/components/OrX0008Type'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { Or33769OnewayType } from '~/types/cmn/business/components/Or33769Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { ParamComfirm } from '~/components/custom-components/template/Or41339/Or41339.type'
import { OrX0008Const } from '~/components/custom-components/organisms/OrX0008/OrX0008.constants'
import { OrX0008Logic } from '~/components/custom-components/organisms/OrX0008/OrX0008.logic'
import { OrX0009Logic } from '~/components/custom-components/organisms/OrX0009/OrX0009.logic'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import type { PlanTargetPeriodDataType } from '~/types/cmn/PlanTargetPeriodDataType'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or51767Const } from '~/components/custom-components/organisms/Or51767/Or51767.constants'
import { Or51767Logic } from '~/components/custom-components/organisms/Or51767/Or51767.logic'
import { useJigyoList } from '~/utils/useJigyoList'
import { useUserListInfo } from '~/utils/useUserListInfo'
import type {
  kentouyousiCommonInfoSelectInEntity,
  kentouyousiCommonInfoSelectOutEntity,
} from '~/repositories/cmn/entities/kentouyousiCommonInfoSelectEntity'
import type { Mo00043Type, Mo00043OnewayType } from '~/types/business/components/Mo00043Type'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Or28207OnewayType, Or28207Type } from '~/types/cmn/business/components/Or28207Type'
import type { Or30063OnewayType, Or30063Type } from '~/types/cmn/business/components/Or30063Type'
import type { kentouyousiInfoUpdateOutEntity } from '~/repositories/cmn/entities/kentouyousiInfoUpdateEntity'
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or41339OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const orHeadLine = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const or10279 = ref({ uniqueCpId: Or10279Const.CP_ID(1) })
const orX0007 = ref({ uniqueCpId: '' })
const orX0008 = ref({ uniqueCpId: '' })
const orX0009 = ref({ uniqueCpId: '' })
const orX0010 = ref({ uniqueCpId: '' })
const gui00070 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const or33769 = ref({ uniqueCpId: '' })
const or51767 = ref({ uniqueCpId: '' })
const or28207 = ref({ uniqueCpId: '' })
const or30063 = ref({ uniqueCpId: '' })

// ナビゲーション制御領域のいずれかの編集フラグがON
// const isEdit = computed(() => {
//   return (
//     screenStore.getCpNavControl(orX0009.value.uniqueCpId) ||
//     screenStore.getCpNavControl(orX0010.value.uniqueCpId) ||
//     local.isSoftDelete
//   )
// })
const isEdit = ref<boolean>(false)
// 計画期間
const isUsePlanPeriod = ref<boolean>(true)
// お気に入りに該当機能
const favorite = ref<boolean>(false)

// 削除フラグ
const deleteFlag = ref<boolean>(false)

const or41179 = ref({ uniqueCpId: '' })
// ローカルTwoway
const local = reactive({
  // 作成日
  createDate: {
    value: systemCommonsStore.getSystemDate,
  } as Mo00020Type,
  // フラグ
  flag: {
    // 期間管理
    periodManage: '1',
  },
  isSoftDelete: false,
  updateKbn: '',
  /**
   * タブ
   */
  mo00043: {
    id: '',
  } as Mo00043Type,
  // 期間管理フラグ
  kikanKanriFlg: '',
  // 利用者ID
  userId: '',
  // 基準日
  kijunbiYmd: '',
  // 履歴情報
  historyInfo: {} as RirekiJoho,
  // 計画期間情報
  planPeriodInfo: {} as PlanPeriodInfo,
  addBtnState: false,
})
// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()
const { jigyoListWatch } = useJigyoList()

// ローカルOneway
const localOneway = reactive({
  //事業所
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 1, totalCount: 0 } } as OrX0007OnewayType,
  // 履歴
  orX0008Oneway: {
    screenID: 'GUI00874',
    officeId: '',
    sc1Id: '',
    useId: '',
    plan1Id: '',
    mode: '',
  } as OrX0008OnewayType,
  //作成者
  orX0009Oneway: {
    createData: {
      createDate: '',
    } as PlanCreateDataType,
  } as OrX0009OnewayType,
  //作成日
  orX0010Oneway: {} as OrX0010OnewayType,
  Or10279Model: {
    staffId: systemCommonsStore.getStaffId,
    baseDate: systemCommonsStore.getSystemDate,
    officeId: systemCommonsStore.getStaffId,
  } as Or10279OneWayType,
  or33769OnewayModel: {
    syubetsuId: '',
    shisetuId: '',
    userId: '',
    planPeriodFlg: '',
  } as Or33769OnewayType,
  /**
   * タブ
   */
  mo00043OnewayType: {
    tabItems: [],
    minWidth: '58px',
  } as Mo00043OnewayType,
})

// e-文書法対象機能の電子ファイル保存設定区分

/**************************************************
 * ライフサイクルフック
 **************************************************/
// 画面状態管理用操作変数
// const screenStore = useScreenStore()

// ダイアログ表示フラグ
// const showDialogOr33769 = computed(() => {
//   // Or00100のダイアログ開閉状態
//   return Or33769Logic.state.get(or33769.value.uniqueCpId)?.isOpen ?? false
// })

// ダイアログ表示フラグ
const showDialogOr51767 = computed(() => {
  // Or51767のダイアログ開閉状態
  return Or51767Logic.state.get(or51767.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr28207 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or28207Logic.state.get(or28207.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr30063 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or30063Logic.state.get(or30063.value.uniqueCpId)?.isOpen ?? false
})

const or28207OnewayModel: Or28207OnewayType = {
  syubetsuId: '',
  shisetuId: '',
  userId: '',
  kikanFlg: '',
}

const or30063OnewayModel: Or30063OnewayType = {
  syubetsuId: '',
  shisetuId: '',
  userId: '',
  kikanFlg: '',
}

/**
 *  ボタン押下時の処理
 *
 * @param kikanFlg - 期間管理フラグ
 */
function or28207OnClick(kikanFlg: string) {
  or28207OnewayModel.kikanFlg = kikanFlg
  // Or00100のダイアログ開閉状態を更新する
  Or28207Logic.state.set({
    uniqueCpId: or28207.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理
 *
 * @param kikanFlg - 期間管理フラグ
 */
function or30063OnClick(kikanFlg: string) {
  or30063OnewayModel.kikanFlg = kikanFlg
  // Or00100のダイアログ開閉状態を更新する
  Or30063Logic.state.set({
    uniqueCpId: or30063.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function handleReturn28207(newValue: Or28207Type) {
  Or00398Logic.state.set({
    uniqueCpId: or00398.value.uniqueCpId,
    state: {
      param: {
        executeFlag: 'copy',
        deleteBtnValue: '',
        kikanKanriFlg: local.kikanKanriFlg,
        houjinId: systemCommonsStore.getHoujinId,
        shisetuId: systemCommonsStore.getShisetuId,
        userId: systemCommonsStore.getUserId,
        svJigyoId: systemCommonsStore.getSvJigyoId,
        raiId: newValue.raiId,
        kijunbiYmd: local.kijunbiYmd,
        sakuseiId: '',
        historyModifiedCnt: '',
        historyInfo: { ...local.historyInfo, raiId: newValue.raiId },
        planPeriodInfo: local.planPeriodInfo,
      } as TransmitParam,
    },
  })
}

function handleReturn30063(newValue: Or30063Type) {
  Or00393Logic.state.set({
    uniqueCpId: or00393.value.uniqueCpId,
    state: {
      param: {
        executeFlag: 'copy',
        deleteBtnValue: '',
        kikanKanriFlg: local.kikanKanriFlg,
        houjinId: systemCommonsStore.getHoujinId,
        shisetuId: systemCommonsStore.getShisetuId,
        userId: systemCommonsStore.getUserId,
        svJigyoId: systemCommonsStore.getSvJigyoId,
        raiId: newValue.raiId,
        kijunbiYmd: local.kijunbiYmd,
        sakuseiId: '',
        historyModifiedCnt: '',
        historyInfo: { ...local.historyInfo, raiId: newValue.raiId },
        planPeriodInfo: local.planPeriodInfo,
      } as TransmitParam,
    },
  })
}

onMounted(async () => {
  localOneway.mo00043OnewayType.tabItems = [
    {
      id: Or41339Const.STR.A,
      title: t('label.study-form'),
      tooltipText: t('label.study-form'),
      tooltipLocation: 'bottom',
    },
    {
      id: Or41339Const.STR.B,
      title: t('label.summary-table'),
      tooltipText: t('label.summary-table'),
      tooltipLocation: 'bottom',
    },
  ]
  local.mo00043.id = Or41339Const.STR.A
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or41339Const.STR_ALL })
  })
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })
  // コントロール設定
  await getCommonData()
})
/**
 * 修正ポップアップ（Or21814）用のユニークCP ID
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * 修正ポップアップ（Or21813）用のユニークCP ID
 */
const or21813 = ref({ uniqueCpId: '' })
const or00393 = ref({ uniqueCpId: '' })
const or00398 = ref({ uniqueCpId: '' })
/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [OrX0007Const.CP_ID(1)]: orX0007.value,
  [OrX0008Const.CP_ID(1)]: orX0008.value,
  [OrX0009Const.CP_ID(1)]: orX0009.value,
  [OrX0010Const.CP_ID(1)]: orX0010.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [Or21814Const.CP_ID(0)]: or21814.value, // 確認ダイアログ
  [Or21813Const.CP_ID(0)]: or21813.value, // 確認ダイアログ
  [Or33769Const.CP_ID(0)]: or33769.value,
  [Or51767Const.CP_ID(0)]: or51767.value,
  [Or00398Const.CP_ID(0)]: or00398.value,
  [Or00393Const.CP_ID(0)]: or00393.value,
  [Or30063Const.CP_ID(0)]: or30063.value,
  [Or28207Const.CP_ID(0)]: or28207.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.consider-table'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: false,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: true,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
  },
})
/**
 * 削除確認ダイアログを表示する
 *
 * @param param - param
 */
const openPopupComfirm = (param: ParamComfirm) => {
  return new Promise((resolve) => {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        isOpen: true,
        dialogText: param.message,
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: param?.firstBtnLabel ?? t('btn.yes'),
        secondBtnType: param?.secondBtnType ?? 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnLabel: t('btn.cancel'),
        thirdBtnType: param?.thirdBtnType ?? 'blank',
      },
    })
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        if (event?.firstBtnClickFlg) {
          if (param.excuteFunction) {
            void param.excuteFunction()
          }
          resolve(true)
        }
        if (event?.secondBtnClickFlg) {
          if (param.excuteFunction1) {
            void param.excuteFunction1()
          }
        }
        if (event?.thirdBtnClickFlg || event?.closeBtnClickFlg) {
          if (param.excuteFunction2) {
            void param.excuteFunction2()
          }
        }
        resolve(false)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
      },
      { once: true }
    )
  })
}
/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      favoriteIconClick()
      setOr11871Event({ favoriteEventFlg: false })
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await _update()
      setOr11871Event({ saveEventFlg: false })
    }
    if (newValue.createEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 新規ボタンが押下された場合、新規作成処理を実行する
        void addBtnClick()
        setOr11871Event({ createEventFlg: false })
      }
    }
    if (newValue.printEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 印刷ボタンが押下された場合、印刷設定画面を表示する
        void printSettingIconClick()
        setOr11871Event({ printEventFlg: false })
      }
    }
  }
)

/**
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg
    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }
    const index = listPeriod.value.findIndex(
      (item) => item.sc1Id.toString() === newValue.planTargetPeriodId
    )
    const isLast = index === listPeriod.value.length - 1
    const isFirst = index === 0
    const handleChangePreiod = async () => {
      const openErrMess = async (message: string) => {
        const param: ParamComfirm = {
          message,
          firstBtnLabel: 'OK',
          secondBtnType: 'blank',
        }
        await openPopupComfirm(param)
      }
      if (planUpdateFlg === '0') {
        handleLogicx0007(index)
      } else if (planUpdateFlg === '1') {
        if (isFirst) {
          await openErrMess(t('message.i-cmn-11262'))
          return
        }
        handleLogicx0007(index - 1)
      } else if (planUpdateFlg === '2') {
        if (isLast) {
          await openErrMess(t('message.i-cmn-11263'))
          return
        }
        handleLogicx0007(index + 1)
      }
      //const isSuccess = await periodSelectHandle(listHistory.value)
      await periodSelectHandle(listHistory.value)
    }
    if (isEdit.value && planUpdateFlg !== '0') {
      if ((isLast && planUpdateFlg === '2') || (isFirst && planUpdateFlg === '1')) return
      const saveAndNext = async () => {
        await _update(false)
        await handleChangePreiod()
      }
      const param: ParamComfirm = {
        message: t('message.i-ldg-10430'),
        excuteFunction1: void handleChangePreiod,
        excuteFunction: void saveAndNext,
        thirdBtnType: 'normal3',
      }
      await openPopupComfirm(param)
    } else {
      await handleChangePreiod()
    }
  },
  { deep: true }
)

/**
 * AC017_「履歴-前へアイコンボタン」押下
 * AC018_「履歴-次へアイコンボタン」押下
 * 履歴変更の監視
 */
watch(
  () => OrX0008Logic.data.get(orX0008.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) return

    const createId = String(newValue.createId)
    const createUpateFlg = newValue.createUpateFlg
    if (!createUpateFlg || (listHistory.value.length === 1 && !listHistory.value[0].rirekiId))
      return
    const index = listHistory.value.findIndex((item) => item.rirekiId.toString() === createId)
    const isLast = index === listHistory.value.length - 1
    const isFirst = index === 0
    const handleChangeHistory = async () => {
      if (createUpateFlg === '0') {
        handleLogicx0008(index)
      } else if (createUpateFlg === '1') {
        if (isFirst) {
          return
        }
        handleLogicx0008(index - 1)
      } else if (createUpateFlg === '2') {
        if (isLast) {
          return
        }
        handleLogicx0008(index + 1)
      }
      await historySelectHandle()
    }

    const isCreateMode = local.updateKbn === 'C'

    if (isCreateMode) {
      if (createUpateFlg === '2') return

      await handleChangeHistory()
      listHistory.value.pop()

      localOneway.orX0008Oneway.createData = {
        ...localOneway.orX0008Oneway.createData,
        totalCount: listHistory.value.length,
      }

      local.updateKbn = ''
      return
    }

    if (isEdit.value && createUpateFlg !== '0') {
      if ((isLast && createUpateFlg === '2') || (isFirst && createUpateFlg === '1')) return
      const saveAndNext = async () => {
        await _update(false)
        await handleChangeHistory()
      }

      const param: ParamComfirm = {
        message: t('message.i-ldg-10430'),
        excuteFunction1: void handleChangeHistory,
        excuteFunction: void saveAndNext,
        thirdBtnType: 'normal3',
      }
      await openPopupComfirm(param)
    } else {
      await handleChangeHistory()
    }
  },
  { deep: true }
)
/**************************************************
 * 関数
 **************************************************/

/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}
const listHistory = ref<RirekiJoho[]>([])
const listPeriod = ref<PlanPeriodInfo[]>([])
const or00398Ref = ref<{
  save(deleteKbn: string, updateKbn: string): Promise<kentouyousiInfoUpdateOutEntity>
}>()
const or00393Ref = ref<{
  save(deleteKbn: string, updateKbn: string): Promise<kentouyousiInfoUpdateOutEntity>
}>()
/**
 * 画面コントロール表示設定
 *
 * @param index - index
 */
const handleLogicx0007 = (index: number) => {
  localOneway.orX0007Oneway.planTargetPeriodData = {
    orX0115Oneway: {
      sc1Id: Number(listPeriod.value[index].sc1Id),
    },
    planTargetPeriodId: Number(listPeriod.value[index].sc1Id),
    planTargetPeriod: listPeriod.value[index]?.startYmd + ' ~ ' + listPeriod.value[index]?.endYmd,
    currentIndex: index + 1,
    totalCount: listPeriod.value.length,
  } as PlanTargetPeriodDataType
  local.planPeriodInfo = listPeriod.value[index]
  OrX0007Logic.data.set({
    uniqueCpId: orX0007.value.uniqueCpId,
    value: {
      planTargetPeriodId: listPeriod.value[index].sc1Id.toString(),
      PlanTargetPeriodUpdateFlg: '',
    },
  })
  local.addBtnState = false
}
const handleLogicx0008 = (index: number) => {
  localOneway.orX0008Oneway.createData = {
    createId: listHistory.value[index]?.rirekiId.toString() ?? '',
    createDate: listHistory.value[index]?.sakuseiYmd ?? '',
    staffId: listHistory.value[index]?.chkShokuId.toString() ?? '',
    staffName: listHistory.value[index]?.shokuinKnj ?? '',
    currentIndex: index + 1,
    totalCount: listHistory.value.length,
  }
  localOneway.orX0008Oneway.plan1Id = listHistory.value[index]?.rirekiId.toString() ?? ''
  localOneway.orX0008Oneway.officeId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.orX0008Oneway.sc1Id =
    OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? ''
  localOneway.orX0008Oneway.useId = systemCommonsStore.getUserId ?? ''
  local.historyInfo = listHistory.value[index]
  OrX0008Logic.data.set({
    uniqueCpId: orX0008.value.uniqueCpId,
    value: {
      createId: listHistory.value[index]?.rirekiId.toString(),
      createUpateFlg: '',
      rirekiObj: {
        createId: listHistory.value[index]?.rirekiId.toString(),
        createDate: listHistory.value[index]?.sakuseiYmd ?? '',
        staffId: listHistory.value[index]?.chkShokuId.toString() ?? '',
        staffName: listHistory.value[index]?.shokuinKnj ?? '',
        currentIndex: index + 1,
        totalCount: listHistory.value.length,
      },
    },
    isInit: true,
  })
  OrX0009Logic.data.set({
    uniqueCpId: orX0009.value.uniqueCpId,
    value: {
      staffId: listHistory.value[index]?.chkShokuId.toString() ?? '',
      staffName: listHistory.value[index]?.shokuinKnj ?? '',
    },
    isInit: true,
  })
  local.addBtnState = false
}
const callbackUserChange = async (userId: string) => {
  systemCommonsStore.setUserId(userId)
  await getCommonData()
}
const isPerioidsEmpty = computed(() => listPeriod.value.length === 0)
const callbackFuncJigyo = async (newJigyoId: string) => {
  systemCommonsStore.setSvJigyoId(newJigyoId)
  await getCommonData()
}
// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, (newJigyoId: string) => {
  void callbackFuncJigyo(newJigyoId)
})
// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc((userId: string) => {
  void callbackUserChange(userId)
})
const isFirstInit = ref(true)
/**
 * 共通情報取得
 */

const getCommonData = async () => {
  const param: kentouyousiCommonInfoSelectInEntity = {
    shisetuId: systemCommonsStore.getShisetuId ?? '1',
    userId: systemCommonsStore.getUserId ?? '',
    syubetsuId: systemCommonsStore.getSyubetu ?? '1',
    svJigyoIdList: ['1'],
    sc1Id: '1',
    shokuinId: '1',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
  }

  const res: kentouyousiCommonInfoSelectOutEntity = await ScreenRepository.select(
    'kentouyousiCommonInfoSelect',
    param
  )
  // if (isFirstInit.value) {
  //   const selectedSvJigyoId = res.data.tekiyoJigyoList.find(
  //     (item) => item.tekiyoFlg === '1'
  //   )?.svJigyoId
  //   Or41179Logic.state.set({
  //     uniqueCpId: or41179.value.uniqueCpId,
  //     state: {
  //       jigyoInfoList: res.data.tekiyoJigyoList.map((item) => ({
  //         ...item,
  //         jigyoRyakuKnj: item.jigyoNameKnj,
  //       })) as any,
  //     },
  //   })
  //   Or41179Logic.data.set({
  //     uniqueCpId: or41179.value.uniqueCpId,
  //     value: { modelValue: selectedSvJigyoId },
  //   })
  // }
  local.kikanKanriFlg = res.data.kikanKanriFlg
  if (res.data.kikanKanriFlg === '1') {
    isUsePlanPeriod.value = true
  } else {
    isUsePlanPeriod.value = false
  }
  if (res.data.planPeriodList?.length) {
    listPeriod.value = res.data.planPeriodList
    handleLogicx0007(res.data.planPeriodList.length - 1)
  } else {
    listPeriod.value = []
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = 0
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = 0
  }
  listHistory.value = res.data.rirekiList.map((item, index) => ({
    ...item,
    ...res.data.rirekiList[index],
  }))
  await handleMapHistoryData(res.data.rirekiList, listHistory.value.length - 1)
  isFirstInit.value = false
}
const periodSelectHandle = async (rirekiJoho: RirekiJoho[], RirekiId?: string) => {
  listHistory.value = rirekiJoho.map((item, index) => ({
    ...item,
    ...rirekiJoho[index],
  }))
  const indexHistory = listHistory.value.findIndex((item) => item.rirekiId.toString() === RirekiId)
  const fallbackIndex = indexHistory === -1 ? listHistory.value.length - 1 : indexHistory
  await handleMapHistoryData(rirekiJoho, fallbackIndex)
  return true
}
const historySelectHandle = async () => {
  await handleMapDetailForm(listHistory.value)
}
const handleMapHistoryData = async (res: RirekiJoho[], index: number) => {
  handleLogicx0008(index)
  await handleMapDetailForm(res)
}
const handleMapDetailForm = async (res: RirekiJoho[]) => {
  local.isSoftDelete = false
  listHistory.value = res.map((item, index) => ({
    ...item,
    ...res[index],
  }))
  const selectedRireki = listHistory.value.find(
    (item) => item.rirekiId === OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId
  )
  localOneway.orX0009Oneway.createData = {
    createDate: selectedRireki?.sakuseiYmd ?? '',
    createId: Number(selectedRireki?.rirekiId),
    totalCount: listHistory.value.length,
    currentIndex: listHistory.value.indexOf(selectedRireki!),
    staffName: selectedRireki?.shokuinKnj ?? '',
    staffId: Number(selectedRireki?.chkShokuId.toString()),
  }
  OrX0010Logic.data.set({
    uniqueCpId: orX0010.value.uniqueCpId,
    value: { value: selectedRireki?.sakuseiYmd ?? '', mo01343: {} as unknown as Mo01343Type },
    isInit: true,
  })
  if (listHistory.value.length === 0) {
    local.updateKbn = ''
    await nextTick()
    await addBtnClick()
  } else {
    getTabsData('getData')
    //setDataContent(res.data.cpnHkd3, res.data.cpnHkd2)
  }
}

/**
 * AC002_「お気に入りアイコンボタン」押下
 */
const favoriteIconClick = () => {
  // TODO お気に入りに該当機能がない場合
  if (favorite.value) {
    // 「アセスメント(インターライ)」をお気に入り機能に追加する。
  }
  // お気に入りに該当機能がある場合
  else {
    // 「アセスメント(インターライ)」をお気に入り機能から削除する。
  }
}
/**
 * AC003_「保存ボタン」押下
 *
 * @param isRunInit - isRunInit
 */
const _update = async (isRunInit = true) => {
  if (isPerioidsEmpty.value) return

  if (local.updateKbn !== 'C') {
    // console.log(!isEdit.value && !local.isSoftDelete)
    //if (!isEdit.value && !local.isSoftDelete) {
    // const param: ParamComfirm = {
    //   message: t('message.i-cmn-21800'),
    //   firstBtnLabel: t('OK'),
    //   secondBtnType: 'blank',
    // }
    //openPopupComfirm(param)
    //   return false
    // }
    if (isEdit.value) {
      local.updateKbn = 'U'
    }
    if (local.isSoftDelete) {
      local.updateKbn = 'D'
    }
  } else if (local.isSoftDelete) {
    local.isSoftDelete = false
    return
  }

  const resData =
    local.mo00043.id === 'A'
      ? await or00398Ref.value?.save(local.updateKbn, local.isSoftDelete ? '1' : '0')
      : await or00393Ref.value?.save(local.updateKbn, local.isSoftDelete ? '1' : '0')
  let currentIndexHistory

  if (resData?.statusCode === 'success') {
    if (local.updateKbn === 'D') {
      currentIndexHistory = listHistory.value.findIndex(
        (item) => item.rirekiId === OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId
      )
      listHistory.value.splice(currentIndexHistory, 1)
      if (currentIndexHistory === listHistory.value.length) {
        currentIndexHistory = listHistory.value.length - 1
      }
      handleLogicx0008(currentIndexHistory)
      await historySelectHandle()
      local.isSoftDelete = false
    } else if (local.updateKbn === 'C' && isRunInit) {
      await periodSelectHandle(listHistory.value, resData.data.rirekiId)
    } else if (local.updateKbn === 'U' && isRunInit) {
      await periodSelectHandle(listHistory.value, resData.data.rirekiId)
    }
    local.updateKbn = ''
  }

  local.addBtnState = false
}
/**
 * AC004_「新規ボタン」押下
 */
const addBtnClick = async () => {
  if (local.isSoftDelete || isPerioidsEmpty.value) return
  if (local.updateKbn === 'C') {
    const param1: ParamComfirm = {
      message: t('message.i-cmn-11265', [t('label.consider-table')]),
      secondBtnType: 'blank',
      firstBtnLabel: t('OK'),
    }
    await openPopupComfirm(param1)
    return
  }
  const handleAdd = () => {
    local.updateKbn = 'C'

    OrX0010Logic.data.set({
      uniqueCpId: orX0010.value.uniqueCpId,
      value: {
        value: systemCommonsStore.getSystemDate ?? '',
        mo01343: {} as unknown as Mo01343Type,
      },
      isInit: true,
    })
    if (!isUsePlanPeriod.value) {
      OrX0007Logic.data.set({
        uniqueCpId: orX0007.value.uniqueCpId,
        value: {
          planTargetPeriodId: listPeriod.value[listPeriod.value?.length - 1].sc1Id.toString(),
          PlanTargetPeriodUpdateFlg: '',
        },
      })
    }
    listHistory.value.push({
      sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
      chkShokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      rirekiId: '',
      sakuseiYmd: OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? '',
      shokuinKnj: '',
      plnType: '',
    })
    OrX0009Logic.data.set({
      uniqueCpId: orX0009.value.uniqueCpId,
      value: {
        staffId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
        staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
      },
      isInit: true,
    })
    localOneway.orX0009Oneway.createData = {
      createDate: systemCommonsStore.getSystemDate ?? '',
      createId: 0,
      totalCount: listHistory.value.length,
      currentIndex: listHistory.value.length,
      staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
      staffId: Number(systemCommonsStore.getCurrentUser.chkShokuId),
    }
    OrX0008Logic.data.set({
      uniqueCpId: orX0008.value.uniqueCpId,
      value: {
        createId: '',
        createUpateFlg: '',
      },
    })
    localOneway.orX0008Oneway.createData = {
      ...localOneway.orX0008Oneway.createData,
      currentIndex: listHistory.value.length,
      totalCount: listHistory.value.length,
      staffId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
    }
    local.addBtnState = true
    getTabsData('add')
  }
  const handleSaveAndAdd = async () => {
    await _update()
    handleAdd()
  }
  if (isEdit.value) {
    const param: ParamComfirm = {
      excuteFunction: handleAdd,
      excuteFunction1: void handleSaveAndAdd,
      message: t('message.i-cmn-10430'),
    }
    await openPopupComfirm(param)
  } else {
    handleAdd()
  }
}

/**
 * AC005_「複写ボタン」押下
 */
const copyBtnClick = () => {
  if (local.isSoftDelete || isPerioidsEmpty.value) return
  switch (local.mo00043.id) {
    case Or41339Const.STR.A:
      or28207OnClick(local.kikanKanriFlg)
      break
    case Or41339Const.STR.B:
      or30063OnClick(local.kikanKanriFlg)
      break
  }
}

/**
 * AC006_「印刷設定アイコンボタン」押下
 */
const printSettingIconClick = async () => {
  if (local.isSoftDelete || isPerioidsEmpty.value) return
  const openPrint = () => {}
  const handleSaveAndOpenPrint = async () => {
    await _update()
    openPrint()
  }
  if (isEdit.value) {
    const param: ParamComfirm = {
      excuteFunction: void handleSaveAndOpenPrint,
      excuteFunction1: openPrint,
      message: t('message.i-cmn-10430'),
    }
    await openPopupComfirm(param)
  } else {
    openPrint()
  }
}
const deleteClick = async () => {
  if (isPerioidsEmpty.value) return
  const softDelete = () => {
    local.isSoftDelete = true
  }
  const param: ParamComfirm = {
    excuteFunction: softDelete,
    message: t('message.i-cmn-11326', [
      `[${OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? ''}]`,
      t('label.consider-table'),
    ]),
  }
  if (local.isSoftDelete) return
  await openPopupComfirm(param)
}
// const handleGetDataCopy = async (newValue: Or33769Type) => {
//   //setDataContent(res.data.cpnHkd3, res.data.cpnHkd2, false)
// }

/**
 * AC022_タブ選択
 *
 * @param param - タブ選択ID
 */
const updateModelValue = async (param: Mo00043Type) => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()
        break
      case 'no':
        // 処理続き
        deleteFlag.value = false
        local.addBtnState = false
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  local.mo00043.id = param.id

  getTabsData('getData')
}

/**
 * アセスメント(インターライ)画面履歴の最新情報を取得する
 *
 * @param executeFlag - executeFlag
 */
const getTabsData = (executeFlag: string) => {
  executeFlag = local.addBtnState ? 'add' : executeFlag
  allTabParamSet({
    executeFlag: executeFlag,
    deleteBtnValue: '',
    kikanKanriFlg: local.kikanKanriFlg,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: systemCommonsStore.getShisetuId,
    userId: systemCommonsStore.getUserId,
    svJigyoId: systemCommonsStore.getSvJigyoId,
    raiId: '',
    kijunbiYmd: local.kijunbiYmd,
    sakuseiId: '',
    historyModifiedCnt: '',
    historyInfo: local.historyInfo,
    planPeriodInfo: local.planPeriodInfo,
  } as unknown as TransmitParam)
}

/**
 * 各タブのパラメータを作成する
 *
 * @param param - パラメータ
 */
const allTabParamSet = (param: TransmitParam) => {
  switch (local.mo00043.id) {
    // タブA
    case Or41339Const.STR.A:
      // Or00398のダイアログ状態を更新する
      Or00398Logic.state.set({
        uniqueCpId: or00398.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case Or41339Const.STR.B:
      // Or00393のダイアログ状態を更新する
      Or00393Logic.state.set({
        uniqueCpId: or00393.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
  }
}

/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 確認ダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

// function or33769OnClick(planPeriodFlg: string) {
//   localOneway.or33769OnewayModel.planPeriodFlg = planPeriodFlg
//   // Or00100のダイアログ開閉状態を更新する
//   Or33769Logic.state.set({
//     uniqueCpId: or33769.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }
// const showContentDetail = computed(() => !local.isSoftDelete && !isPerioidsEmpty.value)
const showOrX0007 = computed(() => isUsePlanPeriod.value)
const showOrX0008 = computed(() => !isPerioidsEmpty.value)
const showOrX0009 = computed(() => !isPerioidsEmpty.value)
</script>

<template>
  <c-v-sheet class="view d-flex flex-column h-100">
    <!-- ローディング -->
    <!-- <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay> -->
    <!-- 操作ボタンエリア -->
    <div class="action-sticky">
      <g-base-or11871
        v-bind="or11871"
        class="action-end"
      >
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            @click="copyBtnClick"
          />
        </template>
        <template #customButtons>
          <base-mo00611
            :oneway-model-value="{
              btnLabel: t('btn.copy'),
              minWidth: '52px',
            }"
            @click="copyBtnClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="t('btn.copy')"
            />
          </base-mo00611>
        </template>
        <template #optionMenuItems>
          <c-v-list-item
            :title="
              t(
                'label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-delete'
              )
            "
            prepend-icon="delete"
            @click="deleteClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.delete-data')"
            />
          </c-v-list-item>
        </template>
      </g-base-or11871>
    </div>
    <!-- アクションエリア -->
    <div class="action-content">
      <c-v-row
        no-gutters
        class="main-Content d-flex flex-0-1 h-100 overflow-y-auto position-relative"
      >
        <!-- ナビゲーションエリア -->
        <c-v-col
          cols="auto"
          class="hidden-scroll h-100 mt-1 ml-6 main-left"
        >
          <!-- 利用者選択一覧 -->
          <g-base-or00248
            v-bind="or00248"
            class="userList"
          />
        </c-v-col>
        <!-- コンテンツエリア -->
        <c-v-col class="main-right d-flex flex-column overflow-hidden h-100">
          <!-- 上段 -->
          <c-v-row
            no-gutters
            class="top"
          >
            <c-v-col>
              <!-- 事業所 -->
              <c-v-row
                no-gutters
                class="d-flex align-end"
                style="padding-left: 24px"
              >
                <c-v-col
                  cols="auto mr-7 office-select"
                  style="width: 212px"
                  ><g-base-or-41179 v-bind="or41179"
                /></c-v-col>
                <!-- </c-v-row>
            <c-v-row no-gutters> -->
                <!-- 計画対象期間 -->
                <c-v-col
                  v-show="showOrX0007"
                  cols="auto"
                >
                  <g-custom-orX0007
                    v-bind="orX0007"
                    :is-edit="isEdit"
                    :parent-method="_update"
                    :oneway-model-value="localOneway.orX0007Oneway"
                    :unique-cp-id="orX0007.uniqueCpId"
                  />
                </c-v-col>
                <!-- 作成者 -->
                <c-v-col
                  v-show="showOrX0009"
                  cols="auto ml-5"
                  style="align-content: center"
                >
                  <!-- TODO GUI00220 職員検索画面未作成 -->
                  <g-custom-orX0009
                    v-bind="orX0009"
                    :class="local.isSoftDelete ? 'disabled-event' : ''"
                    :oneway-model-value="localOneway.orX0009Oneway"
                    :unique-cp-id="orX0009.uniqueCpId"
                  />
                </c-v-col>
                <!-- 基準日 -->
                <c-v-col
                  v-show="showOrX0009"
                  :class="local.isSoftDelete ? 'disabled-event' : ''"
                  cols="auto ml-5"
                >
                  <g-custom-or-x0010
                    v-bind="orX0010"
                    :oneway-model-value="{
                      ...localOneway.orX0010Oneway,
                      isDisabled: local.isSoftDelete,
                    }"
                    :unique-cp-id="orX0010.uniqueCpId"
                    class="custom-required"
                  />
                </c-v-col>

                <!-- 履歴 -->
                <c-v-col
                  v-show="showOrX0008"
                  cols="auto ml-5"
                >
                  <g-custom-orX0008
                    v-bind="orX0008"
                    :is-edit="isEdit"
                    :parent-method="_update"
                    :oneway-model-value="localOneway.orX0008Oneway"
                    :unique-cp-id="orX0008.uniqueCpId"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- コンテンツエリアタブ -->
          <c-v-row
            no-gutters
            class="mt-6"
          >
            <c-v-col class="tabItems">
              <base-mo00043
                :model-value="local.mo00043"
                :oneway-model-value="localOneway.mo00043OnewayType"
                style="padding-left: 24px !important; padding-right: 0px !important"
                @update:model-value="updateModelValue"
              >
              </base-mo00043>
            </c-v-col>
          </c-v-row>
          <!-- 中段 -->
          <c-v-row
            no-gutters
            class="middleContent flex-1-1 h-100"
          >
            <c-v-window v-model="local.mo00043.id">
              <c-v-window-item
                value="A"
                class="h-100 overflow-y-auto"
              >
                <g-custom-or00398
                  ref="or00398Ref"
                  v-bind="or00398"
                ></g-custom-or00398>
              </c-v-window-item>
              <c-v-window-item
                value="B"
                class="h-100 overflow-y-auto"
              >
                <g-custom-or00393
                  ref="or00393Ref"
                  v-bind="or00393"
                ></g-custom-or00393>
              </c-v-window-item>
            </c-v-window>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </div>
  </c-v-sheet>
  <!-- メッセージ -->
  <g-base-or-21815 v-bind="or21815" />
  <g-custom-or-10279
    v-bind="or10279"
    :oneway-model-value="localOneway.Or10279Model"
  />
  <!-- GUI00070 対象期間画面 -->
  <g-custom-gui-00070 v-bind="gui00070" />
  <g-base-or21814 v-bind="or21814" />
  <g-base-or21813 v-bind="or21813" />
  <!-- <g-custom-or-33769
    v-if="showDialogOr33769"
    v-bind="or33769"
    :oneway-model-value="localOneway.or33769OnewayModel"
    @update:model-value="handleGetDataCopy"
  /> -->
  <g-custom-or-51767
    v-if="showDialogOr51767"
    v-bind="or51767"
  />
  <g-custom-or-28207
    v-if="showDialogOr28207"
    v-bind="or28207"
    :oneway-model-value="or28207OnewayModel"
    @update:model-value="handleReturn28207"
  />

  <g-custom-or-30063
    v-if="showDialogOr30063"
    v-bind="or30063"
    :oneway-model-value="or30063OnewayModel"
    @update:model-value="handleReturn30063"
  />
</template>

<style scoped lang="scss">
.view {
  background-color: transparent;
  height: max-content !important;
}

.main-Content {
  .main-left {
    max-width: 273px;

    .userList {
      :deep(.v-list) {
        max-height: 1019px !important;
      }
    }
  }

  .main-right {
    .middleContent {
      min-height: 0;

      .v-window {
        width: 100%;
      }
    }
  }
}

:deep(.rounded.item-label) {
  min-width: 180px;
  padding-left: 8px !important;
}
:deep(.office-select .v-sheet) {
  background-color: transparent !important;
  .v-input__control {
    background-color: #fff;
  }
}

.office-select {
  :deep(.ma-2) {
    margin: 4px 0px !important;
  }
}
.action-sticky {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
  padding-left: 8px;
  padding-bottom: 8px;
  margin-bottom: 8px;
}
.action-end {
  ::v-deep(hr:first-of-type) {
    display: none;
  }
}
.action-content {
  background: rgb(var(--v-theme-background));
  height: max-content;
  z-index: 888;
  padding-bottom: 104px;
}
.createDateOuterClass {
  width: auto !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }

  :deep(.v-input__details) {
    display: none;
  }
  :deep(.v-input__control) {
    background-color: #fff;
  }
}
.tabItems {
  :deep(.v-tab) {
    padding-left: 0px !important;
    padding-right: 0px !important;
    margin-right: 16px;
  }

  :deep(.v-slide-group__prev) {
    display: none;
  }

  :deep(.v-slide-group__next) {
    display: none;
  }

  :deep(.v-btn--variant-text),
  :deep(.v-slide-group__content),
  :deep(.v-slide-group__container),
  :deep(.v-tabs) {
    height: 36px !important;
  }
}
:deep(.custom-required) {
  .item-label::after {
    content: '必須';
    color: #ed6809;
    font-size: 12px;
    padding-left: 4px;
  }
}

:deep(.shoku-label) {
  width: 112px !important;
}

:deep(
  .table-wrapper
    .v-table__wrapper
    td:has(input:not([readonly]):not([disabled]):not([type='checkbox']):not([type='radio']))
    input
) {
  outline: none !important;
}
</style>
