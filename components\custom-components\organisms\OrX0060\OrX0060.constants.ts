import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * OrX0060Const:操作ボタンエリア、テーブルエリア
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace OrX0060Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('OrX0060', seq)

  /**
   * 処遇実施上の留意点
   */
  export const STR_RYUI_KNJ = 'ryui_knj'
  /**
   * 具体的な課題
   */
  export const STR_KADAI_KNJ = 'kadai_knj'
  /**
   * 生活目標
   */
  export const STR_MOKUHYO_KNJ = 'mokuhyo_knj'
  /**
   * 目標期間(達成時期)
   */
  export const STR_KIKAN_KNJ = 'kikan_knj'
  /**
   * 処遇の内容
   */
  export const STR_NAIYO_KNJ = 'naiyo_knj'
  /**
   * 課題取込セキュリティチェック 1: 権限あり
   */
  export const KADAI_TORIKOMI_1 = '1'
  /**
   * 0:本文末に追加、1:本文上書
   */
  export const OPERATION_KBN_1 = '1'
  /**
   * 年月日の区分 1:年月日
   */
  export const YMD_KBN_1 = '1'
  /**
   * 年月日の区分 2:月日
   */
  export const YMD_KBN_2 = '2'
  /**
   * 取込区分
   */
  export const IMPORT_KBN_1 = '1'
  /**
   * セレクトモード 0:単日指定 1:範囲選択 2:範囲選択（期間指定）
   */
  export const SELECT_MODE_0 = '0'
  /**
   * セレクトモード 0:単日指定 1:範囲選択 2:範囲選択（期間指定）
   */
  export const SELECT_MODE_1 = '1'
  /**
   * 期間のカレンダー取込み（0:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日）
   */
  export const PKKAK21_YMD_0 = '0'
  /**
   * 期間のカレンダー取込み（0:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日）
   */
  export const PKKAK21_YMD_1 = '1'
  /**
   * 大分類CD:2220
   */
  export const B1CD_2220 = '2220'
  /**
   * 中分類CD: 1
   */
  export const B2CD_1 = '1'
  /**
   * 中分類CD: 2
   */
  export const B2CD_2 = '2'
  /**
   * 中分類CD: 3
   */
  export const B2CD_3 = '3'
  /**
   * 中分類CD: 8
   */
  export const B2CD_8 = '8'
  /**
   * 中分類CD: 9
   */
  export const B2CD_9 = '9'
  /**
   * 小分類CD:0
   */
  export const B3CD_0 = '0'
  /**
   * テーブル名:"cpn_tuc_syp_kkak21_1"
   */
  export const TABLE_NAME_211 = 'cpn_tuc_syp_kkak21_1'
  /**
   * テーブル名:"cpn_tuc_syp_kkak21_2"
   */
  export const TABLE_NAME_212 = 'cpn_tuc_syp_kkak21_2'
  /**
   * テーブル名:"cpn_tuc_syp_kkak21_3"
   */
  export const TABLE_NAME_213 = 'cpn_tuc_syp_kkak21_3'
  /**
   * 初期値：ヘッダー情報
   */
  export const INIT_HEADERS = [
    {
      title: '具体的な課題',
      key: 'kadaiKnj',
      minWidth: '240px',
      sortable: false,
    },
    {
      title: '生活目標',
      key: 'mokuhyoKnj',
      minWidth: '240px',
      sortable: false,
    },
    {
      title: '目標期間（達成時期）',
      key: 'kikanKnj',
      minWidth: '160px',
      sortable: false,
    },
    {
      title: '処遇の内容',
      key: 'naiyoKnj',
      minWidth: '240px',
      sortable: false,
    },
    {
      title: '担当者',
      key: 'tantoShokuKnj',
      minWidth: '240px',
      sortable: false,
    },
  ]
}
