import { reportOutputType, useReportUtils } from './useReportUtils'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { IInitMasterInfo } from '~/repositories/cmn/entities/GyoumuComSelectEntity'
import type { PrintCloseUpdateEntity } from '~/repositories/cmn/entities/PrintCloseUpdateEntity'
import type {
  IPrintInfo,
  IKikanRirekiInfo,
  PrintSelectOutData,
  PrintSelectOutEntity,
  PrintSelectInEntity,
  IPrintSettingInfo,
} from '~/repositories/cmn/entities/PrintSelectEntity'
import type {
  PrintSubjectSelectInEntity,
  PrintSubjectSelectOutData,
  PrintSubjectSelectOutEntity,
  PrtHistoryInfo,
} from '~/repositories/cmn/entities/PrintSubjectSelectEntity'
import type {
  PrintUserChangeSelectInEntity,
  PrintUserChangeSelectOutData,
  PrintUserChangeSelectOutEntity,
} from '~/repositories/cmn/entities/PrintUserChangeSelectEntity'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01334Items } from '~/types/business/components/Mo01334Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { OrX0130TableType } from '~/components/custom-components/organisms/OrX0130/OrX0130.type'
import type { CmnTucPlanObj } from '~/repositories/cmn/entities/PlanAllPrintSettingsSubjectSelectEntity'
import { initEditFlg } from '~/composables/useComponentVue'

/** 共通メソッド */
const { reportOutput } = useReportUtils()

/**
 * 正常 200
 */
export const STATUS_CODE_SUCCESS_200 = '200'

/**
 * 正常 204
 */
export const STATUS_CODE_SUCCESS_204 = '204'

interface PrintOptionInfo<T> {
  /**
   * 初期設定マスタの情報
   */
  initMasterObj: T
  /**
   * 事業所名
   */
  jigyoKnj: string
}

/**
 * 画面情報
 */
interface IScreenObject<R extends IPrintInfo, K extends IKikanRirekiInfo> {
  /**
   * 印刷設定情報リスト
   */
  prtList: R[]
  /**
   * 期間履歴情報リスト
   */
  kikanRirekiList?: K[]
}

/**
 * DB未保存画面項目
 */
export interface IDbNoSaveData {
  /**
   * 指定日
   */
  selectDate: string
  /**
   * 記入用シートを印刷するフラグ
   */
  emptyFlg?: string
}

/**
 * 印刷対象履歴
 */
export interface HistoryInfo {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 履歴ID
   */
  rirekiId: string
}

/**
 * 帳票用情報
 */
export interface FormInfo<I extends IInitMasterInfo, S extends IDbNoSaveData, H extends HistoryInfo>
  extends InWebEntity {
  /**
   * 事業者名
   */
  jigyoKnj: string
  /**
   * システム日付
   */
  appYmd: string
  /**
   * 初期設定マスタの情報
   */
  initMasterObj: I
  /**
   * 印刷設定
   */
  printSet: IPrintSettingInfo
  /**
   * DB未保存画面項目
   */
  dbNoSaveData: S
  /**
   * 印刷対象履歴リスト
   */
  printSubjectHistory?: H
  /**
   * 利用者リスト
   */
  userList?: CmnTucPlanObj[]
}

/**
 * 印刷データの取得
 *
 * @param ret - API戻り値
 *
 * @param screenObj - 画面項目
 */
const doGetPrintData = <
  T extends PrintSelectOutEntity<PrintSelectOutData<R, K>, R, K>,
  R extends IPrintInfo,
  K extends IKikanRirekiInfo,
>(
  ret: T,
  screenObj: IScreenObject<R, K>
) => {
  const data = ret.data
  screenObj.prtList = data.prtList
  if (data.kikanRirekiList) {
    screenObj.kikanRirekiList = data.kikanRirekiList
  }
}

/**
 * 履歴情報を取得する
 *
 * @param inputData - API入力値
 *
 * @param apiName - API名
 */
const doUserClick = async <
  T extends PrintUserChangeSelectInEntity,
  K extends IKikanRirekiInfo,
  M extends PrintUserChangeSelectOutEntity<PrintUserChangeSelectOutData<K>, K>,
>(
  inputData: T,
  apiName: string
) => {
  const ret: M = await ScreenRepository.select(apiName, inputData)
  return ret.data.kikanRirekiList
}

/**
 * 利用者選択が「複数」の場合、利用者一覧で選択した利用者リストがある、利用者配下の履歴情報を再取得する
 *
 * @param inputData - API入力値
 *
 * @param selectMode - 画面.利用者選択
 *
 * @param apiName - API名
 */
const doRetryRirekiData = async <
  T extends PrintSubjectSelectInEntity,
  K extends PrtHistoryInfo,
  M extends PrintSubjectSelectOutEntity<PrintSubjectSelectOutData<K>, K>,
>(
  inputData: T,
  selectMode: string,
  apiName: string
) => {
  if (selectMode === OrX0130Const.DEFAULT.HUKUSUU) {
    const ret: M = await ScreenRepository.select(apiName, inputData)
    return ret.data.prtHistoryList
  } else {
    return []
  }
}

/**
 * 印刷設定情報リストを取得する
 *
 * @param inputData - API入力値
 *
 * @param apiName - API名
 */
const doPrintGet = async <
  T extends PrintSelectInEntity,
  P extends IPrintInfo,
  K extends IKikanRirekiInfo,
  M extends PrintSelectOutEntity<PrintSelectOutData<P, K>, P, K>,
>(
  inputData: T,
  apiName: string
) => {
  const ret: M = await ScreenRepository.update(apiName, inputData)
  // if (ret.statusCode !== STATUS_CODE_SUCCESS_200) {
  //   return
  // }
  return ret
}

/**
 * 印刷設定情報を保存する
 *
 * @param inputData - API入力値
 *
 * @param apiName - API名
 *
 * @param showMessageBox - メッセージを表示
 */
const doPrintUpdate = async <C extends PrintCloseUpdateEntity<R>, R extends IPrintInfo>(
  inputData: C,
  apiName: string,
  showMessageBox: (text: string, btn: string, isError?: boolean) => Promise<unknown>
) => {
  const result: OutWebEntity = await ScreenRepository.update(apiName, inputData)
  if (
    result.statusCode !== STATUS_CODE_SUCCESS_204 &&
    result.statusCode !== STATUS_CODE_SUCCESS_200
  ) {
    void showMessageBox('message.e-cmn-40172', 'btn.ok')
    return
  }
}

/**
 * 業務チェックを行う
 *
 * @param title - 選択された印刷設定情報リストのプロファイル
 *
 * @param checkbox - 画面.記入用シートを印刷するチェックボックスがチェック
 *
 * @param userListLength - 利用者一覧の件数
 *
 * @param rirekiListLength - 履歴一覧の件数
 *
 * @param showMessageBox - メッセージを表示
 */
const doCheckBeforePrint = (
  title: string,
  checkbox: boolean,
  userListLength: number,
  rirekiListLength: number,
  showMessageBox: (text: string, btn: string, isError?: boolean) => Promise<unknown>
) => {
  // 選択された印刷設定情報リストのプロファイルが空文字の場合
  if (!title) {
    void showMessageBox('message.e-cmn-40172', 'btn.yes')
    return false
  }

  // 画面.記入用シートを印刷するチェックボックスがチェックOFF 且つ利用者一覧が0件の場合
  if (!checkbox && userListLength === 0) {
    void showMessageBox('message.i-cmn-11393', 'btn.yes', false)
    return false
  }

  // 画面.記入用シートを印刷するチェックボックスがチェックOFF 且つ履歴一覧が0件の場合
  if (!checkbox && rirekiListLength === 0) {
    void showMessageBox('message.i-cmn-11455', 'btn.yes', false)
    return false
  }
  return true
}

/**
 * 事業者名
 *
 * @param prtData - 印刷設定情報リスト選択行
 *
 * @param historyData - 印刷対象履歴
 *
 * @param printOption - 印刷オプション
 *
 * @param dbNoSaveData - DB未保存画面項目
 *
 * @param appYmd - システム日付
 *
 * @param prtId - 帳票ID
 */
const doReportOutput = async <
  R extends IPrintInfo,
  I extends IInitMasterInfo,
  D extends IDbNoSaveData,
  H extends HistoryInfo,
>(
  prtData: R,
  historyData: H,
  printOption: PrintOptionInfo<I>,
  dbNoSaveData: D,
  appYmd: string,
  prtId?: string
) => {
  // 帳票データ
  const outputData: FormInfo<I, D, H> = {
    // 印刷設定
    printSet: setPrintData(prtData),
    // 事業所名
    jigyoKnj: printOption.jigyoKnj,
    // システム日付
    appYmd: appYmd,
    // 初期設定マスタの情報
    initMasterObj: printOption.initMasterObj,
    // DB未保存画面項目
    dbNoSaveData: dbNoSaveData,
    // 印刷対象履歴
    printSubjectHistory: historyData,
  }
  // 帳票出力
  await reportOutput(prtId ?? prtData.prtId, outputData, reportOutputType.DOWNLOAD)
}

/**
 * 事業者名
 *
 * @param prtData - 印刷設定情報リスト選択行
 *
 * @param historyData - 印刷対象履歴
 *
 * @param orX0117OnewayType - OrX0117へのワンウェイモデルバリュー
 *
 * @param selectMode - 画面.利用者選択
 *
 * @param selectedUserList - 画面.利用者一覧で選択した利用者リスト
 *
 * @param printOption - 印刷オプション
 *
 * @param dbNoSaveData - DB未保存画面項目
 *
 * @param appYmd - システム日付
 *
 * @param newPrtData - 印刷対象一覧情報リスト
 *
 * @param selectedRirekiData - 画面.利用者一覧で選択した利用者リスト配下の履歴情報リスト
 *
 * @param printIdList -  帳票ID再設定処理 帳票IDリスト
 */
const doHukusuuSetting = <
  K extends IKikanRirekiInfo,
  I extends IInitMasterInfo,
  D extends IDbNoSaveData,
  H extends HistoryInfo,
  P extends PrtHistoryInfo,
  R extends IPrintInfo,
>(
  prtData: R,
  historyData: H[],
  orX0117OnewayType: OrX0117OnewayType,
  selectMode: string,
  selectedUserList: OrX0130TableType[],
  printOption: PrintOptionInfo<I>,
  dbNoSaveData: D,
  appYmd: string,
  newPrtData: P[],
  selectedRirekiData: K[],
  printIdList?: string[]
) => {
  let historyList: OrX0117History[] = []
  // 履歴選択が「複数」の場合、0：履歴選択複数
  // 利用者選択が「複数」の場合、1：利用者選択複数
  orX0117OnewayType.type = selectMode === OrX0130Const.DEFAULT.HUKUSUU ? '1' : '0'
  // 利用者選択が「複数」の場合
  if (selectMode === OrX0130Const.DEFAULT.HUKUSUU) {
    historyList = newPrtData.map((x, index) => {
      return {
        // 利用者名 履歴選択が「単一」の場合、戻り値印刷対象一覧情報リスト.利用者名
        userName: x.userName,
        // 結果内容 戻り値印刷対象一覧情報リスト.結果
        result: x.result,
        // 履歴日付 履歴選択が「単一」の場合、空文字
        historyDate: '',
        reportId: printIdList?.[index] ?? prtData.prtId,
        outputType: reportOutputType.DOWNLOAD,
        reportData: {
          // 印刷設定
          printSet: setPrintData(prtData),
          // 事業所名
          jigyoKnj: printOption.jigyoKnj,
          // システム日付
          appYmd: appYmd,
          // 初期設定マスタの情報
          initMasterObj: printOption.initMasterObj,
          // DB未保存画面項目
          dbNoSaveData: dbNoSaveData,
          // 印刷対象履歴
          printSubjectHistory: historyData[index],
        } as FormInfo<I, D, H>,
      }
    })
  } else {
    historyList = selectedRirekiData.map((x, index) => {
      return {
        // 利用者名 設定した印刷対象履歴リスト.利用者名
        userName: selectedUserList[0].nameKnj,
        // 結果内容 設定した印刷対象履歴リスト.結果
        result: '',
        // 作成日 設定した印刷対象履歴リスト.作成日
        historyDate: x.createYmd,
        reportId: printIdList?.[index] ?? prtData.prtId,
        outputType: reportOutputType.DOWNLOAD,
        reportData: {
          // 印刷設定
          printSet: setPrintData(prtData),
          // 事業所名
          jigyoKnj: printOption.jigyoKnj,
          // システム日付
          appYmd: appYmd,
          // 初期設定マスタの情報
          initMasterObj: printOption.initMasterObj,
          // DB未保存画面項目
          dbNoSaveData: dbNoSaveData,
          // 印刷対象履歴
          printSubjectHistory: historyData[index],
        } as FormInfo<I, D, H>,
      }
    })
  }
  orX0117OnewayType.historyList = historyList
}

/**
 * 画面印刷設定内容を保存
 *
 * @param titleInput - プロファイル
 *
 * @param inputData - 画面印刷設定内容
 *
 * @param apiName - API名
 *
 * @param items - 一覧に表示するデータのオブジェクト配列
 *
 * @param selectedId - 選択された印刷設定情報リストの帳票ID
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param showMessageBox - メッセージを表示
 *
 * @param closeDialog - ダイアログを閉じる
 */
const doPrintClose = async <C extends PrintCloseUpdateEntity<R>, R extends IPrintInfo>(
  titleInput: Mo00045Type | undefined,
  inputData: C,
  apiName: string,
  items: Mo01334Items[],
  selectedId: string,
  uniqueCpId: string,
  showMessageBox: (text: string, btn: string) => Promise<unknown>,
  closeDialog: () => void
) => {
  if (!titleInput) {
    // 閉じる
    void closeDialog()
    return
  } else if (!titleInput.value) {
    const dialogResult = await showMessageBox('message.e-cmn-20845', 'btn.ok')
    switch (dialogResult) {
      case 'yes': {
        for (const item of items) {
          if (item) {
            if (selectedId === item.id && item.mo01337OnewayReport) {
              const data = item.mo01337OnewayReport as Mo01337OnewayType
              titleInput.value = data.value
              inputData.prtList.find((x) => x.prtNo === selectedId)!.prtTitle = titleInput.value
            }
          }
        }
      }
    }
  }
  await ScreenRepository.update(apiName, inputData)
  // EditFlgの初期化
  initEditFlg(true, uniqueCpId)
  void closeDialog()
}

/**
 * 印刷設定情報
 *
 * @param prtData - 印刷設定情報リスト選択行
 */
const setPrintData = <R extends IPrintInfo>(prtData: R) => {
  return {
    // 帳票タイトル
    prtTitle: prtData.prtTitle,
    // セクション番号
    sectionNo: prtData.sectionNo,
    // 帳票No
    prtNo: prtData.prtNo,
    // プロファイル
    profile: prtData.profile,
    // 日付表示有無
    prnDate: prtData.prnDate,
    // 職員表示有無
    prnshoku: prtData.prnshoku,
    // パラメータ01
    param01: prtData.param01,
    // パラメータ02
    param02: prtData.param02,
    // パラメータ03
    param03: prtData.param03,
    // パラメータ04
    param04: prtData.param04,
    // パラメータ05
    param05: prtData.param05,
    // パラメータ06
    param06: prtData.param06,
    // パラメータ07
    param07: prtData.param07,
    // パラメータ08
    param08: prtData.param08,
    // パラメータ09
    param09: prtData.param09,
    // パラメータ10
    param10: prtData.param10,
    // パラメータ11
    param11: prtData.param11,
    // パラメータ12
    param12: prtData.param12,
    // パラメータ13
    param13: prtData.param13,
    // パラメータ14
    param14: prtData.param14,
    // パラメータ15
    param15: prtData.param15,
    // パラメータ16
    param16: prtData.param16,
    // パラメータ17
    param17: prtData.param17,
    // パラメータ18
    param18: prtData.param18,
    // パラメータ19
    param19: prtData.param19,
    // パラメータ20
    param20: prtData.param20,
    // パラメータ21
    param21: prtData.param21,
    // パラメータ22
    param22: prtData.param22,
    // パラメータ23
    param23: prtData.param23,
    // パラメータ24
    param24: prtData.param24,
    // パラメータ25
    param25: prtData.param25,
    // パラメータ26
    param26: prtData.param26,
    // パラメータ27
    param27: prtData.param27,
    // パラメータ28
    param28: prtData.param28,
    // パラメータ29
    param29: prtData.param29,
    // パラメータ30
    param30: prtData.param30,
    // パラメータ31
    param31: prtData.param31,
    // パラメータ32
    param32: prtData.param32,
    // パラメータ33
    param33: prtData.param33,
    // パラメータ34
    param34: prtData.param34,
    // パラメータ35
    param35: prtData.param35,
    // パラメータ36
    param36: prtData.param36,
    // パラメータ37
    param37: prtData.param37,
    // パラメータ38
    param38: prtData.param38,
    // パラメータ39
    param39: prtData.param39,
    // パラメータ40
    param40: prtData.param40,
    // パラメータ41
    param41: prtData.param41,
    // パラメータ42
    param42: prtData.param42,
    // パラメータ43
    param43: prtData.param43,
    // パラメータ44
    param44: prtData.param44,
    // パラメータ45
    param45: prtData.param45,
    // パラメータ46
    param46: prtData.param46,
    // パラメータ47
    param47: prtData.param47,
    // パラメータ48
    param48: prtData.param48,
    // パラメータ49
    param49: prtData.param49,
    // パラメータ50
    param50: prtData.param50,
  }
}

/**
 * ユーティリティを提供するフック
 */
export const usePrint = () => {
  return {
    doCheckBeforePrint,
    doReportOutput,
    doGetPrintData,
    doPrintClose,
    doUserClick,
    doRetryRirekiData,
    doPrintUpdate,
    doPrintGet,
    doHukusuuSetting,
  }
}
