import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 更新入力エンティティ
 */
export interface PlanDuplicateCarePlanIndividualCheckUpdateInEntity extends InWebEntity {
  /** 複写元事業者ID */
  svJigyoIdFrom: string
  /** 複写先事業者ID */
  svJigyoIdTo: string
  /** 職員ID */
  shokuinId?: string
  /** システムコード */
  gsysCd?: string
  /** 機能情報List */
  gdsKinouName?: string[]
}

/**
 * 更新出力エンティティ
 */
export interface PlanDuplicateCarePlanIndividualCheckUpdateOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: PlanDuplicateCarePlanIndividualCheckUpdateDataEntity
}

/**
 * 更新データエンティティ
 */
export interface PlanDuplicateCarePlanIndividualCheckUpdateDataEntity {
  /** チェック結果 */
  chkResult: string
  /** メッセージID */
  messageId: string
  /** 機能ID配列 */
  kinouId: string[]
  /** 機能ID毎の使用権限 */
  kinouSecUse: string[]
  /** 機能ID毎の更新権限 */
  kinouSecUpd: string[]
}
