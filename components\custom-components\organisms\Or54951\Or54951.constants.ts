/**
 * GUI01235_評価表
 *
 * @description
 * 評価表
 * 静的データ
 *
 * <AUTHOR>
 */

import { getSequencedCpId } from '#imports'

export namespace Or54951Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or54951', seq)

  export namespace DEFAULT {
    /**
     * 項目入力支援アイコンボタン「表示」対象コード
     */
    export const ICON_BUTTON_DISPLAY_LIST = [
      '0',
      '100',
      '101',
      '102',
      '103',
      '123',
      '124',
      '125',
      '127',
      '129',
      '300',
      '301',
      '302',
      '303',
      '304',
    ]

    /**
     * 項目入力支援アイコンボタン「非表示」対象コード
     */
    export const ICON_BUTTON_HIDE_LIST = [
      '120',
      '121',
      '122',
      '126',
      '128',
      '200',
      '201',
      '305',
      '500',
    ]

    /**
     * カレンダー「表示」対象コード
     */
    export const CALENDAR_DISPLAY_LIST = ['500']

    /**
     * カレンダー「非表示」対象コード
     */
    export const CALENDAR_HIDE_LIST = [
      '0',
      '100',
      '101',
      '102',
      '103',
      '120',
      '121',
      '122',
      '123',
      '124',
      '125',
      '126',
      '127',
      '128',
      '129',
      '200',
      '201',
      '300',
      '301',
      '302',
      '303',
      '304',
      '305',
    ]

    /**
     * 期間支援メニュー「表示」対象コード
     */
    export const PERIOD_SUPPORT_MENU_DISPLAY_LIST = ['120', '121', '126', '128']

    /**
     * 期間支援メニュー「非表示」対象コード
     */
    export const PERIOD_SUPPORT_MENU_HIDE_LIST = [
      '0',
      '100',
      '101',
      '102',
      '103',
      '122',
      '123',
      '124',
      '125',
      '127',
      '129',
      '200',
      '201',
      '300',
      '301',
      '302',
      '303',
      '304',
      '305',
      '500',
    ]

    /**
     * 罫線省略判定コード（内容が空白の場合）
     * 初期設定マスタ．内容が空白は罫線を省略フラグが「1:する」の場合に対象となるコード
     * 評価表上段様式リスト.連動区分が「100」「101」「102」「121」の場合
     */
    export const BORDER_SKIP_IF_EMPTY_LIST = ['100', '101', '102', '121']

    /**
     * 罫線省略判定コード（内容が同じの場合）
     * 初期設定マスタ．内容が同じは罫線を省略フラグが「1:する」の場合に対象となるコード
     * 評価表上段様式リスト.連動区分が「100」「101」「102」「121」の場合
     */
    export const BORDER_SKIP_IF_SAME_LIST = ['100', '101', '102', '121']

    /**
     * 計画書(2)取込時に各項目へ設定する連動区分コード
     * 評価表上段様式リスト.連動区分が102の場合 → 短期
     * 評価表上段様式リスト.連動区分が103の場合 → 介護
     * 評価表上段様式リスト.連動区分が121の場合 → 短期期間
     * 評価表上段様式リスト.連動区分が123の場合 → 担当
     * 評価表上段様式リスト.連動区分が124の場合 → 事業所名称
     */
    export const RENDOUKBN_PLAN_IMPORT_MAP = {
      TANKI: '102', // 短期
      KAIGO: '103', // 介護
      TAN_KIKAN_KNJ: '121', // 短期期間
      TANTO: '123', // 担当
      JIGYO_NAME: '124', // 事業所名称
    }
    /**
     * 計画書ボタン非表示対象の連動区分コード
     * 評価表上段様式リスト.連動区分が100 ~ 103 , 120 ~ 126 , 200 ~ 201の場合
     */
    export const PLAN_BOOK_HIDE_RENDOUKBN_LIST = [
      '100',
      '101',
      '102',
      '103',
      '120',
      '121',
      '122',
      '123',
      '124',
      '125',
      '126',
      '200',
      '201',
    ]

    /**
     * テーブルデータ入力区分:文章
     */
    export const INPUT_KBN_TEXT = '1'

    /**
     * テーブルデータ入力区分:数値
     */
    export const INPUT_KBN_NUMERIC = '2'

    /**
     * テーブルデータ入力区分:マスタ
     */
    export const INPUT_KBN_MASTER = '3'

    /**
     * テーブルデータ入力区分:マスタ+文章
     */
    export const INPUT_KBN_MASTER_TEXT = '4'

    /**
     * テーブルデータ入力区分:日付
     */
    export const INPUT_KBN_DATE = '5'

    /**
     * 開閉フラグ
     */
    export const IS_OPEN = false

    /**
     * 選択子：複写モード以外のテーブルコンテンツを取得する
     */
    export const TABLE_WRAPPER_SELECTOR = 'div>.v-table>.v-table__wrapper'
    /**
     * 選択子：複写モード以外のテーブルコンテンツを取得する
     */
    export const TABLE_WRAPPER_SELECTOR_DUPLICATE =
      '.dupicate-content .h-100>div>.v-table>.v-table__wrapper'

    /**
     * 入力支援返却値タイプ:追加
     */
    export const INPUT_SUPPORT_TYPE_ADD = '0'

    /**
     * 入力支援返却値タイプ：上書き
     */
    export const INPUT_SUPPORT_TYPE_OVERWRITE = '1'

    /**
     * 画面表示モード：複写
     */
    export const SCREEN_DISPLAY_MODE_COPY = 'copy'
  }
}
