import type { CareCertificationItem } from '../Or29241/Or29241.type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'

/**
 * OrCD002:有機体:介護認定項目
 * OneWayBind領域に保持するデータ構造
 */
export interface Or30269Type {
  /**
   * データリスト
   */
  items: ChekListItemType[]
}

/**
 * 介護認定一覧
 */
export interface concreteContentsCheckList {
  /**
   * ChekListItemType
   */
  items: ChekListItemType[]
}

/**
 * 介護認定項目
 */
export interface ChekListItemType {
  /**
   * planCheck
   */
  planCheck: Mo00018Type
  /**
   * statusCheck
   */
  statusCheck: Mo00018Type
  /**
   * planValue
   */
  planValue: string
  /**
   * statusValue
   */
  statusValue: string
  /**
   * label
   */
  label: string
}

/**
 * 介護認定一覧
 */
export interface CareCertificationList {
  /** セクション */
  sectionList: {
    /** セクションタイトル */
    sectionTitle: string
    sectionItems: CareCertificationItem[]
  }[]
  /**
   * 詳細ダイアログ情報の内容です
   */
  basicMotionDetailList?: CodeType[]
}

/**
 * 介護認定一覧
 */
export interface SectionList {
  /**
   *sectionList
   */
  sectionList: {
    /**
     * value
     */
    value: string
    /**
     * sectionTitle
     */
    sectionTitle: string
    /**
     * sectionItems
     */
    sectionItems: {
      itemType: string
      check: { modelValue: boolean }
      value: string
      tooltip: string
      label: string
      itemName: string
    }
  }[]
}

/**
 * SectionItem
 */
export interface SectionItem {
  /**
   * itemType
   */
  itemType: string
  /**
   * check
   */
  check: {
    /**
     * modelValue
     */
    modelValue: boolean
  }
  /**
   * value
   */
  value: string
  /**
   * itemName
   */
  itemName: string
  /**
   * tooltip
   */
  tooltip?: string // 可选属性
  /**
   * label
   */
  label: string
}

/**
 * Section
 */
export interface Section {
  /**
   * value
   */
  value: string
  /**
   * sectionTitle
   */
  sectionTitle: string
  /**
   * sectionItems
   */
  sectionItems: SectionItem[]
}
