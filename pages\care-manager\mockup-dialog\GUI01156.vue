<script setup lang="ts">
/**
 * GUI01156_利用票予定→実績変換画面
 *
 * @description
 * 利用票予定→実績変換画面
 *
 * <AUTHOR> 董永強
 */
import { computed, ref, onMounted, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or35687Const } from '~/components/custom-components/organisms/Or35687/Or35687.constants'
import { Or35687Logic } from '~/components/custom-components/organisms/Or35687/Or35687.logic'
import type {
  Or35687Type,
  Or35687OnewayType,
  UsingSlipDetailInfo,
} from '~/types/cmn/business/components/Or35687Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01156'
// ルーティング
const routing = 'GUI01156/pinia'
// 画面物理名
const screenName = 'GUI01156'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or35687 = ref({ uniqueCpId: Or35687Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01156' },
})

onMounted(() => {
  or35687Type.value.usingSlipDetailInfo.push({
    dmyJigyoNameKnj: 'サービス事業者1',
    dmyFormalnameKnj: 'サービス内容1',
    dmy0SvTani: '1',
    dmy0SvTanj: '0',
    dmyRHasu: '1',
    svTani: '1',
    dmySvTaniVisible: '1',
    dmyOneOfMonth: '1',
    svtype: '13',
    svcode: '1001',
    scode: '1',
    svStartTime: { value: '01:00' },
    svEndTime: { value: '02:00' },
    dmySeqNo: '1',
    yDay01: { value: '1' },
    yDay02: { value: '1' },
    yDay03: { value: '1' },
    yDay04: { value: '1' },
    yDay05: { value: '1' },
    yDay06: { value: '1' },
    yDay07: { value: '1' },
    yDay08: { value: '1' },
    yDay09: { value: '1' },
    yDay10: { value: '1' },
    yDay11: { value: '1' },
    yDay12: { value: '1' },
    yDay13: { value: '1' },
    yDay14: { value: '1' },
    yDay15: { value: '1' },
    yDay16: { value: '1' },
    yDay17: { value: '1' },
    yDay18: { value: '1' },
    yDay19: { value: '1' },
    yDay20: { value: '1' },
    yDay21: { value: '1' },
    yDay22: { value: '1' },
    yDay23: { value: '1' },
    yDay24: { value: '1' },
    yDay25: { value: '1' },
    yDay26: { value: '1' },
    yDay27: { value: '1' },
    yDay28: { value: '1' },
    yDay29: { value: '1' },
    yDay30: { value: '1' },
    yDay31: { value: '1' },
    yTotal: '100',
    yRentalF: { modelValue: '0' },
    shoukiboKbn: '1',
    yOv30Fl: '1',
    jDay01: { value: '1' },
    jDay02: { value: '1' },
    jDay03: { value: '1' },
    jDay04: { value: '1' },
    jDay05: { value: '1' },
    jDay06: { value: '1' },
    jDay07: { value: '1' },
    jDay08: { value: '1' },
    jDay09: { value: '1' },
    jDay10: { value: '1' },
    jDay11: { value: '1' },
    jDay12: { value: '1' },
    jDay13: { value: '1' },
    jDay14: { value: '1' },
    jDay15: { value: '1' },
    jDay16: { value: '1' },
    jDay17: { value: '1' },
    jDay18: { value: '1' },
    jDay19: { value: '1' },
    jDay20: { value: '1' },
    jDay21: { value: '1' },
    jDay22: { value: '1' },
    jDay23: { value: '1' },
    jDay24: { value: '1' },
    jDay25: { value: '1' },
    jDay26: { value: '1' },
    jDay27: { value: '1' },
    jDay28: { value: '1' },
    jDay29: { value: '1' },
    jDay30: { value: '1' },
    jDay31: { value: '1' },
    jTotal: '200',
    jRentalF: { modelValue: '0' },
    jOv30Fl: '1',
    tensouTime: '202/06/01',
    edaBack: '',
    sortNo: '1',
    henkouTime: '2025/05/01',
    tensouFl: '1',
    fygId: '1',
    max: '99',
    yoteiZumiFlg: { modelValue: false },
    jissekiZumiFlg: { modelValue: false },
    gouseiSikKbn: '1',
    genTaiKbn: '1',
    oyaLineNo: '1',
    santeiTani: '',
    kikanJiki: '',
    kaisuNisu: '',
    tenkintype: '',
    kiteiSu: '',
    yAmountTotal: '200',
    jAmountTotal: '300',
    shienId: '1',
    userid: '1',
    svJigyoId: '1',
    svItemCd: '3658',
    yymmYm: '2025/01',
    yymmD: '2025/01/01',
    edaNo: '1',
    termid: '6',
    riyouListUpdKbn: '',
    dmyStartTime: '2025/01/01',
    dmyEndTime: '2025/12/31',
    cmpOyaLineNo: '1',
    dmyBgColorY: [
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
    ],
    dmyBgColorJ: [
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
    ],
    modifiedCnt: '1',
    schedChangeFlg: false,
    schedChangeValue: 0,
    achvChangeFlg: false,
    achvChangeValue: 0,
    yRentalFName: '通常',
    jRentalFName: '通常',
  })
  or35687Type.value.usingSlipDetailInfo.push({
    dmyJigyoNameKnj: 'サービス事業者1',
    dmyFormalnameKnj: 'サービス内容1',
    dmy0SvTani: '1',
    dmy0SvTanj: '0',
    dmyRHasu: '1',
    svTani: '1',
    dmySvTaniVisible: '1',
    dmyOneOfMonth: '1',
    svtype: '13',
    svcode: '1001',
    scode: '1',
    svStartTime: { value: '01:00' },
    svEndTime: { value: '02:00' },
    dmySeqNo: '1',
    yDay01: { value: '1' },
    yDay02: { value: '1' },
    yDay03: { value: '1' },
    yDay04: { value: '1' },
    yDay05: { value: '1' },
    yDay06: { value: '1' },
    yDay07: { value: '1' },
    yDay08: { value: '1' },
    yDay09: { value: '1' },
    yDay10: { value: '1' },
    yDay11: { value: '1' },
    yDay12: { value: '1' },
    yDay13: { value: '1' },
    yDay14: { value: '1' },
    yDay15: { value: '1' },
    yDay16: { value: '1' },
    yDay17: { value: '1' },
    yDay18: { value: '1' },
    yDay19: { value: '1' },
    yDay20: { value: '1' },
    yDay21: { value: '1' },
    yDay22: { value: '1' },
    yDay23: { value: '1' },
    yDay24: { value: '1' },
    yDay25: { value: '1' },
    yDay26: { value: '1' },
    yDay27: { value: '1' },
    yDay28: { value: '1' },
    yDay29: { value: '1' },
    yDay30: { value: '1' },
    yDay31: { value: '1' },
    yTotal: '100',
    yRentalF: { modelValue: '0' },
    shoukiboKbn: '1',
    yOv30Fl: '1',
    jDay01: { value: '1' },
    jDay02: { value: '1' },
    jDay03: { value: '1' },
    jDay04: { value: '1' },
    jDay05: { value: '1' },
    jDay06: { value: '1' },
    jDay07: { value: '1' },
    jDay08: { value: '1' },
    jDay09: { value: '1' },
    jDay10: { value: '1' },
    jDay11: { value: '1' },
    jDay12: { value: '1' },
    jDay13: { value: '1' },
    jDay14: { value: '1' },
    jDay15: { value: '1' },
    jDay16: { value: '1' },
    jDay17: { value: '1' },
    jDay18: { value: '1' },
    jDay19: { value: '1' },
    jDay20: { value: '1' },
    jDay21: { value: '1' },
    jDay22: { value: '1' },
    jDay23: { value: '1' },
    jDay24: { value: '1' },
    jDay25: { value: '1' },
    jDay26: { value: '1' },
    jDay27: { value: '1' },
    jDay28: { value: '1' },
    jDay29: { value: '1' },
    jDay30: { value: '1' },
    jDay31: { value: '1' },
    jTotal: '200',
    jRentalF: { modelValue: '0' },
    jOv30Fl: '1',
    tensouTime: '202/06/01',
    edaBack: '',
    sortNo: '1',
    henkouTime: '2025/05/01',
    tensouFl: '1',
    fygId: '1',
    max: '99',
    yoteiZumiFlg: { modelValue: false },
    jissekiZumiFlg: { modelValue: false },
    gouseiSikKbn: '1',
    genTaiKbn: '1',
    oyaLineNo: '1',
    santeiTani: '',
    kikanJiki: '',
    kaisuNisu: '',
    tenkintype: '',
    kiteiSu: '',
    yAmountTotal: '200',
    jAmountTotal: '300',
    shienId: '1',
    userid: '1',
    svJigyoId: '1',
    svItemCd: '3658',
    yymmYm: '2025/01',
    yymmD: '2025/01/01',
    edaNo: '1',
    termid: '6',
    riyouListUpdKbn: '',
    dmyStartTime: '2025/01/01',
    dmyEndTime: '2025/12/31',
    cmpOyaLineNo: '1',
    dmyBgColorY: [
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
    ],
    dmyBgColorJ: [
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
    ],
    modifiedCnt: '1',
    schedChangeFlg: false,
    schedChangeValue: 0,
    achvChangeFlg: false,
    achvChangeValue: 0,
    yRentalFName: '通常',
    jRentalFName: '通常',
  })
  or35687Type.value.usingSlipDetailInfo.push({
    dmyJigyoNameKnj: 'サービス事業者1',
    dmyFormalnameKnj: 'サービス内容1',
    dmy0SvTani: '1',
    dmy0SvTanj: '0',
    dmyRHasu: '1',
    svTani: '1',
    dmySvTaniVisible: '1',
    dmyOneOfMonth: '1',
    svtype: '13',
    svcode: '1001',
    scode: '2',
    svStartTime: { value: '01:00' },
    svEndTime: { value: '02:00' },
    dmySeqNo: '1',
    yDay01: { value: '1' },
    yDay02: { value: '1' },
    yDay03: { value: '1' },
    yDay04: { value: '1' },
    yDay05: { value: '1' },
    yDay06: { value: '1' },
    yDay07: { value: '1' },
    yDay08: { value: '1' },
    yDay09: { value: '1' },
    yDay10: { value: '1' },
    yDay11: { value: '1' },
    yDay12: { value: '1' },
    yDay13: { value: '1' },
    yDay14: { value: '1' },
    yDay15: { value: '1' },
    yDay16: { value: '1' },
    yDay17: { value: '1' },
    yDay18: { value: '1' },
    yDay19: { value: '1' },
    yDay20: { value: '1' },
    yDay21: { value: '1' },
    yDay22: { value: '1' },
    yDay23: { value: '1' },
    yDay24: { value: '1' },
    yDay25: { value: '1' },
    yDay26: { value: '1' },
    yDay27: { value: '1' },
    yDay28: { value: '1' },
    yDay29: { value: '1' },
    yDay30: { value: '1' },
    yDay31: { value: '1' },
    yTotal: '100',
    yRentalF: { modelValue: '0' },
    shoukiboKbn: '1',
    yOv30Fl: '1',
    jDay01: { value: '1' },
    jDay02: { value: '1' },
    jDay03: { value: '1' },
    jDay04: { value: '1' },
    jDay05: { value: '1' },
    jDay06: { value: '1' },
    jDay07: { value: '1' },
    jDay08: { value: '1' },
    jDay09: { value: '1' },
    jDay10: { value: '1' },
    jDay11: { value: '1' },
    jDay12: { value: '1' },
    jDay13: { value: '1' },
    jDay14: { value: '1' },
    jDay15: { value: '1' },
    jDay16: { value: '1' },
    jDay17: { value: '1' },
    jDay18: { value: '1' },
    jDay19: { value: '1' },
    jDay20: { value: '1' },
    jDay21: { value: '1' },
    jDay22: { value: '1' },
    jDay23: { value: '1' },
    jDay24: { value: '1' },
    jDay25: { value: '1' },
    jDay26: { value: '1' },
    jDay27: { value: '1' },
    jDay28: { value: '1' },
    jDay29: { value: '1' },
    jDay30: { value: '1' },
    jDay31: { value: '1' },
    jTotal: '200',
    jRentalF: { modelValue: '0' },
    jOv30Fl: '1',
    tensouTime: '202/06/01',
    edaBack: '',
    sortNo: '1',
    henkouTime: '2025/05/01',
    tensouFl: '1',
    fygId: '1',
    max: '99',
    yoteiZumiFlg: { modelValue: false },
    jissekiZumiFlg: { modelValue: false },
    gouseiSikKbn: '3',
    genTaiKbn: '1',
    oyaLineNo: '1',
    santeiTani: '',
    kikanJiki: '',
    kaisuNisu: '',
    tenkintype: '',
    kiteiSu: '',
    yAmountTotal: '200',
    jAmountTotal: '300',
    shienId: '1',
    userid: '1',
    svJigyoId: '1',
    svItemCd: '3658',
    yymmYm: '2025/01',
    yymmD: '2025/01/01',
    edaNo: '1',
    termid: '6',
    riyouListUpdKbn: '',
    dmyStartTime: '2025/01/01',
    dmyEndTime: '2025/12/31',
    cmpOyaLineNo: '1',
    dmyBgColorY: [
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
    ],
    dmyBgColorJ: [
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
      '#fff0f1',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#ffffff',
      '#eff8ff',
    ],
    modifiedCnt: '1',
    schedChangeFlg: false,
    schedChangeValue: 0,
    achvChangeFlg: false,
    achvChangeValue: 0,
    yRentalFName: '通常',
    jRentalFName: '通常',
  })

  jsonInput.value = JSON.stringify(or35687Type.value, null, 2);
})



/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or29520Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or35687.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01156',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or35687Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or35687Const.CP_ID(1)]: or35687.value,
})

// ダイアログ表示フラグ
const showDialogOr35687 = computed(() => {
  // Or35687のダイアログ開閉状態
  return Or35687Logic.state.get(or35687.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or35687)
 */
function onClickOr35687() {
  // Or35687のダイアログ開閉状態を更新する
  Or35687Logic.state.set({
    uniqueCpId: or35687.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const or35687Type = ref<Or35687Type>({
  /** 処理フラグ */
  processFlg: '',
  /** 利用票予定実績リスト一覧明細*/
  usingSlipDetailInfo: [] as UsingSlipDetailInfo[],
})

const or35687Data: Or35687OnewayType = {
  /** 対象期間 */
  targetRange: '2025/05',
  /** 指定された行 */
  selectedIndex: 0,
  /** 開始日 */
  startDay: '',
  /** 終了日 */
  endDay: '',
}

const local = reactive({
  targetRange: { value: '2025/05' } as Mo00045Type,
  selectedIndex: { value: '0' } as Mo00045Type,
  startDay: { value: '' } as Mo00045Type,
  endDay: { value: '' } as Mo00045Type,
})

const jsonInput = ref('');

const updateObject = () => {
  const parsed = JSON.parse(jsonInput.value) as Or35687Type;
  if (parsed) {
    or35687Type.value = parsed;
  }
};
function convertToNumber(str:string) {
    const numValue = Number(str);
    return isNaN(numValue) ? 0 : numValue;
}
const jsonString = computed(() => {
  return JSON.stringify(or35687Type.value, null, 2);
});
function onClick() {
  or35687Data.targetRange = local.targetRange.value
  or35687Data.selectedIndex = convertToNumber(local.selectedIndex.value)
  or35687Data.startDay = local.startDay.value
  or35687Data.endDay = local.endDay.value
  Or35687Logic.state.set({
    uniqueCpId: or35687.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr35687"
        >GUI01156_利用票予定→実績変換
      </v-btn>
      <g-custom-or35687
        v-if="showDialogOr35687"
        v-bind="or35687"
        v-model="or35687Type"
        :oneway-model-value="or35687Data"
      />
    </c-v-col>
  </c-v-row>
    <c-v-row no-gutters>
       <c-v-col>
         <v-btn variant="plain"
           >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
         </v-btn>
       </c-v-col>
     </c-v-row>
        <c-v-row no-gutters>
            <div style="margin-left: 20px">対象期間</div>
     <div style="margin-left: 20px; width: 200px">
       <base-mo00045
         v-model="local.targetRange"
         :oneway-model-value="mo00045OneWay"
       ></base-mo00045>
     </div>
     <div style="margin-left: 20px">指定された行</div>
     <div style="margin-left: 20px; width: 200px">
       <base-mo00045
         v-model="local.selectedIndex"
         :oneway-model-value="mo00045OneWay"
       ></base-mo00045>
     </div>
    <div style="margin-left: 20px">開始日</div>
     <div style="margin-left: 20px; width: 200px">
       <base-mo00045
         v-model="local.startDay"
         :oneway-model-value="mo00045OneWay"
       ></base-mo00045>
     </div>
     <div style="margin-left: 20px">終了日</div>
     <div style="margin-left: 20px; width: 200px">
       <base-mo00045
         v-model="local.endDay"
         :oneway-model-value="mo00045OneWay"
       ></base-mo00045>
     </div>
    </c-v-row>
      <!-- 2. JSON編集エリア -->
      <div class="json-input-area">
        <h3>JSON編集</h3>
        <textarea
          v-model="jsonInput"
          placeholder="JSONを入力してください"
          rows="10"
        ></textarea>
        <v-btn  @click="updateObject">
          確定して更新JSON編集
        </v-btn>
      </div>
           <div class="pl-2 pt-5">
       <v-btn class="update-btn"  @click="onClick()"> GUI01156 疎通起動 </v-btn>
     </div>
     <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↓前画面へ返却値--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
     <div>
      <pre>{{ jsonString }}</pre>
    </div>
</template>
<style>
.json-input-area textarea {
  width: 1600px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: monospace;
}

.update-btn {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.update-btn:hover {
  background-color: #359e75;
}
</style>

