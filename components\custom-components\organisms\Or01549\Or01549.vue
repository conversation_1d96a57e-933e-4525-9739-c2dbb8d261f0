<script setup lang="ts">
/**
 * Or01549_（予防基本）コンテンツエリア
 * GUI01067_基本情報
 *
 * @description
 * （予防基本）コンテンツエリア
 *
 *
 * <AUTHOR> HOANG SY TOAN
 */
import { isUndefined } from 'lodash'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrD2002Logic } from '../OrD2002/OrD2002.logic'
import { OrX0007Const } from '../OrX0007/OrX0007.constants'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0008Const } from '../OrX0008/OrX0008.constants'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { OrX0009Logic } from '../OrX0009/OrX0009.logic'
import { OrX0010Const } from '../OrX0010/OrX0010.constants'
import { OrD2002Const } from '../OrD2002/OrD2002.constants'
import { OrX0010Logic } from '../OrX0010/OrX0010.logic'
import { Or08207Const } from '../Or08207/Or08207.constants'
import { OrX0008Logic } from '../OrX0008/OrX0008.logic'
import type { BasicInfo } from '../Or08207/Or08207.type'
import { Or31728Logic } from '../Or31728/Or31728.logic'
import { Or31728Const } from '../Or31728/Or31728.constants'
import { Or10443Const } from '../Or10443/Or10443.constants'
import { Or10443Logic } from '../Or10443/Or10443.logic'
import { Or27631Const } from '../Or27631/Or27631.constants'
import { Or27631Logic } from '../Or27631/Or27631.logic'
import { Or01549Const } from './Or01549.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import type { OrX0006OnewayType } from '~/types/cmn/business/components/OrX0006Type'
import type { OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import type { OrX0008OnewayType } from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import type { Or10443OnewayType } from '~/types/cmn/business/components/Or10443Type'
import type { Or27631Type } from '~/types/cmn/business/components/Or27631Type'
import { Or08467Const } from '~/components/custom-components/organisms/Or08467/Or08467.constants'
import { Or08467Logic } from '~/components/custom-components/organisms/Or08467/Or08467.logic'
import { Or08097Const } from '~/components/custom-components/organisms/Or08097/Or08097.constants'
import { Or08097Logic } from '~/components/custom-components/organisms/Or08097/Or08097.logic'
import { Or08468Const } from '~/components/custom-components/organisms/Or08468/Or08468.constants'
import { Or08468Logic } from '~/components/custom-components/organisms/Or08468/Or08468.logic'

import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import {
  useScreenInitFlg,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
  useScreenUtils,
} from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { PlanTargetPeriodDataType } from '~/types/cmn/PlanTargetPeriodDataType'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import { CustomClass } from '~/types/CustomClassType'
import type {
  preventionBasicInitialInfoSelectInEntity,
  preventionBasicInitialInfoSelectOutEntity,
} from '~/repositories/cmn/entities/preventionBasicInitialInfoSelectEntity'
import type {
  PlanPeriodInfo,
  preventionBasicPeriodSelectInEntity,
  preventionBasicPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/preventionBasicPeriodSelectEntity'
import type {
  PreventiveHistoryInfo,
  preventionBasicHistorySelectInEntity,
  preventionBasicHistorySelectOutEntity,
} from '~/repositories/cmn/entities/preventionBasicHistorySelectEntity'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type {
  preventionBasicDetailInfoSelectInEntity,
  preventionBasicDetailInfoSelectOutEntity,
  PreventiveCareInfo,
  MedicalHistoryInfo,
  FamilyStructureInfo,
} from '~/repositories/cmn/entities/preventionBasicDetailInfoSelectEntity'
import type {
  DailyScheduleSelectInEntity,
  DailyScheduleSelectOutEntity,
  getDailyScheduleList,
} from '~/repositories/cmn/entities/DailyScheduleSelectEntity'
import type {
  Gui01067BasicInfoListInEntity,
  PreventionBasicDetailInfoUpdateInEntity,
} from '~/repositories/cmn/entities/PreventionBasicDetailInfoUpdateEntity'
import type {
  HistorySelectInfoType,
  Or10929OnewayType,
} from '~/types/cmn/business/components/Or10929Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or10929Const } from '~/components/custom-components/organisms/Or10929/Or10929.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { useUserListInfo } from '~/utils/useUserListInfo'
import type { Or31728Type } from '~/types/cmn/business/components/Or31728Type'
import { useJigyoList } from '~/utils/useJigyoList'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'

const { syscomUserListFunc } = useUserListInfo()
const { t } = useI18n()

/**
 * プロパティ
 */
interface Props {
  uniqueCpId: string
}
const props = defineProps<Props>()
const isPermissionPrint = ref<boolean>()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
/**
 * 画面のサイズを設定する
 */
const { setChildCpBinds } = useScreenUtils()
const { jigyoListWatch } = useJigyoList()

const or08207 = ref({ uniqueCpId: '' })
const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const orHeadLine = ref({ uniqueCpId: '' })
const OrX0007 = ref({ uniqueCpId: '' })
const orX0008_1 = ref({ uniqueCpId: '' })
const orX0009_1 = ref({ uniqueCpId: '' })
const orX0010 = ref({ uniqueCpId: '' })
const orX0010_1 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(1) })
const orD2002_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(1) })
const or10929 = ref({ uniqueCpId: '' })
const or31728 = ref({ uniqueCpId: '' })
const or10443 = ref({ uniqueCpId: '' })
const or41179 = ref({ uniqueCpId: '' })
const or08467 = ref({ uniqueCpId: Or08467Const.CP_ID(1) })
const or08097 = ref({ uniqueCpId: Or08097Const.CP_ID(1) })
const or08468 = ref({ uniqueCpId: Or08468Const.CP_ID(1) })

const orX0007Ref = ref()

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(or08207.value.uniqueCpId)
})
// 画面状態管理用操作変数
const screenStore = useScreenStore()

const commonInfo = {
  baseDate: '',
  loginUserName: '管理者　太郎',
  sc1Id: '',
  assessmentId: '',
  shokuId: '',
  surveyAssessmentKind: '',
  showMessageFlg: 1,
  serviceType: '50010',
  userId: systemCommonsStore.getUserId,
  kycFlg: systemCommonsStore.getFunctionCategory,
  houjinId: systemCommonsStore.getHoujinId,
  shisetsuId: systemCommonsStore.getShisetuId,
  svJigyoId: systemCommonsStore.getSvJigyoId,
}

// 選択中利用者ID
const userId = ref('')

// 計画期間表示フラグ
const planPeriodShow = ref<boolean>(true)

// 履歴表示フラグ
const historyShow = ref<boolean>(true)

// 作成者表示フラグ
const authorShow = ref<boolean>(true)

// 相談日表示フラグ
const consultationDateShow = ref<boolean>(true)

// 入力フォーム表示フラグ
const inputBoomShow = ref<boolean>(true)

// 削除フラグ
const deleteFlag = ref<boolean>(false)

// データ再取得フラグ
const retrieveCmCp1DataFlg = ref<boolean>(false)

// データ再取得フラグ
const retrieveCmCp2DataFlg = ref<boolean>(false)

// 地域キー
const regionKey = ref(Or00248Const.DEFAULT.REGION_KEY)

const local = reactive({
  consultationDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  mo00043: {
    id: '',
  } as Mo00043Type,
  flag: {
    periodManage: '0',
    periodManageRegistration: false,
  },
  addBtnState: false,
  officeId: '1',
  planPeriodId: 1,
  planPeriod: [] as PlanPeriodInfo[],
  planPeriodIndex: 0,
  totalCountPlanPeriod: 0,
  historyId: 1,
  history: [] as PreventiveHistoryInfo[],
  historyIndex: 0,
  totalCountHistory: 0,
  degreeAbility: 5,
  houjinId: '',
  shisetuId: '',
  userId: '**********',
  svJigyoId: systemCommonsStore.getSvJigyoId,
  raiId: '',
  kijunbiYmd: '',
  sakuseiId: '',
  historyModifiedCnt: '',
  shisetsuId: '1',
  itkJigyoId: '1',
  shubetsuId: '1',
  sc1Id: '1',
  ks51Id: '1',
  khn11Id: '1',
  kikanPage: '0',
  historyPage: '0',
  kaigoList: [] as PreventiveCareInfo[],
  byourekiList: [] as MedicalHistoryInfo[],
  kozokuList: [] as FamilyStructureInfo[],
})

const defaultOneway = reactive({
  mo00043Oneway: {
    tabItems: [
      {
        id: 'basicInfo',
        title: t('label.basic_infomation'),
        tooltipText: t('label.basic_infomation'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'carePrevention',
        title: t('label.matters_related_to_care_prevention'),
        tooltipText: t('label.matters_related_to_care_prevention'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'currentMedical',
        title: t('label.current_medical_history_past_medical_history_and_progress'),
        tooltipText: t('label.current_medical_history_past_medical_history_and_progress'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'currentService',
        title: t('label.services_currently_being_used'),
        tooltipText: t('label.services_currently_being_used'),
        tooltipLocation: 'bottom',
      },
    ],
    tabClass: 'px-0',
  } as Mo00043OnewayType,
  mo00611Oneway: {
    name: 'consentInfoInputIconBtn',
    btnLabel: t('btn.consent-info'),
    width: '55px',
    minWidth: '55px',
    size: 'x-small',
    customClass: {
      itemClass: 'pa-0',
      itemStyle: '--v-btn-height:28px; height:128px; min-height:128px; padding:0 8px;',
    },
  },
  orX0006Oneway: {} as OrX0006OnewayType,
  OrX0007Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as OrX0007OnewayType,
  orX0008Oneway: {
    screenID: 'GUI01067',
  } as OrX0008OnewayType,
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
    deleteFlg: false,
    isDisabled: false,
  } as OrX0009OnewayType,
  orX0010Oneway: { deleteFlg: false, isDisabled: false } as OrX0010OnewayType,
})

const localOneway = reactive({
  mo00043Oneway: { ...defaultOneway.mo00043Oneway } as Mo00043OnewayType,
  mo00611Oneway: { ...defaultOneway.mo00611Oneway },
  orX0006Oneway: { ...defaultOneway.orX0006Oneway } as OrX0006OnewayType,
  OrX0007Oneway: { ...defaultOneway.OrX0007Oneway } as OrX0007OnewayType,
  orX0008Oneway: { ...defaultOneway.orX0008Oneway } as OrX0008OnewayType,
  orX0009Oneway: { ...defaultOneway.orX0009Oneway } as OrX0009OnewayType,
  orX0010Oneway: { ...defaultOneway.orX0010Oneway } as OrX0010OnewayType,
  consultationDateOneway: {
    itemLabel: t('label.consultation-date'),
    width: '170',
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    clearable: true,
    customClass: new CustomClass({ labelClass: 'ma-1' }),
  } as Mo00020OnewayType,
  or10929Oneway: {
    historySelectInfo: {} as HistorySelectInfoType,
  } as Or10929OnewayType,
  Or08468Oneway: {
    isAllowEdit: true,
  },
  Or08097Oneway: {
    isAllowEdit: true,
  },
})

// 基本情報
const isDisabledCopyBtn = ref<boolean>(false)

// 基本情報の履歴選択
const Or10443OnewayModel = ref<Or10443OnewayType>({
  userId: local.userId ?? '',
  sectionName: '',
  choIndex: '1',
  kikanFlg: local.flag.periodManage,
  historyId: local.historyId.toString(),
  careManagerInChargeSettingsFlag: 2,
  svJigyoId: local.svJigyoId ?? '',
  svJigyoIdList: [''],
  tantoShokuId: '0',
  khn11Id: local.khn11Id,
  kinounameKnj: '',
  kojinhogoFlg: '',
  sectionAddNo: '',
  sysCd: '',
  sysRyaku: '',
  houjinId: local.houjinId,
  shisetuId: local.shisetuId,
  shokuId: '',
  cpnFlg: '',
  shosikiFlg: '',
  tantoId: '',
  startYmd: '',
  endYmd: '',
  sectionNo: '',
  kycFlg: '',
  chiJigyoId: '',
})

const mo01338Oneway: Mo01338OnewayType = {
  value: Or01549Const.NOTIIFICATION_REGISTER_TIME,
  customClass: new CustomClass({ outerClass: 'ml-0 mr-1', itemClass: 'notice' }),
  itemLabelFontWeight: 'bold',
}

// 計画対象期間
const historyUpdateKbn = ref<string>(Or01549Const.HISTORY_UPDATE_KBN_UPDATE)

/**
 * コンポーネント固有処理
 */
const isInit = useScreenInitFlg()

onMounted(async () => {
  await syscomUserListFunc()
  if (systemCommonsStore.getUserSelectSelfId) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or01549Const.STR_ALL })
  })

  await initOr01549()
  if (isInit) {
    OrD2002Logic.state.set({
      uniqueCpId: orD2002_1.value.uniqueCpId,
      state: {
        mainMessage: t('message.i-cmn-10430'),
      },
    })
  }
  setupOr00248()
})

useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [OrX0007Const.CP_ID(1)]: OrX0007.value,
  [OrX0008Const.CP_ID(1)]: orX0008_1.value,
  [OrX0009Const.CP_ID(1)]: orX0009_1.value,
  [OrX0010Const.CP_ID(1)]: orX0010.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [OrD2002Const.CP_ID(1)]: orD2002_1.value,
  [Or10929Const.CP_ID(1)]: or10929.value,
  [Or08207Const.CP_ID(1)]: or08207.value,
  [Or31728Const.CP_ID(1)]: or31728.value,
  [Or10443Const.CP_ID(1)]: or10443.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
  [Or08467Const.CP_ID(1)]: or08467.value,
  [Or08097Const.CP_ID(1)]: or08097.value,
  [Or08468Const.CP_ID(1)]: or08468.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
})

const svJigyoChangeFlag = ref<boolean>(false)

/**
 * 事業所選択監視関数を実行
 */
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId !== '') {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const _jigyoInfo = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)
    svJigyoChangeFlag.value = true
  }
}

watch(
  () => svJigyoChangeFlag.value,
  async (newValue) => {
    if (newValue) {
      if (isEdit.value) {
        const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
          dialogTitle: t('label.confirm'),
          dialogText: t('message.i-cmn-10430'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'destroy1',
          secondBtnLabel: t('btn.no'),
          thirdBtnType: 'normal3',
          thirdBtnLabel: t('btn.cancel'),
        })

        switch (dialogResult) {
          case Or01549Const.DIALOG_RESULT_YES:
            await _save()
            await getBasicInitialInfoSelect()
            await getPreventionBasicPeriodSelect()
            await getPreventionBasicHistorySelect()
            await getPreventionBasicDetailInfoSelect()
            break
          case Or01549Const.DIALOG_RESULT_NO:
            await getBasicInitialInfoSelect()
            await getPreventionBasicPeriodSelect()
            await getPreventionBasicHistorySelect()
            await getPreventionBasicDetailInfoSelect()
            break
          case Or01549Const.DIALOG_RESULT_CANCEL:
            await getBasicInitialInfoSelect()
            await getPreventionBasicPeriodSelect()
            await getPreventionBasicHistorySelect()
            await getPreventionBasicDetailInfoSelect()
            svJigyoChangeFlag.value = false
            return
        }
      } else {
        await getBasicInitialInfoSelect()
        await getPreventionBasicPeriodSelect()
        await getPreventionBasicHistorySelect()
        await getPreventionBasicDetailInfoSelect()
      }
      svJigyoChangeFlag.value = false
    }
  }
)

Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.prevention-basic'),
    showFavorite: true,
    showViewSelect: false,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    viewSelectItems: [],
    showMasterBtn: true,
    showOptionMenuDelete: false,
    showSaveBtn: true,
    tooltipTextSaveBtn: t('tooltip.save'),
    tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
    tooltipTextPrintBtn: t('tooltip.care-plan2-print-setting-btn'),
    tooltipTextMasterBtn: t('tooltip.regular-master-icon'),
    tooltipTextOptionMenuBtn: '',
  },
})

const or27631 = ref({ uniqueCpId: Or27631Const.CP_ID(1) })
const or27631Type = ref<Or27631Type>({
  consentDate: { value: '' },
  consentName: { value: '' },
})

// Or08467のデータはOr08467Logic.dataを使用して管理

const or31728Type = ref<Or31728Type>({
  serviceType: '基本情報',
  periodManagementFlg: '管理する',
})

const showDialogOr27631 = computed(() => {
  return Or27631Logic.state.get(or27631.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr31728 = computed(() => {
  const state = Or31728Logic.state.get(or31728.value.uniqueCpId) as { isOpen?: boolean }
  return state?.isOpen ?? false
})

const showDialogOr10443 = computed(() => {
  const state = Or10443Logic.state.get(or10443.value.uniqueCpId) as { isOpen?: boolean }
  return state?.isOpen ?? false
})

/**
 * ユーザーがツールバーのボタンを押したタイミングを追跡する
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.favoriteEventFlg) {
      favoriteIconClick()
      setOr11871Event({ favoriteEventFlg: false })
    }

    if (newValue.saveEventFlg) {
      historyUpdateKbn.value = Or01549Const.HISTORY_UPDATE_KBN_UPDATE
      await _save()
      setOr11871Event({ saveEventFlg: false })
    }

    if (newValue.createEventFlg) {
      await handleClickCreate()
      setOr11871Event({ createEventFlg: false })
    }

    if (newValue.printEventFlg) {
      await _print()
      setOr11871Event({ printEventFlg: false })
    }

    if (newValue.masterEventFlg) {
      await _master()
      setOr11871Event({ masterEventFlg: false })
    }
  }
)

/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(OrX0007.value.uniqueCpId),
  (newValue) => {
    if (isUndefined(newValue)) {
      return
    }

    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg

    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }

    if (planUpdateFlg === '0') {
      void planTargetPeriodIconClick()
    } else if (planUpdateFlg === '1') {
      local.kikanPage = '1'
      OrX0007Logic.data.set({
        uniqueCpId: OrX0007.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.planPeriodId.toString(),
          PlanTargetPeriodUpdateFlg: '',
        },
      })

      retrieveCmCp1DataFlg.value = true
    } else if (planUpdateFlg === '2') {
      local.kikanPage = '2'
      OrX0007Logic.data.set({
        uniqueCpId: OrX0007.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.planPeriodId.toString(),
          PlanTargetPeriodUpdateFlg: '',
        },
      })

      retrieveCmCp1DataFlg.value = true
    } else if (planUpdateFlg === '3') {
      local.kikanPage = '0'
      local.planPeriodId = Number(
        OrX0007Logic.data.get(OrX0007.value.uniqueCpId)!.planTargetPeriodId
      )
      OrX0007Logic.data.set({
        uniqueCpId: OrX0007.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.planPeriodId.toString(),
          PlanTargetPeriodUpdateFlg: '',
        },
      })

      retrieveCmCp1DataFlg.value = true
    }
  },
  { deep: true }
)

/**
 * 履歴変更の監視
 */
watch(
  () => OrX0008Logic.data.get(orX0008_1.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    const createId = Number(newValue.createId)
    const createUpateFlg = newValue.createUpateFlg

    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }

    await nextTick()
    console.log(newValue)
    if (createUpateFlg === '0') {
      // OrX0008変更データから履歴情報を更新
      local.historyId = Number(newValue.createId)
      localOneway.orX0008Oneway.createData = {
        ...localOneway.orX0008Oneway.createData,
        createId: newValue.rirekiObj?.staffId ?? '',
        createDate: newValue.rirekiObj?.createDate ?? '',
        staffId: (newValue.rirekiObj?.staffId ?? '').toString(),
        staffName: newValue.rirekiObj?.staffName ?? '',
        currentIndex: newValue.rirekiObj?.currentIndex ?? 0,
        totalCount: newValue.rirekiObj?.totalCount ?? 0,
      }
      retrieveCmCp2DataFlg.value = true
    } else if (createUpateFlg === '1') {
      if (createId <= 1) {
        return
      }
      local.historyPage = '1'
      local.historyId = createId - 1

      if (localOneway.orX0008Oneway.createData?.currentIndex) {
        localOneway.orX0008Oneway.createData.currentIndex =
          localOneway.orX0008Oneway.createData.currentIndex - 1
      }

      OrX0008Logic.data.set({
        uniqueCpId: orX0008_1.value.uniqueCpId,
        value: {
          createId: local.historyId.toString(),
          createUpateFlg: '',
        },
      })

      retrieveCmCp2DataFlg.value = true
    } else if (createUpateFlg === '2') {
      local.historyPage = '2'
      local.historyId = createId + 1

      if (localOneway.orX0008Oneway.createData?.currentIndex) {
        localOneway.orX0008Oneway.createData.currentIndex =
          localOneway.orX0008Oneway.createData.currentIndex + 1
      }

      OrX0008Logic.data.set({
        uniqueCpId: orX0008_1.value.uniqueCpId,
        value: {
          createId: local.historyId.toString(),
          createUpateFlg: '',
        },
      })

      retrieveCmCp2DataFlg.value = true
    } else if (createUpateFlg === '3') {
      local.historyPage = '0'
      local.historyId = Number(OrX0008Logic.data.get(orX0008_1.value.uniqueCpId)!.createId)
      OrX0008Logic.data.set({
        uniqueCpId: orX0008_1.value.uniqueCpId,
        value: {
          createId: local.historyId.toString(),
          createUpateFlg: '',
        },
      })

      retrieveCmCp2DataFlg.value = true
    }
  },
  { deep: true }
)

watch(
  () => OrX0009Logic.data.get(orX0009_1.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // Cập nhật thông tin tác giả vào local state
      localOneway.orX0009Oneway.createData = {
        ...localOneway.orX0009Oneway.createData,
        createId: localOneway.orX0009Oneway.createData?.createId ?? 0,
        createDate: localOneway.orX0009Oneway.createData?.createDate ?? '',
        staffId: Number(newValue.staffId),
        staffName: newValue.staffName,
        currentIndex: localOneway.orX0009Oneway.createData?.currentIndex ?? 1,
        totalCount: localOneway.orX0009Oneway.createData?.totalCount ?? 1,
        ks21Id: localOneway.orX0009Oneway.createData?.ks21Id ?? '',
      }
    }
  },
  { deep: true }
)

/**
 * 利用者一覧における利用者の変更を監視
 */
watch(
  () => systemCommonsStore.getUserSelectSelfId(regionKey.value),
  async (newUserId, oldUserId) => {
    if (newUserId === undefined || newUserId === oldUserId) {
      return
    }

    if (isEdit.value) {
      const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
        dialogTitle: t('label.confirm'),
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case Or01549Const.DIALOG_RESULT_YES:
          await _save()
          await switchToNewUser(newUserId)
          break
        case Or01549Const.DIALOG_RESULT_NO:
          await switchToNewUser(newUserId)
          break
        case Or01549Const.DIALOG_RESULT_CANCEL:
          await revertUserSelection(oldUserId)
          return
      }
    } else {
      await switchToNewUser(newUserId)
    }
  },
  { immediate: false }
)

/**
 * 計画対象期間のイベントを受信
 */
watch(
  () => retrieveCmCp1DataFlg.value,
  async (newValue) => {
    if (newValue) {
      await getPreventionBasicPeriodSelect()
      await getPreventionBasicHistorySelect()
      await getTabsData()
      retrieveCmCp1DataFlg.value = false
    }
  }
)

/**
 * 履歴のイベントを受信
 */
watch(
  () => retrieveCmCp2DataFlg.value,
  async (newValue) => {
    if (newValue) {
      await getPreventionBasicHistorySelect()
      await getTabsData()
      retrieveCmCp2DataFlg.value = false
    }
  }
)

/**
 * Or08467のデータ変更を監視
 */
watch(
  () => Or08467Logic.data.get(or08467.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // kaigoListを更新
      if (local.kaigoList.length > 0) {
        local.kaigoList[0] = {
          ...local.kaigoList[0],
          koutekisvKnj: newValue.publicService ?? '',
          hikoutekisvKnj: newValue.nonPublicService ?? '',
        }
      }
    }
  },
  { deep: true }
)

/**
 * Or08097のデータ変更を監視
 */
watch(
  () => Or08097Logic.data.get(or08097.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // kaigoListを更新
      if (local.kaigoList.length > 0) {
        local.kaigoList[0] = {
          ...local.kaigoList[0],
          imamadeKnj: newValue.untilNowLife ?? '',
          shumiKnj: newValue.hobbyPleasureSpecialSkill ?? '',
          kankeiKnj: newValue.friendCommunityRelation ?? '',
          sugosikataKnj: newValue.dailyLifeHowToSpendYourTime ?? '',
        }
      }
    }
  },
  { deep: true }
)

/**
 * Or08468のデータ変更を監視
 */
watch(
  () => Or08468Logic.data.get(or08468.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // byourekiListを更新
      if (local.byourekiList.length > 0) {
        // Or08468のデータをbyourekiListにマッピング
        const medicalHistoryData =
          newValue.dataList?.map((item, index) => ({
            khn133Id: String(index + 1),
            khn11Id: local.khn11Id,
            byoymdKnj: item.kikanKnj?.value ?? '',
            byoumeiKnj: item.kadaiKnj?.value ?? '',
            iryokikanKnj: item.mokuhyoKnj?.value ?? '',
            ikenshoCd: item.attendingPhysicianOpinionAuthor ? '1' : '0',
            iryokikanTel: item.kadaiNo?.value ?? '',
            keikaKnj: item.tantoShokuKnj?.value ?? '',
            naiyoKnj: item.naiyoKnj?.value ?? '',
            seq: String(item.seq ?? index + 1),
          })) || []

        local.byourekiList = medicalHistoryData
      }
    }
  },
  { deep: true }
)

/**
 * 方法
 */

/**
 * 新しい利用者に切り替える
 *
 * @param newUserId - 切り替える利用者ID
 */
const switchToNewUser = async (newUserId: string) => {
  local.userId = newUserId
  userId.value = newUserId
  await getCommonData()
  await getTabsData()
}

/**
 * キャンセル時に利用者選択を元に戻す
 *
 * @param oldUserId - 元に戻す利用者ID
 */
const revertUserSelection = async (oldUserId: string | undefined) => {
  if (oldUserId) {
    await nextTick(() => {
      systemCommonsStore.setUserSelectSelfId(oldUserId, regionKey.value)
    })
  }
}

const getCommonData = async () => {
  await getBasicInitialInfoSelect()
  await getPreventionBasicPeriodSelect()
  await getPreventionBasicHistorySelect()
}

const getTabsData = async () => {
  await getPreventionBasicDetailInfoSelect()
}

/**
 * タブ切り替え時の処理（表示順序の復元）
 */
async function tabsClick() {
  if (isEdit.value) {
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      dialogTitle: t('label.confirm'),
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    await handleDialogResult(dialogResult)
  }
}

/**
 * AC001_初期表示
 */
const initOr01549 = async () => {
  if (commonInfo.showMessageFlg === 1 && commonInfo.serviceType === '50010') {
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        dialogTitle: t('label.top-btn-title'),
        dialogText: t('message.i-cmn-11276'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      },
    })
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
  isPermissionPrint.value = await hasPrintAuth(Or01549Const.LINK_AUTH)

  if (!isPermissionPrint.value) {
    Or11871Logic.state.set({
      uniqueCpId: or11871.value.uniqueCpId,
      state: {
        disabledPrintBtn: true,
      },
    })
  }

  await getCommonData()
  await getTabsData()
}

const getBasicInitialInfoSelect = async () => {
  const inputData: preventionBasicInitialInfoSelectInEntity = {
    shisetuId: local.shisetsuId,
    userId: local.userId,
    svJigyoId: local.svJigyoId ?? '',
    itkJigyoId: local.itkJigyoId,
    syubetsuId: local.shubetsuId,
  }

  const res: preventionBasicInitialInfoSelectOutEntity = await ScreenRepository.select(
    'preventionBasicInitialInfoSelect',
    inputData
  )
  const resData = res.data
  local.flag.periodManage = resData.kikanFlag
  planPeriodShow.value = resData.kikanFlag === Or01549Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT
  local.planPeriodId = Number(resData.kikanOutD[0].sc1Id)
}

const getPreventionBasicPeriodSelect = async () => {
  const inputData: preventionBasicPeriodSelectInEntity = {
    svJigyoId: local.svJigyoId ?? '',
    itkJigyoId: local.itkJigyoId,
    shisetuId: local.shisetsuId,
    userId: local.userId,
    syubetsuId: local.shubetsuId,
    sc1Id: local.planPeriodId.toString(),
    kikanPage: local.kikanPage,
    kikanFlag: local.flag.periodManage,
  }

  const res: preventionBasicPeriodSelectOutEntity = await ScreenRepository.select(
    'preventionBasicPeriodSelect',
    inputData
  )
  const { kikanObj } = res.data
  if (kikanObj.length === 0) {
    localOneway.OrX0007Oneway.planTargetPeriodData.currentIndex = 0
    localOneway.OrX0007Oneway.planTargetPeriodData.totalCount = 0
    localOneway.OrX0007Oneway.planTargetPeriodData.planTargetPeriodId = 0
    isDisabledCopyBtn.value = true
  } else {
    local.totalCountPlanPeriod = kikanObj.length
    local.planPeriodIndex = local.totalCountPlanPeriod - 1
    let firstData
    if (local.kikanPage === '2') {
      firstData = kikanObj.find((item) => parseInt(item.sc1Id) === local.planPeriodId + 1)
      firstData ??= kikanObj[0]
    } else if (local.kikanPage === '1') {
      firstData = kikanObj.find((item) => parseInt(item.sc1Id) === local.planPeriodId - 1)
      firstData ??= kikanObj[0]
    } else {
      firstData = kikanObj[0]
    }

    local.planPeriodId = parseInt(firstData.sc1Id)
    local.planPeriod = kikanObj

    localOneway.OrX0007Oneway.planTargetPeriodData = {
      planTargetPeriodId: parseInt(firstData.sc1Id),
      planTargetPeriod: firstData.startYmd + ' ~ ' + firstData.endYmd,
      currentIndex: local.planPeriodIndex + 1,
      totalCount: local.totalCountPlanPeriod,
    } as PlanTargetPeriodDataType

    isDisabledCopyBtn.value = false
  }

  const { historyObj } = res.data
  await processHistory(historyObj)
}
async function processHistory(historyObj: PreventiveHistoryInfo[]) {
  if (historyObj.length) {
    historyObj.sort((a, b) => parseInt(a.khn11Id) - parseInt(b.khn11Id))
    local.totalCountHistory = historyObj.length
    historyShow.value = true

    let selectedHistory
    if (local.historyPage === '1') {
      selectedHistory = historyObj.find((h) => parseInt(h.khn11Id) === parseInt(local.khn11Id) - 1)
    } else if (local.historyPage === '2') {
      selectedHistory = historyObj.find((h) => parseInt(h.khn11Id) === parseInt(local.khn11Id) + 1)
    } else {
      selectedHistory = historyObj[historyObj.length - 1]
    }
    selectedHistory ??= historyObj[historyObj.length - 1]

    local.historyId = parseInt(selectedHistory.shokuId)
    local.khn11Id = selectedHistory.khn11Id

    localOneway.orX0008Oneway.createData = {
      createId: String(selectedHistory.shokuId),
      createDate: systemCommonsStore.getSystemDate ?? '',
      staffId: '0',
      staffName: systemCommonsStore.getStaffId ?? '',
      currentIndex: historyObj.findIndex((h) => h.khn11Id === selectedHistory.khn11Id) + 1,
      totalCount: local.totalCountHistory,
    }

    localOneway.orX0009Oneway.createData = {
      createId: 0,
      createDate: '',
      staffId: parseInt(selectedHistory.shokuId),
      staffName: selectedHistory.shokuKnj,
      currentIndex: 1,
      totalCount: 3,
      ks21Id: '',
    } as PlanCreateDataType

    or27631Type.value.consentName.value = selectedHistory.doiNameKnj
    or27631Type.value.consentDate.value = selectedHistory.doiYmd
    local.consultationDate.value = selectedHistory.soudanYmd
    await nextTick()
    setChildCpBinds(props.uniqueCpId, {
      [OrX0010Const.CP_ID(1)]: {
        twoWayValue: {
          value: local.consultationDate.value,
          mo01343: local.consultationDate.mo01343,
        } as Mo00020Type,
      },
      [OrX0009Const.CP_ID(1)]: {
        twoWayValue: localOneway.orX0009Oneway.createData,
      },
      [OrX0007Const.CP_ID(1)]: {
        twoWayValue: localOneway.OrX0007Oneway.planTargetPeriodData,
      },
      [OrX0008Const.CP_ID(1)]: {
        twoWayValue: localOneway.orX0008Oneway.createData,
      },
    })
  }
}

const getPreventionBasicHistorySelect = async () => {
  const inputData: preventionBasicHistorySelectInEntity = {
    userId: local.userId,
    svJigyoId: local.svJigyoId ?? '',
    sc1Id: local.planPeriodId.toString(),
    ks51Id: local.khn11Id,
    historyPage: '0',
  }

  const res: preventionBasicHistorySelectOutEntity = await ScreenRepository.select(
    'preventionBasicHistorySelect',
    inputData
  )

  const { historyObj } = res.data
  await processHistory(historyObj)
}

const getPreventionBasicDetailInfoSelect = async () => {
  const inputData: preventionBasicDetailInfoSelectInEntity = {
    khn11Id: local.khn11Id,
  }

  const res: preventionBasicDetailInfoSelectOutEntity = await ScreenRepository.select(
    'preventionBasicDetailInfoSelect',
    inputData
  )

  if (res.data.kihonList.length) {
    screenStore.setCpTwoWay({
      cpId: Or08207Const.CP_ID(1),
      uniqueCpId: or08207.value.uniqueCpId,
      value: { kihonList: res.data.kihonList[0] },
      isInit: true,
    })
  }

  local.kozokuList = res.data.kozokuList
  local.kaigoList = res.data.kaigoList
  local.byourekiList = res.data.byourekiList

  // Or08467のデータを設定
  if (res.data.kaigoList && res.data.kaigoList.length > 0) {
    const kaigoData = res.data.kaigoList[0]
    // Or08467Logic.dataを通じてデータを設定
    Or08467Logic.data.set({
      uniqueCpId: or08467.value.uniqueCpId,
      value: {
        publicService: kaigoData.koutekisvKnj || '',
        nonPublicService: kaigoData.hikoutekisvKnj || '',
      },
    })
  }

  // Or08097のデータを設定（kaigoListから）
  if (res.data.kaigoList && res.data.kaigoList.length > 0) {
    const kaigoData = res.data.kaigoList[0]

    let dailyScheduleData: getDailyScheduleList[] = []

    const dailyScheduleInput: DailyScheduleSelectInEntity = {
      khn11_id: local.khn11Id,
    }
    const dailyScheduleRes: DailyScheduleSelectOutEntity = await ScreenRepository.select(
      'getDailyScheduleSelect',
      dailyScheduleInput
    )
    dailyScheduleData = dailyScheduleRes.data?.getDailyScheduleList || []

    // Or08097Logic.dataを通じてデータを設定
    Or08097Logic.data.set({
      uniqueCpId: or08097.value.uniqueCpId,
      value: {
        untilNowLife: kaigoData.imamadeKnj || '',
        hobbyPleasureSpecialSkill: kaigoData.shumiKnj || '',
        friendCommunityRelation: kaigoData.kankeiKnj || '',
        dailyLifeHowToSpendYourTime: kaigoData.sugosikataKnj || '',
        dailySchedule: dailyScheduleData,
      },
    })
  }

  // Or08468のデータを設定（byourekiListから）
  if (res.data.byourekiList && res.data.byourekiList.length > 0) {
    const medicalHistoryData = res.data.byourekiList.map((item, index) => ({
      tableIndex: index,
      kadaiKnj: { value: item.byoumeiKnj || '' },
      mokuhyoKnj: { value: item.iryokikanKnj || '' },
      kikanKnj: { value: item.byoymdKnj || '' },
      naiyoKnj: { value: item.naiyoKnj || '' },
      tantoShokuKnj: { value: item.keikaKnj || '' },
      kadaiNo: { value: item.iryokikanTel || '' },
      id2: { value: item.khn133Id || '' },
      id3: { value: '' },
      modifiedCnt2: { value: '' },
      modifiedCnt3: { value: '' },
      updateKbn2: '0',
      updateKbn3: '0',
      seq: parseInt(item.seq) || index + 1,
      passage: 0,
      attendingPhysicianOpinionAuthor: item.ikenshoCd === '1',
    }))

    // Or08468Logic.dataを通じてデータを設定
    Or08468Logic.data.set({
      uniqueCpId: or08468.value.uniqueCpId,
      value: {
        dataList: medicalHistoryData,
        ryuiKnj: '',
        opinion: { value: '' },
      },
    })
  }
}

/**
 * 画面初期化処理
 */
const setupOr00248 = () => {
  Or00248Logic.state.set({
    uniqueCpId: or00248.value.uniqueCpId,
    state: {
      regionKey: regionKey.value,
      displayUserInfoSectionFlg: true,
    },
  })
}

/**
 * 保存時処理
 */
async function _save() {
  if (!isEdit.value) {
    return
  }

  // Or08467のデータを取得
  const or08467Data = Or08467Logic.data.get(or08467.value.uniqueCpId)

  // kaigoListを更新
  if (local.kaigoList.length > 0) {
    local.kaigoList[0] = {
      ...local.kaigoList[0],
      koutekisvKnj: or08467Data?.publicService ?? '',
      hikoutekisvKnj: or08467Data?.nonPublicService ?? '',
    }
  }

  // Or08097のデータを取得
  const or08097Data = Or08097Logic.data.get(or08097.value.uniqueCpId)

  // kaigoListを更新
  if (local.kaigoList.length > 0) {
    local.kaigoList[0] = {
      ...local.kaigoList[0],
      imamadeKnj: or08097Data?.untilNowLife ?? '',
      shumiKnj: or08097Data?.hobbyPleasureSpecialSkill ?? '',
      kankeiKnj: or08097Data?.friendCommunityRelation ?? '',
      sugosikataKnj: or08097Data?.dailyLifeHowToSpendYourTime ?? '',
    }
  }

  // Or08468のデータを取得
  const or08468Data = Or08468Logic.data.get(or08468.value.uniqueCpId)

  // byourekiListを更新
  if (or08468Data?.dataList && or08468Data.dataList.length > 0) {
    local.byourekiList = or08468Data.dataList.map((item, index) => ({
      khn133Id: String(index + 1),
      khn11Id: local.khn11Id,
      byoymdKnj: item.kikanKnj?.value ?? '',
      byoumeiKnj: item.kadaiKnj?.value ?? '',
      iryokikanKnj: item.mokuhyoKnj?.value ?? '',
      ikenshoCd: item.attendingPhysicianOpinionAuthor ? '1' : '0',
      iryokikanTel: item.kadaiNo?.value ?? '',
      keikaKnj: item.tantoShokuKnj?.value ?? '',
      naiyoKnj: item.naiyoKnj?.value ?? '',
      seq: String(item.seq ?? index + 1),
    }))
  }

  const updateData: PreventionBasicDetailInfoUpdateInEntity = {
    historyUpdateKbn: historyUpdateKbn.value,
    khn11Id: local.khn11Id,
    sc1Id: local.sc1Id,
    userId: commonInfo.userId,
    kycFlg: commonInfo.kycFlg,
    houjinId: commonInfo.houjinId,
    shisetuId: commonInfo.shisetsuId,
    svJigyoId: commonInfo.svJigyoId ?? '',
    soudanYmd: local.consultationDate.value,
    shokuId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '',
    shokuKnj: localOneway.orX0009Oneway.createData?.staffName ?? '',
    doiYmd: or27631Type.value.consentDate.value,
    doiNameKnj: or27631Type.value.consentName.value,
    basicInfoList: [
      useScreenTwoWayBind<{ kihonList?: Gui01067BasicInfoListInEntity }>({
        cpId: Or08207Const.CP_ID(1),
        uniqueCpId: or08207.value.uniqueCpId,
      }).refValue.value?.kihonList ?? {},
    ] as Gui01067BasicInfoListInEntity[],
    familyInfoList: local.kozokuList,
    nursingCareInfoList: local.kaigoList,
    oneDayScheduleList: [],
    medicalHistoryList: local.byourekiList,
    modifiedCnt: local.historyModifiedCnt,
  }
  await ScreenRepository.update('preventionBasicDetailInfoUpdate', updateData)
  await getCommonData()
  await getTabsData()
}

/**
 * OrX0010 (作成日) data changes監視
 */
watch(
  () => OrX0010Logic.data.get(orX0010.value.uniqueCpId),
  (newValue) => {
    if (newValue?.value && newValue.value !== local.consultationDate.value) {
      local.consultationDate.value = newValue.value
    }
  },
  { deep: true }
)

/**
 * マスターデータ取得処理
 */
async function _master() {
  if (isEdit.value) {
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      dialogTitle: t('label.confirm'),
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case Or01549Const.DIALOG_RESULT_YES:
        await _save()
        await getTabsData()
        break
      case Or01549Const.DIALOG_RESULT_NO:
        await getTabsData()
        break
      case Or01549Const.DIALOG_RESULT_CANCEL:
        return
    }
  }
}

/**
 * 削除処理
 */
async function _delete() {
  historyUpdateKbn.value = Or01549Const.HISTORY_UPDATE_KBN_DELETE
  const dialogResult = await openInfoDialog(Or01549Const.INFO_MESSAGE_TYPE_11326)
  switch (dialogResult) {
    case Or01549Const.DIALOG_RESULT_YES:
      Or11871Logic.state.set({
        uniqueCpId: or11871.value.uniqueCpId,
        state: {
          disabledCreateBtn: true,
          disabledSaveBtn: true,
          disabledPrintBtn: true,
        },
      })
      localOneway.consultationDateOneway.disabled = true
      OrX0009Logic.state.set({
        uniqueCpId: orX0009_1.value.uniqueCpId,
        state: {
          isDisabled: true,
        },
      })
      deleteFlag.value = true
      isDisabledCopyBtn.value = true
      inputBoomShow.value = false
      break
    case Or01549Const.DIALOG_RESULT_NO:
      return
  }
}

/**
 * 印刷設定ダイアログを開く処理
 */
async function _print() {
  if (isEdit.value) {
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      dialogTitle: t('label.confirm'),
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case Or01549Const.DIALOG_RESULT_YES:
        await _save()
        Or10443Logic.state.set({
          uniqueCpId: or10443.value.uniqueCpId,
          state: { isOpen: true },
        })
        break
      case Or01549Const.DIALOG_RESULT_NO:
        Or10443Logic.state.set({
          uniqueCpId: or10443.value.uniqueCpId,
          state: { isOpen: true },
        })
        break
      case Or01549Const.DIALOG_RESULT_CANCEL:
        return
    }
  } else {
    Or10443Logic.state.set({
      uniqueCpId: or10443.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * GUI01071_予防基本複写画面をポップアップで起動する。
 */
function copyBtnClick() {
  Or31728Logic.state.set({
    uniqueCpId: or31728.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// /**
//  * Or27631のダイアログ開閉状態を更新する
//  */
function handleClickSI042() {
  Or27631Logic.state.set({
    uniqueCpId: or27631.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC013_「計画対象期間選択アイコンボタン」押下
 */
const planTargetPeriodIconClick = async () => {
  if (isEdit.value) {
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      dialogTitle: t('label.confirm'),
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    await handleDialogResult(dialogResult)
  }
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21815StateType) => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)
        let result = Or01549Const.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_CANCEL
        }

        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * ダイアログの結果を処理する
 *
 * @param dialogResult - ダイアログの選択結果
 */
const handleDialogResult = async (dialogResult: unknown) => {
  switch (dialogResult) {
    case Or01549Const.DIALOG_RESULT_YES:
      await _save()
      break
    case Or01549Const.DIALOG_RESULT_NO:
      break
    case Or01549Const.DIALOG_RESULT_CANCEL:
      return
  }
}

/**
 * 新規ボタン押下時の処理
 */
const handleClickCreate = async () => {
  if (local.addBtnState) {
    await openWarningDialog(or21815.value.uniqueCpId, {
      dialogTitle: t('label.error'),
      dialogText: t('message.i-cmn-11265', [t('label.prevention-basic')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  }

  local.addBtnState = true
  historyUpdateKbn.value = Or01549Const.HISTORY_UPDATE_KBN_CREATE
  if (local.flag.periodManage === '1') {
    if (local.totalCountHistory === 0) {
      const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
        dialogTitle: t('label.error'),
        dialogText: t('message.i-cmn-11300'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.ok'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      if (Or01549Const.DIALOG_RESULT_YES === dialogResult) {
        Or11871Logic.state.set({
          uniqueCpId: or11871.value.uniqueCpId,
          state: {
            disabledSaveBtn: false,
            disabledPrintBtn: false,
            disabledOptionMenuDelete: false,
          },
        })
        OrX0010Logic.state.set({
          uniqueCpId: orX0010_1.value.uniqueCpId,
          state: {
            isDisabled: false,
          },
        })
        deleteFlag.value = false
      }
    }
  } else {
    if (isEdit.value) {
      const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
        dialogTitle: t('label.confirm'),
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case 'yes':
          await _save()
          break
        case 'no':
          break
        case 'cancel':
          return
      }
    }
  }
}

/**
 * メニューイベントを設定する
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msgType - message TYPE
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(msgType: string): Promise<string> {
  if (msgType === Or01549Const.INFO_MESSAGE_TYPE_10430) {
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        dialogTitle: t('label.top-btn-title'),
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      },
    })
  } else if (msgType === Or01549Const.INFO_MESSAGE_TYPE_11326) {
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        dialogTitle: t('label.top-btn-title'),
        dialogText: t('message.i-cmn-11326', [
          local.consultationDate.value,
          t('label.prevention-basic'),
        ]),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'blank',
      },
    })
  } else if (msgType === Or01549Const.INFO_MESSAGE_TYPE_11265) {
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        dialogTitle: t('label.top-btn-title'),
        dialogText: t('message.i-cmn-11265'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      },
    })
  } else if (msgType === Or01549Const.INFO_MESSAGE_TYPE_11262) {
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        dialogTitle: t('label.confirm'),
        dialogText: t('message.i-cmn-11262'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      },
    })
  }

  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        let result = Or01549Const.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_CANCEL
        }

        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

function handleOrX0007() {
  return
}

function handleOrX0008() {
  return
}

const favoriteIconClick = () => {
  return
}

/**
 * 「確定ボタン」押下時の処理
 *
 * @param _value - 結果
 */
function onConfirmOr31728(_value: BasicInfo[]) {
  return
}
</script>

<template>
  <c-v-sheet class="d-flex flex-column h-100 view">
    <!-- Or11871：有機体：画面メニューエリア -->
    <g-base-or11871 v-bind="or11871">
      <template #createItems>
        <c-v-list-item
          :disabled="isDisabledCopyBtn"
          :title="t('btn.copy')"
          @click="copyBtnClick"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-copy-btn')"
          />
        </c-v-list-item>
      </template>

      <template #optionMenuItems>
        <c-v-list-item
          :title="t('btn.delete')"
          prepend-icon="delete"
          :disabled="deleteFlag"
          @click="_delete()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.delete-data')"
          />
        </c-v-list-item>
      </template>
    </g-base-or11871>

    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col
        cols="2"
        class="hidden-scroll h-100 w-cus"
      >
        <!-- （利用者基本）利用者選択の表示 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>

      <c-v-col class="hidden-scroll h-100 px-2">
        <c-v-sheet class="content">
          <c-v-container>
            <c-v-row class="header-row align-end">
                <c-v-col
                style="padding: 0 0 0 12px; width: 208px; min-width: 208px; max-width: 208px"
                >
                <!-- 事業所 -->
                <g-base-or41179
                  v-bind="or41179"
                  class="custom-select-business"
                />
                </c-v-col>
              <c-v-col
                v-if="planPeriodShow"
                cols="auto"
                style="padding: 0px; padding-left: 12px"
              >
                <!-- 計画対象期間 -->
                <g-custom-orX0007
                  ref="orX0007Ref"
                  v-bind="OrX0007"
                  :oneway-model-value="localOneway.OrX0007Oneway"
                  :unique-cp-id="OrX0007.uniqueCpId"
                  :parent-method="handleOrX0007"
                />
              </c-v-col>
              <c-v-col
                v-else
                cols="auto"
                style="padding: 0px"
              >
                <base-mo01338 :oneway-model-value="mo01338Oneway" />
              </c-v-col>
              <c-v-col
                v-if="authorShow"
                cols="auto"
                style="padding: 0px"
              >
                <!-- 作成日 -->
                <g-custom-orX0010
                  v-bind="orX0010"
                  :oneway-model-value="localOneway.orX0010Oneway"
                  :unique-cp-id="orX0010.uniqueCpId"
                  class="custom-required"
                />
              </c-v-col>
              <c-v-col
                v-if="consultationDateShow"
                cols="auto"
                style="padding: 0px"
              >
                <!-- 作成者 -->
                <g-custom-orX0009
                  v-bind="orX0009_1"
                  :oneway-model-value="localOneway.orX0009Oneway"
                  :unique-cp-id="orX0009_1.uniqueCpId"
                />
              </c-v-col>
              <c-v-col
                v-if="historyShow"
                cols="auto"
                style="padding: 0px"
              >
                <!-- 履歴 -->
                <g-custom-orX0008
                  v-bind="orX0008_1"
                  :oneway-model-value="localOneway.orX0008Oneway"
                  :unique-cp-id="orX0008_1.uniqueCpId"
                  :parent-method="handleOrX0008"
                />
              </c-v-col>
            </c-v-row>

            <c-v-row>
              <c-v-col
                cols="auto"
                style="padding: 16px 8px 16px 12px"
                name="heelo"
              >
                <!-- 同意 -->
                <base-mo00611
                  :oneway-model-value="localOneway.mo00611Oneway"
                  @click="handleClickSI042"
                />

                <g-custom-or-27631
                  v-if="showDialogOr27631"
                  v-bind="or27631"
                  v-model="or27631Type"
                /> </c-v-col
            ></c-v-row>

            <c-v-row no-gutters>
              <base-mo00043
                v-model="local.mo00043"
                class="mt-2 w-100"
                style="padding: 0 !important"
                :oneway-model-value="localOneway.mo00043Oneway"
                @click="tabsClick"
              >
              </base-mo00043>
            </c-v-row>
            <c-v-row
              v-show="inputBoomShow"
              no-gutters
              class="mt-2 inputBoomShow"
            >
              <c-v-window
                v-model="local.mo00043.id"
                class="tabItems"
              >
                <!--基本情報-->
                <c-v-window-item value="basicInfo">
                  <g-custom-or-08207 v-bind="or08207"></g-custom-or-08207>
                </c-v-window-item>
                <!-- 介護予防に関する事項 -->
                <c-v-window-item value="carePrevention">
                  <g-custom-or-08097
                    v-bind="or08097"
                    :unique-cp-id="or08097.uniqueCpId"
                    :oneway-model-value="localOneway.Or08097Oneway"
                  />
                </c-v-window-item>
                <!-- 現病歴・既往歴と経過 -->
                <c-v-window-item value="currentMedical">
                  <g-custom-or-08468
                    v-bind="or08468"
                    :unique-cp-id="or08468.uniqueCpId"
                    :oneway-model-value="localOneway.Or08468Oneway"
                  />
                </c-v-window-item>
                <!-- 現在利用しているサービス -->
                <c-v-window-item value="currentService">
                  <g-custom-or-08467
                    v-bind="or08467"
                    :unique-cp-id="or08467.uniqueCpId"
                    :oneway-model-value="localOneway.Or08468Oneway"
                  />
                </c-v-window-item>
              </c-v-window>
            </c-v-row>
          </c-v-container>
        </c-v-sheet>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>

  <!-- warning メッセージ -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- INFO 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <!-- 有機体:エラーダイアログ -->
  <g-base-or21813 v-bind="or21813" />

  <g-custom-or-31728
    v-if="showDialogOr31728"
    v-bind="or31728"
    v-model="or31728Type"
    :unique-cp-id="or31728.uniqueCpId"
    @on-confirm="onConfirmOr31728"
  />

  <g-custom-or-10443
    v-if="showDialogOr10443"
    v-bind="or10443"
    :oneway-model-value="Or10443OnewayModel"
  />
</template>

<style scoped lang="scss">
.divider-class {
  border-width: thin;
  margin: 8px 0px;
}

.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}

.content {
  padding: 5px 0px;
  overflow-x: auto;
}
.header-row {
  margin-top: 0px;
  align-items: baseline;
  flex-wrap: nowrap !important;
  white-space: nowrap;
  width: 1086px;
  gap: 24px;
}

:deep(.v-slide-group__content) {
  gap: 24px;
}
:deep(.section-border-all-sides) {
  border: 0;
}
.v-sheet {
  background-color: transparent !important;
}
.first-row,
.header-row {
  :deep(.v-sheet) {
    background-color: transparent !important;
  }
}

:deep(.notice) {
  .v-col {
    .item-label {
      color: rgb(var(--v-theme-red-700)) !important;
      font-weight: bold !important;
    }
  }
}

:deep(.custom-select-business .v-sheet) {
  width: 208px !important;
  max-width: 208px !important;
  min-width: 208px !important;
  margin-right: 0px !important;
}
:deep(.custom-select-business .v-input) {
  width: 208px !important;
  max-width: 208px !important;
  min-width: 208px !important;
  background-color: rgba(var(--v-theme-secondaryBackground)) !important;
}

:deep(.custom-required) {
  .item-label::after {
    content: '必須';
    color: #ed6809;
    font-size: 12px;
    padding-left: 4px;
  }
}

.inputBoomShow {
  width: 1325px !important;
}

.w-cus {
  min-width: 280px !important;
  width: 280px !important;
  max-width: 280px !important;
}
</style>
