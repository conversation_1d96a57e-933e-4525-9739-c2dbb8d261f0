import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 「計画実施‐実績登録」画面の初期情報取得入力エンティティ
 */
export interface IPlanImplementationAchievementsRegistInitSelectInEntity extends InWebEntity {
  /** 区分フラグ */
  kbnFlg: string
  /** 施設ID */
  shisetuId: string
  /** ヘッダID */
  kjisshi1Id: string
  /** 担当者絞込 */
  tantoKnj?: string
  /** 頻度絞込 */
  hindoKnj?: string
  /** 実施日 */
  implementationDate: string
  /** 法人ID */
  hojinId: string
  /** 利用者ID */
  userid: string
  /** 変数設定区分 */
  computeHensyuSetteiKbn: string
  /** 画面表示順 */
  timeSortKbn: string
  /** データID */
  kjisshi2Id: string
  /** 文字絞込 */
  mojiKnj?: string
}

/**
 * 「計画実施‐実績登録」画面の初期情報取得出力エンティティ
 */
export interface IPlanImplementationAchievementsRegistInitSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /** 実施ドロップダウンリスト */
    implementationList: Implementation[]
    /** ケース種別ドロップダウンリスト */
    careKindList: CareKind[]
    /** 計画実施データ欄情報リスト */
    planImplementationDataInfoList: PlanImplementationDataInfo[]
    /** ケースリスト */
    careList: Care[]
  }
}

/**
 * 「計画実施‐実績登録」画面の履歴情報取得入力エンティティ
 */
export interface IPlanImplementationAchievementsRegistHistorySelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 実施日の左7桁 */
  jisshiDayLeft7: string
}

/**
 * 「計画実施‐実績登録」画面の履歴情報取得出力エンティティ
 */
export interface IPlanImplementationAchievementsRegistHistorySelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /** 計画実施履歴ヘッダID情報リスト */
    planImplementationHistoryKjisshi1IdInfoList: { kjisshi1Id: string }[]
  }
}

/**
 * 「計画実施‐実績登録」画面の実績情報取得入力エンティティ
 */
export interface IPlanImplementationAchievementsRegistAchievementsInfoSelectInEntity
  extends InWebEntity {
  /** ヘッダID */
  kjisshi1Id: string
  /** 担当者絞込 */
  tantoKnj?: string
  /** 頻度絞込 */
  hindoKnj?: string
  /** 実施日 */
  implementationDate: string
}

/**
 * 「計画実施‐実績登録」画面の実績情報取得出力エンティティ
 */
export interface IPlanImplementationAchievementsRegistAchievementsInfoSelectOutEntity
  extends OutWebEntity {
  /** data */
  data: {
    /** 計画実施データ欄情報リスト */
    planImplementationDataInfoList: PlanImplementationDataInfo[]
  }
}

/**
 * 「計画実施‐実績登録」画面のケース情報取得入力エンティティ
 */
export interface IPlanImplementationAchievementsRegistCaseInfoSelectInEntity extends InWebEntity {
  /** 施設ID */
  shisetuId: string
  /** 法人ID */
  hojinId: string
  /** 利用者ID */
  userid: string
  /** 実施日 */
  implementationDate: string
  /** 変数設定区分 */
  computeHensyuSetteiKbn: string
  /** 画面表示順 */
  timeSortKbn: string
  /** データID */
  kjisshi2Id: string
  /** 文字絞込 */
  mojiKnj?: string
}

/**
 * 「計画実施‐実績登録」画面のケース情報取得出力エンティティ
 */
export interface IPlanImplementationAchievementsRegistCaseInfoSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /** ケースリスト */
    careList: Care[]
  }
}

/**
 * 「計画実施‐実績登録」画面の実績情報取得入力エンティティ
 */
export interface IPlanImplementationAchievementsRegistUpdateInEntity extends InWebEntity {
  /** 実績更新フラグ */
  achievementsUpdateFlag: string
  /** 計画実施データ欄情報リスト（保存用） */
  planImplementationDataList: PlanImplementationData[]
  /** ケース更新フラグ */
  caseUpdateFlag: string
  /** 事業者ID */
  svJigyoId: string
  /** 法人ID */
  hojinId: string
  /** 施設ID */
  shisetuId: string
  /** 利用者ID */
  userid: string
  /** ケースリスト（保存用） */
  careList: CareInfo[]
}

/**
 * 「計画実施‐実績登録」画面の実績情報取得出力エンティティ
 */
export interface IPlanImplementationAchievementsRegistUpdateOutEntity extends OutWebEntity {
  /** data */
  data: object
}

/**
 * 実施ドロップダウン
 */
export interface Implementation {
  /** 区分コード */
  kbnCd: string
  /** 内容 */
  textKnj: string
}

/**
 * ケース種別ドロップダウン
 */
export interface CareKind {
  /** ケース種別 */
  caseCd: string
  /** 種別名 */
  shubetuKnj?: string
  /** 表示色 */
  color?: string
}

/**
 * 計画実施データ欄情報
 */
export interface PlanImplementationDataInfo {
  /** データID */
  kjisshi2Id: string
  /** ヘッダID */
  kjisshi1Id: string
  /** 課題番号 */
  kadaiNo?: string
  /** 課題 */
  kadaiKnj?: string
  /** 表示順 */
  seq?: string
  /** 担当者 */
  tantoKnj?: string
  /** 計画書(2)データID */
  ks22Id?: string
  /** 計画書(2)担当者ID */
  ks222Id?: string
  /** dmy実施チェック */
  dmyJisshiChk?: string
  /** 区分コード */
  kbnCd?: string
  /** 区分内容 */
  textKnj?: string
  /** 頻度 */
  hindoKnj?: string
  /** 計画書(2)詳細ID */
  cks22Ks22Id: string
  /** 計画書（2）ID */
  ks21Id: string
  /** 具体的 */
  gutaitekiKnj?: string
  /** 長期 */
  choukiKnj?: string
  /** 短期 */
  tankiKnj?: string
  /** 介護 */
  kaigoKnj?: string
  /** サービス種別 */
  svShuKnj?: string
  /** 計画書（2）頻度 */
  cks22HindoKnj?: string
  /** 更新区分 */
  updateKbn?: string
  /** 計画実施表データの更新回数 */
  kjisshi2ModifiedCnt: string
  /** 計画書（２）データ：新／旧共用の更新回数 */
  cks22ModifiedCnt: string
}

/**
 * ケース
 */
export interface Care {
  /** 備考区分 */
  bikoKbn: string
  /** 記録日 */
  yymmYmd: string
  /** レコード番号 */
  recNo: string
  /** 開始時間 */
  timeHh?: string
  /** 開始分 */
  timeMm?: string
  /** ケース種別 */
  caseCd?: string
  /** ケース色 */
  color?: string
  /** ケース事項 */
  caseKnj?: string
  /** 記入者 */
  staffid?: string
  /** 記入者名 */
  staffName?: string
  /** ｹｰｽ転記フラグ */
  caseFlg?: string
  /** 申し送りフラグ */
  moushiokuriFlg?: string
  /** 結果元履歴番号 */
  baseRecNo?: string
  /** システム別フラグ */
  systemFlg?: string
  /** 指示フラグ */
  shijiFlg?: string
  /** 共有区分 */
  kyouyuBikoKbn?: string
  /** 褥瘡フラグ */
  jyoFlg?: string
  /** ユニークID */
  uniqueId: string
  /** DmyW01YymmYmd */
  dmyW01YymmYmd?: string
  /** ComputeColor */
  computeColor?: string
  /** 計画書(2)ID */
  ks21Id?: string
  /** 計画書(2)詳細ID */
  ks22Id?: string
  /** 実施モニタリング詳細ID */
  kjisshi2Id?: string
  /** DmyMFlg */
  dmyMFlg?: string
  /** 実施状況確認詳細ID */
  r4sKjisshi2Id?: string
  /** 結果元ユニークID */
  baseUniqueId?: string
  /** 完了フラグ */
  kanryouFlg?: string
  /** 画像ID */
  computeGazouId?: string
  /** プロブレムID */
  problemId?: string
  /** 変数設定区分 */
  computeHensyuSetteiKbn?: string
  /** 更新区分 */
  updateKbn?: string
  /** 備考の更新回数 */
  casebikoModifiedCnt: string
  /** 老健カルテ情報の更新回数 */
  infoModifiedCnt?: string
}

/**
 * 計画実施データ欄情報（保存用）
 */
export interface PlanImplementationData {
  /** データID */
  kjisshi2Id: string
  /** ヘッダID */
  kjisshi1Id: string
  /** dmy実施チェック */
  dmyJisshiChk?: string
  /** 区分コード */
  kbnCd: string
  /** 更新区分 */
  updateKbn?: string
}

/**
 * ケース（保存用）
 */
export interface CareInfo {
  /** 備考区分 */
  bikoKbn?: string
  /** 記録日 */
  yymmYmd: string
  /** レコード番号 */
  recNo?: string
  /** 開始時間 */
  timeHh: string
  /** 開始分 */
  timeMm: string
  /** ケース種別 */
  caseCd?: string
  /** ケース色 */
  color?: string
  /** ケース事項 */
  caseKnj?: string
  /** 記入者 */
  staffid: string
  /** 記入者名 */
  staffName?: string
  /** ｹｰｽ転記フラグ */
  caseFlg?: string
  /** 申し送りフラグ */
  moushiokuriFlg?: string
  /** 結果元履歴番号 */
  baseRecNo?: string
  /** システム別フラグ */
  systemFlg?: string
  /** 指示フラグ */
  shijiFlg?: string
  /** 共有区分 */
  kyouyuBikoKbn?: string
  /** 褥瘡フラグ */
  jyoFlg?: string
  /** ユニークID */
  uniqueId?: string
  /** DmyW01YymmYmd */
  dmyW01YymmYmd?: string
  /** ComputeColor */
  computeColor?: string
  /** 計画書(2)ID */
  ks21Id?: string
  /** 計画書(2)詳細ID */
  ks22Id?: string
  /** 実施モニタリング詳細ID */
  kjisshi2Id: string
  /** DmyMFlg */
  dmyMFlg?: string
  /** 実施状況確認詳細ID */
  r4sKjisshi2Id?: string
  /** 結果元ユニークID */
  baseUniqueId?: string
  /** 完了フラグ */
  kanryouFlg?: string
  /** 画像ID */
  computeGazouId?: string
  /** プロブレムID */
  problemId?: string
  /** 変数設定区分 */
  computeHensyuSetteiKbn?: string
    /** 画像ステータス */
    picStatus?: string
  /** 更新区分 */
  updateKbn?: string
}
