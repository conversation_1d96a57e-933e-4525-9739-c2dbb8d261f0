<script setup lang="ts">
/**
 * GUI01164_備考欄複写画面
 *
 * @description
 * 備考欄複写画面
 *
 * <AUTHOR> 畢文傑
 */
import { computed, definePageMeta,reactive, ref, useScreenStore } from '#imports'
import type { Or29210OnewayType } from '~/types/cmn/business/components/Or29210Type'
import { Or29210Const } from '~/components/custom-components/organisms/Or29210/Or29210.constants'
import { Or29210Logic } from '~/components/custom-components/organisms/Or29210/Or29210.logic'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * KMD 畢文傑 2025/06/15 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI01164'
// ルーティング
const routing = 'GUI01164/pinia'
// 画面物理名
const screenName = 'GUI01164'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or29210 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: Or29210Const.CP_ID(0) },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or29210Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or29210.value.uniqueCpId = pageComponent.uniqueCpId

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOr29210 = computed(() => {
  // Or29210のダイアログ開閉状態
  return Or29210Logic.state.get(or29210.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const Or29210Data = {
  // 支援事業者ID
  shienId: '9',
  // 利用者ID
  userId: '1',
  // サービス提供年月
  yymmYm: '2004/04',
  // サービス提供年月（変更日）
  yymmD: '01',
  // 備考欄（利用票）
  bikouRiyouKnj: '親利用票',
  // 備考欄（提供票）
  bikouTeikyouKnj: '親提供票',
  // 備考欄（カレンダー）
  bikouCalenderKnj: '親カレンダー',
  // 帳票区分
  sel: '2',
  // 備考更新区分
  updateKbn: 'U',
}

const or29210Type = ref<Or29210OnewayType>({
  // 支援事業者ID
  shienId: '',
  // 利用者ID
  userId: '',
  // サービス提供年月
  yymmYm: '2025/06',
  // サービス提供年月（変更日）
  yymmD: '',
  // 備考欄（利用票）
  bikouRiyouKnj: '親利用票',
  // 備考欄（提供票）
  bikouTeikyouKnj: '親提供票',
  // 備考欄（カレンダー）
  bikouCalenderKnj: '親カレンダー',
  // 帳票区分
  sel: '2',
  // 備考更新区分
  updateKbn: 'U',
})
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 支援事業所
  shienId: { value: '1' } as Mo00045Type,
  // 利用者ID
  userId: { value: '1' } as Mo00045Type,
  // 提供年月
  yymmYm: { value: '2025/09' } as Mo00045Type,
  // 提供年月(変更日)
  yymmD: { value: '24' } as Mo00045Type,
})

/**
 *  ボタン押下時の処理

 * @param option - linkを区別する
 */
function or29210OnClick(option: string) {
  // Or29210のダイアログ開閉状態を更新する
  Or29210Logic.state.set({
    uniqueCpId: or29210.value.uniqueCpId,
    state: { isOpen: true, decisionMakingClickValue: option },
  })
}
function onClickOr29210() {
  Or29210Data.shienId = local.shienId.value
  Or29210Data.userId = local.userId.value
  Or29210Data.yymmYm = local.yymmYm.value
  Or29210Data.yymmD = local.yymmD.value
  // Or29210のダイアログ開閉状態を更新する
  Or29210Logic.state.set({
    uniqueCpId: or29210.value.uniqueCpId,
    state: { isOpen: true, decisionMakingClickValue: '0' },
  })
}
/**************************************************
 * 備考欄複写画面
 * KMD 畢文傑 2025/06/20 ADD END
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- GUI01164_備考欄複写画面 KMD 畢文傑 2025/06/20 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or29210OnClick('0')"
        >備考欄複写画面
      </v-btn>
      <g-custom-or-29210
        v-if="showDialogOr29210"
        v-bind="or29210"
        v-model="or29210Type"
        :oneway-model-value="Or29210Data"
      />
    </c-v-col>
  </c-v-row>
  <!-- GUI01164_備考欄複写画面 KMD 畢文傑 2025/06/20 ADD END-->
   <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">支援事業所</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.shienId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">提供年月</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.yymmYm"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">提供年月(変更日)</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.yymmD"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr29210"> GUI01164 疎通起動 </v-btn>
  </div>
</template>
