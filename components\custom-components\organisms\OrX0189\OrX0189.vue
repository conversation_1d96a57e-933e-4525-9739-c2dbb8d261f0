<script setup lang="ts">
/**
 * OrX0189：有機体：（確定版）入院基本情報
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Gui00059Logic } from '../Gui00059/Gui00059.logic'
import { Gui00059Const } from '../Gui00059/Gui00059.constants'
import { Or10648Logic } from '../Or10648/Or10648.logic'
import { Or10648Const } from '../Or10648/Or10648.constants'
import { Or26261Const } from '../Or26261/Or26261.constants'
import { Or26261Logic } from '../Or26261/Or26261.logic'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import { OrX0157Const } from '../OrX0157/OrX0157.constants'
import { OrX0189Const } from './OrX0189.constants'
import type { OrX0189OneWayType, OrX0189ValuesType } from './OrX0189.Type'
import {
  useCmnRouteCom,
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Or10648SelectInfoType } from '~/types/cmn/business/components/Or10648Type'
import type { Or26261OnewayType, Or26261Type } from '~/types/cmn/business/components/Or26261Type'
import type {
  JigyoInfo,
  TantoInfo,
} from '~/repositories/cmn/entities/HospitalizationTimeInfoOfferPeriodSelectServiceEntity'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
/**************************************************
 * Props
 **************************************************/
/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}
// route共有情報
const cmnRouteCom = useCmnRouteCom()
const systemCommonsStore = useSystemCommonsStore() // システム共有情報ストア
const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()
const { refValue } = useScreenTwoWayBind<OrX0189ValuesType>({
  cpId: OrX0189Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<OrX0189ValuesType> }
useScreenOneWayBind<OrX0189OneWayType>({
  cpId: OrX0189Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value as unknown as {
        /** 担当情報リスト */
        tantoInfoList: TantoInfo[]
        /** 事業所情報リスト */
        jigyoInfoList: JigyoInfo[]
      }
      initSvJigyoData(refValue.value.orX0189Values.shienJigyoId, 'init')
      initTantoShokuData(refValue.value.orX0189Values.tantoShokuId, 'init')
      localOneway.or10648OnewayType.svJigyoId = refValue.value.orX0189Values.shienJigyoId
    },
  },
})

const gui00059 = ref({ uniqueCpId: '' })
const or10648 = ref({ uniqueCpId: '' })
const or26261 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Gui00059Const.CP_ID(1)]: gui00059.value,
  [Or10648Const.CP_ID(1)]: or10648.value,
  [Or26261Const.CP_ID(1)]: or26261.value,
})
/**************************************************
 * 変数定義
 **************************************************/

const local = reactive({
  commonInfo: {} as TeX0012Type,
  jigyoKnj: { value: '' },
  tantoShokuKnj: { value: '' },
  or26261: {
    shokuin: {
      shokuin1Knj: 'no',
      shokuin2Knj: 'selected',
    },
  } as Or26261Type,
})

const localOneway = reactive({
  codeListOneway: {} as {
    /** 担当情報リスト */
    tantoInfoList: TantoInfo[]
    /** 事業所情報リスト */
    jigyoInfoList: JigyoInfo[]
  },
  or26261Oneway: {
    hokuinId: systemCommonsStore.getStaffId ?? '',
    /** 編集モード */
    editMode: OrX0189Const.DEFAULT.VALUE_1,
    // 職員ID
    shokuinId: [],
    // モード
    selectMode: OrX0189Const.DEFAULT.VALUE_12,
    // 初期選択事業所ID
    defSvJigyoId: '',
    // 適用事業所ＩＤリスト
    svJigyoId: [{ svJigyoId: OrX0189Const.DEFAULT.VALUE_1 }],
    // 未設定フラグ
    misetteiFlg: OrX0189Const.DEFAULT.VALUE_0,
    // 基準日
    kijunYmd: local.commonInfo.createYmd,
    // フィルターフラグ
    filterDwFlg: OrX0189Const.DEFAULT.VALUE_1,
    // 表示するカラム
    hyoujiColumn: [],
    // 本日の勤務予定者のみを選択する
    isToday: false,
    // 勤務表パターン表示フラグ
    pattern: '',
    // 資格免許非活性フラグ
    qualification: '',
    menu1Id: '',
    gsyscd: '',
    gbSibasis: '',
    glSibasisDbflg: '',
  } as Or26261OnewayType,
  or10648OnewayType: {
    svJigyoId: '',
  } as Or10648SelectInfoType,
  mo00045Oneway1: {
    maxlength: '41',
    width: '231px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00020Oneway: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
    width: '161px',
  } as Mo00020OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo00030Oneway: {
    mo00045Oneway: {
      maxlength: '14',
      width: '180px',
      isVerticalLabel: false,
      showItemLabel: false,
    },
    mode: OrX0189Const.DEFAULT.VALUE_1,
  },
  orX0157Oneway: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: true,
        itemLabel: t('label.medical_institution_name'),
        width: '231px',
        maxlength: '41',
      },
    },
  },
  orX0157Oneway2: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: true,
        itemLabel: t('label.home_care_support_office_name_label'),
        width: '620px',
        maxlength: '41',
      },
    },
  },
  orX0157Oneway3: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: true,
        itemLabel: t('label.person_name_label'),
        width: '337px',
        maxlength: '41',
      },
    },
  },
})

// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Gui00059のダイアログ開閉状態
  return Gui00059Logic.state.get(gui00059.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr26261 = computed(() => {
  // Or26261のダイアログ開閉状態
  return Or26261Logic.state.get(or26261.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理（検索機能からでない画面呼び出し）
 */
function handleHospKnjHead() {
  // Gui00059のダイアログ開閉状態を更新する
  Gui00059Logic.state.set({
    uniqueCpId: gui00059.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理
 */
function handleShienJigyoId() {
  // Or10648のダイアログ開閉状態を更新する
  Or10648Logic.state.set({
    uniqueCpId: or10648.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理
 */
function handleTantoShokuId() {
  // Or26261のダイアログ開閉状態を更新する
  Or26261Logic.state.set({
    uniqueCpId: or26261.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const svJigyoIdChange = (val: { svJigyoId: number }) => {
  initSvJigyoData(val.svJigyoId.toString(), 'set')
}

const tantoShokuIdChange = (val: Or26261Type) => {
  initTantoShokuData(val.shokuin.chkShokuId.toString(), 'set')
}

const initTantoShokuData = (tantoShokuId: string, key: string) => {
  // const data: TantoInfo = localOneway.codeListOneway.tantoInfoList.find(
  //   (item) => item.tantoShokuId === tantoShokuId
  // ) as TantoInfo
  // if (key === 'set') {
  //   refValue.value.orX0189Values.tantoShokuId = data ? (data.tantoShokuId ?? '') : ''
  // }
  // local.tantoShokuKnj.value = data ? (data.tantoShokuKnj ?? '') : ''
}

const initSvJigyoData = (svJigyoId: string, key: string) => {
  // let data: JigyoInfo = localOneway.codeListOneway.jigyoInfoList.find(
  //   (item) => item.svJigyoId === svJigyoId
  // ) as JigyoInfo
  // if (!data) {
  //   data = {
  //     jigyoKnj: '',
  //     svJigyoId: svJigyoId,
  //     tel: '',
  //     fax: '',
  //   }
  // }
  // local.jigyoKnj.value = data.jigyoKnj ?? ''
  // if (key === 'set') {
  //   refValue.value.orX0189Values.shienJigyoId = data.svJigyoId ?? ''
  //   if (refValue.value.orX0189Values.tel.mo00045) {
  //     refValue.value.orX0189Values.tel.mo00045.value = data.tel ?? ''
  //   }
  //   if (refValue.value.orX0189Values.fax.mo00045) {
  //     refValue.value.orX0189Values.fax.mo00045.value = data.fax ?? ''
  //   }
  // }
}

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0012Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    if (newValue?.teikyouId) {
      local.commonInfo = newValue
    }
  },
  { deep: true }
)

/**
 * Gui00059のイベントを監視
 *
 * @description
 * 自身のEvent領域の検索ボタン押下フラグを更新する。
 * またGui00059のボタン押下フラグをリセットする。
 */
watch(
  () => Gui00059Logic.event.get(gui00059.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    console.log(newValue, '111111111111111111')
    if (newValue.confirmFlg && newValue.confirmParams) {
      // refValue.value.orX0189Values.hospKnj.value =
      //     info.hospital.find((item) => item.id === newValue.confirmParams?.hospitalCode)?.name ?? ''
      Gui00059Logic.event.set({
        uniqueCpId: gui00059.value.uniqueCpId,
        events: {
          confirmFlg: false,
          confirmParams: undefined,
        },
      })
    }

    if (newValue.confirmFlg && newValue.confirmParams) {
      // 子コンポーネントのflgをリセットする
      Gui00059Logic.event.set({
        uniqueCpId: gui00059.value.uniqueCpId,
        events: {
          confirmFlg: false,
          confirmParams: undefined,
        },
      })
    }
  }
)
</script>

<template>
  <div
    v-if="refValue.orX0189Values"
    class="box"
  >
    <div class="d-flex align-center">
      <!-- 退居（所）日 -->
      <div class="data-cell mr-12">
        <p>{{ t('label.tai-y-md') }}</p>
        <base-mo00020
          v-model="refValue.orX0189Values.taiYmd"
          :oneway-model-value="localOneway.mo00020Oneway"
        />
      </div>
      <!-- 情報提供日 -->
      <div class="data-cell d-flex align-end">
        <div class="mr-6">
          <p>{{ t('label.information_provision_date') }}</p>
          <base-mo00020
            v-model="refValue.orX0189Values.teikyouYmd"
            :oneway-model-value="localOneway.mo00020Oneway"
          />
        </div>
        <div class="d-flex align-center">
          <base-mo00020
            v-model="refValue.orX0189Values.shisetuSeikatuYmd"
            class="mr-2"
            :oneway-model-value="localOneway.mo00020Oneway"
          />
          {{ t('label.facility_living_days') }}
        </div>
      </div>
    </div>
    <!-- 医療機関名 -->
    <div class="data-cell d-flex">
      <g-custom-or-x-0157
        v-model="refValue.orX0189Values.hospKnj"
        :oneway-model-value="localOneway.orX0157Oneway"
        @on-click-edit-btn="handleHospKnjHead"
      ></g-custom-or-x-0157>
      <!-- 医療機関選択モーダル -->
      <g-custom-gui-00059
        v-if="showDialog"
        v-bind="gui00059"
      />
    </div>
    <!-- ご担当者名 -->
    <div class="data-cell">
      <p>{{ t('label.responsible_person_name_label') }}</p>
      <base-mo00045
        v-model="refValue.orX0189Values.hospTantoKnj"
        :oneway-model-value="localOneway.mo00045Oneway1"
      />
    </div>
    <!-- 施設名 -->
    <div class="data-cell">
      <p>{{ t('label.facility-name') }}</p>
      <base-mo00045
        v-model="refValue.orX0189Values.shisetuKnj"
        :oneway-model-value="localOneway.mo00045Oneway1"
      />
    </div>
    <!-- 担当者名-->
    <div class="d-flex align-end">
      <g-custom-or-x-0157
        v-model="local.tantoShokuKnj"
        :oneway-model-value="localOneway.orX0157Oneway3"
        @on-click-edit-btn="handleTantoShokuId"
      ></g-custom-or-x-0157>
      <!-- GUI01186_担当ケアマネ検索画面を起動する -->
      <g-custom-or-26261
        v-if="showDialogOr26261"
        v-bind="or26261"
        v-model="local.or26261"
        :oneway-model-value="localOneway.or26261Oneway"
        @update:model-value="tantoShokuIdChange"
      />
      <!-- TEL -->
      <div class="d-flex flex-column ml-12 mr-12">
        <p>{{ t('label.tel_label') }}</p>
        <base-mo00030
          v-model="refValue.orX0189Values.tel"
          :oneway-model-value="localOneway.mo00030Oneway"
        />
      </div>
      <!-- FAX -->
      <div class="d-flex flex-column">
        <p>{{ t('label.fax_label') }}</p>
        <base-mo00030
          v-model="refValue.orX0189Values.fax"
          :oneway-model-value="localOneway.mo00030Oneway"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.box {
  margin: 24px 213px 24px 48px;
}
.data-cell {
  p {
    padding-bottom: 4px;
  }
  margin-bottom: 16px;
}
</style>
