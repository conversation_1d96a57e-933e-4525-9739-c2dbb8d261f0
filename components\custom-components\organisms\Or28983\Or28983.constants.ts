import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or28983:有機体:事業所検索
 * GUI01177__事業所検索
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> 靳先念
 */
export namespace Or28983Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or28983', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /** 人  */
    export const PERSON = '人'
  }
}
