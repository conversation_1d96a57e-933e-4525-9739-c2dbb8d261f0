<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  watch,
} from '#imports'
import { Or26416Const } from '~/components/custom-components/organisms/Or26416/Or26416.constants'
import { Or26416Logic } from '~/components/custom-components/organisms/Or26416/Or26416.logic'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Or26416OnewayType, Or26416Type } from '~/types/cmn/business/components/Or26416Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01271'
// ルーティング
const routing = 'GUI01271/pinia'
// 画面物理名
const screenName = 'GUI01271'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or26416 = ref({ uniqueCpId: Or26416Const.CP_ID(1) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01271' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or26416Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or26416.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01271',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26416Const.CP_ID(0) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or26416Const.CP_ID(0)]: or26416.value,
})

const or26416OnewayType = ref<Or26416OnewayType>({
  /**
   * 認定フラグ
   */
  ninteiFlg: '4',
  /**
   * 計画期間ID
   */
  sc1Id: '97',
  /**
   * 調査票ID
   */
  cschId: '121',
})
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 認定フラグ
  ninteiFlg: { value: '4' } as Mo00045Type,
  // 計画期間ID
  sc1Id: { value: '97' } as Mo00045Type,
  // 調査票ID
  cschId: { value: '121' } as Mo00045Type,
})

// ダイアログ表示フラグ
const showDialogOr26416 = computed(() => {
  // Or26416のダイアログ開閉状態
  return Or26416Logic.state.get(or26416.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or26416)
 */
function onClickOr26416() {
  // Or26416のダイアログ開閉状態を更新する
  Or26416Logic.state.set({
    uniqueCpId: or26416.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理(Or26416)
 */
function onClickOr26416_2() {
  // Or26416のダイアログ開閉状態を更新する
  Or26416Logic.state.set({
    uniqueCpId: or26416.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function onClickOr26416_3() {
  or26416OnewayType.value.cschId = local.cschId.value
  or26416OnewayType.value.ninteiFlg = local.ninteiFlg.value
  or26416OnewayType.value.sc1Id = local.sc1Id.value
  // Or26416のダイアログ開閉状態を更新する
  Or26416Logic.state.set({
    uniqueCpId: or26416.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or26416Type = ref<Or26416Type>({
  itiziHantei: '',
})

watch(or26416Type, () => {
  console.log(or26416Type.value)
})
</script>

<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26416"
        >GUI01271_［要介護度の一次判定］画面
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26416_2"
        >GUI01271_［要介護度の一次判定］画面_全体表示（警告なし）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-26416
    v-if="showDialogOr26416"
    v-bind="or26416"
    v-model="or26416Type"
    :oneway-model-value="or26416OnewayType"
  />
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">認定フラグ</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.ninteiFlg"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">計画期間ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.sc1Id"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">調査票ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.cschId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr26416_3"> GUI01271 疎通起動 </v-btn>
  </div>
</template>
