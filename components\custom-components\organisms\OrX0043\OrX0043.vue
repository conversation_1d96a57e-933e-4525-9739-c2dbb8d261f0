<script setup lang="ts">
/**
 * Or27831: 日課計画入力モーダル
 * GUI01058_日課計画入力
 *
 * <AUTHOR>
 */
import { reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { watch } from '#imports'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or27831OnewayType } from '~/types/cmn/business/components/Or27831Type'
import type { Mo01332OnewayType } from '~/types/business/components/Mo01332Type'
import type { OrX0043Type } from '~/types/cmn/business/components/OrX0043Type'
import type { CustomClass } from '~/types/CustomClassType'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or27831OnewayType
  modelValue: OrX0043Type
}

const props = defineProps<Props>()
const local = reactive({
  orX0043: { ...props.modelValue } as OrX0043Type,
})
const localOneWay = reactive({
  si017OneWay: {
    labelColor: 'rgb(var(--v-theme-black-800))',
    width: '20px',
    minWidth: '20px',
    minHeight: '40px',
  } as Mo00611OnewayType,
  mo01332AtAnyTime: {
    showItemLabel: true,
    items: [
      { label: t('label.day-short-sunday'), value: '1' },
      { label: t('label.day-short-monday'), value: '2' },
      { label: t('label.day-short-tuesday'), value: '3' },
      { label: t('label.day-short-wednesday'), value: '4' },
      { label: t('label.day-short-thursday'), value: '5' },
      { label: t('label.day-short-friday'), value: '6' },
      { label: t('label.day-short-saturday'), value: '7' },
    ],
    isVertical: false,
    customClass: {
      outerStyle: 'column-gap: 0px;',
    } as CustomClass
  } as Mo01332OnewayType,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['check-all-day', 'update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      local.orX0043 = newValue
    }
  }
)
/**
 *「曜日-全選択ボタン」押下
 */
function onCheckAllDay() {
  // 曜日-全選択ボタン

  if (local.orX0043.frequency.values.length <= 0) {
    local.orX0043.frequency.values = ['1', '2', '3', '4', '5', '6', '7']
    // 月～日は全部チェックONの場合	-	月～日は全部チェックOFFにする
  } else if (local.orX0043.frequency.values.filter((item) => item === '0').length <= 0) {
    local.orX0043.frequency.values = []
    // 月～日は全部チェックON以外の場合	-	月～日は全部チェックONにする
  } else {
    local.orX0043.frequency.values = ['1', '2', '3', '4', '5', '6', '7']
  }

  emit('check-all-day', local.orX0043)
  emit('update:modelValue', local.orX0043)
}
</script>

<template>
  <!-- 曜日-全選択ボタン -->
  <base-mo00612
    :oneway-model-value="localOneWay.si017OneWay"
    class="btn-chk-all ml-0 mr-0 mt-0"
    @click.stop="onCheckAllDay()"
  >
    <c-v-tooltip
      activator="parent"
      location="top"
      :text="t('weekly-plan.label.full-select')"
    />
  </base-mo00612>
  <div class="flex-wrap">
    <!-- チェックボックス -->
    <base-mo01332
      :model-value="local.orX0043.frequency"
      :oneway-model-value="localOneWay.mo01332AtAnyTime"
    />
  </div>
</template>

<style scoped lang="scss">
.flex-wrap {
  display: flex !important;
  flex-wrap: wrap;
  position: relative;
}

.btn-chk-all {
  height: 78px;
  background-color: rgb(var(--v-theme-black-300)) !important;
}
:deep(.outer_div_style) {
  flex-wrap: wrap!important;
  display: flex !important;
}

</style>
