import type { AssessmentHome1InitInfoSelectOutEntity } from '~/repositories/cmn/entities/AssessmentHome1InitInfoSelectEntity'
/**
 * Or30732:有機体:［アセスメント］画面（居宅）（1）
 * GUI00794_［アセスメント］画面（居宅）（1）
 *
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */

/**
 * 複写データ
 */
export interface Or30732CopyDataType {
  /**
   * 画面モード
   */
  copyData?: AssessmentHome1InitInfoSelectOutEntity
}

/**
 * 単方向バインドModelValue
 */
export interface assessmentHomeType {
  /**
   * 事業者ID
   */
  jigyoId?: string
  /**
   * 施設ID
   */
  shisetsuId?: string
  /**
   * 利用者ID
   */
  userId?: string
  /**
   * 種別ID
   */
  shubetsuId?: string
}
