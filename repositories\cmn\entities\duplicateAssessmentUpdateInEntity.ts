/**
 * GUI00838_ ｱｾｽﾒﾝﾄ複写］「包括」画面ダイアログインティティ
 * 有機体_Or59423
 *
 * @description
 * ［ｱｾｽﾒﾝﾄ複写］「包括」画面ダイアログインティティ
 *
 * <AUTHOR>
 */

import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 情報収集複写複数画面確定リクエストパラメータタイプ
 */
export interface DuplicateAssessmentUpdateInEntity extends InWebEntity {
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 複写先ケアチェックID
   */
  tgtGdlId: string
  /**
   * 複写先期間ID
   */
  tgtSc1Id: string

  /**
   * 画面Noリスト
   */
  objList: {
    /**
     * 画面No
     */
    typeId: string
  }[]
  /**
   * 期間管理フラグ
   */
  kikanFlg: string
  /**
   * 作成日
   */
  createYmd: string
  /**
   * 記載者ID
   */
  shokuId: string
  /**
   * 計画期間ID
   */
  defSc1Id: string
  /**
   * ケアチェックID
   */
  defGdlId: string
}

/**
 * 情報収集複写複数画面確定レスポンスパラメータタイプ
 */
export interface DuplicateAssessmentUpdateOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 計画期間ID
     */
    scId?: string
    /**
     * ケアチェックID
     */
    gdlId?: string
  }
}
