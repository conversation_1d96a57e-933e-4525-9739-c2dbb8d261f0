/**
 * Or11752:選定表(アセスメント(インターライ))
 * GUI00848: 選定表(アセスメント(インターライ))
 *
 * @description
 *処理ロジック
 *
 * <AUTHOR> DAM XUAN HIEU
 */
import { Or11752Const } from './Or11752.constants'
import type { Or11752StateType } from './Or11752.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import { OrX0007Logic } from '~/components/custom-components/organisms/OrX0007/OrX0007.logic'
import { OrX0008Const } from '~/components/custom-components/organisms/OrX0008/OrX0008.constants'
import { OrX0008Logic } from '~/components/custom-components/organisms/OrX0008/OrX0008.logic'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import { OrX0009Logic } from '~/components/custom-components/organisms/OrX0009/OrX0009.logic'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { OrX0010Const } from '~/components/custom-components/organisms/OrX0010/OrX0010.constants'
import { OrX0010Logic } from '~/components/custom-components/organisms/OrX0010/OrX0010.logic'
import { Or51583Const } from '~/components/custom-components/organisms/Or51583/Or51583.constants'
import { Or51583Logic } from '~/components/custom-components/organisms/Or51583/Or51583.logic'

/**
 * Or11752:
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or11752Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or11752Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or00248Const.CP_ID(1) },
        { cpId: Or00249Const.CP_ID(1) },
        { cpId: OrX0007Const.CP_ID(1) },
        { cpId: OrX0008Const.CP_ID(1) },
        { cpId: OrX0010Const.CP_ID(1) },
        { cpId: OrX0009Const.CP_ID(1) },        
        { cpId: Or11871Const.CP_ID },
        { cpId: Or41179Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or51583Const.CP_ID(1) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(1)]?.uniqueCpId)
    Or00249Logic.initialize(childCpIds[Or00249Const.CP_ID(1)]?.uniqueCpId)
    OrX0007Logic.initialize(childCpIds[OrX0007Const.CP_ID(1)]?.uniqueCpId)
    OrX0008Logic.initialize(childCpIds[OrX0008Const.CP_ID(1)]?.uniqueCpId)
    OrX0009Logic.initialize(childCpIds[OrX0009Const.CP_ID(1)]?.uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(0)]?.uniqueCpId)
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    OrX0010Logic.initialize(childCpIds[OrX0010Const.CP_ID(1)]?.uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)]?.uniqueCpId)
    Or51583Logic.initialize(childCpIds[Or51583Const.CP_ID(1)]?.uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or11752StateType>(Or11752Const.CP_ID(1))
}
