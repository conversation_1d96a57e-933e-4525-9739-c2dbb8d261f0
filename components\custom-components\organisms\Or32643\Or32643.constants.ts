import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or32643:有機体
 * GUI00796_［アセスメント］画面（居宅）（3）
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or32643Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or32643', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * タブID
     */
    export const TAB_ID = '3'
    /**
     * メッセージID
     */
    export const MESSAGE_ID = '3'
    /**
     * 直近の入所・入院コード区分
     */
    export const SELECT_CD_KBN_RECENT_ADMISSION_HOSPITALLIZATION = 232
    /**
     * 直成年後見人制度コード区分
     */
    export const SELECT_CD_KBN_ADULT_OBSERVER_SYSTEM = 231
    /**
     * 改定フラグ H21/4
     */
    export const KAITEI_FLG_H21 = '4'
    /**
     * '介護医療院'フラグ
     */
    export const CARE_HOSPITAL_FLG_T = 'T'
    /**
     * '介護医療院'フラグ
     */
    export const CARE_HOSPITAL_FLG_F = 'F'
    /**
     * フラグ R3/4
     */
    export const KAITEI_FLG_R34 = '5'
    /**
     * 削除区分 0:削除処理なし
     */
    export const DELETE_KBN_DONT = '0'
    /**
     * 画面モード 通常
     */
    export const MODE_NORMAL = 'normal'
    /**
     * 画面モード 複写
     */
    export const MODE_COPY = 'copy'
    /**
     * 改定フラグ
     */
    /** '4':「H21/４改訂版」 */
    export const NINTEI_FORM_FLG_H21_4 = '4'
    /** '5':「R3/4改訂版」 */
    export const NINTEI_FORM_FLG_R3_4 = '5'
    /**
     * 作成日
     */
    /** 作成日:2018年4月1日 */
    export const CREATE_YMD_2018_4_1 = '2018/04/01'
    /** 作成日:2012年4月1日 */
    export const CREATE_YMD_2012_4_1 = '2012/04/01'
    /**
     * 要介護度確定
     */
    /** 「11：要支援１」 */
    export const CARE_CONFIRM_NEED_ONE = '11'
    /** 「12：要支援２」 */
    export const CARE_CONFIRM_NEED_TWO = '12'
    /** 「21：事業対象者」 */
    export const SERVICE_SUBJECT_TANTO = '21'
  }
}
