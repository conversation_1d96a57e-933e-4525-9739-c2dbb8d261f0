/**
 * GUI01235_評価表
 *
 * @description
 * 評価表
 * 静的データ
 *
 * <AUTHOR>
 */

import { getSequencedCpId } from '#imports'

export namespace TeX0013Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('TeX0013', seq)

  export namespace DEFAULT {
    /**
     * テーブルデータ入力区分:文章
     */
    export const INPUT_KBN_STRING = '1'

    /**
     * テーブルデータ入力区分:数値
     */
    export const INPUT_KBN_NUMBER = '2'

    /**
     * テーブルデータ入力区分:マスタ
     */
    export const INPUT_KBN_MASTER = '3'

    /**
     * テーブルデータ入力区分:マスタ+文章
     */
    export const INPUT_KBN_MATSER_STRING = '4'

    /**
     * テーブルデータ入力区分:日付
     */
    export const INPUT_KBN_MASTER_YMD = '5'

    /**
     * 項目年月日／月日表示連動区分
     */
    export const ITEM_TABLE_FIRST_RENDOUKBN = ['120', '121', '126', '128']

    /**
     * 項目アイコン表示フラグ:文章
     */
    export const ITEM_ICON_DISPLAY_FLG_STRING = ['122', '128']

    /**
     * 項目アイコン表示フラグ:マスタ+文章
     */
    export const ITEM_ICON_DISPLAY_FLG_MASTER_STRING = ['305']

    /**
     * 更新区分   U:更新
     */
    export const UPDATE_KBN_U = 'U'

    /**
     * 更新区分   C:新規
     */
    export const UPDATE_KBN_C = 'C'

    /**
     * 更新区分   D:削除
     */
    export const UPDATE_KBN_D = 'D'

    /**
     * デフォルト表示状態
     */
    export const IS_OPEN = false

    /**
     * 複写モード_上書き
     */
    export const COPY_MODE_OVERWRITE = '0'

    /**
     * 計画書ボタン表示連動区分
     */
    export const PLAN_BOOK_BTN_DISPLAY_RENDOU_KBN_LIST = [
      '100',
      '101',
      '102',
      '103',
      '120',
      '121',
      '122',
      '123',
      '124',
      '125',
      '126',
      '127',
      '200',
      '201',
    ]

    /**
     * ダイアログ返却値：ユーザーが「はい」を選択した場合
     */
    export const DIALOG_RESULT_CONFIRM = 'yes'

    /**
     * ダイアログ返却値：ユーザーが「いいえ」を選択した場合
     */
    export const DIALOG_RESULT_NO = 'no'

    /**
     * ダイアログ返却値：ユーザーがダイアログをキャンセルした場合
     */
    export const DIALOG_RESULT_NONE = 'cancel'

    /**
     * 期間管理フラグ：管理する
     */
    export const PLANNING_PERIOD_MANAGE = '1'

    /**
     * 画面入力データ変更チェック結果：変更なし
     */
    export const IS_EDIT_NO_CHANGE = 0

    /**
     * 画面入力データ変更チェック結果：入力内容を廃棄する、次の処理を行う
     */
    export const IS_EDIT_CHANGE_DISCARD = 1

    /**
     * 画面入力データ変更チェック結果：入力内容を保存する
     */
    export const IS_EDIT_CHANGE_SAVE = 2

    /**
     * 画面入力データ変更チェック結果：ダイアログを閉じる、次の操作を中断する
     */
    export const IS_EDIT_CHANGE_CLOSE_DIALOG = 3

    /**
     * ページ変更フラグ：前へ
     */
    export const PAGE_CHANGE_FLG_PREV = '1'

    /**
     * ページ変更フラグ：次へ
     */
    export const PAGE_CHANGE_FLG_NEXT = '2'

    /**
     * ページ変更チェック結果：最前のデータ
     */
    export const BEFORE_PAGE_CHANGE_RESULT = '1'

    /**
     * セクション名
     */
    export const SECTION_NAME = '新評価表'

    /**
     * 保存後処理タイプ
     */
    export namespace SAVE_COMPLETED_AFTER_PROCESS {
      /**
       * '0': 新規処理
       */
      export const CREATE = '0'

      /**
       * '1': 複写
       */
      export const DUPLICATE = '1'

      /**
       * '2': マスタ画面を呼び出す
       */
      export const MASTER = '2'

      /**
       * '3': タブ変更
       */
      export const TAB_CHANGE = '3'

      /**
       * '4': 計画期間変更
       */
      export const PLAN_PERIOD_CHANGE = '4'

      /**
       * '5': 歴史変更
       */
      export const HISTORY_CHANGE = '5'

      /**
       * '6': 事業所変更処理
       */
      export const OFFICE_CHANGE = '6'

      /**
       * '7': 期間ID指定処理
       */
      export const PLAN_PERIOD_ID_SELECT = '7'

      /**
       * '8': 履歴ID指定処理
       */
      export const HISTORY_ID_SELECT = '8'

      /**
       * '9': 優先順位ダイアログ表示
       */
      export const JUNI_DIALOG = '9'

      /**
       * '10': 一覧ダイアログ表示
       */
      export const ALL_VIEW = '10'

      /**
       * '11': 利用者変更
       */
      export const USER_CHANGE = '11'
    }
  }
}
