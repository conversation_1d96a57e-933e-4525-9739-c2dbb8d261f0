<script setup lang="ts">
/**
 * Or53925：有機体：(計画書（２）データ：新／旧共用) 実績情報表入力
 *
 * @description
 * (計画書（２）データ：新／旧共用) 実績情報表入力
 *
 * <AUTHOR>
 */
import { reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or53928Const } from '../Or53928/Or53928.constants'
import { Or53925Const } from './Or53925.constants'
import type {
  Or53925EventType,
  Or53925ItemsType,
  Or53925StateType,
  TransmitParam,
} from './Or53925.type'
import type { Mo01354OnewayType } from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { useScreenEventStatus, useScreenOneWayBind } from '~/composables/useComponentVue'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { PlanImplementationData } from '~/repositories/cmn/entities/PlanImplementationAchievementsRegistEntity'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo01384OnewayType, Mo01384Type } from '~/types/business/components/Mo01384Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Mo01354OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

/**
 * 分子：表用リンクボタン付きセレクトフィールド
 */
const mo01384Oneway = ref<Mo01384OnewayType>({
  items: [],
  width: '100%',
  density: 'compact',
})

// テーブル情報
const tableData = ref({
  values: {
    items: [] as Or53925ItemsType[],
    selectedRowId: '',
  },
})
// テーブル横幅
const tableWidth = ref<number>(Or53925Const.DEFAULT.TABLE_WIDTH_ZERO)
const cuurentImplementation = ref<string>('')

const localOneway = reactive({
  param: {} as TransmitParam,
  // 実績登録データ件数
  mo00615DataCountOneway: {
    itemLabel: '',
    showItemLabel: true,
  } as Mo00615OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
/** OneWayBind管理用共通処理 */
useScreenOneWayBind<Or53925StateType>({
  cpId: Or53925Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (!value) {
        return
      }
      localOneway.param = value
      setTableData()
    },
  },
})

/** EventSatatus管理用共通処理 */
const { setEvent } = useScreenEventStatus<Or53925EventType>({
  cpId: Or53925Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 選択の変更を検知、行選択イベントを通知
watch(
  () => tableData.value?.values?.selectedRowId,
  (newValue) => {
    if (!newValue) {
      return
    }
    setEvent({
      rowSelectEventFlg: true,
      selectedRow: tableData.value.values.items.find((item) => item.id === newValue),
    })
  }
)

// テーブルの横幅を設定
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (
      newValue.columnMinWidth?.columnWidths &&
      newValue.columnMinWidth.columnWidths.length > Or53928Const.DEFAULT.ZERO
    ) {
      tableWidth.value = Or53925Const.DEFAULT.TABLE_WIDTH_15
      newValue.columnMinWidth.columnWidths.forEach((item) => (tableWidth.value += item))
    }
  },
  { deep: true, immediate: true }
)
/**
 * 「実施」フォーカス
 *
 * @param item - 該当行
 */
function onFocusEvent(item: Or53925ItemsType) {
  cuurentImplementation.value = item.implementation.value
}
/**
 * 「実施」フォーカスアウト
 *
 * @param item - 該当行
 */
function onBlurEvent(item: Or53925ItemsType) {
  if (item.implementation.value !== cuurentImplementation.value) {
    item.updateKbn = UPDATE_KBN.UPDATE
    setPlanImplementationDataList()
  }
}

/**
 *  表のデータを設定
 */
function setTableData() {
  // 「実施」の選択肢の更新
  if (localOneway.param.implementationList) {
    mo01384Oneway.value.items = []
    for (const item of localOneway.param.implementationList) {
      mo01384Oneway.value.items?.push({
        title: item.textKnj,
        value: item.kbnCd,
      })
    }
  }
  const count =
    localOneway.param.planImplementationDataInfoList?.length ?? Or53928Const.DEFAULT.ZERO
  localOneway.mo00615DataCountOneway.itemLabel = count + t('label.item')
  if (localOneway.param.planImplementationDataInfoList) {
    tableData.value.values.items = []
    const list = [] as Or53925ItemsType[]
    for (const data of localOneway.param.planImplementationDataInfoList) {
      const item = {
        id: data.kjisshi2Id,
        kjisshi2Id: data.kjisshi2Id,
        kjisshi1Id: data.kjisshi1Id,
        implementation: { value: data.kbnCd } as Mo01384Type,
        kadaiKnj: data.kadaiKnj,
        tantoKnj: data.tantoKnj,
        hindoKnj: data.hindoKnj,
        cks22Ks22Id: data.cks22Ks22Id,
        ks21Id: data.ks21Id,
        dmyJisshiChk: data.dmyJisshiChk,
        gutaitekiKnj: data.gutaitekiKnj,
        choukiKnj: data.choukiKnj,
        tankiKnj: data.tankiKnj,
        kaigoKnj: data.kaigoKnj,
        svShuKnj: data.svShuKnj,
        cks22HindoKnj: data.cks22HindoKnj,
        updateKbn: UPDATE_KBN.NONE,
        kjisshi2ModifiedCnt: data.kjisshi2ModifiedCnt,
        cks22ModifiedCnt: data.cks22ModifiedCnt,
      } as Or53925ItemsType
      list.push(item)
    }
    tableData.value.values.items = list
    tableData.value.values.selectedRowId = localOneway.param.selectedRowId
  }
}

/** 計画実施データ欄情報（保存用）を設定 */
function setPlanImplementationDataList() {
  const list = [] as PlanImplementationData[]
  for (const data of tableData.value.values.items) {
    const item = {
      kjisshi2Id: data.kjisshi2Id,
      kjisshi1Id: data.kjisshi1Id,
      kbnCd: data.implementation.value,
      dmyJisshiChk: data.dmyJisshiChk,
      updateKbn: data.updateKbn,
      kjisshi2ModifiedCnt: data.kjisshi2ModifiedCnt,
    } as PlanImplementationData
    list.push(item)
  }
  setEvent({
    editFlg: true,
    planImplementationDataList: list,
  })
}
</script>
<template>
  <div style="margin: 0 24px 22px 24px">
    <c-v-row
      class="d-flex justify-end pr-2"
      :style="{ width: tableWidth + 'px' }"
    >
      <!-- 実績登録データ件数 -->
      <base-mo00615 :oneway-model-value="localOneway.mo00615DataCountOneway" />
    </c-v-row>
    <c-v-row
      class="table-header main-table"
      style="margin-top: 2px !important"
    >
      <!-- 分子：表 -->
      <base-mo-01354
        v-model="tableData"
        :oneway-model-value="props.onewayModelValue"
        class="list-wrapper"
        :style="{ width: tableWidth + 'px' }"
      >
        <!-- 実施 -->
        <template #[`item.implementation`]="{ item }">
          <!-- 分子：表用セレクトフィールド -->
          <base-mo01384
            v-model="item.implementation"
            :oneway-model-value="mo01384Oneway"
            @focus.stop="onFocusEvent(item)"
            @blur.stop="onBlurEvent(item)"
          />
        </template>
        <!-- 課題 -->
        <template #[`item.kadaiKnj`]="{ item }">
          <c-v-col class="d-flex align-center">
            <g-custom-or-x-0168
              :oneway-model-value="{
                text: item.kadaiKnj,
                lines: 1,
              }"
            ></g-custom-or-x-0168>
          </c-v-col>
        </template>
        <!-- 担当者 -->
        <template #[`item.tantoKnj`]="{ item }">
          <c-v-col class="d-flex align-center">
            <g-custom-or-x-0168
              :oneway-model-value="{
                text: item.tantoKnj,
                lines: 1,
              }"
            ></g-custom-or-x-0168>
          </c-v-col>
        </template>
        <!-- 頻度 -->
        <template #[`item.hindoKnj`]="{ item }">
          <c-v-col class="d-flex align-center">
            <g-custom-or-x-0168
              :oneway-model-value="{
                text: item.hindoKnj,
                lines: 1,
              }"
            ></g-custom-or-x-0168>
          </c-v-col>
        </template>
        <!-- 具体的 -->
        <template #[`item.gutaitekiKnj`]="{ item }">
          <c-v-col class="d-flex align-center">
            <g-custom-or-x-0168
              :oneway-model-value="{
                text: item.gutaitekiKnj,
                lines: 1,
              }"
            ></g-custom-or-x-0168>
          </c-v-col>
        </template>
        <!-- 長期 -->
        <template #[`item.choukiKnj`]="{ item }">
          <c-v-col class="d-flex align-center">
            <g-custom-or-x-0168
              :oneway-model-value="{
                text: item.choukiKnj,
                lines: 1,
              }"
            ></g-custom-or-x-0168>
          </c-v-col>
        </template>
        <!-- 短期 -->
        <template #[`item.tankiKnj`]="{ item }">
          <c-v-col class="d-flex align-center">
            <g-custom-or-x-0168
              :oneway-model-value="{
                text: item.tankiKnj,
                lines: 1,
              }"
            ></g-custom-or-x-0168>
          </c-v-col>
        </template>
        <!-- 介護 -->
        <template #[`item.kaigoKnj`]="{ item }">
          <c-v-col class="d-flex align-center">
            <g-custom-or-x-0168
              :oneway-model-value="{
                text: item.kaigoKnj,
                lines: 1,
              }"
            ></g-custom-or-x-0168>
          </c-v-col>
        </template>
        <!-- サービス種別 -->
        <template #[`item.svShuKnj`]="{ item }">
          <c-v-col class="d-flex align-center">
            <g-custom-or-x-0168
              :oneway-model-value="{
                text: item.svShuKnj,
                lines: 1,
              }"
            ></g-custom-or-x-0168>
          </c-v-col>
        </template>
        <!-- 計画書（2）頻度 -->
        <template #[`item.cks22HindoKnj`]="{ item }">
          <c-v-col class="d-flex align-center">
            <g-custom-or-x-0168
              :oneway-model-value="{
                text: item.cks22HindoKnj,
                lines: 1,
              }"
            ></g-custom-or-x-0168>
          </c-v-col>
        </template>
        <!-- ページングを非表示 -->
        <template #bottom />
      </base-mo-01354>
    </c-v-row>
  </div>
</template>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

.table-header {
  :deep(.v-col) {
    padding: 0 !important;
  }
  :deep(.v-table--density-compact .row-height) {
    height: 31px !important;
  }
  :deep(.table-cell) {
    border: none !important;
  }
  :deep(.table-cell .v-field--focused) {
    border: none !important;
    outline: none !important;
    color: rgb(var(--v-theme-key));
    box-shadow: 0 0 10px rgb(var(--v-theme-key));
  }
  :deep(.table-cell .v-field) {
    border: none !important;
    outline: none !important;
  }
  :deep(.v-input--density-compact) {
    --v-input-padding-top: 0px !important;
    --v-field-padding-bottom: 0px !important;
  }
  :deep(.v-table__wrapper tr td:not(:first-child) .full-width-field) {
    padding: 0 !important;
  }
}

.table-header :deep(.v-table__wrapper) {
  overflow: auto !important;
  border: none !important;
}

/* ヘッダーのスタイル */
.table-header :deep(.v-table__wrapper th) {
  height: 40px;
  white-space: nowrap !important;
  border-top: 1px solid rgb(var(--v-theme-black-200)) !important;
}

.table-header :deep(.v-table__wrapper th:first-child) {
  border-left: 1px solid rgb(var(--v-theme-black-200)) !important;
}

/* 明細のスタイル */
.table-header :deep(.v-table__wrapper tbody tr td) {
  height: 40px !important;
  padding: 0px 16px !important;
}

.table-header :deep(.v-table__wrapper td:first-child) {
  border-left: 1px solid rgb(var(--v-theme-black-200)) !important;
  padding: 0px !important;
}

.table-header :deep(.v-table__wrapper th:last-child) {
  border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
}

.table-header :deep(.v-table__wrapper .selected-row td) {
  background: rgb(var(--v-theme-blue-100)) !important;
}

.table-header :deep(.v-table__wrapper .v-field__input) {
  min-height: 36px !important;
  padding: 0px 0px 0px 16px !important;
}

.table-header :deep(.v-table__wrapper td:last-child) {
  border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
}

.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
</style>
