<script setup lang="ts">
import { ref, reactive, watch, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import type { OrX0129StateType } from '../OrX0129/OrX0129.type'
import { OrX0099Const } from '../OrX0099/OrX0099.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or26447Logic } from '../Or26447/Or26447.logic'
import { Or26447Const } from '../Or26447/Or26447.constants'
import { Or28809Logic } from '../Or28809/Or28809.logic'
import { Or28809Const } from '../Or28809/Or28809.constants'
import { Or27831Const } from '../Or27831/Or27831.constants'
import { Or27831Logic } from '../Or27831/Or27831.logic'
import { Or32398Const } from '../Or32398/Or32398.constants'
import { OrX0129Const } from './OrX0129.constants'
import type {
  Or27831Type,
  Or27831OnewayType,
  DailyPlanInfo,
} from '~/types/cmn/business/components/Or27831Type'

import { UPDATE_KBN } from '~/constants/classification-constants'
import type {
  OrX0129Type,
  OrX0129OnewayType,
  Or28809ReturnData,
  GamenCnikka2DataInfo,
} from '~/types/cmn/business/components/OrX0129Type'
import {
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
  useGyoumuCom,
  useScreenOneWayBind,
} from '#imports'
import type {
  OrX0099CalendarConfig,
  OrX0099CalendarEvent,
  OrX0099OnewayType,
  DblClickResult,
  OrX0099Info,
  OrX0099SegmentedEvent,
} from '~/types/cmn/business/components/OrX0099Type'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type { Or28809OneWayType } from '~/types/cmn/business/components/Or28809Type'
import type {
  Or26447OnewayType,
  careplan2ImportSelectData,
} from '~/types/cmn/business/components/Or26447Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type { OrX0156OnewayType, OrX0156Type } from '~/types/cmn/business/components/OrX0156Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { CustomClass } from '~/types/CustomClassType'
import { useValidation } from '@/utils/useValidation'
/**
 * OrX0129:有機体:操作ボタンエリア、テーブルエリア
 * GUI01057_日課計画
 *
 * @description
 * 操作ボタンエリア、テーブルエリア
 *
 * <AUTHOR>
 */
const { t } = useI18n()
const { byteLength } = useValidation()
const form = ref<VForm>()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}
/** props */
const props = defineProps<Props>()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const gyoumuCom = useGyoumuCom()
// route共有情報
const cmnRouteCom = useCmnRouteCom()

const localOneway = reactive({
  orX0129Oneway: {} as OrX0129OnewayType,
  // 予防計画ボタン
  mo00611OnewayPrevent: {
    btnLabel: t('btn.prevent-care-plan'),
    disabled: false,
    tooltipText: t('tooltip.prevent-care-plant'),
    tooltipLocation: 'bottom',
    minWidth: '65px',
  } as Mo00611OnewayType,
  // 計画書ボタン
  mo00611Oneway: {
    btnLabel: t('btn.care-plan-title'),
    disabled: false,
    tooltipText: t('tooltip.care-plan'),
    tooltipLocation: 'bottom',
    minWidth: '65px',
  } as Mo00611OnewayType,
  // その他のサービス
  orX0156Oneway: {
    itemLabel: t('label.other-service'),
    showItemLabel: true,
    isVerticalLabel: true,
    noResize: true,
    showDividerLineFlg: false,
    maxlength: '220',
    rules: [byteLength(220)],
    autoGrow: false,
    rows: '1',
    customClass: new CustomClass({ labelClass: 'pa-0 mb-1' }),
    height: '45px',
    contentStyle: 'padding:12px !important',
  } as OrX0156OnewayType,
  // GUI00937 入力支援［ケアマネ］画面
  or51775Oneway: {} as Or51775OnewayType,
  // GUI01040 予防計画書取込画面
  or28809Oneway: {} as Or28809OneWayType,
  // GUI01041 計画書（2）取込画面
  or26447Oneway: {} as Or26447OnewayType,
  // GUI01058_日課計画入力
  or27831Oneway: {} as Or27831OnewayType,
  // 日程カレンダー
  orX0099Oneway: {
    // 表の頭height
    headerHeight: 24,
    //  表の頭
    headerSlots: OrX0129Const.HEADER_SLOTS,
    firstSlots: OrX0129Const.FIRST_SLOTS,
    // テーブル幅
    tableWidth: 1309,
    // 深夜Size
    lateAtNightSize: 2,
    // 早朝Size
    morningSizeOne: 2,
    // 午前Size
    morningSizeTwo: 4,
    // 午後ize
    afternoon: 6,
    // 夜間ize
    night: 4,
    // 深夜Size
    lateAtNight: 6,
    // 間隔時間
    minutes: 60,
    // スクロール時間
    rollMinutes: 30,
    // セル高さ
    cellheight: OrX0129Const.CELL_HEIGHT,
    tableStartTime: OrX0129Const.START_TIME,
    showAllTime: true,
    timeAxisWidth: 60,
    timeSize: '14px',
    atAllTimeHeight: 54,
  } as OrX0099OnewayType,
})
const local = reactive({
  orX0129: { selectedNikkaId: '' } as OrX0129StateType,
  // その他のサービステキストエリア
  orX0156: { value: '' } as OrX0156Type,
  // GUI00937 入力支援［ケアマネ］画面
  or51775: { modelValue: '' } as Or51775Type,
  // GUI01058_日課計画入力
  or27831: { dailyPlanDetailList: [] } as Or27831Type,
})

const orX0099_1 = ref({ uniqueCpId: '' })
const or51775_1 = ref({ uniqueCpId: '' })
const or28809_1 = ref({ uniqueCpId: '' })
const or26447_1 = ref({ uniqueCpId: '' })
const or27831_1 = ref({ uniqueCpId: '' })
const calendar = ref({ reRenderCalendar: Function })
const calendarConfig = ref<OrX0099CalendarConfig>({
  events: [],
  cellHeight: 22,
  zoom: 1,
  startHour: 4,
  endHour: 4,
  allCellNumber: 24,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0099Const.CP_ID(1)]: orX0099_1.value,
  [Or51775Const.CP_ID(1)]: or51775_1.value,
  [Or28809Const.CP_ID(1)]: or28809_1.value,
  [Or26447Const.CP_ID(1)]: or26447_1.value,
  [Or27831Const.CP_ID(1)]: or27831_1.value,
})

// ダイアログ表示フラグ
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775_1.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr28809 = computed(() => {
  // Or28809のダイアログ開閉状態
  return Or28809Logic.state.get(or28809_1.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr26447 = computed(() => {
  // Or26447のダイアログ開閉状態
  return Or26447Logic.state.get(or26447_1.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr27831 = computed(() => {
  // Or27831のダイアログ開閉状態
  return Or27831Logic.state.get(or27831_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<OrX0129Type>({
  cpId: OrX0129Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

useScreenOneWayBind<OrX0129OnewayType>({
  cpId: OrX0129Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    // 操作区分
    operaFlg: (value) => {
      if (value) {
        localOneway.orX0129Oneway.operaFlg = value
      }
    },
    // ページング区分
    pagingFlg: (value) => {
      if (value) {
        localOneway.orX0129Oneway.pagingFlg = value
      }
    },
    // 期間管理フラグ(1:期間管理する、0:期間管理しない)
    kikanFlg: (value) => {
      if (value) {
        localOneway.orX0129Oneway.kikanFlg = value
      }
    },
    // 種別ID
    syubetuId: (value) => {
      if (value) {
        localOneway.orX0129Oneway.syubetuId = value
      }
    },
    // 履歴
    rirekiId: (value) => {
      if (value) {
        localOneway.orX0129Oneway.rirekiId = value
      }
    },
    // セキュリティチェック
    torikomiFlg: (value) => {
      if (value) {
        localOneway.orX0129Oneway.torikomiFlg = value
      }
    },
    // 初期設定マスタの情報
    initMasterObj: (value) => {
      if (value) {
        localOneway.orX0129Oneway.initMasterObj = value
      }
    },
    // 複写
    isCopyMode: (value) => {
      localOneway.orX0129Oneway.isCopyMode = value ?? false
    },
  },
})

// 予防計画書取込の制御
const showPlanPreventFlg = computed((): boolean => {
  return (
    !isCopyMode.value &&
    localOneway.orX0129Oneway.torikomiFlg === '1' &&
    systemCommonsStore.getSvJigyoCd === Or32398Const.SV_JIGYO_CD_50010
  )
})
// 計画書取込の制御
const showPlanFlg = computed((): boolean => {
  return (
    !isCopyMode.value &&
    localOneway.orX0129Oneway.torikomiFlg === '1' &&
    systemCommonsStore.getSvJigyoCd !== Or32398Const.SV_JIGYO_CD_50010
  )
})

// 操作区分 = 3:削除の場合、非表示
// or 期間管理フラグが「1:期間管理する」、かつ、表示用「計画対象期間」情報.ページング区分が「0:なし」の場合、非表示
const showFlg = computed((): boolean => {
  if (localOneway.orX0129Oneway.operaFlg === Or32398Const.OPERA_FLG_3) {
    return false
  }
  if (
    localOneway.orX0129Oneway.kikanFlg === Or32398Const.KIKAN_1 &&
    localOneway.orX0129Oneway.pagingFlg === Or32398Const.PAGING_0
  ) {
    return false
  }
  return true
})

// その他のサービス
const sonotaSvKnjModel = computed({
  get(): OrX0156Type {
    return { value: refValue.value?.sonotaSvKnj ?? '' }
  },
  set(val: OrX0156Type) {
    refValue.value!.sonotaSvKnj = String(val.value)
  },
})

/**
 *複写の制御
 */
const isCopyMode = computed(() => {
  return localOneway.orX0129Oneway.isCopyMode ?? false
})
/**
 * 表示・非表示制御
 */
watch(
  () => isCopyMode.value,
  (newValue) => {
    localOneway.orX0099Oneway.disabledFlg = newValue

    // 処遇実施上の留意点
    localOneway.orX0156Oneway.showEditBtnFlg = !newValue
    localOneway.orX0156Oneway.readonly = newValue
  },
  { immediate: true }
)

/**
 * 日程カレンダーの表示/非表示制御
 */
const componentKey = ref(0)
watch(
  () => showFlg.value,
  async (newVal, oldVal) => {
    if (newVal !== oldVal) {
      await nextTick()
      componentKey.value += 1
    }
  }
)

/**
 * 日課計画パタン詳細を設定する。
 */
watch(
  () => refValue.value?.cnikka2DataList,
  (newValue) => {
    if (newValue) {
      setCalendar()
    }
  },
  { deep: true }
)

/**
 * 時間帯内容設定
 */
function setCalendar() {
  const eventList = []
  const dataList =
    refValue.value?.cnikka2DataList.filter((x) => x.updateKbn !== UPDATE_KBN.DELETE) ?? []
  if (dataList.length > 0) {
    // 新しい詳細データID
    let newId = getNewNikka2Id(refValue.value!.cnikka2DataList)
    for (const e of dataList) {
      // 区分(1:共通、2:個別、3:日常) → 列index（0、2、4）
      const columnIndex =
        e.dataKbn === OrX0129Const.DATA_KBN_1 ? 0 : e.dataKbn === OrX0129Const.DATA_KBN_2 ? 2 : 4
      // 日程信息
      const infoList = [] as OrX0099Info[]
      // timeKbn (１：先頭に表示する、２：最後に表示する、0：表示しない)
      if (columnIndex === 4) {
        infoList.push({ index: columnIndex, info: e.naiyoKnj, showTimeFlg: Number(e.timeKbn) })
      } else {
        infoList.push({ index: columnIndex, info: e.naiyoKnj, showTimeFlg: Number(e.timeKbn) })
        infoList.push({ index: columnIndex + 1, info: e.tantoKnj, showTimeFlg: 0 })
      }
      // 計算週
      const event: OrX0099CalendarEvent = {
        // 日程ブロックID
        id: e.nikka2Id ? Number(e.nikka2Id) : newId--,
        // タイトル
        title: e.naiyoKnj,
        // 日程開始時間
        start: e.startTime,
        // 日程終了時間
        end: e.endTime,
        // 日程ブロック背景色
        bgColor: e.backColor,
        // 列index
        headerIndex: columnIndex,
        // 日程内容エリア揃い方向(0：左、1：右、2：中央)
        align:
          e.alignment === OrX0129Const.ALIGN_0
            ? OrX0129Const.ALIGN_LEFT
            : e.alignment === OrX0129Const.ALIGN_1
              ? OrX0129Const.ALIGN_RIGHT
              : OrX0129Const.ALIGN_CENTER,
        // フォントサイズ
        fontSize: `${e.fontSize}px`,
        // フォント色
        fontColor: e.fontColor,
        tableWidth: 760,
        // 日程信息
        info: infoList,
        /** カスタムID */
        customId: '',
        // 結合が必要な列
        mergeHeaderIndex:
          e.dataKbn === OrX0129Const.DATA_KBN_1
            ? [1]
            : e.dataKbn === OrX0129Const.DATA_KBN_2
              ? [3]
              : [],
        // 随時実施するサービス
        atAnyTime: e.zuijiFlg === OrX0129Const.CHECK_ON ? true : false,
        // 表示レイヤー
        zIndex: e.zIndex,
      }
      event.customId = String(event.id)
      eventList.push(event)
    }
  }

  calendarConfig.value.events = eventList

  // カレンダーの画面を反映
  calendar.value?.reRenderCalendar()
}

/**
 * cnikka2DataListから新しい詳細データIDを取得
 *
 * @param dataList - cnikka2DataList
 *
 * @returns - 新しい詳細データID
 */
function getNewNikka2Id(dataList: { nikka2Id: string }[]): number {
  if (!dataList || dataList.length === 0) {
    return -1
  }

  const ids = dataList.map((item) => parseInt(item.nikka2Id, 10)).filter((id) => !isNaN(id))

  if (ids.length === 0) {
    return -1
  }
  const negativeIds = ids.filter((id) => id < 0)
  if (negativeIds.length > 0) {
    const minNegativeId = Math.min(...negativeIds)
    return minNegativeId - 1
  } else {
    return -1
  }
}
/**
 * データ行のセルダブルクリック
 *
 * @param data - ダブルクリックイベントの戻りパラメータ
 */
function handleDoubleClick(data: DblClickResult) {
  if (isCopyMode.value) {
    return
  }

  // GUI01058_日課計画入力をポップアップで起動する
  if (data.clickType === OrX0129Const.CLICK_TYPE_ITEM) {
    local.or27831.dailyPlanDetailList = []
    // 一覧表示区分 「1:共通」「2:個別」「3:日常」→ 列index（0、2、4）
    let dataKbn = ''
    const lineIndex = data.lineIndex ?? 0
    if (lineIndex < 2) {
      dataKbn = OrX0129Const.DATA_KBN_1
    } else if (lineIndex < 4) {
      dataKbn = OrX0129Const.DATA_KBN_2
    } else {
      dataKbn = OrX0129Const.DATA_KBN_3
    }
    // 計画書様式
    localOneway.or27831Oneway.carePlanStyle = Number(dataKbn)
    // 該当日課情報の日課計画詳細ID
    const dataList =
      refValue.value?.cnikka2DataList?.filter(
        (x) => x.dataKbn === dataKbn && x.updateKbn !== UPDATE_KBN.DELETE
      ) ?? []
    if (dataList?.length > 0) {
      for (const item of dataList) {
        // 曜日
        const youbis = safeSplitYoubi(item.youbi)
        const linkingItem =
          item.nikkaIdList?.map((x) => ({
            nikkaId: x,
          })) ?? []
        local.or27831.dailyPlanDetailList.push({
          id: item.nikka2Id,
          //開始時間
          startTime: item.startTime,
          //終了時間
          endTime: item.endTime,
          //随時
          atAnyTime: item.zuijiFlg,
          //枠外表示
          outFrameDisplay: item.wakugaiFlg === OrX0129Const.CHECK_ON,
          //月曜日
          frequency1: youbis[0],
          //火曜日
          frequency2: youbis[1],
          //水曜日
          frequency3: youbis[2],
          //木曜日
          frequency4: youbis[3],
          //金曜日
          frequency5: youbis[4],
          //土曜日
          frequency6: youbis[5],
          //日曜日
          frequency7: youbis[6],
          //共通サービス
          commonService: dataKbn === OrX0129Const.DATA_KBN_1 ? item.naiyoKnj : '',
          //個別サービス
          individualService: dataKbn === OrX0129Const.DATA_KBN_2 ? item.naiyoKnj : '',
          //主な日常生活上の活動
          mainDailyActivities: dataKbn === OrX0129Const.DATA_KBN_3 ? item.naiyoKnj : '',
          //連動項目
          linkingItem: linkingItem,
          //共通担当者
          commonPerson: dataKbn === OrX0129Const.DATA_KBN_1 ? item.tantoKnj : '',
          //個別担当者
          individualPerson: dataKbn === OrX0129Const.DATA_KBN_2 ? item.tantoKnj : '',
          //文字サイズ
          fontSize: item.fontSize,
          //文字位置
          textPosition: item.alignment,
          //文字色
          color: item.fontColor,
          //背景
          background: item.backColor,
          //時間表示
          timeDisplay: item.timeKbn,
        } as DailyPlanInfo)
      }
    }
    // 時間行の日課計画詳細情報を新規
    if (dataList?.length === 0 || !data.event) {
      // 随時実施行セルの場合:00:00、以外の場合:クリック行.開始時間
      // 随時実施行セルの場合:00:00、以外の場合:クリック行.終了時間
      let startTime = 0
      let endTime = 0
      if (data.atAnyTimeTimeFlg !== OrX0129Const.CHECK_ON && data.rowIndex !== -1) {
        const rowIndex = data.rowIndex ?? 0
        startTime = rowIndex < 20 ? rowIndex + 4 : rowIndex - 20
        endTime = startTime + 1
      }

      // 新しい詳細データID
      local.or27831.dailyPlanDetailList.push({
        // id
        id: '',
        //開始時間
        startTime: formatTime(startTime),
        //終了時間
        endTime: formatTime(endTime),
        // 随時実施行セルの場合:1、以外の場合、0
        atAnyTime:
          data.atAnyTimeTimeFlg === OrX0129Const.CHECK_ON
            ? OrX0129Const.CHECK_ON
            : OrX0129Const.CHECK_OFF,
        //枠外表示
        outFrameDisplay: false,
        //月曜日
        frequency1: OrX0129Const.CHECK_OFF,
        //火曜日
        frequency2: OrX0129Const.CHECK_OFF,
        //水曜日
        frequency3: OrX0129Const.CHECK_OFF,
        //木曜日
        frequency4: OrX0129Const.CHECK_OFF,
        //金曜日
        frequency5: OrX0129Const.CHECK_OFF,
        //土曜日
        frequency6: OrX0129Const.CHECK_OFF,
        //日曜日
        frequency7: OrX0129Const.CHECK_OFF,
        //共通サービス
        commonService: '',
        //個別サービス
        individualService: '',
        //主な日常生活上の活動
        mainDailyActivities: '',
        //連動項目
        linkingItem: [],
        //共通担当者
        commonPerson: '',
        //個別担当者
        individualPerson: '',
        //文字サイズ
        fontSize: getFontSize(localOneway.orX0129Oneway.initMasterObj?.nikkaSizeFlg),
        //文字位置
        textPosition: localOneway.orX0129Oneway.initMasterObj?.nikkaPosFlg, //日課計画の初期設定マスタ情報.文字位置(nikka_pos_flg)
        //文字色
        color: localOneway.orX0129Oneway.initMasterObj?.nikkaFontcolor, //日課計画の初期設定マスタ情報.文字色(nikka_fontcolor)
        //背景
        background: localOneway.orX0129Oneway.initMasterObj?.nikkaBackcolor, //日課計画の初期設定マスタ情報.背景色(nikka_backcolor)
        //時間表示
        timeDisplay: localOneway.orX0129Oneway.initMasterObj?.nikkaTimeFlg,
      } as DailyPlanInfo)
    }

    Or27831Logic.state.set({
      uniqueCpId: or27831_1.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * GUI01058_日課計画入力返却情報がある場合
 *
 * @param newValue - ポップアップ返却情報
 */
function handleOr27831Confirm(newValue: Or27831Type) {
  if (newValue) {
    // ポップアップ返却の区分:計画書様式
    const dataKbn = String(localOneway.or27831Oneway.carePlanStyle)

    // ポップアップクリック前のデータ
    const beforDataList =
      refValue.value?.cnikka2DataList?.filter((x) => x.dataKbn === dataKbn) ?? []

    // ポップアップクリック前操作対象外のレコード
    const newCnikka2DataList =
      refValue.value?.cnikka2DataList?.filter((x) => x.dataKbn !== dataKbn) ?? []

    // 返却のリスト
    const dailyPlanDetailList = newValue.dailyPlanDetailList

    // ポップアップ画面のリストはない場合
    if (!dailyPlanDetailList || dailyPlanDetailList.length === 0) {
      if (Array.isArray(beforDataList) && beforDataList.length > 0) {
        for (const deleteItem of beforDataList) {
          deleteItem.updateKbn = UPDATE_KBN.DELETE
        }
        newCnikka2DataList.push(...beforDataList)
      }
      refValue.value!.cnikka2DataList = newCnikka2DataList
      return
    }

    // 新しい詳細データID
    let newId = getNewNikka2Id(refValue.value?.cnikka2DataList ?? [])

    // 返却情報を取得
    for (const item of dailyPlanDetailList) {
      // 曜日
      const youbiStr = [
        item.frequency1 ?? OrX0129Const.CHECK_OFF,
        item.frequency2 ?? OrX0129Const.CHECK_OFF,
        item.frequency3 ?? OrX0129Const.CHECK_OFF,
        item.frequency4 ?? OrX0129Const.CHECK_OFF,
        item.frequency5 ?? OrX0129Const.CHECK_OFF,
        item.frequency6 ?? OrX0129Const.CHECK_OFF,
        item.frequency7 ?? OrX0129Const.CHECK_OFF,
      ].join('')
      const itemData: GamenCnikka2DataInfo = {
        // 詳細データID
        nikka2Id: item.id === '' ? String(newId--) : item.id,
        // 日課計画ID
        nikka1Id: localOneway.orX0129Oneway.rirekiId,
        // 区分
        dataKbn: dataKbn,
        // 曜日
        youbi: youbiStr,
        //開始時間
        startTime: item.startTime ?? '',
        //終了時間
        endTime: item.endTime ?? '',
        // サービス主な日常生活上の活動
        naiyoKnj:
          dataKbn === OrX0129Const.DATA_KBN_1
            ? (item.commonService ?? '')
            : dataKbn === OrX0129Const.DATA_KBN_2
              ? (item.individualService ?? '')
              : (item.mainDailyActivities ?? ''),
        // 文字サイズ
        fontSize: item.fontSize ?? '',
        // 文字位置
        alignment: item.textPosition ?? '',
        // 文字カラー
        fontColor: item.color ?? '',
        // 背景カラー
        backColor: item.background ?? '',
        // 時間表示区分
        timeKbn: item.timeDisplay ?? '',
        // サービス担当者
        tantoKnj:
          dataKbn === OrX0129Const.DATA_KBN_1
            ? item.commonPerson
            : dataKbn === OrX0129Const.DATA_KBN_2
              ? item.individualPerson
              : '',
        // 随時実施するかのフラグ
        zuijiFlg: item.atAnyTime ?? '',
        // 枠外表示するかのフラグ
        wakugaiFlg: item.outFrameDisplay ? OrX0129Const.CHECK_ON : OrX0129Const.CHECK_OFF,
        // 日課連動項目IDリスト
        nikkaIdList: item.linkingItem?.map((i) => i.nikkaId) ?? [],
        // 更新回数
        modifiedCnt: '0',
        // 更新区分
        updateKbn: item.id === '' ? UPDATE_KBN.CREATE : '',
        zIndex: 0,
      }

      // 新規作成レコードを追加
      if (item.id === '') {
        newCnikka2DataList.push(itemData)
      } else {
        const beforData = beforDataList.filter((x) => x.nikka2Id === item.id)?.at(0)
        if (beforData) {
          // 日課計画ID
          itemData.nikka1Id = beforData.nikka1Id
          // 更新回数
          itemData.modifiedCnt = beforData.modifiedCnt
          // 既存レコードは変更、又は新規の場合
          if (
            beforData.updateKbn === UPDATE_KBN.UPDATE ||
            beforData.updateKbn === UPDATE_KBN.CREATE
          ) {
            itemData.updateKbn = beforData.updateKbn
            newCnikka2DataList.push(itemData)
          } else {
            // 既存レコードの変更判断
            const fieldsChanged = OrX0129Const.CHECK_CHANGE_FIELDS.some((field) => {
              const a = beforData[field as keyof GamenCnikka2DataInfo]
              const b = itemData[field as keyof GamenCnikka2DataInfo]

              if (field === 'nikkaIdList') {
                const arrA = a as string[]
                const arrB = b as string[]
                return JSON.stringify([...arrA].sort()) !== JSON.stringify([...arrB].sort())
              }
              return a !== b
            })

            // 変更の場合
            if (fieldsChanged) {
              itemData.updateKbn = UPDATE_KBN.UPDATE
            } else {
              itemData.nikkaIdList = beforData.nikkaIdList
              itemData.updateKbn = beforData.updateKbn
            }
            newCnikka2DataList.push(itemData)
          }
        } else {
          // ダブルクリック時作成の行
          if (Number(item.id) < 0) {
            itemData.updateKbn = UPDATE_KBN.CREATE
            newCnikka2DataList.push(itemData)
          }
        }
      }
    }
    // 返却情報外のデータは削除にする
    const newValueIds: string[] = dailyPlanDetailList.filter((x) => x.id).map((item) => item.id)
    const deleteList = beforDataList.filter((x) => !newValueIds.includes(x.nikka2Id))
    if (deleteList?.length > 0) {
      for (const deleteItem of deleteList) {
        deleteItem.updateKbn = UPDATE_KBN.DELETE
      }
      newCnikka2DataList.push(...deleteList)
    }

    const hasNegativeNonDeleteRecord = newCnikka2DataList.some((item) => {
      const idNum = Number(item.nikka2Id)
      return idNum < 0 && item.updateKbn !== UPDATE_KBN.DELETE
    })
    if (!hasNegativeNonDeleteRecord) {
      newCnikka2DataList.sort((a, b) => Number(a.nikka2Id) - Number(b.nikka2Id))
    }
    // 表示用「日課計画_詳細」リスト
    refValue.value!.cnikka2DataList = newCnikka2DataList
  }
}

/**
 * HH:mmをフォーマット
 *
 * @param time - H
 */
function formatTime(time: number) {
  const hours = time.toString().padStart(2, '0')
  return `${hours}:00`
}

/**
 * 曜日を分けて取得
 *
 * @param youbi - 曜日
 *
 * @returns - 曜日1～曜日7
 */
function safeSplitYoubi(youbi: string | null | undefined): string[] {
  const safeValue = youbi?.slice(0, 7) ?? ''
  const result = [...safeValue]
  while (result.length < 7) {
    result.push('0')
  }
  return result
}

/**
 * 日課情報オブジェクトドラッグ＆ドロップ
 *
 * @param event - 日課情報オブジェクトドラッグ＆ドロップ
 */
function handleMousedown(event: OrX0099SegmentedEvent) {
  if (isCopyMode.value) {
    return
  }
  if (event) {
    const data = refValue.value?.cnikka2DataList?.find((x) => x.nikka2Id === event.id?.toString())
    if (data) {
      const eventStartTime = eventFormatTime(event.start)
      const eventEndTime = eventFormatTime(event.end)
      if (data.startTime !== eventStartTime || data.endTime !== eventEndTime) {
        data.startTime = eventStartTime
        data.endTime = eventEndTime
        data.updateKbn = data.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : data.updateKbn
        data.zIndex = event.zIndex ?? 0
      }
    }
  }
}

/**
 * 日課情報オブジェクトドラッグ＆ドロップ 時間フォーマット
 *
 * @param dateTime - 年月日 時間
 *
 * @returns - HH:mm
 */
function eventFormatTime(dateTime: string): string {
  const date = new Date(dateTime)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

/**
 * 「計画書取込みボタンのアイコンボタン」押下
 * GUI01040 予防計画書取込画面
 */
function openGUI01040() {
  // ・操作区分 = 3:削除の場合、又は、・表示用「計画対象期間」情報.ページング区分が0:なしの場合、処理終了
  if (
    localOneway.orX0129Oneway.operaFlg === Or32398Const.OPERA_FLG_3 ||
    localOneway.orX0129Oneway.pagingFlg === Or32398Const.PAGING_0
  ) {
    return
  }
  // GUI01040 予防計画書取込画面をポップアップで起動する
  localOneway.or28809Oneway = {
    /** 親画面.事業者ID */
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    /** 親画面.施設ID */
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    /** 親画面.利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '0',
    /**  親画面.計画表ID */
    plan11Id: '',
    /** 親画面.計画期間ID */
    sc1Id: cmnRouteCom.getPlanPeriod()?.kikanId ?? '',
    /** 遷移元区分：「2：日課計画」 */
    seniKbn: '2',
    /** 親画面.計画表様式フラグ_0_A3-1枚;1_A4-3枚;2_A4-2枚 */
    itakuKkakPrtFlg: localOneway.orX0129Oneway.initMasterObj?.itakuKkakPrtFlg ?? '',
    /** 親画面.保険サービス取込(事業所名） */
    itakuKkakHsJigyoFlg: localOneway.orX0129Oneway.initMasterObj?.itakuKkakHsJigyoFlg ?? '',
    /** 親画面.頻度取込_「0:選択項目名称」の場合、「1:週〇回／月〇回」の場合、「2:随時」の場合、 */
    itakuKkakHindoFlg: localOneway.orX0129Oneway.initMasterObj?.itakuKkakHindoFlg ?? '',
  }
  // GUI01040 予防計画書取込画面をポップアップで起動する
  Or28809Logic.state.set({
    uniqueCpId: or28809_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01040 予防計画書取込画面の返回値を監視
 *
 * @param returnData - 予防計画書取込画面の返回値
 */
function handleOr28809ReturnData(returnData: Or28809ReturnData) {
  const { hokenServiceKnjList = [], svShubetuHokenKnjList = [] } = returnData
  if (hokenServiceKnjList.length === 0 && svShubetuHokenKnjList.length === 0) {
    return
  }
  // 新しい詳細データID
  let newId = getNewNikka2Id(refValue.value?.cnikka2DataList ?? [])
  //文字サイズを判断  0：極小、1：小、2：中、3：大
  const fontSize = getFontSize(localOneway.orX0129Oneway.initMasterObj?.nikkaSizeFlg)
  // 返却情報リストにより、下記新規データを表示用「日課計画_詳細」リスト追加する
  for (let i = 0; i < Math.max(hokenServiceKnjList.length, svShubetuHokenKnjList.length); i++) {
    // 返却情報リスト.当該行.サービス
    const naiyoKnj = hokenServiceKnjList[i]
    // 返却情報リスト.当該行.種別
    const tantoKnj = svShubetuHokenKnjList[i]
    const data: GamenCnikka2DataInfo = {
      nikka2Id: String(newId--),
      nikka1Id: localOneway.orX0129Oneway.rirekiId,
      dataKbn: OrX0129Const.DATA_KBN_2,
      youbi: '',
      startTime: OrX0129Const.START_TIME,
      endTime: OrX0129Const.END_TIME,
      naiyoKnj: naiyoKnj ?? '',
      fontSize: fontSize,
      // 初期設定マスタ情報.文字位置
      alignment: localOneway.orX0129Oneway.initMasterObj?.nikkaPosFlg ?? '',
      // 初期設定マスタ情報.背景色
      fontColor: localOneway.orX0129Oneway.initMasterObj?.nikkaBackcolor ?? '',
      // 初期設定マスタ情報.文字色
      backColor: localOneway.orX0129Oneway.initMasterObj?.nikkaFontcolor ?? '',
      // 初期設定マスタ情報.時間表示
      timeKbn: localOneway.orX0129Oneway.initMasterObj?.nikkaTimeFlg ?? '',
      tantoKnj: tantoKnj ?? '',
      zuijiFlg: OrX0129Const.CHECK_OFF,
      wakugaiFlg: OrX0129Const.CHECK_OFF,
      nikkaIdList: [],
      modifiedCnt: '0',
      updateKbn: UPDATE_KBN.CREATE,
      zIndex: 0,
    }
    refValue.value!.cnikka2DataList.push(data)
  }
}

/**
 * 初期設定マスタ情報.文字サイズフラグを判断、文字サイズを取得
 *
 * @param sizeFlag - 初期設定マスタ情報.文字サイズフラグ(0：極小、1：小、2：中、3：大)
 *
 * @returns 文字サイズ(6,9,12,15)
 */
function getFontSize(sizeFlag?: string): string {
  return (
    OrX0129Const.FONT_SIZES[sizeFlag as keyof typeof OrX0129Const.FONT_SIZES] ??
    OrX0129Const.FONT_SIZE_DEFAULT
  )
}

/**
 * 「計画書取込みボタンのアイコンボタン」押下
 * GUI01041 計画書（2）取込画面をポップアップで起動する
 */
function openGUI01041() {
  // ・操作区分 = 3:削除の場合、又は、・表示用「計画対象期間」情報.ページング区分が0:なしの場合、処理終了
  if (
    localOneway.orX0129Oneway.operaFlg === Or32398Const.OPERA_FLG_3 ||
    localOneway.orX0129Oneway.pagingFlg === Or32398Const.PAGING_0
  ) {
    return
  }
  // 法人ID：共通情報.法人ID
  localOneway.or26447Oneway.houjinId = systemCommonsStore.getHoujinId ?? ''
  // 事業者ID：共通情報.事業所ID
  localOneway.or26447Oneway.svJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  // 利用者ID：共通情報.利用者ID
  localOneway.or26447Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  // 種別ID：初期情報.種別ID
  localOneway.or26447Oneway.syubetuId = localOneway.orX0129Oneway.syubetuId
  // 施設ID：共通情報.施設ID
  localOneway.or26447Oneway.shisetuId = systemCommonsStore.getShisetuId ?? ''
  // 遷移元："日課計画"
  localOneway.or26447Oneway.seniMoto = '2'
  // 事業所ID：共通情報.事業所ID
  localOneway.or26447Oneway.defSvJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  // 期間管理フラグ：初期情報.期間管理フラグ
  localOneway.or26447Oneway.planManage = localOneway.orX0129Oneway.kikanFlg
  // sys略称：空
  localOneway.or26447Oneway.sys3ryaku = ''
  // 計画書様式：初期設定マスタの情報.計画表様式（予防計画書）
  localOneway.or26447Oneway.cksFlg = localOneway.orX0129Oneway.initMasterObj?.cksFlg ?? ''

  // GUI01041 計画書（2）取込画面をポップアップで起動する
  Or26447Logic.state.set({
    uniqueCpId: or26447_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01041 計画書（2）取込画面の返回値を監視
 *
 * @param rtnData - 取込画面の返回値
 */
function handleOr26447ReturnData(rtnData: careplan2ImportSelectData[]) {
  if (rtnData.length === 0) {
    return
  }
  // 新しい詳細データID
  let newId = getNewNikka2Id(refValue.value?.cnikka2DataList ?? [])
  //文字サイズを判断  0：極小、1：小、2：中、3：大
  const fontSize = getFontSize(localOneway.orX0129Oneway.initMasterObj?.nikkaSizeFlg)
  for (const item of rtnData) {
    // 新規行.開始時間
    let startTime = item.kaishiJikan
    // 新規行.終了時間
    let endTime = item.shuuryouJikan
    // サービス_曜日対象がなし、又は、サービス_曜日対象.当該行.加算フラグが「１」の場合
    if (!item.youbi || item.kasanFlg === '1') {
      startTime = OrX0129Const.START_TIME
      endTime = OrX0129Const.END_TIME
    } else {
      // 項目が「空」、「Null」の場合、又は、下記すべて項目が"00:00"の場合
      if (
        !item.kaishiJikan ||
        item.kaishiJikan === '' ||
        !item.shuuryouJikan ||
        item.shuuryouJikan === ''
      ) {
        startTime = OrX0129Const.DEFAULT_TIME
        endTime = OrX0129Const.DEFAULT_TIME
      }
    }

    const data: GamenCnikka2DataInfo = {
      nikka2Id: String(newId--),
      nikka1Id: localOneway.orX0129Oneway.rirekiId,
      dataKbn: OrX0129Const.DATA_KBN_2,
      // サービス_曜日対象がなし、又は、サービス_曜日対象.当該行.加算フラグが「１」の場合、空を設定
      youbi: item.kasanFlg === '1' ? '' : (item.youbi ?? ''),
      startTime: startTime,
      endTime: endTime,
      // 当該行.介護
      naiyoKnj: item.kaigoKnj ?? '',
      fontSize: fontSize,
      // 初期設定マスタ情報.文字位置
      alignment: localOneway.orX0129Oneway.initMasterObj?.nikkaPosFlg ?? '',
      // 初期設定マスタ情報.背景色
      fontColor: localOneway.orX0129Oneway.initMasterObj?.nikkaBackcolor ?? '',
      // 初期設定マスタ情報.文字色
      backColor: localOneway.orX0129Oneway.initMasterObj?.nikkaFontcolor ?? '',
      // 初期設定マスタ情報.時間表示
      timeKbn: localOneway.orX0129Oneway.initMasterObj?.nikkaTimeFlg ?? '',
      // サービス種
      tantoKnj: item.svShuKnj ?? '',
      zuijiFlg: OrX0129Const.CHECK_OFF,
      wakugaiFlg: OrX0129Const.CHECK_OFF,
      nikkaIdList: [],
      modifiedCnt: '0',
      updateKbn: UPDATE_KBN.CREATE,
      zIndex: 0,
    }
    refValue.value!.cnikka2DataList.push(data)
  }
}

/**
 * 「その他のサービスボタンのアイコンボタン」押下
 *
 */
function openGUI00937() {
  //GUI00937 入力支援［ケアマネ］画面をポップアップで起動する
  localOneway.or51775Oneway = {
    title: t('label.other-service'),
    screenId: '',
    bunruiId: '',
    t1Cd: OrX0129Const.B1CD_860,
    t2Cd: OrX0129Const.B2CD_6,
    t3Cd: OrX0129Const.B3CD_0,
    tableName: OrX0129Const.TABLE_NAME,
    columnName: OrX0129Const.COLUMN_NAME,
    assessmentMethod: '',
    inputContents: refValue.value?.sonotaSvKnj ?? '',
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    mode: '',
  }
  local.or51775.modelValue = refValue.value?.sonotaSvKnj ?? ''
  gyoumuCom.setGUI00937Param(localOneway.or51775Oneway, localOneway.orX0129Oneway.initMasterObj)
  //GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  Or51775Logic.state.set({
    uniqueCpId: or51775_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00937 入力支援［ケアマネ］画面の返回値がある場合
 *
 * @param or51775ConfirmValue - ポップアップ画面での返回値
 */
function handleOr51775Confirm(or51775ConfirmValue: Or51775ConfirmType) {
  if (or51775ConfirmValue) {
    const newValue = or51775ConfirmValue.value ?? ''
    // 返却情報.上書きフラグ(0:本文末に追加、1:本文上書)
    refValue.value!.sonotaSvKnj =
      or51775ConfirmValue.type === Or32398Const.OPERATION_KBN_1
        ? newValue
        : refValue.value!.sonotaSvKnj.concat(newValue)
  }
}
/**
 * バリデーション関数
 */
async function isValid() {
  return (await form.value!.validate()).valid
}

defineExpose({
  isValid,
})
</script>
<template>
  <c-v-form
    ref="form"
    class="h-100"
  >
    <div class="width-container">
      <!--予防計画-->
      <c-v-row
        v-show="showPlanPreventFlg"
        no-gutters
        class="button-class"
      >
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayPrevent"
          @click="openGUI01040()"
        />
      </c-v-row>

      <!--計画書-->
      <c-v-row
        v-show="showPlanFlg"
        no-gutters
        class="button-class"
      >
        <base-mo00611
          :oneway-model-value="localOneway.mo00611Oneway"
          @click="openGUI01041()"
        />
      </c-v-row>
      <!--日程カレンダー-->
      <c-v-row
        no-gutters
        class="table-area-class"
      >
        <div class="scroll-area">
          <g-custom-or-x-0099
            ref="calendar"
            :key="componentKey"
            :calendar-config="calendarConfig"
            :oneway-model-value="localOneway.orX0099Oneway"
            :removeflg="!showFlg"
            v-bind="orX0099_1"
            @double-click="handleDoubleClick"
            @mouse-down="handleMousedown"
          />
        </div>
      </c-v-row>
      <c-v-row
        v-show="showFlg"
        no-gutters
        class="other-btn-area pt-6" :class="{ 'pb-2': isCopyMode}"
      >
        <g-custom-or-x-0156
          v-model="sonotaSvKnjModel"
          :oneway-model-value="localOneway.orX0156Oneway"
          @on-click-edit-btn="openGUI00937()"
        />
      </c-v-row>
    </div>
  </c-v-form>
  <!--GUI01040 予防計画書取込画面-->
  <g-custom-or-28809
    v-if="showDialogOr28809"
    v-bind="or28809_1"
    :oneway-model-value="localOneway.or28809Oneway"
    @update:model-value="handleOr28809ReturnData"
  />
  <!--GUI01041 計画書（2）取込画面-->
  <g-custom-or-26447
    v-if="showDialogOr26447"
    v-bind="or26447_1"
    :oneway-model-value="localOneway.or26447Oneway"
    @update:model-value="handleOr26447ReturnData"
  />
  <!--GUI00937 入力支援［ケアマネ］画面-->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775_1"
    v-model="local.or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleOr51775Confirm"
  />

  <!--GUI01058_日課計画入力-->
  <g-custom-or-27831
    v-if="showDialogOr27831"
    v-bind="or27831_1"
    :model-value="local.or27831"
    :oneway-model-value="localOneway.or27831Oneway"
    @update:model-value="handleOr27831Confirm"
  />
</template>

<style scoped lang="scss">
.width-container {
  width: 1309px;
  margin-left: 0;
  margin-right: auto;
}

.button-class {
  padding-bottom: 19px;
  :deep(.v-btn) {
    --v-btn-height: 29px !important;
    min-height: 29px !important;
    height: 29px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  :deep(.v-btn--size-default) {
    padding: 0 12px !important;
    min-height: 29px !important;
    height: 29px !important;
  }
}

.table-area-class {
  .scroll-area {
    overflow-y: auto;
  }
}

.other-btn-area {
  display: flex;
  justify-content: center;
  width: 1309px;
  box-sizing: border-box;

  & > * {
    width: 1261px;
    max-width: 1261px;
  }
}
</style>
