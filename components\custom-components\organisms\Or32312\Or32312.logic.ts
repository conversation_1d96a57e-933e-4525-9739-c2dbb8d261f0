import { Or33787Const } from '../Or33787/Or33787.constants'
import { Or33787Logic } from '../Or33787/Or33787.logic'
import { Or32312Const } from './Or32312.constants'
import { useInitialize } from '~/composables/useComponentLogic'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or53953Const } from '~/components/custom-components/organisms/Or53953/Or53953.constants'
import { Or53953Logic } from '~/components/custom-components/organisms/Or53953/Or53953.logic'

/**
 * Or32312:GUI01182_［給付状況］画面（個別）
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or32312Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or32312Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or00248Const.CP_ID(1) },
        { cpId: Or00249Const.CP_ID(1) },
        { cpId: Or11871Const.CP_ID },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or33787Const.CP_ID(1) },
        { cpId: Or41179Const.CP_ID(1) },
        { cpId: Or53953Const.CP_ID(1) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(1)].uniqueCpId)
    Or00249Logic.initialize(childCpIds[Or00249Const.CP_ID(1)].uniqueCpId)
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or33787Logic.initialize(childCpIds[Or33787Const.CP_ID(1)].uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(1)].uniqueCpId)
    Or53953Logic.initialize(childCpIds[Or53953Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
}
