
import type { DailyTablePatternConfigInitSelectOutData } from '~/repositories/cmn/entities/DailyTablePatternConfigInitSelectEntity'
/**
 * Or01533:有機体:(日課表)日課表イメージ
 * GUI00989_日課表イメージ
 *
 * @description
 * 日課表イメージメイン画面のの構造体
 *
 * <AUTHOR> 呉李彪
 */
/**
 * データ
 */
export interface Or01533Type extends DailyTablePatternConfigInitSelectOutData {
  /**
   * 表示用「日課表詳細」リスト
   */
  dailyInfoList: Daily[]
  /**
   * 表示用「日課表詳細」リスト
   */
  allDailyInfoList: Daily[]
  /**
   * 「日課表特記事項」
   */
  tokkiKnj: TokkiKnjInfo

  /**
   * 日課表サービス例
   */
  svInfoList: sv[]
}
/**
 * 日課表サービス例
 */
export interface sv {
  /** サービス例ID */
  svId: string
  /** 区分 */
  dataKbn: string
  /** 適用フラグ */
  tekiyoFlg: string
  /** 更新回数 */
  modifiedCnt: string
  /** 更新区分 */
  updateKbn?: string
}

/**
 * 「日課表特記事項」
 */
export interface TokkiKnjInfo {
  /** ＩＤ */
  rirekiId?: string
  /** 特記事項 */
  tokkiKnj?: string
  /** 随時実施するその他のサービス */
  sonotaKnj?: string
  /** 改訂フラグ */
  kaiteiFlg?: string
  /** 更新回数 */
  modifiedCnt?: string
  /** 更新区分 */
  updateKbn?: string
}

/**
 * 日課表
 */
export interface Daily {
  /** カウンタ */
  id: string
  /** カウンタ */
  day1Id: string
  /** カウンタ */
  uuId: string
  /** 区分 */
  dataKbn: string
  /** 開始時間 */
  startTime: string
  /** 終了時間 */
  endTime: string
  /** 内容CD */
  naiyoCd: string
  /** 内容 */
  naiyoKnj: string
  /** 日常処遇サービスメモ */
  memoKnj: string
  /** 担当者 */
  tantoKnj: string
  /** 文字サイズ */
  fontSize: string
  /** 文字サイズ */
  fontSizeTitle: string
  /** 文字位置 */
  alignment: string
  /** 文字カラー */
  fontColor: string
  /** 背景カラー */
  backColor: string
  /** 時間表示区分 */
  timeKbn: string
  /** 内容担当更新区分 */
  updateKbn: string
  /** 更新回数 */
  modifiedCnt: string
  /** zIndex */
  zIndex: number
}

