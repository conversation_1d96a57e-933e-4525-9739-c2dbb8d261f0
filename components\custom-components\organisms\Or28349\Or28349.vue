<script setup lang="ts">
/**
 * Or28349:有機体:特記事項登録 予防評価表
 * GUI01146_特記事項登録 予防評価表
 *
 * @description
 * 特記事項登録 予防評価表
 *
 * <AUTHOR> 李晨昊
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, computed, watch } from 'vue'
import { Gui00038Logic } from '../Gui00038/Gui00038.logic'
import { Gui00038Const } from '../Gui00038/Gui00038.constants'
import { Or26285Logic } from '../Or26285/Or26285.logic'
import { Or26285Const } from '../Or26285/Or26285.constants'
import { OrX0130Const } from '../OrX0130/OrX0130.constants'
import type { Or28349StateType, RowData, specialNoteMattersRegistInitialItem } from './Or28349.type'
import { Or28349Const } from './Or28349.constants'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenTwoWayBind,
} from '~/composables/useComponentVue'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { Mo01408OnewayType, Mo01408Type } from '~/types/business/components/Mo01408Type'

import type {
  Or28349OnewayType,
  specialNoteMattersRegistInitialListType,
  Or28349Type,
  specialNoteMattersItem,
} from '~/types/cmn/business/components/Or28349Type'
import type {
  SpecialNoteMattersRegistInitialInfoInEntity,
  SpecialNoteMattersRegistInitialInfoOutEntity,
} from '~/repositories/cmn/entities/SpecialNoteMattersRegistInitialSelectEntity'
import type {
  SpecialNoteMattersInitSelectInEntity,
  SpecialNoteMattersInitSelectOutEntity,
  SpecialNoteMattersUpdateInEntity,
  SpecialNoteMattersUpdateOutEntity,
} from '~/repositories/cmn/entities/SpecialNoteMattersInitSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01334Items, Mo01334OnewayType } from '~/types/business/components/Mo01334Type'
import { useValidation } from '@/utils/useValidation'
import type { Or26285OnewayType } from '~/types/cmn/business/components/Or26285Type'

const { byteLength } = useValidation()
const { t } = useI18n()
//const validation = useValidation()
const mo01408Oneway = ref<Mo01408OnewayType>({
  showItemLabel: Or28349Const.DEFAULT.SHOW_ITEM_LABEL,
  isRequired: Or28349Const.DEFAULT.IS_REQUIRED,
  items: Or28349Const.DEFAULT.ITEMS,
  multiple: false,
  width: '300px',
})
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or28349Type
  onewayModelValue: Or28349OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
let isDelete = Or28349Const.DEFAULT.IS_DELETE
let isEdit = Or28349Const.DEFAULT.IS_EDIT
// ポスト最小幅
const columnMinWidth = ref<number[]>([137, 137, 180, 429])
// ソート優先度
const sortBy = ref([{ key: 'selfId', order: 'desc' }])
const or21814_1 = ref({ uniqueCpId: '' })
const or21814_2 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const gui00038 = ref({ uniqueCpId: Gui00038Const.CP_ID(0) })
const or26285 = ref({ uniqueCpId: Or26285Const.CP_ID(0) })
useSetupChildProps(props.uniqueCpId, {
  [Gui00038Const.CP_ID(0)]: gui00038.value,
  [Or26285Const.CP_ID(0)]: or26285.value,
})
const defaultInitialValue: specialNoteMattersRegistInitialListType = {
  jigyoRyakuKnj: '0',
  svJigyoList: [],
}
const localOneway = reactive({
  or28349: {
    ...defaultInitialValue,
    ...props.onewayModelValue,
  },
  tableStyle: 'width:880px',
  // 特記事項登録ダイアログ
  mo00024Oneway: {
    width: '900px',
    height: '620px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.special-remarks-registration'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  mo01274Oneway: {
    maxLength: '200',
    rules: [byteLength(200)],
  },

  // 特記事項データテーブルのヘッダー
  headers: [
    // 利用者番号
    {
      title: t('label.plan-user-id'), // ヘッダーに表示される名称
      key: 'selfId',
      width: '150px',
      sortable: true,
    },
    // 利用者名
    {
      title: t('label.user-name'), // ヘッダーに表示される名称
      key: 'userid',
      width: '150px',
      sortable: true,
    },
    // 被保険者番号
    {
      title: t('label.insured-person-number-1'), // ヘッダーに表示される名称
      key: 'hiHokenNo',
      width: '180px',
      sortable: true,
    },
    // 特記事項
    {
      title: t('label.special-notes'), // ヘッダーに表示される名称
      key: 'tokkiKnj',
      width: '500px',
      sortable: true,
    },
  ],
  mo01334Oneway: {
    height: '455px',
    headers: [],
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    density: 'default',
    items: [],
  } as Mo01334OnewayType,
})

const local = reactive({
  specialNoteMattersList: [],
})

/**
 * 一覧の実際表示行数
 */
const showTableData = computed(() => {
  if (
    localOneway.mo01334Oneway.items === undefined ||
    localOneway.mo01334Oneway.items.length === 0
  ) {
    return { trueLastIndex: -1, showLastIndex: -1 }
  } else {
    const showList = localOneway.mo01334Oneway.items.filter((item) => item.updateKbn !== 'D')
    return {
      trueLastIndex: localOneway.mo01334Oneway.items.length - 1,
      showLastIndex: Number(showList[showList.length - 1].id),
    }
  }
})

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

// 追加行のindex
const addRowIndex = ref<number>(0)

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}
const mo01265OnewayType: Mo01265OnewayType = {
  btnLabel: t('btn.delete'),
  width: '90px',
  prependIcon: 'delete',
  tooltipText: t('tooltip.delete-data'),
}
// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.save'),
  width: '90px',
  tooltipText: t('tooltip.save'),
}
const mo00611Oneway2 = ref<Mo00009OnewayType>({
  // ボタンラベルadd-new
  btnIcon: 'edit_square',
})
// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28349Const.DEFAULT.IS_OPEN,
})

const tableData = ref<RowData[]>([])

const or26285Oneway = ref<Or26285OnewayType>({
  // 選択モート: 単一
  selectMode: OrX0130Const.DEFAULT.TANI,
  // 自事業所id
  shienId: localOneway.or28349.shienId,
  // 提供年月
  yymmYm: localOneway.or28349.objectYm,
  // 対象事業者ID
  svJigyoId:
    selectedItemIndex.value >= 0
      ? (localOneway.mo01334Oneway.items[selectedItemIndex.value] as RowData).svJigyoId
      : localOneway.or28349.svJigyoId,
  // 利用者ID
  userId: localOneway.or28349.userId,
  // 利用者名
  sMisentakuName: localOneway.or28349.userName,

  iUseJigyo: '1',
  local: '',
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Mo01408Type>({
  cpId: Or28349Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

const { setState } = useScreenOneWayBind<Or28349StateType>({
  cpId: Or28349Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28349Const.DEFAULT.IS_OPEN
    },
  },
})
const showDialog = computed(() => {
  return Gui00038Logic.state.get(gui00038.value.uniqueCpId)?.isOpen ?? false
})
// モーダルウィンドウ
const showDialogOr26285 = computed(() => {
  return Or26285Logic.state.get(or26285.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 警告メッセージボックス
 *
 * @param text - message
 *
 * @returns 警告メッセージボックス選択結果（yes, no）
 */
async function showOr21813MsgReTwoBtn(text: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: text,
      firstBtnType: 'blank',
      firstBtnLabel: t('btn.no'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.yes'),
    },
  })

  // 確認ダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813_1.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.secondBtnClickFlg) {
          result = 'yes'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
const handleConfirm = (result: Mo01334Items) => {
  // 子コンポーネントのデータを受信する
  isEdit = true
  result.id = String(addRowIndex.value++)
  localOneway.mo01334Oneway.items.push(result)
}

/**
 * メッセージの開閉
 *
 * @param text - message
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function showOr21814MsgThreeBtn(text: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: text,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_2.value.uniqueCpId)
        let result = 'no' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }
        if (event?.closeBtnClickFlg) {
          result = 'cancel'
        }
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_2.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgOneBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.ok'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  isDelete = true
  // 特記事項登録初期情報取得
  const inputData: SpecialNoteMattersRegistInitialInfoInEntity = {
    shienId: props.onewayModelValue.shienId || '',
    svJigyoIdList: props.onewayModelValue.svJigyoIdList || [],
  }
  const resData: SpecialNoteMattersRegistInitialInfoOutEntity = await ScreenRepository.select(
    'specialNoteMattersRegistInitialSelect',
    inputData
  )
  if (resData.statusCode === Or28349Const.DEFAULT.SUCCESS) {
    localOneway.or28349.jigyoRyakuKnj = resData.data.jigyoRyakuKnj
    const newArray = resData.data.svJigyoList.map((item: specialNoteMattersRegistInitialItem) => {
      let title = item.svKindCd + ' : ' + item.jigyoKnj
      if (item.svKindCd.startsWith('A')) {
        title = '     ' + item.jigyoKnj
      }
      return {
        title: title,
        value: item.svJigyoId,
      }
    })
    mo01408Oneway.value.items = newArray
    refValue.value = { value: newArray[0].value }
    //refValue.value に値が設定されている場合
    if (refValue.value) {
      void changeList()
      selectRow(0)
    }
  }
}
/** 特記事項対象利用者選択画面(Or26285) */
const onClickOpenDialogOr26285 = () => {
  console.log(or26285Oneway)
  Or26285Logic.state.set({
    uniqueCpId: or26285.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
  isDelete = false
}
/**
 * リスト情報を削除する
 *
 */
const deleteRow = async () => {
  if (isDelete === false && selectedItemIndex.value >= 0) {
    const dialogResult = await showOr21813MsgReTwoBtn(t('message.i-cmn-10287'))
    switch (dialogResult) {
      case 'yes': {
        // localOneway.mo01334Oneway.items.splice(selectedItemIndex.value, 1)
        if (selectedItemIndex.value !== null) {
          const selectedRow = localOneway.mo01334Oneway.items[selectedItemIndex.value] as RowData
          // 選択された行が新規行の場合
          if (selectedRow.updateKbn === 'C') {
            localOneway.mo01334Oneway.items.splice(selectedItemIndex.value, 1)
          } else {
            selectedRow.updateKbn = 'D'
          }
        }

        if (selectedItemIndex.value <= showTableData.value.showLastIndex) {
          for (; selectedItemIndex.value <= showTableData.value.showLastIndex; ) {
            if (localOneway.mo01334Oneway.items[selectedItemIndex.value].updateKbn !== 'D') {
              break
            } else {
              selectedItemIndex.value = selectedItemIndex.value + 1
            }
          }
        } else {
          selectedItemIndex.value = showTableData.value.showLastIndex
        }

        isEdit = true
        break
      }
      case 'no':
        break
    }
  }
}

/** データの更新 */
const updateList = async () => {
  const upDateList = localOneway.mo01334Oneway.items.map((item) => {
    return {
      ...(item as RowData),
      tokkiKnj: (item as RowData).tokkiKnj.value,
    }
  })
  const inputData: SpecialNoteMattersUpdateInEntity = {
    specialNoteMattersList: [upDateList[selectedItemIndex.value]],
  }
  const resData: SpecialNoteMattersUpdateOutEntity = await ScreenRepository.update(
    'specialNoteMattersUpdate',
    inputData
  )
  if (resData.statusCode === Or28349Const.DEFAULT.SUCCESS) {
    setState({ isOpen: false })
  }
}
/**
 * 閉じるボタン押下時
 */
async function close() {
  if (isEdit) {
    const dialogResult = await showOr21814MsgThreeBtn(t('message.i-cmn-10557'))
    switch (dialogResult) {
      case 'yes': {
        void updateList()
        break
      }
      case 'no':
        setState({ isOpen: false })
        break
      case 'cancel':
        break
      default:
        break
    }
  } else {
    setState({ isOpen: false })
  }
}

watch(
  () => tableData.value,
  (newValue) => {
    for (const item of newValue) {
      if (item.updateKbn === 'C' || item.updateKbn === 'D') {
        continue
      }
      if (item.tokkiKnj.value !== item.tokkiKnjOld) {
        item.updateKbn = 'U'
        isEdit = true
      }
    }
  },
  { deep: true }
)

/**
 * 「保存」ボタン押下
 */
const onConfirmBtn = () => {
  if (isEdit) {
    void updateList()
  } else {
    showOr21814MsgOneBtn(t('message.i-cmn-21800'))
  }
}
/**
 * 「特記事項」値変更
 */
const changeList = async () => {
  console.log('props.onewayModelValue', props.onewayModelValue)
  const inputData: SpecialNoteMattersInitSelectInEntity = {
    yymmYm: props.onewayModelValue.objectYm || '',
    shienId: props.onewayModelValue.shienId || '',
    svJigyoId: refValue.value?.value?.toString() ?? '',
  }
  const resData: SpecialNoteMattersInitSelectOutEntity = await ScreenRepository.select(
    'specialNoteMattersSelect',
    inputData
  )
  if (resData.statusCode === Or28349Const.DEFAULT.SUCCESS) {
    local.specialNoteMattersList = resData.data.specialNoteMattersList
    addRowIndex.value = resData.data.specialNoteMattersList.length

    tableData.value = resData.data.specialNoteMattersList?.map(
      (item: specialNoteMattersItem, index) => {
        //利用者idが0の場合、以下の固定値をセット
        if (item.userid === '0') {
          return {
            id: String(index),
            ...item,
            tokkiKnj: { value: item.tokkiKnj },
            tokkiKnjOld: item.tokkiKnj,
            selfId: '',
            updateKbn: '',
            userName: '(連絡事項)',
            hHokenNo: '',
          }
        }
        const obj = props.onewayModelValue.userList.find((obj) => obj.userid === item.userid)
        // /**利用者id */
        // userid: string
        // /**利用者番号 */
        // selfId: string
        return {
          id: String(index),
          ...item,
          tokkiKnj: { value: item.tokkiKnj },
          tokkiKnjOld: item.tokkiKnj,
          updateKbn: '',
          userid: obj?.userid ?? '',
          selfId: obj?.selfId ?? '',
          userName: (obj?.nameKnj ?? '') + ' ' + (obj?.name2Knj ?? ''),
          hHokenNo: obj?.hiHokenNo ?? '',
        }
      }
    )

    localOneway.mo01334Oneway.items = tableData.value
  }
}
/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or28349StateType>({
  cpId: Or28349Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    showItemLabel: (value) => {
      mo01408Oneway.value.showItemLabel = value
    },
    isRequired: (value) => {
      mo01408Oneway.value.isRequired = value
    },
    items: (value) => {
      mo01408Oneway.value.items = value
    },
  },
})
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)
/**
 * 「サービス提供事業所」の選択状態変更
 */
watch(
  () => refValue.value,
  async (newValue, oldValue) => {
    if (newValue?.value !== oldValue?.value) {
      if (isEdit) {
        const dialogResult = await showOr21814MsgThreeBtn(t('message.i-cmn-10557'))
        switch (dialogResult) {
          case 'yes': {
            void updateList()
            break
          }
          case 'no':
            setState({ isOpen: false })
            break
          case 'cancel':
            void changeList()
            break
        }
      } else {
        void changeList()
      }
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            style="padding-bottom: 0px !important"
          >
            <span>
              {{ t('label.official-business') }}：{{ localOneway.or28349.jigyoRyakuKnj }}</span
            >
          </c-v-col>
          <c-v-col
            cols="12"
            style="padding-bottom: 0px !important"
          >
            <span>
              {{ t('label.reference-month-year') }}：{{
                props.onewayModelValue.objectYm || 'YYYY/MM'
              }}</span
            >
          </c-v-col>
          <c-v-col
            cols="12"
            style="padding-bottom: 0px !important; margin-top: -15.5px !important"
          >
            <c-v-row
              no-gutters
              center
            >
              <c-v-col
                cols="2"
                style="margin: auto"
              >
                <span> {{ t('label.service-offer-office') }}</span></c-v-col
              >
              <c-v-col cols="10">
                <base-mo01408
                  v-model="refValue"
                  :oneway-model-value="mo01408Oneway"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <c-v-col
            cols="12"
            style="padding-bottom: 0px !important; margin-top: -15.5px !important"
          >
            <c-v-row
              space-between
              no-gutters
            >
              <c-v-col cols="10">
                <base-mo01265
                  :oneway-model-value="mo01265OnewayType"
                  class="btn mb-2"
                  @click="deleteRow()"
                >
                </base-mo01265>
              </c-v-col>
              <c-v-col
                cols="2"
                class="rghit-btn"
              >
                <span style="margin: auto 0">{{ t('btn.add-new') }}</span>
                <base-mo00009
                  :oneway-model-value="mo00611Oneway2"
                  class="mt-1"
                  @click="onClickOpenDialogOr26285()"
                >
                </base-mo00009>
              </c-v-col>
            </c-v-row>
            <!-- 確定ボタン-->
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          center
        >
          <c-v-col cols="12">
            <!-- 履歴選択一覧 -->
            <c-v-data-table
              v-model:sort-by="sortBy"
              v-resizable-grid="{ columnWidths: columnMinWidth }"
              :headers="localOneway.headers"
              :items="localOneway.mo01334Oneway.items"
              fixed-header
              class="table-wrapper"
              height="360px"
              hide-default-footer
              hover
            >
              <template #item="{ item, index }">
                <tr
                  v-if="item.updateKbn != 'D'"
                  :class="{ 'select-row': selectedItemIndex === Number(item.id) }"
                  @click="selectRow(index)"
                >
                  <td>
                    {{ item.selfId }}
                  </td>
                  <td>
                    {{ item.userName }}
                  </td>
                  <td>
                    {{ item.hHokenNo }}
                  </td>
                  <td class="text-padding">
                    <base-mo01274
                      v-model="item.tokkiKnj"
                      :one-model-value="localOneway.mo01274Oneway"
                    ></base-mo01274>
                  </td>
                </tr>
              </template>
            </c-v-data-table>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
      <g-base-or21814 v-bind="or21814_1"> </g-base-or21814>
      <g-base-or21814 v-bind="or21814_2"> </g-base-or21814>
      <!-- 入力支援画面 -->
      <g-custom-gui-00038
        v-if="showDialog"
        v-bind="gui00038"
      />
      <g-custom-or26285
        v-if="showDialogOr26285"
        v-bind="or26285"
        :oneway-model-value="or26285Oneway"
        @confirm="handleConfirm"
      />
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway"
          class="mr-2"
          @click="close"
        >
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          class="mr-2"
          :oneway-model-value="mo00609Oneway"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
:deep(.table-wrapper .v-table__wrapper td) {
  border-bottom: 1px solid #ddd !important;
}
.rghit-btn {
  display: flex;
  justify-content: end;
  padding: 8px 4px 8px 0 !important;
}
.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
// 選択した行のCSS
.select-row {
  background: rgb(var(--v-theme-blue-100));
}
</style>
