import { HttpResponse, type ResponseResolver } from 'msw'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { Or41364SaveInEntity } from '~/components/custom-components/organisms/Or41364/Or41364SaveEntity.ts'

import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useNuxtApp } from '#app'

/**
 * （職員管理）職員編集ダイアログ 保存ボタン押下時用モック
 *
 * @description
 * （職員管理）職員編集ダイアログで保存ボタン押下時のリクエストを受け取る。
 */
export function handler(request: Or41364SaveInEntity) {
  const business = request

  const $log = useNuxtApp().$log as DebugLogPluginInterface
  $log.debug('ueUpdateParm2 保存実行 parameters→' + JSON.stringify(business.parameters))

  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {},
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
