import { getSequencedCpId } from '#imports'

/**
 * Or26317：有機体：表用時間テキストフィールド（オプションメニュー付き）
 * 静的データ
 *
 * <AUTHOR> PHAM TIEN THANH
 */
export namespace Or26317Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or26317', seq)

  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * スケジュールアイコン
     */
    export const SCHEDULE_ICON = 'schedule'

    /**
     * キャンセルアイコン
     */
    export const CANCEL_ICON = 'cancel'

    /**
     * 範囲あり
     */
    export const WITH_RANGE = 'WITH_RANGE'

    /**
     * 範囲なし
     */
    export const NO_RANGE = 'NO_RANGE'

    /**
     * Escapeキー
     */
    export const ESCAPE = 'Escape'
  }
}
