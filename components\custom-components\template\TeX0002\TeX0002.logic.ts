import { Or28992Const } from '../../organisms/Or28992/Or28992.constants'
import { Or28992Logic } from '../../organisms/Or28992/Or28992.logic'
import { Or30732Const } from '../../organisms/Or30732/Or30732.constants'
import { Or30732Logic } from '../../organisms/Or30732/Or30732.logic'
import { Or33097Const } from '../../organisms/Or33097/Or33097.constants'
import { Or29241Logic } from '../../organisms/Or29241/Or29241.logic'
import { Or29241Const } from '../../organisms/Or29241/Or29241.constants'
import { Or29242Logic } from '../../organisms/Or29242/Or29242.logic'
import { Or29242Const } from '../../organisms/Or29242/Or29242.constants'
import { OrD2002Const } from '../../organisms/OrD2002/OrD2002.constants'
import { OrD2002Logic } from '../../organisms/OrD2002/OrD2002.logic'
import { Or33097Logic } from '../../organisms/Or33097/Or33097.logic'
import { OrX0001Const } from '../../organisms/OrX0001/OrX0001.constants'
import { OrX0001Logic } from '../../organisms/OrX0001/OrX0001.logic'
import { Or30149Const } from '../../organisms/Or30149/Or30149.constants'
import { Or30149Logic } from '../../organisms/Or30149/Or30149.logic'
import { Or11131Const } from '../../organisms/Or11131/Or11131.constants'
import { Or11131Logic } from '../../organisms/Or11131/Or11131.logic'
import { Or32081Const } from '../../organisms/Or32081/Or32081.constants'
import { Or32081Logic } from '../../organisms/Or32081/Or32081.logic'
import { Or32643Const } from '../../organisms/Or32643/Or32643.constants'
import { Or32643Logic } from '../../organisms/Or32643/Or32643.logic'
import { Or27562Const } from '../../organisms/Or27562/Or27562.constants'
import { Or27562Logic } from '../../organisms/Or27562/Or27562.logic'
import { OrX0009Logic } from '../../organisms/OrX0009/OrX0009.logic'
import { OrX0009Const } from '../../organisms/OrX0009/OrX0009.constants'
import { OrT0003Const } from '../../organisms/OrT0003/Ort0003.constants'
import { Or35672Const } from '../../organisms/Or35672/Or35672.constants'
import { Or35672Logic } from '../../organisms/Or35672/Or35672.logic'
import { OrX0115Logic } from '../../organisms/OrX0115/OrX0115.logic'
import { OrX0115Const } from '../../organisms/OrX0115/OrX0115.constants'
import { Or28326Const } from '../../organisms/Or28326/Or28326.constants'
import { Or28326Logic } from '../../organisms/Or28326/Or28326.logic'
import { Or13844Const } from '../../organisms/Or13844/Or13844.constants'
import { Or13844Logic } from '../../organisms/Or13844/Or13844.logic'
import { Or13872Const } from '../../organisms/Or13872/Or13872.constants'
import { Or13872Logic } from '../../organisms/Or13872/Or13872.logic'
import { Or13850Const } from '../../organisms/Or13850/Or13850.constants'
import { Or13850Logic } from '../../organisms/Or13850/Or13850.logic'
import { Or57082Const } from '../../organisms/Or57082/Or57082.constants'
import { Or57082Logic } from '../../organisms/Or57082/Or57082.logic'
import { Or26257Const } from '../../organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '../../organisms/Or26257/Or26257.logic'
import { Or00386Const } from '../../organisms/Or00386/Or00386.constants'
import { Or00386Logic } from '../../organisms/Or00386/Or00386.logic'
import { Or01997Const } from '../../organisms/Or01997/Or01997.constants'
import { Or01997Logic } from '../../organisms/Or01997/Or01997.logic'
import { Or31535Const } from '../../organisms/Or31535/Or31535.constants'
import { Or31535Logic } from '../../organisms/Or31535/Or31535.logic'
import { Or29958Const } from '../../organisms/Or29958/Or29958.constants'
import { Or29958Logic } from '../../organisms/Or29958/Or29958.logic'
import { TeX0002Const } from './TeX0002.constants'
import type { TeX0002EventType, TeX0002StateType } from './TeX0002.type'
import {
  useEventStatusAccessor,
  useInitialize,
  useOneWayBindAccessor,
  useTwoWayBindAccessor,
} from '~/composables/useComponentLogic'
import type { TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'

/**
 * TeX0002:［アセスメント］画面（居宅）テンプレート
 * GUI00794_［アセスメント］画面（居宅）（1）
 * GUI00795_［アセスメント］画面（居宅）（2）
 * GUI00796_［アセスメント］画面（居宅）（3）
 * GUI00797_［アセスメント］画面（居宅）（4）
 * GUI00798_［アセスメント］画面（居宅）（5）
 * GUI00799_［アセスメント］画面（居宅）（6①）
 * GUI00800_［アセスメント］画面（居宅）（6②）
 * GUI00801_［アセスメント］画面（居宅）（6③-④）
 * GUI00802_［アセスメント］画面（居宅）（6⑤）
 * GUI00803_［アセスメント］画面（居宅）（6⑥）
 * GUI00804_［アセスメント］画面（居宅）（6医）
 * GUI00805_［アセスメント］画面（居宅）（7まとめ）
 * GUI00806_［アセスメント］画面（居宅）（7スケジュール）
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace TeX0002Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: TeX0002Const.CP_ID(0),
      uniqueCpId,
      initOneWayState: {
        isSaveCompleted: false, // 保存完了フラグ
        tabChangeSaveCompleted: false, // タブ変更保存完了フラグ
      },
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or00248Const.CP_ID(0) },
        { cpId: Or35672Const.CP_ID(0) },
        { cpId: Or27562Const.CP_ID(0) },
        { cpId: Or13844Const.CP_ID(0) },
        { cpId: Or13850Const.CP_ID(0) },
        { cpId: Or13872Const.CP_ID(0) },
        { cpId: OrX0115Const.CP_ID(0) },
        { cpId: Or28326Const.CP_ID(0) },
        { cpId: OrX0009Const.CP_ID(0) },
        { cpId: Or41179Const.CP_ID(0) },
        { cpId: OrT0003Const.CP_ID(0) },
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(2) },
        { cpId: Or21814Const.CP_ID(3) },
        { cpId: Or11871Const.CP_ID },
        { cpId: OrD2002Const.CP_ID(0) },
        { cpId: Or28992Const.CP_ID(0) },
        { cpId: Or11131Const.CP_ID(0) },
        { cpId: Or32081Const.CP_ID(0) },
        { cpId: OrX0001Const.CP_ID(0) },
        { cpId: Or29241Const.CP_ID(0) },
        { cpId: Or29242Const.CP_ID(0) },
        { cpId: Or30732Const.CP_ID(1) },
        { cpId: Or33097Const.CP_ID(0) },
        { cpId: Or30149Const.CP_ID(0) },
        { cpId: Or32643Const.CP_ID(0) },
        { cpId: Or26257Const.CP_ID(0) },
        { cpId: Or57082Const.CP_ID(0) },
        { cpId: Or00386Const.CP_ID(0) },
        { cpId: Or01997Const.CP_ID(0) },
        { cpId: Or31535Const.CP_ID(0) },
        { cpId: Or29958Const.CP_ID(0) },
      ],
      // 編集フラグ不要
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(0)].uniqueCpId)
    Or35672Logic.initialize(childCpIds[Or35672Const.CP_ID(0)].uniqueCpId)
    Or27562Logic.initialize(childCpIds[Or27562Const.CP_ID(0)].uniqueCpId)
    Or13844Logic.initialize(childCpIds[Or13844Const.CP_ID(0)].uniqueCpId)
    Or13850Logic.initialize(childCpIds[Or13850Const.CP_ID(0)].uniqueCpId)
    Or13872Logic.initialize(childCpIds[Or13872Const.CP_ID(0)].uniqueCpId)
    OrX0115Logic.initialize(childCpIds[OrX0115Const.CP_ID(0)].uniqueCpId)
    Or28326Logic.initialize(childCpIds[Or28326Const.CP_ID(0)].uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(0)].uniqueCpId)
    OrX0009Logic.initialize(childCpIds[OrX0009Const.CP_ID(0)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(2)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(3)].uniqueCpId)
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    OrD2002Logic.initialize(childCpIds[OrD2002Const.CP_ID(0)].uniqueCpId)
    Or01997Logic.initialize(childCpIds[Or01997Const.CP_ID(0)].uniqueCpId)
    Or28992Logic.initialize(childCpIds[Or28992Const.CP_ID(0)].uniqueCpId)
    OrX0001Logic.initialize(childCpIds[OrX0001Const.CP_ID(0)].uniqueCpId)
    Or30732Logic.initialize(childCpIds[Or30732Const.CP_ID(1)].uniqueCpId)
    // Or33097Logic.initialize(childCpIds[Or33097Const.CP_ID(0)].uniqueCpId)
    // Or29241Logic.initialize(childCpIds[Or29241Const.CP_ID(0)].uniqueCpId)
    // Or30149Logic.initialize(childCpIds[Or30149Const.CP_ID(0)].uniqueCpId)
    // Or11131Logic.initialize(childCpIds[Or11131Const.CP_ID(0)].uniqueCpId)
    // Or32081Logic.initialize(childCpIds[Or32081Const.CP_ID(0)].uniqueCpId)
    // Or29242Logic.initialize(childCpIds[Or29242Const.CP_ID(0)].uniqueCpId)
    // Or32643Logic.initialize(childCpIds[Or32643Const.CP_ID(0)].uniqueCpId)
    // Or26257Logic.initialize(childCpIds[Or26257Const.CP_ID(0)].uniqueCpId)
    // Or57082Logic.initialize(childCpIds[Or57082Const.CP_ID(0)].uniqueCpId)
    // Or00386Logic.initialize(childCpIds[Or00386Const.CP_ID(0)].uniqueCpId)
    // Or31535Logic.initialize(childCpIds[Or31535Const.CP_ID(0)].uniqueCpId)
    // Or29958Logic.initialize(childCpIds[Or29958Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<TeX0002Type>(TeX0002Const.CP_ID(0))

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<TeX0002StateType>(TeX0002Const.CP_ID(0))

  /**
   * 自身のEventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useEventStatusAccessor<TeX0002EventType>(TeX0002Const.CP_ID(0))
}
