<script setup lang="ts">
/**
 * Mo01408：分子：検索付きセレクトフィールド
 *
 * @description
 * 検索付きセレクトフィールドコンポーネント。
 * 検索によるセレクトフィールドの絞込み機能を持つ。
 *
 * @example <caption>単一選択の例</caption>
 * ``` typescript
 * const mo01408 = ref<Mo01408Type>({
 *   value: null,
 * })
 * const mo01408Oneway: Mo01408OnewayType = {
 *   itemLabel: '単一選択',
 *   items: [
 *     { title: 'タイトル1', value: '1' },
 *     { title: 'タイトル2', value: '2' },
 *   ],
 * }
 * ```
 * ``` template
 * <base-mo01408
 *   v-model="mo01408"
 *   :oneway-model-value="mo01408Oneway"
 * />
 * ```
 *
 * @example <caption>複数選択の例</caption>
 * ``` typescript
 * const mo01408 = ref<Mo01408Type>({
 *   // 複数選択の場合は配列で値を保持
 *   value: [],
 * })
 * const mo01408Oneway: Mo01408OnewayType = {
 *   // 複数選択を有効にする
 *   multiple: true,
 *   // 上記以外は単一選択と同様
 * ```
 *
 * @example <caption>検索機能のカスタマイズ例</caption>
 * ``` typescript
 * const mo01408 = ref<Mo01408Type>({
 *   value: null,
 * })
 * const mo01408Oneway: Mo01408OnewayType = {
 *   itemLabel: '単一選択',
 *   items: [
 *     { title: 'タイトル1', value: '1', kana: 'たいとるいち' },
 *     { title: 'タイトル2', value: '2', kana: 'たいとるに' },
 *   ],
 * }
 * function customFilter(_val: string, queryVal: string, item: InternalItem) {
 *   return (
 *     (item.raw as Mo01408ItemType)?.title.includes(queryVal) ||
 *     (item.raw as Mo01408ItemType)?.kana.includes(queryVal)
 *   )
 * }
 * ```
 * ``` template
 * <base-mo01408
 *   v-model="mo01408"
 *   :oneway-model-value="mo01408Oneway"
 *   :custom-filter="customFilter"
 * />
 * ```
 */
import { reactive, ref, useTemplateRef, watch, type ComponentPublicInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Mo01408Type, Mo01408OnewayType } from '@/types/business/components/Mo01408Type'
import { CustomClass } from '~/types/CustomClassType'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue?: Mo01408Type
  onewayModelValue?: Mo01408OnewayType
}

const props = defineProps<Props>()

const defaultModelValue: Mo01408Type = {
  value: null,
}

const defaultOnewayModelValue: Mo01408OnewayType = {
  name: '',
  disabled: false,
  readonly: false,
  multiple: false,
  allowsMultiLine: true,
  density: 'compact',
  clearable: false,
  variant: 'outlined',
  itemLabel: '',
  itemLabelFontWeight: 'normal',
  isRequired: false,
  showItemLabel: true,
  isVerticalLabel: true,
  hintTooltipText: '',
  appendLabel: '',
  hideDetails: 'auto',
  customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'ma-1' }),
}

/**************************************************
 * 変数
 **************************************************/
const local = reactive({
  mo01408: {
    ...defaultModelValue,
    ...props.modelValue,
  } as Mo01408Type,
})

const localOneway = reactive({
  mo01408Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
    customClass: {
      ...defaultOnewayModelValue?.customClass,
      ...props.onewayModelValue?.customClass,
    } as CustomClass,
  } as Mo01408OnewayType,
})

const searchText = ref('')

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    local.mo01408 = {
      ...defaultModelValue,
      ...newValue,
    }
  },
  { deep: true }
)
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.mo01408Oneway = {
      ...defaultOnewayModelValue,
      ...newValue,
      customClass: {
        ...defaultOnewayModelValue?.customClass,
        ...newValue?.customClass,
      } as CustomClass,
    }
  },
  { deep: true }
)

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
watch(
  () => local.mo01408.value,
  () => {
    emit('update:modelValue', local.mo01408)
  }
)

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// フォーカス状態
const isFocus = ref(false)

// スピンボタンのアイコン
const menuIcon = ref('keyboard_arrow_down')

// フォーカス時のイベント
function onFocus() {
  isFocus.value = true
  menuIcon.value = 'search'
}

// ブラー時のイベント
function onBlur() {
  isFocus.value = false
  menuIcon.value = 'keyboard_arrow_down'
}

// 未選択クリック時のイベント
function onClickNoSelect() {
  // リセット処理実施
  const autocompleteRefElement = autocompleteRef.value?.$el as HTMLElement | undefined
  const resetRefElement = autocompleteRefElement?.querySelector('.reset-btn') as
    | HTMLElement
    | undefined
  if (resetRefElement) {
    resetRefElement.click()
  }

  // リセットを繰り返すと正常動作しないため、modelValueの値も更新
  if (localOneway.mo01408Oneway.multiple) {
    local.mo01408.value = []
  }
}

const autocompleteRef = useTemplateRef<ComponentPublicInstance>('autocompleteRef')
</script>
<template>
  <c-v-sheet
    :class="localOneway.mo01408Oneway.customClass?.outerClass"
    :style="localOneway.mo01408Oneway.customClass?.outerStyle"
  >
    <!-- 縦型ラベル -->
    <c-v-row
      v-if="localOneway.mo01408Oneway.isVerticalLabel && localOneway.mo01408Oneway.showItemLabel"
      no-gutters
      :class="localOneway.mo01408Oneway.customClass?.labelClass"
      :style="localOneway.mo01408Oneway.customClass?.labelStyle"
    >
      <!-- 項目名あり -->
      <c-v-col
        v-if="localOneway.mo01408Oneway.itemLabel"
        class="align-self-center"
        cols="auto"
      >
        <base-at-label
          class="item-label"
          :value="localOneway.mo01408Oneway.itemLabel"
          :font-weight="localOneway.mo01408Oneway.itemLabelFontWeight"
        />
      </c-v-col>
      <!-- 項目名なし -->
      <c-v-col
        v-else
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 必須バッチ -->
      <c-v-col
        v-if="localOneway.mo01408Oneway.isRequired"
        class="align-self-center"
        cols="auto"
      >
        <base-mo00037 class="pl-1" />
      </c-v-col>
      <!-- ヒント -->
      <c-v-col
        v-if="localOneway.mo01408Oneway.hintTooltipText"
        class="align-self-center"
        cols="auto"
      >
        <c-v-tooltip :text="localOneway.mo01408Oneway.hintTooltipText">
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>
      <slot name="content" />
    </c-v-row>
    <c-v-row
      no-gutters
      :class="localOneway.mo01408Oneway.customClass?.itemClass"
      :style="localOneway.mo01408Oneway.customClass?.itemStyle"
    >
      <!-- 横型ラベル:項目名あり -->
      <c-v-col
        v-if="
          !localOneway.mo01408Oneway.isVerticalLabel &&
          localOneway.mo01408Oneway.showItemLabel &&
          localOneway.mo01408Oneway.itemLabel
        "
        class="pt-1"
        :class="localOneway.mo01408Oneway.customClass?.labelClass"
        :style="localOneway.mo01408Oneway.customClass?.labelStyle"
        cols="auto"
      >
        <base-at-label
          class="label-center"
          :value="localOneway.mo01408Oneway.itemLabel"
        />
      </c-v-col>
      <!-- 横型:必須バッチ -->
      <c-v-col
        v-if="
          !localOneway.mo01408Oneway.isVerticalLabel &&
          localOneway.mo01408Oneway.showItemLabel &&
          localOneway.mo01408Oneway.isRequired
        "
        class="pt-2"
        cols="auto"
      >
        <base-mo00037 class="pl-1" />
      </c-v-col>
      <!-- 横型:ヒント -->
      <c-v-col
        v-if="
          !localOneway.mo01408Oneway.isVerticalLabel &&
          localOneway.mo01408Oneway.showItemLabel &&
          localOneway.mo01408Oneway.hintTooltipText
        "
        class="pt-2"
        cols="auto"
      >
        <c-v-tooltip :text="localOneway.mo01408Oneway.hintTooltipText">
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>
      <c-v-col>
        <!-- オートコンプリート -->
        <base-at-autocomplete
          ref="autocompleteRef"
          v-model="local.mo01408.value"
          v-model:search="searchText"
          v-bind="{ ...$attrs, ...localOneway.mo01408Oneway }"
          :class="
            localOneway.mo01408Oneway.allowsMultiLine && localOneway.mo01408Oneway.multiple
              ? 'multi-line'
              : ''
          "
          :menu-icon="menuIcon"
          @focus="onFocus"
          @blur="onBlur"
        >
          <!-- 未選択ボタン -->
          <template
            v-if="(localOneway.mo01408Oneway.items?.length ?? 0) > 0"
            #prepend-item
          >
            <base-at-button
              variant="text"
              color="text"
              width="100%"
              height="48"
              class="no-select-btn pl-4 rounded-0"
              @click="onClickNoSelect"
            >
              {{ t('label.unselected') }}
            </base-at-button>
          </template>
          <!-- リセットボタン（非表示。押下時のイベントのみ利用） -->
          <template #prepend="{ reset }">
            <base-at-button
              class="reset-btn"
              @click="reset"
            >
            </base-at-button>
          </template>
          <template
            v-if="localOneway.mo01408Oneway.appendLabel"
            #append
          >
            <base-at-label :value="localOneway.mo01408Oneway.appendLabel" />
          </template>
        </base-at-autocomplete>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
</template>
<style scoped lang="scss">
// フォーカス時の回転無効化
:deep(.v-autocomplete__menu-icon) {
  transform: none;
}
// 未選択ボタン
.no-select-btn {
  justify-content: start;
  &:deep(.v-btn__content) {
    color: rgb(var(--v-theme-black-300));
  }
}
// 入力欄のスタイル
:deep(.v-field__input) {
  overflow-y: hidden;
  height: 36px;
}
// 複数行表示
.multi-line :deep(.v-field__input) {
  height: inherit;
}
// リセットボタン非表示
:deep(.v-input__prepend) {
  display: none;
}
// チェックボックスのスタイル
:deep() ~ .v-list-item {
  .v-selection-control--dirty .v-icon {
    color: rgb(var(--v-theme-key));
  }
}
</style>
