<script setup lang="ts">
import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or29166Const } from '../Or29166/Or29166.constants'
import { Or29166Logic } from '../Or29166/Or29166.logic'
import type { DataTableData, Or26416StateType } from './Or26416.type'
import { Or26416Const } from './Or26416.constants'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Or26416OnewayType, Or26416Type } from '~/types/cmn/business/components/Or26416Type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type {
  RequiredcarePrimaryDecisionInitInfoSelectInEntity,
  RequiredcarePrimaryDecisionInitInfoSelectOutEntity,
} from '~/repositories/cmn/entities/RequiredcarePrimaryDecisionInitInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo01361OnewayType, Mo01361Type } from '~/types/business/components/Mo01361Type'
import type { Or29166OnewayType } from '~/types/cmn/business/components/Or29166Type'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'

/**
 *Or26416:有機体:居宅介護支援事業者選択画面モーダル
 *GUI01271_要介護度の一次判定
 *
 * @description
 *［要介護度の一次判定］画面では、［基本調査1］画面から［基本調査5］画面までに登録された入力内容にもとづいて、要介護度の判定をします。
 * 認定調査項目に未入力がある場合は、最初に［警告］画面が表示され、判定結果は表示されません。
 *［要介護度の一次判定］画面は、［ケアマネ］→［認定調査］→［概況調査］画面などで［判定］をクリックすると表示されます。
 *
 * <AUTHOR> 李晨昊
 */

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or26416Type
  uniqueCpId: string
  onewayModelValue: Or26416OnewayType
}
const selectedItemIndex = ref<number>(-1)
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or26416OnewayType = {
  /** 認定フラグ */
  ninteiFlg: '',
  /** 計画期間ID */
  sc1Id: '',
  /** 調査票ID */
  cschId: '',
}

const localOneway = reactive({
  or26416: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  // 居宅介護支援事業者選択ダイアログ
  mo00024Oneway: {
    width: '940px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or26416',
      toolbarTitle: t('label.level-of-care-required-first-judgment'),
      toolbarName: 'Or26416ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // 注意ラベル
  announcementMessages: {
    value: t('label.or27734-attention-label'),
  } as Mo01337OnewayType,
  // プログレスバー
  mo01361OneWayDefault: {
    progressColor: '#0000FF',
    height: 20,
  } as Mo01361OnewayType,
  mo01361OneWayBuffer: {
    progressColor: '#FF0000',
    height: 20,
  } as Mo01361OnewayType,
  or29166Oneway: {
    OrDisplayMissingItems: '',
    OrDisplayComments: '',
  } as Or29166OnewayType,
})

const local = reactive({
  //要介護時間＝Bufferフラグ
  bufferFlg: false,
  //警告一覧表示フラグ
  showWarnTable: false,
  //警告弾窓表示フラグ
  showDialog: false,
  hanteiInput: {
    value: '',
  } as Mo00045Type,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or26416Const.DEFAULT.IS_OPEN,
})

// 要介護度の一次判定画面情報
const tableData = ref<DataTableData>({
  youkaiList: [],
  keikokuJouhouList: [],
})

// プログレスバー
const mo01361Type = ref<Mo01361Type>({
  progressValue: 0,
})

const conlonStr = ref(t('label.colon'))

const or29166 = ref({ uniqueCpId: Or29166Const.CP_ID(0) })
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or26416StateType>({
  cpId: Or26416Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or26416Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or29166Const.CP_ID(0)]: or29166.value,
})

/**
 * 取込情報初期情報取得
 */
async function init() {
  const inputData: RequiredcarePrimaryDecisionInitInfoSelectInEntity = {
    //認定フラグ
    ninteiFlg: localOneway.or26416.ninteiFlg,
    //計画期間ID
    sc1Id: localOneway.or26416.sc1Id,
    // 調査票ID
    cschId: localOneway.or26416.cschId,
  }
  const resData: RequiredcarePrimaryDecisionInitInfoSelectOutEntity = await ScreenRepository.select(
    'requiredcarePrimaryDecisionInitInfoSelect',
    inputData
  )
  //初期情報
  const dataInfo = resData.data
  if (dataInfo) {
    const youkaiList = resData.data.youkaigodoItiziHanteiJouhou
    if (youkaiList) {
      for (const showInfo of youkaiList) {
        tableData.value.youkaiList.push({
          //得点1
          tokuten1: formattedNumber(showInfo.tokuten1),
          //得点2
          tokuten2: formattedNumber(showInfo.tokuten2),
          //得点3
          tokuten3: formattedNumber(showInfo.tokuten3),
          //得点4
          tokuten4: formattedNumber(showInfo.tokuten4),
          //得点5
          tokuten5: formattedNumber(showInfo.tokuten5),
          //得点6
          tokuten6: formattedNumber(showInfo.tokuten6),
          //得点7
          tokuten7: formattedNumber(showInfo.tokuten7),
          //整容時間
          seiyouJikan: formattedNumber(showInfo.seiyouJikan),
          //入浴時間
          nyuuyokuJikan: showInfo.nyuuyokuJikan,
          //食事時間
          shokuziJikan: formattedNumber(showInfo.shokuziJikan),
          //排泄時間
          haisetuJikan: formattedNumber(showInfo.haisetuJikan),
          //移動時間
          idouJikan: formattedNumber(showInfo.idouJikan),
          //清潔保持時間
          seiketuHoziJikan: formattedNumber(showInfo.seiketuHoziJikan),
          //食事摂取時間
          shokuziSesshuJikan: formattedNumber(showInfo.shokuziSesshuJikan),
          //直接生活介助時間
          chokusetuJikanTotJikan: formattedNumber(showInfo.chokusetuJikanTotJikan),
          //間接生活介助時間
          kansetuKaijoJikan: formattedNumber(showInfo.kansetuKaijoJikan),
          //問題行動関連介助時間
          mondaiKoudouJikan: formattedNumber(showInfo.mondaiKoudouJikan),
          //BPSD関連行為時間
          bpsdJikan: formattedNumber(showInfo.bpsdJikan),
          //機能訓練関連行為時間
          kinouKunrenJikan: formattedNumber(showInfo.kinouKunrenJikan),
          //医療関連行為時間
          iryouJikan: formattedNumber(showInfo.iryouJikan),
          //認知症加算時間
          nintishouKasanJikan: formattedNumber(showInfo.nintishouKasanJikan),
          //合計時間
          allTotJikan: formattedNumber(showInfo.allTotJikan),
          //障害高齢者自立度
          shougaiKoureisha: showInfo.shougaiKoureisha,
          //認知症高齢者自立度
          nintishouKoureisha: showInfo.nintishouKoureisha,
          //認定調査結果
          ninteiChousaKekka: showInfo.ninteiChousaKekka,
          //主治医意見書
          shuziiIKensho: showInfo.shuziiIKensho,
          //認知症自立度Ⅱ以上の蓋然性
          gaizensei: showInfo.gaizensei,
          //状態の安定性
          joutaiAnteisei: showInfo.joutaiAnteisei,
          //給付区分
          kyuuhuKubun: showInfo.kyuuhuKubun,
          //一次判定
          itiziHantei: showInfo.itiziHantei,
          //要介護時間
          youkaigoZikan: showInfo.youkaigoZikan,
          //要介護度
          youKaigodo: showInfo.youKaigodo,
          //記入漏れチェック結果
          checkKekka: showInfo.checkKekka,
          //記入漏れ項目
          kinyuuMoreKoumoku: showInfo.kinyuuMoreKoumoku,
          //記入漏れ項目数
          koumokuSuu: showInfo.koumokuSuu,
        })
      }
      if (youkaiList[0].youkaigoZikan !== null && youkaiList[0].youkaigoZikan !== '') {
        const max = 110
        const current = Number(youkaiList[0].youkaigoZikan)
        //要介護時間
        mo01361Type.value.progressValue = Math.round((current / max) * 100)
        //要介護時間＝Bufferの場合
        if (Number(youkaiList[0].youkaigoZikan) === 110) {
          local.bufferFlg = true
        }
      }
    }
    //警告情報リスト
    const keikokuJouhouList = resData.data.keikokuJouhouList
    if (keikokuJouhouList) {
      for (const showInfo of keikokuJouhouList) {
        tableData.value.keikokuJouhouList.push({
          //番号
          bangou: showInfo.bangou,
          //警告内容
          keikokuNaiyou: showInfo.keikokuNaiyou,
        })
      }
    }
    //警告情報リストのサイズ＞0の場合、表示
    if (tableData.value.keikokuJouhouList.length > 0) {
      local.showWarnTable = true
    }
    // 要介護度の一次判定画面の初期情報.記入漏れチェック結果＝1：記入漏れ有りの場合
    if (youkaiList[0].checkKekka !== null && youkaiList[0].youkaigoZikan !== '') {
      if (Number(youkaiList[0].checkKekka) === 1) {
        localOneway.or29166Oneway.OrDisplayMissingItems = youkaiList[0].kinyuuMoreKoumoku
        localOneway.or29166Oneway.OrDisplayComments = youkaiList[0].koumokuSuu

        // Or9166のダイアログ開閉状態を更新する
        Or29166Logic.state.set({
          uniqueCpId: or29166.value.uniqueCpId,
          state: { isOpen: true },
        })
      }
    }
    local.hanteiInput.value = tableData.value.youkaiList[0].itiziHantei
  }
}

function formattedNumber(val : string){
  if(val === null || val ===undefined){
    return '0.00'
  }
  const num = parseFloat(val)

  return isNaN(num) ? '0.00' : num.toFixed(2)
}
/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}
/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr29166 = computed(() => {
  return Or29166Logic.state.get(or29166.value.uniqueCpId)?.isOpen ?? false
})

const warnHeader = [
  {
    title: t('label.number'),
    align: 'center',
    sortable: false,
    key: 'warnNumber',
  },
  {
    title: t('label.warning-content'),
    align: 'center',
    sortable: false,
    key: 'warnNumber',
  },
]

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 「確定」ボタン押下
 */
function confirm() {
  // 戻り値設定
  const rtnData: Or26416Type = {
    itiziHantei: tableData.value.youkaiList[0].itiziHantei,
  }
  // 選択情報値戻り
  emit('update:modelValue', rtnData)
  close()
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)
</script>
<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <div class="div-flex">
        <div>
          <!--中間評価項目セクション-->
          <c-v-data-table
            class="table-wrapper"
            style="width: 500px"
            hide-default-footer
            :item-per-page="-1"
            hover
            fixed-header
          >
            <tr>
              <th
                colspan="2"
                class="th-class"
              >
                <span>{{ t('label.mid-eva-item') }}</span>
              </th>
              <th>
                <span>{{ t('label.get-point') }}</span>
              </th>
            </tr>
            <tr>
              <!--第１群-->
              <td class="td-background-orign">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.first-group'),
                    customClass: new CustomClass({
                      labelClass: 'td-background-orign center-div ',
                    }),
                  }"
                />
              </td>
              <!--身体機能・起居動作-->
              <td>
                <base-mo00615
                  :oneway-model-value="{ itemLabel: t('label.body-func-live-movement') }"
                />
              </td>
              <!--得点1-->
              <td>
                <base-mo01338
                  class="ml-2"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.tokuten1,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--第２群-->
              <td class="td-background-orign">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.second-group'),
                    customClass: new CustomClass({ labelClass: 'td-background-orign center-div ' }),
                  }"
                />
              </td>
              <!--生活機能-->
              <td>
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.assessment-home-issuesAndGoals-name-6-2'),
                  }"
                />
              </td>
              <!--得点2-->
              <td>
                <base-mo01338
                  class="ml-2"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.tokuten2,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--第３群-->
              <td class="td-background-orign">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.third-group'),
                    customClass: new CustomClass({ labelClass: 'td-background-orign center-div ' }),
                  }"
                />
              </td>
              <!--認知機能-->
              <td>
                <base-mo00615 :oneway-model-value="{ itemLabel: t('label.cognitive-function') }" />
              </td>
              <!--得点3-->
              <td>
                <base-mo01338
                  class="ml-2"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.tokuten3,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--第４群-->
              <td class="td-background-orign">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.fourth-group'),
                    customClass: new CustomClass({ labelClass: 'td-background-orign center-div ' }),
                  }"
                />
              </td>
              <!--精神・行動障害-->
              <td>
                <base-mo00615
                  :oneway-model-value="{ itemLabel: t('label.mental-action-handycap') }"
                />
              </td>
              <!--得点4-->
              <td>
                <base-mo01338
                  class="ml-2"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.tokuten4,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--第５群-->
              <td class="td-background-orign">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.fifth-group'),
                    customClass: new CustomClass({ labelClass: 'td-background-orign center-div ' }),
                  }"
                />
              </td>
              <!--社会生活への適応-->
              <td>
                <base-mo00615
                  :oneway-model-value="{ itemLabel: t('label.society-life-adaptation') }"
                />
              </td>
              <!--得点5-->
              <td>
                <base-mo01338
                  class="ml-2"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.tokuten5,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
          </c-v-data-table>
          <!--日常生活自立度セクション-->
          <div class="dic-radius">
            <!--"日常生活自立度"-->
            <div style="padding-top: 10px">
              <base-mo01338
                :oneway-model-value="{
                  value: t('label.everydaylife-life-independence-level'),
                  customClass: new CustomClass({
                    itemStyle: 'font-weight:bold',
                  }),
                }"
              />
            </div>
            <!--"障害高齢者自立度"-->
            <div class="div-flex label-second">
              <base-mo00615
                :oneway-model-value="{
                  itemLabel: t('label.degree-elderly-disable'),
                  customClass: new CustomClass({
                    labelClass: 'label-width',
                  }),
                }"
              />
              <base-at-label
                class="center-div"
                :value="conlonStr"
              />
              <base-mo01338
                :oneway-model-value="{ value: tableData.youkaiList[0]?.shougaiKoureisha }"
              />
            </div>
            <!--"認知症高齢者自立度"-->
            <div class="div-flex label-second">
              <base-mo00615
                :oneway-model-value="{
                  itemLabel: t('label.dementia-elderly-disable'),
                  customClass: new CustomClass({
                    labelClass: 'label-width',
                  }),
                }"
              />
              <base-at-label
                class="center-div"
                :value="conlonStr"
              />
              <base-mo01338
                :oneway-model-value="{ value: tableData.youkaiList[0]?.nintishouKoureisha }"
              />
            </div>
          </div>

          <!--認知機能・状態の安定性の評価セクション-->
          <div class="dic-radius">
            <!--"認知機能・状態の安定性の評価"-->
            <div style="padding-top: 10px">
              <base-mo01338
                :oneway-model-value="{
                  value: t('label.cognitive-func-state-eva'),
                  customClass: new CustomClass({
                    itemStyle: 'font-weight:bold',
                  }),
                }"
              />
            </div>
            <!--"認知症高齢者の日常生活自立度"-->
            <div>
              <base-mo01338
                :oneway-model-value="{ value: t('label.degree_indep_daily_living_eld_dem') }"
              />
            </div>
            <!--"認定調査結果"-->
            <div class="div-flex label-third">
              <base-mo00615
                :oneway-model-value="{
                  itemLabel: t('label.certification-survey-result'),
                  customClass: new CustomClass({
                    labelClass: 'label-width',
                  }),
                }"
              />
              <base-at-label
                class="center-div"
                :value="conlonStr"
              />
              <base-mo01338
                :oneway-model-value="{ value: tableData.youkaiList[0]?.ninteiChousaKekka }"
              />
            </div>
            <!--"主治医意見書"-->
            <div class="div-flex label-third">
              <base-mo00615
                :oneway-model-value="{
                  itemLabel: t('label.attending-physician-opinion'),
                  customClass: new CustomClass({
                    labelClass: 'label-width',
                  }),
                }"
              />
              <base-at-label
                class="center-div"
                :value="conlonStr"
              />
              <base-mo01338
                :oneway-model-value="{ value: tableData.youkaiList[0]?.shuziiIKensho }"
              />
            </div>
            <!--認知症自立度Ⅱ以上の蓋然性-->
            <div class="div-flex label-second">
              <base-mo00615
                :oneway-model-value="{
                  itemLabel: t('label.dementia-degree-second-more'),
                  customClass: new CustomClass({
                    labelClass: 'label-width2',
                  }),
                }"
              />
              <base-at-label
                class="center-div"
                :value="conlonStr"
              />
              <base-mo01338 :oneway-model-value="{ value: tableData.youkaiList[0]?.gaizensei }" />
            </div>
            <!--"状態の安定性"-->
            <div class="div-flex label-second">
              <base-mo00615
                :oneway-model-value="{
                  itemLabel: t('label.state-stability'),
                  customClass: new CustomClass({
                    labelClass: 'label-width',
                  }),
                }"
              />
              <base-at-label
                class="center-div"
                :value="conlonStr"
              />
              <base-mo01338
                :oneway-model-value="{ value: tableData.youkaiList[0]?.joutaiAnteisei }"
              />
            </div>
            <!--"給付区分"-->
            <div class="div-flex label-second">
              <base-mo00615
                :oneway-model-value="{
                  itemLabel: t('label.benefit-category'),
                  customClass: new CustomClass({
                    labelClass: 'label-width',
                  }),
                }"
              />
              <base-at-label
                class="center-div"
                :value="conlonStr"
              />
              <base-mo01338 :oneway-model-value="{ value: tableData.youkaiList[0]?.kyuuhuKubun }" />
            </div>
          </div>
        </div>
        <div style="padding-left: 8px">
          <!--介助セクション-->
          <c-v-data-table
            class="table-wrapper"
            style="width: 400px"
            hide-default-footer
            :item-per-page="-1"
            hover
            fixed-header
          >
            <tr>
              <th
                class="th-class"
                style="background-color: #ffd39b !important"
              >
                <!--直接生活介助-->
                <span>{{ t('label.direct-life-assistance') }}</span>
              </th>
              <th>
                <!--時間-->
                <span>{{ t('label.time') }}</span>
              </th>
            </tr>
            <tr>
              <!--食事-->
              <td>
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.assessment-comprehensive-tab-name-1'),
                    customClass: new CustomClass({ labelClass: 'center-div ' }),
                  }"
                />
              </td>
              <!--食事時間-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.shokuziJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--排泄-->
              <td>
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.assessment-comprehensive-tab-name-2'),
                    customClass: new CustomClass({ labelClass: 'center-div ' }),
                  }"
                />
              </td>
              <!--排泄時間-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.haisetuJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--移動-->
              <td>
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.mobility'),
                    customClass: new CustomClass({ labelClass: 'center-div ' }),
                  }"
                />
              </td>
              <!--移動時間-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.idouJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--清潔保持-->
              <td>
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.cleanliness-keep'),
                    customClass: new CustomClass({ labelClass: 'center-div ' }),
                  }"
                />
              </td>
              <!--清潔保持時間-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.seiketuHoziJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--直接生活介助合計-->
              <td class="td-background-gray">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.total-kei'),
                    customClass: new CustomClass({ labelClass: 'td-background-gray center-div ' }),
                  }"
                />
              </td>
              <!--直接生活介助合計時間-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.chokusetuJikanTotJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--間接生活介助-->
              <td class="td-background-orign">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.indirect-life-assistance'),
                    customClass: new CustomClass({ labelClass: 'td-background-orign center-div ' }),
                  }"
                />
              </td>
              <!--間接生活介助時間-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.kansetuKaijoJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--BPSD関連行為-->
              <td class="td-background-orign">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.bpsd-related-activities'),
                    customClass: new CustomClass({ labelClass: 'td-background-orign center-div ' }),
                  }"
                />
              </td>
              <!--BPSD関連行為時間-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.bpsdJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--機能訓練関連行為-->
              <td class="td-background-orign">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.func-train-related-behaviors'),
                    customClass: new CustomClass({ labelClass: 'td-background-orign center-div ' }),
                  }"
                />
              </td>
              <!--機能訓練関連行為-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.kinouKunrenJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--医療関連行為-->
              <td class="td-background-orign">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.medical-related-behaviors'),
                    customClass: new CustomClass({ labelClass: 'td-background-orign center-div ' }),
                  }"
                />
              </td>
              <!--医療関連行為時間-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.iryouJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--認知症加算-->
              <td class="td-background-orign">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.dementia-degree-add'),
                    customClass: new CustomClass({ labelClass: 'td-background-orign center-div ' }),
                  }"
                />
              </td>
              <!--認知症加算時間-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.nintishouKasanJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
            <tr>
              <!--合計-->
              <td class="td-background-gray">
                <base-mo00615
                  :oneway-model-value="{
                    itemLabel: t('label.total-kei'),
                    customClass: new CustomClass({ labelClass: 'td-background-gray center-div ' }),
                  }"
                />
              </td>
              <!--合計時間-->
              <td>
                <base-mo01338
                  style="text-align: center"
                  :oneway-model-value="{
                    value: tableData.youkaiList[0]?.allTotJikan,
                    customClass: new CustomClass({
                    itemClass: 'text-right-div',
                  }),
                  }"
                />
              </td>
            </tr>
          </c-v-data-table>
        </div>
      </div>
      <div>
        <div class="div-flex">
          <!--一次判定ラベル-->
          <base-mo00615
            :oneway-model-value="{
              itemLabel: t('label.first-judgment'),
              itemLabelFontWeight: 'bold',
              itemLabelCustomClass: new CustomClass({ labelStyle: 'font-size:20px' }),
            }"
          />
          <base-mo00045
            v-model="local.hanteiInput"
            :oneway-model-value="{
              readonly: 'true',
              width: '600px',
              itemLabel: '123',
              showItemLabel: false,
            }"
          />
        </div>
        <div class="div-flex">
          <div class="center-div">
            <!--要介護時間ラベル-->
            <base-mo00615
              :oneway-model-value="{ itemLabel: t('label.level-of-care-required-time') }"
            />
          </div>
          <div>
            <div class="div-flex">
              <base-mo01338
                :oneway-model-value="{ value: t('label.number-zero') }"
                class="label-padding"
              />
              <base-mo01338 :oneway-model-value="{ value: t('label.number-twenty-five') }" />
              <base-mo01338
                :oneway-model-value="{ value: t('label.number-thirty-two') }"
                class="label-padding"
              />
              <base-mo01338
                :oneway-model-value="{ value: t('label.number-fifty') }"
                class="label-padding"
              />
              <base-mo01338
                :oneway-model-value="{ value: t('label.number-seventy') }"
                class="label-padding"
              />
              <base-mo01338
                :oneway-model-value="{ value: t('label.number-ninety') }"
                class="label-padding"
              />
              <base-mo01338 :oneway-model-value="{ value: t('label.number-one-hundre-ten') }" />
            </div>
            <div style="margin-left: 24px; width: 750px">
              <!-- プログレスバー -->
              <base-mo01361
                v-model="mo01361Type"
                :oneway-model-value="
                  local.bufferFlg
                    ? localOneway.mo01361OneWayBuffer
                    : localOneway.mo01361OneWayDefault
                "
              />
            </div>
            <div class="div-flex">
              <base-mo01338
                :oneway-model-value="{
                  value: Or26416Const.SCALE_LINE.SHOW_SCALE_LINE,
                }"
              />
            </div>
          </div>
        </div>
        <div v-if="local.showWarnTable">
          <!--警告セクション-->
          <c-v-data-table
            class="table-wrapper"
            style="width: 1000px"
            hide-default-footer
            :items="tableData.keikokuJouhouList"
            :headers="warnHeader"
            :item-per-page="-1"
            hover
            fixed-header
          >
            <template #header>
              <tr>
                <th
                  v-for="header in warnHeader"
                  :key="header.key"
                >
                  <div>
                    <span>{{ header.title }}</span>
                  </div>
                </th>
              </tr>
            </template>
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="selectRow(index)"
              >
                <td style="text-align: center">
                  {{ item.bangou }}
                </td>
                <td>
                  <base-mo01337 :oneway-model-value="{ value: item.keikokuNaiyou }" />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </div>
      </div>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
          ><c-v-tooltip
            activator="parent"
            location="top"
            :text="t('tooltip.screen-close')"
        /></base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="confirm"
          ><c-v-tooltip
            activator="parent"
            location="top"
            :text="t('tooltip.confirm-settings')"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- POP画面ポップアップ SH 2025/05/16 ADD END-->
  <g-custom-or-29166
    v-if="showDialogOr29166"
    v-bind="or29166"
    :oneway-model-value="localOneway.or29166Oneway"
    :parent-unique-cp-id="props.uniqueCpId"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
//tdのパターン

.th-class {
  height: 38px;
  padding: 0 16px;
}

:deep(.td-background-orign) {
  background-color: #ffd39b !important;
}

:deep(.td-background-gray) {
  background-color: #a9a9a9 !important;
}

.label-second {
  padding-left: 16px;
}

.label-third {
  padding-left: 32px;
}

:deep(.label-width) {
  width: 200px;
}

:deep(.label-width2) {
  width: 240px;
}

:deep(.title-big-bold) {
  font-size: 20px;
  font-weight: bold;
}

:deep(.v-field__input) {
  font-size: 20px !important;
  font-weight: bold;
  text-align: center !important;
  color: red;
}

.dic-radius {
  border-radius: 15px;
  border: 1px solid rgb(var(--v-theme-black-100));
  width: 500px;
  margin-top: 8px;
}
.div-flex {
  display: flex;
}
.label-padding {
  padding-right: 80px;
}
:deep(.center-div) {
  display: flex;
  justify-content: center;
  align-items: center;
}
:deep(.right-div) {
  text-align: right;
  height: 31px;
  display: flex;
  align-items: center;
}
.text-center-div {
  text-align: center !important;
}
:deep(.text-right-div){
  align-items: center !important;
  text-align: right !important;
  height: 32px !important;
}
</style>
