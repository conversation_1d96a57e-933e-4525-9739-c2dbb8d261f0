<script setup lang="ts">
/**
 * Or28325:(ＧＬ＿居宅アセスメント履歴) 履歴情報リスト
 * GUI00634_［履歴選択］画面 ｱｾｽﾒﾝﾄ(居宅)
 *
 * @description
 * (ＧＬ＿居宅アセスメント履歴) 履歴情報リスト
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */

/**************************************************
 * インポート
 **************************************************/
import { nextTick, onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import type { DataTableType } from './Or28325.type'
import { Or28325Const } from './Or28325.constants'
import type {
  CodeType,
  Or28325OnewayType,
  Or28325Type,
} from '~/types/cmn/business/components/Or28325Type'

/**************************************************
 * プロパティ
 **************************************************/
interface Props {
  onewayModelValue: Or28325OnewayType
  modelValue: Or28325Type
  uniqueCpId: string
  parentUniqueCpId: string
}
/**
 * Propsの定義
 */
const props = defineProps<Props>()

/**************************************************
 * 定数・Reactive・Ref
 **************************************************/
/**
 * i18n
 */
const { t } = useI18n()
/**
 * defaultModelValue
 */
const defaultModelValue: Or28325Type = {
  shokuinInfoList: [],
  rirekiInfoList: [],
  ks21Id: '',
  revisionInfoList: [],
}
/**
 * local
 */
const local = reactive({
  or28325: {
    ...defaultModelValue,
    ...props.modelValue,
  },
  historyListTable: [] as DataTableType[],
})
/**
 * テーブルの選択行インデックス
 */
const tableForm = ref<VForm>()
/**
 * テーブルの選択行インデックス
 */
const selectedItemIndex = ref<number>(-1)
/**
 * テーブルのヘッダ情報
 */
const headers = [
  {
    title: t('label.create-date'),
    key: 'createYmd',
    sortable: false,
    required: false,
    width: '100px',
  },
  { title: t('label.author'), key: 'shokuKnj', sortable: false, required: false, width: '150px' },
  { title: t('label.revision'), key: 'kaitei', sortable: false, required: false, width: '100px' },
]

/**************************************************
 * イベント
 **************************************************/
/**
 * emit
 */
const emit = defineEmits(['update:modelValue', 'change'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()
})

/**************************************************
 * メソッド(Methods)
 **************************************************/
/**
 * 初期化処理
 * プロパティをリアクティブ状態にマージし、表示テーブルを構築し、該当行を選択する。
 */
async function init() {
  // 1) データをリアクティブ状態にマージ
  Object.assign(local.or28325, props.modelValue)

  // 2) 表示テーブルを構築
  local.historyListTable = buildDisplayHistory(local.or28325, local.or28325.revisionInfoList ?? [])

  // 3) 該当行を選択（DOM描画後）
  await nextTick()
  selectedItemIndex.value = selectHistoryRow(local.historyListTable, local.or28325.ks21Id)
}

/**
 * 履歴情報リストの表示データを構築
 * 履歴リストテーブルの表示データを構築する。
 *
 * @param record - メインデータレコード
 *
 * @param revisionList - 改訂コードのリスト
 *
 * @returns テーブル用のフォーマット済みデータ
 */
function buildDisplayHistory(record: Or28325Type, revisionList: CodeType[]): DataTableType[] {
  return record.rirekiInfoList.map((item) => {
    const emp = record.shokuinInfoList.find(
      (e) => Number(e.chkShokuId) === Number(item.shokuId ?? 0)
    )
    const rev = revisionList.find((r) => r.value === item.ninteiFormF)
    return {
      ks21Id: item.sc1Id,
      createYmd: item.asJisshiDateYmd,
      shokuKnj: emp ? `${emp.shokuin1Knj} ${emp.shokuin2Knj}` : '',
      kaitei: rev?.label ?? '',
    }
  })
}

/**
 * 履歴情報リストに該当行を選択状態にする
 * 親画面の履歴IDに一致する行を履歴リストテーブルで選択する。
 *
 * @param record - データテーブルのレコード
 *
 * @param parentHistoryId - 親画面からの履歴ID
 *
 * @returns 選択された行のインデックス（該当なしの場合は-1）
 */
function selectHistoryRow(record: DataTableType[], parentHistoryId: string): number {
  if (
    record.length === Or28325Const.DEFAULT.ZERO ||
    Number(parentHistoryId) <= Or28325Const.DEFAULT.ZERO
  ) {
    return Or28325Const.DEFAULT.DISPLAY_ORDER_ONE
  }
  const idx = record.findIndex((row: DataTableType) => row.ks21Id === parentHistoryId)
  if (idx === Or28325Const.DEFAULT.DISPLAY_ORDER_ONE) return Or28325Const.DEFAULT.DISPLAY_ORDER_ONE
  return idx
}

/**
 * 行選択
 * テーブル内の行選択を処理する。
 *
 * @param index - 選択された行のインデックス
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
  local.or28325.ks21Id = local.historyListTable[index].ks21Id
  emit('update:modelValue', local.or28325)
}

/**
 * 行ダブルクリック
 * テーブル内の行をダブルクリックした際の処理を行う。
 *
 * @param index - ダブルクリックされた行のインデックス
 */
function selectRowDbClick(index: number) {
  selectedItemIndex.value = index
  local.or28325.ks21Id = local.historyListTable[index].ks21Id
  emit('change', local.or28325)
}

/**************************************************
 * 外部公開メソッド(Exposed Methods)
 **************************************************/
defineExpose({
  init,
})
</script>

<template>
  <div>
    <c-v-form ref="tableForm">
      <c-v-data-table
        fixed-header
        :headers="headers"
        :items="local.historyListTable"
        class="table-wrapper"
        height="326px"
        hover
        hide-default-footer
        :items-per-page="-1"
      >
        <template #headers="{ columns }">
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              :style="{
                minWidth: column.minWidth,
                width: column.width,
              }"
            >
              <span>{{ column.title }}</span>
            </th>
          </tr>
        </template>
        <template #item="{ item, index }">
          <tr
            :class="{ 'highlight-row': selectedItemIndex === index }"
            @click="selectRow(index)"
            @dblclick="selectRowDbClick(index)"
          >
            <td>
              {{ item.createYmd }}
            </td>
            <td>
              {{ item.shokuKnj }}
            </td>
            <td>
              {{ item.kaitei }}
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-form>
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
:deep(.v-col) {
  padding: 0px !important;
}

:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: -0.5px;
  z-index: 2;
}
//区分番号幅
.number-width {
  width: 50px !important;
}
//内容幅
.content-width {
  width: 120px;
}
//区分番号
.body-text-category {
  margin-top: 50px;
  margin-left: 20px;
}
.font-size-text {
  font-size: 12px;
}
.table-wrapper :deep(td) {
  height: 32px !important;
}
</style>
