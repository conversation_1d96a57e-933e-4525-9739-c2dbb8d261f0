<script setup lang="ts">
/**
 * Or26323:履歴選択モーダル
 * GUI01282_［履歴選択］画面 認定調査
 *
 * <AUTHOR> 宋鹏飞
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { CodeType } from '../Or28326/Or28326.type'
import { Or26323Const } from './Or26323.constants'
import type { Or26323StateType, rireki1, rireki } from './Or26323.type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind } from '#imports'
import type {
  Or26323Type1,
  Or26323Type2,
  Or26323OnewayType,
} from '~/types/cmn/business/components/Or26323Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  certificationSurveyHistoryInfoSelectInEntity,
  certificationSurveyHistoryInfoSelectOutEntity,
} from '~/repositories/cmn/entities/certificationSurveyHistoryInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or26323OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
const localOneway = reactive({
  or26323: {
    ...props.onewayModelValue,
  } as Or26323OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  // 「履歴選択」ダイアログ
  mo00024Oneway: {
    width: '600px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Gui01282',
      toolbarTitle: t('label.history-select'),
      toolbarName: 'Gui01282ToolBar',
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
})

const defaultModelValue = {
  // 履歴情報
  rirekiInfo: {
    rirekiList: [] as rireki[],
    rireki1List: [] as rireki1[],
  },
}

const local = reactive({
  or26323: {
    ...defaultModelValue.rirekiInfo,
  },
  reList: [] as CodeType[],
  selectData1: {} as Or26323Type1,
  selectData2: {} as Or26323Type2,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue1', 'update:modelValue2'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or26323Const.DEFAULT.IS_OPEN,
  decisionMakingClickValue: Or26323Const.DEFAULT.LEVEL,
})

// 選択した行のindex
const selectedIndex = ref<number>(0)

// テーブルヘッダ
// 履歴情報データテーブルのヘッダー
const moDataTable = reactive({
  headers: [
    // 実施日
    {
      title: t('label.implementation-date'), // ヘッダーに表示される名称
      width: '100px',
      sortable: false,
      key: 'jisshiDateYmd',
    },
    // 記入者
    {
      title: t('label.filler'), // ヘッダーに表示される名称
      width: '400px',
      sortable: false,
      key: 'chkShokuNm',
    },
    // 改訂
    {
      title: t('label.revision'), // ヘッダーに表示される名称
      key: 'dmyCho',
      sortable: false,
      width: '100px',
    },
  ],
  headers1: [
    // 記入日
    {
      title: t('label.entry-date'), // ヘッダーに表示される名称
      key: 'createYmd',
      sortable: false,
      width: '100px',
    },
    // 医師名
    {
      title: t('label.doctorName'), // ヘッダーに表示される名称
      key: 'ishiNameKnj',
      sortable: false,
      width: '400px',
    },
    // 改訂
    {
      title: t('label.revision'), // ヘッダーに表示される名称
      key: 'ikenshoFlg',
      width: '100px',
      sortable: false,
    },
  ],
})
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or26323StateType>({
  cpId: Or26323Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or26323Const.DEFAULT.IS_OPEN
    },
    decisionMakingClickValue: (value) => {
      mo00024.value.decisionMakingClickValue = value ?? Or26323Const.DEFAULT.LEVEL
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await initCodes()
  await getInitDataInfo()
})

// 汎用コードマスタデータを取得し初期化
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [{ mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NINTEIFLG }]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // 要介護度ラジオリスト
  local.reList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_NINTEIFLG)
}

/** 初期情報取得 */
async function getInitDataInfo() {
  // 履歴選択のための初期情報の入力データ (IN)
  // このデータを基に履歴情報を取得する
  const inputData: certificationSurveyHistoryInfoSelectInEntity = {
    svJigyoId: localOneway.or26323.svJigyoId,
    userId: localOneway.or26323.userId,
    sc1Id: localOneway.or26323.sc1Id,
    mode: localOneway.or26323.mode,
  }
  // バックエンドAPIから初期情報取得
  const ret: certificationSurveyHistoryInfoSelectOutEntity = await ScreenRepository.select(
    'certificationSurveyHistoryInfoSelect',
    inputData
  )

  const resData = ret.data

  if (resData) {
    let idList = []
    if (props.onewayModelValue.mode === Or26323Const.MODE_TYPE_ZERO) {
      if (resData.rirekiInfo[0].rireki1List) {
        local.or26323.rireki1List = resData.rirekiInfo[0].rireki1List.map((item) => {
          item.kaiteiFlg = item.dmyCho
          if (item.dmyCho === local.reList.find((val) => val.value === item.dmyCho)?.value) {
            item.dmyCho = local.reList.find((val) => val.value === item.dmyCho)?.label ?? ''
          }
          return item
        })
      }
      idList = resData.rirekiInfo[0].rireki1List.map((item) => {
        return item.cschId
      })
    } else {
      if (resData.rirekiInfo[0].rirekiList) {
        local.or26323.rirekiList = resData.rirekiInfo[0].rirekiList.map((item) => {
          if (
            item.ikenshoFlg === local.reList.find((val) => val.value === item.ikenshoFlg)?.value
          ) {
            item.ikenshoFlg = local.reList.find((val) => val.value === item.ikenshoFlg)?.label ?? ''
          }
          return item
        })
      }
      idList = resData.rirekiInfo[0].rirekiList.map((item) => {
        return item.rirekiId
      })
    }

    checkNumber(idList)
    selectClick(selectedIndex.value)
  }
}

// change check
const checkNumber = (val: string[]) => {
  if (val.findIndex((item) => item === props.onewayModelValue.plan1Id) !== -1) {
    selectedIndex.value = val.findIndex((item) => item === props.onewayModelValue.plan1Id)
  }
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 「確定」ボタン押下
 */
function confirm() {
  // 画面.履歴情報リストを親画面に戻る。
  if (localOneway.or26323.mode === Or26323Const.MODE_TYPE_ZERO) {
    emit('update:modelValue1', local.selectData1)
  } else {
    emit('update:modelValue2', local.selectData2)
  }
  close()
}

function selectClick(index: number) {
  selectedIndex.value = index

  if (localOneway.or26323.mode === Or26323Const.MODE_TYPE_ZERO) {
    local.selectData1 = {
      cschId: local.or26323.rireki1List[index].cschId,
      kaiteiFlg: local.or26323.rireki1List[index].kaiteiFlg,
    }
  }
  if (localOneway.or26323.mode === Or26323Const.MODE_TYPE_ONE) {
    local.selectData2 = {
      rirekiId: local.or26323.rirekiList[index].rirekiId,
      createYmd: local.or26323.rirekiList[index].createYmd,
    }
  }
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row v-if="localOneway.or26323.mode === Or26323Const.MODE_TYPE_ZERO">
        <c-v-col>
          <!-- 履歴選択一覧 -->
          <c-v-data-table
            style="height: 326px"
            :headers="moDataTable.headers"
            :items="local?.or26323?.rireki1List"
            hide-default-footer
            hover
            class="table-header table-wrapper"
            fixed-header
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedIndex === index }"
                @click="selectClick(index)"
                @dblclick="confirm"
              >
                <td>
                  <span>{{ item.jisshiDateYmd }}</span>
                </td>
                <td>
                  <span>{{ item.chkShokuNm }}</span>
                </td>
                <td>
                  <div style="text-align: center">
                    <span>{{ item.dmyCho }}</span>
                  </div>
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
      <c-v-row v-if="localOneway.or26323.mode === Or26323Const.MODE_TYPE_ONE">
        <c-v-col>
          <!-- 履歴選択一覧 -->
          <c-v-data-table
            style="height: 326px"
            :headers="moDataTable.headers1"
            :items="local?.or26323?.rirekiList"
            hide-default-footer
            hover
            class="table-header table-wrapper"
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedIndex === index }"
                @click="selectClick(index)"
                @dblclick="confirm"
              >
                <td>
                  <span>{{ item.createYmd }}</span>
                </td>
                <td>
                  <span>{{ item.ishiNameKnj }}</span>
                </td>
                <td>
                  <div style="text-align: center">
                    <span>{{ item.ikenshoFlg }}</span>
                  </div>
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="confirm"
        >
          <!--ツールチップ表示："設定を確定します。"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';

/** 行選択の様式 */
.select-row {
  background: rgb(var(--v-theme-blue-100));
}
</style>
