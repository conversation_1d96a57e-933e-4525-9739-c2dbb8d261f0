<script setup lang="ts">
/**
 * Or30908:提供事業所（提供事業所）モーダル
 * GUI01188_提供事業所
 *
 * @description
 * 提供事業所
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { v4 as uuidv4 } from 'uuid'
import { isEmpty } from 'lodash'
import { OrX0005Const } from '../OrX0005/OrX0005.constants'
import { Or35052Const } from '../Or35052/Or35052.constants'
import { Or35052Logic } from '../Or35052/Or35052.logic'
import type { jigyoInfo } from '../Or26143/Or26143.type'
import { Gui00048Logic } from '../Gui00048/Gui00048.logic'
import { Gui00048Const } from '../Gui00048/Gui00048.constants'
import type {
  JigyouInfoListTypes,
  TantoInfo,
  TantoshaInfo,
  RiyouInfo,
  JigyouInfo,
} from './Or30908.type'
import { Or30908Const } from './Or30908.constants'
import { Or29841Logic } from '~/components/custom-components/organisms/Or29841/Or29841.logic'
import { Or29841Const } from '~/components/custom-components/organisms/Or29841/Or29841.constants'
import { useSetupChildProps } from '~/composables/useComponentVue'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type {
  OfferOfficeInfoSelectOutEntity,
  OfferOfficeInfoSelectInEntity,
} from '~/repositories/cmn/entities/OfferOfficeInfoSelectEntity'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import { CustomClass } from '~/types/CustomClassType'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { OfferOfficeUpdateInEntity } from '~/repositories/cmn/entities/OfferOfficeUpdateEntity'
import type { UserInfoSelectInEntity } from '~/repositories/cmn/entities/UserInfoSelectEntity'
import type { Or30908OnewayType, Or30908Type } from '~/types/cmn/business/components/Or30908Type'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
// import { hasPrintAuth, hasRegistAuth } from '~/utils/useCmnAuthz'
import type { Or35052OnewayType } from '~/types/cmn/business/components/Or35052Type'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { useJigyoList, useScreenStore, useSystemCommonsStore } from '#imports'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import type { OrX0165OnewayType } from '~/types/cmn/business/components/OrX0165Type'
import { hasRegistAuth, hasViewAuth } from '~/utils/useCmnAuthz'
import type { Or29841OnewayType } from '~/types/cmn/business/components/Or29841Type'

// import { Or53427Logic } from '~/components/custom-components/organisms/Or53427/Or53427.logic'
// import type { Or53427OnewayType } from '~/types/cmn/business/components/Or53427Type'
// import { Or53427Const } from '~/components/custom-components/organisms/Or53427/Or53427.constants'

const { t } = useI18n()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
/**
 * 事業所リスト監視
 */
const { jigyoListWatch } = useJigyoList()

/** 画面状態の状態管理 */
const screenStore = useScreenStore()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or30908Type
  onewayModelValue: Or30908OnewayType
  uniqueCpId: string
  parentCpId: string
}
/** props */
const props = defineProps<Props>()

const or11871 = ref({ uniqueCpId: '' })
const orX0005 = ref({ uniqueCpId: OrX0005Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const or35052 = ref({ uniqueCpId: '' })
const or29841 = ref({ uniqueCpId: Or29841Const.CP_ID(0) })
// const or53427 = ref({ uniqueCpId: Or29841Const.CP_ID(1) })
const or41179 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })
const gui00048 = ref({ uniqueCpId: '' })

/** ダイアログ表示フラグ */
const showDialog = computed(() => {
  // GuiD0048のダイアログ開閉状態
  return Gui00048Logic.state.get(gui00048.value.uniqueCpId)?.isOpen ?? false
})

/** カレンダーイベント */
const calendarEvent = computed(() => {
  return Gui00048Logic.event.get(gui00048.value.uniqueCpId)
})

// ローカル双方向bind
const local = reactive({
  or30908: {
    ...props.modelValue,
    jigyouInfoList: [] as JigyouInfoListTypes[],
    svJigyoId: '',
    jigyoNameKnj: '',
    tantoId: '',
    shokuinName: '',
    yymmYm: { value: props.onewayModelValue.yymmYm },
    riyouInfoList: [] as RiyouInfo[],
    tantoshaList: [] as TantoshaInfo[],
  },
  tensouFlag: '0',
  dataCopy: {},
})

// 入力データ保存用
const inputValues = ref({
  svJigyoId: '',
  jigyoNameKnj: '',
  tantoId: '',
  shokuinName: '',
  yymmYm: { value: '' },
  jigyouInfoList: [] as JigyouInfoListTypes[],
})

const selectedItem = ref('')
const svTensuTol = ref(0)
const kTensuTol = ref(0)
const kTensuOverTol = ref(0)

const localOneway = reactive({
  or30908: {
    ...props.onewayModelValue,
  },
  mo00615OnewayOfficeName: {
    itemLabel: '',
    showItemLabel: true,
    showRequiredLabel: false,
  } as Mo00615OnewayType,
  /** ラベルの単方向モデル */
  mo00615Processym: {
    itemLabel: t('label.offer-ym'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  mo00020Oneway: {
    showItemLabel: false,
    customClass: new CustomClass({ outerClass: 'disp_with' }),
  } as Mo00020OnewayType,
  backmo00009Oneway: {
    variant: 'text',
    color: 'black-800',
    btnIcon: 'chevron_left',
  } as Mo00009OnewayType,
  forwardmo00009Oneway: {
    variant: 'text',
    color: 'black-800',
    btnIcon: 'chevron_right',
  } as Mo00009OnewayType,
  mumber: {
    itemLabel: t('label.items'),
  } as Mo01338OnewayType,
  matter: {
    itemLabel: `0${Or30908Const.STR_BLANK_ZENKAKU}${t('label.item')}`,
  } as Mo01338OnewayType,
  usematter: {
    itemLabel: `0${Or30908Const.STR_BLANK_ZENKAKU}${t('label.item')}`,
  } as Mo01338OnewayType,
  unitCount: {
    itemLabel: '単位数合計',
  } as Mo01338OnewayType,
  svTensuTol: {
    itemLabel: '0',
  } as Mo01338OnewayType,
  kTensuTol: {
    itemLabel: '0',
  } as Mo01338OnewayType,
  kTensuOverTol: {
    itemLabel: '0',
  } as Mo01338OnewayType,
  // 提供年月
  orX0165Plan: {
    label: t('label.offer-ym'),
    pageLabel: props.onewayModelValue.yymmYm,
    pageLabelFontWeight: 'normal',
    isRequired: false,
  } as OrX0165OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: props.onewayModelValue.userId,
  } as OrX0145OnewayType,
})

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

const or35052Oneway = reactive<Or35052OnewayType>({
  // 利用者ID
  userId: localOneway.or30908.userId,
  // セクション名
  sectionName: Or30908Const.SECTION_NAME,
  // 選択帳票番号
  choIndex: Or30908Const.SELECTED_NUMBER,
  // 50音母音
  focusSettingInitial: localOneway.or30908.gojuuonKana,
  // 事業所ID
  svJigyoId: localOneway.or30908.svJigyoId,
  // 担当者ID
  tantoId: local.or30908.tantoId,
  // 支援事業所ID
  shienId: local.or30908.svJigyoId,
  // 職員ID
  shokuId: localOneway.or30908.shokuinId,
  // 法人ID
  houjinId: localOneway.or30908.corporationId,
  // 施設ID
  shisetuId: localOneway.or30908.facilityId,
  // システムコード
  syscd: localOneway.or30908.syscd,
  // 提供年月
  teiYm: localOneway.or30908.yymmYm,
  // 履歴保存フラグ
  h0ldFlg: false,
  // 電子保存の3原則フラグ
  gbeBunshoFlg: '',
  // システム略称
  sysRyaku: Or30908Const.SYSTEM_RYAKU_3GK,
  userMax: '',
  // システム年月日
  appYmd: localOneway.or30908.systemYmd,
})

const or29841Data: Or29841OnewayType = {
  // 支援事業所ID
  supportOfficeId: '1',
  // 処理年月
  processDate: '2025/05',
  // 50音
  gojuuon: [
    {
      // 行番号
      gojuuOnRowNo: '1',
      // 母音
      gojuuOnKana: 'あ',
    },
  ],
  // ロケーション
  location: '',
  // 職員ID
  staffId: '',
}

// const or53427Data: Or53427OnewayType = {
//   /** 処理年月 */
//   shoriYm: '2025/05',
//   /** 担当ケアマネID */
//   tantoId: '1',
//   /** 職員ID */
//   shokuinId: '1',

//   /** 事業所ID */
//   svJigyoId: '1',
//   /**
//    *事業所ID
//    */
//   officeId: '1',
//   /**
//    * システムCD
//    */
//   gSysCd: '1',

//   /**
//    * 適用事業所IDの配列
//    */
//   // gSysCd: '1',
// } as Or53427OnewayType

// 事業所一覧
const mo01334TypeOffice = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

const mo01334OnewayOffice = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.plan-business-name'),
      key: 'jigyoNumber',
      sortable: true,
      minWidth: '86',
    },
    {
      title: t('label.plan-business-mnumber'),
      key: 'jigyoKnj',
      sortable: true,
      minWidth: '286',
    },
    {
      title: t('label.plan-differentiation'),
      key: 'ruakuKnj',
      sortable: true,
      minWidth: '91',
    },
    {
      title: t('label.plan-plans'),
      key: 'keikaku',
      sortable: true,
      minWidth: '60',
    },
    {
      title: t('label.plan-performance'),
      key: 'jisseki',
      sortable: true,
      minWidth: '60',
    },
  ],
  items: [],
  height: 247,
})

// 利用者一覧
const mo01334TypeUser = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

const mo01334OnewayUser = ref<Mo01334OnewayType>({
  headers: [
    {
      key: 'hHokenNo',
      title: t('label.insured-person-number-1'),
      sortable: true,
      minWidth: '86',
    },
    { key: 'selfId', title: t('label.plan-user-id'), sortable: true, minWidth: '86' },
    {
      key: 'nameKnj',
      title: t('label.plan-user-name'),
      sortable: true,
      minWidth: '201',
    },
    { key: 'yokaiKbnText', title: t('label.yokai-knj'), sortable: true, minWidth: '72' },
    { key: 'gendoGaku', title: t('label.limit'), sortable: true, minWidth: '91' },
    { key: 'svTensu', title: t('label.total-unit-number'), sortable: true, minWidth: '91' },
    { key: 'kTensu', title: t('label.within-limits'), sortable: true, minWidth: '91' },
    { key: 'kTensuOver', title: t('label.excess'), sortable: true, minWidth: '91' },
  ],
  items: [],
  height: 211,
})

/**
 *事業所情報
 */
const jigyo = ref<jigyoInfo>()

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()

  if (systemCommonsStore.getUserSelectSelfId) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }

  const screen = screenStore.screen()
  const pageComponent = screen.supplement.pageComponent
  gui00048.value.uniqueCpId = uuidv4()
  // 画面コンポーネント配下に年月選択を追加
  screenStore.setCpStructure({
    cpId: pageComponent.cpId,
    uniqueCpId: pageComponent.uniqueCpId,
    innerComponents: {
      [gui00048.value.uniqueCpId]: {
        cpId: Gui00048Const.CP_ID(1),
      },
    },
  })
  // 年月選択の初期化
  Gui00048Logic.initialize(gui00048.value.uniqueCpId)
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [OrX0005Const.CP_ID(0)]: orX0005.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21814Const.CP_ID(2)]: or21814_1.value,
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or35052Const.CP_ID(1)]: or35052.value,
  [Or29841Const.CP_ID(1)]: or29841.value,
  // [Or53427Const.CP_ID(1)]: or53427.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
  [OrX0145Const.CP_ID(1)]: orx0145.value,
})

// 画面メニューの初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.yukokikangaisvc-list-header-jimusyo'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    // TODO
    // showSaveBtn: true,
    showSaveBtn: false,
    //disabledSaveBtn: !(await hasRegistAuth()),
    showCreateBtn: false,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    disabledPrintBtn: false,
    //disabledPrintBtn: !(await hasPrintAuth()),
    showMasterBtn: true,
    showOptionMenuBtn: true,
    //showMasterBtn: false,
    //showOptionMenuBtn: false,
    showOptionMenuDelete: false,
  },
})

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)
/**
 * 事業所変更時のコールバック
 *
 * @param newJigyoId - 新しい事業所ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId !== '') {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    jigyo.value = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)
  }
}

/**
 * 事業所データ取得フラグの監視
 */
watch(
  () => jigyo.value,
  async (newValue, oldValue) => {
    if (newValue && !isEmpty(oldValue)) {
      checkingItem.value = Or30908Const.CHECKING_ITEM_OFFICE
      const confirmResult = await ac006_3()
      checkingItem.value = ''

      if (confirmResult === Or30908Const.DIALOG_RESULT_CANCEL) {
        return
      }

      local.or30908.svJigyoId = newValue.svJigyoId
      local.or30908.jigyoNameKnj = newValue.jigyoRyakuKnj

      // データ再取得
      await init()
    }
  }
)

/**
 * Or11871イベント発火
 *
 * @param event - イベント
 */
function setOr11871Event(event: Record<string, boolean>) {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

// 画面初期化の場合、true
const isInit = ref(true)
// 前月、翌月処理の場合、true
const isAddMonth = ref(false)

// 確認ダイアログ表示
const openInfoDialogOk = (msg: string) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
const openInfoDialog = (msg: string) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        let result = Or30908Const.DIALOG_RESULT_CANCEL
        if (event?.firstBtnClickFlg) {
          result = Or30908Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or30908Const.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or30908Const.DIALOG_RESULT_CANCEL
        }
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr21814_1 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr21813_1 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr35052 = computed(() => {
  // Or35052のダイアログ開閉状態
  return Or35052Logic.state.get(or35052.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr29841 = computed(() => {
  // Or27647のダイアログ開閉状態
  return Or29841Logic.state.get(or29841.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
// const showDialogOr53427 = computed(() => {
//   // Or27647のダイアログ開閉状態
//   return Or53427Logic.state.get(or53427.value.uniqueCpId)?.isOpen ?? false
// })

/**
 *  AC001_ 初期化
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: OfferOfficeInfoSelectInEntity = {
    /** 事業所ID */
    svJigyoId: local.or30908.svJigyoId ?? '',
    /** 担当者ID */
    tantoId: local.or30908.tantoId ?? '',
    /** 処理年月 */
    yymmYm: local.or30908.yymmYm.value,
    /** 種別ID */
    syubetsuId: localOneway.or30908.syubetsuId,
    /** システムコード */
    syscd: localOneway.or30908.syscd,
    /** 職員ID */
    shokuinId: localOneway.or30908.shokuinId,
  }

  if (isInit.value) {
    // 事業所ID
    inputData.svJigyoId = localOneway.or30908.svJigyoId
    // 担当者ID
    inputData.tantoId = localOneway.or30908.shokuinId
    // 処理年月
    inputData.yymmYm = localOneway.or30908.yymmYm
  }

  const resData: OfferOfficeInfoSelectOutEntity = await ScreenRepository.select(
    'offerOfficeInfoSelect',
    inputData
  )

  // 利用者情報リスト
  local.or30908.riyouInfoList = resData.data.riyouInfoList ?? []

  // 担当者リスト
  local.or30908.tantoshaList = resData.data.tantoshaList ?? []

  if (isInit.value) {
    isInit.value = false
    local.or30908.svJigyoId = localOneway.or30908.svJigyoId
    // 担当者id
    local.or30908.tantoId = localOneway.or30908.userId

    // 転送表示フラグ
    local.tensouFlag = resData.data.tensouFlag
  }

  // 提供事業所表一覧を設定する
  setJigyoshoTable(resData.data.jigyouInfoList ?? [])

  // 利用者一覧を設定する
  setRiyouInfoTable()

  saveInputValues()
}

// 入力項目の内容を保存する
function saveInputValues() {
  inputValues.value = { ...local.or30908 }
}

// 入力変更チェックを行っている項目、チェックから除外する
const checkingItem = ref('')

// 入力項目変更有無をチェックする
const isInputEdit = computed(() => {
  // 事業所ID
  if (
    checkingItem.value !== Or30908Const.CHECKING_ITEM_OFFICE &&
    inputValues.value.svJigyoId !== local.or30908.svJigyoId
  ) {
    return true
  }
  // 提供年月
  if (
    checkingItem.value !== Or30908Const.CHECKING_ITEM_YYMM &&
    inputValues.value.yymmYm !== local.or30908.yymmYm
  ) {
    return true
  }
  // 担当者ID
  if (
    checkingItem.value !== Or30908Const.CHECKING_ITEM_SHOKUIN &&
    inputValues.value.tantoId !== local.or30908.tantoId
  ) {
    return true
  }
  // 提供事業所情報リストの計画、実績のチェックボックス
  for (const item of inputValues.value.jigyouInfoList) {
    if (
      item.keikaku !== String(Number(item.keikakuObj.modelValue)) ||
      item.jisseki !== String(Number(item.jissekiObj.modelValue))
    ) {
      return true
    }
  }

  return false
})

const officeheightStr = ref('0px')

/**
 * 提供事業所表一覧にデータを設定する
 *
 * @param jigyouInfoList - 提供事業所情報リスト
 */
function setJigyoshoTable(jigyouInfoList: JigyouInfo[]) {
  mo01334OnewayOffice.value.items.length = 0
  // 選択状態をクリア
  selectedItem.value = ''
  mo01334TypeOffice.value.value = ''
  // 件数
  localOneway.matter.itemLabel = `${jigyouInfoList.length}${Or30908Const.STR_BLANK_ZENKAKU}${t('label.item')}`

  if (jigyouInfoList.length === 0) {
    mo01334OnewayOffice.value.height = 30 + 36
    officeheightStr.value = 36 * 5 + 'px'
    return
  }

  if (jigyouInfoList.length > 6) {
    mo01334OnewayOffice.value.height = 30 + 36 * 6
    officeheightStr.value = '0px'
  } else {
    mo01334OnewayOffice.value.height = 30 + 36 * jigyouInfoList.length

    officeheightStr.value = 36 * (6 - jigyouInfoList.length) + 'px'
  }

  // チェックボックスの状態設定
  local.or30908.jigyouInfoList = jigyouInfoList.map((item, index) => ({
    id: String(index),
    svJigyoId: item.svJigyoId,
    jigyoKnj: item.jigyoKnj,
    ruakuKnj: item.ruakuKnj,
    jigyoNumber: item.jigyoNumber,
    keikaku: item.keikaku,
    jisseki: item.jisseki,
    keikakuObj: item.keikaku === '1' ? { modelValue: true } : { modelValue: false },
    jissekiObj: item.jisseki === '1' ? { modelValue: true } : { modelValue: false },
    modifiedCnt: item.modifiedCnt,
  }))

  mo01334OnewayOffice.value.items = local.or30908.jigyouInfoList
}

// AC003  「保存ボタン」押下
const save = async () => {
  if (!isInputEdit.value || local.or30908.jigyouInfoList.length === 0) {
    // 「保存ボタン」押下
    //  ■以下のメッセージを表示
    // i.cmn.21800
    // ・メッセージ内容
    // 変更されている項目がないため、保存を行うことは出来ません。「改行」
    // 項目を入力変更してから、再度保存を行ってください。
    // ・後続処理
    // OK：処理終了
    openInfoDialogOk(t('message.i-cmn-21800'))
    return
  } else {
    // 提供事業所情報リスト
    const dataList: JigyouInfoListTypes[] = [...local.or30908.jigyouInfoList]
    dataList.forEach((item) => {
      item.keikaku = String(Number(item.keikakuObj.modelValue))
      item.jisseki = String(Number(item.jissekiObj.modelValue))
    })

    const inputData: OfferOfficeUpdateInEntity = {
      // 事業所ID
      svJigyoId: local.or30908.svJigyoId ?? '',
      // // 提供年月
      yymmYm: local.or30908.yymmYm.value,
      // 提供事業所情報リスト
      jigyouInfoList: dataList,
    }
    await ScreenRepository.update('offerOfficeUpdate', inputData)

    saveInputValues()
  }
}

// 画面入力データの変更がある場合
const ac006_3 = async () => {
  if (isInputEdit.value) {
    //  ■以下のメッセージを表示
    // i.cmn.10430
    // ・後続処理
    // はい：AC003を実行し、処理続き。更新異常の場合、処理終了
    // いいえ：AC003を実行しない。ダイアログ画面を閉じる、処理続き
    // キャンセル：処理終了
    const dialogResult = await openInfoDialog(t('message.i-cmn-10430'))

    switch (dialogResult) {
      case Or30908Const.DIALOG_RESULT_YES:
        // はい：処理継続
        await save()
        return dialogResult
      case Or30908Const.DIALOG_RESULT_NO:
        // いいえ：処理終了
        return dialogResult
      case Or30908Const.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return dialogResult
      default:
        return ''
    }
  }
  return ''
}

/**
 * ケアマネ選択戻り情報
 */
watch(
  () => orX0145Type.value.value,
  async (newValue, oldValue) => {
    if (newValue && !isEmpty(oldValue)) {
      if (!isInit.value) {
        checkingItem.value = Or30908Const.CHECKING_ITEM_SHOKUIN
        const confirmResult = await ac006_3()
        checkingItem.value = ''

        if (confirmResult === Or30908Const.DIALOG_RESULT_CANCEL) {
          return
        }
      }

      // 返却担当者ID＞0の場合
      if ((newValue as TantoCmnShokuin).chkShokuId > '0') {
        // 担当ケアマネ
        local.or30908.tantoId = (newValue as TantoCmnShokuin).chkShokuId
      } else {
        local.or30908.tantoId = ''
      }

      // データ再取得
      await init()
    }
  }
)

/**
 * カレンダーと本コンポーネントの値を同期する
 */
watch(calendarEvent, async (newValue) => {
  if (newValue === undefined) {
    return
  }
  if (newValue.confirmFlg && newValue.confirmParams) {
    checkingItem.value = Or30908Const.CHECKING_ITEM_YYMM
    confirmResultYYmm.value = await ac006_3()
    checkingItem.value = ''

    if (confirmResultYYmm.value === Or30908Const.DIALOG_RESULT_CANCEL) {
      local.or30908.yymmYm.value = beforeYearMonth.value
      localOneway.orX0165Plan.pageLabel = beforeYearMonth.value
      confirmResultYYmm.value = Or30908Const.STATUS_RESET
      return
    }
    // カレンダーダイアログの入力内容を設定する
    local.or30908.yymmYm.value = newValue.confirmParams.startDate
    formatDate()

    // 子コンポーネントのflgをリセットする
    Gui00048Logic.event.set({
      uniqueCpId: gui00048.value.uniqueCpId,
      events: {
        confirmFlg: false,
        confirmParams: undefined,
      },
    })

    await init()
  }
})

/**
 * カレンダーダイアログ表示
 * アイコン押下によってダイアログ表示
 */
function openCalendar() {
  Gui00048Logic.state.set({
    uniqueCpId: gui00048.value.uniqueCpId,
    state: {
      isOpen: true, // 開閉フラグ
      startDate: local.or30908.yymmYm.value, // 開始年月
    },
  })
}

/**
 * AC012 AC015「前のアイコン」押下 「次のアイコン」押下
 *
 * @param b - 加減フラグ
 */
const addMonth = async (b: boolean) => {
  checkingItem.value = Or30908Const.CHECKING_ITEM_YYMM
  const confirmResult = await ac006_3()
  checkingItem.value = ''

  if (confirmResult === Or30908Const.DIALOG_RESULT_CANCEL) {
    return
  }
  isAddMonth.value = true

  // YYYY/MM形式の文字列をDateオブジェクトに変換
  const [year, month] = local.or30908.yymmYm.value.split('/').map(Number)
  const date = new Date(year, month - 1)
  if (b) {
    date.setMonth(date.getMonth() + 1)
  } else {
    date.setMonth(date.getMonth() - 1)
  }
  // 加減後の年月をYYYY/MM形式で返す
  const newYear = date.getFullYear()
  const newMonth = (date.getMonth() + 1).toString().padStart(2, '0')
  local.or30908.yymmYm.value = `${newYear}/${newMonth}`
  localOneway.orX0165Plan.pageLabel = `${newYear}/${newMonth}`

  // データ再取得
  await init()
}
/**
 * 文字列の形式の判定
 * YYYY/MM/DD または YYYY/MM の形式であるか照合する
 * 月や日が1桁の場合も許容する
 *
 * @param str -照合対象の文字列
 */
function isValidDateFormat(str: string): boolean {
  // 正規表現(YYYY/M/D or YYYY/MM/DD or YYYY/M or YYYY/MM)
  const regex = /^(\d{4})\/([1-9]|0[1-9]|1[0-2])(\/([1-9]|0[1-9]|[12][0-9]|3[01]))?$/

  // 正規表現と指定された文字列を照合
  return regex.test(str)
}
/**
 * YYYY/MM/DD形式をYYYY/MM/DD形式に変更
 */
// AC013 AC014  変更前の年月を保存する変数
const beforeYearMonth = ref(props.onewayModelValue.yymmYm)
const formatDate = () => {
  if (isValidDateFormat(local.or30908.yymmYm.value)) {
    const date = new Date(local.or30908.yymmYm.value)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    local.or30908.yymmYm.value = `${year}/${month}`
    localOneway.orX0165Plan.pageLabel = `${year}/${month}`
    beforeYearMonth.value = local.or30908.yymmYm.value
  } else {
    local.or30908.yymmYm.value = beforeYearMonth.value
    localOneway.orX0165Plan.pageLabel = beforeYearMonth.value
  }
}

//  AC022 利用者情報リストを取得する
const getRiyouInfoList = async (svJigyoIdMeisai: string) => {
  // 担当者リスト
  const tantoList: TantoInfo[] = []
  local.or30908.tantoshaList.forEach((item) => {
    tantoList.push({ tantoshaId: item.chkShokuId })
  })
  // バックエンドAPIから初期情報取得
  const inputData: UserInfoSelectInEntity = {
    /** 事業所ID */
    svJigyoId: isEmpty(local.or30908.svJigyoId)
      ? localOneway.or30908.svJigyoId
      : local.or30908.svJigyoId,
    /** 提供年月 */
    yymmYm: local.or30908.yymmYm.value,
    /** 明細事業者ID */
    svJigyoIdMeisai: svJigyoIdMeisai,
    /** 担当者リスト */
    tantoList: tantoList,
  }
  const resData: OfferOfficeInfoSelectOutEntity = await ScreenRepository.select(
    'userInfoSelect',
    inputData
  )

  local.or30908.riyouInfoList = resData.data.riyouInfoList ?? []

  // 利用者一覧を設定
  setRiyouInfoTable()
}

const userheightStr = ref('0px')

/**
 * 利用者一覧の設定
 */
function setRiyouInfoTable() {
  // 既存データをクリア
  mo01334OnewayUser.value.items.length = 0
  mo01334TypeUser.value.value = ''

  if (local.or30908.riyouInfoList.length > 0) {
    // 各合計値
    svTensuTol.value = 0
    kTensuTol.value = 0
    kTensuOverTol.value = 0
    local.or30908.riyouInfoList.forEach((item) => {
      // 合計単位数
      if (item.svTensu) {
        svTensuTol.value += Number(item.svTensu)
        localOneway.svTensuTol.itemLabel = svTensuTol.value.toString()
      }
      // 限度内
      if (item.kTensu) {
        kTensuTol.value += Number(item.kTensu)
        localOneway.kTensuTol.itemLabel = kTensuTol.value.toString()
      }
      // 限度額超過
      if (item.kTensuOver) {
        kTensuOverTol.value += Number(item.kTensuOver)
        localOneway.kTensuOverTol.itemLabel = kTensuOverTol.value.toString()
      }
    })

    if (local.or30908.riyouInfoList.length > 5) {
      mo01334OnewayUser.value.height = 30 + 36 * 5
      userheightStr.value = '0px'
    } else {
      mo01334OnewayUser.value.height = 30 + 36 * local.or30908.riyouInfoList.length

      userheightStr.value = 36 * (5 - local.or30908.riyouInfoList.length) + 'px'
    }
  } else {
    localOneway.svTensuTol.itemLabel = '0'
    localOneway.kTensuTol.itemLabel = '0'
    localOneway.kTensuOverTol.itemLabel = '0'

    mo01334OnewayUser.value.height = 30 + 36 * 1
    userheightStr.value = 36 * 4 + 'px'
  }

  mo01334OnewayUser.value.items = local.or30908.riyouInfoList
  localOneway.usematter.itemLabel = `${addCommas(String(local.or30908.riyouInfoList.length))}${Or30908Const.STR_BLANK_ZENKAKU}${t('label.item')}`
}

// numberデータにコンマを追加
function addCommas(numStr: string | undefined) {
  if (numStr === undefined) {
    return ''
  }
  return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**************************************************
 * ウォッチャー
 **************************************************/

// 提供年月の場合の確認結果
const confirmResultYYmm = ref('')

// 事業所一覧選択変更時
watch(
  () => mo01334TypeOffice.value.value,
  async (newValue) => {
    if (newValue === '') {
      return
    }
    selectedItem.value =
      local.or30908.jigyouInfoList.find((item) => item.id === newValue)?.svJigyoId ?? ''

    // 利用者情報リストを取得する
    await getRiyouInfoList(selectedItem.value)
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      setOr11871Event({ favoriteEventFlg: false })
      return
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await save()
      setOr11871Event({ saveEventFlg: false })
      return
    }
    if (newValue?.printEventFlg) {
      setOr11871Event({ printEventFlg: false })
      // 印刷ボタンが押下された場合、GUI01211_印刷設定をポップアップで起動する。
      // Or35052のダイアログ開閉状態を更新する
      Or35052Logic.state.set({
        uniqueCpId: or35052.value.uniqueCpId,
        state: { isOpen: true },
      })
    }
  }
)

watch(
  () => [
    Or29841Logic.state.get(or29841.value.uniqueCpId)?.isOpen,
    // Or53427Logic.state.get(or53427.value.uniqueCpId)?.isOpen,
  ],
  async (val, val3) => {
    if (!val || !val3) {
      await init()
    }
  }
)
/** 「計画転送ボタン」押下  */
const onClickPlanDelivery = async () => {
  // 職員をチェックする
  // 親画面.職員ID ＝ null or 0の場合
  if (!localOneway.or30908.shokuinId || localOneway.or30908.shokuinId === '0') {
    return
  }
  // 共通関数_保存権限チェック
  // 権限がない場合

  // if (!(await hasRegistAuth())) {
  //   openInfoDialogOk(t('message.i-cmn-10498'))
  //   return
  // }

  // 画面入力データの変更がある場合
  const confirmResult = await ac006_3()
  if (confirmResult === Or30908Const.DIALOG_RESULT_CANCEL) {
    return
  }
  // 支援事業所ID
  or29841Data.supportOfficeId = local.or30908.svJigyoId
  // 処理年月
  or29841Data.processDate = local.or30908.yymmYm.value
  // 50音
  or29841Data.gojuuon = [
    {
      // 行番号
      gojuuOnRowNo: props.onewayModelValue.gojuuonRowNo,
      // 母音
      gojuuOnKana: props.onewayModelValue.gojuuonKana[0],
    },
  ]
  // ロケーション
  or29841Data.location = props.onewayModelValue.location
  const chkShokuIdInfo = orX0145Type.value.value as TantoCmnShokuin
  // 職員ID
  or29841Data.staffId = chkShokuIdInfo.chkShokuId

  Or29841Logic.state.set({
    uniqueCpId: or29841.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/** 「実績取込ボタン」押下  */
const onClickpPrformanceEvaluation = async () => {
  // 職員をチェックする
  // 親画面.職員ID ＝ null or 0の場合
  if (!localOneway.or30908.shokuinId || localOneway.or30908.shokuinId === '0') {
    return
  }
  // 共通関数_保存権限チェック
  // 権限がない場合

  if (!(await hasRegistAuth())) {
    openInfoDialogOk(t('message.i-cmn-10498'))
    return
  }

  // 共通関数.閲覧権限をチェックする
  // 権限がない場合
  if (!(await hasViewAuth())) {
    openInfoDialogOk(t('message.i-cmn-10497', [t('label.using-slip')]))
    return
  }

  // 画面入力データの変更がある場合
  const confirmResult = await ac006_3()
  if (confirmResult === Or30908Const.DIALOG_RESULT_CANCEL) {
    return
  }
}

/** 「計画実績ボタン」押下  */
const onClickProjectPerformance = async () => {
  // 職員をチェックする
  // 親画面.職員ID ＝ null or 0の場合
  if (!localOneway.or30908.shokuinId || localOneway.or30908.shokuinId === '0') {
    return
  }
  // 共通関数_保存権限チェック
  // 権限がない場合

  // if (!(await hasRegistAuth())) {
  //   openInfoDialogOk(t('message.i-cmn-10498'))
  //   return
  // }

  // 画面入力データの変更がある場合
  const confirmResult = await ac006_3()
  if (confirmResult === Or30908Const.DIALOG_RESULT_CANCEL) {
    return
  }

  // Or53427Logic.state.set({
  //   uniqueCpId: or53427.value.uniqueCpId,
  //   state: { isOpen: true },
  // })
}
</script>

<template>
  <c-v-sheet class="view">
    <!-- Or11871：有機体：画面メニューエリア -->
    <g-base-or11871
      v-bind="or11871"
      class="sticky-header"
    >
      <template
        v-if="local.tensouFlag === '1'"
        #optionMenuItems
      >
        <div style="cursor: pointer">
          <!-- 計画転送 -->
          <c-v-list-item
            :title="t('label.delivery')"
            prepend-icon="open_in_browser"
            @click="onClickPlanDelivery"
          />
          <!-- 実績取込 -->
          <c-v-list-item
            :title="t('label.performance-evaluation')"
            prepend-icon="open_in_browser"
            @click="onClickpPrformanceEvaluation"
          />
          <!-- 計画実績 -->
          <c-v-list-item
            :title="t('label.plan-result')"
            prepend-icon="open_in_browser"
            @click="onClickProjectPerformance"
          />
          <!-- 再計算 -->
          <!-- <base-mo00611
            :oneway-model-value="localOneway.mo00611RecomputeOneway"
            @click="recompute"
          /> -->
        </div>
      </template>
    </g-base-or11871>
    <c-v-row class="content-area">
      <c-v-col
        cols="12"
        class="h-100 ml-3"
      >
        <c-v-row class="ma-0 mb-6">
          <c-v-col
            cols="auto"
            class="pa-0"
          >
            <g-base-or-41179
              v-bind="or41179"
              class="office-dropdown"
            />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="pa-0 ml-6"
          >
            <!-- 提供年月 -->
            <g-custom-or-x0165
              :oneway-model-value="localOneway.orX0165Plan"
              class="offer-year-month"
              @on-click-edit-btn="openCalendar"
              @on-click-pre-btn="addMonth(false)"
              @on-click-post-btn="addMonth(true)"
            />
            <!-- 年月選択 -->
            <g-custom-gui-00048
              v-if="showDialog"
              v-bind="gui00048"
            />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="pa-0 ml-6"
          >
            <!-- 担当ケアマネプルダウン -->
            <g-custom-or-x-0145
              v-bind="orx0145"
              v-model="orX0145Type"
              :oneway-model-value="localOneway.orX0145Oneway"
              class="no-ml"
            ></g-custom-or-x-0145>
          </c-v-col>
        </c-v-row>
        <c-v-row
          class="d-flex"
          style="border-top: 1px solid #ddd"
        >
          <c-v-col
            cols="5"
            class="align-center"
          >
            <base-mo-01334
              v-model="mo01334TypeOffice"
              :oneway-model-value="mo01334OnewayOffice"
              class="list-wrapper mt-6"
            >
              <template #[`item.ruakuKnj`]="{ item }">
                <span class="flex-center">{{ item.ruakuKnj }}</span>
              </template>
              <template #[`item.keikaku`]="{ item }">
                <base-mo00018
                  v-model="item.keikakuObj"
                  :oneway-model-value="{
                    showItemLabel: false,
                    customClass: new CustomClass({ outerClass: 'mr-0' }),
                  }"
                  class="flex-center"
                ></base-mo00018>
              </template>
              <template #[`item.jisseki`]="{ item }">
                <base-mo00018
                  v-model="item.jissekiObj"
                  :oneway-model-value="{
                    showItemLabel: false,
                    customClass: new CustomClass({ outerClass: 'mr-0' }),
                  }"
                  class="flex-center"
                ></base-mo00018>
              </template>
              <!-- ページングを非表示 -->
              <template #bottom />
            </base-mo-01334>
            <v-c-row class="blank-row-office"></v-c-row>
            <v-c-row
              no-gutters
              class="d-flex align-center row-name pt-6 pb-6"
            >
              <c-v-col
                cols="7"
                class="fontc-only-one"
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  background-color: #e6e6e6;
                  padding: 0 !important;
                  height: 36px;
                "
              >
                <c-v-col
                  no-gutters
                  cols="9"
                  class="textalign-start pl-3"
                >
                  <label>{{ localOneway.mumber.itemLabel }}</label>
                </c-v-col>
                <c-v-col
                  cols="3"
                  class="flex-end"
                >
                  <label>{{ localOneway.matter.itemLabel }}</label>
                </c-v-col>
              </c-v-col>
            </v-c-row>
          </c-v-col>
          <c-v-col cols="8"></c-v-col>
        </c-v-row>
        <c-v-row
          flex="none"
          style="border-top: 1px solid #ddd"
        >
          <c-v-col cols="7">
            <base-mo-01334
              v-model="mo01334TypeUser"
              :oneway-model-value="mo01334OnewayUser"
              class="list-wrapper mt-6"
            >
              <template #[`item.gendoGaku`]="{ item }">
                <div style="text-align: right">{{ addCommas(item.gendoGaku) }}</div>
              </template>
              <template #[`item.svTensu`]="{ item }">
                <div style="text-align: right">{{ addCommas(item.svTensu) }}</div>
              </template>
              <template #[`item.kTensu`]="{ item }">
                <div style="text-align: right">{{ addCommas(item.kTensu) }}</div>
              </template>
              <template #[`item.kTensuOver`]="{ item }">
                <div style="text-align: right">{{ addCommas(item.kTensuOver) }}</div>
              </template>
              <!-- ページングを非表示 -->
              <template #bottom />
            </base-mo-01334>
            <v-c-row class="blank-row"></v-c-row>
            <v-c-row
              no-gutters
              class="d-flex row-name pt-6 pb-4"
              style="justify-content: space-between; margin-top: 80px"
            >
              <c-v-col
                cols="5"
                class="fontc-only-one"
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  padding: 0 !important;
                  background-color: #e6e6e6;
                  height: 36px;
                "
              >
                <c-v-col
                  no-gutters
                  cols="9"
                  class="textalign-start pl-3"
                >
                  <label>{{ localOneway.mumber.itemLabel }}</label>
                </c-v-col>
                <c-v-col
                  no-gutters
                  cols="3"
                  class="flex-end"
                >
                  <label>{{ localOneway.usematter.itemLabel }}</label>
                </c-v-col>
              </c-v-col>
              <v-spacer></v-spacer>
              <c-v-col
                cols="5"
                class="fontc"
                style="
                  display: flex;
                  flex-direction: row;
                  align-items: end;
                  background-color: #e6e6e6;
                  padding: 0 !important;
                  height: 36px;
                  max-width: 530px;
                  min-width: 410px;
                "
              >
                <c-v-col
                  no-gutters
                  class="textalign"
                  style="width: 25%"
                >
                  <label>{{ localOneway.unitCount.itemLabel }}</label>
                </c-v-col>
                <c-v-col
                  no-gutters
                  class="flex-end"
                  style="width: 25%"
                >
                  <label>{{ addCommas(localOneway.svTensuTol.itemLabel) }}</label>
                </c-v-col>
                <c-v-col
                  class="flex-end"
                  style="width: 25%"
                >
                  <label>{{ addCommas(localOneway.kTensuTol.itemLabel) }}</label>
                </c-v-col>
                <c-v-col
                  no-gutters
                  class="flex-end"
                  style="width: 25%"
                >
                  <label>{{ addCommas(localOneway.kTensuOverTol.itemLabel) }}</label>
                </c-v-col>
              </c-v-col>
            </v-c-row>
          </c-v-col>
          <c-v-col cols="2"></c-v-col>
        </c-v-row>
        <!-- <c-v-row
          flex="none"
          style="border-top: 1px solid #ddd; padding-bottom: 24px"
        ></c-v-row> -->
      </c-v-col>
    </c-v-row>
    <!-- <g-base-or00051></g-base-or00051> -->
  </c-v-sheet>
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <g-base-or21814
    v-if="showDialogOr21814_1"
    v-bind="or21814_1"
  />
  <g-base-or21813
    v-if="showDialogOr21813_1"
    v-bind="or21813_1"
  />
  <!-- GUI01211_［印刷設定］画面 -->
  <g-custom-or-35052
    v-if="showDialogOr35052"
    v-bind="or35052"
    :oneway-model-value="or35052Oneway"
  />
  <!-- GUI01189_計画転送 -->
  <g-custom-or-29841
    v-if="showDialogOr29841"
    v-bind="or29841"
    :oneway-model-value="or29841Data"
  />
  <!-- <g-custom-or-53427
    v-if="showDialogOr53427"
    v-bind="or53427"
    :oneway-model-value="or53427Data"
    :unique-cp-id="or53427.uniqueCpId"
  /> -->
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table-list.scss';
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent !important;
}

:deep(.v-sheet) {
  background-color: transparent !important;
}
.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
  background: rgb(var(--v-theme-background));
}
.date-month {
  :deep(.d-flex) {
    width: 145px !important;
  }
}
.lbl1 {
  margin-left: 8px;
}
.table-wrapper :deep(.v-table__wrapper) {
  overflow-y: auto;
  overflow-x: hidden;
}

:deep(.v-table__wrapper) {
  height: 250px;
}
:deep(.checkbox-auto) {
  .v-col-auto {
    flex: 1;
  }
}

.textaligncenter {
  text-align: center;
}
.fontc {
  font-weight: normal;
  :deep(div:not(last-child)) {
    border-top: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
    border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
    border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
    border-color: #d6d6d6;
  }
  :deep(div:last-child) {
    border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
    border-color: #d6d6d6;
  }
  :deep(.v-col) {
    padding-left: 8px;
    padding-right: 8px;
  }
  :deep(label) {
    color: #2600ff;
  }
}

.fontc-only-one {
  font-weight: normal;

  border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-color: #d6d6d6;
  :deep(label) {
    color: #2600ff;
  }
}

:deep(.v-checkbox-btn) {
  min-height: 31px !important;
  height: 31px !important;
}

:deep(.v-table > .v-table__wrapper > table > thead > tr > th) {
  font-weight: bold;
}

:deep(.v-col) {
  padding-top: 0px;
  padding-bottom: 0px;
}

.office-dropdown {
  display: block;
}

.office-dropdown :deep(.v-col:first-child) {
  margin-top: 0 !important;
  margin-left: 0 !important;
}

.office-dropdown :deep(.v-col:last-child > .v-sheet) {
  background-color: #ffffff !important;
}

.no-ml {
  :deep(.ma-1) {
    margin-left: 0 !important;
  }
}

:deep(.offer-year-month .v-row) {
  margin: 4px 0 !important;
}

:deep(.offer-year-month .page-label) {
  width: 86px;
  justify-content: center;
}

:deep(.list-wrapper .v-table__wrapper th) {
  background-color: #dbeefe !important;
}

:deep(.v-field__overlay) {
  background-color: #ffffff !important;
}

.flex-center {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-content: center;
}

:deep(.v-data-table-header__content) {
  span {
    font-weight: normal;
  }
}

:deep(td:first-child) {
  border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

:deep(td) {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

:deep(.v-data-table-header__content) {
  justify-content: center;
}
:deep(.v-table > .v-table__wrapper > table > thead > tr > th) {
  height: 30px;
  font-size: 13px;
  padding: 0px 0px;
}

:deep(.v-table > .v-table__wrapper > table > tbody > tr > td) {
  height: 36px !important;
  font-size: 14px;
  padding: 0px 8px;
}

.flex-end {
  height: -webkit-fill-available;
  display: flex;
  align-items: center;
  justify-content: end;
}

.textalign {
  height: -webkit-fill-available;
  align-content: center;
  text-align: center;
}

.textalign-start {
  align-content: center;
  text-align: start;
}

.blank-row {
  display: flex;
  height: v-bind(userheightStr);
}

.blank-row-office {
  display: flex;
  height: v-bind(officeheightStr);
}

.sticky-header {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
}
</style>
