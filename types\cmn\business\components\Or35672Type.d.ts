import type { IssuesAndGoalListItem } from '~/types/cmn/business/components/OrX0209Type'

/**
 * Or35672:アセスメント複写モーダル
 * GUI00807_アセスメント複写
 *
 * @description
 * アセスメント複写Onewayタイプ
 *
 * <AUTHOR>
 */
/**
 * 双方向バインドModelValue
 */
export interface Or35672Type {
  /** パラメータ */
  params?: {
    /** 改定フラグ */
    ninteiFormF?: string
    /** 選択中タブID */
    activeTabId?: string
    /** 事業所ID */
    jigyoId?: string
    /** 法人ID */
    houjinId?: string
    /** 施設ID */
    shisetuId?: string
    /** 利用者ID */
    userId?: string
    /** 計画対象期間ID */
    sc1Id?: string
    /** アセスメントID */
    gdlId?: string
    /** 作成者ID */
    createUserId?: string
    /** 作成日 */
    createdYmd?: string
    /** 種別ID */
    syubetsuId?: string
    /** アセスメント番号 */
    accessmentNo?: string
    /** 適用事業所IDリスト */
    svJigyoIdList?: string[]
    /** 期間管理フラグ */
    kikanFlg?: string
    /** 課題と目標リスト */
    issuesAndGoalsList?: IssuesAndGoalListItem[]
  }
  /** 戻りデータ */
  resData?: {
    /** 課題と目標リスト */
    issuesAndGoalsList?: IssuesAndGoalListItem[]
    /** 利用者ID */
    userId?: string
    /** 作成日 */
    createdYmd?: string
    /** 認定フラグ */
    ninteiFormF?: string
    /** 計画対象期間ID */
    sc1Id?: string
    /** アセスメントID */
    gdlId?: string
  }
}

/**
 * 単方向バインドModelValue
 */
export interface Or35672OnewayType {
  /** タブリスト */
  tabItems: {
    /** id */
    id: string
    /** id */
    name: string
  }[]
  /** 改定フラグ */
  ninteiFormF?: string
  /** 選択中タブ */
  activeTabId?: string
  /** 事業所ID */
  jigyoId?: string
  /** 法人ID */
  houjinId?: string
  /** 施設ID */
  shisetuId?: string
  /** 利用者ID */
  userId?: string
  /** 計画対象期間ID */
  sc1Id?: string
  /** アセスメントID */
  gdlId?: string
  /** 作成者ID */
  createUserId?: string
  /** 作成日 */
  createYmd?: string
  /** 種別ID */
  syubetsuId?: string
  /** アセスメント番号 */
  accessmentNo?: string
  /** 適用事業所IDリスト */
  svJigyoIdList?: string[]
  /** 期間管理フラグ */
  kikanFlg?: string
}

/**
 * 履歴一覧アイテムタイプ
 */
export interface HistoryTableItemsType {
  /** id */
  id: string
  /** 計画期間ID */
  periodId: string
  /** 作成日 */
  createDate: string
  /** 作成者ID */
  createUserId: string
  /** 作成者 */
  createUser: string
  /** 改訂フラグ */
  revision: string
  /** フェースシート */
  ass1: string
  /** 家族状況／支援 */
  ass2: string
  /** ｻｰﾋﾞｽ利用状況 */
  ass3: string
  /** 住居等の状況 */
  ass4: string
  /** 本人の健康状態 */
  ass5: string
  /** 本人の基本動作等1 */
  ass6: string
  /** Ｅの状態 */
  ass7: string
  /** Ｆの状態 */
  ass8: string
  /** Ｇの状態 */
  ass9: string
  /** Ｈの状態 */
  ass10: string
  /** Ｉの状態 */
  ass11: string
  /** Ｊの状態 */
  ass12: string
  /** 本人の基本動作等8 */
  ass13: string
  /** 医師の意見 */
  ass14: string
  /** 全体のまとめ･特記事項 */
  ass15: string
  /** １日のスケジュール */
  ass16: string
  /** 改定フラグ */
  ninteiFormF: string
  /** 課題と目標リスト */
  issuesAndGoalsList: IssuesAndGoalListItem[]
}

/** 課題と目標アイテムタイプ */
export interface IssuesAndGoalsItemsType {
  /** id */
  id?: string
  /** アセスメントID */
  gdlId?: string
  /** 計画期間ID */
  sc1Id?: string
  /** アセスメント番号 */
  assNo?: string
  /** 課題 */
  kadaiKnj?: string
  /** 長期 */
  choukiKnj?: string
  /** 短期 */
  tankiKnj?: string
  /** 連番 */
  seq?: string
  /** 更新回数 */
  modifiedCnt?: string
  /** アセスメント名称 */
  assName?: string
}
