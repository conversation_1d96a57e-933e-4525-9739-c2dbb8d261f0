<script setup lang="ts">
import type { Ref } from 'vue'
import { onMounted, reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or30269Const } from './Or30269.constants'
import type { Or30269Type } from './Or30269.type'

import { useScreenStore, useScreenTwoWayBind } from '#imports'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useNuxtApp } from '#app'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'

const $log = useNuxtApp().$log as DebugLogPluginInterface
/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  modelValue: Or30269Type[]
  onewayModelValue: {
    heisei30Kbn: boolean
  }
}

const props = defineProps<Props>()

const { t } = useI18n()
/**************************************************
 * 変数定義
 **************************************************/

/** One-way */
const localOneway = reactive({
  // 選択肢詳細アイコン
  selectOptionsIcon: {
    btnIcon: 'info',
    name: 'serveyLedgerBtn',
    density: 'compact',
    minWidth: '16px',
    minHeight: '16px',
    height: '24px',
    width: '24px',
    labelColor: '#333333',
  } as Mo00009OnewayType,
})

const local = reactive({
  // 選択肢詳細アイコン
  heisei30Kbn: props.onewayModelValue?.heisei30Kbn,
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or30269Type>({
  cpId: Or30269Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or30269Type> }

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  $log.debug(`★[onMounted] [cpId]${Or30269Const.CP_ID(0)} [uId]${props.uniqueCpId}`)
})

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    refValue.value = cloneDeep(newValue)
    // RefValueを更新する
    useScreenStore().setCpTwoWay({
      cpId: Or30269Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: refValue.value,
      isInit: true,
    })
  },
  { deep: true }
)
</script>

<template>
  <c-v-sheet class="container">
    <!-- 上段 -->
    <c-v-row no-gutters>
      <c-v-col cols="6">
        <!-- 上段 -->
        <c-v-row
          no-gutters
          class="dataHeader"
        >
          <c-v-col
            cols="12"
            class="itemRow"
          >
            <c-v-row no-gutters>
              <div class="col-border title title-1"></div>

              <!-- チェックボックス -->

              <div class="col-border title title-2 label-title-size">
                {{ t('label.current-situation') }}
              </div>
              <div class="col-border title title-2 label-title-size">
                {{ t('label.plan') }}
                <div>
                  <base-mo00009 :oneway-model-value="localOneway.selectOptionsIcon">
                    <c-v-tooltip
                      activator="parent"
                      location="right"
                      interactive
                    >
                      <div class="tooltip-content">
                        <span v-if="local.heisei30Kbn"
                          >{{ t('label.assessment-home-6-6-tooltip-label-1') }}
                        </span>
                        <span v-else>{{ t('label.assessment-home-6-6-tooltip-label-2') }}</span>
                      </div>
                    </c-v-tooltip>
                  </base-mo00009>
                </div>
              </div>
              <div class="title title-4 label-title-size">
                {{ $t('label.concrete-contents') }}
              </div>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-col>

      <c-v-col cols="6">
        <!-- 上段 -->

        <c-v-row
          no-gutters
          class="dataHeader"
          style="border-left: none !important"
        >
          <c-v-col
            cols="12"
            class="itemRow"
          >
            <c-v-row no-gutters>
              <div class="col-border title title-1"></div>

              <!-- チェックボックス -->

              <div class="col-border title title-2 label-title-size">
                {{ t('label.current-situation') }}
              </div>
              <div class="col-border title title-2 label-title-size">
                {{ t('label.plan') }}
                <base-mo00009 :oneway-model-value="localOneway.selectOptionsIcon">
                  <c-v-tooltip
                    activator="parent"
                    location="right"
                    interactive
                  >
                    <div class="tooltip-content">
                      <span v-if="local.heisei30Kbn"
                        >{{ t('label.assessment-home-6-6-tooltip-label-1') }}
                      </span>
                      <span v-else>{{ t('label.assessment-home-6-6-tooltip-label-2') }}</span>
                    </div>
                  </c-v-tooltip>
                </base-mo00009>
              </div>
              <div class="title title-4 label-title-size">
                {{ $t('label.concrete-contents') }}
              </div>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="box-outline"
    >
      <!-- 左段 -->
      <!-- 要介護認定項目セクション -->

      <!-- 中段 -->
      <c-v-col cols="6">
        <!-- 上段 -->
        <c-v-row
          v-for="(sub, index) in refValue"
          :key="index"
          no-gutters
          class="dataList-left"
        >
          <c-v-col
            v-if="index !== 3"
            :id="props.uniqueCpId + index"
            cols="12"
            class="itemRow"
          >
            <c-v-row no-gutters>
              <div
                v-if="index === 0"
                cols="auto"
                class="left-label left-label-1 label-title-size"
              >
                <div>
                  {{ t('label.assessment-home-6-6-current-situation-of-assistance-label-1') }}
                </div>
              </div>
              <div
                v-else-if="index === 1"
                class="left-label left-label-2 label-title-size"
              >
                <div
                  class="pb-2"
                  style="color: #a3a3a3"
                >
                  {{ t('label.assessment-home-6-6-current-situation-of-assistance-label-2') }}
                </div>
                <div>
                  {{ t('label.assessment-home-6-6-current-situation-of-assistance-label-3') }}
                </div>
              </div>
              <div
                v-else-if="index === 2"
                class="left-label left-label-3 label-title-size"
                style="color: #a3a3a3"
              >
                <div>
                  {{ t('label.assessment-home-6-6-current-situation-of-assistance-label-3') }}
                </div>
              </div>
              <!-- <div> -->
              <div>
                <div
                  v-for="(item, id) in sub.items"
                  :key="index + '_' + id"
                  class="d-flex"
                  :style="{ height: (1 / sub.items.length) * 100 + '%' }"
                >
                  <div
                    :id="props.uniqueCpId + index + id"
                    class="itemRow d-flex align-center"
                    style="height: 100%"
                  >
                    <div class="col-border d-flex align-center check-box">
                      <div
                        :style="{
                          paddingTop: id === 0 ? '4px' : '0',
                          paddingBottom: id === sub.items.length - 1 ? '10px' : '0',
                        }"
                      >
                        <!-- チェックボックス -->
                        <base-mo00018
                          v-model="item.statusCheck"
                          :oneway-model-value="{
                            showItemLabel: false,
                            isVerticalLabel: false,
                          }"
                          @click.stop
                        >
                          <c-v-tooltip
                            activator="parent"
                            location="bottom"
                            :text="item.label"
                          />
                        </base-mo00018>
                      </div>
                    </div>

                    <div class="col-border d-flex align-center check-box">
                      <div
                        :style="{
                          paddingTop: id === 0 ? '4px' : '0',
                          paddingBottom: id === sub.items.length - 1 ? '10px' : '0',
                        }"
                      >
                        <!-- チェックボックス -->
                        <base-mo00018
                          v-model="item.planCheck"
                          :oneway-model-value="{
                            showItemLabel: false,
                            isVerticalLabel: false,
                          }"
                          @click.stop
                        >
                          <c-v-tooltip
                            activator="parent"
                            location="bottom"
                            :text="item.label"
                          />
                        </base-mo00018>
                      </div>
                    </div>
                  </div>

                  <div
                    class="itemLabel"
                    :style="{
                      paddingTop: id === 0 ? '4px' : '0',
                      paddingBottom: id === sub.items.length - 1 ? '10px' : '0',
                    }"
                  >
                    <span class="text">{{ item.label }}</span>
                  </div>
                </div>
              </div>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <!-- 中段 -->
      <c-v-col cols="6">
        <!-- 上段 -->
        <c-v-row
          v-for="(sub, index) in props.modelValue"
          :key="index"
          no-gutters
          class="dataList-right"
        >
          <c-v-col
            v-if="index === 3"
            :id="props.uniqueCpId + index"
            cols="12"
            class="itemRow"
          >
            <c-v-row no-gutters>
              <div
                v-if="index === 3"
                class="left-label left-label-4 label-title-size"
              >
                <div
                  class="pb-2"
                  style="color: #a3a3a3"
                >
                  {{ t('label.assessment-home-6-6-current-situation-of-assistance-label-3') }}
                </div>
                <div>
                  {{ t('label.assessment-home-6-6-current-situation-of-assistance-label-6') }}
                </div>
              </div>

              <!-- <div> -->
              <div>
                <div
                  v-for="(item, id) in sub.items"
                  :key="index + '_' + id"
                  class="d-flex"
                  :style="{ height: (1 / sub.items.length) * 100 + '%' }"
                >
                  <div
                    :id="props.uniqueCpId + index + id"
                    class="itemRow d-flex align-center"
                    style="height: 100%"
                  >
                    <div class="col-border d-flex align-center check-box">
                      <div
                        :style="{
                          paddingTop: id === 0 ? '4px' : '0',
                          paddingBottom: id === sub.items.length - 1 ? '10px' : '0',
                        }"
                      >
                        <!-- チェックボックス -->
                        <base-mo00018
                          v-model="item.statusCheck"
                          :oneway-model-value="{
                            showItemLabel: false,
                            isVerticalLabel: false,
                          }"
                          @click.stop
                        >
                          <c-v-tooltip
                            activator="parent"
                            location="bottom"
                            :text="item.label"
                          />
                        </base-mo00018>
                      </div>
                    </div>

                    <div class="col-border d-flex align-center check-box">
                      <div
                        :style="{
                          paddingTop: id === 0 ? '4px' : '0',
                          paddingBottom: id === sub.items.length - 1 ? '10px' : '0',
                        }"
                      >
                        <!-- チェックボックス -->
                        <base-mo00018
                          v-model="item.planCheck"
                          :oneway-model-value="{
                            showItemLabel: false,
                            isVerticalLabel: false,
                          }"
                          @click.stop
                        >
                          <c-v-tooltip
                            activator="parent"
                            location="bottom"
                            :text="item.label"
                          />
                        </base-mo00018>
                      </div>
                    </div>
                  </div>

                  <div
                    class="itemLabel"
                    :style="{
                      paddingTop: id === 0 ? '4px' : '0',
                      paddingBottom: id === sub.items.length - 1 ? '10px' : '0',
                    }"
                  >
                    <span class="text">{{ item.label }}</span>
                  </div>
                </div>
              </div>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
</template>

<style scoped lang="scss">
@use '@/styles/base-data-table-list.scss';
.title-1 {
  width: 156px;
  background: rgb(var(--v-theme-blue-100));
}

.title-2 {
  width: 74px;
  background: white;
}

.title-4 {
  background: white;
  padding-left: 12px;
}

.container {
  .dataHeader {
    background: rgba(var(--v-theme-surface));
    border: 1px rgb(var(--v-theme-black-200)) solid !important;

    .v-row {
      .col-border {
        border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
        padding: 8px !important;
      }
    }

    .title {
      word-wrap: break-word;
      white-space: pre-line;
      text-orientation: upright;
      // font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 64px;
      :deep(.material-symbols-rounded) {
        font-variation-settings:
          'FILL' 1,
          'wght' 400,
          'GRAD' 0,
          'opsz' 20;
        opacity: 0.5;
        font-size: 20px;
      }
    }
  }

  .dataList-left:not(:last-child) {
    background: rgba(var(--v-theme-surface));
    border: 1px rgb(var(--v-theme-black-200)) solid !important;

    .v-row {
      .col-border {
        border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
      }
    }

    &.v-row {
      border-top: unset !important;
    }
  }
  .dataList-right:last-child {
    background: rgba(var(--v-theme-surface));
    border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
    border-right: 1px rgb(var(--v-theme-black-200)) solid !important;

    .v-row {
      .col-border {
        border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
      }
    }

    &.v-row {
      border-top: unset !important;
    }
  }

  .word-break {
    word-break: normal;
    white-space: pre-wrap !important;
  }

  :deep(.v-selection-control .v-label) {
    display: inline-table !important;
    line-height: 24px !important;
  }

  :deep(.v-checkbox .v-checkbox-btn) {
    min-height: 24px;
    display: flex;
    align-items: center;
    height: unset !important;
  }

  :deep(.v-selection-control__wrapper) {
    width: 24px !important;
    height: 24px !important;
  }
  .text {
    // padding-right: 36px;
    white-space: nowrap;
  }
}
.left-label {
  width: 156px;
  height: 100%;
  padding-left: 12px;
  display: flex;
  align-items: center;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  background: rgb(var(--v-theme-blue-100));
}
.left-label-1 {
  height: 114px;
}
.left-label-2 {
  height: 217px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.left-label-3 {
  height: 113px;
}
.left-label-4 {
  height: 446px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.check-box {
  width: 74px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.itemLabel {
  display: flex;
  align-items: center;
  padding-left: 12px;
}

.label-title-size {
  font-size: 13px;
}
.container {
  color: #333333;
}
</style>
