import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or51813:有機体:受診状況情報画面
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or51813Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or51813', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * 機能名:ｱｾｽﾒﾝﾄ
     */
    export const FUNCTION_NAME_ASSESSMENT = '[MNU3][3GK][GDL]ｱｾｽﾒﾝﾄ'
    /**
     * ※アセスメント画面の様式版数は下記値と定義されています：
     * 5:R3/4改訂版
     * 4:H21/4改訂版
     * 3:H18/4改訂版
     * 2:H15/4改訂版
     * 1:改訂前
     */
    /**
     * アセスメント画面の様式版数
     * 5:R3/4改訂版
     */
    export const STYLE_NUMBER_R34 = '5'
    /**
     * アセスメント画面の様式版数
     * 4:H21/4改訂版
     */
    export const STYLE_NUMBER_H214 = '4'
    /**
     * アセスメント画面の様式版数
     * 3:H18/4改訂版
     */
    export const STYLE_NUMBER_H184 = '3'
    /**
     * アセスメント画面の様式版数
     * 2:H15/4改訂版
     */
    export const STYLE_NUMBER_H154 = '2'
    /**
     * アセスメント画面の様式版数
     * 1:改訂前
     */
    export const STYLE_NUMBER_BEFORE_REVISION = '1'
  }
}
