<script setup lang="ts">
import { computed, ref, watch, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore } from '#imports'
import { Or27347Const } from '~/components/custom-components/organisms/Or27347/Or27347.constants'
import type {
  Or27347OnewayType,
  Or27347Type,
  IssuesList,
} from '~/types/cmn/business/components/Or27347Type'
import { Or27347Logic } from '~/components/custom-components/organisms/Or27347/Or27347.logic'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01097'
// ルーティング
const routing = 'GUI01097/pinia'
// 画面物理名
const screenName = 'GUI01097'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27347 = ref({ uniqueCpId: Or27347Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01097' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27347Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27347.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01097',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27347Const.CP_ID(0) }],
})

// ダイアログ表示フラグ
const showDialogOr27347 = computed(() => {
  // Or27347のダイアログ開閉状態
  return Or27347Logic.state.get(or27347.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or27347)
 *
 * @param processType - processType
 */
function onClickOr27347(processType: string) {
  or27347Data.processType = processType
  // Or27347のダイアログ開閉状態を更新する
  Or27347Logic.state.set({
    uniqueCpId: or27347.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 親画面からの初期値
 */
const or27347Data: Or27347OnewayType = {
  processType: '',
  issuesList: [
    {
      week1Id: '1',
      issuesNum: 1,
      comprehensiveIs: 'あいうえおかきくけこさしすせそたちつてとなにぬねのあああいいかきくけごた',
      issGoalProposal: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
      specificIntention: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
      goal: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
    },
    {
      week1Id: '2',
      issuesNum: 2,
      comprehensiveIs: 'あいうえおかきくけこさしすせそたちつてとなにぬねのあああいいかきくけごた',
      issGoalProposal: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
      specificIntention: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
      goal: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
    },
    {
      week1Id: '3',
      issuesNum: 3,
      comprehensiveIs: 'あいうえおかきくけこさしすせそたちつてとなにぬねのあああいいかきくけごた',
      issGoalProposal: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
      specificIntention: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
      goal: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
    },
    {
      week1Id: '4',
      issuesNum: 4,
      comprehensiveIs: 'あいうえおかきくけこさしすせそたちつてとなにぬねのあああいいかきくけごた',
      issGoalProposal: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
      specificIntention: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
      goal: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
    },
  ],
}

const or27347Type = ref<Or27347Type>({
  issuesList: [],
})

watch(or27347Type, () => {
  console.log(or27347Type.value)
})

const or27347Data2 = {
  issuesList: [
    {
      week1Id: '1',
      issuesNum: 1,
      comprehensiveIs: 'あいうえおかきくけこさしすせそたちつてとなにぬねのあああいいかきくけごた',
      issGoalProposal: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
      specificIntention: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
      goal: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
    },
  ] as IssuesList[],
}

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 計画表ID
  week1Id: { value: '1' } as Mo00045Type,
  // 課題番号
  issuesNum: { value: '1' } as Mo00045Type,
  // 総合的課題
  comprehensiveIs: {
    value: 'あいうえおかきくけこさしすせそたちつてとなにぬねのあああいいかきくけごた',
  } as Mo00045Type,
  // 課題に対する目標と具体策の提案
  issGoalProposal: {
    value: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
  } as Mo00045Type,
  // 具体策についての意向 本人・家族
  specificIntention: {
    value: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
  } as Mo00045Type,
  // 目標
  goal: {
    value: 'あいうえおかきくけごさしすせそたちつてとなにぬねのあああいいかきくけごた',
  } as Mo00045Type,
})

watch(
  () => [
    local.week1Id,
    local.issuesNum,
    local.comprehensiveIs,
    local.issGoalProposal,
    local.specificIntention,
    local.goal,
  ],
  () => {
    const week1IdjList = local.week1Id.value.split(',')
    const issuesNumList = local.issuesNum.value.split(',')
    const comprehensiveIsList = local.comprehensiveIs.value.split(',')
    const issGoalProposalList = local.issGoalProposal.value.split(',')
    const specificIntentionList = local.specificIntention.value.split(',')
    const goalList = local.goal.value.split(',')
    or27347Data2.issuesList = []
    week1IdjList.forEach((item, index) => {
      or27347Data2.issuesList.push({
        week1Id: item,
        issuesNum: Number(issuesNumList[index]),
        comprehensiveIs: comprehensiveIsList[index],
        issGoalProposal: issGoalProposalList[index],
        specificIntention: specificIntentionList[index],
        goal: goalList[index],
      })
    })
  },
  { deep: true }
)

const or27347onClick = () => {
  or27347Data.issuesList = or27347Data2.issuesList
  // Or27347のダイアログ開閉状態を更新する
  Or27347Logic.state.set({
    uniqueCpId: or27347.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27347('true')"
        >GUI01097_課題番号変更(A4横2枚、A4横3枚の場合)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27347('false')"
        >GUI01097_課題番号変更(A3横1枚の場合)
      </v-btn>
      <g-custom-or-27347
        v-if="showDialogOr27347"
        v-bind="or27347"
        v-model="or27347Type"
        :oneway-model-value="or27347Data"
      />
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px; margin-bottom: 12px">計画表ID(','つぎて----- list)</div>
  <div style="margin-left: 20px">計画表ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.week1Id"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>

  <div style="margin-left: 20px">課題番号</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.issuesNum"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">総合的課題</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.comprehensiveIs"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">課題に対する目標と具体策の提案</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.issGoalProposal"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">具体策についての意向 本人・家族</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.specificIntention"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">目標</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.goal"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>

  <div style="display: flex">
    <div
      v-for="item in or27347Data2.issuesList"
      :key="item.week1Id"
      style="margin-left: 20px; margin-top: 10px"
    >
      <div>
        計画表ID: <span style="color: red">{{ item.week1Id }}</span>
      </div>
      <div>
        総合的課題: <span style="color: red">{{ item.issuesNum }}</span>
      </div>
      <div>
        課題に対する目標と具体策の提案: <span style="color: red">{{ item.comprehensiveIs }}</span>
      </div>
      <div>
        具体策についての意向 本人・家族:<span style="color: red">{{ item.issGoalProposal }}</span>
      </div>
      <div>
        目標:<span style="color: red">{{ item.goal }}</span>
      </div>
    </div>
  </div>

  <div class="pt-5 w-25 pl-5">
    <v-btn @click="or27347onClick"> GUI01097 疎通起動 </v-btn>
  </div>

  <div class="pt-5 w-100 pl-5">
    return ------ data
    <div>
      {{ or27347Type }}
    </div>
  </div>
</template>
