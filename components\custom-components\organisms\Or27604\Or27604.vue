<script setup lang="ts">
/**
 * Or27604:(課題整理総括)課題取込モーダル
 * GUI00923_課題取込画面
 *
 * @description
 * (課題整理総括)課題取込モーダル
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */

import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { Or27604Const } from './Or27604.constants'
import type {
  CpnKkak2RaiTori,
  CpnKkak2RaiToriRireki,
  KrkComKikan,
  KrkComKikanTable,
  Or27604StateType,
} from './Or27604.type'
import { useScreenOneWayBind } from '#build/imports'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { OrCpGroupDefinitionInputFormDeleteDialogOnewayType } from '~/types/business/generator-components/OrCpGroupDefinitionInputFormDeleteDialog'
import type { Or27604OnewayType, Or27604Type } from '~/types/cmn/business/components/Or27604Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CustomClass } from '~/types/CustomClassType'
import type {
  IssueAndImportSelectInEntity,
  IssueAndImportSelectOutEntity,
} from '~/repositories/cmn/entities/IssueAndImportSelectEntity'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
/**
 * useI18n
 */
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or27604Type // モデル値
  uniqueCpId: string // ユニークCPID
  onewayModelValue?: Or27604OnewayType // Onewayモデル値（省略可）
}
/**
 * 引継情報を取得
 */
const props = defineProps<Props>()

/**************************************************
 * イベント発火
 **************************************************/
/**
 * Emit
 */
const emit = defineEmits(['update:modelValue', 'change'])

/**************************************************
 * 変数定義
 **************************************************/
/**
 * デフォルトOnewayモデル値
 */
const defaultOnewayModelValue: Or27604OnewayType = {
  /** 事業者ID */
  svJigyoId: '',
  /** 利用者ID */
  userId: '',
  /** 種別ID */
  syubetsuId: '',
  /** 施設ID */
  shisetuId: '',
  /** 期間管理フラグ */
  kanriFlg: '1',
  /** 期間ID */
  sc1Id: '',
}
/**
 * デフォルトモデル値
 */
const defaultModelValue = {
  or27604: {
    /** アセスメントID */
    raiId: '',
    /** CAPID */
    capId: '',
    /** 課題ID */
    kadaiId: '',
    /** 課題 */
    kadaiKnj: '',
    /** 長期目標 */
    choukiKnj: '',
    /** 短期目標 */
    tankiKnj: '',
    /** ケア内容 */
    careKnj: '',
    /** 表示順2 */
    plan2Sort: '',
    /** 選択状態 */
    sel: '',
    /** 表示順1 */
    plan1Sort: '',
  } as Or27604Type,
  // 計画期間
  krkComKikan: {
    items: [] as KrkComKikanTable[],
  },
  // 課題と目標取込
  cpnKkak2RaiToriRirekiList: {
    items: [] as CpnKkak2RaiToriRireki[],
  },
  // 課題と目標取込明細
  cpnKkak2RaiToriList: {
    items: [] as CpnKkak2RaiTori[],
  },
  // 計画期間BAK
  planPeriodInfoBak: {
    items: [] as KrkComKikanTable[],
  },
  // 課題と目標取込BAK
  cpnKkak2RaiToriRirekiListBak: {
    items: [] as CpnKkak2RaiToriRireki[],
  },
  // 課題と目標取込明細BAK
  cpnKkak2RaiToriListBak: {
    items: [] as CpnKkak2RaiTori[],
  },
}
/**
 * ローカルTwoway
 */
const local = reactive({
  typeList: [] as CodeType[],
  // 本画面
  or27604: {
    ...defaultModelValue,
    ...props.modelValue,
  },
  // 本画面ダイアログ
  mo00024: {
    isOpen: Or27604Const.DEFAULT.IS_OPEN,
  } as Mo00024Type,
  krkComKikan: {
    ...defaultModelValue.krkComKikan,
  },
  cpnKkak2RaiToriRirekiList: {
    ...defaultModelValue.cpnKkak2RaiToriRirekiList,
  },
  cpnKkak2RaiToriList: {
    ...defaultModelValue.cpnKkak2RaiToriList,
  },
  planPeriodInfoBak: {
    ...defaultModelValue.planPeriodInfoBak,
  },
  cpnKkak2RaiToriRirekiListBak: {
    ...defaultModelValue.cpnKkak2RaiToriRirekiListBak,
  },
  cpnKkak2RaiToriListBak: {
    ...defaultModelValue.cpnKkak2RaiToriListBak,
  },
  // 注意ダイアログ
  orConfirmDeleteRowDialog: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
})
/**
 * ローカルOneway
 */
const localOneway = reactive({
  // 本画面
  or27604Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '1000px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10474',
      toolbarTitle: t('label.issues-import'),
      toolbarTitleCenteredFlg: false,
      toolbarName: 'Or27604ToolBar',
      showCardActions: true,
    },
  },
  // 閉じるボタン
  footerClosebtnOneway: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  },
  // 確定ボタン
  footerConfirmbtnOneway: {
    btnLabel: t('btn.confirm'),
    tooltipText: t('tooltip.confirm-btn'),
  },
  // 選択データ無しの確認ダイアログ
  orCpGroupDefinitionInputFormDeleteDialogOneway: {
    msg: t('message.w-cmn-20791'),
    btnNoDisplay: false,
    mo00024Oneway: {
      persistent: true,
    } as Mo00024OnewayType,
    mo00609OnewayYes: {
      class: 'mr-1',
      name: 'deleteDialogYes',
      btnLabel: t('btn.yes'),
      width: '80px',
    } as Mo00609OnewayType,
  } as OrCpGroupDefinitionInputFormDeleteDialogOnewayType,
})

/**
 * 計画期間データテーブルのヘッダー
 */
const planPeriodDataTable = reactive({
  headers: [
    // 計画期間
    {
      title: t('label.plan-period'),
      key: 'planPeriod',
      width: '150px',
      sortable: false,
    },
    // 期間内履歴数
    {
      title: t('label.within-the-period-number-of-history'),
      key: 'numberOfWithinThePeriodHistory',
      width: '100px',
      sortable: false,
    },
  ],
})
/**
 *  課題と目標取込データテーブルのヘッダー
 */
const cpnKkak2RaiToriRirekiDataTable = reactive({
  headers: [
    // 作成日
    {
      title: t('label.create-date'),
      key: 'assDateYmd',
      width: '120px',
    },
    // 作成者
    {
      title: t('label.author'),
      key: 'plnShokuId',
      width: '120px',
    },
    // アセスメント種別
    {
      title: t('label.assessment-kind'),
      key: 'assType',
      width: '200px',
    },
  ],
})
/**
 *  課題と目標取込明細データテーブルのヘッダー
 */
const cpnKkak2RaiToriRirekiItemDataTable = reactive({
  headers: [
    // チェックフラグ
    {
      title: '',
      key: 'checkbox',
      sortable: false,
      minWidth: '80px',
      align: 'center',
    },
    // 課題
    {
      title: t('label.issues'),
      align: 'start',
      key: 'issues',
      sortable: false,
      minWidth: '144px',
    },
    // 長期目標
    {
      title: t('label.long-term-goal'),
      align: 'start',
      key: 'long-term-goal',
      sortable: false,
      minWidth: '220px',
    },
    // 短期目標
    {
      title: t('label.short-term-goal'),
      align: 'start',
      key: 'short-term-goal',
      sortable: false,
      minWidth: '220px',
    },
    // ケア内容
    {
      title: t('label.care-details'),
      align: 'start',
      key: 'care-details',
      sortable: false,
      minWidth: '220px',
    },
  ],
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  console.log('init asdfjkasdhfjkasdhfklhasdfkjhasdjkf aksdfh klasdf aksjdfh asdkfh askdjfh asdkljfh asdkjfh ')
  await initDataAssessmentType()
  await getInitDataInfo()
})

/**
 * 選択された計画期間ID
 */
const selectedIdPlanPeriod = ref<string>('')
/**
 * 選択された課題と目標取込ID
 */
const selectCpnKkak2RaiToriRireki = ref<string>('')
/**
 * 選択された課題と目標取込明細項目
 */
const selectedCpnKkak2RaiToriItem = ref<CpnKkak2RaiTori[]>([])
/**
 * チェックボックスコンポーネント
 */
const mo00018 = ref<Mo00018Type>({
  modelValue: false,
})
/**
 * チェックボックスOnewayコンポーネント
 */
const mo00018OnewayHeader = ref<Mo00018OnewayType>({
  name: '',
  itemLabel: '',
  checkboxLabel: '',
  showItemLabel: false,
  indeterminate: false,
  customClass: new CustomClass({ outerClass: '', labelClass: 'ma-1' }),
})
const mo00018OnewayItem = ref<Mo00018OnewayType>({
  name: '',
  itemLabel: '',
  checkboxLabel: '',
  showItemLabel: false,
  customClass: new CustomClass({ outerClass: '', labelClass: 'ma-1' }),
})
/**
 * 全選択チェックボックスの状態
 * 0: 全選択処理中
 * 1: 個別選択処理中
 */
const chkFlg = ref(0)

/**************************************************
 * Pinia
 **************************************************/
/**
 * Piniaストアバインディング
 */
const { setState } = useScreenOneWayBind<Or27604StateType>({
  cpId: Or27604Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * isOpen状態更新
     *
     * @param value - 新しいisOpen値
     */
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or27604Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * ウォッチャー
 **************************************************/
// 画面開閉フラグ
watch(
  () => local.mo00024.isOpen,
  () => {
    setState({ isOpen: local.mo00024.isOpen })
  }
)

// 注意ダイアログ選択モード
watch(
  () => local.orConfirmDeleteRowDialog.emitType,
  () => {
    if (local.orConfirmDeleteRowDialog.emitType === Or27604Const.DEFAULT.CLICK_YES) {
      local.orConfirmDeleteRowDialog.mo00024.isOpen = false
    }
  }
)

// 全選択チェック
watch(
  () => mo00018.value.modelValue,
  (newValue) => {
    if (chkFlg.value === Or27604Const.DEFAULT.CHK_FLG_0) {
      selectedCpnKkak2RaiToriItem.value = []
      local.cpnKkak2RaiToriList.items?.forEach((item: CpnKkak2RaiTori) => {
        item.checkbox.modelValue = newValue
        if (item.checkbox.modelValue) {
          selectedCpnKkak2RaiToriItem.value.push(item)
        }
      })
    }
  },
  { deep: true, immediate: true }
)

// テーブル内のチェックボックス状態監視
watch(
  () => local.cpnKkak2RaiToriList.items,
  (newItems) => {
    if (!newItems?.length) {
      mo00018.value.modelValue = false
      return
    }

    // すべてのアイテムがチェックされているか確認
    const allChecked = newItems.every((item) => item.checkbox.modelValue)
    const oneChecked = newItems.some((item) => item.checkbox.modelValue)
    if (oneChecked) {
      mo00018OnewayHeader.value.indeterminate = oneChecked && !allChecked
    } else {
      mo00018OnewayHeader.value.indeterminate = false
    }
    // チェックボックスの状態を更新
    if (allChecked) {
      mo00018.value.modelValue = true
    } else {
      mo00018.value.modelValue = false
    }
  },
  { deep: true }
)

/**************************************************
 * イベント処理
 **************************************************/
/**
 * 閉じるボタン押下時の処理
 * モーダルを閉じる
 */
function onClickClose() {
  setState({ isOpen: false })
}

/**
 * 確定ボタン押下時の処理
 * 選択された課題と目標取込情報を親画面に返却する
 */
function onClickConfirm() {
  const returnData = [] as Or27604Type[]
  local.cpnKkak2RaiToriList.items.forEach((item) => {
    if (item.checkbox.modelValue) {
      returnData.push(item)
    }
  })
  if (returnData.length < 1) {
    // 注意ダイアログ表示
    local.orConfirmDeleteRowDialog.mo00024.isOpen = true
  } else {
    // 親画面にデータを返します
    emit('update:modelValue', returnData)
    emit('change', returnData)
    //ダイアログ開閉状態を更新する
    setState({ isOpen: false })
  }
}
/**
 * 行クリック時の処理
 * 選択された行のチェックボックスの状態を更新する
 *
 * @param item - 選択された行のデータ
 */
async function onRowClick(item: CpnKkak2RaiTori) {
  item.checkbox.modelValue = !item.checkbox.modelValue
  await toggleSelectRow()
}
/**
 * 行選択チェックボックス変更時の処理
 * 選択された行の状態を更新し、全選択チェックボックスの状態を同期する
 */
const toggleSelectRow = async () => {
  chkFlg.value = 1
  const items = local.cpnKkak2RaiToriList.items || []

  selectedCpnKkak2RaiToriItem.value = items.filter((item) => item.checkbox.modelValue)

  await nextTick()
  chkFlg.value = 0
}

/**************************************************
 * 関数
 **************************************************/
/**
 * アセスメント種別の初期データを取得する
 * 汎用コードマスタからアセスメント種別のコードを取得し、画面表示用に設定する
 */
async function initDataAssessmentType() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // アセスメント種別
  const result = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND)
  if (result?.length > 0) {
    local.typeList = result
  }
}
/**
 * 初期情報取得
 * APIから計画期間情報を取得し、画面の初期表示データを設定する
 */
async function getInitDataInfo() {
  const inputData: IssueAndImportSelectInEntity = {
    svJigyoId: localOneway.or27604Oneway.svJigyoId,
    userId: localOneway.or27604Oneway.userId,
    syubetsuId: localOneway.or27604Oneway.syubetsuId,
    shisetuId: localOneway.or27604Oneway.shisetuId,
    kanriFlg: localOneway.or27604Oneway.kanriFlg,
  }
  // 初期情報取得
  const resData: IssueAndImportSelectOutEntity = await ScreenRepository.select(
    'issueAndImportSelect',
    inputData
  )
  // 計画期間情報設定
  planPeriodInfoSet(resData)
}

/**
 * 計画期間情報リスト設定
 * 計画期間情報を取得し、テーブルに表示するデータを設定する
 *
 * @param resData - APIから取得した計画期間情報
 */
const planPeriodInfoSet = (resData: IssueAndImportSelectOutEntity) => {
  const dataInfo = resData.data as {
    krkComKikanList: KrkComKikan[]
    cpnKkak2RaiToriRirekiList: CpnKkak2RaiToriRireki[]
    cpnKkak2RaiToriList: CpnKkak2RaiTori[]
  }

  if (!dataInfo) return

  // 計画期間情報取得と設定
  const krkComKikanList = dataInfo.krkComKikanList.map((item: KrkComKikan) => ({
    sc1Id: item.sc1Id,
    planPeriod: `${item.startYmd} ~ ${item.endYmd}`,
    numberOfWithinThePeriodHistory: dataInfo.cpnKkak2RaiToriRirekiList.filter(
      (rireki) => rireki.sc1Id === item.sc1Id
    ).length,
  }))

  // 選択期間IDの設定
  const issuesPeriodId = localOneway.or27604Oneway?.sc1Id
  const selectPeriodId =
    issuesPeriodId && krkComKikanList.some((item) => item.sc1Id === issuesPeriodId)
      ? issuesPeriodId
      : krkComKikanList[0]?.sc1Id || ''

  selectedIdPlanPeriod.value = selectPeriodId
  local.krkComKikan.items = krkComKikanList

  // バックアップデータの設定
  if (dataInfo.cpnKkak2RaiToriRirekiList) {
    local.cpnKkak2RaiToriRirekiListBak.items = dataInfo.cpnKkak2RaiToriRirekiList
  }

  if (dataInfo.cpnKkak2RaiToriList) {
    local.cpnKkak2RaiToriListBak.items = dataInfo.cpnKkak2RaiToriList.map((item) => ({
      ...item,
      checkbox: { modelValue: false } as Mo00018Type,
    }))
  }
  local.cpnKkak2RaiToriRirekiListBak.items.forEach((item) => {
    item.assType = local.typeList.find((type) => type.value === item.assType)?.label ?? ''
  })
  cpnKkak2RaiToriRirekiListSet(selectPeriodId)
}

/**
 * 計画期間行選択時の処理
 * 選択された計画期間に基づいて課題と目標取込情報を更新する
 *
 * @param item - 選択された計画期間行のデータ
 */
function planPeriodRowClick(item: KrkComKikanTable) {
  if (selectedIdPlanPeriod.value !== item.sc1Id) {
    // 計画期間行選択
    selectedIdPlanPeriod.value = item.sc1Id
    // 課題と目標取込情報設定
    cpnKkak2RaiToriRirekiListSet(item.sc1Id)
  }
}

/**
 * 課題と目標取込行選択時の処理
 * 選択された課題と目標取込に基づいて明細情報を更新する
 *
 * @param item - 選択された課題と目標取込行のデータ
 */
const cpnKkak2RaiToriRirekiRowClick = (item: CpnKkak2RaiToriRireki) => {
  if (selectCpnKkak2RaiToriRireki.value !== item.raiId) {
    // 課題と目標取込選択
    selectCpnKkak2RaiToriRireki.value = item.raiId
    // 課題と目標取込明細情報設定
    cpnKkak2RaiToriListSet(item.raiId)
  }
}
/**
 * 課題と目標取込情報設定
 * 選択された計画期間IDに基づいて課題と目標取込情報をフィルタリングして設定する
 *
 * @param selectId - 選択された計画期間ID
 */
function cpnKkak2RaiToriRirekiListSet(selectId: string) {
  if (!local.cpnKkak2RaiToriRirekiListBak.items?.length) return

  const issuesAndGoalSectionList = local.cpnKkak2RaiToriRirekiListBak.items.filter(
    (item) => item.sc1Id === selectId
  )

  const selecthistroyId = issuesAndGoalSectionList[0]?.raiId || ''
  selectCpnKkak2RaiToriRireki.value = selecthistroyId
  local.cpnKkak2RaiToriRirekiList.items = issuesAndGoalSectionList

  cpnKkak2RaiToriListSet(selecthistroyId)
}
/**
 * 課題と目標取込明細情報設定
 * 選択された課題と目標取込IDに基づいて明細情報をフィルタリングして設定する
 *
 * @param selectId - 選択された課題と目標取込ID
 */
const cpnKkak2RaiToriListSet = (selectId: string) => {
  if (!local.cpnKkak2RaiToriListBak.items?.length) {
    local.cpnKkak2RaiToriList.items = []
    mo00018OnewayHeader.value.disabled = true
    return
  }

  const ikouInfoList = local.cpnKkak2RaiToriListBak.items.filter((item) => item.raiId === selectId)

  local.cpnKkak2RaiToriList.items = ikouInfoList
  mo00018OnewayHeader.value.disabled = ikouInfoList.length < 1

  local.cpnKkak2RaiToriList.items.forEach((item) => {
    if (item.checkbox) item.checkbox.modelValue = false
  })
  mo00018.value.modelValue = false // reset check all
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row class="ma-0 pa-0">
        <c-v-col v-if="localOneway.or27604Oneway.kanriFlg === Or27604Const.DEFAULT.CHK_FLG_1">
          <!-- 計画期間選択一覧 -->
          <c-v-data-table
            style="height: 166px"
            :items="local.krkComKikan.items"
            hide-default-footer
            :headers="planPeriodDataTable.headers"
            class="table-wrapper custom-table-wrapper pr-2"
            :items-per-page="-1"
            hover
            fixed-header
          >
            <template #item="{ item }">
              <tr
                :class="{ 'highlight-row': selectedIdPlanPeriod === item.sc1Id }"
                @click="planPeriodRowClick(item)"
              >
                <td style="text-align: left">
                  <base-mo-01337 :oneway-model-value="{ value: item.planPeriod }" />
                </td>
                <td style="text-align: right">
                  <base-mo01336
                    :oneway-model-value="{ value: item.numberOfWithinThePeriodHistory }"
                  ></base-mo01336>
                </td>
              </tr>
            </template>
            <template #bottom />
          </c-v-data-table>
        </c-v-col>
        <c-v-col cols="7">
          <!-- 課題と目標取込選択一覧 -->
          <c-v-data-table
            style="height: 166px"
            :items="local.cpnKkak2RaiToriRirekiList.items"
            :headers="cpnKkak2RaiToriRirekiDataTable.headers"
            hide-default-footer
            class="table-wrapper custom-table-wrapper"
            :items-per-page="-1"
            hover
            fixed-header
          >
            <template #headers>
              <tr>
                <th
                  v-for="header in cpnKkak2RaiToriRirekiDataTable.headers"
                  :key="header.key"
                  :width="header.width"
                >
                  <span>{{ header.title }}</span>
                </th>
              </tr>
            </template>
            <template #item="{ item }">
              <tr
                :class="{
                  'highlight-row': selectCpnKkak2RaiToriRireki === item.raiId,
                }"
                @click="cpnKkak2RaiToriRirekiRowClick(item)"
              >
                <td>
                  <base-mo-01337 :oneway-model-value="{ value: item.createYmd }" />
                </td>
                <td>
                  <base-mo-01337 :oneway-model-value="{ value: item.shokuKnj }" />
                </td>
                <td>
                  <base-mo-01337 :oneway-model-value="{ value: item.assType }" />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
      <hr class="v-divider ma-2" />
      <c-v-row class="ma-0">
        <c-v-col cols="12">
          <!-- 課題と目標取込明細選択一覧 -->
          <c-v-data-table
            style="height: 166px"
            :headers="cpnKkak2RaiToriRirekiItemDataTable.headers"
            :items="local.cpnKkak2RaiToriList.items"
            class="table-wrapper"
            :hide-default-footer="true"
            fixed-header
            :items-per-page="-1"
          >
            <template #[`header.checkbox`]>
              <!-- チェックボックス -->
              <div style="width: 80px">
                <base-mo00018
                  v-model="mo00018"
                  :oneway-model-value="mo00018OnewayHeader"
                  click.stop
                ></base-mo00018>
              </div>
            </template>
            <template #item="{ item }">
              <tr
                height="32px"
                :class="{ 'highlight-row': item.checkbox.modelValue }"
                @click="onRowClick(item)"
              >
                <td width="80px">
                  <base-mo00018
                    v-model="item.checkbox"
                    :oneway-model-value="mo00018OnewayItem"
                    class="pl-1"
                    @change="toggleSelectRow"
                    @click.stop
                  ></base-mo00018>
                </td>
                <td class="custom_td">
                  <base-mo-01337 :oneway-model-value="{ value: item.issues }" />
                </td>
                <td class="custom_td">
                  <base-mo-01337 :oneway-model-value="{ value: item.choukiKnj }" />
                </td>
                <td class="custom_td">
                  <base-mo-01337 :oneway-model-value="{ value: item.tankiKnj }" />
                </td>
                <td class="custom_td">
                  <base-mo-01337 :oneway-model-value="{ value: item.careKnj }" />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <div class="buttonArea">
        <!-- 閉じる -->
        <base-mo00611
          :oneway-model-value="localOneway.footerClosebtnOneway"
          @click="onClickClose"
        />
        <!-- 確定 -->
        <base-mo00609
          :oneway-model-value="localOneway.footerConfirmbtnOneway"
          @click="onClickConfirm"
        />
      </div>
    </template>
    <!-- 確認ダイアログ -->
    <g-custom-or-x-0041
      v-model="local.orConfirmDeleteRowDialog"
      :oneway-model-value="localOneway.orCpGroupDefinitionInputFormDeleteDialogOneway"
    >
    </g-custom-or-x-0041>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';

.v-col {
  padding-left: 0px !important;
  padding-right: 0px !important;
}
:deep(.custom-table-wrapper .v-table__wrapper td) {
  padding-left: 8px !important;
}
:deep(.v-table__wrapper td.custom_td) {
  padding-left: 8px !important;
}
</style>
