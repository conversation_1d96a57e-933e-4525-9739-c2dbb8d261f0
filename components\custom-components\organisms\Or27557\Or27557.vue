<script setup lang="ts">
/**
 * Or27557:有機体:印刷設定モーダル
 * GUI00969_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import type { Or27557StateType, Or27557TwoWayData } from './Or27557.type'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenStore,
  useScreenTwoWayBind,
} from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or27557OnewayType } from '~/types/cmn/business/components/Or27557Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or27557Const } from '~/components/custom-components/organisms/Or27557/Or27557.constants'

import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  PrintSettingsScreenInitialInfoComSelectOutEntity,
  PrintSettingsScreenInitialInfoComSelectInEntity,
  choPrtList,
} from '~/repositories/cmn/entities/printSettingsScreenInitialInfoComSelect'
import type {
  ImplementationPlanHistoryInfoSelectOutEntity,
  ImplementationPlanHistoryInfoSelectInEntity,
} from '~/repositories/cmn/entities/implementationPlanHistoryInfoSelect'
import type {
  LedgerInitializeDataComSelectOutEntity,
  LedgerInitializeDataComSelectInEntity,
} from '~/repositories/cmn/entities/ledgerInitializeDataComSelect'
import type {
  ImplementationPlanHistoryInfoSubjectSelectInEntity,
  ImplementationPlanHistoryInfoSubjectSelectOutEntity,
} from '~/repositories/cmn/entities/ImplementationPlanHistoryInfoSubjectSelect'
import type { PrintSettingsInfoComUpdateInEntity } from '~/repositories/cmn/entities/printSettingsInfoComUpdate'
import type { FilterForUserIdComSelectInEntity } from '~/repositories/cmn/entities/filterForUserIdComSelect'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { Or10464OnewayType } from '~/types/cmn/business/components/Or10464Type'
import { Or10016Logic } from '~/components/custom-components/organisms/Or10016/Or10016.logic'
import { Or10016Const } from '~/components/custom-components/organisms/Or10016/Or10016.constants'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type {
  OrX0133OnewayType,
  OrX0133TableData,
} from '~/types/cmn/business/components/OrX0133Type'
import { OrX0133Const } from '~/components/custom-components/organisms/OrX0133/OrX0133.constants'
import { OrX0133Logic } from '~/components/custom-components/organisms/OrX0133/OrX0133.logic'
import type { InWebEntity } from '@/repositories/AbstructWebRepository'
import { reportOutputType, useReportUtils } from '~/utils/useReportUtils'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { ImplementationPlan3ReportEntity } from '~/repositories/cmn/entities/ImplementationPlan3ReportEntity'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { useCmnCom } from '@/utils/useCmnCom'
import type { UserEntity } from '~/repositories/cmn/entities/AssessmentInterRAIPrintSettingsSelectEntity'
import { useValidation } from '@/utils/useValidation'

const { byteLength } = useValidation()
const { t } = useI18n()

// route共有情報
const cmnRouteCom = useCmnRouteCom()
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or27557OnewayType
  onewayModelValues: Or10464OnewayType
  uniqueCpId: string
  parentCpId: string
}

const props = defineProps<Props>()

// 引継情報を取得する

const or21813 = ref({ uniqueCpId: '' })

const or21814 = ref({ uniqueCpId: '' })

const or21815 = ref({ uniqueCpId: '' })
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or10016 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0133 = ref({ uniqueCpId: OrX0133Const.CP_ID(0) })
const orx0145 = ref({ uniqueCpId: '' })

const { reportOutput } = useReportUtils()

// プロファイル
const choPro = ref('')

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [Or10016Const.CP_ID(0)]: or10016.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

// 帳票イニシャライズデータを取得する
const reportInitData = ref([
  {
    // 氏名伏字印刷
    prtName: '',
    // 文書番号印刷
    prtBng: '',
    // 個人情報印刷
    prtKojin: '',
  },
])

// ダイアログ表示フラグ
const showDialogOr10016 = computed(() => {
  // Or10016のダイアログ開閉状態
  return Or10016Logic.state.get(or10016.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

/**
 * 利用者列幅
 */
const userCols = ref<number>(4)

/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(false)

const orX0133OnewayModel = reactive<OrX0133OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or27557Const.DEFAULT.ONE,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: Or27557Const.DEFAULT.ZERO,
  tableStyle: 'width:335px',
  itemShowFlg: {},
  rirekiList: [] as OrX0133TableData[],
})

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')

// ローカル双方向bind
const local = reactive({
  /**
   * 選択利用者リスト
   */
  userList: [] as UserEntity[],
  selectUserId: '',
  textInput: {
    value: '',
  } as Mo00045Type,
  mo00039Type: '',
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  mo00020TypeKijunbi: {
    value: '',
  } as Mo00020Type,
  /** 担当ケアマネ */
  mo01408modelCare: {
    value: '',
  },
  titleInput: {
    value: '',
  } as Mo00045Type,
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintHistory: {
    modelValue: false,
  } as Mo00018Type,
  reportId: Or27557Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.KEY_1,
})

const localOneway = reactive({
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338: {
    value: t('label.valid-old-format-h21'),
  },
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: true,
    width: '132px',
  } as Mo00020OnewayType,
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  Or10464: {
    ...props.onewayModelValues,
  },
  Or27557: {
    ...props.onewayModelValue,
  },
  mo01338OneWayTitle: {
    value: t('label.title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayLeftTitle: {
    value: t('label.left-bracket'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayRightTitle: {
    value: t('label.right-bracket'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338FivWayTitle: {
    value: t('label.printer-muki'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338FouWayTitle: {
    value: t('label.printer-blank-form'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    maxLength: '50',
    rules: [byteLength(50)],
  } as Mo00045OnewayType,
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '50',
    maxLength: '2',
    rules: [byteLength(2)],
  } as Mo00045OnewayType,
  mo00018OneWayPrintHistory: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.history-info-print'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayCareManagerInCharge: {
    value: t('label.care-manager-in-charge'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo01338OneWayCareManagerInChargeLabel: {
    value: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayBaseDate: {
    value: t('label.base-date'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 印刷設定帳票出力状態リスト
   */
  orX0117Oneway: {
    reportId: '',
    type: '1',
    reportData: {} as InWebEntity,
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    width: 'auto',
    maxWidth: '1420px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  mo00611OneWayClose: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: '',
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
  mo00610OneWayCopy: {
    btnLabel: t('btn.seal-column'),
    disabled: false,
  } as Mo00610OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
})

/**
 * 担当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27557Const.DEFAULT.IS_OPEN,
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width:305px',
  focusSettingInitial: localOneway.Or27557.focusSettingInitial,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<Or27557StateType>({
  cpId: Or27557Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27557Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 画面ID
const screenId = 'GUI00969'
// ルーティング
const routing = 'GUI00969/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})
const or10016Data: Or10016OnewayType = {
  /**
   * 法人ID
   */
  houjinId: '',
  /**
   * 施設ID
   */
  shisetsuId: '',
  /**
   * 職員ID
   */
  shokuinId: '',
  /**
   * システムコード
   */
  systemCode: '',
  /**
   * 事業所ID
   */
  jigyoshoId: '',
  /**
   * ログイン番号
   */
  loginNumber: '',
  /**
   * ログインユーザタイプ
   */
  loginUserType: '',
  /**
   * 電子カルテ連携フラグ
   */
  emrLinkFlag: '',
  /**
   * 帳票セクション番号
   */
  reportSectionNumber: '3GKU0P102P0011',
  /**
   * 引続情報.アセスメント
   */
  assessment: '4',
  /**
   * 引続情報.会議禄フラグ
   */
  conferenceFlag: true,
}

/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'defPrtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})
/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 「担当ケアマネ」の監視
 */
watch(
  () => local.mo01408modelCare.value,
  async () => {
    await getFilterForUserIdComSelect()
  }
)

/**
 * 絞込用利用者IDを取得する
 */
async function getFilterForUserIdComSelect() {
  const inputData: FilterForUserIdComSelectInEntity = {
    tantoId: '',
    startYmd: '',
    endYmd: '',
  }

  // バックエンドAPIから初期情報取得
  await ScreenRepository.select('filterForUserIdComSelect', inputData)
}

/**
 * 変更データを取得する
 *
 * @returns 出力帳票印刷情報リスト
 */
async function getDataTable() {
  const choPrtList = mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => {
    if (id === mo01334TypeReport.value.value) {
      return {
        ...rest,
        prtTitle: local.titleInput.value,
        prndate: local.mo00039Type,
        param03: local.mo00018TypeChangeTitle.modelValue
          ? Or27557Const.DEFAULT.ZERO
          : Or27557Const.DEFAULT.ONE,
        param04: local.textInput.value,
        param05: local.mo00018TypePrintHistory.modelValue
          ? Or27557Const.DEFAULT.ZERO
          : Or27557Const.DEFAULT.ONE,
      }
    }
    return {
      ...rest,
    }
  }) as choPrtList[]

  refValue.value = { choPrtList: choPrtList }

  await nextTick()

  return choPrtList
}

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  if (local.titleInput.value) return
  const dialogResult = await showOr21815MsgOneBtn()
  switch (dialogResult) {
    case 'ok': {
      let label = ''
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (mo01334TypeReport.value.value === item.id && item.mo01337OnewayReport) {
            const data = item.mo01337OnewayReport as Mo01337OnewayType
            label = data.value
          }
        }
      }
      local.titleInput.value = label
      break
    }
  }
  return
}
/**
 * 確認メッセージを表示する
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21814Msg(errorMsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.confirm'),
      dialogText: errorMsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.yes'),
    },
  })
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * エラーダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21813MsgOneBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログの開閉
 *
 * @returns ダイアログの選択結果（ok）
 */
async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
const { refValue } = useScreenTwoWayBind<Or27557TwoWayData>({
  cpId: Or27557Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
onMounted(async () => {
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  orX0133OnewayModel.itemShowFlg = {
    createYmdShokuKnjFlg: true,
    caseNoFlg: true,
    kaiteiKnjFlg: true,
  }
  orX0133OnewayModel.kikanFlg = localOneway.Or27557.kikanFlg
  await getPrintSettingList()
  await initCodes()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  //日付印刷区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  //単複数選択区分
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 初期値 単一
  mo00039OneWayUserSelectType.value = Or27557Const.DEFAULT.TANI
  mo00039OneWayHistorySelectType.value = Or27557Const.DEFAULT.TANI
  local.mo00020TypeKijunbi.value = localOneway.Or27557.appYmd
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: PrintSettingsScreenInitialInfoComSelectInEntity = {
    kikanFlg: localOneway.Or27557.kikanFlg,
    sysCd: localOneway.Or27557.sysCd,
    sysRyaku: localOneway.Or27557.sysRyaku,
    kinounameKnj: Or27557Const.DEFAULT.KINOU_NAMEKNJ,
    houjinId: localOneway.Or27557.houjinId,
    shisetuId: localOneway.Or27557.shisetuId,
    svJigyoId: localOneway.Or27557.svJigyoId,
    userId: localOneway.Or27557.userId,
    shokuId: localOneway.Or27557.shokuId,
    // 担当ケアマネ設定フラグ > 0、かつ、担当者IDが0以外の場合
    // 担当者ID
    // 上記以外の場合
    // 0
    tantoId:
      localOneway.Or27557.careManagerInChargeSettingsFlag > 0 &&
      localOneway.Or27557.tantoId !== Or27557Const.DEFAULT.ZERO
        ? localOneway.Or27557.tantoId
        : Or27557Const.DEFAULT.ZERO,
    appYmd: localOneway.Or27557.appYmd,
    sectionName: localOneway.Or27557.sectionName,
    choIndex: localOneway.Or27557.choIndex,
    kojinhogoFlg: Or27557Const.DEFAULT.ZERO,
    sectionAddNo: Or27557Const.DEFAULT.ZERO,
  }

  // バックエンドAPIから初期情報取得
  const ret: PrintSettingsScreenInitialInfoComSelectOutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoComSelect',
    inputData
  )

  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of ret.data.choPrtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.defPrtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }
  mo01334OnewayReport.value.items = mo01334OnewayList

  if (ret.data.choPrtList.length > 0) mo01334TypeReport.value.value = ret.data.choPrtList[0].prtNo

  refValue.value = { choPrtList: ret.data.choPrtList }

  useScreenStore().setCpTwoWay({
    cpId: Or27557Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })

  //実施計画書履歴リスト
  if (ret.data.rirekiList.length > 0) {
    for (const item of ret.data.rirekiList) {
      if (localOneway.Or27557.kikanFlg === Or27557Const.DEFAULT.ONE) {
        const tmpItem = {
          planPeriod:
            t('label.plan-period') +
            t('label.colon-mark') +
            item.startYmd +
            t('label.wavy') +
            item.endYmd,
        } as OrX0133TableData
        orX0133OnewayModel.rirekiList.push(tmpItem)
      }
      for (const e of item.historyList) {
        const tmpItem1 = {
          planPeriod: '',
          createYmd: e.createYmd,
          kijunbiYmd: '',
          shokuKnj: e.shokuId,
          shokuinKnj: '',
          caseNo: e.caseNo,
          tougaiYm: '',
          kaisuu: '',
          kaiteiKnj: e.kaiteiFlg,
          youshikiKnj: '',
          gdlId: '',
          assType: '',
          assDateYmd: '',
        } as OrX0133TableData
        orX0133OnewayModel.rirekiList.push(tmpItem1)
      }
    }
  }

  reportInitData.value = ret.data.iniDataObject
  //担当ケアマネラベル mo01338OneWayCareManagerInChargeLabel
  if (
    ret.data.selList.length > 0 &&
    localOneway.Or27557.tantoId !== Or27557Const.DEFAULT.ZERO &&
    localOneway.Or27557.careManagerInChargeSettingsFlag > 0
  ) {
    localOneway.mo01338OneWayCareManagerInChargeLabel.value = ret.data.selList[0].shokuinKnj
  }
}

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataComSelectInEntity = {
    sysCd: localOneway.Or27557.sysCd,
    kinounameKnj: Or27557Const.DEFAULT.KINOU_NAMEKNJ,
    shokuId: localOneway.Or27557.shokuId,
    sectionKnj: choPro.value,
    kojinhogoUsedFlg: Or27557Const.DEFAULT.ZERO,
    sectionAddNo: Or27557Const.DEFAULT.ZERO,
  }
  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataComSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataComSelect',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject
}

/**
 * 帳票選択切替
 */
watch(
  () => mo01334TypeReport.value.value,
  async (newValue, oldValue) => {
    if (oldValue) {
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.帳票タイトルが空白以外の場合
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            if (local.titleInput.value) item.prtTitle = local.titleInput.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prndate = local.mo00039Type
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ03 = 画面.敬称を変更する
            item.param03 = local.mo00018TypeChangeTitle.modelValue
              ? Or27557Const.DEFAULT.ZERO
              : Or27557Const.DEFAULT.ONE
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ04 = 画面.敬称
            item.param04 = local.textInput.value
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ05 = 画面.履歴情報を印刷する
            item.param05 = local.mo00018TypePrintHistory.modelValue
              ? Or27557Const.DEFAULT.ZERO
              : Or27557Const.DEFAULT.ONE
          }
        }
      }
    }
    for (const item of mo01334OnewayReport.value.items) {
      if (item) {
        if (newValue === item.id) {
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prndate as string
          // 画面.敬称を変更する = 画面.出力帳票一覧明細に選択される行.パラメータ03
          local.mo00018TypeChangeTitle.modelValue =
            item?.param03 === Or27557Const.DEFAULT.ZERO ? true : false
          // 画面.敬称 = 画面.出力帳票一覧明細に選択される行.パラメータ04
          local.textInput.value = item?.param04 as string
          // 画面.記入用シートを印刷する = 0(チェックオフ)
          local.mo00018TypePrintTheForm.modelValue = true
          // 画面.履歴情報を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ05
          local.mo00018TypePrintHistory.modelValue =
            item?.param05 === Or27557Const.DEFAULT.ZERO ? true : false

          choPro.value = item?.choPro as string

          await getReportInfoDataList()
        }
      }
    }
  }
)

/**
 * 「利用者選択方」ラジオボタン選択 単一複数
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 利用者選択方法が「単一」の場合
    if (newValue === OrX0128Const.DEFAULT.TANI) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 305px'
      localOneway.mo00018OneWayPrintTheForm.disabled = false
      userCols.value = 6
      mo01334TypeHistoryFlag.value = true
      localOneway.orX0117Oneway.type = Or27557Const.DEFAULT.ONE
    } else {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'
      localOneway.mo00018OneWayPrintTheForm.disabled = true
      userCols.value = 11
      mo01334TypeHistoryFlag.value = false
      localOneway.orX0117Oneway.type = Or27557Const.DEFAULT.HUKUSUU
    }
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下 単一複数
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 履歴選択方法が「単一」の場合
    if (newValue === OrX0128Const.DEFAULT.TANI) {
      orX0133OnewayModel.singleFlg = OrX0133Const.DEFAULT.TANI
      localOneway.orX0117Oneway.type = Or27557Const.DEFAULT.ONE
      //画面.記入用シートを印刷するを活性表示にする
      localOneway.mo00018OneWayPrintTheForm.disabled = false
    } else {
      orX0133OnewayModel.singleFlg = OrX0133Const.DEFAULT.HUKUSUU
      localOneway.orX0117Oneway.type = Or27557Const.DEFAULT.ZERO
      //画面.記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWayPrintTheForm.disabled = true
    }
  }
)

/**
 * 履歴情報を取得する
 *
 *   @param userId - 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  orX0133OnewayModel.rirekiList = []
  const inputData: ImplementationPlanHistoryInfoSelectInEntity = {
    kikanFlg: localOneway.Or27557.kikanFlg,
    svJigyoId: localOneway.Or27557.svJigyoId,
    userId: userId,
  }
  // バックエンドAPIから初期情報取得
  const ret: ImplementationPlanHistoryInfoSelectOutEntity = await ScreenRepository.select(
    'implementationPlanHistoryInfoSelect',
    inputData
  )

  //週間表履歴リスト
  if (ret.data.rirekiList.length > 0) {
    for (const item of ret.data.rirekiList) {
      if (localOneway.Or27557.kikanFlg === Or27557Const.DEFAULT.ONE) {
        const tmpItem = {
          planPeriod:
            t('label.plan-period') +
            t('label.colon-mark') +
            item.startYmd +
            t('label.wavy') +
            item.endYmd,
        } as OrX0133TableData
        orX0133OnewayModel.rirekiList.push(tmpItem)
      }
      for (const e of item.historyList) {
        const tmpItem1 = {
          planPeriod: '',
          createYmd: e.createYmd,
          kijunbiYmd: '',
          shokuKnj: e.shokuId,
          shokuinKnj: '',
          caseNo: e.caseNo,
          tougaiYm: '',
          kaisuu: '',
          kaiteiKnj: e.kaiteiFlg,
          youshikiKnj: '',
          gdlId: '',
          assType: '',
          assDateYmd: '',
        } as OrX0133TableData
        orX0133OnewayModel.rirekiList.push(tmpItem1)
      }
    }
  }
}

/**
 * 印刷日付選択状態を追跡する
 */
watch(
  () => local.mo00039Type,
  async () => {
    await checkTitleInput()
  }
)

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 「閉じるボタン」押下
 */
async function close() {
  await checkTitleInput()
  const choPrtList = await getDataTable()
  if (isEdit.value) await savePrintSettingInfo(choPrtList)
  setState({ isOpen: false })
}

/**
 * 「印鑑欄」ボタン押下
 */
async function open() {
  const choPrtList = await getDataTable()
  or10016Data.houjinId = localOneway.Or27557.houjinId
  or10016Data.shisetsuId = localOneway.Or27557.shisetuId
  or10016Data.shokuinId = localOneway.Or27557.shokuId
  or10016Data.systemCode = localOneway.Or27557.sysCd
  or10016Data.jigyoshoId = localOneway.Or27557.svJigyoId
  or10016Data.reportSectionNumber = choPrtList[0].sectionNo
  // Or10016のダイアログ開閉状態を更新する
  Or10016Logic.state.set({
    uniqueCpId: or10016.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「印刷」ボタン押下
 */
async function print() {
  await checkTitleInput()
  if (!choPro.value) await showOr21813MsgOneBtn()
  const historyList = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList
  // 利用者選択方法が「単一」、履歴一覧明細にデータを選択しない場合
  // 処理中断
  const OrX0133Event = OrX0133Logic.event.get(orX0133.value.uniqueCpId)
  if (
    mo00039OneWayUserSelectType.value === Or27557Const.DEFAULT.TANI &&
    OrX0133Event?.orX0133DetList.length === 0
  ) {
    showOr21814Msg(t('message.i-cmn-11393'))
    return
  }
  // 利用者選択が「単一」  且つ、履歴一覧が0件選択の場合
  if (
    mo00039OneWayUserSelectType.value === Or27557Const.DEFAULT.TANI &&
    (historyList === undefined || historyList.length === 0)
  ) {
    showOr21814Msg(t('message.i-cmn-11455'))
    return
  }
  // 利用者選択方法が「複数」、親画面.利用者情報リストにデータを選択しない場合
  // 処理中断
  const OrX0130Event = OrX0130Logic.event.get(orX0130.value.uniqueCpId)
  if (
    mo00039OneWayUserSelectType.value === Or27557Const.DEFAULT.HUKUSUU &&
    OrX0130Event?.userList.length === 0
  ) {
    showOr21814Msg(t('message.i-cmn-11393'))
    return
  }
  // 履歴選択方法が「複数」
  if (Or27557Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value) {
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
    // 履歴情報リストにデータを選択する場合
    if (OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList.length) {
      // 印刷設定情報リストを作成
      const list = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList
      list?.forEach((item) => {
        void PrintSettingsSubjectSelect(item.caseNo)
        void downloadPdf()
      })
    }
  }

  //印刷設定情報保存
  const choPrtList = await getDataTable()

  // void downloadPdf()

  if (isEdit.value) await savePrintSettingInfo(choPrtList)

  if (localOneway.orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷設定情報リストを作成(利用者選択が「複数」、利用者情報リストにデータを選択する場合)
 *
 * @param val - id
 */
const PrintSettingsSubjectSelect = async (val: unknown) => {
  // 印刷設定情報リストを作成する
  const inputData: ImplementationPlanHistoryInfoSubjectSelectInEntity = {
    prtNo: '1',
    svJigyoId: localOneway.Or27557.svJigyoId,
    kijunbiYmd: local.mo00020TypeKijunbi.value ?? Or27557Const.DEFAULT.EMPTY,
    userlist: local.userList,
  } as ImplementationPlanHistoryInfoSubjectSelectInEntity
  const resp: ImplementationPlanHistoryInfoSubjectSelectOutEntity = await ScreenRepository.select(
    'implementationPlanHistoryInfoSubjectSelect',
    inputData
  )

  const list: OrX0117History[] = []
  if (resp.data) {
    for (const data of resp.data.prtHistoryList) {
      if (data) {
        // 利用者複数の場合

        if (Or27557Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 印刷設定情報リストを作成
          const reportData: ImplementationPlan3ReportEntity = {
            printSet: {
              title: local.titleInput.value,
              prnDate: local.mo00039Type,
              param03: local.mo00018TypeChangeTitle.modelValue,
              param04: local.textInput.value,
              param05: local.mo00018TypePrintHistory.modelValue,
            },
            // 画面.利用者一覧[i].利用者ID
            // 画面.履歴情報一覧[i].履歴ID
            printSubjectHistory: {
              userId: local.selectUserId ?? '',
              rirekiId: val,
            },
            dbNoSaveData: {
              selectDate: local.mo00020Type.value,
              emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
            },
            initMasterObj: {
              keishoFlg: local.mo00018TypeChangeTitle.modelValue ? '1' : '0',
              keishoKnj: local.textInput.value,
            },
            /** 承認欄保持フラグ */
            shoninFlg:
              cmnRouteCom.getInitialSettingMaster()?.shoninFlg ?? Or27557Const.DEFAULT.EMPTY,
            /** 敬称使用フラグ */
            keishoFlg:
              cmnRouteCom.getInitialSettingMaster()?.keishoFlg ?? Or27557Const.DEFAULT.EMPTY,
            /** 敬称文字列 */
            keishoKnj:
              cmnRouteCom.getInitialSettingMaster()?.keishoKnj ?? Or27557Const.DEFAULT.EMPTY,
          } as unknown as ImplementationPlan3ReportEntity
          list.push({
            reportId: local.reportId,
            outputType: reportOutputType.DOWNLOAD,
            reportData: reportData,
            userName: data.userName,
            historyDate: data.startYmd,
            result: data.result,
          } as OrX0117History)
        }
      }
    }
  }
  localOneway.orX0117Oneway.historyList = list
}

/**
 * pdf download
 */
const downloadPdf = async () => {
  const inputData: ImplementationPlan3ReportEntity = {
    printSet: {
      prtTitle: local.titleInput.value,
      sectionNo: '1',
      prtNo: '1',
      profile: '3GKU0P144P001',
      prnDate: '2',
      prnshoku: '1',
      param01: '',
      param02: 'CPNP144',
      param03: '1',
      param04: '敬',
      param05: '1',
      param06: '',
      param07: '',
      param08: '',
      param09: '',
      param10: '',
      param11: '',
      param12: '',
      param13: '',
      param14: '',
      param15: '',
      param16: '',
      param17: '',
      param18: '',
      param19: '',
      param20: '',
      param21: '',
      param22: '',
      param23: '',
      param24: '',
      param25: '',
      param26: '',
      param27: '',
      param28: '',
      param29: '',
      param30: '',
      param31: '',
      param32: '',
      param33: '',
      param34: '',
      param35: '',
      param36: '',
      param37: '',
      param38: '',
      param39: '',
      param40: '',
      param41: '',
      param42: '',
      param43: '',
      param44: '',
      param45: '',
      param46: '',
      param47: '',
      param48: '',
      param49: '',
      param50: '',
    },
    printSubjectHistory: {
      userId: localOneway.Or27557.userId,
      rirekiId: '3',
    },
    dbNoSaveData: {
      selectDate: '2025/07/12',
      emptyFlg: '0',
    },
    jigyoKnj: '特別養護　ほのぼの',
    appYmd: localOneway.Or27557.appYmd,
    initMasterObj: {
      keishoFlg: '1',
      keishoKnj: '様',
    },
    /** 承認欄保持フラグ */
    shoninFlg: cmnRouteCom.getInitialSettingMaster()?.shoninFlg ?? Or27557Const.DEFAULT.EMPTY,
    /** 敬称使用フラグ */
    keishoFlg: cmnRouteCom.getInitialSettingMaster()?.keishoFlg ?? Or27557Const.DEFAULT.EMPTY,
    /** 敬称文字列 */
    keishoKnj: cmnRouteCom.getInitialSettingMaster()?.keishoKnj ?? Or27557Const.DEFAULT.EMPTY,
  } as unknown as ImplementationPlan3ReportEntity

  // 帳票出力
  await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
}

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    orX0133OnewayModel.rirekiList = []
    if (newValue?.clickFlg) {
      if (newValue.userList.length > 0) {
        local.selectUserId = newValue.userList[0].userId
        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj +'  '+ item.name2Knj,
            } as UserEntity)
          }
        }

        // 利用者選択方法が「単一」の場合
        if (Or27557Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
          // 利用者選択
          // 利用者選択方法が「単一」の場合
          await getHistoricalInfoList(newValue.userList[0].userId)
        }
        // 利用者選択方法が「複数」の場合
        else if (Or27557Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          localOneway.orX0117Oneway.type = Or27557Const.DEFAULT.ONE
        }
      } else {
        local.userList = []
      }
    } else {
      local.userList = []
    }
  }
)

/**
 * 印刷設定情報を保存する
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
async function savePrintSettingInfo(choPrtList: choPrtList[]) {
  const inputData: PrintSettingsInfoComUpdateInEntity = {
    sysCd: localOneway.Or27557.sysCd,
    sysRyaku: localOneway.Or27557.sysRyaku,
    kinounameKnj: Or27557Const.DEFAULT.KINOU_NAMEKNJ,
    houjinId: localOneway.Or27557.houjinId,
    shisetuId: localOneway.Or27557.shisetuId,
    svJigyoId: localOneway.Or27557.svJigyoId,
    shokuId: localOneway.Or27557.shokuId,
    choPro: choPro.value,
    kojinhogoFlg: Or27557Const.DEFAULT.ZERO,
    sectionAddNo: Or27557Const.DEFAULT.ZERO,
    iniDataList: reportInitData.value,
    choPrtList: choPrtList,
    sectionName: localOneway.Or27557.sectionName,
  }
  // バックエンドAPIから初期情報取得
  await ScreenRepository.update('printSettingsInfoComUpdate', inputData)
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or27557_screen"
      >
        <c-v-col
          cols="12"
          sm="2"
          class="pa-0 pt-2 px-2 or27557_border_right"
        >
          <!-- 帳票 -->
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- （Ⅳ）週間表  -->
            <template #[`item.defPrtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 or27557_border_right content_center"
        >
          <c-v-row style="margin-left: 8px; margin-top: 8px; margin-bottom: 8px">
            <!--印鑑欄-->
            <base-mo00610
              class="mr-2"
              :oneway-model-value="localOneway.mo00610OneWayCopy"
              @click="open()"
            />
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="printerOption customCol or27557_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="or27557_row flex-center"
          >
            <c-v-col
              cols="12"
              sm="3"
              class="pa-2"
            >
              <!-- タイトル  -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <base-mo00045
                v-model="local.titleInput"
                :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                @keyup.enter="checkTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or27557_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-2"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              id="test"
              cols="12"
              sm="5"
              class="pa-2"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-show="local.mo00039Type == '2'"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
                @mousedown="checkTitleInput"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="printerOption customCol or27557_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <!-- 印刷オプションセクション -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or27557_row row-checkbox"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2 d-flex"
            >
              <!-- 敬称を変更するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypeChangeTitle"
                class="label-blue"
                :oneway-model-value="localOneway.mo00018OneWayChangeTitle"
              >
              </base-mo00018>
              <!-- 敬称左ラベル  -->
              <base-mo01338
                class="item-color"
                :oneway-model-value="localOneway.mo01338OneWayLeftTitle"
              ></base-mo01338>
              <!-- 敬称テキストボックス -->
              <base-mo00045
                v-model="local.textInput"
                :oneway-model-value="localOneway.mo00045OnewayTextInput"
                :disabled="!local.mo00018TypeChangeTitle.modelValue"
              />
              <!-- 敬称右ラベル  -->
              <base-mo01338
                class="item-color"
                :oneway-model-value="localOneway.mo01338OneWayRightTitle"
              ></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <!-- 記入用シートを印刷するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypePrintTheForm"
                class="label-blue"
                :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <!-- 履歴情報を印刷する -->
              <base-mo00018
                v-model="local.mo00018TypePrintHistory"
                class="label-blue"
                :oneway-model-value="localOneway.mo00018OneWayPrintHistory"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <!-- 印刷補足ラベル -->
              <base-mo01338
                class="item"
                :oneway-model-value="localOneway.mo01338"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
        >
          <c-v-row
            class="or27557_row"
            no-gutter
            style="align-items: center"
          >
            <c-v-col
              cols="12"
              sm="6"
              class="pa-2 col_height col-paddding"
            >
              <c-v-row
                class="or29976_row"
                style="padding-bottom: 8px; margin: 0px !important"
              >
                <!-- 利用者選択ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                  style="background-color: transparent"
                >
                </base-mo01338
              ></c-v-row>
              <c-v-row class="or27557_row">
                <!-- 利用者選択ラジオボタングループ -->
                <base-mo00039
                  v-model="mo00039OneWayUserSelectType"
                  :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                >
                </base-mo00039>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="mo00039OneWayUserSelectType === Or27557Const.DEFAULT.HUKUSUU"
              cols="12"
              sm="6"
              class="pa-2 col_height"
            >
              <c-v-row class="or27557_row">
                <!-- 基準日  -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayBaseDate"
                  style="background-color: transparent"
                >
                </base-mo01338>
              </c-v-row>
              <c-v-row class="or27557_row">
                <base-mo00020
                  v-model="local.mo00020TypeKijunbi"
                  :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                />
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="mo00039OneWayUserSelectType === Or27557Const.DEFAULT.TANI"
              cols="12"
              sm="6"
              class="pa-2 col_height col-paddding"
            >
              <c-v-row
                class="or27557_row"
                style="padding-bottom: 8px"
              >
                <!-- 履歴選択ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                  style="background-color: transparent"
                >
                </base-mo01338>
              </c-v-row>
              <c-v-row class="or27557_row">
                <!-- 履歴選択-ラジオボタングループ -->
                <base-mo00039
                  v-model="mo00039OneWayHistorySelectType"
                  :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                >
                </base-mo00039>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="localOneway.Or27557.appYmd !== ''"
              sm="12"
              class="pa-2 flex-center"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
                class="search-tack"
                style="display: flex"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or27557_row or29235-x0130"
            no-gutter
          >
            <c-v-col :cols="userCols">
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag"
              cols="12"
              sm="6"
              style="padding-left: 8px; padding-right: 0px"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0133
                v-if="orX0133OnewayModel.singleFlg"
                v-bind="orX0133"
                :oneway-model-value="orX0133OnewayModel"
              ></g-custom-or-x-0133>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          @click="print()"
        >
          <!--ツールチップ表示："PDFをダウンロードします"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.pdf-download')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  >
  </g-base-or21815>
  <!-- GUI01110_「印鑑欄設定」画面を起動する -->
  <g-custom-or-10016
    v-if="showDialogOr10016"
    v-bind="or10016"
    :oneway-model-value="or10016Data"
  />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117"
    :oneway-model-value="localOneway.orX0117Oneway"
  ></g-custom-or-x-0117>
</template>

<style scoped lang="scss">
.or27557_screen {
  margin: -8px !important;
}

.or27557_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or27557_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.customCol {
  margin-left: 0px;
  margin-right: 0px;
}

:deep(.label-blue) {
  .v-input__control {
    .v-label {
      color: #0000ff !important;
    }
  }
}
:deep(.item-color) {
  .item-label {
    color: #0000ff !important;
  }
}
:deep(.item) {
  .item-label {
    color: #a8a8a8 !important;
  }
}
.col_height {
  height: 70px;
}

.row-checkbox {
  padding-top: 8px;
  > div {
    padding-top: 0px !important;
  }
}

.or29235-x0130 {
  > .v-col {
    padding: 0 !important;
  }
}

.col-paddding {
  padding: 0 !important;
}

:deep(.search-tack) {
  margin-left: 0px;
  .ma-0 {
    margin-right: 8px !important;
  }
  .v-input__control {
    width: 200px;
  }
}
</style>
