<script setup lang="ts">
import { computed, ref,reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { OrX0113Const } from '~/components/custom-components/organisms/OrX0113/OrX0113.constants'
import { OrX0113Logic } from '~/components/custom-components/organisms/OrX0113/OrX0113.logic'
import type { OrX0113Type, OrX0113OnewayType } from '~/types/cmn/business/components/OrX0113Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**
 * GUI00001_項目選択
 *
 * @description
 * 「項目選択」画面を表示
 *
 * <AUTHOR> 劉顕康
 */

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00001'
// ルーティング
const routing = 'GUI00001/pinia'
// 画面物理名
const screenName = 'GUI00001'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const orX0113 = ref({ uniqueCpId: OrX0113Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00001' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  OrX0113Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
orX0113.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00001',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: OrX0113Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [OrX0113Const.CP_ID(1)]: orX0113.value,
})

// ダイアログ表示フラグ
const showDialogOrX0113 = computed(() => {
  // OrX0113のダイアログ開閉状態
  return OrX0113Logic.state.get(orX0113.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(OrX0113) 項目区分=30
 */
function onClickOrX0113p1() {
  // 選択区分
  orX0113Data.itemCategory = '30'

  // OrX0113のダイアログ開閉状態を更新する
  OrX0113Logic.state.set({
    uniqueCpId: orX0113.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理(OrX0113) 項目区分=40
 */
function onClickOrX0113p2() {
  // 選択区分
  orX0113Data.itemCategory = '40'

  // OrX0113のダイアログ開閉状態を更新する
  OrX0113Logic.state.set({
    uniqueCpId: orX0113.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理(OrX0113) 項目区分=50
 */
function onClickOrX0113p3() {
  // 選択区分
  orX0113Data.itemCategory = '50'

  // OrX0113のダイアログ開閉状態を更新する
  OrX0113Logic.state.set({
    uniqueCpId: orX0113.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const orX0113Type = ref<OrX0113Type>({
  // 処理フラグ
  processFlg: '0',
  // 選択項目
  selectedIdList: [],
})

const orX0113Data: OrX0113OnewayType = {
  // 処理モード
  processMode: '1',
  // 項目区分
  itemCategory: '30',
  // 選択IDリスト
  selectedIdList: ['2'],
  // 事業所IDリスト
  svJigyoIdList: ['1', '2'],
}
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 処理モード
  processMode: { value: '1' } as Mo00045Type,
  // 項目区分
  itemCategory: { value: '30' } as Mo00045Type,
  // 選択IDリスト
  selectedIdList: { value: '2' } as Mo00045Type,
  // 事業所IDリスト
  svJigyoIdList: { value: '1,2' } as Mo00045Type,
  // 戻り値
  selectData:{} as OrX0113Type,
})
function onClickOrX0113() {
  orX0113Data.processMode = local.processMode.value
  orX0113Data.itemCategory = local.itemCategory.value
  orX0113Data.selectedIdList = local.selectedIdList.value.split(',')
  orX0113Data.svJigyoIdList = local.svJigyoIdList.value.split(',')

  // OrX0113のダイアログ開閉状態を更新する
  OrX0113Logic.state.set({
    uniqueCpId: orX0113.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * GUI00001
 *
 * @param data - GUI00001
 */
const orX0113Change = (data: OrX0113Type) => {

  local.selectData = data
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOrX0113p1"
        >GUI00001_［項目選択］画面（親画面.項目区分=30）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOrX0113p2"
        >GUI00001_［項目選択］画面（親画面.項目区分=40）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOrX0113p3"
        >GUI00001_［項目選択］画面（親画面.項目区分=50）
      </v-btn>
      <g-custom-or-X0113
        v-if="showDialogOrX0113"
        v-bind="orX0113"
        v-model="orX0113Type"
        :oneway-model-value="orX0113Data"
        @update:model-value="orX0113Change"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">処理モード</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.processMode"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">項目区分</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.itemCategory"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">選択IDリスト</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.selectedIdList"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">事業所IDリスト</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.svJigyoIdList"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOrX0113"> GUI00001 疎通起動 </v-btn>
  </div>
  <div class="pt-5 w-25 pl-5">
    <div> 戻り値 </div>
    <div> モード=0 {{local.selectData}} </div>
  </div>
</template>
