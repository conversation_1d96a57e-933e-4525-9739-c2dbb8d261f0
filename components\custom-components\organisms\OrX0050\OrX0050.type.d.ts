/**
 * OrX0050:カレンダー(0,1,2,···，24)（画面コンポーネント）
 * カレンダー
 *
 * @description
 * 日程ブロック
 *
 * <AUTHOR>
 */
/** 日程ブロック */
export interface OrX0050CalendarEvent {
  /** 日程ブロックID */
  id: number
  /** タイトル */
  title: string
  /** 日程開始時間 */
  start: string
  /** 日程終了時間 */
  end: string
  /** 日程ブロック背景色 */
  bgColor: string
  /** 曜日 */
  week: number
  /** メモ */
  memo: string
  /** 日程内容エリア揃い方向 */
  align?: string
  /** フォントサイズ */
  fontSize?: string
  /** フォント色 */
  fontColor?: string
  /** 間隔日数 */
  period?: number
  /** 親ID */
  parentId?: number
  /** 事業者 */
  insuranceBussiness?: string
  /** アイコン表示フラグ */
  disableIcon?: boolean
  /**
   * 時間表示位置
   */
  timeAlign?: string
}
/** 内部日程ブロック */
export interface OrX0050InternalCalendarEvent extends OrX0050CalendarEvent {
  /** 日程ブロック長さ */
  width?: number
  /** 日程ブロック左側の浮動距離 */
  left?: number
  /** 期間開始日付 */
  periodStart?: Date
  /** 期間終了日付 */
  periodEnd?: Date
  /** デファクト日程開始時間 */
  defaultStart?: Date
  /** デファクト日程終了時間 */
  defaultEnd?: Date
  /** カスタムID */
  customId: string
  /** 表示レイヤー */
  zIndex?: number
}

/** カレンダーの設定 */
export interface OrX0050CalendarConfig {
  /** 日程リスト */
  events: OrX0050CalendarEvent[]
  /** セルの高さ */
  cellHeight: number
  /** ズーム */
  zoom: number
  /** りゅうど */
  granularity?: number
  /** 読み取り専用 */
  readonly?: boolean
}
/**
 * Internalカレンダーの設定
 */
export interface OrX0050InternalCalendarConfig extends OrX0050CalendarConfig {
  /** 日程リスト */
  events: OrX0050InternalCalendarEvent[]
  /** タイムラインの開始時間 */
  startHour: number
  /** タイムラインの終了時間 */
  endHour: number
}

/** 日をまたぐ分割に用いる日程ブロック */
export interface OrX0050SegmentedEvent extends OrX0050InternalCalendarEvent {
  /** 分割日程の開始時間 */
  segmentStart: string
  /** 分割日程の終了時間 */
  segmentEnd: string
  /** 曜日の下付き文字 */
  dayIndex: number
  /** 親ID */
  orgParentId: number
  /** 表示レイヤー */
  zIndex?: number
  /** 上に引き伸ばすかどうかの識別子 */
  isResizingTop?: boolean
  /** 下に引き伸ばすかどうかの識別子 */
  isResizingBottom?: boolean
  /** 引き伸ばし状態にあるかどうかの識別子 */
  isResizing?: boolean
}
/**
 * ダブルクリックイベントの戻りパラメータ
 */
export interface OrX0050CalendarDblClickResult {
  /** 開始時間 */
  startHour: string
  /** 終了時間 */
  endHour: string
  /** 曜日 */
  week: {
    /** 曜日(String) */
    jp: string
    /** 曜日(Number) */
    num: number
  }
}
