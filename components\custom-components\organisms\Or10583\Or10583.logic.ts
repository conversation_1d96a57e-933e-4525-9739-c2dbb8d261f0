import { Or15780Const } from '../Or15780/Or15780.constants'
import { Or15781Const } from '../Or15781/Or15781.constants'
import { Or15783Const } from '../Or15783/Or15783.constants'
import { Or15784Const } from '../Or15784/Or15784.constants'
import { Or15786Const } from '../Or15786/Or15786.constants'
import { Or15787Const } from '../Or15787/Or15787.constants'
import { Or15797Const } from '../Or15797/Or15797.constants'
import { Or15792Const } from '../Or15792/Or15792.constants'
import { Or15793Const } from '../Or15793/Or15793.constants'
import { Or15794Const } from '../Or15794/Or15794.constants'
import { Or16661Const } from '../Or16661/Or16661.constants'
import { Or16648Const } from '../Or16648/Or16648.constants'
import { Or16649Const } from '../Or16649/Or16649.constants'
import { Or15823Const } from '../Or15823/Or15823.constants'
import { Or15822Const } from '../Or15822/Or15822.constants'
import { Or15780Logic } from '../Or15780/Or15780.logic'
import { Or15781Logic } from '../Or15781/Or15781.logic'
import { Or15783Logic } from '../Or15783/Or15783.logic'
import { Or15784Logic } from '../Or15784/Or15784.logic'
import { Or15786Logic } from '../Or15786/Or15786.logic'
import { Or15787Logic } from '../Or15787/Or15787.logic'
import { Or15797Logic } from '../Or15797/Or15797.logic'
import { Or15792Logic } from '../Or15792/Or15792.logic'
import { Or15793Logic } from '../Or15793/Or15793.logic'
import { Or15794Logic } from '../Or15794/Or15794.logic'
import { Or16661Logic } from '../Or16661/Or16661.logic'
import { Or16648Logic } from '../Or16648/Or16648.logic'
import { Or16649Logic } from '../Or16649/Or16649.logic'
import { Or15823Logic } from '../Or15823/Or15823.logic'
import { Or15822Logic } from '../Or15822/Or15822.logic'
import { OrX0154Const } from '../OrX0154/OrX0154.constants'
import { OrX0154Logic } from '../OrX0154/OrX0154.logic'
import { Or10583Const } from './Or10583.constants'
import type { Or10583StateType } from './Or10583.type'
import { useInitialize, useOneWayBindAccessor } from '#imports'

/**
 * Or10583.logic:有機体:（計画書一括印刷）ダイアログ
 * GUI00936_計画書一括印刷
 * 処理ロジック
 *
 * @description
 * initialize処理を提供する
 *
 * <AUTHOR> 顧燕妮
 */
export namespace Or10583Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10583Const.CP_ID(0),
      uniqueCpId,

      childCps: [
        { cpId: Or15822Const.CP_ID(1) },

        { cpId: Or15781Const.CP_ID(1) },
        { cpId: Or15786Const.CP_ID(1) },
        { cpId: Or15780Const.CP_ID(1) },
        { cpId: Or15784Const.CP_ID(1) },
        { cpId: Or15787Const.CP_ID(1) },
        { cpId: Or15783Const.CP_ID(1) },

        { cpId: Or15781Const.CP_ID(2) },
        { cpId: Or15786Const.CP_ID(2) },
        { cpId: Or15797Const.CP_ID(1) },
        { cpId: Or15792Const.CP_ID(1) },
        { cpId: Or15793Const.CP_ID(1) },
        { cpId: Or15794Const.CP_ID(1) },
        { cpId: Or15783Const.CP_ID(2) },

        { cpId: Or15781Const.CP_ID(3) },
        { cpId: Or15786Const.CP_ID(3) },
        { cpId: Or16661Const.CP_ID(1) },
        { cpId: Or16648Const.CP_ID(1) },
        { cpId: Or15792Const.CP_ID(2) },
        { cpId: Or16649Const.CP_ID(1) },
        { cpId: Or15794Const.CP_ID(2) },
        { cpId: Or15783Const.CP_ID(3) },

        { cpId: Or15823Const.CP_ID(1) },

        { cpId: Or15781Const.CP_ID(4) },
        { cpId: Or15786Const.CP_ID(4) },
        { cpId: Or15792Const.CP_ID(3) },
        { cpId: Or15783Const.CP_ID(4) },
        { cpId: OrX0154Const.CP_ID(1) },
      ],

      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップOr04904Logic.initialize(childCpIds[Or04904Const.CP_ID(1)].uniqueCpId)
    Or15822Logic.initialize(childCpIds[Or15822Const.CP_ID(1)].uniqueCpId)
    Or15781Logic.initialize(childCpIds[Or15781Const.CP_ID(1)].uniqueCpId)
    Or15786Logic.initialize(childCpIds[Or15786Const.CP_ID(1)].uniqueCpId)
    Or15780Logic.initialize(childCpIds[Or15780Const.CP_ID(1)].uniqueCpId)
    Or15784Logic.initialize(childCpIds[Or15784Const.CP_ID(1)].uniqueCpId)
    Or15787Logic.initialize(childCpIds[Or15787Const.CP_ID(1)].uniqueCpId)
    Or15783Logic.initialize(childCpIds[Or15783Const.CP_ID(1)].uniqueCpId)

    Or15781Logic.initialize(childCpIds[Or15781Const.CP_ID(2)].uniqueCpId)
    Or15786Logic.initialize(childCpIds[Or15786Const.CP_ID(2)].uniqueCpId)
    Or15797Logic.initialize(childCpIds[Or15797Const.CP_ID(1)].uniqueCpId)
    Or15792Logic.initialize(childCpIds[Or15792Const.CP_ID(1)].uniqueCpId)
    Or15793Logic.initialize(childCpIds[Or15793Const.CP_ID(1)].uniqueCpId)
    Or15794Logic.initialize(childCpIds[Or15794Const.CP_ID(1)].uniqueCpId)
    Or15783Logic.initialize(childCpIds[Or15783Const.CP_ID(2)].uniqueCpId)

    Or15781Logic.initialize(childCpIds[Or15781Const.CP_ID(3)].uniqueCpId)
    Or15786Logic.initialize(childCpIds[Or15786Const.CP_ID(3)].uniqueCpId)
    Or16661Logic.initialize(childCpIds[Or16661Const.CP_ID(1)].uniqueCpId)
    Or16648Logic.initialize(childCpIds[Or16648Const.CP_ID(1)].uniqueCpId)
    Or15792Logic.initialize(childCpIds[Or15792Const.CP_ID(2)].uniqueCpId)
    Or16649Logic.initialize(childCpIds[Or16649Const.CP_ID(1)].uniqueCpId)
    Or15794Logic.initialize(childCpIds[Or15794Const.CP_ID(2)].uniqueCpId)
    Or15783Logic.initialize(childCpIds[Or15783Const.CP_ID(3)].uniqueCpId)

    Or15823Logic.initialize(childCpIds[Or15823Const.CP_ID(1)].uniqueCpId)

    Or15781Logic.initialize(childCpIds[Or15781Const.CP_ID(4)].uniqueCpId)
    Or15786Logic.initialize(childCpIds[Or15786Const.CP_ID(4)].uniqueCpId)
    Or15792Logic.initialize(childCpIds[Or15792Const.CP_ID(3)].uniqueCpId)
    Or15783Logic.initialize(childCpIds[Or15783Const.CP_ID(4)].uniqueCpId)
    OrX0154Logic.initialize(childCpIds[OrX0154Const.CP_ID(1)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10583StateType>(Or10583Const.CP_ID(0))
}
