import type { Mo01336OnewayType } from '~/types/business/components/Mo01336Type'
import type { Mo01274Type } from '@/types/business/components/Mo01274Type'
import type { Mo01278Type } from '@/types/business/components/Mo01278Type'
import type { Mo01282Type } from '~/types/business/components/Mo01282Type'
import type {
  DrugMasterInfo,
  DrugUnitMasterInfo,
} from '~/repositories/cmn/entities/MedicationMasterEntity'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'

/**
 * 双方向バインドModelValue
 */
export interface Or17575Type {
  /**
   * 1.［請求］の場合
   * 2.［実施記録］の場合
   * 3.［ケアマネ］の場合
   */
  viewType: '1' | '2' | '3'
  /**
   * 薬剤マスタ情報リスト
   */
  drugMasterInfoList: DrugMasterInfo[]
}
/**
 * Or17575：薬剤マスタリスト：一覧
 * 単方向バインドのインタフェース
 */
export interface Or17575OnewayType {
  /**
   * 薬剤マスタ情報リスト
   */
  drugMasterInfoList: DrugMasterInfo[]
  /**
   * ［薬剤検索・取込］画面薬剤マスタ情報リスト
   */
  importDrugMasterInfoList: DrugMasterInfo[]
  /**
   * 薬剤単位マスタ情報リスト
   */
  drugUnitMasterInfoList: DrugUnitMasterInfo[]
}

/**
 * 薬剤マスタ(展示用)
 */
export interface MedicationMasterModel {
  /**
   * ID
   */
  id: string
  /**
   * 階層
   */
  level: string
  /**
   * 親分類id
   */
  parentId: string
  /**
   * 分類コード
   */
  bunruiId: string
  /**
   * 薬剤id
   */
  drugId: Mo01336OnewayType
  /**
   * 適用フラグ
   */
  useFlg: Mo00018Type
  /**
   * 医薬品コード
   */
  drugCode: Mo01274Type
  /**
   * 施設内コード
   */
  drugIndex: Mo01274Type
  /**
   * レセ電算コード
   */
  drugRezeCd: Mo01274Type
  /**
   * 薬剤名
   */
  drugKnj: Mo01274Type
  /**
   * 薬剤カナ
   */
  drugKana: Mo01274Type
  /**
   * 購入価格
   */
  oldtanka: Mo01278Type
  /**
   * 購入単位
   */
  buyunitid: Mo01282Type
  /**
   * 入数
   */
  irisuu: Mo01278Type
  /**
   * 分量
   */
  bunryou: Mo01274Type
  /**
   * 使用単位
   */
  useunitid: Mo01282Type
  /**
   * 用法
   */
  youhouKnj: Mo01274Type
  /**
   * 用量
   */
  touyonissuu: Mo01274Type
  /**
   * 用量単位
   */
  touyonissuuUnitid: Mo01282Type
  /**
   * 算定区分
   */
  odd: Mo01282Type
  /**
   * 剤型
   */
  shape: Mo01282Type
  /**
   * 経路
   */
  keiroKbn: Mo01282Type
  /**
   * 経路活性/非活性
   */
  keiroKbnDisabled: boolean
  /**
   * 作用・効能等
   */
  memoKnj: Mo01274Type
  /**
   * 表示順
   */
  sort: Mo01278Type

  /**
   * 更新区分
   */
  updateKbn: string
}

/**
 * 薬剤単位マスタ
 */
export interface DrugUnitMasterInfoType {
  /**
   * ID
   */
  unitId: string
  /**
   * 単位コード
   */
  unitCode: string
  /**
   * 単位名称
   */
  unitnameKnj: string
}
