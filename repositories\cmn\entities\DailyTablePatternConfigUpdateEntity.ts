import type { InWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * GUI00989_
 *
 * <AUTHOR>
 */
export interface DailyTablePatternConfigUpdateInEntity extends InWebEntity {
  /** 日課表ID */
  day1Id: string
  /**
   * 保存用「日課表特記事項」情報
   */
  tokkiKnjInfo?: TokkiKnjInfo
  /**
   * 保存用「日課表詳細」リスト
   */
  patternList?: Detail[]
  /**
   * 保存用「日課表サービス例」リスト
   */
  svList?: Sv[]
}

/**
 * 日課表特記事項
 */
export interface TokkiKnjInfo {
  /** 特記事項 */
  tokkiKnj?: string
  /** 随時実施するその他のサービス. */
  sonotaKnj?: string
  /** 更新回数 */
  modifiedCnt?: string
}

/**
 * 保存用「日課表詳細」リスト
 */
export interface Detail {
  /** カウンタ */
  id?: string
  /** 区分 */
  dataKbn?: string
  /** 開始時間 */
  startTime?: string
  /** 終了時間 */
  endTime?: string
  /** 内容CD */
  naiyoCd?: string
  /** 日常処遇サービスメモ */
  memoKnj?: string
  /** 担当者 */
  tantoKnj?: string
  /** 文字サイズ */
  fontSize?: string
  /** 文字位置 */
  alignment?: string
  /** 文字カラー */
  fontColor?: string
  /** 背景カラー */
  backColor?: string
  /** 時間表示区分 */
  timeKbn?: string
  /** 更新回数 */
  modifiedCnt?: string
  /** 更新区分 */
  updateKbn: string
}

/**
 * 保存用「日課表サービス例」リスト
 */
export interface Sv {
  /** サービス例ID */
  svId?: string
  /** 日課表ID */
  day1Id?: string
  /** 区分 */
  dataKbn?: string
  /** 適用フラグ */
  tekiyoFlg?: string
  /** 更新回数 */
  modifiedCnt?: string
  /** 更新区分 */
  updateKbn?: string
}
