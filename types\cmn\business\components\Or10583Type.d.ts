import type { OfficeDataType } from '~/types/cmn/OfficeDataType'
import type { InitMedMasterObj } from '~/components/custom-components/organisms/OrX0154/OrX0154.type'

/**
 * 単方向バインドModelValue
 */
export interface Or10583OnewayType {
  /**
   * 計画書一括印刷画面入力パラメータ
   */
  carePlanPrintAllInData: CarePlanPrintAllInData

  /**
   *setInputComponentsFlg
   */
  setInputComponentsFlg: boolean
}

/**
 * 計画書一括印刷画面入力パラメータ
 */
export interface CarePlanPrintAllInData {
  /**
   *法人ID
   */
  legalPersonId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 計画書様式
   */
  carePlanStyle: string
  /**
   * 職員ID
   */
  employeeId: string
  /**
   *事業所データ
   */
  officeData: OfficeDataType
  /**
   *事業種別CD
   */
  jigyoTypeCd: string
  /**
   * 事業者名
   */
  svJigyoId: string
  /**
   * 事業所名
   */
  jigyoKnj: string
  /**
   * 事業所CD
   */
  jigyoCd: string
  /**
   * 適用事業所ID（※リスト）
   */
  svJigyoIdList: string[]
  /**
   * 担当ケアマネID
   */
  tantoShokuId: string
  /**
   * 職員ＩＤ
   */
  shokuId: string
  /**
   * 利用者ＩＤ
   */
  userId: string
  /**
   * 開始(YYYY/MM/DD)
   */
  startDate: string
  /**
   * 終了(YYYY/MM/DD)
   */
  endDate: string
  /**
   * システム年月日(YYYY/MM/DD)
   */
  sysYmd: string
  /**
   * 履歴ＩＤ
   */
  rirekiId: string
  /**
   * ユーザリスト
   */
  userList: userList[]
  /**
   * 50音（※リスト）
   */
  gojuuOnKana: string[]
  /**
   * 初期設定マスタの情報
   */
  initMasterObj: InitMedMasterObj
  /**
   * 期間管理フラグ
   */
  kikanFlg: string
  /**
   * システム年月
   */
  sysYm: string
}
