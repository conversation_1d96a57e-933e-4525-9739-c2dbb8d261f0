import type { Or35345TextAreaListType } from '~/components/custom-components/organisms/Or35345/Or35345.type'
import type {
  CarePlan2ImportInfoListType,
  ImplementationPlanImportInfoListType,
} from '~/repositories/cmn/entities/evaluationTableImplementationPlanImportSelectInEntity'
import type { EvaluationTableDuplicateSelectOutEntity } from '~/repositories/cmn/entities/evaluationTablePlanPeriodSelectEntity'

/**
 * 単方向バインドModelValue
 */
export interface TeX0013OnewayType {
  /**
   * 様式(0：任意 | 1：居宅版 | 2：施設版 | 3：高齢者住宅版)
   */
  styleFlag: '2'
  /**
   * 計画期間(1：管理する | 0：管理しない)
   */
  planPeriodFlag: '1'
  /**
   * 画面操作エリア表示フラグ
   */
  isShowActionArea?: boolean
}

/**
 * 双方向バインドModelValue
 */
export interface TeX0013Type {
  /**
   * マスタヘッダID
   */
  masterHeadId?: string
  /**
   * 期間ID
   */
  sc1Id?: string
  /**
   * 計画書権限
   */
  planBookAuth?: string
  /**
   * ケース権限
   */
  caseAuth?: string
  /**
   * 実施計画～①権限
   */
  implementationPlan1Authority?: boolean
  /**
   * 実施計画～②権限
   */
  implementationPlan2Authority?: boolean
  /**
   * 実施計画～③権限
   */
  implementationPlan3Authority?: boolean
  /**
   * 期間管理フラグ
   */
  planPeriodFlg?: string
  /**
   * 内容が空白は罫線を省略フラグ
   */
  omittedBlankBorderFlg?: string
  /**
   * 内容が同じは罫線を省略
   */
  omittedSameBorderFlg?: string
  /**
   * 計画書ボタン表示フラグ
   */
  planBookDisplayFlg?: boolean
  /**
   * ヘッダID
   */
  cmoni1Id?: string
  /**
   * 利用者ID
   */
  userId?: string
  /**
   * 法人ID
   */
  houjinId?: string
  /**
   * 施設ID
   */
  shisetuId?: string
  /**
   * 事業者ID
   */
  svJigyoId?: string
  /**
   * 年月日
   */
  createYmd?: string
  /**
   * 職員ID
   */
  shokuId?: string
  /**
   * 再アセスメントの必要
   */
  reAssessment?: string
  /**
   * 実施予定日
   */
  yoteiYmd?: string
  /**
   * 歴史更新回数
   */
  historyModifiedCnt?: string
  /**
   * 備考詳細
   */
  remarksInfoList?: Or35345TextAreaListType[]
  /**
   * 実施計画書取込情報
   */
  implementationPlanImportInfoList?: ImplementationPlanImportInfoListType[]
  /**
   * 要介護度
   */
  yokaiKbn?: string
  /**
   * 計画書（2）取込情報
   */
  carePlan2ImportInfoList?: CarePlan2ImportInfoListType[]
  /**
   * アセスメント方式
   */
  assessmentFormat?: string
  /**
   * 複写データ
   */
  copyData?: EvaluationTableDuplicateSelectOutEntity['data']['evaluationTableUpperTierInfoList']
  /**
   * 複写モード
   */
  copyMode?: string
}

/**
 * 双方向バインドEventタイプ
 */
export interface TeX0013EventType {
  /**
   * 画面再表示フラグ
   */
  isRefresh?: boolean
  /**
   * 保存フラグ
   */
  saveEvent?: boolean
  /**
   * 新規イベント発火フラグ
   */
  createNewFlg?: boolean
  /**
   * 削除イベント発火フラグ
   */
  deleteFlg?: boolean
  /**
   * 複写発火フラグ
   */
  copyEventFlg?: boolean
}
