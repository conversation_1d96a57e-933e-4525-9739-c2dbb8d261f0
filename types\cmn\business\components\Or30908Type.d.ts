/**
 * OOr30908:提供事業所（提供事業所）モーダル
 * GUI01188_提供事業所
 *
 * @description
 * OrX0042のFunction
 *
 * <AUTHOR>
 */

/**
 * Or30908StateType
 */
export interface Or30908StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 *  Or30908OnewayType
 */
export interface Or30908OnewayType {
  /**
   * システムコード
   */
  syscd: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 事業所ID
   */
  svJigyoId: string
  /**
   * システム年月
   */
  yymmYm: string
  /**
   * システム年月日
   */
  systemYmd: string
  /**
   * 職員ID
   */
  shokuinId: string
  /**
   * 職員名
   */
  shokuinName: string
  /**
   * 利用者ID
   */
  userId: string
  /** 法人ID */
  corporationId: string
  /** 施設ID */
  facilityId: string
  /**
   * 50音行番号
   */
  gojuuonRowNo: string
  /**
   * 50音母音
   */
  gojuuonKana: string[]
  /** 適用事業所IDリスト */
  applicableOfficeIdList?: string[]
  /** ロケーション */
  location:string
}

/**
 * TWOWAY
 */
export interface Or30908Type {
  /**
   * ダミー
   */
  dummy?: string
}
