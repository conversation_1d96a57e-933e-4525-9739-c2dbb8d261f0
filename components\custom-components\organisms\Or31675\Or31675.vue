<script setup lang="ts">
/**
 * Or31675
 * GUI01067_基本情報
 *
 * @description
 * 基本情報
 *
 * <AUTHOR> HOANG SY TOAN
 */
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27633Const } from '../Or27633/Or27633.constants'
import { Or08207Const } from '../Or08207/Or08207.constants'
import { useCmnCom } from '#imports'
import type { Mo01298OnewayType } from '~/types/business/components/Mo01298Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or27633Logic } from '~/components/custom-components/organisms/Or27633/Or27633.logic'
import type { Or27633OnewayType } from '~/types/cmn/business/components/Or27633Type'
import type { Mo00045OnewayType } from '@/types/business/components/Mo00045Type'
import type { ConsultationUserInfo } from '~/repositories/cmn/entities/ConsultationUserInitSelectEntity'

const { t } = useI18n()
const { isOverLimitByCharWidth } = useCmnCom()

interface Props {
  modelValue: {
    line1: {
      name: { value: string }
      relationship: { value: string }
      address: { value: string }
    }
    line2: {
      name: { value: string }
      relationship: { value: string }
      address: { value: string }
    }
    line3: {
      name: { value: string }
      relationship: { value: string }
      address: { value: string }
    }
    line4: {
      name: { value: string }
      relationship: { value: string }
      address: { value: string }
    }
  }
  isCopyFlag: boolean
}

const props = defineProps<Props>()
const emits = defineEmits(['update:modelValue'])

const mo01298Oneway = {
  anchorPoint: 's-3',
  title: t('label.emergency_contact'),
  color: 'rgb(var(--v-theme-black-100))',
} as Mo01298OnewayType

const headers = [
  { title: '', width: '10px', key: 'edit', sortable: false },
  {
    title: t('label.name-consent'),
    align: 'left',
    width: '60px',
    key: 'name-consent',
    sortable: false,
  },
  {
    title: t('label.relationship'),
    align: 'left',
    width: '60px',
    key: 'relationship',
    sortable: false,
  },
  {
    title: t('label.address_contact_info'),
    align: 'left',
    width: '200px',
    key: 'address_contact_info',
    sortable: false,
  },
]

const columnMinWidth = ref<number[]>([48, 202, 187, 643])

const localOneway = reactive({
  mo00009Oneway: {
    btnIcon: 'edit_square',
    density: 'compact',
    color: '#869fca',
  } as Mo00009OnewayType,
  SI146: {
    name: 'name',
    variant: 'solo',
    autoGrow: false,
    noResize: true,
    maxLength: Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_NAME,
    rows: '3',
    disabled: false,
  },
  SI149: {
    name: 'relationship',
    variant: 'solo',
    rows: '3',
    autoGrow: false,
    noResize: true,
    maxLength: Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_RELATIONSHIP,
    disabled: false,
  },
  SI152: {
    name: 'address',
    variant: 'solo',
    rows: '3',
    autoGrow: false,
    noResize: true,
    maxLength: Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_ADDRESS,
    disabled: false,
  },
  mo00045OnewayType: { showItemLabel: false } as Mo00045OnewayType,
})

const dataTable = reactive({
  emergencyContact: { items: [{}] },
})

const local = reactive({
  ...props.modelValue,
})

const or27633Data: Or27633OnewayType = {
  /**
   * 利用者ID
   */
  alUserid: '1',
}

const or27633 = ref({ uniqueCpId: Or27633Const.CP_ID(1) })

// ダイアログ表示フラグ
const showDialogOr27633 = computed(() => {
  // Or27633のダイアログ開閉状態
  const state = Or27633Logic.state.get(or27633.value.uniqueCpId) as { isOpen?: boolean }
  return state?.isOpen ?? false
})

watch(
  () => local,
  (newValue) => {
    emits('update:modelValue', newValue)
  },
  { deep: true }
)

watch(
  () => props.isCopyFlag,
  (newValue) => {
    if (newValue) {
      localOneway.SI146.disabled = true
      localOneway.SI149.disabled = true
      localOneway.SI152.disabled = true
    }
  },
  { immediate: true }
)

const lineEdit = ref<string>('')

function handleOpenOr27633(lineNumber: string) {
  //AC042 ~ AC045
  lineEdit.value = lineNumber
  Or27633Logic.state.set({
    uniqueCpId: or27633.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function handleConfirmOr27633(value: ConsultationUserInfo) {
  switch (lineEdit.value) {
    case '1':
      local.line1.name.value = value.nameKnj
      local.line1.relationship.value = value.zokugaraKnj ?? ''
      local.line1.address.value = value.addressKnj
      break
    case '2':
      local.line2.name.value = value.nameKnj
      local.line2.relationship.value = value.zokugaraKnj ?? ''
      local.line2.address.value = value.addressKnj
      break
    case '3':
      local.line3.name.value = value.nameKnj
      local.line3.relationship.value = value.zokugaraKnj ?? ''
      local.line3.address.value = value.addressKnj
      break
    case '4':
      local.line4.name.value = value.nameKnj
      local.line4.relationship.value = value.zokugaraKnj ?? ''
      local.line4.address.value = value.addressKnj
      break
    default:
      throw new Error(`Invalid lineEditValue: ${lineEdit.value}. Must be 1, 2, 3, or 4`)
  }

  lineEdit.value = ''
}

/**
 * 全角=2, 半角=1 の幅で安全に文字列を切り詰めるユーティリティ
 *
 * @param ch - 文字
 */
// 全角/半角の幅を判定（ASCII 0x20-0x7E 以外を全角として2）
const getCharWidth = (ch: string): number => (/[^\x20-\x7E]/.test(ch) ? 2 : 1)

// 指定の半角換算長(maxLength)以内に収まるように切り詰め
const trimByCharWidth = (text: string, maxLength: number): string => {
  let acc = 0
  let out = ''
  for (const ch of text ?? '') {
    const w = getCharWidth(ch)
    if (acc + w > maxLength) break
    out += ch
    acc += w
  }
  return out
}

// Watch all base-mo00045 input changes
watch(
  () => local.line1.name.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_NAME)) {
      local.line1.name.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_NAME
      )
    }
  }
)

watch(
  () => local.line1.relationship.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_RELATIONSHIP)) {
      local.line1.relationship.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_RELATIONSHIP
      )
    }
  }
)

watch(
  () => local.line1.address.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_ADDRESS)) {
      local.line1.address.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_ADDRESS
      )
    }
  }
)

watch(
  () => local.line2.name.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_NAME)) {
      local.line2.name.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_NAME
      )
    }
  }
)

watch(
  () => local.line2.relationship.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_RELATIONSHIP)) {
      local.line2.relationship.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_RELATIONSHIP
      )
    }
  }
)

watch(
  () => local.line2.address.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_ADDRESS)) {
      local.line2.address.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_ADDRESS
      )
    }
  }
)

watch(
  () => local.line3.name.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_NAME)) {
      local.line3.name.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_NAME
      )
    }
  }
)

watch(
  () => local.line3.relationship.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_RELATIONSHIP)) {
      local.line3.relationship.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_RELATIONSHIP
      )
    }
  }
)

watch(
  () => local.line3.address.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_ADDRESS)) {
      local.line3.address.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_ADDRESS
      )
    }
  }
)

watch(
  () => local.line4.name.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_NAME)) {
      local.line4.name.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_NAME
      )
    }
  }
)

watch(
  () => local.line4.relationship.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_RELATIONSHIP)) {
      local.line4.relationship.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_RELATIONSHIP
      )
    }
  }
)

watch(
  () => local.line4.address.value,
  (newValue) => {
    if (isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_ADDRESS)) {
      local.line4.address.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.EMERGENCY_CONTACT_ADDRESS
      )
    }
  }
)
</script>

<template>
  <base-mo01298
    :oneway-model-value="mo01298Oneway"
    class="section-title"
  />
  <c-v-sheet class="pt-6 pb-9 px-0">
    <c-v-row no-gutters>
      <c-v-col cols="12" width="100%">
        <c-v-data-table
          v-resizable-grid="{ columnWidths: columnMinWidth }"
          :headers="headers"
          hide-default-footer
          :items="dataTable.emergencyContact.items"
          class="table-header"
          fixed-header
        >
          <template #item>
            <!-- line 1 -->
            <tr class="table-row">
              <td class="text-center row-cell">
                <base-mo00009
                  v-if="!props.isCopyFlag"
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="handleOpenOr27633('1')"
                ></base-mo00009>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line1.name"
                  v-bind="localOneway.SI146"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line1.relationship"
                  v-bind="localOneway.SI149"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line1.address"
                  v-bind="localOneway.SI152"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
            </tr>

            <!-- line 2 -->
            <tr class="table-row">
              <td class="text-center row-cell">
                <base-mo00009
                  v-if="!props.isCopyFlag"
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="handleOpenOr27633('2')"
                ></base-mo00009>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line2.name"
                  v-bind="localOneway.SI146"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line2.relationship"
                  v-bind="localOneway.SI149"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line2.address"
                  v-bind="localOneway.SI152"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
            </tr>

            <!-- line 3 -->
            <tr class="table-row">
              <td class="text-center row-cell">
                <base-mo00009
                  v-if="!props.isCopyFlag"
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="handleOpenOr27633('3')"
                ></base-mo00009>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line3.name"
                  v-bind="localOneway.SI146"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line3.relationship"
                  v-bind="localOneway.SI149"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line3.address"
                  v-bind="localOneway.SI152"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
            </tr>

            <!-- line 4 -->
            <tr class="table-row">
              <td class="text-center row-cell">
                <base-mo00009
                  v-if="!props.isCopyFlag"
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="handleOpenOr27633('4')"
                ></base-mo00009>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line4.name"
                  v-bind="localOneway.SI146"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line4.relationship"
                  v-bind="localOneway.SI149"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
              <td class="row-cell">
                <base-mo00045
                  v-model="local.line4.address"
                  v-bind="localOneway.SI152"
                  :oneway-model-value="localOneway.mo00045OnewayType"
                ></base-mo00045>
              </td>
            </tr>
          </template>
        </c-v-data-table>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>

  <!-- GUI01073 -->
  <g-custom-or-27633
    v-if="showDialogOr27633"
    v-bind="or27633"
    :unique-cp-id="or27633.uniqueCpId"
    :oneway-model-value="or27633Data"
    @on-confirm="(value: ConsultationUserInfo) => handleConfirmOr27633(value)"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
$icon-color: #869fca;
:deep(.table-row td.row-cell) {
  height: 32px !important;
  padding: 0 !important;
}
:deep(.table-row td.row-cell .v-col) {
  padding: 0 !important;
}

:deep(.table-row td.row-cell .v-sheet) {
  margin: 0 !important;
}

:deep(.table-row td.row-cell) {
  border-left: 1px solid rgb(var(--v-theme-black-200));
  border-right: 1px solid rgb(var(--v-theme-black-200));
  border-collapse: separate;
}

:deep(.table-row td.row-cell:not(:first-child)) {
  border-left: none;
}

:deep(.table-row:last-child td.row-cell) {
  border-bottom: 1px solid rgb(var(--v-theme-black-200));
}
:deep(.table-row:first-child td.row-cell) {
  border-top: 1px solid rgb(var(--v-theme-black-200));
}

// Table header
:deep(.v-data-table__th) {
  border: 1px solid rgb(var(--v-theme-black-200));
  border-collapse: separate;
}
:deep(.v-data-table__th:not(:first-child)) {
  border-left: none;
}

:deep(.table-row td.row-cell .v-field) {
  box-shadow: none !important;
}
:deep(.table-row td.row-cell .v-field__overlay) {
  box-shadow: none !important;
}
:deep(.table-row td.row-cell .v-field__outline) {
  box-shadow: none !important;
}

.text-center {
  width: 4% !important;
}

:deep(.table-header thead th.v-data-table__th) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}
</style>
