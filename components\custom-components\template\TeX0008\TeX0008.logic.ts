import { Or13850Const } from '../../organisms/Or13850/Or13850.constants'
import { Or13850Logic } from '../../organisms/Or13850/Or13850.logic'
import { Or13844Const } from '../../organisms/Or13844/Or13844.constants'
import { Or13844Logic } from '../../organisms/Or13844/Or13844.logic'
import { Or13872Const } from '../../organisms/Or13872/Or13872.constants'
import { Or13872Logic } from '../../organisms/Or13872/Or13872.logic'
import { OrX0096Const } from '../../organisms/OrX0096/OrX0096.constants'
import { OrX0096Logic } from '../../organisms/OrX0096/OrX0096.logic'
import { Or03243Const } from '../../organisms/Or03243/Or03243.constants'
import { Or03243Logic } from '../../organisms/Or03243/Or03243.logic'
import { Or03240Const } from '../../organisms/Or03240/Or03240.constants'
import { Or03240Logic } from '../../organisms/Or03240/Or03240.logic'
import { OrX0001Const } from '../../organisms/OrX0001/OrX0001.constants'
import { OrX0001Logic } from '../../organisms/OrX0001/OrX0001.logic'
import { Or27562Const } from '../../organisms/Or27562/Or27562.constants'
import { Or03249Const } from '../../organisms/Or03249/Or03249.constants'
import { Or03249Logic } from '../../organisms/Or03249/Or03249.logic'
import { Or03244Const } from '../../organisms/Or03244/Or03244.constants'
import { Or03244Logic } from '../../organisms/Or03244/Or03244.logic'
import { Or51885Const } from '../../organisms/Or51885/Or51885.constants'
import { Or51885Logic } from '../../organisms/Or51885/Or51885.logic'
import { Or27216Const } from '../../organisms/Or27216/Or27216.constants'
import { Or27216Logic } from '../../organisms/Or27216/Or27216.logic'
import { Or27504Const } from '../../organisms/Or27504/Or27504.constants'
import { Or27504Logic } from '../../organisms/Or27504/Or27504.logic'
import { Or03237Const } from '../../organisms/Or03237/Or03237.constants'
import { Or03237Logic } from '../../organisms/Or03237/Or03237.logic'
import { Or03245Const } from '../../organisms/Or03245/Or03245.constants'
import { Or03245Logic } from '../../organisms/Or03245/Or03245.logic'
import { Or59423Const } from '../../organisms/Or59423/Or59423.constants'
import { Or59423Logic } from '../../organisms/Or59423/Or59423.logic'
import { Or52060Const } from '../../organisms/Or52060/Or52060.constants'
import { Or52060Logic } from '../../organisms/Or52060/Or52060.logic'
import { OrX0115Const } from '../../organisms/OrX0115/OrX0115.constants'
import { OrX0115Logic } from '../../organisms/OrX0115/OrX0115.logic'
import { Or10926Const } from '../../organisms/Or10926/Or10926.constants'
import { Or10926Logic } from '../../organisms/Or10926/Or10926.logic'
import { Or26257Const } from '../../organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '../../organisms/Or26257/Or26257.logic'
import { Or51105Const } from '../../organisms/Or51105/Or51105.constants'
import { Or51105Logic } from '../../organisms/Or51105/Or51105.logic'
import { Or03250Const } from '../../organisms/Or03250/Or03250.constants'
import { Or03250Logic } from '../../organisms/Or03250/Or03250.logic'
import { Or41345Const } from '../../organisms/Or41345/Or41345.constants'
import { Or41345Logic } from '../../organisms/Or41345/Or41345.logic'
import type { TeX0008EventType, TeX0008StateType } from './TeX0008.type'
import { TeX0008Const } from './TeX0008.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import {
  useEventStatusAccessor,
  useInitialize,
  useOneWayBindAccessor,
  useTwoWayBindAccessor,
} from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { TeX0008Type } from '~/types/cmn/business/components/TeX0008Type'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'

/**
 * TeX0008：アセスメント（包括）画面テンプレート
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace TeX0008Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: TeX0008Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or00248Const.CP_ID(0) },
        { cpId: Or11871Const.CP_ID },
        { cpId: Or13850Const.CP_ID(0) },
        { cpId: Or13850Const.CP_ID(1) },
        { cpId: Or13844Const.CP_ID(0) },
        { cpId: Or13872Const.CP_ID(0) },
        { cpId: OrX0096Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(2) },
        { cpId: Or21814Const.CP_ID(3) },
        { cpId: Or03243Const.CP_ID(0) },
        { cpId: Or03240Const.CP_ID(0) },
        { cpId: Or03250Const.CP_ID(0) },
        { cpId: Or03245Const.CP_ID(0) },
        { cpId: Or03237Const.CP_ID(0) },
        { cpId: OrX0001Const.CP_ID(1) },
        { cpId: Or27562Const.CP_ID(1) },
        { cpId: Or03249Const.CP_ID(0) },
        { cpId: Or03244Const.CP_ID(0) },
        { cpId: Or51885Const.CP_ID(1) },
        { cpId: Or27216Const.CP_ID(1) },
        { cpId: Or27504Const.CP_ID(1) },
        { cpId: Or00249Const.CP_ID(0) },
        { cpId: Or41179Const.CP_ID(1) },
        { cpId: Or59423Const.CP_ID(1) },
        { cpId: Or52060Const.CP_ID(1) },
        { cpId: OrX0115Const.CP_ID(1) },
        { cpId: Or10926Const.CP_ID(1) },
        { cpId: Or26257Const.CP_ID(1) },
        { cpId: Or51105Const.CP_ID(1) },
        // { cpId: Or41345Const.CP_ID(2) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(0)].uniqueCpId)
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    Or13850Logic.initialize(childCpIds[Or13850Const.CP_ID(0)].uniqueCpId)
    Or13850Logic.initialize(childCpIds[Or13850Const.CP_ID(1)].uniqueCpId)
    Or13844Logic.initialize(childCpIds[Or13844Const.CP_ID(0)].uniqueCpId)
    Or13872Logic.initialize(childCpIds[Or13872Const.CP_ID(0)].uniqueCpId)
    OrX0096Logic.initialize(childCpIds[OrX0096Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(2)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(3)].uniqueCpId)
    Or03243Logic.initialize(childCpIds[Or03243Const.CP_ID(0)].uniqueCpId)
    Or03240Logic.initialize(childCpIds[Or03240Const.CP_ID(0)].uniqueCpId)
    Or03250Logic.initialize(childCpIds[Or03250Const.CP_ID(0)].uniqueCpId)
    Or03245Logic.initialize(childCpIds[Or03245Const.CP_ID(0)].uniqueCpId)
    Or03237Logic.initialize(childCpIds[Or03237Const.CP_ID(0)].uniqueCpId)
    OrX0001Logic.initialize(childCpIds[OrX0001Const.CP_ID(1)].uniqueCpId)
    Or03249Logic.initialize(childCpIds[Or03249Const.CP_ID(0)].uniqueCpId)
    Or03244Logic.initialize(childCpIds[Or03244Const.CP_ID(0)].uniqueCpId)
    Or51885Logic.initialize(childCpIds[Or51885Const.CP_ID(1)].uniqueCpId)
    Or27216Logic.initialize(childCpIds[Or27216Const.CP_ID(1)].uniqueCpId)
    Or27504Logic.initialize(childCpIds[Or27504Const.CP_ID(1)].uniqueCpId)
    Or00249Logic.initialize(childCpIds[Or00249Const.CP_ID(0)].uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(1)].uniqueCpId)
    Or59423Logic.initialize(childCpIds[Or59423Const.CP_ID(1)].uniqueCpId)
    Or52060Logic.initialize(childCpIds[Or52060Const.CP_ID(1)].uniqueCpId)
    OrX0115Logic.initialize(childCpIds[OrX0115Const.CP_ID(1)].uniqueCpId)
    Or10926Logic.initialize(childCpIds[Or10926Const.CP_ID(1)].uniqueCpId)
    Or26257Logic.initialize(childCpIds[Or26257Const.CP_ID(1)].uniqueCpId)
    Or51105Logic.initialize(childCpIds[Or51105Const.CP_ID(1)].uniqueCpId)
    // Or41345Logic.initialize(childCpIds[Or41345Const.CP_ID(2)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<TeX0008Type>(TeX0008Const.CP_ID(0))

  /**
   * 自身のEventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useEventStatusAccessor<TeX0008EventType>(TeX0008Const.CP_ID(0))

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<TeX0008StateType>(TeX0008Const.CP_ID(0))
}
