import type { Mo01274Type } from '~/types/business/components/Mo01274Type'
import type { Mo01282Type } from '~/types/business/components/Mo01282Type'

/**
 *OneWayBind領域用の構造
 */
export interface Or26714StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 *イベントタイプ
 */
export interface Or26714EventType {
  /**
   * ダイアログ開閉フラグ
   */
  openFlg?: boolean
}

/**
 *週間計画の入力
 */
export interface WeeklyPlanInput {
  /**
   * データID
   */
  id: string

  /**
   *開始時間
   */
  startHour: timeType

  /**
   *終了時間
   */
  endHour: timeType
  /**
   *月曜日
   */
  dayOfWeek1: boolean
  /**
   *火曜日
   */
  dayOfWeek2: boolean
  /**
   *水曜日
   */
  dayOfWeek3: boolean
  /**
   *木曜日
   */
  dayOfWeek4: boolean
  /**
   *土曜日
   */
  dayOfWeek5: boolean
  /**
   *金曜日
   */
  dayOfWeek6: boolean
  /**
   *日曜日
   */
  dayOfWeek7: boolean
  /**
   *週単位以外(他)
   */
  dayOfWeekOther: boolean

  /** 内容CD */
  naiyoCd: string

  /**
   *内容
   */
  content: string

  /**
   *メモ
   */
  memo: Mo00045Type

  /**
   *頻度
   */
  frequency: string

  /**
   *表示設定セクション
   */
  letterSize: string

  /**
   *文字位置セクション
   */
  charPosition: string

  /**
   *省略セクション
   */
  omitted: string

  /**
   *文字色セクション
   */
  letterColor: string

  /**
   *背景色セクション
   */
  backgroundColor: string

  /**
   *時間表示セクション
   */
  timeDisplay: string

  /**
   * 週単位以外のサービス区分
   */
  igaiKbn: string

  /**
   * 週単位以外のサービス（日付指定）
   */
  igaiDate: string

  /**
   * 週単位以外のサービス（曜日指定
   */
  igaiWeek: string

  /**
   * 福祉用具貸与の単価
   */
  svTani: string

  /**
   * 福祉用具貸与マスタID
   */
  fygId: string

  /**
   * 枠外表示するかのフラグ
   */
  wakugaiFlg: string
}

/**
 *時刻種類
 */
export interface timeType {
  /**
   *値
   */
  value: string | undefined
}

/**
 *加情報
 */
export interface additionInfo {
  /**
   *名
   */
  name: Mo01274Type
  /**
   *値
   */
  value: Mo01282Type
}

/**
 * 加算情報回数リスト
 */
export interface AdditionalInfoPoints {
  /** 加算データID */
  id: string

  /** 週間表ID */
  week1Id: string

  /** 詳細ID */
  week2Id: string

  /** サービス事業者ID */
  svJigyoId: string

  /** サービス項目ID */
  svItemCd: string

  /** 回数 */
  kaisuu: string
}

/**
 *選択されたデータタイプ
 */
export interface selectedDataType {
  /**
   *週次データ
   */
  weeklyData: WeeklyPlanInput
  /**
   *サービスデータ
   */
  serviceData: {
    serviceType: ServiceType
    servicepProviderName: ServiceProvider
    serviceFeeOfficialName: ServiceFeeOfficialName
  }
  /**
   *追加データ
   */
  additionData: additionInfo
}

/**
 * 週間表入力初期情報リスト
 */
export interface WeeklyScheduleInput {
  /** 詳細ID */
  week2Id: string

  /** 週間表ID */
  week1Id: string

  /** 利用者ID */
  userid: string

  /** 曜日 */
  youbi: string

  /** 開始時間 */
  startHour: string

  /** 終了時間 */
  endHour: string

  /** 内容CD */
  naiyoCd: string

  /** 内容 */
  naiyoKnj: string

  /** メモ */
  memoKnj: string

  /** 文字サイズ */
  fontSize: string

  /** 表示モード */
  dispMode: string

  /** 文字位置 */
  alignment: string

  /** サービス種類 */
  svShuruiCd: string

  /** サービス項目（台帳） */
  svItemCd: string

  /** サービス事業者CD */
  svJigyoId: string

  /** 文字カラー */
  fontColor: string

  /** 背景カラー */
  backColor: string

  /** 時間表示区分 */
  timeKbn: string

  /** 週単位以外のサービス区分 */
  igaiKbn: string

  /** 週単位以外のサービス（日付指定） */
  igaiDate: string

  /** 週単位以外のサービス（曜日指定） */
  igaiWeek: string

  /** 福祉用具貸与の単価 */
  svTani: string

  /** 福祉用具貸与マスタID */
  fygId: string

  /** 枠外表示するかのフラグ */
  wakugaiFlg: string
}

/**
 * 週間内容情報リスト
 */
export interface WeeklyContentInfo {
  /** 内容CD */
  naiyoCd: string

  /** 区分 */
  dataKbn: string

  /** 内容 */
  naiyoKnj: string

  /** 表示順 */
  seq: string
}

/**
 * サービス種類略称リスト
 */
export interface ServiceType {
  /** 略称 */
  ruakuKnj: string
}

/**
 * サービス事業者リスト
 */
export interface ServiceProvider {
  /** サービス事業者コード */
  svJigyoCd: string

  /** 事業名 */
  jigyoKnj: string

  /** 事業名（略称） */
  jigyoRyakuKnj: string
}

/**
 * サービス費適用正式名称リスト
 */
export interface ServiceFeeOfficialName {
  /** 正式名 */
  formalnameKnj: string
}

/**
 * 加算情報回数リスト
 */
export interface AdditionalInfoPoints {
  /** 加算データID */
  id: string

  /** 週間表ID */
  week1Id: string

  /** 詳細ID */
  week2Id: string

  /** サービス事業者ID */
  svJigyoId: string

  /** サービス項目ID */
  svItemCd: string

  /** 回数 */
  kaisuu: string
}

/**
 * 加算情報サービス名称リスト
 */
export interface AdditionalServiceName {
  /** 正式名 */
  formalnameKnj: string

  /** サービス種類コード */
  svtype: string
}

/**
 * 加算サービス種類コードリスト
 */
export interface AdditionalServiceTypeCode {
  /** サービス種類コード */
  svtype: string

  /** 更新回数 */
  startdateYmd: string
}

/** 週間表入力初期情報取得 */
export interface Or26714OnewayType {
  /**
   *新規フラグ
   */
  newMode?: string

  /** 週間表ID */
  week1Id?: string

  /** 有効期間ID */
  termid?: string
  /**
   *週単位フラグ
   */
  weekDay?: string

  /**
   *開始時間
   */
  startTime?: string

  /**
   *終了時間
   */
  endTime?: string

  /** 週間表入力初期情報リスト */
  weeklyScheduleInputListSelect?: WeeklyScheduleInput[]

  /** 週間内容情報リスト */
  weeklyContentInforListSelect?: WeeklyContentInfo[]

  /** サービス種類略称リスト */
  serviceTypeListSelect?: ServiceType[]

  /** サービス事業者リスト */
  servicepProviderNameListSelect?: ServiceProvider[]

  /** サービス費適用正式名称リスト */
  serviceFeeOfficialNameListSelect?: ServiceFeeOfficialName[]

  /** 加算情報回数リスト */
  additionalInfoPointsListSelect?: AdditionalInfoPoints[]

  /** 加算情報サービス名称リスト */
  additionalInforServiceNameListSelect?: AdditionalServiceName[]

  /** 加算サービス種類コードリスト */
  additionalServiceTypeCodeListSelect?: AdditionalServiceTypeCode[]
}

/**
 * サービス種類略称リスト
 */
export interface ServiceType {
  /** 略称 */
  ruakuKnj: string
}

/**
 * サービス事業者リスト
 */
export interface ServiceProvider {
  /** サービス事業者コード */
  svJigyoCd: string

  /** 事業名 */
  jigyoKnj: string

  /** 事業名（略称） */
  jigyoRyakuKnj: string
}

/**
 * サービス費適用正式名称リスト
 */
export interface ServiceFeeOfficialName {
  /** 正式名 */
  formalnameKnj: string
}
