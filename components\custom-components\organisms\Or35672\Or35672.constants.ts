import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or35672:アセスメント複写モーダル
 * GUI00807_アセスメント複写
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or35672Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or35672', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ 開く
     */
    export const IS_OPEN = true
    /**
     * （初期値）開閉フラグ 閉じる
     */
    export const IS_CLOSE = false
    /**
     * タブID 1 アセスメントタブ
     */
    export const TAB_ID_ASSESSMENT_TAB = '1'
    /**
     * タブID 2 複数
     */
    export const TAB_ID_MULTIPLE = '2'
    /**
     * 計画期間管理フラグ 0:管理しない
     */
    export const PERIOD_MANAGEMENT_FLG_0 = '0'
    /**
     * 計画期間管理フラグ 1:管理する
     */
    export const PERIOD_MANAGEMENT_FLG_1 = '1'
    /**
     * タブマック ―:使用しない
     */
    export const TAB_MARK_NO_USE = '―'
    /**
     * タブマック ●:データあり
     */
    export const TAB_MARK_DATA_EXISTS = '●'
    /**
     * 改訂 H12
     */
    export const REVISION_H12 = 'H12'
    /**
     * 改訂 H15
     */
    export const REVISION_H15 = 'H15'
    /**
     * 改訂 H18
     */
    export const REVISION_H18 = 'H18'
    /**
     * 改訂 H21
     */
    export const REVISION_H21 = 'H21'
    /**
     * 改訂 R3
     */
    export const REVISION_R3 = 'R3'
  }
}
