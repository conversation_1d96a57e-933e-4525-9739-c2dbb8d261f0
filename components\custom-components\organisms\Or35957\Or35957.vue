<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isEmpty } from 'lodash'
import { Or35957Const } from './Or35957.constants'
import type { DayOfWeek, Or35957StateType, TableData } from './Or35957.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01344OnewayType } from '~/types/business/components/Mo01344Type'
import type { Or35957OnewayType } from '~/types/cmn/business/components/Or35957Type'
import { useScreenOneWayBind } from '#imports'
import { CustomClass } from '~/types/CustomClassType'
import type {
  DailyRateCalculationConfirmationInfoAcquisitionSelectInEntity,
  DailyRateCalculationConfirmationInfoAcquisitionSelectOutEntity,
} from '~/repositories/cmn/entities/DailyRateCalculationConfirmationInfoAcquisitionSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

/**
 * Or35957:有機体:利用票予定→実績変換モーダル
 * GUI01154_日割算定確認
 *
 * @description
 * ［日割算定確認］画面では、利用票、提供票から日割算定対象のサービス内容、提供時間、提供日などの情報が表示される。
 * ［日割算定確認］画面は、［ケアマネ］→［利用・提供票］→［利用票］画面などで［日割算定］をクリックすると表示される。
 *
 * <AUTHOR> 李晨昊
 */

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or35957OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or35957OnewayType = {
  /** 支援事業者ID */
  svJigyoId: '',
  /** 利用者ID */
  userid: '',
  /** 提供年月 */
  yymmYm: '',
  /** 作成年月日 */
  yymmD: '',
  /**被保険者名 */
  insuredName: '',
}

const localOneway = reactive({
  or35957: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  //日割算定確認
  mo00024Oneway: {
    width: '1600px',
    maxWidth: '1600px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or35957',
      class: 'use-slip-rising',
      toolbarTitle: t('label.daily-rate-calculation-confirmation'),
      toolbarName: 'Or35957ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    } as Mo01344OnewayType,
  } as Mo00024OnewayType,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or35957Const.DEFAULT.IS_OPEN,
})

// 選択した行のindex
const selectedItemIndex = ref<number>(0)

//月の最初の日
const monthFirstDay = ref(0)
//月の最後の日
const monthLastDay = ref(0)

const tableData = ref<TableData>({
  showList: [],
})
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or35957StateType>({
  cpId: Or35957Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or35957Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  //親画面.提供年月
  const offerYearMonth = new Date(localOneway.or35957.yymmYm)
  const offerYear = offerYearMonth.getFullYear()
  const offerMonth = offerYearMonth.getMonth()
  monthFirstDay.value = new Date(offerYear, offerMonth, 1).getDate()
  monthLastDay.value = new Date(offerYear, offerMonth + 1, 0).getDate()

  // 初期情報取得
  await init()
})

// 初期情報取得
async function init() {
  //日割算定確認初期情報取得入力エンティティ
  const inputData: DailyRateCalculationConfirmationInfoAcquisitionSelectInEntity = {
    //     事業所ＩＤ：親画面.支援事業者ID
    svJigyoId: localOneway.or35957.svJigyoId,
    // 利用者ＩＤ：親画面.利用者ID
    userid: localOneway.or35957.userid,
    // サービス提供年月：親画面.提供年月
    yymmYm: localOneway.or35957.yymmYm,
    // サービス提供年月（変更日）：親画面.作成年月日
    // yymmD: localOneway.or35957.yymmD,
    yymmD: '01',
  }
  //日割算定確認初期情報取得
  const rtnData: DailyRateCalculationConfirmationInfoAcquisitionSelectOutEntity =
    await ScreenRepository.select(
      'dailyRateCalculationConfirmationInfoAcquisitionSelect',
      inputData
    )

  //初期情報
  if (rtnData.data) {
    //利用票明細情報リスト
    const riyouList = rtnData.data.riyouList
    //日割算定確認リスト
    const rgList = rtnData.data.rgList

    if (riyouList && rgList) {
      for (let i = 0; i < riyouList.length; i++) {
        //利用票明細情報
        const riyouInfo = riyouList[i]
        //日割算定確認情報
        const rginfo = rtnData.data.rgList[i]
        if (riyouInfo && rginfo) {
          tableData.value.showList.push({
            /** 事業所名(略称) */
            svJigyoNameKnj: riyouInfo.svJigyoNameKnj,
            /** サービス項目名 */
            dmyFormalnameKnj: riyouInfo.dmyFormalnameKnj,
            /** サービス開始時間 */
            svStartTime: getDisplaySvTime(rginfo.svStartTime),
            /** サービス終了時間 */
            svEndTime: getDisplaySvTime(rginfo.svEndTime),
            /** 予定回数１ */
            yDay1: rginfo.yDay1,
            /** 予定回数２ */
            yDay2: rginfo.yDay2,
            /** 予定回数３ */
            yDay3: rginfo.yDay3,
            /** 予定回数４ */
            yDay4: rginfo.yDay4,
            /** 予定回数５ */
            yDay5: rginfo.yDay5,
            /** 予定回数６ */
            yDay6: rginfo.yDay6,
            /** 予定回数７ */
            yDay7: rginfo.yDay7,
            /** 予定回数８ */
            yDay8: rginfo.yDay8,
            /** 予定回数９ */
            yDay9: rginfo.yDay9,
            /** 予定回数１０ */
            yDay10: rginfo.yDay10,
            /** 予定回数１１ */
            yDay11: rginfo.yDay11,
            /** 予定回数１２ */
            yDay12: rginfo.yDay12,
            /** 予定回数１３ */
            yDay13: rginfo.yDay13,
            /** 予定回数１４ */
            yDay14: rginfo.yDay14,
            /** 予定回数１５ */
            yDay15: rginfo.yDay15,
            /** 予定回数１６ */
            yDay16: rginfo.yDay16,
            /** 予定回数１７ */
            yDay17: rginfo.yDay17,
            /** 予定回数１８ */
            yDay18: rginfo.yDay18,
            /** 予定回数１９ */
            yDay19: rginfo.yDay19,
            /** 予定回数２０ */
            yDay20: rginfo.yDay20,
            /** 予定回数２１ */
            yDay21: rginfo.yDay21,
            /** 予定回数２２ */
            yDay22: rginfo.yDay22,
            /** 予定回数２３ */
            yDay23: rginfo.yDay23,
            /** 予定回数２４ */
            yDay24: rginfo.yDay24,
            /** 予定回数２５ */
            yDay25: rginfo.yDay25,
            /** 予定回数２６ */
            yDay26: rginfo.yDay26,
            /** 予定回数２７ */
            yDay27: rginfo.yDay27,
            /** 予定回数２８ */
            yDay28: rginfo.yDay28,
            /** 予定回数２９ */
            yDay29: rginfo.yDay29,
            /** 予定回数３０ */
            yDay30: rginfo.yDay30,
            /** 予定回数３１ */
            yDay31: rginfo.yDay31,
            /** 予定合計回数 */
            yTotalKaisu: rginfo.yTotalKaisu,
            /**予定概算単位表示ラベル   */
            ytaniShow: getDisplayTani(rginfo.yTotalKaisu, rginfo.svTani),
            /** 実績回数１ */
            jDay1: rginfo.jDay1,
            /** 実績回数２ */
            jDay2: rginfo.jDay2,
            /** 実績回数３ */
            jDay3: rginfo.jDay3,
            /** 実績回数４ */
            jDay4: rginfo.jDay4,
            /** 実績回数５ */
            jDay5: rginfo.jDay5,
            /** 実績回数６ */
            jDay6: rginfo.jDay6,
            /** 実績回数７ */
            jDay7: rginfo.jDay7,
            /** 実績回数８ */
            jDay8: rginfo.jDay8,
            /** 実績回数９ */
            jDay9: rginfo.jDay9,
            /** 実績回数１０ */
            jDay10: rginfo.jDay10,
            /** 実績回数１１ */
            jDay11: rginfo.jDay11,
            /** 実績回数１２ */
            jDay12: rginfo.jDay12,
            /** 実績回数１３ */
            jDay13: rginfo.jDay13,
            /** 実績回数１４ */
            jDay14: rginfo.jDay14,
            /** 実績回数１５ */
            jDay15: rginfo.jDay15,
            /** 実績回数１６ */
            jDay16: rginfo.jDay16,
            /** 実績回数１７ */
            jDay17: rginfo.jDay17,
            /** 実績回数１８ */
            jDay18: rginfo.jDay18,
            /** 実績回数１９ */
            jDay19: rginfo.jDay19,
            /** 実績回数２０ */
            jDay20: rginfo.jDay20,
            /** 実績回数２１ */
            jDay21: rginfo.jDay21,
            /** 実績回数２２ */
            jDay22: rginfo.jDay22,
            /** 実績回数２３ */
            jDay23: rginfo.jDay23,
            /** 実績回数２４ */
            jDay24: rginfo.jDay24,
            /** 実績回数２５ */
            jDay25: rginfo.jDay25,
            /** 実績回数２６ */
            jDay26: rginfo.jDay26,
            /** 実績回数２７ */
            jDay27: rginfo.jDay27,
            /** 実績回数２８ */
            jDay28: rginfo.jDay28,
            /** 実績回数２９ */
            jDay29: rginfo.jDay29,
            /** 実績回数３０ */
            jDay30: rginfo.jDay30,
            /** 実績回数３１ */
            jDay31: rginfo.jDay31,
            /** 実績合計回数 */
            jTotalKaisu: rginfo.jTotalKaisu,
            /**実績概算単位表示ラベル */
            jTaniShow: getDisplayTani(rginfo.jTotalKaisu, rginfo.svTani),
          })
        }
      }
    }
  }
}

// 休日計算です TODO #146839
const holidaysList = () => {
  const holidays = new Set()

  // 定休日です
  const fixedDates = [
    [1, 1], // 元旦
    [2, 11], // 建国記念日
    [4, 29], // 昭和の日
    [5, 3], // 憲法記念日
    [5, 4], // みどりの日
    [5, 5], // こどもの日
    [8, 11], // 山の日
    [11, 3], // 文化の日
    [11, 23], // 勤労感謝の日
  ]

  // 祝日を追加します。
  fixedDates.forEach(([m, d]) => {
    holidays.add(`${m}-${d}`)
  })

  const targetYear = Number(new Date(localOneway.or35957.yymmYm).getFullYear())

  // n週目Xの日付を計算します
  const getNthWeekday = (m: number, n: number, weekday: number) => {
    const date = new Date(targetYear, m - 1, 1)
    let count = 0
    while (date.getMonth() === m - 1) {
      if (date.getDay() === weekday && ++count === n) {
        return date.getDate()
      }
      date.setDate(date.getDate() + 1)
    }
    return null
  }

  // 可変休日
  ;[
    [1, 2, 1], // 成人の日 1月の第2月曜日です
    [7, 3, 1], // 海の日 7月の第3月曜日です
    [9, 3, 1], // 敬老の日 9月第3月曜日です
    [10, 2, 1], // スポーツの日 10月の第2月曜日です
  ].forEach(([m, n, wd]) => {
    const d = getNthWeekday(Number(m), Number(n), Number(wd))
    if (d) holidays.add(`${m}-${d}`)
  })
  // 祝日に戻ります。
  return holidays
}

//該当する月の日数を取得
const offeryYearMonthDayList = computed(() => {
  const offeryYearMonthDays = [] as DayOfWeek[]
  const targetMonth = new Date(localOneway.or35957.yymmYm).getMonth() + 1
  const holidays = holidaysList()
  for (let i = monthFirstDay.value; i <= monthLastDay.value; i++) {
    const dayOfWeek = getDayOfWeek(localOneway.or35957.yymmYm + Or35957Const.SLASH + i)
    const isHoliday = holidays.has(`${targetMonth}-${i}`)
    offeryYearMonthDays.push({
      day: i,
      dayOfWeek: dayOfWeek,
      bgColor: '#E6E6E6',
      isHoliday: isHoliday,
    } as DayOfWeek)
  }
  return offeryYearMonthDays
})

/**
 * 指定された日付文字列から曜日を取得する
 *
 * @param dateString - 日付を表す文字列（例: "YYYY-MM-DD"形式）
 *
 * @returns 曜日の文字列表現（例: "(月)"、"(火)"など）。無効な日付の場合、空文字を返す。
 */
function getDayOfWeek(dateString: string): string {
  const date = new Date(dateString)
  const daysOfWeek = [
    t('label.day-short-sunday'),
    t('label.day-short-monday'),
    t('label.day-short-tuesday'),
    t('label.day-short-wednesday'),
    t('label.day-short-thursday'),
    t('label.day-short-friday'),
    t('label.day-short-saturday'),
  ]

  if (isNaN(date.getTime())) {
    return ''
  }

  const dayIndex = date.getDay()
  return daysOfWeek[dayIndex]
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function onSelectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * サービス時間を取得する
 *
 * @param time -サービス時間
 */
function getDisplaySvTime(time: string) {
  return time !== Or35957Const.SERVICE_TIME_99 &&
    time !== Or35957Const.SERVICE_TIME_88 &&
    time !== Or35957Const.SERVICE_TIME_77
    ? time
    : ''
}

/**
 * 概算単位を取得する
 *
 * @param totalKaisu -合計回数
 *
 * @param svTani -サービス単位数
 */
function getDisplayTani(totalKaisu: string, svTani: string) {
  if (!isEmpty(totalKaisu) && !isEmpty(svTani)) {
    return parseInt(totalKaisu) * parseInt(svTani)
  }
  return 0
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <div
        class="div-flex"
        style="padding-top: -2px; margin-top: -8px"
      >
        <!--被保険者名-->
        <div class="div-flex">
          <base-mo00615
            class="td-padding"
            style="margin-left: -8px"
            :oneway-model-value="{
              itemLabel: t('label.insured-person-name'),
              customClass: new CustomClass({
                outerClass: 'label-align',
              }),
            }"
          />
          <base-mo01338
            class="td-padding"
            style="margin-left: 6px"
            :oneway-model-value="{
              value: localOneway.or35957.insuredName,
            }"
          />
        </div>
        <!--提供年月-->
        <div class="div-flex div-padding-left-90">
          <base-mo00615
            class="td-padding"
            :oneway-model-value="{
              itemLabel: t('label.offer-ym'),
              customClass: new CustomClass({
                outerClass: 'label-align',
              }),
            }"
          />
          <base-mo01338
            class="td-padding"
            :oneway-model-value="{
              value: localOneway.or35957.yymmYm,
            }"
          />
        </div>
        <!--作成年月日-->
        <div class="div-flex div-padding-left-40">
          <base-mo00615
            class="td-padding"
            :oneway-model-value="{
              itemLabel: t('label.create-ymd'),
              customClass: new CustomClass({
                outerClass: 'label-align',
              }),
            }"
          />
          <base-mo01338
            class="td-padding"
            :oneway-model-value="{
              value: localOneway.or35957.yymmD,
            }"
          />
        </div>
      </div>
      <c-v-divider class="w-100" />
      <c-v-row no-gutters>
        <c-v-col>
          <!-- 利用票予定実績リスト一覧 -->
          <c-v-data-table
            class="table-wrapper"
            hide-default-footer
            fixed-header
            style="height: 524px; width: 1600px"
            :items-per-page="-1"
            :items="tableData.showList"
            hover
            hide-no-data
          >
            <template #headers>
              <tr>
                <!-- サービス事業者 -->
                <th
                  rowspan="2"
                  class="pl-2 pr-2"
                  style="min-width: 120px; font-weight: normal !important"
                >
                  {{ t('label.service-office-table') }}
                </th>
                <!-- サービス内容 -->
                <th
                  rowspan="2"
                  class="pl-2 pr-2 text-left"
                  style="min-width: 110px; font-weight: normal !important"
                >
                  {{ t('label.service-type-contents') }}
                </th>
                <!-- 提供時間帯 -->
                <th
                  rowspan="2"
                  class="pl-2 pr-2 text-left"
                  style="min-width: 50px; max-width: 50px; font-weight: normal !important"
                >
                  {{ t('label.offer-line-time') }}
                </th>
                <!-- 日付1~31 -->
                <th
                  v-for="item in offeryYearMonthDayList"
                  :key="item.day"
                  class="pl-2 pr-2 text-center"
                  :style="[
                    'min-width: 35px',
                    'max-width: 35px',
                    'background-color: ' + item.bgColor + '!important',
                    'font-weight: normal !important',
                  ]"
                >
                  {{ item.day }}
                </th>
                <!-- 合計 -->
                <th
                  rowspan="2"
                  class="pl-2 pr-2 text-center"
                  style="min-width: 35px;'max-width: 35px;width:35px;font-weight: normal !important"
                >
                  {{ t('label.total') }}
                </th>
                <!-- 概算単位 -->
                <th
                  rowspan="2"
                  class="pl-2 pr-2 text-center"
                  style="min-width: 55px; font-weight: normal !important"
                >
                  {{ t('label.estimate') }}<br />{{ t('label.unit') }}
                </th>
              </tr>
              <tr>
                <!-- 曜日1~31 -->
                <th
                  v-for="(item, index) in offeryYearMonthDayList"
                  :key="index"
                  class="pl-2 pr-2 text-center"
                  style="min-width: 38px"
                  :style="[
                    item.dayOfWeek === Or35957Const.DEFAULT.SUNDAY ? 'color: #FF0000;' : '',
                    item.dayOfWeek === Or35957Const.DEFAULT.SATURDAY ? 'color: #0000FF;' : '',
                    item.isHoliday ? 'color:#FF0000' : '',
                    'font-weight: normal !important',
                  ]"
                >
                  {{ item.dayOfWeek }}
                </th>
              </tr>
            </template>
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="onSelectRow(index)"
              >
                <td
                  rowspan="2"
                  class="border_l"
                >
                  <base-mo01337 :oneway-model-value="{ value: item.svJigyoNameKnj }" />
                </td>
                <td
                  rowspan="2"
                  class="border_l"
                >
                  <base-mo01337 :oneway-model-value="{ value: item.dmyFormalnameKnj }" />
                </td>
                <td class="border_td border_l">
                  <base-mo01337
                    class="td-padding"
                    :oneway-model-value="{ value: item.svStartTime }"
                  />
                </td>
                <td
                  v-for="day in offeryYearMonthDayList"
                  :key="day.day"
                  :class="[
                    'text-left',
                    day.isHoliday ? 'sunday_col' : '',
                    day.dayOfWeek === Or35957Const.DEFAULT.SATURDAY ? 'saturday_col' : '',
                    day.dayOfWeek === Or35957Const.DEFAULT.SUNDAY ? 'sunday_col' : '',
                    'border_td',
                    'border_l',
                  ]"
                >
                  <base-mo01336
                    class="td-padding"
                    :oneway-model-value="{
                      value: item[Or35957Const.Y_DAY + day.day],
                      customClass: new CustomClass({
                        outerClass: day.isHoliday
                          ? 'td-sunday-bkg'
                          : day.dayOfWeek === Or35957Const.DEFAULT.SATURDAY
                            ? 'td-saturday-bkg'
                            : day.dayOfWeek === Or35957Const.DEFAULT.SUNDAY
                              ? 'td-sunday-bkg'
                              : selectedItemIndex === index
                                ? 'select-row'
                                : '',
                        itemClass: 'right-div',
                      }),
                    }"
                  />
                </td>
                <td
                  class="border_td border_l"
                  style="text-align: right"
                >
                  {{ item.yTotalKaisu }}
                </td>
                <td
                  class="border_td border_l border_r"
                  style="text-align: right"
                >
                  {{ item.ytaniShow }}
                </td>
              </tr>
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="onSelectRow(index)"
              >
                <td class="pa-2 text-left border_l">
                  <base-mo01337
                    class="td-padding"
                    :oneway-model-value="{ value: item.svEndTime }"
                  />
                </td>
                <td
                  v-for="day in offeryYearMonthDayList"
                  :key="day.day"
                  :class="[
                    'text-left',
                    day.isHoliday ? 'sunday_col' : '',
                    day.dayOfWeek === Or35957Const.DEFAULT.SATURDAY ? 'saturday_col' : '',
                    day.dayOfWeek === Or35957Const.DEFAULT.SUNDAY ? 'sunday_col' : '',
                    'border_l',
                  ]"
                >
                  <base-mo01336
                    class="td-padding"
                    :oneway-model-value="{
                      value: item[Or35957Const.J_DAY + day.day],
                      customClass: new CustomClass({
                        outerClass: day.isHoliday
                          ? 'td-sunday-bkg'
                          : day.dayOfWeek === Or35957Const.DEFAULT.SATURDAY
                            ? 'td-saturday-bkg'
                            : day.dayOfWeek === Or35957Const.DEFAULT.SUNDAY
                              ? 'td-sunday-bkg'
                              : selectedItemIndex === index
                                ? 'select-row'
                                : '',
                        itemClass: 'right-div',
                      }),
                    }"
                  />
                </td>
                <td
                  class="border_l"
                  style="text-align: right"
                >
                  {{ item.jTotalKaisu }}
                </td>
                <td
                  class="border_l border_r"
                  style="text-align: right"
                >
                  {{ item.jTaniShow }}
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.table-wrapper .v-table__wrapper td {
  padding: 0 8px !important;
}
:deep(.v-table.v-table--fixed-header > .v-table__wrapper > table > thead > tr > th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
}

:deep(.v-table__wrapper > table > tbody > tr > td) {
  padding: 0px !important;
  height: 32px !important;
}

.div-flex {
  display: flex;
}

.div-padding-left-90 {
  padding-left: 90px;
}

.div-padding-left-40 {
  padding-left: 40px;
}

// 選択した行のCSS
:deep(.select-row) {
  background: rgb(var(--v-theme-blue-100));
}

.saturday_col {
  background: #e6f5ff !important;
}

.sunday_col {
  background: #ffe8dd !important;
}

:deep(.td-saturday-bkg) {
  background: #e6f5ff !important;
}

:deep(.td-sunday-bkg) {
  background: #ffe8dd !important;
}

:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: 0px;
  z-index: 2;
}

:deep(.label-align) {
  align-items: initial !important;
}

:deep(.right-div) {
  text-align: right;
  display: flex;
}

:deep(.td-padding .v-col) {
  padding: 0px 8px !important;
}
.table-wrapper {
  .v-table__wrapper {
    tbody {
      tr {
        :deep(.border_td) {
          border-bottom: 1px dashed #ccc !important;
        }
        :deep(.border_l) {
          border-left: 1px solid #ccc !important;
        }
        :deep(.border_r) {
          border-right: 1px solid #ccc !important;
        }
      }
    }
  }
}
</style>
