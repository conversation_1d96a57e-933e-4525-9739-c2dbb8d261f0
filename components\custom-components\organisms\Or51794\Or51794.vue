<script setup lang="ts">
/**
 * Or51794:有機体:(予定マスタ)アセスメント（包括）マスタリスト
 * GUI00831_予定マスタ
 *
 * @description
 * 予定マスタ一覧
 *
 * <AUTHOR> PHAM HO HAI DANG
 */
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import { Or51794Logic } from '../Or51794/Or51794.logic'
import type { TableData } from './Or51794.type'
import { Or51794Const } from './Or51794.constants'

import type {
  Or51794OnewayType,
  Or51794Type,
  AssistUpdateInList,
} from '~/types/cmn/business/components/Or51794Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useScreenTwoWayBind } from '~/composables/useComponentVue'
import { useValidation } from '~/utils/useValidation'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
// import { useScreenUtils } from '~/utils/useScreenUtils'

// const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
const validation = useValidation()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or51794OnewayType
  modelValue: Or51794Type
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

const defaultOnewayModelValue: Or51794OnewayType = {
  stringInputAssistList: [],
}

const defaultModelValue: Or51794Type = {
  editFlg: false,
  delBtnDisabled: false,
  stringInputAssistList: [],
  saveResultYoTeiList: [],
}

const localOneWay = reactive({
  or51794: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo01278Oneway: {
    maxLength: '4',
    min: 1000,
    max: 9999,
    isEditCamma: false,
    rules: [
      validation.integer,
      validation.required,
      validation.minValue(1000),
      validation.maxValue(9999),
    ],
  },
  mo01274Oneway: {
    maxLength: '30',
    rules: [validation.required],
  },
})

const local = reactive({
  or51794: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<TableData[]>({
  cpId: Or51794Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []
/**************************************************
 * 変数定義
 **************************************************/
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// "*"
const required: string = Or51794Const.DEFAULT.REQUIRED
// 説明-区分番号
const descriptionCategoryNumber: string = t('label.category-number-input')
// 説明-全共通
const descriptionAllCommon: string = t('label.all-common')

// 元のテーブルデータ
const orgTableData = ref<string>('')
const tableForm = ref<VForm>()
// テーブルデータ
const tableDataFilter = computed(() => {
  const titleList = refValue.value
  return titleList!.filter((i: TableData) => i.updateKbn !== UPDATE_KBN.DELETE)
})
const refs = ref<Record<string, HTMLInputElement>>({})
const setRef = (el: HTMLInputElement | null, tableIndex: string) => {
  if (el) {
    refs.value[tableIndex] = el
  }
}
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)
// テーブルヘッダ
const headers = [
  {
    title: t('label.category-number'),
    key: 'kbnCd',
    width: '105px',
    sortable: true,
    required: true,
  },
  { title: t('label.content'), key: 'textKnj', width: '600px', sortable: false, required: true },
]

const columnMinWidth = ref<number[]>([105, 600])
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  init()
})

/**
 * 予定マスタ情報取得
 *
 */
function init() {
  // if (!local.or51794?.stringInputAssistList?.length) return
  // 戻り値はテーブルデータとして処理されます
  let tmpArr = []
  tmpArr = []
  for (const item of local.or51794.stringInputAssistList) {
    tmpArr.push({
      cf1Id: item.cf1Id,
      cf1Kbn: item.cf1Kbn,
      kbnCd: { value: item.kbnCd },
      textKnj: { value: item.textKnj },
      changeF: UPDATE_KBN.NONE,
      kbnFlg: item.kbnFlg,
      updateKbn: UPDATE_KBN.NONE,
      tableIndex: tmpArr.length,
    })
  }
  // 元のテーブルデータの設定
  if (Or51794Logic?.data?.set) {
    Or51794Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: tmpArr,
      isInit: true,
    })
  }

  // setChildCpBinds(props.parentUniqueCpId, {
  //   Or51794: {
  //     twoWayValue: tmpArr,
  //   },
  // })
  if (tmpArr.length > 0) {
    selectedItemIndex.value = 0
    // 行削除活性
    local.or51794.delBtnDisabled = true
    emit('update:modelValue', local.or51794)
  }
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
  // 行削除活性
  local.or51794.delBtnDisabled = true
  emit('update:modelValue', local.or51794)
}

/**
 * 「新規」押下
 */
async function createRow() {
  // 予定マスタのタイトル一覧の最終に新しい行を追加する。
  const data = {
    // 入力ID
    cf1Id: '0',
    // 入力区分
    cf1Kbn: '6',
    // 区分番号：空白
    kbnCd: { value: '' },
    // 内容：空白
    textKnj: { value: '' },
    // 更新回数
    modifiedCnt: '0',
    // テーブルINDEX(行固有ID)
    tableIndex: refValue.value!.length,
    // 変更フラグ
    changeF: '0',
    // 区分フラグ
    kbnFlg: '1',
    // 更新区分
    updateKbn: UPDATE_KBN.CREATE,
  }
  refValue.value!.push(data)
  selectRow(data.tableIndex)
  await nextTick()
  const id = 'input[id="input-' + data.tableIndex + '"]'
  const input = document?.querySelector(id) as HTMLElement | undefined
  input?.focus()
}

/**
 * 行削除ボタン押下
 */
function deleteRow() {
  if (selectedItemIndex.value !== -1) {
    // メッセージを表示
    // 確認ダイアログのpiniaを設定
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.confirm-dialog-title-info'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11397'),
        // 第1ボタンタイプ
        firstBtnType: 'normal1',
        // 第1ボタンラベル
        firstBtnLabel: t('btn.yes'),
        // 第2ボタンタイプ
        secondBtnType: 'destroy1',
        // 第2ボタンラベル
        secondBtnLabel: t('btn.no'),
        // 第3ボタンタイプ
        thirdBtnType: 'blank',
        iconColor: 'rgb(var(--v-theme-blue-700))',
        iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
      },
    })
    // 確認ダイアログ表示
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * 行複写ボタン押下
 */
async function copyRow() {
  if (selectedItemIndex.value !== -1) {
    let kbnCd = ''
    let textKnj = ''
    tableDataFilter.value.forEach((item: TableData) => {
      if (item.tableIndex === selectedItemIndex.value) {
        kbnCd = item.kbnCd.value
        textKnj = item.textKnj.value
      }
    })
    // 予定マスタのタイトル一覧の最終に新しい行を追加する。

    const data = {
      // 入力ID
      cf1Id: '0',
      // 入力区分
      cf1Kbn: '6',
      // 区分番号：空白
      kbnCd: { value: kbnCd },
      // 内容
      textKnj: { value: textKnj },
      // 更新回数
      modifiedCnt: '0',
      // テーブルINDEX(行固有ID)
      tableIndex: refValue.value!.length,
      // 変更フラグ
      changeF: '0',
      // 区分フラグ
      kbnFlg: '1',
      // 更新区分
      updateKbn: UPDATE_KBN.CREATE,
    }
    const newRowPosition = selectedItemIndex.value + 1
    refValue.value!.splice(newRowPosition, 0, data)
    let index = 0
    refValue.value = refValue.value!.map((item) => {
      const newItem = { ...item } // 他のプロパティは変更せずにtableIndexだけ更新
      if (item.updateKbn !== UPDATE_KBN.DELETE) {
        // 非删除行処理
        newItem.tableIndex = index
        index++ // 削除されていない行のみに連番を付与する
      } else {
        // 行削除処理
        newItem.tableIndex = -2
      }
      return newItem
    })
    selectRow(newRowPosition)
    await nextTick()
    const id = 'input[id="input-' + newRowPosition + '"]'
    const input = document?.querySelector(id) as HTMLElement | undefined
    input?.focus()
  }
}

/**
 * 検証
 */
async function tableValidation() {
  return (await tableForm.value!.validate()).valid
}

/**
 * コンテンツの更新
 */
function onUpdate() {
  refValue.value!.forEach((item: TableData) => {
    if (item.tableIndex === selectedItemIndex.value && item.updateKbn !== UPDATE_KBN.CREATE) {
      item.changeF = '1'
      item.updateKbn = UPDATE_KBN.UPDATE
    }
  })
}

watch(
  () => refValue.value,
  () => {
    local.or51794.editFlg = orgTableData.value !== JSON.stringify(refValue.value)
    // 予定マスタのリスト
    const stringInputAssistList: AssistUpdateInList[] = []
    for (const item of refValue.value!) {
      stringInputAssistList.push({
        ...item,
        kbnCd: item.kbnCd.value,
        textKnj: item.textKnj.value,
        kbnFlg: item.kbnFlg,
      })
    }
    local.or51794.stringInputAssistList = stringInputAssistList
    emit('update:modelValue', local.or51794)
  },
  { deep: true }
)
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      if (selectedItemIndex.value !== null) {
        refValue.value!.forEach((item: TableData) => {
          if (item.tableIndex === selectedItemIndex.value) {
            item.changeF = '1'
            item.updateKbn = UPDATE_KBN.DELETE
            selectedItemIndex.value = -1
            // 行削除非活性
            local.or51794.delBtnDisabled = false
            emit('update:modelValue', local.or51794)
          }
        })
      }
    } else {
      return
    }
  }
)

/**
 * 表データの設定
 */
watch(
  () => local.or51794.saveResultYoTeiList,
  () => {
    if (Array.isArray(local.or51794.saveResultYoTeiList)) {
      const stringInputAssistList = []
      for (const item of local.or51794.saveResultYoTeiList) {
        stringInputAssistList.push({
          kbnCd: { value: item.kbnCd },
          textKnj: { value: item.textKnj },
          cf1Id: item.cf1Id,
          cf1Kbn: item.cf1Kbn,
          // テーブルINDEX(行固有ID)
          tableIndex: stringInputAssistList.length,
          // 変更フラグ
          changeF: UPDATE_KBN.NONE,
          // 更新区分
          updateKbn: UPDATE_KBN.NONE,
          kbnFlg: item.kbnFlg,
        })
      }
      refValue.value = stringInputAssistList
      // 元のテーブルデータの設定
      orgTableData.value = JSON.stringify(refValue.value)
      if (stringInputAssistList.length > 0) {
        selectedItemIndex.value = 0
        // 行削除活性
        local.or51794.delBtnDisabled = true
        emit('update:modelValue', local.or51794)
      }
    }
  }
)

defineExpose({
  tableValidation,
  createRow,
  copyRow,
  deleteRow,
  init,
})
</script>

<template>
  <div class="title-container">
    <!-- 予定マスタ一覧 -->
    <c-v-form ref="tableForm">
      <c-v-data-table
        v-resizable-grid="{ columnWidths: columnMinWidth }"
        fixed-header
        :headers="headers"
        :items="tableDataFilter"
        class="table-wrapper table-header mt-2"
        height="400px"
        hide-default-footer
        :items-per-page="-1"
      >
        <!-- ヘッダ Part -->
        <template #headers>
          <tr>
            <!-- *区分番号 -->
            <th
              class="number-width"
              style="width: 105px"
            >
              <span style="color: red">{{ required }}</span
              >{{ t('label.category-number') }}
            </th>
            <!-- *内容 -->
            <th
              class="content-width"
              style="width: 600px"
            >
              <span style="color: red">{{ required }}</span
              >{{ t('label.content') }}
            </th>
          </tr>
        </template>

        <!-- 一覧 -->
        <template #item="{ item, index }">
          <tr
            :class="{ 'select-row': selectedItemIndex === item.tableIndex }"
            @click="selectRow(item.tableIndex)"
          >
            <!-- 区分番号 -->
            <td class="text-align-right text-padding">
              <!-- 分子：表用数値専用テキストフィールド -->
              <base-mo01278
                :id="`input-${item.tableIndex}`"
                :ref="(el: HTMLInputElement | null) => setRef(el, `input-${item.tableIndex}`)"
                v-model="tableDataFilter[index].kbnCd"
                :oneway-model-value="localOneWay.mo01278Oneway"
                style="min-width: 100px; width: 100%; height: 32px !important"
                @change="onUpdate"
              />
            </td>
            <!-- 内容 -->
            <td class="text-padding">
              <!-- 分子：表用テキストフィールド -->
              <base-mo01274
                v-model="tableDataFilter[index].textKnj"
                style="height: 32px !important"
                :one-model-value="localOneWay.mo01274Oneway"
                :maxlength="30"
                class="content"
                @change="onUpdate"
              />
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-form>
  </div>
  <!-- 説明:※区分番号は1000以上の値を入力してください。  -->
  <div class="body-text-category font-weight-bold font-size-text">
    {{ descriptionCategoryNumber }}
  </div>
  <!-- 説明:※全共通  -->
  <div class="font-size-text">
    {{ descriptionAllCommon }}
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
:deep(.v-col) {
  padding: 0px !important;
}

// 右寄せのCSS
.text-align-right {
  text-align: right;
}

.text-padding {
  padding: 0px !important;
}

// 選択した行のCSS
.select-row {
  background: #dbeefe;
}

:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: -0.5px;
  z-index: 2;
}

//区分番号
.body-text-category {
  margin-top: 50px;
  margin-left: 20px;
}

.font-size-text {
  font-size: 12px;
}

:deep(.full-width-field) {
  height: 50px !important;
}

:deep(input:focus) {
  border: 1px solid rgb(var(--v-theme-blue-500));
}

.content:focus {
  border: 1px solid rgb(var(--v-theme-blue-500));
}
</style>
