<script setup lang="ts">
/**
 * Or27504:有機体:具体的内容・対応するケア項目マスタ
 * GUI00628_具体的内容・対応するケア項目マスタ
 *
 * @description
 * 具体的内容・対応するケア項目マスタ
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed, nextTick } from 'vue'
import { Or10347Const } from '../Or10347/Or10347.constants'
import { Or27504Const } from './Or27504.constants'
import type {
  Or27504StateType,
  ConcreteContentsInfoType,
  CorrespondenceCareItemInfoType,
  Mo1354ConcreteContentsInfoType,
  Mo1354CorrespondenceCareItemInfoType,
  Or27504TabDataType,
} from './Or27504.type'
import type { Or27504OnewayType } from '~/types/cmn/business/components/Or27504Type'
import { definePageMeta } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type {
  IConcreteContentsCorrespondenceCareItemInEntity,
  IConcreteContentsCorrespondenceCareItemOutEntity,
} from '~/repositories/cmn/entities/ConcreteContentsCorrespondenceCareItemEntity'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo01280OnewayType, Mo01280Type } from '~/types/business/components/Mo01280Type'
import type {
  ConcreteContentsCorrespondenceCareUpdateInEntity,
  ConcreteContentsCorrespondenceCareUpdateOutEntity,
  IConcreteContentsInfoListStateType,
  ICorrespondenceCareInfoListStateType,
} from '~/repositories/cmn/entities/ConcreteContentsCorrespondenceCareInsertInEntity'
import { useScreenOneWayBind, useSetupChildProps } from '#build/imports'
import type { Mo01332OnewayType, Mo01332Type } from '~/types/business/components/Mo01332Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or10347OnewayType } from '~/types/cmn/business/components/Or10347Type'
import { Or10347Logic } from '~/components/custom-components/organisms/Or10347/Or10347.logic'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { UPDATE_KBN } from '~/constants/classification-constants'

definePageMeta({
  layout: 'business-platform-layout',
})

const { t } = useI18n()
const systemCommonsStore = useSystemCommonsStore()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or27504OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27504StateType>({
  cpId: Or27504Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27504Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * 変数定義
 **************************************************/

const defaultOnewayModelValue: Or27504OnewayType = {
  /** 問題点CD */
  b4Cd: '1',
  /** 事業所ID */
  svJigyoId: '1',
  /** 機能名 */
  asKinouName: '1',
}
const localOneway = reactive({
  or27504Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  /** 「具体的内容・対応するケア項目マスタ」ダイアログ */
  mo00024Oneway: {
    width: '1000px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      toolbarTitle: t('label.concrete-contents-correspondence-care-item'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      cardTextClass: 'pa-0',
    },
  },
  /** 確認ボタン */
  mo00609Oneway: {
    btnLabel: t('btn.ok'),
  } as Mo00609OnewayType,
  /** 具体的内容 */
  headerOnewayContents: {
    rowHeight: '32',
    height: '573',
    headers: [
      {
        key: 'ci1Knj',
        required: true,
        /** 具体的内容 */
        title: t('label.concrete-contents'),
        sortable: false,
      },
    ] as Mo01354Headers[],
  } as Mo01354OnewayType,

  /** 対応するケア項目 */
  headerOnewayItems: {
    rowHeight: '32',
    height: '573',
    headers: [
      {
        key: 'check',
        required: false,
        /** 選択 */
        title: t('label.selected'),
        sortable: false,
        width: '80',
      },
      {
        key: 'ci2Knj',
        required: true,
        /** 対応するケア項目 */
        title: t('label.corresponding-care'),
        sortable: false,
      },
    ] as Mo01354Headers[],
  } as Mo01354OnewayType,
})

const local = reactive({
  /** 選択具体的内容 */
  concreteContentsInfoSelectItem: {} as Mo1354ConcreteContentsInfoType,
  /** 具体的内容All */
  contentsInfoAll: {
    items: [] as Mo1354ConcreteContentsInfoType[],
  },
  /** 対応するケアAll */
  correspondenceCareItemInfoAll: {
    items: [] as Mo1354CorrespondenceCareItemInfoType[],
  },
  /** 具体的内容AllBAK */
  contentsInfoAllBAK: {
    items: [] as Mo1354ConcreteContentsInfoType[],
  },
  /** 対応するケアAllBAK */
  correspondenceCareItemInfoAllBAK: {
    items: [] as Mo1354CorrespondenceCareItemInfoType[],
  },
  /** 利用フラグ */
  useFlg: '',
})

/** 具体的内容テーブルの使用 */
const mo01354TypeContents = ref<Mo01354Type>({
  values: {
    selectedRowId: '',
    selectedRowIds: [],
    items: [] as Mo1354ConcreteContentsInfoType[],
  },
})

/** 対応するケアテーブルの使用 */
const mo01354TypeItems = ref<Mo01354Type>({
  values: {
    selectedRowId: '',
    selectedRowIds: [],
    items: [] as Mo1354CorrespondenceCareItemInfoType[],
  },
})

/** 共通情報 */
const commonInfo = {
  // 職員ID：共通情報.職員ID
  shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
}

//  行追加ボタン
const mo00611Oneway1 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.add-row'),
  prependIcon: 'add',
  width: '80px',
  tooltipText: t('tooltip.add-row'),
})
const mo00611Oneway4 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.add-row'),
  prependIcon: 'add',
  width: '80px',
  tooltipText: t('tooltip.add-row'),
})
// 行挿入ボタン
const mo00611Oneway2 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.insert-row'),
  prependIcon: 'add',
  width: '80px',
  tooltipText: t('tooltip.insert-row'),
})
const mo00611Oneway5 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.insert-row'),
  prependIcon: 'add',
  width: '80px',
  tooltipText: t('tooltip.insert-row'),
})
// 行複写ボタン
const mo00611Oneway3 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.duplicate-row'),
  prependIcon: 'file_copy',
  width: '80px',
  tooltipText: t('tooltip.duplicate-row'),
})
const mo00611Oneway6 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.duplicate-row'),
  prependIcon: 'file_copy',
  width: '80px',
  tooltipText: t('tooltip.duplicate-row'),
})
// 行削除ボタン
const mo01265Oneway1 = ref<Mo01265OnewayType>({
  btnLabel: t('btn.delete-row'),
  prependIcon: 'delete',
  width: '80px',
  tooltipText: t('tooltip.delete-row'),
})
const mo01265Oneway2 = ref<Mo01265OnewayType>({
  btnLabel: t('btn.delete-row'),
  prependIcon: 'delete',
  width: '80px',
  tooltipText: t('tooltip.delete-row'),
})
// 表示順
const mo00009Oneway1 = ref({
  appendIcon: 'edit_square',
  btnLabel: t('btn.display-order'),
  width: '80px',
  tooltipText: t('tooltip.display-order'),
  color: 'rgb(var(--v-theme-black-500))',
})
const mo00009Oneway2 = ref({
  appendIcon: 'edit_square',
  btnLabel: t('btn.display-order'),
  width: '80px',
  tooltipText: t('tooltip.display-order'),
  color: 'rgb(var(--v-theme-black-500))',
})
// 閉じるボタン
const mo00611Oneway7 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
})
// 上書ボタン
const mo00609OverwriteOneway = ref<Mo00609OnewayType>({
  btnLabel: t('btn.overwrite'),
  width: '90px',
  tooltipText: t('tooltip.overwrite'),
})
// 追加ボタン
const mo00609AddOneway = ref<Mo00609OnewayType>({
  btnLabel: t('btn.add'),
  width: '90px',
  tooltipText: t('tooltip.add'),
})
// 保存ボタン
const mo00609Oneway3 = ref<Mo00609OnewayType>({
  btnLabel: t('btn.save'),
  width: '90px',
  tooltipText: t('tooltip.save'),
  disabled: !(await hasRegistAuth()),
})
// 確定ボタン
const mo00609Oneway4 = ref<Mo00609OnewayType>({
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-btn'),
  disabled: false,
})
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27504Const.DEFAULT.IS_OPEN,
  emitType: 'blank',
})

const mo01280Oneway = ref<Mo01280OnewayType>({
  // デフォルト値の設定
  maxLength: 4000,
  rows: '4',
} as Mo01280OnewayType)

const or10347Data1 = ref<Or10347OnewayType>({
  // 画面区分
  parentViewType: '1',
  // 計画期間ID
  parentProcessingType: '0',
  // 親画面からの初期値
  sortList: [],
})

const or10347Data2 = ref<Or10347OnewayType>({
  // 画面区分
  parentViewType: '1',
  // 計画期間ID
  parentProcessingType: '1',
  // 親画面からの初期値
  sortList: [],
})

// 選択した行のindex
const scrollContentsIndex = ref<number>(0)
// 選択した行のindex
// const scrollItemsIndex = ref<number>(0)

const max_id_1 = ref<number>(0)
const max_id_2 = ref<number>(0)

const updateInfo = ref<Or27504TabDataType>({
  /** Mo1354具体的内容 */
  mo1354ConcreteContentsTab: {} as Mo1354ConcreteContentsInfoType,
  /** Mo1354対応するケア */
  mo1354CorrespondenceCareItemTab: [] as Mo1354CorrespondenceCareItemInfoType[],
})

// 共通処理の編集権限チェック
const isHasRegistAuth = ref(false)

const or21814_1 = ref({ uniqueCpId: '' }) // 確認ダイアログ（はい、いいえ、キャンセル）
const or21814_2 = ref({ uniqueCpId: '' }) // 確認ダイアログ（はい、いいえ）
const or21814_3 = ref({ uniqueCpId: '' }) // 確認ダイアログ（はい）
const or21815_1 = ref({ uniqueCpId: '' })
const or10347_1 = ref({ uniqueCpId: '' })
const or10347_2 = ref({ uniqueCpId: '' })

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // 処理設定
  await init()
  // 初期情報取得
  await getInitDataInfo()
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or21814Const.CP_ID(3)]: or21814_3.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
  [Or10347Const.CP_ID(1)]: or10347_1.value,
  [Or10347Const.CP_ID(2)]: or10347_2.value,
})

/**************************************************
 * 関数
 **************************************************/
// 初期処理
async function init() {
  isHasRegistAuth.value = await hasRegistAuth()
  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      isOpen: false,
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      isOpen: false,
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      isOpen: false,
      dialogTitle: t('label.confirm'),
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })

  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル

      isOpen: false,
      dialogTitle: t('label.caution'),
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
}

/**
 * 具体的内容情報リスト設定
 *
 * @param resData - 具体的内容・対応するケア項目マスタのレスポンスデータ
 */
const infoSet = (resData: IConcreteContentsCorrespondenceCareItemOutEntity) => {
  let concreteContentsInfoList: ConcreteContentsInfoType[] = []
  let correspondenceCareItemAllList: CorrespondenceCareItemInfoType[] = []
  const dataInfo = resData.data
  if (dataInfo) {
    if (dataInfo.useFlg) {
      local.useFlg = dataInfo.useFlg
      // 初期情報.利用フラグが1の場合、確定ボタン活性
      mo00609Oneway4.value.disabled = local.useFlg === '1' ? false : true
    }
    // 具体的内容情報取得
    concreteContentsInfoList = dataInfo.concreteContentsInfoList
    // 表示順でソート
    concreteContentsInfoList.sort((a, b) => Number(a.ci1Seq) - Number(b.ci1Seq))
    // 対応するケア情報取得
    correspondenceCareItemAllList = dataInfo.correspondenceCareInfoList
    // 表示順でソート
    correspondenceCareItemAllList.sort((a, b) => Number(a.ci2Seq) - Number(b.ci2Seq))

    // 対応するケア情報All---設定
    local.correspondenceCareItemInfoAll.items = []

    correspondenceCareItemAllList.forEach((item, index) => {
      local.correspondenceCareItemInfoAll.items.push({
        id: index.toString(),
        // 関連キー
        connectionKey: '',
        // 入力ID
        ci2Id: item.ci2Id,
        // 具体的内容ID
        ci1Id: item.ci1Id,
        // 対応するケア
        ci2Knj: {
          value: item.ci2Knj,
        } as Mo01280Type,
        // 表示順
        ci2Seq: item.ci2Seq,
        // 更新回数
        updateKbn: '',
        isUpdate: false,
        sel: { values: ['0'] } as Mo01332Type,
        onewayModelValue: {
          showItemLabel: false,
          items: [{ label: '', value: index.toString() }],
        } as Mo01332OnewayType,
      })
    })

    max_id_2.value = local.correspondenceCareItemInfoAll.items.length

    // 具体的内容情報BAK---設定
    const mo01354TypeContentsItems = [] as Mo1354ConcreteContentsInfoType[]
    concreteContentsInfoList.forEach((item, index) => {
      mo01354TypeContentsItems.push({
        id: index.toString(),
        // 関連キー
        connectionKey: index.toString(),
        // 入力ID
        ci1Id: item.ci1Id,
        ci1Kbn: item.ci1Kbn,
        b4Cd: item.b4Cd,
        // 具体的内容
        ci1Knj: {
          value: item.ci1Knj,
        } as Mo01280Type,
        // 表示順
        ci1Seq: item.ci1Seq,
        // 更新区分
        updateKbn: '',
        isUpdate: false,
      })
    })
    local.contentsInfoAll.items = mo01354TypeContentsItems
    // 具体的内容情報リストのバックアップを作成
    local.contentsInfoAllBAK.items = local.contentsInfoAll.items.map((item) => ({ ...item }))

    max_id_1.value = local.contentsInfoAll.items.length
    concreteContentsSet()
    mo01354TypeContentsItems.forEach((i) => {
      local.correspondenceCareItemInfoAll.items.forEach((_item) => {
        if (_item.ci1Id === i.ci1Id) {
          _item.connectionKey = i.connectionKey
        }
      })
    })
    // 対応するケア項目リストのバックアップを作成
    local.correspondenceCareItemInfoAllBAK.items = local.correspondenceCareItemInfoAll.items.map(
      (item) => ({ ...item })
    )
    // 対応するケア項目リストが表示されます
    correspondenceCareItemInfoSet()
  }
}
/** 初期情報取得 */
async function getInitDataInfo() {
  const param: IConcreteContentsCorrespondenceCareItemInEntity = {
    /** 問題点CD */
    b4Cd: localOneway.or27504Oneway.b4Cd,
    /** 職員ID */
    shokuinId: commonInfo.shokuId,
    /** 事業所ID */
    svJigyoId: localOneway.or27504Oneway.svJigyoId,
    /** 機能名 */
    asKinouName: localOneway.or27504Oneway.asKinouName,
  }
  // バックエンドAPIから初期情報取得
  const resData: IConcreteContentsCorrespondenceCareItemOutEntity = await ScreenRepository.select(
    'concreteContentsCorrespondenceCareSelect',
    param
  )
  // データ情報設定
  infoSet(resData)
}

/**
 * 具体的内容リスト設定
 *
 * @param selectedRowId - 选中行ID
 */
const concreteContentsSet = (selectedRowId?: string) => {
  local.contentsInfoAll.items.forEach((item, index) => {
    if (!item.ci1Id && item.updateKbn === UPDATE_KBN.DELETE) {
      // 削除対象のアイテムを削除
      local.contentsInfoAll.items.splice(index, 1)
    }
  })

  mo01354TypeContents.value.values.items = local.contentsInfoAll.items.filter(
    (item) => item.updateKbn !== UPDATE_KBN.DELETE
  )
  if (selectedRowId) {
    mo01354TypeContents.value.values.selectedRowId = selectedRowId
  } else {
    mo01354TypeContents.value.values.selectedRowId = mo01354TypeContents.value.values.items[0]?.id
  }
}

/**
 * 対応するケア情報リスト設定
 *
 * @param selectedRowId - 选中行ID
 */
const correspondenceCareItemInfoSet = (selectedRowId?: string) => {
  mo01354TypeItems.value.values.items = local.correspondenceCareItemInfoAll.items.filter((item) => {
    return (
      item.connectionKey === mo01354TypeContents.value.values.selectedRowId &&
      item.updateKbn !== UPDATE_KBN.DELETE
    )
  })
  if (selectedRowId) {
    mo01354TypeItems.value.values.selectedRowId = selectedRowId
  } else {
    mo01354TypeItems.value.values.selectedRowId = mo01354TypeItems.value.values.items[0]?.id
  }
}

/**
 * 閉じる
 */
function onClickCloseAction(): void {
  setState({ isOpen: false })
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = async () => {
  const updateFlag_1 = local.contentsInfoAll.items.find((item) => item.isUpdate === true)
  const updateFlag_2 = local.correspondenceCareItemInfoAll.items.find(
    (item) => item.isUpdate === true
  )
  if (!updateFlag_1 && !updateFlag_2) {
    // 変更がない場合、本画面を閉じる。
    onClickCloseAction()
  } else {
    // 画面内容を変更し、未保存の場合
    // 編集権限無の場合
    if (!isHasRegistAuth.value) {
      const dialogResult = await openConfirmDialog2(t('message.i-cmn-10006'))
      switch (dialogResult) {
        case 'yes': {
          // はい：処理続き
          onClickCloseAction()
          break
        }
        case 'no':
          // いいえ：処理終了
          break
      }
    } else {
      // 編集権限有の場合
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case 'yes': {
          // はい：処理はAC017-2～AC017-3と同じで、本画面を閉じる。
          const isNone = onSaveCheckIsNone()
          if (!isNone) {
            // 空の内容がない API呼び出させずに
            void _save()
            onClickCloseAction()
          }
          break
        }
        case 'no':
          // いいえ：処理続き
          onClickCloseAction()
          break
        case 'cancel':
          // 処理終了
          break
      }
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog1(paramDialogText: string): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
async function openConfirmDialog2(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
      secondBtnType: 'normal3',
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_2.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_2.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確定ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes,)
 */
async function openConfirmDialog3(paramDialogText: string): Promise<'yes'> {
  // 確定ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確定ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_3.value.uniqueCpId)

        let result = 'no' as 'yes'

        if (event?.thirdBtnClickFlg) {
          result = 'yes'
        }

        // 確定ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_3.value.uniqueCpId,
          events: {
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**
 * 行追加ボタン
 *
 * @param type - 1: 具体的内容, 2: 対応するケア
 */
const onClickAddBtn = (type: string) => {
  if (type === Or27504Const.DEFAULT.BTN_TYPE_1) {
    const key1List: string[] = []
    local.contentsInfoAll.items.map((item) => {
      key1List.push(item.id)
    })
    const obj = {
      id: max_id_1.value.toString(),
      connectionKey: max_id_1.value.toString(),
      ci1Seq: '',
      ci1Id: '',
      ci1Kbn: Or27504Const.DEFAULT.CI1_KBN_0,
      b4Cd: localOneway.or27504Oneway.b4Cd,
      ci1Knj: { value: '' } as Mo01280Type,
      updateKbn: UPDATE_KBN.CREATE,
      isUpdate: false,
    } as Mo1354ConcreteContentsInfoType
    local.contentsInfoAll.items.push(obj)
    max_id_1.value++
    local.contentsInfoAll.items.forEach((item, index) => [(item.ci1Seq = (index + 1).toString())])
    concreteContentsSet(obj.id)
    // mo01354TypeContents.value.values.selectedRowId = obj.id
    scrollContentsIndex.value = mo01354TypeContents.value.values.items.length - 1
    // 選択行にスクロール
    scrollToSelectRow('.scrollContents', scrollContentsIndex.value)
  } else if (type === Or27504Const.DEFAULT.BTN_TYPE_2) {
    const filterList = local.correspondenceCareItemInfoAll.items.filter(
      (item) => item.connectionKey === mo01354TypeContents.value.values.selectedRowId
    )
    const _index: number = local.correspondenceCareItemInfoAll.items.findIndex(
      (item) => item.connectionKey === mo01354TypeContents.value.values.selectedRowId
    )

    const itemAllKeyList: string[] = []
    local.correspondenceCareItemInfoAll.items.map((item) => {
      itemAllKeyList.push(item.id)
    })
    const obj = {
      connectionKey: mo01354TypeContents.value.values.selectedRowId,
      id: max_id_2.value.toString(),
      ci2Id: '',
      ci1Id: local.concreteContentsInfoSelectItem.ci1Id
        ? local.concreteContentsInfoSelectItem.ci1Id
        : '',

      ci2Knj: { value: '' } as Mo01280Type,
      ci2Seq: '',
      updateKbn: UPDATE_KBN.CREATE,
      isUpdate: false,
      sel: { values: ['0'] } as Mo01332Type,
      onewayModelValue: {
        showItemLabel: false,
        items: [{ label: '', value: max_id_2.value.toString() }],
      } as Mo01332OnewayType,
    }
    local.correspondenceCareItemInfoAll.items.splice(_index + filterList.length, 0, obj)
    max_id_2.value++
    local.correspondenceCareItemInfoAll.items.forEach(
      (item, index) => (item.ci2Seq = (index + 1).toString())
    )

    correspondenceCareItemInfoSet(obj.id)
  }
}

// 行挿入ボタン
const onClickInsertBtn = (type: string) => {
  if (type === Or27504Const.DEFAULT.BTN_TYPE_1) {
    const _index: number = local.contentsInfoAll.items.findIndex(
      (item) => item.id === mo01354TypeContents.value.values.selectedRowId
    )
    const key1List: string[] = []
    local.contentsInfoAll.items.map((item) => {
      key1List.push(item.id)
    })
    const obj = {
      id: max_id_1.value.toString(),
      connectionKey: max_id_1.value.toString(),
      ci1Seq: '',
      ci1Id: '',
      ci1Kbn: Or27504Const.DEFAULT.CI1_KBN_0,
      b4Cd: localOneway.or27504Oneway.b4Cd,
      ci1Knj: { value: '' } as Mo01280Type,
      updateKbn: UPDATE_KBN.CREATE,
      isUpdate: false,
    }
    local.contentsInfoAll.items.splice(_index, 0, obj)
    max_id_1.value++
    local.contentsInfoAll.items.forEach((item, index) => [(item.ci1Seq = (index + 1).toString())])
    concreteContentsSet(obj.id)
    scrollToSelectRow('.scrollContents', _index)
  } else if (type === Or27504Const.DEFAULT.BTN_TYPE_2) {
    const _index: number = local.correspondenceCareItemInfoAll.items.findIndex(
      (item) => item.id === mo01354TypeItems.value.values.selectedRowId
    )
    const itemAllKeyList: string[] = []
    local.correspondenceCareItemInfoAll.items.map((item) => {
      itemAllKeyList.push(item.id)
    })
    const obj = {
      connectionKey: mo01354TypeContents.value.values.selectedRowId,
      id: max_id_2.value.toString(),
      ci2Id: '',
      ci1Id: local.concreteContentsInfoSelectItem.ci1Id
        ? local.concreteContentsInfoSelectItem.ci1Id
        : '',
      ci2Knj: { value: '' } as Mo01280Type,
      ci2Seq: '',
      updateKbn: UPDATE_KBN.CREATE,
      isUpdate: false,
      sel: { values: ['0'] } as Mo01332Type,
      onewayModelValue: {
        showItemLabel: false,
        items: [{ label: '', value: max_id_2.value.toString()}],
      } as Mo01332OnewayType,
    }

    local.correspondenceCareItemInfoAll.items.splice(_index, 0, obj)
    max_id_2.value++
    local.correspondenceCareItemInfoAll.items.forEach(
      (item, index) => (item.ci2Seq = (index + 1).toString())
    )
    correspondenceCareItemInfoSet(obj.id)
  }
}
// 行複写ボタン
const onClickDuplicateBtn = (type: string) => {
  if (
    type === Or27504Const.DEFAULT.BTN_TYPE_1 &&
    mo01354TypeContents.value.values.items.length > 0
  ) {
    const _index: number = local.contentsInfoAll.items.findIndex(
      (item) => item.id === mo01354TypeContents.value.values.selectedRowId
    )
    const key1List: string[] = []
    local.contentsInfoAll.items.map((item) => {
      key1List.push(item.id)
    })
    const ci1Knj = local.contentsInfoAll.items[_index].ci1Knj as Mo01280Type
    const obj = {
      id: max_id_1.value.toString(),
      connectionKey: max_id_1.value.toString(),
      ci1Seq: '',
      ci1Id: '',
      ci1Kbn: Or27504Const.DEFAULT.CI1_KBN_0,
      b4Cd: localOneway.or27504Oneway.b4Cd,
      ci1Knj: ci1Knj,
      updateKbn: UPDATE_KBN.CREATE,
      isUpdate: false,
    } as Mo1354ConcreteContentsInfoType
    local.contentsInfoAll.items.splice(_index + 1, 0, obj)
    max_id_1.value++
    local.contentsInfoAll.items.forEach((item, index) => [(item.ci1Seq = (index + 1).toString())])
    concreteContentsSet(obj.id)
    scrollToSelectRow('.scrollContents', _index + 1)
  } else if (type === '2') {
    const _index: number = local.correspondenceCareItemInfoAll.items.findIndex(
      (item) => item.id === mo01354TypeItems.value.values.selectedRowId
    )
    const itemAllKeyList: string[] = []
    local.correspondenceCareItemInfoAll.items.map((item) => {
      itemAllKeyList.push(item.id)
    })
    const obj = {
      connectionKey: mo01354TypeContents.value.values.selectedRowId,
      id: max_id_2.value.toString(),
      ci2Id: '',
      ci1Id: local.concreteContentsInfoSelectItem.ci1Id
        ? local.concreteContentsInfoSelectItem.ci1Id
        : '',
      ci2Knj: local.correspondenceCareItemInfoAll.items[_index].ci2Knj,
      ci2Seq: '',
      updateKbn: UPDATE_KBN.CREATE,
      isUpdate: false,
      sel: local.correspondenceCareItemInfoAll.items[_index].sel,
      onewayModelValue: {
        showItemLabel: false,
        items: [{ label: '', value: max_id_2.value.toString() }],
      } as Mo01332OnewayType,
    }
    local.correspondenceCareItemInfoAll.items.splice(_index + 1, 0, obj)
    max_id_2.value++
    local.correspondenceCareItemInfoAll.items.forEach(
      (item, index) => (item.ci2Seq = (index + 1).toString())
    )
    correspondenceCareItemInfoSet(obj.id)
  }
}
// 行削除ボタン
const onClickDeleteBtn = async (type: string) => {
  const message = type === '1' ? t('message.i-cmn-10882') : t('message.i-cmn-10219')
  const dialogResult = await openConfirmDialog2(message)
  switch (dialogResult) {
    case 'yes': {
      // はい選択時削除
      onClickDelete(type)
      break
    }
    case 'no':
      // いいえ選択時
      break
  }
}

// 行削除ボタン
const onClickDelete = (type: string) => {
  if (type === Or27504Const.DEFAULT.BTN_TYPE_1) {
    const _index: number = local.contentsInfoAll.items.findIndex(
      (item) => item.id === mo01354TypeContents.value.values.selectedRowId
    )
    local.contentsInfoAll.items[_index].updateKbn = UPDATE_KBN.DELETE
    const connectionKey = local.contentsInfoAll.items[_index].connectionKey
    // 対応するケア項目の更新
    local.correspondenceCareItemInfoAll.items.forEach((item) => {
      if (item.connectionKey === connectionKey) {
        item.updateKbn = UPDATE_KBN.DELETE
      }
    })
    const tabIndex = mo01354TypeContents.value.values.items.findIndex(
      (item) => item.id === mo01354TypeContents.value.values.selectedRowId
    )
    let selectedRowId = ''
    if (
      mo01354TypeContents.value.values.selectedRowId ===
      mo01354TypeContents.value.values.items[mo01354TypeContents.value.values.items.length - 1].id
    ) {
      // 最後の行を削除する場合、次の行を選択
      selectedRowId = mo01354TypeContents.value.values.items[tabIndex - 1]?.id
    } else {
      selectedRowId = mo01354TypeContents.value.values.items[tabIndex + 1]?.id
    }
    mo01354TypeContents.value.values.items.forEach((item, index) => [
      (item.ci1Seq = (index + 1).toString()),
    ])

    concreteContentsSet(selectedRowId)
  } else if (type === Or27504Const.DEFAULT.BTN_TYPE_2) {
    const _index: number = local.correspondenceCareItemInfoAll.items.findIndex(
      (item) => item.id === mo01354TypeItems.value.values.selectedRowId
    )
    local.correspondenceCareItemInfoAll.items[_index].updateKbn = UPDATE_KBN.DELETE

    local.correspondenceCareItemInfoAll.items.forEach(
      (item, index) => (item.ci2Seq = (index + 1).toString())
    )
    correspondenceCareItemInfoSet()
  }
}

// AC011-1-具体的な内容リストを編集後の順序で再並べ替えます。
const function1034 = (data: {
  sortList: { id: string; displayOrder: { value: string }; concreteContents: string }[]
}) => {
  if (data.sortList && data.sortList.length > 0) {
    data.sortList.sort((a, b) => Number(a.id) - Number(b.id))
    mo01354TypeContents.value.values.items.forEach((item, index) => {
      item.ci1Seq = data.sortList[index].displayOrder.value.toString()
    })
  }
  mo01354TypeContents.value.values.items.sort((a, b) => Number(a.ci1Seq) - Number(b.ci1Seq))
}
// AC011-2-対応するケア項目リストを編集後の順序で再並べ替えます。
const function1034_2 = (data: {
  sortList: { id: string; displayOrder: { value: string }; correspondenceCareItem: string }[]
}) => {
  if (data.sortList && data.sortList.length > 0) {
    data.sortList.sort((a, b) => Number(a.id) - Number(b.id))

    mo01354TypeItems.value.values.items.forEach((item, index) => {
      item.ci2Seq = data.sortList[index].displayOrder.value.toString()
    })
  }
  mo01354TypeItems.value.values.items.sort((a, b) => Number(a.ci2Seq) - Number(b.ci2Seq))
}
// ダイアログ表示フラグ
const showDialogOr10347_1 = computed(() => {
  // Or10347のダイアログ開閉状態
  return Or10347Logic.state.get(or10347_1.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr10347_2 = computed(() => {
  // Or10347のダイアログ開閉状態
  return Or10347Logic.state.get(or10347_2.value.uniqueCpId)?.isOpen ?? false
})

const onClickDisplayBtn = (type: string) => {
  switch (type) {
    case Or27504Const.DEFAULT.BTN_TYPE_1: {
      const or10347Data1List = [] as { key: string; sort: number; concreteContents: string }[]
      const contents = mo01354TypeContents.value.values.items as Mo1354ConcreteContentsInfoType[]
      if (contents.length > 0) {
        contents.map((item) => {
          or10347Data1List.push({
            key: item.id,
            sort: Number(item.ci1Seq),
            concreteContents: item.ci1Knj.value,
          })
        })
        or10347Data1.value.sortList = or10347Data1List
        Or10347Logic.state.set({
          uniqueCpId: or10347_1.value.uniqueCpId,
          state: { isOpen: true },
        })
      }

      break
    }
    case Or27504Const.DEFAULT.BTN_TYPE_2: {
      const or10347Data2List = [] as { key: string; sort: number; correspondenceCareItem: string }[]
      const items = mo01354TypeItems.value.values.items as Mo1354CorrespondenceCareItemInfoType[]
      if (items.length > 0) {
        items.map((item) => {
          or10347Data2List.push({
            key: item.id,
            sort: Number(item.ci2Seq),
            correspondenceCareItem: item.ci2Knj.value,
          })
        })
        or10347Data2.value.sortList = or10347Data2List
        Or10347Logic.state.set({
          uniqueCpId: or10347_2.value.uniqueCpId,
          state: { isOpen: true },
        })
      }

      break
    }
  }
}

/** 「保存ボタン」押下check */
const onSaveCheckIsNone = () => {
  const isNone_1 = local.contentsInfoAll.items.find((item) => item.ci1Knj.value === '')
  const isNone_2 = local.correspondenceCareItemInfoAll.items.find(
    (item) => item.ci2Knj.value === ''
  )
  if (isNone_1) {
    // エラーメッセージを表示
    Or21815Logic.state.set({
      uniqueCpId: or21815_1.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.e-cmn-41716', [t('label.concrete-care-specific-content')]),
      },
    })
  }
  if (isNone_2) {
    // エラーメッセージを表示
    Or21815Logic.state.set({
      uniqueCpId: or21815_1.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.e-cmn-41716', [t('label.corresponding-care-items')]),
      },
    })
  }
  if (isNone_1 || isNone_2) {
    return true
  } else {
    return false
  }
}

/** AC017 「保存ボタン」押下*/
const onClickSaveBtn = async () => {
  const changeFlag_1 = local.contentsInfoAll.items.find(
    (item) => item.updateKbn !== UPDATE_KBN.NONE
  )
  const changeFlag_2 = local.correspondenceCareItemInfoAll.items.find(
    (item) => item.updateKbn !== UPDATE_KBN.NONE
  )

  if (!changeFlag_1 && !changeFlag_2) {
    // AC017-1
    // 画面入力項目が変更しないの場合 NO
    const result = await openConfirmDialog3(t('message.i-cmn-21800'))
    if (result === 'yes') {
      // AC017-3-1
      // API呼び出させずに
      void _save()
    }
  } else {
    // AC017-2-1、AC017-2-2
    // 内容が空かどうかを確認する
    const isNone = onSaveCheckIsNone()
    if (!isNone) {
      // AC017-3-1
      // 空の内容がない API呼び出させずに
      void _save()
    }
  }
}

/** 上書きダイアログ AC015-2-1～AC015-2-4 */
const onClickSubmitAction = async () => {
  const dialogResult = await openConfirmDialog2(t('message.i-cmn-10883'))
  switch (dialogResult) {
    case 'yes': {
      // 「はい」ボタン押下後、選択内容を呼び出し元画面に返却して、本画面を閉じる。
      //  選択内容を呼び出し元画面に返却して、本画面を閉じる。
      let contents = ''
      const filterItems = local.correspondenceCareItemInfoAll.items.filter(
        (item) => item.connectionKey === mo01354TypeContents.value.values.selectedRowId
      )
      if (filterItems) {
        contents = filterItems.filter(it => it.sel.values.length > 1).map((item) => item.ci2Knj.value).join('\n')
      }
      const updateInfo = {
        // 内容
        content: contents,
        // 処理モード
        mode: '1',
        // 具体的内容ID
        ci1Id: '0',
        // 対応するケア項目ID
        ci2Id: '0',
      }
      emit('update:modelValue', updateInfo)
      onClickCloseAction()
      break
    }
    case 'no':
      // 「いいえ」ボタン押下後、ポップアップを閉じる。
      break
  }
}

/** AC015	「上書ボタン」押下		 */
const onClickSubmitBtn = async () => {
  const updateFlag_1 = local.contentsInfoAll.items.find((item) => item.isUpdate === true)
  const updateFlag_2 = local.correspondenceCareItemInfoAll.items.find(
    (item) => item.isUpdate === true
  )
  //   画面内容を変更し、未保存かつ登録権限があるの場合
  // （※新しい行追加のみ、既存行の内容が未変更の場合、画面内容未変更で判定する。）

  if ((isHasRegistAuth.value && updateFlag_1) || updateFlag_2) {
    const dialogResult = await openConfirmDialog1(
      t('message.i-cmn-11329', [t('label.concrete-contents-correspondence-care-item')])
    )
    switch (dialogResult) {
      case 'yes': {
        // 「はい」ボタン押下後、処理はAC017-2～AC017-3と同じです。
        // その後、AC015-2-1～AC015-2-4と同じです。
        const isNone = onSaveCheckIsNone()
        if (!isNone) {
          // 空の内容がない API呼び出させずに
          void _save()
          await onClickSubmitAction()
        }
        break
      }
      case 'no':
        // 「いいえ」ボタン押下後、AC015-2-1～AC015-2-4と同じです。
        await onClickSubmitAction()
        break
      case 'cancel':
        // 「キャンセル」ボタン押下後、メッセージポップアップを閉じる
        break
    }
  }
  if (!updateFlag_1 && !updateFlag_2) {
    // 画面内容が未変更の場合
    // AC015-2-1～AC015-2-4
    await onClickSubmitAction()
  }
}

/** AC016	「追加ボタン」押下		 */
const onClickPushBtn = async () => {
  const updateFlag_1 = local.contentsInfoAll.items.find((item) => item.isUpdate === true)
  const updateFlag_2 = local.correspondenceCareItemInfoAll.items.find(
    (item) => item.isUpdate === true
  )
  //   画面内容を変更し、未保存かつ登録権限があるの場合
  // （※新しい行追加のみ、既存行の内容が未変更の場合、画面内容未変更で判定する。）
  if ((isHasRegistAuth.value && updateFlag_1) || updateFlag_2) {
    const dialogResult = await openConfirmDialog1(
      t('message.i-cmn-11329', [t('label.concrete-contents-correspondence-care-item')])
    )
    switch (dialogResult) {
      case 'yes': {
        // 「はい」ボタン押下後、処理はAC017-2～AC017-3と同じです。
        // 選択内容を呼び出し元画面に返却して、本画面を閉じる。
        // 内容が空かどうかを確認する
        const isNone = onSaveCheckIsNone()
        if (!isNone) {
          // 空の内容がない API呼び出させずに
          void _save()
          //  選択内容を呼び出し元画面に返却して、本画面を閉じる。
          let contents = ''
          const filterItems = local.correspondenceCareItemInfoAll.items.filter(
            (item) => item.connectionKey === mo01354TypeContents.value.values.selectedRowId
          )
          if (filterItems) {
            contents = filterItems.filter(it => it.sel.values.length > 1).map((item) => item.ci2Knj.value).join('\n')
          }
          const updateInfo = {
            // 内容
            content: contents,
            // 処理モード
            mode: '2',
            // 具体的内容ID
            ci1Id: '0',
            // 対応するケア項目ID
            ci2Id: '0',
          }

          emit('update:modelValue', updateInfo)
          onClickCloseAction()
        }
        break
      }
      case 'no':
        // 「いいえ」ボタン押下後
        //  選択内容を呼び出し元画面に返却して、本画面を閉じる。
        onClickCloseAction()
        break
      case 'cancel':
        // 「キャンセル」ボタン押下後、メッセージポップアップを閉じる
        break
    }
  }
  if (!updateFlag_1 && !updateFlag_2) {
    // 画面内容が未変更の場合
    //  選択内容を呼び出し元画面に返却して、本画面を閉じる。
    let contents = ''
    const filterItems = local.correspondenceCareItemInfoAll.items.filter(
      (item) => item.connectionKey === mo01354TypeContents.value.values.selectedRowId
    )
 if (filterItems) {
      contents = filterItems.filter(it => it.sel.values.length > 1).map((item) => item.ci2Knj.value).join('\n')
    }

    const updateInfo = {
      // 内容
      content: contents,
      // 処理モード
      mode: '2',
      // 具体的内容ID
      ci1Id: '0',
      // 対応するケア項目ID
      ci2Id: '0',
    }
    emit('update:modelValue', updateInfo)
    onClickCloseAction()
  }
}

/** AC018「確定ボタン」押下 */
const onClickConfirmBtn = async () => {
  const updateFlag_1 = local.contentsInfoAll.items.find((item) => item.isUpdate === true)
  const updateFlag_2 = local.correspondenceCareItemInfoAll.items.find(
    (item) => item.isUpdate === true
  )
  if (!updateFlag_1 && !updateFlag_2) {
    // 画面内容が未変更の場合
    //  選択内容を呼び出し元画面に返却して、本画面を閉じる。
    emit('update:modelValue', { ...updateInfo.value })
    onClickCloseAction()
  } else {
    // 画面内容を変更し、未保存の場合
    // （※新しい行追加のみ、既存行の内容が未変更の場合、画面内容未変更で判定する。）
    const dialogResult = await openConfirmDialog1(
      t('message.i-cmn-11329', [t('label.concrete-contents-correspondence-care-item')])
    )
    switch (dialogResult) {
      case 'yes': {
        // 「はい」ボタン押下後、処理はAC017-2～AC017-3と同じです。
        // 選択内容を呼び出し元画面に返却して、本画面を閉じる。
        // 内容が空かどうかを確認する
        const isNone = onSaveCheckIsNone()
        if (!isNone) {
          // 空の内容がない API呼び出させずに
          void _save()
          //  選択内容を呼び出し元画面に返却して、本画面を閉じる。
          emit('update:modelValue', { ...updateInfo.value })
          onClickCloseAction()
        }
        break
      }
      case 'no':
        // 「いいえ」ボタン押下後
        //  選択内容を呼び出し元画面に返却して、本画面を閉じる。
        onClickCloseAction()
        break
      case 'cancel':
        // 「キャンセル」ボタン押下後、メッセージポップアップを閉じる
        break
    }
  }
}

const checkboxFocus = (item: Mo1354CorrespondenceCareItemInfoType) => {
  if (item.sel && Array.isArray(item.sel.values)) {
    if (item.sel.values.length === 1) {
      item.sel.values.push(item.id.toString())
    } else if (item.sel.values.length === 2) {
      item.sel.values = ['0']
    }
  }
}


const changeContents = (item: Mo1354ConcreteContentsInfoType) => {
  if (item.updateKbn === UPDATE_KBN.NONE) {
    item.updateKbn = UPDATE_KBN.UPDATE
  }
  item.isUpdate = true
}

const changeItems = (item: Mo1354CorrespondenceCareItemInfoType) => {
  if (item.updateKbn === UPDATE_KBN.NONE) {
    item.updateKbn = UPDATE_KBN.UPDATE
  }
  item.isUpdate = true
}

/**
 * 選択行にスクロール
 *
 * @param className - className
 *
 * @param index - index
 */
function scrollToSelectRow(className: string, index: number) {
  // スクロール
  if (index >= 0) {
    void nextTick(() => {
      const component = document.querySelector(className)!
      const tbody = component.querySelector('.v-table__wrapper tbody')!
      if (tbody) {
        const row = tbody.children[index]
        if (row) {
          const rowRect = row.getBoundingClientRect()
          const tableWrapper = component.querySelector('.v-table__wrapper')!
          if (tableWrapper) {
            const tableRect = tableWrapper.getBoundingClientRect()
            const thead = component.querySelector('.v-table__wrapper thead')!
            if (thead) {
              const theadRect = thead.getBoundingClientRect()

              // 上に移動の場合
              if (rowRect.top < tableRect.top + theadRect.height) {
                tableWrapper.scrollTop =
                  tableWrapper.scrollTop - (tableRect.top + theadRect.height - rowRect.top)
              } else if (rowRect.top + rowRect.height > tableRect.top + tableRect.height) {
                const offsetHeight = rowRect.top + rowRect.height - tableRect.top - tableRect.height
                tableWrapper.scrollTop = tableWrapper.scrollTop + offsetHeight
              }
            }
          }
        }
      }
    })
  }
}

/**
 *  保存処理
 */
async function _save() {
  // 更新データ作成
  const inputData: ConcreteContentsCorrespondenceCareUpdateInEntity = {
    concreteContentsInfoList: [] as IConcreteContentsInfoListStateType[],
    correspondenceCareInfoList: [] as ICorrespondenceCareInfoListStateType[],
  }

  local.contentsInfoAll.items.forEach((item) => {
    local.contentsInfoAllBAK.items.forEach((bakItem) => {
      if (item.ci1Seq !== bakItem.ci1Seq) {
        if (item.updateKbn === UPDATE_KBN.NONE) {
          item.updateKbn = UPDATE_KBN.UPDATE
        }
      }
    })
    inputData.concreteContentsInfoList.push({
      ci1Id: item.ci1Id,
      ci1Kbn: item.ci1Kbn,
      b4Cd: item.b4Cd,
      ci1Knj: item.ci1Knj.value,
      ci1Seq: item.ci1Seq,
      updateKbn: item.updateKbn,
      connectionKey: item.ci1Id ? '' : item.connectionKey,
    })
  })

  local.correspondenceCareItemInfoAll.items.forEach((item) => {
    local.correspondenceCareItemInfoAllBAK.items.forEach((bakItem) => {
      if (item.ci2Seq !== bakItem.ci2Seq) {
        if (item.updateKbn === UPDATE_KBN.NONE) {
          item.updateKbn = UPDATE_KBN.UPDATE
        }
      }
    })
    inputData.correspondenceCareInfoList.push({
      ci2Id: item.ci2Id,
      ci1Id: item.ci1Id,
      ci2Knj: item.ci2Knj.value,
      ci2Seq: item.ci2Seq,
      updateKbn: item.updateKbn,
      connectionKey: item.ci1Id ? '' : item.connectionKey,
    })
  })
  const resData: ConcreteContentsCorrespondenceCareUpdateOutEntity = await ScreenRepository.insert(
    'concreteContentsCorrespondenceCareInsert',
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === 'success') {
    // 画面情報再取得
    await getInitDataInfo()
  }
}

/**************************************************
 * ウォッチャー
 **************************************************/

/**
 *  具体的な内容選択中の行を切り替える
 */
watch(
  () => mo01354TypeContents.value,
  () => {
    correspondenceCareItemInfoSet()
    const selectRow = mo01354TypeContents.value.values.items.find(
      (item) => item.id === mo01354TypeContents.value.values.selectedRowId
    ) as Mo1354ConcreteContentsInfoType
    local.concreteContentsInfoSelectItem = selectRow
  },
  { deep: true }
)

/**
 * 削除確認ダイアログ-ボタン押下時-
 */

watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await getInitDataInfo()
    }
  }
)

/**
 * 「×ボタン」押下
 */

watch(
  () => mo00024.value.emitType,
  async () => {
    if (mo00024.value.emitType === 'closeBtnClick') {
      await onClickCloseBtn()
      mo00024.value.emitType = undefined
    }
  }
)

watch(
  () => local.concreteContentsInfoSelectItem,
  () => {
    updateInfo.value.mo1354ConcreteContentsTab = local.concreteContentsInfoSelectItem
  }
)

watch(
  () => mo01354TypeItems,
  () => {
    updateInfo.value.mo1354CorrespondenceCareItemTab = mo01354TypeItems.value.values
      .items as Mo1354CorrespondenceCareItemInfoType[]
  },
  { deep: true }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row
          no-gutters
          class="flex-nowrap"
        >
          <c-v-col
            class="scrollContents pa-2"
            style="border-right: 1px rgb(var(--v-theme-black-200)) solid"
            col="6"
          >
            <c-v-row no-gutters>
              <!-- 行追加ボタン -->
              <base-mo00611
                :oneway-model-value="mo00611Oneway1"
                class="mr-2"
                @click="onClickAddBtn(Or27504Const.DEFAULT.BTN_TYPE_1)"
              >
              </base-mo00611>
              <!-- 行挿入ボタン -->
              <base-mo00611
                :oneway-model-value="mo00611Oneway2"
                class="mr-2"
                @click="onClickInsertBtn(Or27504Const.DEFAULT.BTN_TYPE_1)"
              >
              </base-mo00611>
              <!-- 行複写ボタン -->
              <base-mo00611
                :oneway-model-value="mo00611Oneway3"
                class="mr-2"
                @click="onClickDuplicateBtn(Or27504Const.DEFAULT.BTN_TYPE_1)"
              >
              </base-mo00611>
              <!-- 行削除ボタン -->
              <base-mo01265
                :oneway-model-value="mo01265Oneway1"
                class="mr-2"
                @click="onClickDeleteBtn(Or27504Const.DEFAULT.BTN_TYPE_1)"
              >
              </base-mo01265>
              <!-- 表示順 -->
              <base-mo00009
                :oneway-model-value="mo00009Oneway1"
                class="mr-2"
                @click="onClickDisplayBtn(Or27504Const.DEFAULT.BTN_TYPE_1)"
              >
              </base-mo00009>
            </c-v-row>
            <div class="table-header pt-2">
              <base-mo-01354
                v-model="mo01354TypeContents"
                :oneway-model-value="localOneway.headerOnewayContents"
                class="list-wrapper"
              >
                <template #[`item.ci1Knj`]="{ item }">
                  <div class="h-100">
                    <base-mo-01280
                      :id="`input-${item.id}`"
                      v-model="item.ci1Knj"
                      class="background-transparent number"
                      :oneway-model-value="mo01280Oneway"
                      @change="changeContents(item)"
                    />
                  </div>
                </template>
                <!-- ページングを非表示 -->
                <template #bottom />
              </base-mo-01354>
            </div>
          </c-v-col>

          <c-v-col
            class="scrollItems pa-2"
            col="6"
          >
            <c-v-row no-gutters>
              <!-- 行追加ボタン -->
              <base-mo00611
                :oneway-model-value="mo00611Oneway4"
                class="mr-2"
                @click="onClickAddBtn(Or27504Const.DEFAULT.BTN_TYPE_2)"
              >
              </base-mo00611>
              <!-- 行挿入ボタン -->
              <base-mo00611
                :oneway-model-value="mo00611Oneway5"
                class="mr-2"
                @click="onClickInsertBtn(Or27504Const.DEFAULT.BTN_TYPE_2)"
              >
              </base-mo00611>
              <!-- 行複写ボタン -->
              <base-mo00611
                :oneway-model-value="mo00611Oneway6"
                class="mr-2"
                @click="onClickDuplicateBtn(Or27504Const.DEFAULT.BTN_TYPE_2)"
              >
              </base-mo00611>
              <!-- 行削除ボタン -->
              <base-mo01265
                :oneway-model-value="mo01265Oneway2"
                class="mr-2"
                @click="onClickDeleteBtn(Or27504Const.DEFAULT.BTN_TYPE_2)"
              >
              </base-mo01265>
              <!-- 表示順 -->
              <base-mo00009
                :oneway-model-value="mo00009Oneway2"
                class="mr-2"
                @click="onClickDisplayBtn(Or27504Const.DEFAULT.BTN_TYPE_2)"
              >
              </base-mo00009>
            </c-v-row>
            <div class="table-header pt-2">
              <base-mo-01354
                v-model="mo01354TypeItems"
                :oneway-model-value="localOneway.headerOnewayItems"
                class="list-wrapper"
              >
                <template #[`item.check`]="{ item }">
                  <div
                    class="checkBox h-100"
                  >
                    <base-mo01332
                      v-model="item.sel"
                      :oneway-model-value="item.onewayModelValue"
                      @click="checkboxFocus(item)"
                    />
                  </div>
                </template>
                <template #[`item.ci2Knj`]="{ item }">
                  <div class="h-100">
                    <base-mo-01280
                      :id="`input-${item.id}`"
                      v-model="item.ci2Knj"
                      class="background-transparent number"
                      :oneway-model-value="mo01280Oneway"
                      @change="changeItems(item)"
                    />
                  </div>
                </template>
                <!-- ページングを非表示 -->
                <template #bottom />
              </base-mo-01354>
            </div>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway7"
          class="ml-2"
          @click="onClickCloseBtn"
        >
        </base-mo00611>

        <!-- 上書ボタン -->
        <base-mo00609
          v-if="props.onewayModelValue.b4Cd === '75'"
          :oneway-model-value="mo00609OverwriteOneway"
          class="ml-2"
          @click="onClickSubmitBtn"
        ></base-mo00609>

        <!-- 追加ボタン -->
        <base-mo00609
          v-if="props.onewayModelValue.b4Cd === '75'"
          :oneway-model-value="mo00609AddOneway"
          class="ml-2"
          @click="onClickPushBtn"
        ></base-mo00609>
        <!-- 保存ボタン -->
        <base-mo00609
          :oneway-model-value="mo00609Oneway3"
          class="ml-2"
          @click="onClickSaveBtn"
        >
        </base-mo00609>
        <!-- 確定ボタン -->
        <base-mo00609
          v-if="props.onewayModelValue.b4Cd !== '75'"
          :oneway-model-value="mo00609Oneway4"
          class="ml-2"
          @click="onClickConfirmBtn"
        ></base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>

  <!-- 未入力ダイアログ -->
  <g-base-or21815 v-bind="or21815_1" />

  <!-- 確認ダイアログ  更新ダイアログ  -->
  <g-base-or21814 v-bind="or21814_1" />

  <!-- 削除ダイアログ  && 上書ダイアログ-->
  <g-base-or21814 v-bind="or21814_2" />

  <g-base-or21814 v-bind="or21814_3" />

  <!-- 左側の GUI00840_表示順変更ケアチェック表画面を表示すること-->
  <g-custom-or-10347
    v-if="showDialogOr10347_1"
    v-bind="or10347_1"
    :oneway-model-value="or10347Data1"
    @update:model-value="function1034"
  />
  <!--  右側のGUI00840_表示順変更ケアチェック表画面を表示すること-->
  <g-custom-or-10347
    v-if="showDialogOr10347_2"
    v-bind="or10347_2"
    :oneway-model-value="or10347Data2"
    @update:model-value="function1034_2"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
.checkBox {
  :deep(input) {
    width: 100% !important;
    height: 100% !important;
  }
  :deep(.outer_div_style) {
    height: 100%;
    display: flex;
    justify-content: center !important;
    align-items: center !important;
  }
}
</style>
