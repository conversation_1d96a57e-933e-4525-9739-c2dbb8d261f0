<script setup lang="ts">
/**
 * OrX0099:カレンダー
 * カレンダー
 *
 * @description
 * 日程カレンダー
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { isDate } from 'lodash'
import type { OrX0099Calendar } from './OrX0099.type'
import type {
  OrX0099Type,
  OrX0099OnewayType,
  DblClickResult,
  OrX0099CalendarConfig,
  OrX0099CalendarEvent,
  OrX0099SegmentedEvent,
} from '~/types/cmn/business/components/OrX0099Type'
/** 入力パラメータ */
interface Props {
  calendarConfig: OrX0099CalendarConfig
  modelValue: OrX0099Type
  onewayModelValue: OrX0099OnewayType
  removeflg: boolean
}
// 入力パラメータ
const props = defineProps<Props>()
// エミット
const emit = defineEmits(['doubleClick', 'mouseUp', 'mouseDown', 'mouseMove'])
// 双方向バインディングの初期値
const defaultModelValue = {
  calendarConfig: {
    events: [] as OrX0099CalendarEvent[],
    startHour: 0,
    endHour: 24,
    cellHeight: 22,
    zoom: 1,
    allCellNumber: 24,
  } as OrX0099CalendarConfig,
  onewayModelValue: {},
  removeflg: true,
}

const local = reactive({
  orx0099: {
    ...props.modelValue,
  } as OrX0099Type,
  calendar: {
    ...defaultModelValue.calendarConfig,
    ...props.calendarConfig,
  } as OrX0099CalendarConfig,
  realCalendar: {
    ...defaultModelValue.calendarConfig,
  } as OrX0099CalendarConfig,
  removeflg: false,
})

const localOneWay = reactive({
  orX0099: {
    ...props.onewayModelValue,
  } as OrX0099OnewayType,
  orX0099Calendar: {
    height: 0,
  } as OrX0099Calendar,
  zIndex: 1000,
  moveChange: false,
})
/**************************************************
 * init
 **************************************************/
onMounted(() => {
  local.removeflg = props.removeflg
  localOneWay.zIndex = 1000
  init()
})
// 日程要素定義
const calendarContainer = ref<Element | null>()
const gridBody = ref<Element | null>()
/**
 * 日程コンポーネント初期化
 */
function init() {
  calendarContainer.value = document.querySelector('.calendar-container')
  gridBody.value = document.querySelector('.grid-body')
  const eventArray: OrX0099CalendarEvent[] = []
  local.calendar.events.forEach((item) => {
    const start = stringToDate('2025-01-01' + ' ' + item.start)
    const end = stringToDate('2025-01-01' + ' ' + item.end)
    if (end <= start) {
      end.setDate(end.getDate() + 1)
    }
    if (item.period !== undefined) {
      const periodEnd = new Date(start.getTime())
      // 期間 を 3 として渡すと、最終的にレンダリングされる日程ブロックの長さは 3 になるはずなので、-1
      periodEnd.setDate(periodEnd.getDate() + (item.period - 1 >= 0 ? item.period - 1 : 0))
      eventArray.push({
        ...item,
        start: formatDate(start, false),
        end: formatDate(end, false),
        periodStart: start,
        periodEnd: periodEnd,
        width: item.tableWidth,
        customId: item.customId,
      })
    } else {
      eventArray.push({
        ...item,
        start: formatDate(start, false),
        end: formatDate(end, false),
        customId: item.customId,
      })
    }
  })
  local.realCalendar = {
    ...local.calendar,
    events: eventArray,
    startHour: 0,
    endHour: 24,
  }

  localOneWay.orX0099Calendar.height =
    localOneWay.orX0099.cellheight * localOneWay.orX0099.firstSlots.length

  if (localOneWay.orX0099.evenGridWidth && localOneWay.orX0099.timeAxisWidth) {
    localOneWay.orX0099.atAnyTimeWidth =
      localOneWay.orX0099.evenGridWidth + localOneWay.orX0099.timeAxisWidth - 1
    localOneWay.orX0099.evenSlotHeaderWidth =
      localOneWay.orX0099.evenGridWidth + localOneWay.orX0099.timeAxisWidth
  } else if (localOneWay.orX0099.timeAxisWidth) {
    localOneWay.orX0099.atAnyTimeWidth = 31 + localOneWay.orX0099.timeAxisWidth - 1
    localOneWay.orX0099.evenSlotHeaderWidth = 31 + localOneWay.orX0099.timeAxisWidth
  } else if (localOneWay.orX0099.evenGridWidth) {
    localOneWay.orX0099.atAnyTimeWidth = localOneWay.orX0099.evenGridWidth + 60 - 1
    localOneWay.orX0099.evenSlotHeaderWidth = localOneWay.orX0099.evenGridWidth + 60
  }
}
/**************************************************
 * 表構造の構築
 **************************************************/
/**
 * ヘッダー
 */
const headerSlots = computed(() => {
  const slots = []
  for (const headerSlot of localOneWay.orX0099.headerSlots) {
    slots.push({
      headerName: headerSlot.headerName,
      width: headerSlot.width,
    })
  }
  return slots
})
/**
 * 第1列
 */
const firstColumnSlots = computed(() => {
  const slots = []
  for (const timeSlot of localOneWay.orX0099.firstSlots) {
    slots.push({
      columnName: timeSlot.columnName,
      borderBottom: timeSlot.borderBottom,
      headerBorderBottom: timeSlot.headerBorderBottom ?? '',
      labelShowFlg: timeSlot.labelShowFlg,
      padding: timeSlot.padding,
    })
  }
  return slots
})
/**
 * 第2列
 */
const secondColumnSlots = computed(() => {
  const slots = []
  for (const timeSlot of localOneWay.orX0099.firstSlots) {
    slots.push({
      columnName: timeSlot.columnName,
      borderBottom: timeSlot.borderBottom,
    })
  }
  return slots
})

/**************************************************
 * 表イベント
 **************************************************/
// 第1列ダブルクリックイベント
function firstColumnClick() {
  const result: DblClickResult = {
    clickType: 'firstSlot',
  }
  emit('doubleClick', result)
}
// 第2列ダブルクリックイベント
function secondColumnClick() {
  const result: DblClickResult = {
    clickType: 'secondSlot',
  }
  emit('doubleClick', result)
}
// セル内容ダブルクリックイベント
const itemDoubleClick = (lineIndex: number, rowIndex: number, atAnyTimeTimeFlg: string) => {
  const result: DblClickResult = {
    clickType: 'item',
    lineIndex: lineIndex,
    rowIndex: rowIndex,
    atAnyTimeTimeFlg: atAnyTimeTimeFlg,
  }
  emit('doubleClick', result)
}

/**
 * 結合されたdiv
 */
const splitEvents = computed<OrX0099SegmentedEvent[]>(() => {
  const segments: OrX0099SegmentedEvent[] = []
  if (localOneWay.orX0099.nonStandardFormatFlg) {
    local.realCalendar.events.forEach((event: OrX0099CalendarEvent) => {
      let currentStart = stringToDate(event.start)
      let endDate = stringToDate(event.end)
      const endArray = event.end.split(':')
      const endArrayHour = Number(endArray[0].split(' ')[1])
      const endArrayMinutes = Number(endArray[1])
      if (endArrayHour >= 24 && endArrayMinutes > 0) {
        endDate = stringToDate('2025-01-02' + ' ' + String(endArrayHour - 24).padStart(2, '0') + ':' + endArrayMinutes)
      }
      // 日をまたぐイベント処理
      while (currentStart < endDate && !event.atAnyTime) {
        const dayIndex = event.headerIndex
        let width = localOneWay.orX0099.headerSlots[dayIndex].width
        if (event.mergeHeaderIndex && event.mergeHeaderIndex.length > 0) {
          for (const index of event.mergeHeaderIndex) {
            width += localOneWay.orX0099.headerSlots[index].width
          }
        }
        if (localOneWay.orX0099.maxTime) {

          const maxTimeArray = localOneWay.orX0099.maxTime.split(':')
          const maxHour = Number(maxTimeArray[0])
          const maxMinutes = Number(maxTimeArray[1])
          if (maxHour === 24 && maxMinutes > 0) {
            const tablemaxTime = stringToDate('2025-01-02' + ' ' + '00' + ':' + maxMinutes)
            if (endDate >= tablemaxTime) {
              endDate = tablemaxTime
            }
          }
        }
        segments.push({
          ...event,
          segmentStart: formatDate(currentStart, false),
          segmentEnd: formatDate(endDate, false),
          dayIndex: dayIndex,
          customId: event.customId,
          orgParentCustomId: event.customId,
          width: width,
          showStar: false,
        })

        currentStart = endDate
        // 開始日を翌日の 0 時設定
        currentStart.setSeconds(currentStart.getSeconds() + 1)
      }
    })
  } else {
    local.realCalendar.events.forEach((event: OrX0099CalendarEvent) => {
      let currentStart = stringToDate(event.start)
      let endDate = stringToDate(event.end)
      // 日をまたぐイベント処理
      while (currentStart < endDate && !event.atAnyTime) {
        const dayIndex = event.headerIndex
        let width = localOneWay.orX0099.headerSlots[dayIndex].width
        if (event.mergeHeaderIndex && event.mergeHeaderIndex.length > 0) {
          for (const index of event.mergeHeaderIndex) {
            width += localOneWay.orX0099.headerSlots[index].width
          }
        }
        if (localOneWay.orX0099.maxTime) {

          const maxTimeArray = localOneWay.orX0099.maxTime.split(':')
          const maxHour = Number(maxTimeArray[0])
          const maxMinutes = Number(maxTimeArray[1])
          if (maxHour === 24 && maxMinutes > 0) {
            const tablemaxTime = stringToDate('2025-01-02' + ' ' + '00' + ':' + maxMinutes)
            if (endDate >= tablemaxTime) {
              endDate = tablemaxTime
            }
          }
        }
        segments.push({
          ...event,
          segmentStart: formatDate(currentStart, false),
          segmentEnd: formatDate(endDate, false),
          dayIndex: dayIndex,
          customId: event.customId,
          orgParentCustomId: event.customId,
          width: width,
          showStar: false,
        })

        currentStart = new Date(endDate)
        // 開始日を翌日の 0 時設定
        currentStart.setSeconds(currentStart.getSeconds() + 1)
      }
    })
  }
  if (localOneWay.orX0099.showStar === undefined || localOneWay.orX0099.showStar) {
    if (segments && segments.length > 0) {
      segments.forEach((element) => {
        const startDate = stringToDate(element.start)
        const endDate = stringToDate(element.end)
        const findInfo = segments.find(
          (item) =>
            item.customId !== element.customId &&
            item.headerIndex === element.headerIndex &&
            ((startDate <= stringToDate(item.start) && endDate > stringToDate(item.start)) ||
              (startDate >= stringToDate(item.start) && startDate < stringToDate(item.end)))
        )
        if (findInfo) {
          element.showStar = true
        }
      })
    }
  }
  return segments
})

/**
 * 結合されたdiv
 */
const atAnyTimeSplitEvents = computed<OrX0099SegmentedEvent[]>(() => {
  const segments: OrX0099SegmentedEvent[] = []
  local.realCalendar.events.forEach((event: OrX0099CalendarEvent) => {
    let currentStart = stringToDate(event.start)
    const endDate = stringToDate(event.end)
    if (event.atAnyTime) {
      const dayIndex = event.headerIndex
      let width = localOneWay.orX0099.headerSlots[dayIndex].width
      if (event.mergeHeaderIndex && event.mergeHeaderIndex.length > 0) {
        for (const index of event.mergeHeaderIndex) {
          width += localOneWay.orX0099.headerSlots[index].width
        }
      }
      segments.push({
        ...event,
        segmentStart: formatDate(currentStart, false),
        segmentEnd: formatDate(endDate, false),
        dayIndex: dayIndex,
        customId: event.customId,
        orgParentCustomId: event.customId,
        width: width,
      })

      currentStart = new Date(endDate)
      // 開始日を翌日の 0 時設定
      currentStart.setSeconds(currentStart.getSeconds() + 1)
    }
  })
  return segments
})
/**
 * オーバーレイdivの幅と高さの設定
 *
 * @param event - 日程
 */
const eventStyle = (event: OrX0099SegmentedEvent) => {
  const start = stringToDate(event.segmentStart)
  let end = stringToDate(event.segmentEnd)
  const endArray = event.segmentEnd.split(':')
  const endArrayHour = Number(endArray[0].split(' ')[1])
  const endArrayMinutes = Number(endArray[1])

  if (endArrayHour === 24 && endArrayMinutes > 0) {
    end = stringToDate('2025-01-02' + ' ' + '00' + ':' + endArrayMinutes)
  }

  const dayEnd = stringToDate('2025-01-01' + ' ' + '24:00')
  const tableStartTime = stringToDate('2025-01-01' + ' ' + localOneWay.orX0099.tableStartTime)
  const startmaxTime = new Date(
    stringToDate('2025-01-02' + ' ' + localOneWay.orX0099.tableStartTime).getTime() - 60 * 30 * 1000
  )
  const nextEndTime = stringToDate('2025-01-02' + ' ' + localOneWay.orX0099.tableStartTime)
  // 日程とタイトルの間の距離
  let startMinutes = 0
  let endMinutes = 0
  if (start >= startmaxTime) {
    startMinutes = 24 * 60 + startmaxTime.getHours() * 60 + startmaxTime.getMinutes()
  } else if (start >= dayEnd && startmaxTime >= dayEnd) {
    startMinutes = 24 * 60 + start.getHours() * 60 + start.getMinutes()
  } else if (start >= dayEnd) {
    startMinutes = 24 * 60
  } else {
    startMinutes = start.getHours() * 60 + start.getMinutes()
  }
  if (localOneWay.orX0099.maxTime) {
    const maxTimeArray = localOneWay.orX0099.maxTime.split(':')
    const maxHour = Number(maxTimeArray[0])
    const maxMinutes = Number(maxTimeArray[1])
    if (maxHour === 24 && maxMinutes > 0) {
      const tablemaxTime = stringToDate('2025-01-02' + ' ' + '00' + ':' + maxMinutes)
      if (end >= tablemaxTime) {
        endMinutes = 24 * 60  + tablemaxTime.getMinutes()
      } else if (end >= dayEnd && tablemaxTime >= dayEnd) {
        endMinutes = 24 * 60 + end.getHours() * 60 + end.getMinutes()
      } else if (end >= dayEnd) {
        endMinutes = 24 * 60
      } else {
        endMinutes = end.getHours() * 60 + end.getMinutes()
      }
    } else {
      const tablemaxTime = stringToDate('2025-01-01' + ' ' + localOneWay.orX0099.maxTime)
      if (end >= tablemaxTime) {
        endMinutes = 24 * 60  + tablemaxTime.getMinutes()
      } else if (end >= dayEnd && tablemaxTime >= dayEnd) {
        endMinutes = 24 * 60 + end.getHours() * 60 + end.getMinutes()
      } else if (end >= dayEnd) {
        endMinutes = 24 * 60
      } else {
        endMinutes = end.getHours() * 60 + end.getMinutes()
      }
    }

  } else {
    if (end >= nextEndTime) {
      endMinutes = 24 * 60 + nextEndTime.getHours() * 60 + nextEndTime.getMinutes()
    } else if (end >= dayEnd && nextEndTime >= dayEnd) {
      endMinutes = 24 * 60 + end.getHours() * 60 + end.getMinutes()
    } else if (end >= dayEnd) {
      endMinutes = 24 * 60
    } else {
      endMinutes = end.getHours() * 60 + end.getMinutes()
    }
  }

  let left = 0
  if (event.dayIndex > 0) {
    for (let i = 0; i < event.dayIndex; i++) {
      left += localOneWay.orX0099.headerSlots[i].width
    }
  }
  let width = localOneWay.orX0099.headerSlots[event.dayIndex].width
  if (event.mergeHeaderIndex && event.mergeHeaderIndex.length > 0) {
    for (const index of event.mergeHeaderIndex) {
      width += localOneWay.orX0099.headerSlots[index].width
    }
  }
  // 日程のスタイル設定
  const positionStyle = {
    zIndex: event.zIndex ?? 1000,
    top: `${((startMinutes - (tableStartTime.getHours() * 60 + tableStartTime.getMinutes())) / localOneWay.orX0099.minutes) * localOneWay.orX0099.cellheight}px`,
    height: `${((endMinutes - startMinutes) / localOneWay.orX0099.minutes) * localOneWay.orX0099.cellheight}px`,
    left: left + 'px',
    width: width + 'px',
    background: event.bgColor ?? '#e3f2fd',
    fontSize: event.fontSize ?? '12px',
    color: event.fontColor ?? 'black',
    textAlign: event.align ?? 'left',
    borderLeftColor: event.fontColor ?? '#2196f3',
    opacity: 1,
  }

  event.buttomTop =
    ((endMinutes - startMinutes) / localOneWay.orX0099.minutes) * localOneWay.orX0099.cellheight -
    10
  // 日程要素が移動中の透明度設定
  if (dragState.value.isDragging && event.id === dragState.value.currentEvent.id) {
    return {
      ...positionStyle,
      opacity: 1,
      zIndex: event.zIndex ?? 1000,
    }
  }
  return positionStyle as Record<string, string | number>
}

const returnEvent = (event: OrX0099SegmentedEvent) => {
  emit('mouseDown', event)
}

const atAnyTimeEventStyle = (event: OrX0099SegmentedEvent) => {
  let width = localOneWay.orX0099.headerSlots[event.dayIndex].width
  if (event.mergeHeaderIndex && event.mergeHeaderIndex.length > 0) {
    for (const index of event.mergeHeaderIndex) {
      width += localOneWay.orX0099.headerSlots[index].width
    }
  }

  let left =
    localOneWay.orX0099.atAnyTimeWidth && localOneWay.orX0099.atAnyTimeWidth > 0
      ? localOneWay.orX0099.atAnyTimeWidth
      : 85
  if (event.dayIndex > 0) {
    for (let i = 0; i < event.dayIndex; i++) {
      left += localOneWay.orX0099.headerSlots[i].width
    }
  }
  // 日程のスタイル設定
  const positionStyle = {
    zIndex: event.zIndex ?? 1000,
    top: '0px',
    height: (localOneWay.orX0099.atAllTimeHeight ?? 50) + 'px',
    left: left + 'px',
    width: width + 'px',
    background: event.bgColor ?? '#e3f2fd',
    fontSize: event.fontSize ?? '12px',
    color: event.fontColor ?? 'black',
    textAlign: event.align ?? 'left',
    borderLeftColor: event.fontColor ?? '#2196f3',
  }

  // 日程要素が移動中の透明度設定
  if (dragState.value.isDragging && event.id === dragState.value.currentEvent.id) {
    return {
      ...positionStyle,
      opacity: 1,
      zIndex: 1000,
    }
  }
  return positionStyle as Record<string, string | number>
}
// 日程移動状態管理
const dragState = ref<{
  isDragging: boolean
  originalX: number
  originalY: number
  originalTime: number
  originalDate: Date
  currentEvent: OrX0099SegmentedEvent
  containerRect?: DOMRect | null | { height: number; width: number }
}>({
  isDragging: false,
  originalX: 0,
  originalY: 0,
  originalTime: 0,
  originalDate: new Date(),
  currentEvent: {
    segmentStart: '',
    segmentEnd: '',
    dayIndex: 0,
    orgParentCustomId: '',
    id: 0,
    title: '',
    start: '',
    end: '',
    bgColor: '',
    headerIndex: 1,
    customId: '',
  },
  containerRect: {
    height: 0,
    width: 0,
  },
})

// 日程引き伸ばし状態管理
const resizeState = ref({
  isResizing: false,
  direction: '' as 'top' | 'bottom' | 'left' | 'right',
  originalY: 0,
  originalX: 0,
  originalTime: 0,
  currentEvent: null as OrX0099SegmentedEvent | null,
  srcWidth: 0,
  mouseEvent: {} as MouseEvent,
  clientWidth: 0,
})

/**
 * 日程要素移動イベント
 *
 * @param event - 日程
 *
 * @param e - マウス移動イベント
 */
const startDrag = async (event: OrX0099SegmentedEvent, e: MouseEvent) => {
  await nextTick()
  dragState.value.isDragging = false
  e.preventDefault()
  const rect = calendarContainer.value?.getBoundingClientRect()
  const startDate = stringToDate(event.start)
  dragState.value = {
    isDragging: true,
    originalX: e.clientX,
    originalY: e.clientY,
    originalTime: startDate.getTime(),
    originalDate: startDate,
    currentEvent: event,
    containerRect: rect,
  }

  window.addEventListener('mousemove', handleDrag)
  window.addEventListener('mouseup', stopDrag)
}

/**
 * 移動中リアルタイムで新日付
 *
 * @param e - マウスポインタ位置座標
 */
const handleDrag = (e: { clientX: number; clientY: number }) => {
  const deltaY = e.clientY - dragState.value.originalY
  // 新開始時間計算（境界制限を含む）
  const newStartDate = calculateNewDate(deltaY)
  const duration =
    stringToDate(dragState.value.currentEvent?.end).getTime() -
    stringToDate(dragState.value.currentEvent?.start).getTime()

  // 新終了時間計算（元の継続時間を維持）
  const newEndDate = new Date(newStartDate.getTime() + duration)
  // 日程時間制限処理
  const clampedDates = clampTimeRange(newStartDate, newEndDate)
  const currentEvent = dragState.value.currentEvent
  if (currentEvent.periodStart !== undefined && currentEvent.periodEnd !== undefined) {
    const width = Math.round((dragState.value.currentEvent.width ?? 14.28) / 14.28)
    const periodEndDate = new Date(clampedDates.end)
    periodEndDate.setDate(periodEndDate.getDate() + (width - 1))
    if (periodEndDate.getDay() === 1) {
      const beforeChangeClampedDatesEnd = new Date(clampedDates.end.getTime())
      beforeChangeClampedDatesEnd.setHours(0, 0, 0, 0)
      clampedDates.end.setDate(clampedDates.end.getDate())
      clampedDates.end.setHours(0, 0, 0, 0)
      clampedDates.start = new Date(beforeChangeClampedDatesEnd.getTime() - duration)
    }
  }

  const startMinutes = clampedDates.start.getMinutes()
  const endMinutes = clampedDates.end.getMinutes()
  // 時間を指定値で割った余りを計算
  const startY = startMinutes % localOneWay.orX0099.rollMinutes
  // 時間を指定値で割った整数部を取得
  const startS = Math.floor(startMinutes / localOneWay.orX0099.rollMinutes)
  // 余りが分割時間の閾値を超えるかどうかをチェック
  if (startY < 7) {
    clampedDates.start.setMinutes(startS * localOneWay.orX0099.rollMinutes)
    clampedDates.start.setSeconds(0)
    clampedDates.start.setMilliseconds(0)
  } else {
    clampedDates.start.setMinutes(startS * localOneWay.orX0099.rollMinutes)
    clampedDates.start.setSeconds(0)
    clampedDates.start.setMilliseconds(0)
  }

  const endY = endMinutes % localOneWay.orX0099.rollMinutes
  const endS = Math.floor(endMinutes / localOneWay.orX0099.rollMinutes)
  if (endY < 7) {
    clampedDates.end.setMinutes(endS * localOneWay.orX0099.rollMinutes)
    clampedDates.end.setSeconds(0)
    clampedDates.end.setMilliseconds(0)
  } else {
    clampedDates.end.setMinutes(endS * localOneWay.orX0099.rollMinutes)
    clampedDates.end.setSeconds(0)
    clampedDates.end.setMilliseconds(0)
  }
  void updateEventPosition(clampedDates.start, clampedDates.end)
}

/**
 * 日程カレンダーの日付範囲超えているか計算
 *
 * @param startDate - 開始日付
 *
 * @param endDate - 終了日付
 */
const clampTimeRange = (startDate: Date, endDate: Date) => {
  const clampedStart = startDate
  const clampedEnd = endDate
  const dayStart = stringToDate('2025-01-01' + ' ' + '00:00')
  dayStart.setHours(0, 0, 0, 0) // 当日の終了時間
  if (startDate < dayStart) {
    clampedStart.setHours(0, 0, 0, 0)
    clampedEnd.setTime(clampedStart.getTime() + (endDate.getTime() - startDate.getTime()))
  }
  // 開始日付制限
  if (clampedStart.getHours() < local.realCalendar.startHour) {
    clampedStart.setHours(local.realCalendar.startHour, 0, 0, 0)
    clampedEnd.setTime(clampedStart.getTime() + (endDate.getTime() - startDate.getTime()))
  }
  // 終了日付制限
  if (clampedEnd.getHours() >= local.realCalendar.endHour) {
    clampedEnd.setHours(local.realCalendar.endHour, 0, 0, 0)
    clampedStart.setTime(clampedEnd.getTime() - (endDate.getTime() - startDate.getTime()))
  }
  return { start: clampedStart, end: clampedEnd }
}

/**
 * 移動後新日付計算
 *
 * @param deltaY - マウス Y 軸相対距離
 */
const calculateNewDate = (deltaY: number) => {
  const { originalDate, containerRect } = dragState.value
  const newDate = new Date(originalDate)

  let speed = 1.4;
  if (localOneWay.orX0099.cellheight > 24) {
    speed = speed +  (localOneWay.orX0099.cellheight - 24) / 10
  }
  if (localOneWay.orX0099.cellheight < 24) {
    speed = speed - (24 - localOneWay.orX0099.cellheight) / 10
  }
  if (local.realCalendar.allCellNumber > 24) {
    speed = 4
  }
  const pixelsPerHour =
    (containerRect!.height / local.realCalendar.allCellNumber) *
    (localOneWay.orX0099.rollMinutes / localOneWay.orX0099.minutes) *
    speed

  const minuteDelta =
    Math.round(((deltaY / pixelsPerHour) * 60) / localOneWay.orX0099.rollMinutes) *
    localOneWay.orX0099.rollMinutes
  newDate.setMinutes(originalDate.getMinutes() + minuteDelta)
  return newDate
}

/**
 * 移動後の新日付割り当て
 *
 * @param newStart - 移動後の開始日付
 *
 * @param newEnd - 移動後の終了日付
 */
const updateEventPosition = async (newStart: Date, newEnd: Date) => {
  await nextTick()
  const originalEvent = local.realCalendar.events.find(
    (e) => e.customId === dragState.value.currentEvent?.orgParentCustomId
  )

  if (originalEvent) {
    originalEvent.start = formatDate(newStart, false)
    originalEvent.end = formatDate(newEnd, false)
  }
}

// 移動イベント停止
const stopDrag = () => {
  dragState.value.isDragging = false
  window.removeEventListener('mousemove', handleDrag)
  window.removeEventListener('mouseup', stopDrag)
}

// 日程ダブルクリックイベント
const showEventGui = (event: OrX0099SegmentedEvent, atAnyTimeTimeFlg: string) => {
  const result: DblClickResult = {
    clickType: 'item',
    lineIndex: event.dayIndex,
    rowIndex: -1,
    event: {
      ...event,
    },
    atAnyTimeTimeFlg: atAnyTimeTimeFlg,
  }
  emit('doubleClick', result)
}
/**
 * 日付書式化 yyyy-MM-dd HH:mm:ss OR HH:mm
 *
 * @param date - Date
 *
 * @param skipYm - 年月表示識別子(true: 表示しない, false: 表示する)
 */
function formatDate(date: Date, skipYm: boolean) {
  if (date === undefined || date === null) return ''
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  let day = String(date.getDate()).padStart(2, '0')
  let hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  if (localOneWay.orX0099.nonStandardFormatFlg) {
    if (Number(day) === 2) {
      hours = String(24 + Number(hours))
      day = String(Number(day) - 1).padStart(2, '0')
    }
  }
  if (!skipYm) {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } else {
    return `${hours}:${minutes}`
  }
}

/**
 * string to date
 *
 * @param date - date
 */
function stringToDate(date: string) {
  const returnDate = new Date(date)
  if (returnDate !== undefined && returnDate !== null && !isDate(returnDate)) return returnDate

  const arrayOne = date.split(' ')
  if (arrayOne === undefined || arrayOne === null || arrayOne.length < 2) {
    return new Date()
  }
  const arrayTwo = arrayOne[0].split('-')
  if (arrayTwo === undefined || arrayTwo === null || arrayTwo.length < 3) {
    return new Date()
  }
  const arrayThree = arrayOne[1].split(':')
  if (arrayThree === undefined || arrayThree === null || arrayThree.length < 2) {
    return new Date()
  }

  const year = arrayTwo[0]
  const month = arrayTwo[1]
  let day = Number(arrayTwo[2])
  let hour = Number(arrayThree[0])
  const minutes = Number(arrayThree[1])
  if ((hour === 24 && minutes > 0) || hour > 24) {
    day = day + 1
    hour = hour - 24
  }
  const returnDateString = year + '-' + month + '-' + String(day).padStart(2, '0') + ' ' + String(hour).padStart(2, '0') + ':' + String(minutes).padStart(2, '0') + ':' + '00'
  return new Date(returnDateString)
}
/**
 * マウス進入日程可移動エリア検出
 *
 * @param event - 日程
 *
 * @param e - マウスイベント
 */
const handleMouseMoveOnEvent = (event: OrX0099SegmentedEvent, e: MouseEvent) => {
  const rect = (e.target as HTMLElement).getBoundingClientRect()
  const mouseY = e.clientY - rect.top
  event.isResizingTop = mouseY < 10
  event.isResizingBottom = mouseY > rect.height - 10
}

/**
 * 日程移動、調整イベント処理分離
 *
 * @param event - 日程
 *
 * @param e - マウスイベント
 */
const handleEventMouseDown = (event: OrX0099SegmentedEvent, e: MouseEvent) => {
  if (event.isResizing) {
    e.stopPropagation()
    void startResize(event, resizeState.value.direction, e)
    return
  }
  localOneWay.zIndex += 1000
  local.calendar.events
    .filter((item) => item.id === event.id)
    .forEach((element) => {
      element.zIndex = localOneWay.zIndex
    })
  local.realCalendar.events
    .filter((item) => item.id === event.id)
    .forEach((element) => {
      element.zIndex = localOneWay.zIndex
    })
  void startDrag(event, e)
  // emit('mouseDown', event)
}

/**
 * 日程開始調整イベント
 *
 * @param event - 日程
 *
 * @param direction - 調整方向
 *
 * @param e - マウスイベント
 */
const startResize = async (
  event: OrX0099SegmentedEvent,
  direction: 'top' | 'bottom' | 'left' | 'right',
  e: MouseEvent
) => {

  localOneWay.zIndex += 1000
  local.calendar.events
    .filter((item) => item.id === event.id)
    .forEach((element) => {
      element.zIndex = localOneWay.zIndex
    })
  local.realCalendar.events
    .filter((item) => item.id === event.id)
    .forEach((element) => {
      element.zIndex = localOneWay.zIndex
    })

  await nextTick()
  resizeState.value = {
    isResizing: true,
    direction,
    originalY: e.clientY,
    originalX: e.clientX,
    originalTime:
      direction === 'top'
        ? stringToDate(event.segmentStart).getTime()
        : stringToDate(event.segmentEnd).getTime(),
    currentEvent: event,
    srcWidth: event.width ?? 14.28,
    mouseEvent: e,
    clientWidth: (e.target as HTMLElement).parentElement?.clientWidth ?? 0,
  }

  window.addEventListener('mousemove', handleResize)
  window.addEventListener('mouseup', stopResize)
}

/**
 * 日程時間調整
 *
 * @param e - マウスイベント
 */
const handleResize = (e: MouseEvent) => {
  if (!resizeState.value.isResizing || !resizeState.value.currentEvent) return
  e.preventDefault()
  const rect = calendarContainer.value?.getBoundingClientRect()
  let speed = 1.4;
  if (localOneWay.orX0099.cellheight > 24) {
    speed = speed +  (localOneWay.orX0099.cellheight - 24) / 10
  }
  if (localOneWay.orX0099.cellheight < 24) {
    speed = speed - (24 - localOneWay.orX0099.cellheight) / 10
  }
  if (local.realCalendar.allCellNumber > 24) {
    speed = 4
  }
  const pixelsPerHour =
    (rect!.height / local.realCalendar.allCellNumber) *
    (localOneWay.orX0099.rollMinutes / localOneWay.orX0099.minutes) *
    speed
  const deltaY = e.clientY - resizeState.value.originalY
  const hourDelta = deltaY / pixelsPerHour
  const minuteDelta =
    Math.round((hourDelta * 60) / localOneWay.orX0099.rollMinutes) * localOneWay.orX0099.rollMinutes

  const originalEvent = local.realCalendar.events.find(
    (e) => e.customId === resizeState.value.currentEvent?.orgParentCustomId
  )

  if (originalEvent) {
    const newDate = new Date(resizeState.value.originalTime)
    newDate.setMinutes(newDate.getMinutes() + minuteDelta)
    const clampedDate = clampTime(newDate)
    if (['top', 'bottom'].includes(resizeState.value.direction)) {
      if (resizeState.value.direction === 'top') {
        // 開始時間終了時間遅くならない
        if (clampedDate.getTime() < stringToDate(originalEvent.end).getTime()) {
          originalEvent.start = formatDate(clampedDate, false)
        }
      } else {
        // 終了時間開始時間早くならない
        if (clampedDate.getTime() > stringToDate(originalEvent.start).getTime()) {
          if (originalEvent.periodStart !== undefined && originalEvent.periodEnd !== undefined) {
            // 期間終了時間
            const periodEndDate = new Date(originalEvent.periodEnd)
            const days = getDaysBetween(stringToDate(originalEvent.start), clampedDate)
            const width = Math.round((originalEvent.width ?? 14.28) / 14.28 - 1)
            periodEndDate.setDate(originalEvent.periodStart.getDate() + width + days)
            periodEndDate.setHours(
              clampedDate.getHours(),
              clampedDate.getMinutes(),
              clampedDate.getSeconds(),
              clampedDate.getMilliseconds()
            )
            if (periodEndDate.getDay() === 1) {
              periodEndDate.setHours(0, 0, 0, 0)
              clampedDate.setHours(0, 0, 0, 0)
              originalEvent.end = formatDate(clampedDate, false)
            } else {
              originalEvent.end = formatDate(clampedDate, false)
            }
            originalEvent.periodEnd = periodEndDate
          } else {
            originalEvent.end = formatDate(clampedDate, false)
          }
        }
      }
    }
  }
}
/**
 * 2 つ日付間隔日数計算
 *
 * @param date1 - 日付1
 *
 * @param date2 - 日付2
 */
function getDaysBetween(date1: Date, date2: Date) {
  if (isNaN(date1.getTime()) ?? isNaN(date2.getTime())) {
    throw new Error('Invalid date')
  }
  const d1 = new Date(date1.getFullYear(), date1.getMonth(), date1.getDate())
  const d2 = new Date(date2.getFullYear(), date2.getMonth(), date2.getDate())
  // 時間差計算日数に変換
  const timeDiff = d2.getTime() - d1.getTime()
  const dayDiff = Math.abs(timeDiff / (1000 * 3600 * 24))
  return Math.floor(dayDiff)
}

/**
 * 日程カレンダー範囲超過計算
 *
 * @param date - 日付
 */
const clampTime = (date: Date): Date => {
  const clamped = date
  let minutes = clamped.getMinutes()
  minutes = Math.round(minutes / localOneWay.orX0099.rollMinutes) * localOneWay.orX0099.rollMinutes
  clamped.setMinutes(minutes, 0, 0)
  const hours = clamped.getHours()

  if (hours < local.realCalendar.startHour) {
    clamped.setHours(local.realCalendar.startHour, 0, 0, 0)
  } else if (hours > local.realCalendar.endHour) {
    clamped.setHours(local.realCalendar.endHour - 1, 59, 59, 999)
  }

  return clamped
}

/**
 * 日程時間調整終了処理
 */
const stopResize = () => {
  resizeState.value.isResizing = false
  window.removeEventListener('mousemove', handleResize)
  window.removeEventListener('mouseup', stopResize)
}

/**
 * カレンダー内の日程データ取得
 */
function getCalendarData() {
  const datas = JSON.parse(JSON.stringify(local.realCalendar.events)) as OrX0099CalendarEvent[]
  const rtnDatas = [] as OrX0099CalendarEvent[]
  for (const data of datas) {
    if (data.periodStart && data.periodEnd) {
      data.period = getDaysBetween(data.periodStart, data.periodEnd) + 1
    } else {
      data.period = 1
    }

    // 日付部分を除去し、時・分・秒のみを残す
    if (data.start && data.end) {
      data.start = data.start.split(' ')[1]
      data.end = data.end.split(' ')[1]
    }
    rtnDatas.push({
      id: data.id,
      customId: data.customId,
      title: data.title,
      start: data.start,
      end: data.end,
      bgColor: data.bgColor,
      headerIndex: data.headerIndex,
      align: data.align,
      fontSize: data.fontSize,
      fontColor: data.fontColor,
      period: data.period,
    })
  }
  return rtnDatas
}
/**
 * カレンダーの再編集
 */
async function reRenderCalendar() {
  await nextTick()
  window.removeEventListener('mousemove', handleDrag)
  window.removeEventListener('mouseup', stopDrag)
  window.removeEventListener('mousemove', handleResize)
  window.removeEventListener('mouseup', stopResize)
  init()
}
watch(
  () => props.removeflg,
  (newValue) => {
    local.removeflg = newValue
  },
  { deep: true }
)
watch(
  () => props.calendarConfig,
  (newValue) => {
    if (newValue) {
      local.calendar = newValue
    }
  },
  { deep: true }
)
watch(
  () => props.onewayModelValue.disabledFlg,
  (newValue) => {
    if (newValue) {
      localOneWay.orX0099.disabledFlg = newValue
    }
  },
  { deep: true }
)
/**
 * 入力するズーム比率監視
 */
watch(
  () => local.calendar?.zoom,
  (newValue) => {
    local.realCalendar.zoom = newValue
  },
  { deep: true }
)
defineExpose({
  getCalendarData,
  reRenderCalendar,
})

const startAllTimeDrag = async () => {
  await nextTick()
  dragState.value.isDragging = true
  document.addEventListener('mousemove', handleAllTimeMouseMove, false)
}
const handleAllTimeMouseMove = () => {
  document.addEventListener('mouseup', stopAllTracking, false)
}
const stopAllTracking = () => {
  if (dragState.value.isDragging) {
    dragState.value.isDragging = false
    emit('mouseMove', true)
    setTimeout(() => {
      document.removeEventListener('mousemove', handleAllTimeMouseMove, false)
      document.removeEventListener('mouseup', stopAllTracking, false)
    }, 1000)
  }
}
</script>
<template>
  <div
    class="calendar-container"
    :style="{
      '--custom-calendar-cell-height': `${localOneWay.orX0099.cellheight}px`,
      '--custom-calendar-zoom': `${local.realCalendar.zoom}`,
      width: `${localOneWay.orX0099.tableWidth}px`,
      height: `${localOneWay.orX0099.tableHeight}px`,
      overflow: `${localOneWay.orX0099.overFlowY}`,
    } "
  >
    <!-- header -->
    <div
      class="grid-header"
      :style="{
        height: `${localOneWay.orX0099.headerHeight} px`,
        borderBottom: local.removeflg ? 'none' : '1px solid #ddd'
      }">
      <div
        class="even-slot-header"
        :style="{
          width: `${localOneWay.orX0099.evenSlotHeaderWidth}px`,
          height: `${localOneWay.orX0099.headerHeight}px`,
        }"
      ></div>
      <div
        v-for="slots in headerSlots"
        :key="slots.headerName"
        class="day-header"
        :style="{
          width: `${slots.width}px`,
          height: `${localOneWay.orX0099.headerHeight}px`,
          borderBottom: !local.removeflg ? 'none' : '1px solid #ddd'

        }"
      >
        <div class="multiline">{{ slots.headerName }}</div>
      </div>
    </div>
    <div
      v-if="!local.removeflg"
      class="table-contant"
      :style="{
        height: `${localOneWay.orX0099Calendar.height}px`,
      }"
    >
      <!-- first -->
      <div>
        <div
          class="even-grid"
          :style="{
            width: `${localOneWay.orX0099.evenGridWidth}px`,
          }"
          @dblclick="!localOneWay.orX0099.disabledFlg ? firstColumnClick : null"
        >
          <div
            class="even-slot"
            :style="{
                '--row-number': localOneWay.orX0099.lateAtNightSize,
            }"
            style="height: calc(var(--custom-calendar-cell-height, 22px) * var(--row-number, 6)); "
          >
            深夜
          </div>
          <div
            class="even-slot"
            :style="{
              '--row-number': localOneWay.orX0099.morningSizeOne,
            }"
            style="height: calc(var(--custom-calendar-cell-height, 22px) * var(--row-number, 2))"
          >
            早朝
          </div>
          <div
            class="even-slot"
            :style="{
              '--row-number': localOneWay.orX0099.morningSizeTwo,
            }"
            style="height: calc(var(--custom-calendar-cell-height, 22px) * var(--row-number, 4))"
          >
            午前
          </div>
          <div
            class="even-slot"
            :style="{
              '--row-number': localOneWay.orX0099.afternoon,
            }"
            style="height: calc(var(--custom-calendar-cell-height, 22px) * var(--row-number, 6))"
          >
            午後
          </div>
          <div
            class="even-slot"
            :style="{
              '--row-number': localOneWay.orX0099.night,
            }"
            style="height: calc(var(--custom-calendar-cell-height, 22px) * var(--row-number, 4))"
          >
            夜間
          </div>
          <div
            class="even-slot"
            :style="{
              '--row-number': localOneWay.orX0099.lateAtNight,
            }"
            style="height: calc((var(--custom-calendar-cell-height, 22px) * var(--row-number, 2))) ;"
          >
            深夜
          </div>
        </div>
      </div>
      <!-- second -->
      <div>
        <!-- タイムライン -->
        <div
          class="time-axis"
          :style="{
            width: `${localOneWay.orX0099.timeAxisWidth}px`,
          }"
          @dblclick="!localOneWay.orX0099.disabledFlg ? secondColumnClick :null"
        >
          <div
            v-for="(slot, index2) in firstColumnSlots"
            :key="index2"
            class="time-slot"
            :style="{
              padding: slot.padding,
              'border-bottom': slot.headerBorderBottom,
              'font-size': localOneWay.orX0099.timeSize ?? '11px',
            }"
          >
            <span v-show="slot.labelShowFlg">
              {{ slot.columnName }}
            </span>
          </div>
        </div>
      </div>
      <!-- info -->
      <div class="week-grid">
        <!-- グリッド -->
        <div class="grid-body">
          <!--info main-->
          <div
            v-for="(headerSlot, indexrSlots) in headerSlots"
            :key="headerSlot.headerName"
            class="day-column"
            :style="{
               width: `${headerSlot.width}px`,
            }"
          >
            <div
              v-for="(slot, index2) in secondColumnSlots"
              :key="index2"
              class="time-cell"
              :style="{
                'border-bottom': slot.borderBottom,
              }"
              @dblclick="!localOneWay.orX0099.disabledFlg ?  itemDoubleClick(indexrSlots, index2, '0') : null"
            ></div>
          </div>
          <!--info div-->
          <div
            v-for="event in splitEvents"
            :key="event.id"
            class="calendar-event"
            :style="eventStyle(event)"
            @dblclick.stop="!localOneWay.orX0099.disabledFlg ?  showEventGui(event, '0') : null"
            @mouseup="!localOneWay.orX0099.disabledFlg ? returnEvent(event) : null"
            @mousedown="!localOneWay.orX0099.disabledFlg ? handleEventMouseDown(event, $event) : null"
            @mousemove="!localOneWay.orX0099.disabledFlg ?  handleMouseMoveOnEvent(event, $event) : null"
          >
            <!-- 調整に利用できるエリア -->
            <div
              class="resize-handle top"
              :style="{
                width: `${event.width}px`,
              }"
              @mousedown.stop="!localOneWay.orX0099.disabledFlg ? startResize(event, 'top', $event) : null"
            ></div>
            <div
              class="resize-handle bottom"
              :style="{
                width: `${event.width}px`,
                top: `${event.buttomTop}px`,
              }"
              @mousedown.stop="!localOneWay.orX0099.disabledFlg ? startResize(event, 'bottom', $event) : null"
            ></div>
            <div
              class="resize-handle right"
              @mousedown.stop="!localOneWay.orX0099.disabledFlg ? startResize(event, 'right', $event) : null"
            ></div>
            <div style="overflow: hidden !important; display: flex">
              <div
                v-for="(infoStr, index3) in event.info"
                :key="index3"
                :style="{
                  width: `${localOneWay.orX0099.headerSlots[infoStr.index].width}px`,
                  display: 'flex',
                }"
                class="multiline"
              >
                <span style="overflow: hidden !important">
                  <i v-if="infoStr.showTimeFlg === 1 || infoStr.showTimeFlg === 2" class="fa-regular fa-clock" :style="{fontSize: `${event.fontSize}`}"></i>
                  {{ index3 === 0 && event.showStar ? '★' : '' }}
                  {{
                    infoStr.showTimeFlg === 1
                      ? formatDate(stringToDate(event.start), true) +
                        '～' +
                        formatDate(stringToDate(event.end), true) +
                        infoStr.info
                      : infoStr.showTimeFlg === 2
                        ? infoStr.info +
                          formatDate(stringToDate(event.start), true) +
                          '～' +
                          formatDate(stringToDate(event.end), true)
                        : infoStr.info
                  }}
                </span>
              </div>
            </div>
              <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  content-class="tooltip-warp"
                  :width=eventStyle(event).width
                  :style="{
                    width: `${event.width}px`,
                  }"
                  open-delay="200"
                  display='flex'
                  >
                  <div style="overflow: hidden !important; display: flex">
                <div
                  v-for="(infoStr, index3) in event.info"
                  :key="index3"
                  :style="{
                    width: `${localOneWay.orX0099.headerSlots[infoStr.index].width}px`,
                    display: 'flex',
                  }"
                >
                  <span style="overflow: hidden !important">
                    {{ index3 === 0 && event.showStar ? '★' : '' }}
                    {{
                      infoStr.showTimeFlg === 1
                        ? formatDate(stringToDate(event.start), true) +
                          '～' +
                          formatDate(stringToDate(event.end), true) +
                          infoStr.info
                        : infoStr.showTimeFlg === 2
                          ? infoStr.info +
                            formatDate(stringToDate(event.start), true) +
                            '～' +
                            formatDate(stringToDate(event.end), true)
                          : infoStr.info
                    }}
                  </span>
                </div>
              </div>
            </c-v-tooltip>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="localOneWay.orX0099.showAllTime && !local.removeflg"
      class="at-any-time"
      :style="{
        height: `${localOneWay.orX0099.atAllTimeHeight ?? 50}px`,
      }"
    >
      <div
        class="at-any-time-even-slot-header"
        :style="{
          width: `${localOneWay.orX0099.atAnyTimeWidth }px`,
        }"
        style="padding: 6px;"
      >
      随時実施するサービス
      </div>
      <div
        v-for="(slots, headerIndex) in headerSlots"
        :key="slots.headerName"
        class="at-any-time-contant"
        :style="{
          width: `${slots.width}px`,
          height: `${localOneWay.orX0099.atAllTimeHeight ?? 50}px`,
        }"
        @dblclick="!localOneWay.orX0099.disabledFlg ? itemDoubleClick(headerIndex, -1, '0') : null"
      >
        <div
          class="at-any-time-time-cell "
          :style="{
            height: `${localOneWay.orX0099.atAllTimeHeight ?? 50}px`,
          }"></div>
        <div
          v-for="event in atAnyTimeSplitEvents"
          :key="event.id"
          class="calendar-event"
          :style="atAnyTimeEventStyle(event)"
          @dblclick.stop="!localOneWay.orX0099.disabledFlg ? showEventGui(event, '1') : null"
          @mousedown="!localOneWay.orX0099.disabledFlg ? startAllTimeDrag() : null"
        >
          <!-- 調整に利用できるエリア -->
          <div
            class="resize-handle top"
            :style="{
              width: `${event.width}px`,
            }"
            @mousedown.stop="!localOneWay.orX0099.disabledFlg ? startResize(event, 'top', $event) : null"
          ></div>
          <div
            class="resize-handle bottom"
            :style="{
              width: `${event.width}px`,
              top: `${event.buttomTop}px`,
            }"
            @mousedown.stop="!localOneWay.orX0099.disabledFlg ? startResize(event, 'bottom', $event) : null"
          ></div>
          <div
            class="resize-handle right"
            @mousedown.stop="!localOneWay.orX0099.disabledFlg ? startResize(event, 'right', $event) :null "
          ></div>
          <div style="overflow: unset !important; display: flex">
            <div
              v-for="(infoStr, index) in event.info"
              :key="index"
              :style="{
                width: `${localOneWay.orX0099.headerSlots[infoStr.index].width}px`,
                display: 'flex',
                overflow: 'hidden',
              }"
            >
              <i v-if=" infoStr.showTimeFlg === 1" class="fa-regular fa-clock" :style="{fontSize: `${event.fontSize}`}"></i>
              <div
                v-if="infoStr.showTimeFlg === 1"
                style="overflow: hidden"
              >
                {{
                  formatDate(stringToDate(event.start), true) +
                  '～' +
                  formatDate(stringToDate(event.end), true)
                }}
              </div>
              <div style="overflow: hidden">{{ infoStr.info }}</div>
              <div
                v-if="infoStr.showTimeFlg === 2"
                style="overflow: hidden"
              >
                {{
                  formatDate(stringToDate(event.start), true) +
                  '～' +
                  formatDate(stringToDate(event.end), true)
                }}
              </div>
            </div>
          </div>
              <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  content-class="tooltip-warp"
                  :width=eventStyle(event).width
                  :style="{
                    width: `${event.width}px`,
                  }"
                  open-delay="200"
                  display='flex'
                  >
                  <div style="overflow: hidden !important; display: flex">
                <div
                  v-for="(infoStr, index3) in event.info"
                  :key="index3"
                  :style="{
                    width: `${localOneWay.orX0099.headerSlots[infoStr.index].width}px`,
                    display: 'flex',
                  }"
                >
                  <span style="overflow: hidden !important">
                    {{ index3 === 0 && event.showStar ? '★' : '' }}
                    {{
                      infoStr.showTimeFlg === 1
                        ? formatDate(stringToDate(event.start), true) +
                          '～' +
                          formatDate(stringToDate(event.end), true) +
                          infoStr.info
                        : infoStr.showTimeFlg === 2
                          ? infoStr.info +
                            formatDate(stringToDate(event.start), true) +
                            '～' +
                            formatDate(stringToDate(event.end), true)
                          : infoStr.info
                    }}
                  </span>
                </div>
              </div>
            </c-v-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
/* カレンダーコンテナ */
.calendar-container {
  width: 867px;
  display: flex;
  position: relative;
  border-left: none;
  border-top: none;
  border-right: none;
  border-bottom: none;
  zoom: var(--custom-calendar-zoom, 1);
  flex-direction: column;
}
.calendar-container * {
  box-sizing: border-box;
}
.table-contant {
  display: flex;
  height: calc(var(--custom-calendar-cell-height, 22px) * 30);
}
/* タイムライン */
.time-axis {
  width: 60px;
  border-right: 1px solid #ddd;
  background: #f8f9fa;
  border-bottom: none;
  box-sizing: border-box;
  position: relative;
  height: auto;
  min-height: 100%;
}
/* 周期グリッド */
.even-grid {
  width: 31px;
  padding-top: 0px;
  border-right: 1px solid #ddd;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
}
/* タイムライン */
.even-slot-header {
  width: 91px;
  justify-content: center;
  align-items: center;
  border-bottom: none;
  border-right: 1px solid #ddd;
  border-top: none;
  border-left: none;
  writing-mode: vertical-rl;
}
/* タイムライン */
.even-slot {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ddd;
  border-left: 1px solid #ddd;
  writing-mode: vertical-rl;
}

/* タイムライン */
.time-slot {
  height: var(--custom-calendar-cell-height, 22px);
  font-size: 11px;
  color: #666;
  padding: 0 8px 0 0;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  position: relative;
  border: none;
  top: 0px;
}
/* 曜日周期グリッド */
.week-grid {
  flex: 1;
  min-width: 0;
}
/* 曜日タイトル */
.grid-header {
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  border-bottom: 1px solid #ddd;
  box-sizing: border-box;
}

.day-header {
  text-align: center;
  padding: 4px;
  border-right: 1px solid #ddd;
  border-top: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #f8f9fa;
}

.day-header:last-child {
  border-right: 1px solid #ddd;
}

.grid-body {
  position: relative;
  display: flex;
  will-change: transform;
  cursor: pointer;
}

.day-column {
  border-right: 1px solid #ddd;
  position: relative;
  min-height: 100%;
}
/* タイムセル */
.time-cell {
  height: var(--custom-calendar-cell-height, 22px);
  border-bottom: 1px solid #ddd;
  box-sizing: border-box;
  position: relative;
  background: #FFFFFF;
}

/* 日程 */
.calendar-event {
  position: absolute;
  background: #e3f2fd;
  border: 0px solid #90caf9;
  border-left: 3px solid #2196f3;
  border-radius: 4px;
  padding: 4px;
  overflow: hidden;
  font-size: 0.9em;
  cursor: move;
  box-sizing: border-box;
  transition: box-shadow 0.2s;
  touch-action: none;
  user-select: none;
  transition: opacity 0.2s;
  text-align: left;
  word-wrap: break-word;
}
/* 日程移動際のスタイル */
.calendar-event.dragging {
  cursor: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.resize-handle {
  position: absolute;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.02s;
}

.resize-handle:hover {
  opacity: 1;
  background: rgba(33, 150, 243, 0.3);
}

.resize-handle.top,
.resize-handle.bottom {
  left: 0;
  right: 0;
  height: 10px;
  opacity: 0.0;
  background: rgb(247, 248, 248);
}

.resize-handle.left {
  top: 0;
  bottom: 0;
  width: 4px;
}

.resize-handle.right {
  top: 0;
  bottom: 0;
  width: 0px;
}
/* 上に調整するハンドル */
.resize-handle.top {
  top: 0;
  cursor: ns-resize;
}
/* 下に調整するハンドル */
.resize-handle.bottom {
  bottom: 0;
  cursor: ns-resize;
}
/* 右に調整するハンドル */
.resize-handle.right {
  right: 0;
  cursor: ns-resize;
}
/* 最後のタイムライン */
.time-slot:last-child {
  top: 0px;
}

.at-any-time {
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  height: 50px;
  border-bottom: 1px solid #ddd;
  border-left: 1px solid #ddd;
  box-sizing: border-box;
}

.at-any-time-even-slot-header {
  width: 84px;
  justify-content: center;
  align-items: center;
  border-top: none;
  border-right: 1px solid #ddd;
  background: #f8f9fa;
}

.at-any-time-contant {
  display: flex;
  height: 50px;
  border-right: 1px solid #ddd;
  cursor: pointer;
  background: #FFFFFF;
  border-bottom: 1px solid #ddd;
}

.at-any-time-time-cell {
  height: 50px;
  border-bottom: 1px solid #ddd;
  box-sizing: border-box;
  background-repeat: no-repeat;
}
.multiline {
  white-space: pre-line;
  display: block;
}

</style>
