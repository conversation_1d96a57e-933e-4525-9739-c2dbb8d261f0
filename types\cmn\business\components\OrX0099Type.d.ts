/**
 * 双方向
 */
export interface OrX0099Type {
  /** ダブルクリック戻り値 */
  dblClickResult: DblClickResult
  /** 日期 */
  day: string
  /** 開始時刻 */
  start: string
  /** 終了時刻 */
  end: string
  /** 表示情報 */
  value: string
  /** 表示情報 */
  moveType: boolean
}

/**
 * 单方向
 */
export interface OrX0099OnewayType {
  /** 表の頭height */
  headerHeight?: number
  /**  表の頭 */
  headerSlots: HeaderSlot[]
  /** 刻度 目盛 */
  firstSlots: ColumnCell[]
  /** テーブル幅 */
  tableWidth: number
  /** 深夜Size */
  lateAtNightSize: number
  /** 早朝Size */
  morningSizeOne: number
  /** 午前Size */
  morningSizeTwo: number
  /** 午後ize */
  afternoon: number
  /** 夜間ize */
  night: number
  /** 深夜size */
  lateAtNight: number
  /** 間隔時間 */
  minutes: number
  /** スクロール時間 */
  rollMinutes: number
  /** セル高さ */
  cellheight: number
  /** overFlowY */
  overFlowY: string
  /** tableHeight */
  tableHeight: number
  /** tableStartTime */
  tableStartTime: string
  /** showAllTime */
  showAllTime: boolean
  /** evenGridWidth */
  evenGridWidth?: number
  /** timeAxisWidth */
  timeAxisWidth?: number
  /** atAnyTimeWidth */
  atAnyTimeWidth?: number
  /**  evenSlotHeaderWidth */
  evenSlotHeaderWidth?: number
  /**  evenSlotHeaderWidth */
  showStar?: boolean
  /** 操作区分 */
  disabledFlg?: boolean
  /** timeSize*/
  timeSize?: string
  /** atAllTimeHeight */
  atAllTimeHeight?: number
  /** addMinutes*/
  addMinutes?: number
  /** addMinutes*/
  maxTime?: string
  /** nonStandardFormatFlg */
  nonStandardFormatFlg?: boolean
}

/**
 * ヘッダー
 */
export interface HeaderSlot {
  /** ヘッダー名 */
  headerName: string
  /** ヘッダーindex */
  index: number
  /** ヘッダー宽 */
  width: number
}

/**
 * カラム
 */
export interface ColumnCell {
  /** カラム名 */
  columnName: string
  /** 表下枠スタイル */
  headerBorderBottom?: string
  /** 表下枠スタイル */
  borderBottom: string
  /** カラムindex */
  index: number
  /** ラベル表示有無 */
  labelShowFlg: boolean
  /** padding :0px 0px 0px 0px */
  padding: string
  /** 時間 */
  time?: string
}

/**
 * ダブルクリックイベントの戻りパラメータ
 */
export interface DblClickResult {
  /**
   *ダブルクリックタイプ
   *ヘッダー:header
   *第一列:firstSlot
   *第二列:secondSlot
   *表コンテンツ:item
   */
  clickType: string
  /** ダブルクリックタイプ 所在列*/
  lineIndex?: number
  /** 所在行*/
  rowIndex?: number
  /**  随時 */
  atAnyTimeTimeFlg?: string
  /** 日をまたぐ分割に用いる日程ブロック */
  event?: OrX0099SegmentedEvent
}

/** カレンダーの設定 */
export interface OrX0099CalendarConfig {
  /** 日程リスト */
  events: OrX0099CalendarEvent[]
  /** セルの高さ */
  cellHeight: number
  /** ズーム */
  zoom: number
  /** タイムラインの開始時間 */
  startHour: number
  /** タイムラインの終了時間 */
  endHour: number
  /** 総行数 */
  allCellNumber: number
}

/**
 * OrX0099:カレンダー(0,2,4,···,24)構造
 * カレンダー
 *
 * @description
 * 日程ブロック
 *
 * <AUTHOR>
 */
export interface OrX0099CalendarEvent {
  /** 日程ブロックID */
  id: number
  /** タイトル */
  title: string
  /** 日程開始時間 */
  start: string
  /** 日程終了時間 */
  end: string
  /** 日程ブロック背景色 */
  bgColor: string
  /** 列index */
  headerIndex: number
  /** 日程内容エリア揃い方向 */
  align?: string
  /** フォントサイズ */
  fontSize?: string
  /** フォント色 */
  fontColor?: string
  /** 間隔日数 */
  period?: number
  /** 親id */
  tableWidth?: number
  /** 日程信息 */
  info?: OrX0099Info[]
  /** 日程ブロック長さ */
  width?: number
  /** 日程ブロック左側の浮動距離 */
  left?: number
  /** 期間開始日付 */
  periodStart?: Date
  /** 期間終了日付 */
  periodEnd?: Date
  /** カスタムID */
  customId: string
  /** 結合が必要な列 */
  mergeHeaderIndex?: number[]
  /** 随時実施するサービス */
  atAnyTime?: boolean
  /** 表示レイヤー */
  zIndex?: number
}

/** 日をまたぐ分割に用いる日程ブロック */
export interface OrX0099SegmentedEvent extends OrX0099CalendarEvent {
  /** 分割日程の開始時間 */
  segmentStart: string
  /** 分割日程の終了時間 */
  segmentEnd: string
  /** 曜日の下付き文字 */
  dayIndex: number
  /** 親ID */
  orgParentCustomId: string
  /** 上に引き伸ばすかどうかの識別子 */
  isResizingTop?: boolean
  /** 下に引き伸ばすかどうかの識別子 */
  isResizingBottom?: boolean
  /** 引き伸ばし状態にあるかどうかの識別子 */
  isResizing?: boolean
  /** ドラッグダウン長*/
  buttomTop?: number
  /** 五芒星 */
  showStar?: boolean
}

/** OrX0099Info */
export interface OrX0099Info {
  /** index */
  index: number
  /** info */
  info: string
  /** 0 ない 1 先頭 2 末尾 */
  showTimeFlg: number
}
