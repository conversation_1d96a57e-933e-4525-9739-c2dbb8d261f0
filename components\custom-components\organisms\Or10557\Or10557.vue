<script setup lang="ts">
/**
 * Or10557有機体:共通サービスマスタモーダル
 * GUI01055_共通サービスマスタ
 *
 * @description
 * 共通サービスマスタ画面
 *
 * <AUTHOR>
 */

import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28980Const } from '../Or28980/Or28980.constant'
import { Or06618Const } from '../Or06618/Or06618.constants'
import type { AsyncFunction, Or10557StateType } from './Or10557.type'
import { Or10557Const } from './Or10557.constant'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { hasRegistAuth, useScreenOneWayBind, useSetupChildProps } from '#build/imports'
import { useGyoumuCom } from '~/utils/useGyoumuCom'
import type {
  Or10557OneWayType,
} from '~/types/cmn/business/components/Or10557Type'
import type {
  CommonServiceMasterListHeader,
  CommonServiceMasterListItem,
  Or28980Type,
} from '~/types/cmn/business/components/Or28980Type'
import type { Or06618OneWayType } from '~/types/cmn/business/components/Or06618Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or21814EventType, Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Or21815StateType, Or21815EventType } from '~/components/base-components/organisms/Or21815/Or21815.type'

/**
 * useI18n
 */
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or10557OneWayType
}
/**
 * props
 */
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * or28980:有機体:(内容マスタ)内容情報テーブル
 */
const or28980 = ref({ uniqueCpId: Or28980Const.CP_ID(0) })
/**
 * Or06618:有機体:(日課計画マスタ)コンテンツエリアタブ
 */
const or06618 = ref({ uniqueCpId: Or06618Const.CP_ID(0) })
/**
 * ローカルTwoway
 */
const local = reactive({
  // 本画面ダイアログ
  mo00024: {
    isOpen: Or10557Const.DEFAULT.IS_OPEN,
  } as Mo00024Type,
  mo00043: { id: Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER } as Mo00043Type,
  mo00043Transfer: { id: Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER } as Mo00043Type,
  mo00043Change: { id: Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER } as Mo00043Type,
  // 内容情報テーブル一覧
  or28980: {
    headers: [] as CommonServiceMasterListHeader[],
    items: [] as CommonServiceMasterListItem[],
  } as Or28980Type,
})

/**
 * ローカルOneway
 */
const localOneway = reactive({
  // 本画面
  or10557Oneway: {
    ...props.onewayModelValue,
  },
  or06618OneWay: { dailyScheduleMasterInData: {} } as Or06618OneWayType,
  // 本画面ダイアログOneway
  mo00024Oneway: {
    width: '900px',
    height: '850px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Or10557',
      toolbarTitle: t('label.common-service-master'),
      toolbarTitleCenteredFlg: false,
      toolbarName: '',
      showCardActions: true,
      cardTextClass: 'card-text pa-0',
    },
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609SaveOneway: {
    btnLabel: t('btn.save'),
    // disabled: !(await hasRegistAuth(Or10557Const.LINK_AUTH)),
  } as Mo00609OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE, title: t('label.daily-care-plan') },
      {
        id: Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER,
        title: t('label.common-service-master'),
      },
    ],
  } as Mo00043OnewayType,
  mo01338Fourway: {
    value: t('label.preserved-by-each-office'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-0',
      itemClass: 'ml-0 align-left',
      itemStyle: 'color:  rgb(var(--v-theme-black-500));',
    }),
  } as Mo01338OnewayType,
})
/**
 * 有機体:(共通サービスマスタ)内容情報テーブルRef
 */
const or28980Ref = ref<{ insert: AsyncFunction; _cleanEditFlag: () => void; _getGetEditFlgFlag: AsyncFunction; getInitDataInfo: () => void}>()
/**
 * 有機体:(日課計画マスタ)コンテンツエリアタブ Ref
 */
const or06618Ref = ref<{ insert: AsyncFunction; _cleanEditFlag: () => void; _getGetEditFlgFlag: AsyncFunction; fetchInitData: () => void}>()
/**************************************************
 * Pinia
 **************************************************/
/**
 * gyoumuCom
 */
const gyoumuCom = useGyoumuCom()
const or21814_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })

useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
  [Or28980Const.CP_ID(1)]: or28980.value,
  [Or06618Const.CP_ID(1)]: or06618.value,
})
/**
 * OneWayBind領域に関する処理
 */
const { setState } = useScreenOneWayBind<Or10557StateType>({
  cpId: Or10557Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or10557Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**************************************************
 * ウォッチャー
 **************************************************/
onMounted(() => {
  // コントロール設定
  local.mo00043.id = Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER
  localOneway.or06618OneWay.dailyScheduleMasterInData.shisetuId =
    localOneway.or10557Oneway.commonServiceMasterInData.shisetuId
  localOneway.or06618OneWay.dailyScheduleMasterInData.svJigyoId =
    localOneway.or10557Oneway.commonServiceMasterInData.svJigyoId
  localOneway.or06618OneWay.dailyScheduleMasterInData.svJigyoIdList =
    localOneway.or10557Oneway.commonServiceMasterInData.svJigyoIdList
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => local.mo00024.emitType,
  async () => {
    if (local.mo00024.emitType === 'closeBtnClick') {
      await close()
      local.mo00024.emitType = undefined
    }
  }
)
/**************************************************
 * 関数
 **************************************************/
// メニュー切替
watch(
  () => local.mo00043Transfer.id,
  async (newValue) => {
    if (local.mo00043Transfer.id !== local.mo00043.id) {
      local.mo00043Transfer.id = local.mo00043.id
      local.mo00043Change.id = newValue
      let auth = Or10557Const.LINK_AUTH_COMMON_SERVICE_MASTER
      if (local.mo00043.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
        auth = Or10557Const.LINK_AUTH_DAILY_CARE_PLANE
      } else if(local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER) {
        auth = Or10557Const.LINK_AUTH_COMMON_SERVICE_MASTER
      }
      if (
        !(await gyoumuCom.checkEdit(
          await editFlg(),
          await hasRegistAuth(auth),
          showConfirmMessageBox,
          showWarnMessageBox,
          insert,
          cancel
        ))
      ) {
        return
      }
      cleanEditFlag()
      // キャンセル選択時は複写データの作成を行わずに終了する
      local.mo00043.id = local.mo00043Change.id
      local.mo00043Transfer.id = local.mo00043Change.id
      localOneway.mo00024Oneway.mo01344Oneway.name = local.mo00043Transfer.id
      switchTitle();
      localOneway.mo00609SaveOneway.disabled = !await hasRegistAuth(auth)
    }
  }
)
function cancel() {
  // キャンセル選択時は複写データの作成を行わずに終了する
  local.mo00043Change.id =  local.mo00043.id
  local.mo00043Transfer.id = local.mo00043Change.id
  localOneway.mo00024Oneway.mo01344Oneway.name = local.mo00043Transfer.id
  switchTitle()
}

function switchTitle() {
  if (local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.daily-care-plan')
    or06618Ref.value?.fetchInitData()
  } else if (local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER) {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t(
      'label.common-service-master'
    )
    or28980Ref.value?.getInitDataInfo()
  }
}

/**
 * 保存
 */
async function save() {
  await insert()
  if (local.mo00043.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
    or06618Ref.value?.fetchInitData()
  } else if(local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER) {
    or28980Ref.value?.getInitDataInfo()
  }
}
/**
 * 保存
 */
async function insert() {
  // 画面入力データ変更があるかどうかを判定する
  // はい選択時は入力内容を保存する
  if (local.mo00043.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
    return await or06618Ref.value?.insert() ?? true
  } else if(local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER) {
    return await or28980Ref.value?.insert() ?? true
  }
  return true
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  let auth = Or10557Const.LINK_AUTH_COMMON_SERVICE_MASTER
  if (local.mo00043.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
    auth = Or10557Const.LINK_AUTH_DAILY_CARE_PLANE
  } else if(local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER) {
    auth = Or10557Const.LINK_AUTH_COMMON_SERVICE_MASTER
  }
  if (
    !(await gyoumuCom.checkEdit(
      await editFlg(),
      await hasRegistAuth(auth),
      showConfirmMessageBox,
      showWarnMessageBox,
      insert
    ))
  ) {
    return
  }
  cleanEditFlag()
  setState({ isOpen: false })
}

/**
 * メッセージを表示する
 */
const showConfirmMessageBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * 確認ダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}
/**
 * メッセージを表示する
 */
const showWarnMessageBox = async () => {
  return await openWarnDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.w-com-10006'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
}

/**
 * メッセージを表示する
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openWarnDialog(state: Or21815StateType): Promise<Or21815EventType | undefined> {
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

function cleanEditFlag() {
  if (local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
    or06618Ref.value?._cleanEditFlag()
  } else if (local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER) {
    or28980Ref.value?._cleanEditFlag()
  }
}
/**
 * ナビゲーション制御領域のいずれかの編集フラグがON
 */
const editFlg = async function getGetEditFlgFlag() {
  if (local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
    return await or06618Ref.value?._getGetEditFlgFlag() ?? false
  } else if (local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER) {
    return await or28980Ref.value?._getGetEditFlgFlag() ?? false
  }
  return false
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043Transfer"
        :oneway-model-value="localOneway.mo00043OneWay"
        style="margin: 0px !important; padding: 0px !important;  height: 36px !important;"
      ></base-mo00043>
      <c-v-window v-model="local.mo00043.id" style="margin: 0px !important; padding: 0px !important;" >
        <c-v-window-item value="GUI01056" style="margin: 0px !important; padding: 0px !important;"
          ><c-v-row
            ><c-v-col
              ><g-custom-or06618
                v-if="local.mo00043.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE"
                ref="or06618Ref"
                v-bind="or06618"
                :oneway-model-value="localOneway.or06618OneWay"
                :unique-cp-id="or06618.uniqueCpId"
              /> </c-v-col
          ></c-v-row>
        </c-v-window-item>
        <c-v-window-item value="GUI01055"  style="margin: 0px !important; padding: 0px !important;">
          <c-v-row>
            <c-v-col >
              <g-custom-or28980
                v-if="local.mo00043.id === Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER"
                v-bind="or28980"
                ref="or28980Ref"
                :unique-cp-id="or28980.uniqueCpId"
                :parent-unique-cp-id="props.uniqueCpId"
              >
              </g-custom-or28980></c-v-col
          ></c-v-row>
        </c-v-window-item>
      </c-v-window>
      <c-v-row
        no-gutters
        class="label-comment"
      >
        <c-v-col style="padding: 8px 8px 8px 8px !important">
          <base-mo01338 :oneway-model-value="localOneway.mo01338Fourway" /> </c-v-col
      ></c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-0 mr-2"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609SaveOneway"
          class="mx-0 mr-0"
          @click="save"
        >
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- スロットの使用例 -->
    <g-base-or21814 v-bind="or21814_1"/>
    <g-base-or21815 v-bind="or21815_1"/>
  </base-mo00024>
</template>

<style scoped lang="scss">
.label-comment {
  color: rgb(var(--v-theme-black-536)) !important;
  margin: 0px 0px 0px 0px;
  height: 15px;
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: 88px;
}
</style>
