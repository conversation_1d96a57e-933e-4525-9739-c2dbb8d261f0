import { Or41172Const } from '~/components/base-components/organisms/Or41172/Or41172.constants'
import { Gui00020Const } from '~/components/custom-components/organisms/Gui00020/Gui00020.constants'
import { Gui00021Const } from '~/components/custom-components/organisms/Gui00021/Gui00021.constants'
import { Gui00026Const } from '~/components/custom-components/organisms/Gui00026/Gui00026.constants'
import { Gui00027Const } from '~/components/custom-components/organisms/Gui00027/Gui00027.constants'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or41164Const } from '../Or41164/Or41164.constants'
import { Or41171Const } from '../Or41171/Or41171.constants'
import { Or41173Const } from '../Or41173/Or41173.constants'
import { Or41174Const } from '../Or41174/Or41174.constants'
import { Or41175Const } from '../Or41175/Or41175.constants'
import { Or41176Const } from '../Or41176/Or41176.constants'
import { Or00251Const } from './Or00251.constants'
import type { Or00251StateType } from './Or00251.type'

/**
 * Or00251：有機体：画面メニュー保存ボタン
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 */
export namespace Or00251Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or00251Const.CP_ID(1),
      uniqueCpId,
      childCps: [
        { cpId: Or41164Const.CP_ID(1) },
        { cpId: Or41171Const.CP_ID(1) },
        { cpId: Or41172Const.CP_ID(1) },
        { cpId: Or41173Const.CP_ID(1) },
        { cpId: Or41174Const.CP_ID(1) },
        { cpId: Or41175Const.CP_ID(1) },
        { cpId: Or41176Const.CP_ID(1) },
        { cpId: Gui00021Const.CP_ID },
        { cpId: Gui00026Const.CP_ID(1) },
        { cpId: Gui00027Const.CP_ID(1) },
        { cpId: Gui00020Const.CP_ID },
      ],

      // 編集フラグ不要
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    // Or41164Logic.initialize(childCpIds[Or41164Const.CP_ID(1)].uniqueCpId)
    // Or41171Logic.initialize(childCpIds[Or41171Const.CP_ID(1)].uniqueCpId)
    // Or41172Logic.initialize(childCpIds[Or41172Const.CP_ID(1)].uniqueCpId)
    // Or41173Logic.initialize(childCpIds[Or41173Const.CP_ID(1)].uniqueCpId)
    // Or41174Logic.initialize(childCpIds[Or41174Const.CP_ID(1)].uniqueCpId)
    // Or41175Logic.initialize(childCpIds[Or41175Const.CP_ID(1)].uniqueCpId)
    // Or41176Logic.initialize(childCpIds[Or41176Const.CP_ID(1)].uniqueCpId)
    // Gui00021Logic.initialize(childCpIds[Gui00021Const.CP_ID].uniqueCpId)
    // Gui00026Logic.initialize(childCpIds[Gui00026Const.CP_ID(1)].uniqueCpId)
    // Gui00027Logic.initialize(childCpIds[Gui00027Const.CP_ID(1)].uniqueCpId)
    // Gui00020Logic.initialize(childCpIds[Gui00020Const.CP_ID].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or00251StateType>(Or00251Const.CP_ID(1))
}
