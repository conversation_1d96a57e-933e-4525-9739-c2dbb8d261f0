<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isNil, trim } from 'lodash'
import { Or28543Const } from './Or28543.constants'
import type { Or28543StateType, TableData } from './Or28543.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01278Type, Mo01278OnewayType } from '~/types/business/components/Mo01278Type'
import type {
  Or28543Type,
  Or28543OnewayType,
  ShowData,
} from '~/types/cmn/business/components/Or28543Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'

/**
 * Or28543:有機体:表示順変更計画表モーダル
 * GUI01112_表示順変更計画表
 *
 * @description
 * 表示順変更計画表モーダル
 *
 * <AUTHOR> NGUYEN VAN PHONG
 */

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or28543Type
  onewayModelValue: Or28543OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const localOneway = reactive({
  Or28543: {
    ...props.onewayModelValue,
  },
  // 情報ダイアログ
  mo00024Oneway: {
    width: 'auto',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or28543',
      toolbarTitle: t('label.display-order-change-schedule'),
      toolbarName: 'Or28543ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00043OneWay: {
    tabItems: [
      {
        id: 'change',
        title: t('label.display-order-modified'),
        tooltipText: t('label.display-order-modified'),
      },
      {
        id: 'move',
        title: t('label.display-order-move'),
        tooltipText: t('label.display-order-move'),
      },
    ],
  } as Mo00043OnewayType,
  // はいボタン
  mo01265OnewayModelValue: {
    btnLabel: t('btn.delete-display-order'),
    tooltipText: t('tooltip.display-order-delete-success'),
  } as Mo01265OnewayType,
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  mo00009Oneway: {
    btnIcon: 'dialpad',
    variant: 'flat',
    labelColor: 'red',
    rounded: '0',
    minWidth: '20px',
    minHeight: '20px',
  } as Mo00009OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28543StateType>({
  cpId: Or28543Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28543Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28543Const.DEFAULT.IS_OPEN,
})

const or21814 = ref({ uniqueCpId: '' })

const local = reactive({
  or28543: {
    ...props.onewayModelValue,
    headers: {},
    isAddColumnMove: false,
    heightDialog: '600px' as string
  },
  mo00043: { id: 'change' } as Mo00043Type,
})

// 表示順変更テーブル情報
const tableData = ref<TableData[]>([])

// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
})
const tableDateMove = ref<TableData[]>([])
const columnMinWidth = ref<number[]>([78])
// 表示順変更テーブルヘッダ
const changeTableHeadersModeA43 = [
  {
    title: t('label.display-order'),
    key: 'sort',
    align: 'left',
    minWidth: '78px',
    sortable: false,
  },
  {
    title: t('label.support-point-for-goal'),
    key: 'support-point-for-goal',
    align: 'left',
    sortable: false,
    width: '125px',
  },
  {
    title: t('label.support-detail-for-person'),
    key: 'support-detail-for-person',
    align: 'left',
    width: '280px',
    sortable: false,
  },
  {
    title: t('label.asterisk-1'),
    key: 'asterisk-1',
    align: 'left',
    width: '45px',
    sortable: false,
  },
  {
    title: t('label.service-type'),
    key: 'service-type',
    align: 'left',
    width: '130px',
    sortable: false,
  },
  {
    title: t('label.service-provider'),
    key: 'service-provider',
    align: 'left',
    width: '130px',
    sortable: false,
  },
  {
    title: t('label.frequency'),
    key: 'frequency',
    align: 'left',
    width: '115px',
    sortable: false,
  },
  {
    title: t('label.period'),
    key: 'period',
    align: 'left',
    width: '115px',
    sortable: false,
  },
]

// 表示順変更テーブルヘッダ
const changeTableHeadersModeA3 = [
  {
    title: t('label.display-order'),
    key: 'sort',
    align: 'left',
    width: '80px',
    sortable: false,
  },
  {
    title: t('label.support-point-for-goal'),
    key: 'support-point-for-goal',
    align: 'left',
    sortable: false,
    width: '130px',
  },
  {
    title: t('label.self-care-family-informal-support'),
    key: 'self-care-family-informal-support',
    align: 'left',
    sortable: false,
    width: '185px',
  },
  {
    title: t('label.care-insurance-or-regional-support'),
    key: 'care-insurance-or-regional-support',
    align: 'left',
    sortable: false,
    width: '145px',
  },
  {
    title: t('label.service-type'),
    key: 'service-type',
    align: 'left',
    sortable: false,
    width: '130px',
  },
  {
    title: t('label.office-location'),
    key: 'office-location',
    align: 'left',
    sortable: false,
    width: '130px',
  },
  {
    title: t('label.period'),
    key: 'period',
    align: 'left',
    sortable: false,
    width: '130px',
  },
]
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  init()
})

/**
 * AC001_画面初期表示時
 */
function init() {
  // 「表示順変更」タブ初期表示
  if (props.onewayModelValue.indexList && props.onewayModelValue.indexList.length > 0) {
    const tempArray = props.onewayModelValue.indexList

    for (let data of tempArray) {
      data = convertPeriodFields(props.onewayModelValue, data)

      const item = {
        ID: data.ID,
        action: {
          onewayModelValue: {
            icon: 'dialpad',
            color: 'red',
            density: 'compact',
          },
        } as Mo00009OnewayType,
        sort: {
          modelValue: {
            value: data.sort + '',
          } as Mo01278Type,
          onewayModelValue: {
            max: 999,
            min: 1,
          } as Mo01278OnewayType,
        },
        goalSupportPoints: {
          onewayModelValue: {
            value: data.goalSupportPoints,
            unit: '',
            customClass: {
              // itemStyle: 'text-align: left; vertical-align: top;',
              outerStyle: 'background: inherit !important;',
            },
          } as Mo01337OnewayType,
        },
        informalSupport: {
          onewayModelValue: {
            value: data.informalSupport,
            unit: '',
          } as Mo01337OnewayType,
        },
        selfSupportContent: {
          onewayModelValue: {
            value: data.selfSupportContent,
            unit: '',
          } as Mo01337OnewayType,
        },
        familySupportContent: {
          onewayModelValue: {
            value: data.familySupportContent,
            unit: '',
          } as Mo01337OnewayType,
        },
        insuranceRegionalSupport: {
          onewayModelValue: {
            value: data.insuranceRegionalSupport,
            unit: '',
          } as Mo01337OnewayType,
        },
        insuranceRegionalSupportContent: {
          onewayModelValue: {
            value: data.insuranceRegionalSupportContent,
            unit: '',
          } as Mo01337OnewayType,
        },
        noteSelf: {
          onewayModelValue: {
            value: data.noteSelf,
            unit: '',
          } as Mo01337OnewayType,
        },
        noteFamily: {
          onewayModelValue: {
            value: data.noteFamily,
            unit: '',
          } as Mo01337OnewayType,
        },
        noteInsuranceRegional: {
          onewayModelValue: {
            value: data.noteInsuranceRegional,
            unit: '',
          } as Mo01337OnewayType,
        },
        serviceType: {
          onewayModelValue: {
            value: data.serviceType,
            unit: '',
          } as Mo01337OnewayType,
        },
        serviceTypeSelf: {
          onewayModelValue: {
            value: data.serviceTypeSelf,
            unit: '',
          } as Mo01337OnewayType,
        },
        serviceTypeFamily: {
          onewayModelValue: {
            value: data.serviceTypeFamily,
            unit: '',
          } as Mo01337OnewayType,
        },
        serviceTypeInsuranceRegional: {
          onewayModelValue: {
            value: data.serviceTypeInsuranceRegional,
            unit: '',
          } as Mo01337OnewayType,
        },
        office: {
          onewayModelValue: {
            value: data.office,
            unit: '',
          } as Mo01337OnewayType,
        },
        serviceProviderSelf: {
          onewayModelValue: {
            value: data.serviceProviderSelf,
            unit: '',
          } as Mo01337OnewayType,
        },
        serviceProviderFamily: {
          onewayModelValue: {
            value: data.serviceProviderFamily,
            unit: '',
          } as Mo01337OnewayType,
        },
        serviceProviderInsuranceRegional: {
          onewayModelValue: {
            value: data.serviceProviderInsuranceRegional,
            unit: '',
          } as Mo01337OnewayType,
        },
        frequencySelf: {
          onewayModelValue: {
            value: data.frequencySelf,
            unit: '',
          } as Mo01337OnewayType,
        },
        frequencyFamily: {
          onewayModelValue: {
            value: data.frequencyFamily,
            unit: '',
          } as Mo01337OnewayType,
        },
        frequencyInsuranceRegional: {
          onewayModelValue: {
            value: data.frequencyInsuranceRegional,
            unit: '',
          } as Mo01337OnewayType,
        },
        periodSelfDateConvert: {
          onewayModelValue: {
            value: data.periodSelfDateConvert + '',
            unit: '',
          } as Mo01337OnewayType,
        },
        periodFamilyDateConvert: {
          onewayModelValue: {
            value: data.periodFamilyDateConvert + '',
            unit: '',
          } as Mo01337OnewayType,
        },
        periodInsuranceRegionalDateConvert: {
          onewayModelValue: {
            value: data.periodInsuranceRegionalDateConvert + '',
            unit: '',
          } as Mo01337OnewayType,
        },
        periodDateConvert: {
          onewayModelValue: {
            value: data.periodDateConvert + '',
            unit: '',
          } as Mo01337OnewayType,
        },
      } as TableData

      tableData.value.push(item)
    }
  }
}

function isValidString(value: string): boolean {
  return !isNil(value) && trim(value) !== ''
}

function convertPeriodFields(dataIn: Or28543OnewayType, item: ShowData) {
  // 初期設定マスタの情報.計画表様式 = A4-3枚の場合
  if (dataIn.mode === Or28543Const.DEFAULT.MODE_A4_3) {
    // /初期設定マスタの情報.計画表：期間の管理 = 日付で管理 の場合
    if (dataIn.isPeriodManagementFlag === Or28543Const.DEFAULT.MANAGEMENT_BY_DATE) {
      // 初期設定マスタの情報.計画表：期間のカレンダー取込 が ?/?～?/? または ?月?日～?月?日 の場合
      if (
        dataIn.calendarImportByPeriod === Or28543Const.DEFAULT.PERIOD_CALENDAR_IMPORT_TYPE_0 ||
        dataIn.calendarImportByPeriod === Or28543Const.DEFAULT.PERIOD_CALENDAR_IMPORT_TYPE_1
      ) {
        // 期間開始と期間終了と期間全部あるの場合
        // 期間開始と期間終了だけあるの場合
        // 期間だけあるの場合
        // 期間（本人）
        if (
          isValidString(item.periodSelfStartDate) &&
          isValidString(item.periodSelfEndDate) &&
          isValidString(item.periodSelf)
        ) {
          item.periodSelfDateConvert = `${item.periodSelfStartDate}\n~\n${item.periodSelfEndDate}\n~\n${item.periodSelf}`
        } else if (
          isValidString(item.periodSelfStartDate) &&
          isValidString(item.periodSelfEndDate) &&
          !isValidString(item.periodSelf)
        ) {
          item.periodSelfDateConvert = `${item.periodSelfStartDate}\n~\n${item.periodSelfEndDate}`
        } else if (
          !isValidString(item.periodSelfStartDate) &&
          !isValidString(item.periodSelfEndDate) &&
          isValidString(item.periodSelf)
        ) {
          item.periodSelfDateConvert = `${item.periodSelf}`
        }
        // 期間（家族）
        if (
          isValidString(item.periodFamilyStartDate) &&
          isValidString(item.periodFamilyEndDate) &&
          isValidString(item.periodFamily)
        ) {
          item.periodFamilyDateConvert = `${item.periodFamilyStartDate}\n~\n${item.periodFamilyEndDate}\n~\n${item.periodFamily}`
        } else if (
          isValidString(item.periodFamilyStartDate) &&
          isValidString(item.periodFamilyEndDate) &&
          !isValidString(item.periodFamily)
        ) {
          item.periodFamilyDateConvert = `${item.periodFamilyStartDate}\n~\n${item.periodFamilyEndDate}`
        } else if (
          !isValidString(item.periodFamilyStartDate) &&
          !isValidString(item.periodFamilyEndDate) &&
          isValidString(item.periodFamily)
        ) {
          item.periodFamilyDateConvert = `${item.periodFamily}`
        }
        // 期間（保険・地域）
        if (
          isValidString(item.periodInsuranceRegionalStartDate) &&
          isValidString(item.periodInsuranceRegionalEndDate) &&
          isValidString(item.periodInsuranceRegional)
        ) {
          item.periodInsuranceRegionalDateConvert = `${item.periodInsuranceRegionalStartDate}\n~\n${item.periodInsuranceRegionalEndDate}\n~\n${item.periodInsuranceRegional}`
        } else if (
          isValidString(item.periodInsuranceRegionalStartDate) &&
          isValidString(item.periodInsuranceRegionalEndDate) &&
          !isValidString(item.periodInsuranceRegional)
        ) {
          item.periodInsuranceRegionalDateConvert = `${item.periodInsuranceRegionalStartDate}\n~\n${item.periodInsuranceRegionalEndDate}`
        } else if (
          !isValidString(item.periodInsuranceRegionalStartDate) &&
          !isValidString(item.periodInsuranceRegionalEndDate) &&
          isValidString(item.periodInsuranceRegional)
        ) {
          item.periodInsuranceRegionalDateConvert = `${item.periodInsuranceRegional}`
        }
      } else if (
        dataIn.calendarImportByPeriod === Or28543Const.DEFAULT.PERIOD_CALENDAR_IMPORT_TYPE_2 ||
        dataIn.calendarImportByPeriod === Or28543Const.DEFAULT.PERIOD_CALENDAR_IMPORT_TYPE_3
      ) {
        // 初期設定マスタの情報.計画表：期間のカレンダー取込 が ?/? または ?月?日 の場合
        // 期間開始と期間終了と期間全部あるの場合
        // 期間開始と期間終了だけあるの場合
        // 期間だけあるの場合
        // 期間（本人）
        if (
          isValidString(item.periodSelfStartDate) &&
          isValidString(item.periodSelfEndDate) &&
          isValidString(item.periodSelf)
        ) {
          item.periodSelfDateConvert = `${item.periodSelfStartDate}\n~\n${item.periodSelf}`
        } else if (
          isValidString(item.periodSelfStartDate) &&
          isValidString(item.periodSelfEndDate) &&
          !isValidString(item.periodSelf)
        ) {
          item.periodSelfDateConvert = `${item.periodSelfStartDate}`
        } else if (
          !isValidString(item.periodSelfStartDate) &&
          !isValidString(item.periodSelfEndDate) &&
          isValidString(item.periodSelf)
        ) {
          item.periodSelfDateConvert = `${item.periodSelf}`
        }
        // 期間（家族）
        if (
          isValidString(item.periodFamilyStartDate) &&
          isValidString(item.periodFamilyEndDate) &&
          isValidString(item.periodFamily)
        ) {
          item.periodFamilyDateConvert = `${item.periodFamilyStartDate}\n~\n${item.periodFamily}`
        } else if (
          isValidString(item.periodFamilyStartDate) &&
          isValidString(item.periodFamilyEndDate) &&
          !isValidString(item.periodFamily)
        ) {
          item.periodFamilyDateConvert = `${item.periodFamilyStartDate}`
        } else if (
          !isValidString(item.periodFamilyStartDate) &&
          !isValidString(item.periodFamilyEndDate) &&
          isValidString(item.periodFamily)
        ) {
          item.periodFamilyDateConvert = `${item.periodFamily}`
        }
        // 期間（保険・地域）
        if (
          isValidString(item.periodInsuranceRegionalStartDate) &&
          isValidString(item.periodInsuranceRegionalEndDate) &&
          isValidString(item.periodInsuranceRegional)
        ) {
          item.periodInsuranceRegionalDateConvert = `${item.periodInsuranceRegionalStartDate}\n~\n${item.periodInsuranceRegional}`
        } else if (
          isValidString(item.periodInsuranceRegionalStartDate) &&
          isValidString(item.periodInsuranceRegionalEndDate) &&
          !isValidString(item.periodInsuranceRegional)
        ) {
          item.periodInsuranceRegionalDateConvert = `${item.periodInsuranceRegionalStartDate}`
        } else if (
          !isValidString(item.periodInsuranceRegionalStartDate) &&
          !isValidString(item.periodInsuranceRegionalEndDate) &&
          isValidString(item.periodInsuranceRegional)
        ) {
          item.periodInsuranceRegionalDateConvert = `${item.periodInsuranceRegional}`
        }
      }
    } else if (dataIn.isPeriodManagementFlag === Or28543Const.DEFAULT.MANAGEMENT_BY_TEXT) {
      // 期間開始と期間終了と期間全部あるの場合
      // 期間開始と期間終了だけあるの場合
      // 期間だけあるの場合
      // 期間（本人）
      if (
        isValidString(item.periodSelfStartDate) &&
        isValidString(item.periodSelfEndDate) &&
        isValidString(item.periodSelf)
      ) {
        item.periodSelfDateConvert = `${item.periodSelf}`
      } else if (
        isValidString(item.periodSelfStartDate) &&
        isValidString(item.periodSelfEndDate) &&
        !isValidString(item.periodSelf)
      ) {
        item.periodSelfDateConvert = `${item.periodSelf}`
      } else if (
        !isValidString(item.periodSelfStartDate) &&
        !isValidString(item.periodSelfEndDate) &&
        isValidString(item.periodSelf)
      ) {
        item.periodSelfDateConvert = `${item.periodSelf}`
      }
      // 期間（家族）
      if (
        isValidString(item.periodFamilyStartDate) &&
        isValidString(item.periodFamilyEndDate) &&
        isValidString(item.periodFamily)
      ) {
        item.periodFamilyDateConvert = `${item.periodFamily}`
      } else if (
        isValidString(item.periodFamilyStartDate) &&
        isValidString(item.periodFamilyEndDate) &&
        !isValidString(item.periodFamily)
      ) {
        item.periodFamilyDateConvert = `${item.periodFamily}`
      } else if (
        !isValidString(item.periodFamilyStartDate) &&
        !isValidString(item.periodFamilyEndDate) &&
        isValidString(item.periodFamily)
      ) {
        item.periodFamilyDateConvert = `${item.periodFamily}`
      }
      // 期間（保険・地域）
      if (
        isValidString(item.periodInsuranceRegionalStartDate) &&
        isValidString(item.periodInsuranceRegionalEndDate) &&
        isValidString(item.periodInsuranceRegional)
      ) {
        item.periodInsuranceRegionalDateConvert = `${item.periodInsuranceRegional}`
      } else if (
        isValidString(item.periodInsuranceRegionalStartDate) &&
        isValidString(item.periodInsuranceRegionalEndDate) &&
        !isValidString(item.periodInsuranceRegional)
      ) {
        item.periodInsuranceRegionalDateConvert = `${item.periodInsuranceRegional}`
      } else if (
        !isValidString(item.periodInsuranceRegionalStartDate) &&
        !isValidString(item.periodInsuranceRegionalEndDate) &&
        isValidString(item.periodInsuranceRegional)
      ) {
        item.periodInsuranceRegionalDateConvert = `${item.periodInsuranceRegional}`
      }
    }
  } else if (
    dataIn.mode === Or28543Const.DEFAULT.MODE_A3 ||
    dataIn.mode === Or28543Const.DEFAULT.MODE_A4_2
  ) {
    item.periodDateConvert = ''
    // /初期設定マスタの情報.計画表：期間の管理 = 日付で管理 の場合
    if (dataIn.isPeriodManagementFlag === Or28543Const.DEFAULT.MANAGEMENT_BY_DATE) {
      // 初期設定マスタの情報.計画表：期間のカレンダー取込 が ?/?～?/? または ?月?日～?月?日 の場合
      if (
        dataIn.calendarImportByPeriod === Or28543Const.DEFAULT.PERIOD_CALENDAR_IMPORT_TYPE_0 ||
        dataIn.calendarImportByPeriod === Or28543Const.DEFAULT.PERIOD_CALENDAR_IMPORT_TYPE_1
      ) {
        // 期間開始と期間終了と期間全部あるの場合
        // 期間開始と期間終了だけあるの場合
        // 期間だけあるの場合
        // 期間
        if (
          isValidString(item.periodStartDate) &&
          isValidString(item.periodEndDate) &&
          isValidString(item.period)
        ) {
          item.periodDateConvert = `${item.periodStartDate}\n~\n${item.periodEndDate}\n~\n${item.period}`
        } else if (
          isValidString(item.periodStartDate) &&
          isValidString(item.periodEndDate) &&
          !isValidString(item.period)
        ) {
          item.periodDateConvert = `${item.periodStartDate}\n~\n${item.periodEndDate}\n~`
        } else if (
          !isValidString(item.periodStartDate) &&
          !isValidString(item.periodEndDate) &&
          isValidString(item.period)
        ) {
          item.periodDateConvert = `~\n${item.period}`
        }
      } else if (
        dataIn.calendarImportByPeriod === Or28543Const.DEFAULT.PERIOD_CALENDAR_IMPORT_TYPE_2 ||
        dataIn.calendarImportByPeriod === Or28543Const.DEFAULT.PERIOD_CALENDAR_IMPORT_TYPE_3
      ) {
        // 初期設定マスタの情報.計画表：期間のカレンダー取込 が ?/? または ?月?日 の場合
        // 期間開始と期間終了と期間全部あるの場合
        // 期間開始と期間終了だけあるの場合
        // 期間だけあるの場合
        // 期間
        if (
          isValidString(item.periodStartDate) &&
          isValidString(item.periodEndDate) &&
          isValidString(item.period)
        ) {
          item.periodDateConvert = `${item.periodStartDate}\n~\n${item.period}`
        } else if (
          isValidString(item.periodStartDate) &&
          isValidString(item.periodEndDate) &&
          !isValidString(item.period)
        ) {
          item.periodDateConvert = `${item.periodStartDate}\n~`
        } else if (
          !isValidString(item.periodStartDate) &&
          !isValidString(item.periodEndDate) &&
          isValidString(item.period)
        ) {
          item.periodDateConvert = `~\n${item.period}`
        }
      }
    } else if (dataIn.isPeriodManagementFlag === Or28543Const.DEFAULT.MANAGEMENT_BY_TEXT) {
      // 期間開始と期間終了と期間全部あるの場合
      // 期間開始と期間終了だけあるの場合
      // 期間だけあるの場合
      // 期間
      if (
        isValidString(item.periodStartDate) &&
        isValidString(item.periodEndDate) &&
        isValidString(item.period)
      ) {
        item.periodDateConvert = `~\n${item.period}`
      } else if (
        isValidString(item.periodStartDate) &&
        isValidString(item.periodEndDate) &&
        !isValidString(item.period)
      ) {
        item.periodDateConvert = `~\n${item.period}`
      } else if (
        !isValidString(item.periodStartDate) &&
        !isValidString(item.periodEndDate) &&
        isValidString(item.period)
      ) {
        item.periodDateConvert = `~\n${item.period}`
      }
    }
  }

  return item
}

/**
 * AC007-2ポップアップウィンドウで選択確認ダイアログを表示し
 *
 * @param errormsg - Message
 */
function showOr21814Msg(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue.mode === Or28543Const.DEFAULT.MODE_A4_3) {
      local.or28543.headers = changeTableHeadersModeA43
      local.or28543.heightDialog = '600px'
    }
    if (
      newValue.mode === Or28543Const.DEFAULT.MODE_A3 ||
      newValue.mode === Or28543Const.DEFAULT.MODE_A4_2
    ) {
      local.or28543.headers = changeTableHeadersModeA3
      local.or28543.heightDialog = '383px'
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

/**
 * AC007-2_Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.secondBtnClickFlg) {
      // 注意ダイアログの確認ボタン押下時
      await nextTick()
      await confirmOk()
    }
    if (newValue.thirdBtnClickFlg) {
      // 注意ダイアログの閉じるボタン押下時
      confirmCancle()
    }
  }
)

/**
 * 注意ダイアログの確認ボタン押下時
 */
async function confirmOk() {
  const startTemp = tableData.value[dragState.start - 1]
  const endTemp = tableData.value[dragState.end - 1]
  const startSort = startTemp.sort.modelValue.value
  startTemp.sort.modelValue.value = endTemp.sort.modelValue.value
  endTemp.sort.modelValue.value = startSort
  tableData.value[dragState.start - 1] = endTemp
  tableData.value[dragState.end - 1] = startTemp
  let testShowData = [...tableData.value]
  testShowData = testShowData
    .slice()
    .sort((a, b) => parseInt(a.sort.modelValue.value) - parseInt(b.sort.modelValue.value))
  await nextTick()

  tableDateMove.value = testShowData

  cssCallBack(dragState.start)
}

/**
 * 注意ダイアログの閉じるボタン押下時
 */
function confirmCancle() {
  cssCallBack(dragState.start)
}

/**
 * AC006_「表示順位削除」押下
 */
function sortDeleteClick() {
  // 表示順リスト表示順リスト内の順序列の内容をクリアする
  // リストのレコードを取得し、ループを使用してレコードの順序に基づいてソートする
  if (tableData.value && tableData.value.length > 0) {
    for (const item of tableData.value) {
      item.sort.modelValue.value = ''
    }
  }
}

const dragState = {
  // 開始索引
  start: -1,
  // 移動時に上書きされるインデックス
  end: -1,
  // 移動中ですか
  dragging: false,
  // 移動方向
  direction: '',
  // 上の浮遊する行
  lastSort: -1,
}

/**
 * AC007_「空白ラベル」ドラッグする_1
 *
 * @param e - $event
 *
 * @param index - 表示順
 */
function sortBtnMousedown(e: MouseEvent, index: number) {
  // 選択するとソートが成功します
  // 選択しない場合は記録位置をロールバックします
  dragState.dragging = true
  dragState.start = index

  const htmls = document.getElementsByClassName('suspension' + index)
  if (htmls) {
    for (const item of htmls) {
      if (item) {
        const html = item.parentElement?.parentElement
        if (html) {
          html.style.background = '#f2f2f2'
        }
      }
    }
  }
}

/**
 * AC007_「空白ラベル」ドラッグする_2
 *
 * @param index - 表示順
 */
function sortBtnMouseup(index: number) {
  if (dragState.start === dragState.end || dragState.start === index || -1 === index) {
    if (-1 !== index) {
      cssCallBack(dragState.start)
    }
    return
  }
  dragState.end = index

  // ポップアップウィンドウで選択確認ダイアログを表示し
  showOr21814Msg(t('message.i-cmn-10678', [dragState.start, dragState.end]))
}

/**
 * AC007_「空白ラベル」ドラッグする_3
 *
 * @param e - $event
 *
 * @param index - 表示順
 */
function sortBtnMousemove(e: MouseEvent, index: number) {
  if (dragState.dragging && index !== dragState.lastSort && index !== dragState.start) {
    if (-1 !== dragState.lastSort) {
      const lastHtmls = document.getElementsByClassName('suspension' + dragState.lastSort)
      if (lastHtmls) {
        for (const item of lastHtmls) {
          if (item) {
            const html = item.parentElement?.parentElement
            if (html) {
              html.style.background = ''
            }
          }
        }
      }
    }
    const htmls = document.getElementsByClassName('suspension' + index)
    if (htmls) {
      for (const item of htmls) {
        if (item) {
          const html = item.parentElement?.parentElement
          if (html) {
            html.style.background = '#0760e652'
            dragState.lastSort = index
          }
        }
      }
    }
  }
}

/**
 * AC007_「空白ラベル」ドラッグする_4
 *
 */
function tdMousemove() {
  if (dragState.dragging) {
    if (-1 === dragState.end) {
      const lastSort = dragState.lastSort
      cssCallBack(dragState.start)
      if (-1 !== lastSort) {
        cssCallBack(lastSort)
      }
    }
  }
}

/**
 * AC002_「×ボタン」押下
 * AC009_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 元素パターン回復
 *
 * @param sort - 表示順
 */
function cssCallBack(sort: number) {
  const element = document.querySelector('.suspension' + sort)
  if (element) {
    const html = element.parentElement?.parentElement
    if (html) {
      html.style.background = 'transparent'
    }
  }

  const lastElement = document.querySelector('.suspension' + dragState.lastSort)
  if (lastElement) {
    const html = lastElement.parentElement?.parentElement
    if (html) {
      html.style.background = 'transparent'
    }
  }

  dragState.start = -1
  dragState.end = -1
  dragState.dragging = false
  dragState.direction = ''
  dragState.lastSort = -1
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * AC008_「確定ボタン」押下
 * 「確定」ボタン押下
 */
function confirm() {
  // 表示されているタブ画面の表示順欄の値で課題データ情報の内容をソートする
  // ※表示順欄に値のない行は順次に最後に移動する
  const respData: Or28543Type = {
    mode: localOneway.Or28543.mode,
    sortList: [],
  }

  if (tableData.value && tableData.value.length > 0) {
    const tempList = new Array<ShowData>()
    for (const item of tableData.value) {

      const data: ShowData = {
        ID: item.ID,
        sort: Number(item.sort.modelValue.value),
        goalSupportPoints: item.goalSupportPoints.onewayModelValue.value,
        informalSupport: item.informalSupport.onewayModelValue.value,
        selfSupportContent: item.selfSupportContent.onewayModelValue.value,
        familySupportContent: item.familySupportContent.onewayModelValue.value,
        insuranceRegionalSupport: item.insuranceRegionalSupport.onewayModelValue.value,
        insuranceRegionalSupportContent:
          item.insuranceRegionalSupportContent.onewayModelValue.value,
        noteSelf: item.noteSelf.onewayModelValue.value,
        noteFamily: item.noteFamily.onewayModelValue.value,
        noteInsuranceRegional: item.noteInsuranceRegional.onewayModelValue.value,
        serviceType: item.serviceType.onewayModelValue.value,
        serviceTypeSelf: item.serviceTypeSelf.onewayModelValue.value,
        serviceTypeFamily: item.serviceTypeFamily.onewayModelValue.value,
        serviceTypeInsuranceRegional: item.serviceTypeInsuranceRegional.onewayModelValue.value,
        office: item.office.onewayModelValue.value,
        serviceProviderSelf: item.serviceProviderSelf.onewayModelValue.value,
        serviceProviderFamily: item.serviceProviderFamily.onewayModelValue.value,
        serviceProviderInsuranceRegional:
          item.serviceProviderInsuranceRegional.onewayModelValue.value,
        frequencySelf: item.frequencySelf.onewayModelValue.value,
        frequencyFamily: item.frequencyFamily.onewayModelValue.value,
        frequencyInsuranceRegional: item.frequencyInsuranceRegional.onewayModelValue.value,
      }
      tempList.push(data)
    }
    tempList.sort((a, b) => {
      if (!a.sort && !b.sort) return 0
      if (!a.sort) return 1
      if (!b.sort) return -1
      return a.sort - b.sort
    })
    respData.sortList = tempList
  }
  console.log('respData', respData);

  // 返却情報.課題データ情報 = ソート後の課題データ情報の内容
  emit('update:modelValue', respData)
  // 本画面を閉じ、親画面に返却する。
  close()
}
/**
 * 「表示順」マウスダウン
 *
 * @param reSetIndex - reSetIndex
 */
async function sortReSetProc(reSetIndex: number) {
  const sortVal = {
    maxSort: 0,
  }
  for (const item of tableData.value) {
    if (
      item &&
      item.sort.modelValue.value !== '' &&
      Number(item.sort.modelValue.value) > sortVal.maxSort
    ) {
      sortVal.maxSort = Number(item.sort.modelValue.value)
    }
  }
  if (tableData.value[reSetIndex].sort.modelValue.value === '') {
    tableData.value[reSetIndex].sort.modelValue.value = String(sortVal.maxSort + 1)
  }
  await nextTick()
}

/**
 * 数値入力イベント
 *
 *@param reSetIndex - reSetIndex
 */
async function onInputNumber(reSetIndex: number) {
  // 数値以外の文字を削除
  const value = tableData.value[reSetIndex].sort.modelValue.value
  tableData.value[reSetIndex].sort.modelValue.value = value.replace(/[^1-9]/g, '')
  await nextTick()
}

// メニュー切替
watch(
  () => local.mo00043.id,
  async (newValue) => {
    if (newValue === 'move') {
      local.or28543.isAddColumnMove = true
      const tdCustom = {
        title: '',
        key: 'action-move',
        align: 'start',
        width: '62px',
        sortable: false,
      }
      changeTableHeadersModeA43.unshift(tdCustom)
      changeTableHeadersModeA3.unshift(tdCustom)
    } else {
      local.or28543.isAddColumnMove = false
      changeTableHeadersModeA3.filter((header) => header.key !== 'action-move')
      changeTableHeadersModeA43.filter((header) => header.key !== 'action-move')
    }

    let testShowData = [...tableData.value]
    testShowData = testShowData.slice().sort((a, b) => {
      if (!a.sort.modelValue.value && !b.sort.modelValue.value) return 0
      if (!a.sort.modelValue.value) return 1
      if (!b.sort.modelValue.value) return -1
      return parseInt(a.sort.modelValue.value) - parseInt(b.sort.modelValue.value)
    })
    tableDateMove.value = testShowData
    tableData.value = testShowData
    await nextTick()
  }
)
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043"
        :oneway-model-value="localOneway.mo00043OneWay"
      >
      </base-mo00043>
      <!-- タブ switch -->
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item value="change">
          <c-v-card-text class="content-change">
            <!-- タブ：表示順変更 -->
            <div>
              <div class="mb-2">
                <base-mo01265
                  :oneway-model-value="localOneway.mo01265OnewayModelValue"
                  color="#ff0000"
                  label-color="#ff0000"
                  @click="sortDeleteClick"
                >
                </base-mo01265>
              </div>
              <c-v-data-table
                v-resizable-grid="{ columnWidths: columnMinWidth }"
                :headers="local.or28543.headers"
                class="table-wrapper"
                hide-default-footer
                :items="tableData"
                fixed-header
                hover
                :items-per-page="-1"
                :style="{height: local.or28543.heightDialog }"
              >
                <template #item="{ item, index }">
                  <tr v-if="local.or28543.mode === Or28543Const.DEFAULT.MODE_A4_3">
                    <td
                      rowspan="3"
                      class="padding-zero"
                    >
                      <base-mo01278
                        v-model="item.sort.modelValue"
                        :oneway-model-value="item.sort.onewayModelValue"
                        @click.stop="sortReSetProc(index)"
                        @input="onInputNumber(index)"
                      >
                      </base-mo01278>
                    </td>
                    <td
                      rowspan="3"
                      class="text-top-left-start"
                    >
                      <base-mo01337
                        :oneway-model-value="item.goalSupportPoints.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td style="padding: 0">
                      <div style="display: flex; height: 100%">
                        <div class="label-div-custom">
                          <!-- 本人の取組 -->
                          {{ t('label.person-group') }}
                        </div>
                        <div class="content-div-custom">
                          <base-mo01337
                            :oneway-model-value="item.selfSupportContent.onewayModelValue"
                          ></base-mo01337>
                        </div>
                      </div>
                    </td>
                    <td class="text-center">
                      <base-mo01337
                        :oneway-model-value="item.noteSelf.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceTypeSelf.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceProviderSelf.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.frequencySelf.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.periodSelfDateConvert.onewayModelValue"
                      ></base-mo01337>
                    </td>
                  </tr>
                  <tr v-if="local.or28543.mode === Or28543Const.DEFAULT.MODE_A4_3">
                    <td style="padding: 0">
                      <div style="display: flex; height: 100%">
                        <div class="label-div-custom">
                          <!-- 家族・地域の「改行」支援、民間... -->
                          {{ t('label.family-community-support') }}
                        </div>
                        <div class="content-div-custom">
                          <base-mo01337
                            :oneway-model-value="item.familySupportContent.onewayModelValue"
                          ></base-mo01337>
                        </div>
                      </div>
                    </td>
                    <td class="text-center">
                      <base-mo01337
                        :oneway-model-value="item.noteFamily.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceTypeFamily.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceProviderFamily.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.frequencyFamily.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.periodFamilyDateConvert.onewayModelValue"
                      ></base-mo01337>
                    </td>
                  </tr>
                  <tr v-if="local.or28543.mode === Or28543Const.DEFAULT.MODE_A4_3">
                    <td style="padding: 0">
                      <div style="display: flex; height: 100%">
                        <div class="label-div-custom">
                          <!-- 介護保険ｻｰﾋﾞｽ「改行」地域支援事業... -->
                          {{ t('label.insurance-regional-support') }}
                        </div>
                        <div class="content-div-custom">
                          <base-mo01337
                            :oneway-model-value="
                              item.insuranceRegionalSupportContent.onewayModelValue
                            "
                          ></base-mo01337>
                        </div>
                      </div>
                    </td>
                    <td class="text-center">
                      <base-mo01337
                        :oneway-model-value="item.noteInsuranceRegional.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceTypeInsuranceRegional.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceProviderInsuranceRegional.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.frequencyInsuranceRegional.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="
                          item.periodInsuranceRegionalDateConvert.onewayModelValue
                        "
                      ></base-mo01337>
                    </td>
                  </tr>
                  <tr
                    v-if="
                      local.or28543.mode === Or28543Const.DEFAULT.MODE_A3 ||
                      local.or28543.mode === Or28543Const.DEFAULT.MODE_A4_2
                    "
                  >
                    <td class="padding-zero">
                      <base-mo01278
                        v-model="item.sort.modelValue"
                        :oneway-model-value="item.sort.onewayModelValue"
                        @click.stop="sortReSetProc(index)"
                        @input="onInputNumber(index)"
                      >
                      </base-mo01278>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.goalSupportPoints.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.informalSupport.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.insuranceRegionalSupport.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceType.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.office.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.periodDateConvert.onewayModelValue"
                      ></base-mo01337>
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </div>
          </c-v-card-text>
        </c-v-window-item>
        <c-v-window-item value="move">
          <!-- タブ：表示順移動 -->
          <c-v-card @mouseup="sortBtnMouseup(-1)">
            <c-v-card-text
              id="customCard"
              class="content-change"
            >
              <c-v-data-table
                :headers="local.or28543.headers"
                class="table-wrapper"
                hide-default-footer
                :items="tableDateMove"
                fixed-header
                hover
                :items-per-page="-1"
                :style="{height: local.or28543.heightDialog }"
              >
                <template #item="{ item, index }">
                  <tr v-if="local.or28543.mode === Or28543Const.DEFAULT.MODE_A4_3">
                    <td
                      rowspan="3"
                      @mousedown="sortBtnMousedown($event, index + 1)"
                      @mouseup="sortBtnMouseup(index + 1)"
                      @mousemove="sortBtnMousemove($event, index + 1)"
                    >
                      <base-mo00009
                        icon="mdi-plus"
                        :oneway-model-value="localOneway.mo00009Oneway"
                        :class="'suspension' + (index + 1)"
                      >
                      </base-mo00009>
                    </td>
                    <td
                      rowspan="3"
                      class="text-center"
                    >
                      {{ item.sort.modelValue.value }}
                    </td>
                    <td
                      rowspan="3"
                      class="text-top-left-start"
                      @mousemove="tdMousemove"
                    >
                      <base-mo01337
                        :oneway-model-value="item.goalSupportPoints.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td
                      style="padding: 0"
                      class="text-top-left-start"
                      @mousemove="tdMousemove"
                    >
                      <div style="display: flex; height: 100%">
                        <div class="label-div-custom">
                          <!-- 本人の取組 -->
                          {{ t('label.person-group') }}
                        </div>
                        <div class="content-div-custom">
                          <base-mo01337
                            :oneway-model-value="item.selfSupportContent.onewayModelValue"
                          ></base-mo01337>
                        </div>
                      </div>
                    </td>
                    <td
                      class="text-top-left-start"
                      @mousemove="tdMousemove"
                    >
                      <base-mo01337
                        :oneway-model-value="item.noteSelf.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td
                      class="text-top-left-start"
                      @mousemove="tdMousemove"
                    >
                      <base-mo01337
                        :oneway-model-value="item.serviceTypeSelf.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td
                      class="text-top-left-start"
                      @mousemove="tdMousemove"
                    >
                      <base-mo01337
                        :oneway-model-value="item.serviceProviderSelf.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td
                      class="text-top-left-start"
                      @mousemove="tdMousemove"
                    >
                      <base-mo01337
                        :oneway-model-value="item.frequencySelf.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td
                      class="text-top-left-start"
                      @mousemove="tdMousemove"
                    >
                      <base-mo01337
                        :oneway-model-value="item.periodSelfDateConvert.onewayModelValue"
                      ></base-mo01337>
                    </td>
                  </tr>
                  <tr v-if="local.or28543.mode === Or28543Const.DEFAULT.MODE_A4_3">
                    <td style="padding: 0">
                      <div style="display: flex; height: 100%">
                        <div class="label-div-custom">
                          <!-- 家族・地域の「改行」支援、民間... -->
                          {{ t('label.family-community-support') }}
                        </div>
                        <div class="content-div-custom">
                          <base-mo01337
                            :oneway-model-value="item.familySupportContent.onewayModelValue"
                          ></base-mo01337>
                        </div>
                      </div>
                    </td>
                    <td class="text-center">
                      <base-mo01337
                        :oneway-model-value="item.noteFamily.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceTypeFamily.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceProviderFamily.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.frequencyFamily.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.periodFamilyDateConvert.onewayModelValue"
                      ></base-mo01337>
                    </td>
                  </tr>
                  <tr v-if="local.or28543.mode === Or28543Const.DEFAULT.MODE_A4_3">
                    <td style="padding: 0">
                      <div style="display: flex; height: 100%">
                        <div class="label-div-custom">
                          <!-- 介護保険ｻｰﾋﾞｽ「改行」地域支援事業... -->
                          {{ t('label.insurance-regional-support') }}
                        </div>
                        <div class="content-div-custom">
                          <base-mo01337
                            :oneway-model-value="
                              item.insuranceRegionalSupportContent.onewayModelValue
                            "
                          ></base-mo01337>
                        </div>
                      </div>
                    </td>
                    <td class="text-center">
                      <base-mo01337
                        :oneway-model-value="item.noteInsuranceRegional.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceTypeInsuranceRegional.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceProviderInsuranceRegional.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.frequencyInsuranceRegional.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="
                          item.periodInsuranceRegionalDateConvert.onewayModelValue
                        "
                      ></base-mo01337>
                    </td>
                  </tr>
                  <tr
                    v-if="
                      local.or28543.mode === Or28543Const.DEFAULT.MODE_A3 ||
                      local.or28543.mode === Or28543Const.DEFAULT.MODE_A4_2
                    "
                  >
                    <td
                      v-if="local.or28543.isAddColumnMove"
                      class="padding-zero"
                      style="padding: 0px 8px !important"
                      @mousedown="sortBtnMousedown($event, index + 1)"
                      @mouseup="sortBtnMouseup(index + 1)"
                      @mousemove="sortBtnMousemove($event, index + 1)"
                    >
                      <base-mo00009
                        icon="mdi-plus"
                        :oneway-model-value="localOneway.mo00009Oneway"
                        :class="'suspension' + (index + 1)"
                      >
                      </base-mo00009>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01278
                        :model-value="item.sort.modelValue"
                        :oneway-model-value="item.sort.onewayModelValue"
                        :disabled="true"
                      >
                      </base-mo01278>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.goalSupportPoints.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.informalSupport.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.insuranceRegionalSupport.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.serviceType.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.office.onewayModelValue"
                      ></base-mo01337>
                    </td>
                    <td class="text-top-left-start">
                      <base-mo01337
                        :oneway-model-value="item.periodDateConvert.onewayModelValue"
                      ></base-mo01337>
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
      </c-v-window>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="confirm"
        >
          <!--ツールチップ表示："設定を確定します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialog"
    v-bind="or21814"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
/** 表示順背景 */
:deep(.txt:disabled) {
  background: inherit;
}
/** 行ドラッグアイコン */
:deep(.material-symbols-rounded) {
  color: red;
}
/** タブコンテンツ */
.content-change {
  padding: 0;
  padding-top: 8px;
}
/** 表示順入力 */
:deep(.full-width-field) {
  width: 100% !important;
  text-align: center !important;
}
:deep(.v-col) {
  padding: 0 !important;
}
.padding-zero {
  padding: 0 !important;
}
.row-hover {
  background-color: #656363;
}
.text-top-left-start {
  text-align: left;
  vertical-align: top;
}
.label-div-custom {
  width: 84px;
  border-right: 1px solid rgb(var(--v-theme-black-200));
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-left: 16px;
}
.content-div-custom {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-left: 16px;
}
</style>
