import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or33763:（アセスメント(インターライ) ）T画面
 * GUI00784_アセスメント(インターライ)画面T
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */

export namespace Or33763Const {
  /**
   * コンポーネントID
   *
   * @param seq  - 連番
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or33763', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const SECTION_KEY = {
      T1: 't1',
      T2: 't2',
      T3: 't3',
      T4: 't4',
      T5: 't5',
    }
    /**
     * （初期値）開閉フラグ
     */
    export const DEFAULT_VALUE_T2 = {
      Key_0: '0',
      Key_1: '1',
      Key_2: '2',
    }
    /**
     * デフォルトサイズ
     */
    export const DEFAULT_SIZE = '14px'
    /**
     * デフォルトカラー
     */
    export const DEFAULT_COLOR = 'rgb(var(--v-theme-text))'
    /**
     * デフォルトセクションキー
     */
    export const DEFAULT_SECCTION_KEY = ['t1', 't2', 't3', 't4', 't5']
    /**
     * デフォルト削除区分
     */
    export const DEFAULT_DELETE_KBN = {
      kbn_0: '0',
      kbn_1: '1',
    }
    /**
     * デフォルトサブ区分
     */
    export const DEFAULT_SUB_KBN = 'T'
    /**
     * ダイアログ結果
     */
    export const DIALOG_RESULT = {
      YES: 'yes',
      CANCEL: 'cancel',
    }
  }
  /**
   * ステータスコード
   */
  export namespace STATUS_CODE {
    /**
     * 成功ステータスコード
     */
    export const SUCCESS = 'success'
    /**
     * 成功ステータスコード
     */
    export const SUCCESS_CODE = '200'

    /**
     * アクション名：アセスメント選択
     */
    export const NAME_ACTION_SELECT = 'assessmentInterRAITSelect'
    /**
     * アクション名：アセスメント追加
     */
    export const NAME_ACTION_ADD = 'assessmentInterRAITInsert'
    /**
     * アクション名：アセスメント更新
     */
    export const NAME_ACTION_UPDATE = 'assessmentInterRAITUpdate'
    /**
     * アクション名：アセスメント保存
     */
    export const NAME_ACTION_SAVE = 'assessmentInterRAITInsert'
    /**
     * アクション名：アセスメント削除
     */
    export const NAME_ACTION_DELETE = 'assessmentInterRAITDelete'
    /**
     * アクション名：アセスメント履歴選択
     */
    export const NAME_ACTION_SELECT_H = 'assessmentInterRAIAHistorySelect'
  }
}
