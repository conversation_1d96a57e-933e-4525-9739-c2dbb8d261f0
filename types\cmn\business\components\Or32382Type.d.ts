/**
 * Or32382:有機体: 計画書と［計画書複写］明細一覧
 * OneWayBind領域に保持するデータ構造
 */
import type { WeeklyTableInput } from '~/components/custom-components/organisms/Or32382/Or32382.type'
import type { WeekTableList } from '~/components/custom-components/organisms/OrX0068/OrX0068.type'
/**
 * oneWayBind領域に保持するデータ構造
 */
export interface Or32382OnewayType {
  /** テーブルアイテム */
  tableItem: WeekTableList[]
  /** 複写画面表示フラグ */
  cpyFlg?: boolean
  /** 期間管理フラグ */
  periodManageFlag: string
  /** 計画期間ID */
  planTargetPeriodId: string
  /** 削除フラグ */
  delFlg?: boolean
}
/**
 * twoWayBind領域に保持するデータ構造
 */
export interface Or32382Type {
  /** 週単位以外サービス */
  wIgaiKnj: string
  /** テーブルアイテム */
  weekData: WeeklyTableInput[]
}
