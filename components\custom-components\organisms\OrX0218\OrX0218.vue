<!-- eslint-disable n/no-extraneous-import -->
<script setup lang="ts">
/**
 * OrX0218:有機体:実施計画①～③表用選択メニュー付き日付入力
 *
 * @description
 * 実施計画①～③表用選択メニュー付き日付入力
 *
 * <AUTHOR>
 */
import { nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { format, parse } from 'fecha'
import type { NodeStyling, TextareaHeight } from './OrX0218.type'
import { OrX0218Const } from './OrX0218.constants'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01343OnewayType, Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { OrX0218OnewayType, OrX0218Type } from '~/types/cmn/business/components/OrX0218Type'
import { useTableCpValidation } from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { dateUtils } from '~/utils/dateUtils'

const { checkYmd } = dateUtils()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: OrX0218Type
  onewayModelValue?: OrX0218OnewayType
}

const props = defineProps<Props>()
/**************************************************
 * 変数
 **************************************************/
const defaultModelValue = {
  orX0218: {
    value: '',
  } as OrX0218Type,
}
const defaultOnewayModelValue: OrX0218OnewayType = {
  showMonthDayBtnFlg: true,
  showYmdBtnFlg: true,
  showEditHelperBtnFlg: true,
  showCalendarBtnFlg: true,
  customWidth: false,
}
const local = reactive({
  // カレンダー
  mo01343: {
    value: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  } as Mo01343Type,
  orX0218: {
    ...defaultModelValue.orX0218,
    ...props.modelValue,
  } as OrX0218Type,
})
const localOneway = reactive({
  // カレンダー
  mo01343Oneway: {
    selectMode: OrX0218Const.DEFAULT.CALENDAR_MODE_SINGLE_DAY,
  } as Mo01343OnewayType,
  orX0218Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as OrX0218OnewayType,
  mo00009Oneway: {
    btnIcon: 'calendar_today',
  } as Mo00009OnewayType,
  mo0009EditHelperOneway: {
    btnIcon: 'edit_square',
    density: 'compact',
    rounded: 'sm',
    size: 'medium',
    color: '#869fca',
  } as Mo00009OnewayType,
})
// 下部メニューバーを表示するかどうかフラグ
const showFooterMenuFlg = ref<boolean>(false)
// フッターメニューコンポーネントのDOM参照用
const footerMenuRef = ref<HTMLDivElement>()
// 期間テキストエリアコンポーネントのDOM参照用
const kikanTextAreaRef = ref<HTMLTextAreaElement>()
// 期間セルのフォーカス状態フラグ
const tableKikanCellFocusFlg = ref<boolean>(false)
// 期間テキストエリアの高さ
const kikanTextAreaHeight = ref<string>()
// 内部更新による変更かをマークするフラグ
const isInternalUpdate = ref(false)
const tableKikanCell = ref<HTMLElement>()
const menuPositionTop = ref<string>('')
const modelValueUpdFlg = ref<boolean>(false)
const menuPositionLeft = ref<string>('')
const inputFlg = ref<boolean>(false)
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits([
  'update:modelValue',
  'blur',
  'keydown',
  'change',
  'focus',
  'keypress',
  'keyup',
  'onClickEditBtn',
])
/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * テキストエリアの値の変化を監視し、高さを再計算
 */
watch(
  () => local.orX0218.value,
  () => {
    emit('change')
    reCalcTextAreaHeight()
    validValue.value = local.orX0218.value
    emit('update:modelValue', local.orX0218)
  }
)
/**
 * カレンダーポップアップの戻り値を監視し、テキストエリアの高さを再設定
 */
async function handleUpdate() {
  if (local.mo01343.mo00024?.isOpen) {
    return
  }
  if (!modelValueUpdFlg.value) {
    isInternalUpdate.value = true
    if (
      localOneway.mo01343Oneway.selectMode === OrX0218Const.DEFAULT.CALENDAR_MODE_SINGLE_DAY ||
      (localOneway.mo01343Oneway.selectMode === OrX0218Const.DEFAULT.SELECT_MODE_MONTH_YEAR &&
        local.mo01343.value !== '')
    ) {
      isInternalUpdate.value = true
      if (
        !localOneway.mo01343Oneway.showYearSelectFlg &&
        localOneway.orX0218Oneway.mdDateFormat &&
        localOneway.orX0218Oneway.mdDateFormat !== ''
      ) {
        local.orX0218.value = format(
          parse(local.mo01343.value, OrX0218Const.DEFAULT.DEFAULT_FORMAT) ?? new Date(),
          localOneway.orX0218Oneway.mdDateFormat
        )
      } else if (
        localOneway.orX0218Oneway.dateFormat &&
        localOneway.orX0218Oneway.dateFormat !== ''
      ) {
        local.orX0218.value = format(
          parse(local.mo01343.value, OrX0218Const.DEFAULT.DEFAULT_FORMAT) ?? new Date(),
          localOneway.orX0218Oneway.dateFormat
        )
      } else {
        local.orX0218.value = local.mo01343.value
      }
    } else if (local.mo01343.value && local.mo01343.endValue) {
      isInternalUpdate.value = true
      if (
        !localOneway.mo01343Oneway.showYearSelectFlg &&
        localOneway.orX0218Oneway.mdDateFormat &&
        localOneway.orX0218Oneway.mdDateFormat !== ''
      ) {
        local.orX0218.value =
          format(
            parse(local.mo01343.value, OrX0218Const.DEFAULT.DEFAULT_FORMAT) ?? new Date(),
            localOneway.orX0218Oneway.mdDateFormat
          ) +
          OrX0218Const.DEFAULT.LINE_BREAK_AND_WAVE +
          format(
            parse(local.mo01343.endValue, OrX0218Const.DEFAULT.DEFAULT_FORMAT) ?? new Date(),
            localOneway.orX0218Oneway.mdDateFormat
          )
      } else if (
        localOneway.orX0218Oneway.dateFormat &&
        localOneway.orX0218Oneway.dateFormat !== ''
      ) {
        local.orX0218.value =
          format(
            parse(local.mo01343.value, OrX0218Const.DEFAULT.DEFAULT_FORMAT) ?? new Date(),
            localOneway.orX0218Oneway.dateFormat
          ) +
          OrX0218Const.DEFAULT.LINE_BREAK_AND_WAVE +
          format(
            parse(local.mo01343.endValue, OrX0218Const.DEFAULT.DEFAULT_FORMAT) ?? new Date(),
            localOneway.orX0218Oneway.dateFormat
          )
      } else {
        local.orX0218.value =
          local.mo01343.value + OrX0218Const.DEFAULT.LINE_BREAK_AND_WAVE + local.mo01343.endValue
      }
    }
    await nextTick()
    reCalcTextAreaHeight()
    emit('update:modelValue', local.orX0218)
  }
  modelValueUpdFlg.value = false
}

watch(
  () => props.modelValue,
  async (newValue) => {
    // 内部更新によるトリガーの場合は処理をスキップ
    if (isInternalUpdate.value) {
      isInternalUpdate.value = false
      return
    }
    local.orX0218 = {
      ...defaultModelValue.orX0218,
      ...newValue,
    }
    await nextTick()
    reCalcTextAreaHeight()
    modelValueUpdFlg.value = true
  },
  { deep: true, immediate: true }
)
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.orX0218Oneway = {
      ...defaultOnewayModelValue,
      ...newValue,
    }
  },
  { deep: true }
)
/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(() => {
  modelValueUpdFlg.value = true
  reCalcTextAreaHeight()
  window.addEventListener(
    'scroll',
    () => {
      if (showFooterMenuFlg.value) {
        showFooterMenuFlg.value = false
      }
    },
    true
  )
})

// コンポーネントアンマウント時にクリーンアップ
onUnmounted(() => {
  if (hiddenTextarea.value?.parentNode) {
    hiddenTextarea.value.parentNode.removeChild(hiddenTextarea.value)
    hiddenTextarea.value = null
  }
})

/**
 * 下部メニューバーを表示する
 */
async function showFooterMenu() {
  const calcTop = tableKikanCell.value?.getBoundingClientRect()
  if (calcTop && !showFooterMenuFlg.value) {
    menuPositionTop.value = `${calcTop.height + calcTop.top + 4}px`
    menuPositionLeft.value = `${calcTop.left - 3}px`
  }
  showFooterMenuFlg.value = !showFooterMenuFlg.value
  await nextTick()
  footerMenuRef.value?.focus()
}
/**
 * 下部メニューバーがフォーカスを失ったら閉じる
 */
function onFooterMenuLostFocus() {
  showFooterMenuFlg.value = false
}
/**
 * セルにフォーカスを設定する
 */
function setTableKikanCellFocus() {
  tableKikanCellFocusFlg.value = true
  kikanTextAreaRef.value?.focus()
}

// 非表示テキストエリアの参照(Vueのrefで管理)
const hiddenTextarea = ref<HTMLTextAreaElement | null>(null)

/**
 * 対象要素のスタイル情報を計算する
 *
 * @param targetElement - スタイルを計算する対象要素
 *
 * @returns 計算されたスタイル情報(NodeStylingオブジェクト)
 */
function calculateNodeStyling(targetElement: HTMLTextAreaElement): NodeStyling {
  const style = window.getComputedStyle(targetElement)

  const boxSizing = style.getPropertyValue(OrX0218Const.DEFAULT.STYLE_BOX_SIZING)

  const paddingSize =
    parseFloat(style.getPropertyValue(OrX0218Const.DEFAULT.STYLE_PADDING_BOTTOM)) +
    parseFloat(style.getPropertyValue(OrX0218Const.DEFAULT.STYLE_PADDING_TOP))

  const borderSize =
    parseFloat(style.getPropertyValue(OrX0218Const.DEFAULT.STYLE_BORDER_BOTTOM_WIDTH)) +
    parseFloat(style.getPropertyValue(OrX0218Const.DEFAULT.STYLE_BORDER_TOP_WIDTH))

  const contextStyle = OrX0218Const.DEFAULT.CONTEXT_STYLE.map(
    (name) => `${name}:${style.getPropertyValue(name)}`
  ).join(OrX0218Const.DEFAULT.SEMICOLON)

  const result: NodeStyling = {
    contextStyle: contextStyle,
    paddingSize: paddingSize,
    borderSize: borderSize,
    boxSizing: boxSizing,
  }
  return result
}

/**
 * テキストエリアの適切な高さを計算する
 *
 * @param targetElement - 高さを計算する対象テキストエリア要素
 *
 * @param minRows - 最小行数(デフォルト: 1)
 *
 * @param maxRows - 最大行数(指定なしの場合はnull)
 *
 * @returns 高さ情報を含むTextareaHeightオブジェクト
 */
function calcTextareaHeight(
  targetElement: HTMLTextAreaElement,
  minRows: number | null = 1,
  maxRows: number | null = null
): TextareaHeight {
  if (!hiddenTextarea.value) {
    hiddenTextarea.value = document.createElement(OrX0218Const.DEFAULT.TEXTAREA)
    document.body.appendChild(hiddenTextarea.value)
  }
  const { contextStyle, paddingSize, borderSize, boxSizing } = calculateNodeStyling(targetElement)

  hiddenTextarea.value.setAttribute(
    OrX0218Const.DEFAULT.STYLE,
    `${contextStyle};${OrX0218Const.DEFAULT.HIDDEN_STYLE}`
  )
  hiddenTextarea.value.value = targetElement.value || targetElement.placeholder || ''

  let height = hiddenTextarea.value.scrollHeight
  const result: TextareaHeight = {}

  if (boxSizing === OrX0218Const.DEFAULT.STYLE_BORDER_BOX) {
    height += borderSize
  } else if (boxSizing === OrX0218Const.DEFAULT.STYLE_CONTENT_BOX) {
    height -= paddingSize
  }

  hiddenTextarea.value.value = ''
  const singleRowHeight = hiddenTextarea.value.scrollHeight - paddingSize

  if (minRows !== null) {
    let minHeight = singleRowHeight * minRows
    if (boxSizing === OrX0218Const.DEFAULT.STYLE_BORDER_BOX) {
      minHeight += paddingSize + borderSize
    }
    height = Math.max(minHeight, height)
    result.minHeight = `${minHeight}px`
  }

  if (maxRows !== null) {
    let maxHeight = singleRowHeight * maxRows
    if (boxSizing === OrX0218Const.DEFAULT.STYLE_BORDER_BOX) {
      maxHeight += paddingSize + borderSize
    }
    height = Math.min(maxHeight, height)
  }

  result.height = `${height}px`

  if (hiddenTextarea.value.parentNode) {
    hiddenTextarea.value.parentNode.removeChild(hiddenTextarea.value)
    hiddenTextarea.value = null
  }

  return result
}

/**
 * カレンダーを開く
 *
 * @param selectMode - 選択モード
 *
 * @param showYear - 年選択モード
 */
function openCalendar(selectMode: string, showYear: boolean) {
  showFooterMenuFlg.value = false
  modelValueUpdFlg.value = false
  localOneway.mo01343Oneway.selectMode = selectMode
  localOneway.mo01343Oneway.showYearSelectFlg = showYear
  if (local.mo01343.mo00024) {
    setMo01343Value()
  }
}

/**
 * テキストエリアの高さを再計算する
 */
function reCalcTextAreaHeight() {
  if (kikanTextAreaRef.value) {
    kikanTextAreaHeight.value = calcTextareaHeight(
      kikanTextAreaRef.value,
      1,
      localOneway.orX0218Oneway.maxRows ?? null
    ).height
  }
}
// バリデーションチェック用の値(ref)
const validValue = ref(local.orX0218.value)

/**
 * バリデーションチェック用の共通処理
 *
 * @description
 * ＜引数＞
 *  refValue：バリデーションチェックする値
 *  validateOn：バリデーションチェックのタイミング
 *  rules：バリデーションチェックのルール
 * ＜戻り値＞
 *  isInvalid：コンポーネントのバリデーション状態を保持する変数
 *  handleBlur：ブラー時に呼び出す関数
 */
const { isInvalid, handleBlur, errorMessages } = useTableCpValidation<string>({
  refValue: validValue,
  validateOn: localOneway.orX0218Oneway?.validateOn,
  rules: (localOneway.orX0218Oneway?.rules ?? []) as ((value: string) => string | boolean)[],
})

/**
 * blur時に実行する処理
 *
 * @param blurEvent - blur時のイベント
 */
function funcBlur(blurEvent: FocusEvent) {
  inputFlg.value = false
  tableKikanCellFocusFlg.value = false
  validValue.value = local.orX0218.value
  handleBlur()
  emit('blur', blurEvent)
}

/**
 * テキストエリアのフォーカスイベント処理
 *
 * @param focusEvent - focus時のイベント
 */
function setTableTextareaKikanCellFocus(focusEvent: FocusEvent) {
  inputFlg.value = true
  setTableKikanCellFocus()
  emit('focus', focusEvent)
}

/**
 * カレンダー値を設定
 */
function setMo01343Value() {
  const unformatArray = local.orX0218.value.split(/\r\n～\r\n|\n～\n/)
  let startValue = ''
  let endValue = ''
  if (unformatArray.length === 2) {
    startValue = formatValue(unformatArray[0])
    endValue = formatValue(unformatArray[1])
  } else {
    startValue = formatValue(local.orX0218.value)
    endValue = ''
  }
  local.mo01343 = {
    value: startValue,
    endValue: endValue,
    mo00024: {
      isOpen: true,
    },
  }
}

/**
 * 年月日
 *
 * @param str - カレンダー値
 */
function formatValue(str: string): string {
  const dateStr = str.replace(/年|月|日/g, (matched) => {
    return matched === '日' ? '' : '/'
  })
  if (checkYmd(dateStr)) {
    return dateStr
  } else if (checkMd(dateStr)) {
    return getMdDate(dateStr)
  }
  return ''
}

/**
 * 年月日を取得
 *
 * @param dateStr - 月日
 */
function getMdDate(dateStr: string) {
  const year = new Date().getFullYear()
  return year + '/' + dateStr
}

/**
 * 月日をチェックする
 *
 * @param dateStr - カレンダー値
 */
function checkMd(dateStr: string) {
  const split = dateStr.split('/')
  if (split.length === 2) {
    const year = new Date().getFullYear()
    const month = Number(split[0]) - 1
    const day = Number(split[1])
    const date = new Date(year, month, day)
    if (date.getFullYear() === year && date.getMonth() === month && date.getDate() === day) {
      return true
    }
  }
  return false
}
</script>

<template>
  <div
    class="table-kikan-container"
    :style="{
      minHeight: '30px !important',
      height: (localOneway.orX0218Oneway.height ?? '100%') + '!important',
      background: errorMessages.length !== 0 ? 'rgb(var(--v-theme-red-50))' : '',
    }"
  >
    <div
      ref="tableKikanCell"
      :class="['table-kikan-cell', { 'table-kikan-cell-active': tableKikanCellFocusFlg }]"
      tabIndex="0"
      style="height: 100% !important"
      @focus="setTableKikanCellFocus"
    >
      <div
        :class="['table-kikan-content', 'ga-1', localOneway.orX0218Oneway.contentClass]"
        :style="['height: 100% !important', localOneway.orX0218Oneway.contentStyle]"
      >
        <div class="table-textarea-content-icon">
          <base-mo00009
            v-if="localOneway.orX0218Oneway.showEditHelperBtnFlg"
            :oneway-model-value="localOneway.mo0009EditHelperOneway"
            :readonly="localOneway.orX0218Oneway.readonly"
            :disabled="localOneway.orX0218Oneway.disabled"
            :class="['icon-edit-btn', localOneway.orX0218Oneway.editBtnClass]"
            @click.stop="$emit('onClickEditBtn', $event)"
          />
        </div>
        <textarea
          ref="kikanTextAreaRef"
          v-bind="{ ...$attrs, ...localOneway.orX0218Oneway }"
          v-model="local.orX0218.value"
          :class="[
            'kikan-textarea',
            'full-width-field ',
            'txt',
            'table-cell',
            localOneway.orX0218Oneway.customWidth ? 'set-width' : '',
            { invalid: isInvalid || errorMessages.length > 0 },
          ]"
          :style="{ height: kikanTextAreaHeight }"
          style="outline: none !important"
          @focus="setTableTextareaKikanCellFocus($event)"
          @change="$emit('change', $event)"
          @blur="funcBlur($event)"
          @keydown="$emit('keydown', $event)"
          @keypress="$emit('keypress', $event)"
          @keyup="$emit('keyup', $event)"
        ></textarea>
        <div
          v-if="localOneway.orX0218Oneway.showCalendarBtnFlg"
          class="table-kikan-content-icon"
        >
          <base-mo00009
            :disabled="localOneway.orX0218Oneway.disabled"
            :readonly="localOneway.orX0218Oneway.readonly"
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="showFooterMenu"
          ></base-mo00009>
        </div>
      </div>
    </div>
  </div>
  <div
    class="footer"
    :style="{
      top: menuPositionTop,
      left: menuPositionLeft,
    }"
  >
    <v-slide-y-transition>
      <div
        v-if="showFooterMenuFlg"
        ref="footerMenuRef"
        class="footer-menu d-flex"
        tabindex="0"
        @blur="onFooterMenuLostFocus"
      >
        <div
          v-if="localOneway.orX0218Oneway.showYmdBtnFlg"
          class="footer-menu-item"
          @click="
            openCalendar(
              localOneway.orX0218Oneway.kikanFlg
                ? OrX0218Const.DEFAULT.CALENDAR_MODE_RANGE
                : OrX0218Const.DEFAULT.CALENDAR_MODE_SINGLE_DAY,
              true
            )
          "
        >
          {{ $t('label.input-by-ymd') }}
        </div>
        <div
          v-if="localOneway.orX0218Oneway.showMonthDayBtnFlg"
          class="footer-menu-item"
          @click="
            openCalendar(
              localOneway.orX0218Oneway.kikanFlg
                ? OrX0218Const.DEFAULT.CALENDAR_MODE_RANGE
                : OrX0218Const.DEFAULT.CALENDAR_MODE_SINGLE_DAY,
              false
            )
          "
        >
          {{ $t('label.input-by-md') }}
        </div>
      </div>
    </v-slide-y-transition>
  </div>
  <!-- カレンダー -->
  <base-mo01343
    v-model="local.mo01343"
    :oneway-model-value="localOneway.mo01343Oneway"
    @update:model-value="handleUpdate"
  />
  <v-messages
    v-if="errorMessages.length !== 0"
    :messages="errorMessages"
    :active="errorMessages.length !== 0"
    color="error"
    style="opacity: unset !important; padding-top: 6px !important"
    @click="setTableKikanCellFocus"
  >
    <template #message="{ message }">
      <base-at-icon
        color="error"
        icon="error"
        size="16"
      />
      {{ message }}
    </template>
  </v-messages>
</template>
<style scoped lang="scss">
$footer-menu-item-color: #0760e6;
$footer-menu-bg-color: #fff;
$footer-menu-border-color: #a6b4c1;
$footer-menu-hover-color: #f6f6f6;
// 表用選択メニュー付き期間入力のコンテナ
.table-kikan-container {
  width: 100%;
  height: 100%;
  min-height: 32px !important;
  // 期間セル
  .table-kikan-cell {
    width: calc(100% - 1px);
    margin-left: 1px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .table-kikan-content {
      display: flex;
      height: 100%;
      padding: 0 16px;
      // 期間入力アイコン
      .table-kikan-content-icon,
      // 入力支援ボタン
      .table-textarea-content-icon {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
// メニューコンテナ
.footer {
  width: 100%;
  position: fixed;
  z-index: 9999;
  .footer-menu {
    position: fixed;
    border: 1px solid $footer-menu-border-color;
    border-radius: 4px;
    background: $footer-menu-bg-color;
    // メニューアイテム
    .footer-menu-item {
      padding: 4px 8px;
      cursor: pointer;
      color: $footer-menu-item-color;
    }
    .footer-menu-item:hover {
      background: $footer-menu-hover-color;
    }
    .footer-menu-item + .footer-menu-item {
      border-left: 1px solid $footer-menu-border-color;
    }
  }
}
.table-kikan-cell-active:not(:has(.txt:disabled)) {
  box-shadow: inset 0 0 0px 2px rgb(var(--v-theme-blue-100));
  outline: solid 1px rgb(var(--v-theme-key));
}
.table-kikan-cell-active:not(:has(.txt:disabled)).invalid {
  box-shadow: inset 0 0 0px 2px rgb(var(--v-theme-blue-100));
  outline: solid 1px rgb(var(--v-theme-error));
}
:deep(.table-cell) {
  border: none !important;
  outline: none !important;
}
.full-width-field {
  width: 100% !important;
  min-width: 65px;
  max-height: 100%;
  margin: 0 !important;
  resize: none;
  vertical-align: top;
}
.set-width {
  width: 74px !important;
  overflow: hidden;
}

.table-kikan-container:has(.txt:disabled) {
  background: rgb(var(--v-theme-black-50));
}
</style>
