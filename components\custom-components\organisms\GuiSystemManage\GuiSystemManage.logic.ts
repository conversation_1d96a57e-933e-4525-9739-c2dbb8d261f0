import { GuiLoginHistoryLogic } from '../GuiLoginHistory/GuiLoginHistory.logic'
import { GuiLoginHistoryConst } from '../GuiLoginHistory/GuiLoginHistory.constants'
import { GuiH0001Const } from '../GuiH0001/GuiH0001.constants'
import { GuiH0001Logic } from '../GuiH0001/GuiH0001.logic'
import { GuiStaffListConst } from '../GuiStaffList/GuiStaffList.constants'
import { GuiStaffListLogic } from '../GuiStaffList/GuiStaffList.logic'
import { GuiSystemManageConst } from './GuiSystemManage.constants'
import { useInitialize } from '~/composables/useComponentLogic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'

/**
 * GuiSystemManage:有機体:システム管理画面（画面コンポーネント）
 * 処理ロジック
 *
 * @description
 * initialize処理を提供する
 */
export namespace GuiSystemManageLogic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: GuiSystemManageConst.CP_ID(0),
      uniqueCpId,
      childCps: [
        { cpId: Or11871Const.CP_ID },
        { cpId: GuiLoginHistoryConst.CP_ID(1) },
        { cpId: GuiH0001Const.CP_ID(1) },
        { cpId: GuiStaffListConst.CP_ID(1) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    GuiLoginHistoryLogic.initialize(childCpIds[GuiLoginHistoryConst.CP_ID(1)].uniqueCpId)
    GuiH0001Logic.initialize(childCpIds[GuiH0001Const.CP_ID(1)].uniqueCpId)
    GuiStaffListLogic.initialize(childCpIds[GuiStaffListConst.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
}
