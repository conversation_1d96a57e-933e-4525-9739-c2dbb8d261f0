<script setup lang="ts">
/**
 * Or10406:有機体:フリーアセスメントチェック項目マスタ
 * GUI00891_フリーアセスメントチェック項目マスタ
 *
 * @description
 * フリーアセスメントチェック項目マスタ
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or28815Const } from '../Or28815/Or28815.constants'
import { Or28816Const } from '../Or28816/Or28816.constants'
import { Or28817Const } from '../Or28817/Or28817.constants'
import { Or28815Logic } from '../Or28815/Or28815.logic'
import { Or28817Logic } from '../Or28817/Or28817.logic'
import { Or28816Logic } from '../Or28816/Or28816.logic'
import { Or28813Logic } from '../Or28813/Or28813.logic'
import type { ResData } from '../Or28813/Or28813.type'
import type { KghMocCheckYoushikiListType } from '../Or21312/Or21312.type'
import type { KghMocCheckKoumokuListType } from '../Or28815/Or28815.type'
import type { KghMocCheckSaimokuList } from '../Or28817/Or28817.type'
import type { KghMocCheckNaiyouList } from '../Or28816/Or28816.type'

import { Or10406Const } from './Or10406.constants'
import type { DataInfoType, Or10406StateType, ResDataInfoType } from './Or10406.type'
import type { Or28817Type } from '~/types/cmn/business/components/Or28817Type'
import type { Or28815Type } from '~/types/cmn/business/components/Or28815Type'
import type { Or28816Type } from '~/types/cmn/business/components/Or28816Type'
import type {
  Or10406Type,
  Or10406OnewayType,
  PropType,
} from '~/types/cmn/business/components/Or10406Type'
import { useSetupChildProps, useScreenOneWayBind, useScreenStore, hasRegistAuth } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Or28813Type } from '~/types/cmn/business/components/Or28813Type'
import type { Or21312Type } from '~/types/cmn/business/components/Or21312Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import { Or28813Const } from '~/components/custom-components/organisms/Or28813/Or28813.constants'
import { Or17575Const } from '~/components/custom-components/organisms/Or17575/Or17575.constants'
import { Or21312Logic } from '~/components/custom-components/organisms/Or21312/Or21312.logic'
import { Or21312Const } from '~/components/custom-components/organisms/Or21312/Or21312.constants'
import { Or01544Const } from '~/components/custom-components/organisms/Or01544/Or01544.constants'

import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { countKghTucCheckHead } from '~/repositories/cmn/entities/FreeAssessmentCheckMasterEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or10406Type
  onewayModelValue: Or10406OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: Or10406Const.DEFAULT.NULL })
const or28813 = ref({ uniqueCpId: Or10406Const.DEFAULT.NULL })
const or17575 = ref({ uniqueCpId: Or10406Const.DEFAULT.NULL })
const or21312 = ref({ uniqueCpId: Or10406Const.DEFAULT.NULL })
const or01544 = ref({ uniqueCpId: Or10406Const.DEFAULT.NULL })
const or28815 = ref({ uniqueCpId: Or10406Const.DEFAULT.NULL })
const or28816 = ref({ uniqueCpId: Or10406Const.DEFAULT.NULL })
const or28817 = ref({ uniqueCpId: Or10406Const.DEFAULT.NULL })
const or21814 = ref({ uniqueCpId: Or10406Const.DEFAULT.NULL })
const or21813 = ref({ uniqueCpId: Or10406Const.DEFAULT.NULL })
// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})

const defaultOnewayModelValue: Or10406OnewayType = {
  type: Or10406Const.DEFAULT.TYPE,
  houjinId: Or10406Const.DEFAULT.STR_1,
  shisetuId: Or10406Const.DEFAULT.STR_1,
  svJigyoId: Or10406Const.DEFAULT.STR_1,
}

const local = reactive({
  mo00043: { id: Or10406Const.DEFAULT.ID } as Mo00043Type,
})
const localOneway = reactive({
  Or10406: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00043OneWay: {
    tabItems: [],
  } as Mo00043OnewayType,
  mo00610oneway: {
    btnLabel: t('btn.import'),
  } as Mo00610OnewayType,
  mo01338oneway: {
    value: t('label.all-common'),
  } as Mo01338OnewayType,
  // 閉じるボタン
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  // 保存ボタン
  mo00609Oneway: {
    btnLabel: t('btn.save'),
    tooltipText: t('tooltip.save'),
  } as Mo00609OnewayType,
  // 行下移動ボタン
  rowDownOneway: {
    btnIcon: 'keyboard_arrow_down',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  // 行上移動ボタン
  rowUpOneway: {
    btnIcon: 'keyboard_arrow_up',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
})

/**************************************************
 * 変数定義
 **************************************************/
// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '920px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or10406',
    toolbarTitle: t('label.free-assessment-check-item-master'),
    toolbarName: 'Or10406ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'content',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or10406Const.DEFAULT.IS_OPEN,
})

const or28813Type = ref<Or28813Type>({
  value: Or10406Const.DEFAULT.NUM_1,
  parentId: Or10406Const.DEFAULT.NULL,
  hierarchy: Or10406Const.DEFAULT.NUM_1,
  opened: [],
  moveFlag: false,
  hasChild: false,
  id: Or10406Const.DEFAULT.STR_1,
})
let cancleFlg = true
const modelKey = ref(Or10406Const.DEFAULT.NULL)
const defprop = ref<PropType>({
  houjinId: localOneway.Or10406.houjinId,
  shisetuId: localOneway.Or10406.shisetuId,
  svJigyoId: localOneway.Or10406.svJigyoId,
})
const or21312Type = ref<Or21312Type>({
  selectRowIndex: Or10406Const.DEFAULT.NUM_D1,
  maxIndex: Or10406Const.DEFAULT.NUM_0,
  hasChildren: false,
  length: Or10406Const.DEFAULT.NUM_0,
})
const or28815Type = ref<Or28815Type>({
  selectRowIndex: Or10406Const.DEFAULT.NUM_D1,
  maxIndex: Or10406Const.DEFAULT.NUM_0,
  hasChildren: false,
  length: Or10406Const.DEFAULT.NUM_0,
})
const or28816Type = ref<Or28816Type>({
  selectRowIndex: Or10406Const.DEFAULT.NUM_D1,
  maxIndex: Or10406Const.DEFAULT.NUM_0,
  hasChildren: false,
  length: Or10406Const.DEFAULT.NUM_0,
})
const or28817Type = ref<Or28817Type>({
  selectRowIndex: Or10406Const.DEFAULT.NUM_D1,
  maxIndex: Or10406Const.DEFAULT.NUM_0,
  hasChildren: false,
  length: Or10406Const.DEFAULT.NUM_0,
})

//hierarchy画面コントロール用パラメター
const FreeAssessmentDisplayFlg1 = ref(true)
const FreeAssessmentDisplayFlg2 = ref(false)
const FreeAssessmentDisplayFlg3 = ref(false)
const FreeAssessmentDisplayFlg4 = ref(false)

// 保存権限
const savePermission = ref<boolean>(true)

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10406StateType>({
  cpId: Or10406Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10406Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(0)]: or21815.value,
  [Or28813Const.CP_ID(0)]: or28813.value,
  [Or17575Const.CP_ID(0)]: or17575.value,
  [Or21312Const.CP_ID(0)]: or21312.value,
  [Or28816Const.CP_ID(0)]: or28816.value,
  [Or28817Const.CP_ID(0)]: or28817.value,
  [Or28815Const.CP_ID(0)]: or28815.value,
  [Or01544Const.CP_ID(0)]: or01544.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 保存権限取得
  savePermission.value = await hasRegistAuth()

  //エラー
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: false,
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // 画面区分
  if (Or10406Const.DEFAULT.STR_1 === props.onewayModelValue.type) {
    localOneway.mo00043OneWay.tabItems = [
      { id: 'freeAssessmentCheckMaster', title: t('label.free-assessment-check-item-master') },
      { id: 'medicineUnit', title: t('label.issues-planning-4-title') },
      { id: 'statisticsStartTime', title: t('label.issues-planning-5-title') },
    ]
  } else if (Or10406Const.DEFAULT.STR_2 === props.onewayModelValue.type) {
    localOneway.mo00043OneWay.tabItems = [
      { id: 'medicineUnit', title: t('label.free-assessment-check-item-master') },
      { id: 'categoryDrugUse', title: t('label.category-drug-use') },
      { id: 'statisticsStartTime', title: t('label.statistics-start-time') },
    ]
  } else if (Or10406Const.DEFAULT.STR_3 === props.onewayModelValue.type) {
    localOneway.mo00043OneWay.tabItems = [
      { id: 'difficultyDegree', title: t('label.difficulty-degree') },
      { id: 'freeAssessmentCheckMaster', title: t('label.medicine') },
      { id: 'statisticsStartTime', title: t('label.statistics-start-time') },
    ]
  }

  //ダイヤログ isEdit falseにする
  isEditClear()
  // タイト設定
  // await tabClick()
})

/**
 * AC002_タブ押下
 *
 * @param param -タブ選択ID
 */
async function tabClick(param: Mo00043Type) {
  if (isEdit.value) {
    const res = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
    switch (res) {
      case 'yes': {
        // AC003(保存処理)を実行し
        const flg = await onClickSaveBtn()
        if (!flg) {
          local.mo00043.id = param.id
        }
        break
      }
      case 'no':
        // 処理続き
        local.mo00043.id = param.id
        break
      case 'cancel':
        // 処理終了
        return
    }
  } else {
    local.mo00043.id = param.id
  }
}

/**
 * 画面が閉じます
 */
function close(): void {
  setState({ isOpen: false })
}

const updateFun = async (updateData: ResDataInfoType) => {
  const countKghTucCheckHead: countKghTucCheckHead = await ScreenRepository.update(
    'kghMocCheckUpdate',
    updateData
  )
  if (countKghTucCheckHead.statusCode === 'success' || countKghTucCheckHead.statusCode === '200') {
    return 1
  } else {
    return 0
  }
}

/**
 * AC013_閉じるボタン押下
 */
async function onClickCloseBtn(): Promise<void> {
  // 画面入力データの変更がある場合、且つ、保存権限がない場合 - 共通
  if (isEdit.value && !savePermission.value) {
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.w-com-10006'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
    switch (dialogResult) {
      // はい
      case 'yes':
        // AC014保存処理を行う
        await onClickSaveBtn()
        close()
        break
      // いええ
      case 'no':
        // 処理続き
        close()
        return
      // キャンセル
      case 'cancel':
        // 処理終了
        return
    }
  }
  // 画面入力データの変更がある場合、且つ、保存権限がある場合
  else if (isEdit.value && savePermission.value) {
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11142', [t('label.free-assessment-check-item-master')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
    switch (dialogResult) {
      // はい
      case 'yes':
        // AC014保存処理を行う
        await onClickSaveBtn()
        close()
        break
      // いええ
      case 'no':
        // 処理続き
        close()
        return
      // キャンセル
      case 'cancel':
        // 処理終了
        return
    }
  }
  // 上記以外の場合
  else {
    close()
  }
}

/**
 * AC014_保存ボタン押下
 */
async function onClickSaveBtn() {
  if (!isEdit.value) {
    return
  }
  switch (reqData.flg) {
    case '1': {
      const data = reqData.data.map((item: KghMocCheckYoushikiListType) => ({
        houjinId: item.houjinId,
        shisetuId: item.shisetuId,
        svJigyoId: item.svJigyoId,
        youshikiId: item.youshikiId,
        sort: item.sort.value,
        useFlg: item.useFlg.modelValue,
        youshikiKnj: item.youshikiKnj.value,
        updateKbn: item.updateKbn,
        modifiedCnt: item.modifiedCnt,
      }))
      const invalidItem = data.find(
        (item) =>
          item.youshikiKnj?.trim() === Or10406Const.DEFAULT.NULL &&
          item.updateKbn !== Or10406Const.DEFAULT.UPDATEKBN_D
      )
      if (invalidItem !== undefined && invalidItem !== null) {
        //  不正入力が発見された
        Or21813Logic.state.set({
          uniqueCpId: or21813.value.uniqueCpId,
          state: {
            isOpen: true,
            dialogText: t('message.e-cmn-41723'),
          },
        })
        return 1 // 保存中止
      }
      const allResData = {} as ResDataInfoType
      allResData.updateSubjectKbn = reqData.flg
      allResData.kghMocCheckYoushikiList = data.map((item) => {
        return {
          ...item,
          useFlg: item.useFlg ? Or10406Const.DEFAULT.STR_1 : Or10406Const.DEFAULT.STR_0,
        }
      })

      const res = await updateFun(allResData)
      if (res) {
        // フリーアセスメントチェック項目マスタツリービューをフラシュする
        Or28813Logic.state.set({
          uniqueCpId: or28813.value.uniqueCpId,
          state: {
            executeFlag: 'flush',
            moveFlag: false,
          },
        })
        Or21312Logic.state.set({
          uniqueCpId: or21312.value.uniqueCpId,
          state: {
            executeFlag: 'flush',
          },
        })
        isEditClear()
      }

      break
    }
    case '2': {
      const data = reqData.data.map((item: KghMocCheckKoumokuListType) => {
        return {
          houjinId: item.houjinId,
          shisetuId: item.shisetuId,
          svJigyoId: item.svJigyoId,
          youshikiId: item.youshikiId,
          koumokuId: item.koumokuId,
          sort: item.sort.value,
          koumokuKnj: item.koumokuKnj.value,
          updateKbn: item.updateKbn,
          modifiedCnt: item.modifiedCnt,
        }
      })
      const invalidItem = data.find(
        (item) =>
          item.koumokuKnj?.trim() === Or10406Const.DEFAULT.NULL &&
          item.updateKbn !== Or10406Const.DEFAULT.UPDATEKBN_D
      )
      console.log('invalidItem', invalidItem)
      if (invalidItem !== undefined && invalidItem !== null) {
        //  不正入力が発見された
        Or21813Logic.state.set({
          uniqueCpId: or21813.value.uniqueCpId,
          state: {
            isOpen: true,
            dialogText: t('message.e-cmn-41723'),
          },
        })
        return 1 // 保存中止
      }
      const allResData = {} as ResDataInfoType
      allResData.updateSubjectKbn = reqData.flg
      allResData.kghMocCheckKoumokuList = data

      const res = await updateFun(allResData)
      if (res) {
        // フリーアセスメントチェック項目マスタツリービューをフラシュする
        Or28813Logic.state.set({
          uniqueCpId: or28813.value.uniqueCpId,
          state: {
            executeFlag: 'flush',
            moveFlag: false,
          },
        })
        Or28815Logic.state.set({
          uniqueCpId: or28815.value.uniqueCpId,
          state: {
            executeFlag: 'flush',
          },
        })
        isEditClear()
      }
      break
    }
    case '3': {
      const data = reqData.data.map((item: KghMocCheckSaimokuList) => {
        return {
          houjinId: item.houjinId,
          shisetuId: item.shisetuId,
          svJigyoId: item.svJigyoId,
          youshikiId: item.youshikiId,
          koumokuId: item.koumokuId,
          saimokuId: item.saimokuId,
          sort: item.sort.value,
          saimokuKnj: item.saimokuKnj.value,
          updateKbn: item.updateKbn,
          modifiedCnt: item.modifiedCnt,
        }
      })
      const invalidItem = data.find(
        (item) =>
          item.saimokuKnj?.trim() === Or10406Const.DEFAULT.NULL &&
          item.updateKbn !== Or10406Const.DEFAULT.UPDATEKBN_D
      )
      if (invalidItem !== undefined && invalidItem !== null) {
        //  不正入力が発見された
        Or21813Logic.state.set({
          uniqueCpId: or21813.value.uniqueCpId,
          state: {
            isOpen: true,
            dialogText: t('message.e-cmn-41723'),
          },
        })
        return 1 // 保存中止
      }
      const allResData = {} as ResDataInfoType
      allResData.updateSubjectKbn = reqData.flg
      allResData.kghMocCheckSaimokuList = data

      const res = await updateFun(allResData)
      if (res) {
        // フリーアセスメントチェック項目マスタツリービューをフラシュする
        Or28813Logic.state.set({
          uniqueCpId: or28813.value.uniqueCpId,
          state: {
            executeFlag: 'flush',
            moveFlag: false,
          },
        })
        Or28817Logic.state.set({
          uniqueCpId: or28817.value.uniqueCpId,
          state: {
            executeFlag: 'flush',
          },
        })
        isEditClear()
      }
      break
    }
    case '4': {
      const data = reqData.data.map((item: KghMocCheckNaiyouList) => {
        return {
          houjinId: item.houjinId,
          shisetuId: item.shisetuId,
          svJigyoId: item.svJigyoId,
          youshikiId: item.youshikiId,
          koumokuId: item.koumokuId,
          saimokuId: item.saimokuId,
          naiyouId: item.naiyouId,
          point: item.point.value,
          naiyouKnj: item.naiyouKnj.value,
          setumeiKnj: item.setumeiKnj.value,
          sort: item.sort.value,
          updateKbn: item.updateKbn,
          modifiedCnt: item.modifiedCnt,
        }
      })
      const invalidItem = data.find(
        (item) =>
          (item.naiyouKnj?.trim() === Or10406Const.DEFAULT.NULL ||
            item.setumeiKnj?.trim() === Or10406Const.DEFAULT.NULL) &&
          item.updateKbn !== Or10406Const.DEFAULT.UPDATEKBN_D
      )
      if (invalidItem !== undefined && invalidItem !== null) {
        //  不正入力が発見された
        Or21813Logic.state.set({
          uniqueCpId: or21813.value.uniqueCpId,
          state: {
            isOpen: true,
            dialogText: t('message.e-cmn-41723'),
          },
        })
        return 1 // 保存中止
      }
      const allResData = {} as ResDataInfoType
      allResData.updateSubjectKbn = reqData.flg
      allResData.kghMocCheckNaiyouList = data

      const res = await updateFun(allResData)
      if (res) {
        // フリーアセスメントチェック項目マスタツリービューをフラシュする
        Or28813Logic.state.set({
          uniqueCpId: or28813.value.uniqueCpId,
          state: {
            executeFlag: 'flush',
            moveFlag: false,
          },
        })
        Or28816Logic.state.set({
          uniqueCpId: or28816.value.uniqueCpId,
          state: {
            executeFlag: 'flush',
          },
        })
        isEditClear()
      }
      break
    }
  }
}
//第１階層-ツリールート
const treeRootLevel = () => {
  defprop.value.houjinId = localOneway.Or10406.houjinId
  defprop.value.shisetuId = localOneway.Or10406.shisetuId
  defprop.value.svJigyoId = localOneway.Or10406.svJigyoId
  FreeAssessmentDisplayFlg3.value = false
  FreeAssessmentDisplayFlg2.value = false
  FreeAssessmentDisplayFlg4.value = false
  FreeAssessmentDisplayFlg1.value = true
}
//第２階層-様式名称
const youshikiLevel = () => {
  defprop.value.houjinId = localOneway.Or10406.houjinId
  defprop.value.shisetuId = localOneway.Or10406.shisetuId
  defprop.value.svJigyoId = localOneway.Or10406.svJigyoId
  FreeAssessmentDisplayFlg1.value = false
  FreeAssessmentDisplayFlg3.value = false
  FreeAssessmentDisplayFlg4.value = false
  FreeAssessmentDisplayFlg2.value = true
}
//第3階層-評価項目
const koumokuLevel = () => {
  defprop.value.houjinId = localOneway.Or10406.houjinId
  defprop.value.shisetuId = localOneway.Or10406.shisetuId
  defprop.value.svJigyoId = localOneway.Or10406.svJigyoId
  FreeAssessmentDisplayFlg1.value = false
  FreeAssessmentDisplayFlg2.value = false
  FreeAssessmentDisplayFlg4.value = false
  FreeAssessmentDisplayFlg3.value = true
}
//第4階層-評価内容
const naiyouLevel = () => {
  defprop.value.houjinId = localOneway.Or10406.houjinId
  defprop.value.shisetuId = localOneway.Or10406.shisetuId
  defprop.value.svJigyoId = localOneway.Or10406.svJigyoId
  FreeAssessmentDisplayFlg1.value = false
  FreeAssessmentDisplayFlg2.value = false
  FreeAssessmentDisplayFlg3.value = false
  FreeAssessmentDisplayFlg4.value = true
}
/**
 * Or28813:有機体:label.free-assessment-check-item-masterツリーセクション_戻り値を監視
 *
 * AC003「第1階層-ツリールート名称ラベル」押下
 * AC004「第2階層-様式名称ラベル」押下
 * AC005「第3階層-評価項目名称ラベル」押下
 * AC006「第4階層-評価細目名称ラベル」押下
 *
 * @param param - 戻ってきたデータ
 */
const or28813UpdateModelValue = async (param: ResData) => {
  console.log(param)
  cancleFlg = true
  // 「第1階層-ツリールート名称ラベル」押下
  if (1 === param.hierarchy) {
    if (isEdit.value) {
      // 画面入力データに変更がある場合
      await currentsave(param)
    } else {
      defprop.value = param
      defprop.value.houjinId = localOneway.Or10406.houjinId
      defprop.value.shisetuId = localOneway.Or10406.shisetuId
      defprop.value.svJigyoId = localOneway.Or10406.svJigyoId
      treeRootLevel()
      oldhierarchy.value = 1
    }
  }
  // 「第2階層-様式名称ラベル」押下
  else if (2 === param.hierarchy) {
    // 画面入力データに変更がある場合
    if (isEdit.value) {
      await currentsave(param)
    } else {
      defprop.value = param
      defprop.value.youshikiId = param.youshikiId
      defprop.value.houjinId = localOneway.Or10406.houjinId
      defprop.value.shisetuId = localOneway.Or10406.shisetuId
      defprop.value.svJigyoId = localOneway.Or10406.svJigyoId
      youshikiLevel()
      oldhierarchy.value = 2
    }
  }
  // 「第3階層-評価項目名称ラベル」押下
  else if (3 === param.hierarchy) {
    // 画面入力データに変更がある場合
    if (isEdit.value) {
      await currentsave(param)
    } else {
      defprop.value = param
      defprop.value.youshikiId = param.youshikiId
      defprop.value.koumokuId = param.koumokuId
      defprop.value.houjinId = localOneway.Or10406.houjinId
      defprop.value.shisetuId = localOneway.Or10406.shisetuId
      defprop.value.svJigyoId = localOneway.Or10406.svJigyoId
      koumokuLevel()
      oldhierarchy.value = 3
    }
    // 「第4階層-評価細目名称ラベル」押下
  } else if (4 === param.hierarchy) {
    // 画面入力データに変更がある場合
    if (isEdit.value) {
      await currentsave(param)
    } else {
      defprop.value = param
      defprop.value.youshikiId = param.youshikiId
      defprop.value.koumokuId = param.koumokuId
      defprop.value.saimokuId = param.saimokuId
      defprop.value.houjinId = localOneway.Or10406.houjinId
      defprop.value.shisetuId = localOneway.Or10406.shisetuId
      defprop.value.svJigyoId = localOneway.Or10406.svJigyoId
      naiyouLevel()
      oldhierarchy.value = 4
    }
  }
}
const oldhierarchy = ref<number | undefined>(1)
const currentsave = async (param: ResData) => {
  const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })

  const ResultFlg = await handleDialogResult(dialogResult)
  if (ResultFlg === 2) {
    return
  }
  oldhierarchy.value = param.hierarchy
  if (cancleFlg) {
    defprop.value = param
  }

  if (ResultFlg) {
    switch (param.hierarchy) {
      case 1:
        treeRootLevel()
        break
      case 2:
        youshikiLevel()
        break
      case 3:
        koumokuLevel()
        break
      case 4:
        naiyouLevel()
        break
    }
  } else {
    return
  }
}
const isEditClear = () => {
  useScreenStore().setCpNavControl({
    cpId: Or21312Const.CP_ID(0),
    uniqueCpId: or21312.value.uniqueCpId,
    editFlg: false,
  })
  useScreenStore().setCpNavControl({
    cpId: Or28815Const.CP_ID(0),
    uniqueCpId: or28815.value.uniqueCpId,
    editFlg: false,
  })
  useScreenStore().setCpNavControl({
    cpId: Or28816Const.CP_ID(0),
    uniqueCpId: or28816.value.uniqueCpId,
    editFlg: false,
  })
  useScreenStore().setCpNavControl({
    cpId: Or28817Const.CP_ID(0),
    uniqueCpId: or28817.value.uniqueCpId,
    editFlg: false,
  })
}
async function handleDialogResult(dialogResult: unknown) {
  switch (dialogResult) {
    case 'yes': {
      const flg = await onClickSaveBtn()
      if (flg) {
        return 2
      }
      isEditClear()
      return 1
    }
    case 'no':
      isEditClear()
      return 1
    case 'cancel':
      cancleFlg = false
      Or28813Logic.state.set({
        uniqueCpId: or28813.value.uniqueCpId,
        state: {
          executeFlag: 'reload',
          moveFlag: false,
        },
      })
      Or28816Logic.state.set({
        uniqueCpId: or28816.value.uniqueCpId,
        state: {
          executeFlag: 'cancle',
        },
      })
      Or28817Logic.state.set({
        uniqueCpId: or28817.value.uniqueCpId,
        state: {
          executeFlag: 'cancle',
        },
      })
      Or28815Logic.state.set({
        uniqueCpId: or28815.value.uniqueCpId,
        state: {
          executeFlag: 'cancle',
        },
      })
      Or21312Logic.state.set({
        uniqueCpId: or21312.value.uniqueCpId,
        state: {
          executeFlag: 'cancle',
        },
      })
      return 0
  }
}

/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 確認ダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

const or21312UpdateModelValue = (value: string) => {
  or28813Type.value.id = value
}

/**
 * 警告ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21815StateType) => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }
        if (event?.closeBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    mo00024.value.isOpen = true
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue) {
      void onClickCloseBtn()
    }
  }
)

watch(
  () => FreeAssessmentDisplayFlg1.value,
  () => {
    modelKey.value = Date.now().toString()
  },
  { deep: true }
)
watch(
  () => FreeAssessmentDisplayFlg2.value,
  () => {
    modelKey.value = Date.now().toString()
  },
  { deep: true }
)
watch(
  () => FreeAssessmentDisplayFlg3.value,
  () => {
    modelKey.value = Date.now().toString()
  },
  { deep: true }
)
watch(
  () => FreeAssessmentDisplayFlg4.value,
  () => {
    modelKey.value = Date.now().toString()
  },
  { deep: true }
)
let reqData = {} as DataInfoType
const receiveDataFromChild = (data: DataInfoType) => {
  reqData = data
}
//保存処理チェック、最初の入力不正行の帳票にフォーカス
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    Or28813Logic.state.set({
      uniqueCpId: or28813.value.uniqueCpId,
      state: {
        executeFlag: 'reload',
        moveFlag: false,
      },
    })
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <!-- タブ switch -->
      <base-mo00043
        :model-value="local.mo00043"
        :oneway-model-value="localOneway.mo00043OneWay"
        @update:model-value="tabClick"
      >
      </base-mo00043>
      <c-v-window
        v-model="local.mo00043.id"
        class="c-v-window"
      >
        <!-- 主画面 -->
        <c-v-window-item value="freeAssessmentCheckMaster">
          <c-v-card>
            <!-- 内容画面 -->
            <c-v-card-text class="freeAssessmentCheckMasterContent">
              <c-v-row no-gutters>
                <c-v-col
                  cols="12"
                  sm="3"
                  class="freeAssessmentCheckMasterContent_left"
                >
                  <div>
                    <g-custom-or-28813
                      v-bind="or28813"
                      v-model="or28813Type"
                      :oneway-model-value="defprop"
                      @update="or28813UpdateModelValue"
                      @get-data="receiveDataFromChild"
                    ></g-custom-or-28813>
                  </div>
                </c-v-col>
                <c-v-col
                  cols="12"
                  sm="9"
                  class="freeAssessmentCheckMasterContent_right"
                >
                  <c-v-row
                    no-gutters
                    class="justify-center"
                  >
                    <!-- ツリー -->
                    <c-v-col
                      v-if="FreeAssessmentDisplayFlg1"
                      :key="modelKey"
                      cols="12"
                      sm="12"
                    >
                      <c-v-row no-gutters>
                        <c-v-col
                          cols="12"
                          sm="12"
                        >
                          <g-custom-or-21312
                            v-bind="or21312"
                            v-model="or21312Type"
                            :oneway-model-value="defprop"
                            :parent-unique-cp-id="props.uniqueCpId"
                            @update:model-value="or21312UpdateModelValue"
                            @get-data="receiveDataFromChild"
                          ></g-custom-or-21312>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                    <!-- 様式 -->
                    <c-v-col
                      v-if="FreeAssessmentDisplayFlg2"
                      :key="modelKey"
                      cols="12"
                      sm="12"
                    >
                      <c-v-row no-gutters>
                        <c-v-col
                          cols="12"
                          sm="12"
                        >
                          <g-custom-or-28815
                            v-bind="or28815"
                            v-model="or28815Type"
                            :parent-unique-cp-id="props.uniqueCpId"
                            :oneway-model-value="defprop"
                            @update:model-value="or21312UpdateModelValue"
                            @get-data="receiveDataFromChild"
                          ></g-custom-or-28815>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                    <!-- 評価項目 -->
                    <c-v-col
                      v-if="FreeAssessmentDisplayFlg3"
                      :key="modelKey"
                      cols="12"
                      sm="12"
                    >
                      <c-v-row
                        no-gutters
                        class="d-flex justify-end"
                      >
                        <c-v-col
                          cols="12"
                          sm="12"
                        >
                          <g-custom-or-28817
                            v-bind="or28817"
                            v-model="or28817Type"
                            :parent-unique-cp-id="props.uniqueCpId"
                            :oneway-model-value="defprop"
                            @update:model-value="or21312UpdateModelValue"
                            @get-data="receiveDataFromChild"
                          ></g-custom-or-28817>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                    <!-- -評価細目 -->
                    <c-v-col
                      v-if="FreeAssessmentDisplayFlg4"
                      :key="modelKey"
                      cols="12"
                      sm="12"
                    >
                      <c-v-row
                        no-gutters
                        class="d-flex justify-end"
                      >
                        <c-v-col
                          cols="12"
                          sm="12"
                        >
                          <g-custom-or-28816
                            v-bind="or28816"
                            v-model="or28816Type"
                            :parent-unique-cp-id="props.uniqueCpId"
                            :oneway-model-value="defprop"
                            @update:model-value="or21312UpdateModelValue"
                            @get-data="receiveDataFromChild"
                          ></g-custom-or-28816>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
      </c-v-window>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        class="text-right"
      >
        <c-v-col>
          <c-v-spacer />
          <!-- 閉じるボタン -->
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OneWay"
            class="mr-2"
            @click="onClickCloseBtn"
          >
          </base-mo00611>
          <!-- 確定ボタン-->
          <base-mo00609
            :oneway-model-value="localOneway.mo00609Oneway"
            @click="onClickSaveBtn"
          >
          </base-mo00609>
        </c-v-col>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- メッセージ 警告 -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <g-base-or21813 v-bind="or21813"></g-base-or21813>
</template>

<style lang="scss">
.content {
  padding: 0px !important;
}
</style>
<style scoped lang="scss">
.c-v-window {
  height: 516px;
}
:deep(.v-card-item) {
  padding: 8px !important;
}

.freeAssessmentCheckMasterContent {
  border-top: thin solid rgb(var(--v-theme-form));
  padding: 8px;

  .freeAssessmentCheckMasterContent_left {
    overflow: auto;
    height: 500px !important;
    max-height: 500px !important;
    max-width: 240px;
  }

  .freeAssessmentCheckMasterContent_right {
    border-left: thin solid rgb(var(--v-theme-form));
    padding: 0px 0px 8px 8px;

    justify-content: center;
    height: 500px !important;
    max-height: 500px !important;

    position: relative;
  }

  .freeAssessmentCheckMaster {
    margin-top: 8px;
    border: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  }
}
</style>
