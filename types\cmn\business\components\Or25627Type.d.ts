/**
 * Or25627：有機体：(予定マスタ)アセスメント（包括）マスタリスト
 * 単方向バインドのインタフェース
 */
export interface Or25627OnewayType {
  /**
   * 文字列入力支援リスト
   */
  stringInputAssistList: StringInputInAssistList[]
}

/**
 * 文字列入力支援リスト
 */
export interface StringInputInAssistList {
  /**
   *入力ID
   */
  cf1Id: string
  /**
   *入力区分
   */
  cf1Kbn: string
  /**
   *区分コード
   */
  kbnCd: string
  /**
   *内容
   */
  textKnj: string
  /**
   *変更フラグ
   */
  changeF: string
  /**
   *区分フラグ
   */
  kbnFlg: string
  /**
   * 更新区分
   */
  updateKbn: string
}

/**
 * 文字列入力支援リスト
 */
export interface StringInputOutAssistList {
  /**
   *入力ID
   */
  cf1Id: string
  /**
   *入力区分
   */
  cf1Kbn: string
  /**
   *区分コード
   */
  kbnCd: string
  /**
   *内容
   */
  textKnj: string
  /**
   *変更フラグ
   */
  changeF: string
  /**
   *区分フラグ
   */
  kbnFlg: string
}

/**
 * Or25627：有機体：(予定マスタ)アセスメント（包括）マスタリスト
 * 双方向バインドのインタフェース
 */
export interface Or25627Type {
  /**
   * 変更Flg
   */
  editFlg: boolean
  /**
   * 行削除ボタン活性Flg
   */
  delBtnDisabled: boolean
  /**
   * 文字列入力支援リスト
   */
  stringInputAssistList: StringInputInAssistList[]
  /**
   * 保存文字列入力支援リスト情報リスト
   */
  saveResultStringInputAssistList?: StringInputOutAssistList[]
}
