import type { Mo01334Headers } from '~/types/business/components/Mo01334Type'

import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type.d.ts'

/**
 * Or41363:有機体:（職員管理）検索結果表示用一覧
 * TwoWayBind領域用の構造
 */

/**
 * Or41363:有機体:（職員管理）検索結果表示用一覧
 * OneWayBind領域用の構造
 */
export interface Or41363StateType {
  /** 一覧のヘッダー */
  headers?: Mo01334Headers[]

  /** 一覧のデータ */
  items?: Or41363ItemType[]

  /** ロード中フラグ */
  loading?: boolean

  /** 一覧のデータ最大数（表示していないデータ含む件数） */
  itemsLength?: number

  /** 現在のページ */
  page?: number

  /**  1ページ辺りの表示数 */
  limit?: string
}

/**
 * Or41363:有機体:（職員管理）検索結果表示用一覧
 * EventStatus領域用の構造
 */
export interface Or41363EventType {
  /** 行選択イベントフラグ */
  rowSelectEventFlg?: boolean

  /** 一覧新規フラグ */
  listNewFlg?: boolean
}

/**
 * 一覧のデータのインタフェース
 */
export interface Or41363ItemType {
  /**
   * ID
   * 分子Mo01334を使用するために必要
   */
  id: string

  /**
   * 職員番号
   */
  mo01337OnewayStaffNumber: Mo01337OnewayType

  /**
   * 職員画像
   */
  staffImage: string

  /**
   * 職員名
   */
  mo01337OnewayStaffName: Mo01337OnewayType

  /**
   * ログインID
   */
  mo01337OnewayLoginId: Mo01337OnewayType

  /**
   * 職員ID
   */
  staffId: string

  /**
   * 勤務形態（区分値）
   */
  workingStyle: number

  /**
   * 勤務形態（ラベル）
   */
  mo01337OnewayWorkingStyleLabel: Mo01337OnewayType

  /**
   * 権限（区分値）
   */
  permission: number

  /**
   * 権限（ラベル）
   */
  mo01337OnewayPermissionLabel: Mo01337OnewayType

  /**
   * アカウント（区分値）
   */
  account: number

  /**
   * アカウント（ラベル）
   */
  mo01337OnewayAccountLabel: Mo01337OnewayType

  /**
   * 登録日
   */
  mo01337OnewayCreated: Mo01337OnewayType

  /**
   * 選択可能フラグ
   * - 一覧コンポーネントにおいてクリックで行選択を可能にするか
   * - true : 選択可能
   * - false : 選択不可
   */
  selectable: boolean
}
