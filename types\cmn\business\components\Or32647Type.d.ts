import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00040Type, Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
/**
 * Or32647:有機体:直近の入所・入院サブセクション
 * TowWayBind領域に保持するデータ構造
 */
export interface Or32647Type {
  /** ラジオボタン選択肢 */
  useFacilityType: UseFacilityType
  /** 施設・機関名 */
  facilityInstitution: {
    twowayValue: Mo00040Type
    onewayValue: Mo00040OnewayType
  }
  /** 郵便番号 */
  zipCode: Mo00045Type
  /** 備考 */
  remarks: Mo00045Type
  /** 電話番号 */
  telNumber: Mo00045Type
  /**
   * pinia値初始化フラグ
   */
  refValueInitDisabled?: boolean
}

/**
 * 利用施設種別
 */
export interface UseFacilityType {
  /** ラジオボタン選択肢 */
  raidoItems?: CodeType[]
  /** ラジオボタン選択肢 */
  raidoValue: string
  /** ラジオボタンonewayモデル */
  radioOneway: Mo00039OnewayType
}

/**
 * OneWayBind領域に保持するデータ構造
 */
export interface Or32647OnewayType {
  /** 施設・機関名 */
  facilityInstitutionLabel: string
  /** 施設名称情報リスト */
  shisetsuDataList: {
    // 施設ID
    shisetuId: string
    //施設名
    shisetuKnj: string
    //調査票施設種別
    scShisetsuShu: string
    //電話番号
    tel: string
    //住所
    addressKnj: string
    //表示順
    sort: string
    //郵便番号
    zip: string
  }[]
  /** 所在地 */
  locationOnewayLabel: string
  /** 電話番号ラベル */
  telNumberLabel: string
}
