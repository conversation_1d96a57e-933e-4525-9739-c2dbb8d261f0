<script setup lang="ts">
/**
 * Or41363:有機体:（職員管理）検索結果表示用一覧
 *
 * @description
 * （職員管理）検索結果表示用一覧コンポーネント。
 */
import { onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or41363Logic } from '../Or41363/Or41363.logic'
import type { Or41363EventType, Or41363StateType, Or41363ItemType } from './Or41363.type'
import { Or41363Const } from './Or41363.constants'
import {
  useSetupChildProps,
  useScreenTwoWayBind,
  useScreenOneWayBind,
  useScreenEventStatus,
  useScreenInitFlg,
} from '~/composables/useComponentVue'
import { useCommonProps } from '~/composables/useCommonProps'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import { Or41364Const } from '~/components/custom-components/organisms/Or41364/Or41364.constants'
import { Or41364Logic } from '~/components/custom-components/organisms/Or41364/Or41364.logic'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())

/**************************************************
 * 変数定義
 **************************************************/
const or41364 = ref({ uniqueCpId: '' })

const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    // 編集ボタン
    {
      title: '',
      key: 'id', // 編集ボタン押下時にidを使用するので、ここはidを設定
      sortable: false,
      minWidth: '70',
    },

    // 職員番号
    {
      title: t('label.staff-number'),
      key: 'mo01337OnewayStaffNumber',
      sortable: false,
      minWidth: '140',
    },

    // 職員名
    {
      title: t('label.staff-name'),
      key: 'mo01337OnewayStaffName',
      sortable: false,
      minWidth: '450',
    },

    // ログインID
    {
      title: t('label.login-id'),
      key: 'mo01337OnewayLoginId',
      sortable: false,
      minWidth: '250',
    },

    // 勤務形態（ラベル）
    {
      title: t('label.working-style'),
      key: 'mo01337OnewayWorkingStyleLabel',
      sortable: false,
      minWidth: '100',
    },

    // 権限（ラベル）
    {
      title: t('label.permission'),
      key: 'mo01337OnewayPermissionLabel',
      sortable: false,
      minWidth: '100',
    },

    // アカウント（ラベル）
    {
      title: t('label.account'),
      key: 'mo01337OnewayAccountLabel',
      sortable: false,
      minWidth: '100',
    },

    // 登録日
    {
      title: t('label.registration-date'),
      key: 'mo01337OnewayCreated',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: Or41363Const.DEFAULT.ITEMS,
  height: '100%',
  showPaginationTopFlg: true, // ページングを表示
  showPaginationBottomFlg: false,
  itemsPerPage: 20, // ページングの1ページあたりの行数
  serverSideFlg: true, // サーバー処理を考慮
  noDataText: t('message.no-data-search-criteria'), // データが存在しない場合のテキスト
  itemsLength: 0,
})

// 表示件数 プルダウン
const mo00040 = ref<Mo00040Type>({
  modelValue: '20',
})

const mo00040Oneway = ref<Mo00040OnewayType>({
  showItemLabel: false,
  isRequired: false,
  width: '80px',
  items: ['20', '40', '60'],
})

// 画面情報領域のsupplement内のisInitの取得およびonMountedにfalseへ更新する処理を登録
const isInit = useScreenInitFlg()

// 編集ボタン（優先度2ボタン） 単方向バインド
const mo00610OnewayEdit = ref<Mo00610OnewayType>({
  btnLabel: t('btn.edit'),
  width: '60px',
  minWidth: '60px',
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Mo01334Type>({
  cpId: Or41363Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

const { setState } = useScreenOneWayBind<Or41363StateType>({
  cpId: Or41363Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    headers: (value) => {
      if (value !== undefined) {
        mo01334Oneway.value.headers = value ?? Or41363Const.DEFAULT.HEADERS
      }
    },
    items: (value) => {
      mo01334Oneway.value.items = value ?? Or41363Const.DEFAULT.ITEMS
      mo01334Oneway.value.itemsLength = value?.length ?? 0
    },
    page: (value) => {
      if (value !== undefined) {
        if (refValue.value) {
          refValue.value.page = value ?? Or41363Const.DEFAULT.PAGE
        }
      }
    },
    loading: (value) => {
      if (value !== undefined) {
        if (refValue.value) {
          refValue.value.loading = value
        }
      }
    },
    itemsLength(value) {
      if (value !== undefined) {
        mo01334Oneway.value.itemsLength = value
      }
    },
    limit(value) {
      if (value !== undefined) {
        mo00040.value.modelValue = value
        mo01334Oneway.value.itemsPerPage = Number(value ?? Or41363Const.DEFAULT.LIMIT)
      }
    },
  },
})

const { setEvent } = useScreenEventStatus<Or41363EventType>({
  cpId: Or41363Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  // ブラウザの「戻る」「進む」ボタンからの遷移時に以前の値を維持したい画面の場合は
  // 画面情報領域のsupplement内のisInitで処理を分岐させて初回表示時のみ画面データの取得を行なう
  // ※値の維持が不要な画面では分岐は不要
  if (isInit) {
    setState({
      items: Or41363Const.DEFAULT.ITEMS,
    })
  }
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or41364Const.CP_ID(1)]: or41364.value,
})

/**
 * 編集ボタンクリック時の処理
 *
 * @param inputId - id
 */
function hensyuuBtnClick(inputId: string) {
  let workUserId = ''

  if (mo01334Oneway.value.items !== undefined) {
    mo01334Oneway.value.items.forEach((item) => {
      if (item.id === inputId) {
        const workItem = item as Or41363ItemType
        workUserId = workItem.mo01337OnewayLoginId.value
      }
    })
  }

  // （職員管理）職員編集ダイアログをオープンする
  Or41364Logic.state.set({
    uniqueCpId: or41364.value.uniqueCpId,
    state: {
      isOpen: true, // ダイアログの開閉状態
      loginUserId: workUserId, // ログインユーザーID
    },
  })
}

// 選択の変更を検知、行選択イベントを通知
watch(
  () => refValue.value?.value,
  () => {
    setEvent({
      rowSelectEventFlg: true,
    })
  }
)

// 一覧上の表示メッセージを、ロード中とそれ以外で分ける
watch(
  () => refValue.value?.loading,
  (newValue) => {
    if (newValue === true) {
      // ロード中の場合
      mo01334Oneway.value.noDataText = t('message.searching')
    } else {
      // ロード中でない場合
      mo01334Oneway.value.noDataText = t('message.no-data-search-criteria')
    }

    // 自身のピニア領域にロード中フラグを設定
    Or41363Logic.state.set({
      uniqueCpId: props.uniqueCpId,
      state: { loading: newValue },
    })
  }
)

// limit変更時
watch(
  () => mo00040.value?.modelValue,
  (newValue) => {
    setState({
      limit: newValue,
    })

    mo01334Oneway.value.itemsPerPage = Number(newValue ?? 20)

    if (refValue.value) {
      refValue.value.page = 1
    }
  }
)

// ページ変更時
watch(
  () => refValue.value?.page,
  (newValue) => {
    setState({
      page: newValue,
    })
  }
)
</script>

<template>
  <base-mo-01334
    v-model="refValue"
    :oneway-model-value="mo01334Oneway"
    class="list-wrapper flex-shrink-0 overflow-x-auto"
  >
    <!-- 編集ボタン -->
    <template #[`item.id`]="{ item }">
      <div
        style="
          background-color: transparent;
          height: 100%;
          width: 100%;
          min-height: 50px;
          min-width: 50px;
          align-items: center;
          display: flex;
          justify-content: center;
        "
      >
        <!-- 編集ボタン 優先度2ボタン -->
        <base-mo00610
          :oneway-model-value="mo00610OnewayEdit"
          @click="hensyuuBtnClick(item.id)"
        />
      </div>
    </template>

    <!-- 職員番号 -->
    <template #[`item.mo01337OnewayStaffNumber`]="{ item }">
      <!-- 分子：一覧専用ラベル（文字列型） -->
      <base-mo01337 :oneway-model-value="item.mo01337OnewayStaffNumber" />
    </template>

    <!-- 職員画像・職員職員名 -->
    <template #[`item.mo01337OnewayStaffName`]="{ item }">
      <div
        style="
          background-color: transparent;
          height: 100%;
          width: 100%;
          min-height: 30px;
          min-width: 32px;
          align-items: center;
          display: flex;
          justify-content: flex-start;
        "
      >
        <div style="height: 32px; width: 32px; background-color: transparent">
          <!-- 職員画像がある場合、職員画像を表示 -->
          <base-mo-00003
            :name="'staffImage-' + item.id"
            size="32px"
            :person-name="item.mo01337OnewayStaffName.value"
            :img-src="item.staffImage"
          />
        </div>

        <div style="height: 32px; width: 16px; background-color: transparent"></div>
        <!-- 分子：一覧専用ラベル（文字列型） -->
        <base-mo01337 :oneway-model-value="item.mo01337OnewayStaffName" />
      </div>
    </template>

    <!-- ログインID -->
    <template #[`item.mo01337OnewayLoginId`]="{ item }">
      <!-- 分子：一覧専用ラベル（文字列型） -->
      <base-mo01337 :oneway-model-value="item.mo01337OnewayLoginId" />
    </template>

    <!-- 勤務形態（ラベル） -->
    <template #[`item.mo01337OnewayWorkingStyleLabel`]="{ item }">
      <!-- 分子：一覧専用ラベル（文字列型） -->
      <base-mo01337 :oneway-model-value="item.mo01337OnewayWorkingStyleLabel" />
    </template>

    <!-- 権限（ラベル） -->
    <template #[`item.mo01337OnewayPermissionLabel`]="{ item }">
      <!-- 分子：一覧専用ラベル（文字列型） -->
      <base-mo01337 :oneway-model-value="item.mo01337OnewayPermissionLabel" />
    </template>

    <!-- アカウント（ラベル） -->
    <template #[`item.mo01337OnewayAccountLabel`]="{ item }">
      <!-- 分子：一覧専用ラベル（文字列型） -->
      <base-mo01337 :oneway-model-value="item.mo01337OnewayAccountLabel" />
    </template>

    <!-- 登録日 -->
    <template #[`item.mo01337OnewayCreated`]="{ item }">
      <!-- 分子：一覧専用ラベル（文字列型） -->
      <base-mo01337 :oneway-model-value="item.mo01337OnewayCreated" />
    </template>

    <!-- v-data-tableの既存のページングを非表示 -->
    <template #bottom />

    <!-- 表示件数プルダウン -->
    <template #tableTopLeft>
      <c-v-row
        no-gutters
        class="align-center justify-end"
      >
        <div>{{ t('label.display-number') }}：</div>
        <!-- Mo00040：分子：セレクトボックス -->
        <base-mo00040
          v-model="mo00040"
          :oneway-model-value="mo00040Oneway"
        />
      </c-v-row>
    </template>
  </base-mo-01334>

  <!-- （職員管理）職員編集ダイアログ -->
  <g-custom-or-41364 v-bind="or41364"></g-custom-or-41364>
</template>

<style scoped lang="scss">
/** （備考）
一覧上のマウスカーソル（cursor）をpointerからdefaultに変更するには、
親コンポーネントで以下を設定する。

:deep(.v-data-table__tr) {
  cursor: inherit;
}
*/
</style>
