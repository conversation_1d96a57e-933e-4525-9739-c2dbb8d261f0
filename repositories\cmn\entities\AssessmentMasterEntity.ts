import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * Or27562:アセスメントマスタモーダル
 * GUI00626_アセスメントマスタ
 *
 * @description
 * アセスメントマスタ画面API用エンティティ
 *
 * <AUTHOR>
 */
export interface AssessmentMasterSelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** 分類1 */
  bunrui1Id: string
  /** 分類2 */
  bunrui2Id: string
  /** 施設ID */
  shisetuId: string
}

/**
 * アセスメントマスタ取得出力エンティティ
 */
export interface AssessmentMasterSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** Visio */
    visioValue?: string
    /** Visio分類３ */
    visioBunrui3: string
    /** Visio更新回数 */
    visioModifiedCnt: string
    /** Visio更新区分 */
    visioUpdateKbn?: string
    /** 障害等の部位の摘要表示 */
    syougaiValue?: string
    /** 障害分類３ */
    syougaiBunrui3: string
    /** 障害更新回数 */
    syougaiModifiedCnt: string
    /** 障害更新区分 */
    syougaiUpdateKbn?: string
    /** 全体のまとめ */
    zenntaiValue?: string
    /** 全体分類３ */
    zenntaiBunrui3: string
    /** 全体更新回数 */
    zenntaiModifiedCnt: string
    /** 全体更新区分 */
    zenntaiUpdateKbn?: string
  }
}

/** アセスメントマスタ保存入力エンティティ */
export interface AssessmentMasterUpdateInEntity extends InWebEntity {
  /** 施設ID */
  shisetuId: string
  /** 事業所ID */
  svJigyoId: string
  /** 分類１ */
  bunrui1Id: string
  /** 分類2 */
  bunrui2Id: string
  /** Visio */
  visioValue?: string
  /** Visio分類３ */
  visioBunrui3: string

  /** Visio更新区分 */
  visioUpdateKbn?: string
  /** 障害等の部位の摘要表示 */
  syougaiValue?: string
  /** 障害分類３ */
  syougaiBunrui3: string

  /** 障害更新区分 */
  syougaiUpdateKbn?: string
  /** 全体のまとめ */
  zenntaiValue?: string
  /** 全体分類３ */
  zenntaiBunrui3: string

  /** 全体更新区分 */
  zenntaiUpdateKbn?: string
}

/**
 * アセスメントマスタ保存出力エンティティ
 */
export interface AssessmentMasterUpdateOutEntity extends OutWebEntity {
  /** data */
  data: object
}
