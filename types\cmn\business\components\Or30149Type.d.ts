import type { OrX0156Type } from './OrX0156Type'
import type { AssessmentHomeTab61SelectOutEntity } from '~/repositories/cmn/entities/AssessmentHome61Entity'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'

/**
 * Or30149:有機体:［アセスメント］画面（居宅）（6①）
 * GUI00799_［アセスメント］画面（居宅）（6①）
 */
/**
 * 双方向バインドModelValue
 */
export interface Or30149Type {
  /**
   * healthRelationList
   */
  healthRelationList: {
    careCertificationList: {
      sectionList:
        {
          value: string
          sectionTitle: string
          sectionItems: {
            itemType: string
            check: { modelValue: boolean }
            value: string
            itemName: string
            tooltip: string
            label: string
          }[]
        }[],
      }
    }

  /**
   * 特記1
   */
  memo_1: OrX0156Type
  /**
   * 特記2
   */
  memo_2: OrX0156Type
  /**
   * 特記3
   */
  memo_3: OrX0156Type
  /**
   * リハビリの必要性
   */
  rehabilitationNecessityRadioList: CodeType[]
  /**
   * リハビリの必要性 選択値
   */
  rehabilitationNecessityRaidoValue: string
  /**
   * 移乗移動介助 現状
   */
  transferMoveCareSupportCurrentList: {
    value: string
    check: Mo00018Type
    onewayModelValue: Mo00018OnewayType
  }[]
  /**
   * 移乗移動介助 計画
   */
  transferMoveCareSupportPlanList: {
    value: string
    check: Mo00018Type
    onewayModelValue: Mo00018OnewayType
  }[]
  /**
   * 洗身介助 現状
   */
  bodyWashingCareSupportCurrentList: {
    value: string
    check: Mo00018Type
    onewayModelValue: Mo00018OnewayType
  }[]
  /**
   * 洗身介助 計画
   */
  bodyWashingCareSupportPlanList: {
    value: string
    check: Mo00018Type
    onewayModelValue: Mo00018OnewayType
  }[]
  /**
   * 視聴覚
   */
  visionHearing: {
    mo00018: Mo00018Type
    mo00018Oneway: Mo00018OnewayType
  }[]
  /**
   * 電話
   */
  telUse: {
    radioList: CodeType[]
    mo00039: string
    mo00039Oneway: Mo00039OnewayType
  }
  /**
   * 言語障害使用有無
   */
  languageHandycapUse: {
    radioList: CodeType[]
    mo00039: string
    mo00039Oneway: Mo00039OnewayType
  }
  /**
   * 言語障害使用有無 入力値
   */
  languageHandycapUseInput: Mo00045Type
  /**
   * コミュニケーション支援機器の使用有無
   */
  communicationSupportDevicesUse: {
    radioList: CodeType[]
    mo00039: string
    mo00039Oneway: Mo00039OnewayType
  }
  /**
   * コミュニケーション支援機器の使用有無 入力値
   */
  communicationSupportDevicesUseInput: Mo00045Type
}
/**
 * 単方向バインドModelValue
 */
export interface Or30149OnewayType {
  /**
   * 画面モード
   */
  mode?: string
}

/**
 * 複写データタイプ
 */
export interface Or30149DataType {
  /**
   * 複写データ
   */
  copyData?: AssessmentHomeTab61SelectOutEntity
}

/**
 * 体位変換・起居一覧ヘッダー
 */
export interface PositionChangeRisingAndSleepingListHeader {
  /**
   * タイトル
   */
  title: string
  /**
   * キー
   */
  key: string
  /**
   * ソート順
   */
  sortable: boolean
  /**
   * 幅
   */
  width?: string
  /**
   * 最小幅
   */
  minWidth?: string
  /**
   * 文字揃え
   */
  align: string
  /**
   * 文字ラップ
   */
  nowrap: boolean
}

/**
 * 体位変換・起居一覧アイテム
 */
export interface PositionChangeRisingAndSleepingListItem {
  /** 6-①1-1、1-2関係 */
  relation: string
  /** 援助の現状 家族実施 */
  familyImplementation: string
  /** 援助の現状 サービス実施 */
  serverImplementation: string
  /** 希望 */
  hope: string
  /** 要援助→計画 */
  needAssistanceToPlan: string
}

/**
 * 入浴一覧ヘッダー
 */
export interface BathingListHeader {
  /**
   * タイトル
   */
  title: string
  /**
   * キー
   */
  key: string
  /**
   * ソート順
   */
  sortable: boolean
  /**
   * 幅
   */
  width?: string
  /**
   * 最小幅
   */
  minWidth?: string
  /**
   * 文字揃え
   */
  align: string
  /**
   * 文字ラップ
   */
  nowrap: boolean
}

/**
 * 入浴一覧アイテム
 */
export interface BathingListItem {
  /** 6-①1-1、1-2関係 */
  relation: string
  /** 援助の現状 家族実施 */
  familyImplementation: string
  /** 援助の現状 サービス実施 */
  serverImplementation: string
  /** 希望 */
  hope: string
  /** 要援助→計画 */
  needAssistanceToPlan: string
}
