<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or26426Const } from '~/components/custom-components/organisms/Or26426/Or26426.constants'
import { Or26426Logic } from '~/components/custom-components/organisms/Or26426/Or26426.logic'
import type { Or26426OnewayType } from '~/types/cmn/business/components/Or26426Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01272'
// ルーティング
const routing = 'GUI01272/pinia'
// 画面物理名
const screenName = 'GUI01272'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or26426 = ref({ uniqueCpId: Or26426Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01272' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  // Or26426Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or26426.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI01272',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26426Const.CP_ID(0) }],
})
Or26426Logic.initialize(init.childCpIds.Or26426.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or26426Const.CP_ID(0)]: or26426.value,
})

// 単方向バインド
const or26426Data: Or26426OnewayType = {
  //  計画期間ID
  sc1Id: '1',
  //  調査票ID
  cschId: '1',
  // 特記コード
  specialNoteCd: '2-2',
}

// ダイアログ表示フラグ
const showDialogOr26426 = computed(() => {
  // Or26426のダイアログ開閉状態
  return Or26426Logic.state.get(or26426.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or26426)
 */
function onClickOr26426() {
  // Or26426のダイアログ開閉状態を更新する
  Or26426Logic.state.set({
    uniqueCpId: or26426.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/05/28 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26426"
        >GUI01272_認定調査票特記事項
      </v-btn>
      <g-custom-or-26426
        v-if="showDialogOr26426"
        v-bind="or26426"
        :oneway-model-value="or26426Data"
        :parent-unique-cp-id="pageComponent.uniqueCpId"
      />
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/05/28 ADD END-->
</template>
