<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28820Const } from '../Or28820/Or28820.constants'
import { Or32390Const } from '../Or32390/Or32390.constants'
import { Or32390Logic } from '../Or32390/Or32390.logic'
import { OrX0069Const } from '../OrX0069/OrX0069.constants'
import { Or27509Const } from '../Or27509/Or27509.constants'
import { Or27509Logic } from '../Or27509/Or27509.logic'
import { Or32387Logic } from '../Or32387/Or32387.logic'
import { Or32387Const } from '../Or32387/Or32387.constants'
import { Or29976Logic } from '../Or29976/Or29976.logic'
import { Or29976Const } from '../Or29976/Or29976.constants'
import type { WeeklyPlanInput } from '../Or26714/Or26714.type'
import { OrX0153Const } from '../OrX0153/OrX0153.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type {
  WeekTableSelectOutData,
  WeekTableList,
} from '~/components/custom-components/organisms/OrX0068/OrX0068.type'
import { Or28820Logic } from '~/components/custom-components/organisms/Or28820/Or28820.logic'
import { OrX0124Logic } from '~/components/custom-components/organisms/OrX0124/OrX0124.logic'
import type { OrX0124StateType } from '~/components/custom-components/organisms/OrX0124/OrX0124.type'
import type { Or21828StateType } from '~/components/base-components/organisms/Or21828/Or21828.type'
import type { Or21830StateType } from '~/components/base-components/organisms/Or21830/Or21830.type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  WeekList,
  WeekTableInitInEntity,
  WeekTableInitOutEntity,
  Week3List,
} from '~/repositories/cmn/entities/WeekTableEntity'
import type {
  WeekListPattern,
  WeekTablePatternSelectInEntity,
  WeekTablePatternSelectOutEntity,
} from '~/repositories/cmn/entities/WeekTablePatternSelectEntity'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import { OrX0068Const } from '~/components/custom-components/organisms/OrX0068/OrX0068.constants'
import type { OrX0004StateType } from '~/components/custom-components/organisms/OrX0004/OrX0004.type'
import type { OrX0005StateType } from '~/components/custom-components/organisms/OrX0005/OrX0005.type'
import type { Or28820OnewayType, Or28820Type } from '~/types/cmn/business/components/Or28820Type'
import type { WeekTableInsertInEntity } from '~/repositories/cmn/entities/WeekTableInsertEntity'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
import { useScreenStore } from '~/stores/session/screen'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import type { OrX0069OnewayType, OrX0069Type } from '~/types/cmn/business/components/OrX0069Type'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import type {
  Or21813EventType,
  Or21813StateType,
} from '~/components/base-components/organisms/Or21813/Or21813.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00043OnewayType } from '~/types/business/components/Mo00043Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type {
  WeekTableHistorySelectInEntity,
  WeekTableHistorySelectOutEntity,
} from '~/repositories/cmn/entities/WeekTableHistorySelectEntity'
import type {
  WeekTablePeriodSelectInEntity,
  WeekTablePeriodSelectOutEntity,
} from '~/repositories/cmn/entities/WeekTablePeriodSelectEntity'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import type { Or27509OnewayType, Or27509Type } from '~/types/cmn/business/components/Or27509Type'
import type { Or32387OnewayType } from '~/types/cmn/business/components/Or32387Type'
import type { Or32390OnewayType } from '~/types/cmn/business/components/Or32390Type'
import { Or36086Logic } from '~/components/custom-components/organisms/Or36086/Or36086.logic'
import { Or36086Const } from '~/components/custom-components/organisms/Or36086/Or36086.constants'
import { Or27501Logic } from '~/components/custom-components/organisms/Or27501/Or27501.logic'
import { Or10828Logic } from '~/components/custom-components/organisms/Or10828/Or10828.logic'
import { Or27501Const } from '~/components/custom-components/organisms/Or27501/Or27501.constants'
import { Or10828Const } from '~/components/custom-components/organisms/Or10828/Or10828.constants'
import { OrX0153Logic } from '~/components/custom-components/organisms/OrX0153/OrX0153.logic'
import type { Or36086OnewayType, Or36086Type } from '~/types/cmn/business/components/Or36086Type'
import type { Or27501OnewayType, Or27501Type } from '~/types/cmn/business/components/Or27501Type'
import type { Or10828OnewayType, Or10828Type } from '~/types/cmn/business/components/Or10828Type'
import type { Or29976OnewayType } from '~/types/cmn/business/components/Or29976Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type {
  WeekTableCopySelectInEntity,
  WeekTableCopySelectOutEntity,
} from '~/repositories/cmn/entities/WeekTableCopySelectEntity'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { useJigyoList } from '~/utils/useJigyoList'
import type { OrX0165OnewayType } from '~/types/cmn/business/components/OrX0165Type'
import type { OrX0157OnewayType, OrX0157Type } from '~/types/cmn/business/components/OrX0157Type'
import type { Mo01352OnewayType } from '~/types/business/components/Mo01352Type'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import type { Or26257OnewayType, Or26257Type } from '~/types/cmn/business/components/Or26257Type'
import { OrX0115Const } from '~/components/custom-components/organisms/OrX0115/OrX0115.constants'
import { OrX0115Logic } from '~/components/custom-components/organisms/OrX0115/OrX0115.logic'
import { useValidation } from '@/utils/useValidation'
import { useColorUtils } from '#imports'

const { byteLength } = useValidation()
/**
 * OrX0068:有機体:週間表（画面コンポーネント）
 *
 * @description
 * 週間表（画面コンポーネント）
 *
 * <AUTHOR>
 */
// システム共有領域の状態管理
const systemCommonsStore = useSystemCommonsStore()

const { t } = useI18n()
const { convertHexToDecimal } = useColorUtils()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  orX0124UniqueCpId: string
  parentUniqueCpId: string
}
/** props */
const props = defineProps<Props>()

/**
 *利用者選択監視関数を実行
 */
const { syscomUserSelectWatchFunc } = useUserListInfo()
/**
 *子コンポーネント
 */
const { getChildCpBinds,setChildCpBinds } = useScreenUtils()
/**
 * jigyoListWatch
 */
const { updateJigyoList, jigyoListWatch } = useJigyoList()
/**************************************************
 * 変数定義
 **************************************************/

const or11871 = ref({ uniqueCpId: Or11871Const.CP_ID })
const or28820 = ref({ uniqueCpId: Or28820Const.CP_ID(0) })
const or27509 = ref({ uniqueCpId: Or27509Const.CP_ID(0) })
const orX0115 = ref({ uniqueCpId: OrX0115Const.CP_ID(0) })
const or32390 = ref({ uniqueCpId: Or32390Const.CP_ID(0) })
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(0) })
const orX0069 = ref({ uniqueCpId: OrX0069Const.CP_ID(0) })
const or32387 = ref({ uniqueCpId: Or32387Const.CP_ID(0) })
const or29976 = ref({ uniqueCpId: Or29976Const.CP_ID(0) })
const or41179_1 = ref({ uniqueCpId: '' })
// KMD 李傑 ADD START
const or36086 = ref({ uniqueCpId: Or36086Const.CP_ID(0) })
const or27501 = ref({ uniqueCpId: Or27501Const.CP_ID(0) })
const or10828 = ref({ uniqueCpId: Or10828Const.CP_ID(0) })
const orX0153 = ref({ uniqueCpId: OrX0153Const.CP_ID(0) })
// KMD 李傑 ADD END
const or26257 = ref({ uniqueCpId: '' }) // 職員検索モーダル

const defaultComponents = {
  // 新規操作メニュー
  orX0004: {
    showCreateBtn: true,
    showCreateMenuCopy: true,
  } as OrX0004StateType,
  // 印刷設定メニュー
  orX0005: {
    showPrintConfig: true,
    showBatchPrintOfPlans: false,
  } as OrX0005StateType,
  // 週間表オプションメニュー
  orX0124: {
    showpPatternFlg: true,
    showDeleteFlg: true,
    showMonthlyImportFlg: true,
    showEstimateFlg: true,
    showNikkaImportFlg: true,
  } as OrX0124StateType,
  // お気に入りアイコンボタン
  or21828: {} as Or21828StateType,
  // 保存ボタン
  or21830: {} as Or21830StateType,
}

const localComponents = reactive({
  orX0004: {
    ...defaultComponents.orX0004,
  } as OrX0004StateType,
  orX0005: {
    ...defaultComponents.orX0005,
  } as OrX0005StateType,
  or21828: {
    ...defaultComponents.or21828,
  } as Or21828StateType,
  or21830: {
    ...defaultComponents.or21830,
  } as Or21830StateType,
  orX0124: {
    ...defaultComponents.orX0124,
  } as OrX0124StateType,
})

const localOneway = reactive({
  // 計画期間
  orX0165Plan: {
    label: t('label.planning-period'),
  } as OrX0165OnewayType,
  // 履歴
  orX0165History: {
    label: t('label.history'),
  } as OrX0165OnewayType,
  //作成者
  orX0157CreateUser: {
    showEditBtnFlg: true,
    editBtnClass: 'custom-edit-btn',
    inputReadonly: true,
    text: {
      orX0157InputOneway: {
        customClass: {
          outerClass: '',
          labelClass: 'mb-1',
        },
        itemLabel: t('label.author'),
        showItemLabel: true,
        isVerticalLabel: true,
        width: '148px',
      },
    },
  } as OrX0157OnewayType,
  //作成日
  mo00020CreateDate: {
    itemLabel: t('label.create-date'),
    showItemLabel: true,
    isVerticalLabel: true,
    isRequired: true,
    hideDetails: true,
    customClass: {
      outerClass: '',
      labelClass: 'mb-1',
    },
    width: '135',
  } as Mo00020OnewayType,
  // ケース番号
  mo00045CaseNum: {
    itemLabel: t('label.caseNo'),
    maxLength: '10',
    isVerticalLabel: true,
    width: '105',
    customClass: {
      outerClass: '',
      labelClass: 'mb-1',
    },
  } as Mo00045OnewayType,
  //計画対象期間画面
  orX0115: {
    kindId: '',
    sc1Id: '',
  } as OrX0115OnewayType,
  //履歴画面
  or28820Oneway: {} as Or28820OnewayType,
  Or27509Oneway: {
    shisetsuId: 1,
    svJigyoId: '1',
    saveAuthority: 'false',
  } as Or27509OnewayType,
  Or32387Oneway: {
    keiyakushaId: '1',
    local: '1',
    week1Id: '1',
  } as Or32387OnewayType,
  Or32390Oneway: {
    svJigyoId: '',
    shisetuId: '',
    userId: '',
    syubetsuId: '',
  } as Or32390OnewayType,
  uniqueCpId: '',
  parentUniqueCpId: '',
  orX0069Oneway: {
    tableItem: [] as WeekTableList[],
    periodManageFlag: '',
    planTargetPeriodId: '',
  } as OrX0069OnewayType,
  syoriMonthOneway: {
    hideDetails: true,
    showNavigationButtons: false,
    textFieldwidth: '110',
  } as Mo01352OnewayType,
  // タブ
  mo00043OnewayType: {
    tabItems: [],
    minWidth: '58px',
  } as Mo00043OnewayType,
  Mo00045Oneway: {
    itemLabel: t('label.caseNo'),
    showItemLabel: true,
    isVerticalLabel: true,
    isRequired: false,
    width: '100',
    customClass: {
      outerClass: '',
      labelClass: 'mb-1',
    },
    maxLength: '10',
    rules: [byteLength(10)],
  } as Mo00045OnewayType,
  // KMD 李傑 ADD START
  or36086Data: {
    // 事業所ID
    shienId: 99,
    // 利用者ID
    userId: 18,
    // 当該年月
    yymmYm: getFormattedDate(),
    // 有効期間ID
    validPeriodId: 4,
    // 週以外フラグ
    isWeeklyFlg: true,
  } as Or36086OnewayType,
  or27501OnewayModel: {
    svJigyoId: '',
    shoriYymm: '',
    termid: '',
    siyouFlg: '',
    dwWeeks: [],
    dwRiyou: [],
    dwKakusyuu: [],
    dsTsukihi: [],
    userId: '',
    svJigyoIds: [],
    caller: '',
    torikomiKbn: '',
    copyTnki: '',
    selSyori: '',
    fygTani: '',
  } as Or27501OnewayType,
  or10828Oneway: {
    dailyscheduleImportType: {
      // 事業者ID
      jigyoId: '',
      // 施設ID
      shisetsuId: '',
      // 利用者ID
      userId: '',
      // 種別ID
      shubetsuId: '',
      // 計画期間ID
      sc1Id: '1',
    },
  } as Or10828OnewayType,
  // KMD 李傑 ADD END
  or29976Oneway: {
    rirekiList: [],
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
    svJigyoName: '',
    userList: [],
    kikanFlg: '',
    userId: '',
    sectionName: '',
    choIndex: '',
    sysCd: '',
    sysRyaku: '',
    shokuId: '',
    managerId: '',
    careManagerInChargeSettingsFlag: 0,
    processDate: '',
    week1Id: '',
    focusSettingInitial: [],
  } as Or29976OnewayType,
  // GUI00220 職員検索画面
  or26257: {
    sysCdKbn: '', // システム略称
    secAccountAllFlg: '0',
    svJigyoIdList: [],
    shokuinId: '',
    gsysCd: '', // システムコード
    selectMode: '12',
    kijunYmd: systemCommonsStore.getSystemDate ?? '',
    defSvJigyoId: '',
    filterDwFlg: '1',
    koyouState: '0',
    areaFlg: '0',
    hyoujiColumnList: [{ hyoujiColumn: 'shokushu_id' }],
    misetteiFlg: '1',
    otherRead: '', // 他職員参照権限
    refStopFlg: '0',
    syoriFlg: '', // 処理フラグ
    menu1Id: '', // メニュー１ID
    kensuFlg: '0',
    shokuinIdList: [], // 職員IDリスト
  } as Or26257OnewayType,
})
const local = reactive({
  mo01343: {
    value: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  } as Mo01343Type,
  orX0002historyFlgC: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
  or27509: {},
  or28820: {} as Or28820Type,
  orX0115: {},
  orX0069: {
    wIgaiKnj: '',
    weekData: [] as WeeklyPlanInput[],
    tabindex: '1',
  } as OrX0069Type,
  // 処理月
  syoriMonth: {
    value: '',
  } as Mo00020Type,
  // ケース
  caseNo: {
    value: '',
  } as Mo00045Type,
  // 計画対象期間情報ID
  planPeriodId: '0',
  // 計画期間ペイジ番号
  planNo: 0,
  // 計画期間総件数
  planCount: 0,
  // 履歴情報ID
  historyId: 0,
  // 履歴情報ペイジ番号
  historyNo: 0,
  // 履歴情報総件数
  historyCount: 0,
  // 作成者ID
  createUserId: '',
  // 作成者
  createUser: {} as OrX0157Type,
  // 作成日
  createDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  /** 職員検索 */
  or26257: {} as Or26257Type,
})

// エラーダイアログのPromise
let or21813ResolvePromise: (value: Or21813EventType) => void
// 確認ダイアログのPromise
let or21814ResolvePromise: (value: Or21814EventType) => void

// 共通情報
const commonInfoData = reactive({
  //事業所ID
  officeId: systemCommonsStore.getSvJigyoId ?? '',
  //利用者ID
  userId: systemCommonsStore.getUserSelectSelfId() ?? '',
  //施設ID
  shisetsuId: systemCommonsStore.getShisetuId ?? '',
  //種別ID
  // shubetsuId: systemCommonsStore.getSyubetu ?? '1',
  shubetsuId: '2',
  //事業者グループ適用ID
  officeGroupId: systemCommonsStore.getTekiyouGroupId ?? '',
  //適用事業所IDリスト
  svJigyoIdList: systemCommonsStore.getSvJigyoIdList as string[],
  // 法人ID
  houjinId: systemCommonsStore.getHoujinId ?? '',
  // 事業者ID
  svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
  // 計画対象期間ID
  sc1Id: '3',
})
// 表示フラグ
const localFlg = ref({
  // 計画対象期間表示表示フラグ
  showPlanningPeriodFlg: true,
  // 履歴表示フラグ
  showHistoryFlg: true,
  // 作成者表示フラグ
  showAuthorFlg: true,
  // 作成日表示フラグ
  showCreateDateFlg: true,
  // 処理年月表示フラグ
  showInsServiceFlg: true,
  // 処理年月表示フラグ
  showSyoriMonthFlg: true,
  // ケース番号表示フラグ
  showCaseFlg: true,
})
// 週間表の初期情報を取得する。
const weekTableData = ref<WeekTableSelectOutData>({
  // 週間表ID
  week1Id: '',
  // 期間管理フラグ
  kikanFlag: '',
  // 新規用有効期間ID
  termNewId: '',
  // 期間リスト
  taishokikanList: [],
  // 期間インデックス
  kikanIndex: '',
  // 期間総件数
  kikanAllCount: '',
  // 履歴リスト
  week1List: [],
  // 履歴インデックス
  rirekiIndex: '',
  // 履歴総件数
  rirekiAllCount: '',
  // 詳細リスト
  week2List: [],
  // 日課利用フラグ
  dayFlg: '',
  // 利用表利用フラグ
  riyoFlg: '',
})

// 有効期間ID
const termId = ref<string>('1')
// 履歴フラグ
const historyFlg = ref<string>(OrX0068Const.UPDATE_KBN_NONE)
// 更新区分
const updateKbn = ref<string>('')
const orX0069Ref = ref({
  initData: (_orX0069Oneway: OrX0069OnewayType) => {},
})
// 臨時有効期間ID
const tempValidId = ref<number>(-1)
// 処理年月日
const syoriYmd = ref<string>('')
// 有効期間ID
const validId = ref<number>(-1)
// ページアクション
const action = ref<string>('')
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<WeekTableSelectOutData>({
  cpId: OrX0068Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = {
  week1Id: '',
  kikanFlag: '',
  termNewId: '',
  taishokikanList: [],
  kikanIndex: '',
  kikanAllCount: '',
  week1List: [],
  rirekiIndex: '',
  rirekiAllCount: '',
  week2List: [],
  dayFlg: '',
  riyoFlg: '',
}
// 履歴選択画面ダイアログ表示フラグ
const showDialogOr28820 = computed(() => {
  // Or28820のダイアログ開閉状態
  return Or28820Logic.state.get(or28820.value.uniqueCpId)?.isOpen ?? false
})
// 週間表マスタ画面ダイアログ表示フラグ
const showDialogOr27509 = computed(() => {
  // Or27509のダイアログ開閉状態
  return Or27509Logic.state.get(or27509.value.uniqueCpId)?.isOpen ?? false
})
// 週間表パターン（設定）ダイアログ表示フラグ
const showDialogOr32387 = computed(() => {
  // Or32387のダイアログ開閉状態
  return Or32387Logic.state.get(or32387.value.uniqueCpId)?.isOpen ?? false
})
// 週間表マスタ画面ダイアログ表示フラグ
const showDialogOr29976 = computed(() => {
  // Or29976のダイアログ開閉状態
  return Or29976Logic.state.get(or29976.value.uniqueCpId)?.isOpen ?? false
})
// ［週間表複写］（週間表）ダイアログ表示フラグ
const showDialogOr32390 = computed(() => {
  // Or32390のダイアログ開閉状態
  return Or32390Logic.state.get(or32390.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or28820Const.CP_ID(0)]: or28820.value,
  [Or29976Const.CP_ID(0)]: or29976.value,
  [OrX0115Const.CP_ID(0)]: orX0115.value,
  [Or32390Const.CP_ID(0)]: or32390.value,
  [Or27509Const.CP_ID(0)]: or27509.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0069Const.CP_ID(0)]: orX0069.value,
  [Or41179Const.CP_ID(1)]: or41179_1.value,
  // KMD 李傑 ADD START
  [Or36086Const.CP_ID(0)]: or36086.value,
  [Or27501Const.CP_ID(0)]: or27501.value,
  [Or10828Const.CP_ID(0)]: or10828.value,
  // KMD 李傑 ADD END
  [Or26257Const.CP_ID(1)]: or26257.value,
})
useSetupChildProps(or32387.value.uniqueCpId, {
  [OrX0153Const.CP_ID(1)]: orX0153.value,
})
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.week-table'),
    showFavorite: true,
    showViewSelect: false,
    showCreateBtn: true,
    showCreateMenuCopy: true,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    viewSelectItems: [],
    showMasterBtn: true,
    showOptionMenuDelete: true,
    showSaveBtn: true,
  },
})
// const isInit = useScreenInitFlg()
onMounted(() => {
  // 事業所選択プルダウンの検索条件設定
  // 検索条件未指定だと、ヘッダーで選択した適用事業所が表示される
  // 検索条件が変更されると、共通処理にて事業所の情報が更新される
  // 更新後やプルダウンの値変更後、変更後の事業所に基づいて処理を行う場合、
  // 関数「 jigyoListWatch 」にコールバック関数を渡す
  // 初期情報取得
  // if (isInit) {
  localOneway.uniqueCpId = props.uniqueCpId
  localOneway.parentUniqueCpId = props.parentUniqueCpId
  updateJigyoList(or41179_1.value.uniqueCpId)
  if (systemCommonsStore.getUserSelectSelfId()) {
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
    // }
    // void initOr32382()
  }
})
// 変更されたリスニングのコンポーネントIDリスト
const watchedComponents = ref<string[]>([
  props.uniqueCpId,
  // or28820.value.uniqueCpId,
  // orX0115.value.uniqueCpId,
  // orX0069.value.uniqueCpId,
  // or11871.value.uniqueCpId,
  // or27509.value.uniqueCpId,
  // or29976.value.uniqueCpId,
  // or41179_1.value.uniqueCpId,
  // or26257.value.uniqueCpId,
])
//変更前の年月を保存する変数
const beforeYearMonth = ref('')
watch(
  () => local.syoriMonth,
  () => {
    formatDate()
    emit('update:modelValue', local.syoriMonth)
  },
  { deep: true }
)
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**
 * YYYY/MM/DD形式をYYYY/MM/DD形式に変更
 */
function formatDate() {
  if (isValidDateFormat(local.syoriMonth.value)) {
    const date = new Date(local.syoriMonth.value)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')

    local.syoriMonth.value = `${year}/${month}`

    beforeYearMonth.value = local.syoriMonth.value
  } else {
    local.syoriMonth.value = beforeYearMonth.value
  }
}
/**
 * 文字列の形式の判定
 * YYYY/MM/DD または YYYY/MM の形式であるか照合する
 *
 * @param str -照合対象の文字列
 */
function isValidDateFormat(str: string): boolean {
  // 正規表現(YYYY/MM/DD or YYYY/MM)
  const regex = /^(?:\d{4})\/(?:\d{2})\/(?:\d{2})$|^(?:\d{4})\/(?:\d{2})$/

  // 正規表現と指定された文字列を照合
  return regex.test(str)
}

/**
 * 初期表示
 */
async function initOr32382() {
  // 週間表の初期情報を取得する。
  await getWeekTableInitData()
  // 計画対象期間: 期間管理フラグが「0:管理しない」の場合、非表示
  localFlg.value.showPlanningPeriodFlg =
    weekTableData.value.kikanFlag !== OrX0068Const.PERIOD_MANAGEMENT_FLG.NO_MANAGEMENT
  // 期間管理フラグが「1:管理する」、かつ、計画期間が登録されていない場合
  if (
    weekTableData.value.kikanFlag === OrX0068Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT &&
    local.planCount === 0
  ) {
    // 履歴非表示
    localFlg.value.showHistoryFlg = false
    // 作成者非表示
    localFlg.value.showAuthorFlg = false
    // 作成日非表示
    localFlg.value.showCreateDateFlg = false
    // 処理年月非表示
    localFlg.value.showSyoriMonthFlg = false
    // ケース番号非表示
    localFlg.value.showCaseFlg = false
    // 週間表イメージ
    localOneway.orX0069Oneway.delFlg = true

    localOneway.orX0165Plan.iconLabel = t('label.planning-period-no-manage')
    localOneway.orX0165Plan.iconLabelFontSize = '11px'
    localOneway.orX0165Plan.iconLabelColor = 'rgb(var(--v-theme-red-700))'
    localOneway.orX0165Plan.pageLabel = '0' + t('label.slash-with-space') + '0'
  } else {
    // 履歴非表示
    localFlg.value.showHistoryFlg = true
    // 作成者非表示
    localFlg.value.showAuthorFlg = true
    // 作成日非表示
    localFlg.value.showCreateDateFlg = true
    // 処理年月非表示
    localFlg.value.showSyoriMonthFlg = true
    // ケース番号非表示
    localFlg.value.showCaseFlg = true
  }

  localOneway.orX0069Oneway.tableItem = refValue.value?.week2List ?? []
  localOneway.orX0069Oneway.wIgaiKnj =
    refValue.value?.week1List[local.historyCount - local.historyNo]?.wIgaiKnj ?? ''
  localOneway.orX0069Oneway.cpyFlg = false
  orX0069Ref.value?.initData(localOneway.orX0069Oneway)
}

/**
 * 週間表の初期情報を取得する。
 */
async function getWeekTableInitData() {
  // パラメータ
  const inParam: WeekTableInitInEntity = {
    // 施設ID
    shisetuId: commonInfoData.shisetsuId,
    // 事業者ID
    svJigyoId: commonInfoData.svJigyoId,
    // 利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? commonInfoData.userId,
    // 種別ID
    syubetsuId: commonInfoData.shubetsuId,
    // 計画対象期間ID
    sc1Id: commonInfoData.sc1Id,
    // 適用事業所ＩＤリスト
    jigyoList: commonInfoData.svJigyoIdList,
    // 適用事業所グループID
    jigyoGpId: commonInfoData.officeGroupId,
  }
  if (systemCommonsStore.getUserSelectSelfId() === '') {
    inParam.userId = commonInfoData.userId
  }
  // 週間表の初期情報を取得する。
  const ret: WeekTableInitOutEntity = await ScreenRepository.select(
    'weekTableImageInitSelect',
    inParam
  )
  await setScreenData(ret)
}
/**
 * 画面項目を取得したデータで設定する。
 *
 * @param ret - 初期情報
 */
async function setScreenData(ret: WeekTableInitOutEntity) {
  // 期間管理フラグ
  weekTableData.value.kikanFlag = ret.data.kikanFlag
  // 計画期間情報
  weekTableData.value.taishokikanList = ret.data.taishokikanList
  // 期間インデックス
  weekTableData.value.kikanIndex = ret.data.kikanIndex
  local.planNo = Number(ret.data.kikanIndex)
  // 期間総件数
  weekTableData.value.kikanAllCount = ret.data.kikanAllCount
  local.planCount = Number(ret.data.kikanAllCount)
  // 履歴情報
  weekTableData.value.week1List = ret.data.week1List
  // 履歴インデックス
  weekTableData.value.rirekiIndex = ret.data.rirekiIndex
  local.historyNo = Number(ret.data.rirekiIndex)
  // 履歴総件数
  weekTableData.value.rirekiAllCount = ret.data.rirekiAllCount
  local.historyCount = Number(ret.data.rirekiAllCount)
  if (
    weekTableData.value.week1List !== null &&
    weekTableData.value.week1List !== undefined &&
    weekTableData.value.week1List.length > 0
  ) {
    // 週間表ID
    weekTableData.value.week1Id =
      weekTableData.value.week1List[local.historyCount - local.historyNo].week1Id
    local.historyId = Number(weekTableData.value.week1Id)
  }
  // 詳細リスト設定処理
  setWeekList(ret.data.week2List)
  // 計画対象期間
  // 計画期間が登録されている場合、計画期間リスト1件目.開始日 + " ～ " + 計画期間リスト1件目.終了日
  if (
    weekTableData.value.taishokikanList !== null &&
    weekTableData.value.taishokikanList !== undefined &&
    weekTableData.value.taishokikanList.length !== 0
  ) {
    const planTargetPeriodNo =
      Number(weekTableData.value.kikanAllCount) - Number(weekTableData.value.kikanIndex)
    local.planPeriodId = weekTableData.value.taishokikanList[planTargetPeriodNo].sc1Id

    localOneway.orX0165Plan.iconLabel =
      weekTableData.value.taishokikanList[planTargetPeriodNo]?.startYmd +
      t('label.wavy') +
      weekTableData.value.taishokikanList[planTargetPeriodNo]?.endYmd
    localOneway.orX0165Plan.iconLabelFontSize = undefined
    localOneway.orX0165Plan.iconLabelColor = undefined
    localOneway.orX0165Plan.pageLabel =
      weekTableData.value.kikanIndex +
      t('label.slash-with-space') +
      weekTableData.value.kikanAllCount
  }
  // 週間表_履歴リストが0件
  if (weekTableData.value.week1List === null || weekTableData.value.week1List.length === 0) {
    // AC004-4を実行
    await createEmptyScreen()
  }
  await nextTick()

  refValue.value = JSON.parse(JSON.stringify(weekTableData.value)) as WeekTableSelectOutData
  setChildCpBinds(props.parentUniqueCpId, {
    OrX0068: {
      twoWayValue: JSON.parse(JSON.stringify(weekTableData.value)),
    },
  })
}
/**
 * 詳細リスト設定処理
 *
 * @param week2List - 詳細リスト
 */
function setWeekList(week2List: WeekList[]) {
  // 詳細リスト
  weekTableData.value.week2List = []
  for (const wtData of week2List) {
    // 週間表ID
    weekTableData.value.week1Id = wtData.week1Id
    weekTableData.value.week2List.push({
      // 詳細ID
      week2Id: wtData.week2Id,
      // 週間表ID
      week1Id: wtData.week1Id,
      // 具体的
      userid: wtData.userid,
      // 曜日
      youbi: { value: wtData.youbi },
      // 開始時間
      kaishiJikan: { value: wtData.kaishiJikan },
      // 終了時間
      shuuryouJikan: { value: wtData.shuuryouJikan },
      // 内容CD
      naiyoCd: { value: wtData.naiyoCd },
      // 内容
      naiyoKnj: { value: wtData.naiyoKnj },
      // メモ
      memoKnj: { value: wtData.memoKnj },
      // 文字サイズ
      fontSize: { value: wtData.fontSize },
      // 表示モード
      dispMode: { value: wtData.dispMode },
      // 文字位置
      alignment: { value: wtData.alignment },
      // サービス種類
      svShuruiCd: { value: wtData.svShuruiCd },
      // サービス項目（台帳）
      svItemCd: { value: wtData.svItemCd },
      // サービス事業者ＣＤ
      svJigyoId: { value: wtData.svJigyoId },
      // サービス事業者名
      svJigyoRks: { value: wtData.svJigyoRks },
      // 文字カラー
      fontColor: { value: wtData.fontColor },
      // 背景カラー
      backColor: { value: wtData.backColor },
      // 時間表示区分
      timeKbn: { value: wtData.timeKbn },
      // 週単位以外文字
      igaiMoji: { value: wtData.igaiMoji },
      // 週単位以外のサービス区分
      igaiKbn: { value: wtData.igaiKbn },
      // 週単位以外のサービス（日付指定）
      igaiDate: { value: wtData.igaiDate },
      // 週単位以外のサービス（曜日指定）
      igaiWeek: { value: wtData.igaiWeek },
      // 福祉用具貸与の単価
      svTani: wtData.svTani,
      // 福祉用具貸与マスタID
      fygId: wtData.fygId,
      // 枠外表示するかのフラグ
      wakugaiFlg: wtData.wakugaiFlg,
      // 更新区分
      updateKbn: wtData.updateKbn,
      // 加算リスト
      week3List: wtData.week3List,
    })
  }
  // 履歴
  if (
    weekTableData.value.week1List !== null &&
    weekTableData.value.week1List !== undefined &&
    weekTableData.value.week1List.length > 0
  ) {
    // 週間表_履歴リストデータが存在する場合、週間表_履歴リスト.順番No + " / " + 週間表_履歴リストの総件数
    local.historyNo = Number(weekTableData.value.rirekiIndex)
    local.historyCount = Number(weekTableData.value.rirekiAllCount)
  } else {
    // 上記以外の場合、"1 / 1"
    local.historyNo = 1
    local.historyCount = 1
  }
  localOneway.orX0165History.pageLabel =
    local.historyNo + t('label.slash-with-space') + local.historyCount

  if (
    weekTableData.value.week1List === null ||
    weekTableData.value.week1List === undefined ||
    weekTableData.value.week1List.length === 0 ||
    weekTableData.value.week1List.length === undefined ||
    action.value === OrX0068Const.PAGE_ACTION.NEW
  ) {
    // 作成者名: 履歴対象がNULL、又は、新規ボタン押下する場合、共通情報.ログインユーザー
    local.createUserId = systemCommonsStore.getCurrentUser.chkShokuId ?? '0'
    local.createUser.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
    // 作成日: 履歴対象がNULL、又は、新規ボタン押下する場合、共通情報.基準日
    local.createDate.value = systemCommonsStore.getSystemDate ?? ''
    action.value = ''
  } else {
    // 上記以外の場合、履歴対象.作成者
    local.createUserId = weekTableData.value.week1List[local.historyCount - local.historyNo].shokuId
    local.createUser.value =
      weekTableData.value.week1List[local.historyCount - local.historyNo].shokuKnj
    // 上記以外の場合、履歴対象.作成日
    local.createDate.value =
      weekTableData.value.week1List[local.historyCount - local.historyNo].createYmd
    // 上記以外の場合、履歴対象.当該年月
    local.syoriMonth.value =
      weekTableData.value.week1List[local.historyCount - local.historyNo].tougaiYm +
      OrX0068Const.FIRST_DAY
    // 上記以外の場合、履歴対象.ケースNo.
    local.caseNo.value = weekTableData.value.week1List[local.historyCount - local.historyNo].caseNo
  }
  localOneway.orX0069Oneway.delFlg = false
  localOneway.orX0069Oneway.tableItem = weekTableData.value.week2List
  localOneway.orX0069Oneway.wIgaiKnj =
    weekTableData.value.week1List[local.historyCount - local.historyNo]?.wIgaiKnj ?? ''
  localOneway.orX0069Oneway.cpyFlg = false
  orX0069Ref.value?.initData(localOneway.orX0069Oneway)
}

/**
 * 画面項目を取得したデータで設定する。
 *
 * @param ret - 初期情報
 */
function setScreenDataPeriod(ret: WeekTablePeriodSelectOutEntity) {
  // 計画期間情報
  weekTableData.value.taishokikanList = ret.data.taishokikanList
  // 期間インデックス
  weekTableData.value.kikanIndex = ret.data.kikanIndex
  local.planNo = Number(ret.data.kikanIndex)
  // 期間総件数
  weekTableData.value.kikanAllCount = ret.data.kikanAllCount
  local.planCount = Number(ret.data.kikanAllCount)
  // 履歴情報
  weekTableData.value.week1List = ret.data.week1List
  // 履歴インデックス
  weekTableData.value.rirekiIndex = ret.data.rirekiIndex
  local.historyNo = Number(ret.data.rirekiIndex)
  // 履歴総件数
  weekTableData.value.rirekiAllCount = ret.data.rirekiAllCount
  local.historyCount = Number(ret.data.rirekiAllCount)
  if (
    weekTableData.value.week1List !== null &&
    weekTableData.value.week1List !== undefined &&
    weekTableData.value.week1List.length > 0
  ) {
    // 週間表ID
    weekTableData.value.week1Id =
      weekTableData.value.week1List[local.historyCount - local.historyNo].week1Id
    local.historyId = Number(weekTableData.value.week1Id)
  }

  // 詳細リスト設定処理
  setWeekList(ret.data.week2List)
  // 計画対象期間
  // 計画期間が登録されている場合、計画期間リスト1件目.開始日 + " ～ " + 計画期間リスト1件目.終了日
  if (
    weekTableData.value.taishokikanList !== null &&
    weekTableData.value.taishokikanList !== undefined
  ) {
    local.planPeriodId = weekTableData.value.taishokikanList[local.planCount - local.planNo].sc1Id
    localOneway.orX0165Plan.iconLabel =
      weekTableData.value.taishokikanList[local.planCount - local.planNo]?.startYmd +
      t('label.wavy') +
      weekTableData.value.taishokikanList[local.planCount - local.planNo]?.endYmd
    localOneway.orX0165Plan.iconLabelFontSize = undefined
    localOneway.orX0165Plan.iconLabelColor = undefined
    localOneway.orX0165Plan.pageLabel =
      weekTableData.value.kikanIndex +
      t('label.slash-with-space') +
      weekTableData.value.kikanAllCount
  }
}

/**
 * 週間表計画対象変更処理
 *
 * @param periodPage - 計画期間ページ区分
 */
async function weekTablePeriodUpdate(periodPage: string) {
  const param: WeekTablePeriodSelectInEntity = {
    // 施設ID
    shisetuId: commonInfoData.shisetsuId,
    // 利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? commonInfoData.userId,
    // 事業者ID
    svJigyoId: commonInfoData.svJigyoId,
    // 種別ID
    syubetsuId: commonInfoData.shubetsuId,
    // 計画対象期間ID
    sc1Id: String(local.planPeriodId),
    // 計画期間ページ区分
    kikanFlag: periodPage,
  }
  // 週間表計画期間変更処理を行う。
  const ret: WeekTablePeriodSelectOutEntity = await ScreenRepository.select(
    'weekTableImagePlanPeriodSelect',
    param
  )
  updateKbn.value = UPDATE_KBN.UPDATE
  setScreenDataPeriod(ret)
}

/**
 * 週間表履歴変更処理
 *
 * @param rirekiPage - 履歴変更区分
 */
async function weekTableHistUpdate(rirekiPage: string) {
  const param: WeekTableHistorySelectInEntity = {
    // 施設ID
    shisetuId: commonInfoData.shisetsuId,
    // 利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? commonInfoData.userId,
    // 事業者ID
    svJigyoId: commonInfoData.svJigyoId,
    // 計画対象期間ID
    sc1Id: String(local.planPeriodId),
    // 週間表ID
    week1Id: String(local.historyId),
    // 履歴変更区分
    kikanFlag: rirekiPage,
  }
  // 週間表計画期間変更処理を行う。
  const ret: WeekTableHistorySelectOutEntity = await ScreenRepository.select(
    'weekTableImageHistorySelect',
    param
  )
  updateKbn.value = UPDATE_KBN.UPDATE
  setScreenDataHistory(ret)
}
/**
 * 画面項目を取得したデータで設定する。
 *
 * @param ret - 履歴変更情報
 */
function setScreenDataHistory(ret: WeekTableHistorySelectOutEntity) {
  // 履歴情報
  weekTableData.value.week1List = ret.data.week1List
  // 履歴インデックス
  weekTableData.value.rirekiIndex = ret.data.rirekiIndex
  local.historyNo = Number(ret.data.rirekiIndex)
  // 履歴総件数
  weekTableData.value.rirekiAllCount = ret.data.rirekiAllCount
  local.historyCount = Number(ret.data.rirekiAllCount)
  if (
    weekTableData.value.week1List !== null &&
    weekTableData.value.week1List !== undefined &&
    weekTableData.value.week1List.length > 0
  ) {
    const historyNo =
      Number(weekTableData.value.rirekiAllCount) - Number(weekTableData.value.rirekiIndex)
    // 週間表ID
    weekTableData.value.week1Id = weekTableData.value.week1List[historyNo].week1Id
    local.historyId = Number(weekTableData.value.week1Id)
  }

  // 詳細リスト設定処理
  setWeekList(ret.data.week2List)
}

/**
 * 「新規ボタン」押下
 */
async function onClickCreate() {
  action.value = OrX0068Const.PAGE_ACTION.NEW
  // 期間管理フラグが「管理する」の場合、かつ、計画対象期間リストが0件
  if (
    refValue.value?.kikanFlag === OrX0068Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT &&
    (refValue.value?.taishokikanList === null || refValue.value?.taishokikanList === undefined)
  ) {
    // 以下のメッセージを表示: e.cmn.40980
    const rs = await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40980'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンタイプ
      secondBtnType: 'blank',
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    // はい：AC011を実行
    if (rs.firstBtnClickFlg) {
      const isEdit = isEditNavControl(watchedComponents.value)
      // 画面入力データに変更がある場合
      if (isEdit) {
        // AC004-2と同じ
        const rs = await showMessageICmn10430()
        if (rs.firstBtnClickFlg) {
          // はいの場合
          // AC003を実行し、処理続き
          await onClickSave()
        } else if (rs.thirdBtnClickFlg) {
          return
        }
      }
      // 週間表計画期間履歴処理を行う。
      await weekTablePeriodUpdate(OrX0068Const.PERIOD_CHANGE_KBN.SELECTED_ID)
    }
  } else {
    // 画面入力データの変更がある場合
    const isEdit = isEditNavControl(watchedComponents.value)
    if (isEdit) {
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        // はいの場合
        // AC003を実行し、処理続き
        await onClickSave()
        // 新規空白画面を作成し
        await createEmptyScreen()
      } else if (rs.secondBtnClickFlg) {
        // いいえの場合
        //  履歴更新区分="C"(新規)の場合
        if (historyFlg.value === OrX0068Const.HISTORY_UPD_KBN.C) {
          // 以下のメッセージを表示: i.cmn.11265
          await showMessageICmn11265()
        } else {
          await createEmptyScreen()
        }
      }
    } else {
      //  履歴更新区分="C"(新規)の場合
      if (historyFlg.value === OrX0068Const.HISTORY_UPD_KBN.C) {
        // 以下のメッセージを表示: i.cmn.11265
        await showMessageICmn11265()
      } else {
        await createEmptyScreen()
      }
    }
  }
}
/**
 * 新規空白画面を作成
 */
async function createEmptyScreen() {
  // 新規空白画面を作成し、デーフォルトデータを画面対応の項目に設定する。
  refValue.value!.week2List = []
  refValue.value!.kikanFlag = ''
  refValue.value!.week1List = []
  localComponents.orX0004 = defaultComponents.orX0004
  localComponents.orX0005 = defaultComponents.orX0005
  localComponents.or21828 = defaultComponents.or21828
  localComponents.or21830 = defaultComponents.or21830
  localComponents.orX0124 = defaultComponents.orX0124

  // ・作成日 ＝ 引継情報.基準日(画面項目定義書第2階層の日付)
  local.createDate.value = systemCommonsStore.getSystemDate ?? ''
  // ・作成者 ＝ 共通情報.ログイン職員情報.職員ID対応の職員を設定する。
  local.createUserId = systemCommonsStore.getCurrentUser.chkShokuId ?? '0'
  local.createUser.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
  // ・履歴ページングは履歴の最大値+1/履歴の最大値+1を設定する。
  local.historyNo = Number(weekTableData.value.rirekiAllCount) + 1
  local.historyCount = Number(weekTableData.value.rirekiAllCount) + 1

  localOneway.orX0165History.pageLabel =
    local.historyNo + t('label.slash-with-space') + local.historyCount
  // ・履歴更新区分 = "C"
  historyFlg.value = OrX0068Const.HISTORY_UPD_KBN.C
  // 履歴対象.処理月.
  local.syoriMonth.value = ""
  // 履歴対象.ケースNo.
  local.caseNo.value = ""

  weekTableData.value.week2List = []

  await nextTick()
  refValue.value = JSON.parse(JSON.stringify(weekTableData.value)) as WeekTableSelectOutData
  // setChildCpBinds(localOneway.parentUniqueCpId, {
  //   OrX0068: {
  //     twoWayValue: JSON.parse(JSON.stringify(weekTableData.value)),
  //   },
  // })
  localOneway.orX0069Oneway.delFlg = false
  localOneway.orX0069Oneway.tableItem = weekTableData.value.week2List
  localOneway.orX0069Oneway.wIgaiKnj = ''
  localOneway.orX0069Oneway.cpyFlg = false
  orX0069Ref.value?.initData(localOneway.orX0069Oneway)
}
/**
 * 「複写ボタン」押下
 */
function onClickCpy() {
  localOneway.Or32390Oneway.svJigyoId = commonInfoData.svJigyoId
  localOneway.Or32390Oneway.shisetuId = commonInfoData.shisetsuId
  localOneway.Or32390Oneway.userId =
    systemCommonsStore.getUserSelectSelfId() ?? commonInfoData.userId
  localOneway.Or32390Oneway.syubetsuId = commonInfoData.shubetsuId
  // 一覧複写画面をポップアップで起動する。
  Or32390Logic.state.set({
    uniqueCpId: or32390.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
function isEditNavControl(components: string[]) {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()

  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                console.log(key, cpKey, itemKey)
                return true
              }
            }
          }
        }
      }
    }
  }
  return false
}
/**
 * 「印刷設定」押下
 */
async function onClickPrintConfig() {
  // AC004-2と同じ
  // 画面入力データの変更がある場合
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      await onClickSave()
    }
  }

  const sectionName = t('label.week-service-plan-table')

  localOneway.or29976Oneway.sectionName = sectionName

  // GUI00986 印刷設定画面をポップアップで起動する。
  // Or29976のダイアログ開閉状態を更新する
  Or29976Logic.state.set({
    uniqueCpId: or29976.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 「週間表マスタアイコンボタン」押下
 */
async function onWeekTableMasterClick() {
  // 画面入力データに変更がある場合	-
  // AC004-2と同じ
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      await onClickSave()
    }
  }
  local.or27509 = ref<Or27509Type>({
    data: '',
    officeData: { officeId: '1', officeName: '特別養護 ほのぼの2' },
  })
  // GUI00977_週間表マスタ画面をポップアップで起動する。
  Or27509Logic.state.set({
    uniqueCpId: or27509.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 「オプションア」＞「削除」押下
 */
async function onOrX0124Delete() {
  // 以下のメッセージを表示: i.cmn.11326
  // 現在表示している{0}の{1}のデータを削除してもよろしいですか？
  // {0}:画面.作成日
  // {1}:週間表
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11326', [local.createDate.value, t('label.week-table')]),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'normal3',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  })
  if (rs.firstBtnClickFlg) {
    localOneway.orX0069Oneway.delFlg = true
    historyFlg.value = OrX0068Const.UPDATE_KBN_D
    localOneway.orX0157CreateUser.text!.orX0157InputOneway.disabled = true
    orX0069Ref.value.initData(localOneway.orX0069Oneway)
  }
}
/**
 * 「オプションア」＞「パターン」押下
 */
async function onOrX0124Pattern() {
  // 共通情報.利用者IDが0の場合、or、週間表履歴が0件
  if (
    refValue.value?.week1List === null ||
    refValue.value?.week1List === undefined ||
    historyFlg.value === OrX0068Const.UPDATE_KBN_D
  ) {
    return
  } else {
    // 週間表リスト
    for (const data of weekTableData.value.week2List) {
      const kaishiJikan = data.kaishiJikan?.value ?? ''
      const shuuryouJikan = data.shuuryouJikan?.value ?? ''
      const youbi = data.youbi?.value ?? ''
      if (kaishiJikan.substring(1, 2) === OrX0068Const.MAX_HOUR) {
        if (kaishiJikan.substring(3, 4) > OrX0068Const.MAX_TIME) {
          // 以下のメッセージを表示: e.cmn.41728
          const rs = await openErrorDialog(or21813.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.error'),
            // ダイアログテキスト
            dialogText: t('message.e-cmn-41728'),
            // 第1ボタンタイプ
            firstBtnType: 'normal1',
            // 第1ボタンラベル
            firstBtnLabel: t('btn.yes'),
            // 第2ボタンタイプ
            secondBtnType: 'blank',
            // 第3ボタンタイプ
            thirdBtnType: 'blank',
          })
          if (rs.firstBtnClickFlg) {
            return
          }
        }
        if (shuuryouJikan > OrX0068Const.END_TIME_FIRST && kaishiJikan < shuuryouJikan) {
          // 以下のメッセージを表示: e.cmn.41729
          const rs = await openErrorDialog(or21813.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.error'),
            // ダイアログテキスト
            dialogText: t('message.e-cmn-41729'),
            // 第1ボタンタイプ
            firstBtnType: 'normal1',
            // 第1ボタンラベル
            firstBtnLabel: t('btn.yes'),
            // 第2ボタンタイプ
            secondBtnType: 'blank',
            // 第3ボタンタイプ
            thirdBtnType: 'blank',
          })
          if (rs.firstBtnClickFlg) {
            return
          }
        }
      }
      if (shuuryouJikan > OrX0068Const.END_TIME_LAST) {
        // 以下のメッセージを表示: e.cmn.41730
        const rs = await openErrorDialog(or21813.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.error'),
          // ダイアログテキスト
          dialogText: t('message.e-cmn-41730'),
          // 第1ボタンタイプ
          firstBtnType: 'normal1',
          // 第1ボタンラベル
          firstBtnLabel: t('btn.yes'),
          // 第2ボタンタイプ
          secondBtnType: 'blank',
          // 第3ボタンタイプ
          thirdBtnType: 'blank',
        })
        if (rs.firstBtnClickFlg) {
          return
        }
      }
      if (youbi === OrX0068Const.YOUBI_OTHER) {
        // 以下のメッセージを表示: e.cmn.40740
        const rs = await openErrorDialog(or21813.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.error'),
          // ダイアログテキスト
          dialogText: t('message.e-cmn-40740'),
          // 第1ボタンタイプ
          firstBtnType: 'normal1',
          // 第1ボタンラベル
          firstBtnLabel: t('btn.yes'),
          // 第2ボタンタイプ
          secondBtnType: 'blank',
          // 第3ボタンタイプ
          thirdBtnType: 'blank',
        })
        if (rs.firstBtnClickFlg) {
          return
        }
      }
    }
    localOneway.Or32387Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? commonInfoData.userId
    localOneway.Or32387Oneway.termid = termId.value
    localOneway.Or32387Oneway.week2List = []
    for (const data of weekTableData.value.week2List) {
      // 週間表リスト
      localOneway.Or32387Oneway.week2List.push({
        // 詳細ID
        week2Id: data.week2Id ?? '',
        // 週間表ID
        week1Id: data.week1Id ?? '',
        // 曜日
        youbi: data.youbi?.value ?? '',
        // 開始時間
        kaishiJikan: data.kaishiJikan?.value ?? '',
        // 終了時間
        shuuryouJikan: data.shuuryouJikan?.value ?? '',
        // 内容CD
        naiyoCd: data.naiyoCd?.value ?? '',
        // 内容
        naiyoKnj: data.naiyoKnj?.value ?? '',
        // メモ
        memoKnj: data.memoKnj?.value ?? '',
        // 文字サイズ
        fontSize: data.fontSize?.value ?? '',
        // 表示モード
        dispMode: data.dispMode?.value ?? '',
        // 文字位置
        alignment: data.alignment?.value ?? '',
        // サービス種類
        svShuruiCd: data.svShuruiCd?.value ?? '',
        // サービス項目（台帳）
        svItemCd: data.svItemCd?.value ?? '',
        // サービス事業者CD
        svJigyoId: data.svJigyoId?.value ?? '',
        // サービス種類名称
        svShuruiKnj: data.svShuruiKnj?.value ?? '',
        // サービス項目名称
        svItemKnj: data.svItemKnj?.value ?? '',
        // サービス事業者名称
        svJigyoKnj: data.svJigyoKnj?.value ?? '',
        // サービス事業者略称
        svJigyoRks: data.svJigyoRks?.value ?? '',
        // 文字カラー
        fontColor: data.fontColor?.value ?? '',
        // 背景カラー
        backColor: data.backColor?.value ?? '',
        // 時間表示区分
        timeKbn: data.timeKbn?.value ?? '',
        // 週単位以外文字
        igaiMoji: data.igaiMoji?.value ?? '',
        // 週単位以外のサービス区分
        igaiKbn: data.igaiKbn?.value ?? '',
        // 週単位以外のサービス（日付指定）
        igaiDate: data.igaiDate?.value ?? '',
        // 週単位以外のサービス（曜日指定）
        igaiWeek: data.igaiWeek?.value ?? '',
        // 加算リスト
        week3List: [],
      })
    }
    Or32387Logic.state.set({
      uniqueCpId: or32387.value.uniqueCpId,
      state: { isOpen: true },
    })

    const param: WeekTablePatternSelectInEntity = {
      // 週間表ID
      week1Id: String(local.historyId),
      // 有効期間ID
      termid: termId.value,
      // 当該年月
      tougaiYm: local.syoriMonth.value,
    }
    // 週間表計画期間変更処理を行う。
    const ret: WeekTablePatternSelectOutEntity = await ScreenRepository.select(
      'weekTableImagePatternSelect',
      param
    )
    updateKbn.value = UPDATE_KBN.UPDATE
    setScreenDataPattern(ret)
  }
}
/**
 * 画面項目を取得したデータで設定する。
 *
 * @param ret - 複写情報
 */
function setScreenDataPattern(ret: WeekTablePatternSelectOutEntity) {
  // 詳細リスト設定処理
  setWeekListPattern(ret.data.week2List)
  localOneway.orX0069Oneway.wIgaiKnj = ret.data.wIgaiKnj
}
/**
 * 詳細リスト設定処理
 *
 * @param week2List - 詳細リスト
 */
function setWeekListPattern(week2List: WeekListPattern[]) {
  // 詳細リスト
  weekTableData.value.week2List = []
  for (const wtData of week2List) {
    // 週間表ID
    weekTableData.value.week1Id = wtData.week1Id
    weekTableData.value.week2List.push({
      // 詳細ID
      week2Id: wtData.week2Id,
      // 週間表ID
      week1Id: wtData.week1Id,
      // 具体的
      userid: systemCommonsStore.getUserSelectSelfId() ?? commonInfoData.userId,
      // 曜日
      youbi: { value: wtData.youbi },
      // 開始時間
      kaishiJikan: { value: wtData.kaishiJikan },
      // 終了時間
      shuuryouJikan: { value: wtData.shuuryouJikan },
      // 内容CD
      naiyoCd: { value: wtData.naiyoCd },
      // 内容
      naiyoKnj: { value: wtData.naiyoKnj },
      // メモ
      memoKnj: { value: wtData.memoKnj },
      // 文字サイズ
      fontSize: { value: wtData.fontSize },
      // 表示モード
      dispMode: { value: wtData.dispMode },
      // 文字位置
      alignment: { value: wtData.alignment },
      // サービス種類
      svShuruiCd: { value: wtData.svShuruiCd },
      // サービス項目（台帳）
      svItemCd: { value: wtData.svItemCd },
      // サービス事業者ＣＤ
      svJigyoId: { value: wtData.svJigyoId },
      // サービス事業者名
      svJigyoRks: { value: wtData.svJigyoRks },
      // 文字カラー
      fontColor: { value: wtData.fontColor },
      // 背景カラー
      backColor: { value: wtData.backColor },
      // 時間表示区分
      timeKbn: { value: wtData.timeKbn },
      // 週単位以外文字
      igaiMoji: { value: wtData.igaiMoji },
      // 週単位以外のサービス区分
      igaiKbn: { value: wtData.igaiKbn },
      // 週単位以外のサービス（日付指定）
      igaiDate: { value: wtData.igaiDate },
      // 週単位以外のサービス（曜日指定）
      igaiWeek: { value: wtData.igaiWeek },
      // 福祉用具貸与の単価
      svTani: '',
      // 福祉用具貸与マスタID
      fygId: '',
      // 枠外表示するかのフラグ
      wakugaiFlg: '',
      // 更新区分
      updateKbn: '',
      // 加算リスト
      week3List: [],
    })
  }
  // 履歴
  if (weekTableData.value.week1List !== null) {
    // 週間表_履歴リストデータが存在する場合、週間表_履歴リスト.順番No + " / " + 週間表_履歴リストの総件数
    local.historyNo = Number(weekTableData.value.rirekiIndex)
    local.historyCount = Number(weekTableData.value.rirekiAllCount)
  } else {
    // 上記以外の場合、"1 / 1"
    local.historyNo = 1
    local.historyCount = 1
  }

  localOneway.orX0165History.pageLabel =
    local.historyNo + t('label.slash-with-space') + local.historyCount

  if (weekTableData.value.week1List === null || action.value === OrX0068Const.PAGE_ACTION.NEW) {
    // 作成者名: 履歴対象がNULL、又は、新規ボタン押下する場合、共通情報.ログインユーザー
    local.createUserId = systemCommonsStore.getCurrentUser.chkShokuId ?? '0'
    local.createUser.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
    // 作成日: 履歴対象がNULL、又は、新規ボタン押下する場合、共通情報.基準日
    local.createDate.value = systemCommonsStore.getSystemDate ?? ''
    action.value = ''
  } else {
    // 上記以外の場合、履歴対象.作成者
    local.createUserId = weekTableData.value.week1List[local.historyCount - local.historyNo].shokuId
    local.createUser.value =
      weekTableData.value.week1List[local.historyCount - local.historyNo].shokuKnj
    // 上記以外の場合、履歴対象.作成日
    local.createDate.value =
      weekTableData.value.week1List[local.historyCount - local.historyNo].createYmd
    // 上記以外の場合、履歴対象.当該年月
    local.syoriMonth.value =
      weekTableData.value.week1List[local.historyCount - local.historyNo].tougaiYm + '/01'
    // 上記以外の場合、履歴対象.ケースNo.
    local.caseNo.value = weekTableData.value.week1List[local.historyCount - local.historyNo].caseNo
  }
  localOneway.orX0069Oneway.delFlg = false
  localOneway.orX0069Oneway.tableItem = weekTableData.value.week2List
  localOneway.orX0069Oneway.wIgaiKnj =
    weekTableData.value.week1List[local.historyCount - local.historyNo]?.wIgaiKnj ?? ''
  orX0069Ref.value?.initData(localOneway.orX0069Oneway)
}
/**
 * 「履歴選択選択アイコンボタン」押下
 *
 * @param skipEditFlg - 画面入力データ変更判定スキップフラグ
 */
async function onClickHistIconBtn(skipEditFlg?: boolean) {
  // 画面入力データに変更がある場合
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit && (skipEditFlg === undefined || skipEditFlg === false)) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // はいの場合
      // AC003を実行し、処理続き
      await onClickSave()
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }
  // GUI00985_［履歴選択］画面 週間表画面をポップアップで起動する。
  // 入力パラメータ: 共通情報.事業所ID, 画面.計画対象期間ID, 共通情報.利用者ID
  localOneway.or28820Oneway.svJigyoId = commonInfoData.shubetsuId
  localOneway.or28820Oneway.sc1Id = String(local.planPeriodId)
  localOneway.or28820Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? commonInfoData.userId
  Or28820Logic.state.set({
    uniqueCpId: or28820.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「履歴-前へアイコンボタン」押下
 */
async function onClickPreHistBtn() {
  // 1件目の履歴データが表示されている状態
  if (local.historyNo === 1) {
    // 処理終了にする。
    return
  }
  const isEdit = isEditNavControl(watchedComponents.value)
  // 画面入力データに変更がある場合
  if (isEdit) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // はいの場合
      // AC003を実行し、処理続き
      await onClickSave()
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }
  // 週間表計画期間履歴処理を行う。
  await weekTableHistUpdate(OrX0068Const.HISTORY_CHANGE_KBN.BEFORE_SELECTED_ID)
}
/**
 * 「履歴-次へアイコンボタン」押下
 */
async function onClickNextHistBtn() {
  // 最終件目の履歴データが表示されている状態
  if (local.historyNo === local.historyCount) {
    // 処理終了にする。
    return
  }
  const isEdit = isEditNavControl(watchedComponents.value)
  // 画面入力データに変更がある場合
  if (isEdit) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // はいの場合
      // AC003を実行し、処理続き
      await onClickSave()
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }
  // 週間表計画期間履歴処理を行う。
  await weekTableHistUpdate(OrX0068Const.HISTORY_CHANGE_KBN.AFTER_SELECTED_ID)
}

/**
 * 「保存ボタン」押下
 *
 */
async function onClickSave() {
  const isEdit = isEditNavControl(watchedComponents.value)
  // 画面の入力データが変更されない、或いは、計画対象期間.計画対象期間件数が0の場合
  if (Number(refValue.value?.kikanAllCount) === 0) {
  // if (!isEdit || Number(refValue.value?.kikanAllCount) === 0) {
    // 処理終了にする
    await openConfirmDialog(or21814.value.uniqueCpId, {
      dialogText: t('message.i-cmn-21800'),
    })
    // 処理終了にする
    return
  }
  // 週間計画履歴.当該年月が空白の場合
  if (refValue.value?.week1List[0]?.tougaiYm === '') {
    // 変数.臨時有効期間ID＝週間計画履歴.有効期間ID
    tempValidId.value = parseInt(refValue.value?.week1List[0]?.termid)
  } else {
    // 変数.処理年月日＝週間計画履歴.当該年月+"/01"
    syoriYmd.value = refValue.value?.week1List[0]?.tougaiYm + OrX0068Const.FIRST_DAY
  }
  // 変数.臨時有効期間ID＝11
  tempValidId.value = 11
  // 変数.処理年月日≦2023/03/31の場合
  if (new Date(syoriYmd.value) <= new Date(OrX0068Const.VALID_1_TO)) {
    // 変数.＝1
    tempValidId.value = 1
  }
  // 2003/04/01≦変数.処理年月日≦2005/09/30の場合
  if (
    new Date(syoriYmd.value) >= new Date(OrX0068Const.VALID_2_FROM) &&
    new Date(syoriYmd.value) <= new Date(OrX0068Const.VALID_2_TO)
  ) {
    // 変数.臨時有効期間ID＝2
    tempValidId.value = 2
  }
  // 2005/10/01≦変数.処理年月日≦2006/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(OrX0068Const.VALID_3_FROM) &&
    new Date(syoriYmd.value) <= new Date(OrX0068Const.VALID_3_TO)
  ) {
    // 変数.臨時有効期間ID＝3
    tempValidId.value = 3
  }
  // 2006/04/01≦変数.処理年月日≦2009/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(OrX0068Const.VALID_4_FROM) &&
    new Date(syoriYmd.value) <= new Date(OrX0068Const.VALID_4_TO)
  ) {
    // 変数.臨時有効期間ID＝4
    tempValidId.value = 4
  }
  // 2009/04/01≦変数.処理年月日≦2012/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(OrX0068Const.VALID_5_FROM) &&
    new Date(syoriYmd.value) <= new Date(OrX0068Const.VALID_5_TO)
  ) {
    // 変数.臨時有効期間ID＝5
    tempValidId.value = 5
  }
  // 2012/04/01≦変数.処理年月日≦2014/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(OrX0068Const.VALID_6_FROM) &&
    new Date(syoriYmd.value) <= new Date(OrX0068Const.VALID_6_TO)
  ) {
    // 変数.臨時有効期間ID＝6
    tempValidId.value = 6
  }
  // 2014/04/01≦変数.処理年月日≦2015/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(OrX0068Const.VALID_7_FROM) &&
    new Date(syoriYmd.value) <= new Date(OrX0068Const.VALID_7_TO)
  ) {
    // 変数.臨時有効期間ID＝7
    tempValidId.value = 7
  }
  // 2015/04/01≦変数.処理年月日≦2017/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(OrX0068Const.VALID_8_FROM) &&
    new Date(syoriYmd.value) <= new Date(OrX0068Const.VALID_8_TO)
  ) {
    // 変数.臨時有効期間ID＝8
    tempValidId.value = 8
  }
  // 2017/04/01≦変数.処理年月日≦2018/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(OrX0068Const.VALID_9_FROM) &&
    new Date(syoriYmd.value) <= new Date(OrX0068Const.VALID_9_TO)
  ) {
    // 変数.臨時有効期間ID＝9
    tempValidId.value = 9
  }
  // 2018/04/01≦変数.処理年月日≦2019/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(OrX0068Const.VALID_10_FROM) &&
    new Date(syoriYmd.value) <= new Date(OrX0068Const.VALID_10_TO)
  ) {
    // 変数.臨時有効期間ID＝10
    tempValidId.value = 10
  }
  // 変数.臨時有効期間ID＜＞変数.有効期間IDの場合
  if (
    tempValidId.value !== validId.value &&
    refValue.value?.week2List?.length !== 0 &&
    validId.value === 1
  ) {
    // ■以下のメッセージを表示
    // id: w.cmn.20581
    // ・メッセージ内容
    // 平成15年3月以前の計画はサービスの有効期間を変更できません。
    // OK：以降の処理を行わない。
    Or21815Logic.state.set({
      uniqueCpId: or21815.value.uniqueCpId,
      state: {
        dialogTitle: t('label.warning'),
        dialogText: t('message.w.cmn.20581'),
        firstBtnType: 'blank',
        secondBtnType: 'blank',
        thirdBtnType: 'normal1',
        thirdBtnLabel: t('btn.ok'),
      },
    })
    Or21815Logic.state.set({
      uniqueCpId: or21815.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }

  if (
    tempValidId.value !== validId.value &&
    refValue.value?.week2List?.length !== 0 &&
    validId.value === 1
  ) {
    // ■以下のメッセージを表示
    // id: i.cmn.11309
    // ・メッセージ内容
    // サービスの有効期間を[改行]
    // {0}[改行]
    // から[改行]
    // {1}[改行]
    // に変更します。サービスによっては名称、単価等が変更される場合があります。[改行]
    // 変更しますか？[改行]
    // [改行]
    // はい：処理を続き
    // いいえ：以降の処理を行わない。
    const rs = await showMessageICmn11309()
    if (rs.thirdBtnClickFlg) {
      return
    }
  }

  // 週間表リスト
  for (const data of weekTableData.value.week2List) {
    const kaishiJikan = data.kaishiJikan?.value ?? ''
    const shuuryouJikan = data.shuuryouJikan?.value ?? ''
    const youbi = data.youbi?.value ?? ''
    if (kaishiJikan.substring(1, 2) === OrX0068Const.MAX_HOUR) {
      if (kaishiJikan.substring(3, 4) > OrX0068Const.MAX_TIME) {
        // 以下のメッセージを表示: e.cmn.41728
        const rs = await openErrorDialog(or21813.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.error'),
          // ダイアログテキスト
          dialogText: t('message.e-cmn-41728'),
          // 第1ボタンタイプ
          firstBtnType: 'normal1',
          // 第1ボタンラベル
          firstBtnLabel: t('btn.yes'),
          // 第2ボタンタイプ
          secondBtnType: 'blank',
          // 第3ボタンタイプ
          thirdBtnType: 'blank',
        })
        if (rs.firstBtnClickFlg) {
          return
        }
      }
      if (shuuryouJikan > OrX0068Const.END_TIME_FIRST && kaishiJikan < shuuryouJikan) {
        // 以下のメッセージを表示: e.cmn.41729
        const rs = await openErrorDialog(or21813.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.error'),
          // ダイアログテキスト
          dialogText: t('message.e-cmn-41729'),
          // 第1ボタンタイプ
          firstBtnType: 'normal1',
          // 第1ボタンラベル
          firstBtnLabel: t('btn.yes'),
          // 第2ボタンタイプ
          secondBtnType: 'blank',
          // 第3ボタンタイプ
          thirdBtnType: 'blank',
        })
        if (rs.firstBtnClickFlg) {
          return
        }
      }
    }
    if (shuuryouJikan > OrX0068Const.END_TIME_LAST) {
      // 以下のメッセージを表示: e.cmn.41730
      const rs = await openErrorDialog(or21813.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.error'),
        // ダイアログテキスト
        dialogText: t('message.e-cmn-41730'),
        // 第1ボタンタイプ
        firstBtnType: 'normal1',
        // 第1ボタンラベル
        firstBtnLabel: t('btn.yes'),
        // 第2ボタンタイプ
        secondBtnType: 'blank',
        // 第3ボタンタイプ
        thirdBtnType: 'blank',
      })
      if (rs.firstBtnClickFlg) {
        return
      }
    }
    if (youbi === OrX0068Const.YOUBI_OTHER) {
      // 以下のメッセージを表示: e.cmn.40740
      const rs = await openErrorDialog(or21813.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.error'),
        // ダイアログテキスト
        dialogText: t('message.e-cmn-40740'),
        // 第1ボタンタイプ
        firstBtnType: 'normal1',
        // 第1ボタンラベル
        firstBtnLabel: t('btn.yes'),
        // 第2ボタンタイプ
        secondBtnType: 'blank',
        // 第3ボタンタイプ
        thirdBtnType: 'blank',
      })
      if (rs.firstBtnClickFlg) {
        return
      }
    }
  }
  // 履歴フラグ
  if (historyFlg.value !== OrX0068Const.UPDATE_KBN_D) {
    if (local.historyId === 0) {
      historyFlg.value = OrX0068Const.UPDATE_KBN_C
    }
  }
  // パラメータ
  const param: WeekTableInsertInEntity = {
    // 施設ID
    shisetuId: commonInfoData.shisetsuId,
    // 法人ID
    hoJinId: commonInfoData.houjinId,
    // 事業者ID
    svJigyoId: commonInfoData.svJigyoId,
    // 利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    // 種別ID
    syubetuId: commonInfoData.shubetsuId,
    // 履歴フラグ
    historyUpdateKbn: historyFlg.value,
    // 更新区分
    updateKbn: updateKbn.value,
    // 計画対象期間ID
    sc1Id: String(local.planPeriodId),
    // 有効期間ID
    termidBf: termId.value,
    // 週間表履歴情報
    week1List: weekTableData.value.week1List,
    // 週間表詳細リスト
    week2List: [],
    // 履歴インデックス
    rirekiIndex: String(local.historyNo),
    // 加算退避IDリスト
    taihiWeek3List: [],
  }
  for (const data of local.orX0069.weekData) {
    if (data.updateKbn === OrX0068Const.UPDATE_KBN_NONE){
      continue
    }
    let youbi = ''
    if (data.dayOfWeek1 === true) {
      youbi = '1'
    } else {
      youbi = '0'
    }
    if (data.dayOfWeek2 === true) {
      youbi = youbi + '1'
    } else {
      youbi = youbi + '0'
    }
    if (data.dayOfWeek3 === true) {
      youbi = youbi + '1'
    } else {
      youbi = youbi + '0'
    }
    if (data.dayOfWeek4 === true) {
      youbi = youbi + '1'
    } else {
      youbi = youbi + '0'
    }
    if (data.dayOfWeek5 === true) {
      youbi = youbi + '1'
    } else {
      youbi = youbi + '0'
    }
    if (data.dayOfWeek6 === true) {
      youbi = youbi + '1'
    } else {
      youbi = youbi + '0'
    }
    if (data.dayOfWeek7 === true) {
      youbi = youbi + '1'
    } else {
      youbi = youbi + '0'
    }
    if (data.dayOfWeekOther === true) {
      youbi = youbi + '1'
    } else {
      youbi = youbi + '0'
    }
    //週間表リスト
    weekTableData.value.week2List.push({
      // 詳細ID
      week2Id: data.parentId,
      // 週間表ID
      week1Id: String(local.historyId),
      // 利用者ＩＤ
      userid: systemCommonsStore.getUserSelectSelfId() ?? commonInfoData.userId,
      // 曜日
      youbi: { value: youbi},
      // 開始時間
      kaishiJikan: { value: data.startHour?.value ?? '' },
      // 終了時間
      shuuryouJikan: { value: data.endHour?.value ?? '' },
      // 内容CD
      naiyoCd: { value: '' },
      // 内容
      naiyoKnj: { value: data.content ?? '' },
      // メモ
      memoKnj: { value: data.memo ?? '' },
      // 文字サイズ
      fontSize: { value: data.letterSize ?? ''},
      // 表示モード
      dispMode: { value: data.frequency ?? ''},
      // 文字位置
      alignment: { value: data.charPosition ?? ''},
      // サービス種類
      svShuruiCd: { value: ''},
      // サービス項目（台帳）
      svItemCd: { value: ''},
      // サービス事業者CD
      svJigyoId: { value: ''},
      // サービス種類名称
      svShuruiKnj: { value: ''},
      // サービス項目名称
      svItemKnj: { value: ''},
      // サービス事業者名称
      svJigyoKnj: { value: ''},
      // サービス事業者略称
      svJigyoRks: { value: ''},
      // 文字カラー
      fontColor: { value: convertHexToDecimal(data.letterColor) ?? '' },
      // 背景カラー
      backColor: { value: convertHexToDecimal(data.backgroundColor) ?? '' },
      // 時間表示区分
      timeKbn: { value: data.timeDisplay ?? ''},
      // 週単位以外文字
      igaiMoji: { value: ''},
      // 週単位以外のサービス区分
      igaiKbn: { value: data.igaiKbn ?? ''},
      // 週単位以外のサービス（日付指定）
      igaiDate: { value: data.igaiDate ?? ''},
      // 週単位以外のサービス（曜日指定）
      igaiWeek: { value: data.igaiWeek ?? ''},
      // 福祉用具貸与の単価
      svTani: data.svTani,
      // 福祉用具貸与マスタID
      fygId: data.fygId,
      // 枠外表示するかのフラグ
      wakugaiFlg: '',
      // 更新区分
      updateKbn: data.updateKbn ?? '',
      // 加算リスト
      week3List: [],
    })
  }
  // edit 週間表リスト
  for (const data of weekTableData.value.week2List) {
    // 週間表リスト
    param.week2List.push({
      // 詳細ID
      week2Id: data.week2Id ?? '',
      // 週間表ID
      week1Id: data.week1Id ?? '',
      // 利用者ＩＤ
      userid: data.userid ?? '',
      // 曜日
      youbi: data.youbi?.value ?? '',
      // 開始時間
      kaishiJikan: data.kaishiJikan?.value ?? '',
      // 終了時間
      shuuryouJikan: data.shuuryouJikan?.value ?? '',
      // 内容CD
      naiyoCd: data.naiyoCd?.value ?? '',
      // 内容
      naiyoKnj: data.naiyoKnj?.value ?? '',
      // メモ
      memoKnj: data.memoKnj?.value ?? '',
      // 文字サイズ
      fontSize: data.fontSize?.value ?? '',
      // 表示モード
      dispMode: data.dispMode?.value ?? '',
      // 文字位置
      alignment: data.alignment?.value ?? '',
      // サービス種類
      svShuruiCd: data.svShuruiCd?.value ?? '',
      // サービス項目（台帳）
      svItemCd: data.svItemCd?.value ?? '',
      // サービス事業者CD
      svJigyoId: data.svJigyoId?.value ?? '',
      // サービス種類名称
      svShuruiKnj: data.svShuruiKnj?.value ?? '',
      // サービス項目名称
      svItemKnj: data.svItemKnj?.value ?? '',
      // サービス事業者名称
      svJigyoKnj: data.svJigyoKnj?.value ?? '',
      // サービス事業者略称
      svJigyoRks: data.svJigyoRks?.value ?? '',
      // 文字カラー
      fontColor: data.fontColor?.value ?? '',
      // 背景カラー
      backColor: data.backColor?.value ?? '',
      // 時間表示区分
      timeKbn: data.timeKbn?.value ?? '',
      // 週単位以外文字
      igaiMoji: data.igaiMoji?.value ?? '',
      // 週単位以外のサービス区分
      igaiKbn: data.igaiKbn?.value ?? '',
      // 週単位以外のサービス（日付指定）
      igaiDate: data.igaiDate?.value ?? '',
      // 週単位以外のサービス（曜日指定）
      igaiWeek: data.igaiWeek?.value ?? '',
      // 福祉用具貸与の単価
      svTani: data.svTani,
      // 福祉用具貸与マスタID
      fygId: data.fygId,
      // 枠外表示するかのフラグ
      wakugaiFlg: data.wakugaiFlg,
      // 更新区分
      updateKbn: data.updateKbn,
      // 加算リスト
      week3List: data.week3List,
    })
  }
  await ScreenRepository.update('weekTableImageInfoUpdate', param)
}

/**
 * 確認ボタン押下
 *
 * @param copyData - 複写
 */
async function onCpyClickOverwrite(copyData: OrX0069Type) {
  await nextTick()
  // 週間表複写処理を行う。
  await weekTableCpyUpdate(copyData)
  Or32390Logic.state.set({
    uniqueCpId: or32390.value.uniqueCpId,
    state: { isOpen: false },
  })
}
/**
 * 週間表複写処理
 *
 * @param copyData - 複写
 */
async function weekTableCpyUpdate(copyData: OrX0069Type) {
  const param: WeekTableCopySelectInEntity = {
    // 計画対象期間ID
    sc1Id: copyData.planTargetPeriodId ?? '',
    // 週間表ID
    week1Id: copyData.week1Id ?? '',
    // 複写元年月
    copyMotoYm: copyData.tougaiYm ?? '',
    // 事業者ID
    kaiteiFlg: copyData.kaiteiFlg ?? '',
    // 取込有効期間ID
    termidFr: copyData.torikomitermid ?? '',
    // 該当有効期間ID
    termidTo: String(local.planPeriodId),
    // 複写先年月
    copySakiYm: local.syoriMonth.value,
  }
  // 週間表複写処理を行う。
  const ret: WeekTableCopySelectOutEntity = await ScreenRepository.select(
    'weekTableImageDuplicateSelect',
    param
  )
  updateKbn.value = UPDATE_KBN.UPDATE
  setScreenDataCopy(ret)
}
/**
 * 画面項目を取得したデータで設定する。
 *
 * @param ret - 複写情報
 */
function setScreenDataCopy(ret: WeekTableCopySelectOutEntity) {
  // 複写可否フラグが複写不可の場合
  if (ret.data.copyFlg === OrX0068Const.DUPLICATE_FLG.NO_DUPLICATE) {
    showOr21815MsgOneBtn(t('message.w-cmn-20578'))
  }
  // 詳細リスト設定処理
  setWeekList(ret.data.week2List)
  localOneway.orX0069Oneway.wIgaiKnj = ret.data.wIgaiKnj
}
/**
 * 警告ダイアログの開閉
 *
 * @param errormsg - Message
 */
function showOr21815MsgOneBtn(errormsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 確認ダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - 確認ダイアログOneWayBind領域
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    or21814ResolvePromise = resolve
  })
}
/**
 * エラーダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - エラーダイアログOneWayBind領域
 */
function openErrorDialog(uniqueCpId: string, state: Or21813StateType) {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21813EventType>((resolve) => {
    or21813ResolvePromise = resolve
  })
}

/**
 * 確認メッセージダイアログ開く(i-cmn-10430)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn10430() {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'destroy1',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'normal3',
    // 第3ボタンラベル
    thirdBtnLabel: t('btn.cancel'),
  })
  return rs
}
/**
 * 確認メッセージダイアログ開く(i-cmn-11309)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn11309() {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11309', [
      refValue.value?.taishokikanList[0]?.startYmd,
      refValue.value?.taishokikanList[0]?.endYmd,
    ]),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'destroy1',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'normal3',
    // 第3ボタンラベル
    thirdBtnLabel: t('btn.cancel'),
  })
  return rs
}
/**
 * 確認メッセージダイアログ開く(i-cmn-11265)
 */
async function showMessageICmn11265() {
  await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11265', [t('label.week-table')]),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'blank',
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  } as Or21814OnewayType)
}

/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise<string>((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = OrX0068Const.DIALOG_RESULT_CANCEL
        if (event?.firstBtnClickFlg) {
          result = OrX0068Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = OrX0068Const.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
defineExpose({
  onClickCreate,
  onClickCpy,
  onOrX0124Delete,
  onOrX0124Pattern,
  onClickSave,
  onClickPrintConfig,
  onWeekTableMasterClick,
  // KMD 李傑 ADD START
  onOrX0124MonthlyImport,
  onOrX0124Estimate,
  onOrX0124NikkaImport,
  // KMD 李傑 ADD END
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * Or21813のイベントを監視
 *
 * @description
 * またOr21813のボタン押下フラグをリセットする。
 */
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21813ResolvePromise !== undefined && newValue !== undefined) {
      or21813ResolvePromise(newValue)
    }
    return
  }
)

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21814ResolvePromise !== undefined && newValue !== undefined) {
      or21814ResolvePromise(newValue)
    }
    return
  }
)
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or32387Logic.state.get(or32387.value.uniqueCpId),
  async (newValue) => {
    if (!newValue?.isOpen) {
      const data = getChildCpBinds(or32387.value.uniqueCpId, {
        OrX0153: { cpPath: OrX0153Const.CP_ID(1), twoWayFlg: true },
      })
      console.log(data)
    }
    return
  }
)
watch(
  () => local.orX0069.wIgaiKnj,
  async (newVal) => {
    await nextTick()
    if (
      weekTableData.value.week1List !== undefined &&
      newVal !== undefined &&
      weekTableData.value.week1List.length > 0
    ) {
      weekTableData.value.week1List[local.historyCount - local.historyNo].wIgaiKnj = newVal
    }
  }
)

watch(
  () => local.orX0069.weekData,
  async (newVal) => {
    // await nextTick()
    // for (const data of newVal) {
    //   // 週間表リスト
    //   weekTableData.value.week2List.push({
    //     // 詳細ID
    //     // week2Id: data.week2Id ?? '',
    //     // 週間表ID
    //     // week1Id: data.week1Id ?? '',
    //     // 利用者ＩＤ
    //     userid: systemCommonsStore.getUserSelectSelfId() ?? commonInfoData.userId,
    //     // 曜日
    //     // youbi: data.youbi?.value ?? '',
    //     // 開始時間
    //     kaishiJikan: { value: data.startHour?.value ?? '' },
    //     // 終了時間
    //     shuuryouJikan: { value: data.endHour?.value ?? '' },
    //     // 内容CD
    //     naiyoCd: { value: '' },
    //     // 内容
    //     naiyoKnj: { value: data.content ?? '' },
    //     // メモ
    //     memoKnj: { value: data.memo.value ?? '' },
    //     // 文字サイズ
    //     fontSize: { value: data.letterSize ?? ''},
    //     // 表示モード
    //     dispMode: { value: data.frequency ?? ''},
    //     // 文字位置
    //     alignment: { value: data.charPosition ?? ''},
    //     // サービス種類
    //     svShuruiCd: { value: ''},
    //     // サービス項目（台帳）
    //     svItemCd: { value: ''},
    //     // サービス事業者CD
    //     svJigyoId: { value: ''},
    //     // サービス種類名称
    //     svShuruiKnj: { value: ''},
    //     // サービス項目名称
    //     svItemKnj: { value: ''},
    //     // サービス事業者名称
    //     svJigyoKnj: { value: ''},
    //     // サービス事業者略称
    //     svJigyoRks: { value: ''},
    //     // 文字カラー
    //     fontColor: { value: data.letterColor ?? '' },
    //     // 背景カラー
    //     backColor: { value: data.backgroundColor ?? '' },
    //     // 時間表示区分
    //     timeKbn: { value: data.timeDisplay ?? ''},
    //     // 週単位以外文字
    //     igaiMoji: { value: ''},
    //     // 週単位以外のサービス区分
    //     igaiKbn: { value: data.igaiKbn ?? ''},
    //     // 週単位以外のサービス（日付指定）
    //     igaiDate: { value: data.igaiDate ?? ''},
    //     // 週単位以外のサービス（曜日指定）
    //     igaiWeek: { value: data.igaiWeek ?? ''},
    //     // 福祉用具貸与の単価
    //     svTani: data.svTani,
    //     // 福祉用具貸与マスタID
    //     fygId: data.fygId,
    //     // 枠外表示するかのフラグ
    //     wakugaiFlg: data.wakugaiFlg,
    //     // 枠外表示するかのフラグ
    //     week3List: {} as Week3List,
    //   })
    // }
  }
)

watch(
  () => local.orX0069.tabindex,
  async (newVal) => {
    await nextTick()
    if (newVal === OrX0069Const.DEFAULT.TAB_NO1) {
      OrX0124Logic.state.set({
        uniqueCpId: props.orX0124UniqueCpId,
        state: {
          showEstimateFlg: false,
          showMonthlyImportFlg: false,
          showNikkaImportFlg: false,
        } as OrX0124StateType,
      })
    } else {
      OrX0124Logic.state.set({
        uniqueCpId: props.orX0124UniqueCpId,
        state: {
          showEstimateFlg: true,
          showMonthlyImportFlg: true,
          showNikkaImportFlg: true,
        } as OrX0124StateType,
      })
    }
  },
  { deep: true, immediate: true }
)
// ナビゲーション制御領域のいずれかの編集フラグがON
// const isEdit = computed(() => {
//   return useScreenStore().isEditNavControl()
// })

// KMD 李傑 ADD START
// 現在のシステム年月の取得（yyyy/MM）
// ダイアログ表示フラグ
const showDialogOr36086 = computed(() => {
  // Or36086のダイアログ開閉状態
  return Or36086Logic.state.get(or36086.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr27501DispFlg1 = computed(() => {
  // Or27501 のダイアログ開閉状態
  return Or27501Logic.state.get(or27501.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr10828 = computed(() => {
  // Or10828のダイアログ開閉状態
  return Or10828Logic.state.get(or10828.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 職員検索モーダルの表示状態を返すComputed
 */
const showDialogOr26257 = computed(() => {
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 対象期間画面の表示フラグ
 */
const showDialogOrX0115 = computed(() => {
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 戻り値です
 */
const or36086Type = ref<Or36086Type>({
  //モード
  clickMode: 0,
  // 選択範囲週.開始日
  startDate: new Date(),
  // 事業所ID
  shienId: 0,
  // 事業所ID
  weeklyList: [],
})

const or27501Type = ref<Or27501Type>({
  value: '',
})

const or10828Type = ref<Or10828Type>({ items: [] })

// 現在のシステム年月の取得（yyyy/MM）
function getFormattedDate() {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  return `${year}/${month}`
}

/**
 * 「オプションア」＞「月間」押下
 */
function onOrX0124MonthlyImport() {
  localOneway.or36086Data.isWeeklyFlg = true
  // Or27487のダイアログ開閉状態を更新する
  Or36086Logic.state.set({
    uniqueCpId: or36086.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「オプションア」＞「概算」押下
 */
function onOrX0124Estimate() {
  localOneway.or27501OnewayModel.svJigyoId = ''
  localOneway.or27501OnewayModel.shoriYymm = ''
  localOneway.or27501OnewayModel.termid = ''
  localOneway.or27501OnewayModel.siyouFlg = ''
  localOneway.or27501OnewayModel.dwWeeks = []
  localOneway.or27501OnewayModel.dwRiyou = []
  localOneway.or27501OnewayModel.dwKakusyuu = []
  localOneway.or27501OnewayModel.dsTsukihi = []
  localOneway.or27501OnewayModel.userId = ''
  localOneway.or27501OnewayModel.svJigyoIds = []
  localOneway.or27501OnewayModel.caller = ''
  localOneway.or27501OnewayModel.torikomiKbn = ''
  localOneway.or27501OnewayModel.copyTnki = ''
  localOneway.or27501OnewayModel.selSyori = ''
  localOneway.or27501OnewayModel.fygTani = ''
  // Or27501のダイアログ開閉状態を更新する
  Or27501Logic.state.set({
    uniqueCpId: or27501.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「オプションア」＞「日課」押下
 */
function onOrX0124NikkaImport() {
  // 引継情報.計画書様式を設定する。
  localOneway.or10828Oneway.dailyscheduleImportType.kikanFlag =
    OrX0068Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT
  // or10828のダイアログ開閉状態を更新する
  Or10828Logic.state.set({
    uniqueCpId: or10828.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 利用者選択監視関数に渡すコールバック関数（サブ01の例）
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param _newSelfId - 利用者
 *
 * @param _oldSelfId - 利用者
 */
const callbackFuncSub01 = (_newSelfId: string, _oldSelfId: string) => {
  Or41179Logic.state.set({
    uniqueCpId: or41179_1.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })
  if (systemCommonsStore.getUserSelectSelfId() !== undefined && systemCommonsStore.getUserSelectSelfId() !== ''){
    void initOr32382()
  }
}
/**
 * 事業所選択更新の監視
 */
// ★事業所選択監視関数を実行
jigyoListWatch(or41179_1.value.uniqueCpId, callbackFuncJigyo)
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId !== '') {
    //画面データ変更かどうかを判断する
    //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
    // if (isEdit.value) {
    //   const dialogResult = await showConfirmMessgeBox()
    //   if (dialogResult?.firstBtnClickFlg === true) {
    //     await _save()
    //   } else if (dialogResult?.thirdBtnClickFlg === true) {
    //     return
    //   }
    // }
    //共通情報.事業所ＩＤ
    // systemCommonsStore.setSvJigyoId(newJigyoId)
    commonInfoData.svJigyoId = newJigyoId

    //共通情報.計画期間ＩＤ = 空白
    local.planPeriodId = ''
    if (systemCommonsStore.getUserSelectSelfId() !== undefined && systemCommonsStore.getUserSelectSelfId() !== ''){
      void initOr32382()
    }
  }
}
// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackFuncSub01)

// KMD 李傑 ADD END

/**
 * AC017 「作成者選択アイコンボタン」押下
 */
const createUserBtnClick = () => {
  const svJigyoIdList: { svJigyoId: string }[] = []
  systemCommonsStore.getSvJigyoIdList.forEach((item) => {
    svJigyoIdList.push({ svJigyoId: item })
  })
  localOneway.or26257.svJigyoIdList = svJigyoIdList
  localOneway.or26257.kijunYmd = systemCommonsStore.getSystemDate ?? ''
  localOneway.or26257.defSvJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.or26257.shokuinIdList = [Number(local.createUserId)]

  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 職員検索モーダル閉じる後の処理
 */
watch(
  () => local.or26257,
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    local.createUserId = newValue.shokuin.chkShokuId
    local.createUser.value = `${newValue.shokuin.shokuin1Knj ?? ''} ${newValue.shokuin.shokuin2Knj ?? ''}`
  }
)

/**
 * AC011 「計画対象期間選択アイコンボタン」押下
 */
const handlePlanEditClick = async () => {
  const isEdit = isEditNavControl(watchedComponents.value)
  // 画面入力データに変更がある場合
  if (isEdit) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // はいの場合
      // AC003を実行し、処理続き
      await onClickSave()
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }

  localOneway.orX0115.kindId = systemCommonsStore.getSyubetu ?? ''
  localOneway.orX0115.sc1Id = local.planPeriodId

  // GUI00070 対象期間画面を開く
  OrX0115Logic.state.set({
    uniqueCpId: orX0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 対象期間画面閉じる後の処理
 */
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  async (newValue) => {
    if (!newValue?.kikanId) {
      return
    }
    if (!newValue?.kikanId) {
      return
    }

    //「期間-選択確認前 アイコンボタン」押下
    if (local.planPeriodId === newValue.kikanId) {
      // 選択前の対象期間から変更がない場合
      return
    }

    local.planPeriodId = newValue.kikanId

    // 週間表計画期間履歴処理を行う。
    await weekTablePeriodUpdate(OrX0068Const.PERIOD_CHANGE_KBN.SELECTED_ID)
  }
)

/**
 * AC012 「計画対象期間-前へアイコンボタン」押下
 */
const handlePlanPreClick = async () => {
  //「期間-前へ アイコンボタン」押下
  if (local.planNo === 1) {
    // 1件目の計画対象期間データが表示されている状態の場合
    await openWarningDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11262'),
    })
    return
  }
  const isEdit = isEditNavControl(watchedComponents.value)
  // 画面入力データに変更がある場合
  if (isEdit) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // はいの場合
      // AC003を実行し、処理続き
      await onClickSave()
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }
  // 週間表計画期間履歴処理を行う。
  await weekTablePeriodUpdate(OrX0068Const.PERIOD_CHANGE_KBN.BEFORE_SELECTED_ID)
}

/**
 * AC013 「計画対象期間-次へアイコンボタン」押下
 */
const handlePlanPostClick = async () => {
  //「期間-次へ アイコンボタン」押下
  if (local.planNo === local.planCount) {
    // 最終件目の計画対象期間データが表示されている状態の場合
    await openWarningDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11263'),
    })
    return
  }
  const isEdit = isEditNavControl(watchedComponents.value)
  // 画面入力データに変更がある場合
  if (isEdit) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // はいの場合
      // AC003を実行し、処理続き
      await onClickSave()
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }
  // 週間表計画期間履歴処理を行う。
  await weekTablePeriodUpdate(OrX0068Const.PERIOD_CHANGE_KBN.AFTER_SELECTED_ID)
}
</script>
<template>
  <!-- コンテンツエリア -->
  <c-v-sheet class="content">
    <c-v-row>
      <!-- 事業所 -->
      <c-v-col
        cols="auto"
        class="pa-0 pr-3"
      >
        <g-base-or41179
          v-bind="or41179_1"
          class="custom-jigyo"
        />
      </c-v-col>
      <!-- 計画対象期間 -->
      <c-v-col
        v-if="localFlg.showPlanningPeriodFlg"
        cols="auto"
        class="pa-0 pr-3"
      >
        <g-custom-or-x0165
          :oneway-model-value="localOneway.orX0165Plan"
          @on-click-edit-btn="handlePlanEditClick"
          @on-click-pre-btn="handlePlanPreClick"
          @on-click-post-btn="handlePlanPostClick"
        />
      </c-v-col>
      <!-- 作成日 -->
      <c-v-col
        v-if="localFlg.showCreateDateFlg"
        cols="auto"
        class="pa-0 pr-3"
      >
        <base-mo00020
          v-model="local.createDate"
          :oneway-model-value="localOneway.mo00020CreateDate"
          :disabled="localOneway.orX0069Oneway.delFlg"
          class="custom-input"
        />
      </c-v-col>
      <!-- 作成者 -->
      <c-v-col
        v-if="localFlg.showAuthorFlg"
        cols="auto"
        class="pa-0 pr-3"
      >
        <g-custom-or-x0157
          v-model="local.createUser"
          :oneway-model-value="localOneway.orX0157CreateUser"
          class="custom-input"
          @on-click-edit-btn="createUserBtnClick"
        />
      </c-v-col>
      <!-- 履歴 -->
      <c-v-col
        v-if="localFlg.showHistoryFlg"
        cols="auto"
        class="pa-0 pr-3"
      >
        <g-custom-or-x0165
          :oneway-model-value="localOneway.orX0165History"
          @on-click-edit-btn="onClickHistIconBtn"
          @on-click-pre-btn="onClickPreHistBtn"
          @on-click-post-btn="onClickNextHistBtn"
        />
      </c-v-col>
      <!-- ケース -->
      <c-v-col
        v-if="localFlg.showCaseFlg"
        cols="auto"
        class="pa-0 pr-3"
      >
        <base-Mo00045
          v-model="local.caseNo"
          :oneway-model-value="localOneway.Mo00045Oneway"
          :disabled="localOneway.orX0069Oneway.delFlg"
          class="custom-input input-text-align-right"
        />
      </c-v-col>
      <!-- 処理年月 -->
      <c-v-col
        v-if="localFlg.showSyoriMonthFlg"
        cols="auto"
        class="pa-0"
      >
        <base-mo00615
          :oneway-model-value="{
            showItemLabel: true,
            showRequiredLabel: false,
            customClass: {
              outerClass: undefined,
              outerStyle: 'background: none',
              labelClass: 'mb-1',
              labelStyle: undefined,
              itemClass: undefined,
              itemStyle: undefined,
            },
            itemLabel: t('label.process-ym'),
          }"
        />
        <base-mo01352
          v-model="local.syoriMonth"
          :oneway-model-value="localOneway.syoriMonthOneway"
          :disabled="localOneway.orX0069Oneway.delFlg"
          class="custom-input"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row
      class="pt-4"
      style="margin-left: -24px"
    >
      <c-v-col
        cols="12"
        class="pa-0"
      >
        <g-custom-or-x-0069
          v-bind="orX0069"
          ref="orX0069Ref"
          v-model="local.orX0069"
        />
      </c-v-col>
    </c-v-row>

    <!-- GUI000985_［履歴選択］画面 週間表のポップアップ -->
    <g-custom-or-28820
      v-if="showDialogOr28820"
      v-bind="or28820"
      v-model="local.or28820"
      :oneway-model-value="localOneway.or28820Oneway"
    />
    <!-- GUI00977 週間表マスタ画面のポップアップ -->
    <g-custom-or-27509
      v-if="showDialogOr27509"
      v-bind="or27509"
      v-model="local.or27509"
      :oneway-model-value="localOneway.Or27509Oneway"
    />
    <!-- GUI00980_週間表パターン（設定）をポップアップ -->
    <g-custom-or-32387
      v-if="showDialogOr32387"
      v-bind="or32387"
      :oneway-model-value="localOneway.Or32387Oneway"
    />
    <!-- GUI00986_［印刷設定］画面をポップアップ -->
    <g-custom-or-29976
      v-if="showDialogOr29976"
      v-bind="or29976"
      :oneway-model-value="localOneway.or29976Oneway"
    />
    <!-- ［週間表複写］（週間表） -->
    <g-custom-or-32390
      v-if="showDialogOr32390"
      v-bind="or32390"
      :oneway-model-value="localOneway.Or32390Oneway"
      @on-click-confirm="onCpyClickOverwrite"
    >
    </g-custom-or-32390>
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814" />
    <!-- Or21813:有機体:エラーダイアログ -->
    <g-base-or21813 v-bind="or21813" />

    <!-- KMD 李傑 ADD START -->
    <!-- GUI01039_月間取込 -->
    <g-custom-or-36086
      v-if="showDialogOr36086"
      v-bind="or36086"
      v-model="or36086Type"
      :oneway-model-value="localOneway.or36086Data"
    />

    <!-- GUI01042_週間計画概算表示 -->
    <g-custom-or-27501
      v-if="showDialogOr27501DispFlg1"
      v-bind="or27501"
      v-model="or27501Type"
      :oneway-model-value="localOneway.or27501OnewayModel"
    />

    <!-- GUI00981_日課表取込 -->
    <g-custom-or-10828
      v-if="showDialogOr10828"
      v-bind="or10828"
      v-model="or10828Type"
      :oneway-model-value="localOneway.or10828Oneway"
    />
    <!-- KMD 李傑 ADD END -->

    <!-- Or26257:有機体:職員検索モーダル -->
    <g-custom-or26257
      v-if="showDialogOr26257"
      v-bind="or26257"
      v-model="local.or26257"
      :oneway-model-value="localOneway.or26257"
    />
    <!-- GUI00070 対象期間画面 -->
    <g-custom-or-x0115
      v-if="showDialogOrX0115"
      v-bind="orX0115"
      :oneway-model-value="localOneway.orX0115"
    />
  </c-v-sheet>
</template>
<style scoped lang="scss">
@use '@/styles/base.scss';

.divider-class {
  border-width: thin;
  margin: 12px 0px;
}

.content {
  padding: 0px 0px;
  background-color: transparent;

  :deep(.v-row) {
    margin: 0px;
  }

  .custom-jigyo {
    flex-direction: column;

    :deep(> div:first-child) {
      margin: 0px !important;
      padding: 0px !important;
      margin-bottom: 4px !important;
    }

    :deep(> div:last-child > div) {
      margin: 0px !important;
    }
  }

  .v-sheet.custom-input {
    background-color: rgb(var(--v-theme-background));
  }

  .custom-input {
    :deep(.v-input__control) {
      background-color: rgb(var(--v-theme-secondaryBackground));
    }
  }

  :deep(.custom-edit-btn) {
    background-color: #ebf2fd !important;
  }

  .custom-tabs {
    min-height: 36px;

    :deep(.v-slide-group__content) {
      gap: 24px;
    }

    :deep(.tabs) {
      height: 35px;
      border-bottom: 1px solid #b4c5dc;
      padding-left: 24px !important;
      padding-right: 24px !important;

      .v-tab.v-tab.v-btn {
        height: 35px;
      }
    }
  }

  .input-text-align-right {
    :deep(input) {
      text-align: right !important;
    }
  }
}
</style>
