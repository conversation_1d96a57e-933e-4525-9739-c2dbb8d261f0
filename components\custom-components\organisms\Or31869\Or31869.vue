<script setup lang="ts">
/**
 * Or31869:会議録画面
 * GUI01122_会議録
 *
 * @description
 * 会議録画面
 *
 * <AUTHOR> LE VAN CUONG
 */
import { useI18n } from 'vue-i18n'
import { v4 as uuidv4 } from 'uuid'
import { Gui00064Const } from '../Gui00064/Gui00064.constants'
import { Gui00064Logic } from '../Gui00064/Gui00064.logic'
import { Or22951Logic } from '../Or22951/Or22951.logic'
import { Or28256Logic } from '../Or28256/Or28256.logic'
import type { Or28256OnewayType } from '../Or28256/Or28256.type'
import { OrX0157Const } from '../OrX0157/OrX0157.constants'
import { Or31869Const } from './Or31869.constants'
import {
  reactive,
  ref,
  computed,
  watch,
  useScreenTwoWayBind,
  nextTick,
  useScreenUtils,
  useCmnCom,
} from '#imports'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or31869OnewayType, Or31869Type } from '~/types/cmn/business/components/Or31869Type'
import { useSetupChildProps } from '~/composables/useComponentVue'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
  Mo01354Items,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { Or01444Const } from '~/components/custom-components/organisms/Or01444/Or01444.constants'
import { Or27354Logic } from '~/components/custom-components/organisms/Or27354/Or27354.logic'
import type { Or27354OnewayType, Or27354Type } from '~/types/cmn/business/components/Or27354Type'
import { Or27354Const } from '~/components/custom-components/organisms/Or27354/Or27354.constants'
import { Or22951Const } from '~/components/custom-components/organisms/Or22951/Or22951.constants'
import { Or28256Const } from '~/components/custom-components/organisms/Or28256/Or28256.constants'
import { Or27094Logic } from '~/components/custom-components/organisms/Or27094/Or27094.logic'
import type {
  Or27094OnewayType,
  Or27094TwowayType,
} from '~/types/cmn/business/components/Or27094Type'
import { Or27094Const } from '~/components/custom-components/organisms/Or27094/Or27094.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or27374Const } from '~/components/custom-components/organisms/Or27374/Or27374.constants'
import { Or27374Logic } from '~/components/custom-components/organisms/Or27374/Or27374.logic'
import type { Or27374Type, Or27374OnewayType } from '~/types/cmn/business/components/Or27374Type'
import { Or27358Const } from '~/components/custom-components/organisms/Or27358/Or27358.constants'
import { Or27358Logic } from '~/components/custom-components/organisms/Or27358/Or27358.logic'
import type { Or27358Type } from '~/types/cmn/business/components/Or27358Type'
import { Or27553Const } from '~/components/custom-components/organisms/Or27553/Or27553.constants'
import { Or27553Logic } from '~/components/custom-components/organisms/Or27553/Or27553.logic'
import type { Or27553OnewayType, Or27553Type } from '~/types/cmn/business/components/Or27553Type'
import { Or55476Logic } from '~/components/custom-components/organisms/Or55476/Or55476.logic'
import { Or55476Const } from '~/components/custom-components/organisms/Or55476/Or55476.constants'
import type {
  Or55476OneWayType,
  RelatedPersonSelectResInfo,
} from '~/components/custom-components/organisms/Or55476/Or55476.type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { AttendeesInfo } from '~/repositories/cmn/entities/MeetingMinutesDetailPtn1UpdateEntity'
import type { Or22951OnewayType } from '~/types/cmn/business/components/Or22951Type'
import type { ApplicableOfficeResponseInfo } from '~/repositories/cmn/entities/ApplicableOfficeConfirmSelectEntity'
import type { OrX0163OnewayType } from '~/types/cmn/business/components/OrX0163Type'
import type { OrX0157OnewayType } from '~/types/cmn/business/components/OrX0157Type'
import type { OrX0185OnewayType } from '~/types/cmn/business/components/OrX0185Type'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'

/**
 * I18n
 */
const { t } = useI18n()

/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

const { isOverLimitByCharWidth } = useCmnCom()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
  onewayModelValue: Or31869OnewayType
  modelValue: Or31869Type
}

/** props */
const props = defineProps<Props>()

/**
 * 双方向バインド用の内部変数
 */
const local = reactive({
  or31869: props.modelValue,
  mo00020HeldDate: { value: '' },
  mo00038HeldNumberOfTimes: {
    mo00045: { value: '' },
  },
  mo00039ResidentAndFamilyParticipation: props.modelValue.attendanceMode,
  mo00039ResidentAndFamilyParticipationValue: Number(props.modelValue.attendeesHeaderInfo.sankaKnj),
  mo00020NextEventDate: {
    value: '',
  },
  mo00020RemainingAndNewChallenges: {
    value: '',
  },
  mo00045HeldLocation: {
    value: '',
  },
  mo00045HeldTime: {
    value: '',
  },
  mo00045Person: {
    value: '',
  },
  mo00045Family: {
    value: '',
  },
  mo00045Relationship: {
    value: '',
  },
  mo00045Remarks: {
    value: '',
  },
  mo00045ResidentAndFamilyParticipation: {
    value: '',
  },
  mo00046ReasonAbsence: {
    value: '',
  },
  mo00046ConsiderItem: {
    value: '',
  },
  mo00046ConsiderContents: {
    value: '',
  },
  mo00046Conclusion: {
    value: '',
  },
  mo00046RemainingIssues: {
    value: '',
  },
  mo00046MainTopicDiscussed: {
    value: '',
  },
  mo00046ProvisionService: {
    value: '',
  },
  mo00046ConsiderMatterContents: {
    value: '',
  },
  mo00046MeetingConclusion: {
    value: '',
  },
  mo00046RemainingAndNewChallenges: {
    value: '',
  },
  mo00046Comprehensive: {
    value: '',
  },
  mo00046Implementation: {
    value: '',
  },
  mo00046AbsenteeCorrespondence: {
    value: '',
  },
  considerItemZoom: true,
  considerContentsZoom: true,
  remainingIssuesZoom: true,
  mainTopicDiscussedZoom: true,
  conclusionZoom: true,
  mo01354Affiliation1: {
    values: {
      selectedRowId: '1',
      selectedRowIds: [],
      items: [],
    },
  } as unknown as Mo01354Type,
  mo01354Affiliation2: {
    values: {
      selectedRowId: '1',
      selectedRowIds: [],
      items: [],
    },
  } as unknown as Mo01354Type,
  mo01354Affiliation3: {
    values: {
      selectedRowId: '1',
      selectedRowIds: [],
      items: [],
    },
  } as unknown as Mo01354Type,
  mo01354MeetingAttendees: {
    values: {
      selectedRowId: '1',
      selectedRowIds: [],
      items: [],
    },
  } as unknown as Mo01354Type,
  mo01354MainManager1: {
    values: {
      selectedRowId: '1',
      selectedRowIds: [],
      items: [],
    },
  } as unknown as Mo01354Type,
  mo01354MainManager2: {
    values: {
      selectedRowId: '1',
      selectedRowIds: [],
      items: [],
    },
  } as unknown as Mo01354Type,
})
/**
 * 片方向バインド用の内部変数
 */
const localOneway = reactive({
  mo01299HeldDate: {
    visible: false,
    customClass: new CustomClass({ outerClass: 'padding: 0px' }),
  },
  mo00020HeldDate: {
    showItemLabel: false,
    hideDetails: true,
    disabled: false,
    width: '128',
  } as Mo00020OnewayType,
  mo01299HeldLocation: {
    visible: false,
    customClass: new CustomClass({ outerClass: 'padding: 0px' }),
  },
  mo00045HeldLocation: {
    hideDetails: true,
    class: 'index',
    showItemLabel: false,
    width: '250px',
    maxLength: '36',
  } as Mo00045OnewayType,
  mo00009HeldLocation: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  orX0157Oneway: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: false,
        // maxlength: '44',
        width: '307px',
      },
    },
  },
  orX0157OnewayHeldTime: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: false,
        // maxlength: '44',
        width: '227px',
      },
    },
  } as OrX0157OnewayType,
  mo01299HeldTime: {
    visible: false,
    customClass: new CustomClass({ outerClass: 'padding: 0px' }),
  },
  mo00045HeldTime: {
    hideDetails: true,
    class: 'index',
    showItemLabel: false,
    width: '200px',
    maxLength: '20',
  } as Mo00045OnewayType,
  mo00009HeldTime: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo01299HeldNumberOfTimes: {
    visible: false,
    customClass: new CustomClass({ outerClass: 'padding: 0px' }),
  },
  mo00009HeldNumberOfTimes: {
    btnIcon: 'square',
  } as Mo00009OnewayType,
  mo00038HeldNumberOfTimes: {
    mo00045Oneway: {
      maxLength: '3',
      showItemLabel: false,
      class: 'text-filed',
      width: '58',
    } as Mo00045OnewayType,
    min: -99,
    max: 999,
    showItemLabel: false,
  },
  mo01299Person: {
    visible: false,
    customClass: new CustomClass({ outerClass: 'padding: 0px' }),
  },
  mo00045Person: {
    hideDetails: true,
    class: 'index',
    showItemLabel: false,
    width: '89px',
    maxLength: '8',
  } as Mo00045OnewayType,
  mo01299Family: {
    visible: false,
    customClass: new CustomClass({ outerClass: 'padding: 0px' }),
  },
  mo00045Family: {
    hideDetails: true,
    class: 'index',
    showItemLabel: false,
    width: '89px',
    maxLength: '8',
  } as Mo00045OnewayType,
  mo01299Relationship: {
    visible: false,
    customClass: new CustomClass({ outerClass: 'padding: 0px' }),
  },
  mo00045Relationship: {
    hideDetails: true,
    class: 'index',
    showItemLabel: false,
    width: '150px',
    maxLength: '12',
  } as Mo00045OnewayType,
  mo01299Remarks: {
    visible: false,
    customClass: new CustomClass({ outerClass: 'padding: 0px' }),
  },
  mo00045Remarks: {
    hideDetails: true,
    class: 'index',
    showItemLabel: false,
    width: '197px',
    maxLength: '18',
  } as Mo00045OnewayType,
  mo01298Attendees: {
    title: t('label.attendees'),
  },
  mo01338AttendeesOneWay: {
    value: t('label.attendees'),
    lable: false,
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
    }),
  },
  mo01299ResidentAndFamilyParticipation: {
    anchorPoint: 'ss-5',
    title: t('label.resident-and-family-participation'),
  },
  mo00039ResidentAndFamilyParticipation: {
    showItemLabel: false,
  },
  mo01338ResidentAndFamilyParticipation: {
    value: t('label.family'),
  },
  mo01338Resident: {
    value: t('label.resident-and-family-participation'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-1',
      itemClass: 'ml-4 align-center',
    }),
  } as Mo01338OnewayType,
  mo00009ResidentAndFamilyParticipation: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00045ResidentAndFamilyParticipation: {
    hideDetails: true,
    class: 'index',
    showItemLabel: false,
    width: '280px',
    isVerticalLabel: false,
    maxLength: '40',
  } as Mo00045OnewayType,
  orX0163ResidentAndFamilyParticipation: {
    height: '100%',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); border-top: none;',
    align: 'center',
  } as OrX0163OnewayType,
  mo00611AddRow: {
    btnLabel: t('btn.add-row'),
    width: '89px',
    minWidth: '89px',
    minHeight: '32px',
    height: '32px',
    prependIcon: 'add',
    tooltipText: t('tooltip.care-plan2-newline-btn'),
  } as Mo00611OnewayType,
  mo00611InsertRow: {
    btnLabel: t('btn.insert-row'),
    width: '89px',
    minWidth: '89px',
    minHeight: '32px',
    height: '32px',
    prependIcon: 'add',
    tooltipText: t('tooltip.care-plan2-insertline-btn'),
  } as Mo00611OnewayType,
  mo01265DeleteRow: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    width: '89px',
    minWidth: '89px',
    minHeight: '32px',
    height: '32px',
    tooltipText: t('tooltip.care-plan2-deleteline-btn'),
  } as Mo00611OnewayType,
  mo01338Attendees: {
    value: t('label.attendees'),
  },
  mo00611Attendees: {
    btnLabel: t('label.attendees'),
    width: '65px',
    minWidth: '65px',
    minHeight: '32px',
    height: '32px',
    tooltipText: t('tooltip.meeting-attendee-capture'),
  } as Mo00611OnewayType,
  mo01338LetterInputSupport: {
    value: t('label.letter-input-support'),
  },
  mo00009LetterInputSupport: {
    btnIcon: 'edit_square',
    tooltipText: t('tooltip.input-assistance'),
  } as Mo00009OnewayType,
  mo00611LetterInputSupport: {
    btnLabel: t('label.letter-input-support'),
    width: '120px',
    tooltipText: t('tooltip.input-assistance'),
  } as Mo00611OnewayType,
  mo01338CaseImport: {
    value: t('label.case-import'),
    customClass: new CustomClass({
      itemClass: 'ml-0 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009CaseImport: {
    btnLabel: t('label.case-import'),
    width: '92px',
    minWidth: '92px',
    minHeight: '29px',
    height: '29px',
    tooltipText: t('tooltip.care-plan2-case-import-icon-btn'),
  } as Mo00611OnewayType,
  mo01338ContentsImport: {
    value: t('label.contents-import'),
  },
  mo00009ContentsImport: {
    btnLabel: t('label.contents-import'),
    width: '72px',
    minWidth: '72px',
    minHeight: '29px',
    height: '29px',
    tooltipText: t('tooltip.content-import'),
  } as Mo00611OnewayType,
  mo01338IssuesImport: {
    value: t('label.issues-import'),
  },
  mo00009IssuesImport: {
    btnLabel: t('label.issues-import'),
    width: '72px',
    minWidth: '72px',
    minHeight: '29px',
    height: '29px',
    tooltipText: t('tooltip.issues-import'),
  } as Mo00611OnewayType,
  mo01338ReasonAbsence: {
    value: t('label.reason-absence'),
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009ReasonAbsence: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046ReasonAbsence: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
    class: 'A12BTextArea',
  } as Mo00046OnewayType,
  orX0163ReasonAbsenceOnewayModelValue: {
    height: '106px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px;',
  } as OrX0163OnewayType,
  orX0185MeetingAttendeesOneway: {
    height: '100%',
    readOnly: false,
    align: 'center',
    contentClass: 'custom-meeting-table',
  } as OrX0185OnewayType,
  mo01338Zoom: {
    value: t('label.zoom-out'),
    customClass: new CustomClass({
      itemClass: 'ml-0 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo01338Expansion: {
    value: t('label.zoom-in'),
    customClass: new CustomClass({
      itemClass: 'ml-0 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009Zoom: {
    btnIcon: 'height',
  },
  mo01338ConsiderItem: {
    value: t('label.consider-item'),
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009ConsiderItem: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046ConsiderItem: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163ConsiderItemOnewayModelValue: {
    height: '106px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px; margin-top:1px',
  } as OrX0163OnewayType,
  mo01338ConsiderContents: {
    value: t('label.consider-contents'),
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009ConsiderContents: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046ConsiderContents: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163ConsiderContentsOnewayModelValue: {
    height: '106px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px; margin-top:1px',
  } as OrX0163OnewayType,
  mo01338Conclusion: {
    value: t('label.conclusion'),
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009Conclusion: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046Conclusion: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163ConclusionOnewayModelValue: {
    height: '106px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px; margin-top:1px',
  } as OrX0163OnewayType,
  mo01338RemainingIssues: {
    value: t('label.remaining-issues'),
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009RemainingIssues: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046RemainingIssues: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163RemainingIssuesOnewayModelValue: {
    height: '106px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px; margin-top:1px',
  } as OrX0163OnewayType,
  mo00020NextEventDate: {
    showItemLabel: false,
    hideDetails: true,
    disabled: false,
    width: '128',
  } as Mo00020OnewayType,
  mo01338MainTopicDiscussed: {
    value: t('label.main-topics-discussed'),
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009MainTopicDiscussed: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046MainTopicDiscussed: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163MainTopicDiscussedOnewayModelValue: {
    height: '480px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px;',
    contentClass: 'text-area-edit',
  } as OrX0163OnewayType,
  mo01338ProvisionService: {
    value: t('label.provision-services-based-on-implementation-plan-other-matters'),
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009ProvisionService: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046ProvisionService: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163rovisionService: {
    height: '106px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px;',
  } as OrX0163OnewayType,
  mo01338ConsiderMatterContents: {
    value: t('label.consider-matter-contents'),
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009ConsiderMatterContents: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046ConsiderMatterContents: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163ConsiderMatterContents: {
    height: '106px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px; margin-top:1px',
  } as OrX0163OnewayType,
  mo01338MeetingConclusion: {
    value: t('label.meeting-conclusion'),
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009MeetingConclusion: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046MeetingConclusion: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163MeetingConclusion: {
    height: '106px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px; margin-top:1px',
  } as OrX0163OnewayType,
  mo01338RemainingAndNewChallenges: {
    value: t('label.remaining-and-new-challenges'),
    customClass: new CustomClass({
      itemClass: 'ml-2 align-center',
      outerClass: 'mr-2',
      labelClass: 'ma-1',
    }),
  },
  mo00009RemainingAndNewChallenges: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046RemainingAndNewChallenges: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  mo00020RemainingAndNewChallenges: {
    itemLabel: t('label.next-event-date'),
    showItemLabel: true,
    hideDetails: true,
    maxLength: '10',
    isVerticalLabel: false,
  } as Mo00020OnewayType,
  orX0163RemainingAndNewChallenges: {
    height: '106px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px; margin-top:1px',
  } as OrX0163OnewayType,
  mo01298: {
    title: t('label.immediate-response'),
  },
  mo01338Immediate: {
    value: t('label.immediate-response'),
    lable: false,
    customClass: new CustomClass({
      itemClass: ' align-center',
      outerClass: '  display-flex',
    }),
  },
  mo01338Comprehensive: {
    value: t('label.comprehensive-plan-policy-modified-necessity'),
    customClass: new CustomClass({
      itemClass: 'ml-2',
      outerClass: 'mr-2 align-content-center',
      labelClass: 'ma-1',
    }),
  },
  mo00009Comprehensive: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046Comprehensive: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163Comprehensive: {
    height: '101px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); border-bottom: none; padding: 8px;',
  } as OrX0163OnewayType,
  mo01338Implementation: {
    value: t('label.implementation-plan-policy-modified-necessity'),
    customClass: new CustomClass({
      itemClass: 'ml-2',
      outerClass: 'mr-2 align-content-center',
      labelClass: 'ma-1',
    }),
  },
  mo00009Implementation: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046Implementation: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163Implementation: {
    height: '102px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); padding: 8px; margin-top:1px',
  } as OrX0163OnewayType,
  mo01338AbsenteeCorrespondence: {
    value: t('label.absentee-correspondence'),
    customClass: new CustomClass({
      itemClass: 'ml-2',
      outerClass: 'mr-2 align-content-center',
      labelClass: 'ma-1',
    }),
  },
  mo00009AbsenteeCorrespondence: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00046AbsenteeCorrespondence: {
    showItemLabel: false,
    noResize: true,
    maxlength: '4000',
    maxRows: '3',
    row: '3',
    autoGrow: false,
  } as Mo00046OnewayType,
  orX0163AbsenteeCorrespondence: {
    height: '101px',
    readOnly: false,
    contentStyle:
      'background-color: white; border: solid 1px rgb(var(--v-theme-light)); border-top: none; padding: 8px; margin-top:1px',
  } as OrX0163OnewayType,
  mo01354Oneway: {
    height: '130px',
    headers: [
      {
        title: t('label.affiliation') + '（' + t('label.occupation') + '）',
        key: 'affiliation',
        sortable: false,
      },
      {
        title: t('label.name'),
        key: 'name',
        sortable: false,
      },
    ] as Mo01354Headers[],
    columnMinWidth: {
      columnWidths: [182, 138],
    } as ResizableGridBinding,
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    showDragIndicatorFlg: true,
    density: 'default',
  } as Mo01354OnewayType,
  mo01354MeetingAttendeesOneway: {
    height: '120px',
    headers: [
      {
        title: t('label.meeting-attendees'),
        key: 'meetingAttendees',
        sortable: false,
      },
    ] as Mo01354Headers[],
    columnMinWidth: {
      columnWidths: [961],
    } as ResizableGridBinding,
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    showDragIndicatorFlg: true,
    density: 'default',
  } as Mo01354OnewayType,
  mo01354MainManagerOneway: {
    height: '130px',
    headers: [
      {
        title: t('label.affiliation') + '（' + t('label.occupation') + '）',
        key: 'affiliation',
        sortable: false,
      },
      {
        title: t('label.job-type'),
        key: 'jobType',
        sortable: false,
      },
      {
        title: t('label.name'),
        key: 'name',
        sortable: false,
      },
      {
        title: t('label.main-duties'),
        key: 'mainManager',
        sortable: false,
      },
    ] as Mo01354Headers[],
    columnMinWidth: {
      columnWidths: [182, 127, 127, 127],
    } as ResizableGridBinding,
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    showDragIndicatorFlg: true,
    density: 'default',
  } as Mo01354OnewayType,
})
/**************************************************
 * 変数定義
 **************************************************/
/**
 * gui00064
 */
const gui00064 = ref({ uniqueCpId: '' })
/**
 * or51775
 */
const or51775 = ref({ uniqueCpId: '' })

/**
 * or27354
 */
const or27354 = ref({ uniqueCpId: '' })
/**
 * or22951
 */
const or22951 = ref({ uniqueCpId: '' })
/**
 * or28256
 */
const or28256 = ref({ uniqueCpId: '' })
/**
 * Or27094
 */
const Or27094 = ref({ uniqueCpId: '' })
/**
 * or27374
 */
const or27374 = ref({ uniqueCpId: '' })
/**
 * or27358
 */
const or27358 = ref({ uniqueCpId: '' })
/**
 * Or27553
 */
const Or27553 = ref({ uniqueCpId: '' })
/**
 * 確認ダイアログのPromise
 */
let or21814ResolvePromise: (value: Or21814EventType) => void

/**
 * or21814
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * or55476
 */
const or55476 = ref({ uniqueCpId: '' })
/**
 * firstTime
 */
const firstTime = ref(false)
/**
 * refValue
 */
const { refValue } = useScreenTwoWayBind<Or31869Type>({
  cpId: Or31869Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**
 * Pinia
 */
const { setChildCpBinds } = useScreenUtils()
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Gui00064Const.CP_ID(1)]: gui00064.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or27354Const.CP_ID(0)]: or27354.value,
  [Or28256Const.CP_ID(0)]: or28256.value,
  [Or22951Const.CP_ID(0)]: or22951.value,
  [Or27094Const.CP_ID(0)]: Or27094.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or27374Const.CP_ID(0)]: or27374.value,
  [Or27358Const.CP_ID(0)]: or27358.value,
  [Or27553Const.CP_ID(0)]: Or27553.value,
  [Or55476Const.CP_ID(0)]: or55476.value,
})

/**
 * memoData
 */
let memoData = {
  or51775Oneway: {
    title: 'タイトルテスト',
  } as Or51775OnewayType,
}

const or28256Oneway = ref<Or28256OnewayType>({
  /**
   * 事業所ID
   */
  sisetsuId: '',
  /**
   * 法人ID
   */
  houjinId: '',
  /**
   * 分類CD
   */
  bunruiCd: '',
  /**
   * 項目NO
   */
  koumokuno: 0,
  /**
   * 開始日
   */
  kaisihi: {
    value: '',
  },
  /**
   * 終了日
   */
  syuuryouhi: {
    value: '',
  },
  /**
   * 対象項目
   */
  targetItem: '',

  /**
   * データ行の選択値
   */
  caseInformation: '',

  /**
   * 利用者ID
   */
  userId: '',

  /**
   * 選択されている日誌系マスタID
   */
  sheetno: '',
  systemYmd: {
    value: '',
  },
  defSvJigyoId: '',
})

/**
 * or28256Type
 */
const or28256Type = ref<string>('test')

/**
 * テキスト 現在の編集
 */
const textCurrentEdit = ref(0)
/**
 * フォーカス現在
 */
const focusCurrent = ref(0)
/**
 * ダイアログ表示フラグ
 */
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr27354 = computed(() => {
  // Or27354のダイアログ開閉状態
  return Or27354Logic.state.get(or27354.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr22951 = computed(() => {
  // Or22951のダイアログ開閉状態
  return Or22951Logic.state.get(or22951.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr28256 = computed(() => {
  // Or28256のダイアログ開閉状態
  return Or28256Logic.state.get(or28256.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const isShowDialogOr27094 = computed(() => {
  return Or27094Logic.state.get(Or27094.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ダイアログ表示フラグ
 */
const showDialogOr27374 = computed(() => {
  return Or27374Logic.state.get(or27374.value.uniqueCpId)!.isOpen ?? false
})
/**
 * Or27374OneWayValue
 */
const Or27374OneWayValue: Or27374OnewayType = {
  /**
   * 期間ID
   */
  sc1Id: local.or31869.sc1Id,
  /**
   * 事業所ID
   */
  shisetuId: systemCommonsStore.getShisetuId!,
  /**
   * 利用者ID
   */
  userId: systemCommonsStore.getUserId!,
  /**
   * 事業者ID
   */
  svJigyoId: systemCommonsStore.getSvJigyoId!,
  /**
   * 種別ID
   */
  syubetsuId: systemCommonsStore.getSyubetu!,
  /**
   * システム年月日
   */
  appYmd: systemCommonsStore.getSystemDate!,
  /**
   * 期間管理フラグ
   */
  kikanFlag: local.or31869.kikanFlag,
}

/**
 * or27374Type
 */
const or27374Type = ref<Or27374Type>({
  content1: '',
  content2: '',
})
/**
 * Or27094OnewayModel
 */
const Or27094OnewayModel: Or27094OnewayType = {
  formatSize: 1,
  screenCategory: Or27094Const.SCREEN_CATEGORY.CARE_MANAGER,
  carePlanMethod: Or27094Const.CARE_PLAN_METHOD,
  id: Or27094Const.ID.ID_130000073,
  createDate: local.or31869.createDate,
  heldDate: local.or31869.heldDate,
  userId: systemCommonsStore.getUserId!,
  svJigyoId: systemCommonsStore.getSvJigyoId!,
  shokuinId: '',
  attendeesNumberOfRow1: 0,
  attendeesNumberOfRow2: 0,
  attendeesNumberOfRow3: 0,
}
/**
 * or27094Type
 */
const or27094Type = ref<Or27094TwowayType>({
  newNursingCareElderlyList: [],
  otherNewNursingList: [],
  otherAttendeesMeetingList: [],
})
/**
 * or27354Data
 */
const or27354Data: Or27354OnewayType = {
  /**
   * 期間ID: 共通情報.期間ID
   */
  periodId: local.or31869.sc1Id,
  /**
   * 計画期間管理する場合、: 1
   * 計画期間管理しない場合 : 0
   */
  periodManageFlag: '1',
  /**
   * 事業者ID
   */
  svJigyoId: systemCommonsStore.getSvJigyoId!,
  /**
   * 利用者ID
   */
  userId: systemCommonsStore.getUserId!,
  /**
   * 種別ID
   */
  syubetsuId: systemCommonsStore.getSyubetu!,
  /**
   * 施設ID
   */
  shisetuId: systemCommonsStore.getShisetuId!,
  /**
   * 画面区分
   */
  gamenKbnList: ['2', '3', '4', '5', '6', '7'],
  /**
   * settinggamenKbnList
   *  0: 3 table
   *  1: 2 table
   */
  settingGamenKbnList: {
    '2': '0',
    '3': '0',
    '4': '1',
    '5': '0',
    '6': '0',
    '7': '0',
  },
  transferedShisetsuId: '',
  transferedJiGyoShoId: '',
}

/**
 * or22951 onewayType
 */
const or22951OnewayValue: Or22951OnewayType = {
  systemCode: '',
  functionId: '',
  selectList: [],
  useMode: '',
  freeparmValL: '',
}

/**
 * Or27358Data
 */
const Or27358Data = {
  /**
   * 事業所ID
   */
  svJigyoId: systemCommonsStore.getSvJigyoId!,
  /**
   * 利用者ID
   */
  userId: systemCommonsStore.getUserId!,
  /**
   * 種別ID
   */
  shubetsuId: systemCommonsStore.getSyubetu!,
  /**
   * 施設ID
   */
  shisetuId: systemCommonsStore.getShisetuId!,
  /**
   * 期間管理フラグ
   */
  kikanFlg: '',
}
/**
 * or27358Type
 */
const or27358Type = ref<Or27358Type>({
  yesCount: '',
  noCount: '',
  yesLetter: '',
  noLetter: '',
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr27358 = computed(() => {
  return Or27358Logic.state.get(or27358.value.uniqueCpId)!.isOpen ?? false
})
/**
 * or27354Type
 */
const or27354Type = ref<Or27354Type[]>([])
/**
 * 「開催場所アイコンボタン」押下
 */
// const endTime = ref()
// const startTime = ref()
function onClickHeldLocation() {
  // Gui00064のダイアログ開閉状態を更新する
  Gui00064Logic.state.set({
    uniqueCpId: gui00064.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function onClickDialogTime() {
  // TODO: GUI00055_時間入力支援(範囲選択)をポップアップで起動する。
}
/**
 * 「開催回数アイコンボタン」押下
 */
// function onClickHeldNumberOfTimes() {
//   const value = Number(local.mo00038HeldNumberOfTimes.mo00045.value) + 1
//   local.mo00038HeldNumberOfTimes.mo00045.value = value.toString()
// }

const showDialogOr55476 = computed(() => {
  // Or55476のダイアログ開閉状態
  return Or55476Logic.state.get(or55476.value.uniqueCpId)?.isOpen ?? false
})
const or55476OneWayType = ref<Or55476OneWayType>({
  userId: systemCommonsStore.getUserId!,
  telCellFlg: '1',
  createYmd: '2025/05/29',
  kinouId: '',
})
/**
 * 「関係者選択アイコンボタン」押下
 */
function onClickResidentAndFamilyParticipation() {
  // GUI01302 関係者選択画面をポップアップで起動する
  Or55476Logic.state.set({
    uniqueCpId: or55476.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「行追加ボタン」押下
 */
function onClickAddRow() {
  if (local.or31869.pattern === Or01444Const.PATTERN_ID_1) {
    const length = local.mo01354Affiliation1.values.items.length
    local.mo01354Affiliation1.values.items.push({
      id: `${length}_${uuidv4()}_affiliation1`,
      affiliation: { value: '' },
      name: { value: '' },
    })
    local.mo01354Affiliation2.values.items.push({
      id: `${length}_${uuidv4()}_affiliation2`,
      affiliation: { value: '' },
      name: { value: '' },
    })
    local.mo01354Affiliation3.values.items.push({
      id: `${length}_${uuidv4()}_affiliation3`,
      affiliation: { value: '' },
      name: { value: '' },
    })
  } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_2) {
    const length = local.mo01354MeetingAttendees.values.items.length
    local.mo01354MeetingAttendees.values.items.push({
      id: `${length}_${uuidv4()}_meetingAttendees`,
      meetingAttendees: { value: '' },
    })
  } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_3) {
    const length = local.mo01354MainManager1.values.items.length
    local.mo01354MainManager1.values.items.push({
      id: `${length}_${uuidv4()}_mainManager1`,
      affiliation: { value: '' },
      jobType: { value: '' },
      name: { value: '' },
      mainManager: { value: '' },
    })
    local.mo01354MainManager2.values.items.push({
      id: `${length}_${uuidv4()}_mainManager2`,
      affiliation: { value: '' },
      jobType: { value: '' },
      name: { value: '' },
      mainManager: { value: '' },
    })
  }
}

/**
 * 「行挿入ボタン」押下
 */
function onClickInsertRow() {
  if (local.or31869.pattern === Or01444Const.PATTERN_ID_1) {
    const length = local.mo01354Affiliation1.values.items.length
    let selectRowId = '-1'
    let index = -1
    if (local.mo01354Affiliation1.values.selectedRowId !== '-1') {
      selectRowId = local.mo01354Affiliation1.values.selectedRowId
      index = local.mo01354Affiliation1.values.items.findIndex((item) => item.id === selectRowId)
    } else if (local.mo01354Affiliation2.values.selectedRowId !== '-1') {
      selectRowId = local.mo01354Affiliation2.values.selectedRowId
      index = local.mo01354Affiliation2.values.items.findIndex((item) => item.id === selectRowId)
    } else if (local.mo01354Affiliation3.values.selectedRowId !== '-1') {
      selectRowId = local.mo01354Affiliation3.values.selectedRowId
      index = local.mo01354Affiliation3.values.items.findIndex((item) => item.id === selectRowId)
    }
    if (index > -1) {
      local.mo01354Affiliation1.values.items.splice(index, 0, {
        id: `${length}_${uuidv4()}_affiliation1`,
        affiliation: { value: '' },
        name: { value: '' },
      })
      local.mo01354Affiliation2.values.items.splice(index, 0, {
        id: `${length}_${uuidv4()}_affiliation2`,
        affiliation: { value: '' },
        name: { value: '' },
      })
      local.mo01354Affiliation3.values.items.splice(index, 0, {
        id: `${length}_${uuidv4()}_affiliation3`,
        affiliation: { value: '' },
        name: { value: '' },
      })
    }
  } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_2) {
    const length = local.mo01354MeetingAttendees.values.items.length
    const selectRowId = local.mo01354MeetingAttendees.values.selectedRowId
    const index = local.mo01354MeetingAttendees.values.items.findIndex(
      (item) => item.id === selectRowId
    )
    if (index > -1) {
      local.mo01354MeetingAttendees.values.items.splice(index, 0, {
        id: `${length}_${uuidv4()}_meetingAttendees`,
        meetingAttendees: { value: '' },
      })
    }
  } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_3) {
    const length = local.mo01354MainManager1.values.items.length
    let selectRowId = '-1'
    let index = -1
    if (local.mo01354MainManager1.values.selectedRowId !== '-1') {
      selectRowId = local.mo01354MainManager1.values.selectedRowId
      index = local.mo01354MainManager1.values.items.findIndex((item) => item.id === selectRowId)
    } else if (local.mo01354MainManager2.values.selectedRowId !== '-1') {
      selectRowId = local.mo01354MainManager2.values.selectedRowId
      index = local.mo01354MainManager2.values.items.findIndex((item) => item.id === selectRowId)
    }
    if (index > -1) {
      local.mo01354MainManager1.values.items.splice(index, 0, {
        id: `${length}_${uuidv4()}_mainManager1`,
        affiliation: { value: '' },
        jobType: { value: '' },
        name: { value: '' },
        mainManager: { value: '' },
      })
      local.mo01354MainManager2.values.items.splice(index, 0, {
        id: `${length}_${uuidv4()}_mainManager2`,
        affiliation: { value: '' },
        jobType: { value: '' },
        name: { value: '' },
        mainManager: { value: '' },
      })
    }
  }
}

/**
 * 「行削除ボタン」押下
 */
async function onClickDeleteRow() {
  if (local.or31869.pattern === Or01444Const.PATTERN_ID_1) {
    let selectRowId = '-1'
    let index = -1

    if (local.mo01354Affiliation1.values.selectedRowId !== '-1') {
      selectRowId = local.mo01354Affiliation1.values.selectedRowId
      index = local.mo01354Affiliation1.values.items.findIndex((item) => item.id === selectRowId)
    } else if (local.mo01354Affiliation2.values.selectedRowId !== '-1') {
      selectRowId = local.mo01354Affiliation2.values.selectedRowId
      index = local.mo01354Affiliation2.values.items.findIndex((item) => item.id === selectRowId)
    } else if (local.mo01354Affiliation3.values.selectedRowId !== '-1') {
      selectRowId = local.mo01354Affiliation3.values.selectedRowId
      index = local.mo01354Affiliation3.values.items.findIndex((item) => item.id === selectRowId)
    }
    if (index === -1) {
      return
    }
  } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_3) {
    let selectRowId = '-1'
    let index = -1
    if (local.mo01354MainManager1.values.selectedRowId !== '-1') {
      selectRowId = local.mo01354MainManager1.values.selectedRowId
      index = local.mo01354MainManager1.values.items.findIndex((item) => item.id === selectRowId)
    } else if (local.mo01354MainManager2.values.selectedRowId !== '-1') {
      selectRowId = local.mo01354MainManager2.values.selectedRowId
      index = local.mo01354MainManager2.values.items.findIndex((item) => item.id === selectRowId)
    }
    if (index === -1) {
      return
    }
  } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_2) {
    const selectRowId = local.mo01354MeetingAttendees.values.selectedRowId
    const index = local.mo01354MeetingAttendees.values.items.findIndex(
      (item) => item.id === selectRowId
    )
    if (index === -1) {
      return
    }
  }
  const rs = await showMessageICom0008()
  if (rs.firstBtnClickFlg) {
    if (local.or31869.pattern === Or01444Const.PATTERN_ID_1) {
      let selectRowId = '-1'
      let index = -1
      if (local.mo01354Affiliation1.values.selectedRowId !== '-1') {
        selectRowId = local.mo01354Affiliation1.values.selectedRowId
        index = local.mo01354Affiliation1.values.items.findIndex((item) => item.id === selectRowId)
      } else if (local.mo01354Affiliation2.values.selectedRowId !== '-1') {
        selectRowId = local.mo01354Affiliation2.values.selectedRowId
        index = local.mo01354Affiliation2.values.items.findIndex((item) => item.id === selectRowId)
      } else if (local.mo01354Affiliation3.values.selectedRowId !== '-1') {
        selectRowId = local.mo01354Affiliation3.values.selectedRowId
        index = local.mo01354Affiliation3.values.items.findIndex((item) => item.id === selectRowId)
      }
      if (index > -1) {
        let temp: Mo01354Items[] = []
        await nextTick(() => {
          local.mo01354Affiliation1.values.items.splice(index, 1)
          local.mo01354Affiliation2.values.items.splice(index, 1)
          local.mo01354Affiliation3.values.items.splice(index, 1)
          if (local.mo01354Affiliation1.values.selectedRowId !== '-1') {
            temp = local.mo01354Affiliation1.values.items
            local.mo01354Affiliation1.values.items = []
          } else if (local.mo01354Affiliation2.values.selectedRowId !== '-1') {
            temp = local.mo01354Affiliation2.values.items
            local.mo01354Affiliation2.values.items = []
          } else if (local.mo01354Affiliation3.values.selectedRowId !== '-1') {
            temp = local.mo01354Affiliation3.values.items
            local.mo01354Affiliation3.values.items = []
          }
        })
        await nextTick()
        const length = temp.length
        for (const item of temp) {
          if (local.mo01354Affiliation1.values.selectedRowId !== '-1') {
            local.mo01354Affiliation1.values.items.push(item)
          } else if (local.mo01354Affiliation2.values.selectedRowId !== '-1') {
            local.mo01354Affiliation2.values.items.push(item)
          } else if (local.mo01354Affiliation3.values.selectedRowId !== '-1') {
            local.mo01354Affiliation3.values.items.push(item)
          }
        }
        if (length < 3) {
          local.mo01354Affiliation1.values.items.push({
            id: `${length}_${uuidv4()}_affiliation1`,
            affiliation: { value: '' },
            name: { value: '' },
          })
          local.mo01354Affiliation2.values.items.push({
            id: `${length}_${uuidv4()}_affiliation2`,
            affiliation: { value: '' },
            name: { value: '' },
          })
          local.mo01354Affiliation3.values.items.push({
            id: `${length}_${uuidv4()}_affiliation3`,
            affiliation: { value: '' },
            name: { value: '' },
          })
        }
      }
    } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_2) {
      const selectRowId = local.mo01354MeetingAttendees.values.selectedRowId
      const index = local.mo01354MeetingAttendees.values.items.findIndex(
        (item) => item.id === selectRowId
      )
      let temp: Mo01354Items[] = []
      await nextTick(() => {
        local.mo01354MeetingAttendees.values.items.splice(index, 1)
        temp = local.mo01354MeetingAttendees.values.items
        local.mo01354MeetingAttendees.values.items = []
      })
      await nextTick()
      const length = temp.length
      for (const item of temp) {
        local.mo01354MeetingAttendees.values.items.push(item)
      }
      if (length < 3) {
        local.mo01354MeetingAttendees.values.items.push({
          id: `${length}_${uuidv4()}_meetingAttendees`,
          meetingAttendees: { value: '' },
        })
      }
    } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_3) {
      let selectRowId = '-1'
      let index = -1
      if (local.mo01354MainManager1.values.selectedRowId !== '-1') {
        selectRowId = local.mo01354MainManager1.values.selectedRowId
        index = local.mo01354MainManager1.values.items.findIndex((item) => item.id === selectRowId)
      } else if (local.mo01354MainManager2.values.selectedRowId !== '-1') {
        selectRowId = local.mo01354MainManager2.values.selectedRowId
        index = local.mo01354MainManager2.values.items.findIndex((item) => item.id === selectRowId)
      }
      if (index > -1) {
        let temp: Mo01354Items[] = []
        await nextTick(() => {
          local.mo01354MainManager1.values.items.splice(index, 1)
          local.mo01354MainManager2.values.items.splice(index, 1)
          if (local.mo01354MainManager1.values.selectedRowId !== '-1') {
            temp = local.mo01354MainManager1.values.items
            local.mo01354MainManager1.values.items = []
          } else if (local.mo01354MainManager2.values.selectedRowId !== '-1') {
            temp = local.mo01354MainManager2.values.items
            local.mo01354MainManager2.values.items = []
          }
        })
        await nextTick()
        const length = temp.length
        for (const item of temp) {
          if (local.mo01354MainManager1.values.selectedRowId !== '-1') {
            local.mo01354MainManager1.values.items.push(item)
          } else if (local.mo01354MainManager2.values.selectedRowId !== '-1') {
            local.mo01354MainManager2.values.items.push(item)
          }
        }
        if (length < 3) {
          local.mo01354MainManager1.values.items.push({
            id: `${length}_${uuidv4()}_mainManager1`,
            affiliation: { value: '' },
            jobType: { value: '' },
            name: { value: '' },
            mainManager: { value: '' },
          })
          local.mo01354MainManager2.values.items.push({
            id: `${length}_${uuidv4()}_mainManager2`,
            affiliation: { value: '' },
            jobType: { value: '' },
            name: { value: '' },
            mainManager: { value: '' },
          })
        }
      }
    }
  } else if (rs.secondBtnClickFlg) {
    return
  }
}
/**
 * 「会議出席者取込ボタン」押下
 */
function onClickAttendees() {
  // GUI01123 会議出席者取込画面をポップアップで起動する。
  Or27094OnewayModel.carePlanMethod = Or27094Const.CARE_PLAN_METHOD
  if (local.or31869.pattern === Or01444Const.PATTERN_ID_1) {
    Or27094OnewayModel.id = Or27094Const.ID.ID_130000075
    const length = local.mo01354Affiliation1.values.items.length
    Or27094OnewayModel.attendeesNumberOfRow1 = 0
    Or27094OnewayModel.attendeesNumberOfRow2 = 0
    Or27094OnewayModel.attendeesNumberOfRow3 = length
    or27094Type.value.newNursingCareElderlyList = []
    or27094Type.value.otherNewNursingList = []
    or27094Type.value.otherAttendeesMeetingList = []
    for (let i = 0; i < length; i++) {
      or27094Type.value.otherNewNursingList.push({
        id: local.mo01354Affiliation1.values.items[i].id,
        affiliation1: local.mo01354Affiliation1.values.items[i].affiliation as { value: string },
        name1: local.mo01354Affiliation1.values.items[i].name as { value: string },
        affiliation2: local.mo01354Affiliation2.values.items[i].affiliation as { value: string },
        name2: local.mo01354Affiliation2.values.items[i].name as { value: string },
        affiliation3: local.mo01354Affiliation3.values.items[i].affiliation as { value: string },
        name3: local.mo01354Affiliation3.values.items[i].name as { value: string },
      })
    }
  } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_2) {
    Or27094OnewayModel.id = Or27094Const.ID.ID_130000075
    const length = local.mo01354MeetingAttendees.values.items.length
    Or27094OnewayModel.attendeesNumberOfRow1 = 0
    Or27094OnewayModel.attendeesNumberOfRow2 = length
    Or27094OnewayModel.attendeesNumberOfRow3 = 0
    or27094Type.value.newNursingCareElderlyList = []
    or27094Type.value.otherNewNursingList = []
    or27094Type.value.otherAttendeesMeetingList = []
    for (let i = 0; i < length; i++) {
      or27094Type.value.otherAttendeesMeetingList.push({
        id: local.mo01354MeetingAttendees.values.items[i].id,
        affiliation1: local.mo01354MeetingAttendees.values.items[i].meetingAttendeesas as {
          value: string
        },
        name1: {
          value: '',
        },
        affiliation2: {
          value: '',
        },
        name2: {
          value: '',
        },
      })
    }
  } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_3) {
    Or27094OnewayModel.id = Or27094Const.ID.ID_42020001
    const length = local.mo01354MainManager1.values.items.length
    Or27094OnewayModel.attendeesNumberOfRow1 = length
    Or27094OnewayModel.attendeesNumberOfRow2 = 0
    Or27094OnewayModel.attendeesNumberOfRow3 = 0
    or27094Type.value.newNursingCareElderlyList = []
    or27094Type.value.otherNewNursingList = []
    or27094Type.value.otherAttendeesMeetingList = []
    for (let i = 0; i < length; i++) {
      or27094Type.value.newNursingCareElderlyList.push({
        id: local.mo01354MainManager1.values.items[i].id,
        affiliation1: local.mo01354MainManager1.values.items[i].affiliation as { value: string },
        occupation1: local.mo01354MainManager1.values.items[i].jobType as { value: string },
        name1: local.mo01354MainManager1.values.items[i].name as { value: string },
        mainDuties1: local.mo01354MainManager2.values.items[i].mainManager as { value: string },
        affiliation2: local.mo01354MainManager2.values.items[i].affiliation as { value: string },
        occupation2: local.mo01354MainManager2.values.items[i].jobType as { value: string },
        name2: local.mo01354MainManager2.values.items[i].name as { value: string },
        mainDuties2: local.mo01354MainManager2.values.items[i].mainManager as { value: string },
      })
    }
  }
  Or27094Logic.state.set({
    uniqueCpId: Or27094.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 「文字入力支援ボタン」押下
 */
// function onClickLetterInputSupport() {
//   memoData = {
//     or51775Oneway: {
//       title: t('label.letter-input-support'),
//       t1Cd: '0',
//       t2Cd: '6',
//       t3Cd: '0',
//       tableName: '',
//       field: '',
//       textContent: '',
//       userId: systemCommonsStore.getUserId!,
//       screenId: '',
//       bunruiId: '',
//       columnName: '',
//       assessmentMethod: '',
//       inputContents: '',
//       mode: '',
//     } as Or51775OnewayType,
//   }
//   // Or51775のダイアログを開く
//   Or51775Logic.state.set({
//     uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
//     state: { isOpen: true }, // ダイアログを開くフラグ
//   })
// }

/**
 * 「ケース取込アイコンボタン」押下
 */
function onClickCaseImport() {
  // GUI02259 適用事業所の選択画面をポップアップで起動する。
  // Or22951のダイアログ開閉状態を更新する
  Or22951Logic.state.set({
    uniqueCpId: or22951.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function onClickCaseImportSubmit(value: ApplicableOfficeResponseInfo[]) {
  // スクリーンGUI01016への呼び出し
  or28256Oneway.value.houjinId = value[0].houjinId ?? ''
  or28256Oneway.value.sisetsuId = value[0].shisetuId ?? ''
  or28256Oneway.value.userId = systemCommonsStore.getUserId ?? ''
  or28256Oneway.value.kaisihi.value = local.or31869.startYmd ?? ''
  or28256Oneway.value.syuuryouhi.value = local.or31869.endYmd ?? ''
  or28256Oneway.value.targetItem = getTargetItemTitle(focusCurrent.value)
  or28256Oneway.value.caseInformation = getTargetItemContent(focusCurrent.value)
  // Or28256のダイアログ開閉状態を更新する
  Or28256Logic.state.set({
    uniqueCpId: or28256.value.uniqueCpId,
    state: {
      isOpen: true,
      items: [],
    },
  })
}

/**
 * ターゲット項目のコンテンツを取得する
 *
 * @param focusValue - フォーカス値
 *
 * @returns ターゲット項目のコンテンツ文字列
 */
function getTargetItemContent(focusValue: number): string {
  switch (focusValue) {
    case Or31869Const.TEXTCURRENTEDIT.REASON_ABSENCE:
      return local.mo00046ReasonAbsence.value
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_ITEM:
      return local.mo00046ConsiderItem.value
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_CONTENTS:
      return local.mo00046ConsiderContents.value
    case Or31869Const.TEXTCURRENTEDIT.CONCLUSION:
      return local.mo00046Conclusion.value
    case Or31869Const.TEXTCURRENTEDIT.REMAINING_ISSUES:
      return local.mo00046RemainingIssues.value
    case Or31869Const.TEXTCURRENTEDIT.MAIN_TOPIC_DISCUSSED:
      return local.mo00046MainTopicDiscussed.value
    case Or31869Const.TEXTCURRENTEDIT.PROVISION_SERVICES:
      return local.mo00046ProvisionService.value
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_MATTER_CONTENTS:
      return local.mo00046ConsiderMatterContents.value
    case Or31869Const.TEXTCURRENTEDIT.METTING_CONCLUSION:
      return local.mo00046MeetingConclusion.value
    case Or31869Const.TEXTCURRENTEDIT.REMAINING_AND_NEW_CHALLENGES:
      return local.mo00046RemainingAndNewChallenges.value
    case Or31869Const.TEXTCURRENTEDIT.COMPREHENSIVE:
      return local.mo00046Comprehensive.value
    case Or31869Const.TEXTCURRENTEDIT.IMPLEMENTATION:
      return local.mo00046Implementation.value
    case Or31869Const.TEXTCURRENTEDIT.ABSENTEE_CORRESPONDENCE:
      return local.mo00046AbsenteeCorrespondence.value
    default:
      return ''
  }
}

/**
/**
 * ターゲット項目のタイトルを取得する
 *
 * @param focusValue - フォーカス値
 *
 * @returns ターゲット項目のタイトル文字列
 */
function getTargetItemTitle(focusValue: number): string {
  switch (focusValue) {
    case Or31869Const.TEXTCURRENTEDIT.REASON_ABSENCE:
      return t('label.reason-absence')
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_ITEM:
      return t('label.consider-item')
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_CONTENTS:
      return t('label.consider-contents')
    case Or31869Const.TEXTCURRENTEDIT.CONCLUSION:
      return t('label.conclusion')
    case Or31869Const.TEXTCURRENTEDIT.REMAINING_ISSUES:
      return t('label.remaining-issues')
    case Or31869Const.TEXTCURRENTEDIT.MAIN_TOPIC_DISCUSSED:
      return t('label.main-topics-discussed')
    case Or31869Const.TEXTCURRENTEDIT.PROVISION_SERVICES:
      return t('label.provision-services-based-on-implementation-plan-other-matters')
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_MATTER_CONTENTS:
      return t('label.consider-matter-contents')
    case Or31869Const.TEXTCURRENTEDIT.METTING_CONCLUSION:
      return t('label.meeting-conclusion')
    case Or31869Const.TEXTCURRENTEDIT.REMAINING_AND_NEW_CHALLENGES:
      return t('label.remaining-and-new-challenges')
    case Or31869Const.TEXTCURRENTEDIT.COMPREHENSIVE:
      return t('label.comprehensive-plan-policy-modified-necessity')
    case Or31869Const.TEXTCURRENTEDIT.IMPLEMENTATION:
      return t('label.implementation-plan-policy-modified-necessity')
    case Or31869Const.TEXTCURRENTEDIT.ABSENTEE_CORRESPONDENCE:
      return t('label.absentee-correspondence')
    default:
      return ''
  }
}

/**
 * 「内容取込アイコンボタン」押下
 */
function onClickContentsImport() {
  // GUI01125 内容取込画面をポップアップで起動する。
  or27354Data.periodManageFlag = '1'
  // Or27354のダイアログ開閉状態を更新する
  Or27354Logic.state.set({
    uniqueCpId: or27354.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const Or27553OneWayValue: Or27553OnewayType = {
  /**
   * 期間管理フラグ
   */
  kanriFlg: '',
}

const refValueOr27553 = ref<Or27553Type>({
  considerItems: '',
  remainIssues: '',
})
const showDialogOr27553 = computed(() => {
  return Or27553Logic.state.get(Or27553.value.uniqueCpId)!.isOpen ?? false
})
/**
 * 「課題取込アイコンボタン」押下
 */
function onClickIssuesImport() {
  if (local.or31869.cpnFlg !== '5' && local.or31869.svJigyoCd === '50010') {
    // GUI01128 予防計画書課題取込画面をポップアップで起動する。
    Or27553OneWayValue.kanriFlg = local.or31869.kikanFlag
    // Or27553のダイアログ開閉状態を更新する
    Or27553Logic.state.set({
      uniqueCpId: Or27553.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
  if (local.or31869.cpnFlg !== '5' && local.or31869.svJigyoCd !== '50010') {
    // GUI01126 計画書（2）ニーズ取込画面をポップアップで起動する。
    Or27358Data.kikanFlg = local.or31869.kikanFlag
    // Or27358のダイアログ開閉状態を更新する
    Or27358Logic.state.set({
      uniqueCpId: or27358.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
  if (local.or31869.cpnFlg === '5') {
    // GUI01127 ［課題取込］画面をポップアップで起動する。
    Or27374OneWayValue.kikanFlag = local.or31869.kikanFlag
    // Or27374のダイアログ開閉状態を更新する
    Or27374Logic.state.set({
      uniqueCpId: or27374.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 「欠席者及び欠席理由入力支援アイコンボタン」押下
 */
function onClickReasonAbsence() {
  /**
   * タイトル："欠席者及び欠席理由"
     大分類CD：910
     中分類CD：7
     小分類CD：0
     テーブル名："kgh_tuc_krk_kaigi1"
     カラム名："riyu_knj"
     アセスメント方式：共通情報.アセスメント方式
     文章内容：画面.欠席者及び欠席理由
     利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.REASON_ABSENCE
  memoData = {
    or51775Oneway: {
      title: t('label.reason-absence'),
      t1Cd: '910',
      t2Cd: '7',
      t3Cd: '0',
      tableName: 'kgh_tuc_krk_kaigi1',
      field: 'riyu_knj',
      textContent: '',
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 「検討した項目入力支援アイコンボタン」押下
 */
function onClickConsiderItem() {
  /**
   * タイトル："検討した項目"
    大分類CD：910
    中分類CD：3
    小分類CD：0
    テーブル名："kgh_tuc_krk_kaigi1"
    カラム名："kento_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.検討した項目
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.CONSIDER_ITEM
  memoData = {
    or51775Oneway: {
      title: t('label.consider-item'),
      t1Cd: '910',
      t2Cd: '3',
      t3Cd: '0',
      tableName: 'kgh_tuc_krk_kaigi1',
      field: 'kento_knj',
      textContent: '',
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 「検討した項目拡大縮小アイコンボタン」押下
 */
// function onClickConsiderItemZoom() {
//   local.considerItemZoom = !local.considerItemZoom
// }

/**
 * 「検討内容入力支援アイコンボタン」押下
 */
function onClickConsiderContents() {
  /**
   * タイトル："検討内容"
    大分類CD：910
    中分類CD：4
    小分類CD：0
    テーブル名："kgh_tuc_krk_kaigi1"
    カラム名："memo_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.検討内容
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.CONSIDER_CONTENTS
  memoData = {
    or51775Oneway: {
      title: t('label.consider-contents'),
      t1Cd: '910',
      t2Cd: '4',
      t3Cd: '0',
      tableName: 'kgh_tuc_krk_kaigi1',
      field: 'memo_knj',
      textContent: local.mo00046ConsiderContents.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}
/**
 * 「検討内容拡大縮小アイコンボタン」押下
 */
// function onClickConsiderContentsZoom() {
//   local.considerContentsZoom = !local.considerContentsZoom
// }

/**
 * 「結論入力支援アイコンボタン」押下
 */
function onClickConclusion() {
  /**
   * タイトル："結論"
    大分類CD：910
    中分類CD：5
    小分類CD：0
    テーブル名："kgh_tuc_krk_kaigi1"
    カラム名："ketsuron_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.結論
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.CONCLUSION
  memoData = {
    or51775Oneway: {
      title: t('label.conclusion'),
      t1Cd: '910',
      t2Cd: '5',
      t3Cd: '0',
      tableName: 'kgh_tuc_krk_kaigi1',
      field: 'ketsuron_knj',
      textContent: local.mo00046Conclusion.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}
/**
 * 「結論拡大縮小アイコンボタン」押下
 */
// function onClickConclusionZoom() {
//   local.conclusionZoom = !local.conclusionZoom
// }

/**
 * 「残された課題入力支援アイコンボタン」押下
 */
function onClickRemainingIssues() {
  /**
   * タイトル："残された課題"
    大分類CD：910
    中分類CD：6
    小分類CD：0
    テーブル名："kgh_tuc_krk_kaigi1"
    カラム名："kadai_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.残された課題
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.REMAINING_ISSUES
  memoData = {
    or51775Oneway: {
      title: t('label.remaining-issues'),
      t1Cd: '910',
      t2Cd: '6',
      t3Cd: '0',
      tableName: 'kgh_tuc_krk_kaigi1',
      field: 'kadai_knj',
      textContent: local.mo00046RemainingIssues.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}
/**
 * 「残された課題拡大縮小アイコンボタン」押下
 */
// function onClickRemainingIssuesZoom() {
//   local.remainingIssuesZoom = !local.remainingIssuesZoom
// }

/**
 * 「主な討議内容入力支援アイコンボタン」押下
 */
function onClickMainTopicDiscussed() {
  /**
   * タイトル："主な討議内容"
    大分類CD：910
    中分類CD：4
    小分類CD：0
    テーブル名："kgh_tuc_krk_kaigi1"
    カラム名："memo_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.主な討議内容
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.MAIN_TOPIC_DISCUSSED
  memoData = {
    or51775Oneway: {
      title: t('label.main-topics-discussed'),
      t1Cd: '910',
      t2Cd: '4',
      t3Cd: '0',
      tableName: 'kgh_tuc_krk_kaigi1',
      field: 'memo_knj',
      textContent: local.mo00046MainTopicDiscussed.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}
/**
 * 「主な討議内容拡大縮小アイコンボタン」押下
 */
// function onClickMainTopicDiscussedZoom() {
//   local.mainTopicDiscussedZoom = !local.mainTopicDiscussedZoom
// }
/**
 * 「実施計画に基づくサービス提供その他検討した事項入力支援アイコンボタン」押下
 */
function onClickProvisionService() {
  /**
   * タイトル："実施計画に基づくサービス提供その他検討した事項"
    大分類CD：2600
    中分類CD：2
    小分類CD：0
    テーブル名："cpn_tuc_syp_kaigi1"
    カラム名："kento_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.実施計画に基づくサービス提供その他検討した事項
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.PROVISION_SERVICES
  memoData = {
    or51775Oneway: {
      title: t('label.provision-services-based-on-implementation-plan-other-matters'),
      t1Cd: '2600',
      t2Cd: '2',
      t3Cd: '0',
      tableName: 'cpn_tuc_syp_kaigi1',
      field: 'kento_knj',
      textContent: local.mo00046ProvisionService.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 「検討事項の内容入力支援アイコンボタン」押下
 */
function onClickConsiderMatterContents() {
  /**
   * タイトル："検討事項の内容"
    大分類CD：2600
    中分類CD：2
    小分類CD：0
    テーブル名："cpn_tuc_syp_kaigi1"
    カラム名："naiyo_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.検討事項の内容
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.CONSIDER_MATTER_CONTENTS
  memoData = {
    or51775Oneway: {
      title: t('label.consider-matter-contents'),
      t1Cd: '2600',
      t2Cd: '2',
      t3Cd: '0',
      tableName: 'cpn_tuc_syp_kaigi1',
      field: 'naiyo_knj',
      textContent: local.mo00046ConsiderMatterContents.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 「会議の結論入力支援アイコンボタン」押下
 */
function onClickMeetingConclusion() {
  /**
   * タイトル："会議の結論"
    大分類CD：2600
    中分類CD：4
    小分類CD：0
    テーブル名："cpn_tuc_syp_kaigi1"
    カラム名："ketsuron_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.会議の結論
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.METTING_CONCLUSION
  memoData = {
    or51775Oneway: {
      title: t('label.meeting-conclusion'),
      t1Cd: '2600',
      t2Cd: '4',
      t3Cd: '0',
      tableName: 'cpn_tuc_syp_kaigi1',
      field: 'ketsuron_knj',
      textContent: local.mo00046MeetingConclusion.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 「残された課題や新たな課題入力支援アイコンボタン」押下
 */
function onClickRemainingAndNewChallenges() {
  /**
   * タイトル："残された課題や新たな課題"
    大分類CD：2600
    中分類CD：5
    小分類CD：0
    テーブル名："cpn_tuc_syp_kaigi1"
    カラム名："kento_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.残された課題や新たな課題
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.REMAINING_AND_NEW_CHALLENGES
  memoData = {
    or51775Oneway: {
      title: t('label.remaining-and-new-challenges'),
      t1Cd: '2600',
      t2Cd: '5',
      t3Cd: '0',
      tableName: 'cpn_tuc_syp_kaigi1',
      field: 'kento_knj',
      textContent: local.mo00046RemainingAndNewChallenges.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 「総合計画方針の変更の必要性入力支援アイコンボタン」押下
 */
function onClickComprehensive() {
  /**
   * タイトル："総合計画方針の変更の必要性"
    大分類CD：2600
    中分類CD：6
    小分類CD：0
    テーブル名："cpn_tuc_syp_kaigi1"
    カラム名："sogo_henko_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.総合計画方針の変更の必要性
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.COMPREHENSIVE
  memoData = {
    or51775Oneway: {
      title: t('label.comprehensive-plan-policy-modified-necessity'),
      t1Cd: '2600',
      t2Cd: '6',
      t3Cd: '0',
      tableName: 'cpn_tuc_syp_kaigi1',
      field: 'sogo_henko_knj',
      textContent: local.mo00046Comprehensive.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 「実施計画方針の変更の必要性入力支援アイコンボタン」押下
 */
function onClickImplementation() {
  /**
   * タイトル："実施計画方針の変更の必要性"
    大分類CD：2600
    中分類CD：7
    小分類CD：0
    テーブル名："cpn_tuc_syp_kaigi1"
    カラム名："keikaku_henko_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.実施計画方針の変更の必要性
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.IMPLEMENTATION
  memoData = {
    or51775Oneway: {
      title: t('label.implementation-plan-policy-modified-necessity'),
      t1Cd: '2600',
      t2Cd: '7',
      t3Cd: '0',
      tableName: 'cpn_tuc_syp_kaigi1',
      field: 'keikaku_henko_knj',
      textContent: local.mo00046Implementation.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 「欠席者への対応入力支援アイコンボタン」押下
 */
function onClickAbsenteeCorrespondence() {
  /**
   * タイトル："欠席者への対応"
    大分類CD：2600
    中分類CD：8
    小分類CD：0
    テーブル名："cpn_tuc_syp_kaigi1"
    カラム名："kesseki_taio_knj"
    アセスメント方式：共通情報.アセスメント方式
    文章内容：画面.欠席者への対応
    利用者ID：共通情報.利用者ID
   */
  textCurrentEdit.value = Or31869Const.TEXTCURRENTEDIT.ABSENTEE_CORRESPONDENCE
  memoData = {
    or51775Oneway: {
      title: t('label.absentee-correspondence'),
      t1Cd: '2600',
      t2Cd: '8',
      t3Cd: '0',
      tableName: 'cpn_tuc_syp_kaigi1',
      field: 'kesseki_taio_knj',
      textContent: local.mo00046AbsenteeCorrespondence.value,
      userId: systemCommonsStore.getUserId!,
      screenId: '',
      bunruiId: '',
      columnName: '',
      assessmentMethod: '',
      inputContents: '',
      mode: '',
    } as Or51775OnewayType,
  }
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * ダイアログ表示フラグ
 */
const showDialog = computed(() => {
  // Gui00064のダイアログ開閉状態
  return Gui00064Logic.state.get(gui00064.value.uniqueCpId)?.isOpen ?? false
})

watch(
  () => Gui00064Logic.event.get(gui00064.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.confirmFlg && newValue.confirmParams) {
      local.mo00045HeldLocation.value = newValue.confirmParams.meetingLocation.value
      // 子コンポーネントのflgをリセットする
      Gui00064Logic.event.set({
        uniqueCpId: gui00064.value.uniqueCpId,
        events: {
          confirmFlg: false,
          confirmParams: undefined,
        },
      })
    }
  }
)

watch(
  () => or28256Type.value,
  (newValue) => {
    if (newValue !== undefined && newValue !== '') {
      // GUI01016の返却情報を設定する
      if (local.or31869.cpnFlg === '5') {
        // 新型養護老人ホームパッケージプランの場合
        local.mo00046ConsiderMatterContents.value = newValue
      } else {
        // その他のアセスメント方式の場合
        switch (focusCurrent.value) {
          case Or31869Const.TEXTCURRENTEDIT.REASON_ABSENCE:
            local.mo00046ReasonAbsence.value = newValue
            break
          case Or31869Const.TEXTCURRENTEDIT.CONSIDER_ITEM:
            local.mo00046ConsiderItem.value = newValue
            break
          case Or31869Const.TEXTCURRENTEDIT.CONSIDER_CONTENTS:
            local.mo00046ConsiderContents.value = newValue
            break
          case Or31869Const.TEXTCURRENTEDIT.CONCLUSION:
            local.mo00046Conclusion.value = newValue
            break
          case Or31869Const.TEXTCURRENTEDIT.REMAINING_ISSUES:
            local.mo00046RemainingIssues.value = newValue
            break
          default:
            // 出席者一覧がフォーカスされている場合または、その他の場合
            if (local.or31869.contentsInfo?.reasonAbsenceDisplay !== '0') {
              // 欠席者及び欠席理由テキストボックスが表示している場合
              local.mo00046ReasonAbsence.value = newValue
            } else {
              // 欠席者及び欠席理由テキストボックスが表示していない場合
              local.mo00046ConsiderItem.value = newValue
            }
            break
        }
      }
    }
  },
  { deep: true }
)

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const handleConfirm = (data: Or51775ConfirmType) => {
  switch (textCurrentEdit.value) {
    case Or31869Const.TEXTCURRENTEDIT.REASON_ABSENCE: {
      if (data.type === '1') {
        local.mo00046ReasonAbsence.value = ''
      }
      local.mo00046ReasonAbsence.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_ITEM: {
      if (data.type === '1') {
        local.mo00046ConsiderItem.value = ''
      }
      local.mo00046ConsiderItem.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_CONTENTS: {
      if (data.type === '1') {
        local.mo00046ConsiderContents.value = ''
      }
      local.mo00046ConsiderContents.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.CONCLUSION: {
      if (data.type === '1') {
        local.mo00046Conclusion.value = ''
      }
      local.mo00046Conclusion.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.REMAINING_ISSUES: {
      if (data.type === '1') {
        local.mo00046RemainingIssues.value = ''
      }
      local.mo00046RemainingIssues.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.MAIN_TOPIC_DISCUSSED: {
      if (data.type === '1') {
        local.mo00046MainTopicDiscussed.value = ''
      }
      local.mo00046MainTopicDiscussed.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.PROVISION_SERVICES: {
      if (data.type === '1') {
        local.mo00046ProvisionService.value = ''
      }
      local.mo00046ProvisionService.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_MATTER_CONTENTS: {
      if (data.type === '1') {
        local.mo00046ConsiderMatterContents.value = ''
      }
      local.mo00046ConsiderMatterContents.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.METTING_CONCLUSION: {
      if (data.type === '1') {
        local.mo00046MeetingConclusion.value = ''
      }
      local.mo00046MeetingConclusion.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.REMAINING_AND_NEW_CHALLENGES: {
      if (data.type === '1') {
        local.mo00046RemainingAndNewChallenges.value = ''
      }
      local.mo00046RemainingAndNewChallenges.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.COMPREHENSIVE: {
      if (data.type === '1') {
        local.mo00046Comprehensive.value = ''
      }
      local.mo00046Comprehensive.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.IMPLEMENTATION: {
      if (data.type === '1') {
        local.mo00046Implementation.value = ''
      }
      local.mo00046Implementation.value += data.value
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.ABSENTEE_CORRESPONDENCE: {
      if (data.type === '1') {
        local.mo00046AbsenteeCorrespondence.value = ''
      }
      local.mo00046AbsenteeCorrespondence.value += data.value
      break
    }
  }
  textCurrentEdit.value = 0
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

// function onFocusReasonAbsence() {
//   focusCurrent.value = Or31869Const.TEXTCURRENTEDIT.REASON_ABSENCE
// }

/**
 *フォーカス考慮項目
 */
// function onFocusConsiderItem() {
//   focusCurrent.value = Or31869Const.TEXTCURRENTEDIT.CONSIDER_ITEM
// }
/**
 * 焦点を当てる 内容を考慮する
 */
// function onFocusConsiderContents() {
//   focusCurrent.value = Or31869Const.TEXTCURRENTEDIT.CONSIDER_CONTENTS
// }
/**
 * 焦点の結論
 */
// function onFocusConclusion() {
//   focusCurrent.value = Or31869Const.TEXTCURRENTEDIT.CONCLUSION
// }

/**
 * 残された課題に焦点を当てる
 */
// function onFocusRemainingIssues() {
//   focusCurrent.value = Or31869Const.TEXTCURRENTEDIT.REMAINING_ISSUES
// }

/**
 * 所属1をクリック行を選択
 */
const clickAffiliation1SelectRow = async () => {
  await nextTick(() => {
    local.mo01354Affiliation2.values.selectedRowId = '-1'
    local.mo01354Affiliation3.values.selectedRowId = '-1'
  })
}
/**
 * 所属2をクリック行を選択
 */
const clickAffiliation2SelectRow = () => {
  local.mo01354Affiliation1.values.selectedRowId = '-1'
  local.mo01354Affiliation3.values.selectedRowId = '-1'
}
/**
 * 所属3をクリック行を選択
 */
const clickAffiliation3SelectRow = () => {
  local.mo01354Affiliation1.values.selectedRowId = '-1'
  local.mo01354Affiliation2.values.selectedRowId = '-1'
}

/**
 * 会議出席者をクリック行を選択
 */
const clickMeetingAttendeesSelectRow = () => {
  //clickMeetingAttendeesSelectRow
}

/**
 * メインマネージャ1の行を選択をクリック
 */
const clickMainManager1SelectRow = () => {
  local.mo01354MainManager2.values.selectedRowId = '-1'
}

/**
 * メインマネージャ2の行を選択をクリック
 */
const clickMainManager2SelectRow = () => {
  local.mo01354MainManager1.values.selectedRowId = '-1'
}

/**
 * メッセージを表示
 *
 * @returns メッセージPromise
 */
async function showMessageICom0008() {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-com-0008'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'destroy1',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  })
  return rs
}
/**
 * 確認ダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - 確認ダイアログOneWayBind領域
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    or21814ResolvePromise = resolve
  })
}

const update = (info: RelatedPersonSelectResInfo) => {
  local.mo00045ResidentAndFamilyParticipation.value = info.nameKnj
}

watch(
  () => local.or31869.attendeesList,
  (newValue) => {
    if (newValue !== undefined) {
      const itemsAffiliation1 = [] as Mo01354Items[]
      const itemsAffiliation2 = [] as Mo01354Items[]
      const itemsAffiliation3 = [] as Mo01354Items[]
      const itemsMeetingAttendees = [] as Mo01354Items[]
      const itemsMainManager1 = [] as Mo01354Items[]
      const itemsMainManager2 = [] as Mo01354Items[]
      newValue.forEach((item, index) => {
        itemsAffiliation1.push({
          id: `${index}_${uuidv4()}_affiliation1`,
          affiliation: { value: item.shozoku1Knj },
          name: { value: item.name1Knj },
        })
        itemsAffiliation2.push({
          id: `${index}_${uuidv4()}_affiliation2`,
          affiliation: { value: item.shozoku2Knj },
          name: { value: item.name2Knj },
        })
        itemsAffiliation3.push({
          id: `${index}_${uuidv4()}_affiliation3`,
          affiliation: { value: item.shozoku3Knj },
          name: { value: item.name3Knj },
        })
        itemsMeetingAttendees.push({
          id: `${index}_${uuidv4()}_meetingAttendees`,
          meetingAttendees: { value: item.susseki },
        })
        itemsMainManager1.push({
          id: `${index}_${uuidv4()}_mainManager1`,
          affiliation: { value: item.shozokuKnj },
          jobType: { value: item.shokushuKnj },
          name: { value: item.nameKnj },
          mainManager: { value: item.tantoKnj },
        })
        itemsMainManager2.push({
          id: `${index}_${uuidv4()}_mainManager2`,
          affiliation: { value: item.shozoku2Knj },
          jobType: { value: String(item.shokushu2Knj) },
          name: { value: item.name2Knj },
          mainManager: { value: item.tanto2Knj },
        })
      })
      // 起動パターン 1
      if (local.or31869.pattern === Or01444Const.PATTERN_ID_1) {
        local.mo01354Affiliation1.values.items = itemsAffiliation1
        local.mo01354Affiliation2.values.items = itemsAffiliation2
        local.mo01354Affiliation3.values.items = itemsAffiliation3
      } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_2) {
        // 起動パターン 2
        local.mo01354MeetingAttendees.values.items = itemsMeetingAttendees
      } else if (local.or31869.pattern === Or01444Const.PATTERN_ID_3) {
        // 起動パターン 3
        local.mo01354MainManager1.values.items = itemsMainManager1
        local.mo01354MainManager2.values.items = itemsMainManager2
      }
      firstTime.value = true
      setChildCpBinds(props.parentUniqueCpId, {
        [Or31869Const.CP_ID(0)]: {
          twoWayValue: {
            ...local.or31869,
            attendeesListResult: [],
          },
        },
      })
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

watch(
  () => local.or31869,
  (newValue) => {
    if (newValue.heldInfo?.kaigiYmd !== undefined) {
      local.mo00020HeldDate.value = local.or31869.heldInfo.kaigiYmd
    } else {
      local.mo00020HeldDate.value = ''
    }
    if (newValue.heldInfo?.whereKnj !== undefined) {
      local.mo00045HeldLocation.value = newValue.heldInfo?.whereKnj
    } else {
      local.mo00045HeldLocation.value = ''
    }
    if (newValue.heldInfo?.timeHm !== undefined) {
      local.mo00045HeldTime.value = newValue.heldInfo?.timeHm
    } else {
      local.mo00045HeldTime.value = ''
    }

    if (newValue.heldInfo?.kaisuu !== undefined) {
      local.mo00038HeldNumberOfTimes.mo00045.value = newValue.heldInfo?.kaisuu
    } else {
      local.mo00038HeldNumberOfTimes.mo00045.value = ''
    }
    if (newValue.heldInfo?.honninKnj !== undefined) {
      local.mo00045Person.value = newValue.heldInfo?.honninKnj
    } else {
      local.mo00045Person.value = ''
    }
    if (newValue.heldInfo?.kazokuKnj !== undefined) {
      local.mo00045Family.value = newValue.heldInfo?.kazokuKnj
    } else {
      local.mo00045Family.value = ''
    }

    if (newValue.heldInfo?.zokugaraKnj !== undefined) {
      local.mo00045Relationship.value = newValue.heldInfo?.zokugaraKnj
    } else {
      local.mo00045Relationship.value = ''
    }

    if (newValue.heldInfo?.bikoKnj !== undefined) {
      local.mo00045Remarks.value = newValue.heldInfo?.bikoKnj
    } else {
      local.mo00045Remarks.value = ''
    }

    if (newValue.contentsInfo?.riyuKnj !== undefined) {
      local.mo00046ReasonAbsence.value = newValue.contentsInfo?.riyuKnj
    } else {
      local.mo00046ReasonAbsence.value = ''
    }

    if (newValue.contentsInfo?.kentoKnj !== undefined) {
      local.mo00046ConsiderItem.value = newValue.contentsInfo?.kentoKnj
    } else {
      local.mo00046ConsiderItem.value = ''
    }

    if (newValue.contentsInfo?.memoKnj !== undefined) {
      local.mo00046ConsiderContents.value = newValue.contentsInfo?.memoKnj
    } else {
      local.mo00046ConsiderContents.value = ''
    }

    if (newValue.contentsInfo?.ketsuronKnj !== undefined) {
      local.mo00046Conclusion.value = newValue.contentsInfo?.ketsuronKnj
    } else {
      local.mo00046Conclusion.value = ''
    }

    if (newValue.contentsInfo?.kadaiKnj !== undefined) {
      local.mo00046RemainingIssues.value = newValue.contentsInfo?.kadaiKnj
    } else {
      local.mo00046RemainingIssues.value = ''
    }

    if (newValue.contentsInfo?.jikaiYmd !== undefined) {
      local.mo00020NextEventDate.value = newValue.contentsInfo?.jikaiYmd
    } else {
      local.mo00020NextEventDate.value = ''
    }
    // 起動パターン 2
    if (newValue.contentsInfo?.memoKnj !== undefined) {
      local.mo00046MainTopicDiscussed.value = newValue.contentsInfo?.memoKnj
    } else {
      local.mo00046MainTopicDiscussed.value = ''
    }
    // 起動パターン 3
    if (newValue.contentsInfo?.kentoKnj !== undefined) {
      local.mo00046ProvisionService.value = newValue.contentsInfo?.kentoKnj
    } else {
      local.mo00046ProvisionService.value = ''
    }

    if (newValue.contentsInfo?.naiyoKnj !== undefined) {
      local.mo00046ConsiderMatterContents.value = newValue.contentsInfo?.naiyoKnj
    } else {
      local.mo00046ConsiderMatterContents.value = ''
    }

    if (newValue.contentsInfo?.ketsuronKnj !== undefined) {
      local.mo00046MeetingConclusion.value = newValue.contentsInfo?.ketsuronKnj
    } else {
      local.mo00046MeetingConclusion.value = ''
    }

    if (newValue.contentsInfo?.kadaiKnj !== undefined) {
      local.mo00046RemainingAndNewChallenges.value = newValue.contentsInfo?.kadaiKnj
    } else {
      local.mo00046RemainingAndNewChallenges.value = ''
    }

    if (newValue.contentsInfo?.jikaiYmd !== undefined) {
      local.mo00020RemainingAndNewChallenges.value = newValue.contentsInfo?.jikaiYmd
    } else {
      local.mo00020RemainingAndNewChallenges.value = ''
    }

    if (newValue.contentsInfo?.sogoHenkoKnj !== undefined) {
      local.mo00046Comprehensive.value = newValue.contentsInfo?.sogoHenkoKnj
    } else {
      local.mo00046Comprehensive.value = ''
    }

    if (newValue.contentsInfo?.keikakuHenkoKnj !== undefined) {
      local.mo00046Implementation.value = newValue.contentsInfo?.keikakuHenkoKnj
    } else {
      local.mo00046Implementation.value = ''
    }

    if (newValue.contentsInfo?.kessekiTaioKnj !== undefined) {
      local.mo00046AbsenteeCorrespondence.value = newValue.contentsInfo?.kessekiTaioKnj
    } else {
      local.mo00046AbsenteeCorrespondence.value = ''
    }
    if (newValue.attendeesHeaderInfo !== undefined) {
      local.mo00039ResidentAndFamilyParticipationValue = Number(
        newValue.attendeesHeaderInfo.sankaKbn
      )
    } else {
      local.mo00039ResidentAndFamilyParticipationValue = 0
    }
    local.mo00039ResidentAndFamilyParticipation = props.modelValue.attendanceMode
    localOneway.mo00020HeldDate.disabled = local.or31869.isCopy
    localOneway.mo00045HeldLocation.disabled = local.or31869.isCopy
    localOneway.mo00009HeldLocation.disabled = local.or31869.isCopy
    localOneway.mo00045HeldTime.disabled = local.or31869.isCopy
    localOneway.mo00009HeldTime.disabled = local.or31869.isCopy
    localOneway.mo00038HeldNumberOfTimes.mo00045Oneway.disabled = local.or31869.isCopy
    localOneway.mo00009HeldNumberOfTimes.disabled = local.or31869.isCopy
    localOneway.mo00045Person.disabled = local.or31869.isCopy
    localOneway.mo00045Family.disabled = local.or31869.isCopy
    localOneway.mo00045Relationship.disabled = local.or31869.isCopy
    localOneway.mo00045Remarks.disabled = local.or31869.isCopy
    localOneway.mo00009ReasonAbsence.disabled = local.or31869.isCopy
    localOneway.mo00046ReasonAbsence.disabled = local.or31869.isCopy
    localOneway.mo00009ConsiderItem.disabled = local.or31869.isCopy
    localOneway.mo00046ConsiderItem.disabled = local.or31869.isCopy
    localOneway.mo00009ConsiderContents.disabled = local.or31869.isCopy
    localOneway.mo00046ConsiderContents.disabled = local.or31869.isCopy
    localOneway.mo00009Conclusion.disabled = local.or31869.isCopy
    localOneway.mo00046Conclusion.disabled = local.or31869.isCopy
    localOneway.mo00009RemainingIssues.disabled = local.or31869.isCopy
    localOneway.mo00046RemainingIssues.disabled = local.or31869.isCopy
    localOneway.mo00009MainTopicDiscussed.disabled = local.or31869.isCopy
    localOneway.mo00046MainTopicDiscussed.disabled = local.or31869.isCopy
    localOneway.mo00009ProvisionService.disabled = local.or31869.isCopy
    localOneway.mo00046ProvisionService.disabled = local.or31869.isCopy
    localOneway.mo00009ConsiderMatterContents.disabled = local.or31869.isCopy
    localOneway.mo00046ConsiderMatterContents.disabled = local.or31869.isCopy
    localOneway.mo00009MeetingConclusion.disabled = local.or31869.isCopy
    localOneway.mo00046MeetingConclusion.disabled = local.or31869.isCopy
    localOneway.mo00009RemainingAndNewChallenges.disabled = local.or31869.isCopy
    localOneway.mo00046RemainingAndNewChallenges.disabled = local.or31869.isCopy
    localOneway.mo00009Comprehensive.disabled = local.or31869.isCopy
    localOneway.mo00046Comprehensive.disabled = local.or31869.isCopy
    localOneway.mo00009Implementation.disabled = local.or31869.isCopy
    localOneway.mo00046Implementation.disabled = local.or31869.isCopy
    localOneway.mo00009AbsenteeCorrespondence.disabled = local.or31869.isCopy
    localOneway.mo00046AbsenteeCorrespondence.disabled = local.or31869.isCopy
    localOneway.mo00020NextEventDate.disabled = local.or31869.isCopy
    localOneway.mo00020RemainingAndNewChallenges.disabled = local.or31869.isCopy
    localOneway.mo00009CaseImport.disabled = local.or31869.isCopy
    localOneway.mo00009ContentsImport.disabled = local.or31869.isCopy
    localOneway.mo00009IssuesImport.disabled = local.or31869.isCopy
    localOneway.mo00611Attendees.disabled = local.or31869.isCopy
    // localOneway.mo00009Attendees.disabled = local.or31869.isCopy
    localOneway.mo00009LetterInputSupport.disabled = local.or31869.isCopy
    localOneway.mo01354MeetingAttendeesOneway.showDragIndicatorFlg = false
    // localOneway.mo01354MeetingAttendeesOneway.showDragIndicatorFlg = !local.or31869.isCopy
    localOneway.mo01354Oneway.showDragIndicatorFlg = false
    // localOneway.mo01354Oneway.showDragIndicatorFlg = !local.or31869.isCopy
    localOneway.mo01354MainManagerOneway.showDragIndicatorFlg = false
    // localOneway.mo01354MainManagerOneway.showDragIndicatorFlg = !local.or31869.isCopy
    localOneway.mo00611AddRow.disabled = local.or31869.isCopy
    localOneway.mo00611InsertRow.disabled = local.or31869.isCopy
    localOneway.mo01265DeleteRow.disabled = local.or31869.isCopy
  },
  { deep: true, immediate: true }
)

watch(
  () => props.modelValue,
  () => {
    local.or31869 = props.modelValue
  },
  { deep: true, immediate: true }
)

watch(
  () => local.mo00020HeldDate.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.heldInfo.kaigiYmd = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00045HeldLocation.value,
  (newValue) => {
    if (
      refValue.value &&
      !isOverLimitByCharWidth(newValue, Or31869Const.CHARACTER_LIMITS.HELD_LOCATION)
    ) {
      console.log(newValue)
      refValue.value.heldInfo.whereKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00045HeldTime.value,
  (newValue) => {
    if (
      refValue.value &&
      !isOverLimitByCharWidth(newValue, Or31869Const.CHARACTER_LIMITS.HELD_TIME)
    ) {
      console.log(newValue)
      refValue.value.heldInfo.timeHm = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00038HeldNumberOfTimes.mo00045.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.heldInfo.kaisuu = newValue
    }
  },
  { deep: true }
)
watch(
  () => local.mo00045Person.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.heldInfo.honninKnj = newValue
    }
  },
  { deep: true }
)
watch(
  () => local.mo00045Family.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.heldInfo.kazokuKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00045Relationship.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.heldInfo.zokugaraKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00045Remarks.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.heldInfo.bikoKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046ReasonAbsence.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.riyuKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046ConsiderItem.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.kentoKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046ConsiderContents.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.memoKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046Conclusion.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.ketsuronKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046RemainingIssues.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.kadaiKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00020NextEventDate.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.jikaiYmd = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046MainTopicDiscussed.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.memoKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046ProvisionService.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.kentoKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046ConsiderMatterContents.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.naiyoKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046MeetingConclusion.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.ketsuronKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046RemainingAndNewChallenges.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.kadaiKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00020RemainingAndNewChallenges.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.jikaiYmd = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046Comprehensive.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.sogoHenkoKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046Implementation.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.keikakuHenkoKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00046AbsenteeCorrespondence.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.contentsInfo.kessekiTaioKnj = newValue
    }
  },
  { deep: true }
)

watch(
  () => local.mo00045ResidentAndFamilyParticipation.value,
  (newValue) => {
    if (refValue.value) {
      refValue.value.attendeesHeaderInfo.sankaKnj = newValue
    }
  },
  { deep: true }
)
watch(or27354Type, () => {
  let content = ''
  or27354Type.value.forEach((item) => {
    content += item.naiyoKnj
  })
  switch (focusCurrent.value) {
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_ITEM: {
      local.mo00046ConsiderItem.value = content
      break
    }
    case Or31869Const.TEXTCURRENTEDIT.CONSIDER_CONTENTS: {
      local.mo00046ConsiderContents.value = content

      break
    }
    case Or31869Const.TEXTCURRENTEDIT.CONCLUSION: {
      local.mo00046Conclusion.value = content

      break
    }
    case Or31869Const.TEXTCURRENTEDIT.REMAINING_ISSUES: {
      local.mo00046RemainingIssues.value = content

      break
    }
    default: {
      local.mo00046ConsiderItem.value = content
    }
  }
  focusCurrent.value = 0
})

watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21814ResolvePromise !== undefined && newValue !== undefined) {
      or21814ResolvePromise(newValue)
    }
    return
  }
)

watch(
  () => or27374Type.value,
  () => {
    if (local.mo00046ProvisionService.value !== '') {
      local.mo00046ProvisionService.value = or27374Type.value.content1
    } else {
      local.mo00046ProvisionService.value += '\n' + or27374Type.value.content1
    }
  }
)

watch(
  () => or27358Type.value,
  () => {
    local.mo00046RemainingIssues.value += or27358Type.value.yesLetter
    local.mo00046ConsiderItem.value += or27358Type.value.yesLetter
  }
)

watch(
  () => [
    local.mo01354Affiliation1.values.items,
    local.mo01354Affiliation2.values.items,
    local.mo01354Affiliation3.values.items,
  ],
  (newValue) => {
    if (local.or31869.pattern === Or01444Const.PATTERN_ID_1) {
      local.or31869.attendeesListResult = []
      const affiliation1 = newValue[0]
      const affiliation2 = newValue[1]
      const affiliation3 = newValue[2]
      if (
        affiliation1.length === affiliation2.length &&
        affiliation2.length === affiliation3.length
      ) {
        for (let i = 0; i < affiliation1.length; i++) {
          const item = {
            shozoku1Knj:
              (affiliation1[i].affiliation as { value: string } | undefined)?.value ?? '',
            name1Knj: (affiliation1[i].name as { value: string } | undefined)?.value ?? '',
            shozoku2Knj:
              (affiliation2[i].affiliation as { value: string } | undefined)?.value ?? '',
            name2Knj: (affiliation2[i].name as { value: string } | undefined)?.value ?? '',
            shozoku3Knj:
              (affiliation3[i].affiliation as { value: string } | undefined)?.value ?? '',
            name3Knj: (affiliation3[i].name as { value: string } | undefined)?.value ?? '',
          } as AttendeesInfo
          local.or31869.attendeesListResult.push(item)
        }
      }
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => local.mo01354MeetingAttendees.values.items,
  (newValue) => {
    if (local.or31869.pattern === Or01444Const.PATTERN_ID_2) {
      local.or31869.attendeesListResult = []
      for (const item of newValue) {
        const attendee = {
          susseki: (item.meetingAttendees as { value: string } | undefined)?.value ?? '',
        } as AttendeesInfo
        local.or31869.attendeesListResult.push(attendee)
      }
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => [local.mo01354MainManager1.values.items, local.mo01354MainManager2.values.items],
  (newValue) => {
    if (local.or31869.pattern === Or01444Const.PATTERN_ID_3) {
      local.or31869.attendeesListResult = []
      const itemsMainManager1 = newValue[0]
      const itemsMainManager2 = newValue[1]
      if (itemsMainManager1.length === itemsMainManager2.length) {
        for (let i = 0; i < itemsMainManager1.length; i++) {
          const item = {
            shozokuKnj:
              (itemsMainManager1[i].affiliation as { value: string } | undefined)?.value ?? '',
            shokushuKnj:
              (itemsMainManager1[i].jobType as { value: string } | undefined)?.value ?? '',
            nameKnj: (itemsMainManager1[i].name as { value: string } | undefined)?.value ?? '',
            tantoKnj:
              (itemsMainManager1[i].mainManager as { value: string } | undefined)?.value ?? '',
            shozoku2Knj:
              (itemsMainManager2[i].affiliation as { value: string } | undefined)?.value ?? '',
            shokushu2Knj:
              (itemsMainManager2[i].jobType as { value: string } | undefined)?.value ?? '',
            name2Knj: (itemsMainManager2[i].name as { value: string } | undefined)?.value ?? '',
            tanto2Knj:
              (itemsMainManager2[i].mainManager as { value: string } | undefined)?.value ?? '',
          } as AttendeesInfo
          local.or31869.attendeesListResult.push(item)
        }
      }
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => local.or31869.attendeesListResult,
  (newValue) => {
    if (refValue.value) {
      if (firstTime.value) {
        firstTime.value = false
        setChildCpBinds(props.parentUniqueCpId, {
          [Or31869Const.CP_ID(0)]: {
            twoWayValue: {
              ...local.or31869,
              attendeesListResult: newValue,
            },
          },
        })
      } else {
        refValue.value.attendeesListResult = newValue
      }
    }
  },
  { deep: true }
)
</script>
<template>
  <c-v-row
    no-gutters
    class="mt-6 pl-0"
  >
    <c-v-col
      v-if="local.or31869.pattern !== Or01444Const.PATTERN_ID_3"
      cols="auto"
      class="mr-4"
    >
      <c-v-row
        no-gutters
        class="mb-1"
      >
        <base-at-label
          :value="t('label.held-date')"
          font-weight="normal"
          style="font-size: 14px"
        />
      </c-v-row>
      <c-v-row no-gutters>
        <base-mo00020
          v-model="local.mo00020HeldDate"
          :oneway-model-value="localOneway.mo00020HeldDate"
        />
      </c-v-row>
    </c-v-col>
    <c-v-col
      cols="auto"
      class="mr-2"
    >
      <c-v-row
        no-gutters
        class="mb-1"
      >
        <base-at-label
          :value="t('label.held-location')"
          font-weight="normal"
          style="font-size: 14px"
        />
      </c-v-row>
      <c-v-row no-gutters>
        <g-custom-or-x-0157
          v-model="local.mo00045HeldLocation"
          :oneway-model-value="localOneway.orX0157Oneway"
          @on-click-edit-btn="onClickHeldLocation"
        ></g-custom-or-x-0157>
      </c-v-row>
    </c-v-col>
    <c-v-col
      cols="auto"
      class="mr-2"
    >
      <c-v-row
        no-gutters
        class="mb-1"
      >
        <base-at-label
          :value="t('label.held-time')"
          font-weight="normal"
          style="font-size: 14px"
        />
      </c-v-row>
      <c-v-row no-gutters>
        <g-custom-or-x-0157
          v-model="local.mo00045HeldTime"
          :oneway-model-value="localOneway.orX0157OnewayHeldTime"
          @on-click-edit-btn="onClickDialogTime"
        ></g-custom-or-x-0157>
      </c-v-row>
      <!-- <base-mo01299
        :oneway-model-value="localOneway.mo01299HeldTime"
        style="height: 100%"
      >
        <template #content>
          <c-v-row
            no-gutters
            class="pt-1 pr-1 pl-1 pb-1"
            style="background-color: rgba(var(--v-theme-black-200), 0.4)"
          >
            <base-at-label
              :value="t('label.held-time')"
              font-weight="normal"
              style="font-size: 14px"
            />
          </c-v-row>
          <c-v-row
            no-gutters
            class="pt-1 pr-1 pl-1 pb-1"
          >
            <g-custom-or-26194
              v-model:start-time="startTime"
              v-model:end-time="endTime"
            />
          </c-v-row>
        </template>
      </base-mo01299> -->
    </c-v-col>
    <c-v-col
      cols="auto"
      class="mr-2"
    >
      <c-v-row
        no-gutters
        class="mb-1"
      >
        <base-at-label
          :value="t('label.held-number-of-times')"
          font-weight="normal"
          style="font-size: 14px"
        />
      </c-v-row>
      <c-v-row no-gutters>
        <!-- <base-mo00009
          :oneway-model-value="localOneway.mo00009HeldNumberOfTimes"
          @click.stop="onClickHeldNumberOfTimes()"
        >
        </base-mo00009> -->
        <base-mo00038
          v-model="local.mo00038HeldNumberOfTimes"
          :oneway-model-value="localOneway.mo00038HeldNumberOfTimes"
        />
      </c-v-row>
      <!-- <base-mo01299
        :oneway-model-value="localOneway.mo01299HeldNumberOfTimes"
        style="height: 100%"
      >
        <template #content>
          <c-v-row
            no-gutters
            class="pt-1 pr-1 pl-1 pb-1"
            style="background-color: rgba(var(--v-theme-black-200), 0.4)"
          >
            <base-at-label
              :value="t('label.held-number-of-times')"
              font-weight="normal"
              style="font-size: 14px"
            />
          </c-v-row>
          <c-v-row
            no-gutters
            class="pt-1 pr-1 pl-1 pb-1"
          >
            <base-mo00009
              :oneway-model-value="localOneway.mo00009HeldNumberOfTimes"
              @click.stop="onClickHeldNumberOfTimes()"
            >
            </base-mo00009>
            <base-mo00038
              v-model="local.mo00038HeldNumberOfTimes"
              :oneway-model-value="localOneway.mo00038HeldNumberOfTimes"
            />
          </c-v-row>
        </template>
      </base-mo01299> -->
    </c-v-col>
    <c-v-col
      v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_1"
      cols="auto"
      class="mr-2"
    >
      <c-v-row
        no-gutters
        class="mb-1"
      >
        <base-at-label
          :value="t('label.person')"
          font-weight="normal"
          style="font-size: 14px"
        />
      </c-v-row>
      <c-v-row no-gutters>
        <base-mo00045
          v-model="local.mo00045Person"
          :oneway-model-value="localOneway.mo00045Person"
        />
      </c-v-row>
      <!-- <base-mo01299
        :oneway-model-value="localOneway.mo01299Person"
        style="height: 100%"
      >
        <template #content>

        </template>
      </base-mo01299> -->
    </c-v-col>
    <c-v-col
      v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_1"
      cols="auto"
      class="mr-2"
    >
      <c-v-row
        no-gutters
        class="mb-1"
      >
        <base-at-label
          :value="t('label.family')"
          font-weight="normal"
          style="font-size: 14px"
        />
      </c-v-row>
      <c-v-row no-gutters>
        <base-mo00045
          v-model="local.mo00045Family"
          :oneway-model-value="localOneway.mo00045Family"
        />
      </c-v-row>
      <!-- <base-mo01299
        :oneway-model-value="localOneway.mo01299Family"
        style="height: 100%"
      >
        <template #content>

        </template>
      </base-mo01299> -->
    </c-v-col>
    <!-- <c-v-col
      v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_1"
      cols="auto"
    >
      <c-v-row
        no-gutters
        class="pt-1 pr-1 pl-1 pb-1"
      >
        <base-at-label
          :value="t('label.relationship')"
          font-weight="normal"
          style="font-size: 14px"
        />
      </c-v-row>
      <c-v-row
        no-gutters
        class="pt-1 pr-1 pl-1 pb-1"
      >
        <base-mo00045
          v-model="local.mo00045Relationship"
          :oneway-model-value="localOneway.mo00045Relationship"
        />
      </c-v-row>
    </c-v-col> -->
    <c-v-col
      v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_1"
      cols="auto"
      class="mr-2"
    >
      <c-v-row
        no-gutters
        class="mb-1"
      >
        <base-at-label
          :value="t('label.remarks')"
          font-weight="normal"
          style="font-size: 14px"
        />
      </c-v-row>
      <c-v-row no-gutters>
        <base-mo00045
          v-model="local.mo00045Remarks"
          :oneway-model-value="localOneway.mo00045Remarks"
        />
      </c-v-row>
      <!-- <base-mo01299
        :oneway-model-value="localOneway.mo01299Remarks"
        style="height: 100%"
      >
        <template #content>

        </template>
      </base-mo01299> -->
    </c-v-col>
  </c-v-row>
  <c-v-divider class="mt-4 mb-4" />
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    class="mt-0 mb-4 ml-0"
    style="width: 900px !important"
  >
    <c-v-col
      cols="auto"
      class="custom-lable width-86 custom-text"
    >
      <base-mo-01338 :oneway-model-value="localOneway.mo01338AttendeesOneWay"></base-mo-01338>
    </c-v-col>
    <!-- <c-v-col
        class="border-1px"
      cols="1"
    >
      <base-mo01298 :oneway-model-value="localOneway.mo01298Attendees" />
    </c-v-col> -->
    <c-v-col
      cols="auto"
      class="padding-0"
    >
      <c-v-row no-gutters>
        <c-v-col
          cols="auto"
          class="custom-lable border-bottom border-left height-44"
        >
          <base-mo-01338 :oneway-model-value="localOneway.mo01338Resident"></base-mo-01338>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col
          cols="auto"
          class="custom-lable border-left height-44"
        >
          <base-mo-01338
            :oneway-model-value="localOneway.mo01338ResidentAndFamilyParticipation"
          ></base-mo-01338>
        </c-v-col>
      </c-v-row>
    </c-v-col>
    <c-v-col
      cols="6"
      class="padding-0 background-field"
    >
      <c-v-row
        no-gutters
        class="border-field border-left height-44"
      >
        <c-v-col
          cols="auto"
          class="pl-2 pt-2"
        >
          <base-at-label
            :value="t('label.resident')"
            font-weight="normal"
            style="font-size: 14px"
          />
        </c-v-col>
        <c-v-col
          cols="auto"
          class="pl-3 pt-1"
        >
          <base-mo00039
            v-model="local.mo00039ResidentAndFamilyParticipationValue"
            :oneway-model-value="localOneway.mo00039ResidentAndFamilyParticipation"
          >
            <base-at-radio
              v-for="(item, index) in local.mo00039ResidentAndFamilyParticipation"
              :key="index"
              :name="'radio' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="border-field border-left border-bottom height-44"
      >
        <g-custom-or-x-0163
          v-model="local.mo00045ResidentAndFamilyParticipation"
          :oneway-model-value="localOneway.orX0163ResidentAndFamilyParticipation"
          @on-click-edit-btn="onClickResidentAndFamilyParticipation"
        ></g-custom-or-x-0163>
        <!-- <c-v-col
          v-if="local.or31869.attendeesHeaderInfo.kinship !== '0'"
          cols="auto"
          class="align-self-center align-items-center"
        >
          <base-mo00009
            :oneway-model-value="localOneway.mo00009ResidentAndFamilyParticipation"
            @click.stop="onClickResidentAndFamilyParticipation()"
          />
        </c-v-col>
        <c-v-col
          cols="auto"
          class="align-self-center align-items-center"
        >
          <base-mo00045
            v-model="local.mo00045ResidentAndFamilyParticipation"
            :oneway-model-value="localOneway.mo00045ResidentAndFamilyParticipation"
          />
        </c-v-col> -->
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <!-- <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="pt-2 border-1px"
  >
    <base-mo01298 :oneway-model-value="localOneway.mo01298Attendees" />
  </c-v-row>
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="border-1px"
  >
    <base-mo01299 :oneway-model-value="localOneway.mo01299ResidentAndFamilyParticipation">
      <template #content>
        <c-v-row no-gutters>
          <c-v-col
            cols="auto"
            class="pl-2 align-self-center align-items-center"
          >
            <base-at-label
              :value="t('label.resident')"
              font-weight="normal"
              style="font-size: 14px"
            />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="pl-3 align-self-center align-items-center"
          >
            <base-mo00039
              v-model="local.mo00039ResidentAndFamilyParticipationValue"
              :oneway-model-value="localOneway.mo00039ResidentAndFamilyParticipation"
            >
              <base-at-radio
                v-for="(item, index) in local.mo00039ResidentAndFamilyParticipation"
                :key="index"
                :name="'radio' + index"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
          <c-v-col cols="auto">
            <base-mo-01338
              :oneway-model-value="localOneway.mo01338ResidentAndFamilyParticipation"
            ></base-mo-01338>
          </c-v-col>
          <c-v-col
            v-if="local.or31869.attendeesHeaderInfo.kinship !== '0'"
            cols="auto"
            class="align-self-center align-items-center"
          >
            <base-mo00009
              :oneway-model-value="localOneway.mo00009ResidentAndFamilyParticipation"
              @click.stop="onClickResidentAndFamilyParticipation()"
            />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="align-self-center align-items-center"
          >
            <base-mo00045
              v-model="local.mo00045ResidentAndFamilyParticipation"
              :oneway-model-value="localOneway.mo00045ResidentAndFamilyParticipation"
            />
          </c-v-col>
        </c-v-row>
      </template>
    </base-mo01299>
  </c-v-row> -->
  <c-v-row
    no-gutters
    class="mt-0"
    :style="{ width: local.or31869.pattern !== Or01444Const.PATTERN_ID_3 ? '965px' : '1130px' }"
  >
    <c-v-col
      cols="auto"
      class="mr-2"
    >
      <base-mo00611
        :oneway-model-value="localOneway.mo00611AddRow"
        @click.stop="onClickAddRow()"
      />
    </c-v-col>
    <c-v-col
      cols="auto"
      class="mr-2"
    >
      <base-mo00611
        :oneway-model-value="localOneway.mo00611InsertRow"
        @click.stop="onClickInsertRow()"
      />
    </c-v-col>
    <base-mo01265
      :oneway-model-value="localOneway.mo01265DeleteRow"
      @click.stop="onClickDeleteRow()"
    />
    <c-v-spacer />
    <c-v-col
      v-if="local.or31869.pattern !== Or01444Const.PATTERN_ID_2"
      cols="auto"
    >
      <base-mo00611
        :oneway-model-value="localOneway.mo00611Attendees"
        @click.stop="onClickAttendees()"
      />
    </c-v-col>

    <!-- <base-mo-01338 :oneway-model-value="localOneway.mo01338Attendees" /> -->
    <!-- <base-mo00009
      :oneway-model-value="localOneway.mo00009Attendees"
      @click.stop="onClickAttendees()"
    /> -->
    <!-- <c-v-col
      cols="auto"
      class="ml-2"
    >
      <base-mo00611
        v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_2"
        :oneway-model-value="localOneway.mo00611LetterInputSupport"
        @click.stop="onClickLetterInputSupport()"
      />
    </c-v-col> -->
    <!-- <base-mo-01338
      v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_2"
      :oneway-model-value="localOneway.mo01338LetterInputSupport"
    />
    <base-mo00009
      v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_2"
      :oneway-model-value="localOneway.mo00009LetterInputSupport"
      @click.stop="onClickLetterInputSupport()"
    /> -->
  </c-v-row>
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_1"
    no-gutters
    class="pt-4"
  >
    <base-mo01354
      v-model="local.mo01354Affiliation1"
      :oneway-model-value="localOneway.mo01354Oneway"
      hide-default-footer
      class="list-wrapper"
      style="width: 322px"
      @click.stop="clickAffiliation1SelectRow"
    >
      <!-- 所属1 -->
      <template #[`item.affiliation`]="{ item }">
        <c-v-col class="d-flex align-center data-table-cell">
          <base-mo01274
            v-model="item.affiliation"
            :oneway-model-value="{ maxLength: '60' }"
            :disabled="local.or31869.isCopy"
          />
        </c-v-col>
      </template>
      <template #[`item.name`]="{ item }">
        <c-v-col class="d-flex align-center data-table-cell">
          <base-mo01274
            v-model="item.name"
            :oneway-model-value="{ maxLength: '32' }"
            :disabled="local.or31869.isCopy"
          />
        </c-v-col>
      </template>
      <template #bottom />
    </base-mo01354>
    <base-mo01354
      v-model="local.mo01354Affiliation2"
      :oneway-model-value="localOneway.mo01354Oneway"
      hide-default-footer
      class="list-wrapper"
      style="width: 322px"
      @click.stop="clickAffiliation2SelectRow"
    >
      <template #[`item.affiliation`]="{ item }">
        <c-v-col class="d-flex align-center data-table-cell">
          <base-mo01274
            v-model="item.affiliation"
            :oneway-model-value="{ maxLength: '60' }"
            :disabled="local.or31869.isCopy"
          />
        </c-v-col>
      </template>
      <template #[`item.name`]="{ item }">
        <c-v-col class="d-flex align-center data-table-cell">
          <base-mo01274
            v-model="item.name"
            :oneway-model-value="{ maxLength: '32' }"
            :disabled="local.or31869.isCopy"
          />
        </c-v-col>
      </template>
      <template #bottom />
    </base-mo01354>
    <base-mo01354
      v-model="local.mo01354Affiliation3"
      :oneway-model-value="localOneway.mo01354Oneway"
      hide-default-footer
      class="list-wrapper"
      style="width: 322px"
      @click.stop="clickAffiliation3SelectRow"
    >
      <!-- 所属1 -->
      <template #[`item.affiliation`]="{ item }">
        <c-v-col class="d-flex align-center data-table-cell">
          <base-mo01274
            v-model="item.affiliation"
            :oneway-model-value="{ maxLength: '60' }"
            :disabled="local.or31869.isCopy"
          />
        </c-v-col>
      </template>
      <template #[`item.name`]="{ item }">
        <c-v-col class="d-flex align-center data-table-cell">
          <base-mo01274
            v-model="item.name"
            :oneway-model-value="{ maxLength: '32' }"
            :disabled="local.or31869.isCopy"
          />
        </c-v-col>
      </template>
      <template #bottom />
    </base-mo01354>
  </c-v-row>
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_2"
    no-gutters
    class="mt-4"
  >
    <base-mo01354
      v-model="local.mo01354MeetingAttendees"
      :oneway-model-value="localOneway.mo01354MeetingAttendeesOneway"
      hide-default-footer
      class="custom-meeting-table"
      style="width: 976px"
      @click.stop="clickMeetingAttendeesSelectRow"
    >
      <!-- 所属1 -->
      <template #[`item.meetingAttendees`]="{ item }">
        <c-v-col class="d-flex align-center data-table-cell height-43">
          <g-custom-or-x-0185
            v-model="item.meetingAttendees"
            :oneway-model-value="localOneway.orX0185MeetingAttendeesOneway"
          >
            <template #menu>
              <div class="orx0185-footer-menu-item">定型文で入力</div>
              <div class="orx0185-footer-menu-item">ケース取込</div>
            </template>
          </g-custom-or-x-0185>
          <!-- <g-custom-or-x-0163
            v-model="item.meetingAttendees"
            :oneway-model-value="localOneway.orX0163MeetingAttendeesOnewayModelValue"
            @on-click-edit-btn="onClickReasonAbsence"
          ></g-custom-or-x-0163> -->
          <!-- <base-mo01274
            v-model="item.meetingAttendees"
            :oneway-model-value="{ maxLength: '114' }"
            :disabled="local.or31869.isCopy"
          /> -->
        </c-v-col>
      </template>
      <template #bottom />
    </base-mo01354>
  </c-v-row>
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="mt-4"
    style="width: 1130px"
  >
    <c-v-col cols="auto">
      <base-mo01354
        v-model="local.mo01354MainManager1"
        :oneway-model-value="localOneway.mo01354MainManagerOneway"
        hide-default-footer
        class="list-wrapper"
        style="width: 565px"
        @click.stop="clickMainManager1SelectRow"
      >
        <!-- 所属1 -->
        <template #[`item.affiliation`]="{ item }">
          <c-v-col class="d-flex align-center data-table-cell">
            <base-mo01274
              v-model="item.affiliation"
              :oneway-model-value="{ maxLength: '60' }"
              :disabled="local.or31869.isCopy"
            />
          </c-v-col>
        </template>
        <template #[`item.jobType`]="{ item }">
          <c-v-col class="d-flex align-center data-table-cell">
            <base-mo01274
              v-model="item.jobType"
              :oneway-model-value="{ maxLength: '30' }"
              :disabled="local.or31869.isCopy"
            />
          </c-v-col>
        </template>
        <template #[`item.name`]="{ item }">
          <c-v-col class="d-flex align-center data-table-cell">
            <base-mo01274
              v-model="item.name"
              :oneway-model-value="{ maxLength: '30' }"
              :disabled="local.or31869.isCopy"
            />
          </c-v-col>
        </template>
        <template #[`item.mainManager`]="{ item }">
          <c-v-col class="d-flex align-center data-table-cell">
            <base-mo01274
              v-model="item.mainManager"
              :oneway-model-value="{ maxLength: '30' }"
              :disabled="local.or31869.isCopy"
            />
          </c-v-col>
        </template>
        <template #bottom />
      </base-mo01354>
    </c-v-col>
    <c-v-col cols="auto">
      <base-mo01354
        v-model="local.mo01354MainManager2"
        :oneway-model-value="localOneway.mo01354MainManagerOneway"
        hide-default-footer
        class="list-wrapper"
        style="width: 565px"
        @click.stop="clickMainManager2SelectRow"
      >
        <!-- 所属1 -->
        <template #[`item.affiliation`]="{ item }">
          <c-v-col class="d-flex align-center data-table-cell">
            <base-mo01274
              v-model="item.affiliation"
              :oneway-model-value="{ maxLength: '60' }"
              :disabled="local.or31869.isCopy"
            />
          </c-v-col>
        </template>
        <template #[`item.jobType`]="{ item }">
          <c-v-col class="d-flex align-center data-table-cell">
            <base-mo01274
              v-model="item.jobType"
              :oneway-model-value="{ maxLength: '30' }"
              :disabled="local.or31869.isCopy"
            />
          </c-v-col>
        </template>
        <template #[`item.name`]="{ item }">
          <c-v-col class="d-flex align-center data-table-cell">
            <base-mo01274
              v-model="item.name"
              :oneway-model-value="{ maxLength: '30' }"
              :disabled="local.or31869.isCopy"
            />
          </c-v-col>
        </template>
        <template #[`item.mainManager`]="{ item }">
          <c-v-col class="d-flex align-center data-table-cell">
            <base-mo01274
              v-model="item.mainManager"
              :oneway-model-value="{ maxLength: '30' }"
              :disabled="local.or31869.isCopy"
            />
          </c-v-col>
        </template>
        <template #bottom />
      </base-mo01354>
    </c-v-col>
  </c-v-row>
  <c-v-divider class="mt-4 mb-4" />
  <c-v-row
    no-gutters
    class="mb-4 width-961"
    justify="end"
  >
    <!-- ケース取込 -->
    <base-mo00611
      :oneway-model-value="localOneway.mo00009CaseImport"
      class="mr-2"
      @click.stop="onClickCaseImport()"
    />
    <!-- <base-mo-01338 :oneway-model-value="localOneway.mo01338CaseImport" />
    <base-mo00009
      :oneway-model-value="localOneway.mo00009CaseImport"
      @click.stop="onClickCaseImport()"
    /> -->
    <!-- 内容取込 -->
    <base-mo00611
      v-if="local.or31869.pattern !== Or01444Const.PATTERN_ID_3"
      :oneway-model-value="localOneway.mo00009ContentsImport"
      class="mr-2"
      @click.stop="onClickContentsImport()"
    />
    <!-- <base-mo-01338
      v-if="local.or31869.pattern !== Or01444Const.PATTERN_ID_3"
      :oneway-model-value="localOneway.mo01338ContentsImport"
    />
    <base-mo00009
      v-if="local.or31869.pattern !== Or01444Const.PATTERN_ID_3"
      :oneway-model-value="localOneway.mo00009ContentsImport"
      @click.stop="onClickContentsImport()"
    /> -->
    <!-- 課題取込 -->
    <base-mo00611
      :oneway-model-value="localOneway.mo00009IssuesImport"
      @click.stop="onClickIssuesImport()"
    />
    <!-- <base-mo-01338 :oneway-model-value="localOneway.mo01338IssuesImport" />
    <base-mo00009
      :oneway-model-value="localOneway.mo00009IssuesImport"
      @click.stop="onClickIssuesImport()"
    /> -->
  </c-v-row>
  <!-- 欠席者及び欠席理由 -->
  <c-v-row
    v-if="
      local.or31869.pattern === Or01444Const.PATTERN_ID_1 &&
      local.or31869.contentsInfo?.reasonAbsenceDisplay !== '0'
    "
    no-gutters
    class="width-961"
  >
    <c-v-col
      cols="auto"
      class="custom-lable"
    >
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338ReasonAbsence"></base-mo-01338>
      </c-v-row>
    </c-v-col>
    <c-v-col>
      <g-custom-or-x-0163
        v-model="local.mo00046ReasonAbsence"
        :oneway-model-value="localOneway.orX0163ReasonAbsenceOnewayModelValue"
        @on-click-edit-btn="onClickReasonAbsence"
      ></g-custom-or-x-0163>
      <!-- <base-mo00009
        :oneway-model-value="localOneway.mo00009ReasonAbsence"
        @click.stop="onClickReasonAbsence()"
      />
      <base-mo00046
        v-model="local.mo00046ReasonAbsence"
        :oneway-model-value="localOneway.mo00046ReasonAbsence"
        @focus="onFocusReasonAbsence"
      /> -->
    </c-v-col>
  </c-v-row>
  <!-- 検討した項目 -->
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_1"
    no-gutters
    class="width-961"
  >
    <c-v-col
      v-if="local.considerItemZoom"
      cols="auto"
      class="custom-lable"
    >
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338ConsiderItem"></base-mo-01338>
        <!-- <base-mo00009
          :oneway-model-value="localOneway.mo00009ConsiderItem"
          @click.stop="onClickConsiderItem()"
        /> -->
      </c-v-row>
    </c-v-col>
    <c-v-col
      v-if="local.considerItemZoom"
      style="width: 100%"
    >
      <g-custom-or-x-0163
        v-model="local.mo00046ConsiderItem"
        :oneway-model-value="localOneway.orX0163ConsiderItemOnewayModelValue"
        @on-click-edit-btn="onClickConsiderItem"
      ></g-custom-or-x-0163>
    </c-v-col>
    <!-- <c-v-col v-if="!local.considerItemZoom">
      <c-v-row
        no-gutters
        class="d-flex justify-center"
      >
        <base-mo-01338 :oneway-model-value="localOneway.mo01338ConsiderItem"></base-mo-01338>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Expansion"></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Zoom"
          @click.stop="onClickConsiderItemZoom()"
        />
      </c-v-row>
    </c-v-col> -->
  </c-v-row>
  <!-- 検討内容 -->
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_1"
    no-gutters
    class="width-961"
  >
    <c-v-col
      v-if="local.considerContentsZoom"
      cols="auto"
      class="custom-lable"
    >
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338ConsiderContents"></base-mo-01338>
        <!-- <base-mo00009
          :oneway-model-value="localOneway.mo00009ConsiderContents"
          @click.stop="onClickConsiderContents()"
        /> -->
      </c-v-row>
      <!-- <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Zoom"></base-mo-01338>
        <c-v-spacer />
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Zoom"
          @click.stop="onClickConsiderContentsZoom()"
        />
      </c-v-row> -->
    </c-v-col>
    <c-v-col
      v-if="local.considerContentsZoom"
      style="width: 100%"
    >
      <!-- <base-mo00046
        v-model="local.mo00046ConsiderContents"
        :oneway-model-value="localOneway.mo00046ConsiderContents"
        @focus="onFocusConsiderContents"
      /> -->
      <g-custom-or-x-0163
        v-model="local.mo00046ConsiderContents"
        :oneway-model-value="localOneway.orX0163ConsiderContentsOnewayModelValue"
        @on-click-edit-btn="onClickConsiderContents"
      ></g-custom-or-x-0163>
    </c-v-col>
    <!-- <c-v-col v-if="!local.considerContentsZoom">
      <c-v-row
        no-gutters
        class="d-flex justify-center"
      >
        <base-mo-01338 :oneway-model-value="localOneway.mo01338ConsiderContents"></base-mo-01338>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Expansion"></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Zoom"
          @click.stop="onClickConsiderContentsZoom()"
        />
      </c-v-row>
    </c-v-col> -->
  </c-v-row>
  <!-- 結論 -->
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_1"
    no-gutters
    class="width-961"
  >
    <c-v-col
      v-if="local.conclusionZoom"
      cols="auto"
      class="custom-lable"
    >
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Conclusion"></base-mo-01338>
        <!-- <c-v-spacer />
        <div style="width: 1px; height: 30px; background-color: rgb(var(--v-theme-black-200))" />

        <base-mo00009
          :oneway-model-value="localOneway.mo00009Conclusion"
          @click.stop="onClickConclusion()"
        /> -->
      </c-v-row>
      <!-- <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Zoom"></base-mo-01338>
        <c-v-spacer />
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Zoom"
          @click.stop="onClickConclusionZoom()"
        />
      </c-v-row> -->
    </c-v-col>
    <c-v-col
      v-if="local.conclusionZoom"
      style="width: 100%"
    >
      <!-- <base-mo00046
        v-model="local.mo00046Conclusion"
        :oneway-model-value="localOneway.mo00046Conclusion"
        @focus="onFocusConclusion"
      /> -->
      <g-custom-or-x-0163
        v-model="local.mo00046Conclusion"
        :oneway-model-value="localOneway.orX0163ConclusionOnewayModelValue"
        @on-click-edit-btn="onClickConclusion"
      ></g-custom-or-x-0163>
    </c-v-col>
    <!-- <c-v-col v-if="!local.conclusionZoom">
      <c-v-row
        no-gutters
        class="d-flex justify-center"
      >
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Conclusion"></base-mo-01338>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Expansion"></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Zoom"
          @click.stop="onClickConclusionZoom()"
        />
      </c-v-row>
    </c-v-col> -->
  </c-v-row>
  <!-- 残された課題 -->
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_1"
    no-gutters
    class="width-961"
  >
    <c-v-col
      v-if="local.remainingIssuesZoom"
      cols="auto"
      class="custom-lable"
    >
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338RemainingIssues"></base-mo-01338>
        <!-- <c-v-spacer />
        <div style="width: 1px; height: 30px; background-color: rgb(var(--v-theme-black-200))" />

        <base-mo00009
          :oneway-model-value="localOneway.mo00009RemainingIssues"
          @click.stop="onClickRemainingIssues()"
        /> -->
      </c-v-row>
      <!-- <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Zoom"></base-mo-01338>
        <c-v-spacer />
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Zoom"
          @click.stop="onClickRemainingIssuesZoom()"
        />
      </c-v-row> -->
    </c-v-col>
    <c-v-col
      v-if="local.remainingIssuesZoom"
      style="width: 100%"
    >
      <!-- <base-mo00046
        v-model="local.mo00046RemainingIssues"
        :oneway-model-value="localOneway.mo00046RemainingIssues"
        @focus="onFocusRemainingIssues"
      /> -->
      <g-custom-or-x-0163
        v-model="local.mo00046RemainingIssues"
        :oneway-model-value="localOneway.orX0163RemainingIssuesOnewayModelValue"
        @on-click-edit-btn="onClickRemainingIssues"
      ></g-custom-or-x-0163>
    </c-v-col>
    <!-- <c-v-col v-if="!local.remainingIssuesZoom">
      <c-v-row
        no-gutters
        class="d-flex justify-center"
      >
        <base-mo-01338 :oneway-model-value="localOneway.mo01338RemainingIssues"></base-mo-01338>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Expansion"></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Zoom"
          @click.stop="onClickRemainingIssuesZoom()"
        />
      </c-v-row>
    </c-v-col> -->
  </c-v-row>
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_1"
    no-gutters
  >
    <!-- <base-mo00020
      v-model="local.mo00020NextEventDate"
      :oneway-model-value="localOneway.mo00020NextEventDate"
    /> -->
    <c-v-col
      cols="auto"
      class="mr-2 mt-4"
    >
      <c-v-row
        no-gutters
        class="mb-1"
      >
        <base-at-label
          :value="t('label.next-event-date')"
          font-weight="normal"
          style="font-size: 14px"
        />
      </c-v-row>
      <c-v-row no-gutters>
        <base-mo00020
          v-model="local.mo00020NextEventDate"
          :oneway-model-value="localOneway.mo00020NextEventDate"
        />
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_2"
    no-gutters
    class="width-961"
  >
    <c-v-col
      v-if="local.mainTopicDiscussedZoom"
      cols="auto"
      class="custom-lable"
    >
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338MainTopicDiscussed"></base-mo-01338>
        <!-- <c-v-spacer />
        <div style="width: 1px; height: 30px; background-color: rgb(var(--v-theme-black-200))" />

        <base-mo00009
          :oneway-model-value="localOneway.mo00009MainTopicDiscussed"
          @click.stop="onClickMainTopicDiscussed()"
        /> -->
      </c-v-row>
      <!-- <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Zoom"></base-mo-01338>
        <c-v-spacer />
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Zoom"
          @click.stop="onClickMainTopicDiscussedZoom()"
        />
      </c-v-row> -->
    </c-v-col>
    <c-v-col
      v-if="local.mainTopicDiscussedZoom"
      style="width: 100%"
    >
      <g-custom-or-x-0163
        v-model="local.mo00046MainTopicDiscussed"
        :oneway-model-value="localOneway.orX0163MainTopicDiscussedOnewayModelValue"
        @on-click-edit-btn="onClickMainTopicDiscussed"
      ></g-custom-or-x-0163>
      <!-- <base-mo00046
        v-model="local.mo00046MainTopicDiscussed"
        :oneway-model-value="localOneway.mo00046MainTopicDiscussed"
      /> -->
    </c-v-col>
    <!-- <c-v-col v-if="!local.mainTopicDiscussedZoom">
      <c-v-row
        no-gutters
        class="d-flex justify-center"
      >
        <base-mo-01338 :oneway-model-value="localOneway.mo01338MainTopicDiscussed"></base-mo-01338>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Expansion"></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Zoom"
          @click.stop="onClickMainTopicDiscussedZoom()"
        />
      </c-v-row>
    </c-v-col> -->
  </c-v-row>
  <!-- 実施計画に基づくサービス提供その他検討した事項 -->
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="width-961"
  >
    <c-v-col
      cols="auto"
      class="custom-lable"
    >
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338ProvisionService"></base-mo-01338>
      </c-v-row>
    </c-v-col>
    <!-- <c-v-col>
      <c-v-row no-gutters class="pt-2 border-1px">
        <base-mo-01338 :oneway-model-value="localOneway.mo01338ProvisionService"></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009ProvisionService"
          @click.stop="onClickProvisionService()"
        />
      </c-v-row>
      <base-mo00046
        v-model="local.mo00046ProvisionService"
        :oneway-model-value="localOneway.mo00046ProvisionService"
      />
    </c-v-col>
    <div style="width: 8px" /> -->
    <c-v-col style="width: 100%">
      <!-- <base-mo00046
        v-model="local.mo00046RemainingIssues"
        :oneway-model-value="localOneway.mo00046RemainingIssues"
        @focus="onFocusRemainingIssues"
      /> -->
      <g-custom-or-x-0163
        v-model="local.mo00046ProvisionService"
        :oneway-model-value="localOneway.orX0163rovisionService"
        @on-click-edit-btn="onClickProvisionService"
      ></g-custom-or-x-0163>
    </c-v-col>
    <!-- <c-v-col>
      <c-v-row no-gutters>
        <base-mo-01338
          :oneway-model-value="localOneway.mo01338ConsiderMatterContents"
        ></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009ConsiderMatterContents"
          @click.stop="onClickConsiderMatterContents()"
        />
      </c-v-row>
      <base-mo00046
        v-model="local.mo00046ConsiderMatterContents"
        :oneway-model-value="localOneway.mo00046ConsiderMatterContents"
      />
    </c-v-col> -->
  </c-v-row>
  <!-- 検討事項の内容 -->
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="width-961"
  >
    <c-v-col
      v-if="local.remainingIssuesZoom"
      cols="auto"
      class="custom-lable"
    >
      <c-v-row no-gutters>
        <base-mo-01338
          :oneway-model-value="localOneway.mo01338ConsiderMatterContents"
        ></base-mo-01338>
      </c-v-row>
    </c-v-col>
    <c-v-col
      v-if="local.remainingIssuesZoom"
      style="width: 100%"
    >
      <g-custom-or-x-0163
        v-model="local.mo00046ConsiderMatterContents"
        :oneway-model-value="localOneway.orX0163ConsiderMatterContents"
        @on-click-edit-btn="onClickConsiderMatterContents"
      ></g-custom-or-x-0163>
    </c-v-col>
  </c-v-row>
  <!-- 会議の結論 -->
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="width-961"
  >
    <c-v-col
      cols="auto"
      class="custom-lable"
    >
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338MeetingConclusion"></base-mo-01338>
      </c-v-row>
    </c-v-col>
    <c-v-col style="width: 100%">
      <g-custom-or-x-0163
        v-model="local.mo00046MeetingConclusion"
        :oneway-model-value="localOneway.orX0163MeetingConclusion"
        @on-click-edit-btn="onClickMeetingConclusion"
      ></g-custom-or-x-0163>
    </c-v-col>
  </c-v-row>
  <!-- 残された課題や新たな課題 -->
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="width-961"
  >
    <c-v-col
      cols="auto"
      class="custom-lable"
    >
      <c-v-row no-gutters>
        <base-mo-01338
          :oneway-model-value="localOneway.mo01338RemainingAndNewChallenges"
        ></base-mo-01338>
      </c-v-row>
    </c-v-col>
    <c-v-col style="width: 100%">
      <g-custom-or-x-0163
        v-model="local.mo00046RemainingAndNewChallenges"
        :oneway-model-value="localOneway.orX0163RemainingAndNewChallenges"
        @on-click-edit-btn="onClickRemainingAndNewChallenges"
      ></g-custom-or-x-0163>
    </c-v-col>
  </c-v-row>
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="mt-4"
  >
    <!-- <base-mo00020
      v-model="local.mo00020NextEventDate"
      :oneway-model-value="localOneway.mo00020NextEventDate"
    /> -->
    <c-v-col
      cols="auto"
      class="mr-4"
    >
      <c-v-row
        no-gutters
        class="mb-1"
      >
        <base-at-label
          :value="t('label.next-event-date')"
          font-weight="normal"
          style="font-size: 14px"
        />
      </c-v-row>
      <c-v-row no-gutters>
        <base-mo00020
          v-model="local.mo00020NextEventDate"
          :oneway-model-value="localOneway.mo00020NextEventDate"
        />
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <!-- <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="pt-2"
  >
    <c-v-col>
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338MeetingConclusion"></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009MeetingConclusion"
          @click.stop="onClickMeetingConclusion()"
        />
      </c-v-row>
      <base-mo00046
        v-model="local.mo00046MeetingConclusion"
        :oneway-model-value="localOneway.mo00046MeetingConclusion"
      />
    </c-v-col>
    <div style="width: 8px" />
    <c-v-col>
      <c-v-row no-gutters>
        <base-mo-01338
          :oneway-model-value="localOneway.mo01338RemainingAndNewChallenges"
        ></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009RemainingAndNewChallenges"
          @click.stop="onClickRemainingAndNewChallenges()"
        />
      </c-v-row>
      <c-v-row no-gutters>
        <base-mo00046
          v-model="local.mo00046RemainingAndNewChallenges"
          :oneway-model-value="localOneway.mo00046RemainingAndNewChallenges"
          style="width: 100%"
        />
      </c-v-row>
      <c-v-row
        no-gutters
        class="pt-1"
      >
        <base-mo00020
          v-model="local.mo00020RemainingAndNewChallenges"
          :oneway-model-value="localOneway.mo00020RemainingAndNewChallenges"
        />
      </c-v-row>
    </c-v-col>
  </c-v-row> -->
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    class="mt-4 mb-2 ml-0 width-961"
  >
    <c-v-col
      cols="auto"
      class="custom-lable"
      style="width: 109px; height: 305px"
    >
      <base-mo-01338 :oneway-model-value="localOneway.mo01338Immediate"></base-mo-01338>
    </c-v-col>
    <c-v-col
      cols="auto"
      class="padding-0 height-234"
    >
      <c-v-row no-gutters>
        <c-v-col
          cols="auto"
          class="custom-text-filed border-bottom border-left"
          style="width: 147px; height: 101px"
        >
          <base-mo-01338 :oneway-model-value="localOneway.mo01338Comprehensive"></base-mo-01338>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col
          cols="auto"
          class="custom-text-filed border-left"
          style="width: 147px; height: 101px; margin-top: 1px"
        >
          <base-mo-01338 :oneway-model-value="localOneway.mo01338Implementation"></base-mo-01338>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col
          cols="auto"
          class="custom-text-filed border-left"
          style="width: 147px; height: 101px; margin-top: 1px"
        >
          <base-mo-01338
            :oneway-model-value="localOneway.mo01338AbsenteeCorrespondence"
          ></base-mo-01338>
        </c-v-col>
      </c-v-row>
    </c-v-col>
    <c-v-col
      cols="auto"
      class="padding-0 background-field"
      style="width: 689px"
    >
      <c-v-row no-gutters>
        <c-v-col style="width: 100%">
          <g-custom-or-x-0163
            v-model="local.mo00046Comprehensive"
            :oneway-model-value="localOneway.orX0163Comprehensive"
            @on-click-edit-btn="onClickComprehensive"
          ></g-custom-or-x-0163>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col style="width: 100%">
          <g-custom-or-x-0163
            v-model="local.mo00046Implementation"
            :oneway-model-value="localOneway.orX0163Implementation"
            @on-click-edit-btn="onClickImplementation"
          ></g-custom-or-x-0163>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col style="width: 100%">
          <g-custom-or-x-0163
            v-model="local.mo00046AbsenteeCorrespondence"
            :oneway-model-value="localOneway.orX0163AbsenteeCorrespondence"
            @on-click-edit-btn="onClickAbsenteeCorrespondence"
          ></g-custom-or-x-0163>
        </c-v-col>
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <!-- <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="pt-2 border-1px"
  >
    <base-mo01298 :oneway-model-value="localOneway.mo01298" />
  </c-v-row>
  <c-v-row
    v-if="local.or31869.pattern === Or01444Const.PATTERN_ID_3"
    no-gutters
    class="pt-2"
  >
    <c-v-col>
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Comprehensive"></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Comprehensive"
          @click.stop="onClickComprehensive()"
        />
      </c-v-row>
      <base-mo00046
        v-model="local.mo00046Comprehensive"
        :oneway-model-value="localOneway.mo00046Comprehensive"
      />
    </c-v-col>
    <div style="width: 8px" />
    <c-v-col>
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Implementation"></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009Implementation"
          @click.stop="onClickImplementation()"
        />
      </c-v-row>
      <base-mo00046
        v-model="local.mo00046Implementation"
        :oneway-model-value="localOneway.mo00046Implementation"
      />
    </c-v-col>
    <div style="width: 8px" />
    <c-v-col>
      <c-v-row no-gutters>
        <base-mo-01338
          :oneway-model-value="localOneway.mo01338AbsenteeCorrespondence"
        ></base-mo-01338>
        <base-mo00009
          :oneway-model-value="localOneway.mo00009AbsenteeCorrespondence"
          @click.stop="onClickAbsenteeCorrespondence()"
        />
      </c-v-row>
      <base-mo00046
        v-model="local.mo00046AbsenteeCorrespondence"
        :oneway-model-value="localOneway.mo00046AbsenteeCorrespondence"
      />
    </c-v-col>
  </c-v-row> -->
  <g-custom-gui-00064
    v-if="showDialog"
    v-bind="gui00064"
    :unique-cp-id="gui00064.uniqueCpId"
  />
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="memoData.or51775Oneway"
    :unique-cp-id="or51775.uniqueCpId"
    @confirm="handleConfirm"
  />
  <g-custom-or-27354
    v-if="showDialogOr27354"
    v-bind="or27354"
    v-model="or27354Type"
    :oneway-model-value="or27354Data"
    :unique-cp-id="or27354.uniqueCpId"
  />
  <g-custom-or-22951
    v-if="showDialogOr22951"
    v-bind="or22951"
    :oneway-model-value="or22951OnewayValue"
    :unique-cp-id="or22951.uniqueCpId"
    @update:model-value="onClickCaseImportSubmit"
  />
  <g-custom-or-28256
    v-if="showDialogOr28256"
    v-bind="or28256"
    v-model="or28256Type"
    :unique-cp-id="or28256.uniqueCpId"
    :oneway-model-value="or28256Oneway"
  />
  <!-- :model-value="or22951Type" -->
  <g-custom-or-27094
    v-if="isShowDialogOr27094"
    v-bind="Or27094"
    v-model="or27094Type"
    :oneway-model-value="Or27094OnewayModel"
  />
  <g-base-or21814
    v-bind="or21814"
    :unique-cp-id="or21814.uniqueCpId"
  />
  <g-custom-or-27374
    v-if="showDialogOr27374"
    v-bind="or27374"
    v-model="or27374Type"
    :oneway-model-value="Or27374OneWayValue"
  />
  <g-custom-or-27358
    v-if="showDialogOr27358"
    v-bind="or27358"
    v-model="or27358Type"
    :oneway-model-value="Or27358Data"
  />
  <g-custom-or-27553
    v-if="showDialogOr27553"
    v-bind="Or27553"
    v-model="refValueOr27553"
    :oneway-model-value="Or27553OneWayValue"
  />
  <g-custom-or-55476
    v-if="showDialogOr55476"
    v-bind="or55476"
    :oneway-model-value="or55476OneWayType"
    @update:model-value="update"
  />
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';
:deep(.section-header) {
  min-width: 200px !important;
  max-width: 200px !important;
}
:deep(.v-data-table tr th),
:deep(.v-data-table tr td) {
  height: 32px !important;
}

:deep(.v-data-table tr td div) {
  padding: 0px !important;
}

:deep(.list-wrapper .v-table__wrapper tr th) {
  background-color: #dbeefe !important;
  font: inherit;
  font-size: 16px;
  padding: 0px 0px 0px 6px !important;
}

:deep(.custom-meeting-table .v-table__wrapper tr td) {
  padding-left: 10px;
}
:deep(.custom-meeting-table .v-table__wrapper tr th) {
  background-color: #dbeefe !important;
}

.custom-lable {
  width: 160px;
  align-items: center;
  justify-content: center;
  display: flex;
  background: #dbeefe;
  border: solid 1px rgb(var(--v-theme-light));
}

.custom-text {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88px;
  line-height: 59px;
  text-align: center;
}

.custom-text-filed {
  align-items: center;
  align-content: center;
  background: #dbeefe;
  border: solid 1px rgb(var(--v-theme-light));
}

.height-auto {
  height: auto !important;
}

.width-115 {
  width: 115px !important;
}

.width-86 {
  width: 86px !important;
}

.width-995 {
  width: 995px !important;
}

.width-961 {
  width: 961px !important;
}

.width-810 {
  width: 810px !important;
}

.width-543 {
  width: 543px !important;
}

.height-80 {
  height: 80px !important;
}

.height-43 {
  height: 43px !important;
}

.height-44 {
  height: 44px !important;
}

.height-234 {
  height: 234px !important;
}

.height-78 {
  height: 78px !important;
}

.height-540 {
  height: 540px !important;
}

.padding-0 {
  padding: 0px !important;
}

.border-top {
  border-top: none !important;
}

.border-bottom {
  border-bottom: none !important;
}

.border-left {
  border-left: none !important;
}

.border-right {
  border-right: none !important;
}

.border-field {
  border: solid 1px rgb(var(--v-theme-light));
}

.background-field {
  background-color: white;
}

.display-flex {
  display: flex;
}

:deep(.text-area-edit textarea) {
  align-content: center !important;
}

.align-content-center {
  align-content: center;
}
</style>
