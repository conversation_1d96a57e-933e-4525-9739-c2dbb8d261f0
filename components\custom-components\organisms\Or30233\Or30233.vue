<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import type {Ref} from 'vue';
import { onMounted, reactive , watch} from 'vue'
import { cloneDeep } from 'lodash'
import type { Or30233Type } from './Or30233.type'
import { Or30233Const } from './Or30233.constants'
import { CustomClass } from '~/types/CustomClassType'
import {
  useScreenStore,
  useScreenTwoWayBind,
} from '#imports'
/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  modelValue: Or30233Type
}

const props = defineProps<Props>()

const { t } = useI18n()
const local = reactive({
  sectionList: props.modelValue.careCertificationList.sectionList,
})

const localOneway = reactive({
  title: t('label.assessment-home-6-6-level-of-care-required-certification-item'),
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or30233Type>({
  cpId: Or30233Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or30233Type> }
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  console.log('local', local)
})

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    refValue.value = cloneDeep(newValue)

    // RefValueを更新する
    useScreenStore().setCpTwoWay({
      cpId: Or30233Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: refValue.value,
      isInit: true,
    })
  },
  { deep: true }
)
</script>

<template>
  <c-v-sheet class="container">
    <!-- 上段 -->
    <c-v-row
      no-gutters
      class="box-outline"
    >
      <!-- 左段 -->
      <!-- 要介護認定項目セクション -->
      <c-v-col
        cols="auto"
        class="row-outline d-flex align-center justify-center bg-custom-blue"
        style="width: 48px"
      >
        <div class="vertical-text">{{ localOneway.title }}</div></c-v-col
      >
      <!-- 中段 -->
      <c-v-col cols="auto">
        <!-- 上段 -->
        <c-v-row
          v-for="sub in refValue.careCertificationList.sectionList"
          :key="sub.value"
          no-gutters
          class="dataList"
        >
          <!-- 処置内容サブセクション -->
          <c-v-col
            cols="auto"
            class="vertical-text bg-custom-blue"
            style="width: 48px"
          >
            {{ sub.sectionTitle }}
          </c-v-col>

          <c-v-col
            cols="auto"
            class="bg-white"
            style="width: 265px"
          >
            <c-v-row
              v-for="item in sub.sectionItems"
              :key="item.itemName"
              no-gutters
              style="width: 265px"
              class="itemRow"
            >
              <c-v-col class="d-flex align-center">
                <!-- チェックボックス -->
                <base-mo00018
                  v-model="item.check"
                  :oneway-model-value="{
                    showItemLabel: false,
                    checkboxLabel: item.label,
                    isVerticalLabel: false,
                    customClass: new CustomClass({
                      outerClass: 'mr-2 or30233-label',
                      labelClass: 'ma-1 ',
                    }),
                    outerDivClass: 'or30233-label',
                  }"
                  @click.stop
                >
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="item.label"
                  />
                </base-mo00018>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
</template>

<style scoped lang="scss">
@use '@/styles/base-data-table-list.scss';
.container {
  .dataList {
    border: 1px rgb(var(--v-theme-black-200)) solid !important;

    .v-row {
      border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
      border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
      min-height: 48px;
      padding-left: 16px;
    }

    .v-row:last-child {
      border-bottom: unset !important;
      .v-col {
        // border-bottom: unset !important;
      }
    }
    &:last-child {
      border-top: unset !important;
    }
  }
  .v-row {
    min-height: 36px;
    line-height: 36px;
  }
  .v-col {
    line-height: 36px;
  }

  .vertical-text {
    writing-mode: vertical-rl;
    text-orientation: upright;
    // font-weight: bold;
    font-size: 13px;
    text-align: center;
    line-height: 48px;
    letter-spacing: 0.4rem;
  }
  .word-break {
    // word-break: normal;
    // white-space: pre-wrap !important;
  }
  :deep(.v-selection-control .v-label) {
    font-size: 13px;
    line-height: 24px !important;
    word-break: keep-all !important;
    white-space: pre !important;
    letter-spacing: -0.04rem;
  }
  :deep(.v-checkbox .v-checkbox-btn) {
    min-height: 36px;
    display: flex;
    align-items: inherit;
    height: unset !important;
    align-items: center;
  }
  :deep(.v-selection-control__wrapper) {
    width: 24px !important;
    height: 24px !important;
  }
}
.row-outline {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-right: none !important;
}
.bg-custom-blue {
  background: rgb(var(--v-theme-blue-100));
}
.or30233-label {
  :deep(v-label) {
    word-break: keep-all !important;
    white-space: pre !important;
  }
}
</style>
