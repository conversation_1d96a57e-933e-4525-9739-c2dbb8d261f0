<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  // useCommonProps,
} from '#imports'
import { Or26714Const } from '~/components/custom-components/organisms/Or26714/Or26714.constants'
import { Or26714Logic } from '~/components/custom-components/organisms/Or26714/Or26714.logic'
import type { Or26714OnewayType } from '~/components/custom-components/organisms/Or26714/Or26714.type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00983'
// ルーティング
const routing = 'GUI00983/pinia'
// 画面物理名
const screenName = 'GUI00983'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or26714 = ref({ uniqueCpId: '' })

const or26714ModelValue = ref(null)

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00983' },
})

/**************************************************
 * Props
 **************************************************/
// const props = defineProps(useCommonProps())

// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  // Or26714Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or26714.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI00983',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26714Const.CP_ID(0) }],
})
Or26714Logic.initialize(init.childCpIds.Or26714.uniqueCpId)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or26714Const.CP_ID(0)]: or26714.value,
})

// ダイアログ表示フラグ
const showDialogOr26714 = computed(() => {
  // Or27610のダイアログ開閉状態
  return Or26714Logic.state.get(or26714.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
const Or26714Data = {
  newMode: '0',
  week1Id: 'week1_001',
  termid: 'term_001',
  weekDay: '1',
  startTime: '14:00',
  endTime: '21:00',
  weeklyScheduleInputListSelect: [
    {
      week2Id: 'week2_001',
      week1Id: 'week1_001',
      userid: 'user_001',
      youbi: '00000001',
      startHour: '09:30',
      endHour: '09:32',
      naiyoCd: '1',
      naiyoKnj: '訪問介護  1',
      memoKnj: 'サンプルメモ',
      fontSize: '12',
      dispMode: '1',
      alignment: '0',
      svShuruiCd: 'sv_001',
      svItemCd: 'item_001',
      svJigyoId: 'jigyo_001',
      fontColor: '#000000',
      backColor: '#FFFFFF',
      timeKbn: '0',
      igaiKbn: '0',
      igaiDate: '',
      igaiWeek: '',
      svTani: '1000',
      fygId: 'fyg_001',
      wakugaiFlg: '0',
    },
    {
      week2Id: 'week2_002',
      week1Id: 'week1_002',
      userid: 'user_002',
      youbi: '11000000',
      startHour: '10:00',
      endHour: '11:00',
      naiyoCd: '2',
      naiyoKnj: '訪問介護',
      memoKnj: 'テストメモ',
      fontSize: '15',
      dispMode: '2',
      alignment: '0',
      svShuruiCd: 'sv_002',
      svItemCd: 'item_002',
      svJigyoId: 'jigyo_002',
      fontColor: '#333333',
      backColor: '#F0F0F0',
      timeKbn: '0',
      igaiKbn: '1',
      igaiDate: '20240401',
      igaiWeek: '1',
      svTani: '2000',
      fygId: 'fyg_002',
      wakugaiFlg: '1',
    },
  ],
  weeklyContentInforListSelect: [
    {
      naiyoCd: '1',
      naiyoKnj: '訪問介護',
    },
    {
      naiyoCd: '2',
      naiyoKnj: '通所介護',
    },
  ],
  serviceTypeListSelect: [
    {
      ruakuKnj: '訪問介護',
    },
    {
      ruakuKnj: '通所介護',
    },
  ],
  servicepProviderNameListSelect: [
    {
      svJigyoCd: 'jigyo_001',
      jigyoKnj: 'サンプル事業所',
      jigyoRyakuKnj: 'サンプル事業所',
    },
    {
      svJigyoCd: 'jigyo_002',
      jigyoKnj: 'テスト事業所',
      jigyoRyakuKnj: 'テスト事業所',
    },
  ],
  serviceFeeOfficialNameListSelect: [
    {
      formalnameKnj: '身体介護中心型',
    },
    {
      formalnameKnj: '生活援助中心型',
    },
  ],
  additionalInfoPointsListSelect: [
    {
      id: 'add_001',
      week1Id: 'week1_001',
      week2Id: 'week2_001',
      svJigyoId: 'jigyo_001',
      svItemCd: 'item_001',
      kaisuu: '5',
    },
  ],
  additionalInforServiceNameListSelect: [
    {
      formalnameKnj: '加算訪問介護',
      svtype: '1',
    },
    {
      formalnameKnj: '加算理学療法',
      svtype: '2',
    },
  ],
} as Or26714OnewayType

const onClickOr26714 = (mode: string, youbi?: string, weekDay?: string) => {
  Or26714Data.newMode = mode
  if (Or26714Data.weeklyScheduleInputListSelect?.[0]) {
    Or26714Data.weeklyScheduleInputListSelect[0].youbi = youbi ?? ''
  }
  Or26714Data.weekDay = weekDay ?? '1'

  Or26714Logic.state.set({
    uniqueCpId: or26714.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>

<template>
  <c-v-row no-gutters>
    <c-v-col> ケアマネモックアップ開発ダイヤログ画面確認用ページ </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
      >
        ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>

  <!-- GUI00983_週間表入力 THS 2025/04/02 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26714('0', '00000001', '0')"
      >
        1-1. 引継情報.週間表入力画面構造体.モード=False (通常モード),
        引継情報.週間表入力画面構造体.週単位フラグ =（週単位以外）の場合
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26714('0', '11000000', '1')"
      >
        1-2. 引継情報.週間表入力画面構造体.モード=False (通常モード),
        引継情報.週間表入力画面構造体.週単位フラグ =（週単位）の場合
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26714('1', '00000001', '0')"
      >
        2-1. 引継情報.週間表入力画面構造体.モード= True (ﾊﾟﾀｰﾝモード),
        引継情報.週間表入力画面構造体.週単位フラグ = （週単位以外）の場合
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26714('1', '10000000', '1')"
      >
        2-2. 引継情報.週間表入力画面構造体.モード=True (通常モード),
        引継情報.週間表入力画面構造体.週単位フラグ =（週単位）の場合
      </v-btn>
    </c-v-col>
  </c-v-row>
  <p>{{ or26714ModelValue }}</p>

  <g-custom-or-26714
    v-if="showDialogOr26714"
    v-bind="or26714"
    v-model="or26714ModelValue"
    :oneway-model-value="Or26714Data"
  />
  <!--GUI00983_週間表入力 THS 2025/04/02 ADD END-->
</template>

<style scoped></style>
