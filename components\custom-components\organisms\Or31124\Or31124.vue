<script setup lang="ts">
/**
 * Or31124：有機体：アセスメント(インターライ)画面C
 * GUI00766_アセスメント(インターライ)画面C
 *
 * @description
 * アセスメント(インターライ)画面C
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or31124Const } from '../Or31124/Or31124.constants'
import { TeX0003Const } from '../../template/Tex0003/TeX0003.constants'
import type { Or31124StateType, SubInfo } from './Or31124.type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { useSetupChildProps, useScreenOneWayBind, useCmnCom } from '#imports'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { ResBodyStatusCode } from '~/constants/api-constants'
import type {
  IAssessmentInterRAICInitInEntity,
  IAssessmentInterRAICInitOutEntity,
  IAssessmentInterRAICUpdateInEntity,
  IAssessmentInterRAICUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAICInitEntity'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { TransmitParam } from '~/types/cmn/business/components/TeX0003Type'
import type {
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
  SubInfoBEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import type { Or30981OnewayType, Or30981Type } from '~/types/cmn/business/components/Or30981Type'
import { Or30981Const } from '~/components/custom-components/organisms/Or30981/Or30981.constants'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import { UPDATE_KBN } from '~/constants/classification-constants'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}
const props: Props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/

// 子コンポーネント用変数
const or51775 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or30981_1 = ref({ uniqueCpId: '' })
const or30981_2 = ref({ uniqueCpId: '' })
const or30981_3 = ref({ uniqueCpId: '' })
const or30981_4 = ref({ uniqueCpId: '' })
const or30981_5 = ref({ uniqueCpId: '' })

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()

// 親情報
const commonInfo: TransmitParam = reactive({
  executeFlag: '',
  deleteBtnValue: '',
  kikanKanriFlg: '',
  kijunbiYmd: '',
  sakuseiId: '',
  historyInfo: {} as HistoryInfoEntity,
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  raiId: '',
  assType: '',
  yokaiKbn: '',
  subInfoB: {} as SubInfoBEntity,
  syubetsuId: '',
  svJigyoKnj: '',
  updateKbn: '',
  historyUpdateKbn: '',
  tableCreateKbn: '',
  svJigyoId: '',
  userId: '',
})

const defaultOneway = reactive({
  // サブ情報（C）初期値
  subInfo: {
    raiId: '',
    c1: '',
    c2A: '',
    c2B: '',
    c2C: '',
    c2D: '',
    c3A: '',
    c3B: '',
    c3C: '',
    c4: '',
    c5: '',
    c1MemoKnj: '',
    c1MemoFont: '12',
    c1MemoColor: '0',
    c2MemoKnj: '',
    c2MemoFont: '12',
    c2MemoColor: '0',
    c3MemoKnj: '',
    c3MemoFont: '12',
    c3MemoColor: '0',
    c4MemoKnj: '',
    c4MemoFont: '12',
    c4MemoColor: '0',
    c5MemoKnj: '',
    c5MemoFont: '12',
    c5MemoColor: '0',
  } as SubInfo,
  // 調査アセスメント種別ラベル
  mo01338SurveyAssessmentTypeOneway: {
    value: '',
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'title-h5 background-transparent pb-1',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 認知タイトル
  mo01338CognitiveOneway: {
    value: t('label.interRAI-method-care-assessment-table-C-basic-info-section-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'secondary-background w-100 pa-6',
      itemStyle: 'font-size: 18px',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 日常の意思決定能力タイトル
  mo01338C1Oneway: {
    value: t('label.interRAI-method-care-assessment-table-C-basic-info-section-C1-title'),
    valueFontWeight: '',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *C1．日常の意思決定能力メモテキストエリア
   */
  or30981OnewayTypeC1: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 日常の意思決定能力_リスト
  everydayIntentionDecisionAbilityList: [] as CodeType[],
  // ラジオボタン
  mo00039Oneway: {
    name: '',
    showItemLabel: false,
    inline: true,
    checkOff: true,
  } as Mo00039OnewayType,
  // 記憶を想起する能力タイトル
  mo01338C2Oneway: {
    value: t('label.interRAI-method-care-assessment-table-C-basic-info-section-C2-title'),
    valueFontWeight: '',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *C2．記憶を想起する能力メモテキストエリア
   */
  or30981OnewayTypeC2: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 「記憶を想起する能力回答」の選択肢
  memoryRecallAbilityResponseSelectItems: [] as CodeType[],
  // a.短期記憶ラベル
  mo01338ShortTermMemoryLabelOneway: {
    value: t('label.short-term-memory'),
    customClass: {
      outerClass: 'align-content-center mb-4',
      itemClass: 'align-center',
    } as CustomClass,
  } as Mo01338OnewayType,
  // b.長期記憶ラベル
  mo01338LongTermMemoryLabelOneway: {
    value: t('label.long-term-memory'),
    customClass: {
      outerClass: 'align-content-center mb-4',
      itemClass: 'align-center',
    } as CustomClass,
  } as Mo01338OnewayType,
  // c.手続き記憶ラベル
  mo01338ProcedureMemoryLabelOneway: {
    value: t('label.procedure-memory'),
    customClass: {
      outerClass: 'custom-label align-content-center mb-4',
      itemClass: 'align-center',
      itemStyle: 'white-space: pre',
    } as CustomClass,
  } as Mo01338OnewayType,
  // d.状況記憶ラベル
  mo01338SituationMemoryLabelOneway: {
    value: t('label.situation-memory'),
    customClass: {
      outerClass: 'custom-label align-content-center mb-4',
      itemClass: 'align-center',
      itemStyle: 'white-space: pre',
    } as CustomClass,
  } as Mo01338OnewayType,
  // せん妄の兆候タイトル
  mo01338C3Oneway: {
    value: t('label.interRAI-method-care-assessment-table-C-basic-info-section-C3-title'),
    valueFontWeight: '',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  // せん妄の兆候注ラベル
  mo01338DeliriumSignNotesLabelOneway: {
    value: t('label.delirium-sign-notes'),
    customClass: { outerClass: '' } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *C3．せん妄の兆候メモテキストエリア
   */
  or30981OnewayTypeC3: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 「0. 行動はない」ラベル
  mo01338C30Oneway: {
    value: t('label.interRAI-method-care-assessment-table-C-basic-info-section-C3-label0'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 「1. 行動はあるが、それは普段と同じである」ラベル
  mo01338C31Oneway: {
    value: t('label.interRAI-method-care-assessment-table-C-basic-info-section-C3-label1'),
    valueFontWeight: 'bold',
    customClass: { outerClass: '' } as CustomClass,
  } as Mo01338OnewayType,
  // 「2. 行動はあり、普段の様子と違う
  //       — 新たに出現した、悪化した、数週間前とは違うなど」ラベル
  mo01338C32Oneway: {
    value: t('label.interRAI-method-care-assessment-table-C-basic-info-section-C3-label2'),
    valueFontWeight: 'bold',
    customClass: { outerClass: '' } as CustomClass,
  } as Mo01338OnewayType,
  // a.せん妄の兆候_注意ラベル
  mo01338C3AAttentionLabelOneway: {
    value: t('label.delirium-sign-attention'),
    customClass: { outerClass: 'align-content-center' } as CustomClass,
  } as Mo01338OnewayType,
  // b.せん妄の兆候_会話ラベル
  mo01338C3BConversationLabelOneway: {
    value: t('label.delirium-sign-conversation'),
    customClass: {
      outerClass: 'custom-label align-content-center',
      itemStyle: 'white-space: pre',
    } as CustomClass,
  } as Mo01338OnewayType,
  // c.せん妄の兆候_精神機能ラベル
  mo01338MentalFunctionLabelOneway: {
    value: t('label.delirium-sign-mental-function'),
    customClass: { outerClass: 'align-content-center' } as CustomClass,
  } as Mo01338OnewayType,
  /** セレクトフィールドの単方向モデル */
  mo00040Oneway: {
    itemTitle: 'label',
    itemValue: 'value',
    width: '380px',
    showItemLabel: false,
    items: [] as CodeType[],
    customClass: { outerClass: '' } as CustomClass,
    class: 'mb-2',
  } as Mo00040OnewayType,
  // 精神状態の急な変化タイトル
  mo01338C4Oneway: {
    value: t('label.interRAI-method-care-assessment-table-C-basic-info-section-C4-title'),
    valueFontWeight: '',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 精神状態の急な変化注ラベル
  mo01338MentalStateChangeNotesLabelOneway: {
    value: t('label.mental-state-change-notes'),
    customClass: { outerClass: '' } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *C4．精神状態の急な変化メモテキストエリア
   */
  or30981OnewayTypeC4: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 精神状態の急な変化回答_リスト
  mentalStateEmergencyChangeResponseList: [] as CodeType[],
  // 意思決定能力の変化タイトル
  mo01338C5Oneway: {
    value: t('label.interRAI-method-care-assessment-table-C-basic-info-section-C5-title'),
    valueFontWeight: '',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *C5．意思決定能力の変化メモテキストエリア
   */
  or30981OnewayTypeC5: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 意思決定能力の変化_リスト
  intentionDecisionAbilityChangeList: [] as CodeType[],
  /**
   * GUI00937_入力支援［ケアマネ］
   */
  or51775Oneway: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
})
// 入力フォーム表示フラグ
const inputFormDisplayFlag = ref(true)
// apiから
const isFromApi = ref(false)
// 日常の意思決定能力前回値
const mo00039C1PreviousValue = ref('')
// C2.サブセクション ～ C5.サブセクションの活性制御フラグ
const isDisabled = ref(false)
// サブ情報
const respInfo = {
  subInfo: {
    raiId: '',
    c2A: '',
    c2B: '',
    c2C: '',
    c2D: '',
    c3A: '',
    c3B: '',
    c3C: '',
    c4: '',
    c5: '',
    c1MemoKnj: '',
    c1MemoFont: '',
    c1MemoColor: '',
    c2MemoKnj: '',
    c2MemoFont: '',
    c2MemoColor: '',
    c3MemoKnj: '',
    c3MemoFont: '',
    c3MemoColor: '',
    c4MemoKnj: '',
    c4MemoFont: '',
    c4MemoColor: '',
    c5MemoKnj: '',
    c5MemoFont: '',
    c5MemoColor: '',
  } as SubInfo,
}

const local = reactive({
  // 日常の意思決定能力
  mo00039C1: '',
  // 短期記憶区分
  c2A: '',
  // 長期記憶区分
  c2B: '',
  // 手続き記憶区分
  c2C: '',
  // 状況記憶区分
  c2D: '',
  // せん妄の兆候_注意区分
  mo00040C3A: {
    modelValue: '',
  } as Mo00040Type,
  // せん妄の兆候_会話区分
  mo00040C3B: {
    modelValue: '',
  } as Mo00040Type,
  // せん妄の兆候_精神機能区分
  mo00040C3C: {
    modelValue: '',
  } as Mo00040Type,
  // 精神状態の急な変化回答
  mo00039C4: '',
  // 意思決定能力の変化
  mo00039C5: '',
  /**
   * アセスメントID
   */
  raiId: '',
  /**
   * 画面項目区分
   */
  vieTypeKbn: '',
  // 更新区分
  updateCategory: UPDATE_KBN.NONE,
})

/**
 * C1．日常の意思決定能力 メモテキストエリア入力値
 */
const or30981TypeC1Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * C2．記憶を想起する能力 メモテキストエリア入力値
 */
const or30981TypeC2Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * C3．せん妄の兆候 メモテキストエリア入力値
 */
const or30981TypeC3Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * C4．精神状態の急な変化 メモテキストエリア入力値
 */
const or30981TypeC4Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * C5．意思決定能力の変化 メモテキストエリア入力値
 */
const or30981TypeC5Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)

// GUI00937_入力支援［ケアマネ］の双方向バインドModelValue
const or51775Type = ref({
  modelValue: '',
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Emit
 **************************************************/

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or31124StateType>({
  cpId: Or31124Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (value) {
        commonInfo.kikanKanriFlg = value.kikanKanriFlg
        commonInfo.planPeriodInfo = value.planPeriodInfo
        commonInfo.historyInfo = value.historyInfo
        commonInfo.userId = value.userId
        commonInfo.svJigyoId = value.svJigyoId
        commonInfo.sakuseiId = value.sakuseiId
        commonInfo.kijunbiYmd = value.kijunbiYmd
        commonInfo.raiId = value.raiId
        commonInfo.historyUpdateKbn = value.historyUpdateKbn
        commonInfo.svJigyoKnj = value.svJigyoKnj
        commonInfo.syubetsuId = value.syubetsuId
        commonInfo.tableCreateKbn = value.tableCreateKbn
        commonInfo.assType = value.assType
      }
      // 新規の場合
      if ('add' === value?.executeFlag) {
        // 画面項目の初期化
        inputProject()
      }
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          void save()
          break
        // 新規
        case 'add':
          break
        // 複写
        case 'copy':
          copy()
          break
        // 削除
        case 'delete':
          del()
          break
        // データ再取得
        case 'getData':
          void getData(commonInfo.historyInfo.raiId)
          break
        default:
          break
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or30981Const.CP_ID(1)]: or30981_1.value,
  [Or30981Const.CP_ID(2)]: or30981_2.value,
  [Or30981Const.CP_ID(3)]: or30981_3.value,
  [Or30981Const.CP_ID(4)]: or30981_4.value,
  [Or30981Const.CP_ID(5)]: or30981_5.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// GUI00787 ［メモ入力］画面表示フラグ
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

onMounted(() => {
  // 初期情報取得
  init()
})

const init = () => {
  // 期間管理フラグが「1:管理する」、かつ、計画期間が登録されていない場合
  if (commonInfo.kikanKanriFlg === Or31124Const.PERIOD_MANAGE_FLAG_1 && commonInfo.planPeriodInfo) {
    inputFormDisplayFlag.value = false
    return
  }

  // 汎用コードマスタデータを取得し初期化
  void initCodes()

  // アセスメント(インターライ)画面A初期情報を取得
  //void getData(commonInfo.historyInfo.raiId)
}

/**
 * 各「メモ入力支援アイコンボタン」押下
 *
 * @param type - 画面項目区分
 */
const clickMemoInputCategoryIcon = (type: 'C1' | 'C2' | 'C3' | 'C4' | 'C5') => {
  local.vieTypeKbn = type
  defaultOneway.or51775Oneway = {
    // タイトル
    title: t('label.memo'),
    // 画面ID
    screenId: Or31124Const.STR.SCREEN_ID,
    // 分類ID
    bunruiId: Or31124Const.STR.EMPTY,
    // 大分類ＣＤ
    t1Cd: Or31124Const.STR.T1_CD,
    // 中分類CD
    t2Cd: Or31124Const.STR.T2_CD,
    // 小分類ＣＤ
    t3Cd: Or31124Const.STR.T3_CD,
    // テーブル名
    tableName: Or31124Const.STR.TABLE_NAME,
    // カラム名
    columnName: Or31124Const.STR.EMPTY,
    // アセスメント方式
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? Or31124Const.STR.EMPTY,
    // 文章内容
    inputContents: Or31124Const.STR.EMPTY,
    // 利用者ID
    userId: systemCommonsStore.getUserId,
    // モード
    mode: Or31124Const.STR.EMPTY,
  } as Or51775OnewayType

  switch (type) {
    // C1．日常の意思決定能力
    case 'C1':
      defaultOneway.or51775Oneway.columnName = Or31124Const.STR.C1_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeC1Area.value.content ?? Or31124Const.STR.EMPTY
      break
    // C2．記憶を想起する能力
    case 'C2':
      defaultOneway.or51775Oneway.columnName = Or31124Const.STR.C2_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeC2Area.value.content ?? Or31124Const.STR.EMPTY
      break
    // C3．せん妄の兆候
    case 'C3':
      defaultOneway.or51775Oneway.columnName = Or31124Const.STR.C3_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeC3Area.value.content ?? Or31124Const.STR.EMPTY
      break
    // C4．精神状態の急な変化
    case 'C4':
      defaultOneway.or51775Oneway.columnName = Or31124Const.STR.C4_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeC4Area.value.content ?? Or31124Const.STR.EMPTY
      break
    // C5．意思決定能力の変化
    case 'C5':
      defaultOneway.or51775Oneway.columnName = Or31124Const.STR.C5_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeC5Area.value.content ?? Or31124Const.STR.EMPTY
      break
  }
  // GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}

/**
 * 画面項目の初期化
 */
const inputProject = () => {
  // C1.サブセクション
  or30981TypeC1Area.value.content = ''
  or30981TypeC1Area.value.fontSize = '12'
  or30981TypeC1Area.value.fontColor = '#000000'
  local.mo00039C1 = ''

  // C2.サブセクション
  or30981TypeC2Area.value.content = ''
  or30981TypeC2Area.value.fontSize = '12'
  or30981TypeC3Area.value.fontColor = '#000000'
  local.c2A = ''
  local.c2B = ''
  local.c2C = ''
  local.c2D = ''

  // C3.サブセクション
  or30981TypeC3Area.value.content = ''
  or30981TypeC3Area.value.fontSize = '12'
  or30981TypeC3Area.value.fontColor = '#000000'
  local.mo00040C3A.modelValue = ''
  local.mo00040C3B.modelValue = ''
  local.mo00040C3C.modelValue = ''

  // C4.サブセクション
  or30981TypeC4Area.value.content = ''
  or30981TypeC4Area.value.fontSize = '12'
  or30981TypeC4Area.value.fontColor = '#000000'
  local.mo00039C4 = ''

  // C5.サブセクション
  or30981TypeC5Area.value.content = ''
  or30981TypeC5Area.value.fontSize = '12'
  or30981TypeC5Area.value.fontColor = '#000000'
  local.mo00039C5 = ''
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    // 記憶を想起する能力回答の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEMORY_RESPONSE },
    // せん妄の兆候回答の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DELIRIUM_RESPONSE },
    // 日常の意思決定能力の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DECISION_ABILITY },
    // 精神状態の急な変化回答の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MENTAL_STATE_RESPONSE },
    // 意思決定能力の変化の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ABILITY_CHANGE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // アセスメント種別の選択肢
  const assessmentKindSelectItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )

  // 親情報.調査アセスメント種別により、コードマスタの区分名称を表示する
  defaultOneway.mo01338SurveyAssessmentTypeOneway.value =
    assessmentKindSelectItems?.find((item) => commonInfo.historyInfo.assType === item.value)
      ?.label ?? Or31124Const.STR.EMPTY

  // 記憶を想起する能力回答の選択肢
  const memoryRecallAbilityResponseSelectItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MEMORY_RESPONSE
  )
  defaultOneway.memoryRecallAbilityResponseSelectItems = sortList(
    memoryRecallAbilityResponseSelectItems
  )

  // せん妄の兆候回答の選択肢
  const dekiriumResponseSelectItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DELIRIUM_RESPONSE
  )
  defaultOneway.mo00040Oneway.items = [{ label: '', value: '' }]
  if (dekiriumResponseSelectItems.length > 0) {
    const list = [] as CodeType[]
    dekiriumResponseSelectItems.forEach((item) => {
      list.push({
        label: item.label.split(Or31124Const.STR.SPLIT)[0],
        value: item.value,
      })
    })
    defaultOneway.mo00040Oneway.items.push(...list)
  }

  // 日常の意思決定能力の選択肢
  defaultOneway.everydayIntentionDecisionAbilityList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DECISION_ABILITY
  )

  // 精神状態の急な変化回答の選択肢
  defaultOneway.mentalStateEmergencyChangeResponseList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MENTAL_STATE_RESPONSE
  )

  // 意思決定能力の変化の選択肢
  const intentionDecisionAbilityChangeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ABILITY_CHANGE
  )
  defaultOneway.intentionDecisionAbilityChangeList = sortList(intentionDecisionAbilityChangeList)
}

/**
 * 汎用コードマスタデータをソートする
 *
 * @param targetList - ソート対象リスト
 */
const sortList = (targetList: CodeType[]): CodeType[] => {
  if (!targetList || targetList.length === 0) {
    return []
  }
  const sortedList = [...targetList].sort((a, b) => {
    const valueA = parseInt(a.value, 10)
    const valueB = parseInt(b.value, 10)
    return valueA - valueB
  })

  const index = Math.floor(sortedList.length / 2)
  const firstHalf = sortedList.slice(0, index)
  const secondHalf = sortedList.slice(index)
  const list: CodeType[] = []

  for (let i = 0; i < firstHalf.length; i++) {
    list.push({ ...firstHalf[i] })
    list.push({ ...secondHalf[i] })
  }
  // secondHalf が firstHalf よりも1つの項目多い場合、その余分な項目を新しい配列に追加する
  if (secondHalf.length > firstHalf.length) {
    list.push({ ...secondHalf[secondHalf.length - 1] })
  }

  return list
}

/**
 * デフォルト文字サイズ
 */
const getDefaultFontSize = (): string => {
  // 初期設定マスタ.アセスメント(インターライ)：文字サイズ
  const fontSize = cmnRouteCom.getInitialSettingMaster()?.assCharSize
  // 文字サイズが「0：小さい」の場合
  if ('0' === fontSize) {
    return '9'
  }
  // 文字サイズが「2：大きい」の場合
  else if ('2' === fontSize) {
    return '15'
  }
  // 上記以外の場合
  else {
    // 12：デフォルト値
    return '12'
  }
}

/**
 * デフォルト文字色
 */
const getDefaultFontColor = (): string => {
  // 初期設定マスタ.アセスメント(インターライ)：文字色
  const textColor = cmnRouteCom.getInitialSettingMaster()?.assCharColor
  // 初期設定マスタ.アセスメント(インターライ).文字サイズがある場合
  if (textColor) {
    // 初期設定マスタ.アセスメント(インターライ).文字色
    return textColor
  }
  // 上記以外の場合
  else {
    // #000000：デフォルト値
    return '0'
  }
}

/**
 * AC003_「保存ボタン」押下
 */
const save = async () => {
  // 画面入力データに変更がない場合
  if (!screenInputDataChangejudgement()) {
    // 処理終了にする
    return
  }
  // 入力値処理です
  const inputData: IAssessmentInterRAICUpdateInEntity = {
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    gsyscd: systemCommonsStore.getSystemCode ?? '',
    kinouId: systemCommonsStore.getFunctionId ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    sectionName: TeX0003Const.SECTION_NAME,
    kikanKanriFlg: commonInfo.kikanKanriFlg,
    historyNo: commonInfo.historyInfo.krirekiNo,
    svJigyoName: commonInfo.svJigyoKnj,
    periodNo: commonInfo.planPeriodInfo.periodNo,
    startYmd: commonInfo.kikanKanriFlg === '1' ? commonInfo.planPeriodInfo.startYmd : '',
    endYmd: commonInfo.kikanKanriFlg === '1' ? commonInfo.planPeriodInfo.endYmd : '',
    index: TeX0003Const.STR.THREE,
    tabName: 'C',
    edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
    edocumentUseParam: useCmnCom().getEDocumentParam(),
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    syubetsuId: commonInfo.syubetsuId,
    subKbn: 'C',
    updateKbn: local.updateCategory,
    historyUpdateKbn: commonInfo.historyUpdateKbn,
    tableCreateKbn: commonInfo.tableCreateKbn,
    sc1Id: commonInfo.historyInfo.sc1Id,
    raiId: commonInfo.raiId,
    kijunbiYmd: commonInfo.kijunbiYmd,
    sakuseiId: commonInfo.sakuseiId,
    assType: commonInfo.assType,
    subInfoC: {
      raiId: commonInfo.historyInfo.raiId,
      c2A: local.c2A,
      c2B: local.c2B,
      c2C: local.c2C,
      c2D: local.c2D,
      c3A: local.mo00040C3A.modelValue,
      c3B: local.mo00040C3B.modelValue,
      c3C: local.mo00040C3C.modelValue,
      c4: local.mo00039C4,
      c5: local.mo00039C5,
      c1MemoKnj: or30981TypeC1Area.value.content,
      c1MemoFont: or30981TypeC1Area.value.fontSize,
      c1MemoColor: or30981TypeC1Area.value.fontColor,
      c2MemoKnj: or30981TypeC2Area.value.content,
      c2MemoFont: or30981TypeC2Area.value.fontSize,
      c2MemoColor: or30981TypeC2Area.value.fontColor,
      c3MemoKnj: or30981TypeC3Area.value.content,
      c3MemoFont: or30981TypeC3Area.value.fontSize,
      c3MemoColor: or30981TypeC3Area.value.fontColor,
      c4MemoKnj: or30981TypeC4Area.value.content,
      c4MemoFont: or30981TypeC4Area.value.fontSize,
      c4MemoColor: or30981TypeC4Area.value.fontColor,
      c5MemoKnj: or30981TypeC5Area.value.content,
      c5MemoFont: or30981TypeC5Area.value.fontSize,
      c5MemoColor: or30981TypeC5Area.value.fontColor,
    } as SubInfo,
  }

  const resData: IAssessmentInterRAICUpdateOutEntity = await ScreenRepository.update(
    'assessmentInterRAICUpdate',
    inputData
  )
  // 保存成功の場合
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    // 更新区分を「U：更新」に設定
    local.updateCategory = UPDATE_KBN.UPDATE
  }
}

/**
 * AC003-2-1_アセスメント(インターライ)画面履歴の最新情報を取得
 *
 * @param raiId - アセスメントID
 */
const getData = async (raiId: string) => {
  // バックエンドAPIから初期情報取得
  const inputData: IAssessmentInterRAICInitInEntity = {
    // 親情報.アセスメントID
    raiId: raiId,
  }
  const assessmentInterRAICResp: IAssessmentInterRAICInitOutEntity = await ScreenRepository.select(
    'assessmentInterRAICInitSelect',
    inputData
  )
  if (
    ResBodyStatusCode.SUCCESS === assessmentInterRAICResp.statusCode &&
    assessmentInterRAICResp.data
  ) {
    // データ取得
    respInfo.subInfo = assessmentInterRAICResp.data.subInfoC
    isFromApi.value = true
    // 取得したサブ情報（C）の各項目を画面の入力フォームへセットする
    setScreenItem(respInfo.subInfo)
    // 更新区分を「U：更新」に設定
    local.updateCategory = UPDATE_KBN.UPDATE
  } else {
    // 更新区分を「C：新規」に設定
    local.updateCategory = UPDATE_KBN.CREATE
    // 画面の入力フォームの各項目を空白でセットする
    setScreenItem(defaultOneway.subInfo)
  }
}

/**
 * 画面項目設定
 *
 * @param subInfo - サブ情報
 */
const setScreenItem = (subInfo: SubInfo) => {
  // C1.サブセクション
  // メモ
  or30981TypeC1Area.value.content = subInfo.c1MemoKnj
  or30981TypeC1Area.value.fontSize = subInfo.c1MemoFont ? subInfo.c1MemoFont : getDefaultFontSize()
  or30981TypeC1Area.value.fontColor = subInfo.c1MemoColor
    ? subInfo.c1MemoColor
    : getDefaultFontColor()
  // 日常の意思決定能力区分
  local.mo00039C1 = subInfo.c1
  if (local.mo00039C1 === '5') {
    clearInputValue()
    return
  }

  // C2.サブセクション
  // メモ
  or30981TypeC2Area.value.content = subInfo.c2MemoKnj
  or30981TypeC2Area.value.fontSize = subInfo.c2MemoFont ? subInfo.c2MemoFont : getDefaultFontSize()
  or30981TypeC2Area.value.fontColor = subInfo.c2MemoColor
    ? subInfo.c2MemoColor
    : getDefaultFontColor()
  // 短期記憶区分
  local.c2A = subInfo.c2A
  // 長期記憶区分
  local.c2B = subInfo.c2B
  // 手続き記憶区分
  local.c2C = subInfo.c2C
  // 状況記憶区分
  local.c2D = subInfo.c2D

  // C3.サブセクション
  // メモ
  or30981TypeC3Area.value.content = subInfo.c3MemoKnj
  or30981TypeC3Area.value.fontSize = subInfo.c3MemoFont ? subInfo.c3MemoFont : getDefaultFontSize()
  or30981TypeC3Area.value.fontColor = subInfo.c3MemoColor
    ? subInfo.c3MemoColor
    : getDefaultFontColor()
  // せん妄の兆候_注意区分
  local.mo00040C3A.modelValue = subInfo.c3A
  // せん妄の兆候_会話区分
  local.mo00040C3B.modelValue = subInfo.c3B
  // せん妄の兆候_精神機能区分
  local.mo00040C3C.modelValue = subInfo.c3C

  // C4.サブセクション
  // メモ
  or30981TypeC4Area.value.content = subInfo.c4MemoKnj
  or30981TypeC4Area.value.fontSize = subInfo.c4MemoFont ? subInfo.c4MemoFont : getDefaultFontSize()
  or30981TypeC4Area.value.fontColor = subInfo.c4MemoColor
    ? subInfo.c4MemoColor
    : getDefaultFontColor()
  // 精神状態の急な変化区分
  local.mo00039C4 = subInfo.c4

  // C5.サブセクション
  // メモ
  or30981TypeC5Area.value.content = subInfo.c5MemoKnj
  or30981TypeC5Area.value.fontSize = subInfo.c5MemoFont ? subInfo.c5MemoFont : getDefaultFontSize()
  or30981TypeC5Area.value.fontColor = subInfo.c5MemoColor
    ? subInfo.c5MemoColor
    : getDefaultFontColor()
  // 意思決定能力の変化区分
  local.mo00039C5 = subInfo.c5
  isFromApi.value = false
}

/**
 * 画面入力データに変更判定（true：変更あり; false：変更なし）
 *
 */
const screenInputDataChangejudgement = () => {
  // C1.サブセクション
  // メモ
  if (or30981TypeC1Area.value.content === respInfo.subInfo.c1MemoKnj) {
    return true
  }
  // メモ色、メモフォント
  if (
    or30981TypeC1Area.value.fontColor === respInfo.subInfo.c1MemoColor ||
    or30981TypeC1Area.value.fontSize === respInfo.subInfo.c1MemoFont
  ) {
    return true
  }
  // 日常の意思決定能力区分
  if (local.mo00039C1 === respInfo.subInfo.c1) {
    return true
  }

  // C2.サブセクション
  // メモ
  if (or30981TypeC2Area.value.content === respInfo.subInfo.c2MemoKnj) {
    return true
  }
  // メモ色、メモフォント
  if (
    or30981TypeC2Area.value.fontColor === respInfo.subInfo.c2MemoColor ||
    or30981TypeC2Area.value.fontSize === respInfo.subInfo.c2MemoFont
  ) {
    return true
  }
  // 短期記憶区分、長期記憶区分、手続き記憶区分、状況記憶区分
  if (
    local.c2A === respInfo.subInfo.c2A ||
    local.c2B === respInfo.subInfo.c2B ||
    local.c2C === respInfo.subInfo.c2C ||
    local.c2D === respInfo.subInfo.c2D
  ) {
    return true
  }

  // C3.サブセクション
  // メモ
  if (or30981TypeC3Area.value.content === respInfo.subInfo.c3MemoKnj) {
    return true
  }
  // メモ色、メモフォント
  if (
    or30981TypeC3Area.value.fontColor === respInfo.subInfo.c3MemoColor ||
    or30981TypeC3Area.value.fontSize === respInfo.subInfo.c3MemoFont
  ) {
    return true
  }
  // せん妄の兆候_注意区分、せん妄の兆候_会話区分、せん妄の兆候_精神機能区分
  if (
    local.mo00040C3A.modelValue === respInfo.subInfo.c3A ||
    local.mo00040C3B.modelValue === respInfo.subInfo.c3B ||
    local.mo00040C3C.modelValue === respInfo.subInfo.c3C
  ) {
    return true
  }

  // C4.サブセクション
  // メモ
  if (or30981TypeC4Area.value.content === respInfo.subInfo.c4MemoKnj) {
    return true
  }
  // メモ色、メモフォント
  if (
    or30981TypeC4Area.value.fontColor === respInfo.subInfo.c4MemoColor ||
    or30981TypeC4Area.value.fontSize === respInfo.subInfo.c4MemoFont
  ) {
    return true
  }
  // 精神状態の急な変化区分
  if (local.mo00039C4 === respInfo.subInfo.c4) {
    return true
  }

  // C5.サブセクション
  // メモ
  if (or30981TypeC5Area.value.content === respInfo.subInfo.c5MemoKnj) {
    return true
  }
  // メモ色、メモフォント
  if (
    or30981TypeC5Area.value.fontColor === respInfo.subInfo.c5MemoColor ||
    or30981TypeC5Area.value.fontSize === respInfo.subInfo.c5MemoFont
  ) {
    return true
  }
  // 意思決定能力の変化区分
  if (local.mo00039C5 === respInfo.subInfo.c5) {
    return true
  }
  return false
}

/**
 * 入力値をクリア
 *
 * @description
 * c2,c3,c4,c5のすべての項目の入力値をクリア、かつ、項目を非活性にする
 */
const clearInputValue = () => {
  // C2.サブセクション ～ C5.サブセクションのすべての項目の入力値をクリア
  or30981TypeC2Area.value.content = ''
  local.c2A = ''
  local.c2B = ''
  local.c2C = ''
  local.c2D = ''
  or30981TypeC3Area.value.content = ''
  local.mo00040C3A.modelValue = ''
  local.mo00040C3B.modelValue = ''
  local.mo00040C3C.modelValue = ''
  or30981TypeC4Area.value.content = ''
  local.mo00039C4 = ''
  or30981TypeC5Area.value.content = ''
  local.mo00039C5 = ''
  // C2.サブセクション ～ C5.サブセクションを非活性にする
  isDisabled.value = true
}

/**
 * AC005_「複写ボタン」押下
 */
const copy = () => {
  void getData(local.raiId)
}

/**
 * AC011_「削除」押下
 */
const del = () => {
  // TODO 画面「C」タブに対し、更新区分を「D:削除」にする。
}

/**
 * 確認メッセージを表示する
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    // 本文末に追加の場合
    if (Or31124Const.ADD_END_TEXT_IMPORT_TYPE === data.type) {
      switch (local.vieTypeKbn) {
        // C1．日常の意思決定能力
        case 'C1':
          or30981TypeC1Area.value.content += data.value
          break
        // C2．記憶を想起する能力
        case 'C2':
          or30981TypeC2Area.value.content += data.value
          break
        // C3．せん妄の兆候
        case 'C3':
          or30981TypeC3Area.value.content += data.value
          break
        // C4．精神状態の急な変化
        case 'C4':
          or30981TypeC4Area.value.content += data.value
          break
        // C5．意思決定能力の変化
        case 'C5':
          or30981TypeC5Area.value.content += data.value
          break
        default:
          break
      }
    }
    // 本文上書の場合
    else if (Or31124Const.OVERWRITE_TEXT_IMPORT_TYPE === data.type) {
      switch (local.vieTypeKbn) {
        // C1．日常の意思決定能力
        case 'C1':
          or30981TypeC1Area.value.content = data.value
          break
        // C2．記憶を想起する能力
        case 'C2':
          or30981TypeC2Area.value.content = data.value
          break
        // C3．せん妄の兆候
        case 'C3':
          or30981TypeC3Area.value.content = data.value
          break
        // C4．精神状態の急な変化
        case 'C4':
          or30981TypeC4Area.value.content = data.value
          break
        // C5．意思決定能力の変化
        case 'C5':
          or30981TypeC5Area.value.content = data.value
          break
        default:
          break
      }
    }
  }
}

/**************************************************
 * ウォッチャー
 **************************************************/

/**
 * 「日常の意思決定能力」ラジオボタン押下
 *
 */
watch(
  () => local.mo00039C1,
  (newValue) => {
    if (isFromApi.value) {
      isFromApi.value = false
      return
    }
    // アセスメント値を5以外→5に変更した場合
    if (newValue === '5') {
      // メッセージ「i-cmn-10180」
      showOr21814MsgTwoBtn(t('message.i-cmn-10180'))
    } else {
      // 日常の意思決定能力前回値を保存
      mo00039C1PreviousValue.value = newValue
      if (isDisabled.value) {
        // C2.サブセクション ～ C5.サブセクションを活性にする
        isDisabled.value = false
      }
    }
  }
)

/**
 *  メッセージの選択結果を監視
 *
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      clearInputValue()
    } else {
      // 値を戻る
      local.mo00039C1 = mo00039C1PreviousValue.value
    }
  }
)
</script>

<template>
  <!-- 入力フォーム -->
  <c-v-row
    v-show="inputFormDisplayFlag"
    class="or31124Container"
    no-gutters
  >
    <c-v-col
      cols="auto"
      class="pt-6"
    >
      <div style="width: 1080px">
        <c-v-row no-gutters>
          <c-v-spacer />
          <!-- 調査アセスメント種別ラベル -->
          <base-mo01338
            style="margin-right: -6px !important"
            :oneway-model-value="defaultOneway.mo01338SurveyAssessmentTypeOneway"
          />
          <!-- 認知タイトル -->
          <base-mo01338 :oneway-model-value="defaultOneway.mo01338CognitiveOneway" />
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col class="pa-0">
            <!-- C1.サブセクション -->
            <c-v-row
              class="secondary-background h-auto"
              no-gutters
            >
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 日常の意思決定能力タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338C1Oneway" />
                  </c-v-col>
                </c-v-row>
                <!-- C1．日常の意思決定能力ラジオグループ -->
                <c-v-row
                  class="asection_right_second"
                  no-gutters
                >
                  <base-mo00039
                    v-model="local.mo00039C1"
                    :oneway-model-value="defaultOneway.mo00039Oneway"
                  >
                    <base-at-radio
                      v-for="(item, index) in defaultOneway.everydayIntentionDecisionAbilityList"
                      :key="'or31124-' + index"
                      :name="'or31124-radio-' + index"
                      :radio-label="item.label"
                      :value="item.value"
                      radio-class="radio-item"
                    />
                  </base-mo00039>
                </c-v-row>
                <c-v-row
                  class="pl-12 pr-6 pb-6"
                  no-gutters
                >
                  <c-v-col class="pa-0">
                    <g-custom-or-30981
                      v-bind="or30981_1"
                      v-model="or30981TypeC1Area"
                      :oneway-model-value="defaultOneway.or30981OnewayTypeC1"
                      @on-click-edit-btn="clickMemoInputCategoryIcon('C1')"
                    ></g-custom-or-30981>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- C2.サブセクション ～ C5.サブセクション -->
            <div :class="{ 'disabled-area': isDisabled }">
              <!-- C2.サブセクション -->
              <c-v-row
                class="secondary-background h-auto"
                no-gutters
              >
                <c-v-col class="pa-0">
                  <c-v-row
                    class="sub_title"
                    no-gutters
                  >
                    <!-- 記憶を想起する能力タイトル -->
                    <c-v-col class="align-content-center px-6 h-100">
                      <base-mo01338 :oneway-model-value="defaultOneway.mo01338C2Oneway" />
                    </c-v-col>
                  </c-v-row>
                  <div class="asection_right_second pr-0">
                    <!-- a.短期記憶 -->
                    <c-v-row
                      class="align-content-center pr-0"
                      no-gutters
                    >
                      <!-- a.短期記憶ラベル -->
                      <base-mo01338
                        :oneway-model-value="defaultOneway.mo01338ShortTermMemoryLabelOneway"
                      />
                      <c-v-spacer />
                      <!-- a.短期記憶区分 -->
                      <base-mo00039
                        v-model="local.c2A"
                        :oneway-model-value="defaultOneway.mo00039Oneway"
                      >
                        <base-at-radio
                          v-for="(
                            item, index
                          ) in defaultOneway.memoryRecallAbilityResponseSelectItems"
                          :key="'or31124-' + index"
                          :name="'or31124-radio-' + index"
                          :radio-label="item.label"
                          :value="item.value"
                          radio-class="radio-item2"
                        />
                      </base-mo00039>
                    </c-v-row>
                    <!-- b.長期記憶 -->
                    <c-v-row
                      class="align-content-center pr-0"
                      no-gutters
                    >
                      <!-- b.長期記憶ラベル -->
                      <base-mo01338
                        :oneway-model-value="defaultOneway.mo01338LongTermMemoryLabelOneway"
                      />
                      <c-v-spacer />
                      <!-- b.長期記憶区分 -->
                      <base-mo00039
                        v-model="local.c2B"
                        :oneway-model-value="defaultOneway.mo00039Oneway"
                      >
                        <base-at-radio
                          v-for="(
                            item, index
                          ) in defaultOneway.memoryRecallAbilityResponseSelectItems"
                          :key="'or31124-' + index"
                          :name="'or31124-radio-' + index"
                          :radio-label="item.label"
                          :value="item.value"
                          radio-class="radio-item2"
                          :class="{
                            radioItemDisabled:
                              commonInfo.historyInfo.assType == '1' ||
                              commonInfo.historyInfo.assType == '3',
                          }"
                        />
                      </base-mo00039>
                    </c-v-row>
                    <!-- c.手続き記憶 -->
                    <c-v-row
                      class="align-content-center pr-0"
                      no-gutters
                    >
                      <!-- c.手続き記憶ラベル -->
                      <base-mo01338
                        :oneway-model-value="defaultOneway.mo01338ProcedureMemoryLabelOneway"
                      />
                      <c-v-spacer />
                      <!-- c.手続き記憶区分 -->
                      <base-mo00039
                        v-model="local.c2C"
                        :oneway-model-value="defaultOneway.mo00039Oneway"
                      >
                        <base-at-radio
                          v-for="(
                            item, index
                          ) in defaultOneway.memoryRecallAbilityResponseSelectItems"
                          :key="'or31124-' + index"
                          :name="'or31124-radio-' + index"
                          :radio-label="item.label"
                          :value="item.value"
                          radio-class="radio-item2"
                        />
                      </base-mo00039>
                    </c-v-row>
                    <!-- d.状況記憶 -->
                    <c-v-row
                      class="align-content-center pr-0"
                      no-gutters
                    >
                      <!-- d.状況記憶ラベル -->
                      <base-mo01338
                        :oneway-model-value="defaultOneway.mo01338SituationMemoryLabelOneway"
                      />
                      <c-v-spacer />
                      <!-- d.状況記憶区分 -->
                      <base-mo00039
                        v-model="local.c2D"
                        :oneway-model-value="defaultOneway.mo00039Oneway"
                      >
                        <base-at-radio
                          v-for="(
                            item, index
                          ) in defaultOneway.memoryRecallAbilityResponseSelectItems"
                          :key="'or31124-' + index"
                          :name="'or31124-radio-' + index"
                          :radio-label="item.label"
                          :value="item.value"
                          radio-class="radio-item2"
                        />
                      </base-mo00039>
                    </c-v-row>
                  </div>
                  <c-v-row
                    class="pl-12 pr-6 pb-6"
                    no-gutters
                  >
                    <c-v-col class="pa-0">
                      <g-custom-or-30981
                        v-bind="or30981_2"
                        v-model="or30981TypeC2Area"
                        :oneway-model-value="defaultOneway.or30981OnewayTypeC2"
                        @on-click-edit-btn="clickMemoInputCategoryIcon('C2')"
                      ></g-custom-or-30981>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- C3.サブセクション -->
              <c-v-row
                class="secondary-background h-auto"
                no-gutters
              >
                <c-v-col class="pa-0">
                  <c-v-row
                    class="sub_title"
                    no-gutters
                  >
                    <!-- せん妄の兆候タイトル -->
                    <c-v-col class="align-content-center px-6 h-100">
                      <base-mo01338 :oneway-model-value="defaultOneway.mo01338C3Oneway" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    class="asection_right_second pt-6"
                    style="padding-bottom: 15px"
                    no-gutters
                  >
                    <c-v-col class="pa-0">
                      <!-- せん妄の兆候注ラベル -->
                      <c-v-row
                        class="mb-4"
                        no-gutters
                      >
                        <base-mo01338
                          :oneway-model-value="defaultOneway.mo01338DeliriumSignNotesLabelOneway"
                        />
                      </c-v-row>
                      <!-- せん妄の兆候回答ボタン -->
                      <c-v-row
                        class="border-all-1 pa-4 mb-4"
                        no-gutters
                      >
                        <c-v-col class="pa-0">
                          <c-v-row no-gutters>
                            <!-- 「0.問題なし」ラベル -->
                            <base-mo01338 :oneway-model-value="defaultOneway.mo01338C30Oneway" />
                          </c-v-row>
                          <c-v-row no-gutters>
                            <!-- 「1. 行動はあるが、それは普段と同じである」ラベル -->
                            <base-mo01338 :oneway-model-value="defaultOneway.mo01338C31Oneway" />
                          </c-v-row>
                          <c-v-row no-gutters>
                            <!-- 2. 行動はあり、普段の様子と違う
                            — 新たに出現した、悪化した、数週間前とは違うなど」ラベル -->
                            <base-mo01338 :oneway-model-value="defaultOneway.mo01338C32Oneway" />
                          </c-v-row>
                        </c-v-col>
                      </c-v-row>
                      <!-- a.せん妄の兆候_注意 -->
                      <c-v-row no-gutters>
                        <!-- a.せん妄の兆候_注意ラベル -->
                        <base-mo01338
                          :oneway-model-value="defaultOneway.mo01338C3AAttentionLabelOneway"
                        />
                        <c-v-spacer />
                        <!-- せん妄の兆候_注意区分 -->
                        <base-mo-00040
                          v-model="local.mo00040C3A"
                          :oneway-model-value="defaultOneway.mo00040Oneway"
                        />
                      </c-v-row>
                      <!-- b.せん妄の兆候_会話 -->
                      <c-v-row no-gutters>
                        <!-- b.せん妄の兆候_会話ラベル -->
                        <base-mo01338
                          :oneway-model-value="defaultOneway.mo01338C3BConversationLabelOneway"
                        />
                        <c-v-spacer />
                        <!-- せん妄の兆候_会話区分 -->
                        <base-mo-00040
                          v-model="local.mo00040C3B"
                          :oneway-model-value="defaultOneway.mo00040Oneway"
                        />
                      </c-v-row>
                      <!-- c.せん妄の兆候_精神機能 -->
                      <c-v-row no-gutters>
                        <!-- c.せん妄の兆候_精神機能ラベル -->
                        <base-mo01338
                          :oneway-model-value="defaultOneway.mo01338MentalFunctionLabelOneway"
                        />
                        <c-v-spacer />
                        <!-- せん妄の兆候_精神機能区分 -->
                        <base-mo-00040
                          v-model="local.mo00040C3C"
                          :oneway-model-value="defaultOneway.mo00040Oneway"
                        />
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    class="pl-12 pr-6 pb-6"
                    no-gutters
                  >
                    <c-v-col class="pa-0">
                      <g-custom-or-30981
                        v-bind="or30981_3"
                        v-model="or30981TypeC3Area"
                        :oneway-model-value="defaultOneway.or30981OnewayTypeC3"
                        @on-click-edit-btn="clickMemoInputCategoryIcon('C3')"
                      ></g-custom-or-30981>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- C4.サブセクション -->
              <c-v-row
                class="secondary-background h-auto"
                no-gutters
              >
                <c-v-col class="pa-0">
                  <c-v-row
                    class="sub_title"
                    no-gutters
                  >
                    <!-- 精神状態の急な変化タイトル -->
                    <c-v-col class="align-content-center px-6 h-100">
                      <base-mo01338 :oneway-model-value="defaultOneway.mo01338C4Oneway" />
                    </c-v-col>
                  </c-v-row>
                  <!-- C4．精神状態の急な変化ラジオグループ -->
                  <c-v-row
                    class="asection_right_second pt-8"
                    no-gutters
                  >
                    <!-- 精神状態の急な変化注ラベル -->
                    <c-v-row
                      class="mb-4"
                      no-gutters
                    >
                      <base-mo01338
                        :oneway-model-value="defaultOneway.mo01338MentalStateChangeNotesLabelOneway"
                      />
                    </c-v-row>
                    <base-mo00039
                      v-model="local.mo00039C4"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                    >
                      <base-at-radio
                        v-for="(
                          item, index
                        ) in defaultOneway.mentalStateEmergencyChangeResponseList"
                        :key="'or31124-' + index"
                        :name="'or31124-radio-' + index"
                        :radio-label="item.label"
                        :value="item.value"
                        radio-class="radio-item3"
                      />
                    </base-mo00039>
                  </c-v-row>
                  <c-v-row
                    class="pl-12 pr-6 pb-6"
                    no-gutters
                  >
                    <c-v-col class="pa-0">
                      <g-custom-or-30981
                        v-bind="or30981_4"
                        v-model="or30981TypeC4Area"
                        :oneway-model-value="defaultOneway.or30981OnewayTypeC4"
                        @on-click-edit-btn="clickMemoInputCategoryIcon('C4')"
                      ></g-custom-or-30981>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- C5.サブセクション -->
              <c-v-row
                class="secondary-background h-auto"
                no-gutters
              >
                <c-v-col class="pa-0">
                  <c-v-row
                    class="sub_title"
                    no-gutters
                  >
                    <!-- 意思決定能力の変化タイトル -->
                    <c-v-col class="align-content-center px-6 h-100">
                      <base-mo01338 :oneway-model-value="defaultOneway.mo01338C5Oneway" />
                    </c-v-col>
                  </c-v-row>
                  <!-- C5．意思決定能力の変化ラジオグループ -->
                  <c-v-row
                    class="asection_right_second"
                    no-gutters
                  >
                    <base-mo00039
                      v-model="local.mo00039C5"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                      class="gridContent"
                    >
                      <base-at-radio
                        v-for="(item, index) in defaultOneway.intentionDecisionAbilityChangeList"
                        :key="'or31124-' + index"
                        :name="'or31124-radio-' + index"
                        :radio-label="item.label"
                        :value="item.value"
                        radio-class="radio-item2"
                        style="margin-bottom: 13px"
                      />
                    </base-mo00039>
                  </c-v-row>
                  <c-v-row
                    class="pl-12 pr-6 pb-6"
                    no-gutters
                  >
                    <c-v-col class="pa-0">
                      <g-custom-or-30981
                        v-bind="or30981_5"
                        v-model="or30981TypeC5Area"
                        :oneway-model-value="defaultOneway.or30981OnewayTypeC5"
                        @on-click-edit-btn="clickMemoInputCategoryIcon('C5')"
                      ></g-custom-or-30981>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
            </div>
          </c-v-col>
        </c-v-row>
      </div>
      <!-- interRAIロゴ -->
      <c-v-row
        class="mt-6"
        no-gutters
      >
        <c-v-col class="pa-0">
          <c-v-img
            width="129"
            aspect-ratio="16/9"
            cover
            :src="InterRAI"
            style="float: right"
          ></c-v-img>
        </c-v-col>
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <!-- GUI00937_入力支援［ケアマネ］ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Type"
    :oneway-model-value="defaultOneway.or51775Oneway"
    @confirm="or51775Confirm"
  />
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  ></g-base-or21814>
</template>

<style scoped lang="scss">
.or31124Container {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: transparent;
}

.title-h5 :deep(.item-label) {
  font-size: 24px;
  font-weight: 400 !important;
}

.sub_title {
  border: 1px #dadada solid;
  height: 48px;
  align-content: center;
  background-color: rgba(var(--v-theme-black-100));
}

.secondary-background {
  background-color: rgba(var(--v-theme-secondaryBackground));
}

.border-all-1 {
  border: 1px solid rgba(var(--v-theme-text));
}

// transparent
.background-transparent {
  background-color: transparent !important;
}

.custom-label {
  white-space: pre-line;
}

.v-btn:focus {
  box-shadow: none !important;
  outline: none !important;
}

.asection_right_second {
  justify-content: start;
  padding: 27px 24px 2px 48px;

  :deep(.v-selection-control--dirty) {
    background-color: #ebf2fd !important;
    border: 1px solid rgb(var(--v-theme-key)) !important;
  }

  :deep(.v-label--clickable) {
    width: 100% !important;
  }

  .radio-item {
    width: 640px;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    margin-bottom: 16px;
  }

  .radio-item2 {
    width: 180px;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    margin-right: 16px;
    margin-bottom: 19px;
  }

  .radio-item3 {
    width: 180px;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    margin-right: 24px;
    margin-bottom: 17px;
  }

  :deep(.v-field__input) {
    min-height: 8px !important;
  }

  :has(> .toGray) {
    :first-child {
      background-color: #c0c0c0 !important;
    }
  }
}

.gridContent {
  display: grid;
  grid-template-columns: repeat(1, 400px);
}

.disabled-area {
  pointer-events: none;
  opacity: 0.6;
}
</style>
