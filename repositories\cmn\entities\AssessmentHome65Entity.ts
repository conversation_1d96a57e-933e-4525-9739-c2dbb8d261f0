import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * ［アセスメント］画面（居宅）（6⑤）モーダル
 *
 * @description
 * ［アセスメント］画面（居宅）（6⑤）画面API用エンティティ
 *
 * <AUTHOR>
 */
/**
 * ［アセスメント］画面（居宅）6⑤取得入力エンティティ
 */
export interface AssessmentHomeTab65SelectInEntity extends InWebEntity {
  /** 計画期間ID */
  sc1Id?: string
  /** アセスメントID */
  gdlId?: string
  /** タブID */
  tabId?: string
  /** 期間処理区分 */
  kknSyoriKbn?: string
  /** 履歴処理区分 */
  rirekiSyoriKbn?: string
  /** 履歴作成日 */
  createYmd?: string
  /** 利用者ID */
  userId?: string
  /** 調査票改訂フラグ */
  ssmNinteiFlg?: string
  /** ロケーション */
  location?: string
  /** 事業所ID */
  svJigyoId?: string
  /** 事業者グループ適用ID */
  svJigyoGrpId?: string
  /** 改訂フラグ */
  ninteiFlg?: string
}

/**
 * ［アセスメント］画面（居宅）6⑤取得出力エンティティ
 */
export interface AssessmentHomeTab65SelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    // 社会生活（への適応）力情報(H21/4改訂)
    cpnTucGdl4Kan15H21Info: SocialLifeAdaptationInfo
    // 社会生活（への適応）力情報(R3/4改訂)
    cpnTucGdl5Kan15R3Info: SocialLifeAdaptationInfo
    // 課題と目標リスト
    issuesAndGoalList: IssuesAndGoalItems[]
    // アセスメント名称
    assName: string
  }
}

/**
 * 社会生活（への適応）力情報
 */
export interface SocialLifeAdaptationInfo {
  /** アセスメントID */
  gdlId: string
  /** 計画期間ID */
  sc1Id: string
  /** 認定項目5-1（薬の内服） */
  bango51: string
  /** 認定項目5-2（金銭の管理） */
  bango52: string
  /** 認定項目5-3（日常の意思決定） */
  bango53: string
  /** 認定項目5-4（集団への不適応） */
  bango54: string
  /** 認定項目5-5（買い物） */
  bango55: string
  /** 認定項目5-6（簡単な調理） */
  bango56: string
  /** 認定項目5-7（電話の利用） */
  bango57: string
  /** 認定項目5-8（日中の活動（生活）状況等） */
  bango58: string
  /** 認定項目5-9（家族居住環境社会参加の状況などの変化） */
  bango59: string
  /** 家族交流有無 */
  kazokuKouryuUmu: string
  /** 家族交流内容 */
  kazokuKouryuKnj: string
  /** 地域交流有無 */
  chiikiKouryuUmu: string
  /** 地域交流内容 */
  chiikiKouryuKnj: string
  /** 友人交流有無 */
  yuujinKouryuUmu: string
  /** 友人交流内容 */
  yuujinKouryuKnj: string
  /** 家族実施5-2_1（金銭管理） */
  famJisshi521: string
  /** 家族実施5-2_2（買い物） */
  famJisshi522: string
  /** 家族実施5-2_3（調理） */
  famJisshi523: string
  /** 家族実施5-2_4（準備後始末） */
  famJisshi524: string
  /** 家族実施5-7_1（定期的な相談助言） */
  famJisshi571: string
  /** 家族実施5-7_2（各種書類作成代行） */
  famJisshi572: string
  /** 家族実施5-7_3（余暇活動支援） */
  famJisshi573: string
  /** 家族実施5-7_4（移送外出介助） */
  famJisshi574: string
  /** 家族実施5-7_5（代読代筆） */
  famJisshi575: string
  /** 家族実施5-7_6（話し相手） */
  famJisshi576: string
  /** 家族実施5-7_7（安否確認） */
  famJisshi577: string
  /** 家族実施5-7_8（緊急連絡手段の確保） */
  famJisshi578: string
  /** 家族実施5-7_9（家族連絡の確保） */
  famJisshi579: string
  /** 家族実施5-7_10（社会活動への支援） */
  famJisshi5710: string
  /** サービス実施5-2_1（金銭管理） */
  serJisshi521: string
  /** サービス実施5-2_2（買い物） */
  serJisshi522: string
  /** サービス実施5-2_3（調理） */
  serJisshi523: string
  /** サービス実施5-2_4（準備後始末） */
  serJisshi524: string
  /** サービス実施5-7_1（定期的な相談助言） */
  serJisshi571: string
  /** サービス実施5-7_2（各種書類作成代行） */
  serJisshi572: string
  /** サービス実施5-7_3（余暇活動支援） */
  serJisshi573: string
  /** サービス実施5-7_4（移送外出介助） */
  serJisshi574: string
  /** サービス実施5-7_5（代読代筆） */
  serJisshi575: string
  /** サービス実施5-7_6（話し相手） */
  serJisshi576: string
  /** サービス実施5-7_7（安否確認） */
  serJisshi577: string
  /** サービス実施5-7_8（緊急連絡手段の確保） */
  serJisshi578: string
  /** サービス実施5-7_9（家族連絡の確保） */
  serJisshi579: string
  /** サービス実施5-7_10（社会活動への支援） */
  serJisshi5710: string
  /** 希望5-2_1（金銭管理） */
  kibo521: string
  /** 希望5-2_2（買い物） */
  kibo522: string
  /** 希望5-2_3（調理） */
  kibo523: string
  /** 希望5-2_4（準備後始末） */
  kibo524: string
  /** 希望5-7_1（定期的な相談助言） */
  kibo571: string
  /** 希望5-7_2（各種書類作成代行） */
  kibo572: string
  /** 希望5-7_3（余暇活動支援） */
  kibo573: string
  /** 希望5-7_4（移送外出介助） */
  kibo574: string
  /** 希望5-7_5（代読代筆） */
  kibo575: string
  /** 希望5-7_6（話し相手） */
  kibo576: string
  /** 希望5-7_7（安否確認） */
  kibo577: string
  /** 希望5-7_8（緊急連絡手段の確保） */
  kibo578: string
  /** 希望5-7_9（家族連絡の確保） */
  kibo579: string
  /** 希望5-7_10（社会活動への支援） */
  kibo5710: string
  /** 要援助計画5-2_1（金銭管理） */
  keikaku521: string
  /** 要援助計画5-2_2（買い物） */
  keikaku522: string
  /** 要援助計画5-2_3（調理） */
  keikaku523: string
  /** 要援助計画5-2_4（準備後始末） */
  keikaku524: string
  /** 要援助計画5-7_1（定期的な相談助言） */
  keikaku571: string
  /** 要援助計画5-7_2（各種書類作成代行） */
  keikaku572: string
  /** 要援助計画5-7_3（余暇活動支援） */
  keikaku573: string
  /** 要援助計画5-7_4（移送外出介助） */
  keikaku574: string
  /** 要援助計画5-7_5（代読代筆） */
  keikaku575: string
  /** 要援助計画5-7_6（話し相手） */
  keikaku576: string
  /** 要援助計画5-7_7（安否確認） */
  keikaku577: string
  /** 要援助計画5-7_8（緊急連絡手段の確保） */
  keikaku578: string
  /** 要援助計画5-7_9（家族連絡の確保） */
  keikaku579: string
  /** 要援助計画5-7_10（社会活動への支援） */
  keikaku5710: string
  /** 緊急連絡見守りの方法 */
  houhouKnj: string
  /** 特記解決すべき課題など */
  memo1Knj: string
}

/**
 * 課題と目標情報
 */
export interface IssuesAndGoalItems {
  /** ID */
  kadaiId: string
  /** アセスメント番号 */
  assNo: string
  /** アセスメント番号名 */
  assKnj: string
  /** 課題 */
  kadaiKnj: string
  /** 長期 */
  choukiKnj: string
  /** 短期 */
  tankiKnj: string
  /** 連番 */
  seq: string
}

/** ［アセスメント］画面（居宅）6⑤保存入力エンティティ */
export interface AssessmentHome65UpdateInEntity extends InWebEntity {
  /** タブID */
  tabId?: string
  /** 機能ID */
  kinoId?: string
  /** 当履歴ページ番号 */
  krirekiNo?: string
  /** e文書用パラメータ */
  edocumentUseParam?: IReportInEntity
  /** e文書削除用パラメータ */
  edocumentDeleteUseParam?: IReportInEntity
  /** 期間対象フラグ */
  kikanFlg?: string
  /** 計画対象期間番号 */
  planningPeriodNo?: string
  /** 開始日 */
  startYmd?: string
  /** 終了日 */
  endYmd?: string
  /** ガイドラインまとめ */
  matomeFlg?: string
  /** ログインID */
  loginId?: string
  /** システム略称 */
  sysRyaku?: string
  /** 職員ID */
  shokuId?: string
  /** システムコード */
  sysCd?: string
  /** 事業者名 */
  svJigyoKnj?: string
  /** 作成者名 */
  createUserName?: string
  /** 利用者名 */
  userName?: string
  /** 法人ID */
  houjinId?: string
  /** 施設ID */
  shisetuId?: string
  /** 利用者ID */
  userId?: string
  /** 事業者ID */
  svJigyoId?: string
  /** 種別ID */
  syubetsuId?: string
  /** 更新区分 */
  updateKbn?: string
  /** 履歴更新区分 */
  historyUpdateKbn?: string
  /** 削除処理区分 */
  deleteProcessKbn?: string
  /** 計画対象期間ID */
  sc1Id?: string
  /** アセスメントID */
  gdlId?: string
  /** 作成日 */
  kijunbiYmd?: string
  /** 作成者ID */
  sakuseiId?: string
  /** 改定フラグ */
  ninteiFormF?: string
  /** 基本動作情報（H21/４改訂版） */
  basicAction4Info?: {
    /** 認定項目5-1（薬の内服） */
    bango51?: string
    /** 認定項目5-2（金銭の管理） */
    bango52?: string
    /** 認定項目5-3（日常の意思決定） */
    bango53?: string
    /** 認定項目5-4（集団への不適応） */
    bango54?: string
    /** 認定項目5-5（買い物） */
    bango55?: string
    /** 認定項目5-6（簡単な調理） */
    bango56?: string
    /** 認定項目5-7（電話の利用） */
    bango57?: string
    /** 認定項目5-8（日中の活動（生活）状況等） */
    bango58?: string
    /** 認定項目5-9（家族居住環境社会参加の状況などの変化） */
    bango59?: string
    /** 家族交流有無 */
    kazokuKouryuUmu?: string
    /** 家族交流内容 */
    kazokuKouryuKnj?: string
    /** 地域交流有無 */
    chiikiKouryuUmu?: string
    /** 地域交流内容 */
    chiikiKouryuKnj?: string
    /** 友人交流有無 */
    yuujinKouryuUmu?: string
    /** 友人交流内容 */
    yuujinKouryuKnj?: string
    /** 家族実施5-2_1（金銭管理） */
    famJisshi521?: string
    /** 家族実施5-2_2（買い物） */
    famJisshi522?: string
    /** 家族実施5-2_3（調理） */
    famJisshi523?: string
    /** 家族実施5-2_4（準備後始末） */
    famJisshi524?: string
    /** 家族実施5-7_1（定期的な相談助言） */
    famJisshi571?: string
    /** 家族実施5-7_2（各種書類作成代行） */
    famJisshi572?: string
    /** 家族実施5-7_3（余暇活動支援） */
    famJisshi573?: string
    /** 家族実施5-7_4（移送外出介助） */
    famJisshi574?: string
    /** 家族実施5-7_5（代読代筆） */
    famJisshi575?: string
    /** 家族実施5-7_6（話し相手） */
    famJisshi576?: string
    /** 家族実施5-7_7（安否確認） */
    famJisshi577?: string
    /** 家族実施5-7_8（緊急連絡手段の確保） */
    famJisshi578?: string
    /** 家族実施5-7_9（家族連絡の確保） */
    famJisshi579?: string
    /** 家族実施5-7_10（社会活動への支援） */
    famJisshi5710?: string
    /** サービス実施5-2_1（金銭管理） */
    serJisshi521?: string
    /** サービス実施5-2_2（買い物） */
    serJisshi522?: string
    /** サービス実施5-2_3（調理） */
    serJisshi523?: string
    /** サービス実施5-2_4（準備後始末） */
    serJisshi524?: string
    /** サービス実施5-7_1（定期的な相談助言） */
    serJisshi571?: string
    /** サービス実施5-7_2（各種書類作成代行） */
    serJisshi572?: string
    /** サービス実施5-7_3（余暇活動支援） */
    serJisshi573?: string
    /** サービス実施5-7_4（移送外出介助） */
    serJisshi574?: string
    /** サービス実施5-7_5（代読代筆） */
    serJisshi575?: string
    /** サービス実施5-7_6（話し相手） */
    serJisshi576?: string
    /** サービス実施5-7_7（安否確認） */
    serJisshi577?: string
    /** サービス実施5-7_8（緊急連絡手段の確保） */
    serJisshi578?: string
    /** サービス実施5-7_9（家族連絡の確保） */
    serJisshi579?: string
    /** サービス実施5-7_10（社会活動への支援） */
    serJisshi5710?: string
    /** 希望5-2_1（金銭管理） */
    kibo521?: string
    /** 希望5-2_2（買い物） */
    kibo522?: string
    /** 希望5-2_3（調理） */
    kibo523?: string
    /** 希望5-2_4（準備後始末） */
    kibo524?: string
    /** 希望5-7_1（定期的な相談助言） */
    kibo571?: string
    /** 希望5-7_2（各種書類作成代行） */
    kibo572?: string
    /** 希望5-7_3（余暇活動支援） */
    kibo573?: string
    /** 希望5-7_4（移送外出介助） */
    kibo574?: string
    /** 希望5-7_5（代読代筆） */
    kibo575?: string
    /** 希望5-7_6（話し相手） */
    kibo576?: string
    /** 希望5-7_7（安否確認） */
    kibo577?: string
    /** 希望5-7_8（緊急連絡手段の確保） */
    kibo578?: string
    /** 希望5-7_9（家族連絡の確保） */
    kibo579?: string
    /** 希望5-7_10（社会活動への支援） */
    kibo5710?: string
    /** 要援助計画5-2_1（金銭管理） */
    keikaku521?: string
    /** 要援助計画5-2_2（買い物） */
    keikaku522?: string
    /** 要援助計画5-2_3（調理） */
    keikaku523?: string
    /** 要援助計画5-2_4（準備後始末） */
    keikaku524?: string
    /** 要援助計画5-7_1（定期的な相談助言） */
    keikaku571?: string
    /** 要援助計画5-7_2（各種書類作成代行） */
    keikaku572?: string
    /** 要援助計画5-7_3（余暇活動支援） */
    keikaku573?: string
    /** 要援助計画5-7_4（移送外出介助） */
    keikaku574?: string
    /** 要援助計画5-7_5（代読代筆） */
    keikaku575?: string
    /** 要援助計画5-7_6（話し相手） */
    keikaku576?: string
    /** 要援助計画5-7_7（安否確認） */
    keikaku577?: string
    /** 要援助計画5-7_8（緊急連絡手段の確保） */
    keikaku578?: string
    /** 要援助計画5-7_9（家族連絡の確保） */
    keikaku579?: string
    /** 要援助計画5-7_10（社会活動への支援） */
    keikaku5710?: string
    /** 緊急連絡見守りの方法 */
    houhouKnj?: string
    /** 特記解決すべき課題など */
    memo1Knj?: string
  }
  /** 基本動作情報（R3/４改訂版） */
  basicAction5Info?: {
    /** 認定項目5-1（薬の内服） */
    bango51?: string
    /** 認定項目5-2（金銭の管理） */
    bango52?: string
    /** 認定項目5-3（日常の意思決定） */
    bango53?: string
    /** 認定項目5-4（集団への不適応） */
    bango54?: string
    /** 認定項目5-5（買い物） */
    bango55?: string
    /** 認定項目5-6（簡単な調理） */
    bango56?: string
    /** 認定項目5-7（電話の利用） */
    bango57?: string
    /** 認定項目5-8（日中の活動（生活）状況等） */
    bango58?: string
    /** 認定項目5-9（家族居住環境社会参加の状況などの変化） */
    bango59?: string
    /** 家族交流有無 */
    kazokuKouryuUmu?: string
    /** 家族交流内容 */
    kazokuKouryuKnj?: string
    /** 地域交流有無 */
    chiikiKouryuUmu?: string
    /** 地域交流内容 */
    chiikiKouryuKnj?: string
    /** 友人交流有無 */
    yuujinKouryuUmu?: string
    /** 友人交流内容 */
    yuujinKouryuKnj?: string
    /** 家族実施5-2_1（金銭管理） */
    famJisshi521?: string
    /** 家族実施5-2_2（買い物） */
    famJisshi522?: string
    /** 家族実施5-2_3（調理） */
    famJisshi523?: string
    /** 家族実施5-2_4（準備後始末） */
    famJisshi524?: string
    /** 家族実施5-7_1（定期的な相談助言） */
    famJisshi571?: string
    /** 家族実施5-7_2（各種書類作成代行） */
    famJisshi572?: string
    /** 家族実施5-7_3（余暇活動支援） */
    famJisshi573?: string
    /** 家族実施5-7_4（移送外出介助） */
    famJisshi574?: string
    /** 家族実施5-7_5（代読代筆） */
    famJisshi575?: string
    /** 家族実施5-7_6（話し相手） */
    famJisshi576?: string
    /** 家族実施5-7_7（安否確認） */
    famJisshi577?: string
    /** 家族実施5-7_8（緊急連絡手段の確保） */
    famJisshi578?: string
    /** 家族実施5-7_9（家族連絡の確保） */
    famJisshi579?: string
    /** 家族実施5-7_10（社会活動への支援） */
    famJisshi5710?: string
    /** サービス実施5-2_1（金銭管理） */
    serJisshi521?: string
    /** サービス実施5-2_2（買い物） */
    serJisshi522?: string
    /** サービス実施5-2_3（調理） */
    serJisshi523?: string
    /** サービス実施5-2_4（準備後始末） */
    serJisshi524?: string
    /** サービス実施5-7_1（定期的な相談助言） */
    serJisshi571?: string
    /** サービス実施5-7_2（各種書類作成代行） */
    serJisshi572?: string
    /** サービス実施5-7_3（余暇活動支援） */
    serJisshi573?: string
    /** サービス実施5-7_4（移送外出介助） */
    serJisshi574?: string
    /** サービス実施5-7_5（代読代筆） */
    serJisshi575?: string
    /** サービス実施5-7_6（話し相手） */
    serJisshi576?: string
    /** サービス実施5-7_7（安否確認） */
    serJisshi577?: string
    /** サービス実施5-7_8（緊急連絡手段の確保） */
    serJisshi578?: string
    /** サービス実施5-7_9（家族連絡の確保） */
    serJisshi579?: string
    /** サービス実施5-7_10（社会活動への支援） */
    serJisshi5710?: string
    /** 希望5-2_1（金銭管理） */
    kibo521?: string
    /** 希望5-2_2（買い物） */
    kibo522?: string
    /** 希望5-2_3（調理） */
    kibo523?: string
    /** 希望5-2_4（準備後始末） */
    kibo524?: string
    /** 希望5-7_1（定期的な相談助言） */
    kibo571?: string
    /** 希望5-7_2（各種書類作成代行） */
    kibo572?: string
    /** 希望5-7_3（余暇活動支援） */
    kibo573?: string
    /** 希望5-7_4（移送外出介助） */
    kibo574?: string
    /** 希望5-7_5（代読代筆） */
    kibo575?: string
    /** 希望5-7_6（話し相手） */
    kibo576?: string
    /** 希望5-7_7（安否確認） */
    kibo577?: string
    /** 希望5-7_8（緊急連絡手段の確保） */
    kibo578?: string
    /** 希望5-7_9（家族連絡の確保） */
    kibo579?: string
    /** 希望5-7_10（社会活動への支援） */
    kibo5710?: string
    /** 要援助計画5-2_1（金銭管理） */
    keikaku521?: string
    /** 要援助計画5-2_2（買い物） */
    keikaku522?: string
    /** 要援助計画5-2_3（調理） */
    keikaku523?: string
    /** 要援助計画5-2_4（準備後始末） */
    keikaku524?: string
    /** 要援助計画5-7_1（定期的な相談助言） */
    keikaku571?: string
    /** 要援助計画5-7_2（各種書類作成代行） */
    keikaku572?: string
    /** 要援助計画5-7_3（余暇活動支援） */
    keikaku573?: string
    /** 要援助計画5-7_4（移送外出介助） */
    keikaku574?: string
    /** 要援助計画5-7_5（代読代筆） */
    keikaku575?: string
    /** 要援助計画5-7_6（話し相手） */
    keikaku576?: string
    /** 要援助計画5-7_7（安否確認） */
    keikaku577?: string
    /** 要援助計画5-7_8（緊急連絡手段の確保） */
    keikaku578?: string
    /** 要援助計画5-7_9（家族連絡の確保） */
    keikaku579?: string
    /** 要援助計画5-7_10（社会活動への支援） */
    keikaku5710?: string
    /** 緊急連絡見守りの方法 */
    houhouKnj?: string
    /** 特記解決すべき課題など */
    memo1Knj?: string
  }

  /** 課題と目標リスト */
  kadaiList: {
    /** id */
    id?: string
    /** アセスメント番号 */
    assNo?: string
    /** 課題 */
    kadaiKnj?: string
    /** 長期 */
    choukiKnj?: string
    /** 短期 */
    tankiKnj?: string
    /** 連番 */
    seq?: string
    /** 更新区分 */
    updateKbn?: string
    /** 更新回数 */
    modifiedCnt?: string
  }[]
}

/**
 * アセスメントマスタ保存出力エンティティ
 */
export interface AssessmentHome65UpdateOutEntity extends OutWebEntity {
  /** data */
  data: {
    /** 計画対象期間ID */
    sc1Id: string
    /** アセスメントID */
    gdlId: string
    /** エラー区分 */
    errKbn: string
  }
}
