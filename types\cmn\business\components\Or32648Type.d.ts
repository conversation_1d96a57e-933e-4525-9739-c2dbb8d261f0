import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
/**
 * Or32648:有機体:制度利用状況
 * TwoWayBind領域に保持するデータ構造
 */
export interface Or32648Type {
  /** 年金 */
  pensionItem: PentionItem
  /** その他チェックボックス */
  otherCheckBoxItem: OtherCheckBoxItem
  /** 成年後見制度 */
  adultObserverSystemItem: AdultObserverSystemItem
  /** 成年後見人等 */
  adultObserverInputValue: Mo00045Type
  /** 健康保険 */
  healthInsurance: HealthInsurance
  /** 労災保険 */
  compensationInsurance: CompensationInsurance
  /** その他 */
  otherItem: OtherItem
  /** 老人保健事業 */
  elderlyHealthProject: ElderlyHealthProject
  /**
   * pinia値初始化フラグ
   */
  refValueInitDisabled?: boolean
}

/**
 * OneWayBind領域
 */
export interface Or32648OnewayType {
  /** 年金ラベル */
  pensionLabel: string
  /** 成年後見人等ラベル */
  textLabel: string
  /**
   * その他または老人保健事業の区分
   * 'other':その他  'elder':老人保健事業
   */
  itemKbn: string
  /** 健康保険ラベル */
  healthInsuranceLable: string
  /** その他ラベル */
  otherLable: string
  /** 老人保健事業ラベル */
  elderlyHealthProjectLabel: string
}

/**
 * 年金
 */
export interface PentionItem {
  /** 年金チェックボックス選択肢 */
  pensionCheckItems: {
    /** チェックモデル */
    checkboxValue: Mo00018Type
    /** onewayモデル */
    onewayModelValue: Mo00018OnewayType
  }[]
  /** 制度利用ﾒﾓ */
  systemUseTextValue: Mo00045Type[]
}

/**
 * その他チェックボックス
 */
export interface OtherCheckBoxItem {
  /** その他チェックボックス選択肢 */
  otherCheckItems: {
    /** チェックモデル */
    checkboxValue: Mo00018Type
    /** onewayモデル */
    onewayModelValue: Mo00018OnewayType
  }[]
}

/**
 * 成年後見制度
 */
export interface AdultObserverSystemItem {
  /** チェックモデル */
  checkboxValue: Mo00018Type
  /** onewayモデル */
  checkboxOnewayModelValue: Mo00018OnewayType
  /** ラジオボタンモデル */
  raidoValue: string
  /** ラジオボタンonewayモデル */
  radioOneway: Mo00039OnewayType
}

/**
 * 健康保険
 */
export interface HealthInsurance {
  /** チェックボックス選択肢 */
  insuranceCheckItems: {
    /** チェックモデル */
    checkboxValue: Mo00018Type
    /** onewayモデル */
    onewayModelValue: Mo00018OnewayType
  }[]
}

/**
 * 労災保険
 */
export interface CompensationInsurance {
  /** チェックボックス選択肢 */
  insuranceCheckItems: {
    /** チェックモデル */
    checkboxValue: Mo00018Type
    /** onewayモデル */
    onewayModelValue: Mo00018OnewayType
  }
  /** 制度利用ﾒﾓ */
  systemUseTextValue: Mo00045Type
}

/**
 * その他
 */
export interface OtherItem {
  /** その他チェックボックス選択肢 */
  otherCheckItems: {
    /** チェックモデル */
    checkboxValue: Mo00018Type
    /** onewayモデル */
    onewayModelValue: Mo00018OnewayType
  }[]
  /** 制度利用ﾒﾓ */
  systemUseTextValue: Mo00045Type[]
}

/**
 * 老人保健事業
 */
export interface ElderlyHealthProject {
  /** チェックボックス選択肢 */
  checkItems: {
    /** チェックモデル */
    checkboxValue: Mo00018Type
    /** onewayモデル */
    onewayModelValue: Mo00018OnewayType
  }[]
}
