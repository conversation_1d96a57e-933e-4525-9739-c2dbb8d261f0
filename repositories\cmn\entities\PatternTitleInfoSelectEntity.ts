/**
 * Or27523:パターン（タイトル）情報取得のエンティティ
 * GUI00996_パターン（タイトル）情報取得
 *
 * <AUTHOR>
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * パターン（タイトル）情報取得出力エンティティ
 */
export interface PatternTitleInfoSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * タイトルリスト(日課表)
     */
    nikkaList: {
      /**
       * 日課表ID
       */
      day1Id: string
      /**
       * タイトル
       */
      day1TitleKnj: string
      /**
       * グループCD
       */
      day1GroupCD: string
      /**
       * 表示順
       */
      day1Seq: string
      /**
       * 更新回数
       */
      day1ModifiedCnt: string
    }[]
    /**
     * タイトルリスト(週間表)
     */
    syukanList: {
      /**
       * 週間表ID
       */
      week1Id: string
      /**
       * タイトル
       */
      week1TitleKnj: string
      /**
       * グループCD
       */
      week1GroupCD: string
      /**
       * 有効期間ID
       */
      termid: string
      /**
       * 有効期間
       */
      termKnj: string
      /**
       * 表示順
       */
      week1Seq: string
      /**
       * 2:H21/4 1:旧様式
       */
      kaiteiFlg: string
      /**
       * 改訂
       */
      kaiteiKnj: string
      /**
       * 更新回数
       */
      week1ModifiedCnt: string
    }[]
    /**
     * タイトルリスト(月間・年間表)
     */
    nenkanList: {
      /**
       * 月間・年間計画ID
       */
      nenkan1Id: string
      /**
       * タイトル
       */
      nenkan1TitleKnj: string
      /**
       * グループCD
       */
      nenkan1GroupCD: string
      /**
       * 表示順
       */
      nenkan1Seq: string
      /**
       * 更新回数
       */
      nenkan1ModifiedCnt: string
    }[]
    /**
     * グループリスト
     */
    groupList: {
      /**
       * 表示順
       */
      seq: string
      /**
       * グループCD
       */
      groupCd: string
      /**
       * グループ名称
       */
      groupKnj: string
      /**
       * マスタ区分
       */
      mstKbn: string
      /**
       * 更新回数
       */
      modifiedCnt: string
    }[]
  }
}

/**
 * パターン（グループ）情報取得入力エンティティ
 */
export interface PatternTitleInfoSelectInEntity extends InWebEntity {
  /**
   * マスタ区分
   */
  mstKbn: string
}
