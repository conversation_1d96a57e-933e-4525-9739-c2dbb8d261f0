<script setup lang="ts">
/**
 * Or35921:週間計画テンプレート
 *
 * @description
 * 週間計画テンプレート
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28778Const } from '../Or28778/Or28778.constants'
import { Or28778Logic } from '../Or28778/Or28778.logic'
import { Or60794Const } from '../Or60794/Or60794.constants'
import { Or60794Logic } from '../Or60794/Or60794.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or26972Const } from '../Or26972/Or26972.constants'
import { Or27730Const } from '../Or27730/Or27730.constants'
import { Or35774Logic } from '../Or35774/Or35774.logic'
import { Or35774Const } from '../Or35774/Or35774.constants'
import type { HistorySelectTableDataItem } from '../Or10929/Or10929.type'
import { Or26972Logic } from '../Or26972/Or26972.logic'
import { Or26447Const } from '../Or26447/Or26447.constants'
import { Or05349Const } from '../Or05349/Or05349.constants'
import { Or27730Logic } from '../Or27730/Or27730.logic'
import { Or10583Const } from '../Or10583/Or10583.constants'
import { Or10583Logic } from '../Or10583/Or10583.logic'
import { Or35921Logic } from './Or35921.logic'
import type { Or35921StateType, Or05349RefFunc } from './Or35921.type'
import type {
  Or60794Type,
  weeklyPlanCopyInfoType,
} from '~/types/cmn/business/components/Or60794Type'
import {
  useJigyoList,
  useScreenOneWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
  useUserListInfo,
} from '#imports'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or35921OnewayType, Or35921Type } from '~/types/cmn/business/components/Or35921Type'
import type {
  kikanObj,
  cks54List,
  cks55List,
  cks56List,
  cks57List,
  cks58List,
  cks52List,
  Or05349OnewayType,
  Or05349Type,
} from '~/types/cmn/business/components/Or05349Type'
import type { History, NichiJyou } from '~/types/cmn/business/components/OrX0066Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import type { OrX0165OnewayType } from '~/types/cmn/business/components/OrX0165Type'
import type { OrX0157OnewayType, OrX0157Type } from '~/types/cmn/business/components/OrX0157Type'
import type {
  WeekPlanEntity,
  WeekPlanSelectOutEntity,
  initType,
  KikanOutDType,
  RirekiOutDType,
} from '~/repositories/cmn/entities/WeekPlanEntity'
import type {
  WeekPlanKikanEntity,
  WeekPlanKikanOutEntity,
} from '~/repositories/cmn/entities/WeekPlanKikanEntity'
import type {
  WeekPlanHistoryEntity,
  WeekPlanHistoryOutEntity,
} from '~/repositories/cmn/entities/WeekPlanHistoryEntity'
import type { WeeklyPlan } from '~/components/custom-components/organisms/Or27610/Or27610.type'
import type {
  Or26972OnewayType,
  Or26972SelectInfoType,
  Or26972Type,
} from '~/types/cmn/business/components/Or26972Type'
import type { Or27730OnewayType } from '~/types/cmn/business/components/Or27730Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or35921Const } from '~/components/custom-components/organisms/Or35921/Or35921.constants'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Or28778OnewayType } from '~/types/cmn/business/components/Or28778Type.d.ts'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { WeekPlanDetailsInfoUpdateEntity } from '~/repositories/cmn/entities/WeekPlanDetailsInfoUpdateEntity'
import { hasViewAuth } from '~/utils/useCmnAuthz'
import type {
  PatternTranDestSelectEntity,
  PatternTranDestSelectOutEntity,
} from '~/repositories/cmn/entities/PatternTranDestSelectEntity'
import { OrX0115Const } from '~/components/custom-components/organisms/OrX0115/OrX0115.constants'
import { OrX0115Logic } from '~/components/custom-components/organisms/OrX0115/OrX0115.logic'
import type { OrX0115EventType } from '~/components/custom-components/organisms/OrX0115/OrX0115.type'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import type { Or35774Type, Or35774OnewayType } from '~/types/cmn/business/components/Or35774Type'
import type {
  CarePlanPrintAllInData,
  Or10583OnewayType,
} from '~/types/cmn/business/components/Or10583Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { Or28238Const } from '~/components/custom-components/organisms/Or28238/Or28238.constants'
import { Or28238Logic } from '~/components/custom-components/organisms/Or28238/Or28238.logic'
import type { Or28238OnewayType } from '~/types/cmn/business/components/Or28238Type'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import type { Or26257OnewayType, Or26257Type } from '~/types/cmn/business/components/Or26257Type'
import type { Mo01352OnewayType, Mo01352Type } from '~/types/business/components/Mo01352Type'
import type {
  WeekPlanSvItemInfoSelectInEntity,
  WeekPlanSvItemInfoSelectOutEntity,
} from '~/repositories/cmn/entities/WeekPlanSvItemInfoSelectEntity'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or05349Type
  onewayModelValue: Or35921OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()
/**
 *利用者選択監視関数を実行
 */
const { syscomUserSelectWatchFunc } = useUserListInfo()
/**
 *子コンポーネント
 */
const { searchUniqCpId } = useScreenUtils()

/**
 * jigyoListWatch
 */
const { jigyoListWatch } = useJigyoList()
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// 閲覧権限
const roleFlg = ref<boolean>(true)
roleFlg.value = await hasViewAuth()
// 期間管理フラグ
const plannningPeriodManageFlg = ref<string>('')
// 新規用有効期間ID
const newValidId = ref<number>(-1)
// 履歴フラグ
const rirekiFlg = ref<number>(-1)

// 計画期間
const planPeriodShow = ref<boolean>(true)
// 履歴表示フラグ
const isHistoryShow = ref<boolean>(true)
// 削除フラグ
const deleteFlag = ref<boolean>(false)
// 臨時有効期間ID
const tempValidId = ref<number>(-1)
// 処理年月日
const syoriYmd = ref<string>('')
// 有効期間ID
const validId = ref<number>(-1)
// 前回の作成日
const createDateBk = ref<string>('')
// モード
const mode = ref<string>('')
// 元開始年月
const motoYmFrom = ref<string>('')
// 元終了年月
const motoYmTo = ref<string>('')
// 先開始年月
const sakiYmFrom = ref<string>('')
// 先終了年月
const sakiYmTo = ref<string>('')
// 初期取得したAPI結果
const initResData = ref<WeekPlanSelectOutEntity>({
  statusCode: '',
  data: {} as unknown as initType,
})

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return true
})

// ローカルTwoway
const local = reactive({
  or35921: {
    ...props.modelValue,
  } as Or05349Type,
  // 新規ボタン押下回数
  addBtnClickCount: 0,
  // 事業所ID
  officeId: '1',
  component: {
    yukokikanId: 0,
    planTargetPeriodId: '0',
    kikanPage: '0',
    periodCurrentIndex: 0,
    periodTotalCount: 0,
    rirekiId: '0',
    rirekiPage: '0',
    rirekiCurrentIndex: 0,
    rirekiTotalCount: 0,
  } as Or35921StateType,
  // 作成者ID
  createUserId: '',
  // 作成者
  createUser: {} as OrX0157Type,
  // 作成日
  createDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  /** 職員検索 */
  or26257: {} as Or26257Type,
  // 処理月
  syoriMonth: {
    value: '',
  } as Mo01352Type,
  cks54: {} as cks54List,
})

// ローカルOneway
const localOneway = reactive({
  uniqueCpId: '',
  parentUniqueCpId: '',
  or35921: {
    ...props.onewayModelValue,
  } as Or35921OnewayType,
  mo00611Oneway: {
    btnLabel: t('label.pattern'),
  } as Mo00611OnewayType,
  or26972Oneway: {
    historySelectInfo: {} as Or26972SelectInfoType,
  } as Or26972OnewayType,
  or22730OnewayModel: {
    cksFlg: '1',
    wscksFlg: 1,
    userId: '1',
    yoKaigoDo: '1',
    cks51List: [] as History[],
    cks52List: [] as cks52List[],
    cks54List: [] as NichiJyou[],
    newCks52List: [] as cks52List[],
    newCks54List: [] as NichiJyou[],
    shuruihyoujiFlg: false,
    defSvJigyoCd: '1',
    jigyoShowFlg: 1,
    komokuShowFlg: false,
    contentShowFlg: false,
  } as Or27730OnewayType,
  or51775OnewayModel: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
  } as Or51775OnewayType,
  orX0115Oneway: {
    kindId: '1',
    sc1Id: '1',
  } as OrX0115OnewayType,
  or10583Oneway: {
    carePlanPrintAllInData: {} as CarePlanPrintAllInData,
    setInputComponentsFlg: false,
  } as Or10583OnewayType,
  or28238Oneway: {
    kikanFlg: '1',
    sysCd: '',
    sysRyaku: '',
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
    userId: '3',
    shokuId: '2',
    sectionName: '1',
    choIndex: '1',
    kss1Id: '',
    loginNumber: '1',
    loginUserType: '1',
    emrLinkFlag: '',
    careManagerInChargeSettingsFlag: 2,
    focusSettingInitial: ['あ'],
    appYmd: '2025/05/05',
    tantoId: '',
    reportId: '***********',
  } as Or28238OnewayType,
  // GUI00220 職員検索画面
  or26257: {
    sysCdKbn: '', // システム略称
    secAccountAllFlg: '0',
    svJigyoIdList: [],
    shokuinId: '',
    gsysCd: '', // システムコード
    selectMode: '12',
    kijunYmd: systemCommonsStore.getSystemDate ?? '',
    defSvJigyoId: '',
    filterDwFlg: '1',
    koyouState: '0',
    areaFlg: '0',
    hyoujiColumnList: [{ hyoujiColumn: 'shokushu_id' }],
    misetteiFlg: '1',
    otherRead: '', // 他職員参照権限
    refStopFlg: '0',
    syoriFlg: '', // 処理フラグ
    menu1Id: '', // メニュー１ID
    kensuFlg: '0',
    shokuinIdList: [], // 職員IDリスト
  } as Or26257OnewayType,
})

const localComponentsOneway = reactive({
  // 計画対象期間タイトルラベル
  orX0165Plan: {
    label: t('label.planning-period'),
  } as OrX0165OnewayType,
  // 履歴タイトルラベル
  orX0165History: {
    label: t('label.history'),
  } as OrX0165OnewayType,

  // 作成者
  orX0157CreateUser: {
    showEditBtnFlg: true,
    editBtnClass: 'custom-edit-btn',
    inputReadonly: true,
    text: {
      orX0157InputOneway: {
        customClass: {
          outerClass: '',
          labelClass: 'mb-1',
        },
        itemLabel: t('label.author'),
        showItemLabel: true,
        isVerticalLabel: true,
        width: '148px',
      },
    },
  } as OrX0157OnewayType,
  // 作成日
  createDateOneway: {
    itemLabel: t('label.create-date'),
    showItemLabel: true,
    isVerticalLabel: true,
    isRequired: true,
    hideDetails: true,
    customClass: {
      outerClass: '',
      labelClass: 'mb-1',
    },
    width: '135',
  } as Mo00020OnewayType,
  // 処理月
  syoriMonthOneway: {
    showNavigationButtons: false,
    textFieldwidth: '115',
  } as Mo01352OnewayType,
})

const or05349OnewayType = ref<Or05349OnewayType>({
  assessmentMethod: localOneway.or35921.assessmentMethod ?? '1',
  houjinId: localOneway.or35921.houjinId ?? '1',
  shisetuId: localOneway.or35921.shisetuId ?? '1',
  svJigyoId: localOneway.or35921.svJigyoId ?? '1',
  userId: systemCommonsStore.getUserSelectSelfId() ?? '1',
  syubetsuId: localOneway.or35921.syubetsuId ?? '1',
  torikomiMoto: '1',
  sys3Ryaku: localOneway.or35921.sys3Ryaku ?? '1',
  cksFlg: '1',
  defSvJigyoCd: localOneway.or35921.defSvJigyoCd ?? '1',
  defSvJigyoId: localOneway.or35921.defSvJigyoId ?? '1',
  shokuinId: localOneway.or35921.shokuinId ?? '1',
  jiOffice: localOneway.or35921.jiOffice ?? '1',
  shuruihyoujiFlg: false,
  jigyoShowFlg: -1,
  komokuShowFlg: false,
  contentShowFlg: false,
  useFlg: '',
  periodTotalCount: -1,
  rirekiId: local.component.rirekiId,
  createDate: local.createDate.value,
  syoriMonth: local.syoriMonth.value,
  isHistoryShow: false,
  deleteFlag: false,
  validId: -1,
  openSourceJudgement: '',
  importCategory: '',
  duplicateCategory: '',
  useSlipSituation: '',
  wp: {} as WeeklyPlan,
  applicableOfficeIdArray: '',
  readonly: false,
})

const or26972Type = ref<Or26972Type>({
  historySelectDataList: [],
})

const or60794Type = ref<Or60794Type>()
const or60794Data: weeklyPlanCopyInfoType = {
  /**
   *期間管理フラグ
   */
  periodManageFlag: Or35921Const.DEFAULT.NUMBER_ZERO,
  /**
   * 有効期間ID
   */
  validPeriodId: 5,
  /**
   * 計画様式
   */
  planningFormat: Or35921Const.DEFAULT.ITAKU,
  /**
   * 週間計画ID
   */
  ks51Id: String(local.component.rirekiId),
  /**
   * 計画期間ID
   */
  periodId: parseInt(local.component.planTargetPeriodId ?? '0'),
}
const or28778Data = ref<Or28778OnewayType>({
  formatFlag: '1',
  officeId: 0,
  facilityId: 0,
  officeDataList: [],
  officeIdList: [],
  officeGroupId: '',
  classification1: Or28778Const.DEFAULT.CLASSIFICATION_1,
  classification2: Or28778Const.DEFAULT.CLASSIFICATION_2,
  carePlanStyle: '1',
  serviceOfficeCode: 50001,
  shisetuId: '1',
  svJigyoId: '1',
  bunrui1Id: '1',
  bunrui2Id: '1',
  tekiyoJigyoInfo: {
    svJigyoId: [],
    tekiyoGroupId: '',
    shokuinId: '',
    gsyscd: '',
    kinouId: '',
    listKbn: '',
    license: [],
    gJigyoId: [],
    opeMonthEnd: '',
  },
})

// 期間
const kikan = ref<OrX0115EventType>({
  kikanId: '',
  kikanIndex: '',
  startYmd: '',
  endYmd: '',
})

const or35774Type: Or35774Type = {
  termid: String(tempValidId.value ?? 1),
  importPartn: 1,
}
const or35774OnewayModel: Or35774OnewayType = {
  wscksFlg: 1,
}
const or35921Model: Or35921Type = {
  jigyoId: '',
  createUserId: '',
  kikanObj: [] as kikanObj[],
  cks52List: [] as cks52List[],
  orignValidId: 1,
}

/**
 * 時間帯データ
 */
const wp = {
  id: '',
  startTime: Or35921Const.DEFAULT.TIME_ZERO,
  endTime: Or35921Const.DEFAULT.TIME_ZERO,
  outFrameDisplay: { modelValue: false },
  dayOfWeek1: false,
  dayOfWeek2: false,
  dayOfWeek3: false,
  dayOfWeek4: false,
  dayOfWeek5: false,
  dayOfWeek6: false,
  dayOfWeek7: false,
  dayOfWeekOther: false,
  contents: '',
  manager: [''],
  frequency: '',
  insuranceType: '',
  insuranceBussiness: '',
  insuranceServices: '',
  insuranceAddition: '',
  fontSize: {
    modelValue: '12',
  },
  fontSizeTitle: {
    value: '12',
  },
  textPosition: {
    modelValue: '0',
  },
  omission: {
    modelValue: 1,
  },
  color: '#333333',
  background: '#E6E6E6',
  timeDisplay: {
    modelValue: 0,
  },
} as WeeklyPlan
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // 利用者を全選択です。
  const uniqueCpId248 = searchUniqCpId(props.uniqueCpId, Or00248Const.CP_ID(0), 0)
  if (uniqueCpId248) {
    const uniqueCpId94 = searchUniqCpId(uniqueCpId248, Or00094Const.CP_ID(0), 0)
    if (uniqueCpId94) {
      Or00094Logic.state.set({
        uniqueCpId: uniqueCpId94,
        state: {
          dispSettingBtnDisplayFlg: true,
          focusSettingFlg: true,
          focusSettingInitial: ['全'],
        },
      })
    }
  }
  // 事業所選択プルダウンの検索条件設定
  // 検索条件未指定だと、ヘッダーで選択した適用事業所が表示される
  // 検索条件が変更されると、共通処理にて事業所の情報が更新される
  // 更新後やプルダウンの値変更後、変更後の事業所に基づいて処理を行う場合、
  // 関数「 jigyoListWatch 」にコールバック関数を渡す
  // 初期情報取得
  localOneway.uniqueCpId = props.uniqueCpId
  localOneway.parentUniqueCpId = props.parentUniqueCpId
  if (systemCommonsStore.getUserSelectSelfId()) {
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }

  // 初期化
  await init()
})

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or35921StateType>({
  cpId: String(Or35921Const.CP_ID),
  uniqueCpId: props.uniqueCpId,
})

const or41179_1 = ref({ uniqueCpId: '' })
const or26972 = ref({ uniqueCpId: Or26972Const.CP_ID(1) })
const orX0115 = ref({ uniqueCpId: '' })
const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const orHeadLine = ref({ uniqueCpId: '' })
const or27730 = ref({ uniqueCpId: '' })
const or35774 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21814_2 = ref({ uniqueCpId: '' })
const or21814_3 = ref({ uniqueCpId: '' })
const or21814_4 = ref({ uniqueCpId: '' })
const or21814_5 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
const or60794 = ref({ uniqueCpId: '' })
const or28778 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })
const or26447 = ref({ uniqueCpId: '' })
const or05349 = ref({ uniqueCpId: '' })
const or10583 = ref({ uniqueCpId: '' })
const or28238 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(0)]: or00248.value,
  [Or41179Const.CP_ID(1)]: or41179_1.value,
  [Or26972Const.CP_ID(1)]: or26972.value,
  [OrX0115Const.CP_ID(0)]: orX0115.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or27730Const.CP_ID(0)]: or27730.value,
  [Or35774Const.CP_ID(0)]: or35774.value,
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or21814Const.CP_ID(3)]: or21814_3.value,
  [Or21814Const.CP_ID(4)]: or21814_4.value,
  [Or21814Const.CP_ID(5)]: or21814_5.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
  [Or60794Const.CP_ID(0)]: or60794.value,
  [Or28778Const.CP_ID(0)]: or28778.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or26447Const.CP_ID(0)]: or26447.value,
  [Or05349Const.CP_ID(0)]: or05349.value,
  [Or10583Const.CP_ID(0)]: or10583.value,
  [Or28238Const.CP_ID(0)]: or28238.value,
  [Or26257Const.CP_ID(1)]: or26257.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [String(Or00248Const.CP_ID)]: or00248.value,
})

const or05349Ref = ref<Or05349RefFunc>({
  initData: (_or05349: Or05349OnewayType) => {},
  doContentSetting: (_or05349: Or05349Type) => {},
})

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.weekly-plan'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: true,
    showOptionMenuBtn: true,
    showOptionMenuDelete: true,
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await _save()
      setOr11871Event({ saveEventFlg: false })
    }
    if (newValue.createEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 新規ボタンが押下された場合、新規作成処理を実行する
        void addBtnClick()
        setOr11871Event({ createEventFlg: false })
      }
    }
    if (newValue.copyEventFlg) {
      await copyBtnClick()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { copyEventFlg: false },
      })
    }
    if (newValue.printEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 印刷ボタンが押下された場合、印刷設定画面を表示する
        setOr11871Event({ printEventFlg: false })
      }
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      void masterIconClick()
      setOr11871Event({ masterEventFlg: false })
    }
    if (newValue.deleteEventFlg) {
      await deleteClick()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { deleteEventFlg: false },
      })
    }
  }
)

/**
 * 利用者選択監視関数に渡すコールバック関数（サブ01の例）
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param _newSelfId - 利用者
 *
 * @param _oldSelfId - 利用者
 */
const callbackFuncSub01 = (_newSelfId: string, _oldSelfId: string) => {
  Or41179Logic.state.set({
    uniqueCpId: or41179_1.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })
  if (systemCommonsStore.getUserSelectSelfId() !== undefined && systemCommonsStore.getUserSelectSelfId() !== ''){
    void init()
  }
}
/**
 * 事業所選択更新の監視
 */
// ★事業所選択監視関数を実行
jigyoListWatch(or41179_1.value.uniqueCpId, callbackFuncJigyo)
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId !== '') {
    //画面データ変更かどうかを判断する
    //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
    // if (isEdit.value) {
    //   const dialogResult = await showConfirmMessgeBox()
    //   if (dialogResult?.firstBtnClickFlg === true) {
    //     await _save()
    //   } else if (dialogResult?.thirdBtnClickFlg === true) {
    //     return
    //   }
    // }
    //共通情報.事業所ＩＤ
    // systemCommonsStore.setSvJigyoId(newJigyoId)
    local.officeId = newJigyoId

    //共通情報.計画期間ＩＤ = 空白
    local.component.planTargetPeriodId = ''
    if (systemCommonsStore.getUserSelectSelfId() !== undefined && systemCommonsStore.getUserSelectSelfId() !== ''){
      void init()
    }
  }
}
// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackFuncSub01)

/**
 * AC014_「計画対象期間選択アイコンボタン」押下の監視
 */
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  async (newValue) => {
    if (newValue) {
      kikan.value = newValue
      // 変数.期間ID
      local.component.planTargetPeriodId = kikan.value.kikanId
      // 計画期間ページ区分
      local.component.kikanPage = Or35921Const.DEFAULT.PAGE_LOCAL
      // データ検索後、AC001-2から実行する
      await getKeikakuInfo()
    }
  }
)

//変更前の年月を保存する変数
const beforeYearMonth = ref('')
watch(
  () => local.syoriMonth,
  () => {
    formatDate()
  }
)

/**
 * 職員検索モーダル閉じる後の処理
 */
watch(
  () => local.or26257,
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    local.createUserId = newValue.shokuin.chkShokuId
    local.createUser.value = `${newValue.shokuin.shokuin1Knj ?? ''} ${newValue.shokuin.shokuin2Knj ?? ''}`
  }
)
/**************************************************
 * 関数
 **************************************************/
/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 *  AC001-1_初期化
 */
async function init() {
  // 閲覧権限
  roleFlg.value = await hasViewAuth()
  // 期間管理フラグ
  plannningPeriodManageFlg.value = ''
  // 新規用有効期間ID
  newValidId.value = -1
  // 履歴フラグ
  rirekiFlg.value = -1
  // 計画期間
  planPeriodShow.value = true
  // 履歴表示フラグ
  isHistoryShow.value = true
  // 削除フラグ
  deleteFlag.value = false
  // 臨時有効期間ID
  tempValidId.value = -1
  // 処理年月日
  syoriYmd.value = ''
  // 有効期間ID
  validId.value = -1
  // 前回の作成日
  createDateBk.value = ''
  // モード
  mode.value = ''
  // 元開始年月
  motoYmFrom.value = ''
  // 元終了年月
  motoYmTo.value = ''
  // 先開始年月
  sakiYmFrom.value = ''
  // 先終了年月
  sakiYmTo.value = ''
  // 初期取得したAPI結果
  initResData.value = {
    statusCode: '',
    data: {} as unknown as initType,
  }

  // 計画対象期間リストが0件のエラーダイアログを初期化(e.cmn.40980)
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40980'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // データ変更確認ダイアログを初期化(i.cmn.10430)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 削除確認ダイアログを初期化(i.cmn.11326)
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11326', [
        local.createDate.value,
        t('label.monthly-yearly-table'),
      ]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })

  // 二回目新規確認ダイアログを初期化(i.cmn.11265)
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.monthly-yearly-table')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // データ変更確認ダイアログを初期化(i.cmn.11310)
  Or21814Logic.state.set({
    uniqueCpId: or21814_4.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11310'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // データ変更確認ダイアログを初期化(i.cmn.21800)
  Or21814Logic.state.set({
    uniqueCpId: or21814_5.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-21800'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // ワーニング用
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20822', [
        motoYmFrom.value,
        motoYmTo.value,
        sakiYmFrom.value,
        sakiYmTo.value,
      ]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })

  // バックエンドAPIから初期情報取得
  const inputData: WeekPlanEntity = {
    //共通情報.取込権限フラグ
    toriAutuFlg: roleFlg.value ? '1' : '0',
    /** 施設ID */
    shisetuId: localOneway.or35921.shisetuId,
    /** 種別ID */
    syubetsuId: localOneway.or35921.syubetsuId,
    /** 事業者ID */
    svJigyoId: localOneway.or35921.svJigyoId ?? '1',
    /** 法人ID */
    houjinId: localOneway.or35921.houjinId,
    /** 利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '1',
    /** 取込元 */
    torikomiMoto: localOneway.or35921.torikomiMoto ?? '1',
    /** 計画対象期間ID */
    sc1Id: '0',
    /** Sys略称 */
    sys3Ryaku: localOneway.or35921.sys3Ryaku ?? '1',
    /** 計画書仕様 */
    cksFlg: localOneway.or35921.cksFlg ?? '1',
    /** 事業所CD */
    defSvJigyoCd: localOneway.or35921.defSvJigyoCd ?? '1',
    /** 事業所ID */
    defSvJigyoId: localOneway.or35921.defSvJigyoId ?? '1',
    /** 職員ID */
    shokuinId: localOneway.or35921.shokuinId ?? '1',
  }

  const resData: WeekPlanSelectOutEntity = await ScreenRepository.select(
    'weekPlanInitialInfoSelect',
    inputData
  )
  initResData.value = resData
  // API取得結果を変数リストに設定
  setKikanVariable(resData)
  setRirekiVariable(resData)
  setDetailVariable(resData)
  setInitDataInfo()

  or05349OnewayType.value.assessmentMethod = localOneway.or35921.assessmentMethod
  or05349OnewayType.value.houjinId = localOneway.or35921.houjinId
  or05349OnewayType.value.shisetuId = localOneway.or35921.shisetuId
  or05349OnewayType.value.svJigyoId = localOneway.or35921.svJigyoId
  or05349OnewayType.value.userId = systemCommonsStore.getUserSelectSelfId() ?? '1'
  or05349OnewayType.value.syubetsuId = localOneway.or35921.syubetsuId
  or05349OnewayType.value.torikomiMoto = localOneway.or35921.torikomiMoto
  or05349OnewayType.value.sys3Ryaku = localOneway.or35921.sys3Ryaku
  or05349OnewayType.value.cksFlg = localOneway.or35921.cksFlg
  or05349OnewayType.value.defSvJigyoCd = localOneway.or35921.defSvJigyoCd
  or05349OnewayType.value.defSvJigyoId = localOneway.or35921.defSvJigyoId
  or05349OnewayType.value.shokuinId = localOneway.or35921.shokuinId
  or05349OnewayType.value.jiOffice = localOneway.or35921.jiOffice
  or05349OnewayType.value.shuruihyoujiFlg = localOneway.or35921.shuruihyoujiFlg
  or05349OnewayType.value.jigyoShowFlg = localOneway.or35921.jigyoShowFlg
  or05349OnewayType.value.komokuShowFlg = localOneway.or35921.komokuShowFlg
  or05349OnewayType.value.contentShowFlg = localOneway.or35921.contentShowFlg
  or05349OnewayType.value.useFlg = localOneway.or35921.useFlg
  or05349OnewayType.value.periodTotalCount = local.component.periodTotalCount
  or05349OnewayType.value.rirekiId = local.component.rirekiId
  or05349OnewayType.value.createDate = local.createDate.value
  or05349OnewayType.value.syoriMonth = local.syoriMonth.value
  or05349OnewayType.value.isHistoryShow = isHistoryShow.value
  or05349OnewayType.value.deleteFlag = deleteFlag.value
  or05349OnewayType.value.validId = validId.value
  or05349OnewayType.value.wp = wp

  or05349Ref.value?.initData(or05349OnewayType)

  doContentSetting()
}

/**
 *  AC001-1-2_変数(計画対象期間)に戻り値を設定する。
 *
 * @param resData - API取得結果
 */
function setKikanVariable(resData: WeekPlanSelectOutEntity) {
  if (resData) {
    // 変数.期間管理フラグ＝期間管理フラグ
    plannningPeriodManageFlg.value = resData.data.kikanFlag
    // 変数.計画対象期間＝計画対象期間情報.計画期間情報リスト
    local.or35921.kikanObj = resData.data.kikanOutD[0].kikanObj
    if (local.or35921.kikanObj.length > 0) {
      // 変数.計画対象期間表示データ
      local.or35921.kikanObjDisp = resData.data.kikanOutD[0].kikanObj.reduce((prev, curr) => {
        return prev.sc1Id > curr.sc1Id ? prev : curr
      })
      // ・変数.計画対象期間ID＝計画対象期間情報.期間ID
      local.component.planTargetPeriodId = local.or35921.kikanObjDisp?.sc1Id
    }
  }
}

/**
 *  AC001-1-3_変数(週間計画履歴)に戻り値を設定する。
 *
 * @param resData - API取得結果
 */
function setRirekiVariable(resData: WeekPlanSelectOutEntity) {
  if (resData) {
    // 変数.新規有効期間ID＝新規用有効期間ID
    newValidId.value = parseInt(resData.data.kikanOutD[0].rirekiOutD[0].termNewId)
    if (local.or35921.kikanObj !== undefined && local.or35921.kikanObj.length > 0) {
      // 変数.週間計画履歴＝計画対象期間情報.履歴変更情報.週間計画履歴リスト
      local.or35921.rirekiObj = resData.data.kikanOutD[0].rirekiOutD[0].rirekiObj.filter(
        (item) => item.sc1Id === local.or35921.kikanObjDisp?.sc1Id
      )
      // 変数.週間計画履歴表示データ
      if (local.or35921.rirekiObj !== undefined && local.or35921.rirekiObj.length > 0) {
        local.or35921.rirekiObjDisp = local.or35921.rirekiObj?.reduce((prev, curr) => {
          return prev.ks51Id > curr.ks51Id ? prev : curr
        })
      }
    }
  }
}

/**
 *  AC001-1-4_変数(詳細情報)に戻り値を設定する。
 *
 * @param resData - API取得結果
 */
function setDetailVariable(resData: WeekPlanSelectOutEntity) {
  if (resData) {
    if (resData.data.kikanOutD[0].rirekiOutD[0].meisaiOutD[0].cks52List) {
      // 変数.週間計画詳細リスト＝計画対象期間情報.履歴変更情報.詳細情報.週間計画詳細リスト
      local.or35921.cks52List =
        resData.data.kikanOutD[0].rirekiOutD[0].meisaiOutD[0].cks52List.filter(
          (item) => item.ks51Id === local.or35921.rirekiObjDisp?.ks51Id
        )
      // 変数.週間計画日常リスト＝計画対象期間情報.履歴変更情報.詳細情報.週間計画日常リスト
      local.or35921.cks54List =
        resData.data.kikanOutD[0].rirekiOutD[0].meisaiOutD[0].cks54List.filter(
          (item) => item.ks51Id === local.or35921.rirekiObjDisp?.ks51Id
        )
      // 変数.週間計画担当リスト＝計画対象期間情報.履歴変更情報.詳細情報.週間計画担当リスト
      local.or35921.cks56List = [] as cks56List[]
      local.or35921.cks55List = [] as cks55List[]
      local.or35921.cks57List = [] as cks57List[]
      local.or35921.cks58List = [] as cks58List[]
      for (const cks52 of local.or35921.cks52List) {
        if (cks52.cks56List !== undefined) {
          for (const cks56 of cks52.cks56List) {
            local.or35921.cks56List.push(cks56)
          }
        }
        if (cks52.cks55List !== undefined) {
          for (const cks55 of cks52.cks55List) {
            local.or35921.cks55List.push(cks55)
          }
        }
        if (cks52.cks57List !== undefined) {
          for (const cks57 of cks52.cks57List) {
            local.or35921.cks57List.push(cks57)
          }
        }
        if (cks52.cks58List !== undefined) {
          for (const cks58 of cks52.cks58List) {
            local.or35921.cks58List.push(cks58)
          }
        }
      }
      // 変数.モード＝計画対象期間情報.履歴変更情報.詳細情報.モード
      mode.value = resData.data.kikanOutD[0].rirekiOutD[0].meisaiOutD[0].mode
    }
  }
}

/**
 *  AC001-2_画面初期情報処理
 */
function setInitDataInfo() {
  // 変数.期間管理フラグが管理の場合
  if (plannningPeriodManageFlg.value === Or35921Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
    // 計画対象期間を表示にする。
    planPeriodShow.value = true
    // 計画対象期間.計画対象期間件数が0の場合
    if (
      local.or35921.kikanObjDisp === undefined ||
      local.or35921.kikanObjDisp?.kikanTotal === '0'
    ) {
      // ・計画対象期間-ページングを"0 / 0"で表示にする。
      // ・計画対象期間を固定文字「計画対象期間アイコンボタンから（改行）期間登録を行ってください」で表示 ※太字、赤色
      localComponentsOneway.orX0165Plan.iconLabel = t('label.planning-period-no-manage')
      localComponentsOneway.orX0165Plan.iconLabelColor = 'rgb(var(--v-theme-red-700))'
      localComponentsOneway.orX0165Plan.pageLabel = '0' + t('label.slash-with-space') + '0'
      local.component.periodCurrentIndex = 0
      local.component.periodTotalCount = 0
      // ・履歴、作成者、作成日、処理月入力フームを非表示にする。
      isHistoryShow.value = false
    } else {
      // ・計画対象期間-ページングを"計画対象期間.計画対象期間インデックス / 計画対象期間.計画対象期間件数"で表示にする。
      // ・計画対象期間を"計画対象期間.開始日～計画対象期間.終了日"で表示 ※太字、黒色
      localComponentsOneway.orX0165Plan.iconLabel =
        local.or35921.kikanObjDisp?.startYmd +
        t('label.wavy-withoutblank') +
        local.or35921.kikanObjDisp?.endYmd
      localComponentsOneway.orX0165Plan.iconLabelColor = undefined
      const kikanIndex = local.or35921.kikanObjDisp?.kikanIndex
      localComponentsOneway.orX0165Plan.pageLabel =
        kikanIndex + t('label.slash-with-space') + local.or35921.kikanObjDisp?.kikanTotal

      local.component.planTargetPeriodId = local.or35921.kikanObjDisp?.sc1Id
      local.component.periodCurrentIndex = parseInt(kikanIndex)
      local.component.periodTotalCount = parseInt(local.or35921.kikanObjDisp?.kikanTotal)
      // ・履歴、作成者、作成日、処理月入力フームを表示にする。
      isHistoryShow.value = true
    }
  } else {
    // 計画対象期間を非表示にする。
    planPeriodShow.value = false
  }
  // AC001-3～AC001-4
  doRirekiSetting()
}

/**
 *  AC001-3～AC001-4
 *
 */
function doRirekiSetting() {
  // 週間計画履歴.履歴総件数が0の場合
  if (
    local.or35921.rirekiObjDisp === undefined ||
    local.or35921.rirekiObjDisp?.rirekiAllCount === '0'
  ) {
    // ・履歴-ページングを"1 / 1"で表示にする。
    local.component.rirekiCurrentIndex = 1
    local.component.rirekiTotalCount = 1
    localComponentsOneway.orX0165History.pageLabel =
      local.component.rirekiCurrentIndex +
      t('label.slash-with-space') +
      local.component.rirekiTotalCount
    // ・変数.履歴フラグを新規でセットする。
    rirekiFlg.value = 2
    // ・変数.有効期間ID＝変数.新規有効期間ID
    validId.value = newValidId.value
    // ・作成日＝共通情報.基準日
    local.createDate.value =
      systemCommonsStore.getSystemDate ??
      new Date().toLocaleDateString().replace(/-/g, Or35921Const.DEFAULT.SLASH)
    createDateBk.value = local.createDate.value
    // ・作成者＝共通情報.職員名
    local.createUserId = systemCommonsStore.getCurrentUser.chkShokuId ?? '0'
    local.createUser.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
  } else {
    // ・履歴-ページングを"週間計画履歴.履歴インデックス / 週間計画履歴.履歴総件数"で表示にする。
    localComponentsOneway.orX0165History.pageLabel =
      local.or35921.rirekiObjDisp?.rirekiIndex +
      t('label.slash-with-space') +
      local.or35921.rirekiObjDisp?.rirekiAllCount
    local.component.rirekiCurrentIndex = parseInt(local.or35921.rirekiObjDisp?.rirekiIndex)
    local.component.rirekiTotalCount = parseInt(local.or35921.rirekiObjDisp?.rirekiAllCount)
    // ・変数.履歴ID＝最大の週間計画履歴.週間計画ID
    local.component.rirekiId = local.or35921.rirekiObjDisp?.ks51Id
    // ・作成日＝最大の週間計画履歴.作成日
    local.createDate.value = local.or35921.rirekiObjDisp?.createYmd
    createDateBk.value = local.createDate.value
    // ・作成者=最大の週間計画履歴.職員IDに応じて職員名
    local.createUserId = local.or35921.rirekiObjDisp?.shokuId ?? '0'
    local.createUser.value = local.or35921.rirekiObjDisp?.shokuKnj ?? ''
    // ・処理月=最大の週間計画履歴.当該年月
    local.syoriMonth.value = local.or35921.rirekiObjDisp?.tougaiYm
    // ・有効期間ID=最大の週間計画履歴.有効期間ID
    local.component.yukokikanId = parseInt(local.or35921.rirekiObjDisp?.termId)
  }

  // 変数.モードが取込の場合
  if (mode.value === '1') {
    // ・変数.文字サイズ＝親画面.文字サイズ
    wp.fontSize.modelValue = localOneway.or35921.fontsize
    // ・変数.表示モード＝親画面.表示モード
    //wp.textPosition.modelValue = localOneway.or35921.dispmode
    // ・変数.文字位置＝親画面.文字位置
    wp.textPosition = { modelValue: localOneway.or35921.textPosition ?? '' }
    // ・変数.文字カラー＝親画面.文字カラー
    wp.color = localOneway.or35921.color ?? ''
    // ・変数.背景カラー＝親画面.背景カラー
    wp.background = localOneway.or35921.backgroundColor ?? ''
    // ・変数.時間表示区分＝親画面.時間表示区分
    //wp.timeDisplay.modelValue = localOneway.or35921.timeDisplay
  }
}

/**
 *  AC001-5～
 */
function doContentSetting() {
  or05349Ref.value?.doContentSetting(local.or35921)
  // 削除処理後の初期化
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      disabledCreateBtn: false,
      disabledPrintBtn: false,
      disabledMasterBtn: false,
      disabledOptionMenuDelete: false,
      disabledCreateMenuCopy: false,
    },
  })
  deleteFlag.value = false
  localComponentsOneway.orX0157CreateUser.text!.orX0157InputOneway.disabled = false
}

/**
 * AC003_「保存ボタン」押下
 */
const _save = async () => {
  // 画面の入力データが変更されない、或いは、計画対象期間.計画対象期間件数が0の場合
  if (!isEdit.value || local.component.periodTotalCount === 0) {
    // ■以下のメッセージを表示
    // i.cmn.21800
    // ・メッセージ内容
    // 「変更されている項目がないため、保存を行うことは出来ません。「改行」
    // 項目を入力変更してから、再度保存を行ってください。」
    await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_21800)
    // ・後続処理
    // OK：処理終了
    return
  }
  // 週間計画履歴.当該年月が空白の場合
  if (local.or35921.rirekiObjDisp?.tougaiYm === '') {
    // 変数.臨時有効期間ID＝週間計画履歴.有効期間ID
    tempValidId.value = parseInt(local.or35921.rirekiObjDisp?.termId)
  } else {
    // 変数.処理年月日＝週間計画履歴.当該年月+"/01"
    syoriYmd.value = local.or35921.rirekiObjDisp?.tougaiYm + Or35921Const.DEFAULT.FIRST_DAY
  }
  // 変数.臨時有効期間ID＝11
  tempValidId.value = 11
  // 変数.処理年月日≦2023/03/31の場合
  if (new Date(syoriYmd.value) <= new Date(Or35921Const.DEFAULT.VALID_1_TO)) {
    // 変数.臨時有効期間ID＝1
    tempValidId.value = 1
  }
  // 2003/04/01≦変数.処理年月日≦2005/09/30の場合
  if (
    new Date(syoriYmd.value) >= new Date(Or35921Const.DEFAULT.VALID_2_FROM) &&
    new Date(syoriYmd.value) <= new Date(Or35921Const.DEFAULT.VALID_2_TO)
  ) {
    // 変数.臨時有効期間ID＝2
    tempValidId.value = 2
  }
  // 2005/10/01≦変数.処理年月日≦2006/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(Or35921Const.DEFAULT.VALID_3_FROM) &&
    new Date(syoriYmd.value) <= new Date(Or35921Const.DEFAULT.VALID_3_TO)
  ) {
    // 変数.臨時有効期間ID＝3
    tempValidId.value = 3
  }
  // 2006/04/01≦変数.処理年月日≦2009/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(Or35921Const.DEFAULT.VALID_4_FROM) &&
    new Date(syoriYmd.value) <= new Date(Or35921Const.DEFAULT.VALID_4_TO)
  ) {
    // 変数.臨時有効期間ID＝4
    tempValidId.value = 4
  }
  // 2009/04/01≦変数.処理年月日≦2012/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(Or35921Const.DEFAULT.VALID_5_FROM) &&
    new Date(syoriYmd.value) <= new Date(Or35921Const.DEFAULT.VALID_5_TO)
  ) {
    // 変数.臨時有効期間ID＝5
    tempValidId.value = 5
  }
  // 2012/04/01≦変数.処理年月日≦2014/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(Or35921Const.DEFAULT.VALID_6_FROM) &&
    new Date(syoriYmd.value) <= new Date(Or35921Const.DEFAULT.VALID_6_TO)
  ) {
    // 変数.臨時有効期間ID＝6
    tempValidId.value = 6
  }
  // 2014/04/01≦変数.処理年月日≦2015/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(Or35921Const.DEFAULT.VALID_7_FROM) &&
    new Date(syoriYmd.value) <= new Date(Or35921Const.DEFAULT.VALID_7_TO)
  ) {
    // 変数.臨時有効期間ID＝7
    tempValidId.value = 7
  }
  // 2015/04/01≦変数.処理年月日≦2017/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(Or35921Const.DEFAULT.VALID_8_FROM) &&
    new Date(syoriYmd.value) <= new Date(Or35921Const.DEFAULT.VALID_8_TO)
  ) {
    // 変数.臨時有効期間ID＝8
    tempValidId.value = 8
  }
  // 2017/04/01≦変数.処理年月日≦2018/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(Or35921Const.DEFAULT.VALID_9_FROM) &&
    new Date(syoriYmd.value) <= new Date(Or35921Const.DEFAULT.VALID_9_TO)
  ) {
    // 変数.臨時有効期間ID＝9
    tempValidId.value = 9
  }
  // 2018/04/01≦変数.処理年月日≦2019/03/31の場合
  if (
    new Date(syoriYmd.value) >= new Date(Or35921Const.DEFAULT.VALID_10_FROM) &&
    new Date(syoriYmd.value) <= new Date(Or35921Const.DEFAULT.VALID_10_TO)
  ) {
    // 変数.臨時有効期間ID＝10
    tempValidId.value = 10
  }
  // 変数.臨時有効期間ID＜＞変数.有効期間ID、且つ、共通情報.計画書様式が居宅「2」の場合
  if (
    tempValidId.value !== validId.value &&
    localOneway.or35921.cksFlg === Or35921Const.DEFAULT.ITAKU
  ) {
    // 週間計画詳細リストのサイズが0ではない、且つ、変数.臨時有効期間ID-変数.有効期間IDが1の場合
    if (local.or35921.data?.dataList?.length !== 0 && tempValidId.value - validId.value === 1) {
      if (tempValidId.value === 2) {
        motoYmFrom.value = ''
        motoYmTo.value = Or35921Const.DEFAULT.VALID_1_TO
        sakiYmFrom.value = Or35921Const.DEFAULT.VALID_2_FROM
        sakiYmTo.value = Or35921Const.DEFAULT.VALID_2_TO
      }
      if (tempValidId.value === 3) {
        motoYmFrom.value = Or35921Const.DEFAULT.VALID_2_FROM
        motoYmTo.value = Or35921Const.DEFAULT.VALID_2_TO
        sakiYmFrom.value = Or35921Const.DEFAULT.VALID_3_FROM
        sakiYmTo.value = Or35921Const.DEFAULT.VALID_3_TO
      }
      if (tempValidId.value === 4) {
        motoYmFrom.value = Or35921Const.DEFAULT.VALID_3_FROM
        motoYmTo.value = Or35921Const.DEFAULT.VALID_3_TO
        sakiYmFrom.value = Or35921Const.DEFAULT.VALID_4_FROM
        sakiYmTo.value = Or35921Const.DEFAULT.VALID_4_TO
      }
      if (tempValidId.value === 5) {
        motoYmFrom.value = Or35921Const.DEFAULT.VALID_4_FROM
        motoYmTo.value = Or35921Const.DEFAULT.VALID_4_TO
        sakiYmFrom.value = Or35921Const.DEFAULT.VALID_5_FROM
        sakiYmTo.value = Or35921Const.DEFAULT.VALID_5_TO
      }
      if (tempValidId.value === 6) {
        motoYmFrom.value = Or35921Const.DEFAULT.VALID_5_FROM
        motoYmTo.value = Or35921Const.DEFAULT.VALID_5_TO
        sakiYmFrom.value = Or35921Const.DEFAULT.VALID_6_FROM
        sakiYmTo.value = Or35921Const.DEFAULT.VALID_6_TO
      }
      if (tempValidId.value === 7) {
        motoYmFrom.value = Or35921Const.DEFAULT.VALID_6_FROM
        motoYmTo.value = Or35921Const.DEFAULT.VALID_6_TO
        sakiYmFrom.value = Or35921Const.DEFAULT.VALID_7_FROM
        sakiYmTo.value = Or35921Const.DEFAULT.VALID_7_TO
      }
      if (tempValidId.value === 8) {
        motoYmFrom.value = Or35921Const.DEFAULT.VALID_7_FROM
        motoYmTo.value = Or35921Const.DEFAULT.VALID_7_TO
        sakiYmFrom.value = Or35921Const.DEFAULT.VALID_8_FROM
        sakiYmTo.value = Or35921Const.DEFAULT.VALID_8_TO
      }
      if (tempValidId.value === 9) {
        motoYmFrom.value = Or35921Const.DEFAULT.VALID_8_FROM
        motoYmTo.value = Or35921Const.DEFAULT.VALID_8_TO
        sakiYmFrom.value = Or35921Const.DEFAULT.VALID_9_FROM
        sakiYmTo.value = Or35921Const.DEFAULT.VALID_9_TO
      }
      if (tempValidId.value === 10) {
        motoYmFrom.value = Or35921Const.DEFAULT.VALID_9_FROM
        motoYmTo.value = Or35921Const.DEFAULT.VALID_9_TO
        sakiYmFrom.value = Or35921Const.DEFAULT.VALID_10_FROM
        sakiYmTo.value = Or35921Const.DEFAULT.VALID_10_TO
      }
      if (tempValidId.value === 11) {
        motoYmFrom.value = Or35921Const.DEFAULT.VALID_10_FROM
        motoYmTo.value = Or35921Const.DEFAULT.VALID_10_TO
        sakiYmFrom.value = Or35921Const.DEFAULT.VALID_11_FROM
        sakiYmTo.value = ''
      }
      // ■以下のメッセージを表示
      // id: w.cmn.20822
      // ・メッセージ内容
      // サービスの有効期間を[改行]
      // {0} ～ {1}[改行]
      // から[改行]
      // {2} ～ {3}[改行]
      // に変更します。サービスによっては名称、単価等が変更される場合があります。[改行]
      // 変更しますか？[改行]
      // [改行]
      const dialogResult = await openWarnDialog()
      switch (dialogResult) {
        case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
          // はい：処理継続
          break
        case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
          // いいえ：以降の処理を行わない。
          return
      }
    } else {
      // ■以下のメッセージを表示
      // id: i.cmn.11310
      // ・メッセージ内容
      // サービスが登録されている履歴は有効期間を変更することはできません。
      // 変更する場合はサービスが登録されていない状態で変更してください。
      await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_11310)
      // OK：以降の処理を行わない。
      return
    }
    // 変数.有効期間ID=変数.臨時有効期間ID
    validId.value = tempValidId.value
  }
  // 主な日常生活一覧より繰り返し、週間計画日常リストをリセットする。
  local.or35921.cks54List = [] as cks54List[]
  let count = 1
  if (local.or35921.njModelArr === undefined || local.or35921.njModelArr.length === 0) {
    local.or35921.cks54List?.push({
      /** 週間計画ID */
      ks51Id: '',
      /** 日常データID */
      ks54Id: '',
      /** 連番 */
      seq: '',
      /** 主な日常生活上の活動 */
      nichijoKnj: '',
    })
  } else {
    for (const str of local.or35921.njModelArr) {
      // 週間計画ID
      local.cks54.ks51Id = String(local.component.rirekiId)
      // ・週間計画日常リスト.連番=1から連番
      local.cks54.seq = String(count)
      // ・週間計画日常リスト.主な日常生活上の活動=主な日常生活内容ラベル
      local.cks54.nichijoKnj = str.value
      local.or35921.cks54List?.push(local.cks54)
      count = count + 1
    }
  }
  const weekPlanDetailsInfoUpdateData: WeekPlanDetailsInfoUpdateEntity = {
    /** 法人ID */
    houjinId: localOneway.or35921.houjinId ?? '1',
    /** 施設ID */
    shisetuId: localOneway.or35921.shisetuId ?? '1',
    /** 利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '1',
    /** 事業者ID */
    svJigyoId: localOneway.or35921.svJigyoId ?? '1',
    /** 種別ID */
    syubetsuId: localOneway.or35921.syubetsuId ?? '1',
    /** 期間対象フラグ */
    lbKikan: plannningPeriodManageFlg.value,
    /** 履歴フラグ */
    rirekiFlg: String(rirekiFlg.value ?? '-1'),
    /** 担当者退避IDリスト */
    deleteTanIdList: [
      {
        /** 詳細データID */
        ks52Id: '',
        /** 担当者データID */
        deleteTanId: '',
      },
    ],
    /** 加算退避IDリスト */
    deleteKasanIdList: [
      {
        /** 詳細データID */
        ks52Id: '',
        /** 加算データID */
        deleteKasanId: '',
      },
    ],
    /** 隔週退避IDリスト */
    deleteTukihiIdList: [
      {
        /** 詳細データID */
        ks52Id: '',
        /** 隔週データID */
        deleteTukihiId: '',
      },
    ],
    /** 連動項目退避IDリスト */
    deleteLinkedItemIdList: [
      {
        /** 詳細データID */
        ks52Id: '',
        /** 連動項目データID */
        deleteLinkedItemId: '',
      },
    ],
    /** 計画期間ID */
    sc1Id: local.component.planTargetPeriodId ?? '0',
    /** 週間計画履歴リスト */
    cks51List: [
      {
        /** 週間計画ID */
        ks51Id: local.or35921.rirekiObjDisp?.ks51Id ?? '',
        /** 当該年月 */
        tougaiYm: local.or35921.rirekiObjDisp?.tougaiYm ?? '',
        /** 作成日 */
        createYmd: local.or35921.rirekiObjDisp?.createYmd ?? '',
        /** 有効期間ID */
        termId: local.or35921.rirekiObjDisp?.termId ?? '',
        /** 週単位以外ｻｰﾋﾞｽ */
        wIgaiKnj: local.or35921.rirekiObjDisp?.wIgaiKnj ?? '',
        /** 職員ID */
        shokuId: local.or35921.rirekiObjDisp?.shokuId ?? '',
      },
    ],
    /** 週間計画日常リスト */
    cks54List: local.or35921.cks54List ?? [
      {
        /** 週間計画ID */
        ks51Id: '',
        /** 日常データID */
        ks54Id: '',
        /** 連番 */
        seq: '',
        /** 主な日常生活上の活動 */
        nichijoKnj: '',
      },
    ],
    /** 週間計画詳細リスト */
    cks52List: local.or35921.cks52List ?? [
      {
        /** ダミーデータID */
        dmyKs52Id: '',
        /** ダミーサービス項目 */
        dmySvItem: '',
        /** wN1 */
        wN1: '',
        /** wN2 */
        wN2: '',
        /** wN3 */
        wN3: '',
        /** wN4 */
        wN4: '',
        /** wN5 */
        wN5: '',
        /** wN6 */
        wN6: '',
        /** wN7 */
        wN7: '',
        /** wN8 */
        wN8: '',
        /** wN9 */
        wN9: '',
        /** ダミー頻度 */
        dmyHindo: '',
        /** 週間計画ID */
        ks51Id: '',
        /** 詳細データID */
        ks52Id: '',
        /** 曜日 */
        youbi: '',
        /** 開始時間 */
        kaishiJikan: '',
        /** 終了時間 */
        shuuryouJikan: '',
        /** 内容 */
        naiyouKnj: '',
        /** 文字サイズ */
        fontSize: '',
        /** 表示モード */
        dispMode: '',
        /** 文字位置 */
        alignment: '',
        /** サービス種類 */
        svShuruiCd: '',
        /** サービス項目（台帳） */
        svItemCd: '',
        /** サービス事業者ID */
        svJigyoId: '',
        /** サービス種類名称 */
        svShuruiKnj: '',
        /** サービス項目名称 */
        svItemKnj: '',
        /** サービス事業者名称 */
        svJigyoKnj: '',
        /** サービス事業者略称 */
        svJigyoRyaku: '',
        /** 文字カラー */
        fontColor: '',
        /** 背景カラー */
        backColor: '',
        /** 時間表示区分 */
        timeKbn: '',
        /** 週単位以外のｻｰﾋﾞｽ区分 */
        igaiKbn: '',
        /** 週単位以外文字 */
        igaiMoji: '',
        /** 週単位以外のｻｰﾋﾞｽ（日付指定） */
        igaiDate: '',
        /** 週単位以外のｻｰﾋﾞｽ（曜日指定） */
        igaiWeek: '',
        /** 福祉用具貸与の単価 */
        svTani: '',
        /** 福祉用具貸与マスタID */
        fygId: '',
        /** 枠外表示するかのフラグ */
        wakugaiFlg: '',
        /** 週間計画隔週リスト */
        cks58List: [
          {
            /** 隔週データのプライマリID */
            ks58Id: '',
            /** 詳細データID */
            ks52Id: '',
            /** 隔週基準年月日 */
            kakusyuuYmd: '',
            /** 隔週週間隔 */
            kakusyuuKankaku: '',
            /** 曜日区分 */
            youbi: '',
          },
        ],
        /** 週間計画加算リスト */
        cks55List: [
          {
            /** 加算データID */
            ks55Id: '',
            /** 詳細データID */
            ks52Id: '',
            /** 加算サービス事業者ID */
            svJigyoId: '',
            /** 加算サービス項目ID */
            svItemCd: '',
            /** 回数 */
            kaisuu: '',
            /** 福祉用具貸与の単価 */
            svTani: '',
            /** 福祉用具貸与マスタID */
            fygId: '',
          },
        ],
        /** 週間計画担当者リスト */
        cks56List: [
          {
            /** 担当者データID */
            ks56Id: '',
            /** 詳細データID */
            ks52Id: '',
            /** 担当者名称 */
            shokushuKnj: '',
            /** 職種 */
            shokushuId: '',
          },
        ],
        /** 週間計画月日リスト */
        cks57List: [
          {
            /** カウンター */
            ks57Id: '',
            /** 詳細データID */
            ks52Id: '',
            /** 月日指定_開始日 */
            startYmd: '',
            /** 月日指定_終了日 */
            endYmd: '',
          },
        ],
        /** 更新区分 */
        updateKbn: '',
      },
    ],
  }
  // 週間計画詳細情報を保存する。
  await ScreenRepository.update('weekPlanDetailsInfoUpdate', weekPlanDetailsInfoUpdateData)
  // 新規押下初期化
  local.addBtnClickCount = 0

  // 週間計画履歴取得
  // AC001-1-3から実行する。
  await getRirekiInfo(false)
}

/**
 * AC004_「新規ボタン」押下
 */
const addBtnClick = async () => {
  local.addBtnClickCount += 1

  // 期間管理フラグが「1:管理する」
  if (plannningPeriodManageFlg.value === Or35921Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
    // 計画期間情報.期間総件数 = 0（期間なし）
    if (local.component.periodTotalCount === 0) {
      openErrorDialog()
      // 「対象期間」画面をポップアップで起動する。
      return
    }
  }
  // 新規データが未保存の場合
  if (local.addBtnClickCount >= 2) {
    await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_11265)
    local.addBtnClickCount = 0
    return
  }
  // 編集データが未保存の場合
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC003を実行
        await _save()
        return
      case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return
    }
  }
  // 共通情報.サービス職種ID OR 種類表示フラグが計画書（２）を取込の場合
  if (localOneway.or35921.shuruihyoujiFlg) {
    // 週間計画の新規取込情報を取得する。
    local.component.rirekiPage = '3'
    await getRirekiInfo(false)
  }
  // ・作成日＝共通情報.基準日
  local.createDate.value =
    systemCommonsStore.getSystemDate ??
    new Date().toLocaleDateString().replace(/-/g, Or35921Const.DEFAULT.SLASH)
  // ・作成者＝共通情報.職員名
  local.createUserId = systemCommonsStore.getCurrentUser.chkShokuId ?? '0'
  local.createUser.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
  // ・履歴-ページング＝履歴-ページングのインデックス + 1+" / "+履歴-ページングのインデックスの総件数+1で表示にする。
  local.component.rirekiCurrentIndex = local.component.rirekiTotalCount
    ? local.component.rirekiTotalCount + 1
    : 1
  local.component.rirekiTotalCount = local.component.rirekiTotalCount
    ? local.component.rirekiTotalCount + 1
    : 1
  localComponentsOneway.orX0165History.pageLabel =
    local.component.rirekiCurrentIndex +
    t('label.slash-with-space') +
    local.component.rirekiTotalCount
  // ・変数.履歴フラグを新規「2」でセットする。
  rirekiFlg.value = 2
  // ・変数.文字サイズ＝親画面.文字サイズ
  wp.fontSize.modelValue = localOneway.or35921.fontsize
  // ・変数.表示モード＝親画面.表示モード
  //wp.textPosition.modelValue = localOneway.or35921.dispmode
  // ・変数.文字位置＝親画面.文字位置
  wp.textPosition = { modelValue: localOneway.or35921.textPosition ?? '' }
  // ・変数.文字カラー＝親画面.文字カラー
  wp.color = localOneway.or35921.color ?? ''
  // ・変数.背景カラー＝親画面.背景カラー
  wp.background = localOneway.or35921.backgroundColor ?? ''
  // ・変数.時間表示区分＝親画面.時間表示区分
  //wp.timeDisplay.modelValue = localOneway.or35921.timeDisplay
  // ・変数.枠外表示するかのフラグ＝FALSE
  wp.outFrameDisplay = { modelValue: false }
}

/**
 * AC005_「複写ボタン」押下
 */
const copyBtnClick = async () => {
  // GUI01046_週間計画複写をポップアップで起動する。
  or60794Data.periodManageFlag = Or35921Const.DEFAULT.PLANNING_PERIOD_NO_MANAGE
  // 週間計画ID
  or60794Data.ks51Id = String(local.component.rirekiId)
  // 計画期間ID
  or60794Data.periodId = parseInt(local.component.planTargetPeriodId ?? '0')
  // 引継情報.計画書様式を設定する。
  // Or60794のダイアログ開閉状態を更新する
  Or60794Logic.state.set({
    uniqueCpId: or60794.value.uniqueCpId,
    state: { isOpen: true },
  })
  // 週間計画の複写データを取得する
  const inputData: WeekPlanEntity = {
    //共通情報.取込権限フラグ
    toriAutuFlg: roleFlg.value ? '1' : '0',
    /** 利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    /** 事業者ID */
    svJigyoId: localOneway.or35921.svJigyoId ?? '1',
    /** 計画対象期間ID */
    sc1Id: String(local.component.planTargetPeriodId),
    /** 週間計画ID */
    ks51Id: String(local.component.rirekiCurrentIndex),
    /** 事業所CD */
    defSvJigyoCd: localOneway.or35921.defSvJigyoCd ?? '1',
    /** 事業所ID */
    defSvJigyoId: localOneway.or35921.defSvJigyoId ?? '1',
    /** 職員ID */
    shokuinId: localOneway.or35921.shokuinId ?? '1',
    /** Sys略称 */
    sys3Ryaku: localOneway.or35921.sys3Ryaku ?? '1',
    /** 計画書様式 */
    cksFlg: localOneway.or35921.cksFlg ?? '1',
    /** 期間管理フラグ */
    kikanFlag: plannningPeriodManageFlg.value,
    /** 現在有効期間ID */
    termid: String(tempValidId.value ?? 1),
    /** 取込元 */
    torikomiMoto: localOneway.or35921.torikomiMoto ?? '1',
  }
  const resData: WeekPlanSelectOutEntity = await ScreenRepository.select(
    'weekPlanDetailsListSelect',
    inputData
  )
  // 複写データより、変数.履歴IDに応じて情報を上書きます。
  if (or60794Type.value !== undefined) {
    local.component.rirekiId = or60794Type.value.weeklyPlanHistorySelectInfo.historyId
  }
  // AC001-1-4から実行する
  setDetailVariable(resData)
  setInitDataInfo()
  doContentSetting()
  emit('update:modelValue', Or35921Logic.data.get(props.uniqueCpId))
}

/**
 * AC007_「印刷」押下
 */
const printClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC003を実行
        await _save()
        return
      case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return
    }
  }
  // GUI01054_印刷設定画をポップアップで起動する
  Or28238Logic.state.set({
    uniqueCpId: or28238.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC008_「計画書一括印刷」押下
 */
const printKeikakuClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC003を実行
        await _save()
        return
      case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return
    }
  }
  // GUI00936_計画書一括印刷画面をポップアップで起動する
  Or10583Logic.state.set({
    uniqueCpId: or10583.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC009_「マスタ他設定アイコンボタン」押下
 */
const masterIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC003を実行
        await _save()
        return
      case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return
    }
  }
  // GUI01037_週間計画マスタ画面をポップアップで起動する。
  Or28778Logic.state.set({
    uniqueCpId: or28778.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC011_「パターン」押下
 */
const pattern = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: PatternTranDestSelectEntity = {
    svKbn: String(parseInt(localOneway.or35921.cksFlg ?? '') - 1),
  }

  const resData: PatternTranDestSelectOutEntity = await ScreenRepository.select(
    'patternTranDestSelect',
    inputData
  )

  if (resData) {
    // 戻り値.データ件数が0ではない場合
    if (resData.data.cnt !== '0') {
      // GUI01044_週間計画パターン（設定）画面をポップアップで起動する
      // 変数.計画対象期間リスト
      or35921Model.kikanObj = local.or35921.kikanObj
      // 親画面.事業者ID
      or35921Model.jigyoId = localOneway.or35921.svJigyoId
      // 親画面.利用者ID
      or35921Model.createUserId = systemCommonsStore.getUserSelectSelfId() ?? '1'
      // 変数.週間計画詳細リスト
      or35921Model.cks52List = local.or35921.cks52List
      // 変数.有効期間ID
      or35921Model.orignValidId = validId.value
      // 週間サービス計画表様式
      or35774OnewayModel.wscksFlg = 1
      // Or35774のダイアログ開閉状態を更新する
      Or35774Logic.state.set({
        uniqueCpId: or35774.value.uniqueCpId,
        state: { isOpen: true },
      })

      // 週間計画パターン取込データを取得する
      const inputData: WeekPlanEntity = {
        //共通情報.取込権限フラグ
        toriAutuFlg: roleFlg.value ? '1' : '0',
        /** 利用者ID */
        userId: systemCommonsStore.getUserSelectSelfId() ?? '1',
        /** 事業者ID */
        svJigyoId: localOneway.or35921.svJigyoId ?? '1',
        /** 計画対象期間ID */
        sc1Id: String(local.component.planTargetPeriodId),
        /** 週間計画ID */
        ks51Id: or35774Type.termid,
        /** 事業所CD */
        defSvJigyoCd: localOneway.or35921.defSvJigyoCd ?? '1',
        /** 事業所ID */
        defSvJigyoId: localOneway.or35921.defSvJigyoId ?? '1',
        /** 職員ID */
        shokuinId: localOneway.or35921.shokuinId ?? '1',
        /** Sys略称 */
        sys3Ryaku: localOneway.or35921.sys3Ryaku ?? '1',
        /** 計画書様式 */
        cksFlg: localOneway.or35921.cksFlg ?? '1',
        /** 期間管理フラグ */
        kikanFlag: plannningPeriodManageFlg.value,
        /** 現在有効期間ID */
        termid: String(tempValidId.value ?? 1),
        /** 取込元 */
        torikomiMoto: localOneway.or35921.torikomiMoto ?? '1',
      }
      const weekPlanDetailsListSelectResData: WeekPlanSelectOutEntity =
        await ScreenRepository.select('weekPlanDetailsListSelect', inputData)
      // ポップアップ画面の返却情報.取込パタンが上書き「1」の場合
      if (or35774Type.importPartn === 1) {
        local.or35921.cks54List?.splice(0)
        local.or35921.cks52List?.splice(0)
      }
      weekPlanDetailsListSelectResData.data.kikanOutD[0].rirekiOutD[0].meisaiOutD[0].cks52List.map(
        (item) => {
          local.or35921.cks52List?.push(item)
        }
      )
      weekPlanDetailsListSelectResData.data.kikanOutD[0].rirekiOutD[0].meisaiOutD[0].cks54List.map(
        (item) => {
          local.or35921.cks54List?.push(item)
        }
      )
      // 週間計画詳サービス項目ID情報取得（利用票）実行する。
      const repData: WeekPlanSvItemInfoSelectInEntity = {
        /** 該当有効期間ID */
        gaiTermid: String(validId.value),
        /** 有効期間ID */
        termid: String(or35921Model.orignValidId),
        /** 処理日 */
        tougaiYm: local.syoriMonth.value,
        /** 週間計画詳細リスト */
        cks52List: local.or35921.cks52List ?? ([] as unknown as cks52List[]),
      }
      const resData: WeekPlanSvItemInfoSelectOutEntity = await ScreenRepository.select(
        'weekPlanSvItemInfoSelect',
        repData
      )
      local.or35921.cks52List = resData.data.cks52List
      // AC001-5から実行する
      doContentSetting()
    } else {
      // GUI01045_週間計画パターンタイトル画面をポップアップで起動する
      // 計画書様式
      localOneway.or22730OnewayModel.cksFlg = localOneway.or35921.cksFlg ?? '1'
      // Or27730のダイアログ開閉状態を更新する
      Or27730Logic.state.set({
        uniqueCpId: or27730.value.uniqueCpId,
        state: { isOpen: true },
      })
    }
  }
}

/**
 * AC012_「削除」押下
 */
const deleteClick = async () => {
  //共通処理 機能の使用権限をチェックする。

  //削除確認（）
  const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_11326)
  switch (dialogResult) {
    case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
      // はい：処理継続
      break
    case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
      // いいえ：処理終了
      return
  }
  // 変数.履歴フラグを削除にセットする。
  rirekiFlg.value = 1

  // 以下の項目を非活性にする
  // 「新規」ボタン、「複写」メニュー、「パターン」メニュー、「印刷設定アイコン」ボタン、「削除」メニュー
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      disabledCreateBtn: true,
      disabledPrintBtn: true,
      disabledMasterBtn: true,
      disabledOptionMenuDelete: true,
      disabledCreateMenuCopy: true,
    },
  })

  deleteFlag.value = true
  localComponentsOneway.orX0157CreateUser.text!.orX0157InputOneway.disabled = true
  // ・以下の項目の内容をクリアし、非表示にする。
  // 週間計画一覧
  local.or35921.cks52List?.splice(0)
  // 主な日常生活一覧
  local.or35921.cks54List?.splice(0)
  // 週単位以外のサービス
  local.or35921.shuUnitOtherService = ''
  // 保険のサービス
  local.or35921.hokenService = ''
}

/**
 * 文字列の形式の判定
 * YYYY/MM/DD または YYYY/MM の形式であるか照合する
 *
 * @param str -照合対象の文字列
 */
function isValidDateFormat(str: string): boolean {
  // 正規表現(YYYY/MM/DD or YYYY/MM)
  const regex = /^(?:\d{4})\/(?:\d{2})\/(?:\d{2})$|^(?:\d{4})\/(?:\d{2})$/

  // 正規表現と指定された文字列を照合
  return regex.test(str)
}

/**
 * YYYY/MM/DD形式をYYYY/MM形式に変更
 */
function formatDate() {
  if (isValidDateFormat(local.syoriMonth.value)) {
    const date = new Date(local.syoriMonth.value)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, Or35921Const.DEFAULT.NUMBER_ZERO)

    local.syoriMonth.value = `${year}/${month}`

    beforeYearMonth.value = local.syoriMonth.value
  } else {
    local.syoriMonth.value = beforeYearMonth.value
  }
}

/**
 * 選択前の履歴から変更
 *
 * @param selectItem - 選択の履歴
 */
const historySelectChange = async (selectItem: HistorySelectTableDataItem | undefined) => {
  // 選択前の履歴から変更がない場合
  if (!selectItem) {
    // 処理終了にする。
    return
  }
  // 選択前の履歴から変更がある場合
  else {
    // 画面履歴変更処理
    local.component.rirekiId = String(selectItem.raiId)
    await getRirekiInfo(true)
  }
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msgType - message TYPE
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(msgType: string): Promise<string> {
  let tempUniqueCpId = ''

  if (msgType === Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430) {
    //編集破棄ダイアログ表示(i.cmn.10430)
    tempUniqueCpId = or21814_1.value.uniqueCpId
  } else if (msgType === Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_11326) {
    //削除確認ダイアログ表示（i.cmn.11326）
    tempUniqueCpId = or21814_2.value.uniqueCpId
  } else if (msgType === Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_11265) {
    //新規ボタン二回目確認ダイアログ表示（i.cmn.11265）
    tempUniqueCpId = or21814_3.value.uniqueCpId
  } else if (msgType === Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_11310) {
    //確認ダイアログ表示（i.cmn.11310）
    tempUniqueCpId = or21814_4.value.uniqueCpId
  } else if (msgType === Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_21800) {
    //確認ダイアログ表示（i.cmn.21800）
    tempUniqueCpId = or21814_5.value.uniqueCpId
  }

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: tempUniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(tempUniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(tempUniqueCpId)

        let result = Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or35921Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or35921Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: tempUniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログ表示（i.cmn.20822）
 *
 * @returns ダイアログの選択結果（yes, no）
 */
async function openWarnDialog(): Promise<string> {
  // 警告ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 警告ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)

        let result = Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or35921Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or35921Const.DEFAULT.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログ表示
 */
function openErrorDialog() {
  // 確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 *  AC014_期間選択ボタン押下時の処理
 */
async function onClickDialogKeikaku() {
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC003を実行
        await _save()
        return
      case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return
    }
  }
  localOneway.orX0115Oneway.kindId = localOneway.or35921.syubetsuId ?? ''
  localOneway.orX0115Oneway.sc1Id = String(local.component.planTargetPeriodId)
  OrX0115Logic.state.set({
    uniqueCpId: orX0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  AC015_計画対象期間-前へアイコンボタンボタン押下時の処理
 */
async function onClickMo00009TwowayKeikaku() {
  // 1件目の計画対象期間データが表示されている状態
  if (
    local.component.periodCurrentIndex === undefined ||
    local.component.periodCurrentIndex === 1
  ) {
    return
  }
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC003を実行
        await _save()
        return
      case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return
    }
  }
  // 計画期間ページ区分
  local.component.kikanPage = Or35921Const.DEFAULT.PAGE_BEFORE

  // データ検索後、AC001-2から実行する
  await getKeikakuInfo()
}

/**
 *  AC016_計画対象期間-計画対象期間-計画対象期間-前へアイコンボタンボタン押下時の処理
 *
 */
async function onClickMo00009ThreewayKeikaku() {
  // 1件目の計画対象期間データが表示されている状態
  if (
    local.component.periodCurrentIndex === undefined ||
    local.component.periodCurrentIndex === local.component.periodTotalCount
  ) {
    return
  }
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC003を実行
        await _save()
        return
      case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return
    }
  }
  // 計画期間ページ区分
  local.component.kikanPage = Or35921Const.DEFAULT.PAGE_AFTER

  // データ検索後、AC001-2から実行する
  await getKeikakuInfo()
}

/**
 *  計画対象期間変更のデータ取得処理
 */
async function getKeikakuInfo() {
  // バックエンドAPIから初期情報取得
  const inputData: WeekPlanKikanEntity = {
    //共通情報.取込権限フラグ
    toriAutuFlg: roleFlg.value ? '1' : '0',
    /** 施設ID */
    shisetuId: localOneway.or35921.shisetuId ?? '1',
    /** 種別ID */
    syubetsuId: localOneway.or35921.syubetsuId ?? '1',
    /** 事業者ID */
    svJigyoId: localOneway.or35921.svJigyoId ?? '1',
    /** 期間管理フラグ */
    kikanFlag: plannningPeriodManageFlg.value,
    /** 利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '1',
    /** 取込元 */
    torikomiMoto: localOneway.or35921.torikomiMoto ?? '1',
    /** 計画対象期間ID */
    sc1Id: String(local.component.planTargetPeriodId),
    /** Sys略称 */
    sys3Ryaku: localOneway.or35921.sys3Ryaku ?? '1',
    /** 計画書仕様 */
    cksFlg: localOneway.or35921.cksFlg ?? '1',
    /** 事業所CD */
    defSvJigyoCd: localOneway.or35921.defSvJigyoCd ?? '1',
    /** 事業所ID */
    defSvJigyoId: localOneway.or35921.defSvJigyoId ?? '1',
    /** 職員ID */
    shokuinId: localOneway.or35921.shokuinId ?? '1',
    /** 計画期間ページ区分 */
    kikanPage: String(local.component.kikanPage),
  }
  const resData: WeekPlanKikanOutEntity = await ScreenRepository.select(
    'weekPlanInvWithinSelect',
    inputData
  )
  // 変数.モードが正常「2」にセットし、AC001-1-2から実行する
  mode.value = Or35921Const.DEFAULT.MODE_NORMAL
  initResData.value.data.kikanOutD = [] as KikanOutDType[]
  initResData.value.data.kikanOutD.push(resData.data)
  setKikanVariable(initResData.value)
  setRirekiVariable(initResData.value)
  setDetailVariable(initResData.value)
  setInitDataInfo()
  doContentSetting()
  emit('update:modelValue', Or35921Logic.data.get(props.uniqueCpId))
}

/**
 *  AC017_履歴-ボタン押下時の処理
 */
async function onClickDialogRireki() {
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC003を実行
        await _save()
        return
      case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return
    }
  }

  localOneway.or26972Oneway.historySelectInfo.historyId = '1'
  localOneway.or26972Oneway.historySelectInfo.cksFlg = 1
  // Or26972のダイアログ開閉状態を更新する
  Or26972Logic.state.set({
    uniqueCpId: or26972.value.uniqueCpId,
    state: { isOpen: true },
  })

  local.component.rirekiPage = Or35921Const.DEFAULT.PAGE_LOCAL
  await getRirekiInfo(true)
}

/**
 *  AC018_履歴-前へアイコンボタンボタン押下時の処理
 */
async function onClickMo00009TwowayRireki() {
  if (
    local.component.rirekiCurrentIndex === undefined ||
    local.component.rirekiCurrentIndex === 1
  ) {
    return
  }
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC003を実行
        await _save()
        return
      case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return
    }
  }
  local.component.rirekiPage = Or35921Const.DEFAULT.PAGE_BEFORE
  local.component.rirekiCurrentIndex = local.component.rirekiCurrentIndex - 1

  await getRirekiInfo(true)
}

/**
 *  AC019_履歴-後へアイコンボタンボタン押下時の処理
 *
 */
async function onClickMo00009ThreewayRireki() {
  if (
    local.component.rirekiCurrentIndex === undefined ||
    local.component.rirekiCurrentIndex === local.component.rirekiTotalCount
  ) {
    return
  }
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or35921Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or35921Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC003を実行
        await _save()
        return
      case Or35921Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case Or35921Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：以降の処理を行わない。
        return
    }
  }
  local.component.rirekiPage = Or35921Const.DEFAULT.PAGE_AFTER
  local.component.rirekiCurrentIndex = local.component.rirekiCurrentIndex + 1

  await getRirekiInfo(true)
}

/**
 *  履歴変更のデータ取得処理
 *
 * @param setModeFlg - モード設定フラグ
 */
async function getRirekiInfo(setModeFlg: boolean) {
  // バックエンドAPIから初期情報取得
  const inputData: WeekPlanHistoryEntity = {
    //共通情報.取込権限フラグ
    toriAutuFlg: roleFlg.value ? '1' : '0',
    /** 利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '1',
    /** 事業者ID */
    svJigyoId: localOneway.or35921.svJigyoId ?? '',
    /** 計画対象期間ID */
    sc1Id: String(local.component.planTargetPeriodId),
    /** 週間計画ID */
    ks51Id: String(local.component.rirekiCurrentIndex),
    /** 履歴ページ区分 */
    rirekiPage: String(local.component.rirekiPage),
    /** 事業所CD */
    defSvJigyoCd: localOneway.or35921.defSvJigyoCd ?? '',
    /** 事業所ID */
    defSvJigyoId: localOneway.or35921.defSvJigyoId ?? '1',
    /** 職員ID */
    shokuinId: localOneway.or35921.shokuinId ?? '',
    /** Sys略称 */
    sys3Ryaku: localOneway.or35921.sys3Ryaku ?? '',
    /** 計画書様式 */
    cksFlg: localOneway.or35921.cksFlg ?? '1',
    /** 期間管理フラグ */
    kikanFlag: plannningPeriodManageFlg.value,
    /** 現在有効期間ID */
    termid: String(tempValidId.value ?? 1),
    /** 取込元 */
    torikomiMoto: localOneway.or35921.torikomiMoto ?? '1',
  }
  const resData: WeekPlanHistoryOutEntity = await ScreenRepository.select(
    'weekPlanHistorySelect',
    inputData
  )

  // 変数.モードが正常「2」にセットし、AC001-1-3から実行する。
  if (setModeFlg) {
    mode.value = Or35921Const.DEFAULT.MODE_NORMAL
  }

  initResData.value.data.kikanOutD[0].rirekiOutD = [] as RirekiOutDType[]
  initResData.value.data.kikanOutD[0].rirekiOutD.push(resData.data)

  setRirekiVariable(initResData.value)
  setDetailVariable(initResData.value)
  setInitDataInfo()
  doContentSetting()
  emit('update:modelValue', Or35921Logic.data.get(props.uniqueCpId))
}

/**
 * AC020_「作成者選択アイコンボタン」押下
 */
const createUserBtnClick = () => {
  const svJigyoIdList: { svJigyoId: string }[] = []
  systemCommonsStore.getSvJigyoIdList.forEach((item) => {
    svJigyoIdList.push({ svJigyoId: item })
  })
  localOneway.or26257.svJigyoIdList = svJigyoIdList
  localOneway.or26257.kijunYmd = systemCommonsStore.getSystemDate ?? ''
  localOneway.or26257.defSvJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.or26257.shokuinIdList = [Number(local.createUserId)]

  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 計画書一括印刷の表示状態を返すComputed
 */
const showDialogOr10583 = computed(() => {
  return Or10583Logic.state.get(or10583.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 対象期間ポップアップの表示状態を返すComputed
 */
const showDialogOrX0115 = computed(() => {
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 履歴選択ポップアップの表示状態を返すComputed
 */
const showDialogOr26972 = computed(() => {
  return Or26972Logic.state.get(or26972.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 職員検索モーダルの表示状態を返すComputed
 */
const showDialogOr26257 = computed(() => {
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 週間計画パターンタイトルポップアップの表示状態を返すComputed
 */
const showDialogOr27730 = computed(() => {
  return Or27730Logic.state.get(or27730.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 週間計画パターン設定ポップアップの表示状態を返すComputed
 */
const showDialogOr35774 = computed(() => {
  return Or35774Logic.state.get(or35774.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 週間計画マスタポップアップの表示状態を返すComputed
 */
const showDialogOr28778 = computed(() => {
  return Or28778Logic.state.get(or28778.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 週間計画複写ポップアップの表示状態を返すComputed
 */
const showDialogOr60794 = computed(() => {
  return Or60794Logic.state.get(or60794.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 印刷設定ポップアップの表示状態を返すComputed
 */
const showDialogOr28238 = computed(() => {
  return Or28238Logic.state.get(or28238.value.uniqueCpId)?.isOpen ?? false
})

/**
 * エラーダイアログの表示状態を返すComputed
 */
const showDialogOr21813 = computed(() => {
  return Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 確認ダイアログの表示状態を返すComputed
 */
const showDialogOr21814_1 = computed(() => {
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 確認ダイアログの表示状態を返すComputed
 */
const showDialogOr21814_2 = computed(() => {
  return Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 確認ダイアログの表示状態を返すComputed
 */
const showDialogOr21814_3 = computed(() => {
  return Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 確認ダイアログの表示状態を返すComputed
 */
const showDialogOr21814_4 = computed(() => {
  return Or21814Logic.state.get(or21814_4.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 確認ダイアログの表示状態を返すComputed
 */
const showDialogOr21814_5 = computed(() => {
  return Or21814Logic.state.get(or21814_5.value.uniqueCpId)?.isOpen ?? false
})
</script>

<template>
  <c-v-sheet class="view d-flex flex-column h-100">
    <div class="action-sticky">
      <!-- 操作ボタンエリア -->
      <g-base-or11871 v-bind="or11871">
        *
        <template #customButtons>
          <base-mo-00611
            :oneway-model-value="localOneway.mo00611Oneway"
            :disabled="deleteFlag"
            @click="pattern"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.pattern')"
          /></base-mo-00611>
        </template>
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            prepend-icon="file_copy"
            :disabled="deleteFlag"
            @click="copyBtnClick"
          />
        </template>
        <template #optionPrintItems>
          <c-v-row no-gutters>
            <c-v-col class="print">
              <c-v-list-item
                :title="t('btn.print')"
                :disabled="deleteFlag"
                @click="printClick"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row no-gutters>
            <c-v-col class="print">
              <c-v-list-item
                :title="t('btn.batch-printing-of-plans')"
                :disabled="deleteFlag"
                @click="printKeikakuClick"
              />
            </c-v-col>
          </c-v-row>
        </template>
      </g-base-or11871>
    </div>

    <div class="action-content">
      <c-v-row
        no-gutters
        class="d-flex flex-0-1 h-100 overflow-y-auto"
      >
        <!-- ナビゲーションエリア -->
        <c-v-col class="hidden-scroll h-100 pa-3 v-col-2">
          <!-- 利用者選択一覧 -->
          <g-base-or00248 v-bind="or00248" />
        </c-v-col>
        <!-- コンテンツエリア -->
        <c-v-col class="d-flex flex-column h-100 pl-6">
          <c-v-row no-gutters>
            <!-- 事業所 -->
            <c-v-col
              cols="auto"
              class="pa-0 pr-6"
            >
              <g-base-or41179
                v-bind="or41179_1"
                class="custom-jigyo"
              />
            </c-v-col>
            <!-- 計画対象期間 -->
            <c-v-col
              v-if="planPeriodShow"
              cols="auto"
              class="pa-0 pr-6"
            >
              <g-custom-or-x0165
                :oneway-model-value="localComponentsOneway.orX0165Plan"
                @on-click-edit-btn="onClickDialogKeikaku"
                @on-click-pre-btn="onClickMo00009TwowayKeikaku"
                @on-click-post-btn="onClickMo00009ThreewayKeikaku"
              />
            </c-v-col>
            <!-- 作成日 -->
            <c-v-col
              v-show="isHistoryShow"
              cols="auto"
              class="pa-0 pr-6"
            >
              <base-mo00020
                :model-value="local.createDate"
                :oneway-model-value="localComponentsOneway.createDateOneway"
                :disabled="deleteFlag"
                class="custom-input"
              />
            </c-v-col>
            <!-- 作成者 -->
            <c-v-col
              v-show="isHistoryShow"
              cols="auto"
              class="pa-0 pr-6"
            >
              <g-custom-or-x0157
                v-model="local.createUser"
                :oneway-model-value="localComponentsOneway.orX0157CreateUser"
                class="custom-input"
                @on-click-edit-btn="createUserBtnClick"
              />
            </c-v-col>
            <!-- 処理月 -->
            <c-v-col
              v-show="isHistoryShow"
              cols="auto"
              class="pa-0 pr-6"
            >
              <base-mo00615
                :oneway-model-value="{
                  showItemLabel: true,
                  showRequiredLabel: false,
                  customClass: {
                    outerClass: undefined,
                    outerStyle: 'background: none',
                    labelClass: 'mb-1',
                    labelStyle: undefined,
                    itemClass: undefined,
                    itemStyle: undefined,
                  },
                  itemLabel: t('label.process-m'),
                }"
              />
              <base-mo01352
                v-model="local.syoriMonth"
                :oneway-model-value="localComponentsOneway.syoriMonthOneway"
                :disabled="deleteFlag"
                class="custom-input"
              />
            </c-v-col>
            <!-- 履歴 -->
            <c-v-col
              v-if="isHistoryShow"
              cols="auto"
              class="pa-0"
            >
              <g-custom-or-x0165
                :oneway-model-value="localComponentsOneway.orX0165History"
                @on-click-edit-btn="onClickDialogRireki"
                @on-click-pre-btn="onClickMo00009TwowayRireki"
                @on-click-post-btn="onClickMo00009ThreewayRireki"
              />
            </c-v-col>
          </c-v-row>
          <!-- 入力フォーム -->
          <g-custom-or05349
            ref="or05349Ref"
            v-bind="or05349"
            v-model="local.or35921"
            :unique-cp-id="or05349.uniqueCpId"
          ></g-custom-or05349>
        </c-v-col>
      </c-v-row>
      <!-- <g-base-or00051 /> -->
    </div>
    <!-- エラー確認ダイアログ -->
    <g-base-or21813
      v-if="showDialogOr21813"
      v-bind="or21813_1"
    />
    <!-- データ変更確認ダイアログ -->
    <g-base-or21814
      v-if="showDialogOr21814_1"
      v-bind="or21814_1"
    />
    <!-- データ削除確認ダイアログ -->
    <g-base-or21814
      v-if="showDialogOr21814_2"
      v-bind="or21814_2"
    />
    <!-- 「新規ボタン」押下二回目チェック確認イアログ -->
    <g-base-or21814
      v-if="showDialogOr21814_3"
      v-bind="or21814_3"
    />
    <!-- 確認イアログ -->
    <g-base-or21814
      v-if="showDialogOr21814_4"
      v-bind="or21814_4"
    />
    <!-- 確認イアログ -->
    <g-base-or21814
      v-if="showDialogOr21814_5"
      v-bind="or21814_5"
    />
    <!-- GUI00070 対象期間画面 -->
    <g-custom-or-x-0115
      v-if="showDialogOrX0115"
      v-bind="orX0115"
      :oneway-model-value="localOneway.orX0115Oneway"
    />
    <!-- GUI01045 ［週間計画パターンタイトル］画面 -->
    <g-custom-or-27730
      v-if="showDialogOr27730"
      v-bind="or27730"
      :oneway-model-value="localOneway.or22730OnewayModel"
    />
    <!-- GUI01044 ［週間計画パターン設定］画面 -->
    <g-custom-or-35774
      v-if="showDialogOr35774"
      v-mode="or35774Type"
      v-bind="or35774"
      :or35921-model="or35921Model"
      :oneway-model-value="or35774OnewayModel"
    />
    <!-- GUI01053 ［履歴選択］画面 -->
    <g-custom-or-26972
      v-if="showDialogOr26972"
      v-bind="or26972"
      v-model="or26972Type"
      :oneway-model-value="localOneway.or26972Oneway"
      @update:model-value="historySelectChange"
    />
    <!-- Or26257:有機体:職員検索モーダル -->
    <g-custom-or26257
      v-if="showDialogOr26257"
      v-bind="or26257"
      v-model="local.or26257"
      :oneway-model-value="localOneway.or26257"
    />
    <!-- GUI01046 ［週間計画複写］画面 -->
    <g-custom-or-60794
      v-if="showDialogOr60794"
      v-bind="or60794"
      v-model="or60794Type"
      :oneway-model-value="or60794Data"
    />
    <!-- GUI01037 ［週間計画マスタ］画面 -->
    <g-custom-or-28778
      v-if="showDialogOr28778"
      v-bind="or28778"
      :oneway-model-value="or28778Data"
    />
    <!-- GUI00936 ［計画書一括印刷］画面 -->
    <g-custom-or-10583
      v-if="showDialogOr10583"
      v-bind="or10583"
      :oneway-model-value="localOneway.or10583Oneway"
    />
    <g-custom-or-28238
      v-if="showDialogOr28238"
      v-bind="or28238"
      :oneway-model-value="localOneway.or28238Oneway"
      :unique-cp-id="or28238.uniqueCpId"
    />
  </c-v-sheet>
</template>

<style scoped lang="scss">
.view {
  display: flex;
  flex-direction: column;
  height: max-content !important;
  background-color: transparent;
}
:deep(.v-window__container) {
  height: 100%;
}

.custom-jigyo {
  flex-direction: column;

  :deep(> div:first-child) {
    margin: 0px !important;
    padding: 0px !important;
    margin-bottom: 4px !important;
  }

  :deep(> div:last-child > div) {
    margin: 0px !important;
  }
}

.v-sheet.custom-input {
  background-color: rgb(var(--v-theme-background));
}
.custom-input {
  :deep(.v-input__control) {
    background-color: rgb(var(--v-theme-secondaryBackground));
  }
}
.action-sticky {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
  button {
    height: 32px !important;
  }
}
.action-content {
  background: rgb(var(--v-theme-background));
  height: max-content;
  z-index: 888;
  padding-bottom: 261px;
}
</style>
