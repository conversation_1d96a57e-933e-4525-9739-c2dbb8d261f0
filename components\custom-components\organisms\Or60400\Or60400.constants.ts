import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or60400:有機体:週間計画一括取込
 * 静的データ
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or60400Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or60400', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * '全'
     */
    export const STR_ALL = '全'
    /**
     * '他'
     */
    export const STR_OTHER = '他'
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * 処理年月フラグ:0
     */
    export const PROCESSINGDMY_0 = '0'
    /**
     * 処理年月フラグ:1
     */
    export const PROCESSINGDMY_1 = '1'
    /**
     * 取込先年月フラグ：0
     */
    export const IMPORTDMY_0 = '0'
    /**
     * 取込先年月フラグ：1
     */
    export const IMPORTDMY_1 = '1'
    /**
     * 週間計画の指定：1（選択状態）
     */
    export const SELECTSTATUS_1 = '1'
    /**
     * 週間計画の指定：0（未選択状態）
     */
    export const SELECTSTATUS_0 = '0'
    /**
     * 2009/05
     */
    export const YYYYMM_2009_05 = '2009/05'
    /**
     * 入力物理名(取込先年月)
     */
    export const SHORIYM = 'shoriYm'
    /**
     *文字列:0
     */
    export const STRING_0 = '0'
    /**
     *文字列:1
     */
    export const STRING_1 = '1'
    /**
     *文字列:2
     */
    export const STRING_2 = '2'
    /**
     *文字列:3
     */
    export const STRING_3 = '3'
    /**
     *文字列:3
     */
    export const STRING_4 = '4'
    /**
     *呼び出し元判定 :1
     */
    export const CALLERSOURCEDETERMINATION = '1'
    /**
     *取り込みの区分 :0
     */
    export const IMPORTCLASSIFICATION = '0'
  }
}
