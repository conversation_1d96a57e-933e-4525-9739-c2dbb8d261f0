import type { KaokuzuMitorizu } from '~/components/custom-components/organisms/Orx0174/OrX0174.type'
import type { KaokuzuMitorizuType } from '~/components/custom-components/organisms/OrX0177/OrX0177.type'

/**
 * OrX0174：有機体：家屋図描画
 *
 * 単方向バインドのデータ構造
 *
 * <AUTHOR>
 */
export interface OrX0174OnewayType {
  /**
   * 表示モード
   * 'dialog'(デフォルト),'main','preview'
   */
  displayMode?: string
  /**
   * 家屋図(詳細モード 情報登録とメイン画面用)
   */
  detailList?: KaokuzuMitorizu[]
  /**
   * 家族図(簡易モード プレビュー用)
   */
  detailListSimple?: KaokuzuMitorizuType[]
  /**
   * 幅(メインと画面のみ)
   */
  width?: number
  /**
   * 高さ(メイン画面のみ)
   */
  height?: number
  /**
   * 縮小率
   */
  shukushouritsu?: string
}
