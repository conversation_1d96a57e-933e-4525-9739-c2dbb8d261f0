import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
import type {
  KyufuInfo,
  ShienIdInfo,
} from '~/components/custom-components/organisms/Or32312/Or32312.type'

/** 給付状況情報取得処理request */
export interface PaymentStatusInfoEntityInEntity extends InWebEntity {
  /**
   * 事業所ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 処理年度
   */
  yymmY: string
  /**
   * 適用事業所ＩＤリスト
   */
  shienIdList: ShienIdInfo[]
}

/**
 * 給付状況情報取得処理response
 */
export interface PaymentStatusInfoEntityOutEntity extends OutWebEntity {
  /** data */
  data: {
    kyufuInfoList: KyufuInfo[]
  }
}

/** 給付状況情報削除処理request */
export interface PaymentStatusInfoDelInEntity extends InWebEntity {
  /**
   * 事業所ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 削除月
   */
  asYm: string
  /**
   * 削除日
   */
  asYmD: string
  /**
   * 職員ID
   */
  shokuId: string
}

/**
 * 給付状況情報削除処理response
 */
export interface PaymentStatusInfoDelOutEntity extends OutWebEntity {
  /** data */
  data: {
    // 削除処理結果
    result: string
  }
}
