<script setup lang="ts">
/**
 * OrX0217:有機体:認定調査票特記事項コンテンツエリアタブ
 * GUI01272_認定調査票特記事項
 *
 * @description
 *［認定調査票特記事項］画面では、［特記事項］画面に反映する特記事項を入力します。
 *［認定調査票特記事項］画面は、［ケアマネ］→［認定調査］→［基本調査］画面などで［特記⇒］をクリックすると表示されます。
 *
 * <AUTHOR>
 */
import { reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27761Const } from '../Or27761/Or27761.constants'
import { Or27761Logic } from '../Or27761/Or27761.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { OrX0217Const } from './OrX0217.constants'
import { useSystemCommonsStore, computed, useSetupChildProps } from '#imports'
import type {
  NTokkiList,
  OrX0217OnewayType,
  OrX0217Type,
} from '~/types/cmn/business/components/OrX0217Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or27761Type, Or27761OnewayType } from '~/types/cmn/business/components/Or27761Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
import type {
  Or21814FirstBtnType,
  Or21814SecondBtnType,
  Or21814ThirdBtnType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { UPDATE_KBN } from '~/constants/classification-constants'

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: OrX0217Type
  onewayModelValue: OrX0217OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
// 選択した行
const selectedItemIndex = ref<number>(-1)

// 有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
// 有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
// 有機体：入力支援［ケアマネ］モーダル
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })
// 有機体:表示順変更認定調査票特記事項モーダル
const or27761 = ref({ uniqueCpId: Or27761Const.CP_ID(0) })

// ダイアログ表示フラグ
const showDialog21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialog21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr27761 = computed(() => {
  // Or27761のダイアログ開閉状態
  return Or27761Logic.state.get(or27761.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr51775 = computed(() => {
  // Or27761のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or27761Const.CP_ID(0)]: or27761.value,
})

// 親画面
const defaultOnewayModelValue: OrX0217OnewayType = {
  //  計画期間ID
  sc1Id: OrX0217Const.DEFAULT.EMPTY,
  //  調査票ID
  cschId: OrX0217Const.DEFAULT.EMPTY,
  // 特記コード
  specialNoteCd: OrX0217Const.DEFAULT.EMPTY,
  // 特記事項タイトル
  tokkiTitle: OrX0217Const.DEFAULT.EMPTY,
  // 特記事項(固定)リスト
  tokkiTextList: [],
}

// 初期化コンポーネント
const localOneway = reactive({
  orX0217: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 特記事項タイトルの入力支援アイコン
  mo00009CertificationOneWay: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  // 表示順の入力支援アイコン
  mo00009SortOneWay: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  // 行挿入ボタン
  mo00611InsertRowOneWay: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
  } as Mo00611OnewayType,
  // 行削除ボタン
  mo01265DelOneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
  } as Mo01265OnewayType,
  // GUI00937 ［入力支援［ケアマネ］］画面
  or51775OnewayModel: {
    // タイトル
    title: OrX0217Const.DEFAULT.EMPTY,
    // 画面ID
    screenId: OrX0217Const.DEFAULT.EMPTY,
    // 分類ID
    bunruiId: OrX0217Const.DEFAULT.EMPTY,
    // 大分類ＣＤ
    t1Cd: OrX0217Const.DEFAULT.EMPTY,
    // 中分類ＣＤ
    t2Cd: OrX0217Const.DEFAULT.EMPTY,
    // 小分類ＣＤ
    t3Cd: OrX0217Const.DEFAULT.EMPTY,
    // テーブル名
    tableName: OrX0217Const.DEFAULT.EMPTY,
    // カラム名
    columnName: OrX0217Const.DEFAULT.EMPTY,
    // アセスメント方式
    assessmentMethod: OrX0217Const.DEFAULT.EMPTY,
    // 文章内容
    inputContents: OrX0217Const.DEFAULT.EMPTY,
    // 利用者ID
    userId: OrX0217Const.DEFAULT.EMPTY,
  } as Or51775OnewayType,
  // 特記事項
  Mo01274OneWay: {
    readonly: false,
  } as Mo01274OnewayType,
})

// ローカル双方向bind
const local = reactive({
  orX0217: {
    ...props.modelValue,
  } as OrX0217Type,
  delNtokkiList: [] as NTokkiList[],
})

// GUI01281［表示順変更認定調査票特記事項］画面の返却情報
const or27761Type = ref<Or27761Type>({
  sortList: [],
})

// GUI01281［表示順変更認定調査票特記事項］画面の初期値
const or27761Data: Or27761OnewayType = {
  indexList: [],
}

// テーブルヘッダ
const headers = [
  {
    title: t('label.tokki-id'),
    key: 'n2tCd',
    minWidth: '50',
    sortable: false,
  },
  {
    title: t('label.memo-knj'),
    key: 'memoKnj',
    minWidth: '800',
    sortable: false,
  },
]

const columnMinWidth = ref<number[]>([153, 830])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面.認定調査票特記事項の先頭行を選択状態にする
selectRow(0)

/**
 * AC002_「特記事項タイトルの入力支援アイコン」押下
 */
const otherServiceBtnClick = () => {
  if (selectedItemIndex.value !== -1) {
    // 文章内容:画面.特記事項
    const selectedItem = local.orX0217.nTokkiList[selectedItemIndex.value]
    localOneway.or51775OnewayModel.inputContents =
      selectedItem?.memoKnj?.value ?? OrX0217Const.DEFAULT.EMPTY
    // タイトル:"認定調査票(特記事項)"
    localOneway.or51775OnewayModel.title = t('label.survey-memo-knj')
    // 大分類CD:430
    localOneway.or51775OnewayModel.t1Cd = '430'
    // 中分類CD
    if (localOneway.orX0217.specialNoteCd.startsWith('1')) {
      localOneway.or51775OnewayModel.t2Cd = '11'
    }
    if (localOneway.orX0217.specialNoteCd.startsWith('2')) {
      localOneway.or51775OnewayModel.t2Cd = '12'
    }
    if (localOneway.orX0217.specialNoteCd.startsWith('3')) {
      localOneway.or51775OnewayModel.t2Cd = '13'
    }
    if (localOneway.orX0217.specialNoteCd.startsWith('4')) {
      localOneway.or51775OnewayModel.t2Cd = '14'
    }
    if (localOneway.orX0217.specialNoteCd.startsWith('5')) {
      localOneway.or51775OnewayModel.t2Cd = '15'
    }
    if (localOneway.orX0217.specialNoteCd.startsWith('6')) {
      localOneway.or51775OnewayModel.t2Cd = '16'
    }
    if (localOneway.orX0217.specialNoteCd.startsWith('7')) {
      localOneway.or51775OnewayModel.t2Cd = '17'
    }
    // 小分類CD:1
    localOneway.or51775OnewayModel.t3Cd = '1'
    // テーブル名:"cpn_tuc_csc4"
    localOneway.or51775OnewayModel.tableName = 'cpn_tuc_csc4'
    // カラム名:"memo_knj"
    localOneway.or51775OnewayModel.columnName = 'memo_knj'
    // アセスメント方式:共通情報.アセスメント方式
    localOneway.or51775OnewayModel.assessmentMethod = '2' //TODO
    // 利用者ID:共通情報.利用者ID
    localOneway.or51775OnewayModel.userId = systemCommonsStore.getUserId!
    // GUI00937_入力支援［ケアマネ］画面をポップアップで起動する。
    Or51775Logic.state.set({
      uniqueCpId: or51775.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * その他のサービス
 *
 * @param data - 入力支援情報
 */
const handleConfirm = (data: Or51775ConfirmType) => {
  // 返却情報.上書きフラグが1:本文末に追加の場合
  if (data.type === OrX0217Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
    //  画面.特記事項 = 画面.特記事項 + 返却情報.内容
    local.orX0217.nTokkiList[selectedItemIndex.value].memoKnj.value =
      local.orX0217.nTokkiList[selectedItemIndex.value].memoKnj.value + data.value
  } else {
    // 返却情報.上書きフラグが2:本文上書の場合
    // 画面.特記事項 = 返却情報.内容
    local.orX0217.nTokkiList[selectedItemIndex.value].memoKnj.value = data.value
  }
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * AC003_「行挿入ボタン」押下
 */
function onInsertRow() {
  if (selectedItemIndex.value !== -1) {
    local.orX0217.nTokkiList.splice(selectedItemIndex.value, 0, {
      // カウンター
      counter: OrX0217Const.DEFAULT.EMPTY,
      // 認定（特記：大）
      n1tCd: OrX0217Const.DEFAULT.EMPTY,
      // 認定（特記：小）
      n2tCd: { value: OrX0217Const.DEFAULT.EMPTY },
      // 特記事項
      memoKnj: { value: OrX0217Const.DEFAULT.EMPTY },
      // 表示順
      seqNo: (local.orX0217.nTokkiList.length + 1).toString(),
      // 更新区分
      updateKbn: UPDATE_KBN.CREATE,
    })
  }
}

/**
 * AC004_「行削除ボタン」押下
 *
 */
async function onDeleteRow() {
  // 現在選択している特記事項は一行のみの場合
  if (local.orX0217.nTokkiList.length === 1) {
    await openErrorDialog(t('message.e-cmn-40793'))
  } else {
    // 一行以外の場合
    const dialogResult = await openInfoDialog(
      t('message.i-cmn-10219'),
      t('btn.yes'),
      'normal1',
      'destroy1',
      'blank'
    )
    switch (dialogResult) {
      case OrX0217Const.DEFAULT.DIALOG_RESULT_YES:
        //  はい：AC004-2を行う。
        onDeleteRowProc()
        break
      case OrX0217Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を終了する。
        break
    }
  }
}

/**
 * 行削除
 *
 */
function onDeleteRowProc() {
  if (local.orX0217.nTokkiList[selectedItemIndex.value].updateKbn === UPDATE_KBN.CREATE) {
    local.orX0217.nTokkiList.splice(selectedItemIndex.value, 1)
  } else {
    local.orX0217.nTokkiList[selectedItemIndex.value].updateKbn = UPDATE_KBN.DELETE
  }
  if (selectedItemIndex.value > local.orX0217.nTokkiList.length - 1)
    selectRow(selectedItemIndex.value - 1)
}

/**
 * AC005_「表示順の入力支援アイコン」押下
 *
 */
function onSortReSet() {
  or27761Data.indexList = []
  local.delNtokkiList = []
  local.orX0217.nTokkiList.forEach((item, index) => {
    if (item.updateKbn !== UPDATE_KBN.DELETE) {
      or27761Data.indexList.push({
        // カウンター
        counter: item.counter,
        // 認定（特記：大）
        n1tCd: item.n1tCd,
        // 表示順
        sort: index + 1,
        // 番号
        number: item.n2tCd.value,
        // 内容
        content: item.memoKnj.value,
        // 初期表示順バックアップ
        sortBackup: item.seqNo,
      })
    } else {
      local.delNtokkiList.push({
        // カウンター
        counter: item.counter,
        // 認定（特記：大）
        n1tCd: item.n1tCd,
        // 認定（特記：小）
        n2tCd: { value: item.n2tCd.value },
        // 特記事項
        memoKnj: { value: item.memoKnj.value },
        // 表示順
        seqNo: item.seqNo,
        // 更新区分
        updateKbn: item.updateKbn,
      })
    }
  })
  // GUI01281［表示順変更認定調査票特記事項］画面をポップアップで起動する。
  Or27761Logic.state.set({
    uniqueCpId: or27761.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ポップアップ返却情報がある場合
const reSetDataSort = (data: Or27761Type) => {
  local.orX0217.nTokkiList = []
  for (const item of data.sortList) {
    let updateKbn = UPDATE_KBN.UPDATE
    if (item.counter === undefined || item.counter === OrX0217Const.DEFAULT.EMPTY) {
      updateKbn = UPDATE_KBN.CREATE
    }
    local.orX0217.nTokkiList.push({
      // カウンター
      counter:
        typeof item.counter === 'string'
          ? item.counter
          : typeof item.counter === 'number'
            ? item.counter.toString()
            : '',
      // 認定（特記：大）
      n1tCd: typeof item.n1tCd === 'string' ? item.n1tCd : '',
      // 認定（特記：小）
      n2tCd: { value: item.number },
      // 特記事項
      memoKnj: { value: item.content },
      // 表示順
      seqNo: item.sort.toString(),
      // 更新区分
      updateKbn: updateKbn,
    })
  }
  for (const item of local.delNtokkiList) {
    local.orX0217.nTokkiList.push({
      // カウンター
      counter: item.counter,
      // 認定（特記：大）
      n1tCd: item.n1tCd,
      // 認定（特記：小）
      n2tCd: { value: item.n2tCd.value },
      // 特記事項
      memoKnj: { value: item.memoKnj.value },
      // 表示順
      seqNo: item.seqNo,
      // 更新区分
      updateKbn: item.updateKbn,
    })
  }
}

/**
 * AC008_「行追加ボタン」押下
 */
function onAddRow() {
  local.orX0217.nTokkiList.push({
    // カウンター
    counter: OrX0217Const.DEFAULT.EMPTY,
    // 認定（特記：大）
    n1tCd: OrX0217Const.DEFAULT.EMPTY,
    // 認定（特記：小）
    n2tCd: { value: OrX0217Const.DEFAULT.EMPTY },
    // 特記事項
    memoKnj: { value: OrX0217Const.DEFAULT.EMPTY },
    // 表示順
    seqNo: (local.orX0217.nTokkiList.length + 1).toString(),
    // 更新区分
    updateKbn: UPDATE_KBN.CREATE,
  })
  selectRow(local.orX0217.nTokkiList.length)
}

/**
 * AC010_「特記事項タイトル」押下 & AC011_「特記事項（固定)ID」押下
 *
 * @param value - 特記事項タイトル
 */
function titleClick(value: string) {
  local.orX0217.nTokkiList[selectedItemIndex.value].n2tCd = { value: value }
}

/**
 * errorダイアログを閉じたタイミングで結果を返却
 *
 * @param msg - msg
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openErrorDialog(msg: string): Promise<string> {
  // 選択行削除確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: 'blank',
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'blank',
    },
  })

  /**
   * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
   *
   * @returns ダイアログの選択結果（yes, no）
   */
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = OrX0217Const.DEFAULT.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = OrX0217Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msg - msg
 *
 * @param firstLabel - firstLabel
 *
 * @param firstBtn - firstBtn
 *
 * @param secondBtn - secondBtn
 *
 * @param thirdBtn - thirdBtn
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(
  msg: string,
  firstLabel: string,
  firstBtn: Or21814FirstBtnType,
  secondBtn: Or21814SecondBtnType,
  thirdBtn: Or21814ThirdBtnType
): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: firstBtn,
      firstBtnLabel: firstLabel,
      secondBtnType: secondBtn,
      secondBtnLabel: t('btn.no'),
      thirdBtnType: thirdBtn,
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = OrX0217Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = OrX0217Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = OrX0217Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = OrX0217Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}

/**************************************************
 *
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

watch(
  () => local.orX0217.nTokkiList,
  (newValue) => {
    // 行内容変更です
    if (local.orX0217.nTokkiList !== undefined && local.orX0217.nTokkiList.length > 0) {
      if (local.orX0217.nTokkiList[selectedItemIndex.value].updateKbn === UPDATE_KBN.NONE) {
        local.orX0217.nTokkiList[selectedItemIndex.value].updateKbn = UPDATE_KBN.UPDATE
      }
    }
    emit('update:modelValue', newValue)
  },
  { deep: true, immediate: false }
)
</script>
<template>
  <div class="label-class">
    <div style="display: flex; margin-left: -4px">
      <div>
        <!-- 特記事項タイトル -->
        <base-mo01338
          :oneway-model-value="{ value: localOneway.orX0217.tokkiTitle, valueFontWeight: 'normal' }"
        >
        </base-mo01338>
      </div>
      <div>
        <!-- 特記事項タイトルの入力支援アイコン -->
        <base-mo00009
          :oneway-model-value="localOneway.mo00009CertificationOneWay"
          @click="otherServiceBtnClick"
        ></base-mo00009>
      </div>
    </div>
    <template
      v-for="(item, index) in localOneway.orX0217.tokkiTextList"
      :key="index"
    >
      <div style="display: flex">
        <template
          v-for="(value, number) in item.tokkiDetailList"
          :key="number"
        >
          <div style="width: 55px">
            <!-- 特記事項（固定)ID -->
            <base-mo01338
              :oneway-model-value="{
                value: value.tokkiId,
                valueFontWeight:
                  value.tokkiId === localOneway.orX0217.specialNoteCd ? 'bold' : 'normal',
              }"
              @click="titleClick(value.tokkiId)"
            />
          </div>
          <div style="margin-left: -20px">
            <!-- 特記事項（固定) -->
            <base-mo01338
              :oneway-model-value="{
                value: value.tokkiText,
                valueFontWeight:
                  value.tokkiId === localOneway.orX0217.specialNoteCd ? 'bold' : 'normal',
              }"
              @click="titleClick(value.tokkiId)"
            />
          </div>
        </template>
      </div>
    </template>
  </div>
  <hr class="v-divider" />
  <c-v-row no-gutters>
    <c-v-col>
      <div style="display: flex">
        <div style="width: 885px">
          <!-- 行追加ボタン -->
          <g-custom-or-26168 @click="onAddRow()" />
          <!-- 行挿入ボタン -->
          <base-mo00611
            v-bind="localOneway.mo00611InsertRowOneWay"
            :disabled="selectedItemIndex === -1"
            class="ml-2"
            @click="onInsertRow()"
          >
            <!--ツールチップ表示："行挿入して新しいデータを登録します"-->
            <c-v-tooltip
              activator="parent"
              location="top"
              :text="t('tooltip.care-plan2-insertline-btn')"
            />
          </base-mo00611>
          <!-- 削除ボタン -->
          <base-mo01265
            v-bind="localOneway.mo01265DelOneWay"
            class="ml-2"
            :disabled="selectedItemIndex === -1"
            @click="onDeleteRow()"
          >
            <!--ツールチップ表示："選択されている行のデータを削除します"-->
            <c-v-tooltip
              activator="parent"
              location="top"
              :text="t('tooltip.care-plan2-deleteline-btn')"
            />
          </base-mo01265>
        </div>
        <div class="label-class">
          <!-- 表示順 -->
          <base-mo01338
            :oneway-model-value="{
              value: t('label.display-order'),
              valueFontWeight: 'normal',
            }"
          />
        </div>
        <div>
          <!-- 表示順の入力支援アイコン -->
          <base-mo00009
            :oneway-model-value="localOneway.mo00009SortOneWay"
            @click="onSortReSet()"
          >
            <!--ツールチップ表示："表示順変更画面を表示’します"-->
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :max-width="600"
              :text="t('tooltip.sort-upd-btn')"
              open-delay="200"
            />
          </base-mo00009>
        </div>
      </div>
    </c-v-col>
  </c-v-row>
  <c-v-row>
    <c-v-col>
      <c-v-data-table
        v-resizable-grid="{ columnWidths: columnMinWidth }"
        :headers="headers"
        :items="local.orX0217.nTokkiList"
        height="232px"
        hover
        class="table-wrapper"
        fixed-header
        items-per-page="-1"
        hide-default-footer
      >
        <template #item="{ item, index }">
          <tr
            v-if="item.updateKbn !== UPDATE_KBN.DELETE"
            :class="{ 'select-row': selectedItemIndex === index }"
            @click="selectRow(index)"
          >
            <td style="padding: 0px !important">
              <base-mo01274
                v-model="item.n2tCd"
                :oneway-model-value="localOneway.Mo01274OneWay"
                max-length="4"
                style="
                  text-align: left !important;
                  margin-left: 1px;
                  margin-top: 1px !important;
                  width: 100% !important;
                "
              ></base-mo01274>
            </td>
            <td style="padding: 0px !important">
              <base-mo01274
                v-model="item.memoKnj"
                :oneway-model-value="localOneway.Mo01274OneWay"
                max-length="93"
                style="margin-top: 2px !important"
              ></base-mo01274>
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-col>
  </c-v-row>
  <!-- Or51775: 有機体：入力支援［ケアマネ］モーダル -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="localOneway.or51775OnewayModel"
    @confirm="handleConfirm"
  />
  <!-- Or27761: 有機体:表示順変更認定調査票特記事項モーダル -->
  <g-custom-or-27761
    v-if="showDialogOr27761"
    v-bind="or27761"
    v-model="or27761Type"
    :oneway-model-value="or27761Data"
    @update:model-value="reSetDataSort"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialog21813"
    v-bind="or21813"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialog21814"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
:deep(full-width-field) {
  padding: 0 15px !important;
}
.label-class :deep(.v-col) {
  padding: 0 !important;
}
</style>
