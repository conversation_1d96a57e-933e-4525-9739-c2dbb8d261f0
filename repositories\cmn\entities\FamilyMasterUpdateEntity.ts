/**
 * Or10472:家族マスタ)ダイアログ
 * GUI00829_［家族マスタ］画面
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 予定マスタ情報保存入力エンティティ
 */
export interface FamilyMasterUpdateInEntity extends InWebEntity {
  /**
   * 区分フラグ
   */
  kbnFlg: string
  /**
   * 予定マスタリスト
   */
  stringInputAssistList: StringInputInAssistList[]
}

/**
 * 予定マスタ情報保存出力エンティティ
 */
export interface FamilyMasterUpdateOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * 予定マスタリスト
     */
    stringInputAssistList: StringInputOutAssistList[]
  }
}

/**
 * 文字列入力支援リスト
 */
export interface StringInputInAssistList {
  /**
   *入力ID
   */
  cf1Id: string
  /**
   *入力区分
   */
  cf1Kbn: string
  /**
   *区分コード
   */
  kbnCd: string
  /**
   *内容
   */
  textKnj: string
  /**
   *変更フラグ
   */
  changeF: string
  /**
   *区分フラグ
   */
  kbnFlg: string
  /**
   * 更新区分
   */
  updateKbn: string
}

/**
 * 文字列入力支援リスト
 */
export interface StringInputOutAssistList {
  /**
   *入力ID
   */
  cf1Id: string
  /**
   *入力区分
   */
  cf1Kbn: string
  /**
   *区分コード
   */
  kbnCd: string
  /**
   *内容
   */
  textKnj: string
  /**
   *変更フラグ
   */
  changeF: string
  /**
   *区分フラグ
   */
  kbnFlg: string
}
