<script setup lang="ts">
/**
 * Or28453:有機体:(日課表)日課表パターン（設定）
 * GUI009990_日課表パターン（設定）
 *
 * @description
 * 日課表パターン（設定）メイン画面の処理
 *
 * <AUTHOR> 呉李彪
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or06632Const } from '../Or06632/Or06632.constants'
import { Or28153Const } from './Or28153.constants'
import type {
  OrX0099CalendarConfig,
  OrX0099CalendarEvent,
  OrX0099OnewayType,
  DblClickResult,
  OrX0099SegmentedEvent
} from '~/types/cmn/business/components/OrX0099Type'
import { OrX0099Const } from '~/components/custom-components/organisms/OrX0099/OrX0099.constants'
import {
  useScreenTwoWayBind,
  useSetupChildProps,
} from '#imports'
import type { Daily, Or28153OnewayType, Or28153Type } from '~/types/cmn/business/components/Or28153Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
const { t } = useI18n()
// エミット
const emit = defineEmits(['doubleClick', 'mouseDown', 'clickGui00998'])
/**************************************************
 * Props
 **************************************************/
 interface Props {
  uniqueCpId: string
  readonly?: boolean
  onewayModelValue: Or28153OnewayType
  hasViewAuthflg: boolean
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const defaultOneway = reactive({
  orX0099Oneway: {
    headerHeight: 38,
    headerSlots: Or06632Const.HEADER_SLOTS,
    firstSlots: Or06632Const.FIRST_SLOTS,
    tableWidth: 1080,
    lateAtNightSize: 24,
    morningSizeOne: 8,
    morningSizeTwo: 16,
    afternoon: 24,
    night: 16,
    lateAtNight: 9,
    minutes: 15,
    rollMinutes: 15,
    cellheight: 22,
    overFlowY: 'hidden',
    tableStartTime: '00:00',
    showAllTime: false,
    showStar: false,
    timeSize: '11px',
    addMinutes: 15,
    maxTime: '24:15',
    nonStandardFormatFlg: true,
  } as OrX0099OnewayType,
})

const localOneway = reactive({
  orX0099Oneway: {
    ...defaultOneway.orX0099Oneway,
  } as OrX0099OnewayType,
  or28153Oneway: {
    ...props.onewayModelValue,
  } as Or28153OnewayType,
  // 週間取込
  mo00611Oneway: {
    btnLabel: t('label.week-import'),
    tooltipText: t('tooltip.week-import'),
    width:'79px'
  } as Mo00611OnewayType,
})

// カレンダーの設定
const calendarConfig = ref<OrX0099CalendarConfig>({
  events: [],
  cellHeight: 22,
  zoom: 1,
  startHour: 0,
  endHour: 0,
  allCellNumber: 97,
})

const orX0099 = ref({ uniqueCpId: '' })


/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0099Const.CP_ID(0)]: orX0099.value,
})

/**************************************************
 * Pinia
 **************************************************/
 const { refValue } = useScreenTwoWayBind<Or28153Type>({
  cpId: Or28153Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})


onMounted(() => {
  if (refValue?.value?.dailyInfoList) {
    setCalendar(refValue?.value?.dailyInfoList)
  }
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
const calendar = ref({ reRenderCalendar: Function })

/**
 * AC030_カレンダーをダブルクリック
 *
 * @param data - ダブルクリックイベントの戻りパラメータ
 */
function handleDoubleClick(data: DblClickResult) {
  emit('doubleClick', data)
}
/**
 * 日課情報オブジェクトドラッグ＆ドロップ
 *
 * @param event - 日課情報オブジェクトドラッグ＆ドロップ
 */
function handelMousedown(event: OrX0099SegmentedEvent) {
  emit('mouseDown', event)
}

/**
 * 日課表詳細の監視
 */
 watch(
  () => refValue.value?.dailyInfoList,
  () => {
    if (refValue?.value?.dailyInfoList) {
      setCalendar(refValue?.value?.dailyInfoList.filter(item => item.updateKbn !== UPDATE_KBN.DELETE))
    }
  }
)

/**
 * 時間帯内容設定
 *
 * @param dataList - 週間計画詳細リスト
 */
function setCalendar(dataList: Daily[]) {
  if (dataList) {
    const eventList = []
    let index = 0
    for (const e of dataList) {
      // 計算週
      const event: OrX0099CalendarEvent = {
        // 日程ブロックID
        id: index++,
        customId: e.uuId,
        // 日程開始時間
        start: e.startTime,
        // 日程終了時間
        end: e.endTime,
        // 日程ブロック背景色
        bgColor: e.backColor,
        // フォント色
        fontColor: e.fontColor,
        // フォントサイズ
        fontSize: `${e.fontSize}px`,
        // 日程内容エリア揃い方向
        align: e.alignment === '2' ? 'right' : e.alignment === '0' ? 'left' : 'center',
        // タイトル
        title: '',
        // 曜日
        headerIndex:
          e.dataKbn === Or06632Const.DATA_KBN_DAILY_LIFE
            ? 0
            : e.dataKbn === Or06632Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
              ? 1
              : 3,
        // 間隔日数
        tableWidth: 760,
        mergeHeaderIndex:
          e.dataKbn === Or06632Const.DATA_KBN_DAILY_LIFE
            ? []
            : e.dataKbn === Or06632Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
              ? [2]
              : [4],
        info:
          e.dataKbn === Or06632Const.DATA_KBN_DAILY_LIFE
            ? [{ index: 0, info: (e.naiyoKnj + e.memoKnj), showTimeFlg: Number(e.timeKbn) }]
            : e.dataKbn === Or06632Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
              ? [
                  { index: 1, info: e.naiyoKnj + e.memoKnj, showTimeFlg: Number(e.timeKbn) },
                  { index: 2, info: e.tantoKnj, showTimeFlg: 0 },
                ]
              : [
                  { index: 3, info: e.naiyoKnj + e.memoKnj, showTimeFlg: Number(e.timeKbn) },
                  { index: 4, info: e.tantoKnj, showTimeFlg: 0 },
                ],
        atAnyTime: false,
        // 表示レイヤー
        zIndex: e.zIndex,
      }
      eventList.push(event)
    }
    calendarConfig.value.events = eventList

    // カレンダーの画面を反映
    calendar.value?.reRenderCalendar()
  }
}

/**
 *  ボタン押下時の処理
 */
function showGUI00998() {
  emit('clickGui00998', 'dailyTableImage')
}
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.or28153Oneway = {...newValue}
  },
  { deep: true }
)
</script>

<template>
    <c-v-row
      no-gutters
      class="pt-4"
    >
      <!-- 週間取込 -->
      <base-mo00611
        v-if ="props.hasViewAuthflg"
        v-bind="localOneway.mo00611Oneway"
        style="height:32px"
        @click="showGUI00998()"
      >
        <!-- ツールチップ表示 -->
        <c-v-tooltip
          v-if="localOneway.mo00611Oneway.tooltipText"
          :text="localOneway.mo00611Oneway.tooltipText"
          activator="parent"
          />
        </base-mo00611>
    </c-v-row>
    <c-v-row
      no-gutters
      class="pt-4"
    >
      <g-custom-or-x-0099
        ref="calendar"
        :calendar-config="calendarConfig"
        :oneway-model-value="{ ...localOneway.orX0099Oneway, disabledFlg: props.readonly ?? false}"
        :removeflg="!localOneway.or28153Oneway.showFlg"
        v-bind="orX0099"
        @double-click="handleDoubleClick"
        @mouse-down="handelMousedown"
      ></g-custom-or-x-0099>
    </c-v-row>
</template>

<style scoped lang="scss">
</style>
