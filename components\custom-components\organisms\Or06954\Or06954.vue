<script setup lang="ts">
import { cloneDeep } from 'lodash'
import { computed, nextTick, reactive, ref, type Ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import { Or10878Const } from '../Or10878/Or10878.constants'
import { Or10883Const } from '../Or10883/Or10883.constants'
import { Or10883Logic } from '../Or10883/Or10883.logic'
import { Or27487Const } from '../Or27487/Or27487.constants'
import { Or27487Logic } from '../Or27487/Or27487.logic'
import { Or28451Const } from '../Or28451/Or28451.constants'
import { Or28543Const } from '../Or28543/Or28543.constants'
import { Or28543Logic } from '../Or28543/Or28543.logic'
import { Or28582Const } from '../Or28582/Or28582.constants'
import { Or28582Logic } from '../Or28582/Or28582.logic'
import type { OrX0216OnewayType } from '../OrX0216/OrX0216.type'
import { Or06954Const } from './Or06954.constants'
import { Or06954Logic } from './Or06954.logic'
import type { DayResult, Or06954Type, TableData } from './Or06954.type'
import { dateUtils, useSetupChildProps, useSystemCommonsStore, useValidation } from '#imports'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21816Const } from '~/components/base-components/organisms/Or21816/Or21816.constants'
import { Or21816Logic } from '~/components/base-components/organisms/Or21816/Or21816.logic'
import { useScreenEventStatus, useScreenTwoWayBind } from '~/composables/useComponentVue'
import { SPACE_FORWARD_SLASH, SPACE_WAVE } from '~/constants/classification-constants'
import type { ITab4Data } from '~/repositories/cmn/entities/PreventionPlanSelectEntity'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo01276OnewayType } from '~/types/business/components/Mo01276Type'
import type { Mo01332OnewayType } from '~/types/business/components/Mo01332Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo01343OnewayType, Mo01343Type } from '~/types/business/components/Mo01343Type'
import type {
  Or06954OnewayType,
  Or06954TwowayType,
} from '~/types/cmn/business/components/Or06954Type'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'
import type { Or27487OnewayType, Or27487Type } from '~/types/cmn/business/components/Or27487Type'
import type {
  Or28543OnewayType,
  Or28543Type,
  ShowData,
} from '~/types/cmn/business/components/Or28543Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'

/**
 * Or06954Logic:有機体:支援計画表一覧
 * GUI01095_支援計画（予防計画書）
 *
 * @description
 * 支援計画コンテンツエリアタブ
 *
 * <AUTHOR>
 */
const { t } = useI18n()
const { convertSeirekiToWareki } = dateUtils()
const { byteLength } = useValidation()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or06954OnewayType
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/

// 対象期間リスト
const tab4DataList: Ref<TableData[]> = ref([])

const defaultOneway = {
  // データ-ページング
  mo00615Oneway: {
    itemLabel: 0 + SPACE_FORWARD_SLASH + 0,
    customClass: {
      outerClass: 'page-label-center',
    },
  } as Mo00615OnewayType,
  // 下アイコンボタン
  mo00009OnewayDown: {
    btnIcon: 'keyboard_arrow_down',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  // 上アイコンボタン
  mo00009OnewayUp: {
    btnIcon: 'keyboard_arrow_up',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  mo00009OnewayEditSquare: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    class: 'icon-btn',
    readonly: false,
  } as Mo00009OnewayType,
  mo00009OnewayEditSquareSort: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    tooltipText: t('tooltip.display-order'),
    readonly: false,
  } as Mo00009OnewayType,
  mo00009OnewayCalendar: {
    // デフォルト値の設定
    btnIcon: 'calendar_today',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
}

const local = reactive({
  Or06954: {
    // 表示用「総合的課題と目標」リスト
    tab3DataList: [],
    // 削除用「総合的課題と目標」リスト
    deleteTab3DataList: [],
    // 表示用「支援計画」リスト
    tab4DataList: [],
    // 削除用「支援計画」リスト
    deleteTab4DataList: [],
    // 選択した行のindex
    selectRowIndex: -1,
    // 総合的課題と目標tab選択した行のindex
    tab3SelectRowIndex: -1,
  } as Or06954TwowayType,
  // 表示順変更計画表
  Or28543: {
    // ID
    ID: '',
    // モード
    mode: '',
    // 表示順リスト
    sortList: [],
  } as Or28543Type,
  // サービス種別入力支援（予防計画書）
  Or27487: {} as Or27487Type,
  // （目標とする生活（１年））入力支援ダイアログ
  Or10883: {} as Or10883TwowayType,
  // カレンダー
  mo01343: {
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  } as Mo01343Type,
})

const localOneway = reactive({
  or06954Oneway: props.onewayModelValue,
  // 行追加ボタン
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    height: '32px',
  } as Mo00611OnewayType,
  // 行挿入ボタン
  mo00611OnewayInsert: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    width: '80px',
    height: '32px',
    tooltipText: t('tooltip.insert-row'),
    disabled: true,
  } as Mo00611OnewayType,
  // 行複写ボタン
  mo00611OnewayDuplicate: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    width: '80px',
    height: '32px',
    tooltipText: t('tooltip.duplicate-row'),
    disabled: true,
  } as Mo00611OnewayType,
  // 行削除ボタン
  mo00611OnewayDelete: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    color: 'error',
    labelColor: 'error',
    disabled: true,
    height: '32px',
  } as Mo00611OnewayType,
  // 表示順ボタン
  mo00611OnewaySort: {
    btnLabel: t('btn.display-order'),
    width: '65px',
    height: '32px',
    tooltipText: t('tooltip.display-order'),
    disabled: false,
  } as Mo00611OnewayType,
  // 行選択
  mo00615OnewayRow: {
    itemLabel: SPACE_FORWARD_SLASH,
  } as Mo00615OnewayType,
  // 表示順
  mo00615OnewaySort: {
    itemLabel: Or06954Const.SORT_BY,
  } as Mo00615OnewayType,
  or10883Oneway: {} as Or10883OnewayType,
  or27487Oneway: {} as Or27487OnewayType,
  // 表示順
  mo00009OnewaySort: {
    ...defaultOneway.mo00009OnewayEditSquareSort,
  } as Mo00009OnewayType,
  // 目標についての支援のポイントアイコンボタン
  mo00009Oneway: {
    ...defaultOneway.mo00009OnewayEditSquare,
  } as Mo00009OnewayType,
  mo00009DateOneway: {
    btnIcon: 'calendar_today',
  } as Mo00009OnewayType,
  // 年月日
  mo00009OnewayCalendar: {
    ...defaultOneway.mo00009OnewayCalendar,
  } as Mo00009OnewayType,
  // 表用分子日付テキストフィールド
  mo01276OnewayDate: {
    customClass: new CustomClass({ outerClass: 'bg-transparent' }),
  } as Mo01276OnewayType,
  // ラベル
  mo01338Oneway: {
    value: Or06954Const.WAVE,
  } as Mo01338OnewayType,
  // 本人の取組
  mo00615OnewayPersonal: {
    itemLabel: Or06954Const.CONCRETE_SUPPORT.SUPPORT_1,
  } as Mo00615OnewayType,
  // 家族・地域の支援、民間サービス等
  mo00615OnewayFamily: {
    itemLabel: Or06954Const.CONCRETE_SUPPORT.SUPPORT_2,
  } as Mo00615OnewayType,
  // 介護保険サービス地域支援事業区市町村サービス
  mo00615OnewayNursing: {
    itemLabel: Or06954Const.CONCRETE_SUPPORT.SUPPORT_3,
  } as Mo00615OnewayType,
  // 記号（本人）
  mo01332OnewayMark: {
    showItemLabel: false,
    items: [{ label: '', value: '1' }],
    isVertical: false,
  } as Mo01332OnewayType,
  mo00040Oneway: {
    itemLabel: '',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerStyle: 'margin-left:0px !important' }),
    itemValue: 'value',
    itemTitle: 'title',
    width: '80px',
    items: [
      { value: '1', title: '〇' },
      { value: '0', title: '' },
    ],
  } as Mo00040OnewayType,
  // 表示順変更計画表
  or28543Oneway: {} as Or28543OnewayType,
  // カレンダー
  mo01343Oneway: {} as Mo01343OnewayType,
  mo00615OnewayTitle: {
    itemLabel: t('label.support_plan'),
    customClass: new CustomClass({ outerStyle: 'background-color: transparent' }),
    itemLabelCustomClass: new CustomClass({ labelStyle: 'font-size: 18px;font-weight: bold;' }),
  } as Mo00615OnewayType,
  orX0216Oneway: {
    showWaveIcon:
      props.onewayModelValue.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_0 ||
      props.onewayModelValue.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_1,
    showEndDate:
      props.onewayModelValue.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_0 ||
      props.onewayModelValue.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_1,
    isCopyMode: props.onewayModelValue.isCopyMode,
    itakuKkakYmdFlg: props.onewayModelValue.itakuKkakYmdFlg,
    itakuKkakKikanFlg: props.onewayModelValue.itakuKkakKikanFlg,
  } as OrX0216OnewayType,
})
// 選択した行のindex
const selectRowIndex = ref<number>(-1)
const popUpRowIndex = ref<number>(-1)
const isInsuranceClick = ref<boolean>(false)
const addId = ref<number>(1)
// 年月日の区分
const ymdKbn = ref<string>('1')
const isDateOpen = ref<boolean>(false)
const is216Click = ref<boolean>(false)
const showEditBtnFlg = ref<number[]>([])

const form = ref<VForm>()

const or21816 = ref({ uniqueCpId: Or21816Const.CP_ID(2) })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(1) })
const or10883 = ref({ uniqueCpId: Or10883Const.CP_ID(1) })
const or28543 = ref({ uniqueCpId: Or28543Const.CP_ID(1) })
const or27487 = ref({ uniqueCpId: Or27487Const.CP_ID(1) })
const or28582 = ref({ uniqueCpId: Or28582Const.CP_ID(1) })
const or10878 = ref({ uniqueCpId: Or10878Const.CP_ID(1) })

const tableWrapperWidth = computed(() => {
  return localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_1
    ? '1117px'
    : '1085px'
})

// ダイアログ表示フラグ
const showDialogOr28543 = computed(() => {
  // Or28543のダイアログ開閉状態
  return Or28543Logic.state.get(or28543.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr10883 = computed(() => {
  // Or10883のダイアログ開閉状態
  return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr27487 = computed(() => {
  // Or27487のダイアログ開閉状態
  return Or27487Logic.state.get(or27487.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr28582 = computed(() => {
  // Or28582のダイアログ開閉状態
  return Or28582Logic.state.get(or28582.value.uniqueCpId)?.isOpen ?? false
})

Or21816Logic.state.set({
  uniqueCpId: or21816.value.uniqueCpId,
  state: {
    tooltipTextUp: t('tooltip.care-plan2-select-up'),
    tooltipTextDown: t('tooltip.care-plan2-select-next'),
  },
})

/**************************************************
 * Pinia
 **************************************************/
const { setEvent } = useScreenEventStatus<Or06954Type>({
  cpId: Or06954Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

const { refValue } = useScreenTwoWayBind<Or06954TwowayType>({
  cpId: Or06954Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21816Const.CP_ID(2)]: or21816.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or10883Const.CP_ID(1)]: or10883.value,
  [Or28543Const.CP_ID(1)]: or28543.value,
  [Or27487Const.CP_ID(1)]: or27487.value,
  [Or28582Const.CP_ID(1)]: or28582.value,
  [Or10878Const.CP_ID(1)]: or10878.value,
})

/**
 * 行選択
 *
 * @param index - 行インデックス
 */
const selectRow = (index: number) => {
  selectRowIndex.value = index
  if (selectRowIndex.value === local.Or06954.selectRowIndex) {
    return
  }
  local.Or06954.selectRowIndex = selectRowIndex.value
  // 表示用「行選択」情報をフォーマット
  formatDisplayPageNumber()
  refValue.value!.selectRowIndex = local.Or06954.selectRowIndex
}

/**
 * 「次の行選択」押下
 */
function nextLine() {
  if (
    selectRowIndex.value + 1 <
    local.Or06954.tab4DataList.filter(
      (x) =>
        x.sogoKadaiNo ===
        local.Or06954.tab3DataList[
          local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
        ].sogoKadaiNo
    ).length
  ) {
    selectRowIndex.value++
    local.Or06954.selectRowIndex = selectRowIndex.value
    refValue.value = local.Or06954
  }
}

/**
 * 「前の行選択」押下
 */
function preLine() {
  if (selectRowIndex.value - 1 >= 0) {
    selectRowIndex.value--
    local.Or06954.selectRowIndex = selectRowIndex.value
    refValue.value = local.Or06954
  }
}

/**
 * テーブル初期化
 */
const initTable = () => {
  tab4DataList.value = []
  if (local.Or06954?.tab4DataList && local.Or06954.tab4DataList.length > 0) {
    local.Or06954.tab4DataList.forEach((item) => {
      tab4DataList.value.push({
        /** 総合的課題番号 */
        sogoKadaiNo: item.sogoKadaiNo,
        /** 目標についての支援のポイント */
        shienPointKnj: { value: item.shienPointKnj },
        /** 具体的な支援の内容（本人） */
        infoServiceKnj: { value: item.infoServiceKnj },
        /** 具体的な支援の内容（家族） */
        kazokuServiceKnj: { value: item.kazokuServiceKnj },
        /** 具体的な支援の内容（保険・地域） */
        hokenServiceKnj: { value: item.hokenServiceKnj },
        /** ※1（本人） */
        svKbn: { modelValue: item.svKbn },
        /** ※1（家族） */
        svKazokuKbn: { modelValue: item.svKazokuKbn },
        /** ※1（保険・地域） */
        svHokenKbn: { modelValue: item.svHokenKbn },
        /** サービス種別（本人） */
        svShubetuKnj: { value: item.svShubetuKnj },
        /** サービス種別（家族） */
        svShubetuKazokuKnj: { value: item.svShubetuKazokuKnj },
        /** サービス種別（保険・地域） */
        svShubetuHoken_knj: { value: item.svShubetuHoken_knj },
        /** 事業所（本人） */
        svJigyoKnj: { value: item.svJigyoKnj },
        /** 事業所（家族） */
        svJigyoKazokuKnj: { value: item.svJigyoKazokuKnj },
        /** 事業所（保険・地域） */
        svJigyoHoken_knj: { value: item.svJigyoHoken_knj },
        /** 頻度（本人） */
        hindoKnj: { value: item.hindoKnj },
        /** 頻度（家族） */
        hindoKazokuKnj: { value: item.hindoKazokuKnj },
        /** 頻度（保険・地域） */
        hindoHokenKnj: { value: item.hindoHokenKnj },
        /** 期間（本人） */
        kikanKnj: { value: item.kikanKnj },
        /** 期間（家族） */
        kikanKazokuKnj: { value: item.kikanKazokuKnj },
        /** 期間（保険・地域） */
        kikanHokenKnj: { value: item.kikanHokenKnj },
        /** 期間開始日（本人） */
        kikanSYmd: {
          value: item.kikanSYmd,
        },
        /** 期間終了日（本人） */
        kikanEYmd: { value: item.kikanEYmd },
        /** 期間開始日（家族） */
        kikanKazokuS_ymd: { value: item.kikanKazokuS_ymd },
        /** 期間終了日（家族） */
        kikanKazokuE_ymd: { value: item.kikanKazokuE_ymd },
        /** 期間開始日（保険・地域） */
        kikanHokenS_ymd: { value: item.kikanHokenS_ymd },
        /** 期間終了日（保険・地域） */
        kikanHokenE_ymd: { value: item.kikanHokenE_ymd },
        /** 表示順 */
        seqNo: item.seqNo,
        /** ＩＤ */
        plan14Id: item.plan14Id,
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
      })
    })
    sortTable()
  }
  const dataLength = tab4DataList.value.filter(
    (x) =>
      x.sogoKadaiNo ===
      local.Or06954.tab3DataList[
        local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
      ].sogoKadaiNo
  ).length
  if ((dataLength > 0 && selectRowIndex.value < 0) || dataLength < selectRowIndex.value + 1) {
    // 最初の行を選択
    selectRow(0)
  }
  // 表示用「行選択」情報をフォーマット
  formatDisplayPageNumber()

  // 総合的課題番号
  const sogoKadaiNo =
    local.Or06954.tab3DataList[
      local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
    ].sogoKadaiNo

  // 行挿入ボタン
  localOneway.mo00611OnewayInsert.disabled =
    tab4DataList.value.filter((x) => x.sogoKadaiNo === sogoKadaiNo).length === 0
  // 行複写ボタン
  localOneway.mo00611OnewayDuplicate.disabled =
    tab4DataList.value.filter((x) => x.sogoKadaiNo === sogoKadaiNo).length === 0
  // 行削除ボタン
  localOneway.mo00611OnewayDelete.disabled =
    tab4DataList.value.filter((x) => x.sogoKadaiNo === sogoKadaiNo).length === 0
}

/**
 * 表示用「行選択」情報をフォーマット 01 / 01
 *
 */
const formatDisplayPageNumber = () => {
  const length = tab4DataList.value.filter(
    (x) =>
      x.sogoKadaiNo ===
      local.Or06954.tab3DataList[
        local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
      ].sogoKadaiNo
  ).length
  const value = length.toString().concat(t('label.page-items'))
  localOneway.mo00615OnewayRow.itemLabel = value
}

/**
 * 初期化のソート順： 総合的課題番号 昇順、表示順 昇順、ID 昇順
 *
 */
const sortTable = () => {
  tab4DataList.value = [...tab4DataList.value].sort((a, b) => {
    // 1. 総合的課題番号で比較（昇順）
    if (a.sogoKadaiNo !== b.sogoKadaiNo) {
      return Number(a.sogoKadaiNo) - Number(b.sogoKadaiNo)
    }
    // 2. 総合的課題番号が同じ場合、表示順で比較（昇順）
    if (a.seqNo !== b.seqNo) {
      return Number(a.seqNo) - Number(b.seqNo)
    }
    // 3. 表示順も同じ場合、IDで比較（昇順）
    return Number(a.plan14Id) - Number(b.plan14Id)
  })
}

/**
 * 行追加
 *
 * @param isInsert - 行挿入ボタンかどうかを判断する
 */
const addItem = (isInsert: boolean) => {
  // 操作区分 = 3:削除の場合
  // 表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    props.onewayModelValue.operaFlg === Or28451Const.ACTION_TYPE.DELETE ||
    props.onewayModelValue.pagingFlg === Or06954Const.PAGING_FLG_NONE
  ) {
    return
  }
  // 総合的課題番号
  const sogoKadaiNo =
    local.Or06954.tab3DataList[
      local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
    ].sogoKadaiNo
  // 表示順
  let seqNo = Or06954Const.SEQ_NO_INIT
  if (isInsert) {
    seqNo = (Number(tab4DataList.value[selectRowIndex.value].seqNo) - 1).toString()
  } else {
    seqNo = (Math.max(...tab4DataList.value.map((item) => Number(item.seqNo))) + 1).toString()
  }
  if (sogoKadaiNo) {
    tab4DataList.value.push({
      /** 総合的課題番号 */
      sogoKadaiNo: sogoKadaiNo,
      /** 目標についての支援のポイント */
      shienPointKnj: { value: '' },
      /** 具体的な支援の内容（本人） */
      infoServiceKnj: { value: '' },
      /** 具体的な支援の内容（家族） */
      kazokuServiceKnj: { value: '' },
      /** 具体的な支援の内容（保険・地域） */
      hokenServiceKnj: { value: '' },
      /** ※1（本人） */
      svKbn: { modelValue: '' },
      /** ※1（家族） */
      svKazokuKbn: { modelValue: '' },
      /** ※1（保険・地域） */
      svHokenKbn: { modelValue: '' },
      /** サービス種別（本人） */
      svShubetuKnj: { value: '' },
      /** サービス種別（家族） */
      svShubetuKazokuKnj: { value: '' },
      /** サービス種別（保険・地域） */
      svShubetuHoken_knj: { value: '' },
      /** 事業所（本人） */
      svJigyoKnj: { value: '' },
      /** 事業所（家族） */
      svJigyoKazokuKnj: { value: '' },
      /** 事業所（保険・地域） */
      svJigyoHoken_knj: { value: '' },
      /** 頻度（本人） */
      hindoKnj: { value: '' },
      /** 頻度（家族） */
      hindoKazokuKnj: { value: '' },
      /** 頻度（保険・地域） */
      hindoHokenKnj: { value: '' },
      /** 期間（本人） */
      kikanKnj: { value: '' },
      /** 期間（家族） */
      kikanKazokuKnj: { value: '' },
      /** 期間（保険・地域） */
      kikanHokenKnj: { value: '' },
      /** 期間開始日（本人） */
      kikanSYmd: { value: '' },
      /** 期間終了日（本人） */
      kikanEYmd: { value: '' },
      /** 期間開始日（家族） */
      kikanKazokuS_ymd: { value: '' },
      /** 期間終了日（家族） */
      kikanKazokuE_ymd: { value: '' },
      /** 期間開始日（保険・地域） */
      kikanHokenS_ymd: { value: '' },
      /** 期間終了日（保険・地域） */
      kikanHokenE_ymd: { value: '' },
      /** 表示順 */
      seqNo: seqNo,
      /** ＩＤ */
      plan14Id: Or06954Const.MINUS + addId.value,
      /** 更新回数 */
      modifiedCnt: '',
    })
    addId.value++
    sortTable()
    local.Or06954.tab4DataList = translateTableData(tab4DataList.value)
    if (isInsert) {
      selectRow(selectRowIndex.value)
    } else {
      selectRow(
        tab4DataList.value.filter(
          (x) =>
            x.sogoKadaiNo ===
            local.Or06954.tab3DataList[
              local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
            ].sogoKadaiNo
        ).length - 1
      )
    }
    refValue.value = local.Or06954
  }
}

/**
 * 行複写
 *
 */
const duplicateItem = () => {
  // 操作区分 = 3:削除の場合
  // 表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    props.onewayModelValue.operaFlg === Or28451Const.ACTION_TYPE.DELETE ||
    props.onewayModelValue.pagingFlg === Or06954Const.PAGING_FLG_NONE
  ) {
    return
  }
  // 複写されたローデータ
  const dataToDuplicate = tab4DataList.value.filter(
    (x) =>
      x.sogoKadaiNo ===
      local.Or06954.tab3DataList[
        local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
      ].sogoKadaiNo
  )[selectRowIndex.value]
  tab4DataList.value.push({ ...dataToDuplicate, plan14Id: '', modifiedCnt: '' })
  sortTable()
  local.Or06954.tab4DataList = translateTableData(tab4DataList.value)
  selectRow(selectRowIndex.value)

  refValue.value = local.Or06954
}

/**
 * 行削除
 */
const deleteItem = async () => {
  // 操作区分 = 3:削除の場合
  // 表示用「計画対象期間」情報.ページング区分が0:なしの場合
  // 削除行を指定しない
  if (
    props.onewayModelValue.operaFlg === Or06954Const.ACTION_KBN_DELETE ||
    props.onewayModelValue.pagingFlg === Or06954Const.PAGING_FLG_NONE ||
    selectRowIndex.value < 0
  ) {
    return
  }
  // ダイアログを開く
  const dialogResult = await openWarnDialog()
  switch (dialogResult) {
    // 確認
    case Or06954Const.DEFAULT.DIALOG_RESULT_YES: {
      const deleteData = tab4DataList.value.filter(
        (x) =>
          x.sogoKadaiNo ===
          local.Or06954.tab3DataList[
            local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
          ].sogoKadaiNo
      )[selectRowIndex.value]
      if (deleteData.plan14Id) {
        const tab4DeleteDataList = translateTableData([deleteData])
        // 削除するデータを削除用「支援計画」リストに入れる
        local.Or06954.deleteTab4DataList.push(...tab4DeleteDataList)
      }
      // 対応する番号のデータを削除
      tab4DataList.value = removeItemById(
        tab4DataList.value,
        local.Or06954.tab3DataList[
          local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
        ].sogoKadaiNo,
        selectRowIndex.value + 1
      )
      const length = tab4DataList.value.filter(
        (x) =>
          x.sogoKadaiNo ===
          local.Or06954.tab3DataList[
            local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
          ].sogoKadaiNo
      ).length
      // 行の選択
      if (selectRowIndex.value >= length) {
        selectRow(length - 1)
      }
      local.Or06954.tab4DataList = translateTableData(tab4DataList.value)

      refValue.value = local.Or06954
      break
    }
    case Or06954Const.DEFAULT.DIALOG_RESULT_NO:
      break
  }
}

/**
 * 対応する番号のデータを削除
 *
 * @param items - 表データ
 *
 * @param targetSogoKadaiNo - 番号
 *
 * @param i - index
 */
function removeItemById(items: TableData[], targetSogoKadaiNo: string, i: number) {
  let count = 0
  return items.filter((item) => {
    if (item.sogoKadaiNo === targetSogoKadaiNo) {
      count++
      return count !== i
    }
    return true
  })
}

/**
 * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
const openWarnDialog = async (): Promise<string> => {
  // 選択行削除確認ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.i-cmn-10853'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      thirdBtnLabel: 'blank',
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = Or06954Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or06954Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or06954Const.DEFAULT.DIALOG_RESULT_NO
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「支援計画表一覧の明細部」値変更
 *
 */
const changeTableData = () => {
  local.Or06954.tab4DataList = translateTableData(tab4DataList.value)
  refValue.value = local.Or06954
}

/**
 * 「支援計画表一覧の明細部」チェックボックス値変更
 *
 * @param _index - index
 *
 * @param _type - 記号
 */
const changeTableCheckboxData = (_index: number, _type: Or06954Const.CheckboxType) => {
  local.Or06954.tab4DataList = translateTableData(tab4DataList.value)

  refValue.value = local.Or06954
}

/**
 * TableData[] を ITab4Data[] に変換する
 *
 * @param table - 変換元のTableData配列
 *
 * @returns 変換後のTab4Data配列
 */
const translateTableData = (table: TableData[]): ITab4Data[] => {
  const tab4DataList: ITab4Data[] = []
  table.forEach((x) => {
    tab4DataList.push({
      /** 総合的課題番号 */
      sogoKadaiNo: x.sogoKadaiNo,
      /** 目標についての支援のポイント */
      shienPointKnj: x.shienPointKnj.value,
      /** 具体的な支援の内容（本人） */
      infoServiceKnj: x.infoServiceKnj.value,
      /** 具体的な支援の内容（家族） */
      kazokuServiceKnj: x.kazokuServiceKnj.value,
      /** 具体的な支援の内容（保険・地域） */
      hokenServiceKnj: x.hokenServiceKnj.value,
      /** ※1（本人） */
      svKbn: x.svKbn.modelValue!,
      /** ※1（家族） */
      svKazokuKbn: x.svKazokuKbn.modelValue!,
      /** ※1（保険・地域） */
      svHokenKbn: x.svHokenKbn.modelValue!,
      /** サービス種別（本人） */
      svShubetuKnj: x.svShubetuKnj.value,
      /** サービス種別（家族） */
      svShubetuKazokuKnj: x.svShubetuKazokuKnj.value,
      /** サービス種別（保険・地域） */
      svShubetuHoken_knj: x.svShubetuHoken_knj.value,
      /** 事業所（本人） */
      svJigyoKnj: x.svJigyoKnj.value,
      /** 事業所（家族） */
      svJigyoKazokuKnj: x.svJigyoKazokuKnj.value,
      /** 事業所（保険・地域） */
      svJigyoHoken_knj: x.svJigyoHoken_knj.value,
      /** 頻度（本人） */
      hindoKnj: x.hindoKnj.value,
      /** 頻度（家族） */
      hindoKazokuKnj: x.hindoKazokuKnj.value,
      /** 頻度（保険・地域） */
      hindoHokenKnj: x.hindoHokenKnj.value,
      /** 期間（本人） */
      kikanKnj: x.kikanKnj.value,
      /** 期間（家族） */
      kikanKazokuKnj: x.kikanKazokuKnj.value,
      /** 期間（保険・地域） */
      kikanHokenKnj: x.kikanHokenKnj.value,
      /** 期間開始日（本人） */
      kikanSYmd: x.kikanSYmd.value,
      /** 期間終了日（本人） */
      kikanEYmd: x.kikanEYmd.value,
      /** 期間開始日（家族） */
      kikanKazokuS_ymd: x.kikanKazokuS_ymd.value,
      /** 期間終了日（家族） */
      kikanKazokuE_ymd: x.kikanKazokuE_ymd.value,
      /** 期間開始日（保険・地域） */
      kikanHokenS_ymd: x.kikanHokenS_ymd.value,
      /** 期間終了日（保険・地域） */
      kikanHokenE_ymd: x.kikanHokenE_ymd.value,
      /** 表示順 */
      seqNo: x.seqNo,
      /** ＩＤ */
      plan14Id: x.plan14Id,
      /** 更新回数 */
      modifiedCnt: x.modifiedCnt,
    })
  })
  return tab4DataList
}

/**
 * 選択されている項目
 *
 * @param selectCell - 選択されている項目
 *
 * @param index - index
 */
function handleFocus(selectCell: string, index: number) {
  // 行の選択
  selectRow(index)
  // 選択されている項目
  setEvent({
    selectCell: selectCell,
  })
}

/**
 * 共通部品カレンダー選択（年月日）を開く
 *
 * @param kbn - 年月日の区分
 */
function openCalendarYMD(kbn: string) {
  // 操作区分 = 3:削除の場合
  // 画面.「支援計画」一覧で行が選択されていない場合
  if (
    props.onewayModelValue.operaFlg === Or28451Const.ACTION_TYPE.DELETE &&
    selectRowIndex.value < 0
  ) {
    return
  }
  const data = tab4DataList.value.filter(
    (x) =>
      x.sogoKadaiNo ===
      local.Or06954.tab3DataList[
        local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
      ].sogoKadaiNo
  )[selectRowIndex.value]
  // 期間開始日
  let kikanSYmd = Or06954Const.EMPTY_STRING
  // 期間終了日
  let kikanEYmd = Or06954Const.EMPTY_STRING
  // 期間
  let kikanKnj = Or06954Const.EMPTY_STRING
  // 初期設定マスタの情報.計画表様式が「0:A3、2:A4-2枚」の場合
  if (
    localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_0 ||
    localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_2
  ) {
    // 期間開始日（本人）
    kikanSYmd = data.kikanSYmd.value
    // 期間終了日（本人）
    kikanEYmd = data.kikanEYmd.value
    // 期間（本人）
    kikanKnj = data.kikanKnj.value
  }
  // 初期設定マスタの情報.計画表様式が「1:A4-3枚」の場合
  else {
    const selectCell = Or06954Logic.event.get(props.uniqueCpId)!.selectCell ?? ''
    // 家族
    if (Or06954Const.FAMILY_CELL.indexOf(selectCell) > 0) {
      // 期間開始日（家族）
      kikanSYmd = data.kikanKazokuS_ymd.value
      // 期間終了日（家族）
      kikanEYmd = data.kikanKazokuE_ymd.value
      // 期間（家族）
      kikanKnj = data.kikanKazokuKnj.value
    }
    // 家族
    else if (Or06954Const.SECURITY_CELL.indexOf(selectCell) > 0) {
      // 期間開始日（保険・地域）
      kikanSYmd = data.kikanHokenS_ymd.value
      // 期間終了日（保険・地域）
      kikanEYmd = data.kikanHokenE_ymd.value
      // 期間（保険・地域）
      kikanKnj = data.kikanHokenKnj.value
    }
    // 本人
    else {
      // 期間開始日（本人）
      kikanSYmd = data.kikanSYmd.value
      // 期間終了日（本人）
      kikanEYmd = data.kikanEYmd.value
      // 期間（本人）
      kikanKnj = data.kikanKnj.value
    }
  }
  if (data) {
    ymdKbn.value = kbn
    let startValue = Or06954Const.EMPTY_STRING
    let endValue = Or06954Const.EMPTY_STRING
    // 初期設定マスタの情報.期間の管理(計画表)が「0:日付で管理」の場合
    if (localOneway.or06954Oneway.itakuKkakKikanFlg === Or06954Const.ITAKU_KKAK_KIKAN_FLG_0) {
      // 初期設定マスタの情報.期間のカレンダー取込(計画表)が「0：?/?～?/?、1：?月?日～?月?日」の場合
      if (
        localOneway.or06954Oneway.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_0 ||
        localOneway.or06954Oneway.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_1
      ) {
        // 開始年月日：処理対象.開始年月日テキストフィールド
        startValue = kikanSYmd
        // 終了年月日：処理対象.期間終了日テキストフィールド
        endValue = kikanEYmd
      } else {
        // 開始年月日：処理対象.開始年月日テキストフィールド
        startValue = kikanSYmd
        // 終了年月日：空
        endValue = Or06954Const.EMPTY_STRING
      }
    }
    // 以外の場合
    else {
      // 初期設定マスタの情報.期間のカレンダー取込(計画表)が「0：?/?～?/?、1：?月?日～?月?日」の場合
      if (
        localOneway.or06954Oneway.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_0 ||
        localOneway.or06954Oneway.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_1
      ) {
        // 開始年月日：処理対象.期間テキストエリアの内容に開始年月日を取得する ※取得できないの場合、システム日付を設定
        startValue = getParseDateValue(
          localOneway.or06954Oneway.itakuKkakYmdFlg,
          kikanKnj,
          kbn === Or06954Const.KBN_TYPE_1
        ).startValue
        // 終了年月日：処理対象.期間テキストエリアの内容に終了年月日を取得する ※取得できないの場合、システム日付を設定
        endValue = getParseDateValue(
          localOneway.or06954Oneway.itakuKkakYmdFlg,
          kikanKnj,
          kbn === Or06954Const.KBN_TYPE_1
        ).endValue
      }
      // 以外の場合
      else {
        // 開始年月日：処理対象.期間テキストエリアの内容に開始年月日を取得する ※取得できないの場合、システム日付を設定
        startValue = getParseDateValue(
          localOneway.or06954Oneway.itakuKkakYmdFlg,
          kikanKnj,
          kbn === Or06954Const.KBN_TYPE_1
        ).startValue
        // 終了年月日：空
        endValue = Or06954Const.EMPTY_STRING
      }
    }

    // セレクトモード
    // 初期設定マスタの情報.期間のカレンダー取込(計画表)が「0：?/?～?/?、1：?月?日～?月?日」の場合、「0：単日指定」
    // 以外の場合、「1：複数日指定」
    localOneway.mo01343Oneway.selectMode =
      localOneway.or06954Oneway.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_0 ||
      localOneway.or06954Oneway.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_1
        ? Or06954Const.SELECT_MODE_1
        : Or06954Const.SELECT_MODE_0
    // カレンダーの表示：「0：通常」
    local.mo01343.calenderDisplay = Or06954Const.CALENDER_DISPLAY_0
    // 開始年月日、終了年月日：
    local.mo01343.value = startValue ?? Or06954Const.EMPTY_STRING
    local.mo01343.endValue = endValue ?? Or06954Const.EMPTY_STRING
    isDateOpen.value = true
    //共通部品カレンダー選択（年月日）を開く。
    local.mo01343.mo00024 = { isOpen: true }
  }
}

/**
 * 表示順変更計画表ボタン押下
 *
 */
function openSortDialog() {
  // 操作区分 = 3:削除の場合
  // 表示用「計画対象期間」情報.ページング区分が0:なしの場合
  // 行総数が0件の場合
  if (
    props.onewayModelValue.operaFlg === Or06954Const.ACTION_KBN_DELETE ||
    props.onewayModelValue.pagingFlg === Or06954Const.PAGING_FLG_NONE ||
    selectRowIndex.value < 0 ||
    tab4DataList.value.filter(
      (x) =>
        x.sogoKadaiNo ===
        local.Or06954.tab3DataList[
          local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
        ].sogoKadaiNo
    ).length === 0
  ) {
    return
  }
  // モード
  localOneway.or28543Oneway.mode =
    localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_0
      ? Or06954Const.MODE.A3
      : localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_2
        ? Or06954Const.MODE.A4_2
        : Or06954Const.MODE.A4_3
  // 計画表様式
  localOneway.or28543Oneway.isPlanFormatFlag = localOneway.or06954Oneway.itakuKkakPrtFlg
  // 期間の管理
  localOneway.or28543Oneway.isPeriodManagementFlag = localOneway.or06954Oneway.itakuKkakKikanFlg
  // 期間のカレンダー取込
  localOneway.or28543Oneway.calendarImportByPeriod = localOneway.or06954Oneway.itakuKkakYmdFlg
  localOneway.or28543Oneway.indexList = translateTableData(
    tab4DataList.value.filter(
      (x) =>
        x.sogoKadaiNo ===
        local.Or06954.tab3DataList[
          local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
        ].sogoKadaiNo
    )
  ).map((x) => {
    return {
      // 表示順
      sort: Number(x.seqNo),
      // 番号
      goalSupportPoints: x.shienPointKnj,
      // インフォーマル
      informalSupport: x.infoServiceKnj,
      // 具体的な支援の内容（本人）
      selfSupportContent: x.infoServiceKnj,
      // 具体的な支援の内容（家族）
      familySupportContent: x.kazokuServiceKnj,
      // 介護保険ｻｰﾋﾞｽ地域支援事業…
      insuranceRegionalSupport: x.infoServiceKnj,
      // 具体的な支援の内容（保険・地域）
      insuranceRegionalSupportContent: x.hokenServiceKnj,
      // ※1（本人）
      noteSelf: x.svKbn,
      //  ※1（家族）
      noteFamily: x.svKazokuKbn,
      //  ※1（保険・地域）
      noteInsuranceRegional: x.svHokenKbn,
      // サービス種別
      serviceType: x.svShubetuKnj,
      // サービス種別（本人）
      serviceTypeSelf: x.svShubetuKnj,
      // サービス種別（家族）
      serviceTypeFamily: x.svShubetuKazokuKnj,
      // サービス種別（保険・地域）
      serviceTypeInsuranceRegional: x.svShubetuHoken_knj,
      // 事業所
      office: x.svJigyoKnj,
      // ｻｰﾋﾞｽ提供者（事業所）（本人）
      serviceProviderSelf: x.svJigyoKnj,
      // ｻｰﾋﾞｽ提供者（事業所）（家族）
      serviceProviderFamily: x.svJigyoKazokuKnj,
      // ｻｰﾋﾞｽ提供者（事業所）（保険・地域）
      serviceProviderInsuranceRegional: x.svJigyoHoken_knj,
      // 頻度（本人）
      frequencySelf: x.hindoKnj,
      // 頻度（家族）
      frequencyFamily: x.hindoKazokuKnj,
      // 頻度（保険・地域）
      frequencyInsuranceRegional: x.hindoHokenKnj,
      // 期間
      period: x.kikanKnj,
      // 期間開始日
      periodStartDate: x.kikanSYmd,
      // 期間終了日
      periodEndDate: x.kikanEYmd,
      // 期間（本人）
      periodSelf: x.kikanKnj,
      // 期間開始日（本人）
      periodSelfStartDate: x.kikanSYmd,
      // 期間終了日（本人）
      periodSelfEndDate: x.kikanEYmd,
      // 期間（家族）
      periodFamily: x.kikanKazokuKnj,
      // 期間開始日（家族）
      periodFamilyStartDate: x.kikanKazokuS_ymd,
      // 期間終了日（家族）
      periodFamilyEndDate: x.kikanKazokuE_ymd,
      // 期間（保険・地域）
      periodInsuranceRegional: x.kikanHokenKnj,
      // 期間開始日（保険・地域）
      periodInsuranceRegionalStartDate: x.kikanHokenS_ymd,
      // 期間終了日（保険・地域）
      periodInsuranceRegionalEndDate: x.kikanHokenE_ymd,
      // ＩＤ
      ID: x.plan14Id,
    } as ShowData
  })
  Or28543Logic.state.set({
    uniqueCpId: or28543.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * サービス種別アイコンボタン押下
 *
 */
function openServiceDialog() {
  // 操作区分 = 3:削除の場合
  // 画面.「支援計画」一覧で行が選択されていない場合
  if (
    props.onewayModelValue.operaFlg === Or28451Const.ACTION_TYPE.DELETE &&
    selectRowIndex.value < 0
  ) {
    return
  }
  const selectedData = tab4DataList.value.filter(
    (x) =>
      x.sogoKadaiNo ===
      local.Or06954.tab3DataList[
        local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
      ].sogoKadaiNo
  )[selectRowIndex.value]
  // 初期設定マスタの情報.計画表様式が「0:A3、2:A4-2枚」の場合
  if (
    localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_0 ||
    localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_2
  ) {
    // サービス種別：画面.「支援計画」一覧での選択行の「サービス種別ステキストエリア」
    localOneway.or27487Oneway.serviceKbn = selectedData.svShubetuKnj.value
    // 事業所：画面.「支援計画」一覧での選択行の「事業所ステキストエリア」
    localOneway.or27487Oneway.office = selectedData.svJigyoKnj.value
    // 計画表:保険サービス(事業所名) ：初期設定マスタの情報.保険サービス（事業所名）(計画表)
    localOneway.or27487Oneway.planInsuranceService = localOneway.or06954Oneway.itakuKkakHsJigyoFlg
    // 計画表:事業所単位 計画表様式:0：初期設定マスタの情報.計画表様式
    localOneway.or27487Oneway.planOfficeType = localOneway.or06954Oneway.itakuKkakPrtFlg
  }
  // 初期設定マスタの情報.計画表様式が「A4-3枚」の場合
  else if (localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_1) {
    // 本人の取組サービス種別：画面.「支援計画」一覧での選択行の「サービス種別（本人） テキストエリア」
    localOneway.or27487Oneway.personServiceType = selectedData.svShubetuKnj.value
    // 本人の取組事業所：画面.「支援計画」一覧での選択行の「事業所（本人） テキストエリア」
    localOneway.or27487Oneway.personOffice = selectedData.svJigyoKnj.value
    // 家族・地域の支援、民間サービス等サービス種別：画面.「支援計画」一覧での選択行の「サービス種別（家族） テキストエリア」
    localOneway.or27487Oneway.familyServiceType = selectedData.svShubetuKazokuKnj.value
    // 家族・地域の支援、民間サービス等事業所：画面.「支援計画」一覧での選択行の「事業所（家族）テキストエリア」
    localOneway.or27487Oneway.familyOffice = selectedData.svJigyoKazokuKnj.value
    // 介護保険サービス地域支援事業区市町村サービスサービス種別：画面.「支援計画」一覧での選択行の「サービス種別（保険・地域） テキストエリア」
    localOneway.or27487Oneway.insuranceServiceType = selectedData.svShubetuHoken_knj.value
    // 介護保険サービス地域支援事業区市町村サービス事業所：画面.「支援計画」一覧での選択行の「事業所（保険・地域）テキストエリア」
    localOneway.or27487Oneway.insuranceOffice = selectedData.svJigyoHoken_knj.value
    // 計画表:保険サービス(事業所名) ：初期設定マスタの情報.保険サービス（事業所名）(計画表)
    localOneway.or27487Oneway.planInsuranceService = localOneway.or06954Oneway.itakuKkakHsJigyoFlg
    // 計画表:事業所単位 計画表様式:0：初期設定マスタの情報.計画表様式
    localOneway.or27487Oneway.planOfficeType = localOneway.or06954Oneway.itakuKkakPrtFlg
  }

  // Or27487のダイアログ開閉状態を更新する
  Or27487Logic.state.set({
    uniqueCpId: or27487.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 曜日取込アイコンボタン押下
 *
 */
function openDayDialog() {
  // 操作区分 = 3:削除の場合
  // 画面.「支援計画」一覧で行が選択されていない場合
  if (
    props.onewayModelValue.operaFlg === Or28451Const.ACTION_TYPE.DELETE &&
    selectRowIndex.value < 0
  ) {
    return
  }
  Or28582Logic.state.set({
    uniqueCpId: or28582.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 保険サービス取込アイコンボタン押下
 *
 */
function openInsuranceDialog() {
  isInsuranceClick.value = !isInsuranceClick.value
  setEvent({
    isTab4InsuranceClick: isInsuranceClick.value,
  })
}

/**
 * 目標とする生活（１年）入力支援アイコンボタン押下
 *
 * @param titlePosition - タイトル
 *
 * @param specialPosition - 特別な判定条件
 *
 * @param index - index
 */
function openDialog(
  titlePosition: string,
  specialPosition = Or06954Const.EMPTY_STRING,
  index = Or06954Const.UNSELECTED_INDEX
) {
  // 操作区分 = 3:削除の場合
  // 画面.「支援計画」一覧で行が選択されていない場合
  if (
    props.onewayModelValue.operaFlg === Or28451Const.ACTION_TYPE.DELETE &&
    selectRowIndex.value < 0 &&
    titlePosition !== Or06954Const.TITLE_POSITION.ABOUT_THE_TARGET_POINTS_OF_SUPPORT
  ) {
    return
  }
  popUpRowIndex.value = index
  const selectedData = tab4DataList.value.filter(
    (x) =>
      x.sogoKadaiNo ===
      local.Or06954.tab3DataList[
        local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
      ].sogoKadaiNo
  )[index > Or06954Const.UNSELECTED_INDEX ? index : selectRowIndex.value]
  if (selectedData) {
    // 共通情報.利用者ID
    localOneway.or10883Oneway.userId = systemCommonsStore.getUserId ?? ''
    // 大分類CD:40
    localOneway.or10883Oneway.t1Cd = Or06954Const.CD_40
    // 中分類CD:10
    localOneway.or10883Oneway.t2Cd = Or06954Const.CD_10
    // 小分類CD:1/2/3
    localOneway.or10883Oneway.t3Cd =
      localOneway.or06954Oneway.kycFlg !== Or06954Const.KYC_FLG_2 &&
      titlePosition ===
        Or06954Const.TITLE_POSITION.LONG_TERM_CARE_INSURANCE_SERVICE_OR_COMMUNITY_SERVICE
        ? Or06954Const.CD_6
        : titlePosition
    let selectCell = Or06954Logic.event.get(props.uniqueCpId)!.selectCell
    if (
      titlePosition === Or06954Const.TITLE_POSITION.FREQUENCY &&
      selectCell !== Or06954Const.CELL.CELL_HINDO_KNJ &&
      selectCell !== Or06954Const.CELL.CELL_HINDO_KAZOKU_KNJ &&
      selectCell !== Or06954Const.CELL.CELL_HINDO_HOKEN_KNJ
    ) {
      selectCell = Or06954Const.CELL.CELL_HINDO_KNJ
    }
    if (
      titlePosition === Or06954Const.TITLE_POSITION.PERIOD &&
      specialPosition === Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP &&
      selectCell !== Or06954Const.CELL.CELL_KIKAN_KNJ &&
      selectCell !== Or06954Const.CELL.CELL_KIKAN_KAZOKU_KNJ &&
      selectCell !== Or06954Const.CELL.CELL_KIKAN_HOKEN_KNJ
    ) {
      selectCell = Or06954Const.CELL.CELL_KIKAN_KNJ
    }
    // タイトル
    localOneway.or10883Oneway.title = setTitle(titlePosition, specialPosition, selectCell)
    // テーブル名:"kyc_tuc_plan14"
    localOneway.or10883Oneway.tableName = Or06954Const.TABLE_NAME
    // カラム名
    localOneway.or10883Oneway.columnName = setColumnName(titlePosition, specialPosition, selectCell)
    // 入力値
    localOneway.or10883Oneway.inputContents = setInputContents(
      selectedData,
      titlePosition,
      specialPosition,
      selectCell as Or06954Const.InputType
    )

    // GUI01109 入力支援【目標とする生活（１年）】画面をポップアップで起動する
    Or10883Logic.state.set({
      uniqueCpId: or10883.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * タイトルの設定
 *
 * @param titlePosition  - タイトル
 *
 * @param specialPosition - 特別な判定条件
 *
 * @param selectCell - 選択した入力ボックス
 */
function setTitle(
  titlePosition: string,
  specialPosition = Or06954Const.EMPTY_STRING,
  selectCell = Or06954Const.EMPTY_STRING
) {
  let value = ''
  switch (titlePosition) {
    // 1:"目標についての支援のポイントアイコンボタン"
    case Or06954Const.TITLE_POSITION.ABOUT_THE_TARGET_POINTS_OF_SUPPORT:
      // 初期設定マスタの情報.計画表様式が「0:A3」の場合
      if (localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_0) {
        // ・"目標に付いての支援のポイント"
        value = Or06954Const.TITLE.TITLE_1_0
      } else {
        // ・"目標についての支援のポイント"
        value = Or06954Const.TITLE.TITLE_1_1
      }
      break
    // 2:"本人等のセルフケアや家族の支援、インフォーマルサービス"
    case Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT:
      if (specialPosition === Or06954Const.TITLE_POSITION.ABOUT_THE_TARGET_POINTS_OF_SUPPORT_SP) {
        value = Or06954Const.TITLE.TITLE_2_SP
      } else if (
        specialPosition === Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP
      ) {
        value = Or06954Const.TITLE.TITLE_2_SP_1
      } else {
        value = Or06954Const.TITLE.TITLE_2
      }
      break
    // 3:"介護保険サービスまたは地域支援事業アイコンボタン"
    case Or06954Const.TITLE_POSITION.LONG_TERM_CARE_INSURANCE_SERVICE_OR_COMMUNITY_SERVICE:
      if (
        specialPosition ===
        Or06954Const.TITLE_POSITION.LONG_TERM_CARE_INSURANCE_SERVICE_OR_COMMUNITY_SERVICE_SP
      ) {
        value = Or06954Const.TITLE.TITLE_3_SP
      } else {
        value = Or06954Const.TITLE.TITLE_3
      }
      break
    // 9:"期間アイコンボタン"
    case Or06954Const.TITLE_POSITION.PERIOD:
      if (specialPosition === Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP) {
        // 処理対象.期間テキストエリアが「期間（家族）テキストエリア」の場合
        if (selectCell === Or06954Const.CELL.CELL_KIKAN_KAZOKU_KNJ) {
          // "期間：家族等"
          value = Or06954Const.TITLE.TITLE_9_2
        }
        // 処理対象.期間テキストエリアが「期間（保険・地域）テキストエリ」の場合
        else if (selectCell === Or06954Const.CELL.CELL_KIKAN_HOKEN_KNJ) {
          // "期間：保険等
          value = Or06954Const.TITLE.TITLE_9_3
        }
        // 処理対象.期間テキストエリアが「期間（本人）テキストエリア」の場合
        else if (selectCell === Or06954Const.CELL.CELL_KIKAN_KNJ) {
          // "期間：本人"
          value = Or06954Const.TITLE.TITLE_9_1
        }
      } else {
        value = Or06954Const.TITLE.TITLE_9
      }
      break
    // 10:"頻度"
    case Or06954Const.TITLE_POSITION.FREQUENCY:
      // 処理対象.頻度テキストエリアが「頻度（本人）テキストエリア」の場合
      if (selectCell === Or06954Const.CELL.CELL_HINDO_KNJ) {
        // "期間：本人"
        value = Or06954Const.TITLE.TITLE_10_1
      }
      // 処理対象.頻度テキストエリアが「頻度（家族）テキストエリア」の場合
      else if (selectCell === Or06954Const.CELL.CELL_HINDO_KAZOKU_KNJ) {
        // "期間：家族等"
        value = Or06954Const.TITLE.TITLE_10_2
      }
      // 処理対象.頻度テキストエリアが「頻度（保険・地域）テキストエリ」の場合
      else if (selectCell === Or06954Const.CELL.CELL_HINDO_HOKEN_KNJ) {
        // "期間：保険等
        value = Or06954Const.TITLE.TITLE_10_3
      }
      break
    default:
    // 処理なし
  }
  return value
}

/**
 * カラム名の設定
 *
 * @param titlePosition  - タイトル
 *
 * @param specialPosition - 特別な判定条件
 *
 * @param selectCell - 選択した入力ボックス
 */
function setColumnName(
  titlePosition: string,
  specialPosition = Or06954Const.EMPTY_STRING,
  selectCell = Or06954Const.EMPTY_STRING
) {
  let value = Or06954Const.EMPTY_STRING
  switch (titlePosition) {
    // 1:"目標についての支援のポイントアイコンボタン"
    case Or06954Const.TITLE_POSITION.ABOUT_THE_TARGET_POINTS_OF_SUPPORT:
      // "shien_point_knj"
      value = Or06954Const.COLUMN_NAME_SHIEN_POINT_KNJ
      break
    // 2:"本人等のセルフケアや家族の支援、インフォーマルサービス"
    case Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT:
      if (specialPosition === Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP) {
        // kazoku_service_knj
        value = Or06954Const.COLUMN_NAME_KAZOKU_SERVICE_KNJ
      } else {
        // "info_service_knj"
        value = Or06954Const.COLUMN_NAME_INFO_SERVICE_KNJ
      }
      break
    // 3:"介護保険サービスまたは地域支援事業アイコンボタン"
    case Or06954Const.TITLE_POSITION.LONG_TERM_CARE_INSURANCE_SERVICE_OR_COMMUNITY_SERVICE:
      // "hoken_service_knj"
      value = Or06954Const.COLUMN_NAME_HOKEN_SERVICE_KNJ
      break
    // 9:"期間アイコンボタン"
    case Or06954Const.TITLE_POSITION.PERIOD:
      if (specialPosition === Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP) {
        // 処理対象.期間テキストエリアが「期間（本人）テキストエリア」の場合
        if (selectCell === Or06954Const.CELL.CELL_KIKAN_KNJ) {
          // "kikan_knj"
          value = Or06954Const.COLUMN_NAME_KIKAN_KNJ
        }
        // 処理対象.期間テキストエリアが「期間（家族）テキストエリア」の場合
        else if (selectCell === Or06954Const.CELL.CELL_KIKAN_KAZOKU_KNJ) {
          // "kikan_kazoku_knj"
          value = Or06954Const.COLUMN_NAME_KIKAN_KAZOKU_KNJ
        }
        // 処理対象.期間テキストエリアが「期間（保険・地域）テキストエリ」の場合
        else if (selectCell === Or06954Const.CELL.CELL_KIKAN_HOKEN_KNJ) {
          // "kikan_hoken_knj"
          value = Or06954Const.COLUMN_NAME_KIKAN_HOKEN_KNJ
        }
      } else {
        // "kikan_knj"
        value = Or06954Const.COLUMN_NAME_KIKAN_KNJ
      }
      break
    // 10:"頻度"
    case Or06954Const.TITLE_POSITION.FREQUENCY:
      // 処理対象.頻度テキストエリアが「頻度（本人）テキストエリア」の場合
      if (selectCell === Or06954Const.CELL.CELL_HINDO_KNJ) {
        // "hindo_knj"
        value = Or06954Const.COLUMN_NAME_HINDO_KNJ
      }
      // 処理対象.頻度テキストエリアが「頻度（家族）テキストエリア」の場合
      else if (selectCell === Or06954Const.CELL.CELL_HINDO_KAZOKU_KNJ) {
        // "hindo_kazoku_knj"
        value = Or06954Const.COLUMN_NAME_HINDO_KAZOKU_KNJ
      }
      // 処理対象.頻度テキストエリアが「頻度（保険・地域）テキストエリ」の場合
      else if (selectCell === Or06954Const.CELL.CELL_HINDO_HOKEN_KNJ) {
        // "hindo_hoken_knj
        value = Or06954Const.COLUMN_NAME_HINDO_HOKEN_KNJ
      }
      break
    default:
    // 処理なし
  }
  return value
}

/**
 * 入力値の設定
 *
 * @param tableData - 表データ
 *
 * @param titlePosition  - タイトル
 *
 * @param specialPosition - 特別な判定条件
 *
 * @param selectCell - 選択した入力ボックス
 */
function setInputContents(
  tableData: TableData,
  titlePosition: string,
  specialPosition = Or06954Const.EMPTY_STRING,
  selectCell?: Or06954Const.InputType
) {
  let value = Or06954Const.EMPTY_STRING
  switch (titlePosition) {
    // 1:"目標についての支援のポイントアイコンボタン"
    case Or06954Const.TITLE_POSITION.ABOUT_THE_TARGET_POINTS_OF_SUPPORT:
      // 画面.「支援計画」一覧での選択行の「目標についての支援のポイントテキストエリア」
      value = tableData.shienPointKnj.value
      break
    // 2:"本人等のセルフケアや家族の支援、インフォーマルサービス"
    case Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT:
      if (specialPosition === Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP) {
        // 画面.「支援計画」一覧での選択行の「具体的な支援の内容（家族） テキストエリア」
        value = tableData.kazokuServiceKnj.value
      } else {
        // 画面.「支援計画」一覧での選択行の「インフォーマルサービステキストエリア」
        value = tableData.infoServiceKnj.value
      }
      break
    // 3:"介護保険サービスまたは地域支援事業アイコンボタン"
    case Or06954Const.TITLE_POSITION.LONG_TERM_CARE_INSURANCE_SERVICE_OR_COMMUNITY_SERVICE:
      // 画面.「支援計画」一覧での選択行の「介護保険サービスまたは地域支援事業 テキストエリア」
      value = tableData.hokenServiceKnj.value
      break
    // 9:"期間アイコンボタン"
    case Or06954Const.TITLE_POSITION.PERIOD:
      // 画面.「支援計画」一覧での選択行の「期間テキストエリア」
      if (specialPosition === Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP) {
        value = tableData[selectCell ?? Or06954Const.CELL.CELL_KIKAN_KNJ].value
      } else {
        value = tableData.kikanKnj.value
      }
      break
    // 10:"頻度"
    case Or06954Const.TITLE_POSITION.FREQUENCY:
      // 入力値: 処理対象.頻度テキストエリア
      value = tableData[selectCell ?? Or06954Const.CELL.CELL_HINDO_KNJ].value
      break
    default:
    // 処理なし
  }
  return value
}

/**
 * 曜日取込の戻り値の設定処理
 *
 * @param value - 戻り値
 */
function dayChange(value: DayResult) {
  const result = convertNumbersToDays(value.listData)
  let selectCell = Or06954Logic.event.get(props.uniqueCpId)!.selectCell
  if (
    selectCell !== Or06954Const.CELL.CELL_HINDO_KNJ &&
    selectCell !== Or06954Const.CELL.CELL_HINDO_KAZOKU_KNJ &&
    selectCell !== Or06954Const.CELL.CELL_HINDO_HOKEN_KNJ
  ) {
    selectCell = Or06954Const.CELL.CELL_HINDO_KNJ
  }
  tab4DataList.value[selectRowIndex.value][selectCell as Or06954Const.HindoType] = {
    value: result === Or06954Const.EMPTY_STRING ? value.textData : result,
  }
}

/**
 * 返されたdaysデータを要求されたフォーマットに変換する
 *
 * @param days - 戻り値
 */
function convertNumbersToDays(days: boolean[]): string {
  const selectedDays: string[] = []

  for (let i = 0; i < days.length; i++) {
    if (days[i]) {
      selectedDays.push(Or06954Const.DAYS_OF_WEEK[i])
    }
  }

  return selectedDays.join(Or06954Const.POINT)
}

/**
 * 対象値から日付の取得
 *
 * @param calendarImport - 期間のカレンダー※0:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日
 *
 * @param dateValue - 表示値
 *
 * @param keepYear - 年表示
 *
 * @returns 日付の取得 { startValue, endValue }
 */
function getParseDateValue(
  calendarImport: string,
  dateValue: string | undefined,
  keepYear = true
): {
  startValue: string
  endValue: string
} {
  // 取得できないの場合、システム日付を設定
  if (!dateValue) {
    return {
      startValue: systemCommonsStore.getSystemDate!,
      endValue: '',
    }
  }

  // 期間のカレンダー
  const rangeMatch = getDateFromRange(calendarImport, dateValue, keepYear)

  if (rangeMatch.length > 1) {
    return {
      startValue: rangeMatch[0],
      endValue: rangeMatch[1],
    }
  }

  return {
    startValue: dateValue,
    endValue: '',
  }
}

/**
 * 期間のカレンダー
 *
 * @param calendarImport - 期間のカレンダー※0:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日
 *
 * @param str - 期間
 *
 * @param keepYear - 年表示
 */
function getDateFromRange(calendarImport: string, str?: string, keepYear = true) {
  if (!str) {
    return []
  }
  const [start, end] = str.split(SPACE_WAVE, 2).map((s) => s.trim())
  switch (calendarImport) {
    // 初期設定マスタの情報.期間のカレンダー取込(計画表)が「0：?/?～?/?、1：?月?日～?月?日」の場合
    case Or06954Const.ITAKU_KKAK_YMD_FLG_0:
    case Or06954Const.ITAKU_KKAK_YMD_FLG_1:
      return [getDate(start, calendarImport, keepYear), getDate(end, calendarImport, keepYear)]
    // 以外の場合
    case Or06954Const.ITAKU_KKAK_YMD_FLG_2:
    case Or06954Const.ITAKU_KKAK_YMD_FLG_3:
      return [getDate(str, calendarImport, keepYear)]
    default:
      return str
  }
}

/**
 * yyyy/MM/ddを取得
 * ※0:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日
 *
 * @param formatDate - 期間
 *
 * @param calendarImport - 期間のカレンダー取込み
 *
 * @param keepYear - 年表示
 */
function getDate(formatDate: string, calendarImport: string, keepYear: boolean) {
  let date = ''
  switch (calendarImport) {
    case Or06954Const.ITAKU_KKAK_YMD_FLG_0:
    case Or06954Const.ITAKU_KKAK_YMD_FLG_2:
      date = keepYear ? formatDate : addYear(formatDate)
      break
    case Or06954Const.ITAKU_KKAK_YMD_FLG_1:
    case Or06954Const.ITAKU_KKAK_YMD_FLG_3: {
      const [year, month, day] = formatDateString(formatDate).split('/')
      date = keepYear ? `${year}/${month}/${day}` : addYear(`${month}/${day}`)
      break
    }
    default:
      break
  }
  return (keepYear && !isNaN(new Date(date).getTime())) ||
    (!keepYear && !isNaN(new Date(date).getTime()))
    ? date
    : ''
}

/**
 * 年を設定
 *
 * @param formatDate - 日付
 */
function addYear(formatDate: string) {
  const [year, _month, _day] = (systemCommonsStore.getSystemDate ?? '').split('/')
  return `${year}/${formatDate}`
}

/**
 * 時間を文字列に変換
 *
 * @param dateStr - 時間
 */
function formatDateString(dateStr: string): string {
  // 現在の年を取得
  const [currentYear, _month, _day] = (systemCommonsStore.getSystemDate ?? '').split('/')

  if (
    dateStr &&
    (dateStr.includes(Or06954Const.YEAR) ||
      dateStr.includes(Or06954Const.MONTH) ||
      dateStr.includes(Or06954Const.DAY))
  ) {
    // 年の抽出
    const yearMatch = /(\d+)年/.exec(dateStr)
    const year = yearMatch ? parseInt(yearMatch[1]) : currentYear

    // 抽出月と日
    const monthMatch = /(\d+)月/.exec(dateStr)
    const dayMatch = /(\d+)日/.exec(dateStr)

    if (!monthMatch || !dayMatch) {
      return ''
    }

    const month = parseInt(monthMatch[1])
    const day = parseInt(dayMatch[1])

    // YYYY/MM/DDに書式設定
    return `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`
  }

  // スラッシュ分割を処理する日付フォーマット
  if (dateStr?.includes('/')) {
    const parts = dateStr.split('/').map((part) => parseInt(part))

    // 2つの部分（月/日）のみの場合は、年を補完
    if (parts.length === 2) {
      return `${currentYear}/${parts[0].toString().padStart(2, '0')}/${parts[1].toString().padStart(2, '0')}`
    }

    // 3つのセクション（年/月/日）がある場合は、直接書式設定
    if (parts.length === 3) {
      return `${parts[0]}/${parts[1].toString().padStart(2, '0')}/${parts[2].toString().padStart(2, '0')}`
    }
  }

  return ''
}

/**
 * 日付をフォーマット
 * ※0:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日
 *
 * @param date - 日付
 *
 * @param formatType - 期間のカレンダー取込み
 *
 * @param keepYear - 年表示
 */
function formatDate(date: string | undefined, formatType: string, keepYear = false): string {
  if (!date) {
    return Or06954Const.EMPTY_STRING
  }
  const [_year, month, day] = date.split('/')
  if (is216Click.value) {
    return convertSeirekiToWareki(date) ?? ''
  }
  switch (formatType) {
    case Or06954Const.ITAKU_KKAK_YMD_FLG_0:
    case Or06954Const.ITAKU_KKAK_YMD_FLG_2:
      if (keepYear) {
        return convertSeirekiToWareki(date) ?? ''
      } else {
        return `${month}/${day}`
      }
    case Or06954Const.ITAKU_KKAK_YMD_FLG_1:
    case Or06954Const.ITAKU_KKAK_YMD_FLG_3:
      if (keepYear) {
        return convertToJapaneseEra(new Date(date)) ?? ''
      } else {
        return `${month.startsWith('0') ? month.slice(1) : month}${Or06954Const.MONTH}${day.startsWith('0') ? day.slice(1) : day}${Or06954Const.DAY}`
      }
    default:
      return date
  }
}

/**
 * 西暦を和暦に変換する
 *
 * @param date - 変換対象の西暦Dateオブジェクト
 *
 * @returns 和暦表記の日付文字列（例: "令和5年7月4日"）
 */
function convertToJapaneseEra(date: Date) {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  // 日本の元号を定義
  const eras = [
    { name: '令和', start: new Date(2019, 4, 1) }, // 2019年5月1日
    { name: '平成', start: new Date(1989, 0, 8) }, // 1989年1月8日
    { name: '昭和', start: new Date(1926, 11, 25) }, // 1926年12月25日
    { name: '大正', start: new Date(1912, 6, 30) }, // 1912年7月30日
    { name: '明治', start: new Date(1868, 0, 25) }, // 1868年1月25日
  ]

  for (const era of eras) {
    if (date >= era.start) {
      const eraYear = year - era.start.getFullYear() + 1
      return `${era.name}${eraYear}年${month}月${day}日`
    }
  }

  return `${year}年${month}月${day}日` // 明治より前の日付
}

/**
 * 年月日の設定
 *
 * @param newValue - 新しい戻り値
 *
 * @param kikanSYmd - 期間開始日
 *
 * @param kikanEYmd - 期間終了日
 *
 * @param kikanKnj - 期間
 */
function setDate(
  newValue: Mo01343Type,
  kikanSYmd: Or06954Const.KikanSType,
  kikanEYmd: Or06954Const.KikanEType,
  kikanKnj: Or06954Const.KikanType
) {
  const selectedData = tab4DataList.value.filter(
    (x) =>
      x.sogoKadaiNo ===
      local.Or06954.tab3DataList[
        local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
      ].sogoKadaiNo
  )[selectRowIndex.value]
  // 開始年月日
  const startYmd = formatDate(
    newValue.value,
    localOneway.or06954Oneway.itakuKkakYmdFlg,
    ymdKbn.value === Or06954Const.KBN_TYPE_1
  )
  // 終了年月日
  const endYmd = formatDate(
    newValue.endValue,
    localOneway.or06954Oneway.itakuKkakYmdFlg,
    ymdKbn.value === Or06954Const.KBN_TYPE_1
  )
  // 初期設定マスタの情報.期間の管理(計画表)が「0:日付で管理」の場合
  if (localOneway.or06954Oneway.itakuKkakKikanFlg === Or06954Const.ITAKU_KKAK_KIKAN_FLG_0) {
    // 初期設定マスタの情報.期間のカレンダー取込(計画表)が「0：?/?～?/?、1：?月?日～?月?日」の場合
    if (
      localOneway.or06954Oneway.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_0 ||
      localOneway.or06954Oneway.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_1
    ) {
      // 処理対象.開始年月日テキストフィールド：戻り値.開始年月日
      selectedData[kikanSYmd].value = startYmd
      // 処理対象.期間終了日テキストフィールド：戻り値.終了年月日
      selectedData[kikanEYmd].value = endYmd
    } else {
      // 処理対象.開始年月日テキストフィールド：戻り値.開始年月日
      selectedData[kikanSYmd].value = startYmd
      // 終了年月日：空
      selectedData[kikanEYmd].value = Or06954Const.EMPTY_STRING
    }
  }
  // 以外の場合
  else {
    // 初期設定マスタの情報.期間のカレンダー取込(計画表)が「0：?/?～?/?、1：?月?日～?月?日」の場合
    if (
      localOneway.or06954Oneway.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_0 ||
      localOneway.or06954Oneway.itakuKkakYmdFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_1
    ) {
      // 処理対象.期間テキストエリア：戻り値.開始年月日 + 「改行」 + "～" +  改行」 + 戻り値.終了年月日
      selectedData[kikanKnj].value =
        startYmd + Or06954Const.LINE_BREAK + SPACE_WAVE + Or06954Const.LINE_BREAK + endYmd
    }
    // 以外の場合
    else {
      // 処理対象.期間テキストエリア：戻り値.開始年月日
      selectedData[kikanKnj].value = newValue.value
    }
  }
}

/**
 * 表示用「支援計画」リストデータ再取得フラグの監視
 */
watch(
  () => refValue.value,
  (newValue) => {
    local.Or06954 = newValue!
    // テーブル初期化
    initTable()
  }
)

/**
 * 行選択上下アイコンボタンを監視
 */
watch(
  () => Or21816Logic.event.get(or21816.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.upEventFlg) {
      // 「前の行選択」押下
      preLine()
      Or21816Logic.event.set({
        uniqueCpId: or21816.value.uniqueCpId,
        events: { upEventFlg: false },
      })
    }
    if (newValue.downEventFlg) {
      // 「次の行選択」押下
      nextLine()
      Or21816Logic.event.set({
        uniqueCpId: or21816.value.uniqueCpId,
        events: { downEventFlg: false },
      })
    }
  }
)

/**
 * 総合的課題番号を監視
 */
watch(
  () =>
    tab4DataList.value.filter(
      (x) =>
        x.sogoKadaiNo ===
        local.Or06954.tab3DataList[
          local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
        ].sogoKadaiNo
    ).length,
  (newValue) => {
    localOneway.mo00611OnewayInsert.disabled = newValue === 0
    localOneway.mo00611OnewayDuplicate.disabled = newValue === 0
    localOneway.mo00611OnewayDelete.disabled = newValue === 0
  }
)

/**
 * 表示順変更計画表を監視
 */
watch(
  () => local.Or28543,
  (newValue) => {
    // 古いデータ
    const oldData = cloneDeep(tab4DataList.value).filter(
      (x) =>
        x.sogoKadaiNo ===
        local.Or06954.tab3DataList[
          local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
        ].sogoKadaiNo
    )
    const newSortedData: TableData[] = []
    // 戻り値に基づいて並べ替え
    newValue.sortList.forEach((item) => {
      const originalData = oldData.find((x) => x.plan14Id === item.ID)
      if (originalData) {
        newSortedData.push({ ...originalData, seqNo: item.sort.toString(), plan14Id: item.ID })
      }
    })

    // 未処理データ
    const otherData = cloneDeep(tab4DataList.value).filter(
      (x) =>
        x.sogoKadaiNo !==
        local.Or06954.tab3DataList[
          local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
        ].sogoKadaiNo
    )
    tab4DataList.value = [...otherData, ...newSortedData]

    selectRow(0)
    local.Or06954.tab4DataList = translateTableData(tab4DataList.value)

    refValue.value = local.Or06954
  }
)

/**
 * GUI01098 サービス種別入力支援（予防計画書）は展開の監視
 */
watch(
  () => local.Or27487,
  (newValue) => {
    const selectedData = tab4DataList.value.filter(
      (x) =>
        x.sogoKadaiNo ===
        local.Or06954.tab3DataList[
          local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
        ].sogoKadaiNo
    )[selectRowIndex.value]
    // 初期設定マスタの情報.計画表様式が「0:A3、2:A4-2枚」の場合
    if (
      localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_0 ||
      localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_2
    ) {
      // サービス種別ステキストエリア：戻り値.サービス種別内容
      selectedData.svShubetuKnj = {
        value: newValue.serviceModelValue,
      }
      // 事業所ステキストエリア：戻り値.事業所内容
      selectedData.svJigyoKnj = {
        value: newValue.officeModelValue,
      }
    }
    // 初期設定マスタの情報.計画表様式が「A4-3枚」の場合
    else if (localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_1) {
      // サービス種別（本人） テキストエリア：戻り値.本人の取組サービス種別
      selectedData.svShubetuKnj = {
        value: newValue.personServiceModelValue,
      }
      // 事業所（本人） テキストエリア：戻り値.本人の取組事業所
      selectedData.svJigyoKnj = {
        value: newValue.personOfficeModelValue,
      }
      // サービス種別（家族） テキストエリア：戻り値.家族・地域の支援、民間サービス等サービス種別
      selectedData.svShubetuKazokuKnj = {
        value: newValue.familyServiceModelValue,
      }
      // 事業所（家族）テキストエリア：戻り値.家族・地域の支援、民間サービス等事業所
      selectedData.svJigyoKazokuKnj = {
        value: newValue.familyOfficeModelValue,
      }
      // サービス種別（保険・地域） テキストエリア：戻り値.介護保険サービス地域支援事業区市町村サービスサービス種別
      selectedData.svShubetuHoken_knj = {
        value: newValue.preventiveServiceModelValue,
      }
      // 事業所（保険・地域）テキストエリア：戻り値.介護保険サービス地域支援事業区市町村サービス事業所
      selectedData.svJigyoHoken_knj = {
        value: newValue.preventiveOfficeModelValue,
      }
    }
    local.Or06954.tab4DataList = translateTableData(tab4DataList.value)

    refValue.value = local.Or06954
  }
)

/**
 * 入力支援【目標とする生活（１年）】は展開の監視
 */
watch(
  () => local.Or10883,
  (newValue) => {
    if (newValue) {
      const keyOfTab4Data = removeUnderscoreAndCapitalize(
        localOneway.or10883Oneway.columnName ?? Or06954Const.EMPTY_STRING
      ) as Or06954Const.InputSupportType
      if (keyOfTab4Data) {
        tab4DataList.value[
          popUpRowIndex.value > Or06954Const.UNSELECTED_INDEX
            ? popUpRowIndex.value
            : selectRowIndex.value
        ][keyOfTab4Data] = { value: newValue.naiyo ?? Or06954Const.EMPTY_STRING }
        popUpRowIndex.value = Or06954Const.UNSELECTED_INDEX
        local.Or06954.tab4DataList = translateTableData(tab4DataList.value)

        refValue.value = local.Or06954
      }
    }
  }
)

/**
 * 共通部品カレンダー選択（年月日）の監視
 */
watch(
  () => local.mo01343,
  (newValue) => {
    if (isDateOpen.value) {
      isDateOpen.value = false
      return
    }
    if (newValue) {
      // 初期設定マスタの情報.計画表様式が「0:A3、2:A4-2枚」の場合
      if (
        localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_0 ||
        localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_2
      ) {
        setDate(
          newValue,
          Or06954Const.CELL.CELL_KIKAN_S_YMD,
          Or06954Const.CELL.CELL_KIKAN_E_YMD,
          Or06954Const.CELL.CELL_KIKAN_KNJ
        )
      }
      // 初期設定マスタの情報.計画表様式が「1:A4-3枚」の場合
      else {
        const selectCell = Or06954Logic.event.get(props.uniqueCpId)!.selectCell ?? ''
        // 家族
        if (Or06954Const.FAMILY_CELL.indexOf(selectCell) > 0) {
          setDate(
            newValue,
            Or06954Const.CELL.CELL_KIKAN_KAZOKU_S_YMD,
            Or06954Const.CELL.CELL_KIKAN_KAZOKU_E_YMD,
            Or06954Const.CELL.CELL_KIKAN_KAZOKU_KNJ
          )
        }
        // 家族
        else if (Or06954Const.SECURITY_CELL.indexOf(selectCell) > 0) {
          setDate(
            newValue,
            Or06954Const.CELL.CELL_KIKAN_HOKEN_S_YMD,
            Or06954Const.CELL.CELL_KIKAN_HOKEN_E_YMD,
            Or06954Const.CELL.CELL_KIKAN_HOKEN_KNJ
          )
        }
        // 本人
        else {
          setDate(
            newValue,
            Or06954Const.CELL.CELL_KIKAN_S_YMD,
            Or06954Const.CELL.CELL_KIKAN_E_YMD,
            Or06954Const.CELL.CELL_KIKAN_KNJ
          )
        }
      }
      is216Click.value = false
      local.Or06954.tab4DataList = translateTableData(tab4DataList.value)

      refValue.value = local.Or06954
    }
  }
)

/**
 * OneWayBind領域データ
 */
watch(
  () => props.onewayModelValue.isCopyMode,
  (newValue) => {
    localOneway.mo01332OnewayMark.disabled = newValue ?? false
  }
)

/**
 * 文字列中のアンダースコア `_` を削除し、アンダースコアの直後の文字を大文字に変換する（キャメルケース変換）
 *
 * @param input - アンダースコアを含む可能性のある入力文字列
 *
 * @returns 変換後のキャメルケース形式の文字列
 */
const removeUnderscoreAndCapitalize = (input: string): string => {
  // アンダースコアで分割して配列に変換
  return (
    input
      .split('_')
      // 空文字列を除去（先頭や連続するアンダースコアを処理）
      .filter((part) => part !== '')
      .map((part, index) => {
        if (index === 0) {
          // 最初の単語は小文字のまま（先頭のアンダースコアを無視）
          return part.toLowerCase()
        } else {
          // 2番目以降の単語は頭文字を大文字に変換
          return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
        }
      })
      // 配列を結合して最終的な文字列を生成
      .join('')
  )
}

/**
 * バリデーション関数
 */
async function isValid() {
  const or06954Valid = (await form.value!.validate()).valid

  return or06954Valid ?? true
}

defineExpose({
  isValid,
})
</script>
<template>
  <div class="container">
    <c-v-row
      no-gutters
      class="mb-4"
    >
      <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayTitle" />
    </c-v-row>
    <c-v-row
      v-if="!props.onewayModelValue.isCopyMode"
      class="top-row"
    >
      <div>
        <!-- 行追加ボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAdd"
          class="mr-2"
          @click="addItem(false)"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.add-row')"
          />
        </base-mo00611>
        <!-- 行挿入ボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayInsert"
          class="mr-2"
          @click="addItem(true)"
        >
        </base-mo00611>
        <!-- 行複写ボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayDuplicate"
          class="mr-2"
          @click="duplicateItem()"
        >
        </base-mo00611>
        <!-- 行削除ボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayDelete"
          class="mr-2"
          @click="deleteItem()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.delete-row')"
          />
        </base-mo00611>
      </div>
      <div class="flex-row">
        <!--表示順-->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewaySort"
          class="mr-2"
          @click="openSortDialog"
        >
        </base-mo00611>
        <!--行選択ラベル-->
        <base-mo00615
          :oneway-model-value="localOneway.mo00615OnewayRow"
          class="row-select"
        />
      </div>
    </c-v-row>
    <!-- 支援計画一覧 -->
    <c-v-form ref="form">
      <c-v-data-table
        fixed-header
        :items="
          tab4DataList.filter(
            (x) =>
              x.sogoKadaiNo ===
              local.Or06954.tab3DataList[
                local.Or06954.tab3SelectRowIndex > -1 ? local.Or06954.tab3SelectRowIndex : 0
              ].sogoKadaiNo
          )
        "
        class="table-header table-wrapper overflow-x-auto border-none"
        hide-default-footer
        :items-per-page="-1"
        height="auto"
        header-height="48px"
      >
        <!-- ヘッダ Part -->
        <template #headers>
          <!-- 初期設定マスタの情報.計画表様式が「0:A3、2:A4-2枚」の場合 -->
          <tr
            v-if="
              localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_0 ||
              localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_2
            "
          >
            <!-- 目標についての支援のポイント -->
            <th
              rowspan="1"
              class="width-231"
            >
              <div class="thead-with-iconbtn width-231">
                {{ t('label.about-the-target-points-of-support') }}
              </div>
            </th>
            <!-- 本人等のセルフケアや家族の支援、インフォマールサービス -->
            <th
              rowspan="1"
              class="width-200"
            >
              <div class="thead-with-iconbtn">
                {{ t('label.self-care-family-informal-support') }}
              </div>
            </th>
            <!-- 介護保険サービスまたは地域支援事業 -->
            <th
              rowspan="1"
              class="width-200"
            >
              <div class="thead-with-iconbtn">
                {{ t('label.long-term-care-insurance-service-or-community-service') }}
              </div>
            </th>
            <!-- サービス種別 -->
            <th
              rowspan="1"
              class="width-160"
            >
              <div class="thead-with-iconbtn">
                {{ t('label.service-type') }}
              </div>
            </th>
            <!-- 事業所 -->
            <th
              rowspan="1"
              class="width-118"
            >
              <div class="thead-with-iconbtn">
                {{ t('label.office-title') }}
              </div>
            </th>
            <!-- 期間 -->
            <th
              rowspan="1"
              colspan="2"
              class="width-128"
            >
              <div class="thead-with-iconbtn">
                {{ t('label.period') }}
              </div>
            </th>
          </tr>
          <!-- 初期設定マスタの情報.計画表様式が「1:A4-3枚」の場合 -->
          <tr
            v-if="localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_1"
          >
            <!-- 目標についての支援のポイント -->
            <th
              rowspan="2"
              class="width-160"
            >
              <div class="thead-with-iconbtn">
                {{ t('label.about-the-target-points-of-support') }}
              </div>
            </th>
            <!-- 具体的な支援の内容 -->
            <th
              rowspan="2"
              colspan="3"
              class="width-284"
            >
              <div class="thead-with-iconbtn">
                <div>{{ t('label.support-detail-for-person') }}</div>
              </div>
            </th>
            <!-- ※1 -->
            <th
              rowspan="2"
              class="width-80"
            >
              <div class="thead-with-iconbtn">
                <div>{{ t('label.star-1') }}</div>
              </div>
            </th>
            <!-- サービス種別 -->
            <th
              rowspan="1"
              class="width-160"
            >
              <div class="thead-with-iconbtn">
                {{ t('label.service-type') }}
              </div>
            </th>
            <!-- サービス提供者（事業所） -->
            <th
              rowspan="2"
              class="width-160"
            >
              <div class="thead-with-iconbtn">
                {{ t('label.service-provider-establishment') }}
              </div>
            </th>
            <!-- 頻度 -->
            <th
              rowspan="1"
              class="width-118"
            >
              <div class="thead-with-iconbtn">
                {{ t('label.frequency') }}
              </div>
            </th>
            <!-- 期間 -->
            <th
              rowspan="1"
              colspan="1"
              class="width-128"
            >
              <div class="thead-with-iconbtn">
                {{ t('label.period') }}
              </div>
            </th>
          </tr>
        </template>
        <!-- 一覧 -->
        <template #item="{ item, index }">
          <!-- 初期設定マスタの情報.計画表様式が「0:A3、2:A4-2枚」の場合 -->
          <tr
            v-if="
              localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_0 ||
              localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_2
            "
            :class="{
              'select-row': selectRowIndex === index,
            }"
            class="table-cell"
            @click="selectRow(index)"
          >
            <!-- 目標についての支援のポイント -->
            <td rowspan="2">
              <g-custom-or-x-0163
                v-model="item.shienPointKnj"
                :oneway-model-value="{
                  height: '44px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_SHIEN_POINT_KNJ, index)
                    openDialog(Or06954Const.TITLE_POSITION.ABOUT_THE_TARGET_POINTS_OF_SUPPORT)
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_SHIEN_POINT_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <!-- 本人等のセルフケアや家族の支援、インフォマールサービス -->
            <td rowspan="2">
              <g-custom-or-x-0163
                v-model="item.infoServiceKnj"
                :oneway-model-value="{
                  height: '44px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_INFO_SERVICE_KNJ, index)
                    openDialog(Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT)
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_INFO_SERVICE_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <!-- 介護保険サービスまたは地域支援事業 -->
            <td rowspan="2">
              <g-custom-or-x-0163
                v-model="item.hokenServiceKnj"
                :oneway-model-value="{
                  height: '44px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_HOKEN_SERVICE_KNJ, index)
                    openDialog(
                      Or06954Const.TITLE_POSITION
                        .LONG_TERM_CARE_INSURANCE_SERVICE_OR_COMMUNITY_SERVICE
                    )
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_HOKEN_SERVICE_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <!-- サービス種別 -->
            <td rowspan="2">
              <g-custom-or-x-0185
                v-model="item.svShubetuKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @focus="handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_KNJ, index)"
                @change="changeTableData()"
              >
                <template #menu>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item input-by-service-type"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_KNJ, index)
                        openServiceDialog()
                      }
                    "
                  >
                    {{ t('label.input-by-service-type') }}
                  </div>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item insurance-service-import"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_KNJ, index)
                        openInsuranceDialog()
                      }
                    "
                  >
                    {{ t('label.insurance-service-import') }}
                  </div>
                </template>
              </g-custom-or-x-0185>
            </td>
            <!-- 事業所 -->
            <td rowspan="2">
              <g-custom-or-x-0163
                v-model="item.svJigyoKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_SV_JIGYO_KNJ, index)
                    openServiceDialog()
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_SV_JIGYO_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <td
              colspan="2"
              class="border-bottom-none"
            >
              <!-- 期間 -->
              <g-custom-or-x-0216
                :kikan-s-ymd="item.kikanSYmd"
                :kikan-e-ymd="item.kikanEYmd"
                :oneway-model-value="
                  {
                    showWaveIcon:
                      localOneway.or06954Oneway.itakuKkakYmdFlg ===
                        Or06954Const.ITAKU_KKAK_YMD_FLG_0 ||
                      localOneway.or06954Oneway.itakuKkakYmdFlg ===
                        Or06954Const.ITAKU_KKAK_YMD_FLG_1,
                    showEndDate:
                      localOneway.or06954Oneway.itakuKkakYmdFlg ===
                        Or06954Const.ITAKU_KKAK_YMD_FLG_0 ||
                      localOneway.or06954Oneway.itakuKkakYmdFlg ===
                        Or06954Const.ITAKU_KKAK_YMD_FLG_1,
                    isCopyMode: localOneway.or06954Oneway.isCopyMode,
                    itakuKkakYmdFlg: localOneway.or06954Oneway.itakuKkakYmdFlg,
                    itakuKkakKikanFlg: localOneway.or06954Oneway.itakuKkakKikanFlg,
                  } as OrX0216OnewayType
                "
                :index="index"
                @update:kikan-s-ymd="item.kikanSYmd = $event"
                @update:kikan-e-ymd="item.kikanEYmd = $event"
                @change="changeTableData()"
                @focus="handleFocus"
                @open-calendar-y-m-d="
                  (kbn: string) => {
                    is216Click = true
                    openCalendarYMD(kbn)
                  }
                "
              />
            </td>
          </tr>
          <tr
            v-if="
              localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_0 ||
              localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_2
            "
            :class="{ 'select-row': selectRowIndex === index }"
            class="table-cell"
            @click="selectRow(index)"
          >
            <!-- 期間 -->
            <td
              colspan="2"
              class="border-top-none"
            >
              <g-custom-or-x-0163
                v-model="item.kikanKnj"
                :oneway-model-value="{
                  height: '44px',
                  showEditBtnFlg:
                    !props.onewayModelValue.isCopyMode && showEditBtnFlg.includes(index),
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    if (!showEditBtnFlg.includes(index)) {
                      showEditBtnFlg.push(index)
                    }
                    handleFocus(Or06954Const.CELL.CELL_KIKAN_KNJ, index)
                    openDialog(Or06954Const.TITLE_POSITION.PERIOD)
                  }
                "
                @focus="
                  () => {
                    showEditBtnFlg.push(index)
                    handleFocus(Or06954Const.CELL.CELL_KIKAN_KNJ, index)
                  }
                "
                @focusout="
                  () => {
                    setTimeout(() => {
                      showEditBtnFlg = showEditBtnFlg.filter((i: number) => i !== index)
                    }, 50)
                  }
                "
                @change="changeTableData()"
              />
            </td>
          </tr>
          <!-- 初期設定マスタの情報.計画表様式が「1:A4-3枚」の場合 -->
          <tr
            v-if="localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_1"
            :class="{
              'select-row': selectRowIndex === index,
            }"
            class="table-cell"
            @click="selectRow(index)"
          >
            <!-- 目標についての支援のポイント -->
            <td rowspan="3">
              <g-custom-or-x-0163
                v-model="item.shienPointKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_SHIEN_POINT_KNJ, index)
                    openDialog(Or06954Const.TITLE_POSITION.ABOUT_THE_TARGET_POINTS_OF_SUPPORT)
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_SHIEN_POINT_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <!-- 本人の取組 -->
            <td
              rowspan="1"
              colspan="1"
              class="horizontal-table label-center"
            >
              <base-mo00615
                :oneway-model-value="localOneway.mo00615OnewayPersonal"
                class="jokyo-label"
              >
              </base-mo00615>
            </td>
            <!-- 具体的な支援の内容（本人） -->
            <td
              rowspan="1"
              colspan="2"
              class="width-200"
            >
              <g-custom-or-x-0163
                v-model="item.infoServiceKnj"
                :oneway-model-value="{
                  height: '44px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_INFO_SERVICE_KNJ, index)
                    openDialog(
                      Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT,
                      Or06954Const.TITLE_POSITION.ABOUT_THE_TARGET_POINTS_OF_SUPPORT_SP,
                      index
                    )
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_INFO_SERVICE_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <!-- 記号（本人） -->
            <td rowspan="1">
              <base-mo00040
                v-model="item.svKbn"
                :oneway-model-value="localOneway.mo00040Oneway"
                :readonly="props.onewayModelValue.isCopyMode"
                @change="changeTableCheckboxData(index, 'svKbn')"
              >
              </base-mo00040>
            </td>
            <!-- サービス種別 -->
            <td rowspan="1">
              <g-custom-or-x-0185
                v-model="item.svShubetuKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @focus="handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_KNJ, index)"
                @change="changeTableData()"
              >
                <template #menu>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item input-by-service-type"
                    @click="openServiceDialog()"
                  >
                    {{ t('label.input-by-service-type') }}
                  </div>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item insurance-service-import"
                    @click="openInsuranceDialog()"
                  >
                    {{ t('label.insurance-service-import') }}
                  </div>
                </template>
              </g-custom-or-x-0185>
            </td>
            <!-- 事業所（本人） -->
            <td rowspan="1">
              <g-custom-or-x-0163
                v-model="item.svJigyoKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_SV_JIGYO_KNJ, index)
                    openServiceDialog()
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_SV_JIGYO_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <!-- 頻度（本人） -->
            <td rowspan="1">
              <g-custom-or-x-0185
                v-model="item.hindoKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @focus="handleFocus(Or06954Const.CELL.CELL_HINDO_KNJ, index)"
                @change="changeTableData()"
              >
                <template #menu>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item frequency-of-input"
                    @click="openDialog(Or06954Const.TITLE_POSITION.FREQUENCY)"
                  >
                    {{ t('label.frequency-of-input') }}
                  </div>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item enter-the-day-of-the-week"
                    @click="openDayDialog()"
                  >
                    {{ t('label.enter-the-day-of-the-week') }}
                  </div>
                </template>
              </g-custom-or-x-0185>
            </td>

            <!-- 期間 -->
            <td
              colspan="1"
              class="period"
            >
              <g-custom-or-x-0185
                v-model="item.kikanKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @focus="handleFocus(Or06954Const.CELL.CELL_KIKAN_KNJ, index)"
                @change="changeTableData()"
              >
                <template #menu>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item frequency-of-input"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_KIKAN_KNJ, index)
                        openDialog(
                          Or06954Const.TITLE_POSITION.PERIOD,
                          Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP
                        )
                      }
                    "
                  >
                    {{ t('label.input-by-period') }}
                  </div>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item input-by-ymd"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_KIKAN_KNJ, index)
                        openCalendarYMD(Or06954Const.KBN_TYPE_1)
                      }
                    "
                  >
                    {{ t('label.input-by-ymd') }}
                  </div>
                  <div
                    v-show="
                      !props.onewayModelValue.isCopyMode &&
                      localOneway.or06954Oneway.itakuKkakKikanFlg ===
                        Or06954Const.ITAKU_KKAK_KIKAN_FLG_1
                    "
                    class="orx0185-footer-menu-item input-by-ymd"
                    @click="openCalendarYMD(Or06954Const.KBN_TYPE_2)"
                  >
                    {{ t('label.input-by-md') }}
                  </div>
                </template>
              </g-custom-or-x-0185>
            </td>
          </tr>
          <tr
            v-if="localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_PRT_FLG_1"
            :class="{
              'select-row': selectRowIndex === index,
            }"
            class="table-cell"
            @click="selectRow(index)"
          >
            <!-- 家族・地域の支援、民間サービス等 -->
            <td
              rowspan="1"
              colspan="1"
              class="horizontal-table label-center"
            >
              <base-mo00615
                :oneway-model-value="localOneway.mo00615OnewayFamily"
                class="jokyo-label"
              >
              </base-mo00615>
            </td>
            <!-- 具体的な支援の内容（家族） -->
            <td
              rowspan="1"
              colspan="2"
            >
              <g-custom-or-x-0163
                v-model="item.kazokuServiceKnj"
                :oneway-model-value="{
                  height: '44px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_KAZOKU_SERVICE_KNJ, index)
                    openDialog(
                      Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT,
                      Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP,
                      index
                    )
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_KAZOKU_SERVICE_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <!-- 記号（家族） -->
            <td rowspan="1">
              <base-mo00040
                v-model="item.svKazokuKbn"
                :oneway-model-value="localOneway.mo00040Oneway"
                :readonly="props.onewayModelValue.isCopyMode"
                @change="changeTableCheckboxData(index, 'svKazokuKbn')"
              >
              </base-mo00040>
            </td>
            <!-- サービス種別（家族） -->
            <td rowspan="1">
              <g-custom-or-x-0185
                v-model="item.svShubetuKazokuKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @focus="handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_KAZOKU_KNJ, index)"
                @change="changeTableData()"
              >
                <template #menu>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item input-by-service-type"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_KAZOKU_KNJ, index)
                        openServiceDialog()
                      }
                    "
                  >
                    {{ t('label.input-by-service-type') }}
                  </div>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item insurance-service-import"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_KAZOKU_KNJ, index)
                        openInsuranceDialog()
                      }
                    "
                  >
                    {{ t('label.insurance-service-import') }}
                  </div>
                </template>
              </g-custom-or-x-0185>
            </td>
            <!-- 事業所（家族） -->
            <td rowspan="1">
              <g-custom-or-x-0163
                v-model="item.svJigyoKazokuKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_KAZOKU_KNJ, index)
                    openServiceDialog()
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_SV_JIGYO_KAZOKU_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <!-- 頻度（家族） -->
            <td rowspan="1">
              <g-custom-or-x-0185
                v-model="item.hindoKazokuKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @focus="handleFocus(Or06954Const.CELL.CELL_HINDO_KAZOKU_KNJ, index)"
                @change="changeTableData()"
              >
                <template #menu>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item frequency-of-input"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_HINDO_KAZOKU_KNJ, index)
                        openDialog(Or06954Const.TITLE_POSITION.FREQUENCY)
                      }
                    "
                  >
                    {{ t('label.frequency-of-input') }}
                  </div>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item enter-the-day-of-the-week"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_HINDO_KAZOKU_KNJ, index)
                        openDayDialog()
                      }
                    "
                  >
                    {{ t('label.enter-the-day-of-the-week') }}
                  </div>
                </template>
              </g-custom-or-x-0185>
            </td>

            <!-- 期間（家族） -->
            <td
              colspan="1"
              class="period"
            >
              <g-custom-or-x-0185
                v-model="item.kikanKazokuKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @focus="handleFocus(Or06954Const.CELL.CELL_KIKAN_KAZOKU_KNJ, index)"
                @change="changeTableData()"
              >
                <template #menu>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item frequency-of-input"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_KIKAN_KAZOKU_KNJ, index)
                        openDialog(
                          Or06954Const.TITLE_POSITION.PERIOD,
                          Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP
                        )
                      }
                    "
                  >
                    {{ t('label.input-by-period') }}
                  </div>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item input-by-ymd"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_KIKAN_KAZOKU_KNJ, index)
                        openCalendarYMD(Or06954Const.KBN_TYPE_1)
                      }
                    "
                  >
                    {{ t('label.input-by-ymd') }}
                  </div>
                  <div
                    v-show="
                      !props.onewayModelValue.isCopyMode &&
                      localOneway.or06954Oneway.itakuKkakKikanFlg ===
                        Or06954Const.ITAKU_KKAK_KIKAN_FLG_1
                    "
                    class="orx0185-footer-menu-item input-by-ymd"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_KIKAN_KAZOKU_KNJ, index)
                        openCalendarYMD(Or06954Const.KBN_TYPE_2)
                      }
                    "
                  >
                    {{ t('label.input-by-md') }}
                  </div>
                </template>
              </g-custom-or-x-0185>
            </td>
          </tr>
          <tr
            v-if="localOneway.or06954Oneway.itakuKkakPrtFlg === Or06954Const.ITAKU_KKAK_YMD_FLG_1"
            :class="{
              'select-row': selectRowIndex === index,
            }"
            class="table-cell"
            @click="selectRow(index)"
          >
            <!-- 介護保険サービス地域支援事業区市町村サービス -->
            <td
              rowspan="1"
              colspan="1"
              class="horizontal-table label-center"
            >
              <base-mo00615
                :oneway-model-value="localOneway.mo00615OnewayNursing"
                class="jokyo-label"
              >
              </base-mo00615>
            </td>
            <!-- 具体的な支援の内容（保険・地域） -->
            <td
              rowspan="1"
              colspan="2"
            >
              <g-custom-or-x-0163
                v-model="item.hokenServiceKnj"
                :oneway-model-value="{
                  height: '44px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_HOKEN_SERVICE_KNJ, index)
                    openDialog(
                      Or06954Const.TITLE_POSITION
                        .LONG_TERM_CARE_INSURANCE_SERVICE_OR_COMMUNITY_SERVICE,
                      Or06954Const.TITLE_POSITION
                        .LONG_TERM_CARE_INSURANCE_SERVICE_OR_COMMUNITY_SERVICE_SP,
                      index
                    )
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_HOKEN_SERVICE_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <!-- 記号（保険・地域） -->
            <td rowspan="1">
              <base-mo00040
                v-model="item.svHokenKbn"
                :oneway-model-value="localOneway.mo00040Oneway"
                :readonly="props.onewayModelValue.isCopyMode"
                @change="changeTableCheckboxData(index, Or06954Const.CheckboxType.SvHokenKbn)"
              >
              </base-mo00040>
            </td>
            <!-- サービス種別（保険・地域） -->
            <td rowspan="1">
              <g-custom-or-x-0185
                v-model="item.svShubetuHoken_knj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @focus="handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_HOKEN_KNJ, index)"
                @change="changeTableData()"
              >
                <template #menu>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item input-by-service-type"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_HOKEN_KNJ, index)
                        openServiceDialog()
                      }
                    "
                  >
                    {{ t('label.input-by-service-type') }}
                  </div>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item insurance-service-import"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_HOKEN_KNJ, index)
                        openInsuranceDialog()
                      }
                    "
                  >
                    {{ t('label.insurance-service-import') }}
                  </div>
                </template>
              </g-custom-or-x-0185>
            </td>
            <!-- 事業所（保険・地域） -->
            <td rowspan="1">
              <g-custom-or-x-0163
                v-model="item.svJigyoHoken_knj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @on-click-edit-btn="
                  () => {
                    handleFocus(Or06954Const.CELL.CELL_SV_SHUBETU_HOKEN_KNJ, index)
                    openServiceDialog()
                  }
                "
                @focus="handleFocus(Or06954Const.CELL.CELL_SV_JIGYO_HOKEN_KNJ, index)"
                @change="changeTableData()"
              />
            </td>
            <!-- 頻度（保険・地域） -->
            <td rowspan="1">
              <g-custom-or-x-0185
                v-model="item.hindoHokenKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @focus="handleFocus(Or06954Const.CELL.CELL_HINDO_HOKEN_KNJ, index)"
                @change="changeTableData()"
              >
                <template #menu>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item frequency-of-input"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_HINDO_HOKEN_KNJ, index)
                        openDialog(Or06954Const.TITLE_POSITION.FREQUENCY)
                      }
                    "
                  >
                    {{ t('label.frequency-of-input') }}
                  </div>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item enter-the-day-of-the-week"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_HINDO_HOKEN_KNJ, index)
                        openDayDialog()
                      }
                    "
                  >
                    {{ t('label.enter-the-day-of-the-week') }}
                  </div>
                </template>
              </g-custom-or-x-0185>
            </td>
            <!-- 期間（保険・地域） -->
            <td
              colspan="1"
              class="period"
            >
              <g-custom-or-x-0185
                v-model="item.kikanHokenKnj"
                :oneway-model-value="{
                  height: '66px',
                  showEditBtnFlg: !props.onewayModelValue.isCopyMode,
                  readonly: props.onewayModelValue.isCopyMode,
                  maxLength: '16000',
                  rules: [byteLength(16000)],
                }"
                @focus="handleFocus(Or06954Const.CELL.CELL_KIKAN_HOKEN_KNJ, index)"
                @change="changeTableData()"
              >
                <template #menu>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item frequency-of-input"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_KIKAN_HOKEN_KNJ, index)
                        openDialog(
                          Or06954Const.TITLE_POSITION.PERIOD,
                          Or06954Const.TITLE_POSITION.SELF_CARE_FAMILY_INFORMAL_SUPPORT_SP
                        )
                      }
                    "
                  >
                    {{ t('label.input-by-period') }}
                  </div>
                  <div
                    v-show="!props.onewayModelValue.isCopyMode"
                    class="orx0185-footer-menu-item input-by-ymd"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_KIKAN_HOKEN_KNJ, index)
                        openCalendarYMD(Or06954Const.KBN_TYPE_1)
                      }
                    "
                  >
                    {{ t('label.input-by-ymd') }}
                  </div>
                  <div
                    v-show="
                      !props.onewayModelValue.isCopyMode &&
                      localOneway.or06954Oneway.itakuKkakKikanFlg ===
                        Or06954Const.ITAKU_KKAK_KIKAN_FLG_1
                    "
                    class="orx0185-footer-menu-item input-by-ymd"
                    @click="
                      () => {
                        handleFocus(Or06954Const.CELL.CELL_KIKAN_HOKEN_KNJ, index)
                        openCalendarYMD(Or06954Const.KBN_TYPE_2)
                      }
                    "
                  >
                    {{ t('label.input-by-md') }}
                  </div>
                </template>
              </g-custom-or-x-0185>
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-form>

    <!-- 確認ダイアログ -->
    <g-base-or21815 v-bind="or21815" />
    <!-- GUI01109 入力支援【目標とする生活（１年）】画面 -->
    <g-custom-or-10883
      v-if="showDialogOr10883"
      v-bind="or10883"
      v-model="local.Or10883"
      :oneway-model-value="localOneway.or10883Oneway"
    />
    <!-- GUI01112 表示順変更計画表画面 -->
    <g-custom-or-28543
      v-if="showDialogOr28543"
      v-bind="or28543"
      v-model="local.Or28543"
      :oneway-model-value="localOneway.or28543Oneway"
    />
    <!-- GUI01098 サービス種別入力支援（予防計画書） -->
    <g-custom-or-27487
      v-if="showDialogOr27487"
      v-bind="or27487"
      v-model="local.Or27487"
      :oneway-model-value="localOneway.or27487Oneway"
    />
    <!-- 年月選択 -->
    <base-mo01343
      v-model="local.mo01343"
      :oneway-model-value="localOneway.mo01343Oneway"
    />
    <!-- 曜日取込 -->
    <g-custom-or-28582
      v-if="showDialogOr28582"
      v-bind="or28582"
      @update:model-value="(value: DayResult) => dayChange(value)"
    >
    </g-custom-or-28582>
  </div>
</template>
<style scoped lang="scss">
@use '@/styles/base.scss';
$icon-width-height: 24px;
$tag-line-min-height: 24px;
$icon-color: #a7bada;
$border-radius: 4px;
// 選択した行のCSS
.select-row {
  background: rgb(var(--v-theme-blue-100));
  border: 1px solid rgb(var(--v-theme-blue-200));
}

.required {
  color: rgb(var(--v-theme-orange-400));
}
.header-text {
  white-space: break-spaces;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.top-row {
  margin: 8px 0px 16px 0px;
  display: flex;
  justify-content: space-between;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.table-wrapper :deep(.v-table__wrapper th) {
  font-size: 13px !important;
  font-weight: 400 !important;
  height: 48px !important;
  padding: 0 8px !important;
}
:deep(.v-table__wrapper) {
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
}
.table-wrapper .v-table__wrapper td {
  padding: 0;
  font-size: 14px;
  height: 129px;
  min-height: 129px;
  max-height: 129px;
}
.thead-with-iconbtn {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.divider-div {
  display: flex;
}
.table-wrapper .v-table__wrapper td {
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  padding: 0;
  font-size: 14px;
}
.table-wrapper :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  position: relative;
  font-size: 14px;
  font-weight: bold;
  white-space: break-spaces;
}
.no-padding {
  padding: 0 !important;
}
// 最小幅: 50px
.width-50 {
  width: 50px;
}
// 最小幅: 60px
.width-60 {
  width: 60px;
}
// 最小幅: 80px
.width-80 {
  width: 80px;
  min-width: 80px;
  max-width: 80px;
}
// 最小幅: 110px
.width-110 {
  width: 110px;
}
// 最小幅: 118px
.width-118 {
  width: 118px;
  min-width: 118px;
  max-width: 118px;
}
// 最小幅: 128px
.width-128 {
  width: 128px;
  min-width: 128px;
  max-width: 128px;
}
// 最小幅: 160px
.width-160 {
  width: 160px;
  max-width: 160px;
  min-width: 160px;
}
// 最小幅: 165px
.width-165 {
  width: 165px;
}
// 最小幅: 176px
.width-176 {
  width: 176px;
}
// 最小幅: 180px
.width-180 {
  width: 180px;
}
// 最小幅: 190px
.width-190 {
  width: 190px;
}
// 最小幅: 200px
.width-200 {
  width: 200px;
  max-width: 200px;
}
// 最小幅: 120px
.width-120 {
  width: 120px;
}
// 最小幅: 231px
.width-231 {
  width: 231px;
  max-width: 231px;
}
// 最小幅: 284px
.width-284 {
  width: 284px;
  max-width: 284px;
  min-width: 284px;
}
// 最小幅: 480px
.width-480 {
  width: 480px;
}
.row-height {
  height: 120px;
}
.horizontal-table {
  height: 100%;
  border-collapse: collapse;
  background-color: #edf1f7;
  width: 68px;
}
.label-center {
  display: table-cell;
  align-content: center;
}
.jokyo-label {
  font-weight: normal;
  min-width: 68px;
  max-width: 68px;
  width: 68px;
  white-space: pre-wrap !important;
  word-wrap: break-word;
  background-color: transparent !important;
  align-content: center;
  margin: 0 8px;
  min-height: 129px;
  font-weight: 400;
  :deep(.v-col-auto) {
    align-content: center;
  }
}
:deep(.v-row--no-gutters) {
  display: -webkit-box;
  flex-wrap: nowrap;
  // display: contents;
  justify-content: center;
  .align-self-end {
    align-content: center;
    width: auto;
  }
}
.checkbox-mark {
  display: flex;
  align-content: center;
  justify-content: center;
  flex-wrap: wrap;
}
.table-cell {
  align-content: center;
}
td {
  min-height: 150px;
}
.jokyo-label .v-row {
  display: -webkit-box;
  flex-wrap: nowrap;
}
.sort-by {
  width: 40px;
  background: transparent !important;
}
.row-select {
  width: auto;
  background: transparent !important;
}
.sort-by-icon {
  align-content: center;
  flex-wrap: wrap;
}
textarea {
  min-height: 68px;
}
.table-two-line-cell {
  min-height: 46px;
}
.wave-icon {
  background: transparent !important;
  :deep(.ma-1) {
    margin: 0 !important;
  }
}
.container {
  margin: 24px 0 24px 24px;
  padding-right: 24px;
}
:deep(.v-table--fixed-height > .v-table__wrapper) {
  overflow-x: auto !important;
  overflow-y: hidden !important;
  width: v-bind(tableWrapperWidth);
}
td {
  position: relative;
}
.edit-col {
  display: flex;
  flex-direction: row;
  padding-left: 8px;
  align-items: center;
}
.icon-btn {
  width: $icon-width-height;
  height: $icon-width-height;
  min-width: $icon-width-height;
  min-height: $icon-width-height;
  color: $icon-color;
}
:deep(.v-field__outline) {
  --v-field-border-width: 0 !important;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
.hindo-width {
  width: 184px;
}
.service-width {
  width: 319px;
}

.text-border {
  border: 1px solid #0760e6 !important;
}

:deep(.v-sheet .v-text-field .v-field) {
  background-color: unset !important;
}
.border-bottom-none {
  height: 80px !important;
  min-height: 80px !important;
  max-height: 80px !important;
  border-bottom: none !important;
  :deep(.full-width-field) {
    height: 25px !important;
    min-height: 25px !important;
  }
}
.border-top-none {
  height: 48px !important;
  min-height: 48px !important;
  max-height: 48px !important;
  border-top: none !important;
}
.period {
  :deep(.full-width-field) {
    min-width: 76px !important;
  }
}
</style>
