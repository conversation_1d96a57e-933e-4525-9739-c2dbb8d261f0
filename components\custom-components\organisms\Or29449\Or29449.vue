<script setup lang="ts">
/**
 * Or29449:見通し入力
 * GUI00916_見通し
 *
 * @description
 * 見通し入力
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { ref, reactive, watch, computed } from 'vue'
import { Or05061Const } from '../Or05061/Or05061.constants'
import type { Mo01280Type } from '../Or26837/Or26837.type'
import { Or05059Const } from '../Or05059/Or05059.constants'
import { Or29449Const } from './Or29449.constants'
import type { AsyncFunction } from './Or29449.type'
import { useScreenTwoWayBind, useSetupChildProps, useScreenUtils } from '#imports'
import type {
  MitosiSyosaiType,
  Or29449Type,
  TransmitParam,
} from '~/types/cmn/business/components/Or29449Type'
import type { Or05059Type } from '~/types/cmn/business/components/Or05059Type'
import type { Or05061OnewayType, Or05061Type } from '~/types/cmn/business/components/Or05061Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
/**
 * Or05059用ref
 */
const or05059 = ref({ uniqueCpId: '' })
/**
 * or05061用ref
 */
const or05061 = ref({ uniqueCpId: '' })
/**
 * or26184コンポーネントの状態
 */
const or05062 = ref({
  selectedIndex: computed(() => or05061Ref.value?.selectedRow ?? 0),
  totalLine: computed(() => or05061Ref.value?.totalItemCount ?? 0),
})
const { setChildCpBinds } = useScreenUtils()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or29449Type
  uniqueCpId: string
  parentUniqueCpId: string
}

/**
 * Propsの定義
 */
const props = defineProps<Props>()

/**
 * useScreenTwoWayBind
 */
const { refValue } = useScreenTwoWayBind<Or29449Type>({
  cpId: Or29449Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
const or05061Ref = ref<{
  createItem(): AsyncFunction
  deleteItem(): AsyncFunction
  duplicateItem(): AsyncFunction
  insertItem(): AsyncFunction
  moveUp(): AsyncFunction
  moveDown(): AsyncFunction
  sortBy(): AsyncFunction
  assessment(): AsyncFunction
  statusFactReference(): AsyncFunction
  notes(): AsyncFunction
  selectedRow: number
  totalItemCount: number
}>()
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or05059Const.CP_ID(0)]: or05059.value,
  [Or05061Const.CP_ID(0)]: or05061.value,
})

/**
 * フォーム値のローカル状態
 */
const local = reactive({
  transmitParam: {
    executeFlag: '',
    deleteBtnValue: '',
    kikanKanriFlg: '',
    houjinId: '',
    shisetuId: '',
    userId: '',
    svJigyoId: '',
    kijunbiYmd: '',
    sakuseiId: '',
    historyModifiedCnt: '',
    historyInfo: {},
  } as TransmitParam,
  or05059: { value: '' } as Or05059Type,
  or05061: {
    kikanFlg: '',
    idParent: props.uniqueCpId,
  } as Or05061OnewayType,
})

function initDataTableOr05061(data: MitosiSyosaiType[]) {
  const newData: Or05061Type[] = []
  data.forEach((item: MitosiSyosaiType) => {
    newData.push({
      ...item,
      mitosiKnj: { value: item.mitosiKnj ?? '' } as Mo01280Type,
      kadaiKnj: { value: item.kadaiKnj ?? '' } as Mo01280Type,
      yusenNo: { value: item.yusenNo ?? '' } as Mo01280Type,
      updateKbn: UPDATE_KBN.NONE,
    })
  })
  return newData
}
watch(
  () => refValue.value,
  (newVal) => {
    if (newVal) {
      local.or05059.value = newVal.rirekiObj.ikouKnj ?? ''
      setChildCpBinds(props.uniqueCpId, {
        Or05059: {
          twoWayValue: local.or05059,
        },
      })

      const data = initDataTableOr05061(newVal.mitosiSyosaiList) ?? [{ updateKbn: '0' }]
      setChildCpBinds(props.uniqueCpId, {
        Or05061: {
          twoWayValue: data,
        },
      })
    }
  },

  {
    immediate: true,
    deep: true,
  }
)
/**
 * タブ2削除ボタン押下時の処理
 *
 * @param type - ボタンの種類
 */
function onClickOr05062(type: string) {
  switch (type) {
    case 'add':
      or05061Ref.value?.createItem()
      break
    case 'delete':
      or05061Ref.value?.deleteItem()
      break
    case 'duplicate':
      or05061Ref.value?.duplicateItem()
      break
    case 'insert':
      or05061Ref.value?.insertItem()
      break
    case 'up':
      or05061Ref.value?.moveUp()
      break
    case 'down':
      or05061Ref.value?.moveDown()
      break
    case 'sortby':
      or05061Ref.value?.sortBy()
      break
    case 'assessment':
      or05061Ref.value?.assessment()
      break
    case 'status-fact-reference':
      or05061Ref.value?.statusFactReference()
      break
    case 'notes':
      or05061Ref.value?.notes()
      break
  }
}

defineExpose({
  or05059: or05059.value.uniqueCpId,
  or05061: or05061.value.uniqueCpId,
})
</script>

<template>
  <c-v-row>
    <c-v-col
      class="pt-0 mt-4 pb-6"
      cols="12"
    >
      <g-custom-or-05059
        ref="or05059Ref"
        v-bind="or05059"
        :model-value="local.or05059"
        :parent-unique-cp-id="props.parentUniqueCpId"
      />
    </c-v-col>
    <c-v-col
      cols="12"
      class="pt-4 pb-4"
      style="border-top: 1px solid #b4c5dc"
    >
      <g-custom-or-05062
        :model-value="or05062"
        @click="onClickOr05062"
      />
    </c-v-col>
    <c-v-col
      cols="12"
      class="pt-0"
    >
      <g-custom-or-05061
        ref="or05061Ref"
        v-bind="or05061"
        :oneway-model-value="local.or05061"
        :parent-unique-cp-id="props.parentUniqueCpId"
      />
    </c-v-col>
  </c-v-row>
</template>

<style scoped lang="scss"></style>
