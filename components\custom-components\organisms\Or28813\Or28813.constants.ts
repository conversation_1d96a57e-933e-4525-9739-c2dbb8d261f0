import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or28813:有機体:フリーアセスメントチェック項目マスタ
 * GUI00891_フリーアセスメントチェック項目マスタ
 *
 * @description
 * フリーアセスメントチェック項目マスタ
 *
 * <AUTHOR>
 */
export namespace Or28813Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or28813', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * sysCd
     */
    export const EMIT_TYPE_SYSCD = '71101'

    /**
     * kinouKbn
     */
    export const EMIT_TYPE_KINOUKBN = '52'
    /**
     * kinouId
     */
    export const EMIT_TYPE_KINOUID = '7219'
    /**
     * String 空
     */
    export const NULL = ''
    /**
     * Number 0
     */
    export const NUM_0 = 0
    /**
     * Number 1
     */
    export const NUM_1 = 1
    /**
     * Number 2
     */
    export const NUM_2 = 2
    /**
     * Number 3
     */
    export const NUM_3 = 3
    /**
     * Number 4
     */
    export const NUM_4 = 4
    /**
     * STR 1
     */
    export const STR_1 = '1'
    /**
     * (ツリーセクション)階層2
     */
    export const HIERARCHY_2 = 'B'
    /**
     * (ツリーセクション)階層3
     */
    export const HIERARCHY_3 = 'C'
    /**
     * (ツリーセクション)階層4
     */
    export const HIERARCHY_4 = 'D'
  }
}
