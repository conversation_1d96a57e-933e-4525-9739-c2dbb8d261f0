<script setup lang="ts">
/**
 * OrX0096：アセスメント（包括）画面_要介護者などの健康上や生活上の問題点及び解決すべき課題等
 *
 * @description
 *
 * アセスメント（包括）画面_要介護者などの健康上や生活上の問題点及び解決すべき課題等
 *
 * <AUTHOR>
 */

import { computed, defineProps, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or27504Const } from '../Or27504/Or27504.constants'
import { Or27504Logic } from '../Or27504/Or27504.logic'
import { Or10347Const } from '../Or10347/Or10347.constants'
import { Or10347Logic } from '../Or10347/Or10347.logic'
import type { Or27504TabDataType } from '../Or27504/Or27504.type'
import type {
  ConcreteCareItemType,
  ConcreteContentType,
  IssuseWholeItemType,
  OrX00096OnewayType,
  OrX0096Type,
} from './OrX0096.type'
import { OrX0096Const } from './OrX0096.constants'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21736Const } from '~/components/base-components/organisms/Or21736/Or21736.constants'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { useScreenStore, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or21735Logic } from '~/components/base-components/organisms/Or21735/Or21735.logic'
import { Or21736Logic } from '~/components/base-components/organisms/Or21736/Or21736.logic'
import { Or21737Logic } from '~/components/base-components/organisms/Or21737/Or21737.logic'
import { Or21738Logic } from '~/components/base-components/organisms/Or21738/Or21738.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { DIALOG_BTN, UPDATE_KBN } from '~/constants/classification-constants'
import type { Or27504Type } from '~/types/cmn/business/components/Or27504Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  Or10347OnewayType,
  Or10347Type,
  Title,
} from '~/types/cmn/business/components/Or10347Type'

/*************************
 * Props
 ************************** */
interface Props {
  onewayModelValue: OrX00096OnewayType
  uniqueCpId: string
  modelValue: OrX0096Type
  parentUniqueCpId: string
}

const { t } = useI18n()

const props = defineProps<Props>()

const _emit = defineEmits(['update:modelValue'])

/**
 * 最大カウント数
 */
const maxCount = ref(0)

/*************************
 * 変数定義
 ************************** */
const or21735 = ref({ uniqueCpId: Or21735Const.CP_ID(1) })
const or21736 = ref({ uniqueCpId: Or21736Const.CP_ID(1) })
const or21737 = ref({ uniqueCpId: Or21737Const.CP_ID(1) })
const or21738 = ref({ uniqueCpId: Or21738Const.CP_ID(1) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })
const or27504 = ref({ uniqueCpId: Or27504Const.CP_ID(1) })
const or10347 = ref({ uniqueCpId: Or10347Const.CP_ID(1) })

// 内容項目追加用フラグ
const contentItemAddFlg = ref<boolean>(true)

/**
 * 課題全般テーブルインデックス
 */
const issuseIndex = ref(0)

/**
 * 右側テーブル高さ
 */
const rightTableHeight = ref('100%')

/**
 * 選択したデータ
 */
const selectedItem = ref({
  concreteContent: '',
  concreateCareContent: '',
})

/**
 * マスタ画面返却値処理フラグ
 */
let masterProcessFlg = true

// Oneway
const localOneway = reactive({
  // 行下移動ボタン
  rowDownOneway: {
    btnIcon: 'height',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  // 生活全般の解決すべき課題入力支援ボタン
  wholeLifeShouldSolutionIssuesInputSupportOneway: {
    btnIcon: 'edit_square',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
    width: '30px',
    height: '30px',
  } as Mo00009OnewayType,
  // 長期目標入力支援ボタン
  longTermGoalInputSupportOneway: {
    btnIcon: 'edit_square',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  // 短期目標入力支援ボタン
  shortTermGoalInputSupportOneway: {
    btnIcon: 'edit_square',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  mo00039Oneway: {
    // デフォルト値の設定
    name: 'concrete_content_table',
    itemLabel: t('label.insurance-service-import-additional-services'),
    showItemLabel: false,
    hideDetails: true,
  } as Mo00039OnewayType,
  or27504Oneway: {
    // 問題点CD
    b4Cd: '0',
    // 職員ID
    shokuinId: '1',
    // 事業所ID
    svJigyoId: '1',
    //  機能名
    asKinouName: '1',
  },
  mo00615Oneway: {
    itemLabel: t('label.assessment-comprehensive-pannel-title'),
    itemLabelFontWeight: 'bold',
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'fontSize:16px;',
      labelClass: 'panel-title',
      itemStyle: 'background:transparent;',
    }),
  } as Mo00615OnewayType,
  // 表示順ボタン
  displayOrderBtnOneway: {
    btnLabel: t('label.display-order'),
  } as Mo00611OnewayType,
  or10347Oneway: {
    // 画面区分
    parentViewType: '',
    // 計画期間ID
    parentProcessingType: '',
    // 親画面からの初期値
    sortList: [],
  } as Or10347OnewayType,
})

/**
 * ロカール
 */

const local = reactive({
  or27504: {
    concreteContentsList: [],
  } as Or27504Type,
  // 表示順変更
  or10347: {} as Or10347Type,
})

/***************************************
 * computed
 **************************************/
const concreteCareContentList = computed(() => {
  const filteredList = refValue.value!.concreteCareItemList?.filter(
    (item) =>
      item.dmyCc32Id === selectedItem.value.concreteContent && item.updateKbn !== UPDATE_KBN.DELETE
  )
  return filteredList
})

const showDialog21814 = computed(() => {
  return Or21814Logic.state.get(or21814.value.uniqueCpId) ?? false
})
// ダイアログ表示フラグ
const showDialogOr27504 = computed(() => {
  // Or27504のダイアログ開閉状態
  return Or27504Logic.state.get(or27504.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr10347 = computed(() => {
  // Or10347のダイアログ開閉フラグ
  return Or10347Logic.state.get(or10347.value.uniqueCpId)?.isOpen ?? false
})

/***************************************
 * 関数定義
 **************************************/

/**
 * ○数字を作成する
 *
 * @param index - 配列のインデックス
 */
function setCircleNumber(index: number) {
  const circleNumbers = [
    '①',
    '②',
    '③',
    '④',
    '⑤',
    '⑥',
    '⑦',
    '⑧',
    '⑨',
    '⑩',
    '⑪',
    '⑫',
    '⑬',
    '⑭',
    '⑮',
    '⑯',
    '⑰',
    '⑱',
    '⑲',
    '⑳',
  ]
  if (index >= 1 && index <= 20) {
    return circleNumbers[index - 1]
  }
  return ''
}

/**
 * ワーニング解消ためのランダムID作成関数
 */
function generateRandomTextareaId(): string {
  return `orX0096-textarea-${Date.now()}-${Math.random().toString(36)}`
}

/**
 * 「問題点や解決すべき課題等ラベル」クリック
 *
 * @param issuseItem - 課題
 *
 * @param index - 該当課題のインデックス
 */
const issuseCellClick = (issuseItem: IssuseWholeItemType, index: number) => {
  // 該当行を選択する
  issuseIndex.value = index
  // 該当行の有無が「空」の場合
  if (!issuseItem.isPlanningFlg) {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'blank',
        thirdBtnLabel: t('btn.close'),
        isOpen: true,
        // ■以下のメッセージを表示
        // i.cmn.11381
        dialogText: t('message.i-cmn-11381'),
      },
    })
    // 処理終了
    return
  }
  // 該当行の有無が「〇」の場合、且つ具体的内容セクションの選択された行がある
  if (issuseItem.isPlanningFlg && selectedItem.value.concreteContent !== '') {
    const findedItem = refValue.value!.concreteContentList?.find(
      (item) => item.cc32Id === selectedItem.value.concreteContent
    )
    if (!findedItem) return
    // 且つ具体的内容セクションの選択された行.問題番号が含まない場合
    if (!findedItem.number.includes(setCircleNumber(index + 1))) {
      // 表示用タイトル作成
      findedItem.number += setCircleNumber(index + 1)
    } else {
      // 且つ具体的内容セクションの選択された行.問題番号が含まる場合
      // 問題番号に追加しない
      return
    }
  }
}

/**
 * 「有無ラベル」クリック
 *
 * @param issuseItem - 課題
 *
 * @param index - 該当課題のインデックス
 */
const planCellClick = async (issuseItem: IssuseWholeItemType, index: number) => {
  // 該当行を選択する
  issuseIndex.value = index
  // 該当行の有無が「空」の場合
  if (!issuseItem.isPlanningFlg) {
    // 該当行の有無を"1"に設定する
    issuseItem.isPlanningFlg = true
  } else {
    // 該当行の有無が「〇」の場合
    // 以下のメッセージを表示
    // i.cmn.11383
    const result = await getDeleteConfirmResult(t('message.i-cmn-11383'))
    if (result === DIALOG_BTN.YES) {
      // はい：処理続き
      // 課題番号と具体番号が完全に一致した場合は、具体番号に対応するデータおよび対応する介護項目の内容を削除
      // 画面中表示した全番号リストを取得する
      const numberList = refValue.value?.concreteContentList?.map((item) => {
        return {
          problemDot: item.number,
          id: item.cc32Id,
        }
      })
      // 完全一致の番号リストを絞り出す
      const findedItems = numberList?.filter(
        (item) => item.problemDot === setCircleNumber(index + 1)
      )
      // 該当問題点コードに完全一致するデータが見つからないの場合
      if (!findedItems) {
        // 該当行の有無を"0"に設定する
        issuseItem.isPlanningFlg = false
        // 該当行の立案を"0"に設定する
        issuseItem.isHaveIssuesFlg = false
        // 処理終了
        return
      }
      // 完全一致のデータを削除する
      // 具体的内容リスト
      refValue.value?.concreteContentList?.forEach((item) => {
        const findedItem = findedItems.find((sItem) => sItem.id === item.cc32Id)
        if (findedItem) {
          item.updateKbn = UPDATE_KBN.DELETE
        }
      })
      // ケア項目リスト
      refValue.value?.concreteCareItemList?.forEach((item) => {
        const findedItem = findedItems.find((sItem) => sItem.id === item.dmyCc32Id)
        if (findedItem) {
          item.updateKbn = UPDATE_KBN.DELETE
        }
      })
      // 該当行の有無を"0"に設定する
      issuseItem.isPlanningFlg = false
      // 該当行の立案を"0"に設定する
      issuseItem.isHaveIssuesFlg = false
    } else {
      // いいえ：処理終了
      return
    }
  }
}

/**
 * 項目「立案」クリック
 *
 * @param issuseItem - 課題
 *
 * @param index - 該当課題のインデックス
 */
const developCellClick = (issuseItem: IssuseWholeItemType, index: number) => {
  // 該当行を選択する
  issuseIndex.value = index
  // 該当行の有無が「空」の場合
  if (!issuseItem.isPlanningFlg) {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'blank',
        thirdBtnLabel: t('btn.close'),
        isOpen: true,
        dialogText: t('message.i-cmn-11381'),
      },
    })
    // 処理終了
    return
  }
  // 該当行の有無が「〇」の場合、
  // 該当行の立案が「空」の場合
  else if (issuseItem.isPlanningFlg && !issuseItem.isHaveIssuesFlg) {
    issuseItem.isHaveIssuesFlg = true
  }
  // 該当行の有無が「○」の場合、且つ
  // 該当行の立案が「○」の場合
  else if (issuseItem.isPlanningFlg && issuseItem.isHaveIssuesFlg) {
    // ・該当行の立案を"0"に設定する
    issuseItem.isHaveIssuesFlg = false
    // 具体的内容表の問題点番号リスト件数分、該当行の問題点番号が具体的内容表の問題点番号に存在する場合
    // 画面中表示した全番号リストを取得する
    const matchingItems = refValue.value?.concreteContentList?.filter((item) => {
      return item.correspondenceKeys.find((sItem) => sItem.b4Cd === issuseItem.b4cd)
    })
    if (matchingItems?.length) {
      // 具体的内容表の優先順位がNULLに設定する
      matchingItems?.forEach((item) => {
        item.juni = null
      })
      // 以下のメッセージを表示
      Or21814Logic.state.set({
        uniqueCpId: or21814.value.uniqueCpId,
        state: {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          secondBtnLabel: t('btn.no'),
          thirdBtnType: 'blank',
          thirdBtnLabel: t('btn.close'),
          isOpen: true,
          dialogText: t('message.i-cmn-11382'),
        },
      })
    }
  }
}
/**
 * 「検索アイコンボタン」押下
 *
 * @param issuseItem - 課題
 *
 * @param index - 該当課題のインデックス
 */
const searchBtnClick = (issuseItem: IssuseWholeItemType, index: number) => {
  // 該当行を選択する
  issuseIndex.value = index
  // 該当行の有無が「空」の場合
  // 以下のメッセージを表示
  if (!issuseItem.isPlanningFlg) {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'blank',
        thirdBtnLabel: t('btn.close'),
        isOpen: true,
        dialogText: t('message.i-cmn-11381'),
      },
    })
    // 処理終了
    return
  } else {
    // 該当行の有無が「〇」の場合
    // 該当行のデータはデフォルトで選択されています
    // GUI00628_具体的内容・対応するケア項目マスタ画面をポップアップで起動する
    // 起動前の設定
    localOneway.or27504Oneway.svJigyoId = props.onewayModelValue.officeId
    masterProcessFlg = true
    localOneway.or27504Oneway.b4Cd = issuseItem.b4cd
    // 具体的内容・対応するｹｱ項目マスタ］画面をポップアップで起動する
    Or27504Logic.state.set({
      uniqueCpId: or27504.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * マスタ画面から取得した返却値
 *
 * @param selectData - 選択した具体的内容と対応するケア項目
 */
const getMasterResult = (selectData: Or27504TabDataType) => {
  // 画面.最大カウンター「cc32IdMax」 + 1
  const maxId = (maxCount.value += 1)
  // b4Cd取得
  const b4cd = refValue.value?.listSection[issuseIndex.value].b4cd ?? ''
  // 返却した入力支援情報.具体的内容が存在する場合、
  if (!selectData) return
  // 返却情報「具体的な内容」が存在する場合、
  //  ・具体的内容の最終に空白行を追加し、追加行を選択状態とする。
  //  ・更新区分：'C'
  //  ・画面.最大カウンター「cc32IdMax」 = 画面.最大カウンター「cc32IdMax」 + 1
  //  ・カウンター(画面記録)「cc32IdRecord」：画面.最大カウンター「cc32IdMax」
  if (selectData.mo1354ConcreteContentsTab) {
    // 具体的内容情報取得
    const content = selectData.mo1354ConcreteContentsTab
    const selectedConcreteContent: ConcreteContentType = {
      key: '',
      correspondenceKeys: [],
      content: content.ci1Knj.value,
      cc32Id: maxId.toString(),
      juni: '',
      b1Cd: props.onewayModelValue.b1Cd,
      seq: '',
      cc32Type: '',
      ci1Id: content.ci1Id,
      dmyB4Cd: [{ b4Cd: b4cd }],
      b4Cd: content.b4Cd,
      number: setCircleNumber(issuseIndex.value + 1) ?? '',
      updateKbn: UPDATE_KBN.CREATE,
    }
    if (refValue.value?.concreteContentList) {
      // 具体的内容の最終に空白行を追加
      refValue.value?.concreteContentList?.push(selectedConcreteContent)
    } else {
      refValue.value!.concreteContentList = [selectedConcreteContent]
    }
    // 追加行を選択状態とする。
    selectedItem.value.concreteContent = selectedConcreteContent.cc32Id
  }
  // 返却した入力支援情報.対応するケア項目リスト件数＞０の場合、
  if (selectData.mo1354CorrespondenceCareItemTab.length > 0) {
    // 返却情報「対応するケア項目」件数分、繰り返し、下記処理を行う
    //  ・対応するケア項目表の最終に空白行を追加し、追加行を選択状態とする。
    //  ・更新区分：'C'
    //  ・dmyCc32Id：具体的内容選択のカウンター(画面記録)「cc32IdRecord」
    const careList = selectData.mo1354CorrespondenceCareItemTab.map((item, index) => {
      return {
        key: '',
        content: item.ci2Knj.value,
        dmyCc32Id: maxId.toString(),
        cc33Id: `c_${item.id}`,
        b1Cd: props.onewayModelValue.b1Cd,
        cc32Id: maxId.toString(),
        seq: (index + 1).toString(),
        ci2Id: item.ci2Id,
        updateKbn: UPDATE_KBN.CREATE,
      }
    })
    if (refValue.value?.concreteCareItemList) {
      careList.forEach((item) => refValue.value?.concreteCareItemList?.push(item))
    } else {
      refValue.value!.concreteCareItemList = careList
    }
    // 一件目を選択状態にする
    selectedItem.value.concreateCareContent = careList[0].cc33Id
  }
}

/**
 * 削除確認ダイアログ
 *
 * @param dialogText - ダイアログテキスト
 */
const getDeleteConfirmResult = (dialogText: string): Promise<'yes' | 'no'> => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      thirdBtnLabel: t('btn.close'),
      isOpen: true,
      dialogText: dialogText,
    },
  })

  // 閉じるタイミングで監視を実行
  return new Promise((resolve) => {
    let result: 'yes' | 'no'
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (!newValue) {
          const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            result = 'yes'
          }
          if (event?.secondBtnClickFlg) {
            result = 'no'
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              closeBtnClickFlg: false,
            },
          })
          resolve(result)
        }
      },
      { once: true }
    )
  })
}

/**
 * 「行追加ボタン」押下処理
 */
const addRowLine = () => {
  // レイアウト変更点
  // 非活性になる
  if (!props.onewayModelValue.tableDisplayFlg) return
  // 現在フォーカスした内容が具体的内容、または対応するケア項目表がフォーカスの場合、具体的内容が0件の場合、
  if (contentItemAddFlg.value || !refValue.value!.concreteContentList?.length) {
    const newItem: ConcreteContentType = {
      key: '',
      correspondenceKeys: [],
      content: '',
      // ・ダミーカウンター「dmyCc32Id」：最大ダミーカウンター「dmyCc32Id」+1
      cc32Id: (maxCount.value + 1).toString(),
      juni: '',
      b1Cd: props.onewayModelValue.b1Cd,
      seq: '',
      cc32Type: '',
      ci1Id: '',
      dmyB4Cd: [],
      b4Cd: '',
      number: '',
      // ・更新区分：'C'
      updateKbn: UPDATE_KBN.CREATE,
    }
    maxCount.value++
    // 具体的内容の最終に空白行を追加し、追加行を選択状態とする。
    if (!refValue.value!.concreteContentList?.length) {
      refValue.value!.concreteContentList = [newItem]
    } else {
      refValue.value!.concreteContentList?.push(newItem)
    }
    selectedItem.value.concreteContent = newItem.cc32Id
  } else {
    // 対応するケア項目表がフォーカスの場合、具体的内容が0件の場合、
    const allDeleteFlg = refValue.value?.concreteContentList.every(
      (item) => item.updateKbn === UPDATE_KBN.DELETE
    )
    if (refValue.value?.concreteContentList.length === 0 || allDeleteFlg) {
      const newItem: ConcreteContentType = {
        key: '',
        correspondenceKeys: [],
        content: '',
        // ・ダミーカウンター「dmyCc32Id」：最大ダミーカウンター「dmyCc32Id」+1
        cc32Id: (maxCount.value + 1).toString(),
        juni: '',
        b1Cd: props.onewayModelValue.b1Cd,
        seq: '',
        cc32Type: '',
        ci1Id: '',
        dmyB4Cd: [],
        b4Cd: '',
        number: '',
        // ・更新区分：'C'
        updateKbn: UPDATE_KBN.CREATE,
      }
      maxCount.value++
      // 具体的内容の最終に空白行を追加し、追加行を選択状態とする。
      if (!refValue.value!.concreteContentList?.length) {
        refValue.value!.concreteContentList = [newItem]
      } else {
        refValue.value!.concreteContentList?.push(newItem)
      }
      selectedItem.value.concreteContent = newItem.cc32Id
      // 処理終了
      return
    }
    // 対応するケア項目追加
    const currentLeng = refValue.value?.concreteCareItemList?.length ?? 0
    const newCareItem: ConcreteCareItemType = {
      key: '',
      content: '',
      dmyCc32Id: selectedItem.value.concreteContent,
      // ・更新区分：'C'
      cc33Id: `C_${currentLeng}`,
      b1Cd: props.onewayModelValue.b1Cd,
      cc32Id: selectedItem.value.concreteContent,
      seq: currentLeng.toString(),
      ci2Id: concreteCareContentList.value?.[0]?.ci2Id ?? '',
      // 更新区分：'C'
      updateKbn: UPDATE_KBN.CREATE,
    }
    // ・対応するケア項目表の最終に空白行を追加し、追加行を選択状態とする。
    refValue.value!.concreteCareItemList?.push(newCareItem)
    selectedItem.value.concreateCareContent = newCareItem.cc33Id
  }
}

/**
 * 「行挿入」ボタン押下
 */
const insertRowLine = () => {
  // レイアウト変更点
  // 非活性になる
  if (!props.onewayModelValue.tableDisplayFlg) return
  // 具体的内容がフォーカスの場合
  if (contentItemAddFlg.value) {
    // 現在フォーカスした内容のインデックスを取得する
    const currentIndex = refValue.value!.concreteContentList?.findIndex(
      (item) => item.cc32Id === selectedItem.value.concreteContent
    )
    // 見つからない場合、処理終了
    if (currentIndex === -1 || currentIndex === undefined) return
    const newItem: ConcreteContentType = {
      key: '',
      correspondenceKeys: [],
      content: '',
      // 最大ダミーカウンター「dmyCc32Id」+1
      cc32Id: (maxCount.value + 1).toString(),
      juni: '',
      b1Cd: props.onewayModelValue.b1Cd,
      seq: '',
      cc32Type: '',
      ci1Id: '',
      dmyB4Cd: [],
      b4Cd: '',
      number: '',
      // ・更新区分：'C'
      updateKbn: UPDATE_KBN.CREATE,
    }
    maxCount.value++
    // ・具体的内容の選択行の上に空白行を追加し、追加行を選択状態とする。
    refValue.value!.concreteContentList?.splice(currentIndex, 0, newItem)
    selectedItem.value.concreteContent = newItem.cc32Id
  }
  // 対応するケア項目表がフォーカスの場合、具体的内容が0件の場合、
  else if (!contentItemAddFlg.value && !refValue.value!.concreteContentList?.length) {
    // ・具体的内容の最終に空白行を追加し、追加行を選択状態とする。
    // ・更新区分：'C'
    // ・ダミーカウンター：最大ダミーカウンター+1
    // 現在フォーカスした内容のインデックスを取得する
    const currentIndex = refValue.value!.concreteContentList?.findIndex(
      (item) => item.cc32Id === selectedItem.value.concreteContent
    )
    // 見つからない場合、処理終了
    if (currentIndex === -1) return
    const newItem: ConcreteContentType = {
      key: '',
      correspondenceKeys: [],
      content: '',
      // ダミーカウンター「dmyCc32Id」：最大ダミーカウンター「dmyCc32Id」+1
      cc32Id: (maxCount.value + 1).toString(),
      juni: '',
      b1Cd: props.onewayModelValue.b1Cd,
      seq: '',
      cc32Type: '',
      ci1Id: '',
      dmyB4Cd: [],
      b4Cd: '',
      number: '',
      // 更新区分：'C'
      updateKbn: UPDATE_KBN.CREATE,
    }
    // ・具体的内容の選択行の上に空白行を追加し、追加行を選択状態とする。
    refValue.value!.concreteContentList?.splice(currentIndex!, 0, newItem)
    selectedItem.value.concreteContent = newItem.cc32Id
  }
  // 対応するケア項目表がフォーカスの場合、具体的内容が1件以上の場合、
  else if (!contentItemAddFlg.value && refValue.value!.concreteContentList?.length) {
    // 対応するケア項目追加
    // 選択したデータのインデックスを取得する
    const currentIndex = concreteCareContentList.value?.findIndex(
      (item) => item.cc33Id === selectedItem.value.concreateCareContent
    )
    const currentLeng = refValue.value?.concreteCareItemList?.length ?? 0
    // 見つからない場合、処理終了
    if (currentIndex === -1) return
    const newCareItem: ConcreteCareItemType = {
      key: '',
      content: '',
      dmyCc32Id: selectedItem.value.concreteContent,
      // 画面一時用唯一ID
      cc33Id: `C33_${currentLeng}`,
      b1Cd: props.onewayModelValue.b1Cd,
      cc32Id: selectedItem.value.concreteContent,
      seq: currentLeng.toString(),
      ci2Id: concreteCareContentList.value?.[0]?.ci2Id ?? '',
      // ・更新区分：'C'
      updateKbn: UPDATE_KBN.CREATE,
    }
    // ・対応するケア項目表の最終に空白行を追加し、追加行を選択状態とする。
    refValue.value!.concreteCareItemList?.splice(currentIndex!, 0, newCareItem)
    selectedItem.value.concreateCareContent = newCareItem.cc33Id
  }
}

/**
 * 「行複写」ボタン押下
 */
const copyRowLine = () => {
  // レイアウト変更点
  // 非活性になる
  if (!props.onewayModelValue.tableDisplayFlg) return
  // 具体的内容が行選択の場合、
  if (contentItemAddFlg.value) {
    const currentIndex = refValue.value!.concreteContentList?.findIndex(
      (item) => item.cc32Id === selectedItem.value.concreteContent
    )
    // 見つからない、または選択したデータなしの場合は、処理終了
    if (currentIndex === undefined || currentIndex === -1) return
    const finedItem = refValue.value!.concreteContentList![currentIndex]
    // 選択行の情報をコピーして
    const newItem = {
      ...finedItem,
      // ダミーカウンター「dmyCc32Id」：最大ダミーカウンター「dmyCc32Id」+1
      cc32Id: (maxCount.value + 1).toString(),
      // 更新区分：'C'
      updateKbn: UPDATE_KBN.CREATE,
    }
    maxCount.value++
    // 該当行の下に新しい行を追加し
    refValue.value!.concreteContentList?.splice(currentIndex + 1, 0, newItem)
    // 追加行を選択状態とする。
    selectedItem.value.concreteContent = newItem.cc32Id
  } else {
    const currentIndex = refValue.value!.concreteCareItemList?.findIndex(
      (item) => item.cc33Id === selectedItem.value.concreateCareContent
    )
    if (currentIndex === undefined || currentIndex === -1) return
    const finedItem = refValue.value!.concreteCareItemList![currentIndex]
    // 選択行の情報をコピーして
    const newCareItem = {
      ...finedItem,
      cc33Id: `C33_${refValue.value!.concreteCareItemList?.length}`,
      // 更新区分：'C'
      updateKbn: UPDATE_KBN.CREATE,
      dmyCc32Id: selectedItem.value.concreteContent,
    }
    // 該当行の下に新しい行を追加し追加行を選択状態とする。
    refValue.value!.concreteCareItemList?.splice(currentIndex + 1, 0, newCareItem)
    selectedItem.value.concreateCareContent = newCareItem.cc33Id
  }
}

/**
 * 「行削除」ボタン押下
 */
const deleteRowLine = async () => {
  // レイアウト変更点
  // 非活性になる
  if (!props.onewayModelValue.tableDisplayFlg) return
  // データが不存在の場合、処理終了
  if (!refValue.value!.concreteContentList?.length) return
  // 具体的内容がフォーカスの場合、
  if (contentItemAddFlg.value) {
    // 以下のメッセージを表示
    // i.cmn.10882
    const result = await getDeleteConfirmResult(t('message.i-cmn-10882'))
    if (result === DIALOG_BTN.YES) {
      // はい：処理続き
      const currentIndex = refValue.value!.concreteContentList?.findIndex(
        (item) => item.cc32Id === selectedItem.value.concreteContent
      )
      if (currentIndex === undefined || currentIndex === -1) return
      const findedItem = refValue.value!.concreteContentList[currentIndex]
      // ・選択された対象行を画面に非表示する。
      // ・選択された行と対応の対応するケアリスト明細の更新区分を'D'で設定する。
      if (findedItem.updateKbn === UPDATE_KBN.CREATE) {
        // 新規データ削除識別用キーをつける
        findedItem.key = `${UPDATE_KBN.CREATE}_${UPDATE_KBN.DELETE}`
      }
      findedItem.updateKbn = UPDATE_KBN.DELETE
      // ・選択された対象行の関連の対応するケア項目データを画面に非表示する。
      // 非表示にするためにフィルタリング
      const filteredList = refValue.value!.concreteContentList.filter(
        (item) => item.updateKbn !== UPDATE_KBN.DELETE
      )
      // 削除した行が最後ではないの場合、次行を選択
      if (currentIndex < filteredList.length) {
        // 削除した行の次行を選択
        // 次行を取得
        const selectedConcreteContent = refValue.value?.concreteContentList.find(
          (item, index) => index > currentIndex && item.updateKbn !== UPDATE_KBN.DELETE
        )
        selectedItem.value.concreteContent = selectedConcreteContent?.cc32Id ?? ''
      } else if (filteredList.length <= currentIndex) {
        // 最後の行が削除された場合、前の行を選択
        if (!filteredList.length) {
          // スキップする
          return
        }
        selectedItem.value.concreteContent = filteredList[filteredList.length - 1].cc32Id
      }
      // 削除後リスト再確認、全削除した場合は選択したIDを空白にする
      if (
        refValue.value!.concreteContentList.every((item) => item.updateKbn === UPDATE_KBN.DELETE)
      ) {
        selectedItem.value.concreteContent = ''
      }
    } else {
      // いいえ：処理終了
      return
    }
  }
  if (!refValue.value!.concreteCareItemList) return
  // 対応するケア項目表がフォーカスの場合
  if (!contentItemAddFlg.value) {
    // 以下のメッセージを表示
    // i.cmn.10219
    const result = await getDeleteConfirmResult(t('message.i-cmn-10219'))
    if (result === DIALOG_BTN.YES) {
      // はい：処理続き
      // はい：処理続き
      const currentIndex = refValue.value!.concreteCareItemList?.findIndex(
        (item) => item.cc33Id === selectedItem.value.concreateCareContent
      )
      if (currentIndex === undefined || currentIndex === OrX0096Const.DEFAULT.INDEX_DEFAULT) return
      const findedItem = refValue.value!.concreteCareItemList[currentIndex]
      // ・選択された対象行を画面に非表示する。
      // ・選択された対象行の関連の対応するケア項目データを画面に非表示する。
      // ・選択された行と対応の対応するケアリスト明細の更新区分を'D'で設定する。
      findedItem.updateKbn = UPDATE_KBN.DELETE
      // 非表示にするためにフィルタリング
      const filteredList = refValue.value!.concreteCareItemList.filter(
        (item) => item.updateKbn !== UPDATE_KBN.DELETE
      )

      // 削除した行の次行を選択
      if (currentIndex < filteredList.length) {
        const selectedConcreteCareItem = refValue.value?.concreteCareItemList.find(
          (item, index) => index > currentIndex && item.updateKbn !== UPDATE_KBN.DELETE
        )
        selectedItem.value.concreateCareContent = selectedConcreteCareItem?.cc33Id ?? ''
      } else if (filteredList.length <= currentIndex) {
        // 最後の行が削除された場合、前の行を選択
        selectedItem.value.concreateCareContent = filteredList[filteredList.length - 1].cc33Id
      }
      // 削除後リスト再確認、全削除した場合は選択したIDを空白にする
      if (
        refValue.value!.concreteCareItemList.every((item) => item.updateKbn === UPDATE_KBN.DELETE)
      ) {
        selectedItem.value.concreateCareContent = ''
      }
    } else {
      // いいえ：処理終了
      return
    }
  }
}

/**
 * ヘーダクリック
 *
 * @param addFlg - 行追加フラグ
 */
const headerClick = (addFlg: boolean) => {
  contentItemAddFlg.value = addFlg
}

/**
 * 具体的内容とケア項目クリック
 *
 * @param addFlg - 行追加フラグ
 *
 * @param id - 行id
 */
const contentClick = (addFlg: boolean, id: string) => {
  contentItemAddFlg.value = addFlg
  if (addFlg) {
    selectedItem.value.concreteContent = id
  } else {
    selectedItem.value.concreateCareContent = id
  }
}

/**
 * 表示順変更ダイアログを呼び出す
 */
const setShowDisplayOrderChangeDialog = () => {
  // 具体的内容または対応するケア項目が存在しない場合は、処理を中断する
  if (!refValue.value?.concreteContentList || !refValue.value.concreteCareItemList) {
    return
  }
  // ［GUI00840_表示順変更ケアチェック表］画面を表示する。ポップアップ起動
  // 具体的内容がフォーカスの場合、具体的内容が1件以上の場合
  if (contentItemAddFlg.value && refValue.value.concreteContentList.length > 1) {
    // 呼び出す情報設定
    // 画面区分:0
    localOneway.or10347Oneway.parentViewType = '0'
    // 処理区分:0
    localOneway.or10347Oneway.parentProcessingType = '0'
    // データ情報:具体的な内容
    localOneway.or10347Oneway.sortList = refValue.value.concreteContentList
      .filter((item) => item.updateKbn !== UPDATE_KBN.DELETE)
      .map((item, index): Title => {
        return {
          sort: index + 1,
          correspondenceCareItem: '',
          concreteContents1: item.number,
          concreteContents2: item.content,
          concreteContents: '',
          key: item.cc32Id,
        }
      })
    // ポップアップを起動する
    Or10347Logic.state.set({
      uniqueCpId: or10347.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
  // 対応するケア項目表がフォーカスの場合、対応するケア項目表内容が1件以上の場合
  if (!contentItemAddFlg.value && refValue.value.concreteContentList.length > 1) {
    // 起動情報設定
    // 画面区分:0
    localOneway.or10347Oneway.parentViewType = '0'
    // 処理区分:1
    localOneway.or10347Oneway.parentProcessingType = '1'
    // データ情報:対応するケア項目
    localOneway.or10347Oneway.sortList = refValue.value.concreteCareItemList
      .filter(
        (item) =>
          item.cc32Id === selectedItem.value.concreteContent && item.updateKbn !== UPDATE_KBN.DELETE
      )
      .map((item, index) => {
        return {
          sort: index + 1,
          correspondenceCareItem: item.content,
          concreteContents: '',
          key: item.cc33Id,
        }
      })
    // ポップアップを起動する
    Or10347Logic.state.set({
      uniqueCpId: or10347.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 表示順変更ダイアログの返却値を取得
 *
 * @param result - 表示順変更ダイアログの返却値
 */
const getDisplayOrderDialogResult = (result: Or10347Type) => {
  console.log(result)
  // 選択領域は具体的内容の場合
  if (contentItemAddFlg.value) {
    const newSeqMap = new Map<string, number>(
      result.sortList?.map((item, index) => [(item as { id: string }).id, index])
    )
    console.log(refValue.value?.concreteContentList)
    console.log(newSeqMap)

    refValue.value!.concreteContentList =
      refValue.value!.concreteContentList?.sort((a, b) => {
        console.group('sort')
        console.log('a', a)
        console.log('b', b)
        console.groupEnd()
        const newAIndex = newSeqMap.get(a.cc32Id) ?? Infinity
        const newBIndex = newSeqMap.get(b.cc32Id) ?? Infinity
        return newAIndex - newBIndex
      }) ?? []
  }
  // 選択領域が対応するケア項目の場合
  else {
    const newCareSeqMap = new Map<string, number>(
      result.sortList?.map((item, index) => [(item as { id: string }).id, index])
    )
    console.log(newCareSeqMap)
    console.log(refValue.value?.concreteCareItemList)
    refValue.value!.concreteCareItemList =
      refValue.value!.concreteCareItemList?.sort((a, b) => {
        console.group('sort')
        console.log('a', a)
        console.log('b', b)
        console.groupEnd()
        const newAIndex = newCareSeqMap.get(a.cc33Id) ?? Infinity
        const newBIndex = newCareSeqMap.get(b.cc33Id) ?? Infinity
        return newAIndex - newBIndex
      }) ?? []
  }
}

/**
 * 医療「検索アイコン」ボタン押下
 */
const clickBtnForHealthCare = () => {
  masterProcessFlg = false
  // 起動前の設定
  localOneway.or27504Oneway.svJigyoId = props.onewayModelValue.officeId
  // 問題点CD：75
  localOneway.or27504Oneway.b4Cd = '75'
  // 具体的内容・対応するｹｱ項目マスタ］画面をポップアップで起動する
  Or27504Logic.state.set({
    uniqueCpId: or27504.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 医療「検索ボタン」返却値処理
 *
 * @param selectData - マスタ画面返却値
 */
const getMedicalCareResult = (selectData: {
  content: string
  mode: string
  ci1Id: string
  ci2Id: string
}) => {
  // モード区分：1上書 2追加
  // 具体的内容入力ID
  // 内容
  // 上書きの場合
  if (selectData.mode === OrX0096Const.DEFAULT.MASTER_CHANGE_FLG_OVERWRITE) {
    if (refValue.value?.thingsToKeepInMindContentsInfo?.memoKnj !== undefined) {
      refValue.value.thingsToKeepInMindContentsInfo.memoKnj = selectData.content
      refValue.value.thingsToKeepInMindContentsInfo.ci1Id = selectData.ci1Id
    }
  } else if (selectData.mode === OrX0096Const.DEFAULT.MASTER_CHANGE_FLG_ADD) {
    if (refValue.value?.thingsToKeepInMindContentsInfo?.memoKnj !== undefined) {
      if (refValue.value.thingsToKeepInMindContentsInfo.memoKnj === '') {
        refValue.value.thingsToKeepInMindContentsInfo.memoKnj = selectData.content
      } else {
        refValue.value.thingsToKeepInMindContentsInfo.memoKnj =
          selectData.content += `\r\n${selectData.content}`
      }
    }
  }
}

/**
 * マスタ画面処理フラグより、処理を行う
 *
 * @param selectData - マスタ画面返却値
 */
const processMasterData = (
  selectData:
    | Or27504TabDataType
    | {
        content: string
        mode: string
        ci1Id: string
        ci2Id: string
      }
) => {
  if (masterProcessFlg) {
    getMasterResult(selectData as Or27504TabDataType)
  } else {
    getMedicalCareResult(
      selectData as {
        content: string
        mode: string
        ci1Id: string
        ci2Id: string
      }
    )
  }
}

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<OrX0096Type>({
  cpId: OrX0096Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21735Const.CP_ID(1)]: or21735.value,
  [Or21736Const.CP_ID(1)]: or21736.value,
  [Or21737Const.CP_ID(1)]: or21737.value,
  [Or21738Const.CP_ID(1)]: or21738.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or27504Const.CP_ID(1)]: or27504.value,
  [Or10347Const.CP_ID(1)]: or10347.value,
})

/**************************************************
 * 画面イベント監視
 **************************************************/
// 「行追加ボタン」押下
watch(
  () => Or21735Logic.event.get(or21735.value.uniqueCpId),
  () => {
    addRowLine()
  }
)

// 「行挿入ボタン」押下
watch(
  () => Or21736Logic.event.get(or21736.value.uniqueCpId),
  () => {
    insertRowLine()
  }
)

// 「行複写ボタン」押下
watch(
  () => Or21737Logic.event.get(or21737.value.uniqueCpId),
  () => {
    copyRowLine()
  }
)

// 「行削除ボタン」押下
watch(
  () => Or21738Logic.event.get(or21738.value.uniqueCpId),
  async () => {
    await deleteRowLine()
  }
)

watch(
  () => props.modelValue,
  async (newValue) => {
    if (!newValue) return
    refValue.value = cloneDeep(newValue)

    // RefValueを更新する
    useScreenStore().setCpTwoWay({
      cpId: OrX0096Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: refValue.value,
      isInit: true,
    })
    console.log(newValue.concreteContentList?.[0])
    console.log(newValue.concreteContentList?.[0]?.cc32Id)

    // 各リスト一行目を選択状態にする「複写の場合は削除データ抜く」
    const concreteContent =
      newValue.concreteContentList?.find((item) => item.updateKbn !== UPDATE_KBN.DELETE)?.cc32Id ??
      ''
    selectedItem.value.concreteContent = concreteContent
    const concreateCareContent =
      newValue.concreteCareItemList?.find((item) => item.updateKbn !== UPDATE_KBN.DELETE)?.cc33Id ??
      ''
    selectedItem.value.concreateCareContent = concreateCareContent

    await nextTick()

    const leftHeight =
      document.querySelector(`#orX0096Wrapper${props.uniqueCpId}`)?.clientHeight ?? 0
    console.log('height', leftHeight)
    rightTableHeight.value = `${leftHeight - 28}px`
  },
  { immediate: true, deep: true }
)

/**
 * ケアリスト変更処理
 */
watch(
  () => selectedItem.value.concreteContent,
  (newValue) => {
    if (!newValue) return
    selectedItem.value.concreateCareContent = concreteCareContentList.value?.[0]?.cc33Id ?? ''
  },
  { deep: true, immediate: true }
)

/**
 * onewayModelvalue変更監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (!newValue || isNaN(newValue.maxCount)) return
    maxCount.value = newValue.maxCount
  },
  { deep: true }
)
</script>

<template>
  <c-v-row no-gutters>
    <c-v-col cols="12 pt-6 pb-6">
      <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway" />
    </c-v-col>
    <c-v-col
      class="d-flex justify-space-between"
      cols="12"
    >
      <c-v-col
        cols="4"
        class="table-content pr-6 table-header"
      >
        <!-- 左側テーブル -->
        <c-v-data-table
          :id="'orX0096Wrapper' + props.uniqueCpId"
          class="list-wrapper"
          hide-default-footer
          fixed-header
          :items-per-page="-1"
          :items="refValue?.listSection"
        >
          <template #headers>
            <tr>
              <th>
                <c-v-col class="d-flex">
                  {{ t('label.assessment-comprehensive-master-issuse-question') }}
                </c-v-col>
              </th>
              <th style="width: 42px">{{ t('label.assessment-comprehensive-master-ari') }}</th>
              <th style="width: 42px">{{ t('label.assessment-comprehensive-master-issuse') }}</th>
            </tr>
          </template>
          <template #item="{ item, index }">
            <tr v-if="props.onewayModelValue.tableDisplayFlg">
              <td @click="issuseCellClick(item, index)">
                <c-v-col class="d-flex justify-space-between align-center">
                  <div class="flex-1-1 h-100 d-flex">
                    <!-- 問題点番号 -->
                    <span>{{ setCircleNumber(index + 1) }}</span>
                    <!-- ラベル -->
                    <span>{{ item.label }}</span>
                  </div>
                  <div>
                    <base-at-button
                      class="search-button"
                      @click.stop="searchBtnClick(item, index)"
                      >{{ t('btn.search') }}</base-at-button
                    >
                  </div>
                </c-v-col>
              </td>
              <td
                class="text-center"
                @click="planCellClick(item, index)"
              >
                {{ item.isPlanningFlg ? '○' : '' }}
              </td>
              <td
                class="text-center"
                @click="developCellClick(item, index)"
              >
                {{ item.isHaveIssuesFlg ? '○' : '' }}
              </td>
            </tr>
          </template>
        </c-v-data-table>
      </c-v-col>
      <c-v-col
        cols="8 d-flex flex-column"
        style="border-left: 1px solid #b4c5dc"
      >
        <!-- 上段ボタンボックス -->
        <c-v-col
          cols="auto"
          class="d-flex flex-row pl-6 action-button-content"
        >
          <!-- 行追加ボタン -->
          <div class="action-content">
            <g-base-or-21735 v-bind="or21735"> </g-base-or-21735>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.add-row')"
            ></c-v-tooltip>
          </div>
          <!-- 行挿入ボタン -->
          <div class="ml-2 action-content">
            <g-base-or-21736 v-bind="or21736"></g-base-or-21736>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.insert-row')"
            ></c-v-tooltip>
          </div>
          <!-- 行複写ボタン: Or21737 -->
          <div class="ml-2 action-content">
            <g-base-or-21737 v-bind="or21737"></g-base-or-21737>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.duplicate-row')"
            ></c-v-tooltip>
          </div>
          <!-- 行削除ボタン: Or21738 -->
          <div class="ml-2 action-content">
            <g-base-or-21738 v-bind="or21738"></g-base-or-21738>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.delete-row')"
            ></c-v-tooltip>
          </div>
          <div class="d-flex align-center justify-end flex-1-1 pr-6 display-order-change-button">
            <!-- 表示順 -->
            <base-mo00611
              :oneway-model-value="localOneway.displayOrderBtnOneway"
              @click="setShowDisplayOrderChangeDialog"
            >
              <c-v-tooltip
                :text="$t('tooltip.care-plan2-display-order-icon-btn')"
                activator="parent"
                location="left"
              >
              </c-v-tooltip>
            </base-mo00611>
          </div>
        </c-v-col>
        <!-- 下段 -->
        <c-v-col
          class="flex-1-1 concrete-content-table mt-4"
          :style="{ height: rightTableHeight }"
        >
          <div
            id="table-content"
            class="d-flex justify-space-between h-100"
          >
            <c-v-col
              cols="6 h-100"
              :class="{ 'active-table': contentItemAddFlg }"
              @click="headerClick(true)"
            >
              <!-- 左側ラジオボタンテーブル -->
              <div class="concreate-content overflow-y-auto pb-2">
                <div class="concrete-header">
                  {{ t('label.assessment-comprehensive-master-concrete') }}
                </div>
                <div
                  v-if="props.onewayModelValue.tableDisplayFlg"
                  class="w-100"
                >
                  <div
                    v-for="(item, index) in refValue?.concreteContentList?.filter(
                      (item) => item.updateKbn !== UPDATE_KBN.DELETE
                    )"
                    :key="index"
                    :class="[
                      'concrete-table-cell d-flex',
                      { 'selected-row': item.cc32Id === selectedItem.concreteContent },
                    ]"
                    @click="contentClick(true, item.cc32Id)"
                  >
                    <!-- 番号 -->
                    <div class="flex-1-1 d-flex flex-column">
                      <input
                        v-model="item.number"
                        type="text"
                        class="bango-input"
                        maxlength="26"
                      />
                      <textarea
                        :id="`concrete_memo_${index}_id`"
                        v-model="item.content"
                        :name="`concrete_memo_${index}`"
                        class="flex-1-1 memo-textarea"
                      ></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </c-v-col>
            <c-v-col
              id="concrete-content-care-table"
              cols="6"
              :class="{ 'active-table': !contentItemAddFlg }"
              style="border-left: 1px solid #b4c5dc"
              @click="headerClick(false)"
            >
              <!-- 右側ラジオボタンテーブル -->
              <div class="concreate-content w-100 overflow-y-auto pb-2">
                <div class="concrete-header">
                  {{ t('label.assessment-comprehensive-master-concrete-care') }}
                </div>
                <div
                  v-if="props.onewayModelValue.tableDisplayFlg"
                  class="w-100"
                >
                  <div
                    v-for="(item, index) in concreteCareContentList"
                    :key="index.toString()"
                    :class="[
                      'concrete-table-cell d-flex',
                      { 'selected-row': item.cc33Id === selectedItem.concreateCareContent },
                    ]"
                    @click="contentClick(false, item.cc33Id)"
                  >
                    <!-- 番号 -->
                    <div class="flex-1-1 d-flex flex-column">
                      <textarea
                        :id="`concrete_memo_${index}_id`"
                        v-model="item.content"
                        :name="`concrete_memo_${index}`"
                        class="flex-1-1 memo-textarea care-textarea"
                      ></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </c-v-col>
          </div>
        </c-v-col>
      </c-v-col>
    </c-v-col>
    <!-- テキストフィールド -->
    <c-v-col
      v-if="props.onewayModelValue.showInputFlg && props.onewayModelValue.tableDisplayFlg"
      cols="12"
      class="d-flex align-center pt-6 pb-6 pr-12"
      style="border-top: 1px solid #b4c5dc"
    >
      <!-- テキストフィールド -->
      <c-v-col
        cols="auto"
        class="pa-2 h-100 d-flex align-center"
        style="
          background-color: rgb(219, 238, 254);
          width: 268px;
          border: 1px solid rgb(var(--v-theme-black-200));
        "
      >
        {{ t('label.assessment-comprehensive-master-medice-management-things-to-keep-in-mind') }}
      </c-v-col>
      <c-v-col
        cols="auto h-100 d-flex align-center pl-2"
        style="
          background-color: #fff;
          border-top: 1px solid rgb(var(--v-theme-black-200));
          border-bottom: 1px solid rgb(var(--v-theme-black-200));
        "
      >
        <base-mo00009
          :oneway-model-value="localOneway.wholeLifeShouldSolutionIssuesInputSupportOneway"
          style="color: #869fca !important"
          @click="clickBtnForHealthCare"
        ></base-mo00009>
      </c-v-col>
      <c-v-col
        cols="auto"
        class="flex-1-1"
        style="
          background-color: #fff;
          border: 1px solid rgb(var(--v-theme-black-200));
          border-left: none;
        "
      >
        <textarea
          v-if="refValue?.thingsToKeepInMindContentsInfo"
          :id="generateRandomTextareaId()"
          v-model="refValue.thingsToKeepInMindContentsInfo.memoKnj"
          class="medicaManagement h-100 w-100 pa-2"
        ></textarea>
      </c-v-col>
    </c-v-col>
    <g-base-or21814
      v-if="showDialog21814"
      v-bind="or21814"
    />
    <g-custom-or-27504
      v-if="showDialogOr27504"
      v-bind="or27504"
      v-model="local.or27504"
      :oneway-model-value="localOneway.or27504Oneway"
      @update:model-value="processMasterData"
    />
    <!-- 表示順変更フラグ -->
    <g-custom-or-10347
      v-if="showDialogOr10347"
      v-bind="or10347"
      v-model="local.or10347"
      :oneway-model-value="localOneway.or10347Oneway"
      @update:model-value="getDisplayOrderDialogResult"
    />
  </c-v-row>
</template>

<style lang="scss" scoped>
@use '@/styles/base-data-table.scss';
@use '@/styles/cmn/mo-data-table.scss';
.v-col {
  padding: 0px;
}
.table-header :deep(.list-wrapper th) {
  white-space: nowrap;
  font-weight: normal !important;
  padding: 0px 8px 0 12px !important;
  background-color: rgba(219, 238, 254, 1) !important;
}
:deep(.list-wrapper td) {
  cursor: pointer;
  padding: 0px 8px 0 12px !important;
  height: 48px !important;
  min-width: 64px;
  font-size: 14px;
  white-space: pre !important;
  background-color: #fff !important;
  user-select: none;
  &.text-center {
    padding: 0px !important;
    font-size: 14px;
  }
}

:deep(.list-wrapper) {
  tr.selected-row {
    border-color: rgb(var(--v-theme-key)) !important;
    td {
      background-color: rgb(var(--v-theme-blue-100)) !important; // 背景色
    }
  }
}
:deep(.list-wrapper td.concrete-content-table-cell) {
  cursor: pointer;
  padding: 0 !important;
  height: 80px !important;
}
.concrete-table-cell {
  height: 92px;
  border: 1px solid rgba(var(--v-theme-black-200), 0.6) !important;
  background-color: #fff;
  border-bottom: none;
  white-space: wrap;
  width: 100% !important;
  position: relative;
}
:deep(.v-selection-control) {
  width: 40px !important;
}
.border-right {
  border-right: 2px solid rgba(var(--v-theme-black-200), 0.6) !important;
  width: 48px;
  justify-content: center !important;
  .mr-1 {
    margin: 0px !important;
  }
}
.bango-input {
  outline: none;
  padding: 8px 12px;
  border-bottom: 1px dashed rgba(var(--v-theme-black-200), 0.6) !important;
  height: 32px;
}
.memo-textarea {
  outline: none;
  padding: 8px;
  resize: none;
  padding: 8px 12px;
  line-height: 21px;
  box-sizing: border-box;
  position: relative;
  &.care-textarea {
    padding: 16px;
    line-height: 19px;
    box-sizing: border-box;
  }
}
.search-button {
  height: 16px !important;
  background-color: #fff !important;
  color: #075ee6 !important;
  border: 1px solid #075ee6 !important;
  min-height: 32px !important;
  min-width: 52px !important;
  padding: 0px !important;
  text-align: center;
}
:deep(.list-wrapper .v-col-auto) {
  width: 100%;
}
.concrete-content-table {
  border: 1px solid #b4c5dc;
  border-bottom: none;
  border-left: none;
  border-right: none;
}
.active-table {
  background-color: rgba(13, 110, 253, 0.08);
  transition: 0.2s all;
}
.medicaManagement {
  resize: none;
  outline: none;
  display: block;
  height: 83px !important;
}
:deep(.v-sheet) {
  background: transparent;
}
:deep(.v-table) {
  background-color: transparent !important;
  tbody {
    background-color: #fff !important;
  }
}
.problem-title :deep(.v-sheet) {
  background-color: transparent !important;
}
.concreate-content {
  padding: 24px 24px 0;
  min-width: 437px;
  .concrete-header {
    padding-bottom: 16px;
    font-size: 16px;
    font-weight: bold;
  }
}
.action-button-content {
  height: 32px;
  .v-btn {
    min-width: 89px !important;
    height: 32px !important;
  }
  .display-order-change-button {
    height: max-content;
    .v-btn {
      min-width: 65px !important;
      height: 32px !important;
    }
  }
  .action-content {
    height: max-content;
  }
}
.selected-row {
  border-color: rgba(7, 96, 230, 1) !important;
  input,
  textarea {
    background-color: rgba(7, 96, 230, 0.08);
  }
}
</style>
