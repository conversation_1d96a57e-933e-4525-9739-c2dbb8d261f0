<script setup lang="ts">
/**
 * Or00393: コンテンツエリアタブ
 * GUI00866_サマリー表画面
 *
 * @description
 * サマリー表画面
 *
 * <AUTHOR> PHAM HO HAI DANG
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or10962Logic } from '../Or10962/Or10962.logic'
import { Or10962Const } from '../Or10962/Or10962.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or00398Const } from '../Or00398/Or00398.constants'
import { Or09999Const } from '../Or09999/Or09999.constants'
import { Or09999Logic } from '../Or09999/Or09999.logic'
import type { Or00393StateType, TableHeader } from './Or00393.type'
import { Or00393Const } from './Or00393.constants'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00028OnewayType, Mo00028Type } from '~/types/business/components/Mo00028Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import type {
  Or00393OnewayType,
  Summary1Info,
  SummarySelectInfo,
} from '~/types/cmn/business/components/Or00393Type'
import type {
  RirekiJoho,
  PlanPeriodInfo,
  TransmitParam,
} from '~/types/cmn/business/components/Or41339Type'
import {
  useScreenOneWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type {
  summaryTableInfoSelectServiceInEntity,
  summaryTableInfoSelectServiceOutEntity,
} from '~/repositories/cmn/entities/summaryTableInfoSelectServiceEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01408OnewayType } from '~/types/business/components/Mo01408Type'
import type { Or09999Type, Or09999OnewayType } from '~/types/cmn/business/components/Or09999Type'
import type { kentouyousiInfoUpdateOutEntity } from '~/repositories/cmn/entities/kentouyousiInfoUpdateEntity'
import type { summaryTableInfoUpdateInEntity } from '~/repositories/cmn/entities/summaryTableInfoUpdateEntity'
/**
 *多言語対応用関数
 */
const { t } = useI18n()

/**
 *システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 *テーブルのヘッダー情報を格納するリアクティブ変数
 */
const headers = ref<unknown[]>([])
/**
 *行選択状態フラグ
 */
const selected = ref(false)
/**
 *テーブル2のタイトル
 */
const titleTable2 = ref('')
/**
 *グループID
 */
const idGroup = ref('1')
/**
 *行サイズ変更フラグ
 */
const rowResizeFlag = ref<boolean>(false)

/**
 *テーブル2選択行インデックス
 */
const selectedItemIndex = ref<number>(0)
/**
 *テーブル3選択行インデックス
 */
const selectedItemIndex3 = ref<number>(0)
/**
 *ダイアログ表示メッセージ
 */
const messageDialog = ref<string>('')
/**
 *編集状態フラグ配列
 */
const isEditState = ref<boolean[]>([])
/**
 *編集可能フラグ（一般）
 */
const isEdit = ref<boolean[]>([])
/**
 *編集可能フラグ（課題）
 */
const isEditIssues = ref<boolean[]>([])
/**
 *編集可能フラグ（長期）
 */
const isEditLong = ref<boolean[]>([])
/**
 *編集可能フラグ（並び順）
 */
const isEditSort = ref<boolean[]>([])
/**
 *編集可能フラグ（詳細）
 */
const isEditDetail = ref<boolean[]>([])

/**
 *Or10962コンポーネント識別用ID
 */
const or10962 = ref({ uniqueCpId: Or10962Const.CP_ID(1) })
/**
 *Or21814コンポーネント識別用ID
 */
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })

/**
 * Or09999コンポーネント識別用ID
 */
const or09999 = ref({
  uniqueCpId: '', // 親画面.画面ID
  screenId: Or09999Const.SCREEN_ID.CONSIDERATION_FORM,
})

/**
 * 左側フォーカス状態
 */
const isfocusLeft = ref<boolean>(true)

/**
 * Or10962から返却されたテキスト
 */
const returnTextOr10962 = ref('')

/**
 * 画面ユーティリティ関数の取得
 */
const { setChildCpBinds } = useScreenUtils()

const or09999Type = ref<Or09999Type>({
  issueDataInfoList: [],
})

/**
 * 親画面からの初期値
 */
const or09999Data = ref<Or09999OnewayType>({
  issueDataInfoList: [],
})
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or00393OnewayType
  uniqueCpId: string
}
/**
 *propsの定義
 */
const props = defineProps<Props>()

/**
 *ローカルデータを格納するリアクティブオブジェクト
 */
const local = reactive({
  // 期間管理フラグ
  kikanKanriFlg: '',
  // 利用者ID
  userId: '',
  // 基準日
  kijunbiYmd: '',
  // 履歴情報
  historyInfo: {} as RirekiJoho,
  // 計画期間情報
  planPeriodInfo: {} as PlanPeriodInfo,
  /**
   * 複写
   */
  isCopyMode: false,
})
/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or00393StateType>({
  cpId: Or00393Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     *パラメータ更新時の処理
     *
     * @param value - 受信したパラメータ
     */
    param: (value: TransmitParam) => {
      if (value) {
        local.kikanKanriFlg = value.kikanKanriFlg + ''
        local.historyInfo = value.historyInfo ?? 0
        local.planPeriodInfo = value.planPeriodInfo
        local.userId = value.userId + ''
        local.kijunbiYmd = value.kijunbiYmd + ''
      }
      switch (value?.executeFlag) {
        //保存
        // case 'save':
        //   void save()
        //   break
        //新規
        case 'add':
          void add()
          break
        //複写
        case 'copy':
          void copy()
          break
        //削除
        case 'delete':
          break
        //データ再取得
        case 'getData':
          void init()
          break
        default:
          break
      }
    },

    isCopyMode(value) {
      local.isCopyMode = value ?? false
    },
  },
})

useSetupChildProps(props.uniqueCpId, {
  [Or09999Const.CP_ID(0)]: or09999.value,
})

/**
 *ダイアログ表示フラグ
 */
const showDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr09999 = computed(() => {
  // Or09999のダイアログ開閉状態
  return Or09999Logic.state.get(or09999.value.uniqueCpId)?.isOpen ?? false
})

/**
 *一覧ステータス
 */
const listTable1 = ref<Summary1Info[]>([])

/** 一覧ステータス */
const listTable2 = ref<Or00393StateType>({
  headers: [
    {
      title: t('label.CAP'),
      key: 'CAP',
      minWidth: '217px',
      width: '217px',
      sortable: false,
    },
    {
      title: t('label.trigger'),
      key: 'trigger',
      minWidth: '160px',
      width: '160px',
      sortable: false,
    },
    {
      title: t('label.priority-ranking'),
      key: 'priority-ranking',
      minWidth: '80px',
      width: '80px',
      sortable: false,
    },
    {
      title: t('label.state'),
      key: 'state',
      minWidth: '300px',
      width: '300px',
      sortable: false,
    },
    {
      title: t('label.methods-of-care'),
      key: 'methods-of-care',
      minWidth: '300px',
      width: '300px',
      sortable: false,
    },
  ] as TableHeader[],
  items: [],
  rowProps: getItemClass,
})

const columnMinWidth = ref<number[]>([243, 260, 260, 260, 260])
/** 一覧ステータス */
const listTable3 = ref<Or00393StateType>({
  headers: [
    {
      title: t('label.CAP'),
      key: 'CAP',
      minWidth: '243px',
      width: '243px',
      sortable: false,
    },
    {
      title: t('label.issues'),
      key: 'issues',
      minWidth: '260px',
      width: '260px',
      sortable: false,
    },
    {
      title: t('label.long-term-goal'),
      key: 'long-term-goal',
      minWidth: '260px',
      width: '260px',
      sortable: false,
    },
    {
      title: t('label.short-term-goal'),
      key: 'short-term-goal',
      minWidth: '260px',
      width: '260px',
      sortable: false,
    },
    {
      title: t('label.care-details'),
      key: 'care-details',
      minWidth: '260px',
      width: '260px',
      sortable: false,
    },
  ] as TableHeader[],
  items: [],
  rowProps: getItemClass3,
})

/**
 * 選択行と総行数
 */
const or26184 = ref({
  selectedIndex: selectedItemIndex.value,
  totalLine: listTable2.value.items?.length,
})

/**
 * 選択行と総行数
 */
const or26184_2 = ref({
  selectedIndex: selectedItemIndex3.value,
  totalLine: listTable3.value.items?.length,
})

/**
 *ローカルOneway
 */
const localOneway = reactive({
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  mo00009Scale: {
    btnIcon: 'height',
    density: 'compact',
    size: '36px',
  },
  mo00028OnewayType: {
    panelTitle: t('label.issues-and-goal'),
    customClass: new CustomClass({
      itemClass: 'bold text-center w-100',
    }),
  } as Mo00028OnewayType,
  mo00028: {
    panel: ref(1),
  } as Mo00028Type,
  //  行追加ボタン
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    width: '90px',
  } as Mo00611OnewayType,
  //行挿入ボタン
  mo00611OnewayAddRow: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    width: '90px',
  } as Mo00611OnewayType,
  // 行複写ボタン
  mo00611OnewayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    width: '90px',
  } as Mo00611OnewayType,
  // 行複写ボタン(課題)
  mo00611OnewayCopyIssues: {
    btnLabel: t('btn.duplicate-row') + '（' + t('label.issues') + '）',
    prependIcon: 'file_copy',
    width: '143px',
  } as Mo00611OnewayType,
  // 行削除ボタン
  mo01265Oneway: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    width: '90px',
  } as Mo01265OnewayType,
  btnAddItemTooltip: {
    memoInputIconBtn: t('tooltip.add-row'),
    auxiliaryInputDialogBtn: t('tooltip.add-row'),
  },
  btnAddRowTooltip: {
    memoInputIconBtn: t('tooltip.insert-row'),
    auxiliaryInputDialogBtn: t('tooltip.insert-row'),
  },
  btnDuplicateRowTooltip: {
    memoInputIconBtn: t('tooltip.duplicate-row'),
    auxiliaryInputDialogBtn: t('tooltip.duplicate-row'),
  },
  btnDuplicateRowIssuesTooltip: {
    memoInputIconBtn: t('tooltip.duplicate-row-issue'),
    auxiliaryInputDialogBtn: t('tooltip.duplicate-row-issue'),
  },
  btnDeleteRowTooltip: {
    memoInputIconBtn: t('tooltip.delete-row'),
    auxiliaryInputDialogBtn: t('tooltip.delete-row'),
  },
  btnSortTooltip: {
    memoInputIconBtn: t('tooltip.display-order'),
    auxiliaryInputDialogBtn: t('tooltip.display-order'),
  },
  or51775Oneway: {
    title: t('label.state'),
    screenId: props.uniqueCpId,
    bunruiId: '',
    t1Cd: '725',
    t2Cd: '1',
    t3Cd: '',
    tableName: 'cpn_tuc_rai_plan1',
    columnName: 'problem_knj',
    inputContents: '',
    assessmentMethod: '01',
    shokuinId: '1',
  } as unknown as Or51775OnewayType,
  or51775Oneway_2: {
    title: t('label.methods-of-care'),
    screenId: props.uniqueCpId,
    bunruiId: '',
    t1Cd: '725',
    t2Cd: '2',
    t3Cd: '',
    tableName: 'cpn_tuc_rai_plan1',
    columnName: 'problem_knj',
    inputContents: '',
    assessmentMethod: '01',
    shokuinId: '1',
  } as unknown as Or51775OnewayType,
  or51775Oneway_3: {
    title: t('label.issues'),
    screenId: props.uniqueCpId,
    bunruiId: '',
    t1Cd: '820',
    t2Cd: '1',
    t3Cd: '0',
    tableName: 'cpn_tuc_rai_plan2',
    columnName: 'kadai_knj',
    inputContents: '',
    assessmentMethod: '01',
    shokuinId: '1',
  } as unknown as Or51775OnewayType,
  or51775Oneway_4: {
    title: t('label.long-term-goals'),
    screenId: props.uniqueCpId,
    bunruiId: '',
    t1Cd: '820',
    t2Cd: '4',
    t3Cd: '0',
    tableName: 'cpn_tuc_rai_plan2',
    columnName: 'kadai_knj',
    inputContents: '',
    assessmentMethod: '01',
    shokuinId: '1',
  } as unknown as Or51775OnewayType,
  or51775Oneway_5: {
    title: t('label.short-term-goal'),
    screenId: props.uniqueCpId,
    bunruiId: '',
    t1Cd: '820',
    t2Cd: '4',
    t3Cd: '0',
    tableName: 'cpn_tuc_rai_plan2',
    columnName: 'tanki_knj',
    inputContents: '',
    assessmentMethod: '01',
    shokuinId: '1',
  } as unknown as Or51775OnewayType,
  or51775Oneway_6: {
    title: t('label.care-details'),
    screenId: props.uniqueCpId,
    bunruiId: '',
    t1Cd: '820',
    t2Cd: '6',
    t3Cd: '0',
    tableName: 'cpn_tuc_rai_plan2',
    columnName: 'care_knj',
    inputContents: '',
    assessmentMethod: '01',
    shokuinId: '1',
  } as unknown as Or51775OnewayType,
  mo01338Oneway: {
    value: '',
    customClass: new CustomClass({ outerClass: 'bg-transparent' }),
  } as Mo01338OnewayType,
  mo01280Oneway: {
    maxlength: Or00393Const.DEFAULT.MAX_LENGTH,
    showItemLabel: false,
    rows: '4',
  } as Mo01274OnewayType,
  mo01280OnewayTable3: {
    maxlength: Or00393Const.DEFAULT.MAX_LENGTH,
    showItemLabel: false,
    rows: '3',
  } as Mo01274OnewayType,
  mo01282Oneway: {
    items: [],
    itemTitle: 'result',
    itemValue: 'capId',
  } as Mo01282OnewayType,
  mo01408Oneway: {
    showItemLabel: false,
    items: [],
    multiple: false,
    width: '245px',
  } as Mo01408OnewayType,
})

/**
 * Or10962ダイアログを開く関数
 */
const showDialogOr10962 = computed(() => {
  // Or10962のダイアログ開閉状態
  return Or10962Logic.state.get(or10962.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Or10962ダイアログを開く関数
 */
function onClickOr10962() {
  if (local.isCopyMode) return
  // Or10962のダイアログ開閉状態を更新する
  Or10962Logic.state.set({
    uniqueCpId: or10962.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * Or51775のユニークIDを保持するref
 */
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(1) })
/**
 * Or51775ダイアログの表示状態を監視するcomputedプロパティ
 */
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or51775の確認ダイアログのOK時の処理
 *
 * @param data - 確認ダイアログから返されるデータ
 */
const handleConfirm51775 = (data: Or51775ConfirmType) => {
  listTable2.value.items![selectedItemIndex.value].problemKnj!.value = data.value
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * Or51775ダイアログを開く関数
 *
 * @param index - index
 */
function onClickOr51775(index: number) {
  selectedItemIndex.value = index
  if (local.isCopyMode) return
  if (selected.value || listTable2.value.items?.length === Or00393Const.DEFAULT.ZERO) return
  // Or10962のダイアログ開閉状態を更新する
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *Or51775の2つ目のユニークIDを保持するref
 */
const or51775_2 = ref({ uniqueCpId: Or51775Const.CP_ID(2) })
/**
 *Or51775_2ダイアログの表示状態を監視するcomputedプロパティ
 */
const showDialogOr51775_2 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775_2.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or51775_2の確認ダイアログのOK時の処理
 *
 * @param data - 確認ダイアログから返されるデータ
 */
const handleConfirm51775_2 = (data: Or51775ConfirmType) => {
  Or51775Logic.state.set({
    uniqueCpId: or51775_2.value.uniqueCpId,
    state: { isOpen: false },
  })
  listTable2.value.items![selectedItemIndex.value].careKnj!.value = data.value
}
/**
 *Or51775_2ダイアログを開く関数
 *
 * @param index - index
 */
function onClickOr51775_2(index: number) {
  selectedItemIndex.value = index
  if (local.isCopyMode) return
  if (selected.value || listTable2.value.items?.length === Or00393Const.DEFAULT.ZERO) return
  // Or10962のダイアログ開閉状態を更新する
  Or51775Logic.state.set({
    uniqueCpId: or51775_2.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * Or51775の3つ目のユニークIDを保持するref
 */
const or51775_3 = ref({ uniqueCpId: Or51775Const.CP_ID(3) })
/**
 *Or51775_3ダイアログの表示状態を監視するcomputedプロパティ
 */
const showDialogOr51775_3 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775_3.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or51775_3の確認ダイアログのOK時の処理
 *
 * @param data - 確認ダイアログから返されるデータ
 */
const handleConfirm51775_3 = (data: Or51775ConfirmType) => {
  Or51775Logic.state.set({
    uniqueCpId: or51775_3.value.uniqueCpId,
    state: { isOpen: false },
  })
  listTable3.value.items![selectedItemIndex3.value].kadaiKnj!.value = data.value
}
/**
 * Or51775_3ダイアログを開く関数
 *
 * @param index - index
 */
function onClickOr51775_3(index: number) {
  selectedItemIndex3.value = index
  if (local.isCopyMode) return
  if (selected.value || listTable3.value.items?.length === Or00393Const.DEFAULT.ZERO) return
  // Or10962のダイアログ開閉状態を更新する
  Or51775Logic.state.set({
    uniqueCpId: or51775_3.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *Or51775の4つ目のユニークIDを保持するref
 */
const or51775_4 = ref({ uniqueCpId: Or51775Const.CP_ID(4) })

/**
 *ダイアログ表示フラグ
 */
const showDialogOr51775_4 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775_4.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or51775_4の確認ダイアログのOK時の処理
 *
 * @param data - 確認ダイアログから返されるデータ
 */
const handleConfirm51775_4 = (data: Or51775ConfirmType) => {
  Or51775Logic.state.set({
    uniqueCpId: or51775_4.value.uniqueCpId,
    state: { isOpen: false },
  })
  listTable3.value.items![selectedItemIndex3.value].choukiKnj!.value = data.value
}
/**
 * Or51775_4ダイアログを開く関数
 *
 * @param index - index
 */
function onClickOr51775_4(index: number) {
  selectedItemIndex3.value = index
  if (local.isCopyMode) return
  if (selected.value || listTable3.value.items?.length === Or00393Const.DEFAULT.ZERO) return
  // Or10962のダイアログ開閉状態を更新する
  Or51775Logic.state.set({
    uniqueCpId: or51775_4.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * Or51775の5つ目のユニークIDを保持するref
 */
const or51775_5 = ref({ uniqueCpId: Or51775Const.CP_ID(5) })
/**
 * Or51775_5ダイアログの表示状態を監視するcomputedプロパティ
 */
const showDialogOr51775_5 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775_5.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or51775_5の確認ダイアログのOK時の処理
 *
 * @param data - 確認ダイアログから返されるデータ
 */
const handleConfirm51775_5 = (data: Or51775ConfirmType) => {
  Or51775Logic.state.set({
    uniqueCpId: or51775_5.value.uniqueCpId,
    state: { isOpen: false },
  })
  listTable3.value.items![selectedItemIndex3.value].tankiKnj!.value = data.value
}
/**
 * Or51775_5ダイアログを開く関数
 *
 * @param index - index
 */
function onClickOr51775_5(index: number) {
  selectedItemIndex3.value = index
  if (local.isCopyMode) return
  if (selected.value || listTable3.value.items?.length === Or00393Const.DEFAULT.ZERO) return
  // Or10962のダイアログ開閉状態を更新する
  Or51775Logic.state.set({
    uniqueCpId: or51775_5.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * Or51775の6つ目のユニークIDを保持するref
 */
const or51775_6 = ref({ uniqueCpId: Or51775Const.CP_ID(6) })
/**
 * Or51775_6ダイアログの表示状態を監視するcomputedプロパティ
 */
const showDialogOr51775_6 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775_6.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or51775_6の確認ダイアログのOK時の処理
 *
 * @param data - 確認ダイアログから返されるデータ
 */
const handleConfirm51775_6 = (data: Or51775ConfirmType) => {
  Or51775Logic.state.set({
    uniqueCpId: or51775_6.value.uniqueCpId,
    state: { isOpen: false },
  })
  listTable3.value.items![selectedItemIndex3.value].careKnj!.value = data.value
}
/**
 * Or51775_6ダイアログを開く関数
 *
 * @param index - index
 */
function onClickOr51775_6(index: number) {
  selectedItemIndex3.value = index
  if (local.isCopyMode) return
  if (selected.value || listTable3.value.items?.length === Or00393Const.DEFAULT.ZERO) return
  // Or10962のダイアログ開閉状態を更新する
  Or51775Logic.state.set({
    uniqueCpId: or51775_6.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 選択された行番号
 */
watch(
  () => selectedItemIndex.value,
  (newValue) => {
    or26184.value.selectedIndex = newValue
  }
)

/**
 * 選択された行番号
 */
watch(
  () => selectedItemIndex3.value,
  (newValue) => {
    or26184_2.value.selectedIndex = newValue
  }
)

/**
 * 総行数
 */
watch(
  () => listTable2.value.items,
  (newValue) => {
    or26184.value.totalLine = newValue ? newValue.length : 0
    or26184.value.selectedIndex = newValue ? selectedItemIndex.value : -1
  },
  { deep: true }
)

/**
 * 総行数
 */
watch(
  () => listTable3.value.items,
  (newValue) => {
    or26184_2.value.totalLine = newValue ? newValue.length : 0
    or26184_2.value.selectedIndex = newValue ? selectedItemIndex3.value : -1
  },
  { deep: true }
)

watch(
  () => selected.value,
  (newValue) => {
    if (newValue) {
      selectedItemIndex.value = -1
      selectedItemIndex3.value = -1
      or26184.value.totalLine = 0
      or26184_2.value.totalLine = 0
    } else {
      selectedItemIndex.value = 0
      selectedItemIndex3.value = 0
      or26184.value.totalLine = listTable2.value.items?.length
      or26184_2.value.totalLine = listTable3.value.items?.length
    }
  }
)

watch(
  () => returnTextOr10962.value,
  (newValue) => {
    if (newValue !== Or00398Const.DEFAULT.EMPTY_STRING && listTable2.value.items!.length > 0) {
      if (isfocusLeft.value) {
        listTable2.value.items![selectedItemIndex.value].problemKnj!.value +=
          `${returnTextOr10962.value}`
      } else {
        listTable2.value.items![selectedItemIndex.value].careKnj!.value +=
          `${returnTextOr10962.value}`
      }
      returnTextOr10962.value = ''
    }
  }
)
watch(
  () => or09999Type.value,
  (newValue) => {
    if (!newValue) return
    listTable3.value.items = []
    listTable3.value.items =
      newValue.issueDataInfoList.map((x) => ({
        raiId: x.raiId,
        capIdModel: {
          value: x.capId,
        },
        kadaiId: x.kadaiId,
        kadaiKnj: { value: x.issues! },
        choukiKnj: { value: x.longTermGoal! },
        tankiKnj: { value: x.shortTermGoal! },
        careKnj: { value: x.careDetails! },
      })) ?? []
  }
)

/**
 * 行を選択する関数
 *
 * @param index - 選択する行のインデックス
 */
function onSelectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * 行選択処理
 *
 * @param _e - クリックイベント（使用しない）
 *
 * @param row - 行情報
 */
function selectRow3(_e: never, row: { index: number; item: { id: string } }) {
  selectedItemIndex3.value = row.index
}

/**
 * 選択行にクラスを付与する関数
 *
 * @param row - 行情報（インデックスとアイテムIDを含む）
 *
 * @returns クラス情報と行のインデックス。選択行には'select-row'を付与。
 */
function getItemClass(row: { index: number; item: { id: string } }): {
  class: Record<string, boolean>
  value: number
} {
  return selectedItemIndex.value === row.index
    ? { class: { 'select-row': true, 'row-zoom': rowResizeFlag.value }, value: row.index }
    : { class: { 'row-zoom': rowResizeFlag.value }, value: -1 }
}

/**
 * 選択された行にクラスを付与する関数
 *
 * @param row - 行情報（インデックスとアイテムIDを含むオブジェクト）
 *
 * @returns クラス情報と行のインデックス。選択行の場合は'select-row'クラスを付与し、valueに行番号を返す。非選択行は空クラスと-1を返す。
 */
function getItemClass3(row: { index: number; item: { id: string } }): {
  class: Record<string, boolean>
  value: number
} {
  return selectedItemIndex3.value === row.index
    ? { class: { 'select-row': true }, value: row.index }
    : { class: {}, value: -1 }
}

/**
 * 拡大縮小アイコン押下
 */
// function onResizeRow() {
//   if (selected.value) return
//   rowResizeFlag.value = !rowResizeFlag.value
// }
/**
 * 新しいアイテムを追加する
 */
function onAddItem() {
  if (local.isCopyMode) return
  if (selected.value) return
  listTable3.value.items?.push({
    capIdModel: {
      value: listTable2.value.items![selectedItemIndex.value].capId,
    },
  })
  selectedItemIndex3.value =
    listTable3.value.items?.length === undefined ? 0 : listTable3.value.items.length - 1
}

/**
 * 選択位置に行を挿入する
 */
function onAddRow() {
  if (local.isCopyMode) return
  if (selected.value) return
  listTable3.value.items?.splice(selectedItemIndex3.value, 0, {
    capIdModel: {
      value: listTable2.value.items![selectedItemIndex.value].capId,
    },
  })
  if (listTable3.value.items?.length === Or00393Const.DEFAULT.ONE) {
    selectedItemIndex3.value++
  }
}

/**
 * 選択行を複製する
 */
function onCloneItem() {
  if (local.isCopyMode) return
  if (selected.value || listTable3.value.items?.length === Or00393Const.DEFAULT.ZERO) return
  listTable3.value.items?.splice(
    selectedItemIndex3.value + 1,
    0,
    listTable3.value.items[selectedItemIndex3.value]
  )
  selectedItemIndex3.value++
}

/**
 * 選択行を複製する
 */
function onCloneItemCustom() {
  if (local.isCopyMode) return
  if (selected.value || listTable3.value.items?.length === Or00393Const.DEFAULT.ZERO) return
  listTable3.value.items?.splice(selectedItemIndex3.value + 1, 0, {
    capIdModel: {
      value: listTable2.value.items![selectedItemIndex.value].capId,
    },
    kadaiKnj: {
      value: listTable3.value.items[selectedItemIndex3.value].kadaiKnj!.value,
    },
    choukiKnj: {
      value: listTable3.value.items[selectedItemIndex3.value].choukiKnj!.value,
    },
  })
  selectedItemIndex3.value++
}

/**
 * アイテムを削除するための確認ダイアログを表示する
 */
function onDelete() {
  if (local.isCopyMode) return
  if (
    selected.value ||
    listTable3.value.items === undefined ||
    listTable3.value.items.length === Or00393Const.DEFAULT.ZERO
  ) {
    return
  }
  messageDialog.value = t('message.i-cmn-10219')
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: messageDialog.value,
    },
  })
}

watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      listTable3.value.items!.splice(selectedItemIndex3.value, 1)
      if (
        selectedItemIndex3.value > 0 ||
        listTable3.value.items!.length === Or00393Const.DEFAULT.ZERO
      ) {
        selectedItemIndex3.value--
      }
    }
  }
)

/**
 * コードの初期化処理
 */
function initCodes() {
  headers.value = [
    {
      title: t('label.BMI'),
      key: 'scaleBmi',
      minWidth: '12%',
      width: '12%',
      sortable: false,
    },
    {
      title: t('label.DRS'),
      key: 'scaleDrs',
      minWidth: '22%',
      width: '22%',
      sortable: false,
    },
    {
      title: t('label.CPS'),
      key: 'scaleCps',
      minWidth: '22%',
      width: '22%',
      sortable: false,
    },
    {
      title: t('label.ADL-H'),
      key: 'scaleAdlh',
      minWidth: '22%',
      width: '22%',
      sortable: false,
    },
    {
      title: t('label.Pain-Score'),
      key: 'scalePs',
      minWidth: '22%',
      width: '22%',
      sortable: false,
    },
  ]

  switch (idGroup.value) {
    case '1':
      titleTable2.value = t('label.functionality-')
      break
    case '2':
      titleTable2.value = t('label.mental-aspect-')
      break
    case '3':
      titleTable2.value = t('label.social-aspects-')
      break
    case '4':
      titleTable2.value = t('label.clinical-aspects-')
      break
  }

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
}

const getTrigKbnByCapId = (capId: string): string | undefined => {
  const item = (localOneway.mo01282Oneway.items as unknown as SummarySelectInfo[]).find(
    (i) => i.capId === capId
  )
  return `${item?.trigKbn}`
}
function add() {
  listTable1.value = []
  localOneway.mo01282Oneway.items = []
  listTable2.value.items = []
  listTable3.value.items = []
  localOneway.mo01408Oneway.items = []
}
async function copy() {
  await init()
}

async function save(deleteKbn: string, updateKbn: string) {
  const param: summaryTableInfoUpdateInEntity = {
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: local.userId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    subKbn: '',
    updateKbn: updateKbn,
    historyUpdateKbn: updateKbn,
    deleteKbn: deleteKbn,
    sc1Id: local.planPeriodInfo.sc1Id,
    raiId: local.historyInfo.rirekiId,
    kijunbiYmd: local.kijunbiYmd,
    sakuseiId: '',
    assType: '',
    SummaryItemInfo: [],
    assignmentInfo: [],
  }
  const resData: kentouyousiInfoUpdateOutEntity = await ScreenRepository.update(
    'summaryTableInfoUpdate',
    param
  )
  return resData
}

/**
 *初期化処理
 */
async function init() {
  localOneway.mo01282Oneway.items = []
  listTable2.value.items = []
  listTable3.value.items = []
  localOneway.mo01408Oneway.items = []
  const inputParam: summaryTableInfoSelectServiceInEntity = {
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
    userid: systemCommonsStore.getUserId ?? '148',
    syubetsuId: '1',
    shisetuId: systemCommonsStore.getShisetuId ?? '1',
  }
  const response: summaryTableInfoSelectServiceOutEntity = await ScreenRepository.select(
    'summaryTableInfoSelect',
    inputParam
  )

  localOneway.mo01282Oneway.items = response.data
    .SummarySelectInfo as unknown as SummarySelectInfo[]

  listTable1.value = []
  const BMI =
    response.data.Summary1Info?.filter((x) => x.raiId === local.historyInfo.rirekiId) ?? []
  listTable1.value.push(BMI[0] as unknown as Summary1Info)

  const listTable2Json =
    response.data.SummaryItemInfo?.filter((x) => x.raiId === local.historyInfo.rirekiId) ?? []

  const itemCount = listTable2Json.length
  isEditState.value = new Array(itemCount).fill(false) as boolean[]
  isEdit.value = new Array(itemCount).fill(false) as boolean[]
  isEditIssues.value = new Array(itemCount).fill(false) as boolean[]
  isEditLong.value = new Array(itemCount).fill(false) as boolean[]
  isEditSort.value = new Array(itemCount).fill(false) as boolean[]
  isEditDetail.value = new Array(itemCount).fill(false) as boolean[]

  localOneway.mo01408Oneway.items =
    listTable2Json?.map((x) => ({
      title: x.capKnj ?? '',
      value: x.capId ?? '',
    })) ?? []
  listTable2.value.items =
    listTable2Json?.map((x) => ({
      capId: x.capId,
      raiId: x.raiId,
      capKnj: x.capKnj,
      problemKnj: {
        value: x.problemKnj ?? '',
      },
      careKnj: {
        value: x.careKnj ?? '',
      },
      youshikiFlg: x.youshikiFlg,
      trigKbn: getTrigKbnByCapId(x.capId),
      sort: x.sort,
    })) ?? []

  const listTable3Json =
    response.data.assignmentInfo?.filter((x) => x.raiId === local.historyInfo.rirekiId) ?? []

  listTable3.value.items =
    listTable3Json?.map((x) => ({
      raiId: x.raiId,
      capIdModel: {
        value: x.capId,
      },
      kadaiId: x.kadaiId,
      kadaiKnj: { value: x.kadaiKnj! },
      choukiKnj: { value: x.choukiKnj! },
      tankiKnj: { value: x.tankiKnj! },
      careKnj: { value: x.careKnj! },
      sort: x.sort,
    })) ?? []
}

function scrollToSection(sectionId: string) {
  const section = document.getElementById(sectionId)
  if (section) {
    section.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

/**
 * ボタン押下時の処理
 */
function onClickOr09999() {
  if (local.isCopyMode) return
  or09999Data.value.issueDataInfoList = []
  let i = 1
  for (const x of listTable3.value.items ?? []) {
    or09999Data.value.issueDataInfoList.push({
      displayOrder: i,
      raiId: x.raiId ?? '',
      capId: x.capIdModel?.value ?? '',
      kadaiId: x.kadaiId ?? '',
      issues: x.kadaiKnj?.value ?? '',
      longTermGoal: x.choukiKnj?.value ?? '',
      shortTermGoal: x.tankiKnj?.value ?? '',
      careDetails: x.careKnj?.value ?? '',
    })
    i++
  }
  setChildCpBinds(props.uniqueCpId, {
    [Or09999Const.CP_ID(0)]: {
      twoWayValue: or09999Data.value.issueDataInfoList,
    },
  })

  Or09999Logic.state.set({
    uniqueCpId: or09999.value.uniqueCpId,
    state: { isOpen: true },
  })
}

onMounted(async () => {
  await init()
  initCodes()
})

defineExpose({
  save,
})
</script>
<template>
  <c-v-sheet class="container">
    <c-v-row class="justify-center pb-0">
      <c-v-col
        cols="12"
        class="padding-0"
      >
        <c-v-row
          class="pt-2"
          style="width: 1174px; padding-left: 24px"
        >
          <c-v-col
            cols="12"
            class="pa-0 pt-3 pb-2"
          >
            <c-v-row class="pb-2">
              <c-v-col
                cols="9"
                class="d-flex align-center pa-0 pl-0"
              >
                <base-mo00611
                  :oneway-model-value="{
                    btnLabel: t('label.assessment-memo'),
                    minWidth: '132px',
                  }"
                  @click="onClickOr10962"
                >
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="t('label.assessment-memo')"
                  />
                </base-mo00611>
              </c-v-col>
              <c-v-col cols="3">
                <c-v-rows class="d-flex justify-end align-center">
                  <div
                    class="link-kanji-style"
                    @click="scrollToSection('issues_and_goal_2')"
                  >
                    {{ t('label.issues-and-goal-down') }}
                  </div>
                </c-v-rows>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <c-v-row style="padding-left: 24px">
          <c-v-col>
            <div
              class="bmi-table bmi-background"
              style="height: 36px; width: 394px"
            >
              {{ t('label.scale_BMI') }}
            </div>
            <c-v-row>
              <div class="bmi-table bmi-background bmi-left">
                {{ t('label.BMI') }}
              </div>
              <div class="bmi-table bmi-background-content bmi-right">
                {{ listTable1[0]?.scaleBmi?.toString() ?? '' }}
              </div>
            </c-v-row>
            <c-v-row>
              <div class="bmi-table bmi-background bmi-left">
                {{ t('label.DRS') }}
              </div>
              <div class="bmi-table bmi-background-content bmi-right">
                {{ listTable1[0]?.scaleDrs ?? '' }}
              </div>
            </c-v-row>
            <c-v-row>
              <div class="bmi-table bmi-background bmi-left">
                {{ t('label.CPS') }}
              </div>
              <div class="bmi-table bmi-background-content bmi-right">
                {{ listTable1[0]?.scaleCps ?? '' }}
              </div>
            </c-v-row>
            <c-v-row>
              <div class="bmi-table bmi-background bmi-left">
                {{ t('label.ADL-H') }}
              </div>
              <div class="bmi-table bmi-background-content bmi-right">
                {{ listTable1[0]?.scaleAdlh ?? '' }}
              </div>
            </c-v-row>
            <c-v-row>
              <div class="bmi-table bmi-background bmi-left">
                {{ t('label.Pain-Score') }}
              </div>
              <div class="bmi-table bmi-background-content bmi-right">
                {{ listTable1[0]?.scalePs ?? '' }}
              </div>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <hr class="mt-5 mb-2" />
        <c-v-row
          class="ma-custom-chek"
          style="padding-left: 24px"
        >
          <v-checkbox
            v-model="selected"
            class="pt-0"
            :label="Or00393Const.DEFAULT.ENABLE_CAP_STRING"
          ></v-checkbox>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <c-v-row
      class="justify-center pt-0"
      style="padding-left: 24px"
    >
      <c-v-col
        cols="12"
        class="pa-0 hidden-scroll h-100 d-flex flex-column"
      >
        <c-v-rows class="middleContent flex-1-1 h-100">
          <c-v-container class="pa-0 h-100">
            <!-- 機能面 -->
            <c-v-row
              no-gutters
              class="h-100"
            >
              <c-v-col class="d-flex pr-0 pt-0 h-100 padding-0">
                <c-v-data-table
                  v-bind="{ ...listTable2, items: selected ? [] : listTable2.items }"
                  fixed-header
                  class="table-header table-wrapper"
                  hide-default-footer
                  items-per-page="-1"
                  style="width: 1057px !important"
                >
                  <thead>
                    <tr>
                      <th
                        v-for="header in listTable2.headers"
                        :key="header.key"
                        :style="{ width: header.width }"
                      >
                        {{ header.title }}
                      </th>
                    </tr>
                    <tr>
                      <th
                        colspan="5"
                        style="font-weight: bold"
                      >
                        {{ t('label.functionality-') }}
                      </th>
                    </tr>
                  </thead>
                  <tbody
                    v-for="(item, index) in selected ? [] : listTable2.items"
                    :key="index"
                  >
                    <tr
                      v-if="Number(item.capId) < 7"
                      :class="{
                        'select-row': selectedItemIndex === index,
                      }"
                      @click="onSelectRow(index)"
                    >
                      <td class="pl-2">
                        {{ item.capKnj }}
                      </td>

                      <td class="pl-2">
                        {{ item.trigKbn }}
                      </td>
                      <td class="pl-2">
                        <div class="text-center">
                          {{ item.sort }}
                        </div>
                      </td>
                      <td class="pl-2">
                        <div
                          class="thead-with-iconbtn"
                          style="height: 100% !important"
                          @focusout.stop="
                            () => {
                              isfocusLeft = true
                            }
                          "
                        >
                          <base-mo00009
                            :oneway-model-value="localOneway.memoInputIconBtn"
                            @click="onClickOr51775(index)"
                          />
                          <base-mo01280
                            v-model="item.problemKnj"
                            class="overflow-auto"
                            :oneway-model-value="localOneway.mo01280Oneway"
                            :readonly="local.isCopyMode"
                          />
                        </div>
                      </td>
                      <td class="pl-2">
                        <div
                          class="thead-with-iconbtn"
                          style="height: 100% !important"
                          @focusout.stop="
                            () => {
                              isfocusLeft = false
                            }
                          "
                        >
                          <base-mo00009
                            :oneway-model-value="localOneway.memoInputIconBtn"
                            @click="onClickOr51775_2(index)"
                          />
                          <base-mo01280
                            v-model="item.careKnj"
                            class="overflow-auto"
                            :oneway-model-value="localOneway.mo01280Oneway"
                            :readonly="local.isCopyMode"
                          />
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </c-v-data-table>
              </c-v-col>
            </c-v-row>
            <!-- 精神面 -->
            <c-v-row
              no-gutters
              class="h-100"
            >
              <c-v-col class="d-flex pr-0 pt-0 h-100 padding-0">
                <c-v-data-table
                  v-bind="{ ...listTable2, items: selected ? [] : listTable2.items }"
                  fixed-header
                  class="table-header table-wrapper"
                  hide-default-footer
                  items-per-page="-1"
                  style="width: 1057px !important"
                >
                  <thead>
                    <tr>
                      <th
                        colspan="5"
                        style="font-weight: bold; border-top: none !important"
                      >
                        {{ t('label.mental-aspect-') }}
                      </th>
                    </tr>
                  </thead>
                  <tbody
                    v-for="(item, index) in selected ? [] : listTable2.items"
                    :key="index"
                  >
                    <tr
                      v-if="Number(item.capId) > 7 && Number(item.capId) < 13"
                      :class="{
                        'select-row': selectedItemIndex === index,
                      }"
                      @click="onSelectRow(index)"
                    >
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![0].width }"
                      >
                        {{ item.capKnj }}
                      </td>

                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![1].width }"
                      >
                        {{ item.trigKbn }}
                      </td>
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![2].width }"
                      >
                        <div class="text-center">
                          {{ item.sort }}
                        </div>
                      </td>
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![3].width }"
                      >
                        <div
                          class="thead-with-iconbtn"
                          style="height: 100% !important"
                          @focusout.stop="
                            () => {
                              isfocusLeft = true
                            }
                          "
                        >
                          <base-mo00009
                            :oneway-model-value="localOneway.memoInputIconBtn"
                            @click="onClickOr51775(index)"
                          />
                          <base-mo01280
                            v-model="item.problemKnj"
                            class="overflow-auto"
                            :oneway-model-value="localOneway.mo01280Oneway"
                            :readonly="local.isCopyMode"
                          />
                        </div>
                      </td>
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![4].width }"
                      >
                        <div
                          class="thead-with-iconbtn"
                          style="height: 100% !important"
                          @focusout.stop="
                            () => {
                              isfocusLeft = false
                            }
                          "
                        >
                          <base-mo00009
                            :oneway-model-value="localOneway.memoInputIconBtn"
                            @click="onClickOr51775_2(index)"
                          />
                          <base-mo01280
                            v-model="item.careKnj"
                            class="overflow-auto"
                            :oneway-model-value="localOneway.mo01280Oneway"
                            :readonly="local.isCopyMode"
                          />
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </c-v-data-table>
              </c-v-col>
            </c-v-row>
            <!-- 社会面 -->
            <c-v-row
              no-gutters
              class="h-100"
            >
              <c-v-col class="d-flex pr-0 pt-0 h-100 padding-0">
                <c-v-data-table
                  v-bind="{ ...listTable2, items: selected ? [] : listTable2.items }"
                  fixed-header
                  class="table-header table-wrapper"
                  hide-default-footer
                  items-per-page="-1"
                  style="width: 1057px !important"
                >
                  <thead>
                    <tr>
                      <th
                        colspan="5"
                        style="font-weight: bold; border-top: none !important"
                      >
                        {{ t('label.social-aspects-') }}
                      </th>
                    </tr>
                  </thead>
                  <tbody
                    v-for="(item, index) in selected ? [] : listTable2.items"
                    :key="index"
                  >
                    <tr
                      v-if="Number(item.capId) > 12 && Number(item.capId) < 16"
                      :class="{
                        'select-row': selectedItemIndex === index,
                      }"
                      @click="onSelectRow(index)"
                    >
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![0].width }"
                      >
                        {{ item.capKnj }}
                      </td>

                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![1].width }"
                      >
                        {{ item.trigKbn }}
                      </td>
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![2].width }"
                      >
                        <div class="text-center">
                          {{ item.sort }}
                        </div>
                      </td>
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![3].width }"
                      >
                        <div
                          class="thead-with-iconbtn"
                          style="height: 100% !important"
                          @focusout.stop="
                            () => {
                              isfocusLeft = true
                            }
                          "
                        >
                          <base-mo00009
                            :oneway-model-value="localOneway.memoInputIconBtn"
                            @click="onClickOr51775(index)"
                          />
                          <base-mo01280
                            v-model="item.problemKnj"
                            class="overflow-auto"
                            :oneway-model-value="localOneway.mo01280Oneway"
                            :readonly="local.isCopyMode"
                          />
                        </div>
                      </td>
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![4].width }"
                      >
                        <div
                          class="thead-with-iconbtn"
                          style="height: 100% !important"
                          @focusout.stop="
                            () => {
                              isfocusLeft = false
                            }
                          "
                        >
                          <base-mo00009
                            :oneway-model-value="localOneway.memoInputIconBtn"
                            @click="onClickOr51775_2(index)"
                          />
                          <base-mo01280
                            v-model="item.careKnj"
                            class="overflow-auto"
                            :oneway-model-value="localOneway.mo01280Oneway"
                            :readonly="local.isCopyMode"
                          />
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </c-v-data-table>
              </c-v-col>
            </c-v-row>
            <!-- 臨床面 -->
            <c-v-row
              no-gutters
              class="h-100"
            >
              <c-v-col class="d-flex pr-0 pt-0 h-100 padding-0">
                <c-v-data-table
                  v-bind="{ ...listTable2, items: selected ? [] : listTable2.items }"
                  fixed-header
                  class="table-header table-wrapper"
                  hide-default-footer
                  items-per-page="-1"
                  style="width: 1057px !important"
                >
                  <thead>
                    <tr>
                      <th
                        colspan="5"
                        style="font-weight: bold; border-top: none !important"
                      >
                        {{ t('label.clinical-aspects-') }}
                      </th>
                    </tr>
                  </thead>
                  <tbody
                    v-for="(item, index) in selected ? [] : listTable2.items"
                    :key="index"
                  >
                    <tr
                      v-if="Number(item.capId) > 15"
                      :class="{
                        'select-row': selectedItemIndex === index,
                      }"
                      @click="onSelectRow(index)"
                    >
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![0].width }"
                      >
                        {{ item.capKnj }}
                      </td>

                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![1].width }"
                      >
                        {{ item.trigKbn }}
                      </td>
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![2].width }"
                      >
                        <div class="text-center">
                          {{ item.sort }}
                        </div>
                      </td>
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![3].width }"
                      >
                        <div
                          class="thead-with-iconbtn"
                          style="height: 100% !important"
                          @focusout.stop="
                            () => {
                              isfocusLeft = true
                            }
                          "
                        >
                          <base-mo00009
                            :oneway-model-value="localOneway.memoInputIconBtn"
                            @click="onClickOr51775(index)"
                          />
                          <base-mo01280
                            v-model="item.problemKnj"
                            class="overflow-auto"
                            :oneway-model-value="localOneway.mo01280Oneway"
                            :readonly="local.isCopyMode"
                          />
                        </div>
                      </td>
                      <td
                        class="pl-2"
                        :style="{ width: listTable2.headers![4].width }"
                      >
                        <div
                          class="thead-with-iconbtn"
                          style="height: 100% !important"
                          @focusout.stop="
                            () => {
                              isfocusLeft = false
                            }
                          "
                        >
                          <base-mo00009
                            :oneway-model-value="localOneway.memoInputIconBtn"
                            @click="onClickOr51775_2(index)"
                          />
                          <base-mo01280
                            v-model="item.careKnj"
                            class="overflow-auto"
                            :oneway-model-value="localOneway.mo01280Oneway"
                            :readonly="local.isCopyMode"
                          />
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </c-v-data-table>
              </c-v-col>
            </c-v-row>
          </c-v-container>
        </c-v-rows>
      </c-v-col>
    </c-v-row>
    <hr class="mt-6 mb-6" />

    <!-- 課題と目標 -->
    <h2
      id="issues_and_goal_2"
      class="mb-6"
      style="padding-left: 24px"
    >
      {{ t('label.issues-and-goal') }}
    </h2>
    <c-v-row
      no-gutters
      class="pb-4"
      style="width: 1310px; padding-left: 24px"
    >
      <c-v-col cols="8">
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAdd"
          class="mx-1 ml-0 pl-0"
          @click="onAddItem"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="localOneway.btnAddItemTooltip.memoInputIconBtn"
          />
        </base-mo00611>
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAddRow"
          class="mx-1"
          @click="onAddRow"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="localOneway.btnAddRowTooltip.memoInputIconBtn"
          />
        </base-mo00611>
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayCopy"
          class="mx-1"
          @click="onCloneItem"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="localOneway.btnDuplicateRowTooltip.memoInputIconBtn"
          />
        </base-mo00611>
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayCopyIssues"
          class="mx-1"
          @click="onCloneItemCustom"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="localOneway.btnDuplicateRowIssuesTooltip.memoInputIconBtn"
          />
        </base-mo00611>
        <base-mo01265
          :oneway-model-value="localOneway.mo01265Oneway"
          class="mx-1"
          @click="onDelete"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="localOneway.btnDeleteRowTooltip.memoInputIconBtn"
          />
        </base-mo01265>
      </c-v-col>
      <c-v-col cols="4">
        <c-v-rows
          class="d-flex justify-end align-center"
          style="width: 425px"
        >
          <base-mo00611
            :oneway-model-value="{
              btnLabel: t('label.display-order'),
              minWidth: '65px',
            }"
            class="mr-6"
            @click="onClickOr09999"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="t('label.display-order')"
            />
          </base-mo00611>
          {{ selected ? 0 : listTable3.items?.length }}件
        </c-v-rows>
      </c-v-col>
    </c-v-row>
    <c-v-rows>
      <c-v-data-table
        v-bind="{ ...listTable3, items: selected ? [] : listTable3.items }"
        fixed-header
        class="table-header table-wrapper list-wrapper"
        hide-default-footer
        items-per-page="-1"
        style="width: 1300px; margin-left: 24px"
        @click:row="selectRow3"
        v-resizable-grid="{ columnWidths: columnMinWidth }"
      >
        <template #[`item.CAP`]="{ item }">
          <div class="pt-1 pb-1 cus_drop_cap">
            <base-mo01408
              v-model="item.capIdModel"
              :oneway-model-value="{
                ...localOneway.mo01408Oneway,
                readonly: local.isCopyMode,
              }"
            />
          </div>
        </template>
        <template #[`item.issues`]="{ item, index }">
          <div
            class="thead-with-iconbtn"
            style="height: 100% !important"
          >
            <base-mo00009
              :oneway-model-value="localOneway.memoInputIconBtn"
              @click="onClickOr51775_3(index)"
            />
            <base-mo01280
              v-model="item.kadaiKnj"
              class="overflow-auto"
              :oneway-model-value="localOneway.mo01280Oneway"
              :readonly="local.isCopyMode"
            />
          </div>
        </template>
        <template #[`item.long-term-goal`]="{ item, index }">
          <div
            class="thead-with-iconbtn"
            style="height: 100% !important"
          >
            <base-mo00009
              :oneway-model-value="localOneway.memoInputIconBtn"
              @click="onClickOr51775_4(index)"
            />
            <base-mo01280
              v-model="item.choukiKnj"
              class="overflow-auto"
              :oneway-model-value="localOneway.mo01280Oneway"
              :readonly="local.isCopyMode"
            />
          </div>
        </template>
        <template #[`item.short-term-goal`]="{ item, index }">
          <div
            class="thead-with-iconbtn"
            style="height: 100% !important"
          >
            <base-mo00009
              :oneway-model-value="localOneway.memoInputIconBtn"
              @click="onClickOr51775_5(index)"
            />
            <base-mo01280
              v-model="item.tankiKnj"
              class="overflow-auto"
              :oneway-model-value="localOneway.mo01280Oneway"
              :readonly="local.isCopyMode"
            />
          </div>
        </template>
        <template #[`item.care-details`]="{ item, index }">
          <div
            class="thead-with-iconbtn"
            style="height: 100% !important"
          >
            <base-mo00009
              :oneway-model-value="localOneway.memoInputIconBtn"
              @click="onClickOr51775_6(index)"
            />
            <base-mo01280
              v-model="item.careKnj"
              class="overflow-auto"
              :oneway-model-value="localOneway.mo01280Oneway"
              :readonly="local.isCopyMode"
            />
          </div>
        </template>
        <template
          v-if="selected"
          #no-data
        ></template>
      </c-v-data-table>
    </c-v-rows>
    <c-v-row no-gutters>
      <c-v-col>
        <!-- <g-base-or00051 /> -->
      </c-v-col>
    </c-v-row>
    <g-custom-or-10962
      v-if="showDialogOr10962"
      v-bind="or10962"
      v-model="returnTextOr10962"
    />
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="handleConfirm51775"
    />
    <g-custom-or-51775
      v-if="showDialogOr51775_2"
      v-bind="or51775_2"
      :oneway-model-value="localOneway.or51775Oneway_2"
      @confirm="handleConfirm51775_2"
    />
    <g-custom-or-51775
      v-if="showDialogOr51775_3"
      v-bind="or51775_3"
      :oneway-model-value="localOneway.or51775Oneway_3"
      @confirm="handleConfirm51775_3"
    />
    <g-custom-or-51775
      v-if="showDialogOr51775_4"
      v-bind="or51775_4"
      :oneway-model-value="localOneway.or51775Oneway_4"
      @confirm="handleConfirm51775_4"
    />
    <g-custom-or-51775
      v-if="showDialogOr51775_5"
      v-bind="or51775_5"
      :oneway-model-value="localOneway.or51775Oneway_5"
      @confirm="handleConfirm51775_5"
    />
    <g-custom-or-51775
      v-if="showDialogOr51775_6"
      v-bind="or51775_6"
      :oneway-model-value="localOneway.or51775Oneway_6"
      @confirm="handleConfirm51775_6"
    />
    <g-base-or21814
      v-if="showDialog"
      v-bind="or21814"
    />
    <g-custom-or-09999
      v-if="showDialogOr09999"
      v-bind="or09999"
      v-model="or09999Type"
      :oneway-model-value="or09999Data"
    />
  </c-v-sheet>
</template>
<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';

.container {
  background-color: transparent;
  padding: 0px !important;

  :has(.contentItem) {
    :deep(.item-label) {
      color: #0000ff;
      font-size: 12px !important;
      font-weight: normal !important;
    }
  }
}

.title-text-size {
  font-size: 18px;
}

.button-style {
  display: flex;
  justify-content: flex-start;
  padding-left: 8px;
}

.list-wrapper {
  background-color: transparent;
}

.divider {
  height: 16px;
}

.bordered {
  border: 1px rgb(var(--v-theme-black-200)) solid;

  :deep(.v-icon) {
    display: none;
  }
}

.bordered-left {
  border-left: none;
}

.bordered-red {
  border: 1px rgb(var(--v-theme-red-800)) solid;
}

.bg-white {
  background-color: rgb(var(--v-theme-white));
}

.v-row {
  margin: 0px;
}

.v-col {
  padding: 0px;
}

.p-endTextField {
  padding-right: 24px;
}

.text_color {
  background: transparent;
  color: rgb(var(--v-theme-key)) !important;
}

.table-header :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  padding: 0 4px;
}

:deep(.table-wrapper) {
  .v-table__wrapper th {
    background-color: #dbeefe !important;
    height: 32px;
    font-weight: 400;
    font-size: 13px;
  }
  td {
    height: 32px !important;
    &.input-table-cell {
      input {
        height: 32px !important;
      }
    }
    &.select-table-cell {
      select {
        height: 32px !important;
        &.full-width-field-select {
          padding: 0 8px !important;
        }
      }
      .button-cell {
        position: absolute;
        left: 0;
      }
      &.special {
        position: relative;
      }
    }
  }
}

.table-wrapper :deep(.v-table__wrapper) {
  overflow-y: auto;
  th {
    padding-left: 8px;
  }
  > table > tbody {
    background-color: rgb(var(--v-theme-surface));
  }
  td {
    height: 75px !important;
  }
}

.cus-full-size {
  :deep(.ma-1) {
    margin: 1px !important;
  }

  :deep(.ml-4) {
    margin-left: 12px !important;
    margin-right: 12px !important;
  }

  :deep(.v-col) {
    padding: 0 !important;
  }

  :deep(.item-label) {
    color: rgb(var(--v-theme-black-900)) !important;
    letter-spacing: normal !important;
  }

  font: inherit;
  background-color: transparent;
  margin: 0 !important;
  height: 32px !important;
  width: 100% !important;
}

:deep(.table-header td) {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-color: rgb(var(--v-theme-black-100));
  max-height: 32px;
  font-size: 14px;
}

:deep(.v-input__details) {
  display: none;
}

:deep(.row-zoom) {
  height: 104px;
}

:deep(.v-data-table-header__content) {
  white-space: pre-line;
}

:deep(.v-expansion-panel-text__wrapper) {
  padding: 8px 16px 16px 0px;
}

.cus-select {
  :deep(.v-col) {
    padding: 0px !important;
    margin: 0px !important;
  }

  :deep(.v-row) {
    padding: 0px !important;
    margin: 0px !important;
  }

  :deep(.v-sheet) {
    margin-right: 0px !important;
  }
}

:deep(.hidden-scroll) {
  .v-data-table__td {
    padding-left: 0px;

    padding-right: 0px;
  }
}

:deep(.justify-space-between) {
  padding-left: 8px !important;
}

:deep(.v-expansion-panel-title) {
  font-weight: bold;
  font-size: 20px;
}

:deep(.v-expansion-panel-title) {
  font-weight: bold;
  font-size: 14px !important;
}
// :deep(.full-width-field) {
//   height: 32px !important;
// }
.thead-with-iconbtn {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.table-wrapper .v-table__wrapper td:has(textarea) .table-cell:not([disabled]):not([readonly]) {
  outline: none !important;
}
:deep .table-wrapper .v-table__wrapper tr:not(.row-selected) td:not(:has(input, textarea, select)) {
  background-color: #dbeefe !important;
}

.link-kanji-style {
  text-align: right;
  font-size: 14px;
  background: transparent;
  max-width: auto;
  cursor: pointer; // マウスカーソルはポインター
  &:hover {
    text-decoration: underline;
  }
}

:deep(.v-icon--size-small) {
  color: rgb(var(--v-theme-light-blue-400)) !important;
}

.bmi-table {
  padding-left: 8px;
  border: 1px rgb(var(--v-theme-black-200)) solid;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.bmi-background {
  background-color: #dbeefe;
}

.bmi-background-content {
  background-color: white;
}

.bmi-left {
  height: 36px;
  width: 180px;
  border-top: none !important;
  border-right: none !important;
  letter-spacing: -0.5px !important;
}

.bmi-right {
  height: 36px;
  width: 214px;
  border-top: none !important;
}

.ma-custom-chek {
  transform: translateX(-11px);
}

.cus_drop_cap {
  :deep(.v-field__input) {
    padding-inline: 4px !important;
  }
  :deep(.v-field__outline) {
    display: none;
  }
}
</style>
