/**
 * Or27514:パターン（グループ）情報保存のエンティティ
 * GUI00995_パターン（グループ）情報保存
 *
 * <AUTHOR>
 */
import type { InWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * パターン（グループ）情報取得入力エンティティ
 */
export interface PatternTitleInfoUpdateInEntity extends InWebEntity {
  /**
   * タイトルリスト(日課表)
   */
  nikkaList: {
    /**
     * 日課表ID
     */
    day1Id: string
    /**
     * タイトル
     */
    day1TitleKnj: string
    /**
     * グループCD
     */
    day1GroupCD: string
    /**
     * 表示順
     */
    day1Seq: string
    /** 更新回数(日課表) */
    day1ModifiedCnt: string
    /** 更新区分 */
    updateKbn?: string
  }[]
  /**
   * タイトルリスト(週間表)
   */
  syukanList: {
    /**
     * 週間表ID
     */
    week1Id: string
    /**
     * タイトル
     */
    week1TitleKnj: string
    /**
     * グループCD
     */
    week1GroupCD: string
    /**
     * 有効期間ID
     */
    termid: string
    /**
     * 有効期間
     */
    termKnj: string
    /**
     * 表示順
     */
    week1Seq: string
    /**
     * 2:H21/4 1:旧様式
     */
    kaiteiFlg: string
    /**
     * 改訂
     */
    kaiteiKnj: string
    /** 更新回数(週間表) */
    week1ModifiedCnt: string
    /** 更新区分 */
    updateKbn?: string
  }[]
  /**
   * タイトルリスト(月間・年間表)
   */
  nenkanList: {
    /**
     * 月間・年間計画ID
     */
    nenkan1Id: string
    /**
     * タイトル
     */
    nenkan1TitleKnj: string
    /**
     * グループCD
     */
    nenkan1GroupCD: string
    /**
     * 表示順
     */
    nenkan1Seq: string
    /** 更新回数(月間・年間表) */
    nenkan1ModifiedCnt: string
    /** 更新区分 */
    updateKbn?: string
  }[]
}
