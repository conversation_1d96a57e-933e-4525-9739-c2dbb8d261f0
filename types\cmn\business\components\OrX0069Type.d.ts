import type { WeekTableList } from '~/components/custom-components/organisms/OrX0068/OrX0068.type'
import type { WeeklyTableInput } from '~/components/custom-components/organisms/Or32382/Or32382.type'

/**
 * OrX0069:有機体: 計画書と［計画書複写］明細一覧
 * OneWayBind領域に保持するデータ構造
 */
export interface OrX0069OnewayType {
  /**
   * テーブルアイテム
   */
  tableItem: WeekTableList[]
  /** 複写画面表示フラグ */
  cpyFlg?: boolean
  /** 期間管理フラグ */
  periodManageFlag: string
  /** 計画期間ID */
  planTargetPeriodId: string
  /** 削除フラグ */
  delFlg?: boolean
  /** 週単位以外サービス */
  wIgaiKnj: string
}
/**
 * twoWayBind領域に保持するデータ構造
 */
export interface OrX0069Type {
  /** 週単位以外サービス */
  wIgaiKnj: string
  /** テーブルアイテム */
  weekData: WeeklyTableInput[]
  /** タブ */
  tabindex: string
  /** 週間表ID */
  week1Id?: string
  /** 計画対象期間ID */
  planTargetPeriodId?: string
  /** 改訂フラグ */
  kaiteiFlg?: string
  /** 当該年月 */
  tougaiYm?: string
  /** 取込有効期間ID */
  torikomitermid?: string
}
