<script setup lang="ts">
/**
 * Or57082:有機体:印刷設定
 * GUI00815_印刷設定
 *
 * @description
 * （アセスメント）印刷設定モーダル
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or57082Const } from './Or57082.constants'
import type { Or57082StateType } from './Or57082.type'
import { dateUtils, useNuxtApp, useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Mo00020OnewayType, Mo00020Type } from '@/types/business/components/Mo00020Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or57082Param } from '~/components/custom-components/organisms/Or57082/Or57082.type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { DIALOG_BTN } from '~/constants/classification-constants'
import { SYS_RYAKU } from '~/constants/constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  IAssessmentHomePrintSettingsHistorySelectInEntity,
  IAssessmentHomePrintSettingsHistorySelectOutEntity,
  IAssessmentHomePrintSettingsInitialUpdateInEntity,
  IAssessmentHomePrintSettingsInitialUpdateOutEntity,
  IAssessmentHomePrintSettinguserSwitchingSelectInEntity,
  IAssessmentHomePrintSettinguserSwitchingSelectOutEntity,
  PeriodHistoryEntity,
  UserEntity,
} from '~/repositories/cmn/entities/AssessmentHomePrintSettingsEntity'
import type {
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
  SysIniInfoEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import type {
  ChoPrtEntity,
  IGdlAssReportInEntity,
  PrintHistoryEntity,
  PrintOptionEntity,
  PrintSetEntity,
} from '~/repositories/cmn/entities/GdlAssReportEntity'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00038OnewayType, Mo00038Type } from '~/types/business/components/Mo00038Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type {
  OrX0128Headers,
  OrX0128Items,
  OrX0128OnewayType,
} from '~/types/cmn/business/components/OrX0128Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { CustomClass } from '~/types/CustomClassType'
import { reportOutputType, useReportUtils } from '~/utils/useReportUtils'
// import { hasPrintAuth } from '~/utils/useCmnAuthz'
import { useCmnCom } from '@/utils/useCmnCom'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()
const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })

const localOneway = reactive({
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * タイトル
   */
  mo01338OneWayTitle: {
    value: t('label.print-settings-title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * タイトル表示
   */
  mo00045OneWay: {
    showItemLabel: false,
    width: '296',
    disabled: true,
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo00045OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 指定日
   */
  mo00020OneWay: {
    showItemLabel: false,
    showSelectArrow: false,
    /** テキストフィールドの幅 */
    width: '130px',
  } as Mo00020OnewayType,
  /**
   * 記入用シートを印刷する
   */
  mo00018OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: false,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 特記事項を別紙に印刷する
   */
  mo00018OneWaySpecialNoteAppendix: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-with-special-note-appendix'),
    isVerticalLabel: true,
    showItemLabel: false,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 特記事項印刷線の高さを最小
   */
  mo00018OneWaySpecialNotePrintLine: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-with-special-note-print-line'),
    isVerticalLabel: true,
    showItemLabel: false,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /** 特記事項印刷線の高さを最小行に設定するステップナビ */
  mo00038OnewaySpecialNotePrintLine: {
    mo00045Oneway: {
      itemLabel: '',
      maxlength: '2',
      width: '60px',
      isVerticalLabel: false,
      showItemLabel: false,
      disabled: Or57082Const.DEFAULT.ENABLED,
    } as Mo00045OnewayType,
    min: Or57082Const.DEFAULT.MIN_LINE,
    max: 99,
    showSpinBtn: true,
  } as Mo00038OnewayType,
  //** 行に設定するラベル */
  mo01338OnewayLineSet: {
    value: t('label.line-set'),
    valueFontWeight: 'bold',
    customClass: { outerClass: 'left-content' },
  } as Mo01338OnewayType,
  /**
   * まとめ印刷線の高さを最小
   */
  mo00018OneWaySummaryPrintLine: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-with-summary-print-line'),
    isVerticalLabel: true,
    showItemLabel: false,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /** まとめ印刷線の高さを最小行に設定するステップナビ */
  mo00038OnewaySummaryPrintLine: {
    mo00045Oneway: {
      itemLabel: '',
      maxlength: '2',
      width: '60px',
      isVerticalLabel: false,
      showItemLabel: false,
      disabled: Or57082Const.DEFAULT.ENABLED,
    } as Mo00045OnewayType,
    min: Or57082Const.DEFAULT.MIN_LINE,
    max: 99,
    showSpinBtn: true,
  } as Mo00038OnewayType,
  /**
   * (状況別)空白の項目も印刷する
   */
  mo00018OneWayBlankItems: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-with-blank-items'),
    isVerticalLabel: true,
    showItemLabel: false,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 特記事項を別紙に印刷する補足
   */
  mo01338OneWaySpecialNoteAppendix: {
    value: Or57082Const.DEFAULT.SUPPLEMENT.SPECIAL_NOTE_APPENDIX,
    customClass: {
      outerClass: 'pl-10 pr-2 pb-2',
    },
  } as Mo01338OnewayType,
  /**
   * 特記事項印刷線の高最小行補足
   */
  mo01338OneWaySpecialNotePrintLine: {
    value: Or57082Const.DEFAULT.SUPPLEMENT.SPECIAL_NOTE_PRINTLINE,
    customClass: {
      outerClass: 'pl-10 pr-2 pb-2',
    },
  } as Mo01338OnewayType,
  /**
   * まとめ印刷線の高最小行補足
   */
  mo01338OneWaySummaryPrintLine: {
    value: Or57082Const.DEFAULT.SUPPLEMENT.SUMMARY_PRINTLINE,
    customClass: {
      outerClass: 'pl-10 pr-2 pb-2',
    },
  } as Mo01338OnewayType,
  /**
   * 改訂版選択ラベル
   */
  mo01338OneWayRevision: {
    value: t('label.revision'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 改訂版選択
   */
  mo00039OneWayRevisionSelect: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 改訂版選択補足
   */
  mo01338OneWayRevisionSelect: {
    value: Or57082Const.DEFAULT.SUPPLEMENT.REVISION_SELECT,
    customClass: {
      outerClass: '',
      labelClass: '',
      itemClass: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    itemLabel: t('label.printer-user-select'),
    showItemLabel: true,
    inline: true,
    customClass: {
      outerClass: '',
      labelClass: 'ma-1 pl-1',
    } as CustomClass,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    itemLabel: t('label.printer-history-select'),
    showItemLabel: true,
    inline: true,
    customClass: {
      outerClass: '',
      labelClass: 'ma-1 pl-1',
    } as CustomClass,
  } as Mo00039OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020KijunbiOneWay: {
    itemLabel: t('label.base-date'),
    showItemLabel: true,
    isVerticalLabel: true,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    customClass: {
      outerClass: '',
    } as CustomClass,
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: Or57082Const.DEFAULT.STR.EMPTY,
    customClass: {
      outerClass: '',
      labelClass: 'ma-1',
    } as CustomClass,
  } as OrX0145OnewayType,
  /**
   * 印刷オプション補足ラベル
   */
  mo01338OneWayPrinterOptionLabel: {
    value: Or57082Const.DEFAULT.SUPPLEMENT.PRINTER_OPTION,
    valueFontWeight: 'bold',
    customClass: {
      outerClass: '',
      labelClass: '',
      itemClass: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    // 共通処理の印刷権限チェックを行う
    // disabled: !(await hasPrintAuth()),
    disabled: false,
  } as Mo00609OnewayType,
})

/**
 * タイトルテキストフィールドmodelValue
 */
const mo00045TitleType = ref<Mo00045Type>({
  value: '',
})

/**
 * 親画面の情報
 */
const local = {
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: Or57082Const.DEFAULT.KOJINHOGO_USED_FLG,
  /**
   * 個人情報番号
   */
  sectionAddNo: Or57082Const.DEFAULT.SECTION_ADD_NO,
  /**
   * 親画面.セクション番号
   */
  sectionNo: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所ID
   */
  svJigyoId: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.施設ID
   */
  shisetuId: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.担当者ID
   */
  tantoId: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.種別ID
   */
  syubetsuId: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.セクション名
   */
  sectionName: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.利用者ID
   */
  userId: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所名
   */
  svJigyoKnj: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.処理年月日
   */
  processYmd: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.アセスメントID
   */
  assessmentId: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 選択行のセクション番号
   */
  currentSectionNo: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
  /**
   * 親画面.初期選択状態の担当者カウンタ値
   */
  selectedUserCounter: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 選択された帳票のプロファイル
   */
  profile: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者ID
   */
  selectUserId: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historyNoSelect: false,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userNoSelect: false,
  /**
   * 改定フラグ
   */
  kaiteiFlg: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 帳票番号
   */
  prtNo: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 帳票ID
   */
  reportId: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 履歴選択の明細
   */
  orX0128DetList: [] as OrX0128Items[],
}

const localData: IAssessmentHomePrintSettingsInitialUpdateOutEntity = {
  data: {},
} as IAssessmentHomePrintSettingsInitialUpdateOutEntity
/**************************************************
 * 変数定義
 **************************************************/
// 初期読み込みのローディング
const isLoading = ref(false)

// ボタンの連続クリックを制限する
const pdfDownloadBtnPress = ref(false)

// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1212px',
  height: '788px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or57082',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or57082ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or57082_content',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or57082Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧 活性/非活性
 */
const mo01334OnewayDisable = ref<boolean>(Or57082Const.DEFAULT.ENABLED)

/**
 * まとめ印刷線の高最小行チェックボックス 表示/非表示
 */
const summaryPrintLineDisplay = ref<boolean>(Or57082Const.DEFAULT.DISPLAY)

/**
 * (状況別)空白の項目も印刷するチェックボックス 表示/非表示
 */
const blankItemsDisplay = ref<boolean>(Or57082Const.DEFAULT.DISPLAY)

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.ledger'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 'auto',
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>('')

/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 記入用シートを印刷する
 */
const mo00018Type = ref<Mo00018Type>({
  modelValue: false,
})

/**
 * 特記事項を別紙に印刷する
 */
const mo00018SpecialNoteAppendix = ref<Mo00018Type>({
  modelValue: false,
})

/**
 * 特記事項印刷線の高さを最小
 */
const mo00018SpecialNotePrintLine = ref<Mo00018Type>({
  modelValue: false,
})

/**
 * まとめ印刷線の高さを最小
 */
const mo00018SummaryPrintLine = ref<Mo00018Type>({
  modelValue: false,
})

/**
 * (状況別)空白の項目も印刷する
 */
const mo00018BlankItems = ref<Mo00018Type>({
  modelValue: false,
})

/**
 * 特記事項印刷線の高さを最小行に設定するステップナビ
 */
const mo00038SpecialNotePrintLine = ref<Mo00038Type>({
  mo00045: { value: '' },
})

/**
 * まとめ印刷線の高さを最小行に設定するステップナビ
 */
const mo00038SummaryPrintLine = ref<Mo00038Type>({
  mo00045: { value: '' },
})

/**
 * 改訂版選択項目リスト
 */
const revisionSelectItems = ref<Mo00039Items[]>([])

/**
 * 改訂範囲
 */
const revisionRange = ref<string>(Or57082Const.DEFAULT.KITEI_H21)

/**
 * 改訂版選択
 */
const mo00039RevisionSelect = ref<string>('2')

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('1')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('1')

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

/**
 * 基準日
 */
const mo00020TypeKijunbi = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)
/**
 * 基準日フラゲ
 */
const kijunbiFlag = ref<boolean>(false)
/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(false)
/**
 * 初期化フラゲ
 */
const initFlag = ref<boolean>(false)

/**
 * 利用者列幅
 */
const userCols = ref('248px')

const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or57082Const.DEFAULT.KIKAN_FLG_1,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:341px; height: 497px',
  headers: [
    {
      title: t('label.create-date'),
      key: 'asJisshiDateYmd',
      minWidth: '120px',
      width: '120px',
      sortable: false,
    },
    {
      title: t('label.author'),
      key: 'shokuinKnj',
      minWidth: '220px',
      width: '220px',
      sortable: false,
    },
    {
      title: t('label.revision'),
      key: 'kaiteiKnj',
      minWidth: '80px',
      width: '80px',
      sortable: false,
    },
  ] as OrX0128Headers[],
  items: [],
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  /**
   * 選択モート
   */
  selectMode: OrX0130Const.DEFAULT.TANI,
  /**
   * テーブルのスタイル
   */
  tableStyle: 'width:202px',
  /**
   * 高
   */
  height: 460,
  /**
   * 指定行選択
   */
  userId: Or57082Const.DEFAULT.STR.EMPTY,
  /**
   * 複数選択時、50音の選択数を表示するか
   */
  showKanaSelectionCount: true,
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
})

/**
 * 担当ケアマネ選択アイコン
 */
const tantoIconBtn = ref<boolean>(false)

/**
 * 担当ケアマネ表示ラベル
 */
const tantoLabel = ref<boolean>(false)

/**
 * 改訂版一覧
 */
const revisionList = ref<CodeType[]>([])
/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or57082Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**
 * 期間管理フラグ
 */
const kikanFlag = ref<string>(Or57082Const.DEFAULT.STR.EMPTY)
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or57082StateType>({
  cpId: Or57082Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or57082Const.DEFAULT.IS_OPEN
    },
    param: (value) => {
      if (value) {
        local.sectionNo = value.sectionNo
        local.prtNo = value.prtNo
        local.svJigyoId = value.svJigyoId
        local.shisetuId = value.shisetuId
        local.tantoId = value.tantoId
        local.syubetsuId = value.syubetsuId
        local.sectionName = value.sectionName
        local.userId = value.userId
        local.svJigyoKnj = value.svJigyoKnj
        local.assessmentId = value.assessmentId
        local.processYmd = value.processYmd
        local.focusSettingInitial = value.focusSettingInitial
        local.selectedUserCounter = value.selectedUserCounter
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面ID
const screenId = 'GUI00793'
// ルーティング
const routing = 'GUI00793/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

onMounted(async () => {
  isLoading.value = true
  // 汎用コード取得API実行
  await initCodes()
  // 画面ボタン活性非活性設定
  btnItemSetting()
  // 初期情報取得
  await init()
  // 初期選択データ設定
  selectRowDataSetting()
  isLoading.value = false
})

// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  initPdfDownloadBtnPress()
  // Or57082のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 初期値に戻す
 */
const initPdfDownloadBtnPress = () => {
  pdfDownloadBtnPress.value = false
}

/**
 * 初期選択データ設定
 */
const selectRowDataSetting = () => {
  // フォーカス設定用イニシャル設定
  orX0130Oneway.focusSettingInitial = local.focusSettingInitial
  // 初期選択状態の担当者カウンタ値設定
  localOneway.orX0145Oneway.selectedUserCounter = local.selectedUserCounter
  // 親画面.利用者情報リスト件数>0
  if (mo01334Oneway.value.items.length > 0) {
    // 利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.userId) {
      // 利用者IDを対するレコードを選択状態にする
      orX0130Oneway.userId = local.userId
    }
    // 親画面.利用者IDが存在しない場合
    else {
      orX0130Oneway.userId = Or57082Const.DEFAULT.STR.EMPTY
    }
  }
  // 利用者一覧明細に親画面.利用者IDが存在しない場合
  else {
    orX0130Oneway.userId = Or57082Const.DEFAULT.STR.EMPTY
  }
}

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 印刷設定改訂版
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINT_SETTING_REVISION },
    // 印刷設定改訂フラグ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINT_SETTING_REVISION_FLAG },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 印刷設定改訂フラグ
  const printSettingRevsionFlagCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRINT_SETTING_REVISION_FLAG
  )
  if (printSettingRevsionFlagCodeTypes?.length > 0) {
    revisionList.value = printSettingRevsionFlagCodeTypes
  }

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWay.items = list
  }

  // 改訂版選択
  const printSettingRevsionCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRINT_SETTING_REVISION
  )
  if (printSettingRevsionCodeTypes?.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of printSettingRevsionCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    revisionSelectItems.value = list
  }

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039OneWayUserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039OneWayHistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期プロジェクト設定
 */
const initSetting = () => {
  // 共通情報.処理年月日が""の場合
  if (Or57082Const.DEFAULT.STR.EMPTY === systemCommonsStore.getProcessDate) {
    // 担当ケアマネ選択
    tantoIconBtn.value = false
    // 担当ケアマネ表示
    tantoLabel.value = false
  } else {
    // 担当ケアマネ選択
    tantoIconBtn.value = true
    // 担当ケアマネ表示
    tantoLabel.value = true
  }
  // 共通情報.ｶﾞｲﾄﾞﾗｲﾝまとめが「2」状況別i以外の場合
  if (
    cmnRouteCom.getInitialSettingMaster()?.gdlMatomeFlg !==
    Or57082Const.DEFAULT.GUIDELINE_SUMMARY_SITUATION
  ) {
    // 非表示
    summaryPrintLineDisplay.value = Or57082Const.DEFAULT.HIDDEN
    blankItemsDisplay.value = Or57082Const.DEFAULT.HIDDEN
  } else {
    // 表示
    summaryPrintLineDisplay.value = Or57082Const.DEFAULT.DISPLAY
    blankItemsDisplay.value = Or57082Const.DEFAULT.DISPLAY
  }
}

/**
 * 初期情報取得
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IAssessmentHomePrintSettingsInitialUpdateInEntity = {
    svJigyoId: local.svJigyoId,
    userId: local.userId,
    sysRyaku: SYS_RYAKU,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: local.shisetuId,
    menu2Knj: Or57082Const.DEFAULT.MENU_KNJ,
    menu3Knj: Or57082Const.DEFAULT.MENU_KNJ,
    sectionName: local.sectionName,
    shokuId: systemCommonsStore.getCurrentUser.chkShokuId,
    index: Or57082Const.DEFAULT.INDEX,
    kojinhogoUsedFlg: Or57082Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or57082Const.DEFAULT.SECTION_ADD_NO,
    gsysCd: systemCommonsStore.getSystemCode,
    revisionRange: mo00039RevisionSelect.value,
    historySelectFlag: mo00039OneWayHistorySelectType.value,
  } as IAssessmentHomePrintSettingsInitialUpdateInEntity

  systemCommonsStore.setSystemSnackBarDisabled(true)
  const resp: IAssessmentHomePrintSettingsInitialUpdateOutEntity = await ScreenRepository.update(
    'assessmentHomePrintSettingsInitialUpdate',
    inputData
  )
  if (resp?.data) {
    localData.data = { ...resp?.data }

    // 期間管理フラグ
    kikanFlag.value = localData.data.kikanFlg

    const prtList: Mo01334Items[] = []
    // 出力帳票印刷情報リストの件数が1より大きい場合、活性表示
    if (resp.data.prtList?.length > Or57082Const.DEFAULT.NUMBER.ZERO) {
      mo01334OnewayDisable.value = Or57082Const.DEFAULT.ENABLED
      for (const item of resp.data.prtList) {
        if (item) {
          prtList.push({
            id: item.prtNo,
            mo01337OnewayLedgerName: {
              value: item.prtTitle,
              unit: Or57082Const.DEFAULT.STR.EMPTY,
            } as Mo01337OnewayType,
            prnDate: item.prnDate === Or57082Const.DEFAULT.STR.TRUE,
            selectable: true,
            profile: item.profile,
            index: item.index,
            prtNo: item.prtNo,
          } as Mo01334Items)
        }
      }
      if (localOneway.mo00039OneWay.items && localOneway.mo00039OneWay.items.length > 0) {
        mo00039Type.value = String(localOneway.mo00039OneWay.items[0].value)
      }
    } else {
      // 以外の場合、非活性
      mo01334OnewayDisable.value = Or57082Const.DEFAULT.DISABLED
    }
    mo01334Oneway.value.items = prtList
    // 出力帳票名一覧初期値設定
    if (prtList.length > 0) {
      const prtItem = prtList.find((item) => item.id === local.prtNo)
      mo01334Type.value.value = prtItem?.id ?? prtList[0].id
    }
    initFlag.value = true
    // アセスメント履歴リスト
    getHistoryData(resp.data.periodHistoryList)
    // 画面.利用者一覧明細に親画面.アセスメントIDが存在する場合
    if (local.assessmentId) {
      // 親画面.アセスメントIDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        orX0128OnewayModel.items.findIndex((item) => item.raiId === local.assessmentId) + 1
      ).toString()
    }
  }

  initSetting()
}

/**
 * アセスメント履歴一覧データ
 *
 * @param periodHistoryList - アセスメント履歴リスト
 */
const getHistoryData = (periodHistoryList: PeriodHistoryEntity[]) => {
  if (periodHistoryList?.length > 0) {
    local.kaiteiFlg = periodHistoryList[0].ninteiFormF
  }
  // 取得したアセスメント履歴情報.アセスメント履歴リストを履歴一覧明細に設定する。
  if (Or57082Const.DEFAULT.KIKAN_FLG_1 === kikanFlag.value) {
    const tempList: string[] = [] as string[]
    let list: OrX0128Items[] = []
    for (const item of periodHistoryList) {
      if (item) {
        const planPeriod =
          t('label.plan-period') +
          t('label.colon-mark') +
          item.startYmd +
          t('label.wavy') +
          item.endYmd
        const kaiteiKnj = revisionList.value.find(
          (revision) => revision.value === item.ninteiFormF
        )?.label
        if (!tempList.includes(planPeriod)) {
          const historyList: OrX0128Items[] = []
          for (const data of periodHistoryList) {
            const dataPlanPeriod =
              t('label.plan-period') +
              t('label.colon-mark') +
              data.startYmd +
              t('label.wavy') +
              data.endYmd
            if (planPeriod === dataPlanPeriod) {
              historyList.push({
                sel: data.sel,
                gdlId: data.gdlId,
                userid: data.userid,
                asJisshiDateYmd: data.asJisshiDateYmd,
                shokuId: data.shokuId,
                ninteiFormF: data.ninteiFormF,
                shokuinKnj: data.shokuinKnj,
                kaiteiKnj: kaiteiKnj,
                id: data.gdlId,
                sc1Id: data.sc1Id,
                startYmd: data.startYmd,
                endYmd: data.endYmd,
              } as OrX0128Items)
            }
          }
          if (historyList.length > 0) {
            list.push({
              sc1Id: item.sc1Id,
              startYmd: item.startYmd,
              endYmd: item.endYmd,
              isPeriodManagementMergedRow: true,
              planPeriod:
                t('label.plan-period') +
                t('label.colon-mark') +
                item.startYmd +
                t('label.wavy') +
                item.endYmd,
              id: Or57082Const.DEFAULT.STR.EMPTY,
            } as OrX0128Items)
            list = list.concat(historyList)
            tempList.push(planPeriod)
          }
        }
      }
    }
    list.forEach((item, index) => {
      item.id = String(++index)
    })
    orX0128OnewayModel.items = list
  } else {
    const list: OrX0128Items[] = []
    for (const data of periodHistoryList) {
      if (data) {
        const kaiteiKnj = revisionList.value.find(
          (revision) => revision.value === data.ninteiFormF
        )?.label
        list.push({
          sel: data.sel,
          gdlId: data.gdlId,
          userid: data.userid,
          asJisshiDateYmd: data.asJisshiDateYmd,
          shokuId: data.shokuId,
          ninteiFormF: data.ninteiFormF,
          shokuinKnj: data.shokuinKnj,
          kaiteiKnj: kaiteiKnj,
          id: data.gdlId,
          sc1Id: data.sc1Id,
          startYmd: data.startYmd,
          endYmd: data.endYmd,
        } as OrX0128Items)
      }
    }
    orX0128OnewayModel.items = list
  }
}

/**
 * 画面印刷設定内容を保存
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    sysRyaku: SYS_RYAKU,
    sectionName: local.sectionName,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: systemCommonsStore.getCurrentUser.chkShokuId,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    index: local.index,
    sysIniInfo: local.sysIniInfo,
    prtList: localData.data.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity

  const resp: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return resp
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  await save()
  setState({
    isOpen: false,
    param: {} as Or57082Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 利用者選択方法が「単一」
  if (Or57082Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 履歴選択が「単一」
    if (Or57082Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      // 記入用シートを印刷するチェック外す
      if (!mo00018Type.value.modelValue) {
        // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
        if (local.userNoSelect) {
          // メッセージを表示
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11393'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'blank',
          })
          // はい
          if (dialogResult === DIALOG_BTN.YES) {
            // 処理終了
            return
          }
        }

        // 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
        if (local.historyNoSelect) {
          // メッセージを表示
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11455'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'blank',
          })
          // はい
          if (dialogResult === DIALOG_BTN.YES) {
            initPdfDownloadBtnPress()
            // 処理終了
            return
          }
        }
      }

      // 履歴情報リストにデータを選択する場合
      if (!local.historyNoSelect) {
        // 印刷ダイアログ画面を開かずに、画面.印刷対象履歴リスト「利用者情報+履歴情報+出力帳票対象」を直接に利用して、帳票側の処理を呼び出す
        await reportOutputPdf()
        initPdfDownloadBtnPress()
        return
      }
    }
    // 履歴選択方法が「複数」
    else if (Or57082Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value) {
      // 履歴情報リストにデータを選択する場合
      if (!local.historyNoSelect) {
        // 印刷設定情報リストを作成
        createReportOutputData(local.currentSectionNo)
        initPdfDownloadBtnPress()
      }
      // 履歴一覧が0件選択場合（※履歴リストが0件、複数件を含む）
      else {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11455'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult === DIALOG_BTN.YES) {
          initPdfDownloadBtnPress()
          // 処理終了
          return
        }
      }
    }
  }
  // 利用者選択方法が「複数」の場合
  else if (Or57082Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
    // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
    if (local.userNoSelect) {
      // メッセージを表示
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11393'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (dialogResult === DIALOG_BTN.YES) {
        initPdfDownloadBtnPress()
        // 処理終了
        return
      }
    }
    // 利用者情報リストにデータを選択する場合
    else {
      // 印刷設定情報リストを作成
      await PrintSettingsSubjectSelect()
      initPdfDownloadBtnPress()
    }
  }

  // AC019-1と同じ => 画面の印刷設定情報を保存する
  await save()

  // 選択された帳票のプロファイルが””の場合
  if (local.profile === Or57082Const.DEFAULT.STR.EMPTY) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      initPdfDownloadBtnPress()
      // 処理終了
      return
    }
  }

  // 「AC020-4-2 また AC020-4-3」で取得した印刷設定情報リスト＞0件の場合
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷ダイアログ画面を開
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    const reportData: IGdlAssReportInEntity = {
      // 共通情報.ｶﾞｲﾄﾞﾗｲﾝまとめ
      gdlMatomeFlg: cmnRouteCom.getInitialSettingMaster()?.gdlMatomeFlg ?? '',
      // 親画面.事業所名
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode ?? '',
      printSet: {
        // 画面.指定日印刷区分
        shiTeiKubun: mo00039Type.value,
        // 画面.指定日
        shiTeiDate: mo00020Type.value.value,
      } as PrintSetEntity,
      printOption: {
        // 画面.記入用シートを印刷する
        emptyFlg: String(mo00018Type.value.modelValue),
        // 画面.特記事項を別紙に印刷するチェックボックス
        specialnoteAppendix: String(mo00018SpecialNoteAppendix.value.modelValue),
        // 画面.特記事項印刷線の高さチェックボックス
        specialnoteLine: String(mo00018SpecialNotePrintLine.value.modelValue),
        // 画面.特記事項印刷線の高キストフィールド
        specialnoteLineHeight: mo00038SpecialNotePrintLine.value.mo00045.value,
        // 画面.まとめ印刷線の高最小行チェックボックス
        summaryLine: String(mo00018SummaryPrintLine.value.modelValue),
        // 画面.まとめ印刷線の高最小行キストフィールド
        summaryLineHeight: mo00038SummaryPrintLine.value.mo00045.value,
        // 画面.(状況別)空白の項目も印刷するチェックボックス
        blankItem: String(mo00018BlankItems.value.modelValue),
      } as PrintOptionEntity,
      printHistoryList: [] as PrintHistoryEntity[],
    } as IGdlAssReportInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        // 全て帳票
        if (Or57082Const.DEFAULT.STR.TWELVE === local.currentSectionNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getCurrentUser.chkShokuId,
            sysRyaku: SYS_RYAKU,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
        //  単一帳票
        else if (local.currentSectionNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getCurrentUser.chkShokuId,
            sysRyaku: SYS_RYAKU,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or57082Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or57082Const.DEFAULT.STR.EMPTY,
      sc1Id:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].sc1Id as string)
          : Or57082Const.DEFAULT.STR.EMPTY,
      startYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].startYmd as string)
          : Or57082Const.DEFAULT.STR.EMPTY,
      endYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].endYmd as string)
          : Or57082Const.DEFAULT.STR.EMPTY,
      gdlId:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].gdlId as string)
          : Or57082Const.DEFAULT.STR.EMPTY,
      asJisshiDateYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].asJisshiDateYmd as string)
          : Or57082Const.DEFAULT.STR.EMPTY,
      shokuId:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].shokuId as string)
          : Or57082Const.DEFAULT.STR.EMPTY,
      ninteiFormF:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].ninteiFormF as string)
          : Or57082Const.DEFAULT.STR.EMPTY,
      result: Or57082Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintHistoryEntity)
    // 帳票出力
    await reportOutput(local.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', local.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 印刷設定情報リストを作成(利用者選択が「複数」、利用者情報リストにデータを選択する場合)
 */
const PrintSettingsSubjectSelect = async () => {
  // 印刷設定情報リストを作成する
  const inputData = {
    // 事業者ID:親画面.事業者ID
    svJigyoId: local.svJigyoId,
    // システムコード：共通情報.システムコード
    gsysCd: systemCommonsStore.getSystemCode ?? '',
    // システム略称：共通情報.システム略称
    sysRyaku: SYS_RYAKU,
    // 法人ID：共通情報.法人ID
    houjinId: systemCommonsStore.getHoujinId ?? '',
    // 施設ID：親画面.施設ID
    shisetuId: local.shisetuId,
    // 職員ID：共通情報.職員ID
    shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
    // 基準日：画面.基準日
    kijunbiYmd: mo00020TypeKijunbi.value.value ?? Or57082Const.DEFAULT.STR.EMPTY,
    // インデックス：0
    index: Or57082Const.DEFAULT.STR.ZERO,
    // 個人情報使用フラグ：0 ※0：不使用、1：使用
    kojinhogoUsedFlg: Or57082Const.DEFAULT.KOJINHOGO_USED_FLG,
    // 個人情報番号：0 ※0：主に日誌以外、1：主に日誌系
    sectionAddNo: Or57082Const.DEFAULT.SECTION_ADD_NO,
    // 利用者リスト：画面.利用者リストの選択対象
    userList: local.userList,
  } as IAssessmentHomePrintSettingsHistorySelectInEntity
  const resp: IAssessmentHomePrintSettingsHistorySelectOutEntity = await ScreenRepository.select(
    'assessmentHomePrintSettingsHistorySelect',
    inputData
  )

  const list: OrX0117History[] = []
  if (resp.data) {
    for (const data of resp.data.printSubjectHistoryList) {
      if (data) {
        // 利用者複数の場合
        if (Or57082Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 印刷設定情報リストを作成
          const reportData: IGdlAssReportInEntity = {
            svJigyoKnj: local.svJigyoKnj,
            syscd: systemCommonsStore.getSystemCode ?? '',
            printSet: {
              shiTeiKubun: mo00039Type.value,
              shiTeiDate: mo00020Type.value.value
                ? mo00020Type.value.value.split('/').join('-')
                : '',
            } as PrintSetEntity,
            printOption: {
              // 画面.記入用シートを印刷する
              emptyFlg: String(mo00018Type.value.modelValue),
              // 画面.特記事項を別紙に印刷するチェックボックス
              specialnoteAppendix: String(mo00018SpecialNoteAppendix.value.modelValue),
              // 画面.特記事項印刷線の高さチェックボックス
              specialnoteLine: String(mo00018SpecialNotePrintLine.value.modelValue),
              // 画面.特記事項印刷線の高キストフィールド
              specialnoteLineHeight: mo00038SpecialNotePrintLine.value.mo00045.value,
              // 画面.まとめ印刷線の高最小行チェックボックス
              summaryLine: String(mo00018SummaryPrintLine.value.modelValue),
              // 画面.まとめ印刷線の高最小行キストフィールド
              summaryLineHeight: mo00038SummaryPrintLine.value.mo00045.value,
              // 画面.(状況別)空白の項目も印刷するチェックボックス
              blankItem: String(mo00018BlankItems.value.modelValue),
            } as PrintOptionEntity,
            printHistoryList: [] as PrintHistoryEntity[],
          } as IGdlAssReportInEntity

          const choPrtList: ChoPrtEntity[] = []
          for (const item of localData.data.prtList) {
            if (item) {
              // 全て帳票
              if (Or57082Const.DEFAULT.STR.TWELVE === local.currentSectionNo) {
                choPrtList.push({
                  shokuId: systemCommonsStore.getCurrentUser.chkShokuId,
                  sysRyaku: SYS_RYAKU,
                  section: local.sectionName,
                  prtNo: item.prtNo,
                  choPro: item.profile,
                  sectionName: item.defPrtTitle,
                  dwobject: item.dwobject,
                  prtOrient: item.prtOrient,
                  prtSize: item.prtSize,
                  listTitle: item.listTitle,
                  prtTitle: item.prtTitle,
                  mTop: item.mtop,
                  mBottom: item.mbottom,
                  mLeft: item.mleft,
                  mRight: item.mright,
                  ruler: item.ruler,
                  prndate: item.prnDate,
                  prnshoku: item.prnshoku,
                  serialFlg: item.serialFlg,
                  modFlg: item.modFlg,
                  secFlg: item.secFlg,
                  param01: item.param01,
                  param02: item.param02,
                  param03: item.param03,
                  param04: item.param04,
                  param05: item.param05,
                  param06: item.param06,
                  param07: item.param07,
                  param08: item.param08,
                  param09: item.param09,
                  param10: item.param10,
                  serialHeight: item.serialHeight,
                  serialPagelen: item.serialPagelen,
                  hsjId: systemCommonsStore.getHoujinId,
                  param11: item.param11,
                  param12: item.param12,
                  param13: item.param13,
                  param14: item.param14,
                  param15: item.param15,
                  param16: item.param16,
                  param17: item.param17,
                  param18: item.param18,
                  param19: item.param19,
                  param20: item.param20,
                  param21: item.param21,
                  param22: item.param22,
                  param23: item.param23,
                  param24: item.param24,
                  param25: item.param25,
                  param26: item.param26,
                  param27: item.param28,
                  param28: item.param28,
                  param29: item.param29,
                  param30: item.param30,
                  param31: item.param31,
                  param32: item.param32,
                  param33: item.param33,
                  param34: item.param34,
                  param35: item.param35,
                  param36: item.param36,
                  param37: item.param37,
                  param38: item.param38,
                  param39: item.param39,
                  param40: item.param40,
                  param41: item.param41,
                  param42: item.param42,
                  param43: item.param43,
                  param44: item.param44,
                  param45: item.param45,
                  param46: item.param46,
                  param47: item.param47,
                  param48: item.param48,
                  param49: item.param49,
                  param50: item.param50,
                  houjinId: systemCommonsStore.getHoujinId,
                  shisetuId: systemCommonsStore.getShisetuId,
                  svJigyoId: systemCommonsStore.getSvJigyoId,
                  zoomRate: item.zoomRate,
                  modifiedCnt: item.modifiedCnt,
                } as ChoPrtEntity)
              }
              //  単一帳票
              else if (local.currentSectionNo === item.prtNo) {
                choPrtList.push({
                  shokuId: systemCommonsStore.getCurrentUser.chkShokuId,
                  sysRyaku: SYS_RYAKU,
                  section: local.sectionName,
                  prtNo: item.prtNo,
                  choPro: item.profile,
                  sectionName: item.defPrtTitle,
                  dwobject: item.dwobject,
                  prtOrient: item.prtOrient,
                  prtSize: item.prtSize,
                  listTitle: item.listTitle,
                  prtTitle: item.prtTitle,
                  mTop: item.mtop,
                  mBottom: item.mbottom,
                  mLeft: item.mleft,
                  mRight: item.mright,
                  ruler: item.ruler,
                  prndate: item.prnDate,
                  prnshoku: item.prnshoku,
                  serialFlg: item.serialFlg,
                  modFlg: item.modFlg,
                  secFlg: item.secFlg,
                  param01: item.param01,
                  param02: item.param02,
                  param03: item.param03,
                  param04: item.param04,
                  param05: item.param05,
                  param06: item.param06,
                  param07: item.param07,
                  param08: item.param08,
                  param09: item.param09,
                  param10: item.param10,
                  serialHeight: item.serialHeight,
                  serialPagelen: item.serialPagelen,
                  hsjId: systemCommonsStore.getHoujinId,
                  param11: item.param11,
                  param12: item.param12,
                  param13: item.param13,
                  param14: item.param14,
                  param15: item.param15,
                  param16: item.param16,
                  param17: item.param17,
                  param18: item.param18,
                  param19: item.param19,
                  param20: item.param20,
                  param21: item.param21,
                  param22: item.param22,
                  param23: item.param23,
                  param24: item.param24,
                  param25: item.param25,
                  param26: item.param26,
                  param27: item.param28,
                  param28: item.param28,
                  param29: item.param29,
                  param30: item.param30,
                  param31: item.param31,
                  param32: item.param32,
                  param33: item.param33,
                  param34: item.param34,
                  param35: item.param35,
                  param36: item.param36,
                  param37: item.param37,
                  param38: item.param38,
                  param39: item.param39,
                  param40: item.param40,
                  param41: item.param41,
                  param42: item.param42,
                  param43: item.param43,
                  param44: item.param44,
                  param45: item.param45,
                  param46: item.param46,
                  param47: item.param47,
                  param48: item.param48,
                  param49: item.param49,
                  param50: item.param50,
                  houjinId: systemCommonsStore.getHoujinId,
                  shisetuId: systemCommonsStore.getShisetuId,
                  svJigyoId: systemCommonsStore.getSvJigyoId,
                  zoomRate: item.zoomRate,
                  modifiedCnt: item.modifiedCnt,
                } as ChoPrtEntity)
              }
            }
          }
          // 印刷設定情報リストパラメータを作成
          reportData.printHistoryList.push({
            userId: data.userid,
            userName: data.userName,
            sc1Id: data.sc1Id,
            startYmd: data.startYmd,
            endYmd: data.endYmd,
            gdlId: data.gdlId,
            asJisshiDateYmd: data.asJisshiDateYmd,
            shokuId: data.shokuId,
            ninteiFormF: data.ninteiFormF,
            result: data.result,
            choPrtList: choPrtList,
          } as PrintHistoryEntity)
          list.push({
            reportId: local.reportId,
            outputType: reportOutputType.DOWNLOAD,
            reportData: reportData,
            userName: data.userName,
            historyDate: data.startYmd,
            result: data.result,
          } as OrX0117History)
        }
      }
    }
  }
  orX0117Oneway.historyList = list
}

/**
 * 切替前の印刷設定を保存する
 *
 * @param selectId - 出力帳票ID
 */
const setBeforChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          item.prnDate = mo00039Type.value
          // パラメータ11
          item.param11 = mo00018BlankItems.value.modelValue
            ? Or57082Const.DEFAULT.STR.ONE
            : Or57082Const.DEFAULT.STR.ZERO
          // パラメータ12
          item.param12 = mo00018SpecialNotePrintLine.value.modelValue
            ? Or57082Const.DEFAULT.STR.ONE
            : Or57082Const.DEFAULT.STR.ZERO
          // パラメータ13
          item.param13 =
            mo00038SpecialNotePrintLine.value.mo00045.value !== ''
              ? mo00038SpecialNotePrintLine.value.mo00045.value
              : item.param13
          // パラメータ14
          item.param14 = mo00018SummaryPrintLine.value.modelValue
            ? Or57082Const.DEFAULT.STR.ONE
            : Or57082Const.DEFAULT.STR.ZERO
          // パラメータ15
          item.param15 =
            mo00038SummaryPrintLine.value.mo00045.value !== ''
              ? mo00038SummaryPrintLine.value.mo00045.value
              : item.param13
          // パラメータ16
          item.param16 = mo00018SpecialNoteAppendix.value.modelValue
            ? Or57082Const.DEFAULT.STR.ONE
            : Or57082Const.DEFAULT.STR.ZERO
          break
        }
      }
    }
  }
}

/**
 * 切替後の印刷設定を画面に設定する
 *
 * @param selectId - 出力帳票ID
 */
const setAfterChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          mo00039Type.value = item.prnDate
          // (状況別)空白の項目も印刷するチェックボックス
          mo00018BlankItems.value.modelValue = item.param11 === Or57082Const.DEFAULT.STR.ONE
          // 特記事項印刷線の高さチェックボックス
          mo00018SpecialNotePrintLine.value.modelValue =
            item.param12 === Or57082Const.DEFAULT.STR.ONE
          // 特記事項最小行入力
          mo00038SpecialNotePrintLine.value.mo00045.value = item.param13
          // まとめ印刷線の高最小行チェックボックス
          mo00018SummaryPrintLine.value.modelValue = item.param14 === Or57082Const.DEFAULT.STR.ONE
          // まとめ最小行入力
          mo00038SummaryPrintLine.value.mo00045.value = item.param15
          // 特記事項を別紙に印刷するチェックボックス
          mo00018SpecialNoteAppendix.value.modelValue =
            item.param16 === Or57082Const.DEFAULT.STR.ONE
          break
        }
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const outputLedgerName = async (selectId: string) => {
  if (!selectId) {
    selectId = Or57082Const.DEFAULT.STR.ONE
  }
  let label = Or57082Const.DEFAULT.STR.EMPTY
  for (const item of mo01334Oneway.value.items) {
    if (item) {
      if (selectId === item.id && item.mo01337OnewayLedgerName) {
        const data = item.mo01337OnewayLedgerName as Mo01337OnewayType
        label = data.value

        // プロファイル
        local.profile = item.profile as string

        // 出力帳票名一覧に選択行番号
        local.index = item.index as string

        // 帳票番号
        local.prtNo = item.prtNo as string

        // 帳票ID
        setReportId(item.id)
        break
      }
    }
  }
  mo00045TitleType.value.value = label

  local.currentSectionNo = selectId

  // 帳票イニシャライズデータを取得する
  await getSectionInitializeData()
}

/**
 * 帳票ID設定
 *
 * @param prtNo - 帳票番号
 */
const setReportId = (prtNo: string) => {
  switch (local.sectionNo) {
    case Or57082Const.DEFAULT.SECTION_NO_H21:
      switch (prtNo) {
        case Or57082Const.DEFAULT.STR.ONE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.LIFE1
          break
        case Or57082Const.DEFAULT.STR.TWO:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.LIFE23
          break
        case Or57082Const.DEFAULT.STR.THREE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.LIFE34
          break
        case Or57082Const.DEFAULT.STR.FOUR:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.LIFE5
          break
        case Or57082Const.DEFAULT.STR.FIVE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.CARE1
          break
        case Or57082Const.DEFAULT.STR.SIX:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.CARE2
          break
        case Or57082Const.DEFAULT.STR.SEVEN:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.CARE34
          break
        case Or57082Const.DEFAULT.STR.EIGHT:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.CARE5
          break
        case Or57082Const.DEFAULT.STR.NINE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.CARE6
          break
        case Or57082Const.DEFAULT.STR.TEN:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.ALL_SUMMARY
          break
        case Or57082Const.DEFAULT.STR.ELEVEN:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.One_Day
          break
        case Or57082Const.DEFAULT.STR.TWELVE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H21_KITEI.ALL
          break
        default:
          local.reportId = Or57082Const.DEFAULT.STR.EMPTY
          break
      }
      break
    case Or57082Const.DEFAULT.SECTION_NO_H30:
      switch (prtNo) {
        case Or57082Const.DEFAULT.STR.ONE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.LIFE1
          break
        case Or57082Const.DEFAULT.STR.TWO:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.LIFE23
          break
        case Or57082Const.DEFAULT.STR.THREE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.LIFE34
          break
        case Or57082Const.DEFAULT.STR.FOUR:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.LIFE5
          break
        case Or57082Const.DEFAULT.STR.FIVE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.CARE1
          break
        case Or57082Const.DEFAULT.STR.SIX:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.CARE2
          break
        case Or57082Const.DEFAULT.STR.SEVEN:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.CARE34
          break
        case Or57082Const.DEFAULT.STR.EIGHT:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.CARE5
          break
        case Or57082Const.DEFAULT.STR.NINE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.CARE6
          break
        case Or57082Const.DEFAULT.STR.TEN:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.ALL_SUMMARY
          break
        case Or57082Const.DEFAULT.STR.ELEVEN:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.One_Day
          break
        case Or57082Const.DEFAULT.STR.TWELVE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H30_KITEI.ALL
          break
        default:
          local.reportId = Or57082Const.DEFAULT.STR.EMPTY
          break
      }
      break
    case Or57082Const.DEFAULT.SECTION_NO_R3:
      switch (prtNo) {
        case Or57082Const.DEFAULT.STR.ONE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.LIFE1
          break
        case Or57082Const.DEFAULT.STR.TWO:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.LIFE23
          break
        case Or57082Const.DEFAULT.STR.THREE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.LIFE34
          break
        case Or57082Const.DEFAULT.STR.FOUR:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.LIFE5
          break
        case Or57082Const.DEFAULT.STR.FIVE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.CARE1
          break
        case Or57082Const.DEFAULT.STR.SIX:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.CARE2
          break
        case Or57082Const.DEFAULT.STR.SEVEN:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.CARE34
          break
        case Or57082Const.DEFAULT.STR.EIGHT:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.CARE5
          break
        case Or57082Const.DEFAULT.STR.NINE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.CARE6
          break
        case Or57082Const.DEFAULT.STR.TEN:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.ALL_SUMMARY
          break
        case Or57082Const.DEFAULT.STR.ELEVEN:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.One_Day
          break
        case Or57082Const.DEFAULT.STR.TWELVE:
          local.reportId = Or57082Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R3_KITEI.ALL
          break
        default:
          local.reportId = Or57082Const.DEFAULT.STR.EMPTY
          break
      }
      break
    default:
      local.reportId = Or57082Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 印刷設定情報リストを作成
 *
 * @param prtNo - 帳票番号
 */
const createReportOutputData = (prtNo: string) => {
  const list: OrX0117History[] = []
  for (const orX0128DetData of local.orX0128DetList) {
    const reportData: IGdlAssReportInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode,
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        // 画面.記入用シートを印刷する
        emptyFlg: String(mo00018Type.value.modelValue),
        // 画面.特記事項を別紙に印刷するチェックボックス
        specialnoteAppendix: String(mo00018SpecialNoteAppendix.value.modelValue),
        // 画面.特記事項印刷線の高さチェックボックス
        specialnoteLine: String(mo00018SpecialNotePrintLine.value.modelValue),
        // 画面.特記事項印刷線の高キストフィールド
        specialnoteLineHeight: mo00038SpecialNotePrintLine.value.mo00045.value,
        // 画面.まとめ印刷線の高最小行チェックボックス
        summaryLine: String(mo00018SummaryPrintLine.value.modelValue),
        // 画面.まとめ印刷線の高最小行キストフィールド
        summaryLineHeight: mo00038SummaryPrintLine.value.mo00045.value,
        // 画面.(状況別)空白の項目も印刷するチェックボックス
        blankItem: String(mo00018BlankItems.value.modelValue),
      } as PrintOptionEntity,
      printHistoryList: [] as PrintHistoryEntity[],
    } as IGdlAssReportInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        // 全て帳票
        if (Or57082Const.DEFAULT.STR.TWELVE === prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getCurrentUser.chkShokuId,
            sysRyaku: SYS_RYAKU,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
        //  単一帳票
        else if (prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getCurrentUser.chkShokuId,
            sysRyaku: SYS_RYAKU,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or57082Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or57082Const.DEFAULT.STR.EMPTY,
      sc1Id: orX0128DetData.sc1Id as string,
      startYmd: orX0128DetData.startYmd as string,
      endYmd: orX0128DetData.endYmd as string,
      gdlId: orX0128DetData.gdlId as string,
      asJisshiDateYmd: orX0128DetData.asJisshiDateYmd as string,
      shokuId: orX0128DetData.shokuId as string,
      ninteiFormF: orX0128DetData.ninteiFormF as string,
      result: Or57082Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintHistoryEntity)
    list.push({
      reportId: local.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or57082Const.DEFAULT.STR.EMPTY,
      historyDate: orX0128DetData.asJisshiDateYmd as string,
      result: Or57082Const.DEFAULT.STR.EMPTY,
    } as OrX0117History)
  }
  orX0117Oneway.historyList = list
}

/**
 * 帳票イニシャライズデータを取得する
 */
const getSectionInitializeData = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    profile: local.profile,
    gsyscd: systemCommonsStore.getSystemCode ?? '',
    shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
    kojinhogoUsedFlg: Or57082Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or57082Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity

  systemCommonsStore.setSystemSnackBarDisabled(true)
  const resp: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (resp.data) {
    local.sysIniInfo = resp.data.sysIniInfo
  }
}

/**
 * 印刷設定履歴リスト取得
 */
const getPrintSettingsHistoryList = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IAssessmentHomePrintSettinguserSwitchingSelectInEntity = {
    userId: local.selectUserId,
    sysRyaku: SYS_RYAKU,
    gsysCd: systemCommonsStore.getSystemCode,
    houjinId: systemCommonsStore.getHoujinId,
    sectionName: local.sectionName,
    shisetuId: local.shisetuId,
    kikanFlg: kikanFlag.value,
    shokuId: systemCommonsStore.getCurrentUser.chkShokuId,
    svJigyoId: local.svJigyoId,
    index: Or57082Const.DEFAULT.INDEX,
    kojinhogoUsedFlg: Or57082Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or57082Const.DEFAULT.SECTION_ADD_NO,
    revisionRange: mo00039RevisionSelect.value,
    historySelectFlag: mo00039OneWayHistorySelectType.value,
  } as IAssessmentHomePrintSettinguserSwitchingSelectInEntity
  const resp: IAssessmentHomePrintSettinguserSwitchingSelectOutEntity =
    await ScreenRepository.select('assessmentHomePrintSettinguserSwitchingSelect', inputData)
  if (resp.data) {
    // アセスメント履歴一覧データ
    getHistoryData(resp.data.periodHistoryList)
  }
}

/**
 * 画面ボタン活性非活性設定
 */
const btnItemSetting = () => {
  // 利用者選択方法が「単一」の場合
  if (Or57082Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 基準日を非表示にする
    // 履歴選択を活性表示にする
    kijunbiFlag.value = false

    // 履歴選択方法が「単一」の場合
    if (Or57082Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWay.disabled = false
    }
    // 以外の場合
    else {
      // 記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWay.disabled = true
    }
  }
  // 以外の場合
  else {
    // 基準日を活性表示にする
    // 履歴選択を非表示にする
    kijunbiFlag.value = true

    localOneway.mo00018OneWay.disabled = true
  }

  // 履歴選択方法が「単一」の場合
  if (Or57082Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
    // 利用者選択方法が「単一」の場合
    if (Or57082Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWay.disabled = false
    }
    // 以外の場合
    else {
      localOneway.mo00018OneWay.disabled = true
    }
  }
  // 以外の場合
  else {
    // 記入用シートを印刷するを非活性表示にする
    localOneway.mo00018OneWay.disabled = true

    // 記入用シートを印刷するをチェックオフにする
    mo00018Type.value.modelValue = false
  }

  // 履歴一覧セクション
  if (Or57082Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    userCols.value = '248px'
    mo01334TypeHistoryFlag.value = true
  } else {
    userCols.value = '100%'
    mo01334TypeHistoryFlag.value = false
  }

  // 特記事項を別紙に印刷する非活性/活性設定
  const prtNoList = ['5', '6', '7', '8', '9', '12']
  // アセスメント履歴リスト.改定フラグ＞3の場合、かつ
  // 印刷設定情報リスト.帳票番号が5/6/7/8/9/12の場合、かつ
  // 記入用シートを印刷するチェックボックスがﾁｪｯｸOFFの場合
  if (
    !Number.isNaN(local.kaiteiFlg) &&
    parseInt(local.kaiteiFlg) > 3 &&
    prtNoList.includes(local.prtNo) &&
    !mo00018Type.value.modelValue
  ) {
    // 活性表示
    localOneway.mo00018OneWaySpecialNoteAppendix.disabled = Or57082Const.DEFAULT.ENABLED
  } else {
    // 以外の場合、非活性表示
    localOneway.mo00018OneWaySpecialNoteAppendix.disabled = Or57082Const.DEFAULT.DISABLED
  }

  // 特記事項印刷線の高さ非活性/活性設定
  // 特記事項を別紙に印刷するチェックボックスが活性表示の場合、かつ
  // 特記事項を別紙に印刷するチェックボックスがﾁｪｯｸONの場合
  if (
    localOneway.mo00018OneWaySpecialNoteAppendix.disabled === Or57082Const.DEFAULT.ENABLED &&
    mo00018SpecialNoteAppendix.value.modelValue === true
  ) {
    // 活性表示
    localOneway.mo00018OneWaySpecialNotePrintLine.disabled = Or57082Const.DEFAULT.ENABLED
  } else {
    // 以外の場合、非活性表示
    localOneway.mo00018OneWaySpecialNotePrintLine.disabled = Or57082Const.DEFAULT.DISABLED
  }

  // 特記事項印刷線の高さを最小行に設定するステップナビ非活性/活性設定
  if (localOneway.mo00038OnewaySpecialNotePrintLine.mo00045Oneway) {
    // 特記事項印刷線の高さチェックボックスが活性表示の場合、かつ
    // 特記事項印刷線の高さチェックボックスがﾁｪｯｸONの場合
    if (
      localOneway.mo00018OneWaySpecialNotePrintLine.disabled === Or57082Const.DEFAULT.ENABLED &&
      mo00018SpecialNotePrintLine.value.modelValue === true
    ) {
      // 活性表示
      localOneway.mo00038OnewaySpecialNotePrintLine.mo00045Oneway.disabled =
        Or57082Const.DEFAULT.ENABLED
    } else {
      // 以外の場合、非活性表示
      localOneway.mo00038OnewaySpecialNotePrintLine.mo00045Oneway.disabled =
        Or57082Const.DEFAULT.DISABLED
    }
  }

  // まとめ印刷線の高さ非活性/活性設定
  // (状況別)空白の項目も印刷する非活性/活性設定
  // アセスメント履歴.改定フラグが＞３ 且つ
  // 印刷設定情報リスト.帳票番号が10/12の場合
  if (
    !Number.isNaN(local.kaiteiFlg) &&
    parseInt(local.kaiteiFlg) > 3 &&
    (local.prtNo === Or57082Const.DEFAULT.STR.TEN ||
      local.prtNo === Or57082Const.DEFAULT.STR.TWELVE)
  ) {
    // 活性表示
    localOneway.mo00018OneWaySummaryPrintLine.disabled = Or57082Const.DEFAULT.ENABLED
    localOneway.mo00018OneWayBlankItems.disabled = Or57082Const.DEFAULT.ENABLED
  } else {
    // 以外の場合、非活性表示
    localOneway.mo00018OneWaySummaryPrintLine.disabled = Or57082Const.DEFAULT.DISABLED
    localOneway.mo00018OneWayBlankItems.disabled = Or57082Const.DEFAULT.DISABLED
  }

  // まとめ印刷線の高さを最小行に設定するステップナビ非活性/活性設定
  if (localOneway.mo00038OnewaySummaryPrintLine.mo00045Oneway) {
    // まとめ印刷線の高最小行チェックボックスが活性表示の場合、かつ
    // まとめ印刷線の高最小行チェックボックスがﾁｪｯｸONの場合
    if (
      localOneway.mo00018OneWaySummaryPrintLine.disabled === Or57082Const.DEFAULT.ENABLED &&
      mo00018SummaryPrintLine.value.modelValue === true
    ) {
      // 活性表示
      localOneway.mo00038OnewaySummaryPrintLine.mo00045Oneway.disabled =
        Or57082Const.DEFAULT.ENABLED
    } else {
      // 以外の場合、非活性表示
      localOneway.mo00038OnewaySummaryPrintLine.mo00045Oneway.disabled =
        Or57082Const.DEFAULT.DISABLED
    }
  }

  // 担当ケアマネ選択アイコンボタン非活性/活性設定
  const kkjTantoFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? Or57082Const.DEFAULT.STR.EMPTY
  // 共通情報.担当ケアマネ設定フラグ > 0、且つ、共通情報.担当者IDが0以外の場合
  if (
    systemCommonsStore.getManagerId !== Or57082Const.DEFAULT.STR.ZERO &&
    parseInt(kkjTantoFlg) > Or57082Const.DEFAULT.NUMBER.ZERO
  ) {
    // 非活性
    localOneway.orX0145Oneway.disabled = true
  }
  // その他場合
  else {
    // 活性
    localOneway.orX0145Oneway.disabled = false
  }
}

/**
 * 担当ケアマネプルダウン
 *
 * @param result - 戻り値
 */
const orx0145UpdateModelValue = (result: OrX0145Type) => {
  if (result) {
    if (result.value) {
      if (!Array.isArray(result.value) && 'chkShokuId' in result.value) {
        // TODO API疎通時に確認
        orX0130Oneway.tantouCareManager = result.value.chkShokuId
      }
    }

    // 画面.利用者選択が単一の場合
    if (Or57082Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
      // 画面.利用者一覧明細の1件目レコードを選択状態にする
      orX0130Oneway.userId = Or57082Const.DEFAULT.STR.EMPTY
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = async (
  uniqueCpId: string,
  state: Or21814OnewayType
): Promise<typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO | typeof DIALOG_BTN.CANCEL> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = DIALOG_BTN.YES as
          | typeof DIALOG_BTN.YES
          | typeof DIALOG_BTN.NO
          | typeof DIALOG_BTN.CANCEL

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }
        if (event?.thirdBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = async (
  uniqueCpId: string,
  state: Or21813StateType
): Promise<typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO | typeof DIALOG_BTN.CANCEL> => {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(uniqueCpId)

        let result = DIALOG_BTN.CANCEL as
          | typeof DIALOG_BTN.YES
          | typeof DIALOG_BTN.NO
          | typeof DIALOG_BTN.CANCEL

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }
        if (event?.thirdBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  async (newValue) => {
    setAfterChangePrintData(newValue)
    await outputLedgerName(newValue)
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 日付印刷区分が2の場合
    if (Or57082Const.DEFAULT.STR.TWO === mo00039Type.value) {
      // 指定日を活性表示にする。
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする。
      mo00020Flag.value = false
    }
  }
)

/**
 * 「日付印刷区分」ラジオボタン押下
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    setBeforChangePrintData(local.prtNo)
    if (Or57082Const.DEFAULT.STR.TWO === newValue) {
      // 指定日を活性表示にする
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする
      mo00020Flag.value = false
    }
  }
)

/**
 * 「利用者選択方法」ラジオボタン選択
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 利用者選択方法が「単一」の場合
    if (Or57082Const.DEFAULT.TANI === newValue) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 202px'
      orX0128OnewayModel.initSelectId = Or57082Const.DEFAULT.STR.EMPTY

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }

      // 利用者一覧明細に親画面.利用者IDが存在する場合
      if (local.userId) {
        // 利用者IDを対するレコードを選択状態にする
        orX0130Oneway.userId = local.userId
      }
      // 利用者一覧明細に親画面.利用者IDが存在しない場合
      else {
        orX0130Oneway.userId = Or57082Const.DEFAULT.STR.EMPTY
      }
    } else {
      // 復元
      orX0117Oneway.type = Or57082Const.DEFAULT.STR.ONE
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }
    }
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    if (OrX0128Logic.event.get(orX0128.value.uniqueCpId)) {
      if (
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList &&
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList.length > 0
      ) {
        local.historyNoSelect = false
      } else {
        local.historyNoSelect = true
      }
    } else {
      local.historyNoSelect = true
    }

    // 履歴選択方法が「単一」の場合
    if (Or57082Const.DEFAULT.TANI === newValue) {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.TANI
    } else {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
      orX0117Oneway.type = Or57082Const.DEFAULT.STR.ZERO
    }
  }
)

/**
 * 記入用シート方式ラジオボタン監視
 */
watch(
  () => mo00018Type.value.modelValue,
  () => {
    setBeforChangePrintData(local.prtNo)
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 特記事項を別紙に印刷するチェックボックス監視
 */
watch(
  () => mo00018SpecialNoteAppendix.value.modelValue,
  () => {
    setBeforChangePrintData(local.prtNo)
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 特記事項印刷線の高さを最小チェックボックス監視
 */
watch(
  () => mo00018SpecialNotePrintLine.value.modelValue,
  () => {
    setBeforChangePrintData(local.prtNo)
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 特記事項最小行入力監視
 */
watch(
  () => mo00038SpecialNotePrintLine.value.mo00045.value,
  () => {
    setBeforChangePrintData(local.prtNo)
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * まとめ印刷線の高さを最小チェックボックス監視
 */
watch(
  () => mo00018SummaryPrintLine.value.modelValue,
  () => {
    setBeforChangePrintData(local.prtNo)
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * まとめ最小行入力監視
 */
watch(
  () => mo00038SummaryPrintLine.value.mo00045.value,
  () => {
    setBeforChangePrintData(local.prtNo)
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * (状況別)空白の項目も印刷チェックボックス監視
 */
watch(
  () => mo00018SummaryPrintLine.value.modelValue,
  () => {
    setBeforChangePrintData(local.prtNo)
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 「改訂版選択」の監視
 */
watch(
  () => mo00039RevisionSelect.value,
  async (newValue) => {
    if (newValue) {
      if (newValue === Or57082Const.DEFAULT.KITEI_RANGE_FLG_2) {
        // 改訂版が「H21~」の場合
        revisionRange.value = Or57082Const.DEFAULT.KITEI_H21
        // アセスメント履歴情報を取得する
        await getPrintSettingsHistoryList()
      }
    }
  }
)

/**
 * 「履歴選択」の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // 画面ボタン活性非活性設定
      btnItemSetting()
      if (newValue.historyDetClickFlg) {
        local.orX0128DetList = newValue.orX0128DetList
        if (newValue.orX0128DetList.length > 0) {
          local.historyNoSelect = false

          for (const item of newValue.orX0128DetList) {
            if (item) {
              // 選択履歴の改定フラグ
              local.kaiteiFlg = item.ninteiFormF as string
            }
          }
        } else {
          local.historyNoSelect = true
          // 選択履歴の改定フラグ
          local.kaiteiFlg = Or57082Const.DEFAULT.STR.EMPTY
        }
      } else {
        local.historyNoSelect = true
        // 選択履歴の改定フラグ
        local.kaiteiFlg = Or57082Const.DEFAULT.STR.EMPTY
      }
    } else {
      // 選択履歴の改定フラグ
      local.kaiteiFlg = Or57082Const.DEFAULT.STR.EMPTY
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      if (newValue.userList.length > 0) {
        local.userNoSelect = false
        local.selectUserId = newValue.userList[0].userId

        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }

        // 利用者選択方法が「単一」の場合
        if (Or57082Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
          if (initFlag.value) {
            // アセスメント履歴情報を取得する
            await getPrintSettingsHistoryList()
            orX0128OnewayModel.initSelectId = Or57082Const.DEFAULT.STR.EMPTY
          }
        }
        // 利用者選択方法が「複数」の場合
        else if (Or57082Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 利用者一覧明細に前回選択された利用者が選択状態になる
          createReportOutputData(local.prtNo)
        }
      } else {
        local.userList = []
        local.userNoSelect = true
        orX0128OnewayModel.items = []
      }
    } else {
      local.userList = []
      local.userNoSelect = true
      orX0128OnewayModel.items = []
    }
  }
)

/**
 * イベントリスナーの解除
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await close()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <!-- ローディング -->
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <template #cardItem>
      <c-v-row
        class="or57082_row h-100 overflow-hidden"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="or57082_table h-100"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
            :disable="{ mo01334OnewayDisable }"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="4"
          class="content_center"
        >
          <!-- 基本設定セクション -->
          <c-v-row
            no-gutter
            class="printerOption customCol or57082_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <!-- タイトルラベル -->
          <c-v-row
            no-gutter
            class="customCol or57082_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="or57082-pd-8"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
          </c-v-row>
          <!-- 帳票タイトル表示 -->
          <c-v-row
            no-gutter
            class="customCol or57082_row"
            style="padding-top: 0"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="or57082-pd-8"
              style="padding-top: 0"
            >
              <base-mo00045
                v-model="mo00045TitleType"
                class="w-100"
                :oneway-model-value="localOneway.mo00045OneWay"
              ></base-mo00045>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <!-- 日付印刷セクション -->
          <c-v-row
            no-gutter
            class="customCol or57082_row or57082-pd-8"
            style="padding-left: 0"
          >
            <c-v-col
              cols="12"
              sm="7"
              style="padding: 0px"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              style="padding: 0px"
              class="d-flex justify-end"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <!-- 印刷オプションセクション -->
          <c-v-row
            no-gutter
            class="printerOption customCol or57082_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <div>
            <!-- 記入用シートを印刷する -->
            <c-v-row
              no-gutter
              class="customCol or57082_row"
            >
              <c-v-col
                cols="12"
                sm="12"
                style="padding: 0px"
              >
                <base-mo00018
                  v-model="mo00018Type"
                  class="or57082_checkbox_label"
                  :oneway-model-value="localOneway.mo00018OneWay"
                >
                </base-mo00018>
              </c-v-col>
            </c-v-row>
            <!-- 特記事項を別紙に印刷する -->
            <c-v-row
              no-gutter
              class="customCol or57082_row"
            >
              <c-v-col
                cols="12"
                sm="12"
                class="pa-0"
              >
                <base-mo00018
                  v-model="mo00018SpecialNoteAppendix"
                  class="or57082_checkbox_label"
                  :oneway-model-value="localOneway.mo00018OneWaySpecialNoteAppendix"
                />
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-0"
              >
                <!-- 特記事項を別紙に印刷する補足ラベル -->
                <base-mo01338
                  class="comment"
                  :oneway-model-value="localOneway.mo01338OneWaySpecialNoteAppendix"
                />
              </c-v-col>
            </c-v-row>
            <!-- 特記事項印刷線の高さチェックボックス -->
            <c-v-row
              no-gutter
              class="customCol or57082_row"
            >
              <c-v-col
                cols="12"
                sm="12"
                class="py-2 my-2"
              >
                <c-v-row class="d-flex align-center">
                  <!-- 特記事項印刷線の高さを最小 -->
                  <base-mo00018
                    v-model="mo00018SpecialNotePrintLine"
                    class="or57082_checkbox_label"
                    :oneway-model-value="localOneway.mo00018OneWaySpecialNotePrintLine"
                  />
                  <!-- 特記事項印刷線の高さを最小行に設定するステップナビ -->
                  <base-mo00038
                    v-model="mo00038SpecialNotePrintLine"
                    :oneway-model-value="localOneway.mo00038OnewaySpecialNotePrintLine"
                  />
                  <!--行に設定するラベル-->
                  <base-mo01338
                    :class="{
                      disable_label: localOneway.mo00018OneWaySpecialNotePrintLine.disabled,
                    }"
                    :oneway-model-value="localOneway.mo01338OnewayLineSet"
                  />
                </c-v-row>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-0"
              >
                <!-- 特記事項印刷線の高最小行補足 -->
                <base-mo01338
                  class="comment"
                  :oneway-model-value="localOneway.mo01338OneWaySpecialNotePrintLine"
                />
              </c-v-col>
            </c-v-row>
            <!-- まとめ印刷線の高最小行チェックボックス -->
            <c-v-row
              v-if="summaryPrintLineDisplay"
              class="or57082_row"
              no-gutter
            >
              <c-v-col
                cols="12"
                sm="12"
                class="py-2 my-2"
              >
                <c-v-row class="d-flex align-center">
                  <!-- まとめ印刷線の高さを最小 -->
                  <base-mo00018
                    v-model="mo00018SummaryPrintLine"
                    class="or57082_checkbox_label"
                    :oneway-model-value="localOneway.mo00018OneWaySummaryPrintLine"
                  />
                  <!-- まとめ印刷線の高さを最小行に設定するステップナビ -->
                  <base-mo00038
                    v-model="mo00038SummaryPrintLine"
                    :oneway-model-value="localOneway.mo00038OnewaySummaryPrintLine"
                  />
                  <!--行に設定するラベル-->
                  <base-mo01338
                    :class="{ disable_label: localOneway.mo00018OneWaySummaryPrintLine.disabled }"
                    :oneway-model-value="localOneway.mo01338OnewayLineSet"
                  />
                </c-v-row>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-0"
              >
                <!-- まとめ印刷線の高最小行補足 -->
                <base-mo01338
                  class="comment"
                  :oneway-model-value="localOneway.mo01338OneWaySummaryPrintLine"
                />
              </c-v-col>
            </c-v-row>
            <!-- (状況別)空白の項目も印刷するチェックボックス -->
            <c-v-row
              v-if="blankItemsDisplay"
              class="or57082_row"
              no-gutter
            >
              <c-v-col
                cols="12"
                sm="12"
                class="pa-0"
              >
                <!-- (状況別)空白の項目も印刷する -->
                <base-mo00018
                  v-model="mo00018BlankItems"
                  class="or57082_checkbox_label"
                  :oneway-model-value="localOneway.mo00018OneWayBlankItems"
                />
              </c-v-col>
            </c-v-row>
          </div>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="content_center"
        >
          <!-- 改訂版選択方法セクション -->
          <c-v-row class="ma-0 pt-2 px-2 d-flex align-center">
            <div class="pa-0">
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayRevision"
                style="background-color: transparent"
              >
              </base-mo01338>
            </div>
            <div
              class="py-0 px-2 d-flex align-center"
              style="margin-bottom: -4px"
            >
              <base-mo00039
                v-model="mo00039RevisionSelect"
                :oneway-model-value="localOneway.mo00039OneWayRevisionSelect"
              >
                <!-- 「～H18」は選択不可の状態 -->
                <base-at-radio
                  v-for="(item, index) in revisionSelectItems"
                  :key="'mo57082-' + index"
                  :name="'mo57082-radio-' + index"
                  :radio-label="item.label"
                  :value="item.value"
                  :disabled="item.value === Or57082Const.DEFAULT.KITEI_RANGE_FLG_1"
                />
              </base-mo00039>
            </div>
            <div class="pa-0">
              <base-mo01338
                class="comment_warnning"
                :oneway-model-value="localOneway.mo01338OneWayRevisionSelect"
              />
            </div>
          </c-v-row>
          <c-v-row
            class="or57082_row pr-2"
            no-gutter
            style="align-items: center"
          >
            <!-- 利用者選択方法セクション -->
            <c-v-col class="pa-0">
              <base-mo00039
                v-model="mo00039OneWayUserSelectType"
                :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
              >
              </base-mo00039>
            </c-v-col>
            <!-- 履歴選択方法セクション -->
            <c-v-col
              v-if="!kijunbiFlag"
              class="pa-0"
            >
              <base-mo00039
                v-model="mo00039OneWayHistorySelectType"
                :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
              >
              </base-mo00039>
            </c-v-col>
            <!-- 基準日選択 -->
            <c-v-col
              v-else
              class="pa-0"
            >
              <base-mo00020
                v-model="mo00020TypeKijunbi"
                :oneway-model-value="localOneway.mo00020KijunbiOneWay"
              />
            </c-v-col>
            <c-v-col
              v-if="tantoIconBtn"
              class="pa-0 pl-2"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
                @update:model-value="orx0145UpdateModelValue"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or57082_row"
            no-gutter
          >
            <div
              :style="{ width: userCols }"
              class="pa-2 userlist-style"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </div>
            <div
              v-if="mo01334TypeHistoryFlag"
              :style="{ width: `calc(100% - ${userCols})` }"
              class="pa-2"
            >
              <div class="overflow-x-auto">
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0128
                  v-if="orX0128OnewayModel.singleFlg"
                  v-bind="orX0128"
                  :oneway-model-value="orX0128OnewayModel"
                ></g-custom-or-x-0128>
              </div>
            </div>
            <div class="w-100 pa-2 pt-0">
              <base-mo01338
                v-if="
                  orX0130Oneway.selectMode === OrX0130Const.DEFAULT.HUKUSUU ||
                  orX0128OnewayModel.singleFlg === OrX0128Const.DEFAULT.HUKUSUU
                "
                class="comment_warnning"
                style="white-space: pre-line"
                :oneway-model-value="localOneway.mo01338OneWayPrinterOptionLabel"
              />
            </div>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row class="d-flex justify-end or57082_row">
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="ml-2"
          @click="pdfDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- メッセージ 警告 -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21814 v-bind="or21813" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>
<style>
.or57082_content {
  padding: 0px !important;
}

.or57082_gokeiClass {
  label {
    color: rgb(var(--v-theme-secondaryBackground));
  }
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
:deep(.v-table__wrapper) {
  overflow-x: auto !important;
}

.or57082_checkbox_label :deep(.v-input) {
  font-weight: bold !important;
}

.or57082_table {
  padding: 8px;
}

.or57082_row {
  margin: 0px !important;
}

.comment :deep(.item-label) {
  font-size: 12px !important;
  color: rgb(var(--v-theme-subText));
}

.comment_warnning :deep(.item-label) {
  font-size: 12px !important;
  color: rgb(var(--v-theme-error));
}

.content_center {
  border-left: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .or57082-pd-8 {
    padding: 8px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-background));
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

/* 非表示ラベルの色変更 */
.disable_label :deep(.item-label) {
  opacity: 0.32;
}

.userlist-style {
  :deep(.v-col) {
    padding: 0;
  }
}
</style>
