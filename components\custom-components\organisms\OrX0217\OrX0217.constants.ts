/**
 * OrX0217:静的データ
 * GUI01272_認定調査票特記事項
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
import { getSequencedCpId } from '~/utils/useScreenUtils'

export namespace OrX0217Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or26426', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * 本文末に追加
     */
    export const ADD_END_TEXT_IMPORT_TYPE = '0'
    /**
     * 本文上書
     */
    export const OVERWRITE_TEXT_IMPORT_TYPE = '1'
    /**
     *CANCEL
     */
    export const DIALOG_RESULT_CANCEL = 'cancel'
    /**
     *YES
     */
    export const DIALOG_RESULT_YES = 'yes'
    /**
     *NO
     */
    export const DIALOG_RESULT_NO = 'no'
    /**
     * value: ''
     */
    export const EMPTY = ''
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM1 = '1'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM2 = '2'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM3 = '3'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM4 = '4'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM5 = '5'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM6 = '6'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM7 = '7'
  }
}
