<script setup lang="ts">
/**
 * Or32647:有機体:直近の入所・入院サブセクション
 * ［アセスメント］画面（居宅）（３）
 *
 * @description
 * ページタイトルを表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { vMaska } from 'maska'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { cloneDeep } from 'lodash'
import { Gui00017Logic } from '../Gui00017/Gui00017.logic'
import { Gui00017Const } from '../Gui00017/Gui00017.constants'
import { Or32647Const } from './Or32647.constants'
import { useNuxtApp } from '#app'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Or32647OnewayType, Or32647Type } from '~/types/cmn/business/components/Or32647Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type {
  AssessmentHomeFacilityInfoSelectInEntity,
  AssessmentHomeFacilityInfoSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentHomeFacilityInfoEntity'
import type {
  AssessmentMotZipFocusOutEntitySelectInEntity,
  AssessmentMotZipFocusOutEntitySelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentMotZipFocusOutEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useScreenStore, useScreenTwoWayBind, useSetupChildProps } from '#imports'
const $log = useNuxtApp().$log as DebugLogPluginInterface
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  modelValue: Or32647Type
  onewayModelValue: Or32647OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

/** One-way */
const localOneway = reactive({
  // 施設・機関名
  facilityInstitutionOnewayType: {
    itemLabel: '',
    items: [],
    itemTitle: 'shisetuKnj',
    itemValue: 'shisetuKnj',
    width: '300px',
    showItemLabel: true,
    isVerticalLabel: true,
  } as Mo00040OnewayType,
  // 所在地ラベル
  mo00615Oneway: {
    itemLabel: '',
  } as Mo00615OnewayType,
  // 所在地
  locationOnewayType: {
    showItemLabel: false,
    width: '108px',
    maxlength: '8',
    isVerticalLabel: false,
  } as Mo00045OnewayType,
  // 郵便番号から入力ボタン
  zipCodeBtnOneway: {
    btnLabel: '〒',
    width: '36px',
    minWidth: '20px',
  } as Mo00611OnewayType,
  // 備考
  remarksOnewayType: {
    maxlength: '60',
    showItemLabel: false,
    minWidth: '464px',
    isVerticalLabel: false,
  } as Mo00045OnewayType,
  // 電話番号
  telLabelOnewayType: {
    itemLabel: '',
    showItemLabel: true,
    width: '161px',
    maxlength: '14',
    isVerticalLabel: true,
  } as Mo00045OnewayType,
})

const gui00017 = ref({ uniqueCpId: '' })
/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Gui00017Const.CP_ID(0)]: gui00017.value,
})

const { refValue } = useScreenTwoWayBind<Or32647Type>({
  cpId: Or32647Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * 算出プロパティ
 **************************************************/
// ダイアログ表示フラグ
const showDialog = computed(() => {
  // GuiD0017のダイアログ開閉状態
  return Gui00017Logic.state.get(gui00017.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  $log.debug(`★[onMounted] [cpId]${Or32647Const.CP_ID(0)} [uId]${props.uniqueCpId}`)
})

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) return
    refValue.value = cloneDeep(newValue)

    // RefValue初期化
    useScreenStore().setCpTwoWay({
      cpId: Or32647Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: refValue.value,
      isInit: true,
    })
  },
  { deep: true, immediate: true }
)

watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.facilityInstitutionOnewayType.items = newValue.shisetsuDataList
    localOneway.facilityInstitutionOnewayType.itemLabel = newValue.facilityInstitutionLabel
    localOneway.mo00615Oneway.itemLabel = newValue.locationOnewayLabel
    localOneway.telLabelOnewayType.itemLabel = newValue.telNumberLabel
  },
  { deep: true, immediate: true }
)

// watch(
//   () => local.or32647.useFacilityType,
//   (newValue) => {
//     getShisetsuDataList(newValue.raidoValue)
//   },
//   { deep: true, immediate: true }
// )

/**
 * 郵便番号検索を監視
 */
// watch(
//   () => Gui00017Logic.event.get(gui00017.value.uniqueCpId),
//   (newValue) => {
//     if (!newValue) return
//     refValue.value!.zipCode.value = newValue.postalCode
//     refValue.value!.remarks.value = newValue.address
//   }
// )

/**************************************************
 * 関数
 **************************************************/
/**
 *  施設名称情報リスト取得
 *
 */
async function getShisetsuDataList() {
  await nextTick()
  localOneway.facilityInstitutionOnewayType.items = [
    {
      // 施設ID
      shisetuId: '',
      // 施設名
      shisetuKnj: '',
      // 調査票施設種別
      scShisetsuShu: '',
      // 電話番号
      tel: '',
      // 住所
      addressKnj: '',
      // 表示順
      sort: '',
      // 郵便番号
      zip: '',
    },
  ]

  // バックエンドAPIから初期情報取得
  const inputData: AssessmentHomeFacilityInfoSelectInEntity = {
    /** 調査票施設種別 */
    scShisetsuShu: refValue.value!.useFacilityType.raidoValue ?? '',
  }
  const resData: AssessmentHomeFacilityInfoSelectOutEntity = await ScreenRepository.select(
    'assessmentShisetuShybetuChangeSelect',
    inputData
  )

  if (resData.data) {
    resData.data.shisetsuDataList.forEach((item) => {
      // リスト追加
      localOneway.facilityInstitutionOnewayType.items?.push(item)
    })
  }
}

/**
 * 郵便番号情報リストを取得する
 */
const getZipData = async () => {
  await nextTick()
  // 郵便番号がフォーカスアウト時、且つ、画面.備考が空の場合
  if (refValue.value.remarks.value === '') {
    // バックエンドAPIから初期情報取得
    const inputData: AssessmentMotZipFocusOutEntitySelectInEntity = {
      /** 郵便番号 */
      zip: refValue.value!.zipCode.value ?? '',
    }
    const resData: AssessmentMotZipFocusOutEntitySelectOutEntity = await ScreenRepository.select(
      'assessmentMotZipFocusOutSelect',
      inputData
    )
    if (resData.data) {
      if (resData.data.motZipList.length === 1) {
        refValue.value.remarks.value =
          resData.data.motZipList[0].kennameKana +
          resData.data.motZipList[0].citynameKana +
          resData.data.motZipList[0].areanameKana
      } else if (resData.data.motZipList.length > 1) {
        // GUI00017［郵便番号検索］画面をポップアップで起動する
        onClick()
      }
      // 返却情報の取得件数が0件の場合
      else {
        return
      }
    }
  } else {
    return
  }
}

/**
 * セレクトフィールドをクリックすると、各種値を設定
 *
 * @param selectValue - セレクトフィールドの選択値
 */
function setValue(selectValue: string | undefined) {
  localOneway.facilityInstitutionOnewayType.items?.forEach(
    (item: { shisetuKnj: string; zip: string; addressKnj: string; tel: string }) => {
      if (item.shisetuKnj === selectValue) {
        // 郵便番号
        refValue.value!.zipCode.value = item.zip
        // 住所
        refValue.value!.remarks.value = item.addressKnj
        // 電話番号
        refValue.value!.telNumber.value = item.tel
      }
    }
  )
}

/**
 *  郵便番号ボタン押下時の処理
 */
function onClick() {
  // Gui00017のダイアログ開閉状態を更新する
  Gui00017Logic.state.set({
    uniqueCpId: gui00017.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const options = { mask: '###-####' }
</script>
<template>
  <c-v-row no-gutters>
    <!-- '直近の入所・入院' -->
    <div class="c-sub-title">
      <span> {{ t('label.recent-admission-hospitalization') }}</span>
    </div>
  </c-v-row>
  <c-v-row
    no-gutters
    class="or32647Wrapper"
  >
    <c-v-col cols="6">
      <!-- 直近の入所・入院ラジオボックス -->
      <c-v-row no-gutters>
        <c-v-col class="px-9 pt-6">
          <base-mo00039
            v-model="refValue!.useFacilityType.raidoValue"
            class="radio-area"
            :oneway-model-value="refValue!.useFacilityType.radioOneway"
            @click="getShisetsuDataList"
          />
        </c-v-col>
      </c-v-row>
    </c-v-col>
    <c-v-col
      cols="6"
      class="pl-9"
    >
      <c-v-row
        no-gutters
        class="pt-6"
      >
        <!-- 施設・機関名-->
        <base-mo00040
          v-model="refValue!.facilityInstitution"
          class="label-set"
          :oneway-model-value="localOneway.facilityInstitutionOnewayType"
          @update:model-value="setValue(refValue!.facilityInstitution.modelValue)"
        />
      </c-v-row>
      <c-v-row
        no-gutters
        class="py-2 pt-6"
      >
        <c-v-col>
          <div>
            <!-- 所在地ラベル -->
            <base-mo00615
              :oneway-model-value="localOneway.mo00615Oneway"
              class="label-set"
            />
          </div>
          <div class="input-field-container d-flex">
            <!-- 郵便番号から入力ボタン-->
            <base-mo00611
              :oneway-model-value="localOneway.zipCodeBtnOneway"
              class="icon-edit-btn"
              @click.stop="onClick"
            />
            <!-- 所在地-->
            <base-mo00045
              v-model="refValue!.zipCode"
              v-maska:[options]
              :oneway-model-value="localOneway.locationOnewayType"
              class="zip-code-boder"
              @blur="getZipData"
            >
            </base-mo00045>
          </div>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="pt-4"
      >
        <!-- 備考-->
        <base-mo00045
          v-model="refValue!.remarks"
          :oneway-model-value="localOneway.remarksOnewayType"
        />
      </c-v-row>
      <c-v-row
        no-gutters
        class="pt-6"
      >
        <!-- 電話番号テキスト-->
        <base-mo00045
          v-model="refValue!.telNumber"
          class="label-set"
          :oneway-model-value="localOneway.telLabelOnewayType"
        />
      </c-v-row>
    </c-v-col>
  </c-v-row>

  <!-- 郵便番号検索ダイアログ -->
  <g-custom-gui-00017
    v-if="showDialog"
    v-bind="gui00017"
  />
</template>

<style lang="scss">
.zip-code-boder .v-field__input {
  border-top-left-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
  border-end-start-radius: 0px !important;
}
</style>
<style scoped lang="scss">
// '直近の入所・入院'ラベル
.c-sub-title {
  height: 35px;
  font-size: 14px !important;
}
.or32647Wrapper {
  background-color: rgb(var(--v-theme-surface));
}

// 郵便番号ボタン
.input-field-container {
  :deep(.v-input__prepend) {
    margin-right: 0px;
  }

  // 入力補助ボタンのスタイル
  .icon-edit-btn {
    line-height: 36px;
    cursor: pointer;
    text-align: center;
    border: 1px rgb(var(--v-theme-black-200)) solid;
    background: #ebf2fd;
    border-top-right-radius: 0px !important;
    border-end-end-radius: 0px !important;
    border-right: none;
    :deep(.v-btn__content span) {
      color: #869fca !important;
    }
  }
}

// 左側境界線の角丸除去
:deep(.zip-code-boder .v-input__control) {
  .v-field {
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px;
  }
}
:deep(.radio-area .v-selection-control) {
  height: 24px;
  min-height: 24px;
  padding-bottom: 16px;
}
:deep(.label-set .ma-1) {
  margin-bottom: 4px !important;
  margin-left: 0px !important;
}
:deep(.label-set .align-self-end) {
  margin-bottom: 4px !important;
  margin-left: 0px !important;
}
</style>
