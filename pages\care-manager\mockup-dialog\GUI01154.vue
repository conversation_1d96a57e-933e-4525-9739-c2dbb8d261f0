<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or35957Const } from '~/components/custom-components/organisms/Or35957/Or35957.constants'
import { Or35957Logic } from '~/components/custom-components/organisms/Or35957/Or35957.logic'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Or35957OnewayType } from '~/types/cmn/business/components/Or35957Type'
import type { CustomClass } from '~/types/CustomClassType'

/**
 * GUI01154_日割算定確認
 *
 * @description
 * 日割算定確認
 *
 * <AUTHOR> 李晨昊
 */
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01154'
// ルーティング
const routing = 'GUI01154/pinia'
// 画面物理名
const screenName = 'GUI01154'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or35957 = ref({ uniqueCpId: Or35957Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01154' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or29520Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or35957.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01154',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or35957Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or35957Const.CP_ID(1)]: or35957.value,
})

// ダイアログ表示フラグ
const showDialogOr35957 = computed(() => {
  // Or35957のダイアログ開閉状態
  return Or35957Logic.state.get(or35957.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or35957)
 */
function onClickOr35957() {
  // Or35957のダイアログ開閉状態を更新する
  Or35957Logic.state.set({
    uniqueCpId: or35957.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or35957Data: Or35957OnewayType = {
  /** 支援事業者ID */
  svJigyoId: '27',
  /** 利用者ID */
  userid: '2',
  /** 提供年月 */
  yymmYm: '2014/05',
  /** 作成年月日 */
  yymmD: '01',
  /**被保険者名 */
  insuredName: '被保険者 太郎',
}

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  /** 支援事業者ID */
  svJigyoId:  { value: '' } as Mo00045Type,
  /** 利用者ID */
  userid:  { value: '' } as Mo00045Type,
  /** 提供年月 */
  yymmYm:  { value: '' } as Mo00045Type,
  /** 作成年月日 */
  yymmD:  { value: '' } as Mo00045Type,
  /**被保険者名 */
  insuredName:  { value: '' } as Mo00045Type,
})

function onClickOr35957Test() {
  or35957Data.svJigyoId = local.svJigyoId.value
  or35957Data.userid = local.userid.value
  or35957Data.yymmYm = local.yymmYm.value
  or35957Data.yymmD = local.yymmD.value
  or35957Data.insuredName = local.insuredName.value
  // Or35957のダイアログ開閉状態を更新する
  Or35957Logic.state.set({
    uniqueCpId: or35957.value.uniqueCpId,
    state: { isOpen: true },
  })
}

</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr35957"
        >GUI01154_日割算定確認
      </v-btn>
      <g-custom-or35957
        v-if="showDialogOr35957"
        v-bind="or35957"
        :oneway-model-value="or35957Data"
      />
    </c-v-col>
  </c-v-row>

    <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">被保険者名</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.insuredName"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">支援事業者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.svJigyoId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userid"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">提供年月</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.yymmYm"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">作成年月日</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.yymmD"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr35957Test"> 疎通起動 </v-btn>
  </div>
</template>
