<script setup lang="ts">
/**
 * Or28983:有機体:［計画転送・実績取込モーダル］画面
 * GUI01190 ［計画転送・実績取込モーダル］
 *
 * <AUTHOR> 靳先念
 */
import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28983Const } from './Or28983.constants'
import type { Or28983StateType } from './Or28983.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or28983InterType } from '~/types/cmn/business/components/Or28983Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo01352OnewayType } from '~/types/business/components/Mo01352Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo01336OnewayType } from '~/types/business/components/Mo01336Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01332OnewayType, Mo01332Type } from '~/types/business/components/Mo01332Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type {
  Mo01334Headers,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type {
  PlanTransferAchievementImportSelectInEntity,
  PlanTransferAchievementImportSelectOutEntity,
} from '~/repositories/cmn/entities/PlanTransferAchievementImportSelectEntity'
import type {
  PlanTransferAchievementImportListSelectInEntity,
  PlanTransferAchievementImportListSelectOutEntity,
} from '~/repositories/cmn/entities/PlanTransferAchievementImportListSelectEntity'

import type {
  PlanTransferExecutionProcessUpdateInEntity,
  PlanTransferExecutionProcessUpdateOutEntity,
} from '~/repositories/cmn/entities/PlanTransferExecutionProcessUpdateEntity'
import type {
  AchievementsImportOfferOfficeExecutionProcessUpdateInEntity,
  AchievementsImportOfferOfficeExecutionProcessUpdateOutEntity,
} from '~/repositories/cmn/entities/AchievementsImportOfferOfficeExecutionProcessUpdateEntity'
import type { AchievementsImportOfferOfficeEndProcessUpdateInEntity } from '~/repositories/cmn/entities/AchievementsImportOfferOfficeEndProcessUpdateEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'
import type {
  Mo01354OnewayType,
  Mo01354Type,
  Mo01354Items,
  Mo01354Headers,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or28983InterType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const orx0145 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

const local = reactive({
  or28983: {
    // 処理年月
    shoriYm: { value: props.onewayModelValue.shoriYm },
    // 運用対象区分
    inoutKbn: '1',
    // 取込後に実績で計算
    recalculate: { modelValue: false } as Mo00018Type,
  },
  mo01334: {
    value: '',
    values: [],
  } as Mo01334Type,
  mo01354: {
    values: {
      selectedRowId: '-1',
      selectedRowIds: [],
      items: [] as Mo01354Items[],
    },
  } as Mo01354Type,
})

const INIT_HEADERS = [
  {
    title: t('label.plan-business-name'),
    key: 'jigyoNumber',
    sortable: false,
  },
  {
    title: t('label.plan-business-mnumber'),
    key: 'jigyoKnj',
    sortable: false,
  },
  {
    title: t('label.plan-differentiation'),
    key: 'ruakuKnj',
    sortable: false,
  },
  {
    title: t('label.plan-plans'),
    key: 'keikaku',
    sortable: false,
  },
  {
    title: t('label.plan-performance'),
    key: 'jisseki',
    sortable: false,
  },
] as Mo01354Headers[]

const localOneway = reactive({
  // 「履歴選択」ダイアログ
  mo00024Oneway: {
    width: '1200px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.plan-transfer-and-performance-capture'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 処理年月
  mo01338Oneway: {
    value: t('label.process-ym'),
  } as Mo01338OnewayType,
  mo01352Oneway: {
    textFieldwidth: '160px',
  } as Mo01352OnewayType,
  // 運用対象
  mo01338OnewayApplication: {
    value: t('label.application-object'),
  } as Mo01338OnewayType,
  // 運用対象区分
  mo00039Oneway: {
    name: 'editType',
    hideDetails: true,
    showItemLabel: false,
  } as Mo00039OnewayType,
  operationsList: [] as CodeType[],
  // 適用チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: t('label.calculated-label-after-delivery'),
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: false,
    multiple: false,
    selectedUserCounter: '',
    width: '190px',
  } as OrX0145OnewayType,
  // 履歴
  ResumeBtnOneway: {
    btnLabel: t('label.history'),
  } as Mo00611OnewayType,
  // 検索
  mo00609OnewaySearch: {
    btnLabel: t('label.search'),
  } as Mo00609OnewayType,
  // 事業所一覧
  mo01354Oneway: {
    /** 初期値：ヘッダー情報 */
    headers: INIT_HEADERS,
    height: '400px',
    rowHeight: '32px',
    /** 横幅調整機能を使用する際に指定 */
    columnMinWidth: {
      columnWidths: [60, 200, 338, 122, 80, 80],
    } as ResizableGridBinding,
    showSelect: true,
    selectStrategy: 'all',
    mandatory: false,
  } as Mo01354OnewayType,
  // 利用者一覧
  mo01334Oneway: {
    height: '400px',
    rowHeight: '32px',
    headers: [
      {
        title: t('label.plan-user-id'),
        key: 'selfId',
        sortable: false,
        minWidth: '20',
      },
      {
        title: t('label.plan-user-name'),
        sortable: false,
        key: 'name1Kana',
        minWidth: '20',
        maxWidth: '120',
      },
    ] as Mo01334Headers[],
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    density: 'default',
    showSelect: true,
    selectStrategy: 'all',
    items: [],
    mandatory: false,
  } as Mo01334OnewayType,
  // チェックボックス
  mo01332OnewayType: {
    showItemLabel: false,
    items: [{ label: '', value: '1' }],
    isVerticalLabel: true,
  } as Mo01332OnewayType,
})

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
}

// 計画転送
const mo00609OneWayPlanForwarding: Mo00609OnewayType = {
  btnLabel: t('label.delivery'),
  width: '90px',
}

// 実績取込
const mo00609OneWayPerformanceEvaluation: Mo00609OnewayType = {
  btnLabel: t('label.performance-evaluation'),
  width: '90px',
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28983Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/

const or21814_1 = ref({ uniqueCpId: '' })

const { setState } = useScreenOneWayBind<Or28983StateType>({
  cpId: Or28983Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28983Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814_1.value,
})

const showDialogOr21814_1 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Emit
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 共通code
  await initCodes()
  // 初期情報取得
  await init()

  // 初期選択状態の担当者カウンタ値設定
  localOneway.orX0145Oneway.selectedUserCounter = props.onewayModelValue.tantoId
})

async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TARGET }, // 415
  ]
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // 運用対象リスト
  localOneway.operationsList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_TARGET)
    .slice(1)
    .concat(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_TARGET)[0])
}

/** 初期情報取得 */
const init = async () => {
  const inputData: PlanTransferAchievementImportSelectInEntity = {
    svJigyoId: props.onewayModelValue.svJigyoId,
    shoriYm: props.onewayModelValue.shoriYm,
    tantoId: props.onewayModelValue.tantoId,
    shokuinId: props.onewayModelValue.shokuinId,
    gsysCd: props.onewayModelValue.gsysCd,
    tekiyoJigyoId: props.onewayModelValue.tekiyoJigyoId,
    inoutKbn: local.or28983.inoutKbn,
  }
  const resData: PlanTransferAchievementImportSelectOutEntity = await ScreenRepository.select(
    'planTransferAchievementImportSelect',
    inputData
  )
  local.or28983.recalculate.modelValue = resData.data.calcFlg === Or28983Const.DEFAULT.ONE
  // 事業所一覧
  local.mo01354.values.items = resData.data.jigyoList.map((item, index) => {
    return {
      id: (index * 10000 + 1).toString(),
      jigyoNumber: { value: Number(item.jigyoNumber), unit: '' } as Mo01336OnewayType,
      jigyoKnj: { value: item.jigyoKnj } as Mo01337OnewayType,
      ruakuKnj: { value: item.ruakuKnj } as Mo01337OnewayType,
      keikaku: { values: [item.keikaku === Or28983Const.DEFAULT.ONE ? '1' : ''] } as Mo01332Type,
      jisseki: { values: [item.jisseki === Or28983Const.DEFAULT.ONE ? '1' : ''] } as Mo01332Type,
    }
  })

  // 利用者一覧
  localOneway.mo01334Oneway.items = resData.data.userList.map((item) => {
    return {
      ...item,
      selfId: { value: Number(item.selfId), unit: '' } as Mo01336OnewayType,
      name1Kana: { value: item.name1Knj + ' ' + item.name2Knj } as Mo01337OnewayType,
    }
  })
}

/**
 * 履歴情報選択行設定様式
 *
 * @param item - 履歴情報選択行
 */

/**
 * 「検索」ボタン押下
 */
const searchTable = async () => {
  const inputData: PlanTransferAchievementImportListSelectInEntity = {
    shienId: props.onewayModelValue.svJigyoId,
    shoriYm: local.or28983.shoriYm.value,
    tekiyoJigyoId: props.onewayModelValue.tekiyoJigyoId,
    tantoId: orX0145Type.value?.value
      ? `${(orX0145Type.value.value as TantoCmnShokuin).shokuinKnj || ''}`.trim()
      : '',
    inoutKbn: local.or28983.inoutKbn,
  }
  const resData: PlanTransferAchievementImportListSelectOutEntity = await ScreenRepository.select(
    'planTransferAchievementImportListSelect',
    inputData
  )
  // 事業所一覧
  local.mo01354.values.items = resData.data.jigyoList.map((item, index) => {
    return {
      id: (index * 10000 + 1).toString(),
      jigyoNumber: { value: Number(item.jigyoNumber), unit: '' } as Mo01336OnewayType,
      jigyoKnj: { value: item.jigyoKnj } as Mo01337OnewayType,
      ruakuKnj: { value: item.ruakuKnj } as Mo01337OnewayType,
      keikaku: { values: [item.keikaku === Or28983Const.DEFAULT.ONE ? '1' : ''] } as Mo01332Type,
      jisseki: { values: [item.jisseki === Or28983Const.DEFAULT.ONE ? '1' : ''] } as Mo01332Type,
    }
  })

  // 利用者一覧
  localOneway.mo01334Oneway.items = resData.data.userList.map((item) => {
    return {
      ...item,
      selfId: { value: Number(item.selfId), unit: '' } as Mo01336OnewayType,
      name1Kana: { value: item.name1Knj + ' ' + item.name2Knj } as Mo01337OnewayType,
    }
  })
}
/** 「履歴」ボタン押下  */
const resumeOpen = () => {
  // GUI04519 転送履歴画面をポップアップで起動する。
}

/** 「運用対象区分」ラジオボタンをクリック 「処理年月次へ」ボタン押下  */
watch(
  () => [local.or28983.shoriYm, local.or28983.inoutKbn],
  async () => {
    await searchTable()
  }
)

/** 「計画転送」ボタン押下 */
const planDelivery = async () => {
  /** 画面.事業所選択がすべて未チェックの場合  */
  if (local.mo01354.values.selectedRowIds.length === Or28983Const.DEFAULT.NUMBER_ZERO) {
    return openInfoDialog(t('message.i-cmn-11394'))
  }
  /** 画面.利用者選択がすべて未チェックの場合  */
  if (local.mo01334.values.length === Or28983Const.DEFAULT.NUMBER_ZERO) {
    return openInfoDialog(t('message.i-cmn-11393'))
  }
  const inputData: PlanTransferExecutionProcessUpdateInEntity = {
    // 支援事業所ID
    svJigyoId: props.onewayModelValue.svJigyoId,
    // 処理年月
    shoriYm: local.or28983.shoriYm.value,
    // サービス提供事業所配列
    selJigyo: local.mo01354.values.selectedRowIds,
    // 利用者配列
    selUser: local.mo01334.values,
    // 職員ID
    shokuinId: props.onewayModelValue.shokuinId,
  }
  const ret: PlanTransferExecutionProcessUpdateOutEntity = await ScreenRepository.update(
    'planTransferExecutionProcessUpdate',
    inputData
  )
  if (ret.data.transferResult === Or28983Const.DEFAULT.ONE) {
    return openInfoDialog(t('message.i-cmn-10516'))
  }
}

/** 「実績取込」ボタン押下 */
const performanceEvaluation = async () => {

  const inputDataProcess: AchievementsImportOfferOfficeExecutionProcessUpdateInEntity = {
    // 支援事業所ID
    shienId: props.onewayModelValue.svJigyoId,
    // 処理年月
    shoriYm: local.or28983.shoriYm.value,
    // サービス提供事業所配列
    svJigyoId: local.mo01354.values.selectedRowIds,
    // 利用者配列
    userIdList: local.mo01334.values,
    // メインパラメータ
    mainParameters: props.onewayModelValue.mainParameters,
    // 取込後に実績で計算
    recalculate: local.or28983.recalculate.modelValue === true ? '1' : '0',
  }

  const resData: AchievementsImportOfferOfficeExecutionProcessUpdateOutEntity =
    await ScreenRepository.update(
      'achievementsImportOfferOfficeExecutionProcessUpdate',
      inputDataProcess
    )
  if (resData.data.result === Or28983Const.DEFAULT.ONE) {
    return openInfoDialog(t('message.i-cmn-10517'))
  }
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = async (): Promise<void> => {
  const inputDataProcess: AchievementsImportOfferOfficeEndProcessUpdateInEntity = {
    // 職員ID
    shokuinId: props.onewayModelValue.shokuinId,
    // システムCD
    gsyscd: props.onewayModelValue.gsysCd,
    // 取込後に実績で計算
    calcFlg: local.or28983.recalculate.modelValue === true ? '1' : '0',
  }
  await ScreenRepository.update('achievementsImportOfferOfficeEndProcessUpdate', inputDataProcess)
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await onClickCloseBtn()
    }
  }
)

/**
 * 確認ダイアログ表示
 *
 * @param msg - メッセージ
 */
const openInfoDialog = (msg: string) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    class="plan-delivery"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row style="margin-top: 2px; margin-bottom: -8px">
        <div class="d-flex">
          <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway" />
          <base-mo-01352
            v-model="local.or28983.shoriYm"
            class="time-change"
            :oneway-model-value="localOneway.mo01352Oneway"
          />
        </div>
        <div class="d-flex">
          <base-mo01338
            class="radio-change-title"
            :oneway-model-value="localOneway.mo01338OnewayApplication"
          />
          <base-mo00039
            v-model="local.or28983.inoutKbn"
            class="radio-change"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="item in localOneway.operationsList"
              :key="item.value"
              :value="item.value"
              :name="item.value"
              :radio-label="item.label"
            />
          </base-mo00039>
        </div>
        <div>
          <base-mo00018
            v-model="local.or28983.recalculate"
            class="checkbox-auto"
            :oneway-model-value="localOneway.mo00018OnewayType"
          >
          </base-mo00018>
        </div>
      </c-v-row>
      <c-v-row style="padding-left: 12px; display: flex; justify-content: space-between">
        <div class="d-flex btn-pad">
          <!-- 担当ケアマネプルダウン -->
          <g-custom-or-x-0145
            v-bind="orx0145"
            v-model="orX0145Type"
            :oneway-model-value="localOneway.orX0145Oneway"
          ></g-custom-or-x-0145>
          <!-- 履歴 -->
          <base-mo00611
            :oneway-model-value="localOneway.ResumeBtnOneway"
            class="btn-resume"
            @click.stop="resumeOpen"
          >
            <!--ツールチップ表示："画面を閉じます"-->
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="t('tooltip.resume-screen')"
            />
          </base-mo00611>
        </div>
        <div
          class="btn-pad"
          style="padding-right: 0px"
        >
          <!-- 検索 -->
          <base-mo00609
            class="mx-2 confim-btn"
            :oneway-model-value="localOneway.mo00609OnewaySearch"
            @click="searchTable"
          >
            <!--ツールチップ表示："設定を確定します。"-->
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :max-width="600"
              :text="t('tooltip.user-intelligence')"
              open-delay="200"
            />
          </base-mo00609>
        </div>
      </c-v-row>

      <c-v-divider
        class="my-0"
        style="margin-top: 20px !important"
      ></c-v-divider>
      <c-v-row style="display: flex; justify-content: space-between">
        <c-v-col
          cols="auto"
          class="table-header"
        >
          <base--mo-01354
            v-model="local.mo01354"
            :oneway-model-value="localOneway.mo01354Oneway"
            hide-default-footer
            class="list-wrapper mt-2"
          >
            <!-- 事業所番号 -->
            <template #[`item.jigyoNumber`]="{ item }">
              <div style="padding-right: 8px; padding-left: 16px">
                <base-mo01336 :oneway-model-value="item.jigyoNumber"> </base-mo01336>
              </div>
            </template>
            <!-- 事業所名 -->
            <template #[`item.jigyoKnj`]="{ item }">
              <div style="padding-right: 8px; padding-left: 16px">
                <base-mo01337 :oneway-model-value="item.jigyoKnj"> </base-mo01337>
              </div>
            </template>
            <!-- 種別 -->
            <template #[`item.ruakuKnj`]="{ item }">
              <div style="padding-right: 8px; padding-left: 16px">
                <base-mo01337 :oneway-model-value="item.ruakuKnj"> </base-mo01337>
              </div>
            </template>
            <!-- 計画 -->
            <template #[`item.keikaku`]="{ item }">
              <base-mo01332
                v-model="item.keikaku"
                :oneway-model-value="localOneway.mo01332OnewayType"
              ></base-mo01332>
            </template>
            <!-- 実績 -->
            <template #[`item.jisseki`]="{ item }">
              <base-mo01332
                v-model="item.jisseki"
                :oneway-model-value="localOneway.mo01332OnewayType"
              ></base-mo01332>
            </template>
          </base--mo-01354>
          <div style="text-align: center; margin-top: 20px">
            {{ local.mo01354.values.selectedRowIds.length }} /
            {{ local.mo01354.values.items.length }}
          </div>
        </c-v-col>
        <div
          style="
            margin-top: 12px;
            height: 500px;
            width: 1px;
            background-color: #000;
            opacity: 0.12;
            margin-left: 8px;
            margin-right: 8px;
          "
        ></div>
        <c-v-col cols="auto">
          <base-mo01334
            v-model="local.mo01334"
            :oneway-model-value="localOneway.mo01334Oneway"
            hide-default-footer
            class="list-wrapper mt-2 list-wrapper-mo01334"
          >
            <!-- 利用者番号 -->
            <template #[`item.selfId`]="{ item }">
              <base-mo01336 :oneway-model-value="item.selfId"> </base-mo01336>
            </template>
            <!-- 利用者名 -->
            <template #[`item.name1Kana`]="{ item }">
              <base-mo01337 :oneway-model-value="item.name1Kana"> </base-mo01337>
            </template>
          </base-mo01334>
          <div style="text-align: center; margin-top: 20px">
            {{ local.mo01334.values.length }} / {{ localOneway.mo01334Oneway.items.length }}
            {{ Or28983Const.DEFAULT.PERSON }}
          </div>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          class="mx-2"
          :oneway-model-value="mo00611Oneway"
          @click="onClickCloseBtn"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 計画転送-->
        <base-mo00609
          class="mx-2"
          :oneway-model-value="mo00609OneWayPlanForwarding"
          @click="planDelivery"
        >
          <!-- ツールチップ表示："利用票の予定データを提供事業所に転送します" -->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.plan-Delivery')"
          />
        </base-mo00609>
        <!-- 実績取込-->
        <base-mo00609
          class="mx-2 confim-btn"
          :oneway-model-value="mo00609OneWayPerformanceEvaluation"
          @click="performanceEvaluation"
        >
          <!-- ツールチップ表示："提供事業所の実績データを取り込みます" -->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.actual-performance')"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21814
    v-if="showDialogOr21814_1"
    v-bind="or21814_1"
  />
</template>
<style lang="scss">
.plan-delivery {
  .v-card-text {
    overflow: hidden !important;
  }
}
</style>
<style scoped lang="scss">
:deep(.v-table--density-default) {
  --v-table-row-height: 33px !important;
}
:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 24px !important;
}
:deep(.time-change) {
  margin-left: 0px;
  display: flex;
  align-items: center;
  .v-col {
    padding: 0 !important;
    .v-input__control {
      width: 160px;
    }
  }
}
:deep(.list-wrapper-mo01334) {
  .v-table__wrapper {
    table {
      thead {
        tr {
          th {
            border-bottom: 1px solid rgb(var(--v-theme-black-200)) !important;
          }
        }
      }
    }
  }
  .v-checkbox-btn {
    min-height: 24px;
    height: 24px;
  }
}
.radio-change-title {
  margin-right: 0px !important;
}

:deep(.radio-change) {
  .radio-group {
    margin-top: 0px;
    .v-selection-control {
      margin-left: 8px;
    }
  }
}

.btn-pad {
  margin-top: 8px;
  .btn-resume {
    margin-left: 80px;
  }
}
:deep(.list-wrapper) {
  margin-top: 40px !important;
  tr th {
    font-size: 14px;
  }
}

:deep(.v-table.v-table--fixed-header > .v-table__wrapper > table > thead > tr > th) {
  box-shadow: none !important;
}
.confim-btn {
  margin-right: 12px !important;
}

:deep(.v-checkbox-btn) {
  display: flex;
  justify-content: center;
}
</style>
