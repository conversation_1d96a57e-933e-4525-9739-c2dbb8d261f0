<script setup lang="ts">
/**
 * Or29241:有機体:［アセスメント］画面（居宅）（6②）
 * GUI00800_［アセスメント］画面（居宅）（6②）
 *
 * @description
 * ［アセスメント］画面（居宅）（6②）
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { reactive, ref, onMounted, watch } from 'vue'
import { Or03080Const } from '../Or03080/Or03080.constants'
import type { Or03080FormModelValueType } from '../Or03080/Or03080.type'
import { TeX0002Logic } from '../../template/TeX0002/TeX0002.logic'
import { Or03081Const } from '../Or03081/Or03081.constants'
import { Or35672Logic } from '../Or35672/Or35672.logic'
import { Or35672Const } from '../Or35672/Or35672.constants'
import { OrX0209Const } from '../OrX0209/OrX0209.constants'
import { OrX0200Const } from '../OrX0200/OrX0200.constants'
import { Or28500Const } from '../Or28500/Or28500.constants'
import { TeX0002Const } from '../../template/TeX0002/TeX0002.constants'
import { Or29241Const } from './Or29241.constants'
import { Or29241Logic } from './Or29241.logic'
import type { SaveData } from './Or29241.type'
import type { Or29241OnewayType } from '~/types/cmn/business/components/Or29241Type'
import type { Mo00039Items, Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type {
  Or03081OnewayType,
  Or03081TwoWayType,
  TableHeaderItem,
} from '~/types/cmn/business/components/Or03081Type'
import {
  useCmnCom,
  useCmnRouteCom,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  CareCertificationItem,
  OrCD002OnewayType,
  OrCD002Type,
} from '~/types/cmn/business/components/OrCD002Type'
import type { TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
import type {
  AssessmentHome62InsertInEntity,
  AssessmentHome62InsertOutEntity,
  assessmentHomeTab62SelectInEntity,
  assessmentHomeTab62SelectOutEntity,
  LifeFunctionInfoFormValues,
} from '~/repositories/cmn/entities/AssessmentHome62Entity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { UPDATE_KBN } from '~/constants/classification-constants'

import type {
  IssuesAndGoalListItem,
  OrX0209OnewayType,
  OrX0209Type,
} from '~/types/cmn/business/components/OrX0209Type'

const { setChildCpBinds, getChildCpBinds } = useScreenUtils()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const cmnRouteCom = useCmnRouteCom()

const { t } = useI18n()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or29241OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const or03080_1 = ref({ uniqueCpId: '' })
const or03080_2 = ref({ uniqueCpId: '' })
const orX0200 = ref({ uniqueCpId: '' })
const or03081_1 = ref({ uniqueCpId: '' })
const or03081_2 = ref({ uniqueCpId: '' })
const or03081_3 = ref({ uniqueCpId: '' })
const orX0209 = ref({ uniqueCpId: '' })
const or28500 = ref({ uniqueCpId: '' })
// 食事場所codeList
let mealLocationCodeList: Mo00018OnewayType[] = []
// 食堂までの段差codeList
let diningRoomCodeList: Mo00039Items[] = []
// 咀嚼の状況code
let chewingSituationIsHaveCodeList: Mo00039Items[] = []
// 咀嚼の状況問題あり場合codeList
let chewingSituationCodeList: Mo00018OnewayType[] = []
// 食事の内容codeList
let mealContentsCodeList: Mo00018OnewayType[] = []
// 尿意codeList
let urgeToUrinateCodeList: Mo00039Items[] = []
// 便意codeList
let urgeToDefecateCodeList: Mo00039Items[] = []

// ドロップダウンオプショングループ
const selectoptions = {
  familyImplementationOptions: [],
  serverImplementationOptions: [],
  hopeOptions: [],
  needAssistanceToPlanOptions: [],
}

// コンポーネント表示フラグ
const isComponentVisible = ref(true)

// フォーカス中項目ID
const updateKbn = ref(TeX0002Const.DEFAULT.UPDATE_KBN_N)

// ロード状態です
const isLoading = ref<boolean>(false)

// ローカルTwoway
const local = reactive({
  // 要介護認定項目 項目名と選択肢
  // 共通情報
  commonInfo: {} as TeX0002Type,
  // 生活機能
  lifeFunction: {
    careCertificationList: {
      sectionList: [
        {
          sectionTitle: t('label.assessment-home-6-1-level-of-care-required-certification-item'),
          sectionItems: [
            {
              /** 項目物理名 */
              name: '',
              /** 項目名 */
              itemName: '',
              /** ラジオボタン選択肢 */
              /** 選択肢タイプ 0:チェックボックス 1:ラジオボタン */
              itemType: '1',
              raidoItems: [] as CodeType[],
              /** ラジオボタン選択肢 */
              raidoValue: '',
              /** ラジオボタンonewayモデル */
              radioOneway: {
                name: '',
                showItemLabel: false,
                hideDetails: true,
              } as Mo00039OnewayType,
            } as CareCertificationItem,
          ],
        },
      ],
      basicMotionDetailList: [],
    },
  } as OrCD002Type,
  //その他食事の現状AND排泄の現状(コンポーネントです)-FormValues
  or03080ModelValue: {
    othersMealSituationForm: {},
    othersExcretionSituationForm: {},
  } as Or03080FormModelValueType,
  // 食事(コンポーネントです)-FormValues
  or03081MealModelValue: {
    items: [],
    textareaValue: { value: '' },
    checkboxGroupValue: [],
    commonInfo: {},
  } as Or03081TwoWayType,
  // 排泄等(コンポーネントです)-FormValues
  or03081ExcretionModelValue: {
    items: [],
    textareaValue: { value: '' },
    checkboxGroupValue: [],
  } as Or03081TwoWayType,
  // 外出(コンポーネントです)-FormValues
  or03081GoingOutModelValue: {
    items: [],
    textareaValue: { value: '' },
    checkboxGroupValue: [],
  } as Or03081TwoWayType,
  issuesAndGoalsList: {
    items: [] as IssuesAndGoalListItem[],
  } as OrX0209Type,
})

const localOneway = reactive({
  pageTitle: {
    /** タイトル数字 */
    tabNo: '6',
    /** タイトル名 */
    title: t('label.assessment-home-6-1-title'),
    scrollToUniqueCpId: '',
  },
  Or29241: {
    ...props.onewayModelValue,
  },
  // 生活機能Oneway
  lifeFunctionOnewayType: {
    titleDisplayRows: '12',
    /** サブタイトル */
    subTitle: t('label.assessment-home-6-2-life-function-meal-excretion'),
    /** 取込ボタン名 */
    importBtnName: t('label.assessment-home-6-1-servey-ledger-import'),
    /** 取込ボタン名 */
    importBtnToolTip: t('tooltip.assessment-home-6-1-servey-ledger-import'),
    /** 詳細ボタンツールチップ */
    detailBtnToolTip: t('tooltip.assessment-home-6-1-basic-motion'),
    codeMasterList: [],
  } as OrCD002OnewayType,
  issuesAndGoalsListOneway: {
    assessmentName:
      t('label.assessment-home-issuesAndGoals-no-6-2') +
      '\r\n' +
      t('label.assessment-home-issuesAndGoals-name-6-2'),
  } as OrX0209OnewayType,
})

// テーブルヘッダーの共通設定
const tableHeaders: TableHeaderItem[] = [
  {
    title: t('label.current-situation-of-assistance'),
    children: [
      {
        title: t('label.family-implementation'),
        value: 'familyImplementation',
        width: '90px',
      },
      {
        title: t('label.server-implementation'),
        value: 'serverImplementation',
        width: '80px',
      },
    ],
    width: '90px',
  },
  { title: t('label.hope'), value: 'hope', width: '70px' },
  {
    title: t('label.need-assistance-to-plan'),
    value: 'needAssistanceToPlan',
    width: '90px',
  },
]

// 食事コンポーネントonewayModelValue
const Or03081MealOnewayModelValue: Or03081OnewayType = reactive({
  key: 'meal',
  height: '246px',
  headingTitle: t('label.meal'),
  tableHeaders: [
    {
      title: t('label.assessment-home-6-2-2-1-and-2-4-relation'),
      value: 'relation',
      width: '98px',
    },
    ...tableHeaders,
  ],
  // テキストドメイン設定です
  textAreaSetting: {
    rows: '3',
    maxRows: '3',
    noResize: true,
  },
  expandCheckboxFormInfo: [
    {
      headingTitle: t('label.staple-food'),
      leftTitle: t('label.current-situation'),
      isShowOtherItem: true,
      idName: 'stapleFood' + props.uniqueCpId,
      leftItems: [],
      rightTitle: t('label.plan'),
      rightItems: [],
    },
    {
      headingTitle: t('label.side-dish'),
      isShowOtherItem: true,
      leftTitle: t('label.current-situation'),
      leftItems: [],
      rightTitle: t('label.plan'),
      rightItems: [],
    },
    {
      headingTitle: t('label.intake-assistance'),
      isShowOtherItem: false,
      leftTitle: t('label.current-situation'),
      leftItems: [],
      rightTitle: t('label.plan'),
      rightItems: [],
    },
  ],
  ...selectoptions,
  commonInfo: {},
})

// 排泄コンポーネントonewayModelValue
const Or03081ExcretionOnewayModelValue: Or03081OnewayType = reactive({
  headingTitle: t('label.excretion-etc'),
  key: 'excretion',
  height: '357px',
  tableHeaders: [
    {
      title: t('label.assessment-home-6-2-2-5-and-2-11-relation'),
      value: 'relation',
      width: '98px',
    },
    ...tableHeaders,
  ],
  // テキストドメイン設定です
  textAreaSetting: {
    rows: '3',
    maxRows: '3',
    noResize: true,
  },
  expandCheckboxFormInfo: [
    {
      headingTitle: t('label.urination-assistance') + '（2-5）',
      leftTitle: t('label.current-situation'),
      leftItems: [],
      rightTitle: t('label.plan'),
      rightItems: [],
    },
    {
      headingTitle: t('label.defecation-assistance') + '（2-6）',
      leftTitle: t('label.current-situation'),
      rightTitle: t('label.plan'),
      leftItems: [],
      rightItems: [],
    },
  ],
  ...selectoptions,
  commonInfo: {},
})

// 外出コンポーネントonewayModelValue
const Or03081GoingOutOnewayModelValue: Or03081OnewayType = reactive({
  headingTitle: t('label.going-out'),
  key: 'goOut',
  tableHeaders: [
    {
      title: t('label.assessment-home-6-2-2-12-relation'),
      value: 'relation',
      width: '98px',
    },
    ...tableHeaders,
  ],
  // テキストドメイン設定です
  textAreaSetting: {
    rows: '3',
    maxRows: '3',
    noResize: true,
  },
  expandCheckboxFormInfo: [],
  ...selectoptions,
  commonInfo: {},
})

/** 解決すべき課題と目標Ref */
const issuesAndGoalListRef = ref<HTMLElement | null>(null)

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0209Const.CP_ID(0)]: orX0209.value,
  [OrX0200Const.CP_ID(0)]: orX0200.value,
  [Or03080Const.CP_ID(1)]: or03080_1.value,
  [Or03080Const.CP_ID(2)]: or03080_2.value,
  [Or03081Const.CP_ID(1)]: or03081_1.value,
  [Or03081Const.CP_ID(2)]: or03081_2.value,
  [Or03081Const.CP_ID(3)]: or03081_3.value,
  [Or28500Const.CP_ID(0)]: or28500.value,
})

onMounted(() => {
  // 汎用コードマスタからコード情報を取得
  void initCodes()
  localOneway.pageTitle.scrollToUniqueCpId = orX0209.value.uniqueCpId
  // getCommonInfo()
  // void reload()
})

/**************************************************
 * 関数
 **************************************************/

/**
 *  画面共通情報を取得
 *
 * @param saveFlg - 保存フラグ
 */
function getCommonInfo(saveFlg: boolean | undefined) {
  const commonInfo = TeX0002Logic.data.get(props.parentUniqueCpId)
  console.log('🚀 ~ getCommonInfo ~ commonInfo:', commonInfo)
  if (commonInfo) {
    local.commonInfo = {
      ...commonInfo,
    }
  }
  // 解決すべき課題と目標一覧パラメータ設定
  if (!saveFlg) {
    localOneway.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
    localOneway.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
    localOneway.issuesAndGoalsListOneway.assNo = local.commonInfo.activeTabId
    localOneway.issuesAndGoalsListOneway.userId = local.commonInfo.userId

    local.issuesAndGoalsList.items =
      local.commonInfo.issuesAndGoalsList ?? ([] as IssuesAndGoalListItem[])
  }
}

/**
 *  画面コントロールデータを設定
 */
function setControlsData() {
  local.issuesAndGoalsList.items =
    cloneDeep(local.commonInfo.issuesAndGoalsList) ?? ([] as IssuesAndGoalListItem[])
}

/**
 *  複写画面から共通情報を取得
 */
function getCommonInfoFromCopy() {
  const commonInfo = Or35672Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo.ninteiFormF = commonInfo.params?.ninteiFormF
    local.commonInfo.activeTabId = commonInfo.params?.activeTabId
    local.commonInfo.jigyoId = commonInfo.params?.jigyoId
    local.commonInfo.sc1Id = commonInfo.params?.sc1Id
    local.commonInfo.gdlId = commonInfo.params?.gdlId
    local.commonInfo.createUserId = commonInfo.params?.createUserId
    local.commonInfo.createYmd = commonInfo.params?.createYmd
  }
}

/**
 *  コントロール初期化
 */
async function reload() {
  isLoading.value = true
  clearData()
  // 画面初期情報取得
  await getInitDataInfo()
  isLoading.value = false
}

/**
 *  画面初期情報取得
 */
async function getInitDataInfo() {
  // バックエンドAPIから初期情報取得
  const inputData: assessmentHomeTab62SelectInEntity = {
    /** 計画期間ID */
    sc1Id: local.commonInfo.sc1Id,
    /** アセスメントID */
    gdlId: local.commonInfo.gdlId,
    /** 改訂フラグ */
    ninteiFlg: local.commonInfo.ninteiFormF,
  }

  const resData: assessmentHomeTab62SelectOutEntity = await ScreenRepository.select(
    'assessmentHomeTab62Select',
    inputData
  )

  // 画面情報を設定
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    setFormData(resData)
  } else {
    if (props.onewayModelValue?.mode === Or29241Const.DEFAULT.MODE_COPY) {
      setOr35672State({ noData: true })
    }
  }
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 入浴関係基本動作の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LIFE_FUNCTION },
    // 家族実施
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_002 },
    // サービス実施 && 希望
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_001 },
    // 要援助→計画
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_003 },
    // 主食 現状計画
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_STAPLEFOODCURRENTSITUATION },
    // 副食 現状 計画
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SIDEDISHCURRENTSITUATION },
    // 摂取介助 現状
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CARESUPPORTCURRENTSITUATION },
    // 摂取介助 計画
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CARESUPPORTPLAN },
    // 排尿介助 現状
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_URINATIONASSISTANCECURRENTSITUATION },
    // 排尿介助 計画
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_URINATIONASSISTANCEPLAN },
    // 排便介助 現状
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DEFECATIONASSISTANCECURRENTSITUATION },
    // 排便介助 計画
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DEFECATIONASSISTANCEPLAN },
    // 食事場所
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEALLOCATION },
    // 食堂までの段差
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DININGROOM },
    // 咀嚼の状況
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CHEWINGSITUATIONISHAVE },
    // 咀嚼の状況問題あり場合
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CHEWINGSITUATION },
    // 食事の内容
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEALCONTENTS },
    // 尿意&便意
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BASIC_SURVEY_2_APPLICABLE_9 },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 基本動作コード処理
  getLifeFunctionCodes(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_LIFE_FUNCTION))

  localOneway.lifeFunctionOnewayType.codeMasterList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_LIFE_FUNCTION
  )
  // 家族実施selectOptionsの処理
  const familyImplementationCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_002
  )
  const familyImplementationTemData = familyImplementationCodeList.map((item) => {
    return {
      displayName: item.label.includes('""') ? '' : item.label,
      value: item.value,
    }
  })
  Or03081MealOnewayModelValue.familyImplementationOptions = familyImplementationTemData
  Or03081ExcretionOnewayModelValue.familyImplementationOptions = familyImplementationTemData
  Or03081GoingOutOnewayModelValue.familyImplementationOptions = familyImplementationTemData

  // サービス実施selectOptionsの処理
  const serverImplementationCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_001
  )
  const serverImplementationTemData = serverImplementationCodeList?.map((item) => {
    return {
      displayName: item.label.includes('""') ? '' : item.label,
      value: item.value,
    }
  })
  Or03081MealOnewayModelValue.serverImplementationOptions = serverImplementationTemData
  Or03081ExcretionOnewayModelValue.serverImplementationOptions = serverImplementationTemData
  Or03081GoingOutOnewayModelValue.serverImplementationOptions = serverImplementationTemData

  // 希望selectOptionsの処理
  const hopeTemData = serverImplementationTemData
  Or03081MealOnewayModelValue.hopeOptions = hopeTemData
  Or03081ExcretionOnewayModelValue.hopeOptions = hopeTemData
  Or03081GoingOutOnewayModelValue.hopeOptions = hopeTemData

  // 要援助→計画selectOptionsの処理
  const needAssistanceToPlanCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_003
  )
  const needAssistanceToPlanTemData = needAssistanceToPlanCodeList?.map((item) => {
    return {
      displayName: item.label.includes('""') ? '' : item.label,
      value: item.value,
    }
  })
  Or03081MealOnewayModelValue.needAssistanceToPlanOptions = needAssistanceToPlanTemData
  Or03081ExcretionOnewayModelValue.needAssistanceToPlanOptions = needAssistanceToPlanTemData
  Or03081GoingOutOnewayModelValue.needAssistanceToPlanOptions = needAssistanceToPlanTemData

  const stapleFoodTemList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_STAPLEFOODCURRENTSITUATION
  )
  // 主食現状checkboxGroup
  const stapleFoodCurrentSituationCodeList = stapleFoodTemList?.map((item) => {
    return { checkboxLabel: item.label, showItemLabel: false }
  })
  // 主食計画checkboxGroup
  const stapleFoodPlanCodeList = stapleFoodTemList?.map((item) => {
    return { checkboxLabel: item.label, showItemLabel: false }
  })

  const sideDishTem = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SIDEDISHCURRENTSITUATION
  )
  // 副食現状checkboxGroup
  const sideDishCurrentSituationCodeList = sideDishTem?.map((item) => {
    return { checkboxLabel: item.label, showItemLabel: false }
  })
  // 副食計画checkboxGroup
  const sideDishPlanCodeList = sideDishTem?.map((item) => {
    return { checkboxLabel: item.label, showItemLabel: false }
  })

  // 摂取介助現状checkboxGroup
  const intakeAssistanceCurrentSituationCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CARESUPPORTCURRENTSITUATION
  )?.map((item) => {
    return { checkboxLabel: item.label, showItemLabel: false }
  })
  // 摂取介助計画checkboxGroup
  const intakeAssistancePlanCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CARESUPPORTPLAN
  )?.map((item) => {
    return { checkboxLabel: item.label, showItemLabel: false }
  })

  const leftMaplist = [
    stapleFoodCurrentSituationCodeList,
    sideDishCurrentSituationCodeList,
    intakeAssistanceCurrentSituationCodeList,
  ]

  const rightMaplist = [stapleFoodPlanCodeList, sideDishPlanCodeList, intakeAssistancePlanCodeList]

  Or03081MealOnewayModelValue.expandCheckboxFormInfo.forEach((item, index) => {
    item.leftItems = leftMaplist[index]
    item.rightItems = rightMaplist[index]
  })

  // 排尿介助現状checkboxGroup
  const urinationAssistanceCurrentSituationCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_URINATIONASSISTANCECURRENTSITUATION
  )?.map((item, index) => {
    if (index === 3) {
      return {
        checkboxLabel: item.label,
        showItemLabel: false,
        customClass: new CustomClass({ outerClass: 'letter-spacing-3' }),
      }
    }
    return {
      checkboxLabel: item.label,
      showItemLabel: false,
    }
  })
  // 排尿介助計画checkboxGroup
  const urinationAssistancePlanCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_URINATIONASSISTANCEPLAN
  )?.map((item, index) => {
    if (index === 3) {
      return {
        checkboxLabel: item.label,
        showItemLabel: false,
        customClass: new CustomClass({ outerClass: 'letter-spacing-3' }),
      }
    }
    return {
      checkboxLabel: item.label,
      showItemLabel: false,
    }
  })
  // 排便介助現状checkboxGroup
  const defecationAssistanceCurrentSituationCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DEFECATIONASSISTANCECURRENTSITUATION
  )?.map((item, index) => {
    if (index === 3 || index === 4) {
      return {
        checkboxLabel: item.label,
        showItemLabel: false,
        customClass: new CustomClass({
          outerClass: index === 3 ? 'letter-spacing-3' : 'letter-spacing-1',
        }),
      }
    }
    return {
      checkboxLabel: item.label,
      showItemLabel: false,
    }
  })
  // 排便介助計画checkboxGroup
  const defecationAssistancePlanCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DEFECATIONASSISTANCEPLAN
  )?.map((item, index) => {
    if (index === 3 || index === 4) {
      return {
        checkboxLabel: item.label,
        showItemLabel: false,
        customClass: new CustomClass({
          outerClass: index === 3 ? 'letter-spacing-3' : 'letter-spacing-1',
        }),
      }
    }
    return {
      checkboxLabel: item.label,
      showItemLabel: false,
    }
  })

  const leftMaplist_2 = [
    urinationAssistanceCurrentSituationCodeList,
    defecationAssistanceCurrentSituationCodeList,
  ]
  const rightMaplist_2 = [urinationAssistancePlanCodeList, defecationAssistancePlanCodeList]

  Or03081ExcretionOnewayModelValue.expandCheckboxFormInfo.forEach((item, index) => {
    item.leftItems = leftMaplist_2[index]
    item.rightItems = rightMaplist_2[index]
  })

  // 食事場所checkboxGroup
  mealLocationCodeList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_MEALLOCATION).map(
    (item) => {
      return {
        checkboxLabel: item.label,
        showItemLabel: false,
      }
    }
  )
  // 食堂までの段差Radio
  diningRoomCodeList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_DININGROOM).map(
    (item) => {
      return { label: item.label, value: item.value }
    }
  )
  // 咀嚼の状況Radio
  chewingSituationIsHaveCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CHEWINGSITUATIONISHAVE
  ).map((item) => {
    return { label: item.label, value: item.value }
  })
  // 咀嚼の状況問題あり場合checkboxGroup
  chewingSituationCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CHEWINGSITUATION
  ).map((item) => {
    return {
      checkboxLabel: item.label,
      showItemLabel: false,
    }
  })
  // 食事の内容checkboxGroup
  mealContentsCodeList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_MEALCONTENTS).map(
    (item) => {
      return {
        checkboxLabel: item.label,
        showItemLabel: false,
      }
    }
  )

  const urgeTemData = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BASIC_SURVEY_2_APPLICABLE_9
  )
  // 尿意Radio
  urgeToUrinateCodeList = urgeTemData.map((item) => {
    return { label: item.label, value: item.value }
  })
  // 便意Radio
  urgeToDefecateCodeList = urgeTemData.map((item) => {
    return { label: item.label, value: item.value }
  })
}

/**
 *  画面データを設定
 *
 * @param resData - 設定データ
 */
function setFormData(resData: assessmentHomeTab62SelectOutEntity) {
  // 複写モードの場合、APIから取得のデータを保持
  if (props.onewayModelValue.mode === Or29241Const.DEFAULT.MODE_COPY) {
    Or29241Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        copyData: resData,
      },
    })
  }

  if (
    !resData.data?.lifeFunctionNinteiFlg4Info?.gdlId &&
    !resData.data?.lifeFunctionNinteiFlg5Info?.gdlId
  ) {
    // サブ情報がない場合、新規状態に設定する
    updateKbn.value = UPDATE_KBN.CREATE

    if (props.onewayModelValue?.mode === Or29241Const.DEFAULT.MODE_COPY) {
      setOr35672State({ noData: true })
    }
  } else {
    updateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_N
  }
  let resLifeFunctionInfo = {} as LifeFunctionInfoFormValues
  if (local.commonInfo.ninteiFormF === Or29241Const.DEFAULT.KAITEI_FLG_H21) {
    /** 生活機能情報（H21/４改訂版） */
    resLifeFunctionInfo = resData.data.lifeFunctionNinteiFlg4Info
  } else {
    /** 生活機能情報（R3/４改訂版） */
    resLifeFunctionInfo = resData.data.lifeFunctionNinteiFlg5Info
  }

  local.lifeFunction.careCertificationList.sectionList.forEach((item) => {
    // 生活機能フォーム値処理です
    item.sectionItems.forEach((sItem) => {
      sItem.raidoValue = resLifeFunctionInfo?.[sItem.name] ?? ''
    })
    // その他食事の現状And排泄の現状フォーム値処理です
    setChildCpBinds(props.uniqueCpId, {
      [Or03080Const.CP_ID(1)]: {
        oneWayState: {
          mode: 1,
          mealLocationCodeList,
          diningRoomCodeList,
          chewingSituationIsHaveCodeList,
          chewingSituationCodeList,
          mealContentsCodeList,
        },
        twoWayValue: {
          othersMealSituationForm: {
            mealLocationValue: [
              { modelValue: !!Number(resLifeFunctionInfo?.shokujiWhere1) },
              { modelValue: !!Number(resLifeFunctionInfo?.shokujiWhere2) },
              { modelValue: !!Number(resLifeFunctionInfo?.shokujiWhere3) },
              { modelValue: !!Number(resLifeFunctionInfo?.shokujiWhere4) },
              { modelValue: !!Number(resLifeFunctionInfo?.shokujiWhere5) },
            ],
            othersMealLocationInputValue: {
              value: resLifeFunctionInfo?.shokuMemo1Knj ?? '',
            },
            diningRoomStepValue: resLifeFunctionInfo?.shokuDansa ?? Or29241Const.DEFAULT.STRING_0,
            chewingSituationValue: resLifeFunctionInfo?.soshaku1 ?? Or29241Const.DEFAULT.STRING_0,
            chewingSituationCheckboxValue: [
              { modelValue: !!Number(resLifeFunctionInfo?.soshaku2) },
              { modelValue: !!Number(resLifeFunctionInfo?.soshaku3) },
              { modelValue: !!Number(resLifeFunctionInfo?.soshaku4) },
            ],
            mealContentsCheckbox: [
              { modelValue: !!Number(resLifeFunctionInfo?.shokujiShu1) },
              { modelValue: !!Number(resLifeFunctionInfo?.shokujiShu2) },
              { modelValue: !!Number(resLifeFunctionInfo?.shokujiShu3) },
              { modelValue: !!Number(resLifeFunctionInfo?.shokujiShu4) },
              { modelValue: !!Number(resLifeFunctionInfo?.shokujiShu5) },
            ],
            diabeticDietInputValue: {
              mo00045: {
                value: resLifeFunctionInfo?.calorie ?? '',
              },
            },
            highBloodPressureDietInputValue: {
              mo00045: {
                value: resLifeFunctionInfo?.gram ?? '',
              },
            },
            othersMealInputValue: { value: resLifeFunctionInfo?.shokuMemo2Knj ?? '' },
          },
        },
      },
      [Or03080Const.CP_ID(2)]: {
        oneWayState: {
          mode: 2,
          urgeToUrinateCodeList,
          urgeToDefecateCodeList,
        },
        twoWayValue: {
          othersExcretionSituationForm: {
            urgeToUrinateModelValue:
              resLifeFunctionInfo?.haisetuNyoui ?? Or29241Const.DEFAULT.STRING_0,
            urgeToDefecateModelValue:
              resLifeFunctionInfo?.haisetuBeni ?? Or29241Const.DEFAULT.STRING_0,
          },
        },
      },
    })

    //食事Tableフォーム値処理です
    local.or03081MealModelValue = {
      items: [
        {
          relation: `1）${t('label.transfer-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi211 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi211 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo211 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku211 ?? Or29241Const.DEFAULT.STRING_0,
          },
        },
        {
          relation: `2）${t('label.move-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi212 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi212 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo212 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku212 ?? Or29241Const.DEFAULT.STRING_0,
          },
        },
        {
          relation: `3）${t('label.intake-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi213 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi213 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo213 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku213 ?? Or29241Const.DEFAULT.STRING_0,
          },
          showArrowClassName: 'mealRightArrow' + props.uniqueCpId,
        },
      ],
      textareaValue: { value: resLifeFunctionInfo?.memo1Knj ?? '' },
      checkboxGroupValue: [
        {
          leftFormValues: [
            { modelValue: !!Number(resLifeFunctionInfo?.shuGenjo1) },
            { modelValue: !!Number(resLifeFunctionInfo?.shuGenjo2) },
            { modelValue: !!Number(resLifeFunctionInfo?.shuGenjo3) },
            { modelValue: !!Number(resLifeFunctionInfo?.shuGenjo4) },
            { modelValue: !!Number(resLifeFunctionInfo?.shuGenjo5) },
          ],
          rightFormValues: [
            { modelValue: !!Number(resLifeFunctionInfo?.shuKeikaku1) },
            { modelValue: !!Number(resLifeFunctionInfo?.shuKeikaku2) },
            { modelValue: !!Number(resLifeFunctionInfo?.shuKeikaku3) },
            { modelValue: !!Number(resLifeFunctionInfo?.shuKeikaku4) },
            { modelValue: !!Number(resLifeFunctionInfo?.shuKeikaku5) },
          ],
          leftOtherInputValue: { value: resLifeFunctionInfo?.genMemo1Knj ?? '' },
          rightOtherInputValue: { value: resLifeFunctionInfo?.keiMemo1Knj ?? '' },
        },
        {
          leftFormValues: [
            { modelValue: !!Number(resLifeFunctionInfo?.hukuGenjo1) },
            { modelValue: !!Number(resLifeFunctionInfo?.hukuGenjo2) },
            { modelValue: !!Number(resLifeFunctionInfo?.hukuGenjo3) },
            { modelValue: !!Number(resLifeFunctionInfo?.hukuGenjo4) },
          ],
          rightFormValues: [
            { modelValue: !!Number(resLifeFunctionInfo?.hukuKeikaku1) },
            { modelValue: !!Number(resLifeFunctionInfo?.hukuKeikaku2) },
            { modelValue: !!Number(resLifeFunctionInfo?.hukuKeikaku3) },
            { modelValue: !!Number(resLifeFunctionInfo?.hukuKeikaku4) },
          ],
          leftOtherInputValue: { value: resLifeFunctionInfo?.genMemo2Knj ?? '' },
          rightOtherInputValue: { value: resLifeFunctionInfo?.keiMemo2Knj ?? '' },
        },
        {
          leftFormValues: [
            { modelValue: !!Number(resLifeFunctionInfo?.seGenjo1) },
            { modelValue: !!Number(resLifeFunctionInfo?.seGenjo2) },
          ],
          rightFormValues: [
            { modelValue: !!Number(resLifeFunctionInfo?.seKeikaku1) },
            { modelValue: !!Number(resLifeFunctionInfo?.seKeikaku2) },
          ],
        },
      ],
    }

    //排泄等Tableフォーム値処理です
    local.or03081ExcretionModelValue = {
      items: [
        {
          relation: `1）${t('label.preparation-adjustment')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi251 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi251 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo251 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku251 ?? Or29241Const.DEFAULT.STRING_0,
          },
        },
        {
          relation: `2）${t('label.transfer-move-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi252 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi252 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo252 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku252 ?? Or29241Const.DEFAULT.STRING_0,
          },
        },
        {
          relation: `3）${t('label.urination-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi253 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi253 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo253 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku253 ?? Or29241Const.DEFAULT.STRING_0,
          },
          showArrowClassName: 'urinationRightArrow' + props.uniqueCpId,
        },
        {
          relation: `4）${t('label.defecation-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi254 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi254 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo254 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku254 ?? Or29241Const.DEFAULT.STRING_0,
          },
          showArrowClassName: 'defecationRightArrow' + props.uniqueCpId,
        },
        {
          relation: `5）${t('label.oral-cavity-cleanliness-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi255 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi255 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo255 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku255 ?? Or29241Const.DEFAULT.STRING_0,
          },
        },
        {
          relation: `6）${t('label.wash-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi256 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi256 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo256 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku256 ?? Or29241Const.DEFAULT.STRING_0,
          },
        },
        {
          relation: `7）${t('label.cosmetic-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi257 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi257 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo257 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku257 ?? Or29241Const.DEFAULT.STRING_0,
          },
        },
        {
          relation: `8）${t('label.changing-clothes-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi258 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi258 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo258 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku258 ?? Or29241Const.DEFAULT.STRING_0,
          },
        },
      ],
      textareaValue: { value: resLifeFunctionInfo?.memo2Knj ?? '' },
      checkboxGroupValue: [
        {
          leftFormValues: [
            { modelValue: !!Number(resLifeFunctionInfo?.ngenjo1) },
            { modelValue: !!Number(resLifeFunctionInfo?.ngenjo2) },
            { modelValue: !!Number(resLifeFunctionInfo?.ngenjo3) },
            { modelValue: !!Number(resLifeFunctionInfo?.ngenjo4) },
            { modelValue: !!Number(resLifeFunctionInfo?.ngenjo5) },
            { modelValue: !!Number(resLifeFunctionInfo?.ngenjo6) },
            { modelValue: !!Number(resLifeFunctionInfo?.ngenjo7) },
          ],
          rightFormValues: [
            { modelValue: !!Number(resLifeFunctionInfo?.nkeikaku1) },
            { modelValue: !!Number(resLifeFunctionInfo?.nkeikaku2) },
            { modelValue: !!Number(resLifeFunctionInfo?.nkeikaku3) },
            { modelValue: !!Number(resLifeFunctionInfo?.nkeikaku4) },
            { modelValue: !!Number(resLifeFunctionInfo?.nkeikaku5) },
            { modelValue: !!Number(resLifeFunctionInfo?.nkeikaku6) },
            { modelValue: !!Number(resLifeFunctionInfo?.nkeikaku7) },
          ],
        },
        {
          leftFormValues: [
            { modelValue: !!Number(resLifeFunctionInfo?.bgenjo1) },
            { modelValue: !!Number(resLifeFunctionInfo?.bgenjo2) },
            { modelValue: !!Number(resLifeFunctionInfo?.bgenjo3) },
            { modelValue: !!Number(resLifeFunctionInfo?.bgenjo4) },
            { modelValue: !!Number(resLifeFunctionInfo?.bgenjo5) },
            { modelValue: !!Number(resLifeFunctionInfo?.bgenjo6) },
            { modelValue: !!Number(resLifeFunctionInfo?.bgenjo7) },
            { modelValue: !!Number(resLifeFunctionInfo?.bgenjo8) },
            { modelValue: !!Number(resLifeFunctionInfo?.bgenjo9) },
          ],
          rightFormValues: [
            { modelValue: !!Number(resLifeFunctionInfo?.bkeikaku1) },
            { modelValue: !!Number(resLifeFunctionInfo?.bkeikaku2) },
            { modelValue: !!Number(resLifeFunctionInfo?.bkeikaku3) },
            { modelValue: !!Number(resLifeFunctionInfo?.bkeikaku4) },
            { modelValue: !!Number(resLifeFunctionInfo?.bkeikaku5) },
            { modelValue: !!Number(resLifeFunctionInfo?.bkeikaku6) },
            { modelValue: !!Number(resLifeFunctionInfo?.bkeikaku7) },
            { modelValue: !!Number(resLifeFunctionInfo?.bkeikaku8) },
            { modelValue: !!Number(resLifeFunctionInfo?.bkeikaku9) },
          ],
        },
      ],
    }

    //外出 Tableフォーム値処理です
    local.or03081GoingOutModelValue = {
      items: [
        {
          relation: `1）${t('label.transfer-going-out-assistance')}`,
          familyImplementation: {
            modelValue: resLifeFunctionInfo?.famJisshi2121 ?? Or29241Const.DEFAULT.STRING_0,
          },
          serverImplementation: {
            modelValue: resLifeFunctionInfo?.serJisshi2121 ?? Or29241Const.DEFAULT.STRING_0,
          },
          hope: {
            modelValue: resLifeFunctionInfo?.kibo2121 ?? Or29241Const.DEFAULT.STRING_0,
          },
          needAssistanceToPlan: {
            modelValue: resLifeFunctionInfo?.keikaku2121 ?? Or29241Const.DEFAULT.STRING_0,
          },
        },
      ],
      textareaValue: { value: resLifeFunctionInfo?.memo3Knj ?? '' },
      checkboxGroupValue: [],
    }
  })

  setOr35672State({ noData: false })
}
/**
 *  生活機能コード処理
 *
 * @param lifeFunctionItemList - 生活機能コードリスト
 */
function getLifeFunctionCodes(lifeFunctionItemList: CodeType[]) {
  const nameStr = Or29241Const.DEFAULT.RADIOSTR
  if (lifeFunctionItemList) {
    let sectionItems = []
    let currentItem = {} as CareCertificationItem
    for (const item of lifeFunctionItemList) {
      // アイテムタイトル
      if (item.label.startsWith('2-')) {
        currentItem = {} as CareCertificationItem
        currentItem.itemName = item.label
        currentItem.raidoItems = []
      }
      const selectItems = item.label.split('　')
      selectItems.forEach((sItem) => {
        currentItem.raidoItems?.push({
          label: sItem,
          value: getNumberFromString(sItem),
          shisetutouKbn: '',
        } as CodeType)
      })
      currentItem.radioOneway = {
        name: currentItem.itemName,
        showItemLabel: false,
        hideDetails: true,
      } as Mo00039OnewayType
    }

    sectionItems = lifeFunctionItemList
      .map((item, index) => {
        if (index % 2 === 0) {
          return {
            itemName: item.label,
            name: nameStr + transformLabel(item.label),
            itemType: '1',
            raidoItems: lifeFunctionItemList[index + 1].label.split('　').map((el) => {
              return { label: el, value: getNumberFromString(el), shisetutouKbn: '' } as CodeType
            }),
            raidoValue: '',
            radioOneway: {
              name: currentItem.itemName,
              showItemLabel: false,
              hideDetails: true,
            },
          }
        }
        return undefined
      })
      .filter((sItem) => !!sItem)

    local.lifeFunction.careCertificationList.sectionList[0].sectionItems = sectionItems
    local.lifeFunction.careCertificationList.basicMotionDetailList = lifeFunctionItemList
  }
}

/**
 *  文字列処理です
 *
 * @param label - ラベルです
 */
function transformLabel(label: string) {
  const match = /(\d+-\d+)/.exec(label)
  if (match) {
    const extracted = match[0].replace('-', '')
    return extracted
  }
}

/**
 *  文字列の数字を取得
 *
 * @param str - 文字列
 */
function getNumberFromString(str: string): string {
  const numbersAsString = /^\d+/.exec(str)

  // 转换为 number[]
  const targetNumber = numbersAsString?.map(Number)?.[0].toString() ?? ''

  return targetNumber
}

/**
 *  新規処理
 */
function createNew() {
  // 画面データをクリア
  clearData()
  updateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_C
}

/**
 *  保存処理
 *
 */
async function onSave(): Promise<SaveData> {
  if (updateKbn.value === TeX0002Const.DEFAULT.UPDATE_KBN_N) {
    updateKbn.value = UPDATE_KBN.UPDATE
  }
  if (!local.commonInfo.deleteKbn) {
    return { saveStatus: false }
  }
  isLoading.value = true

  // 生活機能入力値処理
  const lifeFunctionInfoFormValuesObject: LifeFunctionInfoFormValues = {}
  local.lifeFunction.careCertificationList.sectionList[0].sectionItems.forEach((item) => {
    lifeFunctionInfoFormValuesObject[item.name] = item.raidoValue
  })

  const {
    OrCD002Values,
    Or03080_1Values,
    Or03080_2Values,
    Or03081MealModelValue,
    Or03081ExcretionModelValue,
    Or03081GoingOutModelValue,
    OrX0209ModelValue,
  } = getChildCpBinds(props.uniqueCpId, {
    // 生活機能 要介護認定項目
    OrCD002Values: { cpPath: OrX0200Const.CP_ID(0), twoWayFlg: true },
    // その他食事
    Or03080_1Values: { cpPath: Or03080Const.CP_ID(1), twoWayFlg: true },
    // その他排泄の現状入力値取得
    Or03080_2Values: { cpPath: Or03080Const.CP_ID(2), twoWayFlg: true },
    // その他食事の現状入力値処理
    Or03081MealModelValue: { cpPath: Or03081Const.CP_ID(1), twoWayFlg: true },
    // その他排泄の現状入力値処理
    Or03081ExcretionModelValue: { cpPath: Or03081Const.CP_ID(2), twoWayFlg: true },
    // その他外出の現状入力値
    Or03081GoingOutModelValue: { cpPath: Or03081Const.CP_ID(3), twoWayFlg: true },
    // 課題と目標リスト
    OrX0209ModelValue: { cpPath: OrX0209Const.CP_ID(0), twoWayFlg: true },
  })

  // 生活機能 要介護認定項目入力値取得
  const lifeFunctionRadioData = OrCD002Values.twoWayBind?.value as OrCD002Type

  lifeFunctionRadioData.careCertificationList.sectionList.forEach((item) => {
    item.sectionItems.forEach((sItem) => {
      lifeFunctionInfoFormValuesObject[sItem.name] = sItem.raidoValue
    })
  })

  // その他食事、その他排泄の現状入力値取得
  const othersFormMealValues = Or03080_1Values?.twoWayBind?.value as Or03080FormModelValueType

  const othersFormExcretionValues = Or03080_2Values?.twoWayBind?.value as Or03080FormModelValueType
  // その他食事の現状入力値処理
  othersFormMealValues.othersMealSituationForm.mealLocationValue.forEach((item, index) => {
    lifeFunctionInfoFormValuesObject[Or29241Const.DEFAULT.MEALLOCATIONSTR + (index + 1)] =
      item.modelValue ? '1' : Or29241Const.DEFAULT.STRING_0
  })

  //  咀嚼の状況入力値処理
  othersFormMealValues.othersMealSituationForm.chewingSituationCheckboxValue.forEach(
    (item, index) => {
      lifeFunctionInfoFormValuesObject[Or29241Const.DEFAULT.CHEWINGSITUATIONSTR + (index + 2)] =
        item.modelValue ? '1' : Or29241Const.DEFAULT.STRING_0
    }
  )
  //  食事の内容入力値処理
  othersFormMealValues.othersMealSituationForm.mealContentsCheckbox.forEach((item, index) => {
    lifeFunctionInfoFormValuesObject[Or29241Const.DEFAULT.MEALCONTENTSTR + (index + 1)] =
      item.modelValue ? '1' : Or29241Const.DEFAULT.STRING_0
  })

  const mealTemValues = Or03081MealModelValue?.twoWayBind?.value as Or03081TwoWayType
  const excretionTemValues = Or03081ExcretionModelValue?.twoWayBind?.value as Or03081TwoWayType
  const goingOutTemValues = Or03081GoingOutModelValue?.twoWayBind?.value as Or03081TwoWayType

  //食事のTableValue入力値処理
  mealTemValues?.items.forEach((item, index) => {
    index++
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.FAMILYIMPLEMENTATIONSTR + Or29241Const.DEFAULT.STRING_21 + index
    ] = item.familyImplementation.modelValue
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.SERVERIMPLEMENTATIONSTR + Or29241Const.DEFAULT.STRING_21 + index
    ] = item.serverImplementation.modelValue
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.HOPESTR + Or29241Const.DEFAULT.STRING_21 + index
    ] = item.hope.modelValue
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.NEEDASSISTANCETOPLANSTR + Or29241Const.DEFAULT.STRING_21 + index
    ] = item.needAssistanceToPlan.modelValue
  })

  //排泄のTableValue入力値処理
  excretionTemValues?.items.forEach((item, index) => {
    index++
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.FAMILYIMPLEMENTATIONSTR + Or29241Const.DEFAULT.STRING_25 + index
    ] = item.familyImplementation.modelValue
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.SERVERIMPLEMENTATIONSTR + Or29241Const.DEFAULT.STRING_25 + index
    ] = item.serverImplementation.modelValue
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.HOPESTR + Or29241Const.DEFAULT.STRING_25 + index
    ] = item.hope.modelValue
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.NEEDASSISTANCETOPLANSTR + Or29241Const.DEFAULT.STRING_25 + index
    ] = item.needAssistanceToPlan.modelValue
  })

  //外出のTableValue入力値処理
  goingOutTemValues?.items.forEach((item, index) => {
    index++
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.FAMILYIMPLEMENTATIONSTR + Or29241Const.DEFAULT.STRING_212 + index
    ] = item.familyImplementation.modelValue
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.SERVERIMPLEMENTATIONSTR + Or29241Const.DEFAULT.STRING_212 + index
    ] = item.serverImplementation.modelValue
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.HOPESTR + Or29241Const.DEFAULT.STRING_212 + index
    ] = item.hope.modelValue
    lifeFunctionInfoFormValuesObject[
      Or29241Const.DEFAULT.NEEDASSISTANCETOPLANSTR + Or29241Const.DEFAULT.STRING_212 + index
    ] = item.needAssistanceToPlan.modelValue
  })

  // 食事(主食 副食 摂取介助)checkboxGroupの入力値処理
  mealTemValues?.checkboxGroupValue.forEach((item, index) => {
    item.leftFormValues.forEach((leftItem, leftKey) => {
      lifeFunctionInfoFormValuesObject[
        Or29241Const.DEFAULT.CHECKBOXSTRARR_1[index][0] + (leftKey + 1)
      ] = leftItem.modelValue ? Or29241Const.DEFAULT.STRING_1 : Or29241Const.DEFAULT.STRING_0
    })
    item.rightFormValues.forEach((rightItem, rightKey) => {
      lifeFunctionInfoFormValuesObject[
        Or29241Const.DEFAULT.CHECKBOXSTRARR_1[index][1] + (rightKey + 1)
      ] = rightItem.modelValue ? Or29241Const.DEFAULT.STRING_1 : Or29241Const.DEFAULT.STRING_0
    })
    if (index < 2) {
      lifeFunctionInfoFormValuesObject[Or29241Const.DEFAULT.OTHERCURRENTSTR + (index + 1) + 'Knj'] =
        item.leftOtherInputValue?.value
      lifeFunctionInfoFormValuesObject[Or29241Const.DEFAULT.OTHERPLANSTR + (index + 1) + 'Knj'] =
        item.rightOtherInputValue?.value
    }
  })

  // 排泄checkboxGroupの入力値処理
  excretionTemValues?.checkboxGroupValue.forEach((item, index) => {
    item.leftFormValues.forEach((leftItem, leftKey) => {
      lifeFunctionInfoFormValuesObject[
        Or29241Const.DEFAULT.CHECKBOXSTRARR_2[index][0] + (leftKey + 1)
      ] = leftItem.modelValue ? Or29241Const.DEFAULT.STRING_1 : Or29241Const.DEFAULT.STRING_0
    })
    item.rightFormValues.forEach((rightItem, rightKey) => {
      lifeFunctionInfoFormValuesObject[
        Or29241Const.DEFAULT.CHECKBOXSTRARR_2[index][1] + (rightKey + 1)
      ] = rightItem.modelValue ? Or29241Const.DEFAULT.STRING_1 : Or29241Const.DEFAULT.STRING_0
    })
  })

  const lifeFunctionInfo = {
    ...lifeFunctionInfoFormValuesObject,
    /** 食堂までの段差 */
    shokuDansa: othersFormMealValues.othersMealSituationForm.diningRoomStepValue,
    /** 咀嚼の状況Radio */
    soshaku1: othersFormMealValues.othersMealSituationForm.chewingSituationValue,
    /** ｱ．その他text */
    shokuMemo1Knj: othersFormMealValues.othersMealSituationForm.othersMealLocationInputValue.value,
    /** ｴ．その他text */
    shokuMemo2Knj: othersFormMealValues.othersMealSituationForm.othersMealInputValue.value,
    /** カロリーtext */
    calorie: othersFormMealValues.othersMealSituationForm.diabeticDietInputValue.mo00045.value,
    /** グラムtext */
    gram: othersFormMealValues.othersMealSituationForm.highBloodPressureDietInputValue.mo00045
      .value,
    /** 尿意有無 */
    haisetuNyoui: othersFormExcretionValues.othersExcretionSituationForm.urgeToUrinateModelValue,
    /** 便意有無 */
    haisetuBeni: othersFormExcretionValues.othersExcretionSituationForm.urgeToDefecateModelValue,
    /** 特記1（食事） */
    memo1Knj: mealTemValues?.textareaValue?.value ?? '',
    /** 特記2（排泄） */
    memo2Knj: excretionTemValues?.textareaValue?.value ?? '',
    /** 特記3（外出） */
    memo3Knj: goingOutTemValues?.textareaValue?.value ?? '',
  }

  // 課題と目標リスト
  const changedIssuesAndGoalsList = OrX0209ModelValue?.twoWayBind?.value as OrX0209Type

  // 更新データ作成
  const inputData: AssessmentHome62InsertInEntity = {
    /** 法人ID */
    houjinId: systemCommonsStore.getHoujinId ?? '',
    /** 施設ID */
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    /** 利用者ID */
    userId: local.commonInfo.userId ?? '',
    /** 事業者ID */
    svJigyoId: local.commonInfo.jigyoId ?? '',
    /** 種別ID */
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    /** 更新区分 */
    updateKbn: updateKbn.value,
    /** 履歴更新区分 */
    historyUpdateKbn: local.commonInfo.historyUpdateKbn ?? '',
    /** 削除処理区分 */
    deleteProcessKbn: local.commonInfo.deleteKbn,
    /** 計画対象期間ID */
    sc1Id: local.commonInfo.sc1Id ?? '',
    /** アセスメントID */
    gdlId: local.commonInfo.gdlId ?? '',
    /** 作成日 */
    kijunbiYmd: local.commonInfo.createYmd ?? '',
    /** 作成者ID */
    sakuseiId: local.commonInfo.createUserId ?? '',
    /** 改定フラグ */
    ninteiFormF: local.commonInfo.ninteiFormF ?? '',
    /** 課題と目標リスト */
    kadaiList: [],
    tabId: Or29241Const.DEFAULT.TAB_ID,
    kinoId: systemCommonsStore.getFunctionId ?? '',
    krirekiNo: local.commonInfo.historyNo ?? '',
    edocumentUseParam: useCmnCom().getEDocumentParam(),
    edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
    kikanFlg: local.commonInfo.kikanKanriFlg ?? '',
    planningPeriodNo: local.commonInfo.sc1No ?? '',
    startYmd: local.commonInfo.periodStartYmd ?? '',
    endYmd: local.commonInfo.periodEndYmd ?? '',
    matomeFlg: cmnRouteCom.getInitialSettingMaster()?.gdlMatomeFlg ?? '',
    loginId: systemCommonsStore.getCurrentUser.loginId ?? '',
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    shokuId: local.commonInfo.createUserId ?? '',
    sysCd: systemCommonsStore.getSystemCode ?? '',
    svJigyoKnj: local.commonInfo.jigyoKnj ?? '',
    createUserName: local.commonInfo.createUserName ?? '',
    userName:
      systemCommonsStore.getUserSelectUserInfo()?.nameSei +
      ' ' +
      systemCommonsStore.getUserSelectUserInfo()?.nameMei,
  }
  // 課題と目標リスト作成
  changedIssuesAndGoalsList?.items.forEach((item) => {
    inputData.kadaiList.push({
      /** id */
      id: item.dataId,
      /** アセスメント番号 */
      assNo: item.assNo,
      /** 課題 */
      kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
      /** 長期 */
      choukiKnj: item.longtermGoal.value,
      /** 短期 */
      tankiKnj: item.shorttermGoal.value,
      /** 連番 */
      seq: item.seq?.toString(),
      /** 更新区分 */
      updateKbn: item.updateKbn,
    })
  })

  // 削除された課題を取得
  const delItems = local.commonInfo.issuesAndGoalsList?.filter(
    (item) => !changedIssuesAndGoalsList?.items.some((cKdai) => item.id === cKdai.id)
  )
  delItems?.forEach((item) => {
    inputData.kadaiList.push({
      /** id */
      id: item.dataId,
      /** アセスメント番号 */
      assNo: item.assNo,
      /** 課題 */
      kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
      /** 長期 */
      choukiKnj: item.longtermGoal.value,
      /** 短期 */
      tankiKnj: item.shorttermGoal.value,
      /** 連番 */
      seq: item.seq?.toString(),
      /** 更新区分 */
      updateKbn: UPDATE_KBN.DELETE,
    })
  })

  if (local.commonInfo.ninteiFormF === Or29241Const.DEFAULT.KAITEI_FLG_H21) {
    /** 生活機能情報（H21/４改訂版） */
    inputData.lifeFunction4Info = lifeFunctionInfo
  } else {
    /** 生活機能情報（R3/４改訂版） */
    inputData.lifeFunction5Info = lifeFunctionInfo
  }
  try {
    const resData: AssessmentHome62InsertOutEntity = await ScreenRepository.insert(
      'assessmentHomeTab62Insert',
      inputData
    )
    /****************************************
     * 保存成功の場合
     ****************************************/
    if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
      // 更新後データを取得し設定
      TeX0002Logic.data.set({
        uniqueCpId: props.parentUniqueCpId,
        value: {
          updateData: {
            errKbn: resData.data.errKbn,
            sc1Id: resData.data.sc1Id,
            gdlId: resData.data.gdlId,
          },
        },
      })
      isLoading.value = false
      return { saveStatus: true, sc1Id: resData.data.sc1Id, gdlId: resData.data.gdlId }
    }
    return { saveStatus: false }
  } catch (e) {
    console.log(e)

    isLoading.value = false
    return { saveStatus: false }
  }
}

/**
 *  画面データをクリア
 */
function clearData() {
  // 生活機能

  local.lifeFunction.careCertificationList.sectionList?.forEach((item) => {
    item.sectionItems.forEach((sItem) => {
      sItem.raidoValue = Or29241Const.DEFAULT.STRING_0
    })
  })

  //その他食事の現状And排泄の現状フォーム値
  setChildCpBinds(props.uniqueCpId, {
    [Or03080Const.CP_ID(1)]: {
      twoWayValue: {
        othersMealSituationForm: {
          mode: 0,
          mealLocationValue: [
            { modelValue: false },
            { modelValue: false },
            { modelValue: false },
            { modelValue: false },
            { modelValue: false },
          ],
          othersMealLocationInputValue: {
            value: '',
          },
          diningRoomStepValue: Or29241Const.DEFAULT.STRING_0,
          chewingSituationValue: Or29241Const.DEFAULT.STRING_0,
          chewingSituationCheckboxValue: [
            { modelValue: false },
            { modelValue: false },
            { modelValue: false },
          ],
          mealContentsCheckbox: [
            { modelValue: false },
            { modelValue: false },
            { modelValue: false },
            { modelValue: false },
            { modelValue: false },
          ],
          diabeticDietInputValue: {
            mo00045: {
              value: '',
            },
          },
          highBloodPressureDietInputValue: {
            mo00045: {
              value: '',
            },
          },
          othersMealInputValue: { value: '' },
        },
      },
    },
    [Or03080Const.CP_ID(2)]: {
      twoWayValue: {
        othersExcretionSituationForm: {
          urgeToUrinateModelValue: Or29241Const.DEFAULT.STRING_0,
          urgeToDefecateModelValue: Or29241Const.DEFAULT.STRING_0,
        },
      },
    },
  })

  const temMealData: Or03081TwoWayType = {
    items: local.or03081MealModelValue.items.map((item) => {
      return {
        ...item,
        familyImplementation: { modelValue: Or29241Const.DEFAULT.STRING_0 },
        serverImplementation: { modelValue: Or29241Const.DEFAULT.STRING_0 },
        hope: { modelValue: Or29241Const.DEFAULT.STRING_0 },
        needAssistanceToPlan: { modelValue: Or29241Const.DEFAULT.STRING_0 },
      }
    }),
    textareaValue: { value: '' },
    checkboxGroupValue: local.or03081MealModelValue.checkboxGroupValue.map((item) => {
      // checkboxGroupValueの各項目のleftFormValuesとrightFormValuesを初期化
      // leftFormValuesとrightFormValuesのmodelValueをfalseに設定
      // leftOtherInputValueとrightOtherInputValueのvalueを空文字に設定
      return {
        ...item,
        leftFormValues: item.leftFormValues.map((leftSubItem) => {
          return { ...leftSubItem, modelValue: false }
        }),
        rightFormValues: item.rightFormValues.map((rightSubItem) => {
          return { ...rightSubItem, modelValue: false }
        }),
        leftOtherInputValue: { value: '' },
        rightOtherInputValue: { value: '' },
      }
    }),
  }
  // 食事 Tableフォーム値処理です
  local.or03081MealModelValue = temMealData

  const temExcretionData: Or03081TwoWayType = {
    items: local.or03081ExcretionModelValue.items.map((item) => {
      return {
        ...item,
        familyImplementation: { modelValue: Or29241Const.DEFAULT.STRING_0 },
        serverImplementation: { modelValue: Or29241Const.DEFAULT.STRING_0 },
        hope: { modelValue: Or29241Const.DEFAULT.STRING_0 },
        needAssistanceToPlan: { modelValue: Or29241Const.DEFAULT.STRING_0 },
      }
    }),
    textareaValue: { value: '' },
    checkboxGroupValue: local.or03081ExcretionModelValue.checkboxGroupValue.map((item) => {
      // checkboxGroupValueの各項目のleftFormValuesとrightFormValuesを初期化
      // leftFormValuesとrightFormValuesのmodelValueをfalseに設定
      // leftOtherInputValueとrightOtherInputValueのvalueを空文字に設定
      return {
        ...item,
        leftFormValues: item.leftFormValues.map((leftSubItem) => {
          return { ...leftSubItem, modelValue: false }
        }),
        rightFormValues: item.rightFormValues.map((rightSubItem) => {
          return { ...rightSubItem, modelValue: false }
        }),
        leftOtherInputValue: { value: '' },
        rightOtherInputValue: { value: '' },
      }
    }),
  }
  //排泄等Tableフォーム値処理です
  local.or03081ExcretionModelValue = temExcretionData

  const temGoingOutData: Or03081TwoWayType = {
    items: local.or03081GoingOutModelValue.items.map((item) => {
      return {
        ...item,
        familyImplementation: { modelValue: Or29241Const.DEFAULT.STRING_0 },
        serverImplementation: { modelValue: Or29241Const.DEFAULT.STRING_0 },
        hope: { modelValue: Or29241Const.DEFAULT.STRING_0 },
        needAssistanceToPlan: { modelValue: Or29241Const.DEFAULT.STRING_0 },
      }
    }),
    textareaValue: { value: '' },
    checkboxGroupValue: [],
  }
  //外出 Tableフォーム値処理です
  local.or03081GoingOutModelValue = temGoingOutData
}

/**
 * Or35672イベント発火
 *
 * @param event - イベント
 */
function setOr35672Event(event: Record<string, boolean>) {
  Or35672Logic.event.set({
    uniqueCpId: Or35672Const.CP_ID(0) + props.parentUniqueCpId,
    events: event,
  })
}

/**
 * Or35672ステート発火
 *
 * @param state - ステート
 */
function setOr35672State(state: Record<string, boolean>) {
  Or35672Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 * Or35672ステート発火
 *
 * @param state - ステート
 */
function setTex0002State(state: Record<string, boolean>) {
  TeX0002Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**************************************************
 * ウォッチャー
 **************************************************/

/**
 *  保存イベント監視
 *
 */
watch(
  () => TeX0002Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    console.log('🚀 ~ newValue:', newValue)
    if (!newValue) return
    // 本画面が表示画面ではない場合、スキップ
    getCommonInfo(newValue.saveEventFlg)
    // 画面コントロールデータを設定
    setControlsData()
    if (local.commonInfo.activeTabId !== Or29241Const.DEFAULT.TAB_ID) return
    // 画面共通情報を取得

    if (newValue.isRefresh) {
      isComponentVisible.value = true
      // 画面情報再取得
      await reload()
    }
    if (newValue.isCreateDateChanged) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await reload()
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      if ((await onSave()).saveStatus) {
        setTex0002State({ isSaveCompleted: true })
      }
    }
    if (newValue.saveOnlyEventFlg) {
      // 保存のみの場合
      void onSave()
    }
    if (newValue.tabChangeSaveEventFlg) {
      // タブ変更保存の場合
      if ((await onSave()).saveStatus) {
        setTex0002State({ tabChangeSaveCompleted: true })
      }
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      createNew()
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      // 画面を非表示にする

      isComponentVisible.value = false
      updateKbn.value = UPDATE_KBN.DELETE
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      const copyData = local.commonInfo.copyData as assessmentHomeTab62SelectOutEntity
      setFormData(copyData)
    }
  },
  { deep: true, immediate: true }
)
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or35672Logic.event.get(Or35672Const.CP_ID(0) + props.parentUniqueCpId),
  async (newValue) => {
    if (!newValue?.reload) return

    setOr35672Event({ reload: false })

    // 複写画面から共通情報を取得
    getCommonInfoFromCopy()

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or29241Const.DEFAULT.TAB_ID) {
      return
    }

    // 画面再表示
    if (newValue.reload) {
      // 画面情報再取得
      await reload()
    }
  }
)
</script>

<template>
  <c-v-sheet
    v-show="isComponentVisible"
    :id="'Or29241Component' + props.uniqueCpId"
    class="background-color Or29241Component"
  >
    <v-overlay
      :model-value="isLoading"
      :persistent="true"
      class="align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      >
      </v-progress-circular>
    </v-overlay>
    <!-- 画面タイトル -->
    <div class="mb-6">
      <g-custom-orX0201 :oneway-model-value="localOneway.pageTitle" />
    </div>
    <c-v-row
      no-gutters
      class="contentArea"
    >
      <!-- 左です -->
      <c-v-col cols="12">
        <!-- ● 6-② 生活機能（食事・排泄等） -->
        <g-custom-or-x-0200
          v-bind="orX0200"
          :model-value="local.lifeFunction"
          :oneway-model-value="localOneway.lifeFunctionOnewayType"
          :common-info="local.commonInfo"
          :on-savetab-data="onSave"
        />
        <!-- 食事 -->
        <g-custom-or-03081
          v-bind="or03081_1"
          :model-value="local.or03081MealModelValue"
          :oneway-model-value="{ ...Or03081MealOnewayModelValue, commonInfo: local.commonInfo }"
          :parent-unique-cp-id="props.uniqueCpId"
        >
          <!-- その他食事の現状（6-②2-4関係） -->
          <template #or03080>
            <g-custom-or-03080 v-bind="or03080_1" />
          </template>
        </g-custom-or-03081>

        <!-- 排泄等 -->
        <g-custom-or-03081
          v-bind="or03081_2"
          :model-value="local.or03081ExcretionModelValue"
          :oneway-model-value="{
            ...Or03081ExcretionOnewayModelValue,
            commonInfo: local.commonInfo,
          }"
          :parent-unique-cp-id="props.uniqueCpId"
        >
          <template #or03080>
            <!-- その他排泄の状況（6-②2-5、2-6関係） -->
            <g-custom-or-03080 v-bind="or03080_2" />
          </template>
        </g-custom-or-03081>

        <!-- 外出 -->
        <g-custom-or-03081
          v-bind="or03081_3"
          :model-value="local.or03081GoingOutModelValue"
          :oneway-model-value="{
            ...Or03081GoingOutOnewayModelValue,
            commonInfo: local.commonInfo,
          }"
          :parent-unique-cp-id="props.uniqueCpId"
        />
      </c-v-col>
    </c-v-row>
    <!-- 線 -->
    <c-v-row
      no-gutters
      style="margin: 0 -24px"
      class="py-6"
    >
      <c-v-divider></c-v-divider>
    </c-v-row>
    <!-- フッター -->
    <c-v-row
      no-gutters
      class="pb-6"
    >
      <div
        ref="issuesAndGoalListRef"
        class="w-100"
      >
        <g-custom-or-x-0209
          v-bind="orX0209"
          :model-value="local.issuesAndGoalsList"
          :oneway-model-value="localOneway.issuesAndGoalsListOneway"
        />
      </div>
    </c-v-row>
  </c-v-sheet>
</template>
<style lang="scss" scoped>
.Or29241Component {
  width: 1080px;
}
.contentArea {
  background-color: #fff;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
</style>
