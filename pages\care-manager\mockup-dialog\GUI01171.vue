<script setup lang="ts">
import { computed,onMounted, reactive, ref } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or27943Const } from '~/components/custom-components/organisms/Or27943/Or27943.constants'
import { Or27943Logic } from '~/components/custom-components/organisms/Or27943/Or27943.logic'
import type { Or27943Type, Or27943OnewayType } from '~/types/cmn/business/components/Or27943Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**
 * GUI01171_横出しサービス単位再設定
 *
 * @description
 * 「横出しサービス単位再設定」画面を表示
 *
 * <AUTHOR> 劉顕康
 */

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01171'
// ルーティング
const routing = 'GUI01171/pinia'
// 画面物理名
const screenName = 'GUI01171'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27943 = ref({ uniqueCpId: Or27943Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01171' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27943Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27943.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01171',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27943Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27943Const.CP_ID(1)]: or27943.value,
})

// ダイアログ表示フラグ
const showDialogOr27943 = computed(() => {
  // Or27943のダイアログ開閉状態
  return Or27943Logic.state.get(or27943.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or27943) 項目区分=30
 */
function onClickOr27943() {
  // Or27943のダイアログ開閉状態を更新する
  Or27943Logic.state.set({
    uniqueCpId: or27943.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or27943Type = ref<Or27943Type>({})

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const or27943Data: Or27943OnewayType = {
  // システム年月
  systemDate: '2025/06',
  // 職員ID
  staffId: '101',
  // システムコード
  systemCode: '10',
  // 適用事業所IDリスト
  applicableOfficeIdList: ['1', '8', '9'],
}
const localClick = reactive({
  systemDate: { value: '1' } as Mo00045Type,
  staffId: { value: '1' } as Mo00045Type,
  systemCode: { value: '2025/06' } as Mo00045Type,
})
function onClick() {
  or27943Data.systemDate = localClick.systemDate.value
  or27943Data.staffId = localClick.staffId.value
  or27943Data.systemCode = localClick.systemCode.value
  updateObject()
  Or27943Logic.state.set({
    uniqueCpId: or27943.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const jsonInput = ref('');
onMounted(() => {
    jsonInput.value = JSON.stringify(or27943Data.applicableOfficeIdList, null, 2);
})
const updateObject = () => {
  const parsed = JSON.parse(jsonInput.value) as string[]
  if (parsed) {
    or27943Data.applicableOfficeIdList = parsed;
  }
};
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27943"
        >GUI01171_［横出しサービス単位再設定］画面
      </v-btn>
      <g-custom-or-27943
        v-if="showDialogOr27943"
        v-bind="or27943"
        v-model="or27943Type"
        :oneway-model-value="or27943Data"
      />
    </c-v-col>
  </c-v-row>
      <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">システム年月</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localClick.systemDate"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">職員ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localClick.staffId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">システムコード</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localClick.systemCode"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="json-input-area">
        <h3>適用事業所IDリスト</h3>
        <textarea
          v-model="jsonInput"
          placeholder="JSONを入力してください"
          rows="9"
        ></textarea>
      </div>
  <div class="pl-2 pt-5">
    <v-btn @click="onClick()"> GUI01171 疎通起動 </v-btn>
  </div>
</template>
<style>
.json-input-area textarea {
  width: 200px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: monospace;
}

.update-btn {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.update-btn:hover {
  background-color: #359e75;
}
</style>
