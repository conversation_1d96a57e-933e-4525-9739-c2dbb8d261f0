<script setup lang="ts">
/**
 * Or28754:有機体:印刷設定モーダル
 * GUI01115_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import type { OrX0130TableType } from '../OrX0130/OrX0130.type'
import type { OrX0143TableData } from '../OrX0143/OrX0143.type'
import { OrX0143Logic } from '../OrX0143/OrX0143.logic'
import { Or28780Const } from '../Or28780/Or28780.constants'
import { Or28780Logic } from '../Or28780/Or28780.logic'
import { Or18615Const } from '../Or18615/Or18615.constants'
import { Or18615Logic } from '../Or18615/Or18615.logic'
import { Or26331Const } from '../Or26331/Or26331.constants'
import { Or26331Logic } from '../Or26331/Or26331.logic'
import { Or26326Const } from '../Or26326/Or26326.constants'
import { Or26328Const } from '../Or26328/Or26328.constants'
import { Or26328Logic } from '../Or26328/Or26328.logic'
import { Or10016Logic } from '../Or10016/Or10016.logic'
import { Or10016Const } from '../Or10016/Or10016.constants'
import { Or10148Logic } from '../Or10148/Or10148.logic'
import { Or10148Const } from '../Or10148/Or10148.constants'
import { Or28754Const } from '../Or28754/Or28754.constants'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import type { HistoryInfo, Or28754StateType, RevisionType } from './Or28754.type'
import { useScreenOneWayBind, useSetupChildProps, useScreenUtils, useValidation } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CustomClass } from '~/types/CustomClassType'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { OrX0143OnewayType } from '~/types/cmn/business/components/OrX0143Type'
import { OrX0143Const } from '~/components/custom-components/organisms/OrX0143/OrX0143.constants'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import type { InWebEntity } from '@/repositories/AbstructWebRepository'
import { reportOutputType } from '~/utils/useReportUtils'
import type { IPrintInfo, PrintOnewayEntity } from '~/repositories/cmn/entities/PrintSelectEntity'
import type { PrintCloseUpdateEntity } from '~/repositories/cmn/entities/PrintCloseUpdateEntity'
import type { PrintUserChangeSelectInEntity } from '~/repositories/cmn/entities/PrintUserChangeSelectEntity'
import type {
  PrintSubjectSelectInEntity,
  PrtHistoryInfo,
} from '~/repositories/cmn/entities/PrintSubjectSelectEntity'
import { usePrint } from '~/utils/usePrint'
import type { IInitMasterInfo } from '~/repositories/cmn/entities/GyoumuComSelectEntity'
import type { Or26328OnewayType } from '~/types/cmn/business/components/Or26328Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import type {
  KikanRirekiData,
  PreventionPlanPrintSettingsInitUpdateInEntity,
  PreventionPlanPrintSettingsInitUpdateOutEntity,
  SysIniInfoList,
} from '~/repositories/cmn/entities/PreventionPlanPrintSettingsInitUpdateEntity'
import type { Or10148Type } from '~/types/cmn/business/components/Or10148Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { PreventionPlanPrintSettingsUserChangeSelectEntityOutEntity } from '~/repositories/cmn/entities/PreventionPlanPrintSettingsUserChangeSelectEntity'
import type { PreventionPlanPrintSettingsSubjectSelectOutEntity } from '~/repositories/cmn/entities/PreventionPlanPrintSettingsSubjectSelectEntity'
import type { PreventionPlanInitMasterInfo } from '~/repositories/cmn/entities/PreventionPlanSelectEntity'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
const { byteLength } = useValidation()

/************************************************
 * Props
 ************************************************/
interface Props<T extends IInitMasterInfo> {
  onewayModelValue: PrintOnewayEntity<T>
  uniqueCpId: string
}

const props = defineProps<Props<PreventionPlanInitMasterInfo>>()

// 引継情報を取得する
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const orX0130 = ref({ uniqueCpId: OrX0130Const.CP_ID(0) })
const orX0117 = ref({ uniqueCpId: OrX0117Const.CP_ID(0) })
const orX0143 = ref({ uniqueCpId: OrX0143Const.CP_ID(0) })
const orX0145 = ref({ uniqueCpId: OrX0145Const.CP_ID(0) })
const or28780 = ref({ uniqueCpId: Or28780Const.CP_ID(0) })
const or18615 = ref({ uniqueCpId: Or18615Const.CP_ID(0) })
const or26331 = ref({ uniqueCpId: Or26331Const.CP_ID(0) })
const or26326 = ref({ uniqueCpId: Or26326Const.CP_ID(0) })
const or26328 = ref({ uniqueCpId: Or26328Const.CP_ID(0) })
const or10016 = ref({ uniqueCpId: Or10016Const.CP_ID(0) })
const or10148 = ref({ uniqueCpId: Or10148Const.CP_ID(0) })
const or26328Ref = ref<{
  isValid: () => Promise<boolean>
}>()
const or18615Ref = ref<{
  isValid: () => Promise<boolean>
}>()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// 印刷共通処理
const printCom = usePrint()
// 印刷権限
const prtFlg: boolean = await hasPrintAuth()

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0143Const.CP_ID(0)]: orX0143.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [OrX0145Const.CP_ID(0)]: orX0145.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [Or28780Const.CP_ID(0)]: or28780.value,
  [Or18615Const.CP_ID(0)]: or18615.value,
  [Or26331Const.CP_ID(0)]: or26331.value,
  [Or26326Const.CP_ID(0)]: or26326.value,
  [Or26328Const.CP_ID(0)]: or26328.value,
  [Or10016Const.CP_ID(0)]: or10016.value,
  [Or10148Const.CP_ID(0)]: or10148.value,
})

// ダイアログ表示フラグ
const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr10016 = computed(() => {
  // Or10016のダイアログ開閉状態
  return Or10016Logic.state.get(or10016.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr10148 = computed(() => {
  return Or10148Logic.state.get(or10148.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
// 利用者列幅
const userCols = ref<number>(4)
// 履歴一覧セクションフラゲ
const mo01334TypeHistoryFlag = ref<boolean>(false)
// 選択した利用者データ
const selectedUserList = ref<OrX0130TableType[]>([])
// 選択した履歴データ
const selectedRirekiData = ref<KikanRirekiData[]>([])
// 個人情報を印刷しない表示フラグ
const kojinhogoDisplayFlg = ref<boolean>(false)

// ローカル双方向bind
const local = reactive({
  or28754: {
    // 印刷設定情報リスト
    prtList: [] as IPrintInfo[],
    // 期間履歴情報リスト
    kikanRirekiList: [] as KikanRirekiData[],
    // 期間管理フラグ
    kikanFlag: '',
    // システムINI情報
    sysIniInfoList: [] as SysIniInfoList[],
  },
  // 帳票タイトル
  mo00040: { modelValue: '' } as Mo00040Type,
  // 出力帳票名一覧
  mo01334: {
    value: '',
    values: [],
  } as Mo01334Type,
  // 敬称テキストボックス
  textInput: {
    value: '',
  } as Mo00045Type,
  // 敬称を変更する
  mo00039Type: '',
  // 指定日
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  // 基準日
  mo00020TypeKijunbi: {
    value: systemCommonsStore.getSystemDate!,
  } as Mo00020Type,
  // 帳票タイトル
  titleInput: {
    value: '',
  } as Mo00045Type,
  // 敬称を変更する
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  // 記入用シートを印刷する
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  // 支援計画の開始位置を調整する
  mo00018TypeRepositionStartPoint: {
    modelValue: false,
  } as Mo00018Type,
  // 必要な事業プログラムに○印をつける
  mo00018TypeCheckPrograms: {
    modelValue: false,
  } as Mo00018Type,
  // 総合的課題〜目標の下線を印刷しない
  mo00018TypeSuppressUnderlining: {
    modelValue: false,
  } as Mo00018Type,
  // 期間が空白の場合省略する
  mo00018TypeSkipBlankPeriod: {
    modelValue: false,
  } as Mo00018Type,
  // 介護予防ケアマネジメント様式で印刷する
  mo00018TypePrintInPreventiveCare: {
    modelValue: false,
  } as Mo00018Type,
  // 印刷確認、押印欄を印刷しない
  mo00018TypePrintConfirmation: {
    modelValue: false,
  } as Mo00018Type,
  // タイトルを変更する
  mo00018TypePrintTitleChange: {
    modelValue: false,
  } as Mo00018Type,
  // タイトル
  title: {
    value: '',
  } as Mo00045Type,
  // 個人情報を印刷しない
  mo00018TypeKojinhogo: {
    modelValue: false,
  } as Mo00018Type,
  // 同意情報を印刷する
  mo00018TypePrintAgreementInfo: {
    modelValue: false,
  } as Mo00018Type,
  // 同意欄の登録
  or10148: {
    // 帳票INI
    ledgerINI: {
      parameter05: '',
      parameter06: '',
      parameter07: '',
      parameter08: '',
      parameter09: '',
      parameter10: '',
    },
  } as Or10148Type,
  orX0145: {
    value: {} as TantoCmnShokuin,
  } as OrX0145Type,
})

// 担当ケアマネ
local.orX0145.value = {
  counter: Or28754Const.EMPTY,
  chkShokuId: Or28754Const.EMPTY,
  houjinId: Or28754Const.EMPTY,
  shisetuId: Or28754Const.EMPTY,
  svJigyoId: Or28754Const.EMPTY,
  shokuin1Kana: Or28754Const.EMPTY,
  shokuin2Kana: Or28754Const.EMPTY,
  shokuin1Knj: Or28754Const.EMPTY,
  shokuin2Knj: Or28754Const.EMPTY,
  sex: Or28754Const.EMPTY,
  birthdayYmd: Or28754Const.EMPTY,
  zip: Or28754Const.EMPTY,
  kencode: Or28754Const.EMPTY,
  citycode: Or28754Const.EMPTY,
  areacode: Or28754Const.EMPTY,
  addressKnj: Or28754Const.EMPTY,
  tel: Or28754Const.EMPTY,
  kaikeiId: Or28754Const.EMPTY,
  kyuyoKbn: Or28754Const.EMPTY,
  partKbn: Or28754Const.EMPTY,
  inYmd: Or28754Const.EMPTY,
  outYmd: Or28754Const.EMPTY,
  shozokuId: Or28754Const.EMPTY,
  shokushuId: Or28754Const.EMPTY,
  shokuId: Or28754Const.EMPTY,
  timeStmp: Or28754Const.EMPTY,
  delFlg: Or28754Const.EMPTY,
  shokuNumber: Or28754Const.EMPTY,
  caremanagerKbn: Or28754Const.EMPTY,
  shokuType1: Or28754Const.EMPTY,
  shokuType2: Or28754Const.EMPTY,
  kGroupid: Or28754Const.EMPTY,
  bmpPath: Or28754Const.EMPTY,
  bmpYmd: Or28754Const.EMPTY,
  hankoPath: Or28754Const.EMPTY,
  kojinPath: Or28754Const.EMPTY,
  keitaitel: Or28754Const.EMPTY,
  eMail: Or28754Const.EMPTY,
  senmonNo: Or28754Const.EMPTY,
  sgfFlg: Or28754Const.EMPTY,
  srvSekiKbn: Or28754Const.EMPTY,
  shokushuId2: Or28754Const.EMPTY,
  shokushuId3: Or28754Const.EMPTY,
  shokushuId4: Or28754Const.EMPTY,
  shokushuId5: Or28754Const.EMPTY,
  shikakuId1: Or28754Const.EMPTY,
  shikakuId2: Or28754Const.EMPTY,
  shikakuId3: Or28754Const.EMPTY,
  shikakuId4: Or28754Const.EMPTY,
  shikakuId5: Or28754Const.EMPTY,
  kyuseiFlg: Or28754Const.EMPTY,
  kyuseiKana: Or28754Const.EMPTY,
  kyuseiKnj: Or28754Const.EMPTY,
  sort: Or28754Const.EMPTY,
  selfNumber: Or28754Const.EMPTY,
  ichiranShokushuIdNm: Or28754Const.EMPTY,
  shokushuId2Nm: Or28754Const.EMPTY,
  shokushuId3Nm: Or28754Const.EMPTY,
  shokushuId4Nm: Or28754Const.EMPTY,
  shokushuId5Nm: Or28754Const.EMPTY,
  stopFlg: Or28754Const.EMPTY,
  shokuinKnj: '',
  shokuinKana: Or28754Const.EMPTY,
  title: Or28754Const.EMPTY,
  value: Or28754Const.EMPTY,
} as TantoCmnShokuin

// ローカルOneway
const localOneway = reactive({
  // 利用者選択
  orX0130Oneway: {
    selectMode: Or28754Const.DEFAULT.TANI,
    tableStyle: 'width:270px',
  } as OrX0130OnewayType,
  // 期間履歴
  orX0143Oneway: {
    // 期間管理フラグ
    kikanFlg: '1',
    // 単一複数フラグ(0:単一,1:複数)
    singleFlg: Or28754Const.DEFAULT.TANI,
    tableStyle: 'width:335px',
    kikanRirekiTitleList: [
      // 作成日
      { title: 'create-date', width: '120', key: 'createYmd' },
      // センター職員
      { title: 'center-staff', width: '121', key: 'centerShokuKnj' },
      // 委託先職員
      { title: 'contractor-staff', width: '121', key: 'itakuShokuKnj' },
    ],
    // 履歴情報
    rirekiList: [] as OrX0143TableData[],
  } as OrX0143OnewayType,
  // 主治医意見書の改定フラグ
  revisionOneway: {} as RevisionType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  // 印刷ボタン
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  // 日付印刷
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  // 利用者選択
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  // 履歴選択
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  Or28754: {
    ...props.onewayModelValue,
  },
  // 敬称を変更するチェックボックス
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 敬称テキストボックス
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '65',
    maxLength: '4',
  } as Mo00045OnewayType,
  // 支援計画の開始位置を調整する
  mo00018OneWayRepositionStartPoint: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.reposition-the-support-plans-start-point'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 必要な事業プログラムに○印をつける
  mo00018OneWayCheckPrograms: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.check-the-applicable-business-programs'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 総合的課題〜目標の下線を印刷しない
  mo00018OneWaySuppressUnderlining: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.suppress-underlining-for-integrated-tasks-goals'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 期間が空白の場合省略する
  mo00018OneWaySkipBlankPeriod: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.skip-if-period-is-blank'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 介護予防ケアマネジメント様式で印刷する
  mo00018OneWayPrintInPreventiveCare: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-care-prevention'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 印刷確認、押印欄を印刷しない
  mo00018OneWayPrintConfirmation: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-confirmation-do-not-print-the-seal-field'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // タイトルを変更する
  mo00018OneWayPrintTitleChange: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-title-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // タイトル
  mo00045Title: {
    class: 'text-filed',
    showItemLabel: false,
    customClass: {
      outerClass: 'mr-0',
    },
    // 最大文字数 128
    maxLength: '128',
    rules: [byteLength(128)],
  } as Mo00045OnewayType,
  // 記入日を印刷するチェックボックス
  mo00018OneWayPrintDate: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-entry-date'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 記入用シートを印刷するチェックボックス
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 印刷設定帳票出力状態リスト
  orX0117Oneway: {
    reportId: '',
    type: '1',
    reportData: {} as InWebEntity,
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    width: 'auto',
    maxWidth: '1435px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  // チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: '',
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
  // 出力帳票名一覧
  mo01334Oneway: {
    headers: [
      {
        title: t('label.report'),
        key: 'prtTitle',
        sortable: false,
        minWidth: '100',
      },
    ],
    items: [],
    height: 655,
  } as Mo01334OnewayType,
  // 出力帳票名一覧
  or26328Oneway: {
    // 最大文字数 128
    maxLength: '128',
  } as Or26328OnewayType,
  // 印鑑欄ボタン
  mo00610OneWay: {
    btnLabel: t('btn.seal-column'),
    disabled: false,
  } as Mo00610OnewayType,
  // GUI01110［印鑑欄設定］画面
  or10016Oneway: {} as Or10016OnewayType,
  // 個人情報を印刷しない
  mo00018OneWayKojinhogo: {
    checkboxLabel: t('label.print-kojinhogo'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 同意情報を印刷する
  mo00018OneWayPrintAgreementInfo: {
    checkboxLabel: t('label.print-consent-information'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 同意欄の登録ボタン
  mo00611PrintAgreementInfo: {
    btnLabel: t('btn.consent-column-registration'),
  } as Mo00611OnewayType,
  // 担当ケアマネ
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
})

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28754Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<Or28754StateType>({
  cpId: Or28754Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28754Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子モジュールに値を渡す
setChildrenValue()

/**
 * 「印鑑欄ボタン」押下:GUI01110［印鑑欄設定］画面をポップアップで起動する
 */
function openGUI01110() {
  // 親画面.法人ID
  localOneway.or10016Oneway.houjinId = localOneway.Or28754.houjinId
  // 親画面.施設ID
  localOneway.or10016Oneway.shisetsuId = localOneway.Or28754.shisetuId
  // 親画面.事業所ID
  localOneway.or10016Oneway.jigyoshoId = localOneway.Or28754.svJigyoId
  // 帳票セクション番号:"3GKE00400P001"
  localOneway.or10016Oneway.reportSectionNumber = '3GKE00400P001'
  // 会議録フラグ:FALSE
  localOneway.or10016Oneway.conferenceFlag = false
  // ケアプラン方式:親画面.初期設定マスタの情報.ケアプラン方式
  localOneway.or10016Oneway.assessment = localOneway.Or28754.initMasterObj.cpnFlg
  // Or10016のダイアログ開閉状態を更新する
  Or10016Logic.state.set({
    uniqueCpId: or10016.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * エラーダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 *
 * @param isError - エラーかどうか
 *
 * @returns ダイアログの選択結果（yes）
 */
const showMessageBox = async (text: string, btn: string, isError = true) => {
  if (isError) {
    return await openErrorDialog(text, btn)
  } else {
    return await openInfoDialog(text)
  }
}

/**
 * ダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 */
function openErrorDialog(text: string, btn: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t(text),
      firstBtnType: 'normal1',
      firstBtnLabel: t(btn) ?? t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)
        let result = Or28754Const.DEFAULT.YES

        if (event?.firstBtnClickFlg) {
          result = Or28754Const.DEFAULT.YES
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログの開閉
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes）
 */
async function openInfoDialog(paramDialogText: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t(paramDialogText),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

onMounted(async () => {
  // 指定日 システム日付
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  // 汎用コードマスタデータを取得し初期化
  await initCodes()
  // 印刷設定情報リスト取得
  await getPrintSettingList()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  // 利用者選択
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 履歴選択
  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 選択モート 初期値
  localOneway.orX0130Oneway.selectMode = Or28754Const.DEFAULT.TANI
  // 選択モート 単一複数フラグ
  localOneway.orX0143Oneway.singleFlg = Or28754Const.DEFAULT.TANI
  // 履歴一覧セクションフラグ
  mo01334TypeHistoryFlag.value = true
  // 利用者列幅
  userCols.value = 6
}

/**
 * 印刷オプションの有効化設定
 */
function setPrintOptions() {
  // 画面.利用者選択方法が「単一」 、且つ、 画面.履歴選択方法が「単一」の場合、活性
  if (
    localOneway.orX0130Oneway.selectMode === Or28754Const.DEFAULT.TANI &&
    localOneway.orX0143Oneway.singleFlg === Or28754Const.DEFAULT.TANI
  ) {
    // 記入用シートを印刷する
    localOneway.mo00018OneWayPrintTheForm.disabled = false
  }
  // 以外の場合、非活性
  else {
    localOneway.mo00018OneWayPrintTheForm.disabled = true
  }
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: PreventionPlanPrintSettingsInitUpdateInEntity = {
    // セクション名:親画面.セクション名
    sectionName: localOneway.Or28754.sectionName,
    // 職員ID:親画面.職員ID
    shokuId: localOneway.Or28754.shokuId,
    // 法人ID:親画面.法人ID
    houjinId: localOneway.Or28754.houjinId,
    // 施設ID:親画面.施設ID
    shisetuId: localOneway.Or28754.shisetuId,
    // 事業所ID:親画面.事業所ID
    svJigyoId: localOneway.Or28754.svJigyoId,
    // システムコード:共通情報.システムコード
    gsyscd: localOneway.Or28754.systemCode,
    // 利用者ID:親画面.利用者ID
    userId: localOneway.Or28754.userId,
    // 計画表様式:親画面.初期設定マスタの情報.計画表様式
    itakuKkakPrtFlg: localOneway.Or28754.initMasterObj.itakuKkakPrtFlg,
  }

  // バックエンドAPIから初期情報取得
  const ret = await printCom.doPrintGet<
    PreventionPlanPrintSettingsInitUpdateInEntity,
    IPrintInfo,
    KikanRirekiData,
    PreventionPlanPrintSettingsInitUpdateOutEntity
  >(inputData, 'preventionPlanPrintSettingsInitUpdate')
  // 印刷設定情報リスト、期間履歴情報リスト、期間管理フラグを取得
  printCom.doGetPrintData<
    PreventionPlanPrintSettingsInitUpdateOutEntity,
    IPrintInfo,
    KikanRirekiData
  >(ret, local.or28754)
  // 印刷設定情報リスト
  local.or28754.prtList = [local.or28754.prtList[0]]
  // システムINI情報
  local.or28754.sysIniInfoList = [ret.data.sysIniInfoList[0]]
  // 期間履歴情報リスト
  local.or28754.kikanRirekiList = ret.data.kikanRirekiList
  // Onewayの期間管理フラグを設定
  localOneway.orX0143Oneway.kikanFlg = localOneway.Or28754.kikanFlg
  // 履歴一覧情報
  localOneway.orX0143Oneway.rirekiList = local.or28754.kikanRirekiList
  // 印刷設定情報リストの1件目を設定する
  const prtData = local.or28754.prtList[0]
  // 選択用の利用者IDに親画面.利用者IDを設定する
  localOneway.orX0130Oneway.userId = localOneway.Or28754.userId
  // 個人情報を印刷しない
  local.mo00018TypeKojinhogo.modelValue =
    ret.data.sysIniInfoList[0].kojinhogoFlg === Or28754Const.DEFAULT.CHECK_ON
  kojinhogoDisplayFlg.value =
    local.or28754.sysIniInfoList[0].kojinhogoDisplayFlg === Or28754Const.DEFAULT.CHECK_ON
  // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
  local.titleInput.value = prtData.prtTitle
  // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
  local.mo00039Type = prtData.prnDate
  // 指定日
  local.mo00020Type = {
    value: systemCommonsStore.getSystemDate!,
  } as Mo00020Type
  // 画面.敬称を変更する = 一件目の印刷設定情報リスト.パラメータ03
  local.mo00018TypeChangeTitle.modelValue = prtData?.param03 === Or28754Const.DEFAULT.CHECK_ON
  // 画面.敬称 = 一件目の印刷設定情報リスト.パラメータ04
  local.textInput.value = prtData?.param04
  // 画面.記入用シートを印刷する = 0(チェックオフ)
  local.mo00018TypePrintTheForm.modelValue = false
  // 画面.同意情報を印刷する = 一件目の印刷設定情報リスト.パラメータ12
  local.mo00018TypePrintAgreementInfo.modelValue =
    prtData?.param12 === Or28754Const.DEFAULT.CHECK_ON
  // 画面.支援計画の開始位置を調整する = 一件目の印刷設定情報リスト.パラメータ14
  local.mo00018TypeRepositionStartPoint.modelValue =
    prtData?.param14 === Or28754Const.DEFAULT.CHECK_ON
  // 画面.必要な事業プログラムに○印をつける = 一件目の印刷設定情報リスト.パラメータ13
  local.mo00018TypeCheckPrograms.modelValue = prtData?.param13 === Or28754Const.DEFAULT.CHECK_ON
  // 画面.総合的課題〜目標の下線を印刷しない = 一件目の印刷設定情報リスト.パラメータ15
  local.mo00018TypeSuppressUnderlining.modelValue =
    prtData?.param15 === Or28754Const.DEFAULT.CHECK_ON
  // 画面.期間が空白の場合省略する = 一件目の印刷設定情報リスト.パラメータ16
  local.mo00018TypeSkipBlankPeriod.modelValue = prtData?.param16 === Or28754Const.DEFAULT.CHECK_ON
  // 画面.介護予防ケアマネジメント様式で印刷する = 一件目の印刷設定情報リスト.パラメータ17
  local.mo00018TypePrintInPreventiveCare.modelValue =
    prtData?.param17 === Or28754Const.DEFAULT.CHECK_ON
  // 画面.タイトルを変更する = 一件目の印刷設定情報リスト.パラメータ18
  local.mo00018TypePrintTitleChange.modelValue = prtData?.param18 === Or28754Const.DEFAULT.CHECK_ON
  // 画面.タイトル = 一件目の印刷設定情報リスト.パラメータ19
  local.title.value = prtData?.param19
  // 印刷確認、押印欄を印刷しない = 一件目の印刷設定情報リスト.パラメータ20
  local.mo00018TypePrintConfirmation.modelValue = prtData?.param20 === Or28754Const.DEFAULT.CHECK_ON
  // パラメータ07 = 固定値：-12を設定
  prtData.param07 = Or28754Const.FIXED_VALUE_12
  // パラメータ08 = 固定値：-12を設定
  prtData.param08 = Or28754Const.FIXED_VALUE_12
  // 画面.基準日
  local.mo00020TypeKijunbi.value = systemCommonsStore.getSystemDate!
  // 出力帳票一覧に印刷設定情報リストを設定する
  const mo01334OnewayList: Mo01334Items[] = []
  // 印刷設定情報リスト
  for (const item of local.or28754.prtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }
  // 出力帳票一覧に印刷設定情報リストを設定する
  localOneway.mo01334Oneway.items = mo01334OnewayList

  // 印刷設定情報リストの1件目を設定する
  local.mo01334.value = prtData.prtNo

  // 印刷オプションの有効化設定
  setPrintOptions()
  // 子モジュールに値を渡す
  setChildrenValue()
}

/**
 * 印刷設定情報リストの値を設定する
 *
 */
function setPrtList() {
  // 印刷設定情報リストの1件目を設定する
  const selectedData = local.or28754.prtList.at(0)!
  // システム初期情報リストの1件目を設定する
  const selectedSysIniData = local.or28754.sysIniInfoList.at(0)!
  // 日付表示有無
  selectedData.prnDate = local.mo00039Type
  // 個人情報表示フラグ
  selectedSysIniData.kojinhogoFlg = local.mo00018TypeKojinhogo.modelValue
    ? Or28754Const.DEFAULT.CHECK_ON
    : Or28754Const.DEFAULT.CHECK_OFF
  // 帳票タイトル
  selectedData.prtTitle = local.titleInput.value
  // 日付表示有無
  selectedData.prnDate = local.mo00039Type
  // 敬称を変更する
  selectedData.param03 = local.mo00018TypeChangeTitle.modelValue
    ? Or28754Const.DEFAULT.CHECK_ON
    : Or28754Const.DEFAULT.CHECK_OFF
  // 敬称
  selectedData.param04 = local.textInput.value
  // 同意情報を印刷する
  selectedData.param12 = local.mo00018TypePrintAgreementInfo.modelValue
    ? Or28754Const.DEFAULT.CHECK_ON
    : Or28754Const.DEFAULT.CHECK_OFF
  // 支援計画の開始位置を調整する
  selectedData.param14 = local.mo00018TypeRepositionStartPoint.modelValue
    ? Or28754Const.DEFAULT.CHECK_ON
    : Or28754Const.DEFAULT.CHECK_OFF
  // 必要な事業プログラムに○印をつける
  selectedData.param13 = local.mo00018TypeCheckPrograms.modelValue
    ? Or28754Const.DEFAULT.CHECK_ON
    : Or28754Const.DEFAULT.CHECK_OFF
  // 総合的課題〜目標の下線を印刷しない
  selectedData.param15 = local.mo00018TypeSuppressUnderlining.modelValue
    ? Or28754Const.DEFAULT.CHECK_ON
    : Or28754Const.DEFAULT.CHECK_OFF
  // 期間が空白の場合省略する
  selectedData.param16 = local.mo00018TypeSkipBlankPeriod.modelValue
    ? Or28754Const.DEFAULT.CHECK_ON
    : Or28754Const.DEFAULT.CHECK_OFF
  // 介護予防ケアマネジメント様式で印刷する
  selectedData.param17 = local.mo00018TypePrintInPreventiveCare.modelValue
    ? Or28754Const.DEFAULT.CHECK_ON
    : Or28754Const.DEFAULT.CHECK_OFF
  // タイトルを変更する
  selectedData.param18 = local.mo00018TypePrintTitleChange.modelValue
    ? Or28754Const.DEFAULT.CHECK_ON
    : Or28754Const.DEFAULT.CHECK_OFF
  // タイトル
  selectedData.param19 = local.title.value
  // 印刷確認、押印欄を印刷しない
  selectedData.param20 = local.mo00018TypePrintConfirmation.modelValue
    ? Or28754Const.DEFAULT.CHECK_ON
    : Or28754Const.DEFAULT.CHECK_OFF
}

/**
 * 履歴情報を取得する
 *
 * @param userId - 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  // 履歴リストを初期化
  localOneway.orX0143Oneway.rirekiList = []
  const ret = await printCom.doUserClick<
    PrintUserChangeSelectInEntity,
    KikanRirekiData,
    PreventionPlanPrintSettingsUserChangeSelectEntityOutEntity
  >(
    {
      // 事業所ＩＤ:親画面.事業所ＩＤ
      svJigyoId: localOneway.Or28754.svJigyoId,
      // 利用者ID:画面.利用者一覧に選択行の利用者ID
      userId: userId,
    },
    'preventionPlanPrintSettingsUserChangeSelect'
  )
  // 週間表履歴リスト
  localOneway.orX0143Oneway.rirekiList = ret
}

/**
 * 子モジュールに値を渡す
 *
 */
function setChildrenValue() {
  setChildCpBinds(props.uniqueCpId, {
    // 帳票タイトル
    [Or26328Const.CP_ID(0)]: {
      twoWayValue: {
        titleInput: local.titleInput,
      },
    },
    // 出力帳票名一覧
    [Or26326Const.CP_ID(0)]: {
      twoWayValue: {
        mo01334Type: local.mo01334,
      },
    },
    // 日付印刷
    [Or28780Const.CP_ID(0)]: {
      twoWayValue: {
        mo00039Type: local.mo00039Type,
        mo00020Type: local.mo00020Type,
      },
    },
    // 印刷オプション
    [Or18615Const.CP_ID(0)]: {
      twoWayValue: {
        mo00018TypeChangeTitle: local.mo00018TypeChangeTitle,
        mo00045Type: local.textInput,
      },
    },
    // 利用者選択、基準日、履歴選択
    [Or26331Const.CP_ID(0)]: {
      twoWayValue: {
        mo00039OneWayUserSelectType: localOneway.orX0130Oneway.selectMode,
        mo00020TypeKijunbi: local.mo00020TypeKijunbi,
        mo00039OneWayHistorySelectType: localOneway.orX0143Oneway.singleFlg,
      },
    },
  })
}

/**
 * 「閉じるボタン」押下
 */
async function close() {
  const isValid = await isValidation()
  if (!isValid) {
    return false
  }
  setPrtList()
  // 画面の印刷設定情報を保存する
  await printCom.doPrintClose(
    local.titleInput,
    {
      // セクション名:親画面.セクション名
      sectionName: localOneway.Or28754.sectionName,
      // システムコード：親画面.システムコード
      gsyscd: localOneway.Or28754.systemCode,
      // 職員ID：親画面.職員ID
      shokuId: localOneway.Or28754.shokuId,
      // 法人ID：親画面.法人ID
      houjinId: localOneway.Or28754.houjinId,
      // 施設ID：親画面.施設ID
      shisetuId: localOneway.Or28754.shisetuId,
      // 事業者ID：親画面.事業者ID
      svJigyoId: localOneway.Or28754.svJigyoId,
      // 印刷設定情報リスト：印刷設定情報リスト
      prtList: [local.or28754.prtList[0]],
      // システムINI情報リスト
      sysIniInfoList: [
        { ...local.or28754.sysIniInfoList[0], profile: local.or28754.prtList[0].profile },
      ],
    },
    'preventionPlanPrintSettingsInfoUpdate',
    localOneway.mo01334Oneway.items,
    local.mo01334.value,
    props.uniqueCpId,
    showMessageBox,
    closeDialog
  )
}

/**
 * 本画面を閉じる
 */
function closeDialog() {
  setState({ isOpen: false })
}

/**
 * 「印刷」ボタン押下
 */
async function print() {
  const isValid = await isValidation()
  if (!isValid) {
    return false
  }
  // 業務チェックを行う
  if (
    !printCom.doCheckBeforePrint(
      local.titleInput.value,
      local.mo00018TypePrintTheForm.modelValue,
      selectedUserList.value.length,
      localOneway.orX0143Oneway.rirekiList.length,
      showMessageBox
    )
  ) {
    return
  }

  setPrtList()
  // PDFダウンロード
  // 業務共通化処理が下記を参照する
  await executePrint()
}

/**
 * PDFダウンロード
 */
async function executePrint() {
  // 利用者配下の履歴情報を再取得する
  const resSubjectData = await printCom.doRetryRirekiData<
    PrintSubjectSelectInEntity,
    PrtHistoryInfo,
    PreventionPlanPrintSettingsSubjectSelectOutEntity
  >(
    {
      // 利用者リスト：画面.利用者一覧で選択した利用者リスト
      userList: selectedUserList.value.map((x) => {
        return {
          userId: x.userId,
          userName: x.name1Knj + ' ' + x.name2Knj,
        }
      }),
      // 事業所ID：親画面.事業所ID
      svJigyoId: localOneway.Or28754.svJigyoId,
      // 基準日：画面.基準日
      kijunbiYmd: local.mo00020TypeKijunbi.value,
    },
    localOneway.orX0130Oneway.selectMode,
    'preventionPlanPrintSettingsSubjectSelect'
  )

  const inputData: PrintCloseUpdateEntity<IPrintInfo> = {
    // セクション名:親画面.セクション名
    sectionName: localOneway.Or28754.sectionName,
    // システムコード：親画面.システムコード
    gsyscd: localOneway.Or28754.systemCode,
    // 職員ID：親画面.職員ID
    shokuId: localOneway.Or28754.shokuId,
    // 法人ID：親画面.法人ID
    houjinId: localOneway.Or28754.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.Or28754.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.Or28754.svJigyoId,
    // 印刷設定情報リスト：印刷設定情報リスト
    prtList: local.or28754.prtList,
  }
  // 印刷設定情報を保存する
  await printCom.doPrintUpdate(inputData, 'preventionPlanPrintSettingsInfoUpdate')
  // 業務共通化処理の設定
  const selectedPrtData = local.or28754.prtList.find((x) => x.prtNo === local.mo01334.value)
  if (selectedPrtData) {
    // 利用者選択が「単一」、且つ、履歴選択が「単一」の場合
    if (
      localOneway.orX0130Oneway.selectMode === Or28754Const.DEFAULT.TANI &&
      localOneway.orX0143Oneway.singleFlg === Or28754Const.DEFAULT.TANI
    ) {
      await printCom.doReportOutput(
        selectedPrtData,
        {
          userId: selectedUserList.value[0].selfId,
          rirekiId: selectedRirekiData.value[0].rirekiId,
        },
        {
          // 初期設定マスタの情報
          initMasterObj: {
            ...localOneway.Or28754.initMasterObj,
            kikanYmdFlg: localOneway.Or28754.initMasterObj.itakuKkakYmdFlg,
            printFont: localOneway.Or28754.initMasterObj.itakuKkakPrtSizeFlg,
          },
          // 事業所名
          jigyoKnj: localOneway.Or28754.jigyoKnj,
        },
        {
          // 指定日
          // 印刷設定情報リスト選択行.日付表示有無が2の場合、画面の指定日
          selectDate: selectedPrtData.prnDate === '2' ? local.mo00020Type.value : '',
          // 記入用シートを印刷するフラグ
          emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
          // DB未保存画面項目.システムコード：親画面.システムコード
          gsyscd: localOneway.Or28754.systemCode,
        },
        // システム日付
        localOneway.Or28754.sysYmd ?? ''
      )
    } else {
      const historyData: HistoryInfo[] = []
      if (localOneway.orX0130Oneway.selectMode === Or28754Const.DEFAULT.TANI) {
        selectedRirekiData.value.forEach((x) => {
          historyData.push({
            userId: selectedUserList.value[0].selfId,
            rirekiId: x.rirekiId,
          })
        })
      } else {
        resSubjectData.forEach((x) => {
          historyData.push({
            userId: x.userId,
            rirekiId: x.rirekiId,
          })
        })
      }

      // 画面.印刷対象履歴リストを作成する
      printCom.doHukusuuSetting(
        selectedPrtData,
        historyData,
        localOneway.orX0117Oneway,
        localOneway.orX0130Oneway.selectMode,
        selectedUserList.value,
        {
          // 初期設定マスタの情報
          initMasterObj: {
            ...localOneway.Or28754.initMasterObj,
            kikanYmdFlg: localOneway.Or28754.initMasterObj.itakuKkakYmdFlg,
            printFont: localOneway.Or28754.initMasterObj.itakuKkakPrtSizeFlg,
          },
          // 事業所名
          jigyoKnj: localOneway.Or28754.jigyoKnj,
        },
        {
          // 指定日
          // 印刷設定情報リスト選択行.日付表示有無が2の場合、画面の指定日
          selectDate: selectedPrtData.prnDate === '2' ? local.mo00020Type.value : '',
          // 記入用シートを印刷するフラグ
          emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
          // DB未保存画面項目.システムコード：親画面.システムコード
          gsyscd: localOneway.Or28754.systemCode,
        },
        // システム日付
        localOneway.Or28754.sysYmd ?? '',
        resSubjectData,
        selectedRirekiData.value
      )
      // PDFダウンロードを行う
      OrX0117Logic.state.set({
        uniqueCpId: orX0117.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
    }
  }
}

/**
 * 「同意欄の登録」ボタン押下の処理
 */
function onClickPrintAgreementInfo() {
  local.or10148.ledgerINI = {
    parameter05: local.or28754.prtList[0].param05,
    parameter06: local.or28754.prtList[0].param06,
    parameter07: local.or28754.prtList[0].param07,
    parameter08: local.or28754.prtList[0].param08,
    parameter09: local.or28754.prtList[0].param09,
    parameter10: local.or28754.prtList[0].param10,
  }
  // 「GUI01113_同意欄の登録」画面をポップアップで起動する
  Or10148Logic.state.set({
    uniqueCpId: or10148.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => local.or10148,
  (newValue) => {
    // 一件目の印刷設定情報リスト.パラメータ05 = パラメータ05
    local.or28754.prtList[0].param05 = newValue.ledgerINI.parameter05
    // 一件目の印刷設定情報リスト.パラメータ06 = パラメータ06
    local.or28754.prtList[0].param06 = newValue.ledgerINI.parameter06
    // 一件目の印刷設定情報リスト.パラメータ07 = パラメータ07
    local.or28754.prtList[0].param07 = newValue.ledgerINI.parameter07
    // 一件目の印刷設定情報リスト.パラメータ08 = パラメータ08
    local.or28754.prtList[0].param08 = newValue.ledgerINI.parameter08
    // 一件目の印刷設定情報リスト.パラメータ09 = パラメータ09
    local.or28754.prtList[0].param09 = newValue.ledgerINI.parameter09
    // 一件目の印刷設定情報リスト.パラメータ10 = パラメータ10
    local.or28754.prtList[0].param10 = newValue.ledgerINI.parameter10
  }
)
/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 履歴選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331.value.uniqueCpId)?.mo00039OneWayHistorySelectType,
  (newValue) => {
    if (newValue) {
      // 履歴選択方法が「単一」の場合
      if (newValue === Or28754Const.DEFAULT.TANI) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.TANI
        localOneway.orX0117Oneway.type = '1'
      }
      if (newValue === Or28754Const.DEFAULT.HUKUSUU) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.HUKUSUU
        localOneway.orX0117Oneway.type = '0'
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
      }
      setPrintOptions()
      setChildrenValue()
    }
  }
)

/**
 * 利用者選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331.value.uniqueCpId)?.mo00039OneWayUserSelectType,
  (newValue) => {
    if (newValue) {
      if (newValue === Or28754Const.DEFAULT.TANI) {
        // 選択モート
        localOneway.orX0130Oneway.selectMode = Or28754Const.DEFAULT.TANI
        userCols.value = 6
        mo01334TypeHistoryFlag.value = true
        localOneway.orX0117Oneway.type = '1'
      }
      if (newValue === Or28754Const.DEFAULT.HUKUSUU) {
        localOneway.orX0117Oneway.type = '0'
        // 選択モート
        localOneway.orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
        userCols.value = 11
        mo01334TypeHistoryFlag.value = false
      }
      setPrintOptions()
      setChildrenValue()
    }
  }
)

/**
 * 「履歴一覧」明細選択の監視
 */
watch(
  () => OrX0143Logic.event.get(orX0143.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    selectedRirekiData.value = newValue.orX0143DetList as KikanRirekiData[]
  }
)

/**
 * 日付印刷の監視
 */
watch(
  () => Or28780Logic.data.get(or28780.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // 日付印刷区分
      local.mo00039Type = newValue.mo00039Type
      // 指定日
      local.mo00020Type = newValue.mo00020Type
    }
  }
)

/**
 * 帳票タイトルの監視
 */
watch(
  () => Or26328Logic.data.get(or26328.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // タイトル
      local.titleInput = newValue.titleInput
    }
  }
)

/**
 * 印刷オプションの監視
 */
watch(
  () => Or18615Logic.data.get(or18615.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // 敬称を変更する
      local.mo00018TypeChangeTitle = newValue.mo00018TypeChangeTitle
      // 敬称テキスト
      local.textInput = newValue.mo00045Type
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    selectedUserList.value = newValue!.userList
    if (newValue?.clickFlg && localOneway.orX0130Oneway.selectMode === Or28754Const.DEFAULT.TANI) {
      // 利用者選択
      // 利用者選択方法が「単一」の場合
      await getHistoricalInfoList(newValue.userList[0].userId)
    }
  }
)

/**
 * 担当ケアマネの監視
 */
watch(
  () => local.orX0145,
  (newValue) => {
    if (newValue) {
      localOneway.orX0130Oneway.tantouCareManager = (newValue.value as TantoCmnShokuin).shokuinKnj
    }
  }
)

/**
 * バリデーション関数
 */
async function isValidation() {
  const vali1 = await or26328Ref.value?.isValid()
  const vali2 = await or18615Ref.value?.isValid()
  return (vali1 ?? true) && (vali2 ?? true)
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or28754_screen"
      >
        <!-- 帳票 -->
        <g-custom-or-26326
          v-bind="or26326"
          :oneway-model-value="localOneway.mo01334Oneway"
        />
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 or28754_border_right content_center"
        >
          <c-v-row style="margin-left: 8px; margin-top: 8px; margin-bottom: 8px">
            <!--印鑑欄-->
            <base-mo00610
              class="mr-2"
              :oneway-model-value="localOneway.mo00610OneWay"
              @click="openGUI01110"
            />
          </c-v-row>
          <!-- タイトル -->
          <g-custom-or-26328
            ref="or26328Ref"
            v-bind="or26328"
            :oneway-model-value="localOneway.or26328Oneway"
          />
          <c-v-col
            v-if="kojinhogoDisplayFlg"
            cols="12"
            sm="8"
            class="pa-2"
          >
            <!-- 個人情報を印刷しない -->
            <base-mo00018
              v-model="local.mo00018TypeKojinhogo"
              :oneway-model-value="localOneway.mo00018OneWayKojinhogo"
            >
            </base-mo00018>
          </c-v-col>
          <!-- 日付印刷 -->
          <g-custom-or-28780
            v-bind="or28780"
            :oneway-model-value="localOneway.mo00039OneWay"
          />

          <!-- 印刷オプション -->
          <g-custom-or-18615
            ref="or18615Ref"
            v-bind="or18615"
            :oneway-model-value="{
              mo00018OneWayChangeTitle: localOneway.mo00018OneWayChangeTitle,
              mo00045OnewayTextInput: localOneway.mo00045OnewayTextInput,
            }"
          >
            <template #optionPrintItems>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 記入用シートを印刷するチェックボックス -->
                <base-mo00018
                  v-model="local.mo00018TypePrintTheForm"
                  :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <c-v-row no-gutter>
                  <c-v-col cols="auto">
                    <!-- 同意情報を印刷する -->
                    <base-mo00018
                      v-model="local.mo00018TypePrintAgreementInfo"
                      :oneway-model-value="localOneway.mo00018OneWayPrintAgreementInfo"
                    >
                    </base-mo00018>
                  </c-v-col>
                  <c-v-col style="align-content: center">
                    <base-mo00611
                      :oneway-model-value="localOneway.mo00611PrintAgreementInfo"
                      @click="onClickPrintAgreementInfo()"
                    ></base-mo00611>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 支援計画の開始位置を調整する -->
                <base-mo00018
                  v-model="local.mo00018TypeRepositionStartPoint"
                  :oneway-model-value="localOneway.mo00018OneWayRepositionStartPoint"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 必要な事業プログラムに○印をつける -->
                <base-mo00018
                  v-model="local.mo00018TypeCheckPrograms"
                  :oneway-model-value="localOneway.mo00018OneWayCheckPrograms"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 総合的課題〜目標の下線を印刷しない -->
                <base-mo00018
                  v-model="local.mo00018TypeSuppressUnderlining"
                  :oneway-model-value="localOneway.mo00018OneWaySuppressUnderlining"
                >
                </base-mo00018>
              </c-v-col>
              <!-- 親画面.初期設定マスタの情報.計画表様式は1:A4横3枚の場合、表示 -->
              <c-v-col
                v-if="localOneway.Or28754.initMasterObj.itakuKkakPrtFlg === '1'"
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 期間が空白の場合省略する -->
                <base-mo00018
                  v-model="local.mo00018TypeSkipBlankPeriod"
                  :oneway-model-value="localOneway.mo00018OneWaySkipBlankPeriod"
                >
                </base-mo00018>
              </c-v-col>
              <!-- 親画面.初期設定マスタの情報.計画表様式は1:A4横3枚の場合、表示 -->
              <c-v-col
                v-if="localOneway.Or28754.initMasterObj.itakuKkakPrtFlg === '1'"
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 介護予防ケアマネジメント様式で印刷する -->
                <base-mo00018
                  v-model="local.mo00018TypePrintInPreventiveCare"
                  :oneway-model-value="localOneway.mo00018OneWayPrintInPreventiveCare"
                >
                </base-mo00018>
              </c-v-col>
              <!-- 親画面.初期設定マスタの情報.計画表様式は1:A4横3枚の場合、表示 -->
              <c-v-col
                v-if="localOneway.Or28754.initMasterObj.itakuKkakPrtFlg === '1'"
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- タイトルを変更する -->
                <base-mo00018
                  v-model="local.mo00018TypePrintTitleChange"
                  :oneway-model-value="localOneway.mo00018OneWayPrintTitleChange"
                >
                </base-mo00018>
              </c-v-col>
              <!-- 親画面.初期設定マスタの情報.計画表様式は1:A4横3枚の場合、表示 -->
              <!-- タイトル -->
              <c-v-col
                v-if="localOneway.Or28754.initMasterObj.itakuKkakPrtFlg === '1'"
                cols="12"
                sm="12"
                style="padding-left: 12px"
                class="pt-0"
              >
                <base-mo00045
                  v-model="local.title"
                  :oneway-model-value="localOneway.mo00045Title"
                  :disabled="!local.mo00018TypePrintTitleChange.modelValue"
                />
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 印刷確認、押印欄を印刷しない -->
                <base-mo00018
                  v-model="local.mo00018TypePrintConfirmation"
                  :oneway-model-value="localOneway.mo00018OneWayPrintConfirmation"
                >
                </base-mo00018>
              </c-v-col>
            </template>
          </g-custom-or-18615>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
        >
          <c-v-row
            class="or28754_row"
            no-gutter
            style="align-items: center"
          >
            <!-- 利用者選択 -->
            <g-custom-or-26331
              v-bind="or26331"
              :oneway-model-value="{
                mo00039OneWayUserSelectType: localOneway.mo00039OneWayUserSelectType,
                mo00039OneWayHistorySelectType: localOneway.mo00039OneWayHistorySelectType,
              }"
              :unique-cp-id="or26331.uniqueCpId"
            />
            <!-- 担当ケアマネラベル -->
            <g-custom-or-x-0145
              v-bind="orX0145"
              v-model="local.orX0145"
              :oneway-model-value="localOneway.orX0145Oneway"
            ></g-custom-or-x-0145>
          </c-v-row>
          <c-v-row
            class="or28754_row grid-width"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              style="overflow-x: auto"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="localOneway.orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="localOneway.orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag"
              cols="12"
              sm="6"
              class="pl-2 pr-0"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0143
                v-if="localOneway.orX0143Oneway.singleFlg"
                v-bind="orX0143"
                :oneway-model-value="localOneway.orX0143Oneway"
              ></g-custom-or-x-0143>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mr-2"
          :disabled="prtFlg"
          @click="print()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117"
    :oneway-model-value="localOneway.orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- GUI01110_「印鑑欄設定」画面を起動する -->
  <g-custom-or-10016
    v-if="showDialogOr10016"
    v-bind="or10016"
    :oneway-model-value="localOneway.or10016Oneway"
  />
  <!-- GUI01113_同意欄の登録 -->
  <g-custom-or-10148
    v-if="showDialogOr10148"
    v-bind="or10148"
    v-model="local.or10148"
  />
</template>

<style scoped lang="scss">
.or28754_screen {
  margin: -8px !important;
}

.or28754_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or28754_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.customCol {
  margin-left: 0px;
  margin-right: 0px;
}

.grid-width {
  min-width: 694px;
}
</style>
