<script setup lang="ts">
/**
 * Or29106:有機体:計画書（１）メイン（画面/特殊コンポーネント）
 * GUI01004_計画書（１）
 *
 * @description
 * 計画書（１）メイン画面の処理
 *
 * <AUTHOR> 王利峰
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { OrX0007Const } from '../OrX0007/OrX0007.constants'
import { OrX0008Const } from '../OrX0008/OrX0008.constants'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { OrX0010Const } from '../OrX0010/OrX0010.constants'
import { Or10578Const } from '../Or10578/Or10578.constants'
import { Or10578Logic } from '../Or10578/Or10578.logic'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0008Logic } from '../OrX0008/OrX0008.logic'
import { OrX0010Logic } from '../OrX0010/OrX0010.logic'
import { OrX0009Logic } from '../OrX0009/OrX0009.logic'
import { OrX0139Const } from '../OrX0139/OrX0139.constants'
import { OrX0139Logic } from '../OrX0139/OrX0139.logic'
import { Or32421Logic } from '../Or32421/Or32421.logic'
import { Or32421Const } from '../Or32421/Or32421.constants'
import { Or10583Const } from '../Or10583/Or10583.constants'
import { Or10583Logic } from '../Or10583/Or10583.logic'
import { Or29115Const } from '../Or29115/Or29115.constants'
import { Or29115Logic } from '../Or29115/Or29115.logic'
import type { Or30564StateType, OrX0139RefFunc, OrX0007RefFunc } from './Or30564.type'
import { Or30564Const } from './Or30564.constants'
import type { Or32421Type, Or32421OnewayType } from '~/types/cmn/business/components/Or32421Type'
import {
  hasPrintAuth,
  hasRegistAuth,
  useCmnRouteCom,
  useCommonProps,
  useGyoumuCom,
  useJigyoList,
  useNuxtApp,
  useScreenInitFlg,
  useScreenStore,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
  useUserListInfo,
} from '#imports'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type {
  CarePlan1MasterInData,
  Or10578OnewayType,
} from '~/types/cmn/business/components/Or10578Type'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import type { OrX0007Type, OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import type {
  OrX0008Type,
  OrX0008OnewayType,
  RirekiInfo,
} from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009Type, OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0010Type, OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  GyoumuComSelectInEntity,
  IKikanComInfo,
} from '~/repositories/cmn/entities/GyoumuComSelectEntity'
import type {
  Plan1InitMasterInfo,
  Plan1SelectOutData,
  Plan1SelectOutEntity,
} from '~/repositories/cmn/entities/Plan1SelectEntity'
import { SPACE_WAVE, UPDATE_KBN } from '~/constants/classification-constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type {
  Or21814OnewayType,
  Or21814EventType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Plan1UpdateInEntity } from '~/repositories/cmn/entities/Plan1UpdateEntity'
import type { GyoumuComUpdateOutEntity } from '~/repositories/cmn/entities/GyoumuComUpdateEntity'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { OrX0139OnewayType, IDataInfo } from '~/types/cmn/business/components/OrX0139Type'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import type {
  Plan1CopyReturnSelectOutEntity,
  Plan1CopyReturnSelectInEntity,
} from '~/repositories/cmn/entities/Plan1CopyReturnSelectEntity'
import type { Or10583OnewayType } from '~/types/cmn/business/components/Or10583Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { PrintOnewayEntity, userList } from '~/repositories/cmn/entities/PrintSelectEntity'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type {
  Or21815StateType,
  Or21815EventType,
} from '~/components/base-components/organisms/Or21815/Or21815.type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'

/**
 * setChildCpBinds
 */
const { setChildCpBinds, searchUniqCpId } = useScreenUtils()
/**
 * log
 */
const $log = useNuxtApp().$log as DebugLogPluginInterface
/**
 * useI18n
 */
const { t } = useI18n()
/** props */
const props = defineProps(useCommonProps())
/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * cmnRouteCom
 */
const cmnRouteCom = useCmnRouteCom()
/**
 * gyoumuCom
 */
const gyoumuCom = useGyoumuCom()
/**
 * jigyoListWatch
 */
const { jigyoListWatch } = useJigyoList()
/**
 * useUserListInfo
 */
const { syscomUserSelectWatchFunc } = useUserListInfo()
/**************************************************
 * Props
 **************************************************/

/**************************************************
 * 変数定義
 **************************************************/
/**
 * defaultModelValue
 */
const defaultModelValue = {
  or30564: {
    /** 操作区分 */
    operaFlg: Or30564Const.OPERA_FLAG_0,
    moveSaveConfirmFlg: '',
    rirekiPagingNo: '',
    emitType: '',
    /**履歴ID*/
    rirekiId: '0',
    sc1Id: '',
    kikanFlg: '',
    createDate: '',
    //初期設定マスタの情報
    initMasterObj: {
      /** 計画書書式 */
      shosikiFlg: '',
      /** 計画書様式 */
      cksFlg: '',
      /** 担当者（計画書（１））  */
      cks1TantoFlg: '',
      /** 要介護度（計画書（１））  */
      cks1YokaiFlg: '',
    } as Plan1InitMasterInfo,
    showOrX0008Flg: false,
    /** 初回フラグ  */
    onMountedOpenFlg: Or30564Const.ONMOUNTED_OPEN_0,
  } as Or30564StateType,
}

/**
 * defaultComponents
 */
const defaultComponents = {
  // 期間データ
  orX0007: { PlanTargetPeriodUpdateFlg: '' } as OrX0007Type,
  //履歴
  orX0008: {
    createId: '0',
    createUpateFlg: '',
  } as OrX0008Type,
  //作成者
  orX0009: { staffId: '', staffName: '' } as OrX0009Type,
  //作成日
  orX0010: { value: '' } as OrX0010Type,
  // 複写画面
  or32421: { rirekiId: '' } as Or32421Type,
}

/**
 * defaultOneway
 */
const defaultOneway = reactive({
  //期間データ
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as OrX0007OnewayType,
  //履歴
  orX0008Oneway: {
    createData: {} as RirekiInfo,
    screenID: Or30564Const.SCREEN_ID,
  } as OrX0008OnewayType,
  //作成者
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
    isDisabled: false,
  } as OrX0009OnewayType,
  //作成日
  orX0010Oneway: { isDisabled: false } as OrX0010OnewayType,
  // 複写画面
  or32421Oneway: {} as Or32421OnewayType,
})

/**
 * local
 */
const local = reactive({
  or30564: {
    ...defaultModelValue.or30564,
    // ...Props.modelValue,
  } as Or30564StateType,
})

/**
 * localComponents
 */
const localComponents = reactive({
  orX0007: {
    ...defaultComponents.orX0007,
  } as OrX0007Type,
  orX0008: {
    ...defaultComponents.orX0008,
  } as OrX0008Type,
  orX0009: {
    ...defaultComponents.orX0009,
  } as OrX0009Type,
  orX0010: {
    ...defaultComponents.orX0010,
  } as OrX0010Type,
  or32421: {
    ...defaultComponents.or32421,
  } as Or32421Type,
})

/**
 * localOneway
 */
const localOneway = reactive({
  orX0007Oneway: {
    ...defaultOneway.orX0007Oneway,
  } as OrX0007OnewayType,
  orX0008Oneway: {
    ...defaultOneway.orX0008Oneway,
  } as OrX0008OnewayType,
  orX0009Oneway: {
    ...defaultOneway.orX0009Oneway,
  } as OrX0009OnewayType,
  orX0010Oneway: {
    ...defaultOneway.orX0010Oneway,
  } as OrX0010OnewayType,
  orX0139Oneway: {} as OrX0139OnewayType,
  //マスタ他
  or10578Oneway: {
    carePlan1MasterInData: {
      jigyoId: '',
      shisetuId: '',
      svJigyoIdList: [''],
    } as CarePlan1MasterInData,
  } as Or10578OnewayType,
  //計画書一括印刷
  or10583Oneway: {
    carePlanPrintAllInData: {
      legalPersonId: '',
      shisetuId: '',
      carePlanStyle: '',
      employeeId: '',
      officeData: { officeId: '', officeName: '' },
      jigyoTypeCd: '',
      kinouKengenSwitch: '',
    },
    setInputComponentsFlg: false,
  } as Or10583OnewayType,
  //計画書一括印刷
  or29115Oneway: {
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
    jigyoKnj: '',
    svJigyoIdList: [''],
    shokuId: '',
    userId: '',
    startDate: '',
    endDate: '',
    sysYmd: '',
    systemCode: '',
    userList: [] as userList[],
    initMasterObj: {
      shosikiFlg: '',
      cksFlg: '',
      cks1TantoFlg: '',
      cks1YokaiFlg: '',
    } as Plan1InitMasterInfo,
    tantoShokuId: '',
    rirekiId: '',
    kikanFlg: '',
    jigyoCd: '',
    sectionName: '',
    gojuuOnKana: [''],
    gojuuOnRowNo: '',
  } as PrintOnewayEntity<Plan1InitMasterInfo>,
  //計画書複写
  or32421Oneway: {
    ...defaultOneway.or32421Oneway,
  } as Or32421OnewayType,
})

/**
 * メニュー
 */
const or11871 = ref({ uniqueCpId: '' })
/**
 * 利用者
 */
const or00248 = ref({ uniqueCpId: '' })
/**
 * 計画期間
 */
const orX0007_1 = ref({ uniqueCpId: '' })
/**
 * 履歴情報
 */
const orX0008_1 = ref({ uniqueCpId: '' })
/**
 * 作成者
 */
const orX0009_1 = ref({ uniqueCpId: '' })
/**
 * 作成日
 */
const orX0010_1 = ref({ uniqueCpId: '' })
/**
 * 詳細情報
 */
const orX0139_1 = ref({ uniqueCpId: '' })
/**
 * マスタ他
 */
const or10578_1 = ref({ uniqueCpId: '' })
/**
 * 計画書一括印刷
 */
const or10583_1 = ref({ uniqueCpId: '' })
/**
 * 印刷画面
 */
const or29115_1 = ref({ uniqueCpId: '' })
/**
 * 確認ダイアログ
 */
const or21814_1 = ref({ uniqueCpId: '' })
/**
 * ダイアログ
 */
const or21815_1 = ref({ uniqueCpId: '' })
/**
 * 事業所選択プルダウン
 */
const or41179_1 = ref({ uniqueCpId: '' })
const or32421_1 = ref({ uniqueCpId: '' })

/**
 *orX0139Ref
 */
const orX0139Ref = ref<OrX0139RefFunc>()
const orX0007Ref = ref<OrX0007RefFunc | null>(null)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * isInit
 */
const isInit = useScreenInitFlg()
onMounted(async () => {
  //共通処理の保存権限チェックを行う
  //共通処理の印刷権限チェックを行う
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      disabledSaveBtn: !(await hasRegistAuth(Or30564Const.LINK_AUTH)), //共通処理の保存権限チェックを行う
      disabledPrintBtn: !(await hasPrintAuth(Or30564Const.LINK_AUTH)), //共通処理の印刷権限チェックを行う
    },
  })
  // 利用者を全選択です。
  const uniqueCpId248 = searchUniqCpId(props.uniqueCpId, Or00248Const.CP_ID(1), 0)
  if (uniqueCpId248) {
    const uniqueCpId94 = searchUniqCpId(uniqueCpId248, Or00094Const.CP_ID(0), 0)
    if (uniqueCpId94) {
      Or00094Logic.state.set({
        uniqueCpId: uniqueCpId94,
        state: {
          dispSettingBtnDisplayFlg: true,
          focusSettingFlg: true,
          focusSettingInitial: [Or30564Const.STR_ALL],
        },
      })
    }
  }

  local.or30564.onMountedOpenFlg = Or30564Const.ONMOUNTED_OPEN_0
  local.or30564.searchSelfUserID = systemCommonsStore.getUserSelectSelfId() ?? ''
  // 初期情報取得
  if (isInit) {
    // システム共有情報利用者設定
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: local.or30564.searchSelfUserID,
        },
        verticalLayout: true,
      },
    })
    await initOr30564()
  }
  local.or30564.onMountedOpenFlg = Or30564Const.ONMOUNTED_OPEN_1
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [OrX0007Const.CP_ID(1)]: orX0007_1.value,
  [OrX0008Const.CP_ID(1)]: orX0008_1.value,
  [OrX0009Const.CP_ID(1)]: orX0009_1.value,
  [OrX0010Const.CP_ID(1)]: orX0010_1.value,
  [OrX0139Const.CP_ID(1)]: orX0139_1.value,
  [Or10578Const.CP_ID(1)]: or10578_1.value,
  [Or10583Const.CP_ID(1)]: or10583_1.value,
  [Or29115Const.CP_ID(1)]: or29115_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
  [Or41179Const.CP_ID(1)]: or41179_1.value,
  [Or32421Const.CP_ID(1)]: or32421_1.value,
})

const or00249 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(or00248.value.uniqueCpId, {
  [Or00249Const.CP_ID(0)]: or00249.value,
})

Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.care-plan1'),
    showFavorite: true,
    showViewSelect: false,
    showCreateBtn: true,
    tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    viewSelectItems: [],
    showMasterBtn: true,
    showOptionMenuDelete: true,
    showSaveBtn: true,
  },
})

/**
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007_1.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    $log.debug('計画期間変更フラグの監視 planTargetPeriodUpdateFlg:', newValue)
    if (isUndefined(newValue)) {
      return
    }

    const planID = newValue.planTargetPeriodId
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg

    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }

    if (planUpdateFlg === Or30564Const.ACTION_UPDATE_FLG_0 && planID !== '0' && planID !== '') {
      //「期間-選択確認前 アイコンボタン」押下
      //共通情報.計画期間ＩＤ = 返回値.計画期間ＩＤ
      setCommonPlanPeriod({ sc1Id: planID })
      //共通情報.履歴ＩＤ ＝ 0
      local.or30564.rirekiId = '0'
      //操作区分 ＝ K3:計画対象期間Open
      local.or30564.operaFlg = Or30564Const.OPERA_FLAG_K3
      //データ再取得
      await getCarePlan1Data()
    } else if (planUpdateFlg === Or30564Const.ACTION_UPDATE_FLG_1) {
      //「期間-前へ アイコンボタン」押下
      //共通情報.履歴ＩＤ ＝ 0
      local.or30564.rirekiId = '0'
      //操作区分 ＝ K1:期間-前へ
      local.or30564.operaFlg = Or30564Const.OPERA_FLAG_K1
      //遷移保存確認区分
      local.or30564.moveSaveConfirmFlg = isEdit.value
        ? Or30564Const.COMFIRM_SAVE_FLG_1
        : Or30564Const.COMFIRM_SAVE_FLG_0
      //データ再取得
      await getCarePlan1Data()
    } else if (planUpdateFlg === Or30564Const.ACTION_UPDATE_FLG_2) {
      //「期間-次へ アイコンボタン」押下
      //共通情報.履歴ＩＤ ＝ 0
      local.or30564.rirekiId = '0'
      //操作区分 ＝ K2:期間-次へ
      local.or30564.operaFlg = Or30564Const.OPERA_FLAG_K2
      //遷移保存確認区分
      local.or30564.moveSaveConfirmFlg = isEdit.value
        ? Or30564Const.COMFIRM_SAVE_FLG_1
        : Or30564Const.COMFIRM_SAVE_FLG_0
      //データ再取得
      await getCarePlan1Data()
    }
    OrX0007Logic.data.set({
      uniqueCpId: orX0007_1.value.uniqueCpId,
      value: { planTargetPeriodId: '', PlanTargetPeriodUpdateFlg: '' },
    })
  },
  { deep: true }
)

/**
 * 履歴変更の監視
 */
watch(
  () => OrX0008Logic.data.get(orX0008_1.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    $log.debug('履歴変更の監視 createUpdateFlg:', newValue)
    if (isUndefined(newValue)) {
      return
    }
    const createUpateFlg = newValue.createUpateFlg

    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }

    if (createUpateFlg === Or30564Const.ACTION_UPDATE_FLG_0) {
      //「履歴-選択確認前 アイコンボタン」押下
      // 履歴ＩＤ
      local.or30564.rirekiId = String(newValue.createId)
      //操作区分 ＝ R3:履歴-選択
      local.or30564.operaFlg = Or30564Const.OPERA_FLAG_R3
      // 表示用「履歴」情報を設定する
      local.or30564.rirekiObj.rirekiId = String(newValue.createId)
      local.or30564.rirekiObj.shokuId = newValue.rirekiObj?.staffId ?? ''
      local.or30564.rirekiObj.shokuKnj = newValue.rirekiObj?.staffName ?? ''
      local.or30564.rirekiObj.createYmd = newValue.rirekiObj?.createDate ?? ''
      local.or30564.rirekiObj.pagingNo = String(newValue.rirekiObj?.currentIndex ?? 0)
      local.or30564.rirekiObj.pagingCnt = String(newValue.rirekiObj?.totalCount ?? 0)

      //データ再取得
      await getCarePlan1Data()
    } else if (createUpateFlg === Or30564Const.ACTION_UPDATE_FLG_1) {
      //「履歴-前へ アイコンボタン」押下
      //操作区分 ＝ R1:履歴-前へ
      local.or30564.operaFlg = Or30564Const.OPERA_FLAG_R1
      //遷移保存確認区分
      local.or30564.moveSaveConfirmFlg = isEdit.value
        ? Or30564Const.COMFIRM_SAVE_FLG_1
        : Or30564Const.COMFIRM_SAVE_FLG_0
      //データ再取得
      await getCarePlan1Data()
    } else if (createUpateFlg === Or30564Const.ACTION_UPDATE_FLG_2) {
      //「履歴-次へ アイコンボタン」押下
      //操作区分 ＝ R2:履歴-次へ
      local.or30564.operaFlg = Or30564Const.OPERA_FLAG_R2
      //遷移保存確認区分
      local.or30564.moveSaveConfirmFlg = isEdit.value
        ? Or30564Const.COMFIRM_SAVE_FLG_1
        : Or30564Const.COMFIRM_SAVE_FLG_0
      //データ再取得
      await getCarePlan1Data()
    }
    OrX0008Logic.data.set({
      uniqueCpId: orX0008_1.value.uniqueCpId,
      value: {
        createId: '',
        createUpateFlg: '',
        rirekiObj: undefined,
      },
    })
  },
  { deep: true }
)

/**
 * 事業所選択更新の監視
 */
// ★事業所選択監視関数を実行
jigyoListWatch(or41179_1.value.uniqueCpId, (newJigyoId: string) => {
  void callbackFuncJigyo(newJigyoId)
})
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function callbackFuncJigyo(newJigyoId: string) {
  console.log('callbackFuncJigyo 開始:', newJigyoId)
  const oldJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  await gyoumuCom.doComLogicChangeSvJigyo(
    newJigyoId,
    oldJigyoId,
    local.or30564,
    isEdit.value,
    await hasRegistAuth(Or30564Const.LINK_AUTH),
    showConfirmMessageBox,
    showWarnMessageBox,
    _save,
    getCarePlan1Data,
    setCommonPlanPeriod,
    setSvJigyoId,
    setSelectSelUserIndex
  )
  console.log('callbackFuncJigyo 終了:', newJigyoId)
}

/**
 * 事業所設定
 *
 * @param jigyoId - 設定の事業者ID
 */
const setSvJigyoId = (jigyoId: string) => {
  systemCommonsStore.setSvJigyoId(jigyoId)
  Or41179Logic.data.set({
    uniqueCpId: or41179_1.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    } as Mo00040Type,
  })
}

/**
 * 利用者設定
 *
 * @param index - index
 */
const setSelectSelUserIndex = (index: number) => {
  //利用者設定
  Or00249Logic.data.set({
    uniqueCpId: or00249.value.uniqueCpId,
    value: {
      selectUserIndex: index,
    },
  })
}

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackFuncUser)

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */
function callbackFuncUser(newSelfId: string) {
  console.log('callbackFuncUser 開始')
  local.or30564.newSelfUserID = newSelfId
  if (newSelfId !== '') {
    console.log('利用者選択監視関数を実行newSelfId:', local.or30564.newSelfUserID)
    console.log('利用者選択監視関数を実行oldSelfId:', local.or30564.searchSelfUserID)
    // 事業所選択プルダウンの検索条件を更新
    // 検索条件を変更すると、変更を検知して事業所情報の更新が行われる
    // 更新が完了すると、関数「 jigyoListWatch 」に指定したコールバック関数が実行されます
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: newSelfId,
        },
      },
    })
  }
  console.log('callbackFuncUser 終了')
}

/**
 * 保存（削除）フラグ更新の監視
 */
watch(
  () => local.or30564.operaFlg,
  (newValue) => {
    console.log('保存（削除）フラグ更新の監視:', newValue)
    if (local.or30564.operaFlg === Or30564Const.OPERA_FLAG_3) {
      // 「新規」ボタン、「複写」メニュー、「パターン」メニュー、「印刷設定アイコン」ボタン、「削除」メニューを非活性にする
      Or11871Logic.state.set({
        uniqueCpId: or11871.value.uniqueCpId,
        state: {
          disabledCreateBtn: false,
          disabledPrintBtn: false,
          disabledOptionMenuDelete: false,
          disabledCreateMenuCopy: false,
        },
      })
      // 「作成者選択アイコンボタン」を非活性にする
      OrX0009Logic.state.set({
        uniqueCpId: orX0009_1.value.uniqueCpId,
        state: {
          isDisabled: true,
        },
      })
      // 「作成日選択アイコンボタン」、「作成日」を非活性にする
      OrX0010Logic.state.set({
        uniqueCpId: orX0010_1.value.uniqueCpId,
        state: {
          isDisabled: true,
        },
      })
    } else {
      // 「新規」ボタン、「複写」メニュー、「パターン」メニュー、「印刷設定アイコン」ボタン、「削除」メニューを活性にする
      Or11871Logic.state.set({
        uniqueCpId: or11871.value.uniqueCpId,
        state: {
          disabledCreateBtn: false,
          disabledPrintBtn: false,
          disabledOptionMenuDelete: false,
          disabledCreateMenuCopy: false,
        },
      })
      // 「作成者選択アイコンボタン」を活性にする
      OrX0009Logic.state.set({
        uniqueCpId: orX0009_1.value.uniqueCpId,
        state: {
          isDisabled: false,
        },
      })
      // 「作成日選択アイコンボタン」、「作成日」を活性にする
      OrX0010Logic.state.set({
        uniqueCpId: orX0010_1.value.uniqueCpId,
        state: {
          isDisabled: false,
        },
      })
    }
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    $log.debug('画面メニューのイベントを監視', newValue)
    if (newValue === undefined) {
      return
    }

    if (newValue.favoriteEventFlg) {
      $log.debug('お気に入り')
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { favoriteEventFlg: false },
      })
    }
    if (newValue.saveEventFlg) {
      $log.debug('保存')
      await _save()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { saveEventFlg: false },
      })
    }
    if (newValue.createEventFlg) {
      $log.debug('新規')
      await _create()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { createEventFlg: false },
      })
    }
    if (newValue.printEventFlg) {
      $log.debug('印刷')
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { printEventFlg: false },
      })
    }
    if (newValue.masterEventFlg) {
      $log.debug('マスタ他')
      await _master()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { masterEventFlg: false },
      })
    }
    if (newValue.deleteEventFlg) {
      $log.debug('削除')
      await _delete()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { deleteEventFlg: false },
      })
    }
    if (newValue.copyEventFlg) {
      $log.debug('複写')
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { copyEventFlg: false },
      })
    }
  }
)

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 共通情報.計画期間を設定
 *
 * @param kikanObj - 計画期間
 */
const setCommonPlanPeriod = (kikanObj: IKikanComInfo) => {
  cmnRouteCom.setPlanPeriod({
    kikanId: kikanObj.sc1Id,
    kikanIndex: kikanObj.pagingNo ?? '',
    startYmd: kikanObj.startYmd ?? '',
    endYmd: kikanObj.endYmd ?? '',
  })
}
/**
 * メッセージを表示する
 *
 * @param messageId - メッセージ
 */
const showMessageBox = async (messageId: string) => {
  await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.error'),
    // ダイアログテキスト
    dialogText: t(messageId),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.ok'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
}

/**
 * メッセージを表示する
 */
const showConfirmMessageBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * メッセージを表示する
 */
const showWarnMessageBox = async () => {
  return await openWarnDialog({
    // ダイアログタイトル
    dialogTitle: t('label.warning'),
    // ダイアログテキスト
    dialogText: t('message.w-com-10006'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
}
/**
 * 初期化処理
 */
const initOr30564 = async () => {
  //操作区分 ＝ 0:初期化
  local.or30564.operaFlg = Or30564Const.OPERA_FLAG_0
  //データ取得
  await getCarePlan1Data()
}

/**
 * 画面表示のデータを取得
 */
const getCarePlan1Data = async () => {
  console.log('getCarePlan1Data 開始')

  const inputParam: GyoumuComSelectInEntity = {
    ...gyoumuCom.getGyoumuComSelectInEntity(
      local.or30564.operaFlg,
      local.or30564.moveSaveConfirmFlg,
      local.or30564.rirekiPagingNo,
      cmnRouteCom.getPlanPeriod()?.kikanId
    ),
    //履歴ID
    rirekiId:
      local.or30564.rirekiId && local.or30564.rirekiId !== '' ? local.or30564.rirekiId : '0',
  }

  // local.or30564.showOrX0008Flg = true

  console.log('getCarePlan1Data > inputParam:', inputParam)
  // const ret: ICaraPlan1OutEntity = await CarePlan1Repository.fetchCaraPlan1(input)
  const ret: Plan1SelectOutEntity = await ScreenRepository.select('plan1Select', inputParam)
  const retData = ret.data
  console.log('getCarePlan1Data > retData:', retData)

  //API戻り値の設定前の処理
  if (
    !(await gyoumuCom.doComLogicSelectApi(
      ret,
      await hasRegistAuth(Or30564Const.LINK_AUTH),
      showMessageBox,
      showConfirmMessageBox,
      showWarnMessageBox,
      _save,
      getCarePlan1Data,
      local.or30564,
      {
        dataObj: ret.data.dataObj ?? {},
        initMasterObj: ret.data.initMasterObj ?? {},
        yokaiList: ret.data.yokaiList ?? [],
      } as Plan1SelectOutData,
      setCommonPlanPeriod
    ))
  ) {
    console.log('getCarePlan1Data > gyoumuCom.doComLogicSelectApi return false!')
    return
  }

  //画面初期化のComponents表示フラグ設定、画面初期化のデータ設定
  setFormData(ret)
  setChildCpBinds(props.uniqueCpId, {
    [OrX0007Const.CP_ID(1)]: {
      twoWayValue: {
        planTargetPeriodId: localComponents.orX0007.planTargetPeriodId,
        PlanTargetPeriodUpdateFlg: localComponents.orX0007.PlanTargetPeriodUpdateFlg,
      },
    },
    [OrX0008Const.CP_ID(1)]: {
      twoWayValue: {
        createId: localComponents.orX0008.createId,
        createUpdateFlg: localComponents.orX0008.createUpateFlg,
      },
    },
    [OrX0009Const.CP_ID(1)]: {
      twoWayValue: {
        staffId: localComponents.orX0009.staffId,
        staffName: localComponents.orX0009.staffName,
      },
    },
    [OrX0010Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.orX0010.value,
        mo01343: {},
      },
    },
    [OrX0139Const.CP_ID(1)]: {
      twoWayValue: {
        dataObj: local.or30564.dataObj,
        deleteFlg: '',
      },
      oneWayState: {
        isDisabled: false,
        kikanFlg: local.or30564.kikanFlg,
        sc1Id: local.or30564.sc1Id,
        securityUseKhnFlg: local.or30564.securityUseKhnFlg,
        securityUseTaiKirokuFlg: local.or30564.securityUseTaiKirokuFlg,
        securityUseRyuijikouFlg: local.or30564.securityUseRyuijikouFlg,
        securityUseChoukiFlg: local.or30564.securityUseChoukiFlg,
        yokaiList: local.or30564.yokaiList,
        isCopyMode: false,
        initMasterObj: local.or30564.initMasterObj,
        syubetuId: local.or30564.syubetuId,
      },
    },
  })

  //メイン画面初回Openフラグが0:初回の場合、かつ、初期設定マスタの情報.メッセージ表示が1:表示の場合、
  //かつ、共通情報.事業者コード（※「スケジュール、サービス種類」）が「50010:介護予防支援」以外の場合
  if (
    local.or30564.onMountedOpenFlg === Or30564Const.ONMOUNTED_OPEN_0 &&
    local.or30564.initMasterObj.msgFlg === Or30564Const.MSG_SHOW_1 &&
    systemCommonsStore.getSvJigyoCd !== Or30564Const.JIGYOSHO_CD_KAI_GO_YOBO
  ) {
    // 以下のメッセージを表示: i.cmn.11276
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11276'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
  }

  //選定の利用者INDEXを一時保存
  local.or30564.searchSelUserIndex =
    Or00249Logic.data.get(or00249.value.uniqueCpId)?.selectUserIndex ?? 0

  console.log('getCarePlan1Data 終了')
}
/**
 *
 * 画面コントロール表示設定
 *
 * @param ret - Plan1SelectOutEntity
 */
function setFormData(ret: Plan1SelectOutEntity) {
  console.log('setFormData 開始')

  //APIからの初期データ
  const retData = ret.data
  const kikanObj = local.or30564.kikanObj
  const rirekiObj = local.or30564.rirekiObj

  //課題検討セキュリティチェック
  local.or30564.securityKadaiKentouFlg = retData.securityKadaiKentouFlg
  //課題整理総括セキュリティチェック
  local.or30564.securityKadaiSeirisoukatsuFlg = retData.securityKadaiKentouFlg
  //長期目標セキュリティチェック
  local.or30564.securityUseChoukiFlg = retData.securityUseChoukiFlg
  //基本状況セキュリティチェック
  local.or30564.securityUseKhnFlg = retData.securityUseKhnFlg
  //留意事項セキュリティチェック
  local.or30564.securityUseRyuijikouFlg = retData.securityUseRyuijikouFlg
  //退院退所情報記録書セキュリティチェック
  local.or30564.securityUseTaiKirokuFlg = retData.securityUseTaiKirokuFlg
  //計画期間ID
  local.or30564.sc1Id = kikanObj.sc1Id
  //履歴ID
  local.or30564.rirekiId = rirekiObj.rirekiId
  //作成日
  local.or30564.createDate = rirekiObj.createYmd ?? ''

  //期間管理フラグが「1:管理する」、計画対象期間 を表示にする。
  //期間管理フラグが「0:管理しない」、計画対象期間 を非表示にする。
  // 期間管理フラグが「1:管理する」、かつ、計画対象期間リストが0件
  //           計画対象期間-ページングを"0 / 0"で表示にする。
  //           ・計画対象期間を固定文字「計画対象期間アイコンボタンから（改行）期間登録を行ってください」で表示※太字、赤色
  //           ・履歴、作成者、作成日、入力フームを非表示にする。
  if (local.or30564.kikanFlg === Or30564Const.PLAN_TARGET_PERIOD_1) {
    //種別ID
    localOneway.orX0007Oneway.kindId = local.or30564.syubetuId
    if (kikanObj.pagingFlg !== Or30564Const.PAGING_FLAG_0) {
      //orX0007
      localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = kikanObj.pagingNo
        ? Number(kikanObj.pagingNo)
        : 0
      localOneway.orX0007Oneway.planTargetPeriodData.totalCount = kikanObj.pagingCnt
        ? Number(kikanObj.pagingCnt)
        : 0
      if (kikanObj.startYmd) {
        //表示用「計画対象期間」情報.開始日 + " ～ " + 表示用「計画対象期間」情報.終了日
        localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriod = kikanObj.startYmd
          .concat(SPACE_WAVE)
          .concat(kikanObj.endYmd ?? '')
      }
      localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId = Number(kikanObj.sc1Id)
      localComponents.orX0007.planTargetPeriodId = kikanObj.sc1Id
      //期間管理フラグが「1:管理する」、かつ、計画期間が登録されている場合、表示
      local.or30564.showOrX0008Flg = true
    } else {
      // ・計画対象期間-ページングを"0 / 0"で表示にする。
      // ・計画対象期間を固定文字「計画対象期間アイコンボタンから（改行）期間登録を行ってください」で表示※太字、赤色
      // ・履歴、作成者、作成日、入力フームを非表示にする。
      localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = 0
      localOneway.orX0007Oneway.planTargetPeriodData.totalCount = 0
      localComponents.orX0007.planTargetPeriodId = ''
      //期間管理フラグが「1:管理する」、かつ、計画期間が登録されていない場合、非表示
      local.or30564.showOrX0008Flg = false
    }
  } else {
    local.or30564.showOrX0008Flg = true
  }

  //orX0008
  localOneway.orX0008Oneway.sc1Id = kikanObj.sc1Id
  localOneway.orX0008Oneway.officeId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.orX0008Oneway.useId = systemCommonsStore.getUserSelectSelfId() ?? ''
  localOneway.orX0008Oneway.createData.createDate = rirekiObj.createYmd
  localOneway.orX0008Oneway.createData.createId = rirekiObj.rirekiId
  localOneway.orX0008Oneway.createData.currentIndex = rirekiObj.pagingNo
    ? Number(rirekiObj.pagingNo)
    : 0
  localOneway.orX0008Oneway.createData.staffId = rirekiObj.shokuId
  localOneway.orX0008Oneway.createData.staffName = rirekiObj.shokuKnj
  localOneway.orX0008Oneway.createData.totalCount = rirekiObj.pagingCnt
    ? Number(rirekiObj.pagingCnt)
    : 0
  localComponents.orX0008.createId = rirekiObj.rirekiId

  //orX0009
  localOneway.orX0009Oneway.createData!.createDate = rirekiObj.createYmd
  localOneway.orX0009Oneway.createData!.createId = Number(rirekiObj.rirekiId)
  localOneway.orX0009Oneway.createData!.currentIndex = Number(rirekiObj.pagingNo)
  localOneway.orX0009Oneway.createData!.staffId = Number(rirekiObj.shokuId)
  localOneway.orX0009Oneway.createData!.staffName = rirekiObj.shokuKnj
  localOneway.orX0009Oneway.createData!.totalCount = Number(rirekiObj.pagingCnt)

  localComponents.orX0009.staffId = rirekiObj.shokuId
  localComponents.orX0009.staffName = rirekiObj.shokuKnj
  //orX0010 計画書_履歴リストデータが存在の場合、計画書_履歴リスト1件目.作成日
  localComponents.orX0010.value = rirekiObj.createYmd
  //共通情報を設定する
  setCommonPlanPeriod(kikanObj)

  console.log('setFormData 終了')
}

/**
 * 新規作成時処理
 */
async function _create() {
  console.log(' START _create')
  //操作区分が3:削除の場合
  if (local.or30564.operaFlg === Or30564Const.OPERA_FLAG_3) {
    return
  }
  //期間管理フラグが「1:管理する」、かつ、計画対象期間リストが0件、メッセージを表示e.cmn.40980
  if (
    local.or30564.kikanFlg === Or30564Const.PLAN_TARGET_PERIOD_1 &&
    local.or30564.kikanObj.pagingFlg === Or30564Const.PAGING_FLAG_0
  ) {
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11300'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // GUI00070 対象期間画面をポップアップで起動する。
    if (orX0007Ref.value?.onClickDialog) {
      await orX0007Ref.value.onClickDialog()
    }
    return
  } else if (local.or30564.rirekiId === '0' || !local.or30564.rirekiId) {
    //共通情報.履歴ＩＤが空白または0の場合
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.care-plan1')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  } else {
    //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
    if (!(await checkEditBySave())) {
      return
    }
    //共通情報.履歴ＩＤ ＝ 0
    local.or30564.rirekiId = '0'
    //操作区分 ＝ 1:新規
    local.or30564.operaFlg = Or30564Const.OPERA_FLAG_1
    //データ再取得
    await getCarePlan1Data()
  }
  //二回目新規ボタン押下する場合、メッセージを表示i.cmn.11265
  // 新規作成した{0}が保存されていません。保存を行った後に新規作成してください。
  //計画書（１）の新規情報を取得する。
  //    ・初回作成日リスト・認定情報リスト
  console.log('END _create')
}

/**
 * 保存時処理(checkEdit含めて保存処理)
 * False: 処理中止（取消、いいえ）と保存権限がない時の取消操作、
 * True：処理継続（保存と保存しない）と保存権限がない時の処理継続
 */
async function checkEditBySave(): Promise<boolean> {
  //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
  if (
    !(await gyoumuCom.checkEdit(
      isEdit.value,
      await hasRegistAuth(Or30564Const.LINK_AUTH),
      showConfirmMessageBox,
      showWarnMessageBox,
      _save
    ))
  ) {
    return false
  }
  return true
}
/**
 * 保存時処理
 */
async function _save() {
  if (local.or30564.operaFlg !== Or30564Const.OPERA_FLAG_3) {
    if (!(await orX0139Ref.value?.isValid())) {
      return false
    }
  }

  //保存処理を行う。
  await gyoumuCom.doComLogicSave(
    local.or30564,
    isEdit.value,
    showMessageBox,
    getCarePlan1Data,
    saveApiData,
    setCommonPlanPeriod
  )
  return true
}

/**
 * 詳細情報データ
 */
const dataObj = computed(() => {
  return OrX0139Logic.data.get(orX0139_1.value.uniqueCpId)?.dataObj
})

/**
 * 保存処理を行う。
 */
async function saveApiData() {
  // 表示用「履歴」情報
  const rirekiObj = local.or30564.rirekiObj
  // 表示用「計画対象期間」情報
  const kikanObj = local.or30564.kikanObj
  const orX0009Data = OrX0009Logic.data.get(orX0009_1.value.uniqueCpId)
  // 履歴更新区分の設定
  let rireki_updateKbn = ''
  // 操作区分が3:削除の場合、保存用「履歴」情報.履歴IDが空白以外の場合
  if (local.or30564.operaFlg === Or30564Const.OPERA_FLAG_3 && rirekiObj.rirekiId) {
    rireki_updateKbn = UPDATE_KBN.DELETE
  } else if (
    local.or30564.operaFlg === Or30564Const.OPERA_FLAG_1 ||
    !rirekiObj.rirekiId ||
    rirekiObj.rirekiId === '0'
  ) {
    rireki_updateKbn = UPDATE_KBN.CREATE
  } else {
    rireki_updateKbn = UPDATE_KBN.UPDATE
  }
  const inputParam: Plan1UpdateInEntity = {
    ...gyoumuCom.getGyoumuComUpdateInEntity(
      local.or30564.operaFlg,
      local.or30564.kikanFlg,
      local.or30564.syubetuId,
      cmnRouteCom.getPlanPeriod()?.kikanId
    ),
    rirekiId: local.or30564.rirekiId,
    rirekiObj: {
      rirekiId: rirekiObj.rirekiId,
      createYmd: String(OrX0010Logic.data.get(orX0010_1.value.uniqueCpId)?.value),
      shokuId: orX0009Data?.staffId ?? '',
      shokuKnj: orX0009Data?.staffName ?? '',
      modifiedCnt: rirekiObj.modifiedCnt,
      updateKbn: rireki_updateKbn,
    },
    dataObj: {
      /** 意向 */
      ikouKnj: dataObj.value?.ikouKnj ?? '',
      /** 意見 */
      ikenKnj: dataObj.value?.ikenKnj ?? '',
      /** 方針 */
      houshinKnj: dataObj.value?.houshinKnj ?? '',
      /** 承諾日 */
      shudakuYmd: dataObj.value?.shudakuYmd ?? '',
      /** 計画区分 */
      kubun: dataObj.value?.kubun ?? '',
      /** 認定区分 */
      nintei: dataObj.value?.nintei ?? '',
      /** 初回作成日 */
      shokaiYmd: dataObj.value?.shokaiYmd ?? '',
      /** 認定日 */
      ninteiYmd: dataObj.value?.ninteiYmd ?? '',
      /** 有効開始日 */
      yukoSYmd: dataObj.value?.yukoSYmd ?? '',
      /** 有効終了日 */
      yukoEYmd: dataObj.value?.yukoEYmd ?? '',
      /** 要介護度 */
      yokaiKbn: dataObj.value?.yokaiKbn ?? '',
      /** 要介護見込みフラグ */
      mikomiFlg: dataObj.value?.mikomiFlg ?? '',
      /** 要介護度(その他) */
      sonotaKnj: dataObj.value?.sonotaKnj ?? '',
      /** 家事援助区分 */
      kajiCode: dataObj.value?.kajiCode ?? '',
      /** 家事援助(その他)*/
      kajiSonotaKnj: dataObj.value?.kajiSonotaKnj ?? '',
      /** 作成者所属(旧書式) */
      shozokuId: dataObj.value?.shozokuId ?? '',
      /** 地域コード(旧書式) */
      chiikiCd: dataObj.value?.chiikiCd ?? '',
      /** 調査対象者コード(旧書式) */
      taishoushaCd: dataObj.value?.taishoushaCd ?? '',
      /** 担当者ＩＤ */
      tantoId: dataObj.value?.tantoId ?? '',
      /** 担当者 */
      tantoKnj: dataObj.value?.tantoKnj ?? '',
      /** 暫定フラグ */
      zanteiFlg: dataObj.value?.zanteiFlg ?? '',
      /** 計画区分（初回） */
      kubun1: dataObj.value?.kubun1 ?? '',
      /** 計画区分（紹介） */
      kubun2: dataObj.value?.kubun2 ?? '',
      /** 計画区分（継続）*/
      kubun3: dataObj.value?.kubun3 ?? '',
      /** 更新回数*/
      modifiedCnt: dataObj.value?.modifiedCnt ?? '',
      /** ＩＤ */
      id: dataObj.value?.id ?? '',
    },
    //初期設定マスタの情報
    initMasterObj: local.or30564.initMasterObj,
  }

  //期間管理フラグが0:期間管理しない場合
  if (local.or30564.kikanFlg === '0') {
    //表示用「計画対象期間」情報.計画期間IDが空白の場合
    if (kikanObj.sc1Id === '' || kikanObj.sc1Id === '0') {
      inputParam.kikanObj = {
        sc1Id: kikanObj?.sc1Id ?? '',
        //画面の作成日
        startYmd: String(OrX0010Logic.data.get(orX0010_1.value.uniqueCpId)?.value),
        endYmd: kikanObj?.endYmd ?? '',
        modifiedCnt: kikanObj?.modifiedCnt ?? '',
        updateKbn: UPDATE_KBN.CREATE,
      }
    }
  }
  //画面情報の保存処理を行う。
  const ret: GyoumuComUpdateOutEntity = await ScreenRepository.update('plan1Update', inputParam)
  return ret
}

/**
 * onClickPrint
 */
async function onClickPrint() {
  //下記の条件がいずれか満たす場合、
  //・操作区分 = 3:削除の場合
  if (local.or30564.operaFlg === Or30564Const.OPERA_FLAG_3) {
    return
  }

  //変更、権限のチェック
  if (!(await checkEditBySave())) {
    return
  }

  // GUI01003［印刷設定］画面をポップアップで起動する
  openPrintSetting()
}

/**
 * openPrintSetting
 */
function openPrintSetting() {
  // 共通情報.法人ID
  localOneway.or29115Oneway.houjinId = systemCommonsStore.getHoujinId ?? ''
  // 共通情報.施設ID
  localOneway.or29115Oneway.shisetuId = systemCommonsStore.getShisetuId ?? ''
  // 共通情報.事業所ID
  localOneway.or29115Oneway.svJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  // 共通情報.事業所名
  localOneway.or29115Oneway.jigyoKnj =
    systemCommonsStore.getJigyoSelectInfoList.find(
      (x) => x.svJigyoId === systemCommonsStore.getSvJigyoId
    )?.jigyoRyakuKnj ?? ''
  // 共通情報.事業所CD
  localOneway.or29115Oneway.jigyoCd = systemCommonsStore.getSvJigyoCd ?? ''
  // 共通情報.適用事業所ID（※リスト）
  localOneway.or29115Oneway.svJigyoIdList = systemCommonsStore.getSvJigyoIdList as string[]
  // TODO 共通情報.担当ケアマネID
  localOneway.or29115Oneway.tantoShokuId = '0'
  // 共通情報.職員ID ※ログイン情報
  localOneway.or29115Oneway.shokuId = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
  // 共通情報.利用者ID ※リストに格納する
  localOneway.or29115Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  // 共通情報.開始(YYYY/MM/DD)
  localOneway.or29115Oneway.startDate = systemCommonsStore.getStartDate ?? ''
  // 共通情報.終了(YYYY/MM/DD)
  localOneway.or29115Oneway.endDate = systemCommonsStore.getEndDate ?? ''
  // 共通情報.システム年月日(YYYY/MM/DD)
  localOneway.or29115Oneway.sysYmd = systemCommonsStore.getSystemDate ?? ''
  // 共通情報.履歴ID
  localOneway.or29115Oneway.rirekiId = local.or30564.rirekiId ?? ''
  // 共通情報.ユーザリスト
  localOneway.or29115Oneway.userList =
    systemCommonsStore.getUserSelectUserList()?.map((x) => {
      return {
        userId: x.selfId,
        nameKnj: x.nameSei + ' ' + x.nameKanaMei,
        userNumber: x.selfId,
        sex: String(x.gender),
      } as userList
    }) ?? []
  // 共通情報.50音（※リスト）
  localOneway.or29115Oneway.gojuuOnKana =
    systemCommonsStore.getUserSelectFilterInitials() as string[]
  // 初期設定マスタの情報
  localOneway.or29115Oneway.initMasterObj = local.or30564.initMasterObj
  // 初期設定マスタの情報.計画書様式が1:施設の場合、"施設サービス計画書（１）"
  // 以外の場合、"居宅サービス計画書（１）"
  if (local.or30564.initMasterObj.cksFlg === Or30564Const.CKS_FLG_1) {
    localOneway.or29115Oneway.sectionName = Or30564Const.SECTION_NAME_1
  } else {
    localOneway.or29115Oneway.sectionName = Or30564Const.SECTION_NAME_2
  }
  // システムコード: "71101"
  localOneway.or29115Oneway.systemCode = Or30564Const.SYSTEM_CODE_71101
  // 計画期間管理フラグ
  localOneway.or29115Oneway.kikanFlg = local.or30564.kikanFlg

  // GUI00953［印刷設定］画面
  Or29115Logic.state.set({
    uniqueCpId: or29115_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「計画書一括印刷」押下
 */
async function onClickAllPrint() {
  //下記の条件がいずれか満たす場合、
  //・操作区分 = 3:削除の場合
  if (local.or30564.operaFlg === Or30564Const.OPERA_FLAG_3) {
    return
  }

  if (!(await checkEditBySave())) {
    return
  }
  //ポップアップで起動
  showPlanPrintALLDialog()
}

/**
 * 課題整理総括処理
 */
function onClickKadaiSeirisoukatsu() {
  orX0139Ref.value?.showKadaiSeirisoukatsu()
}

/**
 * 課題検討
 */
function onClickKadaikentou() {
  orX0139Ref.value?.showKadaikentou()
}
/**
 *ログを押下
 */
function onClickLog() {
  // TODO 共通処理 e-文書法対象機能の電子ファイル保存設定を取得する。
  // TODO 共通処理 e-文章法対象の帳票のXPSフォルダをエクスプローラで開く。
}

/**
 * ダイアログ表示フラグ(マスタ他のダイアログ)
 */
const showDialogOr10578 = computed(() => {
  // Or10578のダイアログ開閉状態
  return Or10578Logic.state.get(or10578_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ(計画書一括印刷のダイアログ)
 */
const showDialogOr10583 = computed(() => {
  // Or10583のダイアログ開閉状態
  return Or10583Logic.state.get(or10583_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ(印刷画面のダイアログ)
 */
const showDialogOr29115 = computed(() => {
  // Or29115のダイアログ開閉状態
  return Or29115Logic.state.get(or29115_1.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr32421 = computed(() => {
  return Or32421Logic.state.get(or32421_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * マスタ他のダイアログを表示
 */
function showMasterDialog() {
  //共通からの事業所IDリスト
  const svjigyoIdList: string[] = systemCommonsStore.getSvJigyoIdList as string[]
  //施設ID
  localOneway.or10578Oneway.carePlan1MasterInData.shisetuId = systemCommonsStore.getShisetuId ?? ''
  //事業所ID
  localOneway.or10578Oneway.carePlan1MasterInData.jigyoId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.or10578Oneway.carePlan1MasterInData.svJigyoIdList = svjigyoIdList
  // Or10578のダイアログ開閉状態を更新する
  Or10578Logic.state.set({
    uniqueCpId: or10578_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 計画書一括印刷のダイアログを表示
 */
function showPlanPrintALLDialog() {
  //共通情報.法人ID
  localOneway.or10583Oneway.carePlanPrintAllInData.legalPersonId =
    systemCommonsStore.getHoujinId ?? ''
  //共通情報.施設ID
  localOneway.or10583Oneway.carePlanPrintAllInData.shisetuId = systemCommonsStore.getShisetuId ?? ''
  //共通情報.事業所ID
  localOneway.or10583Oneway.carePlanPrintAllInData.officeData.officeId =
    systemCommonsStore.getSvJigyoId ?? ''
  localOneway.or10583Oneway.carePlanPrintAllInData.officeData.officeName =
    local.or30564.svJigyoRyakuKnj ?? ''
  //共通情報.計画書様式
  localOneway.or10583Oneway.carePlanPrintAllInData.carePlanStyle =
    local.or30564.initMasterObj.cksFlg
  //共通情報.サービス種別
  localOneway.or10583Oneway.carePlanPrintAllInData.jigyoTypeCd =
    systemCommonsStore.getSvJigyoCd ?? ''
  // 職員ID
  localOneway.or10583Oneway.carePlanPrintAllInData.employeeId =
    systemCommonsStore.getUserSelectSelfId() ?? ''
  // Or10583のダイアログ開閉状態を更新する
  Or10583Logic.state.set({
    uniqueCpId: or10583_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * ポップアップ画面が画面閉じる場合
 */
const getMasterDialogResult = () => {
  return new Promise((resolve) => {
    watch(
      () => Or10578Logic.state.get(or10578_1.value.uniqueCpId)?.isOpen,
      (newValue) => {
        resolve(newValue)
      },
      { once: true }
    )
  })
}

/**
 * 「マスタ他設定アイコンボタン」押下
 */
async function _master() {
  if (!(await checkEditBySave())) {
    return
  }
  //ポップアップで起動
  showMasterDialog()
  //ポップアップ画面が画面閉じる場合
  await getMasterDialogResult()
  //共通情報.計画期間ＩＤ
  setCommonPlanPeriod({ ...local.or30564.kikanObj, sc1Id: '0' })
  //共通情報.履歴ＩＤ
  local.or30564.rirekiId = '0'
  //操作区分
  local.or30564.operaFlg = Or30564Const.OPERA_FLAG_0
  //データ再取得
  await getCarePlan1Data()
}

/**
 * 「削除」押下
 */
async function _delete() {
  //下記の条件がいずれか満たす場合、
  //・操作区分 = 3:削除の場合
  //・表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    local.or30564.operaFlg === Or30564Const.OPERA_FLAG_3 ||
    local.or30564.kikanObj.pagingFlg === Or30564Const.PAGING_FLAG_0
  ) {
    return
  }
  const dialogResult = await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11326', [localComponents.orX0010.value, t('label.care-plan1')]),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
  if (dialogResult?.secondBtnClickFlg === true) {
    return
  }
  //操作区分 = 3:削除
  local.or30564.operaFlg = Or30564Const.OPERA_FLAG_3
  OrX0139Logic.data.set({
    uniqueCpId: orX0139_1.value.uniqueCpId,
    value: {
      dataObj: local.or30564.dataObj,
      deleteFlg: Or30564Const.OPERA_FLAG_3,
    },
  })
  //画面項目の非活性・非表示設定
}

/**
 * 確認ダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,

      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * メッセージを表示する
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openWarnDialog(state: Or21815StateType): Promise<Or21815EventType | undefined> {
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * 複写ボタン
 */
function onCreateCopy() {
  // 下記の条件がいずれか満たす場合、
  //・操作区分 = 3:削除の場合
  //・表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    local.or30564.operaFlg === Or30564Const.OPERA_FLAG_3 ||
    local.or30564.kikanObj.pagingFlg === Or30564Const.PAGING_FLAG_0
  ) {
    return
  }
  // GUI01006 計画書複写（計画書（1））画面をポップアップで起動する
  localOneway.or32421Oneway = {
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList as string[],
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    syubetuId: local.or30564.syubetuId ?? '',
    kikanFlg: local.or30564.kikanFlg ?? '',
    securityUseKhnFlg: local.or30564.securityUseKhnFlg ?? '',
    securityUseTaiKirokuFlg: local.or30564.securityUseTaiKirokuFlg ?? '',
    securityUseRyuijikouFlg: local.or30564.securityUseRyuijikouFlg ?? '',
    securityUseChoukiFlg: local.or30564.securityUseChoukiFlg ?? '',
    yokaiList: local.or30564.yokaiList ?? [],
    initMasterObj: local.or30564.initMasterObj,
  }
  Or32421Logic.state.set({
    uniqueCpId: or32421_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01006 計画書複写（計画書（1））画面がある場合
 *
 * @param newValue - ポップアップ返却情報
 */
async function handleOr32421Confirm(newValue: Or32421Type) {
  if (newValue?.rirekiId) {
    // 複写情報を取得する。
    const ret: Plan1CopyReturnSelectOutEntity = await ScreenRepository.select(
      'plan1CopyReturnSelect',
      { rirekiId: newValue.rirekiId } as Plan1CopyReturnSelectInEntity
    )
    if (!ret.data.dataObj) {
      return
    }

    const gamenDataObj = dataObj.value ?? ({} as IDataInfo)
    if (gamenDataObj) {
      // ・意向＝複写元.意向
      gamenDataObj.ikouKnj = ret.data.dataObj.ikouKnj ?? ''
      // ・意見＝複写元.意見
      gamenDataObj.ikenKnj = ret.data.dataObj.ikenKnj ?? ''
      // ・方針＝複写元.方針
      gamenDataObj.houshinKnj = ret.data.dataObj.houshinKnj ?? ''
      // ・家事援助区分＝複写元.家事援助区分
      gamenDataObj.kajiCode = ret.data.dataObj.kajiCode ?? ''
      // ・家事援助(その他)＝複写元.家事援助(その他)
      gamenDataObj.kajiSonotaKnj = ret.data.dataObj.kajiSonotaKnj ?? ''
      // ・担当者ＩＤ＝複写元.担当者ＩＤ
      gamenDataObj.tantoId = ret.data.dataObj.tantoId ?? ''
      // ・担当者＝複写元.担当者
      gamenDataObj.tantoKnj = ret.data.dataObj.tantoKnj ?? ''
    }
    // 複写用情報を本画面に設定する。
    OrX0139Logic.data.set({
      uniqueCpId: orX0139_1.value.uniqueCpId,
      value: {
        dataObj: gamenDataObj,
        deleteFlg: '',
      },
    })
    // 詳細情報表示データの設定
    orX0139Ref.value?.initOrX0139()
  }
}

/**
 * ナビゲーション制御領域のいずれかの編集フラグがON
 */
const isEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})
</script>

<template>
  <c-v-sheet class="view">
    <!-- Or11871：有機体：画面メニューエリア -->
    <div class="sticky-header">
      <g-base-or11871 v-bind="or11871">
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            @click="onCreateCopy()"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-copy-btn')"
            />
          </c-v-list-item>
        </template>
        <template #optionMenuItems>
          <!-- 課題整理総括 -->
          <c-v-list-item
            v-show="local.or30564.securityKadaiSeirisoukatsuFlg === Or30564Const.KENGEN_FLG_1"
            :title="t('label.organizing-issues')"
            prepend-icon="open_in_browser"
            @click="onClickKadaiSeirisoukatsu()"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-organizing-issues-icon-btn')"
            />
          </c-v-list-item>
          <!-- 課題検討 -->
          <c-v-list-item
            v-show="local.or30564.securityKadaiKentouFlg === Or30564Const.KENGEN_FLG_1"
            :title="t('label.consider-issues')"
            prepend-icon="open_in_browser"
            @click="onClickKadaikentou()"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.kaidai-kentou')"
            />
          </c-v-list-item>
          <!-- ログ -->
          <c-v-list-item
            v-show="systemCommonsStore.getEBunshoKbn === Or30564Const.KENGEN_FLG_1"
            :title="t('btn.log')"
            prepend-icon="open_in_browser"
            @click="onClickLog()"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-show-log-btn')"
            />
          </c-v-list-item>
        </template>
        <template #optionPrintItems>
          <!-- 印刷 -->
          <c-v-list-item
            :title="t('btn.print-configuration')"
            prepend-icon="open_in_browser"
            @click="onClickPrint()"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-print-setting-btn')"
            />
          </c-v-list-item>
          <!-- 計画書一括印刷 -->
          <c-v-list-item
            :title="t('btn.plan-all-print')"
            prepend-icon="open_in_browser"
            @click="onClickAllPrint()"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-batch-print-btn')"
            />
          </c-v-list-item>
        </template>
      </g-base-or11871>
    </div>
    <c-v-row
      no-gutters
      class="content-area d-flex overflow-hidden"
    >
      <c-v-col
        cols="auto"
        class="hidden-scroll h-100 user-select"
      >
        <g-base-or00248 v-bind="or00248"></g-base-or00248>
      </c-v-col>
      <c-v-col class="d-flex flex-column">
        <!-- コンテンツエリア -->
        <c-v-row
          no-gutters
          class="second-row"
        >
          <c-v-col cols="auto">
            <!-- 事業所 -->
            <g-base-or-41179 v-bind="or41179_1" />
          </c-v-col>
          <c-v-col
            v-show="local.or30564.kikanFlg === Or30564Const.PLAN_TARGET_PERIOD_1"
            cols="auto"
          >
            <!-- 計画対象期間 -->
            <g-custom-orX0007
              ref="orX0007Ref"
              v-bind="orX0007_1"
              :oneway-model-value="localOneway.orX0007Oneway"
              :unique-cp-id="orX0007_1.uniqueCpId"
              :parent-method="checkEditBySave"
            />
          </c-v-col>
          <c-v-col
            v-show="local.or30564.showOrX0008Flg"
            cols="auto"
          >
            <!-- 作成日 -->
            <g-custom-orX0010
              v-bind="orX0010_1"
              :oneway-model-value="localOneway.orX0010Oneway"
              :unique-cp-id="orX0010_1.uniqueCpId"
            />
          </c-v-col>
          <c-v-col
            v-show="local.or30564.showOrX0008Flg"
            cols="auto"
          >
            <!-- 作成者 -->
            <g-custom-orX0009
              v-bind="orX0009_1"
              :oneway-model-value="localOneway.orX0009Oneway"
              :unique-cp-id="orX0009_1.uniqueCpId"
            />
          </c-v-col>
          <c-v-col
            v-show="local.or30564.showOrX0008Flg"
            cols="auto"
          >
            <!-- 履歴 -->
            <g-custom-orX0008
              v-bind="orX0008_1"
              :oneway-model-value="localOneway.orX0008Oneway"
              :unique-cp-id="orX0008_1.uniqueCpId"
              :parent-method="checkEditBySave"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row class="detail-section">
          <!-- 詳細情報表示 -->
          <g-custom-orX0139
            v-if="
              local.or30564.showOrX0008Flg && local.or30564.operaFlg !== Or30564Const.OPERA_FLAG_3
            "
            ref="orX0139Ref"
            v-bind="orX0139_1"
          />
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>

  <!-- Or10578:有機体:モーダル（GUI00934 計画書（1）マスタモーダル） -->
  <g-custom-or-10578
    v-if="showDialogOr10578"
    v-bind="or10578_1"
    :oneway-model-value="localOneway.or10578Oneway"
  />
  <!-- Or10583:有機体:モーダル（GUI00936 計画書一括印刷） -->
  <g-custom-or-10583
    v-if="showDialogOr10583"
    v-bind="or10583_1"
    :oneway-model-value="localOneway.or10583Oneway"
  />
  <!-- Or29115:有機体:モーダル（GUI01003 印刷画面） -->
  <g-custom-or-29115
    v-if="showDialogOr29115"
    v-bind="or29115_1"
    :oneway-model-value="localOneway.or29115Oneway"
  />

  <!--GUI01006 計画書複写（GUI01006 計画書（1））画面-->
  <g-custom-or-32421
    v-if="showDialogOr32421"
    v-bind="or32421_1"
    :oneway-model-value="localOneway.or32421Oneway"
    :unique-cp-id="or32421_1.uniqueCpId"
    @update:model-value="handleOr32421Confirm"
  />
  <!-- 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <!-- INFO 警告ダイアログ -->
  <g-base-or21815 v-bind="or21815_1" />
</template>

<style scoped lang="scss">
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: rgb(var(--v-theme-background));
  min-height: max-content;
  .sticky-header {
    position: sticky;
    top: 48px;
    background-color: rgb(var(--v-theme-background));
    z-index: 999;
  }
}
.content-area {
  padding-top: 22px;
  padding-left: 23px;
}
.detail-section {
  margin: 24px 0 0 24px;
  padding-bottom: 117px;
}
.second-row {
  align-items: baseline;
  gap: 24px;
  padding-left: 24px;
  padding-bottom: 24px;
  border-bottom: solid thin rgb(var(--v-theme-light-blue-300));
  max-height: fit-content;
}
// 利用者一覧選択領域領域
:deep(.user-select > .v-card) {
  padding: 0px 0px 0px 0px !important; // パディング設定
  width: max-content !important; // 幅を自動調整
  // パンディング設定
  .v-card-actions {
    width: max-content !important; // 幅を自動調整
    padding: 8px !important; // パディング設定
  }
  // 選択領域の幅設定
  .user-info-section-container-style {
    width: 260px; // 幅を260pxに設定
  }
}
</style>
