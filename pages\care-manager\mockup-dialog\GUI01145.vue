<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import type { Or28334OnewayType, Or28334Type } from '~/types/cmn/business/components/Or28334Type'
import { Or28334Const } from '~/components/custom-components/organisms/Or28334/Or28334.constants'
import { Or28334Logic } from '~/components/custom-components/organisms/Or28334/Or28334.logic'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01145'
// ルーティング
const routing = 'GUI01145/pinia'
// 画面物理名
const screenName = 'GUI01145'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or28334 = ref({ uniqueCpId: Or28334Const.CP_ID(0) })
/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01145' },
})
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType
/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or28334Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or28334.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01145',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28334Const.CP_ID(0) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or28334Const.CP_ID(0)]: or28334.value,
})

// ダイアログ表示フラグ
const showDialogOr28334 = computed(() => {
  // Or28334のダイアログ開閉状態
  return Or28334Logic.state.get(or28334.value.uniqueCpId)?.isOpen ?? false
})

// 双方向バインド初期化
const local = reactive({
  Or28334: {
    specialInstructionsPeriodList: [],
  } as Or28334Type,
})

const or28334Data: Or28334OnewayType = {
  // 支援事業所id
  shienId: '1',
  // 利用者id
  userId: '1',
  // 提供年月
  yymmYm: '2025/06',
}
const localData = reactive({
  // 事業所ID
  shienId: { value: '1' } as Mo00045Type,
  userId: { value: '1' } as Mo00045Type,
  yymmYm: { value: '2025/06' } as Mo00045Type,
})
/**
 *  ボタン押下時の処理(Or28334)
 */
function onClickOr28334() {
  // Or28334のダイアログ開閉状態を更新する
  Or28334Logic.state.set({
    uniqueCpId: or28334.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function onClickOr28334_1() {
  or28334Data.shienId = localData.shienId.value
  or28334Data.userId = localData.userId.value
  or28334Data.yymmYm = localData.yymmYm.value
  Or28334Logic.state.set({
    uniqueCpId: or28334.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/06/12 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28334()"
        >GUI01145_特別指示期間
      </v-btn>
      <g-custom-or-28334
        v-if="showDialogOr28334"
        v-bind="or28334"
        v-model="local.Or28334"
        :oneway-model-value="or28334Data"
        :parent-unique-cp-id="pageComponent.uniqueCpId"
      />
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/06/12 ADD END-->
  <!-- POP画面ポップアップ SH 2025/10/29 ADD END-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">支援事業所id</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.shienId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者id</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">提供年月</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.yymmYm"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr28334_1"> GUI01145 疎通起動 </v-btn>
  </div>
</template>
