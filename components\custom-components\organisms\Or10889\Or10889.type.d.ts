import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01274Type } from '~/types/business/components/Mo01274Type'
import type { Mo01278Type } from '~/types/business/components/Mo01278Type'
import type { Mo01336OnewayType } from '~/types/business/components/Mo01336Type'
/**
 * Or10889:有機体:薬剤検索マスタ
 * GUI00670_薬剤検索マスタ
 *
 * OneWayBind領域用の構造
 */
export interface Or10889StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * Or10889:有機体:薬剤検索マスタ
 * DataTableのデータ
 */
export interface DataInfoType {
  /**
   * APIから取得情報
   */
  data: DataInfoDetailsType
}

/**
 * 各一覧明細情報取得
 */
export interface DataInfoDetailsType {
  /**
   * 薬剤分類リスト
   */
  drugBunList: DrugBun[]
  /**
   * 薬剤検索マスタリスト
   */
  drugList: MedicineSearchMaster[]
}

/**
 * 薬剤分類
 */
export interface DrugBun {
  /** 親ID */
  parentId: {
    onewayModelValue: Mo01336OnewayType
  }
  /** ID */
  bunruiId: {
    onewayModelValue: Mo01336OnewayType
  }
  /** 分類名称 */
  drugBunKnj: {
    modelValue: Mo00045Type
  }
  /** 表示順 */
  sort: {
    modelValue: Mo01278Type
  }
}

/**
 * 薬剤検索マスタ
 */
export interface MedicineSearchMaster {
  /** ID */
  drugId: {
    onewayModelValue: Mo01336OnewayType
  }
  /** 薬剤名 */
  drugKnj: {
    modelValue: Mo01274Type
  }
  /** 薬剤カナ */
  drugKana: {
    modelValue: Mo01274Type
  }
  /** 医薬品コード */
  drugCode: {
    modelValue: Mo01274Type
  }
  /** レセ電算コード */
  drugReceCode: {
    modelValue: Mo01274Type
  }
  /** 剤型 */
  shape: string
  /** 作用・効能等 */
  memoKnj: {
    modelValue: Mo01274Type
  }
  /** 表示順 */
  sort: {
    modelValue: Mo01278Type
  }
}
