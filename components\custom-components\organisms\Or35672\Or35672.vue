<script setup lang="ts">
/**
 * Or35672:アセスメント複写モーダル
 * GUI00807_アセスメント複写
 *
 * @description
 * アセスメント複写モーダル
 *
 * <AUTHOR>
 */

import { reactive, ref, watch, onMounted, computed, nextTick, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { Or28992Const } from '../Or28992/Or28992.constants'
import { Or29241Const } from '../Or29241/Or29241.constants'
import { TeX0002Const } from '../../template/TeX0002/TeX0002.constants'
import { OrX0209Const } from '../OrX0209/OrX0209.constants'
import type { Mo01337OnewayType } from '../../../../types/business/components/Mo01337Type'
import { Or31535Const } from '../Or31535/Or31535.constants'
import { Or29958Const } from '../Or29958/Or29958.constants'
import { Or33097Const } from '../Or33097/Or33097.constants'
import { Or29242Const } from '../Or29242/Or29242.constants'
import { Or00386Const } from '../Or00386/Or00386.constants'
import { Or01997Const } from '../Or01997/Or01997.constants'
import { Or30732Const } from '../Or30732/Or30732.constants'
import { Or30149Const } from '../Or30149/Or30149.constants'
import type { Or30149OnewayType } from '../Or30149/Or30149.type'
import { Or32643Const } from '../Or32643/Or32643.constants'
import { Or32081Const } from '../Or32081/Or32081.constants'
import { Or11131Const } from '../Or11131/Or11131.constants'
import { Or35672Const } from './Or35672.constants'
import type { Or35672StateType } from './Or35672.type'
import { Or35672Logic } from './Or35672.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type {
  Or35672OnewayType,
  Or35672Type,
  HistoryTableItemsType,
} from '~/types/cmn/business/components/Or35672Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { useScreenOneWayBind, useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { OrX0077OnewayType } from '~/types/cmn/business/components/OrX0077Type'
import type { OrX0073OnewayType } from '~/types/cmn/business/components/OrX0073Type'
import type {
  Mo01334Headers,
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type {
  AssessmentDuplicateSelectInEntity,
  AssessmentDuplicateSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentDuplicateSelectEntity'
import { ResBodyStatusCode } from '~/constants/api-constants'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Or28992OnewayType } from '~/types/cmn/business/components/Or28992Type'
import type { Or31535OnewayType } from '~/types/cmn/business/components/Or31535Type'
import type { Or29958OnewayType } from '~/types/cmn/business/components/Or29958Type'
import type { Mo00028OnewayType, Mo00028Type } from '~/types/business/components/Mo00028Type'
import { CustomClass } from '~/types/CustomClassType'
import type {
  IssuesAndGoalListItem,
  OrX0209OnewayType,
  OrX0209Type,
} from '~/types/cmn/business/components/OrX0209Type'
import type { Mo01280Type } from '~/types/business/components/Mo01280Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { Or32643OnewayType } from '~/types/cmn/business/components/Or32643Type'
import type { Or29241OnewayType } from '~/types/cmn/business/components/Or29241Type'
import type { Or33097OnewayType } from '~/types/cmn/business/components/Or33097Type'
import type { Or29242OnewayType } from '~/types/cmn/business/components/Or29242Type'
import type { Or00386OnewayType } from '~/types/cmn/business/components/Or00386Type'
import type { Or01997OnewayType } from '~/types/cmn/business/components/Or01997Type'
import type {
  AssessmentDuplicateUpdateInEntity,
  AssessmentDuplicateUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentDuplicateUpdateEntity'
import type {
  AssessmentDuplicateHistoryChangeInEntity,
  AssessmentDuplicateHistoryChangeOutEntity,
} from '~/repositories/cmn/entities/AssessmentDuplicateHistoryChangeEntity'
import type { Or32081OnewayType } from '~/types/cmn/business/components/Or32081Type'
import type { Or11131OnewayType } from '~/types/cmn/business/components/Or11131Type'
import type { Or30732OnewayType } from '~/types/cmn/business/components/Or30732Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or35672OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const orx0077 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const or32643 = ref({ uniqueCpId: '' })
const or28992 = ref({ uniqueCpId: '' })
const or29241 = ref({ uniqueCpId: '' })
const or33097 = ref({ uniqueCpId: '' })
const or29242 = ref({ uniqueCpId: '' })
const or32081 = ref({ uniqueCpId: '' })
const or11131 = ref({ uniqueCpId: '' })
const OrX0209 = ref({ uniqueCpId: '' })
const or00386 = ref({ uniqueCpId: '' })
const or01997 = ref({ uniqueCpId: '' })
const or30732 = ref({ uniqueCpId: '' })
const or31535 = ref({ uniqueCpId: '' })
const or29958 = ref({ uniqueCpId: '' })
const or30149 = ref({ uniqueCpId: '' })

// デフォルトOne-way
const defaultOnewayModelValue: Or35672OnewayType = {
  /** タブリスト */
  tabItems: [] as {
    /** id */
    id: string
    /** id */
    name: string
  }[],
  /** 改定フラグ */
  ninteiFormF: '',
  /** 選択中タブ */
  activeTabId: '',
  /** 事業所ID */
  jigyoId: '',
  /** 法人ID */
  houjinId: '',
  /** 施設ID */
  shisetuId: '',
  /** 利用者ID */
  userId: '',
  /** 計画対象期間ID */
  sc1Id: '',
  /** アセスメントID */
  gdlId: '',
  /** 作成者ID */
  createUserId: '',
  /** 作成日 */
  createYmd: '',
  /** 種別ID */
  syubetsuId: '',
  /** アセスメント番号 */
  accessmentNo: '',
  /** 適用事業所IDリスト */
  svJigyoIdList: [],
  /** 期間管理フラグ */
  kikanFlg: Or35672Const.DEFAULT.PERIOD_MANAGEMENT_FLG_0,
}

// One-way
const localOneway = reactive({
  Or35672Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 複写テンプレート
  orx0077Oneway: {
    // 複写ダイアログ
    mo00024Oneway: {
      maxWidth: '1580px',
      height: '880px',
      persistent: true,
      showCloseBtn: true,
      mo01344Oneway: {
        name: 'Or35672',
        toolbarTitle: t('label.assessment-duplicate'),
        toolbarName: 'Or35672ToolBar',
        // ツールバータイトルの左寄せ
        toolbarTitleCenteredFlg: false,
        showCardActions: true,
      },
    } as Mo00024OnewayType,
  } as OrX0077OnewayType,
  // 計画期間一覧
  mo01334Oneway_1: {
    headers: [] as Mo01334Headers[],
    items: [] as Mo01334Items[],
    height: '172',
  } as Mo01334OnewayType,
  // 履歴一覧
  mo01334Oneway_2: {
    headers: [] as Mo01334Headers[],
    items: [] as Mo01334Items[],
    height: '172',
  } as Mo01334OnewayType,
  // タブ
  mo00043OnewayType: {
    tabItems: [],
  } as Mo00043OnewayType,
  // 1
  or30732Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or30732OnewayType,
  // 2
  or31535Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or31535OnewayType,
  // 3
  or32643Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or32643OnewayType,
  // 5
  or29958Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or29958OnewayType,
  // 6-①
  or28992Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or28992OnewayType,
  // 6-②
  or29241Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or29241OnewayType,
  // 6 ⑤
  or33097Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or33097OnewayType,
  // 6⑥
  or30149Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or30149OnewayType,
  //6医
  or32081Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or32081OnewayType,
  or11131Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or11131OnewayType,
  or29242Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or29242OnewayType,
  or00386Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or00386OnewayType,
  or01997Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_COPY,
  } as Or01997OnewayType,
  // 解決すべき課題と目標
  mo00028OnewayType: {
    panelTitle: t('label.should-solution-issues-and-goal'),
    customClass: new CustomClass({
      itemClass: 'bold',
    }),
  } as Mo00028OnewayType,
  issuesAndGoalsListOneWay: {
    assessmentName: '',
  } as OrX0209OnewayType,
  mo01354Oneway: {
    headers: [] as Mo01354Headers[],
    height: '419',
    showSelect: true,
    selectStrategy: 'all',
    mandatory: false,
    showDragIndicatorFlg: false,
  } as Mo01354OnewayType,
  // 体位変換・起居タイトル
  mo01338Oneway: {
    /** 値のフォントの太さ */
    valueFontWeight: 'bold',
    /** 値 */
    value: '',
    /** カスタムクラス */
    customClass: new CustomClass({
      outerClass: '',
      labelClass: 'd-none',
      itemClass: '',
      itemStyle: 'color: rgb(var(--v-theme-red-700));font-size: 16px;',
    }),
  } as Mo01338OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 追加ボタン設置
  mo00609AddBtnOneway: {
    btnLabel: t('btn.confirm'),
    width: '90px',
    tooltipText: t('tooltip.confirm'),
  } as Mo00609OnewayType,
  orX0073Oneway: {
    cpyFlg: true,
    columnFlag: {},
    tableItem: [],
  } as unknown as OrX0073OnewayType,
})

// ローカル
const local = reactive({
  // タブ
  mo00043: {
    id: '',
  } as Mo00043Type,
  // 共通情報
  commonInfo: {
    params: {
      /** 改定フラグ */
      ninteiFormF: '',
      /** 選択中タブID */
      activeTabId: '',
      /** 事業所ID */
      jigyoId: '',
      /** 計画対象期間ID */
      sc1Id: '',
      /** アセスメントID */
      gdlId: '',
      /** 作成者ID */
      createUserId: '',
      /** 作成日 */
      createdYmd: '',
    },
    /** 戻りデータ */
    resData: {
      /** タブデータ */
      tabData: null,
      /** 課題と目標リスト */
      issuesAndGoalsList: [] as IssuesAndGoalListItem[],
    },
  } as unknown as Or35672Type,
  // 計画期間一覧
  mo01334_1: {
    value: '',
    values: [] as string[],
  } as Mo01334Type,
  // 履歴一覧
  mo01334_2: {
    value: '',
    values: [] as string[],
  } as Mo01334Type,
  // 解決すべき課題と目標
  mo00028: {
    panel: ref(1),
  } as Mo00028Type,
  // 複数タブ一覧
  mo01354: {
    values: {
      selectedRowId: '',
      selectedRowIds: [] as string[],
      items: [
        {
          id: '1',
          section: {
            value: t('label.assessment-duplicate-tab-name-1'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '2',
          section: {
            value: t('label.assessment-duplicate-tab-name-2'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '3',
          section: {
            value: t('label.assessment-duplicate-tab-name-3'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '4',
          section: {
            value: t('label.assessment-duplicate-tab-name-4'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '5',
          section: {
            value: t('label.assessment-duplicate-tab-name-5'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '6',
          section: {
            value: t('label.assessment-duplicate-tab-name-6'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '7',
          section: {
            value: t('label.assessment-duplicate-tab-name-7'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '8',
          section: {
            value: t('label.assessment-duplicate-tab-name-8'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '9',
          section: {
            value: t('label.assessment-duplicate-tab-name-9'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '10',
          section: {
            value: t('label.assessment-duplicate-tab-name-10'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '11',
          section: {
            value: t('label.assessment-duplicate-tab-name-11'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '12',
          section: {
            value: t('label.assessment-duplicate-tab-name-12'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '13',
          section: {
            value: t('label.assessment-duplicate-tab-name-13'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '14',
          section: {
            value: t('label.assessment-duplicate-tab-name-14'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '15',
          section: {
            value: t('label.assessment-duplicate-tab-name-15'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '16',
          section: {
            value: t('label.assessment-duplicate-tab-name-16'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or35672Const.DEFAULT.TAB_MARK_NO_USE,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
      ] as Mo01334Items[],
    },
  } as Mo01354Type,
  // 課題と目標リスト
  issuesAndGoalsList: {
    items: [] as IssuesAndGoalListItem[],
  } as OrX0209Type,
})

// ダイアログ表示フラグ (yes | no)
const showDialogor21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 警告ダイアログ表示フラグ
const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

// 表示中タブ名
const currentTabName = ref('1')

// データありフラグ
const isTabContentShow = ref(true)

// 処理フラグ
const isLoading = ref(false)

// 利用者ID
const userId = ref('')

// 履歴情報
const historyInfo = ref({
  gdlId: '',
  ninteiFormF: '',
  createUserId: '',
  createdYmd: '',
} as {
  gdlId: string
  ninteiFormF: string
  createUserId: string
  createdYmd: string
})

// アセスメント複写タブID -> メイン画面タブID変換関係
const tabMap = new Map<string, string>()
tabMap.set('1', '1')
tabMap.set('2', '2')
tabMap.set('3', '3')
tabMap.set('4', '4')
tabMap.set('5', '5')
tabMap.set('6', '6')
tabMap.set('7', '7')
tabMap.set('8', '8')
tabMap.set('9', '8')
tabMap.set('10', '9')
tabMap.set('11', '10')
tabMap.set('14', '11')
tabMap.set('15', '12')
tabMap.set('16', '13')

// リサイズ監視
const observer = ref<ResizeObserver | null>(null)

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or35672StateType>({
  cpId: Or35672Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      OrX0077Logic.state.set({
        uniqueCpId: orx0077.value.uniqueCpId,
        state: { isOpen: value ?? Or35672Const.DEFAULT.IS_OPEN },
      })
    },
    noData: (value) => {
      if (value) {
        isTabContentShow.value = false
      } else {
        isTabContentShow.value = true
      }
    },
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0077Const.CP_ID(0)]: orx0077.value,
  [Or32643Const.CP_ID(0)]: or32643.value,
  [Or28992Const.CP_ID(0)]: or28992.value,
  [Or29241Const.CP_ID(0)]: or29241.value,
  [OrX0209Const.CP_ID(0)]: OrX0209.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [Or33097Const.CP_ID(0)]: or33097.value,
  [Or29242Const.CP_ID(0)]: or29242.value,
  [Or32081Const.CP_ID(0)]: or32081.value,
  [Or11131Const.CP_ID(0)]: or11131.value,
  [Or00386Const.CP_ID(0)]: or00386.value,
  [Or01997Const.CP_ID(0)]: or01997.value,
  [Or30732Const.CP_ID(0)]: or30732.value,
  [Or31535Const.CP_ID(0)]: or31535.value,
  [Or29958Const.CP_ID(0)]: or29958.value,
  [Or30149Const.CP_ID(0)]: or30149.value,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  isLoading.value = true
  // コントロール初期化
  initControls()

  if (systemCommonsStore.getUserSelectSelfId(orx0077.value.uniqueCpId)) {
    userId.value = systemCommonsStore.getUserSelectSelfId(orx0077.value.uniqueCpId) ?? ''
    await getInitDataInfo('')
  }

  isLoading.value = false
})

onBeforeUnmount(() => {
  if (observer.value) {
    observer.value.disconnect()
  }
})

/**************************************************
 * ワッチャー
 **************************************************/
/**
 * One-way変更監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    localOneway.Or35672Oneway = {
      ...defaultOnewayModelValue,
      ...newValue,
    }

    // 画面共通情報を設定
    local.commonInfo.params = {
      ninteiFormF: newValue.ninteiFormF,
      activeTabId: newValue.activeTabId,
      jigyoId: newValue.jigyoId,
      houjinId: newValue.houjinId,
      shisetuId: newValue.shisetuId,
      userId: newValue.userId,
      sc1Id: newValue.sc1Id,
      gdlId: newValue.gdlId,
      createUserId: newValue.createUserId,
      createdYmd: newValue.createYmd,
      syubetsuId: newValue.syubetsuId,
      accessmentNo: newValue.accessmentNo,
      svJigyoIdList: newValue.svJigyoIdList,
      kikanFlg: newValue.kikanFlg,
    }

    // 選択中タブを設定
    newValue.tabItems?.forEach((item) => {
      if (item.id === newValue.activeTabId) {
        currentTabName.value = item.name
      }
    })

    // 複数タブ一覧を設定
    newValue.tabItems?.forEach((item) => {
      local.mo01354.values.items.forEach((cItem) => {
        if (item.id === tabMap.get(cItem.id)) {
          ;(cItem.historyRegisterCompleted as Mo01337OnewayType).value = ''
          ;(cItem.section as Mo01337OnewayType).customClass = new CustomClass({
            outerClass: 'h-100',
            labelClass: 'ma-0',
            itemClass: 'd-flex align-center h-100 px-4 py-2',
          })
          ;(cItem.historyRegisterCompleted as Mo01337OnewayType).customClass = new CustomClass({
            outerClass: 'h-100',
            labelClass: 'ma-0',
            itemClass: 'd-flex align-center h-100 px-4 py-2',
          })
        }
      })
    })

    // タブ使用しない場合、背景色を設定
    local.mo01354.values.items.forEach((item) => {
      if (
        Or35672Const.DEFAULT.TAB_MARK_NO_USE ===
        (item.historyRegisterCompleted as Mo01337OnewayType).value
      ) {
        // 背景色を設定
        ;(item.section as Mo01337OnewayType).customClass = new CustomClass({
          outerClass: 'h-100',
          outerStyle: 'background-color: rgb(var(--v-theme-black-200));',
          labelClass: 'ma-0',
          itemClass: 'ml-0 d-flex align-center h-100 px-4 py-2',
        })
        ;(item.historyRegisterCompleted as Mo01337OnewayType).customClass = new CustomClass({
          outerClass: 'h-100',
          outerStyle: 'background-color: rgb(var(--v-theme-black-200));',
          labelClass: 'ma-0',
          itemClass: 'ml-0 d-flex align-center h-100 px-4 py-2',
        })
      }
    })
  },
  { deep: true, immediate: true }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => OrX0077Logic.state.get(orx0077.value.uniqueCpId),
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue) {
      close()
    }
  }
)

// 計画期間一覧選択の変更を検知、行選択イベントを通知
watch(
  () => local.mo01334_1.value,
  (newValue) => {
    if (isLoading.value) {
      return
    }

    isLoading.value = true

    // 画面共通情報を設定
    setCommonInfo({
      params: {
        /** 計画期間ID */
        sc1Id: newValue,
      },
    })

    periodChange(newValue)

    isLoading.value = false
  }
)

// 履歴一覧選択の変更を検知、行選択イベントを通知
watch(
  () => local.mo01334_2.value,
  () => {
    if (isLoading.value) {
      return
    }

    // 履歴切替処理
    void historyChange()
  }
)

/**************************************************
 * イベント
 **************************************************/
/**
 * イベント発火
 *
 * @param event - イベント
 */
function setEvent(event: Record<string, boolean>) {
  // 画面共通情報を設定
  setCommonInfo({
    params: {
      /** 改定フラグ */
      ninteiFormF: historyInfo.value.ninteiFormF,
      /** 選択中タブID */
      activeTabId: local.commonInfo.params?.activeTabId,
      /** 事業所ID */
      jigyoId: local.commonInfo.params?.jigyoId,
      /** 法人ID */
      houjinId: local.commonInfo.params?.houjinId,
      /** 施設ID */
      shisetuId: local.commonInfo.params?.shisetuId,
      /** 利用者ID */
      userId: userId.value,
      /** 計画期間ID */
      sc1Id: local.commonInfo.params?.sc1Id,
      /** アセスメントID */
      gdlId: historyInfo.value.gdlId,
      /** 作成者ID */
      createUserId: historyInfo.value.createUserId,
      /** 履歴作成日 */
      createdYmd: historyInfo.value.createdYmd,
      /** 種別ID */
      syubetsuId: local.commonInfo.params?.syubetsuId,
      /** アセスメント番号 */
      accessmentNo: local.commonInfo.params?.accessmentNo,
      /** 適用事業所IDリスト */
      svJigyoIdList: local.commonInfo.params?.svJigyoIdList,
      /** 期間管理フラグ */
      kikanFlg: local.commonInfo.params?.kikanFlg,
      /** 課題と目標リスト */
      issuesAndGoalsList: local.issuesAndGoalsList.items ?? ([] as IssuesAndGoalListItem[]),
    },
  })

  Or35672Logic.event.set({
    uniqueCpId: Or35672Const.CP_ID(0) + props.uniqueCpId,
    events: {
      /** 再表示発火フラグ */
      reload: event.isRefresh,
      /** 確定発火フラグ */
      confirm: event.confirm,
      /** 確定発火フラグ */
      confirm_multiple: event.confirm_multiple,
    },
  })
}

/**************************************************
 * 関数
 **************************************************/
/**
 * コントロール初期化
 *
 */
function initControls() {
  // 計画期間一覧 ヘッダー
  localOneway.mo01334Oneway_1.headers = [
    {
      title: t('label.plan-period'),
      key: 'planPeriod',
      sortable: false,
      minWidth: '204',
    },
    {
      title: t('label.within-the-period-number-of-history'),
      key: 'numberOfWithinThePeriodHistory',
      sortable: false,
      minWidth: '72',
    },
    {
      title: t('label.office-name'),
      key: 'officeName',
      sortable: false,
      minWidth: '180',
    },
  ] as Mo01334Headers[]

  // 履歴一覧 ヘッダー
  localOneway.mo01334Oneway_2.headers = [
    {
      title: t('label.create-date'),
      key: 'createDate',
      sortable: false,
      minWidth: '100',
    },
    {
      title: t('label.author'),
      key: 'createUser',
      sortable: false,
      minWidth: '188',
    },
    {
      title: t('label.revision'),
      key: 'revision',
      sortable: false,
      minWidth: '46',
    },
  ] as Mo01334Headers[]

  // タブを初期化
  localOneway.mo00043OnewayType.tabItems = [
    {
      id: '1',
      title: currentTabName.value,
      tooltipText: currentTabName.value,
      tooltipLocation: 'top',
    },
    {
      id: '2',
      title: t('label.multiple'),
      tooltipText: t('label.multiple'),
      tooltipLocation: 'top',
    },
  ]

  // 一番目のタブを選択する
  local.mo00043.id = localOneway.mo00043OnewayType.tabItems[0].id

  // 複数タブ一覧 ヘッダー
  localOneway.mo01354Oneway.headers = [
    {
      title: t('label.section'),
      key: 'section',
      sortable: false,
    },
    {
      title: t('label.history-register-completed'),
      key: 'historyRegisterCompleted',
      sortable: false,
    },
  ] as Mo01334Headers[]

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
    },
  })

  // 警告ダイアログを初期化
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })

  localOneway.mo01334Oneway_1.items = []
}

/**
 *  画面共通情報を設定
 *
 * @param data - 設定情報
 */
function setCommonInfo(data: Or35672Type) {
  const existedData = Or35672Logic.data.get(props.uniqueCpId)
  const newData = {
    params: {
      /** 改定フラグ */
      ninteiFormF: data.params?.ninteiFormF ?? existedData?.params?.ninteiFormF,
      /** 選択中タブID */
      activeTabId: data.params?.activeTabId ?? existedData?.params?.activeTabId,
      /** 事業所ID */
      jigyoId: data.params?.jigyoId ?? existedData?.params?.jigyoId,
      /** 法人ID */
      houjinId: data.params?.houjinId ?? existedData?.params?.houjinId,
      /** 施設ID */
      shisetuId: data.params?.shisetuId ?? existedData?.params?.shisetuId,
      /** 利用者ID */
      userId: data.params?.userId ?? existedData?.params?.userId,
      /** 計画期間ID */
      sc1Id: data.params?.sc1Id ?? existedData?.params?.sc1Id,
      /** アセスメントID */
      gdlId: data.params?.gdlId ?? existedData?.params?.gdlId,
      /** 作成者ID */
      createUserId: data.params?.createUserId ?? existedData?.params?.createUserId,
      /** 履歴作成日 */
      createdYmd: data.params?.createdYmd ?? existedData?.params?.createdYmd,
      /** 種別ID */
      syubetsuId: data.params?.syubetsuId ?? existedData?.params?.syubetsuId,
      /** アセスメント番号 */
      accessmentNo: data.params?.accessmentNo ?? existedData?.params?.accessmentNo,
      /** 適用事業所IDリスト */
      svJigyoIdList: data.params?.svJigyoIdList ?? existedData?.params?.svJigyoIdList,
      /** 期間管理フラグ */
      kikanFlg: data.params?.kikanFlg ?? existedData?.params?.kikanFlg,
      /** 課題と目標リスト */
      issuesAndGoalsList:
        data.params?.issuesAndGoalsList ?? existedData?.params?.issuesAndGoalsList,
    },
    resData: {
      /** 課題と目標リスト */
      issuesAndGoalsList:
        data.resData?.issuesAndGoalsList ?? existedData?.resData?.issuesAndGoalsList,
      /** 利用者ID */
      userId: data.resData?.userId ?? existedData?.resData?.userId,
      /** 作成日 */
      createdYmd: data.resData?.createdYmd ?? existedData?.resData?.createdYmd,
      /** 認定フラグ */
      ninteiFormF: data.resData?.ninteiFormF ?? existedData?.resData?.ninteiFormF,
      /** 計画対象期間ID */
      sc1Id: data.resData?.sc1Id ?? existedData?.resData?.sc1Id,
      /** アセスメントID */
      gdlId: data.resData?.gdlId ?? existedData?.resData?.gdlId,
    },
  } as Or35672Type

  Or35672Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      params: {
        /** 改定フラグ */
        ninteiFormF: newData.params?.ninteiFormF,
        /** 選択中タブID */
        activeTabId: newData.params?.activeTabId,
        /** 事業所ID */
        jigyoId: newData.params?.jigyoId,
        /** 法人ID */
        houjinId: newData.params?.houjinId,
        /** 施設ID */
        shisetuId: newData.params?.shisetuId,
        /** 利用者ID */
        userId: newData.params?.userId,
        /** 計画期間ID */
        sc1Id: newData.params?.sc1Id,
        /** アセスメントID */
        gdlId: newData.params?.gdlId,
        /** 作成者ID */
        createUserId: newData.params?.createUserId,
        /** 履歴作成日 */
        createdYmd: newData.params?.createdYmd,
        /** 種別ID */
        syubetsuId: newData.params?.syubetsuId,
        /** アセスメント番号 */
        accessmentNo: newData.params?.accessmentNo,
        /** 適用事業所IDリスト */
        svJigyoIdList: newData.params?.svJigyoIdList,
        /** 期間管理フラグ */
        kikanFlg: newData.params?.kikanFlg,
        /** 課題と目標リスト */
        issuesAndGoalsList: cloneDeep(newData.params?.issuesAndGoalsList),
      },
      resData: {
        /** 課題と目標リスト */
        issuesAndGoalsList: cloneDeep(newData.resData?.issuesAndGoalsList),
        /** 利用者ID */
        userId: newData.resData?.userId,
        /** 作成日 */
        createdYmd: newData.resData?.createdYmd,
        /** 認定フラグ */
        ninteiFormF: newData.resData?.ninteiFormF,
        /** 計画対象期間ID */
        sc1Id: newData.resData?.sc1Id,
        /** アセスメントID */
        gdlId: newData.resData?.gdlId,
      },
    },
  })
}

/**
 *初期情報取得
 *
 * @description
 * 画面初期情報を取得する
 *
 * @param periodId - 計画期間ID
 */
async function getInitDataInfo(periodId: string) {
  // アセスメント複写初期情報取得
  const inputData: AssessmentDuplicateSelectInEntity = {
    // 種別ID
    syubetsuId: local.commonInfo.params?.syubetsuId ?? '',
    // 施設ID
    shisetuId: local.commonInfo.params?.shisetuId ?? '',
    // 事業者ID
    svJigyoId: local.commonInfo.params?.jigyoId ?? '',
    // 利用者ID
    userId: userId.value,
    // 計画期間ID
    sc1Id: '',
    // アセスメント番号
    accessmentNo: local.commonInfo.params?.accessmentNo ?? '',
    // 適用事業所IDリスト
    svJigyoIdList: local.commonInfo.params?.svJigyoIdList ?? [],
    // 期間管理フラグ
    kikanFlg: local.commonInfo.params?.kikanFlg ?? '',
  }
  // APIを実行
  const res: AssessmentDuplicateSelectOutEntity = await ScreenRepository.select(
    'assessmentDuplicateSelect',
    inputData
  )

  // API取得成功の場合
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    // 計画期間管理フラグが「1: 管理する」場合
    if (localOneway.Or35672Oneway.kikanFlg === Or35672Const.DEFAULT.PERIOD_MANAGEMENT_FLG_1) {
      // 引数の計画期間IDが空白の場合
      if (periodId.length <= 0) {
        // 計画期間一覧データ設定
        localOneway.mo01334Oneway_1.items = []
        res.data?.planPeriodList?.forEach((item) => {
          localOneway.mo01334Oneway_1.items.push({
            id: item.sc1Id,
            planPeriod: item.planPeriod,
            numberOfWithinThePeriodHistory: {
              value: item.dmyCnt,
              unit: '',
              /** カスタムクラス */
              customClass: new CustomClass({
                outerClass: 'mr-0',
                outerStyle: 'background-color: rgba(0, 0, 0, 0);',
                labelClass: 'ma-0',
                itemClass: 'ml-0',
              }),
            } as Mo01337OnewayType,
            officeName: item.jigyoKnj,
          })
        })

        // 計画期間一覧 選択行設定
        if (localOneway.mo01334Oneway_1.items.length > 0) {
          local.mo01334_1.value = localOneway.mo01334Oneway_1.items[0].id
        } else {
          local.mo01334_1.value = ''
        }
      }

      // 履歴一覧データ設定
      localOneway.mo01334Oneway_2.items = []
      res.data?.rirekiList?.forEach((item) => {
        if (item.sc1Id === local.mo01334_1.value) {
          const rirekiItem = {
            id: item.gdlId,
            periodId: item.sc1Id,
            createDate: item.asJisshiDateYmd,
            createUserId: item.shokuId,
            createUser: item.shokuKnj,
            revision: getRevisionName(item.ninteiFormF),
            ass1: item.ass1,
            ass2: item.ass2,
            ass3: item.ass3,
            ass4: item.ass4,
            ass5: item.ass5,
            ass6: item.ass6,
            ass7: item.ass7,
            ass8: item.ass8,
            ass9: item.ass9,
            ass10: item.ass10,
            ass11: item.ass11,
            ass12: item.ass12,
            ass13: item.ass13,
            ass14: item.ass14,
            ass15: item.ass15,
            ass16: item.ass16,
            ninteiFormF: item.ninteiFormF,
            issuesAndGoalsList: [] as IssuesAndGoalListItem[],
          } as HistoryTableItemsType

          localOneway.mo01334Oneway_2.items.push(rirekiItem)

          // 改訂フラグチェック
          checkKaiteiFlg(rirekiItem.ninteiFormF)
        }
      })
    } else {
      // 計画期間管理フラグが「0: 管理しない」場合
      localOneway.mo01334Oneway_2.items = []
      res.data?.rirekiList?.forEach((item) => {
        const rirekiItem = {
          id: item.gdlId,
          periodId: item.sc1Id,
          createDate: item.asJisshiDateYmd,
          createUserId: item.shokuId,
          createUser: item.shokuKnj,
          revision: getRevisionName(item.ninteiFormF),
          ass1: item.ass1,
          ass2: item.ass2,
          ass3: item.ass3,
          ass4: item.ass4,
          ass5: item.ass5,
          ass6: item.ass6,
          ass7: item.ass7,
          ass8: item.ass8,
          ass9: item.ass9,
          ass10: item.ass10,
          ass11: item.ass11,
          ass12: item.ass12,
          ass13: item.ass13,
          ass14: item.ass14,
          ass15: item.ass15,
          ass16: item.ass16,
          ninteiFormF: item.ninteiFormF,
          issuesAndGoalsList: [] as IssuesAndGoalListItem[],
        } as HistoryTableItemsType

        localOneway.mo01334Oneway_2.items.push(rirekiItem)
      })
    }

    // 課題と目標リスト
    local.issuesAndGoalsList.items = []
    res.data?.gdlKadaiList?.forEach((igItem) => {
      local.issuesAndGoalsList.items.push({
        /** id */
        id: uuidv4(),
        /** データID */
        dataId: igItem.id ?? '',
        /** アセスメントID */
        gdlId: igItem.gdlId ?? '',
        /** 計画期間ID */
        sc1Id: igItem.sc1Id ?? '',
        /** アセスメント番号 */
        assNo: igItem.assNo ?? '',
        /** アセスメント名 */
        assessmentName: {
          value: igItem.assName?.replaceAll('~r~n', '\r\n') ?? '',
        } as Mo01337OnewayType,
        /** 生活全般の解決すべき課題 */
        wholeLifeShouldSolutionIssues: {
          value: igItem.kadaiKnj,
        } as Mo01280Type,
        /** 長期目標 */
        longtermGoal: {
          value: igItem.choukiKnj,
        } as Mo01280Type,
        /** 短期目標 */
        shorttermGoal: {
          value: igItem.tankiKnj,
        } as Mo01280Type,
        /** シーケンス */
        seq: Number(igItem.seq) || 0,
      })
    })

    // 履歴データ一番目の行を選択状態にする
    if (localOneway.mo01334Oneway_2.items.length > 0) {
      local.mo01334_2.value = localOneway.mo01334Oneway_2.items[0].id

      // 履歴情報を保持
      const rirekiInfo = localOneway.mo01334Oneway_2.items[0] as HistoryTableItemsType
      historyInfo.value.gdlId = rirekiInfo.id
      historyInfo.value.ninteiFormF = rirekiInfo.ninteiFormF
      historyInfo.value.createUserId = rirekiInfo.createUserId
      historyInfo.value.createdYmd = rirekiInfo.createDate

      // 改訂フラグチェック
      checkKaiteiFlg(rirekiInfo.ninteiFormF)

      // タブ情報を設定
      setTabInfo(rirekiInfo)

      isTabContentShow.value = true

      // タブ内容を取得
      reload()
    } else {
      isTabContentShow.value = false
    }
  }

  await nextTick()

  // 編集アイコンを非表示にする
  const component = document.getElementById('or35672Component')
  if (component) {
    // 対象アイコン
    const iconTextList = ['edit_square']
    // 対象ラベル
    const labelTextList = [t('label.assessment-home-6-1-servey-ledger-import')]

    const iElements = component.querySelectorAll('i')
    iElements.forEach((el) => {
      if (el.textContent) {
        if (iconTextList.includes(el.textContent?.trim())) {
          el.style.display = 'none'
        }
      }
    })

    const serveyLedgerImportLabelElement = component.querySelectorAll('label')
    serveyLedgerImportLabelElement.forEach((el) => {
      if (el.textContent) {
        if (labelTextList.includes(el.textContent?.trim())) {
          el.style.display = 'none'
        }
      }
    })
  }

  // マスクの高さを設定
  const tabContent = document.getElementById('tabContent')!
  if (tabContent) {
    observer.value = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target.id === 'tabContent') {
          const mask = document.getElementById('mask')!
          const kadaiList = entry.target.querySelector('.issuesAndGoalListComponentWrapper')
          if (mask) {
            let height = entry.contentRect.height
            if (kadaiList) {
              height = height - kadaiList.getBoundingClientRect().height
            }
            mask.style.height = `${height}px`
          }
        }
      }
    })
    observer.value.observe(tabContent)
  }
}

/**
 * 改訂フラグをチェック
 *
 * @param kaiteiFlg - 改訂フラグ
 */
function checkKaiteiFlg(kaiteiFlg: string) {
  // 親画面.改訂フラグが選択した履歴情報の改訂フラグと一致しない場合、タブ内容を非表示
  if (local.commonInfo.params?.ninteiFormF !== kaiteiFlg) {
    isTabContentShow.value = false

    const intTgtKaiteiFlg = parseInt(local.commonInfo.params?.ninteiFormF ?? '-1')
    const intDefKaiteiFlg = parseInt(kaiteiFlg)
    if (intTgtKaiteiFlg < intDefKaiteiFlg) {
      // 親画面.改訂フラグ＜選択した履歴情報の改訂フラグの場合、下記のメッセージを表示
      // ※ R3/4改訂様式をそれ以前の様式に複写することは出来ません。
      localOneway.mo01338Oneway.value = t('label.assessment-duplicate-multiple-tab-description-1')
    } else if (intTgtKaiteiFlg - intDefKaiteiFlg === 1) {
      // 親画面.改訂フラグ-選択した履歴情報の改訂フラグ = 1場合、下記のメッセージを表示
      // ※ 全ての項目を一括して複写します。［改行］「確定」で閉じた場合、履歴に上書され更新されます。
      localOneway.mo01338Oneway.value = t('label.assessment-duplicate-multiple-tab-description-2')
    }
  } else {
    // 親画面.改訂フラグが選択した履歴情報の改訂フラグと一致する場合、下記のメッセージを表示
    // ※「確定」で閉じた場合、選択したアセスメントが計画期間内履歴に上書され更新されます。
    localOneway.mo01338Oneway.value = t('label.assessment-duplicate-multiple-tab-description-3')
  }
}

/**
 * 計画期間切替処理
 *
 * @description
 * 計画期間切替の時に対する履歴データをチェンジする
 *
 * @param periodId - 期間ID
 */
function periodChange(periodId: string) {
  // 画面情報を再取得
  void getInitDataInfo(periodId)
}

/**
 * 履歴切替処理
 *
 * @description
 * 履歴切替の時の処理をする
 */
async function historyChange() {
  // アセスメント複写初期情報取得
  const inputData: AssessmentDuplicateHistoryChangeInEntity = {
    // 計画期間ID
    sc1Id: local.mo01334_1.value,
    // アセスメントID
    gdlId: local.mo01334_2.value,
    /** アセスメント番号 */
    accessmentNo: local.commonInfo.params?.accessmentNo ?? '',
  }
  // APIを実行
  const res: AssessmentDuplicateHistoryChangeOutEntity = await ScreenRepository.select(
    'assessmentDuplicateHistoryChangeSelect',
    inputData
  )

  // API取得成功の場合
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    if (res.data.rirekiList) {
      if (res.data.rirekiList.length > 0) {
        const rirekiItem = {
          id: res.data.rirekiList[0].gdlId,
          periodId: res.data.rirekiList[0].sc1Id,
          createDate: res.data.rirekiList[0].asJisshiDateYmd,
          createUserId: res.data.rirekiList[0].shokuId,
          createUser: res.data.rirekiList[0].shokuKnj,
          revision: getRevisionName(res.data.rirekiList[0].ninteiFormF),
          ass1: res.data.rirekiList[0].ass1,
          ass2: res.data.rirekiList[0].ass2,
          ass3: res.data.rirekiList[0].ass3,
          ass4: res.data.rirekiList[0].ass4,
          ass5: res.data.rirekiList[0].ass5,
          ass6: res.data.rirekiList[0].ass6,
          ass7: res.data.rirekiList[0].ass7,
          ass8: res.data.rirekiList[0].ass8,
          ass9: res.data.rirekiList[0].ass9,
          ass10: res.data.rirekiList[0].ass10,
          ass11: res.data.rirekiList[0].ass11,
          ass12: res.data.rirekiList[0].ass12,
          ass13: res.data.rirekiList[0].ass13,
          ass14: res.data.rirekiList[0].ass14,
          ass15: res.data.rirekiList[0].ass15,
          ass16: res.data.rirekiList[0].ass16,
          ninteiFormF: res.data.rirekiList[0].ninteiFormF,
          issuesAndGoalsList: [] as IssuesAndGoalListItem[],
        } as HistoryTableItemsType

        historyInfo.value.gdlId = rirekiItem.id
        historyInfo.value.ninteiFormF = rirekiItem.ninteiFormF
        historyInfo.value.createUserId = rirekiItem.createUserId
        historyInfo.value.createdYmd = rirekiItem.createDate

        // 改訂フラグチェック
        checkKaiteiFlg(rirekiItem.ninteiFormF)

        // タブ情報を設定
        setTabInfo(rirekiItem)

        // 課題と目標リスト
        local.issuesAndGoalsList.items = []
        res.data.gdlKadaiList?.forEach((igItem) => {
          local.issuesAndGoalsList.items.push({
            /** id */
            id: uuidv4(),
            /** データID */
            dataId: igItem.id ?? '',
            /** アセスメントID */
            gdlId: igItem.gdlId ?? '',
            /** 計画期間ID */
            sc1Id: igItem.sc1Id ?? '',
            /** アセスメント番号 */
            assNo: igItem.assNo ?? '',
            /** アセスメント名 */
            assessmentName: {
              value: igItem.assName?.replaceAll('~r~n', '\r\n') ?? '',
            } as Mo01337OnewayType,
            /** 生活全般の解決すべき課題 */
            wholeLifeShouldSolutionIssues: {
              value: igItem.kadaiKnj,
            } as Mo01280Type,
            /** 長期目標 */
            longtermGoal: {
              value: igItem.choukiKnj,
            } as Mo01280Type,
            /** 短期目標 */
            shorttermGoal: {
              value: igItem.tankiKnj,
            } as Mo01280Type,
            /** シーケンス */
            seq: Number(igItem.seq) || 0,
          })
        })

        // 画面情報を再取得
        reload()
      }
    }
  }
}

/**
 * 履歴データを設定
 *
 * @description
 * 履歴データを設定する
 *
 * @param historyRowData - 履歴行データ
 */
function setTabInfo(historyRowData: HistoryTableItemsType) {
  // 複数タブ一覧を設定
  local.mo01354.values.items.forEach((item) => {
    if (
      Or35672Const.DEFAULT.TAB_MARK_NO_USE !==
      (item.historyRegisterCompleted as Mo01337OnewayType).value
    ) {
      switch (item.id) {
        case '1':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass1
          break
        case '2':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass2
          break
        case '3':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass3
          break
        case '4':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass4
          break
        case '5':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass5
          break
        case '6':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass6
          break
        case '7':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass7
          break
        case '8':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass8
          break
        case '9':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass9
          break
        case '10':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass10
          break
        case '11':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass11
          break
        case '12':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass12
          break
        case '13':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass13
          break
        case '14':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass14
          break
        case '15':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass15
          break
        case '16':
          ;(item.historyRegisterCompleted as Mo01337OnewayType).value = historyRowData.ass16
          break
      }

      if (
        (item.historyRegisterCompleted as Mo01337OnewayType).value ===
        Or35672Const.DEFAULT.TAB_MARK_DATA_EXISTS
      ) {
        item.selectable = true
      }
    }
  })

  // 一番目のタブを表示
  local.mo00043.id = localOneway.mo00043OnewayType.tabItems?.[0].id
}

/**
 * 画面情報再取得
 *
 * @description
 * 画面閉じる処理
 */
function reload() {
  setEvent({ isRefresh: true })
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 画面閉じる処理
 */
function close() {
  // 画面を閉じる。
  setState({ isOpen: false })
}

/**
 * 確定ボタン押下時の処理
 *
 * @description
 * 「複数」以外のタブの場合、確定ボタン押下時に、選択されている履歴に対するタブデータを上書する
 * 「複数」のタブの場合、確定ボタン押下時に、選択されている履歴に対する複数のタブデータを上書する
 */
async function onClickConfirm() {
  // 履歴が未選択の場合
  // メッセージID：i.cmn.11289
  // メッセージ内容："履歴を選択してください。"
  if (
    local.mo01334_2.value === undefined ||
    local.mo01334_2.value === '' ||
    isTabContentShow.value === false
  ) {
    openWarningDialog(t('message.i-cmn-11289'))

    return
  }

  if (local.mo00043.id === Or35672Const.DEFAULT.TAB_ID_MULTIPLE) {
    if (local.mo01354.values.selectedRowIds.length <= 0) {
      openWarningDialog(t('message.i-cmn-11289'))

      return
    }
  }

  // H21様式の履歴をそれ以前の様式に複写しようとした場合
  // 選択中の履歴の改訂フラグ
  let ninteiFormF = '-1'
  const historyItems = cloneDeep(
    localOneway.mo01334Oneway_2.items as Mo01334Items[]
  ) as HistoryTableItemsType[]
  const selectedHistoryRow = historyItems.filter((item) => item.id === local.mo01334_2.value)
  if (selectedHistoryRow.length > 0) {
    ninteiFormF = selectedHistoryRow[0].ninteiFormF
  }
  if (ninteiFormF !== undefined && ninteiFormF !== '') {
    if (parseInt(ninteiFormF) >= 4 && parseInt(local.commonInfo.params?.ninteiFormF ?? '-1') < 4) {
      openWarningDialog(t('message.i-cmn-10805'))

      return
    }
  }

  // 各タブ選択時 複写対象の履歴を選択済みの場合
  if (local.mo00043.id === Or35672Const.DEFAULT.TAB_ID_ASSESSMENT_TAB) {
    const dialogResult = await openConfirmDialog(t('message.i-cmn-10193', [t('label.assessment')]))
    switch (dialogResult) {
      // はい
      case 'yes': {
        // 課題と目標リストを設定
        const issuesAndGoalsList = [] as IssuesAndGoalListItem[]
        local.issuesAndGoalsList.items.forEach((item) => {
          issuesAndGoalsList.push(item)
        })

        // 戻りデータを設定
        const copyData = Or35672Logic.data.get(props.uniqueCpId)

        if (copyData) {
          if (copyData.resData) {
            copyData.resData.userId = userId.value
            copyData.resData.createdYmd = historyInfo.value.createdYmd
            copyData.resData.ninteiFormF = historyInfo.value.ninteiFormF
            copyData.resData.sc1Id = local.mo01334_1.value
            copyData.resData.gdlId = local.mo01334_2.value
            copyData.resData.issuesAndGoalsList = issuesAndGoalsList

            Or35672Logic.data.set({
              uniqueCpId: props.uniqueCpId,
              value: copyData,
            })
          }
        }

        setEvent({ confirm: true })

        // 画面を閉じる。
        setState({ isOpen: false })
        break
      }
      case 'no':
        // いいえ
        break
    }
  } else if (local.mo00043.id === Or35672Const.DEFAULT.TAB_ID_MULTIPLE) {
    // 複写元の対象タブを絞込み
    const targetTabIds = [] as string[]
    local.mo01354.values.items.forEach((item) => {
      if (local.mo01354.values.selectedRowIds.includes(item.id)) {
        if (
          (item.historyRegisterCompleted as Mo01337OnewayType).value ===
          Or35672Const.DEFAULT.TAB_MARK_DATA_EXISTS
        ) {
          targetTabIds.push(item.id)
        }
      }
    })

    // アセスメント複写初期情報取得
    const inputData: AssessmentDuplicateUpdateInEntity = {
      /** 法人ID */
      houjinId: local.commonInfo.params?.houjinId ?? '',
      /** 施設ID */
      shisetuId: local.commonInfo.params?.shisetuId ?? '',
      /** 利用者ID */
      userId: userId.value,
      /** 事業所ID */
      svJigyoId: local.commonInfo.params?.jigyoId ?? '',
      /** 複写先アセスメントID */
      tgtGdlId: local.commonInfo.params?.gdlId ?? '',
      /** 複写先期間ID */
      tgtSc1Id: local.commonInfo.params?.sc1Id ?? '',
      /** 画面Noリスト */
      noList: targetTabIds,
      /** 改訂フラグ */
      ninteiFlg: local.commonInfo.params?.ninteiFormF ?? '',
      /** 作成日 */
      createYmd: local.commonInfo.params?.createdYmd ?? '',
      /** 記載者ID */
      shokuId: local.commonInfo.params?.createUserId ?? '',
      /** 複写元期間ID */
      defSc1Id: local.mo01334_1.value,
      /** 複写元アセスメントID */
      defGdlId: local.mo01334_2.value,
      /** 種別ID */
      syubetsuId: local.commonInfo.params?.syubetsuId ?? '',
    }
    // ［計画書複写］（計画書（2））初期情報取得
    const resData: AssessmentDuplicateUpdateOutEntity = await ScreenRepository.update(
      'assessmentDuplicateUpdate',
      inputData
    )
    /****************************************
     * 保存成功の場合
     ****************************************/
    if (resData.statusCode === 'success') {
      // 表示中タブが上書対象になる場合、画面を再表示
      if (local.commonInfo.params?.activeTabId) {
        if (targetTabIds.includes(local.commonInfo.params?.activeTabId)) {
          setEvent({ confirm_multiple: true })
        }
      }

      // 画面を閉じる。
      setState({ isOpen: false })
    }
  }
}

/**
 * 利用者選択
 *
 * @param newUserId - 変更された利用者ID
 */
async function onChangeUserSelect(newUserId: string) {
  if (isLoading.value) {
    return
  }

  if (newUserId.length <= 0) {
    return
  }

  isLoading.value = true
  userId.value = newUserId
  await getInitDataInfo('')
  isLoading.value = false
}

/**
 * 改訂名を取得
 *
 * @param revisionFlg - 改訂フラグ
 */
function getRevisionName(revisionFlg: string) {
  let revisionName = ''
  switch (revisionFlg) {
    case '1':
      revisionName = Or35672Const.DEFAULT.REVISION_H12
      break
    case '2':
      revisionName = Or35672Const.DEFAULT.REVISION_H15
      break
    case '3':
      revisionName = Or35672Const.DEFAULT.REVISION_H18
      break
    case '4':
      revisionName = Or35672Const.DEFAULT.REVISION_H21
      break
    case '5':
      revisionName = Or35672Const.DEFAULT.REVISION_R3
      break
  }

  return revisionName
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
async function openConfirmDialog(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 */
function openWarningDialog(paramDialogText: string) {
  // 警告ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
}
</script>

<template>
  <!-- 複写テンプレート -->
  <g-custom-or-x-0077
    id="or35672Component"
    v-bind="orx0077"
    :oneway-model-value="localOneway.orx0077Oneway"
    @on-change-user-select="onChangeUserSelect"
  >
    <!-- テーブル -->
    <template #filter>
      <c-v-row no-gutters>
        <!-- 計画期間一覧 -->
        <c-v-col
          v-if="localOneway.Or35672Oneway.kikanFlg === Or35672Const.DEFAULT.PERIOD_MANAGEMENT_FLG_1"
          class="pr-2 table-header"
        >
          <base-mo-01334
            v-model="local.mo01334_1"
            :oneway-model-value="localOneway.mo01334Oneway_1"
            class="list-wrapper"
          >
            <template #[`item.numberOfWithinThePeriodHistory`]="{ item }">
              <div class="d-flex justify-end">
                <base-mo01337 :oneway-model-value="item.numberOfWithinThePeriodHistory" />
              </div>
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <!-- 履歴一覧 -->
        <c-v-col
          cols="6"
          class="pl-2 table-header"
        >
          <base-mo-01334
            v-model="local.mo01334_2"
            :oneway-model-value="localOneway.mo01334Oneway_2"
            class="list-wrapper"
          >
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- 複写body -->
    <template #copyMain>
      <!-- タブ -->
      <base-mo00043
        v-model="local.mo00043"
        :oneway-model-value="localOneway.mo00043OnewayType"
      >
      </base-mo00043>
      <!-- タブコンテンツ -->
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item :value="Or35672Const.DEFAULT.TAB_ID_ASSESSMENT_TAB">
          <div
            class="d-flex flex-column ma-0"
            style="min-height: 0; max-height: 532px"
          >
            <div
              v-show="isTabContentShow === false"
              class="flex-1-1"
              style="min-height: 0"
            >
              <div style="min-height: 532px"></div>
            </div>
            <div
              v-show="isTabContentShow === true"
              class="position-relative flex-1-1 overflow-y-auto"
              style="min-height: 0"
            >
              <!-- 画面編集できないようにマスクをつけ -->
              <div
                id="mask"
                class="position-absolute"
                style="width: 100%; height: 100%; z-index: 999"
              ></div>
              <div
                id="tabContent"
                class="pt-4"
              >
                <!-- 1 -->
                <g-custom-or-30732
                  v-if="localOneway.Or35672Oneway.activeTabId === Or30732Const.DEFAULT.TAB_ID"
                  v-bind="or30732"
                  :oneway-model-value="localOneway.or30732Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-30732>
                <!-- 2 -->
                <!-- <g-custom-or-31535
                  v-if="localOneway.Or35672Oneway.activeTabId === Or31535Const.DEFAULT.TAB_ID"
                  v-bind="or31535"
                  :oneway-model-value="localOneway.or31535Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-31535> -->
                <!-- 3 -->
                <g-custom-or-32643
                  v-if="localOneway.Or32643Oneway.activeTabId === Or32643Const.DEFAULT.TAB_ID"
                  v-bind="or32643"
                  :oneway-model-value="localOneway.or32643Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-32643>
                <!-- 6-⑤ -->
                <!-- <g-custom-or-32643
                  v-if="localOneway.Or35672Oneway.activeTabId === Or32643Const.DEFAULT.TAB_ID"
                  v-bind="or32643"
                  :oneway-model-value="localOneway.or32643Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-32643> -->
                <!-- 5 -->
                <!-- <g-custom-or-29958
                  v-if="localOneway.Or35672Oneway.activeTabId === Or29958Const.DEFAULT.TAB_ID"
                  v-bind="or29958"
                  :oneway-model-value="localOneway.or29958Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-29958> -->
                <!-- 6-① -->
                <!-- <g-custom-or-28992
                  v-if="localOneway.Or35672Oneway.activeTabId === Or28992Const.DEFAULT.TAB_ID"
                  v-bind="or28992"
                  :oneway-model-value="localOneway.or28992Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-28992> -->
                <!-- 6-② -->
                <!-- <g-custom-or-29241
                  v-if="localOneway.Or35672Oneway.activeTabId === Or29241Const.DEFAULT.TAB_ID"
                  v-bind="or29241"
                  :oneway-model-value="localOneway.or29241Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                /> -->
                <!-- 6-⑤ -->
                <!-- <g-custom-or-33097
                  v-if="localOneway.Or35672Oneway.activeTabId === Or33097Const.DEFAULT.TAB_ID"
                  v-bind="or33097"
                  :oneway-model-value="localOneway.or33097Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-33097> -->
                <!-- 6③-④  -->
                <!-- <g-custom-or-29242
                  v-if="localOneway.Or35672Oneway.activeTabId === Or29242Const.DEFAULT.TAB_ID"
                  v-bind="or29242"
                  :oneway-model-value="localOneway.or29242Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-29242> -->
                <!-- 6③-④  -->
                <g-custom-or-00386
                  v-if="localOneway.Or35672Oneway.activeTabId === Or00386Const.DEFAULT.TAB_ID"
                  v-bind="or00386"
                  :oneway-model-value="localOneway.or00386Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-00386>
                <!-- 6-⑥ -->
                <g-custom-or-30149
                  v-if="localOneway.Or35672Oneway.activeTabId === Or30149Const.DEFAULT.TAB_ID"
                  v-bind="or30149"
                  :oneway-model-value="localOneway.or30149Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-30149>
                <!-- 6-⑦6-医 -->
                <!-- <g-custom-or-32081
                  v-if="localOneway.Or35672Oneway.activeTabId === Or32081Const.DEFAULT.TAB_ID"
                  v-bind="or32081"
                  :oneway-model-value="localOneway.or32081Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-32081> -->
                <!-- 7--2：7スケジュール -->
                <!-- <g-custom-or-11131
                  v-if="localOneway.Or35672Oneway.activeTabId === Or11131Const.DEFAULT.TAB_ID"
                  v-bind="or11131"
                  :oneway-model-value="localOneway.or11131Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-11131> -->
                <g-custom-or-01997
                  v-if="localOneway.Or35672Oneway.activeTabId === Or01997Const.DEFAULT.TAB_ID"
                  v-bind="or01997"
                  :oneway-model-value="localOneway.or01997Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                ></g-custom-or-01997>
              </div>
            </div>
          </div>
        </c-v-window-item>
        <c-v-window-item
          :value="Or35672Const.DEFAULT.TAB_ID_MULTIPLE"
          style="min-height: 528px"
        >
          <c-v-row
            v-if="isTabContentShow"
            nogutters
            class="pt-4"
          >
            <c-v-col
              cols="4"
              class="table-header"
            >
              <base-mo-01354
                v-model="local.mo01354"
                :oneway-model-value="localOneway.mo01354Oneway"
                class="list-wrapper"
              >
                <!-- データ行の選択チェックボックスを置き換え -->
                <template #[`item.data-table-select`]="{ item, internalItem, isSelected }">
                  <c-v-row
                    no-gutters
                    align="center"
                    class="h-100 ml-n2"
                    style="width: calc(100% + 16px)"
                    :class="{
                      'disabled-row':
                        Or35672Const.DEFAULT.TAB_MARK_NO_USE ===
                        (item.historyRegisterCompleted as Mo01337OnewayType).value,
                    }"
                  >
                    <!-- 行の選択チェックボックス -->
                    <c-v-col>
                      <base-at-checkbox
                        :model-value="isSelected(internalItem)"
                        checkbox-label=""
                        readonly
                        :disabled="internalItem.selectable === false ? true : false"
                      />
                    </c-v-col>
                  </c-v-row>
                </template>
                <template #[`item.section`]="{ item }">
                  <base-mo01337 :oneway-model-value="item.section" />
                </template>
                <template #[`item.historyRegisterCompleted`]="{ item }">
                  <base-mo01337 :oneway-model-value="item.historyRegisterCompleted" />
                </template>
                <!-- ページングを非表示 -->
                <template #bottom />
              </base-mo-01354>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="isTabContentShow"
            nogutters
          >
            <c-v-col>
              <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
            </c-v-col>
          </c-v-row>
        </c-v-window-item>
      </c-v-window>
    </template>
    <!-- フッターボタン -->
    <template #footer>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="t('tooltip.screen-close')"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609AddBtnOneway"
          @click="onClickConfirm()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="t('tooltip.confirm')"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </g-custom-or-x-0077>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogor21814"
    v-bind="or21814"
  />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
$margin-organism: 24px; // オーガニズム間のマージン
$margin-common: 48px; // 一般的なマージン

.disabled-row {
  background-color: rgb(var(--v-theme-black-200));
}

.select-checkbox-col {
  :deep(.v-selection-control) {
    justify-content: center;
  }
}

.red-description {
  color: rgb(var(--v-theme-red-700));
  font-size: 18px;
}

/***************************************************************
 * 分子：「Mo00043_タブ選択」スタイル 補足
 ***************************************************************/
:deep(.v-slide-group) {
  height: 36px !important; // 高さを36pxに設定
  padding: 0px 24px !important; // パディングを解除
  margin-top: $margin-organism; // 上部マージン
  margin-bottom: $margin-organism; // 下部マージン
}
// 選択タブアイテム設定
:deep(.v-slide-group__content > .v-tab) {
  height: 36px !important; // 高さを36pxに設定
  padding: 0px 30px !important; // パディング設定
  width: max-content !important; // 幅を自動調整
  min-width: 44px !important; // 最小幅を自動調整
}

/***************************************************************
 * 分子：「Mo00039_ラジオボタングループ」スタイル 補足
 ***************************************************************/
:deep(.v-selection-control .v-label) {
  margin-left: 8px !important;
}

:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 24px !important;
}

:deep(.v-radio:not(:last-child)) {
  margin-right: $margin-organism !important;
}
</style>
