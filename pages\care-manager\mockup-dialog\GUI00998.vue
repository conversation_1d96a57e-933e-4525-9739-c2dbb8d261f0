<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or28646Logic } from '~/components/custom-components/organisms/Or28646/Or28646.logic'
import { Or28646Const } from '~/components/custom-components/organisms/Or28646/Or28646.constants'
import type { WeekTableImportType, Or28646Type } from '~/types/cmn/business/components/Or28646Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00998'
// ルーティング
const routing = 'GUI00998/pinia'
// 画面物理名
const screenName = 'GUI00998'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or28646 = ref({ uniqueCpId: '' })
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType
/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00998' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or28646Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or28646.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const { childCpIds } = useInitialize({
  cpId: 'GUI00998',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28646Const.CP_ID(0) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or28646Const.CP_ID(0)]: or28646.value,
})

// ダイアログ表示フラグ
const showDialogOr28646 = computed(() => {
  // Or10558のダイアログ開閉状態
  return Or28646Logic.state.get(or28646.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or28646Data: WeekTableImportType = {
  // 施設ID
  shisetuId: '1',
  // 事業者ID
  svJigyoId: '1',
  // 利用者ID
  userId: '1',
  // 種別ID
  syubetsuId: '2',
  // 期間管理フラグ
  kikanFlg: '1',
}
const localData = reactive({
  syubetsuId: { value: '2' } as Mo00045Type,
  kikanFlg: { value: '1' } as Mo00045Type,
  svJigyoId: { value: '1' } as Mo00045Type,
  userId: { value: '1' } as Mo00045Type,
  shisetuId: { value: '1' } as Mo00045Type,
})
/**
Or28646Logic.initialize(childCpIds.Or28646.uniqueCpId)
/**
 *  ボタン押下時の処理
 *
 * @param manageflag - 期間管理フラグ
 */
function onClickOr28646(manageflag: string) {
  Or28646Logic.initialize(childCpIds.Or28646.uniqueCpId)
  // 引継情報.計画書様式を設定する。
  or28646Data.kikanFlg = manageflag
  // Or28646のダイアログ開閉状態を更新する
  Or28646Logic.state.set({
    uniqueCpId: or28646.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function onClickOr28646_1() {
  // 引継情報.計画書様式を設定する。
  or28646Data.kikanFlg = localData.kikanFlg.value
  or28646Data.syubetsuId = localData.syubetsuId.value
  or28646Data.svJigyoId = localData.svJigyoId.value
  or28646Data.userId = localData.userId.value
  or28646Data.shisetuId = localData.shisetuId.value
  // Or28646のダイアログ開閉状態を更新する
  Or28646Logic.state.set({
    uniqueCpId: or28646.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const handleOr28646Confirm = (data: Or28646Type) => {
  const a = data
}
</script>
<template>
  <!-- {{ local.or28646.weekTableDetailList }} -->
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28646('1')"
        >GUI00998_週間表取込（ページ遷移サンプル）(週計画期間管理フラグが=管理する起動時、kikanFlag=1)
      </v-btn>
      <g-custom-or-28646
        v-if="showDialogOr28646"
        v-bind="or28646"
        :oneway-model-value="or28646Data"
        :unique-cp-id="or28646.uniqueCpId"
        :parent-cp-id="pageComponent.uniqueCpId"
        @update:model-value="handleOr28646Confirm"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28646('0')"
        >GUI00998_週間表取込（ページ遷移サンプル）(週計画期間管理フラグが=管理しない起動時、kikanFlag=0)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">施設ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.shisetuId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">事業所ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.svJigyoId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">種別ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.syubetsuId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">期間管理フラグ</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.kikanFlg"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr28646_1"> GUI00998 疎通起動 </v-btn>
  </div>
</template>
