/**
 * OrX0211：有機体：家族図（取込元編集 - 詳細モード）
 *
 * <AUTHOR>
 */
export interface KazokuzuToolbarItem {
  /** アイテムコード */
  itemCd: string
  /** 表示マーク */
  mark?: string
  /** 名称  */
  itemnameKnj?: string
  /** 表示色 */
  color?: string
  /** 部品種別 */
  hatchFlg?: string
  /** 塗りの種類 */
  hatchType?: string
  /** 枠線の種類 */
  lineType?: string
  /** 表示順 */
  sort?: string
  /** 職員ID */
  shokuId?: string | null
}

/**
 * 家族図
 */
export interface KakeizuElementType {
  /** 利用者ID */
  userid?: string
  /** 連番 */
  id?: string
  /** 種別（枠、棒） */
  type?: string
  /** 図番（連番） */
  zuNo?: string
  /** Ｘ座標 */
  xpos?: string
  /** Ｙ座標 */
  ypos?: string
  /** x2座標 */
  x2?: string
  /** y2座標 */
  y2?: string
  /** 備考 */
  memoKnj?: string
  /** 職員ID */
  shokuId?: string
}

/**
 * ドラッグ終了後の要素
 */
export interface KakeizuDropedElement {
  /** ID */
  id: string
  /** タブ */
  itemCd: string
  /** x */
  x: number
  /** y */
  y: number
  /** 幅 */
  width: number
  /** 高さ */
  height: number
  /** テキスト */
  text?: string
  /** 選択フラグ */
  selected: boolean
  /** Zインデックス */
  zIndex: number
  /** 部品種別 */
  hatchFlg: string
  /** 塗りの種類 */
  hatchType?: string
  /** 枠線の種類 */
  lineType?: string
  /** 表示色 */
  color?: string
  /** ユーザーID */
  userId?: string
  /** 同居フラグ */
  doukyoFlg?: boolean
}

/**
 * 家族図部品種別サイズ情報
 */
export interface KakeizuHatchTypeSizeInfo {
  /** 部品種別 */
  hatchFlg: string
  /** 幅 */
  width: number
  /** 高さ */
  height: number
}

/**
 * 個人と家族情報
 */
export interface KakeizuUserKankeiInfo {
  /** ID */
  id: string
  /** 性別 */
  sex: string
  /** 氏名（姓） */
  name1Knj: string
  /** 氏名（名） */
  name2Knj: string
}
