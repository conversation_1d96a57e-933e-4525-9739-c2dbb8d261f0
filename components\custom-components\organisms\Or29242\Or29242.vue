<script setup lang="ts">
/**
 * Or29242:有機体:［アセスメント］画面（居宅）（6③-④）
 * GUI00801_［アセスメント］画面（居宅）（6③-④）
 *
 * @description
 * ［アセスメント］画面（居宅）（6③-④） Or01896
 *
 * <AUTHOR>
 */

import { useI18n } from 'vue-i18n'
import { ref, onMounted, reactive, watch, computed } from 'vue'
import { cloneDeep, update } from 'lodash'
import { OrCD004Const } from '../OrCD004/OrCD004.constants'
import { TeX0002Logic } from '../../template/TeX0002/TeX0002.logic'
import { Or35672Logic } from '../Or35672/Or35672.logic'
import { Or35672Const } from '../Or35672/Or35672.constants'
import { OrX0209Const } from '../OrX0209/OrX0209.constants'
import { OrX0200Const } from '../OrX0200/OrX0200.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or29242Const } from './Or29242.constants'
import { Or29242Logic } from './Or29242.logic'
import type { Or29242Type } from './Or29242.type'
import type { Or29242OnewayType } from '~/types/cmn/business/components/Or29242Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import {
  useCmnCom,
  useCmnRouteCom,
  useScreenStore,
  useScreenTwoWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  CareCertificationItem,
  OrCD002OnewayType,
  OrCD002Type,
} from '~/types/cmn/business/components/OrCD002Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { OrCD004Type } from '~/types/cmn/business/components/OrCD004Type'
import type { OrCD007OneWayType, OrCD007Type } from '~/types/cmn/business/components/OrCD007Type'
import type {
  assessmentHomeTab63SelectInEntity,
  assessmentHomeTab63SelectOutEntity,
  CognitiveFunctionMentalActionHandycapUpdateInEndity,
  CognitiveFunctionMentalActionHandycapUpdateOutEndity,
} from '~/repositories/cmn/entities/AssessmentHome63Entity'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import { CustomClass } from '~/types/CustomClassType'
import type { OrX0201OnewayType } from '~/types/cmn/business/components/OrX0201Type'
import type {
  IssuesAndGoalListItem,
  OrCD006OnewayType,
  OrCD006Type,
} from '~/types/cmn/business/components/OrCD006Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { OrX0209Type } from '~/types/cmn/business/components/OrX0209Type'
import type { OrX0200Type } from '~/types/cmn/business/components/OrX0200Type'
import { UPDATE_KBN } from '~/constants/classification-constants'

/**
 * Propsタイプ
 */
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  onewayModelValue: Or29242OnewayType
  parentUniqueCpId: string
}

const props = defineProps<Props>()

/**
 * 変数定義
 */
const { t } = useI18n()
const orX0200 = ref({ uniqueCpId: '' })
const orX0200_1 = ref({ uniqueCpId: '' })
const orCd004 = ref({ uniqueCpId: '' })
const orX0209 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })

const systemCommonsStore = useSystemCommonsStore()

/** 解決すべき課題と目標Ref */
const issuesAndGoalListRef = ref<HTMLElement | null>(null)

// ロード状態制御
const isLoadingRef = ref<boolean>(false)

/**
 * 画面更新区分
 */
const updateKbn = ref('')

/** 小分類CD区分 */
let t3cd = ''

// ローカルTwoway
const local = reactive({
  // 共通情報
  commonInfo: {} as TeX0002Type,
  // 認知機能(ラジオグループ)
  cognitiveFunction: {
    careCertificationList: {
      sectionList: [
        {
          sectionTitle: t('label.assessment-home-6-1-level-of-care-required-certification-item'),
          sectionItems: [
            {
              /** 項目物理名 */
              name: '',
              /** 項目名 */
              itemName: '',
              /** ラジオボタン選択肢 */
              /** 選択肢タイプ 0:チェックボックス 1:ラジオボタン */
              itemType: '1',
              raidoItems: [] as CodeType[],
              /** ラジオボタン選択肢 */
              raidoValue: '',
              /** ラジオボタンonewayモデル */
              radioOneway: {
                name: '',
                showItemLabel: false,
                hideDetails: true,
              } as Mo00039OnewayType,
            } as CareCertificationItem,
          ],
        },
      ],
    },
  } as OrCD002Type,
  // 精神・行動障害（ラジオグループ）
  mentalActionHandycap: {
    careCertificationList: {
      sectionList: [
        {
          sectionTitle: t('label.assessment-home-6-1-level-of-care-required-certification-item'),
          sectionItems: [
            {
              /** 項目物理名 */
              name: '',
              /** 項目名 */
              itemName: '',
              /** ラジオボタン選択肢 */
              /** 選択肢タイプ 0:チェックボックス 1:ラジオボタン */
              itemType: '1',
              raidoItems: [] as CodeType[],
              /** ラジオボタン選択肢 */
              raidoValue: '',
              /** ラジオボタンonewayモデル */
              radioOneway: {
                name: '',
                showItemLabel: false,
                hideDetails: true,
              } as Mo00039OnewayType,
            } as CareCertificationItem,
          ],
        },
      ],
    },
  } as OrCD002Type,
  // 特記、解決すべき課題など
  specialNoteShouldSolutionIssues: {
    // デフォルト値
    inputContent: '',
  } as OrCD004Type,
  // 家族からの情報と観察
  verticalTextSpecialInput: {
    inputContent: [{ value: '' }],
  } as OrCD007Type,
  // 援助の現状
  verticalTextSpecialInput_1: {
    inputContent: [
      // 家族
      { value: '' },
      // サービス
      { value: '' },
    ],
  } as OrCD007Type,
  // 援助の希望／本人
  verticalTextSpecialInput_2: {
    inputContent: [{ value: '' }],
  } as OrCD007Type,
  // 援助の希望／家族
  verticalTextSpecialInput_3: {
    inputContent: [{ value: '' }],
  } as OrCD007Type,
  // 援助の計画
  verticalTextSpecialInput_4: {
    inputContent: [{ value: '' }],
  } as OrCD007Type,
  issuesAndGoalsList: {
    items: [] as IssuesAndGoalListItem[],
  } as OrCD006Type,
})

const localOneWay = reactive({
  Or29242: {
    ...props.onewayModelValue,
  },
  // 認知機能
  cognitiveFunctionOneWay: {
    subTitle: t('label.cognitive-function-label'),
    /** 取込ボタン名 */
    importBtnName: t('label.assessment-home-6-1-servey-ledger-import'),
    /** 取込ボタン名 */
    importBtnToolTip: t('tooltip.assessment-home-6-1-servey-ledger-import'),
    /** 詳細ボタンツールチップ */
    detailBtnToolTip: t('tooltip.assessment-home-6-1-basic-motion'),
    titleDisplayRows: '9',
    codeMasterList: [],
    importBtnDisplayFlg: true,
  } as OrCD002OnewayType,
  // 精神・行動障害
  mentalActionHandycapOneWay: {
    subTitle: t('label.mental-action-handycap-label'),
    /** 取込ボタン名 */
    importBtnName: t('label.assessment-home-6-1-servey-ledger-import'),
    /** 取込ボタン名 */
    importBtnToolTip: t('tooltip.assessment-home-6-1-servey-ledger-import'),
    /** 詳細ボタンツールチップ */
    detailBtnToolTip: t('tooltip.assessment-home-6-1-basic-motion'),
    titleDisplayRows: '15',
    importBtnDisplayFlg: false,
    codeMasterList: [],
  } as OrCD002OnewayType,
  verticalTextSpecialInput: {
    label: t('label.kazokuJouhouKnj-label'),
    title: t('label.cognitive-function-mental-action-handycap-label'),
    isShowSubtitle: false,
    maxRows: '22',
    inputCount: 1,
  } as OrCD007OneWayType,
  commonOrX0156Oneway: {
    rows: '4',
    maxRows: '4',
    showDividerLineFlg: false,
    isVerticalLabel: true,
    customClass: new CustomClass({
      labelStyle: 'fontWeight:bold',
    }),
  } as OrX0156OnewayType,
  // タブタイトル
  orX0201Oneway: {
    title: '',
    tabNo: '6',
    titleDisplayFlg: false,
    scrollToUniqueCpId: '',
  } as OrX0201OnewayType,
  // 解決すべき課題と目標一覧
  issuesAndGoalsListOneway: {
    assessmentName:
      t('label.assessment-home-issuesAndGoals-no-1') +
      '\r\n' +
      t('label.assessment-home-issuesAndGoals-name-1'),
  } as OrCD006OnewayType,
  // 入力支援
  or51775Onway: {} as Or51775OnewayType,
})

/**********************************************************
 * 算出プロパティ
 **********************************************************/
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**********************************************************
 * Pinia
 **********************************************************/
// 子コンポーネントのユニックIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrCD004Const.CP_ID(0)]: orCd004.value,
  [OrX0209Const.CP_ID(1)]: orX0209.value,
  [OrX0200Const.CP_ID(0)]: orX0200.value,
  [OrX0200Const.CP_ID(1)]: orX0200_1.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
})

const { refValue } = useScreenTwoWayBind<Or29242Type>({
  uniqueCpId: props.uniqueCpId,
  cpId: Or29242Const.CP_ID(0),
})

// データ初期化
refValue.value = {
  famMemoKnj: { value: '' },
  serMemoKnj: { value: '' },
  kiboMemoKnj: { value: '' },
  kiboFamMemoKnj: { value: '' },
  keikakuMemoKnj: { value: '' },
  memo1Knj: { value: '' },
  kazokuJouhouKnj: { value: '' },
}

// 初期化処理
onMounted(async () => {
  await initControls()
})

/**************************************************
 * 関数
 **************************************************/

/**
 * 共通情報取得
 */
function getCommonInfo(): void {
  const commonInfo = TeX0002Logic.data.get(props.parentUniqueCpId)

  if (commonInfo) {
    local.commonInfo = commonInfo

    // 解決すべき課題と目標一覧パラメータ設定
    localOneWay.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
    localOneWay.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
    localOneWay.issuesAndGoalsListOneway.assNo = Or29242Const.DEFAULT.TAB_ID
    localOneWay.issuesAndGoalsListOneway.userId = local.commonInfo.userId

    local.issuesAndGoalsList.items =
      cloneDeep(commonInfo.issuesAndGoalsList) ?? ([] as IssuesAndGoalListItem[])
  }
}

/**
 * 基本動作処理(特殊場合)
 *
 * @param basicMotionItemList - API返却値
 */
function getTksBasicMotionCodes(basicMotionItemList: CodeType[]): void {
  const prefix = '3-'
  const nameStr = 'bango'
  if (basicMotionItemList) {
    let sectionItems = []
    let currentItem = {} as CareCertificationItem
    basicMotionItemList.forEach((basicMotionItem) => {
      // アイテムをチェックする
      if (basicMotionItem.label.startsWith(prefix)) {
        currentItem = {} as CareCertificationItem
        // タイトルアイテム
        currentItem.itemName = basicMotionItem.label
        currentItem.raidoItems = []
      } else {
        // ラジオグループ
        if (currentItem) {
          // ラジオ選択肢が複数の場合
          if (basicMotionItem.label.includes('　')) {
            basicMotionItem.label.split('　').forEach((sItem) => {
              currentItem.raidoItems?.push({
                label: sItem,
                value: getNumberFromString(sItem),
                shisetutouKbn: '',
              } as CodeType)
            })
          } else {
            // ラジオ選択肢が単一の場合
            currentItem.raidoItems?.push({
              label: basicMotionItem.label,
              value: getNumberFromString(basicMotionItem.label),
              shisetutouKbn: '',
            } as CodeType)
          }
        }
      }
      currentItem.radioOneway = {
        name: currentItem.itemName,
        showItemLabel: false,
        hideDetails: true,
      } as Mo00039OnewayType
    })

    sectionItems = basicMotionItemList
      .map((basicMotionItem, index) => {
        // アイテムのラベルをチェックする
        if (basicMotionItem.label.startsWith(prefix)) {
          const resultIndex = findIndexWithPrefix(basicMotionItemList, index, prefix)
          // インデックスが null の場合、処理を中断する
          return {
            itemName: basicMotionItem.label,
            name: nameStr + transformLabel(basicMotionItem.label),
            itemType: '1',
            raidoItems: basicMotionItemList
              .slice(index + 1, resultIndex ?? basicMotionItemList.length)
              .map((el) => {
                // 選択肢が複数の場合
                if (el.label.includes('　')) {
                  return el.label.split('　').map(
                    (item) =>
                      ({
                        label: item,
                        value: getNumberFromString(item),
                        shisetutouKbn: '',
                      }) as CodeType
                  )
                } else {
                  // 選択肢が単一の場合
                  return {
                    label: el.label,
                    value: getNumberFromString(el.label),
                    shisetutouKbn: '',
                  } as CodeType
                }
              })
              .flat(),
            raidoValue: '',
            radioOneway: {
              name: currentItem.itemName,
              showItemLabel: false,
              hideDetails: true,
            },
          }
        }
        return undefined
      })
      .filter((sItem) => !!sItem)

    local.cognitiveFunction.careCertificationList.sectionList[0].sectionItems = sectionItems
  }
}

/**
 *  基本動作コード処理
 *
 * @param basicMotionItemList - 基本動作コードリスト
 */
function getBasicMotionCodes(basicMotionItemList: CodeType[]): void {
  const nameStr = 'bango'
  if (basicMotionItemList) {
    let sectionItems = []
    let currentItem = {} as CareCertificationItem
    for (const item of basicMotionItemList) {
      // アイテムタイトル
      if (item.label.startsWith('2-')) {
        currentItem = {} as CareCertificationItem
        currentItem.itemName = item.label
        currentItem.raidoItems = []
      }
      const selectItems = item.label.split('　')
      selectItems.forEach((sItem) => {
        currentItem.raidoItems?.push({
          label: sItem,
          value: getNumberFromString(sItem),
          shisetutouKbn: '',
        } as CodeType)
      })
      currentItem.radioOneway = {
        name: currentItem.itemName,
        showItemLabel: false,
        hideDetails: true,
      } as Mo00039OnewayType
    }

    sectionItems = basicMotionItemList
      .map((item, index) => {
        if (index % 2 === 0) {
          return {
            itemName: item.label,
            name: nameStr + transformLabel(item.label),
            itemType: '1',
            raidoItems: basicMotionItemList[index + 1].label.split('　').map((el) => {
              return { label: el, value: getNumberFromString(el), shisetutouKbn: '' } as CodeType
            }),
            raidoValue: '',
            radioOneway: {
              name: currentItem.itemName,
              showItemLabel: false,
              hideDetails: true,
            },
          }
        }
        return undefined
      })
      .filter((sItem) => !!sItem)

    local.mentalActionHandycap.careCertificationList.sectionList[0].sectionItems = sectionItems
  }
}

/**
 *  文字列の数字を取得
 *
 * @param str - 文字列
 */
function getNumberFromString(str: string): string {
  const numbersAsString = /^\d+/.exec(str)

  // 转换为 number[]
  const targetNumber = numbersAsString?.map(Number)?.[0].toString() ?? ''

  return targetNumber
}

/**
 *  文字列処理
 *
 * @param label - ラベル
 */
function transformLabel(label: string): string | undefined {
  const match = /(\d+-\d+)/.exec(label)
  if (match) {
    const extracted = match[0].replace('-', '')
    return extracted
  }
}

// 対応する配列のインデックスを検索する
function findIndexWithPrefix(array: CodeType[], startIndex: number, prefix: string): number | null {
  for (let i = startIndex + 1; i < array.length; i++) {
    if (array[i].label.startsWith(prefix)) {
      return i
    }
  }
  // 対応するインデックスが見つからない場合、null を返す
  return null
}

/**
 * 汎用コードマスタデータを取得し初期化する関数
 */
async function initCodes(): Promise<void> {
  // 認知機能と精神行動障害に関するコード区分リスト
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.COGNITIVE_FUNCTION }, // 認知機能
    { mCdKbnId: CmnMCdKbnId.MENTAL_ACTION_HANDYCAP }, // 精神行動障害
  ]

  // 汎用コードAPIを呼び出し、コードマスタデータを取得
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 基本動作処理
  getTksBasicMotionCodes(CmnSystemCodeRepository.filter(CmnMCdKbnId.COGNITIVE_FUNCTION)) // 認知機能に関連する基本動作処理
  getBasicMotionCodes(CmnSystemCodeRepository.filter(CmnMCdKbnId.MENTAL_ACTION_HANDYCAP)) // 精神行動障害に関連する基本動作処理
  localOneWay.cognitiveFunctionOneWay.codeMasterList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.COGNITIVE_FUNCTION
  )
  localOneWay.mentalActionHandycapOneWay.codeMasterList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.MENTAL_ACTION_HANDYCAP
  )
}

/**
 * コントロール初期化
 */
const initControls = async () => {
  localOneWay.orX0201Oneway.scrollToUniqueCpId = orX0209.value.uniqueCpId

  await initCodes()
}

/**
 * 画面初期情報取得
 */
async function getinitData(): Promise<void> {
  try {
    // バックエンドAPIから初期情報取得
    const inputData: assessmentHomeTab63SelectInEntity = {
      /** 計画期間ID */
      sc1Id: local.commonInfo.sc1Id,
      /** アセスメントID */
      gdlId: local.commonInfo.gdlId,
      /** 改訂フラグ */
      ninteiFormF: local.commonInfo.ninteiFormF,
    }

    const resData: assessmentHomeTab63SelectOutEntity = await ScreenRepository.select(
      'assessmentCognitiveMentalSelect',
      inputData
    )
    if (resData) {
      setFormData(resData)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * フォームデータ設定
 *
 * @param data - APIまたは複写画面から取得したデータ
 */
function setFormData(data: assessmentHomeTab63SelectOutEntity) {
  // 複写モードの場合、APIから取得のデータを保持
  if (props.onewayModelValue.mode === Or29242Const.DEFAULT.MODE_COPY) {
    Or29242Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        copyData: data,
      },
    })
  }
  if (data.data.gdl4Kan13Info.gdlId === '' && data.data.gdl5Kan13Info.gdlId === '') {
    updateKbn.value = UPDATE_KBN.CREATE
  }
  let resGdl4Kan13Info: assessmentHomeTab63SelectOutEntity['data']['gdl4Kan13Info']

  if (local.commonInfo.ninteiFormF === '4') {
    resGdl4Kan13Info = data.data.gdl4Kan13Info
  } else {
    resGdl4Kan13Info = data.data.gdl5Kan13Info
  }
  // 認知機能
  local.cognitiveFunction.careCertificationList.sectionList.forEach((item) => {
    item.sectionItems.forEach((sItem) => {
      ;(sItem.raidoValue as string | undefined) = resGdl4Kan13Info?.[sItem.name] ?? undefined
    })
  })
  // 精神・行動障害
  local.mentalActionHandycap.careCertificationList.sectionList.forEach((item) => {
    item.sectionItems.forEach((sItem) => {
      ;(sItem.raidoValue as string | undefined) = resGdl4Kan13Info?.[sItem.name] ?? undefined
    })
  })

  refValue.value = {
    // 家族からの情報と観察
    kazokuJouhouKnj: { value: resGdl4Kan13Info.kazokuJouhouKnj },
    // 援助の現状
    // 家族
    famMemoKnj: { value: resGdl4Kan13Info.famMemoKnj },
    // サービス
    serMemoKnj: { value: resGdl4Kan13Info.serMemoKnj },
    // 援助の希望／本人
    kiboMemoKnj: { value: resGdl4Kan13Info.kiboMemoKnj },
    // 援助の希望／家族
    kiboFamMemoKnj: { value: resGdl4Kan13Info.kiboFamMemoKnj },
    // 援助の計画
    keikakuMemoKnj: { value: resGdl4Kan13Info.keikakuMemoKnj },
    // 特記、解決すべき課題など
    memo1Knj: { value: resGdl4Kan13Info.memo1Knj },
  }

  useScreenStore().setCpTwoWay({
    uniqueCpId: props.uniqueCpId,
    cpId: Or29242Const.CP_ID(0),
    isInit: true,
    value: refValue.value,
  })
}
/**
 * コントロール初期化
 */
async function reload(): Promise<void> {
  isLoadingRef.value = true
  // 画面初期情報取得
  await initCodes()
  await getinitData()
  isLoadingRef.value = false
}

/**
 * 新規処理
 */
function clearData(): void {
  // 認知機能クリア
  local.cognitiveFunction.careCertificationList.sectionList.forEach((item) => {
    item.sectionItems.forEach((sItem) => {
      sItem.raidoValue = ''
    })
  })
  // 精神・行動障害クリア
  local.mentalActionHandycap.careCertificationList.sectionList.forEach((item) => {
    item.sectionItems.forEach((sItem) => {
      sItem.raidoValue = ''
    })
  })
  // 家族等からの情報と観察
  local.verticalTextSpecialInput = {
    inputContent: [
      {
        value: '',
      },
    ],
  }
  // 援助の現状
  local.verticalTextSpecialInput_1 = {
    inputContent: [
      // 家族
      {
        value: '',
      },
      // サービス
      {
        value: '',
      },
    ],
  }
  // 援助の希望／本人
  local.verticalTextSpecialInput_2 = {
    inputContent: [
      {
        value: '',
      },
    ],
  }
  // 援助の希望／家族
  local.verticalTextSpecialInput_3 = {
    inputContent: [
      {
        value: '',
      },
    ],
  }
  // 援助の計画
  local.verticalTextSpecialInput_4 = {
    inputContent: [
      {
        value: '',
      },
    ],
  }
  // 解決すべき課題
  local.specialNoteShouldSolutionIssues = {
    inputContent: '',
  }
}

/**
 * 保存イベント
 */
async function saveAssmentHomeData() {
  isLoadingRef.value = true
  // 履歴更新区分
  try {
    let historyUpdateKbn = UPDATE_KBN.UPDATE
    if (local.commonInfo.gdlId === '0') {
      historyUpdateKbn = UPDATE_KBN.CREATE
    } else if (local.commonInfo.deleteKbn === '2') {
      historyUpdateKbn = UPDATE_KBN.DELETE
    } else if (local.commonInfo.deleteKbn === '1') {
      updateKbn.value = UPDATE_KBN.DELETE
    }

    const userName =
      (systemCommonsStore.getUserSelectUserInfo()?.nameSei ?? '') +
      (systemCommonsStore.getUserSelectUserInfo()?.nameMei ?? '')

    const childrenRefValue = useScreenUtils().getChildCpBinds(props.uniqueCpId, {
      [OrX0200Const.CP_ID(0)]: { cpPath: OrX0200Const.CP_ID(0), twoWayFlg: true },
      [OrX0200Const.CP_ID(1)]: { cpPath: OrX0200Const.CP_ID(1), twoWayFlg: true },
      [OrX0209Const.CP_ID(1)]: { cpPath: OrX0209Const.CP_ID(1), twoWayFlg: true },
    })
    // 認知機能
    const cognitiveFunction = (
      childrenRefValue[OrX0200Const.CP_ID(0)].twoWayBind?.value as OrX0200Type
    ).careCertificationList
    // 精神・行動障害
    const mentalActionHandycap = (
      childrenRefValue[OrX0200Const.CP_ID(1)].twoWayBind?.value as OrX0200Type
    ).careCertificationList
    // 解決すべき課題
    const kedaiList = childrenRefValue[OrX0209Const.CP_ID(1)].twoWayBind?.value as OrX0209Type
    // 更新APIリクエストパラメータ
    const inputData: CognitiveFunctionMentalActionHandycapUpdateInEndity = {
      tabId: Or29242Const.DEFAULT.TAB_ID,
      kinoId: '',
      krirekiNo: local.commonInfo.historyNo ?? '',
      edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
      edocumentUseParam: useCmnCom().getEDocumentParam(),
      kikanFlg: local.commonInfo.kikanKanriFlg ?? '',
      planningPeriodNo: local.commonInfo.sc1No ?? '',
      startYmd: local.commonInfo.periodStartYmd ?? '',
      endYmd: local.commonInfo.periodEndYmd ?? '',
      matomeFlg: useCmnRouteCom().getInitialSettingMaster()?.gdlMatomeFlg ?? '',
      loginId: systemCommonsStore.getCurrentUser.loginId ?? '',
      sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
      shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      sysCd: systemCommonsStore.getSystemCode ?? '',
      svJigyoKnj: local.commonInfo.jigyoKnj ?? '',
      createUserName: local.commonInfo.createUserName ?? '',
      userName: userName,
      houjinId: systemCommonsStore.getHoujinId ?? '',
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      userId: local.commonInfo.userId ?? '',
      svJigyoId: local.commonInfo.jigyoId ?? '',
      syubetsuId: systemCommonsStore.getSyubetu ?? '',
      historyUpdateKbn: historyUpdateKbn,
      sc1Id: local.commonInfo.sc1Id ?? '',
      gdlId: local.commonInfo.gdlId ?? '',
      ninteiFormF: local.commonInfo.ninteiFormF ?? '',
      kijunbiYmd: local.commonInfo.createYmd ?? '',
      sakuseiId: local.commonInfo.createUserId ?? '',
      deleteKbn: local.commonInfo.deleteKbn ?? '',
      updateKbn: updateKbn.value,
      kan13Info: {
        famMemoKnj: refValue.value?.famMemoKnj.value ?? '',
        serMemoKnj: refValue.value?.serMemoKnj.value ?? '',
        kiboMemoKnj: refValue.value?.kiboMemoKnj.value ?? '',
        kiboFamMemoKnj: refValue.value?.kiboFamMemoKnj.value ?? '',
        keikakuMemoKnj: refValue.value?.keikakuMemoKnj.value ?? '',
        memo1Knj: refValue.value?.memo1Knj.value ?? '',
        kazokuJouhouKnj: refValue.value?.kazokuJouhouKnj.value ?? '',
      } as CognitiveFunctionMentalActionHandycapUpdateInEndity['kan13Info'],
      kadaiList: kedaiList.items.map((item) => {
        return {
          kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
          tankiKnj: item.shorttermGoal.value,
          choukiKnj: item.longtermGoal.value,
          updateKbn: item.updateKbn ?? '',
          seq: item.seq.toString(),
          id: item.id,
          assNo: item.assNo ?? '',
        }
      }),
    }
    cognitiveFunction.sectionList.forEach((item) => {
      item.sectionItems.forEach((sItem) => {
        inputData.kan13Info[sItem.name] = sItem.raidoValue
      })
    })
    mentalActionHandycap.sectionList.forEach((item) => {
      item.sectionItems.forEach((sItem) => {
        inputData.kan13Info[sItem.name] = sItem.raidoValue
      })
    })
    // 更新APIを呼び出し
    const resData: BaseResponseBody<CognitiveFunctionMentalActionHandycapUpdateOutEndity> =
      await ScreenRepository.update('assessmentCognitiveMentalUpdate', inputData)
    if (resData.data) {
      if (resData.data.status === Or29242Const.DEFAULT.KOUSINZUMI_FLG_SUCCESS) {
        // 初期情報再取得
        // await getinitData()
        console.log('実行済み')
      } else if (resData.data.status === Or29242Const.DEFAULT.KOUSINZUMI_FLG_FAIL) {
        // 失敗処理
      }
    }
    isLoadingRef.value = false
  } catch (e) {
    console.log(e)
    isLoadingRef.value = false
  }
}

/**
 * 入力支援画面を呼び出す
 *
 * @param t3Cd - 小分類コード
 *
 * @param inputContents - 入力内容
 *
 * @param title - タイトル
 */
function setShowDialogOr51775(t3Cd: string, inputContents: string, title: string) {
  // テーブル名取得
  let tableName = ''
  if (local.commonInfo.ninteiFormF === '4') {
    tableName = 'cpn_tuc_gdl4_kan13_h21'
  } else {
    tableName = 'cpn_tuc_gdl5_kan13_r3'
  }
  t3cd = t3Cd
  // カラム設定
  let columnName = ''
  switch (t3Cd) {
    case '1':
      columnName = 'kazoku_jouhou_knj'
      break
    case '2':
      columnName = 'fam_memo_Knj'
      break
    case '3':
      columnName = 'ser_memo_knj'
      break
    case '4':
      columnName = 'kibo_memo_knj'
      break
    case '5':
      columnName = 'kibo_fam_memo_knj'
      break
    case '6':
      columnName = 'keikaku_memo_knj'
      break
    case '7':
      columnName = 'memo1_knj'
  }
  localOneWay.or51775Onway = {
    title,
    screenId: 'GUI00801',
    bunruiId: '',
    t1Cd: '602',
    t2Cd: '28',
    t3Cd,
    columnName,
    assessmentMethod: '',
    mode: '',
    tableName,
    inputContents: inputContents,
    userId: local.commonInfo.userId ?? '',
  }
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  // タイトル:”家族等からの情報と観察”
  // 画面ID:"GUI00801"
  // 分類ID:""
  // 大分類CD:602
  // 中分類CD:28
  // 小分類CD:1
  // "テーブル名:
  //  改訂フラグが4「H21/4」の場合、
  //  "cpn_tuc_gdl4_kan13_h21""
  //  改訂フラグが5「R3/4」の場合、
  //  ""cpn_tuc_gdl5_kan13_r3"""
  // カラム名:"kazoku_jouhou_knj"
  // アセスメント方式:共通情報.アセスメント方式ケアプラン方式
  // 文章内容:画面項目.家族等からの情報と観察
  // 利用者ID:共通情報画面.利用者ID
}

/**
 * 入力支援画面返却値設定
 *
 * @param result - 返却値
 */
function getDialogOr51775Result(result: Or51775ConfirmType) {
  // 操作したデータを取得

  if (result.type === Or29242Const.DEFAULT.INPUT_SUPPORT_ADD) {
    switch (t3cd) {
      case '1':
        refValue.value!.kazokuJouhouKnj.value += result.value
        break
      case '2':
        refValue.value!.famMemoKnj.value += result.value
        break
      case '3':
        refValue.value!.serMemoKnj.value += result.value
        break
      case '4':
        refValue.value!.kiboMemoKnj.value += result.value
        break
      case '5':
        refValue.value!.kiboFamMemoKnj.value += result.value
        break
      case '6':
        refValue.value!.keikakuMemoKnj.value += result.value
        break
      case '7':
        refValue.value!.memo1Knj.value += result.value
        break
    }
  }
  if (result.type === Or29242Const.DEFAULT.INPUT_SUPPORT_OVERWRITE) {
    switch (t3cd) {
      case '1':
        refValue.value!.kazokuJouhouKnj.value = result.value
        break
      case '2':
        refValue.value!.famMemoKnj.value = result.value
        break
      case '3':
        refValue.value!.serMemoKnj.value = result.value
        break
      case '4':
        refValue.value!.kiboMemoKnj.value = result.value
        break
      case '5':
        refValue.value!.kiboFamMemoKnj.value = result.value
        break
      case '6':
        refValue.value!.keikakuMemoKnj.value = result.value
        break
      case '7':
        refValue.value!.memo1Knj.value = result.value
        break
    }
  }
}

/**
 * 削除処理
 */
function _delete() {
  console.log('削除処理')
}

/**
 * 印刷処理
 */
function _print() {
  console.log('印刷処理')
}

/**
 * お気に入り処理
 */
function _favorite() {
  console.log('印刷処理')
}

/**
 * Or35672イベント発火
 *
 * @param event - イベント
 */
function setOr35672Event(event: Record<string, boolean>) {
  Or35672Logic.event.set({
    uniqueCpId: Or35672Const.CP_ID(0) + props.parentUniqueCpId,
    events: event,
  })
}

/**
 * 解決すべき課題と目標にスクロール関数
 */
const scrollToIssues = () => {
  if (issuesAndGoalListRef.value) {
    const scrollTop = issuesAndGoalListRef.value.offsetTop
    window.scroll({ top: scrollTop, behavior: 'smooth' })
  }
}

/**
 *  複写画面から共通情報を取得
 */
function getCommonInfoFromCopy() {
  const commonInfo = Or35672Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo.ninteiFormF = commonInfo.params!.ninteiFormF
    local.commonInfo.activeTabId = commonInfo.params!.activeTabId
    local.commonInfo.jigyoId = commonInfo.params!.jigyoId
    local.commonInfo.sc1Id = commonInfo.params!.sc1Id
    local.commonInfo.gdlId = commonInfo.params!.gdlId
    local.commonInfo.createUserId = commonInfo.params!.createUserId
    local.commonInfo.createYmd = commonInfo.params!.createYmd
  }
}

/**
 *  画面イベント監視
 *
 */
watch(
  () => TeX0002Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    console.log('newValue', newValue)

    // 画面共通情報を取得
    getCommonInfo()
    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or29242Const.DEFAULT.TAB_ID) {
      return
    }

    // 画面再発火フラグ
    if (newValue.isRefresh) {
      await reload()
    }

    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      _favorite()
      return
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await saveAssmentHomeData()
      return
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      clearData()
      return
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
      _print()
      return
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      _delete()
      return
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      const copyData = local.commonInfo.copyData as assessmentHomeTab63SelectOutEntity
      console.log(copyData, '複写')
      setFormData(copyData)
      return
    }
  },
  { deep: true, immediate: true }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or35672Logic.event.get(Or35672Const.CP_ID(0) + props.parentUniqueCpId),
  async (newValue) => {
    if (newValue?.reload === false) {
      return
    }

    setOr35672Event({ reload: false })

    // 複写画面から共通情報を取得
    getCommonInfoFromCopy()

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or29242Const.DEFAULT.TAB_ID) {
      return
    }

    // 画面再表示
    if (newValue?.reload) {
      // 画面情報再取得
      await reload()
    }
  }
)
</script>

<template>
  <c-v-sheet
    id="Or29242Component"
    class="background-color pa-2"
    style="width: 1080px"
  >
    <v-overlay
      :model-value="isLoadingRef"
      :persistent="true"
      class="align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      >
      </v-progress-circular
    ></v-overlay>
    <!-- 複写の場合、マスクをつけ -->
    <c-v-row
      no-gutters
      class="d-flex"
    >
      <c-v-col cols="12 mb-4">
        <g-custom-orX0201
          :oneway-model-value="localOneWay.orX0201Oneway"
          @on-click-link-btn="scrollToIssues"
        />
      </c-v-col>
      <!-- ラジオグループ -->
      <c-v-col cols="12">
        <!-- 認知機能 -->
        <g-custom-or-x-0200
          v-bind="orX0200"
          :model-value="local.cognitiveFunction"
          :oneway-model-value="localOneWay.cognitiveFunctionOneWay"
          :common-info="local.commonInfo"
          @get-data-info="getinitData"
        />
        <!-- フォームの拡張 -->
        <!-- 精神・行動障害 -->
        <g-custom-or-x-0200
          v-bind="orX0200_1"
          :model-value="local.mentalActionHandycap"
          :oneway-model-value="localOneWay.mentalActionHandycapOneWay"
          :common-info="local.commonInfo"
          @get-data-info="getinitData"
        />
        <c-v-col class="c-sub-title white">
          {{ t('label.cognitive-function-mental-action-handycap-label') }}
        </c-v-col>
        <c-v-col class="c-sub-title">
          {{ t('label.kazokuJouhouKnj-label') }}
        </c-v-col>
        <c-v-col class="c-sub-content">
          <g-custom-orX0156
            v-model="refValue!.kazokuJouhouKnj"
            :oneway-model-value="localOneWay.commonOrX0156Oneway"
            @on-click-edit-btn="
              setShowDialogOr51775(
                '1',
                refValue!.kazokuJouhouKnj.value ?? '',
                t('label.kazokuJouhouKnj-label')
              )
            "
          />
        </c-v-col>
        <c-v-col class="c-sub-title">
          {{ t('label.current-situation-of-assistance') }}
        </c-v-col>
        <c-v-col class="c-sub-content">
          <g-custom-orX0156
            v-model="refValue!.famMemoKnj"
            :oneway-model-value="{
              ...localOneWay.commonOrX0156Oneway,
              itemLabel: t('label.famMemoKnj-label'),
              showItemLabel: true,
            }"
            @on-click-edit-btn="
              setShowDialogOr51775(
                '2',
                refValue!.famMemoKnj.value ?? '',
                t('label.famMemoKnj-label')
              )
            "
          />
          <c-v-col class="mb-4"></c-v-col>
          <g-custom-orX0156
            v-model="refValue!.serMemoKnj"
            :oneway-model-value="{
              ...localOneWay.commonOrX0156Oneway,
              itemLabel: t('label.serMemoKnj-label'),
              showItemLabel: true,
            }"
            @on-click-edit-btn="
              setShowDialogOr51775(
                '3',
                refValue!.serMemoKnj.value ?? '',
                t('label.serMemoKnj-label')
              )
            "
          />
        </c-v-col>
        <c-v-col class="c-sub-title">
          {{ t('label.assistance-of-support-label') }}
        </c-v-col>
        <c-v-col class="c-sub-content">
          <g-custom-orX0156
            v-model="refValue!.kiboMemoKnj"
            :oneway-model-value="{
              ...localOneWay.commonOrX0156Oneway,
              itemLabel: t('label.kiboMemoKnj-label'),
              showItemLabel: true,
            }"
            @on-click-edit-btn="
              setShowDialogOr51775(
                '4',
                refValue!.kiboMemoKnj.value ?? '',
                t('label.kiboMemoKnj-label')
              )
            "
          />
          <c-v-col class="mb-4"></c-v-col>
          <g-custom-orX0156
            v-model="refValue!.kiboFamMemoKnj"
            :oneway-model-value="{
              ...localOneWay.commonOrX0156Oneway,
              itemLabel: t('label.kiboFamMemoKnj-label'),
              showItemLabel: true,
            }"
            @on-click-edit-btn="
              setShowDialogOr51775(
                '5',
                refValue!.kiboFamMemoKnj.value ?? '',
                t('label.kiboFamMemoKnj-label')
              )
            "
          />
        </c-v-col>
        <c-v-col class="c-sub-title">
          {{ t('label.keikakuMemoKnj-label') }}
        </c-v-col>
        <c-v-col class="c-sub-content">
          <g-custom-orX0156
            v-model="refValue!.keikakuMemoKnj"
            :oneway-model-value="localOneWay.commonOrX0156Oneway"
            @on-click-edit-btn="
              setShowDialogOr51775(
                '6',
                refValue!.keikakuMemoKnj.value ?? '',
                t('label.keikakuMemoKnj-label')
              )
            "
          />
        </c-v-col>
        <c-v-col class="c-sub-title">
          {{ t('label.special-notes-resolved') }}
        </c-v-col>
        <c-v-col class="c-sub-content">
          <g-custom-orX0156
            :model-value="refValue!.memo1Knj"
            :oneway-model-value="localOneWay.commonOrX0156Oneway"
            @on-click-edit-btn="
              setShowDialogOr51775('7', refValue!.memo1Knj.value ?? '', t('label.sentence-master'))
            "
          />
        </c-v-col>
      </c-v-col>
    </c-v-row>
    <g-custom-or51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      :oneway-model-value="localOneWay.or51775Onway"
      @confirm="getDialogOr51775Result"
    />
  </c-v-sheet>

  <!-- 線 -->
  <c-v-row
    no-gutters
    style="margin: 0 -24px"
    class="py-6"
  >
    <c-v-divider></c-v-divider>
  </c-v-row>

  <!-- フッター -->
  <c-v-row
    no-gutters
    class="pb-6"
  >
    <div
      ref="issuesAndGoalListRef"
      class="w-100"
    >
      <g-custom-or-x-0209
        v-bind="orX0209"
        :model-value="local.issuesAndGoalsList"
        :oneway-model-value="localOneWay.issuesAndGoalsListOneway"
      />
    </div>
  </c-v-row>
</template>

<style lang="scss" scoped>
.background-color {
  background-color: transparent;
}
.v-col {
  padding: 0px;
}
.c-sub-title.white {
  background-color: #fff;
  line-height: 1;
  padding-top: 24px;
  padding-bottom: 16px;
  border: none;
}

.c-sub-content :deep(.v-input__control) {
  .v-field__input {
    min-height: auto !important;
  }
}
:deep(.item-label) {
  font-weight: bold !important;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
</style>
