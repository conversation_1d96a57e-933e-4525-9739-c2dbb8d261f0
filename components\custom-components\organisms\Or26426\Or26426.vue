<script setup lang="ts">
/**
 * Or26426:有機体:認定調査票特記事項ダイアログモーダル
 * GUI01272_認定調査票特記事項
 *
 * @description
 *［認定調査票特記事項］画面では、［特記事項］画面に反映する特記事項を入力します。
 *［認定調査票特記事項］画面は、［ケアマネ］→［認定調査］→［基本調査］画面などで［特記⇒］をクリックすると表示されます。
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0217Const } from '../OrX0217/OrX0217.constants'
import { Or26426Const } from './Or26426.constants'
import type { Or26426StateType, Or26426DataValue, ShowItemList, NTokkiList } from './Or26426.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or26426OnewayType } from '~/types/cmn/business/components/Or26426Type'
import {
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useScreenUtils,
  useSetupChildProps,
} from '#imports'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import type {
  Or21814FirstBtnType,
  Or21814SecondBtnType,
  Or21814ThirdBtnType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  CertificationSurveySpecialMatterInitSelectInEntity,
  CertificationSurveySpecialMatterInitSelectOutEntity,
} from '~/repositories/cmn/entities/CertificationSurveySpecialMatterInitSelectEntity'
import type { OrX0217OnewayType } from '~/types/cmn/business/components/OrX0217Type'
import type {
  CertificationSurveySpecialMatterUpdateInEntity,
  NTokkiUpdList,
} from '~/repositories/cmn/entities/CertificationSurveySpecialMatterUpdateEntity'
import { UPDATE_KBN } from '~/constants/classification-constants'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or26426OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}
/**************************************************
 * 引継情報を取得する
 **************************************************/
const props = defineProps<Props>()

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or26426StateType>({
  cpId: Or26426Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or26426Const.DEFAULT.IS_OPEN
    },
  },
})
const { refValue } = useScreenTwoWayBind<Or26426DataValue>({
  cpId: Or26426Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = {
  showItemList: [],
}

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
const { setChildCpBinds } = useScreenUtils()

// 画面タイトル
const mo00024 = ref<Mo00024Type>({
  isOpen: Or26426Const.DEFAULT.IS_OPEN,
})

// ■共通処理の登録権限チェックを行う
const hasView = hasRegistAuth('/care-manager/mockup-sample/picture-book-buttons')

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})

// 有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
// 特記事項(項目1)
const orX0217_1 = ref({ uniqueCpId: OrX0217Const.CP_ID(1) })
// 特記事項(項目2)
const orX0217_2 = ref({ uniqueCpId: OrX0217Const.CP_ID(2) })
// 特記事項(項目3)
const orX0217_3 = ref({ uniqueCpId: OrX0217Const.CP_ID(3) })
// 特記事項(項目4)
const orX0217_4 = ref({ uniqueCpId: OrX0217Const.CP_ID(4) })
// 特記事項(項目5)
const orX0217_5 = ref({ uniqueCpId: OrX0217Const.CP_ID(5) })
// 特記事項(項目6)
const orX0217_6 = ref({ uniqueCpId: OrX0217Const.CP_ID(6) })
// 特記事項(項目7)
const orX0217_7 = ref({ uniqueCpId: OrX0217Const.CP_ID(7) })

// ダイアログ表示フラグ
const showDialog21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0217Const.CP_ID(1)]: orX0217_1.value,
  [OrX0217Const.CP_ID(2)]: orX0217_2.value,
  [OrX0217Const.CP_ID(3)]: orX0217_3.value,
  [OrX0217Const.CP_ID(4)]: orX0217_4.value,
  [OrX0217Const.CP_ID(5)]: orX0217_5.value,
  [OrX0217Const.CP_ID(6)]: orX0217_6.value,
  [OrX0217Const.CP_ID(7)]: orX0217_7.value,
})

// 親画面
const defaultOnewayModelValue: Or26426OnewayType = {
  //  計画期間ID
  sc1Id: '',
  //  調査票ID
  cschId: '',
  // 特記コード
  specialNoteCd: '',
}

// 初期化コンポーネント
const localOneway = reactive({
  or26426: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 認定調査票特記事項
  mo00024Oneway: {
    width: '1000px',
    height: '582px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or26426',
      toolbarTitle: t('label.cert-survey-led-special'),
      toolbarName: 'Or26426ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // 認定調査票特記事項コンテンツエリアタブ
  mo00043OneWay: {
    tabItems: [
      { id: Or26426Const.TAB.TAB_ID_ITEM1, title: t('label.project-attribute-1') },
      { id: Or26426Const.TAB.TAB_ID_ITEM2, title: t('label.project-attribute-2') },
      { id: Or26426Const.TAB.TAB_ID_ITEM3, title: t('label.project-attribute-3') },
      { id: Or26426Const.TAB.TAB_ID_ITEM4, title: t('label.project-attribute-4') },
      { id: Or26426Const.TAB.TAB_ID_ITEM5, title: t('label.project-attribute-5') },
      { id: Or26426Const.TAB.TAB_ID_ITEM6, title: t('label.project-attribute-6') },
      { id: Or26426Const.TAB.TAB_ID_ITEM7, title: t('label.project-attribute-7') },
    ],
  } as Mo00043OnewayType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定ボタン
  mo00609SaveBtnOneway: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  // 特記事項(項目1)
  orX0217OnewayTypeItem1: {
    ...props.onewayModelValue,
    // 特記事項タイトル
    tokkiTitle: '',
    // 特記事項(固定)リスト
    tokkiTextList: [],
  } as OrX0217OnewayType,
  // 特記事項(項目2)
  orX0217OnewayTypeItem2: {
    ...props.onewayModelValue,
    // 特記事項タイトル
    tokkiTitle: '',
    // 特記事項(固定)リスト
    tokkiTextList: [],
  } as OrX0217OnewayType,
  // 特記事項(項目3)
  orX0217OnewayTypeItem3: {
    ...props.onewayModelValue,
    // 特記事項タイトル
    tokkiTitle: '',
    // 特記事項(固定)リスト
    tokkiTextList: [],
  } as OrX0217OnewayType,
  // 特記事項(項目4)
  orX0217OnewayTypeItem4: {
    ...props.onewayModelValue,
    // 特記事項タイトル
    tokkiTitle: '',
    // 特記事項(固定)リスト
    tokkiTextList: [],
  } as OrX0217OnewayType,
  // 特記事項(項目5)
  orX0217OnewayTypeItem5: {
    ...props.onewayModelValue,
    // 特記事項タイトル
    tokkiTitle: '',
    // 特記事項(固定)リスト
    tokkiTextList: [],
  } as OrX0217OnewayType,
  // 特記事項(項目6)
  orX0217OnewayTypeItem6: {
    ...props.onewayModelValue,
    // 特記事項タイトル
    tokkiTitle: '',
    // 特記事項(固定)リスト
    tokkiTextList: [],
  } as OrX0217OnewayType,
  // 特記事項(項目7)
  orX0217OnewayTypeItem7: {
    ...props.onewayModelValue,
    // 特記事項タイトル
    tokkiTitle: '',
    // 特記事項(固定)リスト
    tokkiTextList: [],
  } as OrX0217OnewayType,
})

// ローカル双方向bind
const local = reactive({
  mo00043: { id: Or26426Const.TAB.TAB_ID_ITEM1 } as Mo00043Type,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // AC001_初期表示
  await init()
})

/**
 *  AC001_初期表示
 */
async function init() {
  await nextTick()
  // 週間計画一覧情報取得(IN)
  const inputData: CertificationSurveySpecialMatterInitSelectInEntity = {
    // 計画期間ID
    sc1Id: localOneway.or26426.sc1Id,
    // 調査票ID
    cschId: localOneway.or26426.cschId,
  }
  // 週間計画一覧情報取得する。
  const ret: CertificationSurveySpecialMatterInitSelectOutEntity = await ScreenRepository.select(
    'certificationSurveySpecialMatterInitSelect',
    inputData
  )

  // 特記事項(固定)リスト(項目1)
  localOneway.orX0217OnewayTypeItem1.tokkiTextList = ret.data.tokkiList[0].tokkiTextList
  // 特記事項(固定)リスト(項目2)
  localOneway.orX0217OnewayTypeItem2.tokkiTextList = ret.data.tokkiList[1].tokkiTextList
  // 特記事項(固定)リスト(項目3)
  localOneway.orX0217OnewayTypeItem3.tokkiTextList = ret.data.tokkiList[2].tokkiTextList
  // 特記事項(固定)リスト(項目4)
  localOneway.orX0217OnewayTypeItem4.tokkiTextList = ret.data.tokkiList[3].tokkiTextList
  // 特記事項(固定)リスト(項目5)
  localOneway.orX0217OnewayTypeItem5.tokkiTextList = ret.data.tokkiList[4].tokkiTextList
  // 特記事項(固定)リスト(項目6)
  localOneway.orX0217OnewayTypeItem6.tokkiTextList = ret.data.tokkiList[5].tokkiTextList
  // 特記事項(固定)リスト(項目7)
  localOneway.orX0217OnewayTypeItem7.tokkiTextList = ret.data.tokkiList[6].tokkiTextList
  // 特記事項タイトル(項目1)
  localOneway.orX0217OnewayTypeItem1.tokkiTitle = ret.data.tokkiList[0].tokkiTitle
  // 特記事項タイトル(項目2)
  localOneway.orX0217OnewayTypeItem2.tokkiTitle = ret.data.tokkiList[1].tokkiTitle
  // 特記事項タイトル(項目3)
  localOneway.orX0217OnewayTypeItem3.tokkiTitle = ret.data.tokkiList[2].tokkiTitle
  // 特記事項タイトル(項目4)
  localOneway.orX0217OnewayTypeItem4.tokkiTitle = ret.data.tokkiList[3].tokkiTitle
  // 特記事項タイトル(項目5)
  localOneway.orX0217OnewayTypeItem5.tokkiTitle = ret.data.tokkiList[4].tokkiTitle
  // 特記事項タイトル(項目6)
  localOneway.orX0217OnewayTypeItem6.tokkiTitle = ret.data.tokkiList[5].tokkiTitle
  // 特記事項タイトル(項目7)
  localOneway.orX0217OnewayTypeItem7.tokkiTitle = ret.data.tokkiList[6].tokkiTitle
  // 項目1 ~ 項目7
  const showItemList = [] as ShowItemList[]
  let index = 0
  for (const item of ret.data.tokkiList) {
    index++
    // 特記事項リスト
    const nTokkiList = [] as NTokkiList[]
    // 特記事項リスト.特記事項内容リストにデータが存在する場合、
    if (item.nTokkiList?.length > 0) {
      // 特記事項内容リスト
      for (const nTokki of item.nTokkiList) {
        nTokkiList.push({
          // カウンター
          counter: nTokki.counter,
          // 認定（特記：大）
          n1tCd: nTokki.n1tCd,
          // 認定（特記：小）
          n2tCd: { value: nTokki.n2tCd },
          // 特記事項
          memoKnj: { value: nTokki.memoKnj },
          // 表示順
          seqNo: nTokki.seqNo,
          // 更新区分
          updateKbn: UPDATE_KBN.NONE,
        })
      }
      // 親画面.特記コードが存在するタブ
      if (
        localOneway.or26426.specialNoteCd !== '' &&
        localOneway.or26426.specialNoteCd.substring(0, 1) === index.toString()
      ) {
        // 画面.特記事項内容の末に一行を追加し、特記事項IDを親画面.特記コード、特記事項を空で設定する。
        nTokkiList.push({
          // カウンター
          counter: '',
          // 認定（特記：大）
          n1tCd: '',
          // 認定（特記：小）
          n2tCd: { value: localOneway.or26426.specialNoteCd },
          // 特記事項
          memoKnj: { value: '' },
          // 表示順
          seqNo: (item.nTokkiList.length + 1).toString(),
          // 更新区分
          updateKbn: UPDATE_KBN.CREATE,
        })
      }
    } else {
      // 特記事項リスト.特記事項内容リストにデータが存在しない場合、
      // 親画面.特記コードが存在するタブ
      if (
        localOneway.or26426.specialNoteCd !== '' &&
        localOneway.or26426.specialNoteCd.substring(0, 1) === index.toString()
      ) {
        // 新規行.特記事項ID＝親画面.特記コード、新規行.特記事項を空で設定する
        nTokkiList.push({
          // カウンター
          counter: '',
          // 認定（特記：大）
          n1tCd: '',
          // 認定（特記：小）
          n2tCd: { value: localOneway.or26426.specialNoteCd },
          // 特記事項
          memoKnj: { value: '' },
          // 表示順
          seqNo: '1',
          // 更新区分
          updateKbn: UPDATE_KBN.CREATE,
        })
      } else {
        // 親画面.特記コードが存在しないタブ
        // 新規行の特記事項内容の特記事項ID、特記事項を空で設定する。
        nTokkiList.push({
          // カウンター
          counter: '',
          // 認定（特記：大）
          n1tCd: '',
          // 認定（特記：小）
          n2tCd: { value: '' },
          // 特記事項
          memoKnj: { value: '' },
          // 表示順
          seqNo: '1',
          // 更新区分
          updateKbn: UPDATE_KBN.CREATE,
        })
      }
    }
    showItemList.push({
      nTokkiList: nTokkiList,
    })
  }

  setChildCpBinds(props.parentUniqueCpId, {
    Or26426: {
      twoWayValue: {
        showItemList: showItemList,
      },
    },
  })

  if (localOneway.or26426.specialNoteCd.startsWith('1')) {
    local.mo00043.id = Or26426Const.TAB.TAB_ID_ITEM1
  }
  if (localOneway.or26426.specialNoteCd.startsWith('2')) {
    local.mo00043.id = Or26426Const.TAB.TAB_ID_ITEM2
  }
  if (localOneway.or26426.specialNoteCd.startsWith('3')) {
    local.mo00043.id = Or26426Const.TAB.TAB_ID_ITEM3
  }
  if (localOneway.or26426.specialNoteCd.startsWith('4')) {
    local.mo00043.id = Or26426Const.TAB.TAB_ID_ITEM4
  }
  if (localOneway.or26426.specialNoteCd.startsWith('5')) {
    local.mo00043.id = Or26426Const.TAB.TAB_ID_ITEM5
  }
  if (localOneway.or26426.specialNoteCd.startsWith('6')) {
    local.mo00043.id = Or26426Const.TAB.TAB_ID_ITEM6
  }
  if (localOneway.or26426.specialNoteCd.startsWith('7')) {
    local.mo00043.id = Or26426Const.TAB.TAB_ID_ITEM7
  }
}

/**
 * 「閉じるボタン」押下
 *
 */
async function close() {
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(
      t('message.i-cmn-11329'),
      t('btn.yes'),
      'normal1',
      'destroy1',
      'normal3'
    )
    switch (dialogResult) {
      case OrX0217Const.DEFAULT.DIALOG_RESULT_YES:
        setState({ isOpen: false })
        break
      case OrX0217Const.DEFAULT.DIALOG_RESULT_NO:
        setState({ isOpen: false })
        break
      case OrX0217Const.DEFAULT.DIALOG_RESULT_CANCEL:
        break
    }
  } else {
    setState({ isOpen: false })
  }
}

/**
 * 「確定ボタン」押下
 *
 */
async function onConfirm() {
  // 画面に変更がない場合
  if (!isEdit.value) {
    await openInfoDialog(t('message.i-cmn-21800'), t('btn.ok'), 'normal1', 'blank', 'blank')
    return
  } else {
    // 変更がある場合
    // 特記事項情報を保存する。
    await onSaveProc()
    // AC001-1初期情報取得を行って、画面を再表示する。
    await init()
  }
}

/**
 * 特記事項情報を保存する
 *
 */
async function onSaveProc() {
  const nTokkiUpdList = [] as NTokkiUpdList[]
  for (const item of refValue.value!.showItemList) {
    if (item.nTokkiList !== undefined && item.nTokkiList.length > 0) {
      for (const nTokki of item.nTokkiList) {
        nTokkiUpdList.push({
          // カウンター
          counter: nTokki.counter,
          // 認定（特記：大）
          n1tCd: nTokki.n1tCd,
          // 認定（特記：小）
          n2tCd: nTokki.n2tCd.value,
          // 特記事項
          memoKnj: nTokki.memoKnj.value,
          // 表示順
          seqNo: nTokki.seqNo,
          // 更新区分
          updateKbn: nTokki.updateKbn,
        })
      }
    }
  }

  const param: CertificationSurveySpecialMatterUpdateInEntity = {
    // 計画期間ID
    sc1Id: localOneway.or26426.sc1Id,
    // 調査票ID
    cschId: localOneway.or26426.cschId,
    // 特記事項内容リスト
    nTokkiList: nTokkiUpdList,
  }
  await ScreenRepository.update('certificationSurveySpecialMatterUpdate', param)
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 編集破棄ダイアログ表示
 *
 * @param msg - msg
 *
 * @param firstLabel - firstLabel
 *
 * @param firstBtn - firstBtn
 *
 * @param secondBtn - secondBtn
 *
 * @param thirdBtn - thirdBtn
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(
  msg: string,
  firstLabel: string,
  firstBtn: Or21814FirstBtnType,
  secondBtn: Or21814SecondBtnType,
  thirdBtn: Or21814ThirdBtnType
): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: firstBtn,
      firstBtnLabel: firstLabel,
      secondBtnType: secondBtn,
      secondBtnLabel: t('btn.no'),
      thirdBtnType: thirdBtn,
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = OrX0217Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = OrX0217Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = OrX0217Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = OrX0217Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

watch(
  () => refValue.value!.showItemList,
  (newValue) => {
    console.log(newValue)
  },
  { deep: true, immediate: false }
)
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043"
        :oneway-model-value="localOneway.mo00043OneWay"
      ></base-mo00043>
      <!-- タブ switch -->
      <c-v-window v-model="local.mo00043.id">
        <!-- タブ：項目1 -->
        <c-v-window-item value="item1">
          <g-custom-or-x-0217
            v-if="refValue!.showItemList[0]"
            v-model="refValue!.showItemList[0]"
            v-bind="orX0217_1"
            :oneway-model-value="localOneway.orX0217OnewayTypeItem1"
            :parent-unique-cp-id="props.uniqueCpId"
          ></g-custom-or-x-0217>
        </c-v-window-item>
        <!-- タブ：項目2 -->
        <c-v-window-item value="item2">
          <g-custom-or-x-0217
            v-if="refValue!.showItemList[1]"
            v-model="refValue!.showItemList[1]"
            v-bind="orX0217_2"
            :oneway-model-value="localOneway.orX0217OnewayTypeItem2"
            :parent-unique-cp-id="props.uniqueCpId"
          ></g-custom-or-x-0217>
        </c-v-window-item>
        <!-- タブ：項目3 -->
        <c-v-window-item value="item3">
          <g-custom-or-x-0217
            v-if="refValue!.showItemList[2]"
            v-model="refValue!.showItemList[2]"
            v-bind="orX0217_3"
            :oneway-model-value="localOneway.orX0217OnewayTypeItem3"
            :parent-unique-cp-id="props.uniqueCpId"
          ></g-custom-or-x-0217>
        </c-v-window-item>
        <!-- タブ：項目4 -->
        <c-v-window-item value="item4">
          <g-custom-or-x-0217
            v-if="refValue!.showItemList[3]"
            v-model="refValue!.showItemList[3]"
            v-bind="orX0217_4"
            :oneway-model-value="localOneway.orX0217OnewayTypeItem4"
            :parent-unique-cp-id="props.uniqueCpId"
          ></g-custom-or-x-0217>
        </c-v-window-item>
        <!-- タブ：項目5 -->
        <c-v-window-item value="item5">
          <g-custom-or-x-0217
            v-if="refValue!.showItemList[4]"
            v-model="refValue!.showItemList[4]"
            v-bind="orX0217_5"
            :oneway-model-value="localOneway.orX0217OnewayTypeItem5"
            :parent-unique-cp-id="props.uniqueCpId"
          ></g-custom-or-x-0217
        ></c-v-window-item>
        <!-- タブ：項目6 -->
        <c-v-window-item value="item6">
          <g-custom-or-x-0217
            v-if="refValue!.showItemList[5]"
            v-model="refValue!.showItemList[5]"
            v-bind="orX0217_6"
            :oneway-model-value="localOneway.orX0217OnewayTypeItem6"
            :parent-unique-cp-id="props.uniqueCpId"
          ></g-custom-or-x-0217
        ></c-v-window-item>
        <!-- タブ：項目7 -->
        <c-v-window-item value="item7">
          <g-custom-or-x-0217
            v-if="refValue!.showItemList[6]"
            v-model="refValue!.showItemList[6]"
            v-bind="orX0217_7"
            :oneway-model-value="localOneway.orX0217OnewayTypeItem7"
            :parent-unique-cp-id="props.uniqueCpId"
          ></g-custom-or-x-0217
        ></c-v-window-item>
      </c-v-window>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close()"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609SaveBtnOneway"
          class="mx-2"
          :disabled="!hasView"
          @click="onConfirm()"
        >
          <!--ツールチップ表示："表示されているデータを保存します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.save')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialog21814"
    v-bind="or21814"
  />
</template>
