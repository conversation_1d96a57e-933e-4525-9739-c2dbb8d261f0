<script setup lang="ts">
/**
 * OrX0201:［アセスメント］画面（居宅）タブタイトル
 *
 * @description
 * ［アセスメント］画面（居宅）
 *
 * <AUTHOR>
 */
import { reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { OrX0166OnewayType } from '~/types/cmn/business/components/OrX0166Type'
import type { OrX0201OnewayType } from '~/types/cmn/business/components/OrX0201Type'
import { CustomClass } from '~/types/CustomClassType'

/****************************************************
 * Props
 ****************************************************/
interface Props {
  onewayModelValue: OrX0201OnewayType
}

const props = defineProps<Props>()

/****************************************************
 * 変数定義
 ****************************************************/
const { t } = useI18n()

const defaultOnewayModelValue: OrX0201OnewayType = {
  title: '',
  tabNo: '1',
  titleDisplayFlg: true,
  scrollToUniqueCpId: '',
  isMoveToBottomShowFlg: true,
}

const localOneway = reactive({
  mo01338Oneway: {
    itemLabel: '',
    customClass: new CustomClass({
      outerClass: 'tab-title-content',
      itemClass: 'tab-title-text',
      labelStyle: 'fontSize:20px',
    }),
  } as Mo01338OnewayType,
  orX0201Oneway: {
    ...defaultOnewayModelValue,
  } as OrX0201OnewayType,
  OrX0166Oneway: {
    text: '',
  } as OrX0166OnewayType,
})

/****************************************************
 * 関数定義
 ****************************************************/

const handleClick = () => {
  const issuesAndGoalListComponentWrapper = document.getElementById(
    localOneway.orX0201Oneway.scrollToUniqueCpId + 'issuesAndGoalListComponentWrapper'
  )
  if (issuesAndGoalListComponentWrapper) {
    const offsetTop = issuesAndGoalListComponentWrapper.offsetTop
    window.scroll({ top: offsetTop, behavior: 'smooth' })
  }
}

/****************************************************
 * watch関数
 ****************************************************/
watch(
  () => props.onewayModelValue,
  () => {
    if (!props.onewayModelValue) return

    localOneway.mo01338Oneway.itemLabel = props.onewayModelValue.title
    localOneway.orX0201Oneway = { ...localOneway.orX0201Oneway, ...props.onewayModelValue }

    localOneway.OrX0166Oneway.text = props.onewayModelValue.tabNo
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <c-v-row
    no-gutters
    class="d-flex align-center title-content"
  >
    <c-v-col
      v-if="localOneway.orX0201Oneway.titleDisplayFlg"
      cols="auto"
      class="mr-4"
    >
      <g-custom-or-x-0166 :oneway-model-value="localOneway.OrX0166Oneway"> </g-custom-or-x-0166>
    </c-v-col>
    <c-v-col
      v-if="localOneway.orX0201Oneway.titleDisplayFlg"
      cols="auto"
    >
      <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway" />
    </c-v-col>
    <slot name="customContent" />
    <c-v-col
      v-if="localOneway.orX0201Oneway.isMoveToBottomShowFlg"
      cols="auto"
      class="d-flex action-button align-center"
      @click="handleClick"
    >
      {{ t('label.should-solution-issues-and-goal') }}
      <span class="bottom-arrow d-block ml-2">↓</span>
    </c-v-col>
  </c-v-row>
</template>

<style lang="scss" scoped>
.v-col {
  padding: 0;
}
.title-content {
  height: 32px;
  .tab-title-content {
    background-color: transparent;
    :deep(.item-label) {
      font-size: 20px !important;
    }
  }
  :deep(.tab-title-text) {
    display: none;
  }
  .action-button {
    margin-left: auto;
    height: 32px;
    color: rgb(var(--v-theme-black-900)) !important;
    background-color: transparent !important;
    cursor: pointer;
    user-select: none;
  }
  .bottom-arrow {
    font-size: 14px;
    transform: scaleX(1.8);
  }
}
</style>
