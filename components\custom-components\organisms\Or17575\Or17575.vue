<script setup lang="ts">
/**
 * Or17575:有機体:(薬剤マスタ)薬剤マスタリスト入力
 * GUI00671_薬剤マスタ
 *
 * @description
 * 薬剤マスタ
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { nextTick, ref, onMounted, watch, type ComponentPublicInstance } from 'vue'
import { cloneDeep } from 'lodash'
import { Or17575Const } from '~/components/custom-components/organisms/Or17575/Or17575.constants'
import type {
  Or17575MsgBtnType,
  Or17575StateType,
} from '~/components/custom-components/organisms/Or17575/Or17575.type'
import {
  useSetupChildProps,
  useScreenTwoWayBind,
  useScreenOneWayBind,
  useValidation,
  useScreenStore,
} from '#imports'
import type {
  Or17575Type,
  Or17575OnewayType,
  DrugUnitMasterInfoType,
  MedicationMasterModel,
} from '~/types/cmn/business/components/Or17575Type'
import type { Mo01336OnewayType } from '~/types/business/components/Mo01336Type'
import type { Mo01274Type } from '@/types/business/components/Mo01274Type'
import type { Mo01278Type, Mo01278OnewayType } from '@/types/business/components/Mo01278Type'
import type { Mo01282Type, Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or01544Logic } from '~/components/custom-components/organisms/Or01544/Or01544.logic'
import { Or01544Const } from '~/components/custom-components/organisms/Or01544/Or01544.constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  Mo01354Headers,
  Mo01354Items,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type {
  DrugMasterInfo,
  DrugUnitMasterInfo,
} from '~/repositories/cmn/entities/MedicationMasterEntity'
import type { Mo00045Type, Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { CustomClass } from '~/types/CustomClassType'

const { t } = useI18n()
const validation = useValidation()
const textRefs = new Map<string, HTMLElement>()

/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or17575Type
  onewayModelValue: Or17575OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}
const props = defineProps<Props>()

// 子コンポーネント用変数
const or21814 = ref({ uniqueCpId: Or17575Const.DEFAULT.STR.EMPTY })
const or01544 = ref({ uniqueCpId: Or17575Const.DEFAULT.STR.EMPTY })

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Mo01354Items>({
  cpId: Or17575Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(Or17575Const.DEFAULT.NUMBER.ZERO)]: or21814.value,
  [Or01544Const.CP_ID(Or17575Const.DEFAULT.NUMBER.ZERO)]: or01544.value,
})

useScreenOneWayBind<Or17575StateType>({
  cpId: Or17575Const.CP_ID(Or17575Const.DEFAULT.NUMBER.ZERO),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (value) {
        // 親分類id
        local.parentId = value.parentId
        // 機能ID
        local.functionId = value.functionId
        // 階層
        local.level = String(value.level)
        // 実行フラグ
        switch (value.executeFlag) {
          case Or17575Const.DEFAULT.STR.FILTER:
            void nextTick(() => {
              dataFilter(value.executeFlag)
            })
            break
          case Or17575Const.DEFAULT.STR.IMPORT:
            void nextTick(() => {
              dataImport()
            })
            break
          default:
            void nextTick(() => {
              dataFilter(Or17575Const.DEFAULT.STR.INIT)
            })
            break
        }
      }
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const local = {
  /**
   * 親分類ID
   */
  parentId: Or17575Const.DEFAULT.STR.EMPTY,
  /**
   * 機能ID
   */
  functionId: Or17575Const.DEFAULT.STR.EMPTY,
  /**
   * 階層
   */
  level: Or17575Const.DEFAULT.STR.EMPTY,
  /**
   * 全てデータ
   */
  allDatas: new Array<MedicationMasterModel>(),
  /**
   * ［薬剤検索・取込］画面薬剤マスタ情報リスト
   */
  importDrugMasterInfoList: [] as DrugMasterInfo[],
}
const isLoading = ref(false)

/**
 * 中止(適用フラグ
 */
const mo00018OnewayType: Mo00018OnewayType = {
  customClass: new CustomClass({ outerClass: 'useFlg', labelClass: '' }),
} as Mo00018OnewayType

/**
 * 薬剤単位の選択肢
 */
const unitItems: Mo01282OnewayType = {
  items: [],
  itemTitle: 'unitnameKnj',
  itemValue: 'unitId',
}

/**
 * 剤型単位の選択肢
 */
const agentTypeItems: Mo01282OnewayType = {
  items: [],
  itemTitle: 'label',
  itemValue: 'value',
}

/**
 * 経路単位の選択肢
 */
const routeItems: Mo01282OnewayType = {
  items: [],
  itemTitle: 'label',
  itemValue: 'value',
}

/**
 * 算定区分の選択肢
 */
const calculationCategoryItems: Mo01282OnewayType = {
  items: [],
  itemTitle: 'label',
  itemValue: 'value',
}

/**
 * 用量単位の選択肢
 */
const dosageUnitItems: Mo01282OnewayType = {
  items: [],
  itemTitle: 'label',
  itemValue: 'value',
}

/**
 * ボタンタイプ
 */
const or01544Type = ref<string>(Or17575Const.DEFAULT.STR.EMPTY)

const mo01354Type = ref<Mo01354Type>({
  values: {
    selectedRowId: Or17575Const.DEFAULT.STR.EMPTY,
    selectedRowIds: [],
    items: [],
    scrollToId: '',
  },
} as Mo01354Type)

const mo01354Oneway = ref<Mo01354OnewayType>({
  /** 初期値：ヘッダー情報 */
  headers: [] as Mo01354Headers[],
  height: '343px',
  // Mo01354 表コンポーネントが提供する、デフォルトのヘッダーを利用しない（カスタマイズ可能になる）
  useDefaultHeader: true,
  // 行入替用のアイコンを表示
  showDragIndicatorFlg: false,
} as Mo01354OnewayType)

/**
 * データのIDを削除する
 */
const deleteIdList: string[] = []

/**
 * 最大の表示順序番号
 */
let sortMaxValue = Or17575Const.DEFAULT.NUMBER.ZERO

/**
 * 最大のId番号
 */
let IdMaxValue = Or17575Const.DEFAULT.NUMBER.ZERO

/**
 * 分類名称
 */
const mo00045Oneway = ref<Mo00045OnewayType>({
  isRequired: true,
  showItemLabel: false,
  maxLength: '240',
  rules: [validation.required],
})

/**
 * 表示順
 */
const mo01278OnewayType = ref<Mo01278OnewayType>({
  min: 1,
  max: 9999,
  isEditCamma: false,
} as Mo01278OnewayType)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * データのスクリーニング
 *
 * @param executeFlag - 実行フラグ
 */
const dataFilter = (executeFlag: string | undefined) => {
  sortMaxValue = Or17575Const.DEFAULT.NUMBER.ZERO
  IdMaxValue = Or17575Const.DEFAULT.NUMBER.ZERO
  // 薬剤マスタ情報
  const list: MedicationMasterModel[] = []
  for (const item of local.allDatas) {
    if (item) {
      if (sortMaxValue < Number(item.sort.value)) {
        sortMaxValue = Number(item.sort.value)
      }
      if (
        !executeFlag ||
        Or17575Const.DEFAULT.STR.EMPTY === executeFlag ||
        Or17575Const.DEFAULT.STR.INIT === executeFlag
      ) {
        list.push(item)
      } else if (Or17575Const.DEFAULT.STR.FILTER === executeFlag) {
        if (local.parentId === item.parentId) {
          list.push(item)
        }
      }
    }
  }
  // 表示順でソートする
  list.sort((a, b) => Number(a.sort.value) - Number(b.sort.value))
  mo01354Type.value.values.items = cloneDeep(list)
  if (mo01354Type.value.values.items.length > 0) {
    mo01354Type.value.values.selectedRowId = mo01354Type.value.values.items[0].id
  } else {
    mo01354Type.value.values.selectedRowId = Or17575Const.DEFAULT.STR.EMPTY
  }
  if (mo01354Type.value.values.items.length > Or17575Const.DEFAULT.NUMBER.ZERO) {
    // Or01544のダイアログ状態を更新する
    Or01544Logic.state.set({
      uniqueCpId: or01544.value.uniqueCpId,
      state: {
        addBtndisabled: false,
        copyBtndisabled: false,
        deleteBtndisabled: false,
      },
    })
  } else {
    // 行複写、行削除ボタンは非活性
    // Or01544のダイアログ状態を更新する
    Or01544Logic.state.set({
      uniqueCpId: or01544.value.uniqueCpId,
      state: {
        addBtndisabled: false,
        copyBtndisabled: true,
        deleteBtndisabled: true,
      },
    })
  }
}

/**
 * 取込データ
 */
const dataImport = () => {
  for (const item of local.importDrugMasterInfoList) {
    if (item) {
      sortMaxValue = sortMaxValue + Or17575Const.DEFAULT.NUMBER.ONE
      IdMaxValue = IdMaxValue + Or17575Const.DEFAULT.NUMBER.ONE
      mo01354Type.value.values.items.push({
        id: String(IdMaxValue),
        drugId: {
          value: undefined,
          unit: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01336OnewayType,
        drugCode: {
          value: item.drugCode,
        } as Mo01274Type,
        drugIndex: {
          value: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01274Type,
        drugRezeCd: {
          value: item.drugRezeCd,
        } as Mo01274Type,
        drugKnj: {
          value: item.drugKnj,
        } as Mo00045Type,
        drugKana: {
          value: item.drugKana,
        } as Mo01274Type,
        oldtanka: {
          value: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01278Type,
        buyunitid: {
          modelValue: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01282Type,
        irisuu: {
          value: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01278Type,
        bunryou: {
          value: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01278Type,
        useunitid: {
          modelValue: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01282Type,
        youhouKnj: {
          value: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01274Type,
        touyonissuu: {
          value: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01278Type,
        touyonissuuUnitid: {
          modelValue: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01282Type,
        odd: {
          modelValue: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01282Type,
        shape: {
          modelValue: item.shape,
        } as Mo01282Type,
        keiroKbn: {
          modelValue: item.keiroKbn,
          disabled: false,
        } as Mo01282Type,
        keiroKbnDisabled: item.shape !== Or17575Const.DEFAULT.STR.ONE,
        memoKnj: {
          value: Or17575Const.DEFAULT.STR.EMPTY,
        } as Mo01274Type,
        sort: {
          value: String(sortMaxValue),
        } as Mo01278Type,
        parentId: item.parentId,
        updateKbn: item.updateKbn,
        useFlg: {
          modelValue: item.useFlg === Or17575Const.DEFAULT.STR.ONE,
        } as Mo00018Type,
        level: item.level,
        bunruiId: item.bunruiId,
      } as MedicationMasterModel)
    }
  }
}

/**
 * データソース変更監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue) {
      local.importDrugMasterInfoList = newValue.importDrugMasterInfoList
      medicineMasterInit(newValue.drugMasterInfoList, newValue.drugUnitMasterInfoList)
    }
  },
  { deep: true }
)

/**
 * データ項目変更監視
 */
watch(
  () => mo01354Type.value.values.items,
  (newValue) => {
    if (newValue) {
      if (refValue.value) {
        refValue.value.items = cloneDeep(newValue)
      }
      updateResultDate()
    }
  },
  { deep: true }
)

/**
 * 操作後の最新データを返す
 */
const updateResultDate = () => {
  const drugMasterInfoList: DrugMasterInfo[] = []
  for (const item of mo01354Type.value.values.items) {
    if (item) {
      const data = {
        level: item.level as string,
        parentId: item.parentId as string,
        drugId: String((item.drugId as Mo01336OnewayType).value),
        drugKnj: (item.drugKnj as Mo00045Type).value,
        drugKana: (item.drugKana as Mo01274Type).value,
        drugCode: (item.drugCode as Mo01274Type).value,
        shape: (item.shape as Mo01282Type).modelValue,
        useFlg: (item.useFlg as Mo00018Type).modelValue
          ? Or17575Const.DEFAULT.STR.ONE
          : Or17575Const.DEFAULT.STR.ZERO,
        memoKnj: (item.memoKnj as Mo01274Type).value,
        sort: (item.sort as Mo01278Type).value,
        drugIndex: (item.drugIndex as Mo01274Type).value,
        oldtanka: (item.oldtanka as Mo01278Type).value,
        buyunitid: (item.buyunitid as Mo01282Type).modelValue,
        irisuu: (item.irisuu as Mo01278Type).value,
        useunitid: (item.useunitid as Mo01282Type).modelValue,
        youhouKnj: (item.youhouKnj as Mo01274Type).value,
        touyonissuu: (item.touyonissuu as Mo01278Type).value,
        touyonissuuUnitid: (item.touyonissuuUnitid as Mo01282Type).modelValue,
        odd: (item.odd as Mo01282Type).modelValue,
        bunryou: (item.bunryou as Mo01278Type).value,
        keiroKbn: (item.keiroKbn as Mo01282Type).modelValue,
        drugRezeCd: (item.drugRezeCd as Mo01274Type).value,
        bunruiId: item.bunruiId as string,
        updateKbn: item.updateKbn as string,
      } as DrugMasterInfo
      drugMasterInfoList.push(data)
    }
  }
  for (const item of local.allDatas) {
    if (item) {
      if (deleteIdList.includes(String(item.drugId.value))) {
        drugMasterInfoList.push({
          level: item.level,
          parentId: item.parentId,
          drugId: String(item.drugId.value),
          drugKnj: item.drugKnj.value,
          drugKana: item.drugKana.value,
          drugCode: item.drugCode.value,
          shape: item.shape.modelValue,
          useFlg: item.useFlg.modelValue
            ? Or17575Const.DEFAULT.STR.ONE
            : Or17575Const.DEFAULT.STR.ZERO,
          memoKnj: item.memoKnj.value,
          sort: item.sort.value,
          drugIndex: item.drugIndex.value,
          oldtanka: item.oldtanka.value,
          buyunitid: item.buyunitid.modelValue,
          irisuu: item.irisuu.value,
          useunitid: item.useunitid.modelValue,
          youhouKnj: item.youhouKnj.value,
          touyonissuu: item.touyonissuu.value,
          touyonissuuUnitid: item.touyonissuuUnitid.modelValue,
          odd: item.odd.modelValue,
          bunryou: item.bunryou.value,
          keiroKbn: item.keiroKbn.modelValue,
          drugRezeCd: item.drugRezeCd.value,
          bunruiId: item.bunruiId,
          updateKbn: Or17575Const.DEFAULT.IS_DELETE,
        } as DrugMasterInfo)
      } else {
        for (const data of drugMasterInfoList) {
          if (data) {
            if (String(item.drugId.value) === data.drugId) {
              const tempData = {
                level: item.level,
                parentId: item.parentId,
                drugId: String(item.drugId.value),
                drugKnj: item.drugKnj.value,
                drugKana: item.drugKana.value,
                drugCode: item.drugCode.value,
                shape: item.shape.modelValue,
                useFlg: item.useFlg.modelValue
                  ? Or17575Const.DEFAULT.STR.ONE
                  : Or17575Const.DEFAULT.STR.ZERO,
                memoKnj: item.memoKnj.value,
                sort: item.sort.value,
                drugIndex: item.drugIndex.value,
                oldtanka: item.oldtanka.value,
                buyunitid: item.buyunitid.modelValue,
                irisuu: item.irisuu.value,
                useunitid: item.useunitid.modelValue,
                youhouKnj: item.youhouKnj.value,
                touyonissuu: item.touyonissuu.value,
                touyonissuuUnitid: item.touyonissuuUnitid.modelValue,
                odd: item.odd.modelValue,
                bunryou: item.bunryou.value,
                keiroKbn: item.keiroKbn.modelValue,
                drugRezeCd: item.drugRezeCd.value,
                bunruiId: item.bunruiId,
                updateKbn: item.updateKbn,
              } as DrugMasterInfo
              if (JSON.stringify(tempData) !== JSON.stringify(data)) {
                data.updateKbn = Or17575Const.DEFAULT.IS_UPDATE
              }
              break
            }
          }
        }
      }
    }
  }
  const or17575Type: Or17575Type = {
    drugMasterInfoList: drugMasterInfoList,
    viewType: props.modelValue.viewType,
  }
  emit('update:modelValue', or17575Type)
}

/**
 * 最大桁数制限
 */
watch(
  () => mo01354Type.value.values.items,
  (newValue) => {
    // 行複写、行削除ボタンは非活性
    if (newValue.length > Or17575Const.DEFAULT.NUMBER.ZERO) {
      // Or01544のダイアログ状態を更新する
      Or01544Logic.state.set({
        uniqueCpId: or01544.value.uniqueCpId,
        state: {
          addBtndisabled: false,
          copyBtndisabled: false,
          deleteBtndisabled: false,
        },
      })
      if (!mo01354Type.value.values.selectedRowId) {
        mo01354Type.value.values.selectedRowId = newValue[0].id
      }
    } else {
      // Or01544のダイアログ状態を更新する
      Or01544Logic.state.set({
        uniqueCpId: or01544.value.uniqueCpId,
        state: {
          addBtndisabled: false,
          copyBtndisabled: true,
          deleteBtndisabled: true,
        },
      })
      mo01354Type.value.values.selectedRowId = Or17575Const.DEFAULT.STR.EMPTY
    }
    for (const item of newValue) {
      if (item) {
        // 医薬品コード
        if ((item.drugCode as Mo01274Type).value?.length > 30) {
          item.drugCode = {
            value: (item.drugCode as Mo01274Type).value.substring(0, 30),
          } as Mo01274Type
        }
        // 施設内コード
        if ((item.drugIndex as Mo01274Type).value?.length > 10) {
          item.drugIndex = {
            value: (item.drugIndex as Mo01274Type).value.substring(0, 10),
          } as Mo01274Type
        }
        // レセ電算コード
        if ((item.drugRezeCd as Mo01274Type).value?.length > 9) {
          item.drugRezeCd = {
            value: (item.drugRezeCd as Mo01274Type).value.substring(0, 9),
          } as Mo01274Type
        }
        // 薬剤名
        if ((item.drugKnj as Mo00045Type).value?.length > 240) {
          item.drugKnj = {
            value: (item.drugKnj as Mo00045Type).value.substring(0, 240),
          } as Mo00045Type
        }
        // 薬剤カナ
        if ((item.drugKana as Mo01274Type).value?.length > 30) {
          item.drugKana = {
            value: (item.drugKana as Mo01274Type).value.substring(0, 30),
          } as Mo01274Type
        }
        // 購入価格
        if (
          (item.oldtanka as Mo01278Type).value.replace(',', Or17575Const.DEFAULT.STR.EMPTY).trim()
            .length > 8
        ) {
          item.oldtanka = {
            value: (item.oldtanka as Mo01278Type).value
              .replace(',', Or17575Const.DEFAULT.STR.EMPTY)
              .trim()
              .substring(0, 8),
          } as Mo01278Type
        }
        // 入数
        if (
          (item.irisuu as Mo01278Type).value?.replace(',', Or17575Const.DEFAULT.STR.EMPTY).trim()
            .length > 8
        ) {
          item.irisuu = {
            value: (item.irisuu as Mo01278Type).value
              .replace(',', Or17575Const.DEFAULT.STR.EMPTY)
              .trim()
              .substring(0, 8),
          } as Mo01278Type
        }
        // 用法
        if ((item.youhouKnj as Mo01274Type).value?.length > 256) {
          item.youhouKnj = {
            value: (item.youhouKnj as Mo01274Type).value.substring(0, 256),
          } as Mo01274Type
        }
        // 用量
        if (
          (item.touyonissuu as Mo01278Type).value
            ?.replace(',', Or17575Const.DEFAULT.STR.EMPTY)
            .trim().length > 3
        ) {
          item.touyonissuu = {
            value: (item.touyonissuu as Mo01278Type).value
              .replace(',', Or17575Const.DEFAULT.STR.EMPTY)
              .trim()
              .substring(0, 3),
          } as Mo01278Type
        }
        // 作用・効能等
        if ((item.memoKnj as Mo01274Type).value?.length > 400) {
          item.memoKnj = {
            value: (item.memoKnj as Mo01274Type).value.substring(0, 400),
          } as Mo01274Type
        }
        // 表示順
        if (
          (item.sort as Mo01278Type).value?.replace(',', Or17575Const.DEFAULT.STR.EMPTY).trim()
            .length > 4
        ) {
          item.sort = {
            value: (item.sort as Mo01278Type).value
              .replace(',', Or17575Const.DEFAULT.STR.EMPTY)
              .trim()
              .substring(0, 4),
          } as Mo01278Type
        }

        if (sortMaxValue < Number((item.sort as Mo01278Type).value)) {
          sortMaxValue = Number((item.sort as Mo01278Type).value)
        }

        if (IdMaxValue < Number(item.id)) {
          IdMaxValue = Number(item.id)
        }
      }
    }
  },
  { deep: true }
)

onMounted(async () => {
  await initCodes()
  // 画面区分
  if (Or17575Const.DEFAULT.STR.ONE === props.modelValue.viewType) {
    mo01354Oneway.value.headers = [
      // ID
      {
        title: t('ID'),
        key: 'drugId',
        sortable: true,
        minWidth: '80px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else if (Number(a.drugId.value) < Number(b.drugId.value)) {
            return -1
          } else if (Number(a.drugId.value) > Number(b.drugId.value)) {
            return 1
          } else {
            return 0
          }
        },
      } as Mo01354Headers,
      // 中止
      {
        title: t('label.stop'),
        key: 'useFlg',
        sortable: false,
        minWidth: '70px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else if (Number(a.useFlg) < Number(b.useFlg)) {
            return -1
          } else if (Number(a.useFlg) > Number(b.useFlg)) {
            return 1
          } else {
            return 0
          }
        },
      } as Mo01354Headers,
      // 医薬品コード
      {
        title: t('label.medical-supplies-code'),
        key: 'drugCode',
        sortable: true,
        minWidth: '150px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugCode.value.localeCompare(b.drugCode.value)
          }
        },
      } as Mo01354Headers,
      // 施設内コード
      {
        title: t('label.facilities-inside-code'),
        key: 'drugIndex',
        sortable: true,
        minWidth: '150px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugIndex.value.localeCompare(b.drugIndex.value)
          }
        },
      } as Mo01354Headers,
      // レセ電算コード
      {
        title: t('label.lese-calculus-code'),
        key: 'drugRezeCd',
        sortable: true,
        minWidth: '160px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugRezeCd.value.localeCompare(b.drugRezeCd.value)
          }
        },
      } as Mo01354Headers,
      // 薬剤名
      {
        title: t('label.medical-name'),
        key: 'drugKnj',
        sortable: true,
        minWidth: '250px',
        required: true,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugKnj.value.localeCompare(b.drugKnj.value)
          }
        },
      } as Mo01354Headers,
      // 薬剤カナ
      {
        title: t('label.medical-kana'),
        key: 'drugKana',
        sortable: true,
        minWidth: '250px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugKana.value.localeCompare(b.drugKana.value)
          }
        },
      } as Mo01354Headers,
      // 購入価格
      {
        title: t('label.purchase-price'),
        key: 'oldtanka',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.oldtanka.value.localeCompare(b.oldtanka.value)
          }
        },
      } as Mo01354Headers,
      // 購入単位
      {
        title: t('label.purchase-unit'),
        key: 'buyunitid',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.buyunitid.modelValue || !b.buyunitid.modelValue) {
            return 0
          } else {
            return a.buyunitid.modelValue.localeCompare(b.buyunitid.modelValue)
          }
        },
      } as Mo01354Headers,
      // 入数
      {
        title: t('label.count'),
        key: 'irisuu',
        sortable: true,
        minWidth: '92px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.irisuu.value.localeCompare(b.irisuu.value)
          }
        },
      } as Mo01354Headers,
      // 分量
      {
        title: t('label.amount'),
        key: 'bunryou',
        sortable: true,
        minWidth: '92px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.bunryou.value.localeCompare(b.bunryou.value)
          }
        },
      } as Mo01354Headers,
      // 使用単位
      {
        title: t('label.use-unit'),
        key: 'useunitid',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.useunitid.modelValue || !b.useunitid.modelValue) {
            return 0
          } else {
            return a.useunitid.modelValue.localeCompare(b.useunitid.modelValue)
          }
        },
      } as Mo01354Headers,
      // 用法
      {
        title: t('label.usage'),
        key: 'youhouKnj',
        sortable: true,
        minWidth: '192px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.youhouKnj.value.localeCompare(b.youhouKnj.value)
          }
        },
      } as Mo01354Headers,
      // 用量
      {
        title: t('label.dosage'),
        key: 'touyonissuu',
        sortable: true,
        minWidth: '92px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.touyonissuu.value.localeCompare(b.touyonissuu.value)
          }
        },
      } as Mo01354Headers,
      // 用量単位
      {
        title: t('label.dosage-unit'),
        key: 'touyonissuuUnitid',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.touyonissuuUnitid.modelValue || !b.touyonissuuUnitid.modelValue) {
            return 0
          } else {
            return a.touyonissuuUnitid.modelValue.localeCompare(b.touyonissuuUnitid.modelValue)
          }
        },
      } as Mo01354Headers,
      // 算定区分
      {
        title: t('label.calculation-category'),
        key: 'odd',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.odd.modelValue || !b.odd.modelValue) {
            return 0
          } else {
            return a.odd.modelValue.localeCompare(b.odd.modelValue)
          }
        },
      } as Mo01354Headers,
      // 剤型
      {
        title: t('label.agent-type'),
        key: 'shape',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.shape.modelValue || !b.shape.modelValue) {
            return 0
          } else {
            return a.shape.modelValue.localeCompare(b.shape.modelValue)
          }
        },
      } as Mo01354Headers,
      // 経路
      {
        title: t('label.route'),
        key: 'keiroKbn',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.keiroKbn.modelValue || !b.keiroKbn.modelValue) {
            return 0
          } else {
            return a.keiroKbn.modelValue.localeCompare(b.keiroKbn.modelValue)
          }
        },
      } as Mo01354Headers,
      // 作用・効能等
      {
        title: t('label.action-efficacy'),
        key: 'memoKnj',
        sortable: true,
        minWidth: '150px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.memoKnj.value.localeCompare(b.memoKnj.value)
          }
        },
      } as Mo01354Headers,
      // 表示順
      {
        title: t('label.display-order'),
        key: 'sort',
        sortable: true,
        minWidth: '110px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else if (Number(a.sort.value) < Number(b.sort.value)) {
            return -1
          } else if (Number(a.sort.value) > Number(b.sort.value)) {
            return 1
          } else {
            return 0
          }
        },
      } as Mo01354Headers,
    ]
  } else {
    mo01354Oneway.value.headers = [
      // ID
      {
        title: 'ID',
        key: 'drugId',
        sortable: true,
        minWidth: '80px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else if (Number(a.drugId.value) < Number(b.drugId.value)) {
            return -1
          } else if (Number(a.drugId.value) > Number(b.drugId.value)) {
            return 1
          } else {
            return 0
          }
        },
      } as Mo01354Headers,
      // 医薬品コード
      {
        title: t('label.medical-supplies-code'),
        key: 'drugCode',
        sortable: true,
        minWidth: '150px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugCode.value.localeCompare(b.drugCode.value)
          }
        },
      } as Mo01354Headers,
      // 施設内コード
      {
        title: t('label.facilities-inside-code'),
        key: 'drugIndex',
        sortable: true,
        minWidth: '150px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugIndex.value.localeCompare(b.drugIndex.value)
          }
        },
      } as Mo01354Headers,
      // レセ電算コード
      {
        title: t('label.lese-calculus-code'),
        key: 'drugRezeCd',
        sortable: true,
        minWidth: '160px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugRezeCd.value.localeCompare(b.drugRezeCd.value)
          }
        },
      } as Mo01354Headers,
      // 薬剤名
      {
        title: t('label.medical-name'),
        key: 'drugKnj',
        sortable: true,
        minWidth: '250px',
        required: true,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugKnj.value.localeCompare(b.drugKnj.value)
          }
        },
      } as Mo01354Headers,
      // 薬剤カナ
      {
        title: t('label.medical-kana'),
        key: 'drugKana',
        sortable: true,
        minWidth: '250px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugKana.value.localeCompare(b.drugKana.value)
          }
        },
      } as Mo01354Headers,
      // 購入価格
      {
        title: t('label.purchase-price'),
        key: 'oldtanka',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.oldtanka.value.localeCompare(b.oldtanka.value)
          }
        },
      } as Mo01354Headers,
      // 購入単位
      {
        title: t('label.purchase-unit'),
        key: 'buyunitid',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.buyunitid.modelValue || !b.buyunitid.modelValue) {
            return 0
          } else {
            return a.buyunitid.modelValue.localeCompare(b.buyunitid.modelValue)
          }
        },
      } as Mo01354Headers,
      // 入数
      {
        title: t('label.count'),
        key: 'irisuu',
        sortable: true,
        minWidth: '92px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.irisuu.value.localeCompare(b.irisuu.value)
          }
        },
      } as Mo01354Headers,
      // 分量
      {
        title: t('label.amount'),
        key: 'bunryou',
        sortable: true,
        minWidth: '92px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.bunryou.value.localeCompare(b.bunryou.value)
          }
        },
      } as Mo01354Headers,
      // 使用単位
      {
        title: t('label.use-unit'),
        key: 'useunitid',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.useunitid.modelValue || !b.useunitid.modelValue) {
            return 0
          } else {
            return a.useunitid.modelValue.localeCompare(b.useunitid.modelValue)
          }
        },
      } as Mo01354Headers,
      // 用法
      {
        title: t('label.usage'),
        key: 'youhouKnj',
        sortable: true,
        minWidth: '192px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.youhouKnj.value.localeCompare(b.youhouKnj.value)
          }
        },
      } as Mo01354Headers,
      // 用量
      {
        title: t('label.dosage'),
        key: 'touyonissuu',
        sortable: true,
        minWidth: '92px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.touyonissuu.value.localeCompare(b.touyonissuu.value)
          }
        },
      } as Mo01354Headers,
      // 用量単位
      {
        title: t('label.dosage-unit'),
        key: 'touyonissuuUnitid',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.touyonissuuUnitid.modelValue || !b.touyonissuuUnitid.modelValue) {
            return 0
          } else {
            return a.touyonissuuUnitid.modelValue.localeCompare(b.touyonissuuUnitid.modelValue)
          }
        },
      } as Mo01354Headers,
      // 算定区分
      {
        title: t('label.calculation-category'),
        key: 'odd',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.odd.modelValue || !b.odd.modelValue) {
            return 0
          } else {
            return a.odd.modelValue.localeCompare(b.odd.modelValue)
          }
        },
      } as Mo01354Headers,
      // 剤型
      {
        title: t('label.agent-type'),
        key: 'shape',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.shape.modelValue || !b.shape.modelValue) {
            return 0
          } else {
            return a.shape.modelValue.localeCompare(b.shape.modelValue)
          }
        },
      } as Mo01354Headers,
      // 経路
      {
        title: t('label.route'),
        key: 'keiroKbn',
        sortable: true,
        minWidth: '120px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b || !a.keiroKbn.modelValue || !b.keiroKbn.modelValue) {
            return 0
          } else {
            return a.keiroKbn.modelValue.localeCompare(b.keiroKbn.modelValue)
          }
        },
      } as Mo01354Headers,
      // 作用・効能等
      {
        title: t('label.action-efficacy'),
        key: 'memoKnj',
        sortable: true,
        minWidth: '150px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else {
            return a.drugCode.value.localeCompare(b.drugCode.value)
          }
        },
      } as Mo01354Headers,
      // 表示順
      {
        title: t('label.display-order'),
        key: 'sort',
        sortable: true,
        minWidth: '110px',
        required: false,
        sortRaw(a: MedicationMasterModel, b: MedicationMasterModel) {
          // カスタムソートロジック
          if (!a || !b) {
            return 0
          } else if (Number(a.sort.value) < Number(b.sort.value)) {
            return -1
          } else if (Number(a.sort.value) > Number(b.sort.value)) {
            return 1
          } else {
            return 0
          }
        },
      } as Mo01354Headers,
    ]
  }
  // 初期情報取得
  isLoading.value = true
  init()
  isLoading.value = false
})

/**
 * AC001_初期情報取得
 */
function init() {
  if (props.onewayModelValue.drugMasterInfoList && props.onewayModelValue.drugUnitMasterInfoList) {
    medicineMasterInit(
      props.onewayModelValue.drugMasterInfoList,
      props.onewayModelValue.drugUnitMasterInfoList
    )
  } else {
    // 薬剤マスタリストの作成
    dataFilter(Or17575Const.DEFAULT.STR.INIT)
  }
}

/**
 * 薬剤マスタ情報
 *
 * @param drugMasterInfoList - 薬剤マスタ情報リスト
 *
 * @param drugUnitMasterInfoList - 薬剤単位マスタ情報リスト
 */
function medicineMasterInit(
  drugMasterInfoList: DrugMasterInfo[],
  drugUnitMasterInfoList: DrugUnitMasterInfo[]
) {
  if (drugMasterInfoList) {
    // 薬剤マスタ情報
    const list: MedicationMasterModel[] = []
    for (const item of drugMasterInfoList) {
      if (item) {
        const rowData = {
          id: item.drugId,
          level: item.level,
          parentId: item.parentId,
          bunruiId: item.bunruiId,
          drugId: {
            value: Number(item.drugId),
            unit: Or17575Const.DEFAULT.STR.EMPTY,
          } as Mo01336OnewayType,
          useFlg: {
            modelValue: item.useFlg === Or17575Const.DEFAULT.STR.ONE,
          } as Mo00018Type,
          drugCode: {
            value: item.drugCode,
          } as Mo01274Type,
          drugIndex: {
            value: item.drugIndex,
          } as Mo01274Type,
          drugRezeCd: {
            value: item.drugRezeCd,
          } as Mo01274Type,
          drugKnj: {
            value: item.drugKnj,
          } as Mo00045Type,
          drugKana: {
            value: item.drugKana,
          } as Mo01274Type,
          oldtanka: {
            value: item.oldtanka,
          } as Mo01278Type,
          buyunitid: {
            modelValue: item.buyunitid,
          } as Mo01282Type,
          irisuu: {
            value: item.irisuu,
          } as Mo01278Type,
          bunryou: {
            value: item.bunryou,
          } as Mo01278Type,
          useunitid: {
            modelValue: item.useunitid,
          } as Mo01282Type,
          youhouKnj: {
            value: item.youhouKnj,
          } as Mo01274Type,
          touyonissuu: {
            value: item.touyonissuu,
          } as Mo01278Type,
          touyonissuuUnitid: {
            modelValue: item.touyonissuuUnitid,
          } as Mo01282Type,
          odd: {
            modelValue: item.odd,
          } as Mo01282Type,
          shape: {
            modelValue: item.shape,
          } as Mo01282Type,
          keiroKbn: {
            modelValue:
              Or17575Const.DEFAULT.STR.ONE === item.keiroKbn
                ? Or17575Const.DEFAULT.STR.ONE
                : Or17575Const.DEFAULT.STR.EMPTY,
          } as Mo01282Type,
          keiroKbnDisabled: item.shape !== Or17575Const.DEFAULT.STR.ONE,
          memoKnj: {
            value: item.memoKnj,
          } as Mo01274Type,
          sort: {
            value: String(item.sort),
          } as Mo01278Type,
          updateKbn: Or17575Const.DEFAULT.STR.EMPTY,
        } as MedicationMasterModel

        list.push(rowData)
      }
    }

    // 表示順でソートする
    list.sort((a, b) => Number(a.sort.value) - Number(b.sort.value))
    mo01354Type.value.values.items = cloneDeep(list)
    local.allDatas = cloneDeep(list)
    if (mo01354Type.value.values.items.length > 0) {
      mo01354Type.value.values.selectedRowId = mo01354Type.value.values.items[0].id
    } else {
      mo01354Type.value.values.selectedRowId = Or17575Const.DEFAULT.STR.EMPTY
    }

    // 薬剤単位マスタ情報
    const drugUnitMasterList: DrugUnitMasterInfoType[] = []
    for (const item of drugUnitMasterInfoList) {
      if (item) {
        drugUnitMasterList.push({
          unitId: item.unitId,
          unitCode: item.unitCode,
          unitnameKnj: item.unitnameKnj,
        } as DrugUnitMasterInfoType)
      }
    }
    unitItems.items = drugUnitMasterList

    // 行複写、行削除ボタンは非活性
    if (mo01354Type.value.values.items.length === Or17575Const.DEFAULT.NUMBER.ZERO) {
      // Or01544のダイアログ状態を更新する
      Or01544Logic.state.set({
        uniqueCpId: or01544.value.uniqueCpId,
        state: {
          addBtndisabled: false,
          copyBtndisabled: true,
          deleteBtndisabled: true,
        },
      })
    } else {
      // Or01544のダイアログ状態を更新する
      Or01544Logic.state.set({
        uniqueCpId: or01544.value.uniqueCpId,
        state: {
          addBtndisabled: false,
          copyBtndisabled: false,
          deleteBtndisabled: false,
        },
      })
    }
  } else {
    mo01354Type.value.values.items = []
    mo01354Type.value.values.selectedRowId = Or17575Const.DEFAULT.STR.EMPTY
  }
  if (refValue.value) {
    refValue.value.items = cloneDeep(mo01354Type.value.values.items)
    // APIから取得されたデータでRefValueを更新する
    useScreenStore().setCpTwoWay({
      cpId: Or17575Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: refValue.value,
      isInit: true,
    })
  }
}

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 用量単位
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DOSAGE_UNIT, addBlank: true },
    // 算定区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CALCULATION_CATEGORY, addBlank: true },
    // 剤型
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_AGENT_TYPE, addBlank: true },
    // 経路
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ROUTE, addBlank: true },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 用量単位
  dosageUnitItems.items = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_DOSAGE_UNIT)

  // 算定区分
  calculationCategoryItems.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CALCULATION_CATEGORY
  )

  // 剤型
  agentTypeItems.items = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_AGENT_TYPE)

  // 経路
  routeItems.items = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ROUTE)
}

/**
 * 行追加
 */
async function addRow() {
  sortMaxValue += Or17575Const.DEFAULT.NUMBER.ONE
  IdMaxValue += Or17575Const.DEFAULT.NUMBER.ONE
  mo01354Type.value.values.items.push({
    id: String(IdMaxValue),
    useFlg: {
      modelValue: false,
    } as Mo00018Type,
    drugId: {
      value: undefined,
      unit: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01336OnewayType,
    drugCode: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01274Type,
    drugIndex: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01274Type,
    drugRezeCd: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01274Type,
    drugKnj: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01274Type,
    drugKana: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01274Type,
    oldtanka: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01278Type,
    buyunitid: {
      modelValue: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01282Type,
    irisuu: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01278Type,
    bunryou: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01278Type,
    useunitid: {
      modelValue: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01282Type,
    youhouKnj: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01274Type,
    touyonissuu: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01278Type,
    touyonissuuUnitid: {
      modelValue: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01282Type,
    odd: {
      modelValue: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01282Type,
    shape: {
      modelValue: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01282Type,
    keiroKbn: {
      modelValue: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01282Type,
    keiroKbnDisabled: false,
    memoKnj: {
      value: Or17575Const.DEFAULT.STR.EMPTY,
    } as Mo01274Type,
    sort: {
      value: Or17575Const.DEFAULT.SORT,
    } as Mo01278Type,
    level: local.level,
    bunruiId: local.parentId,
    parentId: local.parentId,
    updateKbn: Or17575Const.DEFAULT.IS_CREATE,
  } as MedicationMasterModel)
  mo01354Type.value.values.selectedRowId = String(IdMaxValue)
  await nextTick()
  setFocus(String(IdMaxValue))
}

/**
 * フォーカス設定
 *
 * @param id - 行id
 */
const setFocus = (id: string) => {
  const inputElement = textRefs.get(id)!.querySelector(`#text-${id} input`)!
  const inputHtmlElement = inputElement as HTMLElement
  inputHtmlElement.focus()
}

const setItemRef = (el: Element | ComponentPublicInstance | null, id: string) => {
  const elHtml: HTMLElement = el as HTMLElement
  if (el) {
    textRefs.set(id, elHtml)
  }
}

/**
 * 行複写
 */
async function copyRow() {
  if (mo01354Type.value.values.selectedRowId) {
    const list = mo01354Type.value.values.items
    for (const item of list) {
      if (item) {
        if (mo01354Type.value.values.selectedRowId === item.id) {
          IdMaxValue += Or17575Const.DEFAULT.NUMBER.ONE
          mo01354Type.value.values.items.push({
            id: String(IdMaxValue),
            level: item.level as string,
            parentId: item.parentId as string,
            bunruiId: item.bunruiId as string,
            drugId: {
              value: undefined,
            } as Mo01336OnewayType,
            useFlg: item.useFlg as Mo00018Type,
            drugCode: item.drugCode as Mo01274Type,
            drugIndex: item.drugIndex as Mo01274Type,
            drugRezeCd: item.drugRezeCd as Mo01274Type,
            drugKnj: item.drugKnj as Mo00045Type,
            drugKana: item.drugKana as Mo01274Type,
            oldtanka: item.oldtanka as Mo01278Type,
            buyunitid: item.buyunitid as Mo01282Type,
            irisuu: item.irisuu as Mo01278Type,
            bunryou: item.bunryou as Mo01278Type,
            useunitid: item.useunitid as Mo01282Type,
            youhouKnj: item.youhouKnj as Mo01274Type,
            touyonissuu: item.touyonissuu as Mo01278Type,
            touyonissuuUnitid: item.touyonissuuUnitid as Mo01282Type,
            odd: item.odd as Mo01282Type,
            shape: item.shape as Mo01282Type,
            keiroKbn: item.keiroKbn as Mo01282Type,
            keiroKbnDisabled: item.keiroKbnDisabled as boolean,
            memoKnj: item.memoKnj as Mo01274Type,
            sort: item.sort as Mo01278Type,
            updateKbn: Or17575Const.DEFAULT.IS_CREATE,
          } as MedicationMasterModel)
          mo01354Type.value.values.selectedRowId = String(IdMaxValue)
          break
        }
      }
    }
  }
  await nextTick()
  setFocus(String(IdMaxValue))
}

/**
 * 行削除
 */
async function deleteRow() {
  // 確認ダイアログを開く
  const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, t('message.w-cmn-20803'))
  // はい
  if (dialogResult === Or17575Const.DEFAULT.STR.YES) {
    if (mo01354Type.value.values.selectedRowId) {
      const list: MedicationMasterModel[] = []
      let newSelectedRowId = Or17575Const.DEFAULT.STR.EMPTY
      for (let i = 0; i < mo01354Type.value.values.items.length; i++) {
        if (mo01354Type.value.values.selectedRowId === mo01354Type.value.values.items[i]?.id) {
          deleteIdList.push(String(mo01354Type.value.values.items[i].id))
          if (i === 0) {
            if (mo01354Type.value.values.items.length > 1) {
              newSelectedRowId = mo01354Type.value.values.items[i + 1].id
            }
          } else if (i > 1) {
            if (i < mo01354Type.value.values.items.length - 1) {
              newSelectedRowId = mo01354Type.value.values.items[i + 1].id
            } else {
              newSelectedRowId = mo01354Type.value.values.items[i - 1].id
            }
          }
        } else {
          list.push({
            id: mo01354Type.value.values.items[i].id,
            drugId: mo01354Type.value.values.items[i].drugId as Mo01336OnewayType,
            drugCode: mo01354Type.value.values.items[i].drugCode as Mo01274Type,
            drugIndex: mo01354Type.value.values.items[i].drugIndex as Mo01274Type,
            drugRezeCd: mo01354Type.value.values.items[i].drugRezeCd as Mo01274Type,
            drugKnj: mo01354Type.value.values.items[i].drugKnj as Mo00045Type,
            drugKana: mo01354Type.value.values.items[i].drugKana as Mo01274Type,
            oldtanka: mo01354Type.value.values.items[i].oldtanka as Mo01278Type,
            buyunitid: mo01354Type.value.values.items[i].buyunitid as Mo01282Type,
            irisuu: mo01354Type.value.values.items[i].irisuu as Mo01278Type,
            bunryou: mo01354Type.value.values.items[i].bunryou as Mo01278Type,
            useunitid: mo01354Type.value.values.items[i].useunitid as Mo01282Type,
            youhouKnj: mo01354Type.value.values.items[i].youhouKnj as Mo01274Type,
            touyonissuu: mo01354Type.value.values.items[i].touyonissuu as Mo01278Type,
            touyonissuuUnitid: mo01354Type.value.values.items[i].touyonissuuUnitid as Mo01282Type,
            odd: mo01354Type.value.values.items[i].odd as Mo01282Type,
            shape: mo01354Type.value.values.items[i].shape as Mo01282Type,
            keiroKbn: mo01354Type.value.values.items[i].keiroKbn as Mo01282Type,
            keiroKbnDisabled:
              (mo01354Type.value.values.items[i].shape as Mo01282Type).modelValue !==
              Or17575Const.DEFAULT.STR.ONE,
            memoKnj: mo01354Type.value.values.items[i].memoKnj as Mo01274Type,
            sort: mo01354Type.value.values.items[i].sort as Mo01278Type,
            parentId: mo01354Type.value.values.items[i].parentId as string,
            updateKbn: mo01354Type.value.values.items[i].updateKbn as string,
            useFlg: mo01354Type.value.values.items[i].useFlg as Mo00018Type,
            level: mo01354Type.value.values.items[i].level as string,
            bunruiId: mo01354Type.value.values.items[i].bunruiId as string,
          } as MedicationMasterModel)
        }
      }
      mo01354Type.value.values.selectedRowId = newSelectedRowId
      mo01354Type.value.values.items = list
    }
  }
  // いいえ
  else if (dialogResult === Or17575Const.DEFAULT.STR.NO) {
    // 処理終了
    return
  }
}

/**
 * 剤型が値変更
 *
 * @param item - 戻り値
 */
const shapSelectChange = (item: MedicationMasterModel) => {
  // 剤型が「内服薬」の場合
  if (Or17575Const.DEFAULT.STR.ONE === item.shape.modelValue) {
    // 活性、初期値が「経口」
    item.keiroKbn.modelValue = Or17575Const.DEFAULT.STR.ONE
    item.keiroKbnDisabled = false
  }
  // 上記以外の場合
  else {
    // 非活性、初期値が空
    item.keiroKbn.modelValue = Or17575Const.DEFAULT.STR.EMPTY
    item.keiroKbnDisabled = true
  }
}

/**
 * 行選択
 */
watch(
  () => mo01354Type.value.values.selectedRowId,
  () => {
    updateResultDate()
  }
)

/**
 * 行のアクションボタン
 */
watch(
  () => or01544Type,
  (newValue) => {
    if (Or17575Const.DEFAULT.STR.ADD === newValue.value) {
      void addRow()
    } else if (Or17575Const.DEFAULT.STR.COPY === newValue.value) {
      void copyRow()
    } else if (Or17575Const.DEFAULT.STR.DELETE === newValue.value) {
      void deleteRow()
    }
    or01544Type.value = Or17575Const.DEFAULT.STR.EMPTY
  },
  { deep: true }
)

/**
 * 確認ダイアログ表示
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no
 */
const openConfirmDialog = async (
  uniqueCpId: string,
  paramDialogText: string
): Promise<Or17575MsgBtnType> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = Or17575Const.DEFAULT.STR.NO as Or17575MsgBtnType

        if (event?.firstBtnClickFlg) {
          result = Or17575Const.DEFAULT.STR.YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or17575Const.DEFAULT.STR.NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or17575Const.DEFAULT.STR.CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or17575Const.DEFAULT.STR.CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <v-overlay
      :model-value="isLoading"
      :persistent="true"
      class="align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      >
      </v-progress-circular>
  </v-overlay>
  <c-v-sheet>
    <c-v-row no-gutters>
      <g-custom-or-01544
        v-bind="or01544"
        v-model="or01544Type"
        style="float: left"
      ></g-custom-or-01544>
    </c-v-row>
    <c-v-row
      no-gutters
      style="margin-top: 8px"
    >
      <c-v-col
        cols="12"
        sm="12"
        class="table-header"
      >
        <base-mo-01354
          v-model="mo01354Type"
          class="list-wrapper"
          :oneway-model-value="mo01354Oneway"
        >
          <!-- (薬剤マスタ)ID -->
          <template #[`item.drugId`]="{ item }">
            <base-mo01336
              :oneway-model-value="item.drugId"
              class="or17575-m-16"
            ></base-mo01336>
          </template>
          <!-- 中止(適用フラグ) -->
          <template #[`item.useFlg`]="{ item }">
            <base-mo00018
              v-model="item.useFlg"
              :oneway-model-value="mo00018OnewayType"
              style="height: 31px; align-content: center"
            ></base-mo00018>
          </template>
          <!-- 医薬品コード -->
          <template #[`item.drugCode`]="{ item }">
            <base-mo01274
              v-model="item.drugCode"
              style="width: 150px"
            ></base-mo01274>
          </template>
          <!-- 施設内コード -->
          <template #[`item.drugIndex`]="{ item }">
            <base-mo01274
              v-model="item.drugIndex"
              style="width: 150px"
            ></base-mo01274>
          </template>
          <!-- レセ電算コード -->
          <template #[`item.drugRezeCd`]="{ item }">
            <base-mo01274
              v-model="item.drugRezeCd"
              style="width: 160px"
            ></base-mo01274>
          </template>
          <!-- 薬剤名 -->
          <template #[`item.drugKnj`]="{ item }">
            <div
              :ref="(el) => setItemRef(el, item.id)"
              class="h-100"
            >
              <base-mo00045
                :id="`text-${item.id}`"
                v-model="item.drugKnj"
                :oneway-model-value="mo00045Oneway"
                style="background: transparent; width: 248px"
              />
            </div>
          </template>
          <!-- 薬剤カナ -->
          <template #[`item.drugKana`]="{ item }">
            <div class="h-100">
              <base-mo00045
                v-model="item.drugKana"
                :oneway-model-value="mo00045Oneway"
                style="width: 248px"
              ></base-mo00045>
            </div>
          </template>
          <!-- 購入価格 -->
          <template #[`item.oldtanka`]="{ item }">
            <base-mo01278
              v-model="item.oldtanka"
              style="width: 120px"
            ></base-mo01278>
          </template>
          <!-- 購入単位 -->
          <template #[`item.buyunitid`]="{ item }">
            <base-mo01282
              v-model="item.buyunitid"
              :oneway-model-value="unitItems"
              style="width: 120px"
            ></base-mo01282>
          </template>
          <!-- 入数 -->
          <template #[`item.irisuu`]="{ item }">
            <base-mo01278
              v-model="item.irisuu"
              style="width: 92px"
            ></base-mo01278>
          </template>
          <!-- 分量 -->
          <template #[`item.bunryou`]="{ item }">
            <base-mo01278
              v-model="item.bunryou"
              style="text-align: right; width: 92px"
            ></base-mo01278>
          </template>
          <!-- 使用単位 -->
          <template #[`item.useunitid`]="{ item }">
            <base-mo01282
              v-model="item.useunitid"
              :oneway-model-value="unitItems"
              style="width: 120px"
            ></base-mo01282>
          </template>
          <!-- 用法 -->
          <template #[`item.youhouKnj`]="{ item }">
            <base-mo01274
              v-model="item.youhouKnj"
              style="width: 192px"
            ></base-mo01274>
          </template>
          <!-- 用量 -->
          <template #[`item.touyonissuu`]="{ item }">
            <base-mo01278
              v-model="item.touyonissuu"
              style="text-align: right; width: 92px"
            ></base-mo01278>
          </template>
          <!-- 用量単位 -->
          <template #[`item.touyonissuuUnitid`]="{ item }">
            <base-mo01282
              v-model="item.touyonissuuUnitid"
              :oneway-model-value="dosageUnitItems"
              style="width: 120px"
            ></base-mo01282>
          </template>
          <!-- 算定区分 -->
          <template #[`item.odd`]="{ item }">
            <base-mo01282
              v-model="item.odd"
              :oneway-model-value="calculationCategoryItems"
              style="width: 120px"
            ></base-mo01282>
          </template>
          <!-- 剤型 -->
          <template #[`item.shape`]="{ item }">
            <base-mo01282
              v-model="item.shape"
              :oneway-model-value="agentTypeItems"
              style="width: 120px"
              @change="shapSelectChange(item)"
            ></base-mo01282>
          </template>
          <!-- 経路 -->
          <template #[`item.keiroKbn`]="{ item }">
            <base-mo01282
              v-model="item.keiroKbn"
              :oneway-model-value="routeItems"
              :disabled="item.keiroKbnDisabled"
              style="width: 120px"
            ></base-mo01282>
          </template>
          <!-- 作用・効能等 -->
          <template #[`item.memoKnj`]="{ item }">
            <base-mo01274
              v-model="item.memoKnj"
              style="width: 150px"
            ></base-mo01274>
          </template>
          <!-- 表示順 -->
          <template #[`item.sort`]="{ item }">
            <base-mo01278
              v-model="item.sort"
              :oneway-model-value="mo01278OnewayType"
              style="width: 100px"
            ></base-mo01278>
          </template>
          <!-- ページングを非表示 -->
          <template #bottom />
        </base-mo-01354>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

:deep(.full-width-field) {
  padding: 0 16px !important;
}

:deep(.v-selection-control__wrapper) {
  margin: 0 auto;
}

.or17575-m-16 {
  width: 80px;
  padding-left: 16px;
  padding-right: 16px;
}

.v-checkbox :deep(.v-checkbox-btn) {
  min-height: 31px !important;
  height: 31px !important;
}

:deep(.v-selection-control__wrapper) {
  height: 31px !important;
  max-height: 31px;
  width: 31px !important;
  max-width: 31px;
}

:deep(.table-cell) {
  max-height: 29px;
  height: 29px !important;
}

:deep(.label-area-style) {
  display: none;
}

.useFlg {
  justify-items: center;
}

:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 20px !important;
}

:deep(.v-input--density-default) {
  height: 20px !important;
}

:deep(.v-checkbox-btn) {
  min-height: 20px !important;
  height: 20px !important;
}

:deep(.v-field) {
  border: 2px !important;
}

:deep(.v-field__input){
  color: black;
}

:deep(.v-select__selection span) {
    color: black;
}
</style>
