/**
 * Or35041:有機体:印刷設定モーダル
 * GUI01215_［印刷設定］画面
 *
 * <AUTHOR> 張凱旋
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 *レスポンス構造
 */
export interface LedgerInitializeDataComSelectInEntity extends InWebEntity {
  /**
   * システムコード
   */
  sysCd: string
  /**
   * 機能名
   */
  kinounameKnj: string
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * セクション
   */
  sectionKnj: string
  /**
   * 個人情報設定フラグ
   */
  kojinhogoFlg?: string
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: string
  /**
   * 個人情報表示値
   */
  sectionAddNo: string
}

/**
 * リクエスト構造
 */
export interface LedgerInitializeDataComSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * 帳票INIデータリスト
     */
    iniDataObject: iniDataObject[]
  }
}

/**
 * 帳票INIデータリスト
 */
export interface iniDataObject {
  /**
   * 氏名伏字印刷
   */
  prtName: string
  /**
   * 文書番号印刷
   */
  prtBng: string
  /**
   * 個人情報印刷
   */
  prtKojin: string
}
