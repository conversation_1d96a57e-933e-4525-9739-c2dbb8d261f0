<script setup lang="ts">
/**
 * Or32382:週間表イメージ
 *
 * @description
 * 週間表イメージ
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or60794Const } from '../Or60794/Or60794.constants'
import type { OrX0050CalendarConfig, OrX0050CalendarEvent } from '../OrX0050/OrX0050.type'
import { Or26714Logic } from '../Or26714/Or26714.logic'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { Or26972Const } from '../Or26972/Or26972.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or27730Const } from '../Or27730/Or27730.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or32382StateType, WeeklyTableInput } from './Or32382.type'
import type {
  additionInfo,
  ServiceFeeOfficialName,
  ServiceType,
  ServiceProvider,
  WeeklyPlanInput,
  WeeklyScheduleInput,
} from '~/components/custom-components/organisms/Or26714/Or26714.type'
import { useColorUtils, useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or32382OnewayType, Or32382Type } from '~/types/cmn/business/components/Or32382Type'
import type { Or33097OnewayType } from '~/types/cmn/business/components/Or33097Type'
import type { Or29241OnewayType } from '~/types/cmn/business/components/Or29241Type'
import type { Or31535OnewayType } from '~/types/cmn/business/components/Or31535Type'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import type { OrCpGroupDefinitionInputFormDeleteDialogType } from '~/types/business/generator-components/OrCpGroupDefinitionInputFormDeleteDialog'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { OrX0018OnewayType } from '~/types/cmn/business/components/OrX0018Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { Or26714Const } from '~/components/custom-components/organisms/Or26714/Or26714.constants'
import { OrX0050Const } from '~/components/custom-components/organisms/OrX0050/OrX0050.constants'
import { Or21796Const } from '~/components/base-components/organisms/Or21796/Or21796.constants'
import { Or21797Const } from '~/components/base-components/organisms/Or21797/Or21797.constants'
import { Or21798Const } from '~/components/base-components/organisms/Or21798/Or21798.constants'
import { OrX0006Const } from '~/components/custom-components/organisms/OrX0006/OrX0006.constants'
import type {
  Or26972OnewayType,
  Or26972SelectInfoType,
} from '~/types/cmn/business/components/Or26972Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or32382Const } from '~/components/custom-components/organisms/Or32382/Or32382.constants'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { OrX0163Type, OrX0163OnewayType } from '~/types/cmn/business/components/OrX0163Type'
import { CustomClass } from '~/types/CustomClassType'
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or32382OnewayType
  uniqueCpId: string
  modelValue: Or32382Type
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// 選択中利用者ID
const userId = ref<string>('')

// 履歴表示フラグ
const isHistoryShow = ref<boolean>(true)
// 削除フラグ
const deleteFlag = ref<boolean>(false)
// 保険編集フラグ
const hokenEditFlg = ref<boolean>(false)
const { convertDecimalToHex } = useColorUtils()
// カレンダーの設定
const calendarConfig = ref<OrX0050CalendarConfig>({
  events: [],
  cellHeight: 36,
  zoom: 1,
  granularity: 1,
  readonly: false,
})

const Or26714Data = {
  // 新規モード
  newMode: false,

  weekDay: '',

  startTime: '',

  endTime: '',

  dataList: [] as WeeklyScheduleInput[],
}

const calendar = ref<{
  reRenderCalendar: () => Promise<void>
  getCalendarData: () => OrX0050CalendarEvent[] | []
}>()
const orX0050 = ref({ uniqueCpId: OrX0050Const.CP_ID(0) })
const or21796 = ref({ uniqueCpId: Or21796Const.CP_ID(0) })
const or21797 = ref({ uniqueCpId: Or21797Const.CP_ID(0) })
const or21798 = ref({ uniqueCpId: Or21798Const.CP_ID(0) })
const or26714 = ref({ uniqueCpId: Or26714Const.CP_ID(0) })

const orX0006 = ref({ uniqueCpId: '' })
const orX0009 = ref({ uniqueCpId: '' })
const or26972 = ref({ uniqueCpId: Or26972Const.CP_ID(1) })
const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const orHeadLine = ref({ uniqueCpId: '' })
const or27730 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21814_2 = ref({ uniqueCpId: '' })
const or21814_3 = ref({ uniqueCpId: '' })
const or21814_4 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
const or60794 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })

const commonInfoData = reactive({
  //共通情報.基準日
  commonDate: '2025/02/26',
  //共通情報.事業所ID
  officeId: '1',
  // 共通情報.事業所CD
  officeCd: '50010',
  // 共通情報.自事業所
  jiOffice: '1',
  //共通情報.利用者ID
  userId: '0000000001',
  //共通情報.サービス事業者ID
  jigyoId: 'W00001',
  //共通情報.施設ID
  shisetsuId: '1',
  //共通情報.種別ID
  shubetsuId: '1',
  //共通情報.法人ID
  houjinId: 'H00001',
  // 共通情報.取込元
  toriUnit: '',
  // 共通情報.計画書様式
  keikakuStyle: '2',
  // 職員ID
  syokuinId: '',
  //事業者グループ適用ID
  officeGroupId: 'G0002',
  //共通情報.ログイン情報.職員名
  loginUserName: 'LOGIN 職員名',
  loginUserId: '00001',
  systemCode: '9898',
  svJigyoId: '1',
  // サービス職種IDor種類表示フラグ
  shuruihyoujiFlg: true,
  // 事業者表示フラグ
  jigyoShowFlg: 0,
  // サービス項目表示フラグ
  komokuShowFlg: true,
  // 内容表示フラグ
  contentShowFlg: true,
  eFileSaveKbn: 1,
  showMessageFlg: 1,
  serviceType: '50010',
})

// ローカルTwoway
const local = reactive({
  or32382: {
    ...props.modelValue,
  } as Or32382Type,
  // 新規ボタン押下回数
  addBtnClickCount: 0,
  // 事業所ID
  officeId: '1',
  component: {
    yukokikanId: 0,
    planTargetPeriodId: 0,
    periodCurrentIndex: 0,
    periodTotalCount: 0,
    rirekiId: 0,
    rirekiCurrentIndex: 0,
    rirekiTotalCount: 0,
  } as Or32382StateType,
  // 作成日
  createDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // 処理月
  syoriMonth: {
    value: '',
  } as Mo00020Type,
  weeklyModel: {
    value: '',
  } as OrX0163Type,
  hokenModel: {
    value: '',
  } as Mo00046Type,
  // 確定メッセージ
  confirmDialog1: {
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
    emitType: 'clickYes',
  } as OrCpGroupDefinitionInputFormDeleteDialogType,
  or26714: {
    dataList: [] as WeeklyPlanInput[],
    dataService: {
      serviceType: [] as ServiceType[],
      servicepProviderName: [] as ServiceProvider[],
      serviceFeeOfficialName: [] as ServiceFeeOfficialName[],
    },
    dataAddition: [] as additionInfo[],
  },
})

// ローカルOneway
const localOneway = reactive({
  or32382: {
    ...props.onewayModelValue,
  },
  or33097Oneway: {
    assessmentHome: {
      title: '',
    },
  } as Or33097OnewayType,
  or29241Oneway: {
    assessmentHome: {
      title: '',
    },
  } as Or29241OnewayType,
  or31535Oneway: {
    assessmentHome: {
      title: '',
    },
  } as Or31535OnewayType,
  // タイトル: 表用テキストフィールド
  orX0018TitleInputOneWay: {
    maxLength: '20',
    rules: [],
  } as OrX0018OnewayType,
  mo00046onewayWeekly: {
    maxLength: 360,
    rows: 3,
    maxRows: '3',
    isVerticalLabel: false,
    showItemLabel: false,
    autoGrow: false,
    noResize: true,
  } as Mo00046Type,
  OrX0163OnewayHoken: {
    readonly: true,
    showItemLabel: false,
    showEditBtnFlg: false,
  } as OrX0163OnewayType,
  OrX0163OnewayWeekly: {
    showEditBtnFlg: true,
    height: '864px',
  } as OrX0163OnewayType,
  mo00009Oneway: {
    btnIcon: 'open_in_new',
    name: 'officeSelectIconBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  or26972Oneway: {
    historySelectInfo: {} as Or26972SelectInfoType,
  } as Or26972OnewayType,
  // GUI00937 共通入力支援画面
  or51775OnewayModel: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
  // その他のサービス
  mo00615OtherServiceOnewayType: {
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabel: t('label.other-service'),
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent;',
    }),
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:14px;',
    }),
  } as Mo00615OnewayType,
  // (保険ｻｰﾋﾞｽ)
  mo00615HokenServiceOnewayType: {
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabel: t('label.week-table-hoken-sevice'),
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent;',
    }),
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:14px;',
      labelClass: 'font-color-blue',
    }),
  } as Mo00615OnewayType,
  // その他のサービス 選択ボタン
  otherServiceMo00009Btn: {
    btnIcon: 'edit_square',
    name: 'otherServiceMo00009Btn',
    density: 'compact',
  } as Mo00009OnewayType,
  // 保険ｻｰﾋﾞｽ 選択ボタン
  hokenMo00009Btn: {
    btnIcon: 'edit_square',
    name: 'hokenMo00009Btn',
    density: 'compact',
    color: 'blue-900',
  } as Mo00009OnewayType,
})

/**
 * 時間帯データ
 */
const wp = {
  id: '',
  startHour: { value: Or32382Const.DEFAULT.TIME_ZERO },
  endHour: { value: Or32382Const.DEFAULT.TIME_ZERO },
  dayOfWeek1: false,
  dayOfWeek2: false,
  dayOfWeek3: false,
  dayOfWeek4: false,
  dayOfWeek5: false,
  dayOfWeek6: false,
  dayOfWeek7: false,
  dayOfWeekOther: false,
  content: '',
  memo: '',
  frequency: '',
  letterSize: '',
  charPosition: '',
  omitted: '',
  letterColor: '',
  backgroundColor: '',
  timeDisplay: '',
  igaiKbn: '',
  igaiDate: '',
  igaiWeek: '',
  svTani: '',
  fygId: '',
  wakugaiFlg: '',
  parentId: '',
  updateKbn:'',
} as WeeklyTableInput
/**************************************************
 * ライフサイクルフック
 **************************************************/
// onMounted(() => {
//   // 情報を取得
//   getInitDataInfo()
// })

// 保険のサービス
const hokenService = ref<string>('')
const or51775Other = ref({ modelValue: '' })
/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or32382StateType>({
  cpId: String(Or32382Const.CP_ID),
  uniqueCpId: props.uniqueCpId,
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [String(Or00248Const.CP_ID)]: or00248.value,
  [OrX0050Const.CP_ID(0)]: orX0050.value,
  [Or21796Const.CP_ID(0)]: or21796.value,
  [Or21797Const.CP_ID(0)]: or21797.value,
  [Or21798Const.CP_ID(0)]: or21798.value,
  [Or26714Const.CP_ID(0)]: or26714.value,
  [OrX0006Const.CP_ID(1)]: orX0006.value,
  [OrX0009Const.CP_ID(1)]: orX0009.value,
  [Or26972Const.CP_ID(1)]: or26972.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or27730Const.CP_ID(0)]: or27730.value,
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or21814Const.CP_ID(3)]: or21814_3.value,
  [Or21814Const.CP_ID(4)]: or21814_4.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
  [Or60794Const.CP_ID(0)]: or60794.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [String(Or00249Const.CP_ID)]: or00249.value,
})
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
watch(
  () => local.or32382,
  () => {
    emit('update:modelValue', local.or32382)
  },
  { deep: true, immediate: true }
)
/**
 *  その他のサービスボタンクリック
 */
const otherServiceBtnClick = () => {
  // GUI00937 共通入力支援画面（その他のサービス）をポップアップで起動する。
  const param = {
    // タイトル
    title: t('label.other-service'),
    // 画面ID
    screenId: '',
    // 分類ID
    bunruiId: '',
    // 大分類CD
    t1Cd: Or32382Const.DEFAULT.T1CD,
    // 中分類CD
    t2Cd: Or32382Const.DEFAULT.T2CD,
    // 小分類CD
    t3Cd: Or32382Const.DEFAULT.T3CD,
    // テーブル名
    tableName: Or32382Const.DEFAULT.TABLENAME,
    // カラム名
    columnName: Or32382Const.DEFAULT.COLUMNNAME,
    // アセスメント方式
    assessmentMethod: '',
    // 文章内容
    inputContents: local.weeklyModel.value,
    // 利用者ID
    userId: commonInfoData.userId,
    // モード
    mode: '',
  } as Or51775OnewayType

  localOneway.or51775OnewayModel = param
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  保険サービスボタンクリック
 */
const hokenServiceBtnClick = () => {
  Or26714Logic.state.set({
    uniqueCpId: or26714.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
// 週間表入力画面ダイアログ表示フラグ
const showDialogOr26714 = computed(() => {
  // Or26714のダイアログ開閉状態
  return Or26714Logic.state.get(or26714.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * ウォッチャー
 **************************************************
/**
 * カレンダーをダブルクリック
 */
function handleDoubleClick() {
  Or26714Logic.state.set({
    uniqueCpId: or26714.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 計算週
 *
 * @param event - スケジュール
 */
function calcWeek(event: WeeklyTableInput) {
  const arr: number[] = []
  if (event.dayOfWeek1) {
    arr.push(1)
  }
  if (event.dayOfWeek2) {
    arr.push(2)
  }
  if (event.dayOfWeek3) {
    arr.push(3)
  }
  if (event.dayOfWeek4) {
    arr.push(4)
  }
  if (event.dayOfWeek5) {
    arr.push(5)
  }
  if (event.dayOfWeek6) {
    arr.push(6)
  }
  if (event.dayOfWeek7) {
    arr.push(7)
  }
  if (arr.length === 0) return []

  const result = []
  let current = { start: arr[0], count: 1 }

  for (let i = 1; i < arr.length; i++) {
    if (arr[i] === arr[i - 1] + 1) {
      current.count++
    } else {
      result.push([current.start, current.count])
      current = { start: arr[i], count: 1 }
    }
  }

  result.push([current.start, current.count])
  return result
}
/**************************************************
 * 関数
 **************************************************/
/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or32382Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or32382Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return data.value
    }
  }
  local.weeklyModel.value = setOrAppendValue(local.weeklyModel.value ?? '', data)
  localOneway.or51775OnewayModel.title = ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}
/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 */
const handleOr26714Confirm = () => {
  for (const item of local.or26714.dataList) {
    wp.parentId = item.id
    wp.letterSize = item.letterSize
    wp.charPosition = item.charPosition
    wp.letterColor = item.letterColor
    wp.backgroundColor = item.backgroundColor
    wp.startHour.value = item.startHour.value
    wp.endHour.value = item.endHour.value
    wp.content = item.content
    wp.memo = item.memo
    wp.dayOfWeek1 = item.dayOfWeek1
    wp.dayOfWeek2 = item.dayOfWeek2
    wp.dayOfWeek3 = item.dayOfWeek3
    wp.dayOfWeek4 = item.dayOfWeek4
    wp.dayOfWeek5 = item.dayOfWeek5
    wp.dayOfWeek6 = item.dayOfWeek6
    wp.dayOfWeek7 = item.dayOfWeek7
    wp.timeDisplay = item.timeDisplay
    wp.updateKbn = Or32382Const.DEFAULT.UPDATE_KBN_C
    const wp1 = cloneDeep(wp)
    local.or32382.weekData.push(wp1)
  }
  setCalendar(local.or32382.weekData)
  Or26714Logic.state.set({
    uniqueCpId: or26714.value.uniqueCpId,
    state: { isOpen: false },
  })
}
/**
 *  画面初期情報取得
 */
function getInitDataInfo() {
  if (
    props.onewayModelValue.tableItem !== undefined &&
    props.onewayModelValue.tableItem.length > 0
  ) {
    for (const item of props.onewayModelValue.tableItem) {
      // 変数.詳細リスト.曜日が"99999999"（週単位以外のサービス）以外の場合
      if (
        item.youbi?.value !== Or32382Const.DEFAULT.WEEK_UNIT_OTHER_SERVICE
      ) {
        wp.parentId = item.week2Id ?? '0'
        wp.letterSize = item.fontSize?.value ?? '0'
        wp.charPosition = item.alignment?.value ?? '0'
        wp.letterColor = convertDecimalToHex(Number(item.fontColor?.value))
        wp.backgroundColor = convertDecimalToHex(Number(item.backColor?.value))
        wp.startHour.value = item.kaishiJikan?.value
        wp.endHour.value = item.shuuryouJikan?.value
        wp.content = item.naiyoKnj?.value ?? ''
        wp.memo = item.memoKnj?.value ?? ''
        let count = 1
        wp.dayOfWeek1 = false
        wp.dayOfWeek2 = false
        wp.dayOfWeek3 = false
        wp.dayOfWeek4 = false
        wp.dayOfWeek5 = false
        wp.dayOfWeek6 = false
        wp.dayOfWeek7 = false
        if (item.youbi?.value !== undefined) {
          for (const str of item.youbi.value.split('')) {
            if (count === 1 && str === Or32382Const.DEFAULT.YOUBI_SEL) {
              wp.dayOfWeek1 = true
            }
            if (count === 2 && str === Or32382Const.DEFAULT.YOUBI_SEL) {
              wp.dayOfWeek2 = true
            }
            if (count === 3 && str === Or32382Const.DEFAULT.YOUBI_SEL) {
              wp.dayOfWeek3 = true
            }
            if (count === 4 && str === Or32382Const.DEFAULT.YOUBI_SEL) {
              wp.dayOfWeek4 = true
            }
            if (count === 5 && str === Or32382Const.DEFAULT.YOUBI_SEL) {
              wp.dayOfWeek5 = true
            }
            if (count === 6 && str === Or32382Const.DEFAULT.YOUBI_SEL) {
              wp.dayOfWeek6 = true
            }
            if (count === 7 && str === Or32382Const.DEFAULT.YOUBI_SEL) {
              wp.dayOfWeek7 = true
            }
            count = count + 1
            if (count === item.youbi?.value.length) {
              break
            }
          }
        }   
        // wp.updateKbn = Or32382Const.DEFAULT.UPDATE_KBN_U
        const wp1 = cloneDeep(wp)
        local.or32382.weekData.push(wp1)
      }
      // 変数.詳細リスト.曜日が"99999999"（週単位以外のサービス）の場合
      else {
        // 共通情報. 内容表示フラグが表示の場合
        if (commonInfoData.contentShowFlg) {
          // 保険のサービステキストエリアが空白、且つ、週間表詳細リスト.サービス項目名称が空白ではない、且つ、週間表詳細リスト.時間表示区分が[2]の場合
          if (
            local.hokenModel.value === '' &&
            item.svItemKnj?.value !== '' &&
            item.timeKbn?.value === Or32382Const.DEFAULT.TIMD_DISP_KBN_LAST
          ) {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間表詳細リスト.内容+ ”(”
            hokenService.value = hokenService.value + item.naiyoKnj?.value + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間表詳細リスト.内容
            hokenService.value = hokenService.value + item.naiyoKnj?.value
          }
        }
        // 週間表詳細リスト.時間表示区分が先頭表示の場合
        if (item.timeKbn?.value === Or32382Const.DEFAULT.TIMD_DISP_KBN_FIRST) {
          // ・変数.保険のサービス＝週間表詳細リスト.開始時間+" ～ " + 週間表詳細リスト.終了時間 + 変数.保険のサービス
          hokenService.value =
            item.kaishiJikan?.value +
            t('label.wavy') +
            item.shuuryouJikan?.value +
            hokenService.value
        }
        // 週間表詳細リスト.時間表示区分が最後表示の場合
        if (item.timeKbn?.value === Or32382Const.DEFAULT.TIMD_DISP_KBN_LAST) {
          // ・変数.保険のサービス＝変数.保険のサービス + 週間表詳細リスト.開始時間+" ～ " + 週間表詳細リスト.終了時間
          hokenService.value =
            hokenService.value +
            item.kaishiJikan?.value +
            t('label.wavy') +
            item.shuuryouJikan?.value
        }
        // 変数.保険編集フラグがTRUEの場合
        if (hokenEditFlg.value) {
          // ・変数.保険のサービス＝変数.保険のサービス+")、"
          hokenService.value = hokenService.value + t('label.right-bracket') + t('label.comma')
          // ・変数.保険編集フラグをFALSEにセットする。
          hokenEditFlg.value = false
        } else {
          // ・変数.保険のサービス＝変数.保険のサービス+"、"
          hokenService.value = hokenService.value + t('label.comma')
        }
      }
    }
  }
  // 時間帯表示の場合
  setCalendar(local.or32382.weekData)
  if (hokenService.value !== '') {
    // ・変数.保険のサービス最後の"、"を除く
    // ・保険のサービステキストエリア＝変数.保険のサービス
    local.hokenModel.value = hokenService.value.substring(0, hokenService.value.length - 1)
    // ・変数.保険のサービス＝空白
    hokenService.value = ''
  } else {
    local.hokenModel.value = hokenService.value
  }
  local.weeklyModel.value = props.modelValue.wIgaiKnj
}
/**
 * 時間帯内容設定
 *
 * @param dataList - 週間表詳細リスト
 */
function setCalendar(dataList: WeeklyTableInput[]) {
  if (dataList) {
    const eventList = []
    let index = 0
    for (const e of dataList) {
      // 計算週
      const weekArray: number[][] = calcWeek(e)
      for (const week of weekArray) {
        const event: OrX0050CalendarEvent = {
          // 日程ブロックID
          id: index++,
          // 親ID
          parentId: Number(e.parentId),
          // 日程開始時間
          start: e.startHour.value ?? '',
          // 日程終了時間
          end: e.endHour.value ?? '',
          // 日程ブロック背景色
          bgColor: e.backgroundColor,
          // フォント色
          fontColor: e.letterColor,
          // フォントサイズ
          fontSize: `${Math.abs(Number(e.letterSize))}px`,
          // 日程内容エリア揃い方向
          align:
            e.charPosition === '2'
              ? Or32382Const.DEFAULT.RIGHT
              : e.charPosition === '0'
                ? Or32382Const.DEFAULT.LEFT
                : Or32382Const.DEFAULT.CENTER,
          // タイトル
          title: String(e.content),
          /** メモ */
          memo: String(e.memo ?? ''),
          // 曜日
          week: week[0],
          // 間隔日数
          period: week[1] > 1 ? week[1] : undefined,
        }
        eventList.push(event)
      }
    }
    calendarConfig.value.events = eventList

    calendarConfig.value.readonly = props.onewayModelValue.cpyFlg ?? false
    localOneway.OrX0163OnewayWeekly.readonly = props.onewayModelValue.cpyFlg ?? false

    // カレンダーの画面を反映
    void calendar.value?.reRenderCalendar()
  }
}
function rgbToHexDirect(rgb: number) {
  const r = (rgb >> 16) & 0xff
  const g = (rgb >> 8) & 0xff
  const b = rgb & 0xff
  return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}
function handleEventsUpdate(updatedEvents: OrX0050CalendarEvent[]) {
  calendarConfig.value.events = updatedEvents
}
watch(
  () => props.onewayModelValue,
  () => {
    local.or32382.weekData = []
    hokenService.value = ''
    getInitDataInfo()
  },
  { deep: true }
)
watch(
  () => local.weeklyModel,
  () => {
    local.or32382.wIgaiKnj = local.weeklyModel.value ?? ''
  },
  { deep: true }
)
</script>
<template>
  <c-v-sheet class="view d-flex flex-column">
    <!-- 操作ボタンエリア -->
    <c-v-row
      no-gutters
      class="main-Content d-flex flex-1-1 h-100"
    >
      <!-- コンテンツエリア -->
      <c-v-col class="main-right d-flex flex-column overflow-x-auto h-100">
        <!-- 入力フォーム -->
        <c-v-row
          v-show="isHistoryShow && !deleteFlag"
          no-gutters
        >
          <div class="zoomed readonly-div">
            <c-v-row
              v-if="!props.onewayModelValue.delFlg"
              no-gutters
              class="flex-1-3"
            >
              <c-v-row class="officeSelect flex-nowrap">
                <div>
                  <div class="calendar-container">
                    <g-custom-or-x-0050
                      ref="calendar"
                      :calendar-config="calendarConfig"
                      v-bind="orX0050"
                      @double-click="handleDoubleClick"
                      @update:events="handleEventsUpdate"
                    ></g-custom-or-x-0050>
                  </div>
                </div>
                <div class="d-flex">
                  <div style="width: 206px">
                    <div>
                      <div class="grid-header header-container justify-center align-center">
                        <c-v-row class="officeSelect">
                          <c-v-col>
                            <base-mo00615
                              :oneway-model-value="localOneway.mo00615OtherServiceOnewayType"
                              :readonly="props.onewayModelValue.cpyFlg"
                              class="text-height"
                            />
                          </c-v-col>
                        </c-v-row>
                      </div>
                    </div>
                    <div
                      no-gutters
                      class="textheight weekly-container col-br-border"
                    >
                      <g-custom-or-x-0163
                        v-model="local.weeklyModel"
                        :oneway-model-value="localOneway.OrX0163OnewayWeekly"
                        :readonly="props.onewayModelValue.cpyFlg"
                        class="hoken-container pt-3"
                        @on-click-edit-btn="otherServiceBtnClick"
                      ></g-custom-or-x-0163>
                    </div>
                  </div>
                </div>
                <div style="width: 206px">
                  <div>
                    <div class="grid-header header-container justify-center align-center">
                      <c-v-row class="officeSelect">
                        <c-v-col>
                          <base-mo00615
                            :oneway-model-value="localOneway.mo00615HokenServiceOnewayType"
                            :readonly="props.onewayModelValue.cpyFlg"
                            class="text-height aLink"
                            @click="hokenServiceBtnClick"
                          />
                        </c-v-col>
                      </c-v-row>
                    </div>
                  </div>
                  <div
                    no-gutters
                    class="textheight hoken-container col-br-border"
                  >
                    <g-custom-or-x-0163
                      v-model="local.hokenModel"
                      :oneway-model-value="localOneway.OrX0163OnewayHoken"
                      :readonly="props.onewayModelValue.cpyFlg"
                      class="hoken-container pt-2"
                    ></g-custom-or-x-0163>
                  </div>
                </div>
              </c-v-row>
            </c-v-row>
          </div>
        </c-v-row>
      </c-v-col>
    </c-v-row>

    <!-- OrXCalendarSample: 週間表入力  -->
    <g-custom-or-26714
      v-if="showDialogOr26714 && !props.onewayModelValue.cpyFlg"
      v-bind="or26714"
      v-model="local.or26714"
      :oneway-model-value="Or26714Data"
      @update:model-value="handleOr26714Confirm"
    />
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="or51775Other"
      :oneway-model-value="localOneway.or51775OnewayModel"
      @confirm="handleOr51775Confirm"
    />
  </c-v-sheet>
</template>

<style scoped lang="scss">
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.viewcpy {
  display: flex;
  flex-direction: column;
  height: 480px;
  background-color: transparent;
}
:deep(.v-window__container) {
  height: 100%;
}

.main-Content {
  .main-header {
    margin-left: 1050px;
    height: 10px;
  }
  .main-left {
    max-width: 20%;
  }

  .main-right {
    .middleContent {
      .v-window {
        width: 100%;
      }
      flex: 1 1 auto;
    }
  }
}
.header {
  :deep(.v-sheet) {
    background-color: transparent !important;
  }
}

/* 主な日常生活一覧タイトル */
.grid-header {
  display: flex;
  height: calc(var(--custom-calendar-cell-height, 18px) * 2);
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
  background: #5c5d5e;
  box-sizing: border-box;
  flex: 1;
  text-align: center;
  padding: 4px;
  display: flex;
  flex-direction: column;
}

.gridheight :deep(.v-field__input) {
  min-height: var(--custom-calendar-cell-height, 36px);
  border-bottom: 1px solid #ddd;
  border-right: 1px solid #ddd;
}
.officeSelect {
  margin: unset;

  .v-col {
    max-width: unset;
    width: auto;
    flex: 0 0 auto;
    padding: 0;
    align-self: center;

    :deep(label) {
      line-height: 2rem;
    }
  }
}
.calendar-container {
  background: #fff;
}
.hoken-container {
  background: #f2f2f2 !important;
}
.header-container {
  background: #dbeefe !important;
}
.weekly-container {
  background: #ffffff !important;
}
:deep(.create_width .d-flex) {
  width: 140px !important;
}

:deep(.syori_width .d-flex) {
  width: 180px !important;
}

.print {
  cursor: pointer;
}

.font-color-red {
  color: rgb(217, 2, 20);
}
.text-center {
  align-items: baseline;
}
.multiline-text {
  white-space: pre-line;
  text-align: left;
}
:deep(.multiline-text .item-label) {
  color: var(--custom-color);
}
.textheight {
  :deep(textarea) {
    height: 864px !important;
  }
}
:deep(.v-alert) {
  border-width: 1px !important;
  border-style: solid;
}
.col-br-border {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}
:deep(.table-textarea-content-icon) {
  height: 100%;
  align-items: start !important;
  padding-top: 8px !important;
}
:deep(.text-height) {
  height: 23px !important;
  min-height: 23px !important;
}

.aLink {
  text-decoration: underline;
  color: #214d97;
  cursor: pointer;
}

:deep(.font-color-blue) {
  color: #214d97;
  font-weight: bold !important;
  cursor: pointer;
}
</style>
