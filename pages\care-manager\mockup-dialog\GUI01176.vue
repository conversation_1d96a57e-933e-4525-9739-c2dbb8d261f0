<script setup lang="ts">
import { computed, definePageMeta, ref, useInitialize, useScreenStore } from '#imports'
import { Or26825Const } from '~/components/custom-components/organisms/Or26825/Or26825.constants'
import { Or26825Logic } from '~/components/custom-components/organisms/Or26825/Or26825.logic'
import type { Or26825Type } from '~/types/cmn/business/components/Or26825Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 * SH 2025/06/06 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI01176'
// ルーティング
const routing = 'GUI01176/pinia'
// 画面物理名
const screenName = 'GUI01176'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or26825 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01176' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or26825Logic.initialize(or26825.value.uniqueCpId)
}
/**
 * 初期データ取得
 */
async function initData() {}
await initData()

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01176',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26825Const.CP_ID(0) }],
})

const or26825Type = ref<Or26825Type>({
  /** 設定単位数*/
  setUnitsNumber: '',
})

// ダイアログ表示フラグ
const showDialogOr26825 = computed(() => {
  return true
})
/**
 *  ボタン押下時の処理(Or26825)
 *
 */
function onClickOr26825() {
  // Or26825のダイアログ開閉状態を更新する
  Or26825Logic.state.set({
    uniqueCpId: or26825.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>

<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/05/16 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26825()"
        >GUI01176_設定単位数入力 初期表示
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/05/16 ADD END-->
  <g-custom-or-26825
    v-if="showDialogOr26825"
    v-bind="or26825"
    v-model="or26825Type"
  />
  <div class="pt-5 pl-5">
    return ----- data
    <div>
      {{ or26825Type }}
    </div>
  </div>
</template>
