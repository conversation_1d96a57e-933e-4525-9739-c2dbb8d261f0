import { HttpResponse, type ResponseResolver } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { IGuiStaffListSelectInEntity } from '~/components/custom-components/organisms/GuiStaffList/GuiStaffListSelectEntity'

/**
 * 職員管理画面 データ取得モック
 *
 * @description
 * 職員管理に表示するデータを返却する。
 * serviceId："staff_list"
 */
export function handler(request: IGuiStaffListSelectInEntity) {
  const business = request

  // 職員番号
  const staffNumber = business.staffNumber

  // 職員名
  const staffName = business.staffName

  // ログインID
  const loginId = business.loginId

  // 勤務形態
  const workingStyle = business.workingStyle

  // 権限
  const permission = business.permission

  // アカウント
  const account = business.account

  // リミット
  const limit = business.limit
  // オフセット
  const offset = business.offset

  let workList = defaultData.staffList

  // 検索条件の「職員番号」について絞り込み
  if (staffNumber !== '') {
    workList = workList.filter((workItem) => {
      if (workItem.staffNumber === staffNumber) {
        return true
      }
    })
  }

  // 検索条件の「職員名」について絞り込み
  if (staffName !== '') {
    workList = workList.filter((workItem) => {
      if (workItem.staffName === staffName) {
        return true
      }
    })
  }

  // 検索条件の「ログインID」について絞り込み
  if (loginId !== '') {
    workList = workList.filter((workItem) => {
      if (workItem.loginId === loginId) {
        return true
      }
    })
  }

  // 検索条件の「勤務形態」について絞り込み
  if (workingStyle !== '') {
    workList = workList.filter((workItem) => {
      let strWorkingStyle = String(workItem.workingStyle)

      if (strWorkingStyle === workingStyle) {
        return true
      }
    })
  }

  // 検索条件の「権限」について絞り込み
  if (permission !== '') {
    workList = workList.filter((workItem) => {
      let strPermission = String(workItem.permission)

      if (strPermission === permission) {
        return true
      }
    })
  }

  // 検索条件の「アカウント」について絞り込み
  if (account !== '') {
    workList = workList.filter((workItem) => {
      let strAccount = String(workItem.account)

      if (strAccount === account) {
        return true
      }
    })
  }

  // トータル件数の取得（リミット・オフセット関係無く全件の件数）
  const workTotal = workList.length

  // リミットとオフセットの考慮
  if (limit > 0 && offset >= 0) {
    if (workList.length > 0) {
      workList = workList.slice(offset, offset + limit)
    }
  }

  // レスポンスを設定
  const res = {
    total: workTotal,
    list: workList,
  }

  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...res,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
