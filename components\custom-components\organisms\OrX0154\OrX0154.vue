<script setup lang="ts">
/**
 * OrX0154:有機体:印刷設定モーダル
 * GUI04513_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26326Const } from '../Or26326/Or26326.constants'
import { Or26326Logic } from '../Or26326/Or26326.logic'
import { Or26328Const } from '../Or26328/Or26328.constants'
import { Or26328Logic } from '../Or26328/Or26328.logic'
import { Or28780Const } from '../Or28780/Or28780.constants'
import { Or28780Logic } from '../Or28780/Or28780.logic'
import { OrX0130Const } from '../OrX0130/OrX0130.constants'
import { Or26340Const } from '../Or26340/Or26340.constants'
import { Or20447Const } from '../Or20447/Or20447.constants'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import type { OrX0130TableType } from '../OrX0130/OrX0130.type'
import { OrX0171Logic } from '../OrX0171/OrX0171.logic'
import { OrX0171Const } from '../OrX0171/OrX0171.constants'
import { OrX0154Const } from './OrX0154.constants'
import type {
  OrX0154StateType,
  InitMedMasterObj,
  PrtHistoryInfo,
  DbNoSaveDataInfo,
} from './OrX0154.type'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import { usePrint, type FormInfo } from '~/utils/usePrint'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { IInitMasterInfo } from '~/repositories/cmn/entities/GyoumuComSelectEntity'
import type { OrX0154OnewayType } from '~/types/cmn/business/components/OrX0154Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Or26328OnewayType } from '~/types/cmn/business/components/Or26328Type'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  PlanAllPrintSettingsInitUpdateInEntity,
  PlanAllPrintSettingsInitUpdateOutEntity,
} from '~/repositories/cmn/entities/PlanAllPrintSettingsInitUpdateEntity'
import type {
  PrintSelectInEntity,
  IPrintInfo,
  IPrintSettingInfo,
} from '~/repositories/cmn/entities/PrintSelectEntity'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { KikanRirekiData } from '~/repositories/cmn/entities/AttendingPhysicianStatementPrintSettingsInitUpdateEntity'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CustomClass } from '~/types/CustomClassType'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type {
  CmnTucPlanObj,
  PlanAllPrintSettingsSubjectSelectInEntity,
  PlanAllPrintSettingsSubjectSelectOutEntity,
} from '~/repositories/cmn/entities/PlanAllPrintSettingsSubjectSelectEntity'
import type { PrintCloseUpdateEntity } from '~/repositories/cmn/entities/PrintCloseUpdateEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { reportOutputType } from '~/utils/useReportUtils'
import type { OrX0171History } from '~/types/cmn/business/components/OrX0171Type'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props<T extends IInitMasterInfo> {
  onewayModelValue: OrX0154OnewayType<T>
  uniqueCpId: string
}

const props = defineProps<Props<InitMedMasterObj>>()

const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or26326 = ref({ uniqueCpId: Or26326Const.CP_ID(0) })
const or26328 = ref({ uniqueCpId: Or26328Const.CP_ID(0) })
const or28780 = ref({ uniqueCpId: Or28780Const.CP_ID(0) })
const orX0130 = ref({ uniqueCpId: OrX0130Const.CP_ID(0) })
const or26340 = ref({ uniqueCpId: Or26340Const.CP_ID(0) })
const or20447 = ref({ uniqueCpId: Or20447Const.CP_ID(0) })
const orX0145 = ref({ uniqueCpId: OrX0145Const.CP_ID(0) })
const orX0171 = ref({ uniqueCpId: OrX0171Const.CP_ID(0) })

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// 印刷共通処理
const printCom = usePrint()
// 印刷権限
const prtFlg: boolean = await hasPrintAuth()

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or26326Const.CP_ID(0)]: or26326.value,
  [Or26328Const.CP_ID(0)]: or26328.value,
  [Or28780Const.CP_ID(0)]: or28780.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or26340Const.CP_ID(0)]: or26340.value,
  [Or20447Const.CP_ID(0)]: or20447.value,
  [OrX0145Const.CP_ID(0)]: orX0145.value,
  [OrX0171Const.CP_ID(0)]: orX0171.value,
})

// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOrX0171 = computed(() => {
  // Or00100のダイアログ開閉状態
  return OrX0171Logic.state.get(orX0171.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
// ローカル双方向bind
const local = reactive({
  orX0154: {
    // 印刷設定情報リスト
    prtList: [] as IPrintInfo[],
  },
  // 出力帳票名一覧
  mo01334: {
    value: '',
    values: [],
  } as Mo01334Type,
  // 帳票タイトル
  titleInput: {
    value: '',
  } as Mo00045Type,
  // 日付印刷区分
  mo00039Type: '',
  // 指定日
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  // 基準日
  mo00020BasicDate: {
    value: props.onewayModelValue.sysYmd,
  },
  // サービス提供年月
  mo01352BasicDate: {
    value: props.onewayModelValue.sysYm ?? props.onewayModelValue.sysYmd.slice(0, 7),
  },
  orX0145: {
    value: {} as TantoCmnShokuin,
  } as OrX0145Type,
})

// 担当ケアマネ
local.orX0145.value = {
  counter: OrX0154Const.EMPTY,
  chkShokuId: OrX0154Const.EMPTY,
  houjinId: OrX0154Const.EMPTY,
  shisetuId: OrX0154Const.EMPTY,
  svJigyoId: OrX0154Const.EMPTY,
  shokuin1Kana: OrX0154Const.EMPTY,
  shokuin2Kana: OrX0154Const.EMPTY,
  shokuin1Knj: OrX0154Const.EMPTY,
  shokuin2Knj: OrX0154Const.EMPTY,
  sex: OrX0154Const.EMPTY,
  birthdayYmd: OrX0154Const.EMPTY,
  zip: OrX0154Const.EMPTY,
  kencode: OrX0154Const.EMPTY,
  citycode: OrX0154Const.EMPTY,
  areacode: OrX0154Const.EMPTY,
  addressKnj: OrX0154Const.EMPTY,
  tel: OrX0154Const.EMPTY,
  kaikeiId: OrX0154Const.EMPTY,
  kyuyoKbn: OrX0154Const.EMPTY,
  partKbn: OrX0154Const.EMPTY,
  inYmd: OrX0154Const.EMPTY,
  outYmd: OrX0154Const.EMPTY,
  shozokuId: OrX0154Const.EMPTY,
  shokushuId: OrX0154Const.EMPTY,
  shokuId: OrX0154Const.EMPTY,
  timeStmp: OrX0154Const.EMPTY,
  delFlg: OrX0154Const.EMPTY,
  shokuNumber: OrX0154Const.EMPTY,
  caremanagerKbn: OrX0154Const.EMPTY,
  shokuType1: OrX0154Const.EMPTY,
  shokuType2: OrX0154Const.EMPTY,
  kGroupid: OrX0154Const.EMPTY,
  bmpPath: OrX0154Const.EMPTY,
  bmpYmd: OrX0154Const.EMPTY,
  hankoPath: OrX0154Const.EMPTY,
  kojinPath: OrX0154Const.EMPTY,
  keitaitel: OrX0154Const.EMPTY,
  eMail: OrX0154Const.EMPTY,
  senmonNo: OrX0154Const.EMPTY,
  sgfFlg: OrX0154Const.EMPTY,
  srvSekiKbn: OrX0154Const.EMPTY,
  shokushuId2: OrX0154Const.EMPTY,
  shokushuId3: OrX0154Const.EMPTY,
  shokushuId4: OrX0154Const.EMPTY,
  shokushuId5: OrX0154Const.EMPTY,
  shikakuId1: OrX0154Const.EMPTY,
  shikakuId2: OrX0154Const.EMPTY,
  shikakuId3: OrX0154Const.EMPTY,
  shikakuId4: OrX0154Const.EMPTY,
  shikakuId5: OrX0154Const.EMPTY,
  kyuseiFlg: OrX0154Const.EMPTY,
  kyuseiKana: OrX0154Const.EMPTY,
  kyuseiKnj: OrX0154Const.EMPTY,
  sort: OrX0154Const.EMPTY,
  selfNumber: OrX0154Const.EMPTY,
  ichiranShokushuIdNm: OrX0154Const.EMPTY,
  shokushuId2Nm: OrX0154Const.EMPTY,
  shokushuId3Nm: OrX0154Const.EMPTY,
  shokushuId4Nm: OrX0154Const.EMPTY,
  shokushuId5Nm: OrX0154Const.EMPTY,
  stopFlg: OrX0154Const.EMPTY,
  shokuinKnj: '',
  shokuinKana: OrX0154Const.EMPTY,
  title: OrX0154Const.EMPTY,
  value: OrX0154Const.EMPTY,
} as TantoCmnShokuin

// ローカルOneway
const localOneway = reactive({
  mo00615Oneway: {
    itemLabel: t('label.basic-settings'),
    customClass: new CustomClass({ outerStyle: 'background:rgb(var(--v-theme-black-100))' }),
  } as Mo00615OnewayType,
  // 利用者
  orX0130Oneway: {
    selectMode: OrX0154Const.DEFAULT.HUKUSUU,
    tableStyle: 'width:270px',
    userId: props.onewayModelValue.userId,
    focusSettingInitial: props.onewayModelValue.gojuuOnKana,
    // userList: props.onewayModelValue.userList,
  } as OrX0130OnewayType,
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    Width: '1420px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  // 印刷ボタン
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  // 出力帳票名一覧
  mo01334Oneway: {
    headers: [
      {
        title: t('label.report'),
        key: 'prtTitle',
        sortable: false,
        minWidth: '100',
      },
    ],
    items: [],
    height: 655,
  } as Mo01334OnewayType,
  // 帳票タイトル
  or26328Oneway: {
    maxLength: '50',
  } as Or26328OnewayType,
  // 日付印刷
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  // 利用者選択
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
  OrX0171Oneway: {},
})

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: OrX0154Const.DEFAULT.IS_OPEN,
})

// 選択した利用者データ
const selectedUserList = ref<OrX0130TableType[]>([])

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<OrX0154StateType>({
  cpId: OrX0154Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? OrX0154Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 指定日 システム日付
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  // 汎用コードマスタデータを取得し初期化
  await initCodes()
  // 印刷設定情報リスト
  await getPrintSettingList()
})

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
      setChildrenValue()
    }
  }
)

/**
 * 「出力帳票名」選択
 */
watch(
  () => Or26326Logic.data.get(or26326.value.uniqueCpId)?.mo01334Type.value,
  (newValue, oldValue) => {
    if (newValue) {
      local.mo01334.value = newValue
    }
    if (oldValue) {
      for (const item of localOneway.mo01334Oneway.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.帳票タイトルが空白以外の場合
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            if (local.titleInput.value) item.prtTitle = local.titleInput.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prnDate = local.mo00039Type
            // 指定日
            item.mo00020Type = local.mo00020Type.value ?? systemCommonsStore.getSystemDate!
            // 画面.基準日
            item.mo00020BasicDate = local.mo00020BasicDate.value ?? props.onewayModelValue.sysYmd
            // 画面.サービス提供年月
            item.mo01352BasicDate =
              local.mo01352BasicDate.value ??
              props.onewayModelValue.sysYm ??
              props.onewayModelValue.sysYmd.slice(0, 7)
            // 画面.担当ケアマネＩＤ
            item.orX0145 = local.orX0145
            setPrtList(oldValue)
          }
        }
      }
    }
    for (const item of localOneway.mo01334Oneway.items) {
      if (item) {
        if (newValue === item.id) {
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prnDate as string
          // 指定日
          local.mo00020Type = {
            value: (item?.mo00020Type as string) ?? systemCommonsStore.getSystemDate!,
          } as Mo00020Type
          // 画面.基準日
          local.mo00020BasicDate.value =
            (item?.mo00020BasicDate as string) ?? props.onewayModelValue.sysYmd
          // 画面.サービス提供年月
          local.mo01352BasicDate.value =
            (item?.mo01352BasicDate as string) ??
            props.onewayModelValue.sysYm ??
            props.onewayModelValue.sysYmd.slice(0, 7)
          // 画面.担当ケアマネＩＤ
          local.orX0145 = item.orX0145 as OrX0145Type
          setPrtList(newValue)
        }
      }
    }
    setChildrenValue()
  }
)

/**
 * 帳票タイトルの監視
 */
watch(
  () => Or26328Logic.data.get(or26328.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // タイトル
      local.titleInput = newValue.titleInput
    }
  }
)

/**
 * 日付印刷の監視
 */
watch(
  () => Or28780Logic.data.get(or28780.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // 日付印刷区分
      local.mo00039Type = newValue.mo00039Type
      // 指定日
      local.mo00020Type = newValue.mo00020Type
    }
  }
)

/**
 * 帳票タイトルの監視
 */
watch(
  () => local.orX0145,
  (newValue) => {
    if (newValue) {
      localOneway.orX0130Oneway.tantouCareManager = (newValue.value as TantoCmnShokuin).shokuinKnj
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  (newValue) => {
    selectedUserList.value = newValue!.userList
  }
)

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  // 利用者選択
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: PlanAllPrintSettingsInitUpdateInEntity = {
    // セクション名:親画面.セクション名
    sectionName: props.onewayModelValue.sectionName,
    // 職員ID:親画面.職員ID
    shokuId: props.onewayModelValue.shokuId,
    // 法人ID:親画面.法人ID
    houjinId: props.onewayModelValue.houjinId,
    // 施設ID:親画面.施設ID
    shisetuId: props.onewayModelValue.shisetuId,
    // 事業所ID:親画面.事業所ID
    svJigyoId: props.onewayModelValue.svJigyoId,
    // システムコード:共通情報.システムコード
    gsyscd: props.onewayModelValue.systemCode,
    // 利用者ID:親画面.利用者ID
    userId: props.onewayModelValue.userId,
    // 機能名
    kinouKnj: '[mnu3][3GK]計画書(1)',
  }

  // バックエンドAPIから初期情報取得
  const ret = await printCom.doPrintGet<
    PrintSelectInEntity,
    IPrintInfo,
    KikanRirekiData,
    PlanAllPrintSettingsInitUpdateOutEntity
  >(inputData, 'planAllPrintSettingsInitUpdate')
  // 印刷設定情報リスト、期間履歴情報リスト、期間管理フラグを取得
  printCom.doGetPrintData<PlanAllPrintSettingsInitUpdateOutEntity, IPrintInfo, KikanRirekiData>(
    ret,
    local.orX0154
  )
  // Onewayの印刷設定情報リストを設定
  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of local.orX0154.prtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }
  // Onewayの印刷設定情報リストを設定
  localOneway.mo01334Oneway.items = mo01334OnewayList

  if (local.orX0154.prtList.length > 0) {
      //親画面.初期選択セクションが1の場合、一件目選択状態にする
      local.mo01334.value = local.orX0154.prtList[0].prtNo
  }
  // 子モジュールに値を渡す
  setChildrenValue()
}

/**
 * 子モジュールに値を渡す
 *
 */
function setChildrenValue() {
  setChildCpBinds(props.uniqueCpId, {
    // 帳票タイトル
    [Or26328Const.CP_ID(0)]: {
      twoWayValue: {
        titleInput: local.titleInput,
      },
    },
    // 出力帳票名一覧
    [Or26326Const.CP_ID(0)]: {
      twoWayValue: {
        mo01334Type: local.mo01334,
      },
    },
    // 日付印刷
    [Or28780Const.CP_ID(0)]: {
      twoWayValue: {
        mo00039Type: local.mo00039Type,
        mo00020Type: local.mo00020Type,
      },
    },
    // 基準日
    [Or26340Const.CP_ID(0)]: {
      twoWayValue: {
        mo00020: local.mo00020BasicDate,
      },
    },
    // サービス提供年月
    [Or20447Const.CP_ID(0)]: {
      twoWayValue: {
        mo01352: local.mo01352BasicDate,
      },
    },
  })
}

/**
 * 「閉じるボタン」押下
 */
async function close() {
  // 画面の印刷設定情報を保存する
  await printCom.doPrintClose(
    local.titleInput,
    {
      // セクション名:親画面.セクション名
      sectionName: props.onewayModelValue.sectionName,
      // システムコード：親画面.システムコード
      gsyscd: props.onewayModelValue.systemCode,
      // 職員ID：親画面.職員ID
      shokuId: props.onewayModelValue.shokuId,
      // 法人ID：親画面.法人ID
      houjinId: props.onewayModelValue.houjinId,
      // 施設ID：親画面.施設ID
      shisetuId: props.onewayModelValue.shisetuId,
      // 事業者ID：親画面.事業者ID
      svJigyoId: props.onewayModelValue.svJigyoId,
      // 印刷設定情報リスト：印刷設定情報リスト
      prtList: local.orX0154.prtList,
    },
    'planAllPrintSettingsInfoUpdate',
    localOneway.mo01334Oneway.items,
    local.mo01334.value,
    props.uniqueCpId,
    showMessageBox,
    closeDialog
  )
}

/**
 * 本画面を閉じる
 */
function closeDialog() {
  setState({ isOpen: false })
}

/**
 * 「印刷」ボタン押下
 */
async function print() {
  // 業務チェックを行う
  if (!local.titleInput.value) {
    void showMessageBox('message.e-cmn-40172', 'btn.yes')
    return
  }
  // PDFダウンロード
  await executePrint()
}

/**
 * PDFダウンロード
 */
async function executePrint() {
  // 利用者配下の履歴情報を再取得する
  const res: PlanAllPrintSettingsSubjectSelectOutEntity = await ScreenRepository.select(
    'planAllPrintSettingsSubjectSelect',
    {
      // 利用者リスト：画面.利用者一覧で選択した利用者リスト
      userList: selectedUserList.value.map((x) => {
        return {
          userId: x.selfId,
          userName: x.name1Knj + ' ' + x.name2Knj,
        }
      }),
      // 事業所ID：親画面.事業所ID
      svJigyoId: props.onewayModelValue.svJigyoId,
      // 基準日：画面.基準日
      kijunbiYmd: local.mo00020BasicDate.value,
      // サービス提供年月
      svYm: local.mo01352BasicDate.value,
      // 事業所CD
      svJigyoCD: props.onewayModelValue.jigyoCd,
      // 印刷設定情報リスト
      prtList: local.orX0154.prtList,
      // 初期設定マスタの情報
      initMasterObj: props.onewayModelValue.initMasterObj,
      // 職員ID
      shokuId: props.onewayModelValue.shokuId,
      // 法人ID
      houjinId: props.onewayModelValue.houjinId,
      // 施設ID
      shisetuId: props.onewayModelValue.shisetuId,
    } as PlanAllPrintSettingsSubjectSelectInEntity
  )
  const inputData: PrintCloseUpdateEntity<IPrintInfo> = {
    // セクション名:親画面.セクション名
    sectionName: props.onewayModelValue.sectionName,
    // システムコード：親画面.システムコード
    gsyscd: props.onewayModelValue.systemCode,
    // 職員ID：親画面.職員ID
    shokuId: props.onewayModelValue.shokuId,
    // 法人ID：親画面.法人ID
    houjinId: props.onewayModelValue.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: props.onewayModelValue.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: props.onewayModelValue.svJigyoId,
    // 印刷設定情報リスト：印刷設定情報リスト
    prtList: local.orX0154.prtList,
  }
  // 印刷設定情報を保存する
  await printCom.doPrintUpdate(inputData, 'planAllPrintSettingsInfoUpdate')
  // 画面.印刷対象履歴リストを作成する TODO
  const selectedIndex = local.orX0154.prtList.findIndex((x) => x.prtNo === local.mo01334.value)
  const selectedPrtData = local.orX0154.prtList[selectedIndex]
  const historyList: OrX0171History[] = res.data.prtHistoryList.map((x) => {
    let reportId = ''
    let printSet: IPrintSettingInfo = {
      prtTitle: '',
      sectionNo: '',
      prtNo: '',
      profile: '',
      prnDate: '',
      prnshoku: '',
      param01: '',
      param02: '',
      param03: '',
      param04: '',
      param05: '',
      param06: '',
      param07: '',
      param08: '',
      param09: '',
      param10: '',
      param11: '',
      param12: '',
      param13: '',
      param14: '',
      param15: '',
      param16: '',
      param17: '',
      param18: '',
      param19: '',
      param20: '',
      param21: '',
      param22: '',
      param23: '',
      param24: '',
      param25: '',
      param26: '',
      param27: '',
      param28: '',
      param29: '',
      param30: '',
      param31: '',
      param32: '',
      param33: '',
      param34: '',
      param35: '',
      param36: '',
      param37: '',
      param38: '',
      param39: '',
      param40: '',
      param41: '',
      param42: '',
      param43: '',
      param44: '',
      param45: '',
      param46: '',
      param47: '',
      param48: '',
      param49: '',
      param50: '',
    }
    let tantoId = ''
    let userList: CmnTucPlanObj[] = []
    let processYmd = null
    let fuseji = ''
    switch (x.prtIdx) {
      // 印刷対象一覧情報リスト[i].帳票種類が1の場合
      case OrX0154Const.PRTIDX_1:
        // 親画面.初期設定マスタの情報.計画書様式が1: 施設の場合、"plan1sReportService"; 以外の場合、"plan1kReportService"
        if (props.onewayModelValue.initMasterObj.cksFlg === '1') {
          reportId = 'plan1sReportService'
        } else {
          reportId = 'plan1kReportService'
        }
        // 印刷対象一覧情報リスト[i].帳票種類が1の場合、一括印刷対象の印刷設定情報リストの1件目
        printSet = convertToIPrintSettingInfo(res.data.prtList[0])
        tantoId = (local.orX0145.value as TantoCmnShokuin).chkShokuId
        break
      // 印刷対象一覧情報リスト[i].帳票種類が2の場合
      case OrX0154Const.PRTIDX_2:
        // 親画面.初期設定マスタの情報.計画書様式が1: 施設の場合、"shisetsuServiceReport"; 以外の場合、"kyotakuServiceReport"
        if (props.onewayModelValue.initMasterObj.cksFlg === '1') {
          reportId = 'shisetsuServiceReport'
        } else {
          reportId = 'kyotakuServiceReport'
        }
        // 印刷対象一覧情報リスト[i].帳票種類が2の場合、一括印刷対象の印刷設定情報リストの2件目
        printSet = convertToIPrintSettingInfo(res.data.prtList[1])
        // 印刷対象一覧情報リスト[i].帳票種類が2の場合、システムINI情報リストの2件目.氏名伏字設定フラグ
        fuseji = res.data.sysIniInfoList[1].amikakeFlg
        break
      case OrX0154Const.PRTIDX_5:
        if (x.yousikiCd === OrX0154Const.YOUSIKI_CD_2) {
          reportId = 'shuukanServiceR34Report'
        } else {
          reportId = 'shuukanServiceReport'
        }
        processYmd = x.tougaiYm ? x.tougaiYm + '/31' : props.onewayModelValue.sysYmd
        // 印刷対象一覧情報リスト[i].帳票種類が5の場合、一括印刷対象の印刷設定情報リストの3件目
        printSet = convertToIPrintSettingInfo(res.data.prtList[2])
        break
      case OrX0154Const.PRTIDX_6:
        reportId = 'dailyRoutinePlanReport'
        // 印刷対象一覧情報リスト[i].帳票種類が6の場合、一括印刷対象の印刷設定情報リストの4件目
        printSet = convertToIPrintSettingInfo(res.data.prtList[3])
        break
      case OrX0154Const.PRTIDX_7:
        reportId = 'ServiceRiteiReport'
        // 印刷対象一覧情報リスト[i].帳票種類が7の場合、一括印刷対象の印刷設定情報リストの5件目
        printSet = convertToIPrintSettingInfo(res.data.prtList[4])
        userList = x.cmnTucPlan
        tantoId = (local.orX0145.value as TantoCmnShokuin).chkShokuId
        break
      case OrX0154Const.PRTIDX_8:
        reportId = 'ServiceUseAnnexedTableReport'
        // 印刷対象一覧情報リスト[i].帳票種類が8の場合、一括印刷対象の印刷設定情報リストの6件目
        printSet = convertToIPrintSettingInfo(res.data.prtList[5])
        userList = x.cmnTucPlan
        tantoId = (local.orX0145.value as TantoCmnShokuin).chkShokuId
        break
    }
    return {
      // 利用者名
      userName: x.userName,
      // 結果内容
      result: x.result,
      reportId: reportId,
      outputType: reportOutputType.DOWNLOAD,
      // 帳票種類
      reportType: x.prtIdx,
      reportData: {
        // 印刷設定
        printSet: printSet,
        // 事業所名
        jigyoKnj: props.onewayModelValue.jigyoKnj,
        // システム日付
        appYmd: props.onewayModelValue.sysYmd,
        // 初期設定マスタの情報
        initMasterObj: props.onewayModelValue.initMasterObj,
        // DB未保存画面項目
        dbNoSaveData: {
          // 指定日
          // 印刷設定情報リスト選択行.日付表示有無が2の場合、画面の指定日
          selectDate: selectedPrtData.prnDate === '2' ? local.mo00020Type.value : '',
          // システムコード
          sysCd: props.onewayModelValue.systemCode,
          // 職員ID
          shokuinId: props.onewayModelValue.shokuId,
          // 記入用シートを印刷するフラグ
          emptyFlg: '0',
        },
        // 印刷対象履歴
        printSubjectHistory: {
          userId: x.userId,
          rirekiId: x.rirekiId,
          sc1Id: x.sc1Id,
          result: x.result,
          tantoId: tantoId,
          processYmd: processYmd,
          yymmYm: local.mo01352BasicDate.value,
          userList: userList,
        },
        // 伏字印刷区分について
        fuseji: fuseji,
      } as FormInfo<InitMedMasterObj, DbNoSaveDataInfo, PrtHistoryInfo>,
    } as OrX0171History
  })
  localOneway.OrX0171Oneway = {
    printType: res.data.prtList[0].param04,
    selectedReport: res.data.selectedReport,
    historyList: historyList,
  }
  // PDFダウンロードを行う
  OrX0171Logic.state.set({
    uniqueCpId: orX0171.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * エラーダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 *
 * @param isError - エラーかどうか
 *
 * @returns ダイアログの選択結果（yes）
 */
const showMessageBox = async (text: string, btn: string, isError = true) => {
  if (isError) {
    return await openErrorDialog(text, btn)
  } else {
    return await openInfoDialog(text)
  }
}

/**
 * ダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 */
function openErrorDialog(text: string, btn: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t(text),
      firstBtnType: 'normal1',
      firstBtnLabel: t(btn) ?? t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)
        let result = OrX0154Const.DEFAULT.YES
        if (event?.firstBtnClickFlg) {
          result = OrX0154Const.DEFAULT.YES
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログの開閉
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes）
 */
async function openInfoDialog(paramDialogText: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t(paramDialogText),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 印刷設定情報リストの値を設定する
 *
 * @param index - 印刷設定情報リストのインデックス
 */
function setPrtList(index: string) {
  // 印刷設定情報リストのインデックス
  const idx = local.orX0154.prtList.findIndex((x) => x.prtNo === index)
  if (idx !== -1) {
    // 選択された印刷設定情報リストのデータ
    const selectedData = local.orX0154.prtList[idx]
    // 帳票タイトル名
    selectedData.prtTitle = local.titleInput.value
    // 日付表示有無
    selectedData.prnDate = local.mo00039Type
  }
}

function convertToIPrintSettingInfo(inType: IPrintInfo): IPrintSettingInfo {
  return {
    prtTitle: inType.prtTitle,
    sectionNo: inType.sectionNo,
    prtNo: inType.prtNo,
    profile: inType.profile,
    prnDate: inType.prnDate,
    prnshoku: inType.prnshoku,
    param01: inType.param01,
    param02: inType.param02,
    param03: inType.param03,
    param04: inType.param04,
    param05: inType.param05,
    param06: inType.param06,
    param07: inType.param07,
    param08: inType.param08,
    param09: inType.param09,
    param10: inType.param10,
    param11: inType.param11,
    param12: inType.param12,
    param13: inType.param13,
    param14: inType.param14,
    param15: inType.param15,
    param16: inType.param16,
    param17: inType.param17,
    param18: inType.param18,
    param19: inType.param19,
    param20: inType.param20,
    param21: inType.param21,
    param22: inType.param22,
    param23: inType.param23,
    param24: inType.param24,
    param25: inType.param25,
    param26: inType.param26,
    param27: inType.param27,
    param28: inType.param28,
    param29: inType.param29,
    param30: inType.param30,
    param31: inType.param31,
    param32: inType.param32,
    param33: inType.param33,
    param34: inType.param34,
    param35: inType.param35,
    param36: inType.param36,
    param37: inType.param37,
    param38: inType.param38,
    param39: inType.param39,
    param40: inType.param40,
    param41: inType.param41,
    param42: inType.param42,
    param43: inType.param43,
    param44: inType.param44,
    param45: inType.param45,
    param46: inType.param46,
    param47: inType.param47,
    param48: inType.param48,
    param49: inType.param49,
    param50: inType.param50,
  } as IPrintSettingInfo
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row no-gutters>
        <c-v-col
          class="col-view"
          cols="2"
        >
          <!-- 帳票 -->
          <g-custom-or-26326
            v-bind="or26326"
            :oneway-model-value="localOneway.mo01334Oneway"
          />
        </c-v-col>
        <c-v-col
          cols="4"
          class="border_right"
          style="margin-top: -12px"
        >
          <div class="basic-title">
            <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway" />
          </div>
          <c-v-divider class="my-0"></c-v-divider>
          <!-- タイトル -->
          <g-custom-or-26328
            v-bind="or26328"
            :oneway-model-value="localOneway.or26328Oneway"
          />
          <c-v-divider class="my-0"></c-v-divider>
          <!-- 日付印刷 -->
          <g-custom-or-28780
            v-bind="or28780"
            :oneway-model-value="localOneway.mo00039OneWay"
          />
        </c-v-col>
        <c-v-col
          cols="6"
          class="pl-4"
        >
          <c-v-row no-gutters>
            <!-- 基準日選択 -->
            <g-custom-or-26340 v-bind="or26340"></g-custom-or-26340>
            <!-- サービス提供年月選択 -->
            <g-custom-or-20447 v-bind="or20447"></g-custom-or-20447>
            <!-- 担当ケアマネラベル -->
            <g-custom-or-x-0145
              v-bind="orX0145"
              v-model="local.orX0145"
              :oneway-model-value="localOneway.orX0145Oneway"
            ></g-custom-or-x-0145>
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 印刷設定画面利用者一覧 -->
            <g-custom-or-x-0130
              v-bind="orX0130"
              :oneway-model-value="localOneway.orX0130Oneway"
            >
            </g-custom-or-x-0130>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          class="mx-2"
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mr-2"
          :disabled="prtFlg"
          @click="print()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
  <g-custom-or-x-0171
    v-if="showDialogOrX0171"
    v-bind="orX0171"
    :oneway-model-value="localOneway.OrX0171Oneway"
    :unique-cp-id="orX0171.uniqueCpId"
  />
</template>
<style scoped lang="scss">
.border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}
.col-view {
  margin-left: -12px;
  margin-top: -12px;
}
.basic-title {
  height: 37px;
  background: rgb(var(--v-theme-black-100)) !important;
  display: flex;
  align-items: center;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
}
</style>
