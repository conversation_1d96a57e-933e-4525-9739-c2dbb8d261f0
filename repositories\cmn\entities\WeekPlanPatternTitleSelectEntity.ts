/**
 * Or27730のエンティティ
 * GUI01045_週間計画パターンタイトル
 *
 * <AUTHOR>
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 週間計画パターンタイトル情報入力エンティティ
 */
export interface WeekPlanPatternTitleSelectInEntity extends InWebEntity {
  /**
   * サービス区分
   */
  serviceKbn: number
}

/**
 * 加算情報出力エンティティ
 */
export interface WeekPlanPatternTitleSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * 週間計画パターンタイトル情報リスト
     */
    titleList: Title[]
    /**
     * 要介護度リスト
     */
    yokaiList: Youkai[]
    /**
     * 最大の有効期間
     */
    termName: string
    /**
     * 最大の有効期間ID
     */
    termId: string
  }
}

/**
 * 要介護度情報
 */
export interface Youkai {
  /**
   * 要介護状態区分
   */
  yokaiKbn: string
  /**
   * 要介護度
   */
  yokaiKnj: string
  /**
   * 表示順
   */
  sort: string
}

/**
 * 週間計画パターンタイトル情報
 */
export interface Title {
  /**
   * 週間計画ID
   */
  ks51Id: string
  /**
   * 名称
   */
  nameKnj: string
  /**
   * 要介護度区分
   */
  youkaiCd: string
  /**
   * 有効期間ID
   */
  termId: string
  /**
   * 有効期間
   */
  termName: string
  /**
   * 表示順
   */
  seq: string
  /**
   * 様式CD
   */
  yousikiCd: string
}
