import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'
import type { IReportInEntity } from '~/types/business/core/ReportRepositoryType'

/**
 * 初期情報取得リクエストパラメータタイプ
 */
export interface assessmentHomeTab7SelectInEntity extends InWebEntity {
  /**
   * 計画期間ID
   */
  sc1Id?: string

  /**
   * アセスメントID
   */
  gdlId?: string

  /**
   * 改定フラグ
   */
  ninteiFormF?: string

  /**
   * 事業者ID
   */
  svJigyoId?: string

  /**
   * 施設ID
   */
  shisetuId?: string

  /**
   * 法人ID
   */
  houjinId?: string

  /**
   * 作成日
   */
  kijunbiYmd?: string

  /**
   * まとめフラグ
   */
  matomeFlg?: string
}

/**
 * 初期情報取得API出力エンティティ
 */
export interface assessmentHomeTab7SelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 状況別まとめマスタ情報リスト
     */
    cpnMucGdlKoumokuList: CpnMucGdlKoumokuListType[]

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト
     */
    gdl4NeceH21List: Gdl4NeceH21ListType[]

    /**
     * ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト
     */
    gdl4NeceR3List: Gdl4NeceR3ListType[]
  }
}

/**
 * 状況別まとめマスタ情報リスト
 */
export interface CpnMucGdlKoumokuListType {
  /**
   * カウンター
   */
  id: string | undefined

  /**
   * マスタID
   */
  mastId: string | undefined

  /**
   * 法人ID
   */
  houjinId: string | undefined

  /**
   * 施設ID
   */
  shisetuId: string | undefined

  /**
   * 事業者ID
   */
  svJigyoId: string | undefined

  /**
   * 項目
   */
  koumokuKnj: string | undefined

  /**
   * 中止フラグ
   */
  stopFlg: string | undefined

  /**
   * ロックフラグ
   */
  lockFlg: string | undefined

  /**
   * 表示順
   */
  sort: string | undefined
}

/**
 * 状況別まとめマスタ情報リスト
 */
export interface Gdl4NeceH21ListType {
  /**
   * アセスメントID
   */
  gdlId: string | undefined

  /**
   * 計画期間ID
   */
  sc1Id: string | undefined

  /**
   * 特記事項
   */
  memoKnj: string | undefined

  /**
   * 必要性1
   */
  hitsuyo1: string | undefined

  /**
   * 必要性2
   */
  hitsuyo2: string | undefined

  /**
   * マスタID
   */
  mastId: string | undefined

  /**
   * 法人ID
   */
  houjinId: string | undefined

  /**
   * 施設ID
   */
  shisetuId: string | undefined

  /**
   * 事業者ID
   */
  svJigyoId: string | undefined

  /**
   * 利用者ID
   */
  userId: string | undefined

  /**
   * 項目
   */
  koumokuKnj: string | undefined

  /**
   * 表示順
   */
  sort: string | undefined

  /**
   * カウンター
   */
  id: string | undefined

  /**
   * 中止フラグ
   */
  stopFlg: string | undefined

  /**
   * 氏名
   */
  nameKnj: string | undefined

  /**
   * 本人との関係
   */
  kankeiKnj: string | undefined

  /**
   * 電話番号
   */
  tel: string | undefined

  /**
   * FAX
   */
  fax: string | undefined

  /**
   * メールアドレス
   */
  eMail: string | undefined

  /**
   * 備考（災害時）
   */
  bikoSaigaiKnj: string | undefined

  /**
   * 備考（権利擁護）
   */
  bikoKenriKnj: string | undefined

  /**
   * dmySelect1
   */
  dmySelect1: string | undefined

  /**
   * dmySelect2
   */
  dmySelect2: string | undefined

  /**
   * 更新回数
   */
  modifiedCnt: string | undefined
}

/**
 * 状況別まとめマスタ情報リスト
 */
export interface Gdl4NeceR3ListType {
  /**
   * アセスメントID
   */
  gdlId: string | undefined

  /**
   * 計画期間ID
   */
  sc1Id: string | undefined

  /**
   * 特記事項
   */
  memoKnj: string | undefined

  /**
   * 必要性1
   */
  hitsuyo1: string | undefined

  /**
   * 必要性2
   */
  hitsuyo2: string | undefined

  /**
   * マスタID
   */
  mastId: string | undefined

  /**
   * 法人ID
   */
  houjinId: string | undefined

  /**
   * 施設ID
   */
  shisetuId: string | undefined

  /**
   * 事業者ID
   */
  svJigyoId: string | undefined

  /**
   * 利用者ID
   */
  userId: string | undefined

  /**
   * 項目
   */
  koumokuKnj: string | undefined

  /**
   * 表示順
   */
  sort: string | undefined

  /**
   * カウンター
   */
  id: string | undefined

  /**
   * 中止フラグ
   */
  stopFlg: string | undefined

  /**
   * 氏名
   */
  nameKnj: string | undefined

  /**
   * 本人との関係
   */
  kankeiKnj: string | undefined

  /**
   * 電話番号
   */
  tel: string | undefined

  /**
   * FAX
   */
  fax: string | undefined

  /**
   * メールアドレス
   */
  eMail: string | undefined

  /**
   * 備考（災害時）
   */
  bikoSaigaiKnj: string | undefined

  /**
   * 備考（権利擁護）
   */
  bikoKenriKnj: string | undefined

  /**
   * 個別避難計画策定の有無
   */
  kobetsuhinan: string | undefined

  /**
   * 更新回数
   */
  modifiedCnt: string | undefined
}

/**
 * 状況別まとめマスタ情報リスト
 */
export interface BasicUpdateInputDataType {
  /**
   * 法人ID
   */
  houjinId: string

  /**
   * 施設ID
   */
  shisetuId: string

  /**
   * 利用者ID
   */
  userId: string

  /**
   * 事業者ID
   */
  svJigyoId: string

  /**
   * 種別ID
   */
  syubetsuId: string

  /**
   * 更新区分
   */
  updateKbn: string

  /**
   * 履歴更新区分
   */
  historyUpdateKbn: string

  /**
   * 削除処理区分
   */
  deleteKbn: string

  /**
   * 計画期間ID
   */
  sc1Id: string | undefined

  /**
   * アセスメントID
   */
  raiId: string | undefined

  /**
   * 作成日
   */
  kijunbiYmd: string

  /**
   * 作成者ID
   */
  sakuseiId: string

  /**
   * 改定フラグ
   */
  ninteiFormF: string

  /**
   * まとめフラグ
   */
  matomeFlg: string
}

/**
 * ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト
 */
export interface Gdl4NeceH21ListUpdateType {
  /**
   * マスタID
   */
  mastId?: string

  /**
   * 特記事項
   */
  memoKnj?: string

  /**
   * 必要性1
   */
  hitsuyo1?: string

  /**
   * 必要性2
   */
  hitsuyo2?: string

  /**
   * 氏名
   */
  nameKnj?: string

  /**
   * 本人との関係
   */
  kankeiKnj?: string

  /**
   * 電話番号
   */
  tel?: string

  /**
   * FAX
   */
  fax?: string

  /**
   * メールアドレス
   */
  eMail?: string

  /**
   * 備考（災害時）
   */
  bikoSaigaiKnj?: string

  /**
   * 備考（権利擁護）
   */
  bikoKenriKnj?: string

  /**
   * 更新回数
   */
  modifiedCnt?: string
}

/**
 * ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト
 */
export interface Gdl4NeceR3ListUpdateList {
  /**
   * マスタID
   */
  mastId?: string

  /**
   * 特記事項
   */
  memoKnj?: string

  /**
   * 必要性1
   */
  hitsuyo1?: string

  /**
   * 必要性2
   */
  hitsuyo2?: string

  /**
   * 氏名
   */
  nameKnj?: string

  /**
   * 本人との関係
   */
  kankeiKnj?: string

  /**
   * 電話番号
   */
  tel?: string

  /**
   * FAX
   */
  fax?: string

  /**
   * メールアドレス
   */
  eMail?: string

  /**
   * 備考（災害時）
   */
  bikoSaigaiKnj?: string

  /**
   * 備考（権利擁護）
   */
  bikoKenriKnj?: string

  /**
   * 個別避難計画策定の有無
   */
  kobetsuhinan?: string

  /**
   * 更新回数
   */
  modifiedCnt?: string
}

/**
 * 課題と目標リスト
 */
export interface KadaiListUpdateType {
  /**
   * ID
   */
  id?: string

  /**
   * アセスメント番号
   */
  assNo?: string

  /**
   * 課題
   */
  kadaiKnj?: string

  /**
   * 長期
   */
  choukiKnj?: string

  /**
   * 短期
   */
  tankiKnj?: string

  /**
   * 連番
   */
  seq?: string

  /**
   * 更新区分
   */
  updateKbn?: string

  /**
   * 更新回数
   */
  modifiedCnt?: string
}

/**
 * 更新リクエスト
 */
export interface assessmentHomeTab7UpdateInEntity extends InWebEntity {
  /** タブID */
  tabId: string
  /** 機能ID */
  kinoId: string
  /** 当履歴ページ番号 */
  krirekiNo: string
  /** e文書用パラメータ */
  edocumentUseParam: IReportInEntity
  /** e文書削除用パラメータ */
  edocumentDeleteUseParam: IReportInEntity
  /** 期間対象フラグ */
  kikanFlg: string
  /** 計画対象期間番号 */
  planningPeriodNo: string
  /** 開始日 */
  startYmd: string
  /** 終了日 */
  endYmd: string
  /** ガイドラインまとめ */
  matomeFlg: string
  /** ログインID */
  loginId: string
  /** システム略称 */
  sysRyaku: string
  /** 職員ID */
  shokuId: string
  /** システムコード */
  sysCd: string
  /** 事業者名 */
  svJigyoKnj: string
  /** 作成者名 */
  createUserName: string
  /** 利用者名 */
  userName: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 利用者ID */
  userId: string
  /** 事業者ID */
  svJigyoId: string
  /** 種別ID */
  syubetsuId: string
  /** 更新区分 */
  updateKbn: string
  /** 履歴更新区分 */
  historyUpdateKbn: string
  /** 削除処理区分 */
  deleteKbn: string
  /** 計画対象期間ID */
  sc1Id: string
  /** アセスメントID */
  raiId: string
  /** 作成日 */
  kijunbiYmd: string
  /** 作成者ID */
  sakuseiId: string
  /** 改訂フラグ */
  ninteiFlg: string
  /** ＧＬ＿全体のまとめ・特記事項（Ｈ２１改訂）リスト */
  gdl4NeceH21List: {
    /** マスタID */
    mastId: string
    /** 特記事項 */
    memoKnj: string
    /** 必要性1 */
    hitsuyo1: string
    /** 必要性2 */
    hitsuyo2: string
    /** 氏名 */
    nameKnj: string
    /** 本人との関係 */
    kankeiKnj: string
    /** 電話番号 */
    tel: string
    /** FAX */
    fax: string
    /** メールアドレス */
    eMail: string
    /** 備考（災害時） */
    bikoSaigaiKnj: string
    /** 備考（権利擁護） */
    bikoKenriKnj: string
  }[]
  /** ＧＬ＿全体のまとめ・特記事項（Ｒ３改訂）リスト */
  gdl4NeceR3List: {
    /** マスタID */
    mastId: string
    /** 特記事項 */
    memoKnj: string
    /** 必要性1 */
    hitsuyo1: string
    /** 必要性2 */
    hitsuyo2: string
    /** 氏名 */
    nameKnj: string
    /** 本人との関係 */
    kankeiKnj: string
    /** 電話番号 */
    tel: string
    /** FAX */
    fax: string
    /** メールアドレス */
    eMail: string
    /** 備考（災害時） */
    bikoSaigaiKnj: string
    /** 備考（権利擁護） */
    bikoKenriKnj: string
    /** 個別避難計画策定の有無 */
    kobetsuhinan: string
  }[]
  /** 課題と目標情報リスト */
  kadaiList: {
    /**id*/
    id: string
    /**アセスメント番号*/
    assNo: string
    /**課題*/
    kadaiKnj: string
    /**長期*/
    choukiKnj: string
    /**短期*/
    tankiKnj: string
    /**連番*/
    seq: string
    /**更新区分*/
    updateKbn: string
  }[]
}

/**
 * 更新レスポンスパラメータタイプ
 */
export interface assessmentHomeTab7UpdateOutEntity extends OutWebEntity {
  /** データ */
  data: {
    /** 計画対象期間ID */
    sc1Id: string
    /** アセスメントID */
    gdlId: string
    /** エラー区分 */
    errKbn: string
  }
}

/**
 * 新規処理APIリクエストパラメータタイプ
 */
export interface AssessmentHomeTab7NewSelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** 施設ID */
  shisetuId: string
  /** 法人ID */
  houjinId: string
}

/**
 * 新規処理APIレスポンスパラメータタイプ
 */
export interface AssessmentHomeTab7NewSelectOutEntity extends OutWebEntity {
  /** データ */
  data: {
    /** 状況別まとめマスタ情報リスト */
    cpnMucGdlKoumokuList: {
      /** カウンター */
      id: string
      /** マスタID */
      mastId: string
      /** 法人ID */
      houjinId: string
      /** 施設ID */
      shisetuId: string
      /** 事業者ID */
      svJigyoId: string
      /** 項目 */
      koumokuKnj: string
      /** 中止フラグ */
      stopFlg: string
      /** ロックフラグ */
      lockFlg: string
      /** 表示順 */
      sort: string
    }[]
  }
}
