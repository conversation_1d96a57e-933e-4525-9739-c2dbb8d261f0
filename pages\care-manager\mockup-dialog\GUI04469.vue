<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import type {
  Mo01354Items,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { OrX0148Const } from '~/components/custom-components/organisms/OrX0148/OrX0148.constants'
import { OrX0148Logic } from '~/components/custom-components/organisms/OrX0148/OrX0148.logic'
import type { OrX0148OnewayType, OrX0148Type } from '~/types/cmn/business/components/OrX0148Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 * GUI04469_印刷設定画面
 * KMD 靳先念 2025/06/25 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI04469'
// ルーティング
const routing = 'GUI04469/pinia'
// 画面物理名
const screenName = 'GUI04469'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType
const local = reactive({
  OrX0148: {
    listValue: {
      values: {
        selectedRowId: '-1',
        selectedRowIds: [],
        items: [] as Mo01354Items[],
      },
    } as Mo01354Type,
  } as OrX0148Type,
  // システムコード
  sysCd: { value: '1' } as Mo00045Type,
  // システム略称
  sysRyaku: { value: '3GK' } as Mo00045Type,
  // 法人ID
  houjinId: { value: '1' } as Mo00045Type,
  // 施設ID
  shisetuId: { value: '0' } as Mo00045Type,
  // 事業者ID
  svJigyoId: { value: '3' } as Mo00045Type,
  // 職員ID
  shokuId: { value: '1' } as Mo00045Type,
  // セクション名
  sectionName: { value: '調査員マスタメンテナンス' } as Mo00045Type,
  // 選択帳票番号
  choIndex: { value: '3' } as Mo00045Type,
})

const localOneway = reactive({
  OrX0148Oneway: {
    sysCd: '1',
    sysRyaku: '3GK',
    houjinId: '1',
    shisetuId: '1',
    svJigyoId: '1',
    shokuId: '1',
    sectionName: '調査員マスタメンテナンス',
    choIndex: '1',
    svJigyoName: '',
  } as OrX0148OnewayType,
})
/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01178' },
})

/**************************************************
 * Props
 **************************************************/
const OrX0148 = ref({ uniqueCpId: '' })
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
const { childCpIds } = useInitialize({
  cpId: 'GUI04469',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: OrX0148Const.CP_ID(0) }],
})
OrX0148Logic.initialize(childCpIds.OrX0148.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [OrX0148Const.CP_ID(0)]: OrX0148.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOrX0148 = computed(() => {
  // Or00100のダイアログ開閉状態
  return OrX0148Logic.state.get(OrX0148.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 初期データ取得
 */
async function initData() {}

/***
 * ボタン押下時の処理
 */
function OrX0148OnClick_0() {
  OrX0148Logic.state.set({
    uniqueCpId: OrX0148.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function onClickOrX0148() {
  localOneway.OrX0148Oneway.sysCd = local.sysCd.value
  localOneway.OrX0148Oneway.sysRyaku = local.sysRyaku.value
  localOneway.OrX0148Oneway.houjinId = local.houjinId.value
  localOneway.OrX0148Oneway.shisetuId = local.shisetuId.value
  localOneway.OrX0148Oneway.svJigyoId = local.svJigyoId.value
  localOneway.OrX0148Oneway.shokuId = local.shokuId.value
  localOneway.OrX0148Oneway.sectionName = local.sectionName.value
  localOneway.OrX0148Oneway.choIndex = local.choIndex.value
  OrX0148Logic.state.set({
    uniqueCpId: OrX0148.value.uniqueCpId,
    state: { isOpen: true },
  })
}

await initData()
/**************************************************
 * 印刷設定画面
 * KMD 靳先念 2025/06/04 ADD START
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="OrX0148OnClick_0()"
        >GUI04469_印刷設定
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-x-0148
    v-if="showDialogOrX0148"
    v-bind="OrX0148"
    v-model="local.OrX0148"
    :oneway-model-value="localOneway.OrX0148Oneway"
    :unique-cp-id="OrX0148.uniqueCpId"
    :parent-cp-id="pageComponent.uniqueCpId"
  />
   <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">システムコード</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.sysCd"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">システム略称</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.sysRyaku"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">法人ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.houjinId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">施設ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.shisetuId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">事業者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.svJigyoId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">職員ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.shokuId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">セクション名</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.sectionName"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">選択帳票番号</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.choIndex"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOrX0148"> GUI04469 疎通起動 </v-btn>
  </div>
</template>
