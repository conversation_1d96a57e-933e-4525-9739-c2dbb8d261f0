<script setup lang="ts">
/**
 * Or30732:有機体:［アセスメント］画面（居宅）（1）
 * GUI00794_［アセスメント］画面（居宅）（1）
 *
 * @description
 * ［アセスメント］画面（居宅）（1）
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or30737Const } from '../Or30737/Or30737.constants'
import { Or30767Const } from '../Or30767/Or30767.constants'
import { TeX0002Logic } from '../../template/TeX0002/TeX0002.logic'
import { Or35672Logic } from '../Or35672/Or35672.logic'
import { Or35672Const } from '../Or35672/Or35672.constants'
import { Or55476Logic } from '../Or55476/Or55476.logic'
import type { Or55476OneWayType, RelatedPersonSelectResInfo } from '../Or55476/Or55476.type'
import { Or55476Const } from '../Or55476/Or55476.constants'
import { Or26257Const } from '../Or26257/Or26257.constants'
import { Or26257Logic } from '../Or26257/Or26257.logic'
import { OrX0209Const } from '../OrX0209/OrX0209.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Gui00059Const } from '../Gui00059/Gui00059.constants'
import { Gui00059Logic } from '../Gui00059/Gui00059.logic'
import { TeX0002Const } from '../../template/TeX0002/TeX0002.constants'
import { Or27349Logic } from '../Or27349/Or27349.logic'
import { Or27349Const } from '../Or27349/Or27349.constants'
import type { NinteiList } from '../Or27349/Or27349.type'
import { Or27210Const } from '../Or27210/Or27210.constants'
import { Or27210Logic } from '../Or27210/Or27210.logic'
import { Or27339Logic } from '../Or27339/Or27339.logic'
import { Or27339Const } from '../Or27339/Or27339.constants'
import type { BedriddenDegreeDementiaDegreeSelectScreenDataTableData } from '../Or27339/Or27339.type'
import { Or30732Const } from './Or30732.constants'
import { ResBodyStatusCode } from '~/constants/api-constants'
import {
  useCmnCom,
  useCmnRouteCom,
  useNuxtApp,
  useScreenStore,
  useSystemCommonsStore,
} from '#imports'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'

import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { useScreenUtils } from '~/utils/useScreenUtils'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type {
  AssessmentHome1UpdateInEntity,
  AssessmentHome1UpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentHome1UpdateEntity'
import type {
  AssessmentHome1InitInfoSelectInEntity,
  AssessmentHome1InitInfoSelectOutEntity,
  FaceSheetInfo,
} from '~/repositories/cmn/entities/AssessmentHome1InitInfoSelectEntity'
import type { OrX0157OnewayType, OrX0157Type } from '~/types/cmn/business/components/OrX0157Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { Or30732OnewayType, Or30732Type } from '~/types/cmn/business/components/Or30732Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type {
  Or26257OnewayType,
  Or26257Type,
  Shokuin,
} from '~/types/cmn/business/components/Or26257Type'
import type {
  IssuesAndGoalListItem,
  OrX0209OnewayType,
  OrX0209Type,
} from '~/types/cmn/business/components/OrX0209Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { Or27349OnewayType } from '~/types/cmn/business/components/Or27349Type'
import type {
  notebookInfoSelectType,
  Or27210Type,
} from '~/types/cmn/business/components/Or27210Type'
import type { BedriddenDegreeDementiaDegreeSelectScreenType } from '~/types/cmn/business/components/Or27339Type'
import type { OrX0201OnewayType } from '~/types/cmn/business/components/OrX0201Type'
import { useValidation } from '@/utils/useValidation'
const { byteLength } = useValidation()

const { getChildCpBinds } = useScreenUtils()

const $log = useNuxtApp().$log as DebugLogPluginInterface
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  onewayModelValue: Or30732OnewayType
  parentUniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const or55476 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' })
const or27349 = ref({ uniqueCpId: '' })
const or27210 = ref({ uniqueCpId: '' })
const or27339 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })
const OrX0209 = ref({ uniqueCpId: '' })
const gui00059 = ref({ uniqueCpId: '' })

// route共有情報
const cmnRouteCom = useCmnRouteCom()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const local = reactive({
  or26257: {
    shokuin: {
      chkShokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
    } as Shokuin,
  } as Or26257Type,
  // 課題と目標リスト
  issuesAndGoalsList: {
    items: [] as IssuesAndGoalListItem[],
  } as OrX0209Type,
  // 手帳情報選択
  or27210: {
    notebookInfoData: {
      getYmd: '',
      toukyuuKnj: '',
      bikoKnj: '',
      teidoKnj: '',
      gappeiShougaiKnj: '',
      tKindKnj: '',
      tTokyu: '',
      tKindCd: '',
    },
  } as Or27210Type,
  // 寝たきり度・認知症度選択
  or27339: '',
  // 入力支援ダイアログ
  or51775: {
    modelValue: '',
  } as Or51775Type,
  // 共通情報
  commonInfo: {} as TeX0002Type,
  consultationFormRadioList: [] as CodeType[],
  genderRadioList: [] as CodeType[],
  nursingCareInsuranceRadioList: [] as CodeType[],
  burdenRadioList: [] as CodeType[],
  userBurdenRadioList: [] as CodeType[],
  notebookRadioList: [] as CodeType[],
  medicalBenefitsCardRadioList: [] as CodeType[],
  handycapSelectList: [] as CodeType[],
  dementiaEverydayLifeIndependenceLevelRadioList: [] as CodeType[],
  seniorEverydayLifeIndependenceLevelRadioList: [] as CodeType[],
  disabilityNotebookSelectList: [] as CodeType[],
  welfareNotebookSelectList: [] as CodeType[],
})

const localOneway = reactive({
  // ページタイトル
  orX0201Oneway: {
    /** タイトル名 */
    title: t('label.face-sheet'),
    tabNo: Or30732Const.DEFAULT.TAB_ID,
  } as OrX0201OnewayType,
  // 相談受付
  section0: {
    // 備考１
    mo00046Oneway: {
      itemLabel: t('label.memo'),
      maxlength: '100',
      width: '600px',
      showItemLabel: true,
      rows: '2',
      maxRows: '2',
      noResize: true,
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(100)],
    } as Mo00046OnewayType,
    // 初回相談受付者
    orX0157Oneway: {
      inputMode: 'TextOnly',
      showEditBtnFlg: true,
      inputReadonly: true,
      text: {
        orX0157InputOneway: {
          disabled: false,
          isVerticalLabel: true,
          showItemLabel: true,
          itemLabel: t('label.first-time-consultation-reception'),
          customClass: new CustomClass({
            outerClass: 'background-transparent',
            labelClass: 'mb-1',
            itemStyle: 'background: rgb(var(--v-theme-surface)) !important;',
          }),
          width: '160px',
        },
      },
    } as OrX0157OnewayType,
    // 相談受付日
    mo00020Oneway: {
      itemLabel: '',
      showItemLabel: false,
      hideDetails: true,
      maxlength: '10',
      disabled: false,
      width: '144',
    } as Mo00020OnewayType,
    mo00020Type: {
      value: '',
      mo01343: {
        value: '',
        mo00024: {
          isOpen: false,
        } as Mo00024Type,
      },
    } as Mo00020Type,
    // 相談受付ラジオ
    mo00039Oneway: {
      // H21/４改訂版（作成日が2018年3月31日以前（平成30年区分=0））の場合、「受付」
      // H21/４改訂版（作成日が2018年4月1日以降（平成30年区分=1））OR  R3/４改訂版の場合、「相談受付」
      name: Or30737Const.CP_ID(0),
      itemLabel: t('label.consultation-reception'),
      showItemLabel: true,
      hideDetails: true,
      customClass: new CustomClass({ outerClass: 'd-flex flex-column', labelClass: 'pa-1 mb-1' }),
    } as Mo00039OnewayType,
    mo00039ModelValue: -1,
    // その他
    mo00045Oneway: {
      itemLabel: '',
      maxlength: '10',
      width: '232px',
      isVerticalLabel: false,
      customClass: new CustomClass({ outerClass: 'ma-0' }),
      rules: [byteLength(10)],
    } as Mo00045OnewayType,
    mo00045: {
      value: '',
    } as Mo00045Type,
    // 職員検索画面
    or26257Oneway: {
      // システム略称
      sysCdKbn: systemCommonsStore.getSystemAbbreviation,
      // アカウント設定
      secAccountAllFlg: '-',
      // 適用事業所ＩＤリスト
      svJigyoIdList: [],
      // 職員ID
      shokuinId: '',
      // システムコード
      gsysCd: '',
      // モード
      selectMode: Or26257Const.DEFAULT.SELECT_MODE_12,
      // 基準日
      kijunYmd: systemCommonsStore.getSystemDate,
      // 事業所ID
      defSvJigyoId: '',
      // フィルターフラグ
      filterDwFlg: '',
      // 雇用状態
      koyouState: '-',
      // 地域フラグ
      areaFlg: '-',
      // 表示名称リスト
      hyoujiColumnList: [],
      // 未設定フラグ
      misetteiFlg: Or26257Const.DEFAULT.MISETTEI_FLG_OK,
      // 他職員参照権限
      otherRead: '',
      // 中止フラグ
      refStopFlg: '-',
      // 処理フラグ
      syoriFlg: '',
      // メニュー１ID
      menu1Id: '',
      // 件数フラグ
      kensuFlg: '-',
      // 職員IDリスト
      shokuinIdList: [],
    } as Or26257OnewayType,
    // 相談受付選択し
    radioItems: [] as CodeType[],
  },
  // 緊急連絡先
  section1: {
    // 氏名
    mo00045Oneway_1: {
      itemLabel: t('label.name'),
      maxlength: '20',
      width: '160px',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(20)],
    } as Mo00045OnewayType,
    // 性別
    mo00039Oneway: {
      name: '',
      itemLabel: t('label.gender'),
      showItemLabel: true,
      hideDetails: true,
      customClass: new CustomClass({ labelClass: 'mb-3' }),
    } as Mo00039OnewayType,
    // 性別選択し
    sexRadioItems: [] as CodeType[],
    // 年齡
    mo00045Oneway_2: {
      itemLabel: t('label.age'),
      maxlength: '3',
      width: '96px',
      appendLabel: t('label.year-old'),
      type: 'number',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00045OnewayType,
    // 本人との続柄
    mo00040Oneway: {
      itemLabel: t('label.person-relationship'),
      items: [],
      itemTitle: 'label',
      itemValue: 'value',
      hideDetails: true,
      width: '160px',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00040OnewayType,
    // 住所
    mo00045Oneway_3: {
      itemLabel: t('label.address'),
      maxlength: '60',
      width: '535px',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(60)],
    } as Mo00045OnewayType,
    // 電話番号
    mo00045Oneway_4: {
      itemLabel: t('label.telno'),
      maxlength: '14',
      width: '160px',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(14)],
    } as Mo00045OnewayType,
    // 携帯番号
    mo00045Oneway_5: {
      itemLabel: t('label.cell-number'),
      maxlength: '80',
      width: '160px',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(14)],
    } as Mo00045OnewayType,
    // 関係者選択画面
    or55746Oneway: {
      userId: systemCommonsStore.getUserId,
      telCellFlg: Or30732Const.DEFAULT.TEL_FLG_3,
      createYmd: '',
      kinouId: '',
    } as Or55476OneWayType,
  },
  // 相談者
  section2: {
    // 氏名
    mo00045Oneway_1: {
      itemLabel: t('label.name'),
      maxlength: '20',
      width: '160px',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(20)],
    } as Mo00045OnewayType,
    // 性別
    mo00039Oneway: {
      name: '',
      itemLabel: t('label.gender'),
      showItemLabel: true,
      hideDetails: true,
      customClass: new CustomClass({ labelClass: 'mb-3' }),
    } as Mo00039OnewayType,
    // 性別選択し
    sexRadioItems: [] as CodeType[],
    // 年齡
    mo00045Oneway_2: {
      itemLabel: t('label.age'),
      maxlength: '3',
      width: '96px',
      appendLabel: t('label.year-old'),
      type: 'number',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(3)],
    } as Mo00045OnewayType,
    // 本人との続柄
    mo00040Oneway: {
      itemLabel: t('label.person-relationship'),
      items: [],
      itemTitle: 'label',
      itemValue: 'value',
      hideDetails: true,
      width: '160px',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00040OnewayType,
    // 住所
    mo00045Oneway_3: {
      itemLabel: t('label.address'),
      maxlength: '60',
      width: '535px',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(60)],
    } as Mo00045OnewayType,
    // 電話番号
    mo00045Oneway_4: {
      itemLabel: t('label.telno'),
      maxlength: '14',
      width: '160px',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(14)],
    } as Mo00045OnewayType,
    // 携帯番号
    mo00045Oneway_5: {
      itemLabel: t('label.cell-number'),
      maxlength: '14',
      width: '160px',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(14)],
    } as Mo00045OnewayType,
    // 関係者選択画面
    or55746Oneway: {
      userId: systemCommonsStore.getUserId,
      telCellFlg: Or30732Const.DEFAULT.TEL_FLG_3,
      createYmd: '',
      kinouId: '',
    } as Or55476OneWayType,
  },
  // 相談経路
  section3: {
    // 相談者入力
    mo00046oneway: {
      showItemLabel: false,
      maxLength: 188,
      rows: '2',
      maxRows: '2',
      noResize: true,
      rules: [byteLength(188)],
    } as Mo00046OnewayType,
  },
  // 居宅サービス計画作成依頼の届出
  section4: {
    // 依頼年月日
    mo00020Oneway: {
      itemLabel: t('label.reporting-date'),
      showItemLabel: true,
      isVerticalLabel: true,
      hideDetails: true,
      maxlength: '10',
      disabled: false,
      width: '144',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00020OnewayType,
  },
  // 相談内容
  section5: {
    // 本人ラベル
    mo00615Oneway_1: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.person'),
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00615OnewayType,
    // 家族 ラベル
    mo00615Oneway_2: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.caregiver-family'),
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00615OnewayType,
    // 本人
    orX0156Oneway_1: {
      persistentCounter: false,
      showItemLabel: false,
      maxlength: '3024',
      rows: '5',
      maxRows: '5',
      noResize: true,
      showDividerLineFlg: false,
      showEditBtnFlg: true,
      rules: [byteLength(3024)],
    } as OrX0156OnewayType,
    // 家族
    orX0156Oneway_2: {
      persistentCounter: false,
      showItemLabel: false,
      maxlength: '416',
      rows: '5',
      maxRows: '5',
      noResize: true,
      showDividerLineFlg: false,
      showEditBtnFlg: true,
      rules: [byteLength(416)],
    } as OrX0156OnewayType,
  },
  section6: {
    // 生活の経過
    mo00046Oneway: {
      persistentCounter: false,
      showItemLabel: false,
      maxlength: '1026',
      rows: '3',
      maxRows: '3',
      noResize: true,
      showDividerLineFlg: false,
      showEditBtnFlg: true,
      rules: [byteLength(1026)],
    } as OrX0156OnewayType,
  },
  section7: {
    // 介護保険
    mo00615Oneway_1: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.nursing-care-insurance'),
      customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'ma-1' }),
    } as Mo00615OnewayType,
    // 利用者負担割合 ラベル
    mo00615Oneway_2: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.user-burden-ratio'),
      customClass: new CustomClass({ outerClass: 'mr-4', labelClass: 'ma-1' }),
    } as Mo00615OnewayType,
    // 利用者負担割合
    mo00039Oneway_1: {
      name: Or30767Const.CP_ID(0),
      showItemLabel: false,
      hideDetails: true,
      inline: true,
    } as Mo00039OnewayType,
    // 後期高齢者医療保険（75歳以上）
    mo00615Oneway_3: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.senior-medical-care'),
      customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'ma-1' }),
    } as Mo00615OnewayType,
    // 一部負担金ラベル
    mo00615Oneway_4: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.burden-money'),
      customClass: new CustomClass({ outerClass: 'mr-4', labelClass: 'ma-1' }),
    } as Mo00615OnewayType,
    // 一部負担金
    mo00039Oneway_2: {
      name: Or30767Const.CP_ID(0),
      showItemLabel: false,
      hideDetails: true,
    } as Mo00039OnewayType,
    // 高額介護サービス該当
    mo00615Oneway_5: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.nursing-care-service-cost-corresponding'),
      customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'ma-1' }),
    } as Mo00615OnewayType,
    // 利用者負担 ラベル
    mo00615Oneway_6: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.user-burden'),
      customClass: new CustomClass({ outerClass: 'mr-4', labelClass: 'ma-1' }),
    } as Mo00615OnewayType,
    // 利用者負担 ラジオ
    mo00039Oneway_3: {
      name: Or30767Const.CP_ID(0),
      showItemLabel: false,
      hideDetails: true,
      inline: true,
    } as Mo00039OnewayType,
    // 利用者負担割合 選択し
    radioItems_1: [] as CodeType[],
    // 一部負担金 選択し
    radioItems_2: [] as CodeType[],
    // 利用者負担 選択し
    radioItems_3: [] as CodeType[],
  },
  // 要介護認定
  section8: {
    // 済 セレクト
    mo00040Oneway_1: {
      itemLabel: t('label.completed'),
      items: [],
      itemTitle: 'label',
      itemValue: 'value',
      hideDetails: true,
      width: '166px',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00040OnewayType,
    // 未 セレクト
    mo00040Oneway_2: {
      itemLabel: t('label.assessment-home-1-prospect'),
      items: [],
      itemTitle: 'label',
      itemValue: 'value',
      hideDetails: true,
      width: '166px',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00040OnewayType,
    // 認定日
    mo00020Oneway: {
      itemLabel: t('label.certification-date'),
      showItemLabel: true,
      isVerticalLabel: true,
      hideDetails: true,
      maxlength: '10',
      disabled: false,
      width: '144',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00020OnewayType,
    // 認定情報選択画面
    or27349Oneway: {
      startYmd1: '',
    } as Or27349OnewayType,
  },
  section9: {
    // 有無ラジオ
    mo00039Oneway: {
      name: '',
      itemLabel: '',
      showItemLabel: true,
      hideDetails: true,
      customClass: new CustomClass({ outerClass: 'ma-0' }),
    } as Mo00039OnewayType,
    // 等級
    mo00045Oneway: {
      itemLabel: t('label.grade'),
      maxlength: '5',
      width: '74px',
      appendLabel: t('label.assessment-home-1-type'),
      type: 'string',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(5)],
    } as Mo00045OnewayType,
    // セレクト
    mo00040Oneway: {
      itemLabel: '',
      items: [],
      itemTitle: 'label',
      itemValue: 'value',
      hideDetails: true,
      width: '88px',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00040OnewayType,
    // 備考
    mo00046Oneway: {
      maxlength: '40',
      width: '387px',
      noResize: true,
      rows: '2',
      maxRows: '2',
      isVerticalLabel: true,
      showItemLabel: false,
      rules: [byteLength(40)],
    } as Mo00046OnewayType,
    // 交付日
    mo00020Oneway: {
      itemLabel: t('label.issue-date'),
      showItemLabel: true,
      isVerticalLabel: true,
      hideDetails: true,
      maxlength: '10',
      disabled: false,
      width: '144',
    } as Mo00020OnewayType,
    // 有無ラジオ選択し
    radioItems: [] as CodeType[],
    // 手帳情報選択
    or27210Oneway: {
      userId: '',
      getYmd: '',
      tkbn: '',
    } as notebookInfoSelectType,
  },
  section10: {
    // 有無ラジオ
    mo00039Oneway: {
      name: '',
      itemLabel: '',
      showItemLabel: true,
      hideDetails: true,
      customClass: new CustomClass({ outerClass: 'ma-0' }),
    } as Mo00039OnewayType,
    // セレクト
    mo00040Oneway: {
      itemLabel: t('label.degree'),
      showItemLabel: true,
      items: [],
      itemTitle: 'label',
      itemValue: 'value',
      hideDetails: true,
      width: '188px',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00040OnewayType,
    // 備考
    mo00046Oneway: {
      maxlength: '64',
      width: '387px',
      noResize: true,
      rows: '2',
      maxRows: '2',
      isVerticalLabel: true,
      showItemLabel: false,
      rules: [byteLength(64)],
    } as Mo00046OnewayType,
    // 交付日
    mo00020Oneway: {
      itemLabel: t('label.issue-date'),
      showItemLabel: true,
      isVerticalLabel: true,
      hideDetails: true,
      maxlength: '10',
      disabled: false,
      width: '144',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00020OnewayType,
    // 有無ラジオ選択し
    radioItems: [] as CodeType[],
  },
  section11: {
    // 有無ラジオ
    mo00039Oneway: {
      name: '',
      itemLabel: '',
      showItemLabel: true,
      hideDetails: true,
      customClass: new CustomClass({ outerClass: 'ma-0' }),
    } as Mo00039OnewayType,
    // セレクト
    mo00040Oneway: {
      itemLabel: t('label.grade'),
      showItemLabel: true,
      items: [],
      itemTitle: 'label',
      itemValue: 'value',
      hideDetails: true,
      width: '188px',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00040OnewayType,
    // 備考
    mo00046Oneway: {
      maxlength: '64',
      width: '387px',
      noResize: true,
      rows: '2',
      maxRows: '2',
      isVerticalLabel: true,
      showItemLabel: false,
      rules: [byteLength(64)],
    } as Mo00046OnewayType,
    // 交付日
    mo00020Oneway: {
      itemLabel: t('label.issue-date'),
      showItemLabel: true,
      isVerticalLabel: true,
      hideDetails: true,
      maxlength: '10',
      disabled: false,
      width: '144',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00020OnewayType,
    // 有無ラジオ選択し
    radioItems: [] as CodeType[],
  },
  // 障碍福祉サービス
  section12: {
    // 障碍福祉サービス受給者証の有無
    mo00039Oneway_1: {
      name: '',
      itemLabel: t('label.welfare-service-benefits-card-presence-or-absence'),
      showItemLabel: true,
    } as Mo00039OnewayType,
    // 自立支援医療受給者証の有無
    mo00039Oneway_2: {
      name: '',
      itemLabel: t('label.independence-support-medical-benefits-card-presence-or-absence'),
      showItemLabel: true,
    } as Mo00039OnewayType,
    // 障害支援区分
    mo00045Oneway: {
      itemLabel: t('label.handycap-support-category'),
      maxlength: '20',
      width: '284px',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(20)],
    } as Mo00045OnewayType,
    // 有無ラジオ選択し
    radioItems: [] as CodeType[],
  },
  // 日常生活自立度
  section13: {
    // 障害高齢者ラジオ
    mo00039Oneway_1: {
      name: '',
      itemLabel: t('label.handycap-senior'),
      showItemLabel: true,
      hideDetails: true,
    } as Mo00039OnewayType,
    // 障害高齢者 判定者
    orX0157Oneway_1: {
      inputMode: 'TextOnly',
      showEditBtnFlg: true,
      text: {
        orX0157InputOneway: {
          disabled: false,
          isVerticalLabel: true,
          showItemLabel: true,
          itemLabel: t('label.judge'),
          customClass: new CustomClass({
            outerClass: 'background-transparent ma-0',
            labelClass: 'mb-1',
            itemClass: 'v-field-no-height',
            itemStyle: 'background: rgb(var(--v-theme-surface)) !important;',
          }),
          width: '226px',
          maxLength: '44',
          rules: [byteLength(44)],
        },
      },
    } as OrX0157OnewayType,
    // 障害高齢者 機関名
    mo00045Oneway_1: {
      itemLabel: `${t('label.institution')}`,
      maxlength: '48',
      width: '226px',
      type: 'string',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(48)],
    } as Mo00045OnewayType,
    // 障害高齢者 判定日
    mo00020Oneway_1: {
      itemLabel: t('label.judgement-date'),
      showItemLabel: true,
      isVerticalLabel: true,
      hideDetails: true,
      maxlength: '10',
      disabled: false,
      width: '144',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00020OnewayType,
    // 認知症度ラジオ
    mo00039Oneway_2: {
      name: '',
      itemLabel: t('label.dementia'),
      showItemLabel: true,
      hideDetails: true,
    } as Mo00039OnewayType,
    radioItems_1: [] as CodeType[],
    radioItems_2: [] as CodeType[],
    // 認知症度 判定者
    orX0157Oneway_2: {
      inputMode: 'TextOnly',
      showEditBtnFlg: true,
      text: {
        orX0157InputOneway: {
          disabled: false,
          isVerticalLabel: true,
          showItemLabel: true,
          itemLabel: t('label.judge'),
          customClass: new CustomClass({
            outerClass: 'background-transparent ma-0',
            labelClass: 'mb-1',
            itemClass: 'v-field-no-height',
            itemStyle: 'background: rgb(var(--v-theme-surface)) !important;',
          }),
          width: '226px',
          maxLength: '44',
          rules: [byteLength(44)],
        },
      },
    } as OrX0157OnewayType,
    // 認知症度 機関名
    mo00045Oneway_2: {
      itemLabel: `${t('label.institution')}`,
      maxlength: '48',
      width: '226px',
      type: 'string',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(48)],
    } as Mo00045OnewayType,
    // 認知症度 判定日
    mo00020Oneway_2: {
      itemLabel: t('label.judgement-date'),
      showItemLabel: true,
      isVerticalLabel: true,
      hideDetails: true,
      maxlength: '10',
      disabled: false,
      width: '144',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00020OnewayType,
    // 寝たきり度・認知症度選択
    or27339Oneway: {
      svJigyoId: '',
      shisetuId: '',
      userId: '',
      houjinId: '',
      sc1Id: '',
      certificationFlg: '',
      kinScreenKbn: '',
    } as BedriddenDegreeDementiaDegreeSelectScreenType,
  },
  section14: {
    // 備考２
    mo00045Oneway: {
      itemLabel: '',
      maxlength: '20',
      width: '317px',
      isVerticalLabel: true,
      customClass: new CustomClass({ labelClass: 'mb-1' }),
      rules: [byteLength(20)],
    } as Mo00045OnewayType,
    // アセスメント実施日（初回）
    mo00020Oneway: {
      itemLabel: t('label.assessment-implementation-date-first-time'),
      hideDetails: true,
      maxlength: '10',
      disabled: false,
      width: '144',
      customClass: new CustomClass({ labelClass: 'mb-1' }),
    } as Mo00020OnewayType,
  },
  // 解決すべき課題と目標一覧
  issuesAndGoalsListOneway: {
    /** アセスメント名 */
    assessmentName:
      t('label.assessment-home-issuesAndGoals-no-1') +
      '\r\n' +
      t('label.assessment-home-issuesAndGoals-name-1'),
  } as OrX0209OnewayType,
  // 入力支援ダイアログ
  or51775Oneway: {
    screenId: 'GUI00794',
    mode: Or51775Const.CARE_MANAGER_PROCESS_MODE,
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg,
  } as Or51775OnewayType,
})

const isLoading = ref(false)

// 対象セクションID
const currentSectionId = ref('')

// 平成30年区分（作成日が2018年4月1日以降フラグ）
const heisei30Kbn = ref(true)

// フォーカス中項目ID
const focusItemId = ref('')

// コンポーネント表示フラグ
const isComponentVisible = ref(true)

// フォーカス中項目ID
const updateKbn = ref(TeX0002Const.DEFAULT.UPDATE_KBN_N)

// 職員検索ダイアログ表示フラグ
const showDialogOr26257 = computed(() => {
  // Or26257のダイアログ開閉状態
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})

// 関係者選択ダイアログ表示フラグ
const showDialogOr55476 = computed(() => {
  // Or55476のダイアログ開閉状態
  return Or55476Logic.state.get(or55476.value.uniqueCpId)?.isOpen ?? false
})

// 認定情報選択ダイアログ表示フラグ
const showDialogOr27349 = computed(() => {
  return Or27349Logic.state.get(or27349.value.uniqueCpId)?.isOpen ?? false
})

// 手帳情報選択ダイアログ表示フラグ
const showDialogOr27210 = computed(() => {
  // Or27210のダイアログ開閉状態
  return Or27210Logic.state.get(or27210.value.uniqueCpId)?.isOpen ?? false
})

// 寝たきり度・認知症度選択ダイアログ表示フラグ
const showDialogOr27339 = computed(() => {
  // Or27339のダイアログ開閉状態
  return Or27339Logic.state.get(or27339.value.uniqueCpId)?.isOpen ?? false
})

// 入力支援ダイアログ表示フラグ(入力支援)
const showDialogOr51775 = computed(() => {
  // Or27761のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogGui00059 = computed(() => {
  // Gui00059のダイアログ開閉状態
  return Gui00059Logic.state.get(gui00059.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or30732Type>({
  cpId: Or30732Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or30732Type> }

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or55476Const.CP_ID(0)]: or55476.value,
  [Or26257Const.CP_ID(0)]: or26257.value,
  [Or27349Const.CP_ID(0)]: or27349.value,
  [Or27210Const.CP_ID(0)]: or27210.value,
  [Or27339Const.CP_ID(0)]: or27339.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [OrX0209Const.CP_ID(0)]: OrX0209.value,
  [Gui00059Const.CP_ID(0)]: gui00059.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  $log.debug(`★[onMounted] [cpId]${Or30732Const.CP_ID(0)} [uId]${props.uniqueCpId}`)

  // コントロール初期化
  initControls()

  // コードマスタデータ取得
  await initCodes()
})

/**
 *  画面共通情報を取得
 *
 * @param event - イベント
 */
function getCommonInfo(event: Record<string, boolean>) {
  const commonInfo = TeX0002Logic.data.get(props.parentUniqueCpId)
  console.log('commonInfo', commonInfo)

  if (commonInfo) {
    local.commonInfo.kikanKanriFlg = commonInfo.kikanKanriFlg
    local.commonInfo.ninteiFormF = commonInfo.ninteiFormF
    local.commonInfo.syubetuId = commonInfo.syubetuId
    local.commonInfo.historyUpdateKbn = commonInfo.historyUpdateKbn
    local.commonInfo.historyModifiedCnt = commonInfo.historyModifiedCnt
    local.commonInfo.activeTabId = commonInfo.activeTabId
    local.commonInfo.jigyoId = commonInfo.jigyoId
    local.commonInfo.jigyoKnj = commonInfo.jigyoKnj
    local.commonInfo.shisetuId = commonInfo.shisetuId
    local.commonInfo.houjinId = commonInfo.houjinId
    local.commonInfo.userId = commonInfo.userId
    local.commonInfo.sc1Id = commonInfo.sc1Id
    local.commonInfo.sc1No = commonInfo.sc1No
    local.commonInfo.periodStartYmd = commonInfo.periodStartYmd
    local.commonInfo.periodEndYmd = commonInfo.periodEndYmd
    local.commonInfo.gdlId = commonInfo.gdlId
    local.commonInfo.historyNo = commonInfo.historyNo
    local.commonInfo.createUserId = commonInfo.createUserId
    local.commonInfo.createUserName = commonInfo.createUserName
    local.commonInfo.createYmd = commonInfo.createYmd
    local.commonInfo.copyUserId = commonInfo.copyUserId
    local.commonInfo.copyCreateYmd = commonInfo.copyCreateYmd
    local.commonInfo.copyNinteiFormF = commonInfo.copyNinteiFormF
    local.commonInfo.copySc1Id = commonInfo.copySc1Id
    local.commonInfo.copyGdlId = commonInfo.copyGdlId
    local.commonInfo.deleteKbn = commonInfo.deleteKbn
    local.commonInfo.issuesAndGoalsList = commonInfo.issuesAndGoalsList
  }

  // 解決すべき課題と目標一覧パラメータ設定
  if (!event.saveEventFlg) {
    localOneway.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
    localOneway.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
    localOneway.issuesAndGoalsListOneway.assNo = local.commonInfo.activeTabId
    localOneway.issuesAndGoalsListOneway.userId = local.commonInfo.userId

    local.issuesAndGoalsList.items =
      local.commonInfo.issuesAndGoalsList ?? ([] as IssuesAndGoalListItem[])
  }

  // 画面制御項目を設定
  setFormItems()
}

/**
 *  複写画面から共通情報を取得
 */
function getCommonInfoFromCopy() {
  const commonInfo = Or35672Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    /** 改定フラグ */
    local.commonInfo.ninteiFormF = commonInfo.params?.ninteiFormF
    /** 選択中タブID */
    local.commonInfo.activeTabId = commonInfo.params?.activeTabId
    /** 事業所ID */
    local.commonInfo.jigyoId = commonInfo.params?.jigyoId
    /** 法人ID */
    local.commonInfo.houjinId = commonInfo.params?.houjinId
    /** 施設ID */
    local.commonInfo.shisetuId = commonInfo.params?.shisetuId
    /** 利用者ID */
    local.commonInfo.userId = commonInfo.params?.userId
    /** 計画対象期間ID */
    local.commonInfo.sc1Id = commonInfo.params?.sc1Id
    /** アセスメントID */
    local.commonInfo.gdlId = commonInfo.params?.gdlId
    /** 作成者ID */
    local.commonInfo.createUserId = commonInfo.params?.createUserId
    /** 作成日 */
    local.commonInfo.createYmd = commonInfo.params?.createdYmd
    /** 種別ID */
    local.commonInfo.syubetuId = commonInfo.params?.syubetsuId
    /** 期間管理フラグ */
    local.commonInfo.kikanKanriFlg = commonInfo.params?.kikanFlg
    /** 課題と目標リスト */
    local.commonInfo.issuesAndGoalsList = commonInfo.params?.issuesAndGoalsList
  }

  // 解決すべき課題と目標一覧パラメータ設定
  localOneway.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
  localOneway.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
  localOneway.issuesAndGoalsListOneway.assNo = local.commonInfo.activeTabId
  localOneway.issuesAndGoalsListOneway.userId = local.commonInfo.userId

  local.issuesAndGoalsList.items =
    local.commonInfo.issuesAndGoalsList ?? ([] as IssuesAndGoalListItem[])

  // 画面制御項目を設定
  setFormItems()
}

/**
 *  画面項目を設定
 */
function setFormItems() {
  // 平成30年区分を判断
  if (local.commonInfo.createYmd) {
    if (parseInt(local.commonInfo.createYmd.replaceAll('/', '') ?? '0') >= 20180401) {
      heisei30Kbn.value = true
    } else {
      heisei30Kbn.value = false
    }
  } else {
    heisei30Kbn.value = false
  }

  // H21/４改訂版（作成日が2018年3月31日以前（平成30年区分=0））の場合
  if (local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21 && !heisei30Kbn.value) {
    // 受付
    localOneway.section0.mo00039Oneway.itemLabel = t('label.reception')
    // 相談受付者
    if (localOneway.section0.orX0157Oneway.text) {
      localOneway.section0.orX0157Oneway.text.orX0157InputOneway.itemLabel = t(
        'label.person-of-consultation-reception'
      )
    }
    // 家族及び介護者
    localOneway.section5.mo00615Oneway_2.itemLabel = t('label.family-and-caregiver')
    // 障害程度（支援）区分→
    localOneway.section12.mo00045Oneway.itemLabel = t('label.handycap-support-category-right')
    // 寝たきり
    localOneway.section13.mo00039Oneway_1.itemLabel = t('label.bedridden')
  } else {
    // 相談受付
    localOneway.section0.mo00039Oneway.itemLabel = t('label.consultation-reception')
    // 初回相談受付日
    if (localOneway.section0.orX0157Oneway.text) {
      localOneway.section0.orX0157Oneway.text.orX0157InputOneway.itemLabel = t(
        'label.first-time-consultation-reception'
      )
    }
    // 介護者・家族
    localOneway.section5.mo00615Oneway_2.itemLabel = t('label.caregiver-family')
    // 障害程度（支援）区分
    localOneway.section12.mo00045Oneway.itemLabel = t('label.handycap-support-category')
    // 障害高齢者
    localOneway.section13.mo00039Oneway_1.itemLabel = t('label.handycap-senior')
  }
}

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 利用者一覧における利用者の変更を監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue === undefined) {
      return
    }
  },
  { deep: true }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0002Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    // 画面共通情報を取得
    getCommonInfo(newValue)

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or30732Const.DEFAULT.TAB_ID) {
      return
    }
    if (newValue.isRefresh) {
      isComponentVisible.value = true

      // 画面情報再取得
      await reload()

      // 次の描画までまつ（ターゲットエレメントのサイズが取得できるため）
      await nextTick()
    }
    if (newValue.isCreateDateChanged) {
      // 保存ボタンが押下された場合、保存処理を実行する
      // 改訂バージョンをチェック
    }

    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      if (await _save()) {
        setTeX0002State({ isSaveCompleted: true })
      }
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      createNew()
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      isComponentVisible.value = false

      updateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_D

      if (local.commonInfo.deleteKbn === TeX0002Const.DEFAULT.DELETE_PROCESS_KBN_TAB) {
        localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_DELETE_TAB
      } else if (local.commonInfo.deleteKbn === TeX0002Const.DEFAULT.DELETE_PROCESS_KBN_ALL) {
        localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_DELETE_ALL
      }
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      await _copy()
    }
  }
)

/**
 * 初回相談受付者変更ワッチャー
 */
watch(
  () => local.or26257,
  (newValue) => {
    if (newValue?.shokuin) {
      refValue.value.section0.soudanTantoId = newValue?.shokuin.chkShokuId
      refValue.value.section0.shokuinName.value =
        newValue?.shokuin.shokuin1Knj + ' ' + newValue?.shokuin.shokuin2Knj
    }
  },
  { deep: true }
)

/**
 * 手帳情報選択変更ワッチャー
 */
watch(
  () => local.or27210,
  (newValue) => {
    switch (currentSectionId.value) {
      case Or30732Const.DEFAULT.SECTION_ID_9:
        // 等級
        refValue.value.section9.sinshouShu.value = newValue.notebookInfoData.tKindCd
        // 等級 セレクト
        refValue.value.section9.tTokyu1.modelValue = newValue.notebookInfoData.toukyuuKnj
        // 備考
        refValue.value.section9.biko1Knj.value = newValue.notebookInfoData.bikoKnj
        // 判定日
        refValue.value.section9.get1Ymd.value = newValue.notebookInfoData.getYmd
        break
      case Or30732Const.DEFAULT.SECTION_ID_10:
        // 程度 セレクト
        refValue.value.section10.tTokyu2.modelValue = newValue.notebookInfoData.tTokyu
        // 備考
        refValue.value.section10.biko2Knj.value = newValue.notebookInfoData.bikoKnj
        // 判定日
        refValue.value.section10.get2Ymd.value = newValue.notebookInfoData.getYmd
        break
      case Or30732Const.DEFAULT.SECTION_ID_11:
        // 等級 セレクト
        refValue.value.section11.tTokyu3.modelValue = newValue.notebookInfoData.tTokyu
        // 備考
        refValue.value.section11.biko3Knj.value = newValue.notebookInfoData.bikoKnj
        // 判定日
        refValue.value.section11.get3Ymd.value = newValue.notebookInfoData.getYmd
        break
    }
  },
  { deep: true }
)

/**************************************************
 * 関数
 **************************************************/
/**
 * Or35672ステート発火
 *
 * @param state - ステート
 */
function setOr35672State(state: Record<string, boolean>) {
  Or35672Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 * TeX0002ステート発火
 *
 * @param state - ステート
 */
function setTeX0002State(state: Record<string, boolean>) {
  TeX0002Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 *  コントロール初期化
 */
function initControls() {
  // 課題と目標一覧スクロール先のIDを設定
  localOneway.orX0201Oneway.scrollToUniqueCpId = OrX0209.value.uniqueCpId

  // 複写の場合、編集アイコンボタンを非表示にする‘
  if (props.onewayModelValue?.mode === Or30732Const.DEFAULT.MODE_COPY) {
    // 解決すべき課題と目標一覧 ↓
    localOneway.orX0201Oneway.isMoveToBottomShowFlg = false
    // 初回相談受付者
    localOneway.section0.orX0157Oneway.showEditBtnFlg = false
    // 相談内容 本人
    localOneway.section5.orX0156Oneway_1.showEditBtnFlg = false
    // 相談内容 介護者・家族
    localOneway.section5.orX0156Oneway_2.showEditBtnFlg = false
    // これまでの生活の経過
    localOneway.section6.mo00046Oneway.showEditBtnFlg = false
    // 日常生活自立度 障害高齢者 判定者
    localOneway.section13.orX0157Oneway_1.showEditBtnFlg = false
    // 日常生活自立度 認知症 判定者
    localOneway.section13.orX0157Oneway_2.showEditBtnFlg = false
    // 解決すべき課題と目標一覧
    localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_COPY
  }
}

/**
 *  画面初期情報取得
 */
async function getInitDataInfo() {
  const param: AssessmentHome1InitInfoSelectInEntity = {
    /** 計画期間ID */
    sc1Id: local.commonInfo.sc1Id ?? '',
    /** 履歴ID */
    gdlId: local.commonInfo.gdlId ?? '',
    /** 改定フラグ */
    ninteiFlg: local.commonInfo.ninteiFormF ?? '',
    /** 履歴作成日 */
    kijunbiYmd: local.commonInfo.createYmd ?? '',
    /** 事業所ID */
    svJigyoId: local.commonInfo.jigyoId ?? '',
    /** 利用者ID */
    userid: local.commonInfo.createUserId ?? '',
    /** 施設ID */
    shisetuId: local.commonInfo.shisetuId ?? '',
    /** 法人ID */
    houjinId: local.commonInfo.houjinId ?? '',
  }

  const res: AssessmentHome1InitInfoSelectOutEntity = await ScreenRepository.select(
    'assessmentHome1InitInfoSelect',
    param
  )
  // 画面情報を設定
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    setFormData(res)
  } else {
    if (props.onewayModelValue?.mode === Or30732Const.DEFAULT.MODE_COPY) {
      setOr35672State({ noData: true })
    }
  }
}
/**
 *  コントロール初期化
 */
async function reload() {
  try {
    isLoading.value = true

    let start: number = performance.now()
    clearData()
    let end: number = performance.now()
    console.log(`initCodes実行時間: ${(end - start).toFixed(3)} ミリ秒`)

    start = performance.now()
    // 汎用コードマスタからコード情報を取得
    await initCodes()

    // 画面初期情報取得
    await getInitDataInfo()

    if (localOneway.issuesAndGoalsListOneway.mode !== OrX0209Const.DEFAULT.MODE_COPY) {
      localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_NORMAL
    }

    // RefValueをリセット
    setTimeout(() => {
      setRefValue()
    }, 0)

    end = performance.now()
    console.log(`getInitDataInfo実行時間: ${(end - start).toFixed(3)} ミリ秒`)
    isLoading.value = false
  } catch (e: unknown) {
    isLoading.value = false
    throw e
  }
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 相談形態
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONSULTATIONFORM },
    // 性別
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER },
    // 介護保険（利用者負担割合)
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NURSINGCAREINSURANCE },
    // 後期高齢者医療保険（75歳以上）の一部負担金
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BURDEN },
    // 高額介護サービス費該当(利用者負担)
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_USERBURDEN },
    // 各手帳（身障、療育、福祉）有無
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NOTEBOOK },
    // 受給者証の有無
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEDICALBENEFITSCARD },
    // 日常生活自立度判定基準(寝たきり)
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DAILY_LIFE_INDEPENDENCE_CRITERIA_BEDRIDDEN },
    // 日常生活自立度判定基準(認知症)
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DAILY_LIFE_INDEPENDENCE_CRITERIA_DEMENTIA },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  local.consultationFormRadioList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CONSULTATIONFORM
  )
  local.genderRadioList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_GENDER).sort(
    (a, b) => parseInt(b.value) - parseInt(a.value)
  )
  // 介護保険（利用者負担割合)
  local.nursingCareInsuranceRadioList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_NURSINGCAREINSURANCE
  )
  // 後期高齢者医療保険（75歳以上）の一部負担金
  local.burdenRadioList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_BURDEN)

  // 高額介護サービス費該当(利用者負担)
  local.userBurdenRadioList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_USERBURDEN)
  // 各手帳（身障、療育、福祉）有無
  local.notebookRadioList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_NOTEBOOK)
  // 受給者証の有無
  local.medicalBenefitsCardRadioList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MEDICALBENEFITSCARD
  )

  // 日常生活自立度判定基準(障害高齢者)
  local.seniorEverydayLifeIndependenceLevelRadioList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DAILY_LIFE_INDEPENDENCE_CRITERIA_BEDRIDDEN
  )
  // 日常生活自立度判定基準(認知症)
  local.dementiaEverydayLifeIndependenceLevelRadioList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DAILY_LIFE_INDEPENDENCE_CRITERIA_DEMENTIA
  )

  // 相談受付
  localOneway.section0.radioItems = cloneDeep(local.consultationFormRadioList)

  // 緊急連絡先 性別
  localOneway.section1.sexRadioItems = cloneDeep(local.genderRadioList)

  // 相談者 性別
  localOneway.section2.sexRadioItems = cloneDeep(local.genderRadioList)

  // 利用者負担割合
  localOneway.section7.radioItems_1 = cloneDeep(local.nursingCareInsuranceRadioList)
  // 一部負担金
  if (local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21 && heisei30Kbn.value) {
    // H21/4版かつ作成日が2018年4月1日以降（平成30年区分=1）の場合、「２割負担」表示しない
    localOneway.section7.radioItems_2 = cloneDeep(local.burdenRadioList)
  } else {
    localOneway.section7.radioItems_2 = cloneDeep(local.burdenRadioList)
    if (localOneway.section7.radioItems_2.length > 1) {
      // 「２割負担」選択しを追加
      localOneway.section7.radioItems_2.splice(1, 0, {
        label: t('label.two-tenths-burden'),
        value: Or30732Const.DEFAULT.TWO_TENTHS_BURDEN,
      })
    }
  }

  // 利用者負担
  localOneway.section7.radioItems_3 = cloneDeep(local.userBurdenRadioList)
  // H21/4版かつ作成日が2018年3月31日以前（平成30年区分=0）の場合、選択肢第５段階はなし
  if (local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21 && !heisei30Kbn.value) {
    localOneway.section7.radioItems_3 = localOneway.section7.radioItems_3.filter(
      (item) => item.value !== Or30732Const.DEFAULT.USER_BURDEN_LEVEL_5
    )
  } else {
    // 第５段階を一番目に移動
    const lastItem = localOneway.section7.radioItems_3.pop()
    if (lastItem) {
      localOneway.section7.radioItems_3.unshift(lastItem)
    }
  }

  // 身障手帳 有無ラジオ
  localOneway.section9.radioItems = cloneDeep(local.notebookRadioList)

  // 療育手帳 有無ラジオ
  localOneway.section10.radioItems = cloneDeep(local.notebookRadioList)

  // 精神障害者保険福祉手帳 有無ラジオ
  localOneway.section11.radioItems = cloneDeep(local.notebookRadioList)

  // 障碍福祉サービス 有無ラジオ
  localOneway.section12.radioItems = cloneDeep(local.medicalBenefitsCardRadioList)

  // 日常生活自立度 認知症
  localOneway.section13.radioItems_2 = cloneDeep(
    local.dementiaEverydayLifeIndependenceLevelRadioList
  )
  // 日常生活自立度 障害高齢者
  localOneway.section13.radioItems_1 = cloneDeep(local.seniorEverydayLifeIndependenceLevelRadioList)
}

/**
 *  保存処理
 */
async function _save(): Promise<boolean> {
  if (updateKbn.value === TeX0002Const.DEFAULT.UPDATE_KBN_N) {
    updateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_U
  }

  const inputData: AssessmentHome1UpdateInEntity = {
    /** タブID */
    tabId: local.commonInfo.activeTabId ?? '',
    /** 機能ID */
    kinoId: systemCommonsStore.getFunctionId ?? '',
    /** 当履歴ページ番号 */
    krirekiNo: local.commonInfo.historyNo ?? '',
    /** e文書用パラメータ */
    edocumentUseParam: useCmnCom().getEDocumentParam(),
    /** e文書削除用パラメータ */
    edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
    /** 期間対象フラグ */
    kikanFlg: local.commonInfo.kikanKanriFlg ?? '',
    /** 計画対象期間番号 */
    planningPeriodNo: local.commonInfo.sc1No,
    /** 開始日 */
    startYmd: local.commonInfo.periodStartYmd,
    /** 終了日 */
    endYmd: local.commonInfo.periodEndYmd,
    /** ガイドラインまとめTODO */
    matomeFlg: '1',
    /** ログインID */
    loginId: systemCommonsStore.getCurrentUser?.loginId ?? '',
    /** システム略称 */
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    /** 職員ID */
    shokuId: local.commonInfo.createUserId ?? '',
    /** システムコード */
    sysCd: systemCommonsStore.getSystemCode ?? '',
    /** 事業者名 */
    svJigyoKnj: local.commonInfo.jigyoKnj ?? '',
    /** 作成者名 */
    createUserName: local.commonInfo.createUserName ?? '',
    /** 利用者名 */
    userName:
      systemCommonsStore.getUserSelectUserInfo()?.nameSei +
      ' ' +
      systemCommonsStore.getUserSelectUserInfo()?.nameMei,
    /** 法人ID */
    houjinId: local.commonInfo.houjinId ?? '',
    /** 施設ID */
    shisetuId: local.commonInfo.shisetuId ?? '',
    /** 利用者ID */
    userId: local.commonInfo.userId ?? '',
    /** 事業者ID */
    svJigyoId: local.commonInfo.jigyoId ?? '',
    /** 種別ID */
    syubetsuId: local.commonInfo.syubetuId ?? '',
    /** 更新区分 */
    updateKbn: updateKbn.value,
    /** 履歴更新区分 */
    historyUpdateKbn: local.commonInfo.historyUpdateKbn ?? '',
    /** 削除処理区分 */
    deleteKbn: local.commonInfo.deleteKbn ?? '',
    /** 計画対象期間ID */
    sc1Id: local.commonInfo.sc1Id,
    /** アセスメントID */
    gdlId: local.commonInfo.gdlId,
    /** 作成日 */
    kijunbiYmd: local.commonInfo.createYmd ?? '',
    /** 作成者ID */
    sakuseiId: local.commonInfo.createUserId ?? '',
    /** 改定フラグ */
    ninteiFormF: local.commonInfo.ninteiFormF ?? '',
    /** 履歴更新回数 */
    historyModifiedCnt: local.commonInfo.historyModifiedCnt,
    /** フェースシート情報 */
    faceInfo: {
      /** 連絡先氏名 */
      knameKnj: refValue.value.section1.kNameKnj.value,
      /** 連絡先性別 */
      ksex: refValue.value.section1.kSex,
      /** 連絡先年齢 */
      knenrei: refValue.value.section1.kNenrei.value,
      /** 連絡先続柄 */
      kzcode: refValue.value.section1.kZcode.modelValue,
      /** 連絡先住所 */
      kaddressKnj: refValue.value.section1.kAddressKnj.value,
      /** 連絡先TEL */
      ktel: refValue.value.section1.kTel.value,
      /** 連絡先TEL（携帯） */
      kkeitaitel: refValue.value.section1.kKeitaitel.value,
      /** 相談者続柄 */
      sdZcode: refValue.value.section2.sdZcode.modelValue,
      /** 相談者住所 */
      sodanshaAddKnj: refValue.value.section2.sodanshaAddKnj.value,
      /** 依頼年月日 */
      requestDateYmd: refValue.value.section4.requestDateYmd.value,
      /** 寝たきり度判定日 */
      adl1DateYmd: refValue.value.section13.adl1DateYmd.value,
      /** 認知症判定日 */
      adl2DateYmd: refValue.value.section13.adl2DateYmd.value,
      /** 備考1 */
      memo1Knj: refValue.value.section0.memo1Knj.value,
      /** 備考2 */
      memo2Knj: refValue.value.section14.memo2Knj.value,
      /** 高額介護サービス費該当 */
      kkaigo: refValue.value.section7.kKaigo,
      /** 相談内容（本人） */
      soudanNaiyouKnj: refValue.value.section5.soudanNaiyouKnj.value,
      /** 相談内容2（家族及び介護者） */
      soudanNaiyou2Knj: refValue.value.section5.soudanNaiyou2Knj.value,
      /** 生活の経過 */
      seikatuKeikaKnj: refValue.value.section6.seikatuKeikaKnj.value,
      /** 支援費受給の有無 */
      sienJukyuUmu: refValue.value.section12.sienJukyuUmu,
      /** 支援費受給の区分 */
      sienJukyuKubunKnj: refValue.value.section12.sienJukyuKubunKnj.value,
      /** 相談形態 */
      soudanKeitai: refValue.value.section0.soudanKeitai,
      /** 相談形態その他 */
      keitaiSonotaKnj: refValue.value.section0.keitaiSonotaKnj.value,
      /** 相談受付者ID */
      soudanTantoId: refValue.value.section0.soudanTantoId,
      /** 相談受付日 */
      soudanUketukeYmd: refValue.value.section0.soudanUketukeYmd.value,
      /** 相談者氏名 */
      adName2Knj: refValue.value.section2.adName2Knj.value,
      /** 相談者性別 */
      sdSex2: refValue.value.section2.sdSex2,
      /** 相談者電話番号 */
      sdTel2: refValue.value.section2.sdTel2.value,
      /** 相談者電話番号（携帯） */
      sdKeitaitel2: refValue.value.section2.sdKeitaitel2.value,
      /** 相談経路 */
      keiro2Knj: refValue.value.section3.keiro2Knj.value,
      /** 相談者年齢 */
      sdNenrei2: refValue.value.section2.sdNenrei2.value,
      /** 身障種別 */
      sinshouShu: refValue.value.section9.sinshouShu.value,
      /** 見込み要介護認定 */
      yokaiKbn1: refValue.value.section8.yokaiKbn1.modelValue,
      /** 認定済要介護認定 */
      yokaiKbn2: refValue.value.section8.yokaiKbn2.modelValue,
      /** 要介護認定日 */
      ninteiYmd: refValue.value.section8.ninteiYmd.value,
      /** 寝たきり度 */
      adl1: refValue.value.section13.adl1,
      /** 認知症度 */
      adl2: refValue.value.section13.adl2,
      /** 寝たきり度判定者 */
      adl1TantoKnj: refValue.value.section13.adl1TantoKnj.value,
      /** 認知症度判定者 */
      adl2TantoKnj: refValue.value.section13.adl2TantoKnj.value,
      /** 寝たきり度判定病院 */
      adl1HospKnj: refValue.value.section13.adl1HospKnj.value,
      /** 認知症度判定病院 */
      adl2HospKnj: refValue.value.section13.adl2HospKnj.value,
      /** 身障手帳有り無し */
      techoUmu1: refValue.value.section9.techoUmu1,
      /** 療育手帳有り無し */
      techoUmu2: refValue.value.section10.techoUmu2,
      /** 精神手帳有り無し */
      techoUmu3: refValue.value.section11.techoUmu3,
      /** 身障等級 */
      ttokyu1: refValue.value.section9.tTokyu1.modelValue,
      /** 療育手帳程度 */
      ttokyu2: refValue.value.section10.tTokyu2.modelValue,
      /** 精神手帳等級 */
      ttokyu3: refValue.value.section11.tTokyu3.modelValue,
      /** 身障手帳備考 */
      biko1Knj: refValue.value.section9.biko1Knj.value,
      /** 療育手帳備考 */
      biko2Knj: refValue.value.section10.biko2Knj.value,
      /** 精神手帳備考 */
      biko3Knj: refValue.value.section11.biko3Knj.value,
      /** 身障手帳交付日 */
      get1Ymd: refValue.value.section9.get1Ymd.value,
      /** 療育手帳交付日 */
      get2Ymd: refValue.value.section10.get2Ymd.value,
      /** 精神手帳交付日 */
      get3Ymd: refValue.value.section11.get3Ymd.value,
      /** 自立支援医療受給者証の有無 */
      shogaiJukyuUmu: refValue.value.section12.shogaiJukyuUmu,
      /** 介護保険利用者負担割合 */
      hutanWariai: refValue.value.section7.hutanWariai,
      /** 後期高齢者医療保険一部負担金 */
      hutanKin: refValue.value.section7.hutanKin,
      /** アセスメント初回実施日 */
      jisshiShokaiYmd: refValue.value.section14.jisshiShokaiYmd.value,
    },
    /** 課題と目標情報リスト */
    kadaiList: [] as {
      /** id */
      id: ''
      /** アセスメント番号 */
      assNo: ''
      /** 課題 */
      kadaiKnj: ''
      /** 長期 */
      choukiKnj: ''
      /** 短期 */
      tankiKnj: ''
      /** 連番 */
      seq: ''
      /** 更新区分 */
      updateKbn: ''
    }[],
  }

  const childCpBindsData = getChildCpBinds(props.uniqueCpId, {
    // 課題と目標リスト
    [OrX0209Const.CP_ID(0)]: { cpPath: OrX0209Const.CP_ID(0), twoWayFlg: true },
  })

  // 課題と目標リスト
  const changedIssuesAndGoalsList = childCpBindsData[OrX0209Const.CP_ID(0)].twoWayBind
    ?.value as OrX0209Type

  // 課題と目標リスト作成
  changedIssuesAndGoalsList?.items.forEach((item) => {
    inputData.kadaiList?.push({
      /** id */
      id: item.dataId,
      /** アセスメント番号 */
      assNo: item.assNo,
      /** 課題 */
      kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
      /** 長期 */
      choukiKnj: item.longtermGoal.value,
      /** 短期 */
      tankiKnj: item.shorttermGoal.value,
      /** 連番 */
      seq: item.seq?.toString(),
      /** 更新区分 */
      updateKbn: item.updateKbn,
    })
  })
  // 削除された課題を取得
  const delItems = local.commonInfo.issuesAndGoalsList?.filter(
    (item) => !changedIssuesAndGoalsList.items?.some((cKdai) => item.id === cKdai.id)
  )
  delItems?.forEach((item) => {
    inputData.kadaiList?.push({
      /** id */
      id: item.dataId,
      /** アセスメント番号 */
      assNo: item.assNo,
      /** 課題 */
      kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
      /** 長期 */
      choukiKnj: item.longtermGoal.value,
      /** 短期 */
      tankiKnj: item.shorttermGoal.value,
      /** 連番 */
      seq: item.seq?.toString(),
      /** 更新区分 */
      updateKbn: TeX0002Const.DEFAULT.UPDATE_KBN_D,
    })
  })

  const resData: AssessmentHome1UpdateOutEntity = await ScreenRepository.update(
    'assessmentHome1Update',
    inputData
  )

  if (resData.data) {
    // 更新後データを取得し設定
    TeX0002Logic.data.set({
      uniqueCpId: props.parentUniqueCpId,
      value: {
        updateData: {
          sc1Id: resData.data.sc1Id,
          gdlId: resData.data.gdlId,
          errKbn: resData.data.errKbn,
        },
      },
    })
  } else {
    return false
  }

  return true
}
/**
 *  新規処理
 */
function createNew() {
  // 画面データをクリア
  clearData()

  updateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_C

  // RefValueをリセット
  setTimeout(() => {
    setRefValue()
  }, 0)
}

/**
 *  画面データをクリア
 */
function clearData() {
  refValue.value = {
    section0: {
      memo1Knj: { value: '' },
      soudanUketukeYmd: {
        value: '',
        mo01343: {
          value: '',
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        },
      } as Mo00020Type,
      soudanKeitai: '',
      keitaiSonotaKnj: {
        value: '',
      },
      soudanTantoId: '',
      shokuinName: { value: '' } as OrX0157Type,
    },
    section1: {
      kNameKnj: { value: '' },
      kSex: '',
      kNenrei: { value: '' },
      kZcode: { modelValue: '' },
      kAddressKnj: { value: '' },
      kTel: { value: '' },
      kKeitaitel: { value: '' },
    },
    section2: {
      adName2Knj: { value: '' },
      sdSex2: '',
      sdNenrei2: { value: '' },
      sdZcode: { modelValue: '' },
      sodanshaAddKnj: {
        value: '',
      },
      sdTel2: { value: '' },
      sdKeitaitel2: { value: '' },
    },
    section3: {
      keiro2Knj: { value: '' },
    },
    section4: {
      requestDateYmd: { value: '' },
    },
    section5: {
      soudanNaiyouKnj: {
        value: '',
      },
      soudanNaiyou2Knj: {
        value: '',
      },
    },
    section6: {
      seikatuKeikaKnj: {
        value: '',
      },
    },
    section7: {
      hutanWariai: '',
      hutanKin: '',
      kKaigo: '',
    },
    section8: {
      yokaiKbn1: { modelValue: '' },
      yokaiKbn2: { modelValue: '' },
      ninteiYmd: {
        value: '',
        mo01343: {
          value: '',
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        },
      } as Mo00020Type,
    },
    section9: {
      techoUmu1: '',
      sinshouShu: { value: '' },
      tTokyu1: { modelValue: '' },
      biko1Knj: { value: '' },
      get1Ymd: {
        value: '',
        mo01343: {
          value: '',
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        },
      } as Mo00020Type,
    },
    section10: {
      techoUmu2: '',
      tTokyu2: { modelValue: '' },
      biko2Knj: { value: '' },
      get2Ymd: {
        value: '',
        mo01343: {
          value: '',
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        },
      } as Mo00020Type,
    },
    section11: {
      techoUmu3: '',
      tTokyu3: { modelValue: '' },
      biko3Knj: { value: '' },
      get3Ymd: {
        value: '',
        mo01343: {
          value: '',
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        },
      } as Mo00020Type,
    },
    section12: {
      sienJukyuUmu: '',
      shogaiJukyuUmu: '',
      sienJukyuKubunKnj: {
        value: '',
      },
    },
    section13: {
      adl1: '',
      adl2: '',
      adl1TantoKnj: { value: '' },
      adl2TantoKnj: { value: '' },
      adl1HospKnj: { value: '' },
      adl2HospKnj: { value: '' },
      adl1DateYmd: {
        value: '',
        mo01343: {
          value: '',
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        },
      } as Mo00020Type,
      adl2DateYmd: {
        value: '',
        mo01343: {
          value: '',
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        },
      } as Mo00020Type,
    },
    section14: {
      memo2Knj: { value: '' },
      jisshiShokaiYmd: {
        value: '',
        mo01343: {
          value: '',
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        },
      } as Mo00020Type,
    },
  }
}

/**
 *  画面データを設定
 *
 * @param res - 設定データ
 */
function setFormData(res: AssessmentHome1InitInfoSelectOutEntity) {
  let start = 0
  let end = 0
  start = performance.now()

  let resFaceInfoData = {} as FaceSheetInfo
  if (res.data?.gdl4FaceH30Info?.gdlId) {
    resFaceInfoData = res.data?.gdl4FaceH30Info
  } else if (res.data?.gdl4FaceH21Info?.gdlId) {
    resFaceInfoData = res.data?.gdl4FaceH21Info
  } else if (res.data?.gdl5FaceInfo?.gdlId) {
    resFaceInfoData = res.data?.gdl5FaceInfo
  }

  if (!resFaceInfoData.gdlId) {
    if (props.onewayModelValue?.mode === Or30732Const.DEFAULT.MODE_COPY) {
      setOr35672State({ noData: true })
      return
    }
  }

  setOr35672State({ noData: false })

  if (resFaceInfoData.gdlId) {
    refValue.value = {
      section0: {
        memo1Knj: { value: resFaceInfoData.memo1Knj ?? '' },
        soudanUketukeYmd: {
          value: resFaceInfoData.soudanUketukeYmd,
          mo01343: {
            value: '',
            mo00024: {
              isOpen: false,
            } as Mo00024Type,
          },
        } as Mo00020Type,
        soudanKeitai: resFaceInfoData.soudanKeitai ?? '',
        keitaiSonotaKnj: {
          value: resFaceInfoData.keitaiSonotaKnj ?? '',
        },
        soudanTantoId: resFaceInfoData.soudanTantoId ?? '',
        shokuinName: { value: res.data?.shokuinName } as OrX0157Type,
      },
      section1: {
        kNameKnj: { value: resFaceInfoData.knameKnj ?? '' },
        kSex: resFaceInfoData.ksex ?? '',
        kNenrei: { value: resFaceInfoData.knenrei ?? '' },
        kZcode: { modelValue: resFaceInfoData.kzcode },
        kAddressKnj: { value: resFaceInfoData.kaddressKnj ?? '' },
        kTel: { value: resFaceInfoData.ktel ?? '' },
        kKeitaitel: { value: resFaceInfoData.kkeitaitel ?? '' },
      },
      section2: {
        adName2Knj: { value: resFaceInfoData.adName2Knj ?? '' },
        sdSex2: resFaceInfoData.sdSex2 ?? '',
        sdNenrei2: { value: resFaceInfoData.sdNenrei2 ?? '' },
        sdZcode: { modelValue: resFaceInfoData.sdZcode },
        sodanshaAddKnj: {
          value: resFaceInfoData.sodanshaAddKnj ?? '',
        },
        sdTel2: { value: resFaceInfoData.sdTel2 ?? '' },
        sdKeitaitel2: { value: resFaceInfoData.sdKeitaitel2 ?? '' },
      },
      section3: {
        keiro2Knj: { value: resFaceInfoData.keiro2Knj ?? '' },
      },
      section4: {
        requestDateYmd: { value: resFaceInfoData.requestDateYmd ?? '' },
      },
      section5: {
        soudanNaiyouKnj: {
          value: resFaceInfoData.soudanNaiyouKnj,
        },
        soudanNaiyou2Knj: {
          value: resFaceInfoData.soudanNaiyou2Knj,
        },
      },
      section6: {
        seikatuKeikaKnj: {
          value: resFaceInfoData.seikatuKeikaKnj,
        },
      },
      section7: {
        hutanWariai: resFaceInfoData.hutanWariai ?? '',
        hutanKin: resFaceInfoData.hutanKin ?? '',
        kKaigo: resFaceInfoData.kkaigo ?? '',
      },
      section8: {
        yokaiKbn1: { modelValue: resFaceInfoData.yokaiKbn1 },
        yokaiKbn2: { modelValue: resFaceInfoData.yokaiKbn2 },
        ninteiYmd: {
          value: resFaceInfoData.ninteiYmd,
          mo01343: {
            value: '',
            mo00024: {
              isOpen: false,
            } as Mo00024Type,
          },
        } as Mo00020Type,
      },
      section9: {
        techoUmu1: resFaceInfoData.techoUmu1 ?? '',
        sinshouShu: { value: resFaceInfoData.sinshouShu ?? '' },
        tTokyu1: { modelValue: resFaceInfoData.ttokyu1 },
        biko1Knj: { value: resFaceInfoData.biko1Knj },
        get1Ymd: {
          value: resFaceInfoData.get1Ymd,
          mo01343: {
            value: '',
            mo00024: {
              isOpen: false,
            } as Mo00024Type,
          },
        } as Mo00020Type,
      },
      section10: {
        techoUmu2: resFaceInfoData.techoUmu2 ?? '',
        tTokyu2: { modelValue: resFaceInfoData.ttokyu2 ?? '' },
        biko2Knj: { value: resFaceInfoData.biko2Knj },
        get2Ymd: {
          value: resFaceInfoData.get2Ymd,
          mo01343: {
            value: '',
            mo00024: {
              isOpen: false,
            } as Mo00024Type,
          },
        } as Mo00020Type,
      },
      section11: {
        techoUmu3: resFaceInfoData.techoUmu3 ?? '',
        tTokyu3: { modelValue: resFaceInfoData.ttokyu3 ?? '' },
        biko3Knj: { value: resFaceInfoData.biko3Knj ?? '' },
        get3Ymd: {
          value: resFaceInfoData.get3Ymd,
          mo01343: {
            value: '',
            mo00024: {
              isOpen: false,
            } as Mo00024Type,
          },
        } as Mo00020Type,
      },
      section12: {
        sienJukyuUmu: resFaceInfoData.sienJukyuUmu ?? '',
        shogaiJukyuUmu: resFaceInfoData.shogaiJukyuUmu ?? '',
        sienJukyuKubunKnj: {
          value: resFaceInfoData.sienJukyuKubunKnj ?? '',
        },
      },
      section13: {
        adl1: resFaceInfoData.adl1 ?? '',
        adl2: resFaceInfoData.adl2 ?? '',
        adl1TantoKnj: { value: resFaceInfoData.adl1TantoKnj ?? '' },
        adl2TantoKnj: { value: resFaceInfoData.adl2TantoKnj ?? '' },
        adl1HospKnj: { value: resFaceInfoData.adl1HospKnj ?? '' },
        adl2HospKnj: { value: resFaceInfoData.adl2HospKnj ?? '' },
        adl1DateYmd: {
          value: resFaceInfoData.adl1DateYmd,
          mo01343: {
            value: '',
            mo00024: {
              isOpen: false,
            } as Mo00024Type,
          },
        } as Mo00020Type,
        adl2DateYmd: {
          value: resFaceInfoData.adl2DateYmd,
          mo01343: {
            value: '',
            mo00024: {
              isOpen: false,
            } as Mo00024Type,
          },
        } as Mo00020Type,
      },
      section14: {
        memo2Knj: { value: resFaceInfoData.memo2Knj ?? '' },
        jisshiShokaiYmd: {
          value: resFaceInfoData.jisshiShokaiYmd,
          mo01343: {
            value: '',
            mo00024: {
              isOpen: false,
            } as Mo00024Type,
          },
        } as Mo00020Type,
      },
    }

    // 本人との続柄
    const consultationRelationRadioList = [] as CodeType[]
    res.data?.zokugaraList?.forEach((item) => {
      consultationRelationRadioList.push({
        label: item.zokugaraKnj,
        value: item.zcode,
      } as CodeType)
    })
    // 緊急連絡先 本人との続柄
    localOneway.section1.mo00040Oneway.items = cloneDeep(consultationRelationRadioList)
    // 相談者 本人との続柄
    localOneway.section2.mo00040Oneway.items = cloneDeep(consultationRelationRadioList)

    // 要介護区分
    const levelOfCareRequiredSelectList = [] as CodeType[]
    res.data?.yokaigoList?.forEach((item) => {
      levelOfCareRequiredSelectList.push({
        label: item.yokaiKnj,
        value: item.yokaiKbn,
      } as CodeType)
    })
    // 要介護認定 済
    localOneway.section8.mo00040Oneway_1.items = cloneDeep(levelOfCareRequiredSelectList)
    // 要介護認定 未
    localOneway.section8.mo00040Oneway_2.items = cloneDeep(levelOfCareRequiredSelectList)

    // 身障手帳等級
    const handycapSelectList = [] as CodeType[]
    res.data?.toukyuuList?.forEach((item) => {
      handycapSelectList.push({
        label: item.toukyuuKnj,
        value: item.toukyuuCd,
      } as CodeType)
    })
    // 身障手帳 等級
    localOneway.section9.mo00040Oneway.items = cloneDeep(handycapSelectList)

    // 療育手帳程度
    const disabilityNotebookSelectList = [] as CodeType[]
    res.data?.teidoList?.forEach((item) => {
      disabilityNotebookSelectList.push({
        label: item.teidoKnj,
        value: item.teidoCd,
      } as CodeType)
    })
    // 療育手帳 程度
    localOneway.section10.mo00040Oneway.items = cloneDeep(disabilityNotebookSelectList)

    // 精神障害者 等級
    const welfareNotebookSelectList = [] as CodeType[]
    res.data?.seishinList?.forEach((item) => {
      welfareNotebookSelectList.push({
        label: item.toukyuuKnj,
        value: item.toukyuuCd,
      } as CodeType)
    })
    // 精神障害者 等級
    localOneway.section11.mo00040Oneway.items = cloneDeep(welfareNotebookSelectList)

    updateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_N
  } else {
    // サブ情報がない場合、新規状態に設定する
    updateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_C

    clearData()
  }

  end = performance.now()
  console.log(`基本動作情報を設定 実行時間: ${(end - start).toFixed(3)} ミリ秒`)
}

/**
 * Or35672イベント発火
 *
 * @param event - イベント
 */
function setOr35672Event(event: Record<string, boolean>) {
  Or35672Logic.event.set({
    uniqueCpId: Or35672Const.CP_ID(0) + props.parentUniqueCpId,
    events: event,
  })
}

/**
 * RefValueをリセット
 *
 */
function setRefValue() {
  // RefValueを更新する
  useScreenStore().setCpTwoWay({
    cpId: Or30732Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/**
 * 複写処理
 *
 */
async function _copy() {
  // パラメータ
  const param: AssessmentHome1InitInfoSelectInEntity = {
    /** 計画期間ID */
    sc1Id: local.commonInfo.copySc1Id ?? '',
    /** 履歴ID */
    gdlId: local.commonInfo.copyGdlId ?? '',
    /** 改定フラグ */
    ninteiFlg: local.commonInfo.copyNinteiFormF ?? '',
    /** 履歴作成日 */
    kijunbiYmd: local.commonInfo.copyCreateYmd ?? '',
    /** 事業所ID */
    svJigyoId: local.commonInfo.jigyoId ?? '',
    /** 利用者ID */
    userid: local.commonInfo.copyUserId ?? '',
    /** 施設ID */
    shisetuId: local.commonInfo.shisetuId ?? '',
    /** 法人ID */
    houjinId: local.commonInfo.houjinId ?? '',
  }

  const res: AssessmentHome1InitInfoSelectOutEntity = await ScreenRepository.select(
    'assessmentHome1InitInfoSelect',
    param
  )

  // 画面情報を設定
  if (res.data) {
    setFormData(res)
  }
}

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or35672Logic.event.get(Or35672Const.CP_ID(0) + props.parentUniqueCpId),
  async (newValue) => {
    if (!newValue?.reload) return

    setOr35672Event({ reload: false })

    // 複写画面から共通情報を取得
    getCommonInfoFromCopy()

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or30732Const.DEFAULT.TAB_ID) {
      return
    }

    // 画面再表示
    if (newValue.reload) {
      // 画面情報再取得
      await reload()
    }
  }
)

/**
 * Gui00059のイベントを監視
 *
 * @description
 * 自身のEvent領域の検索ボタン押下フラグを更新する。
 * またGui00059のボタン押下フラグをリセットする。
 */
watch(
  () => Gui00059Logic.event.get(gui00059.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.confirmFlg && newValue.confirmParams) {
      if (refValue.value) {
        switch (focusItemId.value) {
          // 障害高齢者 判定者
          case Or30732Const.DEFAULT.ITEM_ID_SHOUGAI_HANTEI:
            // 障害高齢者度判定者名
            refValue.value.section13.adl1TantoKnj.value = newValue.confirmParams?.doctorNm ?? ''
            // 障害高齢者度機関名
            refValue.value.section13.adl1HospKnj.value = newValue.confirmParams?.hospitalNm ?? ''
            break
          // 認知症 判定者
          case Or30732Const.DEFAULT.ITEM_ID_NINNCHISHOU_HANTEI:
            // 障害高齢者度判定者名
            refValue.value.section13.adl2TantoKnj.value = newValue.confirmParams?.doctorNm ?? ''
            // 障害高齢者度機関名
            refValue.value.section13.adl2HospKnj.value = newValue.confirmParams?.hospitalNm ?? ''
            break
        }
      }

      Gui00059Logic.event.set({
        uniqueCpId: gui00059.value.uniqueCpId,
        events: {
          confirmFlg: false,
          confirmParams: undefined,
        },
      })
    }

    if (newValue.confirmFlg && newValue.confirmParams) {
      // 子コンポーネントのflgをリセットする
      Gui00059Logic.event.set({
        uniqueCpId: gui00059.value.uniqueCpId,
        events: {
          confirmFlg: false,
          confirmParams: undefined,
        },
      })
    }
  }
)

/**************************************************
 * イベント
 **************************************************/
/**
 *  セクションタイトルクリック処理
 *
 * @param sectionId - セクションID
 */
function openOr55476(sectionId: string) {
  currentSectionId.value = sectionId

  // GUI01302_関係者選択画面を開く
  Or55476Logic.state.set({
    uniqueCpId: or55476.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  セクションタイトルクリック処理
 *
 */
function openOr27349() {
  // 認定日
  localOneway.section8.or27349Oneway.startYmd1 = refValue.value.section8.ninteiYmd.value

  // GUI01302_関係者選択画面を開く
  Or27349Logic.state.set({
    uniqueCpId: or27349.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  セクションタイトルクリック処理
 *
 * @param sectionId - セクションID
 */
function openOr27210(sectionId: string) {
  currentSectionId.value = sectionId

  // 利用者ID
  localOneway.section9.or27210Oneway.userId = local.commonInfo.userId ?? ''
  switch (sectionId) {
    case Or30732Const.DEFAULT.SECTION_ID_9:
      // 手帳区分
      localOneway.section9.or27210Oneway.tkbn = Or27210Const.DEFAULT.TKBN_DISABILITY
      break
    case Or30732Const.DEFAULT.SECTION_ID_10:
      // 手帳区分
      localOneway.section9.or27210Oneway.tkbn = Or27210Const.DEFAULT.TKBN_REMEDIA_EDUCATION
      break
    case Or30732Const.DEFAULT.SECTION_ID_11:
      // 手帳区分
      localOneway.section9.or27210Oneway.tkbn = Or27210Const.DEFAULT.TKBN_MENTAL
      break
  }

  // GUI01302_関係者選択画面を開く
  Or27210Logic.state.set({
    uniqueCpId: or27210.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  セクションタイトルクリック処理
 *
 */
function openOr27339() {
  // 利用者ID
  localOneway.section13.or27339Oneway.userId = local.commonInfo.userId ?? ''
  // 事業者ID
  localOneway.section13.or27339Oneway.svJigyoId = local.commonInfo.jigyoId ?? ''
  // 施設ID
  localOneway.section13.or27339Oneway.shisetuId = systemCommonsStore.getShisetuId ?? ''
  // 法人ID
  localOneway.section13.or27339Oneway.houjinId = systemCommonsStore.getHoujinId ?? ''
  // 計画期間ID
  localOneway.section13.or27339Oneway.sc1Id = local.commonInfo.sc1Id ?? ''
  // 認定フラグ
  localOneway.section13.or27339Oneway.certificationFlg = local.commonInfo.ninteiFormF ?? ''
  // 親画面区分
  localOneway.section13.or27339Oneway.kinScreenKbn =
    Or27339Const.DEFAULT.KIN_SCREEN_KBN_ASSMENT_HOME

  // GUI01302_関係者選択画面を開く
  Or27339Logic.state.set({
    uniqueCpId: or27339.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  関係者選択画面から戻り値処理
 *
 * @param resData - 戻り値
 */
const fncOr55476Return = (resData: RelatedPersonSelectResInfo) => {
  switch (currentSectionId.value) {
    case Or30732Const.DEFAULT.SECTION_ID_1:
      // 氏名
      refValue.value.section1.kNameKnj.value = resData.nameKnj
      // 性別
      refValue.value.section1.kSex = resData.sex
      // 年齢
      refValue.value.section1.kNenrei.value = resData.age ?? ''
      // 続柄
      refValue.value.section1.kZcode.modelValue = resData.zcode
      // 住所
      refValue.value.section1.kAddressKnj.value = resData.addressKnj
      // 電話番号
      refValue.value.section1.kTel.value = resData.tel ?? ''
      // 電話番号
      refValue.value.section1.kKeitaitel.value = resData.tel2 ?? ''
      break
    case Or30732Const.DEFAULT.SECTION_ID_2:
      // 氏名
      refValue.value.section2.adName2Knj.value = resData.nameKnj
      // 性別
      refValue.value.section2.sdSex2 = resData.sex
      // 年齢
      refValue.value.section2.sdNenrei2.value = resData.age ?? ''
      // 続柄
      refValue.value.section2.sdZcode.modelValue = resData.zcode
      // 住所
      refValue.value.section2.sodanshaAddKnj.value = resData.addressKnj
      // 電話番号
      refValue.value.section2.sdTel2.value = resData.tel ?? ''
      // 電話番号
      refValue.value.section2.sdKeitaitel2.value = resData.tel2 ?? ''
      break
  }
}

/**
 *  認定情報選択画面から戻り値処理
 *
 * @param ninteiList - 選択認定情報
 */
const fncOr27349Return = (ninteiList: NinteiList) => {
  // 認定日
  refValue.value.section8.ninteiYmd.value = ninteiList.ninteiYmd
}

/**
 *  寝たきり度・認知症度選択画面から戻り値処理
 *
 * @param retData - 寝たきり度・認知症度選択情報
 */
const fncOr27339Return = (retData: BedriddenDegreeDementiaDegreeSelectScreenDataTableData) => {
  if (retData.bedriddenDegreeDementiaDegreeSelectScreenList.length > 0) {
    // 寝たきり
    refValue.value.section13.adl1 = retData.bedriddenDegreeDementiaDegreeSelectScreenList[0].neta1Cd
    // 寝たきり 判定日
    refValue.value.section13.adl1DateYmd.value =
      retData.bedriddenDegreeDementiaDegreeSelectScreenList[0].chkYmd ?? ''
    // 認知症
    refValue.value.section13.adl2 = retData.bedriddenDegreeDementiaDegreeSelectScreenList[0].neta2Cd
    // 認知症 判定日
    refValue.value.section13.adl2DateYmd.value =
      retData.bedriddenDegreeDementiaDegreeSelectScreenList[0].chkYmd ?? ''
  }
}

/**
 * 初回相談受付者選択ボタンクリック
 *
 */
function soudanTantoSelectClick() {
  localOneway.section0.or26257Oneway.defSvJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  // 選択画面を開く
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 判定者選択ボタンクリック
 *
 * @param itemId - 項目ID
 */
function hanteishaSelectClick(itemId: string) {
  focusItemId.value = itemId

  // Gui00059のダイアログ開閉状態を更新する
  Gui00059Logic.state.set({
    uniqueCpId: gui00059.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援ダイアログを開く
 *
 * @param itemId - 項目ID
 *
 * @param content - 行情報
 */
function openInputSupportDialog(itemId: string, content?: string) {
  switch (itemId) {
    // 相談内容 本人
    case Or30732Const.DEFAULT.ITEM_ID_PERSON:
      // タイトル
      localOneway.or51775Oneway.title = t('label.consultation-content')
      // 大分類CD
      localOneway.or51775Oneway.t1Cd = Or30732Const.DEFAULT.INPUT_SUPPORT_DAI_BUNRUI_CD_602
      // 中分類CD
      localOneway.or51775Oneway.t2Cd = Or30732Const.DEFAULT.INPUT_SUPPORT_CHU_BUNRUI_CD_1
      // 小分類CD
      localOneway.or51775Oneway.t3Cd = Or30732Const.DEFAULT.INPUT_SUPPORT_SHOU_BUNRUI_CD_1
      // テーブル名
      localOneway.or51775Oneway.tableName =
        Or30732Const.DEFAULT.INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL4_FACE_H21
      // クラム名
      localOneway.or51775Oneway.columnName =
        Or30732Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_SOUDAN_NAIYOU_KNJ
      break
    // 相談内容 介護者・家族
    case Or30732Const.DEFAULT.ITEM_ID_FAMILY:
      // タイトル
      localOneway.or51775Oneway.title = t('label.caregiver-family')
      // 大分類CD
      localOneway.or51775Oneway.t1Cd = Or30732Const.DEFAULT.INPUT_SUPPORT_DAI_BUNRUI_CD_602
      // 中分類CD
      localOneway.or51775Oneway.t2Cd = Or30732Const.DEFAULT.INPUT_SUPPORT_CHU_BUNRUI_CD_1
      // 小分類CD
      localOneway.or51775Oneway.t3Cd = Or30732Const.DEFAULT.INPUT_SUPPORT_SHOU_BUNRUI_CD_3
      // テーブル名
      localOneway.or51775Oneway.tableName =
        Or30732Const.DEFAULT.INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL4_FACE_H21
      // クラム名
      localOneway.or51775Oneway.columnName =
        Or30732Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_SOUDAN_NAIYOU2_KNJ
      break
    // これまでの生活の経過
    case Or30732Const.DEFAULT.ITEM_ID_LIFE_PASSAGE:
      // タイトル
      localOneway.or51775Oneway.title = t('label.life-passage-main-life-history')
      // 大分類CD
      localOneway.or51775Oneway.t1Cd = Or30732Const.DEFAULT.INPUT_SUPPORT_DAI_BUNRUI_CD_602
      // 中分類CD
      localOneway.or51775Oneway.t2Cd = Or30732Const.DEFAULT.INPUT_SUPPORT_CHU_BUNRUI_CD_1
      // 小分類CD
      localOneway.or51775Oneway.t3Cd = Or30732Const.DEFAULT.INPUT_SUPPORT_SHOU_BUNRUI_CD_2
      // テーブル名
      localOneway.or51775Oneway.tableName =
        Or30732Const.DEFAULT.INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL4_FACE_H21
      // クラム名
      localOneway.or51775Oneway.columnName =
        Or30732Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_SOUDAN_NAIYOU_KNJ
      break
  }

  // フォーカス情報を保持
  focusItemId.value = itemId

  // 文章内容
  localOneway.or51775Oneway.inputContents = content ?? ''
  // 利用者ID
  localOneway.or51775Oneway.userId = local.commonInfo.userId ?? ''

  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 入力支援ダイアログ確認処理
 *
 * @param resData - 入力支援選択情報
 */
function inputSupportConfirm(resData: Or51775ConfirmType) {
  if (!refValue.value) {
    return
  }

  switch (focusItemId.value) {
    // 相談内容 本人
    case Or30732Const.DEFAULT.ITEM_ID_PERSON:
      refValue.value.section5.soudanNaiyouKnj.value = resData.value
      break
    // 相談内容 介護者・家族
    case Or30732Const.DEFAULT.ITEM_ID_FAMILY:
      refValue.value.section5.soudanNaiyou2Knj.value = resData.value
      break
    // これまでの生活の経過
    case Or30732Const.DEFAULT.ITEM_ID_LIFE_PASSAGE:
      refValue.value.section6.seikatuKeikaKnj.value = resData.value
      break
  }
}
</script>

<template>
  <c-v-sheet
    v-show="isComponentVisible"
    class="view px-6"
  >
    <!-- Or11871：有機体：画面メニューエリア -->
    <!-- ローディング -->
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col
        cols="auto"
        class="hidden-scroll h-100"
      >
        <!-- コンテンツエリア -->
        <c-v-sheet style="width: 1080px">
          <c-v-row no-gutters>
            <!-- タイトル -->
            <g-custom-or-x-0201
              :oneway-model-value="localOneway.orX0201Oneway"
            ></g-custom-or-x-0201>
          </c-v-row>
          <!-- 新規 -->
          <c-v-row
            no-gutters
            class="border-t-sm border-s-sm border-e-sm mt-6"
          >
            <!-- 相談受付 -->
            <c-v-col class="background-white px-12 py-6">
              <!-- 備考１ -->
              <c-v-row no-gutters>
                <base-mo00046
                  v-model="refValue.section0.memo1Knj"
                  :oneway-model-value="localOneway.section0.mo00046Oneway"
                />
              </c-v-row>
              <c-v-row
                no-gutters
                align="center"
                class="pt-3"
              >
                <c-v-col
                  cols="auto"
                  class="pa-0 align-self-end"
                >
                  <!-- 相談受付日 -->
                  <base-mo00020
                    v-model="refValue.section0.soudanUketukeYmd"
                    :oneway-model-value="localOneway.section0.mo00020Oneway"
                  />
                </c-v-col>
                <c-v-col
                  cols="16"
                  class="pl-6"
                >
                  <c-v-row
                    no-gutters
                    class="align-end"
                  >
                    <c-v-col cols="auto">
                      <base-mo00039
                        v-model="refValue.section0.soudanKeitai"
                        :oneway-model-value="localOneway.section0.mo00039Oneway"
                      >
                        <!-- 相談受付ラジオ -->
                        <base-at-radio
                          v-for="item in localOneway.section0.radioItems"
                          :key="item.value"
                          :name="item.value"
                          :radio-label="item.label"
                          :value="item.value"
                        />
                      </base-mo00039>
                    </c-v-col>
                    <c-v-col
                      cols="auto"
                      class="d-flex align-end"
                    >
                      <!-- 相談形態その他 -->
                      <base-mo00045
                        v-model="refValue.section0.keitaiSonotaKnj"
                        :oneway-model-value="localOneway.section0.mo00045Oneway"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="auto"
                      class="pl-6"
                    >
                      <!-- 初回相談受付者 -->
                      <g-custom-or-x-0157
                        v-model="refValue.section0.shokuinName"
                        :oneway-model-value="localOneway.section0.orX0157Oneway"
                        @on-click-edit-btn="soudanTantoSelectClick"
                      >
                      </g-custom-or-x-0157>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 緊急連絡先 -->
            <c-v-col>
              <g-custom-or-x-0186
                :oneway-model-value="{
                  title: t('label.emergency-contact'),
                  isTitleLink: true,
                  contentClass: 'border-s-sm border-e-sm',
                }"
                @link-click="openOr55476"
              >
                <template #content>
                  <c-v-row no-gutters>
                    <c-v-col class="background-white px-12 py-6">
                      <c-v-row no-gutters>
                        <c-v-col>
                          <c-v-row
                            no-gutters
                            class="align-end"
                          >
                            <c-v-col
                              cols="auto"
                              class="mr-6"
                            >
                              <!-- 緊急連絡先の氏名 -->
                              <base-mo00045
                                v-model="refValue.section1.kNameKnj"
                                :oneway-model-value="localOneway.section1.mo00045Oneway_1"
                              />
                            </c-v-col>
                            <c-v-col
                              cols="auto"
                              class="mr-6"
                            >
                              <!-- 緊急連絡先の性別 -->
                              <base-mo00039
                                v-model="refValue.section1.kSex"
                                class="seibetu"
                                :oneway-model-value="localOneway.section1.mo00039Oneway"
                              >
                                <base-at-radio
                                  v-for="item in localOneway.section1.sexRadioItems"
                                  :key="item.value"
                                  :name="item.label"
                                  :radio-label="item.label"
                                  :value="item.value"
                                />
                              </base-mo00039>
                            </c-v-col>
                            <c-v-col
                              cols="auto"
                              class="mr-6"
                            >
                              <!-- 緊急連絡先の年齢 -->
                              <base-mo00045
                                v-model="refValue.section1.kNenrei"
                                :oneway-model-value="localOneway.section1.mo00045Oneway_2"
                              />
                            </c-v-col>
                            <c-v-col cols="auto">
                              <!-- 連絡先続柄 -->
                              <base-mo00040
                                v-model="refValue.section1.kZcode"
                                :oneway-model-value="localOneway.section1.mo00040Oneway"
                              />
                            </c-v-col>
                          </c-v-row>
                          <c-v-row
                            no-gutters
                            class="d-flex flex-row pt-4"
                          >
                            <c-v-col
                              cols="auto"
                              class="mr-6"
                            >
                              <!-- 緊急連絡先の住所 -->
                              <base-mo00045
                                v-model="refValue.section1.kAddressKnj"
                                :oneway-model-value="localOneway.section1.mo00045Oneway_3"
                              />
                            </c-v-col>
                            <c-v-col
                              cols="auto"
                              class="mr-6"
                            >
                              <!-- 連絡先TEL -->
                              <base-mo00045
                                v-model="refValue.section1.kTel"
                                :oneway-model-value="localOneway.section1.mo00045Oneway_4"
                            /></c-v-col>
                            <c-v-col cols="auto">
                              <!-- 連絡先TEL（携帯） -->
                              <base-mo00045
                                v-model="refValue.section1.kKeitaitel"
                                :oneway-model-value="localOneway.section1.mo00045Oneway_5"
                            /></c-v-col>
                          </c-v-row>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </template>
              </g-custom-or-x-0186>
            </c-v-col>
          </c-v-row>
          <!-- 相談者 -->
          <c-v-row no-gutters>
            <c-v-col>
              <g-custom-or-x-0186
                :oneway-model-value="{
                  title: t('label.consultation'),
                  isTitleLink: true,
                  contentClass: 'border-s-sm border-e-sm',
                }"
                @link-click="openOr55476"
              >
                <template #content>
                  <c-v-row no-gutters>
                    <c-v-col class="background-white px-12 py-6">
                      <c-v-row
                        no-gutters
                        class="align-end"
                      >
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <!-- 相談者の氏名 -->
                          <base-mo00045
                            v-model="refValue.section2.adName2Knj"
                            :oneway-model-value="localOneway.section2.mo00045Oneway_1"
                          />
                        </c-v-col>
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <!-- 相談者の性別 -->
                          <base-mo00039
                            v-model="refValue.section2.sdSex2"
                            class="seibetu"
                            :oneway-model-value="localOneway.section2.mo00039Oneway"
                          >
                            <base-at-radio
                              v-for="item in localOneway.section2.sexRadioItems"
                              :key="item.value"
                              :name="item.label"
                              :radio-label="item.label"
                              :value="item.value"
                            />
                          </base-mo00039>
                        </c-v-col>
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <!-- 相談者の年齢 -->
                          <base-mo00045
                            v-model="refValue.section2.sdNenrei2"
                            :oneway-model-value="localOneway.section2.mo00045Oneway_2"
                          />
                        </c-v-col>
                        <c-v-col cols="auto">
                          <!-- 相談者続柄 -->
                          <base-mo00040
                            v-model="refValue.section2.sdZcode"
                            :oneway-model-value="localOneway.section2.mo00040Oneway"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row
                        no-gutters
                        class="d-flex flex-row pt-4"
                      >
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <!-- 相談者の住所 -->
                          <base-mo00045
                            v-model="refValue.section2.sodanshaAddKnj"
                            :oneway-model-value="localOneway.section2.mo00045Oneway_3"
                          />
                        </c-v-col>
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <!-- 相談者電話番号 -->
                          <base-mo00045
                            v-model="refValue.section2.sdTel2"
                            :oneway-model-value="localOneway.section2.mo00045Oneway_4"
                        /></c-v-col>
                        <c-v-col cols="auto">
                          <!-- 相談者電話番号（携帯） -->
                          <base-mo00045
                            v-model="refValue.section2.sdKeitaitel2"
                            :oneway-model-value="localOneway.section2.mo00045Oneway_5"
                        /></c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </template>
              </g-custom-or-x-0186>
            </c-v-col>
          </c-v-row>
          <!-- 相談経路 -->
          <c-v-row no-gutters>
            <c-v-col>
              <g-custom-or-x-0186
                :oneway-model-value="{
                  title: t('label.consultation-route-consultator'),
                  contentClass: 'border-s-sm border-e-sm',
                }"
              >
                <template #content>
                  <c-v-row no-gutters>
                    <c-v-col class="background-white px-12 py-6">
                      <!--相談経路-->
                      <base-mo00046
                        v-model="refValue.section3.keiro2Knj"
                        :oneway-model-value="localOneway.section3.mo00046oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                </template>
              </g-custom-or-x-0186>
            </c-v-col>
          </c-v-row>
          <!-- 依頼年月日 -->
          <c-v-row no-gutters>
            <c-v-col>
              <g-custom-or-x-0186
                :oneway-model-value="{
                  title: t('label.home-service-plan-create'),
                  contentClass: 'border-s-sm border-e-sm',
                }"
              >
                <template #content>
                  <c-v-row no-gutters>
                    <c-v-col class="background-white px-12 py-6">
                      <!-- 届出年月日 -->
                      <base-mo00020
                        v-model="refValue.section4.requestDateYmd"
                        :oneway-model-value="localOneway.section4.mo00020Oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                </template>
              </g-custom-or-x-0186>
            </c-v-col>
          </c-v-row>
          <!-- 相談内容 -->
          <c-v-row no-gutters>
            <c-v-col>
              <g-custom-or-x-0186
                :oneway-model-value="{
                  title: t('label.consultation-content'),
                  contentClass: 'border-s-sm border-e-sm',
                }"
              >
                <template #content>
                  <c-v-row no-gutters>
                    <c-v-col class="background-white px-12 py-6">
                      <c-v-row no-gutters>
                        <c-v-col>
                          <!-- 本人 ラベル -->
                          <base-mo00615
                            :oneway-model-value="localOneway.section5.mo00615Oneway_1"
                            class="white-space"
                          />
                          <!-- 本人 入力 -->
                          <g-custom-or-x-0156
                            v-model="refValue.section5.soudanNaiyouKnj"
                            :oneway-model-value="localOneway.section5.orX0156Oneway_1"
                            @on-click-edit-btn="
                              openInputSupportDialog(
                                Or30732Const.DEFAULT.ITEM_ID_PERSON,
                                refValue.section5.soudanNaiyouKnj.value
                              )
                            "
                          />
                        </c-v-col>
                        <c-v-col
                          cols="auto"
                          class="px-6"
                        ></c-v-col>
                        <c-v-col>
                          <!-- 家族 ラベル -->
                          <base-mo00615
                            :oneway-model-value="localOneway.section5.mo00615Oneway_2"
                            class="white-space"
                          />
                          <!-- 家族 入力 -->
                          <g-custom-or-x-0156
                            v-model="refValue.section5.soudanNaiyou2Knj"
                            :oneway-model-value="localOneway.section5.orX0156Oneway_2"
                            @on-click-edit-btn="
                              openInputSupportDialog(
                                Or30732Const.DEFAULT.ITEM_ID_FAMILY,
                                refValue.section5.soudanNaiyou2Knj.value
                              )
                            "
                          />
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </template>
              </g-custom-or-x-0186>
            </c-v-col>
          </c-v-row>
          <!-- これまでの生活の経過（主な生活史） -->
          <c-v-row
            class="border-b-sm"
            no-gutters
          >
            <c-v-col>
              <g-custom-or-x-0186
                :oneway-model-value="{
                  title:
                    local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21 &&
                    !heisei30Kbn
                      ? t('label.life-passage')
                      : t('label.life-passage-main-life-history'),
                  contentClass: 'border-s-sm border-e-sm',
                }"
              >
                <template #content>
                  <c-v-row no-gutters>
                    <c-v-col class="background-white px-12 py-6">
                      <c-v-row no-gutters>
                        <c-v-col>
                          <!-- 生活経過入力 -->
                          <g-custom-or-x-0156
                            v-model="refValue.section6.seikatuKeikaKnj"
                            :oneway-model-value="localOneway.section6.mo00046Oneway"
                            @on-click-edit-btn="
                              openInputSupportDialog(
                                Or30732Const.DEFAULT.ITEM_ID_LIFE_PASSAGE,
                                refValue.section6.seikatuKeikaKnj.value
                              )
                            "
                          />
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </template>
              </g-custom-or-x-0186>
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="border-s-sm border-b-sm border-e-sm"
            no-gutters
          >
            <c-v-col>
              <!-- 介護保険、後期高齢者医療保険（75歳以上）、高額介護サービス費 -->
              <c-v-row no-gutters>
                <c-v-col class="background-white px-12 py-6">
                  <c-v-row no-gutters>
                    <c-v-col
                      v-if="
                        !(
                          local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21 &&
                          !heisei30Kbn
                        )
                      "
                      cols="auto"
                    >
                      <c-v-row no-gutters>
                        <c-v-col cols="auto">
                          <!-- 介護保険 -->
                          <base-mo00615
                            :oneway-model-value="localOneway.section7.mo00615Oneway_1"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row no-gutters>
                        <c-v-col cols="auto">
                          <!-- 利用者負担割合ラベル -->
                          <base-mo00615
                            :oneway-model-value="localOneway.section7.mo00615Oneway_2"
                          />
                        </c-v-col>
                        <c-v-col cols="auto">
                          <!-- 利用者負担割合ラジオ -->
                          <base-mo00039
                            v-model="refValue.section7.hutanWariai"
                            :oneway-model-value="localOneway.section7.mo00039Oneway_1"
                          >
                            <base-at-radio
                              v-for="item in localOneway.section7.radioItems_1"
                              :key="item.value"
                              :name="item.label"
                              :radio-label="item.label"
                              :value="item.value"
                            />
                          </base-mo00039>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                    <c-v-col
                      v-if="
                        !(
                          local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21 &&
                          !heisei30Kbn
                        )
                      "
                      cols="auto"
                      class="ml-6"
                    >
                      <c-v-row no-gutters>
                        <c-v-col cols="auto">
                          <!-- 後期高齢者医療保険（75歳以上） -->
                          <base-mo00615
                            :oneway-model-value="localOneway.section7.mo00615Oneway_3"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row no-gutters>
                        <c-v-col cols="auto">
                          <!-- 一部負担金ラベル -->
                          <base-mo00615
                            :oneway-model-value="localOneway.section7.mo00615Oneway_4"
                          />
                        </c-v-col>
                        <c-v-col cols="auto">
                          <!-- 一部負担金ラジオ -->
                          <base-mo00039
                            v-model="refValue.section7.hutanKin"
                            :oneway-model-value="localOneway.section7.mo00039Oneway_2"
                          >
                            <base-at-radio
                              v-for="item in localOneway.section7.radioItems_2"
                              :key="item.value"
                              :name="item.label"
                              :radio-label="item.label"
                              :value="item.value"
                            />
                          </base-mo00039>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="pt-2"
                  >
                    <c-v-col cols="auto">
                      <c-v-row no-gutters>
                        <c-v-col cols="auto">
                          <!-- 高額介護サービス費該当 -->
                          <base-mo00615
                            :oneway-model-value="localOneway.section7.mo00615Oneway_5"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row no-gutters>
                        <c-v-col cols="auto">
                          <!-- 利用者負担ラベル -->
                          <base-mo00615
                            :oneway-model-value="localOneway.section7.mo00615Oneway_6"
                          />
                        </c-v-col>
                        <c-v-col cols="auto">
                          <!-- 利用者負担ラジオ -->
                          <base-mo00039
                            v-model="refValue.section7.kKaigo"
                            :oneway-model-value="localOneway.section7.mo00039Oneway_3"
                          >
                            <base-at-radio
                              v-for="item in localOneway.section7.radioItems_3"
                              :key="item.value"
                              :name="item.label"
                              :radio-label="item.label"
                              :value="item.value"
                            />
                          </base-mo00039>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutters
            class="border-s-sm border-b-sm border-e-sm"
          >
            <c-v-col>
              <c-v-row no-gutters>
                <c-v-col class="background-white px-12 py-6">
                  <!-- 要介護認定 -->
                  <g-custom-or-x-0186
                    :oneway-model-value="{
                      title: t('label.care-required-certification'),
                      isTitleLink: true,
                      isInnerSection: true,
                      titleFontSize: '14',
                      contentClass: '',
                    }"
                    @link-click="openOr27349"
                  >
                    <template #content>
                      <c-v-row no-gutters>
                        <c-v-col
                          cols="auto"
                          class="py-6"
                        >
                          <c-v-row no-gutters>
                            <c-v-col
                              cols="auto"
                              class="mr-6"
                            >
                              <!-- 済 -->
                              <base-mo00040
                                v-model="refValue.section8.yokaiKbn1"
                                :oneway-model-value="localOneway.section8.mo00040Oneway_1"
                              />
                            </c-v-col>
                            <c-v-col cols="auto">
                              <!-- 認定日 -->
                              <base-mo00020
                                v-model="refValue.section8.ninteiYmd"
                                :oneway-model-value="localOneway.section8.mo00020Oneway"
                              />
                            </c-v-col>
                          </c-v-row>
                          <c-v-row
                            no-gutters
                            class="pt-6"
                          >
                            <c-v-col cols="auto">
                              <!-- 未 -->
                              <base-mo00040
                                v-model="refValue.section8.yokaiKbn2"
                                :oneway-model-value="localOneway.section8.mo00040Oneway_2"
                              />
                            </c-v-col>
                          </c-v-row>
                        </c-v-col>
                      </c-v-row>
                    </template>
                  </g-custom-or-x-0186>

                  <!-- 身障手帳 -->
                  <!-- H21/4版の場合、「身障手帳」、それ以外、「身体障碍者手帳」 -->
                  <g-custom-or-x-0186
                    :oneway-model-value="{
                      title:
                        local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21
                          ? t('label.disability-notebook')
                          : t('label.person-with-a-physical-disability-notebook'),
                      isTitleLink: true,
                      isInnerSection: true,
                      titleFontSize: '14',
                      contentClass: '',
                    }"
                    @link-click="openOr27210(Or30732Const.DEFAULT.SECTION_ID_9)"
                  >
                    <template #content>
                      <c-v-row
                        no-gutters
                        align="end"
                        class="py-6"
                      >
                        <!-- 有無 -->
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <base-mo00039
                            v-model="refValue.section9.techoUmu1"
                            :oneway-model-value="localOneway.section9.mo00039Oneway"
                          >
                            <base-at-radio
                              v-for="item in localOneway.section9.radioItems"
                              :key="item.value"
                              :name="item.label"
                              :radio-label="item.label"
                              :value="item.value"
                            />
                          </base-mo00039>
                        </c-v-col>
                        <!-- 等級 -->
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <base-mo00045
                            v-model="refValue.section9.sinshouShu"
                            :oneway-model-value="localOneway.section9.mo00045Oneway"
                          />
                        </c-v-col>
                        <!-- セレクト -->
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <base-mo00040
                            v-model="refValue.section9.tTokyu1"
                            :oneway-model-value="localOneway.section9.mo00040Oneway"
                          />
                        </c-v-col>
                        <!-- 備考 -->
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <base-mo00046
                            v-model="refValue.section9.biko1Knj"
                            :oneway-model-value="localOneway.section9.mo00046Oneway"
                          />
                        </c-v-col>
                        <!-- 交付日 -->
                        <c-v-col cols="auto">
                          <base-mo00020
                            v-model="refValue.section9.get1Ymd"
                            :oneway-model-value="localOneway.section9.mo00020Oneway"
                          />
                        </c-v-col>
                      </c-v-row>
                    </template>
                  </g-custom-or-x-0186>

                  <!-- 療育手帳 -->
                  <g-custom-or-x-0186
                    :oneway-model-value="{
                      title: t('label.remedial-education-notebook'),
                      isTitleLink: true,
                      isInnerSection: true,
                      titleFontSize: '14',
                      contentClass: '',
                    }"
                    @link-click="openOr27210(Or30732Const.DEFAULT.SECTION_ID_10)"
                  >
                    <template #content>
                      <c-v-row
                        no-gutters
                        align="end"
                        class="py-6"
                      >
                        <!-- 有無 -->
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <base-mo00039
                            v-model="refValue.section10.techoUmu2"
                            :oneway-model-value="localOneway.section10.mo00039Oneway"
                          >
                            <base-at-radio
                              v-for="item in localOneway.section10.radioItems"
                              :key="item.value"
                              :name="item.label"
                              :radio-label="item.label"
                              :value="item.value"
                            />
                          </base-mo00039>
                        </c-v-col>
                        <!-- 程度セレクト -->
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <base-mo00040
                            v-model="refValue.section10.tTokyu2"
                            :oneway-model-value="localOneway.section10.mo00040Oneway"
                          />
                        </c-v-col>
                        <!-- 備考 -->
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <base-mo00046
                            v-model="refValue.section10.biko2Knj"
                            :oneway-model-value="localOneway.section10.mo00046Oneway"
                          />
                        </c-v-col>
                        <!-- 交付日 -->
                        <c-v-col cols="auto">
                          <base-mo00020
                            v-model="refValue.section10.get2Ymd"
                            :oneway-model-value="localOneway.section10.mo00020Oneway"
                          />
                        </c-v-col>
                      </c-v-row>
                    </template>
                  </g-custom-or-x-0186>

                  <!-- 精神障害者保険福祉手帳 -->
                  <g-custom-or-x-0186
                    :oneway-model-value="{
                      title: t('label.mental-insurance-welfare-notebook'),
                      isTitleLink: true,
                      isInnerSection: true,
                      titleFontSize: '14',
                      contentClass: '',
                    }"
                    @link-click="openOr27210(Or30732Const.DEFAULT.SECTION_ID_11)"
                  >
                    <template #content>
                      <c-v-row
                        no-gutters
                        align="end"
                        class="py-6"
                      >
                        <!-- 有無 -->
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <base-mo00039
                            v-model="refValue.section11.techoUmu3"
                            :oneway-model-value="localOneway.section11.mo00039Oneway"
                          >
                            <base-at-radio
                              v-for="item in localOneway.section11.radioItems"
                              :key="item.value"
                              :name="item.label"
                              :radio-label="item.label"
                              :value="item.value"
                            />
                          </base-mo00039>
                        </c-v-col>
                        <!-- 等級セレクト -->
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <base-mo00040
                            v-model="refValue.section11.tTokyu3"
                            :oneway-model-value="localOneway.section11.mo00040Oneway"
                          />
                        </c-v-col>
                        <!-- 備考 -->
                        <c-v-col
                          cols="auto"
                          class="mr-6"
                        >
                          <base-mo00046
                            v-model="refValue.section11.biko3Knj"
                            :oneway-model-value="localOneway.section11.mo00046Oneway"
                          />
                        </c-v-col>
                        <!-- 交付日 -->
                        <c-v-col cols="auto">
                          <base-mo00020
                            v-model="refValue.section11.get3Ymd"
                            :oneway-model-value="localOneway.section11.mo00020Oneway"
                          />
                        </c-v-col>
                      </c-v-row>
                    </template>
                  </g-custom-or-x-0186>
                  <v-divider></v-divider>
                  <!-- 障碍福祉サービス -->
                  <c-v-row
                    no-gutters
                    class="py-6"
                  >
                    <c-v-col
                      cols="auto"
                      class="mr-6"
                    >
                      <!-- 障碍福祉サービス受給者証の有無 -->
                      <base-mo00039
                        v-model="refValue.section12.sienJukyuUmu"
                        :oneway-model-value="localOneway.section12.mo00039Oneway_1"
                      >
                        <base-at-radio
                          v-for="item in localOneway.section12.radioItems"
                          :key="item.value"
                          :name="item.label"
                          :radio-label="item.label"
                          :value="item.value"
                        />
                      </base-mo00039>
                    </c-v-col>
                    <c-v-col
                      cols="auto"
                      class="mr-6"
                    >
                      <!-- 自立支援医療受給者証の有無 -->
                      <base-mo00039
                        v-model="refValue.section12.shogaiJukyuUmu"
                        :oneway-model-value="localOneway.section12.mo00039Oneway_2"
                      >
                        <base-at-radio
                          v-for="item in localOneway.section12.radioItems"
                          :key="item.value"
                          :name="item.label"
                          :radio-label="item.label"
                          :value="item.value"
                        />
                      </base-mo00039>
                    </c-v-col>
                    <c-v-col cols="auto">
                      <!-- 障害支援区分 -->
                      <base-mo00045
                        v-model="refValue.section12.sienJukyuKubunKnj"
                        :oneway-model-value="localOneway.section12.mo00045Oneway"
                      />
                    </c-v-col>
                  </c-v-row>

                  <!-- 日常生活自立度 -->
                  <g-custom-or-x-0186
                    :oneway-model-value="{
                      title: t('label.everydaylife-life-independence-level'),
                      isTitleLink: true,
                      isInnerSection: true,
                      titleFontSize: '14',
                      contentClass: '',
                    }"
                    @link-click="openOr27339"
                  >
                    <template #content>
                      <c-v-row
                        no-gutters
                        align="end"
                        class="pt-6"
                      >
                        <c-v-col>
                          <c-v-row no-gutters>
                            <!-- 障害高齢者ラジオ -->
                            <c-v-col cols="auto">
                              <base-mo00039
                                v-model="refValue.section13.adl1"
                                :oneway-model-value="localOneway.section13.mo00039Oneway_1"
                              >
                                <base-at-radio
                                  v-for="item in localOneway.section13.radioItems_1"
                                  :key="item.value"
                                  :name="item.label"
                                  :radio-label="item.label"
                                  :value="item.value"
                                />
                              </base-mo00039>
                            </c-v-col>
                          </c-v-row>
                          <c-v-row
                            no-gutters
                            class="pt-4"
                          >
                            <!-- 判定者 -->
                            <c-v-col
                              cols="auto"
                              class="mr-6"
                            >
                              <g-custom-or-x-0157
                                v-model="refValue.section13.adl1TantoKnj"
                                :oneway-model-value="localOneway.section13.orX0157Oneway_1"
                                @on-click-edit-btn="
                                  hanteishaSelectClick(Or30732Const.DEFAULT.ITEM_ID_SHOUGAI_HANTEI)
                                "
                              >
                              </g-custom-or-x-0157>
                            </c-v-col>
                            <!-- 機関名 -->
                            <c-v-col
                              cols="auto"
                              class="mr-6"
                            >
                              <base-mo00045
                                v-model="refValue.section13.adl1HospKnj"
                                :oneway-model-value="localOneway.section13.mo00045Oneway_1"
                              />
                            </c-v-col>
                            <!-- 判定日 -->
                            <c-v-col cols="auto">
                              <base-mo00020
                                v-model="refValue.section13.adl1DateYmd"
                                :oneway-model-value="localOneway.section13.mo00020Oneway_1"
                              />
                            </c-v-col>
                          </c-v-row>
                          <c-v-row
                            no-gutters
                            class="py-6"
                          >
                            <c-v-divider></c-v-divider>
                          </c-v-row>
                          <c-v-row no-gutters>
                            <!-- 認知症ラジオ -->
                            <c-v-col cols="auto">
                              <base-mo00039
                                v-model="refValue.section13.adl2"
                                :oneway-model-value="localOneway.section13.mo00039Oneway_2"
                              >
                                <base-at-radio
                                  v-for="item in localOneway.section13.radioItems_2"
                                  :key="item.value"
                                  :name="item.label"
                                  :radio-label="item.label"
                                  :value="item.value"
                                />
                              </base-mo00039>
                            </c-v-col>
                          </c-v-row>
                          <c-v-row
                            no-gutters
                            class="pt-4"
                          >
                            <!-- 判定者 -->
                            <c-v-col
                              cols="auto"
                              class="mr-6"
                            >
                              <g-custom-or-x-0157
                                v-model="refValue.section13.adl2TantoKnj"
                                :oneway-model-value="localOneway.section13.orX0157Oneway_2"
                                @on-click-edit-btn="
                                  hanteishaSelectClick(
                                    Or30732Const.DEFAULT.ITEM_ID_NINNCHISHOU_HANTEI
                                  )
                                "
                              >
                              </g-custom-or-x-0157>
                            </c-v-col>
                            <!-- 機関名 -->
                            <c-v-col
                              cols="auto"
                              class="mr-6"
                            >
                              <base-mo00045
                                v-model="refValue.section13.adl2HospKnj"
                                :oneway-model-value="localOneway.section13.mo00045Oneway_2"
                              />
                            </c-v-col>
                            <!-- 判定日 -->
                            <c-v-col cols="auto">
                              <base-mo00020
                                v-model="refValue.section13.adl2DateYmd"
                                :oneway-model-value="localOneway.section13.mo00020Oneway_2"
                              />
                            </c-v-col>
                          </c-v-row>
                        </c-v-col>
                      </c-v-row>
                    </template>
                  </g-custom-or-x-0186>
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="
              !(
                local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21 && !heisei30Kbn
              )
            "
            no-gutters
            class="background-white border-s-sm border-b-sm border-e-sm px-6 py-6 justify-end"
          >
            <c-v-col
              cols="auto"
              class="mr-6"
            >
              <!-- 備考２ -->
              <base-mo00045
                v-model="refValue.section14.memo2Knj"
                :oneway-model-value="localOneway.section14.mo00045Oneway"
              />
            </c-v-col>
            <c-v-col cols="auto">
              <!-- アセスメント実施日（初回） -->
              <base-mo00020
                v-model="refValue.section14.jisshiShokaiYmd"
                :oneway-model-value="localOneway.section14.mo00020Oneway"
              />
            </c-v-col>
          </c-v-row>
        </c-v-sheet>
      </c-v-col>
    </c-v-row>

    <!-- Or00051：フッターエリア -->
    <!-- <g-base-or00051 v-bind="or00051" /> -->
  </c-v-sheet>

  <!-- 線 -->
  <c-v-row
    no-gutters
    class="py-6 position-relative"
  >
    <c-v-divider
      class="position-absolute w-100"
      style="top: 50%"
    ></c-v-divider>
  </c-v-row>

  <!-- フッター -->
  <c-v-row
    no-gutters
    class="pb-6 px-6"
  >
    <c-v-col>
      <g-custom-or-x-0209
        v-bind="OrX0209"
        :model-value="local.issuesAndGoalsList"
        :oneway-model-value="localOneway.issuesAndGoalsListOneway"
      >
      </g-custom-or-x-0209>
    </c-v-col>
  </c-v-row>

  <!-- 職員検索画面 -->
  <g-custom-or-26257
    v-if="showDialogOr26257"
    v-bind="or26257"
    v-model="local.or26257"
    :oneway-model-value="localOneway.section0.or26257Oneway"
  />
  <!-- 関係者選択画面 -->
  <g-custom-or-55476
    v-if="showDialogOr55476"
    v-bind="or55476"
    :oneway-model-value="localOneway.section1.or55746Oneway"
    @update:model-value="fncOr55476Return"
  />
  <!-- 認定情報選択画面 -->
  <g-custom-or-27349
    v-if="showDialogOr27349"
    v-bind="or27349"
    :oneway-model-value="localOneway.section8.or27349Oneway"
    @confirm="fncOr27349Return"
  />
  <!-- 手帳情報選択画面 -->
  <g-custom-or-27210
    v-if="showDialogOr27210"
    v-bind="or27210"
    v-model="local.or27210"
    :oneway-model-value="localOneway.section9.or27210Oneway"
  />
  <!-- 寝たきり度・認知症度選択画面 -->
  <g-custom-or-27339
    v-if="showDialogOr27339"
    v-bind="or27339"
    :oneway-model-value="localOneway.section13.or27339Oneway"
    @update:model-value="fncOr27339Return"
  />
  <!-- 入力支援ダイアログ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-model="local.or51775"
    v-bind="or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="inputSupportConfirm"
  />
  <!-- GUI00059_医療機関選択 -->
  <g-custom-gui-00059
    v-if="showDialogGui00059"
    v-bind="gui00059"
  />
</template>

<style scoped lang="scss">
:deep(.v-input__control) {
  background-color: rgb(var(--v-theme-surface));
}
.divider-class {
  border-width: thin;
  margin: 8px 0px;
}
.divider-noLine-class {
  border: none;
  margin: 24px 0px;
  border-color: rgb(var(--v-theme-surface));
}

.view {
  display: flex;
  flex-direction: column;
  // height: 100%;
  background-color: transparent;
  width: 100%;
  overflow-y: auto;
  // :deep(.v-field__outline) {
  //   --v-field-border-width: 0px !important;
  // }
  .textarea {
    overflow-y: scroll;
  }
}

.content-area {
  display: flex;
  flex-grow: 1;
  height: 100%;
  width: 100%;
  padding-right: 0px;
  overflow: auto !important;
}

.second-row {
  margin-top: 0px;
  align-items: baseline;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 36px !important;
}
:deep(.v-selection-control .v-label) {
  margin-left: -6px;
}

:deep(.v-input__append) {
  margin-left: 8px;
}

:deep(.v-field-no-height .v-field) {
  height: unset !important;
}

:deep(.seibetu .v-radio) {
  min-height: 24px;
  height: 24px;
}

.background-white {
  background-color: rgb(var(--v-theme-surface));
}
</style>
