import type { Mo00045Type } from '@/types/business/components/Mo00045Type'

/**
 * Or32382:週間表テンプレート
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or32382StateType {
  /** 無効フラグ */
  printDisabled?: boolean
  /** 無効フラグ */
  printKeikakuDisabled?: boolean
  /** 削除フラグ */
  deleteFlg?: boolean
  /** 複写フラグ */
  copyFlg?: boolean
  /** 編集フラグ */
  editFlg?: boolean
  /** 更新発行フラグ */
  isSaveFlg?: boolean
  /** 再表示発行フラグ */
  isRefresh?: boolean
  /** 有効期間ID */
  yukokikanId?: number
  /** 計画期間ID */
  planTargetPeriodId?: number
  /** 計画期間表示中の番号 */
  periodCurrentIndex?: number
  /** 計画期間登録数 */
  periodTotalCount?: number
  /** 履歴ID */
  rirekiId?: number
  /** 履歴表示中の番号 */
  rirekiCurrentIndex?: number
  /** 履歴登録数 */
  rirekiTotalCount?: number
}

/**
 * Or32382：有機体：画面メニューエリア
 * EventStatus領域に保持するデータ構造
 */
export interface Or32382EventType {
  /** お気に入りイベント発行フラグ */
  favoriteEventFlg?: boolean

  /** 保存イベント発行フラグ */
  saveEventFlg?: boolean

  /** 新規イベント発行フラグ */
  createEventFlg?: boolean

  /** 複写イベント発行フラグ */
  copyEventFlg?: boolean

  /** 印刷イベント発行フラグ */
  printEventFlg?: boolean

  /** マスタ他イベント発行フラグ */
  masterEventFlg?: boolean

  /** 削除イベント発行フラグ */
  deleteEventFlg?: boolean
}
/**
 *週間の入力
 */
export interface WeektableList {
  /** 削除イベント発行フラグ */
  detailList: WeeklyTableInput[]
}
/**
 *週間の入力
 */
export interface WeeklyTableInput {
  /**
   * データID
   */
  id: string

  /**
   *開始時間
    */
  startHour: timeType

  /**
   *終了時間
    */
  endHour: timeType
  /**
   *月曜日
    */
  dayOfWeek1: boolean
  /**
   *火曜日
    */
  dayOfWeek2: boolean
  /**
   *水曜日
    */
  dayOfWeek3: boolean
  /**
   *木曜日
    */
  dayOfWeek4: boolean
  /**
   *土曜日
    */
  dayOfWeek5: boolean
  /**
   *金曜日
    */
  dayOfWeek6: boolean
  /**
   *日曜日
    */
  dayOfWeek7: boolean
  /**
   *週単位以外(他)
    */
  dayOfWeekOther: boolean

  /**
   *内容
    */
  content: string

  /**
   *メモ
    */
  memo: string

  /**
   *頻度
    */
  frequency: string

  /**
   *表示設定セクション
    */
  letterSize: string

  /**
   *文字位置セクション
    */
  charPosition: string

  /**
   *省略セクション
    */
  omitted: string

  /**
   *文字色セクション
    */
  letterColor: string

  /**
   *背景色セクション
    */
  backgroundColor: string

  /**
   *時間表示セクション
    */
  timeDisplay: string

  /**
   * 週単位以外のサービス区分
   */
  igaiKbn: string

  /**
   * 週単位以外のサービス（日付指定）
   */
  igaiDate: string

  /**
   * 週単位以外のサービス（曜日指定）
   */
  igaiWeek: string

  /**
   * 福祉用具貸与の単価
   */
  svTani: string

  /**
   * 福祉用具貸与マスタID
   */
  fygId: string

  /**
   * 詳細ID
   */
  parentId?: string

  /**
   * 更新区分
   */
  updateKbn?: string
}
/**
 *時刻種類
 */
export interface timeType {
  /**
   *値
   */
  value: string | undefined
}
