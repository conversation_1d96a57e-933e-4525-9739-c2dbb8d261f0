import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 *GUI04519_転送履歴画面
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace OrX0203Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('OrX0203', seq)
  /**
   * （初期値）開閉フラグ
   */
  export const IS_OPEN = false
  /**
   * 転送履歴表示モード0
   */
  export const TENSOUMODE_0 = '0'
  /**
   * 転送履歴表示モード1
   */
  export const TENSOUMODE_1 = '1'
  /**
   * 転送履歴表示モード2
   */
  export const TENSOUMODE_2 = '2'
  /**
   * 転送履歴表示モード3
   */
  export const TENSOUMODE_3 = '3'
}
