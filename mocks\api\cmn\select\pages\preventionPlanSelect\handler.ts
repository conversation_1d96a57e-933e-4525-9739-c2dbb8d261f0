/**
 * Or28451:有機体:予防計画書メイン（画面/特殊コンポーネント）
 * GUI01088_目標とする生活（予防計画書）
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import res1 from './data/res1.json'
import res2 from './data/res2.json'
import A3 from './data/A3.json'
import A42 from './data/A42.json'
import A430 from './data/A430.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { PreventionPlanSelectInEntity } from '~/repositories/cmn/entities/PreventionPlanSelectEntity'

/**
 * GUI01088_目標とする生活（予防計画書）画面処理の取得APIモック
 *
 * @description
 * GUI01088_目標とする生活（予防計画書）画面処理のメイン画面に表示されるデータを返却する。
 * dataName："preventionPlanSelect"
 */
export function handler(inEntity: PreventionPlanSelectInEntity) {
  let result = defaultData
  if (inEntity.operaFlg === 'K1') {
    result = res1
  } else if (inEntity.operaFlg === 'K2') {
    result = res2
  } else if (inEntity.userId === '0000000102') {
    result = A3
  } else if (inEntity.userId === '0000000105') {
    result = A42
  } else if (inEntity.userId === '0000000026') {
    result = A430
  }
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...result,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
