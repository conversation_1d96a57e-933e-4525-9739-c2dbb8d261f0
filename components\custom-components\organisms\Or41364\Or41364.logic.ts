import { Or41364Const } from './Or41364.constants'
import type { Or41364StateType, Or41364TwowayType } from './Or41364.type'
import {
  useInitialize,
  useOneWayBindAccessor,
  useTwoWayBindAccessor,
} from '~/composables/useComponentLogic'

import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'

/**
 * Or41364:有機体:（職員管理）職員編集ダイアログ
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 */
export namespace Or41364Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or41364Const.CP_ID(0),
      uniqueCpId,
      childCps: [{ cpId: Or00248Const.CP_ID(1) }],
      // TwoWayの初期値
      // initTwoWayValue: {
      // },
      // OneWayの初期値
      initOneWayState: {
        isOpen: false,
        loginUserId: '',
      },
      // Eventの初期値
      // initEventStatusEvents: {
      // },
    })

    // 子コンポーネントのセットアップ
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
    }
  }

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or41364TwowayType>(Or41364Const.CP_ID(0))

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or41364StateType>(Or41364Const.CP_ID(0))

  /**
   * 自身のEventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  // export const event = useEventStatusAccessor<Or41364EventType>(Or41364Const.CP_ID(0))
}
