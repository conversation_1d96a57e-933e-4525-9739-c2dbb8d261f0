import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * ［アセスメント］画面（居宅）（3）
 *
 * @description
 * ［アセスメント］画面（居宅）（3）郵便番号情報リストを取得する用APIエンティティ
 *
 * <AUTHOR>
 */
/**
 * ［アセスメント］画面（居宅）（3）(郵便番号情報リストを取得する)取得入力エンティティ
 */
export interface AssessmentMotZipFocusOutEntitySelectInEntity extends InWebEntity {
  /** 郵便番号 */
  zip: string
}

/**
 * ［アセスメント］画面（居宅）（3）(郵便番号情報リストを取得する)出力エンティティ
 */
export interface AssessmentMotZipFocusOutEntitySelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** 郵便番号情報リスト */
    motZipList: MotZipEntity[]
  }
}

/**
 * 郵便番号情報
 */
export interface MotZipEntity {
  /** 全国地方公共団体 */
  dantaiCd: string
  /** 現郵便番号(5桁) */
  oldZipCd: string
  /** 新郵便番号(7桁) */
  newZipCd: string
  /** 都道府県名（カナ） */
  kennameKana: string
  /** 市区町村名（カナ） */
  citynameKana: string
  /** 町域名（カナ） */
  areanameKana: string
  /** 都道府県名（漢字） */
  kennameKnj: string
  /** 市区町村名（漢字） */
  citynameKnj: string
  /** 町域名（漢字） */
  areanameKnj: string
}
