<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  reactive,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or28382Logic } from '~/components/custom-components/organisms/Or28382/Or28382.logic'
import { Or28382Const } from '~/components/custom-components/organisms/Or28382/Or28382.constants'
import type { Or28382OnewayType, HistType } from '~/types/cmn/business/components/Or28382Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * 総合計画複写画面ポップアップ
 * KMD NGUYEN DUY ANH 2025/03/12 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI00974'
// ルーティング
const routing = 'GUI00974/pinia'
// 画面物理名
const screenName = 'GUI00974'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or28382 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00974' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or28824Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or28382.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI00974',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28382Const.CP_ID(0) }],
})

Or28382Logic.initialize(init.childCpIds.Or28382.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or28382Const.CP_ID(0)]: or28382.value,
})

// ダイアログ表示フラグ
const showDialogOr28382 = computed(() => {
  // Or28382のダイアログ開閉状態
  return Or28382Logic.state.get(or28382.value.uniqueCpId)?.isOpen ?? false
})

// Or28382
const or28382OnewayTypeModel: Or28382OnewayType = {
  periodManageFlag: '0',
  //法人ID
  houjinId: '1',
  //施設ID
  shisetuId: '6',
  //事業者ID
  svJigyoId: '23',
  //種別ID
  syubetsuId: '2',
  //利用者ID
  userId: '534',
}

function Or28382OnClick(periodManageFlag: string) {
  or28382OnewayTypeModel.periodManageFlag = periodManageFlag

  // Or28382のダイアログ開閉状態を更新する
  Or28382Logic.state.set({
    uniqueCpId: or28382.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 法人ID
  houjinId: { value: '1' } as Mo00045Type,
  // 施設ID
  shisetuId: { value: '6' } as Mo00045Type,
  // 利用者ID
  userId: { value: '534' } as Mo00045Type,
  // 事業者ID
  svJigyoId: { value: '23' } as Mo00045Type,
  // 種別ID
  syubetsuId: { value: '2' } as Mo00045Type,
  // 計画期間管理フラグ
  periodManageFlag: { value: '0' } as Mo00045Type,
  // 戻り値
  returnData: {} as HistType,
})

/** GUI00974 疎通起動  */
function onClickOr28382() {
  or28382OnewayTypeModel.houjinId = local.houjinId.value
  or28382OnewayTypeModel.shisetuId = local.shisetuId.value
  or28382OnewayTypeModel.userId = local.userId.value
  or28382OnewayTypeModel.svJigyoId = local.svJigyoId.value
  or28382OnewayTypeModel.syubetsuId = local.syubetsuId.value
  or28382OnewayTypeModel.periodManageFlag = local.periodManageFlag.value
  /// Or28382のダイアログ開閉状態を更新する
  Or28382Logic.state.set({
    uniqueCpId: or28382.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const handleCopyConfirm = (val: HistType) => {
  local.returnData = val
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>

  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="Or28382OnClick('1')"
        >GUI_00974総合計画複写_サンプル画面,親画面.期間管理フラグが「管理する」場合 !
      </v-btn>
      <g-custom-or-28382
        v-if="showDialogOr28382"
        v-bind="or28382"
        :oneway-model-value="or28382OnewayTypeModel"
        @on-click-overwrite="handleCopyConfirm"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="Or28382OnClick('0')"
        >GUI_00974総合計画複写_サンプル画面,親画面.期間管理フラグが「管理しない」場合 !
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">法人ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.houjinId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">施設ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.shisetuId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">事業者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.svJigyoId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">種別ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.syubetsuId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">計画期間管理フラグ</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.periodManageFlag"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr28382"> GUI00974 疎通起動 </v-btn>
  </div>

  <div style="margin-left: 20px">戻り値</div>
  <div style="margin-left: 20px">
    {{ local.returnData }}
  </div>
</template>
