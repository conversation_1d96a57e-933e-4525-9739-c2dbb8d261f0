<script setup lang="ts">
import { computed, ref,reactive} from 'vue'
import { Or27487Const } from '~/components/custom-components/organisms/Or27487/Or27487.constants'
import type { Or27487OnewayType, Or27487Type } from '~/types/cmn/business/components/Or27487Type'
import { Or27487Logic } from '~/components/custom-components/organisms/Or27487/Or27487.logic'
import { useScreenStore } from '~/stores/session/screen'
import { useInitialize } from '~/composables/useComponentLogic'
import { definePageMeta } from '#imports'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01098'
// ルーティング
const routing = 'GUI01098/pinia'
// 画面物理名
const screenName = 'GUI01098'
const dialogHeight = ref('auto')
// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27487 = ref({ uniqueCpId: Or27487Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01098' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27487Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27487.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01098',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27487Const.CP_ID(0) }],
})

// ダイアログ表示フラグ
const showDialogOr27487 = computed(() => {
  // Or27487のダイアログ開閉状態
  return Or27487Logic.state.get(or27487.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 親画面からの初期値
 */
const or27487Data: Or27487OnewayType = {
  // サービス種別
  serviceKbn:
    'サービス種別1\nサービス種別2\nサービス種別3\nサービス種別4\nサービス種別5\nサービス種別6\nサービス種別7',
  // 事業所
  office: '事業所',
  // 本人の取組サービス種別
  personServiceType:
    '本人の取組サービス種別\n本人の取組サービス種別\n本人の取組サービス種別\n本人の取組サービス種別',
  // 本人の取組事業所
  personOffice: '本人の取組事業所',
  // 家族・地域の支援、民間サービス等サービス種別
  familyServiceType: '家族・地域の支援、民間サービス等サービス種別',
  // 家族・地域の支援、民間サービス等事業所
  familyOffice: '家族・地域の支援、民間サービス等事業所',
  // 介護保険サービス地域支援事業区市町村サービスサービス種別
  insuranceServiceType: '介護保険サービス地域支援事業区市町村サービスサービス種別',
  // 介護保険サービス地域支援事業区市町村サービス事業所
  insuranceOffice: '介護保険サービス地域支援事業区市町村サービス事業所',
  // 計画表:保険サービス(事業所名)
  planInsuranceService: '0',
  // 計画表:事業所単位 計画表様式
  planOfficeType: '9',
  //利用者ID
  userId: '1',
}

const or27487Type = ref<Or27487Type>({
  //サービス種別内容
  serviceModelValue: '',
  //事業所内容
  officeModelValue: '',
  //本人の取組サービス種別
  personServiceModelValue: '',
  //本人の取組事業所
  personOfficeModelValue: '',
  //家族・地域の支援、民間サービス等サービス種別
  familyServiceModelValue: '',
  //家族・地域の支援、民間サービス等事業所
  familyOfficeModelValue: '',
  //介護保険サービス地域支援事業区市町村サービスサービス種別
  preventiveServiceModelValue: '',
  //介護保険サービス地域支援事業区市町村サービス事業所
  preventiveOfficeModelValue: '',
})

/**
 *  ボタン押下時の処理(Or27487)
 *
 * @param planOfficeType - 計画表:事業所単位 計画表様式
 */
function onClickOr27487(planOfficeType: string) {
  or27487Data.planOfficeType = planOfficeType
  switch (planOfficeType) {
    case '0':
      dialogHeight.value = '770px'
      break
    case '1':
      dialogHeight.value = '840px'
      break
    case '2':
      dialogHeight.value = '770px'
      break
    default:
      dialogHeight.value = '770px'
  }
  // Or27487のダイアログ開閉状態を更新する
  Or27487Logic.state.set({
    uniqueCpId: or27487.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType
const local = reactive({
  // 計画表:事業所単位 計画表様式
  planOfficeType: { value: '1' } as Mo00045Type,
  // サービス種別
  serviceKbn: { value: 'サービス種別1\nサービス種別2\nサービス種別3\nサービス種別4\nサービス種別5\nサービス種別6\nサービス種別7' } as Mo00045Type,
  // 事業所
  office: { value: '事業所' } as Mo00045Type,
  // 本人の取組サービス種別
  personServiceType: { value: '本人の取組サービス種別\n本人の取組サービス種別\n本人の取組サービス種別\n本人の取組サービス種別' } as Mo00045Type,
  // 本人の取組事業所
  personOffice: { value: '本人の取組事業所' } as Mo00045Type,
  // 家族・地域の支援、民間サービス等サービス種別
  familyServiceType: { value: '家族・地域の支援、民間サービス等サービス種別' } as Mo00045Type,
  // 家族・地域の支援、民間サービス等事業所
  familyOffice: { value: '家族・地域の支援、民間サービス等事業所' } as Mo00045Type,
  // 介護保険サービス地域支援事業区市町村サービスサービス種別
  insuranceServiceType: { value: '介護保険サービス地域支援事業区市町村サービスサービス種別' } as Mo00045Type,
  // 介護保険サービス地域支援事業区市町村サービス事業所
  insuranceOffice: { value: '介護保険サービス地域支援事業区市町村サービス事業所' } as Mo00045Type,
  // 計画表:保険サービス(事業所名)
  planInsuranceService: { value: '1' } as Mo00045Type,
  // 利用者ID
  userId: { value: '1' } as Mo00045Type,
})
function onClickOr27487_1() {
  or27487Data.planOfficeType = local.planOfficeType.value
  or27487Data.serviceKbn = local.serviceKbn.value
  or27487Data.office = local.office.value
  or27487Data.personServiceType = local.personServiceType.value
  or27487Data.personOffice = local.personOffice.value
  or27487Data.familyServiceType = local.familyServiceType.value
  or27487Data.familyOffice = local.familyOffice.value
  or27487Data.insuranceServiceType = local.insuranceServiceType.value
  or27487Data.insuranceOffice = local.insuranceOffice.value
  or27487Data.planInsuranceService = local.planInsuranceService.value
  or27487Data.userId = local.userId.value
  switch (local.planOfficeType.value) {
    case '0':
      dialogHeight.value = '770px'
      break
    case '1':
      dialogHeight.value = '840px'
      break
    case '2':
      dialogHeight.value = '770px'
      break
    default:
      dialogHeight.value = '770px'
  }
  // Or27487のダイアログ開閉状態を更新する
  Or27487Logic.state.set({
    uniqueCpId: or27487.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27487('0')"
        >GUI01098_サービス種別入力支援（予防計画書）(計画表様式が「A3横1枚」の場合)
      </v-btn>
      <g-custom-or-27487
        v-if="showDialogOr27487"
        v-bind="or27487"
        v-model="or27487Type"
        :oneway-model-value="or27487Data"
        :style="{ height: dialogHeight }"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27487('1')"
        >GUI01098_サービス種別入力支援（予防計画書）(計画表様式が「A4横3枚」の場合)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27487('2')"
        >GUI01098_サービス種別入力支援（予防計画書）(計画表様式が「A4横2枚」の場合)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">計画表:事業所単位 計画表様式</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.planOfficeType"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">サービス種別</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.serviceKbn"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">事業所</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.office"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">本人の取組サービス種別</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.personServiceType"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">本人の取組事業所</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.personOffice"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">家族・地域の支援、民間サービス等サービス種別</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.familyServiceType"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">家族・地域の支援、民間サービス等事業所</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.familyOffice"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">介護保険サービス地域支援事業区市町村サービスサービス種別</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.insuranceServiceType"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">介護保険サービス地域支援事業区市町村サービス事業所</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.insuranceOffice"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">計画表:保険サービス(事業所名)</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.planInsuranceService"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr27487_1"> GUI01098 疎通起動 </v-btn>
  </div>
  <div class="pt-5 w-25 pl-5">
    <div> 戻り値 </div>
    <div> {{or27487Type}} </div>
  </div>
</template>
