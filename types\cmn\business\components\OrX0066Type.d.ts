import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { cks52List } from '~/types/cmn/business/components/Or05349Type'

/**
 * OrX0066OnewayType：有機体：タイトル
 * GUI01044_週間計画パターン設定
 * OneWayBind領域に保持するデータ構造
 */
export interface OrX0066OnewayType {
  /** 計画書様式 */
  cksFlg: string
  /** 利用者ID */
  userId: string
  /** 週間サービス計画表様式 */
  wscksFlg: number
  /** 要介護度デフォルト */
  yoKaigoDo: string
  /** 履歴リスト */
  cks51List: History[]
  /** 詳細リスト(APIから返却全てのデータ) */
  cks52List: cks52List[]
  /** 日常リスト(APIから返却全てのデータ) */
  cks54List: NichiJyou[]
  /** 詳細リスト(画面表示のデータ) */
  newCks52List: cks52List[]
  /** 日常リスト(画面表示のデータ) */
  newCks54List: NichiJyou[]
  /** サービス職種IDor種類表示フラグ */
  shuruihyoujiFlg?: boolean
  /** 事業所CD */
  defSvJigyoCd?: string
  /** 事業者表示フラグ */
  jigyoShowFlg?: number
  /** サービス項目表示フラグ */
  komokuShowFlg?: boolean
  /** 内容表示フラグ */
  contentShowFlg?: boolean
  /** 有効期間 */
  validPeriod?: string
}

/**
 * OrX0066Type 双方向バインド領域
 * GUI01044_週間計画パターン設定
 */
export interface OrX0066Type {
  /**
   * 変更Flg
   */
  editFlg: boolean
  /**
   * 共通情報.計画書様式
   */
  cksFlg: number
  /**
   * 親画面.有効期間ID
   */
  orgTermId: string
  /**
   * 画面.有効期間ID
   */
  termId: string
  /**
   * 画面.履歴リスト
   */
  newCks51List?: History[]
  /**
   * 画面.日常リスト
   */
  newCks54List?: NichiJyou[]
  /**
   * 画面.詳細リスト
   */
  newCks52List?: cks52List[]

  /**
   * 履歴リスト
   */
  resultCks51List?: History[]
  /**
   * 日常リスト
   */
  resultCks54List?: NichiJyou[]
  /**
   * 詳細リスト
   */
  resultCks52List?: cks52List[]
  /**
   * 初期表示フラグ
   */
  initFlg?: boolean

  /**
   * タイトル(選択中)
   */
  selectedTitle?: Mo00040Type
  /**
   * 要介護度(選択中)
   */
  selectedNursingCareRequired?: string

  /**
   * 週間計画ID(選択中)
   */
  selectedTermId: string

  /**
   * 取込パタン
   */
  pattern?: string
}

/**
 * 週間計画パターン設定情報
 */
export interface SettingInfo {
  /**
   * 週間計画ID
   */
  ks51Id: string
  /**
   * 更新区分(取込パタン
   */
  updateKbn?: string
  /**
   * 履歴リスト
   */
  cks51List: History[]
  /**
   * 日常リスト
   */
  cks54List: NichiJyou[]
  /**
   * 詳細リスト
   */
  cks52List: cks52List[]
}

/**
 * タイトル_リスト
 */
export interface TitleDataType {
  /**
   * 週間計画ID
   */
  ks51Id: string
  /**
   * 名称
   */
  nameKnj: string
  /**
   * 有効期間ID
   */
  termId: string
}

/**
 * 履歴情報
 */
export interface History {
  /**
   * 週間計画ID
   */
  ks51Id: string
  /**
   * 要介護度区分
   */
  youkaCd: string
  /**
   * 名称
   */
  nameKnj: string
  /**
   * サービス区分
   */
  switchKbn: string
  /**
   * 表示順
   */
  seq: string
  /**
   * 有効期間ID
   */
  termId: string
  /**
   * 週単位以外ｻｰﾋﾞｽ
   */
  wIgaiKnj: string
}

/**
 * 日常情報
 */
export interface NichiJyou {
  /**
   * 週間計画ID
   */
  ks51Id: string
  /**
   * 日常データID
   */
  ks54Id: string
  /**
   * 連番
   */
  seq: string
  /**
   * 主な日常生活上の活動
   */
  nichijoKnj: string
}
