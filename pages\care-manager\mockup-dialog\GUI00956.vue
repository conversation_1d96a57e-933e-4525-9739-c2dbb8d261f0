<script setup lang="ts">
import { computed, ref, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or36211Const } from '~/components/custom-components/organisms/Or36211/Or36211.constants'
import { Or36211Logic } from '~/components/custom-components/organisms/Or36211/Or36211.logic'
import type { Or36211OnewayType } from '~/types/cmn/business/components/Or36211Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00956'
// ルーティング
const routing = 'GUI00956/pinia'
// 画面物理名
const screenName = 'GUI00956'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or36211 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00956' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or36211Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or36211.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI00956',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or36211Const.CP_ID(0) }],
})
Or36211Logic.initialize(init.childCpIds.Or36211.uniqueCpId)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or36211Const.CP_ID(0)]: or36211.value,
})

// ダイアログ表示フラグ
const showDialogOr36211 = computed(() => {
  // Or36211のダイアログ開閉状態
  return Or36211Logic.state.get(or36211.value.uniqueCpId)?.isOpen ?? false
})

// or36211 onewayModelValue
const or36211OnewayModel: Or36211OnewayType = {
  /** 共通情報.画面名 */
  isShoriname: 'GUI00956',
  /** 共通情報.施設ID */
  shisetuId: '1',
  /** 共通情報.事業所ID */
  svJigyoId: '1',
  /** 共通情報.職員ID */
  shokuinId: '1',
  /** 共通情報.利用者ID */
  userId: '3',
  /** 共通情報.メニュー２名称 */
  menu2Knj: '1',
  /** 共通情報.メニュー３名称 */
  menu3Knj: '1',
  /** 共通情報.自由パラメータ */
  valS: '1',
  /** 共通情報.システム年月日 */
  appYmd: '2000/02/02',
  /** 期間管理フラグ */
  periodManageFlag: '',
  /**種別ID */
  syubetsuId: '2',
  /**  画面名 */
  screenName: '',
  /** 閲覧権限 */
  viewAuthority: '',
}
/**
 *  ボタン押下時の処理(Or36211)
 *
 * @param periodManageFlag - 期間管理フラグ
 *
 * @param screenName - 画面名
 *
 * @param viewAuthority - 閲覧権限
 */
function onClickOr36211(periodManageFlag: string, screenName: string, viewAuthority: string) {
  // 引継情報を設定する。
  or36211OnewayModel.periodManageFlag = periodManageFlag
  or36211OnewayModel.screenName = screenName
  or36211OnewayModel.viewAuthority = viewAuthority
  // Or36211のダイアログ開閉状態を更新する
  Or36211Logic.state.set({
    uniqueCpId: or36211.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  isShoriname: {
    value: '',
  },
  shisetuId: {
    value: '',
  },
  svJigyoId: {
    value: '',
  },
  shokuinId: {
    value: '',
  },
  userId: {
    value: '',
  },
  menu2Knj: {
    value: '',
  },
  menu3Knj: {
    value: '',
  },
  valS: {
    value: '',
  },
  appYmd: {
    value: '',
  },
})

function onClickOr36211Test() {
  or36211OnewayModel.isShoriname = local.isShoriname.value
  or36211OnewayModel.shisetuId = local.shisetuId.value
  or36211OnewayModel.svJigyoId = local.svJigyoId.value
  or36211OnewayModel.shokuinId = local.shokuinId.value
  or36211OnewayModel.userId = local.userId.value
  or36211OnewayModel.menu2Knj = local.menu2Knj.value
  or36211OnewayModel.menu3Knj = local.menu3Knj.value
  or36211OnewayModel.valS = local.valS.value
  or36211OnewayModel.appYmd = local.appYmd.value
  Or36211Logic.state.set({
    uniqueCpId: or36211.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36211('1', 'All', '1')"
        >GUI00956_評価内容参照画面_(テーブル全表示場合)
      </v-btn>
      <g-custom-or-36211
        v-if="showDialogOr36211"
        v-bind="or36211"
        :oneway-model-value="or36211OnewayModel"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36211('1', '実施計画～①', '2')"
        >GUI00956_評価内容参照画面_(期間管理フラグが管理_画面名が「実施計画～①」_実施計画～②の閲覧権限ある場合)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36211('0', '実施計画～①', '2')"
        >GUI00956_評価内容参照画面_(期間管理フラグが管理しない_画面名が「実施計画～①」_実施計画～②の閲覧権限ある場合)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36211('1', '実施計画～①', '3')"
        >GUI00956_評価内容参照画面_(期間管理フラグが管理_画面名が「実施計画～①」_実施計画～③の閲覧権限ある場合)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36211('0', '実施計画～①', '3')"
        >GUI00956_評価内容参照画面_(期間管理フラグが管理しない_画面名が「実施計画～①」_実施計画～③の閲覧権限ある場合)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36211('1', '実施計画～②', '1')"
        >GUI00956_評価内容参照画面_(期間管理フラグが管理_親画面.画面名が「実施計画～②」)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36211('0', '実施計画～②', '1')"
        >GUI00956_評価内容参照画面_(期間管理フラグが管理しない_親画面.画面名が「実施計画～②」)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36211('1', '実施計画～③', '1')"
        >GUI00956_評価内容参照画面_(期間管理フラグが管理_親画面.画面名が「実施計画～③」)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36211('0', '実施計画～③', '1')"
        >GUI00956_評価内容参照画面_(期間管理フラグが管理しない_親画面.画面名が「実施計画～③」)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">画面名</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.isShoriname"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">施設ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.shisetuId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">事業所ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.svJigyoId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">職員ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.shokuinId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">メニュー２名称</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.menu2Knj"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">メニュー３名称</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.menu3Knj"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">自由パラメータ</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.valS"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">システム年月日</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.appYmd"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr36211Test"> GUI00956 疎通起動 </v-btn>
  </div>
</template>
