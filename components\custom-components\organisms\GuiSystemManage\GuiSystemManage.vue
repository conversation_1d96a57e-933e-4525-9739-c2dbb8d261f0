<script setup lang="ts">
/**
 * GuiSystemManage:有機体:システム管理画面（画面コンポーネント）
 */
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { GuiLoginHistoryConst } from '../GuiLoginHistory/GuiLoginHistory.constants'
import { Or11871Const } from '../../../base-components/organisms/Or11871/Or11871.constants'
import { GuiH0001Const } from '../GuiH0001/GuiH0001.constants'
import { GuiStaffListConst } from '../GuiStaffList/GuiStaffList.constants'
import { useCommonProps } from '~/composables/useCommonProps'
import { useSetupChildProps } from '~/composables/useComponentVue'
import type { Mo00043OnewayType } from '~/types/business/components/Mo00043Type'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())

/**************************************************
 * 変数定義
 **************************************************/
const or11871 = ref({ uniqueCpId: '' })
const guiLoginHistory = ref({ uniqueCpId: '' })
const guiH0001 = ref({ uniqueCpId: '' })
const guiStaffList = ref({ uniqueCpId: '' })

const mo00043 = ref({ id: '' })
/**
 * タブ情報
 *  label：表示名
 *  value：値
 *  to：リンク先
 */
const mo00043Oneway = {
  tabItems: [
    {
      title: t('label.staff-management'),
      id: 'staff-management',
    },
    {
      title: t('label.login-history'),
      id: 'login-history',
    },
    {
      title: t('label.operation-history'),
      id: 'operation-history',
    },
  ],
} as Mo00043OnewayType

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  // 画面メニューエリアの表示設定
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      screenTitleLabel: t('label.system-management'),
      showViewSelect: false,
      viewSelectItems: [],
      showSaveBtn: false,
      showCreateBtn: false,
      showPrintBtn: false,
      showMasterBtn: false,
      showOptionMenuBtn: false,
    },
  })
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [GuiLoginHistoryConst.CP_ID(1)]: guiLoginHistory.value,
  [GuiH0001Const.CP_ID(1)]: guiH0001.value,
  [GuiStaffListConst.CP_ID(1)]: guiStaffList.value,
})
</script>

<template>
  <c-v-row
    no-gutters
    class="h-100 pa-2 flex-column flex-nowrap"
    style="box-sizing: border-box"
  >
    <!-- 画面メニューエリア -->
    <g-base-or-11871 v-bind="or11871" />
    <!-- タブ -->
    <base-mo00043
      v-model="mo00043"
      class="mb-2 flex-shrink-0"
      :oneway-model-value="mo00043Oneway"
    />
    <c-v-window
      v-model="mo00043.id"
      class="h-100 overflow-y pr-2 target-back-to-top-button"
      style="overflow-y: auto"
    >
      <!-- 職員管理 -->
      <c-v-window-item value="staff-management">
        <g-custom-gui-staff-list v-bind="guiStaffList" />
      </c-v-window-item>

      <!-- ログイン履歴 -->
      <c-v-window-item value="login-history">
        <g-custom-gui-login-history v-bind="guiLoginHistory" />
      </c-v-window-item>

      <!-- 操作履歴 -->
      <c-v-window-item value="operation-history">
        <g-custom-gui-h-0001 v-bind="guiH0001" />
      </c-v-window-item>
    </c-v-window>
  </c-v-row>
</template>

<style scoped lang="scss"></style>
