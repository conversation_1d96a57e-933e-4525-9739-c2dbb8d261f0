import { Or41363Const } from '../Or41363/Or41363.constants'
import { Or41363Logic } from '../Or41363/Or41363.logic'
import { GuiStaffListConst } from './GuiStaffList.constants'
import { useInitialize } from '~/composables/useComponentLogic'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'

import { Or41199Const } from '~/components/custom-components/organisms/Or41199/Or41199.constants'
import { Or41199Logic } from '~/components/custom-components/organisms/Or41199/Or41199.logic'

/**
 * GuiStaffList:有機体:職員管理画面（画面コンポーネント）
 * 処理ロジック
 *
 * @description
 * initialize処理を提供する
 */
export namespace GuiStaffListLogic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: GuiStaffListConst.CP_ID(0),
      uniqueCpId,
      childCps: [
        { cpId: Or00248Const.CP_ID(1) },
        { cpId: Or41179Const.CP_ID(1) },
        { cpId: Or41363Const.CP_ID(1) },
        { cpId: Or41199Const.CP_ID(1) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(1)].uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(1)].uniqueCpId)
    Or41363Logic.initialize(childCpIds[Or41363Const.CP_ID(1)].uniqueCpId)
    Or41199Logic.initialize(childCpIds[Or41199Const.CP_ID(1)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
}
