<script setup lang="ts">
/**
 * Or31524：有機体
 * GUI00777_ｱｾｽﾒﾝﾄ(ｲﾝﾀｰﾗｲ)画面M
 *
 * @description
 * アセスメント(インターライ)画面M
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */
import { onMounted, computed, reactive, ref, watch, defineProps, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or31524Const } from './Or31524.constants'
import type { Or31524StateType, SubInfo, RespInfo, SubM, Or31524TwoWayType } from './Or31524.type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Or56086BtnInfo, Or56086OnewayType } from '~/types/cmn/business/components/Or56086Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type { Or10412Type, Or10412OnewayType } from '~/types/cmn/business/components/Or10412Type'
import { Or10412Logic } from '~/components/custom-components/organisms/Or10412/Or10412.logic'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import {
  useSetupChildProps,
  useScreenOneWayBind,
  useScreenInitFlg,
  useScreenTwoWayBind,
  useScreenStore,
} from '#imports'
import { Or10412Const } from '~/components/custom-components/organisms/Or10412/Or10412.constants'
import type { Or31494TypeOnewayType } from '~/components/custom-components/organisms/Or31494/Or31494.type'

// import { Or31494Const } from '~/components/custom-components/organisms/Or31494/Or31494.constants'
// import { Or31494Logic } from '~/components/custom-components/organisms/Or31494/Or31494.logic'
import type { Or31493TypeOnewayType } from '~/components/custom-components/organisms/Or31493/Or31493.type'
// import { Or31493Const } from '~/components/custom-components/organisms/Or31493/Or31493.constants'
// import { Or31493Logic } from '~/components/custom-components/organisms/Or31493/Or31493.logic'
import type {
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import type { Or30980Type, Or30980OnewayType } from '~/types/cmn/business/components/Or30980Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Or31524OnewayType } from '~/types/cmn/business/components/Or31524Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  AssessmentInterRAIAHistorySelectEntity,
  AssessmentInterRAIAHistorySelectOutEntity,
} from '~/repositories/cmn/entities/assessmentInterRAIAHistoryEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  AssessmentInterRAIMInEntity,
  AssessmentInterRAIMOutEntity,
  AssessmentInterRAIMUpdateEntity,
  AssessmentInterRAIMUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIMEntity'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or30980Const } from '~/components/custom-components/organisms/Or30980/Or30980.constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { Or30982BtnInfo, Or30982OnewayType } from '~/types/cmn/business/components/Or30982Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'
import type { Or30981OnewayType } from '~/types/cmn/business/components/Or30981Type'
import type { Mo01355OnewayType } from '@/types/business/components/Mo01355Type'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import { Or30981Const } from '~/components/custom-components/organisms/Or30981/Or30981.constants'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or31524OnewayType
  uniqueCpId: string
}
/**
 * プロパティ
 */
const props: Props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * 翻訳
 */
const { t } = useI18n()

/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * 画面共通ルーティングコンポーネント
 */
const cmnRouteCom = useCmnRouteCom()
/**
 * 画面ストア
 */
const screenStore = useScreenStore()
/**
 * 初期化フラグ
 */
const isInit = useScreenInitFlg()

/**
 * 子コンポーネント 10412
 */
const or10412 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 31494
 */
// const or31494 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 31493
 */
// const or31493 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 51775
 */
const or51775 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 21814
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 30980
 */
const or30980 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 30981_0
 */
const or30981_0 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 30981_1
 */
const or30981_1 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 30981_2
 */
const or30981_2 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 30981_3
 */
const or30981_3 = ref({ uniqueCpId: '' })

/**
 * 画面項目区分
 */
const vieTypeKbn = ref<string>('')

/**
 * デフォルトの単方向バインドモデル値
 */
const defaultOnewayModelValue: Or31524OnewayType = {
  periodManageFlag: '0',
  shisetuId: '',
  userId: '',
  officeId: '',
  syubetsuId: '',
  applicableOfficeIdList: [],
  OfficeId: '',
  applicableOfficeGroupId: '',
  planPeriodId: '',
  historyId: '',
  surveyAssessmentKind: '3',
}

/**
 * 各種ラベルに対応するキーを定義
 */
const lableKeys = {
  ...Or31524Const.LABEL_KEYS,
}

/**
 * キーアクション
 */
const actionkeys = {
  ...Or31524Const.ACTION_KEYS,
}

/**
 * 入力リクエストデータ
 */
const inputRequestData: AssessmentInterRAIMUpdateEntity = {
  /**
   * 法人ID
   */
  houjinId: '',
  /**
   * 施設ID
   */
  shisetuId: '',
  /**
   * 利用者ID
   */
  userId: '',
  /**
   * 事業者ID
   */
  svJigyoId: '',

  /**
   * 種別ID
   */
  subKbn: '',
  /**
   * 実行フラグ
   */
  executeFlag: '',
  /**
   * 履歴更新回目
   */
  historyModifiedCnt: '',
  /**
   * 更新区分
   */
  updateKbn: '',
  /**
   * 履歴更新区分
   */
  modifiedKbn: '',
  /**
   * 削除区分
   */
  deleteKbn: '',
  /**
   * 計画対象期間ID
   */
  sc1Id: '',
  /**
   * 基準日
   */
  kijunbiYMD: '',
  /**
   * 作成者ID
   */
  sakuseiId: '',
  /**
   * 更新回数
   */
  modified_cnt: '',
  /**
   * 調査アセスメント種別
   */
  assType: 0,
  /**
   * サブ情報（M）
   */
  subM: {} as SubM,
}

/**
 * デフォルトのモデル値
 */
const defaultModelValue = {
  // アセスメント種別リスト
  surveyAssessmentKindList: [] as CodeType[],
}

/**
 * 削除区分
 */
const deleteKbn = ref('')

/**
 * ローカル変数を格納するためのオブジェクト
 */
const local = {
  executeFlag: '',
  kikanKanriFlg: '',
  houjinId: '',
  shisetuId: '',
  userId: '',
  svJigyoId: '',
  kijunbiYmd: '',
  sakuseiId: '',
  historyModifiedCnt: '',
  historyInfo: {} as HistoryInfoEntity,
  planPeriodInfo: {} as PlanPeriodInfoEntity,
}

/**
 * レスポンス情報を格納するためのオブジェクト
 */
const respInfo = {
  // 期間管理フラグや履歴情報などを格納
  executeFlag: '',
  kikanKanriFlg: '',
  houjinId: '',
  shisetuId: '',
  userId: '',
  svJigyoId: '',
  kijunbiYmd: '',
  sakuseiId: '',
  historyModifiedCnt: '',
  // 計画期間情報
  planPeriodInfo: {
    sc1Id: '0',
    periodNo: '0',
    periodCnt: '0',
    startYmd: '',
    endYmd: '',
  } as PlanPeriodInfoEntity,
  // 履歴情報
  historyInfo: {
    sc1Id: '0',
    raiId: '',
    assType: '',
    capType: '',
    plnType: '',
    shokuinId: '',
    shokuinName: '',
    assDateYmd: '',
    krirekiNo: '0',
    krirekiCnt: '0',
    c1: '',
  } as HistoryInfoEntity,
  // サブ情報
  subInfo: {
    raiId: '',
    m1: '',
    m2a: '',
    m2b: '',
    m2c: '',
    m2d: '',
    m2e: '',
    m2f: '',
    m2g: '',
    m2h: '',
    m2i: '',
    m2j: '',
    m2k: '',
    m2l: '',
    m2m: '',
    m2n: '',
    m2o: '',
    m2p: '',
    m2q: '',
    m2r: '',
    m2s: '',
    m2t: '',
    m2sOrther: '',
    m2tOrther: '',
    m3: '',
    m4a: '',
    m4b: '',
    m4c: '',
    m4d: '',
    m1MemoKnj: '',
    m1MemoFont: '',
    m1MemoColor: '',
    m2MemoKnj: '',
    m2MemoFont: '',
    m2MemoColor: '',
    m3MemoKnj: '',
    m3MemoFont: '',
    m3MemoColor: '',
    m4MemoKnj: '',
    m4MemoFont: '',
    m4MemoColor: '',
  } as SubInfo,
}

/**
 * レスポンス情報のサブ情報を格納するためのオブジェクト
 */
const { refValue } = useScreenTwoWayBind<Or31524TwoWayType>({
  cpId: Or31524Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or31524StateType>({
  cpId: Or31524Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 更新
     *
     * @param value - データ
     */
    param: (value) => {
      if (value) {
        Object.assign(local, value)
        Object.assign(respInfo, value)

        inputRequestData.houjinId = value.houjinId ?? ''
        inputRequestData.shisetuId = value.shisetuId ?? ''
        inputRequestData.userId = value.userId
        inputRequestData.svJigyoId = value.svJigyoId
        inputRequestData.executeFlag = value.executeFlag
        inputRequestData.historyModifiedCnt = value.historyModifiedCnt
        inputRequestData.sc1Id = value.historyInfo.sc1Id
        inputRequestData.kijunbiYMD = value.kijunbiYmd
        inputRequestData.sakuseiId = value.sakuseiId
        inputRequestData.assType = parseInt(value.historyInfo.assType)
      }
      // 調査アセスメント種別設定
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          inputRequestData.deleteKbn = actionkeys.UPDATE
          inputRequestData.updateKbn = actionkeys.UPDATE
          void save()
          break
        // 新規
        case 'add':
          inputRequestData.deleteKbn = actionkeys.CREATE
          inputRequestData.updateKbn = actionkeys.CREATE
          void add()
          break
        // 複写
        case 'copy':
          inputRequestData.deleteKbn = actionkeys.CREATE
          inputRequestData.updateKbn = actionkeys.CREATE
          void copy()
          break
        // 削除
        case 'delete':
          inputRequestData.deleteKbn = actionkeys.DELETE
          inputRequestData.updateKbn = actionkeys.DELETE
          void del()
          break
        // データ再取得
        case 'getData':
          void getData()
          break
        default:
          break
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or10412Const.CP_ID(0)]: or10412.value,
  // [Or31494Const.CP_ID(0)]: or31494.value,
  // [Or31493Const.CP_ID(0)]: or31493.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or30980Const.CP_ID(0)]: or30980.value,
  [Or30981Const.CP_ID(0)]: or30981_0.value,
  [Or30981Const.CP_ID(1)]: or30981_1.value,
  [Or30981Const.CP_ID(2)]: or30981_2.value,
  [Or30981Const.CP_ID(3)]: or30981_3.value,
})

/**
 * ローカルデータを格納するための
 */
const localOneway = reactive({
  or31524Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  or30980Oneway: {
    btnItems: [],
  } as Or30980OnewayType,
  // アセスメント表タイトル
  mo01338Oneway: {
    value: t('label.survey-assessment-kind-home-version'),
    valueFontWeight: 'normal',
    customClass: {
      outerClass: 'background-transparent',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OnewayActivity: {
    value: t('label.activity'),
    valueFontWeight: '',
    customClass: { outerClass: '' } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OnewayActivities: {
    value: t('label.activity-1'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: 'asection_left_title_left_label',
      labelStyle: '',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayActivities: {
    minWidth: '24px',
    minHeight: '24px',
    width: '24px',
    height: '24px',
    icon: true,
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  mo00046OnewayActivities: {
    showItemLabel: false,
    noResize: true,
    autoGrow: false,
    class: 'M1TextArea',
    maxlength: '4000',
    rows: 4,
    maxRows: '4',
  } as Mo00046OnewayType,
  or56086OnewayType: {
    btnItems: [
      {
        label: t('label.none-btn'),
        value: '0',
      } as Or56086BtnInfo,
      {
        label: t('label.more-btn'),
        value: '1',
      } as Or56086BtnInfo,
      {
        label: t('label.about-halfway-btn'),
        value: '2',
      } as Or56086BtnInfo,
      {
        label: t('label.little-less-btn'),
        value: '3',
      } as Or56086BtnInfo,
    ],
    layoutType: '1',
  } as Or56086OnewayType,
  mo00038OnewayTypeActivities: {
    showItemLabel: false,
    isVerticalLabel: false,
    mo00045Oneway: {
      width: '60px',
      isVerticalLabel: false,
      showItemLabel: false,
      maxLength: '1',
    } as Mo00045OnewayType,
  } as Mo00038OnewayType,
  mo01338OnewayActivitiesM2: {
    value: t('label.activity-2'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: 'asection_left_title_left_label',
      labelStyle: '',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayActivitiesM2: {
    minWidth: '24px',
    minHeight: '24px',
    width: '24px',
    height: '24px',
    icon: true,
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  mo00046OnewayActivitiesM2: {
    showItemLabel: false,
    noResize: true,
    autoGrow: false,
    class: 'M2TextArea',
    maxlength: '4000',
    rows: 4,
    maxRows: '4',
  } as Mo00046OnewayType,
  mo00611OnewayDontLikeOrAmNotInvolvedWithBtn: {
    name: 'dont-like-or-am-not-involved-with-btn',
    btnLabel: t('btn.dont-like-or-am-not-involved-with-btn'),
    class: 'button-style',
    width: '278px',
    value: '0',
  },
  mo00611OnewayPreferencesAndUninvolvedBtn: {
    class: 'button-style',
    name: 'preferences-and-uninvolved-btn',
    btnLabel: t('btn.preferences-and-uninvolved-btn'),
    width: '278px',
    value: '1',
  },
  mo00611OnewayPreferencesAndEngagementBnt: {
    class: 'button-style',
    name: 'preferences-and-engagement-bnt',
    btnLabel: t('btn.preferences-and-engagement-bnt'),
    width: '278px',
    value: '2',
  },
  mo01338OnewayDaytimeSleepingTimeTitle: {
    value: t('label.daytime-sleeping-time-title'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: 'asection_left_title_left_label',
      labelStyle: '',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayDaytimeSleepingTime: {
    minWidth: '24px',
    minHeight: '24px',
    width: '24px',
    height: '24px',
    icon: true,
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  mo00046OnewayDaytimeSleepingTime: {
    showItemLabel: false,
    noResize: true,
    autoGrow: false,
    class: 'M3TextArea',
    maxlength: '4000',
    rows: 4,
    maxRows: '4',
  } as Mo00046OnewayType,
  or56086OnewayTypeDaytimeSleepingTime: {
    btnItems: [
      {
        label: t('label.occurs-all-the-time-or-most-of-the-time-btn'),
        value: '0',
      } as Or56086BtnInfo,
      {
        label: t('label.repeated-dozing-btn'),
        value: '1',
      } as Or56086BtnInfo,
      {
        label: t('label.when-you-are-asleep-most-of-the-time-but-are-awake-sometimes-btn'),
        value: '2',
      } as Or56086BtnInfo,
      {
        label: t('label.mostly-asleep-or-unresponsive-btn'),
        value: '3',
      } as Or56086BtnInfo,
    ],
    layoutType: '1',
  } as Or56086OnewayType,
  mo00038OnewayDaytimeSleepingTime: {
    showItemLabel: false,
    isVerticalLabel: false,
    mo00045Oneway: {
      width: '60px',
      isVerticalLabel: false,
      showItemLabel: false,
      maxLength: '1',
    } as Mo00045OnewayType,
  } as Mo00038OnewayType,
  mo01338OnewayInterestTitle: {
    value: t('label.interest-title'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: 'asection_left_title_left_label',
      labelStyle: '',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayInterestMemoBtn: {
    minWidth: '24px',
    minHeight: '24px',
    width: '24px',
    height: '24px',
    icon: true,
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  mo00046OnewayInterestMemo: {
    showItemLabel: false,
    noResize: true,
    autoGrow: false,
    class: 'M4TextArea',
    maxlength: '4000',
    rows: 4,
    maxRows: '4',
  } as Mo00046OnewayType,
  mo00611OnewayNoBtn: {
    name: 'no-btn',
    btnLabel: t('label.no-btn'),
    class: 'button-style',
    width: '98px',
    value: '0',
  } as Mo00611OnewayType,
  mo00611OnewayYesBtn: {
    name: 'yes-btn',
    btnLabel: t('label.yes-btn'),
    class: 'button-style',
    width: '98px',
    value: '1',
  } as Mo00611OnewayType,
  or10412Oneway: {
    selectItemNo: '',
    userId: '',
    fontColor: '',
    historyTable: '',
    historyTableColum: '',
    meMoContent: '',
    textSize: '',
    flag: '',
  } as Or10412OnewayType,
  or31494Oneway: {
    name: 'or31494',
    showCloseBtn: true,
    btnCloseName: '',
    btnCloseColor: 'white',
    iconCloseName: '',
    width: '400',
    scrollable: true,
    mo00611OnewayNo: {
      name: 'no-btn',
      btnLabel: t('label.no-btn'),
      class: 'button-style',
      width: '98px',
    } as Mo00611OnewayType,
    mo00611OnewayYes: {
      name: 'yes-btn',
      btnLabel: t('label.yes-btn'),
      class: 'button-style',
      width: '98px',
    } as Mo00611OnewayType,
    mo01338Oneway: {
      value: t('label.interest-title'),
      valueFontWeight: 'bolder',
    } as Mo01338OnewayType,
    mo00024Oneway: {
      persistent: true,
      showCloseBtn: true,
      class: 'mr-1',
      width: '400px',
      mo01344Oneway: {
        toolbarTitle: t('label.interest-title'),
        showCardActions: true,
        toolbarTitleCenteredFlg: false,
      } as Mo01344OnewayType,
    } as Mo00024OnewayType,
  } as Or31494TypeOnewayType,
  or31493Oneway: {
    name: 'or31493',
    showCloseBtn: true,
    btnCloseName: '',
    btnCloseColor: 'white',
    iconCloseName: '',
    width: '400',
    scrollable: true,
    mo00611OnewayDontLikeOrAmNotInvolvedWithBtn: {
      name: 'dont-like-or-am-not-involved-with-btn',
      btnLabel: t('btn.dont-like-or-am-not-involved-with-btn'),
      class: 'button-style',
      width: '278px',
    } as Mo00611OnewayType,
    mo00611OnewayPreferencesAndUninvolvedBtn: {
      class: 'button-style',
      name: 'preferences-and-uninvolved-btn',
      btnLabel: t('btn.preferences-and-uninvolved-btn'),
      width: '278px',
    } as Mo00611OnewayType,
    mo00611OnewayPreferencesAndEngagementBnt: {
      class: 'button-style',
      name: 'preferences-and-engagement-bnt',
      btnLabel: t('btn.preferences-and-engagement-bnt'),
      width: '278px',
    } as Mo00611OnewayType,
    mo01338Oneway: {
      value: t('label.activity-modal-2'),
      valueFontWeight: 'bolder',
    } as Mo01338OnewayType,
    mo00024Oneway: {
      persistent: true,
      showCloseBtn: true,
      class: 'mr-1',
      width: '450px',
      mo01344Oneway: {
        toolbarTitle: t('label.activity-modal-2'),
        showCardActions: true,
        toolbarTitleCenteredFlg: false,
      } as Mo01344OnewayType,
    } as Mo00024OnewayType,
  } as Or31493TypeOnewayType,
  or51775OnewayTypeOther: {
    title: '',
    classificationID: '2',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
  } as Or51775OnewayType,
  mo00039Oneway: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    inline: true,
    checkOff: false,
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo00039OnewayType,
  or30982Oneway: {
    btnItems: [
      {
        name: '',
        label: t('label.none-btn'),
        value: '0',
        width: '312px',
      } as Or30982BtnInfo,
      {
        name: '',
        label: t('label.about-halfway-btn'),
        value: '2',
        width: '312px',
      } as Or30982BtnInfo,
      {
        name: '',
        label: t('label.more-btn'),
        value: '1',
        width: '312px',
      } as Or30982BtnInfo,
      {
        name: '',
        label: t('label.little-less-btn'),
        value: '3',
        width: '312px',
      } as Or30982BtnInfo,
    ],
    layoutType: '1',
    width: '650px',
    disabled: false,
  } as Or30982OnewayType,
  or30982OnewayM4: {
    btnItems: [
      {
        name: '',
        label: t('label.interRAI-method-care-assessment-table-m4-iie'),
        value: '0',
        width: '180px',
      } as Or30982BtnInfo,
      {
        name: '',
        label: t('label.interRAI-method-care-assessment-table-m4-hai'),
        value: '1',
        width: '180px',
      } as Or30982BtnInfo,
    ],
    layoutType: '2',
    width: '376px',
    disabled: false,
  } as Or30982OnewayType,
  orX0156OnewayTypeM1: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * モーダルでカラーピッカー
     */
    mo01355Oneway: {} as Mo01355OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
    disabled: false,
  } as Or30981OnewayType,
  orX0156OnewayTypeM2: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * モーダルでカラーピッカー
     */
    mo01355Oneway: {} as Mo01355OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
    disabled: false,
  } as Or30981OnewayType,
  orX0156OnewayTypeM3: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * モーダルでカラーピッカー
     */
    mo01355Oneway: {} as Mo01355OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
    disabled: false,
  } as Or30981OnewayType,
  orX0156OnewayTypeM4: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * モーダルでカラーピッカー
     */
    mo01355Oneway: {} as Mo01355OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
    disabled: false,
  } as Or30981OnewayType,
})

/**
 * 調査アセスメント種別入力値
 */
const or30980Type = ref<Or30980Type>({
  value: '3',
} as Or30980Type)

/**
 * 活動への平均参加時間メモ
 */
// const mo00046TypeActivities = ref<Or30981Type>({
//   content: '',
//   fontSize: '14',
//   fontColor: '#FFFFFF',
// } as Or30981Type)

/**
 * 好む活動と関与（現在の能力に適応）メモ
 */
// const mo00046TypeActivitiesM2 = ref<Or30981Type>({
//   content: '',
//   fontSize: '14',
//   fontColor: '#FFFFFF',
// } as Or30981Type)

/**
 * 活動への平均参加時間区分
 */
// const mo00038Activities = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * カード、ゲーム、クイズ入力
//  */
// const mo00038OnewayInputCardsGamesAndQuiz = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * コンピュータ、ネット関係入力
//  */
// const mo00038OnewayInputComputerAndInternetRelated = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 会話、電話入力
//  */
// const mo00038OnewayInputConversationPhoneCall = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 創作活動入力
//  */
// const mo00038OnewayInputCreativeActivities = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * ダンス、舞踏入力
//  */
// const mo00038OnewayInputDanceButoh = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 人生についての議論/回顧(回想法)入力
//  */
// const mo00038OnewayInputLifeDiscussionRecollection = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 運動入力
//  */
// const mo00038OnewayInputSports = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 庭仕事、畑仕事入力
//  */
// const mo00038OnewayInputGardeningH = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 他者の手助け入力
//  */
// const mo00038OnewayInputHelpingOthers = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 音楽や歌入力
//  */
// const mo00038OnewayInputMusicAndSongs = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * ペット入力
//  */
// const mo00038OnewayInputPets = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 読書、執筆入力
//  */
// const mo00038OnewayInputReadingWriting = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 宗教活動入力
//  */
// const mo00038OnewayInputReligionActivities = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 旅行や買い物入力
//  */
// const mo00038OnewayInputGardening = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 屋外の散歩入力
//  */
// const mo00038OnewayInputOutdoorWalks = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * テレビ、ラジオ、ビデオ/DVD鑑賞入力
//  */
// const mo00038OnewayInputWatchingTVRadioVideos = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 料理/お菓子作り入力
//  */
// const mo00038OnewayInputCookingSweets = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * パズル/クロスワード入力
//  */
// const mo00038OnewayInputPuzzlesCrosswords = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * その他１入力
//  */
// const mo00038OnewayInputOther1 = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * その他１課題t入力
//  */
// const mo00045InputOtherNew1 = ref<Mo00045Type>({
//   value: '',
// })

// /**
//  * その他2入力
//  */
// const mo00038InputOther2 = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * その他2課題t入力
//  */
// const mo00045InputOtherNew2 = ref<Mo00045Type>({
//   value: '',
// })

// /**
//  * 日中寝ている時間メモ
//  */
// const mo00046TypeDaytimeSleepingTime = ref<Or30981Type>({
//   content: '',
//   fontSize: '14',
//   fontColor: '#FFFFFF',
// } as Or30981Type)

// /**
//  * 日中寝ている時間入力
//  */
// const mo00038OnewayDaytimeSleepingTime = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 興味・関心メモ
//  */
// const mo00046InterestMemo = ref<Or30981Type>({
//   content: '',
//   fontSize: '14',
//   fontColor: '#FFFFFF',
// } as Or30981Type)

// /**
//  * より多くのレクリエーションに参加することに興味がある入力
//  */
// const mo00038OnewayInterestParticipatingAFallsPreventionProgram = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 転倒予防プログラムに参加することに興味がある入力
//  */
// const mo00038OnewayInterestParticipatingAFallsPreventionProgramB = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 記憶力改善のためのプログラムに参加することに興味がある入力
//  */
// const mo00038OnewayInterestParticipatingAProgramToImproveYourMemory = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 身体機能向上プログラムに参加することに興味がある入力
//  */
// const mo00038OnewayInterestParticipatingAPhysicalFitnessProgram = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

/**
 * Or10412のデータ型を定義
 */
const or10412Type = ref<Or10412Type>({
  selectItemNo: '',
  userId: '',
  fontColor: '',
  historyTable: '',
  historyTableColum: '',
  meMoContent: '',
  textSize: '',
  flag: '',
})

/**
 * M2セクションの設定情報を定義
 */
const sectionConfigM2: {
  key:
    | 'm2a'
    | 'm2b'
    | 'm2c'
    | 'm2d'
    | 'm2e'
    | 'm2f'
    | 'm2g'
    | 'm2h'
    | 'm2i'
    | 'm2j'
    | 'm2k'
    | 'm2l'
    | 'm2m'
    | 'm2n'
    | 'm2o'
    | 'm2p'
    | 'm2q'
    | 'm2r'
    | 'm2s'
    | 'm2t'
  labelName: string
  titleLabel: string
  isOther?: boolean
  keyOther: string
}[] = [
  {
    key: 'm2a',
    titleLabel: 'label.cards-games-and-quiz-label',
    labelName: lableKeys.A,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2b',
    titleLabel: 'label.computer-and-internet-related-label',
    labelName: lableKeys.B,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2c',
    titleLabel: 'label.conversation-phone-call-label',
    labelName: lableKeys.C,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2d',
    titleLabel: 'label.creative-activities-label',
    labelName: lableKeys.D,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2e',
    titleLabel: 'label.dance-butoh-label',
    labelName: lableKeys.E,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2f',
    titleLabel: 'label.life-discussion-recollection-label',
    labelName: lableKeys.F,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2g',
    titleLabel: 'label.sports-label',
    labelName: lableKeys.G,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2h',
    titleLabel: 'label.gardening-h-label',
    labelName: lableKeys.H,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2i',
    titleLabel: 'label.helping-others-label',
    labelName: lableKeys.I,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2j',
    titleLabel: 'label.music-and-songs-label',
    labelName: lableKeys.J,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2k',
    titleLabel: 'label.pets-label',
    labelName: lableKeys.K,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2l',
    titleLabel: 'label.reading-writing-label',
    labelName: lableKeys.L,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2m',
    titleLabel: 'label.religion-activities-label',
    labelName: lableKeys.M,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2n',
    titleLabel: 'label.gardening-label',
    labelName: lableKeys.N,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2o',
    titleLabel: 'label.outdoor-walks-label',
    labelName: lableKeys.O,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2p',
    titleLabel: 'label.watching-tv-radio-videos-label',
    labelName: lableKeys.P,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2q',
    titleLabel: 'label.cooking-sweets-label',
    labelName: lableKeys.Q,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2r',
    titleLabel: 'label.puzzles-crosswords-label',
    labelName: lableKeys.R,
    isOther: false,
    keyOther: '',
  },
  {
    key: 'm2s',
    titleLabel: 'label.other-1-label',
    labelName: lableKeys.S,
    isOther: true,
    keyOther: '1',
  },
  {
    key: 'm2t',
    titleLabel: 'label.other-2-label',
    labelName: lableKeys.T,
    isOther: true,
    keyOther: '2',
  },
]

/**
 * M2セクションのメモ参照を取得
 */
// const memoRefs = computed(() => mo00038TextFieldMapM2)

/**
 * M2セクションの「その他」メモ参照を取得
 */
// const memoOtherRefs = computed(() => mo00038TextFieldMapM2Other)

/**
 * M2セクションのローカルデータをマッピング
 */
const localOnewayMapM2 = sectionConfigM2.map((config) => {
  const key = config.key
  return {
    key,
    isOther: config.isOther,
    labelName: config.labelName,
    keyOther: config.keyOther,
    mo00615Oneway: {
      itemLabel: t(config.titleLabel),
      showItemLabel: true,
      showRequiredLabel: false,
    } as Mo00615OnewayType,
    mo00038Oneway: {
      showItemLabel: false,
      isVerticalLabel: false,
      mo00045Oneway: {
        width: '60px',
        isVerticalLabel: false,
        showItemLabel: false,
        maxLength: '1',
      } as Mo00045OnewayType,
    } as Mo00038OnewayType,
    mo00009Oneway: {
      class: 'ml-1',
      minWidth: '24px',
      minHeight: '24px',
      width: '24px',
      height: '24px',
      icon: true,
      btnIcon: 'message',
    } as Mo00009OnewayType,
    mo00045OnewayOtherNew: {
      width: '523px',
      showItemLabel: false,
      isRequired: false,
      maxLength: '60',
    } as Mo00045OnewayType,
    mo00009OnewayOtherNew: {
      class: 'ml-1 mr-1',
      minWidth: '24px',
      minHeight: '24px',
      width: '24px',
      height: '24px',
      icon: true,
      btnIcon: 'edit_square',
      size: '36px',
      color: '#859FC9',
      labelColor: '#EBF2FD',
      density: 'default',
    } as Mo00009OnewayType,
  }
})

/**
 * M4セクションのメモ参照を取得
 */
// const memoM4Refs = computed(() => mo00038TextFieldMapM4)

/**
 * M4セクションの設定情報を定義
 */
const sectionConfigM4: {
  key: 'm4a' | 'm4b' | 'm4c' | 'm4d'
  labelName: string
  titleLabel: string
}[] = [
  {
    key: 'm4a',
    titleLabel: 'label.interested-participating-more-recreational-activities-label',
    labelName: lableKeys.A,
  },
  {
    key: 'm4b',
    titleLabel: 'label.invert-interested-participating-prevention-program-label',
    labelName: lableKeys.B,
  },
  {
    key: 'm4c',
    titleLabel: 'label.interested-participating-a-program-improve-my-memory-label',
    labelName: lableKeys.C,
  },
  {
    key: 'm4d',
    titleLabel: 'label.interested-participating-a-physical-fitness-program-label',
    labelName: lableKeys.D,
  },
]

/**
 * M4セクションのローカルデータをマッピング
 */
const localOnewayMapM4 = sectionConfigM4.map((config) => {
  const key = config.key
  return {
    key,
    labelName: config.labelName,
    mo00615Oneway: {
      itemLabel: t(config.titleLabel),
      showItemLabel: true,
      showRequiredLabel: false,
    } as Mo00615OnewayType,
    mo00038Oneway: {
      showItemLabel: false,
      isVerticalLabel: false,
      mo00045Oneway: {
        width: '60px',
        isVerticalLabel: false,
        showItemLabel: false,
        maxLength: '1',
      } as Mo00045OnewayType,
    } as Mo00038OnewayType,
    mo00009Oneway: {
      class: 'ml-1',
      minWidth: '24px',
      minHeight: '24px',
      width: '24px',
      height: '24px',
      icon: true,
      btnIcon: 'message',
    } as Mo00009OnewayType,
  }
})

onMounted(() => {
  if (isInit) {
    // 初期情報取得
    void systemCodeSelect()
  }
})

/**
 * 汎用コード取得API実行
 */
const systemCodeSelect = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // アセスメント種別
  defaultModelValue.surveyAssessmentKindList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )
  // 親情報.調査アセスメント種別により、コードマスタの区分名称を表示する
  for (const item of defaultModelValue.surveyAssessmentKindList) {
    if (localOneway.or31524Oneway.surveyAssessmentKind === item.value) {
      localOneway.mo01338Oneway.value = item.label
      or30980Type.value.value = item.value
      break
    }
  }

  // モードによってインターフェースを変更する
  onChangeMode(or30980Type.value.value)
}

/**
 * モードによってインターフェースを変更する
 *
 * @param value - 調査アセスメント種別
 */
const onChangeMode = (value: string) => {
  switch (value) {
    // 調査アセスメント種別 = 1:居宅版
    case '1':
      localOneway.mo00046OnewayActivities.style = 'background: rgb(var(--v-theme-black-536))'
      localOneway.mo00046OnewayDaytimeSleepingTime.style =
        'background: rgb(var(--v-theme-black-536))'

      localOneway.mo00038OnewayTypeActivities.mo00045Oneway!.class = 'toGray'
      localOneway.mo00038OnewayDaytimeSleepingTime.mo00045Oneway!.class = 'toGray'

      localOneway.orX0156OnewayTypeM1.disabled = true
      localOneway.orX0156OnewayTypeM3.disabled = true

      break
    // 調査アセスメント種別 = 3:高齢者住宅版
    case '3':
      localOneway.mo00046OnewayActivities.style = 'background: rgb(var(--v-theme-black-536))'
      localOneway.mo00046OnewayDaytimeSleepingTime.style =
        'background: rgb(var(--v-theme-black-536))'

      localOneway.mo00038OnewayTypeActivities.mo00045Oneway!.class = 'toGray'
      localOneway.mo00038OnewayDaytimeSleepingTime.mo00045Oneway!.class = 'toGray'

      localOneway.orX0156OnewayTypeM1.disabled = true
      localOneway.orX0156OnewayTypeM3.disabled = true
      break
    // 上記以外の場合
    default:
      localOneway.mo00046OnewayActivities.style = 'background: transparent'
      localOneway.mo00046OnewayDaytimeSleepingTime.style = 'background: transparent'

      localOneway.mo00038OnewayTypeActivities.mo00045Oneway!.class = ''
      localOneway.mo00038OnewayDaytimeSleepingTime.mo00045Oneway!.class = ''

      localOneway.orX0156OnewayTypeM1.disabled = false
      localOneway.orX0156OnewayTypeM3.disabled = false
      break
  }
}

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * AC023_「調査アセスメント種別」選択変更
 */
watch(
  () => or30980Type.value,
  (newValue) => {
    if (newValue.value) {
      onChangeMode(newValue.value)
    }
  }
)

/**
 * 色変換
 *
 * @param rgb - rbg値
 *
 * @returns 色値
 */
// const rgbToHex = (rgb: string): string => {
//   let hexStr = ''
//   if (rgb) {
//     if (
//       rgb.length >= 10 &&
//       rgb.startsWith('rgb(') &&
//       rgb.endsWith(')') &&
//       rgb.split(',').length === 3
//     ) {
//       const tempArr = rgb.replace('rgb(', '').replace(')', '').trim().split(',')
//       if (tempArr.length === 3) {
//         hexStr = `#${((1 << 24) + (parseInt(tempArr[0]) << 16) + (parseInt(tempArr[1]) << 8) + parseInt(tempArr[2])).toString(16).slice(1)}`
//       }
//       return hexStr
//     }
//   }
//   return rgb
// }

/**
 * 色変換
 *
 * @param value - value値
 *
 * @returns 色値
 */
// const toPx = (value: number | string | null | undefined): string => {
//   const num = Number(value)
//   return isNaN(num) ? '12px' : `${num}px`
// }

/**
 * データ型
 */
// type SectionKey = `M${1 | 2 | 3 | 4}`
// type SectionKeyM2 = `M2${
//   | 'a'
//   | 'b'
//   | 'c'
//   | 'd'
//   | 'e'
//   | 'f'
//   | 'g'
//   | 'h'
//   | 'i'
//   | 'j'
//   | 'k'
//   | 'l'
//   | 'm'
//   | 'n'
//   | 'o'
//   | 'p'
//   | 'q'
//   | 'r'
//   | 's'
//   | 't'}`
// type SectionKeyM4 = `M4${'a' | 'b' | 'c' | 'd'}`
// type SectionKeyM13 = `M${1 | 3}`
// type SectionKeyM2Other = `M2${'s' | 't'}`

/**
 * M1およびM3セクションのテキストフィールド参照をマッピング
 */
// const mo00038TextFieldMap: Record<SectionKeyM13, Ref<Mo00038Type>> = {
//   M1: mo00038Activities,
//   M3: mo00038OnewayDaytimeSleepingTime,
// }

/**
 * 各セクションのメモエリア参照をマッピング
 */
// const mo00046AreaMap: Record<SectionKey, Ref<Or30981Type>> = {
//   M1: mo00046TypeActivities,
//   M2: mo00046TypeActivitiesM2,
//   M3: mo00046TypeDaytimeSleepingTime,
//   M4: mo00046InterestMemo,
// }

/**
 * M2セクションのテキストフィールド参照をマッピング
 */
// const mo00038TextFieldMapM2: Record<SectionKeyM2, Ref<Mo00038Type>> = {
//   M2a: mo00038OnewayInputCardsGamesAndQuiz,
//   M2b: mo00038OnewayInputComputerAndInternetRelated,
//   M2c: mo00038OnewayInputConversationPhoneCall,
//   M2d: mo00038OnewayInputCreativeActivities,
//   M2e: mo00038OnewayInputDanceButoh,
//   M2f: mo00038OnewayInputLifeDiscussionRecollection,
//   M2g: mo00038OnewayInputSports,
//   M2h: mo00038OnewayInputGardeningH,
//   M2i: mo00038OnewayInputHelpingOthers,
//   M2j: mo00038OnewayInputMusicAndSongs,
//   M2k: mo00038OnewayInputPets,
//   M2l: mo00038OnewayInputReadingWriting,
//   M2m: mo00038OnewayInputReligionActivities,
//   M2n: mo00038OnewayInputGardening,
//   M2o: mo00038OnewayInputOutdoorWalks,
//   M2p: mo00038OnewayInputWatchingTVRadioVideos,
//   M2q: mo00038OnewayInputCookingSweets,
//   M2r: mo00038OnewayInputPuzzlesCrosswords,
//   M2s: mo00038OnewayInputOther1,
//   M2t: mo00038InputOther2,
// }

/**
 * M2セクションの「その他」テキストフィールド参照をマッピング
 */
// const mo00038TextFieldMapM2Other: Record<SectionKeyM2Other, Ref<Mo00045Type>> = {
//   M2s: mo00045InputOtherNew1,
//   M2t: mo00045InputOtherNew2,
// }

/**
 * M4セクションのテキストフィールド参照をマッピング
 */
// const mo00038TextFieldMapM4: Record<SectionKeyM4, Mo00038Type> = {
//   M4a: refValue.value!.m4a,
//   M4b: refValue.value!.m4b,
//   M4c: refValue.value!.m4c,
//   M4d: refValue.value!.m4d,
// }

/**
 * 画面項目の初期化
 */
const inputProject = () => {
  // const sectionKeysM13: SectionKeyM13[] = ['M1', 'M3']
  // const sectionKeys: SectionKey[] = ['M1', 'M2', 'M3', 'M4']
  // const sectionKeysM2: SectionKeyM2[] = Array.from(
  //   { length: 20 },
  //   (_, i) => `M2${String.fromCharCode(97 + i)}` as SectionKeyM2
  // )
  // const sectionKeysM4: SectionKeyM4[] = Array.from(
  //   { length: 4 },
  //   (_, i) => `M4${String.fromCharCode(97 + i)}` as SectionKeyM4
  // )

  // sectionKeysM13.forEach((key) => {
  //   // 1. テキストフィールドをリセットする
  //   mo00038TextFieldMap[key].value.mo00045.value = ''
  // })

  // //2. クリアテキストM2
  // sectionKeysM2.forEach((key) => {
  //   mo00038TextFieldMapM2[key].value.mo00045.value = ''
  // })

  // //3. クリアテキスト M4
  // sectionKeysM4.forEach((key) => {
  //   mo00038TextFieldMapM4[key].mo00045.value = ''
  // })

  // sectionKeys.forEach((key) => {
  //   // 4. メモテキストをクリアする
  //   mo00046AreaMap[key].value.content = ''

  //   // 5. テキストエリアのスタイルをリセットする（存在する場合）
  //   const el = document.querySelector(`.${key}TextArea`)
  //   el?.querySelectorAll('textarea')?.forEach((ta) => {
  //     if (ta.value) {
  //       ta.style.color = Or31524Const.DEFAULT.DEFAULT_COLOR
  //       ta.style.fontSize = Or31524Const.DEFAULT.DEFAULT_SIZE
  //     }
  //   })
  // })

  const dataInit = {
    raiId: '',
    m1: '',
    m2a: '',
    m2b: '',
    m2c: '',
    m2d: '',
    m2e: '',
    m2f: '',
    m2g: '',
    m2h: '',
    m2i: '',
    m2j: '',
    m2k: '',
    m2l: '',
    m2m: '',
    m2n: '',
    m2o: '',
    m2p: '',
    m2q: '',
    m2r: '',
    m2s: '',
    m2t: '',
    m2sOrther: '',
    m2tOrther: '',
    m3: '',
    m4a: '',
    m4b: '',
    m4c: '',
    m4d: '',
    m1MemoKnj: '',
    m1MemoFont: '14',
    m1MemoColor: '#FFFFFF',
    m2MemoKnj: '',
    m2MemoFont: '14',
    m2MemoColor: '#FFFFFF',
    m3MemoKnj: '',
    m3MemoFont: '14',
    m3MemoColor: '#FFFFFF',
    m4MemoKnj: '',
    m4MemoFont: '14',
    m4MemoColor: '#FFFFFF',
  } as SubInfo

  setInitTwoway(dataInit)
}

/**
 * AC024_各「メモ入力支援アイコンボタン」押下_GUI00777 ［メモ入力］画面-戻り値変更
 *
 * @param respData - 戻り値
 */
// watch(
//   () => or10412Type.value,
//   (newValue) => {
//     const memoRefMap: Record<
//       'M1' | 'M2' | 'M3' | 'M4',
//       Ref<{
//         content?: string
//         fontSize?: string
//         fontColor?: string
//       }>
//     > = {
//       M1: mo00046TypeActivities,
//       M2: mo00046TypeActivitiesM2,
//       M3: mo00046TypeDaytimeSleepingTime,
//       M4: mo00046InterestMemo,
//     }
//     const selectNo = newValue.selectItemNo as keyof typeof memoRefMap
//     const targetRef = memoRefMap[selectNo]

//     if (targetRef) {
//       targetRef.value.content = newValue.meMoContent
//       const className = `.${selectNo}TextArea`
//       const element = document.querySelector(className)
//       element?.querySelectorAll('textarea')?.forEach((item) => {
//         if (item.value) {
//           item.style.color = newValue.fontColor
//           item.style.fontSize = toPx(newValue.textSize)
//         }
//       })
//     }
//   }
// )

/**
 * AC023_各 GUI00787 ［メモ入力］画面をポップアップで起動する。
 *
 * @param value - メモ入力カテゴリ
 */
// const memoInputSupportIconBtn = (value: 'M1' | 'M2' | 'M3' | 'M4') => {
//   const memoContentMap: Record<
//     'M1' | 'M2' | 'M3' | 'M4',
//     Ref<{ content?: string; fontSize?: string; fontColor?: string }>
//   > = {
//     M1: mo00046TypeActivities,
//     M2: mo00046TypeActivitiesM2,
//     M3: mo00046TypeDaytimeSleepingTime,
//     M4: mo00046InterestMemo,
//   }

//   const param: Or10412OnewayType = {
//     selectItemNo: value,
//     userId: systemCommonsStore.getUserId ?? '',
//     fontColor: '',
//     historyTable: '-',
//     historyTableColum: '-',
//     meMoContent: memoContentMap[value].value.content ?? '',
//     textSize: '',
//     flag: '-',
//   }

//   const lastElement = document.querySelector(`.${value}TextArea`)
//   if (lastElement) {
//     const textareas = lastElement.querySelectorAll('textarea')
//     textareas.forEach((item) => {
//       if (item.value) {
//         param.fontColor = rgbToHex(item.style.color)
//         param.textSize = item.style.fontSize
//       }
//     })
//   }

//   localOneway.or10412Oneway = { ...param }

//   Or10412Logic.state.set({
//     uniqueCpId: or10412.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }

/**
 * AC027_「本人のケアの目標入力支援アイコンボタン」押下
 *
 * @param type - 画面項目区分
 */
const careTargetInputSupportIconClick = (type: 'M1' | 'M2' | 'M3' | 'M4') => {
  vieTypeKbn.value = type
  localOneway.or51775OnewayTypeOther = {
    // タイトル
    title: t('label.memo'),
    // 画面ID
    screenId: 'GUI00777',
    // 分類ID
    bunruiId: '',
    // 大分類ＣＤ
    t1Cd: '610',
    // 中分類CD
    t2Cd: '13',
    // 小分類ＣＤ
    t3Cd: '1',
    // テーブル名
    tableName: 'cpn_tuc_rai_ass_m',
    // カラム名
    columnName: '',
    // アセスメント方式
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? '',
    // 文章内容
    inputContents: '',
    // 利用者ID
    userId: systemCommonsStore.getUserId,
    // モード
    mode: '',
  } as Or51775OnewayType
  switch (type) {
    case 'M1':
      localOneway.or51775OnewayTypeOther.columnName = 'm1_memo_knj'
      localOneway.or51775OnewayTypeOther.inputContents = ''
      break
    case 'M2':
      localOneway.or51775OnewayTypeOther.columnName = 'm2_memo_knj'
      localOneway.or51775OnewayTypeOther.inputContents = ''
      break
    case 'M3':
      localOneway.or51775OnewayTypeOther.columnName = 'm3_memo_knj'
      localOneway.or51775OnewayTypeOther.inputContents = ''
      break
    case 'M4':
      localOneway.or51775OnewayTypeOther.columnName = 'm4_memo_knj'
      localOneway.or51775OnewayTypeOther.inputContents = ''
      break
    default:
      break
  }
  // GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  // Or51775のダイアログを開く

  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    // 本文末に追加の場合
    if ('0' === data.type) {
      switch (vieTypeKbn.value) {
        case 'M1':
          refValue.value!.m1MemoKnj.content = refValue.value!.m1MemoKnj.content + data.value
          break
        case 'M2':
          refValue.value!.m2MemoKnj.content = refValue.value!.m2MemoKnj.content + data.value
          break
        case 'M3':
          refValue.value!.m3MemoKnj.content = refValue.value!.m3MemoKnj.content + data.value
          break
        case 'M4':
          refValue.value!.m4MemoKnj.content = refValue.value!.m4MemoKnj.content + data.value
          break
        default:
          break
      }
    }
    // 本文上書の場合
    else if ('1' === data.type) {
      switch (vieTypeKbn.value) {
        case 'M1':
          refValue.value!.m1MemoKnj.content = data.value
          break
        case 'M2':
          refValue.value!.m2MemoKnj.content = data.value
          break
        case 'M3':
          refValue.value!.m3MemoKnj.content = data.value
          break
        case 'M4':
          refValue.value!.m4MemoKnj.content = data.value
          break
        default:
          break
      }
    }
  }
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * GUI00787 ［メモ入力］画面表示フラグ
 */
const showDialogOr10412 = computed(() => {
  // Or10412のダイアログ開閉状態
  return Or10412Logic.state.get(or10412.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 入力値の範囲チェックを行うための型定義
 */
// interface RangeCheck {
//   ref: Ref<{ mo00045: { value: string } }>
//   min: number
//   max: number
// }

/**
 * 項目とその許容範囲
 */
// const fieldRangeMap: Record<SectionKeyM13 | SectionKeyM2 | SectionKeyM4, RangeCheck> = {
//   M1: { ref: mo00038Activities, min: 0, max: 3 },
//   M3: { ref: mo00038OnewayDaytimeSleepingTime, min: 0, max: 3 },
//   M2a: { ref: mo00038OnewayInputCardsGamesAndQuiz, min: 0, max: 2 },
//   M2b: { ref: mo00038OnewayInputComputerAndInternetRelated, min: 0, max: 2 },
//   M2c: { ref: mo00038OnewayInputConversationPhoneCall, min: 0, max: 2 },
//   M2d: { ref: mo00038OnewayInputCreativeActivities, min: 0, max: 2 },
//   M2e: { ref: mo00038OnewayInputDanceButoh, min: 0, max: 2 },
//   M2f: { ref: mo00038OnewayInputLifeDiscussionRecollection, min: 0, max: 2 },
//   M2g: { ref: mo00038OnewayInputSports, min: 0, max: 2 },
//   M2h: { ref: mo00038OnewayInputGardeningH, min: 0, max: 2 },
//   M2i: { ref: mo00038OnewayInputHelpingOthers, min: 0, max: 2 },
//   M2j: { ref: mo00038OnewayInputMusicAndSongs, min: 0, max: 2 },
//   M2k: { ref: mo00038OnewayInputPets, min: 0, max: 2 },
//   M2l: { ref: mo00038OnewayInputReadingWriting, min: 0, max: 2 },
//   M2m: { ref: mo00038OnewayInputReligionActivities, min: 0, max: 2 },
//   M2n: { ref: mo00038OnewayInputGardening, min: 0, max: 2 },
//   M2o: { ref: mo00038OnewayInputOutdoorWalks, min: 0, max: 2 },
//   M2p: { ref: mo00038OnewayInputWatchingTVRadioVideos, min: 0, max: 2 },
//   M2q: { ref: mo00038OnewayInputCookingSweets, min: 0, max: 2 },
//   M2r: { ref: mo00038OnewayInputPuzzlesCrosswords, min: 0, max: 2 },
//   M2s: { ref: mo00038OnewayInputOther1, min: 0, max: 2 },
//   M2t: { ref: mo00038InputOther2, min: 0, max: 2 },
//   M4a: { ref: mo00038OnewayInterestParticipatingAFallsPreventionProgram, min: 0, max: 1 },
//   M4b: { ref: mo00038OnewayInterestParticipatingAFallsPreventionProgramB, min: 0, max: 1 },
//   M4c: { ref: mo00038OnewayInterestParticipatingAProgramToImproveYourMemory, min: 0, max: 1 },
//   M4d: { ref: mo00038OnewayInterestParticipatingAPhysicalFitnessProgram, min: 0, max: 1 },
// }

/**
 * メッセージ「i-cmn-41717」
 *
 * @description
 * 確認ダイアログを開く関数
 *
 * @returns ユーザーが選択した結果（yes）
 */
// const openInfoDialogValidate = async (): Promise<string> => {
//   // データ変更確認ダイアログを初期化(e.cmn.41717)
//   Or21814Logic.state.set({
//     uniqueCpId: or21814.value.uniqueCpId,
//     state: {
//       // ダイアログタイトル
//       dialogTitle: t('label.top-btn-title'),
//       // ダイアログテキスト
//       dialogText: t('message.e-cmn-41717'),

//       firstBtnType: 'normal1',
//       firstBtnLabel: t('btn.yes'),

//       secondBtnType: 'blank',
//       thirdBtnType: 'blank',
//       iconName: 'error',
//     },
//   })

//   // 確認ダイアログを開く
//   Or21814Logic.state.set({
//     uniqueCpId: or21814.value.uniqueCpId,
//     state: {
//       isOpen: true,
//     },
//   })
//   // 確認ダイアログを閉じたタイミングで結果を返却
//   return new Promise((resolve) => {
//     watch(
//       () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
//       () => {
//         const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

//         let result = Or31524Const.DEFAULT.DIALOG_RESULT.YES

//         if (event?.firstBtnClickFlg) {
//           result = Or31524Const.DEFAULT.DIALOG_RESULT.YES
//         }

//         // 確認ダイアログのフラグをOFF
//         Or21814Logic.event.set({
//           uniqueCpId: or21814.value.uniqueCpId,
//           events: {
//             firstBtnClickFlg: false,
//           },
//         })

//         resolve(result)
//       },
//       { once: true }
//     )
//   })
// }

/**
 * AC025_各「アセスメント項目区分」値変更
 */
// Object.entries(fieldRangeMap).forEach(([key, { ref, min, max }]) => {
//   watch(
//     () => ref.value.mo00045.value,
//     async (newValue) => {
//       if (newValue) {
//         const inputVal = parseInt(newValue)
//         if (isNaN(inputVal) || inputVal < min || inputVal > max) {
//           const dialogResult = await openInfoDialogValidate()
//           switch (dialogResult) {
//             case Or31524Const.DEFAULT.DIALOG_RESULT.YES:
//               // 入力値を元の値（変更前の値）に戻す
//               ref.value.mo00045.value =
//                 previousAssessmentValues[key as keyof typeof previousAssessmentValues]
//               break
//           }
//         }
//       }
//     },
//     { immediate: true }
//   )
// })

/**
 * 「興味・関心項目」選択時の処理
 */
// const handleInterestSelected = ({ keyType, value }: { keyType: string; value: string }) => {
//   const sectionKey = `M4${keyType}` as SectionKeyM4
//   const targetRef = mo00038TextFieldMapM4[sectionKey]
//   if (targetRef) {
//     targetRef.value.mo00045.value = value
//     previousAssessmentValues[sectionKey] = value
//   }
// }

/**
 * Or31494ダイアログの表示状態を管理するためのComputed
 */
// const showDialogOr31494 = computed(() => {
//   return Or31494Logic.state.get(or31494.value.uniqueCpId)?.isOpen ?? false
// })

/**
 * AC024_「活動への関与選択ボタン」押下
 *
 * @description
 * 活動への関与選択ボタンが押下された際に、対応するセクションの値を更新します。
 */
// const ActivitiesEngagementSelectedbtn = ({
//   keyType,
//   value,
// }: {
//   keyType: string
//   value: string
// }) => {
//   const sectionKey = `M2${keyType}` as SectionKeyM2
//   const targetRef = mo00038TextFieldMapM2[sectionKey]
//   if (targetRef) {
//     targetRef.value.mo00045.value = value
//     previousAssessmentValues[sectionKey] = value
//   }
// }

/**
 * Or31493ダイアログの表示状態を管理するためのComputed
 */
// const showDialogOr31493 = computed(() => {
//   return Or31493Logic.state.get(or31493.value.uniqueCpId)?.isOpen ?? false
// })

/**
 * 「その他」入力支援アイコンのリセット状態を管理するためのフラグ
 */
const isResettingOther = ref(false)

/**
 * 「その他」入力支援アイコンのキーを保持するための変数
 */
const keyTypeOthers = ref<string>('')

/**
 * AC028 および AC029_「その他入力支援アイコン」押下
 *
 * @param key - '1' または '2' を受け取り、対応するダイアログを開く
 */
const onClickOther = (key: string) => {
  keyTypeOthers.value = key

  isResettingOther.value = true
  // その他の入力支援ポップアップを開く
  or51775Other.value.modelValue = ''
  // 画面項目の初期化
  void nextTick(() => {
    isResettingOther.value = false
  })
  // 入力支援ポップアップを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })

  // タイトルを「その他1」または「その他2」に設定
  localOneway.or51775OnewayTypeOther.title = `その他${key}`
  //TODO: GUI00937 入力支援［ケアマネ］をポップアップで起動します。
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * その他1課題t入力
 */
const or51775Other = ref({ modelValue: '' })

/**
 * AC028「その他1入力支援アイコン」押下 または AC029「その他2入力支援アイコン」押下
 */
watch(
  () => or51775Other,
  (newValue) => {
    if (isResettingOther.value) return

    if (keyTypeOthers.value === '1') {
      refValue.value!.m2sOrther.value = newValue.value.modelValue
    } else if (keyTypeOthers.value === '2') {
      refValue.value!.m2tOrther.value = newValue.value.modelValue
    }
  },
  { deep: true }
)

/**
 * AC004_「新規ボタン」押下
 */
const add = () => {
  // アセスメント(インターライ)画面履歴の最新情報を取得
  void inputProject()
}

/**
 *  画面初期情報取得
 *
 * @returns 画面初期情報
 */
const getHistoryInfo = async () => {
  const inputData: AssessmentInterRAIAHistorySelectEntity = {
    raiId: local.historyInfo.raiId,
  }
  const resData: AssessmentInterRAIAHistorySelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIAHistorySelect',
    inputData
  )
  if (resData.statusCode === 'success') {
    return resData.data
  }
}

/**
 * 「保存ボタン」押下き確認ダイアログ
 *
 * @param message -情報を提示します
 */
const showOr21814Msg = (message: string): void => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: message,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}

/**
 * AC003_「保存ボタン」押下
 */
const save = async () => {
  // 取得したインターライ方式履歴情報<>NULL
  const historyInfo = await getHistoryInfo()
  if (historyInfo) {
    // 選定アセスメント種別 > 0 AC003-2-2
    if (Number(historyInfo?.capType) > 0) {
      // 検討アセスメント種別 > 0
      if (Number(historyInfo?.capType) > 0) {
        deleteKbn.value = '1'
        showOr21814Msg(t('message.i-cmn-11267'))
      }
      // 検討アセスメント種別 <= 0 AC003-2-3
      if (Number(historyInfo?.capType) <= 0) {
        deleteKbn.value = '0'
        showOr21814Msg(t('message.i-cmn-11266'))
      }
    } else {
      deleteKbn.value = ''
      void onSave()
    }
  }
}

/**
 * APIに送信するデータをマッピングするための補助関数
 *
 * @description
 * 本関数は、SubInfo オブジェクトの内容を SubM 型に変換するための補助関数（マッピング関数）です。
 * キー名が異なるプロパティの対応を行い、SubM 形式のデータとして返却します。
 *
 * @param info - SubInfo 型の入力データ
 *
 * @returns SubM 型の出力データ
 */
const mapSubInfoToSubM = (info: SubInfo): SubM => {
  return {
    raiId: info.raiId,
    m1: info.m1,
    m2A: info.m2a,
    m2B: info.m2b,
    m2C: info.m2c,
    m2D: info.m2d,
    m2E: info.m2e,
    m2F: info.m2f,
    m2G: info.m2g,
    m2H: info.m2h,
    m2I: info.m2i,
    m2J: info.m2j,
    m2K: info.m2k,
    m2L: info.m2l,
    m2M: info.m2m,
    m2N: info.m2n,
    m2O: info.m2o,
    m2P: info.m2p,
    m2Q: info.m2q,
    m2R: info.m2r,
    m2S: info.m2s,
    m2T: info.m2t,
    m2SKnj: info.m2sOrther,
    m2TKnj: info.m2tOrther,
    m3: info.m3,
    m4A: info.m4a,
    m4B: info.m4b,
    m4C: info.m4c,
    m4D: info.m4d,
    m1MemoKnj: info.m1MemoKnj,
    m1MemoFont: info.m1MemoFont,
    m1MemoColor: info.m1MemoColor,
    m2MemoKnj: info.m2MemoKnj,
    m2MemoFont: info.m2MemoFont,
    m2MemoColor: info.m2MemoColor,
    m3MemoKnj: info.m3MemoKnj,
    m3MemoFont: info.m3MemoFont,
    m3MemoColor: info.m3MemoColor,
    m4MemoKnj: info.m4MemoKnj,
    m4MemoFont: info.m4MemoFont,
    m4MemoColor: info.m4MemoColor,
  }
}

/**
 * 保存ボタン押下
 */
const onSave = async () => {
  // 入力値処理です
  const copiedRespInfo: RespInfo = cloneDeep(respInfo)
  mappingDataModel(copiedRespInfo)
  const subM: SubM = mapSubInfoToSubM(copiedRespInfo.subInfo)
  setInitTwoway(copiedRespInfo.subInfo)
  inputRequestData.subM = subM
  const resData: AssessmentInterRAIMUpdateOutEntity = await ScreenRepository.update(
    'assessmentInterRAIMUpdate',
    inputRequestData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === 'success') {
    // console.log('assessmentInterRAIMUpdate', inputRequestData)
    // 画面情報再取得
  }
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      void onSave()
    }
  }
)

/**
 * AC005「複写ボタン」押下
 *
 * @description
 * copiedRespInfo.subInfo に対応するプロパティとして反映させる共通処理
 *
 * @param copiedRespInfo - 複写元のレスポンス情報
 */
const mappingDataModel = (copiedRespInfo: RespInfo) => {
  const subInfo = copiedRespInfo.subInfo
  /**
   * 活動への平均参加時間
   */
  subInfo.m1 = refValue.value!.m1.mo00045.value
  /**
   * カード、ゲーム、クイズ
   */
  subInfo.m2a = refValue.value!.m2a.mo00045.value
  /**
   * コンピュータ、ネット関係
   */
  subInfo.m2b = refValue.value!.m2b.mo00045.value
  /**
   * 会話、電話
   */
  subInfo.m2c = refValue.value!.m2c.mo00045.value
  /**
   * 創作活動
   */
  subInfo.m2d = refValue.value!.m2d.mo00045.value
  /**
   * ダンス、舞踏
   */
  subInfo.m2e = refValue.value!.m2e.mo00045.value
  /**
   * 人生についての議論/回顧(回想法)
   */
  subInfo.m2f = refValue.value!.m2f.mo00045.value
  /**
   * 運動
   */
  subInfo.m2g = refValue.value!.m2g.mo00045.value
  /**
   * 庭仕事、畑仕事
   */
  subInfo.m2h = refValue.value!.m2h.mo00045.value
  /**
   * 他者の手助け
   */
  subInfo.m2i = refValue.value!.m2i.mo00045.value
  /**
   * 音楽や歌
   */
  subInfo.m2j = refValue.value!.m2j.mo00045.value
  /**
   *  ペット
   */
  subInfo.m2k = refValue.value!.m2k.mo00045.value
  /**
   * 読書、執筆
   */
  subInfo.m2l = refValue.value!.m2l.mo00045.value
  /**
   *  宗教活動
   */
  subInfo.m2m = refValue.value!.m2m.mo00045.value
  /**
   * 好む活動と関与_旅行
   */
  subInfo.m2n = refValue.value!.m2n.mo00045.value
  /**
   * 庭仕事、畑仕事
   */
  subInfo.m2o = refValue.value!.m2o.mo00045.value
  /**
   * 屋外の散歩
   */
  subInfo.m2p = refValue.value!.m2p.mo00045.value
  /**
   * テレビ、ラジオ、ビデオ/DVD鑑賞
   */
  subInfo.m2q = refValue.value!.m2q.mo00045.value
  /**
   * 料理/お菓子作り
   */
  subInfo.m2r = refValue.value!.m2r.mo00045.value
  /**
   * パズル/クロスワード
   */
  subInfo.m2s = refValue.value!.m2s.mo00045.value
  /**
   * その他1
   */
  subInfo.m2t = refValue.value!.m2t.mo00045.value
  /**
   * その他１課題t入力
   */
  subInfo.m2sOrther = refValue.value!.m2sOrther.value
  /**
   * その他2課題t入力
   */
  subInfo.m2tOrther = refValue.value!.m2tOrther.value
  /**
   * 日中寝ている時間
   */
  subInfo.m3 = refValue.value!.m3.mo00045.value
  /**
   *  より多くのレクリエーションに参加することに興味がある
   */
  subInfo.m4a = refValue.value!.m4a.mo00045.value
  /**
   * 転倒予防プログラムに参加することに興味がある
   */
  subInfo.m4b = refValue.value!.m4b.mo00045.value
  /**
   * 記憶力改善のためのプログラムに参加することに興味がある
   */
  subInfo.m4c = refValue.value!.m4c.mo00045.value
  /**
   * 身体機能向上プログラムに参加することに興味がある
   */
  subInfo.m4d = refValue.value!.m4d.mo00045.value
  /**
   * m1_メモ
   */
  subInfo.m1MemoKnj = refValue.value!.m1MemoKnj.content
  /**
   * m1_メモフォント
   */
  subInfo.m1MemoFont = refValue.value!.m1MemoKnj.fontSize
  /**
   * m1_メモ色
   */
  subInfo.m1MemoColor = refValue.value!.m1MemoKnj.fontColor
  /**
   * m2_メモ
   */
  subInfo.m2MemoKnj = refValue.value!.m2MemoKnj.content
  /**
   * m2_メモフォント
   */
  subInfo.m2MemoFont = refValue.value!.m2MemoKnj.fontSize
  /**
   * m2_メモ色
   */
  subInfo.m2MemoColor = refValue.value!.m2MemoKnj.fontColor
  /**
   * m3_メモ
   */
  subInfo.m3MemoKnj = refValue.value!.m3MemoKnj.content
  /**
   * m3_メモフォント
   */
  subInfo.m3MemoFont = refValue.value!.m3MemoKnj.fontSize
  /**
   * m3_メモ色
   */
  subInfo.m3MemoColor = refValue.value!.m3MemoKnj.fontColor
  /**
   * m4_メモ
   */
  subInfo.m4MemoKnj = refValue.value!.m4MemoKnj.content
  /**
   * m4_メモフォント
   */
  subInfo.m4MemoFont = refValue.value!.m4MemoKnj.fontSize
  /**
   * m4_メモ色
   */
  subInfo.m4MemoColor = refValue.value!.m4MemoKnj.fontColor
  // // --- メモ欄および状況区分のデータを subInfo にマッピング ---
  // const mapping = [
  //   {
  //     key: 'm1MemoKnj',
  //     memoRef: mo00046TypeActivities,
  //     textRef: mo00038Activities,
  //     fontKey: 'm1MemoFont',
  //     colorKey: 'm1MemoColor',
  //     className: 'M1TextArea',
  //   },
  //   {
  //     key: 'm2MemoKnj',
  //     memoRef: mo00046TypeActivitiesM2,
  //     textRef: undefined,
  //     fontKey: 'm2MemoFont',
  //     colorKey: 'm2MemoColor',
  //     className: 'M2TextArea',
  //   },
  //   {
  //     key: 'm3MemoKnj',
  //     memoRef: mo00046TypeDaytimeSleepingTime,
  //     textRef: mo00038OnewayDaytimeSleepingTime,
  //     fontKey: 'm3MemoFont',
  //     colorKey: 'm3MemoColor',
  //     className: 'M3TextArea',
  //   },
  //   {
  //     key: 'm4MemoKnj',
  //     memoRef: mo00046InterestMemo,
  //     textRef: undefined,
  //     fontKey: 'm4MemoFont',
  //     colorKey: 'm4MemoColor',
  //     className: 'M4TextArea',
  //   },
  //   {
  //     key: 'm2sOrther',
  //     memoRef: mo00045InputOtherNew1,
  //     textRef: undefined,
  //     fontKey: 'm4MemoFont',
  //     colorKey: 'm4MemoColor',
  //     className: 'M4TextArea',
  //   },
  //   {
  //     key: 'm2tOrther',
  //     memoRef: mo00045InputOtherNew2,
  //     textRef: undefined,
  //     fontKey: 'm4MemoFont',
  //     colorKey: 'm4MemoColor',
  //     className: 'M4TextArea',
  //   },
  // ] as const

  // mapping.forEach(({ key, memoRef, textRef, fontKey, colorKey, className }, index) => {
  //   // メモ内容を subInfo に格納
  //   if (key === 'm2sOrther' || key === 'm2tOrther') {
  //     subInfo[key] = memoRef.value.value ?? ''
  //   } else {
  //     subInfo[key] = memoRef.value.content ?? ''
  //   }

  //   // フォントサイズとカラー情報を取得し、subInfo に格納
  //   const el = document.querySelector(`.${className}`)
  //   const textarea = el?.querySelector('textarea') as HTMLTextAreaElement | null
  //   if (textarea) {
  //     subInfo[fontKey] = textarea.style.fontSize ?? ''
  //     subInfo[colorKey] = textarea.style.color ?? ''
  //   }

  //   // 状況区分の内容（mo00045）を subInfo に格納
  //   const situationKey = `m${index + 1}` as keyof typeof subInfo
  //   if (textRef?.value?.mo00045) {
  //     subInfo[situationKey] = textRef.value.mo00045.value ?? ''
  //   }
  // })

  // // --- M2・M4用の状況区分テキストを subInfo に格納 ---
  // const mappingM2M4 = [
  //   ...Object.entries(mo00038TextFieldMapM2).map(([key, memoRef]) => ({
  //     key: key.toLowerCase(),
  //     memoRef: memoRef as Ref<{ mo00045: { value: string } }>,
  //   })),
  //   ...Object.entries(mo00038TextFieldMapM4).map(([key, memoRef]) => ({
  //     key: key.toLowerCase(),
  //     memoRef: memoRef as Ref<{ mo00045: { value: string } }>,
  //   })),
  // ]

  // mappingM2M4.forEach(({ key, memoRef }) => {
  //   subInfo[key as keyof typeof subInfo] = memoRef.value.mo00045.value ?? ''
  // })
}

/**
 * AC005_「複写ボタン」押下
 */
const copy = async () => {
  // 入力値処理です
  const copiedRespInfo: RespInfo = cloneDeep(respInfo)
  mappingDataModel(copiedRespInfo)
  const subM: SubM = mapSubInfoToSubM(copiedRespInfo.subInfo)
  setInitTwoway(copiedRespInfo.subInfo)
  inputRequestData.subM = subM

  // 画面情報を取得するためのリクエストデータを作成
  const resData: AssessmentInterRAIMUpdateOutEntity = await ScreenRepository.insert(
    'assessmentInterRAIMInsert',
    inputRequestData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === 'success') {
    // 画面情報再取得
    // console.log('assessmentInterRAIMInsert', inputRequestData)
  }
  // TODO: GUI00807 アセスメント複写画面をポップアップで起動する。
}

/**
 * AC011_「削除」押下
 */
const del = async () => {
  // 入力値処理です
  const copiedRespInfo: RespInfo = cloneDeep(respInfo)
  mappingDataModel(copiedRespInfo)
  const subM: SubM = mapSubInfoToSubM(copiedRespInfo.subInfo)
  setInitTwoway(copiedRespInfo.subInfo)
  inputRequestData.subM = subM
  // 画面情報を取得するためのリクエストデータを作成
  const resData: AssessmentInterRAIMUpdateOutEntity = await ScreenRepository.delete_phys(
    'assessmentInterRAIMDelete',
    inputRequestData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === 'success') {
    // 画面情報再取得
    // console.log('assessmentInterRAIMDelete', inputRequestData)
  }
  // TODO 画面「M」タブに対し、更新区分を「D:削除」にする。
}

/**
 * AC003-2-1_アセスメント(インターライ)画面履歴の最新情報を取得
 */
const getData = async () => {
  const inputData: AssessmentInterRAIMInEntity = {
    raiId: local.historyInfo.raiId,
  }
  const resData: AssessmentInterRAIMOutEntity = await ScreenRepository.select(
    'assessmentInterRAIMSelect',
    inputData
  )
  if (resData.statusCode === 'success' && resData.data) {
    const data = resData.data
    respInfo.subInfo = data.subInfo

    // --- 共通処理 ---
    // const mapping: {
    //   key: keyof typeof data.subInfo
    //   memoRef: Ref<{ content?: string; fontSize?: string; fontColor?: string; value?: string }>
    //   textRef?: Ref<{ mo00045: { value: string } } | null | undefined>
    //   className: string
    //   fontKey: keyof typeof data.subInfo
    //   colorKey: keyof typeof data.subInfo
    // }[] = [
    //   {
    //     key: 'm1MemoKnj',
    //     memoRef: mo00046TypeActivities,
    //     textRef: mo00038Activities,
    //     className: 'M1TextArea',
    //     fontKey: 'm1MemoFont',
    //     colorKey: 'm1MemoColor',
    //   },
    //   {
    //     key: 'm2MemoKnj',
    //     memoRef: mo00046TypeActivitiesM2,
    //     textRef: undefined,
    //     className: 'M2TextArea',
    //     fontKey: 'm2MemoFont',
    //     colorKey: 'm2MemoColor',
    //   },
    //   {
    //     key: 'm3MemoKnj',
    //     memoRef: mo00046TypeDaytimeSleepingTime,
    //     textRef: mo00038OnewayDaytimeSleepingTime,
    //     className: 'M3TextArea',
    //     fontKey: 'm3MemoFont',
    //     colorKey: 'm3MemoColor',
    //   },
    //   {
    //     key: 'm4MemoKnj',
    //     memoRef: mo00046InterestMemo,
    //     textRef: undefined,
    //     className: 'M4TextArea',
    //     fontKey: 'm4MemoFont',
    //     colorKey: 'm4MemoColor',
    //   },
    //   {
    //     key: 'm2sOrther',
    //     memoRef: mo00045InputOtherNew1,
    //     textRef: undefined,
    //     className: 'M4TextArea',
    //     fontKey: 'm4MemoFont',
    //     colorKey: 'm4MemoColor',
    //   },
    //   {
    //     key: 'm2tOrther',
    //     memoRef: mo00045InputOtherNew2,
    //     textRef: undefined,
    //     className: 'M4TextArea',
    //     fontKey: 'm4MemoFont',
    //     colorKey: 'm4MemoColor',
    //   },
    // ]

    // mapping.forEach(({ key, memoRef, textRef, className, fontKey, colorKey }, index) => {
    //   // メモ内容
    //   if (key === 'm2sOrther' || key === 'm2tOrther') {
    //     memoRef.value.value = data.subInfo[key] ?? ''
    //   } else {
    //     memoRef.value.content = data.subInfo[key] ?? ''
    //   }

    //   // フォントと色反映
    //   void nextTick(() => {
    //     const el = document.querySelector(`.${className}`)
    //     el?.querySelectorAll('textarea')?.forEach((item) => {
    //       if (item.value) {
    //         item.style.fontSize = data.subInfo[fontKey] ?? ''
    //         item.style.color = data.subInfo[colorKey] ?? ''
    //       }
    //     })
    //   })

    //   // 状況区分 (M1 and M3)
    //   const situationKey = `m${index + 1}` as keyof typeof data.subInfo
    //   if (textRef?.value?.mo00045 && situationKey in data.subInfo) {
    //     textRef.value.mo00045.value = data.subInfo[situationKey] ?? ''

    //     // const upperCaseKey = situationKey.toUpperCase() as keyof typeof previousAssessmentValues
    //     const upperCaseKey = uppercaseFirstLetter(
    //       situationKey
    //     ) as keyof typeof previousAssessmentValues

    //     previousAssessmentValues[upperCaseKey] = data.subInfo[situationKey] ?? ''
    //   }
    // })

    // --- 共通処理 M2 and M4---
    // const mappingM2M4: {
    //   key: keyof typeof data.subInfo
    //   memoRef: { mo00045: { value: string } }
    // }[] = [
    //   ...Object.entries(mo00038TextFieldMapM2).map(([key, memoRef]) => ({
    //     key: key?.toLowerCase() as keyof typeof data.subInfo,
    //     memoRef: memoRef as Ref<{ mo00045: { value: string } }>,
    //   })),
    //   ...Object.entries(mo00038TextFieldMapM4).map(([key, memoRef]) => ({
    //     key: key?.toLowerCase() as keyof typeof data.subInfo,
    //     memoRef: memoRef as { mo00045: { value: string } },
    //   })),
    // ]

    // 状況区分 (M2 and M4)
    // mappingM2M4.forEach(({ key, memoRef }) => {
    //   memoRef.mo00045.value = data.subInfo[key] ?? ''
    //   const upperCaseKey = uppercaseFirstLetter(key) as keyof typeof previousAssessmentValues
    //   previousAssessmentValues[upperCaseKey] = data.subInfo[key] ?? ''
    // })

    if (data.subInfo) {
      const items = data.subInfo
      setInitTwoway(items)
    }
  }
}

/**
 * 初期化処理
 *
 * @param data - 画面初期情報
 */
const setInitTwoway = (data: SubInfo) => {
  const items = data
  screenStore.setCpTwoWay({
    cpId: Or31524Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: {
      /**
       * 活動への平均参加時間
       */
      m1: {
        mo00045: {
          value: items.m1 ?? '',
        },
      },
      /**
       * カード、ゲーム、クイズ
       */
      m2a: {
        mo00045: {
          value: items.m2a ?? '',
        },
      },
      /**
       * コンピュータ、ネット関係
       */
      m2b: {
        mo00045: {
          value: items.m2b ?? '',
        },
      },
      /**
       * 会話、電話
       */
      m2c: {
        mo00045: {
          value: items.m2c ?? '',
        },
      },
      /**
       * 創作活動
       */
      m2d: {
        mo00045: {
          value: items.m2d ?? '',
        },
      },
      /**
       * ダンス、舞踏
       */
      m2e: {
        mo00045: {
          value: items.m2e ?? '',
        },
      },
      /**
       * 人生についての議論/回顧(回想法)
       */
      m2f: {
        mo00045: {
          value: items.m2f ?? '',
        },
      },
      /**
       * 運動
       */
      m2g: {
        mo00045: {
          value: items.m2g ?? '',
        },
      },
      /**
       * 庭仕事、畑仕事
       */
      m2h: {
        mo00045: {
          value: items.m2h ?? '',
        },
      },
      /**
       * 他者の手助け
       */
      m2i: {
        mo00045: {
          value: items.m2i ?? '',
        },
      },
      /**
       * 音楽や歌
       */
      m2j: {
        mo00045: {
          value: items.m2j ?? '',
        },
      },
      /**
       *  ペット
       */
      m2k: {
        mo00045: {
          value: items.m2k ?? '',
        },
      },
      /**
       * 読書、執筆
       */
      m2l: {
        mo00045: {
          value: items.m2l ?? '',
        },
      },
      /**
       *  宗教活動
       */
      m2m: {
        mo00045: {
          value: items.m2m ?? '',
        },
      },
      /**
       * 好む活動と関与_旅行
       */
      m2n: {
        mo00045: {
          value: items.m2n ?? '',
        },
      },
      /**
       * 庭仕事、畑仕事
       */
      m2o: {
        mo00045: {
          value: items.m2o ?? '',
        },
      },
      /**
       * 屋外の散歩
       */
      m2p: {
        mo00045: {
          value: items.m2p ?? '',
        },
      },
      /**
       * テレビ、ラジオ、ビデオ/DVD鑑賞
       */
      m2q: {
        mo00045: {
          value: items.m2q ?? '',
        },
      },
      /**
       * 料理/お菓子作り
       */
      m2r: {
        mo00045: {
          value: items.m2r ?? '',
        },
      },
      /**
       * パズル/クロスワード
       */
      m2s: {
        mo00045: {
          value: items.m2s ?? '',
        },
      },
      /**
       * その他1
       */
      m2t: {
        mo00045: {
          value: items.m2t ?? '',
        },
      },
      /**
       * その他１課題t入力
       */
      m2sOrther: {
        value: items.m2sOrther ?? '',
      },
      /**
       * その他2課題t入力
       */
      m2tOrther: {
        value: items.m2tOrther ?? '',
      },
      /**
       * 日中寝ている時間
       */
      m3: {
        mo00045: {
          value: items.m3 ?? '',
        },
      },
      /**
       *  より多くのレクリエーションに参加することに興味がある
       */
      m4a: {
        mo00045: {
          value: items.m4a ?? '',
        },
      },
      /**
       * 転倒予防プログラムに参加することに興味がある
       */
      m4b: {
        mo00045: {
          value: items.m4b ?? '',
        },
      },
      /**
       * 記憶力改善のためのプログラムに参加することに興味がある
       */
      m4c: {
        mo00045: {
          value: items.m4c ?? '',
        },
      },
      /**
       * 身体機能向上プログラムに参加することに興味がある
       */
      m4d: {
        mo00045: {
          value: items.m4d ?? '',
        },
      },
      /**
       * m1_メモ
       */
      m1MemoKnj: {
        content: items.m1MemoKnj ?? '',
        fontSize: items.m1MemoFont ?? '',
        fontColor: items.m1MemoColor ?? '',
      },
      /**
       * m2_メモ
       */
      m2MemoKnj: {
        content: items.m2MemoKnj ?? '',
        fontSize: items.m2MemoFont ?? '',
        fontColor: items.m2MemoColor ?? '',
      },
      /**
       * m3_メモ
       */
      m3MemoKnj: {
        content: items.m3MemoKnj ?? '',
        fontSize: items.m3MemoFont ?? '',
        fontColor: items.m3MemoColor ?? '',
      },
      /**
       * m4_メモ
       */
      m4MemoKnj: {
        content: items.m4MemoKnj ?? '',
        fontSize: items.m4MemoFont ?? '',
        fontColor: items.m4MemoColor ?? '',
      },
    },
    isInit: true,
  })

  console.log('setCpTwoWay', screenStore.getCpTwoWayInitialValue(props.uniqueCpId))
}

/**
 * 前回のアセスメント値を保持するリアクティブオブジェクト
 */
// const previousAssessmentValues = reactive({
//   M1: '',
//   M3: '',
//   M2a: '',
//   M2b: '',
//   M2c: '',
//   M2d: '',
//   M2e: '',
//   M2f: '',
//   M2g: '',
//   M2h: '',
//   M2i: '',
//   M2j: '',
//   M2k: '',
//   M2l: '',
//   M2m: '',
//   M2n: '',
//   M2o: '',
//   M2p: '',
//   M2q: '',
//   M2r: '',
//   M2s: '',
//   M2t: '',
//   M4a: '',
//   M4b: '',
//   M4c: '',
//   M4d: '',
// })

/**
 * 文字列の先頭文字を大文字に変換して返します。
 *
 * @param str - 文字列
 *
 * @returns 大文字に変換した文字列
 */
// const uppercaseFirstLetter = (str: string): string => {
//   return str.charAt(0).toUpperCase() + str.slice(1)
// }
</script>

<template>
  <c-v-row
    no-gutters
    class="main-container"
  >
    <c-v-col
      cols="12"
      class="main-container_left"
    >
      <div class="type-care">
        <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway" />
      </div>
      <div class="skin-condition-section-title">
        <base-mo01338 :oneway-model-value="localOneway.mo01338OnewayActivity" />
      </div>
      <!-- コンテナ全体 -->
      <div class="container">
        <c-v-row no-gutters>
          <c-v-col cols="12">
            <c-v-row
              no-gutters
              class="content"
            >
              <!-- M1.サブセクション -->
              <c-v-row
                no-gutters
                class="asection"
              >
                <c-v-col
                  cols="12"
                  class="asection_left"
                >
                  <c-v-row
                    no-gutters
                    class="asection_left_title"
                  >
                    <div class="asection_left_title_left">
                      {{ localOneway.mo01338OnewayActivities.value }}
                    </div>
                  </c-v-row>
                </c-v-col>
                <c-v-col
                  cols="12"
                  class="asection_right"
                >
                  <div class="asection_right_content">
                    <div class="asection_right_content--option">
                      <g-custom-or-30982
                        v-model="refValue!.m1.mo00045"
                        :oneway-model-value="localOneway.or30982Oneway"
                      ></g-custom-or-30982>
                    </div>
                  </div>
                  <div style="margin-top: 24px">
                    <div class="mt-1 input-container">
                      <g-custom-or-30981
                        v-bind="or30981_0"
                        v-model="refValue!.m1MemoKnj"
                        :oneway-model-value="localOneway.orX0156OnewayTypeM1"
                        @on-click-edit-btn="careTargetInputSupportIconClick('M1')"
                      ></g-custom-or-30981>
                    </div>
                  </div>
                </c-v-col>
              </c-v-row>

              <!-- M2.サブセクション -->
              <c-v-row
                no-gutters
                class="asection"
              >
                <c-v-col
                  cols="12"
                  class="asection_left"
                >
                  <c-v-row
                    no-gutters
                    class="asection_left_title"
                  >
                    <div class="asection_left_title_left">
                      {{ localOneway.mo01338OnewayActivitiesM2.value }}
                    </div>
                  </c-v-row>
                </c-v-col>
                <c-v-col
                  cols="12"
                  class="asection_right"
                >
                  <div class="asection_right_content">
                    <div class="asection_right_note">
                      <div class="asection_right_note--text">
                        {{ t('btn.dont-like-or-am-not-involved-with-btn') }}
                      </div>
                      <div class="asection_right_note--text">
                        {{ t('btn.preferences-and-uninvolved-btn') }}
                      </div>
                      <div class="asection_right_note--text">
                        {{ t('btn.preferences-and-engagement-bnt') }}
                      </div>
                    </div>
                    <!-- M2セクションの項目をaからtまでレンダリング -->
                    <c-v-row
                      v-for="item in localOnewayMapM2"
                      :key="item.key"
                      no-gutters
                      class="pb-4"
                    >
                      <c-v-col
                        cols="8"
                        class="line-height-35"
                      >
                        <base-mo00615
                          v-if="!item.isOther"
                          :oneway-model-value="item.mo00615Oneway"
                        />
                        <c-v-row
                          v-else
                          no-gutters
                        >
                          <v-c-col cols="6">
                            <div style="width: 69px">
                              <base-mo00615 :oneway-model-value="item.mo00615Oneway" />
                            </div>
                          </v-c-col>
                          <v-c-col
                            cols="8"
                            class="edit-button ml-4"
                          >
                            <base-mo00009
                              :oneway-model-value="item.mo00009OnewayOtherNew"
                              @click="onClickOther(item.keyOther)"
                            />
                            <base-mo-00045
                              v-model="
                                refValue![
                                  item.keyOther !== '' && item.keyOther === '1'
                                    ? 'm2sOrther'
                                    : 'm2tOrther'
                                ]
                              "
                              :oneway-model-value="item.mo00045OnewayOtherNew"
                            />
                          </v-c-col>
                        </c-v-row>
                      </c-v-col>
                      <c-v-col
                        cols="4"
                        class="d-flex justify-sm-space-around align-sm-center"
                        style="justify-content: end !important"
                      >
                        <base-at-select
                          v-model="refValue![item.key].mo00045.value"
                          name=""
                          :items="[
                            localOneway.mo00611OnewayDontLikeOrAmNotInvolvedWithBtn,
                            localOneway.mo00611OnewayPreferencesAndUninvolvedBtn,
                            localOneway.mo00611OnewayPreferencesAndEngagementBnt,
                          ]"
                          item-title="btnLabel"
                          item-value="value"
                          hide-details="true"
                          class="asection_right--select"
                          style="display: contents"
                        >
                          <template #item="{ props: itemProps, item: el }">
                            <c-v-tooltip
                              location="left"
                              :text="el.raw.btnLabel"
                            >
                              <template #activator="{ props: tooltipProps }">
                                <c-v-list-item v-bind="{ ...itemProps, ...tooltipProps }" />
                              </template>
                            </c-v-tooltip>
                          </template>
                        </base-at-select>
                      </c-v-col>
                    </c-v-row>

                    <div>
                      <div class="mt-1 input-container">
                        <g-custom-or-30981
                          v-bind="or30981_1"
                          v-model="refValue!.m2MemoKnj"
                          :oneway-model-value="localOneway.orX0156OnewayTypeM2"
                          @on-click-edit-btn="careTargetInputSupportIconClick('M2')"
                        ></g-custom-or-30981>
                      </div>
                    </div>
                  </div>
                </c-v-col>
              </c-v-row>

              <!-- M3.サブセクション -->
              <c-v-row
                no-gutters
                class="asection"
              >
                <c-v-col
                  cols="12"
                  class="asection_left"
                >
                  <c-v-row
                    no-gutters
                    class="asection_left_title"
                  >
                    <div class="asection_left_title_left">
                      {{ localOneway.mo01338OnewayDaytimeSleepingTimeTitle.value }}
                    </div>
                  </c-v-row>
                </c-v-col>
                <c-v-col
                  cols="12"
                  class="asection_right"
                >
                  <base-mo00039
                    v-model="refValue!.m3.mo00045.value"
                    :oneway-model-value="localOneway.mo00039Oneway"
                    :class="['custom-radio']"
                    :style="`width: 640px`"
                  >
                    <base-at-radio
                      v-for="radio in localOneway.or56086OnewayTypeDaytimeSleepingTime.btnItems"
                      :key="radio.value"
                      :radio-label="radio.label"
                      :value="radio.value"
                      :class="{
                        isChecked: refValue!.m3.mo00045.value === radio.value,
                      }"
                      :style="`width: 640px`"
                    />
                  </base-mo00039>

                  <div style="margin-top: 24px">
                    <div class="mt-1 input-container">
                      <g-custom-or-30981
                        v-bind="or30981_2"
                        v-model="refValue!.m3MemoKnj"
                        :oneway-model-value="localOneway.orX0156OnewayTypeM3"
                        @on-click-edit-btn="careTargetInputSupportIconClick('M3')"
                      ></g-custom-or-30981>
                    </div>
                  </div>
                </c-v-col>
              </c-v-row>

              <!-- M4.サブセクション -->
              <c-v-row
                no-gutters
                class="asection"
              >
                <c-v-col
                  cols="12"
                  class="asection_left"
                >
                  <c-v-row
                    no-gutters
                    class="asection_left_title"
                  >
                    <div class="asection_left_title_left">
                      {{ localOneway.mo01338OnewayInterestTitle.value }}
                    </div>
                  </c-v-row>
                </c-v-col>
                <c-v-col
                  cols="12"
                  class="asection_right asection_right--last"
                >
                  <!-- M4セクションの項目をaからdまでレンダリング -->
                  <c-v-row
                    v-for="(item, index) in localOnewayMapM4"
                    :key="index"
                    no-gutters
                    class="mb-4"
                  >
                    <c-v-col
                      cols="7"
                      class="line-height-35"
                    >
                      <base-mo00615 :oneway-model-value="item.mo00615Oneway" />
                    </c-v-col>
                    <c-v-col
                      cols="5"
                      class="d-flex align-sm-center"
                      style="justify-content: end"
                    >
                      <div>
                        <g-custom-or-30982
                          v-model="refValue![item.key].mo00045"
                          :oneway-model-value="localOneway.or30982OnewayM4"
                        ></g-custom-or-30982>
                      </div>
                    </c-v-col>
                  </c-v-row>

                  <div style="margin-top: 24px">
                    <div class="mt-1 input-container">
                      <g-custom-or-30981
                        v-bind="or30981_3"
                        v-model="refValue!.m4MemoKnj"
                        :oneway-model-value="localOneway.orX0156OnewayTypeM4"
                        @on-click-edit-btn="careTargetInputSupportIconClick('M4')"
                      ></g-custom-or-30981>
                    </div>
                  </div>
                </c-v-col>
              </c-v-row>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <!-- interRAIロゴ -->
        <c-v-row
          no-gutters
          class="pt-4"
        >
          <c-v-col cols="12">
            <c-v-img
              width="129"
              aspect-ratio="16/9"
              fill
              :src="InterRAI"
              style="float: right"
              class="custom-img"
            ></c-v-img>
          </c-v-col>
        </c-v-row>
      </div>
    </c-v-col>
  </c-v-row>
  <!-- GUI00777 ［メモ入力］画面 -->
  <g-custom-or-10412
    v-if="showDialogOr10412"
    v-bind="or10412"
    v-model="or10412Type"
    :oneway-model-value="localOneway.or10412Oneway"
  ></g-custom-or-10412>
  <!-- Or31494 ダイアログコンポーネント -->
  <!-- <g-custom-or-31494
    v-if="showDialogOr31494"
    v-bind="or31494"
    :oneway-model-value="localOneway.or31494Oneway"
    @interest-selected="handleInterestSelected"
  ></g-custom-or-31494> -->
  <!-- Or31493 ダイアログコンポーネント -->
  <!-- <g-custom-or-31493
    v-if="showDialogOr31493"
    v-bind="or31493"
    :oneway-model-value="localOneway.or31493Oneway"
    @interest-selected="ActivitiesEngagementSelectedbtn"
  ></g-custom-or-31493> -->
  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Other"
    :oneway-model-value="localOneway.or51775OnewayTypeOther"
    @confirm="or51775Confirm"
  ></g-custom-or-51775>
  <!-- 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>

<style scoped lang="scss">
.background-transparent {
  background-color: transparent !important;
}
:deep(.custom-img img) {
  object-fit: fill;
}
.skin-condition-section-title {
  display: flex;
  align-items: center;
  width: 1080px;
  background-color: #fff;
  padding-left: 24px;
  height: 73px;
  color: #333333;
  font-size: 18px;
  font-weight: 700;

  :deep(.item-label) {
    font-size: 18px;
  }
}
.type-care {
  width: 1080px;
  display: flex;
  justify-content: end;
  font-size: 24px;
  padding: 4px 0;
  :deep(.item-label) {
    font-size: 24px;
  }
}

.right-input :deep(input) {
  text-align: right !important;
}

:deep(.custom-button-options .gridContent .v-btn > span span) {
  padding-left: 12px;
}

:deep(.custom-button-options .verticalContent .v-btn > span span) {
  padding-left: 12px;
}

.line-height-35 {
  line-height: 36px;
}

.button-style {
  display: flex;
  justify-content: flex-start;
  padding-left: 8px;
}

.label-key {
  width: 15px;
  text-align: center;
}

.main-container {
  padding-top: 16px;
  width: 1080px;
}

.fit-content {
  width: fit-content;
}

.main-container_left {
  :has(> .contentTitle) {
    text-align: right;
    :deep(.item-label) {
      font-size: 18px !important;
    }
  }
}

.title {
  :has(> .custom-header_label) {
    font-size: 16px;
    margin-right: 16px;
  }
}

.container {
  width: 1080px;
  .content {
    .asection {
      :has(> .section-content) {
        background: none !important;
      }

      .asection_left {
        background: rgb(var(--v-theme-background));
        .asection_left_title {
          .asection_left_title_left {
            width: 100%;
            display: flex;
            align-items: center;
            height: 48px;
            font-size: 17px;
            color: #333333;
            font-weight: 700;
            padding-left: 24px;
            background-color: #e6e6e6;

            .asection_left_title_left_label {
              display: none !important;
            }
          }
        }

        .asection_left_content {
          padding-left: 16px;
          padding-bottom: 16px;
          padding-right: 16px;
        }
      }
      .asection_right--last {
        padding: 24px 24px 35px 48px !important;
      }
      .asection_right {
        background: rgb(var(--v-theme-secondaryBackground));
        padding: 24px 24px 24px 48px;

        .asection_right_content {
          width: 100%;
          .asection_right_content--option {
            width: 640px;
          }

          .asection_right_note {
            width: 1008px;
            height: 67px;
            border: 1px solid #333333;
            padding: 8px 16px;
            margin-bottom: 24px;
            .asection_right_note--text {
              font-weight: 700;
              font-style: Bold;
              font-size: 14px;
              line-height: 16px;
            }
          }
        }

        .asection_right_second {
          justify-content: end;
          margin-top: 8px;
          padding-right: 16px;

          :deep(.v-field__input) {
            min-height: 8px !important;
            height: 25px;
          }

          :has(> .toGray) {
            :first-child {
              background-color: rgb(var(--v-theme-black-536)) !important;
            }
          }
        }
      }
    }
  }
}

:deep(.custom-radio) {
  &.inline {
    .v-radio {
      min-width: 180px;
    }
  }
  .v-selection-control-group {
    gap: 16px;
  }
  .v-radio {
    padding: 0 8px;
    border: 1px solid #8a8a8a;
    border-radius: 4px;
    .v-label {
      width: 100%;
    }
    &.isChecked {
      background-color: #ecf3fd;
      border: 1px solid #0760e6;
    }
  }
}
.input-container {
  position: relative;
  .button-square {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 9;
  }
  textarea {
    height: 127px !important;
  }
}

.asection_right--select {
  :deep(.v-input__control) {
    width: 300px !important;
  }
}

:deep(.edit-button) {
  display: flex;
  button {
    background-color: #ebf2fd;
    border: 1px solid #c1c6cc;
    border-right: none;
    border-radius: 0;
    margin: 0 !important;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  .v-field {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
