/**
 * Or26426:処理ロジック
 * GUI01272_認定調査票特記事項
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR>
 */
import { OrX0217Const } from '../OrX0217/OrX0217.constants'
import { OrX0217Logic } from '../OrX0217/OrX0217.logic'
import { Or26426Const } from './Or26426.constants'
import type { Or26426StateType } from './Or26426.type'
import { useInitialize, useOneWayBindAccessor } from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

export namespace Or26426Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or26426Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or21814Const.CP_ID(0) },
        { cpId: OrX0217Const.CP_ID(1) },
        { cpId: OrX0217Const.CP_ID(2) },
        { cpId: OrX0217Const.CP_ID(3) },
        { cpId: OrX0217Const.CP_ID(4) },
        { cpId: OrX0217Const.CP_ID(5) },
        { cpId: OrX0217Const.CP_ID(6) },
        { cpId: OrX0217Const.CP_ID(7) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or21814Logic.initialize(childCpIds.Or21814.uniqueCpId)
    OrX0217Logic.initialize(childCpIds[OrX0217Const.CP_ID(1)].uniqueCpId)
    OrX0217Logic.initialize(childCpIds[OrX0217Const.CP_ID(2)].uniqueCpId)
    OrX0217Logic.initialize(childCpIds[OrX0217Const.CP_ID(3)].uniqueCpId)
    OrX0217Logic.initialize(childCpIds[OrX0217Const.CP_ID(4)].uniqueCpId)
    OrX0217Logic.initialize(childCpIds[OrX0217Const.CP_ID(5)].uniqueCpId)
    OrX0217Logic.initialize(childCpIds[OrX0217Const.CP_ID(6)].uniqueCpId)
    OrX0217Logic.initialize(childCpIds[OrX0217Const.CP_ID(7)].uniqueCpId)

    // 子コンポーネントのセットアップ
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or26426StateType>(Or26426Const.CP_ID(0))
}
