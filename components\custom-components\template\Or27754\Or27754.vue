<script setup lang="ts">
/**
 * Or27754:アセスメント(インターライ)画面テンプレート
 *
 * @description
 * アセスメント(インターライ)画面テンプレート
 *
 * <AUTHOR>
 */

import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import type { DataTableHeader } from 'vuetify'
import { Or29726Const } from '../../organisms/Or29726/Or29726.constants'
import { Or29726Logic } from '../../organisms/Or29726/Or29726.logic'
import type { Or51775ConfirmType } from '../../organisms/Or51775/Or51775.type'
import { Or27754Const } from '~/components/custom-components/template/Or27754/Or27754.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useSetupChildProps, useScreenStore, dateUtils, useScreenTwoWayBind } from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Or27754OnewayType, TransmitParam } from '~/types/cmn/business/components/Or27754Type'
import type { Or53417OnewayType } from '~/types/cmn/business/components/Or53417Type'
import { Or53417Logic } from '~/components/custom-components/organisms/Or53417/Or53417.logic'
import { Or53417Const } from '~/components/custom-components/organisms/Or53417/Or53417.constants'
import type { Or10279OneWayType } from '~/types/cmn/business/components/Or10279Type'
import { Or10279Logic } from '~/components/custom-components/organisms/Or10279/Or10279.logic'
import { Or10279Const } from '~/components/custom-components/organisms/Or10279/Or10279.constants'
import type { OrX0001Type, OrX0001OnewayType } from '~/types/cmn/business/components/OrX0001Type'
import { OrX0001Const } from '~/components/custom-components/organisms/OrX0001/OrX0001.constants'
import { OrX0001Logic } from '~/components/custom-components/organisms/OrX0001/OrX0001.logic'
import type {
  HistorySelectInfoType,
  Or10929Type,
} from '~/types/cmn/business/components/Or10929Type'
import { Or10929Logic } from '~/components/custom-components/organisms/Or10929/Or10929.logic'
import { Or10929Const } from '~/components/custom-components/organisms/Or10929/Or10929.constants'
import type { HistorySelectTableDataItem } from '~/components/custom-components/organisms/Or10929/Or10929.type'
import type {
  SubInfoBEntity,
  IAssessmentInterRAINewSelectInEntity,
  IAssessmentInterRAINewSelectOutEntity,
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
  IAssessmentInterRAIPeriodChangeSelectInEntity,
  IAssessmentInterRAIPeriodChangeSelectOutEntity,
  IAssessmentInterRAIHistoryChangeSelectInEntity,
  IAssessmentInterRAIHistoryChangeSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import { OrX0115Logic } from '~/components/custom-components/organisms/OrX0115/OrX0115.logic'
import { OrX0115Const } from '~/components/custom-components/organisms/OrX0115/OrX0115.constants'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or10269Const } from '~/components/custom-components/organisms/Or10269/Or10269.constants'
import { Or10269Logic } from '~/components/custom-components/organisms/Or10269/Or10269.logic'
import type { Or10269Param } from '~/components/custom-components/organisms/Or10269/Or10269.type'
import { OrX0134Const } from '~/components/custom-components/organisms/OrX0134/OrX0134.constants'
import { OrX0134Logic } from '~/components/custom-components/organisms/OrX0134/OrX0134.logic'
import { Or13872Const } from '~/components/custom-components/organisms/Or13872/Or13872.constants'
import { Or13872Logic } from '~/components/custom-components/organisms/Or13872/Or13872.logic'
import type { OrX0134OnewayType, OrX0134Type } from '~/types/cmn/business/components/OrX0134Type'
import type { Or13844OnewayType } from '~/types/cmn/business/components/Or13844Type'
import { Or13844Const } from '~/components/custom-components/organisms/Or13844/Or13844.constants'
import { Or13844Logic } from '~/components/custom-components/organisms/Or13844/Or13844.logic'
import type { Or13872OnewayType } from '~/types/cmn/business/components/Or13872Type'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import type {
  SvJigyo,
  Or26257Type,
  Or26257OnewayType,
} from '~/types/cmn/business/components/Or26257Type'
import type { OrX0157OnewayType, OrX0157Type } from '~/types/cmn/business/components/OrX0157Type'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { useJigyoList } from '~/utils/useJigyoList'
import {
  DIALOG_BTN,
  MID_LINE,
  SUCCESS_STATUS_CODE,
  SPACE_WAVE,
  UPDATE_KBN,
} from '~/constants/classification-constants'
import type {
  AssessmentInterRAIAHistorySelectEntity,
  AssessmentInterRAIAHistorySelectOutEntity,
} from '~/repositories/cmn/entities/assessmentInterRAIAHistoryEntity'
import type {
  CodeGroup,
  CodeListItem,
  CodeType,
  IssuesConsiderItemType,
  Or27754TwoWayType,
  Or51775StateType,
} from '~/components/custom-components/template/Or27754/Or27754.type'
import type {
  Or29726CopyData,
  Or29726OnewayType,
} from '~/types/cmn/business/components/Or29726Type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  IssueConsiderationInputType,
  IssueConsiderationOutEntity,
} from '~/repositories/cmn/entities/IssueConsiderationItemHeaderEntity'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { useValidation } from '@/utils/useValidation'

const { jigyoListWatch } = useJigyoList()
const { convertDateToSeireki } = dateUtils()
const { byteLength } = useValidation()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or27754OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()
/**
 * COPY
 */
const isCopyMode = computed(() => props.onewayModelValue.cpyFlg)

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()

/**
 * 初期読み込みのローディング
 */
const isLoading = ref(false)
/**
 * <IssuesConsiderItemType[]>
 */
const issuesConsiderDetailAcquisitionInfo = ref<IssuesConsiderItemType[]>([])

const or11871 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or00248 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or53417 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or10279 = ref({ uniqueCpId: Or10279Const.CP_ID(1) })
const orX0001 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or10929 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or21813 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or21814 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const orx0115 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or41179 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or13844 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or10269 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const orX0134 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or13872 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or26257 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or29726_1 = ref({ uniqueCpId: Or29726Const.CP_ID(1) })
const or51775 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
const or27754 = ref({ uniqueCpId: Or27754Const.STR.EMPTY })
/**
 * 共通入力の変更
 */
const isCommonDataEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})

/**
 * タブ入力の変更
 */
const isTabDataEdit = computed(() => {
  const editFlg = false
  // editFlg = useScreenStore().isEditByUniqueCpId(or31000.value.uniqueCpId)
  return editFlg
})

/**
 * すべての入力の変更
 */
const isEdit = computed(() => {
  return isTabDataEdit.value || isCommonDataEdit.value || local.updateKbn || local.historyUpdateKbn
})

/**
 * 計画期間
 */
const planPeriodShow = ref<boolean>(true)
/**
 * 履歴
 */
const historyShow = ref<boolean>(true)
/**
 * 作成者
 */
const authorShow = ref<boolean>(true)
/**
 * 基準日
 */
const baseDateShow = ref<boolean>(true)

/**
 * 削除フラグ
 */
const deleteFlag = ref<boolean>(false)

/**
 * CSV出力ボタン活性/非活性フラゲ
 */
const csvOutputBtnDisabledFlg = ref<boolean>(false)
/**
 * 親情報
 */
const commonInfo = {
  /**
   * 適用事業所ＩＤリスト
   */
  svJigyoIdList: [],

  // 事業所ID
  officeId: '1',

  /**
   * 事業者グループ適用ID
   */
  officeGroupId: '',

  /** 利用者ID */
  userId: '',

  // 事業者ID
  svJigyoId: '',

  /**
   *マスタ区分
   *カテゴリー :1:施設、2:居宅
   */
  mstKbn: '',

  /**
   * 改訂区分
   */
  kaiteiFlg: '',

  // 改訂フラグ
  revisionFlag: '',

  /** 履歴作成日 */
  createYmd: '',
  //--------------------------------------------------------------------------
  // 基準日
  baseDate: '',
  // ログイン情報.職員名
  loginUserName: '管理者　太郎',
  // 計画対象期間ID
  sc1Id: '',
  // アセスメントID
  assessmentId: '',
  // 作成者ID
  shokuId: '',
  // 調査アセスメント種別
  surveyAssessmentKind: '',
}
/**
 * CSV出力ボタン表示/非表示フラゲ
 */
const logBtnShowFlg = ref<boolean>(true)

const valid = ref(false)
/**
 * ItemListHeader
 */
const issuesConsiderItemListHeader = ref([
  {
    title: t('label.lblItem'),
    key: 'item',
    sortable: false,
    minWidth: '101px',
  },
  {
    title: t('label.lblDetailItem'),
    key: 'details',
    sortable: false,
    minWidth: '117px',
  },
  {
    title: t('label.lblCheck'),
    key: 'check',
    sortable: false,
    minWidth: '76px',
  },
  {
    title: t('label.lblConsiderSpecificSituation'),
    key: 'joukyouKnj',
    sortable: false,
    minWidth: '200px',
    // 小分類CD
    smallCategoryCode: '1',
    // カラム名
    columnName: 'joukyou_knj',
  },
  {
    title: t('label.lblCause'),
    key: 'geninKnj',
    sortable: false,
    minWidth: '200px',
    // 小分類CD
    smallCategoryCode: '2',
    // カラム名
    columnName: 'genin_knj',
  },
  {
    title: t('label.lblPersonFamilyIntention'),
    key: 'ikouKnj',
    sortable: false,
    minWidth: '200px',
    // 小分類CD
    smallCategoryCode: '3',
    // カラム名
    columnName: 'genin_knj',
  },
  {
    title: t('label.lblHighlightCareStatus'),
    key: 'highlightCareStatus',
    sortable: false,
    minWidth: '200px',
    // 小分類CD
    smallCategoryCode: '4',
    // カラム名
    columnName: 'genin_knj',
  },
  {
    title: t('label.lblLifeIssuesNeeds'),
    key: 'kadaiKnj',
    sortable: false,
    minWidth: '200px',
    // 小分類CD
    smallCategoryCode: '5',
    // カラム名
    columnName: 'genin_knj',
  },
  {
    title: t('label.lblCareDirection'),
    key: 'careKnj',
    sortable: false,
    minWidth: '200px',
    // 小分類CD
    smallCategoryCode: '6',
    // カラム名
    columnName: 'genin_knj',
  },
])

// COPY ADD
// COPYモード時にのみ使用する「ダミー列」を識別するための特別なキー
// → 実際のレンダリング処理ではこのキーを持つヘッダをスキップする
const COPY_HDR_KEY = '__copy_checkbox'

const COPY_HDR = {
  title: '', // 見出しテキストは空（非表示）
  key: COPY_HDR_KEY, // 特殊キー
  sortable: false, // ソート不可
  width: '0px', // 実際の表示幅は0
  minWidth: '0px',
}

// Vuetifyの「no-data」行でcolspan計算がずれないように、COPYモード時はダミーヘッダを先頭に追加する
// → 表示処理ではこのダミーヘッダを除外して描画するため、見た目に影響はない
const headersForCopy = computed(() => {
  const base = issuesConsiderItemListHeader.value
  return isCopyMode.value ? [COPY_HDR, ...base] : base
})
// COPY ADD END
/**
 * ItemListData
 */
const issuesConsiderItemListData = ref<IssuesConsiderItemType[]>([])
/**
 * ローカルTwoway
 */
const local = reactive({
  // COPY CHECKBOX
  mo00018: { modelValue: false } as Mo00018Type,
  /**
   * 作成日
   */
  kijunbiYmd: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  /**
   * 作成者
   */
  orX0157: {
    value: '',
  } as OrX0157Type,
  /**
   * 期間タブ
   */
  mo00043: {
    id: Or27754Const.STR.EMPTY,
  } as Mo00043Type,
  /**
   * 機関フラグ
   */
  kikanKanriFlg: '0',
  /**
   * 種別ID
   */
  syubetsuId: '',
  /**
   * 二回目新規ボタン押下State
   */
  addBtnState: false,
  /**
   * 計画対象期間情報
   */
  planPeriod: {} as PlanPeriodInfoEntity,
  /**
   * 履歴情報
   */
  history: {} as HistoryInfoEntity,
  /**
   * 利用者ID
   */
  userId: Or27754Const.STR.EMPTY,
  /**
   * アセスメントID
   */
  raiId: Or27754Const.STR.ZERO,
  /**
   * 作成者ID
   */
  sakuseiId: Or27754Const.STR.EMPTY,
  /**
   * 事業所Id
   */
  svJigyoId: Or27754Const.STR.EMPTY,
  /**
   * 事業所名
   */
  svJigyoKnj: Or27754Const.STR.EMPTY,
  /**
   * 履歴番号
   */
  krirekiNo: Or27754Const.STR.ZERO,
  /**
   * 選定表・検討表作成区分
   */
  tableCreateKbn: Or27754Const.STR.EMPTY,
  /**
   * 更新区分
   */
  updateKbn: Or27754Const.STR.EMPTY,
  /**
   * 履歴更新区分
   */
  historyUpdateKbn: Or27754Const.STR.EMPTY,
  /**
   * 調査アセスメント種別
   */
  assType: Or27754Const.STR.EMPTY,
  /**
   * 要介護度
   */
  yokaiKbn: Or27754Const.STR.EMPTY,
  /**
   * サブ情報（B）
   */
  subInfoB: {} as SubInfoBEntity,
})

/**
 * ローカルOneway
 */
const localOneway = reactive({
  /**
   * 計画対象期間選択
   */
  planningPeriodSelectOneway: {
    plainningPeriodManageFlg: '',
    planningPeriodInfo: {},
    pageBtnAutoDisabled: false,
    showLabelMode: false,
    labelAutoDisabled: true,
  } as Or13844OnewayType,
  /**
   * 対象期間
   */
  orX0115Oneway: {
    kindId: Or27754Const.STR.EMPTY,
    sc1Id: Or27754Const.STR.EMPTY,
  } as OrX0115OnewayType,
  /**
   * 履歴選択
   */
  hisotrySelectOneway: {
    /** 履歴情報 */
    historyInfo: {
      /** アセスメントID */
      rirekiId: '',
      /** 履歴番号 */
      krirekiNo: '',
      /** 履歴総件数 */
      krirekiCnt: '',
      /** ケアチェックID */
      cc1Id: '',
    },
    pageBtnAutoDisabled: false,
    showLabelMode: false,
  } as Or13872OnewayType,
  /**
   * 作成者選択
   */
  authorSelectOneway: {
    inputMode: 'TextOnly',
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        isVerticalLabel: true,
        showItemLabel: true,
        itemLabel: t('label.author'),
        customClass: new CustomClass({
          labelClass: 'pb-1',
          labelStyle: 'background-color:rgb(var(--v-theme-background));',
        }),
        width: '148px',
      },
    },
    inputReadonly: true,
  } as OrX0157OnewayType,
  /**
   * 基準日
   */
  createDateOneway: {
    itemLabel: t('label.assumption-date'),
    isRequired: true,
    isVerticalLabel: true,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass',
      labelClass: 'mb-1',
    }),
    width: '163px',
  } as Mo00020OnewayType,
  /**
   * タブ
   */
  mo00043OnewayType: {
    tabItems: [],
    minWidth: '58px',
  } as Mo00043OnewayType,
  /**
   * or53417単方向バインド
   */
  or53417Oneway: {
    shisetuId: Or27754Const.STR.EMPTY,
    svJigyoId: Or27754Const.STR.EMPTY,
    type: Or27754Const.STR.THREE,
  } as Or53417OnewayType,
  /**
   * ［アセスメント（インターライ）CSV出力］画面
   */
  Or10279Oneway: {} as Or10279OneWayType,
  /**
   * ［履歴選択］画面
   */
  or10929Oneway: {} as HistorySelectInfoType,
  /**
   * アセスメント（インターライ）複写
   */
  orX0134OnewayModel: {} as OrX0134OnewayType,
  /**
   * 職員検索画面
   */
  or26257OnewayModel: {
    /**
     * システム略称
     */
    sysCdKbn: systemCommonsStore.getSystemAbbreviation,
    /**
     * アカウント設定
     */
    secAccountAllFlg: '-',
    /**
     * 適用事業所ＩＤリスト
     */
    svJigyoIdList: [],
    /**
     * 職員ID
     */
    shokuinId: systemCommonsStore.getStaffId,
    /**
     * システムコード
     */
    gsysCd: systemCommonsStore.getSystemCode,
    /**
     * モード
     */
    selectMode: '12',
    /**
     * 基準日
     */
    kijunYmd: systemCommonsStore.getSystemDate,
    /**
     * 事業所ID
     */
    defSvJigyoId: systemCommonsStore.getSvJigyoId,
    /**
     * フィルターフラグ
     */
    filterDwFlg: '1',
    /**
     * 雇用状態
     */
    koyouState: '-',
    /**
     * 地域フラグ
     */
    areaFlg: '-',
    /**
     * 表示名称リスト
     */
    hyoujiColumnList: [],
    /**
     * 未設定フラグ
     */
    misetteiFlg: '1',
    /**
     * 他職員参照権限
     */
    otherRead: '-',
    /**
     * 中止フラグ
     */
    refStopFlg: '-',
    /**
     * 処理フラグ
     */
    syoriFlg: '',
    /**
     * メニュー１ID
     */
    menu1Id: '',
    /**
     * 件数フラグ
     */
    kensuFlg: '1',
    /**
     * 職員IDリスト
     */
    shokuinIdList: [],
  } as Or26257OnewayType,
  or51775Oneway: {
    title: '',
    classificationID: systemCommonsStore.getStaffId,
  } as unknown as Or51775OnewayType,
  or29726Oneway: {
    kikanFlg: local.kikanKanriFlg,
    svJigyoId: local.svJigyoId,
    userId: local.userId,
    syubetsuId: local.syubetsuId,
    shisetuId: systemCommonsStore.getShisetuId,
    mstKbn: commonInfo.mstKbn,
    kaiteiKbn: commonInfo.kaiteiFlg,
  } as Or29726OnewayType,
})

/**
 * メッセージ「i-cmn-11260」 - 削除確認画面
 */
const orX0001Type = ref<OrX0001Type>({
  deleteSyubetsu: Or27754Const.STR.EMPTY,
})

/**
 * チェックボックス用のOneWayバインド
 */
const typeOneway = ref<Mo01282OnewayType>({
  items: Or27754Const.CHECK_OPTION,
  itemTitle: 'label',
  itemValue: 'value',
  disabled: isCopyMode.value,
})

/**
 * OrX0001単方向バインド -  削除確認画面
 * メッセージ「i-cmn-11260」
 */
const orX0001Oneway = ref<OrX0001OnewayType>({
  createYmd: Or27754Const.STR.EMPTY,
  kinouKnj: Or27754Const.STR.EMPTY,
  selectTabName: Or27754Const.STR.EMPTY,
  startTabName: Or27754Const.STR.EMPTY,
  endTabName: Or27754Const.STR.EMPTY,
})
/**
 * selectTextFiled
 */
const selectTextFiled = ref<string>('')
/**
 * 選択した行のindex
 */
const selectedItemIndex = ref<number>(0)
/**
 * 行選択処理
 *
 * @param index - 選択した行のインデックス
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * 行の展開状態を管理するフラグ
 */
const isRowExpanded = ref(false)
/**
 * テキスト更新処理
 *
 * @param text - 更新するテキスト
 *
 * @param index
 */
const onUpdate = (text: string, index: number) => {
  selectRow(index)
  selectTextFiled.value = text
}
/**
 * Or51775のダイアログ開閉状態を監視するcomputedプロパティ
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or29726のダイアログ開閉状態を監視するcomputedプロパティ
 */
const showDialogOr29726 = computed(() => {
  return Or29726Logic.state.get(or29726_1.value.uniqueCpId)?.isOpen ?? false
})
/**
 * GUI00792_［履歴選択］画面 双方向バインドModelValue
 */
const or10929Type = ref<Or10929Type>({
  historySelectDataList: [],
} as Or10929Type)

/**
 * GUI04512_アセスメント（インターライ）複写画面 - 双方向バインドModelValue
 */
const orX0134Type = ref<OrX0134Type>({
  sc1Id: Or27754Const.STR.EMPTY,
  raiId: Or27754Const.STR.EMPTY,
  copyFlg: Or27754Const.STR.EMPTY,
} as OrX0134Type)

/**
 * GUI00220_職員検索画面 - 双方向バインドModelValue
 */
const or26257Type = ref<Or26257Type>({
  shokuin: {
    shokuin1Knj: '',
    shokuin2Knj: '',
  },
} as Or26257Type)

/**
 * 初期化のフラグ
 */
let initFlag = false

/**
 * ダイアログ表示フラグ
 */
const showDialogOr10269 = computed(() => {
  // Or10269のダイアログ開閉状態
  return Or10269Logic.state.get(or10269.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr53417 = computed(() => {
  // Or53417のダイアログ開閉状態
  return Or53417Logic.state.get(or53417.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr10279 = computed(() => {
  // Or10279のダイアログ開閉状態
  return Or10279Logic.state.get(or10279.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOrX0115 = computed(() => {
  // OrX0115 cks_flg=1 のダイアログ開閉状態
  return OrX0115Logic.state.get(orx0115.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOrX0001 = computed(() => {
  // OrX00005 cks_flg=1 のダイアログ開閉状態
  return OrX0001Logic.state.get(orX0001.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr10929 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or10929Logic.state.get(or10929.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOrX0134 = computed(() => {
  /**
   * Or00100のダイアログ開閉状態
   */
  return OrX0134Logic.state.get(orX0134.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr26257 = computed(() => {
  // Or26257のダイアログ開閉状態
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * ライフサイクルフック
 **************************************************/
// 画面状態管理用操作変数
const screenStore = useScreenStore()

const pageComponent = screenStore.screen().supplement.pageComponent
// 子コンポーネントのユニークIDを設定する
or27754.value.uniqueCpId = pageComponent.uniqueCpId

onMounted(async () => {
  // コントロール設定
  await initControl()
  // 初期表示
  local.krirekiNo = Or27754Const.STR.ZERO
})

/**
 * 変数を初期状態に戻す
 */
const initVariable = () => {
  local.addBtnState = false
  deleteFlag.value = false
  localOneway.createDateOneway.disabled = false
  localOneway.authorSelectOneway.text!.orX0157InputOneway.disabled = false
  local.updateKbn = Or27754Const.STR.EMPTY
  local.historyUpdateKbn = Or27754Const.STR.EMPTY
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
const callbackFuncJigyo = (newJigyoId: string) => {
  if (newJigyoId) {
    // 「事業所選択」変更がある
    if (local.svJigyoId !== newJigyoId) {
      // 画面入力データに変更がある場合
      if (isEdit.value) {
        // AC004-2と同じ
        openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-10430'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'destroy1',
          secondBtnLabel: t('btn.no'),
          thirdBtnType: 'normal3',
          thirdBtnLabel: t('btn.cancel'),
        })
          .then(async (dialogResult) => {
            switch (dialogResult) {
              case 'yes':
                // AC003(保存処理)を実行し
                await _update()
                break
              case 'no':
                // 処理続き
                initVariable()
                break
              case 'cancel':
                // 処理終了
                return
            }
          })
          .catch((error) => {
            console.error(error)
          })
      }
      const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
      const jigyoInfo = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)

      local.svJigyoId = newJigyoId
      local.svJigyoKnj = jigyoInfo!.jigyoRyakuKnj
      // 画面共通情報.計画対象期間ID（ゼロに設定）
      local.planPeriod.sc1Id = Or27754Const.STR.ZERO
      // 画面共通情報.アセスメントID（ゼロに設定）
      local.history.raiId = Or27754Const.STR.ZERO

      // AC001と同じ
      local.krirekiNo = Or27754Const.STR.ZERO
      if (initFlag) {
        void init()
      }
    }
  }
}

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or27754TwoWayType>({
  cpId: Or27754Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or10929Const.CP_ID(1)]: or10929.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [OrX0001Const.CP_ID(1)]: orX0001.value,
  [Or53417Const.CP_ID(1)]: or53417.value,

  [Or29726Const.CP_ID(1)]: or29726_1.value,
  [OrX0115Const.CP_ID(1)]: orx0115.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
  [Or13844Const.CP_ID(1)]: or13844.value,
  [Or10269Const.CP_ID(1)]: or10269.value,
  [OrX0134Const.CP_ID(1)]: orX0134.value,
  [Or13872Const.CP_ID(1)]: or13872.value,
  [Or26257Const.CP_ID(1)]: or26257.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
})

// ★利用者一覧詳細表示(Or00248)の「領域キー」を設定
Or00248Logic.state.set({
  uniqueCpId: or00248.value.uniqueCpId,
  state: {
    regionKey: Or00248Const.DEFAULT.REGION_KEY, // 「領域キー」を設定
    displayUserInfoSectionFlg: true,
  },
})

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.consider-issues'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: true,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
    tooltipTextSaveBtn: t('tooltip.save'),
    tooltipTextPrintBtn: t('tooltip.care-plan2-print-setting-btn'),
    tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
    tooltipTextMasterBtn: t('tooltip.regular-master-icon'),
  },
})

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */
const callbackUserChange = (newSelfId: string) => {
  if (newSelfId) {
    // 「利用者」変更がある
    if (local.userId !== newSelfId) {
      // 画面入力データに変更がある場合
      if (isEdit.value) {
        // AC004-2と同じ
        openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-10430'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'destroy1',
          secondBtnLabel: t('btn.no'),
          thirdBtnType: 'normal3',
          thirdBtnLabel: t('btn.cancel'),
        })
          .then(async (dialogResult) => {
            switch (dialogResult) {
              case 'yes':
                // AC003(保存処理)を実行し
                await _update()
                break
              case 'no':
                // 処理続き
                initVariable()
                break
              case 'cancel':
                // 処理終了
                return
            }
          })
          .catch((error) => {
            console.error(error)
          })
      }
      // 画面.利用者ID = 返回情報.利用者ID
      local.userId = newSelfId
      // 画面共通情報.計画対象期間ID（ゼロに設定）
      local.planPeriod.sc1Id = Or27754Const.STR.ZERO
      // 画面共通情報.アセスメントID（ゼロに設定）
      local.history.raiId = Or27754Const.STR.ZERO
      void init()
    }
  }
}

systemCommonsStore.setUserSelectZenkaiSvJigyoIdList([''])
systemCommonsStore.setUserSelectSelfId('')

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange)

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * refValueを初期化
 */
const refValueInit = () => {
  refValue.value = {
    kijunbiYmd: local.kijunbiYmd.value,
    sakuseiId: local.sakuseiId,
  } as Or27754TwoWayType
  // 初期値に設定する
  useScreenStore().setCpTwoWay({
    cpId: Or27754Const.CP_ID(1),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}
/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // 基盤処理 - お気に入りボタンが押下された場合、お気に入り処理を実行する
      setOr11871Event({ favoriteEventFlg: false })
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await _update()
      setOr11871Event({ saveEventFlg: false })
    }
    if (newValue.createEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 新規ボタンが押下された場合、新規作成処理を実行する
        await addBtnClick()
        setOr11871Event({ createEventFlg: false })
      }
    }
    if (newValue.printEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 印刷ボタンが押下された場合、印刷設定画面を表示する
        await printSettingIconClick()
        setOr11871Event({ printEventFlg: false })
      }
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      await masterIconClick()
      setOr11871Event({ masterEventFlg: false })
    }
  }
)

/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => Or13844Logic.event.get(or13844.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    if (!newValue?.preBtnClickFlg && !newValue.nextBtnClickFlg) {
      return
    }
    // 画面入力データに変更がある場合
    if (isEdit.value) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case 'yes':
          // AC003(保存処理)を実行し
          await _update()
          break
        case 'no':
          // 処理続き
          initVariable()
          break
        case 'cancel':
          // 処理終了
          return
      }
    }

    setOr13844Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
    local.krirekiNo = Or27754Const.STR.ZERO
    local.history = {} as HistoryInfoEntity

    if (newValue?.preBtnClickFlg) {
      await periodChangeSelect(Or27754Const.PERIOD_KBN_PREV)
    } else if (newValue.nextBtnClickFlg) {
      await periodChangeSelect(Or27754Const.PERIOD_KBN_NEXT)
    }
  },
  { deep: true }
)

/**
 * AC017_「履歴-前へアイコンボタン」押下
 * AC018_「履歴-次へアイコンボタン」押下
 * 履歴変更の監視
 */
watch(
  () => Or13872Logic.event.get(or13872.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }
    if (!newValue?.preBtnClickFlg && !newValue.nextBtnClickFlg) {
      return
    }
    // 画面入力データに変更がある場合
    if (isEdit.value) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case 'yes':
          // AC003(保存処理)を実行し
          await _update()
          break
        case 'no':
          // 処理続き
          initVariable()
          break
        case 'cancel':
          // 処理終了
          return
      }
    }

    setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })

    if (newValue?.preBtnClickFlg) {
      //「履歴-前へ アイコンボタン」押下
      await assessmentInterRAIAHistoryChangeSelect(Or27754Const.PERIOD_KBN_PREV)

      // 履歴情報がNULLの場合
      if (!local.history) {
        // 処理終了にする。
        return
      }

      await setFormData()
    } else if (newValue?.nextBtnClickFlg) {
      //「履歴-次へ アイコンボタン」押下
      await assessmentInterRAIAHistoryChangeSelect(Or27754Const.PERIOD_KBN_NEXT)

      // 履歴情報がNULLの場合
      if (!local.history) {
        // 処理終了にする。
        return
      }

      await setFormData()
    }
  },
  { deep: true }
)

/**
 * AC011_「削除」押下_AC011
 */
watch(
  () => orX0001Type.value,
  (newValue) => {
    if (newValue) {
      // 返す削除種別が0（キャンセル）の場合
      if (Or27754Const.STR.ZERO === newValue.deleteSyubetsu) {
        // 処理終了にする
        return
      }

      // 削除確認画面ダイアログに「現在表示している画面のみ削除する。」を選択する場合
      if (Or27754Const.STR.ONE === newValue.deleteSyubetsu) {
        // 更新区分を「D:削除」
        local.updateKbn = UPDATE_KBN.DELETE
        // 履歴更新区分を「U:更新」
        local.historyUpdateKbn = UPDATE_KBN.UPDATE
        // アセスメント(インターライ)画面履歴の最新情報を取得する
        deleteCurrentTabSubInfo(newValue.deleteSyubetsu)
      }
      // 削除確認画面ダイアログに「表示している画面を履歴ごと削除する」を選択する場合
      else if (Or27754Const.STR.TWO === newValue.deleteSyubetsu) {
        // 更新区分を「D:削除」
        local.updateKbn = UPDATE_KBN.DELETE
        // 履歴更新区分を「D:削除」
        local.historyUpdateKbn = UPDATE_KBN.DELETE
        deleteCurrentTabSubInfo(newValue.deleteSyubetsu)
      }

      // 作成者選択アイコンボタン
      localOneway.authorSelectOneway.text!.orX0157InputOneway.disabled = true
      // 基準日、基準日カレンダを非活性
      localOneway.createDateOneway.disabled = true

      // ボタンは実行無効(新規、複写、印刷、削除)
      deleteFlag.value = true
    }
  }
)

/**
 * 職員検索画面変更ワッチャー
 */
watch(
  () => or26257Type.value,
  (newValue) => {
    // 返す職員IDが空ではなく、または返す職員IDが「0」ではない場合
    if (newValue?.shokuin && Or27754Const.STR.ONE !== newValue?.shokuin.chkShokuId) {
      local.orX0157.value = newValue?.shokuin.shokuin1Knj + ' ' + newValue.shokuin.shokuin2Knj
      local.sakuseiId = newValue?.shokuin.chkShokuId
      refValue.value!.sakuseiId = newValue?.shokuin.chkShokuId
    }
    // 上記以外の場合
    else {
      // 画面項目をクリア
      // 画面.作成者ID
      local.sakuseiId = Or27754Const.STR.EMPTY
      // 画面.作成者名
      local.orX0157.value = Or27754Const.STR.EMPTY
    }
  },
  { deep: true }
)

/**
 * 基準日の変更
 */
watch(
  () => local.kijunbiYmd,
  () => {
    refValue.value!.kijunbiYmd = local.kijunbiYmd.value
  }
)
/**************************************************
 * 関数
 **************************************************/

// COPY
const selectedRows = ref<IssuesConsiderItemType[]>([])
const rows = computed<IssuesConsiderItemType[]>(() => refValue.value?.kadaiList ?? [])
const isIndeterminate = computed(() => {
  const all = rows.value
  const selected = selectedRows.value.length
  return selected > 0 && selected < all.length
})

const toggleSelectRow = () => {
  const all = rows.value
  selectedRows.value = all.filter((i) => i?.checked?.modelValue === true)
  local.mo00018.modelValue = selectedRows.value.length === all.length && all.length > 0
}
// COPY
/**
 * 全選択
 *
 * @param val - チェックボックス
 */
const allCheck = (val: boolean | Mo00018Type) => {
  const all = rows.value
  if (all.length === 0) {
    local.mo00018.modelValue = false
    return
  }

  const next = typeof val === 'boolean' ? val : !!val?.modelValue

  for (const item of all) {
    item.checked ??= { modelValue: false }
    item.checked.modelValue = next
  }

  selectedRows.value = next ? [...all] : []
  local.mo00018.modelValue = next
}

/**
 * 現在のタブページのデータを削除する
 *
 * @param param - 削除ボタンの選択値
 */
const deleteCurrentTabSubInfo = (param: string) => {}

/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * メニューエベントを設定
 *
 * @param state - イベント
 */
const setOr11871State = (state: Record<string, boolean>) => {
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: state,
  })
}

/**
 * 画面コントロール表示設定
 */
const setFormData = async () => {
  localOneway.planningPeriodSelectOneway.plainningPeriodManageFlg = local.kikanKanriFlg
  // 期間管理フラグが「1:管理する」の場合
  if (Or27754Const.STR.ONE === local.kikanKanriFlg) {
    // 計画対象期間 を表示にする
    planPeriodShow.value = true
    // 計画期間情報がNULLではない場合
    if (local.planPeriod) {
      // 履歴表示
      historyShow.value = true
      // 基準日表示
      baseDateShow.value = true
      // 作成者表示
      authorShow.value = true
      // 計画対象期間をセットする
      localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
        id: local.planPeriod.sc1Id,
        period: local.planPeriod.startYmd + SPACE_WAVE + local.planPeriod.endYmd,
        periodNo: local.planPeriod.periodNo ?? '0',
        periodCnt: local.planPeriod.periodCnt ?? '0',
      }
    }
    // 計画期間情報がNULLの場合
    else {
      // 計画対象期間-ページングを"0 / 0"で表示にする
      // 計画対象期間を固定文字「計画対象期間アイコンボタンから（改行）期間登録を行ってください」で表示※太字、赤色
      localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
        id: '0',
        period: '',
        periodNo: '0',
        periodCnt: '0',
      }
      // コンテンツエリア＞作成情報＞履歴、作成者、基準日、および、入力フォームを非表示にする
      // 履歴非表示
      historyShow.value = false
      // 基準日非表示
      baseDateShow.value = false
      // 作成者非表示
      authorShow.value = false

      local.addBtnState = false
    }
  }
  // 期間管理フラグが「0:管理しない」の場合
  else if (Or27754Const.STR.ZERO === local.kikanKanriFlg) {
    // 計画対象期間 を非表示にする
    planPeriodShow.value = false
  }

  // 履歴情報がNULLではない場合
  if (local.history) {
    local.krirekiNo = local.history.krirekiNo
    // 基準日 ＝ 履歴情報.基準日
    local.kijunbiYmd.value = local.history.assDateYmd
    if (local.kijunbiYmd.mo01343) {
      local.kijunbiYmd.mo01343.value = local.history.assDateYmd ?? ''
    }

    // 作成者 ＝ 履歴情報.職員名
    local.orX0157.value = local.history.shokuinName
    local.sakuseiId = local.history.shokuinId

    // 履歴-ページング = 履歴情報.履歴番号＋" / "＋履歴情報.履歴総件数
    localOneway.hisotrySelectOneway.historyInfo = {
      /** アセスメントID */
      rirekiId: local.history.raiId ?? Or27754Const.STR.EMPTY,
      /** 履歴番号 */
      krirekiNo: local.history.krirekiNo ?? '1',
      /** 履歴総件数 */
      krirekiCnt: local.history.krirekiCnt ?? '1',
    }

    getDataTable()
  }
  // 計画期間情報がNULLの場合
  else {
    // 画面.計画対象期間IDが「0」に設定する
    // ・履歴-ページング = "1 / 1"
    localOneway.hisotrySelectOneway.historyInfo = {
      /** アセスメントID */
      rirekiId: Or27754Const.STR.ZERO,
      /** 履歴番号 */
      krirekiNo: Or27754Const.STR.ONE,
      /** 履歴総件数 */
      krirekiCnt: Or27754Const.STR.ONE,
    }
    // 基準日を設定
    local.kijunbiYmd.value = systemCommonsStore.getSystemDate ?? ''

    // 作成者を設定
    // 作成者 ＝ ログイン情報.職員名
    local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? Or27754Const.STR.EMPTY

    // 「AC004」の処理を行
    await addBtnClick()
  }
  refValueInit()
  isLoading.value = false
}

/**
 * コントロール初期化
 */
const initControl = async () => {
  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
      verticalLayout: true,
    },
  })
  // 利用者を全選択です。
  const filter = systemCommonsStore.getUserSelectHistoryFilterInitials()
  if (filter === undefined || filter.length === 0) {
    systemCommonsStore.setUserSelectFilterInitials([Or27754Const.STR_ALL])
  }

  // 共通情報.事業所IDが事業所情報リストに存在する場合
  if (systemCommonsStore.getSvJigyoIdList && systemCommonsStore.getSvJigyoIdList.length > 0) {
    // 基盤作成 共通情報.事業所IDに対する事業所を選択
  }
  // 上記以外の場合
  else {
    // 基盤作成 事業所情報リスト.1件目を選択
  }

  // 共通処理の登録権限チェックを行う
  // if (await hasRegistAuth()) {
  //   // 権限有る：活性
  //   setOr11871State({ disabledSaveBtn: false })
  // } else {
  //   // 権限無し：非活性
  //   setOr11871State({ disabledSaveBtn: true })
  // }

  // 共通処理の印刷権限チェックを行う
  // if (await hasPrintAuth()) {
  //   // 権限有る：活性
  //   setOr11871State({ disabledPrintBtn: false })
  // } else {
  //   // 権限無し：非活性
  //   setOr11871State({ disabledPrintBtn: true })
  // }

  // 共通情報.e-文書法対象機能の電子ファイル保存設定区分が「1:適用する」の場合
  if (Or27754Const.STR.ONE === systemCommonsStore.getEBunshoKbn) {
    logBtnShowFlg.value = true
  } else {
    // その他場合
    logBtnShowFlg.value = false
  }
}

/**
 *  AC001_初期表示
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  await periodChangeSelect(Or27754Const.PERIOD_KBN_OPEN)
  initFlag = true
}

/**
 * 計画期間変更【共通処理】/初期情報取得
 *
 * @param pageFlag - 0:選択している期間ID画面 | 1:選択している期間ID画面の前画面 | 2:選択している期間ID画面の後画面
 */
const periodChangeSelect = async (pageFlag: string) => {
  isLoading.value = true
  // 計画期間変更【共通処理】/初期情報取得
  const inputData: IAssessmentInterRAIPeriodChangeSelectInEntity = {
    svJigyoId: local.svJigyoId ?? Or27754Const.STR.EMPTY,
    userId: local.userId ?? Or27754Const.STR.EMPTY,
    shisetuId: systemCommonsStore.getShisetuId ?? Or27754Const.STR.EMPTY,
    sc1Id: local.planPeriod?.sc1Id ?? Or27754Const.STR.ZERO,
    krirekiNo: local.krirekiNo,
    raiId: local.history?.raiId ?? Or27754Const.STR.ZERO,
    pageFlag: pageFlag,
    menu2Name: MID_LINE,
    menu3Name: Or27754Const.MENU_3_KNJ,
    freeParameter: MID_LINE,
  }

  const resp: IAssessmentInterRAIPeriodChangeSelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIPeriodChangeSelect',
    inputData
  )
  if (SUCCESS_STATUS_CODE === resp.statusCode || 'success' === resp.statusCode) {
    // 上記で戻るエラー区分が「1」の場合
    if (resp.data.errKbn === Or27754Const.STR.ONE) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11262'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })

      switch (dialogResult) {
        case 'yes':
          isLoading.value = false
          // 処理終了
          return
      }
    }
    // 上記で戻るエラー区分が「2」の場合
    else if (resp.data.errKbn === Or27754Const.STR.TWO) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11263'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })

      switch (dialogResult) {
        case 'yes':
          isLoading.value = false
          // 処理終了
          return
      }
    }

    local.planPeriod = resp.data.planPeriodInfo
    local.history = resp.data.historyInfo
    local.kikanKanriFlg = resp.data.kikanKanriFlg
    local.syubetsuId = resp.data.syubetsuId
  }

  await setFormData()
}
/**
 * 使用するコード区分一覧
 */
const CODE_KBN_LIST = [
  CmnMCdKbnId.M_CD_KBN_ID_HEALTH_STATUS,
  CmnMCdKbnId.M_CD_KBN_ID_ADL,
  CmnMCdKbnId.M_CD_KBN_ID_IADL,
  CmnMCdKbnId.M_CD_KBN_ID_COGNITION,
  CmnMCdKbnId.M_CD_KBN_ID_COMMUNICATION_ABILITY,
  CmnMCdKbnId.M_CD_KBN_ID_SOCIETY_RELATIONSHIP,
  CmnMCdKbnId.M_CD_KBN_ID_URINATION_AND_DEFECATION,
  CmnMCdKbnId.M_CD_KBN_ID_BEDSORE_AND_SKIN_PROBLEM,
  CmnMCdKbnId.M_CD_KBN_ID_ORAL_HYGIENE,
  CmnMCdKbnId.M_CD_KBN_ID_MEAL_INTAKE,
  CmnMCdKbnId.M_CD_KBN_ID_PROBLEM_BEHAVIOR,
  CmnMCdKbnId.M_CD_KBN_ID_CARE_ABILITY,
  CmnMCdKbnId.M_CD_KBN_ID_LIVING_ENVIRONMENT,
  CmnMCdKbnId.M_CD_KBN_ID_SPECIAL_SITUATION,
]
/**
 * 画面履歴の最新情報を取得
 */
const getHistoryInfo = async (): Promise<boolean> => {
  let keepFlag = false
  const inputData: AssessmentInterRAIAHistorySelectEntity = {
    raiId: local.history?.raiId ?? Or27754Const.STR.ZERO,
  }
  const resData: AssessmentInterRAIAHistorySelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIHistorySelect',
    inputData
  )
  if (SUCCESS_STATUS_CODE === resData.statusCode || 'success' === resData.statusCode) {
    // 取得したインターライ方式履歴情報<>NULL
    if (resData.data) {
      // 選定アセスメント種別 > 0
      if (parseInt(resData.data?.capType) > 0) {
        // 検討アセスメント種別 > 0
        if (parseInt(resData.data?.plnType) > 0) {
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11267'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'normal3',
            thirdBtnLabel: t('btn.cancel'),
          })
          switch (dialogResult) {
            // はい
            case 'yes':
              // 画面.選定表・検討表作成区分 = "1"、処理続き
              local.tableCreateKbn = Or27754Const.STR.ONE
              keepFlag = true
              break
            // キャンセル
            case 'cancel':
              // 処理終了
              keepFlag = false
              break
          }
        }
        // 検討アセスメント種別 <= 0
        else {
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11266'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'normal3',
            thirdBtnLabel: t('btn.cancel'),
          })
          switch (dialogResult) {
            // はい
            case 'yes':
              // 画面.選定表・検討表作成区分 = "0"、処理続き
              local.tableCreateKbn = Or27754Const.STR.ZERO
              keepFlag = true
              break
            // キャンセル
            case 'cancel':
              // 処理終了
              keepFlag = false
              break
          }
        }
      }
    }
  }
  return keepFlag
}

/**
 * 行のデータをコピーする処理
 *
 * @param _data - コピーするデータ
 */
const handleCopyData = (_data: Or29726CopyData) => {
  console.log('Copying data:', _data)
}
/**
 * AC003_「保存ボタン」押下
 */
const _update = async () => {
  // バリデーションチェック
  if (!valid.value) {
    return
  }
  // 画面入力データに変更がない場合
  if (
    !isEdit.value ||
    Or27754Const.STR.ZERO === local.history.raiId ||
    local.updateKbn === UPDATE_KBN.DELETE ||
    local.historyUpdateKbn === UPDATE_KBN.DELETE
  ) {
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-21800'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // 変数の初期化
    initVariable()
    // 処理終了にする
    return
  }
  // アセスメント(インターライ)画面履歴の最新情報を取得する
  if (!(await getHistoryInfo())) {
    // キャンセル：処理終了
    return
  }

  // 画面タブ入力タブデータに変更がある場合
  if (isTabDataEdit.value) {
    // 更新区分がNULLの場合
    if (!local.updateKbn) {
      // 更新区分 = U
      local.updateKbn = UPDATE_KBN.UPDATE
    }
  }

  // 画面共通入力データに変更がある場合
  if (isCommonDataEdit.value) {
    // 履歴更新区分がNULLの場合
    if (!local.historyUpdateKbn) {
      // 履歴更新区分 = U
      local.historyUpdateKbn = UPDATE_KBN.UPDATE
    }
  }
}

/**
 * AC004_「新規ボタン」押下
 */
const addBtnClick = async () => {
  // 期間管理フラグが「1:管理する」
  if (local.kikanKanriFlg === Or27754Const.STR.ONE) {
    // 計画期間情報.期間総件数 = 0（期間なし）
    if (
      localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodCnt === Or27754Const.STR.ZERO
    ) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11300'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.ok'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if ('yes' === dialogResult) {
        // AC013を実行
        await planningPeriodSelectClick()
      }
    }
  }

  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }
  // 二回目新規ボタン押下する場合
  if (local.addBtnState) {
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.attention'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.assessment')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  }
  // アセスメント(インターライ)画面の新規情報を取得する
  await getCreateSubInfo()

  // 画面共通情報.アセスメントID = ゼロ
  local.history.raiId = Or27754Const.STR.ZERO

  // 画面共通情報.作成者ID = 共通情報.職員ID
  local.sakuseiId = systemCommonsStore.getStaffId ?? ''

  // 画面.履歴番号 = 履歴の最大値+1
  // 画面.履歴-ページング = 画面.履歴番号/画面.履歴番号
  localOneway.hisotrySelectOneway.historyInfo = {
    /** アセスメントID */
    rirekiId: Or27754Const.STR.ZERO,
    /** 履歴番号 */
    krirekiNo: String(Number(local.history.krirekiCnt ?? Or27754Const.STR.ZERO) + 1),
    /** 履歴総件数 */
    krirekiCnt: String(Number(local.history.krirekiCnt ?? Or27754Const.STR.ZERO) + 1),
  }
  local.krirekiNo = localOneway.hisotrySelectOneway.historyInfo.krirekiNo

  // ログイン情報.職員名
  local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''

  // 共通情報.基準日
  local.kijunbiYmd.value = systemCommonsStore.getSystemDate ?? ''
  if (local.kijunbiYmd.mo01343) {
    local.kijunbiYmd.mo01343.value = systemCommonsStore.getSystemDate ?? ''
  }

  local.addBtnState = true

  // 画面.履歴更新区分 = "C"（新規）
  local.historyUpdateKbn = UPDATE_KBN.CREATE

  // 画面.更新区分 = "C"（新規）
  local.updateKbn = UPDATE_KBN.CREATE

  // それぞれサブ画面の新規操作
  AllSubAdd()
}

/**
 * 新規情報を取得する
 */
const getCreateSubInfo = async () => {
  isLoading.value = true
  // バックエンドAPIから初期情報取得
  const inputData: IAssessmentInterRAINewSelectInEntity = {
    userId: local.userId ?? '',
    lsYmd: systemCommonsStore.getSystemDate ?? '',
    svJigyoId: local.svJigyoId ?? '',
    subKbn: local.mo00043.id,
  }

  const resp: IAssessmentInterRAINewSelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAINewSelect',
    inputData
  )
  if (SUCCESS_STATUS_CODE === resp.statusCode || 'success' === resp.statusCode) {
    local.assType = resp.data.assType
    local.yokaiKbn = resp.data.yokaiKbn
    local.subInfoB = resp.data.subInfoB
  }
  isLoading.value = false
}

/**
 * サブ画面の新規操作
 */
const AllSubAdd = () => {
  // アセスメント(インターライ)画面「A ~ V」の新規情報を取得する。
}

/**
 * アセスメント(インターライ)画面履歴の最新情報を取得する
 */
const getDataTable = async () => {
  if (!historyShow.value && !authorShow.value && !baseDateShow.value) {
    return
  }
  isLoading.value = true
  try {
    await getIssuesConsiderDetailAcquisitionInfo()
    issuesConsiderItemListData.value = []
    // 汎用コードを順次取得
    for (const codeKbn of CODE_KBN_LIST) {
      await getAndMapIssuesData(codeKbn)
    }
  } finally {
    isLoading.value = false
  }
}

// COPY
// GUI00654でテーブル値を再読み込みする機能
defineExpose({ getDataTable, local, commonInfo, selectedRows })

/**
 * getIssuesConsiderDetailAcquisitionInfo
 */
const getIssuesConsiderDetailAcquisitionInfo = async () => {
  const param: IssueConsiderationInputType = {
    event: local.event,
    svJigyoId: local.svJigyoId,
    userId: local.userId,
    sc1Id: local.sc1Id,
    syubetsuId: '',
    shisetuId: local.shisetuId,
    mstKbn: commonInfo.mstKbn,
    kky1Id: local.kky1Id,
    level1Id: local.level1Id,
  }
  const { data }: IssueConsiderationOutEntity = await ScreenRepository.select(
    'issueConsiderationItem',
    param
  )
  if (data) {
    issuesConsiderDetailAcquisitionInfo.value = data.kadaiList
  } else {
    issuesConsiderDetailAcquisitionInfo.value = []
  }
}
/**
 * コード取得 → マッピング
 *
 * @param codeKbn - number
 */
const getAndMapIssuesData = async (codeKbn: number) => {
  await CmnSystemCodeRepository.getCodes({
    selectCodeKbnList: [{ mCdKbnId: codeKbn }],
  })

  const result = CmnSystemCodeRepository.filterCodeGroup(codeKbn)
  // 正しいデータ形式で処理
  mapIssuesData(result)
}

/**
 * データマッピング
 *
 * @param codeKbnItem -CodeGroup
 */
const mapIssuesData = (codeKbnItem: CodeGroup) => {
  if (codeKbnItem.codeList.length === 0) {
    return
  }
  const getDataDetail: IssuesConsiderItemType =
    issuesConsiderDetailAcquisitionInfo.value?.find(
      (i: IssuesConsiderItemType) => i.id === codeKbnItem.mCdKbnId
    ) ?? ({} as IssuesConsiderItemType)
  const getListCode = () => {
    const arr = codeKbnItem.codeList
    if (arr.length !== 0 && arr.length < 8) {
      while (arr.length < 8) {
        arr.push({ label: '', value: 'disabled' })
      }
    }

    return arr
  }
  const mappedData: IssuesConsiderItemType = {
    id: codeKbnItem.mCdKbnId,
    item: codeKbnItem.codeKbnName,
    details: getListCode(),
    check: codeKbnItem.codeList.map((item: CodeType) => {
      const found = (getDataDetail?.check || []).find((i: CodeListItem) => i.id === item.value)
      return {
        id: item.value,
        value: found?.value ?? '',
        modelValue: found?.value ?? '',
      } as CodeListItem
    }),

    joukyouKnj: { value: getDataDetail?.joukyouKnj ?? '' },
    geninKnj: { value: getDataDetail?.geninKnj ?? '' },
    ikouKnj: { value: getDataDetail?.ikouKnj ?? '' },
    highlightCareStatus: { value: getDataDetail?.highlightCareStatus ?? '' },
    kadaiKnj: { value: getDataDetail?.kadaiKnj ?? '' },
    careKnj: { value: getDataDetail?.careKnj ?? '' },
    // CHECKBOX IN GUI00654
    checked: {
      modelValue: false,
    },
  }

  issuesConsiderItemListData.value.push(mappedData)

  // ID順にソート
  issuesConsiderItemListData.value.sort(
    (a: IssuesConsiderItemType, b: IssuesConsiderItemType) => Number(a.id) - Number(b.id)
  )
  screenStore.setCpTwoWay({
    cpId: Or27754Const.CP_ID(0),
    uniqueCpId: or27754.value.uniqueCpId,
    value: {
      kadaiList: issuesConsiderItemListData.value,
    },
    isInit: true,
  })
}

/**
 * AC005_「複写ボタン」押下
 */
const copyBtnClick = () => {
  // COPY ADD
  // Or29726のダイアログ開閉状態を更新する
  Or29726Logic.state.set({
    uniqueCpId: or29726_1.value.uniqueCpId,
    state: { isOpen: true },
  })

  const result = {
    type: '',
  }

  // 単一セクション複写情報を返却する場合
  if (result.type === '1') {
    return
  }
  // 複数セクション複写情報を返却するの場合
  else if (result.type === '2') {
    void _update(true)
  }
}

/**
 * AC006_「印刷設定アイコンボタン」押下
 */
const printSettingIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }
}

/**
 * 帳票番号取得
 *
 * @param tabId - タブ選択ID
 */
const getPrtNo = (tabId: string) => {
  console.log('tabId', tabId)
}

/**
 * AC007_「マスタ他設定アイコンボタン」押下
 */
const masterIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // Or53417のダイアログ開閉状態を更新する
  localOneway.or53417Oneway = {
    shisetuId: systemCommonsStore.getShisetuId,
    svJigyoId: local.svJigyoId,
  } as Or53417OnewayType
  Or53417Logic.state.set({
    uniqueCpId: or53417.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC009_「ログ」押下
 */
const logClick = () => {
  // TODO
  void openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('WEB化後ログボタン機能未定'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
}

/**
 * AC010_「CSV」押下
 */
const csvClick = () => {
  // GUI00788 ［アセスメント（インターライ）CSV出力］画面をポップアップで起動する
  // 获取当前日期
  const now = new Date()
  // 获取当前月的开始日期
  const startDate = new Date(now.getFullYear(), now.getMonth(), 1)
  // 获取当前月的结束日期
  const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
  localOneway.Or10279Oneway = {
    staffId: systemCommonsStore.getStaffId,
    baseDate: systemCommonsStore.getSystemDate,
    officeId: systemCommonsStore.getStaffId,
    kinouName: Or27754Const.MENU_3_KNJ,
    startScYmd: getYmdStr(startDate),
    endScYmd: getYmdStr(endDate),
  } as Or10279OneWayType

  // Or10279のダイアログ開閉状態を更新する
  Or10279Logic.state.set({
    uniqueCpId: or10279.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 日付(yyyy/MM/dd)を取得する
 *
 * @param date - 日付
 */
const getYmdStr = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}${month}${day}`
}

/**
 * AC011_「削除」押下
 */
const deleteClick = () => {
  // 画面.計画期間情報が空の場合
  if (!localOneway.planningPeriodSelectOneway.planningPeriodInfo) {
    // 処理終了にする
    return
  }
  // 二回目削除ボタン押下する場合
  if (deleteFlag.value) {
    // 処理終了にする
    return
  }
  // GUI04471_［削除確認］画面をポップアップで起動する
  orX0001Oneway.value.createYmd = local.kijunbiYmd.value ?? Or27754Const.STR.EMPTY
  orX0001Oneway.value.kinouKnj = t('label.assessment')
  orX0001Oneway.value.selectTabName = local.mo00043.id
  orX0001Oneway.value.startTabName = '選定表、検討表、A'
  orX0001Oneway.value.endTabName = 'V'

  // OrX0001のダイアログ開閉状態を更新する
  OrX0001Logic.state.set({
    uniqueCpId: orX0001.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC013_「計画対象期間選択アイコンボタン」押下
 */
const planningPeriodSelectClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // GUI00070 対象期間画面をポップアップで起動する
  localOneway.orX0115Oneway = {
    kindId: local.syubetsuId,
    sc1Id: local.planPeriod.sc1Id,
  } as OrX0115OnewayType
  OrX0115Logic.state.set({
    uniqueCpId: orx0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「履歴選択アイコンボタン」押下
 */
const historySelectClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // GUI00792 ［履歴選択］画面 アセスメント(インターライ)をポップアップで起動
  localOneway.or10929Oneway = {
    svJigyoId: local.svJigyoId ?? Or27754Const.STR.EMPTY,
    userId: local.userId ?? Or27754Const.STR.EMPTY,
    sc1Id: local.planPeriod.sc1Id ?? Or27754Const.STR.EMPTY,
    raiId: local.history.raiId ?? Or27754Const.STR.EMPTY,
  } as HistorySelectInfoType
  // Or10929のダイアログ開閉状態を更新する
  Or10929Logic.state.set({
    uniqueCpId: or10929.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 作成者選択画面クリック
 *
 */
const authorSelectClick = () => {
  const list: SvJigyo[] = []
  for (const item of systemCommonsStore.getSvJigyoIdList) {
    if (item) {
      list.push({
        svJigyoId: item,
      } as SvJigyo)
    }
  }
  localOneway.or26257OnewayModel.svJigyoIdList = list
  // GUI00220 職員検索画面をポップアップで起動
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = (
  uniqueCpId: string,
  state: Or21813StateType
): Promise<typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO | typeof DIALOG_BTN.CANCEL> => {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(uniqueCpId)

        let result = DIALOG_BTN.YES as
          | typeof DIALOG_BTN.YES
          | typeof DIALOG_BTN.NO
          | typeof DIALOG_BTN.CANCEL

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }
        if (event?.thirdBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 確認ダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = (
  uniqueCpId: string,
  state: Or21814OnewayType
): Promise<typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO | typeof DIALOG_BTN.CANCEL> => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = DIALOG_BTN.YES as
          | typeof DIALOG_BTN.YES
          | typeof DIALOG_BTN.NO
          | typeof DIALOG_BTN.CANCEL

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }
        if (event?.thirdBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * AC016_選択前の履歴から変更
 *
 * @param selectItem - 選択の履歴
 */
const historySelectChange = async (selectItem: HistorySelectTableDataItem | undefined) => {
  // 選択前の履歴から変更がなく
  if (
    selectItem?.raiId === localOneway.hisotrySelectOneway.historyInfo?.rirekiId &&
    !selectItem?.sc1Id &&
    Or27754Const.STR.ZERO === selectItem?.sc1Id
  ) {
    // 処理終了にする。
    return
  }
  // 上記以外の場合
  // 選択前の履歴から変更がある場合
  else {
    // アセスメント(インターライ)画面履歴変更処理
    local.history.raiId = selectItem ? selectItem.raiId : Or27754Const.STR.EMPTY

    await assessmentInterRAIAHistoryChangeSelect(Or27754Const.PERIOD_KBN_OPEN)

    // 履歴情報がNULLの場合
    if (!local.history) {
      // 処理終了にする。
      return
    }

    await setFormData()
  }
}

/**
 * 保存後
 *
 * @param errKbn - エラー区分
 *
 * @param sc1Id - 計画対象期間ID
 *
 * @param raiId - アセスメントID
 */
const saveEnd = async (errKbn: string, sc1Id: string, raiId: string) => {
  // 上記で戻るエラー区分が「1」の場合
  if ('1' === errKbn) {
    await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40019'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
  }
  // 上記で戻るエラー区分が「2」の場合
  else if ('2' === errKbn) {
    // 以下のメッセージを表示
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11457'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
  }
  // 保存再表示の場合
  // 変数の初期化
  initVariable()

  local.planPeriod.sc1Id = sc1Id
  local.history.raiId = raiId
  // AC001と同じ
  await init()
}

/**
 * 戻り値
 *
 * @param newValue - 新しい値
 */
const handleReturn = async (newValue: OrX0134Type) => {
  if (newValue) {
    // 複写画面の戻り値.履歴IDが空の場合
    if (!newValue.raiId) {
      // 処理終了
      return
    }
  }
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
const setOr13844Event = (event: Record<string, boolean>) => {
  Or13844Logic.event.set({
    uniqueCpId: or13844.value.uniqueCpId,
    events: event,
  })
}

/**
 * Or13872イベント発火
 *
 * @param event - イベント
 */
const setOr13872Event = (event: Record<string, boolean>) => {
  Or13844Logic.event.set({
    uniqueCpId: or13872.value.uniqueCpId,
    events: event,
  })
}
/**
 * AC017_「履歴-前へアイコンボタン」押下
 * AC018_「履歴-次へアイコンボタン」押下
 *
 * @param pageFlag - 0:選択している期間ID画面 | 1:選択している期間ID画面の前画面 | 2:選択している期間ID画面の後画面
 */
const assessmentInterRAIAHistoryChangeSelect = async (pageFlag: string) => {
  isLoading.value = true
  // バックエンドAPIから初期情報取得
  const inputData: IAssessmentInterRAIHistoryChangeSelectInEntity = {
    svJigyoId: local.svJigyoId ?? Or27754Const.STR.EMPTY,
    userId: local.userId ?? Or27754Const.STR.EMPTY,
    sc1Id: local.planPeriod?.sc1Id ?? Or27754Const.STR.ZERO,
    raiId: local.history?.raiId ?? Or27754Const.STR.ZERO,
    kikanFlag: pageFlag,
  }

  const resp: IAssessmentInterRAIHistoryChangeSelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIHistoryChangeSelect',
    inputData
  )

  if (SUCCESS_STATUS_CODE === resp.statusCode || 'success' === resp.statusCode) {
    local.history = resp.data.historyInfo
  }
  isLoading.value = false
}

/**
 * ボタン押下時処理
 *
 * @description
 * 自身のEventStatus領域のイベントフラグを更新する
 * AC021 「情報収集参照 アイコンボタン押下
 */
function onClickInformationCollectionSheet() {
  // TODO: GUI00663 情報収集シート参照 画面をポップアップ起動する
  // Gui00663Logic.state.set({
  //   uniqueCpId: gui00663.value.uniqueCpId,
  //   state: {
  //     isOpen: true,
  //     screenMode: '1',
  //     mstKbn: 1 //(1:施設、2:居宅）
  //   },
  // })
}

/**
 * ボタン押下時処理
 *
 * @description
 * 自身のEventStatus領域のイベントフラグを更新する
 * GUI00664 情報収集シート取込 画面をポップアップ起動する
 */
function onClickInfoCollectionImport() {
  // TODO: GUI00664 情報収集シート取込 画面をポップアップ起動する
  // Gui00664Logic.state.set({
  //   uniqueCpId: gui00664.value.uniqueCpId,
  //   state: {
  //     isOpen: true,
  //     officeId: 1,
  //     userId: '',
  //     typeId: '',
  //     masterIndicator: '',
  //     info: ''
  //   },
  // })
}
const onClickHeaderModified = (
  title: string,
  smallCategoryCode: string,
  columnName: string,
  index: number,
  key: string
) => {
  selectRow(index)
  selectTextFiled.value = key
  // Or51775のダイアログを開く
  const params: Or51775StateType = {
    // タイトル
    title: title,
    // 大分類CD
    majorCategoryCode: '609',

    middleCategoryCode: '28', // 行数
    // 小分類CD
    smallCategoryCode: smallCategoryCode,
    // テーブル名
    tableName: 'cpn_tuc_myg_kky2',
    // カラム名
    columnName: columnName,
    // アセスメント方式
    assessmentMethod: '', //共通情報.アセスメント方式
    // 文章内容
    articleContent: '', // 画面項目.検討が必要な具体的状況
    // 利用者ID
    userId: local.userId, //共通情報.利用者ID
  }
  localOneway.or51775Oneway.title = params.title

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true, ...params }, // ダイアログを開くフラグ
  })
}
/**
 * テキスト設定処理
 *
 * @param data - Or51775ConfirmType型のデータ
 */
const handleSetText = (data: Or51775ConfirmType) => {
  const field = selectTextFiled.value as keyof IssuesConsiderItemType

  if (data.type === '1') {
    // typeが1の場合：新しい値を代入
    refValue.value.kadaiList[selectedItemIndex.value][field] = {
      value: data.value,
    }
  } else {
    // typeが1以外の場合：値を加算
    refValue.value.kadaiList[selectedItemIndex.value][field] = {
      value: refValue.value.kadaiList[selectedItemIndex.value][field].value + data.value,
    }
  }
}
</script>

<template>
  <c-v-form v-model="valid">
    <c-v-sheet class="view d-flex flex-column h-100">
      <!-- ローディング -->
      <v-overlay
        :model-value="isLoading"
        :persistent="false"
        class="align-center justify-center"
        ><v-progress-circular
          indeterminate
          color="primary"
        ></v-progress-circular
      ></v-overlay>
      <!-- 操作ボタンエリア -->
      <div class="action-sticky">
        <g-base-or11871
          v-if="!isCopyMode"
          v-bind="or11871"
        >
          <template #createItems>
            <c-v-list-item
              :title="t('btn.copy')"
              @click="copyBtnClick"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.care-plan2-copy-btn')"
              />
            </c-v-list-item>
          </template>
          <template #optionMenuItems>
            <c-v-list-item
              :title="
                t(
                  'label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-delete'
                )
              "
              prepend-icon="delete"
              @click="deleteClick"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.delete-data')"
              />
            </c-v-list-item>
          </template>
        </g-base-or11871>
      </div>
      <!-- アクションエリア -->
      <div class="action-content">
        <c-v-row
          no-gutters
          class="main-Content d-flex flex-0-1 h-100 overflow-y-auto position-relative"
        >
          <!-- ナビゲーションエリア -->
          <c-v-col
            v-if="!isCopyMode"
            cols="auto"
            class="hidden-scroll h-100 mt-1 ml-6 main-left"
          >
            <!-- 利用者選択一覧 -->
            <g-base-or00248
              v-bind="or00248"
              class="userList"
            />
          </c-v-col>
          <!-- コンテンツエリア -->
          <c-v-col class="main-right d-flex flex-column overflow-hidden h-100">
            <!-- 上段 -->
            <c-v-row
              v-if="!isCopyMode"
              no-gutters
              class="d-flex align-center"
            >
              <!-- 事業所 -->
              <c-v-col
                cols="auto mr-6 office-select"
                style="width: 220px"
              >
                <g-base-or41179 v-bind="or41179" />
              </c-v-col>
              <c-v-col
                v-if="planPeriodShow"
                cols="auto mr-6"
              >
                <!-- 計画対象期間 -->
                <g-custom-or13844
                  v-bind="or13844"
                  :oneway-model-value="localOneway.planningPeriodSelectOneway"
                  @open-btn-click="planningPeriodSelectClick"
                ></g-custom-or13844>
              </c-v-col>
              <!-- 基準日 -->
              <c-v-col
                v-if="baseDateShow"
                cols="auto mr-6"
              >
                <base-mo00020
                  class="baseDateShow"
                  :model-value="local.kijunbiYmd"
                  :oneway-model-value="localOneway.createDateOneway"
                />
              </c-v-col>
              <!-- 作成者 -->
              <c-v-col
                v-if="authorShow"
                cols="auto mr-6"
                style="width: 160px"
              >
                <g-custom-or-x-0157
                  v-model="local.orX0157"
                  :oneway-model-value="localOneway.authorSelectOneway"
                  @on-click-edit-btn="authorSelectClick"
                />
              </c-v-col>
              <!-- 履歴 -->
              <c-v-col
                v-if="historyShow"
                cols="auto"
              >
                <g-custom-or13872
                  v-bind="or13872"
                  :oneway-model-value="localOneway.hisotrySelectOneway"
                  @open-btn-click="historySelectClick"
                ></g-custom-or13872>
              </c-v-col>
            </c-v-row>
            <c-v-row
              no-gutters
              class="mt=-6"
            >
              <c-v-col
                v-if="!isCopyMode"
                class="mt-5"
              >
                <base-mo00609
                  :oneway-model-value="{
                    btnLabel: t('label.info-collection-reference'),
                  }"
                  class="collection-reference-bnt"
                  @click="onClickInformationCollectionSheet"
                />
                <base-mo00609
                  :oneway-model-value="{
                    btnLabel: t('label.info-collection-import'),
                  }"
                  class="ml-2 collection-reference-bnt"
                  @click="onClickInfoCollectionImport"
                />
              </c-v-col>
              <c-v-col
                cols="12"
                class="d-flex justify-end"
              >
                <div
                  v-if="!isCopyMode"
                  class="note-check"
                >
                  {{ t('label.assessment-check-note') }}
                </div>
              </c-v-col>
              <c-v-col
                cols="12"
                :class="['mt-1', isCopyMode ? 'mb-0' : 'mb-16', 'pr-1']"
              >
                <c-v-data-table
                  id="table-data"
                  :headers="headersForCopy"
                  :items="refValue?.kadaiList"
                  class="table-header table-gui653"
                  :items-per-page="-1"
                  fixed-header
                  hide-default-footer
                >
                  <template #headers="{ columns: rawCols }">
                    <tr style="z-index: 20 !important">
                      <!-- チェックボックス -->
                      <!-- COPY ADD -->
                      <th
                        v-if="isCopyMode"
                        rowspan="2"
                        class="table-checkbox"
                      >
                        <base-mo00018
                          v-model="local.mo00018"
                          :oneway-model-value="{
                            showItemLabel: false,
                            customClass: new CustomClass({ outerClass: '', labelClass: 'ma-1' }),
                            indeterminate: isIndeterminate,
                          }"
                          click.stop
                          style="width: 60px !important"
                          @update:model-value="allCheck"
                        ></base-mo00018>
                      </th>
                      <!-- COPY END -->
                      <th
                        v-for="(header, visIdx) in rawCols.filter(
                          (h: DataTableHeader) => h.key !== COPY_HDR_KEY
                        )"
                        :key="header.key"
                        :class="[
                          'table-th',
                          visIdx <= 2 ? `sticky-col sticky-col-${visIdx + 1}` : '',
                        ]"
                        :style="{ minWidth: header.minWidth, width: header.width }"
                      >
                        <span
                          :class="
                            header.key !== 'check' ? 'content-center-padding' : 'content-center'
                          "
                          >{{ header.title }}</span
                        >
                      </th>
                    </tr>
                  </template>
                  <template #item="{ item, index }">
                    <tr @click="selectRow(index)">
                      <!-- チェックボックス -->
                      <!-- COPY ADD -->
                      <td
                        v-if="isCopyMode"
                        class="table-checkbox"
                        :rowspan="item.details?.length ?? 1"
                        style="border-left: 1px solid rgb(var(--v-theme-black-200)) !important"
                      >
                        <base-mo00018
                          v-model="item.checked"
                          :oneway-model-value="{
                            showItemLabel: false,
                            customClass: new CustomClass({ outerClass: '', labelClass: 'ma-1' }),
                          }"
                          style="width: 60px !important"
                          @update:model-value="toggleSelectRow"
                          @click.stop
                        ></base-mo00018>
                      </td>
                      <!-- COPY END -->
                      <td
                        class="sticky-col sticky-col-1"
                        :rowspan="item.details?.length ?? 1"
                        :disabled="isCopyMode"
                      >
                        <span>{{ item.item }}</span>
                      </td>
                      <td class="sticky-col sticky-col-2">
                        <base-mo01338
                          :oneway-model-value="{
                            value: item.details[0]?.label,
                            customClass: new CustomClass({
                              outerClass: 'my-1 bg-main',
                            }),
                          }"
                        />
                      </td>
                      <td class="sticky-col sticky-col-3 dropdown-lts">
                        <base-mo01282
                          v-model="item.check[0]"
                          class="custom-dropdown"
                          :oneway-model-value="typeOneway"
                        />
                      </td>
                      <td
                        class="px-2"
                        :rowspan="item.details?.length ?? 1"
                      >
                        <g-custom-or-x-0163
                          v-model="item.joukyouKnj"
                          :oneway-model-value="{
                            readOnly: isCopyMode,
                            showEditBtnFlg: !isCopyMode,
                            maxlength: '4000',
                            rules: [byteLength(4000)],
                          }"
                          :disabled="isCopyMode"
                          @on-click-edit-btn="
                            onClickHeaderModified(
                              t('label.lblConsiderSpecificSituation'),
                              '1',
                              'joukyou_knj',
                              index,
                              'joukyouKnj'
                            )
                          "
                        ></g-custom-or-x-0163>
                      </td>
                      <td
                        class="px-2"
                        :rowspan="item.details?.length ?? 1"
                      >
                        <g-custom-or-x-0163
                          v-model="item.geninKnj"
                          :oneway-model-value="{
                            readOnly: isCopyMode,
                            showEditBtnFlg: !isCopyMode,
                            maxlength: '4000',
                            rules: [byteLength(4000)],
                          }"
                          :disabled="isCopyMode"
                          @on-click-edit-btn="
                            onClickHeaderModified(
                              t('label.lblCause'),
                              '2',
                              'genin_knj',
                              index,
                              'geninKnj'
                            )
                          "
                        ></g-custom-or-x-0163>
                      </td>
                      <td
                        class="px-2"
                        :rowspan="item.details?.length ?? 1"
                      >
                        <g-custom-or-x-0163
                          v-model="item.ikouKnj"
                          :oneway-model-value="{
                            readOnly: isCopyMode,
                            showEditBtnFlg: !isCopyMode,
                            maxlength: '4000',
                            rules: [byteLength(4000)],
                          }"
                          :disabled="isCopyMode"
                          @on-click-edit-btn="
                            onClickHeaderModified(
                              t('label.lblPersonFamilyIntention'),
                              '3',
                              'genin_knj',
                              index,
                              'ikouKnj'
                            )
                          "
                        ></g-custom-or-x-0163>
                      </td>
                      <td
                        class="px-2"
                        :rowspan="item.details?.length ?? 1"
                      >
                        <g-custom-or-x-0163
                          v-model="item.highlightCareStatus"
                          :oneway-model-value="{
                            readOnly: isCopyMode,
                            showEditBtnFlg: !isCopyMode,
                            maxlength: '4000',
                            rules: [byteLength(4000)],
                          }"
                          :disabled="isCopyMode"
                          @on-click-edit-btn="
                            onClickHeaderModified(
                              t('label.lblHighlightCareStatus'),
                              '4',
                              'genin_knj',
                              index,
                              'highlightCareStatus'
                            )
                          "
                        ></g-custom-or-x-0163>
                      </td>
                      <td
                        class="px-2"
                        :rowspan="item.details?.length ?? 1"
                      >
                        <g-custom-or-x-0163
                          v-model="item.kadaiKnj"
                          :oneway-model-value="{
                            readOnly: isCopyMode,
                            showEditBtnFlg: !isCopyMode,
                            maxlength: '4000',
                            rules: [byteLength(4000)],
                          }"
                          :disabled="isCopyMode"
                          @on-click-edit-btn="
                            onClickHeaderModified(
                              t('label.lblHighlightCareStatus'),
                              '4',
                              'genin_knj',
                              index,
                              'kadaiKnj'
                            )
                          "
                        ></g-custom-or-x-0163>
                      </td>
                      <td
                        class="px-2"
                        :rowspan="item.details?.length ?? 1"
                      >
                        <g-custom-or-x-0163
                          v-model="item.careKnj"
                          :oneway-model-value="{
                            readOnly: isCopyMode,
                            showEditBtnFlg: !isCopyMode,
                            maxlength: '4000',
                            rules: [byteLength(4000)],
                          }"
                          :disabled="isCopyMode"
                          @on-click-edit-btn="
                            onClickHeaderModified(
                              t('label.lblHighlightCareStatus'),
                              '4',
                              'genin_knj',
                              index,
                              'careKnj'
                            )
                          "
                        ></g-custom-or-x-0163>
                      </td>
                    </tr>
                    <tr
                      v-for="(i, idx) in item.details.slice(1)"
                      :key="idx"
                      :class="i.value"
                    >
                      <td
                        rowspan="1"
                        class="sticky-col sticky-col-2"
                        :style="
                          idx + 1 === item.details.slice(1).length
                            ? 'border-bottom: 1px solid #ccc !important;'
                            : ''
                        "
                        style="padding: 0px 12px"
                      >
                        <base-mo01338
                          :oneway-model-value="{
                            value: i.label,
                            customClass: new CustomClass({
                              outerClass: 'my-1 bg-main',
                            }),
                          }"
                        />
                      </td>
                      <td
                        rowspan="1"
                        :class="[i.value, idx === 0 ? 'first-item' : '']"
                        :style="
                          idx + 1 === item.details.slice(1).length
                            ? 'border-bottom: 1px solid #ccc !important;'
                            : ''
                        "
                        class="sticky-col sticky-col-3 dropdown-col dropdown-lts"
                      >
                        <base-mo01282
                          v-if="i.value !== 'disabled'"
                          v-model="item.check[idx + 1]"
                          class="custom-dropdown"
                          :oneway-model-value="typeOneway"
                        />
                      </td>
                    </tr>
                  </template> </c-v-data-table
              ></c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </div>
    </c-v-sheet>
    <!-- Or21813:有機体:エラーダイアログ -->
    <g-base-or21813 v-bind="or21813" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814" />

    <!-- GUI00627 アセスメント（インターライ）マスタ -->
    <!-- GUI00420 病名マスタ -->
    <!-- GUI00671 薬剤マスタ -->
    <!-- GUI02353 薬剤単位マスタ -->
    <g-custom-or-53417
      v-if="showDialogOr53417"
      v-bind="or53417"
      :oneway-model-value="localOneway.or53417Oneway" />

    <!-- GUI00788 ［アセスメント（インターライ）CSV出力］画面-->
    <g-custom-or-10279
      v-if="showDialogOr10279"
      v-bind="or10279"
      :oneway-model-value="localOneway.Or10279Oneway" />

    <!-- GUI00070 対象期間画面 -->
    <g-custom-or-x-0115
      v-if="showDialogOrX0115"
      v-bind="orx0115"
      :oneway-model-value="localOneway.orX0115Oneway"
    ></g-custom-or-x-0115>

    <!-- メッセージ「i-cmn-11260」 -->
    <!--  削除確認画面 -->
    <g-custom-or-x-0001
      v-if="showDialogOrX0001"
      v-bind="orX0001"
      v-model="orX0001Type"
      :oneway-model-value="orX0001Oneway"
    ></g-custom-or-x-0001>

    <!-- GUI00792 ［履歴選択］画面 -->
    <g-custom-or-10929
      v-if="showDialogOr10929"
      v-bind="or10929"
      v-model="or10929Type"
      :oneway-model-value="localOneway.or10929Oneway"
      @update:model-value="historySelectChange" />
    <!-- GUI00793 印刷設定画面 -->
    <g-custom-or-10269
      v-if="showDialogOr10269"
      v-bind="or10269" />
    <!-- GUI04512_アセスメント（インターライ）複写画面 -->
    <g-custom-or-x-0134
      v-if="showDialogOrX0134"
      v-bind="orX0134"
      v-model="orX0134Type"
      :oneway-model-value="localOneway.orX0134OnewayModel"
      @update:model-value="handleReturn" />
    <!-- GUI00220_職員検索画面 -->
    <g-custom-or-26257
      v-if="showDialogOr26257"
      v-bind="or26257"
      v-model="or26257Type"
      :oneway-model-value="localOneway.or26257OnewayModel" />
    <g-custom-or-29726
      v-if="showDialogOr29726 && !isCopyMode"
      v-bind="or29726_1"
      :oneway-model-value="localOneway.or29726Oneway"
      @copy-data="handleCopyData" />
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="handleSetText"
  /></c-v-form>
</template>

<style scoped lang="scss">
.view {
  background-color: transparent;
  height: max-content !important;
}

.main-Content {
  .main-left {
    max-width: 20%;

    .userList {
      :deep(.v-list) {
        max-height: 1019px !important;
      }
    }
  }

  .main-right {
    .middleContent {
      min-height: 0;

      .v-window {
        width: 100%;
      }
    }
  }
}

:deep(.rounded.item-label) {
  min-width: 180px;
  padding-left: 8px !important;
}
:deep(.office-select .v-sheet) {
  background-color: transparent !important;
  .v-input__control {
    background-color: #fff;
  }
}
.action-sticky {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
}
.action-content {
  background: rgb(var(--v-theme-background));
  height: max-content;
  z-index: 888;
}
.createDateOuterClass {
  width: auto !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }

  :deep(.v-input__details) {
    display: none;
  }
  :deep(.v-input__control) {
    background-color: #fff;
  }
}

.collection-reference-bnt {
  background-color: #fff !important;
  color: #767676 !important;
  border: 1px solid #767676;
  width: 106px;
  padding-left: 8px;
  padding-right: 8px;
}
.note-check {
  color: #333333;
  width: 1314px !important;
  font-weight: 700;
  font-size: 14px;
  display: flex;
  justify-content: end;
}
$font-size-root: 14px;
$spacing-root: 8px;
$border-size-root: 1px;
$color-key: rgb(var(--v-theme-blue-700));
.content-center-padding {
  display: flex;
  text-align: center;
  justify-content: center;
  padding-left: 16px;
  padding-right: 16px;
}
.content-center {
  display: flex;
  text-align: center;
  justify-content: center;
}
:deep(.table-header) {
  width: 1494px !important;
  table {
    table-layout: fixed;
    width: 1494px !important;

    th {
      background-color: #dbeefe !important;
      border: $border-size-root rgb(var(--v-theme-black-200)) solid !important;
      border-left: none !important;
      &:first-child {
        border-left: $border-size-root rgb(var(--v-theme-black-200)) solid !important;
      }
      &:nth-child(7) {
        font-size: 11px;
      }
      font-size: 12px;
      padding: 8px 4px !important;
      height: 56px !important;
      line-height: 100%;
      color: #333333;
      font-weight: 400;
      box-shadow: none;
      text-align: center;
    }
    td {
      border: $border-size-root rgb(var(--v-theme-black-200)) solid !important;
      &.disabled {
        border-bottom: none !important;
      }
      &.sticky-col-1:first-child {
        border-left: $border-size-root rgb(var(--v-theme-black-200)) solid !important;
      }
      &.dropdown-lts:not(.disabled) {
        padding: 0px 0px !important;
      }
      border-left: none !important;
      border-top: none !important;
      font-size: $font-size-root;
      table-layout: fixed;
      vertical-align: top;
    }
    tbody {
      tr {
        height: 40px !important;

        &:not(:last-child) {
          &.disabled {
            td {
              border-bottom: none !important;
              .full-width-field-select {
                display: none;
              }
            }
          }
        }
      }
    }
  }
}
:deep(.v-select .v-select__selection-text) {
  font-size: 12px !important;
}
.table-gui653 {
  $sticky-width-1: 101px;
  $sticky-width-2: 117px;
  $sticky-width-3: 76px;
  // Fixed Column
  .sticky-col {
    padding-left: 10px !important;
    padding-right: 10px !important;
    position: sticky;
    z-index: 1 !important;
  }
  .sticky-col-1 {
    left: 0;
    width: $sticky-width-1;
    background-color: #dbeefe !important;
    height: 320px;
    padding: 8px 10px;
    font-size: 12px;
  }
  .sticky-col-2 {
    left: $sticky-width-1;
    height: 40px !important;
    width: $sticky-width-2;
    background-color: #dbeefe !important;
  }
  .sticky-col-3 {
    left: calc(#{$sticky-width-1} + #{$sticky-width-2});
    background-color: #fff;
    height: 40px !important;
    width: $sticky-width-3;
  }
}
:deep(.sticky-col-2 .item-label) {
  // position: relative;
  font-size: 12px;
  font-weight: normal;
  // top: 5px;
}

:deep(.table-textarea-content) {
  padding: 8px !important;
}
:deep(.ga-2) {
  gap: 0px !important;
}
// :deep(
//   .baseDateShow .v-row.v-row--no-gutters > .v-col,
//   .v-row.v-row--no-gutters > [class*='v-col-']
// ) {
//   width: 163px;
// }
</style>
