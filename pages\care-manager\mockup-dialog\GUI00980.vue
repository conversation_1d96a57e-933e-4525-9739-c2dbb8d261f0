<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or32387Const } from '~/components/custom-components/organisms/Or32387/Or32387.constants'
import { Or32387Logic } from '~/components/custom-components/organisms/Or32387/Or32387.logic'
import type {
  Or32387OnewayType,
  Or32387Type,
  Week2List,
} from '~/types/cmn/business/components/Or32387Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00980'
// ルーティング
const routing = 'GUI00980/pinia'
// 画面物理名
const screenName = 'GUI00980'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or32387 = ref({ uniqueCpId: Or32387Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00980' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or32387Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or32387.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI00980',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or32387Const.CP_ID(0) }],
})
Or32387Logic.initialize(init.childCpIds.Or32387.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or32387Const.CP_ID(0)]: or32387.value,
})

// ダイアログ表示フラグ
const showDialogOr32387 = computed(() => {
  // Or32387のダイアログ開閉状態
  return Or32387Logic.state.get(or32387.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const localOneway = reactive({
  or32387Oneway: {
    svJigyoId: '',
    shokuinId: '',
    shisetsuId: '',
    mstKbn: '2',
    kaiteiFlg: '1',
    keiyakushaId: '1',
    local: '1',
    week1Id: '1',
    assessmentType: '1',
    userId: '1',
    termid: '1',
    weeklyModel: '1',
    processYm: '2025/05',
    week2List: [
      {
        week2Id: '1',
        week1Id: '1',
        youbi: '111010',
        kaishiJikan: '06:00',
        shuuryouJikan: '12:00',
        naiyoCd: '1',
        naiyoKnj: '1',
        memoKnj: '右手指一部麻痺のた',
        fontSize: '12',
        dispMode: '0',
        alignment: '1',
        svShuruiCd: '1',
        svItemCd: '1',
        svJigyoId: '1',
        svShuruiKnj: '訪問看護',
        svItemKnj: '',
        svJigyoKnj: '1',
        svJigyoRks: '短期入所療養介護（介護老人保健施設）',
        fontColor: '8388736',
        backColor: '16753920',
        timeKbn: '1',
        igaiMoji: '1日~3日·5日~7日·\r\n9日~11日·13日~15日',
        igaiKbn: '1',
        igaiDate: '1',
        igaiWeek: '1',
        week3List: [
          {
            id: '1',
            svJigyoId: '1',
            svItemCd: '1',
            kaisuu: '1',
            svTani: '1',
            fygId: '1',
          },
        ],
      },
    ],
    timeDisplay: '1',
  } as Or32387OnewayType,
  mo00045OneWay: {
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo00045OnewayType,
})

// ローカル双方向bind
const local = reactive({
  or32387: {
    // 週間表ID
    week1Id: '',
    // 有効期間ID
    termid: '',
  } as Or32387Type,
  svJigyoId: {
    value: '',
  },
  shokuinId: {
    value: '',
  },
  shisetsuId: {
    value: '',
  },
  mstKbn: {
    value: '',
  },
  kaiteiFlg: {
    value: '',
  },
  keiyakushaId: {
    value: '',
  },
  local: {
    value: '',
  },
  week1Id: {
    value: '',
  },
  assessmentType: {
    value: '',
  },
  userId: {
    value: '',
  },
  termid: {
    value: '',
  },
  weeklyModel: {
    value: '',
  },
  processYm: {
    value: '',
  },
  week2List: {
    value: '',
  },
  timeDisplay: {
    value: '',
  },
})

/***
 * ボタン押下時の処理
 *
 */
function or32387OnClick() {
  Or32387Logic.state.set({
    uniqueCpId: or32387.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function onClickOr32387Test() {
  localOneway.or32387Oneway = {
    svJigyoId: local.svJigyoId.value,
    shokuinId: local.shokuinId.value,
    shisetsuId: local.shisetsuId.value,
    mstKbn: local.mstKbn.value,
    kaiteiFlg: local.kaiteiFlg.value,
    keiyakushaId: local.keiyakushaId.value,
    local: local.local.value,
    week1Id: local.week1Id.value,
    assessmentType: local.assessmentType.value,
    userId: local.userId.value,
    termid: local.termid.value,
    weeklyModel: local.weeklyModel.value,
    processYm: local.processYm.value,
    week2List: JSON.parse(local.week2List.value) as Week2List[],
    timeDisplay: local.timeDisplay.value,
  } as Or32387OnewayType
  Or32387Logic.state.set({
    uniqueCpId: or32387.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or32387OnClick()"
        >週間表パターン（設定）
      </v-btn>
      <g-custom-or-32387
        v-if="showDialogOr32387"
        v-bind="or32387"
        v-model="local.or32387"
        :oneway-model-value="localOneway.or32387Oneway"
        :parent-unique-cp-id="pageComponent.uniqueCpId"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">事業所ＩＤ</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.svJigyoId"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">職員ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.shokuinId"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">施設ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.shisetsuId"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">マスタ区分</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.mstKbn"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">改訂区分</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.kaiteiFlg"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">契約者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.keiyakushaId"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">言語</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.local"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">週間表ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.week1Id"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">アセスメント方式</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.assessmentType"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">有効期間ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.termid"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">週単位以外サービス</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.weeklyModel"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">処理年月</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.processYm"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">詳細リスト</div>
    sample: [{"week2Id": "1", "week1Id": "1", "youbi": "111010", "kaishiJikan": "06:00","shuuryouJikan": "12:00", "naiyoCd": "1", "naiyoKnj": "1", "memoKnj": "右手指一部麻痺のた","fontSize": "12", "dispMode": "0", "alignment": "1", "svShuruiCd": "1", "svItemCd": "1","svJigyoId": "1", "svShuruiKnj": "訪問看護", "svItemKnj": "", "svJigyoKnj": "1", "svJigyoRks":"短期入所療養介護（介護老人保健施設）", "fontColor": "8388736", "backColor": "16753920","timeKbn": "1", "igaiMoji": "1日~3日·5日~7日·\r\n9日~11日·13日~15日", "igaiKbn": "1", "igaiDate":"1", "igaiWeek": "1", "week3List": [ { "id": "1", "svJigyoId": "1", "svItemCd": "1", "kaisuu":"1", "svTani": "1", "fygId": "1" }] }]
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.week2List"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">時間表示フラグ</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.timeDisplay"
      :oneway-model-value="localOneway.mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr32387Test"> GUI00980 疎通起動 </v-btn>
  </div>
  <div class="pt-5 w-25 pl-5">疎通Result: {{ local.or32387 }}</div>
</template>
