/**
 * Or27754:コンポーネント
 * GUI00653: ［課題検討］画面
 *
 * @description
 * ［課題検討］画面
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { IssueConsiderationInputType } from '~/repositories/cmn/entities/IssueConsiderationItemHeaderEntity'

/**
 * GUI00653_［課題検討］画面
 *
 * @description
 * dataName："issueConsiderationItem"
 */
export function handler(inEntity: IssueConsiderationInputType) {
  const returnData = inEntity.event === '2' ? defaultData[0] : defaultData[1]
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...returnData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
