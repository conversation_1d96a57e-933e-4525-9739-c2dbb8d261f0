import type {
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
  SubInfoBEntity
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'

/**
 * パラメータエンティティ
 */
export interface TransmitParam {
  /**
   * 実行フラグ(save：保存,add：新規,copy：複写,delete：削除,getData：データ再取得,hidden：入力フォームを非表示,''：何もしません)
   */
  executeFlag: 'save' | 'add' | 'copy' | 'delete' | 'getData' | 'hidden' | ''
  /**
   * 削除ボタンの選択値
   */
  deleteBtnValue: string
  /**
   * 期間管理フラグ
   */
  kikanKanriFlg: string
  /**
   * 基準日
   */
  kijunbiYmd: string
  /**
   * 作成者ID
   */
  sakuseiId: string
  /**
   * 履歴情報
   */
  historyInfo: HistoryInfoEntity
  /**
   * 計画期間情報
   */
  planPeriodInfo: PlanPeriodInfoEntity
  /**
   * アセスメントID
   */
  raiId: string
  /**
   * 調査アセスメント種別
   */
  assType: string
  /**
   * 要介護度
   */
  yokaiKbn: string
  /**
   * サブ情報（B）
   */
  subInfoB: SubInfoBEntity
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 事業者名
   */
  svJigyoKnj: string
  /**
   * 更新区分("":更新なし／"C":新規／"U":更新／"D":削除)
   */
  updateKbn: string
  /**
   * 履歴更新区分("":更新なし／"C":新規／"U":更新／"D":削除)
   */
  historyUpdateKbn: string
  /**
   * 親画面.選定表・検討表作成区分
   */
  tableCreateKbn: string,
  /**
   * 事業者Id
   */
  svJigyoId: string
  /**
   * 利用者Id
   */
  userId: string
}
