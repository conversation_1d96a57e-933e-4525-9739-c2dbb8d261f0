<script setup lang="ts">
/**
 * Or00734:有機体:日課表マスタパターン設定モダール
 * GUI00987_日課表マスタパターン設定
 *
 * @description
 * 日課表マスタパターン設定
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or17375Logic } from '../Or17375/Or17375.logic'
import { Or17375Const } from '../Or17375/Or17375.constants'
import { Or17377Logic } from '../Or17377/Or17377.logic'
import { Or17377Const } from '../Or17377/Or17377.constants'
import { OrX0006Const } from '../OrX0006/OrX0006.constants'
import { Or00725Const } from '../Or00725/Or00725.constants'
import { Or00734Const } from './Or00734.constants'
import { hasRegistAuth, useScreenStore, useSetupChildProps } from '#build/imports'
import { useGyoumuCom } from '~/utils/useGyoumuCom'
import type { Or00734OnewayType } from '~/types/cmn/business/components/Or00734Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { OrX0006OnewayType } from '~/types/cmn/business/components/OrX0006Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useScreenUtils } from '~/utils/useScreenUtils'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  DailyTaskTableMasterInitSelectInEntity,
  DailyTaskTableMasterInitSelectOutEntity,
} from '~/repositories/cmn/entities/DailyTaskTableMasterInitSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  DailyTaskTableMasterUpdateInEntity,
} from '~/repositories/cmn/entities/DailyTaskTableMasterUpdateEntity'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Or21814OnewayType, Or21814EventType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Or21815StateType, Or21815EventType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or00734OnewayType
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
/**
 * ユーティリティを提供するフック
 */
const gyoumuCom = useGyoumuCom()
const local = reactive({
  mo00040 : {} as Mo00040Type
})
// ローカルOneway
const localOneway = reactive({
  or00734OneWay: {
    ...props.onewayModelValue,
  },
  or17375: {
    memo: '1',
    memoBunrui3: '',
    memoModifiedCnt: '',
  },
  or17375OneWay: { radioItemsList: [] as CodeType[] },
  or17377: {
    time: '1',
    timeInitialBunrui3: '',
    timeInitialModifiedCnt: '',
  },
  or17377OneWay: { radioItemsList: [] as CodeType[] },
  // 事業所入力
  orX0006Oneway: {} as OrX0006OnewayType,
  mo01338Oneway: {
    value: t('label.memo'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Twoway: {
    value: t('label.time-initial'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo00040Oneway: {
    showItemLabel: false,
    itemLabel: t('label.office-selection'),
    itemTitle: 'jigyoKnj',
    itemValue: 'jigyoId',
    width: '180px',
    items: [],
  } as Mo00040OnewayType,
})
const or17375_1 = ref({ uniqueCpId: Or17375Const.CP_ID(1) })
const or17377_1 = ref({ uniqueCpId: Or17377Const.CP_ID(1) })
const orX0006_1 = ref({ uniqueCpId: OrX0006Const.CP_ID(1) })
const or21814_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0006Const.CP_ID(1)]: orX0006_1.value,
  [Or17375Const.CP_ID(1)]: or17375_1.value,
  [Or17377Const.CP_ID(1)]: or17377_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
})

onMounted(async () => {
  await initCodes()
  await init()
  // データ変更確認ダイアログを初期化(i.cmn.10430)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
})

/**
 * 初期情報取得
 */
async function init() {
  // 日課表マスタ初期情報取得(IN)
  const inputData: DailyTaskTableMasterInitSelectInEntity = {
    shisetuId: localOneway.or00734OneWay.shisetuId,
    svJigyoId: localOneway.or00734OneWay.svJigyoId,
    svJigyoIdList: localOneway.or00734OneWay.svJigyoIdList,
  }
  const ret: DailyTaskTableMasterInitSelectOutEntity = await ScreenRepository.select(
    'dailyTaskTableMasterInitSelect',
    inputData
  )
  localOneway.or17375.memo = ret.data.memo || '1'
  localOneway.or17375.memoBunrui3 = ret.data.memoBunrui3
  localOneway.or17375.memoModifiedCnt = ret.data.memoModifiedCnt
  localOneway.or17377.time = ret.data.timeInitial || '0'
  localOneway.or17377.timeInitialBunrui3 = ret.data.timeInitialBunrui3
  localOneway.or17377.timeInitialModifiedCnt = ret.data.timeInitialModifiedCnt
  // 事業所
  if (ret.data.svJigyoInfoList) {
    localOneway.mo00040Oneway.items = ret.data.svJigyoInfoList
    local.mo00040.modelValue = localOneway.or00734OneWay.svJigyoId
  }

  setChildCpBinds(props.uniqueCpId, {
    [Or17375Const.CP_ID(1)]: {
      twoWayValue: {
        memo: localOneway.or17375.memo,
      },
    },
    [Or17377Const.CP_ID(1)]: {
      twoWayValue: {
        time: localOneway.or17377.time,
      },
    },
  })
}
/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // メモ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEMO },
    // 時間（初期値）
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TIME },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // コード取得
  // メモ
  localOneway.or17375OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MEMO
  )
  // 時間（初期値）
  localOneway.or17377OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TIME
  )
}

/**************************************************
 * 関数
 **************************************************/
/**
 * 事業所を監視
 *
 * @description
 */
watch(
  () => local.mo00040,
  async (newValue) => {
    if (newValue?.modelValue && newValue?.modelValue !== localOneway.or00734OneWay.svJigyoId) {
      if (!(await gyoumuCom.checkEdit(
        isEdit.value,
        await hasRegistAuth(Or00734Const.LINK_AUTH),
        showConfirmMessageBox,
        showWarnMessageBox,
        insert,
        caceljigyoId
      ))) {
        return;
      }
      localOneway.or00734OneWay.svJigyoId = newValue.modelValue
      await init()
    }
  }
)

function caceljigyoId() {
  local.mo00040.modelValue = localOneway.or00734OneWay.svJigyoId
}
/**
 * 保存
 */
async function save() {
  //変更されている項目がないため、保存を行うことは出来ません。
  // 項目を入力変更してから、再度保存を行ってください。
  if (!await gyoumuCom.checkEditBySave(isEdit.value, showMessageBox)) {
    return
  }
  await insert();
}

/**
 * 保存
 */
async function insert() {
  const param: DailyTaskTableMasterUpdateInEntity = {
    shisetuId: localOneway.or00734OneWay.shisetuId,
    svJigyoId: localOneway.or00734OneWay.svJigyoId,
    bunrui1Id: '2',
    bunrui2Id: '18',
    bunrui3SetList: [],
  }
  if (!localOneway.or17375.memoBunrui3 ||localOneway.or17375.memoBunrui3 === ''
    || Or17375Logic.data.get(or17375_1.value.uniqueCpId)!.memo !== localOneway.or17375.memo) {
    param.bunrui3SetList.push({
      bunrui3Id: localOneway.or17375.memoBunrui3 || Or00734Const.UPDATE_BUNRUI3ID_MEMO,
      bunrui3Value: Or17375Logic.data.get(or17375_1.value.uniqueCpId)!.memo,
      modifiedCnt: localOneway.or17375.memoModifiedCnt,
    })
  }
  if (!localOneway.or17377.timeInitialBunrui3 || localOneway.or17377.timeInitialBunrui3 === ''
    || Or17377Logic.data.get(or17377_1.value.uniqueCpId)!.time !== localOneway.or17377.time) {
    param.bunrui3SetList.push({
      bunrui3Id: localOneway.or17377.timeInitialBunrui3 || Or00734Const.UPDATE_BUNRUI3ID_TIME,
      bunrui3Value: Or17377Logic.data.get(or17377_1.value.uniqueCpId)!.time,
      modifiedCnt: localOneway.or17377.timeInitialModifiedCnt,
    })
  }
  await ScreenRepository.update(
    'dailyTaskTableMasterUpdate',
    param
  )
  await init();
}

/**
 * メッセージを表示する
 */
const showConfirmMessageBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * 確認ダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}
/**
 * メッセージを表示する
 */
const showWarnMessageBox = async () => {
  return await openWarnDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.w-com-10006'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
}

/**
 * メッセージを表示する
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openWarnDialog(state: Or21815StateType): Promise<Or21815EventType | undefined> {
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * メッセージを表示する
 *
 * @param messageId - メッセージ
 */
const showMessageBox = async (messageId: string) => {
  await openInfoDialog({
    dialogText: t(messageId),
    dialogTitle: t('label.top-btn-title'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
}

/**
 * EditFlag -> get
 *
 */
function _getGetEditFlgFlag() {
  return isEdit.value
}

/**
 * EditFlag初期化処理（isInit: true）
 *
 */
function _cleanEditFlag() {
  useScreenStore().setCpNavControl({
    cpId: Or00725Const.CP_ID(1),
    uniqueCpId: props.uniqueCpId,
    editFlg: false,
  })
}

defineExpose({
  save,
  _cleanEditFlag,
  _getGetEditFlgFlag,
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})
</script>
<template>
  <c-v-sheet class="view">
    <c-v-row class="operationArea">
      <c-v-col
        cols="auto"
        class="ma-1 pt-1 mr-4"
      >
        <base-at-label :value="t('label.office-selection')" />
      </c-v-col>
      <c-v-col>
        <base-mo00040
          v-model="local.mo00040"
          :oneway-model-value="localOneway.mo00040Oneway"
          v-bind="{ ...$attrs }"
        />
      </c-v-col>
    </c-v-row>
    <c-v-divider class="mb-2" />
    <c-v-row class="subSection">
      <c-v-col class="sectionHeader">
        <base-mo01338
          :oneway-model-value="localOneway.mo01338Oneway"
          style="background-color: rgb(var(--v-theme-background)) !important"
        />
      </c-v-col>
      <c-v-col class="sectionContent">
        <!-- （日課表マスタ）メモ選択 -->
        <g-custom-or17375
          v-bind="or17375_1"
          :oneway-model-value="localOneway.or17375OneWay"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row class="subSection">
      <c-v-col class="sectionHeader">
        <base-mo01338
          :oneway-model-value="localOneway.mo01338Twoway"
          style="background-color: rgb(var(--v-theme-background)) !important"
        />
      </c-v-col>
      <c-v-col class="sectionContent">
        <!-- （日課表マスタ）時間（初期値）-->
        <g-custom-or17377
          v-bind="or17377_1"
          :oneway-model-value="localOneway.or17377OneWay"
        />
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <g-base-or21814 v-bind="or21814_1" />
  <g-base-or21815 v-bind="or21815_1" />
</template>
<style scoped>
.view {
  min-height: 90px;
  min-width: 400px;
  display: flex;
  margin: 0px;
  flex-direction: column;

  .v-row {
    margin: unset;
    margin-left: 8px;
    margin-right: 8px;
  }

  .operationArea {
    flex: 0 0 auto;
    height: 55px;

    .v-col {
      padding-left: 0;
      padding-right: 0;
      padding-top: 0;
      padding-bottom: 8px;
    }

    .v-col:last-child {
      text-align: right;
    }
  }

  .subSection {
    border: solid thin rgb(var(--v-theme-light));
    .sectionHeader {
      display: flex;
      flex-direction: column;
      min-width: 200px;
      max-width: 200px;
      justify-content: center;
      align-items: left;
      padding: 0;
      --v-theme-overlay-multiplier: var(--v-theme-background-overlay-multiplier);
      background-color: rgb(var(--v-theme-background)) !important;
      color: rgb(var(--v-theme-on-background)) !important;
      font-weight: bold;

      span {
        width: 100%;
        font-size: 14px;
      }

      small {
        width: 100%;
        padding-left: 8px;
        font-weight: normal;
      }
    }
  }

  .sectionContent {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 8px !important;

    .v-sheet {
      width: 100%;

      :deep(.radio-group) {
        width: 100%;

        .v-col-auto {
          width: 100%;
        }
      }
    }
  }
}
.copyright-text {
  color: rgb(var(--v-theme-text));
  white-space: nowrap;
  font-weight: normal;
}
</style>
