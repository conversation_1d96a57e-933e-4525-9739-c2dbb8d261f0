import { Or26866Const } from './Or26866.constants'
import type { Or26866StateType } from './Or26866.Type'
import { OrX0038Const } from '~/components/custom-components/organisms/OrX0038/OrX0038.constants'
import { OrX0038Logic } from '~/components/custom-components/organisms/OrX0038/OrX0038.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or26323Const } from '~/components/custom-components/organisms/Or26323/Or26323.constants'
import { Or26323Logic } from '~/components/custom-components/organisms/Or26323/Or26323.logic'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or26866:処理ロジック
 * GUI01273_認定調査 特記事項選択
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR>
 */
export namespace Or26866Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or26866Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or21815Const.CP_ID(1) },
        { cpId: OrX0038Const.CP_ID(1) },
        { cpId: Or26323Const.CP_ID(1) },
      ],
    })
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(1)].uniqueCpId)
    Or26323Logic.initialize(childCpIds[Or26323Const.CP_ID(1)].uniqueCpId)
    OrX0038Logic.initialize(childCpIds[OrX0038Const.CP_ID(1)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or26866StateType>(Or26866Const.CP_ID(0))
}
