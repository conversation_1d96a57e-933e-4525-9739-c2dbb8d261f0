<script setup lang="ts">
/**
 * GuiStaffList:有機体:職員管理画面（画面コンポーネント）
 *
 * @description
 */
import { onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { Or41363Const } from '../Or41363/Or41363.constants'
import { Or41363Logic } from '../Or41363/Or41363.logic'

import { Or41199Const } from '../Or41199/Or41199.constants'

import type { Or41363ItemType } from '../Or41363/Or41363.type'

import type {
  IGuiStaffListSelectInEntity,
  IGuiStaffListSelectOutEntity,
} from './GuiStaffListSelectEntity'
import { useCommonProps } from '~/composables/useCommonProps'

import { useSetupChildProps } from '~/composables/useComponentVue'
import { useScreenUtils } from '~/utils/useScreenUtils'

import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00045Type, Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00040Type, Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

const { setChildCpBinds } = useScreenUtils()

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())

/**************************************************
 * 変数定義
 **************************************************/
const or41363 = ref({ uniqueCpId: '' })
const or41199 = ref({ uniqueCpId: '' })

// 職員番号 テキストフィールド
const mo00045StaffNumber = ref<Mo00045Type>({
  value: '',
})
const mo00045OnewayStaffNumber = ref<Mo00045OnewayType>({
  name: 'StaffNumber',
  itemLabel: t('label.staff-number'),
  hideDetails: true,
  showItemLabel: true,
  width: '200px',
  maxLength: '50',
})

// 職員名 テキストフィールド
const mo00045StaffName = ref<Mo00045Type>({
  value: '',
})
const mo00045OnewayStaffName = ref<Mo00045OnewayType>({
  name: 'StaffName',
  itemLabel: t('label.staff-name'),
  hideDetails: true,
  showItemLabel: true,
  width: '250px',
  maxLength: '50',
})

// ログインID テキストフィールド
const mo00045LoginId = ref<Mo00045Type>({
  value: '',
})
const mo00045OnewayLoginId = ref<Mo00045OnewayType>({
  name: 'LoginId',
  itemLabel: t('label.login-id'),
  hideDetails: true,
  showItemLabel: true,
  width: '200px',
  maxLength: '50',
})

// 勤務形態 プルダウン
const mo000040WorkingStyle = ref<Mo00040Type>({
  modelValue: '',
})
const mo00040OnewayWorkingStyle = ref<Mo00040OnewayType>({
  itemLabel: t('label.working-style'),
  showItemLabel: true,
  isRequired: false,
  hideDetails: true,
  width: '180px',
  items: [
    { value: '', title: '' },
    { value: '0', title: t('label.full-time') }, // 常勤
    { value: '1', title: t('label.part-time') }, // 非常勤
  ] as { value: string; title: string }[],
})

// 権限 プルダウン
const mo000040Permission = ref<Mo00040Type>({
  modelValue: '',
})
const mo00040OnewayPermission = ref<Mo00040OnewayType>({
  itemLabel: t('label.permission'),
  showItemLabel: true,
  isRequired: false,
  hideDetails: true,
  width: '180px',
  items: [
    { value: '', title: '' },
    { value: '0', title: t('label.general') }, // 一般
    { value: '1', title: t('label.administrator') }, // 管理者
  ] as { value: string; title: string }[],
})

// アカウント プルダウン
const mo000040Account = ref<Mo00040Type>({
  modelValue: '',
})
const mo00040OnewayAccount = ref<Mo00040OnewayType>({
  itemLabel: t('label.account'),
  showItemLabel: true,
  isRequired: false,
  hideDetails: true,
  width: '180px',
  items: [
    { value: '', title: '' },
    { value: '0', title: t('label.invalid') }, // 無効
    { value: '1', title: t('label.valid') }, // 有効
  ] as { value: string; title: string }[],
})

// 検索ボタン
const mo00609Oneway = ref<Mo00609OnewayType>({
  btnLabel: t('btn.search'),
  width: '80px',
  prependIcon: 'search',
  disabled: false,
})

// 一覧の外側のdivの可視性
const tableContainerDivVisibility = ref('hidden') // 画面遷移時は非表示

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or41363Const.CP_ID(1)]: or41363.value,
  [Or41199Const.CP_ID(1)]: or41199.value,
})

// ▼▼▼ 検索ボタン押下時の処理 -----------------------------------

/**
 * 検索ボタンクリック
 */
async function clickSearchBtn() {
  mo00609Oneway.value.disabled = true // 検索ボタンを非活性

  // 一覧を表示
  tableContainerDivVisibility.value = 'visible'

  // ページ（何ページ目を表示させるか）を初期化
  Or41363Logic.state.set({
    uniqueCpId: or41363.value.uniqueCpId,
    state: { page: 1 },
  })

  // 職員管理データ取得
  await getStafflist()

  mo00609Oneway.value.disabled = false // 検索ボタンを活性化
}

// ▲▲▲ 検索ボタン押下時の処理 -----------------------------------

// ▼▼▼ データ取得用の処理 -----------------------------------
/**
 * 職員管理データ取得関数
 */
async function getStafflist() {
  // テーブル表示をロード中にする
  Or41363Logic.state.set({
    uniqueCpId: or41363.value.uniqueCpId,
    state: {
      loading: true, // ロード中
    },
  })

  const workPage = ref(1) // ページ（何ページ目を表示させるか）
  const piniaPage = Or41363Logic.state.get(or41363.value.uniqueCpId)?.page

  if (typeof piniaPage === 'number') {
    workPage.value = piniaPage
  }

  const workLimit = ref(20) // 1ページ当たりの表示件数
  const piniaLimit = Or41363Logic.state.get(or41363.value.uniqueCpId)?.limit

  if (piniaLimit !== undefined) {
    // 文字列が数値に変換できる場合
    if (/^\d+$/.test(piniaLimit)) {
      workLimit.value = Number(piniaLimit)
    }
  }

  let workWorkingStyle = '' // 勤務形態の選択値
  if (mo000040WorkingStyle.value.modelValue !== undefined) {
    workWorkingStyle = mo000040WorkingStyle.value.modelValue
  }

  let workPermission = '' // 権限の選択値
  if (mo000040Permission.value.modelValue !== undefined) {
    workPermission = mo000040Permission.value.modelValue
  }

  let workAccount = '' // アカウントの選択値
  if (mo000040Account.value.modelValue !== undefined) {
    workAccount = mo000040Account.value.modelValue
  }

  const payload: IGuiStaffListSelectInEntity = {
    staffNumber: mo00045StaffNumber.value.value, // 職員番号
    staffName: mo00045StaffName.value.value, // 職員名
    loginId: mo00045LoginId.value.value, // ログインID
    workingStyle: workWorkingStyle, // 勤務形態
    permission: workPermission, // 権限
    account: workAccount, // アカウント
    limit: workLimit.value, // リミット
    offset: (workPage.value - 1) * workLimit.value, // オフセット
  }

  const res: IGuiStaffListSelectOutEntity = await ScreenRepository.select('staff_list', payload)
  if (res) {
    console.log('ためしGuiStaffList.vue res→' + JSON.stringify(res))

    const workLength = ref(0)
    if (res.data.list !== undefined) {
      workLength.value = res.data.list.length
    }

    // 取得データが1件以上
    if (res.data.list !== undefined && workLength.value > 0) {
      const workTotal = ref(0)
      if (res.data.total !== undefined) {
        workTotal.value = res.data.total
      }

      const workSeq = ref(0)

      setChildCpBinds(props.uniqueCpId, {
        Or41363_1: {
          oneWayState: {
            items: res.data.list.map((data) => {
              workSeq.value++
              const strSeq = workSeq.value.toString()
              return {
                // ID（分子Mo01334を使用するために必要）
                id: strSeq,

                // 職員番号
                mo01337OnewayStaffNumber: {
                  value: data.staffNumber,
                  unit: '',
                },

                // 職員画像
                staffImage: data.staffImage,

                // 職員名
                mo01337OnewayStaffName: {
                  value: data.staffName,
                  unit: '',
                },

                // ログインID
                mo01337OnewayLoginId: {
                  value: data.loginId,
                  unit: '',
                },

                // 職員ID
                staffId: data.staffId,

                // 勤務形態（区分値）
                workingStyle: data.workingStyle,

                // 勤務形態（ラベル）
                mo01337OnewayWorkingStyleLabel: {
                  value: convertPulldownValueToTitle(
                    data.workingStyle,
                    mo00040OnewayWorkingStyle.value.items
                  ),
                  unit: '',
                },

                // 権限（区分値）
                permission: data.permission,

                // 権限（ラベル）
                mo01337OnewayPermissionLabel: {
                  value: convertPulldownValueToTitle(
                    data.permission,
                    mo00040OnewayPermission.value.items
                  ),
                  unit: '',
                },

                // アカウント（区分値）
                account: data.account,

                // アカウント（ラベル）
                mo01337OnewayAccountLabel: {
                  value: convertPulldownValueToTitle(
                    data.account,
                    mo00040OnewayAccount.value.items
                  ),
                  unit: '',
                },

                // 登録日
                mo01337OnewayCreated: {
                  value: data.created,
                  unit: '',
                },

                selectable: false, // 一覧の行選択を不可
              } as Or41363ItemType
            }),

            itemsLength: workTotal.value,
            loading: false, // ロード中を解除
          },
        },
      })
    } else {
      // 取得データが0件の場合、空のデータを設定
      setChildCpBinds(props.uniqueCpId, {
        Or41363_1: {
          oneWayState: {
            items: [],
            itemsLength: 0,
            loading: false, // ロード中を解除
          },
        },
      })
    }
  }
}

// ▲▲▲ データ取得用の処理 -----------------------------------

/**
 * プルダウンの選択肢のvalueを指定してそのtitleを取得します
 *
 * @param inputValue - valueの値
 *
 * @param items - プルダウンの選択肢
 */
function convertPulldownValueToTitle(
  inputValue: number,
  items: { value: string; title: string }[] | undefined
) {
  let workResult = ''
  const strValue = String(inputValue)

  if (items !== undefined) {
    if (items.length > 0) {
      items.forEach((item) => {
        if (strValue === item.value) {
          workResult = item.title
        }
      })
    }
  }

  return workResult
}

// ロード中フラグの監視
watch(
  () => Or41363Logic.state.get(or41363.value.uniqueCpId)?.loading,
  async (newValue) => {
    if (newValue !== undefined) {
      if (newValue === true) {
        // データ取得
        await getStafflist()
      }
    }
  }
)

// 一覧でlimit又はpageを変更時にデータ取得
watch(
  [
    () => Or41363Logic.state.get(or41363.value.uniqueCpId)?.page,
    () => Or41363Logic.state.get(or41363.value.uniqueCpId)?.limit,
  ],
  async () => {
    // データ取得
    await getStafflist()
  }
)
</script>

<template>
  <c-v-row
    no-gutters
    style="display: none"
  >
    <c-v-col>
      <h1 class="page-title">{{ t('label.staff-management') }}</h1>
    </c-v-col>
  </c-v-row>

  <c-v-row no-gutters>
    <c-v-row
      no-gutters
      class="w-100"
    >
      <!-- ↓拡張パネル start -->
      <g-custom-or-41199 v-bind="or41199">
        <template #panelText>
          <c-v-row
            no-gutters
            class="ma-4 mt-0 mb-0 flex-wrap align-top ga-4"
          >
            <!-- 職員番号 テキストフィールド -->
            <base-mo-00045
              v-model="mo00045StaffNumber"
              :oneway-model-value="mo00045OnewayStaffNumber"
            />

            <!-- 職員名 テキストフィールド -->
            <base-mo-00045
              v-model="mo00045StaffName"
              :oneway-model-value="mo00045OnewayStaffName"
            />

            <!-- ログインID テキストフィールド -->
            <base-mo-00045
              v-model="mo00045LoginId"
              :oneway-model-value="mo00045OnewayLoginId"
            />

            <!-- 勤務形態 プルダウン -->
            <base-mo00040
              v-model="mo000040WorkingStyle"
              :oneway-model-value="mo00040OnewayWorkingStyle"
            />

            <!-- 権限 プルダウン -->
            <base-mo00040
              v-model="mo000040Permission"
              :oneway-model-value="mo00040OnewayPermission"
            />

            <!-- アカウント プルダウン -->
            <base-mo00040
              v-model="mo000040Account"
              :oneway-model-value="mo00040OnewayAccount"
            />

            <!-- 検索ボタン -->
            <base-mo00609
              :oneway-model-value="mo00609Oneway"
              class="ml-auto mt-auto"
              @click="clickSearchBtn()"
            />
          </c-v-row>
        </template>
      </g-custom-or-41199>
      <!-- ↑拡張パネル end -->
    </c-v-row>
  </c-v-row>

  <!-- ↓一覧 start -->
  <div class="table-container-div pa-0 ma-0">
    <g-custom-or-41363 v-bind="or41363" />
  </div>
  <!-- ↑一覧 end -->
</template>

<style scoped lang="scss">
.page-title {
  margin: 8px;
}

.sheet-style {
  margin: 8px;
  background-color: rgb(var(--v-theme-background));
}

// 一覧の外側のdiv
.table-container-div {
  visibility: v-bind('tableContainerDivVisibility');
  background-color: rgb(var(--v-theme-background));
  height: auto;
  width: 100%;
}

// 選択カーソル無効化（一覧でマウスオーバー時のカーソルをdefaultに設定）
:deep(.v-data-table__tr) {
  cursor: inherit;
}

// 一覧の設定
:deep(.v-table) {
  overflow-x: auto;
}
</style>
