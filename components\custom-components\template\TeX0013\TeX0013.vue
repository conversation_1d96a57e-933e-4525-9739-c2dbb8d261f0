<script setup lang="ts">
/**
 * GUI01235_評価表
 *
 * @description
 * 評価表
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or13844Const } from '../../organisms/Or13844/Or13844.constants'
import { Or13850Const } from '../../organisms/Or13850/Or13850.constants'
import { Or13872Const } from '../../organisms/Or13872/Or13872.constants'
import { Or51548Const } from '../../organisms/Or51548/Or51548.constants'
import { Or53098Const } from '../../organisms/Or53098/Or53098.constants'
import { OrX0006Const } from '../../organisms/OrX0006/OrX0006.constants'
import { Or54951Const } from '../../organisms/Or54951/Or54951.constants'
import { Or35345Const } from '../../organisms/Or35345/Or35345.constants'
import type { Or35345TextAreaListType } from '../../organisms/Or35345/Or35345.type'
import { Or53098Logic } from '../../organisms/Or53098/Or53098.logic'
import { Or51548Logic } from '../../organisms/Or51548/Or51548.logic'
import type { Or51548ConfirmType } from '../../organisms/Or51548/Or51548.type'
import { Or13844Logic } from '../../organisms/Or13844/Or13844.logic'
import type { Or13844EventType } from '../../organisms/Or13844/Or13844.type'
import { OrX0115Const } from '../../organisms/OrX0115/OrX0115.constants'
import { OrX0115Logic } from '../../organisms/OrX0115/OrX0115.logic'
import type { Or54951TableDataListType } from '../../organisms/Or54951/Or54951.type'
import { TeX0013Logic } from './TeX0013.logic'
import { TeX0013Const } from './TeX0013.constants'
import type { MasterDataType } from './TeX0013.type'
import {
  useCmnCom,
  useCmnRouteCom,
  useJigyoList,
  useScreenInitFlg,
  useScreenStore,
  useScreenTwoWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
  useUserListInfo,
} from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import type {
  TeX0013EventType,
  TeX0013OnewayType,
  TeX0013Type,
} from '~/types/cmn/business/components/TeX0013Type'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00028OnewayType } from '~/types/business/components/Mo00028Type'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type {
  EvaluationTableInitInfoSelectInEntity,
  EvaluationTableInitInfoSelectOutEntity,
  EvaluationTableLowerTierInfoUpdateType,
  EvaluationTableNewSelectInEntity,
  EvaluationTableNewSelectOutEntity,
  EvaluationTablePlanPeriodSelectInEntity,
  EvaluationTablePlanPeriodSelectOutEntity,
  EvaluationTableUpdateInEntity,
  EvaluationTableUpdateOutEntity,
} from '~/repositories/cmn/entities/evaluationTableInitInfoSelect'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { Or54951OnewayType } from '~/types/cmn/business/components/Or54951Type'
import type { Or35345OnewayType } from '~/types/cmn/business/components/Or35345Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { DIALOG_BTN, SPACE_WAVE, UPDATE_KBN } from '~/constants/classification-constants'
import type {
  EvaluationTableCarePlan2ImportSelectInEntity,
  EvaluationTableCarePlan2ImportSelectOutEntity,
  EvaluationTableImplementationPlanImportSelectInEntity,
  EvaluationTableImplementationPlanImportSelectOutEntity,
} from '~/repositories/cmn/entities/evaluationTableImplementationPlanImportSelectInEntity'
import type {
  EvaluationTableDuplicateSelectInEntity,
  EvaluationTableDuplicateSelectOutEntity,
  EvaluationTableHistorySelectInEntity,
  EvaluationTableHistorySelectOutEntity,
} from '~/repositories/cmn/entities/evaluationTablePlanPeriodSelectEntity'
import type { Or51548OnewayType } from '~/types/cmn/business/components/Or51548Type'
import type { Or13844OnewayType } from '~/types/cmn/business/components/Or13844Type'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import type { Or13872OnewayType } from '~/types/cmn/business/components/Or13872Type'
import type { OrX0157OnewayType, OrX0157Type } from '~/types/cmn/business/components/OrX0157Type'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import type { InitialSettingData } from '~/repositories/cmn/entities/InitialSettingMasterSelectEntity'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: TeX0013OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

const { syscomUserSelectWatchFunc } = useUserListInfo()
const { jigyoListWatch } = useJigyoList()
const { getInitialSettingMaster } = useCmnRouteCom()

/** 画面初期化フラグ */
const isInit = useScreenInitFlg()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
/**
 * ユーザーID
 */
const userId = ref('')

/**
 * ロード状態制御
 */
const isLoading = ref(false)

/**
 * 作成者ID
 */
const createUserId = ref('')

/**
 * 保存API実行完了フラグ
 */
const saveCompletedStateFlg = ref(false)

/**
 * ルート共通情報
 */
const routerMasterInfo = ref<MasterDataType>({
  cmoniBangouFlg: '',
  cmoniIchiranFlg: '',
  cmoniKeisen1Flg: '',
  cmoniKeisen2Flg: '',
  cmoniSypYmdFlg: '',
  cmoniSizeFlg: '',
  cmoniSelFlg: '',
  cmoniSncFlg: '',
  cmoniSypChoFlg: '',
  cmoniChoukiFlg: '',
  cmoniSypTanFlg: '',
  cpnFlg: '',
  shosikiFlg: '',
  cksFlg: '',
})

/**
 * 事業者詳細
 */
const officeInfo = ref<{
  houjinId: string
  shisetuId: string
  svJigyoId: string
  svJigyoCd: string
  jigyoRyakuKnj: string
}>(
  {} as {
    houjinId: string
    shisetuId: string
    svJigyoId: string
    svJigyoCd: string
    jigyoRyakuKnj: string
  }
)

/**
 * 期間管理フラグ
 */
const plannningPeriodManageFlg = ref<string>('1')

/**
 * 保存後必要な操作タイプ
 */
const afterProcess: {
  /**
   * '0': 新規処理
   * '1': 複写
   * '2': マスタ画面を呼び出す
   * '3': タブ変更
   * '4': 計画期間変更
   * '5': 歴史変更
   * '6': 事業所変更処理
   * '7': 期間ID指定処理
   * '8': 履歴ID指定処理
   * '9': 優先順位ダイアログ表示
   * '10': 一覧ダイアログ表示
   * '11': 利用者変更
   * '': 処理なし「画面再表示」
   */
  type: '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | '11' | ''
  value: string
} = {
  type: '',
  value: '',
}

/**
 * 新規ボタン押下回数
 */
const createNewBtnClickNum = ref(0)

/**
 * 初期フラグ
 */
const initFlg = ref(false)

/**
 * 画面更新区分
 */
const screenUpadateKbn = ref<'' | 'U' | 'D' | 'C'>('')

/**
 * 作成者ID一時保存
 */
let _staffId = ''

/**
 * 権限保存変数
 */
const userAuth = ref<{
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * ケース権限
   */
  caseAuthority: string
  /**
   * 実施計画～①権限
   */
  implementationPlan1Authority: string
  /**
   * 実施計画～②権限
   */
  implementationPlan2Authority: string
  /**
   * 実施計画～③権限
   */
  implementationPlan3Authority: string
  /**
   * 計画書権限
   */
  planAuthority: string
}>({
  syubetsuId: '',
  caseAuthority: '',
  implementationPlan1Authority: '',
  implementationPlan2Authority: '',
  implementationPlan3Authority: '',
  planAuthority: '',
})

/**
 * 履歴、作成者、作成日表示フラグ
 */
const formDisplayFlg = ref(true)

/**
 * 複写画面唯一のキー
 */
const componentsKey = ref('')

const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or13850 = ref({ uniqueCpId: '' })
const or13850_2 = ref({ uniqueCpId: '' })
const or13844 = ref({ uniqueCpId: '' })
const or13872 = ref({ uniqueCpId: '' })
const orX0006 = ref({ uniqueCpId: '' })
const or41179 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21814_2 = ref({ uniqueCpId: '' })
const or21814_3 = ref({ uniqueCpId: '' })
const or21814_4 = ref({ uniqueCpId: '' })
const or51548 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or53098 = ref({ uniqueCpId: '' })
const or54951 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or35345 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const orX0115 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })

/**
 * ロカールOneway
 */
const localOneway = reactive({
  or13844Oneway: {
    plainningPeriodManageFlg: '',
    planningPeriodInfo: {
      periodCnt: '',
      periodNo: '',
    },
    showLabelMode: false,
  } as Or13844OnewayType,
  // 作成日
  createDateOneway: {
    itemLabel: t('label.create-date'),
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-row',
      labelClass: 'ma-1',
    }),
    disabled: screenUpadateKbn.value === UPDATE_KBN.DELETE,
  } as Mo00020OnewayType,
  // 要介護課題等々
  mo00028OnewayType: {
    panelTitle: t('label.evaluation-table-panel-title'),
    customClass: new CustomClass({
      itemClass: 'bold',
    }),
  } as Mo00028OnewayType,
  or54951Oneway: {} as Or54951OnewayType,
  or35345Oneway: {} as Or35345OnewayType,
  or53098Oneway: {
    shisetuId: officeInfo.value.shisetuId,
    svJigyoId: officeInfo.value.svJigyoId,
    bunrui1Id: '2',
    bunrui2Id: '29',
    editFlg: true,
  },
  or51548Oneway: {} as Or51548OnewayType,
  orX0115Oneway: {
    kindId: '',
    sc1Id: '',
  } as OrX0115OnewayType,
  // 履歴選択
  or13872Oneway: {
    showLabelMode: false,
  } as Or13872OnewayType,
  orX0157Oneway: {
    inputMode: 'TextOnly',
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        isVerticalLabel: true,
        showItemLabel: true,
        itemLabel: t('label.author'),
        customClass: new CustomClass({
          labelClass: 'pb-1',
          labelStyle: 'background-color:rgb(var(--v-theme-background));',
        }),
        width: '220px',
      },
    },
    inputReadonly: true,
  } as OrX0157OnewayType,
})

/**
 * ロカール
 */
const local = reactive({
  createDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      },
    },
  },
  // 作成者
  orX0157: {
    value: '',
  } as OrX0157Type,
})

/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or13850Const.CP_ID(1)]: or13850.value,
  [Or13844Const.CP_ID(0)]: or13844.value,
  [Or13872Const.CP_ID(0)]: or13872.value,
  [Or13850Const.CP_ID(2)]: or13850_2.value,
  [OrX0006Const.CP_ID(1)]: orX0006.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or21814Const.CP_ID(3)]: or21814_3.value,
  [Or21814Const.CP_ID(4)]: or21814_4.value,
  [Or51548Const.CP_ID(1)]: or51548.value,
  [Or53098Const.CP_ID(1)]: or53098.value,
  [Or54951Const.CP_ID(1)]: or54951.value,
  [Or35345Const.CP_ID(1)]: or35345.value,
  [OrX0115Const.CP_ID(1)]: orX0115.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
})
const { refValue } = useScreenTwoWayBind<TeX0013Type>({
  cpId: Or54951Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// ★利用者一覧詳細表示(Or00248)の「領域キー」を設定
Or00248Logic.state.set({
  uniqueCpId: or00248.value.uniqueCpId,
  state: {
    regionKey: props.uniqueCpId, // 「領域キー」を設定
  },
})

systemCommonsStore.setUserSelectZenkaiSvJigyoIdList([''])
systemCommonsStore.setUserSelectSelfId('')
/**************************************************
 * computed
 **************************************************/
// ダイアログ表示フラグ
const showDialogOr21814_1 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})
// ナビゲーション制御領域のいずれかの編集フラグがON
const _isEdit = computed(() => {
  return (
    useScreenStore().isEditByUniqueCpId(or54951.value.uniqueCpId) ||
    useScreenStore().isEditByUniqueCpId(or35345.value.uniqueCpId)
  )
})
// ダイアログ表示フラグ
const showDialogOr21814_2 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr21814_3 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr53098 = computed(() => {
  // or53098のダイアログ開閉状態
  const state = Or53098Logic.state.get(or53098.value.uniqueCpId) as { isOpen?: boolean }
  return state?.isOpen ?? false
})
/**
 * 複写ダイアログ
 */
const showOverwriteDialog = computed(() => {
  return Or51548Logic.state.get(or51548.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 計画期間ダイアログ表示フラグ
 */
const showPlanPeriodDialog = computed(() => {
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})
/** エラーダイアログ表示フラグ */
const showErrorDialog = computed(() => {
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
// // ダイアログ表示フラグ
// const showDialogOr26677 = computed(() => {
//   // Or26677のダイアログ開閉状態
//   return Or26677Logic.state.get(or26677.value.uniqueCpId)?.isOpen ?? false
// })
// // ダイアログ表示フラグ
// const showDialogOr26447 = computed(() => {
//   // Or26677のダイアログ開閉状態
//   return Or26677Logic.state.get(or26447.value.uniqueCpId)?.isOpen ?? false
// })
// // ダイアログ表示フラグ
// const showDialogOr61005 = computed(() => {
//   // Or61005のダイアログ開閉状態
//   return Or61005Logic.state.get(or61005.value.uniqueCpId)?.isOpen ?? false
// })
/**************************************************
 * 関数定義
 **************************************************/
/**
 * コントロール初期化
 */
function initControl() {
  /**
   * 事業所選択初期化処理
   */
  // 事業所選択プルダウンの検索条件設定
  // 検索条件未指定だと、ヘッダーで選択した適用事業所が表示される
  // 検索条件が変更されると、共通処理にて事業所の情報が更新される
  // 更新後やプルダウンの値変更後、変更後の事業所に基づいて処理を行う場合、
  // 関数「 jigyoListWatch 」にコールバック関数を渡す
  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })
  // マスタ情報取得
  getMasterDataInfo()

  // 子コンポーネントに対して初期設定を行う
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      screenTitleLabel: t('label.evaluation-table'),
      showFavorite: true,
      showViewSelect: false,
      viewSelectItems: [],
      showSaveBtn: true,
      showCreateBtn: true,
      showCreateMenuCopy: false,
      showPrintBtn: true,
      showMasterBtn: true,
      showOptionMenuBtn: true,
      showOptionMenuDelete: true,
    },
  })
  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 削除ダイアログ初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 削除ダイアログ初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_4.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.notes'),
      firstBtnType: 'normal3',
      firstBtnLabel: t('label.close'),
      secondBtnType: 'blank',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // エラーダイアログ初期化
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
}

/**
 * データ変更チェック関数
 *
 * @returns
 * 0:データ変更なし、次の操作を行う
 * 1:データの変更を廃棄する、次の操作を行う
 * 2:データ変更を保存して、保存済み、次の操作を行う
 */
const getDataIsChanged = async () => {
  let result = TeX0013Const.DEFAULT.IS_EDIT_NO_CHANGE
  // 共通処理の登録権限を取得する
  // const saveAuth = hasRegistAuth()
  const saveAuth = true
  if (_isEdit.value) {
    // 画面入力変更あり、かつ保存権限がない場合
    // 確認ダイアログ表示
    if (!saveAuth) {
      const dialogResult = await getOr21814_3DialogResult(t('message.w-com-10006'))
      switch (dialogResult) {
        case DIALOG_BTN.YES:
          result = TeX0013Const.DEFAULT.IS_EDIT_CHANGE_DISCARD
          break
        case DIALOG_BTN.CANCEL:
        case DIALOG_BTN.NO:
          result = TeX0013Const.DEFAULT.IS_EDIT_CHANGE_CLOSE_DIALOG
      }
      return result
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await getOr21814_3DialogResult(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case DIALOG_BTN.YES:
          result = TeX0013Const.DEFAULT.IS_EDIT_CHANGE_SAVE
          break
        case DIALOG_BTN.CANCEL:
          result = TeX0013Const.DEFAULT.IS_EDIT_CHANGE_CLOSE_DIALOG
          break
        case DIALOG_BTN.NO:
          result = TeX0013Const.DEFAULT.IS_EDIT_CHANGE_DISCARD
          break
      }
      return result
    }
  } else {
    return result
  }
}

/**
 * マスタ情報取得
 */
const getMasterDataInfo = () => {
  const masterData: InitialSettingData | undefined = getInitialSettingMaster()
  if (masterData) {
    routerMasterInfo.value = {
      /** 初期設定マスタ.モニタリング：番号 */
      cmoniBangouFlg: masterData.cmoniBangouFlg ?? '',
      /** 初期設定マスタ.モニタリング：表示形式 */
      cmoniIchiranFlg: masterData.cmoniIchiranFlg ?? '',
      /** 初期設定マスタ.モニタリング：内容が空白は罫線を省略 */
      cmoniKeisen1Flg: masterData.cmoniKeisen1Flg ?? '',
      /** 初期設定マスタ.モニタリング：内容が同じは罫線を省略 */
      cmoniKeisen2Flg: masterData.cmoniKeisen2Flg ?? '',
      /** 初期設定マスタ.モニタリング：期間のカレンダー取込み */
      cmoniSypYmdFlg: masterData.cmoniSypYmdFlg ?? '',
      /** 初期設定マスタ.モニタリング：印刷時の文字サイズ */
      cmoniSizeFlg: masterData.cmoniSizeFlg ?? '',
      /** 初期設定マスタ.モニタリング：新規ボタン押下時の様式選択画面の表示 */
      cmoniSelFlg: masterData.cmoniSelFlg ?? '',
      /** 初期設定マスタ.モニタリング：モニタリングの進捗管理連動 */
      cmoniSncFlg: masterData.cmoniSncFlg ?? '',
      /** 初期設定マスタ.モニタリング：実施計画～②長期目標取込 */
      cmoniSypChoFlg: masterData.cmoniSypChoFlg ?? '',
      /** 初期設定マスタ.モニタリング：長期目標 */
      cmoniChoukiFlg: masterData.cmoniChoukiFlg ?? '',
      /** 初期設定マスタ.モニタリング：実施計画～②短期目標取込 */
      cmoniSypTanFlg: masterData.cmoniSypTanFlg ?? '',
      /** 初期設定マスタ.ケアプラン方式 */
      cpnFlg: masterData.cpnFlg ?? '',
      /** 初期設定マスタ.計画書書式 */
      shosikiFlg: masterData.shosikiFlg ?? '',
      /** 初期設定マスタ.計画書様式 */
      cksFlg: masterData.cksFlg ?? '',
    }
    // 共通情報設定
    setCommonInfo({
      assessmentFormat: masterData.cpnFlg,
      omittedBlankBorderFlg: masterData.cmoniKeisen1Flg,
      omittedSameBorderFlg: masterData.cmoniKeisen2Flg,
    })
  }
}

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで処理を実行する
 *
 * @param newSelfId - 変更後の利用者番号
 */
const callbackUserChange = (newSelfId: string) => {
  if (newSelfId !== '' && userId.value === '') {
    void userChange(newSelfId)
    return
  }
  // 変更がない場合、スキップ
  if (newSelfId === userId.value) {
    return
  }
  // 利用者変更処理
  void userChange(newSelfId)
}

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange, props.uniqueCpId)

/**
 * 「利用者」データ切替場合
 *
 * @param newSelfId - 変更後の利用者番号
 */
const userChange = async (newSelfId: string) => {
  // 初期化の場合、チェックなしで処理を続行
  if (initFlg.value === false) {
    userId.value = newSelfId
    // 共通情報に保存する
    setCommonInfo({ userId: newSelfId })

    systemCommonsStore.setUserSelectSelfId(newSelfId)
    await reload()
    // 初期化完了フラグをtrueに設定する
    initFlg.value = true

    // 処理終了
    return
  }

  // 画面データ変更ありの場合
  switch (await getDataIsChanged()) {
    case TeX0013Const.DEFAULT.IS_EDIT_NO_CHANGE:
      // 変更なし、初期化を実行する
      userId.value = newSelfId
      // 共通情報に保存する
      setCommonInfo({ userId: newSelfId })
      // システム共通情報に保存する
      systemCommonsStore.setUserSelectSelfId(newSelfId, props.uniqueCpId)
      await reload()
      break
    case TeX0013Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
      // 入力内容を廃棄して、初期化処理を行う
      userId.value = newSelfId
      // 共通情報に保存する
      setCommonInfo({ userId: newSelfId })
      // システム共通情報に保存する
      systemCommonsStore.setUserSelectSelfId(newSelfId, props.uniqueCpId)
      await reload()
      break
    case TeX0013Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
      // 入力内容を保存する、初期化処理を行う
      userId.value = newSelfId
      // 共通情報に保存する
      setCommonInfo({ userId: newSelfId })
      // システム共通情報に保存する
      systemCommonsStore.setUserSelectSelfId(newSelfId, props.uniqueCpId)
      // 保存後処理タイプを一時保存する
      afterProcess.type = TeX0013Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.USER_CHANGE
      break
  }
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId !== '') {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const jigyoInfo = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)
    // データ変更チェック
    if (jigyoInfo) {
      // 初期化の場合
      if (!officeInfo.value.svJigyoId) {
        setCommonInfo({
          svJigyoId: jigyoInfo.svJigyoId,
          houjinId: jigyoInfo.houjinId,
        })
      }
      officeInfo.value = jigyoInfo
    }
  }
}
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**
 * 共通情報設定
 *
 * @param commonInfo - 共通情報
 */
const setCommonInfo = (commonInfo: TeX0013Type) => {
  const originData = TeX0013Logic.data.get(props.uniqueCpId)
  TeX0013Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      masterHeadId: commonInfo?.masterHeadId ?? originData?.masterHeadId,
      sc1Id: commonInfo?.sc1Id ?? originData?.sc1Id,
      planBookAuth: commonInfo?.planBookAuth ?? originData?.planBookAuth,
      caseAuth: commonInfo?.caseAuth ?? originData?.caseAuth,
      planPeriodFlg: commonInfo?.planPeriodFlg ?? originData?.planPeriodFlg,
      omittedBlankBorderFlg: commonInfo?.omittedBlankBorderFlg ?? originData?.omittedBlankBorderFlg,
      omittedSameBorderFlg: commonInfo?.omittedSameBorderFlg ?? originData?.omittedSameBorderFlg,
      planBookDisplayFlg: commonInfo?.planBookDisplayFlg ?? originData?.planBookDisplayFlg,
      cmoni1Id: commonInfo?.cmoni1Id ?? originData?.cmoni1Id,
      userId: commonInfo?.userId ?? originData?.userId,
      houjinId: commonInfo?.houjinId ?? originData?.houjinId,
      shisetuId: commonInfo?.shisetuId ?? originData?.shisetuId,
      svJigyoId: commonInfo?.svJigyoId ?? originData?.svJigyoId,
      createYmd: commonInfo?.createYmd ?? originData?.createYmd,
      shokuId: commonInfo?.shokuId ?? originData?.shokuId,
      reAssessment: commonInfo?.reAssessment ?? originData?.reAssessment,
      yoteiYmd: commonInfo?.yoteiYmd ?? originData?.yoteiYmd,
      historyModifiedCnt: commonInfo?.historyModifiedCnt ?? originData?.historyModifiedCnt,
      remarksInfoList: commonInfo?.remarksInfoList ?? originData?.remarksInfoList,
      implementationPlanImportInfoList:
        commonInfo?.implementationPlanImportInfoList ??
        originData?.implementationPlanImportInfoList,
      yokaiKbn: commonInfo?.yokaiKbn ?? originData?.yokaiKbn,
      copyData: commonInfo?.copyData ?? originData?.copyData,
      copyMode: commonInfo?.copyMode ?? originData?.copyMode,
      carePlan2ImportInfoList:
        commonInfo?.carePlan2ImportInfoList ?? originData?.carePlan2ImportInfoList,
      assessmentFormat: commonInfo.assessmentFormat ?? originData?.assessmentFormat,
    },
  })
}

/**
 * 計画期間取得
 *
 * @param pageFlg - ページ区分
 */
async function getPlanPeroidInfo(pageFlg: string) {
  try {
    const inputData: EvaluationTablePlanPeriodSelectInEntity = {
      svJigyoId: officeInfo.value.svJigyoId,
      userId: userId.value,
      syubetsuId: '0',
      // 共通情報.施設ID
      shisetuId: officeInfo.value.shisetuId,
      sc1Id: '0',
      planPeriodPageCategory: pageFlg,
      kikanKanriFlg: 'null',
      processCategory: '0',
      shokuinId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      cpnFlg: routerMasterInfo.value.cpnFlg,
      shosikiFlg: routerMasterInfo.value.shosikiFlg,
      menu2Name: '[mnu2][3GK]ﾓﾆﾀﾘﾝｸﾞ',
      menu3Name: '[mnu3][3GK]新評価表',
      freeParameter: 'null',
      svJigyoIdList: (systemCommonsStore.getSvJigyoIdList as string[]).join(',') ?? [].join(','),
      gsyscd: systemCommonsStore.getSystemCode ?? '0',
      cmoni1Id: '0',
    }

    const resData: EvaluationTablePlanPeriodSelectOutEntity = await ScreenRepository.select(
      'evaluationTablePlanPeriodSelect',
      inputData
    )
    if (resData.data) {
      const {
        syubetsuId,
        caseAuthority,
        implementationPlan1Authority,
        implementationPlan2Authority,
        implementationPlan3Authority,
        planAuthority,
        historyInfo,
        planPeriodInfo,
        kikanFlg,
      } = resData.data

      // 計画期間詳細保存
      localOneway.or13844Oneway.plainningPeriodManageFlg = kikanFlg
      plannningPeriodManageFlg.value = kikanFlg
      // 期間管理フラグが「1:期間管理する」
      if (plannningPeriodManageFlg.value === TeX0013Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
        if (planPeriodInfo.sc1Id !== '0' && planPeriodInfo.periodCnt !== '0') {
          localOneway.or13844Oneway.planningPeriodInfo = {
            periodCnt: planPeriodInfo.periodCnt ?? '0',
            periodNo: planPeriodInfo.periodNo ?? '0',
            period: `${planPeriodInfo.startYmd}${SPACE_WAVE}${planPeriodInfo.endYmd}`,
            id: planPeriodInfo.sc1Id ?? '',
          }
          formDisplayFlg.value = true
        }
        // 期間管理フラグが「1:期間管理する」、かつ、計画対象期間リストが0件
        else if (planPeriodInfo.periodCnt === '0') {
          // ・計画対象期間-ページングを"0 / 0"で表示に設定する。
          // ・計画対象期間を固定文字「対象期間を登録してください」で表示に設定する。
          // ・履歴、作成者、作成日、入力フームを非表示に設定する。
          localOneway.or13844Oneway.planningPeriodInfo = {
            id: '',
            period: '',
            periodNo: '0',
            periodCnt: '0',
          }
          formDisplayFlg.value = false
        }

        // 各権限を取得する
        userAuth.value = {
          syubetsuId,
          caseAuthority,
          implementationPlan1Authority,
          implementationPlan2Authority,
          implementationPlan3Authority,
          planAuthority,
        }
        // 履歴リストが0件以外
        if (historyInfo.cmoni1Id !== '0' && historyInfo.krirekiCnt !== '0') {
          // 歴史詳細を保存
          localOneway.or13872Oneway.historyInfo = {
            rirekiId: historyInfo.cmoni1Id,
            krirekiNo: historyInfo.krirekiNo ?? '0',
            krirekiCnt: historyInfo.krirekiCnt ?? '0',
          }
          // 作成日保存
          local.createDate.value = historyInfo.createYmd ?? systemCommonsStore.getSysDate ?? ''
          // 作成者詳細保存
          local.orX0157.value = historyInfo.shokuinKnj ?? ''
          // 作成者ID
          _staffId = historyInfo.shokuId ?? ''
          // 共通情報設定
          setCommonInfo({
            masterHeadId: historyInfo.cmoni1Id,
            sc1Id: historyInfo.sc1Id,
            caseAuth: caseAuthority,
            planBookAuth: planAuthority,
            planPeriodFlg: kikanFlg,
            omittedBlankBorderFlg: routerMasterInfo.value.cmoniKeisen1Flg,
            omittedSameBorderFlg: routerMasterInfo.value.cmoniKeisen2Flg,
            cmoni1Id: historyInfo.cmoni1Id,
            createYmd: historyInfo.createYmd,
            shokuId: historyInfo.shokuId,
            implementationPlan1Authority: implementationPlan1Authority === '1',
            implementationPlan2Authority: implementationPlan2Authority === '1',
            implementationPlan3Authority: implementationPlan3Authority === '1',
          })
          return true
        } else {
          localOneway.or13872Oneway.historyInfo = {
            rirekiId: '0',
            krirekiNo: '0',
            krirekiCnt: '0',
          }
          // 共通情報設定
          setCommonInfo({
            masterHeadId: '0',
            sc1Id: planPeriodInfo.sc1Id,
            caseAuth: caseAuthority,
            planBookAuth: planAuthority,
            planPeriodFlg: kikanFlg,
            omittedBlankBorderFlg: routerMasterInfo.value.cmoniKeisen1Flg,
            omittedSameBorderFlg: routerMasterInfo.value.cmoniKeisen2Flg,
            cmoni1Id: '0',
            createYmd: historyInfo.createYmd,
            shokuId: historyInfo.shokuId,
            implementationPlan1Authority: implementationPlan1Authority === '1',
            implementationPlan2Authority: implementationPlan2Authority === '1',
            implementationPlan3Authority: implementationPlan3Authority === '1',
          })
          return false
        }
      }
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 初期情報取得
 */
async function getInitDataInfo() {
  try {
    refValue.value = {
      ...refValue.value,
      remarksInfoList: [],
    }
    const inputData: EvaluationTableInitInfoSelectInEntity = {
      // マスタID
      free1Id: '-1',
      // ヘッダID
      cmoni1Id: localOneway.or13872Oneway?.historyInfo?.rirekiId ?? '',
    }

    const resData: BaseResponseBody<EvaluationTableInitInfoSelectOutEntity> =
      await ScreenRepository.select('evaluationTableInitInfoSelect', inputData)
    if (resData.data) {
      // テーブルエリア設定
      localOneway.or54951Oneway.tableStyle = resData.data.evaluationTableUpperTierStyleInfoList
      localOneway.or54951Oneway.maxRows = resData.data.columnCount1
      localOneway.or54951Oneway.tableDataList = resData.data.evaluationTableUpperTierInfoList
      // 総括エリア設定
      // 計画書表示フラグ設定
      const rendouKbnList = [
        ...resData.data.evaluationTableUpperTierStyleInfoList,
        ...resData.data.evaluationTableLowerTierStyleInfoList,
      ].map((item) => item.rendouKbn)
      let planBookDisplayFlg = true
      rendouKbnList.forEach((item) => {
        if (TeX0013Const.DEFAULT.PLAN_BOOK_BTN_DISPLAY_RENDOU_KBN_LIST.includes(item)) {
          planBookDisplayFlg = true
        } else {
          planBookDisplayFlg = false
        }
      })
      setCommonInfo({ planBookDisplayFlg })
      // 1から3まで繰り返し、下記処理を行う
      // 評価表下段様式の項目数より大きい、または、評価表下段様式リストの件数より大きい項目は非表示
      // 以外の場合、下記処理を行う。
      // 項目データに評価表下段情報リスト.文章を表示する。
      // 下段様式の項目数取得
      // ②計画書ボタンの表示設定：
      // 評価表下段様式リスト.連動区分が127の場合
      //  ・計画書権限有るの場合、計画書ボタンを表示する。
      //  ・以外の場合、計画書ボタンを非表示する。

      const styleNum = parseInt(resData.data.columnCount2)
      // 下段様式リスト取得
      const styleList = resData.data.evaluationTableLowerTierStyleInfoList

      // 一時保存用リスト
      const saveResultList = [] as Or35345TextAreaListType[]
      for (let i = 1; i <= 3; i++) {
        //  評価表下段様式の項目数より大きいは非表示
        if (i > styleNum) break
        // 評価表下段情報リストの件数より大きい項目は非表示
        if (i > styleList.length) break
        const item = styleList[i - 1]
        // ②計画書ボタンの表示設定
        setCommonInfo({
          planBookDisplayFlg: item.rendouKbn === '127' && userAuth.value.planAuthority === '1',
        })
        if (item) {
          saveResultList.push({
            label: item.nameKnj,
            rendouKbn: item.rendouKbn,
            modifiedCnt: resData.data.evaluationTableLowerTierInfo.modifiedCnt,
            koumokuId: item.koumokuId,
            historyModifiedCnt: resData.data.evaluationTableLowerTierInfo.historyModifiedCnt,
            updateKbn: '',
            widthCnt: item.widthCnt,
            textareaValue: { value: resData.data.evaluationTableLowerTierInfo[`koumoku0${i}Knj`] },
          })
        }
      }
      refValue.value.remarksInfoList = saveResultList
      localOneway.or35345Oneway.textAreaInfo = refValue.value.remarksInfoList
      localOneway.or35345Oneway.reAssessment = resData.data.evaluationTableLowerTierInfo.retryChk
      localOneway.or35345Oneway.yoteiYmd = resData.data.evaluationTableLowerTierInfo.yoteiYmd

      setCommonInfo({
        historyModifiedCnt: resData.data.evaluationTableLowerTierInfo.historyModifiedCnt,
        remarksInfoList: saveResultList,
        yoteiYmd: resData.data.evaluationTableLowerTierInfo.yoteiYmd,
        reAssessment: resData.data.evaluationTableLowerTierInfo.retryChk,
      })
    }
  } catch (e) {
    console.log(e, 'error')
  }
}

/**
 * 保存処理
 */
const userSave = async () => {
  try {
    if (!_isEdit.value || screenUpadateKbn.value === UPDATE_KBN.NONE) {
      setShowDialog(t('message.i-cmn-21800'))
      return
    }

    const childrenRefValue = useScreenUtils().getChildCpBinds(props.uniqueCpId, {
      [Or35345Const.CP_ID(0)]: { cpPath: Or35345Const.CP_ID(1), twoWayFlg: true },
      [Or54951Const.CP_ID(0)]: { cpPath: Or54951Const.CP_ID(1), twoWayFlg: true },
    })
    // 評価表データ
    const tableDataList = childrenRefValue[Or54951Const.CP_ID(0)].twoWayBind
      ?.value as Or54951TableDataListType[][]
    // 下段備考データ
    const remarkInfo = childrenRefValue[Or35345Const.CP_ID(0)].twoWayBind
      ?.value as Or35345OnewayType
    console.log('remarkInfo', remarkInfo)
    console.log('tableDataList', tableDataList)
    const evaluationTableUpperTierInfoList = tableDataList.map(
      (item, index): EvaluationTableUpdateInEntity['evaluationTableUpperTierInfoList'][number] => {
        return {
          cmoni3Id: item[0].cmoni3Id,
          updateKbn: item[0].updateKbn === '' ? UPDATE_KBN.UPDATE : item[0].updateKbn!,
          sort: (index + 1).toString(),
          koumoku01Knj: item?.[0]?.knj?.value ?? '',
          koumoku02Knj: item?.[1]?.knj?.value ?? '',
          koumoku03Knj: item?.[2]?.knj?.value ?? '',
          koumoku04Knj: item?.[3]?.knj?.value ?? '',
          koumoku05Knj: item?.[4]?.knj?.value ?? '',
          koumoku06Knj: item?.[5]?.knj?.value ?? '',
          koumoku07Knj: item?.[6]?.knj?.value ?? '',
          koumoku08Knj: item?.[7]?.knj?.value ?? '',
          koumoku09Knj: item?.[8]?.knj?.value ?? '',
          koumoku10Knj: item?.[9]?.knj?.value ?? '',
          koumoku11Knj: item?.[10]?.knj?.value ?? '',
          koumoku12Knj: item?.[11]?.knj?.value ?? '',
          koumoku13Knj: item?.[12]?.knj?.value ?? '',
          koumoku14Knj: item?.[13]?.knj?.value ?? '',
          koumoku15Knj: item?.[14]?.knj?.value ?? '',
          koumoku01Cod: item?.[0]?.cod?.modelValue ?? '',
          koumoku02Cod: item?.[1]?.cod?.modelValue ?? '',
          koumoku03Cod: item?.[2]?.cod?.modelValue ?? '',
          koumoku04Cod: item?.[3]?.cod?.modelValue ?? '',
          koumoku05Cod: item?.[4]?.cod?.modelValue ?? '',
          koumoku06Cod: item?.[5]?.cod?.modelValue ?? '',
          koumoku07Cod: item?.[6]?.cod?.modelValue ?? '',
          koumoku08Cod: item?.[7]?.cod?.modelValue ?? '',
          koumoku09Cod: item?.[8]?.cod?.modelValue ?? '',
          koumoku10Cod: item?.[9]?.cod?.modelValue ?? '',
          koumoku11Cod: item?.[10]?.cod?.modelValue ?? '',
          koumoku12Cod: item?.[11]?.cod?.modelValue ?? '',
          koumoku13Cod: item?.[12]?.cod?.modelValue ?? '',
          koumoku14Cod: item?.[13]?.cod?.modelValue ?? '',
          koumoku15Cod: item?.[14]?.cod?.modelValue ?? '',
          koumoku01Ymd: item?.[0]?.startYmd?.value ?? '',
          koumoku02Ymd: item?.[1]?.startYmd?.value ?? '',
          koumoku03Ymd: item?.[2]?.startYmd?.value ?? '',
          koumoku04Ymd: item?.[3]?.startYmd?.value ?? '',
          koumoku05Ymd: item?.[4]?.startYmd?.value ?? '',
          koumoku06Ymd: item?.[5]?.startYmd?.value ?? '',
          koumoku07Ymd: item?.[6]?.startYmd?.value ?? '',
          koumoku08Ymd: item?.[7]?.startYmd?.value ?? '',
          koumoku09Ymd: item?.[8]?.startYmd?.value ?? '',
          koumoku10Ymd: item?.[9]?.startYmd?.value ?? '',
          koumoku11Ymd: item?.[10]?.startYmd?.value ?? '',
          koumoku12Ymd: item?.[11]?.startYmd?.value ?? '',
          koumoku13Ymd: item?.[12]?.startYmd?.value ?? '',
          koumoku14Ymd: item?.[13]?.startYmd?.value ?? '',
          koumoku15Ymd: item?.[14]?.startYmd?.value ?? '',
        }
      }
    )
    const evaluationTableLowerTierInfo: EvaluationTableLowerTierInfoUpdateType = {
      koumoku01Knj: '',
      koumoku02Knj: '',
      koumoku03Knj: '',
    }
    remarkInfo.textAreaInfo.forEach((item, index) => {
      evaluationTableLowerTierInfo[`koumoku0${index}Knj`] = item.textareaValue.value ?? ''
    })

    const inputData: EvaluationTableUpdateInEntity = {
      // 履歴番号
      krirekiNo: localOneway.or13872Oneway.historyInfo?.krirekiNo ?? '',
      // 期間管理フラグ
      kikanFlg: plannningPeriodManageFlg.value,
      // 期間番号
      periodNo: localOneway.or13844Oneway.planningPeriodInfo?.periodNo ?? '',
      // 開始日
      startYmd: localOneway.or13844Oneway.planningPeriodInfo?.periodStartYmd ?? '',
      // 終了日
      endYmd: localOneway.or13844Oneway.planningPeriodInfo?.periodEndYmd ?? '',
      // 事業者名
      svJigyoName: officeInfo.value.jigyoRyakuKnj ?? '',
      // システム略称
      sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
      // セクション名
      sectionName: TeX0013Const.DEFAULT.SECTION_NAME,
      // 職員ID
      shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      // 機能ID
      kinouId: '0',
      // システムコード
      gsyscd: systemCommonsStore.getSystemCode ?? '',
      // インデックス
      index: '1',
      // e文書用パラメータ
      edocumentUseParam: useCmnCom().getEDocumentParam(),
      // e文書削除用パラメータ
      edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
      // ヘーダID
      cmoni1Id: localOneway.or13872Oneway.historyInfo?.rirekiId ?? '',
      // 計画期間ID
      sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
      // 法人ID
      houjinId: officeInfo.value.houjinId ?? '',
      // 施設ID
      shisetuId: officeInfo.value.shisetuId ?? '',
      // 事業者ID
      svJigyoId: officeInfo.value.svJigyoId ?? '',
      // 利用者ID
      userId: userId.value,
      // 種別ID
      syubetsuId: userAuth.value.syubetsuId,
      // 作成日
      createYmd: local.createDate.value,
      // 作成者
      createUserId: createUserId.value,
      // マスタID
      free1Id: '-1',
      // 更新区分
      updateKbn: screenUpadateKbn.value,
      // 再アセスメントの必要
      retryChk: remarkInfo.reAssessment,
      // 再アセスメント予定日
      yoteiYmd: remarkInfo.yoteiYmd,
      // 評価表上段情報リスト
      evaluationTableUpperTierInfoList,
      // 評価表上段情報リスト
      evaluationTableLowerTierInfo,
    }

    const _resData: EvaluationTableUpdateOutEntity = await ScreenRepository.update(
      'evaluationTableUpdate',
      inputData
    )
    // 上記で戻るエラー区分が「1」の場合
    if (_resData.data.errKbn === '1') {
      // ■以下のメッセージを表示
      // e.cmn.40019
      setShowErrorDialog(t('e-cmn-40019'))
      return
    } else if (_resData.data.errKbn === '2') {
      // ■以下のメッセージを表示
      // i.cmn.XXXXX
      setShowDialog(t('e-cmn-40019'))
    }

    afterProcess.value = _resData.data.cmoni1Id
    saveCompletedStateFlg.value = true
  } catch (e) {
    console.log(e)
  }
}

/**
 * 新規操作実行する前のチェック関数
 */
const beforeCreateNewInfo = async () => {
  // 期間管理フラグが「1:期間管理する」、かつ、計画対象期間リストが0件
  if (
    plannningPeriodManageFlg.value === TeX0013Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
    localOneway.or13844Oneway.planningPeriodInfo?.periodCnt === '0'
  ) {
    //  ■以下のメッセージを表示
    // ・メッセージコード：i.cmn.11300
    // ・メッセージ内容：対象期間が未作成です。「改行」
    // 「計画対象期間」ボタンをクリックして表示される画面で対象期間の登録を行ってください。
    // ・後続処理
    // OK：AC013を実行する。
    setShowDialog(t('message.i-cmn-11300'))
    return
  }
  // 二回目新規ボタン押下する場合

  // 画面入力データに変更がある場合
  switch (await getDataIsChanged()) {
    case TeX0013Const.DEFAULT.IS_EDIT_NO_CHANGE:
      // 変更なし、次操作を行う
      await evaluationTableNewSelect()
      break
    case TeX0013Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
      // 変更を廃棄する、次の操作を行う
      await evaluationTableNewSelect()
      break
    case TeX0013Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
      // 入力を保存する、保存済み、次の操作を行う
      createNewBtnClickNum.value += 1
      afterProcess.type = TeX0013Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.CREATE
      await userSave()
  }
}

/**
 * 新規情報取得
 */
const getCreateNewInfo = async () => {
  try {
    const inputData: EvaluationTableNewSelectInEntity = {
      /**
       * マスタID
       */
      free1Id: '-1',
      /**
       * 利用者ID
       */
      userId: userId.value,
      /**
       * 基準日
       */
      kijunbiYmd: local.createDate.value,
      /**
       * アセスメント方式 todo
       */
      cpnFlg: routerMasterInfo.value.cpnFlg,
    }

    /** 新規表示情報を取得する。 */
    const resData: EvaluationTableNewSelectOutEntity = await ScreenRepository.select(
      'evaluationTableNewSelect',
      inputData
    )

    if (resData.data) {
      // テーブル再設定
      localOneway.or54951Oneway.tableStyle = resData.data.evaluationTableUpperTierStyleInfoList
      localOneway.or54951Oneway.maxRows = resData.data.columnCount1
      // 下段様式の項目数取得
      const styleNum = parseInt(resData.data.columnCount2)
      // 下段様式リスト取得
      const styleList = resData.data.evaluationTableLowerTierStyleInfoList

      // 一時保存用リスト
      const saveResultList = [] as Or35345TextAreaListType[]
      for (let i = 1; i <= 3; i++) {
        //  評価表下段様式の項目数より大きいは非表示
        if (i > styleNum) break
        // 評価表下段情報リストの件数より大きい項目は非表示
        if (i > styleList.length) break
        const item = styleList[i - 1]
        // 全入力フォームを空白にする
        if (item) {
          saveResultList.push({
            label: item.nameKnj,
            rendouKbn: item.rendouKbn,
            modifiedCnt: '',
            koumokuId: item.koumokuId,
            historyModifiedCnt: '',
            updateKbn: '',
            widthCnt: item.widthCnt,
            textareaValue: { value: '' },
          })
        }
      }
      refValue.value = {
        ...refValue.value,
        remarksInfoList: saveResultList,
      }
      localOneway.or35345Oneway.textAreaInfo = saveResultList
      localOneway.or35345Oneway.reAssessment = ''
      localOneway.or35345Oneway.yoteiYmd = ''
      setCommonInfo({
        reAssessment: '',
        yoteiYmd: '',
        remarksInfoList: saveResultList,
        yokaiKbn: resData.data.yokaiKbn,
      })
      // 下記処理を実行する。
      // アセスメント方式が5：パッケージプランの場合
      // 要介護度 < 11(要支援１) の場合
      if (routerMasterInfo.value.cpnFlg === '5') {
        // 要介護度取得
        const careLevel = parseInt(resData.data.yokaiKbn)
        if (careLevel < 11) {
          // 実施計画～②権限無しの場合、処理終了。
          if (userAuth.value.implementationPlan2Authority === '0') {
            setEvent({ createNewFlg: true })
            return
          }
        } else {
          // 以外の場合
          // 実施計画～③権限無しの場合、処理終了。
          if (userAuth.value.implementationPlan3Authority === '0') {
            setEvent({ createNewFlg: true })
            return
          }
        }
        // ［実施計画書取込］画面データを取得する。
        await getImplementationPlanImportInfo()
      }
      // アセスメント方式が5：パッケージプラン以外の場合
      else if (routerMasterInfo.value.cpnFlg !== '5') {
        // 計画書（2）権限無しの場合、処理終了。
        if (userAuth.value.planAuthority === '0') {
          setEvent({ createNewFlg: true })
          return
        }
        await getCarePlan2ImportInfo()
      }
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 実施計画書取込情報取得
 */
const getImplementationPlanImportInfo = async () => {
  try {
    const inputData: EvaluationTableImplementationPlanImportSelectInEntity = {
      svJigyoId: officeInfo.value.svJigyoId,
      userId: userId.value,
      // 基準日
      kijunbiYmd: systemCommonsStore.getSystemDate ?? '',
      shokuinId: systemCommonsStore.getStaffId ?? '',
    }

    // 実施計画書取込情報取得APIを呼び出す
    const resData: EvaluationTableImplementationPlanImportSelectOutEntity =
      await ScreenRepository.select('evaluationTableImplementationPlanImportSelect', inputData)
    if (resData.data) {
      // 共通情報に設定する
      setCommonInfo({
        implementationPlanImportInfoList: resData.data.implementationPlanImportInfoList,
      })
      setEvent({ createNewFlg: true })
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 計画書（2）取込情報取得
 */
const getCarePlan2ImportInfo = async () => {
  try {
    const inputData: EvaluationTableCarePlan2ImportSelectInEntity = {
      sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
      svJigyoId: officeInfo.value.svJigyoId,
      userId: userId.value,
      shisetuId: officeInfo.value.shisetuId,
      kikanKanriFlg: plannningPeriodManageFlg.value,
      // 親画面.計画書（２）内容が空白は罫線を省略 ToDo事項<2025/06/29> 取得不可
      blankOmittedFlg: '',
    }

    // 計画書（2）取込情報取得を呼び出す
    const resData: EvaluationTableCarePlan2ImportSelectOutEntity = await ScreenRepository.select(
      'evaluationTableCarePlan2ImportSelect',
      inputData
    )
    if (resData.data) {
      setCommonInfo({ carePlan2ImportInfoList: resData.data.carePlan2ImportInfoList })
      setEvent({ createNewFlg: true })
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 計画期間変更情報取得
 *
 * @param PlanTargetPeriodUpdateFlg - 変更情報
 */
const getPlanPeroidChangeInfo = async (PlanTargetPeriodUpdateFlg: string): Promise<boolean> => {
  try {
    // 計画期間IDチェック
    const inputData: EvaluationTablePlanPeriodSelectInEntity = {
      svJigyoId: officeInfo.value.svJigyoId,
      userId: userId.value,
      syubetsuId: userAuth.value.syubetsuId,
      shisetuId: officeInfo.value.shisetuId,
      sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
      planPeriodPageCategory: PlanTargetPeriodUpdateFlg,
      kikanKanriFlg: plannningPeriodManageFlg.value,
      shokuinId: _staffId ?? '',
      cpnFlg: routerMasterInfo.value.cpnFlg,
      shosikiFlg: routerMasterInfo.value.shosikiFlg,
      menu2Name: '[mnu2][3GK]ﾓﾆﾀﾘﾝｸﾞ',
      menu3Name: '[mnu3][3GK]新評価表',
      freeParameter: 'null',
      svJigyoIdList: (systemCommonsStore.getSvJigyoIdList as string[]).join(',') ?? [].join(','),
      gsyscd: systemCommonsStore.getSystemCode ?? '',
      cmoni1Id: 'NULL',
      processCategory: '1',
    }

    const resData: EvaluationTablePlanPeriodSelectOutEntity = await ScreenRepository.select(
      'evaluationTablePlanPeriodSelect',
      inputData
    )

    if (resData.data) {
      const {
        syubetsuId,
        caseAuthority,
        implementationPlan1Authority,
        implementationPlan2Authority,
        implementationPlan3Authority,
        planAuthority,
        historyInfo,
        planPeriodInfo,
        kikanFlg,
      } = resData.data
      // 各権限を取得する
      userAuth.value = {
        syubetsuId,
        caseAuthority,
        implementationPlan1Authority,
        implementationPlan2Authority,
        implementationPlan3Authority,
        planAuthority,
      }
      localOneway.or13844Oneway.plainningPeriodManageFlg = kikanFlg
      plannningPeriodManageFlg.value = kikanFlg
      if (plannningPeriodManageFlg.value === TeX0013Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
        if (planPeriodInfo.sc1Id !== '0') {
          localOneway.or13844Oneway.planningPeriodInfo = {
            id: planPeriodInfo.sc1Id ?? '',
            periodCnt: planPeriodInfo.periodCnt ?? '0',
            periodNo: planPeriodInfo.periodNo ?? '0',
            period: `${planPeriodInfo.startYmd}${SPACE_WAVE}${planPeriodInfo.endYmd}`,
          }
        } else {
          localOneway.or13844Oneway.planningPeriodInfo = {
            id: '',
            period: '',
            periodNo: '0',
            periodCnt: '0',
          }
        }
      } else {
        localOneway.or13844Oneway.planningPeriodInfo = {
          id: '',
          period: '',
          periodNo: '0',
          periodCnt: '0',
        }
      }
      if (historyInfo.krirekiCnt !== '0') {
        createUserId.value = historyInfo.shokuId ?? ''
        localOneway.or13872Oneway.historyInfo = {
          krirekiCnt: historyInfo.krirekiCnt ?? '',
          krirekiNo: historyInfo.krirekiNo ?? '',
          rirekiId: historyInfo.cmoni1Id ?? '',
        }
        local.orX0157.value = historyInfo.shokuinKnj ?? ''
        local.createDate.value = historyInfo.createYmd!
        // 作成日設定
        // 共通情報設定
        setCommonInfo({
          masterHeadId: historyInfo.cmoni1Id,
          sc1Id: historyInfo.sc1Id,
          caseAuth: caseAuthority,
          planBookAuth: planAuthority,
          planPeriodFlg: kikanFlg,
          omittedBlankBorderFlg: routerMasterInfo.value.cmoniKeisen1Flg,
          omittedSameBorderFlg: routerMasterInfo.value.cmoniKeisen2Flg,
          cmoni1Id: historyInfo.cmoni1Id,
          createYmd: historyInfo.createYmd,
          shokuId: historyInfo.shokuId,
        })
        return true
      }
      // 履歴情報=NULL
      else {
        // ・作成日 ＝ 共通情報.作成日
        local.createDate.value = systemCommonsStore.getSystemDate ?? ''
        // ・作成者 ＝ ログイン情報.職員名
        local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
        createUserId.value = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
        // ・履歴-ページング = "1 / 1"
        localOneway.or13872Oneway.historyInfo = {
          rirekiId: '',
          krirekiNo: '0',
          krirekiCnt: '0',
        }
        // 共通情報を設定する
        setCommonInfo({
          sc1Id: planPeriodInfo.sc1Id,
          cmoni1Id: '0',
          // createUserId: systemCommonsStore.getCurrentUser.chkShokuId,
          // createUserName: systemCommonsStore.getCurrentUser.shokuinKnj,
          createYmd: systemCommonsStore.getSystemDate,
          // updateKbn: UPDATE_KBN.CREATE,
          // historyUpdateKbn: UPDATE_KBN.CREATE,
          // rrkNo: '1',
          // sc1Number: planPeriodInfo.periodNo,
          masterHeadId: '0',
          caseAuth: caseAuthority,
          planBookAuth: planAuthority,
          planPeriodFlg: kikanFlg,
          omittedBlankBorderFlg: routerMasterInfo.value.cmoniKeisen1Flg,
          omittedSameBorderFlg: routerMasterInfo.value.cmoniKeisen2Flg,
          shokuId: historyInfo.shokuId,
        })
        await evaluationTableNewSelect()
        return false
      }
    } else {
      return false
    }
  } catch (e) {
    console.log(e)
    return false
  }
}

// /**
//  * 歴史変更処理
//  *
//  * @param historyInfo - 歴史情報
//  */
// const _historyChange = async (historyInfo: { createId: number; createUpateFlg: string }) => {
//   console.log(historyInfo)
//   if (historyInfo) {
//     const result = await checkScreenUpdate()
//     // 返却値がundefinedの場合は処理を中断する
//     if (!result) return
//     const { dialogResult, registAuth } = result
//     isLoading.value = true
//     // 画面データ変更しないの場合
//     try {
//       if (!dialogResult) {
//         await getHistoryChangeInfo(historyInfo)
//         await getInitDataInfo()
//         return
//       }
//       // データ変更する場合、ダイアログ返却値ごとに処理を行う
//       if (dialogResult === TeX0013Const.DEFAULT.DIALOG_RESULT_CONFIRM) {
//         if (registAuth) {
//           await userSave()
//         }
//         await getHistoryChangeInfo(historyInfo)
//         await getInitDataInfo()
//         return
//       }
//       if (dialogResult === TeX0013Const.DEFAULT.DIALOG_RESULT_NO) {
//         if (!registAuth) {
//           return
//         }
//         await getHistoryChangeInfo(historyInfo)
//         await getInitDataInfo()
//       }
//     } finally {
//       // どんな条件も実行する
//       isLoading.value = false
//     }
//   }
// }

/**
 * 履歴ID指定処理
 *
 * @param historyId - 歴史情報
 */
const historyIdSelect = async (historyId: string) => {
  try {
    // 歴史ID取得
    // const historyId =
    //   historyInfo.createUpateFlg === '0'
    //     ? historyInfo.createId.toString()
    //     : localOneway.hisotrySelectOneway.createData.createId
    // インプットデータ作成
    const inputData: EvaluationTableHistorySelectInEntity = {
      svJigyoId: officeInfo.value.svJigyoCd,
      userId: userId.value,
      sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
      cmoni1Id: historyId,
      histPageCategory: '0',
    }
    // 歴史情報取得APIを呼び出す
    const resData: EvaluationTableHistorySelectOutEntity = await ScreenRepository.select(
      'evaluationTableHistorySelect',
      inputData
    )
    if (resData.data) {
      const { historyInfo } = resData.data
      // 履歴リストが0件以外
      if (historyInfo.cmoni1Id !== '0' && historyInfo.krirekiCnt !== '0') {
        // 歴史詳細を保存
        localOneway.or13872Oneway.historyInfo = {
          rirekiId: historyInfo.cmoni1Id,
          krirekiNo: historyInfo.krirekiNo ?? '0',
          krirekiCnt: historyInfo.krirekiCnt ?? '0',
        }
        // 作成日保存
        local.createDate.value = historyInfo.createYmd ?? systemCommonsStore.getSysDate ?? ''
        // 作成者詳細保存
        local.orX0157.value = historyInfo.shokuinKnj ?? ''
        // 作成者ID
        _staffId = historyInfo.shokuId ?? ''
        // 共通情報設定
        setCommonInfo({
          masterHeadId: historyInfo.cmoni1Id,
          sc1Id: historyInfo.sc1Id,
          omittedBlankBorderFlg: routerMasterInfo.value.cmoniKeisen1Flg,
          omittedSameBorderFlg: routerMasterInfo.value.cmoniKeisen2Flg,
          cmoni1Id: historyInfo.cmoni1Id,
          createYmd: historyInfo.createYmd,
          shokuId: historyInfo.shokuId,
        })
        return true
      } else {
        localOneway.or13872Oneway.historyInfo = {
          rirekiId: '0',
          krirekiNo: '0',
          krirekiCnt: '0',
        }
        // 共通情報設定
        setCommonInfo({
          masterHeadId: '0',
          omittedBlankBorderFlg: routerMasterInfo.value.cmoniKeisen1Flg,
          omittedSameBorderFlg: routerMasterInfo.value.cmoniKeisen2Flg,
          cmoni1Id: '0',
          createYmd: historyInfo.createYmd,
          shokuId: historyInfo.shokuId,
        })
        return false
      }
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 保存完了後処理タイプ
 */
const checkAfterProcessType = async () => {
  saveCompletedStateFlg.value = false
  switch (afterProcess.type) {
    case TeX0013Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.HISTORY_ID_SELECT: {
      // 返回情報.ヘーダIDが「0」以外の場合
      // 上記以外の場合
      // AC005、新規処理を行う
      if (afterProcess.value !== '0') {
        await historyIdSelect(afterProcess.value)
      } else {
        await getCreateNewInfo()
      }
      break
    }
  }

  afterProcess.type = ''
  afterProcess.value = ''
  screenUpadateKbn.value = ''
}

/**
 * 複写データ取得
 *
 * @param copyInfo - 複写画面返却値
 */
const getCopyDataInfo = async (copyInfo: Or51548ConfirmType) => {
  const inputData: EvaluationTableDuplicateSelectInEntity = {
    cmoni1Id: copyInfo.headId,
    kbnId: '-1',
  }

  const resData: EvaluationTableDuplicateSelectOutEntity = await ScreenRepository.select(
    'evaluationTableDuplicateSelect',
    inputData
  )

  if (resData.data) {
    // 返回情報.下段選択区分 = 1：チェックオンの場合
    // パネル項目1チェック
    if (copyInfo.screenInfo.remarks_1Info === '1') {
      localOneway.or35345Oneway.textAreaInfo[0].textareaValue.value =
        resData.data.evaluationTableLowerTierInfo.koumoku01Knj
    }
    if (copyInfo.screenInfo.remarks_2Info === '1') {
      localOneway.or35345Oneway.textAreaInfo[1].textareaValue.value =
        resData.data.evaluationTableLowerTierInfo.koumoku01Knj
    }
    if (copyInfo.screenInfo.remarks_3Info === '1') {
      localOneway.or35345Oneway.textAreaInfo[2].textareaValue.value =
        resData.data.evaluationTableLowerTierInfo.koumoku01Knj
    }
    setCommonInfo({
      copyData: resData.data.evaluationTableUpperTierInfoList,
      copyMode: copyInfo.copyMode,
    })
    setEvent({ copyEventFlg: true })
  }
}

/**
 * 画面イベント発火
 *
 * @param event - 画面イベント
 */
function setEvent(event: TeX0013EventType) {
  setCommonInfo({
    remarksInfoList: localOneway.or35345Oneway.textAreaInfo,
    sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
    userId: userId.value,
  })
  /**
   * 共通情報設定
   */
  TeX0013Logic.event.set({
    uniqueCpId: props.uniqueCpId,
    events: {
      /** 再表示発火フラグ */
      isRefresh: event.isRefresh,
      /** 保存フラグ */
      saveEvent: event.saveEvent,
      /** 新規発火フラグ */
      createNewFlg: event.createNewFlg,
      /** 削除発火フラグ */
      deleteFlg: event.deleteFlg,
      /** 複写発火フラグ */
      copyEventFlg: event.copyEventFlg,
    },
  })
}

/**
 * 再アセスメントの必要データ取得
 *
 * @param checkValue - 再アセスメントの必要
 *
 * @param yoteiYmd - 予定日
 */
const getReAssessment = (checkValue: string, yoteiYmd: string) => {
  setCommonInfo({ reAssessment: checkValue, yoteiYmd })
}

/**
 * 画面イベント発火
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * ダイアログを呼び出す
 *
 * @param dialogText - ダイアログ内容
 */
const setShowDialog = (dialogText: string) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: dialogText,
    },
  })
}

/**
 * エラーダイアログを呼び出す
 *
 * @param dialogText - ダイアログ内容
 */
const setShowErrorDialog = (dialogText: string) => {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: dialogText,
    },
  })
}

/**
 * 新規APIを呼び出す
 */
const evaluationTableNewSelect = async () => {
  // 新規ボタン押下回数を増やす
  createNewBtnClickNum.value += 1
  // 画面情報を下記に設定する。
  // 作成日 ＝ 親画面.基準日
  local.createDate.value = systemCommonsStore.getSystemDate ?? ''
  // 作成者 ＝ ログイン情報.職員名
  local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
  _staffId = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
  // 履歴-ページング = 履歴の最大値+1/履歴の最大値+1
  const currentNo = parseInt(localOneway.or13872Oneway.historyInfo?.krirekiNo ?? '0')
  localOneway.or13872Oneway.historyInfo!.krirekiCnt = (currentNo + 1).toString()
  localOneway.or13872Oneway.historyInfo!.krirekiNo = (currentNo + 1).toString()
  // 更新区分 = "C"（新規）
  screenUpadateKbn.value = UPDATE_KBN.CREATE
  // 評価表一覧と総括・計画の変更等パネルの入力フォーム項目：空白

  // 新規APIを呼び出す
  await getCreateNewInfo()
}
/**
 * Or21814_2ダイアログを呼び出す、返却値を取得する
 *
 * @param dialogText -ダイアログ内容
 */
const getOr21814_2DialogResult = (dialogText: string): Promise<'yes' | 'no'> => {
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: dialogText,
    },
  })
  // 閉じるタイミングで監視を実行
  return new Promise((resolve) => {
    let result: 'yes' | 'no'
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (!newValue) {
          const event = Or21814Logic.event.get(or21814_2.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            result = TeX0013Const.DEFAULT.DIALOG_RESULT_CONFIRM
          }
          if (event?.secondBtnClickFlg) {
            result = TeX0013Const.DEFAULT.DIALOG_RESULT_NO
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814_2.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              closeBtnClickFlg: false,
            },
          })
          resolve(result)
        }
      },
      { once: true }
    )
  })
}

/**
 * Or21814_2ダイアログを呼び出す、返却値を取得する
 *
 * @param dialogText -ダイアログ内容
 */
function getOr21814_3DialogResult(dialogText: string): Promise<'yes' | 'no' | 'cancel'> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: dialogText,
    },
  })
  // 閉じるタイミングで監視を実行
  return new Promise((resolve) => {
    let result: 'yes' | 'no' | 'cancel'
    watch(
      () => Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (!newValue) {
          const event = Or21814Logic.event.get(or21814_3.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            result = TeX0013Const.DEFAULT.DIALOG_RESULT_CONFIRM
          }
          if (event?.secondBtnClickFlg) {
            result = TeX0013Const.DEFAULT.DIALOG_RESULT_NO
          }
          if (event?.closeBtnClickFlg) {
            result = TeX0013Const.DEFAULT.DIALOG_RESULT_NONE
          }
          if (event?.thirdBtnClickFlg) {
            result = TeX0013Const.DEFAULT.DIALOG_RESULT_NONE
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814_3.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              closeBtnClickFlg: false,
              thirdBtnClickFlg: false,
            },
          })
          resolve(result)
        }
      },
      { once: true }
    )
  })
}

/**
 * 評価表マスタ画面を呼び出す
 */
const setShowEvaluationTableMasterDialog = () => {
  Or53098Logic.state.set({
    uniqueCpId: or53098.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 削除処理処理
 */
const userDelelte = async () => {
  // 画面の更新区分 = D:削除の場合、計画期間件数が0の場合 処理終了
  if (
    screenUpadateKbn.value === UPDATE_KBN.DELETE ||
    !localOneway.or13844Oneway.planningPeriodInfo?.periodCnt
  ) {
    return
  }
  const result = await getOr21814_2DialogResult(
    t('message.i-cmn-11326', [local.createDate.value, t('label.evaluation-table')])
  )
  if (result === 'yes') {
    setEvent({ deleteFlg: true })
    screenUpadateKbn.value = UPDATE_KBN.DELETE
    // 以下の項目を非活性にする。
    // 作成者選択アイコンボタン、作成日、作成日カレンダ
    // localOneway.orX0009.isDisabled = true
    localOneway.createDateOneway.disabled = true
  }
}

/**
 * 画面情報再取得
 */
async function reload() {
  isLoading.value = true
  await getPlanPeroidInfo('0')
  if (localOneway.or13844Oneway.planningPeriodInfo?.periodCnt === '0') {
    isLoading.value = false
    return
  }
  await getInitDataInfo()
  isLoading.value = false
  setEvent({ isRefresh: true })
}

/**
 * 複写画面を呼び出す
 */
const setShowOverwriteDialog = () => {
  // 画面の更新区分 = D:削除の場合
  // 期間管理フラグが「1:期間管理する」、かつ、計画対象期間情報がない場合
  // 処理終了
  if (
    screenUpadateKbn.value === UPDATE_KBN.DELETE ||
    (plannningPeriodManageFlg.value === '1' &&
      localOneway.or13844Oneway.planningPeriodInfo?.periodCnt === '0')
  ) {
    return
  }
  componentsKey.value = new Date().getTime().toString()
  // onewayバインド設定
  localOneway.or51548Oneway = {
    userId: userId.value,
    sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
    shisetsuId: officeInfo.value.shisetuId,
    masterId: '-1',
    planPeriodFlg: plannningPeriodManageFlg.value,
    headId: localOneway.or13872Oneway?.historyInfo?.rirekiId ?? '',
  }
  Or51548Logic.state.set({
    uniqueCpId: or51548.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * Or13844イベント変更
 *
 * @param event - イベント
 */
const setOr13844Event = (event: Or13844EventType) => {
  Or13844Logic.event.set({
    uniqueCpId: or13844.value.uniqueCpId,
    events: event,
  })
}

/**
 * 複写データ取得
 *
 * @param copyData - 複写データ
 */
const getCopyData = async (copyData: Or51548ConfirmType) => {
  isLoading.value = true
  await getCopyDataInfo(copyData)
  isLoading.value = false
}

/**
 * 計画対象期間選択開くボタンクリック
 *
 */
const planningPeriodSelectClick = () => {
  // Oneway設定
  localOneway.orX0115Oneway = {
    kindId: userAuth.value.syubetsuId,
    sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
  }
  // 計画対象期間選択画面を開く
  OrX0115Logic.state.set({
    uniqueCpId: orX0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 初期化処理
 */
onMounted(() => {
  if (isInit) {
    initControl()
  }
})

/**
 * 画面イベント監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    console.log(newValue)
    if (!newValue) return

    if (newValue.saveEventFlg) {
      setEvent({ saveEvent: true })
      afterProcess.type = TeX0013Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.HISTORY_ID_SELECT
      await userSave()
      setOr11871Event({ saveEventFlg: false })
    }
    if (newValue.createEventFlg) {
      isLoading.value = true
      await beforeCreateNewInfo()
      isLoading.value = false
      setOr11871Event({ createEventFlg: false })
    }
    if (newValue.masterEventFlg) {
      setShowEvaluationTableMasterDialog()
      setOr11871Event({ masterEventFlg: false })
    }
    if (newValue.deleteEventFlg) {
      await userDelelte()
      setOr11871Event({ deleteEventFlg: false })
    }
  }
)

/**
 * Or13844期間変更ボタンクリック監視処理
 */
watch(
  () => Or13844Logic.event.get(or13844.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.nextBtnClickFlg === false && newValue.preBtnClickFlg === false) return
    // 1件目の計画対象期間データが表示されている状態
    if (
      newValue?.preBtnClickFlg &&
      localOneway.or13844Oneway.planningPeriodInfo?.periodNo ===
        TeX0013Const.DEFAULT.BEFORE_PAGE_CHANGE_RESULT
    ) {
      setShowDialog(t('message.i-cmn-11262'))
      setOr13844Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
      return
    }
    // 最終件目の計画対象期間データが表示されている状態
    if (
      newValue?.nextBtnClickFlg &&
      localOneway.or13844Oneway.planningPeriodInfo?.periodNo ===
        localOneway.or13844Oneway.planningPeriodInfo?.periodCnt
    ) {
      setShowDialog(t('message.i-cmn-11263'))
      setOr13844Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
      return
    }
    const pageFlg = newValue?.nextBtnClickFlg ? '2' : '1'
    const result = await getDataIsChanged()
    switch (result) {
      case TeX0013Const.DEFAULT.IS_EDIT_NO_CHANGE:
        await getPlanPeroidChangeInfo(pageFlg)
        await getInitDataInfo()
        setEvent({ isRefresh: true })
        break
      case TeX0013Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
        await getPlanPeroidChangeInfo(pageFlg)
        await getInitDataInfo()
        setEvent({ isRefresh: true })
        break
      case TeX0013Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
        await userSave()
        // 保存後タイプ一時保存する
        afterProcess.type = TeX0013Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.PLAN_PERIOD_CHANGE
        afterProcess.value = pageFlg
        break
    }
    setOr13844Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
  }
)

// 事業所プルダウン変更監視
watch(
  () => officeInfo.value,
  async (newValue, oldValue) => {
    if (!oldValue || !newValue) return

    const commonInfo = TeX0013Logic.data.get(props.uniqueCpId)

    // 新しいデータと共通情報が一致する場合、処理を中断する
    if (commonInfo?.svJigyoId === newValue.svJigyoId) return

    switch (await getDataIsChanged()) {
      case TeX0013Const.DEFAULT.IS_EDIT_NO_CHANGE:
        // 画面変更なしの場合
        // 初期化処理を行う
        setCommonInfo({
          houjinId: newValue.houjinId,
          shisetuId: newValue.shisetuId,
          svJigyoId: newValue.svJigyoId,
        })
        await reload()
        break
      case TeX0013Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
        // 入力内容を廃棄し、初期化処理を行う
        setCommonInfo({
          houjinId: newValue.houjinId,
          shisetuId: newValue.shisetuId,
          svJigyoId: newValue.svJigyoId,
        })
        await reload()
        break
      case TeX0013Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
        // 入力内容を保存する、初期化処理を行う
        await userSave()
        // 保存後処理タイプを一時保存する
        afterProcess.type = TeX0013Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.OFFICE_CHANGE
        break
      case TeX0013Const.DEFAULT.IS_EDIT_CHANGE_CLOSE_DIALOG:
        // 処理を中断する、前の事業所に戻る
        Or41179Logic.data.set({
          uniqueCpId: or41179.value.uniqueCpId,
          value: {
            modelValue: commonInfo?.svJigyoId,
          },
        })
    }
  },
  { deep: true }
)

/**
 * OrX0115期間ID変更監視
 */
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  async (newValue) => {
    console.log(newValue)
    // 期間IDと現在使用したID一致する場合、終了
    if (newValue?.kikanId === localOneway.or13844Oneway.planningPeriodInfo?.id) return
    // await getPlanPeroidChangeInfo({ planTargetPeriodId: newValue?.kikanId })
    await getInitDataInfo()
    setEvent({ isRefresh: true })
  }
)

/**
 * 保存API実施完了フラグ変更検知
 */
watch(
  () => saveCompletedStateFlg.value,
  async (newValue) => {
    if (newValue === false) return
    await checkAfterProcessType()
  },
  { deep: true }
)
</script>

<template>
  <c-v-sheet class="common-layout">
    <v-overlay
      :model-value="isLoading"
      :persistent="true"
      class="align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      >
      </v-progress-circular
    ></v-overlay>
    <!-- ヘーダー 操作ボタンエリア -->
    <div class="sticky-header">
      <g-base-or11871 v-bind="or11871">
        <template #optionMenuItems>
          <c-v-list-item
            :title="t('btn.log')"
            prepend-icon="file_upload"
          >
          </c-v-list-item>
        </template>
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            prepend-icon="file_copy"
            @click="setShowOverwriteDialog"
          />
        </template>
      </g-base-or11871>
    </div>
    <!-- アクションエリア -->
    <c-v-row
      no-gutters
      class="main-Content d-flex flex-0-1 h-100"
    >
      <!-- ナビゲーションエリア -->
      <c-v-col
        cols="auto"
        class="hidden-scroll h-100 user-select"
      >
        <!-- 利用者選択一覧 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <c-v-col class="main-right d-flex flex-column overflow-hidden h-100">
        <c-v-row
          no-gutters
          class="d-flex"
        >
          <!-- 事業所選択 -->
          <c-v-col
            cols="auto ml-6 office-select align-self-end"
            style="width: 220px"
          >
            <g-base-or41179 v-bind="or41179" />
          </c-v-col>
          <!-- 計画管理詳細 -->
          <c-v-col cols="auto ml-6 align-self-end">
            <g-custom-or-13844
              v-bind="or13844"
              :oneway-model-value="localOneway.or13844Oneway"
              @open-btn-click="planningPeriodSelectClick"
            />
          </c-v-col>
          <!-- 作成日 -->
          <c-v-col
            cols="auto ml-6 align-self-end create-ymd-select"
            style="width: 200px"
          >
            <base-mo00020
              v-model="local.createDate"
              :oneway-model-value="localOneway.createDateOneway"
            />
          </c-v-col>
          <!-- 作成者 -->
          <c-v-col cols="auto ml-6 align-self-end create-user-select">
            <g-custom-orX0157
              v-model="local.orX0157"
              :oneway-model-value="localOneway.orX0157Oneway"
            />
          </c-v-col>
          <!-- 歴史 -->
          <c-v-col cols="auto ml-6 align-self-end">
            <g-custom-or-13872
              v-bind="or13872"
              :oneway-model-value="localOneway.or13872Oneway"
            />
          </c-v-col>
        </c-v-row>
        <div class="evaluation-table-diver mt-6 mb-4"></div>
        <!-- コンテンツエリア -->
        <c-v-row
          class="middleContent ml-6"
          no-gutters
        >
          <g-custom-or54951
            v-if="
              plannningPeriodManageFlg === '1' &&
              localOneway.or13844Oneway.planningPeriodInfo?.periodCnt
            "
            ref="or54951Ref"
            v-bind="or54951"
            :oneway-model-value="localOneway.or54951Oneway"
          />
        </c-v-row>
        <!-- 線 -->
        <div class="evaluation-table-diver mt-6 mb-4"></div>
        <!-- 備考 -->
        <div class="ml-6 pb-2">
          <g-custom-or35345
            v-if="
              screenUpadateKbn !== UPDATE_KBN.DELETE ||
              (plannningPeriodManageFlg === '1' &&
                localOneway.or13844Oneway.planningPeriodInfo?.periodCnt)
            "
            v-bind="or35345"
            :oneway-model-value="localOneway.or35345Oneway"
            @update:checked="getReAssessment"
          />
        </div>
      </c-v-col>
    </c-v-row>
    <g-base-or21814
      v-if="showDialogOr21814_1"
      v-bind="or21814_1"
    />
    <!-- はい、いいえボタンダイアログ -->
    <g-base-or21814
      v-if="showDialogOr21814_2"
      v-bind="or21814_2"
    />
    <!-- はい、いいえ、キャンセルボタンダイアログ -->
    <g-base-or21814
      v-if="showDialogOr21814_3"
      v-bind="or21814_3"
    />
    <!-- 評価表マスタ -->
    <g-custom-or-53098
      v-if="showDialogOr53098"
      v-bind="or53098"
      :oneway-model-value="localOneway.or53098Oneway"
    />
    <!-- 評価表複写画面 -->
    <g-custom-or-51548
      v-if="showOverwriteDialog"
      v-bind="or51548"
      :key="componentsKey"
      :oneway-model-value="localOneway.or51548Oneway"
      @confirm="getCopyData"
    />
    <!-- 計画期間選択画面 -->
    <g-custom-or-x-0115
      v-if="showPlanPeriodDialog"
      v-bind="orX0115"
      :oneway-model-value="localOneway.orX0115Oneway"
    />
    <!-- エラーダイアログ -->
    <g-base-or21813
      v-if="showErrorDialog"
      v-bind="or21813"
    />
  </c-v-sheet>
</template>
<style lang="scss" scoped>
// 利用者一覧選択領域領域
:deep(.user-select > .v-card) {
  padding: 0px 0px 0px 0px !important; // パディング設定
  width: max-content !important; // 幅を自動調整
  margin-top: 16px !important; // 上部マージン
  // パンディング設定
  .v-card-actions {
    width: max-content !important; // 幅を自動調整
    padding: 8px !important; // パディング設定
  }
  // 選択領域の幅設定
  .user-info-section-container-style {
    width: 260px; // 幅を260pxに設定
  }
}

.common-layout {
  // 背景色：テーマの背景色を使用
  background-color: rgb(var(--v-theme-background)) !important;
  // 幅：内容で広く
  min-height: max-content !important;
  display: flex;
  flex-direction: column;
  // 画面トップの固定ヘッダー
  .sticky-header {
    position: sticky; // スクロール時に固定
    top: 48px; // トップからの距離
    z-index: 999; // 他の要素よりも上に表示
    background: rgb(var(--v-theme-background)); // テーマの背景色
    width: 100%; // 全幅
  }
}

.createDateOuterClass {
  width: auto !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }

  :deep(.v-input__details) {
    display: none;
  }
  :deep(.v-input__control) {
    background-color: #fff;
  }
}

.evaluation-table-diver {
  border-bottom: thin solid rgba(180, 197, 220, 1);
}
.create-ymd-select {
  width: 140px !important;
  :deep(.v-input) {
    width: 130px !important;
  }
}
.create-user-select {
  :deep(.v-input) {
    width: 150px !important;
  }
}
</style>
