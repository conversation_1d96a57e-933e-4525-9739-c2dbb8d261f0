import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 取得入力エンティティ
 */
export interface PlanDuplicateInitialInfoSelectInEntity extends InWebEntity {
  /** 画面名称 */
  shoriName?: string
  /** 機能ID */
  kinouId?: string
  /** システムコード */
  gsysCd?: string
  /** 事業所CD */
  svJigyoCd?: string
  /** ライセンス配列 */
  glLicense?: string[]
  /** 職員ID */
  shokuinId: string
  /** 引継画面名 */
  kinouName?: string
  /** 複写元事業者ID */
  shienId: string
  /** 複写元年月 */
  fromYm: string
  /** 担当ケアマネID */
  tantoId?: string
  /** 50音行番号 */
  gojuuOnRowNo?: string
  /** 50音母音 */
  gojuuOnKana?: string
  /** 事業所ID */
  svJigyoId: string
  /** 施設ID */
  shisetuId?: string
  /** システム年月日 */
  appYmd?: string
  /** 利用者ID配列 */
  userids?: string[]
}

/**
 * 取得出力エンティティ
 */
export interface PlanDuplicateInitialInfoSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: PlanDuplicateInitialInfoSelectDataEntity
}

/**
 * 取得データエンティティ
 */
export interface PlanDuplicateInitialInfoSelectDataEntity {
  /** 利用可能フラグ */
  kbnFlg: string
  /** タブータイトル */
  tabTitle: string[]
  /** タブー選択区分 */
  selKbnFlg: string
  /** 複写方法 */
  copyMode: string
  /** 複写方法_更新回数 */
  copyMode_modifiedCnt: string
  /** 複写方法保険外 */
  copyModeGai: string
  /** 複写方法保険外_更新回数 */
  copyModeGai_modifiedCnt: string
  /** 短期入所複写方法 */
  copyTnki: string
  /** 短期入所複写方法_更新回数 */
  copyTnki_modifiedCnt: string
  /** 複写先利用票状況 */
  copyJokyo: string
  /** 複写先利用票状況_更新回数 */
  copyJokyo_modifiedCnt: string
  /** 複写先保険外ｻｰﾋﾞｽ状況 */
  copyJokyoGai: string
  /** 複写先保険外ｻｰﾋﾞｽ状況_更新回数 */
  copyJokyoGai_modifiedCnt: string
  /** 福祉用具単位数 */
  copyFygTani: string
  /** 福祉用具単位数_更新回数 */
  copyFygTani_modifiedCnt: string
  /** 複写先開始年月 */
  startYm: string
  /** 複写先終了年月 */
  endYm: string
  /** 職員基本情報リスト */
  shokuList: ShokuInfo[]
  /** 複写元休日情報 */
  fromHolidayList: FromHolidayInfo[]
  /** 複写先休日情報 */
  toHolidayList: ToHolidayInfo[]
  /** 期間管理フラグ */
  kikanFlg: string
  /** 複写先年月リスト */
  toYm: string[]
  /** 利用票利用者リスト */
  riyouUserList: UserInfo[]
  /** 保険外利用者リスト */
  hokengaiUserList: UserInfo[]
  /** 計画書等利用者リスト */
  keikakushoUserList: KeikakushoUserInfo[]
  /** 期間複写元リスト */
  kikanCopyFromList: KikanCopyFromInfo[]
  /** 期間複写先リスト */
  kikanCopyToList: KikanCopyToInfo[]
  /** 事業所複写元リスト */
  jigyoCopyFromList: JigyoCopyFromInfo[]
  /** 事業所複写先リスト */
  jigyoCopyToList: JigyoCopyToInfo[]
}

/**
 * 職員基本情報
 */
export interface ShokuInfo {
  /** 職員ID */
  chkShokuId: string
  /** 職員名（姓） */
  shokuin1Knj: string
  /** 職員名（名） */
  shokuin2Knj: string
}

/**
 * 複写元休日情報
 */
export interface FromHolidayInfo {
  /** 日 */
  fromDay: string
  /** 曜日 */
  fromYoubi: string
  /** 稼働フラグ */
  fromHoliday: string
  /** 説明 */
  fromSetsumei: string
  /** 指定事業者休日判定 */
  fromRest: string
}

/**
 * 複写先休日情報
 */
export interface ToHolidayInfo {
  /** 日 */
  toDay: string
  /** 曜日 */
  toYoubi: string
  /** 稼働フラグ */
  toHoliday: string
  /** 説明 */
  toSetsumei: string
  /** 指定事業者休日判定 */
  toRest: string
}

/**
 * 利用者情報
 */
export interface UserInfo {
  /** 選択 */
  sel: string
  /** フリガナ（姓） */
  name1Kana: string
  /** フリガナ（名） */
  name2Kana: string
  /** 氏名（姓） */
  name1Knj: string
  /** 氏名（名） */
  name2Knj: string
  /** 利用者ID */
  id: string
  /** 利用者番号 */
  selfId: string
  /** 同名識別子 */
  douseiKnj: string
  /** 性別 */
  sex: string
  /** 電話番号 */
  tel: string
  /** 携帯番号 */
  keitaitel: string
  /** 要介護度 */
  yokaiKnj: string
  /** 認定有効開始日 */
  startYmd: string
  /** 認定終了日 */
  endYmd: string
  /** 担当ケアマネID */
  tantoId: string
  /** 作成状況０ */
  plan0: string
  /** 作成状況１ */
  plan1: string
  /** 作成状況２ */
  plan2: string
  /** 作成状況３ */
  plan3: string
  /** 作成状況４ */
  plan4: string
  /** 作成状況５ */
  plan5: string
  /** 作成状況６ */
  plan6: string
  /** 作成状況７ */
  plan7: string
  /** 作成状況８ */
  plan8: string
  /** 作成状況９ */
  plan9: string
  /** 作成状況１０ */
  plan10: string
  /** 作成状況１１ */
  plan11: string
  /** 作成状況１２ */
  plan12: string
  /** 作成状況１３ */
  plan13: string
  /** 作成状況１４ */
  plan14: string
  /** 作成状況１５ */
  plan15: string
  /** 作成状況１６ */
  plan16: string
  /** 作成状況１７ */
  plan17: string
  /** 作成状況１８ */
  plan18: string
  /** 作成状況１９ */
  plan19: string
  /** 作成状況２０ */
  plan20: string
  /** 作成状況２１ */
  plan21: string
  /** 作成状況２２ */
  plan22: string
  /** 作成状況２３ */
  plan23: string
  /** 作成状況２４ */
  plan24: string
}

/**
 * 計画書等利用者リスト
 */
export interface KeikakushoUserInfo {
  /** フリガナ（姓） */
  name1Kana: string
  /** フリガナ（名） */
  name2Kana: string
  /** 氏名（姓） */
  name1Knj: string
  /** 氏名（名） */
  name2Knj: string
  /** 利用者ID */
  id: string
  /** 利用者番号 */
  selfId: string
  /** 同名識別子 */
  douseiKnj: string
  /** 性別 */
  sex: string
  /** 生年月日 */
  birthdayYmd: string
  /** 血液型 */
  abo: string
  /** ＲＨ */
  rh: string
  /** 郵便番号 */
  zip: string
  /** 住所 */
  addressKnj: string
  /** 死亡日 */
  deadYmd: string
}

/**
 * 期間複写元リスト
 */
export interface KikanCopyFromInfo {
  /** 期間ID */
  sc1Id: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 事業名 */
  jigyoKnj: string
  /** 利用者ID */
  userId: string
  /** 種別ID */
  syubetsuId: string
  /** 開始日 */
  startYmd: string
  /** 終了日 */
  endYmd: string
  /** 更新回数 */
  modifiedCnt: string
}

/**
 * 期間複写先リスト
 */
export interface KikanCopyToInfo {
  /** 期間ID */
  sc1Id: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 種別ID */
  syubetsuId: string
  /** 開始日 */
  startYmd: string
  /** 終了日 */
  endYmd: string
  /** 更新回数 */
  modifiedCnt: string
}

/**
 * 事業所複写元リスト
 */
export interface JigyoCopyFromInfo {
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 事業名 */
  jigyoKnj: string
  /** 利用者ID */
  userId: string
  /** 種別ID */
  syubetsuId: string
  /** 更新回数 */
  modifiedCnt: string
}

/**
 * 事業所複写先リスト
 */
export interface JigyoCopyToInfo {
  /** サービス事業者ID */
  svJigyoId: string
  /** 事業名 */
  jigyoKnj: string
  /** 更新回数 */
  modifiedCnt: string
}
