<script setup lang="ts">
/**
 * Or30586：有機体：GUI00764_ｱｾｽﾒﾝﾄ(ｲﾝﾀｰﾗｲ)A画面
 *
 * @description
 * アセスメント(インターライ)画面タブA
 *
 * <AUTHOR>
 */
import { onMounted, computed, reactive, ref, watch, defineEmits } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or31000Const } from '~/components/custom-components/organisms/Or31000/Or31000.constants'
import type {
  Or31000StateType,
  Or31000TwoWayType,
} from '~/components/custom-components/organisms/Or31000/Or31000.Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import {
  useCmnCom,
  useSetupChildProps,
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useScreenStore,
} from '#imports'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type {
  BtnStruct,
  Or30980Type,
  Or30980OnewayType,
} from '~/types/cmn/business/components/Or30980Type'
import type { CustomClass } from '~/types/CustomClassType'
import type {
  Or30982Type,
  Or30982BtnInfo,
  Or30982OnewayType,
} from '~/types/cmn/business/components/Or30982Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { Or30980Logic } from '~/components/custom-components/organisms/Or30980/Or30980.logic'
import { Or30980Const } from '~/components/custom-components/organisms/Or30980/Or30980.constants'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type {
  SubInfoAEntity,
  IAssessmentInterRAIAInEntity,
  IAssessmentInterRAIAOutEntity,
  IAssessmentInterRAIAUpdateInEntity,
  IAssessmentInterRAIAUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIAEntity'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type {
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import type { OrX0156OnewayType, OrX0156Type } from '~/types/cmn/business/components/OrX0156Type'
import type { Or30981Type, Or30981OnewayType } from '~/types/cmn/business/components/Or30981Type'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'
import { SUCCESS_STATUS_CODE, UPDATE_KBN } from '~/constants/classification-constants'
import { TeX0003Const } from '~/components/custom-components/template/Tex0003/TeX0003.constants'
import { Or30981Const } from '~/components/custom-components/organisms/Or30981/Or30981.constants'

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}
const props: Props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()

/**
 * 初期読み込みのローディング
 */
const isLoading = ref(false)

/**
 * 子コンポーネント用変数
 */
const or30980 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })
const or30981_1 = ref({ uniqueCpId: '' })
const or30981_2 = ref({ uniqueCpId: '' })
const or30981_3 = ref({ uniqueCpId: '' })
const or30981_4 = ref({ uniqueCpId: '' })
const or30981_5 = ref({ uniqueCpId: '' })
const or30981_6 = ref({ uniqueCpId: '' })
const or30981_7 = ref({ uniqueCpId: '' })
const or30981_8 = ref({ uniqueCpId: '' })

const localOneway = reactive({
  /**
   * アセスメント表タイトル
   */
  mo01338Oneway: {
    value: t('label.interRAI-method-care-assessment-table-A-title'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentTitle',
      itemStyle: 'font-size: 20px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 調査アセスメント種別
   */
  or30980Oneway: {
    btnItems: [],
  } as Or30980OnewayType,
  /**
   * 特に指示のない限り、3日間で評価する
   */
  mo01338OnewayEvaluationTip: {
    value: t('label.interRAI-method-care-assessment-table-A-evaluation-tip'),
    valueFontWeight: 'normal',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 入力対象外のアセスメント項目をグレーで表示します
   */
  mo01338OnewayInputTip: {
    value: t('label.interRAI-method-care-assessment-table-A-input-tip'),
    valueFontWeight: 'normal',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A.基本情報セクション
   */
  mo01338OnewayBasicInfoSectionTitle: {
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-title'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'align-center',
      itemStyle: 'font-size: 18px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A4.婚姻状況サブセクションタイトル
   */
  mo01338OnewayBasicInfoSectionA4Title: {
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-A4-title'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'align-center',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A4.婚姻状況メモテキストエリア
   */
  or30981OnewayTypeA4: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '4000',
      height: '58px'
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  /**
   * A4.婚姻状況ボタン群
   */
  or30982OnewayTypeA4Btns: {
    btnItems: [] as Or30982BtnInfo[],
    layoutType: '1',
  } as Or30982OnewayType,
  /**
   * A7.要介護度サブセクションタイトル
   */
  mo01338OnewayBasicInfoSectionA7Title: {
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-A7-title'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'align-center',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A7.要介護度メモテキストエリア
   */
  or30981OnewayTypeA7: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '4000',
      height: '58px'
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  /**
   * A7.要介護度ボタン群
   */
  or30982OnewayTypeA7Btns: {
    btnItems: [] as Or30982BtnInfo[],
    layoutType: '1',
  } as Or30982OnewayType,
  /**
   * A8.アセスメントの理由サブセクションタイトル
   */
  mo01338OnewayBasicInfoSectionA8Title: {
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-A8-title'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'align-center',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A8.アセスメントの理由メモテキストエリア
   */
  or30981OnewayTypeA8: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '4000',
      height: '58px'
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  /**
   * A8.アセスメントの理由ボタン群
   */
  or30982OnewayTypeA8Btns: {
    btnItems: [] as Or30982BtnInfo[],
    layoutType: '1',
  } as Or30982OnewayType,
  /**
   * A10.本人のケアの目標サブセクションタイトル
   */
  mo01338OnewayBasicInfoSectionA10Title: {
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-A10-title'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'align-center',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A10.本人のケアの目標メモテキストエリア
   */
  orX0156OnewayTypeA10: {
    showItemLabel: false,
    showDividerLineFlg: false,
    showEditBtnFlg: true,
    isVerticalLabel: true,
    customClass: {
      labelClass: 'pa-0',
    } as CustomClass,
    maxRows: '3',
    rows: '3',
    noResize: true,
    autoGrow: false,
    maxlength: '328',
    height: '65'
  } as OrX0156OnewayType,
  /**
   * A11.アセスメント時の居住場所サブセクションタイトル
   */
  mo01338OnewayBasicInfoSectionA11Title: {
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-A11-title'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'align-center',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A11.アセスメント時の居住場所メモテキストエリア
   */
  or30981OnewayTypeA11: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '4000',
      height: '58px'
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  /**
   * A11.アセスメント時の居住場所ボタン群
   */
  or30982OnewayTypeA11Btns: {
    btnItems: [] as Or30982BtnInfo[],
    layoutType: '1',
  } as Or30982OnewayType,
  /**
   * A12.同居形態サブセクションタイトル
   */
  mo01338OnewayBasicInfoSectionA12Title: {
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-A12-title'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'align-center',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A12.同居形態 a.同居者テキストエリア
   */
  or30981OnewayTypeA12A: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '4000',
      height: '58px'
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  /**
   * A12.同居形態サブセクションタイトル
   */
  mo01338OnewayBasicInfoSectionA12ATitle: {
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-A12A-title'),
    customClass: {
      itemClass: 'align-center',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A12.同居形態 a.同居者ボタン群
   */
  or30982OnewayTypeA12ABtns: {
    btnItems: [] as Or30982BtnInfo[],
    layoutType: '1',
  } as Or30982OnewayType,
  /**
   * A12.同居形態 b.変化メモテキストエリア
   */
  or30981OnewayTypeA12B: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '4000',
      height: '58px'
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  /**
   * A12.b. 90日前（または前回アセスメント時）と比較して同居形態の変化ラベルタイトル
   */
  mo01338OnewayBasicInfoSectionA12BTitle: {
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-A12B-title'),
    customClass: {
      itemClass: 'align-center',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A12.同居形態 b.変化ボタン群
   */
  or30982OnewayTypeA12BBtns: {
    btnItems: [] as Or30982BtnInfo[],
    layoutType: '1',
  } as Or30982OnewayType,
  /**
   * A12.同居形態 c.他の居住テキストエリア
   */
  or30981OnewayTypeA12C: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '4000',
      height: '58px'
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  /**
   * A12.同居形態サブセクションタイトル
   */
  mo01338OnewayBasicInfoSectionA12CTitle: {
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-A12C-title'),
    customClass: {
      itemClass: 'align-center',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A12.同居形態 c.他の居住ボタン群
   */
  or30982OnewayTypeA12CBtns: {
    btnItems: [] as Or30982BtnInfo[],
    layoutType: '2',
  } as Or30982OnewayType,
  /**
   * A13.退院後の経過期間サブセクションタイトル
   */
  mo01338OnewayBasicInfoSectionA13Title: {
    valueFontWeight: 'bolder',
    value: t('label.interRAI-method-care-assessment-table-A-basic-info-section-A13-title'),
    customClass: {
      itemClass: 'align-center',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * A13.退院後の経過期間メモテキストエリア
   */
  or30981OnewayTypeA13: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '4000',
      height: '58px'
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  /**
   * A13.退院後の経過期間ボタン群
   */
  or30982OnewayTypeA13Btns: {
    btnItems: [] as Or30982BtnInfo[],
    layoutType: '1',
  } as Or30982OnewayType,
  /**
   * GUI00937_入力支援［ケアマネ］
   */
  or51775Oneway: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
})

const local = {
  /**
   * 親画面.利用者Id
   */
  userId: '',
  /**
   * 親画面.事業者Id
   */
  svJigyoId: '',
  /**
   * 親画面.事業者名
   */
  svJigyoKnj: '',
  /**
   * 親画面.更新区分("":更新なし／"C":新規／"U":更新／"D":削除)
   */
  updateKbn: '',
  /**
   * 親画面.履歴更新区分("":更新なし／"C":新規／"U":更新／"D":削除)
   */
  historyUpdateKbn: '',
  /**
   * 親画面.選定表・検討表作成区分
   */
  tableCreateKbn: '',
  /**
   * 親画面.履歴情報
   */
  historyInfo: {} as HistoryInfoEntity,
  /**
   * 親画面.計画期間情報
   */
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  /**
   * 親画面.基準日
   */
  kijunbiYmd: '',
  /**
   * 親画面.作成者ID
   */
  sakuseiId: '',
  /**
   * 親画面.期間管理フラグ
   */
  kikanKanriFlg: '',
  /**
   * アセスメントID
   */
  raiId: '',
  /**
   * サブ情報
   */
  subInfo: {} as SubInfoAEntity,
  /**
   * 画面項目区分
   */
  vieTypeKbn: '',
  /**
   * 新規情報の要介護度
   */
  yokaiKbn: '',
  /**
   * 新規情報の調査アセスメント種別
   */
  assType: '',
  /**
   * 種別ID
   */
  syubetsuId: ''
}

/**
 * 入力フーム_フラゲ
 */
const inputBoomShow = ref<boolean>(true)

/**
 * 調査アセスメント種別入力値
 */
const or30980Type = ref<Or30980Type>({
  value: '',
} as Or30980Type)

/**
 * A4.婚姻状況ボタン群群戻り値
 */
const or30982TypeA4 = ref<Or30982Type>({
  value: '',
} as Or30982Type)
/**
 * A4.婚姻状況メモテキストエリア入力値
 */
const or30981TypeA4Area = ref<Or30981Type>({
  content: '',
  fontSize: '',
  fontColor: '',
} as Or30981Type)

/**
 * A7.要介護度ボタン群群戻り値
 */
const or30982TypeA7 = ref<Or30982Type>({
  value: '',
} as Or30982Type)
/**
 * A7.要介護度メモテキストエリア入力値
 */
const or30981TypeA7Area = ref<Or30981Type>({
  content: '',
  fontSize: '',
  fontColor: '',
} as Or30981Type)

/**
 * A8.アセスメントの理由ボタン群群戻り値
 */
const or30982TypeA8 = ref<Or30982Type>({
  value: '',
} as Or30982Type)
/**
 * A8.アセスメントの理由メモテキストエリア入力値
 */
const or30981TypeA8Area = ref<Or30981Type>({
  content: '',
  fontSize: '',
  fontColor: '',
} as Or30981Type)

/**
 * A10.本人のケアの目標テキストエリア入力値
 */
const orX0156TypeA10Area = ref<OrX0156Type>({
  value: '',
} as OrX0156Type)

/**
 * A10.本人のケアの目標入力支援戻り値
 */
const or51775Type = ref({
  modelValue: '',
})

/**
 * A11.アセスメント時の居住場所アセスメント時の居住場所ボタン群群戻り値
 */
const or30982TypeA11 = ref<Or30982Type>({
  value: '',
} as Or30982Type)
/**
 * A11.アセスメント時の居住場所メモテキストエリア入力値
 */
const or30981TypeA11Area = ref<Or30981Type>({
  content: '',
  fontSize: '',
  fontColor: '',
} as Or30981Type)

/**
 * A12.同居形態 a.同居者ボタン群群戻り値
 */
const or30982TypeA12A = ref<Or30982Type>({
  value: '',
} as Or30982Type)
/**
 * A12.同居形態 a. 同居者メモテキストエリア入力値
 */
const or30981TypeA12AArea = ref<Or30981Type>({
  content: '',
  fontSize: '',
  fontColor: '',
} as Or30981Type)

/**
 * A12.同居形態 b.変化ボタン群群戻り値
 */
const or30982TypeA12B = ref<Or30982Type>({
  value: '',
} as Or30982Type)
/**
 * A12.同居形態 b. 90日前（または前回アセスメント時）と比較して同居形態の変化 メモテキストエリア入力値
 */
const or30981TypeA12BArea = ref<Or30981Type>({
  content: '',
  fontSize: '',
  fontColor: '',
} as Or30981Type)

/**
 * A12.同居形態 c.他の居住ボタン群群戻り値
 */
const or30982TypeA12C = ref<Or30982Type>({
  value: '',
} as Or30982Type)
/**
 * A12.同居形態 c. 利用者や家族、身内は、利用者は他のところに住むほうがいいのではないかと思っている メモテキストエリア入力値
 */
const or30981TypeA12CArea = ref<Or30981Type>({
  content: '',
  fontSize: '',
  fontColor: '',
} as Or30981Type)

/**
 * A13.退院後の経過期間ボタン群戻り値
 */
const or30982TypeA13 = ref<Or30982Type>({
  value: '',
} as Or30982Type)
/**
 * A13．退院後の経過期間 メモテキストエリア入力値
 */
const or30981TypeA13Area = ref<Or30981Type>({
  content: '',
  fontSize: '',
  fontColor: '',
} as Or30981Type)

/**
 * componentRef
 */
const componentRef = ref<HTMLDivElement | null>(null)
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or31000TwoWayType>({
  cpId: Or31000Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

useScreenOneWayBind<Or31000StateType>({
  cpId: Or31000Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (value) {
        local.kikanKanriFlg = value.kikanKanriFlg
        local.userId = value.userId
        local.svJigyoId = value.svJigyoId
        local.svJigyoKnj = value.svJigyoKnj
        local.kijunbiYmd = value.kijunbiYmd
        local.sakuseiId = value.sakuseiId
        local.historyInfo = value.historyInfo
        local.planPeriodInfo = value.planPeriodInfo
        local.raiId = value.raiId
        local.assType = value.assType
        local.yokaiKbn = value.yokaiKbn
        local.syubetsuId = value.syubetsuId
        local.updateKbn = value.updateKbn
        local.historyUpdateKbn = value.historyUpdateKbn
        local.tableCreateKbn = value.tableCreateKbn
      }
      // 新規以外の場合
      if ('add' !== value?.executeFlag) {
        if (local.historyInfo) {
          // 履歴情報.調査アセスメント種別
          or30980Type.value.value = local.historyInfo.assType
        }
      }
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          void save()
          break
        // 新規
        case 'add':
          void add()
          break
        // 複写
        case 'copy':
          copy()
          break
        // 削除
        case 'delete':
          del()
          break
        // データ再取得
        case 'getData':
          // アセスメント(インターライ)画面A初期情報を取得
          void getSubInfo(local.historyInfo.raiId)
          break
        // 入力フォームを非表示
        case 'hidden':
          // 入力フォームを非表示
          inputBoomShow.value = false
          break
        default:
          break
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or30980Const.CP_ID(1)]: or30980.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or30981Const.CP_ID(1)]: or30981_1.value,
  [Or30981Const.CP_ID(2)]: or30981_2.value,
  [Or30981Const.CP_ID(3)]: or30981_3.value,
  [Or30981Const.CP_ID(4)]: or30981_4.value,
  [Or30981Const.CP_ID(5)]: or30981_5.value,
  [Or30981Const.CP_ID(6)]: or30981_6.value,
  [Or30981Const.CP_ID(7)]: or30981_7.value,
  [Or30981Const.CP_ID(8)]: or30981_8.value,
})
/**************************************************
 * Emit
 **************************************************/
/**
 * emit saveEndイベントを定義
 */
const emit = defineEmits(['saveEnd'])

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * AC023_「調査アセスメント種別」選択変更
 */
watch(
  () => or30980Type.value,
  (newValue) => {
    switch (newValue.value) {
      // 「2:施設版」に変更の場合
      case '2':
        // A11.アセスメント時の居住場所
        localOneway.or30982OnewayTypeA11Btns.disabled = true
        localOneway.or30981OnewayTypeA11.disabled = true

        // A12.同居形態 - a.同居者
        localOneway.or30982OnewayTypeA12ABtns.disabled = true
        localOneway.or30981OnewayTypeA12A.disabled = true
        // A12.同居形態 - b.変化
        localOneway.or30982OnewayTypeA12BBtns.disabled = true
        localOneway.or30981OnewayTypeA12B.disabled = true
        // A12.同居形態 - c.他の居住
        localOneway.or30982OnewayTypeA12CBtns.disabled = true
        localOneway.or30981OnewayTypeA12C.disabled = true
        break
      // 「3:高齢者住宅版」に変更の場合
      case '3':
        // A11.アセスメント時の居住場所
        localOneway.or30982OnewayTypeA11Btns.disabled = false
        localOneway.or30981OnewayTypeA11.disabled = false
        // A12.同居形態 - a.同居者
        localOneway.or30982OnewayTypeA12ABtns.disabled = true
        localOneway.or30981OnewayTypeA12A.disabled = true
        // A12.同居形態 - b.変化
        localOneway.or30982OnewayTypeA12BBtns.disabled = true
        localOneway.or30981OnewayTypeA12B.disabled = true
        // A12.同居形態 - c.他の居住
        localOneway.or30982OnewayTypeA12CBtns.disabled = true
        localOneway.or30981OnewayTypeA12C.disabled = true
        break
      // 「1:居宅版」に変更の場合
      case '1':
        // A11.アセスメント時の居住場所
        localOneway.or30982OnewayTypeA11Btns.disabled = false
        localOneway.or30981OnewayTypeA11.disabled = false
        // A12.同居形態 - a.同居者
        localOneway.or30982OnewayTypeA12ABtns.disabled = false
        localOneway.or30981OnewayTypeA12A.disabled = false
        // A12.同居形態 - b.変化
        localOneway.or30982OnewayTypeA12BBtns.disabled = false
        localOneway.or30981OnewayTypeA12B.disabled = false
        // A12.同居形態 - c.他の居住
        localOneway.or30982OnewayTypeA12CBtns.disabled = false
        localOneway.or30981OnewayTypeA12C.disabled = false
        break
    }
  }
)

/**
 * 調査アセスメント種別入力値変更
 */
watch(
  () => or30980Type.value,
  () => {
    if (refValue.value) {
      refValue.value.or30980Type = or30980Type.value
    }
  }
)

/**
 * A4.婚姻状況ボタン群選択値変更
 */
watch(
  () => or30982TypeA4.value,
  () => {
    if (refValue.value) {
      refValue.value.or30982TypeA4 = or30982TypeA4.value
    }
  }
)

/**
 * A4.婚姻状況メモテキストエリア入力値変更
 */
watch(
  () => or30981TypeA4Area.value,
  () => {
    if (refValue.value) {
      refValue.value.or30981TypeA4Area = or30981TypeA4Area.value
    }
  }
)

/**
 * A7.要介護度ボタン群群戻り値
 */
watch(
  () => or30982TypeA7.value,
  () => {
    if (refValue.value) {
      refValue.value.or30982TypeA7 = or30982TypeA7.value
    }
  }
)

/**
 * A7.要介護度メモテキストエリア入力値変更
 */
watch(
  () => or30981TypeA7Area.value,
  () => {
    if (refValue.value) {
      refValue.value.or30981TypeA7Area = or30981TypeA7Area.value
    }
  }
)

/**
 * A8.アセスメントの理由ボタン群群戻り値変更
 */
watch(
  () => or30982TypeA8.value,
  () => {
    if (refValue.value) {
      refValue.value.or30982TypeA8 = or30982TypeA8.value
    }
  }
)

/**
 * A8.アセスメントの理由メモテキストエリア入力値変更
 */
watch(
  () => or30981TypeA8Area.value,
  () => {
    if (refValue.value) {
      refValue.value.or30981TypeA8Area = or30981TypeA8Area.value
    }
  }
)

/**
 * A10.本人のケアの目標テキストエリア入力値変更
 */
watch(
  () => orX0156TypeA10Area.value,
  () => {
    if (refValue.value) {
      refValue.value.orX0156TypeA10Area = orX0156TypeA10Area.value
    }
  }
)

/**
 * A11.アセスメント時の居住場所アセスメント時の居住場所ボタン群群戻り値変更
 */
watch(
  () => or30982TypeA11.value,
  () => {
    if (refValue.value) {
      refValue.value.or30982TypeA11 = or30982TypeA11.value
    }
  }
)

/**
 * A11.アセスメント時の居住場所メモテキストエリア入力値変更
 */
watch(
  () => or30981TypeA11Area.value,
  () => {
    if (refValue.value) {
      refValue.value.or30981TypeA11Area = or30981TypeA11Area.value
    }
  }
)

/**
 * A12.同居形態 a.同居者ボタン群群戻り値変更
 */
watch(
  () => or30982TypeA12A.value,
  () => {
    if (refValue.value) {
      refValue.value.or30982TypeA12A = or30982TypeA12A.value
    }
  }
)

/**
 * A12.同居形態 a. 同居者メモテキストエリア入力値変更
 */
watch(
  () => or30981TypeA12AArea.value,
  () => {
    if (refValue.value) {
      refValue.value.or30981TypeA12AArea = or30981TypeA12AArea.value
    }
  }
)

/**
 * A12.同居形態 b.変化ボタン群群戻り値変更
 */
watch(
  () => or30982TypeA12B.value,
  () => {
    if (refValue.value) {
      refValue.value.or30982TypeA12B = or30982TypeA12B.value
    }
  }
)

/**
 * A12.同居形態 b. 90日前（または前回アセスメント時）と比較して同居形態の変化 メモテキストエリア入力値変更
 */
watch(
  () => or30981TypeA12BArea.value,
  () => {
    if (refValue.value) {
      refValue.value.or30981TypeA12BArea = or30981TypeA12BArea.value
    }
  }
)

/**
 * A12.同居形態 c.他の居住ボタン群群戻り値変更
 */
watch(
  () => or30982TypeA12C.value,
  () => {
    if (refValue.value) {
      refValue.value.or30982TypeA12C = or30982TypeA12C.value
    }
  }
)

/**
 * A12.同居形態 c. 利用者や家族、身内は、利用者は他のところに住むほうがいいのではないかと思っている メモテキストエリア入力値変更
 */
watch(
  () => or30981TypeA12CArea.value,
  () => {
    if (refValue.value) {
      refValue.value.or30981TypeA12CArea = or30981TypeA12CArea.value
    }
  }
)

/**
 * A13.退院後の経過期間ボタン群戻り値変更
 */
watch(
  () => or30982TypeA13.value,
  () => {
    if (refValue.value) {
      refValue.value.or30982TypeA13 = or30982TypeA13.value
    }
  }
)

/**
 * A13．退院後の経過期間 メモテキストエリア入力値変更
 */
watch(
  () => or30981TypeA13Area.value,
  () => {
    if (refValue.value) {
      refValue.value.or30981TypeA13Area = or30981TypeA13Area.value
    }
  }
)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

onMounted(async () => {
  // 初期情報取得
  await init()
})

/**
 * 初期化
 */
const init = async () => {
  // 期間管理フラグが「1:管理する」
  if ('1' === local.kikanKanriFlg) {
    // 計画期間が登録されていない場合
    if (local.planPeriodInfo && Number(local.planPeriodInfo.periodCnt) > 0) {
      inputBoomShow.value = true
    } else {
      inputBoomShow.value = false
    }
  }

  // 汎用コード取得API実行
  await initCodes()
}

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 調査アセスメント種別
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    // 婚姻状況ボタン群
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MARITAL_STATUS },
    // 要介護度ボタン群
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LEVEL_OF_CARE_REQUIRED },
    // アセスメントの理由ボタン群
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_REASON },
    // アセスメント時の居住場所ボタン群
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_RESIDENCE_LOCATION },
    // 同居形態 a.同居者ボタン群
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_COHABIT_FORM_A },
    // 同居形態 b.変化ボタン群
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_COHABIT_FORM_B },
    // 同居形態 c.他の居住タン群
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_COHABIT_FORM_C },
    // 退院後の経過期間タン群
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DISCHARGE_FROM_HOSPITAL_AFTER_PASSAGE_PERIOD },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  const codeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )
  if (codeTypes.length > 0) {
    const btnItems: BtnStruct[] = []
    for (const item of codeTypes) {
      btnItems.push({
        value: item.value,
        label: item.label,
        name: item.value,
      } as BtnStruct)
    }
    if (btnItems.length > 0) {
      // Or30980のダイアログ状態を更新する
      Or30980Logic.state.set({
        uniqueCpId: or30980.value.uniqueCpId,
        state: {
          flag: 'init',
        },
      })
      localOneway.or30980Oneway.btnItems = btnItems
    }
  }

  // A4．婚姻状況ボタン群 ラジオ選択肢 - ボタングループの生成
  localOneway.or30982OnewayTypeA4Btns.btnItems = btnGroupSettings(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_MARITAL_STATUS), '312px', true)

  // A7.要介護度ボタン群 ラジオ選択肢 - ボタングループの生成
  localOneway.or30982OnewayTypeA7Btns.btnItems = btnGroupSettings(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_LEVEL_OF_CARE_REQUIRED), '312px', true)

  // A8.アセスメントの理由ボタン群 - ボタングループの生成
  localOneway.or30982OnewayTypeA8Btns.btnItems = btnGroupSettings(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_REASON), '312px', true)

  // A11.アセスメント時の居住場所ボタン群 - ボタングループの生成
  localOneway.or30982OnewayTypeA11Btns.btnItems = btnGroupSettings(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_RESIDENCE_LOCATION), '468px', true)

  // A12.同居形態 a.同居者ボタン群 - ボタングループの生成
  localOneway.or30982OnewayTypeA12ABtns.btnItems = btnGroupSettings(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_COHABIT_FORM_A), '312px', true)

  // A12.同居形態 b.変化ボタン群 - ボタングループの生成
  localOneway.or30982OnewayTypeA12BBtns.btnItems = btnGroupSettings(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_COHABIT_FORM_B), '180px', false)

  // A12.同居形態 c.他の居住ボタン群 - ボタングループの生成
  localOneway.or30982OnewayTypeA12CBtns.btnItems = btnGroupSettings(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_COHABIT_FORM_C), '200px', false)

  // A13.退院後の経過期間ボタン群 - ボタングループの生成
  localOneway.or30982OnewayTypeA13Btns.btnItems = btnGroupSettings(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_DISCHARGE_FROM_HOSPITAL_AFTER_PASSAGE_PERIOD), '312px', true)
}

/**
 * ラジオ選択肢 - ボタングループの生成
 *
 * @param list - 汎用コード一覧
 *
 * @param width - 幅(*px)
 *
 * @param refactoring - リファクタリング
 */
const btnGroupSettings = (list: CodeType[], width: string, refactoring: boolean):Or30982BtnInfo[] => {
  // ラジオ選択肢
  const or30982BtnInfoList:Or30982BtnInfo[] = []
  const actionList: CodeType[] = []
  if(refactoring && list.length > 2) {
    const middleIndex = Math.ceil(list.length / 2)
    const leftList = list.slice(0, middleIndex)
    const rightList = list.slice(middleIndex)
    for (let i = 0; i < list.length; i++) {
      if (i % 2 === 0 && i / 2 < leftList.length) {
        actionList.push(leftList[Math.floor(i / 2)]);
      } else if (i % 2 === 1 && Math.floor(i / 2) < rightList.length) {
        actionList.push(rightList[Math.floor(i / 2)]);
      }
    }
  }
  else {
    actionList.push(...list)
  }
  for(const item of actionList) {
    if(item) {
      or30982BtnInfoList.push({
        name: '',
        label: item.label,
        value: item.value,
        width: width
      } as Or30982BtnInfo)
    }
  }
  return or30982BtnInfoList
}

/**
 * AC027_「本人のケアの目標入力支援アイコンボタン」押下
 *
 * @param type - 画面項目区分
 */
const careTargetInputSupportIconClick = (
  type: 'A4' | 'A7' | 'A8' | 'A10' | 'A11' | 'A12a' | 'A12b' | 'A12c' | 'A13'
) => {
  local.vieTypeKbn = type
  localOneway.or51775Oneway = {
    // タイトル
    title: t('label.memo'),
    // 画面ID
    screenId: Or31000Const.STR.SCREEN_ID,
    // 分類ID
    bunruiId: Or31000Const.STR.EMPTY,
    // 大分類ＣＤ
    t1Cd: Or31000Const.STR.T1_CD,
    // 中分類CD
    t2Cd: Or31000Const.STR.T2_CD,
    // 小分類ＣＤ
    t3Cd: Or31000Const.STR.T3_CD,
    // テーブル名
    tableName: Or31000Const.STR.TABLE_NAME,
    // カラム名
    columnName: Or31000Const.STR.EMPTY,
    // アセスメント方式
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? Or31000Const.STR.EMPTY,
    // 文章内容
    inputContents: Or31000Const.STR.EMPTY,
    // 利用者ID
    userId: local.userId,
    // モード
    mode: Or31000Const.STR.EMPTY,
  } as Or51775OnewayType
  switch (type) {
    // A4.婚姻状況の場合
    case 'A4':
      localOneway.or51775Oneway.columnName = Or31000Const.STR.A4_COLUMN_NAME
      localOneway.or51775Oneway.inputContents = or30982TypeA4.value.value ?? Or31000Const.STR.EMPTY
      break
    // A7.要介護度の場合
    case 'A7':
      localOneway.or51775Oneway.columnName = Or31000Const.STR.A7_COLUMN_NAME
      localOneway.or51775Oneway.inputContents = or30982TypeA7.value.value ?? Or31000Const.STR.EMPTY
      break
    // A8.アセスメントの理由の場合
    case 'A8':
      localOneway.or51775Oneway.columnName = Or31000Const.STR.A8_COLUMN_NAME
      localOneway.or51775Oneway.inputContents = or30982TypeA8.value.value ?? Or31000Const.STR.EMPTY
      break
    // A10.本人のケアの目標の場合
    case 'A10':
      localOneway.or51775Oneway.title = t(
        'label.interRAI-method-care-assessment-table-A-basic-info-section-A10-param'
      )
      localOneway.or51775Oneway.columnName = Or31000Const.STR.A10_COLUMN_NAME
      localOneway.or51775Oneway.inputContents =
        orX0156TypeA10Area.value.value ?? Or31000Const.STR.EMPTY
      break
    // A11.アセスメント時の居住場所の場合
    case 'A11':
      localOneway.or51775Oneway.columnName = Or31000Const.STR.A11_COLUMN_NAME
      localOneway.or51775Oneway.inputContents = or30982TypeA11.value.value ?? Or31000Const.STR.EMPTY
      break
    // A12.同居形態 a.同居者の場合
    case 'A12a':
      localOneway.or51775Oneway.columnName = Or31000Const.STR.A12A_COLUMN_NAME
      localOneway.or51775Oneway.inputContents =
        or30982TypeA12A.value.value ?? Or31000Const.STR.EMPTY
      break
    // A12.同居形態 b.変化の場合
    case 'A12b':
      localOneway.or51775Oneway.columnName = Or31000Const.STR.A12B_COLUMN_NAME
      localOneway.or51775Oneway.inputContents =
        or30982TypeA12B.value.value ?? Or31000Const.STR.EMPTY
      break
    // A12.同居形態 c.他の居住の場合
    case 'A12c':
      localOneway.or51775Oneway.columnName = Or31000Const.STR.A12C_COLUMN_NAME
      localOneway.or51775Oneway.inputContents =
        or30982TypeA12C.value.value ?? Or31000Const.STR.EMPTY
      break
    // A13.退院後の経過期間の場合
    case 'A13':
      localOneway.or51775Oneway.columnName = Or31000Const.STR.A13_COLUMN_NAME
      localOneway.or51775Oneway.inputContents = or30982TypeA13.value.value ?? Or31000Const.STR.EMPTY
      break
    default:
      break
  }
  // GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}

/**
 * AC004_「新規ボタン」押下
 */
const add = () => {
  // 画面項目の初期化
  inputProject()
}

/**
 * 画面項目の初期化
 */
const inputProject = () => {
  // 新規情報.調査アセスメント種別 > 0の場合
  if(Number(local.assType) > 0) {
    // 新規情報.調査アセスメント種別
    or30980Type.value.value = local.assType
  }
  // 上記以外の場合
  else {
    // 共通情報.計画書様式が「1：施設」の場合
    if(cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg === '1') {
      // 「2：施設版」を選択状態にする
      or30980Type.value.value = '2'
    }
    // 上記以外の場合
    else {
      // 「1:居宅版」を選択状態にする
      or30980Type.value.value = '1'
    }
  }

  // A4.婚姻状況サブセクション
  or30981TypeA4Area.value.content = ''
  or30981TypeA4Area.value.fontSize = getDefaultFontSize()
  or30981TypeA4Area.value.fontColor = getDefaultFontColor()
  or30982TypeA4.value.value = ''

  // A7.要介護度サブセクション
  or30981TypeA7Area.value.content = ''
  or30981TypeA7Area.value.fontSize = getDefaultFontSize()
  or30981TypeA7Area.value.fontColor = getDefaultFontColor()
  or30982TypeA7.value.value = local.yokaiKbn

  // A8.アセスメントの理由サブセクション
  or30981TypeA8Area.value.content = ''
  or30981TypeA8Area.value.fontSize = getDefaultFontSize()
  or30981TypeA8Area.value.fontColor = getDefaultFontColor()
  // 新規の場合、取得したデフォルト要介護度
  or30982TypeA8.value.value = ''

  // A10.本人のケアの目標サブセクション
  orX0156TypeA10Area.value.value = ''

  // A11.アセスメント時の居住場所サブセクション
  or30981TypeA11Area.value.content = ''
  or30981TypeA11Area.value.fontSize = getDefaultFontSize()
  or30981TypeA11Area.value.fontColor = getDefaultFontColor()
  or30982TypeA11.value.value = ''

  // A12.同居形態サブセクション - A12a
  or30981TypeA12AArea.value.content = ''
  or30981TypeA12AArea.value.fontSize = getDefaultFontSize()
  or30981TypeA12AArea.value.fontColor = getDefaultFontColor()
  or30982TypeA12A.value.value = ''

  // A12.同居形態サブセクション - A12b
  or30981TypeA12BArea.value.content = ''
  or30981TypeA12BArea.value.fontSize = getDefaultFontSize()
  or30981TypeA12BArea.value.fontColor = getDefaultFontColor()
  or30982TypeA12B.value.value = ''

  // A12.同居形態サブセクション - A12c
  or30981TypeA12CArea.value.content = ''
  or30981TypeA12CArea.value.fontSize = getDefaultFontSize()
  or30981TypeA12CArea.value.fontColor = getDefaultFontColor()
  or30982TypeA12C.value.value = ''

  // A13.退院後の経過期間サブセクション
  or30981TypeA13Area.value.content = ''
  or30981TypeA13Area.value.fontSize = getDefaultFontSize()
  or30981TypeA13Area.value.fontColor = getDefaultFontColor()
  or30982TypeA13.value.value = ''

  // 画面.更新区分が"C"（新規）に設定する
  local.updateKbn = UPDATE_KBN.CREATE

  // refValueを初期化
  refValueInit()
}

/**
 * デフォルト文字サイズ
 */
const getDefaultFontSize = (): string => {
  // 初期設定マスタ.アセスメント(インターライ)：文字サイズ
  const fontSize = cmnRouteCom.getInitialSettingMaster()?.assCharSize
  // 文字サイズが「0：小さい」の場合
  if('0' === fontSize) {
     return '9'
  }
  // 文字サイズが「2：大きい」の場合
  else if('2' === fontSize) {
     return '15'
  }
  // 上記以外の場合
  else {
    // 12：デフォルト値
    return '12'
  }
}

/**
 * デフォルト文字色
 */
const getDefaultFontColor = (): string => {
  // 初期設定マスタ.アセスメント(インターライ)：文字色
  const fontSize = cmnRouteCom.getInitialSettingMaster()?.assCharColor
  // 初期設定マスタ.アセスメント(インターライ).文字サイズがある場合
  if(fontSize) {
    // 初期設定マスタ.アセスメント(インターライ).文字色
    return fontSize
  }
  // 上記以外の場合
  else {
    // #000000：デフォルト値
    return '0'
  }
}

/**
 * AC003_「保存ボタン」押下
 */
const save = async () => {
  // アセスメント(インターライ)画面A情報を保存
  const inputData: IAssessmentInterRAIAUpdateInEntity = {
    sysRyaku: systemCommonsStore.getSystemAbbreviation,
    sectionName: TeX0003Const.SECTION_NAME,
    gsyscd: systemCommonsStore.getSystemCode,
    kinouId: systemCommonsStore.getFunctionId,
    shokuId: systemCommonsStore.getStaffId,
    kikanKanriFlg: local.kikanKanriFlg,
    historyNo: local.historyInfo.krirekiNo,
    svJigyoName: local.svJigyoKnj,
    periodNo: local.planPeriodInfo.periodNo,
    startYmd: local.kikanKanriFlg === '1' ? local.planPeriodInfo.startYmd : '',
    endYmd: local.kikanKanriFlg === '1' ? local.planPeriodInfo.endYmd : '',
    index: TeX0003Const.STR.TWO,
    tabName: 'A',
    edocumentUseParam: useCmnCom().getEDocumentParam(),
    edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: systemCommonsStore.getShisetuId,
    userId: local.userId,
    svJigyoId: local.svJigyoId,
    syubetsuId: local.syubetsuId,
    subKbn: TeX0003Const.TAB.A,
    updateKbn: local.updateKbn,
    historyUpdateKbn: local.historyUpdateKbn,
    tableCreateKbn: local.tableCreateKbn,
    sc1Id: local.planPeriodInfo.sc1Id,
    raiId: local.historyInfo.raiId,
    kijunbiYmd: local.kijunbiYmd,
    sakuseiId: local.sakuseiId,
    assType: or30980Type.value.value,
    subInfoA: {
      a4: or30982TypeA4.value.value,
      a7: or30982TypeA7.value.value,
      a8: or30982TypeA8.value.value,
      a10Knj: orX0156TypeA10Area.value.value ?? '',
      a11: or30982TypeA11.value.value,
      a12A: or30982TypeA12A.value.value,
      a12B: or30982TypeA12B.value.value,
      a12C: or30982TypeA12C.value.value,
      a13: or30982TypeA13.value.value,
      a4MemoKnj: or30981TypeA4Area.value.content ?? '',
      a4MemoFont: or30981TypeA4Area.value.fontSize ?? '',
      a4MemoColor: or30981TypeA4Area.value.fontColor ?? '',
      a7MemoKnj: or30981TypeA7Area.value.content ?? '',
      a7MemoFont: or30981TypeA7Area.value.fontSize ?? '',
      a7MemoColor: or30981TypeA7Area.value.fontColor ?? '',
      a8MemoKnj: or30981TypeA8Area.value.content ?? '',
      a8MemoFont: or30981TypeA8Area.value.fontSize ?? '',
      a8MemoColor: or30981TypeA8Area.value.fontColor ?? '',
      a11MemoKnj: or30981TypeA11Area.value.content ?? '',
      a11MemoFont: or30981TypeA11Area.value.fontSize ?? '',
      a11MemoColor: or30981TypeA11Area.value.fontColor ?? '',
      a12AMemoKnj: or30981TypeA12AArea.value.content ?? '',
      a12AMemoFont: or30981TypeA12AArea.value.fontSize ?? '',
      a12AMemoColor: or30981TypeA12AArea.value.fontColor ?? '',
      a12BMemoKnj: or30981TypeA12BArea.value.content ?? '',
      a12BMemoFont: or30981TypeA12BArea.value.fontSize ?? '',
      a12BMemoColor: or30981TypeA12BArea.value.fontColor ?? '',
      a12CMemoKnj: or30981TypeA12CArea.value.content ?? '',
      a12CMemoFont: or30981TypeA12CArea.value.fontSize ?? '',
      a12CMemoColor: or30981TypeA12CArea.value.fontColor ?? '',
      a13MemoKnj: or30981TypeA13Area.value.content ?? '',
      a13MemoFont: or30981TypeA13Area.value.fontSize ?? '',
      a13MemoColor: or30981TypeA13Area.value.fontColor ?? '',
    } as SubInfoAEntity,
  } as IAssessmentInterRAIAUpdateInEntity

  const resp: IAssessmentInterRAIAUpdateOutEntity = await ScreenRepository.update(
    'assessmentInterRAIAUpdate',
    inputData
  )
  if (SUCCESS_STATUS_CODE === resp.statusCode || 'success' === resp.statusCode) {
    emit('saveEnd', resp.data.errKbn, resp.data.sc1Id, resp.data.raiId)
  }
}

/**
 * AC005_「複写ボタン」押下
 */
const copy = () => {
  void getSubInfo(local.raiId)
  // 複写表示の場合
  if (componentRef.value) {
    disableTab(componentRef.value)
  }
}

/**
 * AC011_「削除」押下
 */
const del = () => {
  // 画面「A」タブに対し、更新区分を「D:削除」にする。
  local.updateKbn = UPDATE_KBN.DELETE
  local.historyInfo = {} as HistoryInfoEntity

  inputBoomShow.value = false
}

/**
 * tabを制御
 *
 * @param component - コンポーネント
 */
const disableTab = (component: HTMLElement) => {
  const focusableSelectors = [
    'a[href]',
    'area[href]',
    'input',
    'select',
    'textarea',
    'button',
    'iframe',
    '[tabindex]',
    '[contenteditable]',
  ]

  const focusables = component.querySelectorAll(focusableSelectors.join(','))
  focusables.forEach((el) => {
    el.setAttribute('tabindex', '-1')
  })
}

/**
 * AC003-2-1_アセスメント(インターライ)画面履歴の最新情報を取得
 *
 * @param raiId - アセスメントID
 */
const getSubInfo = async (raiId: string) => {
  isLoading.value = true
  // バックエンドAPIから初期情報取得
  const inputData: IAssessmentInterRAIAInEntity = {
    raiId: raiId,
  }

  const resp: IAssessmentInterRAIAOutEntity = await ScreenRepository.select(
    'assessmentInterRAIAInitSelect',
    inputData
  )

  // 画面項目設定
  if (SUCCESS_STATUS_CODE === resp.statusCode || 'success' === resp.statusCode) {
    // タブ情報がNULLではない場合
    if (resp.data.subInfoA) {
      if (!inputBoomShow.value) {
        inputBoomShow.value = true
      }
      // データ取得
      local.subInfo = resp.data.subInfoA

      // A.基本情報セクション
      // A4.婚姻状況
      // メモ
      or30981TypeA4Area.value.content = local.subInfo.a4MemoKnj
      or30981TypeA4Area.value.fontSize = local.subInfo.a4MemoFont
      or30981TypeA4Area.value.fontColor = local.subInfo.a4MemoColor
      // ボタン群
      or30982TypeA4.value.value = local.subInfo.a4

      // A7.要介護度
      // メモ
      or30981TypeA7Area.value.content = local.subInfo.a7MemoKnj
      or30981TypeA7Area.value.fontSize = local.subInfo.a7MemoFont
      or30981TypeA7Area.value.fontColor = local.subInfo.a7MemoColor
      // ボタン群
      or30982TypeA7.value.value = local.subInfo.a7

      // A8.アセスメントの理由
      // メモ
      or30981TypeA8Area.value.content = local.subInfo.a8MemoKnj
      or30981TypeA8Area.value.fontSize = local.subInfo.a8MemoFont
      or30981TypeA8Area.value.fontColor = local.subInfo.a8MemoColor
      // ボタン群
      or30982TypeA8.value.value = local.subInfo.a8

      // A10.本人のケアの目標
      // メモ
      orX0156TypeA10Area.value.value = local.subInfo.a10Knj

      // A11.アセスメント時の居住場所
      or30981TypeA11Area.value.content = local.subInfo.a11MemoKnj
      or30981TypeA11Area.value.fontSize = local.subInfo.a11MemoFont
      or30981TypeA11Area.value.fontColor = local.subInfo.a11MemoColor
      // ボタン群
      or30982TypeA11.value.value = local.subInfo.a11

      // A12.同居形態
      or30981TypeA12AArea.value.content = local.subInfo.a12AMemoKnj
      or30981TypeA12AArea.value.fontSize = local.subInfo.a12AMemoFont
      or30981TypeA12AArea.value.fontColor = local.subInfo.a12AMemoColor
      // ボタン群
      or30982TypeA12A.value.value = local.subInfo.a12A

      // b.変化
      or30981TypeA12BArea.value.content = local.subInfo.a12BMemoKnj
      or30981TypeA12BArea.value.fontSize = local.subInfo.a12BMemoFont
      or30981TypeA12BArea.value.fontColor = local.subInfo.a12BMemoColor
      // ボタン群
      or30982TypeA12B.value.value = local.subInfo.a12B

      // c.他の居住
      or30981TypeA12CArea.value.content = local.subInfo.a12CMemoKnj
      or30981TypeA12CArea.value.fontSize = local.subInfo.a12CMemoFont
      or30981TypeA12CArea.value.fontColor = local.subInfo.a12CMemoColor
      // ボタン群
      or30982TypeA12C.value.value = local.subInfo.a12C

      // A13.退院後の経過
      // 退院後の経過期間
      or30981TypeA13Area.value.content = local.subInfo.a13MemoKnj
      or30981TypeA13Area.value.fontSize = local.subInfo.a13MemoFont
      or30981TypeA13Area.value.fontColor = local.subInfo.a13MemoColor
      // ボタン群
      or30982TypeA13.value.value = local.subInfo.a13

      // refValueを初期化
      refValueInit()
    }
    // 上記以外の場合
    else {
      // 画面の入力フォームの各項目を新規ディフォルト状態でセットする。
      inputProject()
    }
  }
  isLoading.value = false
}

/**
 * refValueを初期化
 */
const refValueInit = () => {
  refValue.value = {
    or30980Type: or30980Type.value,
    or30982TypeA4: or30982TypeA4.value,
    or30981TypeA4Area: or30981TypeA4Area.value,
    or30982TypeA7: or30982TypeA7.value,
    or30981TypeA7Area: or30981TypeA7Area.value,
    or30982TypeA8: or30982TypeA8.value,
    or30981TypeA8Area: or30981TypeA8Area.value,
    orX0156TypeA10Area: orX0156TypeA10Area.value,
    or30982TypeA11: or30982TypeA11.value,
    or30981TypeA11Area: or30981TypeA11Area.value,
    or30982TypeA12A: or30982TypeA12A.value,
    or30981TypeA12AArea: or30981TypeA12AArea.value,
    or30982TypeA12B: or30982TypeA12B.value,
    or30981TypeA12BArea: or30981TypeA12BArea.value,
    or30982TypeA12C: or30982TypeA12C.value,
    or30981TypeA12CArea: or30981TypeA12CArea.value,
    or30982TypeA13: or30982TypeA13.value,
    or30981TypeA13Area: or30981TypeA13Area.value,
  } as Or31000TwoWayType
  // 初期値に設定する
  setRefValue()
}

/**
 *  refValueを更新する
 */
const setRefValue = () => {
  useScreenStore().setCpTwoWay({
    cpId: Or31000Const.CP_ID(1),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    // 本文末に追加の場合
    if (Or31000Const.ADD_END_TEXT_IMPORT_TYPE === data.type) {
      switch (local.vieTypeKbn) {
        // A4.婚姻状況の場合
        case 'A4':
          or30981TypeA4Area.value.content = or30981TypeA4Area.value.content + data.value
          refValue.value!.or30981TypeA4Area = or30981TypeA4Area.value
          break
        // A7.要介護度の場合
        case 'A7':
          or30981TypeA7Area.value.content = or30981TypeA7Area.value.content + data.value
          refValue.value!.or30981TypeA7Area = or30981TypeA7Area.value
          break
        // A8.アセスメントの理由の場合
        case 'A8':
          or30981TypeA8Area.value.content = or30981TypeA8Area.value.content + data.value
          refValue.value!.or30981TypeA8Area = or30981TypeA8Area.value
          break
        // A10.本人のケアの目標の場合
        case 'A10':
          orX0156TypeA10Area.value.value = orX0156TypeA10Area.value.value + data.value
          refValue.value!.orX0156TypeA10Area = orX0156TypeA10Area.value
          break
        // A11.アセスメント時の居住場所の場合
        case 'A11':
          or30981TypeA11Area.value.content = or30981TypeA11Area.value.content + data.value
          refValue.value!.or30981TypeA11Area = or30981TypeA11Area.value
          break
        // A12.同居形態 a.同居者の場合
        case 'A12a':
          or30981TypeA12AArea.value.content = or30981TypeA12AArea.value.content + data.value
          refValue.value!.or30981TypeA12AArea = or30981TypeA12AArea.value
          break
        // A12.同居形態 b.変化の場合
        case 'A12b':
          or30981TypeA12BArea.value.content = or30981TypeA12BArea.value.content + data.value
          refValue.value!.or30981TypeA12BArea = or30981TypeA12BArea.value
          break
        // A12.同居形態 c.他の居住の場合
        case 'A12c':
          or30981TypeA12CArea.value.content = or30981TypeA12CArea.value.content + data.value
          refValue.value!.or30981TypeA12CArea = or30981TypeA12CArea.value
          break
        // A13.退院後の経過期間の場合
        case 'A13':
          or30981TypeA13Area.value.content = or30981TypeA13Area.value.content + data.value
          refValue.value!.or30981TypeA13Area = or30981TypeA13Area.value
          break
        default:
          break
      }
    }
    // 本文上書の場合
    else if (Or31000Const.OVERWRITE_TEXT_IMPORT_TYPE === data.type) {
      switch (local.vieTypeKbn) {
        // A4.婚姻状況の場合
        case 'A4':
          or30981TypeA4Area.value.content = data.value
          refValue.value!.or30981TypeA4Area = or30981TypeA4Area.value
          break
        // A7.要介護度の場合
        case 'A7':
          or30981TypeA7Area.value.content = data.value
          refValue.value!.or30981TypeA7Area = or30981TypeA7Area.value
          break
        // A8.アセスメントの理由の場合
        case 'A8':
          or30981TypeA8Area.value.content = data.value
          refValue.value!.or30981TypeA8Area = or30981TypeA8Area.value
          break
        // A10.本人のケアの目標の場合
        case 'A10':
          orX0156TypeA10Area.value.value = data.value
          refValue.value!.orX0156TypeA10Area = orX0156TypeA10Area.value
          break
        // A11.アセスメント時の居住場所の場合
        case 'A11':
          or30981TypeA11Area.value.content = data.value
          refValue.value!.or30981TypeA11Area = or30981TypeA11Area.value
          break
        // A12.同居形態 a.同居者の場合
        case 'A12a':
          or30981TypeA12AArea.value.content = data.value
          refValue.value!.or30981TypeA12AArea = or30981TypeA12AArea.value
          break
        // A12.同居形態 b.変化の場合
        case 'A12b':
          or30981TypeA12BArea.value.content = data.value
          refValue.value!.or30981TypeA12BArea = or30981TypeA12BArea.value
          break
        // A12.同居形態 c.他の居住の場合
        case 'A12c':
          or30981TypeA12CArea.value.content = data.value
          refValue.value!.or30981TypeA12CArea = or30981TypeA12CArea.value
          break
        // A13.退院後の経過期間の場合
        case 'A13':
          or30981TypeA13Area.value.content = data.value
          refValue.value!.or30981TypeA13Area = or30981TypeA13Area.value
          break
        default:
          break
      }
    }
  }
}
</script>

<template>
  <c-v-row
    v-if="inputBoomShow"
    ref="componentRef"
    no-gutters
    class="content"
  >
    <!-- ローディング -->
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <c-v-col
      cols="auto"
      class="pt-6"
    >
      <div style="width: 1080px">
        <!-- ヘッダ2 -->
        <c-v-row no-gutters>
          <c-v-col
            cols="6"
            style="align-content: center"
          >
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Oneway"
              style="background: transparent"
            ></base-mo01338>
          </c-v-col>
          <c-v-col cols="6">
            <g-custom-or-30980
              v-bind="or30980"
              v-model="or30980Type"
              :oneway-model-value="localOneway.or30980Oneway"
              style="background: transparent"
            ></g-custom-or-30980>
          </c-v-col>
        </c-v-row>
        <!-- ヘッダ2 -->
        <c-v-row
          no-gutters
          class="mt-4"
        >
          <c-v-col cols="6">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338OnewayEvaluationTip"
              style="background: transparent"
            ></base-mo01338>
          </c-v-col>
          <c-v-col
            cols="6"
            style="text-align: right"
          >
            <base-mo01338
              :oneway-model-value="localOneway.mo01338OnewayInputTip"
              style="background: transparent"
            ></base-mo01338>
          </c-v-col>
        </c-v-row>
        <!-- コンテンツエリア -->
        <c-v-sheet class="content-area">
          <!-- A．基本情報(コンテンツ) -->
          <c-v-row>
            <c-v-col class="pa-0">
              <!-- タイトル -->
              <c-v-row>
                <c-v-col class="header-col-base pl-6 pt-6 pb-7">
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionTitle"
                  ></base-mo01338>
                </c-v-col>
              </c-v-row>
              <!-- A4．婚姻状況-->
              <c-v-row>
                <c-v-col class="pa-0">
                  <!-- タイトル-->
                  <c-v-row class="title">
                    <c-v-col class="pl-6 pr-6 item_title">
                      <base-mo01338
                        :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionA4Title"
                        style="background: transparent"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row class="area pl-12 pr-6">
                    <c-v-col class="pa-0">
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <g-custom-or-30982
                            v-model="or30982TypeA4"
                            :oneway-model-value="localOneway.or30982OnewayTypeA4Btns"
                          ></g-custom-or-30982>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0">
                          <g-custom-or-30981
                            v-bind="or30981_1"
                            v-model="or30981TypeA4Area"
                            :oneway-model-value="localOneway.or30981OnewayTypeA4"
                            @on-click-edit-btn="careTargetInputSupportIconClick('A4')"
                          ></g-custom-or-30981>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- A7．要介護度 -->
              <c-v-row>
                <c-v-col class="pa-0">
                  <!-- タイトル-->
                  <c-v-row class="title">
                    <c-v-col class="pl-6 pr-6 item_title">
                      <base-mo01338
                        :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionA7Title"
                        style="background: transparent"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row class="area pl-12 pr-6">
                    <c-v-col class="pa-0">
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <g-custom-or-30982
                            v-model="or30982TypeA7"
                            :oneway-model-value="localOneway.or30982OnewayTypeA7Btns"
                          ></g-custom-or-30982>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0">
                          <g-custom-or-30981
                            v-bind="or30981_2"
                            v-model="or30981TypeA7Area"
                            :oneway-model-value="localOneway.or30981OnewayTypeA7"
                            @on-click-edit-btn="careTargetInputSupportIconClick('A7')"
                          ></g-custom-or-30981>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- A8．アセスメントの理由 -->
              <c-v-row>
                <c-v-col class="pa-0">
                  <!-- タイトル-->
                  <c-v-row class="title">
                    <c-v-col class="pl-6 pr-6 item_title">
                      <base-mo01338
                        :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionA8Title"
                        style="background: transparent"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row class="area pl-12 pr-6">
                    <c-v-col class="pa-0">
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <g-custom-or-30982
                            v-model="or30982TypeA8"
                            :oneway-model-value="localOneway.or30982OnewayTypeA8Btns"
                          ></g-custom-or-30982>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0">
                          <g-custom-or-30981
                            v-bind="or30981_3"
                            v-model="or30981TypeA8Area"
                            :oneway-model-value="localOneway.or30981OnewayTypeA8"
                            @on-click-edit-btn="careTargetInputSupportIconClick('A8')"
                          ></g-custom-or-30981>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- A10.本人のケアの目標サブセクション -->
              <c-v-row>
                <c-v-col class="pa-0">
                  <!-- タイトル-->
                  <c-v-row class="title">
                    <c-v-col class="pl-6 pr-6 item_title">
                      <base-mo01338
                        :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionA10Title"
                        style="background: transparent"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row class="area pl-12">
                    <c-v-col class="pa-0">
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <g-custom-or-x-0156
                            v-model="orX0156TypeA10Area"
                            :oneway-model-value="localOneway.orX0156OnewayTypeA10"
                            style="width: 903px"
                            @on-click-edit-btn="careTargetInputSupportIconClick('A10')"
                          ></g-custom-or-x-0156>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- A11.アセスメント時の居住場所サブセクション -->
              <c-v-row>
                <c-v-col class="pa-0">
                  <!-- タイトル-->
                  <c-v-row class="title">
                    <c-v-col class="pl-6 pr-6 item_title">
                      <base-mo01338
                        :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionA11Title"
                        style="background: transparent"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row class="area pl-12 pr-6">
                    <c-v-col class="pa-0">
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <g-custom-or-30982
                            v-model="or30982TypeA11"
                            :oneway-model-value="localOneway.or30982OnewayTypeA11Btns"
                          ></g-custom-or-30982>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0">
                          <g-custom-or-30981
                            v-bind="or30981_4"
                            v-model="or30981TypeA11Area"
                            :oneway-model-value="localOneway.or30981OnewayTypeA11"
                            @on-click-edit-btn="careTargetInputSupportIconClick('A11')"
                          ></g-custom-or-30981>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- A12.同居形態サブセクション -->
              <c-v-row>
                <c-v-col class="pa-0">
                  <!-- タイトル-->
                  <c-v-row class="title">
                    <c-v-col class="pl-6 pr-6 item_title">
                      <base-mo01338
                        :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionA12Title"
                        style="background: transparent"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <!-- 同居形態 a.同居者 -->
                  <c-v-row class="area pl-12 pr-6">
                    <c-v-col class="pa-0">
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <base-mo01338
                            :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionA12ATitle"
                            style="background: transparent"
                          ></base-mo01338>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0 pl-6">
                          <g-custom-or-30982
                            v-model="or30982TypeA12A"
                            :oneway-model-value="localOneway.or30982OnewayTypeA12ABtns"
                          ></g-custom-or-30982>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0 pl-6">
                          <g-custom-or-30981
                            v-bind="or30981_5"
                            v-model="or30981TypeA12AArea"
                            :oneway-model-value="localOneway.or30981OnewayTypeA12A"
                            @on-click-edit-btn="careTargetInputSupportIconClick('A12a')"
                          ></g-custom-or-30981>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                  <!-- 同居形態 b.変化 -->
                  <c-v-row
                    class="area pl-12 pr-6"
                    style="padding: 0px 48px"
                  >
                    <c-v-col class="pa-0">
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <base-mo01338
                            :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionA12BTitle"
                            style="background: transparent"
                          ></base-mo01338>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0 pl-6">
                          <g-custom-or-30982
                            v-model="or30982TypeA12B"
                            :oneway-model-value="localOneway.or30982OnewayTypeA12BBtns"
                          ></g-custom-or-30982>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0 pl-6">
                          <g-custom-or-30981
                            v-bind="or30981_6"
                            v-model="or30981TypeA12BArea"
                            :oneway-model-value="localOneway.or30981OnewayTypeA12B"
                            @on-click-edit-btn="careTargetInputSupportIconClick('A12b')"
                          ></g-custom-or-30981>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                  <!-- 同居形態 c.他の居住 -->
                  <c-v-row class="area pl-12 pr-6">
                    <c-v-col class="pa-0">
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <base-mo01338
                            :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionA12CTitle"
                            style="background: transparent"
                          ></base-mo01338>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0 pl-6">
                          <g-custom-or-30982
                            v-model="or30982TypeA12C"
                            :oneway-model-value="localOneway.or30982OnewayTypeA12CBtns"
                          ></g-custom-or-30982>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0 pl-6">
                          <g-custom-or-30981
                            v-bind="or30981_7"
                            v-model="or30981TypeA12CArea"
                            :oneway-model-value="localOneway.or30981OnewayTypeA12C"
                            @on-click-edit-btn="careTargetInputSupportIconClick('A12c')"
                          ></g-custom-or-30981>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- A13.退院後の経過期間サブセクション -->
              <c-v-row>
                <c-v-col class="pa-0">
                  <!-- タイトル-->
                  <c-v-row class="title">
                    <c-v-col class="pl-6 pr-6 item_title">
                      <base-mo01338
                        :oneway-model-value="localOneway.mo01338OnewayBasicInfoSectionA13Title"
                        style="background: transparent"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row class="area pl-12 pr-6 pb-12">
                    <c-v-col class="pa-0">
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <g-custom-or-30982
                            v-model="or30982TypeA13"
                            :oneway-model-value="localOneway.or30982OnewayTypeA13Btns"
                          ></g-custom-or-30982>
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="mt-6">
                        <c-v-col class="pa-0">
                          <g-custom-or-30981
                            v-bind="or30981_8"
                            v-model="or30981TypeA13Area"
                            :oneway-model-value="localOneway.or30981OnewayTypeA13"
                            @on-click-edit-btn="careTargetInputSupportIconClick('A13')"
                          ></g-custom-or-30981>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
        </c-v-sheet>
      </div>
      <!-- interRAIロゴ -->
      <c-v-row class="mt-4">
        <c-v-col class="pa-0">
          <c-v-img
            width="128"
            aspect-ratio="16/9"
            cover
            :src="InterRAI"
            style="float: right"
          ></c-v-img>
        </c-v-col>
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <!-- GUI00937_入力支援［ケアマネ］ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Type"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="or51775Confirm"
  />
</template>
<style scoped lang="scss">
.content {
  display: flex;
  height: 100%;
  width: 100%;

  .content-area {
    width: 100%;
    margin-top: 16px;
    max-height: 3747px;
    background: #FFFFFF;

    .title {
      border: 1px rgb(var(--v-theme-black-200)) solid;
      background: #e6e6e6;
      height: 45px;
      align-content: center;

      .item_title {
        height: 48px;
        align-content: center;
      }
    }

    .area {
      padding-top: 24px;
      padding-bottom: 24px;

      :deep(.textarea-container)>:first-child {
        overflow: hidden;

        textarea {
          height: 63px;
        }
      }
    }
  }
}

.v-row {
  margin: 0px;
}

.v-col {
  padding: 8px;
}
</style>
