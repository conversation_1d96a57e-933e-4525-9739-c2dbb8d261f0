<script setup lang="ts">
/**
 * Or26311: 有機体: 印刷順変更モーダル
 * GUI01010_印刷順変更
 *
 * @description
 * [印刷順変更] ボタンをクリックすると、印刷順変更モーダルが表示される。
 * [印刷順変更] このモーダルでは、印刷順を変更することができる。
 * [印刷順変更] また、[削除] ボタンをクリックすると、印刷順を全て削除することができる。
 *
 * <AUTHOR> DO DUC MANH
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26311Const } from './Or26311.constants'
import type { DataTableData, Or26311StateType, PrintOrderModifiedTableData } from './Or26311.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Or26311Type, Or26311OnewayType } from '~/types/cmn/business/components/Or26311Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { OrX0002OnewayType } from '~/types/cmn/business/components/OrX0002Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import { useScreenOneWayBind, useScreenTwoWayBind, useScreenStore } from '#imports'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01278Type } from '~/types/business/components/Mo01278Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or26311Type
  onewayModelValue: Or26311OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or26311OnewayType = {
  printOrderModifiedType: [
    {
      displayOrder: '',
      carePlanId: '',
      carePlanPrintTitle: '',
    },
  ],
}

const localOneway = reactive({
  Or26311: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo01316Oneway: {
    items: [],
  },
  // 加算情報ダイアログ
  mo00024Oneway: {
    width: '496px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or26311',
      toolbarTitle: t('label.print-order-modified'),
      toolbarName: 'Or26311ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00611DeleteRowBtnOneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
  } as Mo00611OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
  orX0002Oneway: {
    message: t('message.i-cmn-11275'),
    mo00024Oneway: {
      persistent: true,
    } as Mo00024OnewayType,
  } as OrX0002OnewayType,
  mo01265Oneway: {
    btnLabel: t('btn.delete'),
    disabled: false,
    tooltipText: t('tooltip.delete-btn'),
  } as Mo01265OnewayType,
})

const columnMinWidth = ref<number[]>([80, 400])

const emit = defineEmits(['update:modelValue'])

const mo00024 = ref<Mo00024Type>({
  isOpen: Or26311Const.DEFAULT.IS_OPEN,
})
const selectedItemIndex = ref<number>(-1)

const { refValue } = useScreenTwoWayBind<DataTableData>({
  cpId: Or26311Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// テーブルヘッダ
const headers = [
  { title: t('label.print-order'), key: 'printOder', sortable: false, width: '80px' },
  { title: t('label.ledge-name'), key: 'ledgeName', sortable: false },
]

const { setState } = useScreenOneWayBind<Or26311StateType>({
  cpId: Or26311Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or26311Const.DEFAULT.IS_OPEN
    },
  },
})

// 削除ボタン押下時の処理
const isDeletedAllOrder = ref(false)

// 保留中の印刷順リスト
const pendingOrderList: (string | number)[] = []

const _isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})

onMounted(() => {
  init()
})
// 初期化処理
function init() {
  const dataList: PrintOrderModifiedTableData[] = []

  for (const data of localOneway.Or26311.printOrderModifiedType) {
    const item: PrintOrderModifiedTableData = {
      displayOrder: {
        modelValue: {
          value: data.displayOrder,
        } as Mo01278Type,
      },
      carePlanId: data.carePlanId,
      carePlanPrintTitle: {
        onewayModelValue: {
          value: data.carePlanPrintTitle,
          unit: '',
        } as Mo01337OnewayType,
      },
    }
    dataList.push(item)
  }

  useScreenStore().setCpTwoWay({
    cpId: Or26311Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: {
      listData: dataList,
    },
    isInit: true,
  })

  // 保留中の印刷順リストを初期化
  pendingOrderList.length = 0
  const listLength = refValue.value?.listData?.length ?? 0
  for (let i = 1; i <= listLength; i++) {
    pendingOrderList.push(i.toString())
  }
}

const tableDataFilter = computed(() => {
  return {
    listData: refValue.value?.listData,
  }
})

watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClose()
    }
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function onClose() {
  setState({ isOpen: false })
}

/**
 * フォーカスイベント
 *
 * @param item - フォーカスしたアイテム
 */
function onFocusEvent(item: PrintOrderModifiedTableData) {
  if (isDeletedAllOrder.value && item.displayOrder?.modelValue?.value === '') {
    item.displayOrder.modelValue.value = pendingOrderList[0]
    pendingOrderList.shift()
  }
}

/**
 * 数値入力イベント
 *
 * @param item - 入力したアイテム
 */
function onInputNumber(item: PrintOrderModifiedTableData) {
  // 数値以外の文字を削除
  const value = item.displayOrder?.modelValue?.value.toString()
  if (item?.displayOrder?.modelValue) {
    item.displayOrder.modelValue.value = value?.replace(/[^0-9]/g, '') ?? ''
  }
}

// 確認ボタン押下時の処理
function onConfirm() {
  // 保留中の印刷順リストを初期化
  pendingOrderList.length = 0
  const listLength = refValue.value?.listData?.length ?? 0
  for (let i = 1; i <= listLength; i++) {
    pendingOrderList.push(i.toString())
  }

  // テーブルデータを処理用データに変換
  const processedData: Or26311Type = { printOrderModifiedList: [] }

  for (const rowData of tableDataFilter.value.listData ?? []) {
    processedData.printOrderModifiedList.push({
      displayOrder: rowData.displayOrder?.modelValue?.value?.toString() ?? '',
      carePlanId: rowData.carePlanId,
      carePlanPrintTitle: rowData.carePlanPrintTitle.onewayModelValue.value ?? '',
    })
  }

  // データの長さを取得
  const totalRows = processedData.printOrderModifiedList.length

  // データを2つの配列に分割
  const validRows = processedData.printOrderModifiedList.filter((item) => {
    return Number(item.displayOrder) > 0 && Number(item.displayOrder) <= totalRows
  })
  const invalidRows = processedData.printOrderModifiedList.filter((item) => {
    return Number(item.displayOrder) > totalRows || Number(item.displayOrder) <= 0
  })

  // validRowsをdisplayOrderでソート
  validRows.sort((a, b) => {
    return Number(a.displayOrder) - Number(b.displayOrder)
  })

  // invalidRowsをdisplayOrderでソート
  // displayOrderが空の場合は最後に移動
  invalidRows.sort((a, b) => {
    if (a.displayOrder === '' && b.displayOrder === '') {
      return 0
    }
    if (a.displayOrder === '') {
      return 1
    }
    if (b.displayOrder === '') {
      return -1
    }
    return Number(a.displayOrder) - Number(b.displayOrder)
  })

  pendingOrderList.splice(0, validRows.length)

  for (let i = 0; i < invalidRows.length; i++) {
    invalidRows[i].displayOrder = pendingOrderList[i]?.toString()
  }

  const returnData = validRows.concat(invalidRows)

  const formReturnData = {
    printOrderModifiedType: returnData,
  }
  // 親コンポーネントにデータを送信
  emit('update:modelValue', formReturnData)

  // ダイアログを閉じる
  onClose()
}

function onSelectRow(index: number) {
  selectedItemIndex.value = index
}

// 削除ボタン押下時の処理
function onDelete() {
  tableDataFilter.value.listData?.forEach((item) => {
    if (item.displayOrder?.modelValue) {
      item.displayOrder.modelValue.value = ''
    }
  })

  refValue.value?.listData.forEach((item) => {
    if (item.displayOrder?.modelValue) {
      item.displayOrder.modelValue.value = ''
    }
  })

  isDeletedAllOrder.value = true
  pendingOrderList.length = 0
  const listLength = refValue.value?.listData?.length ?? 0
  for (let i = 1; i <= listLength; i++) {
    pendingOrderList.push(i.toString())
  }
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo01265
        class="mb-2"
        :oneway-model-value="localOneway.mo01265Oneway"
        @click="onDelete"
      />
      <c-v-row>
        <c-v-col>
          <c-v-data-table
            v-resizable-grid="{ columnWidths: columnMinWidth }"
            :headers="headers"
            class="table-wrapper"
            hide-default-footer
            :items="tableDataFilter.listData"
            fixed-header
            height="400px"
            :items-per-page="-1"
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="onSelectRow(index)"
              >
                <td>
                  <base-at-text-field
                    v-model="item.displayOrder.modelValue.value"
                    solo
                    flat
                    hide-details
                    class="text-field__custom"
                    maxlength="4"
                    @focus="onFocusEvent(item)"
                    @input="onInputNumber(item)"
                  />
                </td>
                <td>
                  <base-mo01337 :oneway-model-value="item.carePlanPrintTitle.onewayModelValue" />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="onClose()"
        />
        <base-mo00609
          :oneway-model-value="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="onConfirm()"
        />
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';

.text-field__custom {
  height: 100%;

  :deep(.v-field__input) {
    text-align: right;
    cursor: pointer;
    height: 100%;
    padding: 8px !important;
  }

  :deep(.v-field__outline) {
    display: none !important;
  }

  :deep(.v-field--focused) {
    color: unset !important;
    box-shadow: none !important;
  }
}
:deep(.v-data-table tr th),
:deep(.v-data-table tr td) {
  height: 32px !important;
}
</style>
