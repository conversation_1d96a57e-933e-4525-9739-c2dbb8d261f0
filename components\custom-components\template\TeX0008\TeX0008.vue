<script lang="ts" setup>
/**
 * GUI00834_アセスメント（包括）画面
 *
 * @description
 * ［アセスメント（包括）］テンプレート、以下のコンポーネントをタブで表示する。
 *
 * ［アセスメント（包括）］食事画面
 * ［アセスメント（包括）］排泄画面
 * ［アセスメント（包括）］入浴画面
 * ［アセスメント（包括）］洗面画面
 * ［アセスメント（包括）］基本画面
 * ［アセスメント（包括）］医療画面
 * ［アセスメント（包括）］心理画面
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrT0003Const } from '../../organisms/OrT0003/Ort0003.constants'
import { Or13850Const } from '../../organisms/Or13850/Or13850.constants'
import { Or13844Const } from '../../organisms/Or13844/Or13844.constants'
import { Or13872Const } from '../../organisms/Or13872/Or13872.constants'
import type { OrT0003OneWayType, OrT0003Type } from '../../organisms/OrT0003/OrT0003.type'
import type { IssuseWholeItemType } from '../../organisms/OrX0096/OrX0096.type'
import { Or03240Const } from '../../organisms/Or03240/Or03240.constants'
import { Or03249Const } from '../../organisms/Or03249/Or03249.constants'
import { Or03250Const } from '../../organisms/Or03250/Or03250.constants'
import { Or03243Const } from '../../organisms/Or03243/Or03243.constants'
import { Or03244Const } from '../../organisms/Or03244/Or03244.constants'
import type { Or03243OnewayType } from '../../organisms/Or03243/Or34243.type'
import type { Or03240OnewayType } from '../../organisms/Or03240/Or03240.type'
import { Or03237Const } from '../../organisms/Or03237/Or03237.constants'
import type { Or03237OnewayType } from '../../organisms/Or03237/Or03237.type'
import { Or03245Const } from '../../organisms/Or03245/Or03245.constants'
import type { Or03245OnewayType } from '../../organisms/Or03245/Or03245.type'
import type {
  assessmentComprehensiveMealIHistoryChangeInEntity,
  assessmentComprehensiveMealIHistoryChangeOutEntity,
  assessmentComprehensiveMealIPeriodChangeSelectInEntity,
  assessmentComprehensiveMealIPeriodChangeSelectOutEntity,
} from '../../../../repositories/cmn/entities/assessmentComprehensiveMealIPeriodChangeSelect'
import { Or13872Logic } from '../../organisms/Or13872/Or13872.logic'
import { Or13844Logic } from '../../organisms/Or13844/Or13844.logic'
import type { Or13872EventType } from '../../organisms/Or13872/Or13872.type'
import type { Or13844EventType } from '../../organisms/Or13844/Or13844.type'
import { OrX0001Const } from '../../organisms/OrX0001/OrX0001.constants'
import { OrX0001Logic } from '../../organisms/OrX0001/OrX0001.logic'
import { Or51885Const } from '../../organisms/Or51885/Or51885.constants'
import { Or51885Logic } from '../../organisms/Or51885/Or51885.logic'
import { Or27216Const } from '../../organisms/Or27216/Or27216.constants'
import { Or27216Logic } from '../../organisms/Or27216/Or27216.logic'
import type { Or03244OnewayType } from '../../organisms/Or03244/Or34244.type'
import { Or59423Const } from '../../organisms/Or59423/Or59423.constants'
import { Or59423Logic } from '../../organisms/Or59423/Or59423.logic'
import type { Or59423ConfirmType } from '../../organisms/Or59423/Or59423.type'
import { Or52060Const } from '../../organisms/Or52060/Or52060.constants'
import { Or52060Logic } from '../../organisms/Or52060/Or52060.logic'
import type { Or52060Param } from '../../organisms/Or52060/Or52060.type'
import { Or27825Logic } from '../../organisms/Or27825/Or27825.logic'
import { OrX0115Const } from '../../organisms/OrX0115/OrX0115.constants'
import { Or10926Const } from '../../organisms/Or10926/Or10926.constants'
import { OrX0115Logic } from '../../organisms/OrX0115/OrX0115.logic'
import { Or10926Logic } from '../../organisms/Or10926/Or10926.logic'
import { Or26257Const } from '../../organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '../../organisms/Or26257/Or26257.logic'
import type { Or03249OnewayType } from '../../organisms/Or03249/Or03249.type'
import { Or51105Const } from '../../organisms/Or51105/Or51105.constants'
import { Or51105Logic } from '../../organisms/Or51105/Or51105.logic'
import type { Or03250OnewayType } from '../../organisms/Or03250/Or03250.type'
import { Or41345Const } from '../../organisms/Or41345/Or41345.constants'
import { Or41345Logic } from '../../organisms/Or41345/Or41345.logic'
import type { TeX0008EventType, TeX0008IssuseWholeList, TeX0008StateType } from './TeX0008.type'
import { TeX0008Const } from './TeX0008.constants'
import { TeX0008Logic } from './TeX0008.logic'

import {
  useCmnRouteCom,
  useJigyoList,
  useScreenInitFlg,
  useScreenOneWayBind,
  useScreenStore,
  useSetupChildProps,
  useSystemCommonsStore,
  useUserListInfo,
} from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { CustomClass } from '~/types/CustomClassType'

import type { TeX0008OnewayType, TeX0008Type } from '~/types/cmn/business/components/TeX0008Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00028OnewayType } from '~/types/business/components/Mo00028Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { OrX0001OnewayType, OrX0001Type } from '~/types/cmn/business/components/OrX0001Type'
import type { Or27216OnewayType, Or27216Type } from '~/types/cmn/business/components/Or27216Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Or59423OnewayType } from '~/types/cmn/business/components/Or59423Type'
import { DIALOG_BTN, SPACE_WAVE, UPDATE_KBN } from '~/constants/classification-constants'
import type { Or13844OnewayType } from '~/types/cmn/business/components/Or13844Type'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import type { Or10926OnewayType } from '~/types/cmn/business/components/Or10926Type'
import type { Or26257OnewayType, Or26257Type } from '~/types/cmn/business/components/Or26257Type'
import type { Or13872OnewayType } from '~/types/cmn/business/components/Or13872Type'
import type { Or51105OnewayType } from '~/types/cmn/business/components/Or51105Type'
import type { OrX0157OnewayType, OrX0157Type } from '~/types/cmn/business/components/OrX0157Type'
import type { Or41345OnewayType } from '~/types/cmn/business/components/Or41345Type'
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: TeX0008OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/

const { t } = useI18n()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
// route共有情報
const cmnRouteCom = useCmnRouteCom()

// 画面情報領域のsupplement内のisInitの取得およびonMountedにfalseへ更新する処理を登録
const isInit = useScreenInitFlg()

// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()
/** 事業所変更監視 */
const { jigyoListWatch } = useJigyoList()

// 計画期間管理表示フラグ
const plannningPeriodManageFlg = ref<string>('1')

// 利用者ID
const userId = ref('')

/**
 * 新規ボタン押下回数
 */
const createNewClickNumberOfTimes = ref<number>(0)

/**
 * 削除ボタン押下フラグ
 */
const clickDeleteButtonFlg = ref<boolean>(false)

/**
 * 該当歴史のケアチェックID
 */
const careCheckId = ref<string>('')

/**
 * 該当計画期間対象ID
 */
const planPeriodId = ref<string>('')

/**
 * 画面初期化完了フラグ
 */
let screenInitFlg = false

/**
 * 新規する前のデータを一時保存、入力を廃棄することで、保存したデータを画面に設定する
 */
let srceenInfoBk = {
  careCheckId: '',
  planPeriodId: '',
  historyCount: '',
  historyNo: '',
  createUserId: '',
  createUserName: '',
  createYmd: '',
}

/**
 * 事業所詳細
 */
const officeInfo = ref<{
  houjinId: string
  shisetuId: string
  svJigyoId: string
  svJigyoCd: string
  jigyoRyakuKnj: string
}>()

/**
 * 保存後必要な操作タイプ
 */
const afterProcess: {
  /**
   * '0': 新規処理
   * '1': 複写
   * '2': マスタ画面を呼び出す
   * '3': タブ変更
   * '4': 計画期間変更
   * '5': 歴史変更
   * '6': 事業所変更処理
   * '7': 期間ID指定処理
   * '8': 履歴ID指定処理
   * '9': 優先順位ダイアログ表示
   * '10': 一覧ダイアログ表示
   * '11': 利用者変更
   * '': 処理なし「画面再表示」
   */
  type: '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | '11' | ''
  value: string
} = {
  type: '',
  value: '',
}

/**
 * 履歴操作有機体表示フラグ
 */
const historyDisplay = ref(true)

/**
 * 職員ID
 */
let staffId = ''

/**
 * 複写画面のキー
 */
const componentKey = ref('')

const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or13850 = ref({ uniqueCpId: '' })
const or13850_2 = ref({ uniqueCpId: '' })
const or13844 = ref({ uniqueCpId: '' })
const or13872 = ref({ uniqueCpId: '' })
const orT0003 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21814_2 = ref({ uniqueCpId: '' })
const or21814_3 = ref({ uniqueCpId: '' })
const or03240 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or03249 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or03250 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or03243 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or03244 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or03237 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or03245 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const orX0001 = ref({ uniqueCpId: '' })
const or51885 = ref({ uniqueCpId: '' })
const or27216 = ref({ uniqueCpId: '' })
const or41179 = ref({ uniqueCpId: '' })
const or59423 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or52060 = ref({ uniqueCpId: '' })
const orX0115 = ref({ uniqueCpId: '' })
const or10926 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' })
const or51105 = ref({ uniqueCpId: '' })
const or41345 = ref({ uniqueCpId: '' })

/**
 * 介護課題全般リスト
 * 国際化対応ため、定数ファイルから移行
 */
const issuseWholeList: TeX0008IssuseWholeList[] = [
  {
    label: t('label.assessment-comprehensive-common-problem1'),
    key: '1',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '6', '7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem2'),
    key: '2',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '6', '7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem3'),
    key: '3',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '6', '7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem4'),
    key: '4',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '6', '7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem5'),
    key: '5',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem6'),
    key: '6',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem7'),
    key: '7',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem8'),
    key: '8',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem9'),
    key: '9',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem10'),
    key: '10',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem11'),
    key: '11',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem12'),
    key: '12',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem13'),
    key: '13',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['2'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem14'),
    key: '14',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['2'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem15'),
    key: '15',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['2'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem16'),
    key: '16',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['2'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem17'),
    key: '17',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['2'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem18'),
    key: '18',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['3'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem19'),
    key: '19',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['3'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem20'),
    key: '20',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['3'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem21'),
    key: '21',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['3'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem22'),
    key: '109',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem23'),
    key: '22',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem24'),
    key: '23',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem25'),
    key: '24',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem26'),
    key: '25',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem27'),
    key: '26',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem28'),
    key: '27',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem29'),
    key: '28',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem30'),
    key: '29',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['5'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem31'),
    key: '30',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['5'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem32'),
    key: '31',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['5'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem33'),
    key: '32',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['5'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem34'),
    key: '33',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['5'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem35'),
    key: '34',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem36'),
    key: '35',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem37'),
    key: '36',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem38'),
    key: '37',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem39'),
    key: '38',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem40'),
    key: '39',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem41'),
    key: '40',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem42'),
    key: '41',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem43'),
    key: '42',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem44'),
    key: '43',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem45'),
    key: '44',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem46'),
    key: '45',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem47'),
    key: '46',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem48'),
    key: '47',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem49'),
    key: '48',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem50'),
    key: '49',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem51'),
    key: '50',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem52'),
    key: '51',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem53'),
    key: '52',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem54'),
    key: '53',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem55'),
    key: '54',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem-other'),
    key: '55',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '6', '7'],
  },
]

/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(0)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or13850Const.CP_ID(0)]: or13850.value,
  [Or13872Const.CP_ID(0)]: or13872.value,
  [Or13850Const.CP_ID(1)]: or13850_2.value,
  [OrT0003Const.CP_ID(0)]: orT0003.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or21814Const.CP_ID(3)]: or21814_3.value,
  [Or03243Const.CP_ID(0)]: or03243.value,
  [Or03240Const.CP_ID(0)]: or03240.value,
  [Or03237Const.CP_ID(0)]: or03237.value,
  [Or03245Const.CP_ID(0)]: or03245.value,
  [OrX0001Const.CP_ID(1)]: orX0001.value,
  [Or03249Const.CP_ID(0)]: or03249.value,
  [Or03244Const.CP_ID(0)]: or03244.value,
  [Or51885Const.CP_ID(1)]: or51885.value,
  [Or03250Const.CP_ID(0)]: or03250.value,
  [Or27216Const.CP_ID(1)]: or27216.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
  [Or59423Const.CP_ID(1)]: or59423.value,
  [Or52060Const.CP_ID(1)]: or52060.value,
  [OrX0115Const.CP_ID(1)]: orX0115.value,
  [Or10926Const.CP_ID(1)]: or10926.value,
  [Or26257Const.CP_ID(1)]: or26257.value,
  [Or51105Const.CP_ID(1)]: or51105.value,
  [Or13844Const.CP_ID(0)]: or13844.value,
  [Or41345Const.CP_ID(2)]: or41345.value,
})

const { setState } = useScreenOneWayBind<TeX0008StateType>({
  cpId: TeX0008Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// ★利用者一覧詳細表示(Or00248)の「領域キー」を設定
Or00248Logic.state.set({
  uniqueCpId: or00248.value.uniqueCpId,
  state: {
    regionKey: props.uniqueCpId, // 「領域キー」を設定
  },
})

systemCommonsStore.setUserSelectZenkaiSvJigyoIdList([''])
systemCommonsStore.setUserSelectSelfId('')
/*********************************************
 * computed
 ********************************************/
// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOrX0096_2 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen ?? false
})
// 一覧ダイアログ表示フラグ
const showDialogOr27216 = computed(() => {
  // Or27216 のダイアログ開閉状態
  return Or27216Logic.state.get(or27216.value.uniqueCpId)?.isOpen ?? false
})
// 複写ダイアログ
const showDialogOr59423 = computed(() => {
  // Or59423 のダイアログ開閉状態
  return Or59423Logic.state.get(or59423.value.uniqueCpId)?.isOpen ?? false
})
// 印刷設定ダイアログ
const showDialogOr52060 = computed(() => {
  return Or52060Logic.state.get(or52060.value.uniqueCpId)?.isOpen ?? false
})
// マスタ画面
const showDialogOr41345 = computed(() => {
  return Or27825Logic.state.get(or41345.value.uniqueCpId)?.isOpen ?? false
})
// 計画期間選択画面
const showDialogOrX0115 = computed(() => {
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})
// 歴史情報選択画面
const showDialogOr10926 = computed(() => {
  return Or10926Logic.state.get(or10926.value.uniqueCpId)?.isOpen ?? false
})
// 職員選択画面
const showDialogOr26257 = computed(() => {
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})
// 優先順位画面
const showDialogOr51105 = computed(() => {
  return Or51105Logic.state.get(or51105.value.uniqueCpId)?.isOpen ?? false
})
// 記号意味画面
const showDialogOr51885 = computed(() => {
  return Or51885Logic.state.get(or51885.value.uniqueCpId)?.isOpen ?? false
})
// 削除確認画面
const showDialogOrX0001 = computed(() => {
  return OrX0001Logic.state.get(orX0001.value.uniqueCpId)?.isOpen ?? false
})
// 具体的内容
// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  let editFlg = false
  switch (local.mo00043.id) {
    case Or03243Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or03243.value.uniqueCpId)
      break
    case Or03237Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or03237.value.uniqueCpId)
      break
    case Or03249Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or03249.value.uniqueCpId)
      break
    case Or03250Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or03250.value.uniqueCpId)
      break
    case Or03240Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or03240.value.uniqueCpId)
      break
    case Or03244Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or03244.value.uniqueCpId)
      break
    case Or03245Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or03245.value.uniqueCpId)
      break
  }
  return editFlg
})
// ロカールTwoway
const local = reactive({
  orT0003: {} as OrT0003Type,
  // 作成日
  // 作成日
  createDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // タブ
  mo00043: {
    id: '',
  } as Mo00043Type,
  // 削除ダイアログ
  orX0001Type: {
    deleteSyubetsu: '',
  } as OrX0001Type,
  or27216: {
    contentDetailList: [],
  } as Or27216Type,
  orX0157: {
    value: '',
  } as OrX0157Type,
})

// ロカールOneway
const localOneway = reactive({
  orT0003Oneway: {
    createDataList: [],
    termid: 0,
  } as OrT0003OneWayType,
  // 作成日
  createDateOneway: {
    itemLabel: t('label.create-date'),
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-row',
      labelClass: 'ma-1',
    }),
    disabled: clickDeleteButtonFlg.value,
  } as Mo00020OnewayType,
  mo00043Oneway: {
    tabItems: [],
  } as Mo00043OnewayType,
  // 要介護課題等々
  mo00028OnewayType: {
    panelTitle: t('label.assessment-comprehensive-pannel-title'),
    customClass: new CustomClass({
      itemClass: 'bold',
    }),
  } as Mo00028OnewayType,
  or03249Oneway: {
    orX0096CpId: '',
  } as Or03249OnewayType,
  or03240Oneway: {
    orX0096CpId: '',
  } as Or03240OnewayType,
  or03244Oneway: {
    orX0096CpId: '',
  } as Or03244OnewayType,
  or03243Oneway: {
    orX0096CpId: '',
  } as Or03243OnewayType,
  or03237Oneway: {
    orX0096CpId: '',
  } as Or03237OnewayType,
  or03245Oneway: {
    orX0096CpId: '',
  } as Or03245OnewayType,
  or03250Oneway: {
    orX0096CpId: '',
  } as Or03250OnewayType,
  // 削除ダイアログ
  orX0001Oneway: {
    createYmd: local.createDate.value,
    kinouKnj: t('label.assessment'),
    selectTabName: '',
    startTabName: t('label.assessment-comprehensive-tab-name-1'),
    endTabName: t('label.assessment-comprehensive-tab-name-7'),
  } as OrX0001OnewayType,
  // 記号と意味ダイアログ
  or51885Oneway: {
    cc1Id: '',
    sc1Id: '',
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
    userID: '',
    shokuId: '',
  },
  /**
   * 一覧画面ダイアログ
   */
  or27216Oneway: {
    cc1Id: parseInt(careCheckId.value),
    sc1Id: parseInt(planPeriodId.value),
  } as Or27216OnewayType,
  or5942Oneway: {} as Or59423OnewayType,
  /** 計画期間 */
  or13844Oneway: {
    plainningPeriodManageFlg: '1',
    planningPeriodInfo: {},
    pageBtnAutoDisabled: false,
    showLabelMode: false,
    labelAutoDisabled: true,
  } as Or13844OnewayType,
  /** 履歴変更 */
  or13872Oneway: {
    historyInfo: {},
    pageBtnAutoDisabled: false,
    showLabelMode: false,
  } as Or13872OnewayType,
  // or13850Oneway: {
  //   itemTitle: t('label.author'),
  //   iconBtnDisabled: false,
  //   id: '',
  //   name: '',
  //   width: '',
  //   titleWidth: '',
  //   showLabelMode: false,
  // } as Or13850OnewayType,
  // マスタ画面
  or41345Oneway: {} as Or41345OnewayType,
  orX0115Oneway: {
    kindId: '',
    sc1Id: '',
  } as OrX0115OnewayType,
  or10926Oneway: {} as Or10926OnewayType,
  or26257Oneway: {} as Or26257OnewayType,
  or51105Oneway: {} as Or51105OnewayType,
  orX0157Oneway: {
    inputMode: 'TextOnly',
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        isVerticalLabel: true,
        showItemLabel: true,
        itemLabel: t('label.author'),
        customClass: new CustomClass({
          labelClass: 'pb-1',
          labelStyle: 'background-color:rgb(var(--v-theme-background));',
        }),
        width: '220px',
      },
    },
    inputReadonly: true,
  } as OrX0157OnewayType,
})

/**
 * 作成日バックアップ値
 */
const createDateBk = ref('')

/*********************************************
 * 関数定義
 ********************************************/
/**
 * 画面IDより、介護課題リストを作成する
 *
 * @param screenId - 画面ID
 */
const createIssuseTabList = (screenId: string): IssuseWholeItemType[] => {
  let b4cd = 0
  if (screenId === Or03243Const.DEFAULT.TAB_ID) {
    b4cd = 1
  } else if (screenId === Or03250Const.DEFAULT.TAB_ID) {
    b4cd = 14
  } else if (screenId === Or03249Const.DEFAULT.TAB_ID) {
    b4cd = 25
  } else if (screenId === Or03245Const.DEFAULT.TAB_ID) {
    b4cd = 35
  } else if (screenId === Or03240Const.DEFAULT.TAB_ID) {
    b4cd = 49
  } else if (screenId === Or03237Const.DEFAULT.TAB_ID) {
    b4cd = 60
  } else if (screenId === Or03244Const.DEFAULT.TAB_ID) {
    b4cd = 76
  }
  const resultArr = issuseWholeList
    .filter((issuseItem) => issuseItem.showScreenId?.includes(screenId))
    .map((issuseItem, index): IssuseWholeItemType => {
      return {
        ...issuseItem,
        b4cd: String(index + b4cd),
        modifiedCnt: '0',
        cc31Id: '0',
      }
    })
  return resultArr
}
/**
 * 確認ダイアログを呼び出す
 *
 * @param dialogText - 表示したダイアログテキスト
 */
const setShowDialog = (dialogText?: string) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: dialogText,
      firstBtnLabel: t('btn.yes'),
      firstBtnType: 'normal1',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
}

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで処理を実行する
 *
 * @param newSelfId - 変更後の利用者番号
 */
const callbackUserChange = (newSelfId: string) => {
  if (newSelfId !== '' && userId.value === '') {
    void userChange(newSelfId)
    return
  }
  // 変更がない場合、スキップ
  if (newSelfId === userId.value) {
    return
  }
  // 利用者変更処理
  void userChange(newSelfId)
}

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange, props.uniqueCpId)

/**
 * 「利用者」データ切替場合
 *
 * @param newSelfId - 変更後の利用者番号
 */
const userChange = async (newSelfId: string) => {
  // 初期化の場合、チェックなしで処理を続行
  if (screenInitFlg === false) {
    userId.value = newSelfId
    // 共通情報に保存する
    setCommonInfo({ userId: newSelfId })

    systemCommonsStore.setUserSelectSelfId(newSelfId)
    await getInitDataInfo()
    // 初期化完了フラグをtrueに設定する
    screenInitFlg = true

    // 処理終了
    return
  }
  // 画面共通情報.計画対象期間ID（ゼロに設定）
  // 画面共通情報.ケアチェックID（ゼロに設定）
  setCommonInfo({
    sc1Id: '0',
    cc1Id: '0',
  })

  // 画面データ変更ありの場合
  switch (await getDataChangeResult()) {
    case TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE:
      // 変更なし、初期化を実行する
      userId.value = newSelfId
      // 共通情報に保存する
      setCommonInfo({ userId: newSelfId })
      // システム共通情報に保存する
      systemCommonsStore.setUserSelectSelfId(newSelfId, props.uniqueCpId)
      await getInitDataInfo()
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
      // 入力内容を廃棄して、初期化処理を行う
      userId.value = newSelfId
      // 共通情報に保存する
      setCommonInfo({ userId: newSelfId })
      // システム共通情報に保存する
      systemCommonsStore.setUserSelectSelfId(newSelfId, props.uniqueCpId)
      await getInitDataInfo()
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
      // 入力内容を保存する、初期化処理を行う
      setEvent({ saveEventFlg: true })
      userId.value = newSelfId
      // 共通情報に保存する
      setCommonInfo({ userId: newSelfId })
      // システム共通情報に保存する
      systemCommonsStore.setUserSelectSelfId(newSelfId, props.uniqueCpId)
      // 保存後処理タイプを一時保存する
      afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.USER_CHANGE
      break
  }
}

/**
 * コントロール初期化
 */
const initControl = () => {
  local.createDate.value =
    systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
  createDateBk.value = local.createDate.value

  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })

  // 画面用タブネーム初期化
  localOneway.mo00043Oneway.tabItems = [
    {
      id: Or03243Const.DEFAULT.TAB_ID,
      title: t('label.assessment-comprehensive-tab-name-1'),
      tooltipText: t('label.assessment-comprehensive-tab-name-1'),
      tooltipLocation: 'bottom',
    },
    {
      id: Or03250Const.DEFAULT.TAB_ID,
      title: t('label.assessment-comprehensive-tab-name-2'),
      tooltipText: t('label.assessment-comprehensive-tab-name-2'),
      tooltipLocation: 'bottom',
    },
    {
      id: Or03249Const.DEFAULT.TAB_ID,
      title: t('label.assessment-comprehensive-tab-name-3'),
      tooltipText: t('label.assessment-comprehensive-tab-name-3'),
      tooltipLocation: 'bottom',
    },
    {
      id: Or03245Const.DEFAULT.TAB_ID,
      title: t('label.assessment-comprehensive-tab-name-4'),
      tooltipText: t('label.assessment-comprehensive-tab-name-4'),
      tooltipLocation: 'bottom',
    },
    {
      id: Or03240Const.DEFAULT.TAB_ID,
      title: t('label.assessment-comprehensive-tab-name-5'),
      tooltipText: t('label.assessment-comprehensive-tab-name-5'),
      tooltipLocation: 'bottom',
    },
    {
      id: '6',
      title: t('label.assessment-comprehensive-tab-name-6'),
      tooltipText: t('label.assessment-comprehensive-tab-name-6'),
      tooltipLocation: 'bottom',
    },
    {
      id: '7',
      title: t('label.assessment-comprehensive-tab-name-7'),
      tooltipText: t('label.assessment-comprehensive-tab-name-7'),
      tooltipLocation: 'bottom',
    },
  ]

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 削除ダイアログ初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 削除ダイアログ初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 子画面用テーブルデータを作成する
  setChildrenScreenData()
  setCommonInfo({
    /**
     * 計画期間管理フラグ
     */
    planPeriodFlg: TeX0008Const.DEFAULT.PLANNING_PERIOD_MANAGE,
    /**
     * 種別ID
     */
    syubetsuId: systemCommonsStore.getSyubetu,
    /** システム略称 */
    systemAbbreviation: systemCommonsStore.getSystemAbbreviation ?? '',
  })
  //
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      screenTitleLabel: t('label.assessment-comprehensive-master-title'),
      showFavorite: true,
      showViewSelect: false,
      viewSelectItems: [],
      showSaveBtn: true,
      showCreateBtn: true,
      showCreateMenuCopy: false,
      showPrintBtn: true,
      showMasterBtn: true,
      showOptionMenuBtn: true,
      showOptionMenuDelete: true,
      tooltipTextSaveBtn: t('tooltip.save'),
      tooltipTextPrintBtn: t('tooltip.care-plan2-print-setting-btn'),
      tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
      // disabledSaveBtn: !(await hasRegistAuth()),
      // disabledPrintBtn: !(await hasPrintAuth()),
    },
  })
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
const callbackFuncJigyo = (newJigyoId: string) => {
  if (newJigyoId !== '') {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const jigyoInfo = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)

    if (jigyoInfo) {
      if (!officeInfo.value) {
        // 初期表示の場合、共通情報に設定する
        officeInfo.value = jigyoInfo
        setCommonInfo({
          houjinId: jigyoInfo.houjinId,
          shisetuId: jigyoInfo.shisetuId,
          svJigyoId: jigyoInfo.svJigyoId,
          svjigyoName: jigyoInfo.jigyoRyakuKnj,
        })
      } else {
        // ・画面共通情報をクリア ※画面共通情報については、すべてのタブ間で共有する
        // 画面共通情報.計画対象期間ID（ゼロに設定）
        // 画面共通情報.ケアチェックID（ゼロに設定）
        setCommonInfo({
          sc1Id: '0',
          cc1Id: '0',
        })
        officeInfo.value = jigyoInfo
      }
    }
  }
}
// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**
 * 計画期間変更API返却値処理
 *
 * @param resData - API返却値
 */
const processPlanPeriodInfoData = (
  resData: assessmentComprehensiveMealIPeriodChangeSelectOutEntity
) => {
  // 1件目の計画対象期間データが表示されている状態
  if (resData.errKbn === '1') {
    setShowDialog(t('message.i-cmn-11262'))
    return
  }
  // 最終件目の計画対象期間データが表示されている状態
  if (resData.errKbn === '2') {
    setShowDialog(t('message.i-cmn-11263'))
    return
  }
  // 子画面データクリア
  // 画面制御データ初期化
  createNewClickNumberOfTimes.value = 0
  clickDeleteButtonFlg.value = false
  const { historyInfo, planPeriodInfo, kikanKanriFlg } = resData
  setCommonInfo({
    startYmd: planPeriodInfo.startYmd,
    endYmd: planPeriodInfo.endYmd,
    planPeriodFlg: kikanKanriFlg,
  })
  localOneway.or13844Oneway.plainningPeriodManageFlg = kikanKanriFlg
  plannningPeriodManageFlg.value = kikanKanriFlg
  if (plannningPeriodManageFlg.value === TeX0008Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
    // 期間IDチェック、かつ期間件数が0件以外の場合
    if (planPeriodInfo.sc1Id && planPeriodInfo.periodCnt !== TeX0008Const.DEFAULT.DATA_DEFAULT) {
      localOneway.or13844Oneway.planningPeriodInfo = {
        id: planPeriodInfo.sc1Id,
        periodCnt: planPeriodInfo.periodCnt,
        periodNo: planPeriodInfo.periodNo,
        period: `${planPeriodInfo.startYmd}${SPACE_WAVE}${planPeriodInfo.endYmd}`,
      }
      historyDisplay.value = true
    } else {
      localOneway.or13844Oneway.planningPeriodInfo = {
        id: '',
        period: '',
        periodNo: '0',
        periodCnt: '0',
      }
      historyDisplay.value = false
      clearScreenData()
      // 処理終了
      return
    }
  } else {
    localOneway.or13844Oneway.planningPeriodInfo = {
      id: '',
      period: '',
      periodNo: '0',
      periodCnt: '0',
    }
    historyDisplay.value = false
    clearScreenData()
  }
  // 履歴IDが存在する場合
  if (historyInfo.cc1Id && historyInfo.cc1Id !== TeX0008Const.DEFAULT.DATA_DEFAULT) {
    // 取得したサブ情報の各項目を画面へセットする。
    localOneway.or13872Oneway.historyInfo = {
      rirekiId: historyInfo.cc1Id,
      krirekiNo: historyInfo.krirekiNo,
      krirekiCnt: historyInfo.krirekiCnt,
    }
    // localOneway.or13850Oneway = {
    //   name: historyInfo.shokuNum,
    //   id: historyInfo.shokuId,
    // }
    // 作成者設定
    local.orX0157.value = historyInfo.shokuNum ?? ''
    staffId = historyInfo.shokuId ?? ''
    // 作成日設定
    local.createDate.value = historyInfo.createYmd
    // 共通情報を設定する
    setCommonInfo({
      // 親情報.計画対象期間ID = 画面.計画対象期間ID
      sc1Id: planPeriodInfo.sc1Id,
      // 親情報.アセスメントID = 画面.アセスメントID
      cc1Id: historyInfo.cc1Id,
      // 親情報.作成者ID = 画面.作成者ID
      createUserId: historyInfo.shokuId,
      // 親情報.作成日 = 画面.作成日
      createYmd: historyInfo.createYmd,
      // 更新区分クリア
      updateKbn: '',
      historyUpdateKbn: '',
      syubetsuId: resData.syubetsuId,
      rrkNo: resData.historyInfo.krirekiNo,
      sc1Number: planPeriodInfo.periodNo,
    })
    // 子画面を通知する
    setEvent({ isRefresh: true })
  }
  // 履歴情報=NULL
  else {
    // ・作成日 ＝ 共通情報.作成日
    local.createDate.value = systemCommonsStore.getSystemDate ?? ''
    // ・作成者 ＝ ログイン情報.職員名
    // localOneway.or13850Oneway = {
    //   name: systemCommonsStore.getCurrentUser.shokuinKnj,
    //   id: systemCommonsStore.getCurrentUser.chkShokuId,
    // }
    local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
    staffId = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
    // ・履歴-ページング = "1 / 1"
    localOneway.or13872Oneway.historyInfo = {
      rirekiId: '',
      krirekiNo: '0',
      krirekiCnt: '0',
    }
    // 共通情報を設定する
    setCommonInfo({
      sc1Id: planPeriodInfo.sc1Id,
      cc1Id: '0',
      createUserId: systemCommonsStore.getCurrentUser.chkShokuId,
      createUserName: systemCommonsStore.getCurrentUser.chkShokuId,
      createYmd: systemCommonsStore.getSystemDate,
      updateKbn: UPDATE_KBN.CREATE,
      historyUpdateKbn: UPDATE_KBN.CREATE,
      rrkNo: '1',
      sc1Number: planPeriodInfo.periodNo,
    })
    historyNew()
  }
}

/**
 * AC001_初期表示:計画期間対象情報取得
 *
 * @description
 * 計画期間情報取得
 * 履歴情報取得
 */
const getInitDataInfo = async () => {
  // 入力パラメータ
  try {
    // 入力パラメータ作成
    const inputData: assessmentComprehensiveMealIPeriodChangeSelectInEntity = {
      // 事業者ID：画面.事業所ID
      svJigyoId: officeInfo.value?.svJigyoId ?? '',
      // 利用者ID：画面.利用者ID
      userId: userId.value,
      // 施設ID：共通情報.施設ID
      shisetuId: officeInfo.value?.shisetuId ?? '',
      // 期間ID："" 疎通確認------初期表示の場合は'0'に設定する
      sc1Id: '0',
      // 計画期間ページ区分：""
      pageFlag: '0',
      // メニュー２名称
      menu2Name: TeX0008Const.DEFAULT.MENU_NAME_COMPREHIENSIVE_2,
      // メニュー３名称
      menu3Name: TeX0008Const.DEFAULT.MENU_NAME_COMPREHIENSIVE_3,
    }
    // 初期情報取得APIを呼び出す
    const resData: BaseResponseBody<assessmentComprehensiveMealIPeriodChangeSelectOutEntity> =
      await ScreenRepository.select('assessmentComprehensiveMealIPeriodChangeSelect', inputData)

    // API返却値チェック
    if (resData?.data) {
      processPlanPeriodInfoData(resData.data)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 共通情報設定
 *
 * @param commonInfo - 「アセスメント」（包括）画面用共通情報
 */
const setCommonInfo = (commonInfo: TeX0008Type) => {
  const existedData = TeX0008Logic.data.get(props.uniqueCpId)
  const newData = {
    ninteiFormF: commonInfo?.ninteiFormF ?? existedData?.ninteiFormF,
    activeTabId: commonInfo?.activeTabId ?? existedData?.activeTabId,
    jigyoId: commonInfo?.jigyoId ?? existedData?.jigyoId,
    houjinId: commonInfo?.houjinId ?? existedData?.houjinId,
    shisetuId: commonInfo?.shisetuId ?? existedData?.shisetuId,
    userId: commonInfo?.userId ?? existedData?.userId,
    syubetsuId: commonInfo?.syubetsuId ?? existedData?.syubetsuId,
    createYmd: commonInfo?.createYmd ?? existedData?.createYmd,
    historyUpdateKbn: commonInfo?.historyUpdateKbn ?? existedData?.historyUpdateKbn,
    sc1Id: commonInfo?.sc1Id ?? existedData?.sc1Id,
    recId: commonInfo?.recId ?? existedData?.recId,
    cc1Id: commonInfo?.cc1Id ?? existedData?.cc1Id,
    createUserId: commonInfo?.createUserId ?? existedData?.createUserId,
    svJigyoId: commonInfo?.svJigyoId ?? existedData?.svJigyoId,
    updateKbn: commonInfo?.updateKbn ?? existedData?.updateKbn,
    planPeriodFlg: commonInfo?.planPeriodFlg ?? existedData?.planPeriodFlg,
    duplicatePlanId: commonInfo?.duplicatePlanId ?? existedData?.duplicatePlanId,
    duplicateCareCheckId: commonInfo?.duplicateCareCheckId ?? existedData?.duplicateCareCheckId,
    childrenScreenApiResult:
      commonInfo?.childrenScreenApiResult ?? existedData?.childrenScreenApiResult,
    svjigyoName: commonInfo?.svjigyoName ?? existedData?.svjigyoName,
    startYmd: commonInfo?.startYmd ?? existedData?.startYmd,
    endYmd: commonInfo?.endYmd ?? existedData?.endYmd,
    createUserName: commonInfo?.createUserName ?? existedData?.createUserName,
    rrkNo: commonInfo?.rrkNo ?? existedData?.rrkNo,
    sc1Number: commonInfo.sc1Number ?? existedData?.sc1Number,
  }

  TeX0008Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: newData,
  })
}

/**
 * 画面イベント発火
 *
 * @param event - 画面イベント
 */
const setEvent = (event: TeX0008EventType) => {
  TeX0008Logic.event.set({
    uniqueCpId: props.uniqueCpId,
    events: {
      /** 再表示発火フラグ */
      isRefresh: event.isRefresh,
      /** 作成日変更フラグ */
      isCreateDateChanged: event.isCreateDateChanged,
      /** お気に入りイベント発火フラグ */
      favoriteEventFlg: event.favoriteEventFlg,
      /** 保存イベント発火フラグ */
      saveEventFlg: event.saveEventFlg,
      /** 新規イベント発火フラグ */
      createEventFlg: event.createEventFlg,
      /** 複写イベント発火フラグ */
      copyEventFlg: event.copyEventFlg,
      /** 印刷イベント発火フラグ */
      printEventFlg: event.printEventFlg,
      /** マスタ他イベント発火フラグ */
      masterEventFlg: event.masterEventFlg,
      /** 削除イベント発火フラグ */
      deleteEventFlg: event.deleteEventFlg,
      /** 優先順位イベント発火フラグ */
      priorityOrderEventFlg: event.priorityOrderEventFlg,
    },
  })
}

/**
 * 画面IDより、タブリストを作成
 */
const setChildrenScreenData = () => {
  // 食事
  localOneway.or03243Oneway.questionList = createIssuseTabList(Or03243Const.DEFAULT.TAB_ID)
  // 排泄
  localOneway.or03250Oneway.questionList = createIssuseTabList(Or03250Const.DEFAULT.TAB_ID)
  // 入浴
  localOneway.or03249Oneway.questionList = createIssuseTabList(Or03249Const.DEFAULT.TAB_ID)
  // 洗面
  localOneway.or03245Oneway.questionList = createIssuseTabList(Or03245Const.DEFAULT.TAB_ID)
  // 基本
  localOneway.or03244Oneway.questionList = createIssuseTabList(Or03244Const.DEFAULT.TAB_ID)
  // 医療
  localOneway.or03237Oneway.questionList = createIssuseTabList(Or03237Const.DEFAULT.TAB_ID)
  // 心理
  localOneway.or03240Oneway.questionList = createIssuseTabList(Or03240Const.DEFAULT.TAB_ID)
}

/**
 * 作成日変更処理
 *
 * @param mo00020 - 作成日
 */
const createDateChange = (mo00020: Mo00020Type) => {
  // 作成日変更処理
  setCommonInfo({ createYmd: mo00020.value })
}

/**
 * 歴史情報取得
 *
 * @param historyId - 履歴ID
 */
const getHistoryInfo = async (historyId: string) => {
  try {
    const commonInfo = TeX0008Logic.data.get(props.uniqueCpId)

    let deleteKbn = '0'

    if (commonInfo?.updateKbn === UPDATE_KBN.DELETE) {
      deleteKbn = '1'
    } else if (commonInfo?.historyUpdateKbn === UPDATE_KBN.DELETE) {
      deleteKbn = '2'
    }

    // 歴史更新区分クリア
    setCommonInfo({
      historyUpdateKbn: '',
      updateKbn: '',
    })
    const inputData: assessmentComprehensiveMealIHistoryChangeInEntity = {
      /** ケアチェックID */
      cc1Id: historyId,
      /** 期間ID */
      sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
      /** 利用者ID */
      userId: userId.value,
      /** 事業者ID */
      svJigyoId: officeInfo.value?.svJigyoId ?? '',
      /** 歴史変更区分フラグ */
      kikanFlag: '0',
      /** 当履歴番号 */
      rrkNo: localOneway.or13872Oneway.historyInfo?.krirekiNo ?? '',
      /** 削除区分 */
      deleteKbn,
    }

    const resData: BaseResponseBody<assessmentComprehensiveMealIHistoryChangeOutEntity> =
      await ScreenRepository.select('assessmentComprehensiveMealIHistoryChangeSelect', inputData)
    if (resData.data) {
      const { historyInfo } = resData.data
      // 履歴IDが存在する場合
      if (historyInfo.cc1Id && historyInfo.cc1Id !== '0') {
        // 取得したサブ情報の各項目を画面へセットする。
        localOneway.or13872Oneway.historyInfo = {
          rirekiId: historyInfo.cc1Id,
          krirekiNo: historyInfo.krirekiNo,
          krirekiCnt: historyInfo.krirekiCnt,
        }
        // // 作成者設定
        local.orX0157.value = historyInfo.shokuKnj ?? ''
        staffId = historyInfo.shokuId ?? ''
        // localOneway.or13850Oneway = {
        //   name: historyInfo.shokuNum,
        //   id: historyInfo.shokuId,
        // }
        // 作成日設定
        local.createDate.value = historyInfo.createYmd
        // 共通情報を設定する
        setCommonInfo({
          sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id,
          // 親情報.アセスメントID = 画面.アセスメントID
          cc1Id: historyInfo.cc1Id,
          // 親情報.作成者ID = 画面.作成者ID
          createUserId: historyInfo.shokuId,
          // 親情報.作成日 = 画面.作成日
          createYmd: historyInfo.createYmd,
          // 更新区分クリア
          updateKbn: '',
          historyUpdateKbn: '',
          rrkNo: resData.data.historyInfo.krirekiNo,
        })
        // 子画面を通知する
        setEvent({ isRefresh: true })
      }
      // 履歴情報=NULL
      else {
        // ・作成日 ＝ 共通情報.作成日
        local.createDate.value = systemCommonsStore.getSystemDate ?? ''
        // ・作成者 ＝ ログイン情報.職員名 Todo
        // localOneway.or13850Oneway = {
        //   name: systemCommonsStore.getCurrentUser.shokuinKnj,
        //   id: systemCommonsStore.getCurrentUser.chkShokuId,
        // }
        local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
        staffId = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
        // ・履歴-ページング = "1 / 1"
        localOneway.or13872Oneway.historyInfo = {
          rirekiId: '',
          krirekiNo: '0',
          krirekiCnt: '0',
        }
        // 共通情報を設定する
        setCommonInfo({
          sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id,
          cc1Id: '',
          createUserId: systemCommonsStore.getCurrentUser.chkShokuId,
          createYmd: systemCommonsStore.getSystemDate,
          updateKbn: UPDATE_KBN.CREATE,
          historyUpdateKbn: UPDATE_KBN.CREATE,
          rrkNo: '1',
        })
        historyNew()
      }
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 履歴を更新
 */
const historyNew = () => {
  // 画面に表示したデータを一時保存する
  srceenInfoBk = {
    careCheckId: localOneway.or13872Oneway.historyInfo?.rirekiId ?? '0',
    planPeriodId: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '0',
    createUserId: staffId ?? '0',
    createUserName: local.orX0157.value ?? '',
    createYmd: local.createDate.value,
    historyCount: localOneway.or13872Oneway.historyInfo?.krirekiCnt ?? '0',
    historyNo: localOneway.or13872Oneway.historyInfo?.krirekiNo ?? '0',
  }
  // ・履歴レコード総数>0の場合
  if (parseInt(localOneway.or13872Oneway.historyInfo?.krirekiCnt ?? '0') >= 0) {
    // 履歴件数 + 1
    const rirekiMaxCount = (
      parseInt(localOneway.or13872Oneway.historyInfo?.krirekiCnt ?? '0') + 1
    ).toString()
    // ・履歴-ページング = 履歴の最大値+1/履歴の最大値+1
    // ・履歴-ページング = 履歴の最大値+1/履歴の最大値+1
    localOneway.or13872Oneway.historyInfo = {
      rirekiId: '0',
      krirekiCnt: rirekiMaxCount,
      krirekiNo: rirekiMaxCount,
    }
  }
  // ・作成者 ＝ ログイン情報.職員名
  local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
  staffId = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
  setCommonInfo({
    // ・計画対象期間ID = 画面.計画対象期間ID
    sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id,
    cc1Id: '0',
    createUserId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
    // ・作成日 ＝ 共通情報.基準日
    createYmd: systemCommonsStore.getSystemDate ?? '',
    // ・履歴更新区分 = "C"（新規） 子画面で設定
    updateKbn: UPDATE_KBN.CREATE,
    historyUpdateKbn: UPDATE_KBN.CREATE,
    createUserName: systemCommonsStore.getCurrentUser.shokuinKnj,
  })
  // 押下回数を増やす
  createNewClickNumberOfTimes.value += 1
  setEvent({ createEventFlg: true })
}

/**
 * 削除処理
 */
const screenDelete = (): Promise<string> => {
  // OrX0001のダイアログ開閉状態を更新する
  // メッセージを表示 i.cmn.11348
  // タブ区分より、メッセージを設定する
  let tabName = ''
  switch (local.mo00043.id) {
    case Or03243Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-comprehensive-tab-name-1')
      break
    case Or03250Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-comprehensive-tab-name-2')
      break
    case Or03249Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-comprehensive-tab-name-3')
      break
    case Or03245Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-comprehensive-tab-name-4')
      break
    case Or03240Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-comprehensive-tab-name-5')
      break
    case Or03237Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-comprehensive-tab-name-6')
      break
    case Or03244Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-comprehensive-tab-name-7')
      break
  }
  const msg11348 = t('message.i-cmn-11348', [
    local.createDate.value,
    t('label.assessment'),
    tabName,
    t('label.assessment-comprehensive-tab-name-1') +
      '~' +
      t('label.assessment-comprehensive-tab-name-7'),
  ])
  const msgList = msg11348.split(/[\r\n]+/)
  if (msgList.length > 2) {
    localOneway.orX0001Oneway.createYmd = local.createDate.value
    localOneway.orX0001Oneway.kinouKnj = t('label.assessment')
    localOneway.orX0001Oneway.selectTabName = tabName

    OrX0001Logic.state.set({
      uniqueCpId: orX0001.value.uniqueCpId,
      state: { isOpen: true },
    })
  }

  // 閉じるタイミング監視
  return new Promise((resolve) => {
    watch(
      () => OrX0001Logic.state.get(orX0001.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (!newValue) {
          resolve(local.orX0001Type.deleteSyubetsu)
        }
      },
      { once: true }
    )
  })
}

/**
 * 削除処理を行う
 */
const userDelete = async () => {
  // 削除した場合は処理を中断する
  if (clickDeleteButtonFlg.value) return
  const result = await screenDelete()
  // 画面削除した場合
  if (result === TeX0008Const.DEFAULT.DELETE_KBN_GAMEN) {
    setCommonInfo({
      updateKbn: UPDATE_KBN.DELETE,
    })
    setEvent({ deleteEventFlg: true })
  }
  // 削除確認画面ダイアログに「表示している画面を履歴ごと削除する」を選択する場合
  else if (result === TeX0008Const.DEFAULT.DELETE_KBN_HISTORY) {
    // タブより、初期化処理を再実行
    await getHistoryInfo(localOneway.or13872Oneway.historyInfo?.rirekiId ?? '0')
    // 子画面再表示済み待ち
    await nextTick()
    // 入力フォームを非表示にする
    setCommonInfo({
      updateKbn: UPDATE_KBN.DELETE,
      historyUpdateKbn: UPDATE_KBN.DELETE,
    })
    setEvent({ deleteEventFlg: true })
  }
  if (
    result === TeX0008Const.DEFAULT.DELETE_KBN_GAMEN ||
    result === TeX0008Const.DEFAULT.DELETE_KBN_HISTORY
  ) {
    // 作成者選択アイコンボタン、作成日、作成日カレンダ
    localOneway.createDateOneway.disabled = true
    localOneway.orX0157Oneway.text!.orX0157InputOneway.disabled = true
    // 削除ボタン押下フラグをtrueに設定する
    clickDeleteButtonFlg.value = true
    // 以下の項目を非表示にする。
    // 入力フームのすべて項目
    // 以下のボタンは実行無効にする。
    // 新規、複写、印刷、削除
  }
}

/**
 * 画面イベント発火
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
const openConfirmDialog1 = (paramDialogText: string): Promise<'yes' | 'no' | 'cancel'> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen,
      (newValue, oldValue) => {
        if (newValue === false && oldValue === true) {
          const event = Or21814Logic.event.get(or21814_3.value.uniqueCpId)

          let result = 'no' as 'yes' | 'no' | 'cancel'

          if (event?.firstBtnClickFlg) {
            result = DIALOG_BTN.YES
          }
          if (event?.secondBtnClickFlg) {
            result = DIALOG_BTN.NO
          }
          if (event?.thirdBtnClickFlg) {
            result = DIALOG_BTN.CANCEL
          }
          if (event?.closeBtnClickFlg) {
            result = DIALOG_BTN.CANCEL
          }

          // 確認ダイアログのフラグをOFF
          Or21814Logic.event.set({
            uniqueCpId: or21814_3.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              thirdBtnClickFlg: false,
            },
          })

          resolve(result)
        }
      },
      { once: true }
    )
  })
}

/**
 * 歴史イベント処理
 *
 * @param event - イベント
 */
const setOr13872Event = (event: Or13872EventType) => {
  Or13872Logic.event.set({
    uniqueCpId: or13872.value.uniqueCpId,
    events: event,
  })
}

/**
 * 歴史イベント処理
 *
 * @param event - イベント
 */
const setOr13844Event = (event: Or13844EventType) => {
  Or13844Logic.event.set({
    uniqueCpId: or13844.value.uniqueCpId,
    events: event,
  })
}

/**
 *  計画対象期間チェンジ設定
 *
 * @param pageFlg - ページ区分
 */
const planningPeriodChange = async (pageFlg: string) => {
  try {
    // リクエストパラメータ作成
    const inputData: assessmentComprehensiveMealIPeriodChangeSelectInEntity = {
      /**
       * 事業者ID
       */
      svJigyoId: officeInfo.value?.svJigyoId ?? '',
      /**
       * 利用者ID
       */
      userId: userId.value,
      /**
       * 施設ID 共通情報.施設ID
       */
      shisetuId: systemCommonsStore.getShisetuId!,
      /**
       * 期間ID
       */
      sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
      /**
       * 計画期間ページ区分 初期値:''
       */
      pageFlag: pageFlg,
      /**
       * メニュー２
       */
      menu2Name: TeX0008Const.DEFAULT.MENU_NAME_COMPREHIENSIVE_2,
      /**
       * メニュー３
       */
      menu3Name: TeX0008Const.DEFAULT.MENU_NAME_COMPREHIENSIVE_3,
    }

    // 初期情報取得APIを呼び出す
    const resData: BaseResponseBody<assessmentComprehensiveMealIPeriodChangeSelectOutEntity> =
      await ScreenRepository.select('assessmentComprehensiveMealIPeriodChangeSelect', inputData)
    // API返却値チェック
    if (resData?.data) {
      processPlanPeriodInfoData(resData.data)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 *  歴史期間チェンジ設定
 *
 * @param pageflg - ページ区分
 */
const historyChange = async (pageflg: string) => {
  try {
    const inputData: assessmentComprehensiveMealIHistoryChangeInEntity = {
      /** ケアチェックID */
      cc1Id: localOneway.or13872Oneway.historyInfo?.rirekiId ?? '0',
      /** 画面.計画対象期間ID */
      sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '0',
      /** 共通情報.利用者ID */
      userId: userId.value ?? systemCommonsStore.getUserSelectSelfId(),
      /** 事業者ID */
      svJigyoId: officeInfo.value?.svJigyoId ?? '',
      /** 歴史変更区分フラグ */
      kikanFlag: pageflg,
      /** 削除区分 */
      deleteKbn: '0',
      /** 当履歴番号 */
      rrkNo: localOneway.or13872Oneway.historyInfo?.krirekiNo ?? '0',
    }
    const resData: BaseResponseBody<assessmentComprehensiveMealIHistoryChangeOutEntity> =
      await ScreenRepository.select('assessmentComprehensiveMealIHistoryChangeSelect', inputData)
    if (resData.data) {
      const { historyInfo } = resData.data
      // 履歴IDが存在する場合
      if (historyInfo.cc1Id && historyInfo.cc1Id !== '0') {
        // 取得したサブ情報の各項目を画面へセットする。
        localOneway.or13872Oneway.historyInfo = {
          rirekiId: historyInfo.cc1Id,
          krirekiNo: historyInfo.krirekiNo,
          krirekiCnt: historyInfo.krirekiCnt,
        }
        // 作成者設定
        local.orX0157.value = historyInfo.shokuKnj ?? ''
        staffId = historyInfo.shokuId ?? ''
        // localOneway.or13850Oneway = {
        //   name: historyInfo.shokuNum,
        //   id: historyInfo.shokuId,
        // }
        // 作成日設定
        local.createDate.value = historyInfo.createYmd
        // 共通情報を設定する
        setCommonInfo({
          sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id,
          // 親情報.アセスメントID = 画面.アセスメントID
          cc1Id: historyInfo.cc1Id,
          // 親情報.作成者ID = 画面.作成者ID
          createUserId: historyInfo.shokuId,
          // 親情報.作成日 = 画面.作成日
          createYmd: historyInfo.createYmd,
          // 更新区分クリア
          updateKbn: '',
          historyUpdateKbn: '',
          rrkNo: resData.data.historyInfo.krirekiNo,
        })
        // 子画面を通知する
        setEvent({ isRefresh: true })
      }
      // 履歴情報=NULL
      else {
        // ・作成日 ＝ 共通情報.作成日
        local.createDate.value = systemCommonsStore.getSystemDate ?? ''
        // ・作成者 ＝ ログイン情報.職員名
        local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
        staffId = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
        // localOneway.or13850Oneway = {
        //   name: systemCommonsStore.getCurrentUser.shokuinKnj,
        //   id: systemCommonsStore.getCurrentUser.chkShokuId,
        // }
        // ・履歴-ページング = "1 / 1"
        localOneway.or13872Oneway.historyInfo = {
          rirekiId: '',
          krirekiNo: '0',
          krirekiCnt: '0',
        }
        // 共通情報を設定する
        setCommonInfo({
          sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id,
          cc1Id: '',
          createUserId: systemCommonsStore.getCurrentUser.chkShokuId,
          createYmd: systemCommonsStore.getSystemDate,
          updateKbn: UPDATE_KBN.CREATE,
          historyUpdateKbn: UPDATE_KBN.CREATE,
          rrkNo: '1',
        })
        createNewClickNumberOfTimes.value = 1
        setEvent({ createEventFlg: true })
      }
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 新規処理
 */
const createNew = async () => {
  if (clickDeleteButtonFlg.value) return
  // 期間管理フラグが「管理する」、かつ、計画期間情報.期間総件数 = 0（期間なし）
  if (
    plannningPeriodManageFlg.value === TeX0008Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
    localOneway.or13844Oneway.planningPeriodInfo?.periodCnt === '0'
  ) {
    // 以下のメッセージを表示
    // i.cmn.11300
    setShowDialog(t('message.i-cmn-11300'))
    // AC013 「計画対象期間-前へアイコンボタン」押下実行
    await planningPeriodChange('1')
    return
  }
  // 二回目新規ボタン押下する場合
  // 以下のメッセージを表示
  // i.cmn.11265
  if (createNewClickNumberOfTimes.value >= 1) {
    // ・埋め込み文字
    // {0}:ケアチェック表
    setShowDialog(t('message.i-cmn-11265', [t('label.care-table')]))
    return
  }

  // 画面データ変更ありの場合
  switch (await getDataChangeResult()) {
    case TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE:
      // 変更なし、初期化を実行する
      historyNew()
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
      // 入力内容は破棄する、新規処理を行う
      historyNew()
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
      // 入力内容を保存する、新規処理を行う
      setEvent({ saveEventFlg: true })
      // 保存後操作タイプを保存する
      afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.CREATE
      break
  }
}

/**
 * 計画期間変更処理
 *
 * @param planId - 計画期間ID
 */
const getInitDataInfoFromPlanPeriodId = async (planId: string) => {
  try {
    // リクエストパラメータ作成
    const inputData: assessmentComprehensiveMealIPeriodChangeSelectInEntity = {
      /**
       * 事業者ID
       */
      svJigyoId: officeInfo.value?.svJigyoId ?? '',
      /**
       * 利用者ID
       */
      userId: userId.value,
      /**
       * 施設ID 共通情報.施設ID
       */
      shisetuId: officeInfo.value?.shisetuId ?? '',
      /**
       * 期間ID 初期値:''
       */
      sc1Id: planId,
      /**
       * 計画期間ページ区分 初期値:''
       */
      pageFlag: '0',
      /**
       * メニュー２
       */
      menu2Name: TeX0008Const.DEFAULT.MENU_NAME_COMPREHIENSIVE_2,
      /**
       * メニュー３
       */
      menu3Name: TeX0008Const.DEFAULT.MENU_NAME_COMPREHIENSIVE_3,
    }
    // 初期情報取得APIを呼び出す
    const resData: BaseResponseBody<assessmentComprehensiveMealIPeriodChangeSelectOutEntity> =
      await ScreenRepository.select('assessmentComprehensiveMealIPeriodChangeSelect', inputData)

    // API返却値チェック
    if (resData?.data) {
      processPlanPeriodInfoData(resData.data)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * ［具体的内容と対応するケア項目一覧］画面をポップアップで起動する。
 */
const showAllListDialog = () => {
  // Onewayモーダル設定
  // ケアチェックID：画面.ケアチェックID
  localOneway.or27216Oneway.cc1Id = parseInt(localOneway.or13872Oneway.historyInfo?.rirekiId ?? '0')
  // 計画期間ID：画面.計画対象期間ID
  localOneway.or27216Oneway.sc1Id = parseInt(
    localOneway.or13844Oneway.planningPeriodInfo?.id ?? '0'
  )
  Or27216Logic.state.set({
    uniqueCpId: or27216.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 複写ダイアログ前のチェック
 */
const beforeShowDuplicateDialog = async () => {
  // 非活性変更点、削除の場合は無効化する
  if (clickDeleteButtonFlg.value) return

  // 画面データ変更ありの場合
  switch (await getDataChangeResult()) {
    case TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE:
      // 変更なし
      setShowdulipcateDialog()
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
      // 入力内容は破棄する
      setShowdulipcateDialog()
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
      setEvent({ saveEventFlg: true })
      // 操作タイプを一時保存する
      afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.DUPLICATE
      break
  }
}

/**
 * 複写ダイアログを呼び出す
 */
const setShowdulipcateDialog = () => {
  if (clickDeleteButtonFlg.value) return
  let title = ''

  switch (local.mo00043.id) {
    case Or03243Const.DEFAULT.TAB_ID:
      title = t('label.assessment-comprehensive-tab-name-1')
      break
    case Or03250Const.DEFAULT.TAB_ID:
      title = t('label.assessment-comprehensive-tab-name-2')
      break
    case Or03249Const.DEFAULT.TAB_ID:
      title = t('label.assessment-comprehensive-tab-name-3')
      break
    case Or03245Const.DEFAULT.TAB_ID:
      title = t('label.assessment-comprehensive-tab-name-4')
      break
    case Or03240Const.DEFAULT.TAB_ID:
      title = t('label.assessment-comprehensive-tab-name-5')
      break
    case Or03237Const.DEFAULT.TAB_ID:
      title = t('label.assessment-comprehensive-tab-name-6')
      break
    case '7':
      title = t('label.assessment-comprehensive-tab-name-7')
      break
  }
  componentKey.value = new Date().getTime().toString()
  localOneway.or5942Oneway = {
    // 複写先計画期間ID：画面.計画期間ID
    sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '0',
    // 複写先ケアチェックID：画面.ケアチェックID
    cc1Id: localOneway.or13872Oneway.historyInfo?.rirekiId ?? '0',
    // タブID
    tabId: local.mo00043.id,
    // タブタイトル
    title,
    // 期間管理フラグ：共通情報.期間管理フラグ
    planPeriodFlg: plannningPeriodManageFlg.value,
  }
  Or59423Logic.state.set({
    uniqueCpId: or59423.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * データクリア用関数
 *
 * @description
 * 画面再表示前にデータクリア関数
 */
const clearScreenData = () => {
  // 計画期間クリア
  localOneway.or13844Oneway.planningPeriodInfo = {
    id: '',
    periodNo: '0',
    periodCnt: '0',
    period: '',
  }
  // 履歴選択クリア
  localOneway.or13872Oneway.historyInfo = {
    krirekiNo: '0',
    krirekiCnt: '0',
    rirekiId: '0',
  }
  // 作成者クリア
  // localOneway.or13850Oneway.name = ''
  // localOneway.or13850Oneway.id = ''
  local.orX0157.value = ''
  staffId = ''
  // 作成日クリア
  local.createDate.value = ''
  // 共通情報クリア
  setCommonInfo({
    cc1Id: '',
    sc1Id: '',
    createUserId: '',
    createYmd: '',
    svJigyoId: '',
    houjinId: '',
    shisetuId: '',
    updateKbn: '',
    historyUpdateKbn: '',
    userId: '',
    syubetsuId: systemCommonsStore.getSyubetu,
  })
  // 子画面データクリア
  setEvent({ createEventFlg: true })
}

/**
 * データ変更チェック
 */
const getDataChangeResult = async (): Promise<number> => {
  let result = TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE
  // // 共通処理の登録権限を取得する
  // const saveAuth = hasRegistAuth()
  const updateKbn = TeX0008Logic.data.get(props.uniqueCpId)?.updateKbn
  const saveAuth = true
  if (isEdit.value || updateKbn !== UPDATE_KBN.NONE) {
    // 画面入力変更あり、かつ保存権限がない場合
    // 確認ダイアログ表示
    if (!saveAuth) {
      const dialogResult = await openConfirmDialog1(t('message.w-com-10006'))
      switch (dialogResult) {
        case DIALOG_BTN.YES:
          result = TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD
          break
        case DIALOG_BTN.CANCEL:
        case DIALOG_BTN.NO:
          result = TeX0008Const.DEFAULT.IS_EDIT_CHANGE_CLOSE_DIALOG
      }
      return result
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case DIALOG_BTN.YES:
          result = TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE
          break
        case DIALOG_BTN.CANCEL:
          result = TeX0008Const.DEFAULT.IS_EDIT_CHANGE_CLOSE_DIALOG
          break
        case DIALOG_BTN.NO:
          result = TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD
          break
      }
      return result
    }
  } else {
    // 画面変更なしの場合
    return result
  }
}

/**
 * 複写画面データ取得
 *
 * @param deplicateInfo - 複写情報
 */
const getDuplicateData = async (deplicateInfo: Or59423ConfirmType) => {
  // タブ区分により、対した初期情報取得処理を実行する
  if (deplicateInfo.duplicateMode === TeX0008Const.DEFAULT.DUPLICATE_SCREEN) {
    setCommonInfo({
      duplicateCareCheckId: deplicateInfo.careCheckId,
      duplicatePlanId: deplicateInfo.planPeriodId,
      updateKbn: UPDATE_KBN.UPDATE,
    })
    /**
     * 複写イベント発火
     */
    setEvent({ copyEventFlg: true })
  } else if (deplicateInfo.duplicateMode === TeX0008Const.DEFAULT.DUPLICATE_HISTORY) {
    // 複数セクション複写 かつ 選択されているタブが複写対象の場合
    localOneway.or13844Oneway.planningPeriodInfo!.id = deplicateInfo.planPeriodId
    await getHistoryInfo(deplicateInfo.careCheckId)
    setCommonInfo({
      updateKbn: UPDATE_KBN.UPDATE,
      historyUpdateKbn: UPDATE_KBN.UPDATE,
    })
  }
}

/**
 * 印刷画面を呼び出す
 *
 * @description
 * GUI00842 印刷設定画面をポップアップで起動する。
 */
const setShowPrintDialog = () => {
  // 削除の場合、無効化する
  if (clickDeleteButtonFlg.value) return
  // 選択帳票番号設定
  let prtNo = ''
  switch (local.mo00043.id) {
    case Or03243Const.DEFAULT.TAB_ID:
      prtNo = Or03243Const.DEFAULT.TAB_ID
      break
    case Or03250Const.DEFAULT.TAB_ID:
      prtNo = Or03250Const.DEFAULT.TAB_ID
      break
    case Or03249Const.DEFAULT.TAB_ID:
      prtNo = Or03249Const.DEFAULT.TAB_ID
      break
    case Or03245Const.DEFAULT.TAB_ID:
      prtNo = Or03245Const.DEFAULT.TAB_ID
      break
    case Or03240Const.DEFAULT.TAB_ID:
      prtNo = Or03240Const.DEFAULT.TAB_ID
      break
    case Or03237Const.DEFAULT.TAB_ID:
      prtNo = Or03237Const.DEFAULT.TAB_ID
      break
    case Or03244Const.DEFAULT.TAB_ID:
      prtNo = Or03244Const.DEFAULT.TAB_ID
      break
  }
  // 入力パラメータ設定
  // 利用者情報リスト：共通情報.利用者情報リスト
  // 利用者ID：共通情報.利用者ID
  // 担当ケアマネID：共通情報.担当ケアマネID
  // 履歴ID：画面.履歴ID
  // セッション名："ケアチェック表"
  // 選択帳票番号：1
  // 50音行番号：-
  // 50音母音：-
  // ケアチェックID：画面.ケアチェックID
  // 記入用シート方式：-
  const param: Or52060Param = {
    processYmd: '2024/11/11',
    sectionName: Or03243Const.DEFAULT.SECTION_NAME,
    shisetuId: officeInfo.value?.shisetuId ?? '',
    svJigyoId: officeInfo.value?.svJigyoId ?? '',
    svJigyoKnj: officeInfo.value?.jigyoRyakuKnj ?? '',
    shokuId: staffId ?? '',
    userId: userId.value,
    assId: localOneway.or13872Oneway.historyInfo?.rirekiId ?? '',
    prtNo,
    tantoId: '2',
    kkjTantoFlg: '0',
    focusSettingInitial: [],
    userList: [],
    b1Cd: local.mo00043.id,
  }

  Or52060Logic.state.set({
    uniqueCpId: or52060.value.uniqueCpId,
    state: {
      isOpen: true,
      param,
    },
  })
}

/**
 * 以下のマスタ画面を複数タブの形でポップアップで起動します。
 *
 * 初期デフォルト表示:「GUI00830 ケアチェックマスタ」
 *
 * 1. GUI00830: ケアチェックマスタ
 * 2. GUI00832: 提供マスタ
 * 3. GUI00829: 家族マスタ
 * 4. GUI00833: 予定マスタ
 * 5. GUI00831: ケアの提供場所マスタ
 */
const clickMasterIconBtn = async () => {
  // 入力パラメータ
  localOneway.or41345Oneway = {
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '3GK',
    shisetuId: parseInt(officeInfo.value?.shisetuId ?? systemCommonsStore.getShisetuId ?? '0'),
    svJigyoId: parseInt(officeInfo.value?.svJigyoId ?? systemCommonsStore.getSvJigyoId ?? '0'),
    kbnFlg: '4',
    tabId: Or41345Const.TAB.TAB_ID_CARE_CHECK,
  }
  // 画面データ変更ありの場合
  switch (await getDataChangeResult()) {
    case TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE:
      // Or27825のダイアログ開閉状態を更新する
      Or41345Logic.state.set({
        uniqueCpId: or41345.value.uniqueCpId,
        state: { isOpen: true },
      })
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
      // 入力内容を廃棄し、ダイアログを呼び出す

      // Or27825のダイアログ開閉状態を更新する
      Or41345Logic.state.set({
        uniqueCpId: or41345.value.uniqueCpId,
        state: { isOpen: true },
      })
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
      // 入力内容を保存し、マスタ画面を呼び出す
      // 現在中止した操作タイプを一時保存する、保存処理済み後実行する
      afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.MASTER
      setEvent({ saveEventFlg: true })
      break
  }
}

/**
 * 保存後タイプチェック
 */
const checkProcessType = async () => {
  // 子画面の保存返却値を共通情報に設定する
  const commonInfo = TeX0008Logic.data.get(props.uniqueCpId)
  // 画面更新区分チェック
  // 保存後処理タイプチェック
  switch (afterProcess.type) {
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.CREATE: // 保存後新規の場合
      // 新規イベント発火
      historyNew()
      break
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.DUPLICATE:
      // 保存後複数画面を呼び出す
      setCommonInfo({
        sc1Id: commonInfo?.childrenScreenApiResult?.sc1Id,
        cc1Id: commonInfo?.childrenScreenApiResult?.cc1Id,
      })
      setShowdulipcateDialog()
      break
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.MASTER: // 保存後マスタ画面を呼び出すの場合
      Or41345Logic.state.set({
        uniqueCpId: or41345.value.uniqueCpId,
        state: { isOpen: true },
      })
      break
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.TAB_CHANGE: {
      // 更新区分チェック // 保存後タブ変更の場合
      const commonInfo = TeX0008Logic.data.get(props.uniqueCpId)
      if (commonInfo?.updateKbn === UPDATE_KBN.CREATE) {
        // 子画面API返却値を画面と共通情報に設定する
        if (
          localOneway.or13844Oneway.planningPeriodInfo &&
          commonInfo?.childrenScreenApiResult?.sc1Id
        ) {
          localOneway.or13844Oneway.planningPeriodInfo.id =
            commonInfo?.childrenScreenApiResult.sc1Id
        }
        if (localOneway.or13872Oneway.historyInfo && commonInfo.childrenScreenApiResult?.cc1Id) {
          localOneway.or13872Oneway.historyInfo.rirekiId = commonInfo.childrenScreenApiResult.cc1Id
          // 履歴情報再取得
        }
      }
      setCommonInfo({
        sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id,
        cc1Id: localOneway.or13872Oneway.historyInfo?.rirekiId,
        svJigyoId: officeInfo.value?.svJigyoId,
        houjinId: officeInfo.value?.houjinId,
        shisetuId: officeInfo.value?.shisetuId,
        svjigyoName: officeInfo.value?.jigyoRyakuKnj,
        createUserId: staffId,
        createYmd: local.createDate.value,
        userId: userId.value,
        updateKbn: '',
        historyUpdateKbn: '',
      })

      await getHistoryInfo(commonInfo?.childrenScreenApiResult?.cc1Id ?? '0')
      // タブ変更を行う
      local.mo00043.id = afterProcess.value
      break
    }
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.PLAN_PERIOD_CHANGE: // 保存後期間変更の場合
      await planningPeriodChange(afterProcess.value)
      break
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.HISTORY_CHANGE: // 保存後歴史変更の場合
      // 履歴変更処理
      await historyChange(afterProcess.value)
      break
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.OFFICE_CHANGE: // 保存後事業所変更の場合
      // 事業所変更処理
      setCommonInfo({
        houjinId: officeInfo.value?.houjinId,
        shisetuId: officeInfo.value?.shisetuId,
        svJigyoId: officeInfo.value?.svJigyoId,
        svjigyoName: officeInfo.value?.jigyoRyakuKnj,
      })
      await getInitDataInfo()
      break
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.PLAN_PERIOD_ID_SELECT: // 保存後期間ID選択画面を呼び出すの場合
      OrX0115Logic.state.set({
        uniqueCpId: orX0115.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
      break
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.HISTORY_ID_SELECT: // 保存後履歴選択画面を呼び出すの場合
      // 入力パラメータ設定
      localOneway.or10926Oneway = {
        userId: userId.value,
        officeId: officeInfo.value?.svJigyoId ?? '',
        planPeriodId: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
        mode: systemCommonsStore.getSentenceName ?? '',
      }
      Or10926Logic.state.set({
        uniqueCpId: or10926.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
      break
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.JUNI_DIALOG: // 保存後優先順位ダイアログを呼び出すの場合
      // 入力パラメータ
      // 計画期間ID：画面.計画対象期間ID
      localOneway.or51105Oneway.sc1Id = commonInfo?.childrenScreenApiResult?.sc1Id ?? ''
      // ケアチェックID：画面.ケアチェックID
      localOneway.or51105Oneway.cc1Id = commonInfo?.childrenScreenApiResult?.cc1Id ?? ''
      // 「優先順位」画面をポップアップで起動する。
      Or51105Logic.state.set({
        uniqueCpId: or51105.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
      break
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.ALL_VIEW: // 保存後記号意味画面を呼び出すの場合
      // 子画面保存のIDを取得する
      localOneway.or51885Oneway = {
        // 計画期間ID：画面.計画対象期間ID
        sc1Id: commonInfo?.childrenScreenApiResult?.sc1Id ?? '',
        // ケアチェックID：画面.ケアチェックID
        cc1Id: commonInfo?.childrenScreenApiResult?.cc1Id ?? '',
        // 施設ID：親画面.施設ID
        shisetuId: officeInfo.value?.shisetuId ?? '',
        // 事業所ID：親画面.事業所ID
        svJigyoId: officeInfo.value?.svJigyoId ?? '',
        // 職員ID：共通情報.職員ID
        shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
        // 法人ID：共通情報.法人ID
        houjinId: systemCommonsStore.getHoujinId ?? '',
        // 利用者ID：親画面.利用者ID
        userID: userId.value,
      }
      // 「記号と意味」画面をポップアップで起動する
      Or51885Logic.state.set({
        uniqueCpId: or51885.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
      break
    case TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.USER_CHANGE: // 保存後利用者変更の場合
      // ・「食事」タブを選択する
      local.mo00043.id = Or03243Const.DEFAULT.TAB_ID
      // ・画面.期間ID = ""
      // ・画面.計画期間ページ区分 = "0"
      // ・初期表示「AC001」を実行する
      await getInitDataInfo()
      break
  }
  // 処理状態クリア
  afterProcess.type = ''
  afterProcess.value = ''
}

/**
 * 「計画対象期間選択アイコンボタン」押下
 */
const planPeriodIconBtnClick = async () => {
  // 入力内容パラメータ設定
  localOneway.orX0115Oneway = {
    kindId: systemCommonsStore.getSyubetu ?? '',
    sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
  }
  // 画面データ変更ありの場合
  switch (await getDataChangeResult()) {
    case TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE:
      // 開閉フラグを更新する
      OrX0115Logic.state.set({
        uniqueCpId: orX0115.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
      // 入力内容を廃棄する、ダイアログを呼び出す
      // 開閉フラグを更新する
      OrX0115Logic.state.set({
        uniqueCpId: orX0115.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
      // 入力内容を保存する、ダイアログを呼び出す
      // 保存イベント発火
      setEvent({ saveEventFlg: true })
      // 保存後処理タイプを設定する
      afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.PLAN_PERIOD_ID_SELECT
      break
  }
}

/**
 * 「履歴選択アイコンボタン」押下
 */
const historyIconBtnClick = async () => {
  // 入力パラメータ設定
  localOneway.or10926Oneway = {
    userId: userId.value,
    officeId: officeInfo.value?.svJigyoId ?? '',
    planPeriodId: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
    mode: localOneway.or13872Oneway.historyInfo?.rirekiId ?? '',
  }

  switch (await getDataChangeResult()) {
    case TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE:
      // 開閉フラグを更新する
      Or10926Logic.state.set({
        uniqueCpId: or10926.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
      // 入力内容を廃棄する、ダイアログを呼び出す
      // 開閉フラグを更新する
      Or10926Logic.state.set({
        uniqueCpId: or10926.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
      // 入力内容を保存する、ダイアログを呼び出す
      // 保存イベント発火
      setEvent({ saveEventFlg: true })
      // 保存後処理タイプを設定する
      afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.HISTORY_ID_SELECT
      break
  }
}

/**
 * 「作成者選択アイコンボタン」押下
 */
const managerIconBtnClick = () => {
  // 入力パラメータ設定
  // アカウント設定：-
  localOneway.or26257Oneway.secAccountAllFlg = ''
  // フィルターフラグ：1
  localOneway.or26257Oneway.filterDwFlg = '1'
  // モード：12
  localOneway.or26257Oneway.selectMode = TeX0008Const.DEFAULT.STAFF_DIALOG_MODE
  // 表示名称：-
  localOneway.or26257Oneway.hyoujiColumnList = []
  // 作成日：共通情報.基準日
  localOneway.or26257Oneway.kijunYmd = systemCommonsStore.getSystemDate ?? ''
  // 適用事業所ＩＤリスト：
  // 事業所ID：共通情報.適用事業所IDリスト.事業所ID
  localOneway.or26257Oneway.svJigyoIdList =
    systemCommonsStore.getSvJigyoIdList.map((item) => {
      return { svJigyoId: item }
    }) ?? []
  // 雇用状態：-
  localOneway.or26257Oneway.koyouState = ''
  // 地域フラグ：-
  localOneway.or26257Oneway.areaFlg = ''
  // 未設定フラグ：1
  localOneway.or26257Oneway.misetteiFlg = TeX0008Const.DEFAULT.NO_SETTING_FLG
  // 中止フラグ：-
  localOneway.or26257Oneway.refStopFlg = ''
  // 職員IDリスト：-
  localOneway.or26257Oneway.shokuinIdList = []
  // 件数フラグ：-
  localOneway.or26257Oneway.kensuFlg = ''
  // GUI00220 職員検索画面をポップアップで起動する。
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 職員選択画面での返却情報を取得する
 *
 * @param managerInfo - 職員詳細
 */
const getManagerInfo = (managerInfo: Or26257Type) => {
  // 画面.作成者名＝返却情報.選択された職員名（姓）+" "+返却情報.選択された職員名（名）
  // localOneway.or13850Oneway.name =
  //   managerInfo.shokuin.shokuin1Knj + ' ' + managerInfo.shokuin.shokuin2Knj
  local.orX0157.value = managerInfo.shokuin.shokuin1Knj + ' ' + managerInfo.shokuin.shokuin2Knj
  staffId = managerInfo.shokuin.chkShokuId
  // ・親情報を設定 ※親情報が、すべてのタブ間で共有する
  //   親情報.作成者ID = 画面.作成者ID
  setCommonInfo({
    createUserId: managerInfo.shokuin.chkShokuId,
    createUserName: local.orX0157.value,
  })
}

/**
 * 履歴ダイアログ返却値を取得
 *
 * @param cc1Id - ケアチェックID
 */
const getNewCareCheckId = async (cc1Id: string) => {
  // 選択前の履歴から変更がない場合
  if (cc1Id === localOneway.or13872Oneway.historyInfo?.rirekiId) {
    return
  }
  // 画面.ケアチェックID=返却情報.ケアチェックID
  // 画面.履歴変更区分 = "0"
  // 初期表示「AC001」を実行する
  // 歴史情報取得
  await getHistoryInfo(cc1Id)
}

/**
 * 「優先順位ボタン」押下
 */
const setShowPriorityOrderDialog = async () => {
  const updateKbn = TeX0008Logic.data.get(props.uniqueCpId)?.historyUpdateKbn
  // 画面入力データに変更がある場合 また 期間内履歴が更新されていない場合
  if (isEdit.value || updateKbn === UPDATE_KBN.CREATE || updateKbn === UPDATE_KBN.DELETE) {
    // メッセージID：i.cmn.11259
    // メッセージ内容：「({0})の変更を更新していません。更新して終了しますか？」
    // {0}：ケアチェック表
    const result = await openConfirmDialog1(t('message.i-cmn-11259', [t('label.care-table')]))
    // はい：イ保存処理を行って、処理続き。
    if (result === DIALOG_BTN.YES) {
      setEvent({ saveEventFlg: true })
      afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.JUNI_DIALOG
    }
    // いいえ：処理終了。
    else if (result === DIALOG_BTN.NO) {
      return
    }
    // キャンセル：処理終了。
    else if (result === DIALOG_BTN.CANCEL) {
      return
    }
  } else {
    // 入力パラメータ
    // 計画期間ID：画面.計画対象期間ID
    localOneway.or51105Oneway.sc1Id = localOneway.or13844Oneway.planningPeriodInfo?.id ?? ''
    // ケアチェックID：画面.ケアチェックID
    localOneway.or51105Oneway.cc1Id = localOneway.or13872Oneway.historyInfo?.rirekiId ?? ''
    // 「優先順位」画面をポップアップで起動する。
    Or51105Logic.state.set({
      uniqueCpId: or51105.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 「記号意味ボタン」押下
 */
const showMarkAndMeaningDialog = async () => {
  // 入力パラメータ設定
  localOneway.or51885Oneway = {
    // 計画期間ID：画面.計画対象期間ID
    sc1Id: localOneway.or13844Oneway.planningPeriodInfo?.id ?? '',
    // ケアチェックID：画面.ケアチェックID
    cc1Id: localOneway.or13872Oneway.historyInfo?.rirekiId ?? '',
    // 施設ID：親画面.施設ID
    shisetuId: officeInfo.value?.shisetuId ?? '',
    // 事業所ID：親画面.事業所ID
    svJigyoId: officeInfo.value?.svJigyoId ?? '',
    // 職員ID：共通情報.職員ID
    shokuId: staffId ?? '',
    // 法人ID：共通情報.法人ID
    houjinId: systemCommonsStore.getHoujinId ?? '',
    // 利用者ID：親画面.利用者ID
    userID: userId.value,
  }
  // 画面入力データに変更がある場合 また
  // 期間内履歴が更新されていない場合
  const updateKbn = TeX0008Logic.data.get(props.uniqueCpId)?.historyUpdateKbn
  if (isEdit.value || updateKbn === UPDATE_KBN.CREATE || updateKbn === UPDATE_KBN.DELETE) {
    // メッセージID：i.cmn.11259
    // メッセージ内容：「({0})の変更を更新していません。更新して終了しますか？」
    // {0}：ケアチェック表
    const result = await openConfirmDialog1(t('message.i-cmn-11259', [t('label.care-table')]))
    // はい：イ保存処理を行って、処理続き。
    if (result === DIALOG_BTN.YES) {
      setEvent({ saveEventFlg: true })
      afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.ALL_VIEW
    }
    // いいえ：処理終了。
    else if (result === DIALOG_BTN.NO) {
      return
    }
    // キャンセル：処理終了。
    else if (result === DIALOG_BTN.CANCEL) {
      return
    }
  } else {
    // 「記号と意味」画面をポップアップで起動する
    Or51885Logic.state.set({
      uniqueCpId: or51885.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * タブ変更
 *
 * @param newTab - 変更後タブID
 */
const beforeTabChange = async (newTab: Mo00043Type) => {
  // 画面更新区分チェック
  switch (await getDataChangeResult()) {
    case TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE:
      // データ変更なし、タブ変更を行う
      local.mo00043.id = newTab.id
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
      // 画面入力変更あり、編集を破棄して処理を続行
      // 新規の場合は、新規前のデータを画面に設定する
      if (createNewClickNumberOfTimes.value >= 1) {
        localOneway.or13872Oneway.historyInfo = {
          rirekiId: srceenInfoBk.careCheckId,
          krirekiNo: srceenInfoBk.historyNo,
          krirekiCnt: srceenInfoBk.historyCount,
        }
        localOneway.or13844Oneway.planningPeriodInfo!.id = srceenInfoBk.planPeriodId
        // localOneway.or13850Oneway = {
        //   id: srceenInfoBk.createUserId,
        //   name: srceenInfoBk.createUserName,
        // }
        staffId = srceenInfoBk.createUserId
        local.orX0157.value = srceenInfoBk.createUserName
        local.createDate.value = srceenInfoBk.createYmd
        // 新規ボタン押下回数クリア
        createNewClickNumberOfTimes.value = 0

        setCommonInfo({
          cc1Id: srceenInfoBk.careCheckId,
          sc1Id: srceenInfoBk.planPeriodId,
          createYmd: srceenInfoBk.createYmd,
          createUserId: srceenInfoBk.createUserId,
        })
      }
      local.mo00043.id = newTab.id
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
      // 画面変更を保存後変更する
      // 保存イベント発火
      setEvent({ saveEventFlg: true })
      // 保存後処理タイプと変更後のタブIDを一時保存する
      afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.TAB_CHANGE
      afterProcess.value = newTab.id
      break
    case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_CLOSE_DIALOG:
      // ダイアログをキャンセルする、処理終了
      break
  }
}

/**
 * 「ログ」ボタン押下
 */
const userLogClick = () => {
  const inputData = 'これはログ'

  // モックファイルを作成
  const jsonData = JSON.stringify(
    {
      log: inputData,
    },
    null,
    2
  )

  // blobを作成
  const blob = new Blob([jsonData], { type: 'application/json' })

  // ダウンロードリンクを画面にハイパーリンクの形で作成
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `${new Date().getTime().toString()}.xps`
  link.click()

  // ダウンロードリンクを削除する
  URL.revokeObjectURL(link.href)
  // ハイパーリンクを削除する
  document.removeChild(link)
}
// 初期化処理
onMounted(() => {
  local.mo00043.id = Or03243Const.DEFAULT.TAB_ID
  setCommonInfo({ activeTabId: local.mo00043.id })
  // ブラウザの「戻る」「進む」ボタンからの遷移時に以前の値を復元したい画面の場合は
  // 画面情報領域のsupplement内のisInitで処理を分岐させて初回表示時のみ画面データの取得を行なう
  // ※値の復元が不要な画面では分岐は不要
  screenInitFlg = false
  if (isInit) {
    // コントロール設定
    initControl()
  }
})

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 新しいデータの値をチェックし、全falseの場合は処理を中断する
    const isAllFalseFlg = Object.values(newValue).every((item) => item === false)
    if (isAllFalseFlg) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      setOr11871Event({ favoriteEventFlg: false })
      return
    }
    if (newValue.saveEventFlg) {
      setOr11871Event({ saveEventFlg: false })
      // 保存ボタンが押下された場合、保存処理を実行する
      setEvent({ saveEventFlg: true })
      createNewClickNumberOfTimes.value = 0
      return
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      setOr11871Event({ createEventFlg: false })
      await createNew()
      return
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
      setOr11871Event({ printEventFlg: false })
      setShowPrintDialog()
      return
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      // アセスメントマスタのダイアログを開く
      await clickMasterIconBtn()
      setOr11871Event({ masterEventFlg: false })
      return
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      await userDelete()
      setOr11871Event({ deleteEventFlg: false })
      return
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      setOr11871Event({ copyEventFlg: false })
      return
    }
  }
)

/**
 * 画面state監視
 */
watch(
  () => TeX0008Logic.state.get(props.uniqueCpId),
  async (newValue) => {
    if (newValue?.saveCompletedState) {
      // 上記で戻るエラー区分が「1」の場合
      if (newValue.errorKbn === '1') {
        // ■以下のメッセージを表示
        // e.cmn.40019
        // ・後続処理
        // はい：ダイアログ画面を閉じる、処理が正常に終了する
        setShowDialog(t('message.e-cmn-40019'))
        return
      }
      // 上記で戻るエラー区分が「2」の場合
      else if (newValue.errorKbn === '2') {
        // ■以下のメッセージを表示
        // i.cmn.XXXXX
        // ・後続処理
        // はい：ダイアログ画面を閉じる、処理続き
        setShowDialog(t('message.e-cmn-40019'))
      }
      // 各アクションボタンを開放する
      // 作成日
      localOneway.createDateOneway.disabled = false
      // 削除ボタン
      clickDeleteButtonFlg.value = false
      // 作成者選択ボタン
      localOneway.orX0157Oneway.text!.orX0157InputOneway.disabled = false
      createNewClickNumberOfTimes.value = 0
      if (afterProcess.type !== '') {
        await checkProcessType()
        // 画面再表示イベント発火を中断する
        return
      }
      // 子画面保存処理返却値を取得する
      const commonInfo = TeX0008Logic.data.get(props.uniqueCpId)
      // 画面更新区分チェック
      if (!commonInfo) return
      if (commonInfo.childrenScreenApiResult?.sc1Id && commonInfo.childrenScreenApiResult?.cc1Id) {
        // 新規→保存後画面再表示する場合
        // 削除（現在表示している画面のみ削除する、表示している画面を履歴ごと削除する）
        // →保存後画面再表示する場合
        localOneway.or13844Oneway.planningPeriodInfo!.id = commonInfo.childrenScreenApiResult.sc1Id
        // 履歴情報取得
        await getHistoryInfo(commonInfo.childrenScreenApiResult.cc1Id)
      }
      setState({ saveCompletedState: false })
    }
  }
)

/**
 * 歴史変更監視
 */
watch(
  () => Or13872Logic.event.get(or13872.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.preBtnClickFlg === false && newValue.nextBtnClickFlg === false) {
      return
    }
    // 1件目データが表示されている状態
    if (
      newValue?.preBtnClickFlg &&
      localOneway.or13872Oneway.historyInfo?.krirekiNo ===
        TeX0008Const.DEFAULT.BEFORE_PAGE_CHANGE_RESULT
    ) {
      setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })

      return
    }
    // 最終件目データが表示されている状態
    if (
      newValue?.nextBtnClickFlg &&
      localOneway.or13872Oneway.historyInfo?.krirekiNo ===
        localOneway.or13872Oneway.historyInfo?.krirekiCnt
    ) {
      setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })

      return
    }
    // 新規の場合、「<」ボタンを押下する時
    // 現在の期間IDより、初期化情報を再実行
    if (newValue?.preBtnClickFlg && createNewClickNumberOfTimes.value >= 1) {
      createNewClickNumberOfTimes.value = 0
      setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
      await getInitDataInfoFromPlanPeriodId(localOneway.or13844Oneway.planningPeriodInfo?.id ?? '')
      return
    }
    const pageFlg = newValue?.preBtnClickFlg
      ? TeX0008Const.DEFAULT.PAGE_CHANGE_FLG_PREV
      : TeX0008Const.DEFAULT.PAGE_CHANGE_FLG_NEXT

    switch (await getDataChangeResult()) {
      case TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE:
        // 画面変更なしの場合
        await historyChange(pageFlg)
        setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })

        break
      case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
        // 入力内容を廃棄し、初期化処理を行う
        await historyChange(pageFlg)
        setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })

        break
      case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
        // 入力内容を保存する、初期化処理を行う
        setEvent({ saveEventFlg: true })
        // 保存後処理タイプを一時保存する
        afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.HISTORY_CHANGE
        afterProcess.value = pageFlg
        break
    }

    // localOneway.or13850Oneway.iconBtnDisabled = false
    localOneway.orX0157Oneway.text!.orX0157InputOneway.disabled = false
    localOneway.createDateOneway.disabled = false
    clickDeleteButtonFlg.value = false
    createNewClickNumberOfTimes.value = 0
    setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
  }
)
/**
 * 計画対象期間変更監視
 */
watch(
  () => Or13844Logic.event.get(or13844.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.preBtnClickFlg === false && newValue?.nextBtnClickFlg === false) {
      return
    }

    const pageFlg = newValue?.preBtnClickFlg
      ? TeX0008Const.DEFAULT.PAGE_CHANGE_FLG_PREV
      : TeX0008Const.DEFAULT.PAGE_CHANGE_FLG_NEXT
    switch (await getDataChangeResult()) {
      case TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE:
        // 画面変更なしの場合
        await planningPeriodChange(pageFlg)
        break
      case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
        // 入力内容を廃棄し、初期化処理を行う
        await planningPeriodChange(pageFlg)
        break
      case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
        // 入力内容を保存する、初期化処理を行う
        setEvent({ saveEventFlg: true })
        // 保存後処理タイプを一時保存する
        afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.PLAN_PERIOD_CHANGE
        afterProcess.value = pageFlg
        break
    }
    setOr13844Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
  }
)

// 事業所プルダウン変更監視
watch(
  () => officeInfo.value,
  async (newValue, oldValue) => {
    if (!oldValue || !newValue) return

    const commonInfo = TeX0008Logic.data.get(props.uniqueCpId)

    // 新しいデータと共通情報が一致する場合、処理を中断する
    if (commonInfo?.svJigyoId === newValue.svJigyoId) return

    switch (await getDataChangeResult()) {
      case TeX0008Const.DEFAULT.IS_EDIT_NO_CHANGE:
        // 画面変更なしの場合
        // 初期化処理を行う
        setCommonInfo({
          houjinId: newValue.houjinId,
          shisetuId: newValue.shisetuId,
          svJigyoId: newValue.svJigyoId,
        })
        await getInitDataInfo()
        break
      case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_DISCARD:
        // 入力内容を廃棄し、初期化処理を行う
        setCommonInfo({
          houjinId: newValue.houjinId,
          shisetuId: newValue.shisetuId,
          svJigyoId: newValue.svJigyoId,
        })
        await getInitDataInfo()
        break
      case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_SAVE:
        // 入力内容を保存する、初期化処理を行う
        setEvent({ saveEventFlg: true })
        // 保存後処理タイプを一時保存する
        afterProcess.type = TeX0008Const.DEFAULT.SAVE_COMPLETED_AFTER_PROCESS.OFFICE_CHANGE
        break
      case TeX0008Const.DEFAULT.IS_EDIT_CHANGE_CLOSE_DIALOG:
        // 処理を中断する、前の事業所に戻る
        Or41179Logic.data.set({
          uniqueCpId: or41179.value.uniqueCpId,
          value: {
            modelValue: commonInfo?.svJigyoId,
          },
        })
    }
  },
  { deep: true }
)

// 計画対象期間選択変更ワッチャー
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  async (newValue) => {
    // 選択前の対象期間から変更がない場合
    if (newValue?.kikanId === localOneway.or13844Oneway.planningPeriodInfo?.id) {
      // 処理終了
      return
    }
    // 返す期間IDが空ではなく、かつ、返す期間IDが「0」ではない場合
    //  画面共通情報.ケアチェックID（ゼロに設定）
    if (newValue?.kikanId !== '' && newValue?.kikanId !== '0') {
      setCommonInfo({
        sc1Id: '0',
      })
    }
    if (newValue) {
      // 計画対象期間情報を再取得
      await getInitDataInfoFromPlanPeriodId(newValue.kikanId)
    }
  }
)

// マスタ画面state変更監視
watch(
  () => Or27825Logic.state.get(or41345.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (newValue) return
    // ポップアップ画面閉じる
    // ・初期設定マスタ取得（getInitialSettingMaster）
    const _markMeaning = cmnRouteCom.getInitialSettingMaster()
    // ※共通項目「記号意味（画面表示）、記号意味（印刷）」を再設定する
    // ・画面.期間ID = ""
    // ・画面.計画期間ページ区分 = "0"
    // ・「食事」タブを選択する
    // ・初期表示「AC001」を実行する
    local.mo00043.id = Or03243Const.DEFAULT.TAB_ID
    await getInitDataInfo()
  }
)

// 優先順位ダイアログ開閉フラグ変更監視
watch(
  () => Or51105Logic.state.get(or51105.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    // isOpen が存在し、かつ false の場合の処理
    if (newValue === false) {
      // ポップアップ画面閉じる
      // 期間IDを指定して、初期化処理再実行
      await getInitDataInfoFromPlanPeriodId(localOneway.or13844Oneway.planningPeriodInfo?.id ?? '')
    }
  }
)

// 「記号意味」ダイアログ開閉フラグ変更監視
watch(
  () => Or51885Logic.state.get(or51885.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (newValue === false) {
      // ポップアップ画面閉じる
      // 期間IDを指定して、初期化処理再実行
      await getInitDataInfoFromPlanPeriodId(localOneway.or13844Oneway.planningPeriodInfo?.id ?? '')
    }
  }
)

// タブID変更監視
watch(
  () => local.mo00043.id,
  (newValue) => {
    setCommonInfo({
      activeTabId: newValue,
    })
    // 新規後未保存の場合
    const commonInfo = TeX0008Logic.data.get(props.uniqueCpId)
    if (commonInfo?.historyUpdateKbn === UPDATE_KBN.CREATE) {
      setEvent({ createEventFlg: true })
    } else {
      setEvent({ isRefresh: true })
    }
  }
)
</script>

<template>
  <c-v-sheet class="view d-flex flex-column h-100">
    <!-- 操作ボタンエリア -->
    <div class="action-sticky">
      <g-base-or11871 v-bind="or11871">
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            prepend-icon="file_copy"
            @click="beforeShowDuplicateDialog"
          />

          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.duplicate')"
          ></c-v-tooltip>
        </template>
        <template #customButtons>
          <base-at-button
            class="action-button mr-2"
            @click="showAllListDialog"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('label.assessment-comprehensive-master-all-tooltip')"
            ></c-v-tooltip>
            {{ t('label.assessment-comprehensive-master-all') }}</base-at-button
          >
          <base-at-button
            class="action-button mr-2"
            @click="setShowPriorityOrderDialog"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('label.assessment-comprehensive-master-priority-tooltip')"
            ></c-v-tooltip
            >{{ t('label.assessment-comprehensive-master-priority') }}</base-at-button
          >
          <base-at-button
            class="action-button mr-2"
            @click="showMarkAndMeaningDialog"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('label.assessment-comprehensive-master-mark-mean-tooltip')"
            ></c-v-tooltip
            >{{ t('label.assessment-comprehensive-master-mark-mean') }}</base-at-button
          >
        </template>
        <template #optionMenuItems>
          <c-v-list-item
            :title="$t('btn.log')"
            prepend-icon="file_upload"
            @click="userLogClick"
          >
            <c-v-tooltip
              :text="$t('tooltip.log')"
              activator="parent"
              location="left"
          /></c-v-list-item>
        </template>
      </g-base-or11871>
    </div>
    <!-- アクションエリア -->
    <div class="action-content">
      <c-v-row
        no-gutters
        class="main-Content d-flex flex-0-1 h-100 overflow-y-auto position-relative"
      >
        <!-- ナビゲーションエリア -->
        <c-v-col
          cols="auto"
          class="hidden-scroll h-100 user-select"
        >
          <!-- 利用者選択一覧 -->
          <g-base-or00248 v-bind="or00248" />
        </c-v-col>
        <!-- コンテンツエリア -->
        <c-v-col class="main-right d-flex flex-column overflow-hidden h-100">
          <c-v-row
            no-gutters
            class="d-flex align-center"
          >
            <!-- 事業所 -->
            <c-v-col
              cols="auto ml-6 mr-6 office-select align-self-end"
              style="width: 220px"
            >
              <g-base-or41179 v-bind="or41179" />
            </c-v-col>
            <!-- 計画期間 -->
            <c-v-col
              v-if="plannningPeriodManageFlg === TeX0008Const.DEFAULT.PLANNING_PERIOD_MANAGE"
              cols="auto mr-6 align-self-end"
            >
              <!-- 計画対象期間 -->
              <g-custom-or13844
                v-bind="or13844"
                :oneway-model-value="localOneway.or13844Oneway"
                @open-btn-click="planPeriodIconBtnClick"
              ></g-custom-or13844>
              <!-- 計画期間選択画面 -->
              <g-custom-or-x-0115
                v-if="showDialogOrX0115"
                v-bind="orX0115"
                :oneway-model="localOneway.orX0115Oneway"
              />
            </c-v-col>
            <!-- 作成日 -->
            <c-v-col
              v-if="
                plannningPeriodManageFlg === TeX0008Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
                historyDisplay
              "
              cols="auto mr-6 align-self-end create-ymd-select"
              style="width: 200px"
            >
              <base-mo00020
                v-model="local.createDate"
                :oneway-model-value="localOneway.createDateOneway"
                @update:model-value="createDateChange"
              />
            </c-v-col>
            <!-- 作成者設定 -->
            <c-v-col
              v-if="
                plannningPeriodManageFlg === TeX0008Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
                historyDisplay
              "
              cols="auto mt-2  mr-6 align-self-end create-user-select"
            >
              <g-custom-or-x-0157
                v-model="local.orX0157"
                :oneway-model-value="localOneway.orX0157Oneway"
                @on-click-edit-btn="managerIconBtnClick"
              />
              <g-custom-or26257
                v-if="showDialogOr26257"
                v-bind="or26257"
                :oneway-model-value="localOneway.or26257Oneway"
                @update:model-value="getManagerInfo"
              />
            </c-v-col>
            <!-- 歴史選択 -->
            <c-v-col
              v-if="historyDisplay"
              cols="auto align-self-end"
            >
              <g-custom-or13872
                v-show="historyDisplay"
                v-bind="or13872"
                :oneway-model-value="localOneway.or13872Oneway"
                @open-btn-click="historyIconBtnClick"
              ></g-custom-or13872>
              <g-custom-or10926
                v-if="showDialogOr10926"
                v-bind="or10926"
                :oneway-model-value="localOneway.or10926Oneway"
                @update:model-value="getNewCareCheckId"
              />
            </c-v-col>
          </c-v-row>
          <!-- タブ欄 -->
          <c-v-row
            no-gutters
            class="mt-6"
          >
            <c-v-col>
              <base-mo00043
                :model-value="local.mo00043"
                :oneway-model-value="localOneway.mo00043Oneway"
                @update:model-value="beforeTabChange"
              >
              </base-mo00043>
            </c-v-col>
          </c-v-row>
          <!-- 子画面エリア -->
          <c-v-row
            no-gutters
            class="middleContent flex-1-1 h-100"
          >
            <c-v-window
              id="tabWindow"
              v-model="local.mo00043.id"
              class="h-100"
            >
              <!-- タブ1 -->
              <c-v-window-item :value="Or03243Const.DEFAULT.TAB_ID">
                <g-custom-or03243
                  v-bind="or03243"
                  :oneway-model-value="localOneway.or03243Oneway"
                />
              </c-v-window-item>

              <!-- タブ2 -->
              <c-v-window-item :value="Or03250Const.DEFAULT.TAB_ID">
                <g-custom-or03250
                  v-bind="or03250"
                  :oneway-model-value="localOneway.or03250Oneway"
              /></c-v-window-item>

              <!-- タブ3 -->
              <c-v-window-item
                :value="Or03249Const.DEFAULT.TAB_ID"
                class="h-100 overflow-y-auto"
              >
                <g-custom-or03249
                  v-bind="or03249"
                  :oneway-model-value="localOneway.or03249Oneway"
              /></c-v-window-item>

              <!-- タブ4 -->
              <c-v-window-item
                :value="Or03245Const.DEFAULT.TAB_ID"
                class="h-100 overflow-y-auto"
              >
                <g-custom-or03245
                  v-bind="or03245"
                  :oneway-model-value="localOneway.or03245Oneway"
              /></c-v-window-item>
              <c-v-window-item
                value="4"
                class="h-100 overflow-y-auto"
              ></c-v-window-item>

              <!-- タブ5 -->
              <c-v-window-item :value="Or03240Const.DEFAULT.TAB_ID">
                <g-custom-or03240
                  v-bind="or03240"
                  :oneway-model-value="localOneway.or03240Oneway"
                />
              </c-v-window-item>

              <c-v-window-item
                value="5"
                class="h-100 overflow-y-auto"
              ></c-v-window-item>

              <!-- タブ6 -->
              <c-v-window-item
                :value="Or03237Const.DEFAULT.TAB_ID"
                class="h-100 overflow-y-auto"
              >
                <g-custom-or03237
                  v-bind="or03237"
                  :oneway-model-value="localOneway.or03237Oneway"
              /></c-v-window-item>

              <!-- タブ7 -->
              <c-v-window-item
                value="7"
                class="h-100 overflow-y-auto"
              >
                <g-custom-or03244
                  v-bind="or03244"
                  :oneway-model-value="localOneway.or03244Oneway"
              /></c-v-window-item>
            </c-v-window>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </div>
    <!-- 介護課題リストダイアログ -->
    <g-base-or21814
      v-if="showDialog"
      v-bind="or21814"
    />
    <!-- テーブルデータ削除確認ダイアログ -->
    <g-base-or21814
      v-if="showDialogOrX0096_2"
      v-bind="or21814_2"
    />
    <!-- 新規処理確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_3" />
    <!-- 画面削除確認ダイアログ -->
    <g-custom-or-x-0001
      v-if="showDialogOrX0001"
      v-bind="orX0001"
      v-model="local.orX0001Type"
      :oneway-model-value="localOneway.orX0001Oneway"
    ></g-custom-or-x-0001>
    <!-- 記号意味モーダル -->
    <g-custom-or51885
      v-if="showDialogOr51885"
      v-bind="or51885"
      :oneway-model-value="localOneway.or51885Oneway"
    />
    <!-- 一覧ダイアログ -->
    <g-custom-or-27216
      v-if="showDialogOr27216"
      v-bind="or27216"
      v-model="local.or27216"
      :oneway-model-value="localOneway.or27216Oneway"
    />
    <!-- 複写ダイアログ -->
    <g-custom-or59423
      v-if="showDialogOr59423"
      v-bind="or59423"
      :key="componentKey"
      :oneway-model-value="localOneway.or5942Oneway"
      @confirm="getDuplicateData"
    />
    <!-- 印刷ダイアログ -->
    <g-custom-or52060
      v-if="showDialogOr52060"
      v-bind="or52060"
    />
    <!-- マスタ画面 -->
    <!-- <g-custom-or-41345
      v-if="showDialogOr41345"
      v-bind="or41345"
      :oneway-model-value="localOneway.or41345Oneway"
    /> -->
    <!-- 優先順位ダイアログ -->
    <g-custom-or51105
      v-if="showDialogOr51105"
      v-bind="or51105"
      :oneway-model-value="localOneway.or51105Oneway"
    />
  </c-v-sheet>
</template>

<style lang="scss" scoped>
.main-Content {
  .main-left {
    max-width: 20%;
  }

  .main-right {
    .middleContent {
      min-height: 0;

      .v-window {
        width: 100%;
      }
    }
  }
}
.v-btn {
  color: rgb(118, 118, 118) !important;
  background-color: transparent !important;
  border: 1px solid rgb(118, 118, 118) !important;
}
.view {
  background-color: transparent;
  height: max-content !important;
}
.createDateOuterClass {
  width: auto !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }

  :deep(.v-input__details) {
    display: none;
  }
  :deep(.v-input__control) {
    background-color: #fff;
  }
}
:deep(.rounded.item-label) {
  min-width: 180px;
  padding-left: 8px !important;
}
.action-button {
  background-color: #fff !important;
  width: 79px !important;
  padding: 0px !important;
  min-width: auto !important;
  &:first-child {
    width: 54px !important;
  }
}
.action-sticky {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
  button {
    height: 32px !important;
  }
}
:deep(.action-sticky .v-btn) {
  height: 32px !important;
  min-height: 32px !important;
  &:nth-child(4) {
    width: 76px !important;
  }
}
.action-content {
  background: rgb(var(--v-theme-background));
  height: max-content;
  z-index: 888;
}
// 利用者一覧選択領域領域
:deep(.user-select > .v-card) {
  padding: 0px 0px 0px 16px !important; // パディング設定
  width: max-content !important; // 幅を自動調整
  margin-top: 16px !important; // 上部マージン
  // パンディング設定
  .v-card-actions {
    width: max-content !important; // 幅を自動調整
    padding: 8px !important; // パディング設定
  }
  // 選択領域の幅設定
  .user-info-section-container-style {
    width: 260px; // 幅を260pxに設定
  }
}

/***************************************************************
 * 分子：「Mo00043_タブ選択」スタイル 補足
 ***************************************************************/
:deep(.v-slide-group) {
  height: 36px !important; // 高さを36pxに設定
  padding: 0px 32px !important; // パディングを解除
}
// 選択タブアイテム設定
:deep(.v-slide-group__content > .v-tab) {
  height: 36px !important; // 高さを36pxに設定
  padding: 0px 4px !important; // パディング設定
  width: max-content !important; // 幅を自動調整
  min-width: 44px !important; // 最小幅を自動調整
  margin-right: 20px !important;
}

.create-ymd-select {
  width: 140px !important;
  :deep(.v-input) {
    width: 130px !important;
  }
}
.create-user-select {
  :deep(.v-input) {
    width: 160px !important;
  }
}
</style>
