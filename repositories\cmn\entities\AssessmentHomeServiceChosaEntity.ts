import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * ［アセスメント］画面（居宅）（3）
 *
 * @description
 * ［アセスメント］画面（居宅）（3）調査票用APIエンティティ
 *
 * <AUTHOR>
 */
/**
 * ［アセスメント］画面（居宅）（3）(調査票のデータ件数を取得する)取得入力エンティティ
 */
export interface AssessmentHomeServiceChosaCntSelectInEntity extends InWebEntity {
  /** 計画期間ID */
  sc1Id: string
  /** 事業所ID */
  svJigyoId: string
  /** 利用者ID */
  userid: string
}

/**
 * ［アセスメント］画面（居宅）（3）(調査票のデータ件数を取得する)出力エンティティ
 */
export interface AssessmentHomeServiceChosaCntSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** データ件数 */
    chosaCnt: string
  }
}

/**
 * ［アセスメント］画面（居宅）（3）(指定された認定調査票情報を取得する)取得入力エンティティ
 */
export interface AssessmentHomeServiceChosaInfoSelectInEntity extends InWebEntity {
  /** 計画期間ID */
  sc1Id: string
  /** 調査票ID */
  cschId: string
}

/**
 * ［アセスメント］画面（居宅）（3）(指定された認定調査票情報を取得する)出力エンティティ
 */
export interface AssessmentHomeServiceChosaInfoSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** 改訂版認定調査票情報 */
    cpnTucCsc22H21Info: {
      /**
       * 調査票ID
       */
      cschId: string

      /**
       * 計画期間ID
       */
      sc1Id: string

      /**
       * サービス利用CD1
       */
      service1Cd: string

      /**
       * サービス利用CD2
       */
      service2Cd: string

      /**
       * サービス利用CD3
       */
      service3Cd: string

      /**
       * サービス利用CD4
       */
      service4Cd: string

      /**
       * サービス利用CD5
       */
      service5Cd: string

      /**
       * サービス利用CD6
       */
      service6Cd: string

      /**
       * サービス利用CD7
       */
      service7Cd: string

      /**
       * サービス利用CD8
       */
      service8Cd: string

      /**
       * サービス利用CD9
       */
      service9Cd: string

      /**
       * サービス利用CD10
       */
      service10Cd: string

      /**
       * サービス利用CD11
       */
      service11Cd: string

      /**
       * サービス利用CD12
       */
      service12Cd: string

      /**
       * サービス利用CD13
       */
      service13Cd: string

      /**
       * サービス利用CD14
       */
      service14Cd: string

      /**
       * サービス利用CD15
       */
      service15Cd: string

      /**
       * サービス利用CD16
       */
      service16Cd: string

      /**
       * 利用回数1
       */
      kaisuu1: string

      /**
       * 利用回数2
       */
      kaisuu2: string

      /**
       * 利用回数3
       */
      kaisuu3: string

      /**
       * 利用回数4
       */
      kaisuu4: string

      /**
       * 利用回数5
       */
      kaisuu5: string

      /**
       * 利用回数6
       */
      kaisuu6: string

      /**
       * 利用回数7
       */
      kaisuu7: string

      /**
       * 利用回数8
       */
      kaisuu8: string

      /**
       * 利用回数9
       */
      kaisuu9: string

      /**
       * 利用回数10
       */
      kaisuu10: string

      /**
       * 利用回数11
       */
      kaisuu11: string

      /**
       * 利用回数12
       */
      kaisuu12: string

      /**
       * 利用回数13
       */
      kaisuu13: string

      /**
       * 改修ありなし
       */
      kaishuUmu: string

      /**
       * 特別給付
       */
      memo1Knj: string

      /**
       * 給付外サービス
       */
      memo2Knj: string

      /**
       * サービス利用CD17
       */
      service17Cd: string

      /**
       * サービス利用CD18
       */
      service18Cd: string

      /**
       * サービス利用CD19
       */
      service19Cd: string

      /**
       * サービス利用CD20
       */
      service20Cd: string

      /**
       * サービス利用CD21
       */
      service21Cd: string

      /**
       * 利用回数17
       */
      kaisuu17: string

      /**
       * 利用回数18
       */
      kaisuu18: string

      /**
       * 利用回数19
       */
      kaisuu19: string

      /**
       * 利用回数20
       */
      kaisuu20: string

      /**
       * 利用回数21
       */
      kaisuu21: string

      /**
       * サービス利用CD22
       */
      service22Cd: string

      /**
       * サービス利用CD23
       */
      service23Cd: string

      /**
       * 利用回数22
       */
      kaisuu22: string

      /**
       * 利用回数23
       */
      kaisuu23: string

      /**
       * 要介護状態見込み
       */
      yokaiKbn1: string

      /**
       * 要介護状態確定
       */
      yokaiKbn2: string
    }

    /** 認定調査票概況調査情報 */
    cpnTucCsc12Info: {
      /**
       * 調査票ID
       */
      cschId: string

      /**
       * 計画期間ID
       */
      sc1Id: string

      /**
       * 地域コード
       */
      chiikiCd: string

      /**
       * 調査対象者コード
       */
      taishoushaCd: string

      /**
       * 実施場所
       */
      whereCd: string

      /**
       * 実施場所その他
       */
      whereMemoKnj: string

      /**
       * 過去の認定
       */
      oldNintei: string

      /**
       * 前回認定日
       */
      oldNinteiYmd: string

      /**
       * 前回認定結果
       */
      oldNinteiDo1: string

      /**
       * 前回認定結果（要介護度）
       */
      oldNinteiDo2: string

      /**
       * 関係者連番
       */
      kankeiId: string

      /**
       * 連絡先氏名
       */
      renrakuNameKnj: string

      /**
       * 連絡先続柄
       */
      renrakuZcode: string

      /**
       * 連絡先〒
       */
      renrakuZip: string

      /**
       * 連絡先TEL
       */
      renrakuTel: string

      /**
       * 県コード
       */
      kencode: string

      /**
       * 市町村コード
       */
      citycode: string

      /**
       * 地区コード
       */
      areacode: string

      /**
       * 連絡先住所
       */
      addressKnj: string

      /**
       * 障害老人ADL
       */
      adl1Id: string

      /**
       * 痴呆老人ADL
       */
      adl2Id: string

      /**
       * 利用施設種別
       */
      shisetsuShu: string

      /**
       * 利用施設ID
       */
      shisetsuId: string

      /**
       * 利用施設名
       */
      shisetsuNameKnj: string

      /**
       * 内容特記事項
       */
      memoKnj: string

      /**
       * 要介護状態見込み
       */
      yokaiKbn1: string

      /**
       * 要介護状態確定
       */
      yokaiKbn2: string

      /**
       * 認定年月日
       */
      ninteiYmd: string

      /**
       * サービス事業者ID
       */
      svJigyoId: string

      /**
       * 認定申請日
       */
      ninteiShinseiYmd: string

      /**
       * 施設名
       */
      shisetuKnj: string

      /**
       * 郵便番号
       */
      shisetsuZip: string

      /**
       * 施設住所
       */
      shisetsuAddressKnj: string

      /**
       * 電話番号
       */
      shisetsuTel: string
    }

    /** 施設マスタ情報 */
    mscShisetuInfo: {
      /**
       * 施設ID
       */
      shisetuId: string

      /**
       * 施設名
       */
      shisetuKnj: string

      /**
       * 調査票施設種別
       */
      scShisetsuShu: string

      /**
       * 住所
       */
      addressKnj: string

      /**
       * TEL
       */
      tel: string

      /**
       * 郵便番号
       */
      zip: string
    }
  }
}
