<script setup lang="ts">
/**
 * GUI04473_日割利用期間
 *
 * @description
 * 日割利用期間
 *
 * <AUTHOR>
 */
import { computed, ref, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or07212Const } from '~/components/custom-components/organisms/Or07212/Or07212.constants'
import { Or07212Logic } from '~/components/custom-components/organisms/Or07212/Or07212.logic'
import type { Or07212OnewayType } from '~/types/cmn/business/components/Or07212Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI04473'
// ルーティング
const routing = 'GUI04473/pinia'
// 画面物理名
const screenName = 'GUI04473'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or07212 = ref({ uniqueCpId: Or07212Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI04473' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  // Or07212Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or07212.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI04473',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or07212Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or07212Const.CP_ID(1)]: or07212.value,
})

// ダイアログ表示フラグ
const showDialogOr07212 = computed(() => {
  // Or07212のダイアログ開閉状態
  return Or07212Logic.state.get(or07212.value.uniqueCpId)?.isOpen ?? false
})

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 利用者ID
  userId: { value: '1' } as Mo00045Type,
})

/**
 *  ボタン押下時の処理(Or07212)
 */
function onClickOr07212() {
  // Or07212のダイアログ開閉状態を更新する
  Or07212Logic.state.set({
    uniqueCpId: or07212.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or07212Data: Or07212OnewayType = {
    userId: '1'
}


/** GUI04473 疎通起動  */
function Or07212onClick() {
  or07212Data.userId = local.userId.value
  // Or07212のダイアログ開閉状態を更新する
  Or07212Logic.state.set({
    uniqueCpId: or07212.value.uniqueCpId,
    state: { isOpen: true },
  })
}

</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr07212"
        >GUI04473_日割利用期間
      </v-btn>
      <g-custom-or-07212
        v-if="showDialogOr07212"
        v-bind="or07212"
        :oneway-model-value="or07212Data"
      />
    </c-v-col>
  </c-v-row>

  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="Or07212onClick"> GUI04473 疎通起動 </v-btn>
  </div>
</template>
