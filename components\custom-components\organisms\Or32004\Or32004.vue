<script setup lang="ts">
/**
 * Or32004: アセスメント(インターライ)画面N
 * GUI00778_アセスメント(インターライ)画面N
 *
 * @description
 * アセスメント(インターライ)画面N
 *
 * <AUTHOR> PHAM HO HAI DANG
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, watch, computed, onMounted } from 'vue'
import { Or10412Logic } from '../Or10412/Or10412.logic'
import { Or10412Const } from '../Or10412/Or10412.constants'
import { Or32007Const } from '../Or32007/Or32007.constants'
import { Or32007Logic } from '../Or32007/Or32007.logic'
import { Or10889Const } from '../Or10889/Or10889.constants'
import { Or10889Logic } from '../Or10889/Or10889.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or32004Const } from './Or32004.constants'
import type { Or32004StateType, Or32004TwoWayType, TableData } from './Or32004.type'
import type { Or32004OnewayType } from '~/types/cmn/business/components/Or32004Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00038OnewayType, Mo00038Type } from '~/types/business/components/Mo00038Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Or10412OnewayType, Or10412Type } from '~/types/cmn/business/components/Or10412Type'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenInitFlg,
  useCmnRouteCom,
  useScreenTwoWayBind,
} from '#imports'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type {
  OrCpGroupDefinitionInputFormDeleteDialogType,
  OrCpGroupDefinitionInputFormDeleteDialogOnewayType,
} from '~/types/cmn/business/generator-components/OrCpGroupDefinitionInputFormDeleteDialog'
import { Or09995Const } from '~/components/custom-components/organisms/Or09995/Or09995.constants'
import { Or09995Logic } from '~/components/custom-components/organisms/Or09995/Or09995.logic'
import type { Or09995OnewayType } from '~/types/cmn/business/components/Or09995Type'
import type { Mo01278Type } from '~/types/business/components/Mo01278Type'
import type { Mo01274Type } from '~/types/business/components/Mo01274Type'
import type { Mo01282OnewayType, Mo01282Type } from '~/types/business/components/Mo01282Type'
import type {
  AssessmentInterRAINInEntity,
  AssessmentInterRAINOutEntity,
  AssessmentInterRAINUpdateEntity,
  AssessmentInterRAINUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAINEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or10889OnewayType, Or10889Type } from '~/types/cmn/business/components/Or10889Type'
import type {
  AssessmentInterRAIAHistorySelectEntity,
  AssessmentInterRAIAHistorySelectOutEntity,
} from '~/repositories/cmn/entities/assessmentInterRAIAHistoryEntity'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import type { Or30981OnewayType, Or30981Type } from '~/types/cmn/business/components/Or30981Type'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { Mo01355OnewayType } from '@/types/business/components/Mo01355Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type {
  HistoryInfoEntity,
  PlanPeriodInfoEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type {
  Mo01384Type,
  Mo01384OnewayType,
  Mo01384ItemType,
} from '~/types/business/components/Mo01384Type'

/**
 * I18nのt関数
 */
const { t } = useI18n()

/************************************************
 * Props
 ************************************************/
/**
 * Propsの型定義
 */
interface Props {
  onewayModelValue: Or32004OnewayType
  uniqueCpId: string
}
/**
 * Propsの取得
 */
const props = defineProps<Props>()
/**
 * Or32007のref
 */
const or32007 = ref({ uniqueCpId: Or32007Const.CP_ID(1) })
/**
 * Or09995のref
 */
const or09995 = ref({ uniqueCpId: Or09995Const.CP_ID(1) })
/**
 * Or10889のref
 */
const or10889 = ref({ uniqueCpId: Or10889Const.CP_ID(1) })
/**
 * Or21814のref
 */
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })

/**
 * Or51775のref
 */
const or51775 = ref({ uniqueCpId: '' })
/**
 * 初期化フラグ
 */
const isInit = useScreenInitFlg()

/**
 * Or09995の型ref
 */
const or09995Type = ref<
  {
    n1A: string
    n1B: { value: string }
    n1CKnj: { value: string }
    n1D: { value: string }
    n1E: { value: string }
    n1F: { value: string }
  }[]
>([])

/**
 * Or10889の型ref
 */
const or10889Type = ref<Or10889Type>({
  drugId: '',
})

/**
 * ローカルデータ
 */
const local = reactive({
  // 期間管理フラグ
  kikanKanriFlg: '',
  // 利用者ID
  userId: '',
  // 基準日
  kijunbiYmd: '',
  // 履歴情報
  historyInfo: {} as HistoryInfoEntity,
  // 計画期間情報
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  vieTypeKbn: '',
})

/**
 * モード
 */
const mode = ref<string>('')
/**
 * md値
 */
const md = ref<string>('')
// 入力フォーム表示フラグ
/**
 * 入力フォーム表示フラグ
 */
const inputFormDisplayFlag = ref(true)

const or51775Type = ref({
  modelValue: '',
})
/**
 * 親画面からの初期値
 */
const or09995Data: Or09995OnewayType = {
  sortList: [],
}
/**
 * テーブルデータ
 */
const tableData = ref<TableData>({
  yaKuzaiList: [],
})
/**
 * 薬剤名リスト
 */
const MedicineNameItemsList = ref<Mo01384ItemType[]>([])

/**
 * 経路リスト
 */
const RouteItemsList = ref<CodeType[]>([])

/**
 * 頓用リスト
 */
const AsNeededItemsList = ref<CodeType[]>([])

/**
 * テーブルデータのコンポーネントリスト
 */
// const tableDataComponent = ref<{
//   componentList: TableDataComponentItem[]
// }>({
//   componentList: [],
// })

/**
 * 選定表・検討表作成区分
 */
let deleteKbn = ''

/**
 * テーブルヘッダー情報
 */
const headers = ref<unknown[]>([])

/**
 * 選択行インデックス
 */
const selectedItemIndex = ref<number>(0)

/**
 * 行削除確認ダイアログ状態
 */
const orConfirmDeleteRowDialog = ref({
  emitType: '',
  mo00024: {
    isOpen: false,
  } as Mo00024Type,
})

const columnMinWidth = ref<number[]>([320, 100, 100, 180, 100, 120])

/**************************************************
 * Pinia
 **************************************************/
/**
 * レスポンス情報のサブ情報を格納するためのオブジェクト
 */
const { refValue } = useScreenTwoWayBind<Or32004TwoWayType>({
  cpId: Or32004Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
useScreenOneWayBind<Or32004StateType>({
  cpId: Or32004Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 値更新時の処理
     *
     * @param value - 値
     */
    param: (value) => {
      if (value) {
        mode.value = value.historyInfo.assType
        local.kikanKanriFlg = value.kikanKanriFlg + ''
        local.historyInfo = value.historyInfo
        local.planPeriodInfo = value.planPeriodInfo
        // local.userId = value.userId + ''
        local.kijunbiYmd = value.kijunbiYmd + ''
      }
      switch (value?.executeFlag) {
        //保存
        case 'save':
          void save()
          break
        //新規
        case 'add':
          void add()
          break
        //複写
        case 'copy':
          void copy()
          break
        //削除
        case 'delete':
          void del()
          break
        //データ再取得
        case 'getData':
          void init()
          break
        default:
          break
      }
    },
  },
})

/**
 * AC004_「新規ボタン」押下
 */
const add = async () => {
  md.value = 'add'
  // アセスメント(インターライ)画面履歴の最新情報を取得
  await init()
}
/**
 * AC003_「保存ボタン」押下
 */
const save = async () => {
  const historyInfo = await getHistoryInfo()
  if (historyInfo) {
    // 選定アセスメント種別 > 0   AC003-2-2
    if (Number(historyInfo?.capType) > 0) {
      // 検討アセスメント種別 > 0
      if (Number(historyInfo?.plnType) > 0) {
        deleteKbn = '1'
        showOr21814Msg(t('message.i-cmn-11267'))
      }
      // 検討アセスメント種別 <= 0   AC003-2-3
      if (Number(historyInfo?.plnType) <= 0) {
        deleteKbn = '0'
        showOr21814Msg(t('message.i-cmn-11266'))
      }
    }
  } else {
    deleteKbn = ''
    await onSave()
  }
}

/**
 * 保存ボタン押下
 */
const onSave = async () => {
  try {
    const inputData: AssessmentInterRAINUpdateEntity = {
      // 法人ID：共通情報.法人ID
      houjinId: '',
      // 施設ID：共通情報.施設ID
      shisetuId: '',
      // 利用者ID：共通情報.利用者ID
      userId: '',
      // 事業者ID：共通情報.事業者ID
      svJigyoId: '',
      // 種別ID：共通情報.種別ID
      syubetsuId: '',
      // 更新区分：画面.更新区分
      updateKbn: '',
      // 履歴更新区分：画面.履歴更新区分
      historyUpdateKbn: '',
      // 選定表・検討表作成区分：画面.選定表・検討表作成区分
      deleteKbn: deleteKbn,
      //アセスメントID
      raiId: local.historyInfo.raiId,
      // 計画対象期間ID：親情報.計画対象期間ID
      sc1Id: local.historyInfo.sc1Id,
      // 基準日：親情報.基準日
      kijunbiYmd: '',
      // 作成者ID：親情報.作成者ID
      sakuseiId: '',
      // 調査アセスメント種別
      assType: '',
      SubInfoN: {
        n2: mo00038TypeN2TextField.value.mo00045.value,
        n3: mo00038TypeN3TextField.value.mo00045.value,
        n1MemoKnj: N1OneWayModelValue.value[0].memoInputvalue.value.content,
        n1MemoFont: '',
        n1MemoColor: '',
        n2MemoKnj: N2OneWayModelValue.value[0].memoInputvalue.value.content,
        n2MemoFont: '',
        n2MemoColor: '',
        n3MemoKnj: N3OneWayModelValue.value[0].memoInputvalue.value.content,
        n3MemoFont: '',
        n3MemoColor: '',
      },
      tableDatalist: [],
    }
    for (const item of refValue.value!.tableDataComponent.componentList) {
      inputData.tableDatalist.push({
        n1A: item.n1A.modelValue.modelValue!,
        n1B: Number((item.n1B.modelValue as Mo01278Type).value).toString(),
        n1CKnj: item.n1CKnj.modelValue.value,
        n1D: item.n1D.modelValue.modelValue!,
        n1E: Number((item.n1E.modelValue as Mo01278Type).value).toString(),
        n1F: item.n1F.modelValue.modelValue!,
        id: '',
      })
    }
    const resData: AssessmentInterRAINUpdateOutEntity = await ScreenRepository.update(
      'assessmentInterRAINInsert',
      inputData
    )
    /****************************************
     * 保存成功の場合
     ****************************************/
    if (resData.statusCode === 'success') {
      console.log('Data Save: ', inputData)
      // 画面情報再取得
    }

    switch (md.value) {
      case 'add':
        void add()
        break
      case 'copy':
        void copy()
        break
      default:
        break
    }
  } catch (error) {
    console.log(error)
  }
}

/**
 * 「保存ボタン」押下き確認ダイアログ
 *
 * @param message -情報を提示します
 */
const showOr21814Msg = (message: string): void => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: message,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      void onSave()
    }
  }
)

/**
 * AC005_「複写ボタン」押下
 */
const copy = async () => {
  // TODO coppy
  md.value = 'copy'
  await init()
}

/**
 * AC011_「削除」押下
 */
const del = () => {
  // 入力フォームを非表示にする
  inputFormDisplayFlag.value = false
}

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or32007Const.CP_ID(1)]: or32007.value,
  [Or09995Const.CP_ID(1)]: or09995.value,
  [Or10889Const.CP_ID(1)]: or10889.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
})

/**
 * Or32007ダイアログ表示フラグ
 */
const showDialogOr32007 = computed(() => {
  // Or32007のダイアログ開閉状態
  return Or32007Logic.state.get(or32007.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Or10889ダイアログ表示フラグ
 */
const showDialogOr10889 = computed(() => {
  // Or10889のダイアログ開閉状態
  return Or10889Logic.state.get(or10889.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or10889)
 *
 * @param index - 選択した行のindex
 */
function onClickOr10889(index: number) {
  selectedItemIndex.value = index
  // Or10889のダイアログ開閉状態を更新する
  Or10889Logic.state.set({
    uniqueCpId: or10889.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 親画面からの初期値
 */
const or10889Data: Or10889OnewayType = {
  staffId: '1',
}

/**
 * Or09995ダイアログ表示フラグ
 */
const showDialogOr09995 = computed(() => {
  // Or09995のダイアログ開閉状態
  return Or09995Logic.state.get(or09995.value.uniqueCpId)?.isOpen ?? false
})

watch(or09995Type, (newValue) => {
  if (newValue) {
    refValue.value!.tableDataComponent.componentList = []
    for (const item of newValue) {
      const matched = MedicineNameItemsList.value.find((x) => x.title === item.n1A)
      refValue.value!.tableDataComponent.componentList.push({
        n1A: {
          modelValue: {
            value: matched?.value,
          } as Mo01384Type,
          mo01384Oneway: {
            items: MedicineNameItemsList.value,
            linkLabel: '（　※薬剤マスタに追加　）',
          } as Mo01384OnewayType,
        },
        n1B: {
          modelValue: {
            value: item.n1B?.value.toString(),
          } as Mo01278Type,
        },
        n1CKnj: {
          modelValue: {
            value: item.n1CKnj.value,
          } as Mo01274Type,
        },
        n1D: {
          modelValue: {
            modelValue: item.n1D.value,
          } as Mo01282Type,
          mo01282Oneway: {
            items: RouteItemsList.value,
            itemTitle: 'label',
            itemValue: 'value',
          } as Mo01282OnewayType,
        },
        n1E: {
          modelValue: {
            value: item.n1E?.value.toString(),
          } as Mo01278Type,
        },
        n1F: {
          modelValue: {
            modelValue: item.n1F.value,
          } as Mo01282Type,
          mo01282Oneway: {
            items: AsNeededItemsList.value,
            itemTitle: 'label',
            itemValue: 'value',
          } as Mo01282OnewayType,
        },
      })
    }
  }
})

/**
 *  ボタン押下時の処理(Or09995)
 *
 */
function onClickOr09995() {
  or09995Data.sortList = []
  for (const item of refValue.value!.tableDataComponent.componentList) {
    or09995Data.sortList.push({
      n1A: MedicineNameItemsList.value[Number(item.n1A.modelValue.modelValue)].title,
      n1B: Number((item.n1B.modelValue as Mo01278Type).value),
      n1CKnj: item.n1CKnj.modelValue.value,
      n1D: Number(item.n1D.modelValue.modelValue),
      n1E: Number((item.n1E.modelValue as Mo01278Type).value),
      n1F: Number(item.n1F.modelValue.modelValue),
    })
  }
  // Or09995のダイアログ開閉状態を更新する
  Or09995Logic.state.set({
    uniqueCpId: or09995.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * メッセージ41717用ダイアログref
 */
const msgDialog41717 = ref<OrCpGroupDefinitionInputFormDeleteDialogType>({
  mo00024: {
    isOpen: false,
  } as Mo00024Type,
  emitType: 'clickYes',
})

/**************************************************
 * 変数定義
 **************************************************/

const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**
 * デフォルトの単方向バインド値
 */
const defaultOnewayModelValue: Or32004OnewayType = {
  // 事業者ID
  svJigyoId: 0,
  // 利用者ID
  userId: '',
  // アセスメント種別
  assessmentType: t('label.survey-assessment-kind-home-version'),
}

/**
 * Or10412のref
 */
const or10412 = ref({ uniqueCpId: Or10412Const.CP_ID(0) })

/**
 * N1セクションの単方向バインドモデル値
 */
const N1OneWayModelValue = ref([
  {
    leftContentLabel: t(
      'label.interRAI-method-care-assessment-table-N-basic-info-section-N1-title'
    ),
    memoInputvalue: {
      value: { content: '', fontSize: '14', fontColor: '#FFFFFF' } as Or30981Type,
    },
    memoInputSetting: { class: 'N1TextArea' },
    rightContentSub1: t(
      'label.interRAI-method-care-assessment-table-N-basic-info-section-N1-ContentSub1'
    ),
    rightContentSub2: t(
      'label.interRAI-method-care-assessment-table-N-basic-info-section-N1-ContentSub2'
    ),
  },
])

// const memoInputvalueN1 = ref<Or30981Type>({
//   content: '',
//   fontSize: '12',
//   fontColor: '#000000',
// } as Or30981Type)
// const memoInputvalueN2 = ref<Or30981Type>({
//   content: '',
//   fontSize: '12',
//   fontColor: '#000000',
// } as Or30981Type)
// const memoInputvalueN3 = ref<Or30981Type>({
//   content: '',
//   fontSize: '12',
//   fontColor: '#000000',
// } as Or30981Type)
/**
 * N2セクションの単方向バインドモデル値
 */
const N2OneWayModelValue = ref([
  {
    leftContentLabel: t(
      'label.interRAI-method-care-assessment-table-N-basic-info-section-N2-title'
    ),
    memoInputvalue: { value: { content: '', fontSize: '14', fontColor: '#FFFFFF' } as Or30981Type },
    memoInputSetting: { class: 'N2TextArea' },
    buttonGroupList: [
      {
        btnLabel: t('label.interRAI-method-care-assessment-table-N-basic-info-section-N2-btn'),
        btnValue: '0',
      },
      {
        btnLabel: t('label.interRAI-method-care-assessment-table-N-basic-info-section-N2-btn1'),
        btnValue: '1',
      },
    ],
  },
])

/**
 * N3セクションの単方向バインドモデル値
 */
const N3OneWayModelValue = ref([
  {
    leftContentLabel: t(
      'label.interRAI-method-care-assessment-table-N-basic-info-section-N3-title'
    ),
    memoInputvalue: { value: { content: '', fontSize: '14', fontColor: '#FFFFFF' } as Or30981Type },
    memoInputSetting: { class: 'N3TextArea' },
    buttonGroupList: [
      {
        btnLabel: t('label.interRAI-method-care-assessment-table-N-basic-info-section-N3-btn'),
        btnValue: '0',
      },
      {
        btnLabel: t('label.interRAI-method-care-assessment-table-N-basic-info-section-N3-btn1'),
        btnValue: '1',
      },
      {
        btnLabel: t('label.interRAI-method-care-assessment-table-N-basic-info-section-N3-btn2'),
        btnValue: '2',
      },
      {
        btnLabel: t('label.interRAI-method-care-assessment-table-N-basic-info-section-N3-btn8'),
        btnValue: '8',
      },
    ],
  },
])
/**
 * N2.薬のアレルギー用テキストフィールド
 */
const mo00038TypeN2TextField = ref<Mo00038Type>({
  mo00045: {
    value: '',
  } as Mo00045Type,
})
/**
 * N3.処方薬の順守用テキストフィールド
 */
const mo00038TypeN3TextField = ref<Mo00038Type>({
  mo00045: {
    value: '',
  } as Mo00045Type,
})

const defaultMemo = {
  content: '',
  fontSize: '12',
  fontColor: '#000000',
}

/**
 * Or10412の型ref
 */
const or10412Type = ref<Or10412Type>({
  selectItemNo: '',
  userId: '',
  fontColor: '',
  historyTable: '',
  historyTableColum: '',
  meMoContent: '',
  textSize: '',
  flag: '',
})

/**
 * ローカル単方向バインド値
 */
const localOneway = reactive({
  Or32004: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  or10362Oneway: {
    width: '600px',
  },
  // 気分と行動タイトル
  emotionsAndActionsTitle: {
    /** 値のフォントの太さ */
    valueFontWeight: 'bold',
    /** 値 */
    value: t('N．疾患'),
    /** カスタムクラス */
    customClass: new CustomClass({
      outerClass: 'background-color text-right',
      labelClass: '',
      itemClass: 'contentTitle',
    }),
  },
  // メモ入力イコンボタン
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  //メモ入力イコンボタンtoolTip
  btnTooltip: {
    memoInputIconBtn: t('tooltip.assessment-home-6-1-servey-ledger-import'),
    auxiliaryInputDialogBtn: t('tooltip.assessment-home-6-1-servey-ledger-import'),
  },
  btnDisplayTooltip: {
    memoInputIconBtn: t('tooltip.care-plan2-display-order-icon-btn'),
    auxiliaryInputDialogBtn: t('tooltip.care-plan2-display-order-icon-btn'),
  },
  btnAddItemTooltip: {
    memoInputIconBtn: t('tooltip.add-row'),
    auxiliaryInputDialogBtn: t('tooltip.add-row'),
  },
  btnAddRowTooltip: {
    memoInputIconBtn: t('tooltip.insert-row'),
    auxiliaryInputDialogBtn: t('tooltip.insert-row'),
  },
  btnDuplicateRowTooltip: {
    memoInputIconBtn: t('tooltip.duplicate-row'),
    auxiliaryInputDialogBtn: t('tooltip.duplicate-row'),
  },
  btnDeleteRowTooltip: {
    memoInputIconBtn: t('tooltip.delete-row'),
    auxiliaryInputDialogBtn: t('tooltip.delete-row'),
  },
  // メモ入力内容
  mo00046MemoInputOnewayType: {
    name: 'memoInput',
    isVerticalLabel: true,
    showItemLabel: false,
    disabled: false,
    hideDetails: true,
    noResize: true,
    rows: 2,
    maxRows: '2',
    maxlength: '4000',
  } as Mo00046OnewayType,
  // メモ入力内容
  mo00046N3MemoInputwayType: {
    name: 'memoInput',
    isVerticalLabel: true,
    showItemLabel: false,
    disabled: false,
    hideDetails: true,
    noResize: true,
    rows: 3,
    maxRows: '3',
    maxlength: '4000',
  } as Mo00046OnewayType,
  mo00611BtnN2Oneway: {
    width: 'auto',
    class: 'button-style',
  } as Mo00611OnewayType,
  mo00611BtnN3Oneway: {
    width: '450px',
    class: 'button-style',
  } as Mo00611OnewayType,
  mo00611BtnN31Oneway: {
    width: '220px',
    class: 'button-style',
  } as Mo00611OnewayType,
  // 補助入力イコンボタン
  auxiliaryInputDialogBtn: {
    btnIcon: 'comment',
    name: 'auxiliaryInput',
    density: 'compact',
  } as Mo00009OnewayType,
  mo00045OOnewayType: {
    showItemLabel: false,
    // type: 'number',
    // hideDetails: false,
  } as Mo00045OnewayType,
  //  行追加ボタン
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    width: '90px',
  } as Mo00611OnewayType,
  //行挿入ボタン
  mo00611OnewayAddRow: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    width: '90px',
  } as Mo00611OnewayType,
  // 行複写ボタン
  mo00611OnewayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    width: '90px',
  } as Mo00611OnewayType,
  // 行削除ボタン
  mo01265Oneway: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    width: '90px',
  } as Mo01265OnewayType,
  // N3.処方薬の順守
  mo00038OnewayTypeTextField: {
    showItemLabel: false,
    isVerticalLabel: false,
    mo00045Oneway: {
      width: '60px',
      isVerticalLabel: false,
      showItemLabel: false,
      maxLength: '1',
    } as Mo00045OnewayType,
  } as Mo00038OnewayType,
  //単位コメントラベル
  mo01338OnewayTypeLabel: {
    value: t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-label1'),
    valueFontWeight: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentItem',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338TwowayTypeLabel: {
    value: '',
    unit: '/日',
  } as Mo01338OnewayType,
  or10412Oneway: {
    // 選択項目番号
    selectItemNo: '',
    // 利用者ID
    userId: '',
    // 文字色
    fontColor: '',
    // 過去履歴用テーブル名
    historyTable: '-',
    // 過去履歴用カラム名
    historyTableColum: '-',
    // メモ内容
    meMoContent: '',
    // 文字サイズ
    textSize: '',
    // フラグ
    flag: '-',
  } as Or10412OnewayType,
  mo00024DeleteRowDialogOneway: {
    name: '',
    width: '300px',
    message: t('message.i-cmn-11275'),
    mo01344Oneway: {
      showToolbar: false,
      cardTitle: t('label.confirm'),
      showCardActions: true,
      name: '',
      toolbarName: '',
    },
  } as Mo00024OnewayType,
  // メッセージ「e-cmn-41717」
  msgDialogOneway41717: {
    msg: t('message.e-cmn-41717'),
    btnNoDisplay: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '360px',
    } as Mo00024OnewayType,
  } as OrCpGroupDefinitionInputFormDeleteDialogOnewayType,
  mo00039Oneway: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    inline: false,
    checkOff: true,
  } as Mo00039OnewayType,
  or30981OnewayType: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '4000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * モーダルでカラーピッカー
     */
    mo01355Oneway: {} as Mo01355OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      colorIndicatorColor: '',
      iconAColor: '',
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  /**
   * GUI00937_入力支援［ケアマネ］
   */
  or51775Oneway: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
})

/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()

/**
 * AC024_各「メモ入力支援アイコンボタン」押下_GUI00787 ［メモ入力］画面-戻り値変更
 *
 * @param respData - 戻り値
 */
watch(
  () => or10412Type.value,
  (newValue) => {
    switch (newValue.selectItemNo) {
      case 'N1':
        {
          const lastElement = document.querySelector('.N1TextArea')
          if (lastElement) {
            const html = lastElement.querySelectorAll('textarea')
            if (html) {
              for (const item of html) {
                if (item.value) {
                  item.style.color = newValue.fontColor
                  item.style.fontSize = newValue.textSize! + 'px'
                }
              }
            }
          }
        }
        N1OneWayModelValue.value[0].memoInputvalue.value.content = newValue.meMoContent
        break
      case 'N2':
        {
          const lastElement = document.querySelector('.N2TextArea')
          if (lastElement) {
            const html = lastElement.querySelectorAll('textarea')
            if (html) {
              for (const item of html) {
                if (item.value) {
                  item.style.color = newValue.fontColor
                  item.style.fontSize = newValue.textSize! + 'px'
                }
              }
            }
          }
        }
        N2OneWayModelValue.value[0].memoInputvalue.value.content = newValue.meMoContent
        break
      case 'N3':
        {
          const lastElement = document.querySelector('.N3TextArea')
          if (lastElement) {
            const html = lastElement.querySelectorAll('textarea')
            if (html) {
              for (const item of html) {
                if (item.value) {
                  item.style.color = newValue.fontColor
                  item.style.fontSize = newValue.textSize! + 'px'
                }
              }
            }
          }
        }
        N3OneWayModelValue.value[0].memoInputvalue.value.content = newValue.meMoContent
        break
      default:
        break
    }
  }
)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or10412Const.CP_ID(1)]: or10412.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * GUI00787 ［メモ入力］画面表示フラグ
 */
const showDialogOr10412 = computed(() => {
  // Or10412のダイアログ開閉状態
  return Or10412Logic.state.get(or10412.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * 行追加
 */
function onAddItem() {
  /**
   * テーブルデフォルトデータ
   */
  const itemDataDefault = {
    n1A: {
      modelValue: {
        value: '',
      } as Mo01384Type,
      mo01384Oneway: {
        items: MedicineNameItemsList.value,
        linkLabel: '（　※薬剤マスタに追加　）',
      } as Mo01384OnewayType,
    },
    n1B: {
      modelValue: {
        value: '',
      } as Mo01278Type,
    },
    n1CKnj: {
      modelValue: {
        value: '',
      } as Mo01274Type,
    },
    n1D: {
      modelValue: {
        modelValue: '',
      } as Mo01282Type,
      mo01282Oneway: {
        items: RouteItemsList.value,
        itemTitle: 'label',
        itemValue: 'value',
      } as Mo01282OnewayType,
    },
    n1E: {
      modelValue: {
        value: '',
      } as Mo01278Type,
    },
    n1F: {
      modelValue: {
        modelValue: '',
      } as Mo01282Type,
      mo01282Oneway: {
        items: AsNeededItemsList.value,
        itemTitle: 'label',
        itemValue: 'value',
      } as Mo01282OnewayType,
    },
  }
  refValue.value!.tableDataComponent.componentList?.push(itemDataDefault)

  selectRow(refValue.value!.tableDataComponent.componentList.length - 1)
}

/**
 * 行挿入
 */
function onAddRow() {
  if (
    refValue.value!.tableDataComponent.componentList === undefined ||
    refValue.value!.tableDataComponent.componentList.length === 0
  )
    return

  /**
   * テーブルデフォルトデータ
   */
  const itemDataDefault = {
    n1A: {
      modelValue: {
        value: '',
      } as Mo01384Type,
      mo01384Oneway: {
        items: MedicineNameItemsList.value,
        linkLabel: '（　※薬剤マスタに追加　）',
      } as Mo01384OnewayType,
    },
    n1B: {
      modelValue: {
        value: '',
      } as Mo01278Type,
    },
    n1CKnj: {
      modelValue: {
        value: '',
      } as Mo01274Type,
    },
    n1D: {
      modelValue: {
        modelValue: '',
      } as Mo01282Type,
      mo01282Oneway: {
        items: RouteItemsList.value,
        itemTitle: 'label',
        itemValue: 'value',
      } as Mo01282OnewayType,
    },
    n1E: {
      modelValue: {
        value: '',
      } as Mo01278Type,
    },
    n1F: {
      modelValue: {
        modelValue: '',
      } as Mo01282Type,
      mo01282Oneway: {
        items: AsNeededItemsList.value,
        itemTitle: 'label',
        itemValue: 'value',
      } as Mo01282OnewayType,
    },
  }
  refValue.value!.tableDataComponent.componentList.splice(
    selectedItemIndex.value,
    0,
    itemDataDefault
  )
  selectRow(selectedItemIndex.value)
}

/**
 * 行複写
 */
function onCloneItem() {
  if (
    refValue.value!.tableDataComponent.componentList === undefined ||
    refValue.value!.tableDataComponent.componentList.length === 0
  )
    return
  refValue.value!.tableDataComponent.componentList.splice(selectedItemIndex.value, 0, {
    ...refValue.value!.tableDataComponent.componentList[selectedItemIndex.value],
  })
  selectRow(selectedItemIndex.value + 1)
}

/**
 *行削除
 */
watch(
  () => orConfirmDeleteRowDialog.value.emitType,
  () => {
    if (orConfirmDeleteRowDialog.value.emitType === 'ok') {
      if (selectedItemIndex.value !== null) {
        refValue.value!.tableDataComponent.componentList.splice(selectedItemIndex.value, 1)
        if (selectedItemIndex.value > refValue.value!.tableDataComponent.componentList.length - 1)
          selectRow(selectedItemIndex.value - 1)
      }
      orConfirmDeleteRowDialog.value.mo00024.isOpen = false
    }
  }
)

watch(
  () => msgDialog41717.value.emitType,
  (newValue) => {
    if (newValue === 'clickYes') {
      msgDialog41717.value.mo00024.isOpen = false
      const valueN2 = parseInt(mo00038TypeN2TextField.value.mo00045.value)
      const valueN3 = parseInt(mo00038TypeN3TextField.value.mo00045.value)
      if (valueN3 < 0 || (valueN3 > 2 && valueN3 !== 8)) {
        mo00038TypeN3TextField.value.mo00045.value = '8'
      }
      if (valueN2 < 0 || valueN2 > 1) {
        mo00038TypeN2TextField.value.mo00045.value = '1'
      }
    }
  }
)

/**
 * 行削除
 */
function onDelete() {
  if (
    refValue.value!.tableDataComponent.componentList === undefined ||
    refValue.value!.tableDataComponent.componentList.length === 0
  )
    return
  if (selectedItemIndex.value !== -1) {
    orConfirmDeleteRowDialog.value.mo00024.isOpen = true
  }
}

/**
 * AC032_各「アセスメント項目区分」値変更
 */
watch(
  () => mo00038TypeN3TextField.value.mo00045.value,
  (newValue) => {
    // メッセージ「41717」を表示
    if (newValue) {
      const inputVal = parseInt(newValue)
      if (inputVal < 0 || (inputVal > 2 && inputVal !== 8)) {
        msgDialog41717.value.mo00024.isOpen = true

        watch(
          () => msgDialog41717.value.mo00024.isOpen,
          () => {
            if (!msgDialog41717.value.mo00024.isOpen) {
              // 入力値を元の値（変更前の値）に戻す
              mo00038TypeN3TextField.value.mo00045.value = previousAssessmentValues.N3
            }
            stop()
          },
          { immediate: true }
        )
      }
    }
  }
)

/**
 * AC032_各「アセスメント項目区分」値変更
 */
watch(
  () => mo00038TypeN2TextField.value.mo00045.value,
  (newValue) => {
    // メッセージ「41717」を表示
    if (newValue) {
      const inputVal = parseInt(newValue)
      if (inputVal < 0 || inputVal > 1) {
        msgDialog41717.value.mo00024.isOpen = true

        watch(
          () => msgDialog41717.value.mo00024.isOpen,
          () => {
            if (!msgDialog41717.value.mo00024.isOpen) {
              // 入力値を元の値（変更前の値）に戻す
              mo00038TypeN2TextField.value.mo00045.value = previousAssessmentValues.N2
            }
            stop()
          },
          { immediate: true }
        )
      }
    }
  }
)

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    if ('0' === data.type) {
      switch (local.vieTypeKbn) {
        case 'N1':
          refValue.value!.memoInputvalueN1.content =
            refValue.value!.memoInputvalueN1.content + data.value
          break
        case 'N2':
          refValue.value!.memoInputvalueN2.content =
            refValue.value!.memoInputvalueN2.content + data.value
          break
        case 'N3':
          refValue.value!.memoInputvalueN3.content =
            refValue.value!.memoInputvalueN3.content + data.value
          break
      }
    } else if ('1' === data.type) {
      switch (local.vieTypeKbn) {
        case 'N1':
          refValue.value!.memoInputvalueN1.content = data.value
          break
        case 'N2':
          refValue.value!.memoInputvalueN2.content = data.value
          break
        case 'N3':
          refValue.value!.memoInputvalueN3.content = data.value
          break
      }
    }
  }
}

/**
 * 前回のN2およびN3アセスメント値を保持するリアクティブオブジェクト
 */
const previousAssessmentValues = reactive({
  N2: '', // N2セクションの前回のアセスメント値
  N3: '', // N3セクションの前回のアセスメント値
})
/**
 * 「GUI02357_薬剤マスタ 新規登録」画面をポップアップで起動する。
 *
 */
// function onClickGUI02357() {
//   //Todo:「GUI02357_薬剤マスタ 新規登録」画面をポップアップで起動する。
// }

/**
 *  画面初期情報取得
 *
 * @returns 画面初期情報
 */
const getHistoryInfo = async () => {
  const inputData: AssessmentInterRAIAHistorySelectEntity = {
    raiId: local.historyInfo.raiId,
  }
  const resData: AssessmentInterRAIAHistorySelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIAHistorySelect',
    inputData
  )
  if (resData.statusCode === 'success') {
    return resData.data
  }
}

/**
 * 初期化処理
 */
async function init() {
  inputFormDisplayFlag.value = true
  // mode.value = props.onewayModelValue.surveyAssessmentKind ?? ''
  MedicineNameItemsList.value = []
  AsNeededItemsList.value = []
  RouteItemsList.value = []
  refValue.value!.tableDataComponent.componentList = []
  tableData.value.yaKuzaiList = []
  headers.value = [
    {
      title: t('label.medicine-name-a'),
      key: 'medicine_name',
      minWidth: '320px',
      width: '320px',
      sortable: false,
    },
    {
      title: t('label.one-day-volume-b'),
      key: 'one_day_volume',
      minWidth: '100px',
      width: '100px',
      sortable: false,
    },
    {
      title: t('label.unit-c'),
      key: 'unit',
      minWidth: '100px',
      width: '100px',
      sortable: false,
    },
    {
      title: t('label.route-d'),
      key: 'route',
      minWidth: '180px',
      width: '180px',
      sortable: false,
    },
    {
      title: t('label.number-of-times-e'),
      key: 'number_of_times',
      minWidth: '100px',
      width: '100px',
      sortable: false,
    },
    {
      title: t('label.as-needed-f'),
      key: 'as_needed',
      minWidth: '120px',
      width: '120px',
      sortable: false,
    },
  ]

  const inputData: AssessmentInterRAINInEntity = {
    raiId: local.historyInfo.raiId,
  }
  const resData: AssessmentInterRAINOutEntity = await ScreenRepository.select(
    'assessmentInterRAINSelect',
    inputData
  )

  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 経路
    {
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ROUTE,
      addBlank: true,
      targetDate: systemCommonsStore.getSystemDate!,
    },
    // 頓用
    {
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASNEEDED,
      addBlank: true,
      targetDate: systemCommonsStore.getSystemDate!,
    },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // 経路
  RouteItemsList.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ROUTE)
  // 頓用
  AsNeededItemsList.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ASNEEDED)

  if (MedicineNameItemsList.value.length === 0) {
    for (const item of resData.data.MedicineNameItems) {
      MedicineNameItemsList.value.push({
        title: item.label,
        value: item.value ?? '',
      })
    }
  }

  const assessmentInterRAINResp: AssessmentInterRAINOutEntity = await ScreenRepository.select(
    'assessmentInterRAINInitSelect',
    inputData
  )
  if (tableData.value.yaKuzaiList.length === 0) {
    tableData.value.yaKuzaiList = assessmentInterRAINResp.data.yaKuzaiList
    if (md.value === 'add') {
      tableData.value.yaKuzaiList = []
      for (let i = 0; i < 5; i++) {
        tableData.value.yaKuzaiList.push({
          id: '',
          n1A: '',
          n1B: '',
          n1CKnj: '',
          n1D: '',
          n1E: '',
          n1F: '',
        })
      }
    }
    if (tableData.value.yaKuzaiList) {
      for (const item of tableData.value.yaKuzaiList) {
        const itemData = {
          n1A: {
            modelValue: {
              value: item.n1A,
            } as Mo01384Type,
            mo01384Oneway: {
              items: MedicineNameItemsList.value,
              linkLabel: '（　※薬剤マスタに追加　）',
            } as Mo01384OnewayType,
          },
          n1B: {
            modelValue: {
              value: item.n1B,
            } as Mo01278Type,
          },
          n1CKnj: {
            modelValue: {
              value: item.n1CKnj,
            } as Mo01274Type,
          },
          n1D: {
            modelValue: {
              modelValue: item.n1D,
            } as Mo01282Type,
            mo01282Oneway: {
              items: RouteItemsList.value,
              itemTitle: 'label',
              itemValue: 'value',
            } as Mo01282OnewayType,
          },
          n1E: {
            modelValue: {
              value: item.n1E,
            } as Mo01278Type,
          },
          n1F: {
            modelValue: {
              modelValue: item.n1F,
            } as Mo01282Type,
            mo01282Oneway: {
              items: AsNeededItemsList.value,
              itemTitle: 'label',
              itemValue: 'value',
            } as Mo01282OnewayType,
          },
        }
        refValue.value!.tableDataComponent.componentList.push(itemData)
      }
    }
  }

  refValue.value!.memoInputvalueN1 =
    md.value === 'add'
      ? defaultMemo
      : {
          content: assessmentInterRAINResp.data.subInfoN.n1MemoKnj,
          fontSize: assessmentInterRAINResp.data.subInfoN.n1MemoFont,
          fontColor: assessmentInterRAINResp.data.subInfoN.n1MemoColor,
        }
  refValue.value!.memoInputvalueN2 =
    md.value === 'add'
      ? defaultMemo
      : {
          content: assessmentInterRAINResp.data.subInfoN.n2MemoKnj,
          fontSize: assessmentInterRAINResp.data.subInfoN.n2MemoFont,
          fontColor: assessmentInterRAINResp.data.subInfoN.n2MemoColor,
        }
  refValue.value!.memoInputvalueN3 =
    md.value === 'add'
      ? defaultMemo
      : {
          content: assessmentInterRAINResp.data.subInfoN.n3MemoKnj,
          fontSize: assessmentInterRAINResp.data.subInfoN.n3MemoFont,
          fontColor: assessmentInterRAINResp.data.subInfoN.n3MemoColor,
        }

  previousAssessmentValues.N2 = assessmentInterRAINResp.data.subInfoN.n2
  previousAssessmentValues.N3 = assessmentInterRAINResp.data.subInfoN.n3

  mo00038TypeN2TextField.value.mo00045.value =
    md.value === 'add' ? '' : assessmentInterRAINResp.data.subInfoN.n2
  mo00038TypeN3TextField.value.mo00045.value =
    md.value === 'add' ? '' : assessmentInterRAINResp.data.subInfoN.n3

  md.value = ''
}

/**
 * AC027_「本人のケアの目標入力支援アイコンボタン」押下
 *
 * @param type - 画面項目区分
 */
const careTargetInputSupportIconClick = (type: 'N1' | 'N2' | 'N3') => {
  local.vieTypeKbn = type
  localOneway.or51775Oneway = {
    // タイトル
    title: t('label.memo'),
    // 画面ID
    screenId: 'GUI00778',
    // 分類ID
    bunruiId: '',
    // 大分類ＣＤ
    t1Cd: '610',
    // 中分類CD
    t2Cd: '19',
    // 小分類ＣＤ
    t3Cd: '1',
    // テーブル名
    tableName: 'cpn_tuc_rai_ass_n',
    // カラム名
    columnName: '',
    // アセスメント方式
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? '',
    // 文章内容
    inputContents: '',
    // 利用者ID
    userId: systemCommonsStore.getUserId,
    // モード
    mode: '',
  } as Or51775OnewayType
  switch (type) {
    // A4.婚姻状況の場合
    case 'N1':
      localOneway.or51775Oneway.columnName = 'n1_memo_knj'
      break
    // A7.要介護度の場合
    case 'N2':
      localOneway.or51775Oneway.columnName = 'n2_memo_knj'
      break
    // A8.アセスメントの理由の場合
    case 'N3':
      localOneway.or51775Oneway.columnName = 'n3_memo_knj'
      break
    default:
      break
  }
  // GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}

onMounted(async () => {
  if (isInit) {
    await init()
  }
})
const n1Note = computed(() => [
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-a'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-b'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-c'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-d'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-d1'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-d2'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-d3'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-d3-2'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-d4'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-e'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-f'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-f0'),
  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-popup-label-f1'),
])
</script>

<template>
  <c-v-sheet class="container">
    <c-v-row
      v-show="inputFormDisplayFlag"
      class="pt-4"
    >
      <div>
        <div class="sticky background-color">
          <!-- アセスメント種別タイトル -->
          <c-v-row
            style="width: 1080px"
            class="heading-text-m justify-xl-end title-text-size pb-1"
          >
            <c-v-spacer />
            <div style="margin-right: 16px">{{ localOneway.Or32004.assessmentType }}</div>
          </c-v-row>
          <c-v-row
            style="width: 1080px"
            class="bg-white title-main"
            >{{ localOneway.emotionsAndActionsTitle.value }}</c-v-row
          >
        </div>
        <div class="n-container">
          <c-v-row class="sub-title">{{ N1OneWayModelValue[0].leftContentLabel }}</c-v-row>
          <!-- N1.全使用薬剤のリスト-->
          <div class="n1-container">
            <div class="n1-title">
              {{ N1OneWayModelValue[0].rightContentSub1 }}
            </div>
            <div class="n1-note-container card-text">
              {{
                t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-ContentSub2')
              }}
              <div
                v-for="(item, index) in n1Note"
                :key="index"
                :class="['pl-4']"
                :style="{ paddingLeft: index === 7 ? '10px !important' : '' }"
              >
                {{ item }}
              </div>
            </div>
            <c-v-row
              class="justify-space-between align-center mt-4 text-center"
              style="width: 920px"
            >
              <c-v-col
                cols="9"
                class="text-left pl-0"
              >
                <base-mo00611
                  :oneway-model-value="localOneway.mo00611OnewayAdd"
                  class="mx-2 ml-0 pl-0"
                  @click="onAddItem"
                >
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="localOneway.btnAddItemTooltip.memoInputIconBtn"
                  />
                </base-mo00611>
                <base-mo00611
                  :oneway-model-value="localOneway.mo00611OnewayAddRow"
                  class="mx-2"
                  @click="onAddRow"
                >
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="localOneway.btnAddRowTooltip.memoInputIconBtn"
                  />
                </base-mo00611>
                <base-mo00611
                  :oneway-model-value="localOneway.mo00611OnewayCopy"
                  class="mx-2"
                  @click="onCloneItem"
                >
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="localOneway.btnDuplicateRowTooltip.memoInputIconBtn"
                  />
                </base-mo00611>
                <base-mo01265
                  :oneway-model-value="localOneway.mo01265Oneway"
                  class="mx-2"
                  @click="onDelete"
                >
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="localOneway.btnDeleteRowTooltip.memoInputIconBtn"
                  />
                </base-mo01265>
              </c-v-col>
              <c-v-col
                cols="3"
                class="text-right pr-0"
              >
                <base-mo00611
                  :oneway-model-value="{
                    btnLabel: t(
                      'label.interRAI-method-care-assessment-table-N-basic-info-section-N1-label2'
                    ),
                    width: '65px',
                    minWidth: '65px',
                  }"
                  @click="onClickOr09995"
                >
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="localOneway.btnDisplayTooltip.memoInputIconBtn"
                  />
                </base-mo00611>
              </c-v-col>
            </c-v-row>
            <c-v-row class="mb-2">
              <c-v-col class="text-left pa-0">
                <c-v-data-table
                  hide-default-footer
                  v-resizable-grid="{ columnWidths: columnMinWidth }"
                  :items="refValue!.tableDataComponent.componentList"
                  class="mt-2 table-wrapper"
                  :items-per-page="-1"
                  hover
                  fixed-header
                  :headers="headers"
                >
                  <template #item="{ item, index }">
                    <tr
                      :class="{ 'select-row': selectedItemIndex === index }"
                      @click="selectRow(index)"
                    >
                      <td class="select-table-cell special thead-with-iconbtn">
                        <base-mo00009
                          :oneway-model-value="localOneway.memoInputIconBtn"
                          @click="onClickOr10889(index)"
                        ></base-mo00009>
                        <base-mo01384
                          v-model="item.n1A.modelValue"
                          :oneway-model-value="item.n1A.mo01384Oneway"
                          style="width: 100%"
                          class="cus_drop"
                        ></base-mo01384>
                      </td>
                      <td class="input-table-cell">
                        <base-mo01278
                          v-model="item.n1B.modelValue"
                          :maxlength="5"
                        ></base-mo01278>
                      </td>
                      <td class="input-table-cell">
                        <base-mo01274
                          v-model="item.n1CKnj.modelValue"
                          :maxlength="10"
                        ></base-mo01274>
                      </td>
                      <td
                        class="select-table-cell cus_drop"
                        style="text-align: right; padding: 0px"
                      >
                        <base-mo01282
                          v-model="item.n1D.modelValue"
                          :oneway-model-value="item.n1D.mo01282Oneway"
                        ></base-mo01282>
                      </td>
                      <td class="pr-0 input-table-cell">
                        <div class="d-flex align-center justify-end h-100">
                          <base-mo01278
                            v-model="item.n1E.modelValue"
                            :class="[item.n1F.modelValue.modelValue === '0' ? 'mr-1' : '']"
                            :maxlength="2"
                            :style="{
                              width:
                                item.n1F.modelValue.modelValue === '0'
                                  ? 'calc(100% - 23.5px) !important'
                                  : '100%',
                            }"
                          />

                          <span v-if="item.n1F.modelValue.modelValue === '0'">
                            {{ localOneway.mo01338TwowayTypeLabel.unit }}
                          </span>
                        </div>
                      </td>
                      <td
                        class="select-table-cell cus_drop"
                        style="text-align: right; padding: 0px"
                      >
                        <base-mo01282
                          v-model="item.n1F.modelValue"
                          :oneway-model-value="item.n1F.mo01282Oneway"
                        ></base-mo01282>
                      </td>
                    </tr>
                  </template>
                  <template #bottom />
                </c-v-data-table>
                <c-v-row class="justify-end mt-1">{{
                  t('label.interRAI-method-care-assessment-table-N-basic-info-section-N1-label1')
                }}</c-v-row>
              </c-v-col>
            </c-v-row>
            <g-custom-or-30981
              v-model="refValue!.memoInputvalueN1"
              :oneway-model-value="localOneway.or30981OnewayType"
              class="mt-3"
              @on-click-edit-btn="careTargetInputSupportIconClick('N1')"
            />
            <!-- <base-mo00046
                v-model="N1OneWayModelValue.value[0].memoInputvalue"
                :oneway-model-value="{
                  ...localOneway.mo00046MemoInputOnewayType,
                  ...N1OneWayModelValue.value[0]?.memoInputSetting,
                }"
                v-bind="{ ...$attrs }"
              /> -->
          </div>
          <c-v-row class="sub-title">{{ N2OneWayModelValue[0].leftContentLabel }}</c-v-row>
          <div class="n1-container">
            <base-mo00039
              v-model="mo00038TypeN2TextField.mo00045.value"
              :oneway-model-value="localOneway.mo00039Oneway"
              :class="['custom-radio']"
            >
              <base-at-radio
                v-for="radio in N2OneWayModelValue[0].buttonGroupList"
                :key="radio.btnValue"
                :radio-label="radio.btnLabel"
                :value="radio.btnValue"
                :class="{
                  isChecked: mo00038TypeN2TextField.mo00045.value === radio.btnValue,
                }"
              />
            </base-mo00039>
            <g-custom-or-30981
              v-model="refValue!.memoInputvalueN2"
              :oneway-model-value="localOneway.or30981OnewayType"
              class="mt-3"
              @on-click-edit-btn="careTargetInputSupportIconClick('N2')"
            />
            <!-- <base-mo00046
                v-model="N2OneWayModelValue.value[0].memoInputvalue"
                :oneway-model-value="{
                  ...localOneway.mo00046MemoInputOnewayType,
                  ...N2OneWayModelValue.value[0]?.memoInputSetting,
                }"
                v-bind="{ ...$attrs }"
              /> -->
          </div>
          <c-v-row class="sub-title">{{ N3OneWayModelValue[0].leftContentLabel }}</c-v-row>
          <div class="n1-container pb-10">
            <base-mo00039
              v-model="mo00038TypeN3TextField.mo00045.value"
              :oneway-model-value="localOneway.mo00039Oneway"
              :class="['custom-radio']"
            >
              <base-at-radio
                v-for="radio in N3OneWayModelValue[0].buttonGroupList"
                :key="radio.btnValue"
                :radio-label="radio.btnLabel"
                :value="radio.btnValue"
                :class="{
                  isChecked: mo00038TypeN3TextField.mo00045.value === radio.btnValue,
                }"
              />
            </base-mo00039>
            <g-custom-or-30981
              v-model="refValue!.memoInputvalueN3"
              :oneway-model-value="localOneway.or30981OnewayType"
              class="mt-3"
              @on-click-edit-btn="careTargetInputSupportIconClick('N3')"
            />
            <!-- <base-mo00046
                v-model="N3OneWayModelValue.value[0].memoInputvalue"
                :oneway-model-value="{
                  ...localOneway.mo00046N3MemoInputwayType,
                  ...N3OneWayModelValue.value[0]?.memoInputSetting,
                }"
                :class="['text-area', 'w-100', { 'bg-gray': mode === '2' || mode === '3' }]"
                v-bind="{ ...$attrs }"
              /> -->
          </div>
          <!-- interRAIロゴ -->
          <c-v-row no-gutters>
            <c-v-col cols="12">
              <c-v-img
                width="128"
                aspect-ratio="16/9"
                cover
                :src="InterRAI"
                style="float: right"
              ></c-v-img>
            </c-v-col>
          </c-v-row>
        </div>
      </div>
    </c-v-row>
    <g-custom-or-10412
      v-if="showDialogOr10412"
      v-bind="or10412"
      v-model="or10412Type"
      :oneway-model-value="localOneway.or10412Oneway"
    ></g-custom-or-10412>
    <g-custom-or-32007
      v-if="showDialogOr32007"
      v-bind="or32007"
    ></g-custom-or-32007>
    <g-custom-or-x-0002
      v-model="orConfirmDeleteRowDialog"
      :oneway-model-value="localOneway.mo00024DeleteRowDialogOneway"
    ></g-custom-or-x-0002>
    <g-custom-or-x-0003
      v-model="msgDialog41717"
      :oneway-model-value="localOneway.msgDialogOneway41717"
      class="icon-container"
    ></g-custom-or-x-0003>
    <g-custom-or-09995
      v-if="showDialogOr09995"
      v-bind="or09995"
      v-model="or09995Type"
      :oneway-model-value="or09995Data"
    />
    <g-custom-or-10889
      v-if="showDialogOr10889"
      v-bind="or10889"
      v-model="or10889Type"
      :oneway-model-value="or10889Data"
    />
    <g-base-or21814 v-bind="or21814" />
    <!-- GUI00937_入力支援［ケアマネ］ -->
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="or51775Type"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="or51775Confirm"
    />
  </c-v-sheet>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';
.container {
  background-color: transparent;
  :has(.contentItem) {
    :deep(.item-label) {
      color: #0000ff;
      font-size: 12px !important;
      font-weight: normal !important;
    }
  }
}
.title-text-size {
  font-size: 24px;
  font-weight: normal !important;
}
.button-style {
  display: flex;
  justify-content: flex-start;
  padding-left: 8px;
}

.divider {
  height: 16px;
}
.bordered {
  border: 1px rgb(var(--v-theme-black-200)) solid;
}
.bordered-left {
  border-left: none;
}
.bordered-red {
  border: 1px rgb(var(--v-theme-red-800)) solid;
}
.bg-white {
  background-color: rgb(var(--v-theme-white));
}
.v-row {
  margin: 0px;
}
.v-col {
  padding: 8px;
}
.p-endTextField {
  padding-right: 24px;
}
.text_color {
  background: transparent;
  color: rgb(var(--v-theme-key)) !important;
}
// 選択した行のCSS
.select-row {
  background: rgb(var(--v-theme-blue-100));
}
:deep(.table-wrapper) {
  .v-table__wrapper th {
    background-color: #dbeefe !important;
    height: 32px;
    font-weight: 400;
    font-size: 13px;
  }
  td {
    &.select-table-cell {
      select {
        &.full-width-field-select {
          padding: 0 8px !important;
        }
      }
      .button-cell {
        position: absolute;
        left: 0;
      }
      &.special {
        position: relative;
      }
    }
  }
}
.bg-gray {
  background-color: rgb(var(--v-theme-black-536));
}
:deep(.gray-mode input) {
  background-color: rgb(var(--v-theme-black-536));
}
:deep(.full-width-field-select) {
  padding: 8px !important;
}
.sticky {
  padding-top: 0px;
  position: sticky;
  top: 0;
  z-index: 999;
}
.title-main {
  height: 73px;
  color: #333333;
  font-weight: 700;
  font-size: 18px;
  display: flex;
  align-items: center;
  padding-left: 24px;
}
.sub-title {
  color: #333333;
  background-color: #e6e6e6;
  font-size: 16px;
  font-weight: 700;
  height: 48px;
  display: flex;
  align-items: center;
  padding-left: 24px;
}
.n1-container {
  background-color: #fff;
  padding: 24px 24px 24px 48px;
  color: #333333;
  .n1-title {
    font-weight: 700;
    font-size: 14px;
  }
  .n1-note-container {
    border: 1px #333333 solid;
    padding: 8px 16px;
    margin-top: 16px;
  }
}
.input-container {
  position: relative;
  .button-square {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 9;
  }
  textarea {
    height: 127px !important;
  }
}
.n-container {
  width: 1080px;
}
:deep(.custom-radio) {
  &.inline {
    .v-radio {
      min-width: 180px;
    }
  }
  .v-selection-control-group {
    gap: 16px;
  }
  .v-radio {
    min-width: 306px;
    padding: 0 8px;
    border: 1px solid #8a8a8a;
    border-radius: 4px;
    &.isChecked {
      background-color: #ecf3fd;
      border: 1px solid #0760e6;
    }
  }
}

.card-text {
  white-space: pre-line;
}

.thead-with-iconbtn {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.fontSizeItem) {
  width: 70px !important;
  max-width: 70px !important;
}

:deep(.v-radio) {
  max-height: 36px;
}

:deep(
  .table-wrapper
    .v-table__wrapper
    td:has(input:not([readonly]):not([disabled]):not([type='checkbox']):not([type='radio']))
    input
) {
  outline: none !important;
}

.cus_drop {
  :deep(.v-field__outline) {
    display: none;
  }
}
</style>
