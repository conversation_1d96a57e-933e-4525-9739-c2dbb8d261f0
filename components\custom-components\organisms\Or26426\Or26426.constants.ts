/**
 * Or26426:静的データ
 * GUI01272_認定調査票特記事項
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
import { getSequencedCpId } from '~/utils/useScreenUtils'

export namespace Or26426Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or26426', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
  }
  /**
   * タブID
   */
  export namespace TAB {
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM1 = 'item1'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM2 = 'item2'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM3 = 'item3'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM4 = 'item4'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM5 = 'item5'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM6 = 'item6'
    /**
     * 認定調査票特記事項画面のタブID
     */
    export const TAB_ID_ITEM7 = 'item7'
  }
}
