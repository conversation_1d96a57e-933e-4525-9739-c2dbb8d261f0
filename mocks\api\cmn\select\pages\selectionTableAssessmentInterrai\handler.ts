/**
 * Or11752:選定表(アセスメント(インターライ))
 * GUI00848: 選定表(アセスメント(インターライ))
 *
 * @description
 *選定表(アセスメント(インターライ))
 *
 * <AUTHOR> DAM XUAN HIEU
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { SelectionTableAssessmentInterraiInEntity } from '~/repositories/cmn/entities/SelectionTableAssessmentInterraiSelectEntity'

export function handler(inEntity: SelectionTableAssessmentInterraiInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: '200',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
