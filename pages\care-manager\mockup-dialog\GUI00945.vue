<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or27274Const } from '~/components/custom-components/organisms/Or27274/Or27274.constants'
import { Or27274Logic } from '~/components/custom-components/organisms/Or27274/Or27274.logic'
import type { Or27274OneWayType } from '~/types/cmn/business/components/Or27274Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00945'
// ルーティング
const routing = 'GUI00945/pinia'
// 画面物理名
const screenName = 'GUI00935'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27274 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00945' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent
// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or10578Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27274.value.uniqueCpId = pageComponent.uniqueCpId
const init = useInitialize({
  cpId: 'GUI00945',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27274Const.CP_ID(0) }],
})
Or27274Logic.initialize(init.childCpIds.Or27274.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27274Const.CP_ID(0)]: or27274.value,
})

const or27274Data: Or27274OneWayType = {
  jigyoId: '21',
  shisetuId: '1',
  svJigyoIdList: ['21', '28', '63', '286'],
}

/**
 *  ボタン押下時の処理(Or27274)
 *
 */
function onClickOr27274() {
  Or27274Logic.state.set({
    uniqueCpId: or27274.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr27274 = computed(() => {
  // Or27274のダイアログ開閉状態
  return Or27274Logic.state.get(or27274.value.uniqueCpId)?.isOpen ?? false
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >モックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27274()"
        >GUI00945_実施計画①マスタ
      </v-btn>
      <g-custom-or-27274
        v-if="showDialogOr27274"
        v-bind="or27274"
        :oneway-model-value="or27274Data"
      />
    </c-v-col>
  </c-v-row>
</template>
