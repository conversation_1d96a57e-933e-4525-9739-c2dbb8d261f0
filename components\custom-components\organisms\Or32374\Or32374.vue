<script setup lang="ts">
/**
 * Or32374:有機体
 * 調査票複写
 *
 * <AUTHOR>
 */
import { reactive, ref, watch, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { Or11207Const } from '../Or11207/Or11207.constants'
import { Or11216Const } from '../Or11216/Or11216.constants'
import { Or11217Const } from '../Or11217/Or11217.constants'
import { Or11218Const } from '../Or11218/Or11218.constants'
import { Or11219Const } from '../Or11219/Or11219.constants'
import { Or11220Const } from '../Or11220/Or11220.constants'
import { Or11275Const } from '../Or11275/Or11275.constants'
import { Or30055Const } from '../Or30055/Or30055.constants'
import { Or30055Logic } from '../Or30055/Or30055.logic'
import type {
  SurSlipDupInitInfo,
  SurSlipDupInitInfo2,
  SurSlipDupInitInfo3,
  JigyoIdInfo,
  NoInfo,
  RirekiList2Info,
  KikanInfo,
  RirekiInfo,
} from '../Or11207/Or11207.type'
import { Or32374Const } from './Or32374.constants'
import type { CodeType, Or32374StateType } from './Or32374.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type {
  Or32374OnewayType,
  OrX32374OnewayType,
} from '~/types/cmn/business/components/Or32374Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { useScreenOneWayBind, useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { OrX0077OnewayType } from '~/types/cmn/business/components/OrX0077Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Or11207OnewayType } from '~/types/cmn/business/components/Or11207Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  GeneralSituationSurveyDuplicateInfoSelectInEntity,
  GeneralSituationSurveyDuplicateInfoSelectOutEntity,
  initHistoryInfoInEntity,
  initHistoryInfoOutEntity,
  initInfoInEntity,
  initInfoOutEntity,
  initPlanningPeriodInEntity,
  initPlanningPeriodOutEntity,
  savePlanningPeriodCopyInEntity,
  savePlanningPeriodCopyOutEntity,
  surveySlipCopyDetailInfoInEntity,
  surveySlipCopyDetailInfoOutEntity,
} from '~/repositories/cmn/entities/GeneralSituationSurveyPlanningPeriodInitEntity'
import { Or32374Logic } from '~/components/custom-components/organisms/Or32374/Or32374.logic'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { AuthzRepository } from '~/repositories/business/core/authz/AuthzRepository'
import type {
  ICheckAuthzKinouInEntity,
  ICheckAuthzKinouOutEntity,
} from '~/repositories/business/core/authz/entities/CheckAuthzKinouEntity'

const { t } = useI18n()
const orx0077 = ref({ uniqueCpId: '' })
const or11207 = ref({ uniqueCpId: '' })
const or11207OnewayType = {} as Or11207OnewayType
const or32374 = ref({ uniqueCpId: Or32374Const.CP_ID(0) })
const or11216 = ref({ uniqueCpId: '' })
const or11217 = ref({ uniqueCpId: '' })
const or11218 = ref({ uniqueCpId: '' })
const or11219 = ref({ uniqueCpId: '' })
const or11220 = ref({ uniqueCpId: '' })
const or11275 = ref({ uniqueCpId: '' })
const systemCommonsStore = useSystemCommonsStore()
/**************************************************
 * Props
 **************************************************/

interface Props {
  onewayModelValue: Or32374OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOneway = reactive({
  // ラベル
  mo01338: {
    customClass: { itemClass: 'align-center2' },
  } as Mo01338OnewayType,
  mo01338_2: {
    customClass: { itemClass: 'font-red' },
  } as Mo01338OnewayType,
})

const localOneway = reactive({
  overlayShow: true,
  gaiInfo: '',
  kihonInfo: '',
  tokkiInfo: '',
  // 期間管理フラグ
  kikanFlg: '',
  // 選択行計画期間ID
  currentScId: '',
  // 選択行調査票ID
  currentCschId: '',
  currentCschIdIndex: 0,
  ninteiFlg: [] as CodeType[],
  tabId: {},
  mo01338Circle: { ...defaultOneway.mo01338, value: t('label.filled-circle') } as Mo01338OnewayType,
  mo01338Circle2: {
    ...defaultOneway.mo01338_2,
    value: t('label.investigation-ticket-duplication-msg'),
  } as Mo01338OnewayType,
  btnSearchOneway1: {
    btnLabel: t('btn.full-select'),
  },
  btnSearchOneway2: {
    btnLabel: t('btn.full-release'),
  },
  btnSearchOneway3: {
    btnLabel: t('btn.inversion'),
  },
  mo00043OnewayType: {
    tabItems: [],
    minWidth: '58px',
    height: '54px',
  } as Mo00043OnewayType,
  Or32374: {
    ...props.onewayModelValue,
  },
  // 複写テンプレート
  orx0077Oneway: {
    // ［調査票複写］
    mo00024Oneway: {
      maxWidth: '1600px',
      width: '1600px',
      persistent: true,
      showCloseBtn: true,
      mo01344Oneway: {
        name: 'Or32374',
        toolbarTitle: t('label.investigation-ticket-duplication'),
        toolbarName: 'Or32374ToolBar',
        // ツールバータイトルの左寄せ
        toolbarTitleCenteredFlg: false,
        showCardActions: true,
      },
    } as Mo00024OnewayType,
  } as OrX0077OnewayType,
  orX32374Oneway: {
    histList: [],
    // 計画期間リストアイテム
    planPeriodList: [],
    // 計画期間ID
    planPeriodId: '0',
    // 複写履歴リストアイテム
    comprehensivePlanInfoList: [],
    ...props.onewayModelValue,
  } as OrX32374OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定ボタン設置
  mo00609OverwriteBtnOneway: {
    btnLabel: t('btn.confirm'),
    width: '90px',
  } as Mo00609OnewayType,
})

const local = reactive({
  checkBox1: false,
  checkBox2: false,
  checkBox3: false,
  currentTabId: '',
  mo00043: {
    id: '',
  } as Mo00043Type,
  surSlipDupInitInfo: [] as SurSlipDupInitInfo[],
  surSlipDupInitInfo2: [] as SurSlipDupInitInfo2[],
  surSlipDupInitInfo3: [] as SurSlipDupInitInfo3[],
})

/**************************************************
 * 変数定義
 **************************************************/
let copyInitFlg = true;
/**
 * 権限チェック入力値
 */
const input: ICheckAuthzKinouInEntity = {
  keys: [{ path: '/components/custom-components/organisms/Or51786/Or51786' }],
}

/**
 * 権限チェック結果
 */
const res: ICheckAuthzKinouOutEntity = await AuthzRepository.checkAuthzKinou(input)

/**
 * 保存権限の有無を示すフラグ
 */
const isPermissionSave = ref<boolean>(
  Array.isArray(res.data?.authzKinou)
    ? res.data.authzKinou
        .filter((authz) => authz.path === '/components/custom-components/organisms/Or51786/Or51786')
        .every((authz) => authz.use9)
    : false
)
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// const overwriteAddFlg = ref<boolean>(false)
// const histDetailList = ref<HistDetail[]>([])
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or32374StateType>({
  cpId: Or32374Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      OrX0077Logic.state.set({
        uniqueCpId: orx0077.value.uniqueCpId,
        state: { isOpen: value ?? Or32374Const.IS_OPEN },
      })
    },
  },
})
const headers = [
  // 計画期間
  {
    title: t('label.plan-period'), // ヘッダーに表示される名称
    value: 'select',
    key: 'ymd',
    width: '30%',
    sortable: false,
  },
  // 期間内履歴数
  {
    title: t('label.within-the-period-number-of-history'), // ヘッダーに表示される名称
    value: '1',
    key: 'rirekiCount',
    width: '25%',
    sortable: false,
  },
  // 事業所名
  {
    title: t('label.office-name'), // ヘッダーに表示される名称
    value: '1',
    key: 'svJigyoNm',
    width: '30%',
    sortable: false,
  },
]

// テーブルヘッダ
const header2 = reactive({
  headers: [
    // 実施日
    {
      title: t('label.implementation-date'), // ヘッダーに表示される名称
      value: 'select',
      key: 'jisshiDateYmd',
      width: '30%',
      sortable: false,
    },
    // 記入者
    {
      title: t('label.filler'), // ヘッダーに表示される名称
      value: '1',
      key: 'shokuinName',
      width: '25%',
      sortable: false,
    },
    // 改訂
    {
      title: t('label.revision'), // ヘッダーに表示される名称
      value: '1',
      key: 'ninteiName',
      width: '30%',
      sortable: false,
    },
  ],
})

const filteredHeaders = ref()

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0077Const.CP_ID(0)]: orx0077.value,
  [Or11207Const.CP_ID(1)]: or11207.value,
  [Or11216Const.CP_ID(1)]: or11216.value,
  [Or11217Const.CP_ID(1)]: or11217.value,
  [Or11218Const.CP_ID(1)]: or11218.value,
  [Or11219Const.CP_ID(1)]: or11219.value,
  [Or11220Const.CP_ID(1)]: or11220.value,
  [Or11275Const.CP_ID(1)]: or11275.value,
})

onMounted(async () => {
  filteredHeaders.value = header2.headers
  or11207OnewayType.copyParentId = or32374.value.uniqueCpId
  or11207OnewayType.parentId = props.onewayModelValue.parentId
  or11207OnewayType.copyFlg = true
  // tab初期化
  if (props.onewayModelValue.tabId === Or32374Const.TAB_1) {
    localOneway.mo00043OnewayType.tabItems = [
      { id: Or32374Const.TAB_1, title: t('label.certification-survey-tab-1') },
      { id: Or32374Const.TAB_8, title: t('label.multiple') },
    ]
  } else if (props.onewayModelValue.tabId === Or32374Const.TAB_2) {
    localOneway.mo00043OnewayType.tabItems = [
      { id: Or32374Const.TAB_2, title: t('label.certification-survey-tab-2') },
      { id: Or32374Const.TAB_8, title: t('label.multiple') },
    ]
  } else if (props.onewayModelValue.tabId === Or32374Const.TAB_3) {
    localOneway.mo00043OnewayType.tabItems = [
      { id: Or32374Const.TAB_3, title: t('label.certification-survey-tab-3') },
      { id: Or32374Const.TAB_8, title: t('label.multiple') },
    ]
  } else if (props.onewayModelValue.tabId === Or32374Const.TAB_4) {
    localOneway.mo00043OnewayType.tabItems = [
      { id: Or32374Const.TAB_4, title: t('label.certification-survey-tab-4') },
      { id: Or32374Const.TAB_8, title: t('label.multiple') },
    ]
  } else if (props.onewayModelValue.tabId === Or32374Const.TAB_5) {
    localOneway.mo00043OnewayType.tabItems = [
      { id: Or32374Const.TAB_5, title: t('label.certification-survey-tab-5') },
      { id: Or32374Const.TAB_8, title: t('label.multiple') },
    ]
  } else if (props.onewayModelValue.tabId === Or32374Const.TAB_6) {
    localOneway.mo00043OnewayType.tabItems = [
      { id: Or32374Const.TAB_6, title: t('label.certification-survey-tab-6') },
      { id: Or32374Const.TAB_8, title: t('label.multiple') },
    ]
  } else if (props.onewayModelValue.tabId === Or32374Const.TAB_7) {
    localOneway.mo00043OnewayType.tabItems = [
      { id: Or32374Const.TAB_7, title: t('label.certification-survey-tab-7') },
      { id: Or32374Const.TAB_8, title: t('label.multiple') },
    ]
  } else if (props.onewayModelValue.tabId === Or32374Const.TAB_8) {
    localOneway.mo00043OnewayType.tabItems = [
      { id: Or32374Const.TAB_8, title: t('label.certification-survey-tab-8') },
      { id: Or32374Const.TAB_8, title: t('label.multiple') },
    ]
  }
  await init()
})

/**
 * ［調査票複写］初期処理
 */
async function init() {
  local.mo00043.id = props.onewayModelValue.tabId
  // 共通関数より汎用コード一覧を取得する。
  await initCodes()
  // 調査票複写の計画対象期間情報取得
  await initPlanningPeriod()
  // 調査票複写の履歴情報取得
  await initHistoryInfo()
  // 調査票複写の初期情報を取得する
  await initInfo()
}

/**
 *  共通関数より汎用コード一覧を取得する。
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 351（改訂様式）
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NINTEIFLG },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 351（改訂様式）
  localOneway.ninteiFlg = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_NINTEIFLG
  ) as CodeType[]
}

/**
 *  調査票複写の計画対象期間情報取得
 */
const initPlanningPeriod = async () => {
  const inputData: initPlanningPeriodInEntity = {
    /** 事業者ID */
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
    /** 適用事業所IDリスト */
    svJigyoIdList: useSystemCommonsStore().getSvJigyoIdList as string[],
    /** 施設ID */
    shisetuId: systemCommonsStore.getShisetuId ?? '1',
    /** 利用者ID */
    userId: systemCommonsStore.getUserId ?? '1',
    /** 種別ID */
    typeId: systemCommonsStore.getSyubetu ?? '1',
  }
  const resData: initPlanningPeriodOutEntity = await ScreenRepository.select(
    'surveySlipDuplicatePlanningPeriodInfoSelect',
    inputData
  )
  if (resData?.data) {
    local.surSlipDupInitInfo = resData.data.surSlipDupInitInfo
    // 選択行計画期間ID
    localOneway.currentScId = local.surSlipDupInitInfo[0].kikanList[0].scId
    // 期間管理フラグ
    localOneway.kikanFlg = local.surSlipDupInitInfo[0].kikanFlg
    local.surSlipDupInitInfo[0].kikanList.map((item) => {
      return (item.ymd = item.startYmd + t('label.wavy-withoutblank') + item.endYmd)
    })
  } else {
    localOneway.currentScId = ''
  }
}

/**
 *  調査票複写の履歴情報取得
 */
const initHistoryInfo = async () => {
  const inputData: initHistoryInfoInEntity = {
    /** 計画対象期間リスト */
    kikanList: local.surSlipDupInitInfo[0].kikanList,
    /** 調査票改訂フラグ */
    ninteiFlg: props.onewayModelValue.dmyCho,
    /** 事業者ID */
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
    /** 利用者ID */
    userId: systemCommonsStore.getUserId ?? '1',
    /** 期間管理フラグ */
    kikanFlg: local.surSlipDupInitInfo[0].kikanFlg,
    /** 適用事業所IDリスト */
    svJigyoIdList: useSystemCommonsStore().getSvJigyoIdList as string[],
  }
  const resData: initHistoryInfoOutEntity = await ScreenRepository.select(
    'surveySlipDuplicateHistoryInfoSelect',
    inputData
  )
  if (resData?.data) {
    local.surSlipDupInitInfo2 = resData.data.surSlipDupInitInfo
    // 期間管理する場合
    if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
      // 選択行調査票ID
      localOneway.currentCschId = local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[0].cschId
      localOneway.currentCschIdIndex = 0
      local.surSlipDupInitInfo2[0].kikanList[0].rirekiList.map((item) => {
        item.ninteiName = localOneway.ninteiFlg.find(
          (item2) => item2.value === item.ninteiFlg
        )?.label
      })
    } else {
      // 期間管理する場合
      // 選択行調査票ID
      localOneway.currentCschId = local.surSlipDupInitInfo2[0].rirekiList2[0].cschId
      localOneway.currentCschIdIndex = 0
      local.surSlipDupInitInfo2[0].rirekiList2.map((item) => {
        item.ninteiName = localOneway.ninteiFlg.find(
          (item2) => item2.value === item.ninteiFlg
        )?.label
      })
    }
  } else {
    localOneway.currentCschId = ''
    localOneway.currentCschIdIndex = 0
  }
}

/**
 *  調査票複写の初期情報を取得する
 */
const initInfo = async () => {
  let ninteiFlg = ''
  if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
    ninteiFlg = local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[0].ninteiFlg
  } else {
    ninteiFlg = local.surSlipDupInitInfo2[0].rirekiList2[0].ninteiFlg
  }
  const inputData: initInfoInEntity = {
    /** 調査票改訂フラグ */
    ninteiFlg: ninteiFlg,
    /** インデックス */
    tabIndex: props.onewayModelValue.tabId,
    /** 調査票履歴リスト */
    rirekiList: local.surSlipDupInitInfo2[0].kikanList[0].rirekiList,
  }
  const resData: initInfoOutEntity = await ScreenRepository.select(
    'surveySlipDuplicateInitialInfoSelect',
    inputData
  )
  if (resData?.data) {
    local.surSlipDupInitInfo3 = resData.data.surSlipDupInitInfo
    // // 期間管理する場合
    // if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
    //   local.surSlipDupInitInfo3[0].ninteiFlg =
    //     local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[0].ninteiFlg
    //   local.surSlipDupInitInfo3[0].jisshiDateYmd =
    //     local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[0].jisshiDateYmd
    // } else {
    //   // 期間管理する場合
    //   local.surSlipDupInitInfo3[0].ninteiFlg = local.surSlipDupInitInfo2[0].rirekiList2[0].ninteiFlg
    //   local.surSlipDupInitInfo3[0].jisshiDateYmd =
    //     local.surSlipDupInitInfo2[0].rirekiList2[0].jisshiDateYmd
    // }

    Or32374Logic.data.set({
      uniqueCpId: or32374.value.uniqueCpId,
      state: local.surSlipDupInitInfo3[0],
    })

    Or32374Logic.tabInfo.set({
      uniqueCpId: or32374.value.uniqueCpId,
      state: {
        tabId: props.onewayModelValue.tabId,
        state: Or32374Const.GET_INIT_DATA1,
      },
    })
  }
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 */
function close() {
  // 画面を閉じる。
  setState({ isOpen: false })
}

/**
 * 確定ボタン押下時の処理
 *
 *
 */
async function save() {
  const msg =
    local.mo00043.id === Or32374Const.TAB_8
      ? t('label.assessment')
      : t('label.approval-questionnaire')
  // 履歴が未選択の場合
  if (localOneway.currentCschId === '') {
    const dialogResult = await openWarningDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11289'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
    })

    switch (dialogResult) {
      case 'yes':
        return
    }
  }

  if (local.mo00043.id === Or32374Const.TAB_8) {
    if (local.checkBox1 === false && local.checkBox2 === false && local.checkBox3 === false) {
      // "複数"タブ選択時複写対象の画面を1画面以上未選択の場合
      const dialogResult = await openWarningDialog(or21814.value.uniqueCpId, {
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11289'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
      })

      switch (dialogResult) {
        case 'yes':
          return
      }
    }
  }

  // 各タブ選択時複写対象の履歴を選択済みの場合
  const dialogResult = await openWarningDialog(or21814.value.uniqueCpId, {
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10193', [msg]),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
  })

  switch (dialogResult) {
    case 'yes': {
      let copymodel = ''
      const noInfoList = [] as NoInfo[]
      // "複数"タブ選択時
      if (local.mo00043.id === Or32374Const.TAB_8) {
        copymodel = Or32374Const.COPYMODEL2
        if (local.checkBox1 === true) {
          noInfoList.push({ no: Or32374Const.CHECKBOX1 })
        }
        if (local.checkBox2 === true) {
          noInfoList.push({ no: Or32374Const.CHECKBOX2 })
        }
        if (local.checkBox3 === true) {
          noInfoList.push({ no: Or32374Const.CHECKBOX3 })
        }
      } else {
        copymodel = Or32374Const.COPYMODEL1
        if (props.onewayModelValue.tabId === Or32374Const.TAB_1) {
          noInfoList.push({ no: Or32374Const.CHECKBOX1 })
        } else if (props.onewayModelValue.tabId === Or32374Const.TAB_7) {
          noInfoList.push({ no: Or32374Const.CHECKBOX3 })
        } else {
          noInfoList.push({ no: Or32374Const.CHECKBOX2 })
        }
      }
      let motoNinteiFlg = ''
      let motoSc1Id = ''
      // 期間管理する場合
      if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
        motoNinteiFlg =
          local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[localOneway.currentCschIdIndex]
            .ninteiFlg
        motoSc1Id =
          local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[localOneway.currentCschIdIndex].sc1Id
      } else {
        // 期間管理する場合
        motoNinteiFlg =
          local.surSlipDupInitInfo2[0].rirekiList2[localOneway.currentCschIdIndex].ninteiFlg
        motoSc1Id = local.surSlipDupInitInfo2[0].rirekiList2[localOneway.currentCschIdIndex].sc1Id
      }

      // 各タブ選択時
      const inputData: savePlanningPeriodCopyInEntity = {
        /**
         *複写モデル
         */
        copymodel: copymodel,

        /**
         *期間管理フラグ
         */
        kikanFlg: localOneway.kikanFlg,

        /**
         *事業者ID
         */
        svJigyoId: props.onewayModelValue.svJigyoId,

        /**
         *法人ID
         */
        hojinId: props.onewayModelValue.hojinId,

        /**
         *施設ID
         */
        shisetuId: props.onewayModelValue.shisetuId,

        /**
         *利用者ID
         */
        userId: props.onewayModelValue.userId,

        /**
         *種別ID
         */
        syubetsuId: props.onewayModelValue.syubetsuId,

        /**
         *実施日
         */
        jisshiDateYmd: props.onewayModelValue.jisshiDateYmd,

        /**
         *記入者コード
         */
        chkShokuId: props.onewayModelValue.chkShokuId,

        /**
         *複写先調査票ID
         */
        sakiCschId: props.onewayModelValue.cschId,

        /**
         *複写先改訂フラグ
         */
        sakiNinteiFlg: props.onewayModelValue.dmyCho,

        /**
         *複写先計画期間ID
         */
        sakiSc1Id: props.onewayModelValue.sc1Id,

        /**
         *複写元調査票ID
         */
        motoCschId: localOneway.currentCschId,

        /**
         *複写元改訂フラグ
         */
        motoNinteiFlg: motoNinteiFlg,

        /**
         *複写元計画期間ID
         */
        motoSc1Id: motoSc1Id,

        /**
         *タブNoリスト
         */
        noList: noInfoList,

        /**
         *医師意見書ID
         */
        ikenshoId: props.onewayModelValue.ikenshoId,
      }
      const resData: savePlanningPeriodCopyOutEntity = await ScreenRepository.update(
        'surveySlipDuplicateMultipleUpdate',
        inputData
      )
      let rirekiNo = ''
      let dmyCho = ''
      let cschId = ''
      let rirekiSyoriKbn = ''
      let sc1Id = ''
      if (resData?.data) {
        // "複数"タブ選択時
        if (local.mo00043.id === Or32374Const.TAB_8) {
          rirekiNo = ''
          dmyCho = props.onewayModelValue.dmyCho
          cschId = props.onewayModelValue.cschId
          rirekiSyoriKbn = 'open'
          sc1Id = props.onewayModelValue.sc1Id
        } else {
          rirekiNo = props.onewayModelValue.rirekiNo
          dmyCho = motoNinteiFlg
          cschId = localOneway.currentCschId
          rirekiSyoriKbn = ''
          sc1Id = localOneway.currentScId
        }
        const inputData2: GeneralSituationSurveyDuplicateInfoSelectInEntity = {
          /** タブID */
          tabId: props.onewayModelValue.tabId,
          /** 履歴番号 */
          rirekiNo: rirekiNo,
          /** 調査票改訂フラグ */
          dmyCho: dmyCho,
          /** 医師意見書ID */
          ikenshoId: '',
          /** 調査票ID */
          cschId: cschId,
          /** 履歴処理区分 */
          rirekiSyoriKbn: rirekiSyoriKbn,
          /** 期間処理区分 */
          syoriKbn: '',
          /** 計画期間ID */
          sc1Id: sc1Id,
          /** 事業所ID */
          defSvJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
          /** 法人ID */
          houjinId: systemCommonsStore.getHoujinId ?? '1',
          /** 利用者ID */
          userId: systemCommonsStore.getUserId ?? '1',
          /** 施設ID */
          shisetuId: systemCommonsStore.getShisetuId ?? '1',
          /** 適用事業所ＩＤリスト */
          jigyoIdList: useSystemCommonsStore().getSvJigyoIdList.map((item) => {
            return { jigyoId: item }
          }) as JigyoIdInfo[],
          /** 事業者グループ適用ID */
          groupId: systemCommonsStore.getTekiyouGroupId ?? '1',
          /** 複写モデル */
          copymodel: copymodel,
        }
        // 概況調査の複写情報取得
        const resData2: GeneralSituationSurveyDuplicateInfoSelectOutEntity =
          await ScreenRepository.select('generalSituationSurveyDuplicateInfoSelect', inputData2)
        if (resData2?.data) {
          const logic = Or30055Logic.data.get(props.onewayModelValue.parentId)
          if (logic){
            logic.rirekiOutData = resData2.data.rirekiOutData
            Or30055Logic.data.set({
              uniqueCpId: props.onewayModelValue.parentId,
              state: logic,
            })
            Or30055Logic.tabInfo.set({
              uniqueCpId: props.onewayModelValue.parentId,
              state: {
                tabId: props.onewayModelValue.tabId,
                state: Or30055Const.COYP_INIT,
              },
            })
            // 画面を閉じる。
            setState({ isOpen: false })
          }
        }
      }
      return
    }
    case 'no':
      return
  }
}
/**
 * 「計画期間選択一覧」選択変更
 *
 * @param item - 期間情報
 */
async function scIdSelectRow(item: KikanInfo) {
  localOneway.currentScId = item.scId
  const kikanList = [] as KikanInfo[]
  kikanList.push(item)
  // 調査票複写の履歴情報取得
  const inputData: initHistoryInfoInEntity = {
    /** 計画対象期間リスト */
    kikanList: kikanList,
    /** 調査票改訂フラグ */
    ninteiFlg: props.onewayModelValue.dmyCho,
    /** 事業者ID */
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
    /** 利用者ID */
    userId: systemCommonsStore.getUserId ?? '1',
    /** 期間管理フラグ */
    kikanFlg: Or32374Const.PERIOD_MANAGE_FLAG_1,
    // 適用事業所IDリスト
    svJigyoIdList: useSystemCommonsStore().getSvJigyoIdList as string[],
  }
  const resData: initHistoryInfoOutEntity = await ScreenRepository.select(
    'surveySlipDuplicateHistoryInfoSelect',
    inputData
  )
  if (resData?.data) {
    local.surSlipDupInitInfo2 = resData.data.surSlipDupInitInfo
    // 期間管理する場合
    if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
      // 選択行調査票ID
      localOneway.currentCschId = local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[0].cschId
      localOneway.currentCschIdIndex = 0
      local.surSlipDupInitInfo2[0].kikanList[0].rirekiList.map((item) => {
        item.ninteiName = localOneway.ninteiFlg.find(
          (item2) => item2.value === item.ninteiFlg
        )?.label
      })
    } else {
      // 期間管理する場合
      // 選択行調査票ID
      localOneway.currentCschId = local.surSlipDupInitInfo2[0].rirekiList2[0].cschId
      localOneway.currentCschIdIndex = 0
      local.surSlipDupInitInfo2[0].rirekiList2.map((item) => {
        item.ninteiName = localOneway.ninteiFlg.find(
          (item2) => item2.value === item.ninteiFlg
        )?.label
      })
    }
  } else {
    localOneway.currentCschId = ''
    localOneway.currentCschIdIndex = 0
  }

  // 「複数」タブ押下
  if (local.mo00043.id === Or32374Const.TAB_8) {
    let ninteiFlg = ''
    // 期間管理する場合
    if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
      ninteiFlg =
        local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[localOneway.currentCschIdIndex]
          .ninteiFlg
    } else {
      // 期間管理する場合
      ninteiFlg = local.surSlipDupInitInfo2[0].rirekiList2[localOneway.currentCschIdIndex].ninteiFlg
    }
    // 期間管理する場合
    // 調査票複写情報再取得する。
    const inputData: surveySlipCopyDetailInfoInEntity = {
      /** 調査票改訂フラグ */
      ninteiFlg: ninteiFlg,
      /** 計画期間ID */
      sc1Id: localOneway.currentScId,
      /** 調査票ID */
      cschId: localOneway.currentCschId,
      /** インデックス */
      tabIndex: '0',
      /** 複写モデル */
      copymodel: '2',
    }
    const resData: surveySlipCopyDetailInfoOutEntity = await ScreenRepository.select(
      'surveySlipDuplicateMultipleDetailInfoSelect',
      inputData
    )
    if (resData?.data) {
      localOneway.gaiInfo = resData.data.surSlipDupInitInfo[0].gaiInfo
        ? resData.data.surSlipDupInitInfo[0].gaiInfo[0].cschId
        : ''
      localOneway.kihonInfo = resData.data.surSlipDupInitInfo[0].kihonInfo
        ? resData.data.surSlipDupInitInfo[0].kihonInfo[0].cschId
        : ''
      localOneway.tokkiInfo = resData.data.surSlipDupInitInfo[0].tokkiInfo
        ? resData.data.surSlipDupInitInfo[0].tokkiInfo[0].cschId
        : ''
    }
  } else {
    //「複数」以外タブ押下
    let ninteiFlg = ''
    // 期間管理する場合
    if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
      ninteiFlg =
        local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[localOneway.currentCschIdIndex]
          .ninteiFlg
    } else {
      // 期間管理する場合
      ninteiFlg = local.surSlipDupInitInfo2[0].rirekiList2[localOneway.currentCschIdIndex].ninteiFlg
    }
    // 調査票複写情報再取得する。
    const inputData2: surveySlipCopyDetailInfoInEntity = {
      /** 調査票改訂フラグ */
      ninteiFlg: ninteiFlg,
      /** 計画期間ID */
      sc1Id: item.scId,
      /** 調査票ID */
      cschId: local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[0].cschId,
      /** インデックス */
      tabIndex: props.onewayModelValue.tabId,
      /** 複写モデル */
      copymodel: Or32374Const.COPYMODEL1,
    }
    const resData2: surveySlipCopyDetailInfoOutEntity = await ScreenRepository.select(
      'surveySlipDuplicateMultipleDetailInfoSelect',
      inputData2
    )
    if (resData2?.data) {
      local.surSlipDupInitInfo3[0].gaiList = resData2.data.surSlipDupInitInfo[0].gaiList
      local.surSlipDupInitInfo3[0].kihon1List = resData2.data.surSlipDupInitInfo[0].kihon1List
      local.surSlipDupInitInfo3[0].kihon2List = resData2.data.surSlipDupInitInfo[0].kihon2List
      local.surSlipDupInitInfo3[0].kihon3List = resData2.data.surSlipDupInitInfo[0].kihon3List
      local.surSlipDupInitInfo3[0].kihon4List = resData2.data.surSlipDupInitInfo[0].kihon4List
      local.surSlipDupInitInfo3[0].kihon5List = resData2.data.surSlipDupInitInfo[0].kihon5List
      local.surSlipDupInitInfo3[0].tokkiList = resData2.data.surSlipDupInitInfo[0].tokkiList
      // 期間管理する場合
      // if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
      //   local.surSlipDupInitInfo3[0].ninteiFlg =
      //     local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[0].ninteiFlg
      //   local.surSlipDupInitInfo3[0].jisshiDateYmd =
      //     local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[0].jisshiDateYmd
      // } else {
      //   // 期間管理する場合
      //   local.surSlipDupInitInfo3[0].ninteiFlg =
      //     local.surSlipDupInitInfo2[0].rirekiList2[0].ninteiFlg
      //   local.surSlipDupInitInfo3[0].jisshiDateYmd =
      //     local.surSlipDupInitInfo2[0].rirekiList2[0].jisshiDateYmd
      // }

      Or32374Logic.data.set({
        uniqueCpId: or32374.value.uniqueCpId,
        state: local.surSlipDupInitInfo3[0],
      })

      Or32374Logic.tabInfo.set({
        uniqueCpId: or32374.value.uniqueCpId,
        state: {
          tabId: props.onewayModelValue.tabId,
          state: Or32374Const.GET_INIT_DATA2,
        },
      })
    }
  }
}

/**
 * 履歴選択(期間管理フラグ:0)
 *
 * @param item - 履歴情報
 *
 * @param index - index
 */
async function cschIdSelectRow(item: RirekiList2Info, index: number) {
  localOneway.currentCschId = item.cschId
  localOneway.currentCschIdIndex = index
  // 「複数」タブ押下
  if (local.mo00043.id === Or32374Const.TAB_8) {
    let ninteiFlg = ''
    // 期間管理する場合
    if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
      ninteiFlg =
        local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[localOneway.currentCschIdIndex]
          .ninteiFlg
    } else {
      // 期間管理する場合
      ninteiFlg = local.surSlipDupInitInfo2[0].rirekiList2[localOneway.currentCschIdIndex].ninteiFlg
    }
    // 期間管理する場合
    // 調査票複写情報再取得する。
    const inputData: surveySlipCopyDetailInfoInEntity = {
      /** 調査票改訂フラグ */
      ninteiFlg: ninteiFlg,
      /** 計画期間ID */
      sc1Id: localOneway.currentScId,
      /** 調査票ID */
      cschId: localOneway.currentCschId,
      /** インデックス */
      tabIndex: '0',
      /** 複写モデル */
      copymodel: '2',
    }
    const resData: surveySlipCopyDetailInfoOutEntity = await ScreenRepository.select(
      'surveySlipDuplicateMultipleDetailInfoSelect',
      inputData
    )
    if (resData?.data) {
      localOneway.gaiInfo = resData.data.surSlipDupInitInfo[0].gaiInfo
        ? resData.data.surSlipDupInitInfo[0].gaiInfo[0].cschId
        : ''
      localOneway.kihonInfo = resData.data.surSlipDupInitInfo[0].kihonInfo
        ? resData.data.surSlipDupInitInfo[0].kihonInfo[0].cschId
        : ''
      localOneway.tokkiInfo = resData.data.surSlipDupInitInfo[0].tokkiInfo
        ? resData.data.surSlipDupInitInfo[0].tokkiInfo[0].cschId
        : ''
    }
  } else {
    //「複数」以外タブ押下
    // 調査票複写情報再取得する。
    const inputData: surveySlipCopyDetailInfoInEntity = {
      /** 調査票改訂フラグ */
      ninteiFlg: item.ninteiFlg,
      /** 計画期間ID */
      sc1Id: item.sc1Id,
      /** 調査票ID */
      cschId: item.cschId,
      /** インデックス */
      tabIndex: props.onewayModelValue.tabId,
      /** 複写モデル */
      copymodel: Or32374Const.COPYMODEL1,
    }
    const resData: surveySlipCopyDetailInfoOutEntity = await ScreenRepository.select(
      'surveySlipDuplicateMultipleDetailInfoSelect',
      inputData
    )
    if (resData?.data) {
      local.surSlipDupInitInfo3[0].gaiList = resData.data.surSlipDupInitInfo[0].gaiList
      local.surSlipDupInitInfo3[0].kihon1List = resData.data.surSlipDupInitInfo[0].kihon1List
      local.surSlipDupInitInfo3[0].kihon2List = resData.data.surSlipDupInitInfo[0].kihon2List
      local.surSlipDupInitInfo3[0].kihon3List = resData.data.surSlipDupInitInfo[0].kihon3List
      local.surSlipDupInitInfo3[0].kihon4List = resData.data.surSlipDupInitInfo[0].kihon4List
      local.surSlipDupInitInfo3[0].kihon5List = resData.data.surSlipDupInitInfo[0].kihon5List
      local.surSlipDupInitInfo3[0].tokkiList = resData.data.surSlipDupInitInfo[0].tokkiList
      // 期間管理する場合
      // if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
      //   local.surSlipDupInitInfo3[0].ninteiFlg = item.ninteiFlg
      //   local.surSlipDupInitInfo3[0].jisshiDateYmd = item.jisshiDateYmd
      // } else {
      //   // 期間管理する場合
      //   local.surSlipDupInitInfo3[0].ninteiFlg = item.ninteiFlg
      //   local.surSlipDupInitInfo3[0].jisshiDateYmd = item.jisshiDateYmd
      // }

      Or32374Logic.data.set({
        uniqueCpId: or32374.value.uniqueCpId,
        state: local.surSlipDupInitInfo3[0],
      })

      Or32374Logic.tabInfo.set({
        uniqueCpId: or32374.value.uniqueCpId,
        state: {
          tabId: props.onewayModelValue.tabId,
          state: Or32374Const.GET_INIT_DATA3,
        },
      })
    }
  }
}

/**
 * 履歴選択(期間管理フラグ:1)
 *
 * @param item - 履歴情報
 *
 * @param index - index
 */
async function cschIdSelectRow2(item: RirekiInfo, index: number) {
  localOneway.currentCschId = item.cschId
  localOneway.currentCschIdIndex = index
  // 「複数」タブ押下
  if (local.mo00043.id === Or32374Const.TAB_8) {
    let ninteiFlg = ''
    // 期間管理する場合
    if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
      ninteiFlg =
        local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[localOneway.currentCschIdIndex]
          .ninteiFlg
    } else {
      // 期間管理する場合
      ninteiFlg = local.surSlipDupInitInfo2[0].rirekiList2[localOneway.currentCschIdIndex].ninteiFlg
    }
    // 期間管理する場合
    // 調査票複写情報再取得する。
    const inputData: surveySlipCopyDetailInfoInEntity = {
      /** 調査票改訂フラグ */
      ninteiFlg: ninteiFlg,
      /** 計画期間ID */
      sc1Id: localOneway.currentScId,
      /** 調査票ID */
      cschId: localOneway.currentCschId,
      /** インデックス */
      tabIndex: '0',
      /** 複写モデル */
      copymodel: '2',
    }
    const resData: surveySlipCopyDetailInfoOutEntity = await ScreenRepository.select(
      'surveySlipDuplicateMultipleDetailInfoSelect',
      inputData
    )
    if (resData?.data) {
      localOneway.gaiInfo = resData.data.surSlipDupInitInfo[0].gaiInfo
        ? resData.data.surSlipDupInitInfo[0].gaiInfo[0].cschId
        : ''
      localOneway.kihonInfo = resData.data.surSlipDupInitInfo[0].kihonInfo
        ? resData.data.surSlipDupInitInfo[0].kihonInfo[0].cschId
        : ''
      localOneway.tokkiInfo = resData.data.surSlipDupInitInfo[0].tokkiInfo
        ? resData.data.surSlipDupInitInfo[0].tokkiInfo[0].cschId
        : ''
    }
  } else {
    //「複数」以外タブ押下
    // 調査票複写情報再取得する。
    const inputData: surveySlipCopyDetailInfoInEntity = {
      /** 調査票改訂フラグ */
      ninteiFlg: item.ninteiFlg,
      /** 計画期間ID */
      sc1Id: item.sc1Id,
      /** 調査票ID */
      cschId: item.cschId,
      /** インデックス */
      tabIndex: props.onewayModelValue.tabId,
      /** 複写モデル */
      copymodel: Or32374Const.COPYMODEL1,
    }
    const resData: surveySlipCopyDetailInfoOutEntity = await ScreenRepository.select(
      'surveySlipDuplicateMultipleDetailInfoSelect',
      inputData
    )
    if (resData?.data) {
      local.surSlipDupInitInfo3[0].gaiList = resData.data.surSlipDupInitInfo[0].gaiList
      local.surSlipDupInitInfo3[0].kihon1List = resData.data.surSlipDupInitInfo[0].kihon1List
      local.surSlipDupInitInfo3[0].kihon2List = resData.data.surSlipDupInitInfo[0].kihon2List
      local.surSlipDupInitInfo3[0].kihon3List = resData.data.surSlipDupInitInfo[0].kihon3List
      local.surSlipDupInitInfo3[0].kihon4List = resData.data.surSlipDupInitInfo[0].kihon4List
      local.surSlipDupInitInfo3[0].kihon5List = resData.data.surSlipDupInitInfo[0].kihon5List
      local.surSlipDupInitInfo3[0].tokkiList = resData.data.surSlipDupInitInfo[0].tokkiList
      // 期間管理する場合
      // if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
      //   local.surSlipDupInitInfo3[0].ninteiFlg = item.ninteiFlg
      //   local.surSlipDupInitInfo3[0].jisshiDateYmd = item.jisshiDateYmd
      // } else {
      //   // 期間管理する場合
      //   local.surSlipDupInitInfo3[0].ninteiFlg = item.ninteiFlg
      //   local.surSlipDupInitInfo3[0].jisshiDateYmd = item.jisshiDateYmd
      // }

      Or32374Logic.data.set({
        uniqueCpId: or32374.value.uniqueCpId,
        state: local.surSlipDupInitInfo3[0],
      })

      Or32374Logic.tabInfo.set({
        uniqueCpId: or32374.value.uniqueCpId,
        state: {
          tabId: props.onewayModelValue.tabId,
          state: Or32374Const.GET_INIT_DATA3,
        },
      })
    }
  }
}

/**
 * タブ押下
 */
async function tabClick() {
  let ninteiFlg = ''
  // 期間管理する場合
  if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
    ninteiFlg =
      local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[localOneway.currentCschIdIndex].ninteiFlg
  } else {
    // 期間管理する場合
    ninteiFlg = local.surSlipDupInitInfo2[0].rirekiList2[localOneway.currentCschIdIndex].ninteiFlg
  }
  // 「複数」タブ押下
  if (local.mo00043.id === Or32374Const.TAB_8) {
    localOneway.overlayShow = false
    // 期間管理する場合
    // 調査票複写情報再取得する。
    const inputData: surveySlipCopyDetailInfoInEntity = {
      /** 調査票改訂フラグ */
      ninteiFlg: ninteiFlg,
      /** 計画期間ID */
      sc1Id: localOneway.currentScId,
      /** 調査票ID */
      cschId: localOneway.currentCschId,
      /** インデックス */
      tabIndex: '0',
      /** 複写モデル */
      copymodel: '2',
    }
    const resData: surveySlipCopyDetailInfoOutEntity = await ScreenRepository.select(
      'surveySlipDuplicateMultipleDetailInfoSelect',
      inputData
    )
    if (resData?.data) {
      localOneway.gaiInfo = resData.data.surSlipDupInitInfo[0].gaiInfo
        ? resData.data.surSlipDupInitInfo[0].gaiInfo[0].cschId
        : ''
      localOneway.kihonInfo = resData.data.surSlipDupInitInfo[0].kihonInfo
        ? resData.data.surSlipDupInitInfo[0].kihonInfo[0].cschId
        : ''
      localOneway.tokkiInfo = resData.data.surSlipDupInitInfo[0].tokkiInfo
        ? resData.data.surSlipDupInitInfo[0].tokkiInfo[0].cschId
        : ''
    }
  } else {
    localOneway.overlayShow = true
    // 「複数」以外タブ押下
    // 調査票複写情報再取得する。
    const inputData: surveySlipCopyDetailInfoInEntity = {
      /** 調査票改訂フラグ */
      ninteiFlg: ninteiFlg,
      /** 計画期間ID */
      sc1Id: localOneway.currentScId,
      /** 調査票ID */
      cschId: localOneway.currentCschId,
      /** インデックス */
      tabIndex: props.onewayModelValue.tabId,
      /** 複写モデル */
      copymodel: Or32374Const.COPYMODEL1,
    }
    const resData: surveySlipCopyDetailInfoOutEntity = await ScreenRepository.select(
      'surveySlipDuplicateMultipleDetailInfoSelect',
      inputData
    )
    if (resData?.data) {
      local.surSlipDupInitInfo3[0].gaiList = resData.data.surSlipDupInitInfo[0].gaiList
      local.surSlipDupInitInfo3[0].kihon1List = resData.data.surSlipDupInitInfo[0].kihon1List
      local.surSlipDupInitInfo3[0].kihon2List = resData.data.surSlipDupInitInfo[0].kihon2List
      local.surSlipDupInitInfo3[0].kihon3List = resData.data.surSlipDupInitInfo[0].kihon3List
      local.surSlipDupInitInfo3[0].kihon4List = resData.data.surSlipDupInitInfo[0].kihon4List
      local.surSlipDupInitInfo3[0].kihon5List = resData.data.surSlipDupInitInfo[0].kihon5List
      local.surSlipDupInitInfo3[0].tokkiList = resData.data.surSlipDupInitInfo[0].tokkiList
      // 期間管理する場合
      // if (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1) {
      //   local.surSlipDupInitInfo3[0].ninteiFlg =
      //     local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[
      //       localOneway.currentCschIdIndex
      //     ].ninteiFlg
      //   local.surSlipDupInitInfo3[0].jisshiDateYmd =
      //     local.surSlipDupInitInfo2[0].kikanList[0].rirekiList[
      //       localOneway.currentCschIdIndex
      //     ].jisshiDateYmd
      // } else {
      //   // 期間管理する場合
      //   local.surSlipDupInitInfo3[0].ninteiFlg =
      //     local.surSlipDupInitInfo2[0].rirekiList2[localOneway.currentCschIdIndex].ninteiFlg
      //   local.surSlipDupInitInfo3[0].jisshiDateYmd =
      //     local.surSlipDupInitInfo2[0].rirekiList2[localOneway.currentCschIdIndex].jisshiDateYmd
      // }
      Or32374Logic.data.set({
        uniqueCpId: or32374.value.uniqueCpId,
        state: local.surSlipDupInitInfo3[0],
      })

      Or32374Logic.tabInfo.set({
        uniqueCpId: or32374.value.uniqueCpId,
        state: {
          tabId: props.onewayModelValue.tabId,
          state: Or32374Const.GET_INIT_DATA3,
        },
      })
    }
  }
}

/**
 * 利用者選択
 *
 * @param userSelfId - 利用者ID
 */
async function onChangeUserSelect(userSelfId: string) {
  if (userSelfId !== '') {
    if(copyInitFlg === false){
      // 調査票複写の計画対象期間情報取得
      const inputData: initPlanningPeriodInEntity = {
        /** 事業者ID */
        svJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
        /** 適用事業所IDリスト */
        svJigyoIdList: useSystemCommonsStore().getSvJigyoIdList as string[],
        /** 施設ID */
        shisetuId: systemCommonsStore.getShisetuId ?? '1',
        /** 利用者ID */
        userId: userSelfId,
        /** 種別ID */
        typeId: systemCommonsStore.getSyubetu ?? '1',
      }
      const resData: initPlanningPeriodOutEntity = await ScreenRepository.select(
        'surveySlipDuplicatePlanningPeriodInfoSelect',
        inputData
      )
      if (resData?.data) {
        local.surSlipDupInitInfo = resData.data.surSlipDupInitInfo
        // 選択行計画期間ID
        localOneway.currentScId = local.surSlipDupInitInfo[0].kikanList[0].scId
        // 期間管理フラグ
        localOneway.kikanFlg = local.surSlipDupInitInfo[0].kikanFlg
        local.surSlipDupInitInfo[0].kikanList.map((item) => {
          return (item.ymd = item.startYmd + t('label.wavy') + item.endYmd)
        })
      } else {
        localOneway.currentScId = ''
      }

      // 調査票複写の履歴情報取得
      await initHistoryInfo()

      // 調査票複写の初期情報を取得する
      await initInfo()
    } else {
      copyInitFlg = false;
    }
  }
}
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => OrX0077Logic.state.get(orx0077.value.uniqueCpId),
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue?.isOpen) {
      close()
    }
  }
)

/**
 * スクロールバーのスクロール
 *
 * @param event - WheelEvent
 */
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()

  const delta = Math.sign(event.deltaY)

  const contentElement = document.querySelectorAll('.tab-main-div')[0]
  if (!contentElement) return

  const currentScroll = contentElement.scrollTop
  const maxScroll = contentElement.scrollHeight - contentElement.clientHeight

  let newScroll = currentScroll + delta * 50

  if (newScroll < 0) newScroll = 0
  if (newScroll > maxScroll) newScroll = maxScroll

  if (newScroll !== currentScroll) {
    contentElement.scrollTo({
      top: newScroll,
      behavior: 'auto',
    })
  }
}
</script>

<template>
  <!-- 複写テンプレート -->
  <g-custom-or-x-0077
    v-bind="orx0077"
    :oneway-model-value="localOneway.orx0077Oneway"
    @on-change-user-select="onChangeUserSelect"
  >
    <!-- テーブル -->
    <template #filter>
      <c-v-row class="table-data">
        <c-v-col
          v-if="localOneway.kikanFlg !== Or32374Const.PERIOD_MANAGE_FLAG_0"
          :cols="6"
          class="pa-0"
          style="padding-right: 8px !important"
        >
          <c-v-data-table
            :items="local.surSlipDupInitInfo ? local.surSlipDupInitInfo[0]?.kikanList : null"
            :headers="headers"
            :hide-default-footer="true"
            class="table-wrapper"
            height="134px"
            :items-per-page="-1"
            hover
            fixed-header
          >
            <!-- 一覧 -->
            <template #item="{ item }">
              <tr
                :class="{ 'row-selected': localOneway.currentScId === item.scId }"
                @click="scIdSelectRow(item)"
              >
                <!-- 計画期間 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.ymd,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 期間内履歴数 -->
                <td class="text-align-right">
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.rirekiCount,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 事業所名 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.svJigyoNm,
                    }"
                  ></base-mo01337>
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
        <c-v-col
          class="pa-0"
          :cols="6"
        >
          <c-v-data-table
            v-if="localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_0"
            style="width: 100%"
            :items="local.surSlipDupInitInfo2 ? local.surSlipDupInitInfo2[0]?.rirekiList2 : null"
            :headers="filteredHeaders"
            :hide-default-footer="true"
            class="table-wrapper"
            height="134px"
            :items-per-page="-1"
            hover
            fixed-header
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'row-selected': localOneway.currentCschId === item.cschId }"
                @click="cschIdSelectRow(item, index)"
              >
                <!-- 実施日 -->
                <td>
                  <base-mo01337 :oneway-model-value="{ value: item.jisshiDateYmd }" />
                </td>
                <!-- 記入者 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.shokuinName,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 改訂 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.ninteiName,
                    }"
                  ></base-mo01337>
                </td>
              </tr>
            </template>
          </c-v-data-table>
          <c-v-data-table
            v-if="localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1"
            style="width: 100%"
            :items="
              local.surSlipDupInitInfo2
                ? local.surSlipDupInitInfo2[0]?.kikanList[0]?.rirekiList
                : null
            "
            :headers="filteredHeaders"
            :hide-default-footer="true"
            class="table-wrapper"
            height="134px"
            :items-per-page="-1"
            hover
            fixed-header
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'row-selected': localOneway.currentCschId === item.cschId }"
                @click="cschIdSelectRow2(item, index)"
              >
                <!-- 実施日 -->
                <td>
                  <base-mo01337 :oneway-model-value="{ value: item.jisshiDateYmd }" />
                </td>
                <!-- 記入者 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.shokuinName,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 改訂 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.ninteiName,
                    }"
                  ></base-mo01337>
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- 複写body -->
    <template #copyMain>
      <c-v-row style="height: 566px; overflow: hidden; margin: 0px">
        <c-v-col class="copy-body">
          <c-v-row
            no-gutters
            style="margin: 0px"
          >
            <c-v-col class="tabItems">
              <base-mo00043
                v-model="local.mo00043"
                :oneway-model-value="localOneway.mo00043OnewayType"
                @click="tabClick"
              >
              </base-mo00043>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutters
            style="margin: 0px"
          >
            <c-v-window
              v-if="
                (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1 &&
                  localOneway.currentCschId !== '' &&
                  localOneway.currentScId !== '') ||
                (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1 &&
                  localOneway.currentCschId !== '')
              "
              v-model="local.mo00043.id"
              class="tab-main-div"
            >
              <c-v-window-item value="1">
                <g-custom-or11207
                  v-bind="or11207"
                  :oneway-model-value="or11207OnewayType"
                ></g-custom-or11207>
              </c-v-window-item>
              <c-v-window-item value="2">
                <g-custom-or11216
                  v-bind="or11216"
                  ref="basicSurvey1"
                  :oneway-model-value="or11207OnewayType"
                ></g-custom-or11216>
              </c-v-window-item>
              <c-v-window-item value="3">
                <g-custom-or11217
                  v-bind="or11217"
                  ref="basicSurvey2"
                  :oneway-model-value="or11207OnewayType"
                ></g-custom-or11217>
              </c-v-window-item>
              <c-v-window-item value="4">
                <g-custom-or11218
                  v-bind="or11218"
                  ref="basicSurvey3"
                  :oneway-model-value="or11207OnewayType"
                ></g-custom-or11218>
              </c-v-window-item>
              <c-v-window-item value="5">
                <g-custom-or11219
                  v-bind="or11219"
                  ref="basicSurvey4"
                  :oneway-model-value="or11207OnewayType"
                ></g-custom-or11219>
              </c-v-window-item>
              <c-v-window-item value="6">
                <g-custom-or11220
                  v-bind="or11220"
                  ref="basicSurvey5"
                  :oneway-model-value="or11207OnewayType"
                ></g-custom-or11220>
              </c-v-window-item>
              <c-v-window-item value="7">
                <g-custom-or11275
                  v-bind="or11275"
                  ref="tokkiList2"
                  :oneway-model-value="or11207OnewayType"
                ></g-custom-or11275>
              </c-v-window-item>
              <c-v-window-item
                value="8"
                style="width: 45%"
              >
                <c-v-row
                  v-if="
                    (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1 &&
                      localOneway.currentCschId !== '' &&
                      localOneway.currentScId !== '') ||
                    (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1 &&
                      localOneway.currentCschId !== '')
                  "
                >
                  <c-v-col>
                    <base-at-checkbox
                      v-model="local.checkBox1"
                      :checkbox-label="t('label.certification-survey-tab-1')"
                      color="key"
                      class="chk-custom"
                      :disabled="localOneway.gaiInfo === ''"
                    />
                  </c-v-col>
                  <c-v-col>
                    <base-at-checkbox
                      v-model="local.checkBox2"
                      :checkbox-label="t('label.basic-survey')"
                      color="key"
                      class="chk-custom"
                      :disabled="localOneway.kihonInfo === ''"
                    />
                  </c-v-col>
                  <c-v-col>
                    <base-at-checkbox
                      v-model="local.checkBox3"
                      :checkbox-label="t('label.special-note-matter-support')"
                      color="key"
                      class="chk-custom"
                      :disabled="localOneway.tokkiInfo === ''"
                    />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  v-if="
                    (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1 &&
                      localOneway.currentCschId !== '' &&
                      localOneway.currentScId !== '') ||
                    (localOneway.kikanFlg === Or32374Const.PERIOD_MANAGE_FLAG_1 &&
                      localOneway.currentCschId !== '')
                  "
                  style="border-left: 1px solid grey; border-top: 1px solid grey"
                >
                  <c-v-col style="border-bottom: 1px solid grey; border-right: 1px solid grey">
                    <base-mo01338
                      v-if="localOneway.gaiInfo !== ''"
                      :oneway-model-value="localOneway.mo01338Circle"
                    />
                  </c-v-col>
                  <c-v-col style="border-bottom: 1px solid grey; border-right: 1px solid grey">
                    <base-mo01338
                      v-if="localOneway.kihonInfo !== ''"
                      :oneway-model-value="localOneway.mo01338Circle"
                    />
                  </c-v-col>
                  <c-v-col style="border-bottom: 1px solid grey; border-right: 1px solid grey">
                    <base-mo01338
                      v-if="localOneway.tokkiInfo !== ''"
                      :oneway-model-value="localOneway.mo01338Circle"
                    />
                  </c-v-col>
                </c-v-row>
                <base-mo01338 :oneway-model-value="localOneway.mo01338Circle2" />
              </c-v-window-item>
            </c-v-window>
            <div
              v-if="localOneway.overlayShow"
              class="overlay"
              @wheel="handleWheel"
            ></div>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- フッターボタン -->
    <template #footer>
      <c-v-row
        no-gutters
        style="margin: 0 !important"
      >
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          style="margin-right: 0 !important"
          v-bind="localOneway.mo00609OverwriteBtnOneway"
          class="mx-2"
          :disabled="!isPermissionSave"
          @click="save()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </g-custom-or-x-0077>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
.table-header :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  font-size: 14px !important;
  font-weight: bold !important;
  box-shadow: none !important;
  border-collapse: collapse !important;
}

:deep(.table-header td) {
  border-color: rgb(var(--v-theme-black-200)) !important;
  font-size: 14px !important;
  border-collapse: collapse !important;
}

.table-wrapper {
  overflow-x: hidden; // 横スクロールバーを非表示
  overflow-y: auto; // 縦スクロールを有効化
}

.table-wrapper :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  font-size: 14px;
  font-weight: bold !important;
  border-collapse: collapse !important;
  box-shadow: none !important;
}

.table-wrapper .v-table__wrapper td {
  padding: 0px 16px !important;
  font-size: 14px !important;
  border: unset !important;
  border-collapse: collapse !important;
}

.table-wrapper :deep(.v-table__wrapper th:first-child) {
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-wrapper :deep(.v-table__wrapper th:not(:first-child)) {
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-wrapper :deep(.v-table__wrapper th:last-child) {
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-wrapper :deep(.v-table__wrapper td:first-child) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-left: 1px transparent solid !important;
}

.table-wrapper :deep(.v-table__wrapper td:not(:first-child)) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-wrapper :deep(.v-table__wrapper td:last-child) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-right: 1px transparent solid !important;
}
.table-wrapper :deep(.v-table__wrapper thead tr:not(:last-child) th:not([rowspan])) {
  border-bottom: none !important;
}
.table-wrapper :deep(.v-table__wrapper thead tr:not(:last-child) th[rowspan='1']) {
  border-bottom: none !important;
}

/***************************************************************
 * CMNダイヤログ画面 選択行の基本スタイル
 ***************************************************************/
/* 選択行のスタイル */
.table-wrapper :deep(.v-table__wrapper .row-selected) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}

/* 選択行のスタイル */
.table-wrapper :deep(.v-table__wrapper .select-row) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}
/* 選択行のスタイル */
.table-wrapper :deep(.v-table__wrapper .selected-row) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}
/***************************************************************
 * CMNダイヤログ画面 ハイライトの基本スタイル
 ***************************************************************/
.highlight-row {
  background: rgb(var(--v-theme-blue-100));
}

.table-header :deep(.highlight-row) {
  background-color: rgb(var(--v-theme-blue-100));
}

/***************************************************************
 * CMNダイヤログ画面 行選択チェックボックスの基本スタイル
 ***************************************************************/
.v-table :deep(i.fill) {
  color: rgb(var(--v-theme-blue-700)) !important;
}

/***************************************************************
 * CMNダイヤログ画面の行カーソル仕様
 ***************************************************************/
tr {
  cursor: pointer;
}
// paddingの除去
.table-wrapper
  :deep(
    td .v-col,
    .v-row.v-row--no-gutters > .v-col,
    .v-row.v-row--no-gutters > [class*='v-col-']
  ) {
  padding: 0px !important;
}
// 行の高さ: 32px
.table-wrapper table tbody tr td,
.table-wrapper .row-height,
:deep(.table-wrapper table tbody tr td),
:deep(.table-wrapper .row-height) {
  height: 32px !important;
  max-height: 32px !important;
  min-height: 32px !important;
}

:deep(.v-card-text) {
  overflow-y: hidden !important;
}
// 右寄せのCSS
.text-align-right {
  text-align: right;
}
:deep(.copy-body > div:first-of-type) {
  width: 100% !important;
}

:deep(.copy-body) {
  padding: 0px;
}

:deep(.v-overlay__content) {
  max-width: 1600px !important;
}

/***************************************************************
 * CMNダイヤログ画面のmargin（デザインガイド_v1.4に従い調整）
 ***************************************************************/
.v-row {
  margin: 8px;
}

/***************************************************************
 * CMNダイヤログ画面の行カーソル仕様
 ***************************************************************/
tr {
  cursor: pointer;
}

:deep(.container) {
  max-height: 491px;
}

:deep(.scroll-container) {
  max-height: 491px;
}

.tab-main-div {
  width: 100%;
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

:deep(.align-center2) {
  text-align: center;
}

:deep(.font-red label) {
  color: #ff0000 !important;
}

.table-data {
  margin: 0 !important;
}

.overlay {
  position: absolute;
  top: 245px;
  left: 296px;
  width: 1281px;
  height: 507px;
  background-color: transparent;
  z-index: 2;
  pointer-events: auto;
  cursor: not-allowed;
}
</style>
