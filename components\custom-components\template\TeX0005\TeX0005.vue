<script setup lang="ts">
/**
 * TeX0005:［情報収集］画面テンプレート
 *
 * @description
 * ［情報収集］画面テンプレート
 *
 * <AUTHOR>
 */

import { onBeforeMount, reactive, ref, watch, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or29829Const } from '../../organisms/Or29829/Or29829.constants'
import { Or30695Logic } from '../../organisms/Or30695/Or30695.logic'
import { Or29829Logic } from '../../organisms/Or29829/Or29829.logic'
import { Or32418Const } from '../../organisms/Or32418/Or32418.constants'
import { Or55244Const } from '../../organisms/Or55244/Or55244.constants'
import { Or32427Const } from '../../organisms/Or32427/Or32427.constants'
import { Or32410Const } from '../../organisms/Or32410/Or32410.constants'
import { Or32430Logic } from '../../organisms/Or32430/Or32430.logic'
import { Or32418Logic } from '../../organisms/Or32418/Or32418.logic'
import { Or30712Const } from '../../organisms/Or30712/Or30712.constants'
import { Or30712Logic } from '../../organisms/Or30712/Or30712.logic'
import { Or32427Logic } from '../../organisms/Or32427/Or32427.logic'
import { OrX0068Const } from '../../organisms/OrX0068/OrX0068.constants'
import { Or28396Const } from '../../organisms/Or28396/Or28396.constants'
import { Or28396Logic } from '../../organisms/Or28396/Or28396.logic'
import type { InfoCollectionInfo, TakingMedication, Medicine ,
  CopyType} from './TeX0005.type'
import { TeX0005Const } from './TeX0005.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useSetupChildProps } from '~/composables/useComponentVue'
import { useScreenStore } from '~/stores/session/screen'
import { useJigyoList } from '~/utils/useJigyoList'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { useCmnRouteCom } from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type {
  TeX0005OnewayType,
  RirekiInfo,
  KikanInfo,
  InfoCollectionInfoType,
} from '~/types/cmn/business/components/TeX0005Type'
import { Or35959Logic } from '~/components/custom-components/organisms/Or35959/Or35959.logic'
import { Or35959Const } from '~/components/custom-components/organisms/Or35959/Or35959.constants'
import { Or32430Const } from '~/components/custom-components/organisms/Or32430/Or32430.constants'
import { Or32415Logic } from '~/components/custom-components/organisms/Or32415/Or32415.logic'
import { Or32415Const } from '~/components/custom-components/organisms/Or32415/Or32415.constants'
import { Or29516Const } from '~/components/custom-components/organisms/Or29516/Or29516.constants'
import { Or29516Logic } from '~/components/custom-components/organisms/Or29516/Or29516.logic'
import { Or33109Const } from '~/components/custom-components/organisms/Or33109/Or33109.constants'
import { Or33109Logic } from '~/components/custom-components/organisms/Or33109/Or33109.logic'
import type { Or27302OnewayType } from '~/types/cmn/business/components/Or27302Type'
import { Or27302Logic } from '~/components/custom-components/organisms/Or27302/Or27302.logic'
import { Or27302Const } from '~/components/custom-components/organisms/Or27302/Or27302.constants'
import type { OrX0001Type, OrX0001OnewayType } from '~/types/cmn/business/components/OrX0001Type'
import { OrX0001Const } from '~/components/custom-components/organisms/OrX0001/OrX0001.constants'
import { OrX0001Logic } from '~/components/custom-components/organisms/OrX0001/OrX0001.logic'
import type { Or52099OnewayType, Or52099Type } from '~/types/cmn/business/components/Or52099Type'
import { Or52099Logic } from '~/components/custom-components/organisms/Or52099/Or52099.logic'
import { Or52099Const } from '~/components/custom-components/organisms/Or52099/Or52099.constants'
import { OrX0115Const } from '~/components/custom-components/organisms/OrX0115/OrX0115.constants'
import { OrX0115Logic } from '~/components/custom-components/organisms/OrX0115/OrX0115.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or35959OnewayType, Or35959Type } from '~/types/cmn/business/components/Or35959Type'
import type { Or32430OnewayType, Or32430Type } from '~/types/cmn/business/components/Or32430Type'
import type { Or32415OnewayType, Or32415Type } from '~/types/cmn/business/components/Or32415Type'
import type { Or29516OnewayType, Or29516Type } from '~/types/cmn/business/components/Or29516Type'
import type { Or33109OnewayType, Or33109Type } from '~/types/cmn/business/components/Or33109Type'
import { Or30695Const } from '~/components/custom-components/organisms/Or30695/Or30695.constants'
import type { Or30695OnewayType, Or30695Type } from '~/types/cmn/business/components/Or30695Type'
import type { Or29829OnewayType, Or29829Type } from '~/types/cmn/business/components/Or29829Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or32418OnewayType, Or32418Type } from '~/types/cmn/business/components/Or32418Type'
import { Or29513Logic } from '~/components/custom-components/organisms/Or29513/Or29513.logic'
import { Or29513Const } from '~/components/custom-components/organisms/Or29513/Or29513.constants'
import type { Or29513Type, Or29513OnewayType } from '~/types/cmn/business/components/Or29513Type'
import { Or55244Logic } from '~/components/custom-components/organisms/Or55244/Or55244.logic'
import type { Or55244OnewayType, Or55244Type } from '~/types/cmn/business/components/Or55244Type'
import type { Or29203OnewayType } from '~/types/cmn/business/components/Or29203Type'
import type { Or29510OnewayType } from '~/types/cmn/business/components/Or29510Type'
import type { Or29212OnewayType } from '~/types/cmn/business/components/Or29212Type'
import { Or29203Const } from '~/components/custom-components/organisms/Or29203/Or29203.constants'
import { Or29510Const } from '~/components/custom-components/organisms/Or29510/Or29510.constants'
import { Or29212Const } from '~/components/custom-components/organisms/Or29212/Or29212.constants'
import type { Or32427OnewayType } from '~/types/cmn/business/components/Or32427Type'
import type { Or32409OnewayType } from '~/types/cmn/business/components/Or32409Type'
import { Or32410Logic } from '~/components/custom-components/organisms/Or32410/Or32410.logic'
import type { Or32410OnewayType, Or32410Type } from '~/types/cmn/business/components/Or32410Type'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or32409Const } from '~/components/custom-components/organisms/Or32409/Or32409.constants'
import { Or32409Logic } from '~/components/custom-components/organisms/Or32409/Or32409.logic'
import { Or29203Logic } from '~/components/custom-components/organisms/Or29203/Or29203.logic'
import { Or29510Logic } from '~/components/custom-components/organisms/Or29510/Or29510.logic'
import { Or29212Logic } from '~/components/custom-components/organisms/Or29212/Or29212.logic'
import type {
  IInfoCollectionImportInEntity,
  IInfoCollectionImportOutEntity,
} from '~/repositories/cmn/entities/InfoCollectionImportEntity'
import type {
  IInfoCollectionHistorySelectInEntity,
  IInfoCollectionHistorySelectOutEntity,
} from '~/repositories/cmn/entities/InfoCollectionHistorySelectEntity'
import type {
  IInfoCollectionPlanPeriodSelectInEntity,
  IInfoCollectionPlanPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/InfoCollectionPlanPeriodSelectEntity'
import type {
  IInfoCollectionUpdateInEntity,
  IInfoCollectionUpdateOutEntity,
} from '~/repositories/cmn/entities/InfoCollectionUpdateEntity'
import type { Or30712OnewayType } from '~/types/cmn/business/components/Or30712Type'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import type { Or28396OnewayType } from '~/types/cmn/business/components/Or28396Type'
import type { OrX0165OnewayType } from '~/types/cmn/business/components/OrX0165Type'
import type { OrX0157OnewayType, OrX0157Type } from '~/types/cmn/business/components/OrX0157Type'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import type { Or26257OnewayType, Or26257Type } from '~/types/cmn/business/components/Or26257Type'
import type { IInfoCollectionDrugSelectOutEntity } from '~/repositories/cmn/entities/InfoCollectionDrugSelectEntity'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: TeX0005OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**
 * 利用者選択監視関数を実行
 */
const { syscomUserListFunc, syscomUserSelectWatchFunc } = useUserListInfo()
/**
 * jigyoListWatch
 */
const { updateJigyoList, jigyoListWatch } = useJigyoList()

const cmnRouteCom = useCmnRouteCom()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or27302 = ref({ uniqueCpId: '' })
const orX0001 = ref({ uniqueCpId: OrX0001Const.CP_ID(1) })
const or32409 = ref({ uniqueCpId: '' })
const or29203 = ref({ uniqueCpId: '' })
const or29510 = ref({ uniqueCpId: '' })
const or29212 = ref({ uniqueCpId: '' })
const or35959 = ref({ uniqueCpId: '' })
const or32430 = ref({ uniqueCpId: '' })
const or32415 = ref({ uniqueCpId: '' })
//［情報収集］画面（7）画面
const or29516 = ref({ uniqueCpId: '' })
const or29829 = ref({ uniqueCpId: '' })
const or30695 = ref({ uniqueCpId: '' })
const or52099 = ref({ uniqueCpId: '' })
//［情報収集］画面（6）画面
const or32410 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
//［情報収集］画面（10）画面
const or32418 = ref({ uniqueCpId: '' })
//［情報収集］画面（1）画面
const or29513 = ref({ uniqueCpId: '' })
//［情報収集］画面（13）画面
const or55244 = ref({ uniqueCpId: '' })
//［情報収集］画面（14）画面
const or32427 = ref({ uniqueCpId: '' })
//［情報収集］画面（4）画面
const or33109 = ref({ uniqueCpId: '' })
// 対象期間画面
const orX0115 = ref({ uniqueCpId: '' })
const or30712 = ref({ uniqueCpId: '' })
const or41179_1 = ref({ uniqueCpId: '' })
const tex0005 = ref({ uniqueCpId: props.uniqueCpId })

const or28396 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' }) // 職員検索モーダル

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  let isTabEdit = false
  switch (local.currentTabId) {
    case TeX0005Const.DEFAULT.TAB_ONE:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or29513.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_TWO:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or32409.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_THREE:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or32430.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_FOUR:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or33109.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_FIVE:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or29203.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_SIX:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or32410.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_SEVEN:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or29516.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_EIGHT:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or29510.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_NINE:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or32415.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_TEN:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or32418.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_ELEVEN:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or29212.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_TWELVE:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or35959.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_THIRTEEN:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or55244.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_FOURTEEN:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or32427.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_FIFTEEN:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or30695.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_SIXTEEN:
      isTabEdit = useScreenStore().isEditByUniqueCpId(or29829.value.uniqueCpId)
      break
    default:
      isTabEdit = false
      break
  }
  return isTabEdit || deleteFlag.value
})

// 親情報
const commonInfo = {
  // 基準日
  baseDate: '',
  // ログイン情報.職員名
  loginUserName: '管理者　太郎',
  // 計画対象期間ID
  sc1Id: '',
  // アセスメントID
  assessmentId: '',
  // 作成者ID
  shokuId: '',
  // 調査アセスメント種別
  surveyAssessmentKind: '',
}

// 様式
const styleFlag = ref<string>('1')
// 計画期間
const planPeriodShow = ref<boolean>(true)
// 履歴
const historyShow = ref<boolean>(true)
// 作成者
const authorShow = ref<boolean>(true)
// 基準日
const baseDateShow = ref<boolean>(true)
// 入力フーム
const inputBoomShow = ref<boolean>(true)

// お気に入りに該当機能
const favorite = ref<boolean>(false)

// 削除フラグ
const deleteFlag = ref<boolean>(false)

/** データ再取得フラグ(履歴) */
const retrieveHistoryDataFlg = ref<boolean>(false)

// 更新区分
const updateFlag = ref<string>(TeX0005Const.UPD_FLAG_NONE)
// 履歴更新区分
const updateHisFlag = ref<string>(TeX0005Const.UPD_FLAG_NONE)

const or30712OnewayType = {} as Or30712OnewayType

// ローカルTwoway
const local = reactive({
  // 作成日
  createDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // タブ
  mo00043: {
    id: '1',
  } as Mo00043Type,
  currentTabId: '1',
  // フラグ
  flag: {
    // 期間管理
    periodManage: '1',
    // 計画期間が登録されていない
    periodManageRegistration: false,
  },
  or30695: {
    mstKbn: '',
  } as Or30695Type,
  or32409: {
    mstKbn: '',
  } as Or30695Type,
  or29203: {
    mstKbn: '',
  } as Or30695Type,
  or29510: {
    mstKbn: '',
  } as Or30695Type,
  or29212: {
    mstKbn: '',
  } as Or30695Type,

  or35959: {} as Or35959Type,
  or32430: {} as Or32430Type,

  or32415: {} as Or32415Type,
  //［情報収集］画面（7）画面
  or29516: {} as Or29516Type,
  //［情報収集］画面（6）画面
  or32410: {} as Or32410Type,
  //［情報収集］画面（10）画面
  or32418: {} as Or32418Type,

  //［情報収集］画面（1）画面
  or29513: {} as Or29513Type,
  //［情報収集］画面（13）画面
  or55244: {} as Or55244Type,
  //［情報収集］画面（14）画面
  or32427: {
    mstKbn: '',
  } as Or30695Type,
  //［情報収集］画面（4）画面
  or33109: {} as Or33109Type,
  // GUI00792 ［履歴選択］画面
  or52099: {} as Or52099Type,
  /** 職員検索 */
  or26257: {} as Or26257Type,
  // 二回目新規ボタン押下State
  addBtnState: false,
  // 事業所ID
  officeId: '1',
  // 計画対象期間情報ID
  planPeriodId: '0',
  // 計画対象期間情報
  kikanInfo: {} as KikanInfo,
  // 履歴情報ID
  historyId: 0,
  // 履歴情報ペイジ番号
  historyPageNo: 0,
  // 履歴情報
  rirekiInfo: {} as RirekiInfo,
  // 履歴総件数
  historyTotalCount: 0,
  // 作成者ID
  sakuseiId: '',
  // 担当者
  createUser: {} as OrX0157Type,
  // 計画期間ページ番号
  planPageNo: 0,
  // 計画期間総件数
  planTotalCount: 0,
  // 計画期間管理フラグ
  kikanFlg: '',
  // 改訂区分
  kaiteiKbn: '0',
  medicineList:{} as Medicine[]
})

// ローカルOneway
const localOneway = reactive({
  // 計画期間
  orX0165Plan: {
    label: t('label.planning-period'),
  } as OrX0165OnewayType,
  // 履歴
  orX0165History: {
    label: t('label.history'),
  } as OrX0165OnewayType,
  // 担当者
  orX0157CreateUser: {
    showEditBtnFlg: true,
    editBtnClass: 'custom-edit-btn',
    inputReadonly: true,
    text: {
      orX0157InputOneway: {
        customClass: {
          outerClass: '',
          labelClass: 'mb-1',
        },
        itemLabel: t('label.cmn-manager'),
        showItemLabel: true,
        isVerticalLabel: true,
        width: '148px',
      },
    },
  } as OrX0157OnewayType,
  // 基準日
  createDateOneway: {
    itemLabel: t('label.base-date'),
    isRequired: true,
    isVerticalLabel: true,
    showItemLabel: true,
    customClass: new CustomClass({ outerClass: '', labelClass: 'mb-1' }),
    width: '135',
  } as Mo00020OnewayType,
  // タブ
  mo00043OnewayType: {
    tabItems: [],
    tabClass: 'pa-0',
    minWidth: '44px',
  } as Mo00043OnewayType,
  // or27302単方向バインド
  or27302Oneway: {} as Or27302OnewayType,
  or52099Oneway: {
    sc1Id: '',
    svJigyoId: '',
    userid: '',
    mstKbn: '',
  } as Or52099OnewayType,
  or29203OnewayType: {
    periodManageFlag: '1',
  } as Or29203OnewayType,
  //［情報収集］画面（2）画面
  or32409OnewayType: {
    periodManageFlag: '1',
  } as Or32409OnewayType,
  or29510OnewayType: {
    periodManageFlag: '1',
  } as Or29510OnewayType,
  or29212OnewayType: {
    periodManageFlag: '1',
  } as Or29212OnewayType,
  or35959OnewayType: {
    periodManageFlag: '1',
  } as Or35959OnewayType,
  or32430OnewayType: {
    periodManageFlag: '1',
  } as Or32430OnewayType,
  or32415OnewayType: {
    periodManageFlag: '1',
  } as Or32415OnewayType,
  //［情報収集］画面（7）画面
  or29516OnewayType: {
    periodManageFlag: '1',
  } as Or29516OnewayType,
  //［情報収集］画面（10）画面
  or32418OnewayType: {
    periodManageFlag: '1',
  } as Or32418OnewayType,
  //［情報収集］画面（1）画面
  or29513OnewayType: {
    periodManageFlag: '0',
  } as Or29513OnewayType,
  or30695OnewayType: {
    periodManageFlag: '1',
  } as Or30695OnewayType,
  or29829OnewayType: {
    periodManageFlag: '1',
  } as Or29829OnewayType,
  or55244OnewayType: {
    periodManageFlag: '1',
  } as Or55244OnewayType,
  or32427OnewayType: {
    periodManageFlag: '1',
  } as Or32427OnewayType,
  or33109OnewayType: {
    periodManageFlag: '1',
  } as Or33109OnewayType,
  or32410OnewayType: {
    periodManageFlag: '1',
  } as Or32410OnewayType,
  // 削除ダイアログ
  orX0001Oneway: {
    createYmd: '',
    kinouKnj: '',
    selectTabName: '',
    startTabName: '',
    endTabName: '',
  } as OrX0001OnewayType,
  // 対象期間画面
  orX0115: {
    kindId: '',
    sc1Id: '',
  } as OrX0115OnewayType,
  // GUI00220 職員検索画面
  or26257: {
    sysCdKbn: '', // システム略称
    secAccountAllFlg: '',
    svJigyoIdList: [],
    shokuinId: '',
    gsysCd: '', // システムコード
    selectMode: '12',
    kijunYmd: systemCommonsStore.getSystemDate ?? '',
    defSvJigyoId: '',
    filterDwFlg: '1',
    koyouState: '',
    areaFlg: '',
    hyoujiColumnList: [],
    misetteiFlg: '1',
    otherRead: '', // 他職員参照権限
    refStopFlg: '',
    syoriFlg: '', // 処理フラグ
    menu1Id: '', // メニュー１ID
    kensuFlg: '',
    shokuinIdList: [], // 職員IDリスト
  } as Or26257OnewayType,
})

// e-文書法対象機能の電子ファイル保存設定区分
const electronicFileSaveSettingscategoryFlag = true

// メッセージ「i-cmn-11260」 - 削除確認画面
const orX0001Type = ref<OrX0001Type>({
  deleteSyubetsu: '',
})
// インフォメーションダイアログの表示フラグ
const showOr21814 = computed(() => {
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 削除確認画面の表示フラグ
const showOrX0001 = computed(() => {
  return OrX0001Logic.state.get(orX0001.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr27302 = computed(() => {
  // Or27302のダイアログ開閉状態
  return Or27302Logic.state.get(or27302.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 履歴選択ポップアップの表示状態を返すComputed
 */
const showDialogOr52099 = computed(() => {
  return Or52099Logic.state.get(or52099.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * ライフサイクルフック
 **************************************************/
// const isInit = useScreenInitFlg()
onBeforeMount(async () => {
  // 利用者を全選択です。
  // 利用者を取得
  await syscomUserListFunc(false, props.uniqueCpId)
  // 五十音フィルタを設定
  const filter = systemCommonsStore.getUserSelectHistoryFilterInitials()
  if (filter === undefined || filter.length === 0) {
    systemCommonsStore.setUserSelectFilterInitials([OrX0068Const.STR_ALL])
  }

  // 事業所選択プルダウンの検索条件設定
  // 検索条件未指定だと、ヘッダーで選択した適用事業所が表示される
  // 検索条件が変更されると、共通処理にて事業所の情報が更新される
  // 更新後やプルダウンの値変更後、変更後の事業所に基づいて処理を行う場合、
  // 関数「 jigyoListWatch 」にコールバック関数を渡す
  // 初期情報取得
  // if (isInit) {
  updateJigyoList(or41179_1.value.uniqueCpId)
  if (systemCommonsStore.getUserSelectSelfId()) {
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }
  // コントロール設定
  // void init()
  // }
})

/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(1)]: or00248.value,
  [OrX0001Const.CP_ID(1)]: orX0001.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  //［情報収集］画面（12）画面
  [Or35959Const.CP_ID(1)]: or35959.value,
  //［情報収集］画面（3）画面
  [Or32430Const.CP_ID(1)]: or32430.value,
  //［情報収集］画面（その他）画面
  [Or30695Const.CP_ID(1)]: or30695.value,
  //［情報収集］画面（10）画面
  [Or32418Const.CP_ID(1)]: or32418.value,
  //［情報収集］画面（7）画面
  [Or29516Const.CP_ID(1)]: or29516.value,
  //［情報収集］画面（13）画面
  [Or55244Const.CP_ID(1)]: or55244.value,
  //［情報収集］画面（9）画面
  [Or32415Const.CP_ID(1)]: or32415.value,
  //［情報収集］画面（2）画面
  [Or32409Const.CP_ID(1)]: or32409.value,
  //［情報収集］画面（5）画面
  [Or29203Const.CP_ID(1)]: or29203.value,
  //［情報収集］画面（8）画面
  [Or29510Const.CP_ID(1)]: or29510.value,
  //［情報収集］画面（11）画面
  [Or29212Const.CP_ID(1)]: or29212.value,
  //［情報収集］画面（14）画面
  [Or32427Const.CP_ID(1)]: or32427.value,
  //［情報収集］画面（4）画面
  [Or33109Const.CP_ID(1)]: or33109.value,
  //［情報収集］画面（1）画面
  [Or29513Const.CP_ID(1)]: or29513.value,
  //［情報収集］画面（6）画面
  [Or32410Const.CP_ID(1)]: or32410.value,
  [Or52099Const.CP_ID(1)]: or52099.value,
  [Or27302Const.CP_ID(1)]: or27302.value,
  [Or30712Const.CP_ID(1)]: or30712.value,
  [Or28396Const.CP_ID(1)]: or28396.value,
  //［情報収集］画面（服薬状況）画面
  [Or29829Const.CP_ID(1)]: or29829.value,
  [Or41179Const.CP_ID(1)]: or41179_1.value,
  [OrX0115Const.CP_ID(1)]: orX0115.value,
  [Or26257Const.CP_ID(1)]: or26257.value,
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      favoriteIconClick()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { favoriteEventFlg: false },
      })
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      void _update().then(() => {
        Or11871Logic.event.set({
          uniqueCpId: or11871.value.uniqueCpId,
          events: { saveEventFlg: false },
        })
      })
    }
    if (newValue.createEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 新規ボタンが押下された場合、新規作成処理を実行する
        void addBtnClick().then(() => {
          Or11871Logic.event.set({
            uniqueCpId: or11871.value.uniqueCpId,
            events: { createEventFlg: false },
          })
        })
      }
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
      void printSettingIconClick().then(() => {
        Or11871Logic.event.set({
          uniqueCpId: or11871.value.uniqueCpId,
          events: { printEventFlg: false },
        })
      })
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      void masterIconClick().then(() => {
        Or11871Logic.event.set({
          uniqueCpId: or11871.value.uniqueCpId,
          events: { masterEventFlg: false },
        })
      })
    }
  }
)

/**
 * データ再取得フラグの監視
 */
watch(retrieveHistoryDataFlg, (newValue) => {
  if (newValue) {
    // 履歴情報取得
    void getHistoryChangeData(TeX0005Const.ACT_FLAG_RETRIEVE).finally(() => {
      retrieveHistoryDataFlg.value = false
    })
  }
})

/**
 * 「削除」フラグ設定
 *
 * @param isDsp -表示フラグ
 */
function setDelFlg(isDsp: boolean) {
  localOneway.or29513OnewayType.deleteFlg = isDsp
  localOneway.or32409OnewayType.deleteFlg = isDsp
  localOneway.or32430OnewayType.deleteFlg = isDsp
  localOneway.or33109OnewayType.deleteFlg = isDsp
  localOneway.or29203OnewayType.deleteFlg = isDsp
  localOneway.or32410OnewayType.deleteFlg = isDsp
  localOneway.or29516OnewayType.deleteFlg = isDsp
  localOneway.or29510OnewayType.deleteFlg = isDsp
  localOneway.or32415OnewayType.deleteFlg = isDsp
  localOneway.or32418OnewayType.deleteFlg = isDsp
  localOneway.or29212OnewayType.deleteFlg = isDsp
  localOneway.or35959OnewayType.deleteFlg = isDsp
  localOneway.or55244OnewayType.deleteFlg = isDsp
  localOneway.or32427OnewayType.deleteFlg = isDsp
  localOneway.or30695OnewayType.deleteFlg = isDsp
  localOneway.or29829OnewayType.deleteFlg = isDsp
}

/**
 * AC011_「削除」押下_AC011
 */
watch(
  () => orX0001Type.value,
  (newValue) => {
    if (newValue) {
      // はい
      if ('0' !== newValue.deleteSyubetsu) {
        // 削除確認画面ダイアログに「現在表示している画面のみ削除する。」を選択する場合
        // アセスメント(インターライ)画面履歴の最新情報を取得する
        switch (local.mo00043.id) {
          case TeX0005Const.DEFAULT.TAB_ONE:
            localOneway.or29513OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_TWO:
            localOneway.or32409OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_THREE:
            localOneway.or32430OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_FOUR:
            localOneway.or33109OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_FIVE:
            localOneway.or29203OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_SIX:
            localOneway.or32410OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_SEVEN:
            localOneway.or29516OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_EIGHT:
            localOneway.or29510OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_NINE:
            localOneway.or32415OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_TEN:
            localOneway.or32418OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_ELEVEN:
            localOneway.or29212OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_TWELVE:
            localOneway.or35959OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_THIRTEEN:
            localOneway.or55244OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_FOURTEEN:
            localOneway.or32427OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_FIFTEEN:
            localOneway.or30695OnewayType.deleteFlg = true
            break
          case TeX0005Const.DEFAULT.TAB_SIXTEEN:
            localOneway.or29829OnewayType.deleteFlg = true
            break
          default:
            break
        }
        if ('1' === newValue.deleteSyubetsu) {
          updateHisFlag.value = TeX0005Const.UPD_FLAG_NONE
          updateFlag.value = TeX0005Const.UPD_FLAG_D
        }
        // 削除確認画面ダイアログに「表示している画面を履歴ごと削除する」を選択する場合
        else if ('2' === newValue.deleteSyubetsu) {
          updateHisFlag.value = TeX0005Const.UPD_FLAG_D
          updateFlag.value = TeX0005Const.UPD_FLAG_NONE
        }

        // 入力フームのすべて項目を非表示
        inputBoomShow.value = false

        // ボタンは実行無効(新規、複写、印刷、削除)
        deleteFlag.value = true
      }
    }
  }
)

/**************************************************
 * 関数
 **************************************************/

/**
 * TAB1~15情報設定
 *
 * @param sortList - 画面表示リスト
 *
 * @param tabId - タブID
 *
 * @param historyInfo - 履歴情報
 *
 * @param periodManageFlag - 期間管理フラグ
 */
const setTabsData = (
  sortList: Record<string, InfoCollectionInfoType[]>,
  tabId: string,
  historyInfo: RirekiInfo,
  periodManageFlag: string
) => {
  switch (tabId) {
    //tab1
    case TeX0005Const.DEFAULT.TAB_ONE: {
      // 履歴情報取得
      localOneway.or29513OnewayType.rirekiInfo = historyInfo
      localOneway.or29513OnewayType.periodManageFlag = periodManageFlag

      Or29513Logic.data.set({
        uniqueCpId: or29513.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab2
    case TeX0005Const.DEFAULT.TAB_TWO: {
      // 履歴情報取得
      localOneway.or32409OnewayType.rirekiInfo = historyInfo
      localOneway.or32409OnewayType.periodManageFlag = periodManageFlag

      Or32409Logic.data.set({
        uniqueCpId: or32409.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab3
    case TeX0005Const.DEFAULT.TAB_THREE: {
      // 履歴情報取得
      localOneway.or32430OnewayType.rirekiInfo = historyInfo
      localOneway.or32430OnewayType.periodManageFlag = periodManageFlag

      Or32430Logic.data.set({
        uniqueCpId: or32430.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab4
    case TeX0005Const.DEFAULT.TAB_FOUR: {
      // 履歴情報取得
      localOneway.or33109OnewayType.rirekiInfo = historyInfo
      localOneway.or33109OnewayType.periodManageFlag = periodManageFlag

      Or33109Logic.data.set({
        uniqueCpId: or33109.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab5
    case TeX0005Const.DEFAULT.TAB_FIVE: {
      // 履歴情報取得
      localOneway.or29203OnewayType.rirekiInfo = historyInfo
      localOneway.or29203OnewayType.periodManageFlag = periodManageFlag

      Or29203Logic.data.set({
        uniqueCpId: or29203.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab6
    case TeX0005Const.DEFAULT.TAB_SIX: {
      // 履歴情報取得
      localOneway.or32410OnewayType.rirekiInfo = historyInfo
      localOneway.or32410OnewayType.periodManageFlag = periodManageFlag

      Or32410Logic.data.set({
        uniqueCpId: or32410.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab7
    case TeX0005Const.DEFAULT.TAB_SEVEN: {
      // 履歴情報取得
      localOneway.or29516OnewayType.rirekiInfo = historyInfo
      localOneway.or29516OnewayType.periodManageFlag = periodManageFlag

      Or29510Logic.data.set({
        uniqueCpId: or29516.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab8
    case TeX0005Const.DEFAULT.TAB_EIGHT: {
      // 履歴情報取得
      localOneway.or29510OnewayType.rirekiInfo = historyInfo
      localOneway.or29510OnewayType.periodManageFlag = periodManageFlag

      Or29510Logic.data.set({
        uniqueCpId: or29510.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab9
    case TeX0005Const.DEFAULT.TAB_NINE: {
      // 履歴情報取得
      localOneway.or32415OnewayType.rirekiInfo = historyInfo
      localOneway.or32415OnewayType.periodManageFlag = periodManageFlag

      Or32415Logic.data.set({
        uniqueCpId: or32415.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab10
    case TeX0005Const.DEFAULT.TAB_TEN: {
      // 履歴情報取得
      localOneway.or32418OnewayType.rirekiInfo = historyInfo
      localOneway.or32418OnewayType.periodManageFlag = periodManageFlag

      Or32418Logic.data.set({
        uniqueCpId: or32418.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab11
    case TeX0005Const.DEFAULT.TAB_ELEVEN: {
      // 履歴情報取得
      localOneway.or29212OnewayType.rirekiInfo = historyInfo
      localOneway.or29212OnewayType.periodManageFlag = periodManageFlag

      Or29212Logic.data.set({
        uniqueCpId: or29212.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab12
    case TeX0005Const.DEFAULT.TAB_TWELVE: {
      // 履歴情報取得
      localOneway.or35959OnewayType.rirekiInfo = historyInfo
      localOneway.or35959OnewayType.periodManageFlag = periodManageFlag

      const count = sortList['1'].filter(
        (item) =>
          item.shosiki1Flg === Or35959Const.DEFAULT.FORMAT_TWO &&
          item.shosiki2Flg === Or35959Const.DEFAULT.FORMAT_FIVE
      ).length
      if (count > 0) {
        sortList['1'][count - 1].rowSpanFlg = true
      }

      Or35959Logic.data.set({
        uniqueCpId: or35959.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab13
    case TeX0005Const.DEFAULT.TAB_THIRTEEN: {
      // 履歴情報取得
      localOneway.or55244OnewayType.rirekiInfo = historyInfo
      localOneway.or55244OnewayType.periodManageFlag = periodManageFlag

      Or55244Logic.data.set({
        uniqueCpId: or55244.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab14
    case TeX0005Const.DEFAULT.TAB_FOURTEEN: {
      // 履歴情報取得
      localOneway.or32427OnewayType.rirekiInfo = historyInfo
      localOneway.or32427OnewayType.periodManageFlag = periodManageFlag

      Or32427Logic.data.set({
        uniqueCpId: or32427.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    //tab15
    case TeX0005Const.DEFAULT.TAB_FIFTEEN: {
      // 履歴情報取得
      localOneway.or30695OnewayType.rirekiInfo = historyInfo
      localOneway.or30695OnewayType.periodManageFlag = periodManageFlag

      Or30695Logic.data.set({
        uniqueCpId: or30695.value.uniqueCpId,
        value: sortList,
        isInit: true,
      })
      break
    }
    default:
      break
  }
}
/**
 * 画面コントロール表示設定
 *
 * @param planPeriodFlag - 計画期間フラグ
 *
 * @param planPeriodInfo - 計画期間情報
 *
 * @param historyInfo - 履歴情報
 */
const setFormData = (
  planPeriodFlag: string,
  planPeriodInfo: KikanInfo,
  historyInfo: RirekiInfo
) => {
  // 期間管理フラグ
  local.flag.periodManage = planPeriodFlag
  local.flag.periodManageRegistration = true

  // 計画対象期間を設定
  // if (planPeriodInfo.kikanCnt !== '') {
  local.flag.periodManage = planPeriodFlag
  // 期間管理フラグが「1:管理する」
  if (TeX0005Const.DEFAULT.PLANNING_PERIOD_MANAGE === local.flag.periodManage) {
    // 計画対象期間 を表示にする
    planPeriodShow.value = true
    // 期間総件数 = 0（期間なし）
    if (!planPeriodInfo || Number(planPeriodInfo.kikanCnt) === 0) {
      // 計画対象期間-ページングを"0 / 0"で表示にする
      localOneway.orX0165Plan.iconLabel = t('label.planning-period-no-manage')
      localOneway.orX0165Plan.iconLabelFontSize = '11px'
      localOneway.orX0165Plan.iconLabelColor = 'rgb(var(--v-theme-red-700))'
      localOneway.orX0165Plan.pageLabel = '0' + t('label.slash-with-space') + '0'
      local.planPageNo = 0
      local.planTotalCount = 0

      // planPeriodShow.value = false

      // 履歴、作成者、基準日、入力フームを非表示にする。
      historyShow.value = false
      authorShow.value = false
      baseDateShow.value = false
      inputBoomShow.value = false
      // 計画期間が登録されていない
      local.flag.periodManageRegistration = false
      setDelFlg(true)
    } else {
      setDelFlg(false)
      local.planPeriodId = planPeriodInfo.sc1Id
      local.planPageNo = Number(planPeriodInfo.kikanNo)
      local.planTotalCount = Number(planPeriodInfo.kikanCnt)

      localOneway.orX0165Plan.iconLabel =
        planPeriodInfo.startYmd + t('label.wavy') + planPeriodInfo.endYmd
      localOneway.orX0165Plan.iconLabelFontSize = undefined
      localOneway.orX0165Plan.iconLabelColor = undefined
      localOneway.orX0165Plan.pageLabel =
        planPeriodInfo.kikanNo + t('label.slash-with-space') + planPeriodInfo.kikanCnt

      // 履歴、作成者、基準日、入力フームを非表示にする。
      historyShow.value = true
      authorShow.value = true
      baseDateShow.value = true
      inputBoomShow.value = true
    }
  }
  // 期間管理フラグが「0:管理しない」
  else {
    // 計画対象期間 を非表示にする
    planPeriodShow.value = false
  }
  // }

  // 履歴を設定
  // 履歴が存在の場合
  if (local.flag.periodManageRegistration) {
    if (historyInfo && historyInfo.rirekiCnt !== '') {
      // 履歴総件数
      local.historyTotalCount = Number(historyInfo.rirekiCnt)

      historyShow.value = true
      local.historyId = Number(historyInfo.ass1Id)
      local.historyPageNo = Number(historyInfo.rirekiNo)
      local.kaiteiKbn = historyInfo.kaiteiKbn
      local.createDate.value = systemCommonsStore.getSystemDate!
      if (local.createDate.mo01343) {
        local.createDate.mo01343.value = systemCommonsStore.getSystemDate!
      }

      localOneway.orX0165History.pageLabel =
        historyInfo.rirekiNo + t('label.slash-with-space') + historyInfo.rirekiCnt

      // 作成者を設定
      local.sakuseiId = historyInfo.shokuId
      local.createUser.value = historyInfo.shokuKnj

      // 基準日を設定
      local.createDate.value = historyInfo.createYmd
    } else {
      // 基準日を設定
      local.createDate.value = systemCommonsStore.getSystemDate!

      // 作成者を設定
      // 作成者 ＝ ログイン情報.職員名
      local.sakuseiId = systemCommonsStore.getCurrentUser.chkShokuId ?? '0'
      local.createUser.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''

      // 履歴-ページング = "1 / 1"
      localOneway.orX0165History.pageLabel = '1' + t('label.slash-with-space') + '1'

      // 「AC004」の処理を行
      void AllSubAdd()
    }
  }
}

/**
 *  AC001_初期表示
 */
const init = () => {
  or30712OnewayType.parentId = String(tex0005.value.uniqueCpId)
  // 親画面データ設定
  localOneway.mo00043OnewayType.tabItems = [
    {
      id: TeX0005Const.DEFAULT.TAB_ONE,
      title: t('label.info-collection-tab-1'),
      tooltipText: t('label.info-collection-tab-1'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_TWO,
      title: t('label.info-collection-tab-2'),
      tooltipText: t('label.info-collection-tab-2'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_THREE,
      title: t('label.info-collection-tab-3'),
      tooltipText: t('label.info-collection-tab-3'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_FOUR,
      title: t('label.info-collection-tab-4'),
      tooltipText: t('label.info-collection-tab-4'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_FIVE,
      title: t('label.info-collection-tab-5'),
      tooltipText: t('label.info-collection-tab-5'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_SIX,
      title: t('label.info-collection-tab-6'),
      tooltipText: t('label.info-collection-tab-6'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_SEVEN,
      title: t('label.info-collection-tab-7'),
      tooltipText: t('label.info-collection-tab-7'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_EIGHT,
      title: t('label.info-collection-tab-8'),
      tooltipText: t('label.info-collection-tab-8'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_NINE,
      title: t('label.info-collection-tab-9'),
      tooltipText: t('label.info-collection-tab-9'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_TEN,
      title: t('label.info-collection-tab-10'),
      tooltipText: t('label.info-collection-tab-10'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_ELEVEN,
      title: t('label.info-collection-tab-11'),
      tooltipText: t('label.info-collection-tab-11'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_TWELVE,
      title: t('label.info-collection-tab-12'),
      tooltipText: t('label.info-collection-tab-12'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_THIRTEEN,
      title: t('label.info-collection-tab-13'),
      tooltipText: t('label.info-collection-tab-13'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_FOURTEEN,
      title: t('label.info-collection-tab-14'),
      tooltipText: t('label.info-collection-tab-14'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_FIFTEEN,
      title: t('label.info-collection-tab-15'),
      tooltipText: t('label.info-collection-tab-15'),
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0005Const.DEFAULT.TAB_SIXTEEN,
      title: t('label.info-collection-tab-16'),
      tooltipText: t('label.info-collection-tab-16'),
      tooltipLocation: 'bottom',
    },
  ]

  styleFlag.value = props.onewayModelValue.styleFlag
  void getTabsData('')
}

/**
 * AC002_「お気に入りアイコンボタン」押下
 */
const favoriteIconClick = () => {
  // TODO お気に入りに該当機能がない場合
  if (favorite.value) {
    // 「アセスメント(インターライ)」をお気に入り機能に追加する。
  }
  // お気に入りに該当機能がある場合
  else {
    // 「アセスメント(インターライ)」をお気に入り機能から削除する。
  }
}

/**
 * AC003_「保存ボタン」押下
 *
 * @param updateOnly - 強制更新かどうか
 */
const _update = async (updateOnly = false) => {
  // 期間管理フラグが「1:管理する」、且つ、期間情報.期間ページカウントが「0（期間なし）」の場合
  if (!local.flag.periodManageRegistration) {
    return
  }
  // 画面入力データに変更がない場合
  if (!isEdit.value && !updateOnly) {
    // 処理終了にする
    await openConfirmDialog(or21814.value.uniqueCpId, {
      dialogText: t('message.i-cmn-21800'),
    })
    return
  }

  // フラグ設定
  if (updateFlag.value !== TeX0005Const.UPD_FLAG_D) {
    if (local.historyId === 0) {
      updateFlag.value = TeX0005Const.UPD_FLAG_C
    } else {
      updateFlag.value = TeX0005Const.UPD_FLAG_U
    }
  }
  if (updateHisFlag.value !== TeX0005Const.UPD_FLAG_D) {
    if (local.historyId === 0) {
      updateHisFlag.value = TeX0005Const.UPD_FLAG_C
    } else {
      updateHisFlag.value = TeX0005Const.UPD_FLAG_U
    }
  }

  // データ保存
  await saveTabData(local.mo00043.id)

  if (!updateOnly) {
    if (updateHisFlag.value === TeX0005Const.UPD_FLAG_D) {
      local.historyId = 0
    }

    // 情報を再取得する
    // retrieveHistoryDataFlg.value = true
  }

  // 二回以上続けて新規ボタン押下したフラグをクリア
  local.addBtnState = false
}

/**
 * データ保存
 *
 * @param tabId -タブid
 */
const saveTabData = async (tabId: string) => {
  let sortList: Record<string, InfoCollectionInfoType[]> | undefined
  let infomation1List: InfoCollectionInfo[] | undefined
  let drugList: TakingMedication[] | undefined
  // 最新情報を取得する
  switch (tabId) {
    case TeX0005Const.DEFAULT.TAB_ONE:
      //［情報収集］画面（1）画面
      sortList = Or29513Logic.data.get(or29513.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_TWO:
      //［情報収集］画面（2）画面
      sortList = Or32409Logic.data.get(or32409.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_THREE:
      //［情報収集］画面（3）画面
      sortList = Or32430Logic.data.get(or32430.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_FOUR:
      //［情報収集］画面（4）画面
      sortList = Or33109Logic.data.get(or33109.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_FIVE:
      //［情報収集］画面（5）画面
      sortList = Or29203Logic.data.get(or29203.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_SIX:
      //［情報収集］画面（6）画面
      sortList = Or32410Logic.data.get(or32410.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_SEVEN:
      //［情報収集］画面（7）画面
      sortList = Or29516Logic.data.get(or29516.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_EIGHT:
      //［情報収集］画面（8）画面
      sortList = Or29510Logic.data.get(or29510.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_NINE:
      //［情報収集］画面（9）画面
      sortList = Or32415Logic.data.get(or32415.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_TEN:
      //［情報収集］画面（10）画面
      sortList = Or32418Logic.data.get(or32418.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_ELEVEN:
      //［情報収集］画面（11）画面
      sortList = Or29212Logic.data.get(or29212.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_TWELVE:
      //［情報収集］画面（12）画面
      sortList = Or35959Logic.data.get(or35959.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_THIRTEEN:
      //［情報収集］画面（13）画面
      sortList = Or55244Logic.data.get(or55244.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_FOURTEEN:
      //［情報収集］画面（14）画面
      break
    case TeX0005Const.DEFAULT.TAB_FIFTEEN:
      //［情報収集］画面（15）画面
      sortList = Or30695Logic.data.get(or30695.value.uniqueCpId)
      break
    case TeX0005Const.DEFAULT.TAB_SIXTEEN:
      //［情報収集］画面（16）画面
      drugList = Or29829Logic.data
        .get(or29829.value.uniqueCpId)
        ?.takingMedicationType.map((item) => {
          return {
            yakuzaiId: item.drugKnj.value,
            ryouKnj: item.ryouKnj.value,
            kounouKnj: item.kounouKnj.value,
            ass3Id: item.ass3Id,
            ass1Id: String(local.historyId ?? '0'),
            sort: item.sort,
            drugKnj: item.drugKnj.value,
            rowUpdFlg: item.rowUpdFlg,
          }
        })
      break
    default:
      break
  }

  // tab1~15
  if (sortList !== undefined) {
    const infoCollectionInfoList = Object.values(sortList).flat()
    infomation1List = infoCollectionInfoList.map((item) => {
      return {
        level1Knj: item.level1Knj,
        level2Knj: item.level2Knj,
        level3Knj: item.level3Knj,
        shosiki1Flg: item.shosiki1Flg,
        shosiki2Flg: item.shosiki2Flg,
        ass1Id: item.ass1Id,
        level1Id: item.level1Id,
        level2Id: item.level2Id,
        level3Id: item.level3Id,
        kentoFlg: item.kentoFlg,
        memo1Knj: item.memo1Knj.value,
        memo2Knj: item.memo2Knj.value,
        konnanLevel: item.konnanLevel.value,
        koumokuNo: item.koumokuNo,
        mstKbn: item.mstKbn,
        rowSpanFlg: false,
        modifiedCnt: item.modifiedCnt,
      }
    })
  }

  // バックエンドAPIを呼び出す
  const inputData: IInfoCollectionUpdateInEntity = {
    /** 種別ID */
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    /** 履歴更新区分 */
    historyUpdateKbn: updateHisFlag.value,
    /** ヘッダID */
    ass1Id: String(local.historyId),
    /** 期間ID */
    sc1Id: String(local.kikanInfo.sc1Id),
    /** 法人ID */
    houjinId: systemCommonsStore.getHoujinId ?? '1',
    /** 施設ID */
    shisetuId: systemCommonsStore.getShisetuId ?? '1',
    /** 事業者ID */
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
    /** 利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    /** マスタ区分(1:施設、2:居宅) */
    mstKbn: props.onewayModelValue.styleFlag,
    /** 作成日(基準日) */
    createYmd: local.createDate.value,
    /** 担当者ID(職員ID) */
    shokuId: local.sakuseiId,
    /** 改訂区分(0:初版、1:H31/1) */
    kaiteiKbn: local.kaiteiKbn,
    /** 第１階層ID */
    level1Id: local.mo00043.id,
    /** 更新回数 */
    modifiedCnt: local.rirekiInfo.modifiedCnt ?? '0',
    /** 更新区分 */
    updateKbn: updateFlag.value,
    /** 履歴番号 */
    rirekiNo: local.rirekiInfo.rirekiNo,
    /** 情報収集リスト */
    infomation1List: infomation1List,
    /** 服薬状況リスト */
    drugList: drugList,
  }
  // 情報収集画面の情報を取得する。
  const resData: IInfoCollectionUpdateOutEntity = await ScreenRepository.update(
    'infoCollectionUpdate',
    inputData
  )

  if (resData?.data !== null) {
    if (local.mo00043.id !== TeX0005Const.DEFAULT.TAB_SIXTEEN) {
      // ［情報収集］の情報を取得する。
      const infoCollectionInfoList = resData.data.infomation1List.map((item) => {
        return {
          level1Knj: item.level1Knj,
          level2Knj: item.level2Knj,
          level3Knj: item.level3Knj,
          shosiki1Flg: item.shosiki1Flg,
          shosiki2Flg: item.shosiki2Flg,
          ass1Id: item.ass1Id,
          level1Id: item.level1Id,
          level2Id: item.level2Id,
          level3Id: item.level3Id,
          kentoFlg: item.kentoFlg,
          memo1Knj: { value: item.memo1Knj },
          memo2Knj: { value: item.memo2Knj },
          konnanLevel: { value: item.konnanLevel },
          koumokuNo: item.koumokuNo,
          mstKbn: item.mstKbn,
          rowSpanFlg: false,
          modifiedCnt: item.modifiedCnt,
        }
      })

      // 書式2のデータをグループ化する
      const sortList = groupByLevel2Id(
        infoCollectionInfoList as unknown as InfoCollectionInfoType[],
        local.mo00043.id
      )
      // 履歴情報取得
      local.rirekiInfo = resData.data.rirekiList[0]

      // ヘーダ部情報設定
      setFormData(local.flag.periodManage, local.kikanInfo, local.rirekiInfo)

      // TAB1~15情報設定
      setTabsData(sortList, local.mo00043.id, local.rirekiInfo, local.flag.periodManage)
    } else {
      // ［情報収集］（服薬状況）の情報を取得する。
      const or29829Data = Or29829Logic.data.get(or29829.value.uniqueCpId)
      let takingMedicationData: Or29829Type = { takingMedicationType: [] }
      if (or29829Data) {
        takingMedicationData = { ...takingMedicationData }
      }

      takingMedicationData.takingMedicationType = resData.data.drugList?.map((item) => {
        return {
          yakuzaiId: item.yakuzaiId,
          drugKnj: { value: item.yakuzaiId },
          ryouKnj: { value: item.ryouKnj },
          kounouKnj: { value: item.kounouKnj },
          ass3Id: item.ass3Id,
          ass1Id: item.ass1Id,
          sort: item.sort,
          rowUpdFlg: TeX0005Const.UPD_FLAG_U,
        }
      })
      Or29829Logic.data.set({
        uniqueCpId: or29829.value.uniqueCpId,
        value: takingMedicationData,
        isInit: true,
      })
    }
  }
}

/**
 * AC004_「新規ボタン」押下
 */
const addBtnClick = async () => {
  // 二回目新規ボタン押下する場合
  if (local.addBtnState) {
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.attention'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.assessment')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  }

  // 共通情報.基準日
  local.createDate.value = systemCommonsStore.getSystemDate!
  if (local.createDate.mo01343) {
    local.createDate.mo01343.value = systemCommonsStore.getSystemDate!
  }

  local.addBtnState = true
  // 期間管理フラグが「1:管理する」
  if (local.flag.periodManage === '1') {
    // 計画期間情報.期間総件数 = 0（期間なし）
    if (local.historyTotalCount === 0) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.attention'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11300'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.ok'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (TeX0005Const.DIALOG_RESULT_YES === dialogResult) {
        OrX0115Logic.state.set({
          uniqueCpId: orX0115.value.uniqueCpId,
          state: { isOpen: true },
        })
      }
    }
  }

  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // AC003(保存処理)を実行し
        await _update()
        break
      case TeX0005Const.DIALOG_RESULT_CANCEL:
        // 処理終了
        return
    }
  }

  local.historyId = 0
  local.historyPageNo = Number(local.rirekiInfo.rirekiCnt) + 1
  local.historyTotalCount = Number(local.rirekiInfo.rirekiCnt) + 1

  localOneway.orX0165History.pageLabel =
    local.historyPageNo + t('label.slash-with-space') + local.historyTotalCount

  // ・履歴更新区分 = "C"
  updateHisFlag.value = TeX0005Const.UPD_FLAG_C

  // 新規操作
  AllSubAdd()
}

/**
 * サブ画面の新規操作
 */
const AllSubAdd = () => {
  // アセスメント(インターライ)画面「A ~ V」の新規情報を取得する。
  let sortList: Record<string, InfoCollectionInfoType[]> | undefined
  switch (local.mo00043.id) {
    case TeX0005Const.DEFAULT.TAB_ONE:
      //［情報収集］画面（1）画面
      sortList = Or29513Logic.data.get(or29513.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or29513Logic.data.set({
          uniqueCpId: or29513.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_TWO:
      //［情報収集］画面（2）画面
      sortList = Or32409Logic.data.get(or32409.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or32409Logic.data.set({
          uniqueCpId: or32409.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_THREE:
      //［情報収集］画面（3）画面
      sortList = Or32430Logic.data.get(or32430.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.konnanLevel.value = ''
            item.kentoFlg = ''
          })
        })

        Or32430Logic.data.set({
          uniqueCpId: or32430.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_FOUR:
      //［情報収集］画面（4）画面
      sortList = Or33109Logic.data.get(or33109.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or33109Logic.data.set({
          uniqueCpId: or33109.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_FIVE:
      //［情報収集］画面（5）画面
      sortList = Or29203Logic.data.get(or29203.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or29203Logic.data.set({
          uniqueCpId: or29203.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_SIX:
      //［情報収集］画面（6）画面
      sortList = Or32410Logic.data.get(or32410.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or32410Logic.data.set({
          uniqueCpId: or32410.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_SEVEN:
      //［情報収集］画面（7）画面
      sortList = Or29516Logic.data.get(or29516.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or29516Logic.data.set({
          uniqueCpId: or29516.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_EIGHT:
      //［情報収集］画面（8）画面
      sortList = Or29510Logic.data.get(or29510.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or29510Logic.data.set({
          uniqueCpId: or29510.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_NINE:
      //［情報収集］画面（9）画面
      sortList = Or32415Logic.data.get(or32415.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or32415Logic.data.set({
          uniqueCpId: or32415.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_TEN:
      //［情報収集］画面（10）画面
      sortList = Or32418Logic.data.get(or32418.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or32418Logic.data.set({
          uniqueCpId: or32418.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_ELEVEN:
      //［情報収集］画面（11）画面
      sortList = Or29212Logic.data.get(or29212.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or29212Logic.data.set({
          uniqueCpId: or29212.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_TWELVE:
      //［情報収集］画面（12）画面
      sortList = Or35959Logic.data.get(or35959.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.memo2Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or35959Logic.data.set({
          uniqueCpId: or35959.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_THIRTEEN:
      //［情報収集］画面（13）画面
      sortList = Or55244Logic.data.get(or55244.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or55244Logic.data.set({
          uniqueCpId: or55244.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_FOURTEEN:
      //［情報収集］画面（14）画面
      // TODO 本タブ担当実装コードを追加します
      break
    case TeX0005Const.DEFAULT.TAB_FIFTEEN:
      //［情報収集］画面（その他）画面
      sortList = Or30695Logic.data.get(or30695.value.uniqueCpId)
      if (sortList !== undefined) {
        Object.entries(sortList).map((group) => {
          group[1].map((item) => {
            item.memo1Knj.value = ''
            item.kentoFlg = ''
          })
        })

        Or30695Logic.data.set({
          uniqueCpId: or30695.value.uniqueCpId,
          value: { ...sortList },
          isInit: true,
        })
      }
      break
    case TeX0005Const.DEFAULT.TAB_SIXTEEN:
      //［情報収集］画面（服薬状況）画面
      break
    default:
      break
  }
}

/**
 * 画面履歴と最新情報を取得する
 *
 * @param syokiFlg -初期フラグ
 */
const getTabsData = async (syokiFlg: string) => {
  // バックエンドAPIから初期情報取得
  const inputData: IInfoCollectionImportInEntity = {
    /** 事業者ID */
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
    /** 施設ID */
    shisetuId: systemCommonsStore.getShisetuId ?? '1',
    /** 利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    /** 種別ID */
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    /** マスタ区分 */
    mstKbn: props.onewayModelValue.styleFlag,
    /** ヘッダID */
    ass1Id: String(local.historyId ?? '0'),
    /** 第1階層ID */
    level1Id: local.mo00043.id,
    /** 適用事業所ＩＤリスト */
    /** 事業所ID */
    jigyoList: systemCommonsStore.getSvJigyoIdList.slice(),
    /** 事業者グループ適用ID */
    jigyoGpId: systemCommonsStore.getTekiyouGroupId ?? '1',
    /** 改定区分 */
    kaiteiKbn: local.kaiteiKbn,
    /** 初期フラグ */
    syokiFlg: syokiFlg,
    /** 期間ID */
    sc1Id: local.planPeriodId ?? '0',
  }
  // 情報収集画面の情報を取得する。
  const resData: IInfoCollectionImportOutEntity = await ScreenRepository.select(
    'infoCollectionInitSelect',
    inputData
  )
  if (syokiFlg === '') {
    // バックエンドAPIから初期情報取得
    const resDataDrug: IInfoCollectionDrugSelectOutEntity = await ScreenRepository.select(
      'infoCollectionDrugSelect',
      {}
    )
    if (resDataDrug?.data !== null) {
      local.medicineList = resDataDrug.data.drugList.map((item) => {
        return {
          drugId: item.drugId,
          drugKnj: item.drugKnj,
          drugKana: item.drugKana,
        } as Medicine
      })
    }
  }

  if (resData?.data !== null) {
    local.kikanFlg = resData.data.kikanFlg
    if (local.mo00043.id !== TeX0005Const.DEFAULT.TAB_SIXTEEN) {
      // ［情報収集］の情報を取得する。
      const infoCollectionInfoList = resData.data.infomation1List.map((item) => {
        return {
          level1Knj: item.level1Knj,
          level2Knj: item.level2Knj,
          level3Knj: item.level3Knj,
          shosiki1Flg: item.shosiki1Flg,
          shosiki2Flg: item.shosiki2Flg,
          ass1Id: item.ass1Id,
          level1Id: item.level1Id,
          level2Id: item.level2Id,
          level3Id: item.level3Id,
          kentoFlg: item.kentoFlg,
          memo1Knj: { value: item.memo1Knj },
          memo2Knj: { value: item.memo2Knj },
          konnanLevel: { value: item.konnanLevel },
          koumokuNo: item.koumokuNo,
          mstKbn: item.mstKbn,
          rowSpanFlg: false,
          modifiedCnt: item.modifiedCnt,
        }
      })

      // 書式2のデータをグループ化する
      const sortList = groupByLevel2Id(
        infoCollectionInfoList as unknown as InfoCollectionInfoType[],
        local.mo00043.id
      )
      if (syokiFlg === '') {
        // 計画期間情報取得
        local.kikanInfo = resData.data.kikanList[0]
        // 履歴情報取得
        local.rirekiInfo = resData.data.rirekiList[0]
        // ヘーダ部情報設定
        setFormData(resData.data.kikanFlg, resData.data.kikanList[0], resData.data.rirekiList[0])
      }
      // TAB1~15情報設定
      setTabsData(sortList, local.mo00043.id, local.rirekiInfo, resData.data.kikanFlg)

      Or11871Logic.state.set({
        uniqueCpId: or11871.value.uniqueCpId,
        state: {
          screenTitleLabel: t('label.info-collection'),
          showFavorite: true,
          showViewSelect: false,
          viewSelectItems: [],
          showSaveBtn: true,
          showCreateBtn: true,
          showCreateMenuCopy: false,
          showPrintBtn: true,
          showMasterBtn: true,
          showOptionMenuBtn: true,
          showOptionMenuDelete: false,
        },
      })
    } else {
      Or11871Logic.state.set({
        uniqueCpId: or11871.value.uniqueCpId,
        state: {
          screenTitleLabel: t('label.info-collection'),
          showFavorite: true,
          showViewSelect: false,
          viewSelectItems: [],
          showSaveBtn: true,
          showCreateBtn: true,
          showCreateMenuCopy: false,
          showPrintBtn: true,
          showMasterBtn: false,
          showOptionMenuBtn: true,
          showOptionMenuDelete: false,
        },
      })
      // ［情報収集］（服薬状況）の情報を取得する。
      const or29829Data = Or29829Logic.data.get(or29829.value.uniqueCpId)
      let takingMedicationData: Or29829Type = { takingMedicationType: [] }
      if (or29829Data) {
        takingMedicationData = { ...takingMedicationData }
      }

      takingMedicationData.takingMedicationType = resData.data.drugList?.map((item) => {
        return {
          yakuzaiId: item.yakuzaiId,
          drugKnj: { value: item.yakuzaiId },
          ryouKnj: { value: item.ryouKnj },
          kounouKnj: { value: item.kounouKnj },
          ass3Id: item.ass3Id,
          ass1Id: item.ass1Id,
          sort: item.sort,
          rowUpdFlg: TeX0005Const.UPD_FLAG_U,
        }
      })

      Or29829Logic.data.set({
        uniqueCpId: or29829.value.uniqueCpId,
        value: takingMedicationData,
        isInit: true,
      })

      localOneway.or29829OnewayType.drugList = local.medicineList
    }
  }
  // 更新区分
  updateFlag.value = TeX0005Const.UPD_FLAG_NONE
  // 履歴更新区分
  updateHisFlag.value = TeX0005Const.UPD_FLAG_NONE
}

/**
 * 情報収集画面＿履歴変更後データ取得
 *
 * @param changeKubun -変更区分 prev:前へ or next:次へ or open:履歴選択アイコン or new:新規 or retrieve:再取得
 */
async function getHistoryChangeData(changeKubun: string) {
  // バックエンドAPIから初期情報取得
  const inputData: IInfoCollectionHistorySelectInEntity = {
    /** 計画期間 */
    sc1Id: String(local.kikanInfo.sc1Id),
    /** 利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    /** 事業者ID */
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    /** ヘッダID */
    ass1Id: String(local.historyId),
    /** マスタ区分 */
    mstKbn: props.onewayModelValue.styleFlag,
    /** 第1階層ID */
    level1Id: local.mo00043.id,
    /** 履歴処理区分 */
    rirekiKbn: changeKubun,
    /** 履歴番号 */
    rirekiNo: local.rirekiInfo.rirekiNo,
  }
  // 情報収集画面の情報を取得する。
  const resData: IInfoCollectionHistorySelectOutEntity = await ScreenRepository.select(
    'infoCollectionHistorySelect',
    inputData
  )

  if (resData?.data !== null) {
    if (local.mo00043.id !== TeX0005Const.DEFAULT.TAB_SIXTEEN) {
      // ［情報収集］の情報を取得する。
      const infoCollectionInfoList = resData.data.infomation1List.map((item) => {
        return {
          level1Knj: item.level1Knj,
          level2Knj: item.level2Knj,
          level3Knj: item.level3Knj,
          shosiki1Flg: item.shosiki1Flg,
          shosiki2Flg: item.shosiki2Flg,
          ass1Id: item.ass1Id,
          level1Id: item.level1Id,
          level2Id: item.level2Id,
          level3Id: item.level3Id,
          kentoFlg: item.kentoFlg,
          memo1Knj: { value: item.memo1Knj },
          memo2Knj: { value: item.memo2Knj },
          konnanLevel: { value: item.konnanLevel },
          koumokuNo: item.koumokuNo,
          mstKbn: item.mstKbn,
          rowSpanFlg: false,
          modifiedCnt: item.modifiedCnt,
        }
      })

      // 書式2のデータをグループ化する
      const sortList = groupByLevel2Id(
        infoCollectionInfoList as unknown as InfoCollectionInfoType[],
        local.mo00043.id
      )
      // 履歴情報取得
      local.rirekiInfo = resData.data.rirekiList[0]

      // ヘーダ部情報設定
      setFormData(local.flag.periodManage, local.kikanInfo, local.rirekiInfo)

      // TAB1~15情報設定
      setTabsData(sortList, local.mo00043.id, local.rirekiInfo, local.flag.periodManage)
    } else {
      // ［情報収集］（服薬状況）の情報を取得する。
      const or29829Data = Or29829Logic.data.get(or29829.value.uniqueCpId)
      let takingMedicationData: Or29829Type = { takingMedicationType: [] }
      if (or29829Data) {
        takingMedicationData = { ...takingMedicationData }
      }

      takingMedicationData.takingMedicationType = resData.data.drugList?.map((item) => {
        return {
          yakuzaiId: item.yakuzaiId,
          drugKnj: { value: item.yakuzaiId },
          ryouKnj: { value: item.ryouKnj },
          kounouKnj: { value: item.kounouKnj },
          ass3Id: item.ass3Id,
          ass1Id: item.ass1Id,
          sort: item.sort,
          rowUpdFlg: TeX0005Const.UPD_FLAG_U,
        }
      })

      Or29829Logic.data.set({
        uniqueCpId: or29829.value.uniqueCpId,
        value: takingMedicationData,
        isInit: true,
      })
    }
  }
}

/**
 * 情報収集画面＿計画期間変更後データ取得
 *
 * @param changeKubun -変更区分 prev:前へ or next:次へ or open:計画期間選択アイコン
 */
async function getPlanChangeData(changeKubun: string) {
  // バックエンドAPIから初期情報取得
  const inputData: IInfoCollectionPlanPeriodSelectInEntity = {
    /** 施設ID */
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    /** 計画期間 */
    sc1Id: String(local.rirekiInfo.sc1Id ?? ''),
    /** 種別ID */
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    /** 利用者ID */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    /** 事業者ID */
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    /** マスタ区分 */
    mstKbn: props.onewayModelValue.styleFlag,
    /** 第1階層ID */
    level1Id: local.mo00043.id,
    /** 履歴処理区分 */
    kikanSyoriKbn: changeKubun,
    /** 履歴処理区分 */
    ass1Id: String(local.historyId),
  }

  // 情報収集画面の情報を取得する。
  const resData: IInfoCollectionPlanPeriodSelectOutEntity = await ScreenRepository.select(
    'infoCollectionPlanPeriodSelect',
    inputData
  )

  if (resData?.data !== null) {
    if (local.mo00043.id !== TeX0005Const.DEFAULT.TAB_SIXTEEN) {
      // ［情報収集］の情報を取得する。
      const infoCollectionInfoList = resData.data.infomation1List.map((item) => {
        return {
          level1Knj: item.level1Knj,
          level2Knj: item.level2Knj,
          level3Knj: item.level3Knj,
          shosiki1Flg: item.shosiki1Flg,
          shosiki2Flg: item.shosiki2Flg,
          ass1Id: item.ass1Id,
          level1Id: item.level1Id,
          level2Id: item.level2Id,
          level3Id: item.level3Id,
          kentoFlg: item.kentoFlg,
          memo1Knj: { value: item.memo1Knj },
          memo2Knj: { value: item.memo2Knj },
          konnanLevel: { value: item.konnanLevel },
          koumokuNo: item.koumokuNo,
          mstKbn: item.mstKbn,
          rowSpanFlg: false,
          modifiedCnt: item.modifiedCnt,
        }
      })

      // 書式2のデータをグループ化する
      const sortList = groupByLevel2Id(
        infoCollectionInfoList as unknown as InfoCollectionInfoType[],
        local.mo00043.id
      )
      // 履歴情報取得
      local.rirekiInfo = resData.data.rirekiList[0]
      // 計画期間情報取得
      local.kikanInfo = resData.data.kikanList[0]
      // ヘーダ部情報設定
      setFormData(local.flag.periodManage, local.kikanInfo, local.rirekiInfo)
      // TAB1~15情報設定
      setTabsData(sortList, local.mo00043.id, local.rirekiInfo, local.flag.periodManage)
    } else {
      // ［情報収集］（服薬状況）の情報を取得する。
      const or29829Data = Or29829Logic.data.get(or29829.value.uniqueCpId)
      let takingMedicationData: Or29829Type = { takingMedicationType: [] }
      if (or29829Data) {
        takingMedicationData = { ...takingMedicationData }
      }

      takingMedicationData.takingMedicationType = resData.data.drugList?.map((item) => {
        return {
          yakuzaiId: item.yakuzaiId,
          drugKnj: { value: item.yakuzaiId },
          ryouKnj: { value: item.ryouKnj },
          kounouKnj: { value: item.kounouKnj },
          ass3Id: item.ass3Id,
          ass1Id: item.ass1Id,
          sort: item.sort,
          rowUpdFlg: TeX0005Const.UPD_FLAG_U,
        }
      })

      Or29829Logic.data.set({
        uniqueCpId: or29829.value.uniqueCpId,
        value: takingMedicationData,
        isInit: true,
      })
    }
  }
}

// 書式2のデータをグループ化する
function groupByLevel2Id(infoList: InfoCollectionInfoType[], tabId: string) {
  if (tabId === TeX0005Const.DEFAULT.TAB_TWO) {
    return infoList.reduce(
      (acc, current, index, array) => {
        const key = current.level2Id

        if (!acc[key]) {
          acc[key] = []
        }
        if (!current.ass1Id) {
          current.ass1Id = ''
        }
        // 現在のアイテムが条件を満たすかどうか
        const isMatch =
          current.shosiki1Flg === TeX0005Const.FORMAT_ONE &&
          current.shosiki2Flg === TeX0005Const.FORMAT_TWO

        let borderFlag = false

        // 条件を満たすアイテムのみを処理する
        if (isMatch) {
          const nextIndex = index + 1
          const nextItem = array[nextIndex]

          // 次の要素も条件を満たすかどうかを判断する
          if (
            nextItem &&
            nextItem.level2Id === key &&
            nextItem.shosiki1Flg === TeX0005Const.FORMAT_ONE &&
            nextItem.shosiki2Flg === TeX0005Const.FORMAT_TWO
          ) {
            // 次も条件を満たす場合は、現在のブロックの最後ではないことを示します
            borderFlag = true
          }
        }

        const itemWithFlag = {
          ...current,
          borderFlag,
        }

        acc[key].push(itemWithFlag)

        return acc
      },
      {} as Record<string, (InfoCollectionInfoType & { borderFlag?: boolean })[]>
    )
  } else {
    return infoList.reduce(
      (acc, current) => {
        const key = current.level2Id
        if (!acc[key]) {
          acc[key] = []
        }

        if (!current.ass1Id) {
          current.ass1Id = ''
        }

        acc[key].push(current)
        return acc
      },
      {} as Record<string, InfoCollectionInfoType[]>
    )
  }
}

/**
 * 利用者選択監視関数に渡すコールバック関数（サブ01の例）
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param _newSelfId - 利用者
 *
 * @param _oldSelfId - 利用者
 */
const callbackFuncSub01 = (_newSelfId: string, _oldSelfId: string) => {
  if (_newSelfId !== '') {
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
    void init()
  }
}
/**
 * 事業所選択更新の監視
 */
// ★事業所選択監視関数を実行
jigyoListWatch(or41179_1.value.uniqueCpId, callbackFuncJigyo)
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId !== '') {
    //画面データ変更かどうかを判断する
    //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
    // if (isEdit.value) {
    //   const dialogResult = await showConfirmMessgeBox()
    //   if (dialogResult?.firstBtnClickFlg === true) {
    //     await _save()
    //   } else if (dialogResult?.thirdBtnClickFlg === true) {
    //     return
    //   }
    // }
    //共通情報.事業所ＩＤ
    systemCommonsStore.setSvJigyoId(newJigyoId)
    //共通情報.計画期間ＩＤ = 空白
    local.planPeriodId = ''
    if (systemCommonsStore.getUserSelectSelfId() !== undefined && systemCommonsStore.getUserSelectSelfId() !== ''){
      void init()
    }
  }
}

/**
 * AC005_「複写ボタン」押下 TODO 複写画面待ち
 */
const copyBtnClick = () => {
  // 複写先アセスメントID
  or30712OnewayType.tabId = local.mo00043.id
  /** マスタ区分(1:施設、2:居宅) */
  or30712OnewayType.styleFlag = props.onewayModelValue.styleFlag
  /** ヘッダID */
  or30712OnewayType.ass1Id = String(local.rirekiInfo.ass1Id)
  /** 期間ID */
  or30712OnewayType.sc1Id = String(local.kikanInfo.sc1Id)
  /** 更新区分 */
  or30712OnewayType.updateFlag = updateFlag.value ?? TeX0005Const.UPD_FLAG_U
  if (updateFlag.value === '') {
    or30712OnewayType.updateFlag = TeX0005Const.UPD_FLAG_U
  }
  or30712OnewayType.parentId = String(tex0005.value.uniqueCpId)
  // 改訂区分(0:初版、1:H31/1)
  or30712OnewayType.kaiteiKbn = local.kaiteiKbn
  // Or30712のダイアログ開閉状態を更新する
  Or30712Logic.state.set({
    uniqueCpId: or30712.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC006_「印刷設定アイコンボタン」押下
 */
const printSettingIconClick = async () => {
  // 画面.履歴更新区分＝「D：削除」の場合
  if (updateFlag.value === TeX0005Const.UPD_FLAG_D) {
    return
  }
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // AC003(保存処理)を実行し
        await _update()
        break
      case TeX0005Const.DIALOG_RESULT_CANCEL:
        // 処理終了
        return
    }
  }

  let tantoId = TeX0005Const.DEFAULT.ZERO
  // 担当ケアマネ選択アイコンボタン非活性/活性設定
  const kkjTantoFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? Or28396Const.DEFAULT.STR.EMPTY
  // 共通情報.担当ケアマネ設定フラグ > 0、且つ、共通情報.担当者IDが0以外の場合
  if (
    systemCommonsStore.getManagerId !== Or28396Const.DEFAULT.STR.ZERO &&
    parseInt(kkjTantoFlg) > Or28396Const.DEFAULT.NUMBER.ZERO
  ) {
    // 非活性
    tantoId = systemCommonsStore.getManagerId ?? ''
  }

  let sectionName = ''
  if (styleFlag.value === TeX0005Const.STYLE_1) {
    sectionName = t('label.info-collection-sectionName-1')
  } else {
    sectionName = t('label.info-collection-sectionName-2')
  }

  // GUI00669 印刷設定画面をポップアップで起動する。
  // Or28396のダイアログ開閉状態を更新する
  Or28396Logic.state.set({
    uniqueCpId: or28396.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        sysRyaku: systemCommonsStore.getSystemAbbreviation,
        houjinId: systemCommonsStore.getHoujinId,
        sysCd: systemCommonsStore.getSystemCode,
        choIndex: local.mo00043.id,
        kikanFlg: local.kikanFlg,
        svJigyoId: systemCommonsStore.getSvJigyoId,
        shisetuId: systemCommonsStore.getShisetuId,
        tantoId: tantoId,
        shokuId: systemCommonsStore.getStaffId,
        sectionName: sectionName,
        userId: systemCommonsStore.getUserId,
      } as Or28396OnewayType,
    },
  })
}

/**
 * AC007_「マスタ他設定アイコンボタン」押下
 */
const masterIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // AC003(保存処理)を実行し
        await _update()
        break
      case TeX0005Const.DIALOG_RESULT_CANCEL:
        // 処理終了
        return
    }
  }

  // Or27302のダイアログ開閉状態を更新する
  Or27302Logic.state.set({
    uniqueCpId: or27302.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC009_「ログ」押下
 */
const logClick = () => {
  // TODO 共通処理 e-文書法対象機能の電子ファイル保存設定を取得する。
  // TODO 共通処理 e-文章法対象の帳票のXPSフォルダをエクスプローラで開く。
}

/**
 * AC011_「削除」押下
 */
const deleteClick = () => {
  // メッセージを表示 i.cmn.11348
  let tabName = ''
  switch (local.mo00043.id) {
    case TeX0005Const.DEFAULT.TAB_ONE:
      tabName = TeX0005Const.DEFAULT.TAB_ONE
      break
    case TeX0005Const.DEFAULT.TAB_TWO:
      tabName = TeX0005Const.DEFAULT.TAB_TWO
      break
    case TeX0005Const.DEFAULT.TAB_THREE:
      tabName = TeX0005Const.DEFAULT.TAB_THREE
      break
    case TeX0005Const.DEFAULT.TAB_FOUR:
      tabName = TeX0005Const.DEFAULT.TAB_FOUR
      break
    case TeX0005Const.DEFAULT.TAB_FIVE:
      tabName = TeX0005Const.DEFAULT.TAB_FIVE
      break
    case TeX0005Const.DEFAULT.TAB_SIX:
      tabName = TeX0005Const.DEFAULT.TAB_SIX
      break
    case TeX0005Const.DEFAULT.TAB_SEVEN:
      tabName = TeX0005Const.DEFAULT.TAB_SEVEN
      break
    case TeX0005Const.DEFAULT.TAB_EIGHT:
      tabName = TeX0005Const.DEFAULT.TAB_EIGHT
      break
    case TeX0005Const.DEFAULT.TAB_NINE:
      tabName = TeX0005Const.DEFAULT.TAB_NINE
      break
    case TeX0005Const.DEFAULT.TAB_TEN:
      tabName = TeX0005Const.DEFAULT.TAB_TEN
      break
    case TeX0005Const.DEFAULT.TAB_ELEVEN:
      tabName = TeX0005Const.DEFAULT.TAB_ELEVEN
      break
    case TeX0005Const.DEFAULT.TAB_TWELVE:
      tabName = TeX0005Const.DEFAULT.TAB_TWELVE
      break
    case TeX0005Const.DEFAULT.TAB_THIRTEEN:
      tabName = TeX0005Const.DEFAULT.TAB_THIRTEEN
      break
    case TeX0005Const.DEFAULT.TAB_FOURTEEN:
      tabName = TeX0005Const.DEFAULT.TAB_FOURTEEN
      break
    case TeX0005Const.DEFAULT.TAB_FIFTEEN:
      tabName = t('label.other-label')
      break
    case TeX0005Const.DEFAULT.TAB_SIXTEEN:
      tabName = t('label.status-of-medication')
      break
  }
  // メッセージ 11348
  // 現在表示している[{0}］の{1}データを削除します。よろしいですか？\r\n現在表示している画面のみ削除する。\r\n※{2}画面を削除します。\r\n表示している画面を履歴ごと削除する。\r\n※{3}までの全ての項目を削除します。
  const msg11348 = t('message.i-cmn-11348', [
    local.createDate.value,
    t('label.assessment'),
    tabName,
    t('label.info-collection-del-msg'),
  ])
  const msgList = msg11348.split(/[\r\n]+/)
  if (msgList.length > 2) {
    localOneway.orX0001Oneway.createYmd = local.createDate.value
    localOneway.orX0001Oneway.kinouKnj = t('label.assessment')
    localOneway.orX0001Oneway.selectTabName = tabName
    localOneway.orX0001Oneway.startTabName = t('label.info-collection-del-msg')
    localOneway.orX0001Oneway.endTabName = ''

    // OrX0001のダイアログ開閉状態を更新する
    OrX0001Logic.state.set({
      uniqueCpId: orX0001.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

watch(
  () => local.mo00043.id,
  async (newValue, oldValue) => {
    if (newValue === local.currentTabId) {
      return
    }

    const result = await tabsClick()
    if (result) {
      local.currentTabId = newValue
    } else {
      local.mo00043.id = oldValue
    }
  }
)

/**
 * AC022_タブ選択
 *
 */
const tabsClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // AC003(保存処理)を実行し
        await _update()
        break
      case TeX0005Const.DIALOG_RESULT_CANCEL:
        // 処理終了
        return false
    }
  }

  // 処理続き
  await getTabsData(TeX0005Const.UPD_FLAG_TABLE)

  return true
}

/**
 * AC021_「基準日カレンダ」押下
 *
 * @param dateValue - 基準日
 */
const changeCreateDate = (dateValue: Mo00020Type) => {
  // 力入れ検査 - 基盤作成

  // 親情報を設定
  commonInfo.baseDate = dateValue.value
}

/**
 * 「担当者選択アイコンボタン」押下
 */
const createUserBtnClick = () => {
  const svJigyoIdList: { svJigyoId: string }[] = []
  systemCommonsStore.getSvJigyoIdList.forEach((item) => {
    svJigyoIdList.push({ svJigyoId: item })
  })
  localOneway.or26257.svJigyoIdList = svJigyoIdList
  localOneway.or26257.kijunYmd = systemCommonsStore.getSystemDate ?? ''
  localOneway.or26257.defSvJigyoId = systemCommonsStore.getSvJigyoId ?? ''

  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 職員検索モーダル閉じる後の処理
 */
watch(
  () => local.or26257,
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    local.sakuseiId = newValue.shokuin.chkShokuId
    local.createUser.value = `${newValue.shokuin.shokuin1Knj ?? ''} ${newValue.shokuin.shokuin2Knj ?? ''}`
  }
)

/**
 * AC013_「計画対象期間選択アイコンボタン」押下
 */
const handlePlanEditClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // AC003(保存処理)を実行し
        await _update()
        break
      case TeX0005Const.DIALOG_RESULT_CANCEL:
        // 処理終了
        return
    }
  }

  localOneway.orX0115.kindId = systemCommonsStore.getSyubetu ?? ''
  localOneway.orX0115.sc1Id = local.planPeriodId

  // GUI00070 対象期間画面を開く
  OrX0115Logic.state.set({
    uniqueCpId: orX0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 対象期間画面閉じる後の処理
 */
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  (newValue) => {
    if (!newValue?.kikanId) {
      return
    }

    //「期間-選択確認前 アイコンボタン」押下
    if (local.planPeriodId === newValue.kikanId) {
      // 選択前の対象期間から変更がない場合
      return
    }

    local.planPeriodId = newValue.kikanId

    void getPlanChangeData(TeX0005Const.ACT_FLAG_NEXT)
  }
)

/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 */
const handlePlanPreClick = async () => {
  //「期間-前へ アイコンボタン」押下
  if (local.planPageNo === 1) {
    const result = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11262'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    switch (result) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // 処理終了
        return
    }
    return
  }

  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // AC003(保存処理)を実行し
        await _update()
        break
      case TeX0005Const.DIALOG_RESULT_CANCEL:
        // 処理終了
        return
    }
  }

  await getPlanChangeData(TeX0005Const.ACT_FLAG_PREV)

  // 二回以上続けて新規ボタン押下したフラグをクリア
  local.addBtnState = false
}

/**
 * AC015_「計画対象期間-次へアイコンボタン」押下
 */
const handlePlanPostClick = async () => {
  //「期間-次へ アイコンボタン」押下
  // 最終件目の計画対象期間データが表示されている状態
  if (local.planPageNo === local.planTotalCount) {
    const result = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11263'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    switch (result) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // 処理終了
        return
    }
    return
  }

  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // AC003(保存処理)を実行し
        await _update()
        break
      case TeX0005Const.DIALOG_RESULT_CANCEL:
        // 処理終了
        return
    }
  }

  await getPlanChangeData(TeX0005Const.ACT_FLAG_NEXT)

  // 二回以上続けて新規ボタン押下したフラグをクリア
  local.addBtnState = false
}

/**
 * AC016 「履歴選択アイコンボタン」押下
 */
const handleHistoryEditClick = () => {
  localOneway.or52099Oneway.sc1Id = local.planPeriodId
  localOneway.or52099Oneway.svJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.or52099Oneway.userid = systemCommonsStore.getUserSelectSelfId() ?? ''
  localOneway.or52099Oneway.mstKbn = props.onewayModelValue.styleFlag
  // GUI00792 ［履歴選択］画面 アセスメント(インターライ)をポップアップで起動
  // Or52099のダイアログ開閉状態を更新する
  Or52099Logic.state.set({
    uniqueCpId: or52099.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC017_「履歴-前へアイコンボタン」押下
 */
const handleHistoryPreClick = async () => {
  //「履歴-前へ アイコンボタン」押下
  if (local.historyPageNo <= 1) {
    // 1件目の履歴データが表示されている状態の場合
    return
  }
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // AC003(保存処理)を実行し
        await _update()
        break
      case TeX0005Const.DIALOG_RESULT_CANCEL:
        // 処理終了
        return
    }
  }
  //「履歴-前へ アイコンボタン」押下
  await getHistoryChangeData(TeX0005Const.ACT_FLAG_PREV)

  // 二回以上続けて新規ボタン押下したフラグをクリア
  local.addBtnState = false
}

/**
 * AC018_「履歴-次へアイコンボタン」押下
 */
const handleHistoryPostClick = async () => {
  //「履歴-次へ アイコンボタン」押下
  if (local.historyPageNo === local.historyTotalCount) {
    // 最終件目の履歴データが表示されている状態の場合
    return
  }
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case TeX0005Const.DIALOG_RESULT_YES:
        // AC003(保存処理)を実行し
        await _update()
        break
      case TeX0005Const.DIALOG_RESULT_CANCEL:
        // 処理終了
        return
    }
  }
  //「履歴-次へ アイコンボタン」押下
  await getHistoryChangeData(TeX0005Const.ACT_FLAG_NEXT)

  // 二回以上続けて新規ボタン押下したフラグをクリア
  local.addBtnState = false
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = TeX0005Const.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = TeX0005Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = TeX0005Const.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = TeX0005Const.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * AC016_選択前の履歴から変更
 *
 * @param selectItem - 選択の履歴
 */
const historySelectChange = async (selectItem: number | undefined) => {
  // 選択前の履歴から変更がない場合
  if (!selectItem) {
    // 処理終了にする。
    return
  }
  // 選択前の履歴から変更がある場合
  else {
    // アセスメント(インターライ)画面履歴変更処理
    local.historyId = selectItem

    await getHistoryChangeData(TeX0005Const.ACT_FLAG_OPEN)
  }
}

/**
 * 確認ボタン押下
 *
 * @param copyData - 複写
 */
async function onCpyClickOverwrite(copyData: CopyType) {
  await nextTick()
    if (local.mo00043.id !== TeX0005Const.DEFAULT.TAB_SIXTEEN) {
      // ［情報収集］の情報を取得する。
      const infoCollectionInfoList = copyData.infomationList

      // 書式2のデータをグループ化する
      const sortList = groupByLevel2Id(
        infoCollectionInfoList as unknown as InfoCollectionInfoType[],
        local.mo00043.id
      )

      // ヘーダ部情報設定
      setFormData(local.flag.periodManage, local.kikanInfo, local.rirekiInfo)

      // TAB1~15情報設定
      setTabsData(sortList, local.mo00043.id, local.rirekiInfo, local.flag.periodManage)
    } else {
      // ［情報収集］（服薬状況）の情報を取得する。
      const or29829Data = Or29829Logic.data.get(or29829.value.uniqueCpId)
      let takingMedicationData: Or29829Type = { takingMedicationType: [] }
      if (or29829Data) {
        takingMedicationData = { ...takingMedicationData }
      }
      takingMedicationData.takingMedicationType = copyData.takingMedicationType
      Or29829Logic.data.set({
        uniqueCpId: or29829.value.uniqueCpId,
        value: takingMedicationData,
        isInit: true,
      })
    }
}

// ダイアログ表示フラグ
const showDialogOr30712 = computed(() => {
  // Or30712のダイアログ開閉状態
  return Or30712Logic.state.get(or30712.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr28396 = computed(() => {
  // Or28396のダイアログ開閉状態
  return Or28396Logic.state.get(or28396.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOrX0115 = computed(() => {
  // OrX0115のダイアログ開閉状態
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 職員検索モーダルの表示状態を返すComputed
 */
const showDialogOr26257 = computed(() => {
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackFuncSub01)
</script>

<template>
  <c-v-sheet class="view">
    <c-v-row class="custom-header-bar pl-2 pr-4">
      <c-v-col
        cols="12"
        class="pa-0 d-flex align-center"
      >
        <g-base-or11871 v-bind="or11871">
          <template #createItems>
            <c-v-list-item
              :title="t('btn.copy')"
              @click="copyBtnClick"
            />
          </template>
          <template #optionMenuItems>
            <c-v-list-item
              v-if="electronicFileSaveSettingscategoryFlag"
              :title="
                t(
                  'label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-log'
                )
              "
              prepend-icon="open_in_browser"
              @click="logClick"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.care-plan2-show-log-btn')"
              />
            </c-v-list-item>
            <c-v-list-item
              :title="
                t(
                  'label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-delete'
                )
              "
              prepend-icon="delete"
              @click="deleteClick"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.delete-data')"
              />
            </c-v-list-item>
          </template>
        </g-base-or11871>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="main-Content d-flex flex-0-1 h-100 overflow-hidden"
    >
      <!-- ナビゲーションエリア -->
      <c-v-col
        cols="auto"
        class="h-100 pa-0 pl-6 pt-4"
      >
        <!-- 利用者選択一覧 -->
        <g-base-or00248
          v-bind="or00248"
          class="custom-user-list"
        />
      </c-v-col>
      <!-- コンテンツエリア -->
      <c-v-col class="main-right hidden-scroll h-100">
        <!-- 上段 -->
        <c-v-row class="px-6 flex-nowrap">
          <!-- 事業所 -->
          <c-v-col
            cols="auto"
            class="pa-0 pr-6 pt-4"
          >
            <g-base-or41179
              v-bind="or41179_1"
              class="custom-jigyo"
            />
          </c-v-col>
          <!-- 計画対象期間 -->
          <c-v-col
            v-if="planPeriodShow"
            cols="auto"
            class="pa-0 pr-6 pt-4"
          >
            <g-custom-or-x0165
              :oneway-model-value="localOneway.orX0165Plan"
              @on-click-edit-btn="handlePlanEditClick"
              @on-click-pre-btn="handlePlanPreClick"
              @on-click-post-btn="handlePlanPostClick"
            />
          </c-v-col>
          <!-- 基準日 -->
          <c-v-col
            v-if="baseDateShow"
            cols="auto"
            class="pa-0 pr-6 pt-4"
          >
            <base-mo00020
              :model-value="local.createDate"
              :oneway-model-value="localOneway.createDateOneway"
              class="custom-input"
              @update:model-value="changeCreateDate"
            />
          </c-v-col>
          <!-- 担当者 -->
          <c-v-col
            v-if="authorShow"
            cols="auto"
            class="pa-0 pr-6 pt-4"
          >
            <g-custom-or-x0157
              v-model="local.createUser"
              :oneway-model-value="localOneway.orX0157CreateUser"
              class="custom-input"
              @on-click-edit-btn="createUserBtnClick"
            />
          </c-v-col>
          <!-- 履歴 -->
          <c-v-col
            v-if="historyShow"
            cols="auto"
            class="pa-0 pr-6 pt-4"
          >
            <g-custom-or-x0165
              :oneway-model-value="localOneway.orX0165History"
              @on-click-edit-btn="handleHistoryEditClick"
              @on-click-pre-btn="handleHistoryPreClick"
              @on-click-post-btn="handleHistoryPostClick"
            />
          </c-v-col>
        </c-v-row>
        <!-- コンテンツエリアタブ -->
        <c-v-row class="pt-6">
          <c-v-col class="custom-tabs pa-0">
            <base-mo00043
              v-model="local.mo00043"
              :oneway-model-value="localOneway.mo00043OnewayType"
            >
            </base-mo00043>
          </c-v-col>
        </c-v-row>
        <!-- 中段 -->
        <c-v-row
          no-gutters
          class="middleContent flex-1-1 h-100"
        >
          <c-v-window
            id="tabWindow"
            v-model="local.currentTabId"
            class="h-100"
          >
            <c-v-window-item
              value="1"
              class="subWindow"
            >
              <g-custom-or29513
                v-bind="or29513"
                v-model="local.or29513"
                :oneway-model-value="localOneway.or29513OnewayType"
              ></g-custom-or29513>
            </c-v-window-item>
            <c-v-window-item
              value="2"
              class="subWindow"
            >
              <g-custom-or32409
                v-bind="or32409"
                v-model="local.or32409"
                :oneway-model-value="localOneway.or32409OnewayType"
              >
              </g-custom-or32409>
            </c-v-window-item>
            <c-v-window-item
              value="3"
              class="subWindow"
            >
              <g-custom-or32430
                v-bind="or32430"
                v-model="local.or32430"
                :oneway-model-value="localOneway.or32430OnewayType"
              >
              </g-custom-or32430>
            </c-v-window-item>
            <c-v-window-item
              value="4"
              class="subWindow"
            >
              <g-custom-or33109
                v-bind="or33109"
                v-model="local.or33109"
                :oneway-model-value="localOneway.or33109OnewayType"
              >
              </g-custom-or33109>
            </c-v-window-item>
            <c-v-window-item
              value="5"
              class="subWindow"
            >
              <g-custom-or29203
                v-bind="or29203"
                v-model="local.or29203"
                :oneway-model-value="localOneway.or29203OnewayType"
              >
              </g-custom-or29203>
            </c-v-window-item>
            <c-v-window-item
              value="6"
              class="subWindow"
            >
              <g-custom-or32410
                v-bind="or32410"
                v-model="local.or32410"
                :oneway-model-value="localOneway.or32410OnewayType"
              >
              </g-custom-or32410>
            </c-v-window-item>
            <c-v-window-item
              value="7"
              class="subWindow"
            >
              <g-custom-or29516
                v-bind="or29516"
                v-model="local.or29516"
                :oneway-model-value="localOneway.or29516OnewayType"
              >
              </g-custom-or29516>
            </c-v-window-item>
            <c-v-window-item
              value="8"
              class="subWindow"
            >
              <g-custom-or29510
                v-bind="or29510"
                v-model="local.or29510"
                :oneway-model-value="localOneway.or29510OnewayType"
              >
              </g-custom-or29510>
            </c-v-window-item>
            <c-v-window-item
              value="9"
              class="subWindow"
            >
              <g-custom-or32415
                v-bind="or32415"
                v-model="local.or32415"
                :oneway-model-value="localOneway.or32415OnewayType"
              >
              </g-custom-or32415>
            </c-v-window-item>
            <c-v-window-item
              value="10"
              class="subWindow"
            >
              <g-custom-or32418
                v-bind="or32418"
                v-model="local.or32418"
                :oneway-model-value="localOneway.or32418OnewayType"
              >
              </g-custom-or32418>
            </c-v-window-item>
            <c-v-window-item
              value="11"
              class="subWindow"
            >
              <g-custom-or29212
                v-bind="or29212"
                v-model="local.or29212"
                :oneway-model-value="localOneway.or29212OnewayType"
              >
              </g-custom-or29212>
            </c-v-window-item>
            <c-v-window-item
              value="12"
              class="subWindow"
            >
              <g-custom-or35959
                v-bind="or35959"
                v-model="local.or35959"
                :oneway-model-value="localOneway.or35959OnewayType"
              >
              </g-custom-or35959>
            </c-v-window-item>

            <c-v-window-item
              value="13"
              class="subWindow"
            >
              <g-custom-or55244
                v-bind="or55244"
                v-model="local.or55244"
                :oneway-model-value="localOneway.or55244OnewayType"
              >
              </g-custom-or55244>
            </c-v-window-item>
            <c-v-window-item
              value="14"
              class="subWindow"
            >
              <g-custom-or32427
                v-bind="or32427"
                v-model="local.or32427"
                :oneway-model-value="localOneway.or32427OnewayType"
              >
              </g-custom-or32427>
            </c-v-window-item>
            <c-v-window-item
              value="15"
              class="subWindow"
            >
              <g-custom-or30695
                v-bind="or30695"
                v-model="local.or30695"
                :oneway-model-value="localOneway.or30695OnewayType"
              >
              </g-custom-or30695>
            </c-v-window-item>
            <c-v-window-item
              value="16"
              class="subWindow"
            >
              <g-custom-or29829
                v-bind="or29829"
                :oneway-model-value="localOneway.or29829OnewayType"
              >
              </g-custom-or29829>
            </c-v-window-item>
          </c-v-window>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <!-- 下段 -->
  </c-v-sheet>
  <!-- メッセージ -->
  <g-base-or-21814
    v-if="showOr21814"
    v-bind="or21814"
  />

  <!-- GUI00671 薬剤マスタ -->
  <g-custom-or-27302
    v-if="showDialogOr27302"
    v-bind="or27302"
    :oneway-model-value="localOneway.or27302Oneway"
  />

  <!-- メッセージ「i-cmn-11260」 -->
  <!--  削除確認画面 -->
  <g-custom-or-x-0001
    v-if="showOrX0001"
    v-bind="orX0001"
    v-model="orX0001Type"
    :oneway-model-value="localOneway.orX0001Oneway"
  ></g-custom-or-x-0001>

  <!-- GUI00792 ［履歴選択］画面 -->
  <g-custom-or52099
    v-if="showDialogOr52099"
    v-bind="or52099"
    v-model="local.or52099"
    :oneway-model-value="localOneway.or52099Oneway"
    @update:model-value="historySelectChange"
  />
  <g-custom-or-28396
    v-if="showDialogOr28396"
    v-bind="or28396"
  />

  <g-custom-or-30712
    v-if="showDialogOr30712"
    v-bind="or30712"
    :oneway-model-value="or30712OnewayType"
    @on-click-confirm="onCpyClickOverwrite"
  />
  <!-- Or26257:有機体:職員検索モーダル -->
  <g-custom-or26257
    v-if="showDialogOr26257"
    v-bind="or26257"
    v-model="local.or26257"
    :oneway-model-value="localOneway.or26257"
  />
  <!-- GUI00070 対象期間画面 -->
  <g-custom-or-x0115
    v-if="showDialogOrX0115"
    v-bind="orX0115"
    :oneway-model-value="localOneway.orX0115"
  />
</template>

<style scoped lang="scss">
.view {
  display: flex;
  flex-direction: column;
  background: rgb(var(--v-theme-background));

  .custom-header-bar {
    position: sticky;
    top: 48px;
    background: rgb(var(--v-theme-background));
    z-index: 999;
    margin: 0px;
    min-height: 60px;

    :deep(> div > div > div:last-of-type) {
      display: flex;
      align-items: center;
    }

    :deep(.v-btn) {
      --v-btn-height: 32px;
      min-height: 32px !important;
    }

    :deep(.v-divider) {
      margin-top: 0px !important;
      margin-bottom: 0px !important;
      color: rgb(180, 197, 220);
      caret-color: rgb(180, 197, 220);
      --v-border-opacity: 1;
    }
  }
}

.main-Content {
  z-index: 0;
  padding-bottom: 94px;
  .custom-user-list {
    width: auto !important;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: unset;

    :deep(.v-row) {
      margin: 0px;
    }

    :deep(> br) {
      display: none;
    }

    // 利用者情報セクション
    :deep(.user-info-section-container-style) {
      // 画像+利用者名
      > div:first-of-type {
        padding: 0px !important;
        padding-left: 8px !important;
        padding-top: 8px !important;
        gap: 12px;

        .user-name-kana-style,
        .user-name-kanji-style {
          min-width: auto;
        }

        // 画像
        > div:first-of-type {
          padding: 0px !important;
          flex: 0 0 auto;
          width: auto;
          max-width: auto;
        }

        // 利用者名
        > div:last-of-type {
          padding: 0px !important;
          flex: 0 0 auto;
          width: auto;
          max-width: none;

          > div:first-of-type > div:first-of-type {
            display: none;
          }
        }
      }

      // 性別+要介護+ID
      > div:last-of-type {
        padding: 0px !important;
        padding-left: 8px !important;
        padding-right: 8px !important;
        padding-top: 18px !important;
        padding-bottom: 12px !important;
        width: auto !important;
        height: auto !important;
        gap: 8px;

        > div {
          padding: 0px !important;
          flex: 0 0 auto;
          width: auto;
        }
        // 性別
        > div:first-of-type {
          min-width: 71px;
        }
        // 要介護
        > div:nth-of-type(2) {
          min-width: 47px;
        }
        // ID
        > div:last-of-type {
          flex: 1;
          display: flex;
          justify-content: end;
        }
      }
    }

    // 利用者リスト
    :deep(> div:last-of-type) {
      padding: 0px;
      padding-top: 12px;
      width: auto !important;
      flex: 1 1 0%;

      > div:first-of-type {
        height: 100%;

        > div {
          padding: 0px !important;
          flex: 0 0 auto;
          width: auto;
          max-width: 100%;
        }

        > div:first-of-type {
          position: relative;
          z-index: 1;
        }

        > div:last-of-type {
          height: 100%;
          overflow-y: auto;

          > div:first-of-type {
            transform: unset !important;
          }
        }
      }
    }
  }

  .main-right {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.v-row) {
      margin: 0px;
    }

    .custom-jigyo {
      flex-direction: column;

      :deep(> div:first-child) {
        margin: 0px !important;
        padding: 0px !important;
        margin-bottom: 4px !important;
      }

      :deep(> div:last-child > div) {
        margin: 0px !important;
      }
    }

    .v-sheet.custom-input {
      background-color: rgb(var(--v-theme-background));
    }

    .custom-input {
      :deep(.v-input__control) {
        background-color: rgb(var(--v-theme-secondaryBackground));
      }
    }

    :deep(.custom-edit-btn) {
      background-color: #ebf2fd !important;
    }

    .custom-tabs {
      min-height: 36px;

      :deep(.v-slide-group__content) {
        gap: 24px;
      }

      :deep(.tabs) {
        height: 35px;
        border-bottom: thin solid #b4c5dc;
        padding-left: 24px !important;
        padding-right: 24px !important;

        .v-tab.v-tab.v-btn {
          height: 35px;
        }
      }
    }

    .middleContent {
      .v-window {
        width: 100%;
      }

      flex: 1 1 auto;
      overflow-y: hidden;
    }

    .footer {
      flex: 0 1 auto;
    }
  }
}

.subWindow {
  margin-left: 24px;
  margin-top: 8px;
  margin-right: 296px;
}
</style>
