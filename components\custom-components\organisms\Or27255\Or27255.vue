<script setup lang="ts">
/**
 * Or27255:有機体:モーダル（画面/特殊コンポーネント）
 * GUI00959_［表示順変更：実施計画～②(課題)］画面
 *
 * @description
 * 実施計画書～②（課題と目標）表示順変更モーダル
 *
 * <AUTHOR>
 */
import { onMounted, reactive, watch, ref, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { isNumber } from 'lodash'
import { Or27255Const } from './Or27255.constants'

import type { Or27255StateType, TableData } from './Or27255.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Title, Or27255Type } from '~/types/cmn/business/components/Or27255Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo01278Type } from '~/types/business/components/Mo01278Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Mo01336OnewayType } from '~/types/business/components/Mo01336Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or27255Type
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const localOneway = reactive({
  // 情報ダイアログ
  mo00024Oneway: {
    width: 'auto',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27255',
      toolbarTitle: t('label.display-order-modified-plan2-issue'),
      toolbarName: 'Or27255ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo01265OnewayModelValue: {
    btnLabel: t('btn.delete-display-order'),
  } as Mo01265OnewayType,
  mo00043OneWay: {
    tabItems: [
      { id: 'change', title: t('label.display-order-modified') },
      { id: 'move', title: t('label.display-order-move') },
    ],
  } as Mo00043OnewayType,
  // はいボタン
  mo00609Oneway: {
    btnLabel: t('btn.yes'),
  } as Mo00609OnewayType,
  // いいえボタン
  closeMo00609Oneway: {
    btnLabel: t('btn.no'),
  } as Mo00611OnewayType,
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
})

const local = reactive({
  or27255: {
    ...props.modelValue,
  },
  mo00043: { id: 'change' } as Mo00043Type,
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27255Const.DEFAULT.IS_OPEN,
})

const or21814UniqueCpId = ''
const or21814 = ref({ uniqueCpId: or21814UniqueCpId })
// 選択した行のindex
const selectedIndex = ref<number>(-1)
// 選択した行のindex
const selectedIndex1 = ref<number>(-1)
// 表示順変更テーブルヘッダ
const changeTableHeaders = [
  { title: t('label.display-order'), key: 'sort', align: 'start', width: '80px', sortable: false },
  {
    title: t('label.issues-number'),
    key: 'issuesNumber',
    align: 'start',
    width: '80px',
    sortable: false,
  },
  {
    title: t('label.life-whole-solution-issues-needs2'),
    key: 'issueNeeds',
    align: 'start',
    width: '260px',
    sortable: false,
  },
  {
    title: t('label.long-term-goal'),
    key: 'longTermGoal',
    align: 'start',
    width: '210px',
    sortable: false,
  },
  {
    title: t('label.achievement-period'),
    key: 'achievementPeriod',
    align: 'start',
    width: '100px',
    sortable: false,
  },
]
// 表示順移動テーブルヘッダ
const moveTableHeaders = [
  { title: '', key: 'action', align: 'start', sortable: false },
  { title: t('label.display-order'), key: 'sort', width: '80px', align: 'start', sortable: false },
  {
    title: t('label.issues-number'),
    key: 'issuesNumber',
    align: 'start',
    width: '80px',
    sortable: false,
  },
  {
    title: t('label.life-whole-solution-issues-needs2'),
    key: 'lifeWholeSolutionIssuesNeeds',
    align: 'start',
    width: '280px',
    sortable: false,
  },
  {
    title: t('label.long-term-goal'),
    key: 'longTermGoal',
    align: 'start',
    width: '210px',
    sortable: false,
  },
  {
    title: t('label.achievement-period'),
    key: 'achievementPeriod',
    align: 'start',
    width: '100px',
    sortable: false,
  },
]

// 表示順変更テーブル情報
const tableData = ref<TableData[]>([])

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27255StateType>({
  cpId: Or27255Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27255Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(() => {
  init()
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

//メッセージの選択結果を監視
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.secondBtnClickFlg) {
      // 注意ダイアログの確認ボタン押下時
      await nextTick()
      confirmOk()
    } else if (newValue.thirdBtnClickFlg) {
      // 注意ダイアログの閉じるボタン押下時
      confirmCancel()
    } else {
      return
    }
  }
)

watch(
  () => local.mo00043.id,
  (newValue) => {
    if (newValue === 'move' || newValue === 'change') {
      tabsClick()
    }
  }
)

/**
 * AC001_初期情報取得
 */
function init() {
  // 「表示順変更」タブ初期表示
  // 取得した課題一覧データを課題一覧に設定する。
  if (local.or27255.sortList && local.or27255.sortList.length > 0) {
    const tempArray = local.or27255.sortList
    for (const data of tempArray) {
      const item = {
        action: {
          onewayModelValue: {
            icon: 'drag_indicator',
            color: 'grey',
          },
        } as Mo00009OnewayType,
        sort: {
          modelValue: {
            value: data.sort + '',
          } as Mo01278Type,
          onewayModelValue: undefined,
        },
        issuesNumber: {
          onewayModelValue: {
            value: data.issuesNumber,
            unit: '',
          } as Mo01336OnewayType,
        },
        lifeWholeSolutionIssuesNeeds: {
          onewayModelValue: {
            value: data.lifeWholeSolutionIssuesNeeds,
            unit: '',
          } as Mo01337OnewayType,
        },
        longTermGoal: {
          onewayModelValue: {
            value: data.longTermGoal,
            unit: '',
          } as Mo01337OnewayType,
        },
        achievementPeriod: {
          onewayModelValue: {
            value: data.achievementPeriod,
            unit: '',
          } as Mo01337OnewayType,
        },
        copy: data.sort + '',
      } as TableData
      tableData.value.push(item)
    }
  }

  // AC003_「表示順変更」押下、AC004_「表示順移動」押下--ページレイアウトコード構造はこの機能を満たしており、余計なコード作業を必要としません
}

/**
 * AC005_「表示順位削除」押下
 */
function sortDeleteClick() {
  // 表示順リスト表示順リスト内の順序列の内容をクリアする
  // リストのレコードを取得し、ループを使用してレコードの順序に基づいてソートする
  if (tableData.value && tableData.value.length > 0) {
    for (const item of tableData.value) {
      item.sort.modelValue.value = ''
    }
  }
}

const dragState = {
  // 開始索引
  start: -1,
  // 移動時に上書きされるインデックス
  end: -1,
  // 移動中ですか
  dragging: false,
  // 移動方向
  direction: '',
  // 上の浮遊する行
  lastSort: -1,
}

const isFocus = ref<boolean>(false)

const onFocusEvent = (item: TableData) => {
  isFocus.value = true
  const num = deleteComma(item.sort.modelValue.value)
  item.sort.modelValue.value = num
}

const onBlurEvent = (item: TableData) => {
  isFocus.value = false
  let num = removeNonNumeic(item.sort.modelValue.value)
  num = toMinMaxRange(num)
  item.sort.modelValue.value = num
}

/**
 * カンマ削除
 *
 * @param val - 値
 */
const deleteComma = (val: string) => {
  return val ? String(val).replace(/,/g, '') : ''
}

/**
 * 数値以外を削除
 *
 * @param val - 値
 */
const removeNonNumeic = (val: string) => {
  if (val === null || val === undefined || val === '') {
    return val
  }

  // 全角長音（ー――－ー）、小数点（．）、全角数字を半角に変換
  let convert = val.replace(/[ー――－ー]/g, '-').replace(/[０-９．]/g, (s) => {
    return String.fromCharCode(s.charCodeAt(0) - 0xfee0)
  })

  //  先頭が「-」であるか
  const startsWithMinus = convert.startsWith('-')

  // 半角数字、小数点以外を削除
  convert = convert.replace(/[^\d.]+/g, '')

  // 先頭が「-」の場合は、最終的に先頭に「-」を追加
  if (startsWithMinus) {
    convert = `-${convert}`
  }

  //最初の小数点以降の全ての小数点を削除する
  const firstDotIndex = convert.indexOf('.')
  if (firstDotIndex !== -1) {
    convert =
      convert.slice(0, firstDotIndex + 1) + convert.slice(firstDotIndex + 1).replace(/\./g, '')
  }

  // 文字列に数値変換できない値があった場合は何もしない
  const valTypeNum = Number(convert)
  if (isNaN(valTypeNum)) {
    return val
  }

  return valTypeNum.toString()
}

/**
 * 最小、最大の範囲内にする
 *
 * @param val - 値
 */
const toMinMaxRange = (val: string) => {
  if (val === null || val === undefined || val === '') {
    return val
  }

  // 文字列に数値変換できない値があった場合は何もしない
  const valTypeNum = Number(val)
  if (isNaN(valTypeNum)) {
    return val
  }

  return valTypeNum.toString()
}

/**
 * AC006_「空白ラベル」ドラッグする_1
 *
 * @param e - $event
 *
 * @param sort - 表示順
 */
function sortBtnMousedown(e: MouseEvent, sort: Mo01278Type) {
  // 選択するとソートが成功します
  // 選択しない場合は記録位置をロールバックします
  dragState.dragging = true
  dragState.start = parseInt(sort.value)

  const htmls = document.getElementsByClassName('suspension' + sort.value)
  if (htmls) {
    for (const item of htmls) {
      if (item) {
        const html = item.parentElement?.parentElement
        if (html) {
          html.style.background = '#f2f2f2'
        }
      }
    }
  }
}

/**
 * AC006_「空白ラベル」ドラッグする_2
 *
 * @param sort - 表示順
 */
function sortBtnMouseup(sort: Mo01278Type) {
  if (
    dragState.start === dragState.end ||
    dragState.start === parseInt(sort.value) ||
    -1 === parseInt(sort.value)
  ) {
    if (-1 !== parseInt(sort.value)) {
      cssCallBack(dragState.start)
    }
    return
  }
  dragState.end = parseInt(sort.value)

  showOr21814Msg(t('message.i-cmn-10678', [dragState.start, dragState.end]))
}

/**
 * AC007_「表示順」マウスダウン
 *
 * @param item - インデックス
 */
function mousedownForSort(item: TableData) {
  let allEmpty = true
  let maxSort = 0

  if (item.sort.modelValue.value !== '') {
    return
  }

  for (const item of tableData.value) {
    if (item.sort.modelValue.value !== '' && parseInt(item.sort.modelValue.value) > maxSort) {
      maxSort = parseInt(item.sort.modelValue.value)
      allEmpty = false
    }
  }

  if (allEmpty) {
    item.sort.modelValue.value = 1 + ''
  } else {
    item.sort.modelValue.value = maxSort + 1 + ''
  }
}

/**
 * 確認メッセージを表示する
 *
 * @param confirmMsg - エラー内容
 */
function showOr21814Msg(confirmMsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: confirmMsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * AC006_「空白ラベル」ドラッグする_3
 *
 * @param e - $event
 *
 * @param sort - 表示順
 */
function sortBtnMousemove(e: MouseEvent, sort: Mo01278Type) {
  if (
    dragState.dragging &&
    parseInt(sort.value) !== dragState.lastSort &&
    parseInt(sort.value) !== dragState.start
  ) {
    if (-1 !== dragState.lastSort) {
      const lastHtmls = document.getElementsByClassName('suspension' + dragState.lastSort)
      if (lastHtmls) {
        for (const item of lastHtmls) {
          if (item) {
            const html = item.parentElement?.parentElement
            if (html) {
              html.style.background = ''
            }
          }
        }
      }
    }
    const htmls = document.getElementsByClassName('suspension' + sort.value)
    if (htmls) {
      for (const item of htmls) {
        if (item) {
          const html = item.parentElement?.parentElement
          if (html) {
            html.style.background = '#0760e652'
            dragState.lastSort = parseInt(sort.value)
          }
        }
      }
    }
  }
}

/**
 * AC006_「空白ラベル」ドラッグする_4
 *
 */
function tdMousemove() {
  if (dragState.dragging) {
    if (-1 === dragState.end) {
      const lastSort = dragState.lastSort
      cssCallBack(dragState.start)
      if (-1 !== lastSort) {
        cssCallBack(lastSort)
      }
    }
  }
}

/**
 * AC002_「×ボタン」押下
 * AC007_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * AC008_「確定ボタン」押下
 * 「確定」ボタン押下
 */
function confirm() {
  // 表示されているタブ画面の表示順欄の値で課題データ情報の内容をソートする
  // ※表示順欄に値のない行は順次に最後に移動する
  const respData: Or27255Type = {
    sortList: [],
  }

  if (tableData.value && tableData.value.length > 0) {
    const tempList = new Array<Title>()
    for (const item of tableData.value) {
      const data: Title = {
        sort: parseInt(item.sort.modelValue.value),
        issuesNumber: item.issuesNumber.onewayModelValue.value,
        lifeWholeSolutionIssuesNeeds: item.lifeWholeSolutionIssuesNeeds.onewayModelValue.value,
        longTermGoal: item.longTermGoal.onewayModelValue.value,
        achievementPeriod: item.achievementPeriod.onewayModelValue.value,
        copy: item.copy,
      }
      tempList.push(data)
    }
    tempList.sort(
      (a, b) =>
        (isNumber(a.sort) ? a.sort : parseInt(a.sort)) -
        (isNumber(b.sort) ? b.sort : parseInt(b.sort))
    )
    respData.sortList = tempList
  }
  // 返却情報.課題データ情報 = ソート後の課題データ情報の内容
  emit('update:modelValue', respData)
  // 本画面を閉じ、親画面に返却する。
  close()
}

/**
 * 注意ダイアログの確認ボタン押下時
 */
function confirmOk() {
  const startIndex = dragState.start - 1
  const endIndex = dragState.end - 1
  const moveRow = tableData.value.splice(startIndex, 1)[0]
  tableData.value.splice(endIndex, 0, moveRow)
  tableData.value.forEach((item, index) => {
    item.sort.modelValue.value = index + 1 + ''
  })

  cssCallBack(dragState.start)
}

/**
 * 注意ダイアログの閉じるボタン押下時
 */
function confirmCancel() {
  cssCallBack(dragState.start)
}

/**
 * 元素パターン回復
 *
 * @param sort - 表示順
 */
function cssCallBack(sort: number) {
  const element = document.querySelector('.suspension' + sort)
  if (element) {
    const html = element.parentElement?.parentElement
    if (html) {
      html.style.background = 'transparent'
    }
  }

  const lastElement = document.querySelector('.suspension' + dragState.lastSort)
  if (lastElement) {
    const html = lastElement.parentElement?.parentElement
    if (html) {
      html.style.background = 'transparent'
    }
  }

  dragState.start = -1
  dragState.end = -1
  dragState.dragging = false
  dragState.direction = ''
  dragState.lastSort = -1
}

/**
 * タブ切り替え--表示順な回復を示
 */
function tabsClick() {
  for (const item of tableData.value) {
    if (item && item.sort.modelValue.value === '') {
      item.sort.modelValue.value = item.copy
    }
  }

  tableData.value.sort(
    (a, b) => parseInt(a.sort.modelValue.value) - parseInt(b.sort.modelValue.value)
  )

  tableData.value.forEach((item, index) => {
    item.sort.modelValue.value = (index + 1).toString()
  })
}
function selectClick(index: number) {
  selectedIndex.value = index
}
function selectClick1(index: number) {
  selectedIndex1.value = index
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043"
        :oneway-model-value="localOneway.mo00043OneWay"
      >
      </base-mo00043>
      <!-- タブ switch -->
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item value="change">
          <!-- タブ：表示順変更 -->
          <c-v-card class="card-padding">
            <template #title>
              <base-mo01265
                :oneway-model-value="localOneway.mo01265OnewayModelValue"
                color="#ff0000"
                label-color="#ff0000"
                @click="sortDeleteClick"
              >
              </base-mo01265>
            </template>
            <c-v-card-text>
              <c-v-row>
                <c-v-col>
                  <c-v-data-table
                    fixed-header
                    :headers="changeTableHeaders"
                    class="table-wrapper"
                    :items="tableData"
                    hide-default-footer
                    :items-per-page="-1"
                    style="width: 760px; height: 300px"
                    hover
                  >
                    <template #item="{ item,index }">
                      <tr
                        :class="{ 'select-row': selectedIndex === index }"
                        @click="selectClick(index)">
                        <td @mousedown="mousedownForSort(item)">
                          <input
                            v-model="item.sort.modelValue.value"
                            :class="'right-input full-width-field txt'"
                            maxlength="3"
                            @focus="onFocusEvent(item)"
                            @blur="onBlurEvent(item)"
                          />
                        </td>
                        <td>
                          <base-mo01336 :oneway-model-value="item.issuesNumber.onewayModelValue">
                          </base-mo01336>
                        </td>
                        <td>
                          <base-mo01337
                            :oneway-model-value="item.lifeWholeSolutionIssuesNeeds.onewayModelValue"
                          >
                          </base-mo01337>
                        </td>
                        <td>
                          <base-mo01337 :oneway-model-value="item.longTermGoal.onewayModelValue">
                          </base-mo01337>
                        </td>
                        <td>
                          <base-mo01337
                            :oneway-model-value="item.achievementPeriod.onewayModelValue"
                          >
                          </base-mo01337>
                        </td>
                      </tr>
                    </template>
                  </c-v-data-table>
                </c-v-col>
              </c-v-row>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
        <c-v-window-item value="move">
          <!-- タブ：表示順移動 -->
          <c-v-card @mouseup="sortBtnMouseup({ value: '-1' } as Mo01278Type)">
            <c-v-card-text id="customCard" class="card-padding">
              <c-v-row>
                <c-v-col>
                  <c-v-data-table
                    fixed-header
                    class="table-wrapper"
                    :headers="moveTableHeaders"
                    :items="tableData"
                    hide-default-footer
                    :items-per-page="-1"
                    style="width: 820px; height: 300px"
                    hover
                  >
                    <template #item="{ item, index }">
                      <tr
                        :class="{ 'select-row': selectedIndex1 === index }"
                        @click="selectClick1(index)"
                        >
                        <td
                          @mousedown="sortBtnMousedown($event, item.sort.modelValue)"
                          @mouseup="sortBtnMouseup(item.sort.modelValue)"
                          @mousemove="sortBtnMousemove($event, item.sort.modelValue)"
                        >
                          <base-mo00009
                            icon="mdi-plus"
                            :oneway-model-value="item.action.onewayModelValue"
                            :class="'suspension' + item.sort.modelValue.value"
                          >
                            <v-icon icon="drag_indicator"></v-icon>
                          </base-mo00009>
                        </td>
                        <td @mousemove="tdMousemove">
                          <input
                            v-model="item.sort.modelValue.value"
                            :class="'right-input full-width-field txt'"
                            :disabled="true"
                          />
                        </td>
                        <td @mousemove="tdMousemove">
                          <base-mo01336 :oneway-model-value="item.issuesNumber.onewayModelValue">
                          </base-mo01336>
                        </td>
                        <td @mousemove="tdMousemove">
                          <base-mo01337
                            :oneway-model-value="item.lifeWholeSolutionIssuesNeeds.onewayModelValue"
                          >
                          </base-mo01337>
                        </td>
                        <td @mousemove="tdMousemove">
                          <base-mo01337 :oneway-model-value="item.longTermGoal.onewayModelValue">
                          </base-mo01337>
                        </td>
                        <td @mousemove="tdMousemove">
                          <base-mo01337
                            :oneway-model-value="item.achievementPeriod.onewayModelValue"
                          >
                          </base-mo01337>
                        </td>
                      </tr>
                    </template>
                  </c-v-data-table>
                </c-v-col>
              </c-v-row>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
      </c-v-window>
      <g-base-or21814
        v-if="showDialogOr21814"
        v-bind="or21814"
      >
      </g-base-or21814>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="confirm"
        >
        <!--ツールチップ表示："設定を確定します。"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
.right-input {
  text-align: right !important;
}
:deep(.table-wrapper) {
  .v-table__wrapper {
    td {
      &:has(input:not([readonly]):not([disabled]):not([type='checkbox']):not([type='radio'])) {
        input {
          outline: 0px solid rgb(var(--v-theme-black-200)) !important;
        }
      }
    }
  }
}
.full-width-field {
  width: 100% !important; /* 幅をセルいっぱいに広げる */
  height: 100% !important; /* 高さをセルいっぱいに広げる */
  padding: 0 10px !important; /* 不要なパディングを取り除く */
  margin: 0 !important; /* 不要なマージンを取り除く */
}

.card-padding {
  padding: 8px 0 !important;
  :deep(.v-card-item) {
    padding: 8px 0 !important;
  }
  .v-card-text {
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-bottom: 0 !important;
  }
}

</style>
