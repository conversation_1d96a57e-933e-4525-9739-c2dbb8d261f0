/**
 * Or27754：［課題検討］画面
 * 単方向バインドModelValue
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */

/**
 * Or27754OnewayType
 */
export interface Or27754OnewayType {
  /** 事業所ID */
  officeId: string

  /** 事業者グループ適用ID */
  officeGroupApplyId: string

  /** 計画期間ID */
  planPeriodId: number

  /** 課題検討ID */
  issuesConsiderId: string

  /** 項目ID */
  itemId: number

  /** イベント */
  event: string

  /** 期間処理区分 */
  periodProcessKbn: string

  /** 履歴処理区分 */
  historyProcessKbn: string

  /** 利用者ID */
  userId: string

  /** 事業者ID */
  businessOperatorId: string

  /** マスタ区分 */
  masterKbn: string

  /** 改訂区分 */
  revisionKbn: string

  //COPY
  /** 複写画面表示フラグ */
  cpyFlg?: boolean
  /** param */
  param?: TransmitParam
}

/**
 * パラメータエンティティ
 */
// executeFlag: 'save' | 'add' | 'copy' | 'delete' | 'getData' | ''
// /**
//  * 事業所ID
//  */
// officeId: string
// /**
//  * 計画対象期間ID
//  */
// planPeriodId: number
// /**
//  * 履歴ID
//  */
// historyId: number
// }
export interface TransmitParam {
  /**
   * 実行フラグ(save：保存,add：新規,copy：複写,delete：削除,getData：データ再取得,''：何もしません)
   */
  executeFlag: 'save' | 'add' | 'copy' | 'delete' | 'getData' | ''
  /**
   * 削除ボタンの選択値
   */
  deleteBtnValue: string
  /**
   * 期間管理フラグ
   */
  kikanKanriFlg: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 基準日
   */
  kijunbiYmd: string
  /**
   * 作成者ID
   */
  sakuseiId: string
  /**
   * 更新回数_履歴
   */
  historyModifiedCnt: string
  /**
   * 履歴情報
   */
  historyInfo: HistoryInfo
  /**
   * 計画期間情報
   */
  planPeriodInfo: PlanPeriodInfo
}

/**
 * すべて camelCase。各プロパティには元の日本語フィールド名をコメントで併記。
 */
export interface KadaiKentoRequest {
  /** 計画期間ID */
  sc1Id: string

  /** 課題検討ID */
  kkyId: string

  /** 改訂フラグ */
  kaiteiKbn: string

  /** 期間管理フラグ */
  kknKanriFlg: string

  /** 更新区分 */
  updateKbn: string

  /** 履歴更新区分 */
  updateRirekiKbn: string

  /** 削除処理区分 */
  delKbn: string

  /** 当履歴ページ番号 */
  currentHistoryPageNo: string

  /** 履歴作成日 */
  sakuseiDate: string

  /** 記載者ID */
  sakuseiId: string

  /** 計画期間ID（詳細側と同じ値を保持） */
  detailSc1Id: string

  /** 項目ID */
  itemId: string

  /** 法人ID */
  houjinId: string

  /** 施設ID */
  shisetuId: string

  /** 事業所ID */
  jigyoId: string

  /** 利用者ID */
  usesId: string

  /** 課題検討情報リスト */
  kadaiList: KadaiItem[]
}

/**
 * 課題検討情報（リスト要素
 */
export interface KadaiItem {
  /** 検討が必要な具体的状況 */
  joukyouKnj: string

  /** 原因 */
  geninKnj: string

  /** 本人、家族の意向 */
  ikouKnj: string

  /** 自立に向けた可能性 */
  jirituKnj: string

  /** 生活全般の解決すべき課題（ニーズ） */
  kadaiKnj: string

  /** ケアの方向性 */
  careKnj: string

  /** 枠幅1 */
  width1: number
  /** 枠幅2 */
  width2: number
  /** 枠幅3 */
  width3: number
  /** 枠幅4 */
  width4: number
  /** 枠幅5 */
  width5: number
  /** 枠幅6 */
  width6: number
}
/**
 * 履歴情報
 */
export interface HistoryInfo {
  /**
   * 履歴ID
   */
  createId: number
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * アセスメントID
   */
  raiId: string
  /**
   * 調査アセスメント種別
   */
  assType: string
  /**
   * 選定アセスメント種別
   */
  capType: string
  /**
   * 検討アセスメント種別
   */
  plnType: string
  /**
   * 職員ID
   */
  shokuinId: string
  /**
   * 職員名
   */
  shokuinKnj: string
  /**
   * 基準日
   */
  assDateYmd: string
  /**
   * 履歴番号
   */
  krirekiNo: number
  /**
   * 履歴総件数
   */
  krirekiCnt: number
}

/**
 * 計画期間情報
 */
export interface PlanPeriodInfo {
  /**
   * 期間ID
   */
  sc1Id: number
  /**
   * 期間番号
   */
  periodNo: number
  /**
   * 期間総件数
   */
  periodCnt: number
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
}

// -----------------------------------

/**
 * 課題検討項目一覧ヘッダー
 */
export interface IssueConsiderationItemHeaderInputType {
  /**
   * 期間処理区分
   */
  kknSyoriKbn?: string
  /**
   * 履歴処理区分
   */
  rirekiSyoriKbn?: string
  /**
   * イベント
   */
  event: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   *マスタ区分
   *カテゴリー :1:施設、2:居宅
   */
  mstKbn: string
  /**
   * 課題検討ID
   */
  kky1Id?: string
  /**
   * 期間ID
   */
  sc1Id?: string
  /**
   * 項目ID
   */
  level1Id?: string
}

/**
 * パラメータエンティティ
 */
export interface InputExaminationIssues {
  /**
   *事業所ID
   */
  officeId: string

  /**
   *事業者グループ適用ID
   */
  businessGroupApplicableId: string

  /**
   * 利用者ID
   */
  userId: string

  /**
   *事業者ID
   */
  businessId: string

  /**
   *マスタ区分
   */
  masterCategory: string

  /**
   *改訂区分
   */
  revisionCategory: string
}
