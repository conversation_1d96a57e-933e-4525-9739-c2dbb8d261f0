<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or26866Const } from '~/components/custom-components/organisms/Or26866/Or26866.constants'
import { Or26866Logic } from '~/components/custom-components/organisms/Or26866/Or26866.logic'
import type { Or26866OnewayType, Or26866Type } from '~/types/cmn/business/components/Or26866Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 * SH 2025/04/01 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI01273'
// ルーティング
const routing = 'GUI01273/pinia'
// 画面物理名
const screenName = 'GUI01273'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or26866 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01273' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
or26866.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI01273',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26866Const.CP_ID(0) }],
})
Or26866Logic.initialize(init.childCpIds.Or26866.uniqueCpId)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or26866Const.CP_ID(0)]: or26866.value,
})

// ダイアログ表示フラグ
const showDialogOr26866 = computed(() => {
  // Or26866 cks_flg=1 のダイアログ開閉状態
  return Or26866Logic.state.get(or26866.value.uniqueCpId)?.isOpen ?? false
})

const or26866OnewayModel: Or26866OnewayType = {
  /**
   * 事業所ID
   */
  svJigyoId: '1',
  /**
   * ユーザーID
   */
  userId: '171',
  /**
   * 認定フラグ
   */
  ninteiFlg: '5',
  /**
   * 期間管理フラグ
   */
  dayVer: '0',
  /**
   * 計画期間ID
   */
  sc1Id: '0',
  /**
   * 項目番号
   */
  defNo: '1',
  /**
   * 項目番号配列
   */
  defNoArray: ['1', '2'],
  memo: '',
}
const or26866Type = ref<Or26866Type>({
  rtnContents: '',
})

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 利用者ID
  ninteiFlg: { value: '5' } as Mo00045Type,
  // 計画期間ID
  sc1Id: { value: '0' } as Mo00045Type,
  // 事業者ID
  svJigyoId: { value: '1' } as Mo00045Type,
  // ユーザーID
  userId: { value: '171' } as Mo00045Type,
  // 期間管理フラグ
  dayVer: { value: '0' } as Mo00045Type,
  // 項目番号
  defNo: { value: '1' } as Mo00045Type,
  // 項目番号配列
  defNoArray: { value: '1,2' } as Mo00045Type,
})

/**
 *  ボタン押下時の処理(Or26866)
 *
 * @param svJigyoId  - 事業所ID
 */
function onClickOr26866(svJigyoId: string) {
  // 引継情報.事業所IDを設定する。
  or26866OnewayModel.svJigyoId = svJigyoId
  // Or26866のダイアログ開閉状態を更新する
  Or26866Logic.state.set({
    uniqueCpId: or26866.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  疎通起動
 */
function onClickOr01273() {
  or26866OnewayModel.ninteiFlg = local.ninteiFlg.value
  or26866OnewayModel.sc1Id = local.sc1Id.value
  or26866OnewayModel.svJigyoId = local.svJigyoId.value
  or26866OnewayModel.userId = local.userId.value
  or26866OnewayModel.dayVer = local.dayVer.value
  or26866OnewayModel.defNo = local.defNo.value
  or26866OnewayModel.defNoArray = local.defNoArray.value.split(',')
  // Or26866のダイアログ開閉状態を更新する
  Or26866Logic.state.set({
    uniqueCpId: or26866.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/04/27 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26866('0')"
        >GUI001273_認定調査 特記事項選択_履歴件数＜＝0の場合
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26866('1')"
        >GUI001273_認定調査 特記事項選択_履歴件数＞0の場合
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-26866
    v-if="showDialogOr26866"
    v-bind="or26866"
    v-model="or26866Type"
    :oneway-model-value="or26866OnewayModel"
  />
  <!-- POP画面ポップアップ SH 2025/04/27 ADD END-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">認定フラグ</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.ninteiFlg"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">計画期間ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.sc1Id"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">事業者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.svJigyoId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>

  <div style="margin-left: 20px">ユーザーID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">期間管理フラグ</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.dayVer"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">項目番号</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.defNo"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">項目番号配列 (用','拼接生成list)</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.defNoArray"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr01273"> GUI01273 疎通起動 </v-btn>
  </div>
</template>
