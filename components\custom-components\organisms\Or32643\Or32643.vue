<script setup lang="ts">
/**
 * 有機体 Or32643
 * GUI00796_［アセスメント］画面（居宅）（3）
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { onMounted, reactive, ref, watch, computed, nextTick } from 'vue'
import { cloneDeep } from 'lodash'
import { Or32646Const } from '../Or32646/Or32646.constants'
import { TeX0002Const } from '../../template/TeX0002/TeX0002.constants'
import { TeX0002Logic } from '../../template/TeX0002/TeX0002.logic'
import { Or32647Const } from '../Or32647/Or32647.constants'
import { Or32648Const } from '../Or32648/Or32648.constants'
import { Or35672Logic } from '../Or35672/Or35672.logic'
import { Or35672Const } from '../Or35672/Or35672.constants'
import { Or28500Logic } from '../Or28500/Or28500.logic'
import { Or28500Const } from '../Or28500/Or28500.constants'
import { OrX0209Const } from '../OrX0209/OrX0209.constants'
import { Or32643Const } from './Or32643.constants'
import { Or32643Logic } from './Or32643.logic'
import { UPDATE_KBN } from '~/constants/classification-constants'
import {
  useCmnCom,
  useScreenStore,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
  useScreenTwoWayBind,
} from '#imports'
import type { Or28500Type, Or28500OnewayType } from '~/types/cmn/business/components/Or28500Type'
import type { Or32643OnewayType } from '~/types/cmn/business/components/Or32643Type'
import type {
  BeingAtHomeUseItem,
  Or32646OnewayType,
  Or32646Type,
} from '~/types/cmn/business/components/Or32646Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
import type {
  AssessmentHomeServiceChosaInfoSelectInEntity,
  AssessmentHomeServiceChosaInfoSelectOutEntity,
  AssessmentHomeServiceChosaCntSelectInEntity,
  AssessmentHomeServiceChosaCntSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentHomeServiceChosaEntity'
import type {
  AssessmentHomeServiceInitSelectInEntity,
  AssessmentHomeServiceInitSelectOutEntity,
  AssessmentHomeServiceUpdateInEntity,
  AssessmentHomeServiceUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentHomeServiceInitEntity'
import type { Mo00020Type, Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  Or32647OnewayType,
  Or32647Type,
  UseFacilityType,
} from '~/types/cmn/business/components/Or32647Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00040Type, Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type {
  Or32648OnewayType,
  Or32648Type,
  PentionItem,
  OtherCheckBoxItem,
  AdultObserverSystemItem,
  CompensationInsurance,
  ElderlyHealthProject,
  OtherItem,
} from '~/types/cmn/business/components/Or32648Type'
import type { Mo00039Items, Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import type { Mo00038Type } from '~/types/business/components/Mo00038Type'
import type {
  IssuesAndGoalListItem,
  OrX0209OnewayType,
  OrX0209Type,
} from '~/types/cmn/business/components/OrX0209Type'
import { useNuxtApp } from '#app'
import type { OrX0201OnewayType } from '~/types/cmn/business/components/OrX0201Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'

/************************************************
 * Props
 ************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  onewayModelValue?: Or32643OnewayType
  parentUniqueCpId: string
}

const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { getChildCpBinds } = useScreenUtils()
const $log = useNuxtApp().$log as DebugLogPluginInterface
const { t } = useI18n()
/** システムstore */
const systemCommonsStore = useSystemCommonsStore()

/** 解決すべき課題と目標Ref */
const issuesAndGoalListRef = ref<HTMLElement | null>(null)

/** 更新区分 */
const updateKbn = ref('')

/** 初期化データの一時保存 */
let initData = {} as AssessmentHomeServiceInitSelectOutEntity

/** デフォルト twoWay */
const defaultTwoWay = {
  // 在宅利用セクション
  // 左上部
  or32646TypeTopLeft: {
    beingAtHomeUseItems: [
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
    ] as BeingAtHomeUseItem[],
  } as Or32646Type,
  // 右上部
  or32646TypeTopRight: {
    beingAtHomeUseItems: [
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
    ] as BeingAtHomeUseItem[],
  } as Or32646Type,
  // 左下部
  or32646TypeBottomLeft: {
    beingAtHomeUseItems: [
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
    ] as BeingAtHomeUseItem[],
  } as Or32646Type,
  // 右下部
  or32646TypeBottomRight: {
    beingAtHomeUseItems: [
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        raidoValue: '',
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        textareaInputValue: { value: '' } as Mo00046Type,
      },
    ] as BeingAtHomeUseItem[],
  } as Or32646Type,
  // 直近の入所・入院ラジオボタン選択肢
  or32647Type: {
    useFacilityType: { raidoValue: '' } as UseFacilityType,
    facilityInstitution: {
      twowayValue: { modelValue: '' } as Mo00040Type,
      onewayValue: {
        itemLabel: '',
        items: [],
        itemTitle: 'shisetuKnj',
        itemValue: 'shisetuKnj',
        width: '300px',
        showItemLabel: true,
        isVerticalLabel: true,
      } as Mo00040OnewayType,
    },
    zipCode: { value: '' } as Mo00045Type,
    remarks: { value: '' } as Mo00045Type,
    telNumber: { value: '' } as Mo00045Type,
  } as Or32647Type,
  // 制度利用状況
  or32648Type: {
    /** 年金 */
    pensionItem: {
      pensionCheckItems: [
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 老齢関係
            checkboxLabel: t('label.old-age-relation'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'pr-6',
            }),
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 障害関係
            checkboxLabel: t('label.disability-relation'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'pr-6',
            }),
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 遺族･寡婦
            checkboxLabel: t('label.bereaved-family-widow'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'pr-6',
            }),
          } as Mo00018OnewayType,
        },
      ],
      systemUseTextValue: [{ value: '' }, { value: '' }, { value: '' }] as Mo00045Type[],
    } as PentionItem,
    /** その他チェックボックス */
    otherCheckBoxItem: {
      otherCheckItems: [
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 恩給
            checkboxLabel: t('label.grace-pension'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 特別障害者手当
            checkboxLabel: t('label.special-disability-allowance'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 生活保護
            checkboxLabel: t('label.life-protection'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 生活福祉資金貸付
            checkboxLabel: t('label.life-welfare-capital-loan'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 高齢者住宅整備資金貸付
            checkboxLabel: t('label.senior-housing-preparation-capital-loan'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 日常生活自立支援事業(地域福祉権利擁護)
            checkboxLabel: t('label.everyday-life-independence-support-project'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
      ],
    } as OtherCheckBoxItem,
    /** 成年後見制度 */
    adultObserverSystemItem: {
      checkboxValue: { modelValue: false } as Mo00018Type,
      checkboxOnewayModelValue: {
        // 成年後見制度
        checkboxLabel: t('label.adultSystem'),
        isVerticalLabel: false,
      } as Mo00018OnewayType,
      raidoValue: '',
      radioOneway: {} as Mo00039OnewayType,
    } as AdultObserverSystemItem,
    /** 成年後見人等 */
    adultObserverInputValue: { value: '' } as Mo00045Type,
    /** 健康保険  作成日が2012年4月1日前の以外の場合*/
    healthInsurance1: {
      insuranceCheckItems: [
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 国保
            checkboxLabel: t('label.national-health-insurance'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 協会けんぽ(旧・政管健保)
            checkboxLabel: t('label.association-health-insurance'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 組合健保
            checkboxLabel: t('label.union-health-insurance'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 日雇い
            checkboxLabel: t('label.day-labor'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 国公共済
            checkboxLabel: t('label.national-civil-servant-mutual-aid'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 地方共済
            checkboxLabel: t('label.region-mutual-aid'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 私立学校共済
            checkboxLabel: t('label.private-school-mutual-aid'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 船員
            checkboxLabel: t('label.seaman'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 後期高齢者医療
            checkboxLabel: t('label.later-period-senior-medical-care'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
      ],
    },
    /** 健康保険 作成日が2012年4月1日以前の場合 */
    healthInsurance2: {
      insuranceCheckItems: [
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 国保
            checkboxLabel: t('label.national-health-insurance'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 協会けんぽ(旧・政管健保)
            checkboxLabel: t('label.association-health-insurance'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 組合健保
            checkboxLabel: t('label.union-health-insurance'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 日雇い
            checkboxLabel: t('label.day-labor'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 国公共済
            checkboxLabel: t('label.national-civil-servant-mutual-aid'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 地方共済
            checkboxLabel: t('label.region-mutual-aid'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 私立学校共済
            checkboxLabel: t('label.private-school-mutual-aid'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            // 船員
            checkboxLabel: t('label.seaman'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
      ],
    },
    /** 労災保険 */
    compensationInsurance: {
      insuranceCheckItems: {
        checkboxValue: { modelValue: false } as Mo00018Type,
        onewayModelValue: {
          // 労災保険
          checkboxLabel: t('label.compensation-insurance'),
          isVerticalLabel: false,
        } as Mo00018OnewayType,
      },
      systemUseTextValue: { value: '' } as Mo00045Type,
    } as CompensationInsurance,
    /** その他 */
    otherItem: {
      otherCheckItems: [
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            checkboxLabel: '',
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            checkboxLabel: '',
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            checkboxLabel: '',
            isVerticalLabel: false,
          } as Mo00018OnewayType,
        },
      ],
      systemUseTextValue: [
        { value: '' } as Mo00045Type,
        { value: '' } as Mo00045Type,
        { value: '' } as Mo00045Type,
      ],
    } as OtherItem,
    /** 老人保健事業 */
    elderlyHealthProject: {
      checkItems: [
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            /** 健康手帳の交付 */
            checkboxLabel: t('label.health-notebook-issue'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          onewayModelValue: {
            /** 健康診査 */
            checkboxLabel: t('label.health-check-up'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
        },
      ],
    } as ElderlyHealthProject,
  },
}

// ローカルTwoway
const local = reactive({
  // 共通情報
  commonInfo: {} as TeX0002Type,
  // 時点
  timePointYmd: { value: '' } as Mo00020Type,
  // 在宅利用項目 項目名と選択肢
  // 左上部
  or32646TypeTopLeft: {} as Or32646Type,
  // 右上部
  or32646TypeTopRight: {} as Or32646Type,
  // 左下部
  or32646TypeBottomLeft: {} as Or32646Type,
  // 右下部
  or32646TypeBottomRight: {} as Or32646Type,
  // 直近の入所・入院ラジオボタン選択肢
  or32647Type: {} as Or32647Type,
  // 制度利用状況
  or32648Type: {} as Or32648Type,
  // 解決すべき課題と目標
  issuesAndGoalsList: {
    items: [] as IssuesAndGoalListItem[],
  } as OrX0209Type,
  // 期間内履歴選択
  or28500: {
    isBundleImport: { modelValue: false } as Mo00018Type,
    questionnaireRevisionFlg: '1',
    historySelectedList: [
      {
        jisshiDateYmd: '',
        name1_knj: '',
        name2_knj: '',
        chousacodeNameKnj: '',
        ninteiFlg: '',
      },
    ],
  } as Or28500Type,
})

/** デフォルト oneWay */
const defaultOneway = {
  // 在宅利用項目
  // 左上部
  or32646OnewayTypeTopLeft: {
    itemOnewayType: [
      {
        // 訪問介護（ホームヘルプサービス)
        checkboxLable: {
          checkboxLabel: t('label.home-visit-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // (介護予防)訪問入浴介護
        checkboxLable: {
          checkboxLabel: t('label.home-visit-bathing-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // (介護予防)訪問看護
        checkboxLable: {
          checkboxLabel: t('label.home-visit-nursing'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // (介護予防)訪問リハビリテーション
        checkboxLable: {
          checkboxLabel: t('label.home-visit-rehabilitation-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // (介護予防)居宅療養管理指導
        checkboxLable: {
          checkboxLabel: t('label.home-recovery-manage-guidance'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // 通所介護（デイサービス）
        checkboxLable: {
          checkboxLabel: t('label.day-care-service'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // (介護予防)通所リハビリテーション（デイケア）
        checkboxLable: {
          checkboxLabel: t('label.day-care-rehabilitation'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // (介護予防)短期入所生活介護(特養等
        checkboxLable: {
          checkboxLabel: t('label.short-term-admission-life-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '1',
      },
      {
        // (介護予防)短期入所療養介護(老健・診療所）
        checkboxLable: {
          checkboxLabel: t('label.short-term-admission-recovery-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '1',
      },
      {
        // (介護予防)特定施設入所者生活介護
        checkboxLable: {
          checkboxLabel: t('label.special-facility-admission-life-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '1',
      },
      {
        // 看護小規模多機能型居宅介護
        checkboxLable: {
          checkboxLabel: t('label.nursing-function-home-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '1',
      },
    ],
    rowHeight: '452',
    areaFlg: '1',
  } as Or32646OnewayType,
  // 右上部
  or32646OnewayTypeTopRight: {
    itemOnewayType: [
      {
        // (介護予防)福祉用具貸与
        checkboxLable: {
          checkboxLabel: t('label.welfare-equipment-lending'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '2',
      },
      {
        // 特定(介護予防)福祉用具販売
        checkboxLable: {
          checkboxLabel: t('label.specific-welfare-equipment-lending'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '2',
      },
      {
        // 住宅改修
        checkboxLable: {
          checkboxLabel: t('label.housing-repair'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '3',
      },
      {
        // 夜間対応型訪問介護
        checkboxLable: {
          checkboxLabel: t('label.nighttime-home-visit-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '1',
      },
      {
        // (介護予防)認知症対応型通所介護
        checkboxLable: {
          checkboxLabel: t('label.dementia-day-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '1',
      },
      {
        // (介護予防)小規模多機能型居宅介護
        checkboxLable: {
          checkboxLabel: t('label.function-home-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '1',
      },
      {
        // (介護予防)認知症対応型共同生活介護
        checkboxLable: {
          checkboxLabel: t('label.dementia-communal-life-care'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '1',
      },
      {
        // 定期巡回・随時対応型訪問介護看護
        checkboxLable: {
          checkboxLabel: t('label.regular-home-visit-care-nursing'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
    ],
    radioOnewayType: {
      name: '',
      items: [
        {
          // 'あり'
          label: t('label.radio-yes'),
          value: '1',
        },
        {
          // 'なし'
          label: t('label.radio-no'),
          value: '0',
        },
      ],
      hideDetails: false,
      showItemLabel: false,
      checkOff: true,
      /** カスタムクラス */
      customClass: new CustomClass({
        outerClass: 'mt-1 mb-1',
      }),
    } as Mo00039OnewayType,
    rowHeight: '452',
    areaFlg: '2',
  } as Or32646OnewayType,
  // 左下部
  or32646OnewayTypeBottomLeft: {
    itemOnewayType: [
      {
        // 配食サービス
        checkboxLable: {
          checkboxLabel: t('label.food-delivery-service'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // 洗濯サービス
        checkboxLable: {
          checkboxLabel: t('label.washing-service'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // 移送または外出支援
        checkboxLable: {
          checkboxLabel: t('label.transfer-going-out-support'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // 友愛訪問
        checkboxLable: {
          checkboxLabel: t('label.friendship-visit'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // 老人福祉センター
        checkboxLable: {
          checkboxLabel: t('label.elderly-welfare-center'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // 老人憩いの家
        checkboxLable: {
          checkboxLabel: t('label.elderly-rest-home'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // ガイドヘルパー
        checkboxLable: {
          checkboxLabel: t('label.guide-helper'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // 身障／補装具･日常生活用具
        checkboxLable: {
          checkboxLabel: t('label.disability-everyday-tools'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '4',
      },
    ],
    rowHeight: '335',
    areaFlg: '3',
  } as Or32646OnewayType,
  // 右下部
  or32646OnewayTypeBottomRight: {
    itemOnewayType: [
      {
        // 生活支援員の訪問（地域福祉権利擁護事業）
        checkboxLable: {
          checkboxLabel: t('label.life-support-visit'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // ふれあい・いきいきサロン
        checkboxLable: {
          checkboxLabel: t('label.interaction-lively-salon'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      },
      {
        // 市町村特別給付
        checkboxLable: {
          checkboxLabel: t('label.cities-specific-benefit'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '4',
      },
      {
        // テキストエリアを含む
        checkboxLable: {
          checkboxLabel: '',
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '5',
      },
      {
        // テキストエリアを含む
        checkboxLable: {
          checkboxLabel: '',
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'd-flex align-center',
          }),
        } as Mo00018OnewayType,
        itemType: '5',
      },
    ],
    rowHeight: '335',
    areaFlg: '4',
  },
  // 直近の入所・入院Oneway
  or32647OnewayType: {
    facilityInstitutionLabel: t('label.facility-institution-name'),
    shisetsuDataList: [],
    locationOnewayLabel: t('label.location'),
    telNumberLabel: t('label.telno'),
  } as Or32647OnewayType,
  // 制度利用状況 Oneway
  or32648OnewayType: {
    pensionLabel: t('label.pension'),
    textLabel: t('label.adultObserver'),
    /**
     * その他または老人保健事業の区分
     * 'other':その他  'elder':老人保健事業
     */
    itemKbn: Or32648Const.DEFAULT.OTHER_KBN,
    /** 健康保険ラベル */
    healthInsuranceLable: t('label.health-insurance'),
    /** その他ラベル */
    otherLable: t('label.other-label'),
    /** 老人保健事業ラベル */
    elderlyHealthProjectLabel: t('label.elderly-health-project'),
  } as Or32648OnewayType,
}

/** One-way */
const localOneway = reactive({
  // タブタイトル
  orX0201Oneway: {
    title: t('label.assessment-home-3-title'),
    tabNo: Or32643Const.DEFAULT.TAB_ID,
    titleDisplayFlg: true,
  } as OrX0201OnewayType,
  //  調査票取込ボタン
  importBtnOneway: {
    /** 取込ボタン名 */
    btnLabel: t('label.assessment-home-3-servey-ledger-import'),
  } as Mo00611OnewayType,
  mo00020Oneway: {
    /** 時点 */
    appendLabel: t('label.time-point'),
    showItemLabel: false,
  } as Mo00020OnewayType,
  or32646OnewayTypeTopLeft: {} as Or32646OnewayType,
  or32646OnewayTypeTopRight: {} as Or32646OnewayType,
  or32646OnewayTypeBottomLeft: {} as Or32646OnewayType,
  or32646OnewayTypeBottomRight: {} as Or32646OnewayType,
  or32647OnewayType: {} as Or32647OnewayType,
  or32648OnewayType: {} as Or32648OnewayType,
  // 解決すべき課題と目標一覧
  issuesAndGoalsListOneway: {
    assessmentName:
      t('label.assessment-home-issuesAndGoals-no-3') +
      '\r\n' +
      t('label.assessment-home-issuesAndGoals-name-3'),
  } as OrX0209OnewayType,
  // 期間内履歴選択oneway
  or28500Oneway: {
    sc1Id: '',
    svJigyoId: '',
    userId: '',
    kikanFlg: '',
    ninteiFlg: '',
  } as Or28500OnewayType,
})

const isLoading = ref(false)

const or32646 = ref({ uniqueCpId: '' })
const or32646_1 = ref({ uniqueCpId: '' })
const or32646_2 = ref({ uniqueCpId: '' })
const or32646_3 = ref({ uniqueCpId: '' })
const or32647 = ref({ uniqueCpId: '' })
const or32648 = ref({ uniqueCpId: '' })
const OrX0209 = ref({ uniqueCpId: '' })
const or28500 = ref({ uniqueCpId: '' })

/**************************************************
 * 算出プロパティ
 **************************************************/
// ダイアログ表示フラグ
const showDialogOr28500 = computed(() => {
  // ダイアログ開閉状態
  return Or28500Logic.state.get(or28500.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
/** 時点refValue */
const { refValue } = useScreenTwoWayBind<Mo00020Type>({
  cpId: Or32643Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or32646Const.CP_ID(0)]: or32646.value,
  [Or32646Const.CP_ID(1)]: or32646_1.value,
  [Or32646Const.CP_ID(2)]: or32646_2.value,
  [Or32646Const.CP_ID(3)]: or32646_3.value,
  [Or32647Const.CP_ID(0)]: or32647.value,
  [Or32648Const.CP_ID(0)]: or32648.value,
  [OrX0209Const.CP_ID(0)]: OrX0209.value,
  [Or28500Const.CP_ID(0)]: or28500.value,
})

/**************************************************
 * 関数
 **************************************************/
/**
 * Or35672イベント発火
 *
 * @param event - イベント
 */
function setOr35672Event(event: Record<string, boolean>) {
  Or35672Logic.event.set({
    uniqueCpId: Or35672Const.CP_ID(0) + props.parentUniqueCpId,
    events: event,
  })
}

/**
 *  複写画面から共通情報を取得
 */
function getCommonInfoFromCopy() {
  const commonInfo = Or35672Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo.ninteiFormF = commonInfo.params?.ninteiFormF
    local.commonInfo.activeTabId = commonInfo.params?.activeTabId
    local.commonInfo.jigyoId = commonInfo.params?.jigyoId
    local.commonInfo.sc1Id = commonInfo.params?.sc1Id
    local.commonInfo.gdlId = commonInfo.params?.gdlId
    local.commonInfo.createUserId = commonInfo.params?.createUserId
    local.commonInfo.createYmd = commonInfo.params?.createYmd
  }
}

/**
 *  コントロール初期化
 */
async function initControls() {
  await initCodes()
}
/**
 *  画面共通情報を取得
 */
function getCommonInfo() {
  const commonInfo = TeX0002Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo.ninteiFormF =
      commonInfo.ninteiFormF ?? Or32643Const.DEFAULT.NINTEI_FORM_FLG_H21_4
    local.commonInfo.ninteiFormF = '4'
    local.commonInfo.kikanKanriFlg = commonInfo.kikanKanriFlg
    local.commonInfo.userId = commonInfo.userId
    local.commonInfo.activeTabId = commonInfo.activeTabId
    local.commonInfo.jigyoId = commonInfo.jigyoId
    local.commonInfo.sc1Id = commonInfo.sc1Id
    local.commonInfo.gdlId = commonInfo.gdlId
    local.commonInfo.createUserId = commonInfo.createUserId
    local.commonInfo.createYmd = commonInfo.createYmd
    local.commonInfo.copyData = commonInfo.copyData
    local.commonInfo.deleteKbn = commonInfo.deleteKbn
    local.commonInfo.issuesAndGoalsList = commonInfo.issuesAndGoalsList
    local.commonInfo.historyNo = commonInfo.historyNo
    local.commonInfo.periodStartYmd = commonInfo.periodStartYmd
    local.commonInfo.periodStartYmd = commonInfo.periodEndYmd
    local.commonInfo.houjinId = commonInfo.houjinId
    local.commonInfo.shisetuId = commonInfo.shisetuId
    local.commonInfo.shisetuId = commonInfo.syubetuId
    local.commonInfo.shisetuId = commonInfo.sc1No
    local.commonInfo.historyUpdateKbn = commonInfo.historyUpdateKbn
    local.commonInfo.syubetuId = commonInfo.syubetuId

    // 解決すべき課題と目標一覧パラメータ設定
    localOneway.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
    localOneway.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
    localOneway.issuesAndGoalsListOneway.assNo = Or32643Const.DEFAULT.TAB_ID
    localOneway.issuesAndGoalsListOneway.userId = local.commonInfo.userId

    local.issuesAndGoalsList.items =
      cloneDeep(commonInfo.issuesAndGoalsList) ?? ([] as IssuesAndGoalListItem[])
  }
}

/**
 *  oneWayTypeの設定
 *
 * @param createYmd -作成日
 */

function oneWayTypeChange(createYmd: string) {
  const createDateNumber = new Date(createYmd).getTime()
  const basicDate01Number = new Date(Or32643Const.DEFAULT.CREATE_YMD_2018_4_1).getTime()
  const basicDate02Number = new Date(Or32643Const.DEFAULT.CREATE_YMD_2012_4_1).getTime()
  // Onewayの設定
  localOneway.or32646OnewayTypeTopLeft = { ...defaultOneway.or32646OnewayTypeTopLeft }
  localOneway.or32646OnewayTypeTopRight = { ...defaultOneway.or32646OnewayTypeTopRight }
  localOneway.or32646OnewayTypeBottomLeft = { ...defaultOneway.or32646OnewayTypeBottomLeft }
  localOneway.or32646OnewayTypeBottomRight = { ...defaultOneway.or32646OnewayTypeBottomRight }
  localOneway.or32647OnewayType = { ...defaultOneway.or32647OnewayType }
  localOneway.or32648OnewayType = { ...defaultOneway.or32648OnewayType }
  // H21/４改訂版の場合
  if (local.commonInfo.ninteiFormF === Or32643Const.DEFAULT.NINTEI_FORM_FLG_H21_4) {
    // 作成日が2018年4月1日以降の場合
    if (createDateNumber >= basicDate01Number) {
      localOneway.or32646OnewayTypeTopRight.dateType = '1'
      localOneway.or32646OnewayTypeBottomRight.dateType = '1'
      localOneway.or32646OnewayTypeTopRight.radioOnewayType!.items = [
        {
          // '有'
          label: t('label.presence'),
          value: '1',
        },
        {
          // '無'
          label: t('label.absence'),
          value: '0',
        },
      ]
      return
    }
    // 作成日が2018年4月1日以前 且つ 作成日が2012年4月1日以降
    else if (createDateNumber < basicDate01Number && createDateNumber >= basicDate02Number) {
      localOneway.or32646OnewayTypeBottomRight.dateType = '2'
      /**
       * 左上部
       */
      localOneway.or32646OnewayTypeTopLeft.rowHeight = '492'
      localOneway.or32646OnewayTypeTopLeft.itemOnewayType = [
        ...defaultOneway.or32646OnewayTypeTopLeft.itemOnewayType,
        {
          // 市町村特別給付
          checkboxLable: {
            checkboxLabel: t('label.cities-specific-benefit'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
          itemType: '4',
        },
      ]
      localOneway.or32646OnewayTypeTopLeft.itemOnewayType[5] = {
        // (介護予防)通所介護（デイサービス）
        checkboxLable: {
          checkboxLabel: t('label.preventive-care') + t('label.day-care-service'),
          isVerticalLabel: false,
        } as Mo00018OnewayType,
        itemType: '0',
      }
      localOneway.or32646OnewayTypeTopLeft.itemOnewayType[10] = {
        // 複合型サービス
        checkboxLable: {
          checkboxLabel: t('label.composite-service'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'mt-1',
          }),
        } as Mo00018OnewayType,
        itemType: '1',
      }
      /**
       * 右上部
       */
      localOneway.or32646OnewayTypeTopRight.radioOnewayType!.items = [
        {
          // 'あり'
          label: t('label.radio-yes'),
          value: '1',
        },
        {
          // 'なし'
          label: t('label.radio-no'),
          value: '0',
        },
      ]
      localOneway.or32646OnewayTypeTopRight.rowHeight = '492'
      localOneway.or32646OnewayTypeTopRight.itemOnewayType = [
        ...defaultOneway.or32646OnewayTypeTopRight.itemOnewayType,
      ]
      localOneway.or32646OnewayTypeTopRight.itemOnewayType.splice(7, 1)
      localOneway.or32646OnewayTypeTopRight.itemOnewayType.push(
        {
          // 地域密着型特定施設入居者生活介護
          checkboxLable: {
            checkboxLabel: t('label.area-specific-move-in-life-care'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
          itemType: '1',
        },
        {
          // 地域密着型介護老人福祉施設入所者生活介護
          checkboxLable: {
            checkboxLabel: t('label.area-elderly-welfare-admission-lift-care'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
          itemType: '1',
        },
        {
          // 定期巡回・随時対応型訪問介護看護
          checkboxLable: {
            checkboxLabel: t('label.regular-home-visit-care-nursing'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
          itemType: '0',
        },
        {
          // 生活支援員の訪問（地域福祉権利擁護事業）
          checkboxLable: {
            checkboxLabel: t('label.life-support-visit'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
          itemType: '0',
        },
        {
          // ふれあい・いきいきサロン
          checkboxLable: {
            checkboxLabel: t('label.interaction-lively-salon'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
          itemType: '0',
        }
      )
      /**
       * 左下部
       */
      localOneway.or32646OnewayTypeBottomLeft.itemOnewayType = [
        ...defaultOneway.or32646OnewayTypeBottomLeft.itemOnewayType,
      ]
      localOneway.or32646OnewayTypeBottomLeft.itemOnewayType[2] = {
        // 移送サービス
        checkboxLable: {
          checkboxLabel: t('label.transfer-service'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'mt-1',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      }
      localOneway.or32646OnewayTypeBottomLeft.rowHeight = '362'
      /**
       * 右下部
       */
      // 対象追加
      localOneway.or32646OnewayTypeBottomRight.itemOnewayType = [
        ...defaultOneway.or32646OnewayTypeBottomRight.itemOnewayType,
        {
          // テキストエリアを含む
          checkboxLable: {
            checkboxLabel: '',
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'd-flex align-center',
            }),
          } as Mo00018OnewayType,
          itemType: '5',
        },
        {
          // テキストエリアを含む
          checkboxLable: {
            checkboxLabel: '',
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'd-flex align-center',
            }),
          } as Mo00018OnewayType,
          itemType: '5',
        },
      ]
      // 対象削除
      localOneway.or32646OnewayTypeBottomRight.itemOnewayType.splice(0, 1)
      localOneway.or32646OnewayTypeBottomRight.itemOnewayType.splice(0, 1)
      localOneway.or32646OnewayTypeBottomRight.itemOnewayType.splice(0, 1)
    } else {
      localOneway.or32646OnewayTypeBottomRight.dateType = '3'
      /**
       * 左上部
       */
      localOneway.or32646OnewayTypeTopLeft.rowHeight = '492'
      localOneway.or32646OnewayTypeTopLeft.itemOnewayType = [
        ...defaultOneway.or32646OnewayTypeTopLeft.itemOnewayType,
        {
          // 市町村特別給付
          checkboxLable: {
            checkboxLabel: t('label.cities-specific-benefit'),
            isVerticalLabel: false,
          } as Mo00018OnewayType,
          itemType: '4',
        },
      ]
      localOneway.or32646OnewayTypeTopLeft.itemOnewayType[5] = {
        // (介護予防)通所介護（デイサービス）
        checkboxLable: {
          checkboxLabel: t('label.preventive-care') + t('label.day-care-service'),
          isVerticalLabel: false,
        } as Mo00018OnewayType,
        itemType: '0',
      }
      localOneway.or32646OnewayTypeTopLeft.itemOnewayType[10] = {
        // 複合型サービス
        checkboxLable: {
          checkboxLabel: t('label.composite-service'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'mt-1',
          }),
        } as Mo00018OnewayType,
        itemType: '1',
      }
      /**
       * 右上部
       */
      localOneway.or32646OnewayTypeTopRight.radioOnewayType!.items = [
        {
          // 'あり'
          label: t('label.radio-yes'),
          value: '1',
        },
        {
          // 'なし'
          label: t('label.radio-no'),
          value: '0',
        },
      ]
      localOneway.or32646OnewayTypeTopRight.rowHeight = '492'
      localOneway.or32646OnewayTypeTopRight.itemOnewayType = [
        ...defaultOneway.or32646OnewayTypeTopRight.itemOnewayType,
      ]
      localOneway.or32646OnewayTypeTopRight.itemOnewayType.splice(7, 1)
      localOneway.or32646OnewayTypeTopRight.itemOnewayType.push(
        {
          // 地域密着型特定施設入居者生活介護
          checkboxLable: {
            checkboxLabel: t('label.area-specific-move-in-life-care'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
          itemType: '1',
        },
        {
          // 地域密着型介護老人福祉施設入所者生活介護
          checkboxLable: {
            checkboxLabel: t('label.area-elderly-welfare-admission-lift-care'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
          itemType: '1',
        },
        {
          // 定期巡回・随時対応型訪問介護看護
          checkboxLable: {
            checkboxLabel: t('label.regular-home-visit-care-nursing'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
          itemType: '0',
        },
        {
          // 生活支援員の訪問（地域福祉権利擁護事業）
          checkboxLable: {
            checkboxLabel: t('label.life-support-visit'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
          itemType: '0',
        },
        {
          // ふれあい・いきいきサロン
          checkboxLable: {
            checkboxLabel: t('label.interaction-lively-salon'),
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'mt-1',
            }),
          } as Mo00018OnewayType,
          itemType: '0',
        }
      )
      /**
       * 左下部
       */
      localOneway.or32646OnewayTypeBottomLeft.itemOnewayType = [
        ...defaultOneway.or32646OnewayTypeBottomLeft.itemOnewayType,
      ]
      localOneway.or32646OnewayTypeBottomLeft.itemOnewayType[2] = {
        // 移送サービス
        checkboxLable: {
          checkboxLabel: t('label.transfer-service'),
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: 'mt-1',
          }),
        } as Mo00018OnewayType,
        itemType: '0',
      }
      localOneway.or32646OnewayTypeBottomLeft.rowHeight = '362'
      /**
       * 右下部
       */
      // 対象追加
      localOneway.or32646OnewayTypeBottomRight.itemOnewayType = [
        ...defaultOneway.or32646OnewayTypeBottomRight.itemOnewayType,
        {
          // テキストエリアを含む
          checkboxLable: {
            checkboxLabel: '',
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'd-flex align-center',
            }),
          } as Mo00018OnewayType,
          itemType: '5',
        },
        {
          // テキストエリアを含む
          checkboxLable: {
            checkboxLabel: '',
            isVerticalLabel: false,
            customClass: new CustomClass({
              outerClass: 'd-flex align-center',
            }),
          } as Mo00018OnewayType,
          itemType: '5',
        },
      ]
      // 対象削除
      localOneway.or32646OnewayTypeBottomRight.itemOnewayType.splice(0, 1)
      localOneway.or32646OnewayTypeBottomRight.itemOnewayType.splice(0, 1)
      localOneway.or32646OnewayTypeBottomRight.itemOnewayType.splice(0, 1)
      // 作成日が2012年4月1日以前の場合
      // 'elder':老人保健事業
      localOneway.or32648OnewayType.itemKbn = Or32648Const.DEFAULT.ELDER_KBN
    }
  }
  // R３/４改訂版の場合
  else {
    localOneway.or32646OnewayTypeTopLeft.dateType = '4'
    localOneway.or32646OnewayTypeTopRight.dateType = '4'
    localOneway.or32646OnewayTypeBottomLeft.dateType = '4'
    localOneway.or32646OnewayTypeBottomRight.dateType = '4'
    /**
     * 左上部
     */
    localOneway.or32646OnewayTypeTopLeft.rowHeight = '492'
    localOneway.or32646OnewayTypeTopLeft.itemOnewayType.splice(1, 0, {
      // (介護予防)訪問型サービス
      checkboxLable: {
        checkboxLabel: t('label.visit-type-service'),
        isVerticalLabel: false,
      } as Mo00018OnewayType,
      itemType: '0',
    })
    localOneway.or32646OnewayTypeTopLeft.itemOnewayType.splice(7, 0, {
      // (介護予防)通所型サービス
      checkboxLable: {
        checkboxLabel: t('label.care-day-care-service'),
        isVerticalLabel: false,
      } as Mo00018OnewayType,
      itemType: '0',
    })
    localOneway.or32646OnewayTypeTopLeft.itemOnewayType.splice(12, 1)
    localOneway.or32646OnewayTypeTopLeft.itemOnewayType.splice(11, 1)
    /**
     * 右上部
     */
    localOneway.or32646OnewayTypeTopRight.radioOnewayType!.items = [
      {
        // 'あり'
        label: t('label.radio-yes'),
        value: '1',
      },
      {
        // 'なし'
        label: t('label.radio-no'),
        value: '0',
      },
    ]
    localOneway.or32646OnewayTypeTopRight.rowHeight = '492'
    localOneway.or32646OnewayTypeTopRight.itemOnewayType = [
      ...defaultOneway.or32646OnewayTypeTopRight.itemOnewayType,
    ]

    localOneway.or32646OnewayTypeTopRight.itemOnewayType.splice(0, 0, {
      // 看護小規模多機能型居宅介護
      checkboxLable: {
        checkboxLabel: t('label.nursing-function-home-care'),
        isVerticalLabel: false,
        customClass: new CustomClass({
          outerClass: 'd-flex align-center',
        }),
      } as Mo00018OnewayType,
      itemType: '1',
    })
    localOneway.or32646OnewayTypeTopRight.itemOnewayType.splice(0, 0, {
      // (介護予防)特定施設入所者生活介護
      checkboxLable: {
        checkboxLabel: t('label.special-facility-admission-life-care'),
        isVerticalLabel: false,
        customClass: new CustomClass({
          outerClass: 'd-flex align-center',
        }),
      } as Mo00018OnewayType,
      itemType: '1',
    })
    localOneway.or32646OnewayTypeTopRight.itemOnewayType.push({
      // (介護予防)その他の生活支援サービス
      checkboxLable: {
        checkboxLabel: t('label.other-life-help-service'),
        isVerticalLabel: false,
        customClass: new CustomClass({
          outerClass: 'mt-1',
        }),
      } as Mo00018OnewayType,
      itemType: '6',
    })
  }
}

/**
 *  twoWayTypeの設定
 *
 * @param createYmd -作成日
 */
function twoWayTypeChange(createYmd: string) {
  const createDateNumber = new Date(createYmd).getTime()
  const basicDate01Number = new Date(Or32643Const.DEFAULT.CREATE_YMD_2018_4_1).getTime()
  const basicDate02Number = new Date(Or32643Const.DEFAULT.CREATE_YMD_2012_4_1).getTime()
  // Twowayの設定
  local.or32646TypeTopLeft = { ...defaultTwoWay.or32646TypeTopLeft }
  local.or32646TypeTopRight = { ...defaultTwoWay.or32646TypeTopRight }
  local.or32646TypeBottomLeft = { ...defaultTwoWay.or32646TypeBottomLeft }
  local.or32646TypeBottomRight = { ...defaultTwoWay.or32646TypeBottomRight }
  local.or32647Type = { ...defaultTwoWay.or32647Type }
  local.or32648Type.pensionItem = { ...defaultTwoWay.or32648Type.pensionItem }
  local.or32648Type.otherCheckBoxItem = { ...defaultTwoWay.or32648Type.otherCheckBoxItem }
  local.or32648Type.otherCheckBoxItem = { ...defaultTwoWay.or32648Type.otherCheckBoxItem }
  local.or32648Type.adultObserverSystemItem = {
    ...defaultTwoWay.or32648Type.adultObserverSystemItem,
  }
  local.or32648Type.adultObserverInputValue = {
    ...defaultTwoWay.or32648Type.adultObserverInputValue,
  }
  local.or32648Type.healthInsurance = { ...defaultTwoWay.or32648Type.healthInsurance1 }
  local.or32648Type.compensationInsurance = defaultTwoWay.or32648Type.compensationInsurance
  local.or32648Type.otherItem = { ...defaultTwoWay.or32648Type.otherItem }
  local.or32648Type.elderlyHealthProject = { ...defaultTwoWay.or32648Type.elderlyHealthProject }

  // H21/４改訂版の場合
  if (local.commonInfo.ninteiFormF === Or32643Const.DEFAULT.NINTEI_FORM_FLG_H21_4) {
    // 作成日が2018年4月1日以降の場合
    if (createDateNumber >= basicDate01Number) {
      return
    } // 作成日が2018年4月1日以前 且つ 作成日が2012年4月1日以降
    else if (createDateNumber < basicDate01Number && createDateNumber >= basicDate02Number) {
      /**
       * 在宅利用セクション
       */
      // 左上部
      local.or32646TypeTopLeft.beingAtHomeUseItems = [
        ...defaultTwoWay.or32646TypeTopLeft.beingAtHomeUseItems,
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
          textInputValue: { value: '' } as Mo00045Type,
          raidoValue: '',
          textareaInputValue: { value: '' } as Mo00046Type,
        },
      ]
      // 右上部
      local.or32646TypeTopRight.beingAtHomeUseItems = [
        ...defaultTwoWay.or32646TypeTopRight.beingAtHomeUseItems,
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
          textInputValue: { value: '' } as Mo00045Type,
          raidoValue: '',
          textareaInputValue: { value: '' } as Mo00046Type,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
          textInputValue: { value: '' } as Mo00045Type,
          raidoValue: '',
          textareaInputValue: { value: '' } as Mo00046Type,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
          textInputValue: { value: '' } as Mo00045Type,
          raidoValue: '',
          textareaInputValue: { value: '' } as Mo00046Type,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
          textInputValue: { value: '' } as Mo00045Type,
          raidoValue: '',
          textareaInputValue: { value: '' } as Mo00046Type,
        },
      ]
      // 右下部
      local.or32646TypeBottomRight.beingAtHomeUseItems = [
        ...defaultTwoWay.or32646TypeBottomRight.beingAtHomeUseItems,
      ]
      local.or32646TypeBottomRight.beingAtHomeUseItems.splice(0, 1)
    } else {
      // 作成日が2012年4月1日以前の場合
      /**
       * 在宅利用セクション
       */
      /**
       * 在宅利用セクション
       */
      // 左上部
      local.or32646TypeTopLeft.beingAtHomeUseItems = [
        ...defaultTwoWay.or32646TypeTopLeft.beingAtHomeUseItems,
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
          textInputValue: { value: '' } as Mo00045Type,
          raidoValue: '',
          textareaInputValue: { value: '' } as Mo00046Type,
        },
      ]
      // 右上部
      local.or32646TypeTopRight.beingAtHomeUseItems = [
        ...defaultTwoWay.or32646TypeTopRight.beingAtHomeUseItems,
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
          textInputValue: { value: '' } as Mo00045Type,
          raidoValue: '',
          textareaInputValue: { value: '' } as Mo00046Type,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
          textInputValue: { value: '' } as Mo00045Type,
          raidoValue: '',
          textareaInputValue: { value: '' } as Mo00046Type,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
          textInputValue: { value: '' } as Mo00045Type,
          raidoValue: '',
          textareaInputValue: { value: '' } as Mo00046Type,
        },
        {
          checkboxValue: { modelValue: false } as Mo00018Type,
          textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
          textInputValue: { value: '' } as Mo00045Type,
          raidoValue: '',
          textareaInputValue: { value: '' } as Mo00046Type,
        },
      ]
      // 右下部
      local.or32646TypeBottomRight.beingAtHomeUseItems = [
        ...defaultTwoWay.or32646TypeBottomRight.beingAtHomeUseItems,
      ]
      local.or32646TypeBottomRight.beingAtHomeUseItems.splice(0, 1)
      // 右下部
      local.or32646TypeBottomRight.beingAtHomeUseItems = [
        ...defaultTwoWay.or32646TypeBottomRight.beingAtHomeUseItems,
      ]
      local.or32646TypeBottomRight.beingAtHomeUseItems.splice(0, 1)
      // 制度利用状況 健康保険
      local.or32648Type.healthInsurance = { ...defaultTwoWay.or32648Type.healthInsurance2 }
    }
  }
  // R３/４改訂版の場合
  else {
    // 右上部
    local.or32646TypeTopRight.beingAtHomeUseItems = [
      ...defaultTwoWay.or32646TypeTopRight.beingAtHomeUseItems,
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        textNumberValue: { mo00045: { value: '' } } as Mo00038Type,
        textInputValue: { value: '' } as Mo00045Type,
        raidoValue: '',
        textareaInputValue: { value: '' } as Mo00046Type,
      },
    ]
  }
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const cmnSystemCodeInEntity = {
    selectCodeKbnList: [
      // 直近の入所・入院 H21/４改訂版かつ作成日が2018年4月1日以降の場合
      { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_1 },
      // 直近の入所・入院 H21/４改訂版かつ作成日が2018年4月1日以降または2012年4月1日以前の場合
      { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_2 },
      // 直近の入所・入院 R３/４改訂版の場合
      { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_USE_FACILITY_TYPE_4 },
      // 直成年後見人制度
      { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ADULT_GUARDIANSHIP_SYSTEM },
    ],
  }

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes(cmnSystemCodeInEntity)

  const createDateNumber = new Date(local.commonInfo.createYmd ?? '').getTime()
  const basicDateNumber = new Date(Or32643Const.DEFAULT.CREATE_YMD_2018_4_1).getTime()

  // カスタマイズソート
  const customOrder = ['1', '2', '3', '5', '6', '7', '8', '4']
  let recentAdmissionHospitalizationCodes
  // H21/４改訂版の場合
  if (local.commonInfo.ninteiFormF === Or32643Const.DEFAULT.NINTEI_FORM_FLG_H21_4) {
    if (createDateNumber >= basicDateNumber) {
      // 直近の入所・入院
      recentAdmissionHospitalizationCodes = CmnSystemCodeRepository.filter(
        CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_1
      )
    } else {
      // 直近の入所・入院
      recentAdmissionHospitalizationCodes = CmnSystemCodeRepository.filter(
        CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_2
      )
    }
  }
  // R３/４改訂版の場合
  else {
    // 直近の入所・入院
    recentAdmissionHospitalizationCodes = CmnSystemCodeRepository.filter(
      CmnMCdKbnId.M_CD_KBN_ID_USE_FACILITY_TYPE_4
    )
  }
  recentAdmissionHospitalizationCodes.sort((a, b) => {
    const indexA = customOrder.indexOf(a.value)
    const indexB = customOrder.indexOf(b.value)
    return indexA - indexB
  })
  getRecentAdmissionCodes(recentAdmissionHospitalizationCodes)

  // 成年後見制度
  const adultObserverSystemCodes = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ADULT_GUARDIANSHIP_SYSTEM
  )
  // カスタマイズソート
  const customOrder01 = ['1', '2', '3']
  adultObserverSystemCodes.sort((a, b) => {
    const indexA = customOrder01.indexOf(a.value)
    const indexB = customOrder01.indexOf(b.value)
    return indexA - indexB
  })
  // 成年後見制度コード処理
  getAdultObserverSystemCodes(adultObserverSystemCodes)
}

/**
 *  コントロール初期化
 */
async function reload() {
  isLoading.value = true
  let start: number = performance.now()
  // 汎用コードマスタからコード情報を取得
  await initCodes()
  // clearData()
  let end: number = performance.now()
  console.log(`initCodes実行時間: ${(end - start).toFixed(3)} ミリ秒`)

  start = performance.now()
  // 画面初期情報取得
  await getInitDataInfo()
  // RefValue初期化
  useScreenStore().setCpTwoWay({
    cpId: Or32643Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
  end = performance.now()
  console.log(`getInitDataInfo実行時間: ${(end - start).toFixed(3)} ミリ秒`)

  isLoading.value = false
}

/**
 *  直近の入所・入院コード処理
 *
 * @param recentAdmissionList -  直近の入所・入院コードリスト
 */
function getRecentAdmissionCodes(recentAdmissionList: CodeType[]) {
  if (recentAdmissionList) {
    const sectionItem = {} as UseFacilityType
    // ラジオOneway
    sectionItem.radioOneway = {
      name: '',
      showItemLabel: false,
      hideDetails: true,
      inline: false,
      items: [],
    }
    // 返却値を繰り返し
    for (const item of recentAdmissionList) {
      let it = {} as Mo00039Items
      // ラジオを取得
      it = {
        label: item.label,
        value: item.value,
      }
      sectionItem.radioOneway.items?.push(it)
      sectionItem.raidoValue = ''
    }
    local.or32647Type.useFacilityType = sectionItem
  }
}

/**
 *  成年後見制度コード処理
 *
 * @param adultObserverSystemList -  成年後見制度コードリスト
 */
function getAdultObserverSystemCodes(adultObserverSystemList: CodeType[]) {
  if (adultObserverSystemList) {
    let sectionItem = {} as Mo00039OnewayType
    // ラジオOneway
    sectionItem = {
      name: '',
      showItemLabel: false,
      hideDetails: true,
      inline: true,
      items: [],
    }
    // 返却値を繰り返し
    for (const item of adultObserverSystemList) {
      // ラジオを取得
      const it = {
        label: item.label,
        value: item.value,
      }
      sectionItem.items?.push(it)
    }
    local.or32648Type.adultObserverSystemItem.radioOneway = sectionItem
  }
}

/**
 *  画面初期情報取得
 */
async function getInitDataInfo() {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentHomeServiceInitSelectInEntity = {
    /** 事業所ID */
    svJigyoId: local.commonInfo.jigyoId,
    /** 計画期間ID */
    sc1Id: local.commonInfo.sc1Id,
    /** アセスメントID */
    gdlId: local.commonInfo.gdlId,
    /** 利用者ID */
    userId: local.commonInfo.createUserId,
    /** 履歴作成日 */
    createYmd: local.commonInfo.createYmd,
    /** 改定フラグ */
    ninteiFormF: local.commonInfo.ninteiFormF,
  }
  const resData: AssessmentHomeServiceInitSelectOutEntity = await ScreenRepository.select(
    'assessmentHomeServiceInitSelect',
    inputData
  )
  // 画面情報を設定
  if (resData) {
    // 初期化データの一時保存
    initData = resData
    setFormData(resData)
  }
}

/**
 * Or35672ステート発火
 *
 * @param state - ステート
 */
function setTeX0002State(state: Record<string, boolean>) {
  TeX0002Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 *  画面データを設定
 *
 * @param resData - 設定データ
 */
function setFormData(resData: AssessmentHomeServiceInitSelectOutEntity) {
  // 複写モードの場合、APIから取得のデータを保持
  if (props.onewayModelValue?.mode === Or32643Const.DEFAULT.MODE_COPY) {
    Or32643Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        copyData: resData,
      },
    })
  }
  // 画面情報を設定
  let tabData
  const createDateNumber = new Date(local.commonInfo.createYmd ?? '').getTime()
  const basicDate01Number = new Date(Or32643Const.DEFAULT.CREATE_YMD_2018_4_1).getTime()
  const basicDate02Number = new Date(Or32643Const.DEFAULT.CREATE_YMD_2012_4_1).getTime()
  // H21/４改訂版の場合
  if (local.commonInfo.ninteiFormF === Or32643Const.DEFAULT.NINTEI_FORM_FLG_H21_4) {
    if (resData.data.cpnTucGdl4SerInfo) {
      // 改訂フラグ4情報
      tabData = resData.data.cpnTucGdl4SerInfo
    }
  }
  // R3/４改訂版の場合
  else {
    if (resData.data.cpnTucGdl5SerInfo) {
      // 改訂フラグ5情報
      tabData = resData.data.cpnTucGdl5SerInfo
    }
  }
  if (tabData === undefined || Object.keys(tabData).length === 0) {
    return
  }

  // サービス利用状況記載日
  local.timePointYmd.value = tabData.serYmd

  // 左下部
  /** 配食サービス */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[0].checkboxValue.modelValue =
    tabData.ser19Cd === '1'
  /** 洗濯サービス */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[1].checkboxValue.modelValue =
    tabData.ser20Cd === '1'
  /** 移動または外出支援 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[2].checkboxValue.modelValue =
    tabData.ser21Cd === '1'
  /** 友愛訪問 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[3].checkboxValue.modelValue =
    tabData.ser22Cd === '1'
  /** 老人福祉センター */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[4].checkboxValue.modelValue =
    tabData.ser23Cd === '1'
  /** 老人憩いの家 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[5].checkboxValue.modelValue =
    tabData.ser24Cd === '1'
  /** ガイドヘルパー */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[6].checkboxValue.modelValue =
    tabData.ser25Cd === '1'
  /** 身障／補助具・日常生活用具 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[7].checkboxValue.modelValue =
    tabData.ser26Cd === '1'
  /** 配食サービスの利用回数 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[0].textNumberValue.mo00045.value = tabData.kaisu18
  /** 洗濯サービスの利用回数 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[1].textNumberValue.mo00045.value = tabData.kaisu19
  /** 移動または外出支援の利用回数 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[2].textNumberValue.mo00045.value = tabData.kaisu20
  /** 友愛訪問の利用回数 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[3].textNumberValue.mo00045.value = tabData.kaisu21
  /** 老人福祉センターの利用回数 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[4].textNumberValue.mo00045.value = tabData.kaisu22
  /** 老人憩いの家の利用回数 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[5].textNumberValue.mo00045.value = tabData.kaisu23
  /** ガイドヘルパーの利用回数 */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[6].textNumberValue.mo00045.value = tabData.kaisu24
  /** 身障／補助具・日常生活用具メモ */
  local.or32646TypeBottomLeft.beingAtHomeUseItems[7].textInputValue.value = tabData.youguKnj

  // 直近の入所・入院サブセクション
  /** 利用施設種別 */
  local.or32647Type.useFacilityType.raidoValue = tabData.shisetsuShu
  /** 施設名 */
  local.or32647Type.facilityInstitution.twowayValue.modelValue = tabData.shisetsuNameKnj
  /** 郵便番号 */
  local.or32647Type.zipCode.value = tabData.shisetsuZip
  /** 住所（以前備考） */
  local.or32647Type.remarks.value = tabData.shisetsuMemoKnj
  /** 電話番号 */
  local.or32647Type.telNumber.value = tabData.shisetsuTel

  // 制度利用状況サブセクション 左側
  /** 老齢関係 */
  local.or32648Type.pensionItem.pensionCheckItems[0].checkboxValue.modelValue =
    tabData.seido1 === '1'
  /** 障害関係 */
  local.or32648Type.pensionItem.pensionCheckItems[1].checkboxValue.modelValue =
    tabData.seido2 === '1'
  /** 遺族・寡婦 */
  local.or32648Type.pensionItem.pensionCheckItems[2].checkboxValue.modelValue =
    tabData.seido3 === '1'
  /** 老齢関係メモ */
  local.or32648Type.pensionItem.systemUseTextValue[0].value = tabData.riyoMemo1Knj
  /** 障害関係メモ */
  local.or32648Type.pensionItem.systemUseTextValue[1].value = tabData.riyoMemo2Knj
  /** 遺族・寡婦メモ */
  local.or32648Type.pensionItem.systemUseTextValue[2].value = tabData.riyoMemo3Knj
  /** 恩給 */
  local.or32648Type.otherCheckBoxItem.otherCheckItems[0].checkboxValue.modelValue =
    tabData.seido4 === '1'
  /** 特別障害者手当 */
  local.or32648Type.otherCheckBoxItem.otherCheckItems[1].checkboxValue.modelValue =
    tabData.seido5 === '1'
  /** 生活保護 */
  local.or32648Type.otherCheckBoxItem.otherCheckItems[2].checkboxValue.modelValue =
    tabData.seido6 === '1'
  /** 生活福祉資金貸付 */
  local.or32648Type.otherCheckBoxItem.otherCheckItems[3].checkboxValue.modelValue =
    tabData.seido7 === '1'
  /** 高齢者住宅整備資金貸付 */
  local.or32648Type.otherCheckBoxItem.otherCheckItems[4].checkboxValue.modelValue =
    tabData.seido8 === '1'
  /** 日常生活自立支援事業 */
  local.or32648Type.otherCheckBoxItem.otherCheckItems[5].checkboxValue.modelValue =
    tabData.seido9 === '1'
  /** 成年後見人制度 */
  local.or32648Type.adultObserverSystemItem.checkboxValue.modelValue = tabData.seido10 === '1'
  /** 成年後見人制度区分 */
  local.or32648Type.adultObserverSystemItem.raidoValue = tabData.seinenKoukenKbn
  /** 成年後見人等 */
  local.or32648Type.adultObserverInputValue.value = tabData.kokenKnj

  // 制度利用状況サブセクション 右側
  /** 国保 */
  local.or32648Type.healthInsurance.insuranceCheckItems[0].checkboxValue.modelValue =
    tabData.seido11 === '1'
  /** 協会けんぼ（旧・政管健保） */
  local.or32648Type.healthInsurance.insuranceCheckItems[1].checkboxValue.modelValue =
    tabData.seido12 === '1'
  /** 組合保健 */
  local.or32648Type.healthInsurance.insuranceCheckItems[2].checkboxValue.modelValue =
    tabData.seido13 === '1'
  /** 日雇い */
  local.or32648Type.healthInsurance.insuranceCheckItems[3].checkboxValue.modelValue =
    tabData.seido14 === '1'
  /** 国公共済 */
  local.or32648Type.healthInsurance.insuranceCheckItems[4].checkboxValue.modelValue =
    tabData.seido15 === '1'
  /** 地方共済 */
  local.or32648Type.healthInsurance.insuranceCheckItems[5].checkboxValue.modelValue =
    tabData.seido16 === '1'
  /** 私立学校共済 */
  local.or32648Type.healthInsurance.insuranceCheckItems[6].checkboxValue.modelValue =
    tabData.seido17 === '1'
  /** 船員 */
  local.or32648Type.healthInsurance.insuranceCheckItems[7].checkboxValue.modelValue =
    tabData.seido18 === '1'

  /** 労災保険 */
  local.or32648Type.compensationInsurance.insuranceCheckItems.checkboxValue.modelValue =
    tabData.seido19 === '1'
  // 労災保険メモ
  local.or32648Type.compensationInsurance.systemUseTextValue.value = tabData.riyoMemo4Knj

  let items = resData.data.shisetsuDataList
  items = items.filter((item) => item.scShisetsuShu === tabData.shisetsuShu)
  // 施設名称情報リスト
  local.or32647Type.facilityInstitution.onewayValue.items = [
    {
      // 施設ID
      shisetuId: '',
      // 施設名
      shisetuKnj: '',
      // 調査票施設種別
      scShisetsuShu: '',
      // 電話番号
      tel: '',
      // 住所
      addressKnj: '',
      // 表示順
      sort: '',
      // 郵便番号
      zip: '',
    },
    ...items,
  ]

  // H21/４改訂版の場合
  if (local.commonInfo.ninteiFormF === Or32643Const.DEFAULT.NINTEI_FORM_FLG_H21_4) {
    // 情報を設定
    // 在宅利用セクション
    // 左上部
    /** 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ) */
    local.or32646TypeTopLeft.beingAtHomeUseItems[0].checkboxValue.modelValue =
      tabData.ser1Cd === '1'
    /** (介護予防)訪問入浴介護 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[1].checkboxValue.modelValue =
      tabData.ser2Cd === '1'
    /** (介護予防)訪問看護 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[2].checkboxValue.modelValue =
      tabData.ser3Cd === '1'
    /** (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝ */
    local.or32646TypeTopLeft.beingAtHomeUseItems[3].checkboxValue.modelValue =
      tabData.ser4Cd === '1'
    /** (介護予防)居宅療養管理指導 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[4].checkboxValue.modelValue =
      tabData.ser5Cd === '1'
    /** 通所介護（ﾃﾞｲｻｰﾋﾞｽ） */
    local.or32646TypeTopLeft.beingAtHomeUseItems[5].checkboxValue.modelValue =
      tabData.ser6Cd === '1'
    /** (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ） */
    local.or32646TypeTopLeft.beingAtHomeUseItems[6].checkboxValue.modelValue =
      tabData.ser7Cd === '1'
    /** (介護予防)短期入所生活介護(特養等） */
    local.or32646TypeTopLeft.beingAtHomeUseItems[7].checkboxValue.modelValue =
      tabData.ser10Cd === '1'
    /** (介護予防)短期入所生活介護(老健・診療所） */
    local.or32646TypeTopLeft.beingAtHomeUseItems[8].checkboxValue.modelValue =
      tabData.ser11Cd === '1'
    /** (介護予防)特定施設入所生活介護 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[9].checkboxValue.modelValue =
      tabData.ser13Cd === '1'
    /** 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)の利用回数 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[0].textNumberValue.mo00045.value = tabData.kaisu1
    /** (介護予防)訪問入浴介護の利用回数 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[1].textNumberValue.mo00045.value = tabData.kaisu2
    /** (介護予防)訪問看護の利用回数 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[2].textNumberValue.mo00045.value = tabData.kaisu3
    /** (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝの利用回数 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[3].textNumberValue.mo00045.value = tabData.kaisu4
    /** (介護予防)居宅療養管理指導の利用回数 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[4].textNumberValue.mo00045.value = tabData.kaisu5
    /** 通所介護（ﾃﾞｲｻｰﾋﾞｽ）の利用回数 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[5].textNumberValue.mo00045.value = tabData.kaisu6
    /** (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）の利用回数 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[6].textNumberValue.mo00045.value = tabData.kaisu7
    /** (介護予防)短期入所生活介護(特養等）の利用回数 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[7].textNumberValue.mo00045.value = tabData.kaisu9
    /** (介護予防)短期入所生活介護(老健・診療所）の利用回数 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[8].textNumberValue.mo00045.value = tabData.kaisu10
    /** (介護予防)特定施設入所生活介護の利用回数 */
    local.or32646TypeTopLeft.beingAtHomeUseItems[9].textNumberValue.mo00045.value = tabData.kaisu12

    // 作成日が2018年4月1日以降の場合
    if (createDateNumber >= basicDate01Number) {
      /** 左上部 */
      // 複合型サービス / 看護小規模多機能型居宅介護
      local.or32646TypeTopLeft.beingAtHomeUseItems[10].checkboxValue.modelValue =
        tabData.ser36Cd === '1'
      // 複合型サービス（回数） / 看護小規模多機能型居宅介護（回数）
      local.or32646TypeTopLeft.beingAtHomeUseItems[10].textNumberValue.mo00045.value =
        tabData.kaisu36

      // 右上部
      /** (介護予防)福祉用具貸与 */
      local.or32646TypeTopRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
        tabData.ser9Cd === '1'
      /** 特定(介護予防)福祉用具販売 */
      local.or32646TypeTopRight.beingAtHomeUseItems[1].checkboxValue.modelValue =
        tabData.ser14Cd === '1'
      /** 住宅改修 */
      local.or32646TypeTopRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
        tabData.ser15Cd === '1'
      /** 夜間対応訪問介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
        tabData.ser31Cd === '1'
      /** 認知症対応型通所介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[4].checkboxValue.modelValue =
        tabData.ser32Cd === '1'
      /** 小規模多機能居宅介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[5].checkboxValue.modelValue =
        tabData.ser33Cd === '1'
      /** 認知症対応型共同生活介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[6].checkboxValue.modelValue =
        tabData.ser12Cd === '1'
      /** 定期巡回・随時対応型訪問介護看護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[7].checkboxValue.modelValue =
        tabData.ser37Cd === '1'
      /** (介護予防)福祉用具貸与の利用回数 */
      local.or32646TypeTopRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
        tabData.kaisu8
      /** 特定(介護予防)福祉用具販売の利用回数 */
      local.or32646TypeTopRight.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
        tabData.kaisu13
      // 住宅改修の利用回数
      local.or32646TypeTopRight.beingAtHomeUseItems[2].raidoValue = tabData.kaisu15
      /** 夜間対応訪問介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
        tabData.kaisu31
      /** 認知症対応型通所介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[4].textNumberValue.mo00045.value =
        tabData.kaisu32
      /** 小規模多機能居宅介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[5].textNumberValue.mo00045.value =
        tabData.kaisu33
      /** 認知症対応型共同生活介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
        tabData.kaisu11
      /** 定期巡回・随時対応型訪問介護看護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[7].textNumberValue.mo00045.value =
        tabData.kaisu37

      // 右下部
      /** 生活支援員の訪問 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
        tabData.ser17Cd === '1'
      /** ふれあい・いきいきサロン */
      local.or32646TypeBottomRight.beingAtHomeUseItems[1].checkboxValue.modelValue =
        tabData.ser18Cd === '1'
      /** 市町村特別給付 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
        tabData.ser8Cd === '1'
      /** 追加サービス1 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
        tabData.ser27Cd === '1'
      /** 追加サービス2 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[4].checkboxValue.modelValue =
        tabData.ser28Cd === '1'
      /** 追加サービス1メモ */
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].textareaInputValue.value =
        tabData.addSer1Knj
      /** 追加サービス2メモ */
      local.or32646TypeBottomRight.beingAtHomeUseItems[4].textareaInputValue.value =
        tabData.addSer2Knj
      /** 生活支援員の訪問の利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
        tabData.kaisu16
      /** ふれあい・いきいきサロンの利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
        tabData.kaisu17
      /** 市町村特別給付メモ */
      local.or32646TypeBottomRight.beingAtHomeUseItems[2].textInputValue.value = tabData.tokKyuKnj
      /** 追加サービス1の利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
        tabData.kaisu25
      /** 追加サービス2の利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[4].textNumberValue.mo00045.value =
        tabData.kaisu26

      // 制度利用30（後期高齢者医療）
      local.or32648Type.healthInsurance.insuranceCheckItems[8].checkboxValue.modelValue =
        tabData.seido30 === '1'

      /** 制度利用31（その他1） */
      local.or32648Type.otherItem.otherCheckItems[0].checkboxValue.modelValue =
        tabData.seido31 === '1'
      /** 制度利用32（その他2） */
      local.or32648Type.otherItem.otherCheckItems[1].checkboxValue.modelValue =
        tabData.seido32 === '1'
      /** 制度利用33（その他3） */
      local.or32648Type.otherItem.otherCheckItems[2].checkboxValue.modelValue =
        tabData.seido33 === '1'
      /** 制度利用ﾒﾓ5（その他1（メモ）） */
      local.or32648Type.otherItem.systemUseTextValue[0].value = tabData.riyoMemo5Knj
      /** 制度利用ﾒﾓ6（その他2（メモ）） */
      local.or32648Type.otherItem.systemUseTextValue[1].value = tabData.riyoMemo6Knj
      /** 制度利用ﾒﾓ7（その他3（メモ）） */
      local.or32648Type.otherItem.systemUseTextValue[2].value = tabData.riyoMemo7Knj
    }
    // 作成日が2018年4月1日以前 且つ 作成日が2012年4月1日以降
    else if (createDateNumber < basicDate01Number && createDateNumber >= basicDate02Number) {
      /** 左上部 */
      // 複合型サービス / 看護小規模多機能型居宅介護
      local.or32646TypeTopLeft.beingAtHomeUseItems[10].checkboxValue.modelValue =
        tabData.ser36Cd === '1'
      // 市町村特別給付
      local.or32646TypeTopLeft.beingAtHomeUseItems[11].checkboxValue.modelValue =
        tabData.ser8Cd === '1'
      // 複合型サービス（回数） / 看護小規模多機能型居宅介護（回数）
      local.or32646TypeTopLeft.beingAtHomeUseItems[10].textNumberValue.mo00045.value =
        tabData.kaisu36
      // 市町村特別給付メモ
      local.or32646TypeTopLeft.beingAtHomeUseItems[11].textInputValue.value = tabData.tokKyuKnj

      // 右上部
      /** (介護予防)福祉用具貸与 */
      local.or32646TypeTopRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
        tabData.ser9Cd === '1'
      /** 特定(介護予防)福祉用具販売 */
      local.or32646TypeTopRight.beingAtHomeUseItems[1].checkboxValue.modelValue =
        tabData.ser14Cd === '1'
      /** 住宅改修 */
      local.or32646TypeTopRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
        tabData.ser15Cd === '1'
      /** 夜間対応訪問介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
        tabData.ser31Cd === '1'
      /** 認知症対応型通所介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[4].checkboxValue.modelValue =
        tabData.ser32Cd === '1'
      /** 小規模多機能居宅介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[5].checkboxValue.modelValue =
        tabData.ser33Cd === '1'
      /** 認知症対応型共同生活介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[6].checkboxValue.modelValue =
        tabData.ser12Cd === '1'
      // 地域密着型特定施設入居者生活介護
      local.or32646TypeTopRight.beingAtHomeUseItems[7].checkboxValue.modelValue =
        tabData.ser34Cd === '1'
      // 地域密着型介護老人福祉施設入居者生活介護
      local.or32646TypeTopRight.beingAtHomeUseItems[8].checkboxValue.modelValue =
        tabData.ser35Cd === '1'
      /** 定期巡回・随時対応型訪問介護看護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[9].checkboxValue.modelValue =
        tabData.ser37Cd === '1'
      // 生活支援員の訪問
      local.or32646TypeTopRight.beingAtHomeUseItems[10].checkboxValue.modelValue =
        tabData.ser17Cd === '1'
      // ふれあい・いきいきサロン
      local.or32646TypeTopRight.beingAtHomeUseItems[11].checkboxValue.modelValue =
        tabData.ser18Cd === '1'

      /** (介護予防)福祉用具貸与の利用回数 */
      local.or32646TypeTopRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
        tabData.kaisu8
      /** 特定(介護予防)福祉用具販売の利用回数 */
      local.or32646TypeTopRight.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
        tabData.kaisu13
      // 住宅改修の利用回数
      local.or32646TypeTopRight.beingAtHomeUseItems[2].radioValue = tabData.kaisu15
      /** 夜間対応訪問介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
        tabData.kaisu31
      /** 認知症対応型通所介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[4].textNumberValue.mo00045.value =
        tabData.kaisu32
      /** 小規模多機能居宅介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[5].textNumberValue.mo00045.value =
        tabData.kaisu33
      /** 認知症対応型共同生活介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
        tabData.kaisu11
      // 地域密着型特定施設入居者生活介護（回数）
      local.or32646TypeTopRight.beingAtHomeUseItems[7].textNumberValue.mo00045.value =
        tabData.kaisu34
      // 地域密着型介護老人福祉施設入居者生活介護（回数）
      local.or32646TypeTopRight.beingAtHomeUseItems[8].textNumberValue.mo00045.value =
        tabData.kaisu35
      /** 定期巡回・随時対応型訪問介護看護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[9].textNumberValue.mo00045.value =
        tabData.kaisu37
      // 生活支援員の訪問の利用回数
      local.or32646TypeTopRight.beingAtHomeUseItems[10].textNumberValue.mo00045.value =
        tabData.kaisu16
      // ふれあい・いきいきサロンの利用回数
      local.or32646TypeTopRight.beingAtHomeUseItems[11].textNumberValue.mo00045.value =
        tabData.kaisu17

      // 右下部
      /** 追加サービス1 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
        tabData.ser27Cd === '1'
      /** 追加サービス2 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[1].checkboxValue.modelValue =
        // 追加サービス3
        local.or32646TypeBottomRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
          tabData.ser29Cd === '1'
      // 追加サービス4
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
        tabData.ser30Cd === '1'
      /** 追加サービス1メモ */
      local.or32646TypeBottomRight.beingAtHomeUseItems[0].textareaInputValue.value =
        tabData.addSer1Knj
      /** 追加サービス2メモ */
      local.or32646TypeBottomRight.beingAtHomeUseItems[1].textareaInputValue.value =
        tabData.addSer2Knj
      // 追加サービス3メモ
      local.or32646TypeBottomRight.beingAtHomeUseItems[2].textareaInputValue.value =
        tabData.addSer3Knj
      // 追加サービス4メモ
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].textareaInputValue.value =
        tabData.addSer4Knj
      /** 追加サービス1の利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
        tabData.kaisu25
      /** 追加サービス2の利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
        tabData.kaisu26
      // 追加サービス3の利用回数
      local.or32646TypeBottomRight.beingAtHomeUseItems[2].textNumberValue.mo00045.value =
        tabData.kaisu28
      // 追加サービス4の利用回数
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
        tabData.kaisu29

      // 制度利用30（後期高齢者医療）
      local.or32648Type.healthInsurance.insuranceCheckItems[8].checkboxValue.modelValue =
        tabData.seido30 === '1'

      /** 制度利用31（その他1） */
      local.or32648Type.otherItem.otherCheckItems[0].checkboxValue.modelValue =
        tabData.seido31 === '1'
      /** 制度利用32（その他2） */
      local.or32648Type.otherItem.otherCheckItems[1].checkboxValue.modelValue =
        tabData.seido32 === '1'
      /** 制度利用33（その他3） */
      local.or32648Type.otherItem.otherCheckItems[2].checkboxValue.modelValue =
        tabData.seido33 === '1'
      /** 制度利用ﾒﾓ5（その他1（メモ）） */
      local.or32648Type.otherItem.systemUseTextValue[0].value = tabData.riyoMemo5Knj
      /** 制度利用ﾒﾓ6（その他2（メモ）） */
      local.or32648Type.otherItem.systemUseTextValue[1].value = tabData.riyoMemo6Knj
      /** 制度利用ﾒﾓ7（その他3（メモ）） */
      local.or32648Type.otherItem.systemUseTextValue[2].value = tabData.riyoMemo7Knj
    } else {
      /** 左上部 */
      // 市町村特別給付
      local.or32646TypeTopLeft.beingAtHomeUseItems[10].checkboxValue.modelValue =
        tabData.ser8Cd === '1'
      // 市町村特別給付メモ
      local.or32646TypeTopLeft.beingAtHomeUseItems[10].textInputValue.value = tabData.tokKyuKnj

      // 右上部
      /** (介護予防)福祉用具貸与 */
      local.or32646TypeTopRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
        tabData.ser9Cd === '1'
      /** 特定(介護予防)福祉用具販売 */
      local.or32646TypeTopRight.beingAtHomeUseItems[1].checkboxValue.modelValue =
        tabData.ser14Cd === '1'
      /** 住宅改修 */
      local.or32646TypeTopRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
        tabData.ser15Cd === '1'
      /** 夜間対応訪問介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
        tabData.ser31Cd === '1'
      /** 認知症対応型通所介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[4].checkboxValue.modelValue =
        tabData.ser32Cd === '1'
      /** 小規模多機能居宅介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[5].checkboxValue.modelValue =
        tabData.ser33Cd === '1'
      /** 認知症対応型共同生活介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[6].checkboxValue.modelValue =
        tabData.ser12Cd === '1'
      // 地域密着型特定施設入居者生活介護
      local.or32646TypeTopRight.beingAtHomeUseItems[7].checkboxValue.modelValue =
        tabData.ser34Cd === '1'
      // 地域密着型介護老人福祉施設入居者生活介護
      local.or32646TypeTopRight.beingAtHomeUseItems[8].checkboxValue.modelValue =
        tabData.ser35Cd === '1'
      // 生活支援員の訪問
      local.or32646TypeTopRight.beingAtHomeUseItems[9].checkboxValue.modelValue =
        tabData.ser17Cd === '1'
      // ふれあい・いきいきサロン
      local.or32646TypeTopRight.beingAtHomeUseItems[10].checkboxValue.modelValue =
        tabData.ser18Cd === '1'
      /** (介護予防)福祉用具貸与の利用回数 */
      local.or32646TypeTopRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
        tabData.kaisu8
      /** 特定(介護予防)福祉用具販売の利用回数 */
      local.or32646TypeTopRight.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
        tabData.kaisu13
      // 住宅改修の利用回数
      local.or32646TypeTopRight.beingAtHomeUseItems[2].radioValue = tabData.kaisu15
      /** 夜間対応訪問介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
        tabData.kaisu31
      /** 認知症対応型通所介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[4].textNumberValue.mo00045.value =
        tabData.kaisu32
      /** 小規模多機能居宅介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[5].textNumberValue.mo00045.value =
        tabData.kaisu33
      /** 認知症対応型共同生活介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
        tabData.kaisu11
      // 地域密着型特定施設入居者生活介護（回数）
      local.or32646TypeTopRight.beingAtHomeUseItems[7].textNumberValue.mo00045.value =
        tabData.kaisu34
      // 地域密着型介護老人福祉施設入居者生活介護（回数）
      local.or32646TypeTopRight.beingAtHomeUseItems[8].textNumberValue.mo00045.value =
        tabData.kaisu35
      // 生活支援員の訪問の利用回数
      local.or32646TypeTopRight.beingAtHomeUseItems[9].textNumberValue.mo00045.value =
        tabData.kaisu16
      // ふれあい・いきいきサロンの利用回数
      local.or32646TypeTopRight.beingAtHomeUseItems[10].textNumberValue.mo00045.value =
        tabData.kaisu17

      // 右下部
      /** 追加サービス1 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
        tabData.ser27Cd === '1'
      /** 追加サービス2 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[1].checkboxValue.modelValue =
        // 追加サービス3
        local.or32646TypeBottomRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
          tabData.ser29Cd === '1'
      // 追加サービス4
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
        tabData.ser30Cd === '1'
      /** 追加サービス1メモ */
      local.or32646TypeBottomRight.beingAtHomeUseItems[0].textareaInputValue.value =
        tabData.addSer1Knj
      /** 追加サービス2メモ */
      local.or32646TypeBottomRight.beingAtHomeUseItems[1].textareaInputValue.value =
        tabData.addSer2Knj
      // 追加サービス3メモ
      local.or32646TypeBottomRight.beingAtHomeUseItems[2].textareaInputValue.value =
        tabData.addSer3Knj
      // 追加サービス4メモ
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].textareaInputValue.value =
        tabData.addSer4Knj
      /** 追加サービス1の利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
        tabData.kaisu25
      /** 追加サービス2の利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
        tabData.kaisu26
      // 追加サービス3の利用回数
      local.or32646TypeBottomRight.beingAtHomeUseItems[2].textNumberValue.mo00045.value =
        tabData.kaisu28
      // 追加サービス4の利用回数
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
        tabData.kaisu29

      // 健康手帳の交付
      local.or32648Type.elderlyHealthProject.checkItems[0].checkboxValue.modelValue =
        tabData.seido20 === '1'
      // 健康診査
      local.or32648Type.elderlyHealthProject.checkItems[1].checkboxValue.modelValue =
        tabData.seido23 === '1'
    }
  }
  // R３/４改訂版の場合
  else {
    if (resData.data.cpnTucGdl5SerInfo) {
      // 改訂フラグ5情報
      tabData = resData.data.cpnTucGdl5SerInfo

      // 情報を設定
      // 在宅利用セクション
      // 左上部
      /** 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ) */
      local.or32646TypeTopLeft.beingAtHomeUseItems[0].checkboxValue.modelValue =
        tabData.ser1Cd === '1'
      /** 訪問型サービス */
      local.or32646TypeTopLeft.beingAtHomeUseItems[1].checkboxValue.modelValue =
        tabData.ser38Cd === '1'
      /** (介護予防)訪問入浴介護 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[2].checkboxValue.modelValue =
        tabData.ser2Cd === '1'
      /** (介護予防)訪問看護 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[3].checkboxValue.modelValue =
        tabData.ser3Cd === '1'
      /** (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝ */
      local.or32646TypeTopLeft.beingAtHomeUseItems[4].checkboxValue.modelValue =
        tabData.ser4Cd === '1'
      /** (介護予防)居宅療養管理指導 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[5].checkboxValue.modelValue =
        tabData.ser5Cd === '1'
      /** 通所介護（ﾃﾞｲｻｰﾋﾞｽ） */
      local.or32646TypeTopLeft.beingAtHomeUseItems[6].checkboxValue.modelValue =
        tabData.ser6Cd === '1'
      /** (介護予防)通所型サービス */
      local.or32646TypeTopLeft.beingAtHomeUseItems[7].checkboxValue.modelValue =
        tabData.ser39Cd === '1'
      /** (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ） */
      local.or32646TypeTopLeft.beingAtHomeUseItems[8].checkboxValue.modelValue =
        tabData.ser7Cd === '1'
      /** (介護予防)短期入所生活介護(特養等） */
      local.or32646TypeTopLeft.beingAtHomeUseItems[9].checkboxValue.modelValue =
        tabData.ser10Cd === '1'
      /** (介護予防)短期入所生活介護(老健・診療所） */
      local.or32646TypeTopLeft.beingAtHomeUseItems[10].checkboxValue.modelValue =
        tabData.ser11Cd === '1'
      /** 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)の利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[0].textNumberValue.mo00045.value = tabData.kaisu1
      /** 訪問型サービスの利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
        tabData.kaisu38
      /** (介護予防)訪問入浴介護の利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[2].textNumberValue.mo00045.value = tabData.kaisu2
      /** (介護予防)訪問看護の利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[3].textNumberValue.mo00045.value = tabData.kaisu3
      /** (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝの利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[4].textNumberValue.mo00045.value = tabData.kaisu4
      /** (介護予防)居宅療養管理指導の利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[5].textNumberValue.mo00045.value = tabData.kaisu5
      /** 通所介護（ﾃﾞｲｻｰﾋﾞｽ）の利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[6].textNumberValue.mo00045.value = tabData.kaisu6
      /** (介護予防)通所型サービスの利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[7].textNumberValue.mo00045.value =
        tabData.kaisu39
      /** (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）の利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[8].textNumberValue.mo00045.value = tabData.kaisu7
      /** (介護予防)短期入所生活介護(特養等）の利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[9].textNumberValue.mo00045.value = tabData.kaisu9
      /** (介護予防)短期入所生活介護(老健・診療所）の利用回数 */
      local.or32646TypeTopLeft.beingAtHomeUseItems[10].textNumberValue.mo00045.value =
        tabData.kaisu10

      // 右上部
      /** (介護予防)特定施設入所生活介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
        tabData.ser13Cd === '1'
      /** 複合型サービス / 看護小規模多機能型居宅介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[1].checkboxValue.modelValue =
        tabData.ser36Cd === '1'
      /** (介護予防)福祉用具貸与 */
      local.or32646TypeTopRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
        tabData.ser9Cd === '1'
      /** 特定(介護予防)福祉用具販売 */
      local.or32646TypeTopRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
        tabData.ser14Cd === '1'
      /** 住宅改修 */
      local.or32646TypeTopRight.beingAtHomeUseItems[4].checkboxValue.modelValue =
        tabData.ser15Cd === '1'
      /** 夜間対応訪問介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[5].checkboxValue.modelValue =
        tabData.ser31Cd === '1'
      /** 認知症対応型通所介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[6].checkboxValue.modelValue =
        tabData.ser32Cd === '1'
      /** 小規模多機能居宅介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[7].checkboxValue.modelValue =
        tabData.ser33Cd === '1'
      /** 認知症対応型共同生活介護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[8].checkboxValue.modelValue =
        tabData.ser12Cd === '1'
      /** 定期巡回・随時対応型訪問介護看護 */
      local.or32646TypeTopRight.beingAtHomeUseItems[9].checkboxValue.modelValue =
        tabData.ser37Cd === '1'
      /** その他の生活支援サービス */
      local.or32646TypeTopRight.beingAtHomeUseItems[10].checkboxValue.modelValue =
        tabData.ser40Cd === '1'
      /** その他の生活支援サービス（名称） */
      local.or32646TypeTopRight.beingAtHomeUseItems[10].textInputValue.value = tabData.ser40NameKnj
      /** その他の生活支援サービス（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[10].textNumberValue.mo00045.value =
        tabData.kaisu40
      /** (介護予防)特定施設入所生活介護の利用回数 */
      local.or32646TypeTopRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
        tabData.kaisu12
      /** 看護小規模多機能型居宅介護の利用回数 */
      local.or32646TypeTopRight.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
        tabData.kaisu36
      /** (介護予防)福祉用具貸与の利用回数 */
      local.or32646TypeTopRight.beingAtHomeUseItems[2].textNumberValue.mo00045.value =
        tabData.kaisu8
      /** 特定(介護予防)福祉用具販売の利用回数 */
      local.or32646TypeTopRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
        tabData.kaisu13
      // 住宅改修の利用回数
      local.or32646TypeTopRight.beingAtHomeUseItems[4].radioValue = tabData.kaisu15
      /** 夜間対応訪問介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[5].textNumberValue.mo00045.value =
        tabData.kaisu31
      /** 認知症対応型通所介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
        tabData.kaisu32
      /** 小規模多機能居宅介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[7].textNumberValue.mo00045.value =
        tabData.kaisu33
      /** 認知症対応型共同生活介護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[8].textNumberValue.mo00045.value =
        tabData.kaisu11
      /** 定期巡回・随時対応型訪問介護看護（回数） */
      local.or32646TypeTopRight.beingAtHomeUseItems[9].textNumberValue.mo00045.value =
        tabData.kaisu37

      // 右下部
      /** 生活支援員の訪問 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
        tabData.ser17Cd === '1'
      /** ふれあい・いきいきサロン */
      local.or32646TypeBottomRight.beingAtHomeUseItems[1].checkboxValue.modelValue =
        tabData.ser18Cd === '1'
      /** 市町村特別給付 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
        tabData.ser8Cd === '1'
      /** 追加サービス1 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
        tabData.ser27Cd === '1'
      /** 追加サービス2 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[4].checkboxValue.modelValue =
        tabData.ser28Cd === '1'
      /** 追加サービス1メモ */
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].textareaInputValue.value =
        tabData.addSer1Knj
      /** 追加サービス2メモ */
      local.or32646TypeBottomRight.beingAtHomeUseItems[4].textareaInputValue.value =
        tabData.addSer2Knj
      /** 生活支援員の訪問の利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
        tabData.kaisu16
      /** ふれあい・いきいきサロンの利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
        tabData.kaisu17
      /** 市町村特別給付メモ */
      local.or32646TypeBottomRight.beingAtHomeUseItems[2].textInputValue.value = tabData.tokKyuKnj
      /** 追加サービス1の利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
        tabData.kaisu25
      /** 追加サービス2の利用回数 */
      local.or32646TypeBottomRight.beingAtHomeUseItems[4].textNumberValue.mo00045.value =
        tabData.kaisu26

      // 制度利用30（後期高齢者医療）
      local.or32648Type.healthInsurance.insuranceCheckItems[8].checkboxValue.modelValue =
        tabData.seido30 === '1'

      /** 制度利用31（その他1） */
      local.or32648Type.otherItem.otherCheckItems[0].checkboxValue.modelValue =
        tabData.seido31 === '1'
      /** 制度利用32（その他2） */
      local.or32648Type.otherItem.otherCheckItems[1].checkboxValue.modelValue =
        tabData.seido32 === '1'
      /** 制度利用33（その他3） */
      local.or32648Type.otherItem.otherCheckItems[2].checkboxValue.modelValue =
        tabData.seido33 === '1'
      /** 制度利用ﾒﾓ5（その他1（メモ）） */
      local.or32648Type.otherItem.systemUseTextValue[0].value = tabData.riyoMemo5Knj
      /** 制度利用ﾒﾓ6（その他2（メモ）） */
      local.or32648Type.otherItem.systemUseTextValue[1].value = tabData.riyoMemo6Knj
      /** 制度利用ﾒﾓ7（その他3（メモ）） */
      local.or32648Type.otherItem.systemUseTextValue[2].value = tabData.riyoMemo7Knj
    }
  }
}

/**
 *  保存処理
 *
 */
async function _save(): Promise<boolean> {
  // ロード開始
  isLoading.value = true

  if (updateKbn.value === UPDATE_KBN.NONE) {
    updateKbn.value = UPDATE_KBN.UPDATE
  }

  const childCpBindsData = getChildCpBinds(props.uniqueCpId, {
    // 在宅利用項目(左上)
    [Or32646Const.CP_ID(0)]: { cpPath: Or32646Const.CP_ID(0), twoWayFlg: true },
    // 在宅利用項目(右上)
    [Or32646Const.CP_ID(1)]: { cpPath: Or32646Const.CP_ID(1), twoWayFlg: true },
    // 在宅利用項目(左下)
    [Or32646Const.CP_ID(2)]: { cpPath: Or32646Const.CP_ID(2), twoWayFlg: true },
    // 在宅利用項目(右下)
    [Or32646Const.CP_ID(3)]: { cpPath: Or32646Const.CP_ID(3), twoWayFlg: true },
    // 直近の入所・入院一覧
    [Or32647Const.CP_ID(0)]: { cpPath: Or32647Const.CP_ID(0), twoWayFlg: true },
    // 制度利用状況一覧
    [Or32648Const.CP_ID(0)]: { cpPath: Or32648Const.CP_ID(0), twoWayFlg: true },
    // 課題と目標リスト
    [OrX0209Const.CP_ID(0)]: { cpPath: OrX0209Const.CP_ID(0), twoWayFlg: true },
  })

  // 在宅利用項目(左上)データ
  const or32646TopLeftData = childCpBindsData[Or32646Const.CP_ID(0)].twoWayBind
    ?.value as Or32646Type
  // 在宅利用項目(右上)データ
  const or32646TopRightData = childCpBindsData[Or32646Const.CP_ID(1)].twoWayBind
    ?.value as Or32646Type
  // 在宅利用項目(左下)データ
  const or32646BottomLeftData = childCpBindsData[Or32646Const.CP_ID(2)].twoWayBind
    ?.value as Or32646Type
  // 在宅利用項目(右下)データ
  const or32646BottomRightData = childCpBindsData[Or32646Const.CP_ID(3)].twoWayBind
    ?.value as Or32646Type

  // 直近の入所・入院一覧データ
  const or32647Data = childCpBindsData[Or32647Const.CP_ID(0)].twoWayBind?.value as Or32647Type
  // 制度利用状況一覧データ
  const or32648Data = childCpBindsData[Or32648Const.CP_ID(0)].twoWayBind?.value as Or32648Type
  // 課題と目標リスト
  const changedIssuesAndGoalsList = childCpBindsData[OrX0209Const.CP_ID(0)].twoWayBind
    ?.value as OrX0209Type

  // 更新データ作成
  const inputData: AssessmentHomeServiceUpdateInEntity = {
    /** タブID */
    tabId: Or32643Const.DEFAULT.TAB_ID,
    /** 機能ID */
    kinoId: systemCommonsStore.getFunctionId ?? '',
    /** 当履歴ページ番号 */
    krirekiNo: local.commonInfo.historyNo,
    /** e文書用パラメータ */
    edocumentUseParam: useCmnCom().getEDocumentParam(),
    /** e文書削除用パラメータ */
    edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
    /** 期間対象フラグ */
    kikanFlg: local.commonInfo.kikanKanriFlg,
    /** 計画対象期間番号 */
    planningPeriodNo: local.commonInfo.sc1No,
    /** 開始日 */
    startYmd: local.commonInfo.kikanKanriFlg === '1' ? local.commonInfo.periodStartYmd : '',
    /** 終了日 */
    endYmd: local.commonInfo.kikanKanriFlg === '1' ? local.commonInfo.periodEndYmd : '',
    /** ガイドラインまとめ */
    matomeFlg: '1',
    /** ログインID */
    loginId: systemCommonsStore.getCurrentUser?.loginId ?? '',
    /** システム略称 */
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    /** 職員ID */
    shokuId: local.commonInfo.createUserId ?? '',
    /** システムコード */
    sysCd: systemCommonsStore.getSystemCode ?? '0',
    /** 事業者名 */
    svJigyoKnj: local.commonInfo.jigyoId,
    /** 作成者名 */
    createUserName: local.commonInfo.createUserName ?? '0',
    /** 利用者名 */
    userName:
      systemCommonsStore.getUserSelectUserInfo()?.nameSei +
      ' ' +
      systemCommonsStore.getUserSelectUserInfo()?.nameMei,
    /** 法人ID */
    houjinId: local.commonInfo.houjinId,
    /** 施設ID */
    shisetuId: local.commonInfo.shisetuId,
    /** 利用者ID */
    userId: local.commonInfo.userId,
    /** 事業者ID */
    svJigyoId: local.commonInfo.jigyoId,
    /** 種別ID */
    syubetsuId: local.commonInfo.syubetuId,
    /** 更新区分 */
    updateKbn: updateKbn.value,
    /** 履歴更新区分 */
    historyUpdateKbn: local.commonInfo.historyUpdateKbn,
    /** 削除処理区分 */
    deleteKbn: Or32643Const.DEFAULT.DELETE_KBN_DONT,
    /** 計画対象期間ID */
    sc1Id: local.commonInfo.sc1Id,
    /** アセスメントID */
    gdlId: local.commonInfo.gdlId,
    /** 作成日 */
    kijunbiYmd: local.commonInfo.createYmd,
    /** 作成者ID */
    sakuseiId: local.commonInfo.createUserId,
    /** 改定フラグ */
    ninteiFormF: local.commonInfo.ninteiFormF,

    /** 課題と目標リスト */
    kadaiList: [] as {
      /** id */
      id: ''
      /** アセスメント番号 */
      assNo: ''
      /** 課題 */
      kadaiKnj: ''
      /** 長期 */
      choukiKnj: ''
      /** 短期 */
      tankiKnj: ''
      /** 連番 */
      seq: ''
      /** 更新区分 */
      updateKbn: ''
    }[],
  }

  const createDateNumber = new Date(local.commonInfo.createYmd ?? '').getTime()
  const basicDate01Number = new Date(Or32643Const.DEFAULT.CREATE_YMD_2018_4_1).getTime()
  const basicDate02Number = new Date(Or32643Const.DEFAULT.CREATE_YMD_2012_4_1).getTime()

  // 共有info
  const commonSerInfo = {
    // サービス利用状況記載日
    serYmd: local.timePointYmd.value,
    /** 左上部 */
    // 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ) | (介護予防)訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)
    ser1Cd: or32646TopLeftData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
    // (介護予防)訪問入浴介護
    ser2Cd: or32646TopLeftData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
    // (介護予防)訪問看護
    ser3Cd: or32646TopLeftData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
    // (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝ
    ser4Cd: or32646TopLeftData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
    // (介護予防)居宅療養管理指導
    ser5Cd: or32646TopLeftData.beingAtHomeUseItems[4].checkboxValue.modelValue ? '1' : '0',
    // 通所介護（ﾃﾞｲｻｰﾋﾞｽ）| (介護予防)通所介護（ﾃﾞｲｻｰﾋﾞｽ）
    ser6Cd: or32646TopLeftData.beingAtHomeUseItems[5].checkboxValue.modelValue ? '1' : '0',
    // (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）
    ser7Cd: or32646TopLeftData.beingAtHomeUseItems[6].checkboxValue.modelValue ? '1' : '0',
    // (介護予防)短期入所生活介護(特養等）
    ser10Cd: or32646TopLeftData.beingAtHomeUseItems[7].checkboxValue.modelValue ? '1' : '0',
    // (介護予防)短期入所生活介護(老健・診療所）
    ser11Cd: or32646TopLeftData.beingAtHomeUseItems[8].checkboxValue.modelValue ? '1' : '0',
    // (介護予防)特定施設入所生活介護
    ser13Cd: or32646TopLeftData.beingAtHomeUseItems[9].checkboxValue.modelValue ? '1' : '0',
    // 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)の利用回数
    kaisu1: or32646TopLeftData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
    // (介護予防)訪問入浴介護の利用回数
    kaisu2: or32646TopLeftData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
    // (介護予防)訪問看護の利用回数
    kaisu3: or32646TopLeftData.beingAtHomeUseItems[2].textNumberValue.mo00045.value,
    // (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝの利用回数
    kaisu4: or32646TopLeftData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,
    // (介護予防)居宅療養管理指導の利用回数
    kaisu5: or32646TopLeftData.beingAtHomeUseItems[4].textNumberValue.mo00045.value,
    // 通所介護（ﾃﾞｲｻｰﾋﾞｽ）の利用回数
    kaisu6: or32646TopLeftData.beingAtHomeUseItems[5].textNumberValue.mo00045.value,
    // (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）の利用回数
    kaisu7: or32646TopLeftData.beingAtHomeUseItems[6].textNumberValue.mo00045.value,
    // (介護予防)短期入所生活介護(特養等）の利用回数
    kaisu9: or32646TopLeftData.beingAtHomeUseItems[7].textNumberValue.mo00045.value,
    // (介護予防)短期入所生活介護(老健・診療所）の利用回数
    kaisu10: or32646TopLeftData.beingAtHomeUseItems[8].textNumberValue.mo00045.value,
    // (介護予防)特定施設入所生活介護の利用回数
    kaisu12: or32646TopLeftData.beingAtHomeUseItems[9].textNumberValue.mo00045.value,

    /** 左下部 */
    // 配食サービス / 訪問食事サービス
    ser19Cd: or32646BottomLeftData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
    // 洗濯サービス
    ser20Cd: or32646BottomLeftData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
    // 移動または外出支援 / 移送サービス
    ser21Cd: or32646BottomLeftData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
    // 友愛訪問
    ser22Cd: or32646BottomLeftData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
    // 老人福祉センター
    ser23Cd: or32646BottomLeftData.beingAtHomeUseItems[4].checkboxValue.modelValue ? '1' : '0',
    // 老人憩いの家
    ser24Cd: or32646BottomLeftData.beingAtHomeUseItems[5].checkboxValue.modelValue ? '1' : '0',
    // ガイドヘルパー
    ser25Cd: or32646BottomLeftData.beingAtHomeUseItems[6].checkboxValue.modelValue ? '1' : '0',
    // 身障／補助具・日常生活用具
    ser26Cd: or32646BottomLeftData.beingAtHomeUseItems[7].checkboxValue.modelValue ? '1' : '0',
    // 配食サービスの利用回数
    kaisu18: or32646BottomLeftData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
    // 洗濯サービスの利用回数
    kaisu19: or32646BottomLeftData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
    // 移動または外出支援の利用回数 / 移送サービスの利用回数
    kaisu20: or32646BottomLeftData.beingAtHomeUseItems[2].textNumberValue.mo00045.value,
    // 友愛訪問の利用回数
    kaisu21: or32646BottomLeftData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,
    // 老人福祉センターの利用回数
    kaisu22: or32646BottomLeftData.beingAtHomeUseItems[4].textNumberValue.mo00045.value,
    // 老人憩いの家の利用回数
    kaisu23: or32646BottomLeftData.beingAtHomeUseItems[5].textNumberValue.mo00045.value,
    // ガイドヘルパーの利用回数
    kaisu24: or32646BottomLeftData.beingAtHomeUseItems[6].textNumberValue.mo00045.value,
    // 身障／補助具・日常生活用具メモ
    youguKnj: or32646BottomLeftData.beingAtHomeUseItems[7].textInputValue.value,

    // 利用施設種別
    shisetsuShu: or32647Data.useFacilityType.raidoValue,
    // 施設名
    shisetsuNameKnj: or32647Data.facilityInstitution.twowayValue.modelValue ?? '',
    // 郵便番号
    shisetsuZip: or32647Data.zipCode.value,
    // 住所（以前：備考）
    shisetsuMemoKnj: or32647Data.remarks.value,
    // 電話番号
    shisetsuTel: or32647Data.telNumber.value,

    // 老齢関係
    seido1: or32648Data.pensionItem.pensionCheckItems[0].checkboxValue.modelValue ? '1' : '0',
    // 障害関係
    seido2: or32648Data.pensionItem.pensionCheckItems[1].checkboxValue.modelValue ? '1' : '0',
    // 遺族・寡婦
    seido3: or32648Data.pensionItem.pensionCheckItems[2].checkboxValue.modelValue ? '1' : '0',
    // 老齢関係メモ
    riyoMemo1Knj: or32648Data.pensionItem.systemUseTextValue[0].value,
    // 障害関係メモ
    riyoMemo2Knj: or32648Data.pensionItem.systemUseTextValue[1].value,
    // 遺族・寡婦メモ
    riyoMemo3Knj: or32648Data.pensionItem.systemUseTextValue[2].value,
    // 恩給
    seido4: or32648Data.otherCheckBoxItem.otherCheckItems[0].checkboxValue.modelValue ? '1' : '0',
    // 特別障害者手当
    seido5: or32648Data.otherCheckBoxItem.otherCheckItems[1].checkboxValue.modelValue ? '1' : '0',
    // 生活保護
    seido6: or32648Data.otherCheckBoxItem.otherCheckItems[2].checkboxValue.modelValue ? '1' : '0',
    // 生活福祉資金貸付
    seido7: or32648Data.otherCheckBoxItem.otherCheckItems[3].checkboxValue.modelValue ? '1' : '0',
    // 高齢者住宅整備資金貸付
    seido8: or32648Data.otherCheckBoxItem.otherCheckItems[4].checkboxValue.modelValue ? '1' : '0',
    // 日常生活自立支援事業
    seido9: or32648Data.otherCheckBoxItem.otherCheckItems[5].checkboxValue.modelValue ? '1' : '0',
    // 成年後見人制度
    seido10: or32648Data.adultObserverSystemItem.checkboxValue.modelValue ? '1' : '0',
    // 成年後見人制度区分
    seinenKoukenKbn: or32648Data.adultObserverSystemItem.raidoValue,
    // 成年後見人等
    kokenKnj: or32648Data.adultObserverInputValue.value,

    // 国保
    seido11: or32648Data.healthInsurance.insuranceCheckItems[0].checkboxValue.modelValue
      ? '1'
      : '0',
    // 組合保健
    seido12: or32648Data.healthInsurance.insuranceCheckItems[2].checkboxValue.modelValue
      ? '1'
      : '0',
    // 国公共済
    seido13: or32648Data.healthInsurance.insuranceCheckItems[4].checkboxValue.modelValue
      ? '1'
      : '0',
    // 私立学校共済
    seido14: or32648Data.healthInsurance.insuranceCheckItems[6].checkboxValue.modelValue
      ? '1'
      : '0',
    // 協会けんぼ（旧・政管健保）
    seido15: or32648Data.healthInsurance.insuranceCheckItems[1].checkboxValue.modelValue
      ? '1'
      : '0',
    // 日雇い
    seido16: or32648Data.healthInsurance.insuranceCheckItems[3].checkboxValue.modelValue
      ? '1'
      : '0',
    // 地方共済
    seido17: or32648Data.healthInsurance.insuranceCheckItems[5].checkboxValue.modelValue
      ? '1'
      : '0',
    // 船員
    seido18: or32648Data.healthInsurance.insuranceCheckItems[7].checkboxValue.modelValue
      ? '1'
      : '0',

    // 労災保険
    seido19: or32648Data.compensationInsurance.insuranceCheckItems.checkboxValue.modelValue
      ? '1'
      : '0',
    // 労災保険メモ
    riyoMemo4Knj: or32648Data.compensationInsurance.systemUseTextValue.value,
  }
  // H21/４改訂版の場合
  if (local.commonInfo.ninteiFormF === Or32643Const.DEFAULT.NINTEI_FORM_FLG_H21_4) {
    // 下記部分項目は共有infoを参照
    // 作成日が2018年4月1日以降の場合
    if (createDateNumber >= basicDate01Number) {
      inputData.serInfo = {
        ...commonSerInfo,
        /** 左上部 */
        // 複合型サービス / 看護小規模多機能型居宅介護
        ser36Cd: or32646TopLeftData.beingAtHomeUseItems[10].checkboxValue.modelValue ? '1' : '0',
        // 複合型サービス（回数） / 看護小規模多機能型居宅介護（回数）
        kaisu36: or32646TopLeftData.beingAtHomeUseItems[10].textNumberValue.mo00045.value,

        /** 右上部 */
        // (介護予防)福祉用具貸与
        ser9Cd: or32646TopRightData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
        // 特定(介護予防)福祉用具販売
        ser14Cd: or32646TopRightData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
        // 住宅改修
        ser15Cd: or32646TopRightData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
        // 夜間対応訪問介護
        ser31Cd: or32646TopRightData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
        // 認知症対応型通所介護
        ser32Cd: or32646TopRightData.beingAtHomeUseItems[4].checkboxValue.modelValue ? '1' : '0',
        // 小規模多機能居宅介護
        ser33Cd: or32646TopRightData.beingAtHomeUseItems[5].checkboxValue.modelValue ? '1' : '0',
        // 認知症対応型共同生活介護
        ser12Cd: or32646TopRightData.beingAtHomeUseItems[6].checkboxValue.modelValue ? '1' : '0',
        // 定期巡回・随時対応型訪問介護看護
        ser37Cd: or32646TopRightData.beingAtHomeUseItems[7].checkboxValue.modelValue ? '1' : '0',
        // (介護予防)福祉用具貸与の利用回数
        kaisu8: or32646TopRightData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
        // 特定(介護予防)福祉用具販売の利用回数
        kaisu13: or32646TopRightData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
        // 住宅改修の利用回数
        kaisu15: or32646TopRightData.beingAtHomeUseItems[2].raidoValue,
        // 夜間対応訪問介護（回数）
        kaisu31: or32646TopRightData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,
        // 認知症対応型通所介護（回数）
        kaisu32: or32646TopRightData.beingAtHomeUseItems[4].textNumberValue.mo00045.value,
        // 小規模多機能居宅介護（回数）
        kaisu33: or32646TopRightData.beingAtHomeUseItems[5].textNumberValue.mo00045.value,
        // 認知症対応型共同生活介護（回数）
        kaisu11: or32646TopRightData.beingAtHomeUseItems[6].textNumberValue.mo00045.value,
        // 定期巡回・随時対応型訪問介護看護（回数）
        kaisu37: or32646TopRightData.beingAtHomeUseItems[7].textNumberValue.mo00045.value,

        /** 右下部 */
        // 生活支援員の訪問
        ser17Cd: or32646BottomRightData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
        // ふれあい・いきいきサロン
        ser18Cd: or32646BottomRightData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
        // 市町村特別給付
        ser8Cd: or32646BottomRightData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
        // 追加サービス1
        ser27Cd: or32646BottomRightData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
        // 追加サービス2
        ser28Cd: or32646BottomRightData.beingAtHomeUseItems[4].checkboxValue.modelValue ? '1' : '0',
        // 生活支援員の訪問の利用回数
        kaisu16: or32646BottomRightData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
        // ふれあい・いきいきサロンの利用回数
        kaisu17: or32646BottomRightData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
        // 市町村特別給付メモ
        tokKyuKnj: or32646BottomRightData.beingAtHomeUseItems[2].textInputValue.value,
        // 追加サービス1メモ
        addSer1Knj: or32646BottomRightData.beingAtHomeUseItems[3].textareaInputValue.value,
        // 追加サービス2メモ
        addSer2Knj: or32646BottomRightData.beingAtHomeUseItems[4].textareaInputValue.value,
        // 追加サービス1の利用回数
        kaisu25: or32646BottomRightData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,
        // 追加サービス2の利用回数
        kaisu26: or32646BottomRightData.beingAtHomeUseItems[4].textNumberValue.mo00045.value,

        // 制度利用30（後期高齢者医療）
        seido30: or32648Data.healthInsurance.insuranceCheckItems[8].checkboxValue.modelValue
          ? '1'
          : '0',
        // 制度利用31（その他1）
        seido31: or32648Data.otherItem.otherCheckItems[0].checkboxValue.modelValue ? '1' : '0',
        // 制度利用32（その他2）
        seido32: or32648Data.otherItem.otherCheckItems[1].checkboxValue.modelValue ? '1' : '0',
        // 制度利用33（その他3）
        seido33: or32648Data.otherItem.otherCheckItems[2].checkboxValue.modelValue ? '1' : '0',
        // 制度利用ﾒﾓ5（その他1（メモ））
        riyoMemo5Knj: or32648Data.otherItem.systemUseTextValue[0].value,
        // 制度利用ﾒﾓ6（その他2（メモ））
        riyoMemo6Knj: or32648Data.otherItem.systemUseTextValue[1].value,
        // 制度利用ﾒﾓ7（その他3（メモ））
        riyoMemo7Knj: or32648Data.otherItem.systemUseTextValue[2].value,
        // 補足
        ser29Cd: initData.data.cpnTucGdl4SerInfo.ser29Cd,
        ser30Cd: initData.data.cpnTucGdl4SerInfo.ser30Cd,
        kaisu28: initData.data.cpnTucGdl4SerInfo.kaisu28,
        kaisu29: initData.data.cpnTucGdl4SerInfo.kaisu29,
        addSer3Knj: initData.data.cpnTucGdl4SerInfo.addSer3Knj,
        addSer4Knj: initData.data.cpnTucGdl4SerInfo.addSer4Knj,
        seido20: initData.data.cpnTucGdl4SerInfo.seido20,
        seido23: initData.data.cpnTucGdl4SerInfo.seido23,
        ser34Cd: initData.data.cpnTucGdl4SerInfo.ser34Cd,
        ser35Cd: initData.data.cpnTucGdl4SerInfo.ser35Cd,
        kaisu34: initData.data.cpnTucGdl4SerInfo.kaisu34,
        kaisu35: initData.data.cpnTucGdl4SerInfo.kaisu35,
      }
    }
    // 作成日が2018年4月1日以前 且つ 作成日が2012年4月1日以降
    else if (createDateNumber < basicDate01Number && createDateNumber >= basicDate02Number) {
      inputData.serInfo = {
        ...commonSerInfo,
        /** 左上部 */
        // 複合型サービス / 看護小規模多機能型居宅介護
        ser36Cd: or32646TopLeftData.beingAtHomeUseItems[10].checkboxValue.modelValue ? '1' : '0',
        // 市町村特別給付
        ser8Cd: or32646TopLeftData.beingAtHomeUseItems[11].checkboxValue.modelValue ? '1' : '0',
        // 複合型サービス（回数） / 看護小規模多機能型居宅介護（回数）
        kaisu36: or32646TopLeftData.beingAtHomeUseItems[10].textNumberValue.mo00045.value,
        // 市町村特別給付メモ
        tokKyuKnj: or32646TopLeftData.beingAtHomeUseItems[11].textInputValue.value,

        /** 右上部 */
        // (介護予防)福祉用具貸与
        ser9Cd: or32646TopRightData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
        // 特定(介護予防)福祉用具販売
        ser14Cd: or32646TopRightData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
        // 住宅改修
        ser15Cd: or32646TopRightData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
        // 夜間対応訪問介護
        ser31Cd: or32646TopRightData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
        // 認知症対応型通所介護
        ser32Cd: or32646TopRightData.beingAtHomeUseItems[4].checkboxValue.modelValue ? '1' : '0',
        // 小規模多機能居宅介護
        ser33Cd: or32646TopRightData.beingAtHomeUseItems[5].checkboxValue.modelValue ? '1' : '0',
        // 認知症対応型共同生活介護
        ser12Cd: or32646TopRightData.beingAtHomeUseItems[6].checkboxValue.modelValue ? '1' : '0',
        // 地域密着型特定施設入居者生活介護
        ser34Cd: or32646TopRightData.beingAtHomeUseItems[7].checkboxValue.modelValue ? '1' : '0',
        // 地域密着型介護老人福祉施設入居者生活介護
        ser35Cd: or32646TopRightData.beingAtHomeUseItems[8].checkboxValue.modelValue ? '1' : '0',
        // 定期巡回・随時対応型訪問介護看護
        ser37Cd: or32646TopRightData.beingAtHomeUseItems[9].checkboxValue.modelValue ? '1' : '0',
        // 生活支援員の訪問
        ser17Cd: or32646TopRightData.beingAtHomeUseItems[10].checkboxValue.modelValue ? '1' : '0',
        // ふれあい・いきいきサロン
        ser18Cd: or32646TopRightData.beingAtHomeUseItems[11].checkboxValue.modelValue ? '1' : '0',
        // (介護予防)福祉用具貸与の利用回数
        kaisu8: or32646TopRightData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
        // 特定(介護予防)福祉用具販売の利用回数
        kaisu13: or32646TopRightData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
        // 住宅改修の利用回数
        kaisu15: or32646TopRightData.beingAtHomeUseItems[2].raidoValue,
        // 夜間対応訪問介護（回数）
        kaisu31: or32646TopRightData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,
        // 認知症対応型通所介護（回数）
        kaisu32: or32646TopRightData.beingAtHomeUseItems[4].textNumberValue.mo00045.value,
        // 小規模多機能居宅介護（回数）
        kaisu33: or32646TopRightData.beingAtHomeUseItems[5].textNumberValue.mo00045.value,
        // 認知症対応型共同生活介護（回数）
        kaisu11: or32646TopRightData.beingAtHomeUseItems[6].textNumberValue.mo00045.value,
        // 地域密着型特定施設入居者生活介護（回数）
        kaisu34: or32646TopRightData.beingAtHomeUseItems[7].textNumberValue.mo00045.value,
        // 地域密着型介護老人福祉施設入居者生活介護（回数）
        kaisu35: or32646TopRightData.beingAtHomeUseItems[8].textNumberValue.mo00045.value,
        // 定期巡回・随時対応型訪問介護看護（回数）
        kaisu37: or32646TopRightData.beingAtHomeUseItems[9].textNumberValue.mo00045.value,
        // 生活支援員の訪問の利用回数
        kaisu16: or32646TopRightData.beingAtHomeUseItems[10].textNumberValue.mo00045.value,
        // ふれあい・いきいきサロンの利用回数
        kaisu17: or32646TopRightData.beingAtHomeUseItems[11].textNumberValue.mo00045.value,

        /** 右下部 */
        // 追加サービス1
        ser27Cd: or32646BottomRightData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
        // 追加サービス2
        ser28Cd: or32646BottomRightData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
        // 追加サービス3
        ser29Cd: or32646BottomRightData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
        // 追加サービス4
        ser30Cd: or32646BottomRightData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
        // 追加サービス1メモ
        addSer1Knj: or32646BottomRightData.beingAtHomeUseItems[0].textareaInputValue.value,
        // 追加サービス2メモ
        addSer2Knj: or32646BottomRightData.beingAtHomeUseItems[1].textareaInputValue.value,
        // 追加サービス3メモ
        addSer3Knj: or32646BottomRightData.beingAtHomeUseItems[2].textareaInputValue.value,
        // 追加サービス4メモ
        addSer4Knj: or32646BottomRightData.beingAtHomeUseItems[3].textareaInputValue.value,
        // 追加サービス1の利用回数
        kaisu25: or32646BottomRightData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
        // 追加サービス2の利用回数
        kaisu26: or32646BottomRightData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
        // 追加サービス3の利用回数
        kaisu28: or32646BottomRightData.beingAtHomeUseItems[2].textNumberValue.mo00045.value,
        // 追加サービス4の利用回数
        kaisu29: or32646BottomRightData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,

        // 制度利用30（後期高齢者医療）
        seido30: or32648Data.healthInsurance.insuranceCheckItems[8].checkboxValue.modelValue
          ? '1'
          : '0',
        // 制度利用31（その他1）
        seido31: or32648Data.otherItem.otherCheckItems[0].checkboxValue.modelValue ? '1' : '0',
        // 制度利用32（その他2）
        seido32: or32648Data.otherItem.otherCheckItems[1].checkboxValue.modelValue ? '1' : '0',
        // 制度利用33（その他3）
        seido33: or32648Data.otherItem.otherCheckItems[2].checkboxValue.modelValue ? '1' : '0',
        // 制度利用ﾒﾓ5（その他1（メモ））
        riyoMemo5Knj: or32648Data.otherItem.systemUseTextValue[0].value,
        // 制度利用ﾒﾓ6（その他2（メモ））
        riyoMemo6Knj: or32648Data.otherItem.systemUseTextValue[1].value,
        // 制度利用ﾒﾓ7（その他3（メモ））
        riyoMemo7Knj: or32648Data.otherItem.systemUseTextValue[2].value,

        // 補足
        seido20: initData.data.cpnTucGdl4SerInfo.seido20,
        seido23: initData.data.cpnTucGdl4SerInfo.seido23,
      }
    } else {
      inputData.serInfo = {
        ...commonSerInfo,
        /** 左上部 */
        // 複合型サービス / 看護小規模多機能型居宅介護
        ser36Cd: or32646TopLeftData.beingAtHomeUseItems[10].checkboxValue.modelValue ? '1' : '0',
        // 市町村特別給付
        ser8Cd: or32646TopLeftData.beingAtHomeUseItems[11].checkboxValue.modelValue ? '1' : '0',
        // 複合型サービス（回数） / 看護小規模多機能型居宅介護（回数）
        kaisu36: or32646TopLeftData.beingAtHomeUseItems[10].textNumberValue.mo00045.value,
        // 市町村特別給付メモ
        tokKyuKnj: or32646TopLeftData.beingAtHomeUseItems[11].textInputValue.value,

        /** 右上部 */
        // (介護予防)福祉用具貸与
        ser9Cd: or32646TopRightData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
        // 特定(介護予防)福祉用具販売
        ser14Cd: or32646TopRightData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
        // 住宅改修
        ser15Cd: or32646TopRightData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
        // 夜間対応訪問介護
        ser31Cd: or32646TopRightData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
        // 認知症対応型通所介護
        ser32Cd: or32646TopRightData.beingAtHomeUseItems[4].checkboxValue.modelValue ? '1' : '0',
        // 小規模多機能居宅介護
        ser33Cd: or32646TopRightData.beingAtHomeUseItems[5].checkboxValue.modelValue ? '1' : '0',
        // 認知症対応型共同生活介護
        ser12Cd: or32646TopRightData.beingAtHomeUseItems[6].checkboxValue.modelValue ? '1' : '0',
        // 地域密着型特定施設入居者生活介護
        ser34Cd: or32646TopRightData.beingAtHomeUseItems[7].checkboxValue.modelValue ? '1' : '0',
        // 地域密着型介護老人福祉施設入居者生活介護
        ser35Cd: or32646TopRightData.beingAtHomeUseItems[8].checkboxValue.modelValue ? '1' : '0',
        // 定期巡回・随時対応型訪問介護看護
        ser37Cd: or32646TopRightData.beingAtHomeUseItems[9].checkboxValue.modelValue ? '1' : '0',
        // 生活支援員の訪問
        ser17Cd: or32646TopRightData.beingAtHomeUseItems[10].checkboxValue.modelValue ? '1' : '0',
        // ふれあい・いきいきサロン
        ser18Cd: or32646TopRightData.beingAtHomeUseItems[11].checkboxValue.modelValue ? '1' : '0',
        // (介護予防)福祉用具貸与の利用回数
        kaisu8: or32646TopRightData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
        // 特定(介護予防)福祉用具販売の利用回数
        kaisu13: or32646TopRightData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
        // 住宅改修の利用回数
        kaisu15: or32646TopRightData.beingAtHomeUseItems[2].raidoValue,
        // 夜間対応訪問介護（回数）
        kaisu31: or32646TopRightData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,
        // 認知症対応型通所介護（回数）
        kaisu32: or32646TopRightData.beingAtHomeUseItems[4].textNumberValue.mo00045.value,
        // 小規模多機能居宅介護（回数）
        kaisu33: or32646TopRightData.beingAtHomeUseItems[5].textNumberValue.mo00045.value,
        // 認知症対応型共同生活介護（回数）
        kaisu11: or32646TopRightData.beingAtHomeUseItems[6].textNumberValue.mo00045.value,
        // 地域密着型特定施設入居者生活介護（回数）
        kaisu34: or32646TopRightData.beingAtHomeUseItems[7].textNumberValue.mo00045.value,
        // 地域密着型介護老人福祉施設入居者生活介護（回数）
        kaisu35: or32646TopRightData.beingAtHomeUseItems[8].textNumberValue.mo00045.value,
        // 定期巡回・随時対応型訪問介護看護（回数）
        kaisu37: or32646TopRightData.beingAtHomeUseItems[9].textNumberValue.mo00045.value,
        // 生活支援員の訪問の利用回数
        kaisu16: or32646TopRightData.beingAtHomeUseItems[10].textNumberValue.mo00045.value,
        // ふれあい・いきいきサロンの利用回数
        kaisu17: or32646TopRightData.beingAtHomeUseItems[11].textNumberValue.mo00045.value,

        /** 右下部 */
        // 追加サービス1
        ser27Cd: or32646BottomRightData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
        // 追加サービス2
        ser28Cd: or32646BottomRightData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
        // 追加サービス3
        ser29Cd: or32646BottomRightData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
        // 追加サービス4
        ser30Cd: or32646BottomRightData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
        // 追加サービス1メモ
        addSer1Knj: or32646BottomRightData.beingAtHomeUseItems[0].textareaInputValue.value,
        // 追加サービス2メモ
        addSer2Knj: or32646BottomRightData.beingAtHomeUseItems[1].textareaInputValue.value,
        // 追加サービス3メモ
        addSer3Knj: or32646BottomRightData.beingAtHomeUseItems[2].textareaInputValue.value,
        // 追加サービス4メモ
        addSer4Knj: or32646BottomRightData.beingAtHomeUseItems[3].textareaInputValue.value,
        // 追加サービス1の利用回数
        kaisu25: or32646BottomRightData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
        // 追加サービス2の利用回数
        kaisu26: or32646BottomRightData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
        // 追加サービス3の利用回数
        kaisu28: or32646BottomRightData.beingAtHomeUseItems[2].textNumberValue.mo00045.value,
        // 追加サービス4の利用回数
        kaisu29: or32646BottomRightData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,

        // 健康手帳の交付
        seido20: or32648Data.elderlyHealthProject.checkItems[0].checkboxValue.modelValue
          ? '1'
          : '0',
        // 健康診査
        seido23: or32648Data.elderlyHealthProject.checkItems[1].checkboxValue.modelValue
          ? '1'
          : '0',

        // 補足
        seido30: initData.data.cpnTucGdl4SerInfo.seido30,
        seido31: initData.data.cpnTucGdl4SerInfo.seido31,
        seido32: initData.data.cpnTucGdl4SerInfo.seido32,
        seido33: initData.data.cpnTucGdl4SerInfo.seido33,
        riyoMemo5Knj: initData.data.cpnTucGdl4SerInfo.riyoMemo5Knj,
        riyoMemo6Knj: initData.data.cpnTucGdl4SerInfo.riyoMemo6Knj,
        riyoMemo7Knj: initData.data.cpnTucGdl4SerInfo.riyoMemo7Knj,
      }
    }
  }
  // R３/４改訂版の場合 TODO
  else {
    inputData.serInfo = {
      // サービス利用状況記載日
      serYmd: local.timePointYmd.value,
      /** 左上部 */
      // 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ) | (介護予防)訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)
      ser1Cd: or32646TopLeftData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
      // (介護予防)訪問型サービス
      ser38Cd: or32646TopLeftData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
      // (介護予防)訪問入浴介護
      ser2Cd: or32646TopLeftData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
      // (介護予防)訪問看護
      ser3Cd: or32646TopLeftData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
      // (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝ
      ser4Cd: or32646TopLeftData.beingAtHomeUseItems[4].checkboxValue.modelValue ? '1' : '0',
      // (介護予防)居宅療養管理指導
      ser5Cd: or32646TopLeftData.beingAtHomeUseItems[5].checkboxValue.modelValue ? '1' : '0',
      // 通所介護（ﾃﾞｲｻｰﾋﾞｽ）| (介護予防)通所介護（ﾃﾞｲｻｰﾋﾞｽ）
      ser6Cd: or32646TopLeftData.beingAtHomeUseItems[6].checkboxValue.modelValue ? '1' : '0',
      // (介護予防)通所型サービス
      ser39Cd: or32646TopLeftData.beingAtHomeUseItems[7].checkboxValue.modelValue ? '1' : '0',
      // (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）
      ser7Cd: or32646TopLeftData.beingAtHomeUseItems[8].checkboxValue.modelValue ? '1' : '0',
      // (介護予防)短期入所生活介護(特養等）
      ser10Cd: or32646TopLeftData.beingAtHomeUseItems[9].checkboxValue.modelValue ? '1' : '0',
      // (介護予防)短期入所生活介護(老健・診療所）
      ser11Cd: or32646TopLeftData.beingAtHomeUseItems[10].checkboxValue.modelValue ? '1' : '0',
      // 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)の利用回数
      kaisu1: or32646TopLeftData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
      // (介護予防)訪問型サービスの利用回数
      kaisu38: or32646TopLeftData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
      // (介護予防)訪問入浴介護の利用回数
      kaisu2: or32646TopLeftData.beingAtHomeUseItems[2].textNumberValue.mo00045.value,
      // (介護予防)訪問看護の利用回数
      kaisu3: or32646TopLeftData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,
      // (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝの利用回数
      kaisu4: or32646TopLeftData.beingAtHomeUseItems[4].textNumberValue.mo00045.value,
      // (介護予防)居宅療養管理指導の利用回数
      kaisu5: or32646TopLeftData.beingAtHomeUseItems[5].textNumberValue.mo00045.value,
      // 通所介護（ﾃﾞｲｻｰﾋﾞｽ）の利用回数
      kaisu6: or32646TopLeftData.beingAtHomeUseItems[6].textNumberValue.mo00045.value,
      // (介護予防)通所型サービスの利用回数
      kaisu39: or32646TopLeftData.beingAtHomeUseItems[7].textNumberValue.mo00045.value,
      // (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）の利用回数
      kaisu7: or32646TopLeftData.beingAtHomeUseItems[8].textNumberValue.mo00045.value,
      // (介護予防)短期入所生活介護(特養等）の利用回数
      kaisu9: or32646TopLeftData.beingAtHomeUseItems[9].textNumberValue.mo00045.value,
      // (介護予防)短期入所生活介護(老健・診療所）の利用回数
      kaisu10: or32646TopLeftData.beingAtHomeUseItems[10].textNumberValue.mo00045.value,

      /** 右上部 */
      // (介護予防)特定施設入所生活介護
      ser13Cd: or32646TopRightData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
      // 複合型サービス / 看護小規模多機能型居宅介護
      ser36Cd: or32646TopRightData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
      // (介護予防)福祉用具貸与
      ser9Cd: or32646TopRightData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
      // 特定(介護予防)福祉用具販売
      ser14Cd: or32646TopRightData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
      // 住宅改修
      ser15Cd: or32646TopRightData.beingAtHomeUseItems[4].checkboxValue.modelValue ? '1' : '0',
      // 夜間対応訪問介護
      ser31Cd: or32646TopRightData.beingAtHomeUseItems[5].checkboxValue.modelValue ? '1' : '0',
      // 認知症対応型通所介護
      ser32Cd: or32646TopRightData.beingAtHomeUseItems[6].checkboxValue.modelValue ? '1' : '0',
      // 小規模多機能居宅介護
      ser33Cd: or32646TopRightData.beingAtHomeUseItems[7].checkboxValue.modelValue ? '1' : '0',
      // 認知症対応型共同生活介護
      ser12Cd: or32646TopRightData.beingAtHomeUseItems[8].checkboxValue.modelValue ? '1' : '0',
      // 定期巡回・随時対応型訪問介護看護
      ser37Cd: or32646TopRightData.beingAtHomeUseItems[9].checkboxValue.modelValue ? '1' : '0',
      // その他の生活支援サービス
      ser40Cd: or32646TopRightData.beingAtHomeUseItems[10].checkboxValue.modelValue ? '1' : '0',
      // その他の生活支援サービス（名称）
      ser40NameKnj: or32646TopRightData.beingAtHomeUseItems[10].textInputValue.value,
      // その他の生活支援サービス（回数）
      kaisu40: or32646TopRightData.beingAtHomeUseItems[10].textNumberValue.mo00045.value,
      // (介護予防)特定施設入所生活介護の利用回数
      kaisu12: or32646TopRightData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
      // 複合型サービス / 看護小規模多機能型居宅介護の利用回数
      kaisu36: or32646TopRightData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
      // (介護予防)福祉用具貸与の利用回数
      kaisu8: or32646TopRightData.beingAtHomeUseItems[2].textNumberValue.mo00045.value,
      // 特定(介護予防)福祉用具販売の利用回数
      kaisu13: or32646TopRightData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,
      // 住宅改修の利用回数
      kaisu15: or32646TopRightData.beingAtHomeUseItems[4].raidoValue,
      // 夜間対応訪問介護（回数）
      kaisu31: or32646TopRightData.beingAtHomeUseItems[5].textNumberValue.mo00045.value,
      // 認知症対応型通所介護（回数）
      kaisu32: or32646TopRightData.beingAtHomeUseItems[6].textNumberValue.mo00045.value,
      // 小規模多機能居宅介護（回数）
      kaisu33: or32646TopRightData.beingAtHomeUseItems[7].textNumberValue.mo00045.value,
      // 認知症対応型共同生活介護（回数）
      kaisu11: or32646TopRightData.beingAtHomeUseItems[8].textNumberValue.mo00045.value,
      // 定期巡回・随時対応型訪問介護看護（回数）
      kaisu37: or32646TopRightData.beingAtHomeUseItems[9].textNumberValue.mo00045.value,

      /** 左下部 */
      // 配食サービス / 訪問食事サービス
      ser19Cd: or32646BottomLeftData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
      // 洗濯サービス
      ser20Cd: or32646BottomLeftData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
      // 移動または外出支援 / 移送サービス
      ser21Cd: or32646BottomLeftData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
      // 友愛訪問
      ser22Cd: or32646BottomLeftData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
      // 老人福祉センター
      ser23Cd: or32646BottomLeftData.beingAtHomeUseItems[4].checkboxValue.modelValue ? '1' : '0',
      // 老人憩いの家
      ser24Cd: or32646BottomLeftData.beingAtHomeUseItems[5].checkboxValue.modelValue ? '1' : '0',
      // ガイドヘルパー
      ser25Cd: or32646BottomLeftData.beingAtHomeUseItems[6].checkboxValue.modelValue ? '1' : '0',
      // 身障／補助具・日常生活用具
      ser26Cd: or32646BottomLeftData.beingAtHomeUseItems[7].checkboxValue.modelValue ? '1' : '0',
      // 配食サービスの利用回数
      kaisu18: or32646BottomLeftData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
      // 洗濯サービスの利用回数
      kaisu19: or32646BottomLeftData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
      // 移動または外出支援の利用回数 / 移送サービスの利用回数
      kaisu20: or32646BottomLeftData.beingAtHomeUseItems[2].textNumberValue.mo00045.value,
      // 友愛訪問の利用回数
      kaisu21: or32646BottomLeftData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,
      // 老人福祉センターの利用回数
      kaisu22: or32646BottomLeftData.beingAtHomeUseItems[4].textNumberValue.mo00045.value,
      // 老人憩いの家の利用回数
      kaisu23: or32646BottomLeftData.beingAtHomeUseItems[5].textNumberValue.mo00045.value,
      // ガイドヘルパーの利用回数
      kaisu24: or32646BottomLeftData.beingAtHomeUseItems[6].textNumberValue.mo00045.value,
      // 身障／補助具・日常生活用具メモ
      youguKnj: or32646BottomLeftData.beingAtHomeUseItems[7].textInputValue.value,

      /** 右下部 */
      // 生活支援員の訪問
      ser17Cd: or32646BottomRightData.beingAtHomeUseItems[0].checkboxValue.modelValue ? '1' : '0',
      // ふれあい・いきいきサロン
      ser18Cd: or32646BottomRightData.beingAtHomeUseItems[1].checkboxValue.modelValue ? '1' : '0',
      // 市町村特別給付
      ser8Cd: or32646BottomRightData.beingAtHomeUseItems[2].checkboxValue.modelValue ? '1' : '0',
      // 追加サービス1
      ser27Cd: or32646BottomRightData.beingAtHomeUseItems[3].checkboxValue.modelValue ? '1' : '0',
      // 追加サービス2
      ser28Cd: or32646BottomRightData.beingAtHomeUseItems[4].checkboxValue.modelValue ? '1' : '0',
      // 生活支援員の訪問の利用回数
      kaisu16: or32646BottomRightData.beingAtHomeUseItems[0].textNumberValue.mo00045.value,
      // ふれあい・いきいきサロンの利用回数
      kaisu17: or32646BottomRightData.beingAtHomeUseItems[1].textNumberValue.mo00045.value,
      // 市町村特別給付メモ
      tokKyuKnj: or32646BottomRightData.beingAtHomeUseItems[2].textInputValue.value,
      // 追加サービス1メモ
      addSer1Knj: or32646BottomRightData.beingAtHomeUseItems[3].textareaInputValue.value,
      // 追加サービス2メモ
      addSer2Knj: or32646BottomRightData.beingAtHomeUseItems[4].textareaInputValue.value,
      // 追加サービス1の利用回数
      kaisu25: or32646BottomRightData.beingAtHomeUseItems[3].textNumberValue.mo00045.value,
      // 追加サービス2の利用回数
      kaisu26: or32646BottomRightData.beingAtHomeUseItems[4].textNumberValue.mo00045.value,

      /** 直近の入所・入院 */
      // 利用施設種別
      shisetsuShu: or32647Data.useFacilityType.raidoValue,
      // 施設名
      shisetsuNameKnj: or32647Data.facilityInstitution.twowayValue.modelValue ?? '',
      // 郵便番号
      shisetsuZip: or32647Data.zipCode.value,
      // 住所（以前：備考）
      shisetsuMemoKnj: or32647Data.remarks.value,
      // 電話番号
      shisetsuTel: or32647Data.telNumber.value,

      /** 制度利用状況 */
      // 老齢関係
      seido1: or32648Data.pensionItem.pensionCheckItems[0].checkboxValue.modelValue ? '1' : '0',
      // 障害関係
      seido2: or32648Data.pensionItem.pensionCheckItems[1].checkboxValue.modelValue ? '1' : '0',
      // 遺族・寡婦
      seido3: or32648Data.pensionItem.pensionCheckItems[2].checkboxValue.modelValue ? '1' : '0',
      // 老齢関係メモ
      riyoMemo1Knj: or32648Data.pensionItem.systemUseTextValue[0].value,
      // 障害関係メモ
      riyoMemo2Knj: or32648Data.pensionItem.systemUseTextValue[1].value,
      // 遺族・寡婦メモ
      riyoMemo3Knj: or32648Data.pensionItem.systemUseTextValue[2].value,
      // 恩給
      seido4: or32648Data.otherCheckBoxItem.otherCheckItems[0].checkboxValue.modelValue ? '1' : '0',
      // 特別障害者手当
      seido5: or32648Data.otherCheckBoxItem.otherCheckItems[1].checkboxValue.modelValue ? '1' : '0',
      // 生活保護
      seido6: or32648Data.otherCheckBoxItem.otherCheckItems[2].checkboxValue.modelValue ? '1' : '0',
      // 生活福祉資金貸付
      seido7: or32648Data.otherCheckBoxItem.otherCheckItems[3].checkboxValue.modelValue ? '1' : '0',
      // 高齢者住宅整備資金貸付
      seido8: or32648Data.otherCheckBoxItem.otherCheckItems[4].checkboxValue.modelValue ? '1' : '0',
      // 日常生活自立支援事業
      seido9: or32648Data.otherCheckBoxItem.otherCheckItems[5].checkboxValue.modelValue ? '1' : '0',
      // 成年後見人制度
      seido10: or32648Data.adultObserverSystemItem.checkboxValue.modelValue ? '1' : '0',
      // 成年後見人制度区分
      seinenKoukenKbn: or32648Data.adultObserverSystemItem.raidoValue,
      // 成年後見人等
      kokenKnj: or32648Data.adultObserverInputValue.value,

      // 国保
      seido11: or32648Data.healthInsurance.insuranceCheckItems[0].checkboxValue.modelValue
        ? '1'
        : '0',
      // 組合保健
      seido12: or32648Data.healthInsurance.insuranceCheckItems[2].checkboxValue.modelValue
        ? '1'
        : '0',
      // 国公共済
      seido13: or32648Data.healthInsurance.insuranceCheckItems[4].checkboxValue.modelValue
        ? '1'
        : '0',
      // 私立学校共済
      seido14: or32648Data.healthInsurance.insuranceCheckItems[6].checkboxValue.modelValue
        ? '1'
        : '0',
      // 協会けんぼ（旧・政管健保）
      seido15: or32648Data.healthInsurance.insuranceCheckItems[1].checkboxValue.modelValue
        ? '1'
        : '0',
      // 日雇い
      seido16: or32648Data.healthInsurance.insuranceCheckItems[3].checkboxValue.modelValue
        ? '1'
        : '0',
      // 地方共済
      seido17: or32648Data.healthInsurance.insuranceCheckItems[5].checkboxValue.modelValue
        ? '1'
        : '0',
      // 船員
      seido18: or32648Data.healthInsurance.insuranceCheckItems[7].checkboxValue.modelValue
        ? '1'
        : '0',
      // 労災保険
      seido19: or32648Data.compensationInsurance.insuranceCheckItems.checkboxValue.modelValue
        ? '1'
        : '0',
      // 労災保険メモ
      riyoMemo4Knj: or32648Data.compensationInsurance.systemUseTextValue.value,
      // 制度利用30（後期高齢者医療）
      seido30: or32648Data.healthInsurance.insuranceCheckItems[8].checkboxValue.modelValue
        ? '1'
        : '0',
      // 制度利用31（その他1）
      seido31: or32648Data.otherItem.otherCheckItems[0].checkboxValue.modelValue ? '1' : '0',
      // 制度利用32（その他2）
      seido32: or32648Data.otherItem.otherCheckItems[1].checkboxValue.modelValue ? '1' : '0',
      // 制度利用33（その他3）
      seido33: or32648Data.otherItem.otherCheckItems[2].checkboxValue.modelValue ? '1' : '0',
      // 制度利用ﾒﾓ5（その他1（メモ））
      riyoMemo5Knj: or32648Data.otherItem.systemUseTextValue[0].value,
      // 制度利用ﾒﾓ6（その他2（メモ））
      riyoMemo6Knj: or32648Data.otherItem.systemUseTextValue[1].value,
      // 制度利用ﾒﾓ7（その他3（メモ））
      riyoMemo7Knj: or32648Data.otherItem.systemUseTextValue[2].value,

      // 補足
      ser29Cd: initData.data.cpnTucGdl5SerInfo.ser29Cd,
      ser30Cd: initData.data.cpnTucGdl5SerInfo.ser30Cd,
      kaisu28: initData.data.cpnTucGdl5SerInfo.kaisu28,
      kaisu29: initData.data.cpnTucGdl5SerInfo.kaisu29,
      addSer3Knj: initData.data.cpnTucGdl5SerInfo.addSer3Knj,
      addSer4Knj: initData.data.cpnTucGdl5SerInfo.addSer4Knj,
      seido20: initData.data.cpnTucGdl5SerInfo.seido20,
      seido23: initData.data.cpnTucGdl5SerInfo.seido23,
      ser34Cd: initData.data.cpnTucGdl5SerInfo.ser34Cd,
      ser35Cd: initData.data.cpnTucGdl5SerInfo.ser35Cd,
      kaisu34: initData.data.cpnTucGdl5SerInfo.kaisu34,
      kaisu35: initData.data.cpnTucGdl5SerInfo.kaisu35,
    }
  }

  // 課題と目標リスト作成
  changedIssuesAndGoalsList?.items.forEach((item) => {
    inputData.kadaiList.push({
      /** id */
      id: item.dataId,
      /** アセスメント番号 */
      assNo: item.assNo,
      /** 課題 */
      kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
      /** 長期 */
      choukiKnj: item.longtermGoal.value,
      /** 短期 */
      tankiKnj: item.shorttermGoal.value,
      /** 連番 */
      seq: item.seq?.toString(),
      /** 更新区分 */
      updateKbn: item.updateKbn,
    })
    // 削除された課題を取得
    const delItems = local.commonInfo.issuesAndGoalsList?.filter(
      (item) => !changedIssuesAndGoalsList.items?.some((cKdai) => item.id === cKdai.id)
    )
    delItems?.forEach((item) => {
      inputData.kadaiList.push({
        /** id */
        id: item.dataId,
        /** アセスメント番号 */
        assNo: item.assNo,
        /** 課題 */
        kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
        /** 長期 */
        choukiKnj: item.longtermGoal.value,
        /** 短期 */
        tankiKnj: item.shorttermGoal.value,
        /** 連番 */
        seq: item.seq?.toString(),
        /** 更新区分 */
        updateKbn: TeX0002Const.DEFAULT.UPDATE_KBN_D,
      })
    })
  })

  const resData: AssessmentHomeServiceUpdateOutEntity = await ScreenRepository.update(
    'assessmentHomeServiceUpdate',
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData?.data) {
    // 更新後データを取得し設定
    TeX0002Logic.data.set({
      uniqueCpId: props.parentUniqueCpId,
      value: {
        updateData: {
          sc1Id: resData.data.sc1Id,
          gdlId: resData.data.gdlId,
          errKbn: resData.data.errKbn,
        },
      },
    })
    isLoading.value = false
    return true
  } else {
    // ロード完了
    isLoading.value = false
    return false
  }
}

/**
 *  新規処理
 */
function createNew() {
  // 画面データをクリア
  clearData()
}

/**
 *  画面データをクリア
 */
function clearData() {
  // サービス利用状況記載日
  local.timePointYmd.value = ''
  // 在宅利用セクション
  local.or32646TypeTopLeft.beingAtHomeUseItems.forEach((item) => {
    item.checkboxValue.modelValue = false
    item.raidoValue = ''
    item.textNumberValue.mo00045.value = ''
    item.textInputValue.value = ''
    item.textareaInputValue.value = ''
  })
  local.or32646TypeTopRight.beingAtHomeUseItems.forEach((item) => {
    item.checkboxValue.modelValue = false
    item.raidoValue = ''
    item.textNumberValue.mo00045.value = ''
    item.textInputValue.value = ''
    item.textareaInputValue.value = ''
  })
  local.or32646TypeBottomLeft.beingAtHomeUseItems.forEach((item) => {
    item.checkboxValue.modelValue = false
    item.raidoValue = ''
    item.textNumberValue.mo00045.value = ''
    item.textInputValue.value = ''
    item.textareaInputValue.value = ''
  })
  local.or32646TypeBottomRight.beingAtHomeUseItems.forEach((item) => {
    item.checkboxValue.modelValue = false
    item.raidoValue = ''
    item.textNumberValue.mo00045.value = ''
    item.textInputValue.value = ''
    item.textareaInputValue.value = ''
  })
  // 直近の入所・入院サブセクション
  local.or32647Type.useFacilityType.raidoValue = ''
  local.or32647Type.facilityInstitution.twowayValue.modelValue = ''
  local.or32647Type.zipCode.value = ''
  local.or32647Type.remarks.value = ''
  local.or32647Type.telNumber.value = ''
  // 制度利用状況サブセクション
  local.or32648Type.pensionItem.pensionCheckItems.forEach((item) => {
    item.checkboxValue.modelValue = false
  })
  local.or32648Type.pensionItem.systemUseTextValue.forEach((item) => {
    item.value = ''
  })
  local.or32648Type.otherCheckBoxItem.otherCheckItems.forEach((item) => {
    item.checkboxValue.modelValue = false
  })
  local.or32648Type.adultObserverSystemItem.checkboxValue.modelValue = false
  local.or32648Type.adultObserverSystemItem.raidoValue = ''
  local.or32648Type.adultObserverInputValue.value = ''
  local.or32648Type.healthInsurance.insuranceCheckItems.forEach((item) => {
    item.checkboxValue.modelValue = false
  })
  local.or32648Type.compensationInsurance.insuranceCheckItems.checkboxValue.modelValue = false
  local.or32648Type.compensationInsurance.systemUseTextValue.value = ''
  local.or32648Type.otherItem.otherCheckItems.forEach((item) => {
    item.checkboxValue.modelValue = false
  })
  local.or32648Type.otherItem.systemUseTextValue.forEach((item) => {
    item.value = ''
  })
  local.or32648Type.elderlyHealthProject.checkItems.forEach((item) => {
    item.checkboxValue.modelValue = false
  })
}

/**
 * 解決すべき課題と目標にスクロール関数
 */
const scrollToIssues = () => {
  if (issuesAndGoalListRef.value) {
    const scrollTop = issuesAndGoalListRef.value.offsetTop
    window.scroll({ top: scrollTop, behavior: 'smooth' })
  }
}

/**
 * GUI00632［期間内履歴選択］ ダイアログをポップアップで起動する
 */
const openDialogOr28500 = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentHomeServiceChosaCntSelectInEntity = {
    /** 計画期間ID */
    sc1Id: local.commonInfo.kikanKanriFlg === '1' ? (local.commonInfo.sc1Id ?? '') : '0',
    /** 事業所ID */
    svJigyoId: local.commonInfo.jigyoId ?? '',
    /** 利用者ID */
    userid: local.commonInfo.userId ?? '',
  }
  const resData: AssessmentHomeServiceChosaCntSelectOutEntity = await ScreenRepository.select(
    'assessmentHomeServiceChosaCntSelect',
    inputData
  )

  if (resData.data) {
    if (Number(resData.data.chosaCnt) === 0) {
      return
    } else {
      localOneway.or28500Oneway.sc1Id =
        local.commonInfo.kikanKanriFlg === '1' ? (local.commonInfo.sc1Id ?? '') : '0'
      localOneway.or28500Oneway.svJigyoId = local.commonInfo.jigyoId ?? ''
      localOneway.or28500Oneway.userId = local.commonInfo.userId ?? ''
      localOneway.or28500Oneway.kikanFlg = local.commonInfo.kikanKanriFlg
      localOneway.or28500Oneway.ninteiFlg = local.commonInfo.ninteiFormF

      Or28500Logic.state.set({
        uniqueCpId: or28500.value.uniqueCpId,
        state: { isOpen: true },
      })
    }
  }
}

/**
 * GUI00632［期間内履歴選択］ダイアログ返却情報を取得する
 *
 * @param data -［期間内履歴選択］ダイアログModelValue
 */
const getChosaData = async (data: Or28500Type) => {
  if (data) {
    let cschId = ''
    if (data.historySelectedList[0]) {
      cschId = String(data.historySelectedList[0].cschId)
    }
    // バックエンドAPIから初期情報取得
    const inputData: AssessmentHomeServiceChosaInfoSelectInEntity = {
      /** 計画期間ID */
      sc1Id: local.commonInfo.kikanKanriFlg === '1' ? (local.commonInfo.sc1Id ?? '') : '0',
      /** 調査票ID */
      cschId: cschId,
    }
    const resData: AssessmentHomeServiceChosaInfoSelectOutEntity = await ScreenRepository.select(
      'assessmentHomeServiceChosaInfoSelect',
      inputData
    )

    const createDateNumber = new Date(local.commonInfo.createYmd ?? '').getTime()
    const basicDate01Number = new Date(Or32643Const.DEFAULT.CREATE_YMD_2018_4_1).getTime()

    if (resData.data) {
      let kaishuUmu = ''
      if (!resData.data.cpnTucCsc22H21Info.kaishuUmu) {
        kaishuUmu = '0'
      }

      // H21/４改訂版の場合
      if (local.commonInfo.ninteiFormF === Or32643Const.DEFAULT.NINTEI_FORM_FLG_H21_4) {
        // 作成日が2018年4月1日以降の場合
        if (createDateNumber >= basicDate01Number) {
          local.or32646TypeTopLeft.beingAtHomeUseItems[0].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service1Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[1].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service2Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[2].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service3Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[3].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service4Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[4].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service5Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[5].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service6Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[6].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service7Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[7].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service9Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[8].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service10Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[9].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service12Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu1
          local.or32646TypeTopLeft.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu2
          local.or32646TypeTopLeft.beingAtHomeUseItems[2].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu3
          local.or32646TypeTopLeft.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu4
          local.or32646TypeTopLeft.beingAtHomeUseItems[4].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu5
          local.or32646TypeTopLeft.beingAtHomeUseItems[5].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu6
          local.or32646TypeTopLeft.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu7
          local.or32646TypeTopLeft.beingAtHomeUseItems[7].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu9
          local.or32646TypeTopLeft.beingAtHomeUseItems[8].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu10
          local.or32646TypeTopLeft.beingAtHomeUseItems[9].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu12

          local.or32646TypeTopRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service8Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[6].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service11Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[1].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service13Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service14Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service17Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[4].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service18Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[5].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service19Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[7].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service22Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu8
          local.or32646TypeTopRight.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu11
          local.or32646TypeTopRight.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu13
          local.or32646TypeTopRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu17
          local.or32646TypeTopRight.beingAtHomeUseItems[4].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu18
          local.or32646TypeTopRight.beingAtHomeUseItems[5].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu19
          local.or32646TypeTopRight.beingAtHomeUseItems[7].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu22
          local.or32646TypeTopRight.beingAtHomeUseItems[2].raidoValue = kaishuUmu

          local.or32646TypeBottomRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service15Cd === '1'
          local.or32646TypeBottomRight.beingAtHomeUseItems[2].textInputValue.value =
            resData.data.cpnTucCsc22H21Info.memo1Knj
        }
        // 作成日が2018年4月1日以前 且つ 作成日が2012年4月1日以降
        // または、作成日が2012年4月1日以前の場合
        else {
          local.or32646TypeTopLeft.beingAtHomeUseItems[0].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service1Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[1].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service2Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[2].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service3Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[3].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service4Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[4].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service5Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[5].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service6Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[6].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service7Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[7].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service9Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[8].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service10Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[9].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service12Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[11].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service15Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[10].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service23Cd === '1'
          local.or32646TypeTopLeft.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu1
          local.or32646TypeTopLeft.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu2
          local.or32646TypeTopLeft.beingAtHomeUseItems[2].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu3
          local.or32646TypeTopLeft.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu4
          local.or32646TypeTopLeft.beingAtHomeUseItems[4].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu5
          local.or32646TypeTopLeft.beingAtHomeUseItems[5].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu6
          local.or32646TypeTopLeft.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu7
          local.or32646TypeTopLeft.beingAtHomeUseItems[7].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu9
          local.or32646TypeTopLeft.beingAtHomeUseItems[8].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu10
          local.or32646TypeTopLeft.beingAtHomeUseItems[9].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu12
          local.or32646TypeTopLeft.beingAtHomeUseItems[11].textInputValue.value =
            resData.data.cpnTucCsc22H21Info.memo1Knj
          local.or32646TypeTopLeft.beingAtHomeUseItems[10].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu23

          local.or32646TypeTopRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service8Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[6].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service11Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[1].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service13Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service14Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service17Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[4].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service18Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[5].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service19Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[7].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service20Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[8].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service21Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[9].checkboxValue.modelValue =
            resData.data.cpnTucCsc22H21Info.service22Cd === '1'
          local.or32646TypeTopRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu8
          local.or32646TypeTopRight.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu11
          local.or32646TypeTopRight.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu13
          local.or32646TypeTopRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu17
          local.or32646TypeTopRight.beingAtHomeUseItems[4].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu18
          local.or32646TypeTopRight.beingAtHomeUseItems[5].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu19
          local.or32646TypeTopRight.beingAtHomeUseItems[7].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu20
          local.or32646TypeTopRight.beingAtHomeUseItems[8].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu21
          local.or32646TypeTopRight.beingAtHomeUseItems[9].textNumberValue.mo00045.value =
            resData.data.cpnTucCsc22H21Info.kaisuu22
          local.or32646TypeTopRight.beingAtHomeUseItems[2].raidoValue = kaishuUmu
        }
      }
      // R３/４改訂版の場合
      else {
        // 要介護度確定
        let yokaiKbn = ''
        // 要介護度確定を設定する
        //・要介護度確定＝返却情報.改訂版認定調査票情報.要介護状態確定
        //・返却情報.改訂版認定調査票情報.要介護状態確定が0またはnullの場合、要介護度確定＝返却情報.改訂版認定調査
        if (!resData.data.cpnTucCsc22H21Info.yokaiKbn2) {
          yokaiKbn = resData.data.cpnTucCsc22H21Info.yokaiKbn2
        } else {
          yokaiKbn = resData.data.cpnTucCsc22H21Info.yokaiKbn1
        }

        local.or32646TypeTopLeft.beingAtHomeUseItems[0].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service1Cd === '1'
        local.or32646TypeTopLeft.beingAtHomeUseItems[2].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service2Cd === '1'
        local.or32646TypeTopLeft.beingAtHomeUseItems[3].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service3Cd === '1'
        local.or32646TypeTopLeft.beingAtHomeUseItems[4].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service4Cd === '1'
        local.or32646TypeTopLeft.beingAtHomeUseItems[5].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service5Cd === '1'
        local.or32646TypeTopLeft.beingAtHomeUseItems[6].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service6Cd === '1'
        local.or32646TypeTopLeft.beingAtHomeUseItems[8].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service7Cd === '1'
        local.or32646TypeTopLeft.beingAtHomeUseItems[9].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service9Cd === '1'
        local.or32646TypeTopLeft.beingAtHomeUseItems[10].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service10Cd === '1'
        local.or32646TypeTopLeft.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu1
        local.or32646TypeTopLeft.beingAtHomeUseItems[2].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu2
        local.or32646TypeTopLeft.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu3
        local.or32646TypeTopLeft.beingAtHomeUseItems[4].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu4
        local.or32646TypeTopLeft.beingAtHomeUseItems[5].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu5
        local.or32646TypeTopLeft.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu6
        local.or32646TypeTopLeft.beingAtHomeUseItems[8].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu7
        local.or32646TypeTopLeft.beingAtHomeUseItems[9].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu9
        local.or32646TypeTopLeft.beingAtHomeUseItems[10].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu10

        local.or32646TypeTopRight.beingAtHomeUseItems[0].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service12Cd === '1'
        local.or32646TypeTopRight.beingAtHomeUseItems[8].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service11Cd === '1'
        local.or32646TypeTopRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service8Cd === '1'
        local.or32646TypeTopRight.beingAtHomeUseItems[3].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service13Cd === '1'
        local.or32646TypeTopRight.beingAtHomeUseItems[4].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service14Cd === '1'
        local.or32646TypeTopRight.beingAtHomeUseItems[5].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service17Cd === '1'
        local.or32646TypeTopRight.beingAtHomeUseItems[6].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service18Cd === '1'
        local.or32646TypeTopRight.beingAtHomeUseItems[7].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service19Cd === '1'
        local.or32646TypeTopRight.beingAtHomeUseItems[8].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service22Cd === '1'
        local.or32646TypeTopRight.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu12
        local.or32646TypeTopRight.beingAtHomeUseItems[8].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu11
        local.or32646TypeTopRight.beingAtHomeUseItems[2].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu8
        local.or32646TypeTopRight.beingAtHomeUseItems[3].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu13
        local.or32646TypeTopRight.beingAtHomeUseItems[4].raidoValue = kaishuUmu
        local.or32646TypeTopRight.beingAtHomeUseItems[5].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu17
        local.or32646TypeTopRight.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu18
        local.or32646TypeTopRight.beingAtHomeUseItems[7].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu19
        local.or32646TypeTopRight.beingAtHomeUseItems[9].textNumberValue.mo00045.value =
          resData.data.cpnTucCsc22H21Info.kaisuu22

        local.or32646TypeBottomRight.beingAtHomeUseItems[2].checkboxValue.modelValue =
          resData.data.cpnTucCsc22H21Info.service15Cd === '1'
        local.or32646TypeBottomRight.beingAtHomeUseItems[2].textInputValue.value =
          resData.data.cpnTucCsc22H21Info.memo1Knj

        // 要介護度確定が「11：要支援１」、「12：要支援２」、「21：事業対象者」の場合
        if (
          yokaiKbn === Or32643Const.DEFAULT.CARE_CONFIRM_NEED_ONE ||
          yokaiKbn === Or32643Const.DEFAULT.CARE_CONFIRM_NEED_TWO ||
          yokaiKbn === Or32643Const.DEFAULT.SERVICE_SUBJECT_TANTO
        ) {
          // 返却情報.改訂版認定調査票情報.サービス利用CD1が1の場合
          if (resData.data.cpnTucCsc22H21Info.service1Cd === '1') {
            local.or32646TypeTopLeft.beingAtHomeUseItems[0].checkboxValue.modelValue = false
            local.or32646TypeTopLeft.beingAtHomeUseItems[0].textNumberValue.mo00045.value = ''
            local.or32646TypeTopLeft.beingAtHomeUseItems[1].checkboxValue.modelValue = true
            local.or32646TypeTopLeft.beingAtHomeUseItems[1].textNumberValue.mo00045.value =
              resData.data.cpnTucCsc22H21Info.kaisuu1
          }
          // 返却情報.改訂版認定調査票情報.サービス利用CD6が1の場合
          if (resData.data.cpnTucCsc22H21Info.service6Cd === '1') {
            local.or32646TypeTopLeft.beingAtHomeUseItems[6].checkboxValue.modelValue = false
            local.or32646TypeTopLeft.beingAtHomeUseItems[6].textNumberValue.mo00045.value = ''
            local.or32646TypeTopLeft.beingAtHomeUseItems[7].checkboxValue.modelValue = true
            local.or32646TypeTopLeft.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
              resData.data.cpnTucCsc22H21Info.kaisuu6
          }
        }
        // 要介護度確定が上記以外の場合
        else {
          // 返却情報.改訂版認定調査票情報.サービス利用CD1が1の場合
          if (resData.data.cpnTucCsc22H21Info.service1Cd === '1') {
            local.or32646TypeTopLeft.beingAtHomeUseItems[0].checkboxValue.modelValue = true
            local.or32646TypeTopLeft.beingAtHomeUseItems[0].textNumberValue.mo00045.value =
              resData.data.cpnTucCsc22H21Info.service1Cd
            local.or32646TypeTopLeft.beingAtHomeUseItems[1].checkboxValue.modelValue = false
            local.or32646TypeTopLeft.beingAtHomeUseItems[1].textNumberValue.mo00045.value = ''
          }
          // 返却情報.改訂版認定調査票情報.サービス利用CD6が1の場合
          if (resData.data.cpnTucCsc22H21Info.service6Cd === '1') {
            local.or32646TypeTopLeft.beingAtHomeUseItems[6].checkboxValue.modelValue = true
            local.or32646TypeTopLeft.beingAtHomeUseItems[6].textNumberValue.mo00045.value =
              resData.data.cpnTucCsc22H21Info.service6Cd
            local.or32646TypeTopLeft.beingAtHomeUseItems[7].checkboxValue.modelValue = false
            local.or32646TypeTopLeft.beingAtHomeUseItems[6].textNumberValue.mo00045.value = ''
          }
          local.or32647Type.useFacilityType.raidoValue = '0'
          local.or32647Type.facilityInstitution.twowayValue.modelValue = ''
          local.or32647Type.remarks.value = ''
          local.or32647Type.telNumber.value = ''
          local.or32647Type.zipCode.value = ''
          local.or32648Type.adultObserverSystemItem.checkboxValue.modelValue = false
        }
      }

      // 返却情報.認定調査票概況調査情報.利用施設種別＞０の場合
      if (Number(resData.data.cpnTucCsc12Info.shisetsuShu) > 0) {
        // ・返却情報.認定調査票概況調査情報.利用施設種別＝9、
        // 且つ（画面.認定フラグが「5：R3」または、（画面.認定フラグが「4：H21」、かつ、画面.作成日≧2018/04/01の場合））
        if (
          resData.data.cpnTucCsc12Info.shisetsuShu === '9' &&
          (local.commonInfo.ninteiFormF === Or32643Const.DEFAULT.NINTEI_FORM_FLG_H21_4 ||
            local.commonInfo.ninteiFormF === Or32643Const.DEFAULT.NINTEI_FORM_FLG_R3_4) &&
          new Date(local.commonInfo.createYmd ?? '').getTime() >=
            new Date(Or32643Const.DEFAULT.CREATE_YMD_2018_4_1).getTime()
        ) {
          local.or32647Type.useFacilityType.raidoValue = '3'
        } else {
          local.or32647Type.useFacilityType.raidoValue = resData.data.cpnTucCsc12Info.shisetsuShu
        }
      }

      // 返却情報.認定調査票概況調査情報.施設名がある場合
      if (resData.data.cpnTucCsc12Info.shisetuKnj) {
        local.or32647Type.facilityInstitution.twowayValue.modelValue =
          resData.data.cpnTucCsc12Info.shisetuKnj
        local.or32647Type.telNumber.value = resData.data.cpnTucCsc12Info.shisetsuTel
        local.or32647Type.remarks.value = resData.data.cpnTucCsc12Info.memoKnj
        local.or32647Type.zipCode.value = resData.data.cpnTucCsc12Info.shisetsuZip
      } else {
        local.or32647Type.useFacilityType.raidoValue = resData.data.cpnTucCsc12Info.shisetsuShu
        local.or32647Type.facilityInstitution.twowayValue.modelValue =
          resData.data.mscShisetuInfo.shisetuKnj
        local.or32647Type.telNumber.value = resData.data.mscShisetuInfo.tel
        local.or32647Type.remarks.value = resData.data.mscShisetuInfo.addressKnj
        local.or32647Type.zipCode.value = resData.data.mscShisetuInfo.zip
      }

      local.or32646TypeTopLeft.refValueInitDisabled = true
      local.or32646TypeTopRight.refValueInitDisabled = true
      local.or32646TypeBottomLeft.refValueInitDisabled = true
      local.or32646TypeBottomRight.refValueInitDisabled = true
      local.or32647Type.refValueInitDisabled = true
      local.or32648Type.refValueInitDisabled = true

      // 返却情報.一括取込フラグ＝1の場合
      if (data.isBundleImport.modelValue) {
        await nextTick()
        // 調査票一括取込処理を行う(画面設定を保存する)
        await _save()
      }
    }
  }
}
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  $log.debug(`★[onMounted] [cpId]${Or32646Const.CP_ID(0)} [uId]${props.uniqueCpId}`)
  $log.debug(`★[onMounted] [cpId]${Or32646Const.CP_ID(1)} [uId]${props.uniqueCpId}`)
  $log.debug(`★[onMounted] [cpId]${Or32646Const.CP_ID(2)} [uId]${props.uniqueCpId}`)
  $log.debug(`★[onMounted] [cpId]${Or32646Const.CP_ID(3)} [uId]${props.uniqueCpId}`)

  // コントロール初期化
  await initControls()
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 利用者一覧における利用者の変更を監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue === undefined) {
      return
    }
  },
  { deep: true }
)

/**
 * 時点の変更を監視
 */
watch(
  () => local.timePointYmd,
  (newValue) => {
    if (!newValue) return
    refValue.value = cloneDeep(newValue)
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0002Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 画面共通情報を取得
    getCommonInfo()
    // 作成日により、oneWayとtwoWayの設定
    oneWayTypeChange(local.commonInfo.createYmd ?? '')
    twoWayTypeChange(local.commonInfo.createYmd ?? '')
    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or32643Const.DEFAULT.TAB_ID) {
      return
    }

    // 作成日変更ありまたは再表示の場合
    if (newValue.isCreateDateChanged || newValue.isRefresh) {
      // 画面情報再取得
      await reload()
    }

    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      if (await _save()) {
        setTeX0002State({ isSaveCompleted: true })
      }
    }
    if (newValue.saveOnlyEventFlg) {
      // 保存のみの場合
      void _save()
    }
    if (newValue.tabChangeSaveEventFlg) {
      // タブ変更保存の場合
      if (await _save()) {
        setTeX0002State({ tabChangeSaveCompleted: true })
      }
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      createNew()
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      updateKbn.value = UPDATE_KBN.DELETE

      if (local.commonInfo.deleteKbn === TeX0002Const.DEFAULT.DELETE_PROCESS_KBN_TAB) {
        localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_DELETE_TAB
      } else if (local.commonInfo.deleteKbn === TeX0002Const.DEFAULT.DELETE_PROCESS_KBN_ALL) {
        localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_DELETE_ALL
      }
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      const copyData = local.commonInfo.copyData as AssessmentHomeServiceInitSelectOutEntity
      setFormData(copyData)
    }
  },
  { immediate: true }
)

/**
 * 複写画面メニューのイベントを監視
 */
watch(
  () => Or35672Logic.event.get(Or35672Const.CP_ID(0) + props.parentUniqueCpId),
  async (newValue) => {
    if (newValue?.reload === false) {
      return
    }

    setOr35672Event({ reload: false })

    // 複写画面から共通情報を取得
    getCommonInfoFromCopy()
    // 作成日により、oneWayとtwoWayの設定
    oneWayTypeChange(local.commonInfo.createYmd ?? '')
    twoWayTypeChange(local.commonInfo.createYmd ?? '')

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or32643Const.DEFAULT.TAB_ID) {
      return
    }

    // 画面再表示
    if (newValue?.reload) {
      // 画面情報再取得
      await reload()
    }
  }
)

/**
 * GUI00632［期間内履歴選択］ ダイアログのイベントを監視
 */
watch(
  () => Or28500Logic.state.get(or28500.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
  }
)
</script>

<template>
  <c-v-sheet
    id="Or32643Component"
    class="Or32643ComponentWrapper background-color pa-0"
  >
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <c-v-row
      no-gutters
      class="pb-3"
    >
      <c-v-col cols="12">
        <div class="area-width">
          <!-- タイトル -->
          <g-custom-orX0201
            :oneway-model-value="localOneway.orX0201Oneway"
            @on-click-link-btn="scrollToIssues"
          >
            <template #customContent>
              <c-v-row no-gutters>
                <c-v-col
                  cols="8"
                  class="d-flex align-center pl-12"
                >
                  <!-- 調査票取込ボタン -->
                  <base-mo00611
                    class="import-btn"
                    :oneway-model-value="localOneway.importBtnOneway"
                    @click="openDialogOr28500"
                  />
                </c-v-col>
                <c-v-col cols="4">
                  <!-- サービス利用状況記載日 -->
                  <base-mo00020
                    v-model="local.timePointYmd"
                    class="time-point"
                    :oneway-model-value="localOneway.mo00020Oneway"
                  />
                </c-v-col>
              </c-v-row>
            </template>
          </g-custom-orX0201>
        </div>
      </c-v-col>
    </c-v-row>
    <c-v-row no-gutters>
      <div class="area-width">
        <div class="c-sub-title">
          <!-- '在宅利用'ラベル -->
          <span>{{ t('label.being-at-home-use') }}</span>
          <!-- 説明ラベル -->
          <span class="detail-label">{{ t('label.being-at-home-use-detail') }}</span>
        </div>
      </div>
    </c-v-row>
    <!-- 在宅利用 -->
    <c-v-row no-gutters>
      <div class="d-flex area-width">
        <!-- 左上部 -->
        <c-v-col
          cols="6"
          class="at-home-use-area col-padding-t pl-9 pr-0 pb-0"
        >
          <g-custom-or-32646
            v-bind="or32646"
            :model-value="local.or32646TypeTopLeft"
            :oneway-model-value="localOneway.or32646OnewayTypeTopLeft"
            class="right-border pb-4 pr-9"
          />
        </c-v-col>
        <!-- 右上部 -->
        <c-v-col
          cols="6"
          class="at-home-use-area col-padding-t pl-9 pr-0 pb-0"
        >
          <g-custom-or-32646
            v-bind="or32646_1"
            :model-value="local.or32646TypeTopRight"
            :oneway-model-value="localOneway.or32646OnewayTypeTopRight"
            class="pb-4 pr-9"
          />
        </c-v-col>
      </div>
    </c-v-row>
    <c-v-row no-gutters>
      <div class="d-flex area-width">
        <!-- 左下部 -->
        <c-v-col
          cols="6"
          class="at-home-use-area pb-4 pl-9 pr-0 pt-0"
        >
          <div class="top-border">
            <g-custom-or-32646
              v-bind="or32646_2"
              v-model="local.or32646TypeBottomLeft"
              :oneway-model-value="localOneway.or32646OnewayTypeBottomLeft"
              class="right-border padding-t pr-9"
            />
          </div>
        </c-v-col>
        <!-- 右下部 -->
        <c-v-col
          cols="6"
          class="at-home-use-area pb-4 pr-9 pl-0 pt-0"
        >
          <div>
            <g-custom-or-32646
              v-bind="or32646_3"
              v-model="local.or32646TypeBottomRight"
              :oneway-model-value="localOneway.or32646OnewayTypeBottomRight"
              class="top-border padding-t padding-l"
            />
          </div>
        </c-v-col>
      </div>
    </c-v-row>
    <!-- 直近の入所・入院 -->
    <c-v-row no-gutters>
      <div class="area-width">
        <g-custom-or-32647
          v-bind="or32647"
          :model-value="local.or32647Type"
          :oneway-model-value="localOneway.or32647OnewayType"
        />
      </div>
    </c-v-row>
    <!-- 制度利用状況 -->
    <c-v-row no-gutters>
      <!-- 制度利用状況 -->
      <div class="area-width">
        <g-custom-or-32648
          v-bind="or32648"
          :model-value="local.or32648Type"
          :oneway-model-value="localOneway.or32648OnewayType"
        />
      </div>
    </c-v-row>
  </c-v-sheet>

  <!-- 線 -->
  <c-v-row
    no-gutters
    style="margin: 0 -24px"
    class="py-6"
  >
    <c-v-divider></c-v-divider>
  </c-v-row>

  <!-- フッター -->
  <c-v-row
    no-gutters
    class="pb-6"
  >
    <div
      ref="issuesAndGoalListRef"
      class="w-100"
    >
      <!-- 解決すべき課題と目標 -->
      <g-custom-orX-0209
        v-bind="OrX0209"
        :model-value="local.issuesAndGoalsList"
        :oneway-model-value="localOneway.issuesAndGoalsListOneway"
      />
    </div>
  </c-v-row>

  <!-- GUI00632［期間内履歴選択］ ダイアログ -->
  <g-custom-or-28500
    v-if="showDialogOr28500"
    v-bind="or28500"
    v-model="local.or28500"
    :oneway-model-value="localOneway.or28500Oneway"
    @on-confirm="getChosaData"
  />
</template>
<style scoped lang="scss">
@use '@/styles/base-data-table-list.scss';
.Or32643ComponentWrapper {
  // 時点
  :deep(.time-point .v-input__control) {
    width: 128px !important;
    min-width: 128px !important;
  }
  :deep(.time-point .v-input) {
    width: 166px !important;
    min-width: 166px !important;
  }
  :deep(.time-point .v-input__control) {
    background: white !important;
  }
  :deep(.import-btn) {
    height: 32px;
  }
}

.area-width {
  width: 1084px;
}

// '在宅利用'と説明ラベル
.c-sub-title {
  height: 38px;
}
// 説明ラベル
.detail-label {
  font-size: 14px !important;
}
// 在宅利用区域
.at-home-use-area {
  background-color: rgb(var(--v-theme-surface));
  .right-border {
    border-right: 1px rgb(var(--v-theme-black-200)) solid;
  }
  .top-border {
    border-top: 1px rgb(var(--v-theme-black-200)) solid;
  }
  .padding-t {
    padding-top: 18px;
  }
  .padding-b {
    padding-bottom: 18px;
  }
  .padding-l {
    padding-left: 36px;
  }
}
.col-padding-t {
  padding-top: 18px !important;
}
.col-padding-b {
  padding-bottom: 18px !important;
}
// 解決すべき課題と目標区域
:deep(.v-sheet) {
  background-color: transparent !important;
}
</style>
