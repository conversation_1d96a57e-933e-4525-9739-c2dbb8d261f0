<script lang="ts" setup>
/**
 * Or33423:有機体:［フェースシート（パッケージプラン）］④画面
 * GUI00638_［フェースシート（パッケージプラン）］④画面
 *
 * @description
 * GUI00638_［フェースシート（パッケージプラン）］④画面
 *
 * <AUTHOR> DAO DINH DUONG
 */
import { reactive, ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or28409Logic } from '~/components/custom-components/organisms/Or28409/Or28409.logic'
import { Or28409Const } from '~/components/custom-components/organisms/Or28409/Or28409.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or28287Const } from '~/components/custom-components/organisms/Or28287/Or28287.constants'
import { Or28287Logic } from '~/components/custom-components/organisms/Or28287/Or28287.logic'
import { Or55476Logic } from '~/components/custom-components/organisms/Or55476/Or55476.logic'
import { Or55476Const } from '~/components/custom-components/organisms/Or55476/Or55476.constants'
import type {
  Or55476OneWayType,
  RelatedPersonSelectResInfo,
} from '~/components/custom-components/organisms/Or55476/Or55476.type'
import type { Or28287Type, Or28287OnewayType } from '~/types/cmn/business/components/Or28287Type'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type {
  Or33423StateType,
  Or33423TwoWayType,
  TextAreaType,
  DefaultKeys,
} from '~/components/custom-components/organisms/Or33423/Or33423.type'
import { Or33423Const } from '~/components/custom-components/organisms/Or33423/Or33423.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type {
  FaceSheetNotebookInfoSelectInEntity,
  FaceSheetNotebookInfoSelectOutEntity,
} from '~/repositories/cmn/entities/faceSheetNotebookInfoSelectEntity'
import { CustomClass } from '~/types/CustomClassType'
import type { Or33423OnewayType } from '~/types/cmn/business/components/Or33423Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo01343Type } from '@/types/business/components/Mo01343Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { dateUtils } from '~/utils/dateUtils'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { OrX0157OnewayType } from '~/types/cmn/business/components/OrX0157Type'
import { useSetupChildProps, useScreenOneWayBind, useScreenTwoWayBind } from '#imports'

const { t } = useI18n()
const useDateUtils = dateUtils()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or33423OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()
// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()
const kaigoHokenshaList = ref([{ label: '', value: '' }])
const zokuList = ref([{ label: '', value: '' }])
const or51775 = ref({ uniqueCpId: '' })
const or28409 = ref({ uniqueCpId: '' })
const or55476 = ref({ uniqueCpId: '' })
const mo01343Range = ref<Mo01343Type>({
  value: '',
  endValue: '',
  mo00024: {
    isOpen: false,
  },
})
/**
 * 選択された開始日フィールド。
 */
let selectStartDateFiled: Mo00020Type
/**
 * 選択された終了日フィールド。
 */
let selectEndDateFiled: Mo00020Type
/**
 * 日付範囲選択ダイアログを開く関数。
 *
 * @param startDate - 選択された開始日
 *
 * @param endDate - 選択された終了日
 */
const openDialogSelectDateRange = (startDate: Mo00020Type, endDate: Mo00020Type) => {
  selectStartDateFiled = startDate
  selectEndDateFiled = endDate
  mo01343Range.value.value = startDate.value
  mo01343Range.value.endValue = endDate.value
  if (mo01343Range.value.mo00024!.isOpen) mo01343Range.value.mo00024!.isOpen = false
  mo01343Range.value.mo00024!.isOpen = true
}
watch(
  () => [mo01343Range.value.value, mo01343Range.value.endValue],
  () => {
    selectStartDateFiled.value = mo01343Range.value.value
    selectEndDateFiled.value = mo01343Range.value.endValue
  }
)
/**
 * 保存不可ポップアップ（Or21814）用のユニークCP ID
 */
const or21814 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const or28287 = ref({ uniqueCpId: '' })
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or28409Const.CP_ID(0)]: or28409.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or28287Const.CP_ID(0)]: or28287.value,
  [Or55476Const.CP_ID(0)]: or55476.value,
})
// ダイアログ表示フラグ
const showDialogOr28287 = computed(() => {
  // Or28287のダイアログ開閉状態
  return Or28287Logic.state.get(or28287.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr55476 = computed(() => {
  // Or55476のダイアログ開閉状態
  return Or55476Logic.state.get(or55476.value.uniqueCpId)?.isOpen ?? false
})
const or28287Type = ref<Or28287Type>({
  /** 保険者*/
  hokensya: '',
  /** 被保険者番号*/
  hHokenNo: '',
  /** 認定有効開始日 */
  ninteiStartYmd: '',
  /** 認定終了日 */
  ninteiEndYmd: '',
  /** 要介護度*/
  yokaiKnj: '',
  /** 限度額*/
  limit: '',
  /** 要介護状態区分 */
  yokaiKbn: '',
  /** 保険者ID */
  hokenKHokenCd: '',
})
watch(or28287Type, () => {
  refValue.value![0].leftContent.data.hHokenNo.value.value = or28287Type.value.hHokenNo
  refValue.value![0].leftContent.data.ninStartYmd.value.value = or28287Type.value.ninteiStartYmd
  refValue.value![0].leftContent.data.ninEndYmd.value.value = or28287Type.value.ninteiEndYmd
  if (['11', '12'].includes(or28287Type.value.yokaiKbn)) {
    refValue.value![0].leftContent.data.yokaiKbn.value = or28287Type.value.yokaiKbn
  } else if (['3', '4', '5', '6', '7'].includes(or28287Type.value.yokaiKbn)) {
    refValue.value![0].leftContent.data.yokaiKbn1.value = or28287Type.value.yokaiKbn
  }
  refValue.value![0].leftContent.data.kHokenCd.value.modelValue =
    or28287Type.value?.hokenKHokenCd ?? ''
})
function onClickOr28287() {
  // Or28287のダイアログ開閉状態を更新する
  Or28287Logic.state.set({
    uniqueCpId: or28287.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const openOr28409 = () => {
  Or28409Logic.state.set({
    uniqueCpId: or28409.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * コード区分マスタID: 有無設定
 */
const options454 = ref<CodeType[]>([])
/**
 * コード区分マスタID: 特例手続き
 */
const options453 = ref<CodeType[]>([])
/**
 * コード区分マスタID: 要介護状態1
 */
const options445 = ref<CodeType[]>([])
/**
 * コード区分マスタID: 障害程度区分
 */
const options447 = ref<CodeType[]>([])
/**
 * コード区分マスタID: 要介護状態2
 */
const options446 = ref<CodeType[]>([])
/**
 * コード区分マスタID: 障害程度区分の認定
 */
const options516 = ref<CodeType[]>([])
/**
 * コード区分マスタID: 検討の要否
 */
const options448 = ref<CodeType[]>([])
/**
 * コード区分マスタID: 契約能力
 */
const options449 = ref<CodeType[]>([])
/**
 * コード区分マスタID: 必要性
 */
const options450 = ref<CodeType[]>([])
/**
 * コード区分マスタID: 経済的自立評価
 */
const options451 = ref<CodeType[]>([])

/**
 *  配列の要素順を反転する（2要素用）
 *
 *  @param arr - 2要素を持つ配列
 */
function reverseTwoElementArray(arr: CodeType[]): CodeType[] {
  if (!Array.isArray(arr) || arr.length !== 2) {
    throw new Error('2要素の配列を渡してください。')
  }
  return [arr[1], arr[0]]
}
/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_UMUSETTEI },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TOKUREITETSUDUKI },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_YOUKAIGOJOUTAI1 },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_YOUKAIGOJOUTAI2 },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SHOUGAITEIDOKUBUN },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SHOUGAITEIDOKUBUN_NINTEI },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_KENTOUNOYOUHI },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_KEIYAKUNOURYOKU },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_HITSUYOSEI },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NYUKYOSHA_JIRITSUHYOUKA },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  options454.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_UMUSETTEI)
  options453.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_TOKUREITETSUDUKI)
  options445.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_YOUKAIGOJOUTAI1)
  options446.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_YOUKAIGOJOUTAI2)
  options447.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_SHOUGAITEIDOKUBUN)
  options516.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SHOUGAITEIDOKUBUN_NINTEI
  )
  options448.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_KENTOUNOYOUHI)
  options449.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_KEIYAKUNOURYOKU)
  options450.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_HITSUYOSEI)
  options451.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_NYUKYOSHA_JIRITSUHYOUKA)
}

const localOneway = reactive({
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  contentLabel: {
    customClass: new CustomClass({
      itemClass: 'ml-0',
      labelClass: 'ma-1',
    }),
  },
  memoInputOnewayType: {
    showEditBtnFlg: true,
    showDividerLineFlg: false,
    editBtnClass: 'custom-edit-btn',
    maxlength: '260',
    rows: 4,
    maxRows: '4',
  } as OrX0157OnewayType,
  mo00039Oneway: {
    name: 'mo01343',
    hideDetails: true,
    showItemLabel: false,
  } as Mo00039OnewayType,
  mo00020Oneway: {
    showItemLabel: false,
    width: '133px',
  } as Mo00020OnewayType,
  or51775Oneway: {
    title: '',
    screenId: 'GUI00638',
    bunruiId: '',
    t1Cd: '2030',
    t2Cd: '3',
    t3Cd: '0',
    tableName: 'cpn_tuc_syp_face23',
    columnName: 'kenri_hituyo_knj"',
    assessmentMethod: systemCommonsStore.getAuthzOtherCaremanager?.assessmentMethod ?? '',
    inputContents: '',
    userId: props.onewayModelValue.userId ?? systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  or55476OneWayType: {
    userId: props.onewayModelValue.userId ?? systemCommonsStore.getUserId ?? '',
    telCellFlg: '3',
    createYmd: '',
    kinouId: '',
  } as Or55476OneWayType,
})
// ローカル双方向bind
const { refValue } = useScreenTwoWayBind<Or33423TwoWayType>({
  cpId: Or33423Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or33423StateType>({
  cpId: Or33423Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      // 調査アセスメント種別設定
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          break
        // 新規
        case 'add':
          break
        // 複写
        case 'copy':
          break
        // 削除
        case 'delete':
          break
        // データ再取得
        case 'getData':
          break
        default:
          break
      }
    },
    kaigoHokenshaList: (list) => {
      if (list) {
        kaigoHokenshaList.value = [
          { label: '', value: '' },
          ...list.map((item) => ({ label: item.codeName, value: item.codeId })),
        ]
      }
    },
    zokuList: (list) => {
      if (list) {
        zokuList.value = [
          { label: '', value: '' },
          ...list.map((item) => ({ label: item.codeName, value: item.codeId })),
        ]
      }
    },
  },
})
const handleGetDetailFaceSheet = () => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      isOpen: true,
      dialogText: t('message.i-cmn-11403'),
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
    },
  })
  watch(
    () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
    () => {
      const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
      if (event?.firstBtnClickFlg) {
        void getDetailFaceSheet()
      }
      // 確認ダイアログのフラグをOFF
      Or21814Logic.event.set({
        uniqueCpId: or21814.value.uniqueCpId,
        events: {
          firstBtnClickFlg: false,
          secondBtnClickFlg: false,
        },
      })
    },
    { once: true }
  )
}
const getDetailFaceSheet = async () => {
  const inputData: FaceSheetNotebookInfoSelectInEntity = {
    /** 利用者ID */
    uid: '',

    /** 事業所ID */
    defSvJigyoId: '',

    /** 事業所ID */
    shokuinId: '',

    /** システムコード */
    sysCd: '71101',

    /** 交付年月日 */
    dateYmd: '',

    /** 身体障害者手帳権限フラグ */
    sinShogaiFlg: '',

    /** 療育手帳権限フラグ */
    ryoikuFlg: '',

    /** 精神手帳権限フラグ */
    kasanFlg: '',
  }
  const resData: FaceSheetNotebookInfoSelectOutEntity = await ScreenRepository.select(
    'faceSheetNotebookInfoSelect',
    inputData
  )
  if (resData.statusCode === 'success' || resData.statusCode === '200') {
    console.log('resData.data.techouKnj', resData.data)
    refValue.value![1].leftContent.data.techoShogaiKnj.value.value = resData.data.techouKnj
    refValue.value![1].leftContent.data.techoShogai2Knj.value.value = resData.data.techouKnj2
    refValue.value![1].leftContent.data.techoShogai3Knj.value.value = resData.data.techouKnj3
    if (Number(resData.data.techouCount) > 0) {
      refValue.value![1].leftContent.data.techoUmu1.value = '2'
    } else {
      refValue.value![1].leftContent.data.techoUmu1.value = ''
    }
    if (Number(resData.data.teidoCount) > 0) {
      refValue.value![1].leftContent.data.techoUmu2.value = '2'
    } else {
      refValue.value![1].leftContent.data.techoUmu2.value = ''
    }
    if (Number(resData.data.seishinCount) > 0) {
      refValue.value![1].leftContent.data.techoUmu3.value = '2'
    } else {
      refValue.value![1].leftContent.data.techoUmu3.value = ''
    }
  }
}

const validate = () => {
  if (
    useDateUtils.isMoreThanDate(
      refValue.value![0].leftContent.data.ninStartYmd.value.value,
      refValue.value![0].leftContent.data.ninEndYmd.value.value
    ) ||
    useDateUtils.isMoreThanDate(
      refValue.value![1].leftContent.data.techoSYmd.value.value,
      refValue.value![1].leftContent.data.techoEYmd.value.value
    )
  ) {
    Or21813Logic.state.set({
      uniqueCpId: or21813.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        isOpen: true,
        dialogText: t('message.e-cmn-41733'),
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        secondBtnType: 'blank',
        firstBtnLabel: t('OK'),
      },
    })
    return false
  }
  return true
}
/**
 * ninteiUmuの値を監視し、値が「2」の場合、yokaiKbnとyokaiKbnの値をクリアする。
 *
 * @param newVal - ninteiUmuの新しい値
 */
watch(
  () => refValue.value![0].leftContent.data.ninteiUmu.value,
  (newVal) => {
    if (Number(newVal) === 2) {
      refValue.value![0].leftContent.data.yokaiKbn.value = ''
      refValue.value![0].leftContent.data.yokaiKbn1.value = ''
    }
  }
)
/**
 * yokaiKbnの値を監視し、数値が入力された場合、yokaiKbnをクリアし、ninteiUmuを「1」に設定する。
 *
 * @param newVal - yokaiKbnの新しい値
 */
watch(
  () => refValue.value![0].leftContent.data.yokaiKbn.value,
  (newVal) => {
    if (Number(newVal)) {
      refValue.value![0].leftContent.data.yokaiKbn1.value = ''
      refValue.value![0].leftContent.data.ninteiUmu.value = '1'
    }
  }
)
/**
 * yokaiKbnの値を監視し、数値が入力された場合、yokaiKbnをクリアし、ninteiUmuを「1」に設定する。
 *
 * @param newVal - yokaiKbnの新しい値
 */
watch(
  () => refValue.value![0].leftContent.data.yokaiKbn1.value,
  (newVal) => {
    if (Number(newVal)) {
      refValue.value![0].leftContent.data.yokaiKbn.value = ''
      refValue.value![0].leftContent.data.ninteiUmu.value = '1'
    }
  }
)
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr28409 = computed(() => {
  return Or28409Logic.state.get(or28409.value.uniqueCpId)?.isOpen ?? false
})
let selectTextFiled: TextAreaType
/**
 * 画面を開くための関数。
 *
 * @param title - 表示するタイトル文字列
 *
 * @param textFiled - 選択されたテキストフィールドのオブジェクト
 */
const openGUI937 = (title: string, textFiled: TextAreaType, action: DefaultKeys) => {
  localOneway.or51775Oneway = {
    ...localOneway.or51775Oneway,
    ...Or33423Const.DEFAULT[action],
    title,
  }
  selectTextFiled = textFiled
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * テキストフィールドに値を設定する関数。
 *
 * @param data - 設定するデータ。`type`に応じて値を上書きまたは追加する。
 */
const handleSetText = (data: Or51775ConfirmType) => {
  if (data.type === '1') {
    selectTextFiled.value.value = data.value
  } else {
    selectTextFiled.value.value += data.value
  }
}
const handleSetToriNekin = (value: { tKindKnj: string; kingaku: string }) => {
  refValue.value![2].rightContent.data.nenkinUmu.value.modelValue = true
  refValue.value![2].rightContent.data.nenkinKnj.value.value = value.tKindKnj
  refValue.value![2].rightContent.data.nenkinGaku.value.mo00045.value = value.kingaku
}
const or28287Data = computed<Or28287OnewayType>(() => ({
  /** 利用者ID */
  userId: props.onewayModelValue.userId ?? systemCommonsStore.getUserId ?? '',
  telCellFlg: '3',
}))
let keyOr55476: string
/**
 *  ボタン押下時の処理
 *
 * @param telCellFlg - 電話携帯フラグ
 *
 * @param type - ケアプラン方式は5：新型養護老人ホームパッケージプラン且つ、親画面は会議録画面
 */
function onClickOr55476(key: string) {
  keyOr55476 = key
  Or55476Logic.state.set({
    uniqueCpId: or55476.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const handleOr55476Confirm = (data: RelatedPersonSelectResInfo) => {
  refValue.value![2].leftContent.data.kokenUmu.value = '1'
  if (keyOr55476 === 'kokenKnj') {
    refValue.value![2].leftContent.data.kokenKnj.value.value = data.nameKnj
  } else if (keyOr55476 === 'hosaKnj') {
    refValue.value![2].leftContent.data.hosaKnj.value.value = data.nameKnj
  } else if (keyOr55476 === 'hojoKnj') {
    refValue.value![2].leftContent.data.hojoKnj.value.value = data.nameKnj
  }
}
defineExpose({
  validate,
})
await initCodes()
// function getDifferentFields(obj1: any, obj2: any): string[] {
//   const allKeys = new Set([...keys(obj1), ...keys(obj2)])
//   const differentKeys: string[] = []

//   allKeys.forEach((key) => {
//     if (!isEqual(get(obj1, key), get(obj2, key))) {
//       differentKeys.push(key)
//     }
//   })

//   return differentKeys
// }
// const test = () => {
//   console.log(
//     getDifferentFields(screenStore.getCpTwoWayInitialValue(props.uniqueCpId)[0].leftContent.data.yokaiKbn, refValue.value[0].leftContent.data.yokaiKbn)
//   )
//   console.log(
//     screenStore.getCpTwoWayInitialValue(props.uniqueCpId)[0].leftContent.data.yokaiKbn,
//     refValue.value[0].leftContent.data.yokaiKbn
//   )
// }
</script>
<template>
  <!-- <button @click="test">click</button> -->
  <c-v-sheet class="container">
    <c-v-row class="justify-center">
      <c-v-col cols="12">
        <c-v-row
          :class="['bordered', 'clickable-title']"
          style="font-size: 16px"
          @click="onClickOr28287"
        >
          {{ t('label.long-term-care-insurance-status') }}
        </c-v-row>
        <c-v-row
          style="background-color: #fff"
          justify="center"
          :class="['bordered', 'pa-2', 'border-top-none']"
        >
          <div class="content-con">
            <c-v-row
              no-gutters
              class="mt-1 gap-16"
            >
              <c-v-col cols="2">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.nursing-care-insurance') }"
                />
                <base-mo00039
                  v-model="refValue![0].leftContent.data.kHokenUmu.value"
                  :oneway-model-value="{
                    ...localOneway.mo00039Oneway,
                    items: options454,
                  }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="2">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.insurer') }"
                />
                <base-mo00040
                  v-model="refValue![0].leftContent.data.kHokenCd.value"
                  :oneway-model-value="{
                    ...refValue![0].leftContent.data.kHokenCd.setting,
                    items: kaigoHokenshaList,
                  }"
                />
              </c-v-col>
              <c-v-col cols="2">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.insured-person-number-2') }"
                />
                <base-mo00045
                  v-model="refValue![0].leftContent.data.hHokenNo.value"
                  :oneway-model-value="{
                    ...refValue![0].leftContent.data.hHokenNo.setting,
                  }"
                />
              </c-v-col>
              <c-v-col cols="2">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.address-special-procedure') }"
                />
                <base-mo00039
                  v-model="refValue![0].leftContent.data.kHokenTokuKbn.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{
                    ...localOneway.mo00039Oneway,
                    items: options453,
                  }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.completion-date-1') }"
                />
                <base-mo00020
                  v-model="refValue![0].leftContent.data.kHokenTokuYmd.value"
                  :oneway-model-value="{ ...localOneway.mo00020Oneway }"
                />
              </c-v-col>
            </c-v-row>
            <div class="gray-line mt-4" />
            <c-v-row
              :class="['bordered', 'clickable-title', 'rounded', 'mt-6', 'small-padding']"
              align="center"
              @click="onClickOr28287"
            >
              {{ t('label.certification-info') }}
            </c-v-row>
            <c-v-row
              align="center"
              class="mt-7 gap-16"
              no-gutters
            >
              <c-v-col cols="2">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.certification') }"
                />
                <base-mo00039
                  v-model="refValue![0].leftContent.data.ninteiUmu.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{
                    ...localOneway.mo00039Oneway,
                    items: options454,
                  }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="2">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.support-required') }"
                />
                <base-mo00039
                  v-model="refValue![0].leftContent.data.yokaiKbn.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options445 }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.care-required') }"
                />
                <base-mo00039
                  v-model="refValue![0].leftContent.data.yokaiKbn.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options446 }"
                  class="radio-group"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row
              class="mt-5"
              no-gutters
              align="center"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.valid-period') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                >
                  <c-v-row
                    no-gutters
                    class="edit-button"
                  >
                    <base-mo00009
                      :oneway-model-value="localOneway.memoInputIconBtn"
                      @click="
                        openDialogSelectDateRange(
                          refValue![0].leftContent.data.ninStartYmd.value,
                          refValue![0].leftContent.data.ninEndYmd.value
                        )
                      "
                    />
                    <base-mo00020
                      v-model="refValue![0].leftContent.data.ninStartYmd.value"
                      :oneway-model-value="localOneway.mo00020Oneway"
                    />
                  </c-v-row>
                  <div class="px-1">〜</div>
                  <base-mo00020
                    v-model="refValue![0].leftContent.data.ninEndYmd.value"
                    class="datetime-input"
                    :oneway-model-value="localOneway.mo00020Oneway"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </div>
        </c-v-row>
        <c-v-row
          style="background-color: #fff"
          justify="center"
          :class="['bordered', 'pa-2', 'border-top-none']"
        >
          <div class="content-con">
            <base-mo00615
              class="mb-2 mt-2"
              :oneway-model-value="{ itemLabel: t('label.service-usage-before-admission') }"
            />
            <g-custom-or-x0156
              v-model="refValue![0].rightContent.data.nyushomaeKnj.value"
              :oneway-model-value="localOneway.memoInputOnewayType"
              @on-click-edit-btn="
                openGUI937(
                  t('label.long-term-care-insurance-status'),
                  refValue![0].rightContent.data.nyushomaeKnj,
                  'AC14'
                )
              "
              class="x0156-style"
            /></div
        ></c-v-row>

        <c-v-row
          style="flex-wrap: nowrap"
          class="bordered normal-title border-top-none"
        >
          {{ t('label.physical-mental-disability-measures') }}
        </c-v-row>
        <c-v-row
          style="background-color: #fff"
          no-gutters
          justify="center"
        >
          <c-v-col
            class="mb-6"
            cols="11"
          >
            <c-v-row
              :class="['bordered', 'clickable-title', 'rounded', 'mt-6', 'small-padding']"
              :style="
                refValue![1].leftContent.data.techoUmu0.value === '1' ? '' : 'pointer-events: none'
              "
              align="center"
              @click="handleGetDetailFaceSheet"
            >
              {{ t('label.notebook') }}
            </c-v-row>
            <base-mo00615
              class="mb-2 mt-4"
              :oneway-model-value="{ itemLabel: t('label.physical-disability-certificate') }"
            />
            <c-v-row
              align="center"
              class="gap-16"
              no-gutters
            >
              <c-v-col cols="auto">
                <base-mo00039
                  v-model="refValue![1].leftContent.data.techoUmu1.value"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options516 }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.physical-disability-certificate'),
                        refValue![1].leftContent.data.techoShogaiKnj,
                        'AC17'
                      )
                    "
                  />
                  <base-mo00045
                    v-model="refValue![1].leftContent.data.techoShogaiKnj.value"
                    :oneway-model-value="{
                      ...refValue![1].leftContent.data.techoShogaiKnj.setting,
                      width: '236px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <div class="gray-line mt-3"></div>
            <base-mo00615
              class="mb-2 mt-4"
              :oneway-model-value="{ itemLabel: t('label.rehabilitation-certificate') }"
            />
            <c-v-row
              align="center"
              class="gap-16"
              no-gutters
            >
              <c-v-col cols="auto">
                <base-mo00039
                  v-model="refValue![1].leftContent.data.techoUmu2.value"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options516 }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.rehabilitation-certificate'),
                        refValue![1].leftContent.data.techoShogai2Knj,
                        'AC19'
                      )
                    "
                  />
                  <base-mo00045
                    v-model="refValue![1].leftContent.data.techoShogai2Knj.value"
                    :oneway-model-value="{
                      ...refValue![1].leftContent.data.techoShogai2Knj.setting,
                      width: '236px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <div class="gray-line mt-3" />
            <base-mo00615
              class="mb-2 mt-4"
              :oneway-model-value="{ itemLabel: t('label.mental-health-welfare-certificate') }"
            />
            <c-v-row
              align="center"
              class="gap-16"
              no-gutters
            >
              <c-v-col cols="auto">
                <base-mo00039
                  v-model="refValue![1].leftContent.data.techoUmu3.value"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options516 }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.mental-health-welfare-certificate'),
                        refValue![1].leftContent.data.techoShogai3Knj,
                        'AC21'
                      )
                    "
                  />
                  <base-mo00045
                    v-model="refValue![1].leftContent.data.techoShogai3Knj.value"
                    :oneway-model-value="{
                      ...refValue![1].leftContent.data.techoShogai3Knj.setting,
                      width: '236px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <div class="gray-line mt-3" />
            <c-v-row
              no-gutters
              class="mt-1 gap-16"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2 mt-4"
                  :oneway-model-value="{ itemLabel: t('label.disability-degree-certification') }"
                />
                <base-mo00039
                  v-model="refValue![1].leftContent.data.shougaiNinteiUmu.value"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options516 }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col>
                <base-mo00615
                  class="mb-2 mt-4"
                  :oneway-model-value="{ itemLabel: t('label.classification') }"
                />
                <base-mo00039
                  v-model="refValue![1].leftContent.data.teidoKbn.value"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options447 }"
                  class="radio-group"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row
              no-gutters
              class="mt-1 gap-16"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2 mt-4"
                  :oneway-model-value="{ itemLabel: t('label.note') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(t('label.note'), refValue![1].leftContent.data.shogaiKnj, 'AC23')
                    "
                  />
                  <base-mo00045
                    v-model="refValue![1].leftContent.data.shogaiKnj.value"
                    :oneway-model-value="{
                      ...refValue![1].leftContent.data.shogaiKnj.setting,
                      width: '236px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2 mt-4"
                  :oneway-model-value="{ itemLabel: t('label.valid-period') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                >
                  <c-v-row
                    no-gutters
                    class="edit-button"
                  >
                    <base-mo00009
                      :oneway-model-value="localOneway.memoInputIconBtn"
                      @click="
                        openDialogSelectDateRange(
                          refValue![1].leftContent.data.techoSYmd.value,
                          refValue![1].leftContent.data.techoEYmd.value
                        )
                      "
                    />
                    <base-mo00020
                      v-model="refValue![1].leftContent.data.techoSYmd.value"
                      :oneway-model-value="localOneway.mo00020Oneway"
                    />
                  </c-v-row>
                  <div class="px-1">〜</div>
                  <base-mo00020
                    v-model="refValue![1].leftContent.data.techoEYmd.value"
                    class="datetime-input"
                    :oneway-model-value="localOneway.mo00020Oneway"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <c-v-row
          style="flex-wrap: nowrap"
          class="bordered normal-title"
        >
          {{ t('label.continuous-support-necessity') }}
        </c-v-row>
        <c-v-row
          style="background-color: #fff"
          no-gutters
          justify="center"
        >
          <c-v-col
            cols="11"
            class="mb-3 mt-4"
          >
            <g-custom-or-x0156
              v-model="refValue![1].rightContent.data.keizokuKnj.value"
              :oneway-model-value="localOneway.memoInputOnewayType"
              @on-click-edit-btn="
                openGUI937(
                  t('label.continuous-support-necessity'),
                  refValue![1].rightContent.data.keizokuKnj,
                  'AC28'
                )
              "
              class="x0156-style"
            />
            <base-mo00615
              class="mb-2 mt-6"
              :oneway-model-value="{
                itemLabel: t('label.consideration-of-disability-additional-target'),
              }"
            />
            <base-mo00039
              v-model="refValue![1].rightContent.data.sotiKentoKbn.value"
              style="margin-right: 0 !important; display: flex; align-items: center"
              :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options448 }"
              class="radio-group"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row
          style="flex-wrap: nowrap"
          class="bordered normal-title"
        >
          {{ t('label.rights-protection') }}
        </c-v-row>
        <c-v-row
          style="background-color: #fff"
          no-gutters
          justify="center"
        >
          <c-v-col cols="11">
            <c-v-row
              no-gutters
              class="gap-16 mt-4"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.contract-ability-assessment') }"
                />
                <base-mo00039
                  v-model="refValue![2].leftContent.data.keiyakuKbn.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options449 }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.special-notes') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.special-notes'),
                        refValue![2].leftContent.data.keiyakuTokkiKnj,
                        'AC29'
                      )
                    "
                  />
                  <base-mo00045
                    v-model="refValue![2].leftContent.data.keiyakuTokkiKnj.value"
                    :oneway-model-value="{
                      ...refValue![1].leftContent.data.shogaiKnj.setting,
                      width: '596px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <div
              style="width: 900px"
              class="gray-line mt-3"
            ></div>
            <c-v-row
              no-gutters
              class="gap-16 mt-4"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.consideration-of-rights-protection') }"
                />
                <base-mo00039
                  v-model="refValue![2].leftContent.data.yogoKentoKbn.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options450 }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.response-if-needed') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.consideration-of-adult-guardianship'),
                        refValue![2].leftContent.data.kenriHituyoKnj,
                        'AC30'
                      )
                    "
                  />
                  <base-mo00045
                    v-model="refValue![2].leftContent.data.kenriHituyoKnj.value"
                    :oneway-model-value="{
                      ...refValue![1].leftContent.data.shogaiKnj.setting,
                      width: '424px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <c-v-row
              no-gutters
              class="gap-16 mt-4"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.daily-life-support-project') }"
                />
                <base-mo00039
                  v-model="refValue![2].leftContent.data.yogoUmu.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{
                    ...localOneway.mo00039Oneway,
                    items: reverseTwoElementArray(options454),
                  }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <c-v-row>
                  <base-mo00615
                    class="mb-2"
                    :oneway-model-value="{ itemLabel: t('label.person-in-charge') }"
                  />
                  <div class="required-title">{{ t('label.required') }}</div>
                </c-v-row>
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.person-in-charge'),
                        refValue![2].leftContent.data.yogoTantoKnj,
                        'AC32'
                      )
                    "
                  />
                  <c-v-row align="center">
                    <base-mo00045
                      v-model="refValue![2].leftContent.data.yogoTantoKnj.value"
                      :oneway-model-value="{
                        ...refValue![2].leftContent.data.yogoTantoKnj.setting,
                        width: '220px',
                      }"
                    />
                    <div>{{ t('label.social-welfare-council') }}</div>
                  </c-v-row>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <div
              style="width: 900px"
              class="gray-line mt-3"
            ></div>
            <c-v-row
              no-gutters
              class="gap-16 mt-4"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{
                    itemLabel: t('label.consideration-of-adult-guardianship'),
                  }"
                />
                <base-mo00039
                  v-model="refValue![2].leftContent.data.skoukenKentoKbn.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options450 }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.response-if-needed') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.consideration-of-adult-guardianship'),
                        refValue![2].leftContent.data.seinenHituyoKnj,
                        'AC33'
                      )
                    "
                  />
                  <base-mo00045
                    v-model="refValue![2].leftContent.data.seinenHituyoKnj.value"
                    :oneway-model-value="{
                      ...refValue![1].leftContent.data.shogaiKnj.setting,
                      width: '424px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <c-v-row
              no-gutters
              class="gap-16 mt-4 mb-6"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{
                    itemLabel: t('label.guardian-etc'),
                  }"
                />
                <base-mo00039
                  v-model="refValue![2].leftContent.data.kokenUmu.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{
                    ...localOneway.mo00039Oneway,
                    items: reverseTwoElementArray(options454),
                  }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.guardian') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    @click="onClickOr55476('kokenKnj')"
                    :oneway-model-value="localOneway.memoInputIconBtn"
                  />
                  <base-mo00045
                    v-model="refValue![2].leftContent.data.kokenKnj.value"
                    :oneway-model-value="{
                      ...refValue![2].leftContent.data.kokenKnj.setting,
                      width: '157px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.curator') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    @click="onClickOr55476('hosaKnj')"
                    :oneway-model-value="localOneway.memoInputIconBtn"
                  />
                  <base-mo00045
                    v-model="refValue![2].leftContent.data.hosaKnj.value"
                    :oneway-model-value="{
                      ...refValue![2].leftContent.data.hosaKnj.setting,
                      width: '157px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.assistant') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    @click="onClickOr55476('hojoKnj')"
                    :oneway-model-value="localOneway.memoInputIconBtn"
                  />
                  <base-mo00045
                    v-model="refValue![2].leftContent.data.hojoKnj.value"
                    :oneway-model-value="{
                      ...refValue![2].leftContent.data.hojoKnj.setting,
                      width: '157px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <c-v-row
          style="flex-wrap: nowrap"
          class="bordered normal-title"
        >
          {{ t('label.income-and-financial-status') }}
        </c-v-row>
        <c-v-row
          style="background-color: #fff"
          no-gutters
          justify="center"
        >
          <c-v-col
            class="mb-6 mt-3"
            cols="11"
          >
            <c-v-row
              no-gutters
              align="center"
              class="gap-16 mt-4"
            >
              <c-v-col
                cols="auto"
                style="min-width: 100px"
              >
                <base-mo00018
                  v-model="refValue![2].rightContent.data.nenkinUmu.value"
                  :oneway-model-value="{
                    showItemLabel: false,
                    checkboxLabel: t('label.pension'),
                  }"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <c-v-row no-gutters>
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="openOr28409"
                  />
                  <base-mo00045
                    v-model="refValue![2].rightContent.data.nenkinKnj.value"
                    :oneway-model-value="{
                      ...refValue![2].rightContent.data.nenkinKnj.setting,
                      width: '208px',
                    }"
                  />
                </c-v-row>
                    <base-mo00038
                  v-model="refValue![2].rightContent.data.nenkinGaku.value"
                  :oneway-model-value="{
                    ...refValue![2].rightContent.data.nenkinGaku.setting,
                    mo00045Oneway: { width: '126px', showItemLabel: false },
                  }"
                />
                </c-v-row>
              </c-v-col>
              <c-v-col
                style="position: relative; right: 20px"
                cols="auto"
              >
                {{ t('label.yen-per-year') }}
              </c-v-col>
            </c-v-row>
            <c-v-row
              no-gutters
              align="center"
              class="gap-16 mt-4"
            >
              <c-v-col
                cols="auto"
                style="min-width: 100px"
              >
                <base-mo00018
                  v-model="refValue![2].rightContent.data.syunyuSonotaUmu.value"
                  :oneway-model-value="{
                    showItemLabel: false,
                    checkboxLabel: t('label.others'),
                  }"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <c-v-row no-gutters>
                  <c-v-row
                    align="center"
                    no-gutters
                    justify="center"
                    class="edit-button"
                  >
                    <base-mo00009
                      :oneway-model-value="localOneway.memoInputIconBtn"
                      @click="
                        openGUI937(
                          t('label.others'),
                          refValue![2].rightContent.data.syunyuSonotaKnj,
                          'AC36'
                        )
                      "
                    />
                    <base-mo00045
                      v-model="refValue![2].rightContent.data.syunyuSonotaKnj.value"
                      :oneway-model-value="{
                        ...refValue![2].rightContent.data.syunyuSonotaKnj.setting,
                        width: '208px',
                      }"
                    />
                  </c-v-row>
                  <base-mo00038
                    v-model="refValue![2].rightContent.data.syunyuSonotaGaku.value"
                    :oneway-model-value="{
                      ...refValue![2].rightContent.data.syunyuSonotaGaku.setting,
                      mo00045Oneway: { width: '126px', showItemLabel: false },
                    }"
                  />
                </c-v-row>
              </c-v-col>
              <c-v-col
                style="position: relative; right: 20px"
                cols="auto"
              >
                {{ t('label.yen-per-year') }}
              </c-v-col>
            </c-v-row>
            <c-v-row class="mt-2">
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.tax-status') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.tax-status'),
                        refValue![2].rightContent.data.shotokuDankaiKnj,
                        'AC37'
                      )
                    "
                  />
                  <base-mo00045
                    v-model="refValue![2].rightContent.data.shotokuDankaiKnj.value"
                    :oneway-model-value="{
                      ...refValue![2].leftContent.data.hosaKnj.setting,
                      width: '416px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <div
              style="width: 900px"
              class="gray-line mt-3"
            ></div>
            <c-v-row
              no-gutters
              class="gap-16 mt-4 mb-6"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{
                    itemLabel: t('label.economic-independence-evaluation'),
                  }"
                />
                <base-mo00039
                  v-model="refValue![2].rightContent.data.keizaiKbn.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options451 }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.pre-admission-tax-status') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.pre-admission-tax-status'),
                        refValue![2].rightContent.data.kazeijokyoKnj,
                        'AC38'
                      )
                    "
                  />
                  <base-mo00045
                    v-model="refValue![2].rightContent.data.kazeijokyoKnj.value"
                    :oneway-model-value="{
                      ...refValue![2].rightContent.data.kazeijokyoKnj.setting,
                      width: '342px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <c-v-row>
              <base-mo00615
                class="mb-2"
                :oneway-model-value="{ itemLabel: t('label.post-admission-fee-amount') }"
              />
            </c-v-row>
            <c-v-row
              no-gutters
              class="gap-16 mt-4 mb-6"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.tenant') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                >
                  <base-mo00038
                    v-model="refValue![2].rightContent.data.hiyogakuKaisouNyusho.value"
                    :oneway-model-value="{
                      ...refValue![2].rightContent.data.hiyogakuKaisouNyusho.setting,
                      mo00045Oneway: { width: '79px', showItemLabel: false },
                    }"
                  />
                  <div>{{ t('label.tier') }}</div>
                </c-v-row>
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.monthly-amount') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                >
                  <base-mo00038
                    v-model="refValue![2].rightContent.data.hiyogakuNyusho.value"
                    :oneway-model-value="{
                      ...refValue![2].rightContent.data.hiyogakuNyusho.setting,
                      mo00045Oneway: { width: '116px', showItemLabel: false },
                    }"
                  />
                  <div>{{ t('label.yen') }}</div>
                </c-v-row>
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.supporter') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                >
                  <base-mo00038
                    v-model="refValue![2].rightContent.data.hiyogakuHuyo.value"
                    :oneway-model-value="{
                      ...refValue![2].rightContent.data.hiyogakuHuyo.setting,
                      mo00045Oneway: { width: '79px', showItemLabel: false },
                    }"
                  />
                  <div>{{ t('label.tier') }}</div>
                </c-v-row>
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.monthly-amount') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                >
                  <base-mo00038
                    v-model="refValue![2].rightContent.data.hiyogakuKaisouHuyo.value"
                    :oneway-model-value="{
                      ...refValue![2].rightContent.data.hiyogakuKaisouHuyo.setting,
                      mo00045Oneway: { width: '116px', showItemLabel: false },
                    }"
                  />
                  <div>{{ t('label.yen') }}</div>
                </c-v-row>
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.assets-liabilities-status') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.assets-liabilities-status'),
                        refValue![2].rightContent.data.shisanKnj,
                        'AC39'
                      )
                    "
                  />
                  <base-mo00045
                    v-model="refValue![2].rightContent.data.shisanKnj.value"
                    :oneway-model-value="{
                      ...refValue![2].rightContent.data.shisanKnj.setting,
                      width: '181px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <c-v-row no-gutters>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.high-cost-care-service-cap') }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                >
                  <base-mo00038
                    v-model="refValue![2].rightContent.data.kougakuGendogaku.value"
                    :oneway-model-value="{
                      ...refValue![2].rightContent.data.kougakuGendogaku.setting,
                      mo00045Oneway: { width: '204px', showItemLabel: false },
                    }"
                  />
                  <div>{{ t('label.yen') }}</div>
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <c-v-row
          style="flex-wrap: nowrap"
          class="bordered normal-title"
        >
          {{ t('label.public-deduction') }}
        </c-v-row>
        <c-v-row
          style="background-color: #fff"
          no-gutters
          justify="center"
        >
          <c-v-col
            class="mb-6 mt-3"
            cols="11"
          >
            <c-v-row
              no-gutters
              class="gap-16 mt-4 mb-6"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{
                    itemLabel: t('label.public-assistance-availability'),
                  }"
                />
                <base-mo00039
                  v-model="refValue![3].leftContent.data.seihoIryohojoUmu.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{ ...localOneway.mo00039Oneway, items: options454 }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{ itemLabel: t('label.assistance-agency-name') }"
                />
                <c-v-row>
                  <c-v-row
                    align="center"
                    no-gutters
                    justify="center"
                    class="edit-button"
                  >
                    <base-mo00009
                      :oneway-model-value="localOneway.memoInputIconBtn"
                      @click="
                        openGUI937(
                          t('label.assistance-agency-name'),
                          refValue![3].leftContent.data.SI150,
                          'AC40'
                        )
                      "
                    />
                    <base-mo00045
                      v-model="refValue![3].leftContent.data.SI150.value"
                      :oneway-model-value="{
                        ...refValue![3].leftContent.data.SI150.setting,
                        width: '157px',
                      }"
                    />
                  </c-v-row>
                  <c-v-row
                    align="center"
                    no-gutters
                    justify="center"
                    class="edit-button"
                  >
                    <base-mo00009
                      :oneway-model-value="localOneway.memoInputIconBtn"
                      @click="
                        openGUI937(
                          t('label.assistance'),
                          refValue![3].leftContent.data.seihoJisshiKnj,
                          'AC41'
                        )
                      "
                    />
                    <base-mo00045
                      v-model="refValue![3].leftContent.data.seihoJisshiKnj.value"
                      :oneway-model-value="{
                        ...refValue![3].leftContent.data.seihoJisshiKnj.setting,
                        width: '157px',
                      }"
                    />
                  </c-v-row>
                  <base-mo00615
                    :oneway-model-value="{
                      itemLabel: t('label.assistance'),
                    }"
                  />
                  <base-mo00018
                    v-model="refValue![3].leftContent.data.iryohujoUmu.value"
                    class="ml-3"
                    :oneway-model-value="{
                      showItemLabel: false,
                      checkboxLabel: t('label.medical-only-aid'),
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <c-v-row
          style="flex-wrap: nowrap"
          class="bordered normal-title"
        >
          {{ t('label.funeral-etc-1') }}
        </c-v-row>
        <c-v-row
          style="background-color: #fff; height: 408px"
          no-gutters
          justify="center"
        >
          <c-v-col
            class="mb-6 mt-3"
            cols="11"
          >
            <c-v-row no-gutters>
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{
                    itemLabel: t('label.funeral-necessity'),
                  }"
                />
                <c-v-row
                  align="center"
                  no-gutters
                  justify="center"
                  class="edit-button"
                >
                  <base-mo00009
                    :oneway-model-value="localOneway.memoInputIconBtn"
                    @click="
                      openGUI937(
                        t('label.funeral-necessity'),
                        refValue![3].rightContent.data.sosaiJisshiKnj,
                        'AC42'
                      )
                    "
                  />
                  <base-mo00045
                    v-model="refValue![3].rightContent.data.sosaiJisshiKnj.value"
                    :oneway-model-value="{
                      ...refValue![3].rightContent.data.sosaiJisshiKnj.setting,
                      width: '296px',
                    }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <c-v-row
              no-gutters
              class="gap-16 mt-4 mb-6"
            >
              <c-v-col cols="auto">
                <base-mo00615
                  class="mb-2"
                  :oneway-model-value="{
                    itemLabel: t('label.heir'),
                  }"
                />
                <base-mo00039
                  v-model="refValue![3].rightContent.data.sozokuUmu.value"
                  style="margin-right: 0 !important; display: flex; align-items: center"
                  :oneway-model-value="{
                    ...localOneway.mo00039Oneway,
                    items: reverseTwoElementArray(options454),
                  }"
                  class="radio-group"
                />
              </c-v-col>
              <c-v-row
                class="gap-16 mt-2"
                no-gutters
              >
                <c-v-col
                  style="display: flex; gap: 4px"
                  cols="auto"
                >
                  <c-v-row
                    align="center"
                    no-gutters
                    justify="center"
                    class="edit-button mt-5"
                    style="position: relative; top: 1px"
                  >
                    <base-mo00009 :oneway-model-value="localOneway.memoInputIconBtn" />
                    <base-mo00045
                      v-model="refValue![3].rightContent.data.sozoku1Knj.value"
                      :oneway-model-value="{
                        ...refValue![3].rightContent.data.sozoku1Knj.setting,
                        width: '116px',
                      }"
                    />
                  </c-v-row>
                  <div>
                    <base-mo00615
                      :oneway-model-value="{
                        itemLabel: t('label.relationship-1'),
                      }"
                    />
                    <base-mo00040
                      v-model="refValue![3].rightContent.data.sozoku1ZokuCd.value"
                      :oneway-model-value="{
                        ...refValue![3].rightContent.data.sozoku1ZokuCd.setting,
                        items: zokuList,
                        width: '96px',
                      }"
                    />
                  </div>
                </c-v-col>
                <c-v-col
                  style="display: flex; gap: 4px"
                  cols="auto"
                >
                  <c-v-row
                    align="center"
                    no-gutters
                    justify="center"
                    class="edit-button mt-5"
                    style="position: relative; top: 1px"
                  >
                    <base-mo00009 :oneway-model-value="localOneway.memoInputIconBtn" />
                    <base-mo00045
                      v-model="refValue![3].rightContent.data.sozoku2Knj.value"
                      :oneway-model-value="{
                        ...refValue![3].rightContent.data.sozoku2Knj.setting,
                        width: '116px',
                      }"
                    />
                  </c-v-row>
                  <div>
                    <base-mo00615
                      :oneway-model-value="{
                        itemLabel: t('label.relationship-1'),
                      }"
                    />
                    <base-mo00040
                      v-model="refValue![3].rightContent.data.sozoku2ZokuCd.value"
                      :oneway-model-value="{
                        ...refValue![3].rightContent.data.sozoku2ZokuCd.setting,
                        items: zokuList,
                        width: '96px',
                      }"
                    />
                  </div>
                </c-v-col>
              </c-v-row>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <g-custom-or-51775
    v-if="showDialogOr51775"
    :oneway-model-value="localOneway.or51775Oneway"
    v-bind="or51775"
    @confirm="handleSetText"
  />
  <g-custom-or-28409
    v-if="showDialogOr28409"
    v-bind="or28409"
    @confirm="handleSetToriNekin"
  />
  <g-custom-or-00013
    v-model="mo01343Range"
    :oneway-model-value="{
      selectMode: '1',
    }"
  />
  <g-custom-or-28287
    v-if="showDialogOr28287"
    v-bind="or28287"
    v-model="or28287Type"
    :oneway-model-value="or28287Data"
  />
  <g-custom-or-55476
    v-if="showDialogOr55476"
    v-bind="or55476"
    :oneway-model-value="localOneway.or55476OneWayType"
    @update:model-value="handleOr55476Confirm"
  />
  <g-base-or21814 v-bind="or21814" />
  <g-base-or21813 v-bind="or21813" />
</template>
<style lang="scss" scoped>
.container {
  background-color: transparent;
  width: 1080px;
  overflow: auto;
}
.border-top-none {
  border-top: none !important;
}
.bordered {
  border: 0.5px #0000001a solid;
}
.bg-white {
  background-color: rgb(var(--v-theme-white));
}

.gap-16 {
  gap: 20px;
}
.v-row {
  margin: 0px;
}
.v-col {
  padding: 8px;
}
.gray-line {
  height: 1px;
  width: 100%;
  background-color: #0000001f;
}
.toGray {
  background-color: #c0c0c0 !important;
}
:deep(.edit-button) {
  align-items: start !important;
  button {
    background-color: #ebf2fd;
    border: 1px solid #c1c6cc;
    border-right: none;
    border-radius: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  .v-field {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
:deep(.datetime-input) {
  .v-field {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
}
:deep(.edit-button-textarea) {
  position: absolute;
  bottom: 12px;
  left: 12px;
  background-color: #ebf2fd;
}
.clickable-title {
  height: 48px;
  display: flex;
  align-items: center;
  padding: 8px 24px;
  color: #214d97;
  background-color: #ecf3fd;
  &.small-padding {
    padding: 8px 12px;
  }
  font-weight: 700;
  text-decoration: underline;
  cursor: pointer;
  text-underline-offset: 2px;
}

.normal-title {
  height: 48px;
  display: flex;
  align-items: center;
  background-color: #e6e6e6;
  color: #333333;
  font-weight: 700;
  font-size: 16px;
  padding: 8px 24px;
}
:deep(.custom-edit-btn) {
  background-color: #ebf2fd !important;
}
.required-title {
  color: #ed6809;
  font-size: 12px;
}
:deep(.radio-group) {
  position: relative;
  right: 3.5px;
  top: 2px;
  .v-selection-control-group {
    gap: 10px;
  }
}
:deep(.x0156-style) {
  textarea {
    height: 75px !important;
  }
}
.content-con {
  padding: 0 36px;
  padding-bottom: 16px;
  width: 100% !important;
}
</style>
