<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0203Const } from './OrX0203.constants'
import type { OrX0203StateType } from './OrX0203.type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind } from '#imports'
import type { OrX0203OnewayType } from '~/types/cmn/business/components/OrX0203Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0203OnewayType
  uniqueCpId: string
  parentCpId?: string
}
const props: Props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: OrX0203Const.IS_OPEN,
})

// 転送履歴表示モードが0の場合
const headers1 = [
  // 事業所番号
  {
    title: t('label.plan-business-name'), // ヘッダーに表示される名称
    key: 'jigyoNumber',
    width: '119px',
    sortable: false,
  },
  // 転送元事業所
  {
    title: t('label.from-delivery-svJigyoId'), // ヘッダーに表示される名称
    key: 'fromJigyoRyakuKnj',
    width: '220px',
    sortable: false,
  },
  // 転送日
  {
    title: t('label.tensou-ymd'), // ヘッダーに表示される名称
    key: 'tensouYmd',
    width: '120px',
    sortable: false,
  },
  // 転送時間
  {
    title: t('label.tensou-time'), // ヘッダーに表示される名称
    key: 'tensouTime',
    width: '120px',
    sortable: false,
  },
  // 職員名
  {
    title: t('label.staff-name'), // ヘッダーに表示される名称
    key: 'shokuinKnj',
    width: '120px',
    sortable: false,
  },
]

// 転送履歴表示モードが1,2の場合
const headers2 = [
  // 事業所番号
  {
    title: t('label.plan-business-name'), // ヘッダーに表示される名称
    key: 'jigyoNumber',
    width: '119px',
    sortable: false,
  },
  // 転送先事業所ＩＤ
  {
    title: t('label.to-delivery-svJigyoId'), // ヘッダーに表示される名称
    key: 'toJigyoRyakuKnj',
    width: '220px',
    sortable: false,
  },
  // 転送日
  {
    title: t('label.tensou-ymd'), // ヘッダーに表示される名称
    key: 'tensouYmd',
    width: '120px',
    sortable: false,
  },
  // 転送時間
  {
    title: t('label.tensou-time'), // ヘッダーに表示される名称
    key: 'tensouTime',
    width: '120px',
    sortable: false,
  },
  // 職員名
  {
    title: t('label.staff-name'), // ヘッダーに表示される名称
    key: 'shokuinKnj',
    width: '120px',
    sortable: false,
  },
]

// 転送履歴表示モードが3の場合
const headers3 = [
  // 事業所番号
  {
    title: t('label.plan-business-name'), // ヘッダーに表示される名称
    key: 'jigyoNumber',
    width: '119px',
    sortable: false,
  },
  // 転送先事業所ＩＤ
  {
    title: t('label.to-delivery-svJigyoId'), // ヘッダーに表示される名称
    key: 'toJigyoRyakuKnj',
    width: '220px',
    sortable: false,
  },
  // 転送日
  {
    title: t('label.tensou-ymd'), // ヘッダーに表示される名称
    key: 'tensouYmd',
    width: '120px',
    sortable: false,
  },
  // 転送時間
  {
    title: t('label.tensou-time'), // ヘッダーに表示される名称
    key: 'tensouTime',
    width: '120px',
    sortable: false,
  },
  // 職員名
  {
    title: t('label.staff-name'), // ヘッダーに表示される名称
    key: 'shokuinKnj',
    width: '120px',
    sortable: false,
  },
  // 転送元事業所
  {
    title: t('label.from-delivery-svJigyoId'), // ヘッダーに表示される名称
    key: 'fromJigyoRyakuKnj',
    width: '220px',
    sortable: false,
  },
]

const headers4 = [
  // 利用者番号
  {
    title: t('label.plan-user-id'), // ヘッダーに表示される名称
    key: 'selfId',
    width: '119px',
    sortable: false,
  },
  // 利用者名
  {
    title: t('label.plan-user-name'), // ヘッダーに表示される名称
    key: 'nameKnj',
    width: '220px',
    sortable: false,
  },
  // 性別
  {
    title: t('label.gender'), // ヘッダーに表示される名称
    key: 'sex',
    width: '120px',
    sortable: false,
  },
  // 生年月日
  {
    title: t('label.birthday-ymd'), // ヘッダーに表示される名称
    key: 'birthdayYmd',
    width: '120px',
    sortable: false,
  },
  // 同名識別
  {
    title: t('label.same-name-self'), // ヘッダーに表示される名称
    key: 'douseiKnj',
    width: '120px',
    sortable: false,
  },
]

const localOneway = reactive({
  currentJigyoNumber: '',
  currentSelfId: '',
  tableHeaders: [{}],
  /**
   * 処理年月
   */
  yymmYmd: '',
  /**
   *  事業所ID
   */
  svJigyoId: '',
  /**
   *  転送履歴表示モード
   */
  tensouMode: '',
  /**
   *  事業所ID配列
   */
  svJigyoIdList: [] as string[],
  /**
   *  転送区分
   */
  tensouKbn: '',
  /**
   *  行数
   */
  rowsNum: '',
  /**
   *  事業所ID事業名(略称)リスト
   */
  jigyoRyakuKnjList: [] as string[],
  // dialog
  mo00024Oneway: {
    width: '1000px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'GUI04519',
      toolbarTitle: t('label.delivery-history-title'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 計画転送・実績取込ラジオボタングループ
  mo00039Oneway: {
    showItemLabel: false,
  } as Mo00039OnewayType,
  va1LabelList: [
    { label: t('label.delivery'), value: '1' },
    { label: t('label.performance-evaluation'), value: '2' },
  ],
  // 処理年月
  row2_item1: {
    itemLabel: t('label.yukokikangaisvc-list-header-processtime'),
    value: '',
    customClass: {
      labelClass: 'row_title_ltb width_120',
      itemClass: 'row_value width_120 pdl_2',
    },
  } as Mo01338OnewayType,
  // 転送事業所
  row2_item2: {
    itemLabel: t('label.delivery-svJigyoId-title'),
    value: '',
    customClass: {
      labelClass: 'row_title_tb width_120',
      itemClass: 'row_value width_220 pdl_2',
    },
  } as Mo01338OnewayType,
  // 事業所番号
  row3_item1: {
    itemLabel: t('label.plan-business-name'),
    value: '',
    customClass: {
      labelClass: 'row_title_ltr width_120',
      itemClass: 'row_value_trl width_120 pdl_2',
    },
  } as Mo01338OnewayType,
  // 転送事業所
  row3_item2: {
    itemLabel: t('label.delivery-svJigyoId-title'),
    value: '',
    customClass: {
      labelClass: 'row_title_tr width_220',
      itemClass: 'row_value_tr width_220 pdl_2',
    },
  } as Mo01338OnewayType,
  // 転送事業所ドロップダウン形式表示
  mo00040Oneway: {
    itemLabel: '',
    showItemLabel: true,
    isRequired: false,
    items: [{ title: '', value: '' }],
    width: '200px',
    hideDetails: true,
    customClass: {
      labelClass: 'row_title_tb width_120 height_23',
      labelStyle: 'padding-left: 18px;',
      itemClass: 'row_value width_220 height_23',
    },
  } as Mo00040OnewayType,
})

const local = reactive({
  // 計画転送・実績取込ラジオボタングループ
  val1: '',
  // 事業所ID
  svJigyoId: { modelValue: '' } as Mo00040Type,
  // 転送事業所情報リスト
  list1: [
    {
      jigyoNumber: '1111',
      // 転送先事業所ＩＤ
      toJigyoRyakuKnj: ' 転送先事業所',
      // 転送日
      tensouYmd: '1996/12/13',
      // 転送時間
      tensouTime: '19:12:17',
      // 職員名
      shokuinKnj: '職員名',
      // 転送元事業所
      fromJigyoRyakuKnj: '転送元事業所',
    },
    {
      jigyoNumber: '2222',
      // 転送先事業所ＩＤ
      toJigyoRyakuKnj: ' 転送先事業所',
      // 転送日
      tensouYmd: '1996/12/13',
      // 転送時間
      tensouTime: '19:12:17',
      // 職員名
      shokuinKnj: '職員名',
      // 転送元事業所
      fromJigyoRyakuKnj: '転送元事業所',
    },
    {
      jigyoNumber: '3333',
      // 転送先事業所ＩＤ
      toJigyoRyakuKnj: ' 転送先事業所',
      // 転送日
      tensouYmd: '1996/12/13',
      // 転送時間
      tensouTime: '19:12:17',
      // 職員名
      shokuinKnj: '職員名',
      // 転送元事業所
      fromJigyoRyakuKnj: '転送元事業所',
    },
    {
      jigyoNumber: '4444',
      // 転送先事業所ＩＤ
      toJigyoRyakuKnj: ' 転送先事業所',
      // 転送日
      tensouYmd: '1996/12/13',
      // 転送時間
      tensouTime: '19:12:17',
      // 職員名
      shokuinKnj: '職員名',
      // 転送元事業所
      fromJigyoRyakuKnj: '転送元事業所',
    },
  ],
  // 転送利用者情報リスト
  list2: [
    {
      // 利用者番号
      selfId: '111',
      // 利用者名
      nameKnj: '利用者名',
      // 性別
      sex: '男',
      // 生年月日
      birthdayYmd: '1996/12/16',
      // 同名識別
      douseiKnj: '同名識別',
    },
    {
      // 利用者番号
      selfId: '222',
      // 利用者名
      nameKnj: '利用者名',
      // 性別
      sex: '男',
      // 生年月日
      birthdayYmd: '1996/12/16',
      // 同名識別
      douseiKnj: '同名識別',
    },
  ],
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<OrX0203StateType>({
  cpId: OrX0203Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? OrX0203Const.IS_OPEN
    },
  },
})

onMounted(() => {
  // .画面.処理年月に引継情報.処理年月を設定する
  localOneway.yymmYmd = props.onewayModelValue.yymmYmd
  // .画面.事業所IDに引継情報.事業所IDを設定する
  localOneway.svJigyoId = props.onewayModelValue.svJigyoId
  // .画面.転送履歴表示モードに引継情報.転送履歴表示モードを設定する
  localOneway.tensouMode = props.onewayModelValue.tensouMode
  // .画面.事業所ID配列に引継情報.事業所ID配列を設定する
  localOneway.svJigyoIdList = props.onewayModelValue.svJigyoIdList
  // .画面.転送区分に引継情報.転送区分を設定する
  localOneway.tensouKbn = props.onewayModelValue.tensouKbn
  // .画面.行数に引継情報.行数を設定する
  localOneway.rowsNum = props.onewayModelValue.rowsNum
  // .画面.事業所ID事業名(略称)リストに引継情報.事業所ID事業名(略称)リストを設定する
  localOneway.jigyoRyakuKnjList = props.onewayModelValue.jigyoRyakuKnjList

  // 転送履歴表示モードが0   table headers
  if (localOneway.tensouMode === OrX0203Const.TENSOUMODE_0) {
    localOneway.tableHeaders = headers1
  }
  // 転送履歴表示モードが3
  else if (localOneway.tensouMode === OrX0203Const.TENSOUMODE_3) {
    localOneway.tableHeaders = headers3
  }
  // 転送履歴表示モードが1,2
  else {
    localOneway.tableHeaders = headers2
  }

  // 転送履歴表示モードが0,1,2の場合"転送履歴"表示
  if (
    localOneway.tensouMode === OrX0203Const.TENSOUMODE_0 ||
    localOneway.tensouMode === OrX0203Const.TENSOUMODE_1 ||
    localOneway.tensouMode === OrX0203Const.TENSOUMODE_2
  ) {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.delivery-history-title')
  }
  // 転送履歴表示モードが3の場合"転送状況詳細"表示
  else {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.delivery-detail')
  }

  // 転送履歴表示モードが0の場合”転送先事業所”表示
  if (localOneway.tensouMode === OrX0203Const.TENSOUMODE_0) {
    localOneway.row2_item2.itemLabel = t('label.to-delivery-svJigyoId')
    localOneway.row3_item2.itemLabel = t('label.to-delivery-svJigyoId')
    localOneway.mo00040Oneway.itemLabel = t('label.to-delivery-svJigyoId')
  }
  // 転送履歴表示モードが1または2の場合”転送元事業所”表示
  else if (
    localOneway.tensouMode === OrX0203Const.TENSOUMODE_1 ||
    localOneway.tensouMode === OrX0203Const.TENSOUMODE_2
  ) {
    localOneway.row2_item2.itemLabel = t('label.from-delivery-svJigyoId')
    localOneway.row3_item2.itemLabel = t('label.from-delivery-svJigyoId')
    localOneway.mo00040Oneway.itemLabel = t('label.from-delivery-svJigyoId')
  }

  // 画面.事業所ID事業名(略称)リスト
  if (
    (localOneway.tensouMode === OrX0203Const.TENSOUMODE_1 ||
      localOneway.tensouMode === OrX0203Const.TENSOUMODE_2) &&
    localOneway.jigyoRyakuKnjList !== null &&
    localOneway.jigyoRyakuKnjList.length !== 0
  ) {
    localOneway.mo00040Oneway.items = []
    let index = 0
    for (const item of localOneway.jigyoRyakuKnjList) {
      localOneway.mo00040Oneway.items.push({ title: item, value: localOneway.svJigyoIdList[index] })
      index++
    }
  }
  // 転送事業所画面.事業所IDにより取得した事業名(略称)
  local.svJigyoId.modelValue = localOneway.svJigyoId
  // TODO
  localOneway.row2_item2.value = localOneway.svJigyoId

  // TODO
  // 計画転送
  local.val1 = '1'
  // 処理年月
  localOneway.row2_item1.value = '1996/12'
  // 事業所番号
  localOneway.row3_item1.value = ''
  // 転送事業所
  localOneway.row3_item2.value = ''
})

/**
 * 「閉じる」ボタン押下
 */
const onClickCloseBtn = () => {
  setState({ isOpen: false })
}

/**
 * 転送事業所一覧選択変更
 *
 * @param item - item
 */
function jigyoNumberSelectRow(item: any) {
  localOneway.currentJigyoNumber = item.jigyoNumber
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <!-- 計画転送・実績取込ラジオボタングループ -->
      <c-v-row
        v-if="localOneway.tensouMode === OrX0203Const.TENSOUMODE_2"
        no-gutters
        class="row1"
      >
        <c-v-col>
          <base-mo00039
            v-model="local.val1"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="item in localOneway.va1LabelList"
              :key="item.value"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </c-v-col>
      </c-v-row>
      <!-- 転送履歴ヘッダ -->
      <c-v-row
        v-if="localOneway.tensouMode !== OrX0203Const.TENSOUMODE_3"
        no-gutters
        class="row2"
      >
        <c-v-col>
          <!-- 処理年月 -->
          <base-mo01338
            class="inline_flex"
            :oneway-model-value="localOneway.row2_item1"
          ></base-mo01338>
          <!-- 転送事業所ドロップダウン形式表示 -->
          <base-mo00040
            v-if="
              (localOneway.tensouMode === OrX0203Const.TENSOUMODE_1 ||
                localOneway.tensouMode === OrX0203Const.TENSOUMODE_2) &&
              localOneway.jigyoRyakuKnjList !== null &&
              localOneway.jigyoRyakuKnjList.length !== 0
            "
            v-model="local.svJigyoId"
            class="inline_flex mo00040Oneway"
            :oneway-model-value="localOneway.mo00040Oneway"
          />
          <!-- 転送事業所ラベル形式表示 -->
          <base-mo01338
            v-else
            class="inline_flex"
            :oneway-model-value="localOneway.row2_item2"
          ></base-mo01338>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="row3"
      >
        <c-v-col>
          <!-- 転送履歴フィルター -->
          <c-v-row no-gutters>
            <c-v-col cols="auto">
              <!-- 事業所番号 -->
              <base-mo01338 :oneway-model-value="localOneway.row3_item1"></base-mo01338>
            </c-v-col>
            <c-v-col>
              <!-- 転送事業所 -->
              <base-mo01338 :oneway-model-value="localOneway.row3_item2"></base-mo01338>
            </c-v-col>
          </c-v-row>
          <!-- 転送事業所一覧 -->
          <c-v-row no-gutters>
            <c-v-col cols="auto">
              <c-v-data-table
                :items="local.list1"
                :headers="localOneway.tableHeaders"
                :hide-default-footer="true"
                class="table-wrapper"
                style="width: auto; max-height: 92px"
                :items-per-page="-1"
                hover
                fixed-header
              >
                <!-- 一覧 -->
                <template #item="{ item }">
                  <tr
                    v-if="localOneway.tensouMode === OrX0203Const.TENSOUMODE_0"
                    :class="{
                      'row-selected': localOneway.currentJigyoNumber === item.jigyoNumber,
                    }"
                    @click="jigyoNumberSelectRow(item)"
                  >
                    <!-- 事業所番号 -->
                    <td>
                      {{ item.jigyoNumber }}
                    </td>
                    <!-- 転送元事業所 -->
                    <td>
                      {{ item.fromJigyoRyakuKnj }}
                    </td>
                    <!-- 転送日 -->
                    <td>
                      {{ item.tensouYmd }}
                    </td>
                    <!-- 転送時間 -->
                    <td>
                      {{ item.tensouTime }}
                    </td>
                    <!-- 職員名 -->
                    <td>
                      {{ item.shokuinKnj }}
                    </td>
                  </tr>
                  <tr
                    v-else-if="localOneway.tensouMode === OrX0203Const.TENSOUMODE_3"
                    :class="{
                      'row-selected': localOneway.currentJigyoNumber === item.jigyoNumber,
                    }"
                    @click="jigyoNumberSelectRow(item)"
                  >
                    <!-- 事業所番号 -->
                    <td>
                      {{ item.jigyoNumber }}
                    </td>
                    <!-- 転送先事業所 -->
                    <td>
                      {{ item.toJigyoRyakuKnj }}
                    </td>
                    <!-- 転送日 -->
                    <td>
                      {{ item.tensouYmd }}
                    </td>
                    <!-- 転送時間 -->
                    <td>
                      {{ item.tensouTime }}
                    </td>
                    <!-- 職員名 -->
                    <td>
                      {{ item.shokuinKnj }}
                    </td>
                    <!-- 転送元事業所 -->
                    <td>
                      {{ item.fromJigyoRyakuKnj }}
                    </td>
                  </tr>
                  <tr
                    v-else
                    :class="{
                      'row-selected': localOneway.currentJigyoNumber === item.jigyoNumber,
                    }"
                    @click="jigyoNumberSelectRow(item)"
                  >
                    <!-- 事業所番号 -->
                    <td>
                      {{ item.jigyoNumber }}
                    </td>
                    <!-- 転送先事業所 -->
                    <td>
                      {{ item.toJigyoRyakuKnj }}
                    </td>
                    <!-- 転送日 -->
                    <td>
                      {{ item.tensouYmd }}
                    </td>
                    <!-- 転送時間 -->
                    <td>
                      {{ item.tensouTime }}
                    </td>
                    <!-- 職員名 -->
                    <td>
                      {{ item.shokuinKnj }}
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
          </c-v-row>
          <!-- 転送利用者一覧 -->
          <c-v-row
            no-gutters
            style="margin-top: 16px"
          >
            <c-v-col cols="auto">
              <c-v-data-table
                :items="local.list2"
                :headers="headers4"
                :hide-default-footer="true"
                class="table-wrapper"
                style="width: auto; max-height: 92px"
                :items-per-page="-1"
                hover
                fixed-header
              >
              </c-v-data-table>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="onClickCloseBtn"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
      </c-v-row>
    </template>
  </base-mo00024>
</template>
<style scoped lang="scss">
.row2 {
  border: 1px solid rgb(var(--v-theme-black-200));
  border-radius: 5px;
  margin: 8px 8px 0px 8px;
  padding: 8px;
}

.row3 {
  border: 1px solid rgb(var(--v-theme-black-200));
  border-radius: 5px;
  margin: 16px 8px 0px 8px;
  padding: 8px;
}

:deep(.row_title_ltb) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border-left: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-top: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-bottom: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-right: 1px solid transparent !important;
  text-align: center !important;
  :deep(label) {
    font-weight: normal !important;
  }
}

:deep(.row_title_tb) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border-top: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-bottom: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-left: 1px solid transparent !important;
  border-right: 1px solid transparent !important;
  text-align: center !important;
  :deep(label) {
    font-weight: normal !important;
  }
}

:deep(.row_title_tr) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border-top: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-bottom: 1px solid transparent !important;
  border-left: 1px solid transparent !important;
  text-align: center !important;
  :deep(label) {
    font-weight: normal !important;
  }
}

:deep(.row_title_ltr) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border-left: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-top: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-bottom: 1px solid transparent !important;
  text-align: center !important;
  :deep(label) {
    font-weight: normal !important;
  }
}

:deep(.row_value) {
  border: 1px solid rgb(var(--v-theme-black-200)) !important;
}

:deep(.row_value_trl) {
  border-top: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-left: 1px solid rgb(var(--v-theme-black-200)) !important;
}

:deep(.row_value_tr) {
  border-top: 1px solid rgb(var(--v-theme-black-200)) !important;
  border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
}

:deep(.align_center) {
  text-align: center !important;
}

:deep(.inline_flex) {
  display: inline-flex !important;
}

:deep(.width_120) {
  width: 120px !important;
  max-width: 120px !important;
  min-width: 120px !important;
}

:deep(.width_220) {
  width: 220px !important;
  max-width: 220px !important;
  min-width: 220px !important;
}

:deep(.pdl_2) {
  padding-left: 2px !important;
}

:deep(.height_23) {
  height: 23px !important;
  max-height: 23px !important;
  min-height: 23px !important;
}

:deep(.mo00040Oneway .v-input__control) {
  width: 219px !important;
  max-width: 219px !important;
  min-width: 219px !important;
  height: 22px !important;
}

:deep(.v-field__input) {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
  min-height: 23px !important;
  height: 23px !important;
}

:deep(.item-label) {
  font-weight: normal !important;
}

.table-header :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  font-size: 14px !important;
  font-weight: normal !important;
  box-shadow: none !important;
  border-collapse: collapse !important;
  text-align: center;
}

:deep(.table-header td) {
  border-color: rgb(var(--v-theme-black-200)) !important;
  font-size: 14px !important;
  border-collapse: collapse !important;
}

.table-wrapper {
  overflow-x: hidden; // 横スクロールバーを非表示
  overflow-y: auto; // 縦スクロールを有効化
}

.table-wrapper :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  font-size: 14px;
  font-weight: normal !important;
  border-collapse: collapse !important;
  box-shadow: none !important;
  height: 23px;
}

.table-wrapper .v-table__wrapper td {
  padding: 0px 2px !important;
  font-size: 14px !important;
  border: unset !important;
  border-collapse: collapse !important;
}

.table-wrapper :deep(.v-table__wrapper th:first-child) {
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-wrapper :deep(.v-table__wrapper th:not(:first-child)) {
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-wrapper :deep(.v-table__wrapper th:last-child) {
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-wrapper :deep(.v-table__wrapper td:first-child) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-wrapper :deep(.v-table__wrapper td:not(:first-child)) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
    border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-wrapper :deep(.v-table__wrapper td:last-child) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.table-wrapper :deep(.v-table__wrapper thead tr:not(:last-child) th:not([rowspan])) {
  border-bottom: none !important;
}
.table-wrapper :deep(.v-table__wrapper thead tr:not(:last-child) th[rowspan='1']) {
  border-bottom: none !important;
}

/***************************************************************
 * CMNダイヤログ画面 選択行の基本スタイル
 ***************************************************************/
/* 選択行のスタイル */
:deep(.row-selected) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}

/* 選択行のスタイル */
.table-wrapper :deep(.v-table__wrapper .select-row) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}
/* 選択行のスタイル */
.table-wrapper :deep(.v-table__wrapper .selected-row) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}
/***************************************************************
 * CMNダイヤログ画面 ハイライトの基本スタイル
 ***************************************************************/
.highlight-row {
  background: rgb(var(--v-theme-blue-100));
}

.table-header :deep(.highlight-row) {
  background-color: rgb(var(--v-theme-blue-100));
}

/***************************************************************
 * CMNダイヤログ画面 行選択チェックボックスの基本スタイル
 ***************************************************************/
.v-table :deep(i.fill) {
  color: rgb(var(--v-theme-blue-700)) !important;
}

/***************************************************************
 * CMNダイヤログ画面の行カーソル仕様
 ***************************************************************/
tr {
  cursor: pointer;
}
// paddingの除去
.table-wrapper
  :deep(
    td .v-col,
    .v-row.v-row--no-gutters > .v-col,
    .v-row.v-row--no-gutters > [class*='v-col-']
  ) {
  padding: 0px !important;
}
// 行の高さ: 32px
.table-wrapper table tbody tr td,
.table-wrapper .row-height,
:deep(.table-wrapper table tbody tr td),
:deep(.table-wrapper .row-height) {
  height: 23px !important;
  max-height: 23px !important;
  min-height: 23px !important;
  font-size: 14px !important;
}

:deep(.v-card-text) {
  overflow-y: hidden !important;
}
// 右寄せのCSS
.text-align-right {
  text-align: right;
}
:deep(.copy-body > div:first-of-type) {
  width: 100% !important;
}

:deep(.copy-body) {
  padding: 0px;
}

:deep(.v-overlay__content) {
  max-width: 1600px !important;
}

/***************************************************************
 * CMNダイヤログ画面の行カーソル仕様
 ***************************************************************/
:deep(tr) {
  cursor: pointer;
}

:deep(.v-table--fixed-height > .v-table__wrapper) {
  overflow-y: scroll !important;
}

:deep(.v-data-table-header__content) {
  display: block !important;
  text-align: center !important;
}

:deep(.v-table ){
  color: rgb(var(--v-theme-text)) !important;
}

:deep(.v-table > .v-table__wrapper > table > tbody > tr > td){
  padding: 0px 2px !important;
}
</style>
