import { HttpResponse, type ResponseResolver } from 'msw'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { InEntity } from '~/repositories/AbstructWebRepository'

import GUI00036GUI00036Save from '@/mocks/api/dd/update/pages/dch/common/GUI00036/GUI00036_save/handler'
import GUI00038GUI00038Save from '@/mocks/api/dd/update/pages/dch/common/GUI00038/GUI00038_save/handler'
import GUI00022 from '@/mocks/api/dd/update/pages/dch/common/GUI00022/handler'
import GUI00059Display from '@/mocks/api/dd/update/pages/dch/common/GUI00059/display/handler'
import GUI00079Save from '@/mocks/api/dd/update/pages/dch/common/GUI00079/save/handler'
import GUI00024GUI00024Save from '@/mocks/api/dd/update/pages/dch/common/GUI00024/GUI00024_save/handler'
import GUI00024GUI00024AutomaticClose from '@/mocks/api/dd/update/pages/dch/common/GUI00024/GUI00024_automaticClose/handler'
import GUI00050GUI00050Save from '@/mocks/api/dd/update/pages/dch/common/GUI00050/GUI00050_save/handler'
import GUI00068Save from '@/mocks/api/dd/update/pages/dch/common/GUI00068/save/handler'
import GUI00032Confirm from '@/mocks/api/dd/update/pages/dch/common/GUI00032/confirm/handler'
import GUI00033Confirm from '@/mocks/api/dd/update/pages/dch/common/GUI00033/confirm/handler'
import GUI03296Save from '@/mocks/api/dd/update/pages/dch/common/GUI03296/save/handler'
import GUI03368Save from '@/mocks/api/dd/update/pages/dch/common/GUI03368/save/handler'
import GUI03353Save from '@/mocks/api/dd/update/pages/dch/common/GUI03353/save/handler'
import GUI00023Update from '@/mocks/api/dd/update/pages/dch/common/GUI00023/update/handler'
import GUI00025Save from '@/mocks/api/dd/update/pages/dch/common/GUI00025/save/handler'
import GUI00028Save from '@/mocks/api/dd/update/pages/dch/common/GUI00028/save/handler'
import GUI00029Save from '@/mocks/api/dd/update/pages/dch/common/GUI00029/save/handler'
import GUI00019Save from '@/mocks/api/dd/update/pages/dch/common/GUI00019/save/handler'
import GUI00054Save from '@/mocks/api/dd/update/pages/dch/common/GUI00054/save/handler'
import Or00251OfficeGroupChange from '@/mocks/api/dd/update/pages/header/officeGroupChange/handler'
import GUI00027Confirm from '@/mocks/api/dd/update/pages/dch/common/GUI00027/confirm/handler'
import staffEdit from '@/mocks/api/dd/update/staff_edit/handler'

const post: ResponseResolver = async ({ request }) => {
  const requestBody = (await request.json()) as InEntity

  const business = requestBody.data.business
  const dataName = business.dataName
  let filePath = ''
  let handlerParam

  if (dataName) {
    filePath = ['./pages', dataName].join('/')
    handlerParam = business
  } else {
    const screenDefId = business.definitionJson.definition.screenDefId
    const routing = business.definitionJson.definition.routing
    const screenName = business.definitionJson.definition.screenPhysicalName
    handlerParam = business.definitionJson

    filePath = ['./pages', routing, screenName, screenDefId].join('/')
  }

  const handler = _handler(filePath)
  if (handler) {
    return handler(handlerParam)
  } else {
    const responceJson: BaseResponseBody = {
      statusCode: 'success',
      data: {
        definitionJson: business.definitionJson,
      },
    }
    return HttpResponse.json(responceJson, { status: 200 })
  }
}

function _handler(path: string): Function | undefined {
  try {
    if (path === './pages/dch/common/GUI00036/GUI00036_save') {
      return GUI00036GUI00036Save.handler
    } else if (path === './pages/dch/common/GUI00038/GUI00038_save') {
      return GUI00038GUI00038Save.handler
    } else if (path === './pages/dch/common/GUI00022') {
      return GUI00022.handler
    } else if (path === './pages/dch/common/GUI00059/display') {
      return GUI00059Display.handler
    } else if (path === './pages/dch/common/GUI00079/save') {
      return GUI00079Save.handler
    } else if (path === './pages/dch/common/GUI00024/GUI00024_save') {
      return GUI00024GUI00024Save.handler
    } else if (path === './pages/dch/common/GUI00024/GUI00024_automaticClose') {
      return GUI00024GUI00024AutomaticClose.handler
    } else if (path === './pages/dch/common/GUI00050/GUI00050_save') {
      return GUI00050GUI00050Save.handler
    } else if (path === './pages/dch/common/GUI00068/save') {
      return GUI00068Save.handler
    } else if (path === './pages/dch/common/GUI00032/confirm') {
      return GUI00032Confirm.handler
    } else if (path === './pages/dch/common/GUI03296/save') {
      return GUI03296Save.handler
    } else if (path === './pages/dch/common/GUI03368/save') {
      return GUI03368Save.handler
    } else if (path === './pages/dch/common/GUI03353/save') {
      return GUI03353Save.handler
    } else if (path === './pages/dch/common/GUI00023/update') {
      return GUI00023Update.handler
    } else if (path === './pages/dch/common/GUI00033/confirm') {
      return GUI00032Confirm.handler
    } else if (path === './pages/dch/common/GUI00025/save') {
      return GUI00025Save.handler
    } else if (path === './pages/dch/common/GUI00028/save') {
      return GUI00028Save.handler
    } else if (path === './pages/dch/common/GUI00029/save') {
      return GUI00029Save.handler
    } else if (path === './pages/dch/common/GUI00019/save') {
      return GUI00019Save.handler
    } else if (path === './pages/header/officeGroupChange') {
      return Or00251OfficeGroupChange.handler
    } else if (path === './pages/dch/common/GUI00054/save') {
      return GUI00054Save.handler
    } else if (path === './pages/dch/common/GUI00027/confirm') {
      return GUI00027Confirm.handler
    } else if (path === './pages/staff_edit') {
      return staffEdit.handler
    }
  } catch (error) {
    console.error('Error:', error)
  }

  return undefined
}

export default { post }
