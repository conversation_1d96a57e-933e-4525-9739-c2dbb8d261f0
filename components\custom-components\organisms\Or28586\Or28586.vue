<script setup lang="ts">
/**
 * Or28586:有機体:印刷設定モーダル
 * GUI00962_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import type { VForm } from 'vuetify/components'
import { Or26261Logic } from '../Or26261/Or26261.logic'
import { Or26261Const } from '../Or26261/Or26261.constants'
import type { OrX0130TableType } from '../OrX0130/OrX0130.type'
import type { OrX0143TableData } from '../OrX0143/OrX0143.type'
import { OrX0143Logic } from '../OrX0143/OrX0143.logic'
import { Or28780Const } from '../Or28780/Or28780.constants'
import { Or28780Logic } from '../Or28780/Or28780.logic'
import { Or18615Const } from '../Or18615/Or18615.constants'
import { Or18615Logic } from '../Or18615/Or18615.logic'
import { Or26331Const } from '../Or26331/Or26331.constants'
import { Or26331Logic } from '../Or26331/Or26331.logic'
import { Or26326Const } from '../Or26326/Or26326.constants'
import { Or26326Logic } from '../Or26326/Or26326.logic'
import { Or26328Const } from '../Or26328/Or26328.constants'
import { Or26328Logic } from '../Or26328/Or26328.logic'
import { Or10016Logic } from '../Or10016/Or10016.logic'
import { Or10016Const } from '../Or10016/Or10016.constants'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import type { ImplementationPlan2InitMasterInfo, Or28586StateType, RevisionType } from './Or28586.type'
import { useScreenOneWayBind, useSetupChildProps, useScreenUtils } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or28586OnewayType } from '~/types/cmn/business/components/Or28586Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or28586Const } from '~/components/custom-components/organisms/Or28586/Or28586.constants'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import  { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { OrX0143OnewayType } from '~/types/cmn/business/components/OrX0143Type'
import { OrX0143Const } from '~/components/custom-components/organisms/OrX0143/OrX0143.constants'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import type { InWebEntity } from '@/repositories/AbstructWebRepository'
import { reportOutputType } from '~/utils/useReportUtils'
import type { Or26261OnewayType, Or26261Type } from '~/types/cmn/business/components/Or26261Type'
import type { IPrintInfo, PrintSelectInEntity } from '~/repositories/cmn/entities/PrintSelectEntity'
import type { PrintUserChangeSelectInEntity } from '~/repositories/cmn/entities/PrintUserChangeSelectEntity'
import type { PrintSubjectSelectInEntity } from '~/repositories/cmn/entities/PrintSubjectSelectEntity'
import { usePrint } from '~/utils/usePrint'
import type { Or26328OnewayType } from '~/types/cmn/business/components/Or26328Type'
import type { ImplementationPlan2PrintSettingsInitUpdateOutEntity, KikanRirekiData } from '~/repositories/cmn/entities/ImplementationPlan2PrintSettingsInitUpdateEntity'
import type { ImplementationPlan2PrintSettingsSubjectSelectOutEntity } from '~/repositories/cmn/entities/ImplementationPlan2PrintSettingsSubjectSelectEntity'
import type { ImplementationPlan2PrintSettingsInfoUpdateInEntity } from '~/repositories/cmn/entities/ImplementationPlan2PrintSettingsInfoUpdateEntity'
import type { ImplementationPlan2PrintSettingsUserChangeSelectEntity } from '~/repositories/cmn/entities/ImplementationPlan2PrintSettingsUserChangeSelectEntity'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or21814EventType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
const form = ref<VForm>()
/************************************************
 * Props
 ************************************************/
interface Props<T extends ImplementationPlan2InitMasterInfo> {
  onewayModelValue: Or28586OnewayType<T>
  uniqueCpId: string
}

const props = defineProps<Props<ImplementationPlan2InitMasterInfo>>()

// 引継情報を取得する
const or21813 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0143 = ref({ uniqueCpId: '' })
const or10016 = ref({ uniqueCpId: '' })
const or26261 = ref({ uniqueCpId: '' })
const or28780 = ref({ uniqueCpId: '' })
const or18615 = ref({ uniqueCpId: '' })
const or26331 = ref({ uniqueCpId: '' })
const or26326 = ref({ uniqueCpId: '' })
const or26328 = ref({ uniqueCpId: '' })
const orX0145 = ref({ uniqueCpId: OrX0145Const.CP_ID(0) })
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// 印刷共通処理
const printCom = usePrint()
// 印刷権限
const prtFlg: boolean = await hasPrintAuth()

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0143Const.CP_ID(0)]: orX0143.value,
  [Or10016Const.CP_ID(1)]: or10016.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [Or26261Const.CP_ID(0)]: or26261.value,
  [Or28780Const.CP_ID(0)]: or28780.value,
  [Or18615Const.CP_ID(0)]: or18615.value,
  [Or26331Const.CP_ID(0)]: or26331.value,
  [Or26326Const.CP_ID(0)]: or26326.value,
  [Or26328Const.CP_ID(0)]: or26328.value,
  [OrX0145Const.CP_ID(0)]: orX0145.value,
})
// ダイアログ表示フラグ
const showDialogOr10016 = computed(() => {
  // Or10016のダイアログ開閉状態
  return Or10016Logic.state.get(or10016.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr26261 = computed(() => {
  // Or26261のダイアログ開閉状態
  return Or26261Logic.state.get(or26261.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
// 利用者列幅
const userCols = ref<number>(4)
// 履歴一覧セクションフラゲ
const mo01334TypeHistoryFlag = ref<boolean>(false)
// 選択した利用者データ
const selectedUserList = ref<OrX0130TableType[]>([])
// 選択した履歴データ
const selectedRirekiData = ref<KikanRirekiData[]>([])

// ローカル双方向bind
const local = reactive({
  or28586: {
    // 印刷設定情報リスト
    prtList: [] as IPrintInfo[],
    // 期間履歴情報リスト
    kikanRirekiList: [] as KikanRirekiData[],
    // 期間管理フラグ
    kikanFlg: '',
  },
  mo00040: { modelValue: '' } as Mo00040Type,
  mo01334: {
    value: '',
    values: [],
  } as Mo01334Type,
  textInput: {
    value: '',
  } as Mo00045Type,
  mo00039Type: '',
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  mo00020TypeKijunbi: {
    value: systemCommonsStore.getSystemDate!,
  } as Mo00020Type,
  titleInput: {
    value: '',
  } as Mo00045Type,
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  // 履歴情報を印刷する
  mo00018HistoryPrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  // 行の先頭で改ページする
  mo00018PageBreakLineStart: {
    modelValue: false,
  } as Mo00018Type,
  // 援助内容タイトルを課題毎に印刷する
  mo00018SupportContentPrintTheTitleByTopic: {
    modelValue: false,
  } as Mo00018Type,
  // 課題番号を印刷する
  mo00018PrintTheProjectNumber: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintMode: {
    modelValue: false,
  } as Mo00018Type,
  or26261: {
    shokuin: {
      shokuin1Knj: '',
      shokuin2Knj: '',
    },
  } as Or26261Type,
  or26261Id: '',
  orX0145: {
    value: {} as TantoCmnShokuin,
  } as OrX0145Type,
})

const localOneway = reactive({
  orX0130Oneway: {
    selectMode: Or28586Const.DEFAULT.TANI,
    tableStyle: 'width:270px',
  } as OrX0130OnewayType,
  orX0143Oneway: {
    // 期間管理フラグ
    kikanFlg: '1',
    // 単一複数フラグ(0:単一,1:複数)
    singleFlg: Or28586Const.DEFAULT.TANI,
    tableStyle: 'width:335px',
    kikanRirekiTitleList: [
      // 作成者
      { title: 'author', width: '120', key: 'shokuName' },
      // 作成日
      { title: 'create-date', width: '120', key: 'createYmd' },
    ],
    // 履歴情報
    rirekiList: [] as OrX0143TableData[],
  } as OrX0143OnewayType,
  // 主治医意見書の改定フラグ
  revisionOneway: {} as RevisionType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  // 印刷ボタン
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  // 日付印刷
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  // 利用者選択
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  // 履歴選択
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  or28586Oneway: {
    ...props.onewayModelValue,
  },
  // 敬称を変更するチェックボックス
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 敬称テキストボックス
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '65',
    maxLength: '4',
  } as Mo00045OnewayType,
  // 欄外に利用者氏名を印刷する
  mo00018PrintTheProjectNumber: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-users-name-on-the-margin'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 記入日を印刷するチェックボックス
  mo00018OneWayPrintDate: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-entry-date'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 記入用シートを印刷するチェックボックス
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 履歴情報を印刷する
  mo00018HistoryPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.history-print'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 印刷補足ラベル
  mo01338PrintSupplement: {
    value: t('label.print-supplement'),
    showItemLabel: true,
    customClass: new CustomClass({ outerClass: 'ml-10 mt-0', itemStyle: 'color: grey;fontSize: 12px' }),
  } as Mo01338OnewayType,
  // 行の先頭で改ページする
  mo00018PageBreakLineStart: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.page-break-line-start'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 援助内容タイトルを課題毎に印刷する
  mo00018SupportContentPrintTheTitleByTopic: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.support-content-print-the-title-by-topic'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 印刷補足ラベル2
  mo01338PrintSupplementTwo: {
    value: t('label.print-supplement-two'),
    showItemLabel: true,
    customClass: new CustomClass({ outerClass: 'ml-10 mt-0', itemStyle: 'color: grey;fontSize: 12px' }),
  } as Mo01338OnewayType,
  // 担当ケアマネラベル
  mo01338OneWayCareManagerInCharge: {
    value: t('label.care-manager-in-charge'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 担当ケアマネ選択アイコン
  mo00009OnewayType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  // 担当ケアマネ表示ラベル
  mo01338OneWayCareManagerInChargeLabel: {
    value: '',
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 担当ケアマネ
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
  // 印刷設定帳票出力状態リスト
  orX0117Oneway: {
    reportId: '',
    type: '1',
    reportData: {} as InWebEntity,
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    width: 'auto',
    maxWidth: '1450px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  // チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: '',
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
  or26261OneWay: {} as Or26261OnewayType,
  mo01334Oneway: {
    headers: [
      {
        title: t('label.report'),
        key: 'prtTitle',
        sortable: false,
        minWidth: '100',
      },
    ],
    items: [],
    height: 655,
  } as Mo01334OnewayType,
  or26328Oneway: {
    maxLength: '50',
  } as Or26328OnewayType,
  // 印鑑欄ボタン
  mo00610OneWay: {
    btnLabel: t('btn.seal-column'),
    disabled: false,
  } as Mo00610OnewayType,
  // GUI01110［印鑑欄設定］画面
  or10016Oneway:{ } as Or10016OnewayType,
})
// 担当ケアマネ
local.orX0145.value = {
  counter: Or28586Const.EMPTY,
  chkShokuId: Or28586Const.EMPTY,
  houjinId: Or28586Const.EMPTY,
  shisetuId: Or28586Const.EMPTY,
  svJigyoId: Or28586Const.EMPTY,
  shokuin1Kana: Or28586Const.EMPTY,
  shokuin2Kana: Or28586Const.EMPTY,
  shokuin1Knj: Or28586Const.EMPTY,
  shokuin2Knj: Or28586Const.EMPTY,
  sex: Or28586Const.EMPTY,
  birthdayYmd: Or28586Const.EMPTY,
  zip: Or28586Const.EMPTY,
  kencode: Or28586Const.EMPTY,
  citycode: Or28586Const.EMPTY,
  areacode: Or28586Const.EMPTY,
  addressKnj: Or28586Const.EMPTY,
  tel: Or28586Const.EMPTY,
  kaikeiId: Or28586Const.EMPTY,
  kyuyoKbn: Or28586Const.EMPTY,
  partKbn: Or28586Const.EMPTY,
  inYmd: Or28586Const.EMPTY,
  outYmd: Or28586Const.EMPTY,
  shozokuId: Or28586Const.EMPTY,
  shokushuId: Or28586Const.EMPTY,
  shokuId: Or28586Const.EMPTY,
  timeStmp: Or28586Const.EMPTY,
  delFlg: Or28586Const.EMPTY,
  shokuNumber: Or28586Const.EMPTY,
  caremanagerKbn: Or28586Const.EMPTY,
  shokuType1: Or28586Const.EMPTY,
  shokuType2: Or28586Const.EMPTY,
  kGroupid: Or28586Const.EMPTY,
  bmpPath: Or28586Const.EMPTY,
  bmpYmd: Or28586Const.EMPTY,
  hankoPath: Or28586Const.EMPTY,
  kojinPath: Or28586Const.EMPTY,
  keitaitel: Or28586Const.EMPTY,
  eMail: Or28586Const.EMPTY,
  senmonNo: Or28586Const.EMPTY,
  sgfFlg: Or28586Const.EMPTY,
  srvSekiKbn: Or28586Const.EMPTY,
  shokushuId2: Or28586Const.EMPTY,
  shokushuId3: Or28586Const.EMPTY,
  shokushuId4: Or28586Const.EMPTY,
  shokushuId5: Or28586Const.EMPTY,
  shikakuId1: Or28586Const.EMPTY,
  shikakuId2: Or28586Const.EMPTY,
  shikakuId3: Or28586Const.EMPTY,
  shikakuId4: Or28586Const.EMPTY,
  shikakuId5: Or28586Const.EMPTY,
  kyuseiFlg: Or28586Const.EMPTY,
  kyuseiKana: Or28586Const.EMPTY,
  kyuseiKnj: Or28586Const.EMPTY,
  sort: Or28586Const.EMPTY,
  selfNumber: Or28586Const.EMPTY,
  ichiranShokushuIdNm: Or28586Const.EMPTY,
  shokushuId2Nm: Or28586Const.EMPTY,
  shokushuId3Nm: Or28586Const.EMPTY,
  shokushuId4Nm: Or28586Const.EMPTY,
  shokushuId5Nm: Or28586Const.EMPTY,
  stopFlg: Or28586Const.EMPTY,
  shokuinKnj: '',
  shokuinKana: Or28586Const.EMPTY,
  title: Or28586Const.EMPTY,
  value: Or28586Const.EMPTY,
} as TantoCmnShokuin

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28586Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<Or28586StateType>({
  cpId: Or28586Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28586Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  if (local.titleInput.value) return
  const dialogResult = await showOr21815MsgOneBtn()
  switch (dialogResult) {
    case Or28586Const.DEFAULT.YES: {
      // 変更前の帳票タイトルに戻す。
      let label = ''
      for (const item of localOneway.mo01334Oneway.items) {
        if (item) {
          if (local.mo01334.value === item.id && item.mo01337OnewayReport) {
            const data = item.mo01337OnewayReport as Mo01337OnewayType
            label = data.value
          }
        }
      }
      local.titleInput.value = label
      break
    }
  }
  return
}
/**
 * エラーダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 *
 * @param isError - エラーかどうか
 *
 * @returns ダイアログの選択結果（yes）
 */
const showMessageBox = async (text: string, btn: string, isError = true) => {
  if (isError) {
    await openErrorDialog(text, btn)
  } else {
    await openInfoDialog(text)
  }
}
/**
 * エラーダイアログの開閉
 *
 * @param messageId - メッセージID
 *
 * @param firstBtnLabel - 第1ボタンラベルの（省略時は 'btn.yes'）
 */
function openErrorDialog(messageId: string, firstBtnLabel?: string) {
  // 第1ボタンラベル
  const btnLabel = firstBtnLabel ? t(firstBtnLabel) : t('btn.yes')

  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t(messageId),
      firstBtnType: 'normal1',
      firstBtnLabel: btnLabel,
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = Or28586Const.DEFAULT.YES

        if (event?.firstBtnClickFlg) {
          result = Or28586Const.DEFAULT.YES
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログの開閉
 *
 * @param messageId - メッセージID
 *
 * @returns - 確認ダイアログを閉じたタイミングで結果を返却
 */
function openInfoDialog(messageId: string): Promise<Or21814EventType | undefined> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t(messageId),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}
/**
 * 警告ダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.e-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = Or28586Const.DEFAULT.YES

        if (event?.firstBtnClickFlg) {
          result = Or28586Const.DEFAULT.YES
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

onMounted(async () => {
  // 指定日 システム日付
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  await getPrintSettingList()
  await initCodes()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 主治医意見書の改定フラグ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DOCTOR_REVISION_FLAG },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  // 利用者選択
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 履歴選択
  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 主治医意見書の改定フラグ
  localOneway.revisionOneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DOCTOR_REVISION_FLAG
  )
  // 選択モート 初期値
  localOneway.orX0130Oneway.selectMode = Or28586Const.DEFAULT.TANI
  // 選択モート 単一複数フラグ
  localOneway.orX0143Oneway.singleFlg = Or28586Const.DEFAULT.TANI
  mo01334TypeHistoryFlag.value = true
  userCols.value = 6
  setChildrenValue()
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: PrintSelectInEntity = {
    // セクション名:親画面.セクション名
    sectionName: localOneway.or28586Oneway.sectionName,
    // 職員ID:親画面.職員ID
    shokuId: localOneway.or28586Oneway.shokuId,
    // 法人ID:親画面.法人ID
    houjinId: localOneway.or28586Oneway.houjinId,
    // 施設ID:親画面.施設ID
    shisetuId: localOneway.or28586Oneway.shisetuId,
    // 事業所ID:親画面.事業所ID
    svJigyoId: localOneway.or28586Oneway.svJigyoId,
    // システムコード:共通情報.システムコード
    gsyscd: localOneway.or28586Oneway.systemCode,
    // 利用者ID:親画面.利用者ID
    userId: localOneway.or28586Oneway.userId,
  }

  // バックエンドAPIから初期情報取得
  const ret:ImplementationPlan2PrintSettingsInitUpdateOutEntity =
    await ScreenRepository.update('implementationPlan2PrintSettingsInitUpdate', inputData)
  local.or28586.prtList = ret.data.prtList
  local.or28586.kikanRirekiList = ret.data.kikanRirekiList.map((x) => {
    return {
      ...x,
      ikenshoFlg:
        localOneway.revisionOneway.items?.find((y) => y.value === x.ikenshoFlg)?.label ?? '',
    }
  })
  local.or28586.kikanFlg = ret.data.kikanFlag
  localOneway.orX0143Oneway.kikanFlg = ret.data.kikanFlag

  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of local.or28586.prtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtId,
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }
  localOneway.mo01334Oneway.items = mo01334OnewayList

  if (local.or28586.prtList.length > 0) {
    local.mo01334.value = local.or28586.prtList[0].prtId
  }
  setPrintOptions()
}

/**
 * 印刷オプションの有効化設定
 */
 function setPrintOptions() {
  const idx = local.or28586.prtList.findIndex((x) => x.prtId === local.mo01334.value)
  if (idx === 0) {
    // 画面.出力帳票印刷情報リストが一行目を選択した場合、非活性
    // 敬称を変更するチェックボックス
    localOneway.mo00018OneWayChangeTitle.disabled = true
    // 記入日を印刷する
    localOneway.mo00018OneWayPrintDate.disabled = true
  } else {
    // 以外の場合、活性
    // 敬称を変更するチェックボックス
    localOneway.mo00018OneWayChangeTitle.disabled = false
    // 記入日を印刷する
    localOneway.mo00018OneWayPrintDate.disabled = false
  }
  // 画面.利用者選択方法が「単一」 、且つ、 画面.履歴選択方法が「単一」の場合、活性
  if (
    localOneway.orX0130Oneway.selectMode === Or28586Const.DEFAULT.TANI &&
    localOneway.orX0143Oneway.singleFlg === Or28586Const.DEFAULT.TANI
  ) {
    // 記入用シートを印刷する
    localOneway.mo00018OneWayPrintTheForm.disabled = false
  }
  // 以外の場合、非活性
  else {
    localOneway.mo00018OneWayPrintTheForm.disabled = true
  }
}

/**
 * 履歴情報を取得する
 *
 * @param userId - 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  localOneway.orX0143Oneway.rirekiList = []
  const inputData: PrintUserChangeSelectInEntity = {
    // 事業所ＩＤ:親画面.事業所ＩＤ
    svJigyoId: localOneway.or28586Oneway.svJigyoId,
    // 利用者ID:画面.利用者一覧に選択行の利用者ID
    userId: userId,
  }
  // バックエンドAPIから初期情報取得
  const ret: ImplementationPlan2PrintSettingsUserChangeSelectEntity =
    await ScreenRepository.select(
      'implementationPlan2PrintSettingsUserChangeSelect',
      inputData
    )

  //週間表履歴リスト
  localOneway.orX0143Oneway.rirekiList = ret.data.kikanRirekiList.map((x) => {
    return {
      ...x,
      ikenshoFlg: localOneway.revisionOneway.items?.find((y) => y.value === x.ikenshoFlg)?.label,
    }
  })
}

/**
 * 子モジュールに値を渡す
 *
 */
function setChildrenValue() {
  setChildCpBinds(props.uniqueCpId, {
    [Or26328Const.CP_ID(0)]: {
      twoWayValue: {
        titleInput: local.titleInput,
      },
    },
    [Or26326Const.CP_ID(0)]: {
      twoWayValue: {
        mo01334Type: local.mo01334,
      },
    },
    [Or28780Const.CP_ID(0)]: {
      twoWayValue: {
        mo00039Type: local.mo00039Type,
        mo00020Type: local.mo00020Type,
      },
    },
    [Or18615Const.CP_ID(0)]: {
      twoWayValue: {
        mo00018TypeChangeTitle: local.mo00018TypeChangeTitle,
        mo00020Type: local.textInput,
      },
    },
    [Or26331Const.CP_ID(0)]: {
      twoWayValue: {
        mo00039OneWayUserSelectType: localOneway.orX0130Oneway.selectMode,
        mo00020TypeKijunbi: local.mo00020TypeKijunbi,
        mo00039OneWayHistorySelectType: localOneway.orX0143Oneway.singleFlg,
      },
    },
  })
}

/**
 * 「閉じるボタン」押下
 */
async function close() {
  if (!(await isValid())) {
    return false
  }
  await checkTitleInput()
  void save()
  setState({ isOpen: false })
}

/**
 * 画面印刷設定内容を保存
 *
 * @param h21SectionName - H21改訂版セクション名
 */
const save = async (h21SectionName?: string) => {
  const inputData: ImplementationPlan2PrintSettingsInfoUpdateInEntity<IPrintInfo> = {
    // セクション名:親画面.セクション名
    sectionName: localOneway.or28586Oneway.sectionName,
    // システムコード：親画面.システムコード
    gsyscd: localOneway.or28586Oneway.systemCode,
    // 職員ID：親画面.職員ID
    shokuId: localOneway.or28586Oneway.shokuId,
    // 法人ID：親画面.法人ID
    houjinId: localOneway.or28586Oneway.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.or28586Oneway.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or28586Oneway.svJigyoId,
    // 印刷設定情報リスト：印刷設定情報リスト
    prtList: local.or28586.prtList,
    // H21改訂版セクション名
    h21SectionName: h21SectionName,
  }
  await ScreenRepository.update('implementationPlan2PrintSettingsInfoUpdate', inputData)
}

/**
 * 「印刷」ボタン押下
 */
async function print() {
  if (!(await isValid())) {
    return false
  }
  if (
    !printCom.doCheckBeforePrint(
      local.titleInput.value,
      local.mo00018TypePrintTheForm.modelValue,
      localOneway.or28586Oneway.userList.length,
      localOneway.orX0143Oneway.rirekiList.length,
      showMessageBox
    )
  ) {
    return
  }

  // PDFダウンロード
  await executePrint()
}

/**
 * PDFダウンロード
 */
async function executePrint() {
  let resSubjectData = {} as ImplementationPlan2PrintSettingsSubjectSelectOutEntity
  if (localOneway.orX0130Oneway.selectMode === OrX0130Const.DEFAULT.HUKUSUU) {
    const inputData: PrintSubjectSelectInEntity = {
      // 利用者リスト：画面.利用者一覧で選択した利用者リスト
      userList: selectedUserList.value.map((x) => {
        return {
          userId: x.userId,
          userName: x.name1Knj + ' ' + x.name2Knj,
        }
      }),
      // 事業所ID：親画面.事業所ID
      svJigyoId: localOneway.or28586Oneway.svJigyoId,
      // 基準日：画面.基準日
      kijunbiYmd: local.mo00020TypeKijunbi.value,
    }
    resSubjectData = await ScreenRepository.select(
      'implementationPlan2PrintSettingsSubjectSelect',
      inputData
    )
  }

  const kaiteiFlgList: string[] = []
  // 利用者選択が「単一」の場合
  if (localOneway.orX0130Oneway.selectMode === Or28586Const.DEFAULT.TANI) {
    // 画面.記入用シートを印刷するチェックボックスがチェックOFFの場合
    if (!local.mo00018TypePrintTheForm.modelValue) {
      // 履歴一覧選択した改訂フラグを追加する。
      kaiteiFlgList.push(revertIkenshoFlg(selectedRirekiData.value[0].ikenshoFlg))
    }
    // 以外の場合
    else {
      // 履歴一覧データの件数が0より大きい場合
      if (local.or28586.kikanRirekiList.length > 0) {
        // 履歴一覧ですべて選択した改訂フラグを追加する
        selectedRirekiData.value.forEach((x) => {
          kaiteiFlgList.push(revertIkenshoFlg(x.ikenshoFlg))
        })
      }
      // 以外の場合
      else {
        // 親画面.初期設定マスタの情報.改訂フラグを追加する
        kaiteiFlgList.push(localOneway.or28586Oneway.initMasterObj.pkaiteiFlg)
      }
    }
  }
  // 利用者選択が「複数」の場合
  else {
    // AC018-2で取得した改定フラグリストの件数が0より大きい場合
    if (resSubjectData.data?.prtHistoryList.length > 0) {
      // AC018-2で取得した改定フラグリストを設定する。
      resSubjectData.data?.prtHistoryList.forEach((x) => {
        // ※重複を排除
        const kaiteiFlg = revertIkenshoFlg(x.ikenshoFlg)
        if (!kaiteiFlgList.includes(kaiteiFlg)) {
          kaiteiFlgList.push(revertIkenshoFlg(x.ikenshoFlg))
        }
      })
    }
  }

  //画面の印刷設定情報を保存する。
  await save()

  // 利用者選択が「単一」、且つ、履歴選択が「単一」の場合
  if (
    localOneway.orX0130Oneway.selectMode === Or28586Const.DEFAULT.TANI &&
    localOneway.orX0143Oneway.singleFlg === Or28586Const.DEFAULT.TANI
  ) {
    // 業務共通化処理の設定
    const selectedPrtData = local.or28586.prtList.find((x) => x.prtId === local.mo01334.value)
    if (selectedPrtData) {
            // 業務共通化処理の設定
      void printCom.doReportOutput(
        selectedPrtData,
        {
          userId: selectedUserList.value[0].selfId,
          rirekiId: selectedRirekiData.value[0].rirekiId,
        },
        {
          // 初期設定マスタの情報
          initMasterObj: props.onewayModelValue.initMasterObj,
          // 事業者名
          jigyoKnj: props.onewayModelValue.jigyoKnj,
        },
        {
          // 指定日
          // 印刷設定情報リスト選択行.日付表示有無が2の場合、画面の指定日
          selectDate:
            selectedPrtData.prnDate === '2'
              ? (Or28780Logic.data.get(or28780.value.uniqueCpId)?.mo00020Type.value ?? '')
              : '',
          // 記入用シートを印刷するフラグ
          emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
        },
        // システム日付
        props.onewayModelValue.sysYmd
      )
    }
  } else {
    // PDFダウンロードを行う
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 「印鑑欄ボタン」押下:GUI01110［印鑑欄設定］画面をポップアップで起動する
 */
 function openGUI01110() {
  // 親画面.システムコード
  localOneway.or10016Oneway.systemCode = localOneway.or28586Oneway.systemCode ?? ''
  // 親画面.法人ID
  localOneway.or10016Oneway.houjinId = localOneway.or28586Oneway.houjinId
  // 親画面.施設ID
  localOneway.or10016Oneway.shisetsuId = localOneway.or28586Oneway.shisetuId
  // 親画面.事業所ID
  localOneway.or10016Oneway.jigyoshoId = localOneway.or28586Oneway.svJigyoId
  // 親画面.職員ID  ※ログイン情報
  localOneway.or10016Oneway.shokuinId = localOneway.or28586Oneway.shokuId
  // 帳票セクション番号: 帳票セクション番号:親画面.セクション名 + (H21改訂版)
  localOneway.or10016Oneway.reportSectionNumber = localOneway.or28586Oneway.sectionName + Or28586Const.H_21
  // 会議禄フラグ: 0:非表示
  localOneway.or10016Oneway.conferenceFlag = false
  // アセスメント:親画面.初期設定マスタの情報.パッケージプラン改訂フラグ（2:H21/4、1:旧様式）
  localOneway.or10016Oneway.assessment = localOneway.or28586Oneway.initMasterObj.pkaiteiFlg

  // Or10016のダイアログ開閉状態を更新する
  Or10016Logic.state.set({
    uniqueCpId: or10016.value.uniqueCpId,
    state: { isOpen: true },
  })
}


/**
 * 改定フラグ値設定
 *
 * @param ikenshoFlg - 改定フラグ
 */
function revertIkenshoFlg(ikenshoFlg: string) {
  return (
    localOneway.revisionOneway.items?.find((x) => x.label === ikenshoFlg)?.value.toString() ?? ''
  )
}
/**
 * 担当ケアマネプルダウン
 *
 * @param newValue - 選択値
 */
function handleOrX0145Update(newValue: OrX0145Type) {
  // 職員が選択された場合
  if (newValue?.value && !Array.isArray(newValue.value) && 'chkShokuId' in newValue.value) {
    // 担当ケアマネ
    localOneway.orX0130Oneway.tantouCareManager = newValue.value.chkShokuId
  } else {
    // 担当ケアマネ
    localOneway.orX0130Oneway.tantouCareManager = ''
  }
}
/**
 * 「出力帳票名」選択
 */
watch(
  () => Or26326Logic.data.get(or26326.value.uniqueCpId)?.mo01334Type.value,
  (newValue, oldValue) => {
    if (newValue) {
      local.mo01334.value = newValue
      setPrintOptions()
    }
    if (oldValue) {
      for (const item of localOneway.mo01334Oneway.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.帳票タイトルが空白以外の場合
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            if (local.titleInput.value) item.prtTitle = local.titleInput.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prnDate = local.mo00039Type
            // 指定日
            item.mo00020Type = local.mo00020Type.value ?? systemCommonsStore.getSystemDate!
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ03 = 画面.敬称を変更する
            item.param03 = local.mo00018TypeChangeTitle.modelValue ? '0' : '1'
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ04 = 画面.敬称
            item.param04 = local.textInput.value
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ06 = 画面.記入日を印刷する
            item.param06 = local.mo00018TypePrintMode.modelValue ? '0' : '1'
            // 画面.基準日
            item.mo00020TypeKijunbi = local.mo00020TypeKijunbi.value
          }
        }
      }
    }
    for (const item of localOneway.mo01334Oneway.items) {
      if (item) {
        if (newValue === item.id) {
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prnDate as string
          // 指定日
          local.mo00020Type = {
            value: (item?.mo00020Type as string) ?? systemCommonsStore.getSystemDate!,
          } as Mo00020Type
          // 画面.敬称を変更する = 画面.出力帳票一覧明細に選択される行.パラメータ03
          local.mo00018TypeChangeTitle.modelValue = item?.param03 === '0' ? true : false
          // 画面.敬称 = 画面.出力帳票一覧明細に選択される行.パラメータ04
          local.textInput.value = item?.param04 as string
          // 画面.記入用シートを印刷する = 0(チェックオフ)
          local.mo00018TypePrintTheForm.modelValue = true
          // 画面.画面.記入日を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ06
          local.mo00018TypePrintMode.modelValue = item?.param06 === '0' ? true : false
          // 画面.基準日
          local.mo00020TypeKijunbi.value = (item?.mo00020TypeKijunbi ??
            systemCommonsStore.getSystemDate!) as string
        }
      }
    }
    setChildrenValue()
  }
)

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 履歴選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331.value.uniqueCpId)?.mo00039OneWayHistorySelectType,
  (newValue) => {
    if (newValue) {
      // 履歴選択方法が「単一」の場合
      if (newValue === Or28586Const.DEFAULT.TANI) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.TANI
        localOneway.orX0117Oneway.type = '1'
      }
      if (newValue === Or28586Const.DEFAULT.HUKUSUU) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.HUKUSUU
        localOneway.orX0117Oneway.type = '0'
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
      }
      setPrintOptions()
      setChildrenValue()
    }
  }
)

/**
 * 利用者選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331.value.uniqueCpId)?.mo00039OneWayUserSelectType,
  (newValue) => {
    if (newValue) {
      if (newValue === Or28586Const.DEFAULT.TANI) {
        // 選択モート
        localOneway.orX0130Oneway.selectMode = Or28586Const.DEFAULT.TANI
        userCols.value = 6
        mo01334TypeHistoryFlag.value = true
        localOneway.orX0117Oneway.type = '1'
      }
      if (newValue === Or28586Const.DEFAULT.HUKUSUU) {
        localOneway.orX0117Oneway.type = '0'
        // 選択モート
        localOneway.orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
        userCols.value = 11
        mo01334TypeHistoryFlag.value = false
      }
      setPrintOptions()
      setChildrenValue()
    }
  }
)

/**
 * 「履歴一覧」明細選択の監視
 */
watch(
  () => OrX0143Logic.event.get(orX0143.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    selectedRirekiData.value = newValue.orX0143DetList as KikanRirekiData[]
  }
)

/**
 * 日付印刷の監視
 */
watch(
  () => Or28780Logic.data.get(or28780.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // 日付印刷区分
      local.mo00039Type = newValue.mo00039Type
      // 指定日
      local.mo00020Type = newValue.mo00020Type
    }
  }
)


/**
 * 帳票タイトルの監視
 */
watch(
  () => Or26328Logic.data.get(or26328.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // タイトル
      local.titleInput = newValue.titleInput
    }
  }
)

/**
 * 印刷オプションの監視
 */
watch(
  () => Or18615Logic.data.get(or18615.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // 敬称を変更する
      local.mo00018TypeChangeTitle = newValue.mo00018TypeChangeTitle
      // 敬称テキスト
      // local.textInput = newValue.mo00020Type
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    selectedUserList.value = newValue!.userList
    if (newValue?.clickFlg && localOneway.orX0130Oneway.selectMode === Or28586Const.DEFAULT.TANI) {
      // 利用者選択
      // 利用者選択方法が「単一」の場合
      await getHistoricalInfoList(newValue.userList[0].userId)
    }
  }
)

/**
 * 担当ケアマネの監視
 */
watch(
  () => local.orX0145,
  (newValue) => {
    if (newValue) {
      localOneway.orX0130Oneway.tantouCareManager = (newValue.value as TantoCmnShokuin).shokuinKnj
    }
  }
)
/**
 * バリデーション関数
 */
async function isValid() {
  return (await form.value!.validate()).valid
}
</script>

<template>
  <c-v-form
    ref="form"
    class="h-100"
  >
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or28586_screen"
      >
        <!-- 帳票 -->
        <g-custom-or-26326
          v-bind="or26326"
          :oneway-model-value="localOneway.mo01334Oneway"
        />
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 or28586_border_right content_center"
        >
        <c-v-row style="margin-left: 8px; margin-top: 8px; margin-bottom: 8px">
              <!--印鑑欄-->
              <base-mo00610
                class="mr-2"
                :oneway-model-value="localOneway.mo00610OneWay"
                @click="openGUI01110"
              />
            </c-v-row>
            <c-v-divider class="my-0"></c-v-divider>
          <!-- タイトル -->
          <g-custom-or-26328
            v-bind="or26328"
            :oneway-model-value="localOneway.or26328Oneway"
          />
          <c-v-divider class="my-0"></c-v-divider>
          <!-- 日付印刷 -->
          <g-custom-or-28780
            v-bind="or28780"
            :oneway-model-value="localOneway.mo00039OneWay"
          />

          <c-v-divider class="my-0"></c-v-divider>
          <!-- 印刷オプション -->
          <g-custom-or-18615
            v-bind="or18615"
            :oneway-model-value="{
              mo00018OneWayChangeTitle: localOneway.mo00018OneWayChangeTitle,
              mo00045OnewayTextInput: localOneway.mo00045OnewayTextInput,
            }"
          >
            <template #optionPrintItems>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 記入用シートを印刷するチェックボックス -->
                <base-mo00018
                  v-model="local.mo00018TypePrintTheForm"
                  :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2 pb-0"
              >
                <!-- 履歴情報を印刷する -->
                <base-mo00018
                  v-model="local.mo00018HistoryPrintTheForm"
                  :oneway-model-value="localOneway.mo00018HistoryPrintTheForm"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2 pt-0"
              >
                <!-- 印刷補足ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338PrintSupplement"
                >
                </base-mo01338>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 行の先頭で改ページする -->
                <base-mo00018
                  v-model="local.mo00018PageBreakLineStart"
                  :oneway-model-value="localOneway.mo00018PageBreakLineStart"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 援助内容タイトルを課題毎に印刷する -->
                <base-mo00018
                  v-model="local.mo00018SupportContentPrintTheTitleByTopic"
                  :oneway-model-value="localOneway.mo00018SupportContentPrintTheTitleByTopic"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2 pb-0"
              >
                <!-- 課題番号を印刷する -->
                <base-mo00018
                  v-model="local.mo00018PrintTheProjectNumber"
                  :oneway-model-value="localOneway.mo00018PrintTheProjectNumber"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2 pt-0"
              >
                <!-- 印刷補足2ラベル -->
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338PrintSupplementTwo"
                >
                </base-mo01338>
              </c-v-col>
            </template>
          </g-custom-or-18615>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
        >
          <c-v-row
            class="or28586_row"
            no-gutter
            style="align-items: center"
          >
            <!-- 利用者選択 -->
            <g-custom-or-26331
              v-bind="or26331"
              :oneway-model-value="{
                mo00039OneWayUserSelectType: localOneway.mo00039OneWayUserSelectType,
                mo00039OneWayHistorySelectType: localOneway.mo00039OneWayHistorySelectType,
              }"
              :unique-cp-id="or26331.uniqueCpId"
            />
            <!-- 担当ケアマネラベル -->
            <g-custom-or-x-0145
              v-bind="orX0145"
              v-model="local.orX0145"
              :oneway-model-value="localOneway.orX0145Oneway"
              @update:model-value="handleOrX0145Update"
            ></g-custom-or-x-0145>
          </c-v-row>
          <c-v-row
            class="or28586_row grid-width"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              style="overflow-x: auto"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="localOneway.orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="localOneway.orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag"
              cols="12"
              sm="6"
              class="pl-2 pr-0"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0143
                v-if="localOneway.orX0143Oneway.singleFlg"
                v-bind="orX0143"
                :oneway-model-value="localOneway.orX0143Oneway"
              ></g-custom-or-x-0143>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mr-2"
          :disabled="!prtFlg"
          @click="print()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  </c-v-form>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  >
  </g-base-or21815>
  <!-- GUI01110_「印鑑欄設定」画面を起動する -->
  <g-custom-or-10016
    v-if="showDialogOr10016"
    v-bind="or10016"
    :oneway-model-value="localOneway.or10016Oneway"
  />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117"
    :oneway-model-value="localOneway.orX0117Oneway"
  ></g-custom-or-x-0117>
  <g-custom-or-26261
    v-if="showDialogOr26261"
    v-bind="or26261"
    v-model="local.or26261"
    :oneway-model-value="localOneway.or26261OneWay"
  />
</template>

<style scoped lang="scss">
.or28586_screen {
  margin: -8px !important;
}

.or28586_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or28586_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.customCol {
  margin-left: 0px;
  margin-right: 0px;
}

.grid-width {
  min-width: 694px;
}
</style>
