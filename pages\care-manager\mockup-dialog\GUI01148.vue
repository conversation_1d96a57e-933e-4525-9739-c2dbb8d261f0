<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or27573Logic } from '~/components/custom-components/organisms/Or27573/Or27573.logic'
import { Or27573Const } from '~/components/custom-components/organisms/Or27573/Or27573.constants'
import type { Or27573OnewayType, Or27573Type } from '~/types/cmn/business/components/Or27573Type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01148'
// ルーティング
const routing = 'GUI01148/pinia'
// 画面物理名
const screenName = 'GUI01148'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27573 = ref({ uniqueCpId: Or27573Const.CP_ID(1) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01148' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27573Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27573.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01148',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27573Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27573Const.CP_ID(1)]: or27573.value,
})

const or27573Data: Or27573OnewayType = {
  /** 事業所ID*/
  jigyoId: '1',
  /**職員ID */
  staffId: '8',
}

const or27573Type = ref<Or27573Type>({
  checkboxFlg: { modelValue: false } as Mo00018Type,
  /** 事業所ID*/
  jigyoId: '',
  /**職員ID */
  staffId: '',
})

/**
 *  ボタン押下時の処理(Or27573)
 *
 */
function onClickOr27573() {
  // Or27573のダイアログ開閉状態を更新する
  Or27573Logic.state.set({
    uniqueCpId: or27573.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr27573 = computed(() => {
  // Or10320のダイアログ開閉状態
  return Or27573Logic.state.get(or27573.value.uniqueCpId)?.isOpen ?? false
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27573()"
        >GUI01148_関連事業所マスタ
      </v-btn>
      <g-custom-or-27573
        v-if="showDialogOr27573"
        v-bind="or27573"
        v-model="or27573Type"
        :oneway-model-value="or27573Data"
      />
    </c-v-col>
  </c-v-row>
</template>
