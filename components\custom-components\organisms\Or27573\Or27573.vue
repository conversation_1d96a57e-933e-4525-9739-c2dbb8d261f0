<script setup lang="ts">
/**
 * Or27573:有機体:モーダル（関連事業所マスタ）
 * GUI01148_［関連事業所マスタモーダル］画面
 *
 * @description
 * 関連事業所マスタモーダル
 *
 * <AUTHOR>
 */
import { onMounted, reactive, watch, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26369Logic } from '../Or26369/Or26369.logic'
import { Or26369Const } from '../Or26369/Or26369.constants'
import { Or27573Const } from './Or27573.constants'

import type {
  Or27573DataType,
  Or27573StateType,
  KaigoInfoType,
  YohouInfoType,
  SoukouInfoType,
  JigyoAreaInfoType,
  OfficeInfoType,
} from './Or27573.type'
import { hasPrintAuth, hasRegistAuth, useScreenOneWayBind } from '#build/imports'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Or27573Type, Or27573OnewayType } from '~/types/cmn/business/components/Or27573Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type {
  RelatedOfficeMstInfoSelectInEntity,
  RelatedOfficeMstInfoSelectOutEntity,
  RelatedOfficeMstInfoUpdateInEntity,
} from '~/repositories/cmn/entities/RelatedOfficeMstInfoSelectEntity'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Or10857TwowayType } from '~/types/cmn/business/components/Or10857Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import type { Mo01354Headers } from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Or26369OnewayType } from '~/types/cmn/business/components/Or26369Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or27573Type
  onewayModelValue: Or27573OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
const isPermissionRegist = ref<boolean>(true)
/**************************************************
 * 変数定義
 **************************************************/

const mo00024 = ref<Mo00024Type>({
  isOpen: Or27573Const.DEFAULT.IS_OPEN,
})
// Mo00039共通情報設定
const mo00039OnewayType = {
  showItemLabel: false,
} as Mo00039OnewayType
const printBtnMo00009 = ref<Mo00009OnewayType>({
  btnIcon: 'print',
  icon: true,
  width: '32px',
  disabled: false,
  tooltipLocation: 'right',
})

// 事業所一覧初期情報
const tableData = ref<Or27573DataType>({
  showFlg: '',
  groupKbn: '',
  subjigyouId: '',
  jigyoAreaList: [],
  kaigoList: [],
  yohouList: [],
  soukouList: [],
  jigyouList: [],
})

const tableDataList = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

let radioValue = 0 as number

const tableDataFilter = computed(() => {
  const jigyouList = tableData.value.jigyouList.filter((item) => item.dataShowFlg)
  return jigyouList
})
// Or21814_有機体:エラーダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })
const or21814_2 = ref({ uniqueCpId: Or21814Const.CP_ID(2) })
const or26369 = ref({ uniqueCpId: Or26369Const.CP_ID(1) })
// 介護サービスリスト
const kaigoListVariableList = ref<KaigoInfoType[]>([])
// 予防サービスリスト
const yohouListVariableList = ref<YohouInfoType[]>([])
// 総合事業サービスリスト
const soukouListVariableList = ref<SoukouInfoType[]>([])
//事業所エリアリスト
const jigyoAreaListVariableList = ref<JigyoAreaInfoType[]>([])
//変数
const selectOptionLabelList = ref<CodeType[]>([])
const selectOfficeAreaList = ref<CodeType[]>([])
const selectedItem1 = ref({ value: '' } as Mo00045Type)
const selectedItem2 = ref({ value: '' } as Mo00045Type)
const selectValue = ref<string>('')
const radioLabelValue = ref<string>('')
const selectValue2 = ref<string>('')
const length = ref<number>()
const emitFlag = ref<boolean>()
const saveDate = ref<OfficeInfoType[]>([])

const selectedValuesInit = ref<string[]>([])

// 情報ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '900px',
  height: '720px',
  persistent: true,
  showCloseBtn: false,
  mo01344Oneway: {
    name: 'Or27573',
    toolbarTitle: t('label.related-office-master'),
    toolbarName: 'Or27573ToolBar',
    // ツールバータイトルの左寄せ
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'pa-2 pb-0',
  } as Mo01344OnewayType,
})
const defaultOnewayModelValue: Or27573OnewayType = {
  // 事業所ID
  jigyoId: '',
  // 職員ID
  staffId: '',
}
const defaultModelValue: Or27573Type = {
  checkboxFlg: { modelValue: true } as Mo00018Type,
  jigyoId: '',
  staffId: '',
}
const mo00045Oneway = ref<Mo00045OnewayType>({
  name: 'historyTextField',
  class: 'index',
  showItemLabel: false,
  maxLength: Or27573Const.DEFAULT.MAX_DIGITS_STANDARD,
  rules: [Number(Or27573Const.DEFAULT.MAX_DIGITS_STANDARD)],
  readonly: false,
})
const mo00045Oneway2 = ref<Mo00045OnewayType>({
  name: 'historyTextField2',
  class: 'index',
  showItemLabel: false,
  maxLength: Or27573Const.DEFAULT.MAX_DIGITS_EXTENDED,
  rules: [Number(Or27573Const.DEFAULT.MAX_DIGITS_EXTENDED)],
  readonly: false,
  width: '250px',
})
const localOneway = reactive({
  or27573: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00018Oneway: {
    showItemLabel: false,
  } as Mo00018OnewayType,
  mo00018OnewayServiceType: {
    checkboxLabel: t('label.narrow-down-by-service-type-2'),
    customClass: { labelClass: 'width: 200px' },
    showItemLabel: false,
  } as Mo00018OnewayType,
  mo00018Type: {
    modelValue: true,
  } as Mo00018Type,
  mo00009oneway: {
    btnIcon: 'edit_square',
    icon: true,
  } as Mo00009OnewayType,
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.save'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.display-row-save'),
  } as Mo00609OnewayType,
  mo00039Oneway: {
    ...mo00039OnewayType,
    items: [],
    showItemLabel: false,
  } as Mo00039OnewayType,
  mo01338onewayServiceType: {
    value: t('label.service-type'),
    valueFontWeight: 'bolder',
    customClass: { labelStyle: 'display:none !important' },
  } as Mo01338OnewayType,
  mo01338onewayOfficerCode: {
    value: t('label.plan-business-name'),
    valueFontWeight: 'bolder',
    customClass: { labelStyle: 'display:none !important' },
  } as Mo01338OnewayType,
  mo01338onewayOfficerName: {
    value: t('label.office-short-name'),
    valueFontWeight: 'bolder',
    customClass: { labelStyle: 'display:none !important' },
  } as Mo01338OnewayType,
  mo01338onewayOfficeArea: {
    value: t('label.office-area'),
    valueFontWeight: 'bolder',
    customClass: { labelStyle: 'display:none !important' },
  } as Mo01338OnewayType,
  mo01334Oneway: {
    height: '422px',
    items: [],
    headers: [
      //事業所番号
      {
        title: t('label.provider-number'),
        minWidth: '100px',
        sortable: true,
        key: 'jigyouNo',
      },
      //事業名略称
      {
        title: t('label.office-short-name'),
        minWidth: '240px',
        sortable: true,
        key: 'jigyouName',
      },
      //事業種別
      {
        title: t('label.ruaku-knj'),
        minWidth: '160px',
        sortable: true,
        key: 'jigyouType',
      },
      //事業所エリア
      {
        title: t('label.office-area'),
        minWidth: '240px',
        sortable: true,
        key: 'jigyouAreaNm',
      },
    ],
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    density: 'default',
    mandatory: false,
    showSelect: true,
    selectStrategy: 'all',
  } as Mo01334OnewayType,
  mo01334Oneway2: {
    height: '422px',
    items: [],
    headers: [
      //事業所番号
      {
        title: t('label.provider-number'),
        minWidth: '140px',
        sortable: true,
        key: 'jigyouNo',
      },
      //事業所略称
      {
        title: t('label.office-short-name'),
        minWidth: '300px',
        sortable: true,
        key: 'jigyouName',
      },
      //事業種別
      {
        title: t('label.ruaku-knj'),
        minWidth: '160px',
        sortable: true,
        key: 'jigyouType',
      },
    ] as Mo01354Headers[],
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    density: 'default',
    mandatory: false,
    showSelect: true,
    selectStrategy: 'all',
  } as Mo01334OnewayType,
  or26369Oneway: {} as Or26369OnewayType,
})
const local = reactive({
  // 必要な事業プログラム
  or27573: {
    ...defaultModelValue,
    ...props.modelValue,
  },
  // _必要な事業プログラム取込
  or10857: {} as Or10857TwowayType,
  // サービス種別リスト
  radioLabelList: [] as CodeType[],
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27573StateType>({
  cpId: Or27573Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27573Const.DEFAULT.IS_OPEN
    },
  },
})
// ■共通処理の登録権限チェックを行う
const hasRegist = await hasRegistAuth()
const hasPrint = await hasPrintAuth()

const radioLabelListInit = ref<string>('')
/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(async () => {
  await initCodes()
  await init()
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

//メッセージの選択結果を監視
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
  }
)
/**
 * 選択行クラス付与関数
 *
 */
const getItemClass = () => {
  // 選択行の場合「selected-row」のクラスを付与
  return { class: { 'selected-row': true } }
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 有効期間
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SRVSBLIST },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  local.radioLabelList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_SRVSBLIST)
  radioLabelListInit.value = local.radioLabelList[0].value
}

/**
 * 事業所エリア名称取得処理
 *
 * @param id - 事業所エリアID
 *
 * @param jigyoAreaList - 事業所エリアリスト
 */
function getAreaName(id: string, jigyoAreaList: JigyoAreaInfoType[]) {
  const ret = jigyoAreaList.filter((item) => item.svId === id)
  if (ret.length > 0) {
    return ret[0].svName
  }
  return ''
}

/**
 * 事業所エリアソート順取得処理
 *
 * @param id - 事業所エリアID
 *
 * @param jigyoAreaList - 事業所エリアリスト
 */
function getAreaSort(id: string, jigyoAreaList: JigyoAreaInfoType[]) {
  const ret = jigyoAreaList.filter((item) => item.svId === id)
  if (ret.length > 0) {
    return ret[0].sort
  }
  return ''
}

/**
 * AC001_関連事業所マスタの初期情報を取得する。
 */
async function init() {
  const inputData: RelatedOfficeMstInfoSelectInEntity = {
    /** 事業所ID */
    jigyousyoId: props.onewayModelValue.jigyoId,
    /** 職員ID */
    syokuId: props.onewayModelValue.staffId,
  }
  // 関連事業所マスタの初期情報を取得する。
  const ret: RelatedOfficeMstInfoSelectOutEntity = await ScreenRepository.select(
    'relatedOfficeMstInfoSelect',
    inputData
  )
  tableData.value.showFlg = ret.data.showFlg
  tableData.value.groupKbn = ret.data.groupKbn
  let num = 0
  tableData.value.jigyouList = []
  for (const acquInfo of ret.data.jigyouList) {
    tableData.value.jigyouList.push({
      //sel
      sel: acquInfo.sel,
      //事業所ID
      jigyouId: acquInfo.jigyouId,
      //事業者コード
      jigyouCd: acquInfo.jigyouCd,
      // 事業所番号
      jigyouNo: acquInfo.jigyouNo,
      // 事業所略称
      jigyouName: acquInfo.jigyouName,
      // 事業所エリア
      jigyouArea: acquInfo.jigyouArea,
      //事業所種別ID
      jigyouTypeId: acquInfo.jigyouTypeId,
      // 事業種別
      jigyouType: acquInfo.jigyouType,
      //適用フラグ
      jigyouFlg: acquInfo.applyFlg,
      applyFlg: acquInfo.applyFlg,
      id: '' + num++,
      dataShowFlg: true,
      jigyoSort: acquInfo.jigyoSort,
      // 事業所エリア名
      jigyouAreaNm: getAreaName(acquInfo.jigyouArea, ret.data.jigyoAreaList),
      // 事業所エリアソート順
      jigyouAreaSort: getAreaSort(acquInfo.jigyouArea, ret.data.jigyoAreaList),
    })
  }
  //介護サービスリスト
  kaigoListVariableList.value = []
  for (const acquInfo of ret.data.kaigoList) {
    kaigoListVariableList.value.push({
      svId: acquInfo.svId,
      // サービス略称
      svName: acquInfo.svName,
    })
  }
  //総合事業サービスリスト
  soukouListVariableList.value = []
  for (const acquInfo of ret.data.soukouList) {
    soukouListVariableList.value.push({
      svId: acquInfo.svId,
      // サービス略称
      svName: acquInfo.svName,
    })
  }
  //予防サービスリスト
  yohouListVariableList.value = []
  for (const acquInfo of ret.data.yohouList) {
    yohouListVariableList.value.push({
      svId: acquInfo.svId,
      // サービス略称
      svName: acquInfo.svName,
    })
  }
  //事業所エリアリスト
  jigyoAreaListVariableList.value = []
  for (const acquInfo of ret.data.jigyoAreaList) {
    jigyoAreaListVariableList.value.push({
      svId: acquInfo.svId,
      // サービス略称
      svName: acquInfo.svName,
      // 表示順
      sort: acquInfo.sort,
    })
  }
  local.or27573.checkboxFlg.modelValue = true
  selectValue.value = kaigoListVariableList.value[0].svName
  selectValue2.value = ''
  officeSelectRadioClick(0)
}
const buttonsState = ref({
  choose: false,
  select: false,
})

function enableAll(flag: Mo00018Type) {
  if (flag.modelValue !== true) {
    buttonsState.value = {
      choose: true,
      select: true,
    }
  } else {
    buttonsState.value = {
      choose: false,
      select: false,
    }
  }
  updateSelectedItems()
}

/**
 * AC002_「×ボタン」押下
 * AC008_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  emitFlag.value = false
  if (selectedValuesInit.value.length !== tableDataList.value.values.length) {
    emitFlag.value = true
  } else {
    for (const item of tableDataList.value.values) {
      if (!selectedValuesInit.value.includes(item)) {
        emitFlag.value = true
        break
      }
    }
  }

  if (emitFlag.value) {
    // 共通処理の編集権限チェックを行う。
    if (!isPermissionRegist.value) {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
      showOr21814MsgTwoBtn(t('message.w-cmn-10006'))
    } else {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
      showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
    }
  } else {
    setState({ isOpen: false })
  }
}
/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * Or21813のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.firstBtnClickFlg) {
      await save()
    } else if (newValue?.thirdBtnClickFlg) {
      return
    } else {
      setState({ isOpen: false })
    }
  }
)
/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgThreeBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

//選択モーダルをポップアップで起動する
function officeSelectIconClick() {}

// 入力ボックスの変化を聞きます
watch(selectedItem1, () => {
  updateSelectedItems()
})
watch(selectedItem2, () => {
  updateSelectedItems()
})
watch(selectValue, () => {
  updateSelectedItems()
})
watch(selectValue2, () => {
  updateSelectedItems()
})

// リアルタイムでデータを検索します
function updateSelectedItems() {
  // リスト全要素をtrueで再初期化 → 複合フィルタ適用
  tableData.value.jigyouList = tableData.value.jigyouList.map((item) => {
    return {
      ...item,
      dataShowFlg: true,
    }
  })
  if (selectedItem1.value.value !== '') {
    tableData.value.jigyouList = tableData.value.jigyouList.map((item) => {
      return {
        ...item,
        dataShowFlg: item.jigyouNo
          .toString()
          .toLowerCase()
          .includes(selectedItem1.value.value.toString().toLowerCase()),
      }
    })
  }
  if (selectedItem2.value.value !== '') {
    tableData.value.jigyouList = tableData.value.jigyouList.map((item) => {
      let filterShowFlg = false
      if (item.dataShowFlg) {
        filterShowFlg = item.jigyouName
          .toString()
          .toLowerCase()
          .includes(selectedItem2.value.value.toString().toLowerCase())
      }
      return {
        ...item,
        dataShowFlg: filterShowFlg,
      }
    })
  }
  if (selectValue.value !== '') {
    if (!buttonsState.value.select) {
      tableData.value.jigyouList = tableData.value.jigyouList.map((item) => {
        let filterShowFlg = false
        if (item.dataShowFlg) {
          filterShowFlg = item.jigyouType
            .toLowerCase()
            .includes(selectValue.value.toString().toLowerCase())
        }
        return {
          ...item,
          dataShowFlg: filterShowFlg,
        }
      })
    } else {
      tableData.value.jigyouList = tableData.value.jigyouList.map((item) => {
        let filterShowFlg = false
        if (item.dataShowFlg) {
          filterShowFlg = item.jigyouType
            .toLowerCase()
            .includes(radioLabelValue.value.toString().toLowerCase())
        }
        return {
          ...item,
          dataShowFlg: filterShowFlg,
        }
      })
    }
  }
  if (selectValue2.value !== '') {
    const optionLabel = selectOfficeAreaList.value.find((item) => item.value === selectValue2.value)
    const text = optionLabel?.label ?? ''
    tableData.value.jigyouList = tableData.value.jigyouList.map((item) => {
      let filterShowFlg = false
      if (item.dataShowFlg) {
        filterShowFlg = item.jigyouAreaNm
          .toString()
          .toLowerCase()
          .includes(text.toString().toLowerCase())
      }
      return {
        ...item,
        dataShowFlg: filterShowFlg,
      }
    })
  }
  updateTableData()
}

function updateTableData() {
  if (tableData.value.showFlg === '1') {
    localOneway.mo01334Oneway.items = tableDataFilter.value.map((item) => {
      return {
        ...item,
        jigyouFlg: { modelValue: item.jigyouFlg === '1' ? true : false } as Mo00018Type,
        jigyouNo: item.jigyouNo,
        jigyouName: item.jigyouName,
        jigyouType: item.jigyouType,
        jigyouArea: item.jigyouArea,
        jigyouAreaNm: item.jigyouAreaNm,
        jigyouAreaSort: item.jigyouAreaSort,
      }
    })
  } else {
    localOneway.mo01334Oneway2.items = tableDataFilter.value.map((item) => {
      return {
        ...item,
        jigyouFlg: { modelValue: item.jigyouFlg === '1' ? true : false } as Mo00018Type,
        jigyouNo: item.jigyouNo,
        jigyouName: item.jigyouName,
        jigyouType: item.jigyouType,
        jigyouArea: item.jigyouArea,
      }
    })
  }

  length.value = tableDataList.value.values.length
}

function officeNoSelectClick(selectedItem: string) {
  if (selectedItem === '') {
    tableData.value.jigyouList = tableData.value.jigyouList.map((item) => {
      return {
        ...item,
        dataShowFlg:
          item.id.toLowerCase().includes(radioValue.toString().toLowerCase()) &&
          item.jigyouType.toLowerCase().includes(selectValue.value.toString().toLowerCase()),
      }
    })
    if (tableData.value.showFlg === '1') {
      localOneway.mo01334Oneway.items = tableDataFilter.value.map((item) => {
        return {
          ...item,

          jigyouFlg: { modelValue: item.jigyouFlg === '1' ? true : false } as Mo00018Type,
          jigyouNo: item.jigyouNo,
          jigyouName: item.jigyouName,
          jigyouType: item.jigyouType,
          jigyouArea: item.jigyouArea,
          jigyouAreaNm: item.jigyouAreaNm,
          jigyouAreaSort: item.jigyouAreaSort,
        }
      })
    } else {
      localOneway.mo01334Oneway2.items = tableDataFilter.value.map((item) => {
        return {
          ...item,

          jigyouFlg: { modelValue: item.jigyouFlg === '1' ? true : false } as Mo00018Type,
          jigyouNo: item.jigyouNo,
          jigyouName: item.jigyouName,
          jigyouType: item.jigyouType,
          jigyouArea: item.jigyouArea,
        }
      })
    }
  }
  tableData.value.jigyouList = tableData.value.jigyouList.map((item) => {
    // dataShowFlg が true のときのみ条件判定を行う
    let shouldShow = false
    if (item.dataShowFlg) {
      shouldShow =
        item.jigyouNo.toString().toLowerCase().includes(selectedItem.toString().toLowerCase()) &&
        item.jigyouType.toLowerCase().includes(selectValue.value.toString().toLowerCase())
    }
    return {
      ...item,
      dataShowFlg: shouldShow,
    }
  })
  if (tableData.value.showFlg === '1') {
    localOneway.mo01334Oneway.items = tableDataFilter.value.map((item) => {
      return {
        ...item,
        jigyouFlg: { modelValue: item.jigyouFlg === '1' ? true : false } as Mo00018Type,
        jigyouNo: item.jigyouNo,
        jigyouName: item.jigyouName,
        jigyouType: item.jigyouType,
        jigyouArea: item.jigyouArea,

        jigyouAreaNm: item.jigyouAreaNm,
        jigyouAreaSort: item.jigyouAreaSort,
      }
    })
  } else {
    localOneway.mo01334Oneway2.items = tableDataFilter.value.map((item) => {
      return {
        ...item,
        jigyouFlg: { modelValue: item.jigyouFlg === '1' ? true : false } as Mo00018Type,
        jigyouNo: item.jigyouNo,
        jigyouName: item.jigyouName,
        jigyouType: item.jigyouType,
        jigyouArea: item.jigyouArea,

        jigyouAreaNm: item.jigyouAreaNm,
        jigyouAreaSort: item.jigyouAreaSort,
      }
    })
  }
  selectedItem1.value.value = selectedItem
}

function officeSelectRadioClick(id: number) {
  selectOptionLabelList.value = []
  selectOfficeAreaList.value = []
  const matchedItem = local.radioLabelList.find((item) => item.value === id + '')
  radioLabelValue.value = matchedItem ? matchedItem.label : ''
  //変数.介護サービスリストの内容を画面.サービス種類のプルダウンリストに入れる
  if (id === 0) {
    kaigoListVariableList.value.forEach((item) => {
      selectOptionLabelList.value.push({ label: item.svName, value: item.svId })
    })
    selectValue.value = selectOptionLabelList.value[0].label
    //変数.予防サービスリストの内容を画面.サービス種類のプルダウンリストに入れる
  } else if (id === 1) {
    yohouListVariableList.value.forEach((item) => {
      selectOptionLabelList.value.push({ label: item.svName, value: item.svId })
    })
    selectValue.value = selectOptionLabelList.value[0].label
    //変数.総合事業サービスリストの内容を画面.サービス種類のプルダウンリストに入れる
  } else if (id === 2) {
    soukouListVariableList.value.forEach((item) => {
      selectOptionLabelList.value.push({ label: item.svName, value: item.svId })
    })
    selectValue.value = selectOptionLabelList.value[0].label
  }
  radioValue = id
  //事業所エリアリスト
  jigyoAreaListVariableList.value.forEach((item) => {
    selectOfficeAreaList.value.push({ label: item.svName, value: item.svId })
  })
  selectValueClick(selectValue.value)
}

function selectValueClick(selectValue: string) {
  tableData.value.jigyouList = tableData.value.jigyouList.map((item) => {
    return {
      ...item,
      dataShowFlg:
        // item.id.toLowerCase().includes(radioValue.toString().toLowerCase()) &&
        item.jigyouType.toLowerCase().includes(selectValue.toString().toLowerCase()),
    }
  })

  if (tableData.value.showFlg === '1') {
    localOneway.mo01334Oneway.items = tableDataFilter.value.map((item) => {
      return {
        ...item,
        jigyouFlg: { modelValue: item.jigyouFlg === '1' ? true : false } as Mo00018Type,
        jigyouNo: item.jigyouNo,
        jigyouName: item.jigyouName,
        jigyouType: item.jigyouType,
        jigyouArea: item.jigyouArea,

        jigyouAreaNm: item.jigyouAreaNm,
        jigyouAreaSort: item.jigyouAreaSort,
      }
    })

    tableData.value.jigyouList.forEach((item) => {
      if (item.jigyouFlg === '1') {
        tableDataList.value.values.push(item.id)
      }
    })
  } else {
    localOneway.mo01334Oneway2.items = tableDataFilter.value.map((item) => {
      return {
        ...item,
        jigyouFlg: { modelValue: item.jigyouFlg === '1' ? true : false } as Mo00018Type,
        jigyouNo: item.jigyouNo,
        jigyouName: item.jigyouName,
        jigyouType: item.jigyouType,
        jigyouArea: item.jigyouArea,

        jigyouAreaNm: item.jigyouAreaNm,
        jigyouAreaSort: item.jigyouAreaSort,
      }
    })
    //tableDataList.value.values.push(localOneway.mo01334Oneway2.items[0].id)
    tableData.value.jigyouList.forEach((item) => {
      // if (item.id !== localOneway.mo01334Oneway2.items[0].id) {
      if (item.jigyouFlg === '1') {
        tableDataList.value.values.push(item.id)
      }
      // }
    })
  }

  selectedValuesInit.value = [...tableDataList.value.values]
  length.value = tableDataList.value.values.length
}
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr21814_2 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr26369 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or26369Logic.state.get(or26369.value.uniqueCpId)?.isOpen ?? false
})

// チェック対象のselを1に設定、それ以外は0
watch(
  () => tableDataList.value.values,
  (newValue) => {
    tableData.value.jigyouList.forEach((item) => {
      if (newValue.includes(item.id)) {
        item.sel = '1'
      } else {
        item.sel = '0'
      }
    })
  }
)

/**
 * AC009_「保存ボタン」押下
 * 「保存」ボタン押下
 */
async function save() {
  emitFlag.value = false
  if (selectedValuesInit.value.length !== tableDataList.value.values.length) {
    emitFlag.value = true
  } else {
    for (const item of tableDataList.value.values) {
      if (!selectedValuesInit.value.includes(item)) {
        emitFlag.value = true
        break
      }
    }
  }

  if (!emitFlag.value) {
    Or21814Logic.state.set({
      uniqueCpId: or21814_2.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogTitle: t('label.info'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.ok'),
        iconName: 'info',
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
        iconColor: 'rgb(var(--v-theme-blue-700))',
        iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
        dialogText: t('message.i-cmn-21800'),
      },
    })
  } else {
    saveDate.value = []
    // 更新データ作成
    tableData.value.jigyouList.forEach((item) => {
      // TODO
      saveDate.value.push({
        sel: item.sel,
        jigyouId: item.jigyouId,
        jigyouCd: item.jigyouCd,
        jigyouNo: item.jigyouNo,
        jigyouName: item.jigyouName,
        jigyouArea: item.jigyouArea,
        jigyouTypeId: item.jigyouTypeId,
        jigyouType: item.jigyouType,
        jigyouFlg: item.jigyouFlg,
        applyFlg: item.jigyouFlg,
        jigyoSort: item.jigyoSort,
        id: '',
        dataShowFlg: true,
        jigyouAreaNm: '',
        jigyouAreaSort: '',
      })
    })
    const inputData: RelatedOfficeMstInfoUpdateInEntity = {
      groupKbn: tableData.value.groupKbn,
      subJigyoId: localOneway.or27573.jigyoId,
      jigyouList: saveDate.value,
    }
    await ScreenRepository.update('relatedOfficeMstInfoUpdate', inputData)

    // 画面情報再取得
    await init()
    return
  }
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814_2.value.uniqueCpId),
  (newValue) => {
    if (newValue?.firstBtnClickFlg === true) {
      return
    }
  }
)

/**
 * 「印刷設定アイコンボタン」押下
 *
 */
function printClick() {
  // GUI01178 印刷設定画面をポップアップで起動する。
  localOneway.or26369Oneway = {
    //     職員ID:親画面.職員ID
    shokuId: localOneway.or27573.staffId,
    // 法人ID:親画面.法人ID
    // houjinId: localOneway.or27573.houjinId,
    // 施設ID:親画面.施設ID
    // shisetuId: localOneway.or27573.shisetuId,
    // 事業所ID:親画面.事業所ID
    // svJigyoId: localOneway.or27573.svJigyoId,
    // システムコード:親画面.システムコード
    // sysCd: localOneway.or27573.sysCd,
    // セクション名称:"ケアマネ関連事業所"
    sectionName: Or27573Const.DEFAULT.SECTION_NAME,
    // システム略称:親画面.システム略称
    // sysRyaku: localOneway.or27573.sysRyaku,
    // 選択帳票番号："1"(当該タブ画面)
    choIndex: Or27573Const.DEFAULT.CHO_INDEX,
    // 関連事業所の設定:表示フラグ
    // kanrenSetKbn
  } as Or26369OnewayType

  // GUI01178 印刷設定画面をポップアップで起動する。
  Or26369Logic.state.set({
    uniqueCpId: or26369.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #toolbarRight>
      <c-v-btn
        class="mr-2"
        @click="close"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.screen-close')"
        ></c-v-tooltip>
        <base-at-icon icon="close" />
      </c-v-btn>
    </template>
    <template #cardItem>
      <c-v-card class="assessmentCard">
        <c-v-card-text class="w-auto flex-0-0">
          <c-v-row no-gutters>
            <c-v-col
              cols="12"
              style="text-align: right"
            >
              <base-mo00009
                :disabled="hasPrint"
                :oneway-model-value="printBtnMo00009"
                @click="printClick()"
              >
                <c-v-tooltip
                  :text="t('tooltip.care-plan2-print-setting-btn')"
                  activator="parent"
                  location="bottom"
                ></c-v-tooltip>
              </base-mo00009>
            </c-v-col>
          </c-v-row>
          <div class="row-style">
            <c-v-row
              no-gutter
              class="head-content"
            >
              <c-v-col
                cols="2"
                class="d-flex align-center head-title"
              >
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338onewayServiceType"
                  class="titleLabel"
                ></base-mo01338>
              </c-v-col>
              <c-v-col
                cols="4"
                style="width: 60%; padding: 0"
              >
                <base-mo00039
                  v-model="radioLabelListInit"
                  :oneway-model-value="localOneway.mo00039Oneway"
                >
                  <base-at-radio
                    v-for="item in local.radioLabelList"
                    :key="item.value"
                    :name="'or27573-radio-' + item.value"
                    :radio-label="item.label"
                    :value="item.value"
                    class="radio-style"
                    @click="officeSelectRadioClick(parseInt(item.value))"
                  />
                </base-mo00039>
              </c-v-col>
              <c-v-col
                cols="6"
                style="display: flex; align-items: center; flex-direction: column"
              >
                <c-v-row
                  no-gutter
                  style="align-items: center"
                >
                  <div class="select-message-n">
                    <base-at-select
                      v-model="selectValue"
                      name="selectItem"
                      :disabled="buttonsState.select"
                      :items="selectOptionLabelList"
                      item-title="label"
                      item-value="label"
                      single-line
                      class="select-w"
                      style="width: 125px; max-width: 125px"
                    >
                      <template #item="{ props: itemProps, item }">
                        <c-v-tooltip
                          v-if="item.raw.tooltip"
                          location="left"
                          :text="item.raw.tooltip"
                        >
                          <template #activator="{ props: tooltipProps }">
                            <c-v-list-item v-bind="{ ...itemProps, ...tooltipProps }">
                            </c-v-list-item>
                          </template>
                        </c-v-tooltip>
                        <c-v-list-item
                          v-else
                          v-bind="itemProps"
                        >
                          <c-v-list-item-title>{{ item.label }}</c-v-list-item-title>
                        </c-v-list-item>
                      </template>
                    </base-at-select>
                  </div>
                  <c-v-divider
                    vertical
                    inset
                    style="margin-left: 10px"
                  />
                  <div class="is-display">
                    <base-mo00009
                      style="width: 20px"
                      :disabled="true"
                      :oneway-model-value="localOneway.mo00009oneway"
                      @click="officeSelectIconClick()"
                    />
                  </div>
                  <div style="width: 250px">
                    <base-mo00018
                      v-model="local.or27573.checkboxFlg"
                      class="checkbox"
                      :oneway-model-value="localOneway.mo00018OnewayServiceType"
                      @change="enableAll(local.or27573.checkboxFlg)"
                    />
                  </div>
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </div>
          <div
            v-if="tableData.showFlg === '1'"
            class="row-style2"
          >
            <c-v-row
              no-gutter
              class="head-content"
            >
              <c-v-col
                cols="2"
                class="d-flex align-center head-2-title"
              >
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338onewayOfficerCode"
                  class="titleLabel"
                ></base-mo01338>
              </c-v-col>
              <c-v-col
                cols="4"
                style="flex-basis: 26%"
              >
                <base-mo00045
                  v-model="selectedItem1"
                  style="margin-left: 2px; margin-top: 1px; width: 120px !important"
                  :oneway-model-value="mo00045Oneway"
                />
              </c-v-col>
              <c-v-col
                cols="2"
                class="d-flex align-center head-2-title"
              >
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338onewayOfficerName"
                  class="titleLabel"
                ></base-mo01338>
              </c-v-col>
              <c-v-col cols="4">
                <base-mo00045
                  v-model="selectedItem2"
                  style="margin-left: 2px; margin-top: 1px; width: 220px !important"
                  :oneway-model-value="mo00045Oneway2"
                />
              </c-v-col>
            </c-v-row>
          </div>
          <div
            v-if="tableData.showFlg === '0'"
            class="row-style3"
          >
            <c-v-row
              no-gutter
              class="head-content"
            >
              <c-v-col
                cols="2"
                class="d-flex align-center head-2-title"
              >
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338onewayOfficerCode"
                  class="titleLabel"
                ></base-mo01338>
              </c-v-col>
              <c-v-col
                cols="4"
                style="flex-basis: 26%"
              >
                <base-mo00045
                  v-model="selectedItem1"
                  style="margin-left: 2px; margin-top: 1px; width: 120px !important"
                  :oneway-model-value="mo00045Oneway"
                  @click="officeNoSelectClick(selectedItem1.value)"
                />
              </c-v-col>
              <c-v-col
                cols="2"
                class="d-flex align-center head-2-title"
              >
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338onewayOfficerName"
                  class="titleLabel"
                ></base-mo01338>
              </c-v-col>
              <c-v-col cols="4">
                <base-mo00045
                  v-model="selectedItem2"
                  style="width: 220px !important"
                  :oneway-model-value="mo00045Oneway2"
                />
              </c-v-col>
            </c-v-row>
          </div>
          <div
            v-if="tableData.showFlg === '1'"
            class="row-style3"
          >
            <c-v-row no-gutter>
              <c-v-col
                cols="2"
                class="d-flex align-center head-3-title"
              >
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338onewayOfficeArea"
                  class="titleLabel"
                ></base-mo01338>
              </c-v-col>
              <c-v-col
                cols="4"
                class="head-content"
                style="flex-basis: 24%; padding-left: 8px"
              >
                <div
                  class="select-message-n"
                  style="margin-top: 1px"
                >
                  <base-at-select
                    v-model="selectValue2"
                    name="selectItem2"
                    :items="selectOfficeAreaList"
                    item-title="label"
                    item-value="value"
                    single-line
                    class="select-w"
                  >
                    <template #item="{ props: itemProps, item }">
                      <c-v-tooltip
                        v-if="item.raw.tooltip"
                        location="left"
                        :text="item.raw.tooltip"
                      >
                        <template #activator="{ props: tooltipProps }">
                          <c-v-list-item v-bind="{ ...itemProps, ...tooltipProps }">
                          </c-v-list-item>
                        </template>
                      </c-v-tooltip>
                      <c-v-list-item
                        v-else
                        v-bind="itemProps"
                      >
                        <c-v-list-item-title>{{ item.label }}</c-v-list-item-title>
                      </c-v-list-item>
                    </template>
                  </base-at-select>
                </div>
              </c-v-col>
              <c-v-col
                cols="18"
                class="head-content"
              ></c-v-col>
              <div
                class="is-display"
                style="padding-top: 8px"
              >
                <base-mo00009
                  style="width: 20px"
                  :disabled="true"
                  :oneway-model-value="localOneway.mo00009oneway"
                  @click="officeSelectIconClick()"
                />
              </div>
            </c-v-row>
          </div>
        </c-v-card-text>
      </c-v-card>
      <div v-if="tableData.showFlg === '1'">
        <base-mo-01334
          v-model="tableDataList"
          :oneway-model-value="localOneway.mo01334Oneway"
          :row-props="getItemClass"
          class="table-wrapper list-wrapper"
          style="padding: 0 !important; margin: 0 !important"
          hide-default-footer
        >
        </base-mo-01334>
      </div>
      <div v-if="tableData.showFlg === '0'">
        <base-mo-01334
          v-model="tableDataList"
          :oneway-model-value="localOneway.mo01334Oneway2"
          hide-default-footer
          :row-props="getItemClass"
          hover
          class="table-wrapper list-wrapper"
          style="padding: 0 !important; margin: 0 !important"
        >
        </base-mo-01334>
      </div>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        class="footBtn"
      >
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          class="mr-2"
          @click="close"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.screen-close')"
          ></c-v-tooltip>
        </base-mo00611>
        <!-- 保存ボタン -->
        <base-mo00609
          v-bind="localOneway.mo00609Oneway"
          class="mr-2"
          :disabled="hasRegist"
          @click="save()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.display-row-save')"
          ></c-v-tooltip>
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- Or21814:有機体:エラーダイアログ -->
    <g-base-or21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    />
    <g-base-or21814
      v-if="showDialogOr21814_2"
      v-bind="or21814_2"
    />
  </base-mo00024>
  <g-custom-or-26369
    v-if="showDialogOr26369"
    v-bind="or26369"
    :oneway-model-value="localOneway.or26369Oneway"
    :unique-cp-id="or26369.uniqueCpId"
    :parent-cp-id="props.uniqueCpId"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table-list.scss';
.footBtn {
  gap: 8px;
}
:deep(.v-field__input) {
  min-height: 36px !important;
  height: 36px !important;
  max-height: 36px !important;
}
:deep(.v-checkbox .v-checkbox-btn) {
  min-height: 31px !important;
  height: 31px !important;
}
:deep(.table-wrapper thead .v-selection-control) {
  margin-left: -1px;
}
:deep(.table-wrapper) td,
:deep(.table-wrapper) .row-height {
  height: 31px !important;
  max-height: 31px !important;
  min-height: 31px !important;
}
.select-message-n {
  :deep(.v-input__details) {
    display: none;
  }
}
:deep(.v-card-item) {
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

:deep(.v-card-item) {
  padding: 8px 0 !important;
}

:deep(.v-card-text) {
  padding: 0;
}

.assessmentCard {
  box-shadow: none;
  padding-bottom: 8px;
}

// 文字が縦に表示
:has(> .customLabel) {
  writing-mode: vertical-lr !important;
  letter-spacing: 8px;
}

.titleLabel {
  background-color: transparent;
  border-top: none;
  text-align: center;
}
.table-wrapper:deep(thead) {
  border-collapse: collapse;
}

.list-wrapper .table-wrapper:deep(.v-col) {
  padding-left: 0px !important;
}

.table-wrapper:deep(th) {
  top: 1px !important;
  padding: 0 8px !important;
}
:deep(.index) {
  width: 195px !important;
}
:deep(.radio-group) {
  margin-top: 0px;
}

.head-content {
  border-top: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}
.head-title {
  margin-left: 12px !important;
  background-color: rgb(var(--v-theme-background));
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}
.head-2-title {
  margin-top: -1px !important;
  margin-left: 12px !important;
  background-color: rgb(var(--v-theme-background));
  border-top: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.head-3-title {
  margin-left: 12px !important;
  background-color: rgb(var(--v-theme-background));
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}
:deep(.mr-2) {
  margin-right: 0px !important;
}
:deep(.v-col-2) {
  flex: 0 0 16.6666666667%;
  max-width: 16.6666666667%;
  height: 40px;
}
:deep(.v-col-4) {
  padding: 0;
  padding-left: 4px;
  height: 40px;
}
.row-style {
  margin-top: 11px;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}
.row-style3 {
  margin-top: 11px;
  padding-bottom: 11px;
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.row-style2 {
  margin-top: 11px;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}
:deep(.v-row) {
  flex-wrap: nowrap;
}
.list-wrapper2 {
  padding-left: 0px !important;
}

:deep(.v-data-table__tr .v-data-table__tr--clickable) {
  padding-left: 0px !important;
}
:deep(.v-selection-control__input) {
  width: var(--v-selection-control-size);
  height: 32px;
  align-items: center;
  display: flex;
  flex: none;
  position: relative;
  border-radius: 50%;
  justify-content: center;
}

.is-display {
  display: none;
}
</style>
