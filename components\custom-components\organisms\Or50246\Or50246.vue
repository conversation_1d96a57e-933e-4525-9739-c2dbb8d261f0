<script setup lang="ts">
/**
 * Or50246: 内容選択アイコンボタン
 * GUI00983_週間表入力
 *
 * <AUTHOR> HOANG SY TOAN
 */
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { <PERSON>26273Logic } from '../Or26273/Or26273.logic'
import { Or26273Const } from '../Or26273/Or26273.constants'
import { Or50246Const } from './Or50246.constants'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or26273OnewayType, Or26273Type } from '~/types/cmn/business/components/Or26273Type'
import { useSetupChildProps } from '#imports'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  modelValue: {
    id: string
    naiyoCd: string
  }
  index: number
}
/**************************************************
 * 変数定義
 **************************************************/
const props = defineProps<Props>()
const emits = defineEmits(['update:modelValue'])

const localOneWay = reactive({
  mo00009OneWay: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00045ContentOneWay: {
    name: '',
    showItemLabel: false,
    readonly: true,
    customClass: new CustomClass({ outerClass: 'ma-2' }),
  } as Mo00045OnewayType,
})

const localMo00045 = ref<Mo00045Type>({ value: props.modelValue.id ?? '' })

const or26273_1 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or26273Const.CP_ID(1)]: or26273_1.value,
})

// ダイアログ表示フラグ
const showDialogOr26273 = computed(() => {
  // Or26273 cks_flg=1 のダイアログ開閉状態
  return Or26273Logic.state.get(or26273_1.value.uniqueCpId)?.isOpen ?? false
})

// or26273_1 onewayModelValue
const or26273OnewayModel: Or26273OnewayType = {
  dataKbn: '0',
}

const or26273Type = ref<Or26273Type>({
  item: {},
})

watch(
  () => or26273Type.value.item,
  (newValue) => {
    localMo00045.value.value = newValue.title ?? ''
    emits('update:modelValue', {
      id: newValue.id ?? '',
      naiyoCd: newValue.naiyoCd ?? '',
    })
  }
)

watch(
  () => props.modelValue,
  (newValue) => {
    localMo00045.value.value = newValue.id ?? ''
  }
)

const handleClickManage = () => {
  // 引継情報.計画書様式を設定する。
  or26273OnewayModel.dataKbn = Or50246Const.DEFAULT.dataKbn
  Or26273Logic.state.set({
    uniqueCpId: or26273_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>

<template>
  <c-v-row
    no-gutters
    class="border-sm"
  >
    <c-v-col
      cols="4"
      class="bg-grey-lighten-3 pa-2 d-flex align-center ga-2 border-e-sm"
    >
      <base-at-label
        :value="t('weekly-plan.label.contents')"
        font-weight="bolder"
        class="title"
      />
      <base-mo00009
        :oneway-model-value="localOneWay.mo00009OneWay"
        @click.stop="handleClickManage()"
      />
    </c-v-col>
    <c-v-col>
      <base-mo00045
        v-model="localMo00045"
        :oneway-model-value="localOneWay.mo00045ContentOneWay"
      />
    </c-v-col>
  </c-v-row>
  <g-custom-or-26273
    v-if="showDialogOr26273"
    v-bind="or26273_1"
    v-model="or26273Type"
    :oneway-model-value="or26273OnewayModel"
  />
</template>

<style scoped></style>
