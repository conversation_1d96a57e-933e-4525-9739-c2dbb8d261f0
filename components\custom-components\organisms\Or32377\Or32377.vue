<script setup lang="ts">
/**
 * Or32377: (新型養護老人ホームパッケージプラン) 会議録印刷設定モーダル
 * GUI01132_印刷設定
 *
 * @description
 *  (新型養護老人ホームパッケージプラン) 会議録印刷設定モーダル
 *
 * <AUTHOR> LE VAN CUONG
 */
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10269Const } from '../Or10269/Or10269.constants'
import { Or32377Const } from './Or32377.constants'
import type { Or32377TwoWayData } from './Or32377.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or32377OnewayType } from '~/types/cmn/business/components/Or32377Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import {
  useReportUtils,
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
} from '#imports'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  PrintSettingsScreenInitialInfoSelectGUI01132InEntity,
  PrintSettingsScreenInitialInfoSelectGUI01132OutEntity,
  ChoPrt,
  CpnTucSypKaigi1,
} from '~/repositories/cmn/entities/PrintSettingsScreenInitialInfoSelectGUI01132Entity'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130TableType } from '~/components/custom-components/organisms/OrX0130/OrX0130.type.ts'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { Or10016Logic } from '~/components/custom-components/organisms/Or10016/Or10016.logic'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import { Or10016Const } from '~/components/custom-components/organisms/Or10016/Or10016.constants'

import type {
  ImplementationMonitoringPrintSettingsSubjectSelectInEntity,
  ImplementationMonitoringPrintSettingsSubjectSelectOutEntity,
} from '~/repositories/cmn/entities/ImplementationMonitoringPrintSettingsInitUpdateEntity'

import type { PrintSettingsInfoUpdateGUI01132InEntity } from '~/repositories/cmn/entities/PrintSettingsInfoUpdateGUI01132Entity'
import type {
  MeetingMinutesInfoSelectGUI01132InEntity,
  MeetingMinutesInfoSelectGUI01132OutEntity,
} from '~/repositories/cmn/entities/MeetingMinutesInfoSelectGUI01132Entity'

import type {
  LedgerInitializeDataSelectGUI01132InEntity,
  LedgerInitializeDataSelectGUI01132OutEntity,
} from '~/repositories/cmn/entities/LedgerInitializeDataSelectGUI01132Entity'

import type {
  FilterForUserIdSelectGUI01132InEntity,
  FilterForUserIdSelectGUI01132OutEntity,
} from '~/repositories/cmn/entities/FilterForUserIdSelectGUI01132Entity'
import { reportOutputType } from '~/utils/useReportUtils'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  PrintOption,
  PrintSet,
  PrintSubjectHistory,
  IndepSupportNursCareMeeting6ReportInEntity,
} from '~/repositories/cmn/entities/IndepSupportNursCareMeeting6ReportEntity'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'

/**
 * I18n
 */
const { t } = useI18n()

/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

const { reportOutput } = useReportUtils()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or32377OnewayType
  uniqueCpId: string
}

/**
 * 引継情報を取得する
 */
const props = defineProps<Props>()

/**
 * orX0130
 */
const orX0130 = ref({ uniqueCpId: '' })
/**
 * or21813
 */
const or21813 = ref({ uniqueCpId: '' })

/**
 * or21815
 */
const or21815 = ref({ uniqueCpId: '' })

/**
 * or10016
 */
const or10016 = ref({ uniqueCpId: '' })

/**
 * or21814
 */
const or21814 = ref({ uniqueCpId: '' })

/**
 * orx0145
 */
const orx0145 = ref({ uniqueCpId: '' })

/**
 * orX0128
 */
const orX0128 = ref({ uniqueCpId: '' })

/**
 * orX0117
 */
const orX0117 = ref({ uniqueCpId: '' })

/**
 * プロファイル
 */
const choPro = ref('')

/**
 * 帳票イニシャライズデータを取得する
 */
const reportInitData = ref({
  // 氏名伏字印刷
  prtName: '',
  // 文書番号印刷
  prtBng: '',
  // 個人情報印刷
  prtKojin: '',
})

/**
 * 印刷設定帳票出力
 */
const orX0117Oneway = ref<OrX0117OnewayType>({
  type: '0',
  historyList: [],
})

/**
 * 利用者一覧明細選択
 */
const orX0130Type = ref<string>('')

/**
 * orX0130Oneway
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width:210px',
  showKanaSelectionCount: true,
})

const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: '1',
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:365px',
  headers: [
    { title: t('label.held-date'), key: 'createYmd', minWidth: '130px', sortable: false },
    { title: t('label.author'), key: 'shokuinKnj', minWidth: '130px', sortable: false },
    { title: t('label.caseNo'), key: 'caseNo', minWidth: '130px', sortable: false },
    { title: t('label.revision'), key: 'kaiteiFlg', minWidth: '70px', sortable: false },
  ] as OrX0128Headers[],
  items: [],
})
/**
 * 利用者列幅
 */
const userCols = ref<number>(4)
/**
 * 片方向バインド用の内部変数
 */
const localOneway = reactive({
  Or32377: {
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '1300px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or32377',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or32377ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00610SealColumnBtnOneWay: {
    btnLabel: t('label.seal-column'),
  } as Mo00610OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
  mo01338OneWayTitle: {
    value: t('label.title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: false,
  } as Mo00020OnewayType,
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '100%',
  } as Mo00045OnewayType,
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '50',
    maxLength: '2',
  } as Mo00045OnewayType,
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintTheYear: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-the-year'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintAuthor: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-author'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintDescription: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-description'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayBaseDate: {
    value: t('label.base-date'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '120px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  or04007OnewayType: {
    kikanFlg: props.onewayModelValue.kikanFlg,
    historySelectType: '',
  },
  mo01337Oneway: {
    customClass: {
      outerStyle: 'background-color: rgba(0, 0, 0, 0);',
      itemClass: 'itemClass',
    } as CustomClass,
  } as Mo01337OnewayType,
  mo00018CheckAllOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: '',
  } as Mo00018OnewayType,
  mo00018CheckOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: '',
  } as Mo00018OnewayType,
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
  } as OrX0145OnewayType,
})

/**
 * mo00024
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or32377Const.DEFAULT.IS_OPEN,
})

/**
 * 双方向バインド用の内部変数
 */
const local = reactive({
  textInput: {
    value: '',
  } as Mo00045Type,
  titleInput: {
    value: '',
  } as Mo00045Type,
  mo00039Type: '',
  mo00020Type: {
    value: '2024/12/21',
  } as Mo00020Type,
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  mo00039TypeUserSelectType: '',
  mo00039TypeHistorySelectType: '',
  mo00020TypeKijunbi: {
    value: '2024/11/11',
  } as Mo00020Type,
  or04007: {
    cpnTucSypKaigi1List: [] as CpnTucSypKaigi1[],
  },
  /**
   * 帳票ID
   */
  reportId: Or32377Const.DEFAULT.REPORT_ID,
  userSelected: [] as OrX0130TableType[],
  orX0128DetList: [] as OrX0128Items[],
})

/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'prtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 595,
})

/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)
/**
 * or10016Data
 */
const or10016Data: Or10016OnewayType = {
  /**
   * 法人ID
   */
  houjinId: systemCommonsStore.getHoujinId!,
  /**
   * 施設ID
   */
  shisetsuId: systemCommonsStore.getShisetuId!,
  /**
   * 職員ID
   */
  shokuinId: '1',
  // shokuinId: systemCommonsStore.getStaffId!,
  /**
   * システムコード
   */
  systemCode: systemCommonsStore.getSystemCode!,
  /**
   * 事業所ID
   */
  jigyoshoId: localOneway.Or32377.jigyoshoId,
  /**
   * ログイン番号
   */
  loginNumber: localOneway.Or32377.loginNumber,
  /**
   * ログインユーザタイプ
   */
  loginUserType: localOneway.Or32377.loginUserType,
  /**
   * 電子カルテ連携フラグ
   */
  emrLinkFlag: localOneway.Or32377.emrLinkFlag,
  /**
   * 帳票セクション番号
   */
  reportSectionNumber: '3GKU0P092P001',

  /**
   * 引続情報.アセスメント
   */
  assessment: '4',
  /**
   * 引続情報.会議禄フラグ
   */
  conferenceFlag: true,
}
/**************************************************
 * Pinia
 **************************************************/
/**
 * Piniaストアの設定
 * - ダイアログの開閉状態を管理
 * - uniqueCpIdを使用して一意の状態を識別
 */
const { setState } = useScreenOneWayBind({
  cpId: Or32377Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * ダイアログの開いている状態を更新します。
     *
     * @param value - ダイアログの新しいオープン状態の値。
     */
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or32377Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or10016Const.CP_ID(0)]: or10016.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
})

/**
 * スクリーン双方向バインドを使用する
 */
const { refValue } = useScreenTwoWayBind<Or32377TwoWayData>({
  cpId: Or32377Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

onMounted(async () => {
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  await getPrintSettingList()
  await initCodes()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 初期値
  local.mo00039TypeUserSelectType = Or32377Const.USER_SELECT_SINGLE
  local.mo00039TypeHistorySelectType = Or32377Const.HISTORY_SELECT_SINGLE
}

/**
 * ダイアログ表示フラグ
 */
const showDialogOr10016 = computed(() => {
  // Or10016のダイアログ開閉状態
  return Or10016Logic.state.get(or10016.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: PrintSettingsScreenInitialInfoSelectGUI01132InEntity = {
    // // sysCd: systemCommonsStore.getSystemCode!,
    // sysCd: '1',
    // sysRyaku: '3GK',
    // // sysRyaku: systemCommonsStore.getSystemAbbreviation!,
    // kinounameKnj: 'PRT',
    // svJigyoId: systemCommonsStore.getSvJigyoId!,
    // shokuId: '1',
    // // shokuId: systemCommonsStore.getStaffId!,
    // sectionName: localOneway.Or32377.sectionName,
    // choIndex: localOneway.Or32377.choIndex,
    // kojinhogoFlg: '0',
    // tantoId: '1',
    // // tantoId: systemCommonsStore.getManagerId! !== '0' ? systemCommonsStore.getManagerId! : '0',
    // startYmd: '09/09/2025',
    // // startYmd:  systemCommonsStore.getProcessDate!,
    // endYmd: '',
    // userId: localOneway.Or32377.userId,
    // kikanFlg: localOneway.Or32377.kikanFlg,
    // houjinId: systemCommonsStore.getHoujinId!,
    // shisetuId: systemCommonsStore.getShisetuId!,
    // sectionAddNo: '000001',

    sysCd: '71101',
    sysRyaku: '3GK',
    kinounameKnj: '1008',
    houjinId: '1',
    shisetuId: '1',
    svJigyoId: '1',
    tantoId: '26',
    startYmd: '2011/11/01',
    endYmd: '2011/11/01',
    shokuId: '1',
    sectionName: '[3GK]利用料請求書',
    choIndex: '2',
    kojinhogoFlg: '1',
    sectionAddNo: '1',
    userId: '26',
    kikanFlg: '1',
  }

  // バックエンドAPIから初期情報取得
  const ret: PrintSettingsScreenInitialInfoSelectGUI01132OutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoSelectGUI01132',
    inputData
  )
  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of ret.data.choPrtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }
  mo01334OnewayReport.value.items = mo01334OnewayList

  if (localOneway.Or32377.sectionName) {
    mo01334TypeReport.value.value = localOneway.Or32377.sectionName
  } else {
    mo01334TypeReport.value.value = ret.data.choPrtList[0].choPro
  }
  refValue.value = { choPrtList: ret.data.choPrtList }

  useScreenStore().setCpTwoWay({
    cpId: Or32377Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })

  local.or04007.cpnTucSypKaigi1List = ret.data.cpnTucSypKaigi1List

  reportInitData.value = ret.data.iniDataObject as typeof reportInitData.value
}

/**
 * 履歴情報を取得する
 *
 * @param userId -- 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  const inputData: MeetingMinutesInfoSelectGUI01132InEntity = {
    kikanFlg: localOneway.Or32377.kikanFlg,
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    userId: userId,
  }

  // バックエンドAPIから初期情報取得
  const ret: MeetingMinutesInfoSelectGUI01132OutEntity = await ScreenRepository.select(
    'meetingMinutesInfoSelectGUI01132',
    inputData
  )

  const historyList = ret.data.cpnTucSypKaigi1List
  local.or04007.cpnTucSypKaigi1List = historyList
}

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataSelectGUI01132InEntity = {
    // sysCd: systemCommonsStore.getSystemCode!,
    sysCd: '1',
    kinounameKnj: 'PRT',
    shokuId: '1',
    // shokuId: systemCommonsStore.getStaffId!,
    sectionName: choPro.value,
    kojinhogoFlg: '0',
    sectionAddNo: '0',
  }

  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataSelectGUI01132OutEntity = await ScreenRepository.select(
    'ledgerInitializeDataSelectGUI01132',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject as typeof reportInitData.value
}

/**
 * ユーザーリストを取得する
 *
 * @param tantoId -担当者ID
 */
async function getUserList(tantoId: string) {
  const inputData: FilterForUserIdSelectGUI01132InEntity = {
    tantoId: tantoId,
    startYmd: '09/09/2025',
    // startYmd: systemCommonsStore.getProcessDate!,
    endYmd: '2013/12/31',
  }

  // バックエンドAPIから初期情報取得
  const ret: FilterForUserIdSelectGUI01132OutEntity = await ScreenRepository.select(
    'filterForUserIdSelectGUI01132',
    inputData
  )

  return ret
}
/**
 * 印刷設定情報を保存する
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
async function savePrintSettingInfo(choPrtList: ChoPrt[]) {
  const inputData: PrintSettingsInfoUpdateGUI01132InEntity = {
    // sysCd: systemCommonsStore.getSystemCode!,
    sysCd: '1',
    sysRyaku: '1',
    // sysRyaku: systemCommonsStore.getSystemAbbreviation!,
    kinounameKnj: 'PRT',
    houjinId: systemCommonsStore.getHoujinId!,
    shisetuId: systemCommonsStore.getShisetuId!,
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    shokuId: '1',
    // shokuId: systemCommonsStore.getStaffId!,
    choPro: choPro.value,
    kojinhogoFlg: '0',
    sectionAddNo: '0',
    iniDataObject: { ...reportInitData.value },
    choPrtList: choPrtList,
  }
  // バックエンドAPIから初期情報取得
  await ScreenRepository.update('printSettingsInfoUpdateGUI01132', inputData)
}
/**
 * 印刷ダイアログ画面を開
 *
 * @param userId - 利用者ID
 *
 * @param userName - 利用者名
 *
 * @param kaigi1Id - 会議録ID
 *
 * @param shokuName - 作成者名
 *
 * @param assDateYmd - 調査日
 */
const reportOutputPdf = async (
  userId: string,
  userName: string,
  kaigi1Id?: string,
  shokuName?: string,
  assDateYmd?: string
) => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')
    const reportData: IndepSupportNursCareMeeting6ReportInEntity = {
      svJigyoId: localOneway.Or32377.svJigyoId,
      houjinId: systemCommonsStore.getHoujinId!,
      shisetuId: systemCommonsStore.getShisetuId!,
      svJigyoKnj: '',
      syscd: '26', //systemCommonsStore.getSystemCode!,
      kaigi1Id: kaigi1Id,
      asYmd: '',
      sheetNo: or10016Data.reportSectionNumber,
      printSet: {} as PrintSet,
      printOption: {} as PrintOption,
      printSubjectHistoryList: [] as PrintSubjectHistory[],
    }
    reportData.printSet = {
      shiTeiKubun: local.mo00039Type,
      shiTeiDate: local.mo00020Type.value,
    }
    reportData.printOption = {
      emptyFlg: local.mo00018TypePrintTheForm.modelValue ? 'true' : 'false',
      kinyuAssType: '',
      keishoFlg: local.mo00018TypeChangeTitle.modelValue ? 'true' : 'false',
      keishoKnj: local.textInput.value,
      colorFlg: '',
      shokuName: shokuName,
    } as PrintOption
    reportData.printSubjectHistoryList = []
    reportData.printSubjectHistoryList.push({
      userId: userId,
      userName: userName,
      sc1Id: systemCommonsStore.getPeriodInfo?.kikanId,
      startYmd: '',
      endYmd: '',
      raiId: '',
      assType: '',
      assDateYmd: assDateYmd,
      assShokuId: '',
      result: '',
    } as PrintSubjectHistory)
    // 帳票出力
    await reportOutput(local.reportId, reportData, reportOutputType.DOWNLOAD)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}
/**
 * エラーダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21813MsgOneBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = Or32377Const.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = Or32377Const.DIALOG_RESULT_YES
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 印刷設定情報リストを作成(利用者選択が「複数」、利用者情報リストにデータを選択する場合)
 */
const PrintSettingsSubjectSelect = async () => {
  //印刷設定情報リストを作成する
  const inputData: ImplementationMonitoringPrintSettingsSubjectSelectInEntity = {
    svJigyoId: localOneway.Or32377.svJigyoId,
    processYm: local.mo00020TypeKijunbi.value ?? Or32377Const.EMPTY,
    userList: local.userSelected.map((item) => {
      return {
        userId: item.userId,
        userName: item.name1Knj,
      }
    }),
  } as ImplementationMonitoringPrintSettingsSubjectSelectInEntity
  const resp: ImplementationMonitoringPrintSettingsSubjectSelectOutEntity =
    await ScreenRepository.select('implementationMonitoringPrintSettingsSubjectSelect', inputData)

  // APIレスポンスからOrX0117のデータを準備する
  const historyList: OrX0117History[] = []

  for (const item of resp.data.printSubjectHistoryList) {
    const reportData: IndepSupportNursCareMeeting6ReportInEntity = {
      svJigyoId: localOneway.Or32377.svJigyoId,
      houjinId: systemCommonsStore.getHoujinId!,
      shisetuId: systemCommonsStore.getShisetuId!,
      svJigyoKnj: '',
      syscd: '26',
      kaigi1Id: item.kjisshi1Id,
      asYmd: '',
      sheetNo: or10016Data.reportSectionNumber,
      printSet: {
        shiTeiKubun: local.mo00039Type,
        shiTeiDate: local.mo00020Type.value,
      } as PrintSet,
      printOption: {
        emptyFlg: local.mo00018TypePrintTheForm.modelValue ? 'true' : 'false',
        kinyuAssType: '',
        keishoFlg: local.mo00018TypeChangeTitle.modelValue ? 'true' : 'false',
        keishoKnj: local.textInput.value,
        colorFlg: '',
        shokuName: item.shokuId,
      } as PrintOption,
      printSubjectHistoryList: [
        {
          userId: item.userId,
          userName: item.userName,
          sc1Id: systemCommonsStore.getPeriodInfo?.kikanId,
          startYmd: '',
          endYmd: '',
          raiId: '',
          assType: '',
          assDateYmd: item.planCreateYmd,
          assShokuId: item.shokuId,
          result: item.result,
        },
      ] as PrintSubjectHistory[],
    }

    historyList.push({
      reportId: local.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName: item.userName,
      historyDate: item.planCreateYmd,
      result: item.result,
    } as OrX0117History)
  }

  orX0117Oneway.value.historyList = historyList
  orX0117Oneway.value.type = '1' // 利用者複数

  // GUI00082_印刷が終了しました画面をポップアップ表示する
  // OrX0117のダイアログ開閉状態を更新する
  OrX0117Logic.state.set({
    uniqueCpId: orX0117.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/** 印刷ダイアログ表示フラグ */
const showDialogOrX0117 = computed(() => {
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 警告ダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = Or32377Const.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = Or32377Const.DIALOG_RESULT_YES
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
const openConfirmDialog = async (paramDialogText: string): Promise<'yes' | 'no'> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: 'yes' | 'no' = Or32377Const.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = Or32377Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or32377Const.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  if (local.titleInput.value) return
  const dialogResult = await showOr21815MsgOneBtn()
  switch (dialogResult) {
    case Or32377Const.DIALOG_RESULT_YES: {
      let label = ''
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (mo01334TypeReport.value.value === item.id && item.mo01337OnewayReport) {
            const data = item.mo01337OnewayReport as Mo01337OnewayType
            label = data.value
          }
        }
      }
      local.titleInput.value = label
      break
    }
  }
  return
}

/**
 * 「印鑑欄」ボタン押下
 */
function onClickSealColumn() {
  Or10016Logic.state.set({
    uniqueCpId: or10016.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 変更データを取得する
 *
 * @returns 出力帳票印刷情報リスト
 */
async function getDataTable() {
  const choPrtList = mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => {
    if (id === mo01334TypeReport.value.value) {
      return {
        ...rest,
        prtTitle: local.titleInput.value,
        prndate: local.mo00039Type,
        param03: local.mo00018TypeChangeTitle.modelValue ? '0' : '1',
        param04: local.textInput.value,
      }
    }
    return {
      ...rest,
    }
  }) as ChoPrt[]

  refValue.value = { choPrtList: choPrtList }

  await nextTick()

  return choPrtList
}

/**
 * 閉じる
 */
async function close() {
  await checkTitleInput()

  const choPrtList = await getDataTable()

  await savePrintSettingInfo(choPrtList)

  setState({ isOpen: false })
}

/**
 * PDFダウンロード
 */
async function download() {
  await checkTitleInput()
  if (!choPro.value) await showOr21813MsgOneBtn()
  if (local.mo00039TypeUserSelectType === Or32377Const.USER_SELECT_SINGLE) {
    // ユーザー選択モードが単一の場合
    if (
      local.mo00039TypeHistorySelectType === Or32377Const.HISTORY_SELECT_SINGLE &&
      local.orX0128DetList.length === 0
    ) {
      const dialogResult = await openConfirmDialog(t('message.i-cmn-11455'))
      if (dialogResult === Or32377Const.DIALOG_RESULT_YES) {
        return
      }
    }
    if (
      local.mo00039TypeHistorySelectType === Or32377Const.HISTORY_SELECT_MULTIPLE &&
      local.orX0128DetList.length === 0
    ) {
      const dialogResult = await openConfirmDialog(t('message.i-cmn-11455'))
      if (dialogResult === Or32377Const.DIALOG_RESULT_YES) {
        return
      }
    }
  }
  const choPrtList = await getDataTable()

  await savePrintSettingInfo(choPrtList)
  // ユーザー選択モードが単一の場合
  if (local.mo00039TypeUserSelectType === Or32377Const.USER_SELECT_SINGLE) {
    // 履歴選択モードが単一の場合
    if (local.mo00039TypeHistorySelectType === Or32377Const.HISTORY_SELECT_SINGLE) {
      const user = local.userSelected[0]
      const data = local.orX0128DetList[0]
      await reportOutputPdf(
        user.userId,
        user.name1Knj,
        typeof data.kaigi1Id === 'string' ? data.kaigi1Id : undefined,
        typeof data.shokuinKnj === 'string' ? data.shokuinKnj : undefined,
        typeof data.createYmd === 'string' ? data.createYmd : undefined
      )

      // 複数履歴選択モードの場合
    } else if (local.mo00039TypeHistorySelectType === Or32377Const.HISTORY_SELECT_MULTIPLE) {
      // データを準備し、GUI00082 を呼び出して複数のレポートを印刷します
      // OrX0117 用のデータを準備します
      const user = local.userSelected[0]
      const historyList: OrX0117History[] = []

      for (const item of local.orX0128DetList) {
        const reportData: IndepSupportNursCareMeeting6ReportInEntity = {
          svJigyoId: localOneway.Or32377.svJigyoId,
          houjinId: systemCommonsStore.getHoujinId!,
          shisetuId: systemCommonsStore.getShisetuId!,
          svJigyoKnj: '',
          syscd: '26',
          kaigi1Id: typeof item.kaigi1Id === 'string' ? item.kaigi1Id : undefined,
          asYmd: '',
          sheetNo: or10016Data.reportSectionNumber,
          printSet: {
            shiTeiKubun: local.mo00039Type,
            shiTeiDate: local.mo00020Type.value,
          } as PrintSet,
          printOption: {
            emptyFlg: local.mo00018TypePrintTheForm.modelValue ? 'true' : 'false',
            kinyuAssType: '',
            keishoFlg: local.mo00018TypeChangeTitle.modelValue ? 'true' : 'false',
            keishoKnj: local.textInput.value,
            colorFlg: '',
            shokuName: typeof item.shokuinKnj === 'string' ? item.shokuinKnj : undefined,
          } as PrintOption,
          printSubjectHistoryList: [
            {
              userId: user.userId,
              userName: user.name1Knj,
              sc1Id: systemCommonsStore.getPeriodInfo?.kikanId,
              startYmd: '',
              endYmd: '',
              raiId: '',
              assType: '',
              assDateYmd: typeof item.createYmd === 'string' ? item.createYmd : undefined,
              assShokuId: '',
              result: '',
            },
          ] as PrintSubjectHistory[],
        }

        historyList.push({
          reportId: local.reportId,
          outputType: reportOutputType.DOWNLOAD,
          reportData: reportData,
          userName: user.name1Knj, // 氏名
          historyDate: typeof item.createYmd === 'string' ? item.createYmd : '',
          result: '',
        } as OrX0117History)
      }

      orX0117Oneway.value.historyList = historyList
      orX0117Oneway.value.type = '0' // 履歴複数

      // GUI00082_印刷が終了しました画面をポップアップ表示する
      // OrX0117のダイアログ開閉状態を更新する
      OrX0117Logic.state.set({
        uniqueCpId: orX0117.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
    }
  } else if (local.mo00039TypeUserSelectType === Or32377Const.USER_SELECT_MULTIPLE) {
    await PrintSettingsSubjectSelect()
  }
  // setState({ isOpen: false })
}

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334TypeReport.value.value,
  async (newValue, oldValue) => {
    if (oldValue) {
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.帳票タイトルが空白以外の場合
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            if (local.titleInput.value) item.prtTitle = local.titleInput.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prndate = local.mo00039Type
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ03 = 画面.敬称を変更する
            item.param03 = local.mo00018TypeChangeTitle.modelValue ? '0' : '1'
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ04 = 画面.敬称
            item.param04 = local.textInput.value
          }
        }
      }
    }

    for (const item of mo01334OnewayReport.value.items) {
      if (item) {
        if (newValue === item.id) {
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prndate as string
          // 画面.敬称を変更する = 画面.出力帳票一覧明細に選択される行.パラメータ03
          local.mo00018TypeChangeTitle.modelValue = item?.param03 === '0' ? true : false
          // 画面.敬称 = 画面.出力帳票一覧明細に選択される行.パラメータ04
          local.textInput.value = item?.param04 as string
          // 画面.記入用シートを印刷する = 0(チェックオフ)
          local.mo00018TypePrintTheForm.modelValue = true
          choPro.value = item?.choPro as string
          if (systemCommonsStore.getProcessDate! !== '') {
            if (systemCommonsStore.getManagerId! !== '0') {
              localOneway.orX0145Oneway.disabled = true
            } else {
              localOneway.orX0145Oneway.disabled = false
            }
          }
          await getReportInfoDataList()
        }
      }
    }
  }
)

/**
 * 「利用者選択方」ラジオボタン選択
 */
watch(
  () => local.mo00039TypeUserSelectType,
  () => {
    local.userSelected = []
    if (local.mo00039TypeUserSelectType === Or32377Const.USER_SELECT_SINGLE) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width:210px'
      userCols.value = 5
      if (local.mo00039TypeHistorySelectType === Or32377Const.HISTORY_SELECT_SINGLE) {
        localOneway.mo00018OneWayPrintTheForm.disabled = false
      } else {
        local.mo00018TypePrintTheForm.modelValue = false
        localOneway.mo00018OneWayPrintTheForm.disabled = true
      }
    } else {
      localOneway.mo00018OneWayPrintTheForm.disabled = true
      local.mo00018TypePrintTheForm.modelValue = false
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'
      userCols.value = 12
    }
  }
)

/**
 * 「履歴選択」ラジオボタン選択
 */
watch(
  () => local.mo00039TypeHistorySelectType,
  () => {
    localOneway.or04007OnewayType.historySelectType = local.mo00039TypeHistorySelectType
    if (localOneway.Or32377.historyId) {
      orX0128OnewayModel.initSelectId = (
        orX0128OnewayModel.items.findIndex(
          (item) => item.kaigi1Id === localOneway.Or32377.historyId
        ) + 1
      ).toString()
    }
    if (local.mo00039TypeHistorySelectType === Or32377Const.HISTORY_SELECT_SINGLE) {
      localOneway.mo00018OneWayPrintTheForm.disabled = false
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.TANI
    } else {
      local.mo00018TypePrintTheForm.modelValue = false
      localOneway.mo00018OneWayPrintTheForm.disabled = true
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
    }
  }
)

///**
// * ラジオボタンの選択状態を追跡する
// */
watch(
  () => local.mo00039Type,
  async () => {
    await checkTitleInput()
  }
)

/**
 *  テーブルの変更を追跡する
 */
watch(
  () => mo01334OnewayReport.value.items,
  () => {
    const choPrtList = [
      ...mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => ({
        ...rest,
      })),
    ] as ChoPrt[]
    refValue.value = { choPrtList: choPrtList }
  },
  { deep: true }
)

watch(
  () => local.or04007.cpnTucSypKaigi1List,
  () => {
    if (localOneway.or04007OnewayType.kikanFlg === '1') {
      const tempList: string[] = [] as string[]
      let list: OrX0128Items[] = []
      for (const item of local.or04007.cpnTucSypKaigi1List) {
        if (item) {
          const planPeriod =
            t('label.plan-period') +
            Or32377Const.SPLIT_COLON +
            item.startYmd +
            Or32377Const.SPLIT_TILDE +
            item.endYmd
          if (!tempList.includes(planPeriod)) {
            const historyList: OrX0128Items[] = []
            for (const data of local.or04007.cpnTucSypKaigi1List) {
              const dataPlanPeriod =
                t('label.plan-period') +
                Or32377Const.SPLIT_COLON +
                data.startYmd +
                Or32377Const.SPLIT_TILDE +
                data.endYmd
              if (planPeriod === dataPlanPeriod) {
                historyList.push({
                  sel: item.sel,
                  userid: item.userId,
                  shokuinKnj: item.shokuinKnj,
                  createYmd: item.createYmd,
                  caseNo: item.caseNo,
                  kaiteiFlg: item.kaiteiFlg,
                  id: item.kaigi1Id,
                  kaigi1Id: item.kaigi1Id,
                  sc1Id: item.sc1Id,
                  startYmd: item.startYmd,
                  endYmd: item.endYmd,
                } as OrX0128Items)
              }
            }
            if (historyList.length > 0) {
              list.push({
                sc1Id: item.sc1Id,
                startYmd: item.startYmd,
                endYmd: item.endYmd,
                isPeriodManagementMergedRow: true,
                planPeriod:
                  t('label.plan-period') +
                  Or32377Const.SPLIT_COLON +
                  item.startYmd +
                  Or32377Const.SPLIT_TILDE +
                  item.endYmd,
                id: Or10269Const.DEFAULT.STR.EMPTY,
              } as OrX0128Items)
              list = list.concat(historyList)
              tempList.push(planPeriod)
            }
          }
        }
      }
      list.forEach((item, index) => {
        item.id = String(++index)
      })
      orX0128OnewayModel.items = list
    } else {
      const list: OrX0128Items[] = []
      for (const item of local.or04007.cpnTucSypKaigi1List) {
        if (item) {
          list.push({
            sel: item.sel,
            userid: item.userId,
            shokuinKnj: item.shokuinKnj,
            createYmd: item.createYmd,
            caseNo: item.caseNo,
            kaiteiFlg: item.kaiteiFlg,
            id: item.kaigi1Id,
            kaigi1Id: item.kaigi1Id,
            sc1Id: item.sc1Id,
            startYmd: item.startYmd,
            endYmd: item.endYmd,
          } as OrX0128Items)
        }
      }
      orX0128OnewayModel.items = list
    }
    if (localOneway.Or32377.historyId) {
      orX0128OnewayModel.initSelectId = (
        orX0128OnewayModel.items.findIndex(
          (item) => item.kaigi1Id === localOneway.Or32377.historyId
        ) + 1
      ).toString()
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      local.userSelected = newValue.userList
      if (local.mo00039TypeUserSelectType === Or32377Const.USER_SELECT_SINGLE) {
        await getHistoricalInfoList(newValue.userList[0].userId)
      } else {
        local.userSelected = newValue.userList
      }
    }
  }
)

watch(
  () => orX0145Type.value.value,
  (newValue) => {
    // TODO 担当ケアマネ有機体が未作成 「担当ケアマネプルダウン」選択変更がある場合
    console.log(newValue)
    void getUserList('1')
  }
)
/**
 * 「履歴選択」の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      if (newValue.historyDetClickFlg) {
        local.orX0128DetList = newValue.orX0128DetList
      } else {
        local.orX0128DetList = []
      }
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or32377_screen"
      >
        <c-v-col
          cols="12"
          sm="2"
          class="pa-0 pt-2 pl-2 or32377_border_right"
        >
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <template #[`item.prtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="4"
          class="mt-4 pa-0 pt-2 or32377_border_right content_center"
        >
          <div class="ml-2">
            <base-mo00610
              :oneway-model-value="localOneway.mo00610SealColumnBtnOneWay"
              class="mx-2 mb-2"
              @click.stop="onClickSealColumn()"
            />
            <c-v-divider class="my-0 mt-2 ml-1 mr-1"></c-v-divider>
            <c-v-row no-gutter>
              <c-v-col
                cols="auto"
                style="align-content: center"
              >
                <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
              </c-v-col>
              <c-v-col>
                <base-mo00045
                  v-model="local.titleInput"
                  :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                  @keyup.enter="checkTitleInput"
                />
              </c-v-col>
            </c-v-row>
          </div>

          <c-v-divider class="my-0 mt-2 ml-1 mr-1"></c-v-divider>
          <c-v-row
            no-gutter
            class="mr-0"
          >
            <c-v-col cols="7">
              <base-mo00039
                v-model="local.mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col cols="5">
              <base-mo00020
                v-if="local.mo00039Type === '2'"
                ref="printDateRef"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
                @mousedown="checkTitleInput"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0 mt-2 ml-1 mr-1 mb-4"></c-v-divider>

          <c-v-row
            no-gutter
            class="printerOption customCol or32377_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or32377_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 d-flex"
            >
              <base-mo00018
                v-model="local.mo00018TypeChangeTitle"
                :oneway-model-value="localOneway.mo00018OneWayChangeTitle"
              >
              </base-mo00018>
              <base-mo00045
                v-model="local.textInput"
                :oneway-model-value="localOneway.mo00045OnewayTextInput"
                :disabled="!local.mo00018TypeChangeTitle.modelValue"
              />
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="local.mo00018TypePrintTheForm"
                :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-0"
        >
          <c-v-row
            class="or32377_row"
            no-gutter
            style="align-items: center"
          >
            <c-v-col cols="auto">
              <c-v-row no-gutter>
                <c-v-col
                  cols="auto"
                  style="align-content: center"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                  <base-mo00039
                    v-model="local.mo00039TypeUserSelectType"
                    :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === Or32377Const.USER_SELECT_SINGLE"
              style="width: 184px"
            >
              <c-v-row no-gutter>
                <c-v-col
                  cols="auto"
                  style="align-content: center"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                  <base-mo00039
                    v-model="local.mo00039TypeHistorySelectType"
                    :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === Or32377Const.USER_SELECT_MULTIPLE"
              style="width: 184px"
            >
              <c-v-row>
                <c-v-col
                  cols="auto"
                  style="align-content: center"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayBaseDate"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                  <base-mo00020
                    v-model="local.mo00020TypeKijunbi"
                    :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="systemCommonsStore.getProcessDate! !== ''"
              class="pa-0 flex-center"
              cols="auto"
            >
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or32377_row"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              style="overflow-x: auto"
              class="mb-2"
            >
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                v-bind="orX0130"
                v-model="orX0130Type"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="local.mo00039TypeUserSelectType === Or32377Const.USER_SELECT_SINGLE"
              cols="7"
            >
              <g-custom-or-x-0128
                v-if="orX0128OnewayModel.singleFlg"
                v-bind="orX0128"
                :oneway-model-value="orX0128OnewayModel"
                style="width: 375px"
              ></g-custom-or-x-0128>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="download()"
        >
        </base-mo00609>
        <g-custom-or-10016
          v-if="showDialogOr10016"
          v-bind="or10016"
          :oneway-model-value="or10016Data"
        />
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813 v-bind="or21813"> </g-base-or21813>
  <g-base-or21815 v-bind="or21815"> </g-base-or21815>
  <g-base-or21814 v-bind="or21814" />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';
@use '@/styles/cmn/dialog-base.scss';
@use '@/styles/cmn/dialog-data-table-list.scss';

.or32377_screen {
  margin: -8px !important;
}

.or32377_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or32377_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}
</style>
