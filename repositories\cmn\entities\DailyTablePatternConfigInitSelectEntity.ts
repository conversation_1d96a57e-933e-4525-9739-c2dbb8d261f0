import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * GUI00949_課題・目標取込
 *
 * @description
 * 課題・目標取込画面API用エンティティ
 *
 * <AUTHOR>
 */

/** 課題・目標取込入力エンティティ */
export interface DailyTablePatternConfigInitSelectInEntity extends InWebEntity {
  /** 処理区分 */
  processFlg: string
  /** 日課表ID */
  day1Id?: string
}
/**
 * 課題・目標取込出力エンティティ
 */
export interface DailyTablePatternConfigInitSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: DailyTablePatternConfigInitSelectOutData
}

/**
 * 出力エンティティ
 */
export interface DailyTablePatternConfigInitSelectOutData {
  /** パターングループ情報を取得する */
  groupList?: {
    /** ＩＤ */
    groupCd: string
    /** 特記事項 */
    mstKbn: string
    /** 随時実施するその他のサービス */
    groupKnj: string
    /** 改訂フラグ */
    seq: string
  }[]
  /** パターンタイトル情報を取得する */
  titleList?: {
    /** ＩＤ */
    day1Id: string
    /** 特記事項 */
    groupCd: string
    /** 随時実施するその他のサービス */
    titleKnj: string
    /** 改訂フラグ */
    seq: string
  }[]
  /** 表示用「日課表特記事項」情報 */
  tokkiKnjInfo?: {
    /** ＩＤ */
    rirekiId: string
    /** 特記事項 */
    tokkiKnj: string
    /** 随時実施するその他のサービス */
    sonotaKnj: string
    /** 改訂フラグ */
    kaiteiFlg: string
    /** 更新回数 */
    modifiedCnt: string
  }
  /** 「内容情報」リスト */
  naiyoList: {
    /** 内容CD */
    naiyoCd: string
    /** 区分 */
    dataKbn: string
    /** 内容 */
    naiyoKnj: string
    /** 表示順 */
    seq: string
  }[]
  /** 表示用「日課表詳細」リスト */
  patternList: Daily[]
  /** 表示用「日課表サービス例」リスト */
  svList: {
    /** サービス例ID */
    svId: string
    /** 区分 */
    dataKbn: string
    /** 適用フラグ */
    tekiyoFlg: string
    /** 更新回数 */
    modifiedCnt: string
  }[]
  /** 「日課表サービス例（日常）」リスト */
  svDailyList: {
    /** サービス例ID */
    svId: string
    /** 区分 */
    dataKbn: string
    /** 日常生活等介護サービス例 */
    svKnj: string
    /** 表示順 */
    seq: string
  }[]
  /** 日課表サービス例（介護）」リスト */
  svCaregiveList: {
    /** サービス例ID */
    svId: string
    /** 区分 */
    dataKbn: string
    /** 日常生活等介護サービス例 */
    svKnj: string
    /** 表示順 */
    seq: string
  }[]
  /** 「日課表サービス例（受託居宅）」リスト */
  svEntrustedList: {
    /** サービス例ID */
    svId: string
    /** 区分 */
    dataKbn: string
    /** 日常生活等介護サービス例 */
    svKnj: string
    /** 表示順 */
    seq: string
  }[]
}


/**
 * 日課表
 */
export interface Daily {
  /** カウンタ */
  id: string
  /** カウンタ */
  day1Id: string
  /** 区分 */
  dataKbn: string
  /** 開始時間 */
  startTime: string
  /** 終了時間 */
  endTime: string
  /** 内容CD */
  naiyoCd: string
  /** 内容 */
  naiyoKnj: string
  /** 日常処遇サービスメモ */
  memoKnj: string
  /** 担当者 */
  tantoKnj: string
  /** 文字サイズ */
  fontSize: string
  /** 文字位置 */
  alignment: string
  /** 文字カラー */
  fontColor: string
  /** 背景カラー */
  backColor: string
  /** 時間表示区分 */
  timeKbn: string
  /** 更新回数 */
  modifiedCnt: string
}
