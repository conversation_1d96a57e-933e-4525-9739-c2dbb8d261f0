import { Or26311Const } from './Or26311.constants'
import type { Or26311StateType } from './Or26311.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or26311: 有機体: 印刷順変更モーダル
 * GUI01010_印刷順変更
 *
 * @description
 * 印刷順変更モーダルのロジック
 *
 * <AUTHOR> DO DUC MANH
 */
export namespace Or26311Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or26311Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [],

      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
      initTwoWayValue: {
        printOrderModifiedType: [],
      },
    })

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or26311StateType>(Or26311Const.CP_ID(0))
}
