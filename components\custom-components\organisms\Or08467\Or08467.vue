<script setup lang="ts">
/**
 * Or08467: 現在利用しているサービス
 * GUI01070_現在利用しているサービス
 *
 * @description
 * 現在利用しているサービス
 *
 * <AUTHOR> LE VAN CUONG
 */
import { reactive, ref, watch, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or08467Const } from './Or08467.constants'
import { useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { CustomClass } from '~/types/CustomClassType'
import { Or00051Const } from '~/components/base-components/organisms/Or00051/Or00051.constants'
import type { Or08467OnewayType } from '~/types/cmn/business/components/Or08467Type'
import { Or08467Logic } from '~/components/custom-components/organisms/Or08467/Or08467.logic'
import { Or10883Logic } from '~/components/custom-components/organisms/Or10883/Or10883.logic'
import { Or10883Const } from '~/components/custom-components/organisms/Or10883/Or10883.constants'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'
import type { OrX0156OnewayType, OrX0156Type } from '~/types/cmn/business/components/OrX0156Type'
import { useCmnCom } from '~/utils/useCmnCom'

/**
 * I18n
 */
const { t } = useI18n()
/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

// 共通関数を取得
const { isOverLimitByCharWidth } = useCmnCom()

// 文字数制限チェック関数
const checkCharLimit = (value: string, maxLength: number): boolean => {
  if (maxLength === Or08467Const.ZERO) return true
  return !isOverLimitByCharWidth(value, maxLength)
}

// 前の値を保存するための変数
const previousValues = ref({
  publicService: '',
  nonPublicService: ''
})

// 文字数制限付きの値更新処理
const updateValueWithLimit = async (field: 'publicService' | 'nonPublicService', newValue: string) => {
  const maxLength = Or08467Const.FOUR_THOUSAND

  if (newValue && !checkCharLimit(newValue, maxLength)) {
    // 制限を超えた場合は、前の値に戻す
    await nextTick()
    if (field === Or08467Const.PUBLIC_SERVICE) {
      localX.publicService.value = previousValues.value.publicService
    } else {
      localX.nonPublicService.value = previousValues.value.nonPublicService
    }
    return false
  }

  // 制限内の場合は、前の値を更新してから新しい値を設定
  if (field === Or08467Const.PUBLIC_SERVICE) {
    previousValues.value.publicService = newValue
    localX.publicService.value = newValue
  } else {
    previousValues.value.nonPublicService = newValue
    localX.nonPublicService.value = newValue
  }
  return true
}

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue?: Or08467OnewayType
  uniqueCpId: string
}
/**************************************************
 * 変数定義
 **************************************************/
/**
 * 引継情報を取得する
 */
const props = defineProps<Props>()

/**
 * 双方向バインド用の内部変数
 */
const _local = reactive({
  isAllowEdit: props.onewayModelValue?.isAllowEdit ?? true,
})

// OrX0156用 双方向モデル
const localX = reactive({
  publicService: {
    value: props.onewayModelValue?.publicService,
  } as OrX0156Type,
  nonPublicService: {
    value: props.onewayModelValue?.nonPublicService,
  } as OrX0156Type,
})

/**
 * 片方向バインド用の内部変数
 */
const localOneway = reactive({
  publicServiceLabelOneway: {
    itemLabel: t('label.public-service'),
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent',
    }),
  } as Mo00615OnewayType,
  publicServiceBtnOneway: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  nonPublicServiceLabelOneway: {
    itemLabel: t('label.non-public-service'),
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent',
    }),
  } as Mo00615OnewayType,
  nonPublicServiceBtnOneway: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  // OrX0156: 公的サービス 用の片方向バインド
  publicServiceX0156Oneway: {
    itemLabel: t('label.public-service'),
    showItemLabel: true,
    isVerticalLabel: true,
    noResize: true,
    autoGrow: false,
    maxlength: '4000',
    class: 'A12BTextArea',
    rows: 5,
    maxRows: '5',
    disabled: !(props.onewayModelValue?.isAllowEdit ?? true),
    showEditBtnFlg: props.onewayModelValue?.isAllowEdit ?? true,
    showDividerLineFlg: false,
    mo00009: { btnIcon: 'edit_square', color: 'rgb(var(--v-theme-light-blue-400))', variant: 'text', width: '32px', height: '32px', style: 'background: #EBF2FD;' },
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent',
      labelClass: 'title-bar',
      labelStyle:
        'background: rgb(var(--v-theme-black-100)); width: 100%; padding: 12px 12px 12px 12px; border-radius: 4px; min-height: 48px; text-indent: 10px;',
      itemStyle:
        'background: rgb(var(--v-theme-secondaryBackground)); padding: 24px 32px 20px 32px; border-radius: 4px;'
    }),
  } as OrX0156OnewayType,
  // OrX0156: 非公的サービス 用の片方向バインド
  nonPublicServiceX0156Oneway: {
    itemLabel: t('label.non-public-service'),
    showItemLabel: true,
    isVerticalLabel: true,
    noResize: true,
    autoGrow: false,
    maxlength: '4000',
    class: 'A12BTextArea',
    rows: 5,
    maxRows: '5',
    disabled: !(props.onewayModelValue?.isAllowEdit ?? true),
    showEditBtnFlg: props.onewayModelValue?.isAllowEdit ?? true,
    showDividerLineFlg: false,
    mo00009: { btnIcon: 'edit_square', color: 'rgb(var(--v-theme-light-blue-400))', variant: 'text', width: '32px', height: '32px', style: 'background: #EBF2FD;' },
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent',
      labelClass: 'title-bar',
      labelStyle:
        'background: rgb(var(--v-theme-black-100)); width: 100%; padding: 12px 12px 12px 12px; border-radius: 4px; min-height: 48px; text-indent: 10px;',
      // 入力領域を白背景のボックスで内包（OrX0156のCSSは変更しない）
      itemStyle:
        'background: rgb(var(--v-theme-secondaryBackground)); padding: 24px 32px 20px 32px; border-radius: 4px;'
    }),
  } as OrX0156OnewayType,

})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * or00051
 */
const or00051 = ref({ uniqueCpId: '' })

/**
 * or10883
 */
const or10883 = ref({ uniqueCpId: '' })

/**
 * or10883Type
 */
const or10883Type = ref<Or10883TwowayType>({})

/**
 * Or10883 ダイアログを表示するかどうかを決定する計算プロパティ。
 */
const isShowDialogOr10883 = computed(() => {
  return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 現在選択されている項目を示す変数
 */
let itemSelected = 0

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00051Const.CP_ID]: or00051.value,
  [Or10883Const.CP_ID(0)]: or10883.value,
})

/**
 * Or10883のonewayモデルを初期化するための変数。
 */
const or10883OnewayModel: Or10883OnewayType = {
  userId: '',
  t1Cd: '',
  t2Cd: '',
  t3Cd: '',
  inputContents: '',
  title: '',
  historyTableName: '',
  historyColumnName: '',
}
/**
 * 「公的サービスアイコンボタン」押下
 */
function onPublicServiceClick() {
  itemSelected = 1
  or10883OnewayModel.userId = systemCommonsStore.getUserId ?? ''
  or10883OnewayModel.t1Cd = '20'
  or10883OnewayModel.t2Cd = '4'
  or10883OnewayModel.t3Cd = '1'
  or10883OnewayModel.title = t('label.public-service')
  or10883OnewayModel.tableName = 'kyc_tuc_khn13'
  or10883OnewayModel.columnName = 'kouteki_sv_knj'
  or10883OnewayModel.inputContents = localX.publicService.value ?? ''
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「非公的サービスアイコンボタン」押下
 */
function onNonPublicServiceClick() {
  itemSelected = 2
  or10883OnewayModel.userId = systemCommonsStore.getUserId ?? ''
  or10883OnewayModel.t1Cd = '20'
  or10883OnewayModel.t2Cd = '4'
  or10883OnewayModel.t3Cd = '2'
  or10883OnewayModel.title = t('label.non-public-service')
  or10883OnewayModel.tableName = 'kyc_tuc_khn13'
  or10883OnewayModel.columnName = 'hikouteki_sv_knj'
  or10883OnewayModel.inputContents = localX.nonPublicService.value ?? ''
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**************************************************
 * ウォッチャー
 **************************************************/
// Or08467Logic.dataの変更を監視してlocalの値を更新
watch(
  () => Or08467Logic.data.get(props.uniqueCpId),
  (newData) => {
    if (newData) {
      localX.publicService.value = newData.publicService ?? ''
      localX.nonPublicService.value = newData.nonPublicService ?? ''
    }
  },
  { immediate: true }
)

watch(
  () => localX.publicService.value,
  async (newValue) => {
    if (newValue && await updateValueWithLimit('publicService', newValue)) {
      // Or08467Logic.dataを通じてparentに通知
      Or08467Logic.data.set({
        uniqueCpId: props.uniqueCpId,
        value: {
          publicService: newValue ?? '',
          nonPublicService: localX.nonPublicService.value ?? '',
        },
      })
    }
  }
)

watch(
  () => localX.nonPublicService.value,
  async (newValue) => {
    if (newValue && await updateValueWithLimit('nonPublicService', newValue)) {
      // Or08467Logic.dataを通じてparentに通知
      Or08467Logic.data.set({
        uniqueCpId: props.uniqueCpId,
        value: {
          publicService: localX.publicService.value ?? '',
          nonPublicService: newValue ?? '',
        },
      })
    }
  }
)

watch(
  () => or10883Type.value,
  () => {
    const naiyo = or10883Type.value.naiyo ?? ''
    switch (itemSelected) {
      case 1: {
        localX.publicService.value = naiyo
        break
      }
      case 2: {
        localX.nonPublicService.value = naiyo
        break
      }
    }
    itemSelected = 0
    Or10883Logic.state.set({
      uniqueCpId: or10883.value.uniqueCpId,
      state: { isOpen: false },
    })
  }
)
</script>

<template>
  <c-v-sheet
    class="pt-3 pr-2 view"
    style="background-color: transparent"
  >
    <c-v-row>
      <c-v-col>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="mb-0"
          >
            <g-custom-or-x0156
              v-model="localX.publicService"
              :oneway-model-value="localOneway.publicServiceX0156Oneway"
              class="memoInputTextArea"
              @on-click-edit-btn.stop="onPublicServiceClick"
            />
          </c-v-col>
          <c-v-col
            cols="12"
            class="mb-0"
          >
            <g-custom-or-x0156
              v-model="localX.nonPublicService"
              :oneway-model-value="localOneway.nonPublicServiceX0156Oneway"
              class="memoInputTextArea"
              @on-click-edit-btn.stop="onNonPublicServiceClick"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <g-custom-or-10883
    v-if="isShowDialogOr10883"
    v-bind="or10883"
    v-model="or10883Type"
    :oneway-model-value="or10883OnewayModel"
  />
</template>

<style scoped lang="scss">
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 1080px;
}

:deep(.title-bar .item-label) {
  font-weight: bold !important;
}
:deep(.memoInputTextArea) {
  textarea {
    height: 79px !important;
  }
}
</style>
