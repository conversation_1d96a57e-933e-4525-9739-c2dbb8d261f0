<script setup lang="ts">
import { computed, definePageMeta, reactive, ref, useInitialize, useScreenStore } from '#imports'
import { Or36086Const } from '~/components/custom-components/organisms/Or36086/Or36086.constants'
import { Or36086Logic } from '~/components/custom-components/organisms/Or36086/Or36086.logic'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Or36086OnewayType, Or36086Type } from '~/types/cmn/business/components/Or36086Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01039'
// ルーティング
const routing = 'GUI01039/pinia'
// 画面物理名
const screenName = 'GUI01039'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or36086 = ref({ uniqueCpId: Or36086Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01039' },
})
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType
/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or36086Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or36086.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01039',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or36086Const.CP_ID(0) }],
})

// ダイアログ表示フラグ
const showDialogOr36086 = computed(() => {
  // Or36086のダイアログ開閉状態
  return Or36086Logic.state.get(or36086.value.uniqueCpId)?.isOpen ?? false
})

// 現在のシステム年月の取得（yyyy/MM）
function getFormattedDate() {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  return `${year}/${month}`
}

/**
 * 親画面からの初期値
 */
const or36086Data: Or36086OnewayType = {
  // 事業所ID
  shienId: 26,
  // 利用者ID
  userId: 249,
  // 当該年月
  yymmYm: '2015/08',
  // 有効期間ID
  validPeriodId: 8,
  // 週以外フラグ
  isWeeklyFlg: true,
}
const localData = reactive({
  isWeeklyFlg: { value: '1' } as Mo00045Type,
  shienId: { value: '26' } as Mo00045Type,
  userId: { value: '249' } as Mo00045Type,
  yymmYm: { value: '2015/08' } as Mo00045Type,
})
/**
 * 戻り値です
 */
const or36086Type = ref<Or36086Type>({
  //モード
  clickMode: 0,
  // 選択範囲週.開始日
  startDate: new Date(),
  // 事業所ID
  shienId: 0,
  // 事業所ID
  weeklyList: [],
})

/**
 *  ボタン押下時の処理(Or36086)
 *
 * @param isWeeklyFlg - 週以外フラグ
 */
function onClickOr36086(isWeeklyFlg: boolean) {
  or36086Data.isWeeklyFlg = isWeeklyFlg
  // Or27487のダイアログ開閉状態を更新する
  Or36086Logic.state.set({
    uniqueCpId: or36086.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function onClickOr36086_1() {
  or36086Data.isWeeklyFlg = localData.isWeeklyFlg.value === '1' ? true : false
  or36086Data.shienId = Number(localData.shienId.value)
  or36086Data.userId = Number(localData.userId.value)
  or36086Data.yymmYm = localData.yymmYm.value
  // Or27487のダイアログ開閉状態を更新する
  Or36086Logic.state.set({
    uniqueCpId: or36086.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36086(true)"
        >GUI01039_月間取込(親画面.週以外フラグ が「週単位以外」の場合)
      </v-btn>
      <g-custom-or-36086
        v-if="showDialogOr36086"
        v-bind="or36086"
        v-model="or36086Type"
        :oneway-model-value="or36086Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr36086(false)"
        >GUI01039_月間取込(親画面.週以外フラグ が「週単位」の場合)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">事業所ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.shienId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">当該年月</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.yymmYm"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">週以外フラグ(1/0)</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.isWeeklyFlg"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr36086_1"> GUI01039_月間取込 疎通起動 </v-btn>
  </div>
</template>
