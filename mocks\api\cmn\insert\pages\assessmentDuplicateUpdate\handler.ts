/**
 * Or35672:アセスメント複写モーダル
 * GUI00807_アセスメント複写
 *
 * @description
 * アセスメント複写モーダル
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { AssessmentDuplicatetSelectInEntity } from '~/repositories/cmn/entities/AssessmentDuplicatetEntity'
/**
 * GUI00626_アセスメントマスタ初期情報保存APIモック
 *
 * @description
 * GUI00626_アセスメントマスタ情報データを保存する。
 * dataName："assessmentMasterUpdate"
 */
export function handler(inEntity: AssessmentDuplicatetSelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {},
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
