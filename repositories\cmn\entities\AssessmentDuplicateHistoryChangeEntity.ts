import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * Or35672:アセスメント複写モーダル
 * GUI00807_アセスメント複写
 *
 * @description
 * アセスメント複写モーダルAPI用エンティティ
 *
 * <AUTHOR>
 */
/**
 * アセスメント複写モーダル履歴チェンジAPI入力エンティティ
 */
export interface AssessmentDuplicateHistoryChangeInEntity extends InWebEntity {
  /** 計画期間ID */
  sc1Id: string
  /** アセスメントID */
  gdlId: string
  /** アセスメント番号 */
  accessmentNo: string
}

/**
 * アセスメント複写モーダル履歴チェンジAPI取得出力エンティティ
 */
export interface AssessmentDuplicateHistoryChangeOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** 履歴リスト */
    rirekiList: {
      /** アセスメントID */
      gdlId: string
      /** 計画期間ID */
      sc1Id: string
      /** アセスメント実施日 */
      asJisshiDateYmd: string
      /** 記載者ID */
      shokuId: string
      /** 記載者名 */
      shokuKnj: string
      /** フェースシート */
      ass1: string
      /** 家族状況／支援 */
      ass2: string
      /** ｻｰﾋﾞｽ利用状況 */
      ass3: string
      /** 住居等の状況 */
      ass4: string
      /** 本人の健康状態 */
      ass5: string
      /** 本人の基本動作等1 */
      ass6: string
      /** Ｅの状態 */
      ass7: string
      /** Ｆの状態 */
      ass8: string
      /** Ｇの状態 */
      ass9: string
      /** Ｈの状態 */
      ass10: string
      /** Ｉの状態 */
      ass11: string
      /** Ｊの状態 */
      ass12: string
      /** 本人の基本動作等8 */
      ass13: string
      /** 医師の意見 */
      ass14: string
      /** 全体のまとめ･特記事項 */
      ass15: string
      /** 改定フラグ */
      ninteiFormF: string
      /** 更新回数 */
      modifiedCnt: string
      /** １日のスケジュール */
      ass16: string
    }[]
    /** 課題と目標リスト */
      gdlKadaiList: {
        /** id */
        id?: string
        /** アセスメントID */
        gdlId?: string
        /** 計画期間ID */
        sc1Id?: string
        /** アセスメント番号 */
        assNo?: string
        /** 課題 */
        kadaiKnj?: string
        /** 長期 */
        choukiKnj?: string
        /** 短期 */
        tankiKnj?: string
        /** 連番 */
        seq?: string
        /** 更新回数 */
        modifiedCnt?: string
        /** アセスメント名称 */
        assName?: string
      }[]
  }
}
