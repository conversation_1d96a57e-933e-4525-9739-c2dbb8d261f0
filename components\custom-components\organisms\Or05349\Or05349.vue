<script setup lang="ts">
/**
 * Or05349:週間計画入力フォーム
 *
 * @description
 * 週間計画入力フォーム
 *
 * <AUTHOR>
 */

import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27734Const } from '../Or27734/Or27734.constants'
import { Or27734Logic } from '../Or27734/Or27734.logic'
import { Or28809Logic } from '../Or28809/Or28809.logic'
import { Or27501Logic } from '../Or27501/Or27501.logic'
import { Or28809Const } from '../Or28809/Or28809.constants'
import { Or27501Const } from '../Or27501/Or27501.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import type { OrX0048CalendarConfig, OrX0048CalendarEvent } from '../OrX0048/OrX0048.type'
import { Or36086Logic } from '../Or36086/Or36086.logic'
import { Or36086Const } from '../Or36086/Or36086.constants'
import { Or27610Logic } from '../Or27610/Or27610.logic'
import { Or26447Logic } from '../Or26447/Or26447.logic'
import { Or26447Const } from '../Or26447/Or26447.constants'
import { Or27610Const } from '../Or27610/Or27610.constants'
import { OrX0172Const } from '../OrX0172/OrX0172.constants'
import { useScreenOneWayBind, useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { Or27610OnewayType } from '~/types/cmn/business/components/Or27610Type'
import type { Or28992OnewayType } from '~/types/cmn/business/components/Or28992Type'
import type {
  cks54List,
  cks56List,
  Or05349OnewayType,
  Or05349Type,
} from '~/types/cmn/business/components/Or05349Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or33097OnewayType } from '~/types/cmn/business/components/Or33097Type'
import type { Or27734Type } from '~/types/cmn/business/components/Or27734Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or27501OnewayType, Or27501Type } from '~/types/cmn/business/components/Or27501Type'
import type {
  DataTableData,
  WeeklyPlan,
} from '~/components/custom-components/organisms/Or27610/Or27610.type'
import { OrX0048Const } from '~/components/custom-components/organisms/OrX0048/OrX0048.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or05349Const } from '~/components/custom-components/organisms/Or05349/Or05349.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { Or36086OnewayType, Or36086Type } from '~/types/cmn/business/components/Or36086Type'
import type { Or28809OneWayType } from '~/types/cmn/business/components/Or28809Type'
import type {
  WeekPlanImportInfoEditSelectEntity,
  WeekPlanImportInfoEditSelectOutEntity,
} from '~/repositories/cmn/entities/WeekPlanImportInfoEditSelectEntity'
import type {
  WeekPlanMonthlyImportSelectEntity,
  WeekPlanMonthlyImportSelectOutEntity,
} from '~/repositories/cmn/entities/WeekPlanMonthlyImportSelectEntity'
import type {
  careplan2ImportSelectData,
  Or26447OnewayType,
  Or26447Type,
} from '~/types/cmn/business/components/Or26447Type'
import type { OrX0156Type, OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { Mo01282Type } from '~/types/business/components/Mo01282Type'
import type { OrX0172OnewayType, OrX0172Type } from '~/types/cmn/business/components/OrX0172Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or05349Type
  onewayModelValue: Or05349OnewayType
  uniqueCpId: string
  parentCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// 期間管理フラグ
const plannningPeriodManageFlg = ref<string>('')

// 履歴表示フラグ
const isHistoryShow = ref<boolean>(true)
// 削除フラグ
const deleteFlag = ref<boolean>(false)
// 時間帯内容
const timeContent = ref<string>('')
// 保険編集フラグ
const hokenEditFlg = ref<boolean>(false)
// 有効期間ID
const validId = ref<number>(-1)
// 保険のサービス
const hokenService = ref<string>('')
// 選択した行のindex
const selectedItemIndex = ref<number>(1)

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
// カレンダーの設定
const calendarConfig = ref<OrX0048CalendarConfig>({
  events: [],
  cellHeight: 27,
  zoom: 1,
  readonly: false,
})

const data = ref<DataTableData>({ dataList: [] })
const calendar = ref({ reRenderCalendar: Function })

const showData = ref<DataTableData>({
  dataList: [],
})

// ローカルTwoway
const local = reactive({
  or05349: {} as Or05349Type,
  // 主な日常生活内容
  njModelArr: [] as OrX0172Type[],
  weeklyModel: { value: '' } as OrX0156Type,
  hokenModel: { value: '' } as OrX0156Type,
  cks54: {} as cks54List,
  or26447: {
    careplan2ImportSelectList: [] as careplan2ImportSelectData[],
  } as Or26447Type,
})

// ローカルOneway
const localOneway = reactive({
  or05349: {
    ...props.onewayModelValue,
  } as Or05349OnewayType,
  or28992Oneway: {
    assessmentHome: {
      title: '',
    },
  } as Or28992OnewayType,
  or33097Oneway: {
    assessmentHome: {
      title: '',
    },
  } as Or33097OnewayType,
  // タイトル: 表用テキストフィールド
  orX0172Oneway: {
    inputMode: OrX0172Const.INPUT_MODE.TEXT_ONLY,
    text: {
      orX0172InputOneway: {
        rules: [],
      },
    },
    // maxLength: '20',
  } as OrX0172OnewayType,
  orX0156OnewayWeekly: {
    showItemLabel: false,
    showDividerLineFlg: false,
    showEditBtnFlg: true,
    maxlength: '360',
    rows: 3,
    maxRows: '3',
    isVerticalLabel: false,
    autoGrow: false,
    noResize: true,
  } as OrX0156OnewayType,
  orX0156OnewayHoken: {
    showItemLabel: false,
    showDividerLineFlg: false,
    showEditBtnFlg: true,
    maxlength: '360',
    rows: 3,
    maxRows: '3',
    isVerticalLabel: false,
    autoGrow: false,
    noResize: true,
  } as OrX0156OnewayType,
  or28809Data: {
    /** 親画面.事業者ID */
    svJigyoId: '1',
    /** 親画面.施設ID */
    shisetuId: '1',
    /** 親画面.利用者ID */
    userId: '1',
    /** 親画面.種別ID */
    syubetsuId: '1',
    /**  親画面.計画表ID */
    plan11Id: '1',
    /** 親画面.期間管理フラグ */
    kikanFlg: '1',
    /** 親画面.計画期間ID */
    sc1Id: '1',
    /** 親画面.遷移元区分_「1：週間計画」 */
    seniKbn: '1',
    /** 親画面.計画表様式フラグ_0_A3-1枚;1_A4-3枚;2_A4-2枚 */
    itakuKkakPrtFlg: '1',
    /** 親画面.保険サービス取込(事業所名） */
    itakuKkakHsJigyoFlg: '1',
    /** 親画面.頻度取込_「0:選択項目名称」の場合、「1:週〇回／月〇回」の場合、「2:随時」の場合、 */
    itakuKkakHindoFlg: '1',
  } as Or28809OneWayType,
  or27501OnewayModel: {
    svJigyoId: '1',
    shoriYymm: '2025/07',
    termid: '1',
    siyouFlg: '1',
    dwWeeks: [],
    dwRiyou: [],
    dwKakusyuu: [],
    dsTsukihi: [],
    userId: '1',
    svJigyoIds: [],
    caller: '1',
    torikomiKbn: '1',
    copyTnki: '1',
    selSyori: '1',
    fygTani: '1',
  } as Or27501OnewayType,
  mo00611OnewayIngestion: {
    btnLabel: t('label.monthly-import'),
    minWidth: '79px',
  } as Mo00611OnewayType,
  mo00611OnewayPrevention: {
    btnLabel: t('label.prevent-care-plan'),
  } as Mo00611OnewayType,
  mo00611OnewayBudgetEst: {
    btnLabel: t('label.estimate'),
    minWidth: '52px',
  } as Mo00611OnewayType,
  mo00611OnewayPlan: {
    btnLabel: t('label.care-plan-title'),
    minWidth: '65px',
  } as Mo00611OnewayType,
  or51775OnewayModel: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
  } as Or51775OnewayType,
  or27610Oneway: {
    /**
     *index
     */
    index: 0,
    /**
     *週間計画詳細リスト
     */
    weekPlanDetailList: [],
    /**
     *週間計画加算リスト
     */
    weekPlanAdditionList: [],
    /**
     *週間計画担当者リスト
     */
    weekPlanManagerList: [],
    /**
     *週間計画隔週リスト
     */
    weekPlanEveryOtherWeekList: [],
    /**
     *週間計画月日リスト
     */
    weekPlanDateList: [],
    /**
     *初期設定マスタ情報
     */
    initialConfigureMasterInfo: null,
    /**
     *メインパラメータ
     */
    mainParameters: null,
    /**
     *計画書様式
     */
    carePlanStyle: 2,
    /**
     *週以外フラグ
     */
    weekOtherThanFlag: false,
    /**
     *選択された週間計画詳細データID
     */
    selectWeekPlanDetailDataId: '',
    /**
     *パターンモード
     */
    patternMode: false,
    /**
     *利用者ID
     */
    userId: '',
    /**
     *有効期間ID
     */
    validPeriodId: '',
    /**
     *新規モード
     */
    newMode: false,
    /**
     *事業所ID
     */
    officeId: '',
    /**
     * 操作パタン(パターンモード)
     */
    operationPattern: '',
    /**
     * 計画開始時間(開始時間)
     */
    kaishiJikan: '',
    /**
     * 計画終了時間(終了時間)
     */
    shuuryouJikan: '',
    /**
     * 処理日
     */
    processingDate: '',
    /**
     * 開始日(検索サービスの開始日)
     */
    startDate: '',
    /**
     * 終了日(検索サービスの終了日)
     */
    endDate: '',
    /**
     *  機能名
     */
    featureName: '週間計画',
  } as Or27610OnewayType,
})

const or27501Type = ref<Or27501Type>({
  value: '',
})

/**
 * 1 時間を目盛として、時間枠生成
 */
const hourSlots = computed(() => {
  const slots = []
  for (let h = 0; h < 26; h++) {
    slots.push(
      `${h.toString().padStart(2, Or05349Const.DEFAULT.NUMBER_ZERO)}:${Or05349Const.DEFAULT.NUMBER_DB_ZERO}`
    )
  }
  return slots
})

const or27734Type = ref<Or27734Type>({
  //選択された：サービス事業者ID
  serviceOfficeId: '',
  //利用者ID
  userId: '',
  //当該年月
  tougaiYm: '',
  //モード
  model: '',
  //週以外フラグ
  weekOtherThanFlag: false,
  //元画面有効期間ID
  orignValidId: '',
})

/**
 * 親画面からの初期値
 */
const or36086Data: Or36086OnewayType = {
  // 事業所ID
  shienId: 99,
  // 利用者ID
  userId: 18,
  // 当該年月
  yymmYm: getFormattedDate(),
  // 有効期間ID
  validPeriodId: 4,
  // 週以外フラグ
  isWeeklyFlg: true,
}

const or26447OnewayModel: Or26447OnewayType = {
  // "遷移元_1週間計画_2日課計画_3計画モニタリング_4評価表_5実施モニタリング"
  seniMoto: '',
  // 親画面.期間管理フラグ_0True_1False
  kikanFlg: '',
  // 親画面.計画書様式_0施設_1居宅
  cksFlg: '',
  // "画面.取込元__1計画書(1)_2計画書(2)"
  torikomiMoto: '',
  // "画面.取込モード__0上書_1追加"
  torikomiModel: '',
  //番号_0表示_1非表示
  kaigoNo: '',
  // 期間の管理_0日付で管理_1文章で管理
  planManage: '',
  //事業所
  office: '',
  // 親画面.IDが選択している.履歴.有効期間ID
  termid: '',
  //  親画面.法人ID
  houjinId: '',
  //  親画面.事業者ID
  svJigyoId: '',
  //  親画面.利用者ID
  userId: '',
  //  親画面.種別ID
  syubetuId: '',
  //  親画面.施設ID
  shisetuId: '',
  //  親画面.事業所ID
  defSvJigyoId: '',
  //  親画面.契約者ID
  keiyakushaId: '',
  //  親画面.sys略称
  sys3ryaku: '',
  // 計画表：頻度取込
  itakuKkakHindoFlg: '',
}

/**
 * 戻り値です
 */
const or36086Type = ref<Or36086Type>({
  //モード
  clickMode: 0,
  // 選択範囲週.開始日
  startDate: new Date(),
  // 事業所ID
  shienId: 0,
  // 週以外リスト
  weeklyList: [],
})

/**
 * 時間帯データ
 */
const wp = {
  id: '',
  startTime: Or05349Const.DEFAULT.TIME_ZERO,
  endTime: Or05349Const.DEFAULT.TIME_ZERO,
  outFrameDisplay: { modelValue: false },
  dayOfWeek1: false,
  dayOfWeek2: false,
  dayOfWeek3: false,
  dayOfWeek4: false,
  dayOfWeek5: false,
  dayOfWeek6: false,
  dayOfWeek7: false,
  dayOfWeekOther: false,
  contents: '',
  manager: [''],
  frequency: '',
  insuranceType: '',
  insuranceBussiness: '',
  insuranceServices: '',
  insuranceAddition: '',
  fontSize: {
    modelValue: '12',
  },
  fontSizeTitle: {
    value: '12',
  },
  textPosition: {
    modelValue: '0',
  },
  omission: {
    modelValue: 1,
  },
  color: '#333333',
  background: '#E6E6E6',
  timeDisplay: {
    modelValue: 0,
  },
} as WeeklyPlan
/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or05349OnewayType>({
  cpId: Or05349Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

const orX0048 = ref({ uniqueCpId: OrX0048Const.CP_ID(0) })
const or27610 = ref({ uniqueCpId: '' })

const or27734 = ref({ uniqueCpId: Or27734Const.CP_ID(1) })
const or28809 = ref({ uniqueCpId: Or28809Const.CP_ID(1) })
const or27501 = ref({ uniqueCpId: Or27501Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })
const or36086 = ref({ uniqueCpId: Or36086Const.CP_ID(0) })
const or26447 = ref({ uniqueCpId: Or26447Const.CP_ID(0) })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0048Const.CP_ID(0)]: orX0048.value,
  [Or27610Const.CP_ID(0)]: or27610.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or26447Const.CP_ID(0)]: or26447.value,
})
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => local.or05349,
  () => {
    emit('update:modelValue', local.or05349)
  },
  { deep: true, immediate: true }
)

/**
 * 時間帯タイトル
 */
watch(
  () => data.value.dataList,
  () => {
    setCalendar(data.value.dataList)
    local.or05349.data = data.value
  }
)

/**
 * 主な日常生活上の活動
 */
watch(
  () => local.njModelArr,
  () => {
    local.or05349.njModelArr = local.njModelArr
  },
  { deep: true, immediate: true }
)

/**
 * 利用票取込データ取得処理を行う
 */
watch(
  () => or36086Type.value,
  async () => {
    // バックエンドAPIから初期情報取得
    const inputData: WeekPlanMonthlyImportSelectEntity = {
      shienId: '',
      userId: '',
      yymmYm: '',
      termid: '',
      yymmdd: '',
      tougaiYm: '',
      igaiList: [
        {
          asIgaiSvKbn: '',
          asIgaiSvCd: '',
        },
      ],
    }
    // AC025-4の返却情報.支援事業者ID
    inputData.shienId = String(or36086Type.value.shienId)
    // 親画面.利用者ID
    inputData.userId = localOneway.or05349.userId ?? ''
    // AC025-4の返却情報.日付(YYYY/MM)
    inputData.yymmYm = String(or36086Type.value.startDate).substring(0, 7)
    // 変数.有効期間ID
    inputData.termid = String(validId.value)
    // AC025-4の返却情報.日付
    inputData.yymmdd = String(or36086Type.value.startDate)
    // 処理日:画面.処理月
    inputData.tougaiYm = localOneway.or05349.syoriMonth ?? ''
    // AC025-4の返却情報.週以外リスト
    for (const e of or36086Type.value.weeklyList) {
      const data = {
        asIgaiSvKbn: String(e.importMethod),
        asIgaiSvCd: e.svKindCd,
      }
      inputData.igaiList.push(data)
    }
    // 週間計画詳細リスト取得
    const resData: WeekPlanMonthlyImportSelectOutEntity = await ScreenRepository.select(
      'weekPlanMonthlyImportSelect',
      inputData
    )

    if (resData) {
      // ポップアップ画面の返却情報.モードが上書き「1」の場合
      if (or36086Type.value.clickMode === 1) {
        // AC025-5の返却情報より、下記項目を上書き。
        // ・週間計画詳細リスト
        if (local.or05349.cks52List !== undefined) {
          local.or05349.cks52List.splice(0)
        }
        local.or05349.cks52List = resData.data.cks52List
        local.or05349.cks56List = [] as cks56List[]
        for (const cks52 of local.or05349.cks52List) {
          if (cks52.cks56List !== undefined) {
            for (const cks56 of cks52.cks56List) {
              local.or05349.cks56List.push(cks56)
            }
          }
        }
      }
      // ポップアップ画面の返却情報.モードが追加「2」の場合
      if (or36086Type.value.clickMode === 2) {
        // AC025-5の返却情報より、下記項目を追加。
        // ・週間計画詳細リスト
        // 変数.週間計画詳細リスト＝計画対象期間情報.履歴変更情報.詳細情報.週間計画詳細リスト
        for (const e of resData.data.cks52List) {
          local.or05349.cks52List?.push(e)
        }
        if (local.or05349.cks52List !== undefined) {
          // 変数.週間計画担当リスト＝計画対象期間情報.履歴変更情報.詳細情報.週間計画担当リスト
          local.or05349.cks56List = [] as cks56List[]
          for (const cks52 of local.or05349.cks52List) {
            if (cks52.cks56List !== undefined) {
              for (const cks56 of cks52.cks56List) {
                local.or05349.cks56List.push(cks56)
              }
            }
          }
        }
      }
    }
  }
)
/**************************************************
 * 関数
 **************************************************/
/**
 *  AC001-1_初期化
 *
 * @param or05349 - データ
 */
function initData(or05349: Or05349OnewayType) {
  localOneway.or05349 = or05349
  localOneway.orX0172Oneway.readonly = localOneway.or05349.readonly ?? false
  localOneway.orX0156OnewayWeekly.readonly = localOneway.or05349.readonly ?? false
  localOneway.orX0156OnewayHoken.readonly = localOneway.or05349.readonly ?? false
  localOneway.mo00611OnewayIngestion.disabled = localOneway.or05349.readonly ?? false
  localOneway.mo00611OnewayPrevention.disabled = localOneway.or05349.readonly ?? false
  localOneway.mo00611OnewayBudgetEst.disabled = localOneway.or05349.readonly ?? false
  localOneway.mo00611OnewayPlan.disabled = localOneway.or05349.readonly ?? false

  if (localOneway.or05349.wp?.fontSize) {
    wp.fontSize.modelValue = localOneway.or05349.wp?.fontSize.modelValue
    wp.textPosition = localOneway.or05349.wp?.textPosition ?? wp.textPosition
    wp.color = localOneway.or05349.wp?.color ?? wp.color
    wp.background = localOneway.or05349.wp?.background ?? wp.background
    wp.startTime = localOneway.or05349.wp?.startTime ?? wp.startTime
    wp.endTime = localOneway.or05349.wp?.endTime ?? wp.endTime
    wp.insuranceBussiness = localOneway.or05349.wp?.insuranceBussiness ?? wp.insuranceBussiness

    isHistoryShow.value = localOneway.or05349.isHistoryShow ?? false
    deleteFlag.value = localOneway.or05349.deleteFlag ?? false
    validId.value = localOneway.or05349.validId ?? -1
  }
}

/**
 *  AC001-5～
 *
 * @param or05349 - データ
 */
function doContentSetting(or05349: Or05349Type) {
  local.or05349 = or05349
  hokenEditFlg.value = false
  local.hokenModel.value = ''
  // カレンダークリア
  showData.value?.dataList.splice(0)
  data.value?.dataList.splice(0)
  calendarConfig.value.events = []
  calendarConfig.value.readonly = localOneway.or05349.readonly ?? false
  // カレンダーの画面を反映
  calendar.value?.reRenderCalendar()
  // 週間計画詳細リストより繰り返し、時間帯内容ラベルを作成する。
  if (local.or05349.cks52List !== undefined && local.or05349.cks52List.length > 0) {
    for (const cks52 of local.or05349.cks52List) {
      // 週間計画詳細リスト.曜日の最後の一桁が（週単位）以外の場合
      if (
        cks52.youbi.substring(cks52.youbi.length - 1, cks52.youbi.length) !==
        Or05349Const.DEFAULT.WEEK_UNIT
      ) {
        wp.fontSize.modelValue = cks52.fontSize
        wp.textPosition = { modelValue: cks52.alignment }
        wp.color = cks52.fontColor
        wp.background = cks52.backColor
        wp.startTime = cks52.kaishiJikan
        wp.endTime = cks52.shuuryouJikan
        wp.insuranceBussiness = cks52.svJigyoKnj
        let count = 1
        for (const str of cks52.youbi.split('')) {
          if (count === 1 && str === '1') {
            wp.dayOfWeek1 = true
          }
          if (count === 2 && str === '1') {
            wp.dayOfWeek2 = true
          }
          if (count === 3 && str === '1') {
            wp.dayOfWeek3 = true
          }
          if (count === 4 && str === '1') {
            wp.dayOfWeek4 = true
          }
          if (count === 5 && str === '1') {
            wp.dayOfWeek5 = true
          }
          if (count === 6 && str === '1') {
            wp.dayOfWeek6 = true
          }
          if (count === 7 && str === '1') {
            wp.dayOfWeek7 = true
          }
          count = count + 1
          if (count === cks52.youbi.length) {
            break
          }
        }
        // 共通情報. 計画書様式が施設の場合
        if (localOneway.or05349.cksFlg === Or05349Const.DEFAULT.SHISETSU) {
          // 共通情報.サービス職種IDor種類表示フラグが表示の場合
          if (localOneway.or05349.shuruihyoujiFlg) {
            // 週間計画詳細リスト.週間計画担当者リストがNULL以外の場合
            if (local.or05349.cks56List !== undefined && local.or05349.cks56List.length > 0) {
              // 週間計画詳細リスト.週間計画担当者リストより繰り返し、変数.時間帯内容を編集する。
              for (const cks56 of local.or05349.cks56List) {
                // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.週間計画担当者リスト.担当者名称+改行コード
                timeContent.value =
                  timeContent.value + cks56.shokushuKnj + Or05349Const.DEFAULT.LINE_BREAK
              }
            }
          }
        }
        // 共通情報. 計画書様式が居宅「2」、或いは、共通情報.事業所CDが予防介護支援「50010」の場合
        if (
          localOneway.or05349.cksFlg === Or05349Const.DEFAULT.ITAKU ||
          localOneway.or05349.defSvJigyoCd === '50010'
        ) {
          // 共通情報.サービス職種IDor種類表示フラグが表示の場合
          if (localOneway.or05349.shuruihyoujiFlg) {
            // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.サービス種類名称+改行コード
            timeContent.value =
              timeContent.value + cks52.svShuruiKnj + Or05349Const.DEFAULT.LINE_BREAK
          }
          // 共通情報.事業者表示フラグが正式の場合
          if (localOneway.or05349.jigyoShowFlg === 0) {
            // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.サービス事業者名称+改行コード
            timeContent.value =
              timeContent.value + cks52.svJigyoKnj + Or05349Const.DEFAULT.LINE_BREAK
          }
          // 共通情報.事業者表示フラグが略称の場合
          if (localOneway.or05349.jigyoShowFlg === 1) {
            // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.サービス事業者略称+改行コード
            timeContent.value =
              timeContent.value + cks52.svJigyoRyaku + Or05349Const.DEFAULT.LINE_BREAK
          }
          // 共通情報.サービス項目表示フラグが表示の場合
          if (localOneway.or05349.komokuShowFlg) {
            // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.サービス項目名称+改行コード
            timeContent.value =
              timeContent.value + cks52.svItemKnj + Or05349Const.DEFAULT.LINE_BREAK
          }
        }
        // 共通情報. 内容表示フラグが表示の場合
        if (localOneway.or05349.contentShowFlg) {
          // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.内容
          timeContent.value = timeContent.value + cks52.naiyouKnj
        }
        // 週間計画詳細リスト.時間表示区分が先頭表示の場合
        if (cks52.timeKbn === Or05349Const.DEFAULT.TIMD_DISP_KBN_FIRST) {
          // ・変数.時間帯内容＝週間計画詳細リスト.開始時間+" ～ " + 週間計画詳細リスト.終了時間 + 変数.時間帯内容
          timeContent.value =
            cks52.kaishiJikan + t('label.wavy') + cks52.shuuryouJikan + timeContent.value
        }
        // 週間計画詳細リスト.時間表示区分が最後表示の場合
        if (cks52.timeKbn === Or05349Const.DEFAULT.TIMD_DISP_KBN_LAST) {
          // ・変数.時間帯内容＝変数.時間帯内容 + 週間計画詳細リスト.開始時間+" ～ " + 週間計画詳細リスト.終了時間
          timeContent.value =
            timeContent.value + cks52.kaishiJikan + t('label.wavy') + cks52.shuuryouJikan
        }
        // 変数.時間帯内容が空白ではない場合
        if (timeContent.value !== '') {
          // ・変数.時間帯内容最後の改行コードを除く
          timeContent.value.substring(
            0,
            timeContent.value.length - Or05349Const.DEFAULT.LINE_BREAK.length
          )
          // ・時間帯内容ラベル(該当時間帯)＝変数.時間帯内容
          wp.contents = timeContent.value
          // ※ 表示文字サイズなど情報はAC001-5参照
          // ・変数.時間帯内容＝空白
          timeContent.value = ''
          showData.value?.dataList.push(wp)
          data.value?.dataList.push(wp)
        }
      }
      // 週間計画詳細リスト.曜日の最後の一桁が（週単位）の場合
      else {
        // 共通情報.サービス職種IDor種類表示フラグが表示の場合
        if (localOneway.or05349.shuruihyoujiFlg) {
          // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.サービス種類名称が空白ではない場合
          if (local.hokenModel.value === '' && cks52.svShuruiKnj !== '') {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス種類名称+ ”(”
            hokenService.value = hokenService.value + cks52.svShuruiKnj + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス種類名称
            hokenService.value = hokenService.value + cks52.svShuruiKnj
          }
        }
        // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.週単位以外文字が空白ではない、且つ、週間計画詳細リスト.週単位以外文字が「2」ではない場合
        if (local.hokenModel.value === '' && cks52.igaiMoji !== '' && cks52.igaiMoji !== '2') {
          // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.週単位以外文字+ ”(”
          hokenService.value = hokenService.value + cks52.igaiMoji + t('label.left-bracket')
          // ・変数.保険編集フラグをTRUEにセットする。
          hokenEditFlg.value = true
        } else {
          // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.週単位以外文字
          hokenService.value = hokenService.value + cks52.igaiMoji
        }
        // 共通情報. 内容表示フラグが表示の場合
        if (localOneway.or05349.contentShowFlg) {
          // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.サービス項目名称が空白ではない、且つ、週間計画詳細リスト.時間表示区分が[2]の場合
          if (
            local.hokenModel.value === '' &&
            cks52.svItemKnj !== '' &&
            cks52.timeKbn === Or05349Const.DEFAULT.TIMD_DISP_KBN_LAST
          ) {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.内容+ ”(”
            hokenService.value = hokenService.value + cks52.naiyouKnj + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.内容
            hokenService.value = hokenService.value + cks52.naiyouKnj
          }
        }
        // 共通情報.事業者表示フラグが正式の場合
        if (localOneway.or05349.jigyoShowFlg === 0) {
          // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.サービス事業者名称が空白ではない場合
          if (local.hokenModel.value === '' && cks52.svJigyoKnj !== '') {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス事業者名称+ ”(”
            hokenService.value = hokenService.value + cks52.svJigyoKnj + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス事業者名称
            hokenService.value = hokenService.value + cks52.svJigyoKnj
          }
        }
        // 共通情報.事業者表示フラグが略称の場合
        if (localOneway.or05349.jigyoShowFlg === 1) {
          // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.サービス事業者略称が空白ではない場合
          if (local.hokenModel.value === '' && cks52.svJigyoRyaku !== '') {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス事業者略称+ ”(”
            hokenService.value = hokenService.value + cks52.svJigyoRyaku + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス事業者略称
            hokenService.value = hokenService.value + cks52.svJigyoRyaku
          }
        }
        // 共通情報.サービス項目表示フラグが表示の場合
        if (localOneway.or05349.komokuShowFlg) {
          // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.サービス項目名称が空白ではない場合
          if (local.hokenModel.value === '' && cks52.svItemKnj !== '') {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス項目名称+ ”(”
            hokenService.value = hokenService.value + cks52.svItemKnj + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス項目名称
            hokenService.value = hokenService.value + cks52.svItemKnj
          }
        }
        // 週間計画詳細リスト.時間表示区分が先頭表示の場合
        if (cks52.timeKbn === Or05349Const.DEFAULT.TIMD_DISP_KBN_FIRST) {
          // ・変数.保険のサービス＝週間計画詳細リスト.開始時間+" ～ " + 週間計画詳細リスト.終了時間 + 変数.保険のサービス
          hokenService.value =
            cks52.kaishiJikan + t('label.wavy') + cks52.shuuryouJikan + hokenService.value
        }
        // 週間計画詳細リスト.時間表示区分が最後表示の場合
        if (cks52.timeKbn === Or05349Const.DEFAULT.TIMD_DISP_KBN_LAST) {
          // ・変数.保険のサービス＝変数.保険のサービス + 週間計画詳細リスト.開始時間+" ～ " + 週間計画詳細リスト.終了時間
          hokenService.value =
            hokenService.value + cks52.kaishiJikan + t('label.wavy') + cks52.shuuryouJikan
        }
        // 変数.保険編集フラグがTRUEの場合
        if (hokenEditFlg.value) {
          // ・変数.保険のサービス＝変数.保険のサービス+")、"
          hokenService.value = hokenService.value + t('label.right-bracket') + t('label.comma')
          // ・変数.保険編集フラグをFALSEにセットする。
          hokenEditFlg.value = false
        } else {
          // ・変数.保険のサービス＝変数.保険のサービス+"、"
          hokenService.value = hokenService.value + t('label.comma')
        }
      }
    }
    // 時間帯表示の場合
    if (showData.value?.dataList.length > 0) {
      setCalendar(showData.value.dataList)
    }
    if (hokenService.value !== '') {
      // ・変数.保険のサービス最後の"、"を除く
      // ・保険のサービステキストエリア＝変数.保険のサービス
      local.hokenModel.value = hokenService.value.substring(0, hokenService.value.length - 1)
      local.or05349.hokenService = local.hokenModel.value
      // ・変数.保険のサービス＝空白
      hokenService.value = ''
    }
    // ・週単位以外のサービステキストエリア＝週間計画履歴.週単位以外サービス
    local.weeklyModel.value = local.or05349.rirekiObjDisp?.wIgaiKnj
    local.or05349.shuUnitOtherService = local.weeklyModel.value
    // 週間計画日常リストより繰り返し、主な日常生活内容ラベルを作成する。
    if (local.or05349.cks54List !== undefined && local.or05349.cks54List.length > 0) {
      let count = 1
      // 初期化
      local.njModelArr.splice(0)
      for (const cks54 of local.or05349.cks54List) {
        if (count === parseInt(cks54.seq)) {
          // ・主な日常生活内容ラベル＝週間計画日常リスト.主な日常生活上の活動
          local.njModelArr.push({ value: cks54.nichijoKnj })
        } else {
          local.njModelArr.push({ value: '' })
        }
        count = count + 1
      }
    }
    if (local.njModelArr.length < 24) {
      for (let i = local.njModelArr.length; i < 24; i++) {
        local.njModelArr.push({ value: '' })
      }
    }
  }
}

/**
 * 編集破棄ダイアログ表示
 */
function openInfoDialog() {
  // 確認ダイアログを初期化(i.cmn.10184)
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10184'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * AC025_「月間取込選択アイコンボタン」押下
 */
const ingestionClick = () => {
  let defSvJigyoId = ''
  // 共通情報.Sys略称が「CMN」の場合
  if (localOneway.or05349.sys3Ryaku === Or05349Const.DEFAULT.SYS_RYAKU_NM_CMN) {
    // 変数.事業所ID=共通情報.事業者ID
    defSvJigyoId = localOneway.or05349.defSvJigyoId ?? ''
  } else {
    // GUI01049_居宅介護支援事業者選択画面をポップアップで起動する
    // Or27734のダイアログ開閉状態を更新する
    Or27734Logic.state.set({
      uniqueCpId: or27734.value.uniqueCpId,
      state: { isOpen: true },
    })
    // ポップアップ画面の返却情報.事業所IDがNULLではない場合
    if (or27734Type.value.serviceOfficeId !== '') {
      defSvJigyoId = or27734Type.value.serviceOfficeId
    }
  }
  let gaitouYm = ''
  // 処理月が空白、或いはNULLの場合
  if (localOneway.or05349.syoriMonth === '') {
    // 変数.当該年月＝作成日
    gaitouYm = localOneway.or05349.createDate ?? ''
  } else {
    // 変数.当該年月＝処理月+"/01"
    gaitouYm = localOneway.or05349.syoriMonth + Or05349Const.DEFAULT.FIRST_DAY
  }
  // GUI01039_月間取込をポップアップで起動する
  or36086Data.isWeeklyFlg = true
  or36086Data.yymmYm = gaitouYm
  or36086Data.shienId = parseInt(defSvJigyoId)
  or36086Data.validPeriodId = validId.value
  or36086Type.value.clickMode = 1
  // Or27487のダイアログ開閉状態を更新する
  Or36086Logic.state.set({
    uniqueCpId: or36086.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC026_「予防計画選択アイコンボタン」押下
 */
const preventionClick = () => {
  // 変数.有効期間ID<4、且つ、親画面.計画書様式が居宅「2」の場合
  if (validId.value < 4 && localOneway.or05349.cksFlg === Or05349Const.DEFAULT.ITAKU) {
    // ■以下のメッセージを表示
    // id: i.cmn.10184
    // ・メッセージ内容
    // 平成21年4月以前のサービスコードは取り込むことができません。
    openInfoDialog()
    // OK：以降の処理を行わない。
    return
  }
  // 変数.週間計画履歴リスト.履歴総件数が0ではない場合
  if (local.or05349.rirekiObj !== undefined && local.or05349.rirekiObj.length > 0) {
    // GUI01040_予防計画書取込画面をポップアップで起動する
    if (plannningPeriodManageFlg.value === Or05349Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
      localOneway.or28809Data.sc1Id = '1'
    } else {
      localOneway.or28809Data.sc1Id = '2'
    }
    localOneway.or28809Data.seniKbn = Or05349Const.DEFAULT.NUMBER_ONE
    // Or28809のダイアログ開閉状態を更新する
    Or28809Logic.state.set({
      uniqueCpId: or28809.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 *  予防計画取込データを取得処理
 *
 * @param res - 返却情報
 */
async function getOr28809Value(res: {
  sc1Id: string
  kycTucPlan15List: {
    plan15Id: string
    plan11Id: string
    youbi: string
    igaiKbn: string
    igaiDate: string
    igaiWeek: string
    kaishiJikan: string
    shuuryouJikan: string
    svShuruiCd: string
    svItemCd: string
    svJigyoId: string
    tanka: string
    fygId: string
    gouseiSikKbn: string
    kasanFlg: string
    oyaLineNo: string
  }[]
  kycTucPlan16List: {
    plan16Id: string
    plan11Id: string
    plan15Id: string
    startYmd: string
    endYmd: string
    dmyPlan15Id: string
    dmyUniqId: string
  }[]
}) {
  // AC026-3の返却情報より、下記項目を追加。
  // バックエンドAPIから初期情報取得
  const inputData: WeekPlanImportInfoEditSelectEntity = {
    cksFlg: '',
    serviceList: [
      {
        syosaiId: '',
        kasanFlg: '',
        svItemCd: '',
        svJigyoId: '',
        kaishiJikan: '',
        shuuryouJikan: '',
        isKkak2SvId: '',
        svShuruiCd: '',
        svTani: '',
        fygId: '',
        youbi: '',
        igaiKbn: '',
        igaiDate: '',
        igaiWeek: '',
      },
    ],
    yearMonthList: [
      {
        syosaiId: '',
        startYmd: '',
        endYmd: '',
      },
    ],
    carePlanList: [
      {
        svShuKnj: '',
        jigyoNameKnj: '',
        naiyouKnj: '',
      },
    ],
    aiTermId: '',
    iiTermId: '',
    tougaiYm: '',
    ks51Id: '',
    sys3Ryaku: '',
  }
  // AC026-2の返却情報.サービスリスト
  for (const e of res.kycTucPlan15List) {
    const serv = {
      syosaiId: e.plan11Id,
      kasanFlg: e.kasanFlg,
      svItemCd: e.svItemCd,
      svJigyoId: e.svJigyoId,
      kaishiJikan: e.kaishiJikan,
      shuuryouJikan: e.shuuryouJikan,
      isKkak2SvId: e.plan15Id,
      svShuruiCd: e.svShuruiCd,
      svTani: e.tanka,
      fygId: e.fygId,
      youbi: e.youbi,
      igaiKbn: e.igaiKbn,
      igaiDate: e.igaiDate,
      igaiWeek: e.igaiWeek,
    }
    inputData.serviceList.push(serv)
  }
  // AC026-2の返却情報.月日リスト
  for (const e of res.kycTucPlan16List) {
    const yearData = {
      syosaiId: e.plan11Id,
      startYmd: e.startYmd,
      endYmd: e.endYmd,
    }
    inputData.yearMonthList.push(yearData)
  }
  // AC026-2の返却情報.有効期間ID
  inputData.aiTermId = res.sc1Id
  // 親画面.計画書様式
  inputData.cksFlg = localOneway.or05349.cksFlg ?? ''
  // 該当有効期間ID:変数.現在有効期間ID
  inputData.iiTermId = String(validId.value)
  // 画面.処理月
  inputData.tougaiYm = localOneway.or05349.syoriMonth ?? ''
  // 変数.履歴ID
  inputData.ks51Id = localOneway.or05349.rirekiId ?? ''
  // 親画面.Sys略称
  inputData.sys3Ryaku = localOneway.or05349.sys3Ryaku ?? ''
  // AC026-3の返却情報より、週間計画詳細リスト取得
  const resData: WeekPlanImportInfoEditSelectOutEntity = await ScreenRepository.select(
    'weekPlanImportInfoEditSelect',
    inputData
  )

  if (resData) {
    // 変数.週間計画詳細リスト＝計画対象期間情報.履歴変更情報.詳細情報.週間計画詳細リスト
    for (const e of resData.data.cks52List) {
      local.or05349.cks52List?.push(e)
    }
    if (local.or05349.cks52List !== undefined) {
      // 変数.週間計画担当リスト＝計画対象期間情報.履歴変更情報.詳細情報.週間計画担当リスト
      local.or05349.cks56List = [] as cks56List[]
      for (const cks52 of local.or05349.cks52List) {
        if (cks52.cks56List !== undefined) {
          for (const cks56 of cks52.cks56List) {
            local.or05349.cks56List.push(cks56)
          }
        }
      }
    }
  }

  // AC001-8から実行する。
  doContentSetting(local.or05349)
}

/**
 * AC027_「計画書選択アイコンボタン」押下
 */
const keikakusyoClick = () => {
  // 変数.有効期間ID<4、且つ、親画面.計画書様式が居宅「2」の場合
  if (validId.value < 4 && localOneway.or05349.cksFlg === Or05349Const.DEFAULT.ITAKU) {
    // ■以下のメッセージを表示
    // id: i.cmn.10184
    // ・メッセージ内容
    // 平成21年4月以前のサービスコードは取り込むことができません。
    openInfoDialog()
    // OK：以降の処理を行わない。
    return
  }
  // 変数.週間計画履歴リスト.履歴総件数が0ではない場合
  if (local.or05349.rirekiObj !== undefined && local.or05349.rirekiObj.length > 0) {
    // GUI01041_計画書（2）取込画面をポップアップで起動する
    or26447OnewayModel.seniMoto =
      plannningPeriodManageFlg.value === Or05349Const.DEFAULT.PLANNING_PERIOD_MANAGE ? '1' : '2'
    or26447OnewayModel.kikanFlg = ''
    or26447OnewayModel.cksFlg = ''
    or26447OnewayModel.torikomiMoto = ''
    or26447OnewayModel.torikomiModel = ''
    or26447OnewayModel.kaigoNo = ''
    or26447OnewayModel.planManage = ''
    or26447OnewayModel.office = ''
    or26447OnewayModel.termid = ''
    // Or26447のダイアログ開閉状態を更新する
    Or26447Logic.state.set({
      uniqueCpId: or26447.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * 計画書取込
 */
watch(
  () => local.or26447,
  async () => {
    // 計画書取込データを取得処理を行う
    // バックエンドAPIから初期情報取得
    const inputData: WeekPlanImportInfoEditSelectEntity = {
      cksFlg: '',
      serviceList: [
        {
          syosaiId: '',
          kasanFlg: '',
          svItemCd: '',
          svJigyoId: '',
          kaishiJikan: '',
          shuuryouJikan: '',
          isKkak2SvId: '',
          svShuruiCd: '',
          svTani: '',
          fygId: '',
          youbi: '',
          igaiKbn: '',
          igaiDate: '',
          igaiWeek: '',
        },
      ],
      yearMonthList: [
        {
          syosaiId: '',
          startYmd: '',
          endYmd: '',
        },
      ],
      carePlanList: [
        {
          svShuKnj: '',
          jigyoNameKnj: '',
          naiyouKnj: '',
        },
      ],
      aiTermId: '',
      iiTermId: '',
      tougaiYm: '',
      ks51Id: '',
      sys3Ryaku: '',
    }
    // AC027-2の返却情報.サービスリスト
    for (const e of local.or26447.careplan2ImportSelectList) {
      const serv = {
        syosaiId: e.ks22Id,
        kasanFlg: e.kasanFlg,
        svItemCd: e.svItemCd,
        svJigyoId: e.svJigyoId,
        kaishiJikan: e.kaishiJikan,
        shuuryouJikan: e.shuuryouJikan,
        isKkak2SvId: e.ks21Id,
        svShuruiCd: e.svShuruiCd,
        svTani: e.tanka,
        fygId: e.fygId,
        youbi: e.youbi,
        igaiKbn: e.igaiKbn,
        igaiDate: e.igaiDate,
        igaiWeek: e.igaiWeek,
      }
      inputData.serviceList.push(serv)
    }
    // AC026-2の返却情報.月日リスト
    for (const e of local.or26447.careplan2ImportSelectList) {
      const yearData = {
        syosaiId: e.ks221Id,
        startYmd: e.kaishiJikan,
        endYmd: e.shuuryouJikan,
      }
      inputData.yearMonthList.push(yearData)
    }
    // AC026-2の返却情報.有効期間ID
    // inputData.aiTermId = res.sc1Id
    // 親画面.計画書様式
    inputData.cksFlg = localOneway.or05349.cksFlg ?? ''
    // 該当有効期間ID:変数.現在有効期間ID
    inputData.iiTermId = String(validId.value)
    // 画面.処理月
    inputData.tougaiYm = localOneway.or05349.syoriMonth ?? ''
    // 変数.履歴ID
    inputData.ks51Id = localOneway.or05349.rirekiId ?? ''
    // 親画面.Sys略称
    inputData.sys3Ryaku = localOneway.or05349.sys3Ryaku ?? ''
    // AC026-3の返却情報より、週間計画詳細リスト取得
    const resData: WeekPlanImportInfoEditSelectOutEntity = await ScreenRepository.select(
      'weekPlanImportInfoEditSelect',
      inputData
    )

    if (resData) {
      // 変数.週間計画詳細リスト＝計画対象期間情報.履歴変更情報.詳細情報.週間計画詳細リスト
      for (const e of resData.data.cks52List) {
        local.or05349.cks52List?.push(e)
      }
      if (local.or05349.cks52List !== undefined) {
        // 変数.週間計画担当リスト＝計画対象期間情報.履歴変更情報.詳細情報.週間計画担当リスト
        local.or05349.cks56List = [] as cks56List[]
        for (const cks52 of local.or05349.cks52List) {
          if (cks52.cks56List !== undefined) {
            for (const cks56 of cks52.cks56List) {
              local.or05349.cks56List.push(cks56)
            }
          }
        }
      }
    }

    // AC001-8から実行する。
    doContentSetting(local.or05349)
  }
)

/**
 * AC028_「概算選択アイコンボタン」押下
 */
const budgetEstClick = () => {
  let syoriYm = ''
  // 変数.週間計画履歴リスト.当該年月が空白ではない場合
  if (
    local.or05349.rirekiObjDisp?.tougaiYm !== undefined &&
    local.or05349.rirekiObjDisp?.tougaiYm !== ''
  ) {
    // 変数.処理年月 = 変数.週間計画履歴リスト.当該年月
    syoriYm = local.or05349.rirekiObjDisp?.tougaiYm
  } else {
    if (local.or05349.rirekiObjDisp?.createYmd !== undefined) {
      // 変数.処理年月 = 変数.週間計画履歴リスト.作成日
      syoriYm = local.or05349.rirekiObjDisp?.createYmd
    }
  }
  // 変数.処理年月が空白の場合
  if (syoriYm === '') {
    syoriYm =
      systemCommonsStore.getSystemDate ??
      new Date().toLocaleDateString().replace(/-/g, Or05349Const.DEFAULT.SLASH)
  }
  // GUI01042_週間計画概算表示画面をポップアップで起動する
  localOneway.or27501OnewayModel.svJigyoId = localOneway.or05349.defSvJigyoId ?? ''
  localOneway.or27501OnewayModel.shoriYymm = syoriYm
  localOneway.or27501OnewayModel.termid = String(validId.value)
  localOneway.or27501OnewayModel.siyouFlg = localOneway.or05349.useFlg ?? ''
  localOneway.or27501OnewayModel.dwWeeks = local.or05349.cks52List as unknown as []
  localOneway.or27501OnewayModel.dwRiyou = local.or05349.cks55List as unknown as []
  localOneway.or27501OnewayModel.dwKakusyuu = local.or05349.cks58List as unknown as []
  localOneway.or27501OnewayModel.dsTsukihi = local.or05349.cks57List as unknown as []
  localOneway.or27501OnewayModel.userId = localOneway.or05349.userId ?? ''
  localOneway.or27501OnewayModel.caller = localOneway.or05349.openSourceJudgement ?? ''
  localOneway.or27501OnewayModel.torikomiKbn = localOneway.or05349.importCategory ?? ''
  // Or27501のダイアログ開閉状態を更新する
  Or27501Logic.state.set({
    uniqueCpId: or27501.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 時間帯内容設定
 *
 * @param dataList - 週間計画詳細リスト
 */
function setCalendar(dataList: WeeklyPlan[]) {
  if (dataList) {
    const eventList = []
    let index = 0
    for (const e of dataList) {
      // 計算週
      const weekArray: number[][] = calcWeek(e)
      for (const week of weekArray) {
        const etp = e.textPosition as Mo01282Type
        const event: OrX0048CalendarEvent = {
          // 日程ブロックID
          id: index++,
          // 日程開始時間
          start: e.startTime,
          // 日程終了時間
          end: e.endTime,
          // 日程ブロック背景色
          bgColor: e.background,
          // フォント色
          fontColor: e.color,
          // フォントサイズ
          fontSize: `${e.fontSize.modelValue}px`,
          // 日程内容エリア揃い方向
          align: etp.modelValue === '2' ? 'right' : etp.modelValue === '0' ? 'left' : 'center',
          // タイトル
          title: e.contents,
          // 曜日
          week: week[0],
          // 間隔日数
          period: week[1] > 1 ? week[1] : undefined,
          // 事業者
          insuranceBussiness: e.insuranceBussiness,
        }
        eventList.push(event)
      }
    }
    calendarConfig.value.events = eventList

    // カレンダーの画面を反映
    calendar.value?.reRenderCalendar()
  }
}

/**
 * AC029_カレンダータイムズームをダブルクリック
 */
function handleDoubleClickTimeAxis() {
  Or27610Logic.state.set({
    uniqueCpId: or27610.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  Or27610Logic.data.set({
    uniqueCpId: or27610.value.uniqueCpId,
    value: data.value.dataList,
  })
  console.log('handleDoubleClickTimeAxis', Or27610Logic.data.get(or27610.value.uniqueCpId))
}

/**
 * AC030_カレンダーをダブルクリック
 */
function handleDoubleClick() {
  Or27610Logic.data.set({
    uniqueCpId: or27610.value.uniqueCpId,
    value: data.value.dataList,
  })
  Or27610Logic.state.set({
    uniqueCpId: or27610.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  console.log('handleDoubleClick', Or27610Logic.data.get(or27610.value.uniqueCpId))
}

/**
 * 日程ダブルクリックイベント
 *
 * @param data1 - 日程ダブルクリックイベントの戻りパラメータ
 */
function handleDoubleClickEvent(data1: unknown) {
  console.log('handleDoubleClickEvent', data1)
  Or27610Logic.data.set({
    uniqueCpId: or27610.value.uniqueCpId,
    value: data.value.dataList,
  })
  Or27610Logic.state.set({
    uniqueCpId: or27610.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 計算週
 *
 * @param event - スケジュール
 */
function calcWeek(event: WeeklyPlan) {
  const arr: number[] = []
  if (event.dayOfWeek1) {
    arr.push(1)
  }
  if (event.dayOfWeek2) {
    arr.push(2)
  }
  if (event.dayOfWeek3) {
    arr.push(3)
  }
  if (event.dayOfWeek4) {
    arr.push(4)
  }
  if (event.dayOfWeek5) {
    arr.push(5)
  }
  if (event.dayOfWeek6) {
    arr.push(6)
  }
  if (event.dayOfWeek7) {
    arr.push(7)
  }
  if (arr.length === 0) return []

  const result = []
  let current = { start: arr[0], count: 1 }

  for (let i = 1; i < arr.length; i++) {
    if (arr[i] === arr[i - 1] + 1) {
      current.count++
    } else {
      result.push([current.start, current.count])
      current = { start: arr[i], count: 1 }
    }
  }

  result.push([current.start, current.count])
  return result
}

/**
 * AC031_「主な日常生活選択アイコンボタン」押下
 *
 * @param index - 選択した行のindex
 */
const openGui00937DialogAc031 = (index: number) => {
  selectedItemIndex.value = index
  localOneway.or51775OnewayModel.title = t('label.main-life-active')
  localOneway.or51775OnewayModel.t1Cd = '850'
  localOneway.or51775OnewayModel.t2Cd = '2'
  localOneway.or51775OnewayModel.t3Cd = '0'
  localOneway.or51775OnewayModel.tableName = 'cpn_tuc_cks54'
  localOneway.or51775OnewayModel.columnName = 'nichijo_knj'
  localOneway.or51775OnewayModel.userId = localOneway.or05349.userId ?? ''
  // GUI00937_入力支援［ケアマネ］画面をポップアップで起動する。
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC032_「週単位以外のサービスアイコンボタン」押下
 */
const openGui00937DialogAc032 = () => {
  localOneway.or51775OnewayModel.title = t('label.week-unit-other-service')
  localOneway.or51775OnewayModel.t1Cd = '850'
  localOneway.or51775OnewayModel.t2Cd = '2'
  localOneway.or51775OnewayModel.t3Cd = '0'
  localOneway.or51775OnewayModel.tableName = 'cpn_tuc_cks51'
  localOneway.or51775OnewayModel.columnName = 'w_igai_knj'
  localOneway.or51775OnewayModel.userId = localOneway.or05349.userId ?? ''
  // GUI00937_入力支援［ケアマネ］画面をポップアップで起動する。
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC033_「保険のサービスアイコンボタン」押下
 */
const onClickHokenService = () => {
  Or27610Logic.state.set({
    uniqueCpId: or27610.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// 現在のシステム年月の取得（yyyy/MM）
function getFormattedDate() {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, Or05349Const.DEFAULT.NUMBER_ZERO)
  return `${year}/${month}`
}

/**
 * 週間計画概算表示ポップアップの表示状態を返すComputed
 */
const showDialogOr27501 = computed(() => {
  return Or27501Logic.state.get(or27501.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 予防計画書取込ポップアップの表示状態を返すComputed
 */
const showDialogOr28809 = computed(() => {
  return Or28809Logic.state.get(or28809.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 計画書ポップアップの表示状態を返すComputed
 */
const showDialogOr26447 = computed(() => {
  return Or26447Logic.state.get(or26447.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 居宅介護支援事業者選択ポップアップの表示状態を返すComputed
 */
const showDialogOr27734 = computed(() => {
  return Or27734Logic.state.get(or27734.value.uniqueCpId)?.isOpen ?? false
})

// 月間取込ポップアップの表示状態を返すComputed
const showDialogOr36086 = computed(() => {
  // Or36086のダイアログ開閉状態
  return Or36086Logic.state.get(or36086.value.uniqueCpId)?.isOpen ?? false
})

// 週間計画入力ポップアップの表示状態を返すComputed
const showDialogOr27610 = computed(() => {
  // Or27610のダイアログ開閉状態
  return Or27610Logic.state.get(or27610.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
// 戻り値設定
const or51775Other = ref({ modelValue: '' })

/**
 * 確認ダイアログの表示状態を返すComputed
 */
const showDialogOr21814 = computed(() => {
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or05349Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or05349Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  if (localOneway.or51775OnewayModel.title === t('label.main-life-active')) {
    let tmpStr = ''
    let count = 1
    for (const str of local.njModelArr) {
      if (count === selectedItemIndex.value) {
        tmpStr = setOrAppendValue(str.value, data)
        break
      }
      count = count + 1
    }
    local.njModelArr[count - 1].value = tmpStr
  }
  if (localOneway.or51775OnewayModel.title === t('label.week-unit-other-service')) {
    local.weeklyModel.value = setOrAppendValue(local.weeklyModel.value ?? '', data)
    local.or05349.shuUnitOtherService = local.weeklyModel.value
  }
  localOneway.or51775OnewayModel.title = ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}
const handleOr27610Confirm = (weekPlan: WeeklyPlan[]) => {
  data.value.dataList = weekPlan
}
defineExpose({
  initData,
  doContentSetting,
})
</script>

<template>
  <c-v-sheet class="view d-flex flex-column h-100">
    <c-v-row
      no-gutters
      class="d-flex flex-0-1 h-100"
    >
      <c-v-col>
        <!-- 入力フォーム -->
        <c-v-row
          no-gutters
          class="d-flex flex-1-2 mt-4"
          style="padding-top: 8px"
        >
          <c-v-col v-show="isHistoryShow">
            <tr>
              <td style="padding-right: 8px">
                <c-v-row class="flex-0-0 w-auto">
                  <c-v-col>
                    <base-mo-00611
                      :oneway-model-value="localOneway.mo00611OnewayIngestion"
                      @click="ingestionClick"
                    >
                      <c-v-tooltip
                        activator="parent"
                        location="bottom"
                        :text="$t('tooltip.monthly-import')"
                    /></base-mo-00611>
                    <!-- 開く画面 GUI01049_居宅介護支援事業者選択画面 -->
                    <g-custom-or-27734
                      v-if="showDialogOr27734"
                      v-bind="or27734"
                      v-model="or27734Type"
                    />
                    <g-custom-or-36086
                      v-if="showDialogOr36086"
                      v-bind="or36086"
                      v-model="or36086Type"
                      :oneway-model-value="or36086Data"
                    />
                  </c-v-col>
                </c-v-row>
              </td>
              <td
                v-show="
                  localOneway.or05349.jiOffice !== Or05349Const.DEFAULT.KAIGO_YOBOU_SHIEN_JIMUSYO
                "
                style="padding-right: 8px"
              >
                <c-v-row class="officeSelect flex-0-0 w-auto">
                  <c-v-col>
                    <base-mo-00611
                      :oneway-model-value="localOneway.mo00611OnewayPrevention"
                      @click="preventionClick"
                    >
                      <c-v-tooltip
                        activator="parent"
                        location="bottom"
                        :text="$t('tooltip.prevent-care-plant')"
                    /></base-mo-00611>
                    <!-- 開く画面 GUI01040_予防計画書取込画面 -->
                    <g-custom-or-28809
                      v-if="showDialogOr28809"
                      v-bind="or28809"
                      :oneway-model-value="localOneway.or28809Data"
                      @update:model-value="getOr28809Value"
                    />
                  </c-v-col>
                </c-v-row>
              </td>
              <td style="padding-right: 8px">
                <c-v-row class="officeSelect flex-0-0 w-auto">
                  <c-v-col>
                    <base-mo-00611
                      :oneway-model-value="localOneway.mo00611OnewayPlan"
                      @click="keikakusyoClick"
                    >
                      <c-v-tooltip
                        activator="parent"
                        location="bottom"
                        :text="$t('tooltip.care-plan')"
                    /></base-mo-00611>
                    <g-custom-or-26447
                      v-if="showDialogOr26447"
                      v-bind="or26447"
                      v-model="local.or26447"
                      :oneway-model-value="or26447OnewayModel"
                    />
                  </c-v-col>
                </c-v-row>
              </td>
              <td style="padding-right: 8px">
                <c-v-row class="officeSelect flex-0-0 w-auto">
                  <c-v-col>
                    <base-mo-00611
                      :oneway-model-value="localOneway.mo00611OnewayBudgetEst"
                      @click="budgetEstClick"
                    >
                      <c-v-tooltip
                        activator="parent"
                        location="bottom"
                        :text="$t('tooltip.estimate')"
                    /></base-mo-00611>
                    <!-- 開く画面 GUI01042_週間計画概算画面 -->
                    <g-custom-or-27501
                      v-if="showDialogOr27501"
                      v-bind="or27501"
                      v-model="or27501Type"
                      :oneway-model-value="localOneway.or27501OnewayModel"
                    />
                  </c-v-col>
                </c-v-row>
              </td>
            </tr>
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-row
            v-show="localOneway.or05349.periodTotalCount !== 0"
            no-gutters
            class="flex-1-3"
            style="margin-top: 19px;"
          >
            <c-v-row class="officeSelect">
              <div>
                <div class="calendar-container">
                  <g-custom-or-x-0048
                    ref="calendar"
                    :calendar-config="calendarConfig"
                    v-bind="orX0048"
                    @double-click="handleDoubleClick"
                    @double-click-event="handleDoubleClickEvent"
                    @on-time-axis-dbl-click="handleDoubleClickTimeAxis"
                  ></g-custom-or-x-0048>
                </div>
                <!-- Or27610: 週間計画入力  -->
                <g-custom-or-27610
                  v-if="showDialogOr27610"
                  v-bind="or27610"
                  :oneway-model-value="localOneway.or27610Oneway"
                  @confirm="handleOr27610Confirm"
                />
              </div>
              <div style="width: 219px">
                <div>
                  <div
                    class="grid-header calendar-container"
                    style="height: 25px !important"
                  >
                    <c-v-row class="officeSelect">
                      <c-v-col style="min-width: 100%">
                        {{ t('label.main-life-content') }}
                      </c-v-col>
                    </c-v-row>
                  </div>
                </div>
                <div
                  v-for="hour in hourSlots"
                  :key="hour"
                  class="time-cell calendar-container"
                  :class="{ 'select-row': selectedItemIndex === parseInt(hour) + 1 }"
                >
                  <!-- 入力内容設定反映 -->
                  <g-custom-or-x-0172
                    v-model="local.njModelArr[parseInt(hour)]"
                    :oneway-model-value="localOneway.orX0172Oneway"
                    @on-click-edit-btn="openGui00937DialogAc031(parseInt(hour) + 1)"
                  ></g-custom-or-x-0172>
                </div>
              </div>
            </c-v-row>
          </c-v-row>
          <c-v-row no-gutters style="margin-left: 24px">
            <c-v-row
              no-gutters
              class="flex-1-4 mt-6 mb-2"
            >
              <c-v-row class="officeSelect">
                <c-v-col style="width: 1280px">{{ t('label.week-unit-other-sevice') }}</c-v-col>
              </c-v-row>
            </c-v-row>
            <c-v-row
              no-gutters
              class="flex-1-5"
            >
              <g-custom-or-x-0156
                v-model="local.weeklyModel"
                :oneway-model-value="localOneway.orX0156OnewayWeekly"
                style="width: 1280px"
                @on-click-edit-btn="openGui00937DialogAc032"
              >
              </g-custom-or-x-0156>
            </c-v-row>
          </c-v-row>
          <c-v-row
            no-gutters style="margin-left: 24px"
          >
            <c-v-row
              no-gutters
              class="flex-1-6 mt-6 mb-2"
            >
              <c-v-row class="officeSelect">
                <c-v-col style="width: 1280px">{{ t('label.hoken-sevice') }} </c-v-col>
              </c-v-row>
            </c-v-row>
            <c-v-row
              no-gutters
              class="flex-1-7"
            >
              <g-custom-or-x-0156
                v-model="local.hokenModel"
                :oneway-model-value="localOneway.orX0156OnewayHoken"
                style="width: 1280px"
                @on-click-edit-btn="onClickHokenService"
              >
              </g-custom-or-x-0156>
            </c-v-row>
          </c-v-row>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <!-- 確認イアログ -->
    <g-base-or21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    />
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="or51775Other"
      :oneway-model-value="localOneway.or51775OnewayModel"
      @confirm="handleOr51775Confirm"
    />
  </c-v-sheet>
</template>

<style scoped lang="scss">
/* 主な日常生活一覧タイトル */
.grid-header {
  display: flex;
  height: 23px;
  border: 1px solid #ddd;
  background: #f2f2f2 !important;
  border-left: none;
  box-sizing: border-box;
  text-align: center;
  flex-direction: column;
  justify-content: center;
}

/* 主な日常生活一覧セル */
.time-cell {
  background: #fff;
  height: var(--custom-calendar-cell-height, 27px);
  border-right: 1px solid #ddd;
  box-sizing: border-box;
}
.time-cell:nth-child(even) {
  border-bottom: 1px dotted #ddd;
}
.time-cell:nth-child(odd) {
  border-bottom: 1px solid #ddd;
}

.officeSelect {
  margin: unset;

  .v-col {
    max-width: unset;
    width: auto;
    flex: 0 1 auto;
    padding: 0;
    align-self: center;
    :deep(label) {
      line-height: 2rem !important;
    }
  }
}
:deep(.create_width .d-flex) {
  width: 140px !important;
}

:deep(.syori_width .d-flex) {
  width: 110px !important;
}

.print {
  cursor: pointer;
}

.font-color-red {
  color: rgb(217, 2, 20);
}
.text-center {
  align-items: baseline;
}
.multiline-text {
  white-space: pre-line;
  text-align: left;
}

:deep(.v-field__input) {
  height: 46px !important;
}

:deep(.v-btn) {
  min-height: 32px !important;
  max-height: 32px !important;
}
</style>
