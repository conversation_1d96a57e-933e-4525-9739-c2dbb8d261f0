/**
 * OrX0096：アセスメント（包括）画面_要介護者などの健康上や生活上の問題点及び解決すべき課題等
 *
 * @description
 *
 * アセスメント（包括）画面_要介護者などの健康上や生活上の問題点及び解決すべき課題等
 *
 * <AUTHOR>
 */

/**
 * 課題全般リストタイプ
 */
export interface IssuseWholeItemType {
  [key: string]: string | string[] | boolean | undefined
  /**
   * 表示用ラベル
   */
  label: string

  /**
   * 課題ありチェック用フラグ
   */
  isPlanningFlg: boolean

  /**
   * 立案判定チェック用フラグ
   */
  isHaveIssuesFlg: boolean

  /**
   * 表示させる画面ID
   */
  showScreenId: string[]

  /**
   * バッグエンド用唯一のキー
   */
  b4cd: string
  /**
   * cc31Id
   */
  cc31Id: string
}

/**
 * 双方向バインドモーダル
 */
export interface OrX0096Type {
  /**
   * レンダリング用データ
   */
  listSection: IssuseWholeItemType[]
  /**
   * 医学意見
   */
  thingsToKeepInMindContentsInfo?: ThingsToKeepInMindContentsInfoType
  /**
   * 具体的内容
   */
  concreteContentList?: ConcreteContentType[]
  /**
   * 対応するケア項目
   */
  concreteCareItemList?: ConcreteCareItemType[]
}

/**
 * 具体的内容タイプ
 */
export interface ConcreteContentType {
  /**
   * データ構造用唯一のキー
   */
  key: string

  /**
   * 対応する課題唯一のキー
   */
  correspondenceKeys: {
    /**
     * 対応する具体的内容ID
     */
    cc32Id: string

    /**
     * 問題点コード
     */
    b4Cd: string

    /**
     * 順位
     */
    seq: string
    /**
     * カウンター
     */
    cc34Id: string
    /**
     * 大分類CD
     */
    b1Cd: string
  }[]

  /**
   * 具体的内容
   */
  content: string

  /**
   * カウンター
   */
  cc32Id: string

  /**
   * 優先順位
   */
  juni: string | null
  /**
   * 大分類CD
   */
  b1Cd: string
  /**
   * 表示順
   */
  seq: string
  /**
   * 種類
   */
  cc32Type: string
  /**
   * 入力支援ID
   */
  ci1Id: string
  /**
   * dmyB4Cd
   */
  dmyB4Cd: { b4Cd: string }[]
  /**
   * 問題点CD
   */
  b4Cd: string
  /**
   * 番号リスト
   */
  number: string
  /**
   * 更新区分
   */
  updateKbn: string
}

/**
 * 対応するケア項目タイプ
 */
export interface ConcreteCareItemType {
  /**
   * データ構造用唯一のキー
   */
  key: string

  /**
   * 具体的内容
   */
  content: string

  /**
   * 対応する具体的内容カウンター
   */
  dmyCc32Id?: string

  /**
   * cc33cd
   */
  cc33Id: string

  /**
   * 大分類CD
   */
  b1Cd: string
  /**
   * 対応する具体的内容のID
   */
  cc32Id: string
  /**
   * 表示順
   */
  seq: string
  /**
   * 入力支援（対応するケア）ID
   */
  ci2Id: string
  /**
   * 更新区分
   */
  updateKbn: string
}
/**
 * onewayタイプ
 */
export interface OrX00096OnewayType {
  /**
   * 医療タブ用テキストフィールド表示フラグ
   */
  showInputFlg: boolean
  /**
   * 事業所ID
   */
  officeId: string
  /**
   * テーブル表示フラグ「削除用」
   */
  tableDisplayFlg: boolean
  /**
   * 該当タブの最大カウント数
   */
  maxCount: number
  /**
   * 該当タブの大分類CD
   */
  b1Cd: string
}
/**
 * 医学的意見タイプ
 */
export interface ThingsToKeepInMindContentsInfoType {
  /**
   * カウンター
   */
  cc32Id: string

  /**
   * 大分類CD
   */
  b1Cd: string

  /**
   * 内容
   */
  memoKnj: string

  /**
   * 表示順
   */
  seq: string

  /**
   * 優先順位
   */
  juni: string

  /**
   * 種類
   */
  cc32Type: string

  /**
   * 入力支援（具体的内容）id
   */
  ci1Id: string
}
