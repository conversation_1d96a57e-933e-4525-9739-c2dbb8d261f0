import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'
// （職員管理）職員編集ダイアログの保存ボタン押下時のリクエスト用エンティティ

/**
  Or41364 save入力エンティティ
 */
export interface Or41364SaveInEntity extends InWebEntity {
  /**
   * 入力パラメータ
   */
  parameters?: staffEditItemType
}

/**
  Or41364 save出力エンティティ
 */
export interface Or41364SaveOutEntity extends OutWebEntity {
  /**
   * data
   */
  data: object
}

/**
 * 職員編集ダイアログの入力アイテム
 */
export interface staffEditItemType {
  /**
   * ログインユーザーID
   */
  loginUserId: string

  /**
   * 新しいパスワード
   */
  newLoginUserPwd: string
}
