<script setup lang="ts">
/**
 * Or05062:(見通し)行のアクションボタン
 * GUI00916_見通し
 *
 * @description
 * (見通し)行のアクションボタン
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { useI18n } from 'vue-i18n'
import { reactive } from 'vue'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: {
    selectedIndex: number
    totalLine: number
  }
}
const props = defineProps<Props>()
/**
 * useI18n
 */
const { t } = useI18n()
const emit = defineEmits(['click'])
/**
 *  ローカルOneway
 */
const localOneway = reactive({
  mo00009OnewayAssessment: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-assessment-import-screen'),
  } as Mo00009OnewayType,
  mo00009OnewayStatusFactReference: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-status-fact-reference-screen'),
  } as Mo00009OnewayType,
  mo00009OnewayDisplaysTheNotesScreen: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-notes-screen'),
  } as Mo00009OnewayType,
  mo00009OnewaySortby: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.display-order-dialog'),
  } as Mo00009OnewayType,
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    tooltipText: t('tooltip.add-row'),
  } as Mo00611OnewayType,
  mo00611OnewayInsert: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    tooltipText: t('tooltip.insert-row'),
  } as Mo00611OnewayType,
  mo00611OnewayDuplicate: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'content_copy',
    tooltipText: t('tooltip.duplicate-row'),
  } as Mo00611OnewayType,
  mo01265OnewayDelete: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    tooltipText: t('tooltip.delete-row'),
  } as Mo01265OnewayType,
  or28285Oneway: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.care-plan2-select-next'),
  } as Mo00009OnewayType,
})

// 留意する項目：単方向モデル

function onClick(type: string) {
  emit('click', type)
}
</script>

<template>
  <div
    class="d-flex justify-space-between"
    style="padding-right: 8px"
  >
    <div>
      <base-mo-00611
        :oneway-model-value="localOneway.mo00611OnewayAdd"
        class="mr-1"
        @click.stop="onClick('add')"
      />
      <base-mo-00611
        :oneway-model-value="localOneway.mo00611OnewayInsert"
        class="mx-1"
        @click.stop="onClick('insert')"
      />
      <base-mo-00611
        :oneway-model-value="localOneway.mo00611OnewayDuplicate"
        class="mx-1"
        @click.stop="onClick('duplicate')"
      />
      <!-- 分子：優先度2破壊ボタン -->
      <base-mo01265
        :oneway-model-value="localOneway.mo01265OnewayDelete"
        class="mx-1"
        @click.stop="onClick('delete')"
      />
      <base-mo-00611
        :oneway-model-value="{
          btnLabel: t('label.sort-by'),
        }"
        class="mx-1"
      />
      <base-mo-00611
        :oneway-model-value="{
          btnLabel: t('label.assessment'),
          tooltipText: t('tooltip.display-order-dialog'),
        }"
        class="mx-1"
        @click.stop="onClick('sortby')"
      />
      <base-mo-00611
        :oneway-model-value="{
          btnLabel: t('label.see-facts-of-the-situation'),
          tooltipText: t('tooltip.displays-the-assessment-import-screen'),
        }"
        class="mx-1"
        @click.stop="onClick('assessment')"
      />
      {{ props.modelValue.totalLine + t('label.page-items') }}
    </div>
    <div class="d-flex align-center"></div>
  </div>
</template>
<style scoped>
.border-left {
  border-left: 1px solid rgba(var(--v-theme-black-300));
  border-radius: 0 !important;
  align-self: center;
}
</style>
