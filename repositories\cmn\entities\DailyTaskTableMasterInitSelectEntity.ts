import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'
/**
 *日課表マスタ初期情報エンティティ
 */
export interface DailyTaskTableMasterInitSelectInEntity extends InWebEntity {
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事業所ID
   */
  svJigyoId: string
  /**
   * 適用事業所IDリスト
   */
  svJigyoIdList: string[]
}

/**
 *日課表マスタ初期情報エンティティ
 */
export interface DailyTaskTableMasterInitSelectOutEntity extends OutWebEntity {
  /**
   * data
   */
  data: {
    /**
     * メモ
     */
    memo: string
    /**
     * メモ分類3
     */
    memoBunrui3: string
    /**
     * メモ更新回数
     */
    memoModifiedCnt: string
    /**
     * 時間（初期値）
     */
    timeInitial: string
    /**
     * 時間（初期値）分類3
     */
    timeInitialBunrui3: string
    /**
     * 時間（初期値）更新回数
     */
    timeInitialModifiedCnt: string
    /**
     * 適用事業所一覧情報
     */
    svJigyoInfoList: SvJigyoInfo[]
  }
}
/**
 * 適用事業所情報
 */
export interface SvJigyoInfo {
  /**
   * 事業所ID
   */
  jigyoId: string

  /**
   * 事業名
   */
  jigyoKnj: string

  /**
   * 事業名（略称）
   */
  jigyoRyakuKnj: string
}
