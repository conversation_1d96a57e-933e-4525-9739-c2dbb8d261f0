/**
 * Or10577:有機体:'(共通サービスマスタ)ダイアログ
 * GUI01055_共通サービスマスタ
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { InWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * GUI01055_共通サービスマスタ処理の取得APIモック
 *
 * @description
 * GUI01055_共通サービスマスタ処理のメイン画面に表示されるデータを返却する。
 * dataName："carePlanOneInitSelect"
 */
export function handler(inEntity: InWebEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
