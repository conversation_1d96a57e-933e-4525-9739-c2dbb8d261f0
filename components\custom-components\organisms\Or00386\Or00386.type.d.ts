/**
 * Or00386:有機体:［アセスメント］画面（居宅）（4）
 * GUI00797_［アセスメント］画面（居宅）（4）
 *
 * @description
 * ［アセスメント］画面（居宅）（4）
 *
 * <AUTHOR>
 */

import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
/**
 *
 * 状態管理タイプ
 *
 */

/**
 * stateタイプ
 */
export interface Or00386StateType {
  /**
   * 更新発火フラグ
   */
  isSave: boolean
  /**
   * 再更新発火フラグ
   */
  isRefresh: boolean
}

/**
 * チェックボックスタイプ
 */
interface CheckboxType {
  check: Mo00018Type
  label: string
  onewayModelValue: Mo00018OnewayType
}

/**
 * Localタイプ
 */
interface Mo00045Type {
  value: string
}

interface Or00386Type {
  /** インデックス */
  [key: string]: string | Mo00045Type
  /** 1戸建て集合住宅 */
  houseShu: string
  /** 賃貸所有給与住宅等 */
  shoyuShu: string
  /** その他(ﾒﾓ 賃貸所有給与住宅等） */
  shoyuMemoKnj: Mo00045Type
  /** 専用居室の有無 */
  kyoUmu: string
  /** 階数 */
  kyoKaisuKnj: Mo00045Type
  /** エレベーターの有無 */
  elevatorUmu: string
  /** 寝具種類 */
  sleepWhere: string
  /** その他（ベッドの種類） */
  bedMemoKnj: Mo00045Type
  /** 陽当り */
  hiatari: string
  /** 暖房 */
  heater: string
  /** 冷房 */
  airCooling: string
  /** その他（トイレ式） */
  toilMemoKnj: Mo00045Type
  /** 手すり有無（ﾄｲﾚ） */
  toilTesuriUmu: string
  /** 段差の有無(トイレ) */
  toilDansaUmu: string
  /** (自宅に）浴槽の有無 */
  bathUmu: string
  /** 手すりの有無（浴槽） */
  bathTesuriUmu: string
  /** 段差の有無（浴槽） */
  bathDansaUmu: string
  /** 福祉機器（室外）使用状態 */
  hukuOutUse: string
  /** その他(福祉機械（室外）） */
  outMemoKnj: Mo00045Type
  /** 福祉機器（室内）使用状態 */
  hukuInUse: string
  /** その他(福祉機械（室内）） */
  inMemoKnj: Mo00045Type
  /** 洗濯機 */
  setsubi1: string
  /** 湯沸器 */
  setsubi2: string
  /** 冷蔵庫 */
  setsubi3: string
  /** 調理器具 */
  setsubi4: string
  /** その他（暖房器具） */
  setsubi5Shu4MemoKnj: Mo00045Type
  /** 特記事項 */
  memoKnj: Mo00045Type
  /** 1階 */
  kyoKai1: string
  /** 2階 */
  kyoKai2: string
  /** その他（2階以上） */
  kyoKai3: string
  /** ベッドの種類1（固定） */
  bedShu1: string

  /** ベッドの種類2（ギャッチ） */
  bedShu2: string

  /** ベッドの種類3（電動） */
  bedShu3: string

  /** ベッドの種類4（その他） */
  bedShu4: string

  /** 和式 */
  toilShu1: string

  /** 洋式 */
  toilShu2: string

  /** その他 */
  toilShu3: string

  /** 福祉機器（室外）1 */
  hukuOut1: string

  /** 福祉機器（室外）2 */
  hukuOut2: string

  /** 福祉機器（室外）3 */
  hukuOut3: string

  /** 福祉機器（室外）4 */
  hukuOut4: string

  /** 福祉機器（室内）1 */
  hukuIn1: string

  /** 福祉機器（室内）2 */
  hukuIn2: string

  /** 福祉機器（室内）3 */
  hukuIn3: string

  /** 福祉機器（室内）4 */
  hukuIn4: string

  /** 福祉機器（室内）5 */
  hukuIn5: string

  /** 洗濯機 */
  setsubi1: string

  /** 湯沸器 */
  setsubi2: string

  /** 冷蔵庫 */
  setsubi3: string

  /** 立地環境上の問題の有 */
  mondaiUmu1: string

  /** 立地環境上の問題の無 */
  mondaiUmu2: string

  /** 暖房器具（ガス） */
  setsubi5Shu1: string

  /** 暖房器具（電気） */
  setsubi5Shu2: string

  /** 暖房器具（灯油） */
  setsubi5Shu3: string

  /** 暖房器具（その他） */
  setsubi5Shu4: string
}
