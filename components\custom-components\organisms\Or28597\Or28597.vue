<script setup lang="ts">
/**
 * Or28597:有機体:［履歴選択］画面 実施計画～①
 * GUI00952_［履歴選択］画面 実施計画～①
 *
 * @description
 * ［履歴選択］画面 実施計画～①の処理
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Or28597SelectTableDataItem, Or28597StateType, DataTableData } from './Or28597.type'
import { Or28597Const } from './Or28597.constants'
import { useScreenOneWayBind } from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or28597Type, Or28597OnewayType } from '~/types/cmn/business/components/Or28597Type'
import type {
  ImplementPlanOneHistoryInformationSelectInEntity,
  ImplementPlanOneHistoryInformationSelectOutEntity,
} from '~/repositories/cmn/entities/ImplementPlanOneHistoryInformationSelectEntity'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or28597Type
  onewayModelValue: Or28597OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const localOneway = reactive({
  or28597Oneway: {
    ...props.onewayModelValue,
  } as Or28597OnewayType,
  // 「履歴選択」ダイアログ
  mo00024Oneway: {
    width: '600px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.history-select'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // フェースシート(パッケージ)の備考
  kaiteiOneWay: { masterList: [] as CodeType[] },
  // 履歴選択情報データテーブルのヘッダー
  historyTableHeaders: [
    // 作成日
    {
      title: t('label.create-date'), // ヘッダーに表示される名称
      key: 'createYmd',
      sortable: false,
      width: 150,
    },
    // 作成者
    {
      title: t('label.author'), // ヘッダーに表示される名称
      key: 'createdUser',
      sortable: false,
      width: 250,
    },
    // ケース番号
    {
      title: t('label.caseNo'), // ヘッダーに表示される名称
      key: 'caseNo',
      sortable: false,
      width: 150,
    },
    // 改訂
    {
      title: t('label.revision'), // ヘッダーに表示される名称
      key: 'cstName',
      sortable: false,
      width: 150,
    },
  ],
})

const local = reactive({
  or28597: {
    ...props.modelValue,
  } as Or28597Type,
  items: [] as Or28597Type[],
})

/**
 * DataTableのデータ
 */
const historyInfo = ref<DataTableData>({
  items: [] as {
    createdId: number
    createYmd: string
    createUser: string
    caseNo: string
    cstName: string
  }[],
})

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28597Const.DEFAULT.IS_OPEN,
})

// 履歴選択情報選択行データ設定
const historySelectedItem = ref<Or28597SelectTableDataItem>()

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28597StateType>({
  cpId: Or28597Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28597Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 共通関数より汎用コード一覧を取得する。
  await initCodes()
  // 初期情報取得
  await getInitDataInfo()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 終了区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FACESHEET_PACKAGE_PREPARATION },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 終了区分
  localOneway.kaiteiOneWay.masterList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_FACESHEET_PACKAGE_PREPARATION
  )
}

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // 初期情報取得(IN)
  const inputData: ImplementPlanOneHistoryInformationSelectInEntity = {
    svJigyoId: localOneway.or28597Oneway.officeId,
    userId: localOneway.or28597Oneway.userId,
    sc1Id: localOneway.or28597Oneway.sc1Id.toString(),
  }

  const res: ImplementPlanOneHistoryInformationSelectOutEntity = await ScreenRepository.select(
    'implementPlanOneHistoryInformationSelect',
    inputData
  )
  const resData = res.data
  const selectHistoryId = local.or28597.historyId

  // データ情報設定
  if (resData && resData.rirekiList.length > 0) {
    local.items = resData.rirekiList.map((item, index) => {
      return {
        ...item,
        historyId: item.kkak21Id,
        currentIndex: resData.rirekiList.length - index,
        totalCount: resData.rirekiList.length,
      }
    })
    // 履歴選択情報設定
    for (const data of resData.rirekiList) {
      historyInfo.value.items.push({
        createdId: Number(data.kkak21Id),
        createYmd: data.createYmd,
        createUser: data.shokuName,
        caseNo: data.caseNo,
        cstName:
          localOneway.kaiteiOneWay.masterList.find((x) => x.value === data.kaiteiFlg)?.label ?? '',
      })
    }
    if (selectHistoryId === undefined || Number(selectHistoryId) <= 0) {
      historySelectedItem.value = historyInfo.value.items[0]
    }
  }
  // 履歴選択情報があり場合
  if (selectHistoryId) {
    const selectedItem = historyInfo.value.items.find(
      (item) => item.createdId === Number(selectHistoryId)
    )
    if (selectedItem) {
      historySelectedItem.value = selectedItem
    } else {
      historySelectedItem.value = historyInfo.value.items[0]
    }
  }
}

/**
 * 履歴情報選択行
 *
 * @param item - 履歴情報選択行
 */
const historyRowClick = (item: Or28597SelectTableDataItem) => {
  historySelectedItem.value = item
}

/**
 * 履歴情報選択行設定様式
 *
 * @param item - 履歴情報選択行
 */
const historyIsSelected = (item: { createdId: null }) =>
  historySelectedItem.value?.createdId === item.createdId

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // 履歴データがない場合、操作なし
  if (!historySelectedItem.value) return
  //選択された履歴IDを設定
  local.or28597 =
    local.items.find((item) => item.kkak21Id === historySelectedItem.value?.createdId.toString()) ??
    ({} as Or28597Type)

  // 選択情報値戻り
  emit('update:modelValue', local.or28597)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <!-- 履歴選択一覧 -->
      <c-v-data-table
        :items="historyInfo.items"
        :headers="localOneway.historyTableHeaders"
        hide-default-footer
        class="table-wrapper"
        :items-per-page="-1"
        hover
        fixed-header
      >
        <template #item="{ item }">
          <tr
            :class="{ 'select-row': historyIsSelected(item) }"
            @click="historyRowClick(item)"
          >
            <td>
              {{ item.createYmd }}
            </td>
            <td>
              {{ item.createUser }}
            </td>
            <td>
              {{ item.caseNo }}
            </td>
            <td>
              {{ item.cstName }}
            </td>
          </tr>
        </template>
        <template #bottom />
      </c-v-data-table>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway"
          class="ml-2"
          @click="onClickCloseBtn"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.screen-close')"
          />
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          class="mx-2"
          :oneway-model-value="mo00609Oneway"
          @click="onConfirmBtn"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.confirm')"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';

/**************************************************
 * スクロール
 **************************************************/
.table-wrapper :deep(.v-table__wrapper) {
  overflow-y: auto;
  overflow-x: hidden;
  height: 326px;
}
.number-align {
  text-align: right;
}
</style>
