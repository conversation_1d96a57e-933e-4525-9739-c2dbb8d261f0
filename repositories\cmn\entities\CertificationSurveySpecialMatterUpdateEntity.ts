/**
 * Or26426のエンティティ
 * GUI01272_認定調査票特記事項
 *
 * <AUTHOR>
 */
import type { InWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 週間表マスタ情報入力エンティティ
 */
export interface CertificationSurveySpecialMatterUpdateInEntity extends InWebEntity {
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * 調査票ID
   */
  cschId: string
  /**
   * 特記事項内容リスト
   */
  nTokkiList: NTokkiUpdList[]
}

/**
 * 特記事項内容リスト
 */
export interface NTokkiUpdList {
  /**
   * カウンター
   */
  counter: string
  /**
   * 認定（特記：大）
   */
  n1tCd: string
  /**
   * 認定（特記：小）
   */
  n2tCd: string
  /**
   * 特記事項
   */
  memoKnj: string
  /**
   * 表示順
   */
  seqNo: string
  /**
   * 更新区分
   */
  updateKbn: string
}
