<script setup lang="ts">
import { computed, ref,reactive,watch } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or26421Const } from '~/components/custom-components/organisms/Or26421/Or26421.constants'
import { Or26421Logic } from '~/components/custom-components/organisms/Or26421/Or26421.logic'
import type { Or26421Type, Or26421OnewayType } from '~/types/cmn/business/components/Or26421Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**
 * GUI01187_入力支援［担当ケアマネ検索］画面
 *
 * @description
 * 入力支援［担当ケアマネ検索］画面を表示
 *
 * <AUTHOR> 劉顕康
 */

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01187'
// ルーティング
const routing = 'GUI01187/pinia'
// 画面物理名
const screenName = 'GUI01187'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or26421 = ref({ uniqueCpId: Or26421Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01187' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or26421Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or26421.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01187',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26421Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or26421Const.CP_ID(1)]: or26421.value,
})

// ダイアログ表示フラグ
const showDialogOr26421 = computed(() => {
  // Or26421のダイアログ開閉状態
  return Or26421Logic.state.get(or26421.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or26421) 事業所
 */
function onClickOr26421p1() {
  // 項目区分
  or26421Data.koumokuKbn = '30'
  // 項目選択必須フラグ
  or26421Data.itemSelectRequiredFlg = '1'

  // Or26421のダイアログ開閉状態を更新する
  Or26421Logic.state.set({
    uniqueCpId: or26421.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理(Or26421) 職種
 */
function onClickOr26421p2() {
  // 項目区分
  or26421Data.koumokuKbn = '40'
  // 項目選択必須フラグ
  or26421Data.itemSelectRequiredFlg = '0'

  // Or26421のダイアログ開閉状態を更新する
  Or26421Logic.state.set({
    uniqueCpId: or26421.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理(Or26421) 資格免許
 */
function onClickOr26421p3() {
  // 項目区分
  or26421Data.koumokuKbn = '50'
  // 項目選択必須フラグ
  or26421Data.itemSelectRequiredFlg = '1'

  // Or26421のダイアログ開閉状態を更新する
  Or26421Logic.state.set({
    uniqueCpId: or26421.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or26421Type = ref<Or26421Type>({ selectedIdList: [] })

const or26421Data: Or26421OnewayType = {
  // 処理モード
  processMode: '1',
  // キーカラム
  keyColumn: 'id',
  // 選択された項目のID
  selectedIdList: ['137', '138', '139'],
  // データオブジェクト
  dataObject: 'd_jin_mst_khn_sonota_bunrui_jouken',
  // 項目選択必須フラグ
  itemSelectRequiredFlg: '1',
  // 項目区分
  koumokuKbn: '30',
  // 事業所IDリスト
  svJigyoIdList: ['50010', '61113'],
}
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // キーカラム
  keyColumn: { value: 'id' } as Mo00045Type,
  // 選択された項目のID
  selectedIdList: { value: '137,138,139' } as Mo00045Type,
  // データオブジェクト
  dataObject: { value: 'd_jin_mst_khn_sonota_bunrui_jouken' } as Mo00045Type,
  // 項目区分
  koumokuKbn: { value: '30' } as Mo00045Type,
  // 事業所IDリスト
  svJigyoIdList: { value: '50010,61113' } as Mo00045Type,
  // 戻り値
  selectData: {} as {selectedIdList?: string[]},
})
watch(
  () => Or26421Logic.event.get(or26421.value.uniqueCpId),
 (newValue) => {
    local.selectData = {
      selectedIdList:newValue?.selectedIdList.map((item)=>item)
    }
  }
)
function onClickOr26421() {
  // 項目選択必須フラグ
  or26421Data.itemSelectRequiredFlg = '1'
  or26421Data.keyColumn = local.keyColumn.value
  or26421Data.selectedIdList = local.selectedIdList.value.split(',')
  or26421Data.dataObject = local.dataObject.value
  or26421Data.koumokuKbn = local.koumokuKbn.value
  or26421Data.svJigyoIdList = local.svJigyoIdList.value.split(',')

  // Or26421のダイアログ開閉状態を更新する
  Or26421Logic.state.set({
    uniqueCpId: or26421.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26421p1"
        >［入力支援［担当ケアマネ検索］画面（処理モード1、項目区分30、項目選択必須フラグ1）
      </v-btn>
      <g-custom-or-26421
        v-if="showDialogOr26421"
        v-bind="or26421"
        v-model="or26421Type"
        :oneway-model-value="or26421Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26421p2"
        >［入力支援［担当ケアマネ検索］画面（処理モード1、項目区分40、項目選択必須フラグ0）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26421p3"
        >［入力支援［担当ケアマネ検索］画面（処理モード1、項目区分50、項目選択必須フラグ1）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">キーカラム</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.keyColumn"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">選択された項目のID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.selectedIdList"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">データオブジェクト</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.dataObject"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">項目区分</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.koumokuKbn"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">事業所IDリスト</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.svJigyoIdList"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr26421"> GUI01187 疎通起動 </v-btn>
  </div>
  <div class="pt-5 w-25 pl-5">
    <div> 戻り値 </div>
    <div> {{local.selectData}} </div>
  </div>
</template>
