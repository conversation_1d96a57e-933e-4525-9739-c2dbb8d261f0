<script setup lang="ts">
/**
 * Or33097:有機体:アセスメント画面（居宅）（6⑤）
 * GUI00802_アセスメント画面（居宅）（6⑤）
 *
 * @description
 * ［アセスメント］画面（居宅）（6⑤）
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'

import { reactive, ref, onMounted, watch, nextTick, computed } from 'vue'
import { cloneDeep } from 'lodash'
import { Or33097Const } from '../Or33097/Or33097.constants'
import { TeX0002Const } from '../../template/TeX0002/TeX0002.constants'
import { TeX0002Logic } from '../../template/TeX0002/TeX0002.logic'
import { Or35672Logic } from '../Or35672/Or35672.logic'
import { Or35672Const } from '../Or35672/Or35672.constants'
import { Or26866Const } from '../Or26866/Or26866.constants'
import { Or26866Logic } from '../Or26866/Or26866.logic'
import { OrX0200Const } from '../OrX0200/OrX0200.constants'
import { OrX0208Const } from '../OrX0208/OrX0208.constants'
import { OrX0209Const } from '../OrX0209/OrX0209.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or51773Const } from '../Or51773/Or51773.constants'
import type { SaveData } from './Or33097.type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { CustomClass } from '~/types/CustomClassType'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import {
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
  useScreenStore,
  useScreenUtils,
  useCmnCom,
} from '#imports'
import type { Or33097OnewayType, Or33097Type } from '~/types/cmn/business/components/Or33097Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type {
  OrX0208OnewayType,
  OrX0208Type,
  SupportListHeader,
  SupportListItem,
} from '~/types/cmn/business/components/OrX0208Type'
import type {
  AssessmentHome65UpdateInEntity,
  AssessmentHome65UpdateOutEntity,
  AssessmentHomeTab65SelectInEntity,
  AssessmentHomeTab65SelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentHome65Entity'
import type { TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Or26866OnewayType } from '~/types/cmn/business/components/Or26866Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { OrX0201OnewayType } from '~/types/cmn/business/components/OrX0201Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type {
  CareCertificationItem,
  CareCertificationList,
  OrX0200OnewayType,
  OrX0200Type,
} from '~/types/cmn/business/components/OrX0200Type'
import type {
  IssuesAndGoalListItem,
  OrX0209OnewayType,
  OrX0209Type,
} from '~/types/cmn/business/components/OrX0209Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'

const { t } = useI18n()
const { getChildCpBinds } = useScreenUtils()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue?: Or33097OnewayType
  parentUniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// コンポーネント表示フラグ
const isComponentVisible = ref(true)

/** 更新区分 */
const updateKbn = ref('')

// 子コンポーネントバンド
const OrX0200 = ref({ uniqueCpId: '' })
const OrX0208_1 = ref({ uniqueCpId: '' })
const OrX0208_2 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })
const or26866 = ref({ uniqueCpId: '' })
const OrX0209 = ref({ uniqueCpId: '' })

// ローカルディフォルト
const localDefault = reactive({
  // 要介護認定項目 項目名と選択肢
  basicMotionList: {
    careCertificationList: {
      sectionList: [] as {
        sectionTitle: string
        sectionItems: CareCertificationItem[]
      }[],
      basicMotionDetailList: [] as CodeType[],
    } as CareCertificationList,
  } as OrX0200Type,

  // 援助1 6-⑤5-2、5-5～5-6関係
  assistanceList01: {
    headers: [] as SupportListHeader[],
    items: [
      {
        /** 6-⑤5-2、5-5～5-6関係→1）金銭管理 */
        relation: '1）' + t('label.money-management'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-2、5-5～5-6関係→2）買い物 */
        relation: '2）' + t('label.shopping'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-2、5-5～5-6関係→3）調理 */
        relation: '3）' + t('label.cooking'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-2、5-5～5-6関係→4）準備・後始末 */
        relation: '4）' + t('label.preparation-adjustment'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
    ] as SupportListItem[],
  } as OrX0208Type,
  // 援助2 6-⑤5-7～5-8関係
  assistanceList02: {
    headers: [] as SupportListHeader[],
    items: [
      {
        /** 6-⑤5-7～5-8関係→1）定期的相談・助言 */
        relation: '1）' + t('label.regular-consultation-advice'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-7～5-8関係→2）各種書類作成代行 */
        relation: '2）' + t('label.preparation-various-documents'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-7～5-8関係→3）余暇活動支援*/
        relation: '3）' + t('label.leisure-activity-support'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-7～5-8関係→4）移送・外出介助*/
        relation: '4）' + t('label.transfer-going-out-assistance'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-7～5-8関係→5）代読・代筆*/
        relation: '5）' + t('label.reading-for-amanuensis'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-7～5-8関係→6）話し相手*/
        relation: '6）' + t('label.someone-to-talk'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-7～5-8関係→7）安否確認*/
        relation: '7）' + t('label.confirmation-afety'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-7～5-8関係→8）緊急連絡手段の確保 */
        relation: '8）' + t('label.securing-emergency-contact-methods'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-7～5-8関係→9）家族連絡の確保*/
        relation: '9）' + t('label.ensuring-family-contact'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-⑤5-7～5-8関係→10）社会活動への支援*/
        relation: '10）' + t('label.regular-consultation-advice'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
    ] as SupportListItem[],
  } as OrX0208Type,
})

// ローカルTwoway
const local = reactive({
  // 共通情報
  commonInfo: {} as TeX0002Type,
  // 要介護認定項目 項目名と選択肢
  basicMotionList: {
    ...localDefault.basicMotionList,
  } as OrX0200Type,
  // 援助1 6-⑤5-2、5-5～5-6関係
  assistanceList01: {
    ...localDefault.assistanceList01,
  } as OrX0208Type,
  // 援助2 6-⑤5-7～5-8関係
  assistanceList02: {
    ...localDefault.assistanceList02,
  } as OrX0208Type,
  // 入力支援ダイアログ
  or51775Oneway: {
    screenId: 'GUI00802',
    mode: Or51775Const.CARE_MANAGER_PROCESS_MODE,
    assessmentMethod: systemCommonsStore.getAuthzOtherCaremanager?.assessmentMethod ?? '',
  } as Or51775OnewayType,
  // 課題と目標リスト
  issuesAndGoalsList: {
    items: [] as IssuesAndGoalListItem[],
  } as OrX0209Type,
})

/** One-way */
const localOneway = reactive({
  // ページタイトル
  orX0201Oneway: {
    /** タイトル名 */
    title: '',
    tabNo: '',
    scrollToUniqueCpId: '',
    titleDisplayFlg: false,
  } as OrX0201OnewayType,
  // 基本動作
  section1: {
    OrX0200Oneway: {
      /** サブタイトル */
      subTitle: t('label.assessment-home-6-5-society-life-adaptation-ability'),
      /** 取込ボタン名 */
      importBtnName: t('label.assessment-home-6-1-servey-ledger-import'),
      /** 取込ボタン名 */
      importBtnToolTip: t('tooltip.assessment-home-6-1-servey-ledger-import'),
      /** 詳細ボタンツールチップ */
      detailBtnToolTip: t('tooltip.assessment-home-6-1-basic-motion'),
      /** 左テーブルタイトルが表示した桁数 */
      titleDisplayRows: '6',
      /** 汎用コードマスター返却値 */
      codeMasterList: [],
      titleWidth: '400px',
    } as OrX0200OnewayType,
  },
  section2: {
    // 援助1
    assistanceList01Oneway: {
      relationTitle: t('label.assessment-home-6-5-5-2-and-5-5-and-5-6-relation'),
      familyImplementationItems: [],
      serverImplementationItems: [],
      hopeItems: [],
      needAssistanceToPlanItems: [],
      /** クラムの幅 */
      columnWidth: [
        { key: 'relation', width: '217', titleWidth: 'auto' },
        { key: 'currentSituationOfAssistance', width: '240', titleWidth: 'auto' },
        { key: 'familyImplementation', width: '120', titleWidth: 'auto' },
        { key: 'serverImplementation', width: '120', titleWidth: 'auto' },
        { key: 'hope', width: '120' },
        { key: 'needAssistanceToPlan', width: '120', titleWidth: 'auto' },
      ],
      titleIconDisplayFlg: false,
    } as OrX0208OnewayType,
  },
  section3: {
    // 援助2
    assistanceList02Oneway: {
      relationTitle: t('label.assessment-home-6-5-5-7-and-5-8-relation'),
      familyImplementationItems: [],
      serverImplementationItems: [],
      hopeItems: [],
      needAssistanceToPlanItems: [],
      /** クラムの幅 */
      columnWidth: [
        { key: 'relation', width: '217' },
        { key: 'currentSituationOfAssistance', width: '240', titleWidth: 'auto' },
        { key: 'familyImplementation', width: '120', titleWidth: 'auto' },
        { key: 'serverImplementation', width: '120', titleWidth: 'auto' },
        { key: 'hope', width: '120' },
        { key: 'needAssistanceToPlan', width: '120', titleWidth: 'auto' },
      ],
      titleIconDisplayFlg: false,
    } as OrX0208OnewayType,
  },

  section4: {
    // ア．家族等近親者との交流ラベル
    mo00615Oneway_1: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.family-communication'),
      customClass: new CustomClass({ labelClass: 'mb-0' }),
    } as Mo00615OnewayType,
    // イ．地域近隣との交流ラベル
    mo00615Oneway_2: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.area-communication'),
      customClass: new CustomClass({ labelClass: 'mb-0' }),
    } as Mo00615OnewayType,
    // ウ．友人知人との交流ラベル
    mo00615Oneway_3: {
      itemLabelFontWeight: 'normal',
      itemLabel: t('label.friend-communication'),
      customClass: new CustomClass({ labelClass: 'mb-0' }),
    } as Mo00615OnewayType,
    // ア．家族等近親者との交流input
    mo00045Oneway_1: {
      isVerticalLabel: false,
      showItemLabel: false,
      maxLength: '18',
      customClass: new CustomClass({
        labelClass: '',
        labelStyle: '',
        itemStyle: 'width:225px;',
      }),
    } as Mo00045OnewayType,
    // イ．地域近隣との交流input
    mo00045Oneway_2: {
      isVerticalLabel: false,
      showItemLabel: false,
      maxLength: '18',
      customClass: new CustomClass({
        labelClass: '',
        labelStyle: '',
        itemStyle: 'width:225px;',
      }),
    } as Mo00045OnewayType,
    // ウ．友人知人との交流input
    mo00045Oneway_3: {
      isVerticalLabel: false,
      showItemLabel: false,
      maxLength: '18',
      customClass: new CustomClass({
        labelClass: '',
        labelStyle: '',
        itemStyle: 'width:225px;',
      }),
    } as Mo00045OnewayType,
  },

  section5: {
    // 入力
    orX0156Oneway: {
      persistentCounter: false,
      showItemLabel: false,
      maxlength: '4000',
      rows: '4',
      maxRows: '4',
      noResize: true,
      showDividerLineFlg: true,
      iconBtnDisplayFlg: true,
      showEditBtnFlg: true,
      customClass: new CustomClass({ outerClass: 'special-note' }),
    } as OrX0156OnewayType,
  },

  section6: {
    // 入力
    orX0156Oneway: {
      persistentCounter: false,
      showItemLabel: false,
      maxlength: '4000',
      rows: '4',
      maxRows: '4',
      noResize: true,
      showDividerLineFlg: true,
      iconBtnDisplayFlg: true,
      showEditBtnFlg: true,
      customClass: new CustomClass({ outerClass: 'special-note' }),
    } as OrX0156OnewayType,
    // 認定調査特記
    mo00609Oneway: {
      btnLabel: t('label.certification-survey-special-note'),
      variant: 'flat',
      color: 'transparent',
      width: '106',
    } as Mo00609OnewayType,
  },

  // ツールチップ
  btnTooltip: {
    serveyLedgerImportBtn: t('tooltip.assessment-home-6-5-servey-ledger-import'),
    basicMotionDialogBtn: t('tooltip.assessment-home-6-5-basic-motion'),
  },
  // 入力支援ダイアログ
  or51775Oneway: {
    screenId: 'GUI00802',
    mode: Or51775Const.CARE_MANAGER_PROCESS_MODE,
    assessmentMethod: systemCommonsStore.getAuthzOtherCaremanager?.assessmentMethod ?? '',
  } as Or51775OnewayType,
  // 認定調査 特定事項選択ダイアログ
  or26866Oneway: {
    /** 事業所ID */
    svJigyoId: '',
    /** ユーザーID */
    userId: '',
    /** 認定フラグ */
    ninteiFlg: '',
    /** 期間管理フラグ */
    dayVer: '',
    /** 計画期間ID */
    sc1Id: '',
    /** 項目番号 */
    defNo: '',
    /** 項目番号配列 */
    defNoArray: [],
    /** メモ */
    memo: '',
  } as Or26866OnewayType,
  // 解決すべき課題と目標一覧
  issuesAndGoalsListOneway: {
    assessmentName:
      t('label.assessment-home-issuesAndGoals-no-6-1') +
      '\r\n' +
      t('label.assessment-home-issuesAndGoals-no-6-1'),
  } as OrX0209OnewayType,
})

const isLoading = ref(false)

const focusItemId = ref('')

// コンポーネントRef
const componentRef = ref<HTMLDivElement | null>(null)

// コンポーネント表示フラグ
const isComponentVisible = ref(true)

/**************************************************
 * 算出プロパティ
 **************************************************/
// 入力支援ダイアログ表示フラグ(入力支援)
const showDialogOr51775 = computed(() => {
  // Or27761のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr26866 = computed(() => {
  // Or26866 cks_flg=1 のダイアログ開閉状態
  return Or26866Logic.state.get(or26866.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or33097Type>({
  cpId: Or33097Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0200Const.CP_ID(0)]: OrX0200.value,
  [OrX0208Const.CP_ID(0)]: OrX0208_1.value,
  [OrX0208Const.CP_ID(1)]: OrX0208_2.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or26866Const.CP_ID(0)]: or26866.value,
  [OrX0209Const.CP_ID(0)]: OrX0209.value,
})

/**
 * 複写処理
 *
 */
async function _copy() {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentHomeTab65SelectInEntity = {
    /** 事業所ID */
    svJigyoId: local.commonInfo.jigyoId,
    /** 計画期間ID */
    sc1Id: local.commonInfo.sc1Id,
    /** アセスメントID */
    gdlId: local.commonInfo.gdlId,
    /** 利用者ID */
    userId: local.commonInfo.createUserId,
    /** 履歴作成日 */
    createYmd: local.commonInfo.createYmd,
    /** 改訂フラグ */
    ninteiFlg: local.commonInfo.ninteiFormF,
  }

  const res: AssessmentHomeTab65SelectOutEntity = await ScreenRepository.select(
    'assessmentHomeTab65Select',
    inputData
  )

  // 画面情報を設定
  if (res.data) {
    setFormData(res)
  }
}

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // コントロール初期化
  await initControls()
  getCommonInfo()
  await reload()
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 利用者一覧における利用者の変更を監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue === undefined) {
      return
    }
  },
  { deep: true }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0002Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    // 画面共通情報を取得
    getCommonInfo()

    // 画面コントロールデータを設定
    setControlsData()

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or33097Const.DEFAULT.TAB_ID) {
      return
    }

    if (newValue.isRefresh) {
      isComponentVisible.value = true
      // 画面情報再取得
      await reload()
      // 次の描画までまつ（ターゲットエレメントのサイズが取得できるため）
      await nextTick()
    }

    if (newValue.isCreateDateChanged) {
      // 保存ボタンが押下された場合、保存処理を実行する
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      if (await _save(local.commonInfo.deleteKbn)) {
        setTeX0002State({ isSaveCompleted: true })
      }
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      if (await _save()) {
        setTeX0002State({ isSaveCompleted: true })
      }
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      createNew()
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      isComponentVisible.value = false
      // 削除ボタンが押下された場合、削除処理を実行する
      updateKbn.value = UPDATE_KBN.DELETE

      if (local.commonInfo.deleteKbn === TeX0002Const.DEFAULT.DELETE_PROCESS_KBN_TAB) {
        localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_DELETE_TAB
      } else if (local.commonInfo.deleteKbn === TeX0002Const.DEFAULT.DELETE_PROCESS_KBN_ALL) {
        localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_DELETE_ALL
      }
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      await _copy()
    }
  },
  { immediate: true }
)

/**
 * 複写画面メニューのイベントを監視
 */
watch(
  () => Or35672Logic.event.get(Or35672Const.CP_ID(0) + props.parentUniqueCpId),
  async (newValue) => {
    if (newValue?.reload === false) {
      return
    }

    setOr35672Event({ reload: false })

    // 複写画面から共通情報を取得
    getCommonInfoFromCopy()

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or33097Const.DEFAULT.TAB_ID) {
      return
    }

    // 画面再表示
    if (newValue?.reload) {
      // 画面情報再取得
      await reload()
    }
  }
)

/**************************************************
 * 関数
 **************************************************/
/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 基本動作
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SOCIAL_LIFE_TYPE },
    // 家族実施
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FAMILYIMPLEMENTION, addBlank: true },
    // サービス実施
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SERVERIMPLEMENTION, addBlank: true },
    // 希望
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_HOPE, addBlank: true },
    // 要援助→計画
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NEEDASSISTANCETPOPLAN, addBlank: true },
    // 地域交流 家族交流 友人交流radio選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRESENCE_OR_ABSENCE_TYPE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  const basicMotionList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_SOCIAL_LIFE_TYPE)
  localOneway.section1.OrX0200Oneway.codeMasterList = basicMotionList
  // 基本動作コード処理
  getBasicMotionCodes(basicMotionList)

  // 家族実施
  const familyImplementationCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_FAMILYIMPLEMENTION
  )
  localOneway.section2.assistanceList01Oneway.familyImplementationItems = []
  localOneway.section3.assistanceList02Oneway.familyImplementationItems = []
  familyImplementationCodeList.forEach((item) => {
    // 援助1
    localOneway.section2.assistanceList01Oneway.familyImplementationItems?.push({
      displayName: item.label,
      value: item.value,
    })
    // 援助2
    localOneway.section3.assistanceList02Oneway.familyImplementationItems?.push({
      displayName: item.label,
      value: item.value,
    })
  })

  // サービス実施
  const serviceImplementationCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SERVERIMPLEMENTION
  )
  localOneway.section2.assistanceList01Oneway.serverImplementationItems = []
  localOneway.section3.assistanceList02Oneway.serverImplementationItems = []
  serviceImplementationCodeList.forEach((item) => {
    // 援助1
    localOneway.section2.assistanceList01Oneway.serverImplementationItems?.push({
      displayName: item.label,
      value: item.value,
    })
    // 援助2
    localOneway.section3.assistanceList02Oneway.serverImplementationItems?.push({
      displayName: item.label,
      value: item.value,
    })
  })

  // 希望
  const hopeCodeList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_HOPE)
  localOneway.section2.assistanceList01Oneway.hopeItems = []
  localOneway.section3.assistanceList02Oneway.hopeItems = []
  hopeCodeList.forEach((item) => {
    // 援助1
    localOneway.section2.assistanceList01Oneway.hopeItems?.push({
      displayName: item.label,
      value: item.value,
    })
    // 援助2
    localOneway.section3.assistanceList02Oneway.hopeItems?.push({
      displayName: item.label,
      value: item.value,
    })
  })

  // 要援助→計画
  const needAssistanceToPlanCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_NEEDASSISTANCETPOPLAN
  )
  localOneway.section2.assistanceList01Oneway.needAssistanceToPlanItems = []
  localOneway.section3.assistanceList02Oneway.needAssistanceToPlanItems = []
  needAssistanceToPlanCodeList.forEach((item) => {
    // 援助1
    localOneway.section2.assistanceList01Oneway.needAssistanceToPlanItems?.push({
      displayName: item.label,
      value: item.value,
    })
    // 援助2
    localOneway.section3.assistanceList02Oneway.needAssistanceToPlanItems?.push({
      displayName: item.label,
      value: item.value,
    })
  })

  if (!refValue.value) {
    return
  }

  // 交流有無
  const communicateMethod = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRESENCE_OR_ABSENCE_TYPE
  )

  refValue.value.familyCommunicateHave = {
    radioList: communicateMethod,
    mo00039: '',
    mo00039Oneway: {
      name: '',
      showItemLabel: false,
      hideDetails: true,
      checkOff: true,
    } as Mo00039OnewayType,
  }

  refValue.value.areaCommunicateHave = {
    radioList: communicateMethod,
    mo00039: '',
    mo00039Oneway: {
      name: '',
      showItemLabel: false,
      hideDetails: true,
      checkOff: true,
    } as Mo00039OnewayType,
  }

  refValue.value.friendCommunicateHave = {
    radioList: communicateMethod,
    mo00039: '',
    mo00039Oneway: {
      name: '',
      showItemLabel: false,
      hideDetails: true,
      checkOff: true,
    } as Mo00039OnewayType,
  }
}

/**
 *  基本動作コード処理
 *
 * @param basicMotionItemList - 基本動作コードリスト
 */
function getBasicMotionCodes(basicMotionItemList: CodeType[]) {
  if (basicMotionItemList) {
    const sectionItems = [] as CareCertificationItem[]
    let currentItem = {} as CareCertificationItem
    for (const item of basicMotionItemList) {
      // アイテムタイトル
      if (item.label.startsWith('5-')) {
        currentItem = {} as CareCertificationItem
        currentItem.itemName = item.label
        // radio追加
        currentItem.raidoItems = []
        currentItem.itemType = '1'
        sectionItems.push(currentItem)
        continue
      }

      // ラジオボタン処理
      const selectItems = item.label.split('　')
      selectItems.forEach((sItem) => {
        currentItem.raidoItems?.push({
          label: sItem,
          value: getNumberFromString(sItem),
          shisetutouKbn: '',
        } as CodeType)
      })
      currentItem.raidoValue = ''
      currentItem.raidoLabelDmy = ''
      currentItem.radioOneway = {
        name: currentItem.itemName,
        showItemLabel: false,
        hideDetails: true,
      } as Mo00039OnewayType
    }
    // リストクリア
    local.basicMotionList.careCertificationList.sectionList.splice(0)
    // 要介護認定項目リスト追加
    local.basicMotionList.careCertificationList.sectionList.push({
      // '要介護認定項目'
      sectionTitle: t('label.assessment-home-6-5-level-of-care-required-certification-item'),
      sectionItems: sectionItems,
    })
    // アセスメント選択肢説明detail
    local.basicMotionList.careCertificationList.basicMotionDetailList = basicMotionItemList
  }
}

/**
 *  文字列の数字を取得
 *
 * @param str - 文字列
 */
function getNumberFromString(str: string): string {
  // 先頭の連続数字をマッチ
  const numbersAsString = /^\d+/.exec(str)
  // number[]転換
  const targetNumber = numbersAsString?.map(Number)?.[0].toString() ?? ''

  return targetNumber
}

/**
 *  画面初期情報取得
 */
async function getInitDataInfo() {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentHomeTab65SelectInEntity = {
    /** 事業所ID */
    svJigyoId: local.commonInfo.jigyoId,
    /** 計画期間ID */
    sc1Id: local.commonInfo.sc1Id,
    /** アセスメントID */
    gdlId: local.commonInfo.gdlId,
    /** 利用者ID */
    userId: local.commonInfo.createUserId,
    /** 履歴作成日 */
    createYmd: local.commonInfo.createYmd,
    /** 改訂フラグ */
    ninteiFlg: local.commonInfo.ninteiFormF,
  }

  const resData: AssessmentHomeTab65SelectOutEntity = await ScreenRepository.select(
    'assessmentHomeTab65Select',
    inputData
  )
  if (resData.data) {
    setFormData(resData)
  } else {
    if (props.onewayModelValue?.mode === Or33097Const.DEFAULT.MODE_COPY) {
      setOr35672State({ noData: true })
    }
  }
}

/**
 *  画面データを設定
 *
 * @param resData - 設定データ
 */
function setFormData(resData: AssessmentHomeTab65SelectOutEntity) {
  // 画面情報を設定
  let tabData
  if (resData.data.cpnTucGdl4Kan15H21Info.gdlId) {
    // 改訂フラグ4情報
    tabData = resData.data.cpnTucGdl4Kan15H21Info
  } else(resData.data.cpnTucGdl5Kan15R3Info.gdlId) {
    // 改訂フラグ5情報
    tabData = resData.data.cpnTucGdl5Kan15R3Info
  }

  if (!tabData?.gdlId) {
    if (props.onewayModelValue?.mode === Or33097Const.DEFAULT.MODE_COPY) {
      setOr35672State({ noData: true })
      return
    }
  }

  setOr35672State({ noData: false })

  if (tabData?.gdlId) {
    // 基本動作情報を設定
    local.basicMotionList.careCertificationList.sectionList.forEach((item) => {
      item.sectionItems.forEach((sItem) => {
        if (sItem.itemName.includes('5-1')) {
          // 5-1（薬の内服）
          sItem.raidoValue = tabData.bango51
        } else if (sItem.itemName.includes('5-2')) {
          // 5-2（金銭の管理）
          sItem.raidoValue = tabData.bango52
        } else if (sItem.itemName.includes('5-3')) {
          // 5-3（日常の意思決定）
          sItem.raidoValue = tabData.bango53
        } else if (sItem.itemName.includes('5-4')) {
          // 5-4（集団への不適応）
          sItem.raidoValue = tabData.bango54
        } else if (sItem.itemName.includes('5-5')) {
          // 5-5（買い物）
          sItem.raidoValue = tabData.bango55
        } else if (sItem.itemName.includes('5-6')) {
          // 5-6（簡単な調理）
          sItem.raidoValue = tabData.bango56
        } else if (sItem.itemName.includes('5-7')) {
          // 5-7（電話の利用）
          sItem.raidoValue = tabData.bango57
        } else if (sItem.itemName.includes('5-8')) {
          // 5-8（日中の活動（生活）状況等）
          sItem.raidoValue = tabData.bango58
        } else if (sItem.itemName.includes('5-9')) {
          // 5-9（家族居住環境社会参加の状況などの変化）
          sItem.raidoValue = tabData.bango59
        }
      })
    })

    // 社会活動の状況（6-⑤5-8、5-9関係）
    // 家族交流
    refValue.value!.familyCommunicateHave.mo00039 = tabData.kazokuKouryuUmu
    refValue.value!.familyCommunicateInput = { value: tabData.kazokuKouryuKnj }

    // 地域交流
    refValue.value!.areaCommunicateHave.mo00039 = tabData.chiikiKouryuUmu
    refValue.value!.areaCommunicateInput = { value: tabData.chiikiKouryuKnj }

    // 友人交流
    refValue.value!.friendCommunicateHave.mo00039 = tabData.yuujinKouryuUmu
    refValue.value!.friendCommunicateInput = { value: tabData.yuujinKouryuKnj }

    // 緊急連絡・見守りの方法
    refValue.value!.emergencyContactMemo.value = tabData.houhouKnj

    // 援助1
    local.assistanceList01.items[0].familyImplementation.modelValue = tabData.famJisshi521
    local.assistanceList01.items[0].serverImplementation.modelValue = tabData.serJisshi521
    local.assistanceList01.items[0].hope.modelValue = tabData.kibo521
    local.assistanceList01.items[0].needAssistanceToPlan.modelValue = tabData.keikaku521
    local.assistanceList01.items[1].familyImplementation.modelValue = tabData.famJisshi522
    local.assistanceList01.items[1].serverImplementation.modelValue = tabData.serJisshi522
    local.assistanceList01.items[1].hope.modelValue = tabData.kibo522
    local.assistanceList01.items[1].needAssistanceToPlan.modelValue = tabData.keikaku522
    local.assistanceList01.items[2].familyImplementation.modelValue = tabData.famJisshi523
    local.assistanceList01.items[2].serverImplementation.modelValue = tabData.serJisshi523
    local.assistanceList01.items[2].hope.modelValue = tabData.kibo523
    local.assistanceList01.items[2].needAssistanceToPlan.modelValue = tabData.keikaku523
    local.assistanceList01.items[3].familyImplementation.modelValue = tabData.famJisshi524
    local.assistanceList01.items[3].serverImplementation.modelValue = tabData.serJisshi524
    local.assistanceList01.items[3].hope.modelValue = tabData.kibo524
    local.assistanceList01.items[3].needAssistanceToPlan.modelValue = tabData.keikaku524

    // 援助2
    local.assistanceList02.items[0].familyImplementation.modelValue = tabData.famJisshi571
    local.assistanceList02.items[0].serverImplementation.modelValue = tabData.serJisshi571
    local.assistanceList02.items[0].hope.modelValue = tabData.kibo571
    local.assistanceList02.items[0].needAssistanceToPlan.modelValue = tabData.keikaku571
    local.assistanceList02.items[1].familyImplementation.modelValue = tabData.famJisshi572
    local.assistanceList02.items[1].serverImplementation.modelValue = tabData.serJisshi572
    local.assistanceList02.items[1].hope.modelValue = tabData.kibo572
    local.assistanceList02.items[1].needAssistanceToPlan.modelValue = tabData.keikaku572
    local.assistanceList02.items[2].familyImplementation.modelValue = tabData.famJisshi573
    local.assistanceList02.items[2].serverImplementation.modelValue = tabData.serJisshi573
    local.assistanceList02.items[2].hope.modelValue = tabData.kibo573
    local.assistanceList02.items[2].needAssistanceToPlan.modelValue = tabData.keikaku573
    local.assistanceList02.items[3].familyImplementation.modelValue = tabData.famJisshi574
    local.assistanceList02.items[3].serverImplementation.modelValue = tabData.serJisshi574
    local.assistanceList02.items[3].hope.modelValue = tabData.kibo574
    local.assistanceList02.items[3].needAssistanceToPlan.modelValue = tabData.keikaku574
    local.assistanceList02.items[4].familyImplementation.modelValue = tabData.famJisshi575
    local.assistanceList02.items[4].serverImplementation.modelValue = tabData.serJisshi575
    local.assistanceList02.items[4].hope.modelValue = tabData.kibo575
    local.assistanceList02.items[4].needAssistanceToPlan.modelValue = tabData.keikaku575
    local.assistanceList02.items[5].familyImplementation.modelValue = tabData.famJisshi576
    local.assistanceList02.items[5].serverImplementation.modelValue = tabData.serJisshi576
    local.assistanceList02.items[5].hope.modelValue = tabData.kibo576
    local.assistanceList02.items[5].needAssistanceToPlan.modelValue = tabData.keikaku576
    local.assistanceList02.items[6].familyImplementation.modelValue = tabData.famJisshi577
    local.assistanceList02.items[6].serverImplementation.modelValue = tabData.serJisshi577
    local.assistanceList02.items[6].hope.modelValue = tabData.kibo577
    local.assistanceList02.items[6].needAssistanceToPlan.modelValue = tabData.keikaku577
    local.assistanceList02.items[7].familyImplementation.modelValue = tabData.famJisshi578
    local.assistanceList02.items[7].serverImplementation.modelValue = tabData.serJisshi578
    local.assistanceList02.items[7].hope.modelValue = tabData.kibo578
    local.assistanceList02.items[7].needAssistanceToPlan.modelValue = tabData.keikaku578
    local.assistanceList02.items[8].familyImplementation.modelValue = tabData.famJisshi579
    local.assistanceList02.items[8].serverImplementation.modelValue = tabData.serJisshi579
    local.assistanceList02.items[8].hope.modelValue = tabData.kibo579
    local.assistanceList02.items[8].needAssistanceToPlan.modelValue = tabData.keikaku579
    local.assistanceList02.items[9].familyImplementation.modelValue = tabData.famJisshi5710
    local.assistanceList02.items[9].serverImplementation.modelValue = tabData.serJisshi5710
    local.assistanceList02.items[9].hope.modelValue = tabData.kibo5710
    local.assistanceList02.items[9].needAssistanceToPlan.modelValue = tabData.keikaku5710

    // 特記、解決すべき課題など
    refValue.value!.specialNoteMemo.value = tabData.memo1Knj

    updateKbn.value = UPDATE_KBN.NONE
  } else {
    // サブ情報がない場合、新規状態に設定する
    updateKbn.value = UPDATE_KBN.CREATE
    // 画面データクリア
    clearData()
  }
}

/**
 * 保存処理
 *
 * @returns - 保存処理データ
 */
async function _save(): Promise<SaveData> {
  if (updateKbn.value === UPDATE_KBN.NONE) {
    updateKbn.value = UPDATE_KBN.UPDATE
  }

  // ロード開始
  isLoading.value = true

  const childCpBindsData = getChildCpBinds(props.uniqueCpId, {
    // 基本動作 要介護認定項目
    [OrX0200Const.CP_ID(0)]: { cpPath: OrX0200Const.CP_ID(0), twoWayFlg: true },
    // 援助1 6-⑤5-2、5-5～5-6関係一覧
    [OrX0208Const.CP_ID(0)]: { cpPath: OrX0208Const.CP_ID(0), twoWayFlg: true },
    // 援助2 6-⑤5-7～5-8関係一覧
    [OrX0208Const.CP_ID(1)]: { cpPath: OrX0208Const.CP_ID(1), twoWayFlg: true },
    // 課題と目標リスト
    [OrX0209Const.CP_ID(0)]: { cpPath: OrX0209Const.CP_ID(0), twoWayFlg: true },
  })

  const parentChildCpBindsData = getChildCpBinds(props.parentUniqueCpId, {
    // 課題と目標リスト
    [OrX0209Const.CP_ID(0)]: { cpPath: OrX0209Const.CP_ID(0), twoWayFlg: true },
  })

  // 更新データ作成
  const inputData: AssessmentHome65UpdateInEntity = {
    /** タブID */
    tabId: Or33097Const.DEFAULT.TAB_ID,
    /** 機能ID */
    kinoId: systemCommonsStore.getFunctionId ?? '',
    /** 当履歴ページ番号 */
    krirekiNo: local.commonInfo.historyNo,
    /** e文書用パラメータ */
    edocumentUseParam: useCmnCom().getEDocumentParam(),
    /** e文書削除用パラメータ */
    edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
    /** 期間対象フラグ */
    kikanFlg: local.commonInfo.kikanKanriFlg,
    /** 計画対象期間番号 */
    planningPeriodNo: local.commonInfo.sc1No,
    /** 開始日 */
    startYmd: local.commonInfo.kikanKanriFlg === '1' ? local.commonInfo.periodStartYmd : '',
    /** 終了日 */
    endYmd: local.commonInfo.kikanKanriFlg === '1' ? local.commonInfo.periodEndYmd : '',
    /** ガイドラインまとめ */
    matomeFlg: '1',
    /** ログインID */
    loginId: systemCommonsStore.getCurrentUser?.loginId ?? '',
    /** システム略称 */
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    /** 職員ID */
    shokuId: local.commonInfo.createUserId ?? '',
    /** システムコード */
    sysCd: systemCommonsStore.getSystemCode ?? '0',
    /** 事業者名 */
    svJigyoKnj: local.commonInfo.jigyoId,
    /** 作成者名 */
    createUserName: local.commonInfo.createUserName ?? '0',
    /** 利用者名 */
    userName:
      systemCommonsStore.getUserSelectUserInfo()?.nameSei +
      ' ' +
      systemCommonsStore.getUserSelectUserInfo()?.nameMei,
    /** 法人ID */
    houjinId: '',
    /** 施設ID */
    shisetuId: '',
    /** 利用者ID */
    userId: '',
    /** 事業者ID */
    svJigyoId: local.commonInfo.jigyoId,
    /** 種別ID */
    syubetsuId: '',
    /** 更新区分 */
    updateKbn: updateKbn.value,
    /** 履歴更新区分 */
    historyUpdateKbn: local.commonInfo.historyUpdateKbn,
    /** 削除処理区分 */
    deleteKbn: local.commonInfo.deleteKbn ?? '',
    /** 計画対象期間ID */
    sc1Id: local.commonInfo.sc1Id,
    /** アセスメントID */
    gdlId: local.commonInfo.gdlId,
    /** 作成日 */
    kijunbiYmd: local.commonInfo.createYmd,
    /** 作成者ID */
    sakuseiId: local.commonInfo.createUserId,
    /** 改定フラグ */
    ninteiFormF: local.commonInfo.ninteiFormF,
    /** 課題と目標リスト */
    kadaiList: [] as {
      /** id */
      id: ''
      /** アセスメント番号 */
      assNo: ''
      /** 課題 */
      kadaiKnj: ''
      /** 長期 */
      choukiKnj: ''
      /** 短期 */
      tankiKnj: ''
      /** 連番 */
      seq: ''
      /** 更新区分 */
      updateKbn: ''
    }[],
  }

  // 基本動作 要介護認定項目データ
  const basicMotionData = childCpBindsData[OrX0200Const.CP_ID(0)].twoWayBind?.value as OrX0200Type
  // 援助1 6-⑤5-2、5-5～5-6関係一覧データ
  const assistance01Data = childCpBindsData[OrX0208Const.CP_ID(0)].twoWayBind?.value as OrX0208Type
  // 援助2 6-⑤5-7～5-8関係一覧データ
  const assistance02Data = childCpBindsData[OrX0208Const.CP_ID(1)].twoWayBind?.value as OrX0208Type
  // 課題と目標リスト
  const changedIssuesAndGoalsList = parentChildCpBindsData[OrX0209Const.CP_ID(0)].twoWayBind
    ?.value as OrX0209Type

  // タブデータ作成
  const tabData = {
    /** 認定項目5-1（薬の内服） */
    bango51: basicMotionData.careCertificationList.sectionList[0].sectionItems[0].raidoValue,
    /** 認定項目5-2（金銭の管理） */
    bango52: basicMotionData.careCertificationList.sectionList[0].sectionItems[1].raidoValue,
    /** 認定項目5-3（日常の意思決定） */
    bango53: basicMotionData.careCertificationList.sectionList[0].sectionItems[2].raidoValue,
    /** 認定項目5-4（集団への不適応） */
    bango54: basicMotionData.careCertificationList.sectionList[0].sectionItems[3].raidoValue,
    /** 認定項目5-5（買い物） */
    bango55: basicMotionData.careCertificationList.sectionList[0].sectionItems[4].raidoValue,
    /** 認定項目5-6（簡単な調理） */
    bango56: basicMotionData.careCertificationList.sectionList[0].sectionItems[5].raidoValue,
    /** 認定項目5-7（電話の利用） */
    bango57: basicMotionData.careCertificationList.sectionList[1].sectionItems[0].raidoValue,
    /** 認定項目5-8（日中の活動（生活）状況等） */
    bango58: basicMotionData.careCertificationList.sectionList[1].sectionItems[1].raidoValue,
    /** 認定項目5-9（家族居住環境社会参加の状況などの変化） */
    bango59: basicMotionData.careCertificationList.sectionList[1].sectionItems[2].raidoValue,
    /** 家族交流有無 */
    kazokuKouryuUmu: refValue.value!.familyCommunicateHave.mo00039,
    /** 家族交流内容 */
    kazokuKouryuKnj: refValue.value!.familyCommunicateInput.value,
    /** 地域交流有無 */
    chiikiKouryuUmu: refValue.value!.areaCommunicateHave.mo00039,
    /** 地域交流内容 */
    chiikiKouryuKnj: refValue.value!.areaCommunicateInput.value,
    /** 友人交流有無 */
    yuujinKouryuUmu: refValue.value!.friendCommunicateHave.mo00039,
    /** 友人交流内容 */
    yuujinKouryuKnj: refValue.value!.friendCommunicateInput.value,
    /** 家族実施5-2_1（金銭管理） */
    famJisshi521: assistance01Data.items[0].familyImplementation.modelValue,
    /** 家族実施5-2_2（買い物） */
    famJisshi522: assistance01Data.items[1].familyImplementation.modelValue,
    /** 家族実施5-2_3（調理） */
    famJisshi523: assistance01Data.items[2].familyImplementation.modelValue,
    /** 家族実施5-2_4（準備後始末） */
    famJisshi524: assistance01Data.items[3].familyImplementation.modelValue,
    /** 家族実施5-7_1（定期的な相談助言） */
    famJisshi571: assistance02Data.items[0].familyImplementation.modelValue,
    /** 家族実施5-7_2（各種書類作成代行） */
    famJisshi572: assistance02Data.items[1].familyImplementation.modelValue,
    /** 家族実施5-7_3（余暇活動支援） */
    famJisshi573: assistance02Data.items[2].familyImplementation.modelValue,
    /** 家族実施5-7_4（移送外出介助） */
    famJisshi574: assistance02Data.items[3].familyImplementation.modelValue,
    /** 家族実施5-7_5（代読代筆） */
    famJisshi575: assistance02Data.items[4].familyImplementation.modelValue,
    /** 家族実施5-7_6（話し相手） */
    famJisshi576: assistance02Data.items[5].familyImplementation.modelValue,
    /** 家族実施5-7_7（安否確認） */
    famJisshi577: assistance02Data.items[6].familyImplementation.modelValue,
    /** 家族実施5-7_8（緊急連絡手段の確保） */
    famJisshi578: assistance02Data.items[7].familyImplementation.modelValue,
    /** 家族実施5-7_9（家族連絡の確保） */
    famJisshi579: assistance02Data.items[8].familyImplementation.modelValue,
    /** 家族実施5-7_10（社会活動への支援） */
    famJisshi5710: assistance02Data.items[9].familyImplementation.modelValue,
    /** サービス実施5-2_1（金銭管理） */
    serJisshi521: assistance01Data.items[0].serverImplementation.modelValue,
    /** サービス実施5-2_2（買い物） */
    serJisshi522: assistance01Data.items[1].serverImplementation.modelValue,
    /** サービス実施5-2_3（調理） */
    serJisshi523: assistance01Data.items[2].serverImplementation.modelValue,
    /** サービス実施5-2_4（準備後始末） */
    serJisshi524: assistance01Data.items[3].serverImplementation.modelValue,
    /** サービス実施5-7_1（定期的な相談助言） */
    serJisshi571: assistance02Data.items[0].serverImplementation.modelValue,
    /** サービス実施5-7_2（各種書類作成代行） */
    serJisshi572: assistance02Data.items[1].serverImplementation.modelValue,
    /** サービス実施5-7_3（余暇活動支援） */
    serJisshi573: assistance02Data.items[2].serverImplementation.modelValue,
    /** サービス実施5-7_4（移送外出介助） */
    serJisshi574: assistance02Data.items[3].serverImplementation.modelValue,
    /** サービス実施5-7_5（代読代筆） */
    serJisshi575: assistance02Data.items[4].serverImplementation.modelValue,
    /** サービス実施5-7_6（話し相手） */
    serJisshi576: assistance02Data.items[5].serverImplementation.modelValue,
    /** サービス実施5-7_7（安否確認） */
    serJisshi577: assistance02Data.items[6].serverImplementation.modelValue,
    /** サービス実施5-7_8（緊急連絡手段の確保） */
    serJisshi578: assistance02Data.items[7].serverImplementation.modelValue,
    /** サービス実施5-7_9（家族連絡の確保） */
    serJisshi579: assistance02Data.items[8].serverImplementation.modelValue,
    /** サービス実施5-7_10（社会活動への支援） */
    serJisshi5710: assistance02Data.items[9].serverImplementation.modelValue,
    /** 希望5-2_1（金銭管理） */
    kibo521: assistance01Data.items[0].hope.modelValue,
    /** 希望5-2_2（買い物） */
    kibo522: assistance01Data.items[1].hope.modelValue,
    /** 希望5-2_3（調理） */
    kibo523: assistance01Data.items[2].hope.modelValue,
    /** 希望5-2_4（準備後始末） */
    kibo524: assistance01Data.items[3].hope.modelValue,
    /** 希望5-7_1（定期的な相談助言） */
    kibo571: assistance02Data.items[0].hope.modelValue,
    /** 希望5-7_2（各種書類作成代行） */
    kibo572: assistance02Data.items[1].hope.modelValue,
    /** 希望5-7_3（余暇活動支援） */
    kibo573: assistance02Data.items[2].hope.modelValue,
    /** 希望5-7_4（移送外出介助） */
    kibo574: assistance02Data.items[3].hope.modelValue,
    /** 希望5-7_5（代読代筆） */
    kibo575: assistance02Data.items[4].hope.modelValue,
    /** 希望5-7_6（話し相手） */
    kibo576: assistance02Data.items[5].hope.modelValue,
    /** 希望5-7_7（安否確認） */
    kibo577: assistance02Data.items[6].hope.modelValue,
    /** 希望5-7_8（緊急連絡手段の確保） */
    kibo578: assistance02Data.items[7].hope.modelValue,
    /** 希望5-7_9（家族連絡の確保） */
    kibo579: assistance02Data.items[8].hope.modelValue,
    /** 希望5-7_10（社会活動への支援） */
    kibo5710: assistance02Data.items[9].hope.modelValue,
    /** 要援助計画5-2_1（金銭管理） */
    keikaku521: assistance01Data.items[0].needAssistanceToPlan.modelValue,
    /** 要援助計画5-2_2（買い物） */
    keikaku522: assistance01Data.items[1].needAssistanceToPlan.modelValue,
    /** 要援助計画5-2_3（調理） */
    keikaku523: assistance01Data.items[2].needAssistanceToPlan.modelValue,
    /** 要援助計画5-2_4（準備後始末） */
    keikaku524: assistance01Data.items[3].needAssistanceToPlan.modelValue,
    /** 要援助計画5-7_1（定期的な相談助言） */
    keikaku571: assistance02Data.items[0].needAssistanceToPlan.modelValue,
    /** 要援助計画5-7_2（各種書類作成代行） */
    keikaku572: assistance02Data.items[1].needAssistanceToPlan.modelValue,
    /** 要援助計画5-7_3（余暇活動支援） */
    keikaku573: assistance02Data.items[2].needAssistanceToPlan.modelValue,
    /** 要援助計画5-7_4（移送外出介助） */
    keikaku574: assistance02Data.items[3].needAssistanceToPlan.modelValue,
    /** 要援助計画5-7_5（代読代筆） */
    keikaku575: assistance02Data.items[4].needAssistanceToPlan.modelValue,
    /** 要援助計画5-7_6（話し相手） */
    keikaku576: assistance02Data.items[5].needAssistanceToPlan.modelValue,
    /** 要援助計画5-7_7（安否確認） */
    keikaku577: assistance02Data.items[6].needAssistanceToPlan.modelValue,
    /** 要援助計画5-7_8（緊急連絡手段の確保） */
    keikaku578: assistance02Data.items[7].needAssistanceToPlan.modelValue,
    /** 要援助計画5-7_9（家族連絡の確保） */
    keikaku579: assistance02Data.items[8].needAssistanceToPlan.modelValue,
    /** 要援助計画5-7_10（社会活動への支援） */
    keikaku5710: assistance02Data.items[9].needAssistanceToPlan.modelValue,
    /** 緊急連絡見守りの方法 */
    houhouKnj: refValue.value!.emergencyContactMemo.value,
    /** 特記事項 */
    memo1Knj: refValue.value!.specialNoteMemo.value,
  }

  if (Or33097Const.DEFAULT.KAITEI_FLG_H21 === local.commonInfo.ninteiFormF) {
    // 改訂フラグ4情報
    inputData.basicAction4Info = tabData
  } else {
    // 改訂フラグ5情報
    inputData.basicAction5Info = tabData
  }

  // 課題と目標リスト作成
  changedIssuesAndGoalsList?.items.forEach((item) => {
    inputData.kadaiList.push({
      /** id */
      id: item.id,
      /** アセスメント番号 */
      assNo: item.assNo,
      /** 課題 */
      kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
      /** 長期 */
      choukiKnj: item.longtermGoal.value,
      /** 短期 */
      tankiKnj: item.shorttermGoal.value,
      /** 連番 */
      seq: item.seq?.toString(),
      /** 更新区分 */
      updateKbn: item.updateKbn,
    })
    // 削除された課題を取得
    const delItems = local.commonInfo.issuesAndGoalsList?.filter(
      (item) => !changedIssuesAndGoalsList.items?.some((cKdai) => item.id === cKdai.id)
    )
    delItems?.forEach((item) => {
      inputData.kadaiList.push({
        /** id */
        id: item.dataId,
        /** アセスメント番号 */
        assNo: item.assNo,
        /** 課題 */
        kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
        /** 長期 */
        choukiKnj: item.longtermGoal.value,
        /** 短期 */
        tankiKnj: item.shorttermGoal.value,
        /** 連番 */
        seq: item.seq?.toString(),
        /** 更新区分 */
        updateKbn: TeX0002Const.DEFAULT.UPDATE_KBN_D,
      })
    })
  })

  const resData: AssessmentHome65UpdateOutEntity = await ScreenRepository.update(
    'assessmentHomeTab65Update',
    inputData
  )
  /**
   * 保存成功の場合
   */
  if (resData?.data) {
    // 更新後データを取得し設定
    TeX0002Logic.data.set({
      uniqueCpId: props.parentUniqueCpId,
      value: {
        updateData: {
          sc1Id: resData.data.sc1Id,
          gdlId: resData.data.gdlId,
          errKbn: resData.data.errKbn,
        },
      },
    })
    isLoading.value = false
    return true
  }
  // ロード完了
  isLoading.value = false
  return false
}

/**
 * Or35672イベント発火
 *
 * @param event - イベント
 */
function setOr35672Event(event: Record<string, boolean>) {
  Or35672Logic.event.set({
    uniqueCpId: Or35672Const.CP_ID(0) + props.parentUniqueCpId,
    events: event,
  })
}

/**
 * Or35672ステート発火
 *
 * @param state - ステート
 */
function setOr35672State(state: Record<string, boolean>) {
  Or35672Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 * Or35672ステート発火
 *
 * @param state - ステート
 */
function setTeX0002State(state: Record<string, boolean>) {
  TeX0002Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 *  画面共通情報を取得
 */
function getCommonInfo() {
  const commonInfo = TeX0002Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo.ninteiFormF = commonInfo.ninteiFormF ?? Or33097Const.DEFAULT.KAITEI_FLG_H21
    local.commonInfo.kikanKanriFlg = commonInfo.kikanKanriFlg
    local.commonInfo.userId = commonInfo.userId
    local.commonInfo.activeTabId = commonInfo.activeTabId
    local.commonInfo.jigyoId = commonInfo.jigyoId
    local.commonInfo.sc1Id = commonInfo.sc1Id
    local.commonInfo.gdlId = commonInfo.gdlId
    local.commonInfo.createUserId = commonInfo.createUserId
    local.commonInfo.createYmd = commonInfo.createYmd
    local.commonInfo.deleteKbn = commonInfo.deleteKbn
    local.commonInfo.issuesAndGoalsList = commonInfo.issuesAndGoalsList
    local.commonInfo.historyNo = commonInfo.historyNo
    local.commonInfo.periodStartYmd = commonInfo.periodStartYmd
    local.commonInfo.periodStartYmd = commonInfo.periodEndYmd
    local.commonInfo.houjinId = commonInfo.houjinId
    local.commonInfo.shisetuId = commonInfo.shisetuId
    local.commonInfo.syubetuId = commonInfo.syubetuId
    local.commonInfo.sc1No = commonInfo.sc1No
    local.commonInfo.historyUpdateKbn = commonInfo.historyUpdateKbn
    local.commonInfo.syubetuId = commonInfo.syubetuId

    // 解決すべき課題と目標一覧パラメータ設定
    localOneway.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
    localOneway.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
    localOneway.issuesAndGoalsListOneway.assNo = Or33097Const.DEFAULT.TAB_ID
    localOneway.issuesAndGoalsListOneway.userId = local.commonInfo.userId

    local.issuesAndGoalsList.items =
      local.commonInfo.issuesAndGoalsList ?? ([] as IssuesAndGoalListItem[])
  }
}

/**
 *  複写画面から共通情報を取得
 */
function getCommonInfoFromCopy() {
  const commonInfo = Or35672Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    /** 改定フラグ */
    local.commonInfo.ninteiFormF = commonInfo.params?.ninteiFormF
    /** 選択中タブID */
    local.commonInfo.activeTabId = commonInfo.params?.activeTabId
    /** 事業所ID */
    local.commonInfo.jigyoId = commonInfo.params?.jigyoId
    /** 法人ID */
    local.commonInfo.houjinId = commonInfo.params?.houjinId
    /** 施設ID */
    local.commonInfo.shisetuId = commonInfo.params?.shisetuId
    /** 利用者ID */
    local.commonInfo.userId = commonInfo.params?.userId
    /** 計画対象期間ID */
    local.commonInfo.sc1Id = commonInfo.params?.sc1Id
    /** アセスメントID */
    local.commonInfo.gdlId = commonInfo.params?.gdlId
    /** 作成者ID */
    local.commonInfo.createUserId = commonInfo.params?.createUserId
    /** 作成日 */
    local.commonInfo.createYmd = commonInfo.params?.createdYmd
    /** 種別ID */
    local.commonInfo.syubetuId = commonInfo.params?.syubetsuId
    /** 期間管理フラグ */
    local.commonInfo.kikanKanriFlg = commonInfo.params?.kikanFlg
    /** 課題と目標リスト */
    local.commonInfo.issuesAndGoalsList = commonInfo.params?.issuesAndGoalsList
  }
  // 解決すべき課題と目標一覧パラメータ設定
  localOneway.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
  localOneway.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
  localOneway.issuesAndGoalsListOneway.assNo = local.commonInfo.activeTabId
  localOneway.issuesAndGoalsListOneway.userId = local.commonInfo.userId

  local.issuesAndGoalsList.items =
    local.commonInfo.issuesAndGoalsList ?? ([] as IssuesAndGoalListItem[])
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 *  コントロール初期化
 */
async function initControls() {
  // 援助1 6-⑤5-2、5-5～5-6関係
  local.assistanceList01 = {
    ...localDefault.assistanceList01,
  }
  // 援助2 6-⑤5-7～5-8関係
  local.assistanceList02 = {
    ...localDefault.assistanceList02,
  }

  // 課題と目標一覧スクロール先のIDを設定
  localOneway.orX0201Oneway.scrollToUniqueCpId = OrX0209.value.uniqueCpId

  await initCodes()
}

/**
 *  画面コントロールデータを設定
 */
function setControlsData() {
  // 解決すべき課題と目標一覧
  local.issuesAndGoalsList.items =
    cloneDeep(local.commonInfo.issuesAndGoalsList) ?? ([] as IssuesAndGoalListItem[])
}

/**
 *  コントロール初期化
 */
async function reload() {
  isLoading.value = true
  let start: number = performance.now()
  // 汎用コードマスタからコード情報を取得
  await initCodes()

  let end: number = performance.now()
  console.log(`initCodes実行時間: ${(end - start).toFixed(3)} ミリ秒`)

  start = performance.now()
  // 画面初期情報取得
  await getInitDataInfo()
  if (localOneway.issuesAndGoalsListOneway.mode !== OrX0209Const.DEFAULT.MODE_COPY) {
    localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_NORMAL
  }
  // RefValueをリセット
  setTimeout(() => {
    setRefValue()
  }, 0)
  end = performance.now()
  console.log(`getInitDataInfo実行時間: ${(end - start).toFixed(3)} ミリ秒`)

  isLoading.value = false
}

/**
 * RefValue初期化
 */
function setRefValue() {
  useScreenStore().setCpTwoWay({
    cpId: Or33097Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/**
 *  新規処理
 */
function createNew() {
  // 画面データをクリア
  clearData()

  updateKbn.value = UPDATE_KBN.CREATE
  await initCodes()
  // RefValueをリセット
  setTimeout(() => {
    setRefValue()
  }, 0)
}

/**
 *  画面データをクリア
 */
function clearData() {
  // 基本動作
  local.basicMotionList.careCertificationList.sectionList?.forEach((item) => {
    item.sectionItems.forEach((sItem) => {
      if (sItem.raidoItems) {
        sItem.raidoValue = ''
      }
    })
  })
  // 援助1
  local.assistanceList01.items.forEach((item) => {
    item.familyImplementation.modelValue = ''
    item.serverImplementation.modelValue = ''
    item.hope.modelValue = ''
    item.needAssistanceToPlan.modelValue = ''
  })
  // 援助2
  local.assistanceList02.items.forEach((item) => {
    item.familyImplementation.modelValue = ''
    item.serverImplementation.modelValue = ''
    item.hope.modelValue = ''
    item.needAssistanceToPlan.modelValue = ''
  })
  // 社会活動の状況
  refValue.value!.familyCommunicateHave.mo00039 = ''
  refValue.value!.familyCommunicateInput.value = ''
  refValue.value!.areaCommunicateHave.mo00039 = ''
  refValue.value!.areaCommunicateInput.value = ''
  refValue.value!.friendCommunicateHave.mo00039 = ''
  refValue.value!.friendCommunicateInput.value = ''
  // 緊急連絡・見守りの方法
  refValue.value!.emergencyContactMemo.value = ''
  // 特記、解決すべき課題
  refValue.value!.specialNoteMemo.value = ''
}

/**
 * 入力支援ダイアログを開く
 *
 * @param content - 入力内容
 *
 * @param itemId - 項目ID
 */
function openInputSupportDialog(content: string | undefined, itemId: string) {
  let tableName = ''
  let columnName = ''
  let t3Cd = ''
  let title = ''

  focusItemId.value = itemId

  if (local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21) {
    // 改訂フラグが4「H21/4」の場合、"cpn_tuc_gdl4_kan15_h21"
    tableName = TeX0002Const.DEFAULT.INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL4_KAN15_H21
  } else if (local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_R34) {
    // 改訂フラグが5「R3/4」の場合、cpn_tuc_gdl5_kan15_r3
    tableName = TeX0002Const.DEFAULT.INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL5_KAN15_R3
  }

  if (itemId === Or33097Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_EMERGENCY_CONTACT) {
    // 緊急連絡・見守りの方法メモの場合、"houhou_knj"
    columnName = TeX0002Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_HOUHOU_KNJ
    // 緊急連絡・見守りの方法メモの場合、"1"
    t3Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_SHOU_BUNRUI_CD_1
    // 緊急連絡・見守りの方法メモの場合、"緊急連絡・見守りの方法"
    title = t('label.assessment-home-6-5-emergency-contact-and-Monitor-method')
  } else if (itemId === Or33097Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_SPECAIL_NOTE) {
    // 特記事項メモの場合、"memo1_knj"
    columnName = TeX0002Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_MEMO1_KNJ
    // 特記事項メモの場合、"2"
    t3Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_SHOU_BUNRUI_CD_2
    // 緊急連絡・見守りの方法メモの場合、"文章マスタ"
    title = t('label.sentence-master')
  }

  // タイトル
  localOneway.or51775Oneway.title = title
  // 内容
  localOneway.or51775Oneway.inputContents = content ?? ''
  // 分類ID
  localOneway.or51775Oneway.bunruiId = ''
  // 大分類CD
  localOneway.or51775Oneway.t1Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_DAI_BUNRUI_CD_602
  // 中分類CD
  localOneway.or51775Oneway.t2Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_CHU_BUNRUI_CD_29
  // 小分類CD
  localOneway.or51775Oneway.t3Cd = t3Cd
  // テーブル名
  localOneway.or51775Oneway.tableName = tableName
  // クラム名
  localOneway.or51775Oneway.columnName = columnName
  // 利用者ID
  localOneway.or51775Oneway.userId = local.commonInfo.userId ?? ''

  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 入力支援ダイアログ確認処理
 *
 * @param resData - 入力支援選択情報
 */
function inputSupportConfirm(resData: Or51775ConfirmType) {
  if (!refValue.value) {
    return
  }

  switch (focusItemId.value) {
    case Or33097Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_EMERGENCY_CONTACT:
      // 本文末に追加の場合
      if (resData.type === Or51773Const.ADD_END_TEXT_IMPORT_TYPE) {
        refValue.value.emergencyContactMemo.value = `${refValue.value.emergencyContactMemo.value ?? ''}${resData.value}`
      }
      // 本文上書の場合
      else if (resData.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
        refValue.value.emergencyContactMemo.value = resData.value
      }
      break
    case Or33097Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_SPECAIL_NOTE:
      // 本文末に追加の場合
      if (resData.type === Or51773Const.ADD_END_TEXT_IMPORT_TYPE) {
        refValue.value.specialNoteMemo.value = `${refValue.value.specialNoteMemo.value ?? ''}${resData.value}`
      }
      // 本文上書の場合
      else if (resData.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
        refValue.value.specialNoteMemo.value = resData.value
      }
      break
  }
}

/**
 * 認定調査 特定事項選択ダイアログ開く処理
 *
 * @param content - 入力内容
 *
 * @param itemId - 項目ID
 */
function openDialogOr26866(content: string | undefined, itemId: string) {
  focusItemId.value = itemId

  // 計画期間ID
  localOneway.or26866Oneway.sc1Id = local.commonInfo.sc1Id ?? ''
  // 事業所ID
  localOneway.or26866Oneway.svJigyoId = local.commonInfo.jigyoId ?? ''
  // ユーザーID
  localOneway.or26866Oneway.userId = local.commonInfo.userId ?? ''
  // 認定フラグ
  localOneway.or26866Oneway.ninteiFlg = local.commonInfo.ninteiFormF ?? ''
  // 期間管理フラグ
  localOneway.or26866Oneway.dayVer = local.commonInfo.kikanKanriFlg ?? ''
  // 項目番号
  localOneway.or26866Oneway.defNo = '0'
  // 項目番号配列
  localOneway.or26866Oneway.defNoArray = ['5']
  // メモ
  localOneway.or26866Oneway.memo = content ?? ''

  // Or26866のダイアログ開閉状態を更新する
  Or26866Logic.state.set({
    uniqueCpId: or26866.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 認定調査 特定事項選択ダイアログ戻り値処理
 *
 * @param resData - 入力支援選択情報
 */
function fncOr26866Return(resData: string) {
  if (!refValue.value) {
    return
  }
  refValue.value.specialNoteMemo.value = resData
}
</script>

<template>
  <div
    v-show="isComponentVisible"
    class="Or33097ComponentWrapper pa-0"
  >
    <!-- ローディング -->
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <c-v-row
      no-gutters
      class="title"
    >
      <c-v-col>
        <!-- タイトル -->
        <g-custom-or-x-0201 :oneway-model-value="localOneway.orX0201Oneway"></g-custom-or-x-0201>
      </c-v-col>
    </c-v-row>
    <c-v-sheet class="contentWrapper d-flex flex-column flex-0-1 mt-6">
      <c-v-row
        class="flex-1-1"
        no-gutters
      >
        <c-v-col>
          <!-- 要介護認定項目 -->
          <g-custom-or-x-0200
            v-bind="OrX0200"
            :model-value="local.basicMotionList"
            :oneway-model-value="localOneway.section1.OrX0200Oneway"
            :common-info="local.commonInfo"
            :on-savetab-data="_save"
          >
          </g-custom-or-x-0200>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="background-white"
      >
        <c-v-col>
          <c-v-row
            no-gutters
            class="d-flex flex-row flex-nowrap pt-4"
          >
            <!-- 援助1 6-⑤5-2、5-5～5-6関係一覧 -->
            <c-v-col>
              <g-custom-or-x-0208
                v-bind="OrX0208_1"
                :model-value="local.assistanceList01"
                :oneway-model-value="localOneway.section2.assistanceList01Oneway"
              >
              </g-custom-or-x-0208>
            </c-v-col>
            <c-v-col cols="5"> </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="background-white"
      >
        <c-v-col>
          <c-v-row
            no-gutters
            class="d-flex flex-row flex-nowrap pt-4 pb-6"
          >
            <!-- 援助2 6-⑤5-7～5-8関係一覧 -->
            <c-v-col>
              <g-custom-or-x-0208
                v-bind="OrX0208_2"
                :model-value="local.assistanceList02"
                :oneway-model-value="localOneway.section3.assistanceList02Oneway"
              >
              </g-custom-or-x-0208>
            </c-v-col>
            <c-v-col cols="5"></c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="pb-3"
      >
        <c-v-col
          :id="props.uniqueCpId + 'communicationSituationMethod'"
          class="leftContent"
        >
          <!-- 社会活動の状況（6-⑤5-8、5-9関係） -->
          <g-custom-or-x-0186
            :oneway-model-value="{
              title:
                t('label.left-angle-bracket-full-width') +
                t('label.assessment-home-6-5-society-activity-situation') +
                t('label.right-angle-bracket-full-width'),
              contentClass: '',
            }"
          >
            <template #content>
              <c-v-row no-gutters>
                <c-v-col class="pl-4 pr-6">
                  <!-- ア．家族等近親者との交流 -->
                  <c-v-row
                    no-gutters
                    class="py-3 align-center border-b"
                    style="height: 60px"
                  >
                    <c-v-col
                      cols="2"
                      class="mr-9"
                    >
                      <base-mo00615 :oneway-model-value="localOneway.section4.mo00615Oneway_1" />
                    </c-v-col>
                    <!-- 選択肢 -->
                    <c-v-col
                      cols="6"
                      class="d-flex align-center"
                    >
                      <base-mo00039
                        v-model="refValue!.familyCommunicateHave.mo00039"
                        class="communicationRadio"
                        :oneway-model-value="refValue!.familyCommunicateHave.mo00039Oneway"
                      >
                        <div
                          v-for="(item, index) in refValue!.familyCommunicateHave.radioList"
                          :key="index"
                          class="d-flex align-center"
                        >
                          <div
                            v-if="index === 0"
                            class="radio-one-w"
                          >
                            <div class="d-flex align-center">
                              <base-at-radio
                                :key="index"
                                :name="'languageHandycapUseRadio' + index"
                                :radio-label="item.label"
                                :value="item.value"
                              />
                            </div>
                          </div>
                          <div
                            v-if="index === 1"
                            class="d-flex align-center"
                          >
                            <base-mo00045
                              v-model="refValue!.familyCommunicateInput"
                              :oneway-model-value="localOneway.section4.mo00045Oneway_1"
                            />
                            <base-at-radio
                              :key="index"
                              class="pl-16"
                              :name="'languageHandycapUseRadio' + index"
                              :radio-label="item.label"
                              :value="item.value"
                            />
                          </div>
                        </div>
                      </base-mo00039>
                    </c-v-col>
                    <c-v-col cols="4"></c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <c-v-row no-gutters>
                <c-v-col class="pl-4 pr-6">
                  <!-- イ．地域近隣との交流 -->
                  <c-v-row
                    no-gutters
                    class="py-3 align-center border-b"
                    style="height: 60px"
                  >
                    <c-v-col
                      cols="2"
                      class="mr-9"
                    >
                      <base-mo00615 :oneway-model-value="localOneway.section4.mo00615Oneway_2" />
                    </c-v-col>
                    <!-- 選択肢 -->
                    <c-v-col
                      cols="6"
                      class="d-flex align-center"
                    >
                      <base-mo00039
                        v-model="refValue!.areaCommunicateHave.mo00039"
                        class="communicationRadio"
                        :oneway-model-value="refValue!.areaCommunicateHave.mo00039Oneway"
                      >
                        <div
                          v-for="(item, index) in refValue!.areaCommunicateHave.radioList"
                          :key="index"
                          class="d-flex align-center"
                        >
                          <div
                            v-if="index === 0"
                            class="radio-one-w"
                          >
                            <div class="d-flex align-center">
                              <base-at-radio
                                :key="index"
                                :name="'languageHandycapUseRadio' + index"
                                :radio-label="item.label"
                                :value="item.value"
                              />
                            </div>
                          </div>
                          <div
                            v-if="index === 1"
                            class="d-flex align-center"
                          >
                            <base-mo00045
                              v-model="refValue!.areaCommunicateInput"
                              :oneway-model-value="localOneway.section4.mo00045Oneway_2"
                            />
                            <base-at-radio
                              :key="index"
                              class="pl-16"
                              :name="'languageHandycapUseRadio' + index"
                              :radio-label="item.label"
                              :value="item.value"
                            />
                          </div>
                        </div>
                      </base-mo00039>
                    </c-v-col>
                    <c-v-col cols="4"></c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <c-v-row no-gutters>
                <c-v-col class="pl-4 pr-6">
                  <!-- ウ．友人知人との交流  -->
                  <c-v-row
                    no-gutters
                    class="py-3 align-center"
                    style="height: 60px"
                  >
                    <c-v-col
                      cols="2"
                      class="mr-9"
                    >
                      <base-mo00615 :oneway-model-value="localOneway.section4.mo00615Oneway_3" />
                    </c-v-col>
                    <!-- 選択肢 -->
                    <c-v-col
                      cols="6"
                      class="d-flex align-center"
                    >
                      <base-mo00039
                        v-model="refValue!.friendCommunicateHave.mo00039"
                        class="communicationRadio"
                        :oneway-model-value="refValue!.friendCommunicateHave.mo00039Oneway"
                      >
                        <div
                          v-for="(item, index) in refValue!.friendCommunicateHave.radioList"
                          :key="index"
                          class="d-flex align-center"
                        >
                          <div
                            v-if="index === 0"
                            class="radio-one-w"
                          >
                            <div class="d-flex align-center">
                              <base-at-radio
                                :key="index"
                                :name="'languageHandycapUseRadio' + index"
                                :radio-label="item.label"
                                :value="item.value"
                              />
                            </div>
                          </div>
                          <div
                            v-if="index === 1"
                            class="d-flex align-center"
                          >
                            <base-mo00045
                              v-model="refValue!.friendCommunicateInput"
                              :oneway-model-value="localOneway.section4.mo00045Oneway_3"
                            />
                            <base-at-radio
                              :key="index"
                              class="pl-16"
                              :name="'languageHandycapUseRadio' + index"
                              :radio-label="item.label"
                              :value="item.value"
                            />
                          </div>
                        </div>
                      </base-mo00039>
                    </c-v-col>
                    <c-v-col cols="4"></c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
            </template>
          </g-custom-or-x-0186>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="background-white"
      >
        <c-v-col>
          <div class="c-sub-title h-48">
            {{ t('label.assessment-home-6-5-emergency-contact-and-Monitor-method') }}
          </div>
          <!-- 緊急連絡・見守りの方法 -->
          <div class="px-12 pt-4 pb-6">
            <g-custom-or-x-0156
              v-model="refValue!.emergencyContactMemo"
              class="orx0156-one-line"
              :oneway-model-value="localOneway.section5.orX0156Oneway"
              :style="{ width: '984px' }"
              @on-click-edit-btn="
                openInputSupportDialog(
                  refValue!.emergencyContactMemo.value,
                  Or33097Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_EMERGENCY_CONTACT
                )
              "
            />
          </div>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="background-white"
      >
        <c-v-col>
          <div class="c-sub-title h-48">
            {{ t('label.special-note-matter-support') }}
          </div>
          <!-- 特記事項 -->
          <div class="px-12 pt-4 pb-12">
            <g-custom-or-x-0156
              v-model="refValue!.specialNoteMemo"
              :oneway-model-value="localOneway.section6.orX0156Oneway"
              :style="{ width: '984px' }"
              @on-click-edit-btn="
                openInputSupportDialog(
                  refValue!.specialNoteMemo.value,
                  Or33097Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_SPECAIL_NOTE
                )
              "
            >
              <!-- slot -->
              <template #footer>
                <base-mo-00609
                  class="special-note-font"
                  :oneway-model-value="localOneway.section6.mo00609Oneway"
                  @click="
                    openDialogOr26866(
                      refValue!.specialNoteMemo.value,
                      TeX0002Const.DEFAULT.ITEM_KBN_POSITION_CHANGE_RISING_AND_SLEEPING
                    )
                  "
                ></base-mo-00609>
              </template>
            </g-custom-or-x-0156>
          </div>
        </c-v-col>
      </c-v-row>
    </c-v-sheet>
    <!-- 線 -->
    <c-v-row
      no-gutters
      style="margin: 0 -24px"
      class="py-6"
    >
      <c-v-divider></c-v-divider>
    </c-v-row>

    <!-- フッター -->
    <c-v-row
      no-gutters
      class="pb-6"
    >
      <c-v-col>
        <!-- 解決すべき課題と目標 -->
        <g-custom-or-x-0209
          v-bind="OrX0209"
          :model-value="local.issuesAndGoalsList"
          :oneway-model-value="localOneway.issuesAndGoalsListOneway"
        >
        </g-custom-or-x-0209>
      </c-v-col>
    </c-v-row>
  </div>

  <!-- 入力支援ダイアログ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="inputSupportConfirm"
  />
  <!-- 認定調査 特定事項選択ダイアログ -->
  <g-custom-or-26866
    v-if="showDialogOr26866"
    v-bind="or26866"
    :oneway-model-value="localOneway.or26866Oneway"
    @update:model-value="fncOr26866Return"
  />
</template>

<style scoped lang="scss">
.Or33097ComponentWrapper {
  .title {
    width: 1080px;
  }

  .contentWrapper {
    width: 1080px;

    .transfer-move-care-help {
      width: 442px;
      border-top: 1px rgb(var(--v-theme-black-200)) solid;
      border-right: 1px rgb(var(--v-theme-black-200)) solid;
      border-bottom: 1px rgb(var(--v-theme-black-200)) solid;

      .header {
        height: 97px;
        background-color: rgb(var(--v-theme-blue-100));
        border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
        align-items: center;
      }

      .row {
        height: 48px;

        .v-col .chkItem:not(:first-child) {
          padding-left: 12px;
        }
      }

      .row:not(:first-child) {
        border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
      }

      .row:last-child {
        border-bottom: unset;
      }

      .content {
        margin-right: 0 !important;

        .v-col {
          border-right: 1px rgb(var(--v-theme-black-200)) solid;
          border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
        }

        .v-col:first-child {
          border-left: 1px rgb(var(--v-theme-black-200)) solid;
        }
      }
    }

    .body-washing-care-help {
      .header {
        min-height: 40px;
        background-color: rgb(var(--v-theme-black-100));
        border-left: 1px rgb(var(--v-theme-black-200)) solid;
        border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
        border-right: 1px rgb(var(--v-theme-black-200)) solid;
        font-weight: bold;
      }

      .content {
        margin-right: 0 !important;

        .v-col {
          border-right: 1px rgb(var(--v-theme-black-200)) solid;
          border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
        }

        .v-col:first-child {
          border-left: 1px rgb(var(--v-theme-black-200)) solid;
        }
      }
    }
  }
}
:deep(.special-note-font .v-btn__content) {
  opacity: 0.6;
}

.radio-one-w {
  width: 72px;
}

.h-48 {
  height: 48px;
}

.border-b {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
}

:deep(.communicationRadio) {
  height: 30px;
}

:deep(.v-checkbox .v-checkbox-btn) {
  min-height: 24px;
  height: 24px;
}

:deep(.v-radio) {
  min-height: 24px;
  height: 24px;
}

.background-white {
  background-color: rgb(var(--v-theme-surface));
}

// テキストエリアの下部分
:deep(.special-note .tag-container) {
  gap: 16px !important;
}
:deep(.special-note .split-line) {
  width: 1px;
}
:deep(.orx0156-one-line .split-line) {
  display: none;
}
</style>
