/**
 * Or28148:処理ロジック
 * GUI01066_表示順変更予防基本
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO VAN DUONG
 */
export interface Or28148StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * 表示順変更アセスメントテーブルデータ
 */
export interface TableData {
  /**
   * ID
   */
  id: number | null
  /**
   * 空白ラベル（表示非表示あり）
   */
  action?: {
    onewayModelValue: {
      icon: string
      color: string
      density: string
    }
  }
  /**
   * 表示順
   */
  sort: {
    value: number | null
  }
  /**
   * 時間
   */
  time: string
  /**
   * 本人
   */
  person: string
  /**
   * 介護者・家族
   */
  caregiverFamily: string
}
/**
 * 表示順変更アセスメントテーブルデータ
 */
export interface Data {
  /**
   * ID
   */
  id: number | null
  /**
   * 表示順
   */
  sort: number | null
  /**
   * 時間
   */
  time: string
  /**
   * 介護者・家族
   */
  person: string
  /**
   * 介護者・家族
   */
  caregiverFamily: string
  /**
   * チェックボックス
   */
  checkbox?: boolean
}
/**
 * Or28148：有機体：フッターアクションバー（基本型）
 * EventStatus領域用の構造
 */
export interface Or28148EventType {
  /**
   * 閉じるフラグ
   */
  closeFlg?: boolean
  /**
   * 確定フラグ
   */
  comfirmFlg?: boolean
}
