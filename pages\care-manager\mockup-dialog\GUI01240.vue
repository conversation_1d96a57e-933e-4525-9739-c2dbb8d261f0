<script setup lang="ts">
/**
 * GUI01240:有機体:印刷設定
 * GUI01240_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or55130Const } from '~/components/custom-components/organisms/Or55130/Or55130.constants'
import { Or55130Logic } from '~/components/custom-components/organisms/Or55130/Or55130.logic'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01240'
// ルーティング
const routing = 'GUI01240/pinia'
// 画面物理名
const screenName = 'GUI01240'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or55130 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01240' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or55130.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01240',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or55130Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or55130Const.CP_ID(1)]: or55130.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or55130Logic.initialize(or55130.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr55130 = computed(() => {
  // Or55130のダイアログ開閉状態
  return Or55130Logic.state.get(or55130.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or55130)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr55130() {
  // Or55130のダイアログ開閉状態を更新する
  Or55130Logic.state.set({
    uniqueCpId: or55130.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        /**
         * 処理年月日
         */
        processYmd: '2024/11/11',
        /**
         * 基準日
         */
        basicDate: '2024/10/11',
        /**
         * セクション名
         */
        sectionName: '新評価表',
        /**
         * 施設ID
         */
        shisetuId: '1',
        /**
         * 事業者ID
         */
        svJigyoId: '1',
        /**
         * 利用者ID
         */
        userId: '1',
        /**
         * 履歴ID
         */
        assId: '2',
        /**
         * 担当者ID
         */
        tantoId: '2',
        /**
         * 計画書様式
         */
        cksFlg: '3',
        /**
         * 法人ID
         */
        houjinId: '1',
        /**
         * 職員ID
         */
        shokuId: '4',
        /**
         * ヘーダID
         */
        cmoni1Id: '2',
        /**
         * フォーカス設定用イニシャル
         */
        focusSettingInitial: [],
        /**
         * 担当者カウンタ値
         */
        selectedUserCounter: '2',
        /**
         * 担当ケアマネ設定フラグ
         */
        kkjTantoFlg: '1',
      },
    },
  })
}

const local = reactive({
  /**
   * 帳票番号
   */
  prtNo: { value: '1' },
  /**
   * 事業者ID
   */
  svJigyoId: { value: '1' },
  /**
   * 施設ID
   */
  shisetuId: { value: '1' },
  /**
   * セクション名
   */
  sectionName: { value: '退院・退所情報記録書' },
  /**
   * 利用者ID
   */
  userId: { value: '41' },
  /**
   * 履歴ID
   */
   assId: { value: '1' },
  /**
   * 事業所名
   */
  svJigyoKnj: { value: '1' },
  /**
   * 処理年月日
   */
  processYmd: { value: '2025/07/02' },
  /**
   * 担当者カウンタ値
   */
  selectedUserCounter: { value: '2' },
  mo00040Type: {
    modelValue: '',
  },
})

const localOneway = reactive({
  svJigyoId: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '事業者ID',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  shisetuId: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '施設ID',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  sectionName: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: 'セクション名',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  userId: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '利用者ID',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  assId: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '履歴ID',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  svJigyoKnj: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '事業所名',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  processYmd: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '処理年月日',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  focusSettingInitial: {
    showItemLabel: true,
    isVerticalLabel: true,
    readonly: true,
    itemLabel: 'フォーカス設定用イニシャル',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  selectedUserCounter: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '担当者カウンタ値',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  mo00040OnewayType: {
    itemLabel: '50音設定',
    showItemLabel: true,
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
    itemTitle: 'label',
    itemValue: 'value',
    items: [
      { label: '空白', value: '' },
      { label: 'あ行', value: ['あ', 'い', 'う', 'え', 'お'] },
      { label: 'か行', value: ['か', 'き', 'く', 'け', 'こ'] },
      { label: 'さ行', value: ['さ', 'し', 'す', 'せ', 'そ'] },
      { label: 'た行', value: ['た', 'ち', 'つ', 'て', 'と'] },
      { label: 'な行', value: ['な', 'に', 'ぬ', 'ね', 'の'] },
      { label: 'は行', value: ['は', 'ひ', 'ふ', 'へ', 'ほ'] },
      { label: 'ま行', value: ['ま', 'み', 'む', 'め', 'も'] },
      { label: 'や行', value: ['や', 'ゆ', 'よ'] },
      { label: 'ら行', value: ['ら', 'り', 'る', 'れ', 'ろ'] },
      { label: 'わ行', value: ['わ', 'を', 'ん'] },
    ],
  },
})

/**
 *  ボタン押下時の処理(Or51583)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr55130_1() {
  // Or51583_2のダイアログ開閉状態を更新する
  Or55130Logic.state.set({
    uniqueCpId: or55130.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        /**
         * 事業者ID
         */
        svJigyoId: local.svJigyoId.value,

        /**
         * 施設ID
         */
        shisetuId: local.shisetuId.value,

        /**
         * セクション名
         */
        sectionName: local.sectionName.value,

        /**
         * 利用者ID
         */
        userId: local.userId.value,

        /**
         * 履歴ID
         */
         assId: local.assId.value,

        /**
         * 処理年月日
         */
        processYmd: local.processYmd.value,

        /**
         * フォーカス設定用イニシャル(50音)
         */
        focusSettingInitial: local.mo00040Type.modelValue as unknown as string[],
        /**
         * 担当者カウンタ値
         */
        selectedUserCounter: local.selectedUserCounter.value,
      },
    },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr55130"
        >GUI01240_印刷設定
      </v-btn>
      <g-custom-or-55130
        v-if="showDialogOr55130"
        v-bind="or55130"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row
    class="pt-4"
    no-gutters
  >
    <c-v-col>
      <v-btn
        variant="plain"
        to="/care-manager/mockup-dialog/01-format"
        value="ページ遷移サンプル"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row class="background-color">
    <c-v-col cols="4">
      <!-- 事業者ID -->
      <base-mo00045
        v-model="local.svJigyoId"
        :oneway-model-value="localOneway.svJigyoId"
      />

      <!-- 施設ID -->
      <base-mo00045
        v-model="local.shisetuId"
        :oneway-model-value="localOneway.shisetuId"
      />

      <!-- セクション名 -->
      <base-mo00045
        v-model="local.sectionName"
        :oneway-model-value="localOneway.sectionName"
      />

      <!-- 利用者ID -->
      <base-mo00045
        v-model="local.userId"
        :oneway-model-value="localOneway.userId"
      />

      <!-- 履歴ID -->
      <base-mo00045
        v-model="local.assId"
        :oneway-model-value="localOneway.assId"
      />

      <!-- 事業所名 -->
      <base-mo00045
        v-model="local.svJigyoKnj"
        :oneway-model-value="localOneway.svJigyoKnj"
      />

      <!-- 処理年月日 -->
      <base-mo00045
        v-model="local.processYmd"
        :oneway-model-value="localOneway.processYmd"
      />
      <div class="d-flex background-color">
        <!-- セレクトフィールド -->
        <base-mo-00040
          v-model="local.mo00040Type"
          :oneway-model-value="localOneway.mo00040OnewayType"
        />
      </div>
      <!-- 担当者カウンタ値 -->
      <base-mo00045
        v-model="local.selectedUserCounter"
        :oneway-model-value="localOneway.selectedUserCounter"
      ></base-mo00045>
    </c-v-col>
    <c-v-col
      cols="4"
      class="pt-8"
    >
      <v-btn
        variant="elevated"
        @click="onClickOr55130_1"
        >こちらをクリック 疎通起動
      </v-btn>
    </c-v-col>
  </c-v-row>
</template>
