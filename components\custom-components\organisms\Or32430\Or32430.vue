<script setup lang="ts">
/**
 * Or32430：有機体：GUI00675_［情報収集］画面（3）画面
 *
 * @description
 * ［情報収集］画面（3）
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51773Const } from '../Or51773/Or51773.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or27447Logic } from '../Or27447/Or27447.logic'
import { Or27447Const } from '../Or27447/Or27447.constants'
import { Or32430Const } from './Or32430.constants'
import { useScreenTwoWayBind, useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type {
  InfoCollectionInfoType,
  RirekiInfo,
} from '~/types/cmn/business/components/TeX0005Type'
import type { Or32430OnewayType, Or32430Type } from '~/types/cmn/business/components/Or32430Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Or21744StateType } from '~/components/base-components/organisms/Or21744/Or21744.type'
import type { Mo01280OnewayType } from '~/types/business/components/Mo01280Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import type { Or27447OnewayType, Or27447Type } from '~/types/cmn/business/components/Or27447Type'
import type { OrX0163OnewayType } from '~/types/cmn/business/components/OrX0163Type'
import { useValidation } from '@/utils/useValidation'
const { byteLength } = useValidation()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or32430OnewayType
  uniqueCpId: string
  modelValue: Or32430Type
}

const props: Props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

const { t } = useI18n()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const or27447 = ref({ uniqueCpId: Or27447Const.CP_ID(0) })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })
const contentRef = ref<HTMLDivElement | null>(null)
const selectedRow = ref<{ groupIndex: number; subIndex: number } | null>(null)
/**
 * 親画面からの初期値
 */
const or27447Data: Or27447OnewayType = {
  svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
}

const defaultOnewayModelValue: Or32430OnewayType = {
  periodManageFlag: '0',
  rirekiInfo: {} as RirekiInfo,
  deleteFlg: false,
}

const localOneway = reactive({
  or32430Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // アセスメント表タイトル
  mo01338Oneway: {
    value: '',
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentTitle',
      itemStyle: 'font-size: 18px',
    } as CustomClass,
  } as Mo01338OnewayType,
  // すべて[〇]コンポーネント
  mo00611OnewayAllRound: {
    btnLabel: t('btn.all-round-figma'),
    width: '86px',
    height: '29px',
  },
  // すべて[×]コンポーネント
  mo00611OnewayAllWrong: {
    btnLabel: t('btn.all-wrong-figma'),
    width: '86px',
    height: '29px',
  },
  // 全解除コンポーネント
  mo00611OnewayCancelAll: {
    btnLabel: t('btn.full-release'),
    width: '86px',
    height: '29px',
  },

  mo01280Oneway: {
    // デフォルト値の設定
    maxLength: 4000,
    rows: '3',
  } as Mo01280OnewayType,

  mo01280TwoRowOneway: {
    // デフォルト値の設定
    maxLength: 20,
    rows: '2',
  } as Mo01280OnewayType,
  // ******Visioラジオグループセクション******
  cnsiderOneway: {
    customClass: new CustomClass({ outerClass: 'radio-style' }),
    showItemLabel: false,
    itemLabel: '',
    name: 'body',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  or21744Oneway: {
    width: '100px',
    minWidth: '100px',
    disabled: false,
  } as Or21744StateType,
  or51775OnewayTypeOther: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '3',
    t3Cd: '',
    tableName: 'cpn_tuc_myg_ass2',
    columnName: '',
    assessmentMethod: '2',
    inputContents: '',
    shokuinId: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
  orX0163Oneway: {
    height: '75px',
    readOnly: false,
    maxlength: '4000',
    rules: [byteLength(4000)],
    contentStyle:'padding: 11.5px 11.5px;line-height: 17px;letter-spacing: -0.04em;',
  } as OrX0163OnewayType,
})

const local = reactive({
  or32430: {
    ...props.modelValue,
  } as Or32430Type,
})

/**
 *  ラジオボタン初期化
 */
function initCodes() {
  localOneway.cnsiderOneway.items?.push({
    label: t('label.wrong'),
    value: Or32430Const.DEFAULT.WRONGSELECTED,
  })
  localOneway.cnsiderOneway.items?.push({
    label: t('label.circle'),
    value: Or32430Const.DEFAULT.RIGHTSELECTED,
  })
}

/**************************************************
 * Pinia
 **************************************************/
const { refValue: sortList } = useScreenTwoWayBind<Record<string, InfoCollectionInfoType[]>>({
  cpId: Or32430Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
sortList.value = { ...sortList.value }

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * ウォッチャー
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(() => {
  // 初期情報取得
  void init()
})

/**
 * 初期化
 */
const init = () => {
  // ラジオボタン初期化
  void initCodes()

  // 階層1タイトルラベル(情報収集画面詳細情報の「第1階層ID」の値、情報収集画面詳細情報の「第1階層名称」の値の組合文字列)
  if (sortList.value?.['1'] !== undefined) {
    localOneway.mo01338Oneway.value =
      sortList.value['1'][0].level1Id + '　' + sortList.value['1'][0].level1Knj
  }
}

/**
 * 全て[〇]ボタン押下
 *
 */
function onAllSelect1Click() {
  Object.entries(sortList.value!).map((group) => {
    group[1].map((item) => {
      if (item.shosiki1Flg !== Or32430Const.DEFAULT.FORMAT_THREE) {
        item.kentoFlg = Or32430Const.DEFAULT.RIGHTSELECTED
      }
    })
  })
}

/**
 * 全て[×]ボタン押下
 *
 */
function onAllSelect2Click() {
  Object.entries(sortList.value!).map((group) => {
    group[1].map((item) => {
      if (item.shosiki1Flg !== Or32430Const.DEFAULT.FORMAT_THREE) {
        item.kentoFlg = Or32430Const.DEFAULT.WRONGSELECTED
      }
    })
  })
}

/**
 * 全解除ボタン押下
 *
 */
function onNoSelectClick() {
  Object.entries(sortList.value!).map((group) => {
    group[1].map((item) => {
      if (item.shosiki1Flg !== Or32430Const.DEFAULT.FORMAT_THREE) {
        item.kentoFlg = Or32430Const.DEFAULT.UNSELECTED
      }
    })
  })
}

// 選択した行の第１階層Index
const selectedItemIndex = ref<string>('')

// 選択した行の第２階層Index
const selectedItemSubIndex = ref<number>(-1)

// 選択した行のメモ１ORメモ２
const selectedItemType = ref<number>(-1)
/**
 * 「ケアマネ入力支援アイコンボタン」押下onClickOther
 *
 * @param title - id
 *
 * @param index - 第１階層Index
 *
 * @param subIndex - 第２階層Index
 *
 * @param type - メモ１ORメモ２
 */
function onClickOther(title: string, index: string, subIndex: number, type: number) {
  // その他1の入力支援ポップアップを開く
  or51775Other.value.modelValue = ''

  selectedItemIndex.value = index
  selectedItemSubIndex.value = subIndex
  selectedItemType.value = type

  // タイトルを「その他1」または「その他2」に設定
  localOneway.or51775OnewayTypeOther.title = title
  // 入力支援ポップアップを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理(Or27447)
 *
 * @param index - 選択した行のindex
 *
 * @param subIndex - 選択した行のsubindex
 */
function onClickOr27447(index: string, subIndex: number) {
  selectedItemIndex.value = index
  selectedItemSubIndex.value = subIndex
  // Or27447のダイアログ開閉状態を更新する
  Or27447Logic.state.set({
    uniqueCpId: or27447.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr27447 = computed(() => {
  return Or27447Logic.state.get(or27447.value.uniqueCpId)?.isOpen ?? false
})

//課題t入力
const or51775Other = ref({ modelValue: '' })

//課題t入力

const or27447Type = ref<Or27447Type>({
  konnanKnj: '',
})

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or51773Const.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  debugger
  // メモ１の場合
  if (selectedItemType.value === 1) {
    if (sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo1Knj) {
      sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo1Knj.value =
        setOrAppendValue(
          sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].memo1Knj.value ?? '',
          data
        )
    }
  }
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

watch(
  () => local.or32430,
  () => {
    emit('update:modelValue', local.or32430)
  },
  { deep: true }
)

/**
 * 「困難度入力補助アイコン」押下
 *
 * @param data -戻り値
 */
const handleor27447Confirm = (data: string) => {
  debugger
  if (sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].konnanLevel) {
    sortList.value![selectedItemIndex.value][selectedItemSubIndex.value].konnanLevel.value = data
  }
}

/**
 * スクロールバーのスクロール
 *
 * @param event - WheelEvent
 */
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()

  const delta = Math.sign(event.deltaY)

  const contentElement = contentRef.value
  if (!contentElement) return

  const currentScroll = contentElement.scrollTop
  const maxScroll = contentElement.scrollHeight - contentElement.clientHeight

  let newScroll = currentScroll + delta * 50

  if (newScroll < 0) newScroll = 0
  if (newScroll > maxScroll) newScroll = maxScroll

  if (newScroll !== currentScroll) {
    contentElement.scrollTo({
      top: newScroll,
      behavior: 'auto',
    })
  }
}

/**
 * 行を選択したときの処理
 *
 * @param groupIndex - 子行
 *
 * @param subIndex - 子行
 */
function onSelectRow(groupIndex: number, subIndex: number) {
  if (selectedRow.value?.groupIndex === groupIndex && selectedRow.value?.subIndex === subIndex) {
    selectedRow.value = null
  } else {
    selectedRow.value = { groupIndex, subIndex }
  }
}
</script>

<template>
  <!-- 対象期間管理しない場合、非表示 -->
  <c-v-row
    v-if="
      props.onewayModelValue.periodManageFlag !== Or32430Const.PLANNING_PERIOD_NO_MANAGE ||
      props.onewayModelValue.copyFlg === true
    "
    no-gutters
    style="padding-top: 8px"
  >
    <c-v-col
      cols="6"
      class="h-100 px-2"
    >
      <c-v-row
        no-gutters
        style="padding-top: 8px">
        <base-mo01338
          :oneway-model-value="localOneway.mo01338Oneway"
          style="background: transparent"
        ></base-mo01338>
      </c-v-row>
    </c-v-col>
    <!-- 3ボタン -->
    <c-v-col
      cols="6"
      class="h-100 px-2"
    >
      <c-v-row
        no-gutters
        class="top-button"
        justify="end"
      >
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAllRound"
          class="mx-1"
          @click="onAllSelect1Click"
        />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAllWrong"
          class="mx-1"
          @click="onAllSelect2Click"
        />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayCancelAll"
          class="mx-1"
          @click="onNoSelectClick"
        />
      </c-v-row>
    </c-v-col>
    <!-- タイトル -->
    <c-v-row no-gutters>
      <c-v-col class="h-100">
        <c-v-row
          no-gutters
          class="row-tl-border"
          style="width: calc(100% - 15px); height: 64px"
        >
          <c-v-col
            cols="auto"
            class="col-br-border align-items-center-title tbl-title-bg col1"
            style="text-align: center"
            ><label class="label-style">{{ t('label.no') }}</label></c-v-col
          >
          <c-v-col class="col-br-border align-items-center-title tbl-title-bg col2"
            ><label class="label-style">{{ t('label.info-collection-info-item') }}</label></c-v-col
          >
          <c-v-col
            v-show="
              localOneway.or32430Oneway.rirekiInfo &&
              localOneway.or32430Oneway.rirekiInfo.kaiteiKbn ===
                Or32430Const.DEFAULT.KAITEIKBN_ZERO
            "
            cols="auto"
            class="col-br-border tbl-title-bg align-items-center-title col4"
            style="text-align: center"
            ><label class="label-style">{{ t('label.info-collection-consider') }}</label></c-v-col
          >
          <c-v-col
            cols="auto" class="col-br-border tbl-title-bg col3">
            <c-v-row
              no-gutters
              style="height: 31px"
            >
              <c-v-col class="col-br-border-inside-bottom align-items-center-title  tbl-title-bg"
                ><label class="label-style">{{
                  t('label.info-collection-concrete-situation')
                }}</label></c-v-col
              >
            </c-v-row>
            <c-v-row
              no-gutters
              style="height: 31px"
            >
              <c-v-col cols="auto" class="col-br-border-inside-right align-items-center-title  tbl-title-bg col17"
                ><label class="label-style">{{
                    t('label.current-situation')
                  }}</label></c-v-col
              >
              <c-v-col cols="auto" class="align-items-center-title col18"
                ><label class="label-style">{{
                    t('label.difficulty')
                  }}</label></c-v-col
              >
            </c-v-row>
          </c-v-col>
          <c-v-col
            v-show="
              !localOneway.or32430Oneway.rirekiInfo ||
              (localOneway.or32430Oneway.rirekiInfo &&
              localOneway.or32430Oneway.rirekiInfo.kaiteiKbn !==
                Or32430Const.DEFAULT.KAITEIKBN_ZERO)
            "
            cols="auto"
            class="col-br-border tbl-title-bg align-items-center-title col4"
            style="text-align: center"
            ><label class="label-style">{{ t('label.info-collection-consider') }}</label></c-v-col
          >
        </c-v-row>
        <!-- body -->
        <div
          ref="contentRef"
          class="or32430-main-div"
        >
          <template
            v-for="(group, index) in sortList!"
            :key="index"
          >
            <div
              v-if="!props.onewayModelValue.deleteFlg"
              no-gutters
              class="row-l-border"
            >
              <c-v-row
                no-gutters
              >
                <c-v-col
                  cols="12"
                  style="height: 32px;"
                  class="col-br-border tbl-title-bg align-items-center label-style font-bold"
                  ><div class="pl-3">{{ group[0].level2Knj }}</div></c-v-col
                >
              </c-v-row>
              <c-v-row
                v-for="(item, subIndex) in group"
                :key="subIndex"
                no-gutters
                class="or32430-row"
                :class="{
                'select-row':
                    selectedRow?.groupIndex === Number(index) && selectedRow?.subIndex === subIndex,
                }"
                @click="onSelectRow(Number(index), subIndex)"
              >
                <!-- No -->
                <c-v-col
                  v-if="item.shosiki1Flg === Or32430Const.DEFAULT.FORMAT_ONE"
                  cols="auto"
                  class="col-br-border align-items-center col1 text-bg"
                  style="justify-content: center"
                  ><label>{{ item.koumokuNo }}</label>
                </c-v-col>
                <c-v-col
                  v-else-if="item.shosiki1Flg === Or32430Const.DEFAULT.FORMAT_TWO"
                  cols="auto"
                  class="col-br-border align-items-center col1 text-bg"
                  style="justify-content: center"
                  ><label class="label-style">{{ item.koumokuNo }}</label>
                </c-v-col>
                <!-- 情報項目 -->
                <c-v-col
                  v-if="item.shosiki1Flg === Or32430Const.DEFAULT.FORMAT_ONE"
                  cols="auto"
                  class="col-br-border align-items-center col2 text-bg"
                  ><div class="pl-3 label-style">{{ item.level3Knj }}</div></c-v-col
                >
                <c-v-col
                  v-else-if="item.shosiki1Flg === Or32430Const.DEFAULT.FORMAT_TWO"
                  cols="auto"
                  class="col-br-border align-items-center col2 text-bg"
                  ><div class="pl-3 label-style">{{ item.level3Knj }}</div></c-v-col
                >
                <c-v-col
                  v-else
                  cols="auto"
                  class="col-br-border align-items-center col_item_3 text-bg"
                  ><div class="pl-3 label-style">{{ item.level3Knj }}</div></c-v-col
                >
                <!-- 検討1 -->
                <c-v-col
                  v-if="
                    item.shosiki1Flg !== Or32430Const.DEFAULT.FORMAT_THREE &&
                    item.shosiki2Flg !== Or32430Const.DEFAULT.FORMAT_FIVE &&
                    localOneway.or32430Oneway.rirekiInfo.kaiteiKbn ===
                      Or32430Const.DEFAULT.KAITEIKBN_ZERO
                  "
                  cols="auto"
                  class="col-br-border radio-center text-area-bg col4"
                >
                  <div class="radio1">
                    <base-mo00039
                      v-if="item.shosiki2Flg !== Or32430Const.DEFAULT.FORMAT_FIVE"
                      v-model="item.kentoFlg"
                      style="background: inherit"
                      :oneway-model-value="localOneway.cnsiderOneway"
                    />
                  </div>
                </c-v-col>
                <c-v-col
                  v-if="
                    item.shosiki1Flg !== Or32430Const.DEFAULT.FORMAT_THREE &&
                    item.shosiki2Flg === Or32430Const.DEFAULT.FORMAT_FIVE &&
                    localOneway.or32430Oneway.rirekiInfo.kaiteiKbn ===
                      Or32430Const.DEFAULT.KAITEIKBN_ZERO
                  "
                  cols="auto"
                  class="col-br-border text-area-bg col4"
                >
                </c-v-col>
                <!-- 具体的状況 -->
                <c-v-col
                  v-if="item.shosiki1Flg !== Or32430Const.DEFAULT.FORMAT_THREE"
                  cols="auto"
                  class="col-br-border text-area-bg align-items-center col15"
                >
                  <div style="display: flex;width: 100%">
                    <g-custom-or-x-0163
                      v-model="item.memo1Knj"
                      :oneway-model-value="localOneway.orX0163Oneway"
                      @on-click-edit-btn="onClickOther(item.level3Knj, index, subIndex, 1)"
                    ></g-custom-or-x-0163>
                  </div>
                </c-v-col>
                <c-v-col
                  v-if="item.shosiki1Flg !== Or32430Const.DEFAULT.FORMAT_THREE"
                  cols="auto"
                  class="col-br-border text-area-bg align-items-center col16"
                >
                  <div style="display: flex;width: 100%">
                    <g-custom-or-x-0163
                      v-model="item.konnanLevel"
                      :oneway-model-value="localOneway.orX0163Oneway"
                      @on-click-edit-btn="onClickOr27447(index, subIndex)"
                    ></g-custom-or-x-0163>
                  </div>
                </c-v-col>
                <c-v-col
                  v-if="
                    item.shosiki1Flg === Or32430Const.DEFAULT.FORMAT_THREE &&
                    item.shosiki2Flg === Or32430Const.DEFAULT.FORMAT_ZERO
                  "
                  cols="auto"
                  class="col-br-border text-area-bg align-items-center col_status_3"
                >
                  <div style="display: flex;width: 100%">
                    <g-custom-or-x-0163
                      v-model="item.memo1Knj"
                      :oneway-model-value="localOneway.orX0163Oneway"
                      @on-click-edit-btn="onClickOther(item.level3Knj, index, subIndex, 1)"
                    ></g-custom-or-x-0163>
                  </div>
                </c-v-col>
                <!-- 検討2 -->
                <c-v-col
                  v-if="
                    item.shosiki1Flg !== Or32430Const.DEFAULT.FORMAT_THREE &&
                    item.shosiki2Flg !== Or32430Const.DEFAULT.FORMAT_FIVE &&
                    (!localOneway.or32430Oneway.rirekiInfo ||
                    localOneway.or32430Oneway.rirekiInfo.kaiteiKbn !==
                      Or32430Const.DEFAULT.KAITEIKBN_ZERO)
                  "
                  cols="auto"
                  class="col-br-border radio-center text-area-bg col4"
                >
                  <div class="radio1 pl-1">
                    <base-mo00039
                      v-if="item.shosiki2Flg !== Or32430Const.DEFAULT.FORMAT_FIVE"
                      v-model="item.kentoFlg"
                      style="background: inherit"
                      :oneway-model-value="localOneway.cnsiderOneway"
                    />
                  </div>
                </c-v-col>
                <c-v-col
                  v-if="
                    item.shosiki1Flg !== Or32430Const.DEFAULT.FORMAT_THREE &&
                    item.shosiki2Flg === Or32430Const.DEFAULT.FORMAT_FIVE &&
                    localOneway.or32430Oneway.rirekiInfo.kaiteiKbn ===
                      Or32430Const.DEFAULT.KAITEIKBN_ONE
                  "
                  cols="auto"
                  class="col-br-border text-area-bg col4"
                >
                </c-v-col>
              </c-v-row>
            </div>
          </template>
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-row>
  <div
    v-if="props.onewayModelValue.copyFlg === true"
    class="overlay"
    @wheel="handleWheel"
  ></div>
  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Other"
    :oneway-model-value="localOneway.or51775OnewayTypeOther"
    @confirm="handleOr51775Confirm"
  >
  </g-custom-or-51775>
  <g-custom-or-27447
    v-if="showDialogOr27447"
    v-bind="or27447"
    v-model="or27447Type"
    :oneway-model-value="or27447Data"
    @update:model-value="handleor27447Confirm"
  />
</template>

<style lang="scss" scoped>
@use '@/styles/base-data-table-list.scss';

:deep(.ml-4) {
  margin-left: 0px !important;
}

:deep(.v-selection-control-group) {
  flex-direction: row-reverse !important;
}

:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 36px !important;
}

.buttonActive {
  background: #dbeefe;
  color: white;
  border-color: black;

  :deep(.v-btn__content > span) {
    color: rgb(var(--v-theme-black-500)) !important;
  }
}

.radio1 {
  :deep(.v-selection-control-group > div) {
    margin-bottom: -6px;
    margin-top: -8px;
    margin-right: 10px !important;
  }
}

.row-tl-border {
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.row-l-border {
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-br-border {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-br-border-inside-bottom {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-br-border-inside-right {
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-r-border {
  border-bottom: 1px white solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-pl {
  padding-left: 4px !important;
}

.tbl-title-bg {
  background-color: #dbeefe;
}

.top-button {
  padding-bottom: 16px;
  padding-right: 3px;
}

.font-bold {
  font-weight: bold !important;
}

.align-items-center {
  display: flex;
  align-items: center;
}

.align-items-center-title {
  display: flex;
  align-items: center;
  justify-content: center;
}
.radio-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.or32430-main-div {
  overflow-y: hidden;
  // max-height: 490px;
  scrollbar-gutter: stable;
  position: relative;
  z-index: 999;
}

.or32430-row {
  height: 76px;
}

.or32430-title-row {
  height: 21px;
}

:deep(.col1) {
  width: 4%;
  flex-shrink: 0;
}

:deep(.col2) {
  width: 28%;
  flex-shrink: 0;
}

:deep(.col15) {
  width: 40%;
  flex-shrink: 0;
}

:deep(.col16) {
  width: 13%;
  flex-shrink: 0;
}
:deep(.col17) {
  width: 75.6%;
  flex-shrink: 0;
}

:deep(.col18) {
  width: 24.4%;
  flex-shrink: 0;
}
:deep(.col3) {
  width: 53%;
  flex-shrink: 0;
}

:deep(.col4) {
  width: 15%;
  flex-shrink: 0;
}

:deep(.col_item_3) {
  width: 32%;
  flex-shrink: 0;
}

:deep(.col_status_3) {
  width: 68%;
  flex-shrink: 0;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 1000;
  pointer-events: auto;
  cursor: not-allowed;
}
.text-bg {
  background-color: rgb(219, 238, 254);
}
.text-area-bg {
  background-color: rgb(255, 255, 255);
}
.full-width-field {
  padding: 0 3px !important;
}
// 選択した行のCSS
.select-row {
  background: rgb(var(--v-theme-blue-100)) !important;
}

.label-style {
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 16px;
  letter-spacing: -0.04em;
}
</style>
