<script setup lang="ts">
/**
 * Or27554:有機体:印刷設定画面
 * GUI00953_印刷設定画面
 *
 * @description
 * 印刷設定画面の処理
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch, watchEffect } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26326Const } from '../Or26326/Or26326.constants'
import { Or28780Logic } from '../Or28780/Or28780.logic'
import { Or26328Const } from '../Or26328/Or26328.constants'
import { Or28780Const } from '../Or28780/Or28780.constants'
import type { Mo00045Type } from '../Or00386/Or00386.type'
import { Or18615Logic } from '../Or18615/Or18615.logic'
import { Or18615Const } from '../Or18615/Or18615.constants'
import type { Mo00018Type } from '../Or26828/Or26828.type'
import { Or26331Const } from '../Or26331/Or26331.constants'
import type { OrX0143TableData } from '../OrX0143/OrX0143.type'
import type { OrX0130TableType } from '../OrX0130/OrX0130.type'
import { Or26331Logic } from '../Or26331/Or26331.logic'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import { OrX0130Logic } from '../OrX0130/OrX0130.logic'
import { OrX0117Logic } from '../OrX0117/OrX0117.logic'
import { OrX0117Const } from '../OrX0117/OrX0117.constants'
import { OrX0143Logic } from '../OrX0143/OrX0143.logic'
import { Or26328Logic } from '../Or26328/Or26328.logic'
import { Or27554Const } from './Or27554.constants'
import type { Or27554StateType } from './Or27554.type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import {
  hasPrintAuth,
  usePrint,
  useScreenOneWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
  type HistoryInfo,
} from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type {
  PrintSelectInEntity,
  IPrintInfo,
  PrintOnewayEntity,
} from '~/repositories/cmn/entities/PrintSelectEntity'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type {
  ImplementationPlan1PrintSettingsInitUpdateOutEntity,
  KikanRirekiData,
} from '~/repositories/cmn/entities/ImplementationPlan1PrintSettingsInitUpdateEntity'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { OrX0143OnewayType } from '~/types/cmn/business/components/OrX0143Type'
import { OrX0143Const } from '~/components/custom-components/organisms/OrX0143/OrX0143.constants'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { Or10016Logic } from '~/components/custom-components/organisms/Or10016/Or10016.logic'
import { Or10016Const } from '~/components/custom-components/organisms/Or10016/Or10016.constants'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import type { PrintUserChangeSelectInEntity } from '~/repositories/cmn/entities/PrintUserChangeSelectEntity'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { ImplementationPlan1PrintSettingsUserChangeSelectOutEntity } from '~/repositories/cmn/entities/ImplementationPlan1PrintSettingsUserChangeSelectEntity'
import type { Or21814EventType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type {
  PrintSubjectSelectInEntity,
  UserInfo,
} from '~/repositories/cmn/entities/PrintSubjectSelectEntity'
import type { InWebEntity } from '~/repositories/AbstructWebRepository'
import { reportOutputType } from '~/utils/useReportUtils'
import type { Or28780Type } from '~/types/cmn/business/components/Or28780Type'
import type {
  PrtHistoryList,
  ImplementationPlan1PrintSettingsSubjectSelectOutEntity,
} from '~/repositories/cmn/entities/ImplementationPlan1PrintSettingsSubjectSelectEntity'
import type { Or26328OnewayType } from '~/types/cmn/business/components/Or26328Type'
import type { ImplementationPlan1PrintSettingsInfoUpdateInEntity } from '~/repositories/cmn/entities/ImplementationPlan1PrintSettingsInfoUpdateEntity'
import type { ImplementationPlan1InitMasterInfo } from '~/repositories/cmn/entities/ImplementationPlan1SelectEntity'
import { useValidation } from '@/utils/useValidation'

const { t } = useI18n()
const { byteLength } = useValidation()
const { setChildCpBinds } = useScreenUtils()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: PrintOnewayEntity<ImplementationPlan1InitMasterInfo>
  uniqueCpId: string
  parentCpId: string
}
const props = defineProps<Props>()
const or26326_1 = ref({ uniqueCpId: '' })
const or26328_1 = ref({ uniqueCpId: '' })
const or28780_1 = ref({ uniqueCpId: '' })
const or18615_1 = ref({ uniqueCpId: '' })
const or26331_1 = ref({ uniqueCpId: '' })
const orX0130_1 = ref({ uniqueCpId: '' })
const orX0143_1 = ref({ uniqueCpId: '' })
const or10016_1 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const orX0117_1 = ref({ uniqueCpId: '' })
const orX0145_1 = ref({ uniqueCpId: '' })
const or26328Ref = ref<{
  isValid: () => Promise<boolean>
}>()
const or18615Ref = ref<{
  isValid: () => Promise<boolean>
}>()

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or26326Const.CP_ID(1)]: or26326_1.value,
  [Or26328Const.CP_ID(1)]: or26328_1.value,
  [Or28780Const.CP_ID(1)]: or28780_1.value,
  [Or18615Const.CP_ID(1)]: or18615_1.value,
  [Or26331Const.CP_ID(1)]: or26331_1.value,
  [OrX0130Const.CP_ID(1)]: orX0130_1.value,
  [OrX0143Const.CP_ID(1)]: orX0143_1.value,
  [Or10016Const.CP_ID(1)]: or10016_1.value,
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [OrX0117Const.CP_ID(1)]: orX0117_1.value,
  [OrX0145Const.CP_ID(1)]: orX0145_1.value,
})

// ダイアログ表示フラグ
const showDialogOr10016 = computed(() => {
  // Or10016のダイアログ開閉状態
  return Or10016Logic.state.get(or10016_1.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117_1.value.uniqueCpId)?.isOpen ?? false
})

// ローカル双方向bind
const local = reactive({
  or27554: {
    // 印刷設定情報リスト
    prtList: [] as IPrintInfo[],
    // 期間履歴情報リスト
    kikanRirekiList: [] as KikanRirekiData[],
  },
  // 帳票タイトル
  titleInput: {
    value: '',
  } as Mo00045Type,
  // 日付印刷
  or28780: {
    // 日付印刷区分
    mo00039Type: '',
    // 指定日
    mo00020Type: {
      value: '',
    },
  } as Or28780Type,
  // 出力帳票名一覧
  mo01334: {
    value: '',
    values: [],
  } as Mo01334Type,
  // 敬称テキストボックス
  textInput: {
    value: '',
  } as Mo00045Type,
  // 敬称テキストボックスの制御
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  // 記入用シートを印刷する
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  // 作成者を印刷する
  mo00018TypePrintAuthor: {
    modelValue: false,
  } as Mo00018Type,
  // 基準日
  mo00020TypeKijunbi: {
    value: systemCommonsStore.getSystemDate,
  } as Mo00020Type,
})

const localOneway = reactive({
  or27554Oneway: {
    ...props.onewayModelValue,
  },
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    maxWidth: '1420px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  // 出力帳票名一覧
  mo01334Oneway: {
    headers: [
      {
        title: t('label.report'),
        key: 'prtTitle',
        sortable: false,
        minWidth: '100',
      },
    ],
    items: [],
    height: 655,
  } as Mo01334OnewayType,
  or26328Oneway: {
    maxLength: '41',
    rules: [byteLength(41)],
  } as Or26328OnewayType,
  // 印鑑欄ボタン
  mo00610OneWay: {
    btnLabel: t('btn.seal-column'),
    disabled: false,
  } as Mo00610OnewayType,
  // 日付印刷
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  // 印刷オプション
  // 敬称を変更するチェックボックス
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 敬称テキストボックス
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '65',
    maxLength: '4',
    rules: [byteLength(4)],
  } as Mo00045OnewayType,
  // 記入用シートを印刷するチェックボックス
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 作成者を印刷するチェックボックス
  mo00018OneWayPrintPrintAuthor: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-author'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 利用者選択ラジオボタン
  mo00039OneWayUserSelectType: {
    name: 'user-select',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  // 履歴選択ラジオボタン
  mo00039OneWayHistorySelectType: {
    name: 'history-select',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  // 印刷設定画面利用者一覧
  orX0130Oneway: {
    selectMode: Or27554Const.TANI,
    tableStyle: 'width:270px',
  } as OrX0130OnewayType,
  // 履歴一覧情報
  orX0143Oneway: {
    // 期間管理フラグ
    kikanFlg: '1',
    // 単一複数フラグ(0:単一,1:複数)
    singleFlg: Or27554Const.TANI,
    tableStyle: 'width:335px',
    kikanRirekiTitleList: [
      // 作成日
      { title: 'create-date', width: '120', key: 'createYmd' },
      // 作成者
      { title: 'author', width: '120', key: 'shokuKnj' },
      // ケース番号
      { title: 'caseNo', width: '120', key: 'caseNo' },
      // 改訂
      { title: 'revision', width: '80', key: 'kaiteiKnj' },
    ],
    // 履歴情報
    rirekiList: [] as OrX0143TableData[],
  } as OrX0143OnewayType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  // 印刷ボタン
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  // GUI01110［印鑑欄設定］画面
  or10016Oneway: {} as Or10016OnewayType,
  // 印刷設定帳票出力状態リスト
  orX0117Oneway: {
    reportId: '',
    type: '1',
    reportData: {} as InWebEntity,
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
  // 担当ケアマネプルダウン
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    // TODO 前の画面担当ケアマネのインデックス取得
    selectedUserCounter: '',
    customClass: {
      labelClass: 'tanto-label pb-2',
    } as CustomClass,
  } as OrX0145OnewayType,
})
// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27554Const.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27554StateType>({
  cpId: Or27554Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27554Const.IS_OPEN
    },
  },
})

// 印刷共通処理
const printCom = usePrint()
// 印刷権限
const prtFlg: boolean = await hasPrintAuth()

// 利用者列幅
const userCols = ref<number>(4)
// 選択した利用者データ
const selectedUserList = ref<OrX0130TableType[]>([])
// 選択した履歴データ
const selectedRirekiData = ref<KikanRirekiData[]>([])

// 担当ケアマネプルダウンmodelValue
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(async () => {
  // 利用者列幅の設定
  userCols.value = 6
  await initCodes()
  await getPrintSettingList()

  setChildrenValue()
})

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputParam: PrintSelectInEntity = {
    // セクション名:親画面.セクション名
    sectionName: localOneway.or27554Oneway.sectionName,
    // 職員ID:親画面.職員ID
    shokuId: localOneway.or27554Oneway.shokuId,
    // 法人ID:親画面.法人ID
    houjinId: localOneway.or27554Oneway.houjinId,
    // 施設ID:親画面.施設ID
    shisetuId: localOneway.or27554Oneway.shisetuId,
    // 事業所ID:親画面.事業所ID
    svJigyoId: localOneway.or27554Oneway.svJigyoId,
    // システムコード:共通情報.システムコード
    gsyscd: localOneway.or27554Oneway.systemCode,
    // 利用者ID:親画面.利用者ID
    userId: localOneway.or27554Oneway.userId,
  }

  // バックエンドAPIから初期情報取得
  const ret = await printCom.doPrintGet<
    PrintSelectInEntity,
    IPrintInfo,
    KikanRirekiData,
    ImplementationPlan1PrintSettingsInitUpdateOutEntity
  >(inputParam, 'implementationPlan1PrintSettingsInitUpdate')
  // 印刷設定情報リスト、期間履歴情報リストを取得
  printCom.doGetPrintData<
    ImplementationPlan1PrintSettingsInitUpdateOutEntity,
    IPrintInfo,
    KikanRirekiData
  >(ret, local.or27554)

  // 印刷設定情報に印刷設定情報リスト の1件目を設定する
  const firstItem = local.or27554.prtList.at(0)
  if (!firstItem) {
    return
  }

  // 出力帳票名一覧
  const mo01334OnewayList: Mo01334Items[] = [
    {
      id: firstItem.prtNo,
      mo01337OnewayReport: {
        value: firstItem.prtTitle,
        unit: '',
      } as Mo01337OnewayType,
      ...firstItem,
    },
  ]
  // Onewayの印刷設定情報リストを設定
  localOneway.mo01334Oneway.items = mo01334OnewayList

  // 一件目選択
  local.mo01334.value = firstItem.prtNo
  // 帳票タイトル
  local.titleInput.value = firstItem.prtTitle ?? ''
  // 日付印刷の日付印刷区分
  local.or28780.mo00039Type = firstItem.prnDate ?? ''
  // 日付印刷の指定日
  local.or28780.mo00020Type.value = systemCommonsStore.getSystemDate ?? ''

  // 敬称を変更するチェックボックス
  local.mo00018TypeChangeTitle.modelValue =
    firstItem.param03 === Or27554Const.CHECK_ON ? true : false
  // 敬称テキストフィールド
  local.textInput.value = firstItem.param04 ?? ''
  // 画面.利用者選択方法が「単一」 、且つ、 画面.履歴選択方法が「単一」の場合、画面.記入用シートを印刷する:活性
  local.mo00018TypePrintTheForm.modelValue = false
  // 作成者を印刷するチェックボックス
  local.mo00018TypePrintAuthor.modelValue =
    firstItem.param05 === Or27554Const.CHECK_ON ? true : false

  // orX0130 利用者一覧
  // 担当ケアマネ
  localOneway.orX0130Oneway.tantouCareManager = localOneway.or27554Oneway.tantoShokuId
  // TODO 親画面.50音（※リスト）

  // 履歴一覧情報
  localOneway.orX0143Oneway.kikanFlg = localOneway.or27554Oneway.kikanFlg ?? ''
  localOneway.orX0143Oneway.rirekiId = localOneway.or27554Oneway.rirekiId ?? ''
  localOneway.orX0143Oneway.rirekiList = local.or27554.kikanRirekiList

  // 担当ケアマネの制御
  localOneway.orX0145Oneway.disabled = localOneway.or27554Oneway.tantoShokuId !== '0'
}

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分(コード区分:482)
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分(コード区分:487)
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分ラジオボタン
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  // 利用者選択ラジオボタン
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 履歴選択ラジオボタン
  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
}

// 子モジュールに値を渡す
setChildrenValue()

/**
 * 子モジュールに値を渡す
 *
 */
function setChildrenValue() {
  setChildCpBinds(props.uniqueCpId, {
    // 出力帳票名一覧
    [Or26326Const.CP_ID(1)]: {
      twoWayValue: {
        mo01334Type: {
          value: local.or27554.prtList.at(0)?.prtNo ?? '',
        },
      },
    },
    // 帳票タイトル
    [Or26328Const.CP_ID(1)]: {
      twoWayValue: {
        titleInput: local.titleInput,
      },
    },
    // 日付印刷
    [Or28780Const.CP_ID(1)]: {
      twoWayValue: {
        mo00039Type: local.or28780.mo00039Type,
        mo00020Type: local.or28780.mo00020Type,
      },
    },
    // 印刷オプション
    [Or18615Const.CP_ID(1)]: {
      twoWayValue: {
        mo00018TypeChangeTitle: local.mo00018TypeChangeTitle,
        mo00045Type: local.textInput,
      },
    },
    // 利用者選択、基準日、履歴選択
    [Or26331Const.CP_ID(1)]: {
      twoWayValue: {
        mo00039OneWayUserSelectType: localOneway.orX0130Oneway.selectMode,
        mo00020TypeKijunbi: local.mo00020TypeKijunbi,
        mo00039OneWayHistorySelectType: localOneway.orX0143Oneway.singleFlg,
      },
    },
  })
}

/**
 * 「印鑑欄ボタン」押下:GUI01110［印鑑欄設定］画面をポップアップで起動する
 */
function openGUI01110() {
  // 親画面.システムコード
  localOneway.or10016Oneway.systemCode = localOneway.or27554Oneway.systemCode
  // 親画面.法人ID
  localOneway.or10016Oneway.houjinId = localOneway.or27554Oneway.houjinId
  // 親画面.施設ID
  localOneway.or10016Oneway.shisetsuId = localOneway.or27554Oneway.shisetuId
  // 親画面.事業所ID
  localOneway.or10016Oneway.jigyoshoId = localOneway.or27554Oneway.svJigyoId
  // 親画面.職員ID  ※ログイン情報
  localOneway.or10016Oneway.shokuinId = localOneway.or27554Oneway.shokuId
  // 帳票セクション番号: "3GKU0P150P001"
  localOneway.or10016Oneway.reportSectionNumber = Or27554Const.SECTION_NUMBER
  // 会議禄フラグ: 0:非表示
  localOneway.or10016Oneway.conferenceFlag = false
  // アセスメント:親画面.初期設定マスタの情報.パッケージプラン改訂フラグ（2:H21/4、1:旧様式）
  localOneway.or10016Oneway.assessment = localOneway.or27554Oneway.initMasterObj.pkaiteiFlg

  // Or10016のダイアログ開閉状態を更新する
  Or10016Logic.state.set({
    uniqueCpId: or10016_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 担当ケアマネプルダウン
 *
 * @param newValue - 選択値
 */
function handleOrX0145Update(newValue: OrX0145Type) {
  // 職員が選択された場合
  if (newValue?.value && !Array.isArray(newValue.value) && 'chkShokuId' in newValue.value) {
    // 担当ケアマネ
    localOneway.orX0130Oneway.tantouCareManager = newValue.value.chkShokuId
  } else {
    // 担当ケアマネ
    localOneway.orX0130Oneway.tantouCareManager = ''
  }
}

/**
 * 「閉じるボタン」押下
 */
async function close() {
  const isValid = await isValidation()
  if (!isValid) {
    return false
  }

  // 画面項目を設定
  setPrtListData()

  // 画面の印刷設定情報を保存する
  await printCom.doPrintClose(
    local.titleInput,
    {
      // セクション名:親画面.セクション名
      sectionName: localOneway.or27554Oneway.sectionName,
      // システムコード：親画面.システムコード
      gsyscd: localOneway.or27554Oneway.systemCode,
      // 職員ID：親画面.職員ID
      shokuId: localOneway.or27554Oneway.shokuId,
      // 法人ID：親画面.法人ID
      houjinId: localOneway.or27554Oneway.houjinId,
      // 施設ID：親画面.施設ID
      shisetuId: localOneway.or27554Oneway.shisetuId,
      // 事業者ID：親画面.事業者ID
      svJigyoId: localOneway.or27554Oneway.svJigyoId,
      // 印刷設定情報リスト：印刷設定情報リスト
      prtList: local.or27554.prtList,
    },
    'implementationPlan1PrintSettingsInfoUpdate',
    localOneway.mo01334Oneway.items,
    local.mo01334.value,
    props.uniqueCpId,
    showMessageBox,
    closeDialog
  )
}

/**
 * 本画面を閉じる
 */
function closeDialog() {
  setState({ isOpen: false })
}

/**
 * 画面項目を設定
 */
function setPrtListData() {
  const firstItem = local.or27554.prtList.at(0)
  if (firstItem) {
    // 帳票タイトル
    firstItem.prtTitle = local.titleInput.value
    // 日付印刷の日付印刷区分
    firstItem.prnDate = Or28780Logic.data.get(or28780_1.value.uniqueCpId)?.mo00039Type ?? ''
    // 敬称を変更するチェックボックス
    firstItem.param03 =
      (Or18615Logic.data.get(or18615_1.value.uniqueCpId)?.mo00018TypeChangeTitle.modelValue ??
      false)
        ? Or27554Const.CHECK_ON
        : Or27554Const.CHECK_OFF
    // 敬称テキストフィールド
    firstItem.param04 = Or18615Logic.data.get(or18615_1.value.uniqueCpId)?.mo00045Type.value ?? ''
    // 作成者を印刷するチェックボックス
    firstItem.param05 =
      (local.mo00018TypePrintAuthor.modelValue ?? false)
        ? Or27554Const.CHECK_ON
        : Or27554Const.CHECK_OFF
  }
}

/**
 * 「PDFダウンロード」ボタン押下
 */
async function print() {
  const isValid = await isValidation()
  if (!isValid) {
    return false
  }

  // 業務チェックを行う
  if (
    !printCom.doCheckBeforePrint(
      local.titleInput.value,
      local.mo00018TypePrintTheForm.modelValue,
      selectedUserList.value.length,
      localOneway.orX0130Oneway.selectMode === Or27554Const.TANI
        ? selectedRirekiData.value.length
        : 1,
      showMessageBox
    )
  ) {
    return
  }

  // 画面項目を設定
  setPrtListData()
  // PDFダウンロード
  await executePrint()
}

/**
 * PDFダウンロード
 */
async function executePrint() {
  const resSubjectData = await printCom.doRetryRirekiData<
    PrintSubjectSelectInEntity,
    PrtHistoryList,
    ImplementationPlan1PrintSettingsSubjectSelectOutEntity
  >(
    {
      // 利用者リスト：画面.利用者一覧で選択した利用者リスト
      userList:
        selectedUserList.value.map((x) => {
          return {
            userId: x.selfId,
            userName: x.name1Knj + ' ' + x.name2Knj,
          } as UserInfo
        }) ?? [],
      // 事業所ID：親画面.事業所ID
      svJigyoId: localOneway.or27554Oneway.svJigyoId,
      // 基準日：画面.基準日
      kijunbiYmd: local.mo00020TypeKijunbi.value,
    },
    localOneway.orX0130Oneway.selectMode,
    'implementationPlan1PrintSettingsSubjectSelect'
  )

  // 改訂フラグリス設定処理 ※重複を排除
  const kaiteiFlgSet = new Set<string>()
  // 利用者選択が「単一」の場合
  if (localOneway.orX0130Oneway.selectMode === Or27554Const.TANI) {
    // 画面.記入用シートを印刷するチェックボックスがチェックOFFの場合
    if (!local.mo00018TypePrintTheForm.modelValue) {
      // 履歴一覧選択した改訂フラグを追加する。
      const firstKaiteiFlg = selectedRirekiData.value.at(0)?.kaiteiFlg
      if (firstKaiteiFlg) {
        kaiteiFlgSet.add(firstKaiteiFlg)
      }
    } else {
      // 履歴一覧データの件数が0より大きい場合
      if (local.or27554.kikanRirekiList?.length > 0) {
        // 履歴一覧ですべて選択した改訂フラグを追加する
        selectedRirekiData.value.forEach((x) => {
          if (x.kaiteiFlg) kaiteiFlgSet.add(x.kaiteiFlg)
        })
      } else {
        // 親画面.初期設定マスタの情報.改訂フラグを追加する
        const initKaiteiFlg = localOneway.or27554Oneway.initMasterObj.pkaiteiFlg
        if (initKaiteiFlg) kaiteiFlgSet.add(initKaiteiFlg)
      }
    }
  } else {
    // 利用者選択が「複数」の場合
    // 取得した改定フラグリストの件数が0より大きい場合
    if (resSubjectData?.length > 0) {
      // 取得した改定フラグリストを設定する。
      resSubjectData.forEach((x) => {
        if (x.kaiteiFlg) kaiteiFlgSet.add(x.kaiteiFlg)
      })
    }
  }

  const inputParam: ImplementationPlan1PrintSettingsInfoUpdateInEntity<IPrintInfo> = {
    // セクション名:親画面.セクション名
    sectionName: localOneway.or27554Oneway.sectionName,
    // システムコード：親画面.システムコード
    gsyscd: localOneway.or27554Oneway.systemCode,
    // 職員ID：親画面.職員ID
    shokuId: localOneway.or27554Oneway.shokuId,
    // 法人ID：親画面.法人ID
    houjinId: localOneway.or27554Oneway.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.or27554Oneway.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or27554Oneway.svJigyoId,
    // 印刷設定情報リスト：印刷設定情報リスト
    prtList: local.or27554.prtList,
    // 改訂フラグリスト
    kaiteiFlgList: [...kaiteiFlgSet],
  }

  // 印刷設定情報を保存する
  await printCom.doPrintUpdate(inputParam, 'implementationPlan1PrintSettingsInfoUpdate')

  // 業務共通化処理の設定
  const selectedPrtData = local.or27554.prtList.at(0)
  if (selectedPrtData) {
    // 印刷オプション
    const printOption = {
      // 初期設定マスタの情報
      initMasterObj: localOneway.or27554Oneway.initMasterObj,
      // 事業所名
      jigyoKnj: localOneway.or27554Oneway.jigyoKnj,
    }
    // DB未保存画面項目
    const dbNoSaveData = {
      // 指定日:印刷設定情報リスト選択行.日付表示有無が2の場合、画面の指定日
      selectDate:
        selectedPrtData.prnDate === '2'
          ? (Or28780Logic.data.get(or28780_1.value.uniqueCpId)?.mo00020Type.value ?? '')
          : '',
      // 記入用シートを印刷するフラグ
      emptyFlg: local.mo00018TypePrintTheForm.modelValue
        ? Or27554Const.CHECK_ON
        : Or27554Const.CHECK_OFF,
    }

    // 利用者選択が「単一」、且つ、履歴選択が「単一」の場合
    if (
      localOneway.orX0130Oneway.selectMode === Or27554Const.TANI &&
      localOneway.orX0143Oneway.singleFlg === Or27554Const.TANI
    ) {
      void printCom.doReportOutput(
        selectedPrtData,
        {
          userId: selectedUserList.value[0].selfId,
          rirekiId: selectedRirekiData.value[0].rirekiId,
        },
        printOption,
        dbNoSaveData,
        localOneway.or27554Oneway.sysYmd
      )
    } else {
      const historyData: HistoryInfo[] = []
      // 利用者選択が「単一」、且つ、履歴選択が「複数」の場合
      if (localOneway.orX0130Oneway.selectMode === Or27554Const.TANI) {
        selectedRirekiData.value.forEach((x) => {
          historyData.push({
            userId: selectedUserList.value[0].selfId,
            rirekiId: x.rirekiId,
          })
        })
      } else {
        // 利用者選択が「複数」、且つ、履歴選択が「単一」の場合
        resSubjectData.forEach((x) => {
          historyData.push({
            userId: x.userId,
            rirekiId: x.rirekiId,
          })
        })
      }

      // 画面.印刷対象履歴リストを作成する
      printCom.doHukusuuSetting(
        selectedPrtData,
        historyData,
        localOneway.orX0117Oneway,
        localOneway.orX0130Oneway.selectMode,
        selectedUserList.value,
        printOption,
        dbNoSaveData,
        localOneway.or27554Oneway.sysYmd ?? '',
        resSubjectData,
        selectedRirekiData.value
      )

      // PDFダウンロードを行う
      OrX0117Logic.state.set({
        uniqueCpId: orX0117_1.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
    }
  }
}

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)
/**
 * 記入用シートを印刷するの活性制御
 * 印刷設定帳票出力状態
 */
watchEffect(() => {
  // 画面.利用者選択方法が「複数」 or、 画面.履歴選択方法が「複数」の場合、記入用シートを印刷するの活性制御：OFF、非活性
  localOneway.mo00018OneWayPrintTheForm.disabled =
    localOneway.orX0130Oneway.selectMode !== Or27554Const.TANI ||
    localOneway.orX0143Oneway.singleFlg !== Or27554Const.TANI
  if (localOneway.mo00018OneWayPrintTheForm.disabled ?? false) {
    local.mo00018TypePrintTheForm.modelValue = false
  }

  // 画面.利用者選択方法が「複数」の場合(0: 履歴複数 Or 1: 利用者複数 Or 2: その他)
  if (localOneway.orX0130Oneway.selectMode === Or27554Const.HUKUSUU) {
    userCols.value = 11
    localOneway.orX0117Oneway.type = Or27554Const.TYPE_0
  } else {
    // 画面.利用者選択方法が「単一」の場合
    userCols.value = 6
    // 画面.履歴選択方法が「複数」の場合
    if (localOneway.orX0143Oneway.singleFlg === Or27554Const.HUKUSUU) {
      localOneway.orX0117Oneway.type = Or27554Const.TYPE_0
    } else {
      localOneway.orX0117Oneway.type = Or27554Const.TYPE_1
    }
  }
})

/**
 * 利用者選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331_1.value.uniqueCpId)?.mo00039OneWayUserSelectType,
  (newValue) => {
    if (newValue) {
      // 選択モート
      localOneway.orX0130Oneway.selectMode = newValue
      setChildrenValue()
    }
  }
)

/**
 * 履歴選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331_1.value.uniqueCpId)?.mo00039OneWayHistorySelectType,
  (newValue) => {
    if (newValue) {
      // 単一複数フラグ
      localOneway.orX0143Oneway.singleFlg = newValue
      setChildrenValue()
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130_1.value.uniqueCpId),
  async (newValue) => {
    selectedUserList.value = newValue?.userList ?? []
    // 利用者選択方法が「単一」の場合
    if (localOneway.orX0130Oneway.selectMode === Or27554Const.TANI) {
      // 利用者未選択の場合
      if (selectedUserList.value.length === 0) {
        // 期間履歴情報リスト
        local.or27554.kikanRirekiList = []
        localOneway.orX0143Oneway.rirekiId = ''
        localOneway.orX0143Oneway.rirekiList = []
        return
      }
      if (newValue?.clickFlg) {
        // 利用者配下の履歴情報を取得する
        await getHistoricalInfoList(newValue.userList[0].userId)
      }
    }
  }
)

/**
 * 利用者配下の履歴情報を取得する
 *
 * @param userId - 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  // 期間履歴情報リスト
  local.or27554.kikanRirekiList = []

  if (userId) {
    // 利用者配下の履歴情報を取得する
    const result = await printCom.doUserClick<
      PrintUserChangeSelectInEntity,
      KikanRirekiData,
      ImplementationPlan1PrintSettingsUserChangeSelectOutEntity
    >(
      {
        // 事業所ID:親画面.事業所ID
        svJigyoId: localOneway.or27554Oneway.svJigyoId,
        // 利用者ID:画面.利用者一覧に選択行の利用者ID
        userId: userId,
      },
      'implementationPlan1PrintSettingsUserChangeSelect'
    )
    // 期間履歴情報リストに取得した期間履歴情報リストを設定する
    local.or27554.kikanRirekiList = result
  }
  localOneway.orX0143Oneway.rirekiId = ''
  localOneway.orX0143Oneway.rirekiList = local.or27554.kikanRirekiList
}

/**
 * 「履歴一覧」明細選択の監視
 */
watch(
  () => OrX0143Logic.event.get(orX0143_1.value.uniqueCpId),
  (newValue) => {
    selectedRirekiData.value = (newValue?.orX0143DetList as KikanRirekiData[] | undefined) ?? [];
  }
)

/**
 * 帳票タイトルの監視
 */
watch(
  () => Or26328Logic.data.get(or26328_1.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // タイトル
      local.titleInput = newValue.titleInput
    }
  }
)

/**
 * エラーダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 *
 * @param isError - エラーかどうか
 *
 * @returns ダイアログの選択結果（yes）
 */
const showMessageBox = async (text: string, btn: string, isError = true) => {
  if (isError) {
    await openErrorDialog(text, btn)
  } else {
    await openInfoDialog(text)
  }
}

/**
 * エラーダイアログの開閉
 *
 * @param messageId - メッセージID
 *
 * @param firstBtnLabel - 第1ボタンラベルの（省略時は 'btn.yes'）
 */
function openErrorDialog(messageId: string, firstBtnLabel?: string) {
  // 第1ボタンラベル
  const btnLabel = firstBtnLabel ? t(firstBtnLabel) : t('btn.yes')

  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t(messageId),
      firstBtnType: 'normal1',
      firstBtnLabel: btnLabel,
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813_1.value.uniqueCpId)

        let result = Or27554Const.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = Or27554Const.DIALOG_RESULT_YES
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログの開閉
 *
 * @param messageId - メッセージID
 *
 * @returns - 確認ダイアログを閉じたタイミングで結果を返却
 */
function openInfoDialog(messageId: string): Promise<Or21814EventType | undefined> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t(messageId),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * バリデーション関数
 */
async function isValidation() {
  const vali1 = await or26328Ref.value?.isValid()
  const vali2 = await or18615Ref.value?.isValid()
  return (vali1 ?? true) && (vali2 ?? true)
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or27554_screen"
      >
        <!-- 帳票 -->
        <g-custom-or-26326
          v-bind="or26326_1"
          :oneway-model-value="localOneway.mo01334Oneway"
        />
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 or27554_border_right content_center"
        >
          <c-v-row style="margin-left: 8px; margin-top: 8px; margin-bottom: 8px">
            <!--印鑑欄-->
            <base-mo00610
              class="mr-2"
              :oneway-model-value="localOneway.mo00610OneWay"
              @click="openGUI01110"
            />
          </c-v-row>
          <!-- タイトル -->
          <g-custom-or-26328
            ref="or26328Ref"
            v-bind="or26328_1"
            :oneway-model-value="localOneway.or26328Oneway"
          />
          <!-- 日付印刷 -->
          <g-custom-or-28780
            v-bind="or28780_1"
            :oneway-model-value="localOneway.mo00039OneWay"
          />
          <!-- 印刷オプション -->
          <g-custom-or-18615
            v-bind="or18615_1"
            ref="or18615Ref"
            :oneway-model-value="{
              mo00018OneWayChangeTitle: localOneway.mo00018OneWayChangeTitle,
              mo00045OnewayTextInput: localOneway.mo00045OnewayTextInput,
            }"
          >
            <template #optionPrintItems>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 記入用シートを印刷するチェックボックス -->
                <base-mo00018
                  v-model="local.mo00018TypePrintTheForm"
                  :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
                />
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 作成者を印刷するチェックボックス -->
                <base-mo00018
                  v-model="local.mo00018TypePrintAuthor"
                  :oneway-model-value="localOneway.mo00018OneWayPrintPrintAuthor"
                />
              </c-v-col>
            </template>
          </g-custom-or-18615>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
        >
          <c-v-row
            class="or27554_row"
            no-gutter
          >
            <!-- 利用者選択、基準日、履歴選択 -->
            <g-custom-or-26331
              v-bind="or26331_1"
              :oneway-model-value="{
                mo00039OneWayUserSelectType: localOneway.mo00039OneWayUserSelectType,
                mo00039OneWayHistorySelectType: localOneway.mo00039OneWayHistorySelectType,
              }"
              :unique-cp-id="or26331_1.uniqueCpId"
            />
            <div class="pa-2">
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orX0145_1"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
                @update:model-value="handleOrX0145Update"
              />
            </div>
          </c-v-row>
          <c-v-row
            class="or27554_row grid-width"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              style="overflow-x: auto"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="localOneway.orX0130Oneway.selectMode"
                v-bind="orX0130_1"
                :oneway-model-value="localOneway.orX0130Oneway"
              />
            </c-v-col>
            <c-v-col
              v-if="localOneway.orX0130Oneway.selectMode === Or27554Const.TANI"
              cols="12"
              sm="6"
              class="pl-2 pr-0"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0143
                v-if="localOneway.orX0143Oneway.singleFlg"
                v-bind="orX0143_1"
                :oneway-model-value="localOneway.orX0143Oneway"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        />
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mr-2"
          :disabled="!prtFlg"
          @click="print()"
        />
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- エラーダイアログ -->
  <g-base-or21813 v-bind="or21813_1" />
  <!-- 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <!-- GUI01110_「印鑑欄設定」画面を起動する -->
  <g-custom-or-10016
    v-if="showDialogOr10016"
    v-bind="or10016_1"
    :oneway-model-value="localOneway.or10016Oneway"
  />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117_1"
    :oneway-model-value="localOneway.orX0117Oneway"
  />
</template>

<style scoped lang="scss">
.or27554_screen {
  margin: -8px !important;
}

.or27554_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or27554_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-background));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.customCol {
  margin-left: 0px;
  margin-right: 0px;
}

.grid-width {
  min-width: 694px;
}
</style>
