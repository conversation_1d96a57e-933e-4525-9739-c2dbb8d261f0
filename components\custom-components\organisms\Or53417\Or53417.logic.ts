import { Or53417Const } from './Or53417.constants'
import type { Or53417EventType, Or53417StateType } from './Or53417.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or10320Logic } from '~/components/custom-components/organisms/Or10320/Or10320.logic'
import { Or10320Const } from '~/components/custom-components/organisms/Or10320/Or10320.constants'
import { Or27746Const } from '~/components/custom-components/organisms/Or27746/Or27746.constants'
import { Or27746Logic } from '~/components/custom-components/organisms/Or27746/Or27746.logic'

/**
 * Or53417:有機体:モーダル（アセスメント（インターライ）マスタ画面モーダル））
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 */
export namespace Or53417Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or53417Const.CP_ID(1),
      uniqueCpId,
      childCps: [
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or10320Const.CP_ID(1) },
        { cpId: Or27746Const.CP_ID(1) },
      ],
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or10320Logic.initialize(childCpIds[Or10320Const.CP_ID(1)].uniqueCpId)
    Or27746Logic.initialize(childCpIds[Or27746Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or53417StateType>(Or53417Const.CP_ID(1))

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useOneWayBindAccessor<Or53417EventType>(Or53417Const.CP_ID(1))
}
