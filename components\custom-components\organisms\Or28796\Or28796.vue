<script setup lang="ts">
/**
 * Or28796:ケアマネCSVモーダル
 * GUI01311_ケアマネCSV
 *
 * @description
 * ケアマネCSVモーダル
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */

import { onMounted, reactive, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28796Const } from './Or28796.constants'
import type { Or28796StateType } from './Or28796.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useSetupChildProps, useScreenStore } from '#imports'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type {
  careManagerCSVInitInfoSelectInEntity,
  careManagerCSVInitInfoSelectOutEntity,
} from '~/repositories/cmn/entities/careManagerCSVInitInfoSelectEntity'
import type { Or28796OnewayType } from '~/types/cmn/business/components/Or28796Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { Or53898Const } from '~/components/custom-components/organisms/Or53898/Or53898.constants'
import { Or53898Logic } from '~/components/custom-components/organisms/Or53898/Or53898.logic'
import type { Or53898OnewayType } from '~/types/cmn/business/components/Or53898Type'
import { Or29444Const } from '~/components/custom-components/organisms/Or29444/Or29444.constants'
import { Or29444Logic } from '~/components/custom-components/organisms/Or29444/Or29444.logic'
import type { Or29444OnewayType } from '~/types/cmn/business/components/Or29444Type'
import { Or29446Const } from '~/components/custom-components/organisms/Or29446/Or29446.constants'
import { Or29446Logic } from '~/components/custom-components/organisms/Or29446/Or29446.logic'
import type { Or29446OnewayType } from '~/types/cmn/business/components/Or29446Type'
import { Or29448Const } from '~/components/custom-components/organisms/Or29448/Or29448.constants'
import { Or29448Logic } from '~/components/custom-components/organisms/Or29448/Or29448.logic'
import type { Or29448OnewayType } from '~/types/cmn/business/components/Or29448Type'
/**************************************************
 * プロパティ (Props)
 **************************************************/
interface Props {
  onewayModelValue: Or28796OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * リアクティブステート(Reactive State)
 **************************************************/
/*** i18nインスタンス */
const { t } = useI18n()
/*** システム共有情報取得用ストア */
const systemCommonsStore = useSystemCommonsStore()
/*** Or11871子画面のユニークID保持用 */
const or11871 = ref({ uniqueCpId: '' })
/*** ケアマネCSV画面のユニークID保持用 */
const or28796 = ref({ uniqueCpId: '' })
/*** （ケアマネCSV集計）計画書（１）のユニークID保持用 */
const or53898 = ref({ uniqueCpId: '' })
/*** （ケアマネCSV集計）計画書（２）のユニークID保持用 */
const or29444 = ref({ uniqueCpId: '' })
/*** （ケアマネCSV集計）計画書（１）A様式 のユニークID保持用 */
const or29446 = ref({ uniqueCpId: '' })
/*** （ケアマネCSV集計）計画書（２）A様式 のユニークID保持用 */
const or29448 = ref({ uniqueCpId: '' })
/*** ローカルOnewayデータ */
const local = reactive<Or28796OnewayType>({
  kinouId: '1',
})

/*** ローカルOnewayマップ */
const localOneway = reactive({
  or28796: {
    ...local,
    ...props.onewayModelValue,
  },
})
/*** 統計リストデータ */
const Or28796ODataTable = ref({
  toukeiList: [] as Or28796StateType[],
})

/*** 画面状態管理用操作変数 */
const screenStore = useScreenStore()

/**
 * Or53898のダイアログ開閉状態を管理するための変数
 */
const or53898Data: Or53898OnewayType = {
  sysCd: systemCommonsStore?.getSystemCode ?? '',
  toukeiId: '',
  nameKnj: '',
  kinouKbn: systemCommonsStore.getFunctionCategory ?? '',
  officeId: systemCommonsStore.getSvJigyoId ?? '',
}

/**
 * Or29444のダイアログ開閉状態を管理するための変数
 */
const or29444Data: Or29444OnewayType = {
  sysCd: systemCommonsStore?.getSystemCode ?? '',
  toukeiId: '',
  nameKnj: '',
  kinouKbn: systemCommonsStore.getFunctionCategory ?? '',
  officeId: systemCommonsStore.getSvJigyoId ?? '',
  svJigyoIdList: [],
}

/**
 * Or29446のダイアログ開閉状態を管理するための変数
 */
const or29446Data: Or29446OnewayType = {
  sysCd: systemCommonsStore?.getSystemCode ?? '',
  toukeiId: '',
  nameKnj: '',
  kinouKbn: systemCommonsStore.getFunctionCategory ?? '',
  officeId: systemCommonsStore.getSvJigyoId ?? '',
}

/**
 * Or29448のダイアログ開閉状態を管理するための変数
 */
const or29448Data: Or29448OnewayType = {
  sysCd: systemCommonsStore?.getSystemCode ?? '',
  toukeiId: '',
  nameKnj: '',
  kinouKbn: systemCommonsStore.getFunctionCategory ?? '',
  officeId: systemCommonsStore.getSvJigyoId ?? '',
}

/**************************************************
 * 定数(Constants)
 **************************************************/
/*** carePlanボタン設定リスト */
const carePlanConfig: { key: string; title: string; isShow: boolean; width: string }[] = [
  {
    key: Or28796Const.DEFAULT.DEFAULT_VALUE.KEY_301,
    title: 'label.care-manager-csv-plan1',
    isShow: true,
    width: '106px',
  },
  {
    key: Or28796Const.DEFAULT.DEFAULT_VALUE.KEY_302,
    title: 'label.care-manager-csv-plan2',
    isShow: true,
    width: '106px',
  },
  {
    key: Or28796Const.DEFAULT.DEFAULT_VALUE.KEY_303,
    title: 'label.care-manager-csv-plan1-style',
    isShow: true,
    width: '141px',
  },
  {
    key: Or28796Const.DEFAULT.DEFAULT_VALUE.KEY_304,
    title: 'label.care-manager-csv-plan2-style',
    isShow: true,
    width: '141px',
  },
]

/**************************************************
 * 計算プロパティ(Computed Properties)
 **************************************************/
/**
 * ボタンリスト（isShow反映済み）
 */
const localOnewayMap = computed(() => {
  return carePlanConfig.map((config) => {
    return {
      ...config,
      isShow: isCheckShow(config.key),
      mo00611OneWay: {
        btnLabel: t(config.title),
        tooltipLocation: 'bottom',
        tooltipText: t(config.title),
        width: config.width,
      } as Mo00611OnewayType,
    }
  })
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr53898 = computed(() => {
  // Or53898のダイアログ開閉状態
  return Or53898Logic.state.get(or53898.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr29444 = computed(() => {
  // Or29444のダイアログ開閉状態
  return Or29444Logic.state.get(or29444.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr29446 = computed(() => {
  // Or29446のダイアログ開閉状態
  return Or29446Logic.state.get(or29446.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr29448 = computed(() => {
  // Or29448のダイアログ開閉状態
  return Or29448Logic.state.get(or29448.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ライフサイクルフック(Lifecycle Hooks)
 **************************************************/
/*** piniaから最上位の画面コンポーネント情報を取得する
 * これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent
/*** 子コンポーネントのユニークIDを設定する */
or28796.value.uniqueCpId = pageComponent.uniqueCpId

/**************************************************
 * Pinia
 **************************************************/
/*** 子コンポーネントのユニークIDを設定する */
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or53898Const.CP_ID(0)]: or53898.value,
  [Or29444Const.CP_ID(0)]: or29444.value,
  [Or29446Const.CP_ID(0)]: or29446.value,
  [Or29448Const.CP_ID(0)]: or29448.value,
})

/*** 子コンポーネントに対して初期設定を行う */
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.care-manager-csv'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: false,
    showCreateBtn: false,
    showCreateMenuCopy: false,
    showPrintBtn: false,
    showMasterBtn: false,
    showOptionMenuBtn: false,
    showOptionMenuDelete: false,
  },
})

/*** マウント時の初期化処理 */
onMounted(async () => {
  // 利用者を全選択です。
  await init()
})

/**************************************************
 * メソッド(Methods)
 **************************************************/
/**
 * ボタン表示判定
 *
 * @param key - ボタンキー
 *
 * @returns 表示可否
 */
const isCheckShow = (key: string) => {
  const itemCheck = Or28796ODataTable.value.toukeiList.find((item) => item.toukeiId === key)
  return itemCheck ? true : false
}

/**
 * 初期化処理
 */
const init = async () => {
  const inputParam: careManagerCSVInitInfoSelectInEntity = {
    sysCd: systemCommonsStore?.getSystemCode ?? '',
    kinouKbn: '3',
    kinouId: localOneway.or28796.kinouId,
  }

  // APIで月間・年間表詳細情報を取得する。
  const response: careManagerCSVInitInfoSelectOutEntity = await ScreenRepository.select(
    'careManagerCSVInitInfoSelect',
    inputParam
  )

  if (response.data) {
    Or28796ODataTable.value.toukeiList = response.data.toukeiList
  }
}

/**
 * AC003 ~ AC006 ケアプランボタンクリック時の処理
 *
 * @param toukeiId - 統計ID
 */
const onCarePlan = (toukeiId: string) => {
  switch (toukeiId) {
    case Or28796Const.DEFAULT.DEFAULT_VALUE.KEY_301:
      Or53898Logic.state.set({
        uniqueCpId: or53898.value.uniqueCpId,
        state: { isOpen: true },
      })
      break
    case Or28796Const.DEFAULT.DEFAULT_VALUE.KEY_302:
      Or29444Logic.state.set({
        uniqueCpId: or29444.value.uniqueCpId,
        state: { isOpen: true },
      })
      break
    case Or28796Const.DEFAULT.DEFAULT_VALUE.KEY_303:
      Or29446Logic.state.set({
        uniqueCpId: or29446.value.uniqueCpId,
        state: { isOpen: true },
      })
      break
    case Or28796Const.DEFAULT.DEFAULT_VALUE.KEY_304:
      Or29448Logic.state.set({
        uniqueCpId: or29448.value.uniqueCpId,
        state: { isOpen: true },
      })
      break
    default:
      break
  }
}
</script>

<template>
  <c-v-sheet class="view">
    <g-base-or11871
      v-bind="or11871"
      class="pb-6"
    >
    </g-base-or11871>
    <c-v-row
      no-gutters
      class="main-Content mt-3"
    >
      <!-- コンテンツエリア -->
      <c-v-col
        cols="12"
        class="main-right hidden-scroll h-100 px-6"
      >
        <c-v-row no-gutters>
          <c-v-col cols="4">
            <div class="d-flex">
              <template
                v-for="item in localOnewayMap"
                :key="item.key"
              >
                <base-mo00611
                  v-if="item.isShow"
                  v-bind="item.mo00611OneWay"
                  class="mr-2"
                  @click="onCarePlan(item.key)"
                >
                  <c-v-tooltip
                    v-if="item.mo00611OneWay.tooltipText"
                    :text="item.mo00611OneWay.tooltipText"
                    :location="item.mo00611OneWay.tooltipLocation"
                    activator="parent"
                  />
                </base-mo00611>
              </template>
            </div>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <!--GUI01312_（ケアマネCSV集計）計画書（１）画面をポップアップで起動する。  -->
    <g-custom-or-53898
      v-if="showDialogOr53898"
      v-bind="or53898"
      :oneway-model-value="or53898Data"
    >
    </g-custom-or-53898>
    <!-- GUI01313_（ケアマネCSV集計）計画書（２）画面をポップアップで起動する。 -->
    <g-custom-or-29444
      v-if="showDialogOr29444"
      v-bind="or29444"
      :oneway-model-value="or29444Data"
    >
    </g-custom-or-29444>
    <!-- GUI01314_（ケアマネCSV集計）計画書（１）A様式画面をポップアップで起動する。-->
    <g-custom-or-29446
      v-if="showDialogOr29446"
      v-bind="or29446"
      :oneway-model-value="or29446Data"
    >
    </g-custom-or-29446>
    <!-- GUI01315_（ケアマネCSV集計）計画書（２）A様式画面をポップアップで起動する。-->
    <g-custom-or-29448
      v-if="showDialogOr29448"
      v-bind="or29448"
      :oneway-model-value="or29448Data"
    >
    </g-custom-or-29448>
  </c-v-sheet>
</template>

<style scoped lang="scss">
:deep(.pl-4) {
  padding-left: 0px !important;
}

.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.border-bottom {
  border-bottom: 1px solid rgb(var(--v-theme-black-200));
}

.main-Content {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;

  .main-right {
    height: 100%;
    display: flex;
    flex-direction: column;

    .footer {
      flex: 0 1 auto;
    }
  }
}
</style>
