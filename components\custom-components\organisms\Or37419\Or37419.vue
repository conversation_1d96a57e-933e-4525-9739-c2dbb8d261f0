<script setup lang="ts">
/**
 * Or37419:有機体:実施計画～①複写POP画面
 * GUI00948_実施計画～①複写POP画面
 *
 * @description
 * 実施計画～①複写POP画面の処理
 *
 * <AUTHOR>
 */
import { onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { OrX0060Const } from '../OrX0060/OrX0060.constants'
import { Or30919Const } from '../Or30919/Or30919.constants'
import { Or30919Logic } from '../Or30919/Or30919.logic'
import { Or30920Const } from '../Or30920/Or30920.constants'
import { Or30920Logic } from '../Or30920/Or30920.logic'
import type { Or37419ScreenType } from './Or37419.type'
import { Or37419Const } from './Or37419.constants'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenUtils,
  useSystemCommonsStore,
} from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  Or37419OnewayType,
  Or37419StateType,
} from '~/types/cmn/business/components/Or37419Type'
import type { OrX0077OnewayType } from '~/types/cmn/business/components/OrX0077Type'
import type { GamenDataInfo } from '~/types/cmn/business/components/OrX0060Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type {
  Or21814OnewayType,
  Or21814EventType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import type {
  ImplementationPlan1CopySelectInEntity,
  ImplementationPlan1CopySelectOutEntity,
  IKikanInfo,
  IRirekiInfo,
} from '~/repositories/cmn/entities/ImplementationPlan1CopySelectEntity'

const { t } = useI18n()
const { setChildCpBinds } = useScreenUtils()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or37419OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const localOneway = reactive({
  or37419Oneway: {
    ...props.onewayModelValue,
  } as Or37419OnewayType,
  // 複写テンプレート
  orX0077Oneway: {
    // 複写ダイアログ
    mo00024Oneway: {
      maxWidth: '1528px',
      persistent: true,
      showCloseBtn: true,
      mo01344Oneway: {
        name: 'Or37419',
        toolbarTitle: t('label.implementation_plan_1_duplicate'),
        toolbarName: 'Or37419ToolBar',
        // ツールバータイトルの左寄せ
        toolbarTitleCenteredFlg: false,
        showCardActions: true,
      },
    } as Mo00024OnewayType,
  } as OrX0077OnewayType,

  // 閉じるボタン設置
  mo00611Oneway: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,

  // 確定ボタン設置
  mo00609Oneway: {
    btnLabel: t('btn.confirm'),
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
})

const local = reactive({
  or37419: {
    kikanList: [] as IKikanInfo[],
    rirekiList: [] as IRirekiInfo[],
  } as Or37419ScreenType,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or37419StateType>({
  cpId: Or37419Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      OrX0077Logic.state.set({
        uniqueCpId: orX0077_1.value.uniqueCpId,
        state: { isOpen: value },
      })
    },
  },
})

const or21814_1 = ref({ uniqueCpId: '' })
const or30919_1 = ref({ uniqueCpId: '' })
const or30920_1 = ref({ uniqueCpId: '' })
const orX0077_1 = ref({ uniqueCpId: '' })
const orX0060_1 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or30919Const.CP_ID(1)]: or30919_1.value,
  [Or30920Const.CP_ID(1)]: or30920_1.value,
  [OrX0077Const.CP_ID(1)]: orX0077_1.value,
  [OrX0060Const.CP_ID(1)]: orX0060_1.value,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  //操作区分
  local.or37419.operaFlg = Or37419Const.OPERA_FLG_0
  // 選択用の計画期間ID
  local.or37419.sc1Id = ''
  // 選択用の事業所ID
  local.or37419.svJigyoId = ''
  // 選択用の履歴ID
  local.or37419.rirekiId = ''
  //選択用の利用者ID
  local.or37419.userId = props.onewayModelValue.userId
  systemCommonsStore.setUserSelectSelfId(local.or37419.userId, orX0077_1.value.uniqueCpId)
  // 初期情報取得
  await getInitDataInfo()
})
onUnmounted(() => {
  setChildCpBinds(props.uniqueCpId, {
    [Or30919Const.CP_ID(1)]: {
      twoWayValue: {
        sc1Id: '',
        svJigyoId: '',
        isRowChange: false,
      },
      oneWayState: {
        kikanList: [],
      },
    },
    [Or30920Const.CP_ID(1)]: {
      twoWayValue: {
        rirekiId: '',
        isRowChange: false,
      },
      oneWayState: {
        rirekiList: [],
        kikanFlg: '',
      },
    },
    [OrX0060Const.CP_ID(1)]: {
      twoWayValue: {
        ryuiKnj: '',
        dataList: [],
      },
      oneWayState: {
        operaFlg: '',
        pagingFlg: '',
        syubetsuId: '',
        kikanFlg: '',
        kadaiTorikomiFlg: '',
        initMasterObj: {},
        isCopyMode: true,
      },
    },
  })
})
/**
 * 初期情報取得
 */
const getInitDataInfo = async () => {
  const inputParam: ImplementationPlan1CopySelectInEntity = {
    // 施設ID
    operaFlg: local.or37419.operaFlg,
    //期間管理フラグ
    kikanFlg: props.onewayModelValue.kikanFlg,
    //施設ID
    shisetuId: props.onewayModelValue.shisetuId,
    // 利用者ID
    userId: local.or37419.userId,
    // 種別ID
    syubetuId: props.onewayModelValue.syubetuId,
    // 適用事業所ID
    svJigyoIdList: props.onewayModelValue.svJigyoIdList,
    //計画期間ID
    sc1Id: local.or37419.sc1Id,
    // 事業者ID
    svJigyoId: local.or37419.svJigyoId,
    // 履歴ID
    rirekiId: local.or37419.rirekiId,
  }
  // 複写初期情報取得
  const ret: ImplementationPlan1CopySelectOutEntity = await ScreenRepository.select(
    'implementationPlan1CopySelect',
    inputParam
  )
  // API戻り値
  const retData = ret.data
  //操作区分が0:初期化の場合
  if (local.or37419.operaFlg === Or37419Const.OPERA_FLG_0) {
    //表示用「計画期間」リスト
    local.or37419.kikanList = retData.kikanList
    //表示用「履歴情報」リスト
    local.or37419.rirekiList = retData.rirekiList ?? []
    //表示用「課題目標期間内容担当」リスト
    local.or37419.dataList = retData.dataList ?? []

    //選択用の計画期間ID
    local.or37419.sc1Id = local.or37419?.kikanList?.at(0)?.sc1Id ?? ''
    //選択用の事業所ID
    local.or37419.svJigyoId = local.or37419?.kikanList?.at(0)?.svJigyoId ?? ''
    //選択用の履歴ID:表示用「履歴情報」リストの1件目.履歴ID
    local.or37419.rirekiId = local.or37419?.rirekiList?.at(0)?.rirekiId ?? ''
  } else if (local.or37419.operaFlg === Or37419Const.OPERA_FLG_1) {
    //操作区分が1:期間選択の場合
    //表示用「履歴情報」リスト
    local.or37419.rirekiList = retData.rirekiList ?? []
    //表示用「課題目標期間内容担当」リスト
    local.or37419.dataList = retData.dataList ?? []
    //選択用の履歴ID:表示用「履歴情報」リストの1件目.履歴ID
    local.or37419.rirekiId = local.or37419?.rirekiList?.at(0)?.rirekiId ?? ''
  } else if (local.or37419.operaFlg === Or37419Const.OPERA_FLG_2) {
    //操作区分が2:履歴選択の場合
    //表示用「課題目標期間内容担当」リスト
    local.or37419.dataList = retData.dataList ?? []
  }
  // 表示用「支援内容」情報
  let dataList = [] as GamenDataInfo[]
  // 「支援内容」リスト
  if (Array.isArray(local.or37419.dataList) && local.or37419.dataList.length > 0) {
    dataList = local.or37419.dataList.map((item, index) => ({
      id: String(index),
      kadaiKnj: { value: item.kadaiKnj ?? '' },
      mokuhyoKnj: { value: item.mokuhyoKnj ?? '' },
      kikanKnj: { value: item.kikanKnj ?? '' },
      naiyoKnj: { value: item.naiyoKnj ?? '' },
      tantoShokuKnj: { value: item.tantoShokuKnj ?? '' },
      kadaiNo: item.kadaiNo ?? '',
      id2: '',
      id3: '',
      modifiedCnt2: '',
      modifiedCnt3: '',
      updateKbn: '',
      seq: index + 1,
    }))
  }

  // 留意点:表示用「履歴情報」リストにおいて、選択用の履歴ＩＤに対応するデータを取得する
  const ryuiKnj = local.or37419.rirekiId
    ? (local.or37419.rirekiList?.find((x) => x.rirekiId === local.or37419.rirekiId)?.ryuiKnj ?? '')
    : ''

  // 表示用「履歴情報」
  const rirekiList = Array.isArray(local.or37419?.rirekiList)
    ? local.or37419.rirekiList.map((item) => ({
        ...item,
        shogaiKasanFlg: '',
      }))
    : []

  setChildCpBinds(props.uniqueCpId, {
    [Or30919Const.CP_ID(1)]: {
      twoWayValue: {
        sc1Id: local.or37419.sc1Id,
        svJigyoId: local.or37419.svJigyoId,
        isRowChange: false,
      },
      oneWayState: {
        kikanList: local.or37419.kikanList ?? [],
      },
    },
    [Or30920Const.CP_ID(1)]: {
      twoWayValue: {
        rirekiId: local.or37419.rirekiId,
        isRowChange: false,
      },
      oneWayState: {
        rirekiList: rirekiList,
        kikanFlg: props.onewayModelValue.kikanFlg,
      },
    },
    [OrX0060Const.CP_ID(1)]: {
      twoWayValue: {
        rirekiOtherData: {
          ryuiKnj: ryuiKnj,
        },
        dataList: dataList,
      },
      oneWayState: {
        operaFlg: props.onewayModelValue.operaFlg,
        pagingFlg: props.onewayModelValue.pagingFlg,
        syubetsuId: props.onewayModelValue.syubetuId,
        kikanFlg: props.onewayModelValue.kikanFlg,
        kadaiTorikomiFlg: props.onewayModelValue.kadaiTorikomiFlg,
        initMasterObj: props.onewayModelValue.initMasterObj,
        isCopyMode: true,
      },
    },
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,

      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * 「確定」ボタン押下
 */
const onClickConfirm = async () => {
  const data = Or30920Logic.data.get(or30920_1.value.uniqueCpId)
  //「画面.履歴情報一覧」の行を選択している場合
  if (!data?.rirekiId) {
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11289'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  }
  const dialogResult = await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10193', [t('label.implementation-plan1')]),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
  if (dialogResult?.firstBtnClickFlg === true) {
    //選択された履歴IDを設定
    // 選択情報値戻り
    emit('update:modelValue', {
      rirekiId: data.rirekiId,
      size: local.or37419?.dataList?.length ?? 0,
    })
    close()
  }
}

/**
 * 閉じるボタン押下時
 */
const close = (): void => {
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => OrX0077Logic.state.get(orX0077_1.value.uniqueCpId),
  (newValue) => {
    if (!newValue?.isOpen) {
      close()
    }
  }
)

/**
 * 「計画期間一覧」の行をクリック
 */
watch(
  () => Or30919Logic.data.get(or30919_1.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.isRowChange) {
      //操作区分
      local.or37419.operaFlg = Or37419Const.OPERA_FLG_1
      //選択用の計画期間ID
      local.or37419.sc1Id = newValue.sc1Id
      //選択用の事業所ID
      local.or37419.svJigyoId = newValue.svJigyoId
      await getInitDataInfo()
    }
  }
)

/**
 * 「履歴情報一覧」の行をクリック
 */
watch(
  () => Or30920Logic.data.get(or30920_1.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.isRowChange) {
      //操作区分
      local.or37419.operaFlg = Or37419Const.OPERA_FLG_2
      //選択用の履歴ID
      local.or37419.rirekiId = newValue.rirekiId
      await getInitDataInfo()
    }
  }
)

/**
 * 利用者選択
 *
 * @param userSelfId - 利用者ID
 */
async function onChangeUserSelect(userSelfId: string) {
  //操作区分
  local.or37419.operaFlg = Or37419Const.OPERA_FLG_0
  //選択用の計画期間ID
  local.or37419.sc1Id = ''
  //選択用の事業所ID
  local.or37419.svJigyoId = ''
  //選択用の履歴ID
  local.or37419.rirekiId = ''
  //選択用の利用者ID
  local.or37419.userId = userSelfId
  await getInitDataInfo()
}
</script>

<template>
  <!-- 複写テンプレート -->
  <g-custom-or-x-0077
    v-bind="orX0077_1"
    :oneway-model-value="localOneway.orX0077Oneway"
    @on-change-user-select="onChangeUserSelect"
  >
    <template #filter>
      <c-v-row
        no-gutters
        class="pb-2"
      >
        <c-v-col
          v-if="props.onewayModelValue.kikanFlg === Or37419Const.KIKAN_1"
          :style="{ maxWidth: '600px' }"
          class="pr-2"
        >
          <!--計画期間情報一覧-->
          <g-custom-or30919 v-bind="or30919_1" />
        </c-v-col>
        <c-v-col
          :style="
            props.onewayModelValue.kikanFlg === Or37419Const.KIKAN_1
              ? { maxWidth: '530px' }
              : { maxWidth: '710px' }
          "
        >
          <!--履歴情報一覧-->
          <g-custom-or30920 v-bind="or30920_1" />
        </c-v-col>
      </c-v-row>
    </template>
    <!-- 複写body -->
    <template #copyMain>
      <div class="copy-main-class">
        <g-custom-or-x-0060 v-bind="orX0060_1" />
      </div>
    </template>
    <!-- フッターボタン -->
    <template #footer>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611Oneway"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="onClickConfirm()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </g-custom-or-x-0077>

  <!-- 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
</template>

<style scoped lang="scss">
.copy-main-class {
  height: calc(100vh - 330px);
  display: flex;
  flex-direction: column;
}
</style>
