import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or10889:有機体:薬剤検索マスタ
 * GUI00670_薬剤検索マスタ
 *
 * 静的データ
 */
export namespace Or10889Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or10889', seq)

  /**
   * 薬剤分類階層の上限値 3
   */
  export const MAX_LEVEL = '3'

  /**
   * 階層"分類"
   */
  export const LEVEL_CLASSIFICATION = '1'

  /**
   * 階層"全"
   */
  export const LEVEL_ALL = '-1'

  /**
   * キーワード検索
   */
  export const SEARCH_AT_KEYWORD = '0'

  /**
   * 医薬品コ-ドで検索
   */
  export const SEARCH_AT_MEDICAL_SUPPLIES_CODE = '1'

  /**
   * 薬剤分類
   */
  export const MEDICINE_CLASSIFICATION = '0'

  /**
   * 薬剤
   */
  export const MEDICINE = '1'

  /**
   * 入力データ桁数
   */
  export const NUMBER_OF_INPUT_DATA = 100

  /**
   * 未選択
   */
  export const UNSELECT = -1

  /**
   * 一行目
   */
  export const FIRST_ROW = 0

  /**
   * 下标0
   */
  export const ZERO_INDEX = 0

  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * "*"
     */
    export const REQUIRED = '*'
  }
}
