import { OrX0007Const } from '../OrX0007/OrX0007.constants'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0008Const } from '../OrX0008/OrX0008.constants'
import { OrX0008Logic } from '../OrX0008/OrX0008.logic'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { OrX0009Logic } from '../OrX0009/OrX0009.logic'
import { OrX0010Const } from '../OrX0010/OrX0010.constants'
import { OrX0010Logic } from '../OrX0010/OrX0010.logic'
import { Or15843Const } from '../Or15843/Or15843.constants'
import { Or15843Logic } from '../Or15843/Or15843.logic'
import { Or06632Const } from '../Or06632/Or06632.constants'
import { Or27539Logic } from '../Or27539/Or27539.logic'
import { Or27539Const } from '../Or27539/Or27539.constants'
import { Or28528Logic } from '../Or28528/Or28528.logic'
import { Or28528Const } from '../Or28528/Or28528.constants'
import { Or28220Logic } from '../Or28220/Or28220.logic'
import { Or28220Const } from '../Or28220/Or28220.constants'
import { Or28221Logic } from '../Or28221/Or28221.logic'
import { Or28221Const } from '../Or28221/Or28221.constants'
import { OrX0114Const } from '../OrX0114/OrX0114.constants'
import { OrX0114Logic } from '../OrX0114/OrX0114.logic'
import { Or36014Const } from '../Or36014/Or36014.constants'
import { Or36014Logic } from '../Or36014/Or36014.logic'
import { Or28153Const } from '../Or28153/Or28153.constants'
import { Or28153Logic } from '../Or28153/Or28153.logic'
import { Or28401Const } from '../Or28401/Or28401.constants'
import { Or28401Logic } from '../Or28401/Or28401.logic'
import { Or28646Const } from '../Or28646/Or28646.constants'
import { Or28646Logic } from '../Or28646/Or28646.logic'
import { Or10826Const } from '../Or10826/Or10826.constants'
import { Or10826Logic } from '../Or10826/Or10826.logic'
import { Or10827Const } from '../Or10827/Or10827.constants'
import { Or10827Logic } from '../Or10827/Or10827.logic'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { useInitialize } from '~/composables/useComponentLogic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'

/**
 * Or06632:有機体:(日課表)日課表イメージ
 * GUI00989_日課表イメージ
 *
 * @description
 * initialize処理を提供する
 *
 * <AUTHOR> 呉李彪
 */
export namespace Or06632Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or06632Const.CP_ID(0),
      uniqueCpId,
      childCps: [
        { cpId: Or11871Const.CP_ID },
        { cpId: Or00248Const.CP_ID(1) },
        { cpId: OrX0007Const.CP_ID(1) },
        { cpId: OrX0008Const.CP_ID(1) },
        { cpId: OrX0009Const.CP_ID(1) },
        { cpId: OrX0010Const.CP_ID(1) },
        { cpId: Or15843Const.CP_ID(1) },
        { cpId: Or27539Const.CP_ID(1) },
        { cpId: Or28153Const.CP_ID(1) },
        { cpId: Or28528Const.CP_ID(1) },
        { cpId: Or28220Const.CP_ID(1) },
        { cpId: Or28221Const.CP_ID(1) },
        { cpId: OrX0114Const.CP_ID(1) },
        { cpId: OrX0114Const.CP_ID(1) },
        { cpId: Or28401Const.CP_ID(1) },
        { cpId: Or36014Const.CP_ID(1) },
        { cpId: Or21813Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or21815Const.CP_ID(1) },
        { cpId: Or41179Const.CP_ID(1) },
        { cpId: Or28646Const.CP_ID(1) },
        { cpId: Or10826Const.CP_ID(1) },
        { cpId: Or10827Const.CP_ID(1) },
      ],

      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(1)].uniqueCpId)
    OrX0007Logic.initialize(childCpIds[OrX0007Const.CP_ID(1)].uniqueCpId)
    OrX0008Logic.initialize(childCpIds[OrX0008Const.CP_ID(1)].uniqueCpId)
    OrX0009Logic.initialize(childCpIds[OrX0009Const.CP_ID(1)].uniqueCpId)
    OrX0010Logic.initialize(childCpIds[OrX0010Const.CP_ID(1)].uniqueCpId)
    Or15843Logic.initialize(childCpIds[Or15843Const.CP_ID(1)].uniqueCpId)
    Or27539Logic.initialize(childCpIds[Or27539Const.CP_ID(1)].uniqueCpId)
    Or28153Logic.initialize(childCpIds[Or28153Const.CP_ID(1)].uniqueCpId)
    Or28528Logic.initialize(childCpIds[Or28528Const.CP_ID(1)].uniqueCpId)
    Or28220Logic.initialize(childCpIds[Or28220Const.CP_ID(1)].uniqueCpId)
    Or28221Logic.initialize(childCpIds[Or28221Const.CP_ID(1)].uniqueCpId)
    OrX0114Logic.initialize(childCpIds[OrX0114Const.CP_ID(1)].uniqueCpId)
    Or28401Logic.initialize(childCpIds[Or28401Const.CP_ID(1)].uniqueCpId)
    Or36014Logic.initialize(childCpIds[Or36014Const.CP_ID(1)].uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(1)].uniqueCpId)
    Or28646Logic.initialize(childCpIds[Or28646Const.CP_ID(1)].uniqueCpId)
    Or10826Logic.initialize(childCpIds[Or10826Const.CP_ID(1)].uniqueCpId)
    Or10827Logic.initialize(childCpIds[Or10827Const.CP_ID(1)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
}
