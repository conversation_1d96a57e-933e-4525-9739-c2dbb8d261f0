/**
 * Or32643:有機体:［アセスメント］画面（居宅）（3）
 * GUI00796_［アセスメント］画面（居宅）（3）
 * 保存データ・更新API
 *
 * @description
 * ［アセスメント］画面（居宅）（3）
 *
 * <AUTHOR>
 */

import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type {
  AssessmentHomeServiceUpdateInEntity,
  AssessmentHomeServiceUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentHomeServiceInitEntity'
/**
 * GUI00801_アセスメントマスタ初期情報保存APIモック
 *
 * @description
 * GUI00801_アセスメントマスタ情報データを保存する。
 * dataName："assessmentHomeServiceUpdate"
 */
export function handler(inEntity: AssessmentHomeServiceUpdateInEntity) {
  let info
  info = defaultData.dataList.filter((item) => {
    item.sc1Id === inEntity.sc1Id && item.gdlId === inEntity.gdlId
  })
  const responceJson: AssessmentHomeServiceUpdateOutEntity = {
    statusCode: 'success',
    data: {
      ...info,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
