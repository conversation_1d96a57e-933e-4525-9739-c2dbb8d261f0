<script setup lang="ts">
/**
 * Or30981：有機体：インターライ方式_アセスメント_メモ入力
 *
 * @description
 * インターライ方式_アセスメント_メモ入力テキストエリア
 *
 * <AUTHOR>
 */
import { reactive, ref, watch, nextTick, useTemplateRef, type ComponentPublicInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or30981Const } from './Or30981.constants'
import { useSetupChildProps, useColorUtils } from '#imports'
import type { Or30981Type, Or30981OnewayType } from '~/types/cmn/business/components/Or30981Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { OrX0156OnewayType, OrX0156Type } from '~/types/cmn/business/components/OrX0156Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { OrX0169OnewayType, OrX0169Type } from '~/types/cmn/business/components/OrX0169Type'
import { OrX0169Const } from '~/components/custom-components/organisms/OrX0169/OrX0169.constants'

const { t } = useI18n()

const { convertDecimalToHex, convertHexToDecimal } = useColorUtils()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  modelValue: Or30981Type
  onewayModelValue: Or30981OnewayType
}
const props: Props = defineProps<Props>()

/**
 * 子コンポーネント用変数
 */
const orX0169 = ref({ uniqueCpId: '' })

const defaultModelValue = {
  or30981: {
    content: '',
    fontSize: '14',
    fontColor: '#000000',
  } as Or30981Type,
}

const defaultOnewayModelValue = {
  or30981Oneway: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      disabled: false,
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
    /**
     * 活性/非活性制御(デフォルト: false)
     */
    disabled: false,
  } as Or30981OnewayType,
}

const local = reactive({
  or30981: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})

const localOneway = reactive({
  or30982Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
})
/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0169Const.CP_ID(0)]: orX0169.value,
})
/************************************************
 * 変数
 ************************************************/
/**
 * メモテキストエリア入力値
 */
const orX0156TypeArea = ref<OrX0156Type>({ value: '' })

/**
 * 色の設定アイコンボタン
 */
const orX0169Type = ref<OrX0169Type>({ value: '#000000' } as OrX0169Type)

const orX0156TypeAreaRef = useTemplateRef<ComponentPublicInstance>('orX0156TypeTextArea')
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue', 'onClickEditBtn'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * テキストエリアのスタイル設定
 *
 * @param fontSize - 文字サイズ
 *
 * @param color - 文字カラー
 */
const setTextAreaStyle = async (fontSize: string, color: string) => {
  const templateDom: HTMLElement = orX0156TypeAreaRef.value?.$el as HTMLElement
  await nextTick(() => {
    if (templateDom) {
      const htmlElement = templateDom.querySelector('.text-style')
      if (htmlElement) {
        const html = htmlElement.querySelectorAll('textarea')
        if (html) {
          for (const item of html) {
            if (item.value) {
              if (fontSize) {
                // メモフォント
                item.style.fontSize = fontSize?.includes('px') ? fontSize : fontSize + 'px'
              }
              if (color) {
                // メモ色
                item.style.color = color
              }
            }
          }
        }
      }
    }
  })
}

/**
 * テキストエリアのスタイル設定
 *
 * @param disabled - 活性/非活性制御フラゲ
 */
const setDisabledStyle = async (disabled: boolean) => {
  const templateDom: HTMLElement = orX0156TypeAreaRef.value?.$el as HTMLElement
  if (templateDom) {
    await nextTick(() => {
      const htmlElement = templateDom.querySelector('.root-textarea-container')
      if (htmlElement) {
        const htmlLabelElements = htmlElement.querySelectorAll('.item-label')
        // 非活性
        if (disabled) {
          // 背景色
          htmlElement.setAttribute('style', 'background: #F2F2F2')
          if (htmlLabelElements) {
            for (const item of htmlLabelElements) {
              if (item) {
                // 前景色
                item.setAttribute('style', 'color: #bdbdbd;font-weight: normal')
              }
            }
          }
        }
        // 活性
        else {
          // 背景色
          htmlElement.setAttribute('style', 'background: #fff')
          if (htmlLabelElements) {
            for (const item of htmlLabelElements) {
              if (item) {
                // 前景色
                item.setAttribute('style', 'color: rgb(var(--v-theme-text));font-weight: normal')
              }
            }
          }
        }
      }
      const htmlLabelElement = templateDom.querySelector('.item-label')
      if (htmlLabelElement) {
        // 非活性
        if (disabled) {
          // 前景色
          htmlLabelElement.setAttribute('style', 'color: #bdbdbd;font-weight: normal')
        }
        // 活性
        else {
          // 前景色
          htmlLabelElement.setAttribute(
            'style',
            'color: rgb(var(--v-theme-text));font-weight: normal'
          )
        }
      }
      const htmlIconBtnElement = templateDom.querySelector('.icon-btn')
      if (htmlIconBtnElement) {
        // 非活性
        if (disabled) {
          // 前景色
          htmlIconBtnElement.setAttribute('style', 'color: rgb(var(--v-theme-black-200));background: transparent;')
        }
        // 活性
        else {
          // 前景色
          htmlIconBtnElement.setAttribute('style', 'color: #a7bada;background: #ebf2fd;')
        }
      }

      const htmlSelectTextElement = templateDom.querySelector('.v-select__selection-text')
      if (htmlSelectTextElement) {
        // 非活性
        if (disabled) {
          // 前景色
          htmlSelectTextElement.setAttribute('style', 'color: #bdbdbd;font-weight: normal')
        }
        // 活性
        else {
          // 前景色
          htmlSelectTextElement.setAttribute(
            'style',
            'color: rgb(var(--v-theme-text));font-weight: normal'
          )
        }
      }
      const htmlSplitLineElement = templateDom.querySelectorAll('.split-line')
      if (htmlSplitLineElement) {
        for (const item of htmlSplitLineElement) {
          if (item) {
            // 非活性
            if (disabled) {
              // 背景色
              item.setAttribute('style', 'background: #b3b3b3')
            }
            // 活性
            else {
              // 背景色
              item.setAttribute(
                'style',
                'background: #a7bada'
              )
            }
          }
        }
      }
    })
  }
}

/**
 * 戻り値
 */
const updateModevValue = () => {
  const result: Or30981Type = {
    content: orX0156TypeArea.value.value,
    fontSize: '-' + local.or30981.fontSize,
    fontColor: convertHexToDecimal(orX0169Type.value.value),
  } as Or30981Type
  emit('update:modelValue', result)
}

/**
 * 入力補助付きテキストエリア値変換
 */
const orx0156UpdateModelValue = () => {
  updateModevValue()
}
/************************************************
 * ウォッチャー
 ************************************************/
/**
 * フォント値変更
 */
watch(
  () => local.or30981.fontSize,
  async (newValue) => {
    if (newValue) {
      await setTextAreaStyle(newValue, orX0169Type.value.value ?? '#000000')
      updateModevValue()
    }
  }
)

/**
 * カラー値変更
 */
watch(
  () => orX0169Type.value.value,
  async (newValue) => {
    if (newValue) {
      await setTextAreaStyle(local.or30981.fontSize, newValue ?? '#000000')
      updateModevValue()
    }
  }
)

/**
 * 属性値変更(活性/非活性制御)
 */
watch(
  () => props.onewayModelValue,
  async (newValue) => {
    if (newValue) {
      await setDisabledStyle(newValue.disabled)
    } else {
      await setDisabledStyle(false)
    }
  },
  { deep: true, immediate: true }
)

/**
 * 属性値変更
 */
watch(
  () => props.modelValue,
  async (newValue) => {
    if (newValue) {
      orX0156TypeArea.value.value = newValue.content
      orX0169Type.value.value = convertDecimalToHex(Number(newValue.fontColor))
      local.or30981.fontSize = newValue.fontSize.replace('-', '').replace('px', '')
      await setTextAreaStyle(local.or30981.fontSize, orX0169Type.value.value ?? '#000000')
    } else {
      orX0156TypeArea.value.value = ''
    }
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <g-custom-or-x-0156
    ref="orX0156TypeTextArea"
    v-model="orX0156TypeArea"
    :oneway-model-value="localOneway.or30982Oneway.orX0156OnewayType"
    @on-click-edit-btn="$emit('onClickEditBtn', $event)"
    @update:model-value="orx0156UpdateModelValue"
  >
    <template #footer>
      <base-mo01338
        :oneway-model-value="localOneway.or30982Oneway.mo13380OnewayType"
        style="background: transparent; align-content: center"
      ></base-mo01338>
      <!-- 原子：セレクトボックス -->
      <base-at-select
        v-model="local.or30981.fontSize"
        :name="Or30981Const.STR.EMPTY"
        :items="localOneway.or30982Oneway.items"
        class="fontSizeItem"
        :menu-props="{ width: '70px', contentClass: 'no-horizontal-scroll' }"
      >
        <template #message> </template>
        <template
          v-for="(slot, slotName) in $slots"
          #[slotName]="data"
        >
          <slot
            :name="slotName"
            v-bind="data"
          />
        </template>
      </base-at-select>
      <!-- 区切り線 -->
      <div
        v-if="localOneway.or30982Oneway.orX0156OnewayType?.showDividerLineFlg"
        class="split-line"
      ></div>
      <div
        class="d-flex"
        style="align-items: center; cursor: pointer"
      >
        <g-custom-or-x-0169
          v-bind="orX0169"
          v-model="orX0169Type"
          :oneway-model-value="localOneway.or30982Oneway.orX0169OnewayType"
        ></g-custom-or-x-0169>
      </div>
    </template>
  </g-custom-or-x-0156>
</template>
<style>
.no-horizontal-scroll {
  overflow-x: hidden !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.25) !important;
  z-index: 3000 !important;
}

.no-horizontal-scroll .v-list-item {
  max-width: 70px;
  min-height: 36px;
  height: 36px !important;
  line-height: 36px !important;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
<style scoped lang="scss">
$tag-line-min-height: 36px;
$icon-color: #a7bada;

// 区切り線
.split-line {
  width: 2px;
  height: $tag-line-min-height;
  background: $icon-color;
}

:deep(.v-input__details) {
  display: none;
}

:deep(.v-field) {
  background-color: transparent !important;
}

:deep(.v-text-field) {
  background-color: transparent !important;
}

:deep(.tag-container) {
  align-items: center;
}

.fontSizeItem {
  width: 70px;
  max-width: 70px;
  min-height: 32px !important;
  height: 32px !important;
  border: 1px rgb(var(--v-theme-black-200)) solid;
  border-radius: 4px;

  :deep(.v-field--appended) {
    padding-inline-end: 0px;
  }

  :deep(.v-field__field) {
    padding-left: 8px !important;
  }

  :deep(.v-field__input) {
    min-height: 32px !important;
    height: 32px !important;
  }
}
</style>
