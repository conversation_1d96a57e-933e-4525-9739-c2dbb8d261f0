<script setup lang="ts">
/**
 * Or28396:有機体:印刷設定
 * GUI00669_印刷設定
 *
 * @description
 * （アセスメント）情報収集印刷設定モーダル
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or28396Const } from './Or28396.constants'
import type { Or28396StateType } from './Or28396.type'
// import { useSetupChildProps, useScreenOneWayBind, useNuxtApp, dateUtils } from '#imports'
import { useSetupChildProps, useScreenOneWayBind, dateUtils } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { UserEntity } from '~/repositories/cmn/entities/AssessmentInterRAIPrintSettingsSelectEntity'
import type { Mo01408OnewayType } from '~/types/business/components/Mo01408Type'
import type {
  assessment,
  PrtEntity,
  InformationPrintSettingsInitialSelectInEntity,
  InformationPrintSettingsInitialSelectOutEntity,
  AssessmentHistoryInfoSelectInEntity,
  AssessmentHistoryInfoSelectOutEntity,
  sel,
} from '~/repositories/cmn/entities/InformationPrintSettingsInitialSelectEntity'
import type {
  LedgerInitializeDataComSelectOutEntity,
  LedgerInitializeDataComSelectInEntity,
} from '~/repositories/cmn/entities/ledgerInitializeDataComSelect'

import type {
  choPrtList,
  PrintSettingsInfoComUpdateInEntity,
} from '~/repositories/cmn/entities/printSettingsInfoComUpdate'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { Or28396Param } from '~/components/custom-components/organisms/Or28396/Or28396.type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { OrX0133Const } from '~/components/custom-components/organisms/OrX0133/OrX0133.constants'
import { OrX0133Logic } from '~/components/custom-components/organisms/OrX0133/OrX0133.logic'
import type {
  OrX0133OnewayType,
  OrX0133TableData,
} from '~/types/cmn/business/components/OrX0133Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useReportUtils, reportOutputType } from '~/utils/useReportUtils'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { InfoCollectionReportEntity } from '~/repositories/cmn/entities/InfoCollectionReportEntity'
// import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { CustomClass } from '~/types/CustomClassType'
import { useValidation } from '@/utils/useValidation'
import type {
  AssessmentHistoryInfoSubjectSelectInEntity,
  AssessmentHistoryInfoSubjectSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentHistoryInfoSubjectSelect'

const { byteLength } = useValidation()
const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()
// const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const orX0117 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0133 = ref({ uniqueCpId: OrX0133Const.CP_ID(0) })
const orX0130 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })

const localOneway = reactive({
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo01338OneWayLeftTitle: {
    value: t('label.left-bracket'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    width: '55',
    maxLength: '2',
    rules: [byteLength(2)],
    customClass: new CustomClass({ outerClass: 'mr-0' }),
    class: 'inputClass',
  } as Mo00045OnewayType,
  mo01338OneWayRightTitle: {
    value: t('label.right-bracket'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * タイトル
   */
  mo01338OneWayTitle: {
    value: t('label.print-settings-title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * タイトル表示
   */
  mo01338OneWay: {
    value: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 指定日
   */
  mo00020OneWay: {
    showItemLabel: false,
    showSelectArrow: false,
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 記入用シートを印刷する
   */
  mo00018OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * アセスメントタイプ
   */
  mo00039OneWayAssessmentType: {
    name: '',
    showItemLabel: false,
    inline: false,
    disabled: true,
  } as Mo00039OnewayType,
  /**
   * 印刷時に色をつける
   */
  mo00018OneWayColor: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-annotations'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 別紙に印刷する
   */
  mo00018OneWayAppendixPrint: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.appendix-print'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 別紙に印刷する補足ラベル
   */
  mo00018OneWayAppendixPrintSupplementLabel: {
    itemLabel: '',
    value: t('label.appendix-print-supplement'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'Or56885ItemClass',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネ
   */
  mo01338OneWayCareManagerInCharge: {
    value: t('label.care-manager-in-charge'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 担当ケアマネ選択アイコンボタン
   */
  m01408OnewayType: {
    itemLabel: '',
    showItemLabel: false,
    width: '200px',
    items: [],
    disabled: false,
  } as Mo01408OnewayType,

  /**
   * 担当ケアマネ表示ラベル
   */
  mo01338OneWayCareManagerInChargeLabel: {
    value: 'ほのぼの 三郎',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    disabled: false,
  } as Mo00609OnewayType,

  /**
   * 基準日
   */
  mo00615OneWayType: {
    itemLabel: t('label.base-date'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
})

/**
 * 担当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

const localInput = reactive({
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  textInput: {
    value: '',
  } as Mo00045Type,
})

/**
 * 親画面の情報
 */
const local = {
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or28396Const.DEFAULT.STR.ONE,
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: Or28396Const.DEFAULT.KOJINHOGO_USED_FLG,
  /**
   * 個人情報番号
   */
  sectionAddNo: Or28396Const.DEFAULT.SECTION_ADD_NO,
  /**
   * 親画面.事業所ID
   */
  svJigyoId: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.施設ID
   */
  shisetuId: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.担当者ID
   */
  tantoId: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 居宅版 施設版 はんてい
   */
  selectType: '',
  /**
   * 親画面.セクション名
   */
  sectionName: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.選択帳票番号
   */
  choIndex: Or28396Const.DEFAULT.INDEX,
  /**
   * 親画面.システムコード
   */
  sysCd: '',
  /**
   * 親画面.システム略称
   */
  sysRyaku: '',
  /**
   * 親画面.事業所名
   */
  svJigyoName: '',
  /**
   * 親画面.法人ID
   */
  houjinId: '',
  /**
   * 親画面.職員ID
   */
  shokuId: '',
  /**
   * 親画面.利用者ID
   */
  userId: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所名
   */
  svJigyoKnj: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.処理年月日
   */
  processYmd: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 選択行のセクション番号
   */
  currentSectionNo: Or28396Const.DEFAULT.SECTION_NO,
  /**
   * 選択された帳票のプロファイル
   */
  profile: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者ID
   */
  selectUserId: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historySelect: false,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userSelect: false,
  /**
   * 帳票番号
   */
  prtNo: Or28396Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 帳票ID
   */
  reportId: Or28396Const.DEFAULT.STR.EMPTY,
}

const localData: InformationPrintSettingsInitialSelectOutEntity = {
  data: {},
} as InformationPrintSettingsInitialSelectOutEntity
/**************************************************
 * 変数定義
 **************************************************/
// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or28396',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or28396ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or28396_content',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or28396Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '140',
    },
  ],
  items: [],
  height: 637,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>('')

/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 記入用シートを印刷する
 */
const mo00018Type = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * アセスメントタイプ
 */
const mo00039OneWayAssessmentTypeType = ref<string>('')

/**
 * 印刷時に色をつける
 */
const mo00018TypeColor = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

//画面.出力帳票名
const defPrtTitle = ref('')

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')

/**
 * 基準日
 */
const mo00020TypeKijunbi = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

let choPrtInfoList = [] as PrtEntity[]

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)
/**
 * 基準日フラゲ
 */
const kijunbiFlag = ref<boolean>(false)
/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(false)

/**
 * 利用者列幅
 */
const userCols = ref<number>(5)

// プロファイル
const choPro = ref('')

const orX0133OnewayModel = reactive<OrX0133OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: local.kikanFlg,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: Or28396Const.DEFAULT.TANI,
  tableStyle: 'width:436px;height:510px',
  itemShowFlg: {},
  rirekiList: [] as OrX0133TableData[],
})

// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
//警告ウィンドウを表示
const showDialogOr21815 = computed(() => {
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})
// エラー表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width:260px',
})

/**
 * 担当ケアマネ選択アイコン
 */
const tantoIconBtn = ref<boolean>(false)

/**
 * 担当ケアマネ表示ラベル
 */
const tantoLabel = ref<boolean>(false)

let selList = [] as sel[]
/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or28396Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28396StateType>({
  cpId: Or28396Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28396Const.DEFAULT.IS_OPEN
    },
    param: (value) => {
      if (value) {
        local.kikanFlg = value.kikanFlg
        local.svJigyoId = value.svJigyoId
        local.shisetuId = value.shisetuId
        local.tantoId = value.tantoId
        local.sectionName = value.sectionName
        local.choIndex = value.choIndex
        local.sysCd = value.sysCd
        local.houjinId = value.houjinId
        local.userId = value.userId
        local.svJigyoKnj = ''
        local.sysRyaku = value.sysRyaku
        local.svJigyoName = value.svJigyoName
        local.shokuId = value.shokuId
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

// 帳票イニシャライズデータを取得する
const reportInitData = ref([
  {
    // 氏名伏字印刷
    prtName: '',
    // 文書番号印刷
    prtBng: '',
    // 個人情報印刷
    prtKojin: '',
  },
])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面ID
const screenId = 'GUI00669'
// ルーティング
const routing = 'GUI00669/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

onMounted(async () => {
  orX0133OnewayModel.itemShowFlg = {
    kijunbiYmdShokuinKnjFlg: true,
    kaiteiKnjFlg: true,
  }
  // 汎用コード取得API実行
  await initCodes()

  // 初期情報取得
  await init()
})

// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  // Or28396のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別コード
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_REVISION_INFO_SHEET },
    // { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    mo00039Type.value = bizukePrintCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWay.items = list
  }

  // アセスメントタイプ
  const assessmentKindCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_REVISION_INFO_SHEET
  )

  if (assessmentKindCodeTypes?.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of assessmentKindCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayAssessmentType.items = list
  }

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039OneWayUserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039OneWayHistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期プロジェクト設定
 */
const initSetting = () => {
  // 親画面.処理年月日が""の場合
  if (Or28396Const.DEFAULT.STR.EMPTY === local.processYmd) {
    // 担当ケアマネ選択
    tantoIconBtn.value = false
    // 担当ケアマネ表示
    tantoLabel.value = false
  } else {
    // 担当ケアマネ選択
    tantoIconBtn.value = true
    // 担当ケアマネ表示
    tantoLabel.value = true
  }
}

/**
 * 初期情報取得
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: InformationPrintSettingsInitialSelectInEntity = {
    kikanFlg: local.kikanFlg,
    // sysCd: systemCommonsStore.getSystemCode,
    sysCd: local.sysCd,
    sysRyaku: local.sysRyaku,
    kinounameKnj: Or28396Const.DEFAULT.KINOUNAME_KNJ,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    shokuId: local.shokuId,
    userId: local.userId,
    tantoId: local.tantoId,
    appYmd: systemCommonsStore.getSystemDate!,
    sectionName: local.sectionName,
    choIndex: local.choIndex,
    kojinhogoFlg: Or28396Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or28396Const.DEFAULT.SECTION_ADD_NO,
  } as InformationPrintSettingsInitialSelectInEntity
  const resp: InformationPrintSettingsInitialSelectOutEntity = await ScreenRepository.select(
    'informationPrintSettingsInitialSelect',
    inputData
  )
  if (resp?.data) {
    localData.data = { ...resp?.data }
    selList = resp.data.selList
    const choPrtList: Mo01334Items[] = []

    for (const item of resp.data.choPrtList) {
      if (item) {
        choPrtList.push({
          id: item.prtNo,
          mo01337OnewayLedgerName: {
            value: item.defPrtTitle,
            unit: Or28396Const.DEFAULT.STR.EMPTY,
          } as Mo01337OnewayType,
          prnDate: item.prndate === Or28396Const.DEFAULT.STR.TRUE,
          selectable: true,
          prtNo: item.prtNo,
        } as Mo01334Items)

        if (choPrtList.length === Or28396Const.DEFAULT.NUMBER.ONE) {
          mo00018TypeColor.value.modelValue = item.param05 === Or28396Const.DEFAULT.STR.TRUE
        }
      }
    }
    choPrtInfoList = [...resp.data.choPrtList]
    mo01334Oneway.value.items = choPrtList

    outputLedgerName(local.choIndex)
  }
  await getReportInfoDataList()
  // await getPrintSettingsHistoryList()
  initSetting()
}

/**
 * アセスメント履歴一覧データ
 *
 * @param userId - 利用者ID
 *
 * @param assessmentList - アセスメント履歴リスト
 */
const getHistoryData = (userId: string, assessmentList: assessment[]) => {
  orX0133OnewayModel.rirekiList = []
  for (const item of assessmentList) {
    if (Or28396Const.DEFAULT.KIKAN_FLG_1 === local.kikanFlg) {
      const tmpItem = {
        planPeriod:
          t('label.plan-period') +
          t('label.colon-mark') +
          item.startYmd +
          t('label.wavy') +
          item.endYmd,
      } as OrX0133TableData
      orX0133OnewayModel.rirekiList.push(tmpItem)
    }
    if (item.historyList.length > 0) {
      for (const tempData of item.historyList) {
        const tmpItem1 = {
          planPeriod: '',
          createYmd: '',
          caseNo: '1',
          kijunbiYmd: tempData.createYmd,
          shokuKnj: '',
          shokuinKnj: selList.find((item) => item.chkShokuId === tempData.shokuId)?.shokuinKnj,
          kaisuu: '',
          youshikiKnj: '',
          gdlId: '',
          assType: '',
          assDateYmd: '',
          kaiteiKnj: localOneway.mo00039OneWayAssessmentType.items?.find(
            (item) => item.value === tempData.kaiteiKbn
          )?.label,
        } as OrX0133TableData
        orX0133OnewayModel.rirekiList.push(tmpItem1)
      }
    }
  }
}

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataComSelectInEntity = {
    sysCd: local.sysCd,
    kinounameKnj: Or28396Const.DEFAULT.KINOUNAME_KNJ,
    shokuId: local.shokuId,
    sectionKnj: choPro.value === '' ? '1' : choPro.value,
    kojinhogoUsedFlg: local.kojinhogoUsedFlg,
    sectionAddNo: Or28396Const.DEFAULT.STR.ZERO,
  }
  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataComSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataComSelect',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject
}

/**
 * 画面印刷設定内容を保存
 */

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  await savePrintSettingInfo()
  setState({
    isOpen: false,
    param: {} as Or28396Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 印刷帳票名が未入力です。
  if (!localOneway.mo01338OneWay.value) {
    showOr21815MsgTwoBtn(t('message.w-cmn-20845'))
    return
  }
  // 選択された帳票のプロファイルが””の場合
  if (local.profile === Or28396Const.DEFAULT.STR.EMPTY) {
    // メッセージを表示
    showOr21813MsgOneBtn(t('message.e-cmn-40172'))
    return
  }
  // 利用者選択方法が「単一」
  if (Or28396Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 履歴選択が「単一」
    if (Or28396Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      orX0117Oneway.type = Or28396Const.DEFAULT.STR.ONE
      //記入用シートを印刷するチェック入れるの場合
      const value = localOneway.mo00039OneWayAssessmentType.items?.find(
        (item) =>
          item.label ===
          OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList[0].kaiteiKnj
      )?.value
      await downloadPdf(value)
      if (mo00018Type.value.modelValue) {
        // 帳票側の処理を呼び出し、帳票レイアウトのみ印刷する

        return
      }
      // 記入用シートを印刷するチェック外すの場合
      else {
        // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
        if (!OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList.length) {
          const dialogResult = await openConfirmDialog(
            or21814.value.uniqueCpId,
            t('message.i-cmn-11393')
          )
          // はい
          if (dialogResult === Or28396Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
            // 処理終了
            return
          }
        }

        // 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
        if (!OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList.length) {
          const dialogResult = await openConfirmDialog(
            or21814.value.uniqueCpId,
            t('message.i-cmn-11455')
          )
          // はい
          if (dialogResult === Or28396Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
            // 処理終了
            return
          }
        }
      }
    }
    // 履歴選択方法が「複数」
    else if (Or28396Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value) {
      // 履歴情報リストにデータを選択する場合
      if (OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList.length) {
        // 印刷設定情報リストを作成
        const list = OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList
        list?.forEach((item, index) => {
          if (item.kaiteiKnj) {
            const val = localOneway.mo00039OneWayAssessmentType.items?.find(
              (item) =>
                item.label ===
                OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList[index].kaiteiKnj
            )?.value
            void downloadPdf(val)
            void PrintSettingsSubjectSelect(val)
          }
        })
      }
    }
  }

  // // 利用者選択方法が「複数」の場合
  if (Or28396Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
    // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
    orX0117Oneway.type = Or28396Const.DEFAULT.STR.ONE
    if (!OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList.length) {
      const dialogResult = await openConfirmDialog(
        or21814.value.uniqueCpId,
        t('message.i-cmn-11393')
      )
      // はい
      if (dialogResult === Or28396Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        // 処理終了
        return
      }
    }
  }

  // 履歴選択が「複数」、履歴一覧が0件選択場合（※履歴リストが0件、複数件を含む）
  if (
    Or28396Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value &&
    !OrX0133Logic.event.get(orX0133.value.uniqueCpId)?.orX0133DetList.length
  ) {
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, t('message.i-cmn-11455'))
    // はい
    if (dialogResult === Or28396Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
      // 処理終了
      return
    }
  }

  // AC019-2と同じ => 画面の印刷設定情報を保存する
  await savePrintSettingInfo()

  // 「AC020-5-2 また AC020-5-3」で取得した印刷設定情報リスト＞0件の場合
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷設定情報リストを作成(利用者選択が「複数」、利用者情報リストにデータを選択する場合)
 *
 * @param val - 改訂区分
 */
const PrintSettingsSubjectSelect = async (val: unknown) => {
  // 印刷設定情報リストを作成する
  const inputData: AssessmentHistoryInfoSubjectSelectInEntity = {
    prtNo: local.prtNo,
    svJigyoId: local.svJigyoId,
    kijunbiYmd: mo00020TypeKijunbi.value.value ?? Or28396Const.DEFAULT.STR.EMPTY,
    mstKbn: local.sectionName,
    userlist: local.userList,
  } as AssessmentHistoryInfoSubjectSelectInEntity
  const resp: AssessmentHistoryInfoSubjectSelectOutEntity = await ScreenRepository.select(
    'assessmentHistoryInfoSubjectSelect',
    inputData
  )

  const list: OrX0117History[] = []
  if (resp.data) {
    for (const data of resp.data.prtHistoryList) {
      if (data) {
        // 利用者複数の場合
        const selectType = local.sectionName.slice(
          local.sectionName.length - 3,
          local.sectionName.length - 1
        )
        if (Or28396Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 印刷設定情報リストを作成
          const reportData: InfoCollectionReportEntity = {
            printSet: {
              shiTeiKubun: mo00039Type.value,
              shiTeiDate: mo00020Type.value.value,
            },
            printOption: {
              keishoFlg: localInput.mo00018TypeChangeTitle.modelValue ? '1' : '0',
              keisho: localInput.textInput.value,
              emptyFlg: mo00018Type.value.modelValue ? '1' : '0',
              chushakuFlg: mo00018TypeColor.value.modelValue ? '1' : '0',
            },
            ass1Id: mo01334Type.value.value,
            mstKbn: selectType === t('label.survey-assessment-kind-facility-version') ? '1' : '2',
            kaiteiKbn: val,
            jigyoName: local.svJigyoName,
            teikyouSvJigyoIdList: [{ svJigyoId: '1' }],
            /** 承認欄保持フラグ */
            shoninFlg:
              cmnRouteCom.getInitialSettingMaster()?.shoninFlg ?? Or28396Const.DEFAULT.STR.EMPTY,
            /** 敬称使用フラグ */
            keishoFlg:
              cmnRouteCom.getInitialSettingMaster()?.keishoFlg ?? Or28396Const.DEFAULT.STR.EMPTY,
            /** 敬称文字列 */
            keishoKnj:
              cmnRouteCom.getInitialSettingMaster()?.keishoKnj ?? Or28396Const.DEFAULT.STR.EMPTY,
          } as InfoCollectionReportEntity
          list.push({
            reportId: local.reportId,
            outputType: reportOutputType.DOWNLOAD,
            reportData: reportData,
            userName: data.userName,
            historyDate: data.startYmd,
            result: data.result,
          } as OrX0117History)
        }
      }
    }
  }
  orX0117Oneway.historyList = list
}
/**
 * pdf download
 *
 * @param val - 改訂区分
 */

const downloadPdf = async (val: unknown) => {
  let selectIndex = ''
  mo01334Oneway.value.items.map((item, index) => {
    if (item.id === mo01334Type.value.value) {
      // selectIndex = index < 15 ? (index + 1).toString() : ''
      selectIndex = (index + 1).toString()
    }
  })
  const selectType = local.sectionName.slice(
    local.sectionName.length - 3,
    local.sectionName.length - 1
  )

  const inputData: InfoCollectionReportEntity = {
    printSet: {
      shiTeiKubun: mo00039Type.value,
      shiTeiDate: mo00020Type.value.value,
    },
    printOption: {
      keishoFlg: localInput.mo00018TypeChangeTitle.modelValue ? '1' : '0',
      keisho: localInput.textInput.value,
      emptyFlg: mo00018Type.value.modelValue ? '1' : '0',
      chushakuFlg: mo00018Type.value.modelValue ? '1' : '0',
    },
    ass1Id: mo01334Type.value.value,
    mstKbn: selectType === t('label.survey-assessment-kind-facility-version') ? '1' : '2',
    kaiteiKbn: val,
    // level1Id: selectIndex,
    // asYmd: localData.data.sysYmd,
    jigyoName: local.svJigyoName,
    teikyouSvJigyoIdList: [{ svJigyoId: '1' }],
    /** 承認欄保持フラグ */
    shoninFlg: cmnRouteCom.getInitialSettingMaster()?.shoninFlg ?? Or28396Const.DEFAULT.STR.EMPTY,
    /** 敬称使用フラグ */
    keishoFlg: cmnRouteCom.getInitialSettingMaster()?.keishoFlg ?? Or28396Const.DEFAULT.STR.EMPTY,
    /** 敬称文字列 */
    keishoKnj: cmnRouteCom.getInitialSettingMaster()?.keishoKnj ?? Or28396Const.DEFAULT.STR.EMPTY,
  } as InfoCollectionReportEntity

  if (Number(selectIndex) !== 16) {
    inputData.level1Id = selectIndex
    inputData.asYmd = localData.data.sysYmd
  }

  // 帳票出力
  await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
  return
}

/**
 * 切替前の印刷設定を保存する
 *
 * @param selectId - 出力帳票ID
 */
const setBeforChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.choPrtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          item.prndate = mo00039Type.value
          break
        }
      }
    }
  }
}

/**
 * 切替後の印刷設定を画面に設定する
 *
 * @param selectId - 出力帳票ID
 */
const setAfterChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.choPrtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          mo00039Type.value = item.prndate
          break
        }
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const outputLedgerName = (selectId: string) => {
  if (!selectId) {
    selectId = Or28396Const.DEFAULT.SECTION_NO
  }
  let label = Or28396Const.DEFAULT.STR.EMPTY
  for (const item of mo01334Oneway.value.items) {
    if (item) {
      if (selectId === item.id && item.mo01337OnewayLedgerName) {
        const data = item.mo01337OnewayLedgerName as Mo01337OnewayType
        label = data.value
        mo01334Type.value.value = item.id

        // プロファイル
        local.profile = item.profile as string

        // 出力帳票名一覧に選択行番号
        local.index = item.index as string

        // 帳票番号
        local.prtNo = item.prtNo as string

        defPrtTitle.value = label

        // 帳票ID
        setReportId(item.id)
        break
      }
    }
  }
  localOneway.mo01338OneWay.value = label

  local.currentSectionNo = selectId

  // 画面PDFダウンロードボタン活性非活性設定
  pdfDownloadBtnSetting(selectId)
}

/**
 * 帳票ID設定
 *
 * @param prtNo - 帳票番号
 */
const setReportId = (prtNo: string) => {
  const selectType = local.sectionName.slice(
    local.sectionName.length - 3,
    local.sectionName.length - 1
  )
  switch (prtNo) {
    case Or28396Const.DEFAULT.SECTION_NO:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACLITY_ALL
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_ALL
      break
    case Or28396Const.DEFAULT.STR.ONE:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_1
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_1
      break
    case Or28396Const.DEFAULT.STR.TWO:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_2
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_2
      break
    case Or28396Const.DEFAULT.STR.THREE:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_3
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_3
      break
    case Or28396Const.DEFAULT.STR.FOUR:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_4
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_4
      break
    case Or28396Const.DEFAULT.STR.FIVE:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_5
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_5
      break
    case Or28396Const.DEFAULT.STR.SIX:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_6
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_6
      break
    case Or28396Const.DEFAULT.STR.SEVEN:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_7
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_7
      break
    case Or28396Const.DEFAULT.STR.EIGHT:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_8
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_8
      break
    case Or28396Const.DEFAULT.STR.NINE:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_9
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_9
      break
    case Or28396Const.DEFAULT.STR.TEN:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_10
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_10
      break
    case Or28396Const.DEFAULT.STR.ELEVEN:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_11
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_11
      break
    case Or28396Const.DEFAULT.STR.TWELVE:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_12
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_12
      break
    case Or28396Const.DEFAULT.STR.THIRTEEN:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_13
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_13
      break
    case Or28396Const.DEFAULT.STR.FOURTEEN:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_14
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_14
      break
    case Or28396Const.DEFAULT.STR.FIFTEEN:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_OTHER
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_OTHER
      break
    case Or28396Const.DEFAULT.STR.SIXTEEN:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_MEDICATION
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_MEDICATION
      break
    case Or28396Const.DEFAULT.STR.SEVENTEEN:
      local.reportId =
        selectType === t('label.survey-assessment-kind-facility-version')
          ? Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.FACILITY_CONDITION
          : Or28396Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.HOME_CONTIION
      break
    default:
      local.reportId = Or28396Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 画面PDFダウンロードボタン活性非活性設定
 *
 * @param selectId - 出力帳票ID
 */
const pdfDownloadBtnSetting = (selectId: string) => {
  // 記入用シートを印刷するチェックボックスがチェックオンの場合
  if (mo00018Type.value.modelValue) {
    // 居宅版が選択の場合、アセスメント表（R）選択の場合
    if (
      mo00039OneWayAssessmentTypeType.value === Or28396Const.SURVEY_ASSESSMENT_KIND.HOME &&
      selectId === Or28396Const.DEFAULT.STR.NINETEEN
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 施設版が選択の場合、アセスメント表（Q,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value === Or28396Const.SURVEY_ASSESSMENT_KIND.FACILITY &&
      (selectId === Or28396Const.DEFAULT.STR.NINETEEN ||
        selectId === Or28396Const.DEFAULT.STR.TEENTY ||
        selectId === Or28396Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 高齢者住宅版が選択の場合、アセスメント表（Q,R,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value ===
        Or28396Const.SURVEY_ASSESSMENT_KIND.SENIOR_HOUSING &&
      (selectId === Or28396Const.DEFAULT.STR.NINETEEN ||
        selectId === Or28396Const.DEFAULT.STR.TEENTY ||
        selectId === Or28396Const.DEFAULT.STR.TEENTY ||
        selectId === Or28396Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    } else {
      // PDFダウンロードボタンが活性
      localOneway.mo00609Oneway.disabled = false
    }
  }
  // 履歴･利用者共に単一の場合
  else if (
    mo00039OneWayUserSelectType.value === Or28396Const.DEFAULT.TANI &&
    mo00039OneWayHistorySelectType.value === Or28396Const.DEFAULT.TANI
  ) {
    // 居宅版が選択の場合、アセスメント表（R）選択の場合
    if (
      mo00039OneWayAssessmentTypeType.value === Or28396Const.SURVEY_ASSESSMENT_KIND.HOME &&
      selectId === Or28396Const.DEFAULT.STR.TEENTY
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 施設版が選択の場合、アセスメント表（Q,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value === Or28396Const.SURVEY_ASSESSMENT_KIND.FACILITY &&
      (selectId === Or28396Const.DEFAULT.STR.NINETEEN ||
        selectId === Or28396Const.DEFAULT.STR.TEENTY ||
        selectId === Or28396Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 高齢者住宅版が選択の場合、アセスメント表（Q,R,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value ===
        Or28396Const.SURVEY_ASSESSMENT_KIND.SENIOR_HOUSING &&
      (selectId === Or28396Const.DEFAULT.STR.NINETEEN ||
        selectId === Or28396Const.DEFAULT.STR.TEENTY ||
        selectId === Or28396Const.DEFAULT.STR.TEENTY ||
        selectId === Or28396Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    } else {
      // PDFダウンロードボタンが活性
      localOneway.mo00609Oneway.disabled = false
    }
  }
  // 上記以外の場合
  else {
    // PDFダウンロードボタンが活性
    localOneway.mo00609Oneway.disabled = false
  }
}

/**
 * 印刷設定履歴リスト取得
 */
const getPrintSettingsHistoryList = async () => {
  const selectType = local.sectionName.slice(
    local.sectionName.length - 3,
    local.sectionName.length - 1
  )

  // バックエンドAPIから初期情報取得
  const inputData: AssessmentHistoryInfoSelectInEntity = {
    svJigyoId: local.svJigyoId,
    userId: local.selectUserId,
    kikanFlg: local.kikanFlg,
    mstKbn: selectType === t('label.survey-assessment-kind-facility-version') ? '1' : '2',
  } as AssessmentHistoryInfoSelectInEntity
  const resp: AssessmentHistoryInfoSelectOutEntity = await ScreenRepository.select(
    'assessmentHistoryInfoSelect',
    inputData
  )
  if (resp.data) {
    // アセスメント履歴一覧データ
    getHistoryData(local.selectUserId, resp.data.assessmentList)
  }
}

/**
 * 「印刷時に色をつける」クリック
 */
const printColorCheckBoxCliick = () => {
  // 画面.帳票番号は1のパラメータ05＝画面.印刷時に色をつける（2：Off、1：On）
  for (const item of localData.data.choPrtList) {
    if (item) {
      if (item.prtNo === Or28396Const.DEFAULT.STR.ONE) {
        // On
        if (mo00018TypeColor.value) {
          item.param05 = Or28396Const.DEFAULT.TYPE_COLOR.ON
        }
        // Off
        else {
          item.param05 = Or28396Const.DEFAULT.TYPE_COLOR.OFF
        }
        break
      }
    }
  }
}

/**
 * 画面ボタン活性非活性設定
 */
const btnItemSetting = () => {
  // 利用者選択方法が「単一」の場合
  if (Or28396Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 基準日を非表示にする
    // 履歴選択を活性表示にする
    kijunbiFlag.value = false

    // 履歴選択方法が「単一」の場合
    if (Or28396Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWay.disabled = false
    }
    // 以外の場合
    else {
      // 記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWay.disabled = true
    }

    // 記入用シートを印刷するが「チェックオンの場合
    if (mo00018Type.value.modelValue) {
      localOneway.mo00039OneWayAssessmentType.disabled = false
    }
    // 以外の場合
    else {
      localOneway.mo00039OneWayAssessmentType.disabled = true
    }
  }
  // 以外の場合
  else {
    // 基準日を活性表示にする
    // 履歴選択を非表示にする
    kijunbiFlag.value = true

    localOneway.mo00018OneWay.disabled = true
    localOneway.mo00039OneWayAssessmentType.disabled = true
  }

  // 履歴選択方法が「単一」の場合
  if (Or28396Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
    // 利用者選択方法が「単一」の場合
    if (Or28396Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWay.disabled = false
    }
    // 以外の場合
    else {
      localOneway.mo00018OneWay.disabled = true
    }
  }
  // 以外の場合
  else {
    // 記入用シートを印刷するを非活性表示にする
    localOneway.mo00018OneWay.disabled = true

    // 記入用シートを印刷するをチェックオフにする
    mo00018Type.value.modelValue = false
    // 記入用シート方式を非活性表示にする
    localOneway.mo00039OneWayAssessmentType.disabled = true
  }

  // 履歴一覧セクション
  if (Or28396Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    userCols.value = 5
    mo01334TypeHistoryFlag.value = true
  } else {
    userCols.value = 12
    mo01334TypeHistoryFlag.value = false
  }

  // 担当ケアマネ選択アイコンボタン非活性/活性設定
  const kkjTantoFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? Or28396Const.DEFAULT.STR.EMPTY
  // 共通情報.担当ケアマネ設定フラグ > 0、且つ、共通情報.担当者IDが0以外の場合
  if (
    systemCommonsStore.getManagerId !== Or28396Const.DEFAULT.STR.ZERO &&
    parseInt(kkjTantoFlg) > Or28396Const.DEFAULT.NUMBER.ZERO
  ) {
    // 非活性
    localOneway.m01408OnewayType.disabled = true
  }
  // その他場合
  else {
    // 活性
    localOneway.m01408OnewayType.disabled = false
  }
}

/**
 * エラーダイアログ表示
 *
 * @param errormsg - Message
 */
function showOr21813MsgOneBtn(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no
 */
const openConfirmDialog = async (
  uniqueCpId: string,
  paramDialogText: string
): Promise<'yes' | 'no'> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: 'yes' | 'no' = Or28396Const.DEFAULT.STR.YES

        if (event?.firstBtnClickFlg) {
          result = Or28396Const.DEFAULT.STR.YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or28396Const.DEFAULT.STR.YES
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログ表示
 *
 * @param warning - warning - Message
 */
function showOr21815MsgTwoBtn(warning: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      dialogTitle: t('label.warning'),
      dialogText: warning,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
    },
  })
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 印刷設定情報を保存する
 */
async function savePrintSettingInfo() {
  const choPrtList = [] as choPrtList[]
  choPrtInfoList.forEach((item) => {
    const { prndate, ...obj } = item
    choPrtList.push({
      prndate: prndate,
      ...obj,
    })
  })

  const inputData: PrintSettingsInfoComUpdateInEntity = {
    sysCd: local.sysCd,
    sysRyaku: Or28396Const.DEFAULT.SYS_RYAKU,
    kinounameKnj: Or28396Const.DEFAULT.KINOUNAME_KNJ,
    houjinId: local.houjinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    shokuId: local.shokuId,
    kojinhogoFlg: Or28396Const.DEFAULT.STR.ZERO,
    sectionAddNo: Or28396Const.DEFAULT.SECTION_ADD_NO,
    iniDataList: reportInitData.value ,
    choPrtList: choPrtList,
    choPro: choPro.value,
    sectionName: local.sectionName,
  }

  // バックエンドAPIからsave
  await ScreenRepository.update('printSettingsInfoComUpdate', inputData)
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  async (newValue, oldValue) => {
    orX0133OnewayModel.kikanFlg = local.kikanFlg
    setBeforChangePrintData(oldValue)
    setAfterChangePrintData(newValue)
    outputLedgerName(newValue)
    await getReportInfoDataList()

    if (newValue) {
      choPrtInfoList.forEach((item) => {
        if (newValue === item.prtNo) {
          mo00039Type.value = item.prndate
          localInput.mo00018TypeChangeTitle.modelValue =
            item.param03 === Or28396Const.DEFAULT.STR.ONE ? true : false

          localInput.textInput.value = item.param04
          mo00018TypeColor.value.modelValue =
            item.param05 === Or28396Const.DEFAULT.STR.ONE ? true : false
          //画面.選択された帳票のプロファイル
          choPro.value = item?.choPro
        }
      })
    }
    if (oldValue) {
      choPrtInfoList.forEach((item) => {
        if (oldValue === item.prtNo) {
          item.prndate = mo00039Type.value
          item.param03 =
            localInput.mo00018TypeChangeTitle.modelValue === true
              ? Or28396Const.DEFAULT.STR.ONE
              : Or28396Const.DEFAULT.STR.ZERO
          item.param04 = localInput.textInput.value
          item.param05 =
            mo00018TypeColor.value.modelValue === true
              ? Or28396Const.DEFAULT.STR.ONE
              : Or28396Const.DEFAULT.STR.ZERO
        }
      })
    }

    // 日付印刷区分が2の場合
    if (Or28396Const.DEFAULT.STR.TWO === mo00039Type.value) {
      // 指定日を活性表示にする。
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする。
      mo00020Flag.value = false
    }
    //別紙に印刷する
    if (
      defPrtTitle.value === t('label.information-table-other') ||
      defPrtTitle.value === t('label.Situation-item-list')
    ) {
      localOneway.mo00018OneWayAppendixPrint.disabled = true
    } else {
      localOneway.mo00018OneWayAppendixPrint.disabled = false
    }
  },
  { deep: true }
)

/**
 * 「日付印刷区分」ラジオボタン押下
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    if (Or28396Const.DEFAULT.STR.TWO === newValue) {
      // 指定日を活性表示にする
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする
      mo00020Flag.value = false
    }
  }
)

/**
 * 「利用者選択方法」ラジオボタン選択
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 利用者選択方法が「単一」の場合
    if (Or28396Const.DEFAULT.TANI === newValue) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 260px'
    } else {
      // 復元
      orX0117Oneway.type = Or28396Const.DEFAULT.STR.ONE
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'
    }

    // 利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.userId) {
      // TODO 基盤コンポーネントの再作成#147472 (このプロパティない) 利用者IDを対するレコードを選択状態にする
    }
    // 利用者一覧明細に親画面.利用者IDが存在しない場合
    else {
      // TODO 基盤コンポーネントの再作成#147472 (このプロパティない) 利用者一覧明細の1件目を選択状態にする
    }

    // 画面PDFダウンロードボタン活性非活性設定
    pdfDownloadBtnSetting(local.currentSectionNo)
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 履歴選択方法が「単一」の場合
    if (Or28396Const.DEFAULT.TANI === newValue) {
      orX0133OnewayModel.singleFlg = OrX0133Const.DEFAULT.TANI
      orX0117Oneway.type = OrX0133Const.DEFAULT.HUKUSUU
    } else {
      orX0133OnewayModel.singleFlg = OrX0133Const.DEFAULT.HUKUSUU
      orX0117Oneway.type = Or28396Const.DEFAULT.STR.ZERO
    }
  }
)

/**
 * 記入用シート方式ラジオボタン監視
 */
watch(
  () => mo00018Type.value.modelValue,
  () => {
    // 画面PDFダウンロードボタン活性非活性設定
    pdfDownloadBtnSetting(local.currentSectionNo)

    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      if (newValue.userList.length > 0) {
        local.userSelect = false
        local.selectUserId = newValue.userList[0].userId

        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj +'  '+ item.name2Knj,
            } as UserEntity)
          }
        }

        // 利用者選択方法が「単一」の場合
        if (Or28396Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
          // アセスメント履歴情報を取得する
          await getPrintSettingsHistoryList()
        }
        // 利用者選択方法が「複数」の場合
        else if (Or28396Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // TODO 基盤コンポーネントの再作成#147472 利用者一覧明細に前回選択された利用者が選択状態になる
          orX0117Oneway.type = Or28396Const.DEFAULT.STR.ONE
        }
      } else {
        local.userList = []
      }
    } else {
      local.userList = []
    }
  }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.emitType,
  (newValue) => {
    if (newValue === 'closeBtnClick') {
      setState({ isOpen: false, param: {} as Or28396Param })
    }
    mo00024.value.emitType = 'blank'
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="or28396_row"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="or28396_table"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
            style="text-align: left"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="3"
          class="content_center"
        >
          <c-v-row
            no-gutter
            class="printerOption customCol or28396_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or28396_row"
          >
            <c-v-col
              cols="12"
              sm="auto"
              class="label_left"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="auto"
              class="label_right"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWay"></base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or28396_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              style="padding-left: 0px; padding-right: 0px"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              style="padding-left: 0px; padding-right: 8px"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption customCol or28396_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>

          <c-v-row
            no-gutter
            class="customCol or28396_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="d-flex"
              style="padding: 0 12px 0 0; align-items: center"
            >
              <!-- 敬称を変更するチェックボックス -->
              <base-mo00018
                v-model="localInput.mo00018TypeChangeTitle"
                :oneway-model-value="localOneway.mo00018OneWayChangeTitle"
              >
              </base-mo00018>
              <!-- 敬称左ラベル  -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayLeftTitle"></base-mo01338>
              <!-- 敬称テキストボックス -->
              <base-mo00045
                v-model="localInput.textInput"
                :oneway-model-value="localOneway.mo00045OnewayTextInput"
                :disabled="!localInput.mo00018TypeChangeTitle.modelValue"
                class=""
              />
              <!-- 敬称右ラベル  -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayRightTitle"
              ></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              style="padding: 0 12px 0 0"
            >
              <base-mo00018
                v-model="mo00018Type"
                :oneway-model-value="localOneway.mo00018OneWay"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="
              defPrtTitle === t('label.information-table-one') ||
              defPrtTitle === t('label.information-table-all')
            "
            no-gutter
            class="customCol or28396_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-top: 0px; padding-left: 0px; padding-bottom: 0px"
            >
              <base-mo00018
                v-model="mo00018TypeColor"
                :oneway-model-value="localOneway.mo00018OneWayColor"
                @click="printColorCheckBoxCliick"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-else
            no-gutter
            class="customCol or28396_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-top: 0px; padding-left: 0px; padding-bottom: 0px; padding-bottom: 0px"
            >
              <base-mo00018
                v-model="mo00018TypeColor"
                :oneway-model-value="localOneway.mo00018OneWayAppendixPrint"
                @click="printColorCheckBoxCliick"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              style="padding-top: 0px; padding-left: 0px; padding-bottom: 0px"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo00018OneWayAppendixPrintSupplementLabel"
              ></base-mo01338>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="7"
          class="content_center"
        >
          <div style="display: flex">
            <c-v-col
              cols="5"
              class="pa-0"
            >
              <c-v-row
                class="or28396_row"
                no-gutter
                style="align-items: center; padding: 8px 0 8px 8px"
              >
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                  style="background-color: transparent"
                >
                </base-mo01338>
              </c-v-row>
              <c-v-row
                class="or28396_row"
                no-gutter
                style="align-items: center"
              >
                <base-mo00039
                  v-model="mo00039OneWayUserSelectType"
                  :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                >
                </base-mo00039>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="!kijunbiFlag"
              cols="7"
              class="pa-0"
            >
              <c-v-row
                class="or28396_row"
                no-gutter
                style="align-items: center; padding: 8px 0 8px 8px"
              >
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                  style="background-color: transparent"
                >
                </base-mo01338>
              </c-v-row>
              <c-v-row
                class="or28396_row"
                no-gutter
                style="align-items: center"
              >
                <base-mo00039
                  v-model="mo00039OneWayHistorySelectType"
                  :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                >
                </base-mo00039>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="kijunbiFlag"
              cols="7"
              style="padding: 0px"
            >
              <c-v-row
                class="or28396_row"
                no-gutter
                style="align-items: center; padding: 8px 0 8px 8px"
              >
                <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayType"> </base-mo00615>
              </c-v-row>
              <c-v-row
                class="or28396_row"
                no-gutter
                style="align-items: center"
              >
                <base-mo00020
                  v-model="mo00020TypeKijunbi"
                  :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                />
              </c-v-row>
            </c-v-col>
          </div>
          <c-v-row
            class="or28396_row or01408_row"
            no-gutter
            style="align-items: center; margin-top: 8px !important"
          >
            <!-- 担当ケアマネプルダウン -->
            <g-custom-or-x-0145
              v-bind="orx0145"
              v-model="orX0145Type"
              :oneway-model-value="localOneway.orX0145Oneway"
              class="search-tack"
              style="display: flex"
            ></g-custom-or-x-0145>

            <c-v-col
              cols="12"
              sm="5"
              style="align-content: center"
            >
              <base-mo01338
                v-if="tantoLabel"
                :oneway-model-value="localOneway.mo01338OneWayCareManagerInChargeLabel"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or28396_row table-all"
            no-gutter
          >
            <c-v-col :cols="userCols">
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode"
                class="x-0130"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag"
              cols="6"
              class="table-right"
            >
              <div style="height: 520px">
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0133
                  v-if="orX0133OnewayModel.singleFlg"
                  v-bind="orX0133"
                  :oneway-model-value="orX0133OnewayModel"
                ></g-custom-or-x-0133>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="pdfDownload()"
        >
          <!--ツールチップ表示："PDFをダウンロードします"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.pdf-download')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>

  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- Or21813:有機体 -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- メッセージ 警告 -->
  <g-base-or-21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  />
</template>
<style>
.or28396_content {
  padding: 0px !important;
}

.or28396_gokeiClass {
  label {
    color: #ffffff;
  }
}
</style>
<style scoped lang="scss">
.v-row {
  margin: -8px;
}

.or28396_table {
  padding: 8px 8px 0 !important;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  tr {
    td {
      .mr-2 {
        margin-right: 0px !important;
      }
    }
  }
}

.or28396_row {
  margin: 0px !important;
}
.or01408_row {
  margin-left: 8px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.table-all {
  > div {
    padding: 8px !important;
  }
  :deep(.x-0130) {
    > div {
      padding-left: 8px;
    }
  }
  .table-right {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
:deep(.search-tack) {
  margin-left: 0px;
  .ma-0 {
    margin-right: 8px !important;
  }
  .v-input__control {
    width: 200px;
  }
}
:deep(.inputClass) input {
  text-align: center;
}
</style>
