import { Or27754Const } from './Or27754.constants'
import { useInitialize } from '~/composables/useComponentLogic'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or30980Const } from '~/components/custom-components/organisms/Or30980/Or30980.constants'
import { Or30980Logic } from '~/components/custom-components/organisms/Or30980/Or30980.logic'
import { OrX0001Const } from '~/components/custom-components/organisms/OrX0001/OrX0001.constants'
import { OrX0001Logic } from '~/components/custom-components/organisms/OrX0001/OrX0001.logic'
import { OrX0008Const } from '~/components/custom-components/organisms/OrX0008/OrX0008.constants'
import { OrX0008Logic } from '~/components/custom-components/organisms/OrX0008/OrX0008.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or10320Const } from '~/components/custom-components/organisms/Or10320/Or10320.constants'
import { Or10320Logic } from '~/components/custom-components/organisms/Or10320/Or10320.logic'
import { Or10929Const } from '~/components/custom-components/organisms/Or10929/Or10929.constants'
import { OrX0115Logic } from '~/components/custom-components/organisms/OrX0115/OrX0115.logic'
import { OrX0115Const } from '~/components/custom-components/organisms/OrX0115/OrX0115.constants'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or10269Const } from '~/components/custom-components/organisms/Or10269/Or10269.constants'
import { Or10269Logic } from '~/components/custom-components/organisms/Or10269/Or10269.logic'
import { OrX0134Const } from '~/components/custom-components/organisms/OrX0134/OrX0134.constants'
import { OrX0134Logic } from '~/components/custom-components/organisms/OrX0134/OrX0134.logic'
import { Or13844Const } from '~/components/custom-components/organisms/Or13844/Or13844.constants'
import { Or13844Logic } from '~/components/custom-components/organisms/Or13844/Or13844.logic'
import { Or13872Const } from '~/components/custom-components/organisms/Or13872/Or13872.constants'
import { Or13872Logic } from '~/components/custom-components/organisms/Or13872/Or13872.logic'
import { Or13850Const } from '~/components/custom-components/organisms/Or13850/Or13850.constants'
import { Or13850Logic } from '~/components/custom-components/organisms/Or13850/Or13850.logic'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
/**
 * Or27754:コンポーネント
 * GUI00653: ［課題検討］画面
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
export namespace Or27754Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or27754Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or00248Const.CP_ID(1) },
        { cpId: Or30980Const.CP_ID(1) },
        { cpId: OrX0001Const.CP_ID(1) },
        { cpId: OrX0008Const.CP_ID(1) },
        { cpId: Or11871Const.CP_ID },
        { cpId: Or21813Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: OrX0001Const.CP_ID(1) },
        { cpId: Or10320Const.CP_ID(1) },
        { cpId: Or10929Const.CP_ID(1) },
        { cpId: OrX0115Const.CP_ID(1) },
        { cpId: Or41179Const.CP_ID(1) },
        { cpId: Or10269Const.CP_ID(1) },
        { cpId: OrX0134Const.CP_ID(1) },
        { cpId: Or13844Const.CP_ID(1) },
        { cpId: Or13872Const.CP_ID(1) },
        { cpId: Or13850Const.CP_ID(1) },
        { cpId: Or26257Const.CP_ID(1) },
        { cpId: Or51775Const.CP_ID(1) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(1)].uniqueCpId)
    Or30980Logic.initialize(childCpIds[Or30980Const.CP_ID(1)].uniqueCpId)
    OrX0001Logic.initialize(childCpIds[OrX0001Const.CP_ID(1)].uniqueCpId)
    OrX0008Logic.initialize(childCpIds[OrX0008Const.CP_ID(1)].uniqueCpId)
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    OrX0001Logic.initialize(childCpIds[OrX0001Const.CP_ID(1)].uniqueCpId)
    Or10320Logic.initialize(childCpIds[Or10320Const.CP_ID(1)].uniqueCpId)
    OrX0115Logic.initialize(childCpIds[OrX0115Const.CP_ID(1)].uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(1)].uniqueCpId)
    Or10269Logic.initialize(childCpIds[Or10269Const.CP_ID(1)].uniqueCpId)
    OrX0134Logic.initialize(childCpIds[OrX0134Const.CP_ID(1)].uniqueCpId)
    Or13844Logic.initialize(childCpIds[Or13844Const.CP_ID(1)].uniqueCpId)
    Or13872Logic.initialize(childCpIds[Or13872Const.CP_ID(1)].uniqueCpId)
    Or13850Logic.initialize(childCpIds[Or13850Const.CP_ID(1)].uniqueCpId)
    Or26257Logic.initialize(childCpIds[Or26257Const.CP_ID(1)].uniqueCpId)
    Or51775Logic.initialize(childCpIds[Or51775Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
}
