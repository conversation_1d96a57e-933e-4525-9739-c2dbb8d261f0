<script setup lang="ts">
/**
 * Or01087:（_日課計画入力（詳細データ））_日課計画入力情報一覧
 * GUI01058_日課計画入力
 *
 * <AUTHOR> 呉李彪
 */
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27831Const } from '../Or27831/Or27831.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01280Type } from '~/types/business/components/Mo01280Type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import { Or10558Logic } from '~/components/custom-components/organisms/Or10558/Or10558.logic'
import type { Or10558Type } from '~/types/cmn/business/components/Or10558Type'
import { Or10558Const } from '~/components/custom-components/organisms/Or10558/Or10558.constants'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { useSetupChildProps ,useSystemCommonsStore} from '#imports'
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: string
  uniqueCpId: string
  index: number
}
/**************************************************
 * 変数定義
 **************************************************/
const props = defineProps<Props>()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const localOneWay = reactive({
  mo01280OneWay: {
    readonly: false,
    itemLabel: '',
  },
  mo00009OneWay: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  or51775Oneway: {
    title: t('label.svKnj'),
    screenId: '',
    bunruiId: '',
    t1Cd: '860',
    t2Cd: '1',
    t3Cd: '0',
    tableName: 'cpn_tuc_cnikka2',
    columnName: 'naiyo_knj',
    assessmentMethod: '',
    inputContents: '',
    userId: systemCommonsStore.getUserId ?? '',
    mode:''
  } as Or51775OnewayType,
})

const local = reactive({
  mo01280: {
    value: props.modelValue,
  } as Mo01280Type,
  or10558: {} as Or10558Type,
  or51775: {} as Or51775Type,
  isEdit: false,
})

const contentsTrim = computed(() => {
  //16文字オーバーすると、16文字目以降は[...]として省略表示する。
  const itemContent = local.mo01280.value
  return {
    value:
      itemContent.length > Or27831Const.DEFAULT.MAX_CONTENT_LENGTH
        ? itemContent.slice(0, Or27831Const.DEFAULT.MAX_CONTENT_LENGTH - 1) + '...'
        : itemContent,
  }
})

/**
 * 子コンポーネントのプロパティを保持する変数
 * - uniqueCpId: 子コンポーネントの一意のID
 */
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(props.index ?? 0) })
const or10558 = ref({ uniqueCpId: Or10558Const.CP_ID(props.index ?? 0) })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or10558Const.CP_ID(0)]: or10558.value,
})
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
function onChange() {
  emit('update:modelValue', local.mo01280.value)
}
/**
 *  ボタン押下時の処理(Or10558)
 *
 */
function onClickOr10558() {
  // Or28991のダイアログ開閉状態を更新する
  Or10558Logic.state.set({
    uniqueCpId: or10558.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * Or51775ダイアログを開く処理
 * - ボタン押下時に呼び出される
 * - 子コンポーネントのバインドデータを設定し、ダイアログを開く
 */
function Or51775OnClick() {
  localOneWay.or51775Oneway.inputContents = ''
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}
// ダイアログ表示フラグ
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr10558 = computed(() => {
  // Or10558のダイアログ開閉状態
  return Or10558Logic.state.get(or10558.value.uniqueCpId)?.isOpen ?? false
})

watch(
  () => local.or10558.commonServiceList,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      local.mo01280.value = newValue
        .map((item) => item.svKnj)
        .filter((city) => city)
        .join(', ')
    }
  },
  { deep: true }
)
/**
 * テキスト設定処理
 *
 * @param data - Or51775ConfirmType型のデータ
 */
const handleSetText = (data: Or51775ConfirmType) => {
  if (data.type === '1') {
    // typeが1の場合：新しい値を代入
    local.mo01280.value = data.value
  } else {
    // typeが1以外の場合：値を加算
    local.mo01280.value += data.value
  }
}
watch(
  () => local.mo01280.value,
  (newValue) => {
    emit('update:modelValue', newValue)
  },
  { deep: true }
)
</script>

<template>
  <div
    @mouseover="
      () => {
        local.isEdit = true
      }
    "
    @focusout.stop="
      () => {
        local.isEdit = false
      }
    ">
    <base-mo01280
      v-show="local.isEdit"
      v-model="local.mo01280"
      :oneway-model-value="localOneWay.mo01280OneWay"
      class="overflow-hidden"
      maxlength="4000"
      @change="onChange"
    />
    <base-mo01280
      v-show="!local.isEdit"
      v-model="contentsTrim"
      :oneway-model-value="localOneWay.mo01280OneWay"
      class="overflow-hidden"
      maxlength="4000"
    />
  </div>
  <div class="d-flex w-35">
    <div class="devider">
      <!-- 内容-入力支援アイコン -->
      <base-mo00009
        :oneway-model-value="localOneWay.mo00009OneWay"
        @click="Or51775OnClick()"
      ></base-mo00009>
      <!-- GUI01063_共通サービス入力支援画面 -->
      <base-mo00009
        :oneway-model-value="localOneWay.mo00009OneWay"
        @click="onClickOr10558()"
      ></base-mo00009>
    </div>
    <!-- Or51775ダイアログ -->
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="local.or51775"
      :oneway-model-value="localOneWay.or51775Oneway"
      :unique-cp-id="or51775.uniqueCpId"
      @confirm="handleSetText"
    />
    <!--GUI01063_共通サービス入力支援画面-->
    <g-custom-or-10558
      v-if="showDialogOr10558"
      v-bind="or10558"
      v-model="local.or10558"
    />
  </div>
</template>

<style scoped lang="scss">
.flex-space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
