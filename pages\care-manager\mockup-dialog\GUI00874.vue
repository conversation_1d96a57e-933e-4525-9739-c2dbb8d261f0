<script setup lang="ts">
/**
 * GUI00874_［履歴選択］画面 アセスメント検討表(包括)
 *
 * @description
 * ［履歴選択］画面 アセスメント検討表(包括)
 *
 * <AUTHOR> PHAM HO HAI DANG
 */
import { computed, definePageMeta, ref, useScreenStore, useSetupChildProps } from '#imports'
import { Or27829Const } from '~/components/custom-components/organisms/Or27829/Or27829.constants'
import { Or27829Logic } from '~/components/custom-components/organisms/Or27829/Or27829.logic'
import type { Or27829OnewayType } from '~/types/cmn/business/components/Or27829Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * ケアの提供場所マスタ
 * KMD PHAM HO HAI DANG 2025/04/27 ADD START
 **************************************************/

/**
 *画面ID
 */
const screenId = 'GUI00874'

/**
 *ルーティング
 */
const routing = 'GUI00874/pinia'

/**
 *画面物理名
 */
const screenName = 'GUI00874'

/**
 *画面状態管理用操作変数
 */
const screenStore = useScreenStore()

/**
 *or27829
 */
const or27829 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/

/**
 *piniaの画面領域を初期化する
 */
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00874' },
})

/**
 *or27829OnewayModel
 */
const or27829OnewayModel: Or27829OnewayType = {
  officeId: '1',
  planPeriodId: '6',
  userId: '8',
  mode: '',
}

/**
 *mockMode
 */
const mockMode = {
  ...or27829OnewayModel,
  mode: '',
}

/**************************************************
 * Props
 **************************************************/
/**
 *piniaから最上位の画面コンポーネント情報を取得する
 *これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27829Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27829.value.uniqueCpId = pageComponent.uniqueCpId

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27829Const.CP_ID(0)]: or27829.value,
})

/**
 *ダイアログ表示フラグ
 *
 * @returns Or27829のダイアログが表示されている場合はtrue、そうでなければfalse
 */
const showDialogOr27829 = computed(() => {
  // Or27829のダイアログ開閉状態
  return Or27829Logic.state.get(or27829.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ボタン押下時の処理(Or27829)
 *
 */
function onClickOr27829() {
  Or27829Logic.state.set({
    uniqueCpId: or27829.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const or27829Data = ref({
  kd1Id: '1',
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- KMD PHAM HO HAI DANG 2025/04/27 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27829()"
        >GUI00874_［履歴選択］画面 アセスメント検討表(包括)
      </v-btn>
      <g-custom-or-27829
        v-if="showDialogOr27829"
        v-bind="or27829"
        v-model="or27829Data"
        :oneway-model-value="mockMode"
      >
      </g-custom-or-27829>
    </c-v-col>
  </c-v-row>
  <!-- KMD PHAM HO HAI DANG 2025/04/27 ADD END-->
</template>
