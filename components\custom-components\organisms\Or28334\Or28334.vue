<script setup lang="ts">
/**
 * Or28334:有機体:特別指示期間モーダル
 * GUI01145_特別指示期間
 *
 * @description
 * ［特別指示期間］画面では、定期巡回・随時対応型訪問介護看護サービスにおける医師からの特別指示の期間を登録します。
 * ［特別指示期間］画面は、［ケアマネ］→［利用・提供票］→［利用票］画面などで［マスタ他］→［特別指示期間］をクリックすると表示されます。
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10648Logic } from '../Or10648/Or10648.logic'
import { Or10648Const } from '../Or10648/Or10648.constants'
import { Or28334Const } from './Or28334.constants'
import type { DataTableData, Or28334StateType } from './Or28334.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type {
  SpecialInstructionsPeriodTableData,
  Or28334Type,
  Or28334OnewayType,
} from '~/types/cmn/business/components/Or28334Type'
import type {
  SpecialInstructionsPeriodSelectInEntity,
  SpecialInstructionsPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/SpecialInstructionsPeriodSelectEntity'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
} from '~/composables/useComponentVue'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useScreenStore, useScreenUtils } from '#imports'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
// import { hasRegistAuth } from '~/utils/useCmnAuthz'
import type { Mo01332Type } from '~/types/business/components/Mo01332Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type {
  SpecialInstructionsPeriodList,
  SpecialInstructionsPeriodUpdateInEntity,
} from '~/repositories/cmn/entities/SpecialInstructionsPeriodUpdateEntity'
import type { Or10648InterType } from '~/types/cmn/business/components/Or10648Type'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or28334Type
  onewayModelValue: Or28334OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28334StateType>({
  cpId: Or28334Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or28334Const.DEFAULT.IS_OPEN
    },
  },
})
const { refValue } = useScreenTwoWayBind<DataTableData>({
  cpId: Or28334Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = {
  specialInstructionsPeriodList: [],
}

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
const { setChildCpBinds } = useScreenUtils()

// ■共通処理の登録権限チェックを行う
// TODO QA#156559 No.5
// const hasView = await hasRegistAuth('/care-manager/mockup-sample/picture-book-buttons')
const hasView = true
// 画面タイトル
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28334Const.DEFAULT.IS_OPEN,
})

const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(0) })
const or10648 = ref({ uniqueCpId: Or10648Const.CP_ID(0) })

// ダイアログ表示フラグ
const showDialog21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialog21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr10648 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or10648Logic.state.get(or10648.value.uniqueCpId)?.isOpen ?? false
})
const defaultOnewayModelValue: Or28334OnewayType = {
  // 支援事業所id
  shienId: '',
  // 利用者id
  userId: '',
  // 提供年月
  yymmYm: '',
}

const localOneway = reactive({
  or28334: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 行追加コンポーネント
  mo00611AddRowBtnOneWay: {
    btnLabel: t('btn.add'),
    prependIcon: 'add',
    tooltipText: t('tooltip.add'),
  } as Mo00611OnewayType,
  // 行削除コンポーネント
  mo01265Oneway: {
    btnLabel: t('btn.delete'),
    prependIcon: 'delete',
    tooltipText: t('tooltip.delete-data'),
    disabled: true,
  } as Mo01265OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 保存コンポーネント
  mo00609SaveOneway: {
    btnLabel: t('btn.save'),
    tooltipText: t('tooltip.save'),
  } as Mo00609OnewayType,
  // 特別指示期間ダイアログ
  mo00024Oneway: {
    width: '1340px',
    maxWidth: '1340px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or28334',
      toolbarTitle: t('label.special-instructions-period'),
      toolbarName: 'Or28334ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // チェックボックス
  mo01332Oneway: {
    items: [
      {
        label: Or28334Const.DEFAULT.EMPTY,
        value: Or28334Const.DEFAULT.DAY_CHECK,
      },
    ],
    showItemLabel: false,
    disabled: false,
  },
  // 提供事業所入力支援アイコンボタン
  mo00009Oneway: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // GUI01177_事業所検索
  or10648OnewayType: {
    svJigyo: '',
    svtype: '',
  } as Or10648InterType,
})

// テーブルヘッダ
const headers = [
  {
    title: t('label.providing-facility'),
    width: '32px',
    align: 'left',
  },
  {
    title: t('label.special-instructions-period'),
    width: 'auto',
    align: 'left',
    children: [
      { title: t('label.day1'), key: 'day1', width: '32px', sortable: false },
      { title: t('label.day2'), key: 'day2', width: '32px', sortable: false },
      { title: t('label.day3'), key: 'day3', width: '32px', sortable: false },
      { title: t('label.day4'), key: 'day4', width: '32px', sortable: false },
      { title: t('label.day5'), key: 'day5', width: '32px', sortable: false },
      { title: t('label.day6'), key: 'day6', width: '32px', sortable: false },
      { title: t('label.day7'), key: 'day7', width: '32px', sortable: false },
      { title: t('label.day8'), key: 'day8', width: '32px', sortable: false },
      { title: t('label.day9'), key: 'day9', width: '32px', sortable: false },
      { title: t('label.day10'), key: 'day10', width: '32px', sortable: false },
      { title: t('label.day11'), key: 'day11', width: '32px', sortable: false },
      { title: t('label.day12'), key: 'day12', width: '32px', sortable: false },
      { title: t('label.day13'), key: 'day13', width: '32px', sortable: false },
      { title: t('label.day14'), key: 'day14', width: '32px', sortable: false },
      { title: t('label.day15'), key: 'day15', width: '32px', sortable: false },
      { title: t('label.day16'), key: 'day16', width: '32px', sortable: false },
      { title: t('label.day17'), key: 'day17', width: '32px', sortable: false },
      { title: t('label.day18'), key: 'day18', width: '32px', sortable: false },
      { title: t('label.day19'), key: 'day19', width: '32px', sortable: false },
      { title: t('label.day20'), key: 'day20', width: '32px', sortable: false },
      { title: t('label.day21'), key: 'day21', width: '32px', sortable: false },
      { title: t('label.day22'), key: 'day22', width: '32px', sortable: false },
      { title: t('label.day23'), key: 'day23', width: '32px', sortable: false },
      { title: t('label.day24'), key: 'day24', width: '32px', sortable: false },
      { title: t('label.day25'), key: 'day25', width: '32px', sortable: false },
      { title: t('label.day26'), key: 'day26', width: '32px', sortable: false },
      { title: t('label.day27'), key: 'day27', width: '32px', sortable: false },
      { title: t('label.day28'), key: 'day28', width: '32px', sortable: false },
      { title: t('label.day29'), key: 'day29', width: '32px', sortable: false },
      { title: t('label.day30'), key: 'day30', width: '32px', sortable: false },
      { title: t('label.day31'), key: 'day31', width: '32px', sortable: false },
      { title: t('label.day-total'), key: 'total', minWidth: '70px', sortable: false },
    ],
  },
]

// 特別指示期間初期情報
const tableDataFilter = computed(() => {
  const specialInstructionsPeriodList = refValue.value?.specialInstructionsPeriodList ?? []
  return specialInstructionsPeriodList.filter(
    (i: SpecialInstructionsPeriodTableData) => i.updateKbn !== UPDATE_KBN.DELETE
  )
})

// 提供年月の日数
const days = computed(() => {
  const year = localOneway.or28334.yymmYm.slice(0, 4)
  const month = localOneway.or28334.yymmYm.slice(-2)
  return new Date(Number(year), Number(month), 0).getDate()
})

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return isEditNavControl([props.uniqueCpId])
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
})

// 削除ボタン
let isDel = false
// 閉じるボタン
let isClose = false
// 保存ボタン
let isSave = false
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()
})

/**
 * AC001_初期表示
 *
 */
async function init() {
  // 特別指示期間初期情報取得(IN)
  const inputData: SpecialInstructionsPeriodSelectInEntity = {
    shienId: localOneway.or28334.shienId,
    userId: localOneway.or28334.userId,
    yymmYm: localOneway.or28334.yymmYm,
  }
  // 特別指示期間初期情報取得
  const ret: SpecialInstructionsPeriodSelectOutEntity = await ScreenRepository.select(
    'specialInstructionsPeriodSelect',
    inputData
  )
  // 戻り値はテーブルデータとして処理されます
  if (
    ret.data.specialInstructionsPeriodList !== null &&
    ret.data.specialInstructionsPeriodList !== undefined &&
    ret.data.specialInstructionsPeriodList.length > 0
  ) {
    let specialInstructionsPeriodList = [] as SpecialInstructionsPeriodTableData[]
    specialInstructionsPeriodList = []
    ret.data.specialInstructionsPeriodList.forEach((specialInstructionsPeriod, index) => {
      specialInstructionsPeriodList.push({
        // 提供事業所
        jigyoKnj: { value: specialInstructionsPeriod.jigyoKnj, unit: Or28334Const.DEFAULT.EMPTY },
        // サービス提供日1
        day1: { values: [specialInstructionsPeriod.day1] },
        // サービス提供日2
        day2: { values: [specialInstructionsPeriod.day2] },
        // サービス提供日3
        day3: { values: [specialInstructionsPeriod.day3] },
        // サービス提供日4
        day4: { values: [specialInstructionsPeriod.day4] },
        // サービス提供日5
        day5: { values: [specialInstructionsPeriod.day5] },
        // サービス提供日6
        day6: { values: [specialInstructionsPeriod.day6] },
        // サービス提供日7
        day7: { values: [specialInstructionsPeriod.day7] },
        // サービス提供日8
        day8: { values: [specialInstructionsPeriod.day8] },
        // サービス提供日9
        day9: { values: [specialInstructionsPeriod.day9] },
        // サービス提供日10
        day10: { values: [specialInstructionsPeriod.day10] },
        // サービス提供日11
        day11: { values: [specialInstructionsPeriod.day11] },
        // サービス提供日12
        day12: { values: [specialInstructionsPeriod.day12] },
        // サービス提供日13
        day13: { values: [specialInstructionsPeriod.day13] },
        // サービス提供日14
        day14: { values: [specialInstructionsPeriod.day14] },
        // サービス提供日15
        day15: { values: [specialInstructionsPeriod.day15] },
        // サービス提供日16
        day16: { values: [specialInstructionsPeriod.day16] },
        // サービス提供日17
        day17: { values: [specialInstructionsPeriod.day17] },
        // サービス提供日18
        day18: { values: [specialInstructionsPeriod.day18] },
        // サービス提供日19
        day19: { values: [specialInstructionsPeriod.day19] },
        // サービス提供日20
        day20: { values: [specialInstructionsPeriod.day20] },
        // サービス提供日21
        day21: { values: [specialInstructionsPeriod.day21] },
        // サービス提供日22
        day22: { values: [specialInstructionsPeriod.day22] },
        // サービス提供日23
        day23: { values: [specialInstructionsPeriod.day23] },
        // サービス提供日24
        day24: { values: [specialInstructionsPeriod.day24] },
        // サービス提供日25
        day25: { values: [specialInstructionsPeriod.day25] },
        // サービス提供日26
        day26: { values: [specialInstructionsPeriod.day26] },
        // サービス提供日27
        day27: { values: [specialInstructionsPeriod.day27] },
        // サービス提供日28
        day28: { values: [specialInstructionsPeriod.day28] },
        // サービス提供日29
        day29: { values: [specialInstructionsPeriod.day29] },
        // サービス提供日30
        day30: { values: [specialInstructionsPeriod.day30] },
        // サービス提供日31
        day31: { values: [specialInstructionsPeriod.day31] },
        // 合計
        total: { value: Number(specialInstructionsPeriod.total), unit: Or28334Const.DEFAULT.EMPTY },
        // 支援事業所id
        shienId: specialInstructionsPeriod.shienId,
        // 利用者id
        userid: specialInstructionsPeriod.userid,
        // 提供年月
        yymmYm: specialInstructionsPeriod.yymmYm,
        // 月途中保険者変更年月日
        henkouYmd: specialInstructionsPeriod.henkouYmd,
        // 開始日
        stDay: specialInstructionsPeriod.stDay,
        // 終了日
        edDay: specialInstructionsPeriod.edDay,
        // 提供事業所id
        svJigyoId: specialInstructionsPeriod.svJigyoId,
        // 元提供事業所id
        orgSvJigyoId: specialInstructionsPeriod.svJigyoId,
        // 更新回数
        modifiedCnt: specialInstructionsPeriod.modifiedCnt,
        // 更新区分
        updateKbn: Or28334Const.DEFAULT.EMPTY,
        // テーブルインデックス
        tableIndex: index,
      })
    })
    setChildCpBinds(props.parentUniqueCpId, {
      Or28334: {
        twoWayValue: {
          specialInstructionsPeriodList: specialInstructionsPeriodList,
        },
      },
    })
    onSelectRow(0)
  }
}

/**
 * AC002_「×ボタン」押下
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      // AC007_「閉じるボタン」押下
      close()
    }
  }
)

/**
 * AC003_「追加ボタン」押下
 *
 */
function addRowProc() {
  // 特別指示期間表の最終行に新規行を追加する。
  refValue.value!.specialInstructionsPeriodList.push({
    // 提供事業所
    jigyoKnj: { value: Or28334Const.DEFAULT.EMPTY, unit: Or28334Const.DEFAULT.EMPTY },
    // サービス提供日1
    day1: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日2
    day2: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日3
    day3: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日4
    day4: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日5
    day5: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日6
    day6: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日7
    day7: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日8
    day8: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日9
    day9: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日10
    day10: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日11
    day11: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日12
    day12: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日13
    day13: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日14
    day14: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日15
    day15: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日16
    day16: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日17
    day17: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日18
    day18: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日19
    day19: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日20
    day20: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日21
    day21: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日22
    day22: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日23
    day23: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日24
    day24: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日25
    day25: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日26
    day26: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日27
    day27: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日28
    day28: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日29
    day29: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日30
    day30: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // サービス提供日31
    day31: { values: [Or28334Const.DEFAULT.DAY_NOT_CHECK] },
    // 合計
    total: { value: 0, unit: Or28334Const.DEFAULT.EMPTY },
    // 支援事業所id=親画面.支援事業所id
    shienId: localOneway.or28334.shienId,
    // 利用者id=親画面.利用者id
    userid: localOneway.or28334.userId,
    // 提供年月=親画面.提供年月
    yymmYm: localOneway.or28334.yymmYm,
    // 月途中保険者変更年月日=0バイト文字列
    henkouYmd: Or28334Const.DEFAULT.EMPTY,
    // 開始日
    stDay: Or28334Const.DEFAULT.EMPTY,
    // 終了日
    edDay: Or28334Const.DEFAULT.EMPTY,
    // 提供事業所id
    svJigyoId: Or28334Const.DEFAULT.EMPTY,
    // 元提供事業所id
    orgSvJigyoId: Or28334Const.DEFAULT.EMPTY,
    // 更新回数
    modifiedCnt: Or28334Const.DEFAULT.EMPTY,
    // 更新区分="C":新規
    updateKbn: UPDATE_KBN.CREATE,
    // テーブルインデックス
    tableIndex: refValue.value!.specialInstructionsPeriodList.length,
  })
  // ※その他項目は初期値をセット
}

/**
 * AC004_「削除ボタン」押下
 *
 */
function onDeleteRow() {
  // 下記の確認ダイアログを表示する。
  isDel = true
  showOr21814MsgTwoBtn(t('message.i-cmn-10219'))
}

/**
 * 行削除
 *
 */
function onDeleteProc() {
  const tableIndex: number =
    refValue.value!.specialInstructionsPeriodList[selectedItemIndex.value].tableIndex
  refValue.value?.specialInstructionsPeriodList.forEach((item) => {
    if (item.tableIndex === tableIndex) {
      // 選択された行が新規行の場合
      if (item.updateKbn === UPDATE_KBN.CREATE) {
        refValue.value!.specialInstructionsPeriodList.splice(selectedItemIndex.value, 1)
      } else {
        // 選択された行が既存行の場合
        item.updateKbn = UPDATE_KBN.DELETE
      }
      selectedItemIndex.value = -1
      // 行削除非活性
      localOneway.mo01265Oneway.disabled = true
    }
  })
}

/**
 * AC005_「提供事業所入力支援アイコンボタン」押下
 *
 * @param index - index
 */
// function officeSelHelpOpen(index: number) {
//   selectedItemIndex.value = index
//   // サービス事業者: 当該行の提供事業所id
//   localOneway.or10648OnewayType.svJigyo =
//     refValue.value!.specialInstructionsPeriodList[selectedItemIndex.value].shienId
//   // サービス種類: "08"
//   localOneway.or10648OnewayType.svtype = Or28334Const.DEFAULT.SV_TYPE_08
//   // GUI01177_事業所検索をポップアップで起動する。
//   Or10648Logic.state.set({
//     uniqueCpId: or10648.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }

// /**
//  * AC005_「提供事業所入力支援アイコンボタン」押下
//  *
//  * @param index - index
//  */
// function officeSelHelpOpen(index: number) {
//   selectedItemIndex.value = index
//   // サービス事業者: 当該行の提供事業所id
//   localOneway.or10648OnewayType.svJigyo =
//     refValue.value!.specialInstructionsPeriodList[selectedItemIndex.value].shienId
//   // サービス種類: "08"
//   localOneway.or10648OnewayType.svtype = Or28334Const.DEFAULT.SV_TYPE_08
//   // GUI01177_事業所検索をポップアップで起動する。
//   Or10648Logic.state.set({
//     uniqueCpId: or10648.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }

// /**
//  * AC005_「提供事業所入力支援アイコンボタン」押下
//  *
//  * @param index - index
//  */
// function officeSelHelpOpen(index: number) {
//   selectedItemIndex.value = index
//   // サービス事業者: 当該行の提供事業所id
//   localOneway.or10648OnewayType.svJigyo =
//     refValue.value!.specialInstructionsPeriodList[selectedItemIndex.value].shienId
//   // サービス種類: "08"
//   localOneway.or10648OnewayType.svtype = Or28334Const.DEFAULT.SV_TYPE_08
//   // GUI01177_事業所検索をポップアップで起動する。
//   Or10648Logic.state.set({
//     uniqueCpId: or10648.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }

interface type {
  svJigyoId: number
  jigyoKnj: string
}
const svJigyoIdChange = (val: type) => {
  let isSameSvjId = false
  for (const item of refValue.value!.specialInstructionsPeriodList) {
    if (item.tableIndex === selectedItemIndex.value) {
      if (String(val.svJigyoId) === item.svJigyoId) {
        isSameSvjId = true
      }
      // 当該行の更新区分="U":更新※更新区分が空白の場合のみ、セット
      if (item.updateKbn === Or28334Const.DEFAULT.EMPTY) {
        item.updateKbn = UPDATE_KBN.UPDATE
      }
      // 当該行の提供事業所id=返却情報.サービス事業者id
      item.svJigyoId = String(val.svJigyoId)
      // 当該行の提供事業所=返却情報.事業所名
      item.jigyoKnj.value = val.jigyoKnj
    }
  }
  // ■警告メッセージを表示する。
  if (isSameSvjId) {
    // ・メッセージ内容:「選択した事業所は既に設定されています。」
    showOr21815MsgOneBtn(t('message.w-cmn-20112'))
  }
}
/**
 * AC006_「サービス提供日1～31」チェック状態変更
 *
 */
function onServiceDayChange() {
  if (selectedItemIndex.value !== -1) {
    refValue.value?.specialInstructionsPeriodList.forEach((item) => {
      if (item.tableIndex === selectedItemIndex.value) {
        // 当該行の更新区分="U":更新※更新区分が空白の場合のみ、セット
        if (item.updateKbn === Or28334Const.DEFAULT.EMPTY) {
          item.updateKbn = UPDATE_KBN.UPDATE
        }
        // 当該行の合計=当該行の「サービス提供日1～31」チェック状態がONの件数
        let count = 0
        for (let i = 1; i <= 31; i++) {
          const fieldName = `day${i}` as keyof typeof item
          const dayArray = item[fieldName] as Mo01332Type
          if (dayArray.values.includes(Or28334Const.DEFAULT.DAY_CHECK)) {
            count++
          }
        }
        item.total.value = count
      }
    })
  }
}

/**
 * AC007_「閉じるボタン」押下
 *
 */
function close() {
  // 該当画面にデータが変更なし（未保存変更がない）の場合
  if (!isEdit.value) {
    // 本画面を閉じる。
    setState({ isOpen: false })
  } else {
    // 上記以外の場合
    // ・保存権限がない場合、
    if (!hasView) {
      // メッセージを表示する。
      isClose = true
      showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
    } else {
      // 保存権限がある場合
      // メッセージを表示する。
      isClose = true
      showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
    }
  }
}

/**
 * AC010_「保存ボタン」押下
 *
 */
async function saveBtnClick() {
  // 該当画面にデータが変更なし（未保存変更がない）の場合
  if (!isEdit.value) {
    // メッセージ「i.cmn.21800」
    showOr21814MsgOneBtn(t('message.i-cmn-21800'))
  }
  let hasZeroTotal = false
  for (const item of refValue.value!.specialInstructionsPeriodList) {
    // 特別指示期間表の中に合計が0の行が存在する場合
    if (
      item.updateKbn !== UPDATE_KBN.DELETE &&
      item.updateKbn !== UPDATE_KBN.NONE &&
      item.total.value === 0
    ) {
      hasZeroTotal = true
      break
    }
  }
  if (isEdit.value && hasZeroTotal) {
    // メッセージ「w.cmn.20787」
    showOr21815MsgOneBtn(t('message.w-cmn-20787'))
  }
  if (isEdit.value && !hasZeroTotal) {
    // 特別指示期間の情報を保存する。
    await specialInstructionsPeriodListSave()
    // AC001の初期表示処理を行う。
    await init()
  }
}

/**
 * 特別指示期間の情報を保存する。
 *
 */
async function specialInstructionsPeriodListSave() {
  // 特別指示期間リスト
  const specialInstructionsPeriodUpdList = [] as SpecialInstructionsPeriodList[]
  for (const item of refValue.value!.specialInstructionsPeriodList) {
    specialInstructionsPeriodUpdList.push({
      // 支援事業所id
      shienId: item.shienId,
      // 利用者id
      userid: item.userid,
      // 提供年月
      yymmYm: item.yymmYm,
      // 月途中保険者変更年月日
      henkouYmd: item.henkouYmd,
      // サービス提供日1
      day1:
        item.day1.values.length > 1
          ? item.day1.values[1]
          : (item.day1.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日2
      day2:
        item.day2.values.length > 1
          ? item.day2.values[1]
          : (item.day2.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日3
      day3:
        item.day3.values.length > 1
          ? item.day3.values[1]
          : (item.day3.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日4
      day4:
        item.day4.values.length > 1
          ? item.day4.values[1]
          : (item.day4.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日5
      day5:
        item.day5.values.length > 1
          ? item.day5.values[1]
          : (item.day5.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日6
      day6:
        item.day6.values.length > 1
          ? item.day6.values[1]
          : (item.day6.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日7
      day7:
        item.day7.values.length > 1
          ? item.day7.values[1]
          : (item.day7.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日8
      day8:
        item.day8.values.length > 1
          ? item.day8.values[1]
          : (item.day8.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日9
      day9:
        item.day9.values.length > 1
          ? item.day9.values[1]
          : (item.day9.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日10
      day10:
        item.day10.values.length > 1
          ? item.day10.values[1]
          : (item.day10.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日11
      day11:
        item.day11.values.length > 1
          ? item.day11.values[1]
          : (item.day11.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日12
      day12:
        item.day12.values.length > 1
          ? item.day12.values[1]
          : (item.day12.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日13
      day13:
        item.day13.values.length > 1
          ? item.day13.values[1]
          : (item.day13.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日14
      day14:
        item.day14.values.length > 1
          ? item.day14.values[1]
          : (item.day14.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日15
      day15:
        item.day15.values.length > 1
          ? item.day15.values[1]
          : (item.day15.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日16
      day16:
        item.day16.values.length > 1
          ? item.day16.values[1]
          : (item.day16.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日17
      day17:
        item.day17.values.length > 1
          ? item.day17.values[1]
          : (item.day17.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日18
      day18:
        item.day18.values.length > 1
          ? item.day18.values[1]
          : (item.day18.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日19
      day19:
        item.day19.values.length > 1
          ? item.day19.values[1]
          : (item.day19.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日20
      day20:
        item.day20.values.length > 1
          ? item.day20.values[1]
          : (item.day20.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日21
      day21:
        item.day21.values.length > 1
          ? item.day21.values[1]
          : (item.day21.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日22
      day22:
        item.day22.values.length > 1
          ? item.day22.values[1]
          : (item.day22.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日23
      day23:
        item.day23.values.length > 1
          ? item.day23.values[1]
          : (item.day23.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日24
      day24:
        item.day24.values.length > 1
          ? item.day24.values[1]
          : (item.day24.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日25
      day25:
        item.day25.values.length > 1
          ? item.day25.values[1]
          : (item.day25.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日26
      day26:
        item.day26.values.length > 1
          ? item.day26.values[1]
          : (item.day26.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日27
      day27:
        item.day27.values.length > 1
          ? item.day27.values[1]
          : (item.day27.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日28
      day28:
        item.day28.values.length > 1
          ? item.day28.values[1]
          : (item.day28.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日29
      day29:
        item.day29.values.length > 1
          ? item.day29.values[1]
          : (item.day29.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日30
      day30:
        item.day30.values.length > 1
          ? item.day30.values[1]
          : (item.day30.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // サービス提供日31
      day31:
        item.day31.values.length > 1
          ? item.day31.values[1]
          : (item.day31.values[0] ?? Or28334Const.DEFAULT.DAY_NOT_CHECK),
      // 合計負担額
      total: String(item.total.value),
      // 開始日
      stDay: item.stDay,
      // 終了日
      edDay: item.edDay,
      // 提供事業所id
      svJigyoId: item.svJigyoId,
      // 更新回数
      modifiedCnt: item.modifiedCnt,
      // 更新区分
      updateKbn: item.updateKbn,
      // 元提供事業所id
      orgSvJigyoId: item.orgSvJigyoId,
    })
  }
  const param: SpecialInstructionsPeriodUpdateInEntity = {
    // 特別指示期間リスト
    specialInstructionsPeriodList: specialInstructionsPeriodUpdList,
  }
  await ScreenRepository.update('specialInstructionsPeriodUpdate', param)
}

/**
 * 行選択
 *
 * @param tableIndex - テーブルインデックス
 */
function onSelectRow(tableIndex: number) {
  selectedItemIndex.value = tableIndex
  // 行削除活性
  localOneway.mo01265Oneway.disabled = false
}

/**
 * 確認ダイアログの開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgOneBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.ok'),
      // 第2ボタンタイプ
      secondBtnType: 'blank',
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 確認ダイアログの開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      // 第1ボタンタイプ
      firstBtnType: 'destroy1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンタイプ
      secondBtnType: 'normal3',
      // 第2ボタンラベル
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 確認ダイアログの開閉
 *
 * @param infomsg - Message
 */
function showOr21814MsgThreeBtn(infomsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: infomsg,
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンタイプ
      secondBtnType: 'destroy1',
      // 第2ボタンラベル
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'normal3',
      // 第3ボタンラベル
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (isClose) {
      isClose = false
      // 保存権限がない場合、
      if (!hasView) {
        if (newValue.firstBtnClickFlg) {
          // はい：画面を閉じる。
          setState({ isOpen: false })
        } else {
          // いいえ：メッセージダイアログを閉じる。
          return
        }
      } else {
        // 保存権限がある場合
        if (newValue.firstBtnClickFlg) {
          // はい：AC010の保存処理を実行し、画面を閉じる。
          await specialInstructionsPeriodListSave()
        } else if (newValue.secondBtnClickFlg) {
          // AC010の保存処理を実行せず、画面を閉じる。
          setState({ isOpen: false })
        } else {
          // キャンセル：メッセージダイアログを閉じる。
          return
        }
      }
    } else if (isDel) {
      isDel = false
      if (selectedItemIndex.value !== -1 && selectedItemIndex.value !== null) {
        if (newValue.firstBtnClickFlg) {
          // はい：以降の処理を行う。
          onDeleteProc()
        } else {
          // いいえ：以降の処理を行わない。
          return
        }
      }
    } else if (isSave) {
      isSave = false
      if (newValue.firstBtnClickFlg) {
        // API呼び出させずに、そのまま処理を終了する。
        return
      } else {
        return
      }
    }
  }
)

/**
 * 警告ダイアログの開閉
 *
 * @param errormsg - Message
 */
function showOr21815MsgOneBtn(errormsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
  // 確認ダイアログをオープン
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
function isEditNavControl(components: string[]) {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()
  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }
  return false
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <!-- 追加ボタン -->
      <base-mo00611
        :oneway-model-value="localOneway.mo00611AddRowBtnOneWay"
        class="mr-2 mb-2"
        @click="addRowProc()"
      />
      <!-- 行削除ボタン or21738 -->
      <base-mo01265
        class="mb-2"
        :oneway-model-value="localOneway.mo01265Oneway"
        @click="onDeleteRow()"
      />
      <c-v-row>
        <c-v-col>
          <c-v-data-table
            :headers="headers"
            class="table-wrapper"
            hide-default-footer
            :items="tableDataFilter"
            hover
            style="width: 1330px"
            height="643px"
            :items-per-page="-1"
          >
            <template #headers>
              <tr>
                <!-- 提供事業所 -->
                <th
                  rowspan="2"
                  class="text-left"
                  style="min-width: 80px"
                >
                  {{ t('label.providing-facility') }}
                </th>

                <!-- 特別指示期間 -->
                <th
                  colspan="32"
                  class="pl-4 pr-2 text-left"
                >
                  {{ t('label.special-instructions-period') }}
                </th>
              </tr>

              <tr>
                <th
                  v-for="day in 31"
                  :key="`day-${day}`"
                  class="pl-2 pr-2 text-center"
                  :style="{
                    'min-width': '35px',
                    'max-width': '35px',
                  }"
                >
                  {{ day }}
                </th>

                <!-- 合計 -->
                <th
                  class="pl-2 pr-2 text-center"
                  style="min-width: 65px"
                >
                  {{ t('label.total') }}
                </th>
              </tr>
            </template>
            <template #item="{ item }">
              <tr
                :class="{ 'select-row': selectedItemIndex === item.tableIndex }"
                @click="onSelectRow(item.tableIndex)"
              >
                <!-- 提供事業所 -->
                <td>
                  <div class="disp-flex">
                    <base-mo01337 :oneway-model-value="item.jigyoKnj"> </base-mo01337>
                    <!-- <c-v-divider
                      vertical
                      class="mt-1 mb-1"
                    />
                    <base-mo00009  , index
                      :oneway-model-value="localOneway.mo00009Oneway"
                      variant="flat"
                      density="compact"
                      @click="officeSelHelpOpen(index)"
                    ></base-mo00009> -->
                  </div>
                </td>
                <!-- サービス提供日1 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day1"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日2 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day2"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日3 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day3"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日4 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day4"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日5 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day5"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日6 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day6"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日7 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day7"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日8 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day8"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日9 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day9"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日10 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day10"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日11 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day11"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日12 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day12"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日13 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day13"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日14 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day14"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日15 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day15"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日16 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day16"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日17 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day17"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日18 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day18"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日19 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day19"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日20 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day20"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日21 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day21"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日22 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day22"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日23 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day23"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日24 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day24"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日25 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day25"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日26 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day26"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日27 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day27"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日28 -->
                <td class="checkbox-align">
                  <base-mo01332
                    v-model="item.day28"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <!-- サービス提供日29 -->
                <td
                  v-if="Number(days) >= 29"
                  class="checkbox-align"
                >
                  <base-mo01332
                    v-model="item.day29"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <td
                  v-if="Number(days) < 29"
                  style="background-color: rgb(var(--v-theme-black-100)) !important"
                >
                  <base-mo01337 :oneway-model-value="Or28334Const.DEFAULT.EMPTY"> </base-mo01337>
                </td>
                <!-- サービス提供日30 -->
                <td
                  v-if="Number(days) >= 30"
                  class="checkbox-align"
                >
                  <base-mo01332
                    v-model="item.day30"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <td
                  v-if="Number(days) < 30"
                  class="checkbox-align"
                  style="background-color: rgb(var(--v-theme-black-100)) !important"
                >
                  <base-mo01337 :oneway-model-value="Or28334Const.DEFAULT.EMPTY"> </base-mo01337>
                </td>
                <!-- サービス提供日31 -->
                <td
                  v-if="Number(days) >= 31"
                  class="checkbox-align"
                >
                  <base-mo01332
                    v-model="item.day31"
                    style="width: 15px !important"
                    :oneway-model-value="localOneway.mo01332Oneway"
                    @update:model-value="onServiceDayChange()"
                  />
                </td>
                <td
                  v-if="Number(days) < 31"
                  class="checkbox-align"
                  style="background-color: rgb(var(--v-theme-black-100)) !important"
                >
                  <base-mo01337
                    style="width: 15px; padding: 0; margin: 0"
                    :oneway-model-value="Or28334Const.DEFAULT.EMPTY"
                  >
                  </base-mo01337>
                </td>
                <!-- 合計 -->
                <td style="text-align: center">
                  {{ item.total.value }}
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close()"
        ></base-mo00611>
        <!-- 保存ボタン Mo00611 -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609SaveOneway"
          class="mx-2"
          :disabled="!hasView"
          @click="saveBtnClick()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialog21814"
    v-bind="or21814"
  />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or21815
    v-if="showDialog21815"
    v-bind="or21815"
  />
  <!-- Or10648:有機体:［事業所検索］画面 -->
  <g-custom-or-10648
    v-if="showDialogOr10648"
    v-bind="or10648"
    :oneway-model-value="localOneway.or10648OnewayType"
    @update:model-value="svJigyoIdChange"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
.disp-flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.checkbox-align {
  width: 32px !important;
  min-width: 32px !important;
  max-width: 32px !important;
  padding: 0 !important;
}
:deep(.checkbox-align div:first-child) {
  display: flex !important;
  height: 62px;
  padding: 0;
  margin: 0;
  width: 34px !important;
  justify-content: center !important;
  align-items: center !important;
}
:deep(.disp-flex div:first-child) {
  // width: 80px !important;
  padding-left: 0 !important;
}
</style>
