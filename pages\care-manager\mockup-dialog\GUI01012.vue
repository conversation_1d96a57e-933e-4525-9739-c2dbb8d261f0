<script setup lang="ts">
/**
 * GUI01012_計画書（2）マスタ
 *
 * @description
 * 計画書（2）マスタ
 *
 * <AUTHOR> DAO VAN DUONG
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import type { Or26855OnewayType } from '~/types/cmn/business/components/Or26855Type'
import { Or26855Const } from '~/components/custom-components/organisms/Or26855/Or26855.constants'
import { Or26855Logic } from '~/components/custom-components/organisms/Or26855/Or26855.logic'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 * KMD DAO VAN DUONG 2025/03/21 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI01012'
// ルーティング
const routing = 'GUI01012/pinia'
// 画面物理名
const screenName = 'GUI01012'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or26855 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01012' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or26855Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or26855.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI01012',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26855Const.CP_ID(0) }],
})
Or26855Logic.initialize(init.childCpIds.Or26855.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or26855Const.CP_ID(0)]: or26855.value,
})
/**
 * Or26855  のダイアログ開閉状態
 *
 * <AUTHOR> DAO VAN DUONG
 */
const showDialogOr26855 = computed(() => {
  return Or26855Logic.state.get(or26855.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ボタン押下時の処理(Or26855)
 *
 * <AUTHOR> DAO VAN DUONG
 */

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const Or26855Data = ref<Or26855OnewayType>({
  shisetuId: '1',
  svJigyoId: '1',
  bunrui1Id: '2',
  bunrui2Id: '4',
  carePlanStyle: '1',
  kirokuRenkeiFlg: '',
})

const buttonList = [
  {
    manageFlag: '1',
    label:
      'GUI01012_計画書（2）マスタ「ケアプラン方式が包括的自立支援プログラム以外、計画書様式が施設の場合」',
  },
  {
    manageFlag: '2',
    label:
      'GUI01012_計画書（2）マスタ「ケアプラン方式が包括的自立支援プログラム以外、計画書様式が居宅の場合」',
  },
  {
    manageFlag: '3',
    label:
      'GUI01012_計画書（2）マスタ「ケアプラン方式が包括的自立支援プログラム、計画書様式が施設の場合」',
  },
  {
    manageFlag: '4',
    label:
      'GUI01012_計画書（2）マスタ「ケアプラン方式が包括的自立支援プログラム、計画書様式が居宅の場合」',
  },
  {
    manageFlag: '5',
    label:
      'GUI01012_計画書（2）マスタ「初期表示１～４のいずれ且つ、記録との連携＝1(記録との連携を行う)の場合)」',
  },
]

function onClickOr26855(manageflag: string) {
  if (manageflag === '5') {
    Or26855Data.value.kirokuRenkeiFlg = '1'
  } else {
    Or26855Data.value.carePlanStyle = manageflag
  }

  Or26855Logic.state.set({
    uniqueCpId: or26855.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters>
    <c-v-col> ケアマネモックアップ開発ダイヤログ画面確認用ページ </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
      >
        ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ KMD DAO VAN DUONG 2025/02/28 ADD START-->
  <c-v-row no-gutters>
    <c-v-col
      v-for="(button, index) in buttonList"
      :key="index"
      cols="12"
    >
      <v-btn
        variant="plain"
        @click="onClickOr26855(button.manageFlag)"
      >
        {{ button.label }}
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-26855
    v-if="showDialogOr26855"
    v-bind="or26855"
    :oneway-model-value="Or26855Data"
  />
  <!-- POP画面ポップアップ KMD DAO VAN DUONG 2025/02/28 ADD END-->
</template>
