<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue'
import { Or06961Const } from '../Or06961/Or06961.constants'
import { Or06961Logic } from '../Or06961/Or06961.logic'
import { Or06954Const } from '../Or06954/Or06954.constants'
import { Or06954Logic } from '../Or06954/Or06954.logic'
import { Or28428Logic } from '../Or28428/Or28428.logic'
import { OrX0127Const } from './OrX0127.constants'
import type { Or06961OnewayType } from '~/types/cmn/business/components/Or06961Type'
import type { OrX0127OnewayType } from '~/types/cmn/business/components/OrX0127Type'
import { useSetupChildProps, useScreenOneWayBind, useScreenUtils } from '#imports'
import type { Or28428Type } from '~/types/cmn/business/components/Or28428Type'
import type { Or06954OnewayType } from '~/types/cmn/business/components/Or06954Type'
/**
 * OrX0127Logic:有機体:支援計画コンテンツエリアタブ
 * GUI01095_支援計画（予防計画書）
 *
 * @description
 * 支援計画コンテンツエリアタブ
 *
 * <AUTHOR>
 */
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
// ローカルOneway
const localOneway = reactive({
  // 本画面ダイアログOneway
  Or06961Oneway: {
    // 計画表様式
    itakuKkakPrtFlg: '',
    // 操作区分
    operaFlg: '',
    // ページング区分
    pagingFlg: '',
    // 全列表示の有無
    isDetail: false,
    // 詳細ボタンの表示有無
    isShowDetailBtn: true,
  } as Or06961OnewayType,
  Or06954Oneway: {
    // 計画表様式
    itakuKkakPrtFlg: '',
    // 期間の管理(計画表)
    itakuKkakKikanFlg: '',
    // 期間のカレンダー取込(計画表)
    itakuKkakYmdFlg: '',
    // 保険サービス（事業所名）(計画表)
    itakuKkakHsJigyoFlg: '',
    // 頻度取込(計画表)
    itakuKkakHindoFlg: '',
    // 保険サービス取込(計画表)
    itakuKkakHsKasanFlg: '',
    // 機能区分
    kycFlg: '',
    // 操作区分
    operaFlg: '',
    // ページング区分
    pagingFlg: '',
  } as Or06954OnewayType,
  OrX0127Oneway: {
    isDetail: false,
  },
})

/**************************************************
 * Pinia
 **************************************************/
const { setChildCpBinds } = useScreenUtils()

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['changeData', 'focus', 'click'])

const or06961 = ref({ uniqueCpId: Or06961Const.CP_ID(1) })
const or06954 = ref({ uniqueCpId: Or06954Const.CP_ID(1) })
const or06954Ref = ref<{
  isValid: () => Promise<boolean>
}>()

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or06961Const.CP_ID(1)]: or06961.value,
  [Or06954Const.CP_ID(1)]: or06954.value,
})

useScreenOneWayBind<OrX0127OnewayType>({
  cpId: OrX0127Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    // 操作区分
    operaFlg: (value) => {
      localOneway.Or06961Oneway.operaFlg = value!
      localOneway.Or06954Oneway.operaFlg = value!
    },
    // ページング区分
    pagingFlg: (value) => {
      localOneway.Or06961Oneway.pagingFlg = value!
      localOneway.Or06954Oneway.pagingFlg = value!
    },
    // 計画表様式
    itakuKkakPrtFlg: (value) => {
      localOneway.Or06961Oneway.itakuKkakPrtFlg = value!
      localOneway.Or06954Oneway.itakuKkakPrtFlg = value!
    },
    // 期間の管理(計画表)
    itakuKkakKikanFlg: (value) => {
      localOneway.Or06954Oneway.itakuKkakKikanFlg = value!
    },
    // 期間のカレンダー取込(計画表)
    itakuKkakYmdFlg: (value) => {
      localOneway.Or06954Oneway.itakuKkakYmdFlg = value!
    },
    // 保険サービス（事業所名）(計画表)
    itakuKkakHsJigyoFlg: (value) => {
      localOneway.Or06954Oneway.itakuKkakHsJigyoFlg = value!
    },
    // 機能区分
    kycFlg: (value) => {
      localOneway.Or06954Oneway.kycFlg = value!
    },
    // 頻度取込(計画表)
    itakuKkakHindoFlg: (value) => {
      localOneway.Or06954Oneway.itakuKkakHindoFlg = value!
    },
    // 保険サービス取込(計画表)
    itakuKkakHsKasanFlg: (value) => {
      localOneway.Or06954Oneway.itakuKkakHsKasanFlg = value!
    },
    isCopyMode: (value) => {
      if (value) {
        localOneway.Or06961Oneway.isCopyMode = value
        localOneway.Or06954Oneway.isCopyMode = value
      }
    },
  },
})

onMounted(() => {
  const data = Or28428Logic.data.get(props.parentUniqueCpId)
  if (data) {
    setChildCpBinds(props.uniqueCpId, {
      [Or06954Const.CP_ID(1)]: {
        twoWayValue: {
          // 表示用「総合的課題と目標」リスト
          tab3DataList: data.tab3DataObj?.tab3DataList,
          // 削除用「総合的課題と目標」リスト
          deleteTab3DataList: data.tab3DataObj?.deleteTab3DataList,
          // 表示用「支援計画」リスト
          tab4DataList: data.tab4DataObj?.tab4DataList,
          // 削除用「支援計画」リスト
          deleteTab4DataList: data.tab4DataObj?.deleteTab4DataList,
          // 選択した行のindex
          selectRowIndex: data.tab4DataObj?.selectRowIndexRight,
          // tab3選択した行のindex
          tab3SelectRowIndex: data.tab3DataObj?.selectRowIndexRight,
        },
      },
      [Or06961Const.CP_ID(1)]: {
        twoWayValue: {
          // 表示用「総合的課題と目標」リスト
          tab3DataList: data.tab3DataObj?.tab3DataList,
          // 削除用「総合的課題と目標」リスト
          deleteTab3DataList: data.tab3DataObj?.deleteTab3DataList,
          // 選択した行のindex
          selectRowIndex: data.tab3DataObj?.selectRowIndexRight,
          // 表示用「支援計画」リスト
          tab4DataList: data.tab4DataObj?.tab4DataList,
          // 削除用「支援計画」リスト
          deleteTab4DataList: data.tab4DataObj?.deleteTab4DataList,
        },
      },
    })
  }
})

/**
 * OrX0119のフラグの監視
 */
watch(
  () => Or28428Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    setChildCpBinds(props.uniqueCpId, {
      [Or06954Const.CP_ID(1)]: {
        twoWayValue: {
          // 表示用「総合的課題と目標」リスト
          tab3DataList: newValue!.tab3DataObj?.tab3DataList,
          // 削除用「総合的課題と目標」リスト
          deleteTab3DataList: newValue!.tab3DataObj?.deleteTab3DataList,
          // 表示用「支援計画」リスト
          tab4DataList: newValue!.tab4DataObj?.tab4DataList,
          // 削除用「支援計画」リスト
          deleteTab4DataList: newValue!.tab4DataObj?.deleteTab4DataList,
          // 選択した行のindex
          selectRowIndex: newValue!.tab4DataObj?.selectRowIndexRight,
          // tab3選択した行のindex
          tab3SelectRowIndex: newValue!.tab3DataObj?.selectRowIndexRight,
        },
      },
      [Or06961Const.CP_ID(1)]: {
        twoWayValue: {
          // 表示用「総合的課題と目標」リスト
          tab3DataList: newValue!.tab3DataObj?.tab3DataList,
          // 削除用「総合的課題と目標」リスト
          deleteTab3DataList: newValue!.tab3DataObj?.deleteTab3DataList,
          // 選択した行のindex
          selectRowIndex: newValue!.tab3DataObj?.selectRowIndexRight,
          // 表示用「支援計画」リスト
          tab4DataList: newValue!.tab4DataObj?.tab4DataList,
          // 削除用「支援計画」リスト
          deleteTab4DataList: newValue!.tab4DataObj?.deleteTab4DataList,
        },
      },
    })
  }
)

/**
 * Or06954のフラグの監視
 */
watch(
  () => Or06954Logic.data.get(or06954.value.uniqueCpId),
  (newValue) => {
    const or28428Data = Or28428Logic.data.get(props.parentUniqueCpId)
    const newModelValue: Or28428Type = {
      // 表示用「目標とする生活」情報
      tab1DataObj: or28428Data!.tab1DataObj,
      // アセスメント領域と課題情報
      tab2DataObj: or28428Data!.tab2DataObj,
      // 総合的課題と目標情報
      tab3DataObj: {
        // 表示用「総合的課題と目標」リスト
        tab3DataList: or28428Data!.tab3DataObj!.tab3DataList,
        // 削除用「総合的課題と目標」リスト
        deleteTab3DataList: or28428Data!.tab3DataObj!.deleteTab3DataList,
        // 総合的課題と目標tab選択した行のindex
        selectRowIndexRight: newValue!.tab3SelectRowIndex,
        // 左側選択した行のindex
        selectRowIndexLeft: or28428Data!.tab3DataObj!.selectRowIndexLeft,
      },
      // 支援計画情報
      tab4DataObj: {
        // 表示用「支援計画」リスト
        tab4DataList: newValue!.tab4DataList,
        // 削除用「支援計画」リスト
        deleteTab4DataList: newValue!.deleteTab4DataList,
        // 選択した行のindex
        selectRowIndexRight: newValue!.selectRowIndex,
        // 総合的課題と目標tab選択した行のindex
        selectRowIndexLeft: newValue!.tab3SelectRowIndex,
      },
    }
    emit('changeData', newModelValue)
  }
)

/**
 * Or06961のフラグの監視
 */
watch(
  () => Or06961Logic.data.get(or06961.value.uniqueCpId),
  (newValue) => {
    const or28428Data = Or28428Logic.data.get(props.parentUniqueCpId)
    if (or28428Data && newValue) {
      const newModelValue: Or28428Type = {
        ...or28428Data,
        tab3DataObj: {
          // 表示用「総合的課題と目標」リスト
          tab3DataList: newValue.tab3DataList,
          // 削除用「総合的課題と目標」リスト
          deleteTab3DataList: newValue.deleteTab3DataList,
          // 選択した行のindex
          selectRowIndexRight: newValue.selectRowIndex,
          // 左側選択した行のindex
          selectRowIndexLeft: or28428Data.tab3DataObj!.selectRowIndexLeft,
        },
        tab4DataObj: {
          // 表示用「支援計画」リスト
          tab4DataList: or28428Data.tab4DataObj!.tab4DataList,
          // 削除用「支援計画」リスト
          deleteTab4DataList: or28428Data.tab4DataObj!.deleteTab4DataList,
          // 右側選択した行のindex
          selectRowIndexRight: or28428Data.tab4DataObj!.selectRowIndexRight,
          // 選択した行のindex
          selectRowIndexLeft: newValue.selectRowIndex,
        },
      }
      emit('changeData', newModelValue)
    }
  }
)

/**
 * 選択されている項目のフラグの監視
 */
watch(
  () => Or06961Logic.event.get(or06961.value.uniqueCpId)!.selectCell,
  (newValue) => {
    emit('focus', newValue)
  }
)

/**
 * 選択されている項目のフラグの監視
 */
watch(
  () => Or06954Logic.event.get(or06954.value.uniqueCpId)!.selectCell,
  (newValue) => {
    emit('focus', newValue)
  }
)

/**
 * 保険サービス取込の監視
 */
watch(
  () => Or06954Logic.event.get(or06954.value.uniqueCpId)!.isTab4InsuranceClick,
  (newValue) => {
    emit('click', newValue)
  }
)

/**
 * 詳細データを表示するかどうかの監視
 *
 * @param result - 詳細データを表示するかどうか
 */
const updateIsDetail = (result: boolean) => {
  localOneway.OrX0127Oneway.isDetail = result
}

async function isValid() {
  const or06954Valid = await or06954Ref.value?.isValid()

  return or06954Valid ?? true
}

defineExpose({
  isValid,
})
</script>
<template>
  <c-v-row no-gutters>
    <c-v-col
      cols="5"
      class="left-col"
    >
      <g-custom-or06961
        v-bind="or06961"
        :oneway-model-value="localOneway.Or06961Oneway"
        @update-is-detail="updateIsDetail"
      >
      </g-custom-or06961>
    </c-v-col>
    <c-v-col
      cols="7"
      class="right-col"
    >
      <g-custom-or06954
        ref="or06954Ref"
        v-bind="or06954"
        :oneway-model-value="localOneway.Or06954Oneway"
      >
      </g-custom-or06954>
    </c-v-col>
  </c-v-row>
</template>
<style scoped lang="scss">
.left-col {
  padding: 24px !important;
  border-right: 1px solid rgb(var(--v-theme-black-200));
}
</style>
