<script setup lang="ts">
/**
 * GUI04519_転送履歴画面
 *
 * @description
 * GUI04519_転送履歴画面
 *
 * <AUTHOR> 劉子良
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import type { OrX0203Type, OrX0203OnewayType } from '~/types/cmn/business/components/OrX0203Type'
import { OrX0203Const } from '~/components/custom-components/organisms/OrX0203/OrX0203.constants'
import { OrX0203Logic } from '~/components/custom-components/organisms/OrX0203/OrX0203.logic'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

// 画面ID
const screenId = 'GUI04519'
// ルーティング
const routing = 'GUI04519/pinia'
// 画面物理名
const screenName = 'GUI04519'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const orX0203 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI04519' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  OrX0203Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
orX0203.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI04519',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: OrX0203Const.CP_ID(0) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [OrX0203Const.CP_ID(0)]: orX0203.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOrX0203 = computed(() => {
  // OrX0203のダイアログ開閉状態
  return OrX0203Logic.state.get(orX0203.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const orX0203Data = {
  /**
   * 処理年月
   */
  yymmYmd: '',
  /**
   *  事業所ID
   */
  svJigyoId: '1',
  /**
   *  転送履歴表示モード
   */
  tensouMode: '',
  /**
   *  事業所ID配列
   */
  svJigyoIdList: ['1','2'] as string[],
  /**
   *  転送区分
   */
  tensouKbn: '',
  /**
   *  行数
   */
  rowsNum: '',
  /**
   *  事業所ID事業名(略称)リスト
   */
  jigyoRyakuKnjList: ['事業名1','事業名2'] as string[],
}

const orX0203Type = ref<OrX0203Type>()

/**
 *  ボタン押下時の処理
 *
 * @param tensouMode - 転送履歴表示モード
 */
function orX0203OnClick(tensouMode:string) {
  orX0203Data.tensouMode = tensouMode;
  // OrX0203のダイアログ開閉状態を更新する
  OrX0203Logic.state.set({
    uniqueCpId: orX0203.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="orX0203OnClick('0')"
        >転送履歴表示モードが0：転送先情報表示の場合
      </v-btn>
      <g-custom-orX0203
        v-if="showDialogOrX0203"
        v-bind="orX0203"
        :oneway-model-value="orX0203Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="orX0203OnClick('1')"
        >転送履歴表示モードが1：転送先情報表示の場合
      </v-btn>
      <g-custom-orX0203
        v-if="showDialogOrX0203"
        v-bind="orX0203"
        :oneway-model-value="orX0203Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="orX0203OnClick('2')"
        >転送履歴表示モードが2：転送先情報表示の場合
      </v-btn>
      <g-custom-orX0203
        v-if="showDialogOrX0203"
        v-bind="orX0203"
        :oneway-model-value="orX0203Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="orX0203OnClick('3')"
        >転送履歴表示モードが3：転送先情報表示の場合
      </v-btn>
      <g-custom-orX0203
        v-if="showDialogOrX0203"
        v-bind="orX0203"
        :oneway-model-value="orX0203Data"
      />
    </c-v-col>
  </c-v-row>
</template>
