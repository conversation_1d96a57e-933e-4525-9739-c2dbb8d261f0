/**
 * Or60400:有機体:週間計画一括取込
 * GUI00614_週間計画一括取込
 *
 * @description
 * GUI00614_週間計画一括取込
 *
 * <AUTHOR>
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/** 週間計画一括取込入力*/
export interface weekPlanBundleImportInfoSelectInEntity extends InWebEntity {
  /** key */
  [key: string]: unknown
  /** 事業者ID */
  svJigyoId: string
  /** 処理年月 */
  selWeek: string
  /** 対象年月 */
  objYm: string
  /** 担当者ID */
  tantoId: string
  /** 処理年月フラグ */
  selWeekFlag: string
  /** 取込先年月フラグ */
  shoriYmFlag: string
  /** 週間計画の指定 */
  ibSelWeekYm: string
  /** 取込先年月1 */
  shoriYm1: string
  /** 取込先年月2 */
  shoriYm2: string
  /** 取込先年月3 */
  shoriYm3: string
  /** 取込先年月4 */
  shoriYm4: string
  /** 取込先年月5 */
  shoriYm5: string
  /** 取込先年月6 */
  shoriYm6: string
  /** 取込先年月7 */
  shoriYm7: string
  /** 取込先年月8 */
  shoriYm8: string
  /** 取込先年月9 */
  shoriYm9: string
  /** 取込先年月10 */
  shoriYm10: string
  /** 取込先年月11 */
  shoriYm11: string
  /** 取込先年月12 */
  shoriYm12: string
  /** 取込先年月13 */
  shoriYm13: string
  /** 取込先年月14 */
  shoriYm14: string
  /** 取込先年月15 */
  shoriYm15: string
  /** 取込先年月16 */
  shoriYm16: string
  /** 取込先年月17 */
  shoriYm17: string
  /** 取込先年月18 */
  shoriYm18: string
  /** 取込先年月19 */
  shoriYm19: string
  /** 取込先年月20 */
  shoriYm20: string
  /** 取込先年月21 */
  shoriYm21: string
  /** 取込先年月22 */
  shoriYm22: string
  /** 取込先年月23 */
  shoriYm23: string
  /** 取込先年月24 */
  shoriYm24: string
  /** 画面名称 */
  screenNm: string
  /** 職員ID */
  shokuinId: string
  /** システムコード */
  sysCd: string
  /** 利用者情報リスト */
  userList: UserListType[]
}

/** 週間計画一括取込出力 */
export interface weekPlanBundleImportInfoSelectOutEntity extends OutWebEntity {
  /** DataTableのデータ */
  data: {
    /** 利用者情報リスト */
    userList: UserListType[]
    /** 短期入所サービスの取込 */
    copyTnki: string
    /** 取込を許可する利用票の状況 */
    selSyori: string
    /** 福祉用具貸与 設定単位数 */
    fygTani: string
    /** 処理年月を優先する */
    syoriYm: string
  }
}

/** 週間計画一括取込保存入力エンティティ*/
export interface weekPlanBundleImportInfoUpdateInEntity extends InWebEntity {
  /** 職員ID  */
  shokuinId: string
  /** 画面名称 */
  screenNm: string
  /** システムコード */
  sysCd: string
  /** 短期入所取込方法パラメータ */
  copyTnkiParam: string
  /** 取込先利用票状況パラメータ */
  selSyoriParam: string
  /** 福祉用具単位数パラメータ */
  fygTaniParam: string
  /** 処理年月優先パラメータ  */
  syoriYmParam: string
}

/** 週間計画一括取込保存出力エンティティ*/
export interface weekPlanBundleImportInfoUpdateOutEntity extends OutWebEntity {
  /** data */
  data: {
    /** 短期入所取込方法パラメータ */
    copyTnkiParam: string
    /** 取込先利用票状況パラメータ */
    selSyoriParam: string
    /** 福祉用具単位数パラメータ */
    fygTaniParam: string
    /** 処理年月優先パラメータ  */
    syoriYmParam: string
  }
}
/** 利用者List*/
export interface UserListType {
  /** フリガナ（姓） */
  name1Kana: string
  /** フリガナ（名） */
  name2Kana: string
  /** 氏名（姓） */
  name1Knj: string
  /** 氏名（名） */
  name2Knj: string
  /** 性別 */
  sex: string
  /** 利用者ID */
  userId: string
  /** 電話番号 */
  tel: string
  /** 携帯番号 */
  keitaitel: string
  /** 要介護度 */
  yokaiKnj: string
  /** 認定有効開始日 */
  startYmd: string
  /** 認定終了日 */
  endYmd: string
  /** 利用票1 */
  plan1: string
  /** 利用票2 */
  plan2: string
  /** 利用票3 */
  plan3: string
  /** 利用票4 */
  plan4: string
  /** 利用票5 */
  plan5: string
  /** 利用票6 */
  plan6: string
  /** 利用票7 */
  plan7: string
  /** 利用票8 */
  plan8: string
  /** 利用票9 */
  plan9: string
  /** 利用票10 */
  plan10: string
  /** 利用票11 */
  plan11: string
  /** 利用票12 */
  plan12: string
  /** 利用票13 */
  plan13: string
  /** 利用票14 */
  plan14: string
  /** 利用票15 */
  plan15: string
  /** 利用票16 */
  plan16: string
  /** 利用票17 */
  plan17: string
  /** 利用票18 */
  plan18: string
  /** 利用票19 */
  plan19: string
  /** 利用票20 */
  plan20: string
  /** 利用票21 */
  plan21: string
  /** 利用票22 */
  plan22: string
  /** 利用票23 */
  plan23: string
  /** 利用票24 */
  plan24: string
  /** 週間計画 */
  tougaiYm: string
}
