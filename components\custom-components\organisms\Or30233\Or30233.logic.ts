import { Or30233Const } from './Or30233.constants'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or30233:有機体:特記、解決すべき課題など
 * ［アセスメント］画面（居宅）
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or30233Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId } = useInitialize({
      cpId: Or30233Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {
        careCertificationList: {
          sectionList: [
            {
              value: '',
              sectionTitle: '',
              sectionItems: {
                itemType: '',
                check: { modelValue: false },
                value: '',
                tooltip: '',
                label: '',
                itemName: '',
              },
            },
          ],
        },
      },
    })

    return {
      cpId,
      uniqueCpId: uniqCpId,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor(Or30233Const.CP_ID(0))
}
