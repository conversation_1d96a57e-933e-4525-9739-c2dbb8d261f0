import type { Mo01274Type } from '@/types/business/components/Mo01274Type'
/**
 * OrX0217：有機体：認定調査票特記事項コンテンツエリアタブ
 * GUI01272_認定調査票特記事項
 *
 * 単方向バインドのデータ構造
 */
export interface OrX0217OnewayType {
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * 調査票ID
   */
  cschId: string
  /**
   * 特記コード
   */
  specialNoteCd: string
  /**
   * 特記事項タイトル
   */
  tokkiTitle: string
  /**
   * 特記事項(固定)リスト
   */
  tokkiTextList: TokkiTextList[]
}

/**
 * 特記事項(固定)リスト
 */
export interface TokkiTextList {
  /**
   * 詳細リスト
   */
  tokkiDetailList: TokkiDetailList[]
}

/**
 * 詳細リスト
 */
export interface TokkiDetailList {
  /**
   * 特記事項(固定)ID
   */
  tokkiId: string
  /**
   * 特記事項(固定)
   */
  tokkiText: string
}

/**
 * 双方向バインドModelValue
 */
export interface OrX0217Type {
  /**
   * 特記事項内容リスト
   */
  nTokkiList: NTokkiList[]
}

/**
 * 特記事項内容リスト
 */
export interface NTokkiList {
  /**
   * カウンター
   */
  counter: string
  /**
   * 認定（特記：大）
   */
  n1tCd: string
  /**
   * 認定（特記：小）
   */
  n2tCd: Mo01274Type
  /**
   * 特記事項
   */
  memoKnj: Mo01274Type
  /**
   * 表示順
   */
  seqNo: string
  /**
   * 更新区分
   */
  updateKbn: string
}
