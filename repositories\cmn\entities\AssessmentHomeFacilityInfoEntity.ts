import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * ［アセスメント］画面（居宅）（3）
 *
 * @description
 * ［アセスメント］画面（居宅）（3）画面施設情報リスト取得API用エンティティ
 *
 * <AUTHOR>
 */
/**
 * ［アセスメント］画面（居宅）（3）施設情報リスト取得入力エンティティ
 */
export interface AssessmentHomeFacilityInfoSelectInEntity extends InWebEntity {
  /** 調査票施設種別 */
  shisetsuShu: string
}

/**
 * ［アセスメント］画面（居宅）（3）施設情報リスト取得出力エンティティ
 */
export interface AssessmentHomeFacilityInfoSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    shisetsuDataList: ShisetsuData[]
  }
}

/**
 * 施設情報
 */
export interface ShisetsuData {
  /** 施設ID */
  shisetuId: string
  /** 施設名 */
  shisetuKnj: string
  /** 調査票施設種別 */
  scShisetsuShu: string
  /** 電話番号 */
  tel: string
  /** 住所 */
  addressKnj: string
  /** 表示順 */
  sort: string
  /** 郵便番号 */
  zip: string
}
