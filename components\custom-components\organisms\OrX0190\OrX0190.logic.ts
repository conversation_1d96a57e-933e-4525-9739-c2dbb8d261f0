import { Or27349Const } from '../Or27349/Or27349.constants'
import { Or27349Logic } from '../Or27349/Or27349.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { OrX0190Const } from './OrX0190.constants'
import type { OrX0190StateType } from './OrX0190.Type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * OrX0190：有機体：利用者(患者)基本情報について
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 */
export namespace OrX0190Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: OrX0190Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [{ cpId: Or51775Const.CP_ID(1) }, { cpId: Or27349Const.CP_ID(1) }],
      initOneWayState: {},
      initTwoWayValue: {
        codeList: {},
      },
    })
   Or51775Logic.initialize(childCpIds[Or51775Const.CP_ID(1)].uniqueCpId)
   Or27349Logic.initialize(childCpIds[Or27349Const.CP_ID(1)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<OrX0190StateType>(OrX0190Const.CP_ID(0))
}
