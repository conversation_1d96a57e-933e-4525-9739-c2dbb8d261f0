/**
 * Or26323認定調査履歴情報
 * GUI01282_［認定調査履歴情報］画面
 *
 * <AUTHOR>
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 認定調査履歴情報エンティティ
 */
export interface certificationSurveyHistoryInfoSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * 履歴情報
     */
    rirekiInfo: rirekiInfo[]
  }
}

/**
 * 認定調査履歴情報入力エンティティ
 */
export interface certificationSurveyHistoryInfoSelectInEntity extends InWebEntity {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * 処理モード
   */
  mode: string
}

/**
 * 認定調査履歴情報リスト
 */
export interface rireki1 {
  /**
   * 調査票ID
   */
  cschId: string
  /**
   * 実施日
   */
  jisshiDateYmd: string
  /**
   * 記入者コード
   */
  chkShokuId: string
  /**
   * 記入者名
   */
  chkShokuNm: string
  /**
   * 改訂
   */
  dmyCho: string
  /**
   * 改訂フラグ
   */
  kaiteiFlg?: string
}

/**
 * 主治医意見書履歴情報リスト
 */
export interface rireki {
  /**
   * 医師意見書ID
   */
  rirekiId: string
  /**
   *  作成日
   */
  createYmd: string
  /**
   * 医師名前
   */
  ishiNameKnj: string
  /**
   * 改定フラグ
   */
  ikenshoFlg: string
}
/**
 * 履歴情報
 */
export interface rirekiInfo {
  /**
   * 認定調査履歴情報リスト
   */
  rireki1List: rireki1[]
  /**
   * 主治医意見書履歴情報リスト
   */
  rirekiList: rireki[]
}
