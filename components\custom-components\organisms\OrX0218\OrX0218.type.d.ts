/**
 * OrX0218：有機体：実施計画①～③表用選択メニュー付き日付入力
 *
 * <AUTHOR>
 */
export interface NodeStyling {
  /**
   * 計算されたコンテキストスタイル文字列
   * (CONTEXT_STYLEで定義されたプロパティのセミコロン区切りリスト)
   */
  contextStyle: string

  /**
   * 上下パディングの合計サイズ(px)
   * (padding-top + padding-bottom)
   */
  paddingSize: number

  /**
   * 上下ボーダーの合計幅(px)
   * (border-top-width + border-bottom-width)
   */
  borderSize: number

  /**
   * 要素のbox-sizingプロパティの値
   * (content-box または border-box)
   */
  boxSizing: string
}

/**
 * テキストエリアの高さ
 */
export interface TextareaHeight {
  /**
   * 計算されたテキストエリアの高さ(px単位の文字列)
   * (minRowsとmaxRowsの制約を考慮)
   */
  height?: string

  /**
   * 最小高さ(px単位の文字列)
   * (minRowsが指定された場合のみ存在)
   */
  minHeight?: string
}
