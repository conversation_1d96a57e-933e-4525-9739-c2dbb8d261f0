<script setup lang="ts">
/**
 * Or28646:有機体:週間表取込
 * GUI00998_週間表取込画面
 *
 * @description
 * 週間表取込
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, type Ref, watch, computed } from 'vue'
import { Or28646Const } from './Or28646.constants'
import type {
  DataInfoDetailsType,
  HistoryInfoDetailsType,
  Or28646StateType,
  PlanPeriodInfoType,
  WeekTableDetailInfosType,
} from './Or28646.type'
import type {
  Or28646OneWayType,
  WeekTableImportType,
  IkouIchiranItem,
  Or28646TowWayType,
  FilterDataType,
  Or28646Type,
} from '~/types/cmn/business/components/Or28646Type'
import {
  definePageMeta,
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useScreenUtils,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type {
  IHistoryInfoInEntity,
  IHistoryInfoOutEntity,
  IPlanPeriodInEntity,
  IPlanPeriodOutEntity,
  IWeekTableDetailInEntity,
  IWeekTableDetailOutEntity,
} from '~/repositories/cmn/entities/WeekTableImportEntity'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

definePageMeta({
  layout: 'business-platform-layout',
})

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: WeekTableImportType
  uniqueCpId: string
  parentCpId: string
}

//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const props = defineProps<Props>()
const defaultOnewayModelValue: Or28646OneWayType = {
  weekTableImportInfo: {
    // 施設ID
    shisetuId: '1',
    // 事業者ID
    svJigyoId: '1',
    // 利用者ID
    userId: '1',
    // 種別ID
    syubetsuId: '2',
    // 期間管理フラグ
    kikanFlg: '1',
  },
}

const localOneway = reactive({
  or28646: {
    ...props.onewayModelValue,
    ...defaultOnewayModelValue,
  },
  historyInfoParam: {
    // 施設ID
    shisetuId: '1',
    // 事業者ID
    svJigyoId: '1',
    // 利用者ID
    userId: '1',
    // 種別ID
    syubetsuId: '2',
    // 期間管理フラグ
    kikanFlg: '1',
    // 計画期間ID
    sc1Id: '',
  },
  weekTableInfoParam: {
    // 施設ID
    shisetuId: '1',
    // 事業者ID
    svJigyoId: '1',
    // 利用者ID
    userId: '1',
    // 種別ID
    syubetsuId: '2',
    // 期間管理フラグ
    kikanFlg: '1',
    // 計画表ID
    week1Id: '',
  },
  // 「週間表取込」ダイアログ
  mo00024Oneway: {
    width: '1440px',
    height: '596px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.week-table-import'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 確認ボタン
  mo00609Oneway: {
    btnLabel: t('btn.ok'),
  } as Mo00609OnewayType,
  // 適用チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: '',
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
  or28646Towway: {
    listData: [] as FilterDataType[],
  } as Or28646TowWayType,
  mo00040Oneway: {
    itemLabel: Or28646Const.CP_ID(0),
    showItemLabel: false,
    isRequired: false,
    items: [],
    width: '240px',
  } as Mo00040OnewayType,
  mo01282TermidOneway: [] as CodeType[],
  mo01282DataidOneway: [] as CodeType[],
})

const defaultModelValue = {
  // 計画期間
  planPeriodInfo: {
    items: [
      {
        sc1Id: '',
        startYmd: '',
        endYmd: '',
        rirekiCnt: '',
      },
    ],
  },
  // 履歴情報
  historyInfo: {
    items: [
      {
        sc1Id: '',
        week1Id: '',
        shokuKnj: '',
        caseNo: '',
        createYmd: '',
        kaiteiKnj: '',
        termId: '',
        termName: '',
        tougaiYm: '',
        kaiteiFlg: '',
      },
    ],
  },
  // 週間表取込
  weekTableDetailInfo: {
    items: [
      {
        week2Id: '',
        week1Id: '',
        userid: '',
        youbi: '',
        kaishiJikan: '',
        shuuryouJikan: '',
        naiyoCd: '',
        naiyoKnj: '',
        memoKnj: '',
        alignment: '',
        fontColor: '',
        backColor: '',
        timeKbn: '',
        igaiMoji: '',
        igaiKbn: '',
        igaiDate: '',
        igaiWeek: '',
        dataKnj: '',
      },
    ],
  },
  // 履歴情報BAK
  historyInfoBak: {
    items: [
      {
        week1Id: '',
        sc1Id: '',
        createYmd: '',
        shokuKnj: '',
        caseNo: '',
        tougaiYm: '',
        termId: '',
        termName: '',
        kaiteiKnj: '',
        kaiteiFlg: '',
      },
    ],
  },
  // 週間表取込情報BAK
  weekTableDetailInfoBak: {
    items: [
      {
        week2Id: '',
        week1Id: '',
        userid: '',
        youbi: '',
        kaishiJikan: '',
        shuuryouJikan: '',
        naiyoCd: '',
        naiyoKnj: '',
        memoKnj: '',
        alignment: '',
        fontColor: '',
        backColor: '',
        timeKbn: '',
        igaiMoji: '',
        igaiKbn: '',
        igaiDate: '',
        igaiWeek: '',
        dataKnj: '',
      },
    ],
  },
  or28646: {
    weekTableDetailList: [],
    weekMemoImportFlag: { modelValue: false } as Mo00018Type,
    filterSelectValue: { modelValue: '0' } as Mo00040Type,
  },
}
const local = reactive({
  or28646: {
    ...defaultModelValue.or28646,
  } as Or28646Type,
  planPeriodInfo: {
    ...defaultModelValue.planPeriodInfo,
  },
  historyInfo: {
    ...defaultModelValue.historyInfo,
  },
  weekTableDetailInfo: {
    ...defaultModelValue.weekTableDetailInfo,
  },
  historyInfoBak: {
    ...defaultModelValue.historyInfoBak,
  },
  weekTableDetailInfoBak: {
    ...defaultModelValue.weekTableDetailInfoBak,
  },
  // 本画面
  or28646State: {
    selectedTitleValue: '',
  } as Or28646StateType,
  // Filter選択肢
  FilterSelectList: [] as CodeType[],
  setInputComponentsFlg: false,
  mo00024: {
    isOpen: true,
  },
  localSc1Id: '',
  weekId: '',
})
// 計画期間データテーブルのヘッダー
const moDataTable1 = reactive({
  headers: [
    // 計画期間
    {
      title: t('label.plan-period'),
      key: 'planPeriod',
      width: '190px',
    },
    // 期間内履歴数
    {
      title: t('label.within-the-period-number-of-history'),
      key: 'rirekiCnt',
      width: '100px',
    },
  ],
})

// 履歴情報データテーブルのヘッダー
const moDataTable2 = reactive({
  headers: [
    // 作成日
    {
      title: t('label.create-date'), // ヘッダーに表示される名称
      value: 'select',
      key: 'createYmd',
    },
    // 作成者
    {
      title: t('label.author'), // ヘッダーに表示される名称
      value: '1',
      key: 'shokuKnj',
    },
    // ケース番号
    {
      title: t('label.caseNo'), // ヘッダーに表示される名称
      value: '1',
      key: 'caseNo',
    },
    // 処理年月
    {
      title: t('label.process-ym'), // ヘッダーに表示される名称
      value: '1',
      key: 'tougaiYm',
    },
    // 有効期間
    {
      title: t('label.validity-period'), // ヘッダーに表示される名称
      value: '1',
      key: 'termName',
    },
    // 改訂
    {
      title: t('label.revision'), // ヘッダーに表示される名称
      value: '1',
      key: 'kaiteiKnj',
    },
  ],
})

// 週間表取込情報データテーブルのヘッダー
const moDataTable3 = reactive({
  headers: [
    // 時間帯
    {
      title: t('label.weekly-plan-timezone'),
      key: 'jikan',
      sortable: false,
      width: '155px',
    },
    // 曜日
    {
      title: t('label.day-of-week'),
      key: 'youbi',
      sortable: false,
      width: '170px',
    },
    // 内容
    {
      title: t('label.content'),
      key: 'naiyoKnj',
      sortable: false,
      width: '200px',
    },
    // メモ
    {
      title: t('label.memo'),
      key: 'memoKnj',
      sortable: false,
      width: '200px',
    },
    // 頻度
    {
      title: t('label.frequency'),
      key: 'frequency',
      sortable: false,
      width: '200px',
    },
    // 取込先
    {
      title: t('label.torikomi-location'),
      key: 'dataKnj',
      sortable: false,
      width: '300px',
    },
  ],
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00611Oneway1 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
})

const mo00611Oneway2 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.case-confirm-setting'),
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or28646Const.DEFAULT.IS_OPEN,
})

// ナビゲーション制御領域のいずれかの編集フラグがON
useSystemCommonsStore().setShowEditDiscardDialog(useScreenStore().isEditNavControl())
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28646StateType>({
  cpId: Or28646Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28646Const.DEFAULT.IS_OPEN
    },
  },
})
const { refValue } = useScreenTwoWayBind<Or28646Type>({
  cpId: Or28646Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const or21815 = ref({ uniqueCpId: '' })

//警告ウィンドウを表示
const showDialog = computed(() => {
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * ウォッチャー
 **************************************************/
// 画面開閉フラグ
watch(
  () => local.mo00024.isOpen,
  () => {
    setState({ isOpen: local.mo00024.isOpen })
  }
)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})
// 汎用コードマスタデータを取得し初期化
void initCodes()
local.setInputComponentsFlg = true
/** 初期情報取得 */
async function getInitDataInfo() {
  // バックエンドAPIから初期情報取得
  const inParam: IPlanPeriodInEntity = localOneway.or28646.weekTableImportInfo
  const ret: IPlanPeriodOutEntity = await ScreenRepository.select(
    'weekImportInitialInfoSelect',
    inParam
  )
  const resData: DataInfoDetailsType = ret.data
  // // データ情報設定
  planPeriodInfoSet(resData)
}
/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 有効期間
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_VALID_PERIOD },
    // Filterの選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FILTER },
    // 取込先
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_IMPORT_DATA },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // Filter
  local.FilterSelectList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_FILTER)
  local.FilterSelectList.sort((a, b) => (a.value > b.value ? 1 : -1));
  if (local.FilterSelectList !== undefined && local.FilterSelectList.length > 0) {
    for (const item of local.FilterSelectList) {
      localOneway.or28646Towway.listData.push({
        value: item.value,
        title: item.label,
      })
    }
  }
  localOneway.mo00040Oneway.items = localOneway.or28646Towway.listData
  // 有効期間
  localOneway.mo01282TermidOneway = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_VALID_PERIOD
  )
  // 取込先
  localOneway.mo01282DataidOneway = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_IMPORT_DATA
  )
}

//画面を開く時の初期値
const initValue = {
  weekMemoImportFlag: (local.or28646.weekMemoImportFlag as { modelValue: boolean }).modelValue,
  filterSelectValue: local.or28646.filterSelectValue!.modelValue,
}
/**
 * 計画期間情報リスト設定
 *
 * @param dataInfo - 計画期間選択行ID
 */
const planPeriodInfoSet = (dataInfo: DataInfoDetailsType) => {
  let planPeriodInfoList: PlanPeriodInfoType[] = []

  let selectPeriodId = ''
  if (dataInfo) {
    // 計画期間情報取得
    planPeriodInfoList = dataInfo.planPeriodInfo

    if (planPeriodInfoList && planPeriodInfoList.length > 0) {
      selectPeriodId = planPeriodInfoList[0].sc1Id
    }

    selectedItem.value = selectPeriodId // 計画期間选中行
    for (const item of dataInfo.historyInfo) {
      const termNameSet = localOneway.mo01282TermidOneway.find(
        (term) => term.value === item.termId
      )?.label
      if (termNameSet !== undefined) {
        item.termName = termNameSet
      }
    }
    // 履歴情報BAK---設定
    local.historyInfoBak.items = dataInfo.historyInfo
    // 週間表情報
    for (const item of dataInfo.weekTableDetailInfo) {
      const dataKnjSet = localOneway.mo01282DataidOneway.find(
        (data) => data.label === item.dataKnj
      )?.label
      if (dataKnjSet !== undefined) {
        item.dataKnj = dataKnjSet
      }
    }
    // 週間表情報BAK---設定
    local.weekTableDetailInfoBak.items = dataInfo.weekTableDetailInfo

    // 履歴情報設定
    historyInfoSet(selectPeriodId)
  }

  // 計画期間情報設定
  local.planPeriodInfo.items = planPeriodInfoList
}

/**
 * 履歴情報リスト設定
 *
 * @param selectId - 履歴情報選択行ID
 */
const historyInfoSet = (selectId: string) => {
  let historyInfoList
  let selectHistoryId = ''
  if (local.historyInfoBak.items) {
    // 履歴情報取得
    if (props.onewayModelValue.kikanFlg === Or28646Const.DEFAULT.KIKAN_OK) {
      historyInfoList = local.historyInfoBak?.items?.filter(
        (item: { sc1Id: unknown }) => item.sc1Id === selectId
      )
    } else {
      historyInfoList = local.historyInfoBak.items
    }
    if (historyInfoList && historyInfoList.length > 0) {
      selectHistoryId = historyInfoList[0].week1Id
    }

    if (selectHistoryId) {
      // 履歴情報選択明細があり場合、
      historySelectedItem.value = selectHistoryId
      // 週間表情報設定
      weekTableDetailInfoSet(selectHistoryId)
    }
  }

  // 履歴情報設定
  local.historyInfo.items = historyInfoList ?? []
}

/**
 * 週間表情報リスト設定
 *
 * @param selectId - 履歴情報選択行ID
 */
const weekTableDetailInfoSet = (selectId: string) => {
  local.weekTableDetailInfo.items = []

  let weekTableDetailInfoList
  if (local.weekTableDetailInfoBak.items) {
    // 週間表情報取得
    weekTableDetailInfoList = local.weekTableDetailInfoBak?.items?.filter(
      (item: { week1Id: unknown }) => item.week1Id === selectId
    )
  }

  // 週間表情報の設定
  Object.assign(local.weekTableDetailInfo.items, weekTableDetailInfoList)

  if (local.or28646.filterSelectValue?.modelValue !== undefined) {
    filterChanged(local.or28646.filterSelectValue?.modelValue)
  }
}

// 計画期間情報選択行設定
const isSelected = (item: { sc1Id: null }) => selectedItem.value === item.sc1Id
// 履歴情報選択行設定
const historyIsSelected = (item: { week1Id: null }) => historySelectedItem.value === item.week1Id

// 計画期間情報選択行データ設定
const selectedItem = ref('')
// 履歴情報選択行データ設定
const historySelectedItem = ref('')
// 週間表取込情報選択行リスト
const weekTableDetailSelected: Ref<IkouIchiranItem[], IkouIchiranItem[]> = ref([])
// 計画期間情報行選択処理
const planPeriodClick = async (item: { sc1Id: string }) => {
  if (selectedItem.value !== item.sc1Id) {
    // 週間表取込情報をクリアする
    weekTableDetailSelected.value = []
    // 計画期間行選択
    selectedItem.value = item.sc1Id
    // 履歴情報取得
    await getHistoryInfo(item.sc1Id)
  }
}
async function getHistoryInfo(sc1Id: string) {
  // バックエンドAPIから履歴情報取得
  localOneway.historyInfoParam.sc1Id = sc1Id
  const inParam: IHistoryInfoInEntity = localOneway.historyInfoParam
  const ret: IHistoryInfoOutEntity = await ScreenRepository.select(
    'weekImportHistoryInfoSelect',
    inParam
  )
  const resData: HistoryInfoDetailsType = ret.data
  if (resData) {
    for (const item of resData.historyInfo) {
      const termNameSet = localOneway.mo01282TermidOneway.find(
        (term) => term.value === item.termId
      )?.label
      if (termNameSet !== undefined) {
        item.termName = termNameSet
      }
    }
    // 履歴情報BAK---設定
    local.historyInfoBak.items = resData.historyInfo

    for (const item of resData.weekTableDetailInfo) {
      const dataKnjSet = localOneway.mo01282DataidOneway.find(
        (data) => data.label === item.dataKnj
      )?.label
      if (dataKnjSet !== undefined) {
        item.dataKnj = dataKnjSet
      }
    }
    local.weekTableDetailInfoBak.items = resData.weekTableDetailInfo
    // 履歴情報設定
    historyInfoSet(sc1Id)
  }
}

// 履歴情報行選択処理
const historyRowClick = async (item: { week1Id: string }) => {
  // 週間表取込選択情報をクリアする
  weekTableDetailSelected.value = []

  if (historySelectedItem.value !== item.week1Id) {
    historySelectedItem.value = item.week1Id // 履歴情報选中行

    // 週間表情報設定
    await getWeekTableDetailInfo(item.week1Id)
  }
}
async function getWeekTableDetailInfo(week1Id: string) {
  // バックエンドAPIから履歴情報取得
  localOneway.weekTableInfoParam.week1Id = week1Id
  const inParam: IWeekTableDetailInEntity = localOneway.weekTableInfoParam
  const ret: IWeekTableDetailOutEntity = await ScreenRepository.select(
    'weekImportParticularInfoSelect',
    inParam
  )
  const resData: WeekTableDetailInfosType = ret.data
  if (resData) {
    for (const item of resData.weekTableDetailInfo) {
      const dataKnjSet = localOneway.mo01282DataidOneway.find(
        (data) => data.label === item.dataKnj
      )?.label
      if (dataKnjSet !== undefined) {
        item.dataKnj = dataKnjSet
      }
    }
    local.weekTableDetailInfoBak.items = resData.weekTableDetailInfo
    // 週間表情報設定
    weekTableDetailInfoSet(week1Id)
  }
}

// 週間表取込の行選択スタイル設定
const getItemClass = (item: { item: IkouIchiranItem }) => {
  return weekTableDetailSelected.value.includes(item.item)
    ? { class: { 'selected-row': true } }
    : ''
}
/**
 * 絞込の選択肢変更処理
 *
 * @param filterSelectValue - 絞込の選択肢
 */
function filterChanged(filterSelectValue: string): void {
  local.weekTableDetailInfo.items = []
  let weekTableDetailInfoList
  if (local.weekTableDetailInfoBak.items) {
    // 週間表情報取得
    weekTableDetailInfoList = local.weekTableDetailInfoBak?.items?.filter(
      (item: { week1Id: unknown }) => item.week1Id === historySelectedItem.value
    )
    if (filterSelectValue !== local.FilterSelectList[0].value) {
      weekTableDetailInfoList = weekTableDetailInfoList?.filter(
        (item: { dataKnj: unknown }) =>
          item.dataKnj === local.FilterSelectList[Number(filterSelectValue)].label
      )
    }
  }
  // 週間表情報の設定
  Object.assign(local.weekTableDetailInfo.items, weekTableDetailInfoList)
}
/**
 * 閉じるボタン押下時
 */
function onClickCloseBtn(): void {
  // if (!isEdit.value) {
  // weekTableDetailSelected.value = []
  setState({ isOpen: false })
  // } else {
  //   // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
  //   showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
  // }
}

/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
// function showOr21814MsgThreeBtn(errormsg: string) {
//   Or21814Logic.state.set({
//     uniqueCpId: or21814.value.uniqueCpId,
//     state: {
//       // ダイアログタイトル
//       dialogTitle: t('label.confirm'),
//       // ダイアログテキスト0
//       dialogText: errormsg,
//       firstBtnType: 'normal1',
//       firstBtnLabel: t('btn.yes'),
//       secondBtnType: 'destroy1',
//       secondBtnLabel: t('btn.no'),
//       thirdBtnType: 'normal3',
//       thirdBtnLabel: t('btn.cancel'),
//     },
//   })
//   // 確認ダイアログをオープン
//   Or21814Logic.state.set({
//     uniqueCpId: or21814.value.uniqueCpId,
//     state: {
//       isOpen: true,
//     },
//   })
// }

/**
 * OrX0020（閉じる処理ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.firstBtnClickFlg) {
      doConfirm()
      return
    }
    if (newValue.secondBtnClickFlg) {
      // いいえ：画面を閉じる。
      setState({ isOpen: false })
      // 保存しないため、local.or28646の値を前回の初期値で保存した上で、返却する
      local.or28646.weekMemoImportFlag!.modelValue = initValue.weekMemoImportFlag
      local.or28646.filterSelectValue!.modelValue = initValue.filterSelectValue
      emit('update:modelValue', local.or28646)
      return
    }
    if (newValue.thirdBtnClickFlg) {
      // キャンセル：処理終了。
      return
    }
  }
)

/**
 * 確定ボタン処理
 *
 */
function doConfirm() {
  const { setChildCpBinds } = useScreenUtils()
  setChildCpBinds(props.parentCpId, {
    Or28646: {
      twoWayValue: local.or28646,
    },
  })

  emit('update:modelValue', local.or28646)

  setState({ isOpen: false })
}

/**
 * 絞込ボタンクリック
 */
const clickfilter = () => {
  for (let i = 0; i < local.FilterSelectList.length; i++) {
    if (local.or28646.filterSelectValue !== undefined) {
      if (local.or28646.filterSelectValue.modelValue === local.FilterSelectList[i].value) {
        if (i === local.FilterSelectList.length - 1) {
          local.or28646.filterSelectValue.modelValue = local.FilterSelectList[0].value
          break
        } else {
          local.or28646.filterSelectValue.modelValue = local.FilterSelectList[i + 1].value
          break
        }
      }
    }
  }
}

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
watch(
  () => local.or28646,
  () => {
    refValue.value = local.or28646
  },
  { deep: true }
)
/**
 * 確定ボタン押下時
 */
function onConfirmBtn(): void {
  if (weekTableDetailSelected.value && weekTableDetailSelected.value.length > 0) {
    // 選択データが存在する場合
    const weekTableDetailOutList = weekTableDetailSelected.value
    for (const item of weekTableDetailOutList) {
      //画面.曜日取込否
      if (local.or28646.weekMemoImportFlag?.modelValue === false) {
        item.memoKnj = ''
      } else {
        //曜日が「他」の場合
        //頻度が空白ではない場合
        const memoKnj = item.memoKnj
        item.memoKnj = ''
        if (
          item.youbi.substring(7, 8) === Or28646Const.DEFAULT.YOUBI_OTHER &&
          item.frequency !== ''
        ) {
          item.memoKnj =
            t('label.weekly-plan-frequency') +
            ':' +
            item.frequency +
            Or28646Const.DEFAULT.LINE_BREAK +
            memoKnj
        }
        if (item.youbi.substring(6, 7) !== Or28646Const.DEFAULT.YOUBI_OTHER) {
          if (item.youbi.startsWith(Or28646Const.DEFAULT.YOUBI_SEL)) {
            item.memoKnj = t('label.weekly-plan-day-short-monday')
          }
          if (item.youbi.substring(1, 2) === Or28646Const.DEFAULT.YOUBI_SEL) {
            if (item.memoKnj === '') {
              item.memoKnj = t('label.weekly-plan-day-short-tuesday')
            } else {
              item.memoKnj = '、' + t('label.weekly-plan-day-short-tuesday')
            }
          }
          if (item.youbi.substring(2, 3) === Or28646Const.DEFAULT.YOUBI_SEL) {
            if (item.memoKnj === '') {
              item.memoKnj = t('label.weekly-plan-day-short-wednesday')
            } else {
              item.memoKnj = '、' + t('label.weekly-plan-day-short-wednesday')
            }
          }
          if (item.youbi.substring(3, 4) === Or28646Const.DEFAULT.YOUBI_SEL) {
            if (item.memoKnj === '') {
              item.memoKnj = t('label.weekly-plan-day-short-thursday')
            } else {
              item.memoKnj = '、' + t('label.weekly-plan-day-short-thursday')
            }
          }
          if (item.youbi.substring(4, 5) === Or28646Const.DEFAULT.YOUBI_SEL) {
            if (item.memoKnj === '') {
              item.memoKnj = t('label.weekly-plan-day-short-friday')
            } else {
              item.memoKnj = '、' + t('label.weekly-plan-day-short-friday')
            }
          }
          if (item.youbi.substring(5, 6) === Or28646Const.DEFAULT.YOUBI_SEL) {
            if (item.memoKnj === '') {
              item.memoKnj = t('label.weekly-plan-day-short-saturday')
            } else {
              item.memoKnj = '、' + t('label.weekly-plan-day-short-saturday')
            }
          }
          if (item.youbi.substring(6, 7) === Or28646Const.DEFAULT.YOUBI_SEL) {
            if (item.memoKnj === '') {
              item.memoKnj = t('label.weekly-plan-day-short-sunday')
            } else {
              item.memoKnj = '、' + t('label.weekly-plan-day-short-sunday')
            }
          }
          item.memoKnj = item.memoKnj + Or28646Const.DEFAULT.LINE_BREAK + memoKnj
        }
      }
      //画面.開始時間、終了時間
      if (item.kaishiJikan === item.shuuryouJikan) {
        item.kaishiJikan = Or28646Const.DEFAULT.START_TIME
        item.shuuryouJikan = Or28646Const.DEFAULT.END_TIME
      }
      if (item.shuuryouJikan > Or28646Const.DEFAULT.END_TIME) {
        item.shuuryouJikan = Or28646Const.DEFAULT.END_TIME
      }
      if (
        item.kaishiJikan.substring(0, 2) > item.shuuryouJikan.substring(0, 2) ||
        (item.kaishiJikan.substring(0, 2) === item.shuuryouJikan.substring(0, 2) &&
          item.kaishiJikan.substring(3, 5) > item.shuuryouJikan.substring(3, 5))
      ) {
        local.or28646.weekTableDetailList.push({
          //開始時間
          kaishiJikan: Or28646Const.DEFAULT.START_TIME,
          //終了時間
          shuuryouJikan: item.shuuryouJikan,
          //内容CD
          naiyoCd: item.naiyoCd,
          //内容
          contents: item.contents,
          //メモ
          memoKnj: item.memoKnj,
          //文字サイズ
          fontSize: item.fontSize,
          //表示モード
          dispMode: item.dispMode,
          //文字位置
          alignment: item.alignment,
          //文字カラー
          fontColor: item.fontColor,
          //背景カラー
          backColor: item.backColor,
          //時間表示区分
          timeKbn: item.timeKbn,
          //取込先
          dataKnj: item.dataKnj,
        })
        item.shuuryouJikan = Or28646Const.DEFAULT.END_TIME
      }
    }
    for (const item of weekTableDetailOutList) {
      local.or28646.weekTableDetailList.push({
        //開始時間
        kaishiJikan: item.kaishiJikan,
        //終了時間
        shuuryouJikan: item.shuuryouJikan,
        //内容CD
        naiyoCd: item.naiyoCd,
        //内容
        contents: item.contents,
        //メモ
        memoKnj: item.memoKnj,
        //文字サイズ
        fontSize: item.fontSize,
        //表示モード
        dispMode: item.dispMode,
        //文字位置
        alignment: item.alignment,
        //文字カラー
        fontColor: item.fontColor,
        //背景カラー
        backColor: item.backColor,
        //時間表示区分
        timeKbn: item.timeKbn,
        //取込先
        dataKnj: item.dataKnj,
      })
    }
    emit('update:modelValue', local.or28646)
    setState({ isOpen: false })
  } else {
    // 選択データが存在しない場合
    showOr21815MsgOneBtn(t('message.w-cmn-20791'))
  }
}
/**
 * 確定確認ダイアログ
 *
 * @param errormsg - errormsg - Message
 */
function showOr21815MsgOneBtn(errormsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      dialogTitle: t('label.warning'),
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

const mo00018Oneway = ref<Mo00018OnewayType>({
  name: '',
  itemLabel: '',
  checkboxLabel: t('label.day-of-week-memo-import'),
  showItemLabel: false,
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
watch(
  () => local.or28646.filterSelectValue!.modelValue!,
  (newVal): void => {
    filterChanged(newVal)
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row>
          <c-v-col
            v-if="props.onewayModelValue.kikanFlg == Or28646Const.DEFAULT.KIKAN_OK"
            cols="4"
          >
            <!-- 計画期間選択一覧 -->
            <c-v-data-table
              :items="local?.planPeriodInfo?.items"
              style="height: 190px"
              :headers="moDataTable1.headers"
              hide-default-footer
              class="table-wrapper"
              :items-per-page="-1"
              hover
              fixed-header
            >
              <template #headers>
                <tr>
                  <th
                    v-for="header in moDataTable1.headers"
                    :key="header.key"
                    :width="header.width"
                  >
                    <span>{{ header.title }}</span>
                  </th>
                </tr>
              </template>
              <template #item="{ item }">
                <tr
                  :class="{ 'row-selected': isSelected(item) }"
                  @click="planPeriodClick(item)"
                >
                  <td style="text-align: left">
                    <span>{{ item.startYmd }} {{ t('label.wavy') }} {{ item.endYmd }}</span>
                  </td>
                  <td style="text-align: right">
                    <span>{{ item.rirekiCnt }}</span>
                  </td>
                </tr>
              </template>
            </c-v-data-table>
          </c-v-col>

          <c-v-col
            :cols="props.onewayModelValue.kikanFlg == Or28646Const.DEFAULT.KIKAN_OK ? '8' : 'auto'"
          >
            <!-- 履歴選択一覧 -->
            <c-v-data-table
              :items="local?.historyInfo?.items"
              style="height: 190px"
              :headers="moDataTable2.headers"
              hide-default-footer
              class="table-wrapper"
              :items-per-page="-1"
              hover
              fixed-header
            >
              <template #item="{ item }">
                <tr
                  :class="{ 'row-selected': historyIsSelected(item) }"
                  @click="historyRowClick(item)"
                >
                  <td>
                    <span>{{ item.createYmd }}</span>
                  </td>
                  <td>
                    <span>{{ item.shokuKnj }}</span>
                  </td>
                  <td style="text-align: right">
                    <span>{{ item.caseNo }}</span>
                  </td>
                  <td>
                    <span>{{ item.tougaiYm }}</span>
                  </td>
                  <td>
                    <span>{{ item.termName }}</span>
                  </td>
                  <td>
                    <span>{{ item.kaiteiKnj }}</span>
                  </td>
                </tr>
              </template>
              <template #bottom />
            </c-v-data-table>
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          class="c-border mt-2"
        >
          <!--曜日取込否-->
          <c-v-col
            cols="auto"
            class="w-auto flex-0-0"
            style="padding-right: 0px !important"
          >
            <base-mo00018
              v-model="local.or28646.weekMemoImportFlag"
              :oneway-model-value="mo00018Oneway"
            />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="w-auto flex-0-0"
            style="padding-right: 0px !important"
          >
            <base-mo00611
              class="w-auto flex-0-0 mt-2"
              :v-bind="{ ...$attrs }"
              :oneway-model-value="{ btnLabel: t('btn.filter-import') }"
              @click="clickfilter"
            />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="w-auto flex-0-0"
            style="padding-left: 0px !important"
          >
            <base-mo00040
              v-model="local.or28646.filterSelectValue"
              :oneway-model-value="localOneway.mo00040Oneway"
              v-bind="{ ...$attrs }"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col class="w-auto flex-0-0">
            <!-- 週間表情報をテーブル表示 -->
            <c-v-data-table
              v-model="weekTableDetailSelected"
              height="200px"
              style="width: 100%; padding-top: 8px"
              :headers="moDataTable3.headers"
              :items="local?.weekTableDetailInfo?.items"
              item-value="name"
              return-object
              show-select
              fixed-header
              hover
              class="table-wrapper"
              items-per-page="-1"
              :hide-default-footer="true"
              :row-props="getItemClass"
            >
              <template #[`item.naiyoKnj`]="{ item }">
                <div class="solt-row;align-items:center">
                  <span>{{ item.naiyoKnj }}</span>
                </div>
              </template>
              <template #[`item.memoKnj`]="{ item }">
                <div class="solt-row;align-items:center">
                  <span>{{ item.memoKnj }}</span>
                </div>
              </template>
              <template #[`item.dataKnj`]="{ item }">
                <div class="solt-row;align-items:center">
                  <div>{{ item.dataKnj }}</div>
                </div>
              </template>
              <template #[`item.jikan`]="{ item }">
                <div class="solt-row;align-items:center">
                  <div>{{ item.kaishiJikan }} {{ t('label.wavy') }} {{ item.shuuryouJikan }}</div>
                </div>
              </template>
              <template #[`item.youbi`]="{ item }">
                <div class="flex-container">
                  <div
                    v-if="item.youbi.substring(0, 1) == Or28646Const.DEFAULT.YOUBI_SEL"
                    class="week-selected"
                  >
                    {{ t('label.weekly-plan-day-short-monday') }}
                  </div>
                  <div
                    v-else
                    class="week-unselected"
                  >
                    {{ t('label.weekly-plan-day-short-monday') }}
                  </div>
                  <div style="width: 15px"></div>
                  <div
                    v-if="item.youbi.substring(1, 2) == Or28646Const.DEFAULT.YOUBI_SEL"
                    class="week-selected"
                  >
                    {{ t('label.weekly-plan-day-short-tuesday') }}
                  </div>
                  <div
                    v-else
                    class="week-unselected"
                  >
                    {{ t('label.weekly-plan-day-short-tuesday') }}
                  </div>
                  <div style="width: 15px"></div>
                  <div
                    v-if="item.youbi.substring(2, 3) == Or28646Const.DEFAULT.YOUBI_SEL"
                    class="week-selected"
                  >
                    {{ t('label.weekly-plan-day-short-wednesday') }}
                  </div>
                  <div
                    v-else
                    class="week-unselected"
                  >
                    {{ t('label.weekly-plan-day-short-wednesday') }}
                  </div>
                  <div style="width: 15px"></div>
                  <div
                    v-if="item.youbi.substring(3, 4) == Or28646Const.DEFAULT.YOUBI_SEL"
                    class="week-selected"
                  >
                    {{ t('label.weekly-plan-day-short-thursday') }}
                  </div>
                  <div
                    v-else
                    class="week-unselected"
                  >
                    {{ t('label.weekly-plan-day-short-thursday') }}
                  </div>
                  <div style="width: 15px"></div>
                  <div
                    v-if="item.youbi.substring(4, 5) == Or28646Const.DEFAULT.YOUBI_SEL"
                    class="week-selected"
                  >
                    {{ t('label.weekly-plan-day-short-friday') }}
                  </div>
                  <div
                    v-else
                    class="week-unselected"
                  >
                    {{ t('label.weekly-plan-day-short-friday') }}
                  </div>
                </div>
                <div class="flex-container">
                  <div
                    v-if="item.youbi.substring(5, 6) == Or28646Const.DEFAULT.YOUBI_SEL"
                    class="week-selected"
                  >
                    {{ t('label.weekly-plan-day-short-saturday') }}
                  </div>
                  <div
                    v-else
                    class="week-unselected"
                  >
                    {{ t('label.weekly-plan-day-short-saturday') }}
                  </div>
                  <div style="width: 15px"></div>
                  <div
                    v-if="item.youbi.substring(6, 7) == Or28646Const.DEFAULT.YOUBI_SEL"
                    class="week-selected"
                  >
                    {{ t('label.weekly-plan-day-short-sunday') }}
                  </div>
                  <div
                    v-else
                    class="week-unselected"
                  >
                    {{ t('label.weekly-plan-day-short-sunday') }}
                  </div>
                  <div style="width: 75px"></div>
                  <div
                    v-if="item.youbi.substring(7, 8) == Or28646Const.DEFAULT.YOUBI_OTHER"
                    class="week-selected"
                  >
                    {{ t('label.weekly-plan-day-short-other') }}
                  </div>
                  <div
                    v-else
                    class="week-unselected"
                  >
                    {{ t('label.weekly-plan-day-short-other') }}
                  </div>
                </div>
              </template>
            </c-v-data-table>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway1"
          @click="onClickCloseBtn"
        >
        </base-mo00611>

        <!-- 確定 -->
        <base-mo00609
          :oneway-model-value="mo00611Oneway2"
          class="mx-2"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21815:有機体 -->
  <g-base-or21815
    v-if="showDialog"
    v-bind="or21815"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.table-header :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  font-weight: bold;
  white-space: nowrap;
  box-shadow: none !important;
  // padding: 0 8px !important;
}

:deep(.table-header td) {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-color: lightgrey;
  font-size: 14px;
  white-space: nowrap;
  // padding: 0 !important;
}

/** 行選択の様式 */
.row-selected {
  background-color: #e3f2fd !important;
  cursor: pointer;
}

tr {
  cursor: pointer;
}

/** 週間表取込のチェックボックス様式設定 */
.table-header :deep(.highlight-row) {
  background-color: #e3f2fd !important;
}

.v-col {
  padding: 8px;
}

/** チェックボックス選択中の様式 */

/** 週間表取込の明細行の高さ設定 */
.longTerm-Goals-table :deep(.v-data-table__tr) {
  height: 86px;
}

.longTerm-Goals-table3 :deep(.highlight-row) {
  background-color: #e3f2fd !important;
}

.longTerm-Goals-table3 :deep(.v-data-table__tr) {
  max-height: 86px;
}

.longTerm-Goals-table3 :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  outline: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  font-weight: bold;
  white-space: pre-line;
  word-break: break-word;
  box-shadow: none !important;
}

:deep(.longTerm-Goals-table3 td) {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-color: lightgrey;
  font-size: 14px;
  white-space: pre-line;
  word-break: break-word;
  /* display: inline-block; */
  overflow-y: aotu;
  max-height: 20px !important;
}

.td-items {
  padding: 0 !important;
  display: flex;
  /* justify-content: center; */
  span {
    display: flex;
    flex: 1;
    padding: 16px;
    align-items: center;
    &:not(:last-child) {
      border-right: 1px rgb(var(--v-theme-black-200)) solid;
    }
  }
}
.solt-row {
  height: 100%;
  margin: 0 -16px 0 -16px;
  div {
    padding: 0 0 0 16px;
    height: 36px;
    line-height: 36px;
    &:not(:last-child) {
      border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
    }
  }
  .ellipsis-frequency {
    padding-left: 16px;
  }
}
.ellipsis {
  display: inline-block;
  width: 139px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis-frequency {
  display: inline-block;
  width: 139px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.overflow-td {
  height: 100px;
  overflow-y: auto;
}

.pre-line-td {
  width: 100%;
  /* display: inline-block; */
  white-space: pre-line;
  /* overflow: hidden; */
  /* text-overflow: ellipsis; */
}
:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: 0px;
  z-index: 2;
}
/** 枠線 */
.c-border {
  border: solid 1px rgb(224, 224, 224);
}
.bottom-border {
  border-bottom: 1px solid rgb(224, 224, 224);
}
.flex-container {
  display: flex;
}
.week-selected {
  width: 15px;
  color: #0000ff;
}
.week-unselected {
  width: 15px;
  color: #bfd9ff;
}

:deep(.v-data-table__td--select-row .fill) {
  color: rgb(var(--v-theme-key)) !important;
}

:deep(.v-selection-control--dirty .fill) {
  color: rgb(var(--v-theme-key)) !important;
}
</style>
