<script setup lang="ts">
import { computed, ref, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or28415Const } from '~/components/custom-components/organisms/Or28415/Or28415.constants'
import { Or28415Logic } from '~/components/custom-components/organisms/Or28415/Or28415.logic'
import type { Or28415Type, Or28415OnewayType } from '~/types/cmn/business/components/Or28415Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**
 * GUI00647_親族・関係者選択
 *
 * @description
 * 「親族・関係者選択」画面を表示
 *
 * <AUTHOR> 靳先念
 */

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00647'
// ルーティング
const routing = 'GUI00647/pinia'
// 画面物理名
const screenName = 'GUI00647'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or28415 = ref({ uniqueCpId: Or28415Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00647' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or28415Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or28415.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00647',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28415Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or28415Const.CP_ID(1)]: or28415.value,
})

// ダイアログ表示フラグ
const showDialogOr28415 = computed(() => {
  // Or28415のダイアログ開閉状態
  return Or28415Logic.state.get(or28415.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or28415)
 */
function onClickOr28415() {
  // Or28415のダイアログ開閉状態を更新する
  Or28415Logic.state.set({
    uniqueCpId: or28415.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or28415Type = ref<Or28415Type>({
  /** 関係者リスト */
  kankeisyaList: [],
  frenrakusakiFlglg: '1',
})

const title28415 = computed(() => {
  let str = ' '
  if (or28415Type.value.frenrakusakiFlglg === '1') {
    for (const item of or28415Type.value.kankeisyaList) {
      str = str + item.name + '、'
    }
  } else {
    for (const item of or28415Type.value.kankeisyaList) {
      str = str + item.zokugaraKnj + '、'
    }
  }
  str = str.slice(0, str.length - 1)
  return str
})

const or28415Data: Or28415OnewayType = {
  /** 利用者ID */
  userId: '1',
}

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  userId: { value: '1' } as Mo00045Type,
})

/** GUI00647 疎通起動  */
const or28415OnClick = () => {
  or28415Data.userId = local.userId.value
  // Or28415のダイアログ開閉状態を更新する
  Or28415Logic.state.set({
    uniqueCpId: or28415.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28415"
        >GUI00647_［親族・関係者選択］画面{{ title28415 }}
      </v-btn>
      <g-custom-or-28415
        v-if="showDialogOr28415"
        v-bind="or28415"
        v-model="or28415Type"
        :oneway-model-value="or28415Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="or28415OnClick"> GUI00647 疎通起動 </v-btn>
  </div>
  <div class="pt-5 pl-5">
    return ----- data

    <div>
      {{ or28415Type }}
    </div>
  </div>
</template>
