/**
 * Or00233:有機体:（希望負担額登録）ダイアログ
 * GUI01174_希望負担額登録
 *
 * <AUTHOR>
 */
import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 希望負担額情報入力エンティティ
 */
export interface AssessmentRirekiTorikomiSelectInEntity extends InWebEntity {
  /** 事業所ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 施設ID */
  shisetuId: string
  /** 種別ID */
  syubetsuId: string
  /** メニュー２名称 */
  menu2Knj: string
  /** メニュー３名称 */
  menu3Knj: string
  /** 自由パラメータ */
  valS: string
}

/**
 * 期間管理画面全体エンティティ
 */
export interface AssessmentRirekiTorikomiSelectOutEntity extends OutWebEntity {
  /** データ */
  data: {
    /** 期間管理フラグ */
    kikanFlg: string
    /** 判断フラグ */
    handanFlg: string
    /** 期間リスト */
    kikanList: KikanInfo[]
    /** 履歴リスト */
    rirekiList: RirekiInfo[]
    /** アセスメント履歴取込詳細リスト */
    torikomiList: TorikomiDetail[]
  }
}
/**
 * アセスメント履歴取込詳細情報
 */
export interface TorikomiDetail {
  /** 項目ID1 */
  koumoku1Id: string
  /** 項目ID2 */
  koumoku2Id: string
  /** アセスメント番号 */
  assNo: string
  /** アセスメント名 */
  assKnj: string
  /** 項目名1 */
  koumoku1Knj: string
  /** 項目名2 */
  koumoku2Knj: string
  /** 適用フラグ */
  tekiyouFlg: string
  /** アセスメント評価 */
  assAns: string
  /** 現在フラグ */
  genzaiFlg: string
}

/**
 * アセスメント履歴情報
 */
export interface RirekiInfo {
  /** アセスメントID */
  raiId: string
  /** 調査アセスメント種別 */
  assType: string
  /** 調査日 */
  assDateYmd: string
  /** 調査者ID */
  assShokuId: string
  /** 更新回数 */
  modifiedCnt: string
}

/**
 * 計画期間情報
 */
export interface KikanInfo {
  /** 期間ID */
  sc1Id: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 種別ID */
  syubetsuId: string
  /** 開始日 */
  startYmd: string
  /** 終了日 */
  endYmd: string
  /** 事業名 */
  jigyoKnj: string
}
