<script setup lang="ts">
/**
 * OrX0192：有機体：（確定版）家族構成／連絡先について
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or55476OneWayType } from '../Or55476/Or55476.type'
import { Or55476Const } from '../Or55476/Or55476.constants'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import { OrX0157Const } from '../OrX0157/OrX0157.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import type { OrX0192OneWayType, OrX0192ValuesType } from './OrX0192.Type'
import { OrX0192Const } from './OrX0192.constants'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
import type { Mo01267OnewayType } from '~/types/business/components/Mo01267Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'

/**************************************************
 * Props
 **************************************************/
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()
const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル
const or55476 = ref({ uniqueCpId: '' })
const { refValue } = useScreenTwoWayBind<OrX0192ValuesType>({
  cpId: OrX0192Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<OrX0192ValuesType> }
useScreenOneWayBind<OrX0192OneWayType>({
  cpId: OrX0192Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value!
    },
  },
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or55476Const.CP_ID(1)]: or55476.value,
})
/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  commonInfo: {} as TeX0012Type,
  or51775: { modelValue: '' } as Or51775Type,
  or51775Value: '',
  or55476Value: '',
})

const localOneway = reactive({
  orX0156Oneway: {
    itemLabel: t('label.special-note-matter-support'),
    showItemLabel: true,
    showDividerLineFlg: true,
    showEditBtnFlg: true,
    isVerticalLabel: true,
    customClass: {
      labelClass: 'pa-0',
      outerClass: 'w-982',
    } as CustomClass,
    maxRows: '4',
    rows: '4',
    noResize: true,
    autoGrow: false,
    maxlength: '4000',
  },
  codeListOneway: {} as Record<string, CodeType[]>,
  mo01267Oneway: {
    btnLabel: t('label.primary_caregiver_name_label'),
    to: '',
  } as Mo01267OnewayType,
  mo01267Oneway2: {
    btnLabel: t('label.key_person_label'),
    to: '',
  } as Mo01267OnewayType,
  orX0157Oneway: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: false,
        width: '253px',
        maxlength: '40',
      },
    },
  },
  // GUI00937 共通入力支援画面
  or51775Oneway: {
    screenId: OrX0192Const.DEFAULT.GUI,
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: OrX0192Const.DEFAULT.ASSESS_MENT_METHOD, // 共通情報.アセスメント方式
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  or55476Oneway: {
    userId: OrX0192Const.DEFAULT.VALUE_1,
    telCellFlg: OrX0192Const.DEFAULT.VALUE_1,
    createYmd: systemCommonsStore.getSystemDate ?? '',
  } as Or55476OneWayType,
  mo00045Oneway1: {
    maxlength: '41',
    width: '231px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway2: {
    maxlength: '41',
    width: '193px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Oneway: {
    valueFontWeight: 'blod',
    value: t('label.oral-nutrition'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
      outerClass: 'content-title',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338Oneway2: {
    valueFontWeight: 'blod',
    value: t('label.communication-of-intent'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
      outerClass: 'content-title',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039Oneway: {
    itemLabel: '',
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,

  // 文字数入力
  mo00038Oneway1: {
    mo00045Oneway: {
      appendLabel: t('label.primary_caregiver_years_label'),
      showItemLabel: false,
      maxLength: '3',
      width: '100px',
      customClass: new CustomClass({
        outerClass: 'ml-2',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 999,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  mo00040Oneway1: {
    itemTitle: 'zcode',
    itemValue: 'zokugaraKnj',
    showItemLabel: false,
    itemLabelFontWeight: 'bold',
    width: '242px',
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,

  mo00030Oneway: {
    mo00045Oneway: {
      maxlength: '14',
      width: '193px',
      isVerticalLabel: false,
      showItemLabel: false,
    },
    mode: OrX0192Const.DEFAULT.VALUE_1,
  },
  feedingDataList: [
    {
      label: t('label.intake_method_oral'),
      key: 'sesshuHouhouKeikou',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.intake_method_enteral'),
      key: 'sesshuHouhouKeikan',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.intake_method_iv'),
      key: 'sesshuHouhouJoumyaku',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  suibunKbnDataList: [
    {
      label: t('label.thin'),
      key: 'suibunKbn1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medium'),
      key: 'suibunKbn2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.thick'),
      key: 'suibunKbn3',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  foodDataList: [
    {
      label: t('label.rice'),
      key: 'shushokuGohan',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.soft-rice'),
      key: 'shushokuNanhan',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.porridge'),
      key: 'shushokuKayu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.other-label'),
      key: 'shushokuSonota',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  foodFormDataList: [
    {
      label: t('label.meal_form_regular'),
      key: 'fukushokuHutuu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.meal_form_soft'),
      key: 'fukushokuNansai',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.other-label'),
      key: 'fukushokuSonota',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
})

/**
 * mo00039の値変更を監視
 *
 * @param mo00039 - Mo00039Type
 */
const handleSuibun = (mo00039: string) => {
  // if (mo00039 === OrX0192Const.DEFAULT.VALUE_0) {
  //   refValue.value.orX0192Values.shintaiShogaiKbn.modelValue = false
  //   refValue.value.orX0192Values.chitekiShogaiKbn.modelValue = false
  //   refValue.value.orX0192Values.seishinShogaiKbn.modelValue = false
  // }
}

/**
 * mo00039の値変更を監視
 *
 * @param mo00039 - Mo00039Type
 */
const handleGishiKbn = (mo00039: string) => {
  // if (mo00039 === OrX0192Const.DEFAULT.VALUE_0) {
  //   refValue.value.orX0192Values.shintaiShogaiKbn.modelValue = false
  //   refValue.value.orX0192Values.chitekiShogaiKbn.modelValue = false
  //   refValue.value.orX0192Values.seishinShogaiKbn.modelValue = false
  // }
}

/**
 * mo00018の値変更を監視
 *
 * @param mo00018 - mo00018
 */
const handleMo00018 = (mo00018: Mo00018Type) => {
  // if (mo00018.modelValue) {
  //   refValue.value.orX0192Values.shogaiNintei = OrX0192Const.DEFAULT.VALUE_1
  // }
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleFoodAllergyKnj = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  localOneway.or51775Oneway.title = t('label.food-allergy')
  localOneway.or51775Oneway.t2Cd = OrX0192Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = OrX0192Const.DEFAULT.VALUE_2
  localOneway.or51775Oneway.columnName = 'food_allergy'
  localOneway.or51775Oneway.inputContents = t('label.food-allergy')
  local.or51775Value = 'foodAllergyKnj'
  local.or51775.modelValue = refValue.value.orX0192Values.foodAllergyKnj?.value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleKoukuuCareShinikuKnj = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  localOneway.or51775Oneway.title = t('label.gum-swelling-and-bleeding')
  localOneway.or51775Oneway.t2Cd = OrX0192Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = OrX0192Const.DEFAULT.VALUE_6
  localOneway.or51775Oneway.columnName = 'koukuu_care_shiniku_knj'
  localOneway.or51775Oneway.inputContents = t('label.gum-swelling-and-bleeding')
  local.or51775Value = 'koukuuCareShinikuKnj'
  local.or51775.modelValue = refValue.value.orX0192Values.koukuuCareShinikuKnj?.value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleShushokuSonotaKnj = () => {
  // // GUI00937 共通入力支援画面をポップアップで起動する
  localOneway.or51775Oneway.title = t('label.udf_meal_form_category_label')
  localOneway.or51775Oneway.t2Cd = OrX0192Const.DEFAULT.VALUE_2
  localOneway.or51775Oneway.t3Cd = OrX0192Const.DEFAULT.VALUE_4
  localOneway.or51775Oneway.columnName = 'udf_shokuji_keitai_kbn'
  localOneway.or51775Oneway.inputContents = t('label.udf_meal_form_category_label')
  local.or51775Value = 'shushokuSonotaKnj'
  local.or51775.modelValue = refValue.value.orX0192Values.shushokuSonotaKnj?.value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleFukushokuSonotaKnj = () => {
  // // GUI00937 共通入力支援画面をポップアップで起動する
  localOneway.or51775Oneway.title = t('label.udf_meal_form_category_label')
  localOneway.or51775Oneway.t2Cd = OrX0192Const.DEFAULT.VALUE_2
  localOneway.or51775Oneway.t3Cd = OrX0192Const.DEFAULT.VALUE_4
  localOneway.or51775Oneway.columnName = 'udf_shokuji_keitai_kbn'
  localOneway.or51775Oneway.inputContents = t('label.udf_meal_form_category_label')
  local.or51775Value = 'fukushokuSonotaKnj'
  local.or51775.modelValue = refValue.value.orX0192Values.fukushokuSonotaKnj?.value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === OrX0192Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === OrX0192Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  ;(refValue.value.orX0192Values[local.or51775Value] as unknown as Mo00045Type).value =
    setOrAppendValue(
      (refValue.value.orX0192Values[local.or51775Value] as unknown as Mo00045Type).value ?? '',
      data
    )
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

watch(
  () => localOneway.codeListOneway,
  (newVal) => {
    localOneway.mo00040Oneway1.items = newVal.relationshipMasterInfoList
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0012Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    if (newValue?.teikyouId) {
      local.commonInfo = newValue
      localOneway.or55476Oneway.createYmd = newValue.createYmd
      localOneway.or51775Oneway.userId = newValue.userId ?? ''
    }
  },
  { deep: true }
)
</script>

<template>
  <!-- ４．口腔・栄養について -->
  <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
  <div
    v-if="refValue.orX0192Values"
    class="box"
  >
    <div class="mt-6 mb-6">
      <!--摂食方法-->
      <div class="data-cell">
        <p>{{ t('label.feeding-method') }}</p>
        <div class="d-flex">
          <base-mo00018
            v-for="(item, index) in localOneway.feedingDataList"
            :key="index"
            v-model="refValue.orX0192Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: false,
              itemLabel: '',
              checkboxLabel: item.label,
            }"
          />
        </div>
      </div>
      <div class="data-cell">
        <p>{{ t('label.food-allergy') }}</p>
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.orX0192Values.foodAllergy"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
          <div class="d-flex align-center">
            （
            <g-custom-or-x-0157
              v-model="refValue.orX0192Values.foodAllergyKnj"
              class="ml-1"
              :oneway-model-value="localOneway.orX0157Oneway"
              @on-click-edit-btn="handleFoodAllergyKnj"
            ></g-custom-or-x-0157>
            ）
          </div>
        </div>
      </div>
      <!--摂食嚥下機能障害-->
      <div class="data-cell">
        <p>
          {{ t('label.swallowing-dysfunction') }}
        </p>
        <base-mo00039
          v-model="refValue.orX0192Values.sesshokuEngeShougai"
          :oneway-model-value="localOneway.mo00039Oneway"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
            :key="index"
            :name="'radio' + '-' + index"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
      </div>
      <div class="data-cell">
        <p>{{ t('label.fluid-thickened') }}</p>
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.orX0192Values.suibun"
            :oneway-model-value="localOneway.mo00039Oneway"
            @update:model-value="handleSuibun"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
          <div class="d-flex align-center">
            (
            <base-mo00018
              v-for="(item, index) in localOneway.suibunKbnDataList"
              :key="index"
              v-model="refValue.orX0192Values[item.key]"
              :oneway-model-value="
                {
                  name: item.label,
                  hideDetails: true,
                  showItemLabel: false,
                  itemLabel: '',
                  checkboxLabel: item.label,
                } as Mo00018OnewayType
              "
              @update:model-value="handleMo00018"
            />
            )
          </div>
        </div>
      </div>
      <!-- 食形態（主食） -->
      <div class="data-cell">
        <p>{{ t('label.food-form-staple-food') }}</p>
        <div class="d-flex">
          <base-mo00018
            v-for="(item, index) in localOneway.foodDataList"
            :key="index"
            v-model="refValue.orX0192Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: false,
              itemLabel: '',
              checkboxLabel: item.label,
            }"
          />
          <div class="d-flex align-center ml-6">
            (
            <g-custom-or-x-0157
              v-model="refValue.orX0192Values.shushokuSonotaKnj"
              class="ml-1"
              :oneway-model-value="localOneway.orX0157Oneway"
              @on-click-edit-btn="handleShushokuSonotaKnj"
            ></g-custom-or-x-0157>
            )
          </div>
        </div>
      </div>
      <!-- 食形態（副食） -->
      <div class="data-cell">
        <p>{{ t('label.food-form-side-dish') }}</p>
        <div class="d-flex">
          <base-mo00018
            v-for="(item, index) in localOneway.foodFormDataList"
            :key="index"
            v-model="refValue.orX0192Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: false,
              itemLabel: '',
              checkboxLabel: item.label,
            }"
          />
          <div class="d-flex align-center ml-6">
            (
            <g-custom-or-x-0157
              v-model="refValue.orX0192Values.fukushokuSonotaKnj"
              :oneway-model-value="localOneway.orX0157Oneway"
              class="ml-1"
              @on-click-edit-btn="handleFukushokuSonotaKnj"
            ></g-custom-or-x-0157>
            )
          </div>
        </div>
      </div>
      <!-- 義歯使用 -->
      <div class="data-cell">
        <p>{{ t('label.denture-use') }}</p>
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.orX0192Values.gishiUmu"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
          <div class="d-flex align-center">
            (
            <base-mo00039
              v-model="refValue.orX0192Values.gishiKbn"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway
                  .CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY"
                :key="index"
                :name="'radio' + '-' + index"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
            )
          </div>
        </div>
      </div>
      <div class="data-cell">
        <p>{{ t('label.able-to-firmly-chew-with-both-left-and-right-molars') }}</p>
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.orX0192Values.kamiawase"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </div>
      </div>
      <!-- 歯の汚れ -->
      <div class="data-cell">
        <p>{{ t('label.tooth-stains-or-dental-plaque') }}</p>
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.orX0192Values.koukuuCare"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </div>
      </div>

      <!-- 歯肉の腫れ、出血 -->
      <div class="data-cell">
        <p>{{ t('label.gum-swelling-and-bleeding') }}</p>
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.orX0192Values.koukuuCareShiniku"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
          <div class="d-flex align-center ml-6">
            (
            <g-custom-or-x-0157
              v-model="refValue.orX0192Values.koukuuCareShinikuKnj"
              :oneway-model-value="localOneway.orX0157Oneway"
              class="ml-1"
              @on-click-edit-btn="handleKoukuuCareShinikuKnj"
            ></g-custom-or-x-0157>
            )
          </div>
        </div>
      </div>
      <!-- 特記事項（口腔・栄養） -->
      <div class="data-cell">
        <g-custom-or-x-0156
          v-model="refValue.orX0192Values.koukuuEiyoTokkiKnj"
          :oneway-model-value="localOneway.orX0156Oneway"
        ></g-custom-or-x-0156>
      </div>
    </div>
  </div>
  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="local.or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleOr51775Confirm"
  />
</template>

<style scoped lang="scss">
.content-title {
  padding: 14.5px 24px;
  background-color: #e6e6e6;
}
.title {
  padding: 15.5px 12px;
  background-color: #0760e614;
  font-weight: Bold;
  font-size: 14px;
  :deep(.v-btn__content) {
    font-weight: bold;
    color: #214d97; /* 文字颜色 */
  }
}
.box {
  margin: 24px 50px 24px 48px;
}
:deep(.v-selection-control-group) {
  align-items: center !important;
}
:deep(.v-row--no-gutters) {
  margin: 0 !important;
}
.data-cell {
  margin-bottom: 16px;
}

:deep(.v-btn--density-default) {
  height: 100% !important;
  min-height: 0 !important;
}
:deep(.split-line) {
  display: none;
}
.w-982 {
  width: 982px;
}
</style>
