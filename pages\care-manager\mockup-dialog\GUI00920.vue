<script setup lang="ts">
/**
 * GUI00920_アセスメント履歴取込画面
 *
 * @description
 * アセスメント履歴取込画面
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or10355Const } from '~/components/custom-components/organisms/Or10355/Or10355.constants'
import { Or10355Logic } from '~/components/custom-components/organisms/Or10355/Or10355.logic'
import type { Or10355OnewayType, Or10355Type } from '~/types/cmn/business/components/Or10355Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
/**
 * 画面ID
 */
const screenId = 'GUI00920'
/**
 * ルーティング
 */
const routing = 'GUI00920/pinia'
/**
 *  画面物理名
 */
const screenName = 'GUI00920'

/**
 *  画面状態管理用操作変数
 */
const screenStore = useScreenStore()

/**
 * 画面状態管理用操作変数
 */
const or10355 = ref({ uniqueCpId: Or10355Const.CP_ID(1) })

/**************************************************
 * 画面固有処理
 **************************************************/
/**
 *  piniaの画面領域を初期化する
 */
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00920' },
})

/**************************************************
 * Props
 **************************************************/
/**
 * piniaから最上位の画面コンポーネント情報を取得する
 * これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
}
// 子コンポーネントのユニークIDを設定する
or10355.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00920',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or10355Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or10355Const.CP_ID(1)]: or10355.value,
})

/**
 *  ダイアログ表示フラグ
 */
const showDialogOr10355 = computed(() => {
  // Or10355のダイアログ開閉状態
  return Or10355Logic.state.get(or10355.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * Or10030用の単方向バインド値
 */
const or10355OnewayModel: Or10355OnewayType = {
  shisetuId: '1',
  userId: '1',
  svJigyoId: '1',
  syubetsuId: '2',
  menu2Knj: '1',
  menu3Knj: '1',
  valS: '1',
  kikanFlg: '1',
}

/**
 * Or10355コンポーネントの型参照（出力項目付き）
 */
const modelValue = ref<Or10355Type>({
  raiId: '',
})

/**
 *  ボタン押下時の処理
 *
 * @param kikanFlg - システム年月日
 */
function or10355OnClick(kikanFlg: string) {
  or10355OnewayModel.kikanFlg = kikanFlg
  // Or10355のダイアログ開閉状態を更新する
  Or10355Logic.state.set({
    uniqueCpId: or10355.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters>
    <c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or10355OnClick('1')"
        >GUI00920_1-1. 初期表示（退避情報 .期間管理フラグ ：期間管理する）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or10355OnClick('0')"
        >GUI00920_1-2. 初期表示（退避情報 .期間管理フラグ ：期間管理しない）
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-10355
    v-if="showDialogOr10355"
    v-bind="or10355"
    v-model="modelValue"
    :oneway-model-value="or10355OnewayModel"
  />
</template>
