import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or53417:有機体:モーダル（アセスメント（インターライ）マスタモーダル））
 * 静的データ
 */
export namespace Or53417Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or53417', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
  }
}
