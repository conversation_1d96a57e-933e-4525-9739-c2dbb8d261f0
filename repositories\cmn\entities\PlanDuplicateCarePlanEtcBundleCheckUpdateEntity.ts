import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 更新入力エンティティ
 */
export interface PlanDuplicateCarePlanEtcBundleCheckUpdateInEntity extends InWebEntity {
  /** 複写元事業者ID */
  svJigyoIdFrom: string
  /** 複写先事業者ID */
  svJigyoIdTo: string
  /** 職員ID */
  shokuinId?: string
  /** システムコード */
  gsysCd?: string
  /** 機能情報List */
  gdsKinouName?: string[]
}

/**
 * 更新出力エンティティ
 */
export interface PlanDuplicateCarePlanEtcBundleCheckUpdateOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: PlanDuplicateCarePlanEtcBundleCheckUpdateDataEntity
}

/**
 * 更新データエンティティ
 */
export interface PlanDuplicateCarePlanEtcBundleCheckUpdateDataEntity {
  /** チェック結果 */
  chkResult: string
  /** メッセージID */
  messageId: string
  /** 介護予防支援複写可能 */
  copyKyc: string
}
