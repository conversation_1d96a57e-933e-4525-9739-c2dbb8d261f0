<script setup lang="ts">
/**
 * Or31369：有機体：アセスメント(インターライ)画面B
 * GUI00765_アセスメント(インターライ)画面B
 *
 * @description
 * アセスメント(インターライ)画面B
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or31369Const } from '../Or31369/Or31369.constants'
import { Or31124Const } from '../Or31124/Or31124.constants'
import { TeX0003Const } from '../../template/Tex0003/TeX0003.constants'
import type { Or31369StateType, SubInfoB } from './Or31369.type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { useSetupChildProps, useScreenOneWayBind, useCmnCom, useScreenStore } from '#imports'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { ResBodyStatusCode } from '~/constants/api-constants'
import type {
  IAssessmentInterRAIBInEntity,
  IAssessmentInterRAIBOutEntity,
  IAssessmentInterRAIBUpdateInEntity,
  IAssessmentInterRAIBUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIBEntity'
import type { TransmitParam } from '~/types/cmn/business/components/TeX0003Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type {
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
  SubInfoBEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import { Or30981Const } from '~/components/custom-components/organisms/Or30981/Or30981.constants'
import type { Or30981OnewayType, Or30981Type } from '~/types/cmn/business/components/Or30981Type'
import type { OrX0156OnewayType, OrX0156Type } from '~/types/cmn/business/components/OrX0156Type'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}
const props: Props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/

// 子コンポーネント用変数
const or51775 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or30981_1 = ref({ uniqueCpId: '' })
const or30981_2 = ref({ uniqueCpId: '' })
const or30981_3 = ref({ uniqueCpId: '' })
const or30981_4 = ref({ uniqueCpId: '' })
const or30981_5 = ref({ uniqueCpId: '' })
const or30981_6 = ref({ uniqueCpId: '' })
const or30981_7 = ref({ uniqueCpId: '' })
const or30981_8 = ref({ uniqueCpId: '' })

/**
 * emit saveEndイベントを定義
 */
const emit = defineEmits(['saveEnd'])

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()

/** システム共有情報取得 */
const commonInfo: TransmitParam = reactive({
  executeFlag: '',
  deleteBtnValue: '',
  kikanKanriFlg: '',
  kijunbiYmd: '',
  sakuseiId: '',
  historyInfo: {} as HistoryInfoEntity,
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  raiId: '',
  assType: '',
  yokaiKbn: '',
  subInfoB: {} as SubInfoBEntity,
  syubetsuId: '',
  svJigyoKnj: '',
  updateKbn: '',
  historyUpdateKbn: '',
  tableCreateKbn: '',
  svJigyoId: '',
  userId: '',
})

const defaultOneway = reactive({
  // サブ情報（B）初期値
  subInfoB: {
    raiId: '',
    b1MemoKnj: '',
    b1MemoFont: '',
    b1MemoColor: '',
    b1: '',
    b2Ymd: '',
    b3Knj: '',
    b4Knj: '',
    b5MemoKnj: '',
    b5MemoFont: '',
    b5MemoColor: '',
    b5A: '',
    b5B: '',
    b5C: '',
    b5D: '',
    b5E: '',
    b5F: '',
    b6MemoKnj: '',
    b6MemoFont: '',
    b6MemoColor: '',
    b6A: '',
    b6B: '',
    b7MemoKnj: '',
    b7MemoFont: '',
    b7MemoColor: '',
    b7: '',
    b8MemoKnj: '',
    b8MemoFont: '',
    b8MemoColor: '',
    b8: '',
    b9MemoKnj: '',
    b9MemoFont: '',
    b9MemoColor: '',
    b9: '',
    b10MemoKnj: '',
    b10MemoFont: '',
    b10MemoColor: '',
    b10: '',
    b11MemoKnj: '',
    b11MemoFont: '',
    b11MemoColor: '',
    b11: '',
    modifiedCnt: '0',
  } as SubInfoB,
  // 調査アセスメント種別ラベル
  mo01338SurveyAssessmentTypeOneway: {
    value: '',
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'title-h5 background-transparent pb-1',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 相談受付表タイトル
  mo01338BOneway: {
    value: t('label.assessment-interRAI-tab-B-section-title'),
    valueFontWeight: 'bold',
    customClass: { outerClass: 'pa-6 pr-4', itemStyle: 'font-size: 18px' } as CustomClass,
  } as Mo01338OnewayType,
  // 注意事項ラベル
  mo01338MattersToBeAttentionOneway: {
    value: t('label.assessment-interRAI-tab-B-section-attention-matter'),
    valueFontWeight: 'bold',
    customClass: { outerClass: 'pa-6 pl-0', itemStyle: 'font-size: 18px' } as CustomClass,
  } as Mo01338OnewayType,
  // 入所に対して本人の意思度合タイトル
  mo01338B1Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B1-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *B1．入所に対して本人の意思度合メモテキストエリア
   */
  or30981OnewayTypeB1: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: Or31369Const.MAX_ROW_3,
      rows: Or31369Const.MAX_ROW_3,
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 入所に対して本人の意思度合_リスト
  admissionPersonDegreeOfIntentionList: [] as CodeType[],
  // ラジオボタン
  mo00039Oneway: {
    name: '',
    showItemLabel: false,
    inline: true,
    checkOff: true,
  } as Mo00039OnewayType,
  // 受付日タイトル
  mo01338B2Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B2-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 受付日
  mo00020Oneway: {
    showItemLabel: false,
    totalWidth: '130px',
    maxlength: '10',
  } as Mo00020OnewayType,
  // 相談受付時までの経過タイトル
  mo01338B3Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B3-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * B3．相談受付時までの経過メモテキストエリア
   */
  orX0156OnewayTypeB3: {
    showItemLabel: false,
    showDividerLineFlg: false,
    showEditBtnFlg: true,
    isVerticalLabel: true,
    customClass: {
      labelClass: 'pa-0',
    } as CustomClass,
    rows: Or31369Const.MAX_ROW_9,
    maxRows: Or31369Const.MAX_ROW_9,
    noResize: true,
    autoGrow: false,
    disabled: false,
    maxlength: Or31369Const.CARE_MAX_LENGTH,
  } as OrX0156OnewayType,
  // 相談受付内容タイトル
  mo01338B4Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B4-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * B4．相談受付内容メモテキストエリア
   */
  orX0156OnewayTypeB4: {
    showItemLabel: false,
    showDividerLineFlg: false,
    showEditBtnFlg: true,
    isVerticalLabel: true,
    customClass: {
      labelClass: 'pa-0',
    } as CustomClass,
    rows: Or31369Const.MAX_ROW_9,
    maxRows: Or31369Const.MAX_ROW_9,
    noResize: true,
    autoGrow: false,
    disabled: false,
    maxlength: Or31369Const.CARE_MAX_LENGTH,
  } as OrX0156OnewayType,
  // 過去5年間の利用歴タイトル
  mo01338B5Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B5-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *B5．過去5年間の利用歴メモテキストエリア
   */
  or30981OnewayTypeB5: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: Or31369Const.MAX_ROW_3,
      rows: Or31369Const.MAX_ROW_3,
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 「過去5年間の利用歴回答」の選択肢
  past5YearlyUsageHistoryResponseSelectItems: [] as CodeType[],
  // a. 介護施設、療養病院/病棟ラベル
  mo01338B5AOneway: {
    value: t('label.assessment-interRAI-tab-B-section-B5-label-a'),
  } as Mo01338OnewayType,
  // b. 認知症対応型共同生活介護、小規模多機能型居宅介護ラベル
  mo01338B5BOneway: {
    value: t('label.assessment-interRAI-tab-B-section-B5-label-b'),
  } as Mo01338OnewayType,
  // c. 高齢者住宅-有料老人ホーム(特定施設入居者生活介護有り·無し含む)ラベル
  mo01338B5COneway: {
    value: t('label.assessment-interRAI-tab-B-section-B5-label-c'),
  } as Mo01338OnewayType,
  // d. 精神科病院、精神科病棟ラベル
  mo01338B5DOneway: {
    value: t('label.assessment-interRAI-tab-B-section-B5-label-d'),
  } as Mo01338OnewayType,
  // e. 精神障害者施設ラベル
  mo01338B5EOneway: {
    value: t('label.assessment-interRAI-tab-B-section-B5-label-e'),
  } as Mo01338OnewayType,
  // f. 知的障害者施設
  mo01338B5FOneway: {
    value: t('label.assessment-interRAI-tab-B-section-B5-label-f'),
  } as Mo01338OnewayType,
  // 入所直前と通常の居住場所タイトル
  mo01338B6Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B6-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *B6．入所直前と通常の居住場所メモテキストエリア
   */
  or30981OnewayTypeB6: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: Or31369Const.MAX_ROW_3,
      rows: Or31369Const.MAX_ROW_3,
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 「入所直前と通常の居住場所」の選択肢
  residenceLocationSelectItems: [] as CodeType[],
  // a.入所直前の居住場所ラベル
  mo01338B6AOneway: {
    value: t('label.assessment-interRAI-tab-B-section-B6-label-a'),
    customClass: { outerClass: '' } as CustomClass,
  } as Mo01338OnewayType,
  // b.通常の居住場所ラベル
  mo01338B6BOneway: {
    value: t('label.assessment-interRAI-tab-B-section-B6-label-b'),
    customClass: { outerClass: 'pt-2' } as CustomClass,
  } as Mo01338OnewayType,
  // 入所前の同居形態タイトル
  mo01338B7Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B7-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *B7．入所前の同居形態メモテキストエリア
   */
  or30981OnewayTypeB7: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: Or31369Const.MAX_ROW_3,
      rows: Or31369Const.MAX_ROW_3,
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 入所前の同居形態_リスト
  cohabitFormList: [] as CodeType[],
  // 精神疾患歴タイトル
  mo01338B8Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B8-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *B8．精神疾患歴メモテキストエリア
   */
  or30981OnewayTypeB8: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: Or31369Const.MAX_ROW_3,
      rows: Or31369Const.MAX_ROW_3,
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 精神疾患歴_リスト
  mentalIllnessHistoryList: [] as CodeType[],
  // 教育歴タイトル
  mo01338B9Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B9-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *B9．教育歴メモテキストエリア
   */
  or30981OnewayTypeB9: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: Or31369Const.MAX_ROW_3,
      rows: Or31369Const.MAX_ROW_3,
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 教育歴_リスト
  educationalHistoryList: [] as CodeType[],
  // 医療機関受診時の送迎タイトル
  mo01338B10Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B10-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *B10．医療機関受診時の送迎メモテキストエリア
   */
  or30981OnewayTypeB10: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: Or31369Const.MAX_ROW_3,
      rows: Or31369Const.MAX_ROW_3,
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 医療機関受診時の送迎_リスト
  shuttleList: [] as CodeType[],
  // 受診中の付き添いが必要タイトル
  mo01338B11Oneway: {
    value: t('label.assessment-interRAI-tab-B-section-B11-title'),
    valueFontWeight: 'bold',
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: 'font-size: 16px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   *B11．受診中の付き添いが必要メモテキストエリア
   */
  or30981OnewayTypeB11: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: Or31369Const.MAX_ROW_3,
      rows: Or31369Const.MAX_ROW_3,
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  // 受診中の付き添いが必要回答_リスト
  attendantNecessaryResponseList: [] as CodeType[],
  /**
   * GUI00937_入力支援［ケアマネ］
   */
  or51775Oneway: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
})
/** 入力フォーム表示フラグ */
const inputFormDisplayFlag = ref(true)
/** サブ情報 */
const respInfo = {
  subInfoB: {
    raiId: '',
    b1MemoKnj: '',
    b1MemoFont: '',
    b1MemoColor: '',
    b1: '',
    b2Ymd: '',
    b3Knj: '',
    b4Knj: '',
    b5MemoKnj: '',
    b5MemoFont: '',
    b5MemoColor: '',
    b5A: '',
    b5B: '',
    b5C: '',
    b5D: '',
    b5E: '',
    b5F: '',
    b6MemoKnj: '',
    b6MemoFont: '',
    b6MemoColor: '',
    b6A: '',
    b6B: '',
    b7MemoKnj: '',
    b7MemoFont: '',
    b7MemoColor: '',
    b7: '',
    b8MemoKnj: '',
    b8MemoFont: '',
    b8MemoColor: '',
    b8: '',
    b9MemoKnj: '',
    b9MemoFont: '',
    b9MemoColor: '',
    b9: '',
    b10MemoKnj: '',
    b10MemoFont: '',
    b10MemoColor: '',
    b10: '',
    b11MemoKnj: '',
    b11MemoFont: '',
    b11MemoColor: '',
    b11: '',
    modifiedCnt: Or31369Const.UPDATE_NUMBER_OF_TIMES_0,
  } as SubInfoB,
}
const inputItemDisabled = ref<boolean>(false)

const local = reactive({
  // 入所に対して本人の意思度合区分
  mo00039B1: '',
  // 受付日
  mo00020: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // 利用歴介護施設区分
  b5A: '',
  // 利用歴認知症対応型共同生活介護区分
  b5B: '',
  // 利用歴高齢者住宅－有料老人ホーム区分
  b5C: '',
  // 利用歴精神科病院区分
  b5D: '',
  // 利用歴精神障害者施設区分
  b5E: '',
  // 利用歴知的障害者施設区分
  b5F: '',
  // 入所直前の居住場所区分
  b6A: '',
  // 通常の居住場所区分
  b6B: '',
  // 入所前の同居形態区分
  mo00039B7: '',
  // 精神疾患歴回答
  mo00039B8: '',
  // 教育歴区分
  mo00039B9: '',
  // 医療機関受診時の送迎区分
  mo00039B10: '',
  // 受診中の付き添いが必要回答
  mo00039B11: '',
  // 更新区分
  updateCategory: UPDATE_KBN.NONE,
  /**
   * アセスメントID
   */
  raiId: '',
  /**
   * 画面項目区分
   */
  vieTypeKbn: '',
})

/**
 * B1．入所に対して本人の意思度合 メモテキストエリア入力値
 */
const or30981TypeB1Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * B3．相談受付時までの経過 メモテキストエリア入力値
 */
const orX0156TypeB3Area = ref<OrX0156Type>({ value: '' })
/**
 * B4．相談受付内容 メモテキストエリア入力値
 */
const orX0156TypeB4Area = ref<OrX0156Type>({ value: '' })
/**
 * B5．過去5年間の利用歴 メモテキストエリア入力値
 */
const or30981TypeB5Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * B6．入所直前と通常の居住場所 メモテキストエリア入力値
 */
const or30981TypeB6Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * B7．入所前の同居形態 メモテキストエリア入力値
 */
const or30981TypeB7Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * B8．精神疾患歴 メモテキストエリア入力値
 */
const or30981TypeB8Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * B9．教育歴 メモテキストエリア入力値
 */
const or30981TypeB9Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * B10．医療機関受診時の送迎 メモテキストエリア入力値
 */
const or30981TypeB10Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)
/**
 * B11．受診中の付き添いが必要 メモテキストエリア入力値
 */
const or30981TypeB11Area = ref<Or30981Type>({
  content: '',
  fontSize: '12',
  fontColor: '#000000',
} as Or30981Type)

// GUI00937_入力支援［ケアマネ］の双方向バインドModelValue
const or51775Type = ref({
  modelValue: '',
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Emit
 **************************************************/

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or31369StateType>({
  cpId: Or31369Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (value) {
        commonInfo.kikanKanriFlg = value.kikanKanriFlg
        commonInfo.planPeriodInfo = value.planPeriodInfo
        commonInfo.historyInfo = value.historyInfo
        commonInfo.sakuseiId = value.sakuseiId
        commonInfo.kijunbiYmd = value.kijunbiYmd
        commonInfo.raiId = value.raiId
        commonInfo.historyUpdateKbn = value.historyUpdateKbn
        commonInfo.svJigyoKnj = value.svJigyoKnj
        commonInfo.syubetsuId = value.syubetsuId
        commonInfo.tableCreateKbn = value.tableCreateKbn
        commonInfo.assType = value.assType
      }
      initControls()
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          void save()
          break
        // 新規
        case 'add':
          local.updateCategory = UPDATE_KBN.CREATE
          break
        // 複写
        case 'copy':
          copy()
          break
        // 削除
        case 'delete':
          del()
          break
        // データ再取得
        case 'getData':
          void getSubInfo(commonInfo.historyInfo.raiId)
          break
        default:
          break
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or30981Const.CP_ID(1)]: or30981_1.value,
  [Or30981Const.CP_ID(2)]: or30981_2.value,
  [Or30981Const.CP_ID(3)]: or30981_3.value,
  [Or30981Const.CP_ID(4)]: or30981_4.value,
  [Or30981Const.CP_ID(5)]: or30981_5.value,
  [Or30981Const.CP_ID(6)]: or30981_6.value,
  [Or30981Const.CP_ID(7)]: or30981_7.value,
  [Or30981Const.CP_ID(8)]: or30981_8.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// GUI00787 ［メモ入力］画面表示フラグ
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

onMounted(() => {
  // 初期情報取得
  init()
  useScreenStore().setCpNavControl({
    cpId: Or31369Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    editFlg: true,
  })
})

const init = () => {
  // 期間管理フラグが「1:管理する」、かつ、計画期間が登録されていない場合
  if (commonInfo.kikanKanriFlg === Or31369Const.PERIOD_MANAGE_FLAG_1 && commonInfo.planPeriodInfo) {
    inputFormDisplayFlag.value = false
    return
  }
}

/**
 * 各「メモ入力支援アイコンボタン」押下
 *
 * @param type - 画面項目区分
 */
const clickMemoInputCategoryIcon = (
  type: 'B1' | 'B3' | 'B4' | 'B5' | 'B6' | 'B7' | 'B8' | 'B9' | 'B10' | 'B11'
) => {
  local.vieTypeKbn = type
  defaultOneway.or51775Oneway = {
    // タイトル
    title: t('label.memo'),
    // 画面ID
    screenId: Or31369Const.STR.SCREEN_ID,
    // 分類ID
    bunruiId: Or31369Const.STR.EMPTY,
    // 大分類ＣＤ
    t1Cd: Or31369Const.STR.T1_CD,
    // 中分類CD
    t2Cd: Or31369Const.STR.T2_CD,
    // 小分類ＣＤ
    t3Cd: Or31369Const.STR.T3_CD_0,
    // テーブル名
    tableName: Or31369Const.STR.TABLE_NAME,
    // カラム名
    columnName: Or31369Const.STR.EMPTY,
    // アセスメント方式
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? Or31369Const.STR.EMPTY,
    // 文章内容
    inputContents: Or31369Const.STR.EMPTY,
    // 利用者ID
    userId: systemCommonsStore.getUserSelectSelfId(),
    // モード
    mode: Or31369Const.STR.EMPTY,
  } as Or51775OnewayType

  switch (type) {
    // B1．入所に対して本人の意思度合
    case 'B1':
      defaultOneway.or51775Oneway.columnName = Or31369Const.STR.B1_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeB1Area.value.content ?? Or31369Const.STR.EMPTY
      break
    // B3．相談受付時までの経過タイトル
    case 'B3':
      defaultOneway.or51775Oneway.title = Or31369Const.STR.B3_TITLE
      defaultOneway.or51775Oneway.t3Cd = Or31369Const.STR.T3_CD_1
      defaultOneway.or51775Oneway.columnName = Or31369Const.STR.B3_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        orX0156TypeB3Area.value.value ?? Or31369Const.STR.EMPTY
      break
    // B4．相談受付内容
    case 'B4':
      defaultOneway.or51775Oneway.title = Or31369Const.STR.B4_TITLE
      defaultOneway.or51775Oneway.t3Cd = Or31369Const.STR.T3_CD_2
      defaultOneway.or51775Oneway.columnName = Or31369Const.STR.B4_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        orX0156TypeB4Area.value.value ?? Or31369Const.STR.EMPTY
      break
    // B5．過去5年間の利用歴
    case 'B5':
      defaultOneway.or51775Oneway.columnName = Or31369Const.STR.B5_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeB5Area.value.content ?? Or31369Const.STR.EMPTY
      break
    // B6．入所直前と通常の居住場所
    case 'B6':
      defaultOneway.or51775Oneway.columnName = Or31369Const.STR.B6_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeB6Area.value.content ?? Or31369Const.STR.EMPTY
      break
    // B7．入所前の同居形態
    case 'B7':
      defaultOneway.or51775Oneway.columnName = Or31369Const.STR.B7_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeB7Area.value.content ?? Or31369Const.STR.EMPTY
      break
    // B8．精神疾患歴
    case 'B8':
      defaultOneway.or51775Oneway.columnName = Or31369Const.STR.B8_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeB8Area.value.content ?? Or31369Const.STR.EMPTY
      break
    // B9．教育歴
    case 'B9':
      defaultOneway.or51775Oneway.columnName = Or31369Const.STR.B9_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeB9Area.value.content ?? Or31369Const.STR.EMPTY
      break
    // B10．医療機関受診時の送迎
    case 'B10':
      defaultOneway.or51775Oneway.columnName = Or31369Const.STR.B10_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeB10Area.value.content ?? Or31369Const.STR.EMPTY
      break
    // B11．受診中の付き添いが必要
    case 'B11':
      defaultOneway.or51775Oneway.columnName = Or31369Const.STR.B11_COLUMN_NAME
      defaultOneway.or51775Oneway.inputContents =
        or30981TypeB11Area.value.content ?? Or31369Const.STR.EMPTY
      break
  }
  // GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    // 入所に対して本人の意思度合の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DEGREE_OF_INTENTION },
    // 過去5年間の利用歴回答の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_USAGE_HISTORY },
    // 入所直前と通常の居住場所の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_RESIDENCE_LOCATION },
    // 入所前の同居形態の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_COHABIT_FORM },
    // 精神疾患歴の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MENTAL_ILLNESS_HISTORY },
    // 教育歴の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_EDUCATIONAL_HISTORY },
    // 医療機関受診時の送迎の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SHUTTLE },
    // 受診中の付き添いが必要回答の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ATTENDANT_NECESSARY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // アセスメント種別の選択肢
  const assessmentKindSelectItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )

  // 親情報.調査アセスメント種別により、コードマスタの区分名称を表示する
  defaultOneway.mo01338SurveyAssessmentTypeOneway.value =
    assessmentKindSelectItems?.find((item) => commonInfo.historyInfo.assType === item.value)
      ?.label ?? Or31369Const.STR.EMPTY

  // 入所に対して本人の意思度合の選択肢
  const degreeOfIntentionSelectItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DEGREE_OF_INTENTION
  )
  defaultOneway.admissionPersonDegreeOfIntentionList = sortList(degreeOfIntentionSelectItems)

  // 過去5年間の利用歴回答の選択肢
  defaultOneway.past5YearlyUsageHistoryResponseSelectItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_USAGE_HISTORY
  )

  // 入所直前と通常の居住場所の選択肢
  const residenceLocationSelectItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_RESIDENCE_LOCATION
  )
  defaultOneway.residenceLocationSelectItems = sortList(residenceLocationSelectItems)

  // 入所前の同居形態の選択肢
  const cohabitFormSelectItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_COHABIT_FORM
  )
  defaultOneway.cohabitFormList = sortList(cohabitFormSelectItems)

  // 精神疾患歴の選択肢
  defaultOneway.mentalIllnessHistoryList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MENTAL_ILLNESS_HISTORY
  )

  // 教育歴の選択肢
  const educationalHistorySelectItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_EDUCATIONAL_HISTORY
  )
  defaultOneway.educationalHistoryList = sortList(educationalHistorySelectItems)

  // 医療機関受診時の送迎の選択肢
  const shuttleSelectItems = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_SHUTTLE)
  defaultOneway.shuttleList = sortList(shuttleSelectItems)

  // 受診中の付き添いが必要回答の選択肢
  const attendantNecessarySelectItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ATTENDANT_NECESSARY
  )
  defaultOneway.attendantNecessaryResponseList = sortList(attendantNecessarySelectItems)
}

/**
 * 汎用コードマスタデータをソートする
 *
 * @param targetList - ソート対象リスト
 */
const sortList = (targetList: CodeType[]): CodeType[] => {
  if (!targetList || targetList.length === 0) {
    return []
  }
  const sortedList = [...targetList].sort((a, b) => {
    const valueA = parseInt(a.value, 10)
    const valueB = parseInt(b.value, 10)
    return valueA - valueB
  })

  const index = Math.floor(sortedList.length / 2)
  const firstHalf = sortedList.slice(0, index)
  const secondHalf = sortedList.slice(index)
  const list: CodeType[] = []

  for (let i = 0; i < firstHalf.length; i++) {
    list.push({ ...firstHalf[i] })
    list.push({ ...secondHalf[i] })
  }
  // secondHalf が firstHalf よりも1つの項目多い場合、その余分な項目を新しい配列に追加する
  if (secondHalf.length > firstHalf.length) {
    list.push({ ...secondHalf[secondHalf.length - 1] })
  }

  return list
}

/**
 * デフォルト文字サイズ
 */
const getDefaultFontSize = (): string => {
  // 初期設定マスタ.アセスメント(インターライ)：文字サイズ
  const fontSize = cmnRouteCom.getInitialSettingMaster()?.assCharSize
  // 文字サイズが「0：小さい」の場合
  if ('0' === fontSize) {
    return '9'
  }
  // 文字サイズが「2：大きい」の場合
  else if ('2' === fontSize) {
    return '15'
  }
  // 上記以外の場合
  else {
    // 12：デフォルト値
    return '12'
  }
}

/**
 * デフォルト文字色
 */
const getDefaultFontColor = (): string => {
  // 初期設定マスタ.アセスメント(インターライ)：文字色
  const textColor = cmnRouteCom.getInitialSettingMaster()?.assCharColor
  // 初期設定マスタ.アセスメント(インターライ).文字サイズがある場合
  if (textColor) {
    // 初期設定マスタ.アセスメント(インターライ).文字色
    return textColor
  }
  // 上記以外の場合
  else {
    // #000000：デフォルト値
    return '0'
  }
}

/**
 * AC003_「保存ボタン」押下
 */
const save = async () => {
  // 画面入力データに変更がない場合
  if (!screenInputDataChangejudgement(respInfo.subInfoB)) {
    // 処理終了にする
    return
  }
  void (await onSave())
}

/**
 * 画面入力データに変更判定（true：変更あり; false：変更なし）
 *
 * @param subInfoB - サブ情報
 */
const screenInputDataChangejudgement = (subInfoB: SubInfoB) => {
  // B1.サブセクション
  // メモ
  if (
    or30981TypeB1Area.value.content !== subInfoB.b1MemoKnj ||
    or30981TypeB1Area.value.fontSize !== subInfoB.b1MemoFont ||
    or30981TypeB1Area.value.fontColor !== subInfoB.b1MemoColor
  ) {
    return true
  }
  // 入所に対して本人の意思度合
  if (local.mo00039B1 !== subInfoB.b1) {
    return true
  }
  // B2.サブセクション
  // 受付日
  if (local.mo00020.value !== subInfoB.b2Ymd) {
    return true
  }
  // B3.サブセクション
  // 相談受付時までの経過
  if (orX0156TypeB3Area.value.value !== subInfoB.b3Knj) {
    return true
  }
  // B4.サブセクション
  // 相談受付内容
  if (orX0156TypeB4Area.value.value !== subInfoB.b4Knj) {
    return true
  }
  // B5.サブセクション
  // メモ
  if (
    or30981TypeB5Area.value.content !== subInfoB.b5MemoKnj ||
    or30981TypeB5Area.value.fontSize !== subInfoB.b5MemoFont ||
    or30981TypeB5Area.value.fontColor !== subInfoB.b5MemoColor
  ) {
    return true
  }
  // 利用歴介護施設区分、利用歴認知症対応型共同生活介護区分、利用歴高齢者住宅－有料老人ホーム区分
  // 利用歴精神科病院区分、利用歴精神障害者施設区分、利用歴知的障害者施設区分
  if (
    local.b5A !== subInfoB.b5A ||
    local.b5B !== subInfoB.b5B ||
    local.b5C !== subInfoB.b5C ||
    local.b5D !== subInfoB.b5D ||
    local.b5E !== subInfoB.b5E ||
    local.b5F !== subInfoB.b5F
  ) {
    return true
  }
  // B6.サブセクション
  // メモ
  if (
    or30981TypeB6Area.value.content !== subInfoB.b6MemoKnj ||
    or30981TypeB6Area.value.fontSize !== subInfoB.b6MemoFont ||
    or30981TypeB6Area.value.fontColor !== subInfoB.b6MemoColor
  ) {
    return true
  }
  // 入所直前の居住場所区分、通常の居住場所区分
  if (local.b6A !== subInfoB.b6A || local.b6B !== subInfoB.b6B) {
    return true
  }
  // B7.サブセクション
  // メモ
  if (
    or30981TypeB7Area.value.content !== subInfoB.b7MemoKnj ||
    or30981TypeB7Area.value.fontSize !== subInfoB.b7MemoFont ||
    or30981TypeB7Area.value.fontColor !== subInfoB.b7MemoColor
  ) {
    return true
  }
  // 入所前の同居形態区分
  if (local.mo00039B7 !== subInfoB.b7) {
    return true
  }
  // B8.サブセクション
  // メモ
  if (
    or30981TypeB8Area.value.content !== subInfoB.b8MemoKnj ||
    or30981TypeB8Area.value.fontSize !== subInfoB.b8MemoFont ||
    or30981TypeB8Area.value.fontColor !== subInfoB.b8MemoColor
  ) {
    return true
  }
  // 精神疾患歴回答
  if (local.mo00039B8 !== subInfoB.b8) {
    return true
  }
  // B9.サブセクション
  // メモ
  if (
    or30981TypeB9Area.value.content !== subInfoB.b9MemoKnj ||
    or30981TypeB9Area.value.fontSize !== subInfoB.b9MemoFont ||
    or30981TypeB9Area.value.fontColor !== subInfoB.b9MemoColor
  ) {
    return true
  }
  // 教育歴区分
  if (local.mo00039B9 !== subInfoB.b9) {
    return true
  }
  // B10.サブセクション
  // メモ
  if (
    or30981TypeB10Area.value.content !== subInfoB.b10MemoKnj ||
    or30981TypeB10Area.value.fontSize !== subInfoB.b10MemoFont ||
    or30981TypeB10Area.value.fontColor !== subInfoB.b10MemoColor
  ) {
    return true
  }
  // 医療機関受診時の送迎区分
  if (local.mo00039B10 !== subInfoB.b10) {
    return true
  }
  // B11.サブセクション
  // メモ
  if (
    or30981TypeB11Area.value.content !== subInfoB.b11MemoKnj ||
    or30981TypeB11Area.value.fontSize !== subInfoB.b11MemoFont ||
    or30981TypeB11Area.value.fontColor !== subInfoB.b11MemoColor
  ) {
    return true
  }
  // 受診中の付き添いが必要回答
  if (local.mo00039B11 !== subInfoB.b11) {
    return true
  }

  return false
}

/**
 * アセスメント(インターライ)画面B情報を保存
 */
const onSave = async () => {
  // 入力値処理です
  const inputData: IAssessmentInterRAIBUpdateInEntity = {
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    sectionName: TeX0003Const.SECTION_NAME,
    gsyscd: systemCommonsStore.getSystemCode ?? '',
    kinouId: systemCommonsStore.getFunctionId ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    kikanKanriFlg: commonInfo.kikanKanriFlg,
    historyNo: commonInfo.historyInfo.krirekiNo,
    svJigyoName: commonInfo.svJigyoKnj,
    periodNo: commonInfo.planPeriodInfo.periodNo,
    startYmd: commonInfo.kikanKanriFlg === '1' ? commonInfo.planPeriodInfo.startYmd : '',
    endYmd: commonInfo.kikanKanriFlg === '1' ? commonInfo.planPeriodInfo.endYmd : '',
    index: TeX0003Const.STR.TWO,
    tabName: 'B',
    edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
    edocumentUseParam: useCmnCom().getEDocumentParam(),
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    syubetsuId: commonInfo.syubetsuId,
    subKbn: TeX0003Const.TAB.B,
    updateKbn: local.updateCategory,
    historyUpdateKbn: commonInfo.historyUpdateKbn,
    tableCreateKbn: commonInfo.tableCreateKbn,
    sc1Id: commonInfo.historyInfo.sc1Id,
    raiId: commonInfo.historyInfo.raiId,
    kijunbiYmd: commonInfo.kijunbiYmd,
    sakuseiId: commonInfo.sakuseiId,
    assType: commonInfo.assType,
    subInfoB: {
      raiId: commonInfo.historyInfo.raiId,
      b1MemoKnj: or30981TypeB1Area.value.content,
      b1MemoFont: or30981TypeB1Area.value.fontSize,
      b1MemoColor: or30981TypeB1Area.value.fontColor,
      b1: local.mo00039B1,
      b2Ymd: local.mo00020.value,
      b3Knj: orX0156TypeB3Area.value.value,
      b4Knj: orX0156TypeB4Area.value.value,
      b5MemoKnj: or30981TypeB5Area.value.content,
      b5MemoFont: or30981TypeB5Area.value.fontSize,
      b5MemoColor: or30981TypeB5Area.value.fontColor,
      b5A: local.b5A,
      b5B: local.b5B,
      b5C: local.b5C,
      b5D: local.b5D,
      b5E: local.b5E,
      b5F: local.b5F,
      b6MemoKnj: or30981TypeB6Area.value.content,
      b6MemoFont: or30981TypeB6Area.value.fontSize,
      b6MemoColor: or30981TypeB6Area.value.fontColor,
      b6A: local.b6A,
      b6B: local.b6B,
      b7MemoKnj: or30981TypeB7Area.value.content,
      b7MemoFont: or30981TypeB7Area.value.fontSize,
      b7MemoColor: or30981TypeB7Area.value.fontColor,
      b7: local.mo00039B7,
      b8MemoKnj: or30981TypeB8Area.value.content,
      b8MemoFont: or30981TypeB8Area.value.fontSize,
      b8MemoColor: or30981TypeB8Area.value.fontColor,
      b8: local.mo00039B8,
      b9MemoKnj: or30981TypeB9Area.value.content,
      b9MemoFont: or30981TypeB9Area.value.fontSize,
      b9MemoColor: or30981TypeB9Area.value.fontColor,
      b9: local.mo00039B9,
      b10MemoKnj: or30981TypeB10Area.value.content,
      b10MemoFont: or30981TypeB10Area.value.fontSize,
      b10MemoColor: or30981TypeB11Area.value.fontColor,
      b10: local.mo00039B10,
      b11MemoKnj: or30981TypeB11Area.value.content,
      b11MemoFont: or30981TypeB10Area.value.fontSize,
      b11MemoColor: or30981TypeB11Area.value.fontColor,
      b11: local.mo00039B11,
    } as SubInfoB,
  }

  const resData: IAssessmentInterRAIBUpdateOutEntity = await ScreenRepository.update(
    'assessmentInterRAIBUpdate',
    inputData
  )
  // 保存成功の場合
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    emit('saveEnd', resData.errKbn)
    // 更新区分を「U：更新」に設定
    local.updateCategory = UPDATE_KBN.UPDATE
  }
}

const initControls = () => {
  // 調査アセスメント種別が1:居宅版または3:高齢者住宅版の場合
  if (
    commonInfo.historyInfo.assType === Or31369Const.SURVEY_ASSESSMENT_KIND_1 ||
    commonInfo.historyInfo.assType === Or31369Const.SURVEY_ASSESSMENT_KIND_3
  ) {
    // 入力項目B1、B6、B7、B8 非活性表示
    inputItemDisabled.value = true
  } else {
    // 入力項目B1、B6、B7、B8 活性表示
    inputItemDisabled.value = false
  }
  defaultOneway.or30981OnewayTypeB1.disabled = inputItemDisabled.value
  defaultOneway.or30981OnewayTypeB6.disabled = inputItemDisabled.value
  defaultOneway.or30981OnewayTypeB7.disabled = inputItemDisabled.value
  defaultOneway.or30981OnewayTypeB8.disabled = inputItemDisabled.value
}

/**
 * サブ情報B初期情報を取得
 *
 * @param raiId - アセスメントID
 */
const getSubInfo = async (raiId: string) => {
  // バックエンドAPIから初期情報取得
  const inputData: IAssessmentInterRAIBInEntity = {
    // 親情報.アセスメントID
    raiId: raiId,
  }
  const assessmentInterRAIBResp: IAssessmentInterRAIBOutEntity = await ScreenRepository.select(
    'assessmentInterRAIBSelect',
    inputData
  )
  if (
    ResBodyStatusCode.SUCCESS === assessmentInterRAIBResp.statusCode &&
    assessmentInterRAIBResp.data
  ) {
    // 取得したサブ情報（B）の各項目を画面の入力フォームへセットする
    respInfo.subInfoB = assessmentInterRAIBResp.data.subInfoB
  }
  if (respInfo.subInfoB.raiId === '') {
    // 更新区分を「C：新規」に設定
    local.updateCategory = UPDATE_KBN.CREATE
  } else {
    // 更新区分を「U：更新」に設定
    local.updateCategory = UPDATE_KBN.UPDATE
    // 取得したサブ情報（C）の各項目を画面の入力フォームへセットする
    respInfo.subInfoB = defaultOneway.subInfoB
  }
  setScreenItem(respInfo.subInfoB)

  // 汎用コードマスタデータを取得し初期化
  void initCodes()
}

/**
 * 画面項目設定
 *
 * @param subInfoB - サブ情報
 */
const setScreenItem = (subInfoB: SubInfoB) => {
  // B1.サブセクション
  // メモ
  or30981TypeB1Area.value.content = subInfoB.b1MemoKnj
  or30981TypeB1Area.value.fontSize = subInfoB.b1MemoFont
    ? subInfoB.b1MemoFont
    : getDefaultFontSize()
  or30981TypeB1Area.value.fontColor = subInfoB.b1MemoColor
    ? subInfoB.b1MemoColor
    : getDefaultFontColor()
  // 入所に対して本人の意思度合
  local.mo00039B1 = subInfoB.b1
  // B2.サブセクション
  // 受付日
  local.mo00020.value = subInfoB.b2Ymd
  // B3.サブセクション
  // 相談受付時までの経過
  orX0156TypeB3Area.value.value = subInfoB.b3Knj
  // B4.サブセクション
  // 相談受付内容
  orX0156TypeB4Area.value.value = subInfoB.b4Knj
  // B5.サブセクション
  // メモ
  or30981TypeB5Area.value.content = subInfoB.b5MemoKnj
  or30981TypeB5Area.value.fontSize = subInfoB.b5MemoFont
    ? subInfoB.b5MemoFont
    : getDefaultFontSize()
  or30981TypeB5Area.value.fontColor = subInfoB.b5MemoColor
    ? subInfoB.b5MemoColor
    : getDefaultFontColor()
  // 利用歴介護施設区分
  local.b5A = subInfoB.b5A
  // 利用歴認知症対応型共同生活介護区分
  local.b5B = subInfoB.b5B
  // 利用歴高齢者住宅－有料老人ホーム区分
  local.b5C = subInfoB.b5C
  // 利用歴精神科病院区分
  local.b5D = subInfoB.b5D
  // 利用歴精神障害者施設区分
  local.b5E = subInfoB.b5E
  // 利用歴知的障害者施設区分
  local.b5F = subInfoB.b5F
  // B6.サブセクション
  // メモ
  or30981TypeB6Area.value.content = subInfoB.b6MemoKnj
  or30981TypeB6Area.value.fontSize = subInfoB.b6MemoFont
    ? subInfoB.b6MemoFont
    : getDefaultFontSize()
  or30981TypeB6Area.value.fontColor = subInfoB.b6MemoColor
    ? subInfoB.b6MemoColor
    : getDefaultFontColor()
  // 入所直前の居住場所区分
  local.b6A = subInfoB.b6A
  // 通常の居住場所区分
  local.b6B = subInfoB.b6B
  // B7.サブセクション
  // メモ
  or30981TypeB7Area.value.content = subInfoB.b7MemoKnj
  or30981TypeB7Area.value.fontSize = subInfoB.b7MemoFont
    ? subInfoB.b7MemoFont
    : getDefaultFontSize()
  or30981TypeB7Area.value.fontColor = subInfoB.b7MemoColor
    ? subInfoB.b7MemoColor
    : getDefaultFontColor()
  // 入所前の同居形態区分
  local.mo00039B7 = subInfoB.b7
  // B8.サブセクション
  // メモ
  or30981TypeB8Area.value.content = subInfoB.b8MemoKnj
  or30981TypeB8Area.value.fontSize = subInfoB.b8MemoFont
    ? subInfoB.b8MemoFont
    : getDefaultFontSize()
  or30981TypeB8Area.value.fontColor = subInfoB.b8MemoColor
    ? subInfoB.b8MemoColor
    : getDefaultFontColor()
  // 精神疾患歴回答
  local.mo00039B8 = subInfoB.b8
  // B9.サブセクション
  // メモ
  or30981TypeB9Area.value.content = subInfoB.b9MemoKnj
  or30981TypeB9Area.value.fontSize = subInfoB.b9MemoFont
    ? subInfoB.b9MemoFont
    : getDefaultFontSize()
  or30981TypeB9Area.value.fontColor = subInfoB.b9MemoColor
    ? subInfoB.b9MemoColor
    : getDefaultFontColor()
  // 教育歴区分
  local.mo00039B9 = subInfoB.b9
  // B10.サブセクション
  // メモ
  or30981TypeB10Area.value.content = subInfoB.b10MemoKnj
  or30981TypeB10Area.value.fontSize = subInfoB.b10MemoFont
    ? subInfoB.b10MemoFont
    : getDefaultFontSize()
  or30981TypeB10Area.value.fontColor = subInfoB.b10MemoColor
    ? subInfoB.b10MemoColor
    : getDefaultFontColor()
  // 医療機関受診時の送迎区分
  local.mo00039B10 = subInfoB.b10
  // B11.サブセクション
  // メモ
  or30981TypeB11Area.value.content = subInfoB.b11MemoKnj
  or30981TypeB11Area.value.fontSize = subInfoB.b11MemoFont
    ? subInfoB.b11MemoFont
    : getDefaultFontSize()
  or30981TypeB11Area.value.fontColor = subInfoB.b11MemoColor
    ? subInfoB.b11MemoColor
    : getDefaultFontColor()
  // 受診中の付き添いが必要回答
  local.mo00039B11 = subInfoB.b11
}

/**
 * AC005_「複写ボタン」押下
 */
const copy = () => {
  void getSubInfo(local.raiId)
}

/**
 * AC011_「削除」押下
 */
const del = () => {
  inputFormDisplayFlag.value = false
  // 画面「B」タブに対し、更新区分を「D:削除」にする。
  local.updateCategory = UPDATE_KBN.DELETE
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    // 本文末に追加の場合
    if (Or31369Const.ADD_END_TEXT_IMPORT_TYPE === data.type) {
      switch (local.vieTypeKbn) {
        // B1．入所に対して本人の意思度合
        case 'B1':
          or30981TypeB1Area.value.content += data.value
          break
        // B3．相談受付時までの経過タイトル
        case 'B3':
          orX0156TypeB3Area.value.value += data.value
          break
        // B4．相談受付内容
        case 'B4':
          orX0156TypeB4Area.value.value += data.value
          break
        // B5．過去5年間の利用歴
        case 'B5':
          or30981TypeB5Area.value.content += data.value
          break
        // B6．入所直前と通常の居住場所
        case 'B6':
          or30981TypeB6Area.value.content += data.value
          break
        // B7．入所前の同居形態
        case 'B7':
          or30981TypeB7Area.value.content += data.value
          break
        // B8．精神疾患歴
        case 'B8':
          or30981TypeB8Area.value.content += data.value
          break
        // B9．教育歴
        case 'B9':
          or30981TypeB9Area.value.content += data.value
          break
        // B10．医療機関受診時の送迎
        case 'B10':
          or30981TypeB10Area.value.content += data.value
          break
        // B11．受診中の付き添いが必要
        case 'B11':
          or30981TypeB11Area.value.content += data.value
          break
        default:
          break
      }
    }
    // 本文上書の場合
    else if (Or31124Const.OVERWRITE_TEXT_IMPORT_TYPE === data.type) {
      switch (local.vieTypeKbn) {
        // B1．入所に対して本人の意思度合
        case 'B1':
          or30981TypeB1Area.value.content = data.value
          break
        // B3．相談受付時までの経過タイトル
        case 'B3':
          orX0156TypeB3Area.value.value = data.value
          break
        // B4．相談受付内容
        case 'B4':
          orX0156TypeB4Area.value.value = data.value
          break
        // B5．過去5年間の利用歴
        case 'B5':
          or30981TypeB5Area.value.content = data.value
          break
        // B6．入所直前と通常の居住場所
        case 'B6':
          or30981TypeB6Area.value.content = data.value
          break
        // B7．入所前の同居形態
        case 'B7':
          or30981TypeB7Area.value.content = data.value
          break
        // B8．精神疾患歴
        case 'B8':
          or30981TypeB8Area.value.content = data.value
          break
        // B9．教育歴
        case 'B9':
          or30981TypeB9Area.value.content = data.value
          break
        // B10．医療機関受診時の送迎
        case 'B10':
          or30981TypeB10Area.value.content = data.value
          break
        // B11．受診中の付き添いが必要
        case 'B11':
          or30981TypeB11Area.value.content = data.value
          break
        default:
          break
      }
    }
  }
}

/**************************************************
 * ウォッチャー
 **************************************************/

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      void onSave()
    }
  }
)
</script>

<template>
  <!-- 入力フォーム -->
  <c-v-row
    v-show="inputFormDisplayFlag"
    class="or31369Container"
    no-gutters
  >
    <c-v-col
      cols="auto"
      class="pt-6"
    >
      <div style="width: 1080px">
        <c-v-row no-gutters>
          <c-v-spacer />
          <!-- 調査アセスメント種別ラベル -->
          <base-mo01338
            style="margin-right: -6px !important"
            :oneway-model-value="defaultOneway.mo01338SurveyAssessmentTypeOneway"
          />
        </c-v-row>

        <c-v-row
          class="w-100 sub-selection"
          no-gutters
        >
          <!-- 相談受付表タイトル -->
          <base-mo01338 :oneway-model-value="defaultOneway.mo01338BOneway" />
          <!-- 注意事項ラベル -->
          <base-mo01338 :oneway-model-value="defaultOneway.mo01338MattersToBeAttentionOneway" />
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col class="pa-0">
            <!-- B1.サブセクション -->
            <c-v-row
              class="secondary-background h-auto"
              no-gutters
            >
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 入所に対して本人の意思度合タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B1Oneway" />
                  </c-v-col>
                </c-v-row>
                <!-- B1．入所に対して本人の意思度合ラジオグループ -->
                <c-v-row
                  class="asection_right_second"
                  no-gutters
                >
                  <base-mo00039
                    v-model="local.mo00039B1"
                    :oneway-model-value="defaultOneway.mo00039Oneway"
                    class="gridContent"
                  >
                    <base-at-radio
                      v-for="(item, index) in defaultOneway.admissionPersonDegreeOfIntentionList"
                      :key="'or31369-' + index"
                      :name="'or31369-radio-' + index"
                      :radio-label="item.label"
                      :value="item.value"
                      radio-class="radio-item"
                      :class="{ radioItemDisabled: inputItemDisabled }"
                    />
                  </base-mo00039>
                </c-v-row>
                <c-v-row
                  class="pl-12 pr-6 pb-6"
                  no-gutters
                >
                  <c-v-col class="pa-0">
                    <g-custom-or-30981
                      v-bind="or30981_1"
                      v-model="or30981TypeB1Area"
                      :oneway-model-value="defaultOneway.or30981OnewayTypeB1"
                      @on-click-edit-btn="clickMemoInputCategoryIcon('B1')"
                    ></g-custom-or-30981>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- B2.サブセクション -->
            <c-v-row no-gutters>
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 受付日タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B2Oneway" />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  class="w-100 pa-6 pl-12 secondary-background"
                  no-gutters
                >
                  <base-mo00020
                    :model-value="local.mo00020"
                    :oneway-model-value="defaultOneway.mo00020Oneway"
                    v-bind="{ ...$attrs }"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- B3.サブセクション -->
            <c-v-row no-gutters>
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 相談受付時までの経過タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B3Oneway" />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  class="w-100 pa-6 pl-12 secondary-background"
                  no-gutters
                >
                  <g-custom-or-x-0156
                    v-model="orX0156TypeB3Area"
                    :oneway-model-value="defaultOneway.orX0156OnewayTypeB3"
                    style="width: 903px"
                    @on-click-edit-btn="clickMemoInputCategoryIcon('B3')"
                  ></g-custom-or-x-0156>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- B4.サブセクション -->
            <c-v-row no-gutters>
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 相談受付内容タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B4Oneway" />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  class="w-100 pa-6 pl-12 secondary-background"
                  no-gutters
                >
                  <g-custom-or-x-0156
                    v-model="orX0156TypeB4Area"
                    :oneway-model-value="defaultOneway.orX0156OnewayTypeB4"
                    style="width: 903px"
                    @on-click-edit-btn="clickMemoInputCategoryIcon('B4')"
                  ></g-custom-or-x-0156>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- B5.サブセクション -->
            <c-v-row
              class="secondary-background h-auto"
              no-gutters
            >
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 過去5年間の利用歴タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B5Oneway" />
                  </c-v-col>
                </c-v-row>
                <!-- B5．過去5年間の利用歴ラジオグループ -->
                <c-v-row
                  class="asection_right_second pr-2"
                  style="padding-bottom: 5px"
                  no-gutters
                >
                  <!-- 過去5年間の利用歴a. -->
                  <c-v-row
                    class="align-content-center pr-0"
                    no-gutters
                  >
                    <!-- a. 介護施設、療養病院/病棟ラベル -->
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B5AOneway" />
                    <c-v-spacer />
                    <!-- 利用歴介護施設区分 -->
                    <base-mo00039
                      v-model="local.b5A"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                    >
                      <base-at-radio
                        v-for="(
                          item, index
                        ) in defaultOneway.past5YearlyUsageHistoryResponseSelectItems"
                        :key="'or31369-' + index"
                        :name="'or31369-radio-' + index"
                        :radio-label="item.label"
                        :value="item.value"
                        radio-class="radio-item2"
                      />
                    </base-mo00039>
                  </c-v-row>
                  <!-- 過去5年間の利用歴b. -->
                  <c-v-row
                    class="align-content-center pr-0"
                    no-gutters
                  >
                    <!-- b. 認知症対応型共同生活介護、小規模多機能型居宅介護ラベル -->
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B5BOneway" />
                    <c-v-spacer />
                    <!-- 利用歴認知症対応型共同生活介護区分 -->
                    <base-mo00039
                      v-model="local.b5B"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                    >
                      <base-at-radio
                        v-for="(
                          item, index
                        ) in defaultOneway.past5YearlyUsageHistoryResponseSelectItems"
                        :key="'or31369-' + index"
                        :name="'or31369-radio-' + index"
                        :radio-label="item.label"
                        :value="item.value"
                        radio-class="radio-item2"
                      />
                    </base-mo00039>
                  </c-v-row>
                  <!-- 過去5年間の利用歴c. -->
                  <c-v-row
                    class="align-content-center pr-0"
                    no-gutters
                  >
                    <!-- c. 高齢者住宅-有料老人ホーム(特定施設入居者生活介護有り·無し含む)ラベル -->
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B5COneway" />
                    <c-v-spacer />
                    <!-- 利用歴高齢者住宅－有料老人ホーム区分 -->
                    <base-mo00039
                      v-model="local.b5C"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                    >
                      <base-at-radio
                        v-for="(
                          item, index
                        ) in defaultOneway.past5YearlyUsageHistoryResponseSelectItems"
                        :key="'or31369-' + index"
                        :name="'or31369-radio-' + index"
                        :radio-label="item.label"
                        :value="item.value"
                        radio-class="radio-item2"
                      />
                    </base-mo00039>
                  </c-v-row>
                  <!-- 過去5年間の利用歴d. -->
                  <c-v-row
                    class="align-content-center pr-0"
                    no-gutters
                  >
                    <!-- d. 精神科病院、精神科病棟ラベル -->
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B5DOneway" />
                    <c-v-spacer />
                    <!-- 利用歴精神科病院区分 -->
                    <base-mo00039
                      v-model="local.b5D"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                    >
                      <base-at-radio
                        v-for="(
                          item, index
                        ) in defaultOneway.past5YearlyUsageHistoryResponseSelectItems"
                        :key="'or31369-' + index"
                        :name="'or31369-radio-' + index"
                        :radio-label="item.label"
                        :value="item.value"
                        radio-class="radio-item2"
                      />
                    </base-mo00039>
                  </c-v-row>
                  <!-- 過去5年間の利用歴e. -->
                  <c-v-row
                    class="align-content-center pr-0"
                    no-gutters
                  >
                    <!-- e. 精神障害者施設ラベル -->
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B5EOneway" />
                    <c-v-spacer />
                    <!-- 利用歴精神障害者施設区分 -->
                    <base-mo00039
                      v-model="local.b5E"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                    >
                      <base-at-radio
                        v-for="(
                          item, index
                        ) in defaultOneway.past5YearlyUsageHistoryResponseSelectItems"
                        :key="'or31369-' + index"
                        :name="'or31369-radio-' + index"
                        :radio-label="item.label"
                        :value="item.value"
                        radio-class="radio-item2"
                      />
                    </base-mo00039>
                  </c-v-row>
                  <!-- 過去5年間の利用歴f. -->
                  <c-v-row
                    class="align-content-center pr-0"
                    no-gutters
                  >
                    <!-- f. 知的障害者施設 -->
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B5FOneway" />
                    <c-v-spacer />
                    <!-- 利用歴知的障害者施設区分 -->
                    <base-mo00039
                      v-model="local.b5F"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                    >
                      <base-at-radio
                        v-for="(
                          item, index
                        ) in defaultOneway.past5YearlyUsageHistoryResponseSelectItems"
                        :key="'or31369-' + index"
                        :name="'or31369-radio-' + index"
                        :radio-label="item.label"
                        :value="item.value"
                        radio-class="radio-item2"
                      />
                    </base-mo00039>
                  </c-v-row>
                </c-v-row>
                <c-v-row
                  class="pl-12 pr-6 pb-6"
                  no-gutters
                >
                  <c-v-col class="pa-0">
                    <g-custom-or-30981
                      v-bind="or30981_2"
                      v-model="or30981TypeB5Area"
                      :oneway-model-value="defaultOneway.or30981OnewayTypeB5"
                      @on-click-edit-btn="clickMemoInputCategoryIcon('B5')"
                    ></g-custom-or-30981>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- B6.サブセクション -->
            <c-v-row
              class="secondary-background h-auto"
              no-gutters
            >
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 入所直前と通常の居住場所タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B6Oneway" />
                  </c-v-col>
                </c-v-row>
                <!-- B6．入所直前と通常の居住場所ラジオグループ -->
                <c-v-row
                  class="asection_right_second"
                  style="padding: 24px 8px 7px 48px"
                  no-gutters
                >
                  <!-- a.入所直前の居住場所ラベル -->
                  <base-mo01338 :oneway-model-value="defaultOneway.mo01338B6AOneway" />
                  <c-v-row
                    class="pt-6 pl-6"
                    no-gutters
                  >
                    <base-mo00039
                      v-model="local.b6A"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                      class="gridContent2"
                    >
                      <base-at-radio
                        v-for="(item, index) in defaultOneway.residenceLocationSelectItems"
                        :key="'or31369-' + index"
                        :name="'or31369-radio-' + index"
                        :radio-label="item.label"
                        :value="item.value"
                        radio-class="radio-item3"
                        :class="{ radioItemDisabled: inputItemDisabled }"
                      />
                    </base-mo00039>
                  </c-v-row>
                  <!-- b.通常の居住場所ラベル -->
                  <base-mo01338 :oneway-model-value="defaultOneway.mo01338B6BOneway" />
                  <c-v-row
                    class="pt-6 pl-6"
                    no-gutters
                  >
                    <base-mo00039
                      v-model="local.b6B"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                      class="gridContent2"
                    >
                      <base-at-radio
                        v-for="(item, index) in defaultOneway.residenceLocationSelectItems"
                        :key="'or31369-' + index"
                        :name="'or31369-radio-' + index"
                        :radio-label="item.label"
                        :value="item.value"
                        radio-class="radio-item3"
                        :class="{ radioItemDisabled: inputItemDisabled }"
                      />
                    </base-mo00039>
                  </c-v-row>
                </c-v-row>
                <c-v-row
                  class="pl-12 pr-6 pb-6"
                  no-gutters
                >
                  <c-v-col class="pa-0">
                    <g-custom-or-30981
                      v-bind="or30981_3"
                      v-model="or30981TypeB6Area"
                      :oneway-model-value="defaultOneway.or30981OnewayTypeB6"
                      @on-click-edit-btn="clickMemoInputCategoryIcon('B6')"
                    ></g-custom-or-30981>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- B7.サブセクション -->
            <c-v-row
              class="secondary-background h-auto"
              no-gutters
            >
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 入所前の同居形態タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B7Oneway" />
                  </c-v-col>
                </c-v-row>
                <!-- B7．入所前の同居形態ラジオグループ -->
                <c-v-row
                  class="asection_right_second"
                  no-gutters
                >
                  <base-mo00039
                    v-model="local.mo00039B7"
                    :oneway-model-value="defaultOneway.mo00039Oneway"
                    class="gridContent"
                  >
                    <base-at-radio
                      v-for="(item, index) in defaultOneway.cohabitFormList"
                      :key="'or31369-' + index"
                      :name="'or31369-radio-' + index"
                      :radio-label="item.label"
                      :value="item.value"
                      radio-class="radio-item"
                      :class="{ radioItemDisabled: inputItemDisabled }"
                    />
                  </base-mo00039>
                </c-v-row>
                <c-v-row
                  class="pl-12 pr-6 pb-6"
                  no-gutters
                >
                  <c-v-col class="pa-0">
                    <g-custom-or-30981
                      v-bind="or30981_4"
                      v-model="or30981TypeB7Area"
                      :oneway-model-value="defaultOneway.or30981OnewayTypeB7"
                      @on-click-edit-btn="clickMemoInputCategoryIcon('B7')"
                    ></g-custom-or-30981>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- B8.サブセクション -->
            <c-v-row
              class="secondary-background h-auto"
              no-gutters
            >
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 精神疾患歴タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B8Oneway" />
                  </c-v-col>
                </c-v-row>
                <!-- B8．精神疾患歴ラジオグループ -->
                <c-v-row
                  class="asection_right_second"
                  style="padding-bottom: 5px"
                  no-gutters
                >
                  <base-mo00039
                    v-model="local.mo00039B8"
                    :oneway-model-value="defaultOneway.mo00039Oneway"
                    class="gridContent"
                  >
                    <base-at-radio
                      v-for="(item, index) in defaultOneway.mentalIllnessHistoryList"
                      :key="'or31369-' + index"
                      :name="'or31369-radio-' + index"
                      :radio-label="item.label"
                      :value="item.value"
                      radio-class="radio-item2"
                      :class="{ radioItemDisabled: inputItemDisabled }"
                    />
                  </base-mo00039>
                </c-v-row>
                <c-v-row
                  class="pl-12 pr-6 pb-6"
                  no-gutters
                >
                  <c-v-col class="pa-0">
                    <g-custom-or-30981
                      v-bind="or30981_5"
                      v-model="or30981TypeB8Area"
                      :oneway-model-value="defaultOneway.or30981OnewayTypeB8"
                      @on-click-edit-btn="clickMemoInputCategoryIcon('B8')"
                    ></g-custom-or-30981>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- B9.サブセクション -->
            <c-v-row
              class="secondary-background h-auto"
              no-gutters
            >
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 教育歴タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B9Oneway" />
                  </c-v-col>
                </c-v-row>
                <!-- B9．教育歴ラジオグループ -->
                <c-v-row
                  class="asection_right_second"
                  no-gutters
                >
                  <base-mo00039
                    v-model="local.mo00039B9"
                    :oneway-model-value="defaultOneway.mo00039Oneway"
                    class="gridContent3"
                  >
                    <base-at-radio
                      v-for="(item, index) in defaultOneway.educationalHistoryList"
                      :key="'or31369-' + index"
                      :name="'or31369-radio-' + index"
                      :radio-label="item.label"
                      :value="item.value"
                      radio-class="radio-item4"
                    />
                  </base-mo00039>
                </c-v-row>
                <c-v-row
                  class="pl-12 pr-6 pb-6"
                  no-gutters
                >
                  <c-v-col class="pa-0">
                    <g-custom-or-30981
                      v-bind="or30981_6"
                      v-model="or30981TypeB9Area"
                      :oneway-model-value="defaultOneway.or30981OnewayTypeB9"
                      @on-click-edit-btn="clickMemoInputCategoryIcon('B9')"
                    ></g-custom-or-30981>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- B10.サブセクション -->
            <c-v-row
              class="secondary-background h-auto"
              no-gutters
            >
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 医療機関受診時の送迎タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B10Oneway" />
                  </c-v-col>
                </c-v-row>
                <!-- B10．医療機関受診時の送迎ラジオグループ -->
                <c-v-row
                  class="asection_right_second"
                  no-gutters
                >
                  <base-mo00039
                    v-model="local.mo00039B10"
                    :oneway-model-value="defaultOneway.mo00039Oneway"
                    class="gridContent"
                  >
                    <base-at-radio
                      v-for="(item, index) in defaultOneway.shuttleList"
                      :key="'or31369-' + index"
                      :name="'or31369-radio-' + index"
                      :radio-label="item.label"
                      :value="item.value"
                      radio-class="radio-item"
                    />
                  </base-mo00039>
                </c-v-row>
                <c-v-row
                  class="pl-12 pr-6 pb-6"
                  no-gutters
                >
                  <c-v-col class="pa-0">
                    <g-custom-or-30981
                      v-bind="or30981_7"
                      v-model="or30981TypeB10Area"
                      :oneway-model-value="defaultOneway.or30981OnewayTypeB10"
                      @on-click-edit-btn="clickMemoInputCategoryIcon('B10')"
                    ></g-custom-or-30981>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <!-- B11.サブセクション -->
            <c-v-row
              class="secondary-background h-auto"
              no-gutters
            >
              <c-v-col class="pa-0">
                <c-v-row
                  class="sub_title"
                  no-gutters
                >
                  <!-- 受診中の付き添いが必要タイトル -->
                  <c-v-col class="align-content-center px-6 h-100">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338B11Oneway" />
                  </c-v-col>
                </c-v-row>
                <!-- B11．受診中の付き添いが必要ラジオグループ -->
                <c-v-row
                  class="asection_right_second"
                  style="padding-bottom: 5px"
                  no-gutters
                >
                  <base-mo00039
                    v-model="local.mo00039B11"
                    :oneway-model-value="defaultOneway.mo00039Oneway"
                  >
                    <base-at-radio
                      v-for="(item, index) in defaultOneway.attendantNecessaryResponseList"
                      :key="'or31369-' + index"
                      :name="'or31369-radio-' + index"
                      :radio-label="item.label"
                      :value="item.value"
                      radio-class="radio-item2"
                    />
                  </base-mo00039>
                </c-v-row>
                <c-v-row
                  class="pl-12 pr-6 pb-6"
                  no-gutters
                >
                  <c-v-col class="pa-0">
                    <g-custom-or-30981
                      v-bind="or30981_8"
                      v-model="or30981TypeB11Area"
                      :oneway-model-value="defaultOneway.or30981OnewayTypeB11"
                      @on-click-edit-btn="clickMemoInputCategoryIcon('B11')"
                    ></g-custom-or-30981>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </div>
      <!-- interRAIロゴ -->
      <c-v-row
        class="mt-4"
        no-gutters
      >
        <c-v-col class="pa-0">
          <c-v-img
            width="129"
            aspect-ratio="16/9"
            cover
            :src="InterRAI"
            style="float: right"
          ></c-v-img>
        </c-v-col>
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <!-- GUI00937_入力支援［ケアマネ］ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Type"
    :oneway-model-value="defaultOneway.or51775Oneway"
    @confirm="or51775Confirm"
  />
  <!-- 確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
.or31369Container {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: transparent;
}

.title-h5 :deep(.item-label) {
  font-size: 24px;
  font-weight: 400 !important;
}

.sub_title {
  border: 1px #dadada solid;
  height: 48px;
  align-content: center;
  background-color: rgba(var(--v-theme-black-100));
}

.secondary-background {
  background-color: rgba(var(--v-theme-secondaryBackground));
}

.asection_right_second {
  justify-content: start;
  padding: 27px 24px 8px 48px;

  :deep(.v-selection-control--dirty) {
    background-color: #ebf2fd !important;
    border: 1px solid rgb(var(--v-theme-key)) !important;
  }

  :deep(.v-label--clickable) {
    width: 100% !important;
  }

  .radio-item {
    width: 312px;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    margin-right: 16px;
    margin-bottom: 16px;
  }

  .radio-item2 {
    width: 180px;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    margin-right: 16px;
    margin-bottom: 19px;
  }

  .radio-item3 {
    width: 484px;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    margin-right: 16px;
    margin-bottom: 16px;
  }

  .radio-item4 {
    width: 496px;
    border: 1px solid #8a8a8a;
    border-radius: 5px;
    margin-right: 16px;
    margin-bottom: 16px;
  }

  .radioItemDisabled {
    background: #f2f2f2 !important;

    :deep(.text-key) {
      color: #a8aaae !important;
    }
  }

  :deep(.v-field__input) {
    min-height: 8px !important;
  }

  :has(> .toGray) {
    :first-child {
      background-color: #c0c0c0 !important;
    }
  }
}

.gridContent {
  display: grid;
  grid-template-columns: repeat(1, 800px);
}

.gridContent2 {
  display: grid;
  grid-template-columns: repeat(1, 1000px);
}

.gridContent3 {
  display: grid;
  grid-template-columns: repeat(1, 1024px);
}

.sub-selection {
  background-color: rgba(var(--v-theme-secondaryBackground));
}

// transparent
.background-transparent {
  background-color: transparent !important;
}

.custom-label {
  white-space: pre-line;
}

.filed-input :deep(.v-field__input) {
  min-height: 8px !important;
  height: 30px !important;
}

.v-btn:focus {
  box-shadow: none !important;
  outline: none !important;
}
</style>
