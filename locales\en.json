{"label": {"login-id": "Login ID", "password": "Password", "login": "<PERSON><PERSON>", "system-notice": "Notice From the System", "user-list": "User List", "search-criteria": "Search Criteria", "user-group": "User Group", "user-group-list": "User Groups List", "user-name": "User Name", "user-number": "User Number", "gender": "Gender", "date-of-birth": "Date of birth", "age": "Age", "level-of-care-required": "Level Of Nursing Care Required", "phone-number": "Phone number", "mobile-phone-number": "Mobile phone Number", "address": "Address", "care-manager-in-charge": "Care manager in charge", "valid-invalid": "Valid/Invalid", "man": "Man", "woman": "Woman", "save": "Save", "cancel": "Cancel", "search": "Search", "required": "Required", "care-plan": "Planning document", "care-plan1": "Planning document(1)", "care-plan2": "Planning document(2)", "weekly-plan": "Weekly plan", "daily-plan": "Daily routine plan", "process-date": "Processing date", "office": "Office", "plan-target-period": "Planning period", "create-date": "Created date", "author": "Author", "preliminary": "Preliminary", "first-create-date": "First creation date", "plan-class": "Planning classification", "first-time": "First time", "introduction": "Introduction", "continuation": "Continuation", "certification-class": "Certification category", "manager": "Manager", "certification-date": "Certification date", "validity-period": "Validity period", "nursing-care-required": "Nursing care required", "prospect": "Prospect", "other": "Other", "care-plan1-analysis-result": "Results of issue analysis based on the intentions of users and their families regarding their lives", "care-plan1-opinion-service-type": "Opinions of the Nursing Care Certification Examination Committee and designation of service types", "care-plan1-assistance-policy": "Comprehensive assistance policy", "care-plan1-calculation-reason": "Reasons for calculation centered on living assistance", "staff-basic-info": "Staff basic", "staff-number": "Staff number", "sort-number": "Sort number", "furigana": "<PERSON><PERSON><PERSON>", "name": "Name", "birthday": "Birthday", "joining-date": "Joining date", "leaving-date": "Leaving date", "tel-number": "Telphone number", "cell-number": "Cellphone number", "email": "E-mail", "main-occupation": "Main occupation", "other-occupation": "Other occupation", "license": "License", "license1": "License1", "license2": "License2", "license3": "License3", "license4": "License4", "license5": "License5", "part-time": "Part time", "specialists-number": "Nursing care support specialist number", "self-number": "Self number", "personal-folder": "Personal folder", "applicable-office": "Applicable office", "user-basic-info": "user basic info", "user-basic-info-list": "user basic info list", "insurer": "insurer", "phone-and-mobile-number": "phone and mobile number", "group": "group", "tenant-group": "tenant group", "end-of-use": "end of use", "past": "past", "within-the-month": "within the month", "show-also-deceased-person": "show also deceased person", "kana": "kana", "family-name": "family name", "first-name": "first name", "kana-of-family-name": "kana of family name", "kana-of-first-name": "kana of first name", "identify-the-same-name": "identify the same name", "blood-type": "blood type", "rh": "RH", "zip-code-mark": "zip code", "fax": "FAX", "household-category": "household category", "memo": "memo", "statistical-area": "statistical area", "other-address": "other address", "day-of-death": "day of death", "time": "time", "inside-and-outside-facilities": "inside and outside facilities", "location": "location", "reason": "reason", "remarks": "remarks", "obituary": "obituary", "year-old": "year old", "confirm": "confirm", "year": "year", "month": "month", "months": "months", "history": "History", "care-plan2-needs": "Issues (needs) to be resolved in overall daily life", "long-term-goal": "Long term goal", "short-term-goal": "Short term goal", "period": "Period", "brackets-period": "(Period)", "service-contents": "Service contents", "service-type": "Service type", "frequency": "Frequency", "asterisk1": "*1", "asterisk2": "*2", "selection-of-nursing-care-insurance-applicable-office": "Selection of nursing care insurance applicable office", "office-number": "Office number", "office-name": "Office name", "facility-name": "Facility name", "corporation-name": "Corporation name", "business-type": "Business type", "ommited-care-manager": "Care-mng", "past-history": "Past-history", "common": "Common", "care-manager-document-master": "Contents of care manager document master", "detail-view": "Detail view", "basic-info": "basic infomation", "nutritional-status": "Nutritional Status", "lebel-low": "Low", "lebel-middle": "Middle", "lebel-high": "High", "classification": "Classification", "input-support": "Input support", "care-manager-hist-title": "Care manager history information", "stop": "Stop", "main-license": "Main license", "staff-abreviation": "Staff abreviation", "staff-display-name": "Staff display name", "visiting-group": "Visiting group", "work-classification": "Work classification", "care-manager-hist-guide": "*Users eligible for outsourcing must register as a comprehensive person.", "logout": "Logout", "user-settings": "User Settings", "plan-copy": "Plan copy", "organizing-issues-import": "Issue organization summary import", "examination-issues-paper-import": "Importing assignment review paper", "print-configuration": "Print configuration", "batch-printing-of-plans": "Batch printing of plans", "other-features": "Other features", "meeting-minutes": "meeting minutes", "reason-absence": "Absentee and reason for absence", "items-considered": "Items considered", "study-contents": "Study contents", "conclusion": "conclusion", "remaining-issues": "Remaining issues", "next-meeting-date": "Next meeting date", "meeting-date": "meeting date", "meeting-location": "meeting location", "meeting-time": "meeting time", "personal-attendance": "personal", "family-attendance": "family", "relationship": "relationship", "number-of-meeting": "number of meeting", "affilication-occupation": "affilication(occupation)", "meeting-infomation": "Meeting Infomation", "attendee-information": "Attendee Information", "meeting-points": "Meeting points", "certified": "Certified", "applying": "Applying", "caution": "Caution", "plan-monitoring": "Plan monitoring", "evaluation-list": "Evaluation list", "implementation-monitoring": "Implementation monitoring", "emergence-of-new-needs": "Emergence of new needs", "need-for-reassessment": "Need for reassessment", "search-staff": "Search staff", "case-number": "Case number", "display-only-related-offices": "Display only related offices", "monitoring": "Monitoring", "goal": "Goal", "evaluation": "Evaluation", "target-period": "Target period", "service-implementation-status": "Service implementation status", "planner": "Planner", "user": "User", "future-correspondence": "Future correspondence", "the-reason": "The reason", "year-month": "Year Month", "year-month-day": "Year-Month-Day", "month-day": "Month-Day", "target-achievement-status": "Target achievement status", "type": "Type", "kinship-name": "Name", "medical-institution": "Medical institution", "clinical-department": "Clinical Department", "none": "None", "occupation": "Occupation", "affiliation(omitted)": "Affiliation(omitted)", "affiliation(official)": "Affiliation(official)", "affiliation-label": "affilication", "filter": "Filter", "staff-name": "Staff Name", "meeting-attendee-intake": "Meeting attendee intake", "selected-items": "Selected items", "medical-institution-name": "Medical institution name", "clinical-department-name": "Clinical department name", "file-upload": "File Upload", "file-drop-here": "Drop your files here", "or": "or", "display-setting": "Display Setting", "narrow": "<PERSON>rrow", "in-page": "In Pages", "serch-result": "<PERSON><PERSON>", "serch-result-unit-nin": "persons", "insurer-in-area-admission-residence": "Insurer in the area of ​​admission/residence", "insurance-number": "Insurance Number", "admission-movement-address-label1": "※Please register with the insurer of the city, town or ", "admission-movement-address-label2": "village where the facility you plan to enter or reside is located.", "copyright": "2023 NDSoftware co, Ltd. All Rights Reserved.", "document-master-common": "Contents of common document master", "fiscal-year": "fiscal year", "facility-unit-save": "Stored by facility", "image-management-configuration": "Image Management Configuration", "display-number-of-rows": "Display Number Of Rows", "management-items-example-display": "Management Items Example Display", "do": "Do", "do-not": "Do Not", "date-filter": "Date Filter", "row": "Row", "day-of-week-short-sunday": "SUN", "day-of-week-short-monday": "MON", "day-of-week-short-tuesday": "TUE", "day-of-week-short-wednesday": "WED", "day-of-week-short-thursday": "THU", "day-of-week-short-friday": "FRI", "day-of-week-short-saturday": "SAT", "subject-period-screen": "Subject Period  Screen", "assessment-format-select": "Assessment Format Select", "select-assessment-format": "Please select a format of assessment.", "office-unit-save": "Stored by office", "menu-settings": "Menu setting", "save-common-system": "※Save settings common to the system", "0perate-under-own-responsibility": "※This service is available for residential, short term care, and day care facilities. \r\n　Please operate the system under your own responsibility as it is not recommended for other facilities.", "use-r4s-system": "Use the R4 system at offices other than Roken nurnsing home and dayservice rehabilitation offices", "list-form": "list form", "nursing-care-insurance": "nursing care insurance", "medical-insurance": "medical insurance", "geriatric-health": "geriatric health", "public-funds": "public funds", "medical-history": "medical history", "burden-limit": "burden limit", "by-main-responsibility": "by main responsibility", "by-service": "by service", "by-degree-of-nursing-care-required": "by degree of nursing care required", "subject-period-table": "subject period table", "subject": "subject", "case": "case", "case-table": "case table", "hours": "hours", "contents": "contents", "person-recording": "person-recording", "hierarchy-settings": "Hierarchy settings", "maximum-hierarchy": "Maximum hierarchy", "number-input-assistance": "Number Input Assistance", "print-office-settings": "Setup of business name for printing", "item-select": "Item Select", "ledger": "Ledger", "title": "Title", "handover": "Handover", "employment-status": "Employment Status", "sex": "Sex", "staff-category": "Staff-Category", "within-the-month-nighttime-home-visit-nursing-care-dispatch": "Within The Month Nighttime Home Visit Nursing Care Dispatch", "ad": "Ad", "japanese-calendar": "Japanese Calendar", "year-month-select": "Year Month Select", "meiji": "Meiji", "taisho": "<PERSON><PERSON><PERSON>", "showa": "Showa", "heisei": "<PERSON><PERSON><PERSON>", "reiwa": "Reiwa", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "origin-year": "Orijin Year", "within-the-month-home-visit-bathing-dispatch": "Within The Month Home Visit Bathing  Dispatch", "within-the-month-home-visit-nursing-dispatch": "Within The Month Home Visit Nursing Dispatch", "IndependenceSupportStaff": "Independence-Support-Staff", "schedule-achievements": "Schedule Achievements", "hospital-select": "Hospital Select", "tree-display": "Tree Display", "list-display": "List Display", "doctor": "Doctor", "doctor-name": "Doctor Name", "hospital": "Hospital", "clinical-dept": "Clinical Dept", "hospital-name": "Hospital Name", "clinical-dept-name": "Clinical Dept Name", "hosp": "Hospital", "implementation-plan": "Implementation-plan", "implementation-plan-list": "Implementation Plan List", "comprehensive-implementation-plan-list": "Comprehensive Implementation Plan List", "survey-date": "Survey Date", "insured-person-number": "Insured Person Number", "man-and-woman": "Man And Woman", "image-settings": "Image Settings", "process-year-month": "Process Year Month", "confirm-deletion": "Confirm Deletion", "disuse-syndrome": "Disuse Syndrome", "orthostatic-hypotension": "Orthostatic Hypotension", "venous-thrombosis": "Venous Thrombosis", "discharge-destination-from-hospital": "Discharge Destination From Hospital", "still-image-import": "Still Image Import", "still-image-edit": "Editing and importing still images", "list-display-order": "List Display Order", "information-title-goes-here": "Information title goes here.", "description-text-goes-here": "Description text goes here.", "ksg-environment-filter-settings": "ksg Environment Filter Settings", "image": "Image", "filename": "Filename", "automatic-close-after-save": "automatic close after save", "photographing-date": "Photographing date", "image-regist": "Image regist", "still-image": "Still Image", "movie": "Movie", "description": "Description", "record": "Record", "staff": "Staff", "connection": "Connection", "number-of-history": "Number Of History", "discharge-from-hospital-undecided": "Discharge From Hospital Undecided", "motor-function-handycap": "Motor Function Handycap", "central-paralysis": "Central Paralysis", "upper-arm": "Upper Arm", "finger": "Finger", "lower-arm": "Lower Arm", "onset-of-the-disease-date": "Onset Of The Disease Date", "disease-name": "Disease Name", "hospitalization-visiting-the-hospital": "Hospitalization Visiting The Hospital", "hospitalization-visiting-the-hospital-abbreviation": "Hospitalization Visiting The Hospital", "end-category": "End Category", "medical-history-information-screen": "Medical History Information Screen", "display-hierarchy": "Display hierarchy", "sentence-master-common-contents": "Sentence master common contents", "detail-display": "Detail display", "location-import": "location import", "meeting-place": "meeting location", "use-unregistered-display": "Use Unregistered Display", "applicable-office-user-display": "Display users of applicable office", "other-office-user-display": "Display users from other offices", "today-planned-person": "Today's planned person", "today-valid--unregistered": "Registration valid today", "letter-edit-screen": "Letter edit screen", "basic-motion": "Basic MotionBasic Motion", "maintaining-standing-position": "Maintaining Standing Position", "equipment": "Equipment", "training-indoor-walking": "Training Indoor- Walking", "cane-equipment-etc": "Cane, Equipment etc", "sensation-handycap": "Sensation Handycap", "sight": "Sight", "hearing": "Hearing", "higher-brain-function-handycap": "Higher Brain Function Handycap", "voice-utterance-handycap": "Voice, Utterance Handycap", "articulation-handycap": "Articulation Handycap", "aphasia": "Aphasia", "contracture": "Contracture", "situation": "Situation", "body-temperature": "Body Temperature", "blood-pressure-high": "Blood Pressure High", "blood-pressure-low": "Blood Pressure Low", "pulse": "Pulse", "breath": "Breath", "spo2": "SpO2", "arrhythmia": "Arrhythmia", "blood-sugar-level": "Blood Sugar Level", "room-order-line-up": "Arrange by room", "handycap-notebook": "Handycap Notebook", "disability-pension": "Disability Pension", "appendix": "A<PERSON>ndix", "comprehensive-implementation-plan-list-attention-matter": "\"＜Attention＞：・Health status, participation, activity (performance status, ability), mental and physical functions, and environment are based on the WHO ICF\r\n　　　　　　　　 (International Classification of Functioning, Disability and Health)\r\n　　　　　　　 ・If more details are required, please attach a separate sheet\"", "certification-status": "CertificationStatus", "age-upper-limit": "Age Upper Limit", "certification-expired-period": "Certification Expired Period", "address-postal-code-category": "Address Postal Code Category", "fatalities-visible": "Show people who died", "view-past-users": "View past users", "display-only-those-with-electronic-medical-records": "Display only those with electronic medical records", "electronic-medical-record-cooperation-id": "Electronic medical record cooperation id", "paruna-cooperation": "Paruna cooperation", "result": "result", "printing-progress": "Printing in progress...", "printing-complete": "The printing is complete", "food-form": "Food Form", "ratio": "<PERSON><PERSON>", "staple-food": "Staple Food", "side-dish": "Side Dish", "soup": "Soup", "main-dish": "Main Dish", "accompanying-dish": "Accompanying <PERSON><PERSON>", "today": "today", "offer-kind": "Offer kind", "snacks-name": "Snacks Name", "intake": "Intake", "water-name": "Water Name", "volume": "Volume", "facility": "Facility", "display-order": "Display order", "display-page": "Display page", "management-items": "Management Items", "morning": "Morning", "noon": "<PERSON>on", "evening": "Evening", "image-management": "Image Management", "office-facility": "Office facility", "vital": "Vital", "meal": "<PERSON><PERSON>", "snacks": "Snacks", "water": "Water", "oral-cavity": "Oral Cavity", "oral-care": "Oral Care", "excretion": "Excretion", "bathing": "Bathing", "body-measurements": "Body Measurements", "everyday": "Everyday", "recreation": "Recreation", "treatment": "Treatment", "symptoms": "Symptoms", "taking-medication": "Taking Medication", "nurse-care": "Nurse Care", "observation-info": "Observation Info", "rehabilitation": "Rehabilitation", "motor-function": "Motor Function", "action": "Action", "accident": "Accident", "hand-over": "Hand Over", "instruction": "Instruction", "contact-book": "Contact Book", "connection-staff": "Connection Staff", "unselected": "Unselected", "image-edit": "Image-Edit", "monthly-results": "Monthly results (addition confirmation list)", "total": "total", "sched-achv": "Sched Achv", "number-days-subject-addition": "Number of days subject to addition", "number-days-not-subject-addition": "Number of days not subject to addition", "monthly-results-legend-explanation-before": "△:Not subject to addition\n○:Subject(function, short term)\n□:Dementia target\n◆:Livelihood improvement target", "monthly-results-legend-explanation-after": "△:Not subject to addition\n○:Function:Ⅰ Short term:1～6\n◎:Function:Ⅱ Short term:1～6\n●:Function:Ⅰ+Ⅱ Short term:1～6\n□:Dementia target\n◆:Livelihood improvement target", "sched": "Sched", "achv": "Achv", "schedule": "schedule", "achievements": "achievements", "daily-total": "Daily total", "start": "Start", "end": "End", "start-date": "Start date", "end-date": "End date", "time-required": "Time Required", "care-contents": "Care Contents", "implementation": "Implementation", "care-details": "Care Details", "method": "Method", "defecation": "Defecation", "fecal-incontinence": "Fecal Incontinence", "stool-properties": "Stool Properties", "stool-volume": "Stool Volume", "urination": "Urination", "urinary-incontinence": "Urinary Incontinence", "urine-properties": "Urine Properties", "urine-volume": "Urine Volume", "stature": "Stature", "body-weight": "Body Weight", "body-fat": "Body Fat", "bmi": "BMI", "upper-arm-circumference": "Upper Arm Circumference", "triceps-skin-thickness": "Triceps Skin Thickness", "brachialis-muscle-area": "Brachialis Muscle Area", "weight-difference-last-time": "Weight Difference Last Time", "activities": "Activities", "participation": "Participation", "dressing-materials-etc-or-medicine": "Dressing Materials Etc / Medicine", "medicine-name": "Medicine Name", "wake-up": "Wake Up", "before-bed": "Before Bed", "tools-used-etc": "Tools Used Etc", "observation": "Observation", "body-region": "Body Region", "body-pressure": "Body Pressure", "state": "State", "detail": "Detail", "implementation-order": "Implementation Order", "cancel-reason": "Cancel Reason", "special-note": "Special Note", "findings": "Findings", "factor": "Factor", "category": "Category", "risk-level": "Risk Level", "near-miss": "Near Miss", "kind": "Kind", "matters-to-be-handovered": "Matters To Be Handovered", "destination": "Destination", "recreation-abbreviation": "Rec", "handouts": "Handouts", "announcements-from-staff": "Announcements From Staff", "todays-menu": "Todays Menu", "living-situation-at-home": "Living Situation At Home", "image-import": "Image import", "video-import": "Video import", "seal-registration": "Seal Registration", "rehabilitation-full": "Rehabilitation", "improved-nutritional-status": "Improved nutritional status", "skin-care": "Skin care", "on-the-chair": "On the chair", "on-the-bed": "On the bed", "pocket-cm2": "Pocket(cm2)", "pocket-cm2-supplement": "(pocket diameter x maximum diameter\r\nperpendicular to the diameter)\r\n - ulcer area", "necrotic-tissue": "Necrotic tissue", "granulation": "Granulation", "granulation-supplement": "Percentage of benign\r\n granulation tissue", "inflammation-infection": "Inflammation/infection", "size-cm2": "Size(cm2)", "size-cm2-supplement": "Diameter x maximum diameter\r\n perpendicular to the diameter", "exudate": "Exudate", "depth": "De<PERSON><PERSON>", "edema-button": "- Edema (non-localized areas)", "skin-moisturizing-button": "- Skin moisture (excessive sweating, \r\n  urinary incontinence, fecal incontinence)", "poor-nutritional-status-button": "- Poor nutritional status", "joint-contracture-button": "- Joint contracture", "pathologic-bone-prominence-button": "- Pathologic bone prominence", "chair-button": "　　　　　　　　　　　　　 (On a chair: Maintaining a sitting position, relieving pressure)", "basic-movement-ability-button": "- Basic movement ability (changing position in bed by oneself)", "date-of-onset-of-bedsore": "Date of onset of bedsore", "heel": "<PERSON><PERSON>", "greater-trochanter": "Greater trochanter", "ilium": "Ilium", "coccyx-area": "Coccyx area", "ischial-region": "Ischial region", "sacral-region": "Sacral region", "past-2": "2.Past", "current-1": "1.Current", "entry-manager": "Entry manager", "ward": "Ward", "narrow-down": "Narrow down", "diagnosis-plan-list": "Diagnosis plan list", "presence-or-absence-of-bedsore": "Presence or absence of bedsore", "evaluation-risk-factor": "Evaluation risk factor", "everyday-life-independence-level": "Everyday life independence level", "handle": "<PERSON><PERSON>", "handle-description": "If one or more of the \r\nfollowing are answered \r\n'Yes' or 'No,' a nursing \r\ncare plan will be \r\ndeveloped and implemented.\r\n", "evaluation-state-bedsore": "Evaluation state bedsore", "nursing-plan": "Nursing plan", "heed-item": "Heed item", "plan-contents": "Plan contents", "elimination-of-pressure-and-shear-forces": "\r\nEliminating pressure and shear forces\r\n\r\n(position changes, pressure-relieving bedding,\r\n head-raising methods, maintaining wheelchair\r\n posture, etc.)\r\n", "entry-attention-matter": "(Notes on writing)\r\n1 Regarding the use of the 'Criteria for Determining the Degree of Independence in Daily Living for Disabled Elderly (Degree of Bedriddenness)' when assessing the degree of independence in daily living\r\n  Please refer to (Notification from Director of the Elderly Insurance and Welfare Department, Minister's Secretariat, Ministry of Health and Welfare, No. 102-2, November 18, 1991).\r\n2 For patients whose level of independence in daily living is J1 to A2, there is no need to prepare such a plan.\r\n", "office-selection-screen": "Office Selection Screen", "package-plan": "Package Plan", "care": "Care", "nutritional": "Nutritional", "info": "info", "warning": "warning", "currently-displayed-plan-document": "The currently displayed plan document.", "reha": "<PERSON><PERSON>", "bedsore": "Bedsore", "time-zone": "Time Zone", "bed-selection": "Bed Selection", "room-rate": "Room rate", "capacity": "Capacity", "selected-room-name": "Selected room name", "room-name": "Room name", "next-usage-date": "Next usage date", "screen": "Screen", "for-instructions": "For instructions", "for-handover": "For handover", "exercise": "Exercise", "within-the-month-nighttime-home-visit-nursing-care-use": "Within The Month Nighttime Home Visit Nursing Care Use", "within-the-month-home-visit-bathing-use": "Within The Month Home Visit Bathing  Use", "within-the-month-home-visit-nursing-use": "Within The Month Home Visit Nursing Use", "within-the-month-day-service-use": "Within the month Day service use", "within-the-month-small-scale-multi-functional-use": "Within the month small scale multi functional use", "within-the-month-home-visit-reha-use": "Within the month home visit reha use", "within-the-month-home-recuperation-use": "Within the month home recuperation use", "within-the-month-welfare-equipment-use": "Within the month welfare equipment use", "home-visit-nursing-insurance-demand": "Visiting nursing insurance claim", "staff-select": "Staff Select", "staff-furigana": "Staff Furigana", "nursing-instructions-past-medical-history-screen": "Nursing Instructions Past Medical History Screen", "nursing-instructions-attending-physician-screen": "Nursing Instructions Attending Physician Screen", "room": "Room", "offer": "Offer", "independence-support-user": "Independence support user", "account-info-furigana-name": "Account info furigana name", "account-info-bank-code": "Account info bank code", "consignor-info-furigana-name": "Consignor info furigana name", "consignor-info-code": "Consignor info code", "payment-method": "Payment method", "main-auxiliary": "Main-Auxiliary", "survey-manager": "Survey manager", "hospitalization": "Hospitalization", "visiting-the-hospital": "Visiting the hospital", "discharge-from-hospital": "Discharge from hospital", "house-calls": "House calls", "complete-recovery": "Complete recovery", "death": "Death", "side-disease": "Side disease", "main-disease": "Main disease", "demand-year-month": "Demand year month", "time-limit-exceeded": "Time limit exceeded", "overdue-invoices": "Overdue invoices", "usage-fee-items": "Usage fee items", "utility-expenses": "Utility expenses", "nursing-care-service-fee": "Nursing care service fee", "home-visit-nursing-medical-expenses": "Home visit nursing medical expenses", "welfare-equipment-merchandise": "Welfare equipment merchandise", "home-visit-class": "Home visit class", "care-manager": "Care Manager", "room-classification": "Room classification", "business-operators": "Business operators", "number": "Number", "only-staff-with-account": "Only Staff With Account", "only-staff-scheduled-today": "Only Staff Scheduled Today", "start-time": "Start time", "end-time": "End time", "filtering-settings": "Filtering Settings", "ommited-care-manager-package-plan": "Ommited care manager package plan", "nutritional-care": "Nutritional care", "bedsore-care": "Bedsore care", "implementation-record-data": "Implementation record data", "accident-class": "Accident class", "reha-record-data": "Reha record data", "ksg-environment": "Ksg environment", "error": "Error", "day-will-look-at-your-usage-history": "※Day will look at your usage history", "certification-end": "※If you uncheck it, it will remain valid until the certification is completed", "show-approved": "Show [Approved] for users who have been using the service for less than a month", "the-item-content-filtering-settings": "The item content filtering settings will be reset when the system starts", "those-planning-to-start-within-days": "Those planning to start within days", "terminated-within-months": "Terminated within months", "risk": "Risk", "currently-displayed-diagnosis-plan-document": "The currently displayed diagnosis plan document", "diagnosis-plan-document": "Diagnosis plan document", "certification-badge-shin": "A", "certification-badge-nin": "C", "certification-badge-mi": "P", "zeigetutaisyo-badge-tai": "D", "riyoukaisi-mae-badge-zen": "B", "currently-displayed-comprehensive-plan-document": "The currently displayed comprehensive plan document.", "comprehensive-implementation-plan": "Comprehensive-implementation-plan", "login-staff-switching": "Login staff switching", "job": "JoB", "people": "People", "use": "Use", "applying-office": "Applying office", "office-selection": "Office Selection", "umare": "birth", "device-type": "Device Type", "login-result": "<PERSON><PERSON>t", "success": "Success", "failure": "Failure", "login-date-and-time": "Login Date And Time", "login-user": "Login User", "ip-address": "IP Address", "terminal-number": "Terminal Number", "display-number": "Display Number", "system-management": "System Management", "login-history": "Login History", "operation-history": "Operation History", "use-not-yet-applied-display": "Use Not Yet Applied Display", "applicable-office-waiting-person-display": "Applicable Office Waiting Person Display", "other-office-waiting-person-display": "Other Office Waiting Person Display", "certification-end-check-on": "Certification End Check On", "less-than-a-month": "Less Than A Month", "access-date-and-time": "Access Date And Time", "access-type": "Access Type", "interface-type": "I/F Type", "access-url": "Access URL", "db-number": "DB Number", "system-admin-login": "System Administrator Login <PERSON>", "system-admin-login-id": "System Administrator Login ID", "forgot-your-login-id": "Forgot your login ID", "forgot-login-id": "Forgot your login ID", "forgot-your-password": "Forgot your password", "forgot-password": "Forgot your password", "confirmation-of-edits": "Confirmation of edits", "mail-address": "Email address", "master": "Master", "function": "Function", "function-permission": "Function Permission", "registration": "Registration", "brackets-all": "（ALL）", "menu": "<PERSON><PERSON>", "functional-classification": "Functional Classification", "functional-name": "Functional Name", "external-output": "External Output", "view": "View", "use-label": "Use", "security-groups": "Security Groups", "functional-group": "Functional Group", "affiliated-staff": "Affiliated staff", "staff-management": "Staff Management", "working-style": "Working Style", "permission": "Permission", "bulk-permission-settings": "Bulk permission settings", "staff-edit": "Staff Edit", "new-password": "New Password", "re-enter-new-password": "Re-enter new password", "full-time": "Full Time", "administrator": "Administrator", "general": "General", "valid": "<PERSON><PERSON>", "invalid": "Invalid", "account": "account", "registration-date": "Registration date", "***end***": "***end-of-label***"}, "tooltip": {"display-print-settings-screen": "Display Print Settings Screen", "open-the-map-in-a-new-tab": "Open the map in a new tab", "launch-the-mail-app": "Launch the Mail app (the default app) and compose an email", "show-image-by-double-click": "Show Image By Double Click", "display-planning-period-screen": "Display Planning Period Screen", "display-year-month-select-screen": "Display Year-Month Select Screen", "display-staff-select-screen": "Display Staff Select Screen", "change-row-height": "Change Row Height", "display-office-select-screen": "Display Office Select Screen", "tooltip.displays-the-print-settings-screen": "Displays the print settings screen.", "tooltip.reduces-the-display-magnification": "Reduces the display magnification.", "tooltip.set-the-display-magnification-to-the-original-size-100": "Set the display magnification to the original size (100%).", "tooltip.increases-the-display-magnification": "Increases the display magnification.", "tooltip.displays-the-menu-other-functions-call-screen": "Displays the menu (other functions) call screen.", "tooltip.displays-the-image-settings-screen": "Displays the image settings screen.", "tooltip.displays-a-screen-for-selecting-the-applicable-business-location": "Displays a screen for selecting the applicable business location.", "refresh-the-screen": "Refresh the screen.", "the-time-setting-screen-is-displayed": "The time setting screen is displayed.", "select-the-file": "Select the file.", "open-the-file": "Open the file.", "display-the-disease-name-selection-screen": "Display the disease name selection screen.", "display-the-calendar-screen": "Display the calendar screen.", "the-medical-institution-selection-screen-will-be-displayed": "The medical institution selection screen will be displayed.", "the-location-import-screen-will-be-displayed": "The location import screen will be displayed.", "add-bookmark": "Add bookmark", "release-bookmark": "Release Bookmark", "display-button-call-other-features": "Display button call other features", "display-option-function": "Display option function", "displays-the-color-setting-screen": "Displays the color setting screen.", "switching-apply-office": "Switching apply office", "display-before-day": "Display before day", "display-next-day": "Display next day", "***end***": "***tooltipの終端***※競合防止のため、この行の前に追加してください。"}, "validation": {"required-msg": "This field is required.", "alpha": "Please enter in alphabet.", "alpha-dash": "Please enter alphabets or \"-\" or \"_\".", "alpha-num": "Please enter alphabets or numbers.", "alpha-space": "Please enter alphabets or spaces.", "between": "Please enter a number between {min} and {max}.", "confirmed": "Enter a value that matches the {targetTitle} value.", "digits": "Please enter a number with {length} digits.", "dimensions": "Please enter an image with width {width} and height {height}.", "email": "Please enter in email address format.", "not-one-of": "Invalid input value.", "ext": "Please enter a file with the extension {extensions}.", "image": "Please enter an image file.", "integer": "Please enter an integer value.", "is": "Enter a value that matches {other}.", "is-not": "Please enter a value that does not match {other}.", "length": "Please enter {length} characters.", "max": "Please enter {length} characters or less.", "max-value": "Please enter a number less than or equal to {max}.", "mimes": "Please enter a value for MIME type {mimes}.", "min": "Please enter at least {length} characters.", "min-value": "Please enter a number equal to or greater than {min}.", "numeric": "Please enter a numerical value.", "one-of": "Invalid input value.", "regex": "Enter a value that matches the regular expression {pattern}.", "size": "Please enter a file that is less than or equal to {size} in size.", "url": "Invalid URL.", "ban": "It contains invalid characters.", "date-max": "Please enter a date earlier than {max}.", "date-min": "Please enter a date on or after {min}.", "date-range": "Please enter a date between {min} and {max}.", "date-ym": "Please enter the date in YYYY/MM format.", "date-ymd": "Please enter the date in YYYY/MM/DD format.", "half-kana": "Please enter in half-width kana.", "zenkaku": "Please enter full-width characters.", "date-and-time": "Please enter an end date that is later than the start date, and the period between the start date and the end date must be within {date} days.", "byte-length": "Please enter {zenkakuLength} full-width character or less, {halfLength} half-width character or less.", "***end***": "***end-of-validation***"}, "btn": {"new": "new", "modified": "modified", "group": "group", "create-folder": "create folder", "print": "print", "invalid": "invalid", "master": "master", "personal-folder": "personal folder", "care-manager-in-charge": "Care manager in charge", "care-manager-history": "history", "delete": "delete", "save": "save", "copy": "copy", "ok": "OK", "input-support": "Input support", "batch-printing-of-plans": "Batch printing", "delete-row": "Delete row", "add-row": "Add row", "insert-row": "Insert row", "duplicate-row": "Duplicate row", "case-import": "Case import", "display-order": "Display order", "organizing-issues": "Organizing issues", "assessment": "Assessment", "standard-care-plan": "Standard care plan", "back": "back", "confirm": "Confirm", "close": "close", "display-hierarchy": "Display hierarchy", "examination-issues": "Examination of issues", "history": "History", "cancel": "cancel", "yes": "yes", "no": "no", "security-settings": "Security settings", "area-settings": "Area settings", "own-corporation-target": "Own corporation target", "external-corporation-target": "External corporation target", "basic-situation": "Basic situation", "discharge-information": "Discharge information", "overwrite-with-d-arrow": "overwrite↓", "append-with-d-arrow": "append↓", "redisplay": "redisplay", "things-to-keep-in-mind": "Things to keep in mind", "long-term-goal": "Long term goal", "meeting-minutes": "meeting minutes", "inquiry-details": "inquiry details", "detail-import": "detail import", "assignment-import": "assignment import", "attendees": "attendees", "select-all": "select all", "full-release": "full release", "inversion": "inversion", "kinship": "Kinship", "doctor": "Doctor", "staff": "Staff", "overwrite": "Overwrite", "insert": "Insert", "enter-from-zip-code": "Enter from zip code", "previous-page": "Previous page", "next-page": "Next page", "printer-settings": "Printer <PERSON>s", "log": "Log", "nursingCareInsuranceValidEndDateList": "nursingCareInsuranceValidEndDateList", "lastNursingCareInsuranceList": "lastNursingCareInsuranceList", "insuredPersonNmberList": "insuredPersonNmberList", "nursingCareCertificationValidEndDateList": "nursingCareCertificationValidEndDateList", "nursingCareCertificationValidStartDateList": "nursingCareCertificationValidStartDateList", "validNursingCareInsuranceList": "validNursingCareInsuranceList", "medicalInsuranceValidEndDateList": "medicalInsuranceValidEndDateList", "oldmanInsuranceValidEndDateList": "oldmanInsuranceValidEndDateList", "lastOldmanInsuranceList": "lastOldmanInsuranceList", "publiclyFundedValidEndDateList": "publiclyFundedValidEndDateList", "lifeInsuranceSingleCombinedUseList": "lifeInsuranceSingleCombinedUseList", "lastPubliclyFundedList": "lastPubliclyFundedList", "hibakushaGrantList": "hibakushaGrantList", "monthlyResidenceHistyList": "monthlyResidenceHistyList", "burdenLimitValidDeadlineList": "burdenLimitValidDeadlineList", "burdenLimitApplicableDateList": "burdenLimitApplicableDateList", "careManagerInChargeUserList": "careManagerInChargeUserList", "serviceUserList": "サービス利用者一覧", "serviceUserNumberOfPeopleList": "serviceUserNumberOfPeopleList", "requiredNursingCareUserList": "requiredNursingCareUserList", "requiredNursingCareUserNumberOfPeopleList": "requiredNursingCareUserNumberOfPeopleList", "requiredNursingCareServiceUserList": "requiredNursingCareServiceUserList", "requiredNursingCareServiceUserNumberOfPeopleList": "requiredNursingCareServiceUserNumberOfPeopleList", "point": ".", "bs": "bs", "execution": "execution", "add": "add", "import": "Import", "transfer": "Transfer", "seal-column": "Seal Column", "cancel-knj": "Cancel", "initial-value": "Initial Value", "delete-all": "Delete All", "clear-kana": "Clear", "erase-all": "Erase All", "erase": "Erase", "preview": "Preview", "edit-source": "Edit Source", "priority-deletion": "Priority Deletion", "delete-display-order": "Delete Display Order", "export": "Export", "all-wrong": "All[×]", "all-round": "All[〇]", "search": "Search", "week": "Week", "month": "Month", "export-csv": "Export Csv", "summary": "Summary", "office": "Office", "still-image": "Still Image", "movie": "Movie", "calculation": "Calculation", "display-setting": "Display Setting", "line-copy-assignment": "Line Copy (Assignment)", "copy-last-time": "Copy Last Time", "create": "Create", "path": "Path", "zoom-in": "Enlarge", "zoom-out": "Shrink", "frame-matching": "Frame matching", "original-size": "Actual size", "understand": "Understand", "adl-evaluation": "ADL Evaluation", "upload": "upload", "user-list-search": "User List Search", "parts-reference": "Parts Reference", "selected": "selected", "continue-editing": "Continue editing", "discard": "Discard", "send": "Send", "copy-to-other-offices": "Copy to other offices", "edit": "Edit", "terminal": "***end-of-btn***"}, "message": {"convert-full-width": "Due to the input of full-width characters, they will be replaced with half-width characters.", "updated": "Has been updated", "rquired-error": "There are unentered data items", "no-item-select-delete-error": "No items are selected for deletion.", "delete-row": "Trying to delete a row.\r\nIf I delete it, I cannot revert.\r\nAre you sure you want to delete it?", "save-confirm": "If you select another item, your changes will be lost.\r\nDo you want to save changes?", "save": "Has been saved", "delete-confirm-document": "Delete the currently displayed {document} data on [{date}].\r\nIs that OK?", "delete-confirm-select-row": "Delete the selected row. Is it OK?", "unregistered-plan-target-period": "Please register the period from the selection button for the planning period.", "care-plan1-new-plan-unsaved": "The newly created plan (1) has not been saved. Please create a new one after saving.", "select-planning-period": "Please register the period by clicking the Plan Period button.", "no-data": "No Data", "i-ldg-10031": "You only need to finalise (save) once to edit.\r\nWould you like to confirm this?", "i-ldg-10032": "Cancel the selected history. Are you sure?", "i-ldg-10033": "A still image with the same name already exists. Do you want to overwrite it?", "i-ldg-10034": "A video with the same name already exists. Do you want to overwrite it?", "i-ldg-10218": "There's already a text. Do you want to overwrite it?", "i-ldg-10219": "Deletes the data in the current row.\r\nIs that okay?", "i-ldg-10430": "If you select another item, your changes will be lost. \r\n Do you want to save the changes?", "i-ldg-10665": "Delete file name: {msg0}. Are you sure?", "i-ldg-11271": "{msg0} is not selected.\r\n Do you want to exit anyway?", "i-ldg-11334": "This image cannot be used because it has not been correctly compressed to JPEG format.", "i-ldg-11374": "The maximum number of image management items is {msg0}. \r\nPlease deselect the selected image management items and try again.", "i-ldg-40033": "Failed to save the still image.", "i-ldg-40034": "Failed to import the still image.\n{msg0}", "i-ldg-40035": "Failed to import the video.\n{msg0}", "i-scd-11068": "There are no records after this recording date.", "i-scd-11069": "There are no records before this recording date.", "i-scd-11143": "Do you want to delete the data of {msg0}?\r\n Are you sure?", "i-ldg-11331": "Delete image.\r\nAre you sure?\r\n*If you click [Yes], the image cannot be undone.", "i-scd-11379": "Are you sure you want to overwrite the current display with the record of {msg0}?", "w-ldg-20078": "It is not possible to activate the applicable record screen at the applicable user service office at the applicable facility.", "w-ldg-20079": "The History Association setting is not available for users outside of the applicable office.", "w-ldg-20080": "It is not possible to remove a user's history from an office that is not applicable.", "w-ldg-20081": "The applicable user service office at the applicable facility cannot delete the applicable record history.", "w-ldg-20082": "Cannot import because a video with the same name already exists.", "w-ldg-20083": "Cannot import because a still image with the same name already exists.", "w-ldg-20429": "The item has already been registered in a hierarchy higher than the one set. Please delete the unnecessary hierarchy in the text master and set it again.", "w-ldg-20590": "Please enter the time within the range of 0 to 23.", "w-ldg-20591": "Please enter the minutes within the range of 0 to 59.", "w-ldg-20810": "Deletes the selected image.\r\nIs this ok?\r\n\r\n※If you click Yes, there is no undo.", "w-scd-20572": "Only one {msg0} document can be registered per recording date.", "e-ldg-40172": "Unable to read the information on the form.", "e-ldg-40173": "Incorrect time entry.", "e-ldg-41210": "The name of the person in charge of filling out the form is required to update it. Please enter the person of record.", "e-ldg-41286": "Failed to import image data.", "e-ldg-41410": "The image has been removed.\r\nClick on the “Confirm” button or the “Close” button to close.", "e-com-10013": "Please enter in the time format (00:00).", "e-com-10030": "The office has not been selected. \nPlease select an office.", "e-scd-41209": "One or more signatures are required to update. Please enter the recorder's name.", "e-scd-41342": "There are no records before this recording date. No copies have been made.", "e-scd-41503": "No user has been selected. The update process will not be carried out.", "process-complete": "Processing completed.", "no-permission": "You do not have permission to access the feature.", "searching": "Searching...", "no-data-search-criteria": "There is no data that matches your search criteria.", "to-system-admin-login": "To the system administrator login screen", "to-normal-login": "To the normal login screen", "contact-email-address-send": "Enter the contact email address you registered when creating the system administrator and click the Send button.", "receive-email-admin-login-id": "You will receive an email with the system administrator's login ID.", "login-id-and-contact-email-address-send": "Enter the system administrator's login ID and the contact email address registered when creating the system administrator, and click the Send button.", "email-with-url-password": "You will receive an email with a URL for resetting your password.", "follow-email-reset-password": "Please follow the instructions in the email to reset your password.", "error-in-email-address": "There is an error in your email address. Please make sure you entered it correctly.", "email-sent": "Email sent", "email-check-login-id": "Please open the email and check your login ID.", "error-in-login-id-email-address": "Your login ID or email address is incorrect. Please make sure you have entered it correctly.", "check-email-reset-password": "Please check the contents of the email and reset your password.", "password-reset-url-1-hour": "*The password reset URL expires in 1 hour. Please reset your password within 1 hour after the email is sent.", "currently-selected-employee-permission-settings": "The currently selected employee's permission settings will be copied to other employees. Are you sure?", "copy-both-authority-business-establishment-function": "[YES]：Copy both the authority of the business establishment and the function.", "duplicate-functional-permissions-only": "[NO]：Duplicate functional permissions only.", "suspends-processing": "[CANCEL]：Suspends processing.", "copied-information-automatically-updated": "*The copied information is automatically updated.", "loading-dot": "Loading...", "moving-to-home-screen": "Moving to home screen.", "enter-a-password-of-4-to-20-characters": "Please enter a password of 4 to 20 characters, including alphanumeric characters, underscores (_), and hyphens (-).", "passwords-do-not-match": "Passwords do not match. Please enter the same password as New Password.", "***end***": "***messageの終端***※競合防止のため、この行の前に追加してください。"}}