/**
 * OrX0217:処理ロジック
 * GUI01272_認定調査票特記事項
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR>
 */
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or27761Const } from '../Or27761/Or27761.constants'
import { Or27761Logic } from '../Or27761/Or27761.logic'
import { OrX0217Const } from './OrX0217.constants'
import { useInitialize } from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'

export namespace OrX0217Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: OrX0217Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(0) },
        { cpId: Or51775Const.CP_ID(0) },
        { cpId: Or27761Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or21813Logic.initialize(childCpIds.Or21813.uniqueCpId)
    Or21814Logic.initialize(childCpIds.Or21814.uniqueCpId)
    Or51775Logic.initialize(childCpIds.Or51775.uniqueCpId)
    Or27761Logic.initialize(childCpIds.Or27761.uniqueCpId)

    // 子コンポーネントのセットアップ
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
}
