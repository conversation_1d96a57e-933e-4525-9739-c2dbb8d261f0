<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick} from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import type { VForm } from 'vuetify/components'
import { Or28455Const } from '../Or28455/Or28455.constants'
import { Or28455Logic } from '../Or28455/Or28455.logic'
import { Or31926Const } from '../Or31926/Or31926.constants'
import { Or28206Const } from '../Or28206/Or28206.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or27235Const } from '../Or27235/Or27235.constants'
import { Or27235Logic } from '../Or27235/Or27235.logic'
import { Or28206Logic } from '../Or28206/Or28206.logic'
import type { OrX0060StateType } from '../OrX0060/OrX0060.type'
import { OrX0060Const } from './OrX0060.constants'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { UPDATE_KBN, DIALOG_BTN } from '~/constants/classification-constants'
import type {
  OrX0060Type,
  GamenDataInfo,
  OrX0060OnewayType,
  GamenRirekiOtherInfo
} from '~/types/cmn/business/components/OrX0060Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { OrX0002OnewayType } from '~/types/cmn/business/components/OrX0002Type'
import type { Or28455OnewayType, Or28455Type } from '~/types/cmn/business/components/Or28455Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { Or27235Type, Or27235OnewayType } from '~/types/cmn/business/components/Or27235Type'
import type { Or28206Type, Or28206OnewayType } from '~/types/cmn/business/components/Or28206Type'
import {
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
  useGyoumuCom,
  useScreenOneWayBind,
} from '#imports'
import { CustomClass } from '~/types/CustomClassType'
import type { OrX0156OnewayType, OrX0156Type } from '~/types/cmn/business/components/OrX0156Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type {
  Mo01354OnewayType,
  Mo01354Type,
  Mo01354Items,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'
import { useValidation } from '@/utils/useValidation'
import type { OrX0163OnewayType } from '~/types/cmn/business/components/OrX0163Type'
import type { OrX0218OnewayType } from '~/types/cmn/business/components/OrX0218Type'
/**
 * OrX0060:有機体:操作ボタンエリア、テーブルエリア
 * GUI00946_実施計画～①
 *
 * @description
 * 操作ボタンエリア、テーブルエリア
 *
 * <AUTHOR>
 */
const { t } = useI18n()
const { byteLength } = useValidation()
const form = ref<VForm>()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}
/** props */
const props = defineProps<Props>()
/**
 *システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 *共有処理
 */
const gyoumuCom = useGyoumuCom()

/**
 *各コンポーネントに渡すonewayデータ
 */
const localOneway = reactive({
  orX0060Oneway: {} as OrX0060OnewayType,
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    tooltipText: t('tooltip.care-plan2-newline-btn'),
    tooltipLocation: 'bottom',
    minWidth: '89px',
  } as Mo00611OnewayType,
  mo00611OnewayInsert: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    tooltipText: t('tooltip.care-plan2-insertline-btn'),
    tooltipLocation: 'bottom',
    minWidth: '89px',
  } as Mo00611OnewayType,
  mo00611OnewayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    tooltipText: t('tooltip.care-plan2-cpyline-btn'),
    tooltipLocation: 'bottom',
    disabled: true,
    minWidth: '89px',
  } as Mo00611OnewayType,
  mo00611OnewayDelete: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    color: 'error',
    labelColor: 'error',
    tooltipText: t('tooltip.care-plan2-deleteline-btn'),
    tooltipLocation: 'bottom',
    disabled: true,
    minWidth: '89px',
  } as Mo00611OnewayType,
  // 行削除の確認ダイアログ
  orX0002DeleteLineOneway: {
    message: t('message.i-cmn-10219'),
  } as OrX0002OnewayType,
  // 課題取込
  mo00611OnewayImport: {
    btnLabel: t('btn.issues-import'),
    disabled: false,
    tooltipText: t('tooltip.assignment-import-btn'),
    tooltipLocation: 'bottom',
    minWidth: '79px',
  } as Mo00611OnewayType,
  // 表示順
  mo00611OnewayOrder: {
    btnLabel: t('btn.display-order'),
    disabled: false,
    tooltipText: t('tooltip.care-plan2-display-order-icon-btn'),
    tooltipLocation: 'bottom',
    minWidth: '65px',
  } as Mo00611OnewayType,
  // データ-ページング
  mo01338Oneway: {
    unit: t('label.item'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-1',
      itemClass: 'ml-4 align-center',
    }),
  } as Mo01338OnewayType,
  // GUI00937 入力支援［ケアマネ］画面
  or51775Oneway: {} as Or51775OnewayType,
  // GUI00949_課題・目標取込
  or28455Oneway: {} as Or28455OnewayType,
  // GUI00951 表示順変更実施計画～①
  or28206Oneway: {} as Or28206OnewayType,
  // Or27235:GUI01021_担当者入力支援
  or27235Oneway: {} as Or27235OnewayType,
  // 一覧項目
  orX0163Oneway: {
    maxlength: '32000',
    rules: [byteLength(32000)],
    contentStyle:'padding:8px 12px',
    height:'115px'
  } as OrX0163OnewayType,
  orX0218Oneway: {
    showEditHelperBtnFlg: true,
    showKikanBtnFlg: false,
    showYmdBtnFlg: true,
    showMonthDayBtnFlg: true,
    maxlength: '32000',
    rules: [byteLength(32000)],
    contentStyle: 'padding:8px 12px',
    height:'115px'
  } as OrX0218OnewayType,
  // 処遇実施上の留意点
  orX0156Oneway: {
    itemLabel: t('label.or31926-considerations'),
    showItemLabel: true,
    isVerticalLabel: true,
    showDividerLineFlg: false,
    rows: '3',
    maxlength: '32000',
    rules: [byteLength(32000)],
    customClass: new CustomClass({ labelClass: 'service-label pt-6 pb-4' }),
    } as OrX0156OnewayType,
  mo01354Oneway: {
    headers: OrX0060Const.INIT_HEADERS,
    columnMinWidth: { columnWidths: [240,240,208, 240, 240] } as ResizableGridBinding,
    height: '100%',
  } as Mo01354OnewayType,
})
/**
 *ローカル状態管理
 */
const local = reactive({
  orX0002DeleteLineDialog: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
  orX0060: { selectedYmdColKbn: '' } as OrX0060StateType,
  // GUI00949_課題・目標取込
  or28455: { items: [], operationKbn: '' } as Or28455Type,
  // GUI00937 入力支援［ケアマネ］画面
  or51775: {} as Or51775Type,
  // Or28206:表示順変更実施計画～①
  or28206: {} as Or28206Type,
  // Or27235:GUI01021_担当者入力支援
  or27235: {} as Or27235Type,
  // 処遇実施上の留意点
  orX0156: { value: '' } as OrX0156Type,
  // 表
  mo01354TbData: {
    values: {
      selectedRowId: '',
      selectedRowIds: [],
      items: [] as Mo01354Items[],
    },
  } as Mo01354Type,
})

const or28455_1 = ref({ uniqueCpId: '' })
const or51775_1 = ref({ uniqueCpId: '' })
const or28206_1 = ref({ uniqueCpId: '' })
const or27235_1 = ref({ uniqueCpId: '' })
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or28455Const.CP_ID(1)]: or28455_1.value,
  [Or51775Const.CP_ID(1)]: or51775_1.value,
  [Or28206Const.CP_ID(1)]: or28206_1.value,
  [Or27235Const.CP_ID(1)]: or27235_1.value,
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr28455 = computed(() => {
  // Or28455のダイアログ開閉状態
  return Or28455Logic.state.get(or28455_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr28206 = computed(() => {
  // Or28206のダイアログ開閉状態
  return Or28206Logic.state.get(or28206_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr27235 = computed(() => {
  // Or27235のダイアログ開閉状態
  return Or27235Logic.state.get(or27235_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
/**
 *refValue
 */
const { refValue } = useScreenTwoWayBind<OrX0060Type>({
  cpId: OrX0060Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
useScreenOneWayBind<OrX0060OnewayType>({
  cpId: OrX0060Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    // 操作区分
    operaFlg: (value) => {
      if (value) {
        localOneway.orX0060Oneway.operaFlg = value
      }
    },
    // ページング区分
    pagingFlg: (value) => {
      if (value) {
        localOneway.orX0060Oneway.pagingFlg = value
      }
    },
    // 種別ID
    syubetuId: (value) => {
      if (value) {
        localOneway.orX0060Oneway.syubetuId = value
      }
    },
    // 期間管理フラグ(1:期間管理する、0:期間管理しない)
    kikanFlg: (value) => {
      if (value) {
        localOneway.orX0060Oneway.kikanFlg = value
      }
    },
    // 課題取込セキュリティチェック（0:権限なし、1以上:権限あり）
    kadaiTorikomiFlg: (value) => {
      if (value) {
        localOneway.orX0060Oneway.kadaiTorikomiFlg = value
      }
    },
    // 初期設定マスタの情報
    initMasterObj: (value) => {
      if (value) {
        localOneway.orX0060Oneway.initMasterObj = value
        // 期間のカレンダー取込み※0:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日
        const ymdFlg = value.pkkak21YmdFlg;
        localOneway.orX0218Oneway.kikanFlg = ymdFlg === '0' || ymdFlg === '1';
        if (ymdFlg === '1' || ymdFlg === '3') {
          localOneway.orX0218Oneway.dateFormat = 'YYYY年MM月DD日';
          localOneway.orX0218Oneway.mdDateFormat = 'MM月DD日';
        } else {
          localOneway.orX0218Oneway.dateFormat = 'YYYY/MM/DD';
          localOneway.orX0218Oneway.mdDateFormat = 'MM/DD';
        }
      }
    },
    // 実施計画～①複写
    isCopyMode: (value) => {
      localOneway.orX0060Oneway.isCopyMode = value ?? false
    },
  },
})

const filteredDataList = computed(() => {
  return (
    refValue.value?.dataList?.filter(
      (i: GamenDataInfo) => i.updateKbn !== UPDATE_KBN.DELETE
    ) ?? []
  )
})

/**
 *課題取込
 */
const importFlg = computed((): boolean => {
  if (
    localOneway.orX0060Oneway.operaFlg === Or31926Const.OPERA_FLG_3 ||
    localOneway.orX0060Oneway.pagingFlg !== Or31926Const.PAGING_1 ||
    localOneway.orX0060Oneway.kadaiTorikomiFlg !== OrX0060Const.KADAI_TORIKOMI_1
  ) {
    return false
  }

  return true
})

/**
 *一覧に表示レコードの表示順の再設定
 */
const updateSeq = () => {
  filteredDataList.value.forEach((item, index) => {
    const dataItem = refValue.value?.dataList.find(i => i.id === item.id) ?? null
    if (!dataItem) return

    const newSeq = index + 1
    if (dataItem.seq !== newSeq) {
      dataItem.seq = newSeq
      handleChange(dataItem)
    }
  })
}

/**
 *表示用「課題目標期間内容担当」リストでの最大の課題番号 + 1
 */
const newKadaiNo = computed(() => {
  const items = refValue.value?.dataList?.filter(
    item => item.updateKbn !== UPDATE_KBN.DELETE
  ) ?? []

  if (!items || items.length === 0) {
    return '1'
  }

  const kadaiNoValues = items
    .map(item => {
      const num = Number(item.kadaiNo)
      return isNaN(num) ? -Infinity : num
    })
    .filter(num => num !== -Infinity)

  if (kadaiNoValues.length === 0) {
    return '1'
  }

  const max = Math.max(...kadaiNoValues)
  return String(max + 1)
})

/**
 * 新規用のidを取得
 */
let newNegativeIdCounter = -1
const getNewNegativeId = (): number => newNegativeIdCounter--

/**
 *行挿入、行複写、行削除の活性制御
 */
const disableButtons = computed(() => {
  return !local.mo01354TbData.values.selectedRowId
})

/**
 *操作区分 = 3:削除の場合、or 表示用「計画対象期間」情報.ページング区分が0:なしの場合、操作不可
 */
const doFlg = computed(
  (): boolean =>
    localOneway.orX0060Oneway.operaFlg !== Or31926Const.OPERA_FLG_3 &&
    localOneway.orX0060Oneway.pagingFlg !== Or31926Const.PAGING_0
)

/**
 *処遇実施上の留意点
 */
const ryuiKnjModel = computed({
  get(): OrX0156Type {
    return { value: refValue.value?.rirekiOtherData?.ryuiKnj ?? '' }
  },
  set(val: OrX0156Type) {
    refValue.value!.rirekiOtherData.ryuiKnj = String(val.value)
  },
})

/**
 *実施計画～①複写の制御
 */
const isCopyMode = computed(() => {
  return localOneway.orX0060Oneway.isCopyMode ?? false
})

/**
 * 表示・非表示制御
 */
watch(
  () => isCopyMode.value,
  (newValue) => {
    localOneway.orX0163Oneway.showEditBtnFlg = !newValue
    localOneway.orX0163Oneway.readonly = newValue

    localOneway.orX0218Oneway.showEditHelperBtnFlg = !newValue
    localOneway.orX0218Oneway.showYmdBtnFlg = !newValue
    localOneway.orX0218Oneway.showMonthDayBtnFlg = !newValue
    localOneway.orX0218Oneway.showCalendarBtnFlg = !newValue
    localOneway.orX0218Oneway.readonly = newValue

    localOneway.orX0156Oneway.showEditBtnFlg = !newValue
    localOneway.orX0156Oneway.readonly = newValue
  },
  { immediate: true }
)

/**
 * 一覧の監視
 */
watch(
  () => filteredDataList.value,
  (newItems) => {
    local.mo01354TbData.values.items = newItems

    if (newItems.length === 0) {
      local.mo01354TbData.values.selectedRowId = ''
      local.mo01354TbData.values.selectedRowIds = []
    } else {
      const firstItemId = newItems[0].id
      const selectedId = local.mo01354TbData.values.selectedRowId
      const hasSelectedId = selectedId !== null && selectedId !== ''

      if (hasSelectedId) {
        const isSelectedIdValid = newItems.some(item => item.id === selectedId)
        if (!isSelectedIdValid) {
          local.mo01354TbData.values.selectedRowId = firstItemId
          local.mo01354TbData.values.selectedRowIds = [firstItemId]
        }
      } else {
        local.mo01354TbData.values.selectedRowId = firstItemId
        local.mo01354TbData.values.selectedRowIds = [firstItemId]
      }
    }

    updateSeq()
  },
  { immediate: true }
)

// 「行削除ボタン」押下メッセージの場合
watch(
  () => local.orX0002DeleteLineDialog.emitType,
  (value) => {
    if (value === DIALOG_BTN.OK) {
      const { selectedRowId } = local.mo01354TbData.values
      if (!selectedRowId || !refValue.value) return

      const currentVisibleItems = filteredDataList.value
      const currentIndex = currentVisibleItems.findIndex(item => item.id === selectedRowId)
      if (currentIndex === -1) return

      const dataItem = refValue.value.dataList.find(i => i.id === selectedRowId)
      if (!dataItem) return

      dataItem.updateKbn = UPDATE_KBN.DELETE

      const newVisibleItems = refValue.value.dataList
        .filter(item => item.updateKbn !== UPDATE_KBN.DELETE)
        .map(item => ({ ...item}))

      // 削除した後データが無し
      let newIndex = -1
      if (newVisibleItems.length > 0) {
        if (currentIndex < newVisibleItems.length) {
          // 次レコードを選択
          newIndex = currentIndex
        } else {
          // 前のレコードを選択
          newIndex = currentIndex - 1
        }
      }

      local.mo01354TbData.values.selectedRowId =
        newIndex >= 0 ? newVisibleItems[newIndex]?.id ?? '' : ''

      local.orX0002DeleteLineDialog.mo00024.isOpen = false
    }
  }
)

/**
 * GUI00951 表示順変更実施計画～①(処遇内容)ポップアップ画面で返回
 */
watch(
  () => local.or28206.sortList,
  (newValue) => {
    if (newValue) {
      for (const newItem of newValue) {
        if (newItem.sortBackup) {
          const sortBackup = newItem.sortBackup
          const data = refValue.value!.dataList.find((x) => x.id === sortBackup)
          if (data) {
            data.seq = newItem.sort
            // 更新区分を更新
            handleChange(data)
          }
        }
      }
      refValue.value!.dataList = [...refValue.value!.dataList].sort((a, b) => a.seq - b.seq)
      const visibleItems = filteredDataList.value
      local.mo01354TbData.values.selectedRowId = visibleItems.length > 0 ? visibleItems[0].id : ''
    }
  }
)

/**
 *行追加
 */
function addRow() {
  if (!doFlg.value) {
    return
  }
  // 最終に新しい行を追加し、追加行を選択状態とする。
  const newRow: GamenDataInfo = {
    id: getNewNegativeId().toString(),
    kadaiKnj: { value: '' },
    mokuhyoKnj: { value: '' },
    kikanKnj: { value: '' },
    naiyoKnj: { value: '' },
    tantoShokuKnj: { value: '' },
    kadaiNo: String(newKadaiNo.value),
    id2: '-1',
    id3: '-1',
    modifiedCnt2: '0',
    modifiedCnt3: '0',
    updateKbn: UPDATE_KBN.CREATE,
    seq: filteredDataList.value.length + 1
  }
  refValue.value?.dataList.push(newRow)

  local.mo01354TbData.values.selectedRowId = newRow.id
}

/**
 *行挿入
 */
function insertRow() {
  const { selectedRowId } = local.mo01354TbData.values
  if (!doFlg.value || !selectedRowId || !refValue.value) {
    return
  }
  const insertPosition = refValue.value.dataList.findIndex(
    (i) => i.id === selectedRowId
  )
  if (insertPosition === -1) return

  const logicalIndex = filteredDataList.value.findIndex(
    item => item.id === selectedRowId
  )
  if (logicalIndex === -1) return

  // 選択行の上に新しい行を追加し、追加行を選択状態とする。
  const newRow: GamenDataInfo = {
    id: getNewNegativeId().toString(),
    kadaiKnj: { value: '' },
    mokuhyoKnj: { value: '' },
    kikanKnj: { value: '' },
    naiyoKnj: { value: '' },
    tantoShokuKnj: { value: '' },
    kadaiNo: String(newKadaiNo.value),
    id2: '-1',
    id3: '-1',
    modifiedCnt2: '0',
    modifiedCnt3: '0',
    updateKbn: UPDATE_KBN.CREATE,
    seq: logicalIndex + 1,
  }

  // 指定行の前に1行を追加する
  refValue.value.dataList.splice(insertPosition, 0, newRow)
  local.mo01354TbData.values.selectedRowId = newRow.id
}

/**
 *行複写
 */
function copyRow() {
  const { selectedRowId } = local.mo01354TbData.values
  if (!doFlg.value || !selectedRowId || !refValue.value) {
    return
  }

  // 選択対象のインデックス
  const sourcePosition = refValue.value.dataList.findIndex(
    (i) => i.id === selectedRowId
  )
  if (sourcePosition === -1) return

  // copy対象
  const sourceLine = refValue.value.dataList[sourcePosition]
  if (!sourceLine) return

  // 選択対象のseq
  const logicalIndex = filteredDataList.value.findIndex(
    item => item.id === selectedRowId
  )
  if (logicalIndex === -1) return
  const sourceLineSeq = logicalIndex + 1

  // 選択行の情報をコピーして追加行を選択状態とする。
  const cpyLine: GamenDataInfo = {
    id: getNewNegativeId().toString(),
    kadaiKnj: { value: sourceLine.kadaiKnj.value },
    mokuhyoKnj: { value: sourceLine.mokuhyoKnj.value },
    kikanKnj: { value: sourceLine.kikanKnj.value },
    naiyoKnj: { value: sourceLine.naiyoKnj.value },
    tantoShokuKnj: { value: sourceLine.tantoShokuKnj.value },
    kadaiNo: String(newKadaiNo.value),
    id2: '-1',
    id3: '-1',
    modifiedCnt2: '0',
    modifiedCnt3: '0',
    updateKbn: UPDATE_KBN.CREATE,
    seq: sourceLineSeq + 1,
  }

  // 指定行の次に1行を追加する
  const insertPosition = sourcePosition + 1

  refValue.value.dataList.splice(insertPosition, 0, cpyLine)

  local.mo01354TbData.values.selectedRowId = cpyLine.id
  updateSeq()
}

/**
 *行削除
 */
function deleteRow() {
  if (!doFlg.value || !local.mo01354TbData.values.selectedRowId) {
    return
  }

  local.orX0002DeleteLineDialog.mo00024.isOpen = true
}

/**
 * 「課題取込アイコンボタン」押下
 */
function openGUI00949() {
  if (!doFlg.value) {
    return
  }
  localOneway.or28455Oneway.issuesOrGoalImportType = {
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    syubetsuId: localOneway.orX0060Oneway.syubetuId ?? '',
    importKbn: OrX0060Const.IMPORT_KBN_1,
    kikanFlag: localOneway.orX0060Oneway.kikanFlg ?? '',
  }
  // or28455 GUI00949 課題・目標取込画面をポップアップで起動する。
  Or28455Logic.state.set({
    uniqueCpId: or28455_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00949 課題・目標取込画面の返回値がある場合
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 *
 * @param newValue - ポップアップ画面での返回値
 */
async function handleOr28455Return(newValue:Or28455Type) {
  if (isUndefined(newValue)) {
      return
  }

  const dataList = refValue.value?.dataList
  if (!dataList) return

  // 行クリア（返回値.操作モード=1:上書の場合のみには行う）
  if (newValue.operationKbn === OrX0060Const.OPERATION_KBN_1) {
    dataList.forEach(item => {
      if (item.updateKbn !== UPDATE_KBN.DELETE) {
        item.updateKbn = UPDATE_KBN.DELETE
      }
    })
  }

  const validDataList = dataList.filter(
    item => item.updateKbn !== UPDATE_KBN.DELETE
  ) ?? []

  if (newValue.items.length > 0) {
    // 課題番号:表示用「課題目標期間内容担当」リストでの最大の課題番号に基づき、1を加算する
    let maxKadaiNoStart = 1
    if (validDataList.length > 0) {
      const kadaiNoValues: number[] = []
      for (const item of validDataList) {
        const num = Number(item.kadaiNo)
        if (!isNaN(num) && isFinite(num)) {
          kadaiNoValues.push(num)
        }
      }
      if (kadaiNoValues.length === 0) {
        maxKadaiNoStart = 1
      } else {
        maxKadaiNoStart = Math.max(...kadaiNoValues) + 1
      }
    }

  // 返回値.課題目標リストによって、表示用「課題目標期間内容担当」リストにデータを追加する
    const validCount  = validDataList.length
    const dataArr = newValue.items.map(
      (item, index): GamenDataInfo => ({
        id: getNewNegativeId().toString(),
        kadaiKnj: { value: item.issues ?? '' },
        mokuhyoKnj: { value: item.goal ?? '' },
        kikanKnj: { value: '' },
        naiyoKnj: { value: '' },
        tantoShokuKnj: { value: '' },
        kadaiNo: String(maxKadaiNoStart + index),
        id2: '-1',
        id3: '-1',
        modifiedCnt2: '0',
        modifiedCnt3: '0',
        updateKbn: UPDATE_KBN.CREATE,
        seq: validCount + index + 1,
      })
    )
    dataList.push(...dataArr)
  }

  await nextTick()

  // 最後行にフォーカスを設定する
  const items = local.mo01354TbData.values.items
  if (items.length > 0) {
    const lastItem = items[items.length - 1]
    local.mo01354TbData.values.selectedRowId = lastItem.id
  } else {
    local.mo01354TbData.values.selectedRowId = ''
  }

}

/**
 * 「表示順アイコンボタン」押下
 */
function openGUI00951() {
  const { selectedRowId } = local.mo01354TbData.values
  if (!doFlg.value || !selectedRowId || !refValue.value) {
    return
  }

  const items = filteredDataList.value

  localOneway.or28206Oneway = {
    indexList: items.map((x) => {
      return {
        sort: x.seq,
        concreteIssues: x.kadaiKnj.value,
        lifeGoal: x.mokuhyoKnj.value,
        goalPeriod: x.kikanKnj.value,
        treatmentContents: x.naiyoKnj.value,
        manager: x.tantoShokuKnj.value,
        sortBackup: x.id,
      }
    }),
  }

  // GUI00951 表示順変更実施計画～①(処遇内容)をポップアップで起動する
  Or28206Logic.state.set({
    uniqueCpId: or28206_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 列が押下されました
 *
 * @param titleKey - タイトル
 *
 * @param columnKey - 列のキー
 *
 * @param tableIndex - index
 */
function openGUI00937(titleKey?: string, columnKey?: string, tableIndex?:string) {
  if (!titleKey || !columnKey) return

  //GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  let _b2CD = OrX0060Const.B2CD_2
  let _tableName = OrX0060Const.TABLE_NAME_212
  let _textValue = ''

  // 処遇実施上の留意点アイコンボタン
  if (columnKey === OrX0060Const.STR_RYUI_KNJ) {
    _b2CD = OrX0060Const.B2CD_8
    _tableName = OrX0060Const.TABLE_NAME_211
    _textValue = refValue.value?.rirekiOtherData?.ryuiKnj ?? ''
  } else {
    const targetTableIndex = tableIndex ?? local.mo01354TbData.values.selectedRowId
    const data = filteredDataList.value.find(i => i.id === targetTableIndex)
    if (!data) return
    // 選択行を設定
    if (tableIndex && local.mo01354TbData.values.selectedRowId !== tableIndex) {
      local.mo01354TbData.values.selectedRowId = tableIndex
    }
    switch (columnKey) {
      // 具体的な課題
      case OrX0060Const.STR_KADAI_KNJ:
        _b2CD = OrX0060Const.B2CD_1
        _textValue = data.kadaiKnj?.value ?? ''
        break

      // 生活目標
      case OrX0060Const.STR_MOKUHYO_KNJ:
        _b2CD = OrX0060Const.B2CD_2
        _textValue = data.mokuhyoKnj?.value ?? ''
        break

      // 目標期間(達成時期)
      case OrX0060Const.STR_KIKAN_KNJ:
        _b2CD = OrX0060Const.B2CD_9
        _textValue = data.kikanKnj?.value ?? ''
        break

      // 処遇の内容
      case OrX0060Const.STR_NAIYO_KNJ:
        _b2CD = OrX0060Const.B2CD_3
        _tableName = OrX0060Const.TABLE_NAME_213
        _textValue = data.naiyoKnj?.value ?? ''
        break
      default:
        break
    }

  }

  //GUI00937 入力支援［ケアマネ］画面をポップアップで起動する
  localOneway.or51775Oneway = {
    title: titleKey,
    screenId: '',
    bunruiId: '',
    t1Cd: OrX0060Const.B1CD_2220,
    t2Cd: _b2CD,
    t3Cd: OrX0060Const.B3CD_0,
    tableName: _tableName,
    columnName: columnKey,
    assessmentMethod: '',
    inputContents: _textValue,
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    mode: '',
  }
  local.or51775.modelValue = _textValue
  gyoumuCom.setGUI00937Param(localOneway.or51775Oneway, localOneway.orX0060Oneway.initMasterObj)
  //GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  Or51775Logic.state.set({
    uniqueCpId: or51775_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00937 入力支援［ケアマネ］画面の返回値がある場合
 *
 * @param or51775ConfirmValue - ポップアップ画面での返回値
 */
function handleOr51775Confirm(or51775ConfirmValue: Or51775ConfirmType) {
  if (or51775ConfirmValue) {
    const newValue = or51775ConfirmValue.value ?? ''
    // 返却情報.上書きフラグ(0:本文末に追加、1:本文上書)
    const title = localOneway.or51775Oneway.title
    // 処遇実施上の留意点
    if (title === t('label.or31926-considerations')) {
      refValue.value!.rirekiOtherData.ryuiKnj =
        or51775ConfirmValue.type === OrX0060Const.OPERATION_KBN_1
          ? newValue
          : refValue.value!.rirekiOtherData.ryuiKnj.concat(newValue)
      return
    }
    // 「課題・目標・内容・担当者」一覧
    const selectedId = local.mo01354TbData.values.selectedRowId
    if (!selectedId) return

    const data = filteredDataList.value.find(i => i.id === selectedId)
    if (!data) return

    const isOverwrite = or51775ConfirmValue.type === OrX0060Const.OPERATION_KBN_1
    const concatValue = (current: string, newVal: string) => isOverwrite ? newVal : current + newVal

    switch (title) {
      // 具体的な課題
      case t('label.or31926-specific-tasks'):
        data.kadaiKnj.value = concatValue(data.kadaiKnj.value, newValue)
        break
      // 生活目標
      case t('label.or31926-life-goals'):
        data.mokuhyoKnj.value = concatValue(data.mokuhyoKnj.value, newValue)
        break
      // 目標期間（達成時期）
      case t('label.or31926-goal-timeframe'):
        data.kikanKnj.value = concatValue(data.kikanKnj.value, newValue)
        break
      // 処遇の内容
      case t('label.or31926-care-details'):
        data.naiyoKnj.value = concatValue(data.naiyoKnj.value, newValue)
        break
      default:
        return
    }
    // 更新区分を更新
    handleChange(data)
  }
}

/**
 * GUI01021 担当者入力支援
 *
 * @param tableIndex - index
 */
function openGUI01021(tableIndex: string) {
  const selectedId = local.mo01354TbData.values.selectedRowId

  if (selectedId !== tableIndex) {
    local.mo01354TbData.values.selectedRowId = tableIndex
  }

  const data = filteredDataList.value.find(i => i.id === tableIndex)
  // 行が選択されていない場合
  if (!data) return

  local.or27235.mode = 7
  local.or27235.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  local.or27235.manager = data.tantoShokuKnj.value ?? ''
  Or27235Logic.state.set({
    uniqueCpId: or27235_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01021 担当者入力支援画面ポップアップ画面で返回
 *
 * @param newValue - 結果
 */
function handleOr27235Confirm(newValue: string) {
  const selectedId = local.mo01354TbData.values.selectedRowId
  if (!selectedId) return

  const data = filteredDataList.value.find(i => i.id === selectedId)
  // 行が選択されていない場合
  if (!data) return
  data.tantoShokuKnj.value = newValue
  // 更新区分を更新
  handleChange(data)
}

/**
 * 更新区分を設定
 *
 * @param item - 更新行
 */
function handleChange(item: GamenDataInfo) {
  if (item.updateKbn === UPDATE_KBN.NONE) {
    item.updateKbn = UPDATE_KBN.UPDATE
  }
}
/**
 * 実施計画～①複写返却情報
 *
 * @param copyDataList - 複写用表示用「課題目標期間内容担当」リスト
 *
 * @param rirekiOtherData - 複写用表示用留意点
 */
function updateCopyValue(copyDataList: GamenDataInfo[], rirekiOtherData: GamenRirekiOtherInfo) {
  if (!refValue.value) { return}
  // 既存データ表示用「課題目標期間内容担当」リストを削除する
  refValue.value.dataList?.forEach((x) => {
    if (x.updateKbn !== UPDATE_KBN.DELETE) {
      x.updateKbn = UPDATE_KBN.DELETE
    }
  })
  local.mo01354TbData.values.selectedRowId = ''
  local.mo01354TbData.values.selectedRowIds = []

  // orX0060 複写用表示用「課題目標期間内容担当」リスト
  if (copyDataList?.length > 0) {
    const copyDataArr = copyDataList.map((item) => ({
      ...item,
      id: getNewNegativeId().toString(),
    }))
    refValue.value.dataList.push(...copyDataArr)
  }
  // 留意点
  refValue.value.rirekiOtherData = rirekiOtherData
}

/**
 * 削除処理:リストを削除
 *
 */
function deleteAllRow() {
  if (refValue.value) {
    refValue.value.dataList?.forEach((x) => {
      if (x.updateKbn !== UPDATE_KBN.DELETE) {
        x.updateKbn = UPDATE_KBN.DELETE
      }
    })
    local.mo01354TbData.values.selectedRowId = ''
    local.mo01354TbData.values.selectedRowIds = []
  }
}

/**
 * バリデーション関数
 */
async function isValid() {
  return (await form.value!.validate()).valid
}

defineExpose({
  updateCopyValue,
  deleteAllRow,
  isValid,
})
</script>
<template>
  <c-v-form
    ref="form"
    class="h-100"
  >
    <div class="d-flex flex-column h-100">
      <div class="flex-grow-1 overflow-y-auto pb-2" :class="{ 'pl-6': !isCopyMode, 'pl-2': isCopyMode }">
        <!-- 一覧操作ボタン -->
        <div class="width-container">
          <c-v-row
            no-gutters
            class="btn-area-class"
          >
            <c-v-col cols="auto">
              <c-v-row
                no-gutters
                class="btn-group-class"
              >
                <!-- 行追加ボタン -->
                <c-v-col cols="auto">
                  <base-mo00611
                    v-if="!isCopyMode"
                    :oneway-model-value="localOneway.mo00611OnewayAdd"
                    @click="addRow"
                  />
                </c-v-col>
                <!-- 行挿入ボタン -->
                <c-v-col cols="auto">
                  <base-mo00611
                    v-if="!isCopyMode"
                    :oneway-model-value="localOneway.mo00611OnewayInsert"
                    :disabled="disableButtons"
                    @click="insertRow"
                  />
                </c-v-col>
                <!-- 行複写ボタン -->
                <c-v-col cols="auto">
                  <base-mo00611
                    v-if="!isCopyMode"
                    :oneway-model-value="localOneway.mo00611OnewayCopy"
                    :disabled="disableButtons"
                    @click="copyRow"
                  />
                </c-v-col>
                <!-- 行削除ボタン -->
                <c-v-col cols="auto">
                  <base-mo00611
                    v-if="!isCopyMode"
                    :oneway-model-value="localOneway.mo00611OnewayDelete"
                    :disabled="disableButtons"
                    @click="deleteRow"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <!-- 表示順タイトルラベル -->
            <c-v-col cols="auto">
              <c-v-row
                no-gutters
                class="btn-group-class"
              >
                <!--課題取込ラベル-->
                <base-mo00611
                  v-if="!isCopyMode"
                  v-show="importFlg"
                  :oneway-model-value="localOneway.mo00611OnewayImport"
                  @click="openGUI00949"
                />
                <!--表示順ラベル-->
                <base-mo00611
                  v-if="!isCopyMode"
                  :oneway-model-value="localOneway.mo00611OnewayOrder"
                  @click="openGUI00951"
                />
                <!-- データ-ページング -->
                <base-mo01338
                  :oneway-model-value="{
                    ...localOneway.mo01338Oneway,
                    value: String(local.mo01354TbData.values.items.length),
                  }"/>
              </c-v-row>
            </c-v-col>
          </c-v-row>
        </div>
        <!-- 一覧 -->
        <c-v-row
          no-gutters
          class="pt-4"
        >
          <div class="table-header table-wrapper">
            <base-mo01354
              v-model="local.mo01354TbData"
              class="list-wrapper"
              :oneway-model-value="localOneway.mo01354Oneway"
              :hide-default-footer="true"
            >
              <!--具体的な課題-->
              <template #[`item.kadaiKnj`]="{ item}">
                <g-custom-or-x-0163
                  v-model="item.kadaiKnj"
                  :oneway-model-value="localOneway.orX0163Oneway"
                  style="letter-spacing:-0.56px"
                  @on-click-edit-btn="openGUI00937(t('label.or31926-specific-tasks'), OrX0060Const.STR_KADAI_KNJ,item.tableIndex)"
                  @change="handleChange(item)"
                />
              </template>
              <!--生活目標-->
              <template #[`item.mokuhyoKnj`]="{ item}">
                <g-custom-or-x-0163
                  v-model="item.mokuhyoKnj"
                  :oneway-model-value="localOneway.orX0163Oneway"
                  style="letter-spacing:-0.56px"
                  @on-click-edit-btn="openGUI00937(t('label.or31926-life-goals'), OrX0060Const.STR_MOKUHYO_KNJ,item.id)"
                  @change="handleChange(item)"
                />
              </template>
              <!--目標期間（達成時期）-->
              <template #[`item.kikanKnj`]="{ item}">
                <g-custom-or-x-0218
                  v-model="item.kikanKnj"
                  :oneway-model-value="localOneway.orX0218Oneway"
                  style="letter-spacing:-0.56px"
                  @on-click-edit-btn="openGUI00937(t('label.or31926-goal-timeframe'), OrX0060Const.STR_KIKAN_KNJ,item.id)"
                  @change="handleChange(item)"
                />
              </template>
              <!--処遇の内容-->
              <template #[`item.naiyoKnj`]="{ item}">
                <g-custom-or-x-0163
                  v-model="item.naiyoKnj"
                  :oneway-model-value="localOneway.orX0163Oneway"
                  style="letter-spacing:-0.56px"
                  @on-click-edit-btn="openGUI00937(t('label.or31926-care-details'), OrX0060Const.STR_NAIYO_KNJ,item.id)"
                  @change="handleChange(item)"
                />
              </template>
              <!--担当者-->
              <template #[`item.tantoShokuKnj`]="{ item}">
                <g-custom-or-x-0163
                  v-model="item.tantoShokuKnj"
                  :oneway-model-value="localOneway.orX0163Oneway"
                  style="letter-spacing:-0.56px"
                  @on-click-edit-btn="openGUI01021(item.id)"
                  @change="handleChange(item)"
                />
              </template>
            </base-mo01354>
          </div>
        </c-v-row>
        <div class="py-6">
          <c-v-divider class="divider-class" />
        </div>
        <!-- 処遇実施上の留意点 -->
        <c-v-row
          no-gutters
          class="width-container ryuiKnj-input"
        >
          <g-custom-or-x-0156
            v-show="doFlg"
            v-model="ryuiKnjModel"
            class="w-100"
            :oneway-model-value="localOneway.orX0156Oneway"
            @on-click-edit-btn="
              openGUI00937(t('label.or31926-considerations'), OrX0060Const.STR_RYUI_KNJ)
            "
          />
        </c-v-row>
      </div>
    </div>
  </c-v-form>
  <!-- 「行削除ボタン」押下のメッセージ -->
  <g-custom-or-x-0002
    v-model="local.orX0002DeleteLineDialog"
    :oneway-model-value="localOneway.orX0002DeleteLineOneway"
  ></g-custom-or-x-0002>

  <!--GUI00937 入力支援［ケアマネ］画面-->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775_1"
    v-model="local.or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleOr51775Confirm"
  />

  <!--GUI00949 課題・目標取込画面画面-->
  <g-custom-or-28455
    v-if="showDialogOr28455"
    v-bind="or28455_1"
    v-model="local.or28455"
    :oneway-model-value="localOneway.or28455Oneway"
    :unique-cp-id="or28455_1.uniqueCpId"
    @update:model-value="handleOr28455Return"
  >
  </g-custom-or-28455>

  <!--GUI00951 表示順変更実施計画～①(処遇内容)-->
  <g-custom-or-28206
    v-if="showDialogOr28206"
    v-bind="or28206_1"
    v-model="local.or28206"
    :oneway-model-value="localOneway.or28206Oneway"
  />

  <!--GUI01021 担当者入力支援画面-->
  <g-custom-or-27235
    v-if="showDialogOr27235"
    v-bind="or27235_1"
    v-model="local.or27235"
    :oneway-model-value="localOneway.or27235Oneway"
    @on-confirm="handleOr27235Confirm"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
.table-header :deep(.v-table__wrapper tr th) {
  padding-left: 12px;
  padding-right: 0px;
  font-weight: normal !important;
  height:40px !important;
  font-size: 13px !important;
}
.table-header :deep(.v-table__wrapper tr td) {
  font-weight: normal !important;
  height:115px !important;
}
// 選択した行のCSS
.btn-area-class {
  justify-content: space-between;
  align-items: baseline;

  .btn-group-class {
    display: flex;
    gap: 8px;
  }

  :deep(.v-btn) {
    --v-btn-height: 32px !important;
    min-height: 32px !important;
    height: 32px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  :deep(.v-btn--size-default) {
    min-height: 32px !important;
    height: 32px !important;
  }
}

.divider-class {
  border-width: thin;
}

.width-container {
  width: 1168px;
}

.ryuiKnj-input {
  :deep(.v-sheet) {
    .service-label {
      .item-label {
        font-size: 16px;
        font-weight: bold !important;
      }
    }
  }
}
:deep(.v-sheet) {
  background-color: transparent !important;
}

:deep(.list-wrapper .v-table__wrapper th) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}
</style>
