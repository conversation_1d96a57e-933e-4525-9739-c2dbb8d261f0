/**
 * 計画転送・実績取込（提供事業所）
 *
 * @description
 * 計画転送・実績取込（提供事業所）
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
/**
 * 週間計画
 *
 * @description
 * 週間計画初期情報データを返却する。
 */
export function handler() {
  let result = defaultData

  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...result,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
