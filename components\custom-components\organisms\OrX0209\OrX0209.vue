<script setup lang="ts">
/**
 * OrX0209:有機体:解決すべき課題と目標
 * GUI00794_［アセスメント］画面（居宅）（1）
 * GUI00795_［アセスメント］画面（居宅）（2）
 * GUI00796_［アセスメント］画面（居宅）（3）
 * GUI00797_［アセスメント］画面（居宅）（4）
 * GUI00798_［アセスメント］画面（居宅）（5）
 * GUI00799_［アセスメント］画面（居宅）（6①）
 * GUI00800_［アセスメント］画面（居宅）（6②）
 * GUI00801_［アセスメント］画面（居宅）（6③-④）
 * GUI00802_［アセスメント］画面（居宅）（6⑤）
 * GUI00803_［アセスメント］画面（居宅）（6⑥）
 * GUI00804_［アセスメント］画面（居宅）（6医）
 * GUI00805_［アセスメント］画面（居宅）（7まとめ）
 * GUI00806_［アセスメント］画面（居宅）（7スケジュール）
 *
 * @description
 * 解決すべき課題と目標を表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { v4 as uuidv4 } from 'uuid'
import { cloneDeep } from 'lodash'
import { TeX0002Const } from '../../template/TeX0002/TeX0002.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { OrX0209Const } from './OrX0209.constants'
import { useNuxtApp } from '#app'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type {
  IssuesAndGoalListItem,
  OrX0209OnewayType,
  OrX0209Type,
} from '~/types/cmn/business/components/OrX0209Type'
import { useCmnRouteCom, useScreenStore, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21735Logic } from '~/components/base-components/organisms/Or21735/Or21735.logic'
import { Or21736Const } from '~/components/base-components/organisms/Or21736/Or21736.constants'
import { Or21736Logic } from '~/components/base-components/organisms/Or21736/Or21736.logic'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { Or21737Logic } from '~/components/base-components/organisms/Or21737/Or21737.logic'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import { Or21738Logic } from '~/components/base-components/organisms/Or21738/Or21738.logic'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo01280Type } from '~/types/business/components/Mo01280Type'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { OrX0163OnewayType } from '~/types/cmn/business/components/OrX0163Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
const $log = useNuxtApp().$log as DebugLogPluginInterface

/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  modelValue?: OrX0209Type
  onewayModelValue?: OrX0209OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// route共有情報
const cmnRouteCom = useCmnRouteCom()

const or21735 = ref({ uniqueCpId: '' })
const or21736 = ref({ uniqueCpId: '' })
const or21737 = ref({ uniqueCpId: '' })
const or21738 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' }) // 入力支援ダイアログ

/** デフォルトOneway */
const defaultOneway = reactive({
  OrX0209Oneway: {
    /** アセスメント名 */
    assessmentName: '',
    /** アセスメントID */
    gdlId: '',
    /** 計画期間ID */
    sc1Id: '',
    /** アセスメント番号 */
    assNo: '',
    /** 表示モード */
    mode: OrX0209Const.DEFAULT.MODE_NORMAL,
  } as OrX0209OnewayType,
})

/** ローカルモーダル */
const local = reactive({
  // 解決すべき課題と目標一覧
  mo01354: {
    values: {
      selectedRowId: '',
      selectedRowIds: [] as string[],
      items: [] as IssuesAndGoalListItem[],
    },
  } as Mo01354Type,
  // 入力支援ダイアログ
  or51775: {
    modelValue: '',
  } as Or51775Type,
})

// Oneway
const localOneway = reactive({
  OrX0209Oneway: {
    ...defaultOneway.OrX0209Oneway,
    ...props.onewayModelValue,
  } as OrX0209OnewayType,
  mo01338Oneway: {
    value: t('label.should-solution-issues-and-goal'),
    valueFontWeight: 'bold',
    customClass: new CustomClass({
      outerClass: 'background-transparent',
      itemClass: 'ma-0',
      itemStyle: 'font-size: 18px;',
    }),
  } as Mo01338OnewayType,
  // 一覧
  mo01354Oneway: {
    rowHeight: OrX0209Const.DEFAULT.ROW_HEIGHT_NORMAL,
    headers: [] as Mo01354Headers[],
    useDefaultHeader: false,
    height: 'auto',
  } as Mo01354OnewayType,
  // 入力支援アイコン付けテキストエリア
  orX0163Oneway: {
    showEditBtnFlg: true,
  } as OrX0163OnewayType,
  // 入力支援ダイアログ
  or51775Oneway: {
    screenId: 'GUI00794',
    mode: Or51775Const.CARE_MANAGER_PROCESS_MODE,
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg,
  } as Or51775OnewayType,
  // 総件数
  rowCountOneway: {
    /** 値のフォントの太さ */
    valueFontWeight: 'normal',
    /** 値 */
    value: 'background-transparent',
    /** カスタムクラス */
    customClass: new CustomClass({
      outerClass: 'background-transparent',
      labelClass: 'd-none',
      itemClass: '',
    }),
  } as Mo01338OnewayType,
})

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

// 総件数
const pageCount = computed(() => {
  return local.mo01354.values.items.length
})

// 入力支援ダイアログ表示フラグ(入力支援)
const showDialogOr51775 = computed(() => {
  // Or27761のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

// フォーカス中クラムID
const focusColumnId = ref('')

// フォーカス中クラム内容
const focusColumnContent = ref('')

// 最初の一覧データのバックアップ
const originalListData = ref<IssuesAndGoalListItem[]>([])

// 削除リスト
const deleteData = ref<IssuesAndGoalListItem[]>([])

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<OrX0209Type>({
  cpId: OrX0209Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21735Const.CP_ID(0)]: or21735.value,
  [Or21736Const.CP_ID(0)]: or21736.value,
  [Or21737Const.CP_ID(0)]: or21737.value,
  [Or21738Const.CP_ID(0)]: or21738.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  $log.debug(`★[onMounted] [cpId]${OrX0209Const.CP_ID(0)} [uId]${props.uniqueCpId}`)

  initControls()
})

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    local.mo01354.values.items.splice(0)
    newValue?.items.forEach((item) => {
      local.mo01354.values.items.push({
        /** id */
        id: item.id,
        /** データID */
        dataId: item.dataId,
        /** アセスメントID */
        gdlId: item.gdlId,
        /** 計画期間ID */
        sc1Id: item.sc1Id,
        /** アセスメント番号 */
        assNo: item.assNo,
        /** アセスメント名 */
        assessmentName: item.assessmentName,
        /** 生活全般の解決すべき課題 */
        wholeLifeShouldSolutionIssues: item.wholeLifeShouldSolutionIssues,
        /** 長期目標 */
        longtermGoal: item.longtermGoal,
        /** 短期目標 */
        shorttermGoal: item.shorttermGoal,
        /** シーケンス */
        seq: item.seq,
        /** 更新区分 */
        updateKbn: item.updateKbn,
      })
    })

    // データバックアップ
    originalListData.value =
      cloneDeep(local.mo01354.values.items as IssuesAndGoalListItem[]) ??
      ([] as IssuesAndGoalListItem[])

    // RefValueをリセット
    setTimeout(() => {
      setRefValue()
    }, 0)
  },
  { deep: true, immediate: true }
)

watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.OrX0209Oneway = {
      ...defaultOneway.OrX0209Oneway,
      ...newValue,
    }

    if (localOneway.OrX0209Oneway.mode === OrX0209Const.DEFAULT.MODE_DELETE_TAB) {
      setDisabled(true)
    } else if (localOneway.OrX0209Oneway.mode === OrX0209Const.DEFAULT.MODE_DELETE_ALL) {
      setDisabled(true)
      local.mo01354.values.items.splice(0)
    } else {
      setDisabled(false)
    }

    if (localOneway.OrX0209Oneway.mode === OrX0209Const.DEFAULT.MODE_COPY) {
      localOneway.orX0163Oneway.showEditBtnFlg = false
      localOneway.orX0163Oneway.textareaReadonly = true
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => local.mo01354.values.items,
  (newValue) => {
    if (refValue.value) {
      refValue.value.items =
        cloneDeep(newValue as IssuesAndGoalListItem[]) ?? ([] as IssuesAndGoalListItem[])

      if (refValue.value.items.length > 0 && selectedItemIndex.value < 0) {
        selectedItemIndex.value = 0
      }
    }
  },
  { deep: true, immediate: true }
)

// 「行追加ボタン」押下
watch(
  () => Or21735Logic.event.get(or21735.value.uniqueCpId),
  () => {
    addNewLine()
  }
)

// 「行挿入ボタン」押下
watch(
  () => Or21736Logic.event.get(or21736.value.uniqueCpId),
  () => {
    insertNewLine()
  }
)

// 「行複写ボタン」押下
watch(
  () => Or21737Logic.event.get(or21737.value.uniqueCpId),
  () => {
    copyLine()
  }
)

// 「行削除ボタン」押下
watch(
  () => Or21738Logic.event.get(or21738.value.uniqueCpId),
  () => {
    deleteLine()
  }
)

// 表示順を監視
watch(
  pageCount,
  (newValue) => {
    localOneway.rowCountOneway.value = newValue.toString() + t('label.item')
  },
  { immediate: true }
)
/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**************************************************
 * 関数
 **************************************************/
/**
 * 行を追加
 *
 */
function addNewLine() {
  // 行を追加
  local.mo01354.values.items.push({
    /** id */
    id: uuidv4(),
    /** データID */
    dataId: '',
    /** アセスメントID */
    gdlId: localOneway.OrX0209Oneway?.gdlId,
    /** 計画期間ID */
    sc1Id: localOneway.OrX0209Oneway?.sc1Id,
    /** アセスメント番号 */
    assNo: localOneway.OrX0209Oneway?.assNo,
    /** アセスメント名 */
    assessmentName: { value: localOneway.OrX0209Oneway?.assessmentName ?? '' } as Mo01337OnewayType,
    /** 生活全般の解決すべき課題 */
    wholeLifeShouldSolutionIssues: { value: '' } as Mo01280Type,
    /** 長期目標 */
    longtermGoal: { value: '' } as Mo01280Type,
    /** 短期目標 */
    shorttermGoal: { value: '' } as Mo01280Type,
    /** シーケンス */
    seq: local.mo01354.values.items.length + 1,
    /** 更新回数 */
    modifiedCnt: '0',
    /** 更新区分 */
    updateKbn: TeX0002Const.DEFAULT.UPDATE_KBN_C,
  })

  selectedItemIndex.value = local.mo01354.values.items.length - 1
  local.mo01354.values.scrollToId = local.mo01354.values.selectedRowId

  // 選択行にスクロール
  scrollToSelectRow()
}

/**
 * 行を挿入
 *
 */
function insertNewLine() {
  // 行を挿入
  local.mo01354.values.items.splice(selectedItemIndex.value, 0, {
    /** id */
    id: uuidv4(),
    /** データID */
    dataId: '',
    /** アセスメントID */
    gdlId: localOneway.OrX0209Oneway?.gdlId,
    /** 計画期間ID */
    sc1Id: localOneway.OrX0209Oneway?.sc1Id,
    /** アセスメント番号 */
    assNo: localOneway.OrX0209Oneway?.assNo,
    /** アセスメント名 */
    assessmentName: { value: localOneway.OrX0209Oneway?.assessmentName ?? '' } as Mo01337OnewayType,
    /** 生活全般の解決すべき課題 */
    wholeLifeShouldSolutionIssues: { value: '' } as Mo01280Type,
    /** 長期目標 */
    longtermGoal: { value: '' } as Mo01280Type,
    /** 短期目標 */
    shorttermGoal: { value: '' } as Mo01280Type,
    /** シーケンス */
    seq: 0,
    /** 更新回数 */
    modifiedCnt: '0',
    /** 更新区分 */
    updateKbn: TeX0002Const.DEFAULT.UPDATE_KBN_C,
  })

  // seqを更新
  local.mo01354.values.items.forEach((item, index) => {
    item.seq = index + 1
  })
}

/**
 * 行を複写
 *
 */
function copyLine() {
  const currentLineData = local.mo01354.values.items[
    selectedItemIndex.value
  ] as IssuesAndGoalListItem

  // 行を複写
  local.mo01354.values.items.splice(selectedItemIndex.value + 1, 0, {
    /** id */
    id: uuidv4(),
    /** データID */
    dataId: currentLineData.dataId,
    /** アセスメントID */
    gdlId: currentLineData.gdlId,
    /** 計画期間ID */
    sc1Id: currentLineData.sc1Id,
    /** アセスメント番号 */
    assNo: currentLineData.assNo,
    /** アセスメント名 */
    assessmentName: currentLineData.assessmentName,
    /** 生活全般の解決すべき課題 */
    wholeLifeShouldSolutionIssues: currentLineData.wholeLifeShouldSolutionIssues,
    /** 長期目標 */
    longtermGoal: currentLineData.longtermGoal,
    /** 短期目標 */
    shorttermGoal: currentLineData.shorttermGoal,
    /** シーケンス */
    seq: 0,
    /** 更新回数 */
    modifiedCnt: '0',
    /** 更新区分 */
    updateKbn: TeX0002Const.DEFAULT.UPDATE_KBN_C,
  })

  selectedItemIndex.value = selectedItemIndex.value + 1

  // seqを更新
  local.mo01354.values.items.forEach((item, index) => {
    item.seq = index + 1
  })

  // 選択行にスクロール
  scrollToSelectRow()
}

/**
 * 行を削除
 *
 */
function deleteLine() {
  if (selectedItemIndex.value < 0) {
    return
  }

  // 該当行情報
  const currentRowData = local.mo01354.values.items[
    selectedItemIndex.value
  ] as IssuesAndGoalListItem
  // 修正前の行データを取得
  const originalRowData = originalListData.value.filter(
    (item) =>
      item.dataId === currentRowData.dataId &&
      item.sc1Id === currentRowData.sc1Id &&
      item.gdlId === currentRowData.gdlId
  ) as IssuesAndGoalListItem[]

  if (originalRowData.length > 0) {
    // 既存行を削除の場合
    local.mo01354.values.items.forEach((item) => {
      if (
        (item as IssuesAndGoalListItem).dataId === originalRowData[0].dataId &&
        (item as IssuesAndGoalListItem).sc1Id === originalRowData[0].sc1Id &&
        (item as IssuesAndGoalListItem).gdlId === originalRowData[0].gdlId
      ) {
        // 削除リストに保持
        deleteData.value.push(item as IssuesAndGoalListItem)
      }
    })
  }

  // 行を削除
  local.mo01354.values.items.splice(selectedItemIndex.value, 1)

  // 次の行を選択
  toNextSelectRow()
}

/**
 * 次の行を選択
 *
 */
function toNextSelectRow() {
  let selectPreIndex = -1
  let selectNextIndex = -1
  for (let i = 0; i < local.mo01354.values.items.length; i++) {
    if (local.mo01354.values.items[i].deleteFlg === true) {
      continue
    }

    if (i >= selectedItemIndex.value) {
      selectNextIndex = i
      break
    } else {
      selectPreIndex = i
    }
  }

  if (selectNextIndex >= 0) {
    // 次の行を選択にする
    local.mo01354.values.selectedRowId = local.mo01354.values.items[selectNextIndex].id
    local.mo01354.values.scrollToId = local.mo01354.values.selectedRowId
  } else {
    // 次の行がない場合、前の行を選択にする
    if (selectPreIndex >= 0) {
      local.mo01354.values.selectedRowId = local.mo01354.values.items[selectPreIndex].id
      local.mo01354.values.scrollToId = local.mo01354.values.selectedRowId
    }
  }
}

/**
 * 初期化処理
 */
function initControls() {
  localOneway.mo01354Oneway.headers.splice(0)
  localOneway.mo01354Oneway.headers = [
    {
      title: t('label.assessment'),
      key: 'assessmentName',
      sortable: false,
      minWidth: '180',
    },
    {
      title: t('label.whole-life-should-solution-issues'),
      key: 'wholeLifeShouldSolutionIssues',
      sortable: false,
      minWidth: '380',
    },
    {
      title: t('label.long-term-goal'),
      key: 'longtermGoal',
      sortable: false,
      minWidth: '270',
    },
    {
      title: t('label.short-term-goal'),
      key: 'shorttermGoal',
      sortable: false,
      minWidth: '270',
    },
  ]
}

/**
 * RefValueをリセット
 *
 */
function setRefValue() {
  // RefValueを更新する
  useScreenStore().setCpTwoWay({
    cpId: OrX0209Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/**
 * 操作ボタン制御
 *
 * @param disabled - 非活性フラグ
 */
function setDisabled(disabled: boolean) {
  // 行追加
  Or21735Logic.state.set({
    uniqueCpId: or21735.value.uniqueCpId,
    state: {
      disabled: disabled,
    },
  })
  // 行挿入
  Or21736Logic.state.set({
    uniqueCpId: or21736.value.uniqueCpId,
    state: {
      disabled: disabled,
    },
  })
  // 行複写
  Or21737Logic.state.set({
    uniqueCpId: or21737.value.uniqueCpId,
    state: {
      disabled: disabled,
    },
  })
  // 行削除
  Or21738Logic.state.set({
    uniqueCpId: or21738.value.uniqueCpId,
    state: {
      disabled: disabled,
    },
  })
}

/**
 * 行選択
 *
 * @param item - 行情報
 */
function onSelectRow(item: IssuesAndGoalListItem) {
  local.mo01354.values.items.forEach((rowData, index) => {
    if ((rowData as IssuesAndGoalListItem).id === item.id) {
      selectedItemIndex.value = index
    }
  })
}

/**
 * 行選択されるかどうかを判断
 *
 * @param item - 行情報
 */
function isSelected(item: IssuesAndGoalListItem) {
  let currentIndex = -1
  local.mo01354.values.items.forEach((rowData, index) => {
    if ((rowData as IssuesAndGoalListItem).id === item.id) {
      currentIndex = index
    }
  })
  return selectedItemIndex.value === currentIndex
}

/**
 * 行選択されるかどうかを判断
 *
 * @param item - 行情報
 *
 * @param columnId - 列フラグ
 *
 * @param index - 選択した行のindex
 */
function isSameContent(item: IssuesAndGoalListItem, columnId: string, index: number) {
  if (index <= 0 || local.mo01354.values.items.length <= 1) {
    return
  }

  let isSame = false

  // 該当行情報を取得
  const currentRowData = local.mo01354.values.items[index] as IssuesAndGoalListItem
  // 前の行情報を取得
  const preRowData = local.mo01354.values.items[index - 1] as IssuesAndGoalListItem

  if (currentRowData && preRowData) {
    switch (columnId) {
      // 生活全般の解決すべき課題
      case TeX0002Const.DEFAULT.COLUMN_ID_KADAI:
        if (
          currentRowData.wholeLifeShouldSolutionIssues.value &&
          preRowData.wholeLifeShouldSolutionIssues.value &&
          currentRowData.wholeLifeShouldSolutionIssues.value ===
            preRowData.wholeLifeShouldSolutionIssues.value
        ) {
          isSame = true
        }
        break
      // 長期目標
      case TeX0002Const.DEFAULT.COLUMN_ID_CHOUKI:
        if (
          currentRowData.longtermGoal.value &&
          preRowData.longtermGoal.value &&
          currentRowData.longtermGoal.value === preRowData.longtermGoal.value
        ) {
          isSame = true
        }
        break
      // 短期目標
      case TeX0002Const.DEFAULT.COLUMN_ID_TANKI:
        if (
          currentRowData.shorttermGoal.value &&
          preRowData.shorttermGoal.value &&
          currentRowData.shorttermGoal.value === preRowData.shorttermGoal.value
        ) {
          isSame = true
        }
        break
    }
  }

  return isSame
}

/**
 * 入力支援ダイアログを開く
 *
 * @param item - 行情報
 *
 * @param columnId - 列フラグ
 */
function openInputSupportDialog(item: IssuesAndGoalListItem, columnId: string) {
  // 削除モードの場合、処理しない
  if (
    localOneway.OrX0209Oneway.mode === OrX0209Const.DEFAULT.MODE_DELETE_TAB ||
    localOneway.OrX0209Oneway.mode === OrX0209Const.DEFAULT.MODE_DELETE_ALL
  ) {
    return
  }

  switch (columnId) {
    // 生活全般の解決すべき課題
    case TeX0002Const.DEFAULT.COLUMN_ID_KADAI:
      // 内容
      localOneway.or51775Oneway.inputContents = item.wholeLifeShouldSolutionIssues.value
      // タイトル
      localOneway.or51775Oneway.title = t('label.whole-life-should-solution-issues')
      // 大分類CD
      localOneway.or51775Oneway.t1Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_DAI_BUNRUI_CD_820
      // 中分類CD
      localOneway.or51775Oneway.t2Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_CHU_BUNRUI_CD_1
      // 小分類CD
      localOneway.or51775Oneway.t3Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_SHOU_BUNRUI_CD_0
      // テーブル名
      localOneway.or51775Oneway.tableName =
        TeX0002Const.DEFAULT.INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL_KADAI
      // クラム名
      localOneway.or51775Oneway.columnName =
        TeX0002Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_KADAI_KNJ
      break
    // 長期目標
    case TeX0002Const.DEFAULT.COLUMN_ID_CHOUKI:
      // 内容
      localOneway.or51775Oneway.inputContents = item.longtermGoal.value
      // タイトル
      localOneway.or51775Oneway.title = t('label.long-term-goals')
      // 大分類CD
      localOneway.or51775Oneway.t1Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_DAI_BUNRUI_CD_820
      // 中分類CD
      localOneway.or51775Oneway.t2Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_CHU_BUNRUI_CD_2
      // 小分類CD
      localOneway.or51775Oneway.t3Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_SHOU_BUNRUI_CD_0
      // テーブル名
      localOneway.or51775Oneway.tableName =
        TeX0002Const.DEFAULT.INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL_KADAI
      // クラム名
      localOneway.or51775Oneway.columnName =
        TeX0002Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_CHOUKI_KNJ
      break
    // 短期目標
    case TeX0002Const.DEFAULT.COLUMN_ID_TANKI:
      // 内容
      localOneway.or51775Oneway.inputContents = item.shorttermGoal.value
      // タイトル
      localOneway.or51775Oneway.title = t('label.short-term-goal')
      // 大分類CD
      localOneway.or51775Oneway.t1Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_DAI_BUNRUI_CD_820
      // 中分類CD
      localOneway.or51775Oneway.t2Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_CHU_BUNRUI_CD_4
      // 小分類CD
      localOneway.or51775Oneway.t3Cd = TeX0002Const.DEFAULT.INPUT_SUPPORT_SHOU_BUNRUI_CD_0
      // テーブル名
      localOneway.or51775Oneway.tableName =
        TeX0002Const.DEFAULT.INPUT_SUPPORT_TABLE_NAME_CPN_TUC_GDL_KADAI
      // クラム名
      localOneway.or51775Oneway.columnName =
        TeX0002Const.DEFAULT.INPUT_SUPPORT_COLUMN_NAME_TANKI_KNJ
      break
  }

  // 該当行を選択にする
  local.mo01354.values.selectedRowId = item.id

  // フォーカス情報を保持
  focusColumnId.value = columnId

  // 利用者ID
  localOneway.or51775Oneway.userId = localOneway.OrX0209Oneway.userId ?? ''

  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 入力支援ダイアログ確認処理
 *
 * @param resData - 入力支援選択情報
 */
function inputSupportConfirm(resData: Or51775ConfirmType) {
  // 値を修正
  local.mo01354.values.items.forEach((item) => {
    if (local.mo01354.values.selectedRowId === item.id) {
      switch (focusColumnId.value) {
        // 生活全般の解決すべき課題
        case TeX0002Const.DEFAULT.COLUMN_ID_KADAI:
          ;(item as IssuesAndGoalListItem).wholeLifeShouldSolutionIssues.value = resData.value
          break
        // 長期目標
        case TeX0002Const.DEFAULT.COLUMN_ID_CHOUKI:
          ;(item as IssuesAndGoalListItem).longtermGoal.value = resData.value
          break
        // 短期目標
        case TeX0002Const.DEFAULT.COLUMN_ID_TANKI:
          ;(item as IssuesAndGoalListItem).shorttermGoal.value = resData.value
          break
      }
    }
  })
}

/**
 * セルフォーカス処理
 *
 * @param columnId - クラムID
 *
 * @param content - 内容
 */
function onFocus(columnId: string, content = '') {
  focusColumnId.value = columnId
  focusColumnContent.value = content
}

/**
 * 確認ダイアログ表示
 *
 * @param item - 行データ
 */
function rowDataChange(item: IssuesAndGoalListItem) {
  // すでに状態がある場合、処理しない
  if (item.updateKbn === TeX0002Const.DEFAULT.UPDATE_KBN_C ||
    item.updateKbn === TeX0002Const.DEFAULT.UPDATE_KBN_U
  ) {
    return
  }

  // 更新状態にする
  item.updateKbn = TeX0002Const.DEFAULT.UPDATE_KBN_U
}

/**
 * 行選択処理
 *
 * @param mo01354 - 選択情報
 */
function onSelect(mo01354: Mo01354Type) {
  local.mo01354.values.items.forEach((item, index) => {
    if (item.id === mo01354.values.selectedRowId) {
      selectedItemIndex.value = index
      return
    }
  })
}

/**
 * 選択行にスクロール
 *
 */
function scrollToSelectRow() {
  // スクロール
  if (selectedItemIndex.value >= 0) {
    void nextTick(() => {
      const component = document.documentElement
      const tbody = component.querySelector('.v-table__wrapper tbody')!
      if (tbody) {
        const row = tbody.children[selectedItemIndex.value]
        if (row) {
          const rowRect = row.getBoundingClientRect()
          const componentRect = component.getBoundingClientRect()
          // 上に移動の場合
          if (rowRect.top > componentRect.top + componentRect.height - rowRect.height) {
            const offsetHeight =
              rowRect.top + rowRect.height - componentRect.top - componentRect.height
            component.scrollTop = component.scrollTop + offsetHeight
          }
        }
      }
    })
  }
}
</script>

<template>
  <c-v-row
    :id="props.uniqueCpId + 'issuesAndGoalListComponentWrapper'"
    no-gutters
    class="issuesAndGoalListComponentWrapper"
  >
    <c-v-col>
      <!-- タイトル -->
      <c-v-row no-gutters>
        <base-mo-01338 :oneway-model-value="localOneway.mo01338Oneway" />
      </c-v-row>
      <!-- 操作ボタン -->
      <c-v-row
        no-gutters
        class="pt-6"
      >
        <c-v-col v-if="localOneway.OrX0209Oneway.mode !== OrX0209Const.DEFAULT.MODE_COPY" class="d-flex flex-row">
          <!-- 行追加ボタン -->
          <div>
            <g-base-or-21735 v-bind="or21735"></g-base-or-21735>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.add-row')"
            ></c-v-tooltip>
          </div>
          <!-- 行挿入ボタン -->
          <div class="ml-2">
            <g-base-or-21736 v-bind="or21736"></g-base-or-21736>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.insert-row')"
            ></c-v-tooltip>
          </div>
          <!-- 行複写ボタン: Or21737 -->
          <div class="ml-2">
            <g-base-or-21737 v-bind="or21737"></g-base-or-21737>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.duplicate-row')"
            ></c-v-tooltip>
          </div>
          <!-- 行削除ボタン: Or21738 -->
          <div class="ml-2">
            <g-base-or-21738 v-bind="or21738"></g-base-or-21738>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.delete-row')"
            ></c-v-tooltip>
          </div>
        </c-v-col>
        <c-v-col>
          <div class="d-flex justify-end align-center">
            <!-- 表示順 -->
            <g-custom-or-x-0030 v-if="localOneway.OrX0209Oneway.mode !== OrX0209Const.DEFAULT.MODE_COPY" style="margin-left: unset;margin-right: unset;" />
            <!-- 件数 -->
            <base-mo01338
              class="ml-4"
              :oneway-model-value="localOneway.rowCountOneway"
            ></base-mo01338>
          </div>
        </c-v-col>
      </c-v-row>
      <!-- 一覧テーブル -->
      <c-v-row
        no-gutters
        class="mt-4"
      >
        <c-v-col class="table-header">
          <base-mo-01354
            v-model="local.mo01354"
            :oneway-model-value="localOneway.mo01354Oneway"
            class="list-wrapper"
            @update:model-value="onSelect"
          >
            <!-- アセスメント -->
            <template #[`item`]="{ item, index }">
              <tr
                :class="{ 'select-row': isSelected(item) }"
                @click="onSelectRow(item)"
              >
                <!-- アセスメント -->
                <td class="pa-0 pl-3 pr-3">
                  <c-v-row
                    no-gutters
                    class="px-4"
                  >
                    <c-v-col class="custom-label"
                      ><base-mo-01337 :oneway-model-value="item.assessmentName"
                    /></c-v-col>
                  </c-v-row>
                </td>
                <!-- 生活全般の解決すべき課題 -->
                <td
                  class="pa-0"
                  :class="{
                    tdTopline:
                      isSameContent(item, TeX0002Const.DEFAULT.COLUMN_ID_KADAI, index) === false,
                    copyLineColor: isSameContent(item, TeX0002Const.DEFAULT.COLUMN_ID_KADAI, index),
                  }"
                >
                  <g-custom-or-x-0163
                    v-model="item.wholeLifeShouldSolutionIssues"
                    :oneway-model-value="localOneway.orX0163Oneway"
                    @on-click-edit-btn="
                      openInputSupportDialog(item, TeX0002Const.DEFAULT.COLUMN_ID_KADAI)
                    "
                    @focus="
                      onFocus(
                        TeX0002Const.DEFAULT.COLUMN_ID_KADAI,
                        item.wholeLifeShouldSolutionIssues.value
                      )
                    "
                    @change="rowDataChange(item)"
                  />
                </td>
                <!-- 長期目標 -->
                <td
                  class="pa-0"
                  :class="{
                    tdTopline:
                      isSameContent(item, TeX0002Const.DEFAULT.COLUMN_ID_CHOUKI, index) === false,
                    copyLineColor: isSameContent(
                      item,
                      TeX0002Const.DEFAULT.COLUMN_ID_CHOUKI,
                      index
                    ),
                  }"
                >
                  <g-custom-or-x-0163
                    v-model="item.longtermGoal"
                    :oneway-model-value="localOneway.orX0163Oneway"
                    @on-click-edit-btn="
                      openInputSupportDialog(item, TeX0002Const.DEFAULT.COLUMN_ID_CHOUKI)
                    "
                    @focus="onFocus(TeX0002Const.DEFAULT.COLUMN_ID_CHOUKI, item.longtermGoal.value)"
                    @change="rowDataChange(item)"
                  />
                </td>
                <!-- 短期目標 -->
                <td
                  class="pa-0"
                  :class="{
                    tdTopline:
                      isSameContent(item, TeX0002Const.DEFAULT.COLUMN_ID_TANKI, index) === false,
                    copyLineColor: isSameContent(item, TeX0002Const.DEFAULT.COLUMN_ID_TANKI, index),
                  }"
                >
                  <g-custom-or-x-0163
                    v-model="item.shorttermGoal"
                    :oneway-model-value="localOneway.orX0163Oneway"
                    @on-click-edit-btn="
                      openInputSupportDialog(item, TeX0002Const.DEFAULT.COLUMN_ID_TANKI)
                    "
                    @focus="onFocus(TeX0002Const.DEFAULT.COLUMN_ID_TANKI, item.shorttermGoal.value)"
                    @change="rowDataChange(item)"
                  />
                </td>
              </tr>
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01354>
        </c-v-col>
      </c-v-row>
    </c-v-col>
  </c-v-row>

  <!-- 入力支援ダイアログ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-model="local.or51775"
    v-bind="or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="inputSupportConfirm"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

:deep(.table-header .v-table__wrapper th) {
  font-weight: normal !important;
  background-color: rgb(var(--v-theme-blue-100)) !important;
}

:deep(.table-header .v-table__wrapper tr td) {
  padding: 1px !important;
  border-bottom: unset !important;
}

:deep(.list-wrapper .v-table__wrapper tr:not(:first-child) td:first-child) {
  border-top: 1px solid rgb(var(--v-theme-light)) !important;
}

:deep(
  .table-header
    .v-table__wrapper
    tr:not(.row-selected):not(.select-row):not(.selected-row)
    td:not(:has(input, textarea, select))
) {
  background-color: rgb(var(--v-theme-surface));
}

.componentWrapper {
  background-color: rgb(var(--v-theme-surface));

  :deep(.full-width-field) {
    padding: 0 16px !important;
  }
}

.custom-label {
  white-space: pre-line;
}

.tdTopline {
  border-top: 1px solid rgb(var(--v-theme-light)) !important;
}

:deep(.copyLineColor textarea) {
  color: white !important;
}

.background-white {
  background-color: rgb(var(--v-theme-surface));
}

.background-transparent {
  background-color: transparent;
}

.select-row {
  background: rgb(var(--v-theme-blue-100));
}
</style>
