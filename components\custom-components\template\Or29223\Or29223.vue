<script setup lang="ts">
/**
 * Or29223:（課題整理総括）状況の事実
 * GUI00915_状況の事実
 *
 * @description
 * （課題整理総括）状況の事実
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */

import { onMounted, ref, watch, nextTick, computed, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'

import { Or29449Const } from '../../organisms/Or29449/Or29449.constants'
import { Or29224Const } from '../../organisms/Or29224/Or29224.constants'
import { Or05658Const } from '../../organisms/Or05658/Or05658.constants'
import { Or05656Const } from '../../organisms/Or05656/Or05656.constants'
import { Or29223Const } from './Or29223.constants'
import type {
  JyokyoSyosaiType,
  KikanObjType,
  MitosiSyosaiType,
  Or29223Type,
  RirekiObjType,
} from './Or29223.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useSetupChildProps, useScreenStore, useScreenTwoWayBind, useScreenUtils } from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { CustomClass } from '~/types/CustomClassType'
import type {
  TransmitParam,
  Or29223OnewayType,
  HistoryInfo,
  PlanPeriodInfo,
} from '~/types/cmn/business/components/Or29223Type'

import type { Or10320OnewayType } from '~/types/cmn/business/components/Or10320Type'
import { Or10320Logic } from '~/components/custom-components/organisms/Or10320/Or10320.logic'
import { Or10320Const } from '~/components/custom-components/organisms/Or10320/Or10320.constants'
import type { OrX0001Type, OrX0001OnewayType } from '~/types/cmn/business/components/OrX0001Type'
import { OrX0001Const } from '~/components/custom-components/organisms/OrX0001/OrX0001.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import type { OrX0007Type, OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import { OrX0007Logic } from '~/components/custom-components/organisms/OrX0007/OrX0007.logic'
import type { OrX0008Type, OrX0008OnewayType } from '~/types/cmn/business/components/OrX0008Type'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import { OrX0008Const } from '~/components/custom-components/organisms/OrX0008/OrX0008.constants'
import { OrX0008Logic } from '~/components/custom-components/organisms/OrX0008/OrX0008.logic'
import { OrX0010Const } from '~/components/custom-components/organisms/OrX0010/OrX0010.constants'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import type { PlanTargetPeriodDataType } from '~/types/cmn/PlanTargetPeriodDataType'
import type {
  HistorySelectInfoType,
  Or10929Type,
  Or10929OnewayType,
} from '~/types/cmn/business/components/Or10929Type'
import { Or10929Const } from '~/components/custom-components/organisms/Or10929/Or10929.constants'
import type { HistorySelectTableDataItem } from '~/components/custom-components/organisms/Or10929/Or10929.type'
import type {
  OrganizingIssuesInitSelectInEntity,
  OrganizingIssuesInitSelectOutEntity,
} from '~/repositories/cmn/entities/OrganizingIssuesInitSelectEntity'
import type {
  OrganizingIssuesPlanPeriodSelectInEntity,
  OrganizingIssuesPlanPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/OrganizingIssuesPlanPeriodSelectEntity'
import type {
  OrganizingIssuesHistorySelectInEntity,
  OrganizingIssuesHistorySelectOutEntity,
} from '~/repositories/cmn/entities/OrganizingIssuesHistorySelectEntity'
import type {
  OrganizingIssuesNewInitSelectInEntity,
  OrganizingIssuesNewInitSelectOutEntity,
} from '~/repositories/cmn/entities/OrganizingIssuesNewInitSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { OrX0115Const } from '~/components/custom-components/organisms/OrX0115/OrX0115.constants'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { useReportUtils } from '~/utils/useReportUtils'
import { useJigyoList } from '~/utils/useJigyoList'
import type { Mo00043Type, Mo00043OnewayType } from '~/types/business/components/Mo00043Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or29449Type } from '~/types/cmn/business/components/Or29449Type'
import type { Or29224Type } from '~/types/cmn/business/components/Or29224Type'
import type { Or05656OnewayType } from '~/types/cmn/business/components/Or05656Type'
import type { Or05061Type } from '~/types/cmn/business/components/Or05061Type'
import type { Or05059Type } from '~/types/cmn/business/components/Or05059Type'
import type {
  OrganizingIssuesUpdateInEntity,
  OrganizingIssuesUpdateOutEntity,
} from '~/repositories/cmn/entities/organizingIssuesUpdateEntity'
import type { Or05658Type } from '~/types/cmn/business/components/Or05658Type'
import type { OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
/**
 * useReportUtils
 */
const { reportOutput } = useReportUtils()
/**
 * useJigyoList
 */
const { jigyoListWatch } = useJigyoList()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or29223OnewayType
  uniqueCpId: string
  cpnFlg?: string
}

/**
 * useScreenUtils
 */
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * useScreenStore
 */
const { setChildCpBinds, getChildCpBinds } = useScreenUtils()
/**
 * useI18n
 */
const { t } = useI18n()

/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()

/**
 * 利用者一覧変更監視
 */
const { syscomUserSelectWatchFunc } = useUserListInfo()
/**
 * Or29224コンポーネントref
 */
const or29224Ref = ref<{
  ungroupItems(or05656: Or05656OnewayType[][]): Promise<unknown>
  or05658: string
  or05656: string
}>()
/**
 * Or29449コンポーネントref
 */
const or29449Ref = ref<{
  or05061: string
  or05059: string
}>()
const isInit = ref(true)
/**
 * or11871
 */
const or11871 = ref({ uniqueCpId: '' })
/**
 * or00248
 */
const or00248 = ref({ uniqueCpId: '' })
/**
 * orHeadLine
 */
const orHeadLine = ref({ uniqueCpId: '' })
/**
 * or00249
 */
const or00249 = ref({ uniqueCpId: '' })
/**
 * or10320
 */
const or10320 = ref({ uniqueCpId: '' })
/**
 * orX0001
 */
const orX0001 = ref({ uniqueCpId: '' })

/**
 * orX0007
 */
const orX0007 = ref({ uniqueCpId: '' })
/**
 * orX0008
 */
const orX0008 = ref({ uniqueCpId: '' })
/**
 * orX0009
 */
const orX0009 = ref({ uniqueCpId: '' })

/**
 * or10929
 */
const or10929 = ref({ uniqueCpId: '' })

/**
 * or21814
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * or21815
 */
const or21815 = ref({ uniqueCpId: '' })
/**
 * orx0115
 */
const orx0115 = ref({ uniqueCpId: '' })
/**
 * or41179
 */
const or41179 = ref({ uniqueCpId: '' })
/**
 * or29449用ref
 */
const or29449 = ref({ uniqueCpId: '' })
/**
 * or29224用ref
 */
const or29224 = ref({ uniqueCpId: '' })
/**
 * Or05658用ref
 */
const or05658 = ref({
  uniqueCpId: '',
})
/**
 * Or05656用ref
 */
const or05656 = ref({
  uniqueCpId: '',
})
/**
 * Orx0010用ref
 */
const orX0010 = ref({ uniqueCpId: '' })
/**
 * Or05061用ref
 */
const or05061 = ref({
  uniqueCpId: '',
})
/**
 * Or05059用ref
 */
const or05059 = ref({
  uniqueCpId: '',
})
const computedOr05658UniqueCpId = computed(() => or29224Ref.value?.or05658 ?? '')
const computedOr05656UniqueCpId = computed(() => or29224Ref.value?.or05656 ?? '')
const computedOr05061UniqueCpId = computed(() => or29449Ref.value?.or05061 ?? '')
const computedOr05059UniqueCpId = computed(() => or29449Ref.value?.or05059 ?? '')

watch(
  computedOr05658UniqueCpId,
  (val) => {
    or05658.value.uniqueCpId = val
  },
  { immediate: true }
)
watch(
  computedOr05656UniqueCpId,
  (val) => {
    or05656.value.uniqueCpId = val
  },
  { immediate: true }
)
watch(
  computedOr05061UniqueCpId,
  (val) => {
    or05061.value.uniqueCpId = val
  },
  { immediate: true }
)
watch(
  computedOr05059UniqueCpId,
  (val) => {
    or05059.value.uniqueCpId = val
  },
  { immediate: true }
)
// 画面状態管理用操作変数
const screenStore = useScreenStore()

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return (
    screenStore.getCpNavControl(orX0009.value.uniqueCpId) ??
    screenStore.getCpNavControl(or05658.value.uniqueCpId) ??
    screenStore.getCpNavControl(or05656.value.uniqueCpId) ??
    screenStore.getCpNavControl(or05061.value.uniqueCpId) ??
    screenStore.getCpNavControl(or05059.value.uniqueCpId)
  )
})
/**
 *  親情報
 */
const _commonInfo = {
  // 基準日
  baseDate: '',
  // ログイン情報.職員名
  loginUserName: '管理者　太郎',
  // 計画対象期間ID
  sc1Id: '',
  // アセスメントID
  assessmentId: '',
  // 作成者ID
  shokuId: '',
  // 調査アセスメント種別
  surveyAssessmentKind: '',
}

/**
 *  選択中利用者ID
 */
const _userId = ref('')
/**
 *  様式
 */
const styleFlag = ref<string>('1')
/**
 *  計画期間
 */
const planPeriodShow = ref<boolean>(true)
/**
 *  履歴
 */
const historyShow = ref<boolean>(true)
/**
 *  作成者
 */
const authorShow = ref<boolean>(true)
/**
 *  基準日
 */
const baseDateShow = ref<boolean>(true)

/**
 *  お気に入りに該当機能
 */
const favorite = ref<boolean>(false)

/**
 *  削除フラグ
 */
const deleteFlag = ref<boolean>(false)

/**
 *  データ再取得フラグ
 */
const retrieveCmCp1DataFlg = ref<boolean>(false)

/**
 *  ローカルTwoway
 */
const local = reactive({
  Or29223: {
    setInputComponentsFlg: false,
    /** 期間管理フラグ */
    kikanFlg: '',
    /** 計画期間情報 */
    kikanObj: {} as KikanObjType,
    /** 履歴情報 */
    rirekiObj: {} as RirekiObjType,
    /** 状況の事実リスト */
    jyokyoSyosaiList: [] as JyokyoSyosaiType[],
    /** 見通しリスト */
    mitosiSyosaiList: [] as MitosiSyosaiType[],
  },
  // タブ
  mo00043: {
    id: '',
  } as Mo00043Type,
  // 作成日
  createDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // フラグ
  flag: {
    // 期間管理
    periodManage: '1',
    // 計画期間が登録されていない
    periodManageRegistration: false,
  },
  // 二回目新規ボタン押下State
  addBtnState: false,
  // 事業所ID
  officeId: '1',
  // 計画対象期間情報ID
  planPeriodId: 1,
  // 計画対象期間情報
  planPeriod: {} as PlanPeriodInfo,
  // 計画対象期間情報
  planPeriodList: [] as PlanPeriodInfo[],
  // 履歴情報ID
  historyId: 1,
  // 履歴情報
  history: {} as HistoryInfo,
  // 履歴情報
  historyList: [] as HistoryInfo[],
  // 計画期間総件数
  totalCount: 0,
  // 認知能力
  degreeAbility: '1',
  // 法人ID
  houjinId: '',
  // 施設ID
  shisetuId: '',
  // 利用者ID
  userId: '',
  // 事業者ID
  svJigyoId: '',
  // アセスメントID
  raiId: '',
  // 基準日
  kijunbiYmd: '',
  // 作成者ID
  sakuseiId: '',
  historyModifiedCnt: '',
  or29449: {
    kikanFlg: '',
    rirekiObj: {} as RirekiObjType,
    kikanObj: {} as KikanObjType,
    mitosiSyosaiList: [] as MitosiSyosaiType[],
  } as Or29449Type,
  or29224: {} as Or29224Type,
})

/**
 * localComponents
 */
const localComponents = reactive({
  // 期間データ
  orX0007: { PlanTargetPeriodUpdateFlg: '' } as OrX0007Type,
  //履歴
  orX0008: {
    createId: '',
    createUpateFlg: '',
  } as OrX0008Type,
})

//
/**
 *  ローカルOneway
 */
const localOneway = reactive({
  orX0010Oneway: {} as OrX0010OnewayType,
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 1, totalCount: 0 } } as OrX0007OnewayType,
  /**
   * 対象期間
   */
  orX0115Oneway: {
    kindId: '',
    sc1Id: '',
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    rirekiId: '',
  } as OrX0115OnewayType,
  // タブ
  mo00043OnewayType: {
    tabItems: [],
    minWidth: '58px',
  } as Mo00043OnewayType,
  // 履歴
  orX0008Oneway: {
    createData: {} as PlanCreateDataType,
    screenID: 'GUI00915',
    // officeId: systemCommonsStore.getSvJigyoId ?? '',
    officeId: '21',
    sc1Id: local.history.sc1Id,
    useId: systemCommonsStore.getUserId,
    plan1Id: '',
    mode: '',
  } as unknown as OrX0008OnewayType,
  //作成者
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
    isDisabled: false,
  } as OrX0009OnewayType,
  // 基準日
  createDateOneway: {
    itemLabel: t('label.base-date'),
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({ outerClass: 'createDateOuterClass', labelClass: 'ma-1' }),
  } as Mo00020OnewayType,
  // or10320単方向バインド
  or10320Oneway: {
    facilityId: '',
    jigyoId: '',
    tekiyouOfficeIdList: [],
    tekiyouOfficeId: '',
    jigyoGrouptekiyouId: '',
  } as unknown as Or10320OnewayType,
  or10929Oneway: {
    historySelectInfo: {} as HistorySelectInfoType,
  } as Or10929OnewayType,
  mo00009OnewayAssessment: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-assessment-import-screen'),
  } as Mo00009OnewayType,

  or28285Oneway: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.care-plan2-select-next'),
  } as Mo00009OnewayType,
})

/**
 * Or10320双方向バインド
 */
const or10320Type = ref<Or10320OnewayType>({
  shisetuId: '',
  svJigyoId: '',
})

/**
 * メッセージ「i-cmn-11260」 - 削除確認画面
 */
const orX0001Type = ref<OrX0001Type>({
  deleteSyubetsu: '',
})

/**
 * OrX0001単方向バインド -  削除確認画面
 * メッセージ「i-cmn-11260」
 */
const orX0001Oneway = ref<OrX0001OnewayType>({
  createYmd: '',
  kinouKnj: '',
  selectTabName: '',
  startTabName: '',
  endTabName: '',
})

/**
 * or10929Type
 */
const or10929Type = ref<Or10929Type>({
  historySelectDataList: [],
})
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or29223Const.STR_ALL })
  })

  // 事業所選択プルダウンの検索条件設定
  // 検索条件未指定だと、ヘッダーで選択した適用事業所が表示される
  // 検索条件が変更されると、共通処理にて事業所の情報が更新される
  // 更新後やプルダウンの値変更後、変更後の事業所に基づいて処理を行う場合、
  // 関数「 jigyoListWatch 」にコールバック関数を渡す
  if (systemCommonsStore.getUserSelectSelfId()) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }

  // コントロール設定
  await init()
})
// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId !== '') {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const _jigyoInfo = jigyoInfoList?.find((jigyoInfo) => {
      return jigyoInfo.svJigyoId === newJigyoId
    })
    local.officeId = newJigyoId
    local.planPeriodId = 1
    local.historyId = 1
    retrieveCmCp1DataFlg.value = true
  }
}

/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [OrX0007Const.CP_ID(1)]: orX0007.value,
  [OrX0008Const.CP_ID(1)]: orX0008.value,
  [OrX0009Const.CP_ID(1)]: orX0009.value,
  [Or10929Const.CP_ID(1)]: or10929.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [OrX0010Const.CP_ID(0)]: orX0010.value,
  [OrX0001Const.CP_ID(1)]: orX0001.value,
  [Or10320Const.CP_ID(1)]: or10320.value,
  [OrX0115Const.CP_ID(1)]: orx0115.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
  [Or29449Const.CP_ID(0)]: or29449.value,
  [Or29224Const.CP_ID(0)]: or29224.value,
  [Or05658Const.CP_ID(0)]: or05658.value,
  [Or05656Const.CP_ID(0)]: or05656.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
})

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.organizing-issues'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: true,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */

const callbackUserChange = (newSelfId: string) => {
  systemCommonsStore.setUserId(newSelfId)
  void getTabsData()
}

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange)

// /**
//  * データの取得
//  */
// const userChange = async () => {
//   await getTabsData()
// }

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 利用者変更監視
 */
// watch(
//   () => userId.value,
//   async (newValue) => {
//     if (newValue === undefined) {
//       return
//     }

//     local.userId = newValue
//     // 利用者が変更された場合、データを再取得する
//     await userChange()
//   }
// )

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      favoriteIconClick()
      setOr11871Event({ favoriteEventFlg: false })
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      void _update()
      setOr11871Event({ saveEventFlg: false })
    }
    if (newValue.createEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 新規ボタンが押下された場合、新規作成処理を実行する
        void addBtnClick()
        setOr11871Event({ createEventFlg: false })
      }
    }
    if (newValue.printEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 印刷ボタンが押下された場合、印刷設定画面を表示する
        void printSettingIconClick()
        setOr11871Event({ printEventFlg: false })
      }
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      void masterIconClick()
      setOr11871Event({ masterEventFlg: false })
    }
  }
)

/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }

    const planID = newValue.planTargetPeriodId
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg
    const index = local.planPeriodList.findIndex((i) => i.sc1Id === local.planPeriod.sc1Id) || 0
    //「期間-前へ アイコンボタン」押下
    localOneway.orX0115Oneway.sc1Id = local.planPeriodId.toString()
    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }
    if (isEdit.value || deleteFlag.value) {
      // AC004-2と同じ
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case 'yes':
          // AC003(保存処理)を実行し
          await _update()

          // 処理続き
          // await getTabsData()
          break
        case 'no':
          // 処理続き
          // await getTabsData()
          break
        case 'cancel':
          // 処理終了
          return
      }
    }
    if (planUpdateFlg === '0') {
      local.planPeriodId = Number(planID)
      //「期間-選択確認前 アイコンボタン」押下
      void planTargetPeriodIconClick()
    } else if (planUpdateFlg === '1') {
      // 1件目の計画対象期間データが表示されている状態
      if (index <= 0) {
        const result = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11262'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        switch (result) {
          case 'yes':
            // 処理終了
            return
        }
        return
      }
      local.planPeriod = local.planPeriodList[index - 1]
      local.planPeriodId = Number(local.planPeriod.sc1Id)
      void planTargetPeriodIconClick()
      //更新フラグINIT
      OrX0007Logic.data.set({
        uniqueCpId: orX0007.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.planPeriodId.toString(),
          PlanTargetPeriodUpdateFlg: '',
        },
      })

      //データ再取得フラグ設定
      retrieveCmCp1DataFlg.value = true
    } else if (planUpdateFlg === '2') {
      // 最終件目の計画対象期間データが表示されている状態
      if (index + 1 >= local.planPeriodList.length) {
        const result = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11263'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        switch (result) {
          case 'yes':
            // 処理終了
            return
        }
        return
      }
      local.planPeriod = local.planPeriodList[index + 1]
      local.planPeriodId = Number(local.planPeriod.sc1Id)
      void planTargetPeriodIconClick()

      //更新フラグINIT
      OrX0007Logic.data.set({
        uniqueCpId: orX0007.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.planPeriodId.toString(),
          PlanTargetPeriodUpdateFlg: '',
        },
      })

      //データ再取得フラグ設定
      retrieveCmCp1DataFlg.value = true
    } else if (planUpdateFlg === '3') {
      //「期間-選択確認後 アイコンボタン」押下

      local.planPeriodId = Number(
        OrX0007Logic.data.get(orX0007.value.uniqueCpId)!.planTargetPeriodId
      )

      //更新フラグINIT
      OrX0007Logic.data.set({
        uniqueCpId: orX0007.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.planPeriodId.toString(),
          PlanTargetPeriodUpdateFlg: '',
        },
      })

      //データ再取得フラグ設定
      retrieveCmCp1DataFlg.value = true
    }
  },
  { deep: true }
)

/**
 * AC017_「履歴-前へアイコンボタン」押下
 * AC018_「履歴-次へアイコンボタン」押下
 * 履歴変更の監視
 */
watch(
  () => OrX0008Logic.data.get(orX0008.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }

    const createUpateFlg = newValue.createUpateFlg
    const index = local.historyList.findIndex((i) => i.kss1Id === local.history.kss1Id)
    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }
    if (isEdit.value || deleteFlag.value) {
      // AC004-2と同じ
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case 'yes':
          // AC003(保存処理)を実行し
          await _update()

          // 処理続き
          // await getTabsData()
          break
        case 'no':
          // 処理続き
          // await getTabsData()
          break
        case 'cancel':
          // 処理終了
          return
      }
    }
    if (createUpateFlg === '0') {
      local.history = local.historyList.find((i) => i.kss1Id === newValue.createId.toString()) ?? {}
      local.historyId = Number(local.history.kss1Id)
      void organizingIssuesHistoryClick()
    } else if (createUpateFlg === '1') {
      if (index <= 0) {
        return
      }
      //「履歴-前へ アイコンボタン」押下
      local.history = local.historyList[index - 1]
      local.historyId = Number(local.history.kss1Id)
      void organizingIssuesHistoryClick()
      retrieveCmCp1DataFlg.value = true
    } else if (createUpateFlg === '2') {
      if (index + 1 >= local.historyList.length) {
        return
      }
      //「履歴-前へ アイコンボタン」押下
      local.history = local.historyList[index + 1]
      local.historyId = Number(local.history.kss1Id)
      void organizingIssuesHistoryClick()

      retrieveCmCp1DataFlg.value = true
    } else if (createUpateFlg === '3') {
      //「履歴-選択確認後 アイコンボタン」押下
      local.historyId = Number(OrX0008Logic.data.get(orX0008.value.uniqueCpId)!.createId)

      //更新フラグINIT
      OrX0008Logic.data.set({
        uniqueCpId: orX0008.value.uniqueCpId,
        value: {
          createId: local.historyId.toString(),
          createUpateFlg: '',
        },
      })

      retrieveCmCp1DataFlg.value = true
    }
  },
  { deep: true }
)

/**
 * データ再取得フラグの監視
 */
watch(
  () => retrieveCmCp1DataFlg.value,
  (newValue) => {
    if (newValue) {
      // 共通情報取得
      const _param = {
        executeFlag: 'getData',
        deleteBtnValue: '',
        kikanKanriFlg: local.flag.periodManage,
        houjinId: local.houjinId,
        shisetuId: local.shisetuId,
        userId: systemCommonsStore.getUserId ?? '',
        svJigyoId: local.svJigyoId,
        raiId: local.raiId,
        kijunbiYmd: local.kijunbiYmd,
        sakuseiId: local.sakuseiId,
        historyModifiedCnt: local.history.modifiedCnt,
        historyInfo: local.history,
        planPeriodInfo: local.planPeriod,
      }
      local.Or29223.setInputComponentsFlg = true
    }
  }
)

/**
 * Input の 監視
 */
watch(
  () => local.Or29223.setInputComponentsFlg,
  (newValue) => {
    if (newValue) {
      //Input
      setChildCpBinds(props.uniqueCpId, {
        OrX0007_1: {
          twoWayValue: {
            planTargetPeriodId: localComponents.orX0007.planTargetPeriodId,
            PlanTargetPeriodUpdateFlg: localComponents.orX0007.PlanTargetPeriodUpdateFlg,
          },
        },
        OrX0008_1: {
          twoWayValue: {
            createId: localComponents.orX0008.createId,
            createUpdateFlg: localComponents.orX0008.createUpateFlg,
          },
        },
      })
      local.Or29223.setInputComponentsFlg = false
    }
  }
)

/**
 * AC011_「削除」押下_AC011
 */
watch(
  () => orX0001Type.value,
  (newValue) => {
    if (newValue) {
      // はい
      if ('0' !== newValue.deleteSyubetsu) {
        // 削除確認画面ダイアログに「現在表示している画面のみ削除する。」を選択する場合
        if ('1' === newValue.deleteSyubetsu) {
          // アセスメント(インターライ)画面履歴の最新情報を取得する
          deleteCurrentTabSubInfo(newValue.deleteSyubetsu)
        }
        // 削除確認画面ダイアログに「表示している画面を履歴ごと削除する」を選択する場合
        else if ('2' === newValue.deleteSyubetsu) {
          deleteAllTabSubInfo(newValue.deleteSyubetsu)
        }

        // TODO 作成者選択アイコンボタン、基準日、基準日カレンダを非活性

        // ボタンは実行無効(新規、複写、印刷、削除)
        deleteFlag.value = true
      }
    }
  }
)

/**************************************************
 * 関数
 **************************************************/
/**
 * すべてのタブページのデータを削除する
 *
 * @param _param - 削除ボタンの選択値
 */
const deleteAllTabSubInfo = (_param: string) => {
  const _paramDelete = {
    executeFlag: 'delete',
    deleteBtnValue: _param,
    kikanKanriFlg: local.flag.periodManage,
    houjinId: local.houjinId,
    shisetuId: local.shisetuId,
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: local.svJigyoId,
    raiId: local.raiId,
    kijunbiYmd: local.kijunbiYmd,
    sakuseiId: local.sakuseiId,
    historyModifiedCnt: local.history.modifiedCnt,
    historyInfo: local.history,
    planPeriodInfo: local.planPeriod,
  } as TransmitParam
  // TODO 他タブ担当実装コードを追加します
}

/**
 * 現在のタブページのデータを削除する
 *
 * @param _param - 削除ボタンの選択値
 */
const deleteCurrentTabSubInfo = (_param: string) => {
  const _paramDelete = {
    executeFlag: 'delete',
    deleteBtnValue: _param,
    kikanKanriFlg: local.flag.periodManage,
    houjinId: local.houjinId,
    shisetuId: local.shisetuId,
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: local.svJigyoId,
    raiId: local.raiId,
    kijunbiYmd: local.kijunbiYmd,
    sakuseiId: local.sakuseiId,
    historyModifiedCnt: local.history.modifiedCnt,
    historyInfo: local.history,
    planPeriodInfo: local.planPeriod,
  } as TransmitParam
}

/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * 画面コントロール表示設定
 *
 * @param planPeriodFlag - 計画期間フラグ
 *
 * @param planPeriodInfo - 計画期間情報
 *
 * @param historyInfo - 履歴情報
 */
const setFormDataHeader = (
  planPeriodFlag: string,
  planPeriodInfo: PlanPeriodInfo,
  historyInfo: HistoryInfo
) => {
  // 期間管理フラグ
  // local.flag.periodManage = planPeriodFlag
  // 履歴情報
  local.history = historyInfo
  // 計画期間情報
  local.planPeriod = planPeriodInfo
  // 計画対象期間を設定
  if (local.flag.periodManage) {
    // 期間管理フラグが「1:管理する」
    if ('1' === local.flag.periodManage) {
      // 計画対象期間 を表示にする
      planPeriodShow.value = true
      // 期間総件数 = 0（期間なし）
      if (!local.flag.periodManage || local.planPeriodList.length === 0) {
        // 計画対象期間-ページングを"0 / 0"で表示にする
        localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = 0
        localOneway.orX0007Oneway.planTargetPeriodData.totalCount = 0
        localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId = 0
        localComponents.orX0007.planTargetPeriodId = '0'

        // 履歴、作成者、基準日、入力フームを非表示にする。
        historyShow.value = false
        authorShow.value = false
        baseDateShow.value = false
        // 履歴情報
        local.history = {} as HistoryInfo
        local.totalCount = 0
        local.addBtnState = false

        deleteAllTabSubInfo('2')
        return
      } else {
        local.planPeriodId = parseInt(planPeriodInfo.sc1Id)
        const index = local.planPeriodList.findIndex((i) => i.sc1Id === local.planPeriod.sc1Id) || 0
        localOneway.orX0007Oneway.planTargetPeriodData = {
          planTargetPeriodId: parseInt(planPeriodInfo.sc1Id),
          planTargetPeriod: planPeriodInfo.startYmd + ' ~ ' + planPeriodInfo.endYmd,
          currentIndex: index + 1,
          totalCount: local.planPeriodList.length,
        } as PlanTargetPeriodDataType
        localComponents.orX0007.planTargetPeriodId =
          planPeriodInfo.sc1Id.toString() || planPeriodInfo.sc1Id

        // 履歴、作成者、基準日、入力フームを非表示にする。
        historyShow.value = true
        authorShow.value = true
        baseDateShow.value = true
      }
    }
    // 期間管理フラグが「0:管理しない」
    else if ('0' === local.flag.periodManage) {
      // 計画対象期間 を非表示にする
      planPeriodShow.value = false
    }
  } else {
    local.addBtnState = false
  }
  // 履歴を設定
  // 履歴が存在の場合
  if (historyInfo) {
    // 期間総件数
    local.totalCount = local.historyList.length
    const index = local.historyList.findIndex((i) => i.kss1Id === historyInfo.kss1Id)
    historyShow.value = true

    local.historyId = parseInt(historyInfo.kss1Id ?? '0')

    local.createDate.value = systemCommonsStore.getSystemDate!
    if (local.createDate.mo01343) {
      local.createDate.mo01343.value = systemCommonsStore.getSystemDate!
    }

    localOneway.orX0008Oneway.createData = {
      createId: historyInfo.kss1Id ?? '',
      createDate: local.history.createYmd ?? '',
      staffId: local.history.shokuId ?? '',
      staffName: local.history.shokuName ?? '',
      currentIndex: (index <= 0 ? 0 : index) + 1,
      totalCount: local.totalCount,
    }
    localComponents.orX0008.createId = historyInfo.kss1Id ?? ''

    // 作成者を設定
    localOneway.orX0009Oneway.createData = {
      createId: 0,
      createDate: '',
      staffId: historyInfo.shokuId,
      staffName: historyInfo.shokuName,
      currentIndex: index + 1,
      totalCount: local.totalCount,
      ks21Id: '',
    } as PlanCreateDataType

    // 基準日を設定
    local.createDate.value = historyInfo.createYmd ?? ''
  } else {
    local.totalCount = 0
    // 基準日を設定
    local.createDate.value = systemCommonsStore.getSystemDate!

    // 作成者を設定
    // 作成者 ＝ ログイン情報.職員名
    localOneway.orX0009Oneway.createData = {
      createId: 0,
      createDate: '',
      staffId: systemCommonsStore.getCurrentUser.chkShokuId,
      staffName: systemCommonsStore.getCurrentUser.shokuinKnj,
      currentIndex: 1,
      totalCount: 1,
      ks21Id: '',
    } as PlanCreateDataType

    // 履歴-ページング = "1 / 1"
    localOneway.orX0008Oneway.createData = {
      createId: '0',
      createDate: systemCommonsStore.getSystemDate ?? '',
      staffId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
      currentIndex: 1,
      totalCount: 1,
    }
    localComponents.orX0008.createId = '0'
    // 「AC004」の処理を行
    void addBtnClick()
  }
}

/**
 *  AC001_初期表示
 */
const init = async () => {
  localOneway.mo00043OnewayType.tabItems = [
    {
      id: 'facts-of-the-situation',
      title: t('label.facts-of-the-situation'),
      tooltipText: t('label.facts-of-the-situation'),
      tooltipLocation: 'bottom',
    },
    {
      id: 'prospect',
      title: t('label.prospect_tab_title'),
      tooltipText: t('label.prospect_tab_title'),
      tooltipLocation: 'bottom',
    },
  ]
  local.mo00043.id = 'facts-of-the-situation'

  styleFlag.value = props.onewayModelValue.styleFlag

  await getTabsData()

  local.Or29223.setInputComponentsFlg = true
}

/**
 * AC002_「お気に入りアイコンボタン」押下
 */
const favoriteIconClick = () => {
  // TODO お気に入りに該当機能がない場合
  if (favorite.value) {
    // 「アセスメント(インターライ)」をお気に入り機能に追加する。
  }
  // お気に入りに該当機能がある場合
  else {
    // 「アセスメント(インターライ)」をお気に入り機能から削除する。
  }
}

/**
 * AC003_「保存ボタン」押下
 *
 * @param _isRunInit - フラグ for whether to run initialization
 */
const _update = async (_isRunInit = true) => {
  // 画面入力データに変更がない場合
  if (!isEdit.value && !deleteFlag.value && !local.addBtnState) {
    void openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-21800'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // 処理終了にする
    return
  }
  let status = '0'
  if (deleteFlag.value) {
    status = 'D'
  } else if (local.addBtnState) {
    status = 'C'
  } else if (isEdit.value) {
    status = 'U'
  }
  // or05656
  const dataOr05656 = getChildCpBinds(or29224.value.uniqueCpId, {
    Or05656: { cpPath: 'Or05656', twoWayFlg: true },
  })
  const getor05656 = dataOr05656.Or05656.twoWayBind?.value as Or05656OnewayType[][]

  const ungroupItems = or29224Ref.value
    ? await or29224Ref.value.ungroupItems(getor05656)
    : ([] as unknown[])
  // Or05658
  const getOr05658 = getChildCpBinds(or29224.value.uniqueCpId, {
    Or05658: { cpPath: 'Or05658', twoWayFlg: true },
  })
  const or05658Data = getOr05658.Or05658.twoWayBind?.value as Or05658Type

  //Or05061
  const getOr05061 = getChildCpBinds(or29449.value.uniqueCpId, {
    Or05061: { cpPath: 'Or05061', twoWayFlg: true },
  })
  const or05061Data =
    (getOr05061.Or05061.twoWayBind?.value as Or05061Type[]) || local.Or29223.mitosiSyosaiList
  //Or05059
  const getOr05059 = getChildCpBinds(or29449.value.uniqueCpId, {
    Or05059: { cpPath: 'Or05059', twoWayFlg: true },
  })
  const or05059Data = (getOr05059.Or05059.twoWayBind?.value as Or05059Type) || {
    value: local.Or29223.rirekiObj.ikouKnj,
  }
  setChildCpBinds(or29224.value.uniqueCpId, {
    Or05656: {
      twoWayValue: getor05656 ?? [],
    },
  })

  setChildCpBinds(or29224.value.uniqueCpId, {
    Or05658: {
      twoWayValue: or05658Data ?? [],
    },
  })
  setChildCpBinds(or29449.value.uniqueCpId, {
    Or05061: {
      twoWayValue: or05061Data,
    },
  })

  setChildCpBinds(or29449.value.uniqueCpId, {
    Or05059: {
      twoWayValue: or05059Data,
    },
  })

  // 入力値処理です
  const inputData: OrganizingIssuesUpdateInEntity = {
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    // houjinId: '1',
    // shisetuId: '6',
    // userId: systemCommonsStore.getUserId ?? '',
    // svJigyoId: '21',
    // syubetsuId: '2',
    historyUpdateKbn: status,
    historyModifiedCnt: local.history.modifiedCnt,
    sc1Id: local.planPeriodId.toString(),
    kss1Id: local.historyId.toString(),
    createYmd: local.createDate.value,
    shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
    youshikiId: or05658Data?.youshikiId ?? '',
    youin1Knj: or05658Data?.youin1Knj ?? '',
    youin2Knj: or05658Data?.youin2Knj ?? '',
    youin3Knj: or05658Data?.youin3Knj ?? '',
    youin4Knj: or05658Data?.youin4Knj ?? '',
    youin5Knj: or05658Data?.youin5Knj ?? '',
    youin6Knj: or05658Data?.youin6Knj ?? '',
    ikouKnj:
      or05059Data?.value
        .replace(/\u2026/g, '')
        .replace(/\.\.\./g, '')
        .replace(/\r?\n/g, '')
        .trim() ?? '',
    jyokyoSyosaiList: Array.isArray(ungroupItems) ? ungroupItems : [],
    mitosiSyosaiList: or05061Data?.length
      ? or05061Data.map((i: Or05061Type) => {
          const getStringValue = (obj: unknown): string => {
            if (obj && typeof obj === 'object' && obj !== null && 'value' in obj) {
              const valueObj = obj as { value: unknown }
              return typeof valueObj.value === 'string' ? valueObj.value : ''
            }
            return ''
          }

          const kadaiKnjValue = getStringValue(i.kadaiKnj)
          const mitosiKnjValue = getStringValue(i.mitosiKnj)
          const yusenNoValue = getStringValue(i.yusenNo) || '0'
          return {
            kadaiKnj: kadaiKnjValue
              .replace(/\u2026/g, '')
              .replace(/\.\.\./g, '')
              .replace(/\r?\n/g, '')
              .trim(),
            kss1Id: i.kss1Id ?? '',
            kss3Id: i.kss3Id ?? '',
            mitosiKnj: mitosiKnjValue
              .replace(/\u2026/g, '')
              .replace(/\.\.\./g, '')
              .replace(/\r?\n/g, '')
              .trim(),
            modifiedCnt: i.modifiedCnt ?? '0',
            sort: i.sort ?? '0',
            updateKbn: i.updateKbn ?? '0',
            yusenNo: yusenNoValue,
          }
        })
      : [],
  }
  await onSave(inputData)

  if (deleteFlag.value && local.historyList.length <= 1) {
    await addBtnClick()
  } else {
    void planTargetPeriodIconClick()
  }
  local.addBtnState = false
  deleteFlag.value = false
  // 一時的なデモ---START
  // void printOutput()
  // 一時的なデモ---END
}
async function onSave(inputData: OrganizingIssuesUpdateInEntity) {
  const resData: OrganizingIssuesUpdateOutEntity = await ScreenRepository.update(
    'organizingIssuesUpdate',
    inputData
  )
  // 保存成功の場合
  if (resData.statusCode === '200') {
    return true
  } else {
    return false
  }
}
/**
 * AC004_「新規ボタン」押下
 */
const addBtnClick = async () => {
  // 二回目新規ボタン押下する場合
  if (local.addBtnState) {
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.attention'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.assessment')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  }

  // 共通情報.基準日
  local.createDate.value = systemCommonsStore.getSystemDate!
  if (local.createDate.mo01343) {
    local.createDate.mo01343.value = systemCommonsStore.getSystemDate!
  }

  local.addBtnState = true
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()
        // 処理続き
        // addBtnClick()
        break
      case 'no':
        // 処理続き
        // addBtnClick()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }
  // バックエンドAPIから初期情報取得
  const inputData: OrganizingIssuesNewInitSelectInEntity = {
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId!,
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    // houjinId: '1',
    // shisetuId: '6',
    // svJigyoId: '21',
    userId: systemCommonsStore.getUserId ?? '',
    cpnFlg: props?.cpnFlg ?? '9',
    enabledRaiass: local.mo00043.id === 'facts-of-the-situation' ? '1' : '0',
    enabledRaiass2: local.mo00043.id === 'prospect' ? '1' : '0',
  }

  const result: OrganizingIssuesNewInitSelectOutEntity = await ScreenRepository.select(
    'organizingIssuesNewInitSelect',
    inputData
  )
  if (result.data) {
    let kikanObj = {} as PlanPeriodInfo
    let rirekiObj = {} as HistoryInfo
    kikanObj = local.planPeriod
    rirekiObj = {
      kss1Id: local.planPeriodId.toString(),
      sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
      createYmd: local.createDate.value ?? '',
      shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      shokuName: OrX0008Logic.data.get(orX0008.value.uniqueCpId)!.rirekiObj?.staffName ?? '',
      youin1Knj: '',
      youin2Knj: '',
      youin3Knj: '',
      youin4Knj: '',
      youin5Knj: '',
      youin6Knj: '',
      ikouKnj: '',
      youshikiId: '',
      modifiedCnt: '0',
      krirekiNo: '0',
      krirekiTotalCnt: '0',
    }
    if (!deleteFlag.value) {
      local.historyList.push(rirekiObj)
    }

    const newData = {
      kikanFlg: '',
      /** 計画期間情報 */
      kikanObj: kikanObj,
      /** 履歴情報 */
      rirekiObj: rirekiObj,
      /** 状況の事実リスト */
      jyokyoSyosaiList: result.data.jyokyoSyosaiList ? result.data.jyokyoSyosaiList : [],
      /** 見通しリスト */
      mitosiSyosaiList: result.data.mitosiSyosaiList ? result.data.mitosiSyosaiList : [],
    } as Or29223Type
    setFormDataTabs(newData)
    setFormDataHeader(local.Or29223.kikanFlg, kikanObj, rirekiObj)
  }
}

/**
 * アセスメント(インターライ)画面履歴の最新情報を取得する
 */
const getTabsData = async () => {
  if (!historyShow.value && !authorShow.value && !baseDateShow.value) {
    return
  }
  // バックエンドAPIから初期情報取得
  const inputData: OrganizingIssuesInitSelectInEntity = {
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    shisetuId: systemCommonsStore.getShisetuId!,
    userId: local.userId,
    syubetsuId: '',
    // svJigyoId: '21',
    // shisetuId: '6',
    // userId: systemCommonsStore.getUserId ?? '',
    // syubetsuId: '2',
  }

  const result: OrganizingIssuesInitSelectOutEntity = await ScreenRepository.select(
    'organizingIssuesInitSelect',
    inputData
  )
  if (result.data) {
    let kikanObj: KikanObjType | undefined
    let rirekiObj = {} as HistoryInfo
    local.planPeriodList = result.data.kikanObj
    if (isInit.value) {
      kikanObj = result.data.kikanObj[result.data.kikanObj.length - 1]
      isInit.value = false
    } else {
      kikanObj = local.planPeriodList.find((i) => i.sc1Id === local.planPeriodId.toString())
    }
    local.flag.periodManage = local.Or29223.kikanFlg = result.data.kikanFlg
    local.historyList = result.data.rirekiObj.filter((i) => i.sc1Id === kikanObj?.sc1Id)

    rirekiObj = local.historyList?.length ? local.historyList[local.historyList.length - 1] : {}

    const newData = {
      kikanFlg: '',
      /** 計画期間情報 */
      kikanObj: kikanObj,
      /** 履歴情報 */
      rirekiObj: rirekiObj,
      /** 状況の事実リスト */
      jyokyoSyosaiList: result.data.jyokyoSyosaiList ? result.data.jyokyoSyosaiList : [],
      /** 見通しリスト */
      mitosiSyosaiList: result.data.mitosiSyosaiList ? result.data.mitosiSyosaiList : [],
    } as Or29223Type
    setFormDataTabs(newData)
    if (kikanObj) {
      setFormDataHeader(local.Or29223.kikanFlg, kikanObj, rirekiObj)
    }
  }
}

function setFormDataTabs(result: Or29223Type) {
  local.Or29223 = {
    ...result,
    setInputComponentsFlg: false,
  }
  // tab 2
  local.or29449.rirekiObj = {
    ...local.Or29223.rirekiObj,
    ikouKnj: local.Or29223?.rirekiObj.ikouKnj,
  }
  local.or29449.kikanFlg = local.Or29223.kikanFlg
  local.or29449.kikanObj = local.Or29223.kikanObj
  local.or29449.mitosiSyosaiList = local.Or29223.mitosiSyosaiList.map((i) => {
    return {
      ...i,
      kadaiKnj: i.kadaiKnj,
      mitosiKnj: i.mitosiKnj,
    }
  })
  setChildCpBinds(props.uniqueCpId, {
    Or29449: {
      twoWayValue: {
        isInit: true,
        ...local.or29449,
      },
    },
  })
  setChildCpBinds(or29449.value.uniqueCpId, {
    Or05059: {
      twoWayValue: { value: local.or29449.rirekiObj.ikouKnj },
    },
  })

  //tab 1
  local.or29224.rirekiObj = local.Or29223.rirekiObj
  local.or29224.jyokyoSyosaiList = local.Or29223.jyokyoSyosaiList.map((i) => {
    return {
      ...i,
      bikoKnj: i.bikoKnj,
    }
  })
  setChildCpBinds(props.uniqueCpId, {
    Or29224: {
      twoWayValue: {
        isInit: true,
        ...local.or29224,
      },
    },
  })
}

/**
 * 表示制御用に文字列を最大34文字＋[…]に省略する
 *
 * @param text - 入力文字列
 *
 * @param numPerLine - 一行あたりの文字数
 *
 * @param numberLine - 行数
 *
 * @returns 表示用文字列（最大numPerLine文字、超過部分は[…]で省略）
 */
// function formatForDisplay(text: string, numPerLine: number, numberLine: number): string {
//   if (!text?.length) return ''

//   const cleaned = text.replace(/\r?\n/g, '').trim()
//   const lines: string[] = []
//   let line = '',
//     i = 0

//   while (i < cleaned.length && lines.length < numberLine) {
//     line += cleaned[i]
//     if (line.length === numPerLine) {
//       lines.push(line)
//       line = ''
//     }
//     i++
//   }

//   if (line) lines.push(line)

//   if (i < cleaned.length && lines.length === numberLine) {
//     lines[numberLine - 1] = lines[numberLine - 1].slice(0, numPerLine - 1) + '…'
//   }

//   return lines.join('\n')
// }

/**
 * AC005_「複写ボタン」押下
 */
const copyBtnClick = () => {
  // 実行無効制御
  if (deleteFlag.value) {
    return
  }

  // TODO 画面未作成 GUI00807 アセスメント複写画面をポップアップで起動する
  const _param = {
    // 利用者ID
    userId: systemCommonsStore.getUserId ?? '',
    // 複写先計画期間ID
    sc1Id: local.planPeriodId,
    // 複写先アセスメントID
    assessmentId: local.history.raiId,
    // タブID
    tabId: '1',
    // タブタイトル
    tabTitle: 'A',
    // 複写先改訂フラグ
    copyBeforeRevisionFlag: '-',
    // 期間管理フラグ
    periodManageFlag: local.flag.periodManage,
    // まとめフラグ
    summaryFlag: '-',
  }
  const result = {
    type: '',
  }

  // 単一セクション複写情報を返却する場合
  if (result.type === '1') {
    // TODO 本タブ担当実装コードを追加します
    const _param2 = {
      executeFlag: 'copy',
      deleteBtnValue: '',
      kikanKanriFlg: local.flag.periodManage,
      houjinId: local.houjinId,
      shisetuId: local.shisetuId,
      userId: systemCommonsStore.getUserId ?? '',
      svJigyoId: local.svJigyoId,
      raiId: local.raiId,
      kijunbiYmd: local.kijunbiYmd,
      sakuseiId: local.sakuseiId,
      historyModifiedCnt: local.history.modifiedCnt,
      historyInfo: local.history,
      planPeriodInfo: local.planPeriod,
    } as TransmitParam
  }
  // 複数セクション複写情報を返却するの場合
  else if (result.type === '2') {
    void _update()
  }
}

/**
 * AC006_「印刷設定アイコンボタン」押下
 */
const printSettingIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()

        // 処理続き
        await getTabsData()
        break
      case 'no':
        // 処理続き
        await getTabsData()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // TODO GUI00924 印刷設定画面をポップアップで起動する。
}

/**
 * AC007_「マスタ他設定アイコンボタン」押下
 */
const masterIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()

        // 処理続き
        await getTabsData()
        break
      case 'no':
        // 処理続き
        await getTabsData()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // Or10320のダイアログ開閉状態を更新する
  Or10320Logic.state.set({
    uniqueCpId: or10320.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC011_「削除」押下
 */
const deleteClick = async () => {
  // 実行無効制御
  if (deleteFlag.value) {
    return
  }

  // AC004-2と同じ
  const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11326', [local.createDate.value, t('label.organizing-issues')]),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })

  switch (dialogResult) {
    case 'yes':
      deleteFlag.value = true
      break
    case 'no':
      deleteFlag.value = false
      local.addBtnState = false
      break
    case 'cancel':
      // 処理終了
      return
  }
}
/**
 * AC022_タブ選択
 *
 * @param param - タブ選択ID
 */
const updateModelValue = async (param: Mo00043Type) => {
  if (!historyShow.value && !authorShow.value && !baseDateShow.value) {
    deleteAllTabSubInfo('2')
  }

  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()

        // 処理続き
        // await getTabsData()
        break
      case 'no':
        // 処理続き
        await getTabsData()
        deleteFlag.value = false
        local.addBtnState = false
        break
      case 'cancel':
        // 処理終了
        return
    }
  }
  // else {
  //   await getTabsData()
  // }

  local.mo00043.id = param.id
}

/**
 * AC021_「基準日カレンダ」押下
 *
 * @param dateValue - 基準日
 */
const _changeCreateDate = (dateValue: Mo00020Type) => {
  // 親情報を設定
  local.createDate.value = dateValue.value
}

/**
 * AC013_「計画対象期間選択アイコンボタン」押下
 */
const planTargetPeriodIconClick = async () => {
  // organizingIssuesPlanPeriodSelect
  // バックエンドAPIから初期情報取得
  const inputData: OrganizingIssuesPlanPeriodSelectInEntity = {
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    shisetuId: systemCommonsStore.getShisetuId!,
    userId: systemCommonsStore.getUserId!,
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    sc1Id: local.planPeriodId.toString(),
    kikanFlg: '1',
    kss1Id: local.historyId.toString(),
    youshikiId: '1',
    // svJigyoId: '21',
    // shisetuId: '6',
    // userId: systemCommonsStore.getUserId ?? '',
    // syubetsuId: '2',
    // kss1Id: local.historyId.toString(),
    // youshikiId: '1',
    // sc1Id: local.planPeriodId.toString(),
    // kikanFlg: '1',
  }

  const result: OrganizingIssuesPlanPeriodSelectOutEntity = await ScreenRepository.select(
    'organizingIssuesPlanPeriodSelect',
    inputData
  )
  if (result.data) {
    let kikanObj: KikanObjType = {} as KikanObjType
    let rirekiObj: HistoryInfo = {} as HistoryInfo

    local.planPeriodList = result.data.kikanObj
    if (local.planPeriodId) {
      const foundKikanObj = result.data.kikanObj.find(
        (i) => i.sc1Id === local.planPeriodId.toString()
      )
      if (foundKikanObj) {
        kikanObj = foundKikanObj
      } else {
        kikanObj = {} as KikanObjType
      }

      local.historyList = result.data.rirekiObj.filter((i) => i.sc1Id === kikanObj.sc1Id)
      rirekiObj =
        local.historyList.find((i) => i.kss1Id === local.historyId.toString()) ??
        local.historyList[local.historyList.length - 1]
    }
    const newData = {
      kikanFlg: local.Or29223?.kikanFlg ?? '',
      /** 履歴情報 */
      rirekiObj: rirekiObj,
      /** 状況の事実リスト */
      jyokyoSyosaiList: result.data.jyokyoSyosaiList,
      /** 見通しリスト */
      mitosiSyosaiList: result.data.mitosiSyosaiList,
      /** 計画期間情報 */
      kikanObj: kikanObj,
    } as Or29223Type
    setFormDataTabs(newData)
    setFormDataHeader(local.Or29223?.kikanFlg ?? '', newData.kikanObj, rirekiObj)
  }
}
const organizingIssuesHistoryClick = async () => {
  // organizingIssuesHistorySelect
  // バックエンドAPIから初期情報取得
  const inputData: OrganizingIssuesHistorySelectInEntity = {
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    userId: systemCommonsStore.getUserId!,
    sc1Id: local.planPeriodId.toString(),
    kss1Id: local.historyId.toString(),
    kikanFlg: '1',
    shisetuId: '6',
    syubetsuId: '2',
    rirekiID: '0',
    youshikiId: '1',
    // svJigyoId: '21',
    // userId: systemCommonsStore.getUserId ?? '',
    // sc1Id: local.planPeriodId.toString(),
    // kss1Id: local.historyId.toString(),
    // shisetuId: '6',
    // syubetsuId: '2',
    // kikanFlg: '1',
    // rirekiID: '0',
    // youshikiId: '1',
  }

  const result: OrganizingIssuesHistorySelectOutEntity = await ScreenRepository.select(
    'organizingIssuesHistorySelect',
    inputData
  )
  if (result.data) {
    let rirekiObj = {} as HistoryInfo
    if (local.history) {
      rirekiObj = result.data.rirekiObj.find((i) => i.kss1Id === local.historyId.toString()) ?? {}
    }
    const newData = {
      kikanFlg: '',
      /** 計画期間情報 */
      kikanObj: local.planPeriod,
      /** 履歴情報 */
      rirekiObj: rirekiObj,
      /** 状況の事実リスト */
      jyokyoSyosaiList: result.data.jyokyoSyosaiList,
      /** 見通しリスト */
      mitosiSyosaiList: result.data.mitosiSyosaiList,
    } as Or29223Type
    setFormDataTabs(newData)
    setFormDataHeader(local.Or29223.kikanFlg, local.planPeriod, rirekiObj)
  }
}
/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 確認ダイアログOneWayBind領域用の構造
 *
 * @returns ダイアログの結果を返す Promise
 */
const openConfirmDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * AC016_選択前の履歴から変更
 *
 * @param selectItem - 選択の履歴
 */
const historySelectChange = async (selectItem: HistorySelectTableDataItem | undefined) => {
  // 選択前の履歴から変更がない場合
  if (!selectItem) {
    // 処理終了にする。
    return
  }
  // 選択前の履歴から変更がある場合
  else {
    // アセスメント(インターライ)画面履歴変更処理
    local.historyId = parseInt(selectItem.raiId)

    await getTabsData()
  }
}

/**
 * 保存後の帳票出力
 */
const _printOutput = async () => {
  const reportId = 'tab-name'
  // TODO GUI00924
  await reportOutput(reportId, {})
}
</script>

<template>
  <c-v-sheet class="view h-100">
    <div class="action-sticky">
      <g-base-or11871 v-bind="or11871">
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            :disabled="deleteFlag"
            @click="copyBtnClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-copy-btn')"
            />
          </c-v-list-item>
        </template>
        <template #optionMenuItems>
          <c-v-list-item
            :title="
              t(
                'label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-delete'
              )
            "
            prepend-icon="delete"
            @click="deleteClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.delete-data')"
            />
          </c-v-list-item>
        </template>
      </g-base-or11871>
    </div>

    <div class="action-content">
      <c-v-row
        no-gutters
        class="main-Content"
      >
        <!-- ナビゲーションエリア -->
        <c-v-col
          cols="auto"
          class="hidden-scroll h-100 mt-1 ml-2 main-left"
        >
          <!-- 利用者選択一覧 -->
          <g-base-or00248
            v-bind="or00248"
            class="userList"
          />
        </c-v-col>
        <!-- コンテンツエリア -->
        <c-v-col class="main-right hidden-scroll h-100">
          <!-- 上段 -->
          <c-v-row
            no-gutters
            class="top"
          >
            <c-v-col>
              <!-- 事業所 -->
              <c-v-row
                no-gutters
                class="d-flex align-end"
                style="padding-left: 24px"
              >
                <c-v-col
                  cols="auto mr-6 office-select"
                  style="width: 212px"
                  ><g-base-or-41179 v-bind="or41179"
                /></c-v-col>
                <!-- </c-v-row>
            <c-v-row no-gutters> -->
                <!-- 計画対象期間 -->
                <c-v-col
                  v-show="planPeriodShow"
                  cols="auto"
                >
                  <g-custom-orX0007
                    v-bind="orX0007"
                    :is-edit="isEdit"
                    :parent-method="_update"
                    :oneway-model-value="localOneway.orX0007Oneway"
                    :unique-cp-id="orX0007.uniqueCpId"
                  />
                </c-v-col>

                <!-- 基準日 -->
                <c-v-col
                  v-show="baseDateShow"
                  cols="auto ml-4"
                >
                  <g-custom-or-x0010
                    class="custom-required"
                    v-bind="orX0010"
                    :oneway-model-value="{
                      ...localOneway.orX0010Oneway,
                    }"
                    :unique-cp-id="orX0010.uniqueCpId"
                  />
                </c-v-col>
                <!-- 作成者 -->
                <c-v-col
                  v-show="authorShow"
                  cols="auto ml-4"
                  style="align-content: center"
                >
                  <!-- TODO GUI00220 職員検索画面未作成 -->
                  <g-custom-orX0009
                    v-bind="orX0009"
                    :oneway-model-value="localOneway.orX0009Oneway"
                    :unique-cp-id="orX0009.uniqueCpId"
                  />
                </c-v-col>

                <!-- 履歴 -->
                <c-v-col
                  v-show="historyShow"
                  cols="auto ml-4"
                >
                  <g-custom-orX0008
                    v-bind="orX0008"
                    :is-edit="isEdit"
                    :parent-method="_update"
                    :oneway-model-value="localOneway.orX0008Oneway"
                    :unique-cp-id="orX0008.uniqueCpId"
                  />
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutters
                class="tabItems"
              >
                <c-v-col class="containerContent">
                  <base-mo00043
                    :model-value="local.mo00043"
                    :oneway-model-value="localOneway.mo00043OnewayType"
                    style="padding-left: 24px !important; padding-right: 0px !important"
                    @update:model-value="updateModelValue"
                  >
                  </base-mo00043>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <!-- main -->
            <c-v-col
              cols="12"
              class="mb-4"
              style="padding-left: 24px"
            >
              <c-v-window
                v-model="local.mo00043.id"
                class="containerContent"
              >
                <c-v-window-item value="facts-of-the-situation">
                  <g-custom-or-29224
                    v-if="!deleteFlag"
                    v-bind="or29224"
                    ref="or29224Ref"
                    :model-value="local.or29224"
                    :parent-unique-cp-id="props.uniqueCpId"
                  />
                </c-v-window-item>
                <c-v-window-item
                  value="prospect"
                  class="mt-2"
                >
                  <g-custom-or-29449
                    v-if="!deleteFlag"
                    v-bind="or29449"
                    ref="or29449Ref"
                    :model-value="local.or29449"
                    :parent-unique-cp-id="props.uniqueCpId"
                  />
                </c-v-window-item> </c-v-window
            ></c-v-col>
          </c-v-row>
          <!-- 中段 -->
        </c-v-col>
      </c-v-row>
    </div>
    <!-- 下段 -->
    <!-- <c-v-row
      no-gutters
      class="footer"
    >
      <c-v-col>
        <g-base-or00051 />
      </c-v-col>
    </c-v-row> -->
  </c-v-sheet>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- メッセージ -->
  <g-base-or-21815 v-bind="or21815" />

  <!-- GUI00627 アセスメント（インターライ）マスタ -->
  <!-- GUI00420 病名マスタ -->
  <!-- GUI00671 薬剤マスタ -->
  <!-- GUI02353 薬剤単位マスタ -->
  <!-- <g-custom-or-10320
    v-bind="or10320"
    v-model="or10320Type"
    :oneway-model-value="localOneway.or10320Oneway"
  /> -->

  <!-- GUI00070 対象期間画面 -->
  <g-custom-or-x-0115
    v-bind="orx0115"
    :oneway-model-value="localOneway.orX0115Oneway"
  ></g-custom-or-x-0115>

  <!-- メッセージ「i-cmn-11260」 -->
  <!--  削除確認画面 -->
  <g-custom-or-x-0001
    v-bind="orX0001"
    v-model="orX0001Type"
    :oneway-model-value="orX0001Oneway"
  ></g-custom-or-x-0001>

  <!-- GUI00792 ［履歴選択］画面 -->
  <g-custom-or-10929
    v-bind="or10929"
    v-model="or10929Type"
    :oneway-model-value="localOneway.or10929Oneway"
    @update:model-value="historySelectChange"
  />
</template>

<style scoped lang="scss">
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.main-Content {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;

  .main-left {
    max-width: 274px;

    .userList {
      :deep(.v-list) {
        max-height: 1019px !important;
      }
    }
  }
  .main-right {
    height: 100%;
    display: flex;
    flex-direction: column;
    .top {
      flex: 0 1 auto;

      :deep(.v-sheet) {
        background-color: transparent !important;
      }
    }

    .footer {
      flex: 0 1 auto;
    }
  }
}

.planPeriod {
  :first-child {
    align-items: center;
  }
}

.createDateOuterClass {
  background: rgb(var(--v-theme-background));
}

.tabItems {
  margin-top: 8px;
  // border-top: thin solid rgb(var(--v-theme-form));
  :deep(.v-slide-group__prev) {
    display: none;
  }
  :deep(.v-slide-group__next) {
    display: none;
  }
  :deep(.v-tab) {
    padding-left: 0px !important;
    padding-right: 0px !important;
    margin-right: 16px;
  }
}

.containerContent {
  margin: 0 auto;
  width: 100%;
}
.border-left {
  border-left: 1px solid rgba(var(--v-theme-black-100));
  border-radius: 0 !important;
  align-self: center;
}
.disabled-event {
  pointer-events: none;
}
:deep(.jigyo) {
  flex-direction: column;
  position: relative;
  bottom: 12px;
  .v-input {
    width: 203px !important;
  }
  .v-field.v-field--appended {
    background-color: #fff;
  }
}

.office-select {
  :deep(.ma-2) {
    margin: 4px 0px !important;
  }
}

:deep(.custom-required) {
  .item-label::after {
    content: '必須';
    color: #ed6809;
    font-size: 12px;
    padding-left: 4px;
  }
}
:deep(.containerContent .pl-4) {
  padding-left: 0px !important;
}

:deep(.shoku-label) {
  min-width: 112px;
}

.action-sticky {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
  padding-left: 8px;
  padding-bottom: 8px;
  margin-bottom: 8px;
}
.action-end {
  ::v-deep(hr:first-of-type) {
    display: none;
  }
}
.action-content {
  background: rgb(var(--v-theme-background));
  height: max-content;
  z-index: 888;
  padding-bottom: 104px;
}
</style>
