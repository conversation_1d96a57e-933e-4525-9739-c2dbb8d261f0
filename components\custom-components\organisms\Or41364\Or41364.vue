<script setup lang="ts">
/**
 * Or41364:有機体:（職員管理）職員編集ダイアログ
 *
 * @description
 * 職員管理画面で表示する職員編集ダイアログです。
 */

import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or41364Logic } from './Or41364.logic'
import type { Or41364StateType } from './Or41364.type'

import { Or41364Const } from './Or41364.constants'
import type { Or41364SaveInEntity } from './Or41364SaveEntity'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00612OnewayType } from '~/types/business/components/Mo00612Type'
import { useCommonProps } from '~/composables/useCommonProps'

import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'

import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import type { Mo00045Type, Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useNuxtApp } from '#app'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Mo00606OnewayType } from '~/types/business/components/Mo00606Type'

const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())

/**************************************************
 * 変数定義
 **************************************************/

const or00248 = ref({ uniqueCpId: '' })

// サブセクション
const mo01299Oneway = {
  anchorPoint: 'ss-1',
  title: t('label.password'),
}

/** ダイアログ 双方向バインド */
const mo00024 = ref<Mo00024Type>({
  isOpen: false,
})

/** ダイアログ 単方向バインド */
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '600px',
  persistent: true,
  showCloseBtn: true,
  disabledCloseBtnEvent: true, // バツボタン押下でダイアログを閉じない（emitTypeを監視）
  mo01344Oneway: {
    name: 'Or41364DialogCard',
    toolbarColor: 'white',
    toolbarTitle: t('label.staff-edit'),
    toolbarName: 'screenImportToolBar',
    showCardActions: true,
    toolbarTitleCenteredFlg: false, // ダイアログのタイトルを左寄せ
  },
})

// 新しいパスワード テキストフィールド
const mo00045NewPassword = ref<Mo00045Type>({
  value: '',
})
const mo00045OnewayNewPassword = ref<Mo00045OnewayType>({
  name: 'NewPassword',
  itemLabel: t('label.new-password'),
  hideDetails: true,
  showItemLabel: true,
  width: '200px',
  maxLength: '50',
  type: 'password',
})

// 新しいパスワードの再入力 テキストフィールド
const mo00045ReEnterNewPassword = ref<Mo00045Type>({
  value: '',
})
const mo00045OnewayReEnterNewPassword = ref<Mo00045OnewayType>({
  name: 'ReEnterNewPassword',
  itemLabel: t('label.re-enter-new-password'),
  hideDetails: true,
  showItemLabel: true,
  width: '200px',
  maxLength: '50',
  type: 'password',
})

// 保存ボタン（優先度1ボタン） 単方向バインド
const mo00609Oneway = ref<Mo00609OnewayType>({
  btnLabel: t('btn.save'),
  width: '100px',
  disabled: false,
})

/** キャンセルボタン */
const mo00612Oneway = ref<Mo00612OnewayType>({
  btnLabel: t('btn.cancel'),
  width: '100px',
})

// 新しいパスワードのメッセージ表示フラグ
const dispNewPasswordMessageFlg = ref(false)
// 新しいパスワードの再入力のメッセージ表示フラグ
const dispReEnterNewPasswordMessageFlg = ref(false)

/** ダイアログ 編集内容の確認 双方向バインド */
const mo00024ConfirmationEdits = ref<Mo00024Type>({
  isOpen: false,
})

/** ダイアログ 編集内容の確認 単方向バインド */
const mo00024OnewayConfirmationEdits = ref<Mo00024OnewayType>({
  width: '320px',
  persistent: true,
  showCloseBtn: false, // バツボタンは非表示
  mo01344Oneway: {
    name: 'ConfirmationEditsDialog',
    toolbarColor: 'white',
    toolbarTitle: t('label.confirmation-of-edits'),
    toolbarName: 'ConfirmationEditsDialogToolBar',
    showCardActions: true,
    toolbarTitleCenteredFlg: false, // ダイアログのタイトルを左寄せ
  },
})

/** 編集を続けるボタン */
const mo00612OnewayContinueEditing = ref<Mo00612OnewayType>({
  btnLabel: t('btn.continue-editing'),
  width: '120px',
})

/** 破棄ボタン（優先度1破壊ボタン） */
const mo00606OnewayDiscard = ref<Mo00606OnewayType>({
  btnLabel: t('btn.discard'),
  width: '100px',
})

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or41364StateType>({
  cpId: Or41364Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {},
})

/************************************************
 * コンポーネント固有処理
 ************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(1)]: or00248.value,
})

/**
 * ダイアログオープンイベントを監視
 *
 * @description
 * 自身のOneway領域のisOpenの値を監視し、
 * trueに設定された場合はダイアログを表示する
 */
watch(
  () => Or41364Logic.state.get(props.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (typeof newValue?.isOpen === 'boolean') {
      if (newValue.isOpen === true) {
        // 各値を初期化
        mo00045NewPassword.value.value = ''
        mo00045ReEnterNewPassword.value.value = ''
        dispNewPasswordMessageFlg.value = false
        dispReEnterNewPasswordMessageFlg.value = false

        // ダイアログを表示
        mo00024.value.isOpen = true
      }
    }
  }
)

/**
 * ボタンクリック時の処理
 *
 * @param clickType - クリックしたボタンの種類
 */
function clickBtnAction(clickType: string) {
  switch (clickType) {
    case 'clickSave': {
      // 保存ボタン押下時

      // 保存ボタンを非活性
      mo00609Oneway.value.disabled = true

      // 入力チェック
      if (inputCheck() === false) {
        mo00609Oneway.value.disabled = false // 保存ボタンを活性化
        return // 入力チェックエラーで終了
      }

      // 自身のPinia領域からログインユーザーIDを取得
      const workLoginUserId = Or41364Logic.state.get(props.uniqueCpId)?.loginUserId

      // ログインユーザーIDが取得できなければ終了
      if (workLoginUserId === undefined) {
        mo00609Oneway.value.disabled = false // 保存ボタンを活性化
        return
      }

      // リクエストを送信
      const payload: Or41364SaveInEntity = {
        parameters: {
          loginUserId: workLoginUserId, // ログインユーザーID
          newLoginUserPwd: mo00045NewPassword.value.value, // 新しいパスワード
        },
      }
      // APIへリクエスト
      ScreenRepository.update(
        'staff_list', // dataName
        payload // ペイロード
      )
        .then((result) => {
          $log.debug('保存実行結果:statusCode→' + result.statusCode)
        })
        .catch((error) => {
          $log.debug('error→' + error)
        })

      // 保存ボタンを活性化
      mo00609Oneway.value.disabled = false
      // ダイアログを閉じる処理
      closeDialog()

      break
    }

    case 'clickCancel': {
      // キャンセルボタン押下時

      execCancel()
      break
    }
  }
}

/**
 * ダイアログを閉じる処理
 */
function closeDialog() {
  const workMyStateItem = Or41364Logic.state.get(props.uniqueCpId)

  if (workMyStateItem?.isOpen === undefined || workMyStateItem.isOpen === true) {
    // 自身のOneway領域を初期化
    Or41364Logic.state.set({
      uniqueCpId: props.uniqueCpId,
      state: { isOpen: false, loginUserId: '' },
    })

    // ダイアログを非表示
    mo00024.value.isOpen = false
  }

  // 編集内容の確認ダイアログを閉じる
  mo00024ConfirmationEdits.value.isOpen = false
}

/**
 * 入力チェックを実施します。
 * return - true:チェックOK false:チェックNG
 */
function inputCheck() {
  // 各メッセージを初期化
  dispNewPasswordMessageFlg.value = false
  dispReEnterNewPasswordMessageFlg.value = false

  // 新しいパスワードの入力チェック
  if (checkPasswordValid(mo00045NewPassword.value.value) === false) {
    // チェックNGの場合
    // 新しいパスワードのメッセージを表示
    dispNewPasswordMessageFlg.value = true
    return false
  }

  // 新しいパスワードのと再入力のチェック
  if (mo00045NewPassword.value.value !== mo00045ReEnterNewPassword.value.value) {
    // チェックNGの場合
    // 新しいパスワードの再入力のメッセージを表示
    dispReEnterNewPasswordMessageFlg.value = true
    return false
  }

  return true
}

/**
 * パスワードのバリデーションチェック
 *
 * @param inputPass - パスワードの入力値
 * return - true:チェックOK false:チェックNG
 */
function checkPasswordValid(inputPass: string): boolean {
  // 正規表現(半角英数字、-、_)
  const regex = /^[a-zA-Z0-9\-_]+$/

  // 正規表現とパスワードの入力値を照合
  if (regex.test(inputPass) === true) {
    if (inputPass.length >= 4 && inputPass.length <= 20) {
      return true
    }
  }

  return false
}

/**
 * 編集を続けるボタンをクリック
 */
function clickContinueEditing() {
  // 編集内容の確認ダイアログを閉じる
  mo00024ConfirmationEdits.value.isOpen = false
}

/**
 * 破棄ボタンをクリック
 */
function clickDiscard() {
  // ダイアログを閉じる
  closeDialog()
}

/**
 * キャンセルボタンをクリック時の処理
 */
function execCancel() {
  // 入力がある場合、編集内容の確認ダイアログを表示
  if (mo00045NewPassword.value.value !== '' || mo00045ReEnterNewPassword.value.value !== '') {
    mo00024ConfirmationEdits.value.isOpen = true
  } else {
    // 入力が無い場合、ダイアログをクローズ
    closeDialog()
  }
}

// ダイアログのバツボタンクリックを監視
watch(
  () => mo00024.value.emitType,
  (newValue) => {
    // バツボタン押下時
    if (newValue === 'closeBtnClick') {
      // キャンセルボタン押下時と同じ動作を実行
      execCancel()

      // emitTypeを初期化
      mo00024.value.emitType = 'blank'
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet class="sheet-style">
        <div class="pa-0 pt-2">
          <!-- サブセクション -->
          <base-mo01299 :oneway-model-value="mo01299Oneway">
            <template #content>
              <div class="pl-4 pt-4 pb-0">
                <!-- 新しいパスワード テキストフィールド -->
                <base-mo-00045
                  v-model="mo00045NewPassword"
                  :oneway-model-value="mo00045OnewayNewPassword"
                />
                <div
                  v-if="dispNewPasswordMessageFlg"
                  class="pt-2 pb-2"
                  style="width: 250px; color: rgb(var(--v-theme-error))"
                >
                  {{ t('message.enter-a-password-of-4-to-20-characters') }}
                </div>

                <!-- 新しいパスワードの再入力 テキストフィールド -->
                <base-mo-00045
                  v-model="mo00045ReEnterNewPassword"
                  :oneway-model-value="mo00045OnewayReEnterNewPassword"
                />

                <div
                  v-if="dispReEnterNewPasswordMessageFlg"
                  class="pt-2 pb-2"
                  style="width: 250px; color: rgb(var(--v-theme-error))"
                >
                  {{ t('message.passwords-do-not-match') }}
                </div>

                <div
                  class="pt-2 pb-2"
                  style="width: 250px"
                >
                  {{ t('message.enter-a-password-of-4-to-20-characters') }}
                </div>
              </div>
            </template>
          </base-mo01299>
        </div>
      </c-v-sheet>
    </template>
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />

        <c-v-col
          cols="auto"
          class="pr-2"
        >
          <!-- キャンセルボタン -->
          <base-mo00612
            :oneway-model-value="mo00612Oneway"
            @click="clickBtnAction('clickCancel')"
          />
        </c-v-col>
        <c-v-col
          cols="auto"
          class="pr-4"
        >
          <!-- 保存ボタン 優先度1ボタン -->
          <base-mo00609
            :oneway-model-value="mo00609Oneway"
            @click="clickBtnAction('clickSave')"
          />
        </c-v-col>
      </c-v-row>
    </template>
  </base-mo00024>

  <!-- 編集内容の確認 ダイアログ START -->
  <base-mo00024
    v-model="mo00024ConfirmationEdits"
    :oneway-model-value="mo00024OnewayConfirmationEdits"
  >
    <template #cardItem>
      <c-v-sheet class="dialog-sheet-style">
        <p>{{ t('message.back-confirm') }}</p>
      </c-v-sheet>
    </template>
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <c-v-col cols="auto">
          <!-- 編集を続けるボタン -->
          <base-mo00612
            :oneway-model-value="mo00612OnewayContinueEditing"
            @click="clickContinueEditing()"
          />
        </c-v-col>
        <c-v-col
          cols="auto"
          class="pl-2 pr-2"
        >
          <!-- 破棄ボタン -->
          <base-mo00606
            :oneway-model-value="mo00606OnewayDiscard"
            @click="clickDiscard()"
          />
        </c-v-col>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- 編集内容の確認 ダイアログ END -->
</template>

<style scoped lang="scss">
.sheet-style {
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 15px;
  padding-right: 15px;
}

/* ▼テーブルの色と枠線の設定 */
.table-header {
  background-color: rgb(var(--v-theme-black-100)) !important;

  color: rgb(var(--v-theme-black-900)) !important;
}

.table-wrapper :deep(.v-table__wrapper) {
  overflow-x: scroll; /* 横スクロールを有効化 */
}
.table-wrapper table {
  border-collapse: collapse;
}
.table-wrapper :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100));
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.table-wrapper :deep(.v-table__wrapper td) {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
}
/* ▲テーブルの色と枠線の設定 */
</style>
