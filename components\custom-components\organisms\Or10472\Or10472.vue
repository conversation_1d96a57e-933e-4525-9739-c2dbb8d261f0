<script setup lang="ts">
/**
 * Or10472:家族マスタ)ダイアログ
 * GUI00829_［家族マスタ］画面
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */
import { onMounted, reactive, ref, watch, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or50242Const } from '../Or50242/Or50242.constants'
import { Or25627Const } from '../Or25627/Or25627.constants'
import { Or10472Const } from './Or10472.constants'
import type { AsyncFunction, Or10472StateType } from './Or10472.type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or10472OnewayType } from '~/types/cmn/business/components/Or10472Type'
import type {
  FamilyMasterSelectInEntity,
  FamilyMasterSelectOutEntity,
} from '~/repositories/cmn/entities/FamilyMasterSelectEntity'
import { useScreenOneWayBind, useSetupChildProps, useScreenStore } from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { Or25627Type } from '~/types/cmn/business/components/Or25627Type'
import type {
  FamilyMasterUpdateInEntity,
} from '~/repositories/cmn/entities/FamilyMasterUpdateEntity'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10472OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or10472OnewayType = {
  viewAuthority: 'F',
  saveAuthority: 'F',
  dispTabFlg: 'family',
  sysRyaku: 'SYS',
}

//(予定マスタ) 行のアクションボタン
const or50242 = ref({ uniqueCpId: Or50242Const.CP_ID(0) })
// (予定マスタ)アセスメント（包括）マスタリスト
const or25627 = ref({ uniqueCpId: Or25627Const.CP_ID(0) })
//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
const localOneway = reactive({
  or10895: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
})

const local = reactive({
  mo00043: { id: Or10472Const.TAB.TAB_ID_FAMILY },
  or25627: {
    editFlg: false,
    delBtnDisabled: false,
    stringInputAssistList: [],
    saveResultStringInputAssistList: [],
  } as Or25627Type,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10472Const.DEFAULT.IS_OPEN,
})
const or25627Ref = ref<{
  tableValidation(): AsyncFunction
  createRow(): AsyncFunction
  copyRow(): AsyncFunction
  deleteRow(): AsyncFunction
  init(): AsyncFunction
}>()

// Or50242 Ref
const or50242Ref = ref<{ delBtnDisable(disabled: boolean): AsyncFunction }>()

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or10472StateType>({
  cpId: Or10472Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10472Const.DEFAULT.IS_OPEN
    },
  },
})
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or50242Const.CP_ID(0)]: or50242.value,
  [Or25627Const.CP_ID(0)]: or25627.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
})

onMounted(async () => {
  await init()
})

/**
 * 予定マスタ初期処理
 */
async function init() {
  // 区分フラグ
  let kbnFlg: string
  //引継情報. sys略称 が "SYS" の場合、１
  if (localOneway.or10895.sysRyaku === Or10472Const.SYS) {
    kbnFlg = '1'
  } else {
    // 以外の場合、3
    kbnFlg = '3'
  }
  // 予定マスタ情報取得(IN)
  const inputData: FamilyMasterSelectInEntity = {
    kbnFlg: kbnFlg,
    cf1Kbn: '3',
  }
  // 予定マスタ初期情報取得
  const ret: FamilyMasterSelectOutEntity = await ScreenRepository.select(
    'familyMasterSelect',
    inputData
  )
  local.or25627.stringInputAssistList = []
  ret.data.stringInputAssistList.map((item) => {
    return local.or25627.stringInputAssistList.push({
      cf1Id: item.cf1Id,
      cf1Kbn: item.cf1Kbn,
      kbnCd: item.kbnCd,
      textKnj: item.textKnj,
      changeF: item.changeF,
      kbnFlg: item.kbnFlg,
      updateKbn: UPDATE_KBN.NONE,
    })
  })
  // 親画面.表示タブフラグが「予定」の場合
  if (localOneway.or10895.dispTabFlg === Or10472Const.TAB.TAB_ID_FAMILY) {
    // 予定タブを選択する
    local.mo00043.id = Or10472Const.TAB.TAB_ID_FAMILY
  }
  or25627Ref.value?.init()
}

/**
 * 保存
 */
async function save() {
  await nextTick()
  // 画面入力データ変更があるかどうかを判定する
  if (isEdit.value) {
    // 変更がある場合、処理継続
    const valid = or25627Ref.value?.tableValidation()
    // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
    const dataArray = local.or25627.stringInputAssistList.filter(
      (data) => data.updateKbn !== UPDATE_KBN.DELETE
    )
    //予定一覧.区分番号、予定一覧.内容の何れかがブランクの場合
    const blankArray = dataArray.filter((data) => data.kbnCd === '' || data.textKnj === '')
    if (blankArray.length > 0) {
      showOr21813Msg(t('message.e-cmn-41708'))
      return false
    }
    //予定マスタ一覧に、重複した区分番号が存在する場合
    const nameSet = new Set<string>()
    for (const item of dataArray) {
      if (nameSet.has(item.kbnCd)) {
        showOr21813Msg(t('message.e-cmn-41713', [item.kbnCd]))
        return false
      }
      nameSet.add(item.kbnCd)
    }
    if (!valid) {
      return false
    }
    // 区分フラグ
    let kbnFlg: string
    //引継情報. sys略称 が "SYS" の場合、１
    if (localOneway.or10895.sysRyaku === Or10472Const.SYS) {
      kbnFlg = '1'
    } else {
      // 以外の場合、3
      kbnFlg = '3'
    }
    const param: FamilyMasterUpdateInEntity = {
      kbnFlg: kbnFlg,
      stringInputAssistList: local.or25627.stringInputAssistList
        .filter((item) => item.updateKbn !== UPDATE_KBN.NONE)
        .map((item) => {
          return {
            ...item,
            kbnFlg: item.kbnFlg || kbnFlg,
            cf1Kbn: item.cf1Kbn || '1',
          }
        }),
    }
    // 予定マスタ情報保存
    await ScreenRepository.update('familyMasterUpdate', param)
    await init()
    return true
  }
}

/**
 * 保存ボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21813Msg(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 行追加ボタン
 */
function onAddItem() {
  or25627Ref.value?.createRow()
}

/**
 * 行複写ボタン
 */
function onCloneItem() {
  or25627Ref.value?.copyRow()
}

/**
 * 行削除ボタン
 */
function onDelete() {
  or25627Ref.value?.deleteRow()
}

/**
 * 行削除ボタン活性状態を監視
 *
 * @description
 * 活性状態を監視
 */
watch(
  () => local.or25627.delBtnDisabled,
  (newValue) => {
    or50242Ref.value?.delBtnDisable(!newValue)
  }
)
defineExpose({
  save,
})
</script>

<template>
  <div class="mt-2">
    <g-custom-or-50242
      ref="or50242Ref"
      v-bind="or50242"
      @on-add-item="onAddItem"
      @on-clone-item="onCloneItem"
      @on-delete="onDelete"
    />
    <!-- (予定マスタ)アセスメント（包括）マスタリスト -->
    <g-custom-or-25627
      ref="or25627Ref"
      v-bind="or25627"
      v-model="local.or25627"
      :parent-unique-cp-id="props.uniqueCpId"
    />
  </div>

  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
.v-row {
  margin: -8px;
}
</style>
