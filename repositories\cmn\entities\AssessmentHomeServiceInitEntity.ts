import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
import type { IReportInEntity } from '~/types/business/core/ReportRepositoryType'
/**
 * ［アセスメント］画面（居宅）（3）
 *
 * @description
 * ［アセスメント］画面（居宅）（3）画面API用エンティティ
 *
 * <AUTHOR>
 */
/**
 * ［アセスメント］画面（居宅）（3）取得入力エンティティ
 */
export interface AssessmentHomeServiceInitSelectInEntity extends InWebEntity {
  /** 計画期間ID */
  sc1Id?: string
  /** アセスメントID */
  gdlId?: string
  /** タブID */
  tabId?: string
  /** 期間処理区分 */
  kknSyoriKbn?: string
  /** 履歴処理区分 */
  rirekiSyoriKbn?: string
  /** 履歴作成日 */
  createYmd?: string
  /** 利用者ID */
  userId?: string
  /** 調査票改訂フラグ */
  ssmNinteiFlg?: string
  /** ロケーション */
  location?: string
  /** 事業所ID */
  svJigyoId?: string
  /** 事業者グループ適用ID */
  svJigyoGrpId?: string
  /** 改訂フラグ */
  ninteiFormF?: string
}

/**
 * ［アセスメント］画面（居宅）（3）取得出力エンティティ
 */
export interface AssessmentHomeServiceInitSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** サービス利用状況情報(H21/4改訂) */
    cpnTucGdl4SerInfo: ServiceUseSituationH21Info
    /** サービス利用状況情報(R3/4改訂) */
    cpnTucGdl5SerInfo: ServiceUseSituationR34Info
    /** 施設情報リスト */
    shisetsuDataList: ShisetsuData[]
    /** 課題と目標リスト */
    issuesAndGoalList: IssuesAndGoalItems[]
  }
}

/**
 * 施設情報
 */
export interface ShisetsuData {
  /** 施設ID */
  shisetuId: string
  /** 施設名 */
  shisetuKnj: string
  /** 調査票施設種別 */
  scShisetsuShu: string
  /** 電話番号 */
  tel: string
  /** 住所 */
  addressKnj: string
  /** 表示順 */
  sort: string
  /** 郵便番号 */
  zip: string
}

/**
 * サービス利用状況（Ｈ２１改訂）情報
 */
export interface ServiceUseSituationH21Info {
  /** アセスメントID */
  gdlId: string
  /** 計画期間ID */
  sc1Id: string
  /** 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ) */
  ser1Cd: string
  /** (介護予防)訪問入浴介護 */
  ser2Cd: string
  /** (介護予防)訪問看護 */
  ser3Cd: string
  /** (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝ */
  ser4Cd: string
  /** (介護予防)居宅療養管理指導 */
  ser5Cd: string
  /** (介護予防)通所介護（ﾃﾞｲｻｰﾋﾞｽ） */
  ser6Cd: string
  /** (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ） */
  ser7Cd: string
  /** 市町村特別給付 */
  ser8Cd: string
  /** (介護予防)福祉用具貸与 */
  ser9Cd: string
  /** (介護予防)短期入所生活介護(特養等） */
  ser10Cd: string
  /** (介護予防)短期入所生活介護(老健・診療所） */
  ser11Cd: string
  /** 認知症対応型共同生活介護 */
  ser12Cd: string
  /** (介護予防)特定施設入所生活介護 */
  ser13Cd: string
  /** 特定(介護予防)福祉用具販売 */
  ser14Cd: string
  /** 住宅改修 */
  ser15Cd: string
  /** 在宅サービス利用16 */
  ser16Cd: string
  /** 生活支援員の訪問 */
  ser17Cd: string
  /** ふれあい・いきいきサロン */
  ser18Cd: string
  /** 配食サービス */
  ser19Cd: string
  /** 洗濯サービス */
  ser20Cd: string
  /** 移動または外出支援 */
  ser21Cd: string
  /** 友愛訪問 */
  ser22Cd: string
  /** 老人福祉センター */
  ser23Cd: string
  /** 老人憩いの家 */
  ser24Cd: string
  /** ガイドヘルパー */
  ser25Cd: string
  /** 身障／補助具・日常生活用具 */
  ser26Cd: string
  /** 追加サービス1 */
  ser27Cd: string
  /** 追加サービス2 */
  ser28Cd: string
  /** 追加サービス3 */
  ser29Cd: string
  /** 追加サービス4 */
  ser30Cd: string
  /** サービス名 */
  ser16NameKnj: string
  /** 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)の利用回数 */
  kaisu1: string
  /** (介護予防)訪問入浴介護の利用回数 */
  kaisu2: string
  /** (介護予防)訪問看護の利用回数 */
  kaisu3: string
  /** (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝの利用回数 */
  kaisu4: string
  /** (介護予防)居宅療養管理指導の利用回数 */
  kaisu5: string
  /** 通所介護（ﾃﾞｲｻｰﾋﾞｽ）の利用回数 */
  kaisu6: string
  /** (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）の利用回数 */
  kaisu7: string
  /** (介護予防)福祉用具貸与の利用回数 */
  kaisu8: string
  /** (介護予防)短期入所生活介護(特養等）の利用回数 */
  kaisu9: string
  /** (介護予防)短期入所生活介護(老健・診療所）の利用回数 */
  kaisu10: string
  /** 認知症対応型共同生活介護（回数） */
  kaisu11: string
  /** (介護予防)特定施設入所生活介護の利用回数 */
  kaisu12: string
  /** 特定(介護予防)福祉用具販売の利用回数 */
  kaisu13: string
  /** 利用回数14 */
  kaisu14: string
  /** 住宅改修の利用回数 */
  kaisu15: string
  /** 生活支援員の訪問の利用回数 */
  kaisu16: string
  /** ふれあい・いきいきサロンの利用回数 */
  kaisu17: string
  /** 配食サービスの利用回数 */
  kaisu18: string
  /** 洗濯サービスの利用回数 */
  kaisu19: string
  /** 移動または外出支援の利用回数 */
  kaisu20: string
  /** 友愛訪問の利用回数 */
  kaisu21: string
  /** 老人福祉センターの利用回数 */
  kaisu22: string
  /** 老人憩いの家の利用回数 */
  kaisu23: string
  /** ガイドヘルパーの利用回数 */
  kaisu24: string
  /** 追加サービス1の利用回数 */
  kaisu25: string
  /** 追加サービス2の利用回数 */
  kaisu26: string
  /** 利用回数27 */
  kaisu27: string
  /** 追加サービス3の利用回数 */
  kaisu28: string
  /** 追加サービス4の利用回数 */
  kaisu29: string
  /** 利用回数30 */
  kaisu30: string
  /** 市町村特別給付メモ */
  tokKyuKnj: string
  /** 追加サービス1メモ */
  addSer1Knj: string
  /** 追加サービス2メモ */
  addSer2Knj: string
  /** 追加サービス3メモ */
  addSer3Knj: string
  /** 追加サービス4メモ */
  addSer4Knj: string
  /** 身障／補助具・日常生活用具メモ */
  youguKnj: string
  /** 施設名 */
  shisetsuNameKnj: string
  /** 電話番号 */
  shisetsuTel: string
  /** 住所（以前備考） */
  shisetsuMemoKnj: string
  /** 利用施設種別 */
  shisetsuShu: string
  /** 老齢関係 */
  seido1: string
  /** 障害関係 */
  seido2: string
  /** 遺族・寡婦 */
  seido3: string
  /** 恩給 */
  seido4: string
  /** 特別障害者手当 */
  seido5: string
  /** 生活保護 */
  seido6: string
  /** 生活福祉資金貸付 */
  seido7: string
  /** 高齢者住宅整備資金貸付 */
  seido8: string
  /** 日常生活自立支援事業 */
  seido9: string
  /** 成年後見人制度 */
  seido10: string
  /** 国保 */
  seido11: string
  /** 組合保健 */
  seido12: string
  /** 国公共済 */
  seido13: string
  /** 私立学校共済 */
  seido14: string
  /** 協会けんぼ（旧・政管健保） */
  seido15: string
  /** 日雇い */
  seido16: string
  /** 地方共済 */
  seido17: string
  /** 船員 */
  seido18: string
  /** 勤労災害保険 */
  seido19: string
  /** 健康手帳の交付 */
  seido20: string
  /** 制度利用21 */
  seido21: string
  /** 制度利用22 */
  seido22: string
  /** 健康診査 */
  seido23: string
  /** 制度利用24 */
  seido24: string
  /** 制度利用25 */
  seido25: string
  /** 制度利用26 */
  seido26: string
  /** 制度利用27 */
  seido27: string
  /** 制度利用28 */
  seido28: string
  /** 制度利用29 */
  seido29: string
  /** 追加制度1 */
  addSeido1Knj: string
  /** 追加制度2 */
  addSeido2Knj: string
  /** 追加制度3 */
  addSeido3Knj: string
  /** 追加制度4 */
  addSeido4Knj: string
  /** 追加制度5 */
  addSeido5Knj: string
  /** 老齢関係メモ */
  riyoMemo1Knj: string
  /** 障害関係メモ */
  riyoMemo2Knj: string
  /** 遺族・寡婦メモ */
  riyoMemo3Knj: string
  /** 労災保険メモ */
  riyoMemo4Knj: string
  /** 成年後見人等 */
  kokenKnj: string
  /** 成年後見人制度区分 */
  seinenKoukenKbn: string
  /** 夜間対応訪問介護 */
  ser31Cd: string
  /** 認知症対応型通所介護 */
  ser32Cd: string
  /** 小規模多機能居宅介護 */
  ser33Cd: string
  /** 地域密着型特定施設入居者生活介護 */
  ser34Cd: string
  /** 地域密着型介護老人福祉施設入居者生活介護 */
  ser35Cd: string
  /** 夜間対応訪問介護（回数） */
  kaisu31: string
  /** 認知症対応型通所介護（回数） */
  kaisu32: string
  /** 小規模多機能居宅介護（回数） */
  kaisu33: string
  /** 地域密着型特定施設入居者生活介護（回数） */
  kaisu34: string
  /** 地域密着型介護老人福祉施設入居者生活介護（回数） */
  kaisu35: string
  /** 郵便番号 */
  shisetsuZip: string
  /** サービス利用状況記載日 */
  serYmd: string
  /** dmySerYmd */
  dmySerYmd: string
  /** 複合型サービス */
  ser36Cd: string
  /** 定期巡回・随時対応型訪問介護看護 */
  ser37Cd: string
  /** 複合型サービス（回数） */
  kaisu36: string
  /** 定期巡回・随時対応型訪問介護看護（回数） */
  kaisu37: string
  /** 制度利用30（後期高齢者医療） */
  seido30: string
  /** 制度利用31（その他1） */
  seido31: string
  /** 制度利用32（その他2） */
  seido32: string
  /** 制度利用33（その他3） */
  seido33: string
  /** 制度利用ﾒﾓ5（その他1（メモ）） */
  riyoMemo5Knj: string
  /** 制度利用ﾒﾓ6（その他2（メモ）） */
  riyoMemo6Knj: string
  /** 制度利用ﾒﾓ7（その他3（メモ）） */
  riyoMemo7Knj: string
}

/**
 * サービス利用状況（R3/4改訂）情報
 */
export interface ServiceUseSituationR34Info {
  /** 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ) */
  ser1Cd: string
  /** (介護予防)訪問入浴介護 */
  ser2Cd: string
  /** (介護予防)訪問看護 */
  ser3Cd: string
  /** (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝ */
  ser4Cd: string
  /** (介護予防)居宅療養管理指導 */
  ser5Cd: string
  /** 通所介護（ﾃﾞｲｻｰﾋﾞｽ） */
  ser6Cd: string
  /** (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ） */
  ser7Cd: string
  /** 市町村特別給付 */
  ser8Cd: string
  /** (介護予防)福祉用具貸与 */
  ser9Cd: string
  /** (介護予防)短期入所生活介護(特養等） */
  ser10Cd: string
  /** (介護予防)短期入所生活介護(老健・診療所） */
  ser11Cd: string
  /** 認知症対応型共同生活介護 */
  ser12Cd: string
  /** (介護予防)特定施設入所生活介護 */
  ser13Cd: string
  /** 特定(介護予防)福祉用具販売 */
  ser14Cd: string
  /** 住宅改修 */
  ser15Cd: string
  /** 在宅サービス利用16 */
  ser16Cd: string
  /** 生活支援員の訪問 */
  ser17Cd: string
  /** ふれあい・いきいきサロン */
  ser18Cd: string
  /** 配食サービス */
  ser19Cd: string
  /** 洗濯サービス */
  ser20Cd: string
  /** 移動または外出支援 */
  ser21Cd: string
  /** 友愛訪問 */
  ser22Cd: string
  /** 老人福祉センター */
  ser23Cd: string
  /** 老人憩いの家 */
  ser24Cd: string
  /** ガイドヘルパー */
  ser25Cd: string
  /** 身障／補助具・日常生活用具 */
  ser26Cd: string
  /** 追加サービス1 */
  ser27Cd: string
  /** 追加サービス2 */
  ser28Cd: string
  /** 追加サービス3 */
  ser29Cd: string
  /** 追加サービス4 */
  ser30Cd: string
  /** 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)の利用回数 */
  kaisu1: string
  /** (介護予防)訪問入浴介護の利用回数 */
  kaisu2: string
  /** (介護予防)訪問看護の利用回数 */
  kaisu3: string
  /** (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝの利用回数 */
  kaisu4: string
  /** (介護予防)居宅療養管理指導の利用回数 */
  kaisu5: string
  /** 通所介護（ﾃﾞｲｻｰﾋﾞｽ）の利用回数 */
  kaisu6: string
  /** (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）の利用回数 */
  kaisu7: string
  /** (介護予防)福祉用具貸与の利用回数 */
  kaisu8: string
  /** (介護予防)短期入所生活介護(特養等）の利用回数 */
  kaisu9: string
  /** (介護予防)短期入所生活介護(老健・診療所）の利用回数 */
  kaisu10: string
  /** 認知症対応型共同生活介護（回数） */
  kaisu11: string
  /** (介護予防)特定施設入所生活介護の利用回数 */
  kaisu12: string
  /** 利用回数14 */
  kaisu14: string
  /** 住宅改修の利用回数 */
  kaisu15: string
  /** 生活支援員の訪問の利用回数 */
  kaisu16: string
  /** ふれあい・いきいきサロンの利用回数 */
  kaisu17: string
  /** 配食サービスの利用回数 */
  kaisu18: string
  /** 洗濯サービスの利用回数 */
  kaisu19: string
  /** 移動または外出支援の利用回数 */
  kaisu20: string
  /** 友愛訪問の利用回数 */
  kaisu21: string
  /** 老人福祉センターの利用回数 */
  kaisu22: string
  /** 老人憩いの家の利用回数 */
  kaisu23: string
  /** ガイドヘルパーの利用回数 */
  kaisu24: string
  /** 追加サービス1の利用回数 */
  kaisu25: string
  /** 追加サービス2の利用回数 */
  kaisu26: string
  /** 利用回数27 */
  kaisu27: string
  /** 追加サービス3の利用回数 */
  kaisu28: string
  /** 追加サービス4の利用回数 */
  kaisu29: string
  /** 利用回数30 */
  kaisu30: string
  /** 市町村特別給付メモ */
  tokKyuKnj: string
  /** 追加サービス1メモ */
  addSer1Knj: string
  /** 追加サービス2メモ */
  addSer2Knj: string
  /** 追加サービス3 */
  addSer3Knj: string
  /** 追加サービス4 */
  addSer4Knj: string
  /** 身障／補助具・日常生活用具メモ */
  youguKnj: string
  /** 施設名 */
  shisetsuNameKnj: string
  /** 電話番号 */
  shisetsuTel: string
  /** 利用施設種別 */
  shisetsuShu: string
  /** 老齢関係 */
  seido1: string
  /** 障害関係 */
  seido2: string
  /** 遺族・寡婦 */
  seido3: string
  /** 恩給 */
  seido4: string
  /** 特別障害者手当 */
  seido5: string
  /** 生活保護 */
  seido6: string
  /** 生活福祉資金貸付 */
  seido7: string
  /** 高齢者住宅整備資金貸付 */
  seido8: string
  /** 日常生活自立支援事業 */
  seido9: string
  /** 成年後見人制度 */
  seido10: string
  /** 国保 */
  seido11: string
  /** 組合保健 */
  seido12: string
  /** 国公共済 */
  seido13: string
  /** 私立学校共済 */
  seido14: string
  /** 協会けんぼ（旧・政管健保） */
  seido15: string
  /** 日雇い */
  seido16: string
  /** 地方共済 */
  seido17: string
  /** 船員 */
  seido18: string
  /** 労災保険 */
  seido19: string
  /** 制度利用20 */
  seido20: string
  /** 制度利用21 */
  seido21: string
  /** 制度利用22 */
  seido22: string
  /** 制度利用23 */
  seido23: string
  /** 制度利用24 */
  seido24: string
  /** 制度利用25 */
  seido25: string
  /** 制度利用26 */
  seido26: string
  /** 制度利用27 */
  seido27: string
  /** 制度利用28 */
  seido28: string
  /** 制度利用29 */
  seido29: string
  /** 追加制度1 */
  addSeido1Knj: string
  /** 追加制度2 */
  addSeido2Knj: string
  /** 追加制度3 */
  addSeido3Knj: string
  /** 追加制度4 */
  addSeido4Knj: string
  /** 追加制度5 */
  addSeido5Knj: string
  /** 老齢関係メモ */
  riyoMemo1Knj: string
  /** 障害関係メモ */
  riyoMemo2Knj: string
  /** 遺族・寡婦メモ */
  riyoMemo3Knj: string
  /** 労災保険メモ */
  riyoMemo4Knj: string
  /** サービス名 */
  ser16NameKnj: string
  /** 成年後見人等 */
  kokenKnj: string
  /** 計画期間ID */
  sc1Id: string
  /** 成年後見人制度区分 */
  seinenKoukenKbn: string
  /** 特定(介護予防)福祉用具販売の利用回数 */
  kaisu13: string
  /** アセスメントID */
  gdlId: string
  /** 住所（以前備考） */
  shisetsuMemoKnj: string
  /** 夜間対応訪問介護 */
  ser31Cd: string
  /** 認知症対応型通所介護 */
  ser32Cd: string
  /** 小規模多機能居宅介護 */
  ser33Cd: string
  /** 地域密着型特定施設入居者生活介護 */
  ser34Cd: string
  /** 地域密着型介護老人福祉施設入居者生活介護 */
  ser35Cd: string
  /** 夜間対応訪問介護（回数） */
  kaisu31: string
  /** 認知症対応型通所介護（回数） */
  kaisu32: string
  /** 小規模多機能居宅介護（回数） */
  kaisu33: string
  /** 地域密着型特定施設入居者生活介護（回数） */
  kaisu34: string
  /** 地域密着型介護老人福祉施設入居者生活介護（回数） */
  kaisu35: string
  /** サービス利用状況記載日 */
  serYmd: string
  /** dmySerYmd */
  dmySerYmd: string
  /** 郵便番号 */
  shisetsuZip: string
  /** 複合型サービス */
  ser36Cd: string
  /** 定期巡回・随時対応型訪問介護看護 */
  ser37Cd: string
  /** 複合型サービス（回数） */
  kaisu36: string
  /** 定期巡回・随時対応型訪問介護看護（回数） */
  kaisu37: string
  /** 制度利用30（後期高齢者医療） */
  seido30: string
  /** 制度利用31（その他1） */
  seido31: string
  /** 制度利用32（その他2） */
  seido32: string
  /** 制度利用33（その他3） */
  seido33: string
  /** 制度利用ﾒﾓ5（その他1（メモ）） */
  riyoMemo5Knj: string
  /** 制度利用ﾒﾓ6（その他2（メモ）） */
  riyoMemo6Knj: string
  /** 制度利用ﾒﾓ7（その他3（メモ）） */
  riyoMemo7Knj: string
  /** 訪問型サービス */
  ser38Cd: string
  /** 通所型サービス */
  ser39Cd: string
  /** その他の生活支援サービス */
  ser40Cd: string
  /** その他の生活支援サービス（名称） */
  ser40NameKnj: string
  /** 訪問型サービス（回数） */
  kaisu38: string
  /** 通所型サービス（回数） */
  kaisu39: string
  /** その他の生活支援サービス（回数） */
  kaisu40: string
}

/**
 * 課題と目標情報
 */
export interface IssuesAndGoalItems {
  /** ID */
  kadaiId: string
  /** アセスメント番号 */
  assNo: string
  /** アセスメント番号名 */
  assKnj: string
  /** 課題 */
  kadaiKnj: string
  /** 長期 */
  choukiKnj: string
  /** 短期 */
  tankiKnj: string
  /** 連番 */
  seq: string
}

/** ［アセスメント］画面（居宅）（3）保存入力エンティティ */
export interface AssessmentHomeServiceUpdateInEntity extends InWebEntity {
  /** タブID */
  tabId?: string
  /** 機能ID */
  kinoId?: string
  /** 当履歴ページ番号 */
  krirekiNo?: string
  /** e文書用パラメータ */
  edocumentUseParam?: IReportInEntity
  /** e文書削除用パラメータ */
  edocumentDeleteUseParam?: IReportInEntity
  /** 期間対象フラグ */
  kikanFlg?: string
  /** 計画対象期間番号 */
  planningPeriodNo?: string
  /** 開始日 */
  startYmd?: string
  /** 終了日 */
  endYmd?: string
  /** ガイドラインまとめ */
  matomeFlg?: string
  /** ログインID */
  loginId?: string
  /** システム略称 */
  sysRyaku?: string
  /** 職員ID */
  shokuId?: string
  /** システムコード */
  sysCd?: string
  /** 事業者名 */
  svJigyoKnj?: string
  /** 作成者名 */
  createUserName?: string
  /** 利用者名 */
  userName?: string
  /** 法人ID */
  houjinId?: string
  /** 施設ID */
  shisetuId?: string
  /** 利用者ID */
  userId?: string
  /** 事業者ID */
  svJigyoId?: string
  /** 種別ID */
  syubetsuId?: string
  /** 更新区分 */
  updateKbn?: string
  /** 履歴更新区分 */
  historyUpdateKbn?: string
  /** 削除処理区分 */
  deleteKbn?: string
  /** 計画対象期間ID */
  sc1Id?: string
  /** アセスメントID */
  gdlId?: string
  /** 作成日 */
  kijunbiYmd?: string
  /** 作成者ID */
  sakuseiId?: string
  /** 改定フラグ */
  ninteiFormF?: string
  /** サービス利用状況情報 */
  serInfo: SerInfoEntity

  /** 課題と目標リスト */
  kadaiList: {
    /** id */
    id?: string
    /** アセスメント番号 */
    assNo?: string
    /** 課題 */
    kadaiKnj?: string
    /** 長期 */
    choukiKnj?: string
    /** 短期 */
    tankiKnj?: string
    /** 連番 */
    seq?: string
    /** 更新区分 */
    updateKbn?: string
  }[]
}

/**
 * アセスメントマスタ保存出力エンティティ
 */
export interface AssessmentHomeServiceUpdateOutEntity extends OutWebEntity {
  /** data */
  data: {
    /** 計画対象期間ID */
    sc1Id: string
    /** アセスメントID */
    gdlId: string
    /** エラー区分 */
    errKbn: string
  }
}

/**
 * ｻｰﾋﾞｽ利用状況情報
 */
export interface SerInfoEntity {
  /** 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ) */
  ser1Cd: string
  /** (介護予防)訪問入浴介護 */
  ser2Cd: string
  /** (介護予防)訪問看護 */
  ser3Cd: string
  /** (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝ */
  ser4Cd: string
  /** (介護予防)居宅療養管理指導 */
  ser5Cd: string
  /** (介護予防)通所介護（ﾃﾞｲｻｰﾋﾞｽ） */
  ser6Cd: string
  /** (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ） */
  ser7Cd: string
  /** 市町村特別給付 */
  ser8Cd: string
  /** (介護予防)福祉用具貸与 */
  ser9Cd: string
  /** (介護予防)短期入所生活介護(特養等） */
  ser10Cd: string
  /** (介護予防)短期入所生活介護(老健・診療所） */
  ser11Cd: string
  /** 認知症対応型共同生活介護 */
  ser12Cd: string
  /** (介護予防)特定施設入所生活介護 */
  ser13Cd: string
  /** 特定(介護予防)福祉用具販売 */
  ser14Cd: string
  /** 住宅改修 */
  ser15Cd: string
  /** 生活支援員の訪問 */
  ser17Cd: string
  /** ふれあい・いきいきサロン */
  ser18Cd: string
  /** 配食サービス */
  ser19Cd: string
  /** 洗濯サービス */
  ser20Cd: string
  /** 移動または外出支援 */
  ser21Cd: string
  /** 友愛訪問 */
  ser22Cd: string
  /** 老人福祉センター */
  ser23Cd: string
  /** 老人憩いの家 */
  ser24Cd: string
  /** ガイドヘルパー */
  ser25Cd: string
  /** 身障／補助具・日常生活用具 */
  ser26Cd: string
  /** 追加サービス1 */
  ser27Cd: string
  /** 追加サービス2 */
  ser28Cd: string
  /** 追加サービス3 */
  ser29Cd: string
  /** 追加サービス4 */
  ser30Cd: string
  /** 訪問介護（ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)の利用回数 */
  kaisu1: string
  /** (介護予防)訪問入浴介護の利用回数 */
  kaisu2: string
  /** (介護予防)訪問看護の利用回数 */
  kaisu3: string
  /** (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝの利用回数 */
  kaisu4: string
  /** (介護予防)居宅療養管理指導の利用回数 */
  kaisu5: string
  /** 通所介護（ﾃﾞｲｻｰﾋﾞｽ）の利用回数 */
  kaisu6: string
  /** (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ（ﾃﾞｲｹｱ）の利用回数 */
  kaisu7: string
  /** (介護予防)福祉用具貸与の利用回数 */
  kaisu8: string
  /** (介護予防)短期入所生活介護(特養等）の利用回数 */
  kaisu9: string
  /** (介護予防)短期入所生活介護(老健・診療所）の利用回数 */
  kaisu10: string
  /** 認知症対応型共同生活介護（回数） */
  kaisu11: string
  /** (介護予防)特定施設入所生活介護の利用回数 */
  kaisu12: string
  /** 特定(介護予防)福祉用具販売の利用回数 */
  kaisu13: string
  /** 住宅改修の利用回数 */
  kaisu15: string
  /** 生活支援員の訪問の利用回数 */
  kaisu16: string
  /** ふれあい・いきいきサロンの利用回数 */
  kaisu17: string
  /** 配食サービスの利用回数 */
  kaisu18: string
  /** 洗濯サービスの利用回数 */
  kaisu19: string
  /** 移動または外出支援の利用回数 */
  kaisu20: string
  /** 友愛訪問の利用回数 */
  kaisu21: string
  /** 老人福祉センターの利用回数 */
  kaisu22: string
  /** 老人憩いの家の利用回数 */
  kaisu23: string
  /** ガイドヘルパーの利用回数 */
  kaisu24: string
  /** 追加サービス1の利用回数 */
  kaisu25: string
  /** 追加サービス2の利用回数 */
  kaisu26: string
  /** 追加サービス3の利用回数 */
  kaisu28: string
  /** 追加サービス4の利用回数 */
  kaisu29: string
  /** 市町村特別給付メモ */
  tokKyuKnj: string
  /** 追加サービス1メモ */
  addSer1Knj: string
  /** 追加サービス2メモ */
  addSer2Knj: string
  /** 追加サービス3メモ */
  addSer3Knj: string
  /** 追加サービス4メモ */
  addSer4Knj: string
  /** 身障／補助具・日常生活用具メモ */
  youguKnj: string
  /** 施設名 */
  shisetsuNameKnj: string
  /** 郵便番号 */
  shisetsuZip: string
  /** 電話番号 */
  shisetsuTel: string
  /** 住所（以前：備考） */
  shisetsuMemoKnj: string
  /** 利用施設種別 */
  shisetsuShu: string
  /** 老齢関係 */
  seido1: string
  /** 障害関係 */
  seido2: string
  /** 遺族・寡婦 */
  seido3: string
  /** 恩給 */
  seido4: string
  /** 特別障害者手当 */
  seido5: string
  /** 生活保護 */
  seido6: string
  /** 生活福祉資金貸付 */
  seido7: string
  /** 高齢者住宅整備資金貸付 */
  seido8: string
  /** 日常生活自立支援事業 */
  seido9: string
  /** 成年後見人制度 */
  seido10: string
  /** 国保 */
  seido11: string
  /** 組合保健 */
  seido12: string
  /** 国公共済 */
  seido13: string
  /** 私立学校共済 */
  seido14: string
  /** 協会けんぼ（旧・政管健保） */
  seido15: string
  /** 日雇い */
  seido16: string
  /** 地方共済 */
  seido17: string
  /** 船員 */
  seido18: string
  /** 労災保険 */
  seido19: string
  /** 健康手帳の交付 */
  seido20: string
  /** 健康診査 */
  seido23: string
  /** 老齢関係メモ */
  riyoMemo1Knj: string
  /** 障害関係メモ */
  riyoMemo2Knj: string
  /** 遺族・寡婦メモ */
  riyoMemo3Knj: string
  /** 労災保険メモ */
  riyoMemo4Knj: string
  /** 成年後見人等 */
  kokenKnj: string
  /** 成年後見人制度区分 */
  seinenKoukenKbn: string
  /** 夜間対応訪問介護 */
  ser31Cd: string
  /** 認知症対応型通所介護 */
  ser32Cd: string
  /** 小規模多機能居宅介護 */
  ser33Cd: string
  /** 地域密着型特定施設入居者生活介護 */
  ser34Cd: string
  /** 地域密着型介護老人福祉施設入居者生活介護 */
  ser35Cd: string
  /** 夜間対応訪問介護（回数） */
  kaisu31: string
  /** 認知症対応型通所介護（回数） */
  kaisu32: string
  /** 小規模多機能居宅介護（回数） */
  kaisu33: string
  /** 地域密着型特定施設入居者生活介護（回数） */
  kaisu34: string
  /** 地域密着型介護老人福祉施設入居者生活介護（回数） */
  kaisu35: string
  /** サービス利用状況記載日 */
  serYmd: string
  /** 複合型サービス */
  ser36Cd: string
  /** 定期巡回・随時対応型訪問介護看護 */
  ser37Cd: string
  /** 複合型サービス（回数） */
  kaisu36: string
  /** 定期巡回・随時対応型訪問介護看護（回数） */
  kaisu37: string
  /** 制度利用30（後期高齢者医療） */
  seido30: string
  /** 制度利用31（その他1） */
  seido31: string
  /** 制度利用32（その他2） */
  seido32: string
  /** 制度利用33（その他3） */
  seido33: string
  /** 制度利用ﾒﾓ5（その他1（メモ）） */
  riyoMemo5Knj: string
  /** 制度利用ﾒﾓ6（その他2（メモ）） */
  riyoMemo6Knj: string
  /** 制度利用ﾒﾓ7（その他3（メモ）） */
  riyoMemo7Knj: string
  /** (介護予防)訪問型サービス */
  ser38Cd: string
  /** (介護予防)通所型サービス */
  ser39Cd: string
  /** その他の生活支援サービス */
  ser40Cd: string
  /** (介護予防)訪問型サービスの利用回数 */
  kaisu38: string
  /** (介護予防)通所型サービスの利用回数 */
  kaisu39: string
  /** その他の生活支援サービス（回数） */
  kaisu40: string
  /** その他の生活支援サービス（名称） */
  ser40NameKnj: string
}
