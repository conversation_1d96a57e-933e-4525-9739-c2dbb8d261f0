<script setup lang="ts">
/**
 * Or01997:有機体:［アセスメント］画面（居宅）（7まとめ）
 * GUI00805_［アセスメント］画面（居宅）（7まとめ）
 *
 * @description
 * ［アセスメント］画面（居宅）（7まとめ）
 *
 * <AUTHOR>
 *
 * ToDo 共通情報.まとめフラグ未実装
 */
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { OrCD001Const } from '../OrCD001/OrCD001.constants'
import { Or58226Const } from '../Or58226/Or58226.constants'
import { Or58213Const } from '../Or58213/Or58213.constants'
import { Or50030Const } from '../Or50030/Or50030.constants'
import { TeX0002Logic } from '../../template/TeX0002/TeX0002.logic'
import { Or35672Logic } from '../Or35672/Or35672.logic'
import { Or35672Const } from '../Or35672/Or35672.constants'
import { Or10812Const } from '../Or10812/Or10812.constants'
import { Or10812Logic } from '../Or10812/Or10812.logic'
import { OrX0209Const } from '../OrX0209/OrX0209.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or01997Const } from './Or01997.constants'
import type { Or01997Type } from './Or01997.type'
import {
  useCmnCom,
  useCmnRouteCom,
  useScreenStore,
  useScreenTwoWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or01997OnewayType } from '~/types/cmn/business/components/Or01997Type'
import type { TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
import type {
  AssessmentHomeTab7NewSelectInEntity,
  AssessmentHomeTab7NewSelectOutEntity,
  assessmentHomeTab7SelectInEntity,
  assessmentHomeTab7SelectOutEntity,
  assessmentHomeTab7UpdateInEntity,
  assessmentHomeTab7UpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentHome7Entity'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Or10812Type } from '~/types/cmn/business/components/Or10812Type'
import type { OrX0201OnewayType } from '~/types/cmn/business/components/OrX0201Type'
import type {
  IssuesAndGoalListItem,
  OrCD006OnewayType,
} from '~/types/cmn/business/components/OrCD006Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { OrX0209Type } from '~/types/cmn/business/components/OrX0209Type'

/**********************************************************
 * Props
 **********************************************************/
interface Props {
  onewayModelValue: Or01997OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

/**********************************************************
 * 変数定義
 **********************************************************/
const { t } = useI18n()

/**
 * 子コンポーネンID
 */
const orCd001 = ref({ uniqueCpId: OrCD001Const.CP_ID(0) })
const or58226 = ref({ uniqueCpId: Or58226Const.CP_ID(0) })
const or58213 = ref({ uniqueCpId: '' })
const or50030 = ref({ uniqueCpId: '' })
const or50030_1 = ref({ uniqueCpId: '' })
const or10812 = ref({ uniqueCpId: '' })
const orX0209 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })

/** 解決すべき課題と目標Ref */
const issuesAndGoalListRef = ref<HTMLElement | null>(null)

/** 小分類コード */
let t3cd = ''

/**
 * タブRef
 */
const componentsRef = ref<HTMLElement | null>()

/**
 * 画面表示まとめフラグ
 *
 * @example '1':全体のまとめ '2':状況別入力
 */
const summaryFlg = ref(useCmnRouteCom().getInitialSettingMaster()?.gdlMatomeFlg ?? '1')

/**
 * 平成区分フラグ
 *
 * @example '1':平成以前 '2':平成以降
 */
const heiseiKbnFlg = ref<string>('1')

/**
 * ロード状態制御フラグ
 */
const isLoadingRef = ref<boolean>(false)

/**
 * 状況別マスタ情報
 */
const masterRenderList = ref<assessmentHomeTab7SelectOutEntity['data']['cpnMucGdlKoumokuList']>([])

/**
 * 必要性の有無ラジオボタングループ
 */
const requireRadioGroup = ref<CodeType[]>([])

/**
 * 必要性2有無ラジオボタングループ
 */
const require2RadioGroup = ref<CodeType[]>([])

/**
 * 個別避難計画測定有無リスト
 */
const evacuationPlanRadioGroup = ref<CodeType[]>([])

/**
 * インデックス
 */
const currentIndex = ref(0)

/**
 * 画面更新区分
 */
const updateKbn = ref('')

/**
 * 画面表示フラグ
 */
const screenDisplayFlg = ref(true)

/**
 * ロカールOneway
 */
const localOneway = reactive({
  pageTitle: {
    tabNo: '7',
    title: t('label.tab7-title-all'),
  } as OrX0201OnewayType,
  // 全体のまとめ「まとめ入力」
  orX0156AllInput: {
    rows: '12',
    maxRows: '12',
  } as OrX0156OnewayType,
  // 状況別入力
  orX0156DependentInput: {
    rows: '3',
    maxRows: '3',
  } as OrX0156OnewayType,
  // ラジオボタングループ共通設定
  mo00039Oneway: {
    showItemLabel: false,
    checkOff: true,
  } as Mo00039OnewayType,
  // テキストフィールド共通設定
  mo00045Oneway: {
    showItemLabel: false,
    width: '220px',
    maxLength: '20',
  } as Mo00045OnewayType,
  // テキストフィールド共通設定
  mo00045Oneway_2: {
    showItemLabel: false,
    width: '186px',
    maxLength: '14',
  } as Mo00045OnewayType,
  // 備考災害時
  orX0156Oneway: {
    rows: '3',
    maxRows: '3',
    width: '100%',
    itemLabel: t('label.assessment-home-tab7-biko-label'),
    showItemLabel: true,
    isVerticalLabel: true,
  } as OrX0156OnewayType,
  // 特記事項ダイアログ
  or10812Oneway: {
    sc1Id: '1',
    gdlId: '1',
    revisionFlag: '',
  },
  orX0201Oneway: {
    title: t('label.tab7-title-all'),
    tabNo: '7',
    scrollToUniqueCpId: '',
  } as OrX0201OnewayType,
  // 解決すべき課題と目標一覧
  issuesAndGoalsListOneway: {
    assessmentName:
      t('label.assessment-home-issuesAndGoals-no-1') +
      '\r\n' +
      t('label.assessment-home-issuesAndGoals-name-1'),
  } as OrCD006OnewayType,
  // 入力支援画面
  or51775oneway: {} as Or51775OnewayType,
})

/**
 * ロカールTwoway
 */
const local = reactive({
  commonInfo: {} as TeX0002Type,
  issuesAndGoalsList: {
    items: [] as IssuesAndGoalListItem[],
  },
})

/**********************************************************
 * 計算プロパティ
 **********************************************************/
const showDialogOr10812 = computed(() => {
  return Or10812Logic.state.get(or10812.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**********************************************************
 * Pinia
 **********************************************************/
useSetupChildProps(props.uniqueCpId, {
  [OrCD001Const.CP_ID(0)]: orCd001.value,
  [Or58226Const.CP_ID(0)]: or58226.value,
  [Or58213Const.CP_ID(0)]: or58213.value,
  [Or50030Const.CP_ID(0)]: or50030.value,
  [Or50030Const.CP_ID(1)]: or50030_1.value,
  [Or10812Const.CP_ID(1)]: or10812.value,
  [OrX0209Const.CP_ID(0)]: orX0209.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
})

const { refValue } = useScreenTwoWayBind<Or01997Type>({
  uniqueCpId: props.uniqueCpId,
  cpId: Or01997Const.CP_ID(0),
})

// 初期化
refValue.value = {
  require: '',
  evacuationPlan: '',
  stakeholderNames: { value: '' },
  relationship: { value: '' },
  phoneNumber: { value: '' },
  fax: { value: '' },
  email: { value: '' },
  remark_disaster: { value: '' },
  require2: '',
  remark_advocacy: { value: '' },
  dmy_select1: '',
  dmy_select2: '',
  recordList: [
    {
      mastId: '',
      memoKnj: { value: '' },
    },
    {
      mastId: '',
      memoKnj: { value: '' },
    },
    {
      mastId: '',
      memoKnj: { value: '' },
    },
    {
      mastId: '',
      memoKnj: { value: '' },
    },
    {
      mastId: '',
      memoKnj: { value: '' },
    },
  ] as Or01997Type['recordList'],
}

/**********************************************************
 * 関数定義
 **********************************************************/
/**
 * 共通情報取得
 */
function getCommonInfo() {
  summaryFlg.value = useCmnRouteCom().getInitialSettingMaster()?.gdlMatomeFlg ?? '1'
  const commonInfo = TeX0002Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo = commonInfo

    // 解決すべき課題と目標一覧パラメータ設定
    localOneway.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
    localOneway.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
    localOneway.issuesAndGoalsListOneway.assNo = Or01997Const.DEFAULT.TAB_ID
    localOneway.issuesAndGoalsListOneway.userId = local.commonInfo.userId

    local.issuesAndGoalsList.items =
      cloneDeep(commonInfo.issuesAndGoalsList) ?? ([] as IssuesAndGoalListItem[])

    // 作成日チェックし、平成区分フラグを設定する
    const createTime = new Date('2018/04/01').getTime()
    const commonCreateYmd = new Date(commonInfo?.createYmd ?? '2018/04/01').getTime()
    if (commonCreateYmd > createTime) {
      heiseiKbnFlg.value = '1'
    } else {
      heiseiKbnFlg.value = '2'
    }
  }
}

/**
 *  複写画面から共通情報を取得
 */
function getCommonInfoFromCopy() {
  summaryFlg.value = useCmnRouteCom().getInitialSettingMaster()?.gdlMatomeFlg ?? '1'
  const commonInfo = Or35672Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    /** 改定フラグ */
    local.commonInfo.ninteiFormF = commonInfo.params?.ninteiFormF
    /** 選択中タブID */
    local.commonInfo.activeTabId = commonInfo.params?.activeTabId
    /** 事業所ID */
    local.commonInfo.jigyoId = commonInfo.params?.jigyoId
    /** 法人ID */
    local.commonInfo.houjinId = commonInfo.params?.houjinId
    /** 施設ID */
    local.commonInfo.shisetuId = commonInfo.params?.shisetuId
    /** 利用者ID */
    local.commonInfo.userId = commonInfo.params?.userId
    /** 計画対象期間ID */
    local.commonInfo.sc1Id = commonInfo.params?.sc1Id
    /** アセスメントID */
    local.commonInfo.gdlId = commonInfo.params?.gdlId
    /** 作成者ID */
    local.commonInfo.createUserId = commonInfo.params?.createUserId
    /** 作成日 */
    local.commonInfo.createYmd = commonInfo.params?.createdYmd
    /** 種別ID */
    local.commonInfo.syubetuId = commonInfo.params?.syubetsuId
    /** 期間管理フラグ */
    local.commonInfo.kikanKanriFlg = commonInfo.params?.kikanFlg
    /** 課題と目標リスト */
    local.commonInfo.issuesAndGoalsList = commonInfo.params?.issuesAndGoalsList

    local.issuesAndGoalsList.items =
      cloneDeep(commonInfo.params?.issuesAndGoalsList) ?? ([] as IssuesAndGoalListItem[])

    // 作成日チェックし、平成区分フラグを設定する
    const createTime = new Date('2018/04/01').getTime()
    const commonCreateYmd = new Date(commonInfo.params?.createdYmd ?? '2018/04/01').getTime()
    if (commonCreateYmd > createTime) {
      heiseiKbnFlg.value = '1'
    } else {
      heiseiKbnFlg.value = '2'
    }
  }
}

/**
 * 初期情報取得
 */
async function getInitDataInfo() {
  isLoadingRef.value = true
  try {
    // リクエストパラメータ
    const inputData: assessmentHomeTab7SelectInEntity = {
      /**
       * 計画期間ID
       */
      sc1Id: local.commonInfo.sc1Id,
      /**
       * アセスメントID
       */
      gdlId: local.commonInfo.gdlId,
      /**
       * 改定フラグ
       */
      ninteiFormF: local.commonInfo.ninteiFormF,
      /**
       * 事業者ID
       */
      svJigyoId: local.commonInfo.jigyoId,
      /**
       * 施設ID
       */
      shisetuId: local.commonInfo.shisetuId ?? '',
      /**
       * 法人ID
       */
      houjinId: useSystemCommonsStore().getHoujinId ?? '',
      /**
       * 作成日
       */
      kijunbiYmd: local.commonInfo.createYmd,
      /**
       * まとめフラグ
       */
      matomeFlg: summaryFlg.value,
    }

    // APIを呼び出し
    const {
      data: { cpnMucGdlKoumokuList, gdl4NeceR3List, gdl4NeceH21List },
    }: assessmentHomeTab7SelectOutEntity = await ScreenRepository.select(
      'assessmentHomeTab7MatomeSelect',
      inputData
    )
    masterRenderList.value = cpnMucGdlKoumokuList ?? []
    // 改定フラグチェック
    // R3の場合
    if (gdl4NeceR3List.length === 0 && gdl4NeceH21List.length === 0) {
      // データがない場合、更新区分を新規に設定する
      updateKbn.value = UPDATE_KBN.CREATE
      isLoadingRef.value = false
      // 初期化
      refValue.value = {
        require: '',
        evacuationPlan: '',
        stakeholderNames: { value: '' },
        relationship: { value: '' },
        phoneNumber: { value: '' },
        fax: { value: '' },
        email: { value: '' },
        remark_disaster: { value: '' },
        require2: '',
        remark_advocacy: { value: '' },
        dmy_select1: '',
        dmy_select2: '',
        recordList: [
          {
            mastId: '',
            memoKnj: { value: '' },
          },
          {
            mastId: '',
            memoKnj: { value: '' },
          },
          {
            mastId: '',
            memoKnj: { value: '' },
          },
          {
            mastId: '',
            memoKnj: { value: '' },
          },
          {
            mastId: '',
            memoKnj: { value: '' },
          },
        ] as Or01997Type['recordList'],
      }
      // Piniaに保存する
      useScreenStore().setCpTwoWay({
        uniqueCpId: props.uniqueCpId,
        cpId: Or01997Const.CP_ID(0),
        isInit: true,
        value: refValue.value,
      })
      return
    } else {
      updateKbn.value = UPDATE_KBN.NONE
    }
    if (local.commonInfo.ninteiFormF === '5') {
      const recordList = masterRenderList.value.map((item) => {
        const findedItem = gdl4NeceR3List.find((sItem) => sItem.mastId === item.mastId)
        if (findedItem) {
          return {
            mastId: item.mastId ?? '',
            memoKnj: { value: findedItem.memoKnj ?? '' },
          }
        } else {
          return {
            mastId: item.mastId ?? '',
            memoKnj: { value: '' },
          }
        }
      })
      // R3/４改訂版を利用する
      refValue.value = {
        require: gdl4NeceR3List[0]?.hitsuyo1 ?? '0',
        evacuationPlan: gdl4NeceR3List[0]?.kobetsuhinan ?? '0',
        stakeholderNames: { value: gdl4NeceR3List[0]?.nameKnj ?? '0' },
        relationship: { value: gdl4NeceR3List[0]?.kankeiKnj ?? '0' },
        phoneNumber: { value: gdl4NeceR3List[0]?.tel ?? '' },
        fax: { value: gdl4NeceR3List[0]?.fax ?? '' },
        email: { value: gdl4NeceR3List[0]?.eMail ?? '' },
        remark_disaster: { value: gdl4NeceR3List[0]?.bikoSaigaiKnj ?? '' },
        require2: gdl4NeceR3List[0]?.hitsuyo2 ?? '',
        remark_advocacy: { value: gdl4NeceR3List[0]?.bikoKenriKnj ?? '' },
        dmy_select1: '',
        dmy_select2: '',
        recordList: recordList,
      }
    }
    // H21の場合
    else if (local.commonInfo.ninteiFormF === '4') {
      const recordList = masterRenderList.value.map((item) => {
        const findedItem = gdl4NeceH21List.find((sItem) => sItem.mastId === item.mastId)
        if (findedItem) {
          return {
            mastId: item.mastId ?? '',
            memoKnj: { value: findedItem.memoKnj ?? '' },
          }
        } else {
          return {
            mastId: item.mastId ?? '',
            memoKnj: { value: '' },
          }
        }
      })
      refValue.value = {
        require: gdl4NeceH21List[0]?.hitsuyo1 ?? '0',
        evacuationPlan: '',
        stakeholderNames: { value: gdl4NeceH21List[0]?.nameKnj ?? '' },
        relationship: { value: gdl4NeceH21List[0]?.kankeiKnj ?? '' },
        phoneNumber: { value: gdl4NeceH21List[0]?.tel ?? '' },
        fax: { value: gdl4NeceH21List[0]?.fax ?? '' },
        email: { value: gdl4NeceH21List[0]?.eMail ?? '' },
        remark_disaster: { value: gdl4NeceH21List[0]?.bikoSaigaiKnj ?? '' },
        require2: gdl4NeceH21List[0]?.hitsuyo2 ?? '',
        remark_advocacy: { value: gdl4NeceH21List[0]?.bikoKenriKnj ?? '' },
        dmy_select1: gdl4NeceH21List[0]?.dmySelect1 ?? '',
        dmy_select2: gdl4NeceH21List[0]?.dmySelect2 ?? '',
        recordList: recordList,
      }
    }

    // Piniaに保存する
    useScreenStore().setCpTwoWay({
      uniqueCpId: props.uniqueCpId,
      cpId: Or01997Const.CP_ID(0),
      isInit: true,
      value: refValue.value,
    })
  } catch (e) {
    console.log(e)
  } finally {
    isLoadingRef.value = false
  }
}

/**
 * 保存処理
 */
async function userSave() {
  try {
    const systemCommonsStore = useSystemCommonsStore()
    isLoadingRef.value = true
    const userName =
      (systemCommonsStore.getUserSelectUserInfo()?.nameSei ?? '管理者太郎') +
      (systemCommonsStore.getUserSelectUserInfo()?.nameMei ?? '')

    const childrenRefValue = useScreenUtils().getChildCpBinds(props.uniqueCpId, {
      [OrX0209Const.CP_ID(0)]: { cpPath: OrX0209Const.CP_ID(0), twoWayFlg: true },
    })
    // 画面.履歴更新区分<>"D"の場合、以下の設定を行う
    // ・画面.アセスメントID＝0の場合、 履歴更新区分＝"C"
    // ・画面.アセスメントID<>0の場合、履歴更新区分＝"U"
    let historyUpdateKbn = UPDATE_KBN.UPDATE
    if (local.commonInfo.gdlId === '0') {
      historyUpdateKbn = UPDATE_KBN.CREATE
    } else if (local.commonInfo.deleteKbn === '2') {
      historyUpdateKbn = UPDATE_KBN.DELETE
    }

    // 画面更新区分設定
    if (updateKbn.value === UPDATE_KBN.NONE) {
      if (local.commonInfo.deleteKbn === '1') {
        updateKbn.value = UPDATE_KBN.DELETE
      } else if (local.commonInfo.historyUpdateKbn === UPDATE_KBN.CREATE) {
        updateKbn.value = UPDATE_KBN.CREATE
      } else {
        updateKbn.value = UPDATE_KBN.UPDATE
      }
    }

    const kadaiList = childrenRefValue[OrX0209Const.CP_ID(0)].twoWayBind?.value as OrX0209Type
    const inputData: assessmentHomeTab7UpdateInEntity = {
      tabId: Or01997Const.DEFAULT.TAB_ID,
      raiId: local.commonInfo.gdlId ?? '',
      kikanFlg: local.commonInfo.kikanKanriFlg ?? '1',
      updateKbn: updateKbn.value,
      deleteKbn: local.commonInfo.deleteKbn ?? '0',
      planningPeriodNo: local.commonInfo.historyNo ?? '0',
      edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
      edocumentUseParam: useCmnCom().getEDocumentParam(),
      startYmd: local.commonInfo.periodStartYmd ?? '',
      endYmd: local.commonInfo.periodEndYmd ?? '',
      krirekiNo: local.commonInfo.historyNo ?? '2',
      kinoId: '42009001',
      matomeFlg: summaryFlg.value,
      loginId: systemCommonsStore.getCurrentUser.loginId ?? '',
      sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '3GK',
      shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      sysCd: systemCommonsStore.getSystemCode ?? '1',
      svJigyoKnj: local.commonInfo.jigyoKnj ?? '事業所名称A',
      createUserName: local.commonInfo.createUserName ?? '管理者　太郎',
      userName: userName,
      houjinId: systemCommonsStore.getHoujinId ?? '',
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      userId: local.commonInfo.userId ?? '000000001',
      svJigyoId: local.commonInfo.jigyoId ?? '',
      syubetsuId: systemCommonsStore.getSyubetu ?? '',
      historyUpdateKbn: historyUpdateKbn,
      sc1Id: local.commonInfo.sc1Id ?? '',
      kijunbiYmd: local.commonInfo.createYmd ?? '',
      sakuseiId: local.commonInfo.createUserId ?? '',
      ninteiFlg: local.commonInfo.ninteiFormF ?? '4',
      gdl4NeceH21List:
        refValue.value?.recordList.map((item, index) => {
          return {
            mastId: masterRenderList.value[index].mastId ?? '',
            memoKnj: item.memoKnj.value,
            hitsuyo1: refValue.value?.require ?? '',
            hitsuyo2: refValue.value?.require2 ?? '',
            nameKnj: refValue.value?.stakeholderNames.value ?? '',
            kankeiKnj: refValue.value?.relationship.value ?? '',
            tel: refValue.value?.phoneNumber.value ?? '',
            fax: refValue.value?.fax.value ?? '',
            eMail: refValue.value?.email.value ?? '',
            bikoSaigaiKnj: refValue.value?.remark_disaster.value ?? '',
            bikoKenriKnj: refValue.value?.remark_advocacy.value ?? '',
            kobetsuhinan: refValue.value?.evacuationPlan ?? '',
          }
        }) ?? [],
      gdl4NeceR3List:
        refValue.value?.recordList.map((item, index) => {
          return {
            mastId: masterRenderList.value[index].mastId ?? '',
            memoKnj: item.memoKnj.value,
            hitsuyo1: refValue.value?.require ?? '',
            hitsuyo2: refValue.value?.require2 ?? '',
            nameKnj: refValue.value?.stakeholderNames.value ?? '',
            kankeiKnj: refValue.value?.relationship.value ?? '',
            tel: refValue.value?.phoneNumber.value ?? '',
            fax: refValue.value?.fax.value ?? '',
            eMail: refValue.value?.email.value ?? '',
            bikoSaigaiKnj: refValue.value?.remark_disaster.value ?? '',
            bikoKenriKnj: refValue.value?.remark_advocacy.value ?? '',
            kobetsuhinan: refValue.value?.evacuationPlan ?? '',
          }
        }) ?? [],
      kadaiList: kadaiList.items.map((item) => {
        const assNo =
          item.updateKbn === UPDATE_KBN.CREATE ? Or01997Const.DEFAULT.TAB_ID : item.assNo
        return {
          kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
          tankiKnj: item.shorttermGoal.value,
          choukiKnj: item.longtermGoal.value,
          updateKbn: item.updateKbn ?? '',
          seq: item.seq.toString(),
          id: item.dataId ?? '',
          assNo,
        }
      }),
    }
    const createTime = new Date('2018/04/01').getTime()
    const commonCreateYmd = new Date(local.commonInfo?.createYmd ?? '2018/04/01').getTime()
    if (local.commonInfo.ninteiFormF === '4' && createTime >= commonCreateYmd) {
      inputData.gdl4NeceH21List.forEach((item) => {
        item.hitsuyo1 = refValue.value?.dmy_select1 ?? ''
        item.hitsuyo2 = refValue.value?.dmy_select2 ?? ''
      })
    }

    const resData: assessmentHomeTab7UpdateOutEntity = await ScreenRepository.update(
      'assessmentHomeTab7MatomeUpdate',
      inputData
    )
    if (resData) {
      TeX0002Logic.data.set({
        uniqueCpId: props.parentUniqueCpId,
        value: {
          updateData: {
            errKbn: resData.data.errKbn ?? '',
            gdlId: resData.data.gdlId,
            sc1Id: resData.data.sc1Id,
          },
        },
      })
      TeX0002Logic.state.set({
        uniqueCpId: props.parentUniqueCpId,
        state: {
          isSaveCompleted: true,
        },
      })
      return { gdlId: resData.data.gdlId, sc1Id: resData.data.sc1Id }
    }
    console.log(resData)
    isLoadingRef.value = false
  } catch (e) {
    console.log(e)
    isLoadingRef.value = false
  }
}

/**
 * 新規処理
 */
async function createNew() {
  try {
    const inputData: AssessmentHomeTab7NewSelectInEntity = {
      /** 事業者ID */
      svJigyoId: local.commonInfo.jigyoId ?? '',
      /** 施設ID */
      shisetuId: useSystemCommonsStore().getShisetuId ?? '',
      /** 法人ID */
      houjinId: useSystemCommonsStore().getHoujinId ?? '',
    }
    // ［アセスメント］画面（居宅）（7まとめ） 初期画面作成の情報を取得する。
    const resData: AssessmentHomeTab7NewSelectOutEntity = await ScreenRepository.select(
      'assessmentHomeTab7MatomeNewSelect',
      inputData
    )
    // 更新区分を新規に設定する
    updateKbn.value = UPDATE_KBN.CREATE
    if (resData.data) {
      masterRenderList.value = resData.data.cpnMucGdlKoumokuList
      // データクリア
      refValue.value = {
        require: '',
        evacuationPlan: '',
        stakeholderNames: { value: '' },
        relationship: { value: '' },
        phoneNumber: { value: '' },
        fax: { value: '' },
        email: { value: '' },
        remark_disaster: { value: '' },
        require2: '',
        remark_advocacy: { value: '' },
        dmy_select1: '',
        dmy_select2: '',
        recordList: masterRenderList.value.map((item) => {
          return {
            mastId: item.mastId ?? '',
            memoKnj: { value: '' },
          }
        }),
      }
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 初期コード取得
 */
async function initCodes() {
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NECESSITY_STATUS },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NECESSITY_STATUS_2 },
    { mCdKbnId: CmnMCdKbnId.M_CD_CMN_ID_PLAN_UMU },
  ]
  // 汎用コードAPIを呼び出し
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 必要性
  requireRadioGroup.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_NECESSITY_STATUS)
  // 必要性2
  require2RadioGroup.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_NECESSITY_STATUS_2
  ).reverse()
  // 測定中有無 個別避難計画測定有無リスト
  evacuationPlanRadioGroup.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_CMN_ID_PLAN_UMU)
}

/**
 * 「特記事項アイコンボタン」押下
 *
 * @param index - インデックス
 */
const setShowDialogOr10812 = (index: number) => {
  localOneway.or10812Oneway.revisionFlag = local.commonInfo.ninteiFormF ?? ''
  currentIndex.value = index
  Or10812Logic.state.set({
    uniqueCpId: or10812.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 「特記事項ダイアログ決定ボタン」押下処理
 *
 * @param result - ダイアログ返却値
 */
const getOr10812DialogResult = (result: Or10812Type) => {
  console.log(result)
  // 追加の場合
  if (result.overwriteFlag === '0') {
    refValue.value!.recordList[currentIndex.value].memoKnj.value +=
      `${result.specialNoteMatterSelect}\r\n`
  } else if (result.overwriteFlag === '1') {
    refValue.value!.recordList[currentIndex.value].memoKnj.value =
      `${result.specialNoteMatterSelect}\r\n`
  }
}

/**
 * 解決すべき課題と目標にスクロール関数
 */
const scrollToIssues = () => {
  if (issuesAndGoalListRef.value) {
    const scrollTop = issuesAndGoalListRef.value.offsetTop
    window.scroll({ top: scrollTop, behavior: 'smooth' })
  }
}

/**
 * 入力支援画面を呼び出す
 *
 * @param t3Cd - 小分類コード
 */
const setShowDialogOr51775 = (t3Cd: string) => {
  // テーブル名設定
  let tableName = ''
  // テーブル名：改訂フラグが4「H21/4」の場合、"cpn_tuc_gdl4_nece_h21"、改訂版(R3)の場合、"cpn_tuc_gdl5_nece_r3"
  if (local.commonInfo.ninteiFormF === '4') {
    tableName = 'cpn_tuc_gdl4_nece_h21'
  } else {
    tableName = 'cpn_tuc_gdl5_nece_r3'
  }
  t3cd = t3Cd
  localOneway.or51775oneway = {
    // タイトル："備考"
    title: '備考',
    // 大分類CD：602
    t1Cd: '602',
    // 中分類CD：21
    t2Cd: '21',
    // 小分類CD：2
    t3Cd,
    tableName,
    // カラム名："biko_saigai_knj"
    columnName: 'biko_saigai_knj',
    // アセスメント方式：共通情報.アセスメント方式
    assessmentMethod: '',
    // 文章内容：画面.権利擁護備考内容
    inputContents: refValue?.value?.remark_disaster.value ?? '',
    // 画面ID:"GUI00805"
    screenId: 'GUI00805',
    // 利用者ID：共通情報.利用者ID
    userId: local.commonInfo.userId ?? '',
    bunruiId: '',
  }

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 入力支援画面返却値取得
 *
 * @param result - 入力支援画面返却値
 */
const getDialogOr51775Result = (result: Or51775ConfirmType) => {
  switch (t3cd) {
    case '1': {
      if (result.type === Or01997Const.DEFAULT.INPUT_SUPPORT_ADD) {
        refValue.value!.remark_disaster.value += result.value
      } else if (result.type === Or01997Const.DEFAULT.INPUT_SUPPORT_OVERWRITE) {
        refValue.value!.remark_disaster.value = result.value
      }
      break
    }
    case '2': {
      if (result.type === Or01997Const.DEFAULT.INPUT_SUPPORT_ADD) {
        refValue.value!.remark_advocacy.value += result.value
      } else if (result.type === Or01997Const.DEFAULT.INPUT_SUPPORT_OVERWRITE) {
        refValue.value!.remark_advocacy.value = result.value
      }
      break
    }
  }
}

/**
 * 複写画面取得
 */
const getCopyData = async () => {
  try {
    // リクエストパラメータ
    const inputData: assessmentHomeTab7SelectInEntity = {
      /**
       * 計画期間ID
       */
      sc1Id: local.commonInfo.copySc1Id,
      /**
       * アセスメントID
       */
      gdlId: local.commonInfo.copyGdlId,
      /**
       * 改定フラグ
       */
      ninteiFormF: local.commonInfo.copyNinteiFormF,
      /**
       * 事業者ID
       */
      svJigyoId: local.commonInfo.jigyoId ?? '',
      /**
       * 施設ID
       */
      shisetuId: local.commonInfo.shisetuId ?? '',
      /**
       * 法人ID
       */
      houjinId: useSystemCommonsStore().getHoujinId ?? '',
      /**
       * 作成日
       */
      kijunbiYmd: local.commonInfo.copyCreateYmd,
      /**
       * まとめフラグ
       */
      matomeFlg: summaryFlg.value,
    }
    const {
      data: { gdl4NeceR3List, gdl4NeceH21List },
    }: assessmentHomeTab7SelectOutEntity = await ScreenRepository.select(
      'assessmentHomeTab7MatomeSelect',
      inputData
    )
    if (local.commonInfo.ninteiFormF === '5') {
      const recordList = masterRenderList.value.map((item) => {
        const findedItem = gdl4NeceR3List.find((sItem) => sItem.mastId === item.mastId)
        if (findedItem) {
          return {
            mastId: item.mastId ?? '',
            memoKnj: { value: findedItem.memoKnj ?? '' },
          }
        } else {
          return {
            mastId: item.mastId ?? '',
            memoKnj: { value: '' },
          }
        }
      })
      // R3/４改訂版を利用する
      refValue.value = {
        require: gdl4NeceR3List[0]?.hitsuyo1 ?? '0',
        evacuationPlan: gdl4NeceR3List[0]?.kobetsuhinan ?? '0',
        stakeholderNames: { value: gdl4NeceR3List[0]?.nameKnj ?? '0' },
        relationship: { value: gdl4NeceR3List[0]?.kankeiKnj ?? '0' },
        phoneNumber: { value: gdl4NeceR3List[0]?.tel ?? '' },
        fax: { value: gdl4NeceR3List[0]?.fax ?? '' },
        email: { value: gdl4NeceR3List[0]?.eMail ?? '' },
        remark_disaster: { value: gdl4NeceR3List[0]?.bikoSaigaiKnj ?? '' },
        require2: gdl4NeceR3List[0]?.hitsuyo2 ?? '',
        remark_advocacy: { value: gdl4NeceR3List[0]?.bikoKenriKnj ?? '' },
        dmy_select1: '',
        dmy_select2: '',
        recordList: recordList,
      }
    }
    // H21の場合
    else if (local.commonInfo.ninteiFormF === '4') {
      const recordList = masterRenderList.value.map((item) => {
        const findedItem = gdl4NeceH21List.find((sItem) => sItem.mastId === item.mastId)
        if (findedItem) {
          return {
            mastId: item.mastId ?? '',
            memoKnj: { value: findedItem.memoKnj ?? '' },
          }
        } else {
          return {
            mastId: item.mastId ?? '',
            memoKnj: { value: '' },
          }
        }
      })
      refValue.value = {
        require: gdl4NeceH21List[0]?.hitsuyo1 ?? '0',
        evacuationPlan: '',
        stakeholderNames: { value: gdl4NeceH21List[0]?.nameKnj ?? '' },
        relationship: { value: gdl4NeceH21List[0]?.kankeiKnj ?? '' },
        phoneNumber: { value: gdl4NeceH21List[0]?.tel ?? '' },
        fax: { value: gdl4NeceH21List[0]?.fax ?? '' },
        email: { value: gdl4NeceH21List[0]?.eMail ?? '' },
        remark_disaster: { value: gdl4NeceH21List[0]?.bikoSaigaiKnj ?? '' },
        require2: gdl4NeceH21List[0]?.hitsuyo2 ?? '',
        remark_advocacy: { value: gdl4NeceH21List[0]?.bikoKenriKnj ?? '' },
        dmy_select1: gdl4NeceH21List[0]?.dmySelect1 ?? '',
        dmy_select2: gdl4NeceH21List[0]?.dmySelect2 ?? '',
        recordList: recordList,
      }
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * tabを制御
 *
 * @param component - コンポーネント
 */
const disableTab = (component: HTMLElement) => {
  const focusableSelectors = [
    'a[href]',
    'area[href]',
    'input',
    'select',
    'textarea',
    'button',
    'iframe',
    '[tabindex]',
    '[contenteditable]',
  ]

  const focusables = component.querySelectorAll(focusableSelectors.join(','))
  focusables.forEach((el) => {
    el.setAttribute('tabindex', '-1')
  })
}

/**
 * 初期化処理
 */
onMounted(async () => {
  localOneway.orX0201Oneway.scrollToUniqueCpId = orX0209.value.uniqueCpId
  console.log('orX0209.value.uniqueCpId', orX0209.value.uniqueCpId)
  await initCodes()
})

/**********************************************************
 * watch関数
 **********************************************************/
// 画面イベント監視
watch(
  () => TeX0002Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    // 画面共通情報を取得
    getCommonInfo()

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or01997Const.DEFAULT.TAB_ID) {
      return
    }

    // 初期表示、タブ切り替えの場合
    if (newValue.isRefresh) {
      screenDisplayFlg.value = true
      await getInitDataInfo()
    }
    // 作成日変更処理
    if (newValue.isCreateDateChanged) {
      screenDisplayFlg.value = true
      await getInitDataInfo()
    }
    if (newValue.saveEventFlg) {
      await userSave()
    }
    if (newValue.createEventFlg) {
      await createNew()
    }
    if (newValue.deleteEventFlg) {
      screenDisplayFlg.value = false
    }
    if (newValue.copyEventFlg) {
      await createNew()
      // 更新区分設定
      updateKbn.value = UPDATE_KBN.UPDATE
      // 改定フラグ設定
      local.commonInfo.ninteiFormF = local.commonInfo.copyNinteiFormF
      await getCopyData()
    }
  },
  { immediate: true, deep: true }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or35672Logic.event.get(Or35672Const.CP_ID(0) + props.parentUniqueCpId),
  async (newValue) => {
    if (newValue?.reload === false) {
      return
    }
    getCommonInfoFromCopy()
    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or01997Const.DEFAULT.TAB_ID) {
      return
    }

    // 画面再表示
    if (newValue?.reload) {
      // 画面情報再取得
      await getInitDataInfo()
      // 全処理済み、タブ変更を禁止する
      if (props.onewayModelValue.mode === Or01997Const.DEFAULT.MODE_COPY) {
        if (componentsRef.value) {
          disableTab(componentsRef.value)
        }
      }
    }
  },
  { deep: true, immediate: true }
)
</script>
<template>
  <div
    v-show="screenDisplayFlg"
    ref="componentsRef"
    style="width: 1080px"
    class="pl-6"
  >
    <v-overlay
      :model-value="isLoadingRef"
      :persistent="true"
      class="align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      >
      </v-progress-circular
    ></v-overlay>
    <!-- タブタイトル -->
    <div class="pb-4 d-flex justify-space-between">
      <g-custom-orX0201
        :oneway-model-value="localOneway.orX0201Oneway"
        @on-click-link-btn="scrollToIssues"
      />
    </div>
    <!-- 全体のまとめ -->
    <div
      v-if="summaryFlg === '1'"
      class="c-sub-content"
    >
      <g-custom-orX0156
        v-if="refValue!.recordList[0].memoKnj.value !== undefined"
        v-model="refValue!.recordList[0].memoKnj"
        :oneway-model-value="localOneway.orX0156AllInput"
        @on-click-edit-btn="setShowDialogOr10812(0)"
      >
        <template #footer>
          <div class="special-notes d-flex align-center ml-4">{{ t('label.special-notes') }}</div>
        </template>
      </g-custom-orX0156>
    </div>
    <!-- 全体のまとめ「状況別入力」 -->
    <!-- 特記事項リストが繰り返して出力 -->
    <div v-else>
      <div
        v-for="(item, index) in masterRenderList"
        :key="index"
      >
        <div class="c-sub-title loop-title">{{ item.koumokuKnj }}</div>
        <div class="c-sub-content">
          <g-custom-orX0156
            v-if="refValue!.recordList[index].memoKnj.value !== undefined"
            v-model="refValue!.recordList[index].memoKnj"
            :oneway-model-value="localOneway.orX0156DependentInput"
            @on-click-edit-btn="setShowDialogOr10812(index)"
          >
            <template #footer>
              <div class="special-notes d-flex align-center ml-4">
                {{ t('label.special-notes') }}
              </div>
            </template>
          </g-custom-orX0156>
        </div>
      </div>
    </div>
    <!-- 災害時の対応の必要性について -->
    <div
      v-if="heiseiKbnFlg === '1'"
      class="c-sub-title loop-title"
    >
      {{ t('label.assessment-home-tab7-disaster-necessity-label') }}
    </div>
    <!-- 災害時の対応の必要性について -->
    <div
      v-if="heiseiKbnFlg === '1'"
      class="c-sub-content"
    >
      <!-- 必要性の有無 -->
      <div class="or-content">
        <div class="mr-16">
          <span class="d-block mb-3">{{ t('label.assessment-home-tab7-necessity-yn-label') }}</span>
          <base-mo00039
            v-if="refValue"
            v-model="refValue!.require"
            :oneway-model-value="localOneway.mo00039Oneway"
            class="radio-group"
          >
            <div
              v-for="(item, index) in requireRadioGroup"
              :key="index"
              class="mr-6 radio-item"
            >
              <base-at-radio
                :value="item.value"
                :radio-label="item.label"
              />
            </div>
          </base-mo00039>
        </div>
        <div
          v-if="local.commonInfo.ninteiFormF === '5'"
          class="ml-16"
        >
          <span class="d-block mb-3">{{ t('label.assessment-home-tab7-individual-label') }}</span>
          <base-mo00039
            v-if="refValue"
            v-model="refValue!.evacuationPlan"
            :oneway-model-value="localOneway.mo00039Oneway"
            class="radio-group"
          >
            <div
              v-for="(item, index) in evacuationPlanRadioGroup"
              :key="index"
              class="mr-6 radio-item"
            >
              <base-at-radio
                :value="item.value"
                :radio-label="item.label"
              />
            </div>
          </base-mo00039>
        </div>
      </div>
      <!-- 災害時の連絡先（家族以外/民生委員等） -->
      <div class="or-content c-sub-title third-header">
        {{ t('label.assessment-home-tab7-saigai-label') }}
      </div>
      <div class="or-content">
        <div
          cols="auto"
          class="mr-6"
        >
          <span class="d-block mb-2">{{ t('label.assessment-home-tab7-name-label') }}</span>
          <base-mo00045
            v-if="refValue"
            v-model="refValue!.stakeholderNames"
            :oneway-model-value="localOneway.mo00045Oneway"
          />
        </div>
        <div
          cols="auto"
          class="mr-6"
        >
          <span class="d-block mb-1">{{ t('label.assessment-home-tab7-ship-label') }}</span>
          <base-mo00045
            v-if="refValue"
            v-model="refValue!.relationship"
            :oneway-model-value="{ ...localOneway.mo00045Oneway, maxLength: '16' }"
          />
        </div>
        <div
          cols="auto"
          class="mr-6"
        >
          <span class="d-block mb-1">{{ t('label.assessment-home-tab7-tel-label') }}</span>
          <base-mo00045
            v-if="refValue"
            v-model="refValue!.phoneNumber"
            :oneway-model-value="localOneway.mo00045Oneway_2"
          />
        </div>
        <div cols="auto">
          <span class="d-block mb-1">{{ t('label.assessment-home-tab7-fax-label') }}</span>
          <base-mo00045
            v-if="refValue"
            v-model="refValue!.fax"
            :oneway-model-value="localOneway.mo00045Oneway_2"
          />
        </div>
      </div>
      <div class="or-content">
        <div class="remark">
          <span class="d-block mb-1">{{ t('label.assessment-home-tab7-e-mail-label') }}</span>
          <base-mo00045
            v-if="refValue"
            v-model="refValue!.email"
            :oneway-model-value="{ showItemLabel: false, maxLength: '128' }"
          />
        </div>
      </div>
      <div
        cols="11"
        class="remark"
      >
        <g-custom-orX0156
          v-if="refValue"
          v-model="refValue!.remark_disaster"
          :oneway-model-value="localOneway.orX0156Oneway"
          @on-click-edit-btn="setShowDialogOr51775('1')"
        />
      </div>
    </div>
    <!-- 権利擁護に関する対応の必要性について -->
    <div
      v-if="heiseiKbnFlg === '1'"
      class="c-sub-title loop-title"
    >
      {{ t('label.assessment-home-tab7-regarding-necessity-label') }}
    </div>
    <div
      v-if="heiseiKbnFlg === '1'"
      class="c-sub-content"
    >
      <!-- 必要性の有無 -->
      <div class="or-content">
        <div>
          <span class="d-block mb-3">{{ t('label.assessment-home-tab7-necessity-yn-label') }}</span>
          <base-mo00039
            v-if="refValue"
            v-model="refValue!.require2"
            :oneway-model-value="localOneway.mo00039Oneway"
            class="radio-group"
          >
            <div
              v-for="(item, index) in requireRadioGroup"
              :key="index"
              class="mr-6 radio-item"
            >
              <base-at-radio
                :value="item.value"
                :radio-label="item.label"
              />
            </div>
          </base-mo00039>
        </div>
      </div>
      <!-- 備考 -->
      <div class="or-content">
        <div class="remark">
          <g-custom-orX0156
            v-model="refValue!.remark_advocacy"
            :oneway-model-value="localOneway.orX0156Oneway"
            @on-click-edit-btn="setShowDialogOr51775('2')"
          />
        </div>
      </div>
    </div>
    <div
      v-if="heiseiKbnFlg === '2'"
      class="c-sub-content"
    >
      <div class="or-content">
        <div class="mr-12">
          <span class="d-block mb-3">
            {{ t('label.assessment-home-tab7-safety-correspondence-label') }}
          </span>
          <base-mo00039
            v-if="refValue"
            v-model="refValue!.dmy_select1"
            :oneway-model-value="localOneway.mo00039Oneway"
            class="radio-group"
          >
            <div
              v-for="(item, index) in require2RadioGroup"
              :key="index"
              class="mr-6 radio-item"
            >
              <base-at-radio
                :value="item.value"
                :radio-label="item.label"
              />
            </div>
          </base-mo00039>
        </div>
        <div class="ml-12">
          <span class="d-block mb-3">
            {{ t('label.assessment-home-tab7-advocacy-necessity-label') }}
          </span>
          <base-mo00039
            v-if="refValue"
            v-model="refValue!.dmy_select2"
            :oneway-model-value="localOneway.mo00039Oneway"
            class="radio-group"
          >
            <div
              v-for="(item, index) in require2RadioGroup"
              :key="index"
              class="mr-6 radio-item"
            >
              <base-at-radio
                :value="item.value"
                :radio-label="item.label"
              />
            </div>
          </base-mo00039>
        </div>
      </div>
    </div>
    <g-custom-or-10812
      v-if="showDialogOr10812"
      v-bind="or10812"
      :oneway-model-value="localOneway.or10812Oneway"
      @update:model-value="getOr10812DialogResult"
    />
    <!-- 入力支援画面 -->
    <g-custom-or51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      :oneway-model-value="localOneway.or51775oneway"
      @confirm="getDialogOr51775Result"
    />
  </div>
  <!-- 線 -->
  <c-v-row
    v-show="screenDisplayFlg"
    no-gutters
    style="margin: 0 -24px"
    class="py-6"
  >
    <c-v-divider></c-v-divider>
  </c-v-row>
  <!-- フッター -->
  <c-v-row
    v-show="screenDisplayFlg"
    no-gutters
    class="pb-6"
    style="width: 1080px"
  >
    <div
      ref="issuesAndGoalListRef"
      class="w-100"
    >
      <g-custom-or-x-0209
        v-bind="orX0209"
        :model-value="local.issuesAndGoalsList"
        :oneway-model-value="localOneway.issuesAndGoalsListOneway"
      />
    </div>
  </c-v-row>
</template>
<style lang="scss" scoped>
$margin-organism: 24px; // オーガニズム間のマージン
$margin-common: 48px; // 一般的なマージン
// サブタイトルコンテンツ
:deep(*.c-sub-title) {
  padding: 0px $margin-organism; // パディングを設定
  display: flex; // フレキシブルボックスを使用
  flex-wrap: nowrap; // 子要素を改行せずに横に並べる
  white-space: pre; // 空白文字を保持する
  align-items: center; // 垂直方向の中央揃え
  width: 100%; // 全幅
  font-weight: bolder; // 太字
  font-size: 16px; // フォントサイズ
  line-height: $margin-common; // 行間
  background-color: rgb(230, 230, 230); // 背景色
  box-sizing: border-box !important; // ボックスサイズをborder-boxに設定
  border: rgba(0, 0, 0, 0.1) 1px solid;
  &.action {
    background-color: rgba(7, 96, 230, 0.08);
    border: rgb(197, 214, 229) 1px solid;
    span {
      color: rgba(33, 77, 151, 1);
      text-decoration: underline;
      cursor: pointer;
    }
  }
}

// タブ情報入力領域
:deep(*.c-sub-content) {
  padding: $margin-organism $margin-common; // パディング設定
  width: 100% !important; // 全幅
  background-color: #fff; // 白色の背景
  // 領域内有機体ボックススタイル
  *.or-content {
    display: flex; // フレキシブルボックスを使用
    flex-wrap: nowrap; // 子要素を改行せずに横に並べる
    &:not(:last-child) {
      margin-bottom: 16px; // 最後の要素以外は下に16pxのマージン
    }
    &.show-border {
      border-bottom: rgba(0, 0, 0, 0.12) 1px solid;
    }
  }
}
.v-col {
  padding: 0px;
}
.special-notes {
  color: #666;
}
.radio-group {
  margin-left: -2px;
}
.radio-item {
  height: 36px;
}
.c-sub-title {
  &.third-header {
    font-size: 14px;
    border-radius: 4px;
    padding-left: 12px;
  }
  &.loop-title {
    line-height: 35px;
  }
}
:deep(.v-sheet.v-theme--mainTheme.mr-2) {
  margin-right: 0px !important;
}
.remark {
  width: 885px;
  :deep(.split-line) {
    display: none;
  }
}
.link-button {
  background-color: transparent !important;
  color: #000 !important;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
</style>
