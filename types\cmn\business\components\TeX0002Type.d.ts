import type { IssuesAndGoalListItem } from '~/types/cmn/business/components/OrX0209Type'
/**
 * TeX0002:［アセスメント］画面（居宅）テンプレート
 * GUI00794_［アセスメント］画面（居宅）（1）
 * GUI00795_［アセスメント］画面（居宅）（2）
 * GUI00796_［アセスメント］画面（居宅）（3）
 * GUI00797_［アセスメント］画面（居宅）（4）
 * GUI00798_［アセスメント］画面（居宅）（5）
 * GUI00799_［アセスメント］画面（居宅）（6①）
 * GUI00800_［アセスメント］画面（居宅）（6②）
 * GUI00801_［アセスメント］画面（居宅）（6③-④）
 * GUI00802_［アセスメント］画面（居宅）（6⑤）
 * GUI00803_［アセスメント］画面（居宅）（6⑥）
 * GUI00804_［アセスメント］画面（居宅）（6医）
 * GUI00805_［アセスメント］画面（居宅）（7まとめ）
 * GUI00806_［アセスメント］画面（居宅）（7スケジュール）*
 *
 * <AUTHOR>
 */
/**
 * 双方向バインドModelValue
 */
export interface TeX0002Type {
  /**
   * 期間管理フラグ
   */
  kikanKanriFlg?: string
  /**
   * 改定フラグ
   */
  ninteiFormF?: string
  /**
   * 種別ID
   */
  syubetuId?: string
  /**
   * 履歴更新区分
   */
  historyUpdateKbn?: string
  /**
   * 更新回数
   */
  historyModifiedCnt?: string
  /**
   * 選択中タブ
   */
  activeTabId?: string
  /**
   * 事業所ID
   */
  jigyoId?: string
  /**
   * 事業所名称
   */
  jigyoKnj?: string
  /**
   * 施設ID
   */
  shisetuId?: string
  /**
   * 法人ID
   */
  houjinId?: string
  /**
   * 利用者ID
   */
  userId?: string
  /**
   * 計画対象期間ID
   */
  sc1Id?: string
  /**
   * 計画対象期間番号
   */
  sc1No?: string
  /**
   * 計画対象期間開始日
   */
  periodStartYmd?: string
  /**
   * 計画対象期間終了日
   */
  periodEndYmd?: string
  /**
   * アセスメントID
   */
  gdlId?: string
  /**
   * 履歴番号
   */
  historyNo?: string
  /**
   * 作成者ID
   */
  createUserId?: string
  /**
   * 作成者名
   */
  createUserName?: string
  /**
   * 作成日
   */
  createYmd?: string
  /**
   * 複写元 利用者ID
   */
  copyUserId?: string
  /**
   * 複写元 作成日
   */
  copyCreateYmd?: string
  /**
   * 複写元 認定フラグ
   */
  copyNinteiFormF?: string
  /**
   * 複写元 計画期間ID
   */
  copySc1Id?: string
  /**
   * 複写元 アセスメントID
   */
  copyGdlId?: string
  /**
   * 削除区分
   */
  deleteKbn?: string
  /**
   * 課題と目標リスト
   */
  issuesAndGoalsList?: IssuesAndGoalListItem[]
  /**
   * 更新後データ
   */
  updateData?: UpdateData
}

/**
 * 単方向バインドModelValue
 */
export interface TeX0002OnewayType {
  /**
   * 様式(0：任意 | 1：居宅版 | ：施設版 | 3：高齢者住宅版)
   */
  styleFlag: '1'
  /**
   * 計画期間(1：管理する | 0：管理しない)
   */
  planPeriodFlag: '1'
}

/**
 * 更新後データ
 */
export interface UpdateData {
  /** 計画対象期間ID */
  sc1Id: string
  /** アセスメントID */
  gdlId: string
  /** エラー区分 */
  errKbn: string
}
