<script setup lang="ts">
/**
 * GUI00872: 選定表
 *
 * @description 検討表
 *
 *
 * <AUTHOR> DAO DINH DUONG
 */

import { onMounted, reactive, ref, watch, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep, isUndefined, uniqBy } from 'lodash'
import { Or33456Const } from './Or33456.constants'
import { Or05019Logic } from '~/components/custom-components/organisms/Or05019/Or05019.logic'
import { Or05019Const } from '~/components/custom-components/organisms/Or05019/Or05019.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useSetupChildProps, useScreenStore, useScreenTwoWayBind } from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or33769Const } from '~/components/custom-components/organisms/Or33769/Or33769.constants'
import { Or33769Logic } from '~/components/custom-components/organisms/Or33769/Or33769.logic'
import { OrX0010Const } from '~/components/custom-components/organisms/OrX0010/OrX0010.constants'
import { OrX0010Logic } from '~/components/custom-components/organisms/OrX0010/OrX0010.logic'
import type { OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import type {
  ConsiderInitialInfoSelectInEntity,
  ConsiderInitialInfoSelectOutEntity,
  CpnHkd3,
  CpnHkd2,
  Taishokikan,
  Rireki,
  RirekiJoho,
} from '~/repositories/cmn/entities/considerInitialInfoSelectEntity'
import type {
  ConsiderPeriodSelectInEntity,
  ConsiderPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/considerPeriodSelectEntity'
import type {
  ConsiderHistoryModifiedSelectInEntity,
  ConsiderHistoryModifiedSelectOutEntity,
} from '~/repositories/cmn/entities/considerHistoryModifiedSelectServiceEntity'
import type {
  ConsiderInitialInfoDataUpdateInEntity,
  ConsiderInitialInfoDataUpdateOutEntity,
} from '~/repositories/cmn/entities/considerInitialInfoDataUpdateEntity'
import type { Or33456OnewayType } from '~/types/cmn/business/components/Or33456Type'
import type { Or10279OneWayType } from '~/types/cmn/business/components/Or10279Type'
import { Or10279Const } from '~/components/custom-components/organisms/Or10279/Or10279.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import type { OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import { OrX0007Logic } from '~/components/custom-components/organisms/OrX0007/OrX0007.logic'
import type { OrX0008OnewayType } from '~/types/cmn/business/components/OrX0008Type'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { Or33769OnewayType, Or33769Type } from '~/types/cmn/business/components/Or33769Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { ParamComfirm } from '~/components/custom-components/organisms/Or33456/Or33456.type'
import { OrX0008Const } from '~/components/custom-components/organisms/OrX0008/OrX0008.constants'
import { OrX0008Logic } from '~/components/custom-components/organisms/OrX0008/OrX0008.logic'
import { OrX0009Logic } from '~/components/custom-components/organisms/OrX0009/OrX0009.logic'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import type { PlanTargetPeriodDataType } from '~/types/cmn/PlanTargetPeriodDataType'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or51767Const } from '~/components/custom-components/organisms/Or51767/Or51767.constants'
import { Or51767Logic } from '~/components/custom-components/organisms/Or51767/Or51767.logic'
import type { Or51767Param } from '~/components/custom-components/organisms/Or51767/Or51767.type'
import type { Or05019TwoWayType } from '~/components/custom-components/organisms/Or05019/Or05019.type'
import { useJigyoList } from '~/utils/useJigyoList'
import { useUserListInfo } from '~/utils/useUserListInfo'
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or33456OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const orHeadLine = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const or10279 = ref({ uniqueCpId: Or10279Const.CP_ID(1) })
const orX0007 = ref({ uniqueCpId: '' })
const orX0008 = ref({ uniqueCpId: '' })
const orX0009 = ref({ uniqueCpId: '' })
const orX0010 = ref({ uniqueCpId: '' })
const gui00070 = ref({ uniqueCpId: '' })
const or05019 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const or33769 = ref({ uniqueCpId: '' })
const or51767 = ref({ uniqueCpId: '' })

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return (
    screenStore.getCpNavControl(or05019.value.uniqueCpId) ??
    screenStore.getCpNavControl(orX0009.value.uniqueCpId) ??
    screenStore.getCpNavControl(orX0010.value.uniqueCpId) ??
    local.isSoftDelete
  )
})
// 計画期間
const isUsePlanPeriod = ref<boolean>(true)
// お気に入りに該当機能
const favorite = ref<boolean>(false)

// 削除フラグ
const deleteFlag = ref<boolean>(false)

const or41179 = ref({ uniqueCpId: '' })
// ローカルTwoway
const local = reactive({
  // 作成日
  createDate: {
    value: systemCommonsStore.getSystemDate,
  } as Mo00020Type,
  // フラグ
  flag: {
    // 期間管理
    periodManage: '1',
  },
  isSoftDelete: false,
  updateKbn: '',
})
// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()
const { jigyoListWatch } = useJigyoList()

// ローカルOneway
const localOneway = reactive({
  //事業所
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 1, totalCount: 0 } } as OrX0007OnewayType,
  // 履歴
  orX0008Oneway: {
    screenID: 'GUI00872',
    officeId: '',
    sc1Id: '',
    useId: '',
    plan1Id: '',
    mode: '',
  } as OrX0008OnewayType,
  //作成者
  orX0009Oneway: {
    createData: {
      createDate: '',
    } as PlanCreateDataType,
  } as OrX0009OnewayType,
  //作成日
  orX0010Oneway: {} as OrX0010OnewayType,
  Or10279Model: {
    staffId: systemCommonsStore.getStaffId,
    baseDate: systemCommonsStore.getSystemDate,
    officeId: systemCommonsStore.getStaffId,
  } as Or10279OneWayType,
  or33769OnewayModel: {
    syubetsuId: '',
    shisetuId: '',
    userId: '',
    planPeriodFlg: '',
  } as Or33769OnewayType,
})

// e-文書法対象機能の電子ファイル保存設定区分

/**************************************************
 * ライフサイクルフック
 **************************************************/
// 画面状態管理用操作変数
const screenStore = useScreenStore()

// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
or05019.value.uniqueCpId = pageComponent.uniqueCpId
// ダイアログ表示フラグ
const showDialogOr33769 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or33769Logic.state.get(or33769.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr51767 = computed(() => {
  // Or51767のダイアログ開閉状態
  return Or51767Logic.state.get(or51767.value.uniqueCpId)?.isOpen ?? false
})
onMounted(async () => {
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or33456Const.STR_ALL })
  })
  // コントロール設定
  await getCommonData()
})
/**
 * 修正ポップアップ（Or21814）用のユニークCP ID
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * 修正ポップアップ（Or21813）用のユニークCP ID
 */
const or21813 = ref({ uniqueCpId: '' })
/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or05019Const.CP_ID(0)]: or05019.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [OrX0007Const.CP_ID(1)]: orX0007.value,
  [OrX0008Const.CP_ID(1)]: orX0008.value,
  [OrX0009Const.CP_ID(1)]: orX0009.value,
  [OrX0010Const.CP_ID(1)]: orX0010.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [Or21814Const.CP_ID(0)]: or21814.value, // 確認ダイアログ
  [Or21813Const.CP_ID(0)]: or21813.value, // 確認ダイアログ
  [Or33769Const.CP_ID(0)]: or33769.value,
  [Or51767Const.CP_ID(0)]: or51767.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
})
useScreenTwoWayBind<Or05019TwoWayType>({
  cpId: Or05019Const.CP_ID(0),
  uniqueCpId: or05019.value.uniqueCpId,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.consider-table'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: false,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
  },
})
/**
 * 削除確認ダイアログを表示する
 *
 * @param param - パラメータ
 */
const openPopupComfirm = (param: ParamComfirm) => {
  return new Promise((resolve) => {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        isOpen: true,
        dialogText: param.message,
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: param?.firstBtnLabel ?? t('btn.yes'),
        secondBtnType: param?.secondBtnType ?? 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnLabel: t('btn.cancel'),
        thirdBtnType: param?.thirdBtnType ?? 'blank',
      },
    })
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        if (event?.firstBtnClickFlg) {
          if (param.excuteFunction) {
            void param.excuteFunction()
          }
          resolve(true)
        }
        if (event?.secondBtnClickFlg) {
          if (param.excuteFunction1) {
            void param.excuteFunction1()
          }
        }
        if (event?.thirdBtnClickFlg || event?.closeBtnClickFlg) {
          if (param.excuteFunction2) {
            void param.excuteFunction2()
          }
        }
        resolve(false)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
      },
      { once: true }
    )
  })
}
/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      favoriteIconClick()
      setOr11871Event({ favoriteEventFlg: false })
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      _update()
      setOr11871Event({ saveEventFlg: false })
    }
    if (newValue.createEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 新規ボタンが押下された場合、新規作成処理を実行する
        void addBtnClick()
        setOr11871Event({ createEventFlg: false })
      }
    }
    if (newValue.printEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 印刷ボタンが押下された場合、印刷設定画面を表示する
        void printSettingIconClick()
        setOr11871Event({ printEventFlg: false })
      }
    }
  }
)

/**
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg
    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }
    const index = listPeriod.value.findIndex((item) => item.sc1Id === newValue.planTargetPeriodId)
    const isLast = index === listPeriod.value.length - 1
    const isFirst = index === 0
    const handleChangePreiod = async () => {
      const openErrMess = (message: string) => {
        const param: ParamComfirm = {
          message,
          firstBtnLabel: 'OK',
          secondBtnType: 'blank',
        }
        openPopupComfirm(param)
      }
      if (planUpdateFlg === '0') {
        handleLogicx0007(index)
      } else if (planUpdateFlg === '1') {
        if (isFirst) {
          openErrMess(t('message.i-cmn-11262'))
          return
        }
        handleLogicx0007(index - 1)
      } else if (planUpdateFlg === '2') {
        if (isLast) {
          openErrMess(t('message.i-cmn-11263'))
          return
        }
        handleLogicx0007(index + 1)
      }
      const isSuccess = await periodSelectHandle()
    }
    if (isEdit.value && planUpdateFlg !== '0') {
      if ((isLast && planUpdateFlg === '2') || (isFirst && planUpdateFlg === '1')) return
      const saveAndNext = async () => {
        await _update(false)
        handleChangePreiod()
      }
      const param: ParamComfirm = {
        message: t('message.i-ldg-10430'),
        excuteFunction1: handleChangePreiod,
        excuteFunction: saveAndNext,
        thirdBtnType: 'normal3',
      }
      openPopupComfirm(param)
    } else {
      handleChangePreiod()
    }
  },
  { deep: true }
)

/**
 * AC017_「履歴-前へアイコンボタン」押下
 * AC018_「履歴-次へアイコンボタン」押下
 * 履歴変更の監視
 */
watch(
  () => OrX0008Logic.data.get(orX0008.value.uniqueCpId),
  (newValue) => {
    if (isUndefined(newValue)) return

    const createId = String(newValue.createId)
    const createUpateFlg = newValue.createUpateFlg
    if (!createUpateFlg || (listHistory.value.length === 1 && !listHistory.value[0].rirekiId))
      return
    const index = listHistory.value.findIndex((item) => item.rirekiId === createId)
    const isLast = index === listHistory.value.length - 1
    const isFirst = index === 0
    const handleChangeHistory = () => {
      if (createUpateFlg === '0') {
        handleLogicx0008(index)
      } else if (createUpateFlg === '1') {
        if (isFirst) {
          return
        }
        handleLogicx0008(index - 1)
      } else if (createUpateFlg === '2') {
        if (isLast) {
          return
        }
        handleLogicx0008(index + 1)
      }
      historySelectHandle()
    }

    const isCreateMode = local.updateKbn === 'C'

    if (isCreateMode) {
      if (createUpateFlg === '2') return

      handleChangeHistory()
      listHistory.value.pop()

      localOneway.orX0008Oneway.createData = {
        ...localOneway.orX0008Oneway.createData,
        totalCount: listHistory.value.length,
      }

      local.updateKbn = ''
      return
    }

    if (isEdit.value && createUpateFlg !== '0') {
      if ((isLast && createUpateFlg === '2') || (isFirst && createUpateFlg === '1')) return
      const saveAndNext = async () => {
        await _update(false)
        handleChangeHistory()
      }

      const param: ParamComfirm = {
        message: t('message.i-ldg-10430'),
        excuteFunction1: handleChangeHistory,
        excuteFunction: saveAndNext,
        thirdBtnType: 'normal3',
      }
      openPopupComfirm(param)
    } else {
      handleChangeHistory()
    }
  },
  { deep: true }
)
/**************************************************
 * 関数
 **************************************************/

/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}
interface HistoryList extends Rireki, RirekiJoho {}
const listHistory = ref<HistoryList[]>([])
const listPeriod = ref<Taishokikan[]>([])
/**
 * 画面コントロール表示設定
 *
 * @param index - 画面コントロール表示設定
 */
const handleLogicx0007 = (index: number) => {
  localOneway.orX0007Oneway.planTargetPeriodData = {
    orX0115Oneway: {
      sc1Id: Number(listPeriod.value[index].sc1Id),
    },
    planTargetPeriodId: Number(listPeriod.value[index].sc1Id),
    planTargetPeriod: listPeriod.value[index]?.startYmd + ' ~ ' + listPeriod.value[index]?.endYmd,
    currentIndex: index + 1,
    totalCount: listPeriod.value.length,
  } as PlanTargetPeriodDataType
  OrX0007Logic.data.set({
    uniqueCpId: orX0007.value.uniqueCpId,
    value: {
      planTargetPeriodId: listPeriod.value[index].sc1Id,
      PlanTargetPeriodUpdateFlg: '',
    },
  })
}
const handleLogicx0008 = (index: number) => {
  localOneway.orX0008Oneway.createData = {
    createId: listHistory.value[index]?.rirekiId ?? '',
    createDate: listHistory.value[index]?.sakuseiYmd ?? '',
    staffId: listHistory.value[index]?.shokuinId ?? '',
    staffName: listHistory.value[index]?.shokuinKnj ?? '',
    currentIndex: index + 1,
    totalCount: listHistory.value.length,
  }
  localOneway.orX0008Oneway.plan1Id = listHistory.value[index]?.rirekiId ?? ''
  localOneway.orX0008Oneway.officeId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.orX0008Oneway.sc1Id =
    OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? ''
  localOneway.orX0008Oneway.useId = systemCommonsStore.getUserId ?? ''
  OrX0008Logic.data.set({
    uniqueCpId: orX0008.value.uniqueCpId,
    value: {
      createId: listHistory.value[index]?.rirekiId,
      createUpateFlg: '',
      rirekiObj: {
        createId: listHistory.value[index]?.rirekiId,
        createDate: listHistory.value[index]?.sakuseiYmd ?? '',
        staffId: listHistory.value[index]?.shokuinId ?? '',
        staffName: listHistory.value[index]?.shokuinKnj ?? '',
        currentIndex: index + 1,
        totalCount: listHistory.value.length,
      },
    },
    isInit: true,
  })
  OrX0009Logic.data.set({
    uniqueCpId: orX0009.value.uniqueCpId,
    value: {
      staffId: listHistory.value[index]?.shokuinId ?? '',
      staffName: listHistory.value[index]?.shokuinKnj ?? '',
    },
    isInit: true,
  })
}
const callbackUserChange = async (userId: string) => {
  systemCommonsStore.setUserId(userId)
  await getCommonData()
}
const isPerioidsEmpty = computed(() => listPeriod.value.length === 0)
const callbackFuncJigyo = (newJigyoId: string) => {
  systemCommonsStore.setSvJigyoId(newJigyoId)
  getCommonData()
}
// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)
// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange)
const isFirstInit = ref(true)
/**
 * 共通情報取得
 */

const getCommonData = async () => {
  const param: ConsiderInitialInfoSelectInEntity = {
    jigyoList: systemCommonsStore.getSvJigyoIdList.map((item) => ({ jigyoId: item })) ?? [],
    jigyoGpId: systemCommonsStore.getTekiyouGroupId ?? '',
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    shubetuId: systemCommonsStore.getSyubetu ?? '',
    appYmd: systemCommonsStore.getSystemDate ?? '',
    sysCd: systemCommonsStore.getSystemCode ?? '',
  }

  const res: ConsiderInitialInfoSelectOutEntity = await ScreenRepository.select(
    'considerInitialInfoSelect',
    param
  )
  if (isFirstInit.value) {
    const selectedSvJigyoId = res.data.tekiyoJigyoList.find(
      (item) => item.tekiyoFlg === '1'
    )?.svJigyoId
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        jigyoInfoList: res.data.tekiyoJigyoList.map((item) => ({
          ...item,
          jigyoRyakuKnj: item.jigyoNameKnj,
        })) as any,
      },
    })
    Or41179Logic.data.set({
      uniqueCpId: or41179.value.uniqueCpId,
      value: { modelValue: selectedSvJigyoId },
    })
  }
  if (res.data.kikanKanriFlg === '1') {
    isUsePlanPeriod.value = true
  } else {
    isUsePlanPeriod.value = false
  }
  if (res.data.taishokikanList?.length) {
    listPeriod.value = res.data.taishokikanList
    handleLogicx0007(res.data.taishokikanList.length - 1)
  } else {
    listPeriod.value = []
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = 0
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = 0
  }
  listHistory.value = res.data.rirekiJoho.map((item, index) => ({
    ...item,
    ...res.data.rirekiList[index],
  }))
  handleMapHistoryData(res, listHistory.value.length - 1)
  isFirstInit.value = false
}
const periodSelectHandle = async (RirekiId?: string) => {
  const param: ConsiderPeriodSelectInEntity = {
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    kikanFlg: isUsePlanPeriod.value ? '1' : '0',
    sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
    rirekiId: RirekiId ?? '0',
  }

  const res: ConsiderPeriodSelectOutEntity = await ScreenRepository.select(
    'considerPeriodSelect',
    param
  )
  if (res.statusCode === '200') {
    listHistory.value = res.data.rirekiJoho.map((item, index) => ({
      ...item,
      ...res.data.rirekiList[index],
    }))
    const indexHistory = listHistory.value.findIndex((item) => item.rirekiId === RirekiId)
    const fallbackIndex = indexHistory === -1 ? listHistory.value.length - 1 : indexHistory
    handleMapHistoryData(res, fallbackIndex)
    return true
  } else {
    return false
  }
}
const historySelectHandle = async () => {
  const param: ConsiderHistoryModifiedSelectInEntity = {
    sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
    rirekiID: OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId ?? '0',
    kikanKanriFlg: local.flag.periodManage,
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    shubetuId: systemCommonsStore.getSyubetu ?? '',
  }
  const res: ConsiderHistoryModifiedSelectOutEntity = await ScreenRepository.select(
    'considerHistoryModifiedSelect',
    param
  )
  handleMapDetailForm(res)
}
const handleMapHistoryData = (res: ConsiderHistoryModifiedSelectOutEntity, index: number) => {
  handleLogicx0008(index)
  handleMapDetailForm(res)
}
const handleMapDetailForm = async (res: ConsiderHistoryModifiedSelectOutEntity) => {
  local.isSoftDelete = false
  listHistory.value = res.data.rirekiJoho.map((item, index) => ({
    ...item,
    ...res.data.rirekiList[index],
  }))
  const selectedRireki = listHistory.value.find(
    (item) => item.rirekiId === OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId
  )
  localOneway.orX0009Oneway.createData = {
    createDate: selectedRireki?.sakuseiYmd ?? '',
    createId: Number(selectedRireki?.rirekiId),
    totalCount: listHistory.value.length,
    currentIndex: listHistory.value.indexOf(selectedRireki!),
    staffName: selectedRireki?.shokuinKnj ?? '',
    staffId: Number(selectedRireki?.shokuinId),
  }
  OrX0010Logic.data.set({
    uniqueCpId: orX0010.value.uniqueCpId,
    value: { value: selectedRireki?.sakuseiYmd ?? '', mo01343: {} as unknown as Mo01343Type },
    isInit: true,
  })
  if (listHistory.value.length === 0) {
    CpnHkd2List.value = uniqBy(
      Or05019Const.DEFAULT.defaultCpnHkd2,
      (item) => `${item.k1Cd}_${item.kd2Id}`
    ).map((item) => ({
      ...item,
      sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
    }))
    CpnHkd3List.value = uniqBy(
      Or05019Const.DEFAULT.defaultCpnHkd3,
      (item) => `${item.k2Cd}_${item.kd3Id}`
    ).map((item) => ({
      ...item,
      sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
    }))
    local.updateKbn = ''
    await nextTick()
    addBtnClick()
  } else {
    setDataContent(res.data.cpnHkd3, res.data.cpnHkd2)
  }
}

const CpnHkd3List = ref<CpnHkd3[]>([])
const CpnHkd2List = ref<CpnHkd2[]>([])
const setDataContent = (CpnHkd3Data: CpnHkd3[], CpnHkd2Data: CpnHkd2[], isInit = true) => {
  if (isInit) {
    CpnHkd2List.value = uniqBy(CpnHkd2Data, (item) => `${item.k1Cd}_${item.kd2Id}`)
    CpnHkd3List.value = uniqBy(CpnHkd3Data, (item) => `${item.k2Cd}_${item.kd3Id}`)
  }
  const or05019Data = Or05019Logic.data.get(or05019.value.uniqueCpId)
  const findItemWithK2Cd = (k2Cd: string) => {
    return CpnHkd3Data.find((item) => item.k2Cd === k2Cd)
  }
  const findItemWithK1Cd = (k1Cd: string) => {
    return CpnHkd2Data.find((item) => item.k1Cd === k1Cd)
  }
  if (or05019Data && CpnHkd3Data) {
    const data = cloneDeep(or05019Data)
    function applyUmu(k2Cd: string, si1: string, si2: string): void {
      const item = findItemWithK2Cd(k2Cd)
      data[si1].value.modelValue = item?.umu === '1'
      data[si2].value.modelValue = item?.umu === '0'
    }
    applyUmu('1', 'mainCaregiverExists', 'noMainCaregiver')
    applyUmu('8', 'hasSubCaregiver', 'hasNoSubCaregiver')
    applyUmu('2', 'careMotivationActive', 'careMotivationPassive')
    const k2Cd9 = findItemWithK2Cd('9')
    data.carePeriodYear.value.mo00045.value = k2Cd9?.kikanNen ?? ''
    data.carePeriodMonth.value.mo00045.value = k2Cd9?.kikanTuki ?? ''
    applyUmu('3', 'mainCaregiverHealthGood', 'mainCaregiverHealthPoor')
    applyUmu('10', 'mainCaregiverFamilyRelationGood', 'mainCaregiverFamilyRelationPoor')
    applyUmu('4', 'mainCaregiverEmploymentNone', 'mainCaregiverEmploymentExists')
    applyUmu('11', 'mainCaregiverChildcareNone', 'mainCaregiverChildcareExists')
    const k2Cd5 = findItemWithK2Cd('5')
    data.careAvailableTime.value.mo00045.value = k2Cd5?.jikan ?? ''
    applyUmu('12', 'physicalBurdenNone', 'physicalBurdenExists')
    applyUmu('6', 'mentalBurdenNone', 'mentalBurdenExists')
    applyUmu('13', 'financialBurdenNone', 'financialBurdenExists')
    const k2Cd7 = findItemWithK2Cd('7')
    data.visitFrequency.value.mo00045.value = k2Cd7?.kaisuu ?? ''
    data.visitFrequencyUnit.value = k2Cd7?.kankaku || ''
    const k2Cd14 = findItemWithK2Cd('14')
    data.outingOvernightFrequency.value.mo00045.value = k2Cd14?.kaisuu ?? ''
    data.outingOvernightFrequencyUnit.value = k2Cd14?.kankaku || ''
    const k1Cd1 = findItemWithK1Cd('1')
    data.careAbilitySpecialNotes.value.value = k1Cd1?.tokki || ''
    applyUmu('15', 'welfareEquipmentUseExists', 'welfareEquipmentUseNone')
    applyUmu('19', 'welfareEquipmentSufficiencyAdequate', 'welfareEquipmentSufficiencyInadequate')
    applyUmu('16', 'housingRenovationNeedNone', 'housingRenovationNeedRequired')
    applyUmu('17', 'housingOwnershipOwned', 'housingOwnershipRented')
    applyUmu('20', 'housingTypeDetached', 'housingTypeApartment')
    applyUmu('18', 'hasPrivateRoom', 'hasNoPrivateRoom')
    applyUmu('21', 'isFirstFloor', 'isSecondFloorOrAbove')
    const k1Cd2 = findItemWithK1Cd('2')
    data.livingEnvironmentSpecialNotes.value.value = k1Cd2?.tokki || ''
    function applyUmuSocialResources(k2Cd: string, si1: string, si2: string): void {
      const item = findItemWithK2Cd(k2Cd)
      data[si1].value.modelValue = item?.umu === '1'
      data[si2].value.modelValue = item?.umu === '2'
      if (item?.umu === '3') {
        data[si1].value.modelValue = true
        data[si2].value.modelValue = true
      }
    }
    applyUmuSocialResources('25', 'homeHelpServiceInLocalArea', 'homeHelpServiceWillingToUse')
    applyUmuSocialResources('33', 'welfareEquipmentRentalInLocalArea', 'welfareEquipmentRentalWillingToUse')
    applyUmuSocialResources('29', 'homeBathingServiceInLocalArea', 'homeBathingServiceWillingToUse')
    applyUmuSocialResources('32', 'shortStayCareFacilityInLocalArea', 'shortStayCareFacilityWillingToUse')
    applyUmuSocialResources('23', 'homeNursingServiceInLocalArea', 'homeNursingServiceWillingToUse')
    applyUmuSocialResources('31', 'shortStayRehabFacilityInLocalArea', 'shortStayRehabFacilityWillingToUse')
    applyUmuSocialResources('24', 'homeRehabilitationServiceInLocalArea', 'homeRehabilitationServiceWillingToUse')
    applyUmuSocialResources('30', 'dementiaCareGroupHomeInLocalArea', 'dementiaCareGroupHomeWillingToUse')
    applyUmuSocialResources('22', 'homeMedicalManagementInLocalArea', 'homeMedicalManagementWillingToUse')
    applyUmuSocialResources('34', 'specifiedFacilityResidentCareInLocalArea', 'specifiedFacilityResidentCareWillingToUse')
    applyUmuSocialResources('26', 'dayCareServiceInLocalArea', 'dayCareServiceWillingToUse')
    applyUmuSocialResources('52', 'welfareEquipmentPurchaseInLocalArea', 'welfareEquipmentPurchaseWillingToUse')
    applyUmuSocialResources('27', 'dayRehabilitationServiceInLocalArea', 'dayRehabilitationServiceWillingToUse')
    applyUmuSocialResources('28', 'housingRenovationInLocalArea', 'housingRenovationWillingToUse')
    applyUmuSocialResources('35', 'otherService1InLocalArea', 'otherService1WillingToUse')
    applyUmuSocialResources('36', 'elderlyWelfareFacilityInLocalArea', 'elderlyWelfareFacilityWillingToUse')
    applyUmuSocialResources('37', 'elderlyRehabFacilityInLocalArea', 'elderlyRehabFacilityWillingToUse')
    applyUmuSocialResources('38', 'medicalCareFacilityInLocalArea', 'medicalCareFacilityWillingToUse')
    applyUmuSocialResources('39', 'otherService2InLocalArea', 'otherService2WillingToUse')
    const k1Cd3 = findItemWithK1Cd('3')
    data.availableSocialResourcesSpecialNotes.value.value = k1Cd3?.tokki || ''
    applyUmu('40', 'hobbyExists', 'hobbyNone')
    applyUmu('43', 'seniorClubParticipationExists', 'seniorClubParticipationNone')
    applyUmu('41', 'walkingExists', 'walkingNone')
    applyUmu('44', 'shoppingExists', 'shoppingNone')
    applyUmu('42', 'socializingWithNeighborsFriendsExists', 'socializingWithNeighborsFriendsNone')
    applyUmu('45', 'otherActivitiesExists', 'otherActivitiesNone')
    const k1Cd4 = findItemWithK1Cd('4')
    data.socialParticipationSpecialNotes.value.value = k1Cd4?.tokki || ''
    applyUmu('46', 'attendingPhysicianExists', 'attendingPhysicianNone')
    applyUmu('48', 'hospitalAdmissionDestinationExists', 'hospitalAdmissionDestinationNone')
    applyUmu('47', 'familyCommunicationNetworkExists', 'familyCommunicationNetworkNone')
    const k1Cd5 = findItemWithK1Cd('5')
    data.emergencyResponseSpecialNotes.value.value = k1Cd5?.tokki || ''
    applyUmu('49', 'careMethodInstructionReceived', 'careMethodInstructionNotReceived')
    applyUmu('50', 'socialResourceUsageKnown', 'socialResourceUsageUnknown')
    applyUmu('51', 'socialResourceCostKnown', 'socialResourceCostUnknown')
    const k1Cd6 = findItemWithK1Cd('6')
    data.familyInstructionSpecialNotes.value.value = k1Cd6?.tokki || ''
    Or05019Logic.data.set({
      uniqueCpId: or05019.value.uniqueCpId,
      value: data,
      isInit,
    })
  }
}
/**
 * AC002_「お気に入りアイコンボタン」押下
 */
const favoriteIconClick = () => {
  // TODO お気に入りに該当機能がない場合
  if (favorite.value) {
    // 「アセスメント(インターライ)」をお気に入り機能に追加する。
  }
  // お気に入りに該当機能がある場合
  else {
    // 「アセスメント(インターライ)」をお気に入り機能から削除する。
  }
}
const mapDataCpnHkd2 = (item: CpnHkd2) => {
  const data = cloneDeep(Or05019Logic.data.get(or05019.value.uniqueCpId))!
  if (item.k1Cd === '1') {
    item.tokki = data.careAbilitySpecialNotes.value.value ?? ''
  }
  if (item.k1Cd === '2') {
    item.tokki = data.livingEnvironmentSpecialNotes.value.value ?? ''
  }
  if (item.k1Cd === '3') {
    item.tokki = data.availableSocialResourcesSpecialNotes.value.value ?? ''
  }
  if (item.k1Cd === '4') {
    item.tokki = data.socialParticipationSpecialNotes.value.value ?? ''
  }
  if (item.k1Cd === '5') {
    item.tokki = data.emergencyResponseSpecialNotes.value.value ?? ''
  }
  if (item.k1Cd === '6') {
    item.tokki = data.familyInstructionSpecialNotes.value.value ?? ''
  }
  return cloneDeep(item)
}
const mapDataCpnHkd3 = (item: CpnHkd3) => {
  const data = cloneDeep(Or05019Logic.data.get(or05019.value.uniqueCpId))!
  function mappingk2cd(si1: string, si2: string): void {
    if (data[si1].value.modelValue) {
      item.umu = '1'
    } else if (data[si2].value.modelValue) {
      item.umu = '0'
    } else {
      item.umu = '-1'
    }
  }
  function mappingk2cdType2(si1: string, si2: string): void {
    if (data[si1].value.modelValue && data[si2].value.modelValue) {
      item.umu = '3'
    } else if (data[si1].value.modelValue) {
      item.umu = '1'
    } else if (data[si2].value.modelValue) {
      item.umu = '2'
    } else {
      item.umu = '0'
    }
  }
  if (item.k2Cd === '1') {
    mappingk2cd('mainCaregiverExists', 'noMainCaregiver')
  }
  if (item.k2Cd === '8') {
    mappingk2cd('hasSubCaregiver', 'hasNoSubCaregiver')
  }
  if (item.k2Cd === '2') {
    mappingk2cd('careMotivationActive', 'careMotivationPassive')
  }
  if (item.k2Cd === '9') {
    item.kikanNen = data.carePeriodYear.value.mo00045.value
    item.kikanTuki = data.carePeriodMonth.value.mo00045.value
  }
  if (item.k2Cd === '3') {
    mappingk2cd('mainCaregiverHealthGood', 'mainCaregiverHealthPoor')
  }
  if (item.k2Cd === '10') {
    mappingk2cd('mainCaregiverFamilyRelationGood', 'mainCaregiverFamilyRelationPoor')
  }
  if (item.k2Cd === '4') {
    mappingk2cd('mainCaregiverEmploymentNone', 'mainCaregiverEmploymentExists')
  }
  if (item.k2Cd === '11') {
    mappingk2cd('mainCaregiverChildcareNone', 'mainCaregiverChildcareExists')
  }
  if (item.k2Cd === '5') {
    item.jikan = data.careAvailableTime.value.mo00045.value
  }
  if (item.k2Cd === '12') {
    mappingk2cd('physicalBurdenNone', 'physicalBurdenExists')
  }
  if (item.k2Cd === '6') {
    mappingk2cd('mentalBurdenNone', 'mentalBurdenExists')
  }
  if (item.k2Cd === '13') {
    mappingk2cd('financialBurdenNone', 'financialBurdenExists')
  }
  if (item.k2Cd === '7') {
    item.kaisuu = data.visitFrequency.value.mo00045.value
    item.kankaku = data.visitFrequencyUnit.value
  }
  if (item.k2Cd === '14') {
    item.kaisuu = data.outingOvernightFrequency.value.mo00045.value
    item.kankaku = data.outingOvernightFrequencyUnit.value
  }
  if (item.k2Cd === '15') {
    mappingk2cd('welfareEquipmentUseExists', 'welfareEquipmentUseNone')
  }
  if (item.k2Cd === '19') {
    mappingk2cd('welfareEquipmentSufficiencyAdequate', 'welfareEquipmentSufficiencyInadequate')
  }
  if (item.k2Cd === '16') {
    mappingk2cd('housingRenovationNeedNone', 'housingRenovationNeedRequired')
  }
  if (item.k2Cd === '17') {
    mappingk2cd('housingOwnershipOwned', 'housingOwnershipRented')
  }
  if (item.k2Cd === '20') {
    mappingk2cd('housingTypeDetached', 'housingTypeApartment')
  }
  if (item.k2Cd === '18') {
    mappingk2cd('hasPrivateRoom', 'hasNoPrivateRoom')
  }
  if (item.k2Cd === '21') {
    mappingk2cd('isFirstFloor', 'isSecondFloorOrAbove')
  }
  if (item.k2Cd === '25') {
    mappingk2cdType2('homeHelpServiceInLocalArea', 'homeHelpServiceWillingToUse')
  }
  if (item.k2Cd === '33') {
    mappingk2cdType2('welfareEquipmentRentalInLocalArea', 'welfareEquipmentRentalWillingToUse')
  }
  if (item.k2Cd === '29') {
    mappingk2cdType2('homeBathingServiceInLocalArea', 'homeBathingServiceWillingToUse')
  }
  if (item.k2Cd === '32') {
    mappingk2cdType2('shortStayCareFacilityInLocalArea', 'shortStayCareFacilityWillingToUse')
  }
  if (item.k2Cd === '23') {
    mappingk2cdType2('homeNursingServiceInLocalArea', 'homeNursingServiceWillingToUse')
  }
  if (item.k2Cd === '31') {
    mappingk2cdType2('shortStayRehabFacilityInLocalArea', 'shortStayRehabFacilityWillingToUse')
  }
  if (item.k2Cd === '24') {
    mappingk2cdType2('homeRehabilitationServiceInLocalArea', 'homeRehabilitationServiceWillingToUse')
  }
  if (item.k2Cd === '30') {
    mappingk2cdType2('dementiaCareGroupHomeInLocalArea', 'dementiaCareGroupHomeWillingToUse')
  }
  if (item.k2Cd === '22') {
    mappingk2cdType2('homeMedicalManagementInLocalArea', 'homeMedicalManagementWillingToUse')
  }
  if (item.k2Cd === '34') {
    mappingk2cdType2('specifiedFacilityResidentCareInLocalArea', 'specifiedFacilityResidentCareWillingToUse')
  }
  if (item.k2Cd === '26') {
    mappingk2cdType2('dayCareServiceInLocalArea', 'dayCareServiceWillingToUse')
  }
  if (item.k2Cd === '52') {
    mappingk2cdType2('welfareEquipmentPurchaseInLocalArea', 'welfareEquipmentPurchaseWillingToUse')
  }
  if (item.k2Cd === '27') {
    mappingk2cdType2('dayRehabilitationServiceInLocalArea', 'dayRehabilitationServiceWillingToUse')
  }
  if (item.k2Cd === '28') {
    mappingk2cdType2('housingRenovationInLocalArea', 'housingRenovationWillingToUse')
  }
  if (item.k2Cd === '35') {
    mappingk2cdType2('otherService1InLocalArea', 'otherService1WillingToUse')
  }
  if (item.k2Cd === '36') {
    mappingk2cdType2('elderlyWelfareFacilityInLocalArea', 'elderlyWelfareFacilityWillingToUse')
  }
  if (item.k2Cd === '37') {
    mappingk2cdType2('elderlyRehabFacilityInLocalArea', 'elderlyRehabFacilityWillingToUse')
  }
  if (item.k2Cd === '38') {
    mappingk2cdType2('medicalCareFacilityInLocalArea', 'medicalCareFacilityWillingToUse')
  }
  if (item.k2Cd === '39') {
    mappingk2cdType2('otherService2InLocalArea', 'otherService2WillingToUse')
  }
  if (item.k2Cd === '40') {
    mappingk2cd('hobbyExists', 'hobbyNone')
  }
  if (item.k2Cd === '43') {
    mappingk2cd('seniorClubParticipationExists', 'seniorClubParticipationNone')
  }
  if (item.k2Cd === '41') {
    mappingk2cd('walkingExists', 'walkingNone')
  }
  if (item.k2Cd === '44') {
    mappingk2cd('shoppingExists', 'shoppingNone')
  }
  if (item.k2Cd === '42') {
    mappingk2cd('socializingWithNeighborsFriendsExists', 'socializingWithNeighborsFriendsNone')
  }
  if (item.k2Cd === '45') {
    mappingk2cd('otherActivitiesExists', 'otherActivitiesNone')
  }
  if (item.k2Cd === '46') {
    mappingk2cd('attendingPhysicianExists', 'attendingPhysicianNone')
  }
  if (item.k2Cd === '48') {
    mappingk2cd('hospitalAdmissionDestinationExists', 'hospitalAdmissionDestinationNone')
  }
  if (item.k2Cd === '47') {
    mappingk2cd('familyCommunicationNetworkExists', 'familyCommunicationNetworkNone')
  }
  if (item.k2Cd === '49') {
    mappingk2cd('careMethodInstructionReceived', 'careMethodInstructionNotReceived')
  }
  if (item.k2Cd === '50') {
    mappingk2cd('socialResourceUsageKnown', 'socialResourceUsageUnknown')
  }
  if (item.k2Cd === '51') {
    mappingk2cd('socialResourceCostKnown', 'socialResourceCostUnknown')
  }
  return cloneDeep(item)
}
/**
 * AC003_「保存ボタン」押下
 *
 * @param isRunInit
 */
const _update = async (isRunInit = true) => {
  if (isPerioidsEmpty.value) return
  if (local.updateKbn !== 'C') {
    if (!isEdit.value && !local.isSoftDelete) {
      const param: ParamComfirm = {
        message: t('message.i-cmn-21800'),
        firstBtnLabel: t('OK'),
        secondBtnType: 'blank',
      }
      openPopupComfirm(param)
      return false
    }
    if (isEdit.value) {
      local.updateKbn = 'U'
    }
    if (local.isSoftDelete) {
      local.updateKbn = 'D'
    }
  } else if (local.isSoftDelete) {
    local.isSoftDelete = false
    return
  }
  let cpnHkd2 = CpnHkd2List.value.map((item) => mapDataCpnHkd2(item))
  let cpnHkd3 = CpnHkd3List.value.map((item) => mapDataCpnHkd3(item))
  if (!isUsePlanPeriod.value && local.updateKbn === 'C') {
    cpnHkd2 = cpnHkd2.map((item) => ({
      ...item,
      sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
    }))
    cpnHkd3 = cpnHkd3.map((item) => ({
      ...item,
      sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
    }))
  }
  const param: ConsiderInitialInfoDataUpdateInEntity = {
    kikanKanriFlg: isUsePlanPeriod.value,
    updateKbn: local.updateKbn,
    delFlg: local.updateKbn === 'D' ? '1' : '0',
    userid: systemCommonsStore.getUserId ?? '',
    jigyoId: systemCommonsStore.getSvJigyoId ?? '',
    kikanId: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
    rirekiId:
      local.updateKbn !== 'C'
        ? (OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId ?? '')
        : '',
    rirekiList: listHistory.value,
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetsuId: systemCommonsStore.getShisetuId ?? '',
    createYmd: OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? '',
    shokuId: OrX0009Logic.data.get(orX0009.value.uniqueCpId)?.staffId ?? '',
    cpnHkd2,
    cpnHkd3,
  }
  let currentIndexHistory

  const resData: ConsiderInitialInfoDataUpdateOutEntity = await ScreenRepository.update(
    'considerInitialInfoDataUpdate',
    param
  )
  if (resData.statusCode === '200') {
    CpnHkd2List.value = []
    CpnHkd3List.value = []
    if (local.updateKbn === 'D') {
      currentIndexHistory = listHistory.value.findIndex(
        (item) => item.rirekiId === OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId
      )
      listHistory.value.splice(currentIndexHistory, 1)
      if (currentIndexHistory === listHistory.value.length) {
        currentIndexHistory = listHistory.value.length - 1
      }
      handleLogicx0008(currentIndexHistory)
      historySelectHandle()
      local.isSoftDelete = false
    } else if (local.updateKbn === 'C' && isRunInit) {
      await periodSelectHandle(resData.data.rirekiId)
    } else if (local.updateKbn === 'U' && isRunInit) {
      await periodSelectHandle(resData.data.rirekiId)
    }
    local.updateKbn = ''
  }
}
/**
 * AC004_「新規ボタン」押下
 */
const addBtnClick = async () => {
  if (local.isSoftDelete || isPerioidsEmpty.value) return
  if (local.updateKbn === 'C') {
    const param1: ParamComfirm = {
      message: t('message.i-cmn-11265', [t('label.consider-table')]),
      secondBtnType: 'blank',
      firstBtnLabel: t('OK'),
    }
    openPopupComfirm(param1)
    return
  }
  const handleAdd = () => {
    local.updateKbn = 'C'
    Or05019Logic.data.set({
      uniqueCpId: or05019.value.uniqueCpId,
      value: cloneDeep(Or05019Const.DEFAULT.defaultData),
      isInit: true,
    })
    OrX0010Logic.data.set({
      uniqueCpId: orX0010.value.uniqueCpId,
      value: {
        value: systemCommonsStore.getSystemDate ?? '',
        mo01343: {} as unknown as Mo01343Type,
      },
      isInit: true,
    })
    if (!isUsePlanPeriod.value) {
      OrX0007Logic.data.set({
        uniqueCpId: orX0007.value.uniqueCpId,
        value: {
          planTargetPeriodId: listPeriod.value[listPeriod.value?.length - 1].sc1Id,
          PlanTargetPeriodUpdateFlg: '',
        },
      })
    }
    listHistory.value.push({
      kd1Id: '',
      sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
      createYmd: OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? '',
      shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      houjinId: systemCommonsStore.getHoujinId ?? '',
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
      userid: systemCommonsStore.getUserId ?? '',
      rirekiId: '',
      sakuseiYmd: '',
      shokuinId: '',
      shokuinKnj: '',
    })
    OrX0009Logic.data.set({
      uniqueCpId: orX0009.value.uniqueCpId,
      value: {
        staffId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
        staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
      },
      isInit: true,
    })
    localOneway.orX0009Oneway.createData = {
      createDate: systemCommonsStore.getSystemDate ?? '',
      createId: 0,
      totalCount: listHistory.value.length,
      currentIndex: listHistory.value.length,
      staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
      staffId: Number(systemCommonsStore.getCurrentUser.chkShokuId),
    }
    OrX0008Logic.data.set({
      uniqueCpId: orX0008.value.uniqueCpId,
      value: {
        createId: '',
        createUpateFlg: '',
      },
    })
    localOneway.orX0008Oneway.createData = {
      ...localOneway.orX0008Oneway.createData,
      currentIndex: listHistory.value.length,
      totalCount: listHistory.value.length,
      staffId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
    }
  }
  const handleSaveAndAdd = async () => {
    await _update()
    handleAdd()
  }
  if (isEdit.value) {
    const param: ParamComfirm = {
      excuteFunction: handleAdd,
      excuteFunction1: handleSaveAndAdd,
      message: t('message.i-cmn-10430'),
    }
    openPopupComfirm(param)
  } else {
    handleAdd()
  }
}

/**
 * AC005_「複写ボタン」押下
 */
const copyBtnClick = () => {
  if (local.isSoftDelete || isPerioidsEmpty.value) return
  or33769OnClick(local.flag.periodManage)
}

/**
 * AC006_「印刷設定アイコンボタン」押下
 */
const printSettingIconClick = async () => {
  if (local.isSoftDelete || isPerioidsEmpty.value) return
  const openPrint = () => {
    Or51767Logic.state.set({
      uniqueCpId: or51767.value.uniqueCpId,
      state: {
        isOpen: true,
        param: {
          shokuId: OrX0009Logic.data.get(orX0009.value.uniqueCpId)?.staffId ?? '',
          prtNo: '',
          svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
          shisetuId: systemCommonsStore.getShisetuId ?? '',
          tantoId: '',
          syubetsuId: systemCommonsStore.getSyubetu ?? '',
          sectionName: '',
          userId: systemCommonsStore.getUserId ?? '',
          assessmentId: OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId,
          svJigyoKnj: systemCommonsStore.getSvJigyoCd ?? '',
          processYmd: OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? '',
          parentUserIdSelectDataFlag: false,
          focusSettingInitial: [],
          selectedUserCounter: '',
        } as Or51767Param,
      },
    })
  }
  const handleSaveAndOpenPrint = async () => {
    await _update()
    openPrint()
  }
  if (isEdit.value) {
    const param: ParamComfirm = {
      excuteFunction: handleSaveAndOpenPrint,
      excuteFunction1: openPrint,
      message: t('message.i-cmn-10430'),
    }
    openPopupComfirm(param)
  } else {
    openPrint()
  }
}
const deleteClick = () => {
  if (isPerioidsEmpty.value) return
  const softDelete = () => {
    local.isSoftDelete = true
  }
  const param: ParamComfirm = {
    excuteFunction: softDelete,
    message: t('message.i-cmn-11326', [
      `[${OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? ''}]`,
      t('label.consider-table'),
    ]),
  }
  if (local.isSoftDelete) return
  openPopupComfirm(param)
}
const handleGetDataCopy = async (newValue: Or33769Type) => {
  const param: ConsiderHistoryModifiedSelectInEntity = {
    sc1Id: newValue.sc1Id ?? '',
    rirekiID: newValue.raiId ?? '0',
    kikanKanriFlg: local.flag.periodManage,
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    shubetuId: systemCommonsStore.getSyubetu ?? '',
  }
  const res: ConsiderHistoryModifiedSelectOutEntity = await ScreenRepository.select(
    'considerHistoryModifiedSelect',
    param
  )
  setDataContent(res.data.cpnHkd3, res.data.cpnHkd2, false)
}
function or33769OnClick(planPeriodFlg: string) {
  localOneway.or33769OnewayModel.planPeriodFlg = planPeriodFlg
  // Or00100のダイアログ開閉状態を更新する
  Or33769Logic.state.set({
    uniqueCpId: or33769.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const showContentDetail = computed(() => !local.isSoftDelete && !isPerioidsEmpty.value)
const showOrX0007 = computed(() => isUsePlanPeriod.value)
const showOrX0008 = computed(() => !isPerioidsEmpty.value)
const showOrX0009 = computed(() => !isPerioidsEmpty.value)
</script>

<template>
  <c-v-sheet class="view">
    <g-base-or11871
      v-bind="or11871"
      class="custom-header-bar"
    >
      <template #createItems>
        <c-v-list-item
          :title="t('btn.copy')"
          @click="copyBtnClick"
        />
      </template>
      <template #optionMenuItems>
        <c-v-list-item
          :title="
            t('label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-delete')
          "
          prepend-icon="delete"
          @click="deleteClick"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.delete-data')"
          />
        </c-v-list-item>
      </template>
    </g-base-or11871>
    <c-v-row
      no-gutters
      class="main-content"
    >
      <!-- ナビゲーションエリア -->
      <div
        cols="2"
        class="h-100 pl-5"
        style="width: 300px;"
      >
        <!-- 利用者選択一覧 -->
        <g-base-or00248 v-bind="or00248" />
      </div>
      <!-- コンテンツエリア -->
      <c-v-col
      cols="-1"
        class="main-right h-100"
      >
        <!-- 上段 -->
        <c-v-row
          no-gutters
          class="top"
        >
          <c-v-row no-gutters>
            <!-- 計画対象期間 -->
            <c-v-col cols="auto">
              <g-base-or-41179
                class="jigyo"
                v-bind="or41179"
              />
            </c-v-col>
            <c-v-col
              v-show="showOrX0007"
              cols="auto"
            >
              <g-custom-orX0007
                v-bind="orX0007"
                :is-edit="isEdit"
                :parent-method="_update"
                :oneway-model-value="localOneway.orX0007Oneway"
                :unique-cp-id="orX0007.uniqueCpId"
              />
            </c-v-col>
            <!-- 基準日 -->
            <c-v-col
              v-show="showOrX0009"
              :class="[local.isSoftDelete ? 'disabled-event' : '']"
              cols="auto ml-4"
            >
              <g-custom-or-x0010
                v-bind="orX0010"
                :oneway-model-value="{
                  ...localOneway.orX0010Oneway,
                  isDisabled: local.isSoftDelete,
                }"
                class="custom-required"
              />
            </c-v-col>
            <c-v-col
              v-show="showOrX0009"
              cols="auto ml-4"
              class="x0009"
            >
              <!-- TODO GUI00220 職員検索画面未作成 -->
              <g-custom-orX0009
                v-bind="orX0009"
                :class="[local.isSoftDelete ? 'disabled-event' : '']"
                :oneway-model-value="localOneway.orX0009Oneway"
                :unique-cp-id="orX0009.uniqueCpId"
              />
            </c-v-col>
            <!-- 履歴 -->
            <c-v-col
              v-show="showOrX0008"
              cols="auto ml-4"
            >
              <g-custom-orX0008
                v-bind="orX0008"
                :is-edit="isEdit"
                :parent-method="_update"
                :oneway-model-value="localOneway.orX0008Oneway"
                :unique-cp-id="orX0008.uniqueCpId"
              />
            </c-v-col>
            <!-- 作成者 -->
          </c-v-row>
        </c-v-row>
        <c-v-row no-gutters>
          <g-custom-or-05019
            v-show="showContentDetail"
            v-bind="or05019"
            class="consider"
          />
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <!-- メッセージ -->
  <g-base-or-21815 v-bind="or21815" />
  <g-custom-or-10279
    v-bind="or10279"
    :oneway-model-value="localOneway.Or10279Model"
  />
  <!-- GUI00070 対象期間画面 -->
  <g-custom-gui-00070 v-bind="gui00070" />
  <g-base-or21814 v-bind="or21814" />
  <g-base-or21813 v-bind="or21813" />
  <g-custom-or-33769
    v-if="showDialogOr33769"
    v-bind="or33769"
    :oneway-model-value="localOneway.or33769OnewayModel"
    @update:model-value="handleGetDataCopy"
  />
  <g-custom-or-51767
    v-if="showDialogOr51767"
    v-bind="or51767"
  />
</template>

<style scoped lang="scss">
:deep(.pl-4) {
  padding-left: 0px !important;
}

.view {
  display: flex;
  flex-direction: column;
  height: fit-content !important;
  background-color: rgb(var(--v-theme-background));
}

.main-content {
  .main-right {
    height: 100%;
    display: flex;
    flex-direction: column;
    .top {
      flex: 0 1 auto;
      padding-left: 20px;
      border-bottom: 1px solid #b4c5dc;
      padding-bottom: 16px;
      :deep(.v-sheet) {
        background-color: transparent !important;
      }
    }
    .footer {
      flex: 0 1 auto;
    }
  }
}

.createDateOuterClass {
  background: rgb(var(--v-theme-background));
}

.tabItems {
  border-top: thin solid rgb(var(--v-theme-form));
  margin-top: 8px;
}
.disabled-event {
  pointer-events: none;
}
.consider {
  padding-bottom: 50px;
  margin-left: 20px;
}
:deep(.jigyo) {
  flex-direction: column;
  position: relative;
  bottom: 3px;
  .v-input {
    width: 203px !important;
  }
  .v-field.v-field--appended {
    background-color: #fff;
  }
  .ma-2 {
    margin: 4px !important;
  }
}
.action-sticky {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
}
.custom-header-bar {
  position: sticky;
  top: 48px;
  background-color: rgb(var(--v-theme-background));
  z-index: 999;

  margin: 0px;
  min-height: 60px;

  :deep(> div > div > div:last-of-type) {
    display: flex;
    align-items: center;
  }

  :deep(.v-btn) {
    --v-btn-height: 32px;
    min-height: 32px !important;
  }

  :deep(.v-divider) {
    margin-top: 0px !important;
    margin-bottom: 0px !important;
    color: rgb(180, 197, 220);
    caret-color: rgb(180, 197, 220);
    --v-border-opacity: 1;
  }
}
.date-header {
  align-items: start;
}
:deep(.custom-required) {
  .item-label::after {
    content: '必須';
    color: #ed6809;
    font-size: 12px;
    padding-left: 4px;
  }
}
:deep(.x0009) {
  .shoku-label {
    width: 112px !important;
  }
}
</style>
