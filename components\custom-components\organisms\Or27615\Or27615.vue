<script setup lang="ts">
import { cloneDeep } from 'lodash'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or32917Const } from '../Or32917/Or32917.constants'
import { Or32917Logic } from '../Or32917/Or32917.logic'
import type { Or32917DataType } from '../Or32917/Or32917.type'
import { Or32918Const } from '../Or32918/Or32918.constants'
import { Or32918Logic } from '../Or32918/Or32918.logic'
import type { kghMocKrkFree2Info, Or32918Param, Or32918StateType } from '../Or32918/Or32918.type'
import { Or32919Const } from '../Or32919/Or32919.constants'
import { OrX0097Logic } from '../OrX0097/OrX0097.logic'
import { Or27615Const } from './Or27615.constants'
import { Or27615Logic } from './Or27615.logic'
import type { Or27615StateType } from './Or27615.type'
import {
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
} from '#imports'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  IssuesPlanningStyleSettingsMasterChangeSelectInEntity,
  IssuesPlanningStyleSettingsMasterChangeSelectOutEntity,
} from '~/repositories/cmn/entities/IssuesPlanningStyleSettingsMasterChangeSelectEntity'
import type {
  IssuesPlanningStyleSettingsMasterSelectInEntity,
  IssuesPlanningStyleSettingsMasterSelectOutEntity,
} from '~/repositories/cmn/entities/IssuesPlanningStyleSettingsMasterSelectEntity'
import type {
  IssuesPlanningStyleSettingsMasterUpdateInEntity,
  IssuesPlanningStyleSettingsMasterUpdateOutEntity,
  KghMocKrkFree2Info,
} from '~/repositories/cmn/entities/IssuesPlanningStyleSettingsMasterUpdateEntity'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Or32917OnewayType, Or32917Type } from '~/types/cmn/business/components/Or32917Type'
import type { Or32918OnewayType, Or32918Type } from '~/types/cmn/business/components/Or32918Type'
import type { Or32919OnewayType, Or32919Type } from '~/types/cmn/business/components/Or32919Type'
import type { OrX0118OnewayType } from '~/types/cmn/business/components/OrX0118Type'
import type { CustomClass } from '~/types/CustomClassType'

/**
 * Or27615:有機体:課題立案様式設定マスタコンテンツエリアタブ
 * GUI00904_課題立案様式設定マスタ
 *
 * @description
 * 課題立案様式設定マスタ
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// 週間計画パターンタイトル情報
const or32917Data = ref<Or32917OnewayType>({
  kghMocKrkFree1TitleInfoList: [],
})
const or32918Data = ref<Or32918OnewayType>({
  freeId: '',
  columnCount: '',
  kghMocKrkFree2InfoList: [],
  codeKbn: {
    firstCode: 0,
    secondCode: 0,
  },
  koteiKbn: '',
  youshikiKbn: '',
  useFlg: '',
  sumFlg: false,
})
const or32919Data = ref<Or32919OnewayType>({
  kghMocKrkFree2InfoList: [],
  rowCount: 0,
  fontSize: '',
  labelFlg: false,
  fontSizeFlg: false,
})
const local = reactive({
  or32917: {
    value: '',
  } as Or32917Type,
  or32918: {
    value: '',
  } as Or32918Type,
  or32919: {
    value: '',
  } as Or32919Type,

  mo01338Oneway1: {
    value: t('label.issues-planning-block-1'),
    customClass: {
      outerStyle: 'padding: 8px 0px 8px 8px',
    },
  } as Mo01338OnewayType,

  mo01338Oneway2: {
    value: t('label.issues-planning-block-2'),
    customClass: {
      outerStyle: 'padding: 8px 0px 8px 8px',
    },
  } as Mo01338OnewayType,

  // アイコンボタン
  mo00009Oneway: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Fourway: {
    value: t('label.category-number'),
  } as Mo01338OnewayType,
  svJigyoId: '',
  kinouKbn: '4',
  youshikiKbn: '',
  freeId: '',
})

//様式名セレクトフィールド
const or32917 = ref({ uniqueCpId: Or32917Const.CP_ID(0) })

//課題立案表一覧
const or32918 = ref({ uniqueCpId: '' })

//プレビュー一覧
const or32919 = ref({ uniqueCpId: Or32919Const.CP_ID(0) })

// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(1) })

const mo00024 = ref<Mo00024Type>({
  isOpen: Or27615Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or32918OnewayType>({
  cpId: Or27615Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

refValue.value = {
  freeId: '',
  columnCount: '',
  kghMocKrkFree2InfoList: [],
  codeKbn: {
    firstCode: 0,
    secondCode: 0,
  },
  koteiKbn: '',
  youshikiKbn: '',
  useFlg: '',
  sumFlg: false,
}

const { setState } = useScreenOneWayBind<Or27615StateType>({
  cpId: Or27615Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      local.svJigyoId = value?.svJigyoId ?? ''
      local.youshikiKbn = value?.youshikiKbn ?? ''
      switch (value?.executeFlag) {
        // 初期情報取得
        case 'getData':
          void init()
          break
        default:
          break
      }
      Or32917Logic.state.set({
        uniqueCpId: or32917.value.uniqueCpId,
        state: {
          editAuthorityFlg: value!.editFlg,
        },
      })
      Or32918Logic.state.set({
        uniqueCpId: or32918.value.uniqueCpId,
        state: {
          param: {
            executeFlag: value!.executeFlag,
          } as Or32918Param,
        },
      })
    },
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or27615Const.DEFAULT.IS_OPEN
    },
  },
})

useSetupChildProps(props.uniqueCpId, {
  [Or32918Const.CP_ID(0)]: or32918.value,
})

useScreenOneWayBind<Or32918StateType>({
  cpId: Or32918Const.CP_ID(0),
  uniqueCpId: or32918.value.uniqueCpId,
  onUpdate: {
    tableData: (value) => {
      debugger
      if (value && value.length !== 0) {
        void save(value)
      }
    },
  },
})

/**************************************************
 * ライフサイクルフック
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/
async function save(value: kghMocKrkFree2Info[]) {
  // 請求データ
  const requestParam: IssuesPlanningStyleSettingsMasterUpdateInEntity = {
    freeId: or32918Data.value.freeId,
    columnCount: or32918Data.value.columnCount,
    kghMocKrkFree2InfoList: value.map(
      (item) =>
        ({
          nameKnj: item.nameKnj.value,
          inputKbnUpdateFlag: item.inputKbnUpdateFlag ?? Or27615Const.DEFAULT.EMPTY,
          inputKbn: item.inputKbn.modelValue,
          rendouKbn: item.rendouKbn.modelValue,
          widthCnt: item.widthCnt.value?.toString(),
          modifiedCnt: item.modifiedCnt,
        }) as KghMocKrkFree2Info
    ),
  }
  Or32918Logic.state.set({
    uniqueCpId: or32918.value.uniqueCpId,
    state: {
      tableData: [],
    },
  })
  // 情報保存
  const res: IssuesPlanningStyleSettingsMasterUpdateOutEntity = await ScreenRepository.update(
    'issuesPlanningStyleSettingsMasterUpdate',
    requestParam
  )
  //AC010-3 課題立案表データリスト.入力值に変更がある場合、■エラーがあるの場合、以下のメッセージを表示e.cmn.40157
  if (
    res.statusCode === Or27615Const.DEFAULT.STATUS_CODE_SUCCESS_200 ||
    res.statusCode === ResBodyStatusCode.SUCCESS
  ) {
    if (Or27615Logic.state.get(props.uniqueCpId)?.param?.isClose) {
      setState({ isOpen: false })
    } else if (Or27615Logic.state.get(props.uniqueCpId)?.param?.isChangeTab) {
      OrX0097Logic.event.set({
        uniqueCpId: props.parentUniqueCpId,
        state: {
          isSave: true,
        },
      })
    }
    await reload(local.freeId)
  } else {
    showOr21813Msg(t('message.e-cmn-40157'))
  }
}

/**
 * 保存ボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21813Msg(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 *  refValueを更新する
 */
function setRefValue() {
  useScreenStore().setCpTwoWay({
    cpId: Or27615Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

//AC001 初期表示
const init = async () => {
  // 課題立案様式設定情報取得(IN)
  const inputData: IssuesPlanningStyleSettingsMasterSelectInEntity = {
    svJigyoId: local.svJigyoId,
    kinouKbn: local.kinouKbn,
    youshikiKbn: local.youshikiKbn,
  }
  // AC001-1 課題立案区分マスタ初期情報取得
  // ■課題立案様式設定の初期情報（課題立案様式設定情報（画面生成用データ））を取得する。
  // ■課題立案様式設定の初期情報（課題立案表データリスト）を取得する。
  // ■課題立案様式設定の初期情報（課題立案様式名情報リスト）を取得する。
  const res: IssuesPlanningStyleSettingsMasterSelectOutEntity = await ScreenRepository.select(
    'issuesPlanningStyleSettingsMasterSelect',
    inputData
  )

  if (
    res.statusCode === Or27615Const.DEFAULT.STATUS_CODE_SUCCESS_200 ||
    res.statusCode === ResBodyStatusCode.SUCCESS
  ) {
    if (or32917Data.value.kghMocKrkFree1TitleInfoList.length === 0) {
      res.data.kghMocKrkFree1TitleInfoList.forEach((item) => {
        or32917Data.value.kghMocKrkFree1TitleInfoList.push({
          free1Id: {
            modelValue: item.free1Id,
          },
          titleKnj: item.titleKnj,
          dmyTitleKnj: item.titleKnj,
        })
      })
    }

    local.freeId = res.data.kghMocKrkFree1TitleInfoList[0]?.free1Id
    // or32918有機体情報入力
    or32918Data.value.freeId = res.data.kghMocKrkFree1TitleInfoList[0]?.free1Id
    or32918Data.value.columnCount = res.data.kghMocKrkFree1Info.columnCount
    or32918Data.value.codeKbn = {
      firstCode: CmnMCdKbnId.M_CD_KBN_ID_KOMOKU_INPUT,
      secondCode: CmnMCdKbnId.M_CD_KBN_ID_LINKAGE_KOMOKU,
    }
    or32918Data.value.useFlg = res.data.useFlg
    if (or32918Data.value.kghMocKrkFree2InfoList.length === 0) {
      res.data.kghMocKrkFree2InfoList.forEach((item, index) => {
        if (index < (res.data.kghMocKrkFree1Info.columnCount as unknown as number)) {
          or32918Data.value.kghMocKrkFree2InfoList.push({
            itemNum: {
              value: t('label.lblItem') + (index + 1),
              unit: '',
              customClass: {
                itemStyle:
                  'background: rgb(var(--v-theme-black-50)); height: 32px; padding-left: 17px;padding-top: 8px',
              } as CustomClass,
              valueFontWeight: 'bold',
            },
            nameKnj: {
              value: item.nameKnj,
            },
            inputKbn: {
              modelValue: item.inputKbn,
            },
            rendouKbn: {
              modelValue: item.rendouKbn,
            },
            widthCnt: {
              value: item.widthCnt ? parseInt(item.widthCnt) : 0,
              unit: '',
            },
            modifiedCnt: item.modifiedCnt,
          })
        }
      })
    }
    refValue.value = cloneDeep(or32918Data.value)

    // refValueを更新する
    setRefValue()

    // or32919有機体情報入力
    or32919Data.value.rowCount = 3
    or32919Data.value.labelFlg = true
    or32919Data.value.fontSize = res.data.kghMocKrkFree1Info.fontSize
    if (or32919Data.value.kghMocKrkFree2InfoList.length === 0) {
      res.data.kghMocKrkFree2InfoList.forEach((item, index) => {
        if (index < (res.data.kghMocKrkFree1Info.columnCount as unknown as number)) {
          or32919Data.value.kghMocKrkFree2InfoList.push({
            nameKnj: {
              value: item.nameKnj,
              unit: '',
              customClass: {
                outerClass: 'mr-0',
              } as CustomClass,
              valueFontWeight: 'bold',
            },
            widthCnt: item.widthCnt,
            itemNum: t('label.lblItem') + (index + 1),
          })
        }
      })
    }
  }
}

// AC004 「様式名セレクトフィールド」選択
const reload = async (value: string) => {
  // 課題立案様式設定情報取得(IN)
  const inputData: IssuesPlanningStyleSettingsMasterChangeSelectInEntity = {
    free1Id: value,
  }
  // 課題立案様式設定初期情報取得(OUT)
  // ■課題立案様式設定の初期情報（課題立案様式設定情報（画面生成用データ））を取得する。
  // ■課題立案様式設定の初期情報（課題立案表データリスト）を取得する。
  // ■課題立案様式設定の初期情報（使用フラグ）を取得する。
  const res: IssuesPlanningStyleSettingsMasterChangeSelectOutEntity = await ScreenRepository.select(
    'issuesPlanningStyleSettingsMasterChangeSelect',
    inputData
  )

  if (
    res.statusCode === Or27615Const.DEFAULT.STATUS_CODE_SUCCESS_200 ||
    res.statusCode === ResBodyStatusCode.SUCCESS
  ) {
    or32918Data.value.kghMocKrkFree2InfoList = []
    or32919Data.value.kghMocKrkFree2InfoList = []

    local.freeId = value
    // or32918有機体情報入力
    or32918Data.value.freeId = value
    or32918Data.value.columnCount = res.data.kghMocKrkFree1Info.columnCount
    or32918Data.value.codeKbn = {
      firstCode: CmnMCdKbnId.M_CD_KBN_ID_KOMOKU_INPUT,
      secondCode: CmnMCdKbnId.M_CD_KBN_ID_LINKAGE_KOMOKU,
    }
    or32918Data.value.useFlg = res.data.useFlg
    res.data.kghMocKrkFree2InfoList.forEach((item, index) => {
      if (index < (res.data.kghMocKrkFree1Info.columnCount as unknown as number)) {
        or32918Data.value.kghMocKrkFree2InfoList.push({
          itemNum: {
            value: t('label.lblItem') + (index + 1),
            unit: '',
            customClass: {
              itemStyle:
                'background: rgb(var(--v-theme-black-50)); height: 32px; padding-left: 17px;padding-top: 8px',
            } as CustomClass,
            valueFontWeight: 'bold',
          },
          nameKnj: {
            value: item.nameKnj,
          },
          inputKbn: {
            modelValue: item.inputKbn,
          },
          rendouKbn: {
            modelValue: item.rendouKbn,
          },
          widthCnt: {
            value: item.widthCnt ? parseInt(item.widthCnt) : 0,
            unit: '',
          },
          modifiedCnt: item.modifiedCnt,
        })
      }
    })

    refValue.value = cloneDeep(or32918Data.value)

    // refValueを更新する
    setRefValue()

    // or32919有機体情報入力
    or32919Data.value.rowCount = 3
    or32919Data.value.labelFlg = true
    or32919Data.value.fontSize = res.data.kghMocKrkFree1Info.fontSize
    res.data.kghMocKrkFree2InfoList.forEach((item, index) => {
      if (index < (res.data.kghMocKrkFree1Info.columnCount as unknown as number)) {
        or32919Data.value.kghMocKrkFree2InfoList.push({
          nameKnj: {
            value: item.nameKnj,
            unit: '',
            customClass: {
              outerClass: 'mr-0',
            } as CustomClass,
            valueFontWeight: 'bold',
          },
          widthCnt: item.widthCnt,
          itemNum: t('label.lblItem') + (index + 1),
        })
      }
    })
  }
}

/**
 * 文字数アップデート
 *
 * @param newVal - アップデート用データ
 */
const handleUpdateValue = (newVal: OrX0118OnewayType) => {
  const nameMap1 = new Map(
    refValue.value?.kghMocKrkFree2InfoList.map((item) => [item.nameKnj.value, item])
  )

  const nameMap2 = new Map(
    or32919Data.value.kghMocKrkFree2InfoList.map((item) => [item.nameKnj.value, item])
  )
  newVal.list?.forEach((targetItem) => {
    const sourceItem1 = nameMap1.get(targetItem.nameKnj)
    if (sourceItem1) {
      sourceItem1.widthCnt.value = targetItem.widthCnt
    }
    const sourceItem2 = nameMap2.get(targetItem.nameKnj)
    if (sourceItem2) {
      sourceItem2.widthCnt = targetItem.widthCnt.toString()
    }
  })
}

/**
 * タイトルアップデート
 *
 * @param newVal - アップデート用データ
 */
const handleUpdateTitle = (newVal: kghMocKrkFree2Info) => {
  const nameMap = new Map(
    or32919Data.value.kghMocKrkFree2InfoList.map((item) => [item.itemNum, item])
  )
  const sourceItem = nameMap.get(newVal.itemNum.value)
  if (sourceItem) {
    sourceItem.nameKnj.value = newVal.nameKnj.value
  }
}

// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 様式名セレクトフィールド選択データを監視
 *
 * @param newVal - アップデート用データ
 */
const handleUpdateStatus = async (newVal: Or32917DataType) => {
  local.freeId = newVal.value ?? ''
  debugger
  if (newVal.status === 'yes') {
    Or32918Logic.state.set({
      uniqueCpId: or32918.value.uniqueCpId,
      state: {
        param: {
          executeFlag: 'save',
        } as Or32918Param,
      },
    })
  } else {
    await reload(local.freeId)
  }
}
</script>

<template>
  <g-custom-or-32917
    v-bind="or32917"
    v-model="local.or32917"
    :oneway-model-value="or32917Data"
    :parent-unique-cp-id="props.uniqueCpId"
    @update:model-value="handleUpdateStatus"
  />
  <c-v-divider class="divider"></c-v-divider>
  <!-- 分子：ラベル -->
  <base-mo01338 :oneway-model-value="local.mo01338Oneway1" />
  <g-custom-or-32918
    v-bind="or32918"
    v-model="local.or32918"
    :oneway-model-value="refValue"
    :parent-unique-cp-id="props.uniqueCpId"
    @update:model-value="handleUpdateValue"
    @update:title="handleUpdateTitle"
  />
  <c-v-divider class="divider"></c-v-divider>
  <!-- 分子：ラベル -->
  <base-mo01338 :oneway-model-value="local.mo01338Oneway2" />
  <g-custom-or-32919
    v-bind="or32919"
    v-model="local.or32919"
    :oneway-model-value="or32919Data"
    :parent-unique-cp-id="props.uniqueCpId"
    :style="{
      marginRight: '16px',
    }"
  />
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
.content-head {
  margin-bottom: 8px;
}
.head-btn-margin {
  margin-left: 150px !important;
}
.divider {
  margin-top: 10px;
}
</style>
