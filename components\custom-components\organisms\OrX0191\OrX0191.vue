<script setup lang="ts">
/**
 * OrX0191：有機体：（確定版）家族構成／連絡先について
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or55476Logic } from '../Or55476/Or55476.logic'
import type { Or55476OneWayType, RelatedPersonSelectResInfo } from '../Or55476/Or55476.type'
import { Or55476Const } from '../Or55476/Or55476.constants'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import { OrX0157Const } from '../OrX0157/OrX0157.constants'
import type { CodeType, OrX0191OneWayType, OrX0191ValuesType } from './OrX0191.Type'
import { OrX0191Const } from './OrX0191.constants'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { KinshipInfo } from '~/repositories/cmn/entities/HospitalizationTimeInfoOfferPeriodSelectServiceEntity'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
import type { Mo01267OnewayType } from '~/types/business/components/Mo01267Type'

/**************************************************
 * Props
 **************************************************/
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()
const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル
const or55476 = ref({ uniqueCpId: '' })
const { refValue } = useScreenTwoWayBind<OrX0191ValuesType>({
  cpId: OrX0191Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<OrX0191ValuesType> }
useScreenOneWayBind<OrX0191OneWayType>({
  cpId: OrX0191Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value as Record<string, CodeType[]>
    },
  },
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or55476Const.CP_ID(1)]: or55476.value,
})
/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  commonInfo: {} as TeX0012Type,
  or51775: { modelValue: '' } as Or51775Type,
  or51775Value: '',
  or55476Value: '',
})

const localOneway = reactive({
  codeListOneway: {} as Record<string, CodeType[]>,
  mo01267Oneway: {
    btnLabel: t('label.primary_caregiver_name_label'),
    to: '',
  } as Mo01267OnewayType,
  mo01267Oneway2: {
    btnLabel: t('label.decision_supporter_label'),
    to: '',
  } as Mo01267OnewayType,
  orX0157Oneway: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: false,
        width: '381px',
        maxlength: '36',
      },
    },
  },
  // GUI00937 共通入力支援画面
  or51775Oneway: {
    screenId: OrX0191Const.DEFAULT.GUI,
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: OrX0191Const.DEFAULT.ASSESS_MENT_METHOD, // 共通情報.アセスメント方式
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  or55476Oneway: {
    userId: local.commonInfo.userId ?? '',
    telCellFlg: OrX0191Const.DEFAULT.VALUE_1,
  } as Or55476OneWayType,
  mo00045Oneway1: {
    maxlength: '41',
    width: '231px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway2: {
    maxlength: '41',
    width: '193px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Oneway: {
    valueFontWeight: 'blod',
    value: t('label.family_structure_contact_label'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
      outerClass: 'content-title',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338Oneway2: {
    valueFontWeight: 'blod',
    value: t('label.communication-of-intent'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
      outerClass: 'content-title',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039Oneway: {
    itemLabel: '',
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,

  // 文字数入力
  mo00038Oneway1: {
    mo00045Oneway: {
      appendLabel: t('label.primary_caregiver_years_label'),
      showItemLabel: false,
      maxLength: '3',
      width: '100px',
      customClass: new CustomClass({
        outerClass: 'ml-2',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 999,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  mo00040Oneway1: {
    itemTitle: 'zcode',
    itemValue: 'zokugaraKnj',
    showItemLabel: false,
    itemLabelFontWeight: 'bold',
    width: '242px',
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,

  mo00030Oneway: {
    mo00045Oneway: {
      maxlength: '14',
      width: '193px',
      isVerticalLabel: false,
      showItemLabel: false,
    },
    mode: OrX0191Const.DEFAULT.VALUE_1,
  },
  communicationDataList: [
    {
      label: t('label.conversation-support'),
      key: 'ishisotuu1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.complex-conversation'),
      key: 'ishisotuu2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.cant-chat-normally-but-can-convey-specific-needs'),
      key: 'ishisotuu3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.cant-converse-but-can-communicate'),
      key: 'ishisotuu4',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.no-verbal-communication-silent'),
      key: 'ishisotuu5',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
})

/**
 *  ボタン押下時の処理
 *
 * @param key -key
 */
function onClickOr55476(key: string) {
  local.or55476Value = key
  Or55476Logic.state.set({
    uniqueCpId: or55476.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const handleOr55476 = (info: RelatedPersonSelectResInfo) => {
  console.log(info, '1111111111111')
  const data = ((localOneway.codeListOneway.kinshipInfoList as KinshipInfo[]) ?? []).find(
    (item) => item.id === info.id
  )
  switch (local.or55476Value) {
    case 'kaigoMainKnj':
      refValue.value.orX0191Values.kaigoMainKnj.value = info.nameKnj
      refValue.value.orX0191Values.kaigoMainZcode.modelValue = info.zcode
      handleInit()
      refValue.value.orX0191Values.kaigoMainAge.mo00045.value = info.age ?? ''
      refValue.value.orX0191Values.kaigoMainTel.value = info.tel ?? ''
      if (data) {
        refValue.value.orX0191Values.kaigoMainKousei =
          data.kankei2Kbn === OrX0191Const.DEFAULT.VALUE_1
            ? OrX0191Const.DEFAULT.VALUE_1
            : OrX0191Const.DEFAULT.VALUE_0
        if (!info.age) {
          refValue.value.orX0191Values.kaigoMainAge.mo00045.value = data.age ?? ''
        }
      } else {
        refValue.value.orX0191Values.kaigoMainKousei = OrX0191Const.DEFAULT.VALUE_1
      }
      break
    case 'decisionSupporterKnj':
      refValue.value.orX0191Values.decisionSupporterKnj.value = info.nameKnj
      refValue.value.orX0191Values.decisionSupporterZcode.modelValue = info.zcode
      handleInit()
      refValue.value.orX0191Values.decisionSupporterAge.mo00045.value = info.age ?? ''
      refValue.value.orX0191Values.decisionSupporterTel.value = info.tel ?? ''
      if (data) {
        refValue.value.orX0191Values.decisionSupporterKousei =
          data.kankei2Kbn === OrX0191Const.DEFAULT.VALUE_1
            ? OrX0191Const.DEFAULT.VALUE_1
            : OrX0191Const.DEFAULT.VALUE_0
        if (!info.age) {
          refValue.value.orX0191Values.decisionSupporterAge.mo00045.value = data.age ?? ''
        }
      } else {
        refValue.value.orX0191Values.decisionSupporterKousei = OrX0191Const.DEFAULT.VALUE_1
      }
      break
  }
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === OrX0191Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === OrX0191Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  ;(refValue.value.orX0191Values[local.or51775Value] as unknown as Mo00045Type).value =
    setOrAppendValue(
      (refValue.value.orX0191Values[local.or51775Value] as unknown as Mo00045Type).value ?? '',
      data
    )
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

const handleInit = () => {
  if (
    !localOneway.codeListOneway.relationshipMasterInfoList?.find(
      (item) => item.zcode === refValue.value.orX0191Values.kaigoMainZcode?.modelValue
    )
  ) {
    refValue.value.orX0191Values.kaigoMainZcode.modelValue = undefined
  }
  if (
    !localOneway.codeListOneway.relationshipMasterInfoList?.find(
      (item) => item.zcode === refValue.value.orX0191Values.decisionSupporterZcode?.modelValue
    )
  ) {
    refValue.value.orX0191Values.decisionSupporterZcode.modelValue = undefined
  }
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr55476 = computed(() => {
  // Or55476のダイアログ開閉状態
  return Or55476Logic.state.get(or55476.value.uniqueCpId)?.isOpen ?? false
})
watch(
  () => localOneway.codeListOneway,
  (newVal) => {
    localOneway.mo00040Oneway1.items = newVal.relationshipMasterInfoList
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0012Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    if (newValue?.teikyouId) {
      local.commonInfo = newValue
      localOneway.mo01267Oneway.disabled =
        local.commonInfo.kinshipAuthorityFlag === OrX0191Const.DEFAULT.VALUE_0
      localOneway.mo01267Oneway2.disabled =
        local.commonInfo.kinshipAuthorityFlag === OrX0191Const.DEFAULT.VALUE_0
      localOneway.or55476Oneway.createYmd = newValue.createYmd
      localOneway.or51775Oneway.userId = newValue.userId ?? ''
    }
  },
  { deep: true }
)
</script>

<template>
  <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
  <div
    v-if="refValue.orX0191Values"
    class="box"
  >
    <!--主介護者氏名-->
    <div class="title">
      <base-mo01267
        :oneway-model-value="localOneway.mo01267Oneway"
        @click="onClickOr55476('kaigoMainKnj')"
      />
    </div>
    <div class="mt-6 mb-6">
      <div class="d-flex data-cell">
        <div>
          <p>{{ t('label.name') }}</p>
          <div class="d-flex">
            <base-mo00045
              v-model="refValue.orX0191Values.kaigoMainKnj"
              :oneway-model-value="localOneway.mo00045Oneway1"
            />
          </div>
        </div>
        <div class="ml-12">
          <p>{{ t('label.primary_caregiver_name_relationship_age_label') }}</p>
          <div class="d-flex">
            <base-mo00040
              v-model="refValue.orX0191Values.kaigoMainZcode"
              :oneway-model-value="localOneway.mo00040Oneway1"
            />
            <base-mo00038
              v-model="refValue.orX0191Values.kaigoMainAge"
              :oneway-model-value="localOneway.mo00038Oneway1"
            ></base-mo00038>
          </div>
        </div>
      </div>
      <div class="d-flex">
        <div class="mr-12">
          <p>{{ t('label.cohabitation_separation_label') }}</p>
          <div>
            <base-mo00039
              v-model="refValue.orX0191Values.kaigoMainKousei"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway
                  .CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION"
                :key="index"
                :name="'radio' + '-' + index"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </div>
        </div>
        <div>
          <p>{{ t('label.tel-label') }}</p>
          <div class="d-flex">
            <base-mo00045
              v-model="refValue.orX0191Values.kaigoMainTel"
              :oneway-model-value="localOneway.mo00045Oneway2"
            />
          </div>
        </div>
      </div>
    </div>
    <!--意思決定支援者（代諾者）-->
    <div class="title">
      <base-mo01267
        :oneway-model-value="localOneway.mo01267Oneway2"
        @click="onClickOr55476('decisionSupporterKnj')"
      />
    </div>
    <div class="mt-6 mb-6">
      <div class="d-flex data-cell">
        <div>
          <p>{{ t('label.name') }}</p>
          <div class="d-flex">
            <base-mo00045
              v-model="refValue.orX0191Values.decisionSupporterKnj"
              :oneway-model-value="localOneway.mo00045Oneway1"
            />
          </div>
        </div>
        <div class="ml-12">
          <p>{{ t('label.primary_caregiver_name_relationship_age_label') }}</p>
          <div class="d-flex">
            <base-mo00040
              v-model="refValue.orX0191Values.decisionSupporterZcode"
              :oneway-model-value="localOneway.mo00040Oneway1"
            />
            <base-mo00038
              v-model="refValue.orX0191Values.decisionSupporterAge"
              :oneway-model-value="localOneway.mo00038Oneway1"
            ></base-mo00038>
          </div>
        </div>
      </div>
      <div class="d-flex">
        <div class="mr-12">
          <p>{{ t('label.cohabitation_separation_label') }}</p>
          <div>
            <base-mo00039
              v-model="refValue.orX0191Values.decisionSupporterKousei"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway
                  .CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION"
                :key="index"
                :name="'radio' + '-' + index"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </div>
        </div>
        <div>
          <p>{{ t('label.tel-label') }}</p>
          <div class="d-flex">
            <base-mo00045
              v-model="refValue.orX0191Values.decisionSupporterTel"
              :oneway-model-value="localOneway.mo00045Oneway2"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- ３．意思疎通について  -->
  <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway2"></base-mo01338>
  <div
    v-if="refValue.orX0191Values"
    class="box"
  >
    <div class="mt-6 mb-6">
      <div class="data-cell d-flex flex-column">
        <div class="mb-6">
          <p>{{ t('label.eyesight') }}</p>
          <base-mo00039
            v-model="refValue.orX0191Values.ishisotuuShiryoku"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_CODE"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </div>
        <div class="mb-6">
          <p>{{ t('label.hearing_power') }}</p>
          <base-mo00039
            v-model="refValue.orX0191Values.ishisotuuChouryoku"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_CODE"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </div>
        <div class="mb-6">
          <p>{{ t('label.glasses_label') }}</p>
          <base-mo00039
            v-model="refValue.orX0191Values.ishisotuuMegane"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </div>
        <div>
          <p>{{ t('label.hearing_aid') }}</p>
          <base-mo00039
            v-model="refValue.orX0191Values.ishisotuuHochouki"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </div>
      </div>
      <!-- 意思疎通 -->
      <div>
        <p>{{ t('label.communication') }}</p>
        <div class="d-flex flex-wrap">
          <base-mo00018
            v-for="(item, index) in localOneway.communicationDataList"
            :key="index"
            v-model="refValue.orX0191Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: false,
              itemLabel: '',
              checkboxLabel: item.label,
            }"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="local.or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleOr51775Confirm"
  />
  <g-custom-or-55476
    v-if="showDialogOr55476"
    v-bind="or55476"
    :oneway-model-value="localOneway.or55476Oneway"
    @update:model-value="handleOr55476"
  />
</template>

<style scoped lang="scss">
.content-title {
  padding: 11px 24px;
  background-color: #e6e6e6;
  height: 48px;
}
.title {
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 48px;
  background-color: #0760e614;
  font-weight: Bold;
  font-size: 14px;
  :deep(.v-btn__content) {
    font-weight: bold;
    color: #214d97; /* 文字颜色 */
  }
}
.box {
  margin: 24px 50px 24px 48px;
}
:deep(.v-selection-control-group) {
  align-items: center !important;
}
:deep(.v-row--no-gutters) {
  margin: 0 !important;
}
.data-cell {
  margin-bottom: 16px;
}

:deep(.v-btn--density-default) {
  height: 100% !important;
  min-height: 0 !important;
}
:deep(.v-input__append) {
  margin-inline-start: 4px !important ;
}
:deep(.input-container) {
  .v-field {
    width: 48px !important;
  }
}
</style>
