/**
 * GUI01235_評価表
 *
 * @description
 * 評価表
 *
 * <AUTHOR>
 */

import { Or13850Logic } from '../../organisms/Or13850/Or13850.logic'
import { Or13850Const } from '../../organisms/Or13850/Or13850.constants'
import { Or13844Const } from '../../organisms/Or13844/Or13844.constants'
import { Or13844Logic } from '../../organisms/Or13844/Or13844.logic'
import { Or13872Const } from '../../organisms/Or13872/Or13872.constants'
import { Or13872Logic } from '../../organisms/Or13872/Or13872.logic'
import { OrX0007Const } from '../../organisms/OrX0007/OrX0007.constants'
import { OrX0006Const } from '../../organisms/OrX0006/OrX0006.constants'
import { Or51548Const } from '../../organisms/Or51548/Or51548.constants'
import { Or53098Const } from '../../organisms/Or53098/Or53098.constants'
import { Or51548Logic } from '../../organisms/Or51548/Or51548.logic'
import { OrX0006Logic } from '../../organisms/OrX0006/OrX0006.logic'
import { OrX0007Logic } from '../../organisms/OrX0007/OrX0007.logic'
import { Or54951Const } from '../../organisms/Or54951/Or54951.constants'
import { Or54951Logic } from '../../organisms/Or54951/Or54951.logic'
import { Or35345Const } from '../../organisms/Or35345/Or35345.constants'
import { Or35345Logic } from '../../organisms/Or35345/Or35345.logic'
import { OrX0115Const } from '../../organisms/OrX0115/OrX0115.constants'
import { OrX0115Logic } from '../../organisms/OrX0115/OrX0115.logic'
import { TeX0013Const } from './TeX0013.constants'
import { useEventStatusAccessor, useInitialize, useTwoWayBindAccessor } from '#imports'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'

import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { TeX0013EventType, TeX0013Type } from '~/types/cmn/business/components/TeX0013Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'

export namespace TeX0013Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: TeX0013Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {} as TeX0013Type,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or11871Const.CP_ID },
        { cpId: Or00248Const.CP_ID(1) },
        { cpId: Or13850Const.CP_ID(1) },
        { cpId: Or13850Const.CP_ID(2) },
        { cpId: Or13844Const.CP_ID(0) },
        { cpId: Or13872Const.CP_ID(0) },
        { cpId: OrX0007Const.CP_ID(1) },
        { cpId: OrX0006Const.CP_ID(1) },
        { cpId: Or41179Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(2) },
        { cpId: Or21814Const.CP_ID(3) },
        { cpId: Or21814Const.CP_ID(4) },
        { cpId: Or51548Const.CP_ID(1) },
        { cpId: Or53098Const.CP_ID(1) },
        { cpId: Or54951Const.CP_ID(1) },
        { cpId: Or35345Const.CP_ID(1) },
        { cpId: OrX0115Const.CP_ID(1) },
        { cpId: Or21813Const.CP_ID(0) },
      ],

      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    // Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(1)].uniqueCpId)
    Or13850Logic.initialize(childCpIds[Or13850Const.CP_ID(1)].uniqueCpId)
    Or13850Logic.initialize(childCpIds[Or13850Const.CP_ID(2)].uniqueCpId)
    Or13844Logic.initialize(childCpIds[Or13844Const.CP_ID(0)].uniqueCpId)
    Or13872Logic.initialize(childCpIds[Or13872Const.CP_ID(0)].uniqueCpId)
    OrX0007Logic.initialize(childCpIds[OrX0007Const.CP_ID(1)].uniqueCpId)
    OrX0006Logic.initialize(childCpIds[OrX0006Const.CP_ID(1)].uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(2)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(3)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(4)].uniqueCpId)
    Or51548Logic.initialize(childCpIds[Or51548Const.CP_ID(1)].uniqueCpId)
    Or54951Logic.initialize(childCpIds[Or54951Const.CP_ID(1)].uniqueCpId)
    Or35345Logic.initialize(childCpIds[Or35345Const.CP_ID(1)].uniqueCpId)
    OrX0115Logic.initialize(childCpIds[OrX0115Const.CP_ID(1)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<TeX0013Type>(TeX0013Const.CP_ID(0))

  /**
   * 自身のEventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useEventStatusAccessor<TeX0013EventType>(TeX0013Const.CP_ID(0))
}
