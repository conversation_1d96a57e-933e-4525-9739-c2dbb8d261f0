<script setup lang="ts">
/**
 * Or27564:有機体:モニタリングタイトルマスタ
 * GUI01221_モニタリングタイトルマスタ画面
 *
 * @description
 * モニタリングタイトルマスタ
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or10659Const } from '~/components/custom-components/organisms/Or10659/Or10659.constants'
import { Or10659Logic } from '~/components/custom-components/organisms/Or10659/Or10659.logic'
import type { Or10659OneWayType } from '~/components/custom-components/organisms/Or10659/Or10659.type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 * 高金康
 2025/05/16 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI01219'
// ルーティング
const routing = 'GUI01219/pinia'
// 画面物理名
const screenName = 'GUI01219'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or10659 = ref({ uniqueCpId: '' })

const mo00039OneWay = ref({
  name: '',
  showItemLabel: false,
  inline: false,
  items: [
    {
      label: 'GUI01221_モニタリングタイトルマスタ',
      value: '0',
    },
  ],
} as Mo00039OnewayType)

const mo00039Type = ref<string>('0')

const mo00046OneWayType = ref({
  showItemLabel: false,
} as Mo00046OnewayType)

const or10659Oneway = ref({
  /**
   * 編集権限
   */
  editFlg: true,
  /**
   * システム略称
   */
  systemName: '',
  /**
   * 様式フィルタ
   */
  moniFilt: '',
  /**
   * 事業所ID
   */
  svJigyoId: '1',
  /**
   * 様式区分
   */
  yoshikiKbn: '1',
} as Or10659OneWayType)

const info = ref<{ value: string }>({
  value: JSON.stringify(or10659Oneway.value, null, 2),
})
/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01219' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01219',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or10659Const.CP_ID(1) }],
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or10659Const.CP_ID(1)]: or10659.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or10659Logic.initialize(or10659.value.uniqueCpId)
}

/**
 *  ボタン押下時の処理
 */
function onClickOr10659() {
  // 画面.ケアプラン方式
  Or10659Logic.state.set({
    uniqueCpId: or10659.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr10659 = computed(() => {
  // Or10659のダイアログ開閉状態
  return Or10659Logic.state.get(or10659.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ボタン押下時の処理
 */
function onClick() {
  or10659Oneway.value =JSON.parse(info.value.value) as Or10659OneWayType
  Or10659Logic.state.set({
    uniqueCpId: or10659.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>

<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ CD 2025/05/09 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr10659()"
        >GUI01221_モニタリングタイトルマスタ
      </v-btn>

      <g-custom-or-10659
        v-if="showDialogOr10659"
        :oneway-model-value="or10659Oneway"
        v-bind="or10659"
      />
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ CD 2025/05/09 ADD END-->
<c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row
    no-gutters
    class="pb-2 pl-2"
  >
    <base-mo00039
      v-model="mo00039Type"
      :oneway-model-value="mo00039OneWay"
      class="bg-transparent"
    >
    </base-mo00039>
  </c-v-row>
  <c-v-row
    no-gutters
    class="content pb-2"
  >
    <div class="w-25 h-75 pl-2">
      <div>データ</div>
      <base-mo00046
        v-model="info"
        :oneway-model-value="mo00046OneWayType"
      />
    </div>
    <div class="pl-2 pt-5">
      <v-btn @click="onClick()"> GUI01221_疎通起動 </v-btn>
    </div>
  </c-v-row>
</template>
<style scoped lang="scss">
.content {
  background: rgb(var(--v-theme-background));
}
.bg-transparent {
  background: transparent !important;
}
</style>

