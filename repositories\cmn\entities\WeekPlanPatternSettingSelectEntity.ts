/**
 * Or35774のエンティティ
 * GUI01044_週間計画パターン設定
 *
 * <AUTHOR>
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'
import type { cks52List } from '~/types/cmn/business/components/Or05349Type'

/**
 * 週間計画パターン設定情報入力エンティティ
 */
export interface WeekPlanPatternSettingSelectInEntity extends InWebEntity {
  /**
   * 計画書様式
   */
  ssmCksFlg: string
  /**
   * 親画面.利用者
   */
  userId: string
  /**
   * 共通情報.システム年月日
   */
  appYmd: string
}

/**
 * 週間計画パターン出力エンティティ
 */
export interface WeekPlanPatternSettingSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * 要介護度デフォルト
     */
    yoKaigoDo: string
    /**
     * 履歴リスト
     */
    cks51List: History[]
    /**
     * 明細リスト
     */
    detailList: DetailList[]
  }
}

/**
 * 明細リスト
 */
export interface DetailList {
    /**
     * 日常リスト
     */
    cks54List: NichiJyou[]
    /**
     * 詳細リスト
     */
    cks52List: cks52List[]
}

/**
 * 履歴情報
 */
export interface History {
  /**
   * 週間計画ID
   */
  ks51Id: string
  /**
   * 要介護度区分
   */
  youkaCd: string
  /**
   * 名称
   */
  nameKnj: string
  /**
   * サービス区分
   */
  switchKbn: string
  /**
   * 表示順
   */
  seq: string
  /**
   * 有効期間ID
   */
  termId: string
  /**
   * 週単位以外ｻｰﾋﾞｽ
   */
  wIgaiKnj: string
}

/**
 * 日常情報
 */
export interface NichiJyou {
  /**
   * 週間計画ID
   */
  ks51Id: string
  /**
   * 日常データID
   */
  ks54Id: string
  /**
   * 連番
   */
  seq: string
  /**
   * 主な日常生活上の活動
   */
  nichijoKnj: string
}
/**
 * 詳細情報
 */
export interface Detail {
  /**
   * 詳細データID
   */
  ks52Id: string
  /**
   * 週間計画ID
   */
  ks51Id: string
  /**
   * 曜日
   */
  youbi: string
  /**
   * 開始時間
   */
  kaishiJikan: string
  /**
   * 終了時間
   */
  shuuryouJikan: string
  /**
   * 内容
   */
  naiyouKnj: string
  /**
   * 文字サイズ
   */
  fontSize: string
  /**
   * 表示モード
   */
  disp_mode: string
  /**
   * 文字位置
   */
  alignment: string
  /**
   * サービス種類CD
   */
  svShuruiCd: string
  /**
   * サービス項目（台帳）ID
   */
  svItemCd: string
  /**
   * サービス事業者CD
   */
  svJigyoId: string
  /**
   * サービス種類名称
   */
  svShuruiKnj: string
  /**
   * サービス項目名称
   */
  svItemKnj: string
  /**
   * サービス事業者名称
   */
  svJigyoKnj: string
  /**
   * サービス事業者略称
   */
  svJigyoRyaku: string
  /**
   * 文字カラー
   */
  fontColor: string
  /**
   * 背景カラー
   */
  backColor: string
  /**
   * 時間表示区分
   */
  timeKbn: string
  /**
   * 週単位以外のｻｰﾋﾞｽ区分
   */
  igaiKbn: string
  /**
   * 週単位以外文字
   */
  igaiMoji: string
  /**
   * 週単位以外のｻｰﾋﾞｽ（日付指定）
   */
  igaiDate: string
  /**
   * 週単位以外のｻｰﾋﾞｽ（曜日指定）
   */
  igaiWeek: string
  /**
   * 福祉用具貸与の単価
   */
  svTani: string
  /**
   * 福祉用具貸与マスタID
   */
  fygId: string
  /**
   * 枠外表示するかのフラグ
   */
  wakugaiFlg: string
  /**
   * 加算リスト
   */
  cks55List: cks55DataType
  /**
   * 隔週リスト
   */
  cks58List: cks58DataType
  /**
   * 担当者リスト
   */
  cks56List: cks56DataType
  /**
   * 月日リスト
   */
  cks57List: cks57DataType
}

/**
 * 加算リスト
 */
export interface cks55DataType {
  /**
   *加算データID
   */
  ks55Id: string
  /**
   *詳細データID
   */
  ks52Id: string
  /**
   *加算サービス事業者ID
   */
  svJigyoId: string
  /**
   *加算サービス項目ID
   */
  svItemCd: string
  /**
   *回数
   */
  kaisuu: string
  /**
   *福祉用具貸与の単価
   */
  svTani: string
  /**
   *福祉用具貸与マスタID
   */
  fygId: string
}
/**
 * 隔週リスト
 */
export interface cks58DataType {
  /**
   *隔週データのプライマリID
   */
  ks58Id: string
  /**
   *詳細データID
   */
  ks52Id: string
  /**
   *隔週基準年月日
   */
  kakusyuuYmd: string
  /**
   *隔週週間隔
   */
  kakusyuuKankaku: string
  /**
   *曜日区分
   */
  youbi: string
}
/**
 * 担当者リスト
 */
export interface cks56DataType {
  /**
   *担当者データID
   */
  ks56Id: string
  /**
   *詳細データID
   */
  ks52Id: string
  /**
   *担当者名称
   */
  shokushuKnj: string
  /**
   *職種
   */
  shokushuId: string
}
/**
 * 月日リスト
 */
export interface cks57DataType {
  /**
   *月日データID
   */
  ks56Id: string
  /**
   *詳細データID
   */
  ks52Id: string
  /**
   *月日指定_開始日
   */
  startYmd: string
  /**
   *月日指定_終了日
   */
  endYmd: string
}
/**
 * 要介護度情報
 */
export interface Youkai {
  /**
   * コードID
   */
  codeId: string
  /**
   * コード名称
   */
  codeName: string
}
