import { OrX0203Const } from './OrX0203.constants'
import type { OrX0203StateType } from './OrX0203.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 *GUI04519_転送履歴画面
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR>
 */
export namespace OrX0203Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: OrX0203Const.CP_ID(0),
      uniqueCpId,
      childCps: [],
      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })
    // 子コンポーネントのセットアップ
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<OrX0203StateType>(OrX0203Const.CP_ID(0))
}
