import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * Or35672:アセスメント複写モーダル
 * GUI00807_アセスメント複写
 *
 * @description
 * アセスメント複写モーダルAPI用エンティティ
 *
 * <AUTHOR>
 */
/**
 * アセスメント複写モーダルAPI取得入力エンティティ
 */
export interface AssessmentDuplicatetSelectInEntity extends InWebEntity {
  /** 種別ID */
  syubetsuId?: string
  /** 施設ID */
  shisetuId?: string
  /** 事業所ID */
  svJigyoId?: string
  /** 利用者ID */
  userId?: string
  /** 期間管理フラグ */
  periodManagementFlg?: string
}

/**
 * アセスメント複写モーダルAPI取得出力エンティティ
 */
export interface AssessmentDuplicatetSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** 計画期間選択一覧リスト */
    planPeriodList: {
      /** 期間内履歴数 */
      dmyCnt: string
      /** 開始日 */
      startYmd: string
      /** 終了日 */
      endYmd: string
      /** 計画期間 */
      planPeriod: string
      /** 事業所名 */
      jigyoKnj: string
      /** 期間ID */
      sc1Id: string
      /** 法人ID */
      houjinId: string
      /** 施設ID */
      shisetuId: string
      /** 事業者ID */
      svJigyoId: string
      /** 利用者ID */
      userId: string
      /** 種別ID */
      syubetsuId: string
      /** 事業名（略称） */
      jigyoRyakuKnj: string
    }[]
    /** 履歴リスト */
    rirekiList: {
      /** アセスメントID */
      gdlId: string
      /** 計画期間ID */
      sc1Id: string
      /** アセスメント実施日 */
      asJisshiDateYmd: string
      /** 記載者ID */
      shokuId: string
      /** 記載者名 */
      shokuKnj: string
      /** フェースシート */
      ass1: string
      /** 家族状況／支援 */
      ass2: string
      /** ｻｰﾋﾞｽ利用状況 */
      ass3: string
      /** 住居等の状況 */
      ass4: string
      /** 本人の健康状態 */
      ass5: string
      /** 本人の基本動作等1 */
      ass6: string
      /** Ｅの状態 */
      ass7: string
      /** Ｆの状態 */
      ass8: string
      /** Ｇの状態 */
      ass9: string
      /** Ｈの状態 */
      ass10: string
      /** Ｉの状態 */
      ass11: string
      /** Ｊの状態 */
      ass12: string
      /** 本人の基本動作等8 */
      ass13: string
      /** 医師の意見 */
      ass14: string
      /** 全体のまとめ･特記事項 */
      ass15: string
      /** 改定フラグ */
      ninteiFormF: string
      /** 更新回数 */
      modifiedCnt: string
      /** １日のスケジュール */
      ass16: string
      /** 課題と目標リスト */
      issuesAndGoalsList: {
        /** id */
        id?: string
        /** アセスメントID */
        gdlId?: string
        /** 計画期間ID */
        sc1Id?: string
        /** アセスメント番号 */
        assNo?: string
        /** 課題 */
        kadaiKnj?: string
        /** 長期 */
        choukiKnj?: string
        /** 短期 */
        tankiKnj?: string
        /** 連番 */
        seq?: string
        /** 更新回数 */
        modifiedCnt?: string
        /** アセスメント名称 */
        assName?: string
      }[]
    }[]
  }
}
