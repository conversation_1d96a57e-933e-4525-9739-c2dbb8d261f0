// import { Or30798Logic } from '../Or30798/Or30798.logic'
import type { TeX0002EventType } from '../../template/TeX0002/TeX0002.type'
import { Or30221Const } from '../Or30221/Or30221.constants'
import { Or30221Logic } from '../Or30221/Or30221.logic'
import { Or30233Logic } from '../Or30233/Or30233.logic'
import { Or30233Const } from '../Or30233/Or30233.constants'
import { Or30269Const } from '../Or30269/Or30269.constants'
import { Or30269Logic } from '../Or30269/Or30269.logic'
import { Or54682Const } from '../Or54682/Or54682.constants'
import { Or54682Logic } from '../Or54682/Or54682.logic'
import { Or28500Logic } from '../Or28500/Or28500.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or28500Const } from '../Or28500/Or28500.constants'
import { OrX0209Logic } from '../../organisms/OrX0209/OrX0209.logic'
import { OrX0209Const } from '../../organisms/OrX0209/OrX0209.constants'
import { Or26866Const } from '../Or26866/Or26866.constants'
import { Or26866Logic } from '../Or26866/Or26866.logic'
import { Or30149Const } from './Or30149.constants'
import type { Or30149CopyDataType, Or30149StateType } from './Or30149.type'
import {
  useEventStatusAccessor,
  useInitialize,
  useOneWayBindAccessor,
  useTwoWayBindAccessor,
} from '~/composables/useComponentLogic'

/**
 * Or30149Const:有機体:［アセスメント］画面（居宅）（1）
 * 処理ロジック
 *
 * @description
 * initialize処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or30149Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or30149Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {

      },
      childCps: [
        { cpId: Or30221Const.CP_ID(0) },
        { cpId: Or30233Const.CP_ID(0) },
        { cpId: Or30269Const.CP_ID(0) },
        { cpId: Or54682Const.CP_ID(0) },
        { cpId: OrX0209Const.CP_ID(0) },
        { cpId: Or28500Const.CP_ID(0) },
        { cpId: Or51775Const.CP_ID(0) },

        { cpId: Or26866Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or30221Logic.initialize(childCpIds[Or30221Const.CP_ID(0)].uniqueCpId)
    Or30233Logic.initialize(childCpIds[Or30233Const.CP_ID(0)].uniqueCpId)
    Or30269Logic.initialize(childCpIds[Or30269Const.CP_ID(0)].uniqueCpId)
    Or54682Logic.initialize(childCpIds[Or54682Const.CP_ID(0)].uniqueCpId)
    OrX0209Logic.initialize(childCpIds[OrX0209Const.CP_ID(0)].uniqueCpId)
    Or28500Logic.initialize(childCpIds[Or28500Const.CP_ID(0)].uniqueCpId)
    Or51775Logic.initialize(childCpIds[Or51775Const.CP_ID(0)].uniqueCpId)
    Or26866Logic.initialize(childCpIds[Or26866Const.CP_ID(0)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or30149CopyDataType>(Or30149Const.CP_ID(0))

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or30149StateType>(Or30149Const.CP_ID(0))

  /**
   * 自身のEventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useEventStatusAccessor<TeX0002EventType>(Or30149Const.CP_ID(0))
}
