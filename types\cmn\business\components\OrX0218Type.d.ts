import type { MoInputCommonType } from '~/types/business/components/MoInputCommonType'
/**
 * OrX0218:有機体:実施計画①～③表用選択メニュー付き日付入力
 * 双方向バインドのインタフェース
 */
export interface OrX0218Type {
  /**
   * テキストエリアに表示する値
   * 如何なる場合でも返却されるテキストエリアのブレーンテキスト値
   */
  value: string
}

/**
 * OrX0218:有機体:実施計画①～③表用選択メニュー付き日付入力
 * 単方向バインドのインタフェース
 */
export interface OrX0218OnewayType extends MoInputCommonType {
  /**
   * コンポーネント名
   */
  name?: string | undefined

  /**
   * 無効フラグ
   */
  disabled?: boolean

  /**
   * 読み取り専用
   */
  readonly?: boolean
  /**
   * 高さ
   */
  height?: string
  /**
   * 年月日で入力フラグ
   */
  showYmdBtnFlg?: boolean
  /**
   * 月日で入力フラグ
   */
  showMonthDayBtnFlg?: boolean
  /**
   * 入力支援アイコン表示フラグ
   *
   * @default false 非表示
   */
  showEditHelperBtnFlg?: boolean
  /**
   * 入力支援ボタンのクラス
   */
  editBtnClass?: string
  /**
   * カレンダーボタン表示フラグ
   *
   * @default true 表示
   */
  showCalendarBtnFlg?: boolean
  /**
   * 年月日フォーマット
   *
   * @example YYYY/MM/DD、YYYY年MM月DD日
   */
  dateFormat?: string
  /**
   * 月日フォーマット
   *
   * @example MM/DD、MM月DD日
   */
  mdDateFormat?: string
  /**
   * コンテンツクラス
   */
  contentClass?: string
  /**
   * コンテンツスタイル
   */
  contentStyle?: string
  /**
   * 期間入力フラグ
   */
  kikanFlg?: boolean
  /**
   * カスタム幅
   */
  customWidth?: boolean
  /**
   * 最大行数
   */
  maxRows?: number
}
