import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'
import type { IReportInEntity } from '~/types/business/core/ReportRepositoryType'
/**
 * Or29242:有機体:［アセスメント］画面（居宅）（6③-④）
 * GUI00801_［アセスメント］画面（居宅）（6③-④）
 *
 * @description
 * ［アセスメント］画面（居宅）（6③-④）
 *
 * <AUTHOR>
 */

/**
 * 初期情報取得リクエストパラメータタイプ
 */
export interface assessmentHomeTab63SelectInEntity extends InWebEntity {
  /** 計画期間ID */
  sc1Id?: string
  /** アセスメントID */
  gdlId?: string
  /** 改訂フラグ */
  ninteiFormF?: string
}

/**
 * 初期情報取得レスポンスパラメータタイプ
 */
export interface assessmentHomeTab63SelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * ③認知機能・④精神・行動障害情報
     */
    gdl4Kan13Info: CognitiveFunctionMentalActionHandycapNinteiFlg45Type

    /**
     * ③認知機能・④精神・行動障害情報
     */
    gdl5Kan13Info: CognitiveFunctionMentalActionHandycapNinteiFlg45Type
  }
}

/**
 * 認知機能改訂フラグ4 | 5レスポンスパラメータタイプ
 */
export interface CognitiveFunctionMentalActionHandycapNinteiFlg45Type {
  /** インデックス署名*/
  [key: string]: string | undefined
  /** アセスメントID */
  gdlId?: string
  /** 計画期間ID */
  sc1Id?: string
  /** 認定項目3-1（意思の伝達） */
  bango31?: string
  /** 認定項目3-2（毎日の日課を理解する） */
  bango32?: string
  /** 認定項目3-3（生年月日や年齢を答える） */
  bango33?: string
  /** 認定項目3-4（面接調査の直前記憶） */
  bango34?: string
  /** 認定項目3-5（自分の名前を答える） */
  bango35?: string
  /** 認定項目3-6（今の季節を理解する） */
  bango36?: string
  /** 認定項目3-7（自分のいる場所を答える） */
  bango37?: string
  /** 認定項目3-8（徘徊） */
  bango38?: string
  /** 認定項目3-9（外出すると戻れない（迷子）） */
  bango39?: string
  /** 認定項目3-10（介護者との意思伝達） */
  bango310?: string
  /** 認定項目4-1（被害妄想（物を盗られたなど）） */
  bango41?: string
  /** 認定項目4-2（作話をする） */
  bango42?: string
  /** 認定項目4-3（感情が不安定になる） */
  bango43?: string
  /** 認定項目4-4（昼夜の逆転） */
  bango44?: string
  /** 認定項目4-5（しつこく同じ話をする） */
  bango45?: string
  /** 認定項目4-6（大声を出す） */
  bango46?: string
  /** 認定項目4-7（介護に抵抗する） */
  bango47?: string
  /** 認定項目4-8（落ち着きがない（家に帰る等）） */
  bango48?: string
  /** 認定項目4-9（外に出たがり目が離せない） */
  bango49?: string
  /** 認定項目4-10（ものを集める無断でもってくる） */
  bango410?: string
  /** 認定項目4-11（物を壊す衣類を破く） */
  bango411?: string
  /** 認定項目4-12（ひどい物忘れ） */
  bango412?: string
  /** 認定項目4-13（独り言や独り笑い） */
  bango413?: string
  /** 認定項目4-14（自分勝手な行動） */
  bango414?: string
  /** 認定項目4-15（話がまとまらない会話にならない） */
  bango415?: string
  /** 認定項目4-16（幻視幻聴） */
  bango416?: string
  /** 認定項目4-17（暴言暴力） */
  bango417?: string
  /** 認定項目4-18（目的なく動き回る） */
  bango418?: string
  /** 認定項目4-19（火の始末管理） */
  bango419?: string
  /** 認定項目4-20（不潔行為） */
  bango420?: string
  /** 認定項目4-21（異食行動） */
  bango421?: string
  /** 援助の現状（家族） */
  famMemoKnj?: string
  /** 援助の現状（サービス） */
  serMemoKnj?: string
  /** 援助の希望（本人） */
  kiboMemoKnj?: string
  /** 援助の希望（家族） */
  kiboFamMemoKnj?: string
  /** 援助の計画 */
  keikakuMemoKnj?: string
  /** 特記解決すべき課題など */
  memo1Knj?: string
  /** 家族からの情報と観察 */
  kazokuJouhouKnj?: string
}

/**
 * 保存リクエストパラメータタイプ
 */
export interface CognitiveFunctionMentalActionHandycapUpdateInEndity extends InWebEntity {
  /** タブID */
  tabId: string
  /** 機能ID */
  kinoId: string
  /** 当履歴ページ番号 */
  krirekiNo: string
  /** e文書用パラメータ */
  edocumentUseParam: IReportInEntity
  /** e文書削除用パラメータ */
  edocumentDeleteUseParam: IReportInEntity
  /** 期間対象フラグ */
  kikanFlg: string
  /** 計画対象期間番号 */
  planningPeriodNo: string
  /** 開始日 */
  startYmd: string
  /** 終了日 */
  endYmd: string
  /** ガイドラインまとめ */
  matomeFlg: string
  /** ログインID */
  loginId: string
  /** システム略称 */
  sysRyaku: string
  /** 職員ID */
  shokuId: string
  /** システムコード */
  sysCd: string
  /** 事業者名 */
  svJigyoKnj: string
  /** 作成者名 */
  createUserName: string
  /** 利用者名 */
  userName: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 利用者ID */
  userId: string
  /** 事業者ID */
  svJigyoId: string
  /** 種別ID */
  syubetsuId: string
  /** 更新区分 */
  updateKbn: string
  /** 履歴更新区分 */
  historyUpdateKbn: string
  /** 削除処理区分 */
  deleteKbn: string
  /** 計画対象期間ID */
  sc1Id: string
  /** アセスメントID */
  gdlId: string
  /** 作成日 */
  kijunbiYmd: string
  /** 作成者ID */
  sakuseiId: string
  /** 改定フラグ */
  ninteiFormF: string
  /** ③認知機能・④精神・行動障害情報 */
  kan13Info: {
    /** インデックス */
    [key: string]: string
    /** 認定項目3-1（意思の伝達） */
    bango31: string
    /** 認定項目3-2（毎日の日課を理解する） */
    bango32: string
    /** 認定項目3-3（生年月日や年齢を答える） */
    bango33: string
    /** 認定項目3-4（面接調査の直前記憶） */
    bango34: string
    /** 認定項目3-5（自分の名前を答える） */
    bango35: string
    /** 認定項目3-6（今の季節を理解する） */
    bango36: string
    /** 認定項目3-7（自分のいる場所を答える） */
    bango37: string
    /** 認定項目3-8（徘徊） */
    bango38: string
    /** 認定項目3-9（外出すると戻れない（迷子）） */
    bango39: string
    /** 認定項目3-10（介護者との意思伝達） */
    bango310: string
    /** 認定項目4-1（被害妄想（物を盗られたなど）） */
    bango41: string
    /** 認定項目4-2（作話をする） */
    bango42: string
    /** 認定項目4-3（感情が不安定になる） */
    bango43: string
    /** 認定項目4-4（昼夜の逆転） */
    bango44: string
    /** 認定項目4-5（しつこく同じ話をする） */
    bango45: string
    /** 認定項目4-6（大声を出す） */
    bango46: string
    /** 認定項目4-7（介護に抵抗する） */
    bango47: string
    /** 認定項目4-8（落ち着きがない（家に帰る等）） */
    bango48: string
    /** 認定項目4-9（外に出たがり目が離せない） */
    bango49: string
    /** 認定項目4-10（ものを集める無断でもってくる） */
    bango410: string
    /** 認定項目4-11（物を壊す衣類を破く） */
    bango411: string
    /** 認定項目4-12（ひどい物忘れ） */
    bango412: string
    /** 認定項目4-13（独り言や独り笑い） */
    bango413: string
    /** 認定項目4-14（自分勝手な行動） */
    bango414: string
    /** 認定項目4-15（話がまとまらない会話にならない） */
    bango415: string
    /** 認定項目4-16（幻視幻聴） */
    bango416: string
    /** 認定項目4-17（暴言暴力） */
    bango417: string
    /** 認定項目4-18（目的なく動き回る） */
    bango418: string
    /** 認定項目4-19（火の始末管理） */
    bango419: string
    /** 認定項目4-20（不潔行為） */
    bango420: string
    /** 認定項目4-21（異食行動） */
    bango421: string
    /** 援助の現状（家族） */
    famMemoKnj: string
    /** 援助の現状（サービス） */
    serMemoKnj: string
    /** 援助の希望（本人） */
    kiboMemoKnj: string
    /** 援助の希望（家族） */
    kiboFamMemoKnj: string
    /** 援助の計画 */
    keikakuMemoKnj: string
    /** 特記解決すべき課題など */
    memo1Knj: string
    /** 家族からの情報と観察 */
    kazokuJouhouKnj: string
  }
  /**
   * 課題と目標情報リスト
   */
  kadaiList: {
    /* id */
    id: string
    /* アセスメント番号 */
    assNo: string
    /* 課題 */
    kadaiKnj: string
    /* 長期 */
    choukiKnj: string
    /* 短期 */
    tankiKnj: string
    /* 連番 */
    seq: string
    /* 更新区分 */
    updateKbn: string
  }[]
}

/**
 * 保存レスポンスパラメータタイプ
 */
export interface CognitiveFunctionMentalActionHandycapUpdateOutEndity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** 計画対象期間ID */
    sc1Id: string
    /** アセスメントID */
    gdlId: string
    /** エラー区分 */
    errKbn: string
  }
}
