import { HttpResponse, type ResponseResolver } from 'msw'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { InEntity } from '~/repositories/AbstructWebRepository'

import GUI00380 from '@/mocks/api/dd/select/pages/dch/users/um-asp/GUI00380/handler'
import GUI00036GUI00036Init from '@/mocks/api/dd/select/pages/dch/common/GUI00036/GUI00036_init/handler'
import GUI00036GUI00036GetLedgerInfo from '@/mocks/api/dd/select/pages/dch/common/GUI00036/GUI00036_getLedgerInfo/handler'
import GUI00017Initial from '@/mocks/api/dd/select/pages/dch/common/GUI00017/initial/handler'
import GUI00017ListAll from '@/mocks/api/dd/select/pages/dch/common/GUI00017/list_all/handler'
import GUI00018Initial from '@/mocks/api/dd/select/pages/dch/common/GUI00018/initial/handler'
import GUI00018Export from '@/mocks/api/dd/select/pages/dch/common/GUI00018/export/handler'
import GUI00027Init from '@/mocks/api/dd/select/pages/dch/common/GUI00027/init/handler'
import GUI00026ListAll from '@/mocks/api/dd/select/pages/dch/common/GUI00026/list_all/handler'
import GUI00064Init from '@/mocks/api/dd/select/pages/dch/common/GUI00064/init/handler'
import GUI00038GUI00038Init from '@/mocks/api/dd/select/pages/dch/common/GUI00038/GUI00038_init/handler'
import GUI03368Init from '@/mocks/api/dd/select/pages/dch/common/GUI03368/init/handler'
import GUI03368Redisplay from '@/mocks/api/dd/select/pages/dch/common/GUI03368/redisplay/handler'
import GUI03368CopyLastTime from '@/mocks/api/dd/select/pages/dch/common/GUI03368/copyLastTime/handler'
import GUI03368BackHistory from '@/mocks/api/dd/select/pages/dch/common/GUI03368/backHistory/handler'
import GUI03368ForwardHistory from '@/mocks/api/dd/select/pages/dch/common/GUI03368/forwardHistory/handler'
import GUI03368ImportHistory from '@/mocks/api/dd/select/pages/dch/common/GUI03368/import/handler'
import GUI00022 from '@/mocks/api/dd/select/pages/dch/common/GUI00022/handler'
import GUI00059ListHospital from '@/mocks/api/dd/select/pages/dch/common/GUI00059/list_hospital/handler'
import GUI00059Initial from '@/mocks/api/dd/select/pages/dch/common/GUI00059/initial/handler'
import GUI00059ListAll from '@/mocks/api/dd/select/pages/dch/common/GUI00059/list_all/handler'
import GUI00059ListHospitalDepartment from '@/mocks/api/dd/select/pages/dch/common/GUI00059/list_hospital_department/handler'
import GUI00037Testpage from '@/mocks/api/dd/select/pages/dch/common/GUI00037/testpage/handler'
import GUI00037Init from '@/mocks/api/dd/select/pages/dch/common/GUI00037/init/handler'
import GUI00037GetHolidayInfo from '@/mocks/api/dd/select/pages/dch/common/GUI00037/getHolidayInfo/handler'
import GUI00070 from '@/mocks/api/dd/select/pages/dch/common/GUI00070/handler'
import GUI00032Init from '@/mocks/api/dd/select/pages/dch/common/GUI00032/init/handler'
import GUI00079Init from '@/mocks/api/dd/select/pages/dch/common/GUI00079/init/handler'
import GUI03221Init from '@/mocks/api/dd/select/pages/dch/common/GUI03221/init/handler'
import GUI03296Testpage from '@/mocks/api/dd/select/pages/dch/common/GUI03296/testpage/handler'
import GUI03296Init from '@/mocks/api/dd/select/pages/dch/common/GUI03296/init/handler'
import GUI03296Redisplay from '@/mocks/api/dd/select/pages/dch/common/GUI03296/redisplay/handler'
import GUI03296CopyLastTime from '@/mocks/api/dd/select/pages/dch/common/GUI03296/copyLastTime/handler'
import GUI03296BackHistory from '@/mocks/api/dd/select/pages/dch/common/GUI03296/backHistory/handler'
import GUI03296ForwardHistory from '@/mocks/api/dd/select/pages/dch/common/GUI03296/forwardHistory/handler'
import GUI03296Import from '@/mocks/api/dd/select/pages/dch/common/GUI03296/import/handler'
import GUI03353Init from '@/mocks/api/dd/select/pages/dch/common/GUI03353/init/handler'
import GUI03353Redisplay from '@/mocks/api/dd/select/pages/dch/common/GUI03353/redisplay/handler'
import GUI03353CopyLastTime from '@/mocks/api/dd/select/pages/dch/common/GUI03353/copyLastTime/handler'
import GUI03353BackHistory from '@/mocks/api/dd/select/pages/dch/common/GUI03353/backHistory/handler'
import GUI03353ForwardHistory from '@/mocks/api/dd/select/pages/dch/common/GUI03353/forwardHistory/handler'
import GUI03353GetWardAndBedriddenDegree from '@/mocks/api/dd/select/pages/dch/common/GUI03353/getWardAndBedriddenDegree/handler'
import GUI00024GUI00024Init from '@/mocks/api/dd/select/pages/dch/common/GUI00024/GUI00024_init/handler'
import GUI00024GUI00024Redisplay from '@/mocks/api/dd/select/pages/dch/common/GUI00024/GUI00024_redisplay/handler'
import GUI00050GUI00050Init from '@/mocks/api/dd/select/pages/dch/common/GUI00050/GUI00050_init/handler'
import GUI00050GUI00050TestInit from '@/mocks/api/dd/select/pages/dch/common/GUI00050/GUI00050Test_init/handler'
import GUI00054Init from '@/mocks/api/dd/select/pages/dch/common/GUI00054/init/handler'
import GUI00054Add from '@/mocks/api/dd/select/pages/dch/common/GUI00054/add/handler'
import GUI00068Init from '@/mocks/api/dd/select/pages/dch/common/GUI00068/init/handler'
import GUI00063Init from '@/mocks/api/dd/select/pages/dch/common/GUI00063/init/handler'
import GUI00021Init from '@/mocks/api/dd/select/pages/dch/common/GUI00021/init/handler'
import GUI03266Init from '@/mocks/api/dd/select/pages/dch/common/GUI03266/init/handler'
import GUI00042Init from '@/mocks/api/dd/select/pages/dch/common/GUI00042/init/handler'
import GUI00023Init from '@/mocks/api/dd/select/pages/dch/common/GUI00023/init/handler'
import GUI00023SelectUserList from '@/mocks/api/dd/select/pages/dch/common/GUI00023/selectUserList/handler'
import GUI00023UpdateStaffList from '@/mocks/api/dd/select/pages/dch/common/GUI00023/updateStaffList/handler'
import GUI00023UpdateUserList from '@/mocks/api/dd/select/pages/dch/common/GUI00023/updateUserList/handler'
import GUI00023CheckUsableInfo from '@/mocks/api/dd/select/pages/dch/common/GUI00023/checkUsableInfo/handler'
import GUI00023CheckUserOfficeAuth from '@/mocks/api/dd/select/pages/dch/common/GUI00023/checkUserOfficeAuth/handler'
import GUI00056Init from '@/mocks/api/dd/select/pages/dch/common/GUI00056/init/handler'
import GUI03214Init from '@/mocks/api/dd/select/pages/dch/common/GUI03214/init/handler'
import GUI03214StringFormat from '@/mocks/api/dd/select/pages/dch/common/GUI03214/stringFormat/handler'
import GUI03215Init from '@/mocks/api/dd/select/pages/dch/common/GUI03215/init/handler'
import GUI03215StringFormat from '@/mocks/api/dd/select/pages/dch/common/GUI03215/stringFormat/handler'
import GUI00041Init from '@/mocks/api/dd/select/pages/dch/common/GUI00041/init/handler'
import GUI00041Office from '@/mocks/api/dd/select/pages/dch/common/GUI00041/office/handler'
import GUI00041Search from '@/mocks/api/dd/select/pages/dch/common/GUI00041/search/handler'
import GUI00041DisplayInfo from '@/mocks/api/dd/select/pages/dch/common/GUI00041/displayInfo/handler'
import GUI00033Init from '@/mocks/api/dd/select/pages/dch/common/GUI00033/init/handler'
import GUI00033InitKsg from '@/mocks/api/dd/select/pages/dch/common/GUI00033/initKsg/handler'
import GUI00021Search from '@/mocks/api/dd/select/pages/dch/common/GUI00021/search/handler'
import GUI00021Select from '@/mocks/api/dd/select/pages/dch/common/GUI00021/select/handler'
import GUIxxxx from '@/mocks/api/dd/select/pages/sample_page/hidden-item/use-sample/GUIxxxx/handler'
import GUIyyyy from '@/mocks/api/dd/select/pages/sample_page/hidden-item/input-sample/GUIyyyy/handler'
import Or00251TopHeaderInfo from '@/mocks/api/dd/select/pages/header/topHeaderInfo/handler'
import Or00251NoticeCount from '@/mocks/api/dd/select/pages/header/noticeCount/handler'
import UserList from '@/mocks/api/dd/select/user-list/handler'
import UserListHistory from '@/mocks/api/dd/select/user-list-history/handler'

import ShokuinList from '@/mocks/api/dd/select/shokuin-list/handler'
import SettingsCmnSelect from '@/mocks/api/dd/select/login/handler'
import OperationHistory from '@/mocks/api/dd/select/operation_history/handler'
import LoginHistory from '~/mocks/api/dd/select/login_history/handler'
import dsKghInfoNewsListFile from '@/mocks/api/dd/select/dsKghInfoNewsListFile/handler'
import StaffList from '@/mocks/api/dd/select/staff_list/handler'

const post: ResponseResolver = async ({ request }) => {
  const requestBody = (await request.json()) as InEntity

  const business = requestBody.data.business
  const dataName = business.dataName
  let filePath = ''
  let handlerParam

  if (dataName) {
    filePath = ['./pages', dataName].join('/')
    handlerParam = business
  } else {
    const screenDefId = business.definitionJson.definition.screenDefId
    const routing = business.definitionJson.definition.routing
    const screenName = business.definitionJson.definition.screenPhysicalName
    handlerParam = business.definitionJson

    filePath = ['./pages', routing, screenName, screenDefId].join('/')
  }

  const handler = _handler(filePath)
  if (handler) {
    return handler(handlerParam)
  } else {
    const responceJson: BaseResponseBody = {
      statusCode: 'success',
      data: {
        definitionJson: business.definitionJson,
      },
    }
    return HttpResponse.json(responceJson, { status: 200 })
  }
}

function _handler(path: string): Function | undefined {
  try {
    if (path === './pages/dch/users/um-asp/GUI00380') {
      return GUI00380.handler
    } else if (path === './pages/dch/common/GUI00036/GUI00036_init') {
      return GUI00036GUI00036Init.handler
    } else if (path === './pages/dch/common/GUI00036/GUI00036_getLedgerInfo') {
      return GUI00036GUI00036GetLedgerInfo.handler
    } else if (path === './pages/dch/common/GUI00017/initial') {
      return GUI00017Initial.handler
    } else if (path === './pages/dch/common/GUI00017/list_all') {
      return GUI00017ListAll.handler
    } else if (path === './pages/dch/common/GUI00018/initial') {
      return GUI00018Initial.handler
    } else if (path === './pages/dch/common/GUI00018/export') {
      return GUI00018Export.handler
    } else if (path === './pages/dch/common/GUI00027/init') {
      return GUI00027Init.handler
    } else if (path === './pages/dch/common/GUI00026/list_all') {
      return GUI00026ListAll.handler
    } else if (path === './pages/dch/common/GUI00064/init') {
      return GUI00064Init.handler
    } else if (path === './pages/dch/common/GUI00038/GUI00038_init') {
      return GUI00038GUI00038Init.handler
    } else if (path === './pages/dch/common/GUI03368/init') {
      return GUI03368Init.handler
    } else if (path === './pages/dch/common/GUI03368/redisplay') {
      return GUI03368Redisplay.handler
    } else if (path === './pages/dch/common/GUI03368/copyLastTime') {
      return GUI03368CopyLastTime.handler
    } else if (path === './pages/dch/common/GUI03368/backHistory') {
      return GUI03368BackHistory.handler
    } else if (path === './pages/dch/common/GUI03368/forwardHistory') {
      return GUI03368ForwardHistory.handler
    } else if (path === './pages/dch/common/GUI03368/import') {
      return GUI03368ImportHistory.handler
    } else if (path === './pages/dch/common/GUI00022') {
      return GUI00022.handler
    } else if (path === './pages/dch/common/GUI00059/list_hospital') {
      return GUI00059ListHospital.handler
    } else if (path === './pages/dch/common/GUI00059/initial') {
      return GUI00059Initial.handler
    } else if (path === './pages/dch/common/GUI00059/list_all') {
      return GUI00059ListAll.handler
    } else if (path === './pages/dch/common/GUI00059/list_hospital_department') {
      return GUI00059ListHospitalDepartment.handler
    } else if (path === './pages/dch/common/GUI00037/testpage') {
      return GUI00037Testpage.handler
    } else if (path === './pages/dch/common/GUI00037/init') {
      return GUI00037Init.handler
    } else if (path === './pages/dch/common/GUI00037/getHolidayInfo') {
      return GUI00037GetHolidayInfo.handler
    } else if (path === './pages/dch/common/GUI00070') {
      return GUI00070.handler
    } else if (path === './pages/dch/common/GUI00032/init') {
      return GUI00032Init.handler
    } else if (path === './pages/dch/common/GUI00079/init') {
      return GUI00079Init.handler
    } else if (path === './pages/dch/common/GUI03221/init') {
      return GUI03221Init.handler
    } else if (path === './pages/dch/common/GUI03353/init') {
      return GUI03353Init.handler
    } else if (path === './pages/dch/common/GUI03353/redisplay') {
      return GUI03353Redisplay.handler
    } else if (path === './pages/dch/common/GUI03353/copyLastTime') {
      return GUI03353CopyLastTime.handler
    } else if (path === './pages/dch/common/GUI03353/backHistory') {
      return GUI03353BackHistory.handler
    } else if (path === './pages/dch/common/GUI03353/forwardHistory') {
      return GUI03353ForwardHistory.handler
    } else if (path === './pages/dch/common/GUI03353/getWardAndBedriddenDegree') {
      return GUI03353GetWardAndBedriddenDegree.handler
    } else if (path === './pages/dch/common/GUI00024/GUI00024_init') {
      return GUI00024GUI00024Init.handler
    } else if (path === './pages/dch/common/GUI00024/GUI00024_redisplay') {
      return GUI00024GUI00024Redisplay.handler
    } else if (path === './pages/dch/common/GUI00050/GUI00050_init') {
      return GUI00050GUI00050Init.handler
    } else if (path === './pages/dch/common/GUI00050/GUI00050Test_init') {
      return GUI00050GUI00050TestInit.handler
    } else if (path === './pages/dch/common/GUI00054/init') {
      return GUI00054Init.handler
    } else if (path === './pages/dch/common/GUI00054/add') {
      return GUI00054Add.handler
    } else if (path === './pages/dch/common/GUI00068/init') {
      return GUI00068Init.handler
    } else if (path === './pages/dch/common/GUI00063/init') {
      return GUI00063Init.handler
    } else if (path === './pages/sample_page/hidden-item/use-sample/GUIxxxx') {
      return GUIxxxx.handler
    } else if (path === './pages/sample_page/hidden-item/input-sample/GUIyyyy') {
      return GUIyyyy.handler
    } else if (path === './pages/dch/common/GUI03296/testpage') {
      return GUI03296Testpage.handler
    } else if (path === './pages/dch/common/GUI03296/init') {
      return GUI03296Init.handler
    } else if (path === './pages/dch/common/GUI03296/redisplay') {
      return GUI03296Redisplay.handler
    } else if (path === './pages/dch/common/GUI03296/copyLastTime') {
      return GUI03296CopyLastTime.handler
    } else if (path === './pages/dch/common/GUI03296/backHistory') {
      return GUI03296BackHistory.handler
    } else if (path === './pages/dch/common/GUI03296/forwardHistory') {
      return GUI03296ForwardHistory.handler
    } else if (path === './pages/dch/common/GUI03296/import') {
      return GUI03296Import.handler
    } else if (path === './pages/dch/common/GUI00021/init') {
      return GUI00021Init.handler
    } else if (path === './pages/dch/common/GUI03266/init') {
      return GUI03266Init.handler
    } else if (path === './pages/dch/common/GUI00042/init') {
      return GUI00042Init.handler
    } else if (path === './pages/dch/common/GUI00023/init') {
      return GUI00023Init.handler
    } else if (path === './pages/dch/common/GUI00023/selectUserList') {
      return GUI00023SelectUserList.handler
    } else if (path === './pages/dch/common/GUI00023/updateStaffList') {
      return GUI00023UpdateStaffList.handler
    } else if (path === './pages/dch/common/GUI00023/updateUserList') {
      return GUI00023UpdateUserList.handler
    } else if (path === './pages/dch/common/GUI00023/checkUsableInfo') {
      return GUI00023CheckUsableInfo.handler
    } else if (path === './pages/dch/common/GUI00023/checkUserOfficeAuth') {
      return GUI00023CheckUserOfficeAuth.handler
    } else if (path === './pages/dch/common/GUI00056/init') {
      return GUI00056Init.handler
    } else if (path === './pages/dch/common/GUI03214/init') {
      return GUI03214Init.handler
    } else if (path === './pages/dch/common/GUI03214/stringFormat') {
      return GUI03214StringFormat.handler
    } else if (path === './pages/dch/common/GUI03215/init') {
      return GUI03215Init.handler
    } else if (path === './pages/dch/common/GUI03215/stringFormat') {
      return GUI03215StringFormat.handler
    } else if (path === './pages/dch/common/GUI00041/init') {
      return GUI00041Init.handler
    } else if (path === './pages/dch/common/GUI00041/office') {
      return GUI00041Office.handler
    } else if (path === './pages/dch/common/GUI00041/search') {
      return GUI00041Search.handler
    } else if (path === './pages/dch/common/GUI00041/displayInfo') {
      return GUI00041DisplayInfo.handler
    } else if (path === './pages/dch/common/GUI00033/init') {
      return GUI00033Init.handler
    } else if (path === './pages/dch/common/GUI00033/initKsg') {
      return GUI00033InitKsg.handler
    } else if (path === './pages/header/topHeaderInfo') {
      return Or00251TopHeaderInfo.handler
    } else if (path === './pages/header/noticeCount') {
      return Or00251NoticeCount.handler
    } else if (path === './pages/user-list') {
      return UserList.handler // 利用者一覧情報の取得
    } else if (path === './pages/user-list-history') {
      return UserListHistory.handler // 履歴付き利用者一覧情報の取得
    } else if (path === './pages/dch/common/GUI00021/search') {
      return GUI00021Search.handler
    } else if (path === './pages/shokuin-list') {
      return ShokuinList.handler // 職員一覧情報の取得
    } else if (path === './pages/dch/common/GUI00021/select') {
      return GUI00021Select.handler
    } else if (path === './pages/dsKghInfoNewsListFile') {
      return dsKghInfoNewsListFile.handler
    } else if (path === './pages/settingsCmnSelect') {
      return SettingsCmnSelect.handler
    } else if (path === './pages/operation_history') {
      return OperationHistory.handler // 操作履歴情報の取得
    } else if (path === './pages/login_history') {
      return LoginHistory.handler
    } else if (path === './pages/staff_list') {
      return StaffList.handler
    }
  } catch (error) {
    console.error('Error:', error)
  }

  return undefined
}

export default { post }
