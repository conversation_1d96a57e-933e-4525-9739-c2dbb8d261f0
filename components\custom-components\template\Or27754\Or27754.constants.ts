import { getSequencedCpId } from '#imports'
/**
 * Or27754:コンポーネント
 * GUI00653: ［課題検討］画面
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
export namespace Or27754Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or27754', seq)
  /**
   * 全
   */
  export const HISTORY_CATEGORY_PROCESS = ''
  /**
   *  office Name
   */
  export const OFFICE_NAME = '特別養護 ほのぼの'
  /**
   * CHECK_OPTION
   */
  export const CHECK_OPTION = [
    {
      label: '',
      value: '',
    },
    {
      label: '〇',
      value: '1',
    },
    {
      label: '×',
      value: '0',
    },
  ]
  /**
   * CONTENT_MESSAGE
   */
  export const CONTENT_MESSAGE = '選定表、検討表、A～V'
  /**
   * 全
   */
  export const STR_ALL = '全'
  /**
   * 帳票_セクション名
   */
  export const SECTION_NAME = 'インターライ方式ケアアセスメント表'
  /**
   * 期間処理区分 ダイアログ選択 当履歴
   */
  export const PERIOD_KBN_OPEN = '0'
  /**
   * 期間処理区分 前履歴
   */
  export const PERIOD_KBN_PREV = '1'
  /**
   * 期間処理区分 後履歴
   */
  export const PERIOD_KBN_NEXT = '2'
  /**
   * メニュー３名称
   */
  export const MENU_3_KNJ = '[mnu3][3GK][ITR]ｱｾｽﾒﾝﾄ'

  /**
   * 固定文字セット
   */
  export namespace STR {
    /**
     * 空文字
     */
    export const EMPTY = ''
    /**
     * 区切り文字- チルダ
     */
    export const SPLIT_TILDE = ' ~ '
    /**
     * ZERO
     */
    export const ZERO = '0'
    /**
     * ONE
     */
    export const ONE = '1'
    /**
     * TWO
     */
    export const TWO = '2'
    /**
     * THREE
     */
    export const THREE = '3'
    /**
     * FOUR
     */
    export const FOUR = '4'
    /**
     * FIVE
     */
    export const FIVE = '5'
    /**
     * SIX
     */
    export const SIX = '6'
    /**
     * SEVEN
     */
    export const SEVEN = '7'
    /**
     * EIGHT
     */
    export const EIGHT = '8'
    /**
     * NINE
     */
    export const NINE = '9'
    /**
     * TEN
     */
    export const TEN = '10'
    /**
     * ELEVEN
     */
    export const ELEVEN = '11'
    /**
     * TWELVE
     */
    export const TWELVE = '12'
    /**
     * THIRTEEN
     */
    export const THIRTEEN = '13'
    /**
     * FOURTEEN
     */
    export const FOURTEEN = '14'
    /**
     * FIFTEEN
     */
    export const FIFTEEN = '15'
    /**
     * SIXTEEN
     */
    export const SIXTEEN = '16'
    /**
     * SEVENTEEN
     */
    export const SEVENTEEN = '17'
    /**
     * EIGHTEEN
     */
    export const EIGHTEEN = '18'
    /**
     * NINETEEN
     */
    export const NINETEEN = '19'
    /**
     * TEENTY
     */
    export const TEENTY = '20'
    /**
     * TWENTI_ONE
     */
    export const TWENTI_ONE = '21'
    /**
     * TWENTI_TWO
     */
    export const TWENTI_TWO = '22'
    /**
     * TWENTI_THREE
     */
    export const TWENTI_THREE = '23'
  }
  /**
   * 数値定数
   */
  export namespace NUMBER {
    /**
     * ZERO
     */
    export const ZERO = 0
    /**
     * ONE
     */
    export const ONE = 1
    /**
     * TWO
     */
    export const TWO = 2
    /**
     * THREE
     */
    export const THREE = 3
  }
}
