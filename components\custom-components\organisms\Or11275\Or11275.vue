<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or32374Const } from '../Or32374/Or32374.constants'
import { Or32374Logic } from '../Or32374/Or32374.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or51773Const } from '../Or51773/Or51773.constants'
import { useScreenStore, useSetupChildProps, useScreenTwoWayBind } from '#imports'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or11207OnewayType } from '~/types/cmn/business/components/Or11207Type'
import type { Or27761OnewayType, Or27761Type } from '~/types/cmn/business/components/Or27761Type'
import type {
  TableItemsType,
  Or11275Type,
  valueType,
} from '~/components/custom-components/organisms/Or11275/Or11275.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or27761Logic } from '~/components/custom-components/organisms/Or27761/Or27761.logic'
import { Or27761Const } from '~/components/custom-components/organisms/Or27761/Or27761.constants'
import { Or30055Logic } from '~/components/custom-components/organisms/Or30055/Or30055.logic'
import { Or30055Const } from '~/components/custom-components/organisms/Or30055/Or30055.constants'
import { Or11275Const } from '~/components/custom-components/organisms/Or11275/Or11275.constants'

import type {
  Mo01354OnewayType,
  Mo01354Type,
  Mo01354Headers,
  Mo01354Items,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { OrX0163OnewayType } from '~/types/cmn/business/components/OrX0163Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
// import type { Mo01332OnewayType, Mo01332Type } from '~/types/business/components/Mo01332Type'
import { useValidation } from '@/utils/useValidation'
const { byteLength } = useValidation()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// const systemCommonsStore = useSystemCommonsStore()

interface Props {
  modelValue: Mo01354Type
  uniqueCpId: string
  parentCpId: string
  parentUniqueCpId: string
  onewayModelValue: Or11207OnewayType
}

const props = defineProps<Props>()

const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })
const or27761 = ref({ uniqueCpId: Or27761Const.CP_ID(0) })
const contentRef = ref<HTMLDivElement | null>(null)

// ダイアログ表示フラグ
const showDialog21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or27761Const.CP_ID(0)]: or27761.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
})

const or27761Type = ref<Or27761Type>({
  sortList: [],
})

/**
 * 親画面からの初期値
 */
const or27761Data: Or27761OnewayType = {
  indexList: [],
}

// ダイアログ表示フラグ
const showDialogOr27761 = computed(() => {
  // Or27761のダイアログ開閉状態
  return Or27761Logic.state.get(or27761.value.uniqueCpId)?.isOpen ?? false
})

// ターブルヘッダー情報
const INIT_HEADERS = [
  // {
  //   title: '',
  //   key: 'check',
  //   width: '40px',
  // },
  {
    title: '',
    key: 'n2tCd',
    width: '200px',
  },
  {
    title: '',
    key: 'memoKnj',
  },
] as Mo01354Headers[]

/**
 * 分子：表
 * 単方向バインドモデルのローカル変数
 */
const mo01354Oneway = ref<Mo01354OnewayType>({
  /** 初期値：ヘッダー情報 */
  headers: INIT_HEADERS,
  height: '',
})

const local = reactive({
  ...props.modelValue,
  Or11275: {
    isDisabled: true,
    showIndex: -1,
    showOldIndex: ['-1', '-1', '-1', '-1', '-1', '-1', '-1'],
    idIndex: 10000 as number,
    selectInfo: [] as string[],
  },
})

const localOneway = reactive({
  copyFlg: false,
  delFlg: '',
  Or11275: {
    ...props.onewayModelValue,
  },
  // 行追加
  mo11275OneWay: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    width: '89px',
    minWidth: '89px',
    height: '32px',
    disabled: local.Or11275.isDisabled,
  } as Mo00611OnewayType,
  // 行挿入
  mo11275OneWayInsert: {
    // デフォルト値の設定
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    width: '89px',
    minWidth: '89px',
    height: '32px',
    disabled: local.Or11275.isDisabled,
  } as Mo00009OnewayType,
  // 行複写
  mo11275OneWayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    width: '89px',
    minWidth: '89px',
    height: '32px',
    disabled: local.Or11275.isDisabled,
  } as Mo00611OnewayType,
  // 行削除
  mo11275OneWayDel: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    width: '89px',
    minWidth: '89px',
    height: '32px',
    disabled: local.Or11275.isDisabled,
  } as Mo01265OnewayType,
  // 表示順
  mo11275OnewayEdit: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // 表示順
  mo00611OnewayEdit: {
    btnLabel: t('label.display-order'), // ボタンラベル
    width: '65px',
    minWidth: '65px',
    height: '32px',
    disabled: false,
    prependIcon: '',
    appendIcon: '',
  } as Mo00611OnewayType,
  or51775OnewayTypeOther: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '7',
    t3Cd: '',
    tableName: 'cpn_tuc_myg_ass2',
    columnName: 'memo1_knj',
    assessmentMethod: '2',
    inputContents: '',
    shokuinId: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
  orX0163Oneway: {
    height: '40px',
    readOnly: false,
    maxLength: '93',
    rules: [byteLength(93)],
  } as OrX0163OnewayType,
  mo01338_1Oneway: {
    value: '',
    customClass: {
      outerClass: 'pa-0',
      itemStyle: 'font-weight: 700;',
    } as CustomClass,
  } as Mo01338OnewayType,
  //特記事項ID
  mo01282Id1Oneway: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  mo01282Id2Oneway: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  mo01282Id3Oneway: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  mo01282Id4Oneway: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  mo01282Id5Oneway: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  mo01282Id6Oneway: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  mo01282Id7Oneway: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  // mo01332OnewayType: {
  //   showItemLabel: false,
  //   items: [{ label: '', value: '1' }],
  //   isVertical: false,
  //   customClass: { itemClass: 'mt-1 ml-3' } as CustomClass,
  // } as Mo01332OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or11275Type>({
  cpId: Or11275Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * init
 **************************************************/

onMounted(() => {
  if (props.onewayModelValue.copyFlg) {
    localOneway.copyFlg = props.onewayModelValue.copyFlg
  }
  getCommonInfo()
})

/**
 * 特記事項初期情報取得
 */
function init() {
  // 特記事項初期情報取得。
  const tokkiList2 = Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]
    .tokkiList[0]
  // refValue.value!.tokkiList.splice(0)
  refValue.value!.tokkiList = []
  localOneway.mo01282Id1Oneway.items = []
  localOneway.mo01282Id2Oneway.items = []
  localOneway.mo01282Id3Oneway.items = []
  localOneway.mo01282Id4Oneway.items = []
  localOneway.mo01282Id5Oneway.items = []
  localOneway.mo01282Id6Oneway.items = []
  localOneway.mo01282Id7Oneway.items = []
  if (tokkiList2) {
    // 再割り当て
    tokkiList2.tokkiList.map((item, index) => {
      const tableDataItems: Mo01354Items[] = []
      item.NtokkiList?.forEach((ntokki) => {
        tableDataItems.push({
          id: ntokki.counter,
          n1tCd: ntokki.n1tCd,
          n2tCd: { modelValue: ntokki.n2tCd },
          memoKnj: { value: ntokki.memoKnj },
          seqNo: ntokki.seqNo,
          modifiedCnt: ntokki.modifiedCnt,
          // check: { values: [] },
        })
      })
      // 再割り当て
      refValue.value!.tokkiList.push({
        n1tCd: item.NtokkiList[0].n1tCd,
        tokkiTitle: item.tokkiTitle,
        tokkiTextList: item.tokkiTextList,
        tableData: {
          values: {
            selectedRowId: '-1',
            selectedRowIds: [],
            items: tableDataItems,
          },
        },
      })
      item.tokkiTextList.forEach((items) => {
        items.kTokkiTextList.forEach((iteme) => {
          if (index === Or11275Const.NUMBER_0) {
            localOneway.mo01282Id1Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_1) {
            localOneway.mo01282Id2Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_2) {
            localOneway.mo01282Id3Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_3) {
            localOneway.mo01282Id4Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_4) {
            localOneway.mo01282Id5Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_5) {
            localOneway.mo01282Id6Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_6) {
            localOneway.mo01282Id7Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          }
        })
      })
    })
    useScreenStore().setCpTwoWay({
      cpId: Or11275Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: refValue.value,
      isInit: true,
    })
  }
}
// 選択した行の第１階層Index
const selectedItemIndex = ref<number>(-1)

// 選択した行の第２階層Index
const selectedItemSubIndex = ref<number>(-1)
/**
 * 「ケアマネ入力支援アイコンボタン」押下onClickOther
 *
 * @param index - 第１階層Index
 *
 * @param subIndex - 第２階層Index
 */
function onClickOther(index: number, subIndex: number) {
  // その他1の入力支援ポップアップを開く
  or51775Other.value.modelValue = ''

  selectedItemIndex.value = index
  selectedItemSubIndex.value = subIndex

  // タイトルを「その他1」または「その他2」に設定
  localOneway.or51775OnewayTypeOther.title = t('label.certification-survey-led-special')

  // 入力支援ポップアップを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
//課題t入力
const or51775Other = ref({ modelValue: '' })

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or51773Const.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  const typeValue = refValue.value!.tokkiList[selectedItemIndex.value].tableData.values.items[
    selectedItemSubIndex.value - 1
  ].n2tCd as unknown as valueType
  // メモ１の場合

  typeValue.value = setOrAppendValue(typeValue.value ?? '', data)
  refValue.value!.tokkiList[selectedItemIndex.value].tableData.values.items[
    selectedItemSubIndex.value - 1
  ].n2tCd = typeValue
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * 共通情報取得
 */

function getCommonInfo() {
  // const commonInfo = TeX0008Logic.data.get(props.parentUniqueCpId)
  // if (commonInfo) {
  //   local.commonInfo.activeTabId = commonInfo.activeTabId
  //   local.commonInfo.cc1Id = commonInfo.cc1Id
  //   local.commonInfo.createUserId = commonInfo.createUserId
  //   local.commonInfo.createYmd = commonInfo.createYmd
  //   local.commonInfo.jigyoId = commonInfo.jigyoId
  //   local.commonInfo.modifiedCnt = commonInfo.modifiedCnt
  //   local.commonInfo.ninteiFormF = commonInfo.ninteiFormF
  //   local.commonInfo.sc1Id = commonInfo.sc1Id
  //   local.commonInfo.listSection = commonInfo.listSection
  // }
}

/**
 * データ型の再定義
 */
interface MyType {
  id: string
  n1tCd: string
  n2tCd: { modelValue: string }
  memoKnj: { value: string }
  // check: Mo01332Type
}

/**
 * 行追加ボタン押下
 */
const addRow = () => {
  // はんてい
  if (
    Number(refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId) < 1
  ) {
    return
  }
  // タイプの再定義
  const obj: MyType = {
    id: String(
      local.Or11275.idIndex +
        refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.length +
        1
    ),
    n1tCd: refValue.value!.tokkiList[local.Or11275.showIndex].n1tCd,
    n2tCd: { modelValue: '' },
    memoKnj: { value: '' },
    // check: { values: [] },
  }
  // 追加行を選択状態にする
  refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.push(obj)
  local.Or11275.idIndex += 1
}

/**
 * 行挿入ボタン押下
 */
const insertRow = () => {
  // はんてい
  if (
    Number(refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId) < 1
  ) {
    return
  }
  // findIndex
  const index = refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.findIndex(
    (item) =>
      item.id === refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId
  )
  // 新規行を選択状態にする
  refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.splice(index + 1, 0, {
    id: String(
      local.Or11275.idIndex +
        refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.length +
        1
    ),
    n1tCd: refValue.value!.tokkiList[local.Or11275.showIndex].n1tCd,
    n2tCd: { modelValue: '' },
    memoKnj: { value: '' },
    // check: { values: [] },
  })
  local.Or11275.idIndex += 1
}

/**
 * 行複写
 */
const copyRow = () => {
  // はんてい
  if (
    Number(refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId) < 1
  ) {
    return
  }
  const newValue = {
    values: {
      selectedRowId: '-1',
      selectedRowIds: [],
      items: [] as Mo01354Items[],
    },
  } as Mo01354Type
  // 選択行の項目を複写し、一覧の最終に追加する
  newValue.values.items = refValue.value!.tokkiList[
    local.Or11275.showIndex
  ].tableData.values.items.filter(
    (item) =>
      item.id === refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId
  )
  const selItems = newValue?.values?.items
  // 選択データ
  const selData = selItems?.find(
    (item) =>
      item.id === refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId
  ) as unknown as TableItemsType

  const n1tCd = selData.n1tCd
  const n2tCd = selData.n2tCd.modelValue
  const memoKnj = selData.memoKnj.value
  // 複写行を選択状態にする
  refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.push({
    id: String(
      local.Or11275.idIndex +
        refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.length +
        1
    ),
    n1tCd: n1tCd,
    n2tCd: {
      modelValue: n2tCd,
    },
    memoKnj: {
      value: memoKnj,
    },
    // check: { values: [] },
  })
  local.Or11275.idIndex += 1
}

const isDeleteRow = () => {
  // 現在選択している特記事項は一行のみの場合
  if (
    Number(refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId) < 1 ||
    refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.length === 1
  ) {
    return
  }
  // 一行以外の場合
  showOr21814MsgTwoBtn(t('message.i-cmn-11314'))
}

/**
 * 確認ダイアログの開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'destroy1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 行削除ボタン押下
 */
const deleteRow = () => {
  // はんてい
  if (
    Number(refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId) < 1 ||
    refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.length === 1
  ) {
    return
  }
  // findIndex
  const index = refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.findIndex(
    (item) =>
      item.id === refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId
  )
  refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.splice(index, 1)
  // 現在選択している特記事項は一行のみの場合、削除しない。
  if (
    refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items.length >=
    index + 1
  ) {
    refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId =
      refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items[index].id
  } else {
    refValue.value!.tokkiList[local.Or11275.showIndex + 1].tableData.values.selectedRowId = '1'
  }
}

/**
 * 表示順
 */
const indicationOrder = () => {
  // 表を選択するかどうかを判断する
  if (local.Or11275.showIndex < 0) {
    return
  }
  // テーブルのデータが選択されているかどうかを判断する
  if (
    Number(refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.selectedRowId) < 0
  ) {
    return
  }
  // 割り当て＃ワリアテ＃ dialog
  const selItems = refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items
  const selectedItems = selItems as unknown as TableItemsType[]
  or27761Data.indexList = []
  selectedItems.forEach((item, index) => {
    or27761Data.indexList.push({
      id: item.counter,
      sort: index + 1,
      number: item.n1tCd,
      content: item.n2tCd.modelValue,
      sortBackup: '',
      memoKnj: item.memoKnj.value,
      seqNo: item.seqNo,
      modifiedCnt: item.modifiedCnt,
    })
  })
  // Or27761のダイアログ開閉状態を更新する
  Or27761Logic.state.set({
    uniqueCpId: or27761.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 変更されたデータを受け取る
 */
function tablDataValueChange(data: {
  sortList: [
    {
      sortBackup: string
      memoKnj: string
      seqNo: string
      modifiedCnt: string
      id: string
      number: string
      content: string
    },
  ]
}) {
  // 再割り当て
  refValue.value!.tokkiList[local.Or11275.showIndex].tableData.values.items = data.sortList.map(
    (item) => {
      return {
        id: item.id,
        n1tCd: item.number,
        n2tCd: {
          modelValue: item.content,
        },
        sortBackup: item.sortBackup,
        memoKnj: { value: item.memoKnj },
        seqNo: item.seqNo,
        modifiedCnt: item.modifiedCnt,
        // check: { values: [] },
      }
    }
  )
}

// リスニングテーブル値の変化
watch(
  () => refValue.value!.tokkiList,
  (newValue) => {
    local.Or11275.selectInfo = []

    // findIndx
    newValue.forEach((item) => {
      local.Or11275.selectInfo.push(item.tableData.values.selectedRowId)
    })
  },
  { deep: true }
)

watch(
  () => local.Or11275.selectInfo,
  (newValue, oldValue) => {
    if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
      return
    }
    let index = -1
    if (newValue.filter((x) => x !== '-1').length > 1) {
      const diffIndexes = local.Or11275.showOldIndex
        .map((val, idx) => (val !== newValue[idx] ? idx : '-1'))
        .filter((idx) => idx !== '-1')
      index = diffIndexes[0]
      newValue.forEach((item, i) => {
        if (i !== index) {
          item = '-1'
        }
      })
      refValue.value!.tokkiList.forEach((item, i) => {
        if (i !== index) {
          refValue.value!.tokkiList[i].tableData.values.selectedRowId = '-1'
        }
      })
      local.Or11275.showOldIndex = newValue
    } else {
      local.Or11275.showOldIndex = newValue
      index = newValue.findIndex((item) => item !== '-1')
    }
    // check
    // if (index !== -1) {
    //   refValue.value!.tokkiList.forEach((item, id) => {
    //     item.tableData.values.items.forEach((items) => {
    //       items.check = { values: [] }
    //       if (items.id === newValue[index] && id === index) {
    //         items.check = { values: [Or11275Const.STR_1] }
    //       }
    //     })
    //   })
    // }

    // findIndex
    local.Or11275.showIndex = index
    if (index === -1) {
      local.Or11275.isDisabled = true
    } else {
      nextTick()
        .then(() => {
          // ボタンの状態を変更
          localOneway.mo11275OneWayInsert.disabled = false
          localOneway.mo11275OneWay.disabled = false
          localOneway.mo11275OneWayCopy.disabled = false
          localOneway.mo11275OneWayDel.disabled = false
        })
        .catch(() => {})
    }
  },
  { deep: true }
)

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    } else {
      // 行削除ボタン
      if (newValue.firstBtnClickFlg) {
        deleteRow()
      }
    }
  }
)

/**
 * or30055データ変更
 */
watch(
  () => Or30055Logic.tabInfo.get(props.onewayModelValue.parentId),
  (newValue) => {
    if (newValue) {
      if (
        newValue.tabId === '7' &&
        (newValue.state === Or30055Const.GET_DATA || newValue.state === Or30055Const.CHANGE_HIS)
      ) {
        init()
        localOneway.mo11275OneWayInsert.disabled = true
        localOneway.mo11275OneWay.disabled = true
        localOneway.mo11275OneWayCopy.disabled = true
        localOneway.mo11275OneWayDel.disabled = true
      }
      // else if (newValue.tabId === '7' && newValue.state === Or30055Const.SAVE_BUTTON) {
      // }
    }
  }
)

/**
 * getRefValue data
 */
const defaultValueChange = () => {
  const tokkiList: {
    counter: string
    n1tCd: string
    n2tCd: string
    memoKnj: string
    modified_cnt: string
  }[] = []
  const array = cloneDeep(refValue.value)
  array?.tokkiList.forEach((item) => {
    item?.tableData.values.items.forEach((val) => {
      const arr = val as unknown as TableItemsType
      tokkiList.push({
        counter: val.id,
        n1tCd: arr.n1tCd,
        n2tCd: arr.n2tCd.modelValue,
        memoKnj: arr.memoKnj.value,
        modified_cnt: arr.modifiedCnt,
      })
    })
  })
  return cloneDeep(tokkiList)
}
/**
 * defineExport dataChange
 */
defineExpose({ defaultValueChange })

const getDate4Copy = () => {
  // 特記事項初期情報取得。
  const tokkiList2 = Or32374Logic.data.get(props.onewayModelValue.copyParentId)?.tokkiList[0]
  // refValue.value!.tokkiList.splice(0)
  refValue.value!.tokkiList = []
  localOneway.mo01282Id1Oneway.items = []
  localOneway.mo01282Id2Oneway.items = []
  localOneway.mo01282Id3Oneway.items = []
  localOneway.mo01282Id4Oneway.items = []
  localOneway.mo01282Id5Oneway.items = []
  localOneway.mo01282Id6Oneway.items = []
  localOneway.mo01282Id7Oneway.items = []
  if (tokkiList2) {
    // 再割り当て
    tokkiList2.tokkiList.map((item, index) => {
      const tableDataItems: Mo01354Items[] = []
      item.NtokkiList?.forEach((ntokki) => {
        tableDataItems.push({
          id: ntokki.counter,
          n1tCd: ntokki.n1tCd,
          n2tCd: { modelValue: ntokki.n2tCd },
          memoKnj: { value: ntokki.memoKnj },
          seqNo: ntokki.seqNo,
          modifiedCnt: ntokki.modifiedCnt,
          // check: { values: [] },
        })
      })
      // 再割り当て
      refValue.value!.tokkiList.push({
        n1tCd: item.NtokkiList[0].n1tCd,
        tokkiTitle: item.tokkiTitle,
        tokkiTextList: item.tokkiTextList,
        tableData: {
          values: {
            selectedRowId: '-1',
            selectedRowIds: [],
            items: tableDataItems,
          },
        },
      })
      item.tokkiTextList.forEach((items) => {
        items.kTokkiTextList.forEach((iteme) => {
          if (index === Or11275Const.NUMBER_0) {
            localOneway.mo01282Id1Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_1) {
            localOneway.mo01282Id2Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_2) {
            localOneway.mo01282Id3Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_3) {
            localOneway.mo01282Id4Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_4) {
            localOneway.mo01282Id5Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_5) {
            localOneway.mo01282Id6Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          } else if (index === Or11275Const.NUMBER_6) {
            localOneway.mo01282Id7Oneway.items?.push({
              title: iteme.tokkiId + Or11275Const.SPACES + iteme.tokkiText,
              value: iteme.tokkiId,
            })
          }
        })
      })
    })
  }

  // 特記事項初期情報取得。
  // const tokkiList2 = Or32374Logic.data.get(props.onewayModelValue.copyParentId)?.tokkiList[0]
  // refValue.value!.tokkiList.splice(0)
  // if (tokkiList2) {
  //   // 再割り当て
  //   tokkiList2.tokkiList.map((item) => {
  //     const tableDataItems: Mo01354Items[] = []
  //     item.NtokkiList?.forEach((ntokki) => {
  //       tableDataItems.push({
  //         id: ntokki.counter,
  //         n1tCd: ntokki.n1tCd,
  //         n2tCd: { modelValue: ntokki.n2tCd },
  //         memoKnj: { value: ntokki.memoKnj },
  //         seqNo: ntokki.seqNo,
  //         modifiedCnt: ntokki.modifiedCnt,
  //         // check: { values: [] },
  //       })
  //     })
  //     // 再割り当て
  //     refValue.value!.tokkiList.push({
  //       tokkiTitle: item.tokkiTitle,
  //       tokkiTextList: item.tokkiTextList,
  //       tableData: {
  //         values: {
  //           selectedRowId: '-1',
  //           selectedRowIds: [],
  //           items: tableDataItems,
  //         },
  //       },
  //     })
  // })
  //}
}

/**
 * 複写監視
 */
watch(
  () => Or32374Logic.tabInfo.get(props.onewayModelValue.copyParentId),
  (newValue) => {
    if (newValue) {
      // 初期化
      if (
        newValue.tabId === Or32374Const.TAB_7 &&
        (newValue.state === Or32374Const.GET_INIT_DATA1 ||
          newValue.state === Or32374Const.GET_INIT_DATA2 ||
          newValue.state === Or32374Const.GET_INIT_DATA3)
      ) {
        getDate4Copy()
      }
    }
  }
)

watch(
  () => Or30055Logic.delFlg.get(props.onewayModelValue.parentId),
  (newValue) => {
    if (newValue) {
      localOneway.delFlg = newValue.delFlg!
    }
  }
)
</script>
<template>
  <c-v-container
    ref="contentRef"
    class="container-block container"
  >
    <c-v-divider />
    <c-v-sheet
      v-if="
        (Or30055Logic.data.get(props.onewayModelValue.parentId) != undefined &&
          localOneway.delFlg === Or30055Const.DELFLG_TYPE0) ||
        localOneway.copyFlg === true
      "
      class="sheet-block"
    >
      <c-v-row
        align="center"
        no-guitter
      >
        <c-v-col
          :cols="10"
          style="padding: 24px 0 16px 0 !important"
        >
          <!--行追加-->
          <base-mo00611
            class="mr-0"
            :oneway-model-value="localOneway.mo11275OneWay"
            @click="addRow"
          >
            <c-v-tooltip
              activator="parent"
              location="top"
              :text="t('tooltip.care-plan2-newline-btn')"
            />
          </base-mo00611>
          <!--行挿入-->
          <base-mo00611
            class="ml-2"
            :oneway-model-value="localOneway.mo11275OneWayInsert"
            @click="insertRow"
          >
            <c-v-tooltip
              activator="parent"
              location="top"
              :text="t('tooltip.care-plan2-insertline-btn')"
            />
          </base-mo00611>
          <!--行複写-->
          <base-mo00611
            class="ml-2"
            :oneway-model-value="localOneway.mo11275OneWayCopy"
            @click="copyRow"
          >
            <c-v-tooltip
              activator="parent"
              location="top"
              :text="t('tooltip.care-plan2-cpyline-btn')"
            />
          </base-mo00611>
          <!--行削除-->
          <base-mo01265
            class="ml-2"
            :oneway-model-value="localOneway.mo11275OneWayDel"
            @click="isDeleteRow"
          >
            <c-v-tooltip
              activator="parent"
              location="top"
              :text="t('tooltip.care-plan2-deleteline-btn')"
            />
          </base-mo01265>
        </c-v-col>
        <c-v-col
          :cols="2"
          style="
            display: flex !important;
            justify-content: flex-end !important;
            padding: 24px 0 16px 0 !important;
          "
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.display-order')"
          ></c-v-tooltip>
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OnewayEdit"
            @click="indicationOrder"
          />
        </c-v-col>
      </c-v-row>
    </c-v-sheet>
    <div
      v-for="(val, index) in refValue!.tokkiList"
      :key="index"
      class="sheet-block-color"
    >
      <c-v-row class="table-title pt-2 pb-2">{{ val.tokkiTitle }}</c-v-row>
      <div class="w-100 pa-3"></div>
      <c-v-col
        cols="12"
        :class="['pa-4', 'multiline', 'font-weight-bold', 'black-bordered', 'list-wrapper-left']"
      >
        <c-v-row
          v-for="(item, index1) in val.tokkiTextList"
          :key="index1"
          class="table-btn-all"
        >
          <c-v-row
            v-for="(itemInfo, index2) in item.kTokkiTextList"
            :key="index2"
            class="table-btn-all"
            align="center"
          >
            <base-mo01338
              :oneway-model-value="{
                ...localOneway.mo01338_1Oneway,
                value: itemInfo.tokkiId + Or11275Const.SPACE + itemInfo.tokkiText,
              }"
            />
          </c-v-row>
        </c-v-row>
      </c-v-col>
      <div class="w-100 pa-3"></div>
      <base-mo-01354
        v-model="val.tableData"
        hide-default-header
        hide-default-footer
        :oneway-model-value="mo01354Oneway"
        class="list-wrapper"
      >
        <!-- 分子：表用チェックフィールド -->
        <!-- <template #[`item.check`]="{ item }">
          <base-mo01332
            :key="item.check"
            :model-value="{ values: [...item.check.values] }"
            :oneway-model-value="localOneway.mo01332OnewayType"
            @click.prevent
          ></base-mo01332>
        </template> -->
        <!-- ID（ラベル） -->
        <template #[`item.n2tCd`]="{ item }">
          <!-- ※※※本来は表用ラベルを使用する※※※ -->
          <base-mo-01282
            v-model="item.n2tCd"
            class="fontSize"
            :oneway-model-value="
              index === Or11275Const.NUMBER_0
                ? localOneway.mo01282Id1Oneway
                : index === Or11275Const.NUMBER_1
                  ? localOneway.mo01282Id2Oneway
                  : index === Or11275Const.NUMBER_2
                    ? localOneway.mo01282Id3Oneway
                    : index === Or11275Const.NUMBER_3
                      ? localOneway.mo01282Id4Oneway
                      : index === Or11275Const.NUMBER_4
                        ? localOneway.mo01282Id5Oneway
                        : index === Or11275Const.NUMBER_5
                          ? localOneway.mo01282Id6Oneway
                          : localOneway.mo01282Id7Oneway
            "
          />
        </template>
        <!-- 列２（テキストフィールド） -->
        <template #[`item.memoKnj`]="{ item }">
          <!-- 分子：表用数値専用テキストフィールド -->
          <div style="display: flex">
            <g-custom-or-x-0163
              v-model="item.memoKnj"
              :oneway-model-value="localOneway.orX0163Oneway"
              @on-click-edit-btn="onClickOther(index, item.id)"
            ></g-custom-or-x-0163>
          </div>
        </template>
      </base-mo-01354>
      <div class="w-100 pa-3"></div>
    </div>
  </c-v-container>
  <g-custom-or-27761
    v-if="showDialogOr27761"
    v-bind="or27761"
    v-model="or27761Type"
    :oneway-model-value="or27761Data"
    @update:model-value="tablDataValueChange"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialog21814"
    v-bind="or21814"
  />
  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Other"
    :oneway-model-value="localOneway.or51775OnewayTypeOther"
    @confirm="handleOr51775Confirm"
  ></g-custom-or-51775>
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';
.sheet-block {
  position: relative;
  z-index: 1;
  background-color: rgb(var(--v-theme-background)) !important;
  .v-row {
    margin: 0;
  }
  .table-title {
    font-weight: bolder;
    padding: 4px 12px;
    background-color: #e6e6e6 !important;
  }
  .table-btn {
    display: flex;
    .v-btn {
      min-width: 54px !important;
      max-width: 54px;
    }
    span {
      display: inline-block;
      word-wrap: break-word;
      white-space: normal;
    }
  }
  .table-btn-all {
    display: inline-block;
    align-items: center;
    justify-content: start;
    flex-wrap: wrap;
    padding: 4px 12px;
  }
}
.sheet-block-color {
  position: relative;
  z-index: 1;
  background-color: #ffffff !important;
  .v-row {
    margin: 0;
  }
  .table-title {
    padding: 4px 12px;
    height: 48px;
    display: flex;
    align-items: center;
    background-color: #e6e6e6 !important;
  }
  .table-btn {
    display: flex;
    .v-btn {
      min-width: 54px !important;
      max-width: 54px;
    }
    span {
      display: inline-block;
      word-wrap: break-word;
      white-space: normal;
    }
  }
  .table-btn-all {
    display: inline-block;
    align-items: center;
    justify-content: start;
    flex-wrap: wrap;
    padding: 0px 8px;
  }
  :deep(.list-wrapper) {
    width: calc(100% - 24px);
    margin-left: 12px;
    tr {
      height: 50px;
    }
  }
}
.container-block {
  max-width: 100%;
  padding: 0px;
}
.black-bordered {
  border: 1px rgb(var(--v-theme-black-800)) solid;
}
.list-wrapper-left {
  width: calc(100% - 48px);
  margin-left: 24px;
}
:deep(.fontSize) {
  .v-select__selection-text {
    font-size: 12px !important;
  }
}
</style>
