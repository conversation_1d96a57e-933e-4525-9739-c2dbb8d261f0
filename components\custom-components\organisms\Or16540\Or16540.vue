<script setup lang="ts">
/**
 *Or16540：有機体：(実施計画➀マスタ)課題等の省略選択
 */
import { reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or16540Const } from './Or16540.constants'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import { useScreenTwoWayBind } from '#imports'
import type { Or16540Type } from '~/types/cmn/business/components/Or16540Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or16540Type
  uniqueCpId: string
}
const props = defineProps<Props>()

const localOneway = reactive({
  mo00018Oneway: {
    name: t('label.implement-plan-one-content-is-blank-omitted'),
    itemLabel: t('label.implement-plan-one-content-is-blank-omitted'),
    showItemLabel: false,
    hideDetails: true,
    checkboxLabel: t('label.implement-plan-one-content-is-blank-omitted'),
  } as Mo00018OnewayType,
  mo00018Twoway: {
    name: t('label.implement-plan-one-content-is-same-omitted'),
    itemLabel: t('label.implement-plan-one-content-is-same-omitted'),
    showItemLabel: false,
    hideDetails: true,
    checkboxLabel: t('label.implement-plan-one-content-is-same-omitted'),
  } as Mo00018OnewayType,
  mo00039Oneway: {
    // デフォルト値の設定
    name: Or16540Const.CP_ID(0),
    itemLabel: t('label.level-of-care-required'),
    showItemLabel: false,
    hideDetails: true,
  } as Mo00039OnewayType,
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or16540Type>({
  cpId: Or16540Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
</script>

<template>
  <c-v-row
    no-gutters
    class="text-center"
    style="padding-top: 0px; padding-bottom: 0px; padding-right: 0px; padding-left: 0px"
  >
    <base-mo00018
      v-if="refValue"
      v-model="refValue!.mo00018ContentIsBlankOmitted"
      :oneway-model-value="localOneway.mo00018Oneway"
    ></base-mo00018>
    <base-mo00018
      v-if="refValue"
      v-model="refValue!.mo00018ContentIsSameOmitted"
      :oneway-model-value="localOneway.mo00018Twoway"
    ></base-mo00018>
  </c-v-row>
</template>

<style scoped lang="scss">
.text-center {
  align-items: baseline;
}
</style>
