<script setup lang="ts">
/**
 * Or27653:有機体:内容マスタモーダル
 * GUI00935_内容マスタ
 *
 * @description
 * 内容マスタ画面
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or00725Const } from '../Or00725/Or00725.constants'
import { Or00734Const } from '../Or00734/Or00734.constants'
import { OrX0037Const } from '../OrX0037/OrX0037.constants'
import type { AsyncFunction, Or27653StateType } from './Or27653.type'
import { Or27653Const } from './Or27653.constants'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { hasRegistAuth, useScreenOneWayBind, useSetupChildProps } from '#build/imports'
import { useGyoumuCom } from '~/utils/useGyoumuCom'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or27653OnewayType } from '~/types/cmn/business/components/Or27653Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or00734OnewayType } from '~/types/cmn/business/components/Or00734Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or21814OnewayType, Or21814EventType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Or21815StateType, Or21815EventType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import type { OrX0037OnewayType } from '~/types/cmn/business/components/OrX0037Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or27653OnewayType
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
/**
 * ユーティリティを提供するフック
 */
const gyoumuCom = useGyoumuCom()
// ローカルTwoway
const local = reactive({
  // 本画面ダイアログ
  mo00024: {
    isOpen: Or27653Const.DEFAULT.IS_OPEN,
  } as Mo00024Type,
  mo00043: { id: Or27653Const.TAB.TAB_ID_CONTENT } as Mo00043Type,
  mo00043Transfer: { id: Or27653Const.TAB.TAB_ID_CONTENT } as Mo00043Type,
  mo00043Change: { id: Or27653Const.TAB.TAB_ID_CONTENT } as Mo00043Type,
})

// ローカルOneway
const localOneway = reactive({
  // 本画面
  or27653Oneway: {
    ...props.onewayModelValue,
  },
  or00734OneWay: {
    ...props.onewayModelValue,
  } as Or00734OnewayType,
  // 本画面ダイアログOneway
  mo00024Oneway: {
    width: '900px',
    height: '1100px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Or27653',
      toolbarTitle: t('label.content-master'),
      toolbarTitleCenteredFlg: false,
      toolbarName: '',
      showCardActions: true,
      cardTextClass: 'card-text pa-0',
    },
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609SaveOneway: {
    btnLabel: t('btn.save'),
    // disabled: !(await hasRegistAuth(Or27653Const.LINK_AUTH_CONTENT)),
  } as Mo00609OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or27653Const.TAB.TAB_ID_WEEK_TABLE, title: t('label.week-table') },
      { id: Or27653Const.TAB.TAB_ID_DAILY_TABLE, title: t('label.daily-table') },
      { id: Or27653Const.TAB.TAB_ID_CONTENT, title: t('label.content-master') },
      {
        id: Or27653Const.TAB.TAB_ID_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES,
        title: t('label.daily-life-and-nursing-care-services-examples'),
      },
    ],
    tabClass: 'pa-2'
  } as Mo00043OnewayType,
  mo01338Fourway: {
    value: t('label.system-common-save'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-0',
      itemClass: 'ml-0 align-left',
      itemStyle: 'color:  rgb(var(--v-theme-black-500));',
    }),
  } as Mo01338OnewayType,
  orX0037OneWay: {
    shisetsuId: Number(props.onewayModelValue.shisetuId),
    svJigyoId: props.onewayModelValue.svJigyoId,
    officeData: {
      officeId: props.onewayModelValue.svJigyoId,
      officeName:''
    }
  } as OrX0037OnewayType,
})
const or21814_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
const or00734 = ref({ uniqueCpId: Or00734Const.CP_ID(1) })
const or00725 = ref({ uniqueCpId: Or00725Const.CP_ID(1) })
const orX0037 = ref({ uniqueCpId: OrX0037Const.CP_ID(1) })
const or00725Ref = ref<{ save: AsyncFunction; _cleanEditFlag: () => void; _getGetEditFlgFlag: AsyncFunction }>()
const or00734Ref = ref<{ save: AsyncFunction; _cleanEditFlag: () => void; _getGetEditFlgFlag: AsyncFunction }>()


/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27653StateType>({
  cpId: Or27653Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or27653Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00725Const.CP_ID(1)]: or00725.value,
  [Or00734Const.CP_ID(1)]: or00734.value,
  [OrX0037Const.CP_ID(1)]: orX0037.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
})

/**************************************************
 * ウォッチャー
 **************************************************/

/**************************************************
 * 関数
 **************************************************/
onMounted(async () => { })

// メニュー切替
watch(
  () => local.mo00043Transfer.id,
  async (newValue) => {
    if (local.mo00043.id !== newValue) {
      local.mo00043Transfer.id = local.mo00043.id
      local.mo00043Change.id = newValue
      let auth = Or27653Const.LINK_AUTH_CONTENT
      if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_CONTENT) {
        auth = Or27653Const.LINK_AUTH_CONTENT
      } else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_DAILY_TABLE) {
        auth = Or27653Const.LINK_AUTH_DAILY_TABLE
      } else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_WEEK_TABLE) {
        auth = Or27653Const.LINK_AUTH_WEEK_TABLE
      }else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES) {
        auth = Or27653Const.LINK_AUTH_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES
      }
      if (
        !(await gyoumuCom.checkEdit(
          await editFlg(),
          await hasRegistAuth(auth),
          showConfirmMessageBox,
          showWarnMessageBox,
          insert,
          cancel
        ))
      ) {
        return
      }
      cleanEditFlag()
      local.mo00043.id = newValue
      local.mo00043Transfer.id = local.mo00043.id
      localOneway.mo00024Oneway.mo01344Oneway.name = local.mo00043Transfer.id
      switchTitle();
      localOneway.mo00609SaveOneway.disabled = !await hasRegistAuth(auth)
    }
  }
)

function switchTitle() {
  if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_WEEK_TABLE) {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.week-table')
  } else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_DAILY_TABLE) {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.daily-table')
    localOneway.mo01338Fourway.value = t('label.save-by-bussiness-unit')
  } else if (
    local.mo00043Transfer.id ===
    Or27653Const.TAB.TAB_ID_CONTENT
  ) {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.content-master')
    localOneway.mo01338Fourway.value = t('label.system-common-save')
  } else if (
    local.mo00043Transfer.id ===
    Or27653Const.TAB.TAB_ID_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES
  ) {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t(
      'label.daily-life-and-nursing-care-services-examples'
    )
  }
}

function cancel() {
  // キャンセル選択時は複写データの作成を行わずに終了する
  local.mo00043Change.id =  local.mo00043.id
  local.mo00043Transfer.id = local.mo00043Change.id
  localOneway.mo00024Oneway.mo01344Oneway.name = local.mo00043Transfer.id
  switchTitle()
}

/**
 * 保存
 */
async function save() {
  await insert()
}

/**
 * 保存
 */
async function insert() {
  if (local.mo00043.id === Or27653Const.TAB.TAB_ID_CONTENT) {
    return await or00725Ref.value?.save() ?? true
  } else if(local.mo00043.id === Or27653Const.TAB.TAB_ID_DAILY_TABLE)
  {
    return await or00734Ref.value?.save() ?? true
  }
  return true
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  let auth = Or27653Const.LINK_AUTH_CONTENT
  if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_CONTENT) {
    auth = Or27653Const.LINK_AUTH_CONTENT
  } else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_DAILY_TABLE) {
    auth = Or27653Const.LINK_AUTH_DAILY_TABLE
  } else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_WEEK_TABLE) {
    auth = Or27653Const.LINK_AUTH_WEEK_TABLE
  }else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES) {
    auth = Or27653Const.LINK_AUTH_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES
  }
  if (
    !(await gyoumuCom.checkEdit(
      await editFlg(),
      await hasRegistAuth(auth),
      showConfirmMessageBox,
      showWarnMessageBox,
      insert
    ))
  ) {
    return
  }
  cleanEditFlag();
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => local.mo00024.emitType,
  async () => {
    if (local.mo00024.emitType === 'closeBtnClick') {
      await close()
      local.mo00024.emitType = undefined
    }
  }
)

/**
 * メッセージを表示する
 */
const showConfirmMessageBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * 確認ダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}
/**
 * メッセージを表示する
 */
const showWarnMessageBox = async () => {
  return await openWarnDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.w-com-10006'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
}

/**
 * メッセージを表示する
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openWarnDialog(state: Or21815StateType): Promise<Or21815EventType | undefined> {
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

function cleanEditFlag() {
  if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_CONTENT) {
    or00725Ref.value?._cleanEditFlag()
  } else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_DAILY_TABLE) {
    or00734Ref.value?._cleanEditFlag()
  }
}
/**
 * ナビゲーション制御領域のいずれかの編集フラグがON
 */
const editFlg = async function getGetEditFlgFlag() {
  if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_CONTENT) {
    return await or00725Ref.value?._getGetEditFlgFlag() ?? false
  } else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_DAILY_TABLE) {
    return await or00734Ref.value?._getGetEditFlgFlag() ?? false
  } else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_WEEK_TABLE) {
    return false;
  }else if (local.mo00043Transfer.id === Or27653Const.TAB.TAB_ID_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES) {
    return false;
  }
  return false
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043Transfer"
        style="padding: 0px !important"
        :oneway-model-value="localOneway.mo00043OneWay"
      ></base-mo00043>
      <c-v-window v-model="local.mo00043.id" >
        <c-v-window-item value="GUI00977">
          <!-- タブ：週間表 -->
          <g-custom-or-x-0037
            v-if="local.mo00043.id === Or27653Const.TAB.TAB_ID_WEEK_TABLE"
            v-bind="orX0037"
            :oneway-model-value="localOneway.orX0037OneWay"
            :parent-unique-cp-id="props.uniqueCpId"
          ></g-custom-or-x-0037> </c-v-window-item>
        <c-v-window-item value = 'GUI00987'>
          <c-v-sheet class="pt-6">
            <c-v-row>
              <c-v-col>
              <!--日課表-->
              <g-custom-or00734
                v-if="local.mo00043.id === Or27653Const.TAB.TAB_ID_DAILY_TABLE"
                v-bind="or00734"
                ref="or00734Ref"
                :oneway-model-value="localOneway.or00734OneWay"
                :parent-unique-cp-id="props.uniqueCpId"
                :unique-cp-id="or00734.uniqueCpId"
              > </g-custom-or00734>
              </c-v-col>
            </c-v-row>
          </c-v-sheet>
        </c-v-window-item>
        <c-v-window-item value="GUI00935">
          <c-v-sheet class="content">
            <c-v-row class="ma-0">
              <c-v-col class="v-col pa-0">
                <!--内容-->
                <g-custom-or00725
                  v-if="local.mo00043.id === Or27653Const.TAB.TAB_ID_CONTENT"
                  v-bind="or00725"
                  ref="or00725Ref"
                  class="intentionList"
                  :unique-cp-id="or00725.uniqueCpId"
                  :parent-unique-cp-id="props.uniqueCpId"
                >
                </g-custom-or00725></c-v-col
            ></c-v-row>
          </c-v-sheet>
        </c-v-window-item>
        <c-v-window-item value="GUI00988"> 日常生活・介護サービス例 </c-v-window-item>
      </c-v-window>
      <c-v-row
        no-gutters
        class="label-comment"
      >
        <c-v-col style="padding: 8px 8px 8px 8px !important">
          <base-mo01338 :oneway-model-value="localOneway.mo01338Fourway" /> </c-v-col
      ></c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-0 mr-2"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609SaveOneway"
          class="mx-0 mr-0"
          @click="save"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21814 v-bind="or21814_1"></g-base-or21814>
  <g-base-or21815 v-bind="or21815_1"></g-base-or21815>
</template>

<style scoped lang="scss">
.view {
  width: 1080px;
  display: flex;
  margin: 0px;
  flex-direction: column;
}

.v-divider {
  margin-top: 8px;
  margin-bottom: 8px;
}

.intentionList {
  height: 250px;
}

.buttonArea {
  button:not(:nth-child(1)) {
    margin-left: 8px;
  }
}
.label-comment {
  color: rgb(var(--v-theme-black-536)) !important;
  margin: 0px 0px 0px 0px;
  height: 10px;
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: 82px;
}
</style>
