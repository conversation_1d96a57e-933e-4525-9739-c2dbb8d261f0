<script setup lang="ts">
/**
 * OrX0066：有機体： タイトルリスト(週間計画パターン設定)
 * GUI01044_週間計画パターン（設定）
 * 週間計画パターン（設定）
 *
 * @description
 * 週間計画パターン（設定）
 *
 * <AUTHOR>
 */
import { reactive, nextTick, ref, watch, computed, onMounted, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'

import { Or35774Const } from '../Or35774/Or35774.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { OrX0172Const } from '../OrX0172/OrX0172.constants'
import type { OrX0048CalendarConfig, OrX0048CalendarEvent } from '../OrX0048/OrX0048.type'
import type { OrX0049CalendarConfig, OrX0049CalendarEvent } from '../OrX0049/OrX0049.type'
import { Or27610Logic } from '../Or27610/Or27610.logic'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51773Const } from '../Or51773/Or51773.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { OrX0066Const } from './OrX0066.constants'
import type { OrX0066StateType, OrX0066ValuesType } from './OrX0066.type'
import type { OrX0066OnewayType, OrX0066Type } from '~/types/cmn/business/components/OrX0066Type'

import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01299OnewayType } from '~/types/business/components/Mo01299Type'

import type {
  WeekPlanPatternSettingSelectInEntity,
  WeekPlanPatternSettingSelectOutEntity,
} from '~/repositories/cmn/entities/WeekPlanPatternSettingSelectEntity'
import {
  useScreenTwoWayBind,
  useScreenOneWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
  useScreenStore,
} from '#imports'

import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or27610Const } from '~/components/custom-components/organisms/Or27610/Or27610.constants'
import type {
  DataTableData,
  WeeklyPlan,
} from '~/components/custom-components/organisms/Or27610/Or27610.type'
import { OrX0048Const } from '~/components/custom-components/organisms/OrX0048/OrX0048.constants'
import { OrX0049Const } from '~/components/custom-components/organisms/OrX0049/OrX0049.constants'
import type { Or35921Type } from '~/types/cmn/business/components/Or35921Type'
import type { cks52List } from '~/types/cmn/business/components/Or05349Type'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type {
  WeekPlanPatternSettingUpdateInEntity,
  WeekPlanPatternSettingUpdateOutEntity,
} from '~/repositories/cmn/entities/WeekPlanPatternSettingUpdateEntity'
import type {
  WeekPlanPatternSettingsDetailSelectInEntity,
  WeekPlanPatternSettingsDetailSelectOutEntity,
} from '~/repositories/cmn/entities/WeekPlanPatternSettingsDetailSelectEntity'
import { useValidation } from '@/utils/useValidation'
import type { Mo01282Type } from '~/types/business/components/Mo01282Type'
import type { Or27610OnewayType } from '~/types/cmn/business/components/Or27610Type'
import type { OrX0172OnewayType, OrX0172Type } from '~/types/cmn/business/components/OrX0172Type'
const { byteLength } = useValidation()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0066OnewayType
  modelValue: OrX0066Type
  or35921Model: Or35921Type
  uniqueCpId: string
  parentUniqueCpId: string
}
const props: Props = defineProps<Props>()

const defaultModelValue: OrX0066Type = {
  editFlg: false,
  //共通情報.計画書様式
  cksFlg: 0,
  //親画面.有効期間ID
  orgTermId: '',
  //画面.有効期間ID
  termId: '',
  newCks51List: [],
  newCks54List: [],
  newCks52List: [],
  initFlg: false,
  selectedNursingCareRequired: '',
  selectedTitle: { modelValue: '' } as Mo00040Type,
  selectedTermId: '',
  resultCks51List: [],
  resultCks54List: [],
  resultCks52List: [],
}
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
// 有効期間
const periodDisp = ref<string>('')
// 時間帯内容
const timeContent = ref<string>('')
// 保険編集フラグ
const hokenEditFlg = ref<boolean>(false)
// 保険のサービス
const hokenService = ref<string>('')
// 頻度
const frequency = ref<string>('')
// 選択した行のindex
const selectedItemIndex = ref<number>(1)

const mo00024 = ref<Mo00024Type>({
  isOpen: OrX0066Const.DEFAULT.IS_OPEN,
})

const showDialogOr21813 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 確認ダイアログの表示状態を返すComputed
 */
const showDialogOr21814_1 = computed(() => {
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 確認ダイアログの表示状態を返すComputed
 */
const showDialogOr21814_2 = computed(() => {
  return Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 警告ダイアログの表示状態を返すComputed
 */
const showDialogOr21815_1 = computed(() => {
  return Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 警告ダイアログの表示状態を返すComputed
 */
const showDialogOr21815_2 = computed(() => {
  return Or21815Logic.state.get(or21815_2.value.uniqueCpId)?.isOpen ?? false
})

// カレンダーの設定
const calendarConfig48 = ref<OrX0048CalendarConfig>({
  events: [],
  cellHeight: 22,
  zoom: 1,
})
const calendarConfig49 = ref<OrX0049CalendarConfig>({
  events: [],
  timeSlotStartHour: 4,
  timeSlotEndHour: 4,
  cellHeight: 22,
  zoom: 1,
})
const data = ref<DataTableData>({ dataList: [] })
const calendar = ref({ reRenderCalendar: Function })
/**
 * 1 時間を目盛として、時間枠生成
 */
const hourSlots = computed(() => {
  const slots = []
  for (let h = 0; h < 24; h++) {
    slots.push(
      `${h.toString().padStart(2, OrX0066Const.DEFAULT.NUMBER_ZERO)}:${OrX0066Const.DEFAULT.NUMBER_DB_ZERO}`
    )
  }
  return slots
})
const mo00009OnewayWeekly = ref({
  btnIcon: 'open_in_new',
  minWidth: '20px',
} as Mo00009OnewayType)
const mo00009OnewayHoken = ref({
  btnIcon: 'open_in_new',
  minWidth: '20px',
} as Mo00009OnewayType)

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
const or51775Other = ref({ modelValue: '' })

const localOneway = reactive({
  orX0066Oneway: {
    ...props.onewayModelValue,
  },
  // タイトル
  mo00615Oneway: {
    itemLabel: t('label.title'),
  } as Mo00615OnewayType,
  //タイトルプルダウン
  mo00040Oneway: {
    itemLabel: OrX0066Const.CP_ID(0),
    showItemLabel: false,
    isRequired: false,
    items: OrX0066Const.DEFAULT.ITEMS,
    width: '300px',
  } as Mo00040OnewayType,
  // タイトル: 表用テキストフィールド
  orX0172Oneway: {
    inputMode: OrX0172Const.INPUT_MODE.TEXT_ONLY,
    text: {
      orX0172InputOneway: {
        rules: [],
      },
    },
    // maxLength: '20',
  } as OrX0172OnewayType,

  // ******要介護度ラジオグループセクション******
  nursingCareRequiredRaidoGroupOneway: {
    name: 'nursingCareRequiredRadioGroup',
    showItemLabel: false,
    hideDetails: true,
  },

  // 有効期間 ラベル
  mo01299OnewayValidPeriod: {
    anchorPoint: 'ss-1',
    title: t('label.validPeriod'),
  } as Mo01299OnewayType,

  // 週間取込ボタン
  PeriodImportMo00610Oneway: {
    name: 'weekImport',
    btnLabel: t('btn.weekImport'),
    width: '180px',
    minWidth: '40px',
    minHeight: '40px',
    labelColor: 'blue-400',
    color: 'blue-400',
  } as Mo00610OnewayType,

  mo00046onewayWeekly: {
    // maxLength: 360,
    maxLength: 4000,
    rules: [byteLength(4000)],
    rows: 3,
    maxRows: '3',
    isVerticalLabel: false,
    showItemLabel: false,
    autoGrow: false,
    noResize: true,
  } as Mo00046Type,
  mo00046onewayHoken: {
    maxLength: 360,
    rows: 3,
    maxRows: '3',
    isVerticalLabel: false,
    showItemLabel: false,
    autoGrow: false,
    noResize: true,
  } as Mo00046Type,
  or51775OnewayModel: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
  } as Or51775OnewayType,
  or27610Oneway: {
    /**
     *index
     */
    index: 0,
    /**
     *週間計画詳細リスト
     */
    weekPlanDetailList: [],
    /**
     *週間計画加算リスト
     */
    weekPlanAdditionList: [],
    /**
     *週間計画担当者リスト
     */
    weekPlanManagerList: [],
    /**
     *週間計画隔週リスト
     */
    weekPlanEveryOtherWeekList: [],
    /**
     *週間計画月日リスト
     */
    weekPlanDateList: [],
    /**
     *初期設定マスタ情報
     */
    initialConfigureMasterInfo: null,
    /**
     *メインパラメータ
     */
    mainParameters: null,
    /**
     *計画書様式
     */
    carePlanStyle: 2,
    /**
     *週以外フラグ
     */
    weekOtherThanFlag: false,
    /**
     *選択された週間計画詳細データID
     */
    selectWeekPlanDetailDataId: '',
    /**
     *パターンモード
     */
    patternMode: false,
    /**
     *利用者ID
     */
    userId: '',
    /**
     *有効期間ID
     */
    validPeriodId: '',
    /**
     *新規モード
     */
    newMode: false,
    /**
     *事業所ID
     */
    officeId: '',
    /**
     * 操作パタン(パターンモード)
     */
    operationPattern: '',
    /**
     * 計画開始時間(開始時間)
     */
    kaishiJikan: '',
    /**
     * 計画終了時間(終了時間)
     */
    shuuryouJikan: '',
    /**
     * 処理日
     */
    processingDate: '',
    /**
     * 開始日(検索サービスの開始日)
     */
    startDate: '',
    /**
     * 終了日(検索サービスの終了日)
     */
    endDate: '',
    /**
     *  機能名
     */
    featureName: '',
  } as Or27610OnewayType,
})

const local = reactive({
  orX0066: {
    ...defaultModelValue,
    ...props.modelValue,
  },
  weeklyModel: {
    value: '',
  } as Mo00046Type,
  hokenModel: {
    value: '',
  } as Mo00046Type,
  mo00024: {
    isOpen: true,
  },
  // 有効期間リスト
  validPeriodList: [] as CodeType[],
  // 要介護度リスト
  nursingCareRequiredList: [] as CodeType[],
  // 主な日常生活内容
  njModelArr: [] as OrX0172Type[],
})

const mo00040Tiles = ref<string[]>([])
/**************************************************
 * Pinia
 **************************************************/
useScreenTwoWayBind<OrX0066ValuesType>({
  cpId: OrX0066Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<OrX0066ValuesType> }

useScreenOneWayBind<OrX0066StateType>({
  cpId: OrX0066Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or35774Const.DEFAULT.IS_OPEN
    },
  },
})

const nursingCareRequiredRadio = ref({ uniqueCpId: '' })
const orX0048 = ref({ uniqueCpId: '' })
const orX0049 = ref({ uniqueCpId: '' })
const or27610 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21814_2 = ref({ uniqueCpId: '' })
const or21814_3 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
const or21815_2 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0048Const.CP_ID(0)]: orX0048.value,
  [OrX0049Const.CP_ID(0)]: orX0049.value,
  [Or27610Const.CP_ID(0)]: or27610.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or21814Const.CP_ID(3)]: or21814_3.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
  [Or21815Const.CP_ID(1)]: or21815_2.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
})
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()

  // エラーダイアログを初期化(e.cmn.40794)
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40794'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // データ変更確認ダイアログを初期化(i.cmn.10430)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを初期化(i.cmn.10903)
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10903'),
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })

  // 確認ダイアログを初期化(i.cmn.10902)
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10902'),
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })

  // ワーニング用
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20797'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  Or21815Logic.state.set({
    uniqueCpId: or21815_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20798'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 有効期間
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_VALID_PERIOD },
    // 要介護度ラジオリストの選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_YOUKAIGO },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 有効期間
  local.validPeriodList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_VALID_PERIOD)

  // 要介護度ラジオリスト
  local.nursingCareRequiredList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_YOUKAIGO)
  local.orX0066.selectedNursingCareRequired = local.nursingCareRequiredList[0].value
}

/**
 * 週間計画パターン設定情報取得
 * AC001_初期情報取得
 */
async function init() {
  // 汎用コードマスタデータを取得し初期化
  void initCodes()

  const inputParam: WeekPlanPatternSettingSelectInEntity = {
    // 共通情報.計画書様式
    ssmCksFlg: localOneway.orX0066Oneway.cksFlg,
    //親画面.利用者
    userId: localOneway.orX0066Oneway.userId,
    //共通情報.システム年月日
    appYmd: systemCommonsStore.getSystemDate ?? '',
  }

  //週間計画パターンの初期情報を取得する。
  const ret: WeekPlanPatternSettingSelectOutEntity = await ScreenRepository.select(
    'weekPlanPatternSettingsInitSelect',
    inputParam
  )

  // 要介護度デフォルト
  localOneway.orX0066Oneway.yoKaigoDo = ret.data.yoKaigoDo
  // 履歴リスト
  localOneway.orX0066Oneway.cks51List = ret.data.cks51List
  if (ret.data.cks51List.length > 0) {
    if (ret.data.detailList.length > 0) {
      // 日常リスト
      localOneway.orX0066Oneway.cks54List = ret.data.detailList[0].cks54List
      localOneway.orX0066Oneway.newCks54List = localOneway.orX0066Oneway.cks54List
      // 詳細リスト
      localOneway.orX0066Oneway.cks52List = ret.data.detailList[0].cks52List
      localOneway.orX0066Oneway.newCks52List = localOneway.orX0066Oneway.cks52List
    }

    //初期選択されたタイトル設定 プルダウンの1件目
    if (local.orX0066.selectedTitle) {
      local.orX0066.selectedTitle.modelValue = localOneway.orX0066Oneway.cks51List[0].nameKnj
    }
    local.orX0066.selectedTermId = localOneway.orX0066Oneway.cks51List[0].termId

    // 週単位以外のサービス 履歴リスト.週単位以外ｻｰﾋﾞｽ
    local.weeklyModel.value = localOneway.orX0066Oneway.cks51List[0].wIgaiKnj
  }

  if (
    localOneway.orX0066Oneway.cks51List !== undefined &&
    localOneway.orX0066Oneway.cks51List.length > 0
  ) {
    for (const item of localOneway.orX0066Oneway.cks51List) {
      mo00040Tiles.value.push(item.nameKnj)
    }
  }
  // タイトルプルダウンアイテム設定
  localOneway.mo00040Oneway.items = mo00040Tiles.value
  // 有効期間内容設定
  setValidPeriodDisp(local.orX0066.selectedTermId)

  // 日常リスト.主な日常生活上の活動
  if (
    localOneway.orX0066Oneway.newCks54List !== undefined &&
    localOneway.orX0066Oneway.newCks54List.length > 0
  ) {
    for (const item of localOneway.orX0066Oneway.newCks54List) {
      local.njModelArr.push({ value: item.nichijoKnj })
    }
  }

  // 履歴リスト
  local.orX0066.newCks51List = localOneway.orX0066Oneway.cks51List
  // 詳細リスト
  local.orX0066.newCks52List = localOneway.orX0066Oneway.cks52List
  // 日常リスト
  local.orX0066.newCks54List = localOneway.orX0066Oneway.cks54List
}

/**
 * 履歴リスト.有効期間IDによって、有効期間リスト.コード内容を取得する
 *
 * @param termId - 有効期間ID
 */
function setValidPeriodDisp(termId: string) {
  local.validPeriodList.forEach((item) => {
    if (item.value === termId) {
      periodDisp.value = item.label
    }
  })
}

/**
 * AC004「タイトル」変更
 * 選択タイトルが対応する「週間計画ID」 より、週間計画パターン明細再取得する、
 */
async function onClickTitle() {
  // AC002-1を実行
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case OrX0066Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC008を実行し、処理続き。
        await onClickSave()
        return
      case OrX0066Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case OrX0066Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：処理終了。
        return
    }
  }
  if (local.orX0066.selectedTitle?.modelValue !== '') {
    // （行タイトル）時間、時間帯ラベルの設定：項目一覧の表示データを参照する。
    // 週間計画パターン明細再取得する。 選択タイトルが対応する「週間計画ID」
    const param: WeekPlanPatternSettingsDetailSelectInEntity = {
      ks51Id: local.orX0066.selectedTermId,
      termId: localOneway.orX0066Oneway.validPeriod ?? '',
    }
    //週間計画パターンタイトル情報保存
    const retData: WeekPlanPatternSettingsDetailSelectOutEntity = await ScreenRepository.select(
      'weekPlanPatternSettingsDetailSelect',
      param
    )

    if (local.orX0066.newCks51List !== undefined && local.orX0066.newCks51List.length > 0) {
      local.orX0066.selectedTermId = local.orX0066.newCks51List[0].termId
      // 日常リスト
      local.orX0066.newCks54List = retData.data.cks54List
      // 詳細リスト
      local.orX0066.newCks52List = retData.data.cks52List

      setValidPeriodDisp(local.orX0066.selectedTermId)
      // ・(保険サービス)：親画面.(保険サービス)
      local.hokenModel.value = props.or35921Model.hokenService ?? ''
      // 詳細リスト
      doContentSetting(local.orX0066.newCks52List)
      // 生活リスト
      if (local.orX0066.newCks54List !== undefined && local.orX0066.newCks54List.length > 0) {
        let count = 1
        // 初期化
        local.njModelArr.splice(0)
        for (const cks54 of local.orX0066.newCks54List) {
          if (count === parseInt(cks54.seq)) {
            // ・主な日常生活内容ラベル＝週間計画日常リスト.主な日常生活上の活動
            local.njModelArr.push({ value: cks54.nichijoKnj })
          } else {
            local.njModelArr.push({ value: '' })
          }
          count = count + 1
        }
      }
      if (local.njModelArr.length < 24) {
        for (let i = local.njModelArr.length; i < 24; i++) {
          local.njModelArr.push({ value: '' })
        }
      }
      // 週単位以外のサービス 履歴リスト.週単位以外ｻｰﾋﾞｽ
      local.weeklyModel.value = local.orX0066.newCks51List[0].wIgaiKnj
    }
  }
}

/**
 * AC005「要介護度」変更
 * 画面.タイトル設定：要介護度が対応するタイトル設定する。
 */
async function changeYoKaigoDo() {
  // AC002-1を実行
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case OrX0066Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC008を実行し、処理続き。
        await onClickSave()
        return
      case OrX0066Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case OrX0066Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：処理終了。
        return
    }
  }

  // 画面.タイトル設定：要介護度が対応するタイトル設定する。
  const mo00040FilterTiles = ref<string[]>([])
  if (
    localOneway.orX0066Oneway.cks51List !== undefined &&
    localOneway.orX0066Oneway.cks51List.length > 0
  ) {
    if (local.orX0066.selectedNursingCareRequired !== local.nursingCareRequiredList[0].value) {
      for (const item of localOneway.orX0066Oneway.cks51List.filter(
        (item) => item.termId === local.orX0066.selectedNursingCareRequired
      )) {
        mo00040FilterTiles.value.push(item.nameKnj)
      }
    } else {
      for (const item of localOneway.orX0066Oneway.cks51List) {
        mo00040FilterTiles.value.push(item.nameKnj)
      }
    }
  }
  if (mo00040FilterTiles.value.length > 0) {
    // プルダウン
    localOneway.mo00040Oneway.items = mo00040FilterTiles.value
    if (local.orX0066.selectedTitle !== undefined) {
      local.orX0066.selectedTitle.modelValue = String(localOneway.mo00040Oneway.items[0])
    }
    setValidPeriodDisp(local.orX0066.selectedTermId)
  } else {
    if (local.orX0066.selectedTitle) {
      local.orX0066.selectedTitle.modelValue = ''
    }
    localOneway.mo00040Oneway.items = []
  }
}

/**
 * AC006 「週間取込ボタン」押下
 */
async function weekImport() {
  // タイトルが選択されない場合 e-cmn-40794
  if (local.orX0066.selectedTitle === undefined || local.orX0066.selectedTitle.modelValue === '') {
    await openErrorDialog()
    return
  } else {
    // i-cmn-10903
    const dialogResult = await openInfoDialog(OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10903)
    switch (dialogResult) {
      case OrX0066Const.DEFAULT.DIALOG_RESULT_YES:
        // 親画面からのデータを選択タイトルデータと明細データに上書きする。
        await nextTick()
        weekImportAfter()
        break
      case OrX0066Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理終了。
        return
    }
  }
}

function weekImportAfter() {
  // 共通情報.計画書様式が「居宅」の場合
  if (
    localOneway.orX0066Oneway.cksFlg !== undefined &&
    localOneway.orX0066Oneway.cksFlg !== OrX0066Const.DEFAULT.SHISETSU
  ) {
    if (props.or35921Model.orignValidId !== undefined && props.or35921Model.orignValidId > 0) {
      // 有効期間文字を取得する。 親画面.有効期間ID
      setValidPeriodDisp(String(props.or35921Model.orignValidId))
      // ・(保険サービス)：親画面.(保険サービス)
      local.hokenModel.value = props.or35921Model.hokenService ?? ''
      if (props.or35921Model.cks52List !== undefined) {
        // 詳細データ：親画面.詳細リスト
        doContentSetting(props.or35921Model.cks52List)
      }
      // ・週単位以外のサービステキストエリア＝週間計画履歴.週単位以外サービス
      local.weeklyModel.value = props.or35921Model.shuUnitOtherService ?? ''
      // 親画面を画面項目に上書きする。
      if (props.or35921Model?.cks54List !== undefined && props.or35921Model?.cks54List.length > 0) {
        let count = 1
        // 初期化
        local.njModelArr.splice(0)
        for (const cks54 of props.or35921Model.cks54List) {
          if (count === parseInt(cks54.seq)) {
            // ・主な日常生活内容ラベル＝週間計画日常リスト.主な日常生活上の活動
            local.njModelArr.push({ value: cks54.nichijoKnj })
          } else {
            local.njModelArr.push({ value: '' })
          }
          count = count + 1
        }
      }
    }
  }
  // 履歴リスト
  local.orX0066.newCks51List = localOneway.orX0066Oneway.cks51List
  if (local.orX0066.newCks51List !== undefined && local.orX0066.newCks51List.length > 0) {
    local.orX0066.selectedTermId = local.orX0066.newCks51List[0].termId
    // 日常リスト
    local.orX0066.newCks54List = localOneway.orX0066Oneway.cks54List
    // 詳細リスト
    local.orX0066.newCks52List = localOneway.orX0066Oneway.cks52List

    setValidPeriodDisp(local.orX0066.selectedTermId)
  }
}

const showData = ref<DataTableData>({
  dataList: [],
})

/**
 *  詳細リスト設定
 *
 * @param cks52List - 詳細リスト
 */
function doContentSetting(cks52List: cks52List[]) {
  hokenEditFlg.value = false
  local.hokenModel.value = ''
  // カレンダークリア
  showData.value?.dataList.splice(0)
  data.value?.dataList.splice(0)
  calendarConfig49.value.events = []
  calendarConfig48.value.events = []
  // カレンダーの画面を反映
  calendar.value?.reRenderCalendar()
  // 週間計画詳細リストより繰り返し、時間帯内容ラベルを作成する。
  if (cks52List !== undefined && cks52List.length > 0) {
    for (const cks52 of cks52List) {
      // 時間帯データ
      const wp = {
        id: '',
        startTime: OrX0066Const.DEFAULT.TIME_ZERO,
        endTime: OrX0066Const.DEFAULT.TIME_ZERO,
        outFrameDisplay: { modelValue: false },
        dayOfWeek1: false,
        dayOfWeek2: false,
        dayOfWeek3: false,
        dayOfWeek4: false,
        dayOfWeek5: false,
        dayOfWeek6: false,
        dayOfWeek7: false,
        dayOfWeekOther: false,
        contents: '',
        manager: [''],
        frequency: '',
        insuranceType: '',
        insuranceBussiness: '',
        insuranceServices: '',
        insuranceAddition: '',
        fontSize: {
          modelValue: '12',
        },
        fontSizeTitle: {
          value: '12',
        },
        textPosition: {
          modelValue: '0',
        },
        omission: {
          modelValue: 1,
        },
        color: '#333333',
        background: '#E6E6E6',
        timeDisplay: {
          modelValue: 0,
        },
      } as WeeklyPlan
      // 週間計画詳細リスト.曜日の最後の一桁が（週単位）以外の場合
      if (
        cks52.youbi.substring(cks52.youbi.length - 1, cks52.youbi.length) !==
        OrX0066Const.DEFAULT.WEEK_UNIT
      ) {
        wp.fontSize.modelValue = cks52.fontSize
        wp.textPosition = { modelValue: cks52.alignment }
        wp.color = cks52.fontColor
        wp.background = cks52.backColor
        wp.startTime = cks52.kaishiJikan
        wp.endTime = cks52.shuuryouJikan
        let count = 1
        for (const str of cks52.youbi.split('')) {
          if (count === 1 && str === '1') {
            wp.dayOfWeek1 = true
          }
          if (count === 2 && str === '1') {
            wp.dayOfWeek2 = true
          }
          if (count === 3 && str === '1') {
            wp.dayOfWeek3 = true
          }
          if (count === 4 && str === '1') {
            wp.dayOfWeek4 = true
          }
          if (count === 5 && str === '1') {
            wp.dayOfWeek5 = true
          }
          if (count === 6 && str === '1') {
            wp.dayOfWeek6 = true
          }
          if (count === 7 && str === '1') {
            wp.dayOfWeek7 = true
          }
          count = count + 1
          if (count === cks52.youbi.length) {
            break
          }
        }
        // 共通情報. 計画書様式が施設の場合
        if (localOneway.orX0066Oneway.cksFlg === OrX0066Const.DEFAULT.SHISETSU) {
          // 共通情報.サービス職種IDor種類表示フラグが表示の場合
          if (localOneway.orX0066Oneway.shuruihyoujiFlg) {
            // 週間計画詳細リスト.週間計画担当者リストがNULL以外の場合
            if (
              props.or35921Model.cks56List !== undefined &&
              props.or35921Model.cks56List.length > 0
            ) {
              // 週間計画詳細リスト.週間計画担当者リストより繰り返し、変数.時間帯内容を編集する。
              for (const cks56 of props.or35921Model.cks56List) {
                // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.週間計画担当者リスト.担当者名称+改行コード
                timeContent.value =
                  timeContent.value + cks56.shokushuKnj + OrX0066Const.DEFAULT.LINE_BREAK
              }
            }
          }
        }
        // 共通情報. 計画書様式が居宅「2」、或いは、共通情報.事業所CDが予防介護支援「50010」の場合
        if (
          localOneway.orX0066Oneway.cksFlg === OrX0066Const.DEFAULT.ITAKU ||
          localOneway.orX0066Oneway.defSvJigyoCd === '50010'
        ) {
          // 共通情報.サービス職種IDor種類表示フラグが表示の場合
          if (localOneway.orX0066Oneway.shuruihyoujiFlg) {
            // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.サービス種類名称+改行コード
            timeContent.value =
              timeContent.value + cks52.svShuruiKnj + OrX0066Const.DEFAULT.LINE_BREAK
          }
          // 共通情報.事業者表示フラグが正式の場合
          if (localOneway.orX0066Oneway.jigyoShowFlg === 0) {
            // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.サービス事業者名称+改行コード
            timeContent.value =
              timeContent.value + cks52.svJigyoKnj + OrX0066Const.DEFAULT.LINE_BREAK
          }
          // 共通情報.事業者表示フラグが略称の場合
          if (localOneway.orX0066Oneway.jigyoShowFlg === 1) {
            // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.サービス事業者略称+改行コード
            timeContent.value =
              timeContent.value + cks52.svJigyoRyaku + OrX0066Const.DEFAULT.LINE_BREAK
          }
          // 共通情報.サービス項目表示フラグが表示の場合
          if (localOneway.orX0066Oneway.komokuShowFlg) {
            // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.サービス項目名称+改行コード
            timeContent.value =
              timeContent.value + cks52.svItemKnj + OrX0066Const.DEFAULT.LINE_BREAK
          }
        }
        // 共通情報. 内容表示フラグが表示の場合
        if (localOneway.orX0066Oneway.contentShowFlg) {
          // ・変数.時間帯内容＝変数.時間帯内容+週間計画詳細リスト.内容
          timeContent.value = timeContent.value + cks52.naiyouKnj
        }
        // 週間計画詳細リスト.時間表示区分が先頭表示の場合
        if (cks52.timeKbn === OrX0066Const.DEFAULT.TIMD_DISP_KBN_FIRST) {
          // ・変数.時間帯内容＝週間計画詳細リスト.開始時間+" ～ " + 週間計画詳細リスト.終了時間 + 変数.時間帯内容
          timeContent.value =
            cks52.kaishiJikan + t('label.wavy') + cks52.shuuryouJikan + timeContent.value
        }
        // 週間計画詳細リスト.時間表示区分が最後表示の場合
        if (cks52.timeKbn === OrX0066Const.DEFAULT.TIMD_DISP_KBN_LAST) {
          // ・変数.時間帯内容＝変数.時間帯内容 + 週間計画詳細リスト.開始時間+" ～ " + 週間計画詳細リスト.終了時間
          timeContent.value =
            timeContent.value + cks52.kaishiJikan + t('label.wavy') + cks52.shuuryouJikan
        }
        // 変数.時間帯内容が空白ではない場合
        if (timeContent.value !== '') {
          // ・変数.時間帯内容最後の改行コードを除く
          timeContent.value.substring(
            0,
            timeContent.value.length - OrX0066Const.DEFAULT.LINE_BREAK.length
          )
          // ・時間帯内容ラベル(該当時間帯)＝変数.時間帯内容
          wp.contents = timeContent.value
          // ※ 表示文字サイズなど情報はAC001-5参照
          // ・変数.時間帯内容＝空白
          timeContent.value = ''
          showData.value?.dataList.push(wp)
          data.value?.dataList.push(wp)
        }
      }
      // 週間計画詳細リスト.曜日の最後の一桁が（週単位）の場合
      else {
        // 共通情報.サービス職種IDor種類表示フラグが表示の場合
        if (localOneway.orX0066Oneway.shuruihyoujiFlg) {
          // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.サービス種類名称が空白ではない場合
          if (local.hokenModel.value === '' && cks52.svShuruiKnj !== '') {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス種類名称+ ”(”
            hokenService.value = hokenService.value + cks52.svShuruiKnj + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス種類名称
            hokenService.value = hokenService.value + cks52.svShuruiKnj
          }
        }
        // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.週単位以外文字が空白ではない、且つ、週間計画詳細リスト.週単位以外文字が「2」ではない場合
        if (local.hokenModel.value === '' && cks52.igaiMoji !== '' && cks52.igaiMoji !== '2') {
          // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.週単位以外文字+ ”(”
          hokenService.value = hokenService.value + cks52.igaiMoji + t('label.left-bracket')
          // ・変数.保険編集フラグをTRUEにセットする。
          hokenEditFlg.value = true
        } else {
          // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.週単位以外文字
          hokenService.value = hokenService.value + cks52.igaiMoji
        }
        // 共通情報. 内容表示フラグが表示の場合
        if (localOneway.orX0066Oneway.contentShowFlg) {
          // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.サービス項目名称が空白ではない、且つ、週間計画詳細リスト.時間表示区分が[2]の場合
          if (
            local.hokenModel.value === '' &&
            cks52.svItemKnj !== '' &&
            cks52.timeKbn === OrX0066Const.DEFAULT.TIMD_DISP_KBN_LAST
          ) {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.内容+ ”(”
            hokenService.value = hokenService.value + cks52.naiyouKnj + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.内容
            hokenService.value = hokenService.value + cks52.naiyouKnj
          }
        }
        // 共通情報.事業者表示フラグが正式の場合
        if (localOneway.orX0066Oneway.jigyoShowFlg === 0) {
          // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.サービス事業者名称が空白ではない場合
          if (local.hokenModel.value === '' && cks52.svJigyoKnj !== '') {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス事業者名称+ ”(”
            hokenService.value = hokenService.value + cks52.svJigyoKnj + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス事業者名称
            hokenService.value = hokenService.value + cks52.svJigyoKnj
          }
        }
        // 共通情報.事業者表示フラグが略称の場合
        if (localOneway.orX0066Oneway.jigyoShowFlg === 1) {
          // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.サービス事業者略称が空白ではない場合
          if (local.hokenModel.value === '' && cks52.svJigyoRyaku !== '') {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス事業者略称+ ”(”
            hokenService.value = hokenService.value + cks52.svJigyoRyaku + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス事業者略称
            hokenService.value = hokenService.value + cks52.svJigyoRyaku
          }
        }
        // 共通情報.サービス項目表示フラグが表示の場合
        if (localOneway.orX0066Oneway.komokuShowFlg) {
          // 保険のサービステキストエリアが空白、且つ、週間計画詳細リスト.サービス項目名称が空白ではない場合
          if (local.hokenModel.value === '' && cks52.svItemKnj !== '') {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス項目名称+ ”(”
            hokenService.value = hokenService.value + cks52.svItemKnj + t('label.left-bracket')
            // ・変数.保険編集フラグをTRUEにセットする。
            hokenEditFlg.value = true
          } else {
            // ・変数.保険のサービス＝ 変数.保険のサービス+週間計画詳細リスト.サービス項目名称
            hokenService.value = hokenService.value + cks52.svItemKnj
          }
        }
        // 週間計画詳細リスト.時間表示区分が先頭表示の場合
        if (cks52.timeKbn === OrX0066Const.DEFAULT.TIMD_DISP_KBN_FIRST) {
          // ・変数.保険のサービス＝週間計画詳細リスト.開始時間+" ～ " + 週間計画詳細リスト.終了時間 + 変数.保険のサービス
          hokenService.value =
            cks52.kaishiJikan + t('label.wavy') + cks52.shuuryouJikan + hokenService.value
        }
        // 週間計画詳細リスト.時間表示区分が最後表示の場合
        if (cks52.timeKbn === OrX0066Const.DEFAULT.TIMD_DISP_KBN_LAST) {
          // ・変数.保険のサービス＝変数.保険のサービス + 週間計画詳細リスト.開始時間+" ～ " + 週間計画詳細リスト.終了時間
          hokenService.value =
            hokenService.value + cks52.kaishiJikan + t('label.wavy') + cks52.shuuryouJikan
        }
        // 変数.保険編集フラグがTRUEの場合
        if (hokenEditFlg.value) {
          // ・変数.保険のサービス＝変数.保険のサービス+")、"
          hokenService.value = hokenService.value + t('label.right-bracket') + t('label.comma')
          // ・変数.保険編集フラグをFALSEにセットする。
          hokenEditFlg.value = false
        } else {
          // ・変数.保険のサービス＝変数.保険のサービス+"、"
          hokenService.value = hokenService.value + t('label.comma')
        }
      }
    }
    // 時間帯表示の場合
    if (showData.value?.dataList.length > 0) {
      if (
        localOneway.orX0066Oneway.wscksFlg !== undefined &&
        localOneway.orX0066Oneway.wscksFlg === 1
      ) {
        setCalendar49(showData.value.dataList)
      }
      if (
        localOneway.orX0066Oneway.wscksFlg !== undefined &&
        localOneway.orX0066Oneway.wscksFlg === 2
      ) {
        setCalendar48(showData.value.dataList)
      }
    }
    if (hokenService.value !== '') {
      // ・変数.保険のサービス最後の"、"を除く
      // ・保険のサービステキストエリア＝変数.保険のサービス
      local.hokenModel.value = hokenService.value.substring(0, hokenService.value.length - 1)
      // ・変数.保険のサービス＝空白
      hokenService.value = ''
    }
  }
}

/**
 * AC008 「保存ボタン」押下
 */
async function onClickSave() {
  // AC006-1を実行
  if (local.orX0066.selectedTitle === undefined || local.orX0066.selectedTitle.modelValue === '') {
    await openErrorDialog()
    return
  }

  // 追加 Insert
  const param: WeekPlanPatternSettingUpdateInEntity = {
    // サービス区分: 引継情報.計画書様式 - 1
    serviceKbn: parseInt(localOneway.orX0066Oneway.cksFlg) - 1,
    newCks51List: local.orX0066.newCks51List ?? [
      {
        ks51Id: '',
        youkaCd: '',
        nameKnj: '',
        switchKbn: '',
        seq: '',
        termId: '',
        wIgaiKnj: '',
      },
    ],
    newCks54List: local.orX0066.newCks54List ?? [],
    newCks52List: local.orX0066.newCks52List ?? ([] as cks52List[]),
  }
  //週間計画パターンタイトル情報保存
  const ret: WeekPlanPatternSettingUpdateOutEntity = await ScreenRepository.update(
    'weekPlanPatternSettingsUpdate',
    param
  )

  local.orX0066.resultCks51List = ret.data.newCks51List
  local.orX0066.resultCks54List = ret.data.newCks54List
  local.orX0066.resultCks52List = ret.data.newCks52List

  emit('update:modelValue', local.orX0066)
}

/**
 * AC009 「追加ボタン」押下
 */
async function onClickAdd() {
  // AC002-1を実行
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case OrX0066Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC008を実行し、処理続き。
        await onClickSave()
        return
      case OrX0066Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：AC004-3‐1を実行
        break
      case OrX0066Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：処理終了。
        return
    }
  }

  // いいえ：AC004-3‐1を実行
  // （行タイトル）時間、時間帯ラベルの設定：項目一覧の表示データを参照する。
  // 週間計画パターン明細再取得する。 選択タイトルが対応する「週間計画ID」
  const param: WeekPlanPatternSettingsDetailSelectInEntity = {
    ks51Id: local.orX0066.selectedTermId,
    termId: localOneway.orX0066Oneway.validPeriod ?? '',
  }
  //週間計画パターンタイトル情報保存
  const retData: WeekPlanPatternSettingsDetailSelectOutEntity = await ScreenRepository.select(
    'weekPlanPatternSettingsDetailSelect',
    param
  )

  // ・共通情報.計画書様式が居宅、且つ、
  // ・親画面.有効期間IDと画面.有効期間IDが異なる、且つ、
  // ・サービスが登録される場合（詳細データリストが空白）且つ、
  // ・親画面.有効期間IDが5以上、且つ、
  // 「親画面.有効期間ID」から「画面.有効期間ID」を引いた値が1になる
  if (
    localOneway.orX0066Oneway.cksFlg !== undefined &&
    localOneway.orX0066Oneway.cksFlg !== OrX0066Const.DEFAULT.SHISETSU &&
    props.or35921Model.orignValidId !== parseInt(local.orX0066.selectedTermId) &&
    local.orX0066.newCks52List?.length === 0 &&
    props.or35921Model.orignValidId !== undefined &&
    props.or35921Model.orignValidId >= 5 &&
    props.or35921Model.orignValidId - parseInt(local.orX0066.selectedTermId) === 1
  ) {
    // ■以下のメッセージを表示
    // w.cmn.20797
    // 「サービスの有効期間が異なる計画を取り込みます。
    // サービスによっては名称、単価等が変更される場合があります。
    // 取り込みますか？」
    // ・後続処理
    // はい：処理を続行する
    // いいえ：画面を維持し処理終了する。
    const dialogResult = await openWarnDialog(OrX0066Const.DEFAULT.WARN_MESSAGE_TYPE_20797)
    switch (dialogResult) {
      case OrX0066Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：処理を続行する
        break
      case OrX0066Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：画面を維持し処理終了する。
        return
    }
  }
  // ・共通情報.計画書様式が居宅、且つ、
  // ・親画面.有効期間IDと画面.有効期間IDが異なる、且つ、
  // ・サービスが登録される場合（詳細データリストが空白）且つ、
  // ・（親画面.有効期間IDが5以上、且つ、
  // 「親画面.有効期間ID」から「画面.有効期間ID」を引いた値が1になる）以外
  if (
    localOneway.orX0066Oneway.cksFlg !== undefined &&
    localOneway.orX0066Oneway.cksFlg !== OrX0066Const.DEFAULT.SHISETSU &&
    props.or35921Model.orignValidId !== parseInt(local.orX0066.selectedTermId) &&
    local.orX0066.newCks52List?.length === 0 &&
    !(
      props.or35921Model.orignValidId !== undefined &&
      props.or35921Model.orignValidId >= 5 &&
      props.or35921Model.orignValidId - parseInt(local.orX0066.selectedTermId) === 1
    )
  ) {
    // ■以下のメッセージを表示
    // w.cmn.20798
    // 「サービスの有効期間が異なる計画を取り込むことはできません。」
    // ・後続処理
    // OK:画面を維持し処理終了する。
    await openWarnDialog(OrX0066Const.DEFAULT.WARN_MESSAGE_TYPE_20798)
    return
  }

  // 対象データを呼び元に返却して、画面を閉じる。
  // 戻り値：
  // ・画面.週間計画ID
  emit('update:modelValue', local.orX0066.selectedTermId)
  // ・取込パタン（追加ボタン押下：追加、上書ボタン押下：上書）
  local.orX0066.pattern = OrX0066Const.DEFAULT.TORI_TYPE_ADD
  emit('update:modelValue', local.orX0066.pattern)
  // ・日常リスト
  emit('update:modelValue', retData.data.cks54List)
  // ・詳細リスト
  emit('update:modelValue', retData.data.cks52List)
}

/**
 * AC010 「上書ボタン」押下
 */
async function onClickOverwrite() {
  // AC002-1を実行
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case OrX0066Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：AC008を実行し、処理続き。
        await onClickSave()
        return
      case OrX0066Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：処理を続き
        break
      case OrX0066Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：処理終了。
        return
    }
  }
  // AC006-1を実行
  if (local.orX0066.selectedTitle === undefined || local.orX0066.selectedTitle.modelValue === '') {
    await openErrorDialog()
    return
  }
  // ・共通情報.計画書様式が居宅、且つ、
  // ・親画面.有効期間IDと画面.有効期間IDが異なる、且つ、
  // ・サービスが登録される場合（詳細データリストが空白）且つ、
  // ・親画面.有効期間IDが5以上、且つ、
  // 「親画面.有効期間ID」から「画面.有効期間ID」を引いた値が1になる
  if (
    localOneway.orX0066Oneway.cksFlg !== undefined &&
    localOneway.orX0066Oneway.cksFlg !== OrX0066Const.DEFAULT.SHISETSU &&
    props.or35921Model.orignValidId !== parseInt(local.orX0066.selectedTermId) &&
    local.orX0066.newCks52List?.length === 0 &&
    props.or35921Model.orignValidId !== undefined &&
    props.or35921Model.orignValidId >= 5 &&
    props.or35921Model.orignValidId - parseInt(local.orX0066.selectedTermId) === 1
  ) {
    // ■以下のメッセージを表示
    // w.cmn.20797
    // 「サービスの有効期間が異なる計画を取り込みます。
    // サービスによっては名称、単価等が変更される場合があります。
    // 取り込みますか？」
    // ・後続処理
    // はい：処理を続行する
    // いいえ：画面を維持し処理終了する。
    const dialogResult = await openWarnDialog(OrX0066Const.DEFAULT.WARN_MESSAGE_TYPE_20797)
    switch (dialogResult) {
      case OrX0066Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：処理を続行する
        break
      case OrX0066Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：画面を維持し処理終了する。
        return
    }
  }
  // ・共通情報.計画書様式が居宅、且つ、
  // ・親画面.有効期間IDと画面.有効期間IDが異なる、且つ、
  // ・サービスが登録される場合（詳細データリストが空白）且つ、
  // ・（親画面.有効期間IDが5以上、且つ、
  // 「親画面.有効期間ID」から「画面.有効期間ID」を引いた値が1になる）以外
  if (
    localOneway.orX0066Oneway.cksFlg !== undefined &&
    localOneway.orX0066Oneway.cksFlg !== OrX0066Const.DEFAULT.SHISETSU &&
    props.or35921Model.orignValidId !== parseInt(local.orX0066.selectedTermId) &&
    local.orX0066.newCks52List?.length === 0 &&
    !(
      props.or35921Model.orignValidId !== undefined &&
      props.or35921Model.orignValidId >= 5 &&
      props.or35921Model.orignValidId - parseInt(local.orX0066.selectedTermId) === 1
    )
  ) {
    // ■以下のメッセージを表示
    // w.cmn.20798
    // 「サービスの有効期間が異なる計画を取り込むことはできません。」
    // ・後続処理
    // OK:画面を維持し処理終了する。
    await openWarnDialog(OrX0066Const.DEFAULT.WARN_MESSAGE_TYPE_20798)
    return
  }
  // ■以下のメッセージを表示
  // i.cmn.10902
  // 「現在の週間計画を上書きします。<BR>
  // よろしいですか？」
  // ・後続処理
  const dialogResult = await openInfoDialog(OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10902)
  switch (dialogResult) {
    case OrX0066Const.DEFAULT.DIALOG_RESULT_YES:
      // はい：該当パターンのデータの書込みで処理続き。
      break
    case OrX0066Const.DEFAULT.DIALOG_RESULT_NO:
      // いいえ：処理終了。
      return
  }
  // 対象データを呼び元に返却して、画面を閉じる。
  // 戻り値：
  // ・画面.週間計画ID
  emit('update:modelValue', local.orX0066.selectedTermId)
  // ・取込パタン（追加ボタン押下：追加、上書ボタン押下：上書）
  local.orX0066.pattern = OrX0066Const.DEFAULT.TORI_TYPE_OVERWRITE
  emit('update:modelValue', local.orX0066.pattern)
}

/**
 * AC011_「主な日常生活選択アイコンボタン」押下
 *
 * @param index - 選択した行のindex
 */
const openGui00937DialogAc011 = (index: number) => {
  selectedItemIndex.value = index
  localOneway.or51775OnewayModel.title = t('label.main-life-active')
  localOneway.or51775OnewayModel.t1Cd = '850'
  localOneway.or51775OnewayModel.t2Cd = '2'
  localOneway.or51775OnewayModel.t3Cd = '0'
  localOneway.or51775OnewayModel.tableName = 'cpn_tuc_cks54'
  localOneway.or51775OnewayModel.columnName = 'nichijo_knj'
  localOneway.or51775OnewayModel.assessmentMethod = ''
  localOneway.or51775OnewayModel.userId = localOneway.orX0066Oneway.userId
  // GUI00937_入力支援［ケアマネ］画面をポップアップで起動する。
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC012_「週単位以外のサービスアイコンボタン」押下
 */
const openGui00937DialogAc012 = () => {
  localOneway.or51775OnewayModel.title = t('label.week-unit-other-service')
  localOneway.or51775OnewayModel.t1Cd = '850'
  localOneway.or51775OnewayModel.t2Cd = '2'
  localOneway.or51775OnewayModel.t3Cd = '0'
  localOneway.or51775OnewayModel.tableName = 'cpn_tuc_cks51'
  localOneway.or51775OnewayModel.columnName = 'w_igai_knj'
  localOneway.or51775OnewayModel.assessmentMethod = ''
  localOneway.or51775OnewayModel.userId = localOneway.orX0066Oneway.userId
  // GUI00937_入力支援［ケアマネ］画面をポップアップで起動する。
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC013_「保険のサービスアイコンボタン」押下
 */
const openDialogAc014 = () => {
  Or27610Logic.state.set({
    uniqueCpId: or27610.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * カレンダーをダブルクリック
 */
function handleDoubleClick() {
  Or27610Logic.data.set({
    uniqueCpId: or27610.value.uniqueCpId,
    value: data.value.dataList,
  })
  Or27610Logic.state.set({
    uniqueCpId: or27610.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  console.log('handleDoubleClick', Or27610Logic.data.get(or27610.value.uniqueCpId))
}

/**
 * カレンダータイムズームをダブルクリック
 */
function handleDoubleClickTimeAxis() {
  Or27610Logic.state.set({
    uniqueCpId: or27610.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  Or27610Logic.data.set({
    uniqueCpId: or27610.value.uniqueCpId,
    value: data.value.dataList,
  })
  console.log('handleDoubleClickTimeAxis', Or27610Logic.data.get(or27610.value.uniqueCpId))
}

/**
 * 日程ダブルクリックイベント
 */
function handleDoubleClickEvent() {
  Or27610Logic.data.set({
    uniqueCpId: or27610.value.uniqueCpId,
    value: data.value.dataList,
  })
  Or27610Logic.state.set({
    uniqueCpId: or27610.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// 週間計画入力ポップアップの表示状態を返すComputed
const showDialogOr27610 = computed(() => {
  // Or27610のダイアログ開閉状態
  return Or27610Logic.state.get(or27610.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 計算週
 *
 * @param event - スケジュール
 */
function calcWeek(event: WeeklyPlan) {
  const arr: number[] = []
  if (event.dayOfWeek1) {
    arr.push(1)
  }
  if (event.dayOfWeek2) {
    arr.push(2)
  }
  if (event.dayOfWeek3) {
    arr.push(3)
  }
  if (event.dayOfWeek4) {
    arr.push(4)
  }
  if (event.dayOfWeek5) {
    arr.push(5)
  }
  if (event.dayOfWeek6) {
    arr.push(6)
  }
  if (event.dayOfWeek7) {
    arr.push(7)
  }
  if (arr.length === 0) return []

  const result = []
  let current = { start: arr[0], count: 1 }

  for (let i = 1; i < arr.length; i++) {
    if (arr[i] === arr[i - 1] + 1) {
      current.count++
    } else {
      result.push([current.start, current.count])
      current = { start: arr[i], count: 1 }
    }
  }

  result.push([current.start, current.count])
  return result
}

defineExpose({
  onClickSave,
  onClickAdd,
  onClickOverwrite,
})
/**************************************************
 * ウォッチャー
 **************************************************/
// AC004「タイトル」変更
watch(
  () => local.orX0066.selectedTitle?.modelValue,
  async () => {
    await onClickTitle()
    emit('update:modelValue', local.orX0066.selectedTitle?.modelValue)
    console.log('タイトル ' + local.orX0066.selectedTitle?.modelValue)
  }
)

// 要介護度入力値
watch(
  () => local.orX0066.selectedNursingCareRequired,
  () => {
    emit('update:modelValue', local.orX0066.selectedNursingCareRequired)
    console.log('要介護度入力値 ' + local.orX0066.selectedNursingCareRequired)
  }
)

// 週間計画0048
watch(
  () => data.value,
  () => {
    setCalendar48(data.value.dataList)
  }
)

//週間計画 0049
watch(
  () => data.value,
  () => {
    setCalendar49(data.value.dataList)
  }
)

/**
 * 時間帯内容設定
 *
 * @param dataList - 週間計画詳細リスト
 */
function setCalendar48(dataList: WeeklyPlan[]) {
  if (dataList) {
    const eventList = []
    let index = 0
    for (const e of dataList) {
      frequency.value = frequency.value + e.frequency
      // 計算週
      const weekArray: number[][] = calcWeek(e)
      for (const week of weekArray) {
        const etp = e.textPosition as Mo01282Type
        const event: OrX0048CalendarEvent = {
          // 日程ブロックID
          id: index++,
          // 日程開始時間
          start: e.startTime,
          // 日程終了時間
          end: e.endTime,
          // 日程ブロック背景色
          bgColor: e.background,
          // フォント色
          fontColor: e.color,
          // フォントサイズ
          fontSize: `${e.fontSize.modelValue}px`,
          // 日程内容エリア揃い方向
          align: etp.modelValue === '2' ? 'right' : etp.modelValue === '0' ? 'left' : 'center',
          // タイトル
          title: e.contents,
          // 曜日
          week: week[0],
          // 間隔日数
          period: week[1] > 1 ? week[1] : undefined,
        }
        eventList.push(event)
      }
    }
    calendarConfig48.value.events = eventList

    // カレンダーの画面を反映
    calendar.value?.reRenderCalendar()
  }
}

/**
 * 時間帯内容設定
 *
 * @param dataList - 週間計画詳細リスト
 */
function setCalendar49(dataList: WeeklyPlan[]) {
  if (dataList) {
    const eventList = []
    let index = 0
    for (const e of dataList) {
      frequency.value = frequency.value + e.frequency
      // 計算週
      const weekArray: number[][] = calcWeek(e)
      for (const week of weekArray) {
        const etp = e.textPosition as Mo01282Type
        const event: OrX0049CalendarEvent = {
          // 日程ブロックID
          id: index++,
          // 日程開始時間
          start: e.startTime,
          // 日程終了時間
          end: e.endTime,
          // 日程ブロック背景色
          bgColor: e.background,
          // フォント色
          fontColor: e.color,
          // フォントサイズ
          fontSize: `${e.fontSize.modelValue}px`,
          // 日程内容エリア揃い方向
          align: etp.modelValue === '2' ? 'right' : etp.modelValue === '0' ? 'left' : 'center',
          // タイトル
          title: e.contents,
          // 曜日
          week: week[0],
          // 間隔日数
          period: week[1] > 1 ? week[1] : undefined,
        }
        eventList.push(event)
      }
    }
    calendarConfig49.value.events = eventList

    // カレンダーの画面を反映
    calendar.value?.reRenderCalendar()
  }
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msgType - message TYPE
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(msgType: string): Promise<string> {
  let tempUniqueCpId = ''

  if (msgType === OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10430) {
    tempUniqueCpId = or21814_1.value.uniqueCpId
  } else if (msgType === OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10903) {
    tempUniqueCpId = or21814_2.value.uniqueCpId
  } else if (msgType === OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10902) {
    tempUniqueCpId = or21814_3.value.uniqueCpId
  }

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: tempUniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(tempUniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(tempUniqueCpId)

        let result = OrX0066Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (msgType === OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10430) {
          if (event?.firstBtnClickFlg) {
            result = OrX0066Const.DEFAULT.DIALOG_RESULT_YES
          }
          if (event?.secondBtnClickFlg) {
            result = OrX0066Const.DEFAULT.DIALOG_RESULT_NO
          }
          if (event?.thirdBtnClickFlg) {
            result = OrX0066Const.DEFAULT.DIALOG_RESULT_CANCEL
          }
        } else if (
          msgType === OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10903 ||
          msgType === OrX0066Const.DEFAULT.INFO_MESSAGE_TYPE_10902
        ) {
          if (event?.secondBtnClickFlg) {
            result = OrX0066Const.DEFAULT.DIALOG_RESULT_YES
          }
          if (event?.thirdBtnClickFlg) {
            result = OrX0066Const.DEFAULT.DIALOG_RESULT_NO
          }
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: tempUniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログ表示
 *
 * @param msgType - message TYPE
 *
 * @returns ダイアログの選択結果（yes, no）
 */
async function openWarnDialog(msgType: string): Promise<string> {
  let tempUniqueCpId = ''

  if (msgType === OrX0066Const.DEFAULT.WARN_MESSAGE_TYPE_20797) {
    tempUniqueCpId = or21815_1.value.uniqueCpId
  } else if (msgType === OrX0066Const.DEFAULT.WARN_MESSAGE_TYPE_20798) {
    tempUniqueCpId = or21815_2.value.uniqueCpId
  }

  // 警告ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: tempUniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 警告ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(tempUniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(tempUniqueCpId)

        let result = OrX0066Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = OrX0066Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = OrX0066Const.DEFAULT.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: tempUniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openErrorDialog(): Promise<string> {
  // 確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = OrX0066Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = OrX0066Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = OrX0066Const.DEFAULT.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

// 有効期間
const validPeriod = ref<string>(t('label.valid-period'))
// 週単位以外
const weekUnitOtherSevice1 = ref<string>(t('label.not-weekly'))
// のサービス
const weekUnitOtherSevice2 = ref<string>(t('label.week-unit-other-sevice-ori'))
// (保険
const hokenSevice1 = ref<string>(t('label.hoken-sevice-ori-1'))
// ｻｰﾋﾞｽ)
const hokenSevice2 = ref<string>(t('label.hoken-sevice-ori-2'))

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    let retStr = `${str}${data.value}`
    if (data.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      retStr = data.value
    }
    return retStr
  }
  if (localOneway.or51775OnewayModel.title === t('label.main-life-active')) {
    let tmpStr = ''
    let count = 1
    for (const str of local.njModelArr) {
      if (count === selectedItemIndex.value) {
        tmpStr = setOrAppendValue(str.value, data)
        break
      }
      count = count + 1
    }
    local.njModelArr[count - 1].value = tmpStr
  }
  if (localOneway.or51775OnewayModel.title === t('label.week-unit-other-service')) {
    local.weeklyModel.value = setOrAppendValue(local.weeklyModel.value ?? '', data)
  }

  localOneway.or51775OnewayModel.title = ''

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

const handleOr27610Confirm = (weekPlan: WeeklyPlan[]) => {
  data.value.dataList = weekPlan
}
</script>

<template>
  <!--タイトル-->
  <c-v-row
    no-gutters
    class="title-margin"
  >
    <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway" />
    <base-mo00040
      v-model="local.orX0066.selectedTitle"
      :oneway-model-value="localOneway.mo00040Oneway"
      v-bind="{ ...$attrs }"
      style="margin-left: -10px"
    >
    </base-mo00040>
  </c-v-row>
  <!--要介護度-->
  <c-v-row no-gutters>
    <div style="margin-left: -6px">
      <base-mo00039
        v-bind="nursingCareRequiredRadio"
        v-model="local.orX0066.selectedNursingCareRequired"
        :oneway-model-value="localOneway.nursingCareRequiredRaidoGroupOneway"
        class="radio-group-indent"
        @change="changeYoKaigoDo"
      >
        <span></span>
        <base-at-radio
          v-for="(item, index) in local.nursingCareRequiredList"
          :key="index"
          :name="'radio' + index"
          :value="item.value"
          :radio-label="item.label"
        />
      </base-mo00039>
    </div>
  </c-v-row>
  <c-v-row style="margin-top: 0px; border-bottom: 1px solid rgb(224, 224, 224)"></c-v-row>

  <!--有効期間-->
  <c-v-row
    no-gutters
    style="margin-top: 12px; align-items: center; border-bottom: 1px solid rgb(224, 224, 224)"
  >
    <!--有効期間-->
    <!--共通情報.計画書様式(cks_flg)が施設「1」の場合、非表示-->
    <c-v-col
      v-if="
        localOneway.orX0066Oneway.cksFlg !== undefined &&
        localOneway.orX0066Oneway.cksFlg !== OrX0066Const.DEFAULT.SHISETSU
      "
      style="margin-left: -8px"
    >
      <tbody>
        <tr class="yukoustyle">
          <td>{{ validPeriod }}</td>
          <td>{{ periodDisp }}</td>
        </tr>
      </tbody>
    </c-v-col>

    <!--週間取込-->
    <c-v-col class="weekImport">
      <base-mo00610
        v-bind="localOneway.PeriodImportMo00610Oneway"
        style="min-height: 20px"
        @click="weekImport"
      >
      </base-mo00610>
    </c-v-col>
  </c-v-row>

  <!--週間計画内容area-->
  <!--タイトルが空白の場合、非表示-->
  <c-v-row
    v-show="
      local.orX0066.selectedTitle !== undefined && local.orX0066.selectedTitle.modelValue !== ''
    "
    no-gutters
    class="flex-1-3 mt-5"
  >
    <c-v-row style="margin-left: 0px">
      <div>
        <!-- 週間サービス計画表様式が"改訂前"の場合-->
        <div
          v-if="
            localOneway.orX0066Oneway.wscksFlg !== undefined &&
            localOneway.orX0066Oneway.wscksFlg === 1
          "
          class="calendar-container"
        >
          <g-custom-or-x-0049
            ref="calendar"
            :calendar-config="calendarConfig49"
            v-bind="orX0049"
            @double-click="handleDoubleClick"
            @on-time-axis-dbl-click="handleDoubleClickTimeAxis"
          ></g-custom-or-x-0049>
        </div>
        <!-- 週間サービス計画表様式が"R3/4改訂版"の場合-->
        <div
          v-if="
            localOneway.orX0066Oneway.wscksFlg !== undefined &&
            localOneway.orX0066Oneway.wscksFlg === 2
          "
          class="calendar-container"
        >
          <g-custom-or-x-0048
            ref="calendar"
            :calendar-config="calendarConfig48"
            v-bind="orX0048"
            @double-click="handleDoubleClick"
            @double-click-event="handleDoubleClickEvent"
            @on-time-axis-dbl-click="handleDoubleClickTimeAxis"
          ></g-custom-or-x-0048>
        </div>
        <!-- Or27610: 週間計画入力  -->
        <g-custom-or-27610
          v-if="showDialogOr27610"
          v-bind="or27610"
          :oneway-model-value="localOneway.or27610Oneway"
          @confirm="handleOr27610Confirm"
        />
      </div>
      <div style="width: 240px">
        <div>
          <div
            class="grid-header calendar-container"
            style="height: 25px !important"
          >
            <c-v-row class="officeSelect">
              <c-v-col style="min-width: 100%; padding: 0px !important">
                {{ t('label.main-life-content') }}
              </c-v-col>
            </c-v-row>
          </div>
        </div>
        <div
          v-for="hour in hourSlots"
          :key="hour"
          class="time-cell calendar-container"
          :class="{ 'select-row': selectedItemIndex === parseInt(hour) + 1 }"
        >
          <!-- 入力内容設定反映 -->
          <g-custom-or-x-0172
            v-model="local.njModelArr[parseInt(hour)]"
            :oneway-model-value="localOneway.orX0172Oneway"
            @on-click-edit-btn="openGui00937DialogAc011(parseInt(hour) + 1)"
          ></g-custom-or-x-0172>
        </div>
      </div>
    </c-v-row>
    <!-- 週単位以外のサービス 保険サービス -->
    <c-v-row
      no-gutters
      class="flex-1-4 mt-4"
    >
      <c-v-row
        class="officeSelect flex-0-0 w-auto"
        style="margin-left: 0px; margin-top: 8px"
      >
        <c-v-col
          :style="
            localOneway.orX0066Oneway.cksFlg === OrX0066Const.DEFAULT.ITAKU
              ? 'width:110px;'
              : 'width:90px;border-right: 1px solid #ddd;'
          "
          >{{ weekUnitOtherSevice1 }}<br />{{ weekUnitOtherSevice2 }}</c-v-col
        >
        <c-v-col v-if="!(localOneway.orX0066Oneway.cksFlg === OrX0066Const.DEFAULT.ITAKU)">
          <base-mo00009
            :oneway-model-value="mo00009OnewayWeekly"
            @click="openGui00937DialogAc012"
          ></base-mo00009>
        </c-v-col>
        <c-v-col>
          <base-mo00046
            v-model="local.weeklyModel"
            :oneway-model-value="localOneway.mo00046onewayWeekly"
            style="width: 1024px; border-style: none"
          />
        </c-v-col>
      </c-v-row>

      <c-v-row
        v-if="
          localOneway.orX0066Oneway.cksFlg !== undefined &&
          localOneway.orX0066Oneway.cksFlg !== OrX0066Const.DEFAULT.SHISETSU
        "
        class="officeSelect flex-0-0 w-auto"
        style="margin-left: 0px"
      >
        <c-v-col
          :style="
            localOneway.orX0066Oneway.cksFlg === OrX0066Const.DEFAULT.ITAKU
              ? 'width:110px;'
              : 'width:90px;border-right: 1px solid #ddd;'
          "
          >{{ hokenSevice1 }}<br />{{ hokenSevice2 }}</c-v-col
        >
        <c-v-col v-if="!(localOneway.orX0066Oneway.cksFlg === OrX0066Const.DEFAULT.ITAKU)">
          <base-mo00009
            :oneway-model-value="mo00009OnewayHoken"
            @click="openDialogAc014"
          ></base-mo00009>
        </c-v-col>
        <c-v-col>
          <base-mo00046
            v-model="local.hokenModel"
            :oneway-model-value="localOneway.mo00046onewayHoken"
            style="width: 1024px; border-style: none"
          />
        </c-v-col>
      </c-v-row>
    </c-v-row>
  </c-v-row>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21814
    v-if="showDialogOr21814_1"
    v-bind="or21814_1"
  />
  <g-base-or21814
    v-if="showDialogOr21814_2"
    v-bind="or21814_2"
  />
  <g-base-or21815
    v-if="showDialogOr21815_1"
    v-bind="or21815_1"
  />
  <g-base-or21815
    v-if="showDialogOr21815_2"
    v-bind="or21815_2"
  />
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Other"
    :oneway-model-value="localOneway.or51775OnewayModel"
    @confirm="handleOr51775Confirm"
  />
</template>

<style scoped lang="scss">
:deep(.v-field__overlay) {
  background-color: white;
}
.title-margin {
  margin-top: 8px;
  margin-bottom: 8px;
  margin-left: 4px;
}

.weekImport {
  margin-right: -8px;
  text-align: right;
}

/* 主な日常生活一覧タイトル */
.grid-header {
  display: flex;
  height: 23px;
  border: 1px solid #ddd;
  background: #f2f2f2 !important;
  border-left: none;
  box-sizing: border-box;
  text-align: center;
  flex-direction: column;
  justify-content: center;
}

/* 主な日常生活一覧セル */
.time-cell {
  background: #fff;
  height: var(--custom-calendar-cell-height, 22px);
  border-right: 1px solid #ddd;
  box-sizing: border-box;
}
.time-cell:nth-child(even) {
  border-bottom: 1px dotted #ddd;
}
.time-cell:nth-child(odd) {
  border-bottom: 1px solid #ddd;
}
.officeSelect {
  margin: unset;
  border: 1px solid #e5e5e5;
  background-color: #f2f2f2;

  .v-col {
    max-width: unset;
    width: auto;
    flex: 0 1 auto;
    padding: 0px;
    align-self: center;
  }
}
.calendar-container {
  background: #fff;
}

.yukoustyle {
  td {
    border-top: 1px solid rgb(224, 224, 224);
    border-bottom: 1px solid rgb(224, 224, 224);
    border-right: 1px solid rgb(224, 224, 224);
    padding: 8px;
  }
  td:first-child {
    border-left: 1px solid rgb(224, 224, 224);
    font-weight: bold;
    width: 100px;
    background-color: rgb(var(--v-theme-black-50));
  }
  td:last-child {
    width: 200px;
  }
}
:deep(.table-cell) {
  border: none;
}
:deep(.v-field__input) {
  min-height: var(--custom-calendar-cell-height, 22px) !important;
}
</style>
