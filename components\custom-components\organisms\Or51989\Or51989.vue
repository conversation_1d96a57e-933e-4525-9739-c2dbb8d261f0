<script setup lang="ts">
/**
 * Or51989:課題立案様式タイトルマスタモーダル
 * GUI00905_課題立案様式タイトルマスタ
 *
 * @description
 * 課題立案様式タイトルマスタモーダル
 *
 * <AUTHOR> 盧青陽
 */

import { useScreenStore, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import { cloneDeep, isEqual } from 'lodash'
import {
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch,
  type ComponentPublicInstance,
} from 'vue'
import { useI18n } from 'vue-i18n'
import type {
  Mo01354Headers,
  Mo01354Items,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo01278Type } from '~/types/business/components/Mo01278Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import type {
  IssuesPlanningStyleTitleMasterInfoList,
  Or51989Type,
} from '~/types/cmn/business/components/Or51989Type'
import { CustomClass } from '~/types/CustomClassType'
import { useValidation } from '~/utils/useValidation'
import { Or51989Const } from './Or51989.constants'
import type { Or51989OnewayType, TableData } from './Or51989.type'
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or51989OnewayType
  modelValue: Or51989Type
  uniqueCpId: string
  parentUniqueCpId: string
}
//引継情報を取得する
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
const validation = useValidation()
const defaultOnewayModelValue: Or51989OnewayType = {
  kinouKbn: '',
  youshikiKbn: '',
  svJigyoId: '',
}
//フォーカス中のデータ行のウォッチ
const selectItemMap = new Map<string, TableData>()
const selectId = ref<string>('')

const or21814 = ref({ uniqueCpId: '' }) //閉じる提示ポップアップ
const or21814_2 = ref({ uniqueCpId: '' }) //削除 確認ダイアログ(はい、いいえ)
const or21815 = ref({ uniqueCpId: '' }) //削除ボタン警告
const defaultModelValue: Or51989Type = {
  editFlg: false,
  delBtnDisabled: true,
  focusIndex: '',
  issuesPlanningStyleTitleMasterList: [],
}
// 元のテーブルデータ
const orgTableData = ref<TableData[]>()

//画面表示用テーブルデータ：倫理削除データをフィルター
const tableDataFilter = computed(() => {
  const titleList = refValue.value
  return titleList!.filter((i: TableData) => i.updateKbn !== UPDATE_KBN.DELETE)
})

//変数：セールを変更する前のデータを保存
const oldValMap = new Map<string, Mo01354Items>()

//行データに対応するdom要素を参照
const textRefs = new Map<string, HTMLElement>()

//input要素を参照する変数の宣言
const refs = ref<HTMLInputElement[]>([])

// 五十音ソートのためのコラレーターを定義
const collator = new Intl.Collator('ja', {
  sensitivity: 'base',
  numeric: true,
})

const localOneWay = reactive({
  or51989: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo01278Oneway: {
    maxLength: '4',
    min: 0,
    max: 9999,
    isEditCamma: false,
    rules: [
      validation.integer,
      validation.required,
      validation.minValue(0),
      validation.maxValue(9999),
    ],
  },
  mo01274Oneway: {
    showItemLabel: false,
    isRequired: true,
    rules: [validation.required],
  },
  mo01354Oneway: {
    columnMinWidth: {
      columnWidths: [84, 400, 120, 170, 110, 100, 190, 220, 100],
    } as ResizableGridBinding,
    rowHeight: '32px',
    headers: [
      {
        title: t('label.free1_id'),
        key: 'free1Id',
        sortable: true,
        required: false,
        sortRaw(a: TableData, b: TableData) {
          // カスタムソートロジック
          return collator.compare(a.free1Id.value, b.free1Id.value)
        },
      },
      {
        title: t('label.style-name'),
        key: 'titleKnj',
        sortable: true,
        required: true,
        sortRaw(a: TableData, b: TableData) {
          // カスタムソートロジック
          return collator.compare(a.titleKnj.value, b.titleKnj.value)
        },
      },
      {
        title: t('label.column_count'),
        key: 'columnCount',
        sortable: true,
        required: false,
        sortRaw(a: TableData, b: TableData) {
          // カスタムソートロジック
          return collator.compare(a.columnCount.modelValue ?? '', b.columnCount.modelValue ?? '')
        },
      },
      {
        title: t('label.font_size'),
        key: 'fontSize',
        sortable: true,
        required: false,
        sortRaw(a: TableData, b: TableData) {
          // カスタムソートロジック
          return collator.compare(a.fontSize.modelValue ?? '', b.fontSize.modelValue ?? '')
        },
      },
      {
        title: t('label.sort'),
        key: 'sort',
        sortable: true,
        required: false,
        sortRaw(a: TableData, b: TableData) {
          // カスタムソートロジック
          return collator.compare(a.sort.value, b.sort.value)
        },
      },
      {
        title: t('label.youshi_size'),
        key: 'youshiSize',
        sortable: true,
        required: false,
        sortRaw(a: TableData, b: TableData) {
          // カスタムソートロジック
          return collator.compare(a.youshiSize.modelValue ?? '', b.youshiSize.modelValue ?? '')
        },
      },
      {
        title: t('label.free_kbn'),
        key: 'freeKbn',
        sortable: true,
        required: false,
        sortRaw(a: TableData, b: TableData) {
          // カスタムソートロジック
          return collator.compare(a.freeKbn.modelValue ?? '', b.freeKbn.modelValue ?? '')
        },
      },
      {
        title: t('label.kirikae_kbn'),
        key: 'kirikaeKbn',
        sortable: true,
        required: false,
        sortRaw(a: TableData, b: TableData) {
          // カスタムソートロジック
          return collator.compare(a.kirikaeKbn.modelValue ?? '', b.kirikaeKbn.modelValue ?? '')
        },
      },
      {
        title: t('label.applicable'),
        key: 'tekiyouFlg',
        sortable: true,
        required: false,
        sortRaw(a: TableData, b: TableData) {
          // カスタムソートロジック
          return collator.compare(
            a.tekiyouFlg.modelValue ? '1' : '0',
            b.tekiyouFlg.modelValue ? '1' : '0'
          )
        },
      },
    ] as Mo01354Headers[],
    height: '530px',
  },
  // 項目数リスト
  mo01282OClumncountList: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  // 印刷文字サイズリスト
  mo01282OFontsizeList: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  // 用紙サイズリスト
  mo01282OYoushisizeList: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  // 課題立案形式リスト
  mo01282OFreekbnList: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  // 課題チェック表示切替リスト
  mo01282OKirikaekbnList: {
    items: [],
    itemTitle: 'title',
    itemValue: 'value',
  } as Mo01282OnewayType,
  mo00018OnewayType: {
    showItemLabel: false,
    customClass: new CustomClass({
      outerClass: 'mr-0 child-center',
    }),
  } as Mo00018OnewayType,
})

const mo01354Data = ref<Mo01354Type>({
  values: {
    selectedRowId: '0',
    selectedRowIds: [],
    items: [] as TableData[],
  },
})
const local = reactive({
  or51989: {
    ...defaultModelValue,
    ...props.modelValue,
  },
  /**
   * 保存用テーブルデータ
   */
  TableData: [] as IssuesPlanningStyleTitleMasterInfoList[],
})

const tableRef = ref<HTMLElement>()

const tableBodyRef = ref<HTMLElement>()
/**************************************************
 * ライフサイクルフック
 **************************************************/
defineExpose({
  createRow,
  copyRow,
  deleteRow,
  init,
})

onMounted(async () => {
  initContorls()
  await initCodes()
  await nextTick()
  tableBodyRef.value = tableRef.value!.querySelector('table tbody') as unknown as HTMLElement
  if (tableBodyRef.value) {
    tableBodyRef.value.addEventListener('click', handleTrClick)
  }
})

onUnmounted(() => {
  if (tableBodyRef.value) {
    tableBodyRef.value.removeEventListener('click', handleTrClick)
  }
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<TableData[]>({
  cpId: Or51989Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const handleTrClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const tr = target.closest('tr')
  if (tr?.parentElement) {
    const rowIndex = Array.from(tr.parentElement.children).indexOf(tr)
    mo01354Data.value.values.selectedRowId = mo01354Data.value.values.items[rowIndex].id
  }
}

/**
 *  refValueを更新する
 */
function setRefValue() {
  useScreenStore().setCpTwoWay({
    cpId: Or51989Const.CP_ID(1),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/**
 * 課題立案区分マスタ情報取得
 *
 */
function init() {
  // 戻り値はテーブルデータとして処理されます
  let tmpArr: Mo01354Items[] = []
  tmpArr = []

  local.or51989.issuesPlanningStyleTitleMasterList.forEach((item, index) => {
    tmpArr.push({
      id: index.toString(),
      free1Id: { value: item.free1Id },
      titleKnj: { value: item.titleKnj },
      columnCount: { modelValue: item.columnCount },
      fontSize: { modelValue: item.fontSize },
      sort: { value: item.sort },
      youshiSize: { modelValue: item.youshiSize },
      freeKbn: { modelValue: item.freeKbn },
      kirikaeKbn: { modelValue: item.kirikaeKbn },
      tekiyouFlg: { modelValue: item.tekiyouFlg === '1' },
      svJigyoId: item.svJigyoId,
      kinouKbn: item.kinouKbn,
      youshikiKbn: item.youshikiKbn,
      initColumnCount: item.initColumnCount,
      useFlg: item.useFlg,
      otherTekiyouFlg: item.otherTekiyouFlg,
      defUpdateFlg: item.defUpdateFlg,
      defUpdateFlg1: item.defUpdateFlg1,
      dmyFree1Id: item.dmyFree1Id,
      modifiedCnt: item.modifiedCnt,
      tableIndex: tmpArr.length,
      updateKbn: UPDATE_KBN.NONE,
    })
  })
  // 元のテーブルデータの設定
  orgTableData.value = cloneDeep(refValue.value)
  refValue.value = tmpArr as unknown as TableData[]
  setRefValue()

  if (tmpArr.length) {
    mo01354Data.value.values.selectedRowId = Or51989Const.DEFAULT.NUM_ZERO
    // 行削除活性
    local.or51989.delBtnDisabled = true
    emit('update:modelValue', local.or51989)
  }
  mo01354Data.value.values.items = tmpArr
}

/**
 * 「新規」押下
 */
async function createRow() {
  // 予定マスタのタイトル一覧の最終に新しい行を追加する。
  let lastSort = 0
  if (refValue.value!.length > 0) {
    for (const data of refValue.value!) {
      if (data.updateKbn !== UPDATE_KBN.DELETE) {
        lastSort = Math.max(lastSort, Number(data.sort.value))
      }
    }
  }
  // 表示順最大値＜9999の場合
  if (lastSort < 9999) {
    // 表示順最大値＋1を設定
    lastSort += 1
  } else {
    // 上記以外の場合、9999を設定
    lastSort = 9999
  }
  const data = {
    //テーブルINDEX(行固有ID)
    id: Math.max(...mo01354Data.value.values.items.map((item) => Number(item.id))) + 1 + '',
    // 画面表示項目：
    // No = 空白
    free1Id: { value: '0' },
    // 様式名= 空白
    titleKnj: { value: '' },
    // 項目数 = 1
    columnCount: { modelValue: '1' },
    // 印刷文字サイズ = 2：11pt
    fontSize: { modelValue: '2' },
    // 表示順 = 一覧に表示順のMAX+1 ※表示順+1 > 9999の場合、9999で固定。
    sort: { value: lastSort.toString() },
    // 用紙 = 0：A4
    youshiSize: { modelValue: '0' },
    // 課題立案入力形式 =0: 評価項目単位
    freeKbn: { modelValue: '0' },
    // 課題チェック表示切替 =0: 全て
    kirikaeKbn: { modelValue: '0' },
    // 適用 = チェックON ※適用チェックは1つのみのため、他の適用チェックをOFFにする
    tekiyouFlg: { modelValue: true },
    // 画面非表示項目：
    // 事業所ID = 親画面.事業所ID
    svJigyoId: localOneWay.or51989.svJigyoId,
    // 機能区分 = 親画面.機能区分
    kinouKbn: localOneWay.or51989.kinouKbn,
    // 様式区分 = 親画面.様式区分
    youshikiKbn: localOneWay.or51989.youshikiKbn,
    // 使用フラグ
    useFlg: Or51989Const.DEFAULT.NUM_ZERO,
    // 他適用フラグ
    otherTekiyouFlg: Or51989Const.DEFAULT.NUM_ZERO,
    // フリーID = 0
    dmyFree1Id: Or51989Const.DEFAULT.NUM_ZERO,
    // 初期表示の行の分割数（上） = 0
    initColumnCount: Or51989Const.DEFAULT.NUM_ZERO,
    // アップデートフラグ = 0
    defUpdateFlg: Or51989Const.DEFAULT.NUM_ZERO,
    // アップデートフラグ1 = 0
    defUpdateFlg1: Or51989Const.DEFAULT.NUM_ZERO,
    // 更新回数 = 0
    modifiedCnt: Or51989Const.DEFAULT.NUM_ZERO,
    // 更新区分 = "C":新規
    updateKbn: UPDATE_KBN.CREATE,
  }
  mo01354Data.value.values.items = [...mo01354Data.value.values.items, data].map((item) => {
    return {
      ...item,
      tekiyouFlg: data.id === item.id ? (item.tekiyouFlg as Mo00018Type) : false,
    }
  })
  refValue.value = [...(mo01354Data.value.values.items as TableData[])]
  mo01354Data.value.values.selectedRowId = data.id.toString()
  await nextTick()
  setFocus(data.id)
}
/**
 * ポップアップ初期化
 */
const initContorls = () => {
  //行削除確認ポップアップ
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      isOpen: false,
    },
  })
  //削除ボタン警告ポップアップ
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: false,
      dialogTitle: t('label.caution'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
}
/**
 * 行削除ボタン押下
 */
function deleteRow() {
  // 選択中の行のID
  const selectedRowId = mo01354Data.value.values.selectedRowId

  //選択していないなら、処理なし
  if (!selectedRowId || !mo01354Data.value.values.items.length) return
  // 対象行を取得
  const target = mo01354Data.value.values.items.find((item) => item.id === selectedRowId)
  if (!target) return
  // 使用中、または、他適用中の場合
  if (target.useFlg === '1' || target.otherTekiyouFlg === '1') {
    Or21815Logic.state.set({
      uniqueCpId: or21815.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.w-cmn-20783'),
      },
    })
    return
  } else {
    Or21814Logic.state.set({
      uniqueCpId: or21814_2.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-10764'),
      },
    })
    return
  }
}

/**
 * 行複写ボタン押下
 */
async function copyRow() {
  const selectedRowId = mo01354Data.value.values.selectedRowId
  if (!selectedRowId || !mo01354Data.value.values.items.length) return
  //複写元
  const selectedRow = mo01354Data.value.values.items.find((item) => item.id === selectedRowId)
  if (!selectedRow) return
  let lastSort = 0
  if (refValue.value!.length > 0) {
    for (const data of refValue.value!) {
      if (data.updateKbn !== UPDATE_KBN.DELETE) {
        lastSort = Math.max(lastSort, Number(data.sort.value))
      }
    }
  }
  // 表示順最大値＜9999の場合
  if (lastSort < 9999) {
    // 表示順最大値＋1を設定
    lastSort += 1
  } else {
    // 上記以外の場合、9999を設定
    lastSort = 9999
  }
  //オリジナル行のfree1Idチェック
  const isSourceRow = Number((selectedRow.free1Id as Mo01278Type).value) >= 1
  const newDmyFree1Id = isSourceRow
    ? (selectedRow.free1Id as Mo01278Type).value // コピーデータが既存データの場合
    : (selectedRow.dmyFree1Id as string) // コピー元が行複写データまたは新規データの場合
  // タイトル一覧の最終に新しい行を追加する。
  const data = {
    ...selectedRow,
    // テーブルINDEX(行固有ID)
    id: Math.max(...mo01354Data.value.values.items.map((item) => Number(item.id))) + 1 + '',
    // 画面表示項目：
    // No = 空白
    free1Id: { value: (selectedRow.free1Id as Mo01278Type).value },
    // 表示順 = 一覧に表示順のMAX+1 ※表示順+1 > 9999の場合、9999で固定。
    sort: { value: lastSort.toString() },
    // 適用 = チェックON ※適用チェックは1つのみのため、他の適用チェックをOFFにする
    tekiyouFlg: { modelValue: true },
    // 画面非表示項目：
    // 事業所ID = 親画面.事業所ID
    svJigyoId: localOneWay.or51989.svJigyoId,
    // 機能区分 = 親画面.機能区分
    kinouKbn: localOneWay.or51989.kinouKbn,
    // 様式区分 = 親画面.様式区分
    youshikiKbn: localOneWay.or51989.youshikiKbn,
    // 使用フラグ
    useFlg: Or51989Const.DEFAULT.NUM_ZERO,
    // 他適用フラグ
    otherTekiyouFlg: Or51989Const.DEFAULT.NUM_ZERO,
    //フリーID
    dmyFree1Id: newDmyFree1Id,
    // 初期表示の行の分割数（上） = 0
    initColumnCount: Or51989Const.DEFAULT.NUM_ZERO,
    // アップデートフラグ = 0
    defUpdateFlg: Or51989Const.DEFAULT.NUM_ZERO,
    // アップデートフラグ1 = 0
    defUpdateFlg1: Or51989Const.DEFAULT.NUM_ZERO,
    // 更新回数 = 0
    modifiedCnt: Or51989Const.DEFAULT.NUM_ZERO,
    // 更新区分 = "C":新規
    updateKbn: UPDATE_KBN.CREATE,
  }
  mo01354Data.value.values.items = [...mo01354Data.value.values.items, data].map((item) => {
    return {
      ...item,
      tekiyouFlg: data.id === item.id ? (item.tekiyouFlg as Mo00018Type) : false,
    }
  })
  refValue.value = [...(mo01354Data.value.values.items as TableData[])]
  mo01354Data.value.values.selectedRowId = data.id
  await nextTick()
  setFocus(data.id)
}
//ダイアログを表示する
function open21814ChangeDialog(errormsg: string): Promise<'yes' | 'no'> {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.confirm-dialog-title-info'),
      // ダイアログテキスト
      // dialogText: t('message.i-cmn-51300', [t('label.column_count')]),
      dialogText: errormsg,
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンタイプ
      secondBtnType: 'destroy1',
      // 第2ボタンラベル
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
      iconColor: 'rgb(var(--v-theme-blue-700))',
      iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
//プルダウン専用：ポップアップのコントロール
async function onUpdate(type: string) {
  const findIndex = mo01354Data.value.values.items.findIndex(
    (item) => item.id === mo01354Data.value.values.selectedRowId
  )
  const errormsg = {
    value: '',
  }
  const targetRow = mo01354Data.value.values.items[findIndex]
  if (type !== 'freeKbn' && (targetRow.free1Id as Mo01278Type).value !== '') {
    const columnCountErrormsg = t('message.i-cmn-51300', [t('label.column_count')])
    const fontSizeErrormsg = t('message.i-cmn-51300', [t('label.font_size')])
    const youshiSizeErrormsg = t('message.i-cmn-51300', [t('label.youshi_size')])
    const kirikaeKbnErrormsg = t('message.i-cmn-51300', [t('label.kirikae_kbn')])
    if (type === Or51989Const.DEFAULT.COLUMNCOUNT) {
      errormsg.value = columnCountErrormsg
    } else if (type === Or51989Const.DEFAULT.FONTSIZE) {
      errormsg.value = fontSizeErrormsg
    } else if (type === Or51989Const.DEFAULT.YOUSHISIZE) {
      errormsg.value = youshiSizeErrormsg
    } else if (type === Or51989Const.DEFAULT.KIRIKAEKBN) {
      errormsg.value = kirikaeKbnErrormsg
    }
    const dialogResult = await open21814ChangeDialog(errormsg.value)
    const resultFlag = dialogResult === 'yes'

    if (resultFlag) {
      const tem = mo01354Data.value.values.items[findIndex]
      tem.defUpdateFlg = '1'
      if (type === Or51989Const.DEFAULT.COLUMNCOUNT) {
        tem.defUpdateFlg1 = '1'
      }
      mo01354Data.value.values.items[findIndex] = {
        ...tem,
      }
      selectItemMap.set(selectId.value, cloneDeep(tem as TableData))
    } else {
      const tem = cloneDeep(selectItemMap.get(selectId.value)) as Mo01354Items
      mo01354Data.value.values.items[findIndex] = {
        ...tem,
      }
    }
  } else {
    ;(mo01354Data.value.values.items as Mo01354Items[]).forEach((item: Mo01354Items) => {
      if (Number(item.id) === Number(mo01354Data.value.values.selectedRowId)) {
        if (item.updateKbn !== UPDATE_KBN.CREATE) {
          item.updateKbn = UPDATE_KBN.UPDATE
        }
        item.defUpdateFlg1 = '1'
      }
    })
  }
}

/** 汎用コードマスタデータを取得し初期化 */
const initCodes = async () => {
  //汎用コード取得apiのinEntity生成
  const selectCodeKbnList = [
    //分割数（上）_課題立案様式タイトル
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ISSUES_PLANNING_STYLE_TITLE },
    //用紙
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BLANK_FORM },
    //課題立案入力形式
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ISSUES_PLANNING_INPUT_FORMAT },
    //課題チェック表示切替
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ISSUES_CHECK_DISPLAY_SWITCHING },
    //印刷文字サイズ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINT_LETTER_SIZE },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  const issuesPlanningStyleTitle = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ISSUES_PLANNING_STYLE_TITLE
  )
  const blankForm = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_BLANK_FORM)
  const issuesPlanningInputFormat = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ISSUES_PLANNING_INPUT_FORMAT
  )
  const issuesCheckDisplaySwitching = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ISSUES_CHECK_DISPLAY_SWITCHING
  )
  const printLetterSize = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_PRINT_LETTER_SIZE)
  if (!localOneWay.mo01282OClumncountList.items?.length) {
    issuesPlanningStyleTitle.forEach((item) => {
      localOneWay.mo01282OClumncountList.items?.push({
        title: item.label,
        value: item.value,
      })
    })
  }
  if (!localOneWay.mo01282OFontsizeList.items?.length) {
    printLetterSize.forEach((item) => {
      localOneWay.mo01282OFontsizeList.items?.push({
        title: item.label,
        value: item.value,
      })
    })
  }
  if (!localOneWay.mo01282OYoushisizeList.items?.length) {
    blankForm.forEach((item) => {
      localOneWay.mo01282OYoushisizeList.items?.push({
        title: item.label,
        value: item.value,
      })
    })
  }
  if (!localOneWay.mo01282OFreekbnList.items?.length) {
    issuesPlanningInputFormat.forEach((item) => {
      localOneWay.mo01282OFreekbnList.items?.push({
        title: item.label,
        value: item.value,
      })
    })
  }
  if (!localOneWay.mo01282OKirikaekbnList.items?.length) {
    issuesCheckDisplaySwitching.forEach((item) => {
      localOneWay.mo01282OKirikaekbnList.items?.push({
        title: item.label,
        value: item.value,
      })
    })
  }
}
//フォーカス中のデータの値を保存
const cacheSortValue = (item: TableData) => {
  oldValMap.set(item.id, cloneDeep(item))
}
//表示順データの入力チェック
const handleSortBlur = (el: Event, item: TableData) => {
  const inputEl = el.target! as HTMLInputElement
  const value = inputEl.value.trim()
  if (!value || value === '' || value === undefined || value === null || !/^\d+$/.test(value)) {
    const index = mo01354Data.value.values.items.findIndex((subItem) => subItem.id === item.id)
    if (index !== -1) {
      mo01354Data.value.values.items.splice(index, 1, oldValMap.get(item.id)!)
    }
  }
}

//一行の適用しかチェックできないように row:該当行
function onchange(row: TableData) {
  //他のチェックボックスの状態をoff
  const newList = mo01354Data.value.values.items.filter((item) => item.free1Id !== row.free1Id)
  newList.forEach((item) => (item.tekiyouFlg = { modelValue: false }))
}

/**
 * 行 DOM要素のREF設置メソッド
 *
 * @param el - コンポーネントインスタンス
 *
 * @param id - 行ID
 */
const setItemRef = (el: Element | ComponentPublicInstance | null, id: string) => {
  const elHtml: HTMLElement = el as HTMLElement
  if (el) {
    textRefs.set(id, elHtml)
  }
}

const setRef = (el: HTMLInputElement | null, id: string) => {
  if (el) {
    refs.value.splice(Number(id), 0, el)
  }
}
/**
 * フォーカス設定
 *
 * @param id - 行id
 */
const setFocus = (id: string) => {
  if (!id) {
    return
  }
  const inputElement = textRefs.get(id)!.querySelector(`#text-${id} input`)!
  const inputHtmlElement = inputElement as HTMLElement
  inputHtmlElement.focus()
}
/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * modeValue監視
 */
watch(
  () => props.modelValue,
  () => {
    local.or51989 = {
      ...defaultModelValue,
      ...props.modelValue,
    }
  },
  { immediate: true, deep: true }
)
/**
 * 行選択
 */
watch(
  () => mo01354Data.value.values.selectedRowId,
  () => {
    // 行削除活性
    local.or51989.delBtnDisabled = true
    emit('update:modelValue', local.or51989)
  }
)
/**
 * 一覧データ変更監視
 */
watch(
  () => refValue.value,
  () => {
    const issuesPlanningStyleTitleMasterList: IssuesPlanningStyleTitleMasterInfoList[] = []
    refValue.value!.forEach((item) => {
      if (
        item.id === mo01354Data.value.values.selectedRowId &&
        !isEqual(
          item,
          orgTableData.value?.find((sub) => sub.id === item.id)
        )
      ) {
        if (item.updateKbn !== UPDATE_KBN.CREATE && item.updateKbn !== UPDATE_KBN.DELETE) {
          item.updateKbn = UPDATE_KBN.UPDATE
        }
      }
      issuesPlanningStyleTitleMasterList.push({
        svJigyoId: item.svJigyoId,
        free1Id: item.free1Id.value,
        dmyFree1Id: item.dmyFree1Id,
        kinouKbn: item.kinouKbn,
        youshikiKbn: item.youshikiKbn,
        titleKnj: item.titleKnj.value,
        columnCount: item.columnCount.modelValue!,
        initColumnCount: item.initColumnCount,
        sort: item.sort.value,
        fontSize: item.fontSize.modelValue!,
        youshiSize: item.youshiSize.modelValue!,
        freeKbn: item.freeKbn.modelValue!,
        useFlg: item.useFlg,
        kirikaeKbn: item.kirikaeKbn.modelValue!,
        tekiyouFlg: item.tekiyouFlg.modelValue ? '1' : '0',
        otherTekiyouFlg: item.defUpdateFlg,
        defUpdateFlg: item.defUpdateFlg,
        defUpdateFlg1: item.defUpdateFlg1,
        modifiedCnt: item.modifiedCnt,
        updateKbn: item.updateKbn,
      })
    })
    local.or51989.issuesPlanningStyleTitleMasterList = issuesPlanningStyleTitleMasterList
    if (!tableDataFilter.value.length) {
      local.or51989.delBtnDisabled = false
    }
    emit('update:modelValue', local.or51989)
  },
  { deep: true }
)
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814_2.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      if (mo01354Data.value.values.selectedRowId) {
        let rowId = ''
        let isDel = false
        let indexDel = -1
        refValue.value!.forEach((item: TableData, index: number) => {
          if (item.id === mo01354Data.value.values.selectedRowId) {
            // 物理削除
            if (item.free1Id.value === '') {
              indexDel = index
            } else {
              item.updateKbn = UPDATE_KBN.DELETE
            }
            isDel = true
          } else if (!rowId && isDel && item.updateKbn !== UPDATE_KBN.DELETE) {
            rowId = item.id
          }
        })
        if (indexDel !== -1) {
          refValue.value?.splice(indexDel, 1)
          mo01354Data.value.values.items.splice(indexDel, 1)
        }
        await nextTick()
        if (tableDataFilter.value.length) {
          if (!rowId) {
            rowId = tableDataFilter.value[tableDataFilter.value.length - 1].id
          }
          mo01354Data.value.values.selectedRowId = rowId
        }
        setFocus(rowId)
      }
    } else {
      return
    }
  }
)
/**
 * エラー行のフォーカス位置に戻す
 */
watch(
  () => local.or51989.focusIndex,
  async (newValue) => {
    if (!newValue) {
      return
    }
    const row = mo01354Data.value.values.items[Number(local.or51989.focusIndex)]
    mo01354Data.value.values.selectedRowId = row.id
    await nextTick()
    setFocus(row.id)
    local.or51989.focusIndex = ''
  }
)
/**
 * 削除処理完了後、画面表示データの再取得
 */
watch(
  () => tableDataFilter.value,
  (newValue) => {
    mo01354Data.value.values.items = newValue
  },
  { deep: true }
)
/**
 * チェック状態中のデータのウォッチ
 */
watch(
  () => mo01354Data.value.values.selectedRowId,
  (newVal, oldVal) => {
    if (newVal === oldVal) return
    const item = mo01354Data.value.values.items.find((item) => item.id === newVal)! as TableData
    selectId.value = newVal
    selectItemMap.set(newVal, cloneDeep(item))
  },
  { deep: true }
)
watch(
  () => props.onewayModelValue,
  (newVal) => {
    if (!newVal) return
    localOneWay.or51989 = { ...newVal }
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <div class="flex-1-1">
    <!-- 予定マスタ一覧 -->
    <c-v-form ref="tableForm">
      <div
        ref="tableRef"
        class="table-header"
      >
        <base-mo-01354
          v-model="mo01354Data"
          :oneway-model-value="localOneWay.mo01354Oneway"
          class="list-wrapper w-100"
        >
          <!-- No -->
          <template #[`item.free1Id`]="{ item }">
            <!-- 分子：表用数値専用テキストフィールド -->
            <base-mo01338
              :oneway-model-value="{
                value: item.free1Id.value,
                customClass: new CustomClass({
                  labelStyle: 'display: none',
                }),
              }"
              class="text-right background-transparent"
            />
          </template>
          <!-- 様式名 -->
          <template #[`item.titleKnj`]="{ item }">
            <div :ref="(el) => setItemRef(el, item.id)">
              <!-- 分子：表用数値専用テキストフィールド -->
              <base-mo00045
                :id="`text-${item.id}`"
                v-model="item.titleKnj"
                maxlength="60"
                class="background-transparent"
                :oneway-model-value="localOneWay.mo01274Oneway"
              />
            </div>
          </template>
          <!-- 項目数 -->
          <template #[`item.columnCount`]="{ item }">
            <div class="h-100 pointer-auto">
              <!-- 分子：表用数値専用テキストフィールド -->
              <base-mo-01282
                v-model="item.columnCount"
                :disabled="item.useFlg === '1'"
                :class="item.useFlg === '1' ? 'area-color pointer-none' : ''"
                :oneway-model-value="localOneWay.mo01282OClumncountList"
                @change="onUpdate('columnCount')"
              />
            </div>
          </template>
          <!-- 印刷文字サイズ -->
          <template #[`item.fontSize`]="{ item }">
            <!-- 分子：表用数値専用テキストフィールド -->
            <base-mo-01282
              v-model="item.fontSize"
              :oneway-model-value="localOneWay.mo01282OFontsizeList"
              @change="onUpdate('fontSize')"
            />
          </template>
          <!-- 表示順 -->
          <template #[`item.sort`]="{ item }">
            <!-- 分子：表用数値専用テキストフィールド -->
            <base-mo-01278
              :ref="(el: HTMLInputElement | null) => setRef(el, `input-${item.id}`)"
              v-model="item.sort"
              :style="{ width: '54px' }"
              :oneway-model-value="localOneWay.mo01278Oneway"
              @focus="cacheSortValue(item)"
              @change="($event: Event) => handleSortBlur($event, item)"
            />
          </template>
          <!-- 用紙 -->
          <template #[`item.youshiSize`]="{ item }">
            <!-- 分子：表用数値専用テキストフィールド -->
            <base-mo-01282
              v-model="item.youshiSize"
              :oneway-model-value="localOneWay.mo01282OYoushisizeList"
              @change="onUpdate('youshiSize')"
            />
          </template>
          <!-- 課題立案入力形式 -->
          <template #[`item.freeKbn`]="{ item }">
            <div class="h-100 pointer-auto">
              <!-- 分子：表用数値専用テキストフィールド -->
              <base-mo-01282
                v-model="item.freeKbn"
                :disabled="item.useFlg === '1'"
                :class="item.useFlg === '1' ? 'area-color pointer-none' : ''"
                :oneway-model-value="localOneWay.mo01282OFreekbnList"
                @change="onUpdate('freeKbn')"
              />
            </div>
          </template>
          <!-- 課題チェック表示切替 -->
          <template #[`item.kirikaeKbn`]="{ item }">
            <!-- 分子：表用数値専用テキストフィールド -->
            <base-mo-01282
              v-model="item.kirikaeKbn"
              :oneway-model-value="localOneWay.mo01282OKirikaekbnList"
              @change="onUpdate('kirikaeKbn')"
            />
          </template>
          <!-- 適用 -->
          <template #[`item.tekiyouFlg`]="{ item }">
            <!-- 分子：表用数値専用テキストフィールド -->
            <base-mo00018
              v-model="item.tekiyouFlg"
              :oneway-model-value="localOneWay.mo00018OnewayType"
              :class="[item.tekiyouFlg.modelValue ? 'point-event' : '']"
              @change="onchange(item)"
            />
          </template>
          <!-- ページングを非表示 -->
          <template #bottom />
        </base-mo-01354>
      </div>
    </c-v-form>
  </div>

  <!-- 説明:※全共通 ただし適用は事業所単位で保存  -->
  <div class="font-size-text mt-4">
    {{ t('label.all-common-jigyousyou_tani') }}
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <g-base-or21814 v-bind="or21814_2" />
  <g-base-or21815 v-bind="or21815" />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

.pointer-none {
  pointer-events: none !important;
}

.pointer-auto {
  pointer-events: auto !important;
}

.font-size-text {
  font-size: 12px;
}

.area-color {
  background-color: rgb(var(--v-theme-black-200));
}

.point-event {
  pointer-events: none !important;
}

:deep(.v-data-table) {
  --v-table-header-height: 32px !important;
}
:deep(.full-width-field) {
  width: 100% !important;
}
.child-center {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}
.background-transparent {
  background-color: transparent !important;
}

:deep(.v-selection-control),
:deep(.v-selection-control__wrapper),
:deep(.v-selection-control__wrapper .v-selection-control__input) {
  min-height: 29px !important;
  height: 29px !important;
  width: 29px !important;
}
:deep(.v-table--density-compact) {
  --v-table-row-height: 32px !important;
}
</style>
