<script setup lang="ts">
/**
 * GUI01213_シミュレーション
 *
 * @description
 * シミュレーション
 *
 * <AUTHOR>
 */
import { definePageMeta, reactive, ref, useScreenStore, useSystemCommonsStore } from '#imports'
import { Or36245Const } from '~/components/custom-components/organisms/Or36245/Or36245.constants'
import { Or36245Logic } from '~/components/custom-components/organisms/Or36245/Or36245.logic'
import type { Or36245OnewayType } from '~/types/cmn/business/components/Or36245Type'
import { useCmnCom } from '~/utils/useCmnCom'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * 変数定義
 **************************************************/
// 画面ID
const screenId = 'GUI01213'
// ルーティング
const routing = 'care-manager/simulation'
// 画面物理名
const screenName = 'simulation'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const systemCommonsStore = useSystemCommonsStore()
const or36245 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: Or36245Const.CP_ID(0) },
})

// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or36245Logic.initialize(pageComponent.uniqueCpId)
}

// 子コンポーネントのユニークIDを設定する
or36245.value.uniqueCpId = pageComponent.uniqueCpId

// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

const or36245Oneway: Or36245OnewayType = {
  /** 事業所ID */
  officeId: {
    modelValue: '1',
  },
  /** 事業所情報リスト */
  officeInfoList: [
    { officeId: '1', officeName: '特別養護 ほのぼの' },
    { officeId: '2', officeName: 'はれやか通所リハ' },
    { officeId: '3', officeName: '配食（独）ほのぼの' },
    { officeId: '4', officeName: '通所リハ ４所' },
    { officeId: '5', officeName: '通所リハ ５所' },
  ],
  /** 利用者ID */
  userId: '1',
  /** 画面名称 */
  screenName: 'using-slip',
  /** システム年月 */
  sysYearMonth: systemCommonsStore.getSystemDate ?? '',
  /** 事業者グループ適用ID */
  officeGroupApplyId: '1',
  /** 適用事業所IDリスト */
  applyOfficeIdList: ['201', '202', '203'],
  /** システムコード */
  sysCode: systemCommonsStore.getSystemCode ?? '',
  /** 事業所CD */
  svJigyoCd: systemCommonsStore.getSvJigyoCd ?? '',
  /** 法人ID */
  corporationId: systemCommonsStore.getHoujinId ?? '',
  /** 施設ＩＤ */
  facilityId: systemCommonsStore.getShisetuId ?? '',
  /** ライセンス配列 */
  licenseList: [],
  /** 和暦の元号開始年リスト */
  startYearList: [],
  /** 機能情報リスト */
  functionInfoList: [],
  /** e-文書の履歴保存フラグ */
  historySaveFlg: false,
  /** 事業名（略称） */
  officeName: '',
  /** ロケーション */
  local: '0',
  /** 機能ID */
  functionId: '',
  /** 被保険者番号 */
  insuredNo: '',
  /** 保険者番号 */
  insurNo: '',
  /** 保険者名 */
  insurName: '',
  /** 職員ID */
  staffId: systemCommonsStore.getStaffId ?? '',
  /** 支援事業者ID */
  shienId: '1',
  /** 利用者リスト */
  userIds: [],
  /** 50音フィルタ */
  gojuonFilter: '全',
  /** ログイン番号 */
  loginCd: systemCommonsStore.getLoginInfo?.loginNumber ?? '',
  /** ログインユーザタイプ */
  loginUserType: systemCommonsStore.getLoginInfo?.userType ?? '',
  /** 電子カルテ連携フラグ */
  emrLinkFlag: systemCommonsStore.getElectronicMedicalRecordCooperationFlag,
}

const or36245Data = reactive({
  /** 利用者ID */
  userId: ''
})
</script>

<template>
  <g-custom-or-36245
    v-bind="or36245"
    v-model="or36245Data"
    :oneway-model-value="or36245Oneway"
  />
</template>

<style scoped lang="scss">
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
