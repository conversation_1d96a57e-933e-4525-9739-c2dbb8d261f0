import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/** 初期取得入力エンティティ */
export interface WeekTableInitInEntity extends InWebEntity {
  /** 施設ID */
  shisetuId: string
  /** 利用者ID */
  userId: string
  /** 事業所ID */
  svJigyoId: string
  /** 種別ID */
  syubetsuId: string
  /** 計画期間ID */
  sc1Id: string
  /** 適用事業所ＩＤリスト */
  jigyoList: string[]
  /** 適用事業所グループID */
  jigyoGpId: string
}

/**
 * 初期取得出力エンティティ
 */
export interface WeekTableInitOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** 期間管理フラグ */
    kikanFlag: string
    /** 新規用有効期間ID */
    termNewId: string
    /** 期間リスト */
    taishokikanList: KikanObj[]
    /** 期間インデックス */
    kikanIndex: string
    /** 期間総件数 */
    kikanAllCount: string
    /** 履歴リスト */
    week1List: RirekiObj[]
    /** 履歴インデックス */
    rirekiIndex: string
    /** 履歴総件数 */
    rirekiAllCount: string
    /** 詳細リスト */
    week2List: WeekList[]
    /** 日課利用フラグ */
    dayFlg: string
    /** 利用表利用フラグ */
    riyoFlg: string
  }
}
/** 計画期間情報 */
export interface KikanObj {
  /** 開始日 */
  startYmd: string
  /** 終了日 */
  endYmd: string
  /** 期間ID */
  sc1Id: string
}
/**
 * 履歴情報
 */
export interface RirekiObj {
  /** 週間表ID */
  week1Id: string
  /** 期間ID */
  sc1Id: string
  /** 作成日 */
  createYmd: string
  /** 職員ID */
  shokuId: string
  /** 作成者 */
  shokuKnj: string
  /** ケースNo. */
  caseNo: string
  /** 当該年月 */
  tougaiYm: string
  /** 有効期間ID */
  termid: string
  /** 改訂フラグ */
  kaiteiFlg: string
  /** 週単位以外サービス */
  wIgaiKnj: string
}
/**
 * 詳細リスト
 */
export interface WeekList {
  /** 詳細ID */
  week2Id: string
  /** 週間表ID */
  week1Id: string
  /** 利用者ＩＤ */
  userid: string
  /** 曜日 */
  youbi: string
  /** 開始時間 */
  kaishiJikan: string
  /** 終了時間 */
  shuuryouJikan: string
  /** 内容CD */
  naiyoCd: string
  /** 内容 */
  naiyoKnj: string
  /** メモ */
  memoKnj: string
  /** 文字サイズ */
  fontSize: string
  /** 表示モード */
  dispMode: string
  /** 文字位置 */
  alignment: string
  /** サービス種類 */
  svShuruiCd: string
  /** サービス項目（台帳） */
  svItemCd: string
  /** サービス事業者CD */
  svJigyoId: string
  /** サービス種類名称 */
  svShuruiKnj: string
  /** サービス項目名称 */
  svItemKnj: string
  /** サービス事業者名称 */
  svJigyoKnj: string
  /** サービス事業者略称 */
  svJigyoRks: string
  /** 文字カラー */
  fontColor: string
  /** 背景カラー */
  backColor: string
  /** 時間表示区分 */
  timeKbn: string
  /** 週単位以外文字 */
  igaiMoji: string
  /** 週単位以外のサービス区分 */
  igaiKbn: string
  /** 週単位以外のサービス（日付指定） */
  igaiDate: string
  /** 週単位以外のサービス（曜日指定） */
  igaiWeek: string
  /** 福祉用具貸与の単価 */
  svTani: string
  /** 福祉用具貸与マスタID */
  fygId: string
  /** 枠外表示するかのフラグ */
  wakugaiFlg: string
  /** 更新区分 */
  updateKbn: string
  /** 加算リスト */
  week3List: Week3List
}
/** 加算リスト */
export interface Week3List {
  /** 加算データID */
  id: string
  /** 加算サービス名 */
  svKasanKnj: string
  /** 加算サービス事業者ID */
  svJigyoId: string
  /** 加算サービス項目ID */
  svItemCd: string
  /** 回数 */
  kaisuu: string
  /** 福祉用具貸与の単価 */
  svTani: string
  /** 福祉用具貸与マスタID */
  fygId: string
}
/**
 * 適用事業所一覧情報
 */
export interface JigyoList {
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** サービス事業者ID */
  svJigyoId: string
  /** 事業所名 */
  jigyoNameKnj: string
  /** 適用フラグ */
  tekiyoFlg: string
}
