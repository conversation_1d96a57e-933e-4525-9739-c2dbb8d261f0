<script setup lang="ts">
/**
 * Or28813:有機体:フリーアセスメントチェック項目マスタ
 * GUI00891_フリーアセスメントチェック項目マスタ
 *
 * @description
 * フリーアセスメントチェック項目マスタ
 *
 * <AUTHOR>
 */

import { reactive, onMounted, ref, nextTick } from 'vue'
import { useScreenOneWayBind } from '#imports'
import type {
  Or28813Type,
  Or28813OnewayType,
  freeAssessmentCheckItemMaster,
  Or28813onewayModelValueType,
} from '~/types/cmn/business/components/Or28813Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type {
  freeAssessmentCheckItemMasterInEntity,
  freeAssessmentCheckItemMasterOutEntity,
  KghMocCheckSaimokuList,
} from '~/repositories/cmn/entities/FreeAssessmentCheckMasterEntity'
import { Or28813Const } from '~/components/custom-components/organisms/Or28813/Or28813.constants'
import type {
  freeAssessmentCheckItemMasterInfoType,
  Or28813StateType,
  ResData,
} from '~/components/custom-components/organisms/Or28813/Or28813.type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or28813Type
  onewayModelValue: Or28813onewayModelValueType
  uniqueCpId: string
  parentUniqueCpId: string
}
const props = defineProps<Props>()

const local = reactive({
  or28813: {
    ...props.modelValue,
  } as Or28813Type,
  activated: [props.modelValue.value],
})

const innerDiv = ref<HTMLElement>()

const localOneway = reactive({
  or28813Oneway: {
    items: [],
    openAll: false,
    openActivated: false,
    disabled: false,
    height: Or28813Const.DEFAULT.NULL,
    maxHeight: Or28813Const.DEFAULT.NULL,
    minHeight: Or28813Const.DEFAULT.NULL,
    width: Or28813Const.DEFAULT.NULL,
    maxWidth: Or28813Const.DEFAULT.NULL,
    minWidth: Or28813Const.DEFAULT.NULL,
    color: Or28813Const.DEFAULT.NULL,
  } as Or28813OnewayType,
})
const systemCommonsStore = useSystemCommonsStore()
let respDataInfoType: freeAssessmentCheckItemMasterInfoType = {
  title: Or28813Const.DEFAULT.NULL,
  hierarchy: Or28813Const.DEFAULT.NUM_0,
  parentId: Or28813Const.DEFAULT.NUM_0,
  id: Or28813Const.DEFAULT.NUM_0,
  sort: Or28813Const.DEFAULT.NUM_0,
  updateCategory: Or28813Const.DEFAULT.NUM_0,
  kinouKnj: Or28813Const.DEFAULT.NULL,
  kinouId: Or28813Const.DEFAULT.NULL,
  kghMocCheckYoushikiTreeList: [],
}
/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or28813StateType>({
  cpId: Or28813Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    moveFlag: (value) => {
      if (value) {
        local.or28813.moveFlag = value
      }
    },
    executeFlag: (value) => {
      switch (value) {
        case 'reload':
          reLoad()
          break
        case 'flush':
          void flush()
          break
      }
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue', 'update'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await init()
  const respData: ResData = {
    id: Or28813Const.DEFAULT.NUM_1,
    hierarchy: Or28813Const.DEFAULT.NUM_1,
    youshikiId: Or28813Const.DEFAULT.NULL,
    koumokuId: Or28813Const.DEFAULT.NULL,
    saimokuId: Or28813Const.DEFAULT.NULL,
  }
  emit('update:modelValue', respData)
})

/**
 * AC001_初期情報取得
 */
async function init() {
  await freeAssessmentCheckItemMasterInit()

  //フリーアセスメントチェック項目マスタツリーの作成
  localOneway.or28813Oneway.items = getTreeData([], [respDataInfoType])

  await nextTick()
  const target = innerDiv.value?.querySelector('#v-list-group--id-7219') as HTMLElement | null
  target?.click()

  innerDiv.value!.querySelector('button')!.click()
}

/**
 * フリーアセスメントチェック項目マスタ情報
 */
async function freeAssessmentCheckItemMasterInit() {
  // バックエンドAPIから初期情報取得
  const inputData: freeAssessmentCheckItemMasterInEntity = {
    sysCd: Or28813Const.DEFAULT.EMIT_TYPE_SYSCD,
    kinouKbn: Or28813Const.DEFAULT.EMIT_TYPE_KINOUKBN,
    kinouId: Or28813Const.DEFAULT.EMIT_TYPE_KINOUID,
    houjinId: systemCommonsStore.getHoujinId ?? Or28813Const.DEFAULT.STR_1,
    shisetuId: systemCommonsStore.getShisetuId ?? Or28813Const.DEFAULT.STR_1,
    svJigyoId: systemCommonsStore.getSvJigyoId ?? Or28813Const.DEFAULT.STR_1,
  }
  const medicineClassificationMasterResp: freeAssessmentCheckItemMasterOutEntity =
    await ScreenRepository.select('kghMocCheckTreeSelect', inputData)
  if (medicineClassificationMasterResp) {
    respDataInfoType = medicineClassificationMasterResp.data.tree
  }
}

/**
 * ツリーデータを作成
 *
 * @param treeData - ツリーデータ
 *
 * @param items -フリーアセスメントチェック項目マスタリスト
 */
const getTreeData = (
  treeData: freeAssessmentCheckItemMaster[],
  items: freeAssessmentCheckItemMasterInfoType[]
) => {
  items.forEach((item) => {
    let temITem: freeAssessmentCheckItemMaster = {
      title: item.kinouKnj,
      hierarchy: Or28813Const.DEFAULT.NUM_1,
      parentId: Or28813Const.DEFAULT.NUM_0,
      id: Number(item.kinouId),
      key: String(item.kinouId),
      children: [],
    }
    if (item?.kghMocCheckYoushikiTreeList.length) {
      temITem = {
        ...temITem,
        children: item?.kghMocCheckYoushikiTreeList.map((item_2) => {
          return {
            title: item_2.youshikiKnj,
            hierarchy: Or28813Const.DEFAULT.NUM_2,
            parentId: Number(item.kinouId),
            id: Or28813Const.DEFAULT.HIERARCHY_2 + item_2.youshikiId,
            key: String(item_2.youshikiId),
            youshikiId: item_2.youshikiId,
            children: item_2?.kghMocCheckKoumokuList.map((item_3) => {
              return {
                title: item_3.koumokuKnj,
                hierarchy: Or28813Const.DEFAULT.NUM_3,
                parentId: Or28813Const.DEFAULT.HIERARCHY_2 + item_2.youshikiId,
                youshikiId: item_2.youshikiId,
                koumokuId: item_3.koumokuId,
                id: Or28813Const.DEFAULT.HIERARCHY_3 + item_3.koumokuId,
                key: String(item_3.koumokuId),
                children: item_3?.kghMocCheckSaimokuList?.map((item_4: KghMocCheckSaimokuList) => {
                  return {
                    title: item_4.saimokuKnj,
                    hierarchy: Or28813Const.DEFAULT.NUM_4,
                    parentId: Or28813Const.DEFAULT.HIERARCHY_3 + item_3.koumokuId,
                    koumokuId: item_3.koumokuId,
                    youshikiId: item_2.youshikiId,
                    id: Or28813Const.DEFAULT.HIERARCHY_4 + item_4.saimokuId,
                    key: String(item_4.saimokuId),
                    saimokuId: item_4.saimokuId,
                  } as freeAssessmentCheckItemMaster
                }),
              }
            }),
          } as freeAssessmentCheckItemMaster
        }),
      }
    }
    treeData.push(temITem)
  })
  return treeData
}
/**
 * オブジェクト検索
 *
 * @param data - 検索データ
 *
 * @param targetId - 目標ID
 */
const findObjectById = (
  data: freeAssessmentCheckItemMaster | freeAssessmentCheckItemMaster[],
  targetId: number
): freeAssessmentCheckItemMaster | undefined => {
  if (Array.isArray(data)) {
    for (const element of data) {
      const found = findObjectById(element, targetId)
      if (found !== undefined) {
        return found
      }
    }
  } else if (typeof data === 'object' && data !== null) {
    if (data.id === targetId) {
      return data
    }
    if (data.children) {
      const found = findObjectById(data.children, targetId)
      if (found !== undefined) {
        return found
      }
    }
  }

  return undefined
}
/**
 * Or10406 ダイヤログキャンセルボタン押下hierarchy戻る
 */
const reLoad = () => {
  local.activated = [props.onewayModelValue.id]
}
const flush = async () => {
  await freeAssessmentCheckItemMasterInit()

  //フリーアセスメントチェック項目マスタツリーの作成
  localOneway.or28813Oneway.items = getTreeData([], [respDataInfoType])

  local.activated = [props.onewayModelValue.id]
}

const activated = (param: number[]) => {
  const currentId = param[0]
  // 戻り値設定
  const obj = findObjectById(localOneway.or28813Oneway.items, param[0])

  const respData: ResData = {
    id: currentId,
    hierarchy: obj?.hierarchy,
    youshikiId: obj?.youshikiId,
    koumokuId: obj?.koumokuId,
    saimokuId: obj?.saimokuId,
  }

  emit('update', respData)
}
</script>

<template>
  <div class="tree_content">
    <div
      ref="innerDiv"
      class="scroll-x"
    >
      <c-v-treeview
        v-model:activated="local.activated"
        v-model:opened="local.or28813.opened"
        v-bind="{ ...$attrs, ...localOneway.or28813Oneway }"
        activatable
        active-strategy="single-independent"
        active-class="activated-tree-item"
        color="blue-700"
        density="compact"
        item-props
        item-value="id"
        @update:activated="activated"
      >
        <template #prepend="{ item }">
          <base-at-icon
            :class="{ 'no-children': !(item.children?.length > 0) }"
            :icon="item.prependIcon ? item.prependIcon : 'folder fill'"
            :color="item.prependIconColor ? item.prependIconColor : 'blue-500'"
          />
        </template>
        <template #append="{ item }">
          <base-at-icon
            v-if="item.appendIcon"
            :icon="item.appendIcon"
            :color="item.appendIconColor ? item.appendIconColor : 'blue-500'"
          />
        </template>
        <template #title="{ item }">
          <label class="label-width">{{ item.title }}</label>
        </template>
      </c-v-treeview>
    </div>
  </div>
</template>
<style scoped lang="scss">
:deep(.activated-tree-item) {
  pointer-events: none;
}
:deep(.v-btn) {
  pointer-events: auto;
}

:deep(.no-children) {
  margin-left: 28px !important;
}
:deep(.v-list-item__spacer) {
  width: 12px !important;
}
.tree_content {
  width: 225px;
  height: 492px;
  overflow-y: auto;
}
.scroll-x {
  width: 500px;
}
.label-width {
  min-width: 80px;
}
</style>
