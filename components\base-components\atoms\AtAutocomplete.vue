<script lang="ts">
import { defineComponent, ref } from 'vue'
import { VAutocomplete } from 'vuetify/components'

export default defineComponent({
  name: 'BaseAutocomplete',
  extends: VAutocomplete,
  props: {
    /**
     * コンポーネント名
     * デフォルト：空文字
     * ※コンポーネント名が重複すると警告が発生するので、Page内では一意性を保つこと
     */
    name: {
      type: String,
      default: '',
      required: false,
    },
    variant: {
      type: String,
      default: 'outlined',
    },
    density: {
      type: String,
      default: 'compact',
    },
    modelValue: {
      type: [String, Array<string>],
      default: undefined,
    },
    search: {
      type: String,
      default: '',
    },
    // デフォルト値を定める場合や、props定義をオーバーライドする場合はここに定義を追加する
  },
  /**
   * イベント登録
   * マウス関連
   *  click
   *  dbclick
   *  mousedown
   *  mouseup
   *  mousemove
   *  mouseout
   *  dragstart
   *  drag
   *  dragenter
   *  dragleave
   *  dragover
   *  drop
   *  dragend
   * キーボード関連
   *  keydown
   *  keypress
   *  keyup
   * フォーム関連
   *  select
   *  change
   *  submit
   *  reset
   *  focus
   *  blur
   *  focusin
   *  focusout
   * タッチ関連
   *  touchstart
   *  touchend
   *  touchmove
   *  touchenter
   *  touchleave
   *  touchchancel
   *
   *  当コンポーネントは、以下のイベントをサポートする
   *  v-modelの更新（'update:modelValue'）とchange、focus、blur
   */
  emits: ['update:modelValue', 'change', 'focus', 'blur', 'update:search'],
  data() {
    return {
      internalValue: ref(this.$props.modelValue), // 内部で保持する値
    }
  },
  watch: {
    // 外部の値が変更されたら、内部の値も更新
    modelValue(newValue: string) {
      this.internalValue = newValue
    },
    // 内部の値が変更されたら、外部にイベントを発行
    internalValue(newInternalValue) {
      this.$emit('update:modelValue', newInternalValue)
    },
  },
})
</script>
<template>
  <v-autocomplete
    :ref="name"
    :search="search"
    v-model="internalValue"
    @update:search="$emit('update:search', $event)"
    v-bind="{ ...$attrs, ...$props }"
    @change="$emit('change', $event)"
    @focus="$emit('focus', $event)"
    @blur="$emit('blur', $event)"
  >
    <template
      v-for="(slot, slotName) in $slots"
      #[slotName]="data"
    >
      <slot
        key="1"
        :name="slotName"
        v-bind="data"
      />
    </template>
    <template #message="{ message }">
      <base-at-icon
        color="error"
        icon="error"
        size="16"
      />
      {{ message }}
    </template>
  </v-autocomplete>
</template>
<style scoped>
:deep(.v-field__input) {
  min-height: 36px;
}
</style>
