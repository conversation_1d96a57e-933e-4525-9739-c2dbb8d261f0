<script setup lang="ts">
/**
 * TeX0012:［アセスメント］画面（居宅）テンプレート
 *
 * @description
 * ［アセスメント］画面（居宅）テンプレート、以下のコンポーネントをタブで表示する。
 * GUI00794_［アセスメント］画面（居宅）（1）
 * GUI00795_［アセスメント］画面（居宅）（2）
 *
 * <AUTHOR>
 */

import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { OrD2002Const } from '../../organisms/OrD2002/OrD2002.constants'
import { Or28992Const } from '../../organisms/Or28992/Or28992.constants'
import { OrX0008Logic } from '../../organisms/OrX0008/OrX0008.logic'
import { OrX0008Const } from '../../organisms/OrX0008/OrX0008.constants'
import { Or17392Const } from '../../organisms/Or17392/Or17392.constants'
import { Or17393Const } from '../../organisms/Or17393/Or17393.constants'
import { OrX0009Logic } from '../../organisms/OrX0009/OrX0009.logic'
import { OrX0001Const } from '../../organisms/OrX0001/OrX0001.constants'
import { Or27562Logic } from '../../organisms/Or27562/Or27562.logic'
import { Or27562Const } from '../../organisms/Or27562/Or27562.constants'
import { Or26257Const } from '../../organisms/Or26257/Or26257.constants'
import { OrX0009Const } from '../../organisms/OrX0009/OrX0009.constants'
import { Or17391Logic } from '../../organisms/Or17391/Or17391.logic'
import { Or17391Const } from '../../organisms/Or17391/Or17391.constants'
import { OrX0007Logic } from '../../organisms/OrX0007/OrX0007.logic'
import { Or15169Const } from '../../organisms/Or15169/Or15169.constants'
import { Or15194Const } from '../../organisms/Or15194/Or15194.constants'
import { Or15109Const } from '../../organisms/Or15109/Or15109.constants'
import { Or15108Const } from '../../organisms/Or15108/Or15108.constants'
import { Or15133Const } from '../../organisms/Or15133/Or15133.constants'
import { OrX0007Const } from '../../organisms/OrX0007/OrX0007.constants'
import { Or15154Const } from '../../organisms/Or15154/Or15154.constants'
import { Or15183Const } from '../../organisms/Or15183/Or15183.constants'
import { Or15141Const } from '../../organisms/Or15141/Or15141.constants'
import { Or56886Logic } from '../../organisms/Or56886/Or56886.logic'
import { Or56886Const } from '../../organisms/Or56886/Or56886.constants'
import type { Or15133ValuesType } from '../../organisms/Or15133/Or15133.Type'
import type { Or15183ValuesType } from '../../organisms/Or15183/Or15183.Type'
import type { Or15141ValuesType } from '../../organisms/Or15141/Or15141.Type'
import type { Or15194ValuesType } from '../../organisms/Or15194/Or15194.Type'
import type { Or15169ValuesType } from '../../organisms/Or15169/Or15169.Type'
import type { Or15108ValuesType } from '../../organisms/Or15108/Or15108.type'
import type { Or15109ValuesType } from '../../organisms/Or15109/Or15109.type'
import type { Or15154ValuesType } from '../../organisms/Or15154/Or15154.type'
import type { Or56886Param } from '../../organisms/Or56886/Or56886.type'
import { TeX0012Logic } from './TeX0012.logic'
import type { TeX0012StateType } from './TeX0012.type'
import { TeX0012Const } from './TeX0012.constants'
import {
  hasPrintAuth,
  hasRegistAuth,
  useGyoumuCom,
  useJigyoList,
  useNuxtApp,
  useScreenInitFlg,
  useScreenOneWayBind,
  useScreenStore,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type {
  Or21814OnewayType,
  Or21814EventType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00028Type } from '~/types/business/components/Mo00028Type'
import type { Or28992OnewayType } from '~/types/cmn/business/components/Or28992Type'
import type { TeX0012OnewayType, TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
import type {} from '~/repositories/cmn/entities/CpnTucGdlComInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import type { OrX0001OnewayType, OrX0001Type } from '~/types/cmn/business/components/OrX0001Type'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or27562OnewayType } from '~/types/cmn/business/components/Or27562Type'
import type { Or17391OnewayType } from '~/types/cmn/business/components/Or17391Type'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'

import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type {
  AuthorityFlag,
  HistoryInfo1,
  HospitalizationTimeInfoOfferHistorySelectInEntity,
  HospitalizationTimeInfoOfferHistorySelectOutEntity,
  HospitalizationTimeInfoOfferInitInfo,
  HospitalizationTimeInfoOfferInitInfoSelectInEntity,
  HospitalizationTimeInfoOfferInitInfoSelectOutEntity,
  HospitalizationTimeInfoOfferNewInfoSelectInEntity,
  HospitalizationTimeInfoOfferNewInfoSelectOutEntity,
  HospitalizationTimeInfoOfferPeriodSelectInEntity,
  HospitalizationTimeInfoOfferPeriodSelectOutEntity,
  HospitalizationTimeInfoOfferUpdateInEntity,
  HospitalizationTimeInfoOfferUpdateOutEntity,
  SaveInfo,
} from '~/repositories/cmn/entities/HospitalizationTimeInfoOfferPeriodSelectServiceEntity'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00038Type } from '~/types/business/components/Mo00038Type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00030Type } from '~/types/business/components/Mo00030Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo01408Type } from '~/types/business/components/Mo01408Type'
import type { OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import type {
  OrX0008OnewayType,
  OrX0008Type,
  RirekiInfo,
} from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type {
  Or21815EventType,
  Or21815StateType,
} from '~/components/base-components/organisms/Or21815/Or21815.type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'

const $log = useNuxtApp().$log as DebugLogPluginInterface

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: TeX0012OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
/**
 *共有処理
 */
const gyoumuCom = useGyoumuCom()
const { setChildCpBinds, getChildCpBinds } = useScreenUtils()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()
// route共有情報
// 事業所変更監視
const { jigyoListWatch } = useJigyoList()
const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or17391 = ref({
  uniqueCpId: '',
  parentUniqueCpId: props.uniqueCpId,
  or00249UniqueCpId: '',
  or00094UniqueCpId: '',
}) // GUI00807_アセスメント複写
const or27562 = ref({ uniqueCpId: '' }) // GUI00626_アセスメントマスタ
const or41179 = ref({ uniqueCpId: '' }) // 事業所選択
const orX0009 = ref({ uniqueCpId: '' })
const or56886 = ref({ uniqueCpId: '' }) // GUI1304_印刷
const or21814_1 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes, no, cancel)
const or21814_2 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes)
const or21814_3 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes,no)
const or21815_1 = ref({ uniqueCpId: '' }) // 確認ダイアログ
const orHeadLine = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const or00094 = ref({ uniqueCpId: '' })
const orD2002 = ref({ uniqueCpId: '' })
const or28992 = ref({
  uniqueCpId: Or28992Const.CP_ID(0),
  parentUniqueCpId: props.uniqueCpId,
})

const orX0007 = ref({ uniqueCpId: OrX0007Const.CP_ID(0) })
const orX0008 = ref({ uniqueCpId: OrX0008Const.CP_ID(0) })
const or17392 = ref({
  uniqueCpId: Or17392Const.CP_ID(0),
  parentUniqueCpId: props.uniqueCpId,
})
const or17393 = ref({
  uniqueCpId: Or17393Const.CP_ID(0),
  parentUniqueCpId: props.uniqueCpId,
})
const orX0001 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' })

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  let editFlg = false
  switch (local.mo00043.id) {
    case Or17392Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or17392.value.uniqueCpId)
      break
    case Or17393Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or17393.value.uniqueCpId)
      break
  }
  console.log(editFlg, '1133333222')
  return editFlg
})

// ローカルTwoway
const local = reactive({
  orX0008: {
    /**
     *計画書ID（履歴Id）
     */
    createId: '0',
    /**
     *履歴更新フラグ
     */
    createUpateFlg: '0',
  } as OrX0008Type,
  periodInfo: {},
  listDataGroup: {} as Record<string, CodeType[]>,
  // 記入日
  createDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // タブ
  mo00043: {
    id: '',
  } as Mo00043Type,
  mo00028: {
    panel: ref(1),
  } as Mo00028Type,
  // 削除ダイアログ
  orX0001Type: {
    deleteSyubetsu: '',
  } as OrX0001Type,
  copyDisabled: false,
})

// 権限フラグ
const isPermissionViewAuth = ref(true)

// 画面情報の取得中かどうか
const isLoading = ref(false)

// 画面初期化完了フラグ
let isInitCompleted = false

// 選択中利用者ID
const userId = ref('')

// 変更タブIDを保持
const tabIdBk = ref('')

// 期間管理フラグ
const plannningPeriodManageFlg = ref(TeX0012Const.DEFAULT.PLANNING_PERIOD_MANAGE)

// 履歴更新回数
const historyModifiedCnt = ref('0')

// 履歴表示フラグ
const isHistoryShow = ref(true)

// 画面情報領域のsupplement内のisInitの取得およびonMountedにfalseへ更新する処理を登録
const isInit = useScreenInitFlg()

// 画面処理パターン
const clickMode = ref('')

// 複写コンポネントキー
const copyComponentKey = ref('')

// 複写コンポネントキー
const deleteKbn = ref(TeX0012Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL)

// 事業所IDバックアップ
const jigyoId = ref('')

// 履歴IDバックアップ
const teikyouIdOld = ref('')

// 入力作成日バックアップ
const createDateOld = ref('')

// ローカルOneway
const localOneway = reactive({
  // 計画対象期間選択
  orX0007Oneway: {
    kindId: systemCommonsStore.getSyubetu ?? '',
  } as OrX0007OnewayType,
  tex0012Oneway: {
    /** 続柄マスタ情報 */
    relationshipMasterInfoList: [],
    /** 介護保険情報リスト */
    nursingCareInsuranceInfoList: [],
    /** 担当情報リスト */
    tantoInfoList: [],
    /** 事業所情報リスト */
    jigyoInfoList: [],
    /**  親族関係者情報リスト */
    kinshipInfoList: [],
    confirmationInfo: {},
  } as HospitalizationTimeInfoOfferInitInfo,
  // 作成者選択
  orX0009Oneway: {
    showId: '',
    // モード
    mode: TeX0012Const.DEFAULT.SELECT_MODE_12,
    isDisabled: false,
  } as OrX0009OnewayType,

  // アセスメント複写
  or17391Oneway: {
    periodManagementFlg: '',
  } as Or17391OnewayType,
  // アセスメントマスタ
  or27562Oneway: {
    /** 事業者ID */
    svJigyoId: '',
    /** 分類1 */
    bunrui1Id: '2',
    /** 分類2 */
    bunrui2Id: '12',
    /** 施設ID */
    shisetuId: '',
  } as Or27562OnewayType,
  // 計画対象期間フラグ
  plainningPeriodManageFlg: false,

  // 期间選択
  periodSelectOneway: {
    /** 期間ID  */
    sc1Id: '',
  },
  // 履歴選択
  orX0008Oneway: {
    createData: {} as RirekiInfo,
  } as OrX0008OnewayType,
  // 履歴選択
  hisotrySelectOneway: {
    /** 履歴情報 */
    historyInfo: {
      /** 提供ID  */
      teikyouId: '',
      /** 期間ID  */
      sc1Id: '',
      /** 記入日  */
      createYmd: '',
      /** 職員ID  */
      chkShokuId: '',
      /** 履歴総件数  */
      krirekiCnt: '',
      /** 履歴番号  */
      krirekiNo: '',
      /** 担当ケアマネID  */
      tantoShokuId: '',
      /** ケアマネジャー氏名  */
      tantoShokuKnj: '',
      /** 居宅介護支援事業所ID  */
      shienJigyoId: '',
      /** 事業所名  */
      jigyoKnj: '',
      youshikiKbn: '',
    } as HistoryInfo1,
    pageBtnAutoDisabled: true,
  },

  // 作成日
  createDateOneway: {
    itemLabel: t('label.entry-date'),
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-row',
      labelClass: 'ma-1',
    }),
    width: '130px',
  } as Mo00020OnewayType,
  // タブ
  mo00043OnewayType: {
    tabItems: [],
  } as Mo00043OnewayType,
  or17392Oneway: {
    mode: TeX0012Const.DEFAULT.MODE_NORMAL,
  } as Or28992OnewayType,
  or17393Oneway: {
    mode: TeX0012Const.DEFAULT.MODE_NORMAL,
  } as Or28992OnewayType,
  // 削除ダイアログ
  orX0001Oneway: {
    createYmd: '',
    kinouKnj: '',
    selectTabName: '',
    startTabName: '',
    endTabName: '',
  } as OrX0001OnewayType,
})

// アセスメント複写ダイアログ表示フラグ
const showDialogOr17391 = computed(() => {
  return Or17391Logic.state.get(or17391.value.uniqueCpId)?.isOpen ?? false
})

// アセスメントマスタダイアログ表示フラグ
const showDialogOr27562 = computed(() => {
  // Or27562のダイアログ開閉状態
  return Or27562Logic.state.get(or27562.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr56886 = computed(() => {
  // Or56886のダイアログ開閉状態
  return Or56886Logic.state.get(or56886.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // 各コードグループの初期化を行います。
  await initCodes([
    // 住環境
    {
      key: 'CONFIRMATION_INFO_LIVING_ENVIRONMENT',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_LIVING_ENVIRONMENT,
    },
    // 省略の選択コード区分
    { key: 'M_CD_KBN_ID_OMITTED', mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_OMITTED },
    // 入院時の要介護度（確定版）
    {
      key: 'CONFIRMATION_INFO_CARE_LEVEL_AT_ADMISSION_CONFIRMED',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_CARE_LEVEL_AT_ADMISSION_CONFIRMED,
    },
    //要支援値
    {
      key: 'SUPPORT_LEVEL_VALUE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SUPPORT_LEVEL_VALUE,
    },
    //要介護値
    {
      key: 'CARE_LEVEL_VALUE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CARE_LEVEL_VALUE,
    },
    //障害高齢者の日常生活自立度
    {
      key: 'CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DISAB',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DISAB,
    },
    //認知症高齢者の日常生活自立度
    {
      key: 'CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DEM',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DEM,
    },
    //自己負担割合有無
    {
      key: 'CONFIRMATION_INFO_OWN_BURDEN_RATIO_BEDSORE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_OWN_BURDEN_RATIO_BEDSORE,
    },
    //服薬状況
    {
      key: 'TAKING_MEDICALTION_SITUATION',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAKING_MEDICALTION_SITUATION,
    },
    //薬剤管理
    {
      key: 'MEDICINE_MANAGEMENT',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEDICINE_MANAGEMENT,
    },
    //診察方法
    {
      key: 'MEDICAL_EXAMINATION_METHOD',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEDICAL_EXAMINATION_METHOD,
    },
    //主介護者（同居別居）
    {
      key: 'CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION,
    },
    //世帯に対する配慮
    {
      key: 'CONFIRMATION_INFO_HOUSEHOLD',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_HOUSEHOLD,
    },
    //退院後の主介護者2
    {
      key: 'CONFIRMATION_INFO_PRIMARY_CAREGIVER',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_PRIMARY_CAREGIVER,
    },
    //介護力見込み
    {
      key: 'CONFIRMATION_INFO_NURSING_CARE_PROSPECT',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_NURSING_CARE_PROSPECT,
    },
    {
      key: 'CONFIRMATION_INFO_PARALYSIS_STATUS',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_PARALYSIS_STATUS,
    },
    {
      key: 'MEAL_FREQUENCY',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEAL_FREQUENCY,
    },
    {
      key: 'MEAL_WATER_RESTRICTION',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEAL_WATER_RESTRICTION,
    },
    {
      key: 'CONFIRMATION_INFO_SWALLOWING_FUNCTION',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_SWALLOWING_FUNCTION,
    },
    {
      key: 'CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY,
    },
    {
      key: 'CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS,
    },
    {
      key: 'CONFIRMATION_INFO_PORTABLE_TOILETS',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_PORTABLE_TOILETS,
    },
    {
      key: 'SLEEPING_STATE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SLEEPING_STATE,
    },
    {
      key: 'CONFIRMATION_INFO_CODE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_CODE,
    },
    {
      key: 'HOSPITALIZATION_FREQUENCY',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_HOSPITALIZATION_FREQUENCY,
    },
  ])
  // ブラウザの「戻る」「進む」ボタンからの遷移時に以前の値を復元したい画面の場合は
  // 画面情報領域のsupplement内のisInitで処理を分岐させて初回表示時のみ画面データの取得を行なう
  // ※値の復元が不要な画面では分岐は不要
  isInitCompleted = false
  if (isInit) {
    // コントロール設定
    await initControls()
    local.mo00043.id = Or17392Const.DEFAULT.TAB_ID
  }
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<TeX0012StateType>({
  cpId: Or17391Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    // 保存完了処理
    isSaveCompleted: (value) => {
      if (value) {
        // 更新後の計画対象期間ID、履歴IDを再設定
        if (localOneway.periodSelectOneway.sc1Id) {
          localOneway.periodSelectOneway.sc1Id =
            TeX0012Logic.data.get(or28992.value.uniqueCpId)?.updateData?.sc1Id ?? ''
        }
        if (localOneway.orX0008Oneway.createData) {
          localOneway.orX0008Oneway.createData.createId =
            TeX0012Logic.data.get(or28992.value.uniqueCpId)?.updateData?.teikyouId ?? ''
        }

        // タブ情報再取得
        void reload()
        // 保存完了フラグをリセット
        setState({ isSaveCompleted: false })
      }
    },
    // タブ変更保存完了フラグ
    tabChangeSaveCompleted: (value) => {
      if (value) {
        // 更新後の計画対象期間ID、履歴IDを再設定
        if (localOneway.periodSelectOneway.sc1Id) {
          localOneway.periodSelectOneway.sc1Id =
            TeX0012Logic.data.get(or28992.value.uniqueCpId)?.updateData?.sc1Id ?? ''
        }
        if (localOneway.orX0008Oneway.createData) {
          localOneway.orX0008Oneway.createData.createId =
            TeX0012Logic.data.get(or28992.value.uniqueCpId)?.updateData?.teikyouId ?? ''
        }

        // タブを変更
        local.mo00043.id = tabIdBk.value

        // 保存完了フラグをリセット
        setState({ tabChangeSaveCompleted: false })
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(0)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or17391Const.CP_ID(1)]: or17391.value,
  [Or27562Const.CP_ID(0)]: or27562.value,
  [Or56886Const.CP_ID(0)]: or56886.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [OrX0009Const.CP_ID(0)]: orX0009.value,
  [OrD2002Const.CP_ID(0)]: orD2002.value,
  [Or26257Const.CP_ID(0)]: or26257.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value, // 確認ダイアログ(yes, no, cancel)
  [Or21814Const.CP_ID(2)]: or21814_2.value, // 確認ダイアログ(yes)
  [Or21814Const.CP_ID(3)]: or21814_3.value, // 確認ダイアログ(yes, no)
  [OrX0001Const.CP_ID(0)]: orX0001.value,
  [Or17392Const.CP_ID(1)]: or17392.value,
  [Or17393Const.CP_ID(1)]: or17393.value,
  [OrX0007Const.CP_ID(0)]: orX0007.value,
  [OrX0008Const.CP_ID(0)]: orX0008.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(0)]: or00249.value,
  [Or00094Const.CP_ID(0)]: or00094.value,
})

// ★利用者一覧詳細表示(Or00248)の「領域キー」を設定
Or00248Logic.state.set({
  uniqueCpId: or00248.value.uniqueCpId,
  state: {
    regionKey: props.uniqueCpId, // 「領域キー」を設定
  },
})

systemCommonsStore.setUserSelectZenkaiSvJigyoIdList([''])
systemCommonsStore.setUserSelectSelfId('')

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange, props.uniqueCpId)

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 新しいデータの値をチェックし、全falseの場合は処理を中断する
    const isAllFalseFlg = Object.values(newValue).every((item) => item === false)
    if (isAllFalseFlg) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      $log.debug('お気に入り')
      setOr11871Event({ favoriteEventFlg: false })
      return
    }
    if (newValue.saveEventFlg) {
      $log.debug('保存')
      // 保存ボタンが押下された場合、保存処理を実行する
      setOr11871Event({ saveEventFlg: false })

      // 削除区分を設定
      deleteKbn.value = TeX0012Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL

      await setEvent({ saveEventFlg: true })
      return
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      $log.debug('新規')
      setOr11871Event({ createEventFlg: false })
      clickMode.value = 'createNew'
      await createNew()
      return
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
      $log.debug('印刷')
      setOr11871Event({ printEventFlg: false })
      clickMode.value = 'print'
      print()
      return
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      $log.debug('マスタ他')
      // アセスメントマスタのダイアログを開く
      Or27562Logic.state.set({
        uniqueCpId: or27562.value.uniqueCpId,
        state: { isOpen: true },
      })
      setOr11871Event({ masterEventFlg: false })
      return
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      clickMode.value = 'delete'
      await _delete()
      setOr11871Event({ deleteEventFlg: false })
      return
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      $log.debug('複写')
      _copy()
      return
    }
  }
)

/**
 * 複写画面のイベントを監視
 */
watch(
  () => Or17391Logic.event.get(Or17391Const.CP_ID(1) + or17391.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue?.confirm === false && newValue?.confirm_multiple === false) {
      return
    }

    setOr17391Event({ confirm: false, confirm_multiple: false })

    // 該当タブで確定を押下する場合
    if (newValue.confirm) {
      // 複写データを取得
      const copyData = Or17391Logic.data.get(or17391.value.uniqueCpId)
      if (copyData) {
        await setEvent({ copyEventFlg: true })
      }
    }

    // 複数タブ上書の場合
    if (newValue.confirm_multiple) {
      // タブを再表示
      await setEvent({ isRefresh: true })
    }
  }
)

/**
 * タブ変更監視
 */
watch(
  () => showDialogOr27562.value,
  () => {
    localOneway.or27562Oneway = {
      /** 事業者ID */
      svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
      /** 分類1 */
      bunrui1Id: '2',
      /** 分類2 */
      bunrui2Id: '12',
      /** 施設ID */
      shisetuId: systemCommonsStore.getShisetuId ?? '',
    }
  }
)

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 利用者一覧における利用者の変更を監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue === undefined) {
      return
    }
  }
)

/**************************************************
 * 関数
 **************************************************/
/**
 * 指定されたコードグループのコードを初期化します。
 *
 * @param codeGroups - コードを取得およびフィルタリングするためのキーとmCdKbnIdを含むオブジェクトの配列。
 */
async function initCodes(codeGroups: { key: string; mCdKbnId: number }[]) {
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList: [...codeGroups] })
  for (const group of codeGroups) {
    local.listDataGroup[group.key] = CmnSystemCodeRepository.filter(group.mCdKbnId) ?? []
  }
}
/**
 *  コントロール初期化
 */
const initControls = async () => {
  isPermissionViewAuth.value = true
  // 子コンポーネントに対して初期設定を行う
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      screenTitleLabel: t('label.hospitalization-info'),
      tooltipTextSaveBtn: t('tooltip.save'),
      tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
      tooltipTextPrintBtn: t('tooltip.care-plan2-print-setting-btn'),
      tooltipTextOptionMenuBtn: t('tooltip.regular-master-icon'),
      showFavorite: true,
      showViewSelect: false,
      viewSelectItems: [],
      showSaveBtn: true,
      disabledSaveBtn: !isPermissionViewAuth.value, //共通処理の保存権限チェックを行う
      disabledPrintBtn: await hasPrintAuth(TeX0012Const.DEFAULT.LINK_AUTH), //共通処理の印刷権限チェックを行う
      showCreateBtn: true,
      showCreateMenuCopy: false,
      showPrintBtn: true,
      showMasterBtn: false,
      showOptionMenuBtn: true,
      showOptionMenuDelete: true,
    },
  })
  clickMode.value = ''

  local.createDate.value =
    systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
  createDateOld.value = local.createDate.value

  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを初期化(yes, no, cancel)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを初期化(yes)
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
    },
  })

  // 確認ダイアログを初期化(yes, no)
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
    },
  })

  localOneway.mo00043OnewayType.tabItems = [
    {
      id: Or17392Const.DEFAULT.TAB_ID,
      title: t('label.outer'),
    },
    {
      id: Or17393Const.DEFAULT.TAB_ID,
      title: t('label.inner'),
    },
  ]

  TeX0012Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      /**
       * 事業所ID
       */
      jigyoId: systemCommonsStore.getSvJigyoId,
      /**
       * 計画対象期間ID
       */
      sc1Id: localOneway.periodSelectOneway.sc1Id,
      /**
       * アセスメントID
       */
      teikyouId: localOneway.orX0008Oneway.createData.createId,
      /**
       * 作成者ID
       */
      createUserId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '',
      /**
       * 作成日
       */
      createYmd: local.createDate.value,
    },
  })
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
function setOr11871Event(event: Record<string, boolean>) {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * Or17391イベント発火
 *
 * @param event - イベント
 */
function setOr17391Event(event: Record<string, boolean>) {
  Or17391Logic.event.set({
    uniqueCpId: Or17391Const.CP_ID(1) + or17391.value.uniqueCpId,
    events: event,
  })
}

/**
 * イベント発火
 *
 * @param event - イベント
 */
async function setEvent(event: Record<string, boolean>) {
  // 複写の場合
  let copyData: object = {}
  if (event.copyEventFlg === true) {
    const or17391Data = Or17391Logic.data.get(or17391.value.uniqueCpId)
    copyData = or17391Data?.resData?.tabData ?? {}
  }

  // 画面共通情報を設定
  setCommonInfo({
    /** 履歴更新回数 */
    historyModifiedCnt: historyModifiedCnt.value,
    /** 選択中タブ */
    activeTabId: local.mo00043.id,
    /** 事業所ID */
    jigyoId: systemCommonsStore.getSvJigyoId,
    /** 計画対象期間ID */
    sc1Id: localOneway.periodSelectOneway.sc1Id,
    /** アセスメントID */
    teikyouId: localOneway.orX0008Oneway.createData.createId,
    /** 作成者ID */
    createUserId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '',
    /** 作成日 */
    createYmd: local.createDate.value,
    /** 複写データ */
    copyData: copyData,
    /** 複写データ */
    deleteKbn: deleteKbn.value,
  })

  if (event.isRefresh) {
    // 画面情報再取得
    void getInitDataInfo()
    // 次の描画までまつ（ターゲットエレメントのサイズが取得できるため）
    //  nextTick()
  }
  if (event.isCreateDateChanged) {
    // 保存ボタンが押下された場合、保存処理を実行する
    // 改訂バージョンをチェック
  }

  if (event.favoriteEventFlg) {
    // お気に入りボタンが押下された場合、お気に入り処理を実行する
  }
  if (event.saveEventFlg) {
    // 保存ボタンが押下された場合、保存処理を実行する
    // 画面変更チェック
    await save()
  }
  if (event.createEventFlg) {
    // 新規ボタンが押下された場合、新規作成処理を実行する
    void handleCreate()
  }
  if (event.printEventFlg) {
    // 印刷ボタンが押下された場合、印刷設定画面を表示する
  }
  if (event.masterEventFlg) {
    // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
  }
  if (event.deleteEventFlg) {
    // 削除ボタンが押下された場合、削除処理を実行する
  }
  if (event.copyEventFlg) {
    // 複写ボタンが押下された場合、複写処理を実行する
    // const copyData = local.commonInfo.copyData as IAssessmentFaceInfoSelectOutEntity
  }
}
/**
 * 新規
 *
 */
async function handleCreate() {
  try {
    // バックエンドAPIから初期情報取得
    const inputData: HospitalizationTimeInfoOfferNewInfoSelectInEntity = {
      /** 利用者ID */
      userid: userId.value,
      /** 法人ID */
      hojinId: systemCommonsStore.getHoujinId ?? '',
      /** 施設ID */
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      /** 事業者ID */
      svJigyoId: jigyoId.value,
      /** 基準日 */
      appYmd: systemCommonsStore.getSystemDate ?? '',
      ...(local.periodInfo as AuthorityFlag),
    }

    const resData: HospitalizationTimeInfoOfferNewInfoSelectOutEntity =
      await ScreenRepository.select('hospitalizationTimeInfoOfferNewInfoSelect', inputData)
    if (resData?.data) {
      setInit({
        ...localOneway.tex0012Oneway,
        confirmationInfo: resData.data.newInfo,
      })
    }
  } finally {
    isLoading.value = false
  }
}

/**
 * 保存
 *
 */
async function save() {
  const or17392Data = getChildCpBinds(or17392.value.uniqueCpId, {
    or15169: { cpPath: Or15169Const.CP_ID(1) },
    or15194: { cpPath: Or15194Const.CP_ID(1) },
    or15133: { cpPath: Or15133Const.CP_ID(1) },
    or15183: { cpPath: Or15183Const.CP_ID(1) },
    or15141: { cpPath: Or15141Const.CP_ID(1) },
  })
  const or17393Data = getChildCpBinds(or17393.value.uniqueCpId, {
    or15154: { cpPath: Or15154Const.CP_ID(1) },
    or15109: { cpPath: Or15109Const.CP_ID(1) },
    or15108: { cpPath: Or15108Const.CP_ID(1) },
  })
  const or15169 = or17392Data.or15169.twoWayBind?.value as Or15169ValuesType
  const or15194 = or17392Data.or15194.twoWayBind?.value as Or15194ValuesType
  const or15133 = or17392Data.or15133.twoWayBind?.value as Or15133ValuesType
  const or15183 = or17392Data.or15183.twoWayBind?.value as Or15183ValuesType
  const or15141 = or17392Data.or15141.twoWayBind?.value as Or15141ValuesType
  const or15108 = or17393Data.or15108.twoWayBind?.value as Or15108ValuesType
  const or15109 = or17393Data.or15109.twoWayBind?.value as Or15109ValuesType
  const or15154 = or17393Data.or15154.twoWayBind?.value as Or15154ValuesType
  // バックエンドAPIから初期情報取得
  const inputData: HospitalizationTimeInfoOfferUpdateInEntity = {
    /** 期間ID */
    sc1Id: '',
    /** 法人ID */
    houjinId: '',
    /** 施設ID */
    shisetuId: '',
    /** 事業者ID */
    svJigyoId: '',
    /** 利用者ID */
    userId: '',
    /** 種別ID */
    syubetsuId: '',
    /** 作成日 */
    createYmd: '',
    /** 更新区分 */
    updateKbn: '',
    historyInfo: {},
    saveInfo: {
      ...getValuesData(or15169.or15169Values),
      ...getValuesData(or15194.or15194Values),
      ...getValuesData(or15133.or15133Values),
      ...getValuesData(or15183.or15183Values),
      ...getValuesData(or15141.or15141Values),
      ...getValuesData(or15154.or15154Values),
      ...getValuesData(or15108.or15108Values),
      ...getValuesData(or15109.or15109Values),
    },
  }
  const resData: HospitalizationTimeInfoOfferUpdateOutEntity = await ScreenRepository.update(
    'hospitalizationTimeInfoOfferUpdate',
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === TeX0012Const.DEFAULT.SUCCESS) {
    localOneway.periodSelectOneway.sc1Id = resData.data.sc1Id
    localOneway.orX0008Oneway.createData.createId = resData.data.cc1Id
    // 画面情報再取得
    await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
    // 初期情報を取得
    await reload()
  }
}
// 根据字段名类型转换对象
function getValuesData(
  fieldObj: Record<
    string,
    | string
    | Mo00038Type
    | Mo00040Type
    | Mo00030Type
    | Mo00018Type
    | Mo00020Type
    | Mo00045Type
    | Mo00046Type
    | Mo01408Type
  >
): SaveInfo {
  const obj: SaveInfo = {}
  for (const key in fieldObj) {
    switch (getFieldType(key)) {
      case 'Mo00038Type':
        obj[key] = (fieldObj[key] as Mo00038Type)?.mo00045?.value ?? ''
        break
      case 'Mo00045Type':
        obj[key] = (fieldObj[key] as Mo00045Type)?.value ?? ''
        break
      case 'Mo00020Type':
        obj[key] = (fieldObj[key] as Mo00020Type)?.value ?? ''
        break
      case 'Mo00030Type':
        obj[key] = (fieldObj[key] as Mo00030Type)?.mo00045?.value ?? ''
        break
      case 'Mo00018Type':
        obj[key] = (fieldObj[key] as Mo00018Type)?.modelValue ? '1' : '0'
        break
      case 'Mo00040Type':
        obj[key] = (fieldObj[key] as Mo00040Type)?.modelValue
        break
      case 'Mo00046Type':
        obj[key] = (fieldObj[key] as Mo00046Type)?.value ?? ''
        break
      case 'Mo01408Type':
        obj[key] =
          (fieldObj[key] as Mo00045Type)?.value === ''
            ? '-1'
            : (fieldObj[key] as Mo00045Type)?.value
        break
      default:
        obj[key] = fieldObj[key] === '' ? '0' : (fieldObj[key] as string)
        break
    }
  }
  return obj
}

// 根据字段名获取其类型
function getFieldType(fieldName: string): string {
  return TeX0012Const.typeMap[fieldName]
}

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */
function callbackUserChange(newSelfId: string) {
  if (newSelfId !== '' && userId.value === '') {
    // 利用者変更処理
    void userChange(newSelfId)
    return
  }

  // 変更がない場合、スキップ
  if (newSelfId === userId.value) {
    return
  }

  // 利用者変更処理
  void userChange(newSelfId)
}

/**
 *  画面初期情報再取得
 */
async function reload() {
  // 計画対象期間がない場合、リロードしない
  if (
    plannningPeriodManageFlg.value === TeX0012Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
    (localOneway.periodSelectOneway.sc1Id === undefined ||
      localOneway.periodSelectOneway.sc1Id === '')
  ) {
    return
  }

  // 作成者選択アイコンボタンを活性
  localOneway.orX0009Oneway.isDisabled = false
  // 作成日を活性
  localOneway.createDateOneway.disabled = false
  // 削除区分を設定
  deleteKbn.value = TeX0012Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL

  // 画面共通情報を設定
  setCommonInfo({
    /** 履歴更新回数 */
    historyModifiedCnt: historyModifiedCnt.value,
    /** 選択中タブ */
    activeTabId: local.mo00043.id,
    /** 事業所ID */
    jigyoId: systemCommonsStore.getSvJigyoId,
    /** 利用者ID */
    userId: userId.value,
    /** 計画対象期間ID */
    sc1Id: localOneway.periodSelectOneway.sc1Id,
    /** teikyouIdID */
    teikyouId: localOneway.orX0008Oneway.createData.createId,
    /** 作成者ID */
    createUserId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '',
    /** 作成日 */
    createYmd: local.createDate.value,
  })

  // 画面情報再取得
  await setEvent({ isRefresh: true })
}

/**
 *  画面初期情報取得
 *
 */
async function getInitDataInfo() {
  isLoading.value = true
  useScreenStore().setCpNavControl({
    cpId: TeX0012Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    editFlg: false,
  })
  if (!localOneway.orX0008Oneway.createData.createId) {
    await handleCreate()
  } else {
    try {
      // バックエンドAPIから初期情報取得
      const inputData: HospitalizationTimeInfoOfferInitInfoSelectInEntity = {
        /** 提供ID */
        teikyouId: localOneway.orX0008Oneway.createData.createId,
        /** 利用者ID */
        userid: userId.value,
        /** 基準日 */
        appYmd: systemCommonsStore.getSystemDate ?? '',
      }
      const resData: HospitalizationTimeInfoOfferInitInfoSelectOutEntity =
        await ScreenRepository.select('hospitalizationTimeInfoOfferInitInfoSelect', inputData)
      if (resData?.data) {
        localOneway.tex0012Oneway = resData.data // confirmationInfo
        setInit(resData.data)
      }
    } finally {
      isLoading.value = false
    }
  }
}
/**
 *  画面情報を設定
 *
 * @param data - 設定情報
 */
function setInit(data: HospitalizationTimeInfoOfferInitInfo) {
  // 画面情報を設定
  const kaigoMainZcode =
    data.relationshipMasterInfoList.find(
      (item) => item.zcode === data.confirmationInfo.kaigoMainZcode
    ) ?? ''
  const keyPersonZcode =
    data.relationshipMasterInfoList.find(
      (item) => item.zcode === data.confirmationInfo.keyPersonZcode
    ) ?? ''
  const taiingoKaigoMainZcode =
    data.relationshipMasterInfoList.find(
      (item) => item.zcode === data.confirmationInfo.taiingoKaigoMainZcode
    ) ?? ''
  setChildCpBinds(or17392.value.uniqueCpId, {
    [Or15169Const.CP_ID(1)]: {
      twoWayValue: {
        or15169Values: {
          shienJigyoId: data.confirmationInfo.shienJigyoId,
          tantoShokuId: data.confirmationInfo.tantoShokuId,
          teikyouYmd: {
            value: data.confirmationInfo.teikyouYmd,
            mo01343: {
              value: data.confirmationInfo.teikyouYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          sickYmd: {
            value: data.confirmationInfo.sickYmd,
            mo01343: {
              value: data.confirmationInfo.sickYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          hospKnjHead: {
            value: data.confirmationInfo.hospKnjHead,
          },
          tantoKnj: {
            value: data.confirmationInfo.tantoKnj,
          },
          telHead: { mo00045: { value: data.confirmationInfo.telHead } },
          fax: { mo00045: { value: data.confirmationInfo.fax } },
        },
      },
      oneWayState: {
        codeList: {
          jigyoInfoList: data.jigyoInfoList,
          tantoInfoList: data.tantoInfoList,
        },
      },
    },
    [Or15194Const.CP_ID(1)]: {
      twoWayValue: {
        or15194Values: {
          houseKbn: data.confirmationInfo.houseKbn ?? '0',
          houseFloor: { mo00045: { value: data.confirmationInfo.houseFloor } },
          houseRoomFloor: { mo00045: { value: data.confirmationInfo.houseRoomFloor } },
          elevatorUmu: data.confirmationInfo.elevatorUmu ?? '0',
          houseTokkiKnj: { value: data.confirmationInfo.houseTokkiKnj },
          nyuuinYokaiKbnKakutei: data.confirmationInfo.nyuuinYokaiKbnKakutei ?? '0',
          yokaiKbn: data.confirmationInfo.yokaiKbn ?? '0',
          ninteiStartYmd: {
            value: data.confirmationInfo.ninteiStartYmd,
            mo01343: {
              value: data.confirmationInfo.ninteiStartYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          ninteiEndYmd: {
            value: data.confirmationInfo.ninteiEndYmd,
            mo01343: {
              value: data.confirmationInfo.ninteiEndYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          ninteiShinseiFlg: {
            modelValue: !!Number(data.confirmationInfo.ninteiShinseiFlg),
          },
          ninteiShinseiYmd: {
            value: data.confirmationInfo.ninteiShinseiYmd,
            mo01343: {
              value: data.confirmationInfo.ninteiShinseiYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          ninteiKbnHenkou: {
            modelValue: !!Number(data.confirmationInfo.ninteiKbnHenkou),
          },
          kbnHenkouYmd: {
            value: data.confirmationInfo.kbnHenkouYmd,
            mo01343: {
              value: data.confirmationInfo.kbnHenkouYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          ninteiMishinseiFlg: {
            modelValue: !!Number(data.confirmationInfo.ninteiMishinseiFlg),
          },
          shogaiJiritsuCd: data.confirmationInfo.shogaiJiritsuCd ?? '-1',
          ninchiJiritsuCd: data.confirmationInfo.ninchiJiritsuCd ?? '-1',
          ishiHandanFlg: { modelValue: !!Number(data.confirmationInfo.ishiHandanFlg) },
          careHandanFlg: { modelValue: !!Number(data.confirmationInfo.careHandanFlg) },
          futanWariFlg: data.confirmationInfo.futanWariFlg ?? '0',
          futanWariai: { mo00045: { value: data.confirmationInfo.futanWariai } },
          shogaiNintei: data.confirmationInfo.shogaiNintei ?? '0',
          shintaiShogaiKbn: {
            modelValue: !!Number(data.confirmationInfo.shintaiShogaiKbn),
          },
          chitekiShogaiKbn: {
            modelValue: !!Number(data.confirmationInfo.chitekiShogaiKbn),
          },
          seishinShogaiKbn: {
            modelValue: !!Number(data.confirmationInfo.seishinShogaiKbn),
          },
          nenkin1Umu: { modelValue: !!Number(data.confirmationInfo.nenkin1Umu) },
          nenkin2Umu: { modelValue: !!Number(data.confirmationInfo.nenkin2Umu) },
          nenkin3Umu: { modelValue: !!Number(data.confirmationInfo.nenkin3Umu) },
          nenkin4Umu: { modelValue: !!Number(data.confirmationInfo.nenkin4Umu) },
          nenkin5Umu: { modelValue: !!Number(data.confirmationInfo.nenkin5Umu) },
          nenkinMemoKnj: { value: data.confirmationInfo.nenkinMemoKnj },
        },
      },
      oneWayState: {
        codeList: {
          CONFIRMATION_INFO_LIVING_ENVIRONMENT:
            local.listDataGroup.CONFIRMATION_INFO_LIVING_ENVIRONMENT,
          M_CD_KBN_ID_OMITTED: local.listDataGroup.M_CD_KBN_ID_OMITTED,
          CONFIRMATION_INFO_CARE_LEVEL_AT_ADMISSION_CONFIRMED:
            local.listDataGroup.CONFIRMATION_INFO_CARE_LEVEL_AT_ADMISSION_CONFIRMED,
          SUPPORT_LEVEL_VALUE: local.listDataGroup.SUPPORT_LEVEL_VALUE,
          CARE_LEVEL_VALUE: local.listDataGroup.CARE_LEVEL_VALUE,
          CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DISAB:
            local.listDataGroup.CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DISAB,
          CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DEM:
            local.listDataGroup.CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DEM,
          CONFIRMATION_INFO_OWN_BURDEN_RATIO_BEDSORE:
            local.listDataGroup.CONFIRMATION_INFO_OWN_BURDEN_RATIO_BEDSORE,
        },
      },
    },
    [Or15133Const.CP_ID(1)]: {
      twoWayValue: {
        or15133Values: {
          setaiKousei1: { modelValue: !!Number(data.confirmationInfo.setaiKousei1) },
          setaiKousei2: { modelValue: !!Number(data.confirmationInfo.setaiKousei2) },
          setaiKousei3: { modelValue: !!Number(data.confirmationInfo.setaiKousei3) },
          setaiKousei4: { modelValue: !!Number(data.confirmationInfo.setaiKousei4) },
          setaiKousei5: { modelValue: !!Number(data.confirmationInfo.setaiKousei5) },
          setaiKouseiMemoKnj: { value: data.confirmationInfo.setaiKouseiMemoKnj },
          kaigoMainKakutei: { value: data.confirmationInfo.kaigoMainKakutei },
          kaigoMainZcode: {
            modelValue: kaigoMainZcode ? kaigoMainZcode.zcode : null,
          },
          kaigoMainAge: { mo00045: { value: data.confirmationInfo.kaigoMainAge } },
          kaigoMainKousei: data.confirmationInfo.kaigoMainKousei ?? '0',
          kaigoMainTel: { value: data.confirmationInfo.kaigoMainTel },
          keyPerson: { value: data.confirmationInfo.keyPerson },
          keyPersonZcode: {
            modelValue: keyPersonZcode ? keyPersonZcode.zcode : null,
          },
          keyPersonAge: { mo00045: { value: data.confirmationInfo.keyPersonAge } },
          keyPersonRenrakuTel: {
            mo00045: { value: data.confirmationInfo.keyPersonRenrakuTel },
          },
          keyPersonTel: { value: data.confirmationInfo.keyPersonTel },
        },
      },
      oneWayState: {
        codeList: {
          kinshipInfoList: data.kinshipInfoList,
          relationshipMasterInfoList: data.relationshipMasterInfoList,
          CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION:
            local.listDataGroup.CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION,
        },
      },
    },
    [Or15183Const.CP_ID(1)]: {
      twoWayValue: {
        or15183Values: {
          userInfo: { value: data.confirmationInfo.userInfo },
          userSeikatureki: { value: data.confirmationInfo.userSeikatureki },
          userIkouKnj: { value: data.confirmationInfo.userIkouKnj },
          userIkouCksFlg: {
            modelValue: !!Number(data.confirmationInfo.userIkouCksFlg),
          },
          kazokuIkouKnj: { value: data.confirmationInfo.kazokuIkouKnj },
          kazokuIkouCksFlg: {
            modelValue: !!Number(data.confirmationInfo.kazokuIkouCksFlg),
          },
          serviceRiyoKbn: {
            modelValue: !!Number(data.confirmationInfo.serviceRiyoKbn),
          },
          serviceRiyoKbn2: {
            modelValue: !!Number(data.confirmationInfo.serviceRiyoKbn2),
          },
          serviceRiyoMemoKnj: { value: data.confirmationInfo.serviceRiyoMemoKnj },
        },
      },
      oneWayState: {
        codeList: {},
      },
    },
    [Or15141Const.CP_ID(1)]: {
      twoWayValue: {
        or15141Values: {
          zaitakuYokenKnj: { value: data.confirmationInfo.zaitakuYokenKnj },
          kazokuKaigo1Umu: {
            modelValue: !!Number(data.confirmationInfo.kazokuKaigo1Umu),
          },
          kazokuKaigo3Umu: {
            modelValue: !!Number(data.confirmationInfo.kazokuKaigo3Umu),
          },
          kazokuKaigo2Umu: {
            modelValue: !!Number(data.confirmationInfo.kazokuKaigo2Umu),
          },
          kazokuKaigo7Umu: {
            modelValue: !!Number(data.confirmationInfo.kazokuKaigo7Umu),
          },
          kazokuKaigo6Umu: {
            modelValue: !!Number(data.confirmationInfo.kazokuKaigo6Umu),
          },
          kazokuKaigo7Ninzu: {
            mo00045: { value: data.confirmationInfo.kazokuKaigo7Ninzu },
          },
          kazokuKaigoMemoKnj: { value: data.confirmationInfo.kazokuKaigoMemoKnj },
          setaiHairyo: data.confirmationInfo.setaiHairyo ?? '0',
          setaiHairyoMemoKnj: { value: data.confirmationInfo.setaiHairyoMemoKnj },
          taiingoKaigoMain: data.confirmationInfo.taiingoKaigoMain ?? '0',
          taiingoKaigoMainName: { value: data.confirmationInfo.taiingoKaigoMainName },
          taiingoKaigoMainZcode: {
            modelValue: taiingoKaigoMainZcode ? taiingoKaigoMainZcode.zcode : null,
          },
          taiingoKaigoMainAge: {
            mo00045: { value: data.confirmationInfo.taiingoKaigoMainAge },
          },
          kazokuKaigo8Umu: {
            modelValue: !!Number(data.confirmationInfo.kazokuKaigo8Umu),
          },
          kazokuKaigo9Umu: {
            modelValue: !!Number(data.confirmationInfo.kazokuKaigo9Umu),
          },
          kazokuKaigo8Kbn: data.confirmationInfo.taiingoKaigoMain ?? '0',
          kazokuKaigo4Umu: {
            modelValue: !!Number(data.confirmationInfo.kazokuKaigo4Umu),
          },
          gyakutaiUmu: data.confirmationInfo.gyakutaiUmu ?? '0',
          gyakutaiUmuMemoKnj: { value: data.confirmationInfo.gyakutaiUmuMemoKnj },
          tokkiKnj: { value: data.confirmationInfo.tokkiKnj },
          hospConfSanka: {
            modelValue: !!Number(data.confirmationInfo.hospConfSanka),
          },
          leavConfSanka: {
            modelValue: !!Number(data.confirmationInfo.leavConfSanka),
          },
          leavConfMemoKnj: { value: data.confirmationInfo.leavConfMemoKnj },
          leavHoumonDoukou: {
            modelValue: !!Number(data.confirmationInfo.leavHoumonDoukou),
          },
        },
      },
      oneWayState: {
        codeList: {
          kinshipInfoList: data.kinshipInfoList,
          CONFIRMATION_INFO_HOUSEHOLD: local.listDataGroup.CONFIRMATION_INFO_HOUSEHOLD,
          M_CD_KBN_ID_OMITTED: local.listDataGroup.M_CD_KBN_ID_OMITTED,
          CONFIRMATION_INFO_PRIMARY_CAREGIVER:
            local.listDataGroup.CONFIRMATION_INFO_PRIMARY_CAREGIVER,
          CONFIRMATION_INFO_NURSING_CARE_PROSPECT:
            local.listDataGroup.CONFIRMATION_INFO_NURSING_CARE_PROSPECT,
          relationshipMasterInfoList: data.relationshipMasterInfoList,
        },
      },
    },
  })
  setChildCpBinds(or17393.value.uniqueCpId, {
    [Or15154Const.CP_ID(1)]: {
      twoWayValue: {
        or15154Values: {
          // 麻痺の状況
          mahiKbn: data.confirmationInfo.mahiKbn ?? '0',
          // 褥瘡の有無
          jyokusouUmu: data.confirmationInfo.jyokusouUmu ?? '0',
          /** 褥瘡メモ */
          jyokusouMemoKnj: { value: data.confirmationInfo.jyokusouMemoKnj },

          // ADL移動
          adlIdou1: { modelValue: !!Number(data.confirmationInfo.adlIdou1) },
          adlIdou2: { modelValue: !!Number(data.confirmationInfo.adlIdou2) },
          adlIdou3: { modelValue: !!Number(data.confirmationInfo.adlIdou3) },
          adlIdou4: { modelValue: !!Number(data.confirmationInfo.adlIdou4) },

          // ADL移乗
          adlIjyou1: { modelValue: !!Number(data.confirmationInfo.adlIjyou1) },
          adlIjyou2: { modelValue: !!Number(data.confirmationInfo.adlIjyou2) },
          adlIjyou3: { modelValue: !!Number(data.confirmationInfo.adlIjyou3) },
          adlIjyou4: { modelValue: !!Number(data.confirmationInfo.adlIjyou4) },

          // ADL更衣
          adlKoui1: { modelValue: !!Number(data.confirmationInfo.adlKoui1) },
          adlKoui2: { modelValue: !!Number(data.confirmationInfo.adlKoui2) },
          adlKoui3: { modelValue: !!Number(data.confirmationInfo.adlKoui3) },
          adlKoui4: { modelValue: !!Number(data.confirmationInfo.adlKoui4) },

          // ADL起居動作
          adlKikyo1: { modelValue: !!Number(data.confirmationInfo.adlKikyo1) },
          adlKikyo2: { modelValue: !!Number(data.confirmationInfo.adlKikyo2) },
          adlKikyo3: { modelValue: !!Number(data.confirmationInfo.adlKikyo3) },
          adlKikyo4: { modelValue: !!Number(data.confirmationInfo.adlKikyo4) },

          // ADL整容
          adlSeiyou1: { modelValue: !!Number(data.confirmationInfo.adlSeiyou1) },
          adlSeiyou2: { modelValue: !!Number(data.confirmationInfo.adlSeiyou2) },
          adlSeiyou3: { modelValue: !!Number(data.confirmationInfo.adlSeiyou3) },
          adlSeiyou4: { modelValue: !!Number(data.confirmationInfo.adlSeiyou4) },

          // ADL入浴
          adlNyuuyoku1: { modelValue: !!Number(data.confirmationInfo.adlNyuuyoku1) },
          adlNyuuyoku2: { modelValue: !!Number(data.confirmationInfo.adlNyuuyoku2) },
          adlNyuuyoku3: { modelValue: !!Number(data.confirmationInfo.adlNyuuyoku3) },
          adlNyuuyoku4: { modelValue: !!Number(data.confirmationInfo.adlNyuuyoku4) },

          // ADL食事
          adlShokuji1: { modelValue: !!Number(data.confirmationInfo.adlShokuji1) },
          adlShokuji2: { modelValue: !!Number(data.confirmationInfo.adlShokuji2) },
          adlShokuji3: { modelValue: !!Number(data.confirmationInfo.adlShokuji3) },
          adlShokuji4: { modelValue: !!Number(data.confirmationInfo.adlShokuji4) },

          // 排尿
          hainyou1: { modelValue: !!Number(data.confirmationInfo.hainyou1) },
          hainyou2: { modelValue: !!Number(data.confirmationInfo.hainyou2) },
          hainyou3: { modelValue: !!Number(data.confirmationInfo.hainyou3) },
          hainyou4: { modelValue: !!Number(data.confirmationInfo.hainyou4) },

          // 排便
          haiben1: { modelValue: !!Number(data.confirmationInfo.haiben1) },
          haiben2: { modelValue: !!Number(data.confirmationInfo.haiben2) },
          haiben3: { modelValue: !!Number(data.confirmationInfo.haiben3) },
          haiben4: { modelValue: !!Number(data.confirmationInfo.haiben4) },

          // ADL移動（室内）
          adlIdouShitsunai1: {
            modelValue: !!Number(data.confirmationInfo.adlIdouShitsunai1),
          },
          adlIdouShitsunai2: {
            modelValue: !!Number(data.confirmationInfo.adlIdouShitsunai2),
          },
          adlIdouShitsunai3: {
            modelValue: !!Number(data.confirmationInfo.adlIdouShitsunai3),
          },
          adlIdouShitsunai4: {
            modelValue: !!Number(data.confirmationInfo.adlIdouShitsunai4),
          },

          // ADL移動手段
          adlIdouShudan1: {
            modelValue: !!Number(data.confirmationInfo.adlIdouShudan1),
          },
          adlIdouShudan2: {
            modelValue: !!Number(data.confirmationInfo.adlIdouShudan2),
          },
          adlIdouShudan3: {
            modelValue: !!Number(data.confirmationInfo.adlIdouShudan3),
          },
          adlIdouShudan4: {
            modelValue: !!Number(data.confirmationInfo.adlIdouShudan4),
          },
          /** 食事回数 */
          shokujiCnt: { mo00045: { value: data.confirmationInfo.shokujiCnt } },
          // 食事回数（朝）
          shokujiCntMorn: { value: data.confirmationInfo.shokujiCntMorn },
          // 食事回数（昼）
          shokujiCntNoon: { value: data.confirmationInfo.shokujiCntNoon },
          // 食事回数（夜）
          shokujiCntEven: { value: data.confirmationInfo.shokujiCntEven },
          // 食事制限
          shokujiSeigen: data.confirmationInfo.shokujiSeigen ?? '0',
          // 食事制限メモ
          shokujiSeigenMemoKnj: { value: data.confirmationInfo.shokujiSeigenMemoKnj },
          // 食事形態（普通）
          shokujiKeitai1: {
            modelValue: !!Number(data.confirmationInfo.shokujiKeitai1),
          },
          // 食事形態（きざみ）
          shokujiKeitai2: {
            modelValue: !!Number(data.confirmationInfo.shokujiKeitai2),
          },
          // 食事形態（嚥下障害食）
          shokujiKeitai3: {
            modelValue: !!Number(data.confirmationInfo.shokujiKeitai3),
          },
          // 食事形態（ミキサー）
          shokujiKeitai4: {
            modelValue: !!Number(data.confirmationInfo.shokujiKeitai4),
          },
          // 水分制限
          suibunSeigen: data.confirmationInfo.suibunSeigen ?? '0',
          // 水分制限メモ
          suibunSeigenMemoKnj: { value: data.confirmationInfo.suibunSeigenMemoKnj },
          // 摂取方法（経口）
          sesshuHouhou1: {
            modelValue: !!Number(data.confirmationInfo.sesshuHouhou1),
          },
          // 摂取方法（経管栄養）
          sesshuHouhou2: {
            modelValue: !!Number(data.confirmationInfo.sesshuHouhou2),
          },
          // 水分とろみ
          suibunToromi: data.confirmationInfo.suibunToromi,
          // UDF等の食形態区分
          udfShokujiKeitaiKbn: { value: data.confirmationInfo.udfShokujiKeitaiKbn },
          // 嚥下機能
          engeUmu: data.confirmationInfo.engeUmu ?? '0',
          // 義歯
          gishiUmu: data.confirmationInfo.gishiUmu ?? '0',
          /** 義歯区分 */
          gishiKbn: data.confirmationInfo.gishiKbn ?? '0',
          // 口腔清潔
          koukuuCare: data.confirmationInfo.koukuuCare ?? '0',
          // 口臭
          koushuuUmu: data.confirmationInfo.koushuuUmu ?? '0',
          // ポータブルトイレ
          portableToiletUmu: data.confirmationInfo.portableToiletUmu ?? '0',
          // オムツ/パッド
          omutsuPadUmu: data.confirmationInfo.omutsuPadUmu ?? '0',
          // 睡眠の状態
          sleepJyoutai: data.confirmationInfo.sleepJyoutai ?? '0',
          /** 眠剤の使用 */
          sleepDrugUmu: data.confirmationInfo.sleepDrugUmu ?? '0',
          // 睡眠の状態メモ
          sleepMemoKnj: { value: data.confirmationInfo.sleepMemoKnj },
          // 視力
          eyesightKbn: data.confirmationInfo.eyesightKbn,
          // 喫煙量
          smoking: { mo00045: { value: data.confirmationInfo.smoking } },
          // 飲酒量
          alcohol: { mo00045: { value: data.confirmationInfo.alcohol } },
          // 喫煙有無
          smokingUmu: data.confirmationInfo.smokingUmu ?? '0',
          // 飲酒有無
          alcoholUmu: data.confirmationInfo.alcoholUmu ?? '0',
          // メガネ
          glassesUmu: data.confirmationInfo.glassesUmu ?? '0',
          // メガネメモ
          glassesMemoKnj: { value: data.confirmationInfo.glassesMemoKnj },
          // 聴力
          hearingKbn: data.confirmationInfo.hearingKbn,
          // 補聴器
          hearingAidUmu: data.confirmationInfo.hearingAidUmu ?? '0',
          // 言語
          languageAbility: data.confirmationInfo.languageAbility,
          // 意思疎通
          comnKbn: data.confirmationInfo.comnKbn,
          // コミュニケーションに関する特記事項
          comnTokkiKnj: { value: data.confirmationInfo.comnTokkiKnj },
          // 精神面における療養上の問題（なし）
          ryouyou1Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou1Umu),
          },
          // 精神面における療養上の問題（幻視幻聴）
          ryouyou2Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou2Umu),
          },
          // 精神面における療養上の問題（興奮）
          ryouyou3Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou3Umu),
          },
          // 精神面における療養上の問題（焦燥不穏）
          ryouyou4Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou4Umu),
          },
          // 精神面における療養上の問題（妄想）
          ryouyou5Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou5Umu),
          },
          // 精神面における療養上の問題（暴力/攻撃性）
          ryouyou6Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou6Umu),
          },
          // 精神面における療養上の問題（介護への抵抗）
          ryouyou7Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou7Umu),
          },
          // 精神面における療養上の問題（不眠）
          ryouyou8Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou8Umu),
          },
          // 精神面における療養上の問題（昼夜逆転）
          ryouyou9Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou9Umu),
          },
          // 精神面における療養上の問題（徘徊）
          ryouyou10Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou10Umu),
          },
          // 精神面における療養上の問題（危険行為）
          ryouyou11Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou11Umu),
          },
          // 精神面における療養上の問題（不潔行為）
          ryouyou12Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou12Umu),
          },
          // 精神面における療養上の問題（その他）
          ryouyou13Umu: {
            modelValue: !!Number(data.confirmationInfo.ryouyou13Umu),
          },
          // 精神面における療養上の問題（その他メモ）
          ryouyouMemoKnj: { value: data.confirmationInfo.ryouyouMemoKnj },
          // 疾患歴（なし）
          sick1Umu: {
            modelValue: !!Number(data.confirmationInfo.sick1Umu),
          },
          // 疾患歴（悪性腫瘍）
          sick2Umu: {
            modelValue: !!Number(data.confirmationInfo.sick2Umu),
          },
          // 疾患歴（認知症）
          sick3Umu: {
            modelValue: !!Number(data.confirmationInfo.sick3Umu),
          },
          // 疾患歴（急性呼吸器感染症）
          sick4Umu: {
            modelValue: !!Number(data.confirmationInfo.sick4Umu),
          },
          // 疾患歴（脳血管障害）
          sick5Umu: {
            modelValue: !!Number(data.confirmationInfo.sick5Umu),
          },
          // 疾患歴（骨折）
          sick6Umu: {
            modelValue: !!Number(data.confirmationInfo.sick6Umu),
          },
          // 疾患歴（その他）
          sick7Umu: {
            modelValue: !!Number(data.confirmationInfo.sick7Umu),
          },
          // 疾患歴（その他）メモ
          sickMemoKnj: { value: data.confirmationInfo.sickMemoKnj },
          // 最近半年間での入院
          nyuuinUmu: data.confirmationInfo.nyuuinUmu ?? '0',
          // 入院理由
          nyuuinRiyu: { value: data.confirmationInfo.nyuuinRiyu },
          // 最近半年間での入院開始日
          nyuuinStartYmd: {
            value: data.confirmationInfo.nyuuinStartYmd,
            mo01343: {
              value: data.confirmationInfo.nyuuinStartYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          // 最近半年間での入院終了日
          nyuuinEndYmd: {
            value: data.confirmationInfo.nyuuinEndYmd,
            mo01343: {
              value: data.confirmationInfo.nyuuinEndYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          // 入院頻度
          nyuuinHindo: data.confirmationInfo.nyuuinHindo,
          // 医療処置（なし）
          shochi1Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi1Umu),
          },
          // 医療処置（点滴）
          shochi2Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi2Umu),
          },
          // 医療処置（酸素療法）
          shochi3Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi3Umu),
          },
          // 医療処置（喀痰吸引）
          shochi4Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi4Umu),
          },
          // 医療処置（気管切開）
          shochi5Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi5Umu),
          },
          // 医療処置（胃ろう）
          shochi6Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi6Umu),
          },
          // 医療処置（経鼻栄養）
          shochi7Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi7Umu),
          },
          // 医療処置（経腸栄養）
          shochi8Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi8Umu),
          },
          // 医療処置（褥瘡）
          shochi9Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi9Umu),
          },
          // 医療処置（尿道カテーテル）
          shochi10Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi10Umu),
          },
          // 医療処置（尿路ストーマ）
          shochi11Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi11Umu),
          },
          // 医療処置（消化管ストーマ）
          shochi12Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi12Umu),
          },
          // 医療処置（痛みコントロール）
          shochi13Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi13Umu),
          },
          // 医療処置（排便コントロール）
          shochi14Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi14Umu),
          },
          // 医療処置（自己注射）
          shochi15Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi15Umu),
          },
          // 医療処置（自己注射）メモ
          shochi15MemoKnj: {
            value: data.confirmationInfo.shochi15MemoKnj,
          },
          // 医療処置（その他）
          shochi16Umu: {
            modelValue: !!Number(data.confirmationInfo.shochi16Umu),
          },
          // 医療処置（その他）メモ
          shochi16MemoKnj: {
            value: data.confirmationInfo.shochi16MemoKnj,
          },
        },
      },
      oneWayState: {
        codeList: {
          CONFIRMATION_INFO_PARALYSIS_STATUS:
            local.listDataGroup.CONFIRMATION_INFO_PARALYSIS_STATUS,
          MEAL_FREQUENCY: local.listDataGroup.MEAL_FREQUENCY,
          MEAL_WATER_RESTRICTION: local.listDataGroup.MEAL_WATER_RESTRICTION,
          CONFIRMATION_INFO_SWALLOWING_FUNCTION:
            local.listDataGroup.CONFIRMATION_INFO_SWALLOWING_FUNCTION,
          CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY:
            local.listDataGroup.CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY,
          CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS:
            local.listDataGroup.CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS,
          CONFIRMATION_INFO_PORTABLE_TOILETS:
            local.listDataGroup.CONFIRMATION_INFO_PORTABLE_TOILETS,
          SLEEPING_STATE: local.listDataGroup.SLEEPING_STATE,
          CONFIRMATION_INFO_CODE: local.listDataGroup.CONFIRMATION_INFO_CODE,
          HOSPITALIZATION_FREQUENCY: local.listDataGroup.HOSPITALIZATION_FREQUENCY,
          M_CD_KBN_ID_OMITTED: local.listDataGroup.M_CD_KBN_ID_OMITTED.map((item) => {
            return {
              ...item,
              value: item.value.toString(),
            }
          }),
        },
      },
    },
    [Or15108Const.CP_ID(1)]: {
      twoWayValue: {
        or15108Values: {
          /** 内服薬 */
          drugUmu: data.confirmationInfo.drugUmu ?? '0',
          /** 内服薬メモ */
          drugMemoKnj: { value: data.confirmationInfo.drugMemoKnj },
          /** 居宅療養管理指導 */
          ryouyouKanriUmu: data.confirmationInfo.ryouyouKanriUmu ?? '0',
          /** 居宅療養管理指導メモ */
          ryouyouKanriMemoKnj: { value: data.confirmationInfo.ryouyouKanriMemoKnj },
          /** 薬剤管理 */
          drugKanriKbn: data.confirmationInfo.drugKanriKbn,
          /** 薬剤管理（管理者） */
          drugKanriKanrisya: { value: data.confirmationInfo.drugKanriKanrisya },
          /** 薬剤管理（管理方法） */
          drugKanriHouhou: { value: data.confirmationInfo.drugKanriHouhou },
          /** 服薬状況 */
          drugJyoukyou: data.confirmationInfo.drugJyoukyou,
          /** お薬に関する、特記事項 */
          drugTokkiKnj: { value: data.confirmationInfo.drugTokkiKnj },
        },
      },
      oneWayState: {
        codeList: {
          TAKING_MEDICALTION_SITUATION: local.listDataGroup.TAKING_MEDICALTION_SITUATION,
          MEDICINE_MANAGEMENT: local.listDataGroup.MEDICINE_MANAGEMENT,
          M_CD_KBN_ID_OMITTED: local.listDataGroup.M_CD_KBN_ID_OMITTED.map((item) => {
            return {
              ...item,
              value: item.value.toString(),
            }
          }),
        },
      },
    },
    [Or15109Const.CP_ID(1)]: {
      twoWayValue: {
        or15109Values: {
          /** 医療機関名 */
          hospKnj: { value: data.confirmationInfo.hospKnj },
          /** 電話番号 */
          hospTel: { mo00045: { value: data.confirmationInfo.hospTel } },
          /** 医師名フリガナ */
          doctorKana: { mo00045: { value: data.confirmationInfo.doctorKana } },
          /** 医師名 */
          doctorKnj: { value: data.confirmationInfo.doctorKnj },
          /** 診察方法 */
          hospHouhou: data.confirmationInfo.hospHouhou,
          /** 診察頻度（回数） */
          hospKaisu: { mo00045: { value: data.confirmationInfo.hospKaisu } },
        },
      },
      oneWayState: {
        codeList: {
          MEDICAL_EXAMINATION_METHOD: local.listDataGroup.MEDICAL_EXAMINATION_METHOD,
        },
      },
    },
  })
}

/**
 *  画面共通情報を設定
 *
 * @param data - 設定情報
 */
function setCommonInfo(data: TeX0012Type) {
  const existedData = TeX0012Logic.data.get(props.uniqueCpId)
  const newData = {
    /** 履歴更新回数 */
    historyModifiedCnt: historyModifiedCnt.value,
    /** 選択中タブ */
    activeTabId: data.activeTabId ?? existedData?.activeTabId,
    /** 事業所ID */
    jigyoId: data.jigyoId ?? existedData?.jigyoId,
    /** 計画期間ID */
    sc1Id: data.sc1Id ?? existedData?.sc1Id,
    /** アセスメントID */
    teikyouId: data.teikyouId ?? existedData?.teikyouId,
    /** 作成者ID */
    createUserId: data.createUserId ?? existedData?.createUserId,
    /** 履歴作成日 */
    createYmd: data.createYmd ?? existedData?.createYmd,
    /** 複写データ */
    copyData: data.copyData ?? existedData?.copyData,
    /** 削除区分 */
    deleteKbn: deleteKbn.value ?? existedData?.deleteKbn,
    /** 介護保険権限フラグ */
    nursingCareInsuranceAuthorityFlag:
      data.nursingCareInsuranceAuthorityFlag ?? existedData?.nursingCareInsuranceAuthorityFlag,
    /** 親族関係者権限フラグ */
    kinshipAuthorityFlag: data.kinshipAuthorityFlag ?? existedData?.kinshipAuthorityFlag,
    /** 既往歴権限フラグ */
    pastMedicalHistoryAuthorityFlag:
      data.pastMedicalHistoryAuthorityFlag ?? existedData?.pastMedicalHistoryAuthorityFlag,
  } as TeX0012Type

  TeX0012Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      /** 履歴更新回数 */
      historyModifiedCnt: newData.historyModifiedCnt,
      /** 選択中タブ */
      activeTabId: newData.activeTabId,
      /** 事業所ID */
      jigyoId: newData.jigyoId,
      /** 計画期間ID */
      sc1Id: newData.sc1Id,
      /** アセスメントID */
      teikyouId: newData.teikyouId,
      /** 作成者ID */
      createUserId: newData.createUserId,
      /** 履歴作成日 */
      createYmd: newData.createYmd,
      /** 複写データ */
      copyData: newData.copyData,
      /** 削除区分 */
      deleteKbn: newData.deleteKbn,
      /** 介護保険権限フラグ */
      nursingCareInsuranceAuthorityFlag: newData.nursingCareInsuranceAuthorityFlag,
      /** 親族関係者権限フラグ */
      kinshipAuthorityFlag: newData.kinshipAuthorityFlag,
      /** 既往歴権限フラグ */
      pastMedicalHistoryAuthorityFlag: newData.pastMedicalHistoryAuthorityFlag,
    },
  })
}

/**
 *  計画対象期間チェンジ設定
 *
 * @param sc1Id - 計画対象期間ID
 *
 * @param pageKbn - 期間処理区分
 */
async function getPlanningPeriodInfo(sc1Id: string, pageKbn: string) {
  try {
    // バックエンドAPIから初期情報取得
    const inputData: HospitalizationTimeInfoOfferPeriodSelectInEntity = {
      menu2Name: TeX0012Const.DEFAULT.MENU2_NAME,
      menu3Name: TeX0012Const.DEFAULT.MENU3_NAME,
      /** 事業所ID */
      svJigyoId: jigyoId.value,
      /** システムコード */
      gsysCd: systemCommonsStore.getSystemCode ?? '1',
      /** 利用者ID */
      userid: userId.value,
      /** 施設ID */
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      /** 職員ID  */
      shokuId: systemCommonsStore.getStaffId ?? '1',
      /** 期間ID  */
      sc1Id: sc1Id ? sc1Id : '1',
      /** 計画期間ページ区分  */
      pageKbn: pageKbn ?? '0',
    }
    const resData: HospitalizationTimeInfoOfferPeriodSelectOutEntity =
      await ScreenRepository.select('hospitalizationTimeInfoOfferPeriodSelect', inputData)
    /** =============画面情報を設定============= */
    if (resData?.data) {
      local.periodInfo = {
        ...resData.data,
        /** 計画期間情報 */
        planPeriodInfo: undefined,
        /** 履歴情報 */
        historyInfo: undefined,
      }
      // 画面共通情報を設定
      setCommonInfo({
        // 介護保険権限
        nursingCareInsuranceAuthorityFlag: resData.data.nursingCareInsuranceAuthorityFlag,
        // 親族関係者権限フラグ
        kinshipAuthorityFlag: resData.data.kinshipAuthorityFlag,
        // 既往歴権限フラグ
        pastMedicalHistoryAuthorityFlag: resData.data.pastMedicalHistoryAuthorityFlag,
      })
      /** ------------計画対象期間を設定------------ */
      // 計画期間管理フラグ設定
      plannningPeriodManageFlg.value = resData.data.kikanKanriFlg

      // 計画対象期間管理フラグが「1:管理する」場合、計画対象期間情報を設定
      if (
        plannningPeriodManageFlg.value === TeX0012Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
        resData?.data?.planPeriodInfo?.sc1Id &&
        resData.data.planPeriodInfo.periodCnt !== '0'
      ) {
        localOneway.periodSelectOneway.sc1Id = resData.data.planPeriodInfo.sc1Id
        localOneway.orX0007Oneway.planTargetPeriodData = {
          planTargetPeriodId: Number(resData.data.planPeriodInfo.sc1Id),
          planTargetPeriod:
            resData.data.planPeriodInfo.startYmd + '～' + resData.data.planPeriodInfo.endYmd,
          currentIndex: Number(resData.data.planPeriodInfo.periodNo),
          totalCount: Number(resData.data.planPeriodInfo.periodCnt),
        }
        isHistoryShow.value = true
      } else if (
        plannningPeriodManageFlg.value === TeX0012Const.DEFAULT.PLANNING_PERIOD_NO_MANAGE &&
        resData?.data?.planPeriodInfo?.sc1Id &&
        resData.data.planPeriodInfo.periodCnt !== '0'
      ) {
        localOneway.periodSelectOneway.sc1Id = resData.data.planPeriodInfo.sc1Id ?? ''
        localOneway.orX0007Oneway.planTargetPeriodData = {
          planTargetPeriodId: 0,
          planTargetPeriod: '',
          currentIndex: 0,
          totalCount: 0,
        }
        isHistoryShow.value = true
      } else {
        localOneway.periodSelectOneway.sc1Id = resData.data.planPeriodInfo.sc1Id ?? ''
        localOneway.orX0007Oneway.planTargetPeriodData = {
          planTargetPeriodId: Number(resData.data.planPeriodInfo.sc1Id ?? 0),
          planTargetPeriod: '',
          currentIndex: 0,
          totalCount: 0,
        }
        isHistoryShow.value = false
      }
      /** ------------履歴情報を設定------------ */
      // 表示中計画対象期間の履歴情報取得
      if (resData?.data?.historyInfo.teikyouId) {
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: resData.data.historyInfo.teikyouId,
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: Number(resData.data.historyInfo.krirekiNo ?? 1),
            /** 履歴総件数 */
            totalCount: Number(resData.data.historyInfo.krirekiCnt ?? 1),
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: TeX0012Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }
        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: parseInt(resData.data.historyInfo.chkShokuId ?? '0'),
          staffName: resData.data.historyInfo.chkShokuName ?? '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value = resData.data.historyInfo.createYmd ?? ''
        createDateOld.value = local.createDate.value
      } else {
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: resData.data.historyInfo.teikyouId ?? '',
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: 1,
            /** 履歴総件数 */
            totalCount: 1,
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: TeX0012Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }
        // 履歴更新回数
        historyModifiedCnt.value = ''

        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: 0,
          staffName: '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value =
          systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
        createDateOld.value = local.createDate.value
      }
    } else {
      localOneway.orX0007Oneway.planTargetPeriodData = {
        planTargetPeriodId: 0,
        planTargetPeriod: '',
        currentIndex: 0,
        totalCount: 0,
      }
      isHistoryShow.value = false
    }
  } catch (e: unknown) {
    console.log(e)
  }
}

/**
 *  履歴情報を取得
 *
 * @param teikyouId - 履歴ID
 *
 * @param kikanFlag - 履歴処理区分
 */
async function getHistoryInfo(teikyouId: string, kikanFlag: string) {
  try {
    // バックエンドAPIから初期情報取得
    const inputData: HospitalizationTimeInfoOfferHistorySelectInEntity = {
      /** 事業所ID */
      svJigyoId: jigyoId.value,
      /** 利用者ID */
      userid: userId.value,
      /** 提供ID  */
      cc1Id: teikyouId ? teikyouId : '',
      /** 計画期間ID */
      sc1Id: localOneway.periodSelectOneway.sc1Id ?? '',
      /** 履歴変更区分 */
      kikanFlag: kikanFlag ?? '0',
      // 削除処理区分
      deleteKbn: '',
      // 当履歴ページ番号
      rrkNo: '',
    }
    const resData: HospitalizationTimeInfoOfferHistorySelectOutEntity =
      await ScreenRepository.select('hospitalizationTimeInfoOfferHistorySelect', inputData)

    // 画面情報を設定
    if (resData?.data) {
      if (resData?.data?.historyInfo?.teikyouId) {
        // 履歴IDをバックアップ
        teikyouIdOld.value = resData?.data?.historyInfo.teikyouId

        // 表示中計画対象期間の履歴情報取得
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: resData.data.historyInfo.teikyouId,
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: Number(resData.data.historyInfo.krirekiNo ?? 1),
            /** 履歴総件数 */
            totalCount: Number(resData.data.historyInfo.krirekiCnt ?? 1),
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: TeX0012Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }

        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: parseInt(resData.data.historyInfo.chkShokuId ?? '0'),
          staffName: resData.data.historyInfo.chkShokuName ?? '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value = resData.data.historyInfo.createYmd ?? ''
        createDateOld.value = local.createDate.value
      } else {
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: '',
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: Number(resData.data.historyInfo.krirekiNo ?? 1),
            /** 履歴総件数 */
            totalCount: Number(resData.data.historyInfo.krirekiCnt ?? 1),
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: TeX0012Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }

        // 履歴更新回数
        historyModifiedCnt.value = ''

        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: 0,
          staffName: '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value =
          systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
        createDateOld.value = local.createDate.value
      }
    }
  } catch (e: unknown) {
    console.log(e)
  }
}

/**
 *  データ変更チェック
 */
async function checkChange(): Promise<number> {
  let result = 0
  // 画面変更チェック
  if (isEdit.value) {
    if (!isPermissionViewAuth.value) {
      // 画面入力変更あり、かつ保存権限がない場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog2(t('message.w-com-10006'))
      switch (dialogResult) {
        case 'yes': {
          // 編集を破棄して処理を続く
          result = 1
        }
      }
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case 'yes': {
          // 編集を保存して処理を続く
          result = 2
          break
        }
        case 'no':
          // 編集を破棄して処理を続く
          result = 1
          break
        case 'cancel':
          // キャンセル選択時は何もしない
          result = 3
          break
      }
    }
  } else {
    // 変更なしの場合は何もしないまま処理を続く
    result = 0
  }
  return result
}

/**
 * 利用者変更処理
 *
 * @param newUserId - 新しい利用者ID
 */
async function userChange(newUserId: string) {
  isLoading.value = true

  // 初期化の場合、チェックなしで処理を続行
  if (isInitCompleted === false) {
    userId.value = newUserId
    // 初期情報を取得
    await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
    // 画面情報再取得
    void reload()

    isInitCompleted = true
    isLoading.value = false
    return
  }

  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()
      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存して処理を続行
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 保存
      await setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }

  isLoading.value = false
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (jigyoId.value === '') {
    jigyoId.value = newJigyoId
    return
  }

  // 事業所変更がない場合、スキップ
  if (newJigyoId === jigyoId.value) {
    return
  }

  // まず変更前の事業所を保持
  void setJigyo(jigyoId.value)

  // 事業所変更処理
  void jigyoChange(newJigyoId)
}

/**
 * 事業所を選択
 *
 * @param jigyoId - 事業所ID
 */
async function setJigyo(jigyoId: string) {
  await nextTick()

  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    },
  })
}

/**
 * 事業所変更処理
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function jigyoChange(newJigyoId: string) {
  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      jigyoId.value = newJigyoId
      // 変更後の事業所に設定
      await setJigyo(newJigyoId)
      // 画面情報再取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      jigyoId.value = newJigyoId
      // 変更後の事業所に設定
      await setJigyo(newJigyoId)

      // 画面情報再取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()

      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存して処理を続行
      jigyoId.value = newJigyoId
      // 変更後の事業所に設定
      await setJigyo(newJigyoId)

      // 画面情報再取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // はい選択時は入力内容を保存する
      await setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog1(paramDialogText: string): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog2(paramDialogText: string) {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise(() => {
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      () => {
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_2.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
async function openConfirmDialog3(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_3.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_3.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * メッセージを表示する
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
async function openWarnDialog(state: Or21815StateType): Promise<Or21815EventType | undefined> {
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      ...state,

      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 *
 * @returns - 確認ダイアログを閉じたタイミングで結果を返却
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,

      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}
/**
 * 複写ボタンクリック
 *
 */
function copyBtnClick() {
  // 保存ボタンが押下された場合、保存処理を実行する
  setOr11871Event({ copyEventFlg: true })
}

/**
 * 新規処理
 *
 */
async function createNew() {
  if (plannningPeriodManageFlg.value === TeX0012Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
    if (localOneway.periodSelectOneway.sc1Id) {
      if (localOneway.orX0008Oneway.createData.createId) {
        // 画面変更チェック
        if (isEdit.value) {
          // 画面入力変更がある場合
          // 確認ダイアログ表示
          const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
          switch (dialogResult) {
            case 'yes': {
              // はい選択時は入力内容を保存する
              await setEvent({ saveEventFlg: true })

              // 履歴件数 + 1
              void historyNew()
              break
            }
            case 'no': {
              // いいえ選択時は編集内容を破棄するので何もしない
              // 履歴件数 + 1
              void historyNew()
              break
            }
            case 'cancel':
              // キャンセル
              break
          }

          return
        } else {
          // 履歴件数 + 1
          void historyNew()
        }
      } else {
        // 計画期間情報がある、当該画面データが保存されない場合
        // 確認ダイアログ表示
        await openConfirmDialog2(t('message.i-cmn-11265', [t('label.hospitalization-info')]))

        return
      }
    } else {
      // 計画期間情報がない場合
      // 確認ダイアログ表示
      await openConfirmDialog2(t('message.i-cmn-11300'))

      return
    }
  }
}

/**
 * 印刷処理
 *
 */
function print() {
  // Or56886のダイアログ開閉状態を更新する
  Or56886Logic.state.set({
    uniqueCpId: or56886.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        prtNo: '1',
        houjinId: '1',
        shokuId: '1',
        svJigyoId: '1',
        shisetuId: '1',
        tantoId: '1',
        tantoFlg: '0',
        sectionName: 'インターライ方式ケアアセスメント表',
        userId: '41',
        historyId: '3',
        svJigyoKnj: '1',
        processYmd: '2025/07/02',
        focusSettingInitial: ['や', 'ゆ', 'よ'],
        selectedUserCounter: '2',
      } as Or56886Param,
    },
  })
}

/**
 * 複写処理
 *
 */
function _copy() {
  // 複写画面パラメータを設定
  or17391.value.or00249UniqueCpId = or00249.value.uniqueCpId
  or17391.value.or00094UniqueCpId = or00094.value.uniqueCpId
  localOneway.or17391Oneway.periodManagementFlg = plannningPeriodManageFlg.value
  copyComponentKey.value = Date.now().toString()
  const tabItems = [] as {
    /** id */
    id: string
    /** id */
    name: string
  }[]
  localOneway.mo00043OnewayType.tabItems.forEach((item) => {
    tabItems.push({
      id: item.id,
      name: item.title,
    })
  })
  localOneway.or17391Oneway = {
    /** 期間管理フラグ */
    periodManagementFlg: plannningPeriodManageFlg.value,
    /** タブリスト */
    tabItems: tabItems,
    /** 選択中タブ */
    activeTabId: local.mo00043.id,
    /** 事業所IDリスト*/
    svJigyoIdList: Array.from(systemCommonsStore.getSvJigyoIdList) ?? [],
    /** 種別ID */
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    /** 利用者ID */
    userId: userId.value,
    /** 基準日 */
    createYmd: systemCommonsStore.getSystemDate ?? '',
    /** 施設ID */
    shisetuId: systemCommonsStore.getShisetuId ?? '',
  }
  // 複写画面を開く
  Or17391Logic.state.set({
    uniqueCpId: or17391.value.uniqueCpId,
    state: { isOpen: true },
  })
  setOr11871Event({ copyEventFlg: false })
}

/**
 * 履歴を更新
 *
 */
async function historyNew() {
  // 履歴件数 + 1
  let rirekiCnt: number = localOneway.orX0008Oneway.createData?.totalCount
    ? (localOneway.orX0008Oneway.createData?.totalCount ?? 0)
    : 0
  rirekiCnt = rirekiCnt + 1
  localOneway.orX0008Oneway.createData = {
    /** 提供ID */
    createId: '',
    /** 作成日 */
    createDate: '',
    /** 職員ID */
    staffId: '',
    /** 作成者名（職員名） */
    staffName: '',
    /** 履歴番号 */
    currentIndex: rirekiCnt,
    /** 履歴総件数 */
    totalCount: rirekiCnt,
  }
  await setEvent({ createEventFlg: true })
}

/**
 * 削除処理
 *
 */
function _delete() {
  // メッセージ 11326
  // 現在表示している[{0}］の{1}データを削除します。よろしいですか？\r\n現在表示している画面のみ削除する。\r\n※{2}画面を削除します。\r\n表示している画面を履歴ごと削除する。\r\n※{3}までの全ての項目を削除します。
  // 削除確認ダイアログを初期化(i.cmn.11326)
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11326', [
        local.createDate.value,
        t('label.hospitalization-info'),
      ]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen,
      async () => {
        const event = Or21814Logic.event.get(or21814_3.value.uniqueCpId)

        let result = TeX0012Const.DEFAULT.DIALOG_RESULT_CANCEL
        if (event?.firstBtnClickFlg) {
          result = TeX0012Const.DEFAULT.DIALOG_RESULT_YES
          // 削除実施後、作成者選択アイコンボタンを非活性
          localOneway.orX0009Oneway.isDisabled = true
          // 削除実施後、作成日を非活性
          localOneway.createDateOneway.disabled = true
          //共通処理の复写権限チェックを行う
          local.copyDisabled = true
          Or11871Logic.state.set({
            uniqueCpId: or11871.value.uniqueCpId,
            state: {
              disabledCreateBtn: true, //共通処理の新规権限チェックを行う
              disabledPrintBtn: true, //共通処理の印刷権限チェックを行う
              disabledOptionMenuDelete: true, //共通処理の削除権限チェックを行う
            },
          })
          await setEvent({ deleteEventFlg: true })
        }
        if (event?.secondBtnClickFlg) {
          result = TeX0012Const.DEFAULT.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_3.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 作成日変更処理
 *
 * @param mo00020 - 作成日
 */
async function createDateChange(mo00020: Mo00020Type) {
  if (mo00020.value === createDateOld.value) {
    // 作成日が変更されていない場合は何もしない
    return
  }

  if (createDateOld.value === '' || mo00020.value === '') {
    // 履歴に作成日がない、または入力されない場合は何もしない
    return
  }

  // H21/4改訂版（改訂フラグ＝4）AND  作成日が2018年3月31日以前（平成30年区分=0）AND 入力した作成日>= '2018/04/01'の場合
  if (
    parseInt(createDateOld.value.replaceAll('/', '')) < 20180331 &&
    parseInt(mo00020.value.replaceAll('/', '')) >= 20180401
  ) {
    // 確認ダイアログ表示
    const dialogResult = await openConfirmDialog3(t('message.i-cmn-11324'))
    switch (dialogResult) {
      case 'yes': {
        createDateOld.value = mo00020.value
        // はい選択時は入力内容を保存する
        if (isEdit.value) {
          await setEvent({ saveEventFlg: true })
        }

        // 作成日変更処理
        await setEvent({ isCreateDateChanged: true })
        break
      }
      case 'no': {
        // いいえ選択時入力前の値に戻る
        mo00020.value = createDateOld.value
        break
      }
    }
  }

  // H21/４改訂版（改訂フラグ＝4）AND  作成日が2018年4月1日以降（平成30年区分=1）AND 入力した作成日< '2018/04/01'の場合
  if (
    parseInt(createDateOld.value.replaceAll('/', '')) >= 20180401 &&
    parseInt(mo00020.value.replaceAll('/', '')) < 20180401
  ) {
    // 確認ダイアログ表示
    const dialogResult = await openConfirmDialog3(t('message.i-cmn-11324'))
    switch (dialogResult) {
      case 'yes': {
        createDateOld.value = mo00020.value
        // はい選択時は入力内容を保存する
        if (isEdit.value) {
          await setEvent({ saveEventFlg: true })
        }

        // 作成日変更処理
        await setEvent({ isCreateDateChanged: true })
        break
      }
      case 'no': {
        // いいえ選択時入力前の値に戻る
        mo00020.value = createDateOld.value
        break
      }
    }
  }
}

/**
 * 保存時処理(checkEdit含めて保存処理)
 * False: 処理中止（取消、いいえ）と保存権限がない時の取消操作、
 * True：処理継続（保存と保存しない）と保存権限がない時の処理継続
 */
async function checkEditBySave(): Promise<boolean> {
  //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
  if (
    !(await gyoumuCom.checkEdit(
      isEdit.value,
      await hasRegistAuth(TeX0012Const.DEFAULT.LINK_AUTH),
      showConfirmMessageBox,
      showWarnMessageBox,
      save
    ))
  ) {
    return false
  }
  return true
}

/**
 * メッセージを表示する
 *
 * @returns 返回値
 */
const showConfirmMessageBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * メッセージを表示する
 */
const showWarnMessageBox = async () => {
  return await openWarnDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.w-com-10006'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
}
/**
 * タブ変更処理
 *
 * @param mo00043 - タブ情報
 */
function tabChange(mo00043: Mo00043Type) {
  // 画面変更チェック
  local.mo00043.id = mo00043.id
}

/**
 * 削除ダイアログ選択変更処理
 *
 * @param orX0001Value - 戻り値
 */
function deleteDialogChange(orX0001Value: OrX0001Type) {
  if ('0' !== orX0001Value.deleteSyubetsu) {
    // 確定ボタン押下の場合
    deleteKbn.value = orX0001Value.deleteSyubetsu
    // 削除実施後、作成者選択アイコンボタンを非活性
    localOneway.orX0009Oneway.isDisabled = true
    // 削除実施後、作成日を非活性
    localOneway.createDateOneway.disabled = true

    // アセスメント(インターライ)画面履歴の最新情報を取得する
    switch (local.mo00043.id) {
      case Or17392Const.DEFAULT.TAB_ID:
        break
      case Or17393Const.DEFAULT.TAB_ID:
        break
    }
  } else {
    // キャンセル
    // 何もしない
  }
}

watch(
  () => isEdit.value,
  () => {
    console.log(isEdit.value, '11111111111111111111')
  }
)

/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    console.log(newValue, 'newValue計画期間変更の監視')
    // 計画期間変更区分
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg
    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }
    const { currentIndex, totalCount } = localOneway.orX0007Oneway.planTargetPeriodData
    const sc1Id =
      newValue.planTargetPeriodId !== '0'
        ? newValue.planTargetPeriodId
        : (localOneway.periodSelectOneway.sc1Id ?? '')
    if (newValue?.PlanTargetPeriodUpdateFlg === '0') {
      await getPlanningPeriodInfo(sc1Id, TeX0012Const.DEFAULT.PERIOD_KBN_OPEN)
      // 画面情報再取得
      void reload()
    } else if (newValue?.PlanTargetPeriodUpdateFlg === '1') {
      if (currentIndex === 1) {
        await openConfirmDialog2(t('message.i-cmn-11262'))
        // 処理終了
        return
      }
      await getPlanningPeriodInfo(sc1Id, TeX0012Const.DEFAULT.PERIOD_KBN_PREV)
      // 画面情報再取得
      void reload()
    } else if (newValue?.PlanTargetPeriodUpdateFlg === '2') {
      if (currentIndex === totalCount) {
        await openConfirmDialog2(t('message.i-cmn-11263'))
        // 処理終了
        return
      }
      await getPlanningPeriodInfo(sc1Id, TeX0012Const.DEFAULT.PERIOD_KBN_NEXT)
      // 画面情報再取得
      void reload()
    }
    // OrX0007Logic.data.set({
    //   uniqueCpId: orX0007.value.uniqueCpId,
    //   value: { planTargetPeriodId: '', PlanTargetPeriodUpdateFlg: '' },
    // })
  },
  { deep: true }
)

watch(
  () => OrX0008Logic.data.get(orX0008.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    console.log(newValue, 'newValue履歴変更の監視')
    const { createId, createUpateFlg } = newValue
    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }
    const { currentIndex, totalCount } = localOneway.orX0008Oneway.createData

    const teikyouId =
      createId !== '' ? createId : (localOneway.orX0008Oneway.createData.createId ?? '')
    if (createUpateFlg === '0') {
      // 画面入力変更あり、編集を保存して処理を続行
      // 履歴情報を再取得
      await getHistoryInfo(teikyouId, TeX0012Const.DEFAULT.HISTORY_KBN_OPEN)
      // 画面情報再取得
      void reload()
    } else if (createUpateFlg === '1') {
      if (currentIndex === 1) {
        // 処理終了
        return
      }
      await getHistoryInfo(teikyouId, TeX0012Const.DEFAULT.HISTORY_KBN_PREV)
      // 画面情報再取得
      void reload()
    } else if (createUpateFlg === '2') {
      if (currentIndex === totalCount) {
        // 処理終了
        return
      }
      await getHistoryInfo(teikyouId, TeX0012Const.DEFAULT.HISTORY_KBN_NEXT)
      // 画面情報再取得
      void reload()
    }
    OrX0008Logic.data.set({
      uniqueCpId: orX0008.value.uniqueCpId,
      value: {
        createId: teikyouId,
        createUpateFlg: '',
        rirekiObj: undefined,
      },
    })
  },
  { deep: true }
)
/**
 * 作成者変更監視
 */
watch(
  () => OrX0009Logic.data.get(orX0009.value.uniqueCpId),
  async (newValue) => {
    if (newValue) {
      // ・親情報を設定 ※親情報が、すべてのタブ間で共有する
      // 親情報.作成者ID = 画面.作成者ID
      setCommonInfo({ createUserId: newValue.staffId })
      await getInitDataInfo()
    }
  },
  { deep: true }
)
/**
 * 複写
 *
 * @param teikyouId -teikyouId
 */
function aceeptDataInfo(teikyouId: string) {
  /** 提供ID */
  localOneway.orX0008Oneway.createData.createId = teikyouId
  /** 基準日 */
  void getInitDataInfo()
}
</script>

<template>
  <!-- Or11871：有機体：画面メニューエリア -->
  <!-- ローディング -->
  <v-overlay
    :model-value="isLoading"
    :persistent="false"
    class="align-center justify-center"
    ><v-progress-circular
      indeterminate
      color="primary"
    ></v-progress-circular
  ></v-overlay>
  <c-v-sheet class="view d-flex flex-column h-100">
    <!-- 操作ボタンエリア -->
    <div class="action-sticky">
      <g-base-or11871 v-bind="or11871">
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            prepend-icon="file_copy"
            :disabled="local.copyDisabled"
            @click="copyBtnClick"
          />
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="t('tooltip.care-plan2-copy-btn')"
          ></c-v-tooltip>
        </template>
      </g-base-or11871>
    </div>
    <!-- アクションエリア -->
    <div class="action-content">
      <c-v-row
        no-gutters
        class="main-Content d-flex flex-0-1 h-100 overflow-y-auto position-relative"
      >
        <!-- ナビゲーションエリア -->
        <c-v-col
          cols="auto"
          class="hidden-scroll h-100 user-select"
        >
          <!-- 利用者選択一覧 -->
          <g-base-or00248 v-bind="or00248" />
        </c-v-col>
        <!-- コンテンツエリア -->
        <c-v-col class="main-right d-flex flex-column overflow-hidden h-100">
          <!-- 上段 -->
          <c-v-row
            no-gutters
            class="d-flex align-end"
          >
            <c-v-col
              cols="auto ml-6 mr-6 office-select"
              style="width: 220px"
            >
              <!-- 事業所選択画面 -->
              <g-base-or-41179
                class="business-content"
                v-bind="or41179"
              />
            </c-v-col>
            <c-v-col
              v-show="plannningPeriodManageFlg === TeX0012Const.DEFAULT.PLANNING_PERIOD_MANAGE"
              cols="auto mr-6 top-bg"
            >
              <!-- 計画対象期間 -->
              <g-custom-orX0007
                v-bind="orX0007"
                :oneway-model-value="localOneway.orX0007Oneway"
                :parent-method="checkEditBySave"
              />
            </c-v-col>
            <c-v-col
              v-show="isHistoryShow"
              cols="auto mr-6"
              style="width: 130px"
            >
              <!-- 作成日 -->
              <base-mo00020
                v-model="local.createDate"
                :oneway-model-value="localOneway.createDateOneway"
                @update:model-value="createDateChange"
              />
            </c-v-col>
            <c-v-col
              v-show="isHistoryShow"
              cols="auto mt-2 top-bg mr-6"
            >
              <!-- 作成者 -->
              <g-custom-orX0009
                v-bind="orX0009"
                :oneway-model-value="localOneway.orX0009Oneway"
              />
            </c-v-col>
            <c-v-col
              v-show="isHistoryShow"
              cols="auto top-bg"
            >
              <!-- 履歴 -->
              <g-custom-orX0008
                v-bind="orX0008"
                v-model="local.orX0008"
                :is-edit="isEdit"
                :oneway-model-value="localOneway.orX0008Oneway"
                :unique-cp-id="orX0008.uniqueCpId"
                :parent-method="checkEditBySave"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutters
            class="mt-6"
          >
            <c-v-col>
              <base-mo00043
                :model-value="local.mo00043"
                :oneway-model-value="localOneway.mo00043OnewayType"
                @update:model-value="tabChange"
              >
              </base-mo00043>
            </c-v-col>
          </c-v-row>

          <!-- 中段 -->
          <c-v-row
            no-gutters
            class="middleContent flex-1-1 h-100"
          >
            <c-v-window
              id="tabWindow"
              v-model="local.mo00043.id"
              class="h-100"
            >
              <!-- タブ1 -->
              <c-v-window-item :value="Or17392Const.DEFAULT.TAB_ID">
                <div
                  v-if="
                    !localOneway.orX0009Oneway.isDisabled &&
                    localOneway.tex0012Oneway.confirmationInfo.adlIdou1
                  "
                >
                  <g-custom-or-17392
                    v-bind="or17392"
                    :oneway-model-value="localOneway.or17392Oneway"
                    :parent-unique-cp-id="props.uniqueCpId"
                  />
                </div>
              </c-v-window-item>
              <!-- タブ2 -->
              <c-v-window-item :value="Or17393Const.DEFAULT.TAB_ID">
                <div
                  v-if="
                    !localOneway.orX0009Oneway.isDisabled &&
                    localOneway.tex0012Oneway.confirmationInfo.adlIdou1
                  "
                >
                  <g-custom-or-17393
                    v-bind="or17393"
                    :oneway-model-value="localOneway.or17393Oneway"
                    :parent-unique-cp-id="props.uniqueCpId"
                  />
                </div>
              </c-v-window-item>
            </c-v-window>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </div>
    <g-custom-or-17391
      v-if="showDialogOr17391"
      v-bind="or17391"
      :key="copyComponentKey"
      :oneway-model-value="localOneway.or17391Oneway"
      :parent-unique-cp-id="props.uniqueCpId"
      @aceept-data="aceeptDataInfo"
    />
    <!-- アセスメントマスタ画面 -->
    <g-custom-or-27562
      v-if="showDialogOr27562"
      v-bind="or27562"
      :oneway-model-value="localOneway.or27562Oneway"
    />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_1" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_2" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_3" />
    <!-- Or21815:有機体:警告ダイアログ -->
    <g-base-or21815 v-bind="or21815_1" />
    <!--  印刷画面 -->
    <g-custom-or-56886
      v-if="showDialogOr56886"
      v-bind="or56886"
    />
    <!--  削除確認画面 -->
    <g-custom-or-x-0001
      v-bind="orX0001"
      v-model="local.orX0001Type"
      :oneway-model-value="localOneway.orX0001Oneway"
      @update:model-value="deleteDialogChange"
    ></g-custom-or-x-0001>
  </c-v-sheet>
</template>

<style scoped lang="scss">
.main-Content {
  .main-left {
    max-width: 20%;
  }

  .main-right {
    .middleContent {
      min-height: 0;

      .v-window {
        width: 100%;
      }
    }
  }
}
.v-btn {
  color: rgb(118, 118, 118) !important;
  background-color: transparent !important;
  border: 1px solid rgb(118, 118, 118) !important;
}
.view {
  background-color: transparent;
  height: max-content !important;
}
.createDateOuterClass {
  width: auto !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }

  :deep(.v-input__details) {
    display: none;
  }
  :deep(.v-input__control) {
    background-color: #fff;
  }
}
:deep(.rounded.item-label) {
  min-width: 180px;
  padding-left: 8px !important;
}
.action-button {
  background-color: #fff !important;
  width: 79px !important;
  padding: 0px !important;
  min-width: auto !important;
  &:first-child {
    width: 54px !important;
  }
}
.action-sticky {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
  button {
    height: 32px !important;
  }
}
:deep(.action-sticky .v-btn) {
  height: 32px !important;
  min-height: 32px !important;
  &:nth-child(4) {
    width: 76px !important;
  }
}
.action-content {
  background: rgb(var(--v-theme-background));
  height: max-content;
  z-index: 888;
}
.top-bg {
  :deep(.v-sheet) {
    background-color: rgb(var(--v-theme-background)) !important;
  }
}

// 利用者一覧選択領域領域
:deep(.user-select > .v-card) {
  padding: 0px 0 0px 24px !important; // パディング設定
  width: max-content !important; // 幅を自動調整
  max-height: 1110px !important;
  margin-top: 24px !important; // 上部マージン
  // パンディング設定
  .v-card-actions {
    width: max-content !important; // 幅を自動調整
    padding: 8px !important; // パディング設定
  }
  // 選択領域の幅設定
  .user-info-section-container-style {
    width: 260px; // 幅を260pxに設定
  }
}
:deep(.v-list) {
  max-height: 1110px !important;
}
.business-content {
  :deep(.ma-2) {
    margin-bottom: 4px !important;
  }
}
</style>
