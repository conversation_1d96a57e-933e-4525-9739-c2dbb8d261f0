/**
 * Or26323:履歴選択モーダル
 * GUI01282_［履歴選択］画面 認定調査
 *
 * OneWayBind領域用の構造
 *
 * <AUTHOR> 宋鹏飞
 */
export interface Or26323StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
  /**
   * ［履歴選択］画面 認定調査 '0'/'1'
   */
  decisionMakingClickValue?: string
}

/**
 * DataTableのデータ
 */
export interface DataTableData {
  /**
   * 履歴選択リスト
   */
  historySelectionList: HistorySelectionListType[]
}

/**
 * 認定調査履歴情報リスト
 */
export interface rireki1 {
  /**
   * 調査票ID
   */
  cschId: string
  /**
   * 実施日
   */
  jisshiDateYmd: string
  /**
   * 記入者コード
   */
  chkShokuId: string
  /**
   * 記入者名
   */
  chkShokuNm: string
  /**
   * 改訂
   */
  dmyCho: string
  /**
   * 改訂フラグ
   */
  kaiteiFlg?: string
}

/**
 * 主治医意見書履歴情報リスト
 */
export interface rireki {
  /**
   * 医師意見書ID
   */
  rirekiId: string
  /**
   *  作成日
   */
  createYmd: string
  /**
   * 医師名前
   */
  ishiNameKnj: string
  /**
   * 改定フラグ
   */
  ikenshoFlg: string
}
