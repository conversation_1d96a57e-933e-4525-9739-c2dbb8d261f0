<script setup lang="ts">
/**
 * Or30341:有機体:アセスメント(インターライ)画面I-1
 * GUI00772_アセスメント(インターライ)画面I-1
 *
 * @description
 * GUI00772_アセスメント(インターライ)画面I-1
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, onMounted, ref, computed } from 'vue'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or30981Const } from '../Or30981/Or30981.constants'
import { TeX0003Const } from '../../template/Tex0003/TeX0003.constants'
import type { Or30341StateType, Or30341TwoWayType, RenderDataListType } from './Or30341.type'
import { Or30341Const } from './Or30341.constants'
import type { Or31909OnewayType } from '~/types/cmn/business/components/Or31909Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenTwoWayBind,
  useScreenStore,
  useCmnRouteCom,
  useSystemCommonsStore,
  useCmnCom,
} from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type {
  AssessmentInterRAII1InitSelectEntity,
  AssessmentInterRAII1InitSelectOutEntity,
  AssessmentInterRAII1UpdateEntity,
  AssessmentInterRAII1UpdateOutEntity,
} from '~/repositories/cmn/entities/assessmentInterRAII1InitSelect'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type {
  HistoryInfoEntity,
  PlanPeriodInfoEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'
import { ResBodyStatusCode } from '~/constants/api-constants'

const { t } = useI18n()

const cmnRouteCom = useCmnRouteCom()
// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or31909OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { TABLENAME, COLUMNNAME, SCREENID, T1CD, T2CD, T3CD, INDEX, TABKBN_1 } = Or30341Const

// 子コンポーネント用変数
const or51775 = ref({ uniqueCpId: '' })
const or30981 = ref({ uniqueCpId: '' })
// ロード状態です
const isLoading = ref<boolean>(false)

// 更新区分
let updateKbn = UPDATE_KBN.UPDATE

const locaModelValue: Or30341TwoWayType = {
  key: 'i1',
  memoInputvalue: {
    content: '',
    fontSize: '',
    fontColor: '',
  },
  subFormList: [
    {
      items: [
        {
          key: 'a',
          value: '',
        },
        {
          key: 'b',
          value: '',
        },
      ],
    },
    {
      items: [
        {
          key: 'c',
          value: '',
        },
        {
          key: 'd',
          value: '',
        },
        {
          key: 'e',
          value: '',
        },
        {
          key: 'f',
          value: '',
        },
        {
          key: 'g',
          value: '',
        },
        {
          key: 'h',
          value: '',
        },
        {
          key: 'i',
          value: '',
        },
        {
          key: 'j',
          value: '',
        },
      ],
    },
    {
      items: [
        {
          key: 'k',
          value: '',
        },
        {
          key: 'l',
          value: '',
        },
        {
          key: 'm',
          value: '',
        },
        {
          key: 'n',
          value: '',
        },
      ],
    },
    {
      items: [
        {
          key: 'o',
          value: '',
        },
        {
          key: 'p',
          value: '',
        },
        {
          key: 'q',
          value: '',
        },
        {
          key: 'r',
          value: '',
        },
      ],
    },
    {
      items: [
        {
          key: 's',
          value: '',
        },
        {
          key: 't',
          value: '',
        },
      ],
    },
    {
      items: [
        {
          key: 'u',
          value: '',
        },
        {
          key: 'v',
          value: '',
        },
      ],
    },
  ],
}

const renderDataList = reactive<RenderDataListType>({
  leftContentLabel: t('label.disease-I1'),
  key: 'i1',
  memoInputSetting: { class: 'i1TextArea' },
  selectOptionTitle: t('label.disease-code'),
  selectOptionLabelList: [],
  subFormList: [
    {
      categoryName: t('label.I-disease-I1-category-name-1'),
      items: [
        {
          label: t('label.I-disease-I1-label-a'),
          key: 'a',
        },
        {
          label: t('label.I-disease-I1-label-b'),
          key: 'b',
        },
      ],
    },
    {
      categoryName: t('label.I-disease-I1-category-name-2'),
      items: [
        {
          label: t('label.I-disease-I1-label-c'),
          key: 'c',
        },
        {
          label: t('label.I-disease-I1-label-d'),
          key: 'd',
        },
        {
          label: t('label.I-disease-I1-label-e'),
          key: 'e',
        },
        {
          label: t('label.I-disease-I1-label-f'),
          key: 'f',
        },
        {
          label: t('label.I-disease-I1-label-g'),
          key: 'g',
        },
        {
          label: t('label.I-disease-I1-label-h'),
          key: 'h',
        },
        {
          label: t('label.I-disease-I1-label-i'),
          key: 'i',
        },
        {
          label: t('label.I-disease-I1-label-j'),
          key: 'j',
        },
      ],
    },
    {
      categoryName: t('label.I-disease-I1-category-name-3'),
      items: [
        {
          label: t('label.I-disease-I1-label-k'),
          key: 'k',
        },
        {
          label: t('label.I-disease-I1-label-l'),
          key: 'l',
        },
        {
          label: t('label.I-disease-I1-label-m'),
          key: 'm',
        },
        {
          label: t('label.I-disease-I1-label-n'),
          key: 'n',
        },
      ],
    },
    {
      categoryName: t('label.I-disease-I1-category-name-4'),
      items: [
        {
          label: t('label.I-disease-I1-label-o'),
          key: 'o',
        },
        {
          label: t('label.I-disease-I1-label-p'),
          key: 'p',
        },
        {
          label: t('label.I-disease-I1-label-q'),
          key: 'q',
        },
        {
          label: t('label.I-disease-I1-label-r'),
          key: 'r',
        },
      ],
    },
    {
      categoryName: t('label.I-disease-I1-category-name-5'),
      items: [
        {
          label: t('label.I-disease-I1-label-s'),
          key: 's',
        },
        {
          label: t('label.I-disease-I1-label-t'),
          key: 't',
        },
      ],
    },
    {
      categoryName: t('label.I-disease-I1-category-name-6'),
      items: [
        {
          label: t('label.I-disease-I1-label-u'),
          key: 'u',
        },
        {
          label: t('label.I-disease-I1-label-v'),
          key: 'v',
        },
      ],
    },
  ],
})

// システム共有情報取得
const commonInfo = {
  executeFlag: '',
  kikanKanriFlg: '',
  kijunbiYmd: '',
  sakuseiId: '',
  historyInfo: {} as HistoryInfoEntity,
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  deleteBtnValue: '',
  raiId: '',
  assType: '',
  yokaiKbn: '',
  syubetsuId: '',
  svJigyoKnj: '',
  historyUpdateKbn: '',
  tableCreateKbn: '',
  svJigyoId: '',
  userId: '',
}

const localOneway = reactive({
  isShow: true,
  or10362Oneway: {
    width: '600px',
  },
  // アセスメント種別
  assessmentKinds: [] as CodeType[],
  // 調査アセスメント種別ラベル
  surveyAssessmentTypeOneway: '',
  // 疾患タイトル
  diseaseTitle: {
    /** 値のフォントの太さ */
    valueFontWeight: 'bold',
    /** 値 */
    value: t('label.disease'),
    /** カスタムクラス */
    customClass: new CustomClass({
      itemClass: 'align-center',
      itemStyle: 'font-size: 18px',
      outerClass: 'py-6 pl-6',
    }),
  },
  subTitleMo01338Oneway: {
    value: '',
    valueFontWeight: 'bolder',
    customClass: {
      itemStyle: ' font-size: 16px',
      itemClass: 'align-center',
    } as CustomClass,
  },
  // メモ入力内容
  mo00046MemoInputOnewayType: {
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    },
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Mo00046OnewayType,
  // GUI00937 入力支援 の単方向バインドModelValue
  or51775Oneway: {} as Or51775OnewayType,
})

// GUI00937 ［メモ入力］画面の双方向バインドModelValue
const or51775Type = ref<Or51775Type>({
  modelValue: '',
})

/**************************************************
 * Pinia
 **************************************************/

useScreenOneWayBind<Or30341StateType>({
  cpId: Or30341Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (value) {
        Object.assign(commonInfo, value)
      }
      // 調査アセスメント種別設定

      switch (value?.executeFlag) {
        // 保存
        case 'save':
          void onSave()
          break
        // 新規
        case 'add':
          void onAdd()
          break
        // 複写
        case 'copy':
          updateKbn = UPDATE_KBN.CREATE
          void onCopy()
          break
        // 削除
        case 'delete':
          updateKbn = UPDATE_KBN.DELETE
          void onDel()
          break
        // データ再取得
        case 'getData':
          void getInitDataInfo(commonInfo.historyInfo.raiId)
          break
        default:
          break
      }
    },
  },
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or30981Const.CP_ID(1)]: or30981.value,
})

/**************************************************
 * Pinia
 **************************************************/

const { refValue } = useScreenTwoWayBind<Or30341TwoWayType>({
  cpId: Or30341Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

refValue.value = { ...locaModelValue }

/**************************************************
 * Emit
 **************************************************/
/**
 * emit saveEndイベントを定義
 */
const emit = defineEmits(['saveEnd'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// GUI00787 ［メモ入力］画面表示フラグ
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

onMounted(async () => {
  await initCode()
})

/**
 * 画面項目の初期化
 */
const inputProject = () => {
  locaModelValue.memoInputvalue.content = ''
  locaModelValue.memoInputvalue.fontSize = getDefaultFontSize()
  locaModelValue.memoInputvalue.fontColor = getDefaultFontColor()
  locaModelValue.subFormList.forEach((item) => {
    item.items.forEach((subItem) => {
      subItem.value = ''
    })
  })
}

/**
 * デフォルト文字サイズ
 */
const getDefaultFontSize = (): string => {
  // 初期設定マスタ.アセスメント(インターライ)：文字サイズ
  const fontSize = cmnRouteCom.getInitialSettingMaster()?.assCharSize
  // 文字サイズが「0：小さい」の場合
  if ('0' === fontSize) {
    return '9'
  }
  // 文字サイズが「2：大きい」の場合
  else if ('2' === fontSize) {
    return '15'
  }
  // 上記以外の場合
  else {
    // 12：デフォルト値
    return '12'
  }
}

/**
 * デフォルト文字色
 */
const getDefaultFontColor = (): string => {
  // 初期設定マスタ.アセスメント(インターライ)：文字色
  const fontSize = cmnRouteCom.getInitialSettingMaster()?.assCharColor
  // 初期設定マスタ.アセスメント(インターライ).文字サイズがある場合
  if (fontSize) {
    // 初期設定マスタ.アセスメント(インターライ).文字色
    return fontSize
  }
  // 上記以外の場合
  else {
    // #000000：デフォルト値
    return '0'
  }
}

/**
 *  画面データをクリア
 */
const onAdd = () => {
  inputProject()
  setRefValue()
  updateKbn = UPDATE_KBN.CREATE
}

/**
 *  refValueを更新する
 *
 * @param isInit -
 */
function setRefValue(isInit = true) {
  useScreenStore().setCpTwoWay({
    cpId: Or30341Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: { ...locaModelValue },
    isInit,
  })
}

/**
 *  画面初期情報取得
 *
 * @param raiId - アセスメントID
 */
const getInitDataInfo = async (raiId: string) => {
  updateKbn = UPDATE_KBN.UPDATE
  localOneway.isShow = true
  isLoading.value = true
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentInterRAII1InitSelectEntity = {
    raiId,
  }
  const resData: AssessmentInterRAII1InitSelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAII1InitSelect',
    inputData
  )
  // 画面情報を設定
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    const subInfoI1Info = resData.data.subInfoI1
    if (subInfoI1Info?.raiId) {
      locaModelValue.memoInputvalue.content = subInfoI1Info.i1MemoKnj ?? ''
      locaModelValue.memoInputvalue.fontSize = subInfoI1Info.i1MemoFont ?? ''
      locaModelValue.memoInputvalue.fontColor = subInfoI1Info.i1MemoColor ?? ''
      locaModelValue.subFormList.forEach((item) => {
        item.items.forEach((subItem) => {
          subItem.value = subInfoI1Info[locaModelValue.key + subItem.key.toLocaleUpperCase()] ?? ''
        })
      })

      isLoading.value = false
    } else {
      updateKbn = UPDATE_KBN.CREATE
      inputProject()
    }
    isLoading.value = false
    // 初期値に設定する
    setRefValue()
  }
}

/**
 *  汎用コード取得API実行
 */
const initCode = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    //I1.疾患Options
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DISEASE },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  localOneway.assessmentKinds = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )
  renderDataList.selectOptionLabelList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DISEASE
  )
  // アセスメント種別
  localOneway.surveyAssessmentTypeOneway =
    localOneway.assessmentKinds.find((item) => item.value === commonInfo.historyInfo.assType)
      ?.label ?? ''
}

/**
 * 保存ボタン押下
 */
const onSave = async () => {
  // 更新データ作成
  const inputData: AssessmentInterRAII1UpdateEntity = {
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    sectionName: TeX0003Const.SECTION_NAME,
    gsyscd: systemCommonsStore.getSystemCode ?? '',
    kinouId: systemCommonsStore.getFunctionId ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    kikanKanriFlg: commonInfo.kikanKanriFlg,
    historyNo: commonInfo.historyInfo.krirekiNo,
    svJigyoName: commonInfo.svJigyoKnj,
    periodNo: commonInfo.planPeriodInfo.periodNo,
    startYmd: commonInfo.planPeriodInfo.startYmd ?? '',
    endYmd: commonInfo.planPeriodInfo.endYmd ?? '',
    index: INDEX,
    tabName: TABLENAME,
    edocumentUseParam: useCmnCom().getEDocumentParam(),
    edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    syubetsuId: commonInfo.syubetsuId,
    subKbn: TABKBN_1,
    updateKbn,
    historyUpdateKbn: commonInfo.historyUpdateKbn,
    tableCreateKbn: commonInfo.tableCreateKbn,
    sc1Id: commonInfo.planPeriodInfo.sc1Id,
    raiId: commonInfo.historyInfo.raiId,
    kijunbiYmd: commonInfo.kijunbiYmd,
    sakuseiId: commonInfo.sakuseiId,
    assType: commonInfo.historyInfo.assType,
    subInfoI1: {
      raiId: '',
      i1A: '',
      i1B: '',
      i1C: '',
      i1D: '',
      i1E: '',
      i1F: '',
      i1G: '',
      i1H: '',
      i1I: '',
      i1J: '',
      i1K: '',
      i1L: '',
      i1M: '',
      i1N: '',
      i1O: '',
      i1P: '',
      i1Q: '',
      i1R: '',
      i1S: '',
      i1T: '',
      i1U: '',
      i1V: '',
      i1MemoKnj: '',
      i1MemoFont: '',
      i1MemoColor: '',
    },
  }

  // 入力値処理です
  refValue.value!.subFormList.forEach((item) => {
    item.items.forEach((subItem) => {
      inputData.subInfoI1[refValue.value!.key + subItem.key.toLocaleUpperCase()] = subItem.value
    })
    inputData.subInfoI1[refValue.value!.key + 'MemoKnj'] = refValue.value!.memoInputvalue.content
    inputData.subInfoI1[refValue.value!.key + 'MemoColor'] =
      refValue.value!.memoInputvalue.fontColor
    inputData.subInfoI1[refValue.value!.key + 'MemoFont'] = refValue.value!.memoInputvalue.fontSize
  })

  const resData: AssessmentInterRAII1UpdateOutEntity = await ScreenRepository.update(
    'assessmentInterRAII1Update',
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    emit('saveEnd', resData.data.errKbn)
  }
}
/**
 * AC005_「複写ボタン」押下
 */
const onCopy = () => {
  void getInitDataInfo(commonInfo.raiId)
}

/**
 * AC011_「削除」押下
 */
const onDel = () => {
  localOneway.isShow = false
}

/**
 *  メモ入力選択ボタンクリック
 *
 */
const memoInputIconBtnClick = () => {
  //  GUI00937 入力支援をポップアップで起動
  localOneway.or51775Oneway = {
    // タイトル
    title: t('label.memo'),
    // 画面ID
    screenId: SCREENID,
    // 分類ID
    bunruiId: '',
    // 大分類ＣＤ
    t1Cd: T1CD,
    // 中分類CD
    t2Cd: T2CD,
    // 小分類ＣＤ
    t3Cd: T3CD,
    // テーブル名
    tableName: TABLENAME,
    // カラム名
    columnName: COLUMNNAME,
    // アセスメント方式
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? '',
    // 文章内容
    inputContents: refValue.value?.memoInputvalue.content,
    // 利用者ID
    userId: systemCommonsStore.getUserId,
    // モード
    mode: '1',
  } as Or51775OnewayType
  // GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    // 本文末に追加の場合
    if (Or30341Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE === data.type) {
      refValue.value!.memoInputvalue.content += data.value
    }
    // 本文上書の場合
    else if (Or30341Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE === data.type) {
      refValue.value!.memoInputvalue.content = data.value
    }
  }
}
</script>

<template>
  <c-v-sheet class="or30341Container">
    <v-overlay
      :model-value="isLoading"
      :persistent="true"
      class="align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      >
      </v-progress-circular>
    </v-overlay>
    <c-v-row no-gutters>
      <c-v-col cols="auto content">
        <!-- アセスメント種別タイトル -->
        <c-v-row
          no-gutters
          class="mb-2 mt-6"
        >
          <c-v-spacer />
          <div class="heading-text-l">
            {{ localOneway.surveyAssessmentTypeOneway }}
          </div>
        </c-v-row>
        <!-- マトリクス領域です -->
        <div class="content-area">
          <c-v-row no-geutters>
            <!--  疾患タイトル -->
            <base-mo01338 :oneway-model-value="localOneway.diseaseTitle" />
          </c-v-row>
          <c-v-row
            v-show="localOneway.isShow"
            no-gutters
          >
            <!-- タイトル部分 -->
            <c-v-col
              cols="12 "
              class="title-style"
            >
              <base-mo01338
                class="background-transparent"
                :oneway-model-value="{
                  ...localOneway.subTitleMo01338Oneway,
                  value: renderDataList.leftContentLabel,
                }"
              />
            </c-v-col>
            <!-- 内容部分 -->
            <c-v-col
              col="12"
              class="pl-12 pr-6"
            >
              <!--プルダウンですlabelGroup  -->
              <c-v-row
                class="mb-6"
                no-gutters
              >
                <c-v-col
                  cols="12"
                  class="pl-0 pt-6 pb-1 fontStyle"
                >
                  {{ renderDataList.selectOptionTitle }}
                </c-v-col>
                <c-v-col
                  cols="12"
                  class="bordered py-2 px-4"
                >
                  <div
                    v-for="(item, key) in renderDataList.selectOptionLabelList"
                    :key="key"
                    class="fontStyle"
                  >
                    {{ item.label }}
                  </div>
                </c-v-col>
              </c-v-row>
              <!--フォーム部分です -->
              <div>
                <c-v-row
                  v-for="(formItem, formKey) in renderDataList.subFormList"
                  :key="formKey"
                  class="mt-2"
                  no-gutters
                >
                  <c-v-col cols="12">
                    <c-v-row no-gutters>
                      <c-v-col
                        cols="12"
                        class="liltte-title-style mb-6"
                      >
                        {{ formItem.categoryName }}
                      </c-v-col>
                    </c-v-row>
                    <c-v-row
                      v-for="(subItem, subIndex) in formItem.items"
                      :key="subIndex"
                      no-gutters
                      class="mb-4 justify-space-between"
                    >
                      <c-v-col>
                        {{ subItem.label }}
                      </c-v-col>
                      <c-v-col cols="auto">
                        <div class="select-style">
                          <base-at-select
                            v-model="refValue!.subFormList[formKey].items[subIndex].value"
                            name=""
                            :items="
                              renderDataList.selectOptionLabelList.map((item) => {
                                return { ...item, label: item.label.split('―')[0] }
                              })
                            "
                            item-title="label"
                            item-value="value"
                            hide-details="true"
                          >
                          </base-at-select>
                        </div>
                      </c-v-col>
                    </c-v-row>
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="mb-12"
                >
                  <c-v-col class="pa-0">
                    <g-custom-or-30981
                      v-bind="or30981"
                      v-model="refValue!.memoInputvalue"
                      :oneway-model-value="localOneway.mo00046MemoInputOnewayType"
                      @on-click-edit-btn="memoInputIconBtnClick"
                    ></g-custom-or-30981>
                  </c-v-col>
                </c-v-row>
              </div>
            </c-v-col>
          </c-v-row>
        </div>
        <!-- interRAIロゴ -->
        <c-v-row
          no-gutters
          class="mt-2"
        >
          <c-v-col cols="12">
            <c-v-img
              width="129"
              aspect-ratio="16/9"
              cover
              :src="InterRAI"
              style="float: right"
            ></c-v-img>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <!-- GUI00937_入力支援［ケアマネ］ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Type"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="or51775Confirm"
  />
</template>

<style scoped lang="scss">
.or30341Container {
  background-color: transparent;
  .content {
    width: 1080px;
    .heading-text-l {
      font-weight: 400 !important;
    }
    .content-area {
      width: 100%;
      background-color: #fff;
    }
  }
}

.button-style {
  display: flex;
  justify-content: flex-start;
  padding-left: 8px;
}

.select-style {
  width: 450px !important;
}
.bordered {
  border: 1px #3333 solid;
}

.v-row {
  margin: 0px;
}
.v-col {
  padding: 8px;
}

.fontStyle {
  font-size: 14px;
  font-weight: bold;
}
.title-style {
  border: 1px rgb(var(--v-theme-black-200)) solid;
  background: #e6e6e6;
  padding: 13.5px 24px !important;
  height: 48px !important;
}
.liltte-title-style {
  border: 1px rgb(var(--v-theme-black-200)) solid;
  background: #0760e614;
  padding: 13.5px 12px !important;
  height: 48px !important;
  border-radius: 4px;
  font-weight: 700;
}
.background-transparent {
  background-color: transparent;
}
</style>
