<script setup lang="ts">
/**
 * OrX0073:有機体:［計画書複写］（計画書（2））計画書（2）複写詳細リスト
 * GUI01017
 *
 * @description
 * ［計画書複写］（計画書（2））計画書（2）複写詳細リストを表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import {
  ref,
  reactive,
  computed,
  watch,
  nextTick,
  onMounted,
  onBeforeMount,
  onUnmounted,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0042Const } from '../OrX0042/OrX0042.constants'
import type { ColumnInfo, Keikasyo2 } from '../OrX0042/OrX0042.type'
import { Or00586Const } from '../Or00586/Or00586.constants'
import { Or00586Logic } from '../Or00586/Or00586.logic'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type {
  OrX0073OnewayType,
  OrX0073OnewayType2,
  OrX0073Type,
} from '~/types/cmn/business/components/OrX0073Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useSetupChildProps, useSystemCommonsStore } from '#imports'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21736Const } from '~/components/base-components/organisms/Or21736/Or21736.constants'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21735Logic } from '~/components/base-components/organisms/Or21735/Or21735.logic'
import { Or21736Logic } from '~/components/base-components/organisms/Or21736/Or21736.logic'
import { Or21737Logic } from '~/components/base-components/organisms/Or21737/Or21737.logic'
import { Or21738Logic } from '~/components/base-components/organisms/Or21738/Or21738.logic'
import type {
  IssuesConsiderBlankFormInfoType,
  Or00586Type,
} from '~/types/cmn/business/components/Or00586Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import type { Or27235OnewayType, Or27235Type } from '~/types/cmn/business/components/Or27235Type'
import { Or27235Const } from '~/components/custom-components/organisms/Or27235/Or27235.constants'
import { Or27235Logic } from '~/components/custom-components/organisms/Or27235/Or27235.logic'
import type { Or28720OnewayType, Or28720Type } from '~/types/cmn/business/components/Or28720Type'
import { Or28720Const } from '~/components/custom-components/organisms/Or28720/Or28720.constants'
import { Or28720Logic } from '~/components/custom-components/organisms/Or28720/Or28720.logic'
import type { Or27362OnewayType, Or27362Type } from '~/types/cmn/business/components/Or27362Type'
import { Or27362Const } from '~/components/custom-components/organisms/Or27362/Or27362.constants'
import { Or27362Logic } from '~/components/custom-components/organisms/Or27362/Or27362.logic'
import type { Or50429OneWayType, Or50429Type } from '~/types/cmn/business/components/Or50429Type'
import { Or50429Const } from '~/components/custom-components/organisms/Or50429/Or50429.constants'
import { Or50429Logic } from '~/components/custom-components/organisms/Or50429/Or50429.logic'
import type { Or27043Type } from '~/types/cmn/business/components/Or27043Type'
import { Or27043Const } from '~/components/custom-components/organisms/Or27043/Or27043.constants'
import { Or27043Logic } from '~/components/custom-components/organisms/Or27043/Or27043.logic'
import type {
  Or28256OnewayType,
  Or28256DataType,
} from '~/components/custom-components/organisms/Or28256/Or28256.type.d.ts'
import { Or28256Const } from '~/components/custom-components/organisms/Or28256/Or28256.constants'
import { Or28256Logic } from '~/components/custom-components/organisms/Or28256/Or28256.logic'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import type { Or54215OnewayType } from '~/types/cmn/business/components/Or54215Type'
import { Or54215Const } from '~/components/custom-components/organisms/Or54215/Or54215.constants'
import { Or54215Logic } from '~/components/custom-components/organisms/Or54215/Or54215.logic'
import type { Or53186OnewayType, Or53186Type } from '~/types/cmn/business/components/Or53186Type'
import { Or53186Const } from '~/components/custom-components/organisms/Or53186/Or53186.constants'
import { Or53186Logic } from '~/components/custom-components/organisms/Or53186/Or53186.logic'
import { Or51726Const } from '~/components/custom-components/organisms/Or51726/Or51726.constants'
import { Or51726Logic } from '~/components/custom-components/organisms/Or51726/Or51726.logic'
import type { Or51726Type } from '~/types/cmn/business/components/Or51726Type'
import type { Or28567OnewayType, Or28567Type } from '~/types/cmn/business/components/Or28567Type'
import { Or28567Const } from '~/components/custom-components/organisms/Or28567/Or28567.constants'
import { Or28567Logic } from '~/components/custom-components/organisms/Or28567/Or28567.logic'
import type { Or10477OnewayType, Or10477Type } from '~/types/cmn/business/components/Or10477Type'
import { Or10477Const } from '~/components/custom-components/organisms/Or10477/Or10477.constants'
import { Or10477Logic } from '~/components/custom-components/organisms/Or10477/Or10477.logic'
import type { Or28650OnewayType, Or28650Type } from '~/types/cmn/business/components/Or28650Type'
import { Or28650Const } from '~/components/custom-components/organisms/Or28650/Or28650.constants'
import { Or28650Logic } from '~/components/custom-components/organisms/Or28650/Or28650.logic'
import type {
  Or10860OnewayType,
  Or10860Type,
  sectionItem,
} from '~/types/cmn/business/components/Or10860Type'
import { Or10860Const } from '~/components/custom-components/organisms/Or10860/Or10860.constants'
import { Or10860Logic } from '~/components/custom-components/organisms/Or10860/Or10860.logic'
import { Or28582Const } from '~/components/custom-components/organisms/Or28582/Or28582.constants'
import { Or28582Logic } from '~/components/custom-components/organisms/Or28582/Or28582.logic'
import type { Or10475OnewayType, Or10475Type } from '~/types/cmn/business/components/Or10475Type'
import { Or10475Const } from '~/components/custom-components/organisms/Or10475/Or10475.constants'
import { Or10475Logic } from '~/components/custom-components/organisms/Or10475/Or10475.logic'
import type { Or22951OnewayType } from '~/types/cmn/business/components/Or22951Type'
import { Or22951Const } from '~/components/custom-components/organisms/Or22951/Or22951.constants'
import { Or22951Logic } from '~/components/custom-components/organisms/Or22951/Or22951.logic'
import { Or10878Const } from '~/components/custom-components/organisms/Or10878/Or10878.constants'
import { Or10878Logic } from '~/components/custom-components/organisms/Or10878/Or10878.logic'
import type { Or10878OnewayType, Or10878Type } from '~/types/cmn/business/components/Or10878Type'
import type { ApplicableOfficeResponseInfo } from '~/repositories/cmn/entities/ApplicableOfficeConfirmSelectEntity'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  ValidPeriodIdSelectInEntity,
  ValidPeriodIdSelectOutEntity,
} from '~/repositories/cmn/entities/ValidPeriodIdSelectEntity'
import { useValidation } from '~/utils/useValidation'
import type { AuthzOtherCaremanagerType } from '~/types/business/stores/SystemCommonsType'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'

const { byteLength } = useValidation()

// システム共有領域の状態管理
const systemCommonsStore = useSystemCommonsStore()
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue?: OrX0073OnewayType2
  modelValue: OrX0073Type
}
const props = defineProps<Props>()

const or21735 = ref({ uniqueCpId: Or21735Const.CP_ID(0) })
const or21736 = ref({ uniqueCpId: Or21736Const.CP_ID(0) })
const or21737 = ref({ uniqueCpId: Or21737Const.CP_ID(0) })
const or21738 = ref({ uniqueCpId: Or21738Const.CP_ID(0) })
const or00586 = ref({ uniqueCpId: Or00586Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or27235 = ref({ uniqueCpId: Or27235Const.CP_ID(0) })
const or28720 = ref({ uniqueCpId: Or28720Const.CP_ID(0) })
const or27362 = ref({ uniqueCpId: Or27362Const.CP_ID(0) })
const or50429 = ref({ uniqueCpId: Or50429Const.CP_ID(0) })
const or27043 = ref({ uniqueCpId: Or27043Const.CP_ID(0) })
const or28256 = ref({ uniqueCpId: Or28256Const.CP_ID(0) })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })
const or54215 = ref({ uniqueCpId: Or54215Const.CP_ID(0) })
const or53186 = ref({ uniqueCpId: Or53186Const.CP_ID(0) })
const or51726 = ref({ uniqueCpId: Or51726Const.CP_ID(0) })
const or28567 = ref({ uniqueCpId: Or28567Const.CP_ID(0) })
const or10477 = ref({ uniqueCpId: Or10477Const.CP_ID(0) })
const or28650 = ref({ uniqueCpId: Or28650Const.CP_ID(0) })
const or10860 = ref({ uniqueCpId: Or10860Const.CP_ID(0) })
const or28582 = ref({ uniqueCpId: Or28582Const.CP_ID(0) })
const or10475 = ref({ uniqueCpId: Or10475Const.CP_ID(0) })
const or22951 = ref({ uniqueCpId: Or22951Const.CP_ID(0) })
const or10878 = ref({ uniqueCpId: Or10878Const.CP_ID(1) })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21735Const.CP_ID(0)]: or21735.value,
  [Or21736Const.CP_ID(0)]: or21736.value,
  [Or21737Const.CP_ID(0)]: or21737.value,
  [Or21738Const.CP_ID(0)]: or21738.value,
  [Or00586Const.CP_ID(0)]: or00586.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or27235Const.CP_ID(0)]: or27235.value,
  [Or28720Const.CP_ID(0)]: or28720.value,
  [Or27362Const.CP_ID(0)]: or27362.value,
  [Or50429Const.CP_ID(0)]: or50429.value,
  [Or27043Const.CP_ID(0)]: or27043.value,
  [Or28256Const.CP_ID(0)]: or28256.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or54215Const.CP_ID(0)]: or54215.value,
  [Or53186Const.CP_ID(0)]: or53186.value,
  [Or51726Const.CP_ID(0)]: or51726.value,
  [Or28567Const.CP_ID(0)]: or28567.value,
  [Or10477Const.CP_ID(0)]: or10477.value,
  [Or28650Const.CP_ID(0)]: or28650.value,
  [Or10860Const.CP_ID(0)]: or10860.value,
  [Or28582Const.CP_ID(0)]: or28582.value,
  [Or10475Const.CP_ID(0)]: or10475.value,
  [Or22951Const.CP_ID(0)]: or22951.value,
  [Or10878Const.CP_ID(1)]: or10878.value,
})
// 確認ダイアログのPromise
let or21814ResolvePromise: (value: Or21814EventType) => void
// TODO 共通情報
const commonInfoData = reactive({
  //事業者グループ適用ID
  officeGroupId: 'G0002',
  eFileSaveKbn: '1',
  showMessageFlg: '1',
  // サービスＫＢＮ
  serviceType: '50010',
  // 計画書(2)マスタ
  carePlan2Mastr: {
    // 期間の管理
    periodManager: OrX0042Const.PERIOD_MANAGER.DATE,
    // 番号フラグ
    numberFlg: '1',
    // 日付フラグ
    dateFlg: '1',
    //取込設定
    importSettings: '1',
  },
  // e-文書法対象機能
  eDocumentLawTgtFunc: {
    // 電子ファイル保存設定区分
    elecFileSaveType: '0',
  },
  //処理名
  processFirstName: '[knu1][3GK]計画書(2)',
  //システムコード
  syscd: '3GK',
  // 頻度取込フラグ
  cks2HindoFlg: 0,
})

const defaultOneway = {
  // 生活全般の解決すべき課題(ニーズ)アイコンボタン
  mo00009OnewayGutaitekiKnj: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 長期目標
  mo00009OnewaylongTermGoal: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 長期目標期間年月日
  mo00009OnewaylongTermGoalYmd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 長期目標期間月日
  mo00009OnewaylongTermGoalMd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 長期目標期間
  mo00009OnewaylongTermGoalPeriod: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 短期目標
  mo00009OnewayShortTermGoal: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 短期目標期間
  mo00009OnewayShortTermGoalPeriod: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 短期目標期間年月日
  mo00009OnewayShortTermGoalPeriodYmd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 短期目標期間月日
  mo00009OnewayShortTermGoalPeriodMd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // サービス内容
  mo00009OnewayServiceContents: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,

  // サービス種別
  mo00009OnewayServiceKbn: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 保険サービス
  mo00009OnewayInsService: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 利用票取込
  mo00009OnewayUseSlipImport: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // ※2
  mo00009OnewayStar2: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 担当者
  mo00009OnewayManager: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 担当者取込
  mo00009OnewayManagerImport: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 頻度
  mo00009OnewayFrequency: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 頻度曜日
  mo00009OnewayFrequencyDayOfWeek: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 頻度（取込）
  mo00009OnewayFrequencyImport: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 期間
  mo00009OnewayPeriod: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 期間年月日
  mo00009OnewayPeriodYmd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 間月日
  mo00009OnewayPeriodMd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 画面メニューマスタ他設定
  mo00009OnewayMasterConfig: {
    density: 'compact',
    rounded: 'sm',
    size: 'medium',
    btnIcon: 'database',
  } as Mo00009OnewayType,
  // 1編集入力アイコンボタン
  mo00009OnewayStart1Edit: {
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 週間日課
  mo00009OnewayWeekDailyRoutineEdit: {
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,

  // 計画書複写］（計画書（2））計画書（2）複写詳細リスト
  orX0073Oneway: {
    tableItem: [] as Keikasyo2[],
    cpyFlg: false,
    periodManageFlag: '',
    planTargetPeriodId: '',
  } as OrX0073OnewayType,
  // 選択列一括上書ボタン
  mo00611BatchOverriteScOneway: {
    btnLabel: t('btn.select-columns-bundle-overrite'),
    width: '119px',
  } as Mo00611OnewayType,
  // データ-ページング
  mo01338Oneway: {
    unit: t('label.item'),
    customClass: {
      outerClass: 'page-label-center',
    },
  } as Mo01338OnewayType,
  // 下アイコンボタン
  mo00009OnewayDown: {
    btnIcon: 'keyboard_arrow_down',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  // 上アイコンボタン
  mo00009OnewayUp: {
    btnIcon: 'keyboard_arrow_up',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  // GUI01021_担当者入力支援
  or27235OnewayType: {
    pattern: 0,
  } as Or27235OnewayType,
  // GUI01029_プロブレムリスト取込
  or28720OnewayType: {
    processFirstName: '',
  } as Or28720OnewayType,
  // GUI01023_課題整理総括取込画面
  or27362OnewayType: {
    organizingIssuesImportType: { svJigyoId: '', syubetsuId: '', userId: '', shisetuId: '' },
  } as Or27362OnewayType,
  // GUI00957_サービス種別入力支援
  or50429OnewayType: {
    screenDisplayMode: '0',
    serviceKind: '',
    officeName: '',
    processName: '',
    insuranceServiceImport: '',
    createDate: '',
    serviceContent: '',
    revisionFlg: '',
  } as Or50429OneWayType,
  // GUI01016	_ケース一覧
  or28256OnewayType: {
    houjinId: '',
    sisetsuId: '',
    bunruiCd: '',
    koumokuno: 0,
    syscd: '',
    kaisihi: { value: '' },
    syuuryouhi: { value: '' },
    targetItem: '',
    caseInformation: '',
    userId: '',
    sheetno: '',
    authen: '',
    systemYmd: { value: '' },
    defSvJigyoId: '',
  } as Or28256OnewayType,
  // GUI00937_入力支援［ケアマネ］
  or51775OnewayType: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
  // GUI01019_SDケアマスタ簡易登録
  or54215OnewayType: {
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
    choukiKnj: '',
    tankiKnj: '',
    needsKnj: '',
    serviceKnj: '',
  } as Or54215OnewayType,
  // GUI01018_SDケア作成
  or53186OnewayType: {
    ks21Id: '',
    termid: '',
    systemDate: '',
  } as Or53186OnewayType,
  // GUI01015_課題と短期目標取込
  or28567OnewayType: {
    issuesAndShortTermGoalImportType: {
      shisetuId: '',
      userId: '',
      svJigyoId: '',
      syubetsuId: '',
      cpnFlg: '',
      periodManagementFlag: '',
    },
  } as Or28567OnewayType,
  // GUI01026_課題と目標取込画面
  or10477OnewayType: {
    issuesOrGoalImportType: {
      shisetuId: '',
      svJigyoId: '',
      syubetsuId: '',
      userId: '',
      sc1Id: '',
      kanriFlg: '',
    },
  } as Or10477OnewayType,
  // GUI01027_フリーアセスメント取込
  or28650OnewayType: {
    importFreeAssessmentSelectType: {
      svJigyoId: '',
      userId: '',
      syubetsuId: '',
      shisetuId: '',
      kanriFlg: '',
      particularFlag: '1',
    },
  } as Or28650OnewayType,
  // GUI01032_表示順変更計画書（2）
  or10860OnewayType: {
    carePlanStyle: 0,
    numberFlag: 0,
  } as Or10860OnewayType,
  // GUI01028_課題とサービス内容取込画面
  or10475OnewayType: {
    issuesAndServiceContenImportType: {
      shisetuId: '',
      svJigyoId: '',
      syubetsuId: '',
      userId: '',
      sc1Id: '',
      kanriFlg: '',
    },
  } as Or10475OnewayType,
  // GUI02259_適用事業所の選択画面
  or22951OnewayType: {
    systemCode: '',
    functionId: '',
    selectList: [],
    useMode: '',
    freeparmValL: '',
  } as Or22951OnewayType,
  // GUI01091_保険サービス登録画面
  or10878Oneway: {
    houjinId: '',
    shokuinId: '',
    shisetuId: '',
    userId: '',
    svJigyoCd: '',
    svJigyoId: '',
    sysYmd: '',
    termid: '',
    functionId: '',
    callerFlag: '',
    hsJigyoFlg: '',
    hindoFlg: '',
    hokenSvList: [],
    hokenYmdList: [],
  } as Or10878OnewayType,
}

const localOneway = reactive({
  mo00009OnewayGutaitekiKnj: {
    ...defaultOneway.mo00009OnewayGutaitekiKnj,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoal: {
    ...defaultOneway.mo00009OnewaylongTermGoal,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoalYmd: {
    ...defaultOneway.mo00009OnewaylongTermGoalYmd,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoalMd: {
    ...defaultOneway.mo00009OnewaylongTermGoalMd,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoalPeriod: {
    ...defaultOneway.mo00009OnewaylongTermGoalPeriod,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoal: {
    ...defaultOneway.mo00009OnewayShortTermGoal,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoalPeriod: {
    ...defaultOneway.mo00009OnewayShortTermGoalPeriod,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoalPeriodYmd: {
    ...defaultOneway.mo00009OnewayShortTermGoalPeriodYmd,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoalPeriodMd: {
    ...defaultOneway.mo00009OnewayShortTermGoalPeriodMd,
  } as Mo00009OnewayType,
  mo00009OnewayServiceContents: {
    ...defaultOneway.mo00009OnewayServiceContents,
  } as Mo00009OnewayType,
  mo00009OnewayServiceKbn: {
    ...defaultOneway.mo00009OnewayServiceKbn,
  } as Mo00009OnewayType,
  mo00009OnewayInsService: {
    ...defaultOneway.mo00009OnewayInsService,
  } as Mo00009OnewayType,
  mo00009OnewayUseSlipImport: {
    ...defaultOneway.mo00009OnewayUseSlipImport,
  } as Mo00009OnewayType,
  mo00009OnewayStar2: {
    ...defaultOneway.mo00009OnewayStar2,
  } as Mo00009OnewayType,
  mo00009OnewayManager: {
    ...defaultOneway.mo00009OnewayManager,
  } as Mo00009OnewayType,
  mo00009OnewayManagerImport: {
    ...defaultOneway.mo00009OnewayManagerImport,
  } as Mo00009OnewayType,
  mo00009OnewayFrequency: {
    ...defaultOneway.mo00009OnewayFrequency,
  } as Mo00009OnewayType,
  mo00009OnewayFrequencyDayOfWeek: {
    ...defaultOneway.mo00009OnewayFrequencyDayOfWeek,
  } as Mo00009OnewayType,
  mo00009OnewayFrequencyImport: {
    ...defaultOneway.mo00009OnewayFrequencyImport,
  } as Mo00009OnewayType,
  mo00009OnewayPeriod: {
    ...defaultOneway.mo00009OnewayPeriod,
  } as Mo00009OnewayType,
  mo00009OnewayPeriodYmd: {
    ...defaultOneway.mo00009OnewayPeriodYmd,
  } as Mo00009OnewayType,
  mo00009OnewayPeriodMd: {
    ...defaultOneway.mo00009OnewayPeriodMd,
  } as Mo00009OnewayType,
  mo00009OnewayMasterConfig: {
    ...defaultOneway.mo00009OnewayMasterConfig,
  } as Mo00009OnewayType,
  mo00009OnewayStart1Edit: {
    ...defaultOneway.mo00009OnewayStart1Edit,
  } as Mo00009OnewayType,
  mo00009OnewayWeekDailyRoutineEdit: {
    ...defaultOneway.mo00009OnewayWeekDailyRoutineEdit,
  } as Mo00009OnewayType,
  orX0073Oneway: {} as OrX0073OnewayType,
  // ～
  mo01337OnewayWaveDash: {
    value: t('label.wave-dash'),
  } as Mo01337OnewayType,
  // 〇
  mo01337OnewayCircle: {
    value: t('label.circle'),
  } as Mo01337OnewayType,
  mo00611BatchOverriteScOneway: {
    ...defaultOneway.mo00611BatchOverriteScOneway,
  } as Mo00611OnewayType,
  mo01338Oneway: {
    ...defaultOneway.mo01338Oneway,
  } as Mo01338OnewayType,
  mo00009OnewayDown: {
    ...defaultOneway.mo00009OnewayDown,
  } as Mo00009OnewayType,
  mo00009OnewayUp: {
    ...defaultOneway.mo00009OnewayUp,
  } as Mo00009OnewayType,
  // GUI01007_課題検討用紙取込画面
  or00586Oneway: {} as IssuesConsiderBlankFormInfoType,
  // GUI01021_担当者入力支援
  or27235OnewayType: {
    ...defaultOneway.or27235OnewayType,
  } as Or27235OnewayType,
  // GUI01029_プロブレムリスト取込
  or28720OnewayType: {
    ...defaultOneway.or28720OnewayType,
  } as Or28720OnewayType,
  // GUI01023_課題整理総括取込画面
  or27362OnewayType: {
    ...defaultOneway.or27362OnewayType,
  } as Or27362OnewayType,
  // GUI00957_サービス種別入力支援
  or50429OnewayType: {
    ...defaultOneway.or50429OnewayType,
  } as Or50429OneWayType,
  // GUI01016_ケース一覧
  or28256OnewayType: {
    ...defaultOneway.or28256OnewayType,
  } as Or28256OnewayType,
  // GUI00937_入力支援［ケアマネ］
  or51775OnewayType: {
    ...defaultOneway.or51775OnewayType,
  } as Or51775OnewayType,
  // GUI01019_SDケアマスタ簡易登録
  or54215OnewayType: {
    ...defaultOneway.or54215OnewayType,
  } as Or54215OnewayType,
  // GUI01018_SDケア作成
  or53186OnewayType: {
    ...defaultOneway.or53186OnewayType,
  } as Or53186OnewayType,
  // GUI01015_課題と短期目標取込
  or28567OnewayType: {
    ...defaultOneway.or28567OnewayType,
  } as Or28567OnewayType,
  // GUI01026_課題と目標取込画面
  or10477OnewayType: {
    ...defaultOneway.or10477OnewayType,
  } as Or10477OnewayType,
  // GUI01027_フリーアセスメント取込
  or28650OnewayType: {
    ...defaultOneway.or28650OnewayType,
  } as Or28650OnewayType,
  // GUI01032_表示順変更計画書（2）
  or10860OnewayType: {
    ...defaultOneway.or10860OnewayType,
  } as Or10860OnewayType,
  // GUI01028_課題とサービス内容取込画面
  or10475OnewayType: {
    ...defaultOneway.or10475OnewayType,
  } as Or10475OnewayType,
  // GUI02259_適用事業所の選択画面
  or22951OnewayType: {
    ...defaultOneway.or22951OnewayType,
  } as Or22951OnewayType,
  // GUI01091_保険サービス登録画面
  or10878Oneway: {
    ...defaultOneway.or10878Oneway,
  } as Or10878OnewayType,
})

const local = reactive({
  orX0073: {
    selectedItemIndex: 0,
    selectedItemTableIndex: -1,
    selectedColKey: '',
    tableItem: [],
    selectedItem: [],
  } as OrX0073Type,
  or00586: {} as Or00586Type,
  mo00018: { modelValue: false } as Mo00018Type,
  // GUI01021_担当者入力支援画面双方向バインドのインタフェース
  or27235Type: { mode: 0, userId: '', manager: '', result: '' } as Or27235Type,
  // GUI01023_課題整理総括取込画面双方向バインドのインタフェース
  or27362Type: { kadaiKnj: '', yusenNo: '' } as Or27362Type,
  // GUI01029_プロブレムリスト取込画面双方向バインドのインタフェース
  or28720Type: { operation_mode: 0, problemList: [], importItemList: [] } as Or28720Type,
  // GUI00957_サービス種別入力支援画面双方向バインドのインタフェース
  or50429Type: { serviceType: { value: '' }, offerOffice: { value: '' } } as Or50429Type,
  // GUI01022_担当者・頻度画面支援双方向バインドのインタフェース
  or27043Type: {
    selectedJobTypeIds: [],
    dayOfWeekInfo: {
      dayOfWeek1: '',
      dayOfWeek2: '',
      dayOfWeek3: '',
      dayOfWeek4: '',
      dayOfWeek5: '',
      dayOfWeek6: '',
      dayOfWeek7: '',
      startTime: { value: '' },
      endTime: { value: '' },
    },
  } as Or27043Type,
  // GUI01016_ケース一覧画面双方向バインドのインタフェース
  or28256Type: {
    value: '',
    startDate: '',
    endDate: '',
    caseInformation: '',
  } as Or28256DataType,
  // GUI00937_入力支援［ケアマネ］画面双方向バインドのインタフェース
  or51775Type: {
    modelValue: '',
  } as Or51775Type,
  // GUI01018_SDケア作成画面双方向バインドのインタフェース
  or53186Type: { carePlan2List: [], importMode: '' } as Or53186Type,
  // GUI01031_SDケア選択画面双方向バインドのインタフェース
  or51726Type: { selectResult: '' } as Or51726Type,
  // GUI01015_課題と短期目標取込画面双方向バインドのインタフェース
  or28567Type: { issues: '', longTermGoalIssues: '', shortTermGoal: '' } as Or28567Type,
  // GUI01026_課題と目標取込画面双方向バインドのインタフェース
  or10477Type: { kadaiKnj: '', choukiKnj: '', tankiKnj: '', careKnj: '' } as Or10477Type,
  // GUI01027_フリーアセスメント取込画面双方向バインドのインタフェース
  or28650Type: { issues: '', longTermGoals: '', shortTermGoal: '', careContent: '' } as Or28650Type,
  // GUI01032_表示順変更計画書（2）画面双方向バインドのインタフェース
  or10860Type: { sectionList: [] } as Or10860Type,
  // GUI01024_曜日取込画面双方向バインドのインタフェース
  or28582Type: { listData: [], textData: '' } as { listData: boolean[]; textData: string },
  // GUI01028_課題とサービス内容取込画面双方向バインドのインタフェース
  or10475Type: { kadaiKnj: '', serviceMemoKnj: '' } as Or10475Type,
  // GUI01091_保険サービス登録画面
  or10878Type: {
    processingMode: '',
    hokenSvList: [],
    hokenYmdList: [],
    insuranceServiceImportList: [],
    validityPeriodId: '',
  } as Or10878Type,
})

// 選択した行のindex
const selectedItemIndex = ref<number>(0)
// 選択した行のtableIndex
const selectedItemTableIndex = ref<number>(-1)
// 選択したColumnのキー
const selectedColKey = ref<string>('')

/** リサイズ状況 */
const resizing = reactive({
  isResizing: false,
  columnIndex: -1,
  startX: 0,
  startWidth: 0,
})
/** 各カラムの幅 */
const columnsWidth = reactive([
  { width: 88, minWidth: 88 }, // 0:課題番号
  { width: 240, minWidth: 240 }, // 1:生活全般の解決すべき課題(ニーズ)
  { width: 155, minWidth: 155 }, // 2:長期目標
  { width: 150, minWidth: 150 }, // 3:長期目標期間
  { width: 155, minWidth: 155 }, // 4:短期目標
  { width: 150, minWidth: 150 }, // 5:短期目標期間
  { width: 88, minWidth: 88 }, // 6:番号
  { width: 202, minWidth: 202 }, // 7:サービス内容
  { width: 78, minWidth: 78 }, // 8:※1
  { width: 202, minWidth: 202 }, // 9:サービス種別
  { width: 155, minWidth: 155 }, // 10:※2
  { width: 155, minWidth: 155 }, // 11:担当者
  { width: 111, minWidth: 111 }, // 12:頻度
  { width: 150, minWidth: 150 }, // 13:期間
  { width: 155, minWidth: 155 }, // 14:週間日課
  { width: 155, minWidth: 155 }, // 15:連動項目
])
// 援助内容 ヘッダColspan
const assistanceContentsColspan = computed(() => {
  let defaultColspan = 3 // サービス内容+頻度+期間
  if (columnFlag.value.showNumberFlg) {
    // 番号
    defaultColspan += 1
  }
  if (columnFlag.value.showRenkeiFlg) {
    // 連動項目
    defaultColspan += 1
  }
  if (columnFlag.value.showStar1Flg) {
    // ※1
    defaultColspan += 1
  }
  if (columnFlag.value.showServiceType) {
    // サービス種別
    defaultColspan += 1
  }
  if (columnFlag.value.showStar2Flg) {
    // ※2
    defaultColspan += 1
  }
  if (columnFlag.value.showManagerFlg) {
    // 担当者
    defaultColspan += 1
  }
  if (columnFlag.value.showWeekDailyRoutingFlg) {
    // 週間日課
    defaultColspan += 1
  }
  return defaultColspan
})
// *1のアイテム
const star1Items = ref<{ title: string; value: string }[]>([])
// 連動項目のアイテム
const renkeiItems = ref<{ title: string; value: string }[]>([])
const headers = ref([
  // 課題番号
  { title: t('label.issues-number'), align: 'left', key: 'kadaiNo' },
  // 生活全般の解決すべき課題(ニーズ)
  {
    title: t('label.life-whole-solution-issues'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL,
  },
  // 長期目標
  {
    title: t('label.long-term-goal'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL,
  },
  // 長期目標期間
  {
    title: t('label.care-plan2-period'),
    align: 'left',
    key: 'longTermGoalYmd',
    children: [
      // 年月日
      { title: t('label.yyyy-mm-dd'), align: 'left', key: 'choSYmd' },
      // 月日
      {
        title: t('label.mm-dd'),
        align: 'left',
        key: 'longTermGoalStartMd',
      },
    ],
  },
  // 短期目標
  {
    title: t('label.short-term-goal'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL,
  },
  // 短期目標期間
  {
    title: t('label.care-plan2-period'),
    align: 'left',
    key: 'shortTermGoalYmd',
    children: [
      // 年月日
      {
        title: t('label.yyyy-mm-dd'),
        align: 'left',
      },
      // 月日
      {
        title: t('label.mm-dd'),
        align: 'left',
      },
    ],
  },
  // 番号
  {
    title: t('label.number'),
    align: 'left',
    key: 'number',
  },
  // サービス内容
  {
    title: t('label.service-contents'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL,
  },
  // ※1
  {
    title: t('label.star-1'),
    align: 'left',
    key: 'hkyuKnj',
  },
  // サービス種別
  {
    title: t('label.service-kbn'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL,
    children: [
      // 保険サービス
      {
        title: t('label.insurance-service'),
        align: 'left',
      },
      // 利用票取込
      {
        title: t('label.use-slip-import'),
        align: 'left',
      },
    ],
  },
  // ※2
  {
    title: t('label.star-2'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL,
  },
  // 担当者
  {
    title: t('label.care-plan2-manager'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL,
    children: [
      // （取込）
      {
        title: t('label.import'),
        align: 'left',
      },
    ],
  },
  // 頻度
  {
    title: t('label.frequency'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL,
    children: [
      // 曜日
      {
        title: t('label.day-of-week'),
        align: 'left',
      },
      // （取込）
      {
        title: t('label.import'),
        align: 'left',
      },
    ],
  },
  // 期間
  {
    title: t('label.care-plan2-period'),
    align: 'left',
    key: 'periodYmd',
    children: [
      // 年月日
      {
        title: t('label.yyyy-mm-dd'),
        align: 'left',
      },
      // 月日
      {
        title: t('label.mm-dd'),
        align: 'left',
      },
    ],
  },
  // 週間日課
  {
    title: t('label.week-daily-routine'),
    align: 'left',
    key: 'weekDailyRoutine',
  },
])
// 削除データfilter
const tableDataFilter = computed(() => {
  return tableItem.value?.filter((i: Keikasyo2) => i.updateKbn !== UPDATE_KBN.DELETE) ?? []
})

const columnFlag = ref({
  // 計画対象期間表示表示フラグ
  showPlanningPeriodFlg: false,
  // 履歴表示フラグ
  showHistoryFlg: false,
  // 作成者表示フラグ
  showAuthorFlg: false,
  // 作成日表示フラグ
  showCreateDateFlg: false,
  // 保険サービス
  showInsServiceFlg: false,
  //'期間月日表示フラグ
  showPeriodMdFlg: false,
  // 番号表示フラグ
  showNumberFlg: false,
  // 連動項目表示フラグ
  showRenkeiFlg: false,
  // ※1ラベル表示フラグ
  showStar1Flg: false,
  // サービス種別表示フラグ
  showServiceType: false,
  // (サービス種別)保険サービス
  showServiceTypeInsServiceFlg: false,
  // (サービス種別)利用票取込
  showServiceTypeUseSlipImportFlg: false,
  // ※2
  showStar2Flg: false,
  // 担当者
  showManagerFlg: false,
  // 担当者（取込）
  showManagerImportFlg: false,
  // 曜日
  showDayOfWeekFlg: false,
  // 頻度（取込）
  showFreqImportFlg: false,
  // 週間日課
  showWeekDailyRoutingFlg: false,
  // 長期目標期間開始年月日テキストボックス
  showPeriodStartYmdFlg: false,
  // 期間～
  showPeriodLabelFlg: false,
  // 期間終了年月日
  showPeriodEndYmdFlg: false,
})
// 選択列一括更新機能の対象
const batchColUpdateWhiteList = ref<ColumnInfo[]>([
  { key: OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL, title: t('label.service-kbn') },
  { key: OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL, title: t('label.frequency') },
  { key: OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL, title: t('label.star-2') },
  { key: OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL, title: t('label.choukiKnj') },
  { key: OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL, title: t('label.tankiKnj') },
  { key: OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL, title: t('label.kikanKnj') },
])
// ケース取込項目機能の対象
const caseImportWhiteList = ref<ColumnInfo[]>([
  {
    key: OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL,
    title: t('label.life-whole-solution-issues'),
  },
  { key: OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL, title: t('label.long-term-goal') },
  { key: OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL, title: t('label.care-plan2-period') },
  { key: OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL, title: t('label.short-term-goal') },
  { key: OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL, title: t('label.care-plan2-period') },
  { key: OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL, title: t('label.service-contents') },
  { key: OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL, title: t('label.service-kbn') },
  { key: OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL, title: t('label.star-2') },
  { key: OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL, title: t('label.frequency') },
  { key: OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL, title: t('label.care-plan2-period') },
])
// 選択行ID
const selectedRows = ref<Keikasyo2[]>([])
const tableItem = ref<Keikasyo2[]>(localOneway.orX0073Oneway.tableItem)
let initFlg = false
/** 行追加非活性フラグ */
const or21735Disabled = computed(() => {
  if (props.onewayModelValue?.disabled) {
    return true
  }
  return false
})
watch(
  or21735Disabled,
  () => {
    Or21735Logic.state.set({
      uniqueCpId: or21735.value.uniqueCpId,
      state: {
        disabled: or21735Disabled.value,
      },
    })
  },
  { immediate: true }
)
/** 行挿入非活性フラグ */
const or21736Disabled = computed(() => {
  if (props.onewayModelValue?.disabled) {
    return true
  }
  if (selectedItemTableIndex.value < 0) {
    return true
  }
  return false
})
watch(
  or21736Disabled,
  () => {
    Or21736Logic.state.set({
      uniqueCpId: or21736.value.uniqueCpId,
      state: {
        disabled: or21736Disabled.value,
      },
    })
  },
  { immediate: true }
)
/** 行複写非活性フラグ */
const or21737Disabled = computed(() => {
  if (props.onewayModelValue?.disabled) {
    return true
  }
  if (selectedItemTableIndex.value < 0) {
    return true
  }
  return false
})
watch(
  or21737Disabled,
  () => {
    Or21737Logic.state.set({
      uniqueCpId: or21737.value.uniqueCpId,
      state: {
        disabled: or21737Disabled.value,
      },
    })
  },
  { immediate: true }
)
/** 選択列一括上書非活性フラグ */
const batchOverwriteDisable = computed(() => {
  if (props.onewayModelValue?.disabled) {
    return true
  }
  if (selectedItemTableIndex.value < 0) {
    return true
  }
  return false
})
/** 行削除非活性フラグ */
const or21738Disabled = computed(() => {
  if (props.onewayModelValue?.disabled) {
    return true
  }
  if (selectedItemTableIndex.value < 0) {
    return true
  }
  return false
})
watch(
  or21738Disabled,
  () => {
    Or21738Logic.state.set({
      uniqueCpId: or21738.value.uniqueCpId,
      state: {
        disabled: or21738Disabled.value,
      },
    })
  },
  { immediate: true }
)
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['onWeekDailuRoutingClick', 'update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/

function initColumnFlag() {
  const cpStyleFlg =
    (
      systemCommonsStore.searchSubsystemSettings(
        'cmn',
        systemCommonsStore.getSvJigyoId ?? ''
      ) as AuthzOtherCaremanagerType
    )?.planForm ?? OrX0042Const.CARE_PLAN_STYLE_FLG.HOME
  // ②保険サービス② 共通情報.計画書様式フラグ=2(居宅) 且つ 事業者コードは"30010", "50010", "23031", "43031", "61084", "71084","61104"の場合表示、以外は非表示
  if (
    !(
      cpStyleFlg === OrX0042Const.CARE_PLAN_STYLE_FLG.HOME &&
      OrX0042Const.OFFICE_CD_LIST.includes(systemCommonsStore.getSvJigyoCd ?? '')
    )
  ) {
    columnFlag.value.showInsServiceFlg = false
  } else {
    columnFlag.value.showInsServiceFlg = true
  }
  // (長期目標期間)月日指定: 共通情報.計画書(2)マスタ.期間の管理＝0（日付）の場合、非表示
  if (commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DATE) {
    columnFlag.value.showPeriodMdFlg = false
  } else {
    columnFlag.value.showPeriodMdFlg = true
  }
  // 番号: 共通情報.計画書(2)マスタ.番号フラグ＝０（表示しない）の場合、非表示
  // 共通情報.記録との連携＝1(記録との連携を行う)の場合、常時表示（共通情報.計画書(2)マスタ.番号フラグ＝０（表示しない）の場合でも表示する）
  if (
    commonInfoData.carePlan2Mastr.numberFlg === OrX0042Const.NUMBER_FLG.NOT_SHOW &&
    useCmnRouteCom().getInitialSettingMaster()?.cbKrkRenkeiFlg !==
      OrX0042Const.KIROKU_RENKEI_FLG.TRUE
  ) {
    columnFlag.value.showNumberFlg = false
  } else {
    columnFlag.value.showNumberFlg = true
  }
  if (
    useCmnRouteCom().getInitialSettingMaster()?.cbKrkRenkeiFlg !==
    OrX0042Const.KIROKU_RENKEI_FLG.TRUE
  ) {
    columnFlag.value.showRenkeiFlg = false
  } else {
    columnFlag.value.showRenkeiFlg = true
  }
  // 共通情報.システム略称＝'CMN' 又は 共通情報.計画書様式フラグ=2(居宅)の場合、表示
  // 以外非表示
  // TEMP add default CMN for Figma review
  if (
    (systemCommonsStore.getSystemAbbreviation ?? 'CMN') === OrX0042Const.SYS_ABBR.CMN ||
    cpStyleFlg === OrX0042Const.CARE_PLAN_STYLE_FLG.HOME
  ) {
    // ※1
    columnFlag.value.showStar1Flg = true
    // サービス種別
    columnFlag.value.showServiceType = true
    // ※2
    columnFlag.value.showStar2Flg = true
    // 曜日
    columnFlag.value.showDayOfWeekFlg = true
  } else {
    // ※1
    columnFlag.value.showStar1Flg = false
    // サービス種別
    columnFlag.value.showServiceType = false
    // ※2
    columnFlag.value.showStar2Flg = false
    // 曜日
    columnFlag.value.showDayOfWeekFlg = false
  }
  // 共通情報.計画書様式フラグ=2(居宅) 且つ
  // 事業者コードは"30010", "50010", "23031", "43031", "61084", "71084","61104"の場合表示
  // 、以外は非表示
  if (
    cpStyleFlg === OrX0042Const.CARE_PLAN_STYLE_FLG.HOME &&
    OrX0042Const.OFFICE_CD_LIST.includes(systemCommonsStore.getSvJigyoCd ?? '')
  ) {
    // (サービス種別)保険サービス
    columnFlag.value.showServiceTypeInsServiceFlg = true
    // (サービス種別)利用票取込
    columnFlag.value.showServiceTypeUseSlipImportFlg = true
  } else {
    // (サービス種別)保険サービス
    columnFlag.value.showServiceTypeInsServiceFlg = false
    // (サービス種別)利用票取込
    columnFlag.value.showServiceTypeUseSlipImportFlg = false
  }
  // 共通情報.システム略称＝'CMN' 又は 共通情報.計画書様式フラグ=2(居宅)の場合、非表示
  // TEMP add default CMN for Figma review
  if (
    (systemCommonsStore.getSystemAbbreviation ?? 'CMN') === OrX0042Const.SYS_ABBR.CMN ||
    cpStyleFlg === OrX0042Const.CARE_PLAN_STYLE_FLG.HOME
  ) {
    // 担当者
    columnFlag.value.showManagerFlg = false
    // 担当者（取込）
    columnFlag.value.showManagerImportFlg = false
    // 頻度（取込）
    columnFlag.value.showFreqImportFlg = false
    // 週間日課
    columnFlag.value.showWeekDailyRoutingFlg = false
  } else {
    // 担当者
    columnFlag.value.showManagerFlg = true
    // 担当者（取込）
    columnFlag.value.showManagerImportFlg = true
    // 頻度（取込）
    columnFlag.value.showFreqImportFlg = true
    // 週間日課
    columnFlag.value.showWeekDailyRoutingFlg = true
  }
  // 共通情報.計画書(2)マスタ.期間の管理＝１（文章）の場合、非表示
  if (commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DOC) {
    columnFlag.value.showPeriodStartYmdFlg = false
  } else {
    columnFlag.value.showPeriodStartYmdFlg = true
  }
  // 共通情報.計画書(2)マスタ.期間の管理＝１（文章）
  // 又は（期間の管理=日付かつ日付フラグ=3（?月?日）または2（?/?））の場合、非表示
  if (
    commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DOC ||
    (commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DATE &&
      [OrX0042Const.DATE_FLG.MM_MO_DD_DAY, OrX0042Const.DATE_FLG.MM_DD].includes(
        commonInfoData.carePlan2Mastr.dateFlg
      ))
  ) {
    // 期間～ラベル
    columnFlag.value.showPeriodLabelFlg = false
    // 終了年月日テキストボックス
    columnFlag.value.showPeriodEndYmdFlg = false
  } else {
    // 期間～ラベル
    columnFlag.value.showPeriodLabelFlg = true
    // 終了年月日テキストボックス
    columnFlag.value.showPeriodEndYmdFlg = true
  }
  // データ-ページング
  void resetMo01338OnewayItemLabel()
}
/**
 * 行選択
 *
 * @param index - 選択した行のindex
 *
 * @param tableIndex - 選択した行のtableIndex
 */
function onSelectRow(index: number, tableIndex: number) {
  selectedItemIndex.value = index
  selectedItemTableIndex.value = tableIndex
}
/**
 * 列が押下されました
 *
 * @param columnKey - 列のキー
 */
function onClickCol(columnKey?: string) {
  if (columnKey !== undefined) {
    selectedColKey.value = columnKey
  } else {
    selectedColKey.value = ''
  }
}

/** 「選択列一括上書ボタン」実行中かどうか */
const batchColUpdating = ref<boolean>(false)

/**
 * 「選択列一括上書ボタン」押下
 */
async function onClickbatchOverriteSc() {
  // 選択された列は選択列一括更新機能の対象外の場合
  // 選択された列は最終行の場合
  const keyList = batchColUpdateWhiteList.value.map((colInfo) => colInfo.key)
  const title = batchColUpdateWhiteList.value.find(
    (colInfo) => colInfo.key === selectedColKey.value
  )?.title
  if (
    selectedColKey.value === '' ||
    selectedColKey.value === undefined ||
    !keyList.includes(selectedColKey.value) ||
    selectedItemIndex.value === tableDataFilter.value.length - 1
  ) {
    // error
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-20834'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'blank',
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    return
  }

  // 現在選択されている列と選択行以下の列の背景色を変更
  batchColUpdating.value = true

  if (title !== null && title !== undefined) {
    // 選択された列の列名を取得できた場合
    // 以下のメッセージを表示i.cmn.20835
    const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-20835', [title, title]),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'normal3',
      // 第2ボタンボタンタイプ
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    if (rs.firstBtnClickFlg) {
      // はい：AC027-5 を実行
      confirmBatchSet()
    } else {
      cancelBatchSet()
    }
  } else {
    // 以下のメッセージを表示i.cmn.20835
    const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-20836'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'normal3',
      // 第2ボタンボタンタイプ
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    if (rs.firstBtnClickFlg) {
      // はい：AC027-5 を実行
      confirmBatchSet()
    } else {
      cancelBatchSet()
    }
  }
}
/**
 * 「行追加ボタン」押下
 */
async function addNewLine() {
  await nextTick()
  // 最終に新しい行を追加し、追加行を選択状態とする。
  const data: Keikasyo2 = {
    updateKbn: UPDATE_KBN.CREATE,
    tableIndex: tableItem.value.length,
    checked: { modelValue: false },
    // 長期期間
    choKikan: { value: '', startValue: '', endValue: '' },
    // 短期期間
    tanKikan: { value: '', startValue: '', endValue: '' },
    // 期間
    kikan: { value: '', startValue: '', endValue: '' },
    /** 具体的 */
    gutaiteki: { value: '' },
    /** 長期 */
    chouki: { value: '' },
    /** 短期 */
    tanki: { value: '' },
    /** 介護 */
    kaigo: { value: '' },
    /** サービス種 */
    svShu: { value: '' },
    /** 頻度 */
    hindo: { value: '' },
    //  サービス事業者名
    jigyoName: { value: '' },
    // 週間日課
    weekDailyRoute: { value: t('label.absence') },
    /** 課題番号 */
    kadaiNum: { value: '' },
    /** 介護番号 */
    kaigoNum: { value: '' },
    /** 給付文字 */
    hkyu: { modelValue: '' },
    /** 日課連動項目ID */
    nikkaId: { modelValue: '' },
  }
  tableItem.value.push(data)
  selectedItemIndex.value = tableDataFilter.value.length - 1
  selectedItemTableIndex.value = data.tableIndex
}

/**
 * GUI01023_課題整理総括取込画面確定ボタン押下
 *
 * @param data - 課題整理総括取込画面情報
 */
async function onClickIssuesConfirm(data: Or27362Type[]) {
  if (data.length > 0) {
    for (const item of data) {
      await nextTick()
      // 最終に新しい行を追加し、追加行を選択状態とする。
      const data: Keikasyo2 = {
        updateKbn: UPDATE_KBN.CREATE,
        tableIndex: tableItem.value.length,
        checked: { modelValue: false },
        // 長期期間
        choKikan: { value: '', startValue: '', endValue: '' },
        // 短期期間
        tanKikan: { value: '', startValue: '', endValue: '' },
        // 期間
        kikan: { value: '', startValue: '', endValue: '' },
        /** 具体的 */
        gutaiteki: { value: item.kadaiKnj ?? '' },
        /** 長期 */
        chouki: { value: '' },
        /** 短期 */
        tanki: { value: '' },
        /** 介護 */
        kaigo: { value: '' },
        /** サービス種 */
        svShu: { value: '' },
        /** 頻度 */
        hindo: { value: '' },
        //  サービス事業者名
        jigyoName: { value: '' },
        // 週間日課
        weekDailyRoute: { value: t('label.absence') },
        /** 課題番号 */
        kadaiNum: { value: '' },
        /** 介護番号 */
        kaigoNum: { value: '' },
        /** 給付文字 */
        hkyu: { modelValue: '' },
        /** 日課連動項目ID */
        nikkaId: { modelValue: '' },
      }
      addNewLineByData(data)
    }
  }
}

/**
 * 行追加
 *
 * @param data - 新行
 */
function addNewLineByData(data: Keikasyo2) {
  tableItem.value.push(data)
  selectedItemIndex.value = tableDataFilter.value.length - 1
  selectedItemTableIndex.value = data.tableIndex
}

/**
 * GUI01029_プロブレムリスト取込画面確定ボタン押下
 *
 * @param data - プロブレムリスト取込画面情報
 */
async function onClickPlConfirm(data: Or28720Type) {
  const problemList = data.problemList!
  for (const item of problemList) {
    await nextTick()
    // 最終に新しい行を追加し、追加行を選択状態とする。
    const newLine: Keikasyo2 = {
      updateKbn: UPDATE_KBN.CREATE,
      tableIndex: tableItem.value.length,
      checked: { modelValue: false },
      // 長期期間
      choKikan: { value: '', startValue: '', endValue: '' },
      // 短期期間
      tanKikan: { value: '', startValue: '', endValue: '' },
      // 期間
      kikan: { value: '', startValue: '', endValue: '' },
      /** 具体的 */
      gutaiteki: { value: '' },
      /** 長期 */
      chouki: { value: '' },
      /** 短期 */
      tanki: { value: '' },
      /** 介護 */
      kaigo: { value: '' },
      /** サービス種 */
      svShu: { value: '' },
      /** 頻度 */
      hindo: { value: '' },
      //  サービス事業者名
      jigyoName: { value: '' },
      // 週間日課
      weekDailyRoute: { value: t('label.absence') },
      /** 課題番号 */
      kadaiNum: { value: '' },
      /** 介護番号 */
      kaigoNum: { value: '' },
      /** 給付文字 */
      hkyu: { modelValue: '' },
      /** 日課連動項目ID */
      nikkaId: { modelValue: '' },
    }
    if (data.importItemList !== undefined) {
      //取込先項目番号が100、文字列を「生活先般の解決すべき課題（ニーズ)」に設定する。
      if (data.importItemList.includes(Or28720Const.IMPORT_LiFE_WHOLE_SOLUTION_ISSUES_NEEDS)) {
        newLine.gutaiteki.value = item.titleKnj
      }
      //取込先項目番号が101、文字列を「長期目標」に設定する。
      if (data.importItemList.includes(Or28720Const.IMPORT_LONG_TERM_GOAL)) {
        newLine.chouki.value = item.titleKnj
      }
      //取込先項目番号が102、文字列を「短期目標」に設定する。
      if (data.importItemList.includes(Or28720Const.IMPORT_SHORT_TERM_GOAL)) {
        newLine.tanki.value = item.titleKnj
      }
      //取込先項目番号が103、文字列を「サービス内容」に設定する。
      if (data.importItemList.includes(Or28720Const.IMPORT_SERVICE_CONTENTS)) {
        newLine.kaigo.value = item.titleKnj
      }
    }
    addNewLineByData(newLine)
  }
}

/**
 * GUI00957_サービス種別入力支援画面確定ボタン押下
 *
 * @param data - サービス種別入力支援画面情報
 */
function onClickSvKbnConfirm(data: Or50429Type) {
  if (
    data !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    //返却情報を画面の サービス種別に上書きする
    tableDataFilter.value[selectedItemIndex.value].svShu.value = data.serviceType.value!
    //返却情報を画面の ※２に上書きする
    tableDataFilter.value[selectedItemIndex.value].jigyoName.value = data.offerOffice.value!
  }
}

/**
 * GUI01022_担当者・頻度画面確定ボタン押下
 *
 * @param data - 担当者・頻度画面情報
 */
function onClickManagerImportConfirm(data: Or27043Type) {
  if (
    data !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    let hindoKnj = ''
    let hindoKnjNumber = 0
    if (data.dayOfWeekInfo !== undefined) {
      if (data.dayOfWeekInfo.dayOfWeek1 === OrX0042Const.YOBI.SELECT) {
        // 月(i18nの変数名が不正だが、値が正しいようです)
        hindoKnj += t('label.weekly-plan-day-short-sunday')
        hindoKnjNumber++
      }
      if (data.dayOfWeekInfo.dayOfWeek2 === OrX0042Const.YOBI.SELECT) {
        // 火
        hindoKnj += t('label.weekly-plan-day-short-monday')
        hindoKnjNumber++
      }
      if (data.dayOfWeekInfo.dayOfWeek3 === OrX0042Const.YOBI.SELECT) {
        // 水
        hindoKnj += t('label.weekly-plan-day-short-tuesday')
        hindoKnjNumber++
      }
      if (data.dayOfWeekInfo.dayOfWeek4 === OrX0042Const.YOBI.SELECT) {
        // 木
        hindoKnj += t('label.weekly-plan-day-short-wednesday')
        hindoKnjNumber++
      }
      if (data.dayOfWeekInfo.dayOfWeek5 === OrX0042Const.YOBI.SELECT) {
        // 金
        hindoKnj += t('label.weekly-plan-day-short-thursday')
        hindoKnjNumber++
      }
      if (data.dayOfWeekInfo.dayOfWeek6 === OrX0042Const.YOBI.SELECT) {
        // 土
        hindoKnj += t('label.weekly-plan-day-short-friday')
        hindoKnjNumber++
      }
      if (data.dayOfWeekInfo.dayOfWeek7 === OrX0042Const.YOBI.SELECT) {
        // 日
        hindoKnj += t('label.weekly-plan-day-short-saturday')
        hindoKnjNumber++
      }
    }
    if (commonInfoData.cks2HindoFlg === 0) {
      if (
        data.dayOfWeekInfo.startTime.value === OrX0042Const.FREQUENCY.TIME_ZERO &&
        data.dayOfWeekInfo.endTime.value === OrX0042Const.FREQUENCY.TIME_ZERO
      ) {
        //返却情報を画面の 頻度に上書きする
        tableDataFilter.value[selectedItemIndex.value].hindo.value = hindoKnj
      } else {
        //返却情報を画面の 頻度に上書きする
        tableDataFilter.value[selectedItemIndex.value].hindo.value =
          t('label.left-parentheses‌') +
          hindoKnj +
          data.dayOfWeekInfo.startTime.value +
          t('label.wavy-withoutblank') +
          data.dayOfWeekInfo.endTime.value +
          t('label.right-parentheses‌')
      }
    }
    if (commonInfoData.cks2HindoFlg === 1) {
      const result = t('btn.week') + hindoKnjNumber + t('label.times')
      //1週回/月回
      if (
        data.dayOfWeekInfo.startTime.value === OrX0042Const.FREQUENCY.TIME_ZERO &&
        data.dayOfWeekInfo.endTime.value === OrX0042Const.FREQUENCY.TIME_ZERO
      ) {
        //返却情報を画面の 頻度に上書きする
        tableDataFilter.value[selectedItemIndex.value].hindo.value = result
      } else {
        //返却情報を画面の 頻度に上書きする
        tableDataFilter.value[selectedItemIndex.value].hindo.value =
          result +
          t('label.left-parentheses‌') +
          data.dayOfWeekInfo.startTime.value +
          t('label.wavy-withoutblank') +
          data.dayOfWeekInfo.endTime.value +
          t('label.right-parentheses‌')
      }
    }
    if (commonInfoData.cks2HindoFlg === 2) {
      //随時
      //返却情報を画面の 頻度に上書きする
      tableDataFilter.value[selectedItemIndex.value].hindo.value = t('label.at-any-time')
    }
    //返却情報を画面の 担当者に上書きする
    if (data.selectedJobTypeIds !== undefined && data.selectedJobTypeIds.length > 0) {
      let svShuKnj = ''
      for (const item of data.selectedJobTypeIds) {
        svShuKnj += item.shokushuKnj + OrX0042Const.ENTER
      }
      tableDataFilter.value[selectedItemIndex.value].svShu.value = svShuKnj
    }
    //返却情報を画面の 週間日課に上書きする
    if (
      (data.selectedJobTypeIds !== undefined && data.selectedJobTypeIds.length > 0) ||
      hindoKnjNumber !== 0 ||
      data.dayOfWeekInfo.startTime.value !== OrX0042Const.FREQUENCY.TIME_ZERO ||
      data.dayOfWeekInfo.endTime.value !== OrX0042Const.FREQUENCY.TIME_ZERO
    ) {
      tableDataFilter.value[selectedItemIndex.value].weekDailyRoute.value = t('label.presence')
    } else {
      tableDataFilter.value[selectedItemIndex.value].weekDailyRoute.value = t('label.absence')
    }
  }
}

/**
 * GUI01032_表示順変更計画書（2）画面確定ボタン押下
 *
 * @param data - 表示順変更計画書（2）画面情報
 */
function onClickSortModifiedConfirm(data: Or10860Type) {
  if (data?.sectionList !== undefined && data.sectionList.length > 0) {
    // 削除データ
    const deleteData: Keikasyo2[] =
      tableItem.value?.filter((i: Keikasyo2) => i.updateKbn === UPDATE_KBN.DELETE) ?? []
    // 表示順変更データ
    const sortData: Keikasyo2[] = []
    data.sectionList.forEach((newOrder) => {
      sortData.push(tableDataFilter.value[Number(newOrder.orderBackup) - 1])
    })
    sortData.push(...deleteData)
    tableItem.value = sortData
  }
}

/**
 * GUI01016_「ケース取込の入力支援アイコンボタン」押下
 *
 * @param data - ケース取込の入力支援情報
 */
function onClickCaseImportConfirm(data: string) {
  if (
    data !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    switch (caseImportColKey.value) {
      // 生活全般の解決すべき課題(ニーズ)
      case OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].gutaiteki.value = data
        break
      // 長期目標
      case OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].chouki.value = data
        break
      // 長期目標(期間)
      case OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].choKikan.value = data
        break
      // 短期目標
      case OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].tanki.value = data
        break
      // 短期目標(期間)
      case OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].tanKikan.value = data
        break
      // サービス内容
      case OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].kaigo.value = data
        break
      // サービス種別
      case OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].svShu.value = data
        break
      // ※２
      case OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].jigyoName.value = data
        break
      // 頻度
      case OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].hindo.value = data
        break
      // 期間
      case OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].kikan.value = data
        break
    }
  }
}

// ケース取込カラム
const caseImportColKey = ref<string>('')

/**
 * ケース取込カラム設定
 *
 * @param columnKey - ケース取込カラム
 */
function resetCaseImportCol(columnKey: string) {
  if (columnKey !== undefined) {
    caseImportColKey.value = columnKey
  } else {
    caseImportColKey.value = ''
  }
}

/**
 * 「生活全般の解決すべき課題(ニーズ)アイコンボタン」押下
 * 「長期目標アイコンボタン」押下
 * 「長期目標期間アイコンボタン」押下
 * 「短期目標アイコンボタン」押下
 * 「短期目標期間アイコンボタン」押下
 * 「サービス内容アイコンボタン」押下
 * 「頻度アイコンボタン」押下
 * 「期間アイコンボタン」押下
 * GUI00937_入力支援［ケアマネ］画面「確定」ボタンで戻った場合
 *
 * @param data - 入力支援［ケアマネ］の入力支援情報
 */
function onClickCareManagerConfirm(data: Or51775Type) {
  if (
    data !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    switch (careManagerClickKey.value) {
      // 生活全般の解決すべき課題(ニーズ)
      case OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].gutaiteki.value = data.modelValue!
        break
      // 長期目標
      case OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].chouki.value = data.modelValue!
        break
      // 長期目標(期間)
      case OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].choKikan.value = data.modelValue!
        break
      // 短期目標
      case OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].tanki.value = data.modelValue!
        break
      // 短期目標(期間)
      case OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].tanKikan.value = data.modelValue!
        break
      // サービス内容
      case OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].kaigo.value = data.modelValue!
        break
      // 頻度
      case OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].hindo.value = data.modelValue!
        break
      // 期間
      case OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].kikan.value = data.modelValue!
        break
    }
  }
}

/**
 * GUI01018_「SDケアの入力支援アイコンボタン」押下
 *
 * @param data - SDケア作成の入力支援情報
 */
async function onClickCareCreateConfirm(data: Or53186Type) {
  if (
    data?.carePlan2List !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    const res = data.carePlan2List
    if (res.length > 0) {
      //返却リスト.取込モード:追加の場合、選択行に上書。
      if (data.importMode === OrX0042Const.IMPORT_MODE.ADD) {
        //生活全般の解決すべき課題(ニーズ)
        tableDataFilter.value[selectedItemIndex.value].gutaiteki.value =
          res[0].lifeWholeSolution ?? ''
        //長期目標
        tableDataFilter.value[selectedItemIndex.value].chouki.value = res[0].longTermGoal ?? ''
        //短期目標
        tableDataFilter.value[selectedItemIndex.value].tanki.value = res[0].shortTermGoal ?? ''
        //サービス内容
        tableDataFilter.value[selectedItemIndex.value].kaigo.value = res[0].serviceContents ?? ''
        //サービス種別
        if (res[0].serviceKbn !== undefined) {
          let svShuKnj = ''
          for (const item of res[0].serviceKbn as unknown as {
            serviceId: string
            serviceName: string
          }[]) {
            svShuKnj += item.serviceName + OrX0042Const.ENTER
          }
          tableDataFilter.value[selectedItemIndex.value].svShu.value = svShuKnj
        }
      }
      //返却リスト.取込モード:行追加の場合、新しい行に値設定。
      if (data.importMode === OrX0042Const.IMPORT_MODE.ADD_LINE) {
        await nextTick()
        // 最終に新しい行を追加し、追加行を選択状態とする。
        const data: Keikasyo2 = {
          updateKbn: UPDATE_KBN.CREATE,
          tableIndex: tableItem.value.length,
          checked: { modelValue: false },
          // 長期期間
          choKikan: { value: '', startValue: '', endValue: '' },
          // 短期期間
          tanKikan: { value: '', startValue: '', endValue: '' },
          // 期間
          kikan: { value: '', startValue: '', endValue: '' },
          /** 具体的 */
          gutaiteki: { value: res[0].lifeWholeSolution ?? '' },
          /** 長期 */
          chouki: { value: res[0].longTermGoal ?? '' },
          /** 短期 */
          tanki: { value: res[0].shortTermGoal ?? '' },
          /** 介護 */
          kaigo: { value: res[0].serviceContents ?? '' },
          /** サービス種 */
          svShu: { value: '' },
          /** 頻度 */
          hindo: { value: '' },
          //  サービス事業者名
          jigyoName: { value: '' },
          // 週間日課
          weekDailyRoute: { value: t('label.absence') },
          /** 課題番号 */
          kadaiNum: { value: '' },
          /** 介護番号 */
          kaigoNum: { value: '' },
          /** 給付文字 */
          hkyu: { modelValue: '' },
          /** 日課連動項目ID */
          nikkaId: { modelValue: '' },
        }
        //サービス種別
        if (res[0].serviceKbn !== undefined) {
          let svShuKnj = ''
          for (const item of res[0].serviceKbn as unknown as {
            serviceId: string
            serviceName: string
          }[]) {
            svShuKnj += item.serviceName + OrX0042Const.ENTER
          }
          data.svShu.value = svShuKnj
        }
        addNewLineByData(data)
      }
    }
  }
}

/**
 * GUI01031_「SDケアの入力支援アイコンボタン」押下
 *
 * @param data - SDケア選択画面の入力支援情報
 */
function onClickSdCareSelectConfirm(data: string) {
  if (data === Or51726Const.DEFAULT.SD_CARE_CREATE) {
    //SDケア選択画面で「SDケア作成」をクリックする場合
    openSdCareCreate()
  }
  if (data === Or51726Const.DEFAULT.MASTER_SIMPLE_REGISTRATION) {
    //SDケア選択画面で「マスタ簡易登録」をクリックする場合
    openSdCareMst()
  }
}

/**
 * GUI01015_課題と短期目標取込画面「確定」ボタン押下
 *
 * @param data - 課題と短期目標取込画面情報
 */
async function onClickIssuesTankiKnjConfirm(data: Or28567Type[]) {
  if (
    tableItem.value !== undefined &&
    tableItem.value.length >= 0 &&
    data !== undefined &&
    data.length > 0
  ) {
    //生活全般の解決すべき課題（ニーズ）,短期目標を設定する
    for (const item of data) {
      await nextTick()
      // 最終に新しい行を追加し、追加行を選択状態とする。
      const data: Keikasyo2 = {
        updateKbn: UPDATE_KBN.CREATE,
        tableIndex: tableItem.value.length,
        checked: { modelValue: false },
        // 長期期間
        choKikan: { value: '', startValue: '', endValue: '' },
        // 短期期間
        tanKikan: { value: '', startValue: '', endValue: '' },
        // 期間
        kikan: { value: '', startValue: '', endValue: '' },
        /** 具体的 */
        gutaiteki: { value: item.issues },
        /** 長期 */
        chouki: { value: '' },
        /** 短期 */
        tanki: { value: item.shortTermGoal },
        /** 介護 */
        kaigo: { value: '' },
        /** サービス種 */
        svShu: { value: '' },
        /** 頻度 */
        hindo: { value: '' },
        //  サービス事業者名
        jigyoName: { value: '' },
        // 週間日課
        weekDailyRoute: { value: t('label.absence') },
        /** 課題番号 */
        kadaiNum: { value: '' },
        /** 介護番号 */
        kaigoNum: { value: '' },
        /** 給付文字 */
        hkyu: { modelValue: '' },
        /** 日課連動項目ID */
        nikkaId: { modelValue: '' },
      }
      addNewLineByData(data)
    }
  }
}

/**
 * GUI01028_課題とサービス内容取込画面「確定」ボタン押下
 *
 * @param data - 課題とサービス内容取込画面情報
 */
async function onClickIssuesKaigoKnjConfirm(data: Or10475Type[]) {
  if (
    tableItem.value !== undefined &&
    tableItem.value.length >= 0 &&
    data !== undefined &&
    data.length > 0
  ) {
    //生活全般の解決すべき課題（ニーズ）,サービス内容を設定する
    for (const item of data) {
      await nextTick()
      // 最終に新しい行を追加し、追加行を選択状態とする。
      const data: Keikasyo2 = {
        updateKbn: UPDATE_KBN.CREATE,
        tableIndex: tableItem.value.length,
        checked: { modelValue: false },
        // 長期期間
        choKikan: { value: '', startValue: '', endValue: '' },
        // 短期期間
        tanKikan: { value: '', startValue: '', endValue: '' },
        // 期間
        kikan: { value: '', startValue: '', endValue: '' },
        /** 具体的 */
        gutaiteki: { value: item.kadaiKnj },
        /** 長期 */
        chouki: { value: '' },
        /** 短期 */
        tanki: { value: '' },
        /** 介護 */
        kaigo: { value: item.serviceMemoKnj },
        /** サービス種 */
        svShu: { value: '' },
        /** 頻度 */
        hindo: { value: '' },
        //  サービス事業者名
        jigyoName: { value: '' },
        // 週間日課
        weekDailyRoute: { value: t('label.absence') },
        /** 課題番号 */
        kadaiNum: { value: '' },
        /** 介護番号 */
        kaigoNum: { value: '' },
        /** 給付文字 */
        hkyu: { modelValue: '' },
        /** 日課連動項目ID */
        nikkaId: { modelValue: '' },
      }
      addNewLineByData(data)
    }
  }
}

/**
 * GUI01026_課題と目標取込画面「確定」ボタン押下
 *
 * @param data - 課題と目標取込画面情報
 */
async function onClickIssuesGoalImportConfirm(data: Or10477Type[]) {
  if (
    tableItem.value !== undefined &&
    tableItem.value.length >= 0 &&
    data !== undefined &&
    data.length > 0
  ) {
    //生活全般の解決すべき課題（ニーズ）,短期目標,長期目標,サービス内容を設定する
    for (const item of data) {
      await nextTick()
      // 最終に新しい行を追加し、追加行を選択状態とする。
      const data: Keikasyo2 = {
        updateKbn: UPDATE_KBN.CREATE,
        tableIndex: tableItem.value.length,
        checked: { modelValue: false },
        // 長期期間
        choKikan: { value: '', startValue: '', endValue: '' },
        // 短期期間
        tanKikan: { value: '', startValue: '', endValue: '' },
        // 期間
        kikan: { value: '', startValue: '', endValue: '' },
        /** 具体的 */
        gutaiteki: { value: item.kadaiKnj },
        /** 長期 */
        chouki: { value: item.choukiKnj },
        /** 短期 */
        tanki: { value: item.tankiKnj },
        /** 介護 */
        kaigo: { value: item.careKnj },
        /** サービス種 */
        svShu: { value: '' },
        /** 頻度 */
        hindo: { value: '' },
        //  サービス事業者名
        jigyoName: { value: '' },
        // 週間日課
        weekDailyRoute: { value: t('label.absence') },
        /** 課題番号 */
        kadaiNum: { value: '' },
        /** 介護番号 */
        kaigoNum: { value: '' },
        /** 給付文字 */
        hkyu: { modelValue: '' },
        /** 日課連動項目ID */
        nikkaId: { modelValue: '' },
      }
      addNewLineByData(data)
    }
  }
}

/**
 * GUI01027_フリーアセスメント取込画面「確定」ボタン押下
 *
 * @param data - フリーアセスメント取込画面情報
 */
async function onClickFreeAssessmentImportConfirm(data: Or28650Type[]) {
  if (
    tableItem.value !== undefined &&
    tableItem.value.length >= 0 &&
    data !== undefined &&
    data.length > 0
  ) {
    //課題立案様式設定マスタで設定した連動項目に返却データを設定する。
    for (const item of data) {
      await nextTick()
      // 最終に新しい行を追加し、追加行を選択状態とする。
      const data: Keikasyo2 = {
        updateKbn: UPDATE_KBN.CREATE,
        tableIndex: tableItem.value.length,
        checked: { modelValue: false },
        // 長期期間
        choKikan: { value: '', startValue: '', endValue: '' },
        // 短期期間
        tanKikan: { value: '', startValue: '', endValue: '' },
        // 期間
        kikan: { value: '', startValue: '', endValue: '' },
        /** 具体的 */
        gutaiteki: { value: item.issues },
        /** 長期 */
        chouki: { value: item.longTermGoals },
        /** 短期 */
        tanki: { value: item.shortTermGoal },
        /** 介護 */
        kaigo: { value: item.careContent },
        /** サービス種 */
        svShu: { value: '' },
        /** 頻度 */
        hindo: { value: '' },
        //  サービス事業者名
        jigyoName: { value: '' },
        // 週間日課
        weekDailyRoute: { value: t('label.absence') },
        /** 課題番号 */
        kadaiNum: { value: '' },
        /** 介護番号 */
        kaigoNum: { value: '' },
        /** 給付文字 */
        hkyu: { modelValue: '' },
        /** 日課連動項目ID */
        nikkaId: { modelValue: '' },
      }
      addNewLineByData(data)
    }
  }
}

/**
 * GUI01024_曜日取込画面「確定」ボタン押下
 *
 * @param data - 曜日取込画面情報
 */
function onClickYobiConfirm(data: { listData: boolean[]; textData: string }) {
  if (
    data !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    let hindoKnj = ''
    if (data.listData !== undefined && data.listData.length === 7) {
      if (data.listData[0]) {
        hindoKnj += t('label.weekly-plan-day-short-sunday')
      }
      if (data.listData[1]) {
        hindoKnj += t('label.weekly-plan-day-short-monday')
      }
      if (data.listData[2]) {
        hindoKnj += t('label.weekly-plan-day-short-tuesday')
      }
      if (data.listData[3]) {
        hindoKnj += t('label.weekly-plan-day-short-wednesday')
      }
      if (data.listData[4]) {
        hindoKnj += t('label.weekly-plan-day-short-thursday')
      }
      if (data.listData[5]) {
        hindoKnj += t('label.weekly-plan-day-short-friday')
      }
      if (data.listData[6]) {
        hindoKnj += t('label.weekly-plan-day-short-saturday')
      }
    }
    if (hindoKnj !== '') {
      //返却情報を画面の頻度に上書きする。
      tableDataFilter.value[selectedItemIndex.value].hindo.value = hindoKnj
    } else {
      //返却情報を画面の頻度に上書きする。
      tableDataFilter.value[selectedItemIndex.value].hindo.value = data.textData
    }
  }
}

/**
 * GUI02259_適用事業所の選択画面「確定」ボタン押下
 *
 * @param data - 適用事業所の選択画面情報
 */
function onClickTekiyoJigyoConfirm(data: ApplicableOfficeResponseInfo[]) {
  if (data) {
    openCaseImport()
  }
}

/**
 * 「行挿入ボタン」押下
 */
async function insertNewLine() {
  await nextTick()
  if (Array.isArray(tableItem.value) && tableItem.value.length > 0) {
    // 選択行の上に新しい行を追加し、追加行を選択状態とする。
    const insertLine: Keikasyo2 = {
      updateKbn: UPDATE_KBN.CREATE,
      tableIndex: tableItem.value?.length ?? 0,
      checked: { modelValue: false },
      // 長期期間
      choKikan: { value: '', startValue: '', endValue: '' },
      // 短期期間
      tanKikan: { value: '', startValue: '', endValue: '' },
      // 期間
      kikan: { value: '', startValue: '', endValue: '' },
      /** 具体的 */
      gutaiteki: { value: '' },
      /** 長期 */
      chouki: { value: '' },
      /** 短期 */
      tanki: { value: '' },
      /** 介護 */
      kaigo: { value: '' },
      /** サービス種 */
      svShu: { value: '' },
      /** 頻度 */
      hindo: { value: '' },
      //  サービス事業者名
      jigyoName: { value: '' },
      // 週間日課
      weekDailyRoute: { value: t('label.absence') },
      /** 課題番号 */
      kadaiNum: { value: '' },
      /** 介護番号 */
      kaigoNum: { value: '' },
      /** 給付文字 */
      hkyu: { modelValue: '' },
      /** 日課連動項目ID */
      nikkaId: { modelValue: '' },
    }
    const index =
      tableItem.value?.findIndex(
        (e: { tableIndex: number }) => e.tableIndex === selectedItemTableIndex.value
      ) ?? 0
    tableItem.value.splice(index, 0, insertLine)
    selectedItemIndex.value = index
    selectedItemTableIndex.value = insertLine.tableIndex
  }
}
/**
 * 「行複写ボタン」押下
 */
async function cpyLine() {
  await nextTick()
  if (Array.isArray(tableItem.value) && tableItem.value.length > 0) {
    // 選択行の情報をコピーして最終に新しい行を追加し、追加行を選択状態とする。
    const sourceLine = tableDataFilter.value.find(
      (item) => item.tableIndex === selectedItemTableIndex.value
    )
    if (sourceLine) {
      const cpyLine = { ...sourceLine }
      cpyLine.updateKbn = UPDATE_KBN.CREATE
      cpyLine.tableIndex = tableItem.value?.length ?? 0
      tableItem.value.push(cpyLine)
      selectedItemIndex.value = tableDataFilter.value.length - 1
      selectedItemTableIndex.value = cpyLine.tableIndex
    }
  }
}

/**
 * 「行削除ボタン」押下
 */
async function deleteLine() {
  // 以下のメッセージを表示: i.cmn.10219
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10219'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'normal3',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  })
  if (rs.firstBtnClickFlg) {
    // AC028-2 を実行
    await confirmDeleteLine()
  }
}
/**
 * 「行削除ボタン」押下,はい場合後続処理
 */
async function confirmDeleteLine() {
  await nextTick()
  const data = tableItem.value.find(
    (e: { tableIndex: number }) => e.tableIndex === selectedItemTableIndex.value
  )
  if (data !== undefined) {
    data.updateKbn = UPDATE_KBN.DELETE
    selectedItemIndex.value = -1
    selectedItemTableIndex.value = -1
  }
}
/**
 * 「選択列一括上書ボタン」押下: 現行列の値を取得し、選択行以下に反映する
 */
function confirmBatchSet() {
  // 期間の場合
  const srcLine = tableDataFilter.value[selectedItemIndex.value]
  switch (selectedColKey.value) {
    // 期間
    case OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].kikan.startValue = srcLine.kikan.startValue
        tableDataFilter.value[i].kikan.endValue = srcLine.kikan.endValue
        tableDataFilter.value[i].kikan.value = srcLine.kikan.value
      }
      break
    // 短期目標期間
    case OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].tanKikan.value = srcLine.tanKikan.value
        tableDataFilter.value[i].tanKikan.startValue = srcLine.tanKikan.startValue
        tableDataFilter.value[i].tanKikan.endValue = srcLine.tanKikan.endValue
      }
      break
    // 長期目標期間
    case OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].choKikan.value = srcLine.choKikan.value
        tableDataFilter.value[i].choKikan.startValue = srcLine.choKikan.startValue
        tableDataFilter.value[i].choKikan.endValue = srcLine.choKikan.endValue
      }
      break
    // ※2
    case OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].jigyoName.value = srcLine.jigyoName.value
      }
      break
    // 頻度
    case OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].hindo.value = srcLine.hindo.value
      }
      break
    // サービス種別
    case OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].svShu.value = srcLine.svShu.value
      }
      break
  }
  cancelBatchSet()
}

/**
 * 選択された列の背景色を削除する。
 */
function cancelBatchSet() {
  // 現在選択されている列と選択行以下の列の背景色を戻す
  batchColUpdating.value = false
}
/**
 * 「アセスメントの入力支援アイコンボタン」押下
 */
function onClickAssessment() {
  if (props.onewayModelValue?.disabled) {
    return
  }

  const carePlanMethod =
    (
      systemCommonsStore.searchSubsystemSettings(
        'cmn',
        systemCommonsStore.getSvJigyoId ?? ''
      ) as AuthzOtherCaremanagerType
    )?.assessmentMethod ?? ''
  // 共通情報.ケアプラン方式=1:包括的自立支援プログラム 且つ 計画書(2)マスタ.取込設定＝０:短期目標の場合
  if (
    carePlanMethod === OrX0042Const.CARE_PLAN_METHOD.COMPREHENSIVE_INDEPENDENCE_SUPPORT &&
    commonInfoData.carePlan2Mastr.importSettings === OrX0042Const.IMPORT_SETTINGS.TANKI_KNG
  ) {
    openIssuesTankiKnj()
  }
  // 共通情報.ケアプラン方式=1:包括的自立支援プログラム 且つ 計画書(2)マスタ.取込設定＝1:サービス内容の場合
  if (
    carePlanMethod === OrX0042Const.CARE_PLAN_METHOD.COMPREHENSIVE_INDEPENDENCE_SUPPORT &&
    commonInfoData.carePlan2Mastr.importSettings === OrX0042Const.IMPORT_SETTINGS.KAIGO_KNG
  ) {
    openIssuesKaigoKnj()
  }
  // TODO 共通情報.ケアプラン方式=2:居宅サービス計画の場合
  // 共通情報.ケアプラン方式=7:フリーアセスメントの場合
  if (carePlanMethod === OrX0042Const.CARE_PLAN_METHOD.FREE_ASSESSMENT) {
    openFreeAssessmentImport()
  }
  // 共通情報.ケアプラン方式=8:情報収集の場合
  if (carePlanMethod === OrX0042Const.CARE_PLAN_METHOD.COLLECT_INFO) {
    // GUI01007_課題検討用紙取込画面をポップアップで起動する。
    // 入力パラメータ: 共通情報.種別ID
    localOneway.or00586Oneway = {
      syubetsuId: systemCommonsStore.getSyubetu ?? '',
      svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      userId: useSystemCommonsStore().getUserId ?? '',
      kikanFlg: localOneway.orX0073Oneway.periodManageFlag ?? '',
      sc1Id: localOneway.orX0073Oneway.planTargetPeriodId,
      parentViewType: '2',
      mstKbn: '1',
    }
    Or00586Logic.state.set({
      uniqueCpId: or00586.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
  //共通情報.ケアプラン方式=9:インタラインの場合
  if (carePlanMethod === OrX0042Const.CARE_PLAN_METHOD.INTER_LINE) {
    openIssuesGoalImport()
  }
}
/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr00586 = computed(() => {
  return Or00586Logic.state.get(or00586.value.uniqueCpId)?.isOpen ?? false
})
/**
 * GUI01007_課題検討用紙取込画面閉じるの場合
 */
async function onOr00586Close() {
  // AC024と同じ（行追加）
  await addNewLine()
  // AC032-5の場合
  // 生活全般の解決すべき課題（ニーズ）,サービス内容を設定する
  const gutaitekiKnjArr = []
  for (const data of local.or00586.issuesConsiderBlankFormList) {
    gutaitekiKnjArr.push(data.kadaiKnj)
  }
  const gutaitekiKnj = gutaitekiKnjArr.join('\r\n')
  tableItem.value[tableItem.value.length - 1].gutaiteki.value = gutaitekiKnj
  tableItem.value[tableItem.value.length - 1].kaigo.value = gutaitekiKnj
}
/**
 * 「週間日課アイコンボタン」押下
 */
async function onWeekDailyRoutingClick() {
  // 選択行.週間日課が【無】の場合
  const line = tableDataFilter.value[selectedItemIndex.value]
  if (line.weekDailyRoute.value === t('label.absence')) {
    // 以下のメッセージを表示: i.cmn.11281
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11281'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'blank',
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    return
  }

  // 週間日課が【有】の場合
  // GUI01021_担当者入力支援画面をポップアップで起動する。
  onClickManager()
}

onBeforeMount(async () => {
  // 汎用コード取得
  await initCodes()
})

onMounted(async () => {
  initColumnFlag()

  // リサイズ
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)

  await nextTick()
})

onUnmounted(() => {
  // リサイズ
  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', onMouseUp)
})

/**
 * ページの再設定
 */
async function resetMo01338OnewayItemLabel() {
  await nextTick()
  if (tableDataFilter.value.length > 0) {
    // データ-ページング
    selectedRows.value = []
    local.mo00018.modelValue = false
    local.mo00018.modelValue = true
    allCheck(local.mo00018)
  }
}

/**
 * 全選択
 *
 * @param mo00018 - チェックボックス
 */
const allCheck = (mo00018: Mo00018Type) => {
  if (tableDataFilter.value.length > 0) {
    if (mo00018.modelValue) {
      tableItem.value?.forEach((item: Keikasyo2) => {
        item.checked = { modelValue: true }
      })
      selectedRows.value = tableItem.value
    } else {
      tableItem.value?.forEach((item: Keikasyo2) => {
        item.checked = { modelValue: false }
      })
      selectedRows.value = []
    }
  } else {
    local.mo00018.modelValue = false
  }
}

/**
 * 行選択チェックボックスチェックした時の処理
 */
const toggleSelectRow = () => {
  selectedRows.value = []
  tableItem.value?.forEach((item: Keikasyo2) => {
    if (item.checked.modelValue === true) {
      selectedRows.value.push(item)
    }
  })
  if (selectedRows.value.length === tableDataFilter.value.length) {
    local.mo00018.modelValue = true
  } else {
    local.mo00018.modelValue = false
  }
}

/**
 * 確認ダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - 確認ダイアログOneWayBind領域
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    or21814ResolvePromise = resolve
  })
}

/**
 * テーブルデータの初期化
 *
 * @param orX0073Oneway - データ
 */
function initData(orX0073Oneway: OrX0073OnewayType) {
  initFlg = true
  tableItem.value = orX0073Oneway.tableItem
  localOneway.orX0073Oneway.cpyFlg = orX0073Oneway.cpyFlg
  localOneway.orX0073Oneway.periodManageFlag = orX0073Oneway.periodManageFlag
  localOneway.orX0073Oneway.planTargetPeriodId = orX0073Oneway.planTargetPeriodId
  localOneway.orX0073Oneway.columnMinWidth = orX0073Oneway.columnMinWidth
  initFlg = false
}

/**
 * 「担当者アイコンボタン」押下
 *
 */
function onClickManager() {
  //モード：1
  local.or27235Type.mode = 1
  //利用者ID:共通情報.利用者ID
  local.or27235Type.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  //担当者：画面.担当者
  local.or27235Type.manager = tableDataFilter.value[selectedItemIndex.value].svShu.value
  // GUI01021_担当者入力支援ポップアップを開く
  Or27235Logic.state.set({
    uniqueCpId: or27235.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01021_担当者入力支援確定ボタン押下
 *
 * @param value - 担当者入力支援情報
 */
function handleOr27235Confirm(value: string) {
  tableDataFilter.value[selectedItemIndex.value].svShu.value = value
}

/**
 * GUI01021_担当者入力支援ポップアップの表示状態を返す
 */
const showDialogOr27235 = computed(() => {
  return Or27235Logic.state.get(or27235.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「PL取込の入力支援アイコンボタン」押下
 *
 */
function onClickPl() {
  if (props.onewayModelValue?.disabled) {
    return
  }

  // 共通情報.処理名
  localOneway.or28720OnewayType.processFirstName = commonInfoData.processFirstName
  // GUI01029_プロブレムリスト取込入力支援ポップアップを開く
  Or28720Logic.state.set({
    uniqueCpId: or28720.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01029_プロブレムリスト取込入力支援ポップアップの表示状態を返す
 */
const showDialogOr28720 = computed(() => {
  return Or28720Logic.state.get(or28720.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「課題整理総括の入力支援アイコンボタン」押下
 *
 */
function onClickIssues() {
  if (props.onewayModelValue?.disabled) {
    return
  }

  // 共通情報.施設ID
  localOneway.or27362OnewayType.organizingIssuesImportType.shisetuId =
    systemCommonsStore.getShisetuId ?? ''
  // 共通情報.利用者ID
  localOneway.or27362OnewayType.organizingIssuesImportType.userId =
    systemCommonsStore.getUserSelectSelfId() ?? ''
  // 共通情報.事業所ID
  localOneway.or27362OnewayType.organizingIssuesImportType.svJigyoId =
    systemCommonsStore.getSvJigyoId ?? ''
  // 共通情報.種別ID
  localOneway.or27362OnewayType.organizingIssuesImportType.syubetsuId =
    systemCommonsStore.getSyubetu ?? ''
  // GUI01023_課題整理総括取込画面入力支援ポップアップを開く
  Or27362Logic.state.set({
    uniqueCpId: or27362.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01023_課題整理総括取込画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr27362 = computed(() => {
  return Or27362Logic.state.get(or27362.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「サービス種別アイコンボタン」「(サービス種別)利用票取込アイコンボタン」「※2アイコンボタン」押下
 *
 * @param screenDisplayMode - 画面表示モード
 */
function onClickSvKbn(screenDisplayMode: string) {
  // 画面表示モード 1：サービス種別
  localOneway.or50429OnewayType.screenDisplayMode = screenDisplayMode
  // サービス種別：画面.サービス種別
  localOneway.or50429OnewayType.serviceKind =
    tableDataFilter.value[selectedItemIndex.value].svShu.value
  // 事業所名：画面.※２
  localOneway.or50429OnewayType.officeName =
    tableDataFilter.value[selectedItemIndex.value].jigyoName.value
  //「(サービス種別)利用票取込アイコンボタン」押下
  if (screenDisplayMode === '3') {
    // 基準年月：画面.作成年月
    localOneway.or50429OnewayType.createDate =
      systemCommonsStore.getSystemDate?.substring(
        0,
        systemCommonsStore.getSystemDate?.lastIndexOf('/')
      ) ?? ''
  }
  // GUI00957_サービス種別入力支援入力支援ポップアップを開く
  Or50429Logic.state.set({
    uniqueCpId: or50429.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00957_サービス種別入力支援ポップアップの表示状態を返す
 */
const showDialogOr50429 = computed(() => {
  return Or50429Logic.state.get(or50429.value.uniqueCpId)?.isOpen ?? false
})

// 担当者・頻度フラグ
const managerImportFlg = ref<boolean>(false)

/**
 * 「担当者（取込）アイコンボタン」押下
 *
 */
function onClickManagerImport() {
  //計画書（２）行データID
  const itemKs22Id = tableDataFilter.value[selectedItemIndex.value].ks22Id
  const selectedJobType = []
  if (
    tableDataFilter.value[selectedItemIndex.value].tantoList !== undefined &&
    tableDataFilter.value[selectedItemIndex.value].tantoList!.length > 0
  ) {
    for (const item of tableDataFilter.value[selectedItemIndex.value].tantoList ?? []) {
      if (item.ks22Id === itemKs22Id) {
        selectedJobType.push({ shokushuId: item.shokushuId })
      }
    }
  }
  //職種（担当者）リスト
  local.or27043Type.selectedJobTypeIds = selectedJobType
  if (
    tableDataFilter.value[selectedItemIndex.value].yobiList !== undefined &&
    tableDataFilter.value[selectedItemIndex.value].yobiList!.length > 0
  ) {
    for (const item of tableDataFilter.value[selectedItemIndex.value].yobiList ?? []) {
      if (item.ks22Id === itemKs22Id) {
        if (item.youbi !== undefined) {
          const rest = item.youbi.split('')
          if (rest.length === 7) {
            local.or27043Type.dayOfWeekInfo.dayOfWeek1 = rest[0] // 月
            local.or27043Type.dayOfWeekInfo.dayOfWeek2 = rest[1] // 火
            local.or27043Type.dayOfWeekInfo.dayOfWeek3 = rest[2] // 水
            local.or27043Type.dayOfWeekInfo.dayOfWeek4 = rest[3] // 木
            local.or27043Type.dayOfWeekInfo.dayOfWeek5 = rest[4] // 金
            local.or27043Type.dayOfWeekInfo.dayOfWeek6 = rest[5] // 土
            local.or27043Type.dayOfWeekInfo.dayOfWeek7 = rest[6] // 日
          }
        }
        if (item.kaishiJikan !== undefined) {
          // 計画書（２）サービス、曜日情報.開始時間
          local.or27043Type.dayOfWeekInfo.startTime.value = item.kaishiJikan
        }
        if (item.shuuryouJikan !== undefined) {
          // 計画書（２）サービス、曜日情報.終了時間
          local.or27043Type.dayOfWeekInfo.endTime.value = item.shuuryouJikan
        }
      }
    }
  }
  // GUI01022_担当者・頻度画面入力支援ポップアップを開く
  Or27043Logic.state.set({
    uniqueCpId: or27043.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01022_担当者・頻度画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr27043 = computed(() => {
  return Or27043Logic.state.get(or27043.value.uniqueCpId)?.isOpen ?? false
})

// 表示順変更計画書（2）フラグ
const sortModifiedFlg = ref<boolean>(false)

/**
 * 「表示順変更アイコンボタン」押下
 *
 */
function onClickSortModified() {
  if (props.onewayModelValue?.disabled) {
    return
  }

  //共通情報.計画書様式フラグ
  localOneway.or10860OnewayType.carePlanStyle = Number(OrX0042Const.SYS_ABBR.CPN)
  //共通情報.計画書(2)マスタ.番号フラグ
  localOneway.or10860OnewayType.numberFlag = Number(commonInfoData.carePlan2Mastr.numberFlg)
  //画面．計画書（２）データリスト
  const sectionList: sectionItem[] = []
  let num = 1
  for (const item of tableDataFilter.value) {
    const section: sectionItem = {
      displayOrder: num + '',
      orderBackup: num + '',
      issuesNumber: item.kadaiNum.value,
      lifeWholeSolutionIssuesNeeds: item.gutaiteki.value,
      longTermGoal: item.chouki.value,
      longTermGoalPeriod: '',
      shortTermGoal: item.tanki.value,
      shortTermGoalPeriod: '',
      number: item.kaigoNum.value,
      serviceContents: item.kaigo.value,
      manager: item.svShu.value,
      frequency: item.hindo.value,
      period: '',
      weekDailyRoutine: item.weekDailyRoute.value,
      asterisk1: t('label.circle'),
      serviceType: item.svShu.value,
      asterisk2: item.jigyoName.value,
    }
    if (columnFlag.value.showPeriodStartYmdFlg) {
      //長期目標期間
      section.longTermGoalPeriod = item.choKikan.startValue
      //短期目標期間
      section.shortTermGoalPeriod = item.tanKikan.startValue
      //期間
      section.period = item.kikan.startValue
    }
    if (columnFlag.value.showPeriodLabelFlg && columnFlag.value.showPeriodEndYmdFlg) {
      //長期目標期間
      section.longTermGoalPeriod =
        section.longTermGoalPeriod + t('label.wave-dash') + item.choKikan.endValue
      //短期目標期間
      section.shortTermGoalPeriod =
        section.shortTermGoalPeriod + t('label.wave-dash') + item.tanKikan.endValue
      //期間
      section.period = section.period + t('label.wave-dash') + item.kikan.endValue
    }
    //長期目標期間
    section.longTermGoalPeriod = section.longTermGoalPeriod + item.choKikan.value
    //短期目標期間
    section.shortTermGoalPeriod = section.shortTermGoalPeriod + item.tanKikan.value
    //期間
    section.period = section.period + item.kikan.value
    sectionList.push(section)
    num++
  }
  local.or10860Type.sectionList = sectionList
  // GUI01032_表示順変更計画書（2）入力支援ポップアップを開く
  Or10860Logic.state.set({
    uniqueCpId: or10860.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01032_表示順変更計画書（2）画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr10860 = computed(() => {
  return Or10860Logic.state.get(or10860.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「ケース取込の入力支援アイコンボタン」押下
 *
 */
function onClickCaseImport() {
  if (props.onewayModelValue?.disabled) {
    return
  }

  //ポップアップ画面起動の判断。
  //選択された取込項目の種類は「テキストエリア」の場合、処理続行。以外の場合、処理終了。
  const keyList = caseImportWhiteList.value.map((colInfo) => colInfo.key)
  if (
    caseImportColKey.value === '' ||
    caseImportColKey.value === undefined ||
    !keyList.includes(caseImportColKey.value)
  ) {
    return
  }
  //共通情報.システム略称≠'CPN'の場合
  if ((systemCommonsStore.getSystemAbbreviation ?? '') !== OrX0042Const.SYS_ABBR.CPN) {
    //GUI02259_適用事業所の選択画面をポップアップで起動する。
    // 共通情報.法人ID
    // 共通情報.施設ID
    localOneway.or22951OnewayType.selectList = [
      {
        houjinId: systemCommonsStore.getHoujinId ?? '',
        shisetsuId: systemCommonsStore.getShisetuId ?? '',
      },
    ]
    // 共通情報.事業所ID
    // 共通情報.システムコート
    localOneway.or22951OnewayType.systemCode = commonInfoData.syscd
    // 共通情報.ログイン情報.職員ID
    // 入力支援ポップアップを開く
    Or22951Logic.state.set({
      uniqueCpId: or22951.value.uniqueCpId,
      state: { isOpen: true },
    })
  } else {
    //共通情報.システム略称='CPN'の場合
    //サービス事業者コード情報を取得する。
    openCaseImport()
  }
}

/**
 * GUI02259_適用事業所の選択画面ポップアップの表示状態を返す
 */
const showDialogOr22951 = computed(() => {
  return Or22951Logic.state.get(or22951.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01016_ケース一覧画面をポップアップで起動する。
 *
 */
function openCaseImport() {
  // 共通情報.法人ID
  localOneway.or28256OnewayType.houjinId = systemCommonsStore.getHoujinId ?? ''
  // 共通情報.施設ID
  localOneway.or28256OnewayType.sisetsuId = systemCommonsStore.getShisetuId ?? ''
  // 共通情報.利用者ID
  localOneway.or28256OnewayType.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  // 画面抽出期間開始日
  localOneway.or28256OnewayType.kaisihi.value =
    tableDataFilter.value[selectedItemIndex.value].kikan.startValue ?? ''
  // 画面抽出期間終了日
  localOneway.or28256OnewayType.syuuryouhi.value =
    tableDataFilter.value[selectedItemIndex.value].kikan.endValue ?? ''
  // 選択されている日誌系マスタID
  localOneway.or28256OnewayType.sheetno = '1'
  // 選択されている分類コード
  localOneway.or28256OnewayType.bunruiCd = '1'
  // 選択されている項目番号
  localOneway.or28256OnewayType.koumokuno = 0
  // 共通情報.システムコード
  localOneway.or28256OnewayType.syscd = commonInfoData.syscd

  localOneway.or28256OnewayType.systemYmd.value = useSystemCommonsStore().getSystemDate ?? ''
  localOneway.or28256OnewayType.defSvJigyoId = useSystemCommonsStore().getSvJigyoId ?? ''

  switch (caseImportColKey.value) {
    // 生活全般の解決すべき課題(ニーズ)
    case OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL:
      // 取込項目のタイトル
      localOneway.or28256OnewayType.targetItem = t('label.life-whole-solution-issues')
      // 取込項目のデータ
      localOneway.or28256OnewayType.caseInformation =
        tableDataFilter.value[selectedItemIndex.value].gutaiteki.value
      break
    // 長期目標
    case OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL:
      // 取込項目のタイトル
      localOneway.or28256OnewayType.targetItem = t('label.long-term-goal')
      // 取込項目のデータ
      localOneway.or28256OnewayType.caseInformation =
        tableDataFilter.value[selectedItemIndex.value].chouki.value
      break
    // 長期目標(期間)
    case OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL:
      // 取込項目のタイトル
      localOneway.or28256OnewayType.targetItem = t('label.care-plan2-period')
      // 取込項目のデータ
      localOneway.or28256OnewayType.caseInformation =
        tableDataFilter.value[selectedItemIndex.value].choKikan.value
      break
    // 短期目標
    case OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL:
      // 取込項目のタイトル
      localOneway.or28256OnewayType.targetItem = t('label.short-term-goal')
      // 取込項目のデータ
      localOneway.or28256OnewayType.caseInformation =
        tableDataFilter.value[selectedItemIndex.value].tanki.value
      break
    // 短期目標(期間)
    case OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL:
      // 取込項目のタイトル
      localOneway.or28256OnewayType.targetItem = t('label.care-plan2-period')
      // 取込項目のデータ
      localOneway.or28256OnewayType.caseInformation =
        tableDataFilter.value[selectedItemIndex.value].tanKikan.value
      break
    // サービス内容
    case OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL:
      // 取込項目のタイトル
      localOneway.or28256OnewayType.targetItem = t('label.service-contents')
      // 取込項目のデータ
      localOneway.or28256OnewayType.caseInformation =
        tableDataFilter.value[selectedItemIndex.value].kaigo.value
      break
    // サービス種別
    case OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL:
      // 取込項目のタイトル
      localOneway.or28256OnewayType.targetItem = t('label.service-kbn')
      // 取込項目のデータ
      localOneway.or28256OnewayType.caseInformation =
        tableDataFilter.value[selectedItemIndex.value].svShu.value
      break
    // ※２
    case OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL:
      // 取込項目のタイトル
      localOneway.or28256OnewayType.targetItem = t('label.star-2')
      // 取込項目のデータ
      localOneway.or28256OnewayType.caseInformation =
        tableDataFilter.value[selectedItemIndex.value].jigyoName.value
      break
    // 頻度
    case OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL:
      // 取込項目のタイトル
      localOneway.or28256OnewayType.targetItem = t('label.frequency')
      // 取込項目のデータ
      localOneway.or28256OnewayType.caseInformation =
        tableDataFilter.value[selectedItemIndex.value].hindo.value
      break
    // 期間
    case OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL:
      // 取込項目のタイトル
      localOneway.or28256OnewayType.targetItem = t('label.period')
      // 取込項目のデータ
      localOneway.or28256OnewayType.caseInformation =
        tableDataFilter.value[selectedItemIndex.value].kikan.value
      break
  }
  // GUI01016_ケース一覧入力支援ポップアップを開く
  Or28256Logic.state.set({
    uniqueCpId: or28256.value.uniqueCpId,
    state: {
      isOpen: true,
      items: [],
    },
  })
}

/**
 * GUI01016_ケース一覧画面ポップアップの表示状態を返す
 */
const showDialogOr28256 = computed(() => {
  return Or28256Logic.state.get(or28256.value.uniqueCpId)?.isOpen ?? false
})

//入力支援［ケアマネ］画面押下区分
const careManagerClickKey = ref<string>('')
// 入力支援［ケアマネ］フラグ
const careManagerFlg = ref<boolean>(false)

/**
 * 「生活全般の解決すべき課題(ニーズ)アイコンボタン」押下
 * 「長期目標アイコンボタン」押下
 * 「長期目標期間アイコンボタン」押下
 * 「短期目標アイコンボタン」押下
 * 「短期目標期間アイコンボタン」押下
 * 「サービス内容アイコンボタン」押下
 * 「頻度アイコンボタン」押下
 * 「期間アイコンボタン」押下
 *
 * @param colKey - 入力支援［ケアマネ］画面押下区分
 */
function onClickCareManager(colKey: string) {
  careManagerClickKey.value = colKey
  // 画面ID:"GUI01014"
  localOneway.or51775OnewayType.screenId = OrX0042Const.SCREEN_ID
  // 分類ID:""
  // 大分類CD:820
  localOneway.or51775OnewayType.t1Cd = OrX0042Const.CLASSIFICATION_CD.BIG_820
  // 小分類CD:0
  localOneway.or51775OnewayType.t3Cd = OrX0042Const.CLASSIFICATION_CD.SMALL_0
  // テーブル名:"cpn_tuc_cks22"
  localOneway.or51775OnewayType.tableName = OrX0042Const.TABLE_NAME
  // アセスメント方式:親画面.アセスメント方式
  localOneway.or51775OnewayType.assessmentMethod =
    (
      systemCommonsStore.searchSubsystemSettings(
        'cmn',
        systemCommonsStore.getSvJigyoId ?? ''
      ) as AuthzOtherCaremanagerType
    )?.assessmentMethod ?? ''
  // 利用者ID:共通情報.利用者ID
  localOneway.or51775OnewayType.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  switch (careManagerClickKey.value) {
    // 生活全般の解決すべき課題(ニーズ)
    case OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL:
      // タイトル:"生活全般の解決すべき課題（ニーズ）"
      localOneway.or51775OnewayType.title = t('label.life-whole-solution-issues-needs')
      // 中分類CD:1
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_1
      // カラム名:"gutaiteki_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].gutaiteki.value
      break
    // 長期目標
    case OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL:
      // タイトル:"長期目標"
      localOneway.or51775OnewayType.title = t('label.long-term-goal')
      // 中分類CD:2
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_2
      // カラム名:"chouki_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].chouki.value
      break
    // 長期目標(期間)
    case OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL:
      //タイトル:"（期間）"
      localOneway.or51775OnewayType.title = t('label.care-plan2-period')
      // 中分類CD:3
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_3
      //カラム名:"cho_kikan_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].choKikan.value
      break
    // 短期目標
    case OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL:
      //タイトル:"短期目標"
      localOneway.or51775OnewayType.title = t('label.short-term-goal')
      // 中分類CD:4
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_4
      //カラム名:"tanki_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.TANKI_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].tanki.value
      break
    // 短期目標(期間)
    case OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL:
      //タイトル:"（期間）"
      localOneway.or51775OnewayType.title = t('label.care-plan2-period')
      // 中分類CD:5
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_5
      //カラム名:"tan_kikan_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].tanKikan.value
      break
    // サービス内容
    case OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL:
      //タイトル:"サービス内容"
      localOneway.or51775OnewayType.title = t('label.service-contents')
      // 中分類CD:6
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_6
      //カラム名:"kaigo_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.KAIGO_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].kaigo.value
      break
    // 頻度
    case OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL:
      //タイトル:"頻度"
      localOneway.or51775OnewayType.title = t('label.frequency')
      // 中分類CD:10
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_10
      //カラム名:"hindo_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.HINDO_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].hindo.value
      break
    // 期間
    case OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL:
      //タイトル:"期間"
      localOneway.or51775OnewayType.title = t('label.period')
      // 中分類CD:11
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_11
      //カラム名:"kikan_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.KIKAN_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].kikan.value
      break
  }
  // GUI00937_入力支援［ケアマネ］入力支援ポップアップを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00937_入力支援［ケアマネ］ポップアップの表示状態を返す
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「SDケアの入力支援アイコンボタン」押下
 *
 */
function onClickSdCare() {
  if (props.onewayModelValue?.disabled) {
    return
  }

  local.or51726Type = { selectResult: '' }
  // GUI01031_SDケア選択入力支援ポップアップを開く
  Or51726Logic.state.set({
    uniqueCpId: or51726.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01031_SDケア選択画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr51726 = computed(() => {
  return Or51726Logic.state.get(or51726.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01019_SDケアマスタ簡易登録画面をポップアップで起動する。
 *
 */
function openSdCareMst() {
  // 共通情報.法人ID
  localOneway.or54215OnewayType.houjinId = systemCommonsStore.getHoujinId ?? ''
  // 共通情報.施設ID
  localOneway.or54215OnewayType.shisetuId = systemCommonsStore.getShisetuId ?? ''
  // 共通情報.事業者ID
  localOneway.or54215OnewayType.svJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  // 画面.計画書（２）データ部.長期目標
  localOneway.or54215OnewayType.choukiKnj =
    tableDataFilter.value[selectedItemIndex.value].chouki.value
  // 画面.計画書（２）データ部.生活全般の解決すべき課題（ニーズ）
  localOneway.or54215OnewayType.needsKnj =
    tableDataFilter.value[selectedItemIndex.value].gutaiteki.value
  // 画面.計画書（２）データ部.サービス内容
  localOneway.or54215OnewayType.serviceKnj =
    tableDataFilter.value[selectedItemIndex.value].kaigo.value
  // 画面.計画書（２）データ部.短期目標
  localOneway.or54215OnewayType.tankiKnj =
    tableDataFilter.value[selectedItemIndex.value].tanki.value
  // 入力支援ポップアップを開く
  Or54215Logic.state.set({
    uniqueCpId: or54215.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01019_SDケアマスタ簡易登録画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr54215 = computed(() => {
  return Or54215Logic.state.get(or54215.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01018_SDケア作成画面をポップアップで起動する。
 *
 */
function openSdCareCreate() {
  // 画面.計画書(2)ID
  localOneway.or53186OnewayType.ks21Id = props.onewayModelValue?.ks21Id ?? ''
  // 画面.有効期間ID
  localOneway.or53186OnewayType.termid = props.onewayModelValue?.termId ?? ''
  // API取得システム基準日
  localOneway.or53186OnewayType.systemDate = systemCommonsStore.getSystemDate ?? ''
  // 入力支援ポップアップを開く
  Or53186Logic.state.set({
    uniqueCpId: or53186.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01018_SDケア作成画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr53186 = computed(() => {
  return Or53186Logic.state.get(or53186.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01015_課題と短期目標取込画面をポップアップで起動する。
 *
 */
function openIssuesTankiKnj() {
  // 共通情報.種別ID
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.syubetsuId =
    systemCommonsStore.getSyubetu ?? ''
  // 共通情報.利用者ID
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.userId =
    systemCommonsStore.getUserSelectSelfId() ?? ''
  // 共通情報.施設ID
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.shisetuId =
    systemCommonsStore.getShisetuId ?? ''
  // 共通情報.事業者ID
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.svJigyoId =
    systemCommonsStore.getSvJigyoId ?? ''
  // 共通情報.アセスメント方式
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.cpnFlg =
    (
      systemCommonsStore.searchSubsystemSettings(
        'cmn',
        systemCommonsStore.getSvJigyoId ?? ''
      ) as AuthzOtherCaremanagerType
    )?.assessmentMethod ?? ''
  // 共通情報.期間管理フラグ
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.periodManagementFlag =
    localOneway.orX0073Oneway.periodManageFlag
  // 入力支援ポップアップを開く
  Or28567Logic.state.set({
    uniqueCpId: or28567.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01015_課題と短期目標取込画面ポップアップの表示状態を返す
 */
const showDialogOr28567 = computed(() => {
  return Or28567Logic.state.get(or28567.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01028_課題とサービス内容取込画面をポップアップで起動する。
 *
 */
function openIssuesKaigoKnj() {
  // 共通情報.種別ID
  localOneway.or10475OnewayType.issuesAndServiceContenImportType.syubetsuId =
    systemCommonsStore.getSyubetu ?? ''
  // 入力支援ポップアップを開く
  Or10475Logic.state.set({
    uniqueCpId: or10475.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01028_課題とサービス内容取込画面ポップアップの表示状態を返す
 */
const showDialogOr10475 = computed(() => {
  return Or10475Logic.state.get(or10475.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01026_課題と目標取込画面をポップアップで起動する。
 *
 */
function openIssuesGoalImport() {
  // 共通情報.種別ID
  localOneway.or10477OnewayType.issuesOrGoalImportType.syubetsuId =
    systemCommonsStore.getSyubetu ?? ''
  // 入力支援ポップアップを開く
  Or10477Logic.state.set({
    uniqueCpId: or10477.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01026_課題と目標取込画面ポップアップの表示状態を返す
 */
const showDialogOr10477 = computed(() => {
  return Or10477Logic.state.get(or10477.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01027_フリーアセスメント取込画面をポップアップで起動する。
 *
 */
function openFreeAssessmentImport() {
  // 共通情報.種別ID
  localOneway.or28650OnewayType.importFreeAssessmentSelectType.syubetsuId =
    systemCommonsStore.getSyubetu ?? ''
  // 入力支援ポップアップを開く
  Or28650Logic.state.set({
    uniqueCpId: or28650.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01027_フリーアセスメント取込画面ポップアップの表示状態を返す
 */
const showDialogOr28650 = computed(() => {
  return Or28650Logic.state.get(or28650.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「曜日アイコンボタン」押下
 *
 */
function onClickYobi() {
  // GUI01024_曜日取込入力支援ポップアップを開く
  Or28582Logic.state.set({
    uniqueCpId: or28582.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01024_曜日取込画面ポップアップの表示状態を返す
 */
const showDialogOr28582 = computed(() => {
  return Or28582Logic.state.get(or28582.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「保険サービス登録入力支援アイコンボタ」押下
 */
async function onClickInsServiceInputHelp() {
  // システム基準日を取得する
  const param: ValidPeriodIdSelectInEntity = {
    // 法人ID
    houjinId: systemCommonsStore.getHoujinId ?? '',
    // 施設ID
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    // 共通情報.利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    // 期間管理ID: 期間管理フラグが「管理する」の場合:  期間管理ID 上記以外: 0
    sc1Id:
      localOneway.orX0073Oneway.periodManageFlag === OrX0042Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT
        ? localOneway.orX0073Oneway.planTargetPeriodId
        : '0',
    // 作成日：期間管理フラグが「管理する」の場合: 作成日 上記以外: 画面取得作成日
    createYmd: props.onewayModelValue?.createYmd ?? '',
  }
  const ret: ValidPeriodIdSelectOutEntity = await ScreenRepository.select('systemDateSelect', param)
  // GUI01091_保険サービス登録画面をポップアップで起動する。
  // 共通情報.利用者ID
  localOneway.or10878Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  // 共通情報.事業所CD
  localOneway.or10878Oneway.svJigyoCd = systemCommonsStore.getSvJigyoCd ?? ''
  // 共通情報.事業所ID
  localOneway.or10878Oneway.svJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  // AC023で取得したシステム基準日
  localOneway.or10878Oneway.sysYmd = ret.data.sysKijunbi
  // 画面.有効期間ID
  localOneway.or10878Oneway.termid = props.onewayModelValue?.termId ?? ''
  // 機能ID=0
  localOneway.or10878Oneway.functionId = OrX0042Const.FUNCTION_ID_ZERO
  // 呼出元フラグ：1 データ部の保険サービスボタンから
  localOneway.or10878Oneway.callerFlag = OrX0042Const.CALLER_FLAG_ONE
  // 入力支援ポップアップを開く
  Or10878Logic.state.set({
    uniqueCpId: or10878.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr10878 = computed(() => {
  return Or10878Logic.state.get(or10878.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01091_保険サービス登録画面「確定」ボタン押下
 *
 * @param data - GUI01091_保険サービス登録画面情報
 */
function onClickInsServiceConfirm(data: Or10878Type) {
  if (data?.insuranceServiceImportList && data.insuranceServiceImportList.length > 0) {
    let svShu = ''
    let jigyoName = ''
    let hindo = ''
    const handledSvShus: string[] = []
    const handledJigyoNames: string[] = []
    const handledHindos: string[] = []

    let firstItem = true
    data.insuranceServiceImportList.forEach((insuranceService) => {
      if (!firstItem) {
        svShu += '\n'
        jigyoName += '\n'
        hindo += '\n'
      } else {
        firstItem = false
      }

      if (!handledSvShus.includes(insuranceService.dmySvShuruiKnj)) {
        svShu += insuranceService.dmySvShuruiKnj
        handledSvShus.push(insuranceService.dmySvShuruiKnj)
      }
      if (!handledJigyoNames.includes(insuranceService.dmySvJigyoKnj)) {
        jigyoName += insuranceService.dmySvJigyoKnj
        handledJigyoNames.push(insuranceService.dmySvJigyoKnj)
      }
      if (!handledHindos.includes(insuranceService.dmyWeek)) {
        hindo += insuranceService.dmyWeek
        handledHindos.push(insuranceService.dmyWeek)
      }
    })

    // ※１
    tableDataFilter.value[selectedItemIndex.value].hkyu.modelValue =
      star1Items.value[star1Items.value.length - 1].value
    // サービス種別
    if (data.processingMode === Or10878Const.PROCESSINGMODE_ADD) {
      tableDataFilter.value[selectedItemIndex.value].svShu.value += svShu
    } else {
      tableDataFilter.value[selectedItemIndex.value].svShu.value = svShu
    }
    // ※２
    if (data.processingMode === Or10878Const.PROCESSINGMODE_ADD) {
      tableDataFilter.value[selectedItemIndex.value].jigyoName.value += jigyoName
    } else {
      tableDataFilter.value[selectedItemIndex.value].jigyoName.value = jigyoName
    }
    // 頻度
    if (data.processingMode === Or10878Const.PROCESSINGMODE_ADD) {
      tableDataFilter.value[selectedItemIndex.value].hindo.value += hindo
    } else {
      tableDataFilter.value[selectedItemIndex.value].hindo.value = hindo
    }
  }
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 援助現状 サービス実施
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SERVERIMPLEMENTION },
    // 計画書（２）連動項目
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CARE_PLAN2_LINKAGE_ITEM },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 援助現状 サービス実施
  star1Items.value.splice(0)
  let hasEmptyItem = false
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_SERVERIMPLEMENTION)?.forEach((item) => {
    if (item.label === '') {
      hasEmptyItem = true
    }

    star1Items.value.push({
      title: item.label,
      value: item.label,
    })
  })
  if (!hasEmptyItem) {
    star1Items.value.unshift({ title: '', value: '' }) // (空白)
  }

  // 計画書（２）連動項目
  renkeiItems.value.splice(0)
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_CARE_PLAN2_LINKAGE_ITEM)?.forEach(
    (item) => {
      if (item.label === '') {
        hasEmptyItem = true
      }

      renkeiItems.value.push({
        title: item.label,
        value: item.label,
      })
    }
  )
}

/**
 * リサイズ開始
 *
 * @param e - イベント
 *
 * @param columnIndex - カラムIndex
 */
const startResize = (e: MouseEvent, columnIndex: number) => {
  e.preventDefault()
  resizing.isResizing = true
  resizing.columnIndex = columnIndex
  resizing.startX = e.clientX
  resizing.startWidth = columnsWidth[columnIndex].width

  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

/**
 * リサイズ中
 *
 * @param e - イベント
 */
const onMouseMove = (e: MouseEvent) => {
  if (!resizing.isResizing) return

  const diff = e.clientX - resizing.startX
  const newWidth = Math.max(columnsWidth[resizing.columnIndex].minWidth, resizing.startWidth + diff)

  columnsWidth[resizing.columnIndex].width = newWidth
}

/**
 * リサイズ終了
 */
const onMouseUp = () => {
  if (!resizing.isResizing) return

  resizing.isResizing = false
  resizing.columnIndex = -1

  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

defineExpose({
  resetMo01338OnewayItemLabel,
  initData,
  onClickAssessment,
  onClickIssues,
  onClickSdCare,
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21814ResolvePromise !== undefined && newValue !== undefined) {
      or21814ResolvePromise(newValue)
    }
    return
  }
)

watch(
  () => props.modelValue,
  () => {
    selectedItemIndex.value = props.modelValue.selectedItemIndex
    selectedItemTableIndex.value = props.modelValue.selectedItemTableIndex
    selectedColKey.value = props.modelValue.selectedColKey
    tableItem.value = props.modelValue.tableItem
  }
)
watch(
  () => tableItem.value,
  () => {
    if (initFlg) {
      return
    }
    const modelValue = {
      selectedItemIndex: selectedItemIndex.value,
      selectedItemTableIndex: selectedItemTableIndex.value,
      selectedColKey: selectedColKey.value,
      tableItem: tableItem.value,
    }
    emit('update:modelValue', modelValue)
  },
  { deep: true, immediate: true }
)
watch(
  () => selectedRows.value,
  async () => {
    const modelValue = {
      selectedItemIndex: selectedItemIndex.value,
      selectedItemTableIndex: selectedItemTableIndex.value,
      selectedColKey: selectedColKey.value,
      tableItem: tableItem.value,
      selectedItem: selectedRows.value,
    }
    await nextTick()
    // 全選択
    if (local.mo00018.modelValue) {
      modelValue.selectedItem = tableItem.value
    }
    emit('update:modelValue', modelValue)
  }
)
// 「行追加ボタン」押下
watch(
  () => Or21735Logic.event.get(or21735.value.uniqueCpId),
  async () => {
    await nextTick()
    await addNewLine()
  }
)
// 「行挿入ボタン」押下
watch(
  () => Or21736Logic.event.get(or21736.value.uniqueCpId),
  async () => {
    await nextTick()
    await insertNewLine()
  }
)
// 「行複写ボタン」押下
watch(
  () => Or21737Logic.event.get(or21737.value.uniqueCpId),
  async () => {
    await nextTick()
    await cpyLine()
  }
)
// 「行削除ボタン」押下
watch(
  () => Or21738Logic.event.get(or21738.value.uniqueCpId),
  async () => {
    await deleteLine()
  }
)

// GUI01007_課題検討用紙取込画面「確定」ボタンで戻った場合
watch(
  () => local.or00586,
  () => {
    void onOr00586Close()
  }
)
// GUI01023_課題整理総括取込画面「確定」ボタンで戻った場合
watch(
  () => local.or27362Type,
  async (newValue) => {
    await onClickIssuesConfirm(newValue as unknown as Or27362Type[])
  },
  { deep: true }
)
// GUI01029_プロブレムリスト取込画面「確定」ボタンで戻った場合
watch(
  () => local.or28720Type,
  async (newValue) => {
    // 入力支援ポップアップを閉く
    Or28720Logic.state.set({
      uniqueCpId: or28720.value.uniqueCpId,
      state: { isOpen: false },
    })
    await onClickPlConfirm(newValue)
  },
  { deep: true }
)
// GUI00957_サービス種別入力支援画面「確定」ボタンで戻った場合
watch(
  () => local.or50429Type,
  (newValue) => {
    // 入力支援ポップアップを閉く
    Or50429Logic.state.set({
      uniqueCpId: or50429.value.uniqueCpId,
      state: { isOpen: false },
    })
    onClickSvKbnConfirm(newValue)
  },
  { deep: true }
)
// GUI01022_担当者・頻度画面「確定」ボタンで戻った場合
watch(
  () => local.or27043Type,
  (newValue) => {
    if (managerImportFlg.value) {
      onClickManagerImportConfirm(newValue)
    }
  },
  { deep: true }
)
watch(
  () => Or27043Logic.state.get(or27043.value.uniqueCpId)?.isOpen,
  (newValue) => {
    managerImportFlg.value = newValue ?? false
  },
  { deep: true }
)
// GUI01032_表示順変更計画書（2）画面「確定」ボタンで戻った場合
watch(
  () => local.or10860Type,
  (newValue) => {
    if (sortModifiedFlg.value) {
      onClickSortModifiedConfirm(newValue)
    }
  },
  { deep: true }
)
watch(
  () => Or10860Logic.state.get(or10860.value.uniqueCpId)?.isOpen,
  (newValue) => {
    sortModifiedFlg.value = newValue ?? false
  },
  { deep: true }
)
// GUI01016_ケース一覧画面「確定」ボタンで戻った場合
watch(
  () => local.or28256Type,
  (newValue) => {
    onClickCaseImportConfirm(newValue as unknown as string)
  },
  { deep: true }
)
// GUI00937_入力支援［ケアマネ］画面「確定」ボタンで戻った場合
watch(
  () => local.or51775Type,
  (newValue) => {
    if (careManagerFlg.value) {
      // 入力支援ポップアップを閉く
      Or51775Logic.state.set({
        uniqueCpId: or51775.value.uniqueCpId,
        state: { isOpen: false },
      })
      onClickCareManagerConfirm(newValue)
    }
  },
  { deep: true }
)
watch(
  () => Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen,
  (newValue) => {
    careManagerFlg.value = newValue ?? false
  },
  { deep: true }
)
// GUI01018_SDケア作成画面「確定」ボタンで戻った場合
watch(
  () => local.or53186Type,
  async (newValue) => {
    await onClickCareCreateConfirm(newValue)
  },
  { deep: true }
)
// GUI01031_SDケア選択画面「選択」ボタンで戻った場合
watch(
  () => local.or51726Type,
  (newValue) => {
    onClickSdCareSelectConfirm(newValue as unknown as string)
  },
  { deep: true }
)
// GUI01015_課題と短期目標取込画面「確定」ボタンで戻った場合
watch(
  () => local.or28567Type,
  async (newValue) => {
    await onClickIssuesTankiKnjConfirm(newValue as unknown as Or28567Type[])
  },
  { deep: true }
)
// GUI01028_課題とサービス内容取込画面「確定」ボタンで戻った場合
watch(
  () => local.or10475Type,
  async (newValue) => {
    await onClickIssuesKaigoKnjConfirm(newValue as unknown as Or10475Type[])
  },
  { deep: true }
)
// GUI01026_課題と目標取込画面「確定」ボタンで戻った場合
watch(
  () => local.or10477Type,
  async (newValue) => {
    await onClickIssuesGoalImportConfirm(newValue as unknown as Or10477Type[])
  },
  { deep: true }
)
// GUI01027_フリーアセスメント取込画面「確定」ボタンで戻った場合
watch(
  () => local.or28650Type,
  async (newValue) => {
    await onClickFreeAssessmentImportConfirm(newValue as unknown as Or28650Type[])
  },
  { deep: true }
)
// GUI01024_曜日取込画面「確定」ボタンで戻った場合
watch(
  () => local.or28582Type,
  (newValue) => {
    onClickYobiConfirm(newValue)
  },
  { deep: true }
)
// GUI01091_保険サービス登録画面「確定」ボタンで戻った場合
watch(
  () => local.or10878Type,
  (newValue) => {
    onClickInsServiceConfirm(newValue)
  },
  { deep: true }
)
</script>

<template>
  <!-- 行3 -->
  <c-v-row
    no-gutters
    class="flex-0-0"
    :class="{
      'px-6': !localOneway.orX0073Oneway.cpyFlg,
    }"
  >
    <div class="flex-col">
      <div class="line3-flex-box">
        <!-- 行追加ボタン: Or21735 -->
        <div v-if="!localOneway.orX0073Oneway.cpyFlg">
          <g-base-or-21735
            v-bind="or21735"
            width="89"
          />
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-newline-btn')"
          ></c-v-tooltip>
        </div>
        <!-- 行挿入ボタン: Or21736 -->
        <div v-if="!localOneway.orX0073Oneway.cpyFlg">
          <g-base-or-21736
            v-bind="or21736"
            width="89"
          />
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-insertline-btn')"
          ></c-v-tooltip>
        </div>
        <!-- 行複写ボタン: Or21737 -->
        <div v-if="!localOneway.orX0073Oneway.cpyFlg">
          <g-base-or-21737
            v-bind="or21737"
            width="89"
          />
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-cpyline-btn')"
          ></c-v-tooltip>
        </div>
        <!-- 選択列一括上書ボタン: Mo00611 -->
        <base-mo00611
          v-if="!localOneway.orX0073Oneway.cpyFlg"
          :oneway-model-value="{
            ...localOneway.mo00611BatchOverriteScOneway,
            disabled: batchOverwriteDisable,
          }"
          @click="onClickbatchOverriteSc"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-boscline-btn')"
          />
        </base-mo00611>
        <!-- 行削除ボタン: Or21738 -->
        <div v-if="!localOneway.orX0073Oneway.cpyFlg">
          <g-base-or-21738
            v-bind="or21738"
            width="89"
          />
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-deleteline-btn')"
          ></c-v-tooltip>
        </div>
      </div>
      <div class="line3-flex-box">
        <!-- PL取込 -->
        <!-- TEMP comment out for figma review -->
        <g-custom-or-x-0028
          v-if="!localOneway.orX0073Oneway.cpyFlg && false"
          @on-click-icon-btn="onClickPl"
        />
        <!-- ケース取込 -->
        <g-custom-or-x-0029
          v-if="!localOneway.orX0073Oneway.cpyFlg"
          width="92"
          @on-click-icon-btn="onClickCaseImport"
        />
        <!-- 表示順 -->
        <g-custom-or-x-0030
          v-if="!localOneway.orX0073Oneway.cpyFlg"
          width="65"
          @on-click-icon-btn="onClickSortModified"
        />
        <!-- データ-ページング -->
        <base-mo01338
          :oneway-model-value="{
            ...localOneway.mo01338Oneway,
            value: String(tableDataFilter.length),
          }"
          class="pl-4"
        />
      </div>
    </div>
  </c-v-row>
  <!-- 入力内容詳細表 -->
  <c-v-row
    no-gutters
    class="pt-4 min-h-0 flex-0-1"
    :class="{
      'px-6': !localOneway.orX0073Oneway.cpyFlg,
    }"
  >
    <c-v-col
      cols="12"
      class="pa-0 h-100"
    >
      <c-v-data-table
        :headers="headers"
        class="custom-table table-wrapper h-100 overflow-auto"
        hide-default-footer
        :items-per-page="-1"
        :items="tableDataFilter"
        fixed-header
        return-object
      >
        <!-- ヘッダ -->
        <template #headers>
          <tr>
            <!-- チェックボックス -->
            <th
              v-if="localOneway.orX0073Oneway.cpyFlg"
              rowspan="2"
              class="table-checkbox"
              style="width: 58px"
            >
              <base-mo00018
                v-model="local.mo00018"
                :oneway-model-value="{
                  checkboxLabel: '',
                  showItemLabel: false,
                  customClass: new CustomClass({ outerClass: '', labelClass: 'ma-1' }),
                  indeterminate:
                    tableDataFilter.length > 0 &&
                    selectedRows.length > 0 &&
                    selectedRows.length != tableDataFilter.length,
                }"
                click.stop
                @update:model-value="allCheck"
              />
            </th>
            <!-- 課題番号 -->
            <th
              v-if="columnFlag.showNumberFlg"
              rowspan="2"
              :class="{
                'sticky-col': !localOneway.orX0073Oneway.cpyFlg,
              }"
              :style="{
                left: '0px',
                width: columnsWidth[0].width + 'px',
                minWidth: columnsWidth[0].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.issues-number-2') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 0)"
                />
              </div>
            </th>
            <!-- 生活全般の解決すべき課題(ニーズ) -->
            <th
              rowspan="2"
              :class="{
                'sticky-col': !localOneway.orX0073Oneway.cpyFlg,
              }"
              :style="{
                left: (columnFlag.showNumberFlg ? columnsWidth[0].width : 0) + 'px',
                width: columnsWidth[1].width + 'px',
                minWidth: columnsWidth[1].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.life-whole-solution-issues-2') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 1)"
                />
              </div>
            </th>
            <!-- 目標 -->
            <th colspan="4">
              {{ t('label.goal') }}
            </th>
            <!-- 援助内容 -->
            <th :colspan="assistanceContentsColspan">
              {{ t('label.display-order-modified-evaluation-table-header-assistance-contents') }}
            </th>
          </tr>
          <tr>
            <!-- 長期目標 -->
            <th
              :style="{
                width: columnsWidth[2].width + 'px',
                minWidth: columnsWidth[2].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.long-term-goal') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 2)"
                />
              </div>
            </th>
            <!-- 長期目標期間 -->
            <th
              :style="{
                width: columnsWidth[3].width + 'px',
                minWidth: columnsWidth[3].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.care-plan2-period') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 3)"
                />
              </div>
            </th>
            <!-- 短期目標 -->
            <th
              :style="{
                width: columnsWidth[4].width + 'px',
                minWidth: columnsWidth[4].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.short-term-goal') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 4)"
                />
              </div>
            </th>
            <!-- 短期目標期間 -->
            <th
              :style="{
                width: columnsWidth[5].width + 'px',
                minWidth: columnsWidth[5].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.care-plan2-period') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 5)"
                />
              </div>
            </th>
            <!-- 番号 -->
            <th
              v-if="columnFlag.showNumberFlg"
              :style="{
                width: columnsWidth[6].width + 'px',
                minWidth: columnsWidth[6].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.number') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 6)"
                />
              </div>
            </th>
            <!-- 連動項目 -->
            <th
              v-if="columnFlag.showRenkeiFlg"
              :style="{
                width: columnsWidth[15].width + 'px',
                minWidth: columnsWidth[15].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.linking-item') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 15)"
                />
              </div>
            </th>
            <!-- サービス内容 -->
            <th
              :style="{
                width: columnsWidth[7].width + 'px',
                minWidth: columnsWidth[7].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.service-contents') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 7)"
                />
              </div>
            </th>
            <!-- ※1 -->
            <th
              v-if="columnFlag.showStar1Flg"
              :style="{
                width: columnsWidth[8].width + 'px',
                minWidth: columnsWidth[8].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.star-1') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 8)"
                />
              </div>
            </th>
            <!-- サービス種別 -->
            <th
              v-if="columnFlag.showServiceType"
              :style="{
                width: columnsWidth[9].width + 'px',
                minWidth: columnsWidth[9].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.service-kbn') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 9)"
                />
              </div>
            </th>
            <!-- ※2 -->
            <th
              v-if="columnFlag.showStar2Flg"
              :style="{
                width: columnsWidth[10].width + 'px',
                minWidth: columnsWidth[10].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.star-2') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 10)"
                />
              </div>
            </th>
            <!-- 担当者 -->
            <th
              v-if="columnFlag.showManagerFlg"
              :style="{
                width: columnsWidth[11].width + 'px',
                minWidth: columnsWidth[11].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.care-plan2-manager') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 11)"
                />
              </div>
            </th>
            <!-- 頻度 -->
            <th
              :style="{
                width: columnsWidth[12].width + 'px',
                minWidth: columnsWidth[12].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.frequency') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 12)"
                />
              </div>
            </th>
            <!-- 期間 -->
            <th
              :style="{
                width: columnsWidth[13].width + 'px',
                minWidth: columnsWidth[13].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.kikanKnj') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 13)"
                />
              </div>
            </th>
            <!-- 週間日課 -->
            <th
              v-if="columnFlag.showWeekDailyRoutingFlg"
              :style="{
                width: columnsWidth[14].width + 'px',
                minWidth: columnsWidth[14].minWidth + 'px',
              }"
            >
              <div class="resize-col">
                {{ t('label.week-daily-routine-2') }}
                <div
                  class="resize-handle"
                  @mousedown="startResize($event, 14)"
                />
              </div>
            </th>
          </tr>
        </template>
        <!-- BODY -->
        <template #item="{ index: index }">
          <tr
            :class="{ 'select-row': selectedItemTableIndex === tableDataFilter[index].tableIndex }"
            @click="onSelectRow(index, tableDataFilter[index].tableIndex)"
          >
            <!-- チェックボックス -->
            <td
              v-if="localOneway.orX0073Oneway.cpyFlg"
              class="table-checkbox"
            >
              <base-mo00018
                v-model="tableDataFilter[index].checked"
                :oneway-model-value="{
                  checkboxLabel: '',
                  showItemLabel: false,
                }"
                @change="toggleSelectRow"
                @click.stop
              />
            </td>
            <!-- 課題番号テキストボックス -->
            <td
              v-if="columnFlag.showNumberFlg"
              :class="{
                'sticky-col': !localOneway.orX0073Oneway.cpyFlg,
              }"
              :style="{
                left: '0px',
              }"
            >
              <g-custom-or-x0164
                v-model="tableDataFilter[index].kadaiNum"
                :oneway-model-value="{
                  inputMode: 'NumericOnly',
                  showEditBtnFlg: false,
                  numeric: {
                    orX0164InputOneway: {},
                    min: -9,
                    max: 99,
                  },
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  height: '115px',
                }"
                @click="resetCaseImportCol('kadaiNo')"
              />
            </td>
            <!-- 生活全般の解決すべき課題(ニーズ) -->
            <td
              class="custom-table-textarea"
              :class="{
                'sticky-col': !localOneway.orX0073Oneway.cpyFlg,
              }"
              :style="{
                left: (columnFlag.showNumberFlg ? columnsWidth[0].width : 0) + 'px',
              }"
            >
              <g-custom-or-x0163
                v-model="tableDataFilter[index].gutaiteki"
                :oneway-model-value="{
                  showEditBtnFlg: true,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  height: '115px',
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
                @focus="resetCaseImportCol(OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL)"
                @on-click-edit-btn="
                  onClickCareManager(OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL)
                "
              />
            </td>
            <!-- 長期目標 -->
            <td class="custom-table-textarea">
              <g-custom-or-x0163
                v-model="tableDataFilter[index].chouki"
                :oneway-model-value="{
                  showEditBtnFlg: true,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  height: '115px',
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
                @focus="resetCaseImportCol(OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL)"
                @on-click-edit-btn="onClickCareManager(OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL)"
              />
            </td>
            <!-- 長期目標期間 -->
            <td
              class="custom-kikan"
              :class="{
                'bg-red':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL &&
                  selectedItemIndex === index,
                'bg-orange':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL &&
                  selectedItemIndex < index,
              }"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL)"
            >
              <g-custom-or-x0159
                v-model="tableDataFilter[index].choKikan"
                :oneway-model-value="{
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  showMonthDayBtnFlg: columnFlag.showPeriodMdFlg,
                  height: '115px',
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
              />
            </td>
            <!-- 短期目標テキストエリア -->
            <td class="custom-table-textarea">
              <g-custom-or-x0163
                v-model="tableDataFilter[index].tanki"
                :oneway-model-value="{
                  showEditBtnFlg: true,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  height: '115px',
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
                @focus="resetCaseImportCol(OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL)"
                @on-click-edit-btn="onClickCareManager(OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL)"
              />
            </td>
            <!-- 短期目標期間開始年月日テキストボックス -->
            <td
              class="custom-kikan"
              :class="{
                'bg-red':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL &&
                  selectedItemIndex === index,
                'bg-orange':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL &&
                  selectedItemIndex < index,
              }"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL)"
            >
              <g-custom-or-x0159
                v-model="tableDataFilter[index].tanKikan"
                :oneway-model-value="{
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  showMonthDayBtnFlg: columnFlag.showPeriodMdFlg,
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
              />
            </td>
            <!-- 番号 -->
            <td v-if="columnFlag.showNumberFlg">
              <g-custom-or-x0164
                v-model="tableDataFilter[index].kaigoNum"
                :oneway-model-value="{
                  inputMode: 'NumericOnly',
                  showEditBtnFlg: false,
                  numeric: {
                    orX0164InputOneway: {},
                    min: -9,
                    max: 99,
                  },
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  height: '115px',
                }"
                @click="resetCaseImportCol('kaigoNo')"
              />
            </td>
            <!-- 連動項目 -->
            <td v-if="columnFlag.showRenkeiFlg">
              <base-mo01282
                v-model="tableDataFilter[index].nikkaId"
                :oneway-model-value="{
                  items: renkeiItems,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                }"
                class="custom-table-select"
              />
            </td>
            <!-- サービス内容 -->
            <td class="custom-table-textarea">
              <g-custom-or-x0163
                v-model="tableDataFilter[index].kaigo"
                :oneway-model-value="{
                  showEditBtnFlg: true,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  height: '115px',
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
                @focus="resetCaseImportCol(OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL)"
                @on-click-edit-btn="onClickCareManager(OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL)"
              />
            </td>
            <!-- ※1 -->
            <td v-if="columnFlag.showStar1Flg">
              <base-mo01282
                v-model="tableDataFilter[index].hkyu"
                :oneway-model-value="{
                  items: star1Items,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                }"
                class="custom-table-select"
              />
            </td>
            <!-- サービス種別 -->
            <td
              v-if="columnFlag.showServiceType"
              class="custom-table-textarea"
              :class="{
                'bg-red':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL &&
                  selectedItemIndex === index,
                'bg-orange':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL &&
                  selectedItemIndex < index,
              }"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL)"
            >
              <!-- サービス種別テキストエリア -->
              <g-custom-or-x0185
                v-model="tableDataFilter[index].svShu"
                :oneway-model-value="{
                  showEditBtnFlg: true,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  height: '115px',
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
                @focus="resetCaseImportCol(OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL)"
              >
                <template #menu>
                  <div
                    class="orx0185-footer-menu-item"
                    @click="onClickSvKbn('1')"
                  >
                    {{ t('label.enter-using', [t('label.service-kind')]) }}
                  </div>
                  <div
                    v-if="columnFlag.showServiceTypeInsServiceFlg"
                    class="orx0185-footer-menu-item"
                    @click="onClickInsServiceInputHelp()"
                  >
                    {{ t('label.enter-using', [t('label.insurance-service')]) }}
                  </div>
                  <div
                    v-if="columnFlag.showServiceTypeInsServiceFlg"
                    class="orx0185-footer-menu-item"
                    @click="onClickSvKbn('3')"
                  >
                    {{ t('label.enter-using', [t('label.use-slip-import')]) }}
                  </div>
                </template>
              </g-custom-or-x0185>
            </td>
            <!-- ※2 -->
            <td
              v-if="columnFlag.showStar2Flg"
              class="custom-table-textarea"
              :class="{
                'bg-red':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL &&
                  selectedItemIndex === index,
                'bg-orange':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL &&
                  selectedItemIndex < index,
              }"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL)"
            >
              <g-custom-or-x0163
                v-model="tableDataFilter[index].jigyoName"
                :oneway-model-value="{
                  showEditBtnFlg: true,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  height: '115px',
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
                @focus="resetCaseImportCol(OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL)"
                @on-click-edit-btn="onClickSvKbn('2')"
              />
            </td>
            <!-- 担当者 -->
            <td
              v-if="columnFlag.showManagerFlg"
              class="custom-table-textarea"
            >
              <g-custom-or-x0185
                v-model="tableDataFilter[index].svShu"
                :oneway-model-value="{
                  showEditBtnFlg: true,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  height: '115px',
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
                @focus="resetCaseImportCol(OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL)"
              >
                <template #menu>
                  <div
                    class="orx0185-footer-menu-item"
                    @click="onClickManager()"
                  >
                    {{ t('label.enter-using', [t('label.care-plan2-manager')]) }}
                  </div>
                  <div
                    v-if="columnFlag.showManagerImportFlg"
                    class="orx0185-footer-menu-item"
                    @click="onClickManagerImport()"
                  >
                    {{ t('label.enter-using', [t('label.import2')]) }}
                  </div>
                </template>
              </g-custom-or-x0185>
            </td>
            <!-- 頻度 -->
            <td
              class="custom-table-textarea"
              :class="{
                'bg-red':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL &&
                  selectedItemIndex === index,
                'bg-orange':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL &&
                  selectedItemIndex < index,
              }"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL)"
            >
              <g-custom-or-x0185
                v-model="tableDataFilter[index].hindo"
                :oneway-model-value="{
                  showEditBtnFlg: true,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  height: '115px',
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
                @focus="resetCaseImportCol(OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL)"
              >
                <template #menu>
                  <div
                    class="orx0185-footer-menu-item"
                    @click="onClickCareManager(OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL)"
                  >
                    {{ t('label.enter-using', [t('label.frequency')]) }}
                  </div>
                  <div
                    v-if="columnFlag.showDayOfWeekFlg"
                    class="orx0185-footer-menu-item"
                    @click="onClickYobi()"
                  >
                    {{ t('label.enter-using', [t('label.day-of-week')]) }}
                  </div>
                  <div
                    v-if="columnFlag.showFreqImportFlg"
                    class="orx0185-footer-menu-item"
                    @click="onClickManagerImport()"
                  >
                    {{ t('label.enter-using', [t('label.import2')]) }}
                  </div>
                </template>
              </g-custom-or-x0185>
            </td>
            <!-- 期間 -->
            <td
              class="custom-kikan"
              :class="{
                'bg-red':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL &&
                  selectedItemIndex === index,
                'bg-orange':
                  batchColUpdating &&
                  selectedColKey === OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL &&
                  selectedItemIndex < index,
              }"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL)"
            >
              <g-custom-or-x0159
                v-model="tableDataFilter[index].kikan"
                :oneway-model-value="{
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                  showMonthDayBtnFlg: columnFlag.showPeriodMdFlg,
                  height: '115px',
                  rules: [byteLength(4000)],
                }"
                maxlength="4000"
              />
            </td>
            <!-- 週間日課 -->
            <td
              v-if="columnFlag.showWeekDailyRoutingFlg"
              class="custom-table-textarea"
            >
              <g-custom-or-x0163
                v-model="tableDataFilter[index].weekDailyRoute"
                :oneway-model-value="{
                  showEditBtnFlg: true,
                  textareaReadonly: true,
                  readonly: localOneway.orX0073Oneway.cpyFlg,
                }"
                @on-click-edit-btn="onWeekDailyRoutingClick"
              />
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-col>
  </c-v-row>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- GUI01011 履歴選択画面のポップアップ -->
  <g-custom-or-00586
    v-if="showDialogOr00586"
    v-bind="or00586"
    v-model="local.or00586"
    :oneway-model-value="localOneway.or00586Oneway"
  />
  <!-- Or27235: 有機体: GUI01021_担当者入力支援画面をポップアップで起動する。 -->
  <g-custom-or-27235
    v-if="showDialogOr27235"
    v-bind="or27235"
    v-model="local.or27235Type"
    :oneway-model-value="localOneway.or27235OnewayType"
    :parent-unique-cp-id="props.uniqueCpId"
    @on-confirm="handleOr27235Confirm"
  >
  </g-custom-or-27235>
  <!-- Or28720: 有機体: GUI01029_プロブレムリスト取込画面をポップアップで起動する。 -->
  <g-custom-or-28720
    v-if="showDialogOr28720"
    v-bind="or28720"
    v-model="local.or28720Type"
    :oneway-model-value="localOneway.or28720OnewayType"
  >
  </g-custom-or-28720>
  <!-- Or27362: 有機体: GUI01023_課題整理総括取込画面をポップアップで起動する。 -->
  <g-custom-or-27362
    v-if="showDialogOr27362"
    v-bind="or27362"
    v-model="local.or27362Type"
    :oneway-model-value="localOneway.or27362OnewayType"
  >
  </g-custom-or-27362>
  <!-- Or50429: 有機体: GUI00957_サービス種別入力支援画面をポップアップで起動する。 -->
  <g-custom-or-50429
    v-if="showDialogOr50429"
    v-bind="or50429"
    v-model="local.or50429Type"
    :oneway-model-value="localOneway.or50429OnewayType"
  >
  </g-custom-or-50429>
  <!-- Or27043: 有機体: GUI01022_担当者・頻度画面をポップアップで起動する。 -->
  <g-custom-or-27043
    v-if="showDialogOr27043"
    v-bind="or27043"
    v-model="local.or27043Type"
  >
  </g-custom-or-27043>
  <!-- Or10860: 有機体: GUI01032_表示順変更計画書（2）画面をポップアップで起動する。 -->
  <g-custom-or-10860
    v-if="showDialogOr10860"
    v-bind="or10860"
    v-model="local.or10860Type"
    :oneway-model-value="localOneway.or10860OnewayType"
  >
  </g-custom-or-10860>
  <!-- Or28256: 有機体: GUI01016_ケース一覧画面をポップアップで起動する。 -->
  <g-custom-or-28256
    v-if="showDialogOr28256"
    v-bind="or28256"
    v-model="local.or28256Type"
    :oneway-model-value="localOneway.or28256OnewayType"
  >
  </g-custom-or-28256>
  <!-- Or51775: 有機体: GUI00937_入力支援［ケアマネ］画面をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="local.or51775Type"
    :oneway-model-value="localOneway.or51775OnewayType"
  >
  </g-custom-or-51775>
  <!-- Or54215: 有機体: GUI01019_SDケアマスタ簡易登録画面をポップアップで起動する。 -->
  <g-custom-or-54215
    v-if="showDialogOr54215"
    v-bind="or54215"
    :oneway-model-value="localOneway.or54215OnewayType"
  >
  </g-custom-or-54215>
  <!-- Or53186: 有機体: GUI01018_SDケア作成画面をポップアップで起動する。 -->
  <g-custom-or-53186
    v-if="showDialogOr53186"
    v-bind="or53186"
    v-model="local.or53186Type"
    :oneway-model-value="localOneway.or53186OnewayType"
  >
  </g-custom-or-53186>
  <!-- Or51726: 有機体: GUI01031_SDケア選択画面をポップアップで起動する。 -->
  <g-custom-or-51726
    v-if="showDialogOr51726"
    v-bind="or51726"
    v-model="local.or51726Type"
  >
  </g-custom-or-51726>
  <!-- Or28567: 有機体: GUI01015_課題と短期目標取込画面をポップアップで起動する。 -->
  <g-custom-or-28567
    v-if="showDialogOr28567"
    v-bind="or28567"
    v-model="local.or28567Type"
    :oneway-model-value="localOneway.or28567OnewayType"
  >
  </g-custom-or-28567>
  <!-- Or10475: 有機体: GUI01028_課題とサービス内容取込画面をポップアップで起動する。 -->
  <g-custom-or-10475
    v-if="showDialogOr10475"
    v-bind="or10475"
    v-model="local.or10475Type"
    :oneway-model-value="localOneway.or10475OnewayType"
  >
  </g-custom-or-10475>
  <!-- Or10477: 有機体: GUI01026_課題と目標取込画面をポップアップで起動する。 -->
  <g-custom-or-10477
    v-if="showDialogOr10477"
    v-bind="or10477"
    v-model="local.or10477Type"
    :oneway-model-value="localOneway.or10477OnewayType"
  >
  </g-custom-or-10477>
  <!-- Or28650: 有機体: GUI01027_フリーアセスメント取込画面をポップアップで起動する。 -->
  <g-custom-or-28650
    v-if="showDialogOr28650"
    v-bind="or28650"
    v-model="local.or28650Type"
    :oneway-model-value="localOneway.or28650OnewayType"
  >
  </g-custom-or-28650>
  <!-- Or28582: 有機体: GUI01024_曜日取込画面をポップアップで起動する。 -->
  <g-custom-or-28582
    v-if="showDialogOr28582"
    v-bind="or28582"
    v-model="local.or28582Type"
    assessment-id=""
  >
  </g-custom-or-28582>
  <!-- Or22951: 有機体: GUI02259_適用事業所の選択画面をポップアップで起動する。 -->
  <g-custom-or-22951
    v-if="showDialogOr22951"
    v-bind="or22951"
    :oneway-model-value="localOneway.or22951OnewayType"
    @update:model-value="onClickTekiyoJigyoConfirm"
  >
  </g-custom-or-22951>
  <!-- Or10878: 有機体: GUI01091_保険サービス登録画面のポップアップで起動する。 -->
  <g-custom-or-10878
    v-if="showDialogOr10878"
    v-bind="or10878"
    v-model="local.or10878Type"
    :oneway-model-value="localOneway.or10878Oneway"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';
.custom-table.table-wrapper {
  :deep(.v-table__wrapper > table) {
    width: max-content;
    table-layout: fixed;
  }

  :deep(.v-table__wrapper > table > thead > tr:not(:first-child) > th) {
    &:first-child {
      border-left: 0px !important;
    }
  }

  :deep(.v-table__wrapper > table > thead > tr > th) {
    background: rgb(var(--v-theme-blue-100)) !important;

    &:not(:first-child) {
      border-left: 0px !important;
    }
    &:not(:last-child) {
      border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
    }

    font-weight: 400;
    font-size: 13px;
    line-height: 16px;
    letter-spacing: -0.04em;

    padding-left: 12px;
    padding-right: 12px;
  }

  :deep(.v-table__wrapper > table > tbody > tr > td) {
    height: 116px !important;

    &:not(:first-child) {
      border-left: 0px !important;
    }
    &:not(:last-child) {
      border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
    }

    &:has(input) {
      padding: 1px 1px !important;
    }

    &:has(textarea) {
      padding: 1px 1px !important;
    }

    &:has(> select:not([disabled]):not([readonly])) > select:not([disabled]):not([readonly]) {
      outline: unset;
    }

    &:has(input:not([readonly]):not([disabled]):not([type='checkbox']):not([type='radio'])) {
      padding: 1px 1px !important;
    }
  }

  :deep(.v-selection-control--density-default) {
    --v-selection-control-size: 32px;
  }

  :deep(.v-checkbox .v-checkbox-btn) {
    height: 32px;
    min-height: 32px;
  }
}
// col flex
.flex-col {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex-shrink: 0;

  :deep(.v-btn) {
    --v-btn-height: 32px;
  }

  :deep(.v-btn--size-default) {
    padding: 0 12px;
  }
}
.line3-flex-box {
  display: flex;
  column-gap: 8px;
  align-items: center;
  flex-shrink: 0;
  flex-wrap: wrap;

  :deep(.v-btn) {
    min-width: 0px !important;
  }

  :deep(.v-btn--size-default) {
    padding: 0 12px;
  }
}
.page-label-center {
  display: flex;
  align-items: center;
}
:deep(.table-checkbox > div) {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px !important;
}
.min-h-0 {
  min-height: 0;
}
.bg-red {
  background-color: rgb(255, 200, 210);
}
.bg-orange {
  background-color: rgb(255, 200, 100);
}
.resize-col {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}
.resize-handle {
  position: absolute;
  top: 0;
  right: -12px;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  transition: background-color 0.2s ease;
}
.custom-table-pulldown {
  background-color: transparent;

  & :deep(.v-text-field .v-field) {
    background-color: transparent;
  }

  :deep(.v-field__outline) {
    display: none;
  }

  :deep(.v-row .v-col .v-input) {
    background-color: transparent;
  }

  :deep(.v-field__input) {
    height: 115px;
  }
}
.custom-kikan {
  :deep(.table-kikan-container .table-kikan-cell .table-kikan-content) {
    padding: 8px 12px;
  }

  :deep(textarea) {
    overflow-x: hidden;
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 17px;
    letter-spacing: -0.04em;
  }
}
.custom-table-select {
  & :deep(.v-input__control .v-field) {
    background-color: transparent;
  }
}
.custom-table-textarea {
  :deep(.table-textarea-cell .table-textarea-content) {
    padding: 8px 12px;
  }

  :deep(textarea) {
    line-height: 19.8px;
    letter-spacing: -0.04em;
  }
}
th .sticky-col {
  background: rgb(var(--v-theme-blue-100));
}
tr:not(.select-row) .sticky-col {
  background: rgb(var(--v-theme-secondaryBackground));
}
tr.select-row .sticky-col {
  background: rgb(var(--v-theme-blue-100));
}
.sticky-col {
  position: sticky;
  z-index: 2;
}
</style>
