{"resultCd": "0", "syubetuId": "2", "kihonJokyoFlg": "1", "kihonCheckListFlg": "1", "kikanFlg": "1", "svJigyoRyakuKnj": "事業名（略称）", "kikanObj": {"sc1Id": "2", "startYmd": "2002/02/01", "endYmd": "2002/02/28", "pagingNo": "2", "pagingCnt": "5", "pagingFlg": "1"}, "rirekiObj": {"rirekiId": "11", "shokuId": "1", "shokuKnj": "管理者 岡本 伊", "createYmd": "2004/01/01", "pagingNo": "1", "pagingCnt": "3"}, "initMasterObj": {"itakuKkakPrtFlg": "0", "cpnFlg": "1", "msgFlg": "0", "itakuKkakKikanFlg": "1", "itakuKkakHsJigyoFlg": "1", "itakuKkakHindoFlg": "1", "itakuKkakYmdFlg": "1", "itakuKkakHsKasanFlg": "1"}, "tab1DataObj": {"mokuhyoDayKnj": "目標とする生活（1日）", "mokuhyoYearKnj": "目標とする生活（1年）", "sogoHoshinKnj": "総合的な方針", "kenkoRyuitenKnj": "健康状態について", "program1Flg": "1", "program2Flg": "0", "program3Flg": "1", "program4Flg": "0", "program5Flg": "1", "program6Flg": "0", "program1Cnt": "1", "program2Cnt": "2", "program3Cnt": "3", "program4Cnt": "4", "program5Cnt": "5", "program6Cnt": "6", "datoHoshinKnj": "妥当な支援の実施に向けた方針", "centerIkenKnj": "地域包括支援センターの意見", "centerKakuninFlg": "2", "doiKnj": "同意者", "doiYmd": "2025/05/27", "yukoEYmd": ""}, "tab2DataList": [{"assryoId": "1", "jokyoKnj": "アセスメント領域と現在の状況1", "iyokuikouKnj": "本人・家族の意欲・意向11", "kadaiFlg": "1", "kadaiKnj": "領域における課題（背景・原因）111111111", "plan12Id": "1", "modifiedCnt": "0"}, {"assryoId": "2", "jokyoKnj": "アセスメント領域と現在の状況2", "iyokuikouKnj": "本人・家族の意欲・意向22", "kadaiFlg": "1", "kadaiKnj": "領域における課題（背景・原因）2222222222222", "plan12Id": "2", "modifiedCnt": "0"}, {"assryoId": "3", "jokyoKnj": "アセスメント領域と現在の状況3", "iyokuikouKnj": "本人・家族の意欲・意向33", "kadaiFlg": "2", "kadaiKnj": "領域における課題（背景・原因）333333333333333", "plan12Id": "3", "modifiedCnt": "0"}, {"assryoId": "4", "jokyoKnj": "アセスメント領域と現在の状況4", "iyokuikouKnj": "本人・家族の意欲・意向44", "kadaiFlg": "1", "kadaiKnj": "領域における課題（背景・原因）4444444444444444", "plan12Id": "4", "modifiedCnt": "0"}, {"assryoId": "5", "jokyoKnj": "アセスメント領域と現在の状況5", "iyokuikouKnj": "本人・家族の意欲・意向55", "kadaiFlg": "2", "kadaiKnj": "領域における課題（背景・原因）55555555555555", "plan12Id": "5", "modifiedCnt": "0"}], "tab3DataList": [{"sogoKadaiNo": "1", "sogoKadaiKnj": "aaaa1", "sogoTeianKnj": "bbbb", "sogoIkouKnj": "vcccc", "sogoMokuhyoKnj": "asdqweqae444", "kadaiNo": "1", "plan13Id": "1", "modifiedCnt": "0"}, {"sogoKadaiNo": "2", "sogoKadaiKnj": "aaaa2", "sogoTeianKnj": "bbbb", "sogoIkouKnj": "vcccc", "sogoMokuhyoKnj": "asdqweqae333", "kadaiNo": "2", "plan13Id": "2", "modifiedCnt": "0"}, {"sogoKadaiNo": "3", "sogoKadaiKnj": "aaaa3", "sogoTeianKnj": "bbbb", "sogoIkouKnj": "vcccc", "sogoMokuhyoKnj": "asdqweqae222", "kadaiNo": "3", "plan13Id": "3", "modifiedCnt": "0"}, {"sogoKadaiNo": "4", "sogoKadaiKnj": "aaaa4", "sogoTeianKnj": "bbbb", "sogoIkouKnj": "vcccc", "sogoMokuhyoKnj": "asdqweqae111", "kadaiNo": "4", "plan13Id": "4", "modifiedCnt": "0"}], "tab4DataList": [{"sogoKadaiNo": "1", "shienPointKnj": "目標についての支援のポイント1", "infoServiceKnj": "具体的な支援の内容（本人）1", "kazokuServiceKnj": "具体的な支援の内容（家族）1", "hokenServiceKnj": "具体的な支援の内容（保険・地域）1", "svKbn": "1", "svKazokuKbn": "1", "svHokenKbn": "1", "svShubetuKnj": "サービス種別（本人）1", "svShubetuKazokuKnj": "サービス種別（家族）1", "svShubetuHoken_knj": "サービス種別（保険・地域）1", "svJigyoKnj": "事業所（本人）1", "svJigyoKazokuKnj": "事業所（家族）1", "svJigyoHoken_knj": "事業所（保険・地域）1", "hindoKnj": "頻度（本人）1", "hindoKazokuKnj": "頻度（家族）1", "hindoHokenKnj": "頻度（保険・地域）1", "kikanKnj": "2020/01/03 ～ 2020/01/23", "kikanKazokuKnj": "2020/01/04 ～ 2020/01/24", "kikanHokenKnj": "2020/01/05 ～ 2020/01/25", "kikanSYmd": "2020/01/02", "kikanEYmd": "2020/01/12", "kikanKazokuS_ymd": "2020/01/03", "kikanKazokuE_ymd": "2020/01/13", "kikanHokenS_ymd": "2020/01/04", "kikanHokenE_ymd": "2020/01/14", "seqNo": "1", "plan14Id": "1", "modifiedCnt": "0"}, {"sogoKadaiNo": "2", "shienPointKnj": "目標についての支援のポイント2", "infoServiceKnj": "具体的な支援の内容（本人）2", "kazokuServiceKnj": "具体的な支援の内容（家族）2", "hokenServiceKnj": "具体的な支援の内容（保険・地域）2", "svKbn": "0", "svKazokuKbn": "1", "svHokenKbn": "0", "svShubetuKnj": "サービス種別（本人）2", "svShubetuKazokuKnj": "サービス種別（家族）2", "svShubetuHoken_knj": "サービス種別（保険・地域）2", "svJigyoKnj": "事業所（本人）2", "svJigyoKazokuKnj": "事業所（家族）2", "svJigyoHoken_knj": "事業所（保険・地域）2", "hindoKnj": "頻度（本人）2", "hindoKazokuKnj": "頻度（家族）2", "hindoHokenKnj": "頻度（保険・地域）2", "kikanKnj": "2020/01/03 ～ 2020/01/23", "kikanKazokuKnj": "2020/01/04 ～ 2020/01/24", "kikanHokenKnj": "2020/01/05 ～ 2020/01/25", "kikanSYmd": "2020/02/02", "kikanEYmd": "2020/02/12", "kikanKazokuS_ymd": "2020/02/03", "kikanKazokuE_ymd": "2020/02/13", "kikanHokenS_ymd": "2020/02/04", "kikanHokenE_ymd": "2020/02/14", "seqNo": "2", "plan14Id": "2", "modifiedCnt": "0"}, {"sogoKadaiNo": "3", "shienPointKnj": "目標についての支援のポイント3", "infoServiceKnj": "具体的な支援の内容（本人）3", "kazokuServiceKnj": "具体的な支援の内容（家族）3", "hokenServiceKnj": "具体的な支援の内容（保険・地域）3", "svKbn": "1", "svKazokuKbn": "1", "svHokenKbn": "0", "svShubetuKnj": "サービス種別（本人）3", "svShubetuKazokuKnj": "サービス種別（家族）3", "svShubetuHoken_knj": "サービス種別（保険・地域）3", "svJigyoKnj": "事業所（本人）3", "svJigyoKazokuKnj": "事業所（家族）3", "svJigyoHoken_knj": "事業所（保険・地域）3", "hindoKnj": "頻度（本人）3", "hindoKazokuKnj": "頻度（家族）3", "hindoHokenKnj": "頻度（保険・地域）3", "kikanKnj": "2020/01/03 ～ 2020/01/23", "kikanKazokuKnj": "2020/01/04 ～ 2020/01/24", "kikanHokenKnj": "2020/01/05 ～ 2020/01/25", "kikanSYmd": "2020/03/02", "kikanEYmd": "2020/03/12", "kikanKazokuS_ymd": "2020/03/03", "kikanKazokuE_ymd": "2020/03/13", "kikanHokenS_ymd": "2020/03/04", "kikanHokenE_ymd": "2020/03/14", "seqNo": "4", "plan14Id": "3", "modifiedCnt": "1"}, {"sogoKadaiNo": "4", "shienPointKnj": "目標についての支援のポイント4", "infoServiceKnj": "具体的な支援の内容（本人）4", "kazokuServiceKnj": "具体的な支援の内容（家族）4", "hokenServiceKnj": "具体的な支援の内容（保険・地域）4", "svKbn": "0", "svKazokuKbn": "0", "svHokenKbn": "1", "svShubetuKnj": "サービス種別（本人）4", "svShubetuKazokuKnj": "サービス種別（家族）4", "svShubetuHoken_knj": "サービス種別（保険・地域）4", "svJigyoKnj": "事業所（本人）4", "svJigyoKazokuKnj": "事業所（家族）4", "svJigyoHoken_knj": "事業所（保険・地域）4", "hindoKnj": "頻度（本人）4", "hindoKazokuKnj": "頻度（家族）4", "hindoHokenKnj": "頻度（保険・地域）4", "kikanKnj": "2020/01/03 ～ 2020/01/23", "kikanKazokuKnj": "2020/01/04 ～ 2020/01/24", "kikanHokenKnj": "2020/01/05 ～ 2020/01/25", "kikanSYmd": "2020/04/02", "kikanEYmd": "2020/04/12", "kikanKazokuS_ymd": "2020/04/03", "kikanKazokuE_ymd": "2020/04/13", "kikanHokenS_ymd": "2020/04/04", "kikanHokenE_ymd": "2020/04/14", "seqNo": "3", "plan14Id": "4", "modifiedCnt": "0"}, {"sogoKadaiNo": "5", "shienPointKnj": "目標についての支援のポイント5", "infoServiceKnj": "具体的な支援の内容（本人）5", "kazokuServiceKnj": "具体的な支援の内容（家族）5", "hokenServiceKnj": "具体的な支援の内容（保険・地域）5", "svKbn": "0", "svKazokuKbn": "1", "svHokenKbn": "1", "svShubetuKnj": "サービス種別（本人）5", "svShubetuKazokuKnj": "サービス種別（家族）5", "svShubetuHoken_knj": "サービス種別（保険・地域）5", "svJigyoKnj": "事業所（本人）5", "svJigyoKazokuKnj": "事業所（家族）5", "svJigyoHoken_knj": "事業所（保険・地域）5", "hindoKnj": "頻度（本人）5", "hindoKazokuKnj": "頻度（家族）5", "hindoHokenKnj": "頻度（保険・地域）5", "kikanKnj": "2020/01/03 ～ 2020/01/23", "kikanKazokuKnj": "2020/01/04 ～ 2020/01/24", "kikanHokenKnj": "2020/01/05 ～ 2020/01/25", "kikanSYmd": "2020/05/02", "kikanEYmd": "2020/05/12", "kikanKazokuS_ymd": "2020/05/03", "kikanKazokuE_ymd": "2020/05/13", "kikanHokenS_ymd": "2020/05/04", "kikanHokenE_ymd": "2020/05/14", "seqNo": "5", "plan14Id": "5", "modifiedCnt": "1"}, {"sogoKadaiNo": "1", "shienPointKnj": "目標についての支援のポイント6", "infoServiceKnj": "具体的な支援の内容（本人）6", "kazokuServiceKnj": "具体的な支援の内容（家族）6", "hokenServiceKnj": "具体的な支援の内容（保険・地域）6", "svKbn": "0", "svKazokuKbn": "0", "svHokenKbn": "1", "svShubetuKnj": "サービス種別（本人）6", "svShubetuKazokuKnj": "サービス種別（家族）6", "svShubetuHoken_knj": "サービス種別（保険・地域）6", "svJigyoKnj": "事業所（本人）6", "svJigyoKazokuKnj": "事業所（家族）6", "svJigyoHoken_knj": "事業所（保険・地域）6", "hindoKnj": "頻度（本人）6", "hindoKazokuKnj": "頻度（家族）6", "hindoHokenKnj": "頻度（保険・地域）6", "kikanKnj": "2020/01/03 ～ 2020/01/23", "kikanKazokuKnj": "2020/01/04 ～ 2020/01/24", "kikanHokenKnj": "2020/01/05 ～ 2020/01/25", "kikanSYmd": "2020/06/02", "kikanEYmd": "2020/06/12", "kikanKazokuS_ymd": "2020/06/03", "kikanKazokuE_ymd": "2020/06/13", "kikanHokenS_ymd": "2020/06/04", "kikanHokenE_ymd": "2020/06/14", "seqNo": "6", "plan14Id": "6", "modifiedCnt": "3"}], "hokenSvList": [{"youbi": "1000000", "igaiKbn": "", "igaiDate": "", "igaiWeek": "", "kaishiJikan": "009:00", "shuuryouJikan": "09:30", "svShuruiCd": "61", "svItemCd": "40526", "svJigyoId": "29", "tanka": "1234", "fygId": "", "gouseiSikKbn": "1", "kasanFlg": "0", "oyaLineNo": "1", "dmyPlan15Id": "0", "plan15Id": "0", "modifiedCnt": "0"}], "hokenYmdList": [], "termid": "1"}