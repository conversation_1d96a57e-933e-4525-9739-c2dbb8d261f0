<script setup lang="ts">
/**
 * Or26994:有機体:モーダル（基本的な事項）
 * GUI01090_基本的な事項画面
 *
 * @description
 * ［基本的な事項］画面が表示される。
 *
 * <AUTHOR> 朱征宇
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26987Const } from '../Or26987/Or26987.constants'
import { Or26988Const } from '../Or26988/Or26988.constants'
import { Or26989Const } from '../Or26989/Or26989.constants'
import { Or26990Const } from '../Or26990/Or26990.constants'
import { Or26991Const } from '../Or26991/Or26991.constants'
import { Or26992Const } from '../Or26992/Or26992.constants'
import { Or26987Logic } from '../Or26987/Or26987.logic'
import { Or26988Logic } from '../Or26988/Or26988.logic'
import { Or26989Logic } from '../Or26989/Or26989.logic'
import { Or26990Logic } from '../Or26990/Or26990.logic'
import { Or26991Logic } from '../Or26991/Or26991.logic'
import { Or26992Logic } from '../Or26992/Or26992.logic'
import { Or26979Const } from '../Or26979/Or26979.constants'
import { Or26979Logic } from '../Or26979/Or26979.logic'
import { Or26981Const } from '../Or26981/Or26981.constants'
import { Or26981Logic } from '../Or26981/Or26981.logic'
import type { Or26994StateType } from './Or26994.type'
import { Or26994Const } from './Or26994.constants'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import { useScreenUtils } from '~/utils/useScreenUtils'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Or26987OnewayType, Or26987Type } from '~/types/cmn/business/components/Or26987Type'
import type { Or26988Type } from '~/types/cmn/business/components/Or26988Type'
import type { Or26989OnewayType, Or26989Type } from '~/types/cmn/business/components/Or26989Type'
import type { Or26990OnewayType, Or26990Type } from '~/types/cmn/business/components/Or26990Type'
import type { Or26991OnewayType, Or26991Type } from '~/types/cmn/business/components/Or26991Type'
import type { Or26994Type, Or26994OneWayType } from '~/types/cmn/business/components/Or26994Type'
import type { Or26992Type } from '~/types/cmn/business/components/Or26992Type'
import type { Or26979Type } from '~/types/cmn/business/components/Or26979Type'
import type { Or26981OnewayType, Or26981Type } from '~/types/cmn/business/components/Or26981Type'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or26994Type
  onewayModelValue: Or26994OneWayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

const defaultWay = reactive({
  // 初回作成日
  or26991Oneway: {} as Or26991OnewayType,
  or26981Oneway: {
    // 委託先の作成者（職員ID）
    itakuShokuId: props.onewayModelValue.userId,
    // 事業所ＩＤ
    svJigyoId: props.onewayModelValue.svJigyoId,
    // 適用事業所ＩＤリスト
    jigyoIdList: props.onewayModelValue.jigyoIdList,
  } as Or26981OnewayType,
  or26987Oneway: {
    /** 利用者ID */
    userId: props.onewayModelValue.userId,
  } as Or26987OnewayType,
})

const local = reactive({
  or26979: {
    // 担当地域包括支援センター
    chiJigyoKnj: { value: props.modelValue.chiJigyoKnj },
    // 計画作成者
    centerShokuKnj: { value: props.modelValue.centerShokuKnj },
  } as Or26979Type,
  or26981: {
    // 委託の場合の作成事業者センター
    itkJigyoKnj: { value: props.modelValue.itkJigyoKnj },
    // 委託先の作成者
    itakuShokuKnj: { value: props.modelValue.itakuShokuKnj },
    // 委託先の作成者（職員ID）
    itakuShokuId: props.modelValue.itakuShokuId,
  } as Or26981Type,
  or26987: {
    /** 認定日 */
    ninteiYmd: { value: props.modelValue.ninteiYmd } as Mo00020Type,
    /** 認定の有効期間開始日 */
    yukoSYmd: { value: props.modelValue.yukoSYmd } as Mo00020Type,
    /** 認定の有効期間終了日 */
    yukoEYmd: { value: props.modelValue.yukoEYmd } as Mo00020Type,
    // 要支援区分
    certificationClass: props.modelValue.yoshienKbn,
    // 利用サービス（介護保険）
    mo00018NursingCareInsurance: {
      modelValue: props.modelValue.service1Flg === Or26994Const.DEFAULT.CHECK_ON,
    },
    // 利用サービス（地域支援事業）
    mo00018RegionalSupportBusiness: {
      modelValue: props.modelValue.service2Flg === Or26994Const.DEFAULT.CHECK_ON,
    },
  } as Or26987Type,
  or26988: {
    // 初回
    mo00018FirstTime: {
      modelValue: getKubun(props.modelValue.kubun, Or26994Const.DEFAULT.PLAN_CATEGORY_FIRST_TIME),
    },
    // 紹介
    mo00018Introduction: {
      modelValue: getKubun(props.modelValue.kubun, Or26994Const.DEFAULT.PLAN_CATEGORY_INTRODUCTION),
    },
    // 継続
    mo00018Continuation: {
      modelValue: getKubun(props.modelValue.kubun, Or26994Const.DEFAULT.PLAN_CATEGORY_CONTINUATION),
    },
  } as Or26988Type,
  or26992: {
    // 利用サービス（介護保険）
    mo00018NursingCareInsurance: {
      modelValue: props.modelValue.service1Flg === Or26994Const.DEFAULT.CHECK_ON,
    },
    // 利用サービス（地域支援事業）
    mo00018RegionalSupportBusiness: {
      modelValue: props.modelValue.service2Flg === Or26994Const.DEFAULT.CHECK_ON,
    },
  } as Or26992Type,
  or26989: {
    // 認定区分
    certificationClass: props.modelValue.nintei,
  } as Or26989Type,
  or26990: {
    // 要支援区分
    certificationClass: props.modelValue.yoshienKbn,
  } as Or26990Type,
  or26991: {
    // 初回作成日
    value: props.modelValue.shokaiYmd,
  } as Or26991Type,
})

const localOneway = reactive({
  or26994OneWay: {
    ...props.onewayModelValue,
  },
  // 閉じるコンポーネント,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 基本的な事項ダイアログ
  mo00024Oneway: {
    width: '810px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or26994',
      toolbarTitle: t('label.basic-matter'),
      toolbarName: 'Or26994ToolBar',
      font: '24px',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'card-text pa-2',
    },
  } as Mo00024OnewayType,
  // 有効期間入力
  or26987Oneway: {
    ...defaultWay.or26987Oneway,
  } as Or26987OnewayType,
  // 委託の場合：計画作成事業者・事業所名及び所在地（連絡先）
  or26981Oneway: {
    ...defaultWay.or26981Oneway,
  } as Or26981OnewayType,
  // 認定区分
  or26989Oneway: { radioItems: [] as CodeType[] } as Or26989OnewayType,
  // 要支援区分
  or26990Oneway: { radioItems: [] as CodeType[] } as Or26990OnewayType,
  // 初回作成日
  or26991neway: {
    ...defaultWay.or26991Oneway,
  } as Or26991OnewayType,
})

const or26987 = ref({ uniqueCpId: '' })
const or26988 = ref({ uniqueCpId: '' })
const or26989 = ref({ uniqueCpId: '' })
const or26990 = ref({ uniqueCpId: '' })
const or26991 = ref({ uniqueCpId: '' })
const or26992 = ref({ uniqueCpId: '' })
const or26979 = ref({ uniqueCpId: '' })
const or26981 = ref({ uniqueCpId: '' })

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or26994Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or26994StateType>({
  cpId: Or26994Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or26994Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or26987Const.CP_ID(1)]: or26987.value,
  [Or26988Const.CP_ID(1)]: or26988.value,
  [Or26989Const.CP_ID(1)]: or26989.value,
  [Or26990Const.CP_ID(1)]: or26990.value,
  [Or26991Const.CP_ID(1)]: or26991.value,
  [Or26992Const.CP_ID(1)]: or26992.value,
  [Or26979Const.CP_ID(1)]: or26979.value,
  [Or26981Const.CP_ID(1)]: or26981.value,
})

onMounted(async () => {
  // 共通関数より汎用コード一覧を取得する
  await initCodes()
})

setChildCpBinds(props.uniqueCpId, {
  [Or26987Const.CP_ID(1)]: {
    twoWayValue: local.or26987,
  },
  [Or26988Const.CP_ID(1)]: {
    twoWayValue: local.or26988,
  },
  [Or26989Const.CP_ID(1)]: {
    twoWayValue: local.or26989,
  },
  [Or26990Const.CP_ID(1)]: {
    twoWayValue: local.or26990,
  },
  [Or26991Const.CP_ID(1)]: {
    twoWayValue: local.or26991,
  },
  [Or26992Const.CP_ID(1)]: {
    twoWayValue: local.or26992,
  },
  [Or26979Const.CP_ID(1)]: {
    twoWayValue: local.or26979,
  },
  [Or26981Const.CP_ID(1)]: {
    twoWayValue: local.or26981,
  },
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 認定区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CERTIFICATION_CATEGORY },
    // 要支援区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SUPPORT_REQUIRED_CATEGORY },
    // 要支援区分2
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SUPPORT_REQUIRED_CATEGORY_2 },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 認定区分
  localOneway.or26989Oneway.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CERTIFICATION_CATEGORY
  )

  // 要支援区分
  localOneway.or26990Oneway.radioItems = CmnSystemCodeRepository.filter(
    localOneway.or26994OneWay.itakuKkakPrtFlg === Or26994Const.DEFAULT.ITAKUKKAKPRT_FLG_0 ||
      localOneway.or26994OneWay.itakuKkakPrtFlg === Or26994Const.DEFAULT.ITAKUKKAKPRT_FLG_2
      ? CmnMCdKbnId.M_CD_KBN_ID_SUPPORT_REQUIRED_CATEGORY
      : CmnMCdKbnId.M_CD_KBN_ID_SUPPORT_REQUIRED_CATEGORY_2
  )
}

/**
 *  計画区分を取得し初期化
 *
 * @param kubun - 計画区分
 *
 * @param type - 初回/紹介/継続
 */
function getKubun(kubun: string, type: string) {
  // 初回
  if (type === Or26994Const.DEFAULT.PLAN_CATEGORY_FIRST_TIME) {
    if (
      kubun === Or26994Const.DEFAULT.PLAN_CATEGORY_1 ||
      kubun === Or26994Const.DEFAULT.PLAN_CATEGORY_4
    ) {
      return true
    } else {
      return false
    }
  }
  // 紹介
  else if (type === Or26994Const.DEFAULT.PLAN_CATEGORY_INTRODUCTION) {
    if (
      kubun === Or26994Const.DEFAULT.PLAN_CATEGORY_2 ||
      kubun === Or26994Const.DEFAULT.PLAN_CATEGORY_4 ||
      kubun === Or26994Const.DEFAULT.PLAN_CATEGORY_5
    ) {
      return true
    } else {
      return false
    }
  }
  // 継続
  else {
    if (
      kubun === Or26994Const.DEFAULT.PLAN_CATEGORY_3 ||
      kubun === Or26994Const.DEFAULT.PLAN_CATEGORY_5
    ) {
      return true
    } else {
      return false
    }
  }
}

/**
 * 計画区分を再設定
 *
 * @param data - 画面データ
 */
function analysisKubun(data: Or26988Type) {
  if (data.mo00018FirstTime.modelValue) {
    if (data.mo00018Introduction.modelValue) {
      // 計画区分 4:初回・紹介
      return Or26994Const.DEFAULT.PLAN_CATEGORY_4
    } else {
      // 計画区分 1:初回
      return Or26994Const.DEFAULT.PLAN_CATEGORY_1
    }
  } else if (data.mo00018Introduction.modelValue) {
    if (data.mo00018Continuation.modelValue) {
      // 計画区分 5:紹介・継続
      return Or26994Const.DEFAULT.PLAN_CATEGORY_5
    } else {
      // 計画区分 2:紹介
      return Or26994Const.DEFAULT.PLAN_CATEGORY_2
    }
  } else if (data.mo00018Continuation.modelValue) {
    // 計画区分 3:継続
    return Or26994Const.DEFAULT.PLAN_CATEGORY_3
  } else {
    // 計画区分 0:指定なし
    return Or26994Const.DEFAULT.PLAN_CATEGORY_0
  }
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    mo00024.value.isOpen = true
    // 画面データに変更がない場合、ダイアログを閉じます
    if (!newValue) {
      close()
    }
  }
)

/**
 * 認定情報入力ダイアログ戻り値処理
 */
watch(
  () => Or26987Logic.data.get(or26987.value.uniqueCpId),
  (newValue) => {
    local.or26990.certificationClass = newValue?.certificationClass
    if (newValue?.mo00018NursingCareInsurance) {
      local.or26992.mo00018NursingCareInsurance = newValue?.mo00018NursingCareInsurance
    }
    if (newValue?.mo00018RegionalSupportBusiness) {
      local.or26992.mo00018RegionalSupportBusiness = newValue?.mo00018RegionalSupportBusiness
    }
  }
)

/**
 * 保存処理
 */
function confirm() {
  const result: Or26994Type = {
    /** 担当地域包括支援センター */
    chiJigyoKnj: Or26979Logic.data.get(or26979.value.uniqueCpId)?.chiJigyoKnj.value ?? '',
    /** 計画作成者 */
    centerShokuKnj: Or26979Logic.data.get(or26979.value.uniqueCpId)?.centerShokuKnj.value ?? '',
    /** 委託の場合の作成事業者センター */
    itkJigyoKnj: Or26981Logic.data.get(or26981.value.uniqueCpId)?.itkJigyoKnj.value ?? '',
    /** 委託先の作成者（職員ID） */
    itakuShokuId: Or26981Logic.data.get(or26981.value.uniqueCpId)?.itakuShokuId ?? '',
    /** 委託先の作成者 */
    itakuShokuKnj: Or26981Logic.data.get(or26981.value.uniqueCpId)?.itakuShokuKnj.value ?? '',
    /** 認定日 */
    ninteiYmd: Or26987Logic.data.get(or26987.value.uniqueCpId)?.ninteiYmd.value ?? '',
    /** 認定の有効期間開始日 */
    yukoSYmd: Or26987Logic.data.get(or26987.value.uniqueCpId)?.yukoSYmd.value ?? '',
    /** 認定の有効期間終了日 */
    yukoEYmd: Or26987Logic.data.get(or26987.value.uniqueCpId)?.yukoEYmd.value ?? '',
    /** 計画区分 */
    kubun: analysisKubun(Or26988Logic.data.get(or26988.value.uniqueCpId)!),
    /** 認定区分 */
    nintei: Or26989Logic.data.get(or26989.value.uniqueCpId)?.certificationClass ?? '',
    /** 要支援区分 */
    yoshienKbn: Or26990Logic.data.get(or26990.value.uniqueCpId)?.certificationClass ?? '',
    /** 初回作成日 */
    shokaiYmd: Or26991Logic.data.get(or26991.value.uniqueCpId)?.value ?? '',
    /** 利用サービス（介護保険） */
    service1Flg: Or26992Logic.data.get(or26992.value.uniqueCpId)?.mo00018NursingCareInsurance
      .modelValue
      ? Or26994Const.DEFAULT.CHECK_ON
      : Or26994Const.DEFAULT.CHECK_OFF,
    /** 利用サービス（地域支援事業） */
    service2Flg: Or26992Logic.data.get(or26992.value.uniqueCpId)?.mo00018RegionalSupportBusiness
      .modelValue
      ? Or26994Const.DEFAULT.CHECK_ON
      : Or26994Const.DEFAULT.CHECK_OFF,
  }
  emit('update:modelValue', result)
  close()
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-card
        variant="outlined"
        color="#CCCCCC"
        class="sizeCard"
      >
        <!-- 担当地域包括支援センター -->
        <g-custom-or-26979 v-bind="or26979" />
      </c-v-card>
      <c-v-card
        variant="outlined"
        color="#CCCCCC"
        class="sizeCard mt-2"
      >
        <!-- 委託の場合：計画作成事業者・事業所名及び所在地（連絡先） -->
        <g-custom-or-26981
          v-bind="or26981"
          :unique-cp-id="or26981.uniqueCpId"
          :oneway-model-value="localOneway.or26981Oneway"
        />
      </c-v-card>
      <c-v-card
        variant="outlined"
        color="#CCCCCC"
        class="sizeCard mt-2"
      >
        <c-v-sheet class="view">
          <c-v-row no-gutters>
            <c-v-col
              cols="auto"
              class="display-col"
            >
              <!-- 認定年月日 -->
              <g-custom-or-26987
                v-bind="or26987"
                :unique-cp-id="or26987.uniqueCpId"
                :oneway-model-value="localOneway.or26987Oneway"
              />
            </c-v-col>
            <c-v-col cols="auto"> </c-v-col>
          </c-v-row>
        </c-v-sheet>
      </c-v-card>
      <c-v-sheet class="view mt-2">
        <c-v-row no-gutters>
          <c-v-col
            cols="auto"
            class="display-col"
          >
            <!-- 計画区分 -->
            <g-custom-or-26988 v-bind="or26988" />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="display-col ml-2"
          >
            <!-- 認定区分 -->
            <g-custom-or-26989
              v-bind="or26989"
              :unique-cp-id="or26989.uniqueCpId"
              :oneway-model-value="localOneway.or26989Oneway"
            />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="display-col ml-2 box-width"
          >
            <!-- 要支援区分 -->
            <g-custom-or-26990
              v-bind="or26990"
              :unique-cp-id="or26990.uniqueCpId"
              :oneway-model-value="localOneway.or26990Oneway"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mt-2"
        >
          <c-v-card
            variant="outlined"
            color="#CCCCCC"
            class="sizeCard padding-none"
          >
            <c-v-col
              cols="auto"
              class="display-col padding-none"
            >
              <!-- 初回作成日 -->
              <g-custom-or26991 v-bind="or26991" />
            </c-v-col>
          </c-v-card>
          <c-v-col
            v-if="
              localOneway.or26994OneWay.itakuKkakPrtFlg ===
                Or26994Const.DEFAULT.ITAKUKKAKPRT_FLG_0 ||
              localOneway.or26994OneWay.itakuKkakPrtFlg === Or26994Const.DEFAULT.ITAKUKKAKPRT_FLG_2
            "
            cols="auto"
            class="display-col padding-none"
          >
            <!-- 利用サービス -->
            <g-custom-or-26992 v-bind="or26992" />
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="confirm"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.screen-close')"
          />
        </base-mo00611>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped>
.view {
  min-width: 520px;
  display: flex;
  margin: 0px;
  padding: 0px;
  flex-direction: column;

  .v-row {
    margin: unset;
  }
}
.display-col {
  display: flex;
}
.sizeCard {
  padding: 8px;
}
:deep(.v-toolbar-title__placeholder) {
  width: 700px;
}
.padding-none {
  padding: 0 !important;
}
.box-width {
  min-width: 347px;
}
</style>
