/**
 * OrX0068の週間表イメージ初期情報取得のタイプ
 * GUI00978_週間表イメージ
 *
 * @description
 * 初期情報取得のタイプ
 *
 * <AUTHOR>
 */
export interface WeekTableSelectOutData {
    /** 週間表ID */
    week1Id: string
    /** 期間管理フラグ */
    kikanFlag: string
    /** 新規用有効期間ID */
    termNewId: string
    /** 期間リスト */
    taishokikanList: KikanObj[]
    /** 期間インデックス */
    kikanIndex: string
    /** 期間総件数 */
    kikanAllCount: string
    /** 履歴リスト */
    week1List: RirekiObj[]
    /** 履歴インデックス */
    rirekiIndex: string
    /** 履歴総件数 */
    rirekiAllCount: string
    /** 詳細リスト */
    week2List: WeekTableList[]
    /** 日課利用フラグ */
    dayFlg: string
    /** 利用表利用フラグ */
    riyoFlg: string
}
/** 計画期間情報 */
export interface KikanObj {
  /** 開始日 */
  startYmd: string
  /** 終了日 */
  endYmd: string
  /** 期間ID */
  sc1Id: string
}
/**
 * 履歴情報
 */
export interface RirekiObj {
  /** 週間表ID */
  week1Id: string
  /** 期間ID */
  sc1Id: string
  /** 作成日 */
  createYmd: string
  /** 職員ID */
  shokuId: string
  /** 作成者 */
  shokuKnj: string
  /** ケースNo. */
  caseNo: string
  /** 当該年月 */
  tougaiYm: string
  /** 有効期間ID */
  termid: string
  /** 改訂フラグ */
  kaiteiFlg: string
  /** 週単位以外サービス */
  wIgaiKnj: string
}

/**
 * 詳細リスト
 */
export interface WeekTableList {
  /** 詳細ID */
  week2Id?: string
  /** 週間表ID */
  week1Id?: string
  /** 利用者ＩＤ */
  userid?: string
  /** 曜日 */
  youbi?: ModelValue
  /** 開始時間 */
  kaishiJikan?: ModelValue
  /** 終了時間 */
  shuuryouJikan?: ModelValue
  /** 内容CD */
  naiyoCd?: ModelValue
  /** 内容 */
  naiyoKnj?: ModelValue
  /** メモ */
  memoKnj?: ModelValue
  /** 文字サイズ */
  fontSize?: ModelValue
  /** 表示モード */
  dispMode?: ModelValue
  /** 文字位置 */
  alignment?: ModelValue
  /** サービス種類 */
  svShuruiCd?: ModelValue
  /** サービス項目（台帳） */
  svItemCd?: ModelValue
  /** サービス事業者CD */
  svJigyoId?: ModelValue
  /** サービス種類名称 */
  svShuruiKnj?: ModelValue
  /** サービス項目名称 */
  svItemKnj?: ModelValue
  /** サービス事業者名称 */
  svJigyoKnj?: ModelValue
  /** サービス事業者略称 */
  svJigyoRks?: ModelValue
  /** 文字カラー */
  fontColor?: ModelValue
  /** 背景カラー */
  backColor?: ModelValue
  /** 時間表示区分 */
  timeKbn?: ModelValue
  /** 週単位以外文字 */
  igaiMoji?: ModelValue
  /** 週単位以外のサービス区分 */
  igaiKbn?: ModelValue
  /** 週単位以外のサービス（日付指定） */
  igaiDate?: ModelValue
  /** 週単位以外のサービス（曜日指定） */
  igaiWeek?: ModelValue
  /** 福祉用具貸与の単価 */
  svTani: string
  /** 福祉用具貸与マスタID */
  fygId: string
  /** 枠外表示するかのフラグ */
  wakugaiFlg: string
  /** 更新区分 */
  updateKbn: string
  /** 加算リスト */
  week3List: Week3List
}
/**
 * 加算リスト
 */
export interface Week3List{
  /** 加算データID */
  id: string
  /** 加算サービス名 */
  svKasanKnj: string
  /** 加算サービス事業者ID */
  svJigyoId: string
  /** 加算サービス項目ID */
  svItemCd: string
  /** 回数 */
  kaisuu: string
  /** 福祉用具貸与の単価 */
  svTani: string
  /** 福祉用具貸与マスタID */
  fygId: string
}
/**
 * ModelValue構造
 */
export interface ModelValue {
  /**
   * value
   */
  value: string
}
