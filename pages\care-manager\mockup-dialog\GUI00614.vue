<script setup lang="ts">
import { computed, definePageMeta, ref, useScreenStore } from '#imports'
import { Or60400Logic } from '~/components/custom-components/organisms/Or60400/Or60400.logic'
import type { Or60400OnewayType } from '~/types/cmn/business/components/Or60400Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00614'
// ルーティング
const routing = 'GUI00614/pinia'
// 画面物理名
const screenName = 'GUI00614'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or60400 = ref({ uniqueCpId: '' })

const info = ref({
  value: JSON.stringify(
    {
      svJigyoId: '21',
      selWeek: '2017/02',
      objYm: '2017/02',
      tantoId: '1',
      chkShokuId: '1',
      screenNm: '1',
      shokuinId: '1',
      sysCd: '1',
    },
    null,
    2
  ),
})

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00614' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or60400Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or60400.value.uniqueCpId = pageComponent.uniqueCpId

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or60400Data = ref<Or60400OnewayType>({
  svJigyoId: '',
  selWeek: '',
  objYm: '',
  tantoId: '',
  chkShokuId: '6',
  screenNm: '',
  shokuinId: '',
  sysCd: '',
})

/**
 *  ボタン押下時の処理
 *
 */
function or60400nClick() {
  // Or060400のダイアログ開閉状態を更新する
  Or60400Logic.state.set({
    uniqueCpId: or60400.value.uniqueCpId,
    state: { isOpen: true },
  })
}
// ダイアログ表示フラグ
const showDialogOr60400 = computed(() => {
  // Or060400のダイアログ開閉状態
  return Or60400Logic.state.get(or60400.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ボタン押下時の処理
 *
 */
function onClick() {
  const tem = { ...JSON.parse(info.value.value) } as Or60400OnewayType
  or60400Data.value = {
    ...or60400Data.value,
    ...tem,
  }
  Or60400Logic.state.set({
    uniqueCpId: or60400.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ CD 2025/06/16 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or60400nClick()"
      >
        GUI00614_週間計画一括取込
      </v-btn>
      <g-custom-or-60400
        v-if="showDialogOr60400"
        v-bind="or60400"
        :oneway-model-value="or60400Data"
      />
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ CD 2025/06/16 ADD END-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row
    no-gutters
    class="content pb-2"
  >
    <div class="w-25 h-75 pl-2">
      <div>データ</div>
      <base-mo00046
        v-model="info"
        :oneway-model-value="{
          showItemLabel: false,
        }"
      />
    </div>
    <div class="mb-2">
      <v-btn @click="onClick()"> GUI00614_週間計画一括取込 </v-btn>
    </div>
  </c-v-row>
</template>
