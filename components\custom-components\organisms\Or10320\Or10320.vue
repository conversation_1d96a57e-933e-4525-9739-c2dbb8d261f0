<script setup lang="ts">
/**
 * Or10320:有機体:モーダル（画面/特殊コンポーネント）
 * GUI00627_［アセスメント（インターライ）マスタ］画面
 *
 * @description
 * アセスメント（インターライ）マスタ
 *
 * <AUTHOR>
 */
import { onMounted, reactive, watch, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10320Const } from './Or10320.constants'

import type { Or10320StateType, Or10320RefType } from './Or10320.type'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenTwoWayBind,
  useSystemCommonsStore,
  useScreenStore,
} from '#imports'
import type { Or10320OnewayType } from '~/types/cmn/business/components/Or10320Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { CustomClass } from '~/types/CustomClassType'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  IAssessmentInterraiMasterSelectInEntity,
  IAssessmentInterraiMasterSelectOutEntity,
  IAssessmentInterraiMasterInsertInEntity,
  IAssessmentInterraiMasterInsertOutEntity,
  InitialSettings,
} from '~/repositories/cmn/entities/AssessmentInterraiMasterEntity'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { useJigyoList } from '~/utils/useJigyoList'
import { useColorUtils } from '~/utils/useColorUtils'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
const { convertDecimalToHex } = useColorUtils()

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10320OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or10320RefType>({
  cpId: Or10320Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
const or21814 = ref({ uniqueCpId: '' })
const or41179 = ref({ uniqueCpId: '' })

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const { jigyoListWatch, updateJigyoList } = useJigyoList()

// 処理区分（noSaveAuthority:保存権限なし、close:画面閉じる、tabClick:タブ押下）
const processCategory = ref('')
// メモの文字サイズ_リスト
const memoLetterSizeList = ref<CodeType[]>([])
// メモの文字色表示_リスト
const memoLetterColorList = ref<CodeType[]>([])

const colorStyle = ref({
  width: '32px',
  height: '32px',
  'background-color': '#000000',
  cursor: 'pointer',
})
const isEdit = computed(() => useScreenStore().isEditNavControl())
// 事業所IDバックアップ
const jigyoIdOld = ref('')

const isInit = ref(false) // 初期化フラグ

// メモの文字サイズ
const mo01338onewayMemoLetterSize = ref<string>('0')

// メモの文字色
const memoLetterColor = ref<string>('')

const local = reactive({
  /** 親画面.事業者ID */
  svJigyoId: props.onewayModelValue.svJigyoId,
  /** 親画面.施設ID */
  shisetuId: props.onewayModelValue.shisetuId,
})

const localOneway = reactive({
  or10320: {
    ...props.onewayModelValue,
  },
  mo00615onewayInit: {
    itemLabel: t('label.initial-value'),
    itemLabelFontWeight: 'bold',
    customClass: {
      labelClass: 'customLabel',
    } as CustomClass,
    itemLabelCustomClass: {
      outerClass: '',
      labelClass: 'mt-1',
      labelStyle: 'letter-spacing: 8px',
    } as CustomClass,
  } as Mo00615OnewayType,
  mo01338onewayMemoLetterSize: {
    value: t('label.memo-letter-size'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'd-flex align-center',
      itemStyle: 'height: 36px',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338onewayMemoLetterSizeRadioGroup: {
    name: t('label.memo-letter-size'),
    showItemLabel: false,
    inline: true,
    customClass: {
      outerClass: '',
      labelClass: '',
    } as CustomClass,
  } as Mo00039OnewayType,
  mo01338onewayMemoLetterColor: {
    value: t('label.memo-letter-color'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'd-flex align-center',
      itemStyle: 'height: 36px',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338onewayFooter: {
    value: t('label.office-unit-save'),
    customClass: {
      itemClass: 'footerLabel',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00611Oneway: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.save'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.save'),
  } as Mo00609OnewayType,
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10320StateType>({
  cpId: Or10320Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    executeFlag: (value) => {
      switch (value) {
        case 'save':
          void save()
          setState({ executeFlag: '' })
          break
        default:
          break
      }
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(() => {
  // 汎用コードマスタデータを取得し初期化
  void initCodes()
  updateJigyoList(or41179.value.uniqueCpId, true)
  void jigyoChange(localOneway.or10320.svJigyoId)
  void init()
  //   ・共通情報.事業所IDが事業所情報リストに存在する場合、共通情報.事業所IDの事業所名
  let svJigyoId = ''
  if (systemCommonsStore.getSvJigyoIdList?.includes(local.svJigyoId)) {
    svJigyoId = local.svJigyoId
  }
  // ・上記以外の場合、事業所情報リスト.1件目.事業所IDの事業所名
  else if (systemCommonsStore.getSvJigyoIdList?.length) {
    svJigyoId = systemCommonsStore.getSvJigyoIdList[0]
  }
  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
      verticalLayout: true,
    },
  })
  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: {
      modelValue: svJigyoId,
    },
  })
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
})

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId === jigyoIdOld.value) {
    return
  }

  // まず変更前の事業所を保持
  setJigyo(jigyoIdOld.value)

  // 事業所変更処理
  void jigyoChange(newJigyoId)
}

/**
 * 事業所変更処理
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function jigyoChange(newJigyoId: string) {
  // 画面変更チェック
  if (isEdit.value && isInit.value) {
    // 確認ダイアログ表示
    const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
    switch (dialogResult) {
      case 'yes': {
        // はい選択時は入力内容を保存する
        await save()
        local.svJigyoId = newJigyoId
        jigyoIdOld.value = newJigyoId
        // 変更後の事業所に設定
        setJigyo(newJigyoId)
        void init()

        break
      }
      case 'no':
        // いいえ選択時は編集内容を破棄するので何もしない
        local.svJigyoId = newJigyoId
        jigyoIdOld.value = newJigyoId
        // 変更後の事業所に設定
        setJigyo(newJigyoId)
        void init()
        break
      case 'cancel':
        // キャンセル選択時は一覧の選択を戻す
        break
    }

    return
  } else {
    local.svJigyoId = newJigyoId
    jigyoIdOld.value = newJigyoId
    // 変更後の事業所に設定
    setJigyo(newJigyoId)
    void init()
  }
}

/**
 * 事業所を選択
 *
 * @param jigyoId - 事業所ID
 */
function setJigyo(jigyoId: string) {
  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    },
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog1(paramDialogText: string): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'cancel' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

//メッセージの選択結果を監視
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 処理区分が「タブ押下」の場合
    if (processCategory.value === 'tabClick') {
      if (newValue.firstBtnClickFlg) {
        // AC009処理を行う。
        void save()
      }
    } else if (processCategory.value === 'close') {
      if (newValue.firstBtnClickFlg) {
        // AC009処理を行う。
        void save()
      }
    } else if (processCategory.value === 'noSaveAuthority') {
      if (newValue.firstBtnClickFlg) {
        void init()
      }
    }
  }
)

watch(
  () => isEdit.value,
  (newValue) => {
    if (newValue) {
      refValue.value!.memoChangeFlag = true
    }
  }
)

/**
 * メモの文字色変更
 */
watch(
  () => refValue.value?.memoLetterColor ?? '',
  (newValue) => {
    colorStyle.value['background-color'] = convertDecimalToHex(parseInt(newValue)) ?? ''
  }
)

/**
 * AC001_初期情報取得(アセスメント（インターライ）マスタ情報を取得する)
 */
async function init() {
  isInit.value = false
  const inputData: IAssessmentInterraiMasterSelectInEntity = {
    // 施設ID
    shisetuId: local.shisetuId,
    // 事業所ID
    svJigyoId: local.svJigyoId,
  }
  const ret: IAssessmentInterraiMasterSelectOutEntity = await ScreenRepository.select(
    'assessmentInterraiMasterSelect',
    inputData
  )
  // メモの文字サイズ：1（普通）を初期値とする
  let size = Or10320Const.DEFAULT.MEMO_LETTER_SIZE_NORMAL
  // メモの文字色：0（黒）を初期値とする
  let color = Or10320Const.DEFAULT.MEMO_LETTER_COLOR_BLACK
  if (ret.data && ret.data.initialSettingsList?.length > 0) {
    for (const item of ret.data.initialSettingsList) {
      // APIから取得に分類３「1：メモ文字サイズ」対応の整数値
      if (item.bunrui3Id === Or10320Const.BUNRUI3ID_MEMO_LETTER_SIZE) {
        size = item.intValue
        // APIから取得に分類３「2：メモ文字色」対応の整数値
      } else if (item.bunrui3Id === Or10320Const.BUNRUI3ID_MEMO_LETTER_COLOR) {
        color = item.intValue
      }
    }
  }
  mo01338onewayMemoLetterSize.value = size
  memoLetterColor.value = color
  const data = {
    mo01338onewayMemoLetterSize: size,
    memoLetterColor: color,
    memoChangeFlag: false,
  } as Or10320RefType
  const colorItem: InitialSettings | undefined = ret.data.initialSettingsList.find(
    (item) => item.bunrui3Id === Or10320Const.BUNRUI3ID_MEMO_LETTER_COLOR
  )
  if (colorItem) {
    colorStyle.value['background-color'] = convertDecimalToHex(parseInt(colorItem.intValue)) ?? ''
  }

  useScreenStore().setCpTwoWay({
    cpId: Or10320Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: data,
    isInit: true,
  })
  isInit.value = true
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // メモの文字サイズ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_SIZE },
    // メモの文字色表示
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_COLOR },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // メモの文字サイズ選択肢
  memoLetterSizeList.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_SIZE
  )
  // メモの文字色選択肢
  memoLetterColorList.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_COLOR
  )
}

/**
 * AC009_「保存ボタン」押下
 * 「保存」ボタン押下
 */
async function save() {
  // 変更ありの項目データを保存する

  const inputData: IAssessmentInterraiMasterInsertInEntity = {
    // 施設ID
    shisetuId: local.shisetuId,
    // 事業所ID
    svJigyoId: local.svJigyoId,
    // 分類1
    bunrui1Id: Or10320Const.BUNRUI1ID_TWO,
    // 分類2
    bunrui2Id: Or10320Const.BUNRUI2ID_THIRTY,
    // 分類3設定リスト
    bunrui3SetList: [
      {
        //  分類３「1：メモ文字サイズ」
        bunrui3Id: Or10320Const.BUNRUI3ID_MEMO_LETTER_SIZE,
        // メモの文字サイズ
        bunrui3Value: refValue.value!.mo01338onewayMemoLetterSize,
      },
      {
        //  分類３「2：メモ文字色」
        bunrui3Id: Or10320Const.BUNRUI3ID_MEMO_LETTER_COLOR,
        // メモの文字色
        bunrui3Value: refValue.value!.memoLetterColor,
      },
    ],
  }
  const resData: IAssessmentInterraiMasterInsertOutEntity = await ScreenRepository.insert(
    'assessmentInterraiMasterInsert',
    inputData
  )

  if (resData.statusCode === 'success') {
    void init()
  }
}
</script>

<template>
  <c-v-row>
    <c-v-col class="pb-0">
      <div class="py-2">
        <g-base-or-41179 v-bind="or41179" />
      </div>
      <c-v-divider />
      <div class="first-box"></div>

      <c-v-card class="assessmentCard">
        <c-v-card-text class="w-auto flex-0-0">
          <c-v-row
            no-gutter
            class="content"
          >
            <div
              style="width: 50px; height: 100px"
              class="initValue d-flex align-center justify-left pl-2"
            >
              <base-mo00615
                :oneway-model-value="localOneway.mo00615onewayInit"
                class="titleLabel"
              ></base-mo00615>
            </div>
            <c-v-col>
              <c-v-row
                no-gutter
                class="letterSize"
              >
                <div
                  class="letterSizeLabel d-flex align-center"
                  style="padding-left: 8px !important; width: 200px"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338onewayMemoLetterSize"
                    class="titleLabel"
                  ></base-mo01338>
                </div>
                <c-v-col class="d-flex align-center">
                  <base-mo00039
                    v-model="refValue!.mo01338onewayMemoLetterSize"
                    :oneway-model-value="localOneway.mo01338onewayMemoLetterSizeRadioGroup"
                  >
                    <base-at-radio
                      v-for="(item, index) in memoLetterSizeList"
                      :key="'or10320-' + index"
                      :name="'or10320-radio-' + index"
                      :radio-label="item.label"
                      :value="item.value"
                      class="radioItem"
                    />
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
              <c-v-row no-gutter>
                <div
                  class="letterColorLabel d-flex align-center"
                  style="padding-left: 8px !important; width: 200px"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338onewayMemoLetterColor"
                    class="titleLabel"
                  ></base-mo01338>
                </div>
                <c-v-col class="letterSize d-flex align-center">
                  <div
                    class="pl-2"
                    style="display: inline-flex; align-items: center"
                  >
                    <!-- メモの文字色表示 -->
                    <div
                      :style="colorStyle"
                      class="elevation-2"
                    ></div>
                    <!-- メモの文字色リスト -->
                    <base-at-select
                      v-model="refValue!.memoLetterColor"
                      name="selectColor"
                      :items="memoLetterColorList"
                      item-title="label"
                      item-value="value"
                      label="Compact"
                      single-line
                      class="ml-2"
                      style="width: 160px"
                    ></base-at-select>
                  </div>
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
        </c-v-card-text>
      </c-v-card>
      <div class="second-box"></div>
      <base-mo01338 :oneway-model-value="localOneway.mo01338onewayFooter"></base-mo01338>
      <g-base-or21814
        v-if="showDialogOr21814"
        v-bind="or21814"
      >
      </g-base-or21814>
    </c-v-col>
  </c-v-row>
</template>

<style scoped lang="scss">
$font-size-comment: 12px;
$line-height-content: 50px;

:deep(.v-card-item) {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  padding: 8px 0 !important;
}

:deep(.v-input__details) {
  display: none;
}

:deep(.v-card-text) {
  padding: 0;
  border-left: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.assessmentCard {
  box-shadow: none;
}

.initValue,
.letterSizeLabel,
.letterColorLabel {
  height: $line-height-content;
  background-color: rgb(var(--v-theme-background));
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

:has(> .customLabel .v-col) {
  writing-mode: vertical-lr !important;
}

div:has(> .footerLabel) {
  :nth-child(2) {
    :first-child {
      :first-child {
        color: rgb(var(--v-theme-subText)) !important;
        font-size: $font-size-comment !important;
      }
    }
  }
}

.radioItem {
  width: 150px;
}

.titleLabel {
  background-color: rgb(var(--v-theme-background));
}

.letterSize {
  height: $line-height-content;
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.content {
  border-top: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  margin: 8px 0;
  :deep(.v-col) {
    padding: 0px !important;
  }
  :deep(.v-row) {
    margin: 0;
  }
}

.first-box {
  height: 100px;
}

.second-box {
  height: 185px;
}
</style>
