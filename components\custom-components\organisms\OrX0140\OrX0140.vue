<script setup lang="ts">
/**
 * OrX0140Logic:有機体:主治医意見書コンテンツタブ
 * GUI01286_主治医意見書
 *
 * @description
 * 主治医意見書コンテンツタブ
 *
 * <AUTHOR>
 */
import { reactive, onMounted, watch, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or29757Logic } from '../Or29757/Or29757.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { OrX0146Const } from '../OrX0146/OrX0146.constants'
import { Or29758Const } from '../Or29758/Or29758.constants'
import { Gui00059Logic } from '../Gui00059/Gui00059.logic'
import { Gui00059Const } from '../Gui00059/Gui00059.constants'
import type { Gui00059EventType } from '../Gui00059/Gui00059.type'
import { OrX0140Const } from './OrX0140.constants'
import { Or10545Const } from '~/components/custom-components/organisms/Or10545/Or10545.constants'
import { Or10545Logic } from '~/components/custom-components/organisms/Or10545/Or10545.logic'
import { useSetupChildProps } from '#imports'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00039Items, Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Or10545OnewayType, Or10545Type } from '~/types/cmn/business/components/Or10545Type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { OrX0146OnewayType } from '~/types/cmn/business/components/OrX0146Type'
import type { OrX0140Type } from '~/types/cmn/business/components/OrX0140Type'
import type { AttendingPhysicianStatementRirekiComInfo } from '~/repositories/cmn/entities/AttendingPhysicianStatementSelectEntity'
import type { OrX0157OnewayType } from '~/types/cmn/business/components/OrX0157Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import { useValidation } from '@/utils/useValidation'

const { t } = useI18n()
const { byteLength } = useValidation()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  orX0140: {
    // ＩＤ
    iks2Id: '',
    // 医師意見
    ishiDoui: '',
    // 医師名前
    ishiNameKnj: { value: '' },
    // 医療機関TEL
    ishiTel: { value: '' },
    // 医療機関名
    iryouKikanKnj: { value: '' },
    // 医療機関FAX
    ishiFax: { value: '' },
    // 最終診察日付
    sinsatuYmd: {
      value: '',
      mo01343: {},
    },
    // 意見書回数
    ikenshoKaisuu: '',
    // 他科受診
    elseJusinFlg: '',
    // 内科選択フラッグ
    elseJusin1Flg: { modelValue: false },
    // 精神科選択フラッグ
    elseJusin2Flg: { modelValue: false },
    // 外科選択フラッグ
    elseJusin3Flg: { modelValue: false },
    // 整形外科選択フラッグ
    elseJusin4Flg: { modelValue: false },
    // 脳神精外科選択フラッグ
    elseJusin5Flg: { modelValue: false },
    // 皮膚科選択フラッグ
    elseJusin6Flg: { modelValue: false },
    // 泌尿器科選択フラッグ
    elseJusin7Flg: { modelValue: false },
    // 婦人科選択フラッグ
    elseJusin8Flg: { modelValue: false },
    // 眼科選択フラッグ
    elseJusin9Flg: { modelValue: false },
    // 耳鼻咽喉科選択フラッグ
    elseJusin10Flg: { modelValue: false },
    // リハビリテーション科選択フラッグ
    elseJusin11Flg: { modelValue: false },
    // 歯科選択フラッグ
    elseJusin12Flg: { modelValue: false },
    // その他選択フラッグ
    elseJusin13Flg: { modelValue: false },
    // その他科の名前
    elseJusinKnj: { value: '' },
    // 診断名前1
    sindanName1Knj: { value: '' },
    // 診断1の日付
    sindan1Ymd: {
      value: '',
      mo01343: {},
    },
    // 診断名前2
    sindanName2Knj: { value: '' },
    // 診断2の日付
    sindan2Ymd: {
      value: '',
      mo01343: {},
    },
    // 診断名前3
    sindanName3Knj: { value: '' },
    // 診断3の日付
    sindan3Ymd: {
      value: '',
      mo01343: {},
    },
    // 安定
    anteiFlg: '',
    // 不安定内容
    huanteiKnj: '',
    // 生活機能低下原因
    reasonMemoKnj: { value: '' },
    // 特別医療関連行為1
    tokubetu1Flg: { modelValue: false },
    // 特別医療関連行為2
    tokubetu2Flg: { modelValue: false },
    // 特別医療関連行為3
    tokubetu3Flg: { modelValue: false },
    // 特別医療関連行為4
    tokubetu4Flg: { modelValue: false },
    // 特別医療関連行為5
    tokubetu5Flg: { modelValue: false },
    // 特別医療関連行為6
    tokubetu6Flg: { modelValue: false },
    // 特別医療関連行為7
    tokubetu7Flg: { modelValue: false },
    // 特別医療関連行為8
    tokubetu8Flg: { modelValue: false },
    // 特別医療関連行為9
    tokubetu9Flg: { modelValue: false },
    // 特別医療関連行為10
    tokubetu10Flg: { modelValue: false },
    // 特別医療関連行為11
    tokubetu11Flg: { modelValue: false },
    // 特別医療関連行為12
    tokubetu12Flg: { modelValue: false },
    // 障害高齢者の日常生活自立度
    neta1Cd: '',
    // 認知症高齢者の日常生活自立度
    neta2Cd: '',
    // 短期記憶
    chukaku1Flg: '',
    // 認知能力
    chukaku2Flg: '',
    // 伝達能力
    chukaku3Flg: '',
    // 周辺症状フラッグ
    shuhenFlg: '',
    // 周辺症状1選択フラッグ
    shuhen1Flg: { modelValue: false },
    // 周辺症状2選択フラッグ
    shuhen2Flg: { modelValue: false },
    // 周辺症状3選択フラッグ
    shuhen3Flg: { modelValue: false },
    // 周辺症状4選択フラッグ
    shuhen4Flg: { modelValue: false },
    // 周辺症状5選択フラッグ
    shuhen5Flg: { modelValue: false },
    // 周辺症状6選択フラッグ
    shuhen6Flg: { modelValue: false },
    // 周辺症状7選択フラッグ
    shuhen7Flg: { modelValue: false },
    // 周辺症状8選択フラッグ
    shuhen8Flg: { modelValue: false },
    // 周辺症状9選択フラッグ
    shuhen9Flg: { modelValue: false },
    // 周辺症状10選択フラッグ
    shuhen10Flg: { modelValue: false },
    // 周辺症状11選択フラッグ
    shuhen11Flg: { modelValue: false },
    // 周辺症状12その他選択フラッグ
    shuhen12Flg: { modelValue: false },
    // 周辺症状12その他
    shuhen12Knj: { value: '' },
    // 精神症状フラッグ
    seisinFlg: '',
    // 症状名前
    seisinKnj: { value: '' },
    // 受診フラグ
    seisinJusinFlg: '',
    // 受診科の名前
    seisinJusinKnj: { value: '' },
    // 更新回数
    modifiedCnt: '',
  } as OrX0140Type,
  rirekiObj: {} as AttendingPhysicianStatementRirekiComInfo,
  or10545: { sickYmd: '', sickKnj: '', keizokuKbn: '' } as Or10545Type,
  disabled: false,
})

const localOneway = reactive({
  mo01299Oneway1: {
    title: t('label.employment-context'),
  },
  mo00615Oneway1: {
    itemLabel: t('label.basic-investigation-four8-three'),
  } as Mo00615OnewayType,
  mo00615Oneway2: {
    itemLabel: '',
  } as Mo00615OnewayType,
  mo00039Oneway1: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo00615Oneway3: {
    itemLabel: t('label.attending-physician'),
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'text-decoration: underline;color: #214D97;font-weight:blod',
    }),
  } as Mo00615OnewayType,
  mo00615Oneway4: {
    itemLabel: t('label.ishi-Name'),
  } as Mo00615OnewayType,
  mo00045Oneway1: {
    itemLabel: '',
    width: '500px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-2' }),
    maxLength: '30',
    rules: [byteLength(30)],
    disabled: false,
  } as Mo00045OnewayType,
  mo00615Oneway5: {
    itemLabel: t('label.tel'),
  } as Mo00615OnewayType,
  mo00045Oneway2: {
    itemLabel: '',
    width: '200px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-2' }),
    maxLength: '15',
    rules: [byteLength(15)],
    disabled: false,
  } as Mo00045OnewayType,
  mo00615Oneway6: {
    itemLabel: t('label.medical_institution_name'),
  } as Mo00615OnewayType,
  mo00045Oneway3: {
    itemLabel: '',
    width: '500px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-2' }),
    maxLength: '60',
    rules: [byteLength(60)],
    disabled: false,
  } as Mo00045OnewayType,
  mo00615Oneway7: {
    itemLabel: t('label.fax_label'),
  } as Mo00615OnewayType,
  mo00045Oneway4: {
    itemLabel: '',
    width: '200px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-2' }),
    maxLength: '15',
    rules: [byteLength(15)],
    disabled: false,
  } as Mo00045OnewayType,
  mo00615Oneway8: {
    itemLabel: t('label.iryou-Kikan-Address'),
  } as Mo00615OnewayType,
  mo00046Oneway3: {
    name: 'huanteiKnj',
    itemLabel: '',
    rows: 1,
    hideDetails: true,
    showItemLabel: false,
    maxlength: '90',
    rules: [byteLength(90)],
    customClass: new CustomClass({ itemStyle: 'width: 500px' }),
    disabled: false,
    noResize: true,
  } as Mo00046OnewayType,
  mo00615Oneway9: {
    itemLabel: t('label.sinsatu-Ymd'),
  } as Mo00615OnewayType,
  mo00020Oneway1: {
    itemLabel: '',
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '161',
    disabled: false,
    customClass: new CustomClass({ outerStyle: 'margin-left: 12px' }),
  } as Mo00020OnewayType,
  mo00615Oneway10: {
    itemLabel: t('label.ikensho-Kaisuu'),
  } as Mo00615OnewayType,
  mo00039Oneway2: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
    customClass: new CustomClass({ outerStyle: 'margin-left: 12px' }),
  } as Mo00039OnewayType,
  mo00615Oneway11: {
    itemLabel: t('label.elseJusin-Flg'),
  } as Mo00615OnewayType,
  mo00039Oneway3: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
    customClass: new CustomClass({ outerStyle: 'margin-left: 12px;margin-top: 8px' }),
  } as Mo00039OnewayType,
  mo00615Oneway12: {
    itemLabel: t('label.case'),
    customClass: new CustomClass({
      outerStyle: 'align-items: center;height: 17px;',
    }),
  } as Mo00615OnewayType,
  mo00018Oneway1: {
    showItemLabel: false,
    checkboxLabel: t('label.internal-medicine'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway2: {
    showItemLabel: false,
    checkboxLabel: t('label.psychiatry'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway3: {
    showItemLabel: false,
    checkboxLabel: t('label.surgery'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway4: {
    showItemLabel: false,
    checkboxLabel: t('label.plastic-surgery-checkbox'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway5: {
    showItemLabel: false,
    checkboxLabel: t('label.neurosurgery'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway6: {
    showItemLabel: false,
    checkboxLabel: t('label.dermatology'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway7: {
    showItemLabel: false,
    checkboxLabel: t('label.urology'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway8: {
    showItemLabel: false,
    checkboxLabel: t('label.gynecology'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway9: {
    showItemLabel: false,
    checkboxLabel: t('label.ophthalmology'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway10: {
    showItemLabel: false,
    checkboxLabel: t('label.otorhinolaryngology'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway11: {
    showItemLabel: false,
    checkboxLabel: t('label.rehabilitation-medicine'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway12: {
    showItemLabel: false,
    checkboxLabel: t('label.dentistry'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway13: {
    showItemLabel: false,
    checkboxLabel: t('label.other-label'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00615Oneway13: {
    itemLabel: t('label.section-title-1'),
    itemLabelFontWeight: 'blod',
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:14px;font-weight:blod',
    }),
  } as Mo00615OnewayType,
  mo01299Oneway2: {
    title: t('label.medical-opinion-on-illness'),
  },
  mo00615Oneway14: {
    itemLabel: t('label.section-title-2'),
    itemLabelFontWeight: 'blod',
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:14px;font-weight:blod',
    }),
  } as Mo00615OnewayType,
  mo00615Oneway15: {
    itemLabel: t('label.first-point'),
  } as Mo00615OnewayType,
  mo00045Oneway6: {
    itemLabel: '',
    width: '200px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-2' }),
    maxLength: '12',
    rules: [byteLength(12)],
    disabled: false,
  } as Mo00045OnewayType,
  orX0157Oneway1: {
    showEditBtnFlg: true,
    editBtnClass: 'custom-edit-btn',
    text: {
      orX0157InputOneway: {
        customClass: {
          outerClass: 'ml-3',
          labelClass: '',
        },
        itemLabel: '',
        showItemLabel: false,
        isVerticalLabel: true,
        width: '656px',
        maxLength: '60',
        disabled:false,
        rules: [byteLength(60)],
      },
    },
  } as OrX0157OnewayType,
  mo00615Oneway16: {
    itemLabel: t('label.onset-date'),
    customClass: new CustomClass({
      outerClass: 'ml-4',
    }),
  } as Mo00615OnewayType,
  orX0146Oneway: {
    itemLabel: '',
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '161',
    disabled: false,
        maxLength: '24',
        rules: [byteLength(24)],
  } as OrX0146OnewayType,
  mo00615Oneway17: {
    itemLabel: t('label.second-point'),
  } as Mo00615OnewayType,
  mo00615Oneway18: {
    itemLabel: t('label.third-point'),
  } as Mo00615OnewayType,
  mo00615Oneway19: {
    itemLabel: t('label.section-title-3'),
    itemLabelFontWeight: 'blod',
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:14px;font-weight:blod',
    }),
  } as Mo00615OnewayType,
  orX0156Oneway1: {
    itemLabel: t('label.guidelines-for-filling-out'),
    showItemLabel: true,
    showDividerLineFlg: false,
    showEditBtnFlg: true,
    maxlength: '1120',
    rules: [byteLength(1120)],
    rows: 4,
    maxRows: '4',
    isVerticalLabel: true,
    autoGrow: false,
    noResize: true,
    disabled:false,
    customClass: new CustomClass({ labelStyle: 'margin-bottom:10px' }),
  } as OrX0156OnewayType,
  mo00039Oneway4: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo00615Oneway20: {
    itemLabel: t('label.section-title-10'),
    itemLabelFontWeight: 'blod',
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:14px;font-weight:blod',
    }),
  } as Mo00615OnewayType,
  mo00045Oneway8: {
    itemLabel: t('label.unstable-content'),
    width: '982px',
    showItemLabel: false,
    isVerticalLabel: true,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-2', labelStyle: 'margin-bottom:10px' }),
    maxLength: '60',
    rules: [byteLength(60)],
    disabled: false,
  } as Mo00045OnewayType,
  mo00046Oneway1: {
    name: 'huanteiKnj',
    itemLabel: t('label.unstable-content'),
    isVerticalLabel: true,
    rows: 1,
    hideDetails: true,
    maxlength: '228',
    rules: [byteLength(228)],
    customClass: new CustomClass({ itemStyle: 'width: 982px', labelStyle: 'margin-bottom:10px' }),
    disabled: false,
    noResize: true,
  } as Mo00046OnewayType,
  mo01299Oneway5: {
    title: t('label.section-title-4'),
  },
  mo00615Oneway23: {
    itemLabel: t('label.treatment-contents'),
  } as Mo00615OnewayType,
  mo00018Oneway14: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label1'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway15: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label2'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway16: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label3'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway17: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label4'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway18: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label5'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway19: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label6'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway20: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label7'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway21: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label8'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway22: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label9'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00615Oneway24: {
    itemLabel: t('label.special-correspondence'),
  } as Mo00615OnewayType,
  mo00018Oneway23: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label10'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway24: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label11'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00615Oneway25: {
    itemLabel: t('label.section-title-7'),
    itemLabelFontWeight: 'blod',
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:14px;font-weight:blod',
    }),
  } as Mo00615OnewayType,
  mo00018Oneway25: {
    showItemLabel: false,
    checkboxLabel: t('label.tokubetu-item-label12'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00615Oneway26: {
    itemLabel: t('label.section-title-6'),
    itemLabelFontWeight: 'blod',
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:14px;font-weight:blod',
    }),
  } as Mo00615OnewayType,
  mo00615Oneway27: {
    itemLabel: '',
    itemLabelFontWeight: 'blod',
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'font-size:14px;font-weight:blod',
    }),
  } as Mo00615OnewayType,
  mo01299Oneway6: {
    title: t('label.section-title-5'),
  },
  mo00039Oneway5: {
    itemLabel: t('label.neta1'),
    name: '',
    showItemLabel: true,
    hideDetails: true,
    items: [],
    disabled: false,
    customClass: new CustomClass({ labelStyle: 'margin-bottom:10px' }),
  } as Mo00039OnewayType,
  mo00039Oneway6: {
    itemLabel: t('label.neta2'),
    name: '',
    showItemLabel: true,
    hideDetails: true,
    items: [],
    disabled: false,
    customClass: new CustomClass({ labelStyle: 'margin-bottom:10px' }),
  } as Mo00039OnewayType,
  mo00039Oneway7: {
    itemLabel: t('label.point-short-term-memory'),
    name: '',
    showItemLabel: true,
    hideDetails: true,
    items: [],
    disabled: false,
    customClass: new CustomClass({ labelStyle: 'margin-bottom:10px' }),
  } as Mo00039OnewayType,
  mo00039Oneway8: {
    itemLabel: t('label.point-cognitive-ability'),
    name: '',
    showItemLabel: true,
    hideDetails: true,
    items: [],
    disabled: false,
    customClass: new CustomClass({ labelStyle: 'margin-bottom:10px' }),
  } as Mo00039OnewayType,
  mo00039Oneway9: {
    itemLabel: t('label.point-communication-skills'),
    name: '',
    showItemLabel: true,
    hideDetails: true,
    items: [],
    disabled: false,
    customClass: new CustomClass({ labelStyle: 'margin-bottom:10px' }),
  } as Mo00039OnewayType,
  mo00039Oneway10: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    items: [],
    disabled: false,
  } as Mo00039OnewayType,
  mo00018Oneway26: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-1'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway27: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-2'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway28: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-3'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway29: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-4'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway30: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-5'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway31: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-6'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway32: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-7'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway33: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-8'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway34: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-9'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway35: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-10'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway36: {
    showItemLabel: false,
    checkboxLabel: t('label.shuhen-item-11'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00018Oneway37: {
    showItemLabel: false,
    checkboxLabel: t('label.other-label'),
    hideDetails: true,
    disabled: false,
    customClass: new CustomClass({}),
  } as Mo00018OnewayType,
  mo00045Oneway9: {
    itemLabel: '',
    width: '200px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-2' }),
    maxLength: '20',
    rules: [byteLength(20)],
    disabled: false,
  } as Mo00045OnewayType,
  mo00045Oneway10: {
    itemLabel: t('label.symptom-name'),
    width: '982px',
    showItemLabel: true,
    isRequired: false,
    maxLength: '86',
    rules: [byteLength(86)],
    customClass: new CustomClass({ labelStyle: 'margin-bottom:10px' }),
    disabled: false,
  } as Mo00045OnewayType,
  mo00039Oneway11: {
    itemLabel: t('label.consultation-with-specialist'),
    name: '',
    showItemLabel: true,
    hideDetails: true,
    items: [],
    disabled: false,
    customClass: new CustomClass({ labelStyle: 'margin-bottom:10px' }),
  } as Mo00039OnewayType,
  mo00045Oneway11: {
    itemLabel: '',
    width: '200px',
    showItemLabel: false,
    isRequired: false,
    customClass: new CustomClass({ outerClass: 'mt-0 mr-2' }),
    maxLength: '30',
    rules: [byteLength(30)],
    disabled: false,
  } as Mo00045OnewayType,
  or10545Oneway: { userId: '' } as Or10545OnewayType,
  sindanType: '',
  // 入力支援［ケアマネ］
  or51775Oneway: {
    title: '生活機能低下の直接の原因',
    t1Cd: '1500',
    t2Cd: '1',
    t3Cd: '0',
    tableName: 'cpn_tuc_ikensho2',
    columnName: 'reason_memo_knj',
    inputContents: '',
    userId: '',
  } as Or51775OnewayType,
  mo00039Oneway11Items: [] as Mo00039Items[],
})

const or10545 = ref({ uniqueCpId: Or10545Const.CP_ID(1) })
const orX0146 = ref({ uniqueCpId: OrX0146Const.CP_ID(1) })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(1) })
const gui00059 = ref({ uniqueCpId: Gui00059Const.CP_ID(1) })
/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or10545Const.CP_ID(1)]: or10545.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
  [OrX0146Const.CP_ID(1)]: orX0146.value,
  [Gui00059Const.CP_ID(1)]: gui00059.value,
})

onMounted(async () => {
  await initCodes()
  local.orX0140 = Or29757Logic.data.get(props.parentUniqueCpId)!.ikensho2Obj
  local.rirekiObj = Or29757Logic.data.get(props.parentUniqueCpId)!.rirekiObj
  local.disabled = !!Or29757Logic.state.get(props.parentUniqueCpId)?.disabled
  initTabData()
  changeInputDisabled()
})
/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 医師意見
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ISHIDOUI_TYPE },
    // 意見書回数
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_IKENSHOKAISUU_TYPE },
    // 他科受診
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ELSEJUSIN_FLG_TYPE },
    // 安定
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ANTEI_FLG_TYPE },
    // 障害高齢者の日常生活自立度
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NETA_1_TYPE },
    // 認知症高齢者の日常生活自立度
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NETA_2_TYPE },
    // 短期記憶
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CHUKAKU_1_TYPE },
    // 認知能力
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CHUKAKU_2_TYPE },
    // 伝達能力
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CHUKAKU_3_TYPE },
    // 周辺症状有無
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SHUHEN_FLG_TYPE },
    // 専門医受診有無
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SEISIN_JUSIN_TYPE },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // コード取得
  // 医師意見
  localOneway.mo00039Oneway1.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ISHIDOUI_TYPE
  )
  // 意見書回数
  localOneway.mo00039Oneway2.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_IKENSHOKAISUU_TYPE
  )
  // 他科受診
  localOneway.mo00039Oneway3.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ELSEJUSIN_FLG_TYPE
  )
  // 安定
  localOneway.mo00039Oneway4.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ANTEI_FLG_TYPE
  )
  // 障害高齢者の日常生活自立度
  localOneway.mo00039Oneway5.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_NETA_1_TYPE
  )
  // 認知症高齢者の日常生活自立度
  localOneway.mo00039Oneway6.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_NETA_2_TYPE
  )
  // 短期記憶
  localOneway.mo00039Oneway7.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CHUKAKU_1_TYPE
  )
  // 認知能力
  localOneway.mo00039Oneway8.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CHUKAKU_2_TYPE
  )
  // 伝達能力
  localOneway.mo00039Oneway9.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CHUKAKU_3_TYPE
  )
  // 周辺症状有無
  localOneway.mo00039Oneway10.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SHUHEN_FLG_TYPE
  )
  // 専門医受診有無
  localOneway.mo00039Oneway11Items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SEISIN_JUSIN_TYPE
  )
}

/**
 * TwoWayBind領域データ
 */
watch(
  () => Or29757Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    local.rirekiObj = newValue!.rirekiObj
    local.orX0140 = newValue!.ikensho2Obj
    initTabData()
  }
)

/**
 * 他科受診有無ラジオボタン
 */
watch(
  () => local.orX0140.elseJusinFlg,
  (newValue) => {
    if (newValue === '2') {
      local.orX0140.elseJusin1Flg.modelValue = false
      local.orX0140.elseJusin2Flg.modelValue = false
      local.orX0140.elseJusin3Flg.modelValue = false
      local.orX0140.elseJusin4Flg.modelValue = false
      local.orX0140.elseJusin5Flg.modelValue = false
      local.orX0140.elseJusin6Flg.modelValue = false
      local.orX0140.elseJusin7Flg.modelValue = false
      local.orX0140.elseJusin8Flg.modelValue = false
      local.orX0140.elseJusin9Flg.modelValue = false
      local.orX0140.elseJusin10Flg.modelValue = false
      local.orX0140.elseJusin11Flg.modelValue = false
      local.orX0140.elseJusin12Flg.modelValue = false
      local.orX0140.elseJusin13Flg.modelValue = false
    }
  }
)

/**
 * ［既往歴選択］画面をポップアップでの返回値
 */
watch(
  () => local.or10545,
  (newValue) => {
    if (localOneway.sindanType === '1') {
      local.orX0140.sindan1Ymd.value = convertToJapaneseEra(new Date(newValue.sickYmd))
      local.orX0140.sindanName1Knj.value = newValue.sickKnj
    } else if (localOneway.sindanType === '2') {
      local.orX0140.sindan2Ymd.value = convertToJapaneseEra(new Date(newValue.sickYmd))
      local.orX0140.sindanName2Knj.value = newValue.sickKnj
    } else if (localOneway.sindanType === '3') {
      local.orX0140.sindan3Ymd.value = convertToJapaneseEra(new Date(newValue.sickYmd))
      local.orX0140.sindanName3Knj.value = newValue.sickKnj
    }
  }
)

/**
 * 入力を無効にする
 */
watch(
  () => Or29757Logic.state.get(props.parentUniqueCpId)?.disabled,
  (newValue) => {
    local.disabled = !!newValue
    changeInputDisabled()
  }
)

/**
 * Gui00059のイベントを監視
 *
 * @description
 * 自身のEvent領域の検索ボタン押下フラグを更新する。
 * またGui00059のボタン押下フラグをリセットする。
 */
watch(
  () => Gui00059Logic.event.get(gui00059.value.uniqueCpId),
  (newValue?: Gui00059EventType) => {
    if (newValue?.confirmFlg) {
      // 医療機関名
      local.orX0140.iryouKikanKnj.value = newValue.confirmParams?.hospitalNm ?? ''
      // 医療機関所在地
      local.orX0140.iryouKikanAddress.value = newValue.confirmParams?.hospitalLocation ?? ''
      // 電話
      local.orX0140.ishiTel.value = newValue.confirmParams?.hospitalTel ?? ''
      // FAX
      local.orX0140.ishiFax.value = newValue.confirmParams?.fax ?? ''
      // 返回値.処理区分が4：医師の場合
      if (
        newValue.confirmParams?.hospitalSelectKbn === OrX0140Const.HOSPITAL_SELECT_KBN_PHYSICIAN
      ) {
        local.orX0140.ishiNameKnj.value = newValue.confirmParams.doctorNm ?? ''
      }
    }
  }
)

/**
 * タブデータを初期化する
 *
 */
const initTabData = () => {
  // 表示用「主治医意見書履歴」情報.改訂フラグがH21/4改訂版、かつ、表示用「主治医意見書履歴」情報.作成日が"2015/04/01"より前の場合
  if (local.rirekiObj.kaiteiFlg === '2' && local.rirekiObj.createYmd < '2015/04/01') {
    localOneway.mo00615Oneway2.itemLabel = t('label.consent-statement')
  } else {
    localOneway.mo00615Oneway2.itemLabel = t('label.consent-statement-alt')
  }
  // 表示用「主治医意見書履歴」情報.改訂フラグがR3/4改訂版、または、(表示用「主治医意見書履歴」情報.改訂フラグがH21/4改訂版、かつ、表示用「主治医意見書履歴」情報.作成日が"2018/04/01"以降))、かつ、周辺症状有無ラベルの先頭が"認知症の行動"以外の場合
  if (
    local.rirekiObj.kaiteiFlg === '3' ||
      (local.rirekiObj.kaiteiFlg === '2' && local.rirekiObj.createYmd < '2018/04/01')
  ) {
    localOneway.mo00615Oneway27.itemLabel = t('label.section-title-8')
  } else {
    localOneway.mo00615Oneway27.itemLabel = t('label.section-title-9')
  }
}

/**
 * 入力無効状態を切り替える
 */
const changeInputDisabled = () => {
  // 医師意見
  localOneway.mo00039Oneway1.disabled = local.disabled
  // 医師名前
  localOneway.mo00045Oneway1.disabled = local.disabled
  // 医療機関TEL
  localOneway.mo00045Oneway2.disabled = local.disabled
  // 医療機関名
  localOneway.mo00045Oneway3.disabled = local.disabled
  // 医療機関FAX
  localOneway.mo00045Oneway4.disabled = local.disabled
  // 医療機関所在地
  localOneway.mo00046Oneway3.disabled = local.disabled
  // 最終診察日付
  localOneway.mo00020Oneway1.disabled = local.disabled
  // 意見書回数
  localOneway.mo00039Oneway2.disabled = local.disabled
  // 他科受診
  localOneway.mo00039Oneway3.disabled = local.disabled
  // 内科選択フラッグ
  localOneway.mo00018Oneway1.disabled = local.disabled
  // 精神科選択フラッグ
  localOneway.mo00018Oneway2.disabled = local.disabled
  // 外科選択フラッグ
  localOneway.mo00018Oneway3.disabled = local.disabled
  // 整形外科選択フラッグ
  localOneway.mo00018Oneway4.disabled = local.disabled
  // 脳神精外科選択フラッグ
  localOneway.mo00018Oneway5.disabled = local.disabled
  // 皮膚科選択フラッグ
  localOneway.mo00018Oneway6.disabled = local.disabled
  // 泌尿器科選択フラッグ
  localOneway.mo00018Oneway7.disabled = local.disabled
  // 婦人科選択フラッグ
  localOneway.mo00018Oneway8.disabled = local.disabled
  // 眼科選択フラッグ
  localOneway.mo00018Oneway9.disabled = local.disabled
  // 耳鼻咽喉科選択フラッグ
  localOneway.mo00018Oneway10.disabled = local.disabled
  // リハビリテーション科選択フラッグ
  localOneway.mo00018Oneway11.disabled = local.disabled
  // 歯科選択フラッグ
  localOneway.mo00018Oneway12.disabled = local.disabled
  // その他選択フラッグ
  localOneway.mo00018Oneway13.disabled = local.disabled
  // その他科の名前
  localOneway.mo00045Oneway6.disabled = local.disabled
  // 診断の日付
  localOneway.orX0146Oneway.disabled = local.disabled
  // 安定
  localOneway.mo00039Oneway4.disabled = local.disabled
  // 不安定内容
  localOneway.mo00045Oneway8.disabled = local.disabled
  localOneway.mo00046Oneway1.disabled = local.disabled
  // 特別医療関連行為1
  localOneway.mo00018Oneway14.disabled = local.disabled
  // 特別医療関連行為2
  localOneway.mo00018Oneway15.disabled = local.disabled
  // 特別医療関連行為3
  localOneway.mo00018Oneway16.disabled = local.disabled
  // 特別医療関連行為4
  localOneway.mo00018Oneway17.disabled = local.disabled
  // 特別医療関連行為5
  localOneway.mo00018Oneway18.disabled = local.disabled
  // 特別医療関連行為6
  localOneway.mo00018Oneway19.disabled = local.disabled
  // 特別医療関連行為7
  localOneway.mo00018Oneway20.disabled = local.disabled
  // 特別医療関連行為8
  localOneway.mo00018Oneway21.disabled = local.disabled
  // 特別医療関連行為9
  localOneway.mo00018Oneway22.disabled = local.disabled
  // 特別医療関連行為10
  localOneway.mo00018Oneway23.disabled = local.disabled
  // 特別医療関連行為11
  localOneway.mo00018Oneway24.disabled = local.disabled
  // 特別医療関連行為12
  localOneway.mo00018Oneway25.disabled = local.disabled
  // 障害高齢者の日常生活自立度
  localOneway.mo00039Oneway5.disabled = local.disabled
  // 認知症高齢者の日常生活自立度
  localOneway.mo00039Oneway6.disabled = local.disabled
  // 短期記憶
  localOneway.mo00039Oneway7.disabled = local.disabled
  // 認知能力
  localOneway.mo00039Oneway8.disabled = local.disabled
  // 伝達能力
  localOneway.mo00039Oneway9.disabled = local.disabled
  // 周辺症状フラッグ
  localOneway.mo00039Oneway10.disabled = local.disabled
  // 周辺症状1選択フラッグ
  localOneway.mo00018Oneway26.disabled = local.disabled
  // 周辺症状2選択フラッグ
  localOneway.mo00018Oneway27.disabled = local.disabled
  // 周辺症状3選択フラッグ
  localOneway.mo00018Oneway28.disabled = local.disabled
  // 周辺症状4選択フラッグ
  localOneway.mo00018Oneway29.disabled = local.disabled
  // 周辺症状5選択フラッグ
  localOneway.mo00018Oneway30.disabled = local.disabled
  // 周辺症状6選択フラッグ
  localOneway.mo00018Oneway31.disabled = local.disabled
  // 周辺症状7選択フラッグ
  localOneway.mo00018Oneway32.disabled = local.disabled
  // 周辺症状8選択フラッグ
  localOneway.mo00018Oneway33.disabled = local.disabled
  // 周辺症状9選択フラッグ
  localOneway.mo00018Oneway34.disabled = local.disabled
  // 周辺症状10選択フラッグ
  localOneway.mo00018Oneway35.disabled = local.disabled
  // 周辺症状11選択フラッグ
  localOneway.mo00018Oneway36.disabled = local.disabled
  // 周辺症状12その他選択フラッグ
  localOneway.mo00018Oneway37.disabled = local.disabled
  // 周辺症状12その他
  localOneway.mo00045Oneway9.disabled = local.disabled
  // 精神症状フラッグ
  localOneway.mo00039Oneway10.disabled = local.disabled
  // 症状名前
  localOneway.mo00045Oneway10.disabled = local.disabled
  // 受診フラグ
  localOneway.mo00039Oneway11.disabled = local.disabled
  // 受診科の名前
  localOneway.mo00045Oneway11.disabled = local.disabled
  // 診断名前１テキスト
  localOneway.orX0157Oneway1.showEditBtnFlg = !local.disabled
  localOneway.orX0157Oneway1.text!.orX0157InputOneway.disabled = local.disabled
  localOneway.orX0156Oneway1.showEditBtnFlg = !local.disabled
  localOneway.orX0156Oneway1.disabled = local.disabled
}

/**
 * 既往歴選択
 *
 * @param type - 区分
 */
const handlePastMedicalHistoryClick = (type: string) => {
  localOneway.sindanType = type
  localOneway.or10545Oneway.userId = systemCommonsStore.getUserId ?? ''
  // Or00100のダイアログ開閉状態を更新する
  Or10545Logic.state.set({
    uniqueCpId: or10545.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 生活機能低下原因支援アイコンボタン
 */
const handleInstrumentalADLClick = () => {
  localOneway.or51775Oneway.inputContents = local.orX0140.reasonMemoKnj.value ?? ''
  localOneway.or51775Oneway.userId = systemCommonsStore.getUserId ?? ''
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 「他科受診-内科チェックボックス～他科受診-その他チェックボックス」変更
 */
const onElseJusinChange = () => {
  if (
    local.orX0140.elseJusin1Flg.modelValue ||
    local.orX0140.elseJusin2Flg.modelValue ||
    local.orX0140.elseJusin3Flg.modelValue ||
    local.orX0140.elseJusin4Flg.modelValue ||
    local.orX0140.elseJusin5Flg.modelValue ||
    local.orX0140.elseJusin6Flg.modelValue ||
    local.orX0140.elseJusin7Flg.modelValue ||
    local.orX0140.elseJusin8Flg.modelValue ||
    local.orX0140.elseJusin9Flg.modelValue ||
    local.orX0140.elseJusin10Flg.modelValue ||
    local.orX0140.elseJusin11Flg.modelValue ||
    local.orX0140.elseJusin12Flg.modelValue ||
    local.orX0140.elseJusin13Flg.modelValue
  ) {
    local.orX0140.elseJusinFlg = '1'
  }
}

// ダイアログ表示フラグ
const showDialogOr10545 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or10545Logic.state.get(or10545.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogGui00059 = computed(() => {
  // Gui00059のダイアログ開閉状態
  return Gui00059Logic.state.get(gui00059.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  local.orX0140.reasonMemoKnj.value = data.value
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * 西暦を和暦に変換する
 *
 * @param date - 変換対象の西暦Dateオブジェクト
 *
 * @returns 和暦表記の日付文字列（例: "令和5年7月4日"）
 */
function convertToJapaneseEra(date: Date) {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  // 日本の元号を定義
  const eras = [
    { name: '令和', start: new Date(2019, 4, 1) }, // 2019年5月1日
    { name: '平成', start: new Date(1989, 0, 8) }, // 1989年1月8日
    { name: '昭和', start: new Date(1926, 11, 25) }, // 1926年12月25日
    { name: '大正', start: new Date(1912, 6, 30) }, // 1912年7月30日
    { name: '明治', start: new Date(1868, 0, 25) }, // 1868年1月25日
  ]

  for (const era of eras) {
    if (date >= era.start) {
      const eraYear = year - era.start.getFullYear() + 1
      return `${era.name}${eraYear}年${month}月${day}日`
    }
  }

  return `${year}年${month}月${day}日` // 明治より前の日付
}

/**
 * GUI00059_［医療機関選択］画面をポップアップで起動する
 */
const handleAttendingPhysicianClick = () => {
  Gui00059Logic.state.set({
    uniqueCpId: gui00059.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <div
    v-if="
      Or29757Logic.state.get(props.parentUniqueCpId)?.operaFlg !== Or29758Const.ACTION_TYPE.DELETE
    "
    class="view"
  >
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway1"
      class="section-view"
    >
      <template #content>
        <c-v-row
          no-gutters
          style="height: 24px; gap: 48px"
        >
          <!-- 医師意見文字ラベル -->
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway2" />

          <!-- 医師意見ラジオボタン -->
          <base-mo00039
            v-model="local.orX0140.ishiDoui"
            :oneway-model-value="localOneway.mo00039Oneway1"
            class="radio-group"
            :readonly="true"
          >
          </base-mo00039>
        </c-v-row>
        <c-v-row
          no-gutters
          class="header-row"
        >
          <!-- 主治医ラベル -->
          <base-mo00615
            :oneway-model-value="localOneway.mo00615Oneway3"
            @click="handleAttendingPhysicianClick"
          />
        </c-v-row>
        <div style="display: flex; flex-direction: column; gap: 16px">
          <c-v-row no-gutters>
            <!-- 医師氏名ラベル -->
            <base-mo00615
              class="width-120"
              :oneway-model-value="localOneway.mo00615Oneway4"
            />
            <!-- 医師氏名テキストボックス -->
            <base-mo-00045
              v-model="local.orX0140.ishiNameKnj"
              :oneway-model-value="localOneway.mo00045Oneway1"
            />
            <!-- 電話ラベル -->
            <base-mo00615
              class="width-52 ml-6"
              :oneway-model-value="localOneway.mo00615Oneway5"
            />
            <!-- 電話テキストボックス -->
            <base-mo-00045
              v-model="local.orX0140.ishiTel"
              :oneway-model-value="localOneway.mo00045Oneway2"
            />
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 医療機関名ラベル -->
            <base-mo00615
              class="width-120"
              :oneway-model-value="localOneway.mo00615Oneway6"
            />
            <!-- 医療機関名テキストボックス -->
            <base-mo-00045
              v-model="local.orX0140.iryouKikanKnj"
              :oneway-model-value="localOneway.mo00045Oneway3"
            />
            <!-- FAXラベル -->
            <base-mo00615
              class="width-52 ml-6"
              :oneway-model-value="localOneway.mo00615Oneway7"
            />
            <!-- FAXテキストボックス -->
            <base-mo-00045
              v-model="local.orX0140.ishiFax"
              :oneway-model-value="localOneway.mo00045Oneway4"
            />
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 医療機関所在地ラベル -->
            <base-mo00615
              class="width-120"
              :oneway-model-value="localOneway.mo00615Oneway8"
            />
            <!-- 医療機関名テキストボックス -->
            <base-mo-00046
              v-model="local.orX0140.iryouKikanAddress"
              :oneway-model-value="localOneway.mo00046Oneway3"
            />
          </c-v-row>
        </div>
        <c-v-divider />
        <div class="sheet-card">
          <c-v-row
            no-gutters
            class="sheet-row"
            style="height: 60px"
          >
            <div class="sheet-header">
              <!-- 最終診察日ラベル -->
              <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway9" />
            </div>
            <!-- 最終診察日テキストボックス -->
            <base-mo00020
              v-model="local.orX0140.sinsatuYmd"
              :oneway-model-value="localOneway.mo00020Oneway1"
            >
            </base-mo00020>
          </c-v-row>
          <c-v-row
            no-gutters
            class="sheet-row"
            style="height: 60px"
          >
            <div class="sheet-header">
              <!-- 意見書作成回数ラベル -->
              <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway10" />
            </div>
            <!-- 意見書作成回数ラジオボタン -->
            <base-mo00039
              v-model="local.orX0140.ikenshoKaisuu"
              class="radio-group"
              :oneway-model-value="localOneway.mo00039Oneway2"
            ></base-mo00039>
          </c-v-row>
          <c-v-row
            no-gutters
            class="sheet-row"
            style="height: 200px"
          >
            <div class="sheet-header">
              <!-- 他科受診有無ラベル -->
              <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway11" />
            </div>
            <div
              style="display: flex; flex-direction: column; gap: 10px; width: calc(100% - 166px)"
            >
              <!-- 他科受診有無ラジオボタン -->
              <base-mo00039
                v-model="local.orX0140.elseJusinFlg"
                class="radio-group"
                :oneway-model-value="localOneway.mo00039Oneway3"
              ></base-mo00039>
              <div style="margin-left: 12px; display: flex; flex-direction: column; gap: 10px">
                <!-- 有の場合ラベル -->
                <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway12" />
                <div class="checkbox-group">
                  <c-v-row
                    no-gutters
                    style="gap: 48px"
                  >
                    <!-- 内科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin1Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway1"
                      @change="onElseJusinChange()"
                    />
                    <!-- 精神科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin2Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway2"
                      @change="onElseJusinChange()"
                    />
                    <!-- 外科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin3Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway3"
                      @change="onElseJusinChange()"
                    />
                    <!-- 整形外科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin4Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway4"
                      @change="onElseJusinChange()"
                    />
                    <!-- 脳神精外科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin5Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway5"
                      @change="onElseJusinChange()"
                    />
                    <!-- 皮膚科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin6Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway6"
                      @change="onElseJusinChange()"
                    />
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    style="gap: 48px"
                  >
                    <!-- 泌尿器科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin7Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway7"
                      @change="onElseJusinChange()"
                    />
                    <!-- 婦人科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin8Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway8"
                      @change="onElseJusinChange()"
                    />
                    <!-- 眼科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin9Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway9"
                      @change="onElseJusinChange()"
                    />
                    <!-- 耳鼻咽喉科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin10Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway10"
                      @change="onElseJusinChange()"
                    />
                    <!-- リハビリテーション科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin11Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway11"
                      @change="onElseJusinChange()"
                    />
                    <!-- 歯科チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin12Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway12"
                      @change="onElseJusinChange()"
                    />
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    style="gap: 16px; align-items: center"
                  >
                    <!-- その他チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.elseJusin13Flg"
                      class="checkbox-item"
                      :oneway-model-value="localOneway.mo00018Oneway13"
                      @change="onElseJusinChange()"
                    />

                    <!-- その他科の名前 -->
                    <base-mo-00045
                      v-model="local.orX0140.elseJusinKnj"
                      :oneway-model-value="localOneway.mo00045Oneway6"
                    />
                  </c-v-row>
                </div>
              </div>
            </div>
          </c-v-row>
        </div>
      </template>
    </base-mo01299>
    <!-- 傷病に関する意見タイトルラベル -->
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway2"
      class="section-view"
    >
      <template #content>
        <c-v-row
          no-gutters
          class="header-row"
        >
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway13" />
        </c-v-row>
        <div class="sheet-card">
          <c-v-row
            no-gutters
            class="sheet-row"
            style="height: 60px"
          >
            <div
              class="sheet-header"
              style="width: 52px"
            >
              <!-- 診断名前１ラベル -->
              <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway15" />
            </div>
            <!--診断名前１テキスト -->
            <g-custom-or-x0157
              v-model="local.orX0140.sindanName1Knj"
              :oneway-model-value="localOneway.orX0157Oneway1"
              class="custom-input"
              @on-click-edit-btn="handlePastMedicalHistoryClick('1')"
            />
            <!-- 発症年月日１ラベル -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway16" />
            <!-- 発症年月日１テキストボックス -->
            <g-custom-or-X-0146
              v-model="local.orX0140.sindan1Ymd"
              class="ml-1"
              :oneway-model-value="localOneway.orX0146Oneway"
            />
          </c-v-row>
          <c-v-row
            no-gutters
            class="sheet-row"
            style="height: 60px"
          >
            <div
              class="sheet-header"
              style="width: 52px"
            >
              <!-- 診断名前２ラベル -->
              <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway17" />
            </div>
            <!-- 診断名前２テキスト -->
            <g-custom-or-x0157
              v-model="local.orX0140.sindanName2Knj"
              :oneway-model-value="localOneway.orX0157Oneway1"
              class="custom-input"
              @on-click-edit-btn="handlePastMedicalHistoryClick('2')"
            />
            <!-- 発症年月日２ラベル -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway16" />
            <!-- 発症年月日２テキストボックス -->
            <g-custom-or-X-0146
              v-model="local.orX0140.sindan2Ymd"
              class="ml-1"
              :oneway-model-value="localOneway.orX0146Oneway"
            />
          </c-v-row>
          <c-v-row
            no-gutters
            class="sheet-row"
            style="height: 60px"
          >
            <div
              class="sheet-header"
              style="width: 52px"
            >
              <!-- 診断名前３ラベル -->
              <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway18" />
            </div>
            <!-- 診断名前３テキスト -->
            <g-custom-or-x0157
              v-model="local.orX0140.sindanName3Knj"
              :oneway-model-value="localOneway.orX0157Oneway1"
              class="custom-input"
              @on-click-edit-btn="handlePastMedicalHistoryClick('3')"
            />
            <!-- 発症年月日３ラベル -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway16" />
            <!-- 発症年月日３テキストボックス -->
            <g-custom-or-X-0146
              v-model="local.orX0140.sindan3Ymd"
              class="ml-1"
              :oneway-model-value="localOneway.orX0146Oneway"
            />
          </c-v-row>
        </div>
        <c-v-row
          no-gutters
          class="header-row"
        >
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway14" />
        </c-v-row>
        <div style="display: flex; gap: 16px; flex-direction: column">
          <c-v-row no-gutters>
            <!-- 安定ラジオボタン -->
            <base-mo00039
              v-model="local.orX0140.anteiFlg"
              class="radio-group"
              :oneway-model-value="localOneway.mo00039Oneway4"
            >
            </base-mo00039>
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 不安定内容テキストボックス -->
            <base-mo-00045
              v-if="local.rirekiObj.kaiteiFlg === '2'"
              v-model="local.orX0140.huanteiKnj"
              :oneway-model-value="localOneway.mo00045Oneway8"
            />
            <!-- 不安定内容テキストエリア -->
            <base-mo-00046
              v-else
              v-model="local.orX0140.huanteiKnj"
              :oneway-model-value="localOneway.mo00046Oneway1"
            />
          </c-v-row>
        </div>
        <c-v-row
          no-gutters
          class="header-row"
        >
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway19" />
        </c-v-row>
        <c-v-row no-gutters>
          <g-custom-or-x-0156
            v-model="local.orX0140.reasonMemoKnj"
            :oneway-model-value="localOneway.orX0156Oneway1"
            style="width: 1050px"
            @on-click-edit-btn="handleInstrumentalADLClick"
          >
          </g-custom-or-x-0156
        ></c-v-row>
      </template>
    </base-mo01299>
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway5"
      class="section-view"
    >
      <template #content>
        <div style="display: flex; flex-direction: column; gap: 16px">
          <div style="display: flex; flex-direction: column; gap: 10px">
            <!-- 処置内容ラベル -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway23" />

            <div class="checkbox-group">
              <c-v-row
                no-gutters
                style="gap: 48px"
              >
                <!-- 処置内容-点滴の管理チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu1Flg"
                  :oneway-model-value="localOneway.mo00018Oneway14"
                />
                <!-- 中心静脈栄養チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu2Flg"
                  :oneway-model-value="localOneway.mo00018Oneway15"
                />
                <!-- 処置内容-透析チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu3Flg"
                  :oneway-model-value="localOneway.mo00018Oneway16"
                />
                <!-- 処置内容-ストーマの処置チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu4Flg"
                  :oneway-model-value="localOneway.mo00018Oneway17"
                />
                <!-- 処置内容-酸素療法チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu5Flg"
                  :oneway-model-value="localOneway.mo00018Oneway18"
                />
                <!-- 処置内容-レスピレーターチェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu6Flg"
                  :oneway-model-value="localOneway.mo00018Oneway19"
                />
              </c-v-row>
              <c-v-row
                no-gutters
                style="gap: 48px"
              >
                <!-- 処置内容-気管切開の処置チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu7Flg"
                  :oneway-model-value="localOneway.mo00018Oneway20"
                />
                <!-- 処置内容-疼痛の看護チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu8Flg"
                  :oneway-model-value="localOneway.mo00018Oneway21"
                />
                <!-- 処置内容-経管栄養チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu9Flg"
                  :oneway-model-value="localOneway.mo00018Oneway22"
                />
              </c-v-row>
            </div>
          </div>
          <div style="display: flex; flex-direction: column; gap: 10px">
            <!-- 特別な対応ラベル -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway24" />
            <div class="checkbox-group">
              <c-v-row
                no-gutters
                style="gap: 48px"
              >
                <!-- 特別な対応-モニター測定（血圧、心拍、酸素飽和度等）チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu10Flg"
                  :oneway-model-value="localOneway.mo00018Oneway23" />
                <!-- 特別な対応-褥瘡の処置チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu11Flg"
                  :oneway-model-value="localOneway.mo00018Oneway24"
              /></c-v-row>
            </div>
          </div>

          <div style="display: flex; flex-direction: column; gap: 10px">
            <!-- 失禁への対応ラベル -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway1" />
            <div class="checkbox-group">
              <c-v-row
                no-gutters
                style="gap: 48px"
              >
                <!-- 失禁への対応-カテーテル（コンドームカテーテル、留置カテーテル等）チェックボックス -->
                <base-mo00018
                  v-model="local.orX0140.tokubetu12Flg"
                  :oneway-model-value="localOneway.mo00018Oneway25"
              /></c-v-row>
            </div>
          </div>
        </div>
      </template>
    </base-mo01299>
    <!-- 心身の状態に関する意見タイトルラベル -->
    <base-mo01299
      :oneway-model-value="localOneway.mo01299Oneway6"
      class="section-view"
    >
      <template #content>
        <c-v-row
          no-gutters
          class="header-row"
        >
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway26" />
        </c-v-row>
        <div style="display: flex; flex-direction: column; gap: 18px">
          <c-v-row no-gutters>
            <!-- 障害高齢者の日常生活自立度ラジオボタン -->
            <base-mo00039
              v-model="local.orX0140.neta1Cd"
              class="radio-group"
              :oneway-model-value="localOneway.mo00039Oneway5"
            >
            </base-mo00039>
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 認知症高齢者の日常生活自立度ラジオボタン -->
            <base-mo00039
              v-model="local.orX0140.neta2Cd"
              class="radio-group"
              :oneway-model-value="localOneway.mo00039Oneway6"
            ></base-mo00039>
          </c-v-row>
        </div>
        <c-v-row
          no-gutters
          class="header-row"
        >
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway25" />
        </c-v-row>
        <div style="display: flex; flex-direction: column; gap: 16px">
          <c-v-row no-gutters>
            <!-- 短期記憶ラジオボタンラジオボタン -->
            <base-mo00039
              v-model="local.orX0140.chukaku1Flg"
              class="radio-group"
              :oneway-model-value="localOneway.mo00039Oneway7"
            ></base-mo00039>
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 認知能力ラジオボタン -->
            <base-mo00039
              v-model="local.orX0140.chukaku2Flg"
              class="radio-group"
              :oneway-model-value="localOneway.mo00039Oneway8"
            ></base-mo00039>
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 伝達能力ラジオボタン -->
            <base-mo00039
              v-model="local.orX0140.chukaku3Flg"
              class="radio-group"
              :oneway-model-value="localOneway.mo00039Oneway9"
            ></base-mo00039>
          </c-v-row>
        </div>
        <c-v-row
          no-gutters
          class="header-row"
        >
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway27" />
        </c-v-row>

        <div style="display: flex; flex-direction: column; gap: 16px">
          <c-v-row no-gutters>
            <!-- 周辺症状有無ラジオボタン -->
            <base-mo00039
              v-model="local.orX0140.shuhenFlg"
              class="radio-group"
              :oneway-model-value="localOneway.mo00039Oneway10"
            ></base-mo00039>
          </c-v-row>
          <c-v-row no-gutters>
            <div style="display: flex; flex-direction: column; gap: 10px">
              <!-- 有の場合ラベル -->
              <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway12" />
              <div class="checkbox-group">
                <c-v-row
                  no-gutters
                  style="gap: 48px;width:101%"
                >
                  <!-- 周辺症状-幻視・幻聴チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen1Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway26"
                  />
                  <!-- 周辺症状-妄想チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen2Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway27"
                  />
                  <!-- 周辺症状-昼夜逆転チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen3Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway28"
                  />
                  <!-- 周辺症状-暴言チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen4Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway29"
                  />
                  <!-- 周辺症状-暴行チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen5Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway30"
                  />
                  <!-- 周辺症状-介護への抵抗チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen6Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway31"
                  />
                  <!-- 周辺症状-徘徊チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen7Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway32"
                  />
                  <!-- 周辺症状-火の不始末チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen8Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway33"
                  />
                </c-v-row>
                <c-v-row
                  no-gutters
                  style="gap: 48px"
                >
                  <!-- 周辺症状-不潔行為チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen9Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway34"
                  />
                  <!-- 周辺症状-異食行動チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen10Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway35"
                  />
                  <!-- 周辺症状-性的問題行動チェックボックス -->
                  <base-mo00018
                    v-model="local.orX0140.shuhen11Flg"
                    class="checkbox-item"
                    :oneway-model-value="localOneway.mo00018Oneway36"
                  />
                  <div class="checkbox-item">
                    <!-- 周辺症状-その他チェックボックス -->
                    <base-mo00018
                      v-model="local.orX0140.shuhen12Flg"
                      :oneway-model-value="localOneway.mo00018Oneway37"
                    />
                    <!-- その他科の名前 -->
                    <base-mo-00045
                      v-model="local.orX0140.shuhen12Knj"
                      :oneway-model-value="localOneway.mo00045Oneway9"
                    />
                  </div>
                </c-v-row>
              </div>
            </div>
          </c-v-row>
        </div>
        <c-v-row
          no-gutters
          class="header-row"
        >
          <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway20" />
        </c-v-row>
        <div style="display: flex; flex-direction: column">
          <c-v-row
            no-gutters
            style="margin-bottom: 16px"
          >
            <!-- 精神症状有無ラジオボタン -->
            <base-mo00039
              v-model="local.orX0140.seisinFlg"
              class="radio-group"
              :oneway-model-value="localOneway.mo00039Oneway10"
            ></base-mo00039>
          </c-v-row>

          <c-v-row
            no-gutters
            style="margin-bottom: 20px"
          >
            <!-- 症状名テキストボックス -->
            <base-mo-00045
              v-model="local.orX0140.seisinKnj"
              :oneway-model-value="localOneway.mo00045Oneway10"
            />
          </c-v-row>
          <c-v-row no-gutters>
            <!-- 専門医受診有無ラジオボタン -->
            <base-mo00039
              v-model="local.orX0140.seisinJusinFlg"
              :oneway-model-value="localOneway.mo00039Oneway11"
            >
              <template
                v-for="item in localOneway.mo00039Oneway11Items"
                :key="item.value"
              >
                <base-mo01288
                  :oneway-model-value="{
                    name: 'mo00039-radio-' + item.value,
                    radioLabel: item.label,
                    value: item.value,
                  }"
                />
                <!-- 専門医受診テキストボックス -->
                <base-mo-00045
                  v-if="item.value === '1'"
                  v-model="local.orX0140.seisinJusinKnj"
                  style="margin-left: 8px; margin-right: 12px"
                  :oneway-model-value="localOneway.mo00045Oneway11"
                />
              </template>
            </base-mo00039>
          </c-v-row>
        </div>
      </template>
    </base-mo01299>
  </div>
  <!-- ［既往歴選択］画面をポップアップ -->
  <g-custom-or-10545
    v-if="showDialogOr10545"
    v-bind="or10545"
    v-model="local.or10545"
    :oneway-model-value="localOneway.or10545Oneway"
    :unique-cp-id="or10545.uniqueCpId"
  />
  <!-- 入力支援［ケアマネ］画面をポップアップ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleOr51775Confirm"
  />
  <!--［医療機関選択］画面をポップアップ -->
  <g-custom-gui-00059
    v-if="showDialogGui00059"
    v-bind="gui00059"
  />
</template>
<style scoped lang="scss">
.view {
  padding: 8px 24px 0 24px;
  width: 1128px;
  :deep(.v-checkbox-btn) {
    min-height: 24px;
    height: 24px;
  }
}
.section-view {
  display: flex;
  flex-direction: column;
  :deep(.section-header) {
    width: unset !important;
    max-width: unset !important;
    min-width: unset !important;
  }
  :deep(.section-label) {
    height: 48px !important;
    background-color: #e6e6e6 !important;
  }
  :deep(.section-content) {
    padding: 24px 48px !important;
    display: flex;
    gap: 24px;
    flex-direction: column;
  }
}
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  .checkbox-item {
    height: 24px;
    display: flex;
    align-items: center;
    margin-right: 0px !important;
  }
}
.width-52 {
  width: 52px;
}
.width-120 {
  width: 120px;
}
.header-row {
  height: 48px;
  background: rgba(7, 96, 230, 0.08);
  padding: 0px 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.sheet-card {
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  width: 100%;
  .sheet-row {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    align-items: center;
    width: 100%;
    .sheet-header {
      width: 166px;
      border-right: 1px solid rgba(0, 0, 0, 0.1);
      background: #dbeefe;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0px 12px;
    }
  }
}
:deep(.v-selection-control__wrapper),
:deep(.v-selection-control__wrapper .v-selection-control__input) {
  height: 24px !important;
  width: 24px !important;
}
.custom-input {
  :deep(.v-input__control) {
    background-color: rgb(var(--v-theme-secondaryBackground));
  }
}
:deep(.radio-group) {
  margin-top: 0px !important;
}
.radio-group {
  :deep(.v-selection-control-group) {
    gap: 24px;
  }
  :deep(.v-radio) {
    margin: 0px !important;
    height: 24px !important;
    min-height: 24px !important;
    gap: 8px;
  }
}
:deep(.v-checkbox-btn) {
  gap: 8px;
}
</style>
