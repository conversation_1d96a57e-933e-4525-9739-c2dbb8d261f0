<script setup lang="ts">
/**
 * Or52572:（（リハビリテーション計画書 分類項目マスタ））課題分析一覧
 * GUI00930_課題分析
 *
 * @description
 * （（リハビリテーション計画書 分類項目マスタ））課題分析一覧
 *
 * <AUTHOR> PHAM TIEN THANH
 */
import { reactive, watch, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or52572Logic } from './Or52572.logic'
import { useSetupChildProps } from '#imports'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { Or52572Type, Or52572OnewayType } from '~/types/cmn/business/components/Or52572Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { ShousaiInfoType } from '~/components/custom-components/organisms/Or61011/Or61011.type'
import type { OrX0163Type, OrX0163OnewayType } from '~/types/cmn/business/components/OrX0163Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo01351OnewayType } from '~/types/business/components/Mo01351Type'

import { useValidation } from '@/utils/useValidation'

/**************************************************
 * i18n
 **************************************************/
const { t } = useI18n()
const { byteLength } = useValidation()
let rafId: number | null = null

/**
 * フォームバリデーション状態
 */
const valid = ref(false)

/**
 * バリデーションエラーメッセージの配列
 */
const validationErrors = ref<string[]>([])

/**
 * エラーパネルの表示状態
 */
const showErrorPanel = computed(() => validationErrors.value.length > 0)

/**
 * エラーパネル用のOneWayBind
 */
const errorPanelOneway = computed<Mo01351OnewayType>(() => ({
  mo00002Oneway: {
    title: t('label.validation-error'),
    msg: validationErrors.value,
    visible: showErrorPanel.value,
    color: 'error',
    icon: 'error fill',
  },
}))

/**
 * バリデーションエラーを収集する関数
 */
function collectValidationErrors() {
  const errors: string[] = []

  // 各OrX0163コンポーネントのエラーを収集
  if (local.or52572.shousaiData) {
    local.or52572.shousaiData.forEach((item, index) => {
      const currentValue = orX0163Models.value[index]?.value || ''
      const validationResult = byteLength(4000)(currentValue)
      if (validationResult !== true) {
        errors.push(`${item.koumokuKnj}: ${validationResult}`)
      }
    })
  }

  validationErrors.value = errors
}

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  modelValue: Or52572Type
  onewayModelValue: Or52572OnewayType
}
/**
 * Props
 */
const props: Props = defineProps<Props>()

/**************************************************
 * Emit
 **************************************************/
/**
 * Emit
 */
const emit = defineEmits(['update:modelValue', 'shousai-change'])

/**
 * 入力支援ダイアログコンポーネント
 */
const or51775_1 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775_1.value,
})

/**************************************************
 * 変数定義
 **************************************************/
/**
 * 初期化フラグ
 */
let initFlg = false

/**
 * 現在選択中の項目のインデックス（入力支援ダイアログ用）
 */
const currentSelectedIndex = ref<number>(0)

/**
 * 選択されたテーブル行
 */
const selectedTableItems = ref<ShousaiInfoType[]>([])

/**
 * ローカル
 */
const local = reactive({
  or52572: {
    ...props.modelValue,
  } as Or52572Type,
})

// 各セクションのOrX0163用モデル（オブジェクトの再生成を避ける）
const orX0163Models = ref<OrX0163Type[]>([])

/**
 * 入力支援ダイアログのOneWayBind
 */
const or51775Oneway = reactive({
  title: '',
  bunruiId: '1',
} as Or51775OnewayType)

/**
 * データテーブルのヘッダー定義
 */
const dataTableHeaders = reactive([
  {
    title: t('label.category-name'),
    key: 'koumokuKnj',
    align: 'start',
    sortable: false,
    width: '300px',
  },
  {
    title: t('label.content'),
    key: 'naiyoKnj',
    align: 'start',
    sortable: false,
  },
])

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * 各セクションのテキストエリア値を取得
 *
 * @param index - セクションのインデックス
 */
// 未使用（旧実装の名残）
const _getTextareaValue = (index: number): string => {
  return local.or52572.shousaiData?.[index]?.naiyoKnj || ''
}

/**
 * 各セクションのテキストエリア値を更新
 *
 * @param index - セクションのインデックス
 *
 * @param value - 新しい値
 */
const updateTextareaValue = (index: number, value: string) => {
  if (local.or52572.shousaiData?.[index]) {
    local.or52572.shousaiData[index].naiyoKnj = value
  }
}

/**
 * 各セクションのOneWayBind設定を取得
 *
 * @param item - 詳細アイテム
 *
 * @param _index - セクションのインデックス
 */
const getSectionOneway = (item: ShousaiInfoType, _index: number) => {
  return {
    // ラベル
    mo01337Oneway: {
      itemLabel: item.koumokuKnj,
      customClass: new CustomClass({ outerClass: 'label-health-state' }),
      itemLabelFontWeight: 'normal',
    } as Mo01337OnewayType,
    // OrX0163用のOneWayBind
    orX0163Oneway: {
      showEditBtnFlg: true,
      disabled: props.onewayModelValue?.cpyFlg ?? false,
      readonly: false,
      textareaReadonly: false,
      align: 'top',
      height: '65px',
      contentClass: 'custom-orx0163-content',
      contentStyle:
        'background-color: white; border: solid 1px rgb(var(--v-theme-light)); border-left: none;',
      hideDetails: true,
    } as OrX0163OnewayType,
  }
}

/**
 * データの初期化
 *
 * @param or52572Oneway - データ
 */
function initData(or52572Oneway: Or52572OnewayType) {
  initFlg = true

  // モデル値の設定
  if (or52572Oneway.shousaiData) {
    local.or52572.shousaiData = or52572Oneway.shousaiData
  }

  initFlg = false
}

/**
 * 入力支援ダイアログを開く
 *
 * @param index - セクションのインデックス
 *
 * @param item - 詳細アイテム
 */
const handleOpenInputSupportDialog = (index: number, item: ShousaiInfoType) => {
  currentSelectedIndex.value = index
  or51775Oneway.title = item.koumokuKnj ?? ''
  or51775Oneway.bunruiId = item.koumokuMstId

  // Or51775のダイアログ開閉状態を更新する
  Or51775Logic.state.set({
    uniqueCpId: or51775_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援ダイアログからの確認処理
 *
 * @param data - 入力支援情報
 */
const handleConfirm = (data: Or51775ConfirmType) => {
  // 選択中のテキストエリアに値を設定
  const targetIndex = currentSelectedIndex.value
  if (local.or52572.shousaiData?.[targetIndex]) {
    local.or52572.shousaiData[targetIndex].naiyoKnj = data.value
  }

  // Or51775のダイアログを閉じる
  Or51775Logic.state.set({
    uniqueCpId: or51775_1.value.uniqueCpId,
    state: { isOpen: false },
  })

  // 親コンポーネントに値を emit
  emit('update:modelValue', local.or52572)
}

/**
 * テキストエリアの値変更処理
 */
const scheduleEmitModelValue = () => {
  if (initFlg) return
  if (rafId !== null) return
  rafId = window.requestAnimationFrame(() => {
    rafId = null
    emit('update:modelValue', local.or52572)
  })
}

/**
 * OrX0163のv-model更新ハンドラ
 *
 * @param index - 対象インデックス
 *
 * @param newValue - 新しい値
 */
function onOrX0163Update(index: number, newValue: OrX0163Type) {
  const value = newValue?.value ?? ''
  // v-model表示用の安定オブジェクトを更新
  if (orX0163Models.value[index]) {
    orX0163Models.value[index].value = value
  }
  updateTextareaValue(index, value)
  emit('shousai-change', { index, value })
  scheduleEmitModelValue()

  // バリデーションエラーを収集
  collectValidationErrors()
}

/**
 * Props の変更を監視
 */
watch(
  () => props.modelValue,
  (newValue) => {
    if (initFlg) {
      return
    }
    local.or52572 = { ...newValue }
    // shousaiData変化に追随してOrX0163モデルを再同期
    orX0163Models.value = (local.or52572.shousaiData || []).map((it) => ({
      value: it.naiyoKnj || '',
    }))
  },
  { deep: true }
)

// 初期同期：OrX0163モデル生成
watch(
  () => local.or52572.shousaiData,
  (list) => {
    orX0163Models.value = (list || []).map((it) => ({ value: it?.naiyoKnj || '' }))
    // データ変更時にバリデーションエラーを収集
    collectValidationErrors()
  },
  { immediate: true, deep: true }
)

watch(
  () => selectedTableItems.value,
  () => {
    Or52572Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: { shousaiData: selectedTableItems.value },
    })
  }
)

/**
 * 入力支援ダイアログ開閉状態
 */
const showInputSupportDialog = computed(() => {
  return Or51775Logic.state.get(or51775_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 外部公開する関数
 */
defineExpose({
  initData,
})
</script>

<template>
  <div
    v-if="!props.onewayModelValue?.cpyFlg"
    class="sections-container"
  >
    <!-- エラーパネル -->
    <base-mo01351
      v-if="showErrorPanel"
      :oneway-model-value="errorPanelOneway"
      class="mb-2"
    />

    <c-v-form  v-model="valid">
      <!-- 各セクションをループで表示 -->
      <div
        v-for="(item, index) in local.or52572.shousaiData || []"
        :key="`section_${index}`"
        class="row-center section-row ml-0"
      >
        <!-- ラベルセクション -->
        <div :class="index === 0 ? 'border-top-none' : ''" class="row-border">
          {{ getSectionOneway(item, index).mo01337Oneway.itemLabel }}
        </div>

        <!-- OrX0163セクション -->
        <div class="input-text">
            <g-custom-or-x0163
              v-model="orX0163Models[index]"
              :oneway-model-value="getSectionOneway(item, index).orX0163Oneway"
              @update:model-value="onOrX0163Update(index, $event)"
              @on-click-edit-btn="handleOpenInputSupportDialog(index, item)"
            />
        </div>
      </div>
    </c-v-form>

    <g-custom-or51775
      v-if="showInputSupportDialog"
      :unique-cp-id="or51775_1.uniqueCpId"
      :oneway-model-value="or51775Oneway"
      @confirm="handleConfirm"
    />
  </div>

  <div
    v-else
    class="data-table-container"
  >
    <c-v-data-table
      v-model="selectedTableItems"
      :items="local.or52572.shousaiData || []"
      :headers="dataTableHeaders"
      item-value="koumokuMstId"
      return-object
      show-select
      fixed-header
      hover
      class="table-wrapper"
      :items-per-page="-1"
      :hide-default-footer="true"
    >
    </c-v-data-table>
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.sections-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-row {
  height: 66px !important;

  display: flex;
  margin-bottom: 0px;
}

.divider-div {
  display: flex;
  align-items: center;
}

.label-icon-container {
  height: 66px;
  width: 136px;
}
.border-top-none {
  border-top: none;
}
.border-c {
  border: solid 1px rgb(var(--v-theme-light));
}
.row-border {
  border: solid 1px rgb(var(--v-theme-light));
  border-bottom: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 136px;
  background-color: rgb(var(--v-theme-blue-100));
}

.align-header {
  align-items: center;
  height: 66px;
  width: 136px;
  margin: 0px;
}

.section-header {
  max-width: 136px;
  min-width: 136px;
  height: 66px;
  padding: 0px;
  padding-left: 12px;
}

.padding-header {
  padding-top: 0px;
}

.white-space {
  white-space: pre-wrap !important;
  padding: 0px;
  height: 66px;
  width: 136px;
}

:deep(.white-space .v-row) {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.wrap {
  background-color: rgb(var(--v-theme-secondaryBackground));
}

:deep(
  table
    .v-selection-control__input:has(
      input[type='checkbox']:not([aria-checked]):not([disabled])[checked]
    )
) {
  i {
    color: rgb(var(--v-theme-blue-700)) !important;
  }
}
.custom-content textarea {
  font-size: 14px;
  line-height: 14px;
  padding: 0px !important;
}

// OrX0163用のスタイル調整
:deep(.custom-orx0163-content) {
  width: 100%;
}
:deep(.input-text) {
  width: 600px;
}
</style>
