import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or29241:有機体:［アセスメント］画面（居宅）（6②）
 * GUI00800_［アセスメント］画面（居宅）（6②）
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or29241Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or29241', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * 基本動作コード区分
     */
    export const TAB_ID = '7'
    /**
     * 線 デフォルト幅
     */
    export const LINE_DEFAULT_WIDTH = 1
    /**
     * 線 デフォルト高さ
     */
    export const LINE_DEFAULT_HEIGHT = 1
    /**
     * 線 デフォルト色
     */
    export const LINE_DEFAULT_COLOR = 'rgb(var(--v-theme-black-200))'
    /**
     * 矢印 サイズ
     */
    export const ARROW_SIZE = 4
    /**
     * 矢印 色
     */
    export const ARROW_COLOR = 'rgb(var(--v-theme-black-200))'
    /**
     * 更新区分 C:新規
     */
    export const UPDATE_KBN_C = 'C'
    /**
     * 更新区分 U:更新
     */
    export const UPDATE_KBN_U = 'U'
    /**
     * 更新区分 D:削除
     */
    export const UPDATE_KBN_D = 'D'
    /**
     * 削除区分 0:削除処理なし
     */
    export const DELETE_KBN_DONT = '0'
    /**
     * 画面モード 通常
     */
    export const MODE_NORMAL = 'normal'

    /**
     * 画面モード 複写
     */
    export const MODE_COPY = 'copy'
    /**
     * 入力物理名(食事場所)
     */
    export const MEALLOCATIONSTR = 'shokujiWhere'
    /**
     * 入力物理名(咀嚼の状況)
     */
    export const CHEWINGSITUATIONSTR = 'soshaku'
    /**
     * 入力物理名(食事の内容)
     */
    export const MEALCONTENTSTR = 'shokujiShu'
    /**
     * 入力物理名(家族実施)
     */
    export const FAMILYIMPLEMENTATIONSTR = 'famJisshi'
    /**
     * 入力物理名(サービス実施)
     */
    export const SERVERIMPLEMENTATIONSTR = 'serJisshi'
    /**
     * 入力物理名(希望)
     */
    export const HOPESTR = 'kibo'
    /**
     * 入力物理名(要援助→計画)
     */
    export const NEEDASSISTANCETOPLANSTR = 'keikaku'
    /**
     * 入力物理名(その他現状)
     */
    export const OTHERCURRENTSTR = 'genMemo'
    /**
     * 入力物理名(その他計画)
     */
    export const OTHERPLANSTR = 'keiMemo'

    /**
     * 入力物理名(介護認定Radio)
     */
    export const RADIOSTR = 'bango'

    /**
     * 入力物理名(checkboxList1)
     */
    export const CHECKBOXSTRARR_1 = [
      ['shuGenjo', 'shuKeikaku'],
      ['hukuGenjo', 'hukuKeikaku'],
      ['seGenjo', 'seKeikaku'],
    ]
    /**
     * 入力物理名(checkboxList2)
     */
    export const CHECKBOXSTRARR_2 = [
      ['ngenjo', 'nkeikaku'],
      ['bgenjo', 'bkeikaku'],
    ]

    /**
     *文字列:-1
     */
    export const STRING_NEGATIVE_1 = '-1'

    /**
     *文字列:0
     */
    export const STRING_0 = '0'
    /**
     *文字列:1
     */
    export const STRING_1 = '1'
    /**
     *文字列:1
     */
    export const STRING_2 = '2'
    /**
     *文字列:1
     */
    export const STRING_3 = '3'
    /**
     *文字列:1
     */
    export const STRING_4 = '4'
    /**
     *文字列:1
     */
    export const STRING_5 = '5'
    /**
     *文字列:1
     */
    export const STRING_6 = '6'
    /**
     *文字列:1
     */
    export const STRING_7 = '7'
    /**
     *文字列:1
     */
    export const STRING_8 = '8'
    /**
     *文字列:1
     */
    export const STRING_9 = '9'
    /**
     *文字列:1
     */
    export const STRING_10 = '10'
    /**
     *文字列:1
     */
    export const STRING_11 = '11'

    /**
     *文字列:21
     */ export const STRING_21 = '21'
    /**
     *文字列:25
     */
    export const STRING_25 = '25'
    /**
     *文字列:212
     */
    export const STRING_212 = '212'
    /**
     *文字列:Relation
     */
    export const RELATIONSTR = 'Relation'
    /**
     *文字列:mealRightArrow （摂取介助 -> 食事）
     */
    export const MEALRIGHTARROW = 'mealRightArrow'
    /**
     *文字列:stapleFood （摂取介助 -> 食事）
     */
    export const STAPLEFOOD = 'stapleFood'
    /**
     *文字列:urinationRightArrow （排尿介助 -> 排尿介助）
     */
    export const URINATIONRIGHTARROW = 'urinationRightArrow'
    /**
     *文字列:urinationAssistance （排尿介助 -> 排尿介助）
     */
    export const URINATIONASSISTANCE = 'urinationAssistance'
    /**
     *文字列:defecationRightArrow （排便介助 -> 排便介助）
     */
    export const DEFECATIONRIGHTARROW = 'defecationRightArrow'
    /**
     *文字列:defecationAssistance （排便介助 -> 排便介助）
     */
    export const DEFECATIONASSISTANCE = 'defecationAssistance'
    /**
     * 改定フラグ H21/4
     */
    export const KAITEI_FLG_H21 = '4'
    /**
     * 改定フラグ R3/4
     */
    export const KAITEI_FLG_R34 = '5'
  }
}
