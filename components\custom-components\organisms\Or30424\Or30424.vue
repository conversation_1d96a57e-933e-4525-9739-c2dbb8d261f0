<script setup lang="ts">
/**
 * Or30424:(インターライ方式_CAP検討_問題と指針)問題と指針-表
 * GUI00864_［検討表］画面（［アセスメント（インターライ）］画面）
 *
 * @description
 * (インターライ方式_CAP検討_問題と指針)問題と指針-表
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
// 必要なVue・ライブラリをインポート
import { reactive, ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or10962Logic } from '../Or10962/Or10962.logic'
import type { Or30424StateType, TableData, TableHeader, FocusRowType } from './Or30424.type'
import { Or30424Const } from './Or30424.constants'
import { Or30424Logic } from './Or30424.logic'
import type {
  Or30424OnewayType,
  Or30424Type,
  KentohyoMondai,
} from '~/types/cmn/business/components/Or30424Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useScreenTwoWayBind } from '~/composables/useComponentVue'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useSystemCommonsStore, useSetupChildProps, useValidation } from '#imports'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import { Or10962Const } from '~/components/custom-components/organisms/Or10962/Or10962.constants'
import type { OrX0173OnewayType } from '~/types/cmn/business/components/OrX0173Type'
const { byteLength } = useValidation()

/**
 * or51775
 */
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(1) })
/**
 * useI18n
 */
const { t } = useI18n()
/**************************************************
 * Props（親コンポーネントから受け取る値の型定義）
 **************************************************/
interface Props {
  onewayModelValue: Or30424OnewayType
  modelValue: Or30424Type
  uniqueCpId: string
  parentUniqueCpId: string
}

/**
 * defineProps
 */
const props = defineProps<Props>()
/**
 * Or10962コンポーネントのユニークID
 */
const or10962 = ref({ uniqueCpId: '' })

/**
 * Or10962から返却されたテキスト
 */
const returnTextOr10962 = ref('')
/**
 * デフォルト値の定義
 */
const defaultOnewayModelValue: Or30424OnewayType = {
  focusRow: Or30424Const.FIELD_NAME,
  kentohyoMondaiList: [],
  selectedItemIndex: -1,
}
// システム共有情報取得
/**
 * systemCommonsStore
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * 行の高さ拡大・縮小フラグ
 */
const rowResizeFlag = ref<boolean>(false)
/**
 * 入力フォーム用のローカル状態
 * 各種OneWayバインディング用
 */
const localOneWay = reactive({
  or30424: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  or51775Oneway: {
    /**
     * タイトル
     */
    title: '',
    /**
     * 大分類ＣＤ
     */
    t1Cd: '725',
    /**
     * 中分類ＣＤ
     */
    t2Cd: '',
    /**
     * 小分類ＣＤ
     */
    t3Cd: '',
    /**
     * テーブル名
     */
    tableName: 'cpn_tuc_rai_plan1',
    /**
     * カラム名
     */
    columnName: '',
    /**
     * アセスメント方式
     */
    assessmentMethod: '',
    /**
     * 文章内容
     */
    inputContents: '',
    /**
     * 利用者ID
     */
    shokuinId: systemCommonsStore.getUserId ?? '',
  } as unknown as Or51775OnewayType,
  memoInputLabelBtn: {
    // デフォルト値を設定
    value: t('label.assessment-memo'),
    customClass: new CustomClass({
      outerClass: 'd-flex align-end',
    }),
  } as Mo01338OnewayType,
  infoIcon: {
    btnIcon: 'info',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    minWidth: '24px',
    minHeight: '24px',
    height: '24px',
    width: '24px',
  } as Mo00009OnewayType,
})

/**************************************************
 * Pinia（双方向バインディング用のカスタムフック）
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or10962Const.CP_ID(0)]: or10962.value,
})
/**
 * 双方向バインディング用
 */
const { refValue } = useScreenTwoWayBind<Or30424Type>({
  cpId: Or30424Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})
/**
 * 選択中の行インデックス
 */
const selectedItemIndex = ref<number>(-1)

/**************************************************
 * 変数定義
 **************************************************/
/**
 * 確認ダイアログ用ユニークID
 */
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

/**
 * テーブルヘッダー定義
 */
const dataTable = ref<Or30424StateType>({
  headers: [
    {
      title: t('label.capand-inducers'),
      key: 'capKnj',
      width: '217px',
      sortable: false,
      required: false,
    },
    {
      title: t('label.consideration-and-analysis'),
      key: 'problemKnj',
      width: '461px',
      sortable: false,
      required: false,
    },
    {
      title: t('label.directions-of-care'),
      key: 'careKnj',
      width: '461px',
      sortable: false,
      required: false,
    },
  ] as TableHeader[],
  items: [],
})
const columnMinWidth = ref<number[]>([217, 461, 461])
// 各列の最小幅を指定
/**************************************************
 * Emit（親へのイベント通知）
 **************************************************/
/**
 * 親コンポーネントへのイベント通知
 */
const emit = defineEmits(['update:modelValue', 'change'])

const orX0173OnewayModelValue = reactive({
  showItemLabel: false,
  rows: 5,
  maxRows: '5',
  maxlength: '4000',
  autoGrow: true,
  rules: [byteLength(4000)],
} as OrX0173OnewayType)
/**
 * 共通: modelValueの更新とemit
 * 双方向バインディング値へ反映し、emitする
 */
function updateModelValue() {
  // 双方向バインディング値へ反映
  if (refValue.value) {
    refValue.value.kentohyoMondaiList = (dataTable.value.items ?? []).map((item) => ({
      raiId: item.raiId ?? 0,
      capId: item.capId ?? '',
      capKnj: item.capKnj?.value ?? '',
      problemKnj: item.problemKnj?.value ?? '',
      careKnj: item.careKnj?.value ?? '',
      sort: item.sort ?? 0,
      youshikiFlg: item.youshikiFlg ?? 0,
      updateKbn: item.updateKbn ?? '',
    }))
    localOneWay.or30424.kentohyoMondaiList = refValue.value.kentohyoMondaiList ?? []
  }
  localOneWay.or30424.selectedItemIndex = selectedItemIndex.value
  emit('update:modelValue', localOneWay.or30424)
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 予定マスタ情報取得・初期化処理
 * テーブルデータ初期化
 */
function init() {
  refValue.value = Or30424Logic.data.get(props.uniqueCpId)
  // 双方向バインディング値からデータ取得
  const data = refValue.value?.kentohyoMondaiList

  if (!data?.length) {
    dataTable.value.items = []
  }
  selectedItemIndex.value = 0
  // データをテーブル表示用に変換
  dataTable.value.items = (data as unknown as KentohyoMondai[]).map(
    (item: KentohyoMondai, idx: number) => ({
      raiId: item.raiId ?? 0,
      capId: item.capId ?? String(idx),
      capKnj: { value: item.capKnj ?? '' },
      problemKnj: { value: item.problemKnj ?? '' },
      careKnj: { value: item.careKnj ?? '' },
      sort: item.sort ?? 0,
      youshikiFlg: item.youshikiFlg ?? 0,
      tableIndex: idx,
      updateKbn: '',
    })
  )
  // updateModelValue()
}

/**
 * 行選択時の処理
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  // すでに選択されている行の場合はemitしない
  if (selectedItemIndex.value === index) return
  selectedItemIndex.value = index
  const selectedRow = dataTable.value.items?.[selectedItemIndex.value]
  if (selectedRow)
    emit('change', {
      capId: selectedRow.capId,
      capKnj: selectedRow.capKnj?.value,
      problemKnj: selectedRow.problemKnj?.value,
      careKnj: selectedRow.careKnj?.value,
      updateKbn: selectedRow.updateKbn,
    })
  updateModelValue()
}

/**
 * 編集時の更新フラグ設定
 */
function onUpdate() {
  // 選択中の行の変更フラグを更新
  ;(dataTable.value.items ?? []).forEach((item: TableData) => {
    if (item.tableIndex === selectedItemIndex.value && item.updateKbn !== UPDATE_KBN.CREATE) {
      item.updateKbn = UPDATE_KBN.UPDATE
    }
  })
  updateModelValue()
}

/**
 * Or21814ダイアログの表示状態を管理する計算プロパティ
 */
const showDialogOr21814 = computed(() => {
  // Or10412のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 行の高さ拡大・縮小
 */
function onResizeRow() {
  rowResizeFlag.value = !rowResizeFlag.value
}
/**
 * テーブルヘッダークリック時にOr51775ダイアログを開く
 *
 * @param title - ヘッダーのタイトル
 *
 * @param key - ヘッダーのキー
 *
 * @param index - index
 */
function onClickOr51775(title: string, key: FocusRowType, index: number) {
  selectRow(index)
  localOneWay.or30424.focusRow = key
  if (!dataTable.value.items?.length) return
  const idx = selectedItemIndex.value
  const item = (dataTable.value.items ?? [])[idx]
  const targetCell = item[key]
  localOneWay.or51775Oneway.title = title
  localOneWay.or51775Oneway.t2Cd =
    key === Or30424Const.PROBLEM_KNJ_FIELD ? Or30424Const.NUMBER_1 : Or30424Const.NUMBER_2
  localOneWay.or51775Oneway.t3Cd = String(item.capId ?? '')
  localOneWay.or51775Oneway.columnName =
    key === Or30424Const.PROBLEM_KNJ_FIELD ? Or30424Const.PROBLEM_KNJ : Or30424Const.CARE_KNJ
  localOneWay.or51775Oneway.inputContents = targetCell.value
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * ダイアログ表示フラグ
 * Or51775のダイアログが開いているかどうか
 */
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or10962ダイアログの表示状態を監視するcomputedプロパティ
 */
const showDialogOr10962 = computed(() => {
  // Or10962のダイアログ開閉状態を取得
  return Or10962Logic.state.get(or10962.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or51775ダイアログからの確認イベントを処理し、ダイアログを閉じる
 *
 * @param data - Or51775ダイアログから返された確認データ
 */
const handleConfirm51775 = (data: Or51775ConfirmType) => {
  if (!dataTable.value.items?.length) return

  const idx = selectedItemIndex.value
  const item = (dataTable.value.items ?? [])[idx]

  if (idx === Or30424Const.NUMBER_MINUS_1 || !item) return

  const key = localOneWay.or30424.focusRow
  const targetCell = item[key]
  if (targetCell) {
    targetCell.value =
      data.type === Or30424Const.NUMBER_1 ? data.value : targetCell.value + data.value

    item.updateKbn = UPDATE_KBN.UPDATE
  }
  onUpdate()
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

watch(
  () => returnTextOr10962.value,
  (newValue) => {
    if (newValue && refValue?.value?.kentohyoMondaiList?.length) {
      // 選択中の行にテキストを追加
      refValue.value.kentohyoMondaiList[selectedItemIndex.value][localOneWay.or30424.focusRow] +=
        `\n${newValue}`
      returnTextOr10962.value = '' // テキストをクリア
      init()
    }
  }
)
/**
 * Or10962ダイアログを開く
 */
function onClickOr10962() {
  // Or10962のダイアログ開閉状態を更新する
  Or10962Logic.state.set({
    uniqueCpId: or10962.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function scrollToSection(sectionId: string) {
  const section = document.getElementById(sectionId)
  if (section) {
    section.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

// 外部からinit関数を呼び出せるように公開
defineExpose({
  init,
  onResizeRow,
})
</script>

<template>
  <div class="list-wrapper">
    <div class="d-flex align-center justify-space-between">
      <base-mo00611
        :oneway-model-value="{
          btnLabel: t('label.assessment-memo'),
        }"
        style="width: 132px !important; height: 32px !important"
        @click="onClickOr10962"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('label.assessment-memo')"
        />
      </base-mo00611>
      <div
        class="link-kanji-style"
        @click="scrollToSection('issues_and_goal')"
      >
        {{ t('label.issues-and-goal-down') }}
      </div>
    </div>

    <c-v-data-table
      v-resizable-grid="{ columnWidths: columnMinWidth }"
      v-bind="{ ...dataTable }"
      fixed-header
      class="table-wrapper mt-4"
      hide-default-footer
      :items-per-page="-1"
    >
      <template #headers>
        <tr>
          <th
            v-for="header in dataTable.headers"
            :key="header.id"
            :width="header.width"
            class="table-header"
          >
            <span class="thead-text">
              {{ header.title }}
            </span>
          </th>
        </tr>
      </template>

      <!-- テーブル行の描画 -->
      <template #item="{ item }">
        <tr
          :id="`data-index-${item.tableIndex}`"
          :class="{
            'highlight-row': selectedItemIndex === item.tableIndex,
            'row-zoom': rowResizeFlag,
          }"
          @click="selectRow(item.tableIndex)"
        >
          <!-- テキストフィールド（CAP） -->
          <td class="text-padding">
            <div class="pl-3 d-flex align-center">
              {{ item.capKnj?.value }}

              <base-mo00009
                class="custom-info"
                :oneway-model-value="localOneWay.infoIcon"
              />
            </div>
          </td>
          <!-- 検討・分析 -->
          <td class="text-padding">
            <g-custom-or-x-0173
              v-model="item.problemKnj"
              :oneway-model-value="orX0173OnewayModelValue"
              @click="
                onClickOr51775(
                  t('label.examination-analysis-issues'),
                  Or30424Const.PROBLEM_KNJ_FIELD,
                  item.tableIndex
                )
              "
            >
            </g-custom-or-x-0173>
          </td>
          <!-- ケア方針 -->
          <td class="text-padding align-top left-align">
            <!-- テキストフィールド（ケア） -->
            <g-custom-or-x-0173
              v-model="item.careKnj"
              :oneway-model-value="orX0173OnewayModelValue"
              @click="
                onClickOr51775(
                  t('label.examination-analysis-issues'),
                  Or30424Const.CARE_KNJ_FIELD,
                  item.tableIndex
                )
              "
            >
            </g-custom-or-x-0173>
          </td>
        </tr>
      </template>
    </c-v-data-table>
  </div>

  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>

  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="localOneWay.or51775Oneway"
    @confirm="handleConfirm51775"
  />
  <!--10962-->
  <g-custom-or-10962
    v-if="showDialogOr10962"
    v-bind="or10962"
    v-model="returnTextOr10962"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';
:deep(button.button-style) {
  border-radius: none;
  border: none;
  height: 100%;
  background: transparent;
}

.table-wrapper :deep(.v-table__wrapper th) {
  background-color: #dbeefe !important;
}

.table-wrapper {
  :deep(.v-data-table__td) {
    padding: 0px 12px !important;
    height: 32px !important;
    :deep(.v-data-table-header__content) {
      font-weight: 500 !important;
    }
  }
}

.table-wrapper .v-table__wrapper th {
  font-weight: 500 !important;
}

.thead-with-iconbtn {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-wrapper {
  width: 1139px;
}
:deep(.v-col) {
  padding: 0px !important;
}
:deep(.row-zoom) {
  height: 120px;
}
.text-padding {
  padding: 0px !important;
  background-color: transparent !important;
}
:deep(.v-table--fixed-header) {
  position: sticky;
  top: -0.5px;
  z-index: 2;
}

:deep(tbody > tr) {
  height: 79.9px;
}
:deep(.v-field__outline__start),
:deep(.v-field__outline__end) {
  opacity: 0 !important;
}
/* テーブルラッパーのスタイル */
:deep(.table-wrapper) {
  /* テーブル内のセル共通スタイル */
  .v-table__wrapper {
    td {
      /* textareaを含むセルのスタイル */
      &:has(textarea) {
        .table-cell:not([disabled]):not([readonly]) {
          outline: none !important;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        div:has(textarea:not([disabled]):not([readonly])) {
          height: 101px !important;
        }
      }
    }
  }
}
.cap-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}
.link-kanji-style {
  text-align: right;
  font-size: 14px;
  background: transparent;
  max-width: auto;
  cursor: pointer; // マウスカーソルはポインター
  &:hover {
    text-decoration: underline;
  }
}
:deep(.v-sheet .v-text-field .v-field) {
  background-color: transparent !important;
  border: 0px solid #cdcdcd;
}
:deep(.v-btn__content) {
  padding-left: 4px;
  padding-right: 6px;
}

.table-header {
  font-weight: 400 !important;
  height: 32px !important;
  padding: 0px 12px !important;
}

:deep(.custom-info) {
  .material-symbols-rounded {
    font-variation-settings:
      'FILL' 1,
      'wght' 300,
      'GRAD' 0,
      'opsz' 20;
    opacity: 0.6;
    font-size: 16px;
  }
}
</style>
