import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * Or35672:アセスメント複写モーダル
 * GUI00807_アセスメント複写
 *
 * @description
 * アセスメント複写モーダルAPI用エンティティ
 *
 * <AUTHOR>
 */
/**
 * アセスメント複写モーダルAPI更新入力エンティティ
 */
export interface AssessmentDuplicateUpdateInEntity extends InWebEntity {
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 利用者ID */
  userId: string
  /** 事業所ID */
  svJigyoId: string
  /** 複写先アセスメントID */
  tgtGdlId: string
  /** 複写先期間ID */
  tgtSc1Id: string
  /** 画面Noリスト */
  noList: string[]
  /** 改訂フラグ */
  ninteiFlg: string
  /** 作成日 */
  createYmd: string
  /** 記載者ID */
  shokuId: string
  /** 複写元期間ID */
  defSc1Id: string
  /** 複写元アセスメントID */
  defGdlId: string
  /** 種別ID */
  syubetsuId: string
}

/**
 * アセスメント複写モーダルAPI更新入力エンティティ
 */
export interface AssessmentDuplicateUpdateOutEntity extends OutWebEntity {
  /** data */
  data: object
}
