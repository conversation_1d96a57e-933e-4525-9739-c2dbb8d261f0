import { Or17631Const } from '../Or17631/Or17631.constants'
import { Or17630Const } from '../Or17630/Or17630.constants'
import { Or17629Const } from '../Or17629/Or17629.constants'
import { Or17628Const } from '../Or17628/Or17628.constants'
import { Or17635Const } from '../Or17635/Or17635.constants'
import { Or17634Const } from '../Or17634/Or17634.constants'
import { Or17633Const } from '../Or17633/Or17633.constants'
import { Or17631Logic } from '../Or17631/Or17631.logic'
import { Or17630Logic } from '../Or17630/Or17630.logic'
import { Or17629Logic } from '../Or17629/Or17629.logic'
import { Or17628Logic } from '../Or17628/Or17628.logic'
import { Or17635Logic } from '../Or17635/Or17635.logic'
import { Or17634Logic } from '../Or17634/Or17634.logic'
import { Or17633Logic } from '../Or17633/Or17633.logic'
import { Or10899Const } from './Or10899.constants'
import type { Or10899StateType } from './Or10899.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'

/**
 * Or10899:有機体:予防計画書マスタモーダル
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 */
export namespace Or10899Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10899Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or17631Const.CP_ID(1) },
        { cpId: Or17630Const.CP_ID(1) },
        { cpId: Or17629Const.CP_ID(1) },
        { cpId: Or17628Const.CP_ID(1) },
        { cpId: Or17635Const.CP_ID(1) },
        { cpId: Or17634Const.CP_ID(1) },
        { cpId: Or17633Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or21815Const.CP_ID(1) },
      ],

      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or17631Logic.initialize(childCpIds[Or17631Const.CP_ID(1)].uniqueCpId)
    Or17630Logic.initialize(childCpIds[Or17630Const.CP_ID(1)].uniqueCpId)
    Or17629Logic.initialize(childCpIds[Or17629Const.CP_ID(1)].uniqueCpId)
    Or17628Logic.initialize(childCpIds[Or17628Const.CP_ID(1)].uniqueCpId)
    Or17635Logic.initialize(childCpIds[Or17635Const.CP_ID(1)].uniqueCpId)
    Or17634Logic.initialize(childCpIds[Or17634Const.CP_ID(1)].uniqueCpId)
    Or17633Logic.initialize(childCpIds[Or17633Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10899StateType>(Or10899Const.CP_ID(0))
}
