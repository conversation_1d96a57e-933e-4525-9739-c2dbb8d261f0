/**
 * ［アセスメント］画面（居宅）（3）郵便番号情報リストを取得する
 *
 * @description
 * ［アセスメント］画面（居宅）（3）郵便番号情報リストを取得する
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { AssessmentMotZipFocusOutEntitySelectInEntity } from '~/repositories/cmn/entities/AssessmentMotZipFocusOutEntity'
/**
 * ［アセスメント］画面（居宅）（3）郵便番号情報リストを取得する
 *
 * @description
 * ［アセスメント］画面（居宅）（3）郵便番号情報リストを取得する
 * dataName："assessmentMotZipFocusOutSelect"
 */
export function handler(inEntity: AssessmentMotZipFocusOutEntitySelectInEntity) {
  let motZipList
  if (inEntity.zip === '100') {
    motZipList = defaultData.data1.motZipList
  } else if (inEntity.zip === '200') {
    motZipList = defaultData.data2.motZipList
  } else {
    motZipList = []
  }
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      motZipList: motZipList,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
