<script setup lang="ts">
/**
 * Or22951:適用事業所の選択モーダル
 * GUI02259_適用事業所の選択
 *
 * @description
 * 適用事業所の選択モーダル
 *
 * <AUTHOR>
 */

import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or22951Const } from './Or22951.constants'
import type { Or22951StateType } from './Or22951.type'
import { useScreenOneWayBind, useSetupChildProps, useSystemCommonsStore } from '#build/imports'
import type {
  Or22951OnewayType,
  Or22951TableItem,
} from '~/types/cmn/business/components/Or22951Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type {
  Mo01334Headers,
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type {
  ApplicableOfficeSelectInEntity,
  ApplicableOfficeSelectOutEntity,
} from '~/repositories/cmn/entities/ApplicableOfficeSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type {
  ApplicableOfficeConfirmSelectInEntity,
  ApplicableOfficeConfirmSelectOutEntity,
  ApplicableOfficeInfo,
  ApplicableOfficeResponseInfo,
} from '~/repositories/cmn/entities/ApplicableOfficeConfirmSelectEntity'

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue?: Or22951OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

const or21814 = ref({ uniqueCpId: '' }) // 確認ダイアログ
const or21815 = ref({ uniqueCpId: '' }) // 警告ダイアログ

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// デフォルトoneway
const defaultOneway = {
  // 本画面
  or22951Oneway: {
    /** システムコード */
    systemCode: '',
    /** 機能ID */
    functionId: '',
  } as Or22951OnewayType,
}

// ローカル
const local = reactive({
  // ダイアログ
  mo00024: {
    isOpen: true,
  },
  // 適用事業所一覧
  mo01334: {
    value: '',
  } as Mo01334Type,
})

// ローカルoneway
const localOneway = reactive({
  // 本画面oneway
  or22951neway: {
    ...defaultOneway.or22951Oneway,
    ...props.onewayModelValue,
  } as Or22951OnewayType,
  // ダイアログ
  mo00024Oneway: {
    width: '960',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.applicable-office-select'),
      toolbarTitleCenteredFlg: false,
    },
  } as Mo00024OnewayType,
  // 適用事業所一覧
  mo01334Oneway: {
    headers: [] as Mo01334Headers[],
    items: [] as Mo01334Items[],
    showSelect: true,
    mandatory: false,
    height: '294',
    rowHeight: '32',
  } as Mo01334OnewayType,
  // 閉じるボタン
  mo00611Oneway: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 確定ボタン
  mo00609_confirmOneway: {
    btnLabel: t('btn.confirm'),
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  // コントロール初期化
  initControls()

  // 初期情報取得
  void getInitDataInfo()
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or22951StateType>({
  cpId: Or22951Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or22951Const.DEFAULT.IS_CLOSE
    },
  },
})

/**************************************************
 * ウォッチャー
 **************************************************/
// 画面開閉フラグ
watch(
  () => local.mo00024.isOpen,
  () => {
    setState({ isOpen: local.mo00024.isOpen })
  }
)

/**************************************************
 * イベント処理
 **************************************************/
/**
 *  閉じる/Xボタン押下時の処理
 */
function onClose() {
  setState({ isOpen: false })
}

/**
 *  保存ボタン押下時の処理
 */
async function onConfirm() {
  // 選択情報
  const selectInfoList = [] as ApplicableOfficeInfo[]
  // 一覧情報
  const tableList = cloneDeep(
    localOneway.mo01334Oneway.items as Mo01334Items[]
  ) as Or22951TableItem[]

  if (localOneway.mo01334Oneway.selectStrategy === Or22951Const.DEFAULT.SELECT_MODE_ALL) {
    // 複数選択の場合
    if (local.mo01334.values.length > 0) {
      tableList.forEach((item) => {
        if (local.mo01334.values.includes(item.id)) {
          selectInfoList.push({
            /** 法人名 */
            houjinKnj: item.houjinKnj.value,
            /** 施設名 */
            shisetuKnj: item.shisetuKnj.value,
            /** 事業名 */
            jigyoKnj: item.jigyoKnj.value,
            /** 事業名（略称） */
            jigyoRyakuKnj: item.jigyoRyakuKnj.value,
            /** サービス事業所名 */
            svJigyoKnj: item.svJigyoKnj.value,
            /** 法人ID */
            houjinId: item.houjinId,
            /** 施設ID */
            shisetuId: item.shisetuId,
            /** サービス事業者コード */
            svJigyoCd: item.svJigyoCd,
            /** サービス事業者ID */
            svJigyoId: item.svJigyoId,
          })
        }
      })
    } else {
      // 未選択の場合、警告メッセージを表示
      // メッセージコード：w.cmn.20501
      // メッセージ内容：事業所が適用されていません。
      openWarningDialog(t('message.w-cmn-20501'))
      return
    }
  } else {
    // 単一選択の場合
    if (local.mo01334.value.length > 0) {
      tableList.forEach((item) => {
        if (local.mo01334.value === item.id) {
          selectInfoList.push({
            /** 法人名 */
            houjinKnj: item.houjinKnj.value,
            /** 施設名 */
            shisetuKnj: item.shisetuKnj.value,
            /** 事業名 */
            jigyoKnj: item.jigyoKnj.value,
            /** 事業名（略称） */
            jigyoRyakuKnj: item.jigyoRyakuKnj.value,
            /** サービス事業所名 */
            svJigyoKnj: item.svJigyoKnj.value,
            /** 法人ID */
            houjinId: item.houjinId,
            /** 施設ID */
            shisetuId: item.shisetuId,
            /** サービス事業者コード */
            svJigyoCd: item.svJigyoCd,
            /** サービス事業者ID */
            svJigyoId: item.svJigyoId,
          })
        }
      })
    } else {
      // 未選択の場合、警告メッセージを表示
      // メッセージコード：w.cmn.20501
      // メッセージ内容：事業所が適用されていません。
      openWarningDialog(t('message.w-cmn-20501'))
      return
    }
  }

  // バックエンドAPIから初期情報取得
  // TODO 共通情報の一部が未実装
  const inputData: ApplicableOfficeConfirmSelectInEntity = {
    /** 機能ID */
    kinouId: systemCommonsStore.getFunctionId ?? '1',
    /** 職員ID */
    shokuinId: systemCommonsStore.getCurrentUser.chkShokuId ?? '1',
    /** システムコード */
    sysCd: systemCommonsStore.getSystemCode ?? '1',
    /** 自由パラメータvalL */
    freeparmValL: props.onewayModelValue?.freeparmValL ?? '1',
    /** 適用事業所ＩＤ */
    jid: (systemCommonsStore.getSvJigyoIdList as string[]) ?? [],
    /** 選択した事業所一覧リスト */
    applicableOfficeList: selectInfoList,
  }
  const resData: ApplicableOfficeConfirmSelectOutEntity = await ScreenRepository.select(
    'applicableOfficeConfirmSelect',
    inputData
  )

  if (resData.data) {
    if (resData.data?.checkFlg === Or22951Const.DEFAULT.JIGYO_CHECK_FLG_SINGLE_NO_APPLICABLE) {
      // APIのレスポンスパラメータ.チェックフラグが1（※事業所の適用できない）の場合
      // メッセージコード：w.cmn.20502
      // メッセージ内容：
      // 法人または施設を複数適用しています。[改行]
      // 単独の法人・施設の事業所のみ適用してください。
      openWarningDialog(t('message.w-cmn-20502'))
      return
    } else if (
      resData.data?.checkFlg === Or22951Const.DEFAULT.JIGYO_CHECK_FLG_MULTIPLE_NO_APPLICABLE
    ) {
      // APIのレスポンスパラメータ.チェックフラグが2（※複数適用できない）の場合
      // メッセージコード：w.cmn.20503
      // メッセージ内容：
      // 事業所を複数適用しています。[改行]
      // 一つの事業所のみ適用してください。
      openWarningDialog(t('message.w-cmn-20503'))
      return
    } else if (
      resData.data?.checkFlg === Or22951Const.DEFAULT.JIGYO_CHECK_FLG_OTHER_NO_APPLICABLE
    ) {
      // APIのレスポンスパラメータ.チェックフラグが3（※その他の適用できない）の場合
      // メッセージコード：w.cmn.20504
      // メッセージ内容：
      // {0}は同時に適用できません。
      // パラメータ:
      // {0}: APIのレスポンスパラメータ.テキスト
      openWarningDialog(t('message.w-cmn-20504', [resData.data?.naiyou]))
      return
    } else if (resData.data?.checkFlg === Or22951Const.DEFAULT.JIGYO_CHECK_FLG_NO_VIEW_PERMISSION) {
      // APIのレスポンスパラメータ.チェックフラグが4（※閲覧権限なし）の場合
      // メッセージコード：i.cmn.10423
      // メッセージ内容：
      // 閲覧権限がありません。
      openConfirmDialog(t('message.i-cmn-10423'))
      return
    } else if (resData.data?.checkFlg === Or22951Const.DEFAULT.JIGYO_CHECK_FLG_APPLICABLE) {
      // APIのレスポンスパラメータ.チェックフラグが0（※適用チェックが発生しない）の場合
      // レスポンスパラメータ.事業所一覧リストを呼び出し元の画面へ返却し、本画面を閉じる。
      // 戻り値設定
      const rtnData: ApplicableOfficeResponseInfo[] = resData.data?.applicableOfficeList ?? []
      // 選択情報値戻り
      emit('update:modelValue', rtnData)

      // 画面閉じる
      onClose()
    }
  }
}

/**************************************************
 * 関数
 **************************************************/
/**
 *  コントロール初期化
 */
function initControls() {
  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // 警告ダイアログを初期化
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: false,
      dialogTitle: t('label.caution'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // 適用事業所一覧 ヘッダー
  localOneway.mo01334Oneway.headers = [
    {
      title: t('label.corporation-name'),
      key: 'houjinKnj',
      sortable: false,
      minWidth: '140',
    },
    {
      title: t('label.facility-name'),
      key: 'shisetuKnj',
      sortable: false,
      minWidth: '140',
    },
    {
      title: t('label.office-name'),
      key: 'jigyoKnj',
      sortable: false,
      minWidth: '140',
    },
    {
      title: t('label.office-abbreviation'),
      key: 'jigyoRyakuKnj',
      sortable: false,
      minWidth: '240',
    },
    {
      title: t('label.office-kind-name'),
      key: 'svJigyoKnj',
      sortable: false,
      minWidth: '180',
    },
  ] as Mo01334Headers[]

  // 選択モードを設定
  if (localOneway.or22951neway.systemCode === Or22951Const.DEFAULT.SYS_CODE_60201) {
    // システムコードが60201の場合、複数選択できる
    localOneway.mo01334Oneway.selectStrategy = Or22951Const.DEFAULT.SELECT_MODE_ALL
    local.mo01334.values = []
  } else {
    if (localOneway.or22951neway.functionId !== Or22951Const.DEFAULT.FUNCTION_ID_130000029) {
      // システムコードが60201ではない場合 かつ 機能IDが130000029ではない場合、複数選択できる
      localOneway.mo01334Oneway.selectStrategy = Or22951Const.DEFAULT.SELECT_MODE_ALL
      local.mo01334.values = []
    } else {
      // システムコードが60201ではない場合 かつ 機能IDが130000029の場合、単一選択できる
      localOneway.mo01334Oneway.selectStrategy = Or22951Const.DEFAULT.SELECT_MODE_SINGLE
    }
  }
}

/**
 *  画面初期情報取得
 */
async function getInitDataInfo() {
  // バックエンドAPIから初期情報取得
  // TODO 共通情報の一部が未実装
  const inputData: ApplicableOfficeSelectInEntity = {
    /** メニュー３ID */
    menu3Id: systemCommonsStore.getMenuInfo?.menu3?.functionId ?? '1',
    /** 利用者リスト画面の区分 */
    ulistKbn: [systemCommonsStore.getMenuInfo?.menu3?.userStaffListCategory1 ?? '1'],
    /** システムコード */
    gsysCd: systemCommonsStore.getSystemCode ?? '1',
    /** 適用事業所ＩＤ */
    jid: systemCommonsStore.getSvJigyoIdList as string[],
    /** システム日付 */
    appYmd: systemCommonsStore.getSystemDate ?? '1',
    /** 利用者ＩＤ */
    userId: systemCommonsStore.getUserId ?? '1',
    /** 事業所CD */
    svJigyoCd: (systemCommonsStore.getSvJigyoCdList as string[]) ?? [],
    /** 過去の利用者を表示 */
    kakoriyoAllUse: String(
      systemCommonsStore.systemCommons.localCommons.commonFilterConditions?.forUserList
        ?.kakoriyoAllUse ?? '1'
    ),
    /** 過去の利用者を表示の使用フラグ */
    kakoriyoAllFlg: String(
      systemCommonsStore.systemCommons.localCommons.commonFilterConditions?.forUserList
        ?.kakoriyoAllFlg ?? '1'
    ),
    /** システム年月 */
    opeMonth: systemCommonsStore.getSystemDate ?? '1',
    /** システム年月の月末 */
    opeMonthEnd: getEndOfMonth(systemCommonsStore.getSystemDate ?? '1'),
    /** ショートの絞り込みフラグ */
    shortKbn: String(
      systemCommonsStore.systemCommons.localCommons.commonFilterConditions?.forUserList?.shortKbn ??
        ''
    ),
    /** ショートの猶予日数 */
    shortNissu: String(
      systemCommonsStore.systemCommons.localCommons.commonFilterConditions?.forUserList
        ?.shortNissu ?? '1'
    ),
    /** ショートの絞り込みを使用する */
    shortUse: String(
      systemCommonsStore.systemCommons.localCommons.commonFilterConditions?.forUserList?.shortUse ??
        ''
    ),
    /** 利用モード */
    useMode: props.onewayModelValue?.useMode ?? '1',
    /** 自由パラメータvalL */
    freeparmValL: props.onewayModelValue?.freeparmValL ?? '1',
  }
  const resData: ApplicableOfficeSelectOutEntity = await ScreenRepository.select(
    'applicableOfficeSelect',
    inputData
  )
  // 画面情報を設定
  if (resData.data) {
    setFormData(resData)
  }
}
/**
 * 月末日付を取得する
 *
 * @param dateString - 日付文字列（YYYY-MM-DD形式）
 *
 * @returns 月末日付（YYYY/MM/DD形式）、不正な場合は空文字
 */
function getEndOfMonth(dateString: string): string {
  if (!dateString) return ''
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return ''
  const year = date.getFullYear()
  const month = date.getMonth() + 1 // getMonth()は0-11を返すため+1する
  const nextMonth = new Date(year, month, 0) // 翌月の0日を指定すると当月の月末になる
  // YYYY-MM-DD → YYYY/MM/DD に変換して返す
  return nextMonth.toISOString().split('T')[0].replace(/-/g, '/')
}
/**
 *  画面データを設定
 *
 * @param resData - 設定データ
 */
function setFormData(resData: ApplicableOfficeSelectOutEntity) {
  localOneway.mo01334Oneway.items.splice(0)

  // 行データのカスタマイズ様式
  const customClass = new CustomClass({
    outerStyle: 'background-color: rgba(0, 0, 0, 0);',
    labelClass: 'ma-0',
    itemClass: 'overflowContent',
  })

  if (resData.data?.applicableOfficeList) {
    resData.data?.applicableOfficeList.forEach((item, index) => {
      localOneway.mo01334Oneway.items.push({
        /** id */
        id: (index + 1).toString(),
        /** 法人名 */
        houjinKnj: { value: item.houjinKnj, customClass: customClass } as Mo01337OnewayType,
        /** 施設名 */
        shisetuKnj: { value: item.shisetuKnj, customClass: customClass } as Mo01337OnewayType,
        /** 事業名 */
        jigyoKnj: { value: item.jigyoKnj, customClass: customClass } as Mo01337OnewayType,
        /** 事業名（略称） */
        jigyoRyakuKnj: { value: item.jigyoRyakuKnj, customClass: customClass } as Mo01337OnewayType,
        /** サービス事業所名 */
        svJigyoKnj: { value: item.svJigyoKnj, customClass: customClass } as Mo01337OnewayType,
        /** 法人ID */
        houjinId: item.svJigyoKnj,
        /** 施設ID */
        shisetuId: item.shisetuId,
        /** サービス事業者コード */
        svJigyoCd: item.svJigyoCd,
        /** サービス事業者ID */
        svJigyoId: item.svJigyoId,
      } as Or22951TableItem)

      // 親画面から選択行IDを設定
      if (localOneway.mo01334Oneway.selectStrategy === Or22951Const.DEFAULT.SELECT_MODE_ALL) {
        // 複数選択の場合
        localOneway.or22951neway.selectList.forEach((selItem) => {
          if (item.houjinId === selItem.houjinId && item.shisetuId === selItem.shisetsuId) {
            local.mo01334.values.push((index + 1).toString())
          }
        })
      } else {
        // 単一選択の場合
        if (localOneway.or22951neway.selectList.length > 0) {
          if (
            item.houjinId === localOneway.or22951neway.selectList[0].houjinId &&
            item.shisetuId === localOneway.or22951neway.selectList[0].shisetsuId
          ) {
            local.mo01334.value = (index + 1).toString()
          }
        }
      }
    })
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 */
function openConfirmDialog(paramDialogText: string) {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
}

/**
 * 警告ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 */
function openWarningDialog(paramDialogText: string) {
  // 警告ダイアログ表示
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet class="d-flex flex-column table-header">
        <!-- 履歴一覧 -->
        <base-mo-01334
          v-model="local.mo01334"
          :oneway-model-value="localOneway.mo01334Oneway"
          class="list-wrapper"
        >
          <!-- 法人名 -->
          <template #[`item.houjinKnj`]="{ item }">
            <base-mo01337 :oneway-model-value="item.houjinKnj" />
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.houjinKnj.value"
              open-delay="200"
            />
          </template>
          <!-- 施設名 -->
          <template #[`item.shisetuKnj`]="{ item }">
            <base-mo01337 :oneway-model-value="item.shisetuKnj" />
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.shisetuKnj.value"
              open-delay="200"
            />
          </template>
          <!-- 事業所名 -->
          <template #[`item.jigyoKnj`]="{ item }">
            <base-mo01337 :oneway-model-value="item.jigyoKnj" />
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.jigyoKnj.value"
              open-delay="200"
            />
          </template>
          <!-- 事業所略称 -->
          <template #[`item.jigyoRyakuKnj`]="{ item }">
            <base-mo01337 :oneway-model-value="item.jigyoRyakuKnj" />
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.jigyoRyakuKnj.value"
              open-delay="200"
            />
          </template>
          <!-- 事業種別名 -->
          <template #[`item.svJigyoKnj`]="{ item }">
            <base-mo01337 :oneway-model-value="item.svJigyoKnj" />
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.svJigyoKnj.value"
              open-delay="200"
            />
          </template>
          <!-- ページングを非表示 -->
          <template #bottom />
        </base-mo-01334>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- 閉じる -->
      <base-mo00611
        :oneway-model-value="localOneway.mo00611Oneway"
        @click="onClose"
      />
      <!-- 確定 -->
      <base-mo00609
        class="ml-2"
        :oneway-model-value="localOneway.mo00609_confirmOneway"
        @click="onConfirm"
      />
    </template>
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814" />
    <!-- Or21815:有機体:警告ダイアログ -->
    <g-base-or21815 v-bind="or21815" />
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';

:deep(.overflowContent .v-col) {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  position: relative;
  font-size: 14px;

  label {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    line-clamp: 1;
    -webkit-line-clamp: 1;
    position: absolute;
  }
}
</style>
