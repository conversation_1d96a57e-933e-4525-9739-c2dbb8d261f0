<script setup lang="ts">
/**
 * Or26257:有機体:職員検索モーダル
 * GUI00220_職員検索画面
 *
 * @description
 * 職員検索画面
 *
 * <AUTHOR> 董永強
 */
import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { isEmpty } from 'lodash'
import { OrX0006Const } from '../OrX0006/OrX0006.constants'
import { Or26257Const } from './Or26257.constants'
import type { Or26257StateType } from './Or26257.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type {
  Or26257OnewayType,
  Or26257Type,
  SvJigyo,
} from '~/types/cmn/business/components/Or26257Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { OrX0113Const } from '~/components/custom-components/organisms/OrX0113/OrX0113.constants'
import { OrX0113Logic } from '~/components/custom-components/organisms/OrX0113/OrX0113.logic'
import type { OrX0113Type, OrX0113OnewayType } from '~/types/cmn/business/components/OrX0113Type'
import type {
  StaffSearchInitSelectInEntity,
  StaffSearchInitSelectOutEntity,
  Shokuin,
} from '~/repositories/cmn/entities/StaffSearchInitSelectEntity'
import type {
  StaffSearchDateChangeInEntity,
  StaffSearchDateChangeOutEntity,
} from '~/repositories/cmn/entities/StaffSearchDateChangeEntity'
import type {
  StaffSearchRelatedInEntity,
  StaffSearchRelatedOutEntity,
} from '~/repositories/cmn/entities/StaffSearchRelatedEntity'
import type {
  StaffSearchOfficeInEntity,
  StaffSearchOfficeOutEntity,
} from '~/repositories/cmn/entities/StaffSearchOfficeEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { convHiraganaToHalfKanaMap } from '~/constants/KanaMap'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00030OnewayType } from '~/types/business/components/Mo00030Type'
import { useValidation } from '@/utils/useValidation'
const { byteLength } = useValidation()

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
const orX0006_1 = ref({ uniqueCpId: '' })
const orX0113 = ref({ uniqueCpId: OrX0113Const.CP_ID(0) })
const or00094 = ref({ uniqueCpId: '' }) // 五十音ヘッドライン

interface Props {
  modelValue: Or26257Type
  onewayModelValue: Or26257OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const local = reactive({
  or26257: {} as Or26257Type,
  shoriYmd: {
    value: '',
  },
  staffNumber: {
    mo00045: {
      value: '',
    },
  },
  staffName: {
    value: '',
  },
  staffFurigana: {
    mo00045: {
      value: '',
    },
  },
  onlyStaffScheduledToday: {
    modelValue: false,
  },
  staffList: [] as Shokuin[],
  selectedItem: {} as OrX0113Type,
  selectedOfficeItem: [] as string[],
  selectedOccupationItem: [] as string[],
  selectedLicenseItem: [] as string[],
})

const localOneway = reactive({
  or26257: {
    ...props.onewayModelValue,
  },
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定ボタン
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  //職員検索画面
  mo00024Oneway: {
    width: '950px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or26257',
      toolbarTitle: t('label.search-staff'),
      toolbarName: 'Or26257ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // カレンダー
  mo00020Oneway: {
    showItemLabel: false,
    hideDetails: true,
    maxlength: '10',
    customClass: new CustomClass({ labelClass: 'ma-1' }),
    showSelectArrow: true,
    width: '140',
    mo00009OnewayBack: {
      tooltipText: t('tooltip.display-before-day'),
    },
    mo00009OnewayForward: {
      tooltipText: t('tooltip.display-next-day'),
    },
  } as Mo00020OnewayType,
  // 職員番号
  m00038StaffNumberOneway: {
    mo00045Oneway: {
      name: 'staffNumber',
      itemLabel: t('label.staff-number'),
      isRequired: false,
      isVerticalLabel: false,
      width: '135px',
      hideDetails: true,
      customClass: new CustomClass({
        labelClass: 'pt-0 ma-2',
      }),
      maxLength: '10',
      rules: [byteLength(10)],
    },
    isEditCamma: false,
  } as Mo00038OnewayType,
  // 職員名
  mo00045StaffNameOneway: {
    itemLabel: t('label.staff-name'),
    isVerticalLabel: false,
    width: '235',
    customClass: new CustomClass({
      labelClass: 'pt-0 ma-2',
    }),
    maxLength: '42',
    rules: [byteLength(42)],
  } as Mo00045OnewayType,
  // 職員カナ
  mo00030StaffFuriganaOneway: {
    mo00045Oneway: {
      showItemLabel: true,
      itemLabel: t('label.staff-search-furigana'),
      isVerticalLabel: false,
      width: '235',
      customClass: new CustomClass({
        labelClass: 'pt-0 ma-2',
      }),
      maxLength: '40',
    } as Mo00045OnewayType,
    rules: [byteLength(40)],
    mode: '2',
  } as Mo00030OnewayType,
  //事業所
  mo00615OfficeOneway: {
    itemLabel: t('label.office-pl'),
    customClass: new CustomClass({ outerClass: 'ma-2' }),
  } as Mo00615OnewayType,
  mo00611OfficeOneway: {
    name: 'officeBtn',
    btnLabel: '',
    minWidth: '36px',
    class: 'btn-office',
  } as Mo00611OnewayType,
  // 職種
  mo00615OccupationOneway: {
    itemLabel: t('label.occupation'),
    customClass: new CustomClass({ outerClass: 'ma-2' }),
  } as Mo00615OnewayType,
  mo00611OccupationOneway: {
    name: 'OccupationBtn',
    btnLabel: '',
    minWidth: '36px',
    class: 'btn-occupation',
  } as Mo00611OnewayType,
  mo01338OccupationOneway: {
    value: '',
  } as Mo01338OnewayType,
  // 資格免許
  mo00615LicenseOneway: {
    itemLabel: t('label.license'),
    customClass: new CustomClass({ outerClass: 'ma-2' }),
  } as Mo00615OnewayType,
  mo00611LicenseOneway: {
    name: 'licenseBtn',
    btnLabel: '',
    minWidth: '36px',
    class: 'btn-license',
  } as Mo00611OnewayType,
  mo01338LicenseOneway: {
    value: '',
  } as Mo01338OnewayType,
  // 本日の勤務予定者のみ
  mo00018Oneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: t('label.only-staff-scheduled-today'),
    customClass: new CustomClass({ outerClass: 'mt-1' }),
  } as Mo00018OnewayType,
  //人数(未設定含む)
  mo00615StaffCntOneway: {
    itemLabel: t('label.number-of-people', [0]),
  } as Mo00615OnewayType,
  // 項目選択画面
  orX0113Oneway: {
    itemCategory: '',
    processMode: '1',
    selectedIdList: [],
    svJigyoIdList:[],
  } as OrX0113OnewayType,
})

// テーブルヘッダ
const staffHeader = [
  {
    title: t('label.staff-number'),
    key: 'shokuNumber',
    width: '120px',
    align: 'left',
    sortable: true,
    sortRaw(a: Shokuin, b: Shokuin) {
      if (
        a.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE ||
        b.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE
      ) {
        return 0
      }
      if (a.shokuNumber.localeCompare(b.shokuNumber) < 0) {
        return -1
      } else if (a.shokuNumber.localeCompare(b.shokuNumber) > 0) {
        return 1
      } else {
        return 0
      }
    },
  },
  {
    title: t('label.staff-name'),
    key: 'shokuin1Knj,shokuin2Knj',
    width: '180px',
    align: 'left',
    sortable: true,
    sortRaw(a: Shokuin, b: Shokuin) {
      if (
        a.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE ||
        b.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE
      ) {
        return 0
      }
      if (a.shokuNumber.localeCompare(b.shokuNumber) < 0) {
        return -1
      } else if (a.shokuNumber.localeCompare(b.shokuNumber) > 0) {
        return 1
      } else {
        return 0
      }
    },
  },
  {
    title: t('label.main-occupation'),
    key: 'shokushuKnj1',
    width: '135px',
    align: 'left',
    sortable: true,
    sortRaw(a: Shokuin, b: Shokuin) {
      if (
        a.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE ||
        b.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE
      ) {
        return 0
      }
      if (a.shokuNumber.localeCompare(b.shokuNumber) < 0) {
        return -1
      } else if (a.shokuNumber.localeCompare(b.shokuNumber) > 0) {
        return 1
      } else {
        return 0
      }
    },
  },
  {
    title: t('label.other-occupation'),
    key: 'shokushuKnj2',
    width: '115px',
    align: 'left',
    sortable: true,
    sortRaw(a: Shokuin, b: Shokuin) {
      if (
        a.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE ||
        b.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE
      ) {
        return 0
      }
      if (a.shokuNumber.localeCompare(b.shokuNumber) < 0) {
        return -1
      } else if (a.shokuNumber.localeCompare(b.shokuNumber) > 0) {
        return 1
      } else {
        return 0
      }
    },
  },
  {
    title: t('label.other-occupation'),
    key: 'shokushuKnj3',
    width: '115px',
    align: 'left',
    sortable: true,
    sortRaw(a: Shokuin, b: Shokuin) {
      if (
        a.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE ||
        b.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE
      ) {
        return 0
      }
      if (a.shokuNumber.localeCompare(b.shokuNumber) < 0) {
        return -1
      } else if (a.shokuNumber.localeCompare(b.shokuNumber) > 0) {
        return 1
      } else {
        return 0
      }
    },
  },
  {
    title: t('label.other-occupation'),
    key: 'shokushuKnj4',
    width: '115px',
    align: 'left',
    sortable: true,
    sortRaw(a: Shokuin, b: Shokuin) {
      if (
        a.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE ||
        b.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE
      ) {
        return 0
      }
      if (a.shokuNumber.localeCompare(b.shokuNumber) < 0) {
        return -1
      } else if (a.shokuNumber.localeCompare(b.shokuNumber) > 0) {
        return 1
      } else {
        return 0
      }
    },
  },
  {
    title: t('label.other-occupation'),
    key: 'shokushuKnj5',
    width: '115px',
    align: 'left',
    sortable: true,
    sortRaw(a: Shokuin, b: Shokuin) {
      if (
        a.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE ||
        b.shokuNumber === Or26257Const.DEFAULT.MISETTEI_VALUE
      ) {
        return 0
      }
      if (a.shokuNumber.localeCompare(b.shokuNumber) < 0) {
        return -1
      } else if (a.shokuNumber.localeCompare(b.shokuNumber) > 0) {
        return 1
      } else {
        return 0
      }
    },
  },
]

// 未設定
const misetteiShokuin = {
  chkShokuId: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shokuNumber: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shokuin1Knj: t('label.misettei-staff-name'),
  shokuin2Knj: '',
  shokushuId: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shokushuId2: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shokushuId3: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shokushuId4: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shokushuId5: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shikakuId1: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shikakuId2: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shikakuId3: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shikakuId4: Or26257Const.DEFAULT.MISETTEI_VALUE,
  shikakuId5: Or26257Const.DEFAULT.MISETTEI_VALUE,
} as Shokuin

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or26257Const.DEFAULT.IS_OPEN,
})

const isLoading = ref(false)
const kinmuHyoujiFlg = ref(true)
const selectedItemIndex = ref(-1)
const staffList = ref([] as Shokuin[])
const relatedBeforStaffList = ref([] as Shokuin[])
const onlyStaffScheduledTodayEdit = ref(false)
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or26257StateType>({
  cpId: Or26257Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or26257Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0006Const.CP_ID(1)]: orX0006_1.value,
  [OrX0113Const.CP_ID(1)]: orX0113.value,
  [Or00094Const.CP_ID(1)]: or00094.value, // 五十音ヘッドライン
})

onMounted(async () => {
  local.staffList.push(misetteiShokuin)
  isLoading.value = true
  // 50音ボタンを初期化
  Or00094Logic.data.set({
    uniqueCpId: or00094.value.uniqueCpId,
    value: { selectValueArray: [Or26257Const.DEFAULT.ALL] },
  })
  // 職員検索初期処理
  const inputData: StaffSearchInitSelectInEntity = {
    /** システム略称 */
    sysCdKbn: localOneway.or26257.sysCdKbn,
    /** アカウント設定 */
    secAccountAllFlg: localOneway.or26257.secAccountAllFlg,
    /** 適用事業所ＩＤリスト */
    svJigyoIdList: localOneway.or26257.svJigyoIdList,
    /** 職員ID */
    shokuinId: localOneway.or26257.shokuinId,
    /** システムコード */
    gsysCd: localOneway.or26257.gsysCd,
    /** モード */
    selectMode: localOneway.or26257.selectMode,
    /** 基準日 */
    kijunYmd: localOneway.or26257.kijunYmd,
    /** 事業所ID */
    defSvJigyoId: localOneway.or26257.defSvJigyoId,
    /** フィルターフラグ */
    filterDwFlg: localOneway.or26257.filterDwFlg,
    /** 雇用状態 */
    koyouState: localOneway.or26257.koyouState,
    /** 地域フラグ */
    areaFlg: localOneway.or26257.areaFlg,
    /** 表示名称リスト */
    hyoujiColumnList: localOneway.or26257.hyoujiColumnList,
    /** 未設定フラグ */
    misetteiFlg: localOneway.or26257.misetteiFlg,
    /** 他職員参照権限 */
    otherRead: localOneway.or26257.otherRead,
    /** 中止フラグ */
    refStopFlg: localOneway.or26257.refStopFlg,
    /** 職員IDリスト */
    shokuinIdList: localOneway.or26257.shokuinIdList!,
    /** 処理フラグ */
    syoriFlg: '0',
  }

  // 職員検索初期処理API実行
  const ret: StaffSearchInitSelectOutEntity = await ScreenRepository.select(
    'staffSearchInitSelect',
    inputData
  )
  // 処理年月日
  local.shoriYmd.value = ret.data.shoriYmd
  // 職員明細
  local.staffList = [misetteiShokuin, ...ret.data.shokuinList]
  staffList.value = local.staffList
  relatedBeforStaffList.value = local.staffList
  localOneway.mo00615StaffCntOneway.itemLabel = t('label.number-of-people', [
    local.staffList.length,
  ])
  kinmuHyoujiFlg.value =
    ret.data.jyoukenn[0].kinmuHyoujiFlg !== Or26257Const.DEFAULT.KINMU_HYOUJI_FLG_0
  isLoading.value = false
})

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  selectedItemIndex.value = -1
  setState({ isOpen: false })
}

/**
 *確定ボタン処理
 */
function onClick_Confirm() {
  rtnDataSet()
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * 処理年月日値変更
 */
watch(
  () => local.shoriYmd.value,
  async (newValue, oldValue) => {
    if (!isEmpty(oldValue)) {
      local.staffNumber.mo00045.value = ''
      local.staffName.value = ''
      local.staffFurigana.mo00045.value = ''
      local.selectedOfficeItem = []
      local.selectedOccupationItem = []
      local.selectedLicenseItem = []
      await staffSearchDateChange()
    }
  }
)

/**
 * 本日の勤務予定者のみ値変更
 */
watch(
  () => local.onlyStaffScheduledToday,
  async (newValue) => {
    if (newValue.modelValue) {
      onlyStaffScheduledTodayEdit.value = true
    }
    await staffSearchRelated()
  }
)

/**
 * 項目選択画面の値変更
 */
watch(
  () => local.selectedItem,
  async (newValue) => {
    // 事業所選択
    if (localOneway.orX0113Oneway.itemCategory === Or26257Const.DEFAULT.ITEM_CATEGORY_OFFICE) {
      localOneway.mo00611OfficeOneway.btnLabel =
        newValue.selectedIdList.length > 0 ? t('btn.selected') : ''
      local.selectedOfficeItem = newValue.selectedIdList.length > 0 ? newValue.selectedIdList : []
    } else if (
      localOneway.orX0113Oneway.itemCategory === Or26257Const.DEFAULT.ITEM_CATEGORY_OCCUPATION
    ) {
      // 職種選択
      localOneway.mo00611OccupationOneway.btnLabel =
        newValue.selectedIdList.length > 0 ? t('btn.selected') : ''
      local.selectedOccupationItem =
        newValue.selectedIdList.length > 0 ? newValue.selectedIdList : []
    } else if (
      localOneway.orX0113Oneway.itemCategory === Or26257Const.DEFAULT.ITEM_CATEGORY_LICENSE
    ) {
      // 資格免許選択
      localOneway.mo00611LicenseOneway.btnLabel =
        newValue.selectedIdList.length > 0 ? t('btn.selected') : ''
      local.selectedLicenseItem = newValue.selectedIdList.length > 0 ? newValue.selectedIdList : []
    }
    // 職員検索絞り込み処理
    await staffSearchRelated()
  }
)

/**
 *戻り値設定
 */
function rtnDataSet() {
  // 戻り値設定
  const svJigyoIdList = [] as SvJigyo[]
  let shokuin = {} as Shokuin
  // 職員一覧選択行があるの場合
  if (selectedItemIndex.value >= 0) {
    shokuin = local.staffList[selectedItemIndex.value]
    // 事業所リストの件数が1の場合
    if (local.selectedOfficeItem.length === 1) {
      // 適用事業所ＩＤリスト 事業所リスト
      svJigyoIdList.push({ svJigyoId: local.selectedOfficeItem[0] })
    }
  } else {
    // 職員一覧選択行がなしの場合
    // システム略称が"SYS"の場合
    if (localOneway.or26257.sysCdKbn === Or26257Const.DEFAULT.SYSCD_KBN_SYS) {
      // 事業所リストの件数が1の場合
      if (local.selectedOfficeItem.length === 1) {
        // 適用事業所ＩＤリスト 事業所リスト
        svJigyoIdList.push({ svJigyoId: local.selectedOfficeItem[0] })
      }
    } else {
      // 上記処理以外の場合
      // 件数フラグが1の場合
      if (localOneway.or26257.kensuFlg === Or26257Const.DEFAULT.KENSU_FLG) {
        // 事業所リストの件数が1の場合
        if (local.selectedOfficeItem.length === 1) {
          // 適用事業所ＩＤリスト 事業所リスト
          svJigyoIdList.push({ svJigyoId: local.selectedOfficeItem[0] })
        }
      }
    }
  }

  local.or26257.shokuin = shokuin
  local.or26257.svJigyoIdList = svJigyoIdList
  // 選択情報値戻り
  emit('update:modelValue', local.or26257)
  // 画面閉じる
  close()
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * 五十音を変更した場合の処理
 */
watch(
  () => Or00094Logic.event.get(or00094.value.uniqueCpId),
  (newValue) => {
    if (newValue?.charBtnClickFlg) {
      // 50音ヘッドラインの文字ボタン押下イベントを検知
      const selectValueArray = Or00094Logic.data.get(or00094.value.uniqueCpId)?.selectValueArray
      if (selectValueArray === undefined || selectValueArray.length === 0) {
        return
      }
      // 親ボタン押下
      if (selectValueArray.length === 1) {
        const inputData = selectValueArray[0]
        if (inputData !== Or26257Const.DEFAULT.ALL) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter((item) => {
              return (
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.startsWith(convHiraganaToHalfKanaMap[inputData])
              )
            }),
          ]
        } else {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) => item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE
            ),
          ]
        }
      } else {
        // 子ボタン押下
        const inputData = selectValueArray[0]
        if (inputData === Or26257Const.DEFAULT.KANA_A) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_A) >= 0 &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_KA) < 0
            ),
          ]
        } else if (inputData === Or26257Const.DEFAULT.KANA_KA) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_KA) >= 0 &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_SA) < 0
            ),
          ]
        } else if (inputData === Or26257Const.DEFAULT.KANA_SA) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_SA) >= 0 &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_TA) < 0
            ),
          ]
        } else if (inputData === Or26257Const.DEFAULT.KANA_TA) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_TA) >= 0 &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_NA) < 0
            ),
          ]
        } else if (inputData === Or26257Const.DEFAULT.KANA_NA) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_NA) >= 0 &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_HA) < 0
            ),
          ]
        } else if (inputData === Or26257Const.DEFAULT.KANA_HA) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_HA) >= 0 &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_MA) < 0
            ),
          ]
        } else if (inputData === Or26257Const.DEFAULT.KANA_MA) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_MA) >= 0 &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_YA) < 0
            ),
          ]
        } else if (inputData === Or26257Const.DEFAULT.KANA_YA) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_YA) >= 0 &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_RA) < 0
            ),
          ]
        } else if (inputData === Or26257Const.DEFAULT.KANA_RA) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_RA) >= 0 &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_WA) < 0
            ),
          ]
        } else if (inputData === Or26257Const.DEFAULT.KANA_WA) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                [
                  Or26257Const.DEFAULT.HALF_KANA_WA,
                  Or26257Const.DEFAULT.HALF_KANA_WO,
                  Or26257Const.DEFAULT.HALF_KANA_EN,
                ].includes(item.shokuin1Kana.charAt(0)) &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_WA) < 0
            ),
          ]
        } else if (inputData === Or26257Const.DEFAULT.KANA_SONOTA) {
          local.staffList = [
            misetteiShokuin,
            ...staffList.value.filter(
              (item) =>
                item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_A) < 0 &&
                !item.shokuin1Kana.charAt(0).startsWith(Or26257Const.DEFAULT.HALF_KANA_WO) &&
                item.shokuin1Kana.charAt(0).localeCompare(Or26257Const.DEFAULT.HALF_KANA_EN) > 0
            ),
          ]
        }
      }
      localOneway.mo00615StaffCntOneway.itemLabel = t('label.number-of-people', [
        local.staffList.length,
      ])
    }
  }
)

/**
 *  項目選択ダイアログ表示
 *
 * @param itemCategory - 項目区分
 */
async function openItemSelectDialog(itemCategory: string) {
  // 事業所選択
  if (itemCategory === Or26257Const.DEFAULT.ITEM_CATEGORY_OFFICE) {
    // IDリストの一行目=0
    let svJigyoIdList: string[] = [Or26257Const.DEFAULT.MISETTEI_VALUE]
    // 職員検索事業所処理
    const retSvJigyoIdList = await staffSearchOffice()
    // 親画面.適用事業所ＩＤリストの件数>0の場合
    if (localOneway.or26257.svJigyoIdList.length > 0) {
      // 親画面.適用事業所ＩＤリストを変数.IDリストに追加する。
      localOneway.or26257.svJigyoIdList.concat(
        svJigyoIdList.map((item) => {
          return {
            svJigyoId: item,
          }
        })
      )
    } else {
      // Api取得した事業所ＩＤリストの件数>0の場合
      if (retSvJigyoIdList.length > 0) {
        // Api取得した事業所ＩＤリストをIDリストに追加する。
        svJigyoIdList = svJigyoIdList.concat(retSvJigyoIdList)
      }
    }
    local.selectedOfficeItem = svJigyoIdList
    localOneway.orX0113Oneway.selectedIdList = local.selectedOfficeItem
  } else if (itemCategory === Or26257Const.DEFAULT.ITEM_CATEGORY_OCCUPATION) {
    // 職種選択
    localOneway.orX0113Oneway.selectedIdList = local.selectedOccupationItem
  } else if (itemCategory === Or26257Const.DEFAULT.ITEM_CATEGORY_LICENSE) {
    // 資格免許選択
    localOneway.orX0113Oneway.selectedIdList = local.selectedLicenseItem
  }
  // 項目区分
  localOneway.orX0113Oneway.itemCategory = itemCategory
  // 項目選択ダイアログ表示
  OrX0113Logic.state.set({
    uniqueCpId: orX0113.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 職員検索処理年月日値変更
 */
async function staffSearchDateChange() {
  isLoading.value = true
  // 職員検索処理年月日値変更処理
  const inputData: StaffSearchDateChangeInEntity = {
    /** システム略称 */
    sysCdKbn: localOneway.or26257.sysCdKbn,
    /** アカウント設定 */
    secAccountAllFlg: localOneway.or26257.secAccountAllFlg,
    /** 適用事業所ＩＤリスト */
    svJigyoIdList: localOneway.or26257.svJigyoIdList,
    /** 職員ID */
    shokuinId: localOneway.or26257.shokuinId,
    /** システムコード */
    gsysCd: localOneway.or26257.gsysCd,
    /** モード */
    selectMode: localOneway.or26257.selectMode,
    /** 処理年月日 */
    shoriYmd: local.shoriYmd.value,
    /** 地域フラグ */
    areaFlg: localOneway.or26257.areaFlg,
    /** 表示名称リスト */
    hyoujiColumnList: localOneway.or26257.hyoujiColumnList,
    /** 未設定フラグ */
    misetteiFlg: localOneway.or26257.misetteiFlg,
    /** 職員IDリスト */
    shokuinIdList: localOneway.or26257.shokuinIdList!,
    /** 中止フラグ */
    refStopFlg: localOneway.or26257.refStopFlg,
    /** 処理フラグ */
    syoriFlg: '',
    /** 条件情報 */
    jyoukenn: [
      {
        /** 年齢表示フラグ */
        ageHyoujiFlg: Or26257Const.DEFAULT.AGE_HYOUJI_FLG_0,
        /** 雇用状態表示フラグ */
        koyouStateHyoujiFlg: Or26257Const.DEFAULT.KOYOU_STATE_HYOUJI_FLG_0,
        /** 年齢活性フラグ */
        ageKasseiFlg: Or26257Const.DEFAULT.AGE_KASSEI_FLG_0,
        /** 雇用状態活性フラグ */
        koyouStateKasseiFlg: Or26257Const.DEFAULT.KOYOU_STATE_KASSEI_FLG_0,
        /** 雇用状態 */
        koyouState: Or26257Const.DEFAULT.KOYOU_STATE_0,
        /** 本日の勤務予定者のみ */
        kinmuFlg: local.onlyStaffScheduledToday.modelValue
          ? Or26257Const.DEFAULT.KINMUFLG_1
          : Or26257Const.DEFAULT.KINMUFLG_0,
        /** 職員番号 */
        shokuNumber: '',
        /** 職員名 */
        shokuinKnj: '',
        /** 職員カナ */
        shokuinKana: '',
        /** 郵便番号 */
        zipHyouji: '',
        /** 住所 */
        addressKnj: '',
        /** 電話番号 */
        tel: '',
        /** 最小年齢 */
        ageMin: '',
        /** 最大年齢 */
        ageMax: '',
        /** 性別 */
        sex: '',
        /** 職種リスト */
        ilShokushuIdList: [],
        /** 資格免許リスト */
        ilShikakuIdlist: [],
        /** アカウントのある職員のみ */
        validFlg: '',
        /** 勤務表 */
        kinmuPatternId: '',
        /** 事業所リスト */
        ilSvJigyoIdList: [],
      },
    ],
  }

  // 職員検索処理年月日値変更API実行
  const ret: StaffSearchDateChangeOutEntity = await ScreenRepository.select(
    'staffSearchDateChangeSelect',
    inputData
  )
  // 職員明細
  local.staffList = [misetteiShokuin, ...ret.data.shokuinList]
  staffList.value = local.staffList
  relatedBeforStaffList.value = local.staffList
  // 人数(未設定含む)
  localOneway.mo00615StaffCntOneway.itemLabel = t('label.number-of-people', [
    local.staffList.length,
  ])
  isLoading.value = false
}

/**
 * 職員検索絞り込み処理
 */
async function staffSearchRelated() {
  if (local.selectedOfficeItem.length > 0 || onlyStaffScheduledTodayEdit.value) {
    isLoading.value = true
    // 職員検索絞り込み処理
    const inputData: StaffSearchRelatedInEntity = {
      /** システム略称 */
      sysCdKbn: localOneway.or26257.sysCdKbn,
      /** 条件情報 */
      jyoukenn: [
        {
          /** 本日の勤務予定者のみ */
          kinmuFlg: local.onlyStaffScheduledToday.modelValue
            ? Or26257Const.DEFAULT.KINMUFLG_1
            : Or26257Const.DEFAULT.KINMUFLG_0,
          /** 勤務表 */
          kinmuPatternId: '',
          /** 事業所リスト */
          ilSvJigyoIdList: local.selectedOfficeItem.map((item) => parseInt(item)),
        },
      ],
      /** 適用事業所ＩＤリスト */
      svJigyoIdList: localOneway.or26257.svJigyoIdList.map((item) => parseInt(item.svJigyoId)),
      /** 中止フラグ */
      refStopFlg: localOneway.or26257.refStopFlg,
    }

    // 職員検索絞り込み処理API実行
    const ret: StaffSearchRelatedOutEntity = await ScreenRepository.select(
      'staffSearchRelatedSelect',
      inputData
    )
    // 取得した職員IDリストの件数>0の場合
    if (ret.data.chkShokuIdList.length > 0) {
      const filterStaffList = local.staffList
        .filter((item) => item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE)
        .filter((item) => ret.data.chkShokuIdList.includes(parseInt(item.shokuNumber)))
      local.staffList = [misetteiShokuin, ...filterStaffList]
    } else {
      local.staffList = [misetteiShokuin]
    }
    onlyStaffScheduledTodayEdit.value = false
    isLoading.value = false
  } else {
    local.staffList = relatedBeforStaffList.value
  }

  const filterStaffList = local.staffList
    .filter((item) => item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE)
    .filter((item) => {
      // 職員番号がNULL以外の場合
      if (!isEmpty(local.staffNumber.mo00045.value)) {
        return item.shokuNumber === local.staffNumber.mo00045.value
      }
      return true
    })
    .filter((item) => {
      // 職員名がNULL以外の場合
      if (!isEmpty(local.staffName.value)) {
        return (item.shokuin1Knj + ' ' + item.shokuin2Knj).includes(local.staffName.value)
      }
      return true
    })
    .filter((item) => {
      // 職員カナがNULL以外の場合
      if (!isEmpty(local.staffFurigana.mo00045.value)) {
        return (item.shokuin1Kana + ' ' + item.shokuin2Kana).includes(
          local.staffFurigana.mo00045.value
        )
      }
      return true
    })
  local.staffList = [misetteiShokuin, ...filterStaffList]

  // 職種選択IDリストの件数>0
  if (local.selectedOccupationItem.length > 0) {
    const filterStaffList = local.staffList
      .filter((item) => item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE)
      .filter((item) => {
        // 職種選択IDリストの一行目が0の場合
        if (local.selectedOccupationItem[0] === Or26257Const.DEFAULT.MISETTEI_VALUE) {
          // 職種選択IDリストの件数>=2以外の場合
          if (local.selectedOccupationItem.length < 2) {
            return (
              item.shokushuId === Or26257Const.DEFAULT.MISETTEI_VALUE &&
              item.shokushuId2 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
              item.shokushuId3 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
              item.shokushuId4 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
              item.shokushuId5 === Or26257Const.DEFAULT.MISETTEI_VALUE
            )
          } else {
            // 職種選択IDリストの件数>=2の場合
            const selectedOccupationItem = local.selectedOccupationItem.filter(
              (occupationItem) => occupationItem !== Or26257Const.DEFAULT.MISETTEI_VALUE
            )
            return (
              (item.shokushuId === Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokushuId2 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokushuId3 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokushuId4 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shokushuId5 === Or26257Const.DEFAULT.MISETTEI_VALUE) ||
              selectedOccupationItem.includes(item.shokushuId) ||
              selectedOccupationItem.includes(item.shokushuId2) ||
              selectedOccupationItem.includes(item.shokushuId3) ||
              selectedOccupationItem.includes(item.shokushuId4) ||
              selectedOccupationItem.includes(item.shokushuId5)
            )
          }
        } else {
          // 職種選択IDリストの一行目が0以外の場合
          return (
            local.selectedOccupationItem.includes(item.shokushuId) ||
            local.selectedOccupationItem.includes(item.shokushuId2) ||
            local.selectedOccupationItem.includes(item.shokushuId3) ||
            local.selectedOccupationItem.includes(item.shokushuId4) ||
            local.selectedOccupationItem.includes(item.shokushuId5)
          )
        }
      })
    local.staffList = [misetteiShokuin, ...filterStaffList]
  }
  // 資格免許選択IDリストの件数>0
  if (local.selectedLicenseItem.length > 0) {
    const filterStaffList = local.staffList
      .filter((item) => item.chkShokuId !== Or26257Const.DEFAULT.MISETTEI_VALUE)
      .filter((item) => {
        // 資格免許選択IDリストの一行目が0の場合
        if (local.selectedLicenseItem[0] === Or26257Const.DEFAULT.MISETTEI_VALUE) {
          // 資格免許選択IDリストの件数>=2以外の場合
          if (local.selectedLicenseItem.length < 2) {
            return (
              item.shikakuId1 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
              item.shikakuId2 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
              item.shikakuId3 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
              item.shikakuId4 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
              item.shikakuId5 === Or26257Const.DEFAULT.MISETTEI_VALUE
            )
          } else {
            // 資格免許選択IDリストの件数>=2の場合
            const selectedLicenseItem = local.selectedLicenseItem.filter(
              (licenseItem) => licenseItem !== Or26257Const.DEFAULT.MISETTEI_VALUE
            )
            return (
              (item.shikakuId1 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shikakuId2 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shikakuId3 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shikakuId4 === Or26257Const.DEFAULT.MISETTEI_VALUE &&
                item.shikakuId5 === Or26257Const.DEFAULT.MISETTEI_VALUE) ||
              selectedLicenseItem.includes(item.shikakuId1) ||
              selectedLicenseItem.includes(item.shikakuId2) ||
              selectedLicenseItem.includes(item.shikakuId3) ||
              selectedLicenseItem.includes(item.shikakuId4) ||
              selectedLicenseItem.includes(item.shikakuId5)
            )
          }
        } else {
          // 資格免許選択IDリストの一行目が0以外の場合
          return (
            local.selectedLicenseItem.includes(item.shikakuId1) ||
            local.selectedLicenseItem.includes(item.shikakuId2) ||
            local.selectedLicenseItem.includes(item.shikakuId3) ||
            local.selectedLicenseItem.includes(item.shikakuId4) ||
            local.selectedLicenseItem.includes(item.shikakuId5)
          )
        }
      })
    local.staffList = [misetteiShokuin, ...filterStaffList]
  }
  staffList.value = local.staffList
  // 人数(未設定含む)
  localOneway.mo00615StaffCntOneway.itemLabel = t('label.number-of-people', [
    local.staffList.length,
  ])
}

/**
 * 職員検索事業所処理
 */
async function staffSearchOffice() {
  // 職員検索事業所処理
  const inputData: StaffSearchOfficeInEntity = {
    /** 適用事業所ＩＤリスト */
    svJigyoIdList: localOneway.or26257.svJigyoIdList.map((item) => parseInt(item.svJigyoId)),
    /** 職員ID */
    shokuinId: localOneway.or26257.shokuinId,
    /** システムコード */
    gsysCd: localOneway.or26257.gsysCd,
    /** メニュー１ID */
    menu1Id: localOneway.or26257.menu1Id,
  }

  // 職員検索事業所処理API実行
  const ret: StaffSearchOfficeOutEntity = await ScreenRepository.select(
    'staffSearchOfficeSelect',
    inputData
  )
  return ret.data.svJigyoIdList.map((item) => item.toString())
}

// 項目選択ダイアログ表示フラグ
const showDialogOrX0113 = computed(() => {
  // OrX0113のダイアログ開閉状態
  return OrX0113Logic.state.get(orX0113.value.uniqueCpId)?.isOpen ?? false
})
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <!-- ローディング -->
    <v-overlay
      :model-value="isLoading"
      :persistent="true"
      class="align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      />
    </v-overlay>
    <template #cardItem>
      <c-v-row
        v-if="localOneway.or26257.selectMode === Or26257Const.DEFAULT.SELECT_MODE_12"
        no-gutters
      >
        <c-v-col cols="auto">
          <!-- 処理年月日 -->
          <base-mo00020
            v-model="local.shoriYmd"
            :oneway-model-value="localOneway.mo00020Oneway"
          />
        </c-v-col>
      </c-v-row>
      <c-v-row
        v-if="localOneway.or26257.selectMode === Or26257Const.DEFAULT.SELECT_MODE_12"
        no-gutters
      >
        <c-v-col cols="auto">
          <!-- 職員番号 -->
          <base-mo00038
            v-model="local.staffNumber"
            :oneway-model-value="localOneway.m00038StaffNumberOneway"
            @change="staffSearchRelated"
          />
        </c-v-col>
        <c-v-col
          cols="auto"
          style="padding-left: 0 !important"
        >
          <!-- 職員名 -->
          <base-mo00045
            v-model="local.staffName"
            :oneway-model-value="localOneway.mo00045StaffNameOneway"
            @change="staffSearchRelated"
          />
        </c-v-col>
        <c-v-col
          cols="auto"
          style="padding-left: 0 !important"
        >
          <!-- 職員ｶﾅ -->
          <base-mo00030
            v-model="local.staffFurigana"
            :oneway-model-value="localOneway.mo00030StaffFuriganaOneway"
            @update:model-value="staffSearchRelated"
          />
        </c-v-col>
      </c-v-row>
      <c-v-row
        v-if="localOneway.or26257.selectMode === Or26257Const.DEFAULT.SELECT_MODE_12"
        no-gutters
      >
        <c-v-col
          cols="auto"
          class="ml-2 mt-2"
        >
          <!-- 事業所 -->
          <c-v-row
            no-gutters
            class="text-center"
          >
            <c-v-col cols="auto">
              <base-mo00615 :oneway-model-value="localOneway.mo00615OfficeOneway" />
            </c-v-col>
            <c-v-col
              cols="auto"
              align-self="center"
            >
              <base-mo00611
                :oneway-model-value="localOneway.mo00611OfficeOneway"
                @click="openItemSelectDialog(Or26257Const.DEFAULT.ITEM_CATEGORY_OFFICE)"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="auto"
          class="mt-2"
        >
          <!--職種  -->
          <c-v-row
            no-gutters
            class="text-center"
          >
            <c-v-col cols="auto">
              <base-mo00615 :oneway-model-value="localOneway.mo00615OccupationOneway" />
            </c-v-col>
            <c-v-col
              cols="auto"
              align-self="center"
            >
              <base-mo00611
                :oneway-model-value="localOneway.mo00611OccupationOneway"
                @click="openItemSelectDialog(Or26257Const.DEFAULT.ITEM_CATEGORY_OCCUPATION)"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="auto"
          class="mt-2"
        >
          <!--資格免許  -->
          <c-v-row
            no-gutters
            class="text-center"
          >
            <c-v-col cols="auto">
              <base-mo00615 :oneway-model-value="localOneway.mo00615LicenseOneway" />
            </c-v-col>
            <c-v-col
              cols="auto"
              align-self="center"
            >
              <base-mo00611
                :oneway-model-value="localOneway.mo00611LicenseOneway"
                @click="openItemSelectDialog(Or26257Const.DEFAULT.ITEM_CATEGORY_LICENSE)"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="auto"
          style="padding-bottom: 0 !important"
        >
          <!-- 本日の勤務予定者のみ -->
          <base-mo00018
            v-if="kinmuHyoujiFlg"
            v-model="local.onlyStaffScheduledToday"
            :oneway-model-value="localOneway.mo00018Oneway"
            style="margin-top: -4px"
          />
        </c-v-col>
        <!-- 項目選択画面 -->
        <g-custom-or-X0113
          v-if="showDialogOrX0113"
          v-bind="orX0113"
          v-model="local.selectedItem"
          :oneway-model-value="localOneway.orX0113Oneway"
        />
      </c-v-row>
      <c-v-row
        v-if="localOneway.or26257.selectMode === Or26257Const.DEFAULT.SELECT_MODE_12"
        no-gutters
        class="mt-2"
      >
        <c-v-col
          cols="12"
          class="d-flex"
          style="max-height: 465px; min-height: 465px"
        >
          <div
            style="width: 31px"
            class="onn50"
          >
            <!-- 五十音ヘッドライン -->
            <g-base-or-00094 v-bind="or00094" />
          </div>
          <!-- 職員明細一覧 -->
          <c-v-data-table
            class="table-header w-100 h-100 table-wrapper"
            style="max-height: 422px; min-height: 422px"
            hide-default-footer
            hide-no-data
            fixed-header
            hover
            :items-per-page="-1"
            :headers="staffHeader"
            :items="local.staffList"
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="selectRow(index)"
                @dblclick="onClick_Confirm"
              >
                <td class="pa-2 text-right">
                  {{ item.chkShokuId !== '0' ? item.shokuNumber : '' }}
                </td>
                <td class="pa-2">{{ item.shokuin1Knj + ' ' + item.shokuin2Knj }}</td>
                <td class="pa-2">{{ item.shokushuKnj1 }}</td>
                <td class="pa-2">{{ item.shokushuKnj2 }}</td>
                <td class="pa-2">{{ item.shokushuKnj3 }}</td>
                <td class="pa-2">{{ item.shokushuKnj4 }}</td>
                <td class="pa-2">{{ item.shokushuKnj5 }}</td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="mt-2"
        style="padding-left: 155px"
      >
        <c-v-col>
          <base-mo00615 :oneway-model-value="localOneway.mo00615StaffCntOneway" />
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          style="margin-right: 0 !important"
          @click="onClick_Confirm"
        >
          <!--ツールチップ表示："設定を確定します。"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';

:deep(.v-row.v-row--no-gutters > .v-col, .v-row.v-row--no-gutters > [class*='v-col-']) {
  padding: 0px !important;
}
.v-card-actions .v-row {
  margin-right: 0 !important;
}

.onn50 :deep(.parent-container) {
  height: 38px;
}

.onn50 :deep(.parent-btn) {
  height: 38px;
}

.onn50 :deep(.parent-container:first-child .parent-btn) {
  border-right: none !important;
}
</style>
