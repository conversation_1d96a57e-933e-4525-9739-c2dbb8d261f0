<script setup lang="ts">
/**
 * OrX0143:有機体:履歴一覧情報
 * 履歴一覧情報
 *
 * @description
 * 履歴一覧情報画面では、内容を選択し、呼び出し元の画面に反映します。
 *
 * <AUTHOR>
 */
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { groupBy } from 'lodash'
import { OrX0143Const } from './OrX0143.constants'
import type { OrX0143TableData, SortConfig } from './OrX0143.type'
import type {
  OrX0143EventType,
  OrX0143OnewayType,
} from '~/types/cmn/business/components/OrX0143Type'
import { useScreenEventStatus, useSetupChildProps } from '~/composables/useComponentVue'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0143OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: OrX0143OnewayType = {
  // 期間管理フラグ
  kikanFlg: '',
  // 単一複数フラグ(0:単一,1:複数)
  singleFlg: '',
  // 表形式スタイル設定
  tableStyle: 'width:520px',
  // 期間履歴タイトルリスト
  kikanRirekiTitleList: [],
  // 期間履歴情報リスト
  rirekiList: [],
}

const localOneway = reactive({
  orX0143: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
})

// テーブルヘッダ
const headers = computed(() => {
  const headersList: { title: string; key: string; style: string; sortable: boolean }[] = []
  if (localOneway.orX0143.kikanRirekiTitleList?.length > 0) {
    localOneway.orX0143.kikanRirekiTitleList.forEach((item) => {
      headersList.push({
        // タイトル
        title: t(`label.${item.title}`),
        // key
        key: item.key,
        // 列幅
        style: `width:${item.width}px;min-width:${item.width}px`,
        sortable: false,
      })
    })
  }
  return headersList
})

const multiHeaders = computed(() => {
  return [{ title: '', key: 'sel', sortable: false }].concat(headers.value)
})
/**************************************************
 * Pinia
 **************************************************/
const { setEvent } = useScreenEventStatus<OrX0143EventType>({
  cpId: OrX0143Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
useSetupChildProps(props.uniqueCpId, {})

/**************************************************
 * 変数定義
 **************************************************/
const checkBoxLabel = ref<string>('')
const historyData = ref<OrX0143TableData[]>([])
const selectAll = ref<boolean>(false)
const selectedRows = ref<boolean[]>([])

// 単一モード（singleFlg='0'）と複数モード（singleFlg='1'）の選択状態を分けて保存
const taniModeSelectedRows = ref<boolean[]>([])
const hukusuuModeSelectedRows = ref<boolean[]>([])
const taniModeSelectAll = ref<boolean>(false)
const hukusuuModeSelectAll = ref<boolean>(false)

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * デフォルト選択を設定
 */
function setDefaultSelection() {
  const dataLength = historyData.value.length
  if (dataLength === 0) return

  let defaultIndex = -1

  // rirekiIdが指定されている場合、該当する行を探す
  if (localOneway.orX0143.rirekiId) {
    defaultIndex = historyData.value.findIndex((x) => x.rirekiId === localOneway.orX0143.rirekiId)
  }

  // rirekiIdに該当する行が見つからない場合、デフォルト行を選択
  if (defaultIndex === -1) {
    if (localOneway.orX0143.kikanFlg === OrX0143Const.KIKAN_FLG_ON) {
      // kikanFlgが'1'の場合、2行目（インデックス1）を選択
      defaultIndex = 1
    } else {
      // kikanFlgが'1'以外の場合、1行目（インデックス0）を選択
      defaultIndex = 0
    }
  }

  // 選択可能な範囲内であれば選択
  if (defaultIndex >= 0 && defaultIndex < dataLength) {
    // planPeriodがある行（グループヘッダー）は選択しない
    if (!historyData.value[defaultIndex].planPeriod) {
      taniModeSelectedRows.value[defaultIndex] = true
    } else {
      // グループヘッダーの場合、次の有効な行を探す
      for (let i = defaultIndex + 1; i < dataLength; i++) {
        if (!historyData.value[i].planPeriod) {
          taniModeSelectedRows.value[i] = true
          break
        }
      }
    }
  }
}

/**
 * 現在のモードに応じて選択状態を復元
 */
function restoreSelectionForCurrentMode() {
  if (localOneway.orX0143.singleFlg === OrX0143Const.DEFAULT.TANI) {
    // 単一モード
    selectedRows.value = [...taniModeSelectedRows.value]
    selectAll.value = taniModeSelectAll.value
  } else {
    // 複数モード
    selectedRows.value = [...hukusuuModeSelectedRows.value]
    selectAll.value = hukusuuModeSelectAll.value
  }
}

/**
 * 現在のモードの選択状態を保存
 */
function saveSelectionForCurrentMode() {
  if (localOneway.orX0143.singleFlg === OrX0143Const.DEFAULT.TANI) {
    // 単一モード
    taniModeSelectedRows.value = [...selectedRows.value]
    taniModeSelectAll.value = selectAll.value
  } else {
    // 複数モード
    hukusuuModeSelectedRows.value = [...selectedRows.value]
    hukusuuModeSelectAll.value = selectAll.value
  }
}

/**
 * 明細行クリックイベント
 *
 * @param index - インデックス
 */
const handleRowClick = (index: number) => {
  if (!historyData.value[index].planPeriod) {
    // 複数モード
    if (localOneway.orX0143.singleFlg === OrX0143Const.DEFAULT.HUKUSUU) {
      selectedRows.value[index] = !selectedRows.value[index]
      for (let i = 0; i < selectedRows.value.length; i++) {
        if (historyData.value[i].planPeriod === '' && !selectedRows.value[i]) {
          selectAll.value = false
          saveSelectionForCurrentMode()
          onClickEvent()
          return
        }
      }
      selectAll.value = true
    }
    // 単一モード
    else {
      selectedRows.value = new Array<boolean>(historyData.value.length).fill(false)
      selectedRows.value[index] = true
    }

    // 現在のモードの選択状態を保存
    saveSelectionForCurrentMode()
    // 選択押下
    onClickEvent()
  }
}

/**
 * 履歴一覧の「選択」クリック
 *
 * @param index - インデックス
 */
const checkedChange = (index: number) => {
  if (!historyData.value[index].planPeriod) {
    // 複数モード
    if (localOneway.orX0143.singleFlg === OrX0143Const.DEFAULT.HUKUSUU) {
      selectedRows.value[index] = !selectedRows.value[index]
      for (let i = 0; i < selectedRows.value.length; i++) {
        if (!historyData.value[i].planPeriod && !selectedRows.value[i]) {
          selectAll.value = false
          saveSelectionForCurrentMode()
          return
        }
      }
      selectAll.value = true
    }
    // 単一モード
    else {
      selectedRows.value = new Array<boolean>(historyData.value.length).fill(false)
      selectedRows.value[index] = true
    }

    // 現在のモードの選択状態を保存
    saveSelectionForCurrentMode()
  }
}

/**
 * アセスメント履歴一覧データ
 *
 */
const historyEdit = () => {
  let sortedData: OrX0143TableData[] = []
  if (localOneway.orX0143.kikanFlg === '1') {
    sortedData = dynamicSort(localOneway.orX0143.rirekiList, [
      { field: OrX0143Const.CELL_START_YMD, order: OrX0143Const.DEFAULT.DESC },
      { field: OrX0143Const.CELL_END_YMD, order: OrX0143Const.DEFAULT.DESC },
      { field: OrX0143Const.CELL_END_YMD, order: OrX0143Const.DEFAULT.DESC },
      { field: OrX0143Const.CELL_CREATE_YMD, order: OrX0143Const.DEFAULT.DESC },
      { field: OrX0143Const.CELL_RIREKI_ID, order: OrX0143Const.DEFAULT.DESC },
    ])
    historyData.value = addSummaryAboveGroup(sortedData)
  } else {
    historyData.value = dynamicSort(localOneway.orX0143.rirekiList, [
      { field: OrX0143Const.CELL_CREATE_YMD, order: OrX0143Const.DEFAULT.DESC },
      { field: OrX0143Const.CELL_RIREKI_ID, order: OrX0143Const.DEFAULT.DESC },
    ])
  }

  // データが更新された場合、選択状態配列のサイズを調整
  const newDataLength = historyData.value.length

  // 既存の選択状態を保持しながらサイズを調整
  if (taniModeSelectedRows.value.length !== newDataLength) {
    const oldTaniSelection = [...taniModeSelectedRows.value]
    taniModeSelectedRows.value = new Array<boolean>(newDataLength).fill(false)

    // 既存の選択状態を可能な限り復元
    for (let i = 0; i < Math.min(oldTaniSelection.length, newDataLength); i++) {
      taniModeSelectedRows.value[i] = oldTaniSelection[i]
    }

    // 初回または選択がない場合はデフォルト選択を設定
    if (oldTaniSelection.length === 0 || !taniModeSelectedRows.value.some((selected) => selected)) {
      setDefaultSelection()
    }
  }

  if (hukusuuModeSelectedRows.value.length !== newDataLength) {
    const oldHukusuuSelection = [...hukusuuModeSelectedRows.value]
    hukusuuModeSelectedRows.value = new Array<boolean>(newDataLength).fill(false)
    // 既存の選択状態を可能な限り復元
    for (let i = 0; i < Math.min(oldHukusuuSelection.length, newDataLength); i++) {
      hukusuuModeSelectedRows.value[i] = oldHukusuuSelection[i]
    }
  }

  // 現在のモードの選択状態を復元
  restoreSelectionForCurrentMode()
}

/**
 * データのグループ化
 *
 * @param data - 元のデータ
 */
function addSummaryAboveGroup(data: OrX0143TableData[]): OrX0143TableData[] {
  // sc1Idでグループ化
  const grouped = groupBy(data, OrX0143Const.CELL_SC1ID)

  // 結果配列の生成
  const result: OrX0143TableData[] = []

  Object.entries(grouped).forEach(([sc1Id, items]) => {
    result.push({
      sc1Id: sc1Id,
      startYmd: '',
      endYmd: '',
      createYmd: '',
      rirekiId: '',
      planPeriod:
        t('label.plan-period') +
        t('label.colon-mark') +
        items[0].startYmd +
        t('label.wavy') +
        items[0].endYmd,
      isSummaryRow: true,
    })

    // 元のデータを追加
    result.push(...items)
  })

  return result
}

/**
 * データを必要に応じてソート
 *
 * @param data - 元のデータ
 *
 * @param configs - ソート情報
 */
function dynamicSort<T>(data: T[], configs: SortConfig<T>[]): T[] {
  return [...data].sort((a, b) => {
    for (const { field, order } of configs) {
      // 比較値を取得（null値の可能性を処理）
      const aValue = a[field] ?? ''
      const bValue = b[field] ?? ''

      // 文字列の場合は直接比較し、数値または日付の場合は特別な処理が必要です
      let compareResult: number
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        compareResult = aValue.localeCompare(bValue)
      } else {
        compareResult = aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      }

      // 現在のフィールドの比較結果が等しくない場合、直ちに返す
      if (compareResult !== 0) {
        return order === OrX0143Const.DEFAULT.ASC ? compareResult : -compareResult
      }
    }
    // すべてのフィールドが等しい
    return 0
  })
}

/**
 * すべて選択
 */
const toggleAllRows = () => {
  const allSelected = selectAll.value
  selectedRows.value = selectedRows.value.map(() => allSelected)

  // 現在のモードの選択状態を保存
  saveSelectionForCurrentMode()
  onClickEvent()
}

/**
 *  選択押下時の処理
 */
function onClickEvent() {
  setEvent({
    historyDetClickFlg: true,
    orX0143DetList: historyData.value.filter((_, index) => selectedRows.value[index]),
  })
}

/**
 * 履歴選択 - 値の監視
 */
watch(
  () => localOneway.orX0143.singleFlg,
  (newValue, oldValue) => {
    // 単一と複数の切り替え時に、選択結果が保持
    if (newValue && oldValue !== undefined) {
      // 切り替え前のモードの選択状態を保存
      saveSelectionForCurrentMode()

      // 配列サイズを調整（データが変更された場合に備えて）
      const dataLength = historyData.value.length
      if (taniModeSelectedRows.value.length !== dataLength) {
        const oldTaniSelection = [...taniModeSelectedRows.value]
        taniModeSelectedRows.value = new Array<boolean>(dataLength).fill(false)
        // 既存の選択状態を可能な限り復元
        for (let i = 0; i < Math.min(oldTaniSelection.length, dataLength); i++) {
          taniModeSelectedRows.value[i] = oldTaniSelection[i]
        }
      }

      if (hukusuuModeSelectedRows.value.length !== dataLength) {
        const oldHukusuuSelection = [...hukusuuModeSelectedRows.value]
        hukusuuModeSelectedRows.value = new Array<boolean>(dataLength).fill(false)
        // 既存の選択状態を可能な限り復元
        for (let i = 0; i < Math.min(oldHukusuuSelection.length, dataLength); i++) {
          hukusuuModeSelectedRows.value[i] = oldHukusuuSelection[i]
        }
      }

      // 新しいモードの選択状態を復元
      restoreSelectionForCurrentMode()
    }
  }
)

/**
 * 履歴ID値の監視
 */
watch(
  () => localOneway.orX0143.rirekiId,
  (newValue) => {
    if (newValue) {
      const index = historyData.value.findIndex((x) => x.rirekiId === localOneway.orX0143.rirekiId)
      if (index > -1) {
        selectedRows.value = new Array<boolean>(historyData.value.length).fill(false)
        selectedRows.value[index] = true
        selectAll.value = false

        // 現在のモードの選択状態を保存
        saveSelectionForCurrentMode()
        onClickEvent()
      }
    }
  }
)

/**
 * アセスメント履歴一覧データ
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.orX0143 = {
      ...defaultOnewayModelValue,
      ...newValue,
    }
    // アセスメント履歴一覧データ
    historyEdit()

    // nextTickを使用してhistoryEditの処理完了を待つ
    void nextTick(() => {
      onClickEvent()
    })
  },
  { deep: true }
)
</script>

<template>
  <c-v-row>
    <c-v-col class="d-flex">
      <!-- 初期表示(期間管理フラグが「管理する」の場合) -->
      <div v-if="OrX0143Const.DEFAULT.TANI === localOneway.orX0143.singleFlg">
        <c-v-form ref="tableForm">
          <c-v-data-table
            :headers="headers"
            class="table-wrapper"
            fixed-header
            :items="historyData"
            height="483px"
            hide-default-footer
            :style="localOneway.orX0143.tableStyle"
            :items-per-page="-1"
          >
            <!-- ヘッダ -->
            <template #headers>
              <tr>
                <th
                  v-for="header in headers"
                  :key="header.key"
                  :style="header.style"
                >
                  {{ header.title }}
                </th>
              </tr>
            </template>
            <!-- 一覧 -->
            <template #item="{ item, index }">
              <tr
                :class="{
                  'header-row': item.planPeriod && item.planPeriod !== '',
                  'selected-row': selectedRows[index] && !item.planPeriod,
                }"
                @click="handleRowClick(index)"
              >
                <td
                  v-if="item.planPeriod && item.planPeriod !== ''"
                  :colspan="headers.length"
                >
                  {{ item.planPeriod }}
                </td>
                <!-- 初期表示(期間管理フラグが「管理しない」の場合) -->
                <template v-else>
                  <td
                    v-for="header in headers"
                    :key="header.key"
                  >
                    {{ item[header.key] }}
                  </td>
                </template>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-form>
      </div>
      <!-- 履歴選択方法が「複数」の場合 -->
      <div v-if="OrX0143Const.DEFAULT.HUKUSUU === localOneway.orX0143.singleFlg">
        <c-v-data-table
          :headers="multiHeaders"
          class="table-wrapper"
          hide-default-footer
          :items="historyData"
          height="483px"
          fixed-header
          hover
          :style="localOneway.orX0143.tableStyle"
          :items-per-page="-1"
        >
          <!-- ヘッダ -->
          <template #headers>
            <tr>
              <th class="table-checkbox width-60">
                <base-at-checkbox
                  v-model="selectAll"
                  :indeterminate="
                    historyData.length > 0 && selectedRows.includes(true) && !selectAll
                  "
                  :checkbox-label="checkBoxLabel"
                  :disabled="historyData.length === 0"
                  @change="toggleAllRows"
                ></base-at-checkbox>
              </th>
              <th
                v-for="header in headers"
                :key="header.key"
                :style="header.style"
              >
                {{ header.title }}
              </th>
            </tr>
          </template>
          <template #item="{ item, index }">
            <tr
              :class="{
                'header-row': item.planPeriod && item.planPeriod !== '',
                'selected-row': selectedRows[index] && !item.planPeriod,
              }"
              @click="handleRowClick(index)"
            >
              <td
                v-if="!item.planPeriod"
                class="table-checkbox"
              >
                <base-at-checkbox
                  v-model="selectedRows[index]"
                  :checkbox-label="checkBoxLabel"
                  @change="checkedChange(index)"
                ></base-at-checkbox>
              </td>
              <td
                v-if="item.planPeriod && item.planPeriod !== ''"
                :colspan="multiHeaders.length"
              >
                {{ item.planPeriod }}
              </td>
              <!-- 履歴選択方法が「複数」且つ 期間管理フラグが「管理しない」の場合 -->
              <template v-else>
                <td
                  v-for="header in headers"
                  :key="header.key"
                >
                  {{ item[header.key] }}
                </td>
              </template>
            </tr>
          </template>
        </c-v-data-table>
      </div>
    </c-v-col>
  </c-v-row>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.table-wrapper .v-table__wrapper td {
  padding: 0;
}
:deep(.table-header td) {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-color: lightgrey;
  max-height: 40px;
  font-size: 14px;
}
:deep(.table-wrapper .v-table__wrapper table) {
  table-layout: fixed;
}
:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: 0px;
  z-index: 2;
}

.header-row {
  cursor: default;
  background-color: rgb(var(--v-theme-blue-100));
}
// 選択した行のCSS
.selected-row {
  background-color: rgb(var(--v-theme-blue-100));
}
:deep(.table-checkbox > div) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
}
// 行の高さ: 32px
:deep(.table-wrapper table tbody tr td),
:deep(.table-wrapper .row-height) {
  height: 32px !important;
}
// 最小幅: 60px
.width-60 {
  min-width: 60px;
  width: 60px;
}
</style>
