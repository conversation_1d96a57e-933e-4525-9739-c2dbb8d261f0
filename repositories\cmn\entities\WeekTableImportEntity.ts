import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/** 対象期間入力エンティティ */
export interface IPlanPeriodInEntity extends InWebEntity {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 期間管理フラグ
   */
  kikanFlg: string
}

/**
 * 対象期間出力エンティティ
 */
export interface IPlanPeriodOutEntity extends OutWebEntity {
  /** data */
  data: {
    // 期間リスト
    planPeriodInfo: {
      /** 開始日 */
      startYmd: string
      /** 終了日 */
      endYmd: string
      /** 期間ID */
      sc1Id: string
      /** 期間内履歴数 */
      rirekiCnt: string
    }[]
    // 履歴リスト
    historyInfo: {
      /** 週間表ID */
      week1Id: string
      /** 期間ID */
      sc1Id: string
      /** 作成日 */
      createYmd: string
      /** 作成者 */
      shokuKnj: string
      /** ケースNo. */
      caseNo: string
      /** 当該年月 */
      tougaiYm: string
      /** 有効期間 */
      termName: string
      /** 有効期間ID */
      termId: string
      /** 改訂漢字 */
      kaiteiKnj: string
      /** 改訂フラグ */
      kaiteiFlg: string
    }[]
    // 詳細リスト
    weekTableDetailInfo: {
      /**
       * 詳細ID
       */
      week2Id: string
      /**
       * 週間表ID
       */
      week1Id: string
      /**
       * 利用者ＩＤ
       */
      userid: string
      /**
       * 曜日
       */
      youbi: string
      /**
       * 開始時間
       */
      kaishiJikan: string
      /**
       * 終了時間
       */
      shuuryouJikan: string
      /**
       * 内容CD
       */
      naiyoCd: string
      /**
       * 内容
       */
      naiyoKnj: string
      /**
       * メモ
       */
      memoKnj: string
      /**
       * 文字位置
       */
      alignment: string
      /**
       * 文字カラー
       */
      fontColor: string
      /**
       * 背景カラー
       */
      backColor: string
      /**
       * 時間表示区分
       */
      timeKbn: string
      /**
       * 週単位以外文字
       */
      igaiMoji: string
      /**
       * 週単位以外のｻｰﾋﾞｽ区分
       */
      igaiKbn: string
      /**
       * 週単位以外のｻｰﾋﾞｽ（日付指定）
       */
      igaiDate: string
      /**
       * 週単位以外のｻｰﾋﾞｽ（曜日指定）
       */
      igaiWeek: string
      /**
       * 取込先
       */
      dataKnj: string
    }[]
  }
}

/** 履歴情報入力エンティティ */
export interface IHistoryInfoInEntity extends InWebEntity {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 期間管理フラグ
   */
  kikanFlg: string
  /**
   * 計画対象期間ID
   */
  sc1Id: string
}

/**
 * 履歴情報出力エンティティ
 */
export interface IHistoryInfoOutEntity extends OutWebEntity {
  /** data */
  data: {
    historyInfo: {
      /** 計画表ID */
      week1Id: string
      /** 計画期間ID */
      sc1Id: string
      /** 作成日 */
      createYmd: string
      /** 作成者 */
      shokuKnj: string
      /** ケースNo. */
      caseNo: string
      /** 処理年月 */
      tougaiYm: string
      /** 有効期間 */
      termName: string
      /** 有効期間ID */
      termId: string
      /** 改訂 */
      kaiteiKnj: string
      /** 改訂フラグ */
      kaiteiFlg: string
    }[]
    weekTableDetailInfo: {
      /**
       * 詳細ID
       */
      week2Id: string
      /**
       * 週間表ID
       */
      week1Id: string
      /**
       * 利用者ＩＤ
       */
      userid: string
      /**
       * 曜日
       */
      youbi: string
      /**
       * 開始時間
       */
      kaishiJikan: string
      /**
       * 終了時間
       */
      shuuryouJikan: string
      /**
       * 内容CD
       */
      naiyoCd: string
      /**
       * 内容
       */
      naiyoKnj: string
      /**
       * メモ
       */
      memoKnj: string
      /**
       * 文字位置
       */
      alignment: string
      /**
       * 文字カラー
       */
      fontColor: string
      /**
       * 背景カラー
       */
      backColor: string
      /**
       * 時間表示区分
       */
      timeKbn: string
      /**
       * 週単位以外文字
       */
      igaiMoji: string
      /**
       * 週単位以外のｻｰﾋﾞｽ区分
       */
      igaiKbn: string
      /**
       * 週単位以外のｻｰﾋﾞｽ（日付指定）
       */
      igaiDate: string
      /**
       * 週単位以外のｻｰﾋﾞｽ（曜日指定）
       */
      igaiWeek: string
      /**
       * 取込先
       */
      dataKnj: string
    }[]
  }
}
/** 日課表明細入力エンティティ */
export interface IWeekTableDetailInEntity extends InWebEntity {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 期間管理フラグ
   */
  kikanFlg: string
  /**
   * 計画表ID
   */
  week1Id: string
}

/**
 * 日課表明細出力エンティティ
 */
export interface IWeekTableDetailOutEntity extends OutWebEntity {
  /** data */
  data: {
    weekTableDetailInfo: {
      /**
       * 詳細ID
       */
      week2Id: string
      /**
       * 週間表ID
       */
      week1Id: string
      /**
       * 利用者ＩＤ
       */
      userid: string
      /**
       * 曜日
       */
      youbi: string
      /**
       * 開始時間
       */
      kaishiJikan: string
      /**
       * 終了時間
       */
      shuuryouJikan: string
      /**
       * 内容CD
       */
      naiyoCd: string
      /**
       * 内容
       */
      naiyoKnj: string
      /**
       * メモ
       */
      memoKnj: string
      /**
       * 文字位置
       */
      alignment: string
      /**
       * 文字カラー
       */
      fontColor: string
      /**
       * 背景カラー
       */
      backColor: string
      /**
       * 時間表示区分
       */
      timeKbn: string
      /**
       * 週単位以外文字
       */
      igaiMoji: string
      /**
       * 週単位以外のｻｰﾋﾞｽ区分
       */
      igaiKbn: string
      /**
       * 週単位以外のｻｰﾋﾞｽ（日付指定）
       */
      igaiDate: string
      /**
       * 週単位以外のｻｰﾋﾞｽ（曜日指定）
       */
      igaiWeek: string
      /**
       * 取込先
       */
      dataKnj: string
    }[]
  }
}
