/**
 * Or28483:（利用者基本情報データ②－1日のタイムテーブル）1日のスケジュール一表
 * GUI01068_介護予防に関する事項
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> PHAM TIEN THANH
 */
import type { Mo01280Type, Mo01280OnewayType } from '~/types/business/components/Mo01280Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'

/**
 * 親画面から GUI01072: 1日のスケジュールモーダル 開閉
 */
export interface Or28483StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 *  table Data
 */
export interface TableData {
  /**
   * 日のスケジュールモーダル
   */
  dailyScheduleList: dailyScheduleItem[]
}

/**
 * dailyScheduleItem
 */
export interface dailyScheduleItem {
  /**
   * カウンター
   */
  issuingId?: number | null
  /**
   * 表示順
   */
  seq?: number | null
  /**
   * チェックボックス
   */
  checkbox?: {
    /**
     * チェックボックス
     */
    modelValue: Mo00018Type
    /**
     * チェックボックス
     */
    onewayModelValue: Mo00018OnewayType
  }
  /**
   * 時間
   */
  jikanKnj: {
    /**
     * 時間
     */
    modelValue: Mo01280Type
    /**
     * 時間
     */
    onewayModelValue: Mo01280OnewayType
  }
  /**
   * 本人
   */
  honinKnj: {
    /**
     * 本人
     */
    modelValue: Mo01280Type
    /**
     * 本人
     */
    onewayModelValue: Mo01280OnewayType
  }
  /**
   * 介護者・家族
   */
  kaigoshaKnj: {
    /**
     * 介護者・家族
     */
    modelValue: Mo01280Type
    /**
     * 介護者・家族
     */
    onewayModelValue: Mo01280OnewayType
  }
}
