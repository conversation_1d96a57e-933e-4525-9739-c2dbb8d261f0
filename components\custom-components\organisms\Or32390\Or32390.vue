<script setup lang="ts">
/**
 * Or32390:有機体:'週間表複写モーダル
 * GUI00982_［週間表複写]
 *
 * @description
 *
 * <AUTHOR>
 */
import { reactive, ref, watch, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { OrX0069Const } from '../OrX0069/OrX0069.constants'
import { Or32390Const } from './Or32390.constants'
import type { Or32390StateType } from './Or32390.type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Or32390OnewayType } from '~/types/cmn/business/components/Or32390Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  WeekCopyInfoSelectInEntity,
  WeekCopyInfoSelectOutEntity,
} from '~/repositories/cmn/entities/WeekCopyInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { OrX0077OnewayType } from '~/types/cmn/business/components/OrX0077Type'
import type { OrX0125OnewayType,
  Week1 } from '~/types/cmn/business/components/OrX0125Type'
import type { OrX0069OnewayType, OrX0069Type } from '~/types/cmn/business/components/OrX0069Type'
import type { WeekTableList } from '~/components/custom-components/organisms/OrX0068/OrX0068.type'
import type {
  WeekCopHistorySelectInEntity,
  WeekCopyHistorySelectOutEntity,
} from '~/repositories/cmn/entities/weekCopyHistorySelectEntity'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo01278OnewayType } from '~/types/business/components/Mo01278Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or32390OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or32390OnewayType = {
  svJigyoId: '',
  shisetuId: '',
  userId: '',
  syubetsuId: '',
}

const localOneway = reactive({
  Or32390: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 複写テンプレート
  orx0077Oneway: {
    // 週間表複写 ダイアログ
    mo00024Oneway: {
      maxWidth: '1580px',
      height: '880px',
      persistent: true,
      showCloseBtn: true,
      mo01344Oneway: {
        name: 'Or32390',
        toolbarTitle: t('label.weekly-copy'),
        toolbarName: 'Or32390ToolBar',
        // ツールバータイトルの左寄せ
        toolbarTitleCenteredFlg: false,
        showCardActions: true,
      },
    } as Mo00024OnewayType,
  } as OrX0077OnewayType,
  orX0125Oneway: {
    // 期間リスト
    taishokikanList: [],
    // 履歴リスト
    week1List: [],
    // 期間管理フラグ
    kikanFlag: '',
    ...props.onewayModelValue,
  } as OrX0125OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定ボタン設置
  mo00609OverwriteBtnOneway: {
    btnLabel: t('btn.confirm'),
    width: '90px',
  } as Mo00609OnewayType,
  orX0069Oneway: {
    cpyFlg: true,
    columnFlag: {},
    tableItem: [],
  } as unknown as OrX0069OnewayType,
  // 文字サイズ区分
  si039OneWay: {
    items: [] as CodeType[],
    itemTitle: 'label',
    itemValue: 'value',
  },
  // 文字サイズテキストボックス
  si040OneWay: {
    mo00045Oneway: {
      maxLength: '4',
      width: '40px',
    } as Mo00045OnewayType,
  } as Mo01278OnewayType,
  // 文字位置区分
  si041OneWay: {
    items: [] as CodeType[],
    itemTitle: 'label',
    itemValue: 'value',
  },
  // 時間表示区分
  si047OneWay: {
    items: [] as CodeType[],
    itemTitle: 'label',
    itemValue: 'value',
  },
  confirmData: {
    week1Id: '',
    planTargetPeriodId: '',
    kaiteiFlg: '',
    tougaiYm: '',
    torikomitermid: '',
  }
})

const local = reactive({
  orX0069: {
    wIgaiKnj: '',
    tabindex: '',
    week1Id: '',
    planTargetPeriodId: '',
    kaiteiFlg: '',
    tougaiYm: '',
    torikomitermid: '',
  } as OrX0069Type,
  // 有効期間
  mo01282ValidPeriodOneway: [] as CodeType[],
})

/**************************************************
 * 変数定義
 **************************************************/
// OrX0069 Ref
const orX0069Ref = ref({
  initData: (_orX0069Oneway: OrX0069OnewayType) => {},
})

const orx0077 = ref({ uniqueCpId: OrX0077Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const orX0069 = ref({ uniqueCpId: OrX0069Const.CP_ID(0) })
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(0) })
// ダイアログ表示フラグ
const showDialogOr21815 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})
const emit = defineEmits(['onClickConfirm'])
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or32390StateType>({
  cpId: Or32390Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      OrX0077Logic.state.set({
        uniqueCpId: orx0077.value.uniqueCpId,
        state: { isOpen: value ?? Or32390Const.DEFAULT.IS_OPEN },
      })
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0077Const.CP_ID(0)]: orx0077.value,
  [OrX0069Const.CP_ID(0)]: orX0069.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
})

onMounted(async () => {
  await init()
})

async function init() {
  // 計画期間一覧と履歴一覧データを取得し、初期化する
  await initByUserId(localOneway.Or32390.userId ?? '')
}

/**
 * 初期表示
 *
 * @param userId - 利用者ID
 */
async function initByUserId(userId: string) {
  // 週間表複写情報取得(IN)
  const inputData: WeekCopyInfoSelectInEntity = {
    // 事業者ID
    svJigyoId: localOneway.Or32390.svJigyoId,
    // 利用者ID
    userId: userId,
    // 種別ID
    syubetsuId: localOneway.Or32390.syubetsuId,
    // 施設ID
    shisetuId: localOneway.Or32390.shisetuId,
  }
  // 週間表複写初期情報取得
  const ret: WeekCopyInfoSelectOutEntity = await ScreenRepository.select(
    'weekCopyInfoSelect',
    inputData
  )
  //計画期間一覧
  const taishokikanList = ret.data.taishokikanList
  localOneway.orX0125Oneway.taishokikanList = taishokikanList
  //履歴一覧
  const week1List = ret.data.week1List
  localOneway.orX0125Oneway.week1List = week1List
  localOneway.orX0125Oneway.kikanFlag = ret.data.kikanFlag
  localOneway.orX0069Oneway.cpyFlg = true
  orX0069Ref.value?.initData(localOneway.orX0069Oneway)
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => OrX0077Logic.state.get(orx0077.value.uniqueCpId),
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue?.isOpen) {
      close()
    }
  }
)

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 「はい」ボタン押下
    if (newValue.secondBtnClickFlg) {
      // 確認ダイアログを閉じる。処理を続行する。
      emit('onClickConfirm', localOneway.confirmData)
    } else {
      // 次の確認ダイアログ[i.cmn.10193]
      close()
    }
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 */
function close() {
  // 画面を閉じる。
  setState({ isOpen: false })
}

/**
 * 確定ボタン押下時の処理
 *
 * @description
 */
function onClickOverwriteAdd() {
  const resultList = local.orX0069.week1Id
  // AC013-1/AC014-1
  // 履歴一覧が選択しない場合
  if (resultList !== undefined && resultList.length <= 0) {
    // 以下メッセージの警告ダイアログを表示する
    showOr21814Msg(t('message.i-cmn-11289'))
    return
  } else {
    //履歴一覧が選択する場合
    showOr21814MsgTwoBtn(t('message.i-cmn-10193', [t('label.week-table')]))
    return
  }
}

/**
 * 確認ダイアログ表示する
 *
 * @param infomsg - Message
 */
function showOr21814MsgTwoBtn(infomsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: infomsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 確認ダイアログ表示する
 *
 * @param infomsg - Message
 */
function showOr21814Msg(infomsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: infomsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 期間選択変更
 */
function onPeriodSelected() {
  const tableItemList = [] as WeekTableList[]
  localOneway.orX0069Oneway.tableItem = tableItemList
  localOneway.orX0069Oneway.cpyFlg = true
  orX0069Ref.value?.initData(localOneway.orX0069Oneway)
}
/**
 * 履歴リスト選択変更
 *
 * @param item - 履歴リスト
 */
async function onHistIdSelected(item: Week1) {
  // コンポーネントの再レンダリングをトリガする
  // 週間表複写履歴変更
  const inputData: WeekCopHistorySelectInEntity = {
    // 週間表ID
    week1Id: item.week1Id,
    // 有効期間ID
    termId: item.termid,
  }
  // 週間表複写履歴変更
  const ret: WeekCopyHistorySelectOutEntity = await ScreenRepository.select(
    'weekCopyHistorySelect',
    inputData
  )
  const tableItemList = [] as WeekTableList[]
  ret.data.week2List.forEach((item) => {
    const data = {
      ...item,
      youbi: { value: item.youbi ?? '' },
      kaishiJikan: { value: item.kaishiJikan ?? '' },
      shuuryouJikan: { value: item.shuuryouJikan ?? '' },
      naiyoCd: { value: item.naiyoCd ?? '' },
      naiyoKnj: { value: item.naiyoKnj ?? '' },
      memoKnj: { value: item.memoKnj ?? '' },
      fontSize: { value: item.fontSize ?? '' },
      dispMode: { value: item.dispMode ?? '' },
      alignment: { value: item.alignment ?? '' },
      svShuruiCd: { value: item.svShuruiCd ?? '' },
      svItemCd: { value: item.svItemCd ?? '' },
      svJigyoId: { value: item.svJigyoId ?? '' },
      svShuruiKnj: { value: item.svShuruiKnj ?? '' },
      svItemKnj: { value: item.svItemKnj ?? '' },
      svJigyoKnj: { value: item.svJigyoKnj ?? '' },
      fontColor: { value: item.fontColor ?? '' },
      backColor: { value: item.backColor ?? '' },
      svJigyoRks: { value: item.svJigyoRks ?? '' },
      timeKbn: { value: item.timeKbn ?? '' },
      igaiMoji: { value: item.igaiMoji ?? '' },
      igaiKbn: { value: item.igaiKbn ?? '' },
      igaiWeek: { value: item.igaiWeek ?? '' },
      igaiDate: { value: item.igaiDate ?? '' },
      updateKbn: '',
      week3List: {
        id: item.week3List.id,
        svKasanKnj: item.week3List.svKasanKnj,
        svJigyoId: item.week3List.svJigyoId,
        svItemCd: item.week3List.svItemCd,
        kaisuu: item.week3List.kaisuu,
        svTani: item.week3List.svTani,
        fygId: item.week3List.fygId,
      },
    }
    tableItemList.push(data)
  })
  localOneway.orX0069Oneway.tableItem = tableItemList
  localOneway.orX0069Oneway.cpyFlg = true
  orX0069Ref.value?.initData(localOneway.orX0069Oneway)
  localOneway.confirmData.week1Id = item.week1Id
  localOneway.confirmData.planTargetPeriodId = item.sc1Id
  localOneway.confirmData.torikomitermid = item.termid
  localOneway.confirmData.tougaiYm = item.tougaiYm
  localOneway.confirmData.kaiteiFlg = item.kaiteiFlg
}
/**
 * 利用者選択
 *
 * @param userSelfId - 利用者ID
 */
async function onChangeUserSelect(userSelfId: string) {
  if (userSelfId !== undefined && userSelfId !== ''){
    await initByUserId(userSelfId)
  }
}
</script>

<template>
  <!-- 複写テンプレート -->
  <g-custom-or-x-0077
    v-bind="orx0077"
    :oneway-model-value="localOneway.orx0077Oneway"
    @on-change-user-select="onChangeUserSelect"
  >
    <!-- テーブル -->
    <template #filter>
      <g-custom-or-x-0125
        :oneway-model-value="localOneway.orX0125Oneway"
        @on-hist-id-click="onHistIdSelected"
        @on-period-click="onPeriodSelected"
      />
    </template>
    <!-- 複写body -->
    <template #copyMain>
      <g-custom-or-x-0069
        v-bind="orX0069"
        ref="orX0069Ref"
        v-model="local.orX0069"
      ></g-custom-or-x-0069>
    </template>
    <!-- フッターボタン -->
    <template #footer>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OverwriteBtnOneway"
          @click="onClickOverwriteAdd()"
        >
          <!--ツールチップ表示："設定を確定します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </g-custom-or-x-0077>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  />
</template>

<style scoped lang="scss"></style>
