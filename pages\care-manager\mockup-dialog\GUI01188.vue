<!-- 画面：業務基盤レイアウト確認ページ -->
<script setup lang="ts">
/**
 * GUI011188_提供事業所）
 *
 * @description
 * 提供事業所
 *
 * <AUTHOR> 宋鹏飞
 */
import {
  definePageMeta,
  ref,
  reactive,
  useScreenStore,
  useInitialize,
  useSetupChildProps,
} from '#imports'
import { Or30908Const } from '~/components/custom-components/organisms/Or30908/Or30908.constants'
import { Or30908Logic } from '~/components/custom-components/organisms/Or30908/Or30908.logic'
import type { Or30908OnewayType } from '~/types/cmn/business/components/Or30908Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * 変数定義
 **************************************************/
// 画面ID
const screenId = 'Or30908'
// ルーティング
const routing = 'care-manager/care-plan2'
// 画面物理名
const screenName = 'care-plan2'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const Or30908 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
const local = reactive({
  or30908: {
    /**
     * 事業所ID
     */
    svJigyoId: '26',
    /**
     * 担当者ID
     */
    tantoId: '12',
    /**
     * システムコード
     */
    syscd: '123',
    /**
     * システム年月
     */
    yymmYm: '2011/12',
    systemYmd: '2011/12/01',
    /**
     * 種別ID
     */
    syubetsuId: '1',
    /**
     * 職員ID
     */
    shokuinId: '26',
    /**
     * 職員名
     */
    shokuinName: '親職員名',
    /**
     * 利用者ID
     */
    userId: '1',
    /** 法人ID */
    corporationId: '11',
    /** 施設ID */
    facilityId: '12',
    /**
     * 50音行番号
     */
    gojuuonRowNo: '1',
    /**
     * 50音母音
     */
    gojuuonKana: [],
    /** ロケーション */
    location:'1213'
  } as Or30908OnewayType,
})

// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: Or30908Const.CP_ID(0) },
})
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or30908Logic.initialize(pageComponent.uniqueCpId)
}

// 子コンポーネントのユニークIDを設定する
Or30908.value.uniqueCpId = pageComponent.uniqueCpId
// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01188',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or30908Const.CP_ID(0) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or30908Const.CP_ID(0)]: Or30908.value,
})
</script>
<template>
  <div class="view-padding">
    <g-custom-Or30908
      v-bind="Or30908"
      v-model="local.or30908"
      :oneway-model-value="local.or30908"
    />
  </div>
</template>
<style scoped lang="scss">
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.view-padding {
  padding: 0px 8px;
}
</style>
