<script setup lang="ts">
/**
 * GUI00834_［アセスメント（包括）］画面
 *
 * @description
 *
 * ［アセスメント（包括）］テーブル有機体
 *
 * 画面ID_ GUI00834
 *
 * <AUTHOR>
 */
import { reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import type { Mo00040OnewayType } from '../../../../types/business/components/Mo00040Type'
import type { Or34069Type } from './Or34069.type'
import { Or34069Const } from './Or34069.constants'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or11905OnewayType } from '~/types/cmn/business/components/Or11905Type'
import { useScreenStore, useScreenTwoWayBind } from '#imports'
import type { Or34069OnewayType } from '~/types/cmn/business/components/Or34069Type'

/** Props */
interface Props {
  onewayModelValue: Or34069OnewayType
  modelValue: Or34069Type
  uniqueCpId: string
}
const props = defineProps<Props>()

const { t } = useI18n()

const { refValue } = useScreenTwoWayBind<Or34069Type>({
  cpId: Or34069Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/** One-way */
const localOneway = reactive({
  // アイコンボタン
  iconBtnOneway: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  shortTextOneway: {
    showItemLabel: false,
    itemLabel: '',
    isVerticalLabel: false,
    maxLength: '24',
    width: '100%',
  } as Mo00045OnewayType,
  Mo00040Oneway: {
    showItemLabel: false,
    items: [
      {
        value: '1',
        title: '○',
      },
      {
        value: '2',
        title: '△',
      },
      {
        value: '3',
        title: '提供',
      },
      {
        value: '',
        title: '',
      },
    ],
  } as Mo00040OnewayType,
  or11905Oneway_1: {
    optionsList: [],
  } as Or11905OnewayType,
  Mo00040Oneway_2: {
    showItemLabel: false,
    optionsList: [],
  } as Or11905OnewayType,
  Mo00040Oneway_3: {
    showItemLabel: false,
    optionsList: [],
  } as Or11905OnewayType,
  Mo00040Oneway_4: {
    showItemLabel: false,
    optionsList: [],
  } as Or11905OnewayType,
  or34069: {} as Or34069Type,
  // ページタイトル
  pageTitleOneway: {
    itemLabel: props.modelValue.title,
    itemLabelFontWeight: 'bold',
    itemLabelCustomClass: new CustomClass({
      labelClass: 'item-label',
      labelStyle: 'fontSize:18px;white-space:pre;',
    }),
  } as Mo00615OnewayType,
})

/**
 * 双方向バインド変更監視
 */
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) return
    refValue.value = cloneDeep(newValue)

    // RefValueを更新する
    useScreenStore().setCpTwoWay({
      cpId: Or34069Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: refValue.value,
      isInit: true,
    })
  },
  { deep: true, immediate: true }
)
/**
 * 単方向バインド監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (!newValue) return
    localOneway.or11905Oneway_1.optionsList = props.onewayModelValue.careOfferMarkMeaning
    localOneway.Mo00040Oneway_2.optionsList = props.onewayModelValue.careFamilyMarkMeaning
    localOneway.Mo00040Oneway_3.optionsList = props.onewayModelValue.carePlanMarkMeaning
    localOneway.Mo00040Oneway_4.optionsList = props.onewayModelValue.careLocationMarkMeaning
  },
  { deep: true }
)
</script>

<template>
  <!-- ページタイトル部分 -->
  <div class="pb-4 pt-6 pageTitle w-100">
    <base-mo00615
      :oneway-model-value="localOneway.pageTitleOneway"
      style="background-color: transparent"
    />
  </div>
  <c-v-sheet class="table-content">
    <c-v-row no-gutters>
      <div>
        <!-- ヘッダー部分 -->
        <div class="table-header d-flex">
          <div
            class="pa-3 d-flex align-center"
            style="width: 445px"
          >
            {{ t('label.care-contents-blank') }}
          </div>
          <div class="5 b-l d-flex">
            <!-- 現状 -->
            <div class="d-flex flex-column">
              <div class="h-50 d-flex align-center pl-3 b-b">
                {{ t('label.current-situation') }}
              </div>
              <div class="h-50 d-flex">
                <div class="w-50 d-flex align-center pl-3 select-box">{{ t('label.offer') }}</div>
                <div class="w-50 b-l d-flex align-center pl-3 select-box">
                  {{ t('label.family') }}
                </div>
              </div>
            </div>

            <div class="d-flex align-center pl-3 b-l select-box">
              {{ t('label.schedule') }}
            </div>
          </div>
        </div>
      </div>
      <div style="width: 506px">
        <div class="table-header right-table d-flex align-center pl-3">
          {{ t('label.care-offer-location-use-tool') }}
        </div>
      </div>
    </c-v-row>
    <!-- ボディー部分 -->
    <c-v-row
      v-if="props.onewayModelValue.showTableBodyFlg"
      no-gutters
    >
      <div class="7 b-r">
        <!-- タイトル -->
        <div
          v-for="(item, index) in refValue?.careItems"
          :key="index"
          class="12 d-flex table-body"
        >
          <div
            class="2 pa-2 pl-3 b-l b-b d-flex align-center title-content"
            style="letter-spacing: -1px"
          >
            {{ item.title }}
          </div>
          <div class="d-flex h-100">
            <!-- 具体的ケア内容 -->
            <div class="6 d-flex flex-column">
              <div
                v-for="(sItem, sIndex) in item.careLabel"
                :key="sIndex"
                class="b-l b-b"
              >
                <div
                  v-if="sItem.label"
                  class="label-content w-100 pa-2"
                >
                  {{ sItem.label }}
                </div>
                <!-- 入力欄 -->
                <div
                  v-else
                  class="label-content w-100 pa-1"
                >
                  <base-mo00045
                    v-model="sItem.inputContent"
                    :oneway-model-value="{ ...localOneway.shortTextOneway, ...sItem.inputOptions }"
                  ></base-mo00045>
                </div>
              </div>
            </div>
            <!-- セレクトコンテンツ -->
            <div
              class="12"
              style="height: 38px"
            >
              <div
                v-for="(sItem, sIndex) in item.careLabel"
                :key="sIndex"
                class="d-flex w-100 b-l h-100"
              >
                <div class="select-box b-b">
                  <g-custom-or11905
                    v-model="sItem.offerValue"
                    :oneway-model-value="localOneway.or11905Oneway_1"
                  />
                </div>
                <div class="b-l b-b select-box">
                  <g-custom-or11905
                    v-model="sItem.familyValue"
                    :oneway-model-value="localOneway.Mo00040Oneway_2"
                  />
                </div>
                <div class="b-l b-b select-box">
                  <g-custom-or11905
                    v-model="sItem.planValue"
                    :oneway-model-value="localOneway.Mo00040Oneway_3"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div
          v-for="(item, index) in refValue?.careLocationItems"
          :key="index"
          class="12 d-flex table-body b-r"
        >
          <div
            class="pa-2 pl-3 b-b d-flex align-center title-content right"
            style="letter-spacing: -1px"
          >
            {{ item.title }}
          </div>
          <div class="d-flex h-100">
            <!-- ケアの提供場所 -->
            <div class="d-flex flex-column">
              <div
                v-for="(sItem, sIndex) in item.careLocationLabel"
                :key="sIndex"
                :class="[
                  'b-l b-b d-flex align-center pr-2 pl-3 care-location-label-content',
                  sItem.inputShowMode.appendInput === '0' ? 'pa-2' : 'pa-1 pl-2',
                ]"
              >
                <div class="care-location-label-content d-flex align-center">
                  <div
                    v-if="sItem.label"
                    class="auto"
                  >
                    {{ sItem.label }}
                  </div>
                  <div
                    v-if="sItem.inputShowMode.appendInput === '1'"
                    class="flex-1-1"
                  >
                    <base-mo00045
                      v-model="sItem.inputContent"
                      :oneway-model-value="{
                        ...localOneway.shortTextOneway,
                        ...sItem.inputOptions,
                      }"
                    ></base-mo00045>
                  </div>
                  <div
                    v-if="sItem.inputShowMode.appendInput === '2'"
                    class="6 d-flex align-center pl-2"
                  >
                    (
                    <base-mo00045
                      v-model="sItem.inputContent"
                      :oneway-model-value="{
                        ...localOneway.shortTextOneway,
                        ...sItem.inputOptions,
                      }"
                      style="width: 88px"
                    />
                    <div>)</div>
                    <!-- ユニット -->
                    <div class="auto pl-2">
                      {{ sItem.inputShowMode.unit }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- セレクトコンテンツ -->
            <div class="d-flex flex-column">
              <div
                v-for="(sItem, sIndex) in item.careLocationLabel"
                :key="sIndex"
                class="b-b b-l care-location-select-box"
              >
                <g-custom-or11905
                  v-model="sItem.locationValue"
                  :oneway-model-value="localOneway.Mo00040Oneway_4"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </c-v-row>
  </c-v-sheet>
</template>

<style lang="scss" scoped>
div {
  user-select: none;
}
.table-header {
  background-color: #dbeefe;
  border: 1px rgb(var(--v-theme-black-200)) solid;
  height: 50px;
  font-weight: normal;
  &.right-table {
    border-left: none;
  }
}
.b-l {
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}
.b-b {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
}
.b-r {
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}
.table-body {
  background-color: #fff;
}
.label-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 341px !important;
  padding-left: 12px !important;
  background-color: #f2f2f2;
}
:deep(.v-field__input) {
  min-height: 29px !important;
  height: 21px !important;
  padding: 8px !important;
  min-width: 80% !important;
}
.title-content {
  white-space: pre;
  width: 104px;
  background-color: #f2f2f2;
}
.pageTitle :deep(.v-sheet) {
  background: rgb(var(--v-theme-background));
}
.table-content {
  background: transparent;
}
:deep(.v-sheet.v-theme--mainTheme.mr-2) {
  margin-right: 0px !important;
}
.select-box {
  width: 114px !important;
}
.care-location-label-content {
  width: 301px;
  background-color: #f2f2f2;
}
.care-location-select-box {
  width: 100px !important;
  height: 38px !important;
}
</style>
