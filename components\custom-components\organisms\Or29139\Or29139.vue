<script setup lang="ts">
/**
 * Or29139:履歴選択モーダル
 * GUI01163_備考欄画面
 *
 * <AUTHOR> 畢文傑
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Gui00038Logic } from '../Gui00038/Gui00038.logic'
import { Gui00038Const } from '../Gui00038/Gui00038.constants'
import { Or29210Logic } from '../Or29210/Or29210.logic'
import { Or29210Const } from '../Or29210/Or29210.constants'
import { Or29139Const } from './Or29139.constants'
import type { RemarksInfoList, Or29139StateType } from './Or29139.type'
import { useScreenStore } from '~/stores/session/screen'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useScreenUtils,
  useSetupChildProps,
} from '#imports'
import type {
  Or29139OnewayType,
  Or29139DataType,
} from '~/types/cmn/business/components/Or29139Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo01299OnewayType } from '~/types/business/components/Mo01299Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Or55021OnewayType, radioItemsList } from '~/types/cmn/business/components/Or55021Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type {
  RemarksColumnSelectInEntity,
  RemarksColumnSelectOutEntity,
} from '~/repositories/cmn/entities/RemarksColumnSelectEntity'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { RemarksColumnUpdateInEntity } from '~/repositories/cmn/entities/RemarksColumnUpdatetEntity'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { useValidation } from '@/utils/useValidation'

const { byteLength } = useValidation()

const { setChildCpBinds } = useScreenUtils()

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or29139OnewayType
  onewayModelValue: Or29139OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props: Props = defineProps<Props>()

const or21814 = ref({ uniqueCpId: '' })
// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: '' })
const or29210 = ref({ uniqueCpId: '' })
const defaultOneway = reactive({
  or55021Oneway: {
    radioItems: [],
  } as Or55021OnewayType,
})
const localOneway = reactive({
  mo00039Oneway: {
    // デフォルト値の設定
    name: 'mo00039Oneway',
    itemLabel: '',
    showItemLabel: false,
    hideDetails: true,
  } as Mo00039OnewayType,
  // はいボタン
  mo01265OnewayModelValue: {
    btnLabel: t('label.clear'),
    tooltipText: t('tooltip.clear-the-contents-of-the-note'),
  } as Mo01265OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.save'),
    tooltipText: t('tooltip.save'),
  } as Mo00609OnewayType,
  // 「履歴選択」ダイアログ
  mo00024Oneway: {
    width: '800px',
    maxWidth: '1800px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Gui01163',
      toolbarTitle: t('label.remarks-text'),
      toolbarName: 'Gui01163ToolBar',
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  textOption1: {
    showItemLabel: false,
    noResize: true,
    autoGrow: false,
    maxlength: '1000',
    rules: [byteLength(1000)],
    rows: 3,
    maxRows: '3',
  } as Mo00046OnewayType,
  textOption3: {
    showItemLabel: false,
    noResize: true,
    autoGrow: false,
    maxlength: '360',
    rules: [byteLength(360)],
    rows: 3,
    maxRows: '3',
  } as Mo00046OnewayType,
  or55021Oneway: {
    ...defaultOneway.or55021Oneway,
  } as Or55021OnewayType,
})
const mo00610Oneway: Mo00610OnewayType = {
  tooltipText: t('tooltip.care-plan2-copy-btn'),
  btnLabel: t('label.duplicate')
}

const mo01299Oneway = ref<Mo01299OnewayType>({
  anchorPoint: 'ss-1',
  title: t('label.remarks-br'),
})
const Mo0009OnewayModelValue: Mo00009OnewayType = {
  icon: true,
  btnIcon: 'edit_square',
  prependIcon: 'edit_square',
  density: 'compact',
}
const local = reactive({
  radioValue: '1',
  remarksList: [] as RemarksInfoList[],
})
/**
 * gui00038
 */
const gui00038 = ref({ uniqueCpId: Gui00038Const.CP_ID(0) })


/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or29139Const.DEFAULT.IS_OPEN,
})
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue: or29139 } = useScreenTwoWayBind<Or29139DataType>({
  cpId: Or29139Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
const { setState } = useScreenOneWayBind<Or29139StateType>({
  cpId: Or29139Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or29139Const.DEFAULT.IS_OPEN
    },
  },
})
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Gui00038Const.CP_ID(1)]: gui00038.value,
  [Or29210Const.CP_ID(1)]: or29210.value,
})

// ダイアログ表示フラグ
const showDialogOr29210 = computed(() => {
  // Or29210のダイアログ開閉状態
  return Or29210Logic.state.get(or29210.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or29210ダイアログに渡すonewayModel
 */
const Or29210Data =computed(() => {
  return {
  // 支援事業者ID
  shienId: props.onewayModelValue.shienId,
  // 利用者ID
  userId: props.onewayModelValue.userid,
  // サービス提供年月
  yymmYm: props.onewayModelValue.yymmYm,
  // サービス提供年月（変更日）
  yymmD: props.onewayModelValue.yymmD,
  // 備考欄（利用票）
  bikouRiyouKnj: or29139.value!.bikouRiyouKnj.value,
  // 備考欄（提供票）
  bikouTeikyouKnj: or29139.value!.bikouTeikyouKnj.value,
  // 備考欄（カレンダー）
  bikouCalenderKnj: or29139.value!.bikouCalenderKnj.value,
  // 帳票区分
  sel: local.radioValue,
  // 備考更新区分
  updateKbn: 'U'
}
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  void getData()
  // code取得
  await initCodes()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 帳票区分
    { mCdKbnId: CmnMCdKbnId.CLASSIFICATION_OF_ACCOUNTS_AND_INAOICES },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // 帳票区分
  localOneway.or55021Oneway.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.CLASSIFICATION_OF_ACCOUNTS_AND_INAOICES
  ) as radioItemsList[]
  localOneway.or55021Oneway.radioItems.sort((a, b) => {
    return parseInt(a.value) - parseInt(b.value);
  });
}
/** 初期情報取得 */
async function getData() {
  const inputData: RemarksColumnSelectInEntity = {
    // 支援事業者ID
    shienId: props.onewayModelValue.shienId,
    // 利用者ID
    userid: props.onewayModelValue.userid,
    // サービス提供年月
    yymmYm: props.onewayModelValue.yymmYm,
    // サービス提供年月（変更日）
    yymmD: props.onewayModelValue.yymmD,
  }

  // バックエンドAPIから初期情報取得
  const res: RemarksColumnSelectOutEntity = await ScreenRepository.select(
    'remarksColumnSelect',
    inputData
  )
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: false,
    },
  })
  if (res?.data !== null && res.data.remarksInfoList.length !== Or29139Const.DEFAULT.ZERO) {
    // 明細リスト
    local.remarksList = res.data.remarksInfoList
    or29139.value!.bikouRiyouKnj.value = local.remarksList[0]?.bikouRiyouKnj ?? ''
    or29139.value!.bikouTeikyouKnj.value = local.remarksList[0]?.bikouTeikyouKnj ?? ''
    or29139.value!.bikouCalenderKnj.value = local.remarksList[0]?.bikouCalenderKnj ?? ''

    useScreenStore().setCpTwoWay({
      cpId: Or29139Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: or29139.value,
      isInit: true,
    })
  }
  if (res.data.errorMsg) {
    const dialogResult = await showOr21813Msg()
    if (dialogResult === Or29139Const.DIALOG_RESULT_YES) {
      await close()
    }
  }
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  if (isEdit.value) {
    const result = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10091'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
    if (result === Or29139Const.DIALOG_RESULT_YES) {
      if (await saveFun()) {
        setState({ isOpen: false })
      }
    } else if (result === Or29139Const.DIALOG_RESULT_NO) {
      setState({ isOpen: false })
    }
  } else {
    setState({ isOpen: false })
  }
}

/**
 * 「確定」ボタン押下
 */
async function saveFun() {
  // 画面入力データ変更があるかどうかを判定する
  if (!isEdit.value) {
    // メッセージ内容：
    //「変更されている項目がないため、保存を行うことは出来ません。
    // 項目を入力変更してから、再度保存を行ってください。」
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-21800'),
    })
    return
  }
  const param = {
    updateKbn:Or29139Const.UPD_FLAG_U,
    remarksInfoList: local.remarksList,
  } as RemarksColumnUpdateInEntity
  param.remarksInfoList[0].bikouRiyouKnj = or29139.value!.bikouRiyouKnj.value
  param.remarksInfoList[0].bikouTeikyouKnj = or29139.value!.bikouTeikyouKnj.value
  param.remarksInfoList[0].bikouCalenderKnj = or29139.value!.bikouCalenderKnj.value
  const inputMockDataUpdate: RemarksColumnUpdateInEntity = param
  const resData = await ScreenRepository.update('remarksColumnUpdate', inputMockDataUpdate)
  if (resData.statusCode === Or29139Const.SUCCESS) {
    await getData()
  }
  return true
}
/**
 *  「複写ボタン」押下
 */
async function copyClick() {
  if (isEdit.value) {
    const result = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10091'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
    if (result === Or29139Const.DIALOG_RESULT_YES) {
      await saveFun()
    } else if (result === Or29139Const.DIALOG_RESULT_NO) {
      if (local.radioValue === Or29139Const.DEFAULT.RADIO_ONE) {
        or29139.value!.bikouRiyouKnj.value = local.remarksList[0].bikouRiyouKnj
      } else if (local.radioValue === Or29139Const.DEFAULT.RADIO_TWO) {
        or29139.value!.bikouTeikyouKnj.value = local.remarksList[0].bikouTeikyouKnj
      } else if (local.radioValue === Or29139Const.DEFAULT.RADIO_THREE) {
        or29139.value!.bikouCalenderKnj.value = local.remarksList[0].bikouCalenderKnj
      }
    }
  }else {
    Or29210Logic.state.set({
      uniqueCpId: or29210.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}
/**
 * AC004_「クリアボタン」押下
 */
async function clearClick() {
  const result = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10090'),
    firstBtnType: 'destroy1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
  })
  if (result === Or29139Const.DIALOG_RESULT_YES) {
    if (local.radioValue === Or29139Const.DEFAULT.RADIO_ONE) {
      or29139.value!.bikouRiyouKnj.value = ''
    } else if (local.radioValue === Or29139Const.DEFAULT.RADIO_TWO) {
      or29139.value!.bikouTeikyouKnj.value = ''
    } else if (local.radioValue === Or29139Const.DEFAULT.RADIO_THREE) {
      or29139.value!.bikouCalenderKnj.value = ''
    }
  }
}

// 「帳票区分」選択
async function radioClick(oldVal: string, value: string) {
  if (value !== local.radioValue) {
    if (isEdit.value) {
      const result = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10091'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })
      if (result === Or29139Const.DIALOG_RESULT_YES) {
        await saveFun()
      } else if (result === Or29139Const.DIALOG_RESULT_NO) {
        if (oldVal === Or29139Const.DEFAULT.RADIO_ONE) {
          or29139.value!.bikouRiyouKnj.value = local.remarksList[0].bikouRiyouKnj
        } else if (oldVal === Or29139Const.DEFAULT.RADIO_TWO) {
          or29139.value!.bikouTeikyouKnj.value = local.remarksList[0].bikouTeikyouKnj
        } else if (oldVal === Or29139Const.DEFAULT.RADIO_THREE) {
          or29139.value!.bikouCalenderKnj.value = local.remarksList[0].bikouCalenderKnj
        }
      } else {
        local.radioValue = oldVal
      }
    }
  }
}
/**
 * 入力支援アイコンをクリック
 */
function onOpenInputSupport() {
  Gui00038Logic.state.set({
    uniqueCpId: gui00038.value.uniqueCpId,
    state: {
      isOpen: true,
      sentenceContents:
        local.radioValue === Or29139Const.DEFAULT.RADIO_ONE
          ? or29139.value!.bikouRiyouKnj.value
          : local.radioValue === Or29139Const.DEFAULT.RADIO_TWO
            ? or29139.value!.bikouTeikyouKnj.value
            : or29139.value!.bikouCalenderKnj.value,
    },
  })
}


/**
 * Or29210監視
 */
watch(
  () => Or29210Logic.state.get(or29210.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if(!newValue){
      await getData()
    }
  }
)
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(mo00024, async () => {
  // 組織dialog自動クローズを手動判定に変更
  if (mo00024.value.emitType === 'closeBtnClick') {
    await close()
    mo00024.value.emitType = 'blank'
  }
})
/**
 * 入力支援画面確定処理
 *
 * @description
 * 入力支援画面の呼び出し元コンポーネントに値を設定する。
 * またGui00038のボタン押下フラグをリセットする。
 */

watch(
  () => Gui00038Logic.event.get(gui00038.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 文字数の上限
    const maxLength = Or29139Const.DEFAULT.MAXLENGTH1
    const maxLength3 = Or29139Const.DEFAULT.MAXLENGTH2
    if (newValue.confirmFlg && newValue.confirmParams) {
      if (local.radioValue === Or29139Const.DEFAULT.RADIO_ONE) {
        or29139.value!.bikouRiyouKnj.value = newValue.confirmParams.text.slice(0, maxLength)
      } else if (local.radioValue === Or29139Const.DEFAULT.RADIO_TWO) {
        or29139.value!.bikouTeikyouKnj.value = newValue.confirmParams.text.slice(0, maxLength)
      } else if (local.radioValue === Or29139Const.DEFAULT.RADIO_THREE) {
        or29139.value!.bikouCalenderKnj.value = newValue.confirmParams.text.slice(0, maxLength3)
      }
      // 入力支援画面のeventStatusの値をリセット
      setChildCpBinds(props.uniqueCpId, {
        [Gui00038Const.CP_ID(1)]: {
          eventStatusEvents: {
            confirmFlg: false,
            confirmParams: undefined,
          },
        },
      })
    }
  }
)

/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 確認ダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise<string>((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = Or29139Const.DIALOG_RESULT_CANCEL
        if (event?.firstBtnClickFlg) {
          result = Or29139Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or29139Const.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * error開閉
 *
 * @param uniqueCpId
 *
 * @param errormsg - Message
 */

const showOr21813Msg = () => {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40688'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row>
        <c-v-col>
            <div style="display: flex; align-items: center; justify-content: flex-end">
              <div style="margin-right: 8px">
                <base-mo00610
                  :oneway-model-value="mo00610Oneway"
                  @click.stop="copyClick"
                />
              </div>
              <div>
                <base-mo01265
                  :oneway-model-value="localOneway.mo01265OnewayModelValue"
                  color="#ff0000"
                  label-color="#ff0000"
                  @click="clearClick"
                >
                </base-mo01265>
              </div>
            </div>
            <div>
              <base-mo00039
                v-model="local.radioValue"
                :oneway-model-value="localOneway.mo00039Oneway"
              >
                <base-at-radio
                  v-for="item in localOneway.or55021Oneway.radioItems"
                  :key="item.id"
                  :name="item.label"
                  :radio-label="item.label"
                  :value="item.value"
                  @click="radioClick(local.radioValue, $event.target.value)"
                />
              </base-mo00039>
            </div>
          <c-v-container>
            <c-v-row class="subSection">
              <c-v-col class="sectionContent">
                <base-mo01299
                  :oneway-model-value="mo01299Oneway"
                  class="content"
                >
                  <template #content>
                    <div>
                      <base-mo00009
                        :oneway-model-value="Mo0009OnewayModelValue"
                        class="edit"
                        variant="flat"
                        density="compact"
                        @click="onOpenInputSupport"
                      ></base-mo00009>
                      <!-- 備考欄（利用票） -->
                      <base-mo00046
                        v-if="local.radioValue === Or29139Const.DEFAULT.RADIO_ONE"
                        v-model="or29139!.bikouRiyouKnj"
                        :oneway-model-value="localOneway.textOption1"
                        variant="outlined"
                        class="memoInputTextArea"
                      ></base-mo00046>
                      <!-- 備考欄（提供票） -->
                      <base-mo00046
                        v-else-if="local.radioValue === Or29139Const.DEFAULT.RADIO_TWO"
                        v-model="or29139!.bikouTeikyouKnj"
                        :oneway-model-value="localOneway.textOption1"
                        variant="outlined"
                        class="memoInputTextArea"
                      ></base-mo00046>
                      <!-- 備考欄（カレンダー） -->
                      <base-mo00046
                        v-else-if="local.radioValue === Or29139Const.DEFAULT.RADIO_THREE"
                        v-model="or29139!.bikouCalenderKnj"
                        :oneway-model-value="localOneway.textOption3"
                        variant="outlined"
                        class="memoInputTextArea"
                      ></base-mo00046>
                    </div>
                  </template>
                </base-mo01299>
              </c-v-col>
            </c-v-row>
          </c-v-container>
        </c-v-col>
      </c-v-row>
      <g-custom-gui00038
        v-if="Gui00038Logic.state.get(gui00038.uniqueCpId)?.isOpen"
        v-bind="gui00038"
      />
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン mo00609 -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="saveFun"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:情報ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
  <!-- Or-29210:備考欄複写画面 -->
  <g-custom-or-29210
    v-if="showDialogOr29210"
    v-bind="or29210"
    :oneway-model-value="Or29210Data"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
.content {
  position: relative;
}
// :deep(.content) .item-label{
  // margin-left: 10px;
// }
.edit {
  position: absolute;
  top: 41px;
  left: 65px;
  background: none;
}
:deep(.section-label) {
  padding-left: 24px !important;
}
.sectionContent {
  padding: 0 !important;
  // margin-bottom: 80px;
}
:deep(.section-header) {
  min-width: 115px !important;
  max-width: 115px !important;
  padding: 0 !important;
  border-right: 1px solid rgb(var(--v-theme-light));
}
:deep(.section-content) {
  padding: 0 !important;
}
</style>
