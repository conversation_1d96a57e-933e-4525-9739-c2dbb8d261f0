<script setup lang="ts">
/**
 * Or11207：有機体：概況調査
 *
 * @description
 * 概況調査
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or55021Const } from '../Or55021/Or55021.constants'
import { Or55022Const } from '../Or55022/Or55022.constants'
import { Or55024Const } from '../Or55024/Or55024.constants'
import { Or30055Const } from '../Or30055/Or30055.constants'
import { Or32374Const } from '../Or32374/Or32374.constants'
import { Or27626Logic } from '../Or27626/Or27626.logic'
import { Or27349Logic } from '../Or27349/Or27349.logic'
import { Or27626Const } from '../Or27626/Or27626.constants'
import { Or27349Const } from '../Or27349/Or27349.constants'
import { Or26323Const } from '../Or26323/Or26323.constants'
import { Or26323Logic } from '../Or26323/Or26323.logic'
import { Or26416Const } from '../Or26416/Or26416.constants'
import { Or26416Logic } from '../Or26416/Or26416.logic'
import { Or55476Logic } from '../Or55476/Or55476.logic'
import { Or55476Const } from '../Or55476/Or55476.constants'
import { Or11207Const } from './Or11207.constants'
import type { Or11207Type } from './Or11207.type'
import { useScreenStore, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { Or10412Const } from '~/components/custom-components/organisms/Or10412/Or10412.constants'
import { Or30980Const } from '~/components/custom-components/organisms/Or30980/Or30980.constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Or55021OnewayType, radioItemsList } from '~/types/cmn/business/components/Or55021Type'
import type { Or55022OnewayType } from '~/types/cmn/business/components/Or55022Type'
import type { Or55024OnewayType } from '~/types/cmn/business/components/Or55024Type'
import type { Or55025OnewayType } from '~/types/cmn/business/components/Or55025Type'
import type { Or55023OnewayType } from '~/types/cmn/business/components/Or55023Type'
import type { Or55080OnewayType } from '~/types/cmn/business/components/Or55080Type'
import type { Or62126OnewayType } from '~/types/cmn/business/components/Or62126Type'
import type { Or11207OnewayType } from '~/types/cmn/business/components/Or11207Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { Or30055Logic } from '~/components/custom-components/organisms/Or30055/Or30055.logic'
import { Or32374Logic } from '~/components/custom-components/organisms/Or32374/Or32374.logic'
import type {
  GeneralSituationSurveyDataUpdateInEntity,
  GeneralSituationSurveyDataUpdateOutEntity,
} from '~/repositories/cmn/entities/GeneralSituationSurveyPlanningPeriodInitEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or27626Type } from '~/types/cmn/business/components/Or27626Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Or27349OnewayType } from '~/types/cmn/business/components/Or27349Type'
import type { NinteiList } from '~/components/custom-components/organisms/Or27349/Or27349.type'
import type { Or26323OnewayType, Or26323Type2 } from '~/types/cmn/business/components/Or26323Type'
import type { Or26416OnewayType, Or26416Type } from '~/types/cmn/business/components/Or26416Type'
import type {
  Or55476OneWayType,
  RelatedPersonSelectResInfo,
} from '~/components/custom-components/organisms/Or55476/Or55476.type'
import type { OrX0158OnewayType } from '~/types/cmn/business/components/OrX0158Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type {
  OrX0157InputOnewayType,
  OrX0157OnewayType,
} from '~/types/cmn/business/components/OrX0157Type'
import { useValidation } from '@/utils/useValidation'
const { byteLength } = useValidation()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or11207OnewayType
  uniqueCpId: string
}
const props: Props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
// 子コンポーネント用変数
const or10412 = ref({ uniqueCpId: '' })
const or30980 = ref({ uniqueCpId: '' })
const or55021_1 = ref({ uniqueCpId: '' })
const or55022_1 = ref({ uniqueCpId: '' })
const or55024_1 = ref({ uniqueCpId: '' })
const or27626 = ref({ uniqueCpId: '' })
const or26323 = ref({ uniqueCpId: '' })
const or27349 = ref({ uniqueCpId: '' })
const systemCommonsStore = useSystemCommonsStore()
const contentRef = ref<HTMLDivElement | null>(null)
const or26416 = ref({ uniqueCpId: Or26416Const.CP_ID(1) })
const or55476 = ref({ uniqueCpId: '' })

const defaultOneway = reactive({
  or55021Oneway: {
    radioItems: [],
  } as Or55021OnewayType,
  or55022Oneway: {} as Or55022OnewayType,
  or55023Oneway: {
    radioItems: [],
  } as Or55023OnewayType,
  or55024Oneway: {} as Or55024OnewayType,
  or55025Oneway: {
    radioItems: [],
  } as Or55025OnewayType,
  or55077Oneway: {
    radioItems: [],
  } as Or55021OnewayType,
  or55080Oneway: {
    radioItems: [],
  } as Or55080OnewayType,
  or62126Oneway: {
    radioItems: [],
  } as Or62126OnewayType,
})

const localOneway = reactive({
  // 氏名
  orX0157ReadonlyOneway: {
    inputMode: Or11207Const.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        width: '180',
        showItemLabel: false,
        readonly: false,
        maxLength: '8',
      } as OrX0157InputOnewayType,
    },
  } as OrX0157OnewayType,
  // 入所措置の端緒テキストエリア
  orX0156Oneway: {
    showItemLabel: false,
    noResize: true,
    showDividerLineFlg: false,
    maxlength: '4000',
    rows: '6',
    maxRows: '6',
  } as OrX0156OnewayType,
  copyFlg: false,
  delFlg: '',
  service1CdName: '',
  service9CdName: '',
  service10CdName: '',
  service23CdName: '',
  mo00039Oneway: {
    // デフォルト値の設定
    name: Or55021Const.CP_ID(0),
    itemLabel: '',
    showItemLabel: false,
    hideDetails: true,
  } as Mo00039OnewayType,
  // 判定ラベル
  mo00615Oneway1: {
    btnLabel: t('label.decide'),
    width: '90px',
    height: '32px',
  },
  // 主治医意見書選択
  mo00611Oneway1: {
    btnLabel: '',
    tooltipText:
      t('label.medical-doctor-opinion-selection-subtitle1') +
      t('label.medical-doctor-opinion-selection-subtitle2'),
    width: '160px',
    height: '32px',
  },
  // 判定アイコンボタン
  mo00009Oneway1: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // 取込ラベル
  mo00615Oneway2: {
    itemLabel: t('label.import2'),
    customClass: new CustomClass({
      outerClass: 'mr-0 label_transparent label_margin_left_16',
      labelClass: 'ma-1',
    }),
  } as Mo00615OnewayType,
  // 取込アイコンボタン
  mo00009Oneway2: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // 要介護度見込
  mo00615Oneway3: {
    itemLabel: t('label.level-of-care-required_expect'),
    customClass: new CustomClass({ outerClass: 'mr-0 label_transparent', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  // 要介護度見込ボタン
  mo00009Oneway3: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // 要介護度確定
  mo00615Oneway4: {
    itemLabel: t('label.level-of-care-required_define'),
    customClass: new CustomClass({
      outerClass: 'mr-0 label_transparent label_margin_left_8',
      labelClass: 'ma-1',
    }),
  } as Mo00615OnewayType,
  // 要介護度確定ボタン
  mo00009Oneway4: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // 要介護度認定日
  createDateOneway1: {
    itemLabel: t('label.level-of-care-required_define_date'),
    isRequired: false,
    isVerticalLabel: true,
    showItemLabel: true,
    customClass: new CustomClass({ outerClass: '', labelClass: 'mb-1' }),
    width: '127',
  } as Mo00020OnewayType,
  // 認定申請日
  createDateOneway2: {
    itemLabel: t('label.certification-application-date'),
    isRequired: false,
    isVerticalLabel: true,
    showItemLabel: true,
    customClass: new CustomClass({ outerClass: '', labelClass: 'mb-1' }),
    width: '127',
  } as Mo00020OnewayType,
  // 主治医意見書選択
  mo00615Oneway5: {
    itemLabel: t('label.medical-doctor-opinion-selection'),
    customClass: new CustomClass({ outerClass: 'mr-0 label_transparent', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  // 主治医意見書選択ボタン
  mo00009Oneway5: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // ※一次判定で使用する主治医意見書
  medicalDoctorOpinionSelectionSubTitle1: {
    value: t('label.medical-doctor-opinion-selection-subtitle1'),
    customClass: new CustomClass({ outerClass: 'label_transparent label_margin_left_16' }),
  } as Mo01338OnewayType,
  // を選択してください。
  medicalDoctorOpinionSelectionSubTitle2: {
    value: t('label.medical-doctor-opinion-selection-subtitle2'),
    customClass: new CustomClass({ outerClass: 'label_transparent label_margin_left_28' }),
  } as Mo01338OnewayType,
  // 記入日
  mo00615Oneway6: {
    itemLabel: t('label.entry-date'),
    customClass: new CustomClass({ outerClass: 'mr-0 label_transparent', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  // 記入日
  createYmd: {
    value: '',
  } as Mo00045Type,
  //要介護度プルダウン
  mo00040Oneway: {
    itemLabel: t('label.level-of-care-required_expect'),
    showItemLabel: true,
    isRequired: false,
    items: [{ title: '', value: '' }],
    customClass: new CustomClass({ outerClass: '', labelClass: 'mb-1' }),
    width: '150px',
    hideDetails: true,
  } as Mo00040OnewayType,
  //要介護度確定
  mo00040Oneway5: {
    showEditBtnFlg: true,
    showItemLabel: true,
    itemLabel: t('label.level-of-care-required_define2'),
    items: [{ title: '', value: '' }],
    isRequired: false,
    customClass: new CustomClass({ outerClass: '', labelClass: 'mb-1' }),
    width: '186px',
    itemTitle: 'title',
    itemValue: 'value',
  } as OrX0158OnewayType,
  //要支援プルダウン
  mo00040Oneway2: {
    itemLabel: '',
    showItemLabel: false,
    isRequired: false,
    items: [{ title: '', value: '' }],
    width: '90px',
    hideDetails: true,
  } as Mo00040OnewayType,
  //要介護プルダウン
  mo00040Oneway3: {
    itemLabel: '',
    showItemLabel: false,
    isRequired: false,
    items: [{ title: '', value: '' }],
    width: '90px',
    hideDetails: true,
  } as Mo00040OnewayType,
  //調査対象者との関係
  mo00040Oneway4: {
    itemLabel: '',
    showItemLabel: false,
    isRequired: false,
    items: [{ title: '', value: '' }],
    width: '200px',
    hideDetails: true,
  } as Mo00040OnewayType,
  // Ⅰ調査実施者(記入者)
  row_large_title_1: {
    value: t('label.investigator-recorder-title1'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({ itemClass: 'row_large_title' }),
  } as Mo01338OnewayType,
  // 実施場所
  row_title_1_1: {
    value: t('label.investigator-recorder-title1-row1-title'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // Ⅱ調査対象者
  row_large_title_2: {
    value: t('label.investigator-recorder-title2'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({ itemClass: 'row_large_title' }),
  } as Mo01338OnewayType,
  // 過去の認定
  row_title_2_1: {
    value: t('label.investigator-recorder-title2-row1-title'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // 氏名
  row_title_family_1_1: {
    value: t('label.name'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // 調査対象者との関係
  row_title_family_1_2: {
    value: t('label.relationship-with-investigators'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // 電話
  row_title_family_1_3: {
    value: t('label.tel'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // 郵便番号
  row_title_family_1_4: {
    value: t('label.postal-code'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // 住所
  row_title_family_1_5: {
    value: t('label.address'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // 施設等名
  row_title_facilities_1_1: {
    value: t('label.facility-name-2'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // 住所
  row_title_facilities_1_2: {
    value: t('label.address'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // 前回認定
  row_title_2_2: {
    value: t('label.investigator-recorder-title2-row2-title'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // 前回認定結果
  row_title_2_3: {
    value: t('label.investigator-recorder-title2-row3-title'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({
      outerClass: 'label_transparent row_title',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo01338OnewayType,
  // 家族等連絡先
  row_title_2_4: {
    value: t('label.investigator-recorder-title2-row4-title'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({ outerClass: 'label_transparent row_title' }),
  } as Mo01338OnewayType,
  // Ⅲ現在受けているサービスの状況についてチェック及び頻度を記入してください。
  row_large_title_3: {
    value: t('label.investigator-recorder-title3'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({ itemClass: 'row_large_title' }),
  } as Mo01338OnewayType,
  // 在宅利用[認定調査を行った月のサービス利用回数を記入。(介護予防)福祉用具貸与は調査日時点の､特定(介護予防)福祉用具販売は過去6月の品目数を記載]
  row_large_title_3_subtitle: {
    value: t('label.investigator-recorder-title3-subtitle'),
    customClass: new CustomClass({ itemClass: 'row_large_title3_subtitle' }),
  } as Mo01338OnewayType,
  // 施設等利用
  row_title_3_2: {
    value: t('label.investigator-recorder-title3-row2-title1'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({ outerClass: 'label_transparent row_title' }),
  } as Mo01338OnewayType,
  // 施設等連絡先
  row_title_3_3: {
    value: t('label.investigator-recorder-title3-row3-title'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({ outerClass: 'label_transparent row_title' }),
  } as Mo01338OnewayType,
  // Ⅳ調査対象者の家族状況、調査対象者の居住環境（外出が困難になるほど日常生活に支障となるような環境の有無）、施設等における状況、日常的に使用する機器・器械の有無等について特記すべき事項を記入してください。
  row_large_title_4_1: {
    value: t('label.investigator-recorder-title4-1-1'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({ itemClass: 'row_large_title2' }),
  } as Mo01338OnewayType,
  row_large_title_4_2: {
    value: t('label.investigator-recorder-title4-1-2'),
    valueFontWeight: 'bolder',
    customClass: new CustomClass({ itemClass: 'row_large_title2' }),
  } as Mo01338OnewayType,
  or55021Oneway: {
    ...defaultOneway.or55021Oneway,
  } as Or55021OnewayType,
  // 実施場所
  or55022Oneway: {
    itemLabel: '',
    showItemLabel: false,
    isVerticalLabel: false,
    maxLength: '20',
    rules: [byteLength(20)],
    width: '240px',
    customClass: new CustomClass({ outerClass: 'mb-1', labelClass: 'mx-2' }),
  } as Mo00045OnewayType,
  // 記入日
  or55022OnewayEntrydate: {
    itemLabel: t('label.entry-date'),
    isVerticalLabel: true,
    customClass: new CustomClass({ outerClass: '', labelClass: 'mb-1' }),
    width: '110',
  } as Mo00045OnewayType,
  or55023Oneway: {
    ...defaultOneway.or55023Oneway,
  } as Or55023OnewayType,
  or55024Oneway: {
    ...defaultOneway.or55024Oneway,
  } as Or55024OnewayType,
  // 前回認定
  createDateOneway3: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
    customClass: new CustomClass({ labelClass: 'ma-1' }),
    width: '135px',
  } as Mo00020OnewayType,
  or55025Oneway: {
    ...defaultOneway.or55025Oneway,
  } as Or55025OnewayType,
  // 家族等連絡先
  mo00009Oneway6: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // 氏名
  or55028Oneway: {
    showItemLabel: false,
    itemLabel: t('label.name'),
    isVerticalLabel: false,
    maxLength: '40',
    width: '240px',
    customClass: new CustomClass({
      outerClass: 'w-75 mb-1',
      labelClass: 'mx-2 label-val-title-width',
    }),
  } as Mo00045OnewayType,
  // 調査対象者との関係
  mo00615Oneway9: {
    itemLabel: t('label.relationship-with-investigators'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  // 要介護度確定ボタン
  mo00009Oneway7: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // 電話
  or550305Oneway: {
    showItemLabel: false,
    itemLabel: t('label.tel'),
    isVerticalLabel: false,
    maxLength: '14',
    rules: [byteLength(14)],
    width: '180px',
    customClass: new CustomClass({
      outerClass: 'w-75 mb-1',
      labelClass: 'mx-2 label-val-title-width',
    }),
  } as Mo00045OnewayType,
  // 郵便番号
  or550411Oneway: {
    showItemLabel: false,
    itemLabel: t('label.postal-code'),
    isVerticalLabel: false,
    maxLength: '8',
    width: '180px',
    customClass: new CustomClass({ outerClass: 'mb-1', labelClass: 'mx-2 label-val-title-width' }),
  } as Mo00045OnewayType,
  // 住所
  or550411Oneway_address: {
    showItemLabel: false,
    itemLabel: t('label.postal-address'),
    isVerticalLabel: false,
    maxLength: '114',
    width: '480px',
    customClass: new CustomClass({ outerClass: 'mb-1', labelClass: 'mx-2 label-val-title-width' }),
  } as Mo00045OnewayType,
  // 住所
  or55032Oneway: {
    itemLabel: t('label.address'),
    showItemLabel: false,
    isVerticalLabel: false,
    rows: '2',
    autoGrow: false,
    maxLength: '114',
    customClass: new CustomClass({
      outerClass: 'w-75 mb-1',
      labelClass: 'mx-2 label-val-title-width',
    }),
  } as Mo00046OnewayType,
  // 内容特記事項
  mo00046Oneway: {
    itemLabel: '',
    showItemLabel: false,
    isVerticalLabel: false,
    rows: '6',
    autoGrow: false,
    maxLength: '4000',
    rules: [byteLength(4000)],
    customClass: new CustomClass({}),
  } as Mo00046OnewayType,
  // 月
  or55034Oneway: {
    itemLabel: t('label.weekly-plan-day-short-sunday'),
    isVerticalLabel: false,
    maxLength: '3',
    width: '60',
    customClass: new CustomClass({ outerClass: 'mb-1', labelClass: 'or55034Oneway_mr' }),
  } as Mo00045OnewayType,
  or55055Oneway: {
    isVerticalLabel: false,
    maxLength: '40',
    width: '60',
    customClass: new CustomClass({ outerClass: 'mb-1', labelClass: 'mx-2' }),
  } as Mo00045OnewayType,
  // 回
  label_times: {
    value: t('label.times'),
    customClass: new CustomClass({ itemClass: 'or55034Oneway_ml' }),
  } as Mo01338OnewayType,
  // 品目
  product_category: {
    value: t('label.product-category'),
    customClass: new CustomClass({ itemClass: 'or55034Oneway_ml' }),
  } as Mo01338OnewayType,
  // 日
  label_day: {
    value: t('label.day'),
    customClass: new CustomClass({ itemClass: 'or55034Oneway_ml' }),
  } as Mo01338OnewayType,
  or55077Oneway: {
    radioItems: [],
  } as Or55025OnewayType,
  // 市町村特別給付
  or55078Oneway: {
    itemLabel: '',
    maxlength: '76',
    rules: [byteLength(76)],
    isVerticalLabel: false,
    customClass: new CustomClass({
      outerClass: 'w-100 mb-1',
      labelClass: 'pt-0 ma-2',
      labelStyle: 'margin-top: 4px !important;hight:50px !important;',
    }),
  } as Mo00045OnewayType,
  // 護保険給付外の在宅ｻｰﾋﾞｽ
  or55079Oneway: {
    itemLabel: '',
    maxlength: '66',
    rules: [byteLength(66)],
    isVerticalLabel: false,
    customClass: new CustomClass({
      outerClass: 'w-100 mb-1',
      labelClass: 'pt-0 ma-2',
      labelStyle: 'margin-top: 4px !important;hight:50px !important;',
    }),
  } as Mo00045OnewayType,
  or55080Oneway: {
    ...defaultOneway.or55080Oneway,
  } as Or55080OnewayType,
  or55080Oneway1: {
    radioItems: [],
  } as Or55080OnewayType,
  or55080Oneway2: {
    radioItems: [],
  } as Or55080OnewayType,
  // 施設等連絡先施設名
  or55085Oneway: {
    itemLabel: '',
    showItemLabel: false,
    isVerticalLabel: false,
    maxLength: '42',
    rules: [byteLength(42)],
    width: '240px',
    customClass: new CustomClass({
      outerClass: 'w-75 mb-1',
      labelClass: 'mx-2 label-val-title-width',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo00045OnewayType,
  // 施設等連絡先郵便番号
  or50411Oneway: {
    itemLabel: t('label.postal-code'),
    isVerticalLabel: false,
    maxLength: '40',
    width: '240px',
    customClass: new CustomClass({
      outerClass: 'w-75 mb-1',
      labelClass: 'mx-2 label-val-title-width',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo00045OnewayType,
  // 施設等連絡先住所
  or55087Oneway: {
    itemLabel: t('label.address'),
    showItemLabel: false,
    isVerticalLabel: false,
    maxLength: '108',
    rules: [byteLength(108)],
    width: '500px',
    customClass: new CustomClass({
      outerClass: 'mb-1',
      labelClass: 'mx-2 label-val-title-width',
      itemStyle: 'font-weight: normal !important;',
    }),
  } as Mo00045OnewayType,
  // 施設等連絡先電話
  or55088Oneway: {
    itemLabel: t('label.tel'),
    isVerticalLabel: false,
    maxLength: '14',
    rules: [byteLength(14)],
    width: '185px',
    customClass: new CustomClass({
      outerClass: 'w-75 mb-1',
      labelClass: 'mx-2 label-val-title-width',
    }),
  } as Mo00045OnewayType,
  // 施設等連絡先
  mo00009Oneway8: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // ※家族状況
  row_title_4_1: {
    value: t('label.investigator-recorder-title4-row1-title'),
    customClass: new CustomClass({ outerClass: 'label_transparent row_title' }),
  } as Mo01338OnewayType,
  or62126Oneway: {
    ...defaultOneway.or62126Oneway,
  } as Or62126OnewayType,
  // (家族状況については、左のいずれかにチェックするとともに特記すべき事項を記載)
  family_situation_describe: {
    value: t('label.family-situation-describe'),
  } as Mo01338OnewayType,
  // ※1 特定施設入居者生活介護適用施設を除く。
  contact_for_facilities_describe1: {
    value: t('label.contact_for_facilities_describe1'),
    customClass: { itemStyle: 'font-size:12px;' },
  } as Mo01338OnewayType,
  // ※2サービス付き高齢者向け住宅の登録を受けているものを除く。
  contact_for_facilities_describe2: {
    value: t('label.contact_for_facilities_describe2'),
    customClass: { itemStyle: 'font-size:12px;' },
  } as Mo01338OnewayType,
  or26323Oneway: {
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    userId: systemCommonsStore.getUserId ?? '',
    sc1Id: '',
    mode: Or11207Const.mode_1,
    plan1Id: '',
  } as Or26323OnewayType,
})

const local = reactive({
  // 計画期間情報開始日
  startYmd: { startYmd1: '' } as Or27349OnewayType,
  // 調査票改訂フラグ
  dmyCho: '',
  or11207: {
    ninteiYmd: {},
    ninteiShinseiYmd: {},
    oldNinteiYmd: {},
    renrakuNameKnj: {},
  } as Or11207Type,
  // 要介護度認定日
  createDate1: {
    value: '',
  } as Mo00020Type,
  // 認定申請日
  createDate2: {
    value: '',
  } as Mo00020Type,
  // 期間管理フラグ
  kikanKanriFlg: '',
  // 利用者ID
  userId: '',
  // 基準日
  kijunbiYmd: '',
})

const or26416OnewayType = ref<Or26416OnewayType>({
  /**
   * 認定フラグ
   */
  ninteiFlg: '1',
  /**
   * 計画期間ID
   */
  sc1Id: '1',
  /**
   * 調査票ID
   */
  cschId: '1',
})

const or55476OneWayType = ref<Or55476OneWayType>({
  userId: '',
  telCellFlg: '',
  createYmd: '',
  kinouId: '',
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or11207Type>({
  cpId: Or11207Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or10412Const.CP_ID(1)]: or10412.value,
  [Or30980Const.CP_ID(1)]: or30980.value,
  [Or55021Const.CP_ID(1)]: or55021_1.value,
  [Or55022Const.CP_ID(1)]: or55022_1.value,
  [Or55024Const.CP_ID(1)]: or55024_1.value,
  [Or27626Const.CP_ID(1)]: or27626.value,
  [Or26323Const.CP_ID(1)]: or26323.value,
  [Or27349Const.CP_ID(1)]: or27349.value,
  [Or26416Const.CP_ID(0)]: or26416.value,
  [Or55476Const.CP_ID(1)]: or55476.value,
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // code取得
  await initCodes()
  if (props.onewayModelValue.copyFlg) {
    localOneway.copyFlg = props.onewayModelValue.copyFlg
  }
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 集計する要介護度
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DEGREE_NURSING_CARE },
    // 実施場所
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_IMPLEMENT_PLACE },
    // 過去の認定
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PAST_RECOGNITION },
    // 前回認定結果
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PREVIOUS_CERTIFICATION_RESULTS },
    // 前回認定結果(要支援度)
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PREVIOUS_CERTIFICATION_RESULTS_SUPPORT_REQUIRED_DEGREE },
    // 前回認定結果(要介護度)）
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PREVIOUS_CERTIFICATION_RESULTS_REQUIRED_CARE },
    // 住宅改修
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_RESIDENTIAL_RENOVATION },
    // 利用施設種別3
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_3 },
    // 利用施設種別4
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_4 },
    // 家族状況
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_KAZOKUJOKYO },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_3).map((item) => {
    if (
      item.value === Or11207Const.STR_9 ||
      item.value === Or11207Const.STR_10 ||
      item.value === Or11207Const.STR_12
    ) {
      item.label = item.label + Or11207Const.FACILITY_TYPE_1
    } else if (item.value === Or11207Const.STR_11) {
      item.label = item.label + Or11207Const.FACILITY_TYPE_12
    }
    return item
  })

  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_4).map((item) => {
    if (
      item.value === Or11207Const.STR_10 ||
      item.value === Or11207Const.STR_11 ||
      item.value === Or11207Const.STR_13
    ) {
      item.label = item.label + Or11207Const.FACILITY_TYPE_1
    } else if (item.value === Or11207Const.STR_12) {
      item.label = item.label + Or11207Const.FACILITY_TYPE_12
    }
    return item
  })

  // 集計する要介護度
  localOneway.mo00040Oneway.items = []
  localOneway.mo00040Oneway.items?.push(
    ...CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_DEGREE_NURSING_CARE).map((item) => {
      return { title: item.label, value: item.value }
    })
  )
  // 要介護度確定
  localOneway.mo00040Oneway5.items = []
  const displayItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DEGREE_NURSING_CARE
  ).map((item) => {
    return { title: ref(item.label), value: ref(item.value) }
  })
  localOneway.mo00040Oneway5.items = displayItems
  // 要支援プルダウン
  localOneway.mo00040Oneway2.items = []
  localOneway.mo00040Oneway2.items?.push(
    ...CmnSystemCodeRepository.filter(
      CmnMCdKbnId.M_CD_KBN_ID_PREVIOUS_CERTIFICATION_RESULTS_SUPPORT_REQUIRED_DEGREE
    ).map((item) => {
      return { title: item.label, value: item.value }
    })
  )
  // 要介護プルダウン
  localOneway.mo00040Oneway3.items = []
  localOneway.mo00040Oneway3.items?.push(
    ...CmnSystemCodeRepository.filter(
      CmnMCdKbnId.M_CD_KBN_ID_PREVIOUS_CERTIFICATION_RESULTS_REQUIRED_CARE
    ).map((item) => {
      return { title: item.label, value: item.value }
    })
  )
  // 実施場所
  localOneway.or55021Oneway.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_IMPLEMENT_PLACE
  ) as radioItemsList[]
  // 過去の認定
  localOneway.or55023Oneway.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PAST_RECOGNITION
  ) as radioItemsList[]
  // 前回認定結果
  localOneway.or55025Oneway.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PREVIOUS_CERTIFICATION_RESULTS
  ) as radioItemsList[]
  // 住宅改修
  localOneway.or55077Oneway.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_RESIDENTIAL_RENOVATION
  ) as radioItemsList[]
  // 家族状況
  localOneway.or62126Oneway.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_KAZOKUJOKYO
  ) as radioItemsList[]
}

/**
 * pinia取得
 */
const getDate = () => {
  local.startYmd.startYmd1 =
    Or30055Logic.data.get(props.onewayModelValue.parentId)?.kikanInfo[0]?.startYmd ?? ''
  localOneway.or26323Oneway.sc1Id =
    Or30055Logic.data.get(props.onewayModelValue.parentId)?.kikanInfo[0]?.sc1Id ?? ''
  localOneway.or26323Oneway.plan1Id =
    Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]?.cpnTucCscList[0]
      ?.cschId ?? ''
  // 調査対象者との関係
  // 続柄リスト
  if (Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData) {
    if (Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]?.gaiList) {
      const zokugaraList = Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]
        .gaiList[0]?.zokugaraList
      if (zokugaraList) {
        localOneway.mo00040Oneway4.items = []
        localOneway.mo00040Oneway4.items?.push(
          ...zokugaraList.map((item) => {
            return { title: item.zokugaraKnj, value: item.zcode }
          })
        )
      }
    }
  }

  if (Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData) {
    if (Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]?.cpnTucCscList) {
      // 調査票リスト
      const cpnTucCscList = Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]
        .cpnTucCscList
      if (cpnTucCscList) {
        // 調査票改訂フラグ
        local.dmyCho = cpnTucCscList[0]?.dmyCho
        // 調査票リスト．調査票改訂フラグ＝４の場合：
        if (cpnTucCscList[0].dmyCho === Or11207Const.dmyCho_4) {
          // "施設利用" と表示
          localOneway.row_title_3_2.value = t('label.investigator-recorder-title3-row2-title2')
          // 特記事項セクション
          localOneway.row_large_title_4_1.value = t('label.investigator-recorder-title4-1-1')
          localOneway.row_large_title_4_2.value = t('label.investigator-recorder-title4-1-2')
        } else {
          // 調査票リスト．調査票改訂フラグ＝5の場合：
          // "施設等利用" と表示
          localOneway.row_title_3_2.value = t('label.investigator-recorder-title3-row2-title1')
          // 特記事項セクション
          localOneway.row_large_title_4_1.value = t('label.investigator-recorder-title4-2-1')
          localOneway.row_large_title_4_2.value = t('label.investigator-recorder-title4-2-2')
        }
        const jisshiDateYmd = cpnTucCscList[0]?.jisshiDateYmd.replaceAll('/', '')
        if (
          cpnTucCscList[0]?.dmyCho === Or11207Const.dmyCho_4 &&
          Number(jisshiDateYmd) < Or11207Const.ymd_20150401
        ) {
          // (調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2015/04/01”)の場合、”(介護予防)訪問介護(ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)”を表示する
          // サービス利用CD1
          localOneway.service1CdName = t('label.certification-survey-service-cd-name1')
          // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2015/04/01”)の場合、”(介護予防)短期入所生活介護(特養等)”を表示する
          // サービス利用CD9
          localOneway.service9CdName = t('label.certification-survey-service-cd-name2')
          // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2015/04/01”)の場合、”複合型サービス”を表示する
          // サービス利用CD23
          localOneway.service23CdName = t('label.certification-survey-service-cd-name3')
        } else if (
          (cpnTucCscList[0]?.dmyCho === Or11207Const.dmyCho_4 &&
            Number(jisshiDateYmd) >= Or11207Const.ymd_20180401) ||
          cpnTucCscList[0]?.dmyCho === Or11207Const.dmyCho_5
        ) {
          // (調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2018/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、”訪問介護(ﾎｰﾑﾍﾙﾌﾟ)･訪問型ｻｰﾋﾞｽ'”を表示する
          // サービス利用CD1
          localOneway.service1CdName = t('label.certification-survey-service-cd-name4')
          // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2018/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、”（介護予防）短期入所療養介護（療養ｼｮｰﾄ）”を表示する
          // サービス利用CD10
          localOneway.service10CdName = t('label.certification-survey-service-cd-name5')
          // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2018/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、利用施設種別4リスト.コード内容を表示する
          // 施設等利用利用施設種別4
          localOneway.or55080Oneway = localOneway.or55080Oneway1
        } else if (
          cpnTucCscList[0]?.dmyCho === Or11207Const.dmyCho_4 &&
          Number(jisshiDateYmd) >= Or11207Const.ymd_20150401 &&
          Number(jisshiDateYmd) < Or11207Const.ymd_20180401
        ) {
          // (調査票リスト．調査票改訂フラグ＝４且つ”2015/04/01”<=画面.実施日<”2018/04/01”)の場合、”(介護予防)訪問介護(ﾎｰﾑﾍﾙﾌﾟ)･訪問型ｻｰﾋﾞｽ”を表示する
          // サービス利用CD1
          localOneway.service1CdName = t('label.certification-survey-service-cd-name6')
        }

        if (
          (cpnTucCscList[0]?.dmyCho === Or11207Const.dmyCho_4 &&
            Number(jisshiDateYmd) >= Or11207Const.ymd_20150401) ||
          cpnTucCscList[0]?.dmyCho === Or11207Const.dmyCho_5
        ) {
          // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2015/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、”(介護予防)短期入所生活介護(ショートステイ)”を表示する
          // サービス利用CD9
          localOneway.service9CdName = t('label.certification-survey-service-cd-name7')
          // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2015/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、”看護小規模多機能型居宅介護”を表示する
          // サービス利用CD23
          localOneway.service23CdName = t('label.certification-survey-service-cd-name8')
        }
        if (
          cpnTucCscList[0]?.dmyCho === Or11207Const.dmyCho_4 &&
          Number(jisshiDateYmd) < Or11207Const.ymd_20180401
        ) {
          // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2018/04/01”)の場合、”(介護予防)短期入所療養介護(老健・診療所)”を表示する
          // サービス利用CD10
          localOneway.service10CdName = t('label.certification-survey-service-cd-name9')
          // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2018/04/01”)の場合、利用施設種別3リスト.コード内容を表示する
          // 施設等利用利用施設種別3
          localOneway.or55080Oneway = localOneway.or55080Oneway2
        }
      }
    }
    if (Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]?.gaiList) {
      if (
        Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]?.gaiList[0]
          ?.gssList
      ) {
        const data = Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]
          .gaiList[0]?.gssList[0]
        // 要介護度見込select
        refValue.value!.yokaiKbn1 = data ? { modelValue: data.yokaiKbn1 } : { modelValue: '' }
        // 要介護度確定
        refValue.value!.yokaiKbn2 = data ? { modelValue: data.yokaiKbn2 } : { modelValue: '' }
        // 要介護度認定日date
        refValue.value!.ninteiYmd = data ? { value: data.ninteiYmd } : { value: '' }
        // 認定申請日
        refValue.value!.ninteiShinseiYmd = data ? { value: data.ninteiShinseiYmd } : { value: '' }
        // 記入日
        localOneway.createYmd.value = data ? data.createYmd : ''
        // 実施場所
        refValue.value!.whereCd = data ? data.whereCd : ''
        // 実施場所その他text
        refValue.value!.whereMemoKnj = data ? { value: data.whereMemoKnj } : { value: '' }
        // 過去の認定
        refValue.value!.oldNintei = data ? data.oldNintei : ''
        // 前回認定
        refValue.value!.oldNinteiYmd = data ? { value: data.oldNinteiYmd } : { value: '' }
        // 前回認定結果
        refValue.value!.oldNinteiDo1 = data ? data.oldNinteiDo1 : ''
        // 要支援プルダウン
        if (refValue.value!.oldNinteiDo1 === Or11207Const.oldNinteiDo_2) {
          refValue.value!.oldNinteiDo3 = data
            ? { modelValue: data.oldNinteiDo3 }
            : { modelValue: '' }
        } else {
          refValue.value!.oldNinteiDo3 = data ? { modelValue: '' } : { modelValue: '' }
        }
        // 要介護プルダウン
        if (refValue.value!.oldNinteiDo1 === Or11207Const.oldNinteiDo_3) {
          refValue.value!.oldNinteiDo2 = data
            ? { modelValue: data.oldNinteiDo3 }
            : { modelValue: '' }
        } else {
          refValue.value!.oldNinteiDo2 = data ? { modelValue: '' } : { modelValue: '' }
        }
        // 氏名
        refValue.value!.renrakuNameKnj = data ? { value: data.renrakuNameKnj } : { value: '' }
        // 調査対象者との関係
        refValue.value!.kankeiId = data ? { modelValue: data.kankeiId } : { modelValue: '' }
        // 電話
        refValue.value!.renrakuTel = data ? { value: data.renrakuTel } : { value: '' }
        // 郵便番号
        refValue.value!.renrakuZip = data ? data.renrakuZip : ''
        // 住所 textarea
        refValue.value!.addressKnj.value = data ? data.addressKnj : ''

        // サービス利用CD1 checkbox
        refValue.value!.service1Cd = data
          ? data.service1Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数1
        refValue.value!.kaisuu1 = data ? { value: data.kaisuu1 } : { value: '' }
        // サービス利用CD2 checkbox
        refValue.value!.service2Cd = data
          ? data.service2Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数2
        refValue.value!.kaisuu2 = data ? { value: data.kaisuu2 } : { value: '' }
        // サービス利用CD3 checkbox
        refValue.value!.service3Cd = data
          ? data.service3Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数3
        refValue.value!.kaisuu3 = data ? { value: data.kaisuu3 } : { value: '' }
        // サービス利用CD4 checkbox
        refValue.value!.service4Cd = data
          ? data.service4Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数4
        refValue.value!.kaisuu4 = data ? { value: data.kaisuu4 } : { value: '' }
        // サービス利用CD5 checkbox
        refValue.value!.service5Cd = data
          ? data.service5Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数5
        refValue.value!.kaisuu5 = data ? { value: data.kaisuu5 } : { value: '' }
        // サービス利用CD6 checkbox
        refValue.value!.service6Cd = data
          ? data.service6Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数6
        refValue.value!.kaisuu6 = data ? { value: data.kaisuu6 } : { value: '' }
        // サービス利用CD7 checkbox
        refValue.value!.service7Cd = data
          ? data.service7Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数7
        refValue.value!.kaisuu7 = data ? { value: data.kaisuu7 } : { value: '' }
        // サービス利用CD8 checkbox
        refValue.value!.service8Cd = data
          ? data.service8Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数8
        refValue.value!.kaisuu8 = data ? { value: data.kaisuu8 } : { value: '' }
        // サービス利用CD9 checkbox
        refValue.value!.service9Cd = data
          ? data.service9Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数9
        refValue.value!.kaisuu9 = data ? { value: data.kaisuu9 } : { value: '' }
        // サービス利用CD10 checkbox
        refValue.value!.service10Cd = data
          ? data.service10Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数10
        refValue.value!.kaisuu10 = data ? { value: data.kaisuu10 } : { value: '' }
        // サービス利用CD11 checkbox
        refValue.value!.service11Cd = data
          ? data.service11Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数11
        refValue.value!.kaisuu11 = data ? { value: data.kaisuu11 } : { value: '' }
        // サービス利用CD12 checkbox
        refValue.value!.service12Cd = data
          ? data.service12Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数12
        refValue.value!.kaisuu12 = data ? { value: data.kaisuu12 } : { value: '' }
        // サービス利用CD13 checkbox
        refValue.value!.service13Cd = data
          ? data.service13Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数13
        refValue.value!.kaisuu13 = data ? { value: data.kaisuu13 } : { value: '' }
        // サービス利用CD14 checkbox
        refValue.value!.service14Cd = data
          ? data.service14Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        //   改修ありなし
        refValue.value!.kaishuUmu = data ? data.kaishuUmu : ''
        // サービス利用CD15 checkbox
        refValue.value!.service15Cd = data
          ? data.service15Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 特別給付
        refValue.value!.memo1Knj = data ? { value: data.memo1Knj } : { value: '' }
        // サービス利用CD16 checkbox
        refValue.value!.service16Cd = data
          ? data.service16Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 給付外サービス
        refValue.value!.memo2Knj = data ? { value: data.memo2Knj } : { value: '' }
        // サービス利用CD17 checkbox
        refValue.value!.service17Cd = data
          ? data.service17Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数17
        refValue.value!.kaisuu17 = data ? { value: data.kaisuu17 } : { value: '' }
        // サービス利用CD18 checkbox
        refValue.value!.service18Cd = data
          ? data.service18Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数18
        refValue.value!.kaisuu18 = data ? { value: data.kaisuu18 } : { value: '' }
        // サービス利用CD19 checkbox
        refValue.value!.service19Cd = data
          ? data.service19Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数19
        refValue.value!.kaisuu19 = data ? { value: data.kaisuu19 } : { value: '' }
        // サービス利用CD20 checkbox
        refValue.value!.service20Cd = data
          ? data.service20Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数20
        refValue.value!.kaisuu20 = data ? { value: data.kaisuu20 } : { value: '' }
        // サービス利用CD21 checkbox
        refValue.value!.service21Cd = data
          ? data.service21Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数21
        refValue.value!.kaisuu21 = data ? { value: data.kaisuu21 } : { value: '' }
        // サービス利用CD22 checkbox
        refValue.value!.service22Cd = data
          ? data.service22Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数22
        refValue.value!.kaisuu22 = data ? { value: data.kaisuu22 } : { value: '' }
        // サービス利用CD23 checkbox
        refValue.value!.service23Cd = data
          ? data.service23Cd === Or11207Const.checkBox_on
            ? true
            : false
          : false
        // 利用回数23
        refValue.value!.kaisuu23 = data ? { value: data.kaisuu23 } : { value: '' }
        // 利用施設種別
        refValue.value!.shisetsuShu = data ? data.shisetsuShu : ''
        // 施設名
        refValue.value!.shisetuKnj = data ? { value: data.shisetuKnj } : { value: '' }
        // 郵便番号
        refValue.value!.shisetsuZip = data ? data.shisetsuZip : ''
        // 住所
        refValue.value!.shisetsuAddressKnj = data
          ? { value: data.shisetsuAddressKnj }
          : { value: '' }
        // 電話番号
        refValue.value!.shisetsuTel = data ? { value: data.shisetsuTel } : { value: '' }
        // 内容特記事項 textarea
        refValue.value!.memoKnj.value = data ? data.memoKnj : ''
        // 家族状況
        refValue.value!.kazokuJokyo = data ? data.kazokuJokyo : ''
      }
    }
    const cpnTucCscList = Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]
      .cpnTucCscList
    const jisshiDateYmd = cpnTucCscList![0]?.jisshiDateYmd?.replaceAll('/', '')
    // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2018/04/01”)の場合、
    if (
      cpnTucCscList![0]?.dmyCho === Or11207Const.dmyCho_4 &&
      Number(jisshiDateYmd) < Or11207Const.ymd_20180401
    ) {
      if (CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_3) !== undefined)
        localOneway.or55080Oneway.radioItems = CmnSystemCodeRepository.filter(
          CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_3
        ) as radioItemsList[]
    } else {
      // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2018/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、
      if (CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_4) !== undefined)
        localOneway.or55080Oneway.radioItems = CmnSystemCodeRepository.filter(
          CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_4
        ) as radioItemsList[]
    }
    // TODO
    localOneway.or55080Oneway.radioItems = CmnSystemCodeRepository.filter(
      CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_3
    ) as radioItemsList[]
  }

  useScreenStore().setCpTwoWay({
    cpId: Or11207Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/**
 * 複写pinia取得
 */
const getDate4Copy = () => {
  // 調査対象者との関係
  // 続柄リスト
  if (Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData) {
    if (Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]?.gaiList) {
      const zokugaraList = Or32374Logic.data.get(props.onewayModelValue.copyParentId)?.gaiList[0]
        .zokugaraList
      if (zokugaraList) {
        localOneway.mo00040Oneway4.items = []
        localOneway.mo00040Oneway4.items?.push(
          ...zokugaraList.map((item) => {
            return { title: item.zokugaraKnj, value: item.zcode }
          })
        )
      }
    }
  }
  // 調査票改訂フラグ
  const ninteiFlg = Or32374Logic.data.get(props.onewayModelValue.copyParentId)?.ninteiFlg
  local.dmyCho = ninteiFlg ?? ''
  // 調査票リスト．調査票改訂フラグ＝４の場合：
  if (ninteiFlg === Or11207Const.dmyCho_4) {
    // "施設利用" と表示
    localOneway.row_title_3_2.value = t('label.investigator-recorder-title3-row2-title2')
    // 特記事項セクション
    localOneway.row_large_title_4_1.value = t('label.investigator-recorder-title4-1-1')
    localOneway.row_large_title_4_2.value = t('label.investigator-recorder-title4-1-2')
  } else {
    // 調査票リスト．調査票改訂フラグ＝5の場合：
    // "施設等利用" と表示
    localOneway.row_title_3_2.value = t('label.investigator-recorder-title3-row2-title1')
    // 特記事項セクション
    localOneway.row_large_title_4_1.value = t('label.investigator-recorder-title4-2-1')
    localOneway.row_large_title_4_2.value = t('label.investigator-recorder-title4-2-2')
  }
  const cpnTucCscList = Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]
    .cpnTucCscList
  const jisshiDateYmd = cpnTucCscList![0]?.jisshiDateYmd?.replaceAll('/', '')
  const data = Or32374Logic.data.get(props.onewayModelValue.copyParentId)?.gaiList[0]?.gssList[0]
  // 要介護度見込select
  refValue.value!.yokaiKbn1 = data ? { modelValue: data.yokaiKbn1 } : { modelValue: '' }
  // 要介護度確定
  refValue.value!.yokaiKbn2 = data ? { modelValue: data.yokaiKbn2 } : { modelValue: '' }
  // 要介護度認定日date
  refValue.value!.ninteiYmd = data ? { value: data.ninteiYmd } : { value: '' }
  // 認定申請日
  refValue.value!.ninteiShinseiYmd = data ? { value: data.ninteiShinseiYmd } : { value: '' }
  // 記入日
  localOneway.createYmd.value = data ? data.createYmd : ''
  // 実施場所
  refValue.value!.whereCd = data ? data.whereCd : ''
  // 実施場所その他text
  refValue.value!.whereMemoKnj = data ? { value: data.whereMemoKnj } : { value: '' }
  // 過去の認定
  refValue.value!.oldNintei = data ? data.oldNintei : ''
  // 前回認定
  refValue.value!.oldNinteiYmd = data ? { value: data.oldNinteiYmd } : { value: '' }
  // 前回認定結果
  refValue.value!.oldNinteiDo1 = data ? data.oldNinteiDo1 : ''
  // 要支援プルダウン
  if (refValue.value!.oldNinteiDo1 === Or11207Const.oldNinteiDo_2) {
    refValue.value!.oldNinteiDo3 = data ? { modelValue: data.oldNinteiDo3 } : { modelValue: '' }
  } else {
    refValue.value!.oldNinteiDo3 = data ? { modelValue: '' } : { modelValue: '' }
  }
  // 要介護プルダウン
  if (refValue.value!.oldNinteiDo1 === Or11207Const.oldNinteiDo_3) {
    refValue.value!.oldNinteiDo2 = data ? { modelValue: data.oldNinteiDo3 } : { modelValue: '' }
  } else {
    refValue.value!.oldNinteiDo2 = data ? { modelValue: '' } : { modelValue: '' }
  }
  // 氏名
  refValue.value!.renrakuNameKnj = data ? { value: data.renrakuNameKnj } : { value: '' }
  // 調査対象者との関係
  refValue.value!.kankeiId = data ? { modelValue: data.kankeiId } : { modelValue: '' }
  // 電話
  refValue.value!.renrakuTel = data ? { value: data.renrakuTel } : { value: '' }
  // 郵便番号
  refValue.value!.renrakuZip = data ? data.renrakuZip : ''
  // 住所 textarea
  refValue.value!.addressKnj.value = data ? data.addressKnj : ''

  // 調査票改訂フラグ
  if (ninteiFlg === Or11207Const.dmyCho_4 && Number(jisshiDateYmd) < Or11207Const.ymd_20150401) {
    // (調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2015/04/01”)の場合、”(介護予防)訪問介護(ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ)”を表示する
    // サービス利用CD1
    localOneway.service1CdName = t('label.certification-survey-service-cd-name1')
    // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2015/04/01”)の場合、”(介護予防)短期入所生活介護(特養等)”を表示する
    // サービス利用CD9
    localOneway.service9CdName = t('label.certification-survey-service-cd-name2')
    // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2015/04/01”)の場合、”複合型サービス”を表示する
    // サービス利用CD23
    localOneway.service23CdName = t('label.certification-survey-service-cd-name3')
  } else if (
    (ninteiFlg === Or11207Const.dmyCho_4 && Number(jisshiDateYmd) >= Or11207Const.ymd_20180401) ||
    ninteiFlg === Or11207Const.dmyCho_5
  ) {
    // (調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2018/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、”訪問介護(ﾎｰﾑﾍﾙﾌﾟ)･訪問型ｻｰﾋﾞｽ'”を表示する
    // サービス利用CD1
    localOneway.service1CdName = t('label.certification-survey-service-cd-name4')
    // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2018/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、”（介護予防）短期入所療養介護（療養ｼｮｰﾄ）”を表示する
    // サービス利用CD10
    localOneway.service10CdName = t('label.certification-survey-service-cd-name5')
    // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2018/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、利用施設種別4リスト.コード内容を表示する
    // 施設等利用利用施設種別4
    localOneway.or55080Oneway = localOneway.or55080Oneway1
  } else if (
    ninteiFlg === Or11207Const.dmyCho_4 &&
    Number(jisshiDateYmd) >= Or11207Const.ymd_20150401 &&
    Number(jisshiDateYmd) < Or11207Const.ymd_20180401
  ) {
    // (調査票リスト．調査票改訂フラグ＝４且つ”2015/04/01”<=画面.実施日<”2018/04/01”)の場合、”(介護予防)訪問介護(ﾎｰﾑﾍﾙﾌﾟ)･訪問型ｻｰﾋﾞｽ”を表示する
    // サービス利用CD1
    localOneway.service1CdName = t('label.certification-survey-service-cd-name6')
  }

  if (
    (ninteiFlg === Or11207Const.dmyCho_4 && Number(jisshiDateYmd) >= Or11207Const.ymd_20150401) ||
    ninteiFlg === Or11207Const.dmyCho_5
  ) {
    // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2015/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、”(介護予防)短期入所生活介護(ショートステイ)”を表示する
    // サービス利用CD9
    localOneway.service9CdName = t('label.certification-survey-service-cd-name7')
    // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2015/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、”看護小規模多機能型居宅介護”を表示する
    // サービス利用CD23
    localOneway.service23CdName = t('label.certification-survey-service-cd-name8')
  }
  if (ninteiFlg === Or11207Const.dmyCho_4 && Number(jisshiDateYmd) < Or11207Const.ymd_20180401) {
    // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2018/04/01”)の場合、”(介護予防)短期入所療養介護(老健・診療所)”を表示する
    // サービス利用CD10
    localOneway.service10CdName = t('label.certification-survey-service-cd-name9')
    // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2018/04/01”)の場合、利用施設種別3リスト.コード内容を表示する
    // 施設等利用利用施設種別3
    localOneway.or55080Oneway = localOneway.or55080Oneway2
  }
  // サービス利用CD1 checkbox
  refValue.value!.service1Cd = data
    ? data.service1Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数1
  refValue.value!.kaisuu1 = data ? { value: data.kaisuu1 } : { value: '' }
  // サービス利用CD2 checkbox
  refValue.value!.service2Cd = data
    ? data.service2Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数2
  refValue.value!.kaisuu2 = data ? { value: data.kaisuu2 } : { value: '' }
  // サービス利用CD3 checkbox
  refValue.value!.service3Cd = data
    ? data.service3Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数3
  refValue.value!.kaisuu3 = data ? { value: data.kaisuu3 } : { value: '' }
  // サービス利用CD4 checkbox
  refValue.value!.service4Cd = data
    ? data.service4Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数4
  refValue.value!.kaisuu4 = data ? { value: data.kaisuu4 } : { value: '' }
  // サービス利用CD5 checkbox
  refValue.value!.service5Cd = data
    ? data.service5Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数5
  refValue.value!.kaisuu5 = data ? { value: data.kaisuu5 } : { value: '' }
  // サービス利用CD6 checkbox
  refValue.value!.service6Cd = data
    ? data.service6Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数6
  refValue.value!.kaisuu6 = data ? { value: data.kaisuu6 } : { value: '' }
  // サービス利用CD7 checkbox
  refValue.value!.service7Cd = data
    ? data.service7Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数7
  refValue.value!.kaisuu7 = data ? { value: data.kaisuu7 } : { value: '' }
  // サービス利用CD8 checkbox
  refValue.value!.service8Cd = data
    ? data.service8Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数8
  refValue.value!.kaisuu8 = data ? { value: data.kaisuu8 } : { value: '' }
  // サービス利用CD9 checkbox
  refValue.value!.service9Cd = data
    ? data.service9Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数9
  refValue.value!.kaisuu9 = data ? { value: data.kaisuu9 } : { value: '' }
  // サービス利用CD10 checkbox
  refValue.value!.service10Cd = data
    ? data.service10Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数10
  refValue.value!.kaisuu10 = data ? { value: data.kaisuu10 } : { value: '' }
  // サービス利用CD11 checkbox
  refValue.value!.service11Cd = data
    ? data.service11Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数11
  refValue.value!.kaisuu11 = data ? { value: data.kaisuu11 } : { value: '' }
  // サービス利用CD12 checkbox
  refValue.value!.service12Cd = data
    ? data.service12Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数12
  refValue.value!.kaisuu12 = data ? { value: data.kaisuu12 } : { value: '' }
  // サービス利用CD13 checkbox
  refValue.value!.service13Cd = data
    ? data.service13Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数13
  refValue.value!.kaisuu13 = data ? { value: data.kaisuu13 } : { value: '' }
  // サービス利用CD14 checkbox
  refValue.value!.service14Cd = data
    ? data.service14Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  //   改修ありなし
  refValue.value!.kaishuUmu = data ? data.kaishuUmu : ''
  // サービス利用CD15 checkbox
  refValue.value!.service15Cd = data
    ? data.service15Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 特別給付
  refValue.value!.memo1Knj = data ? { value: data.memo1Knj } : { value: '' }
  // サービス利用CD16 checkbox
  refValue.value!.service16Cd = data
    ? data.service16Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 給付外サービス
  refValue.value!.memo2Knj = data ? { value: data.memo2Knj } : { value: '' }
  // サービス利用CD17 checkbox
  refValue.value!.service17Cd = data
    ? data.service17Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数17
  refValue.value!.kaisuu17 = data ? { value: data.kaisuu17 } : { value: '' }
  // サービス利用CD18 checkbox
  refValue.value!.service18Cd = data
    ? data.service18Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数18
  refValue.value!.kaisuu18 = data ? { value: data.kaisuu18 } : { value: '' }
  // サービス利用CD19 checkbox
  refValue.value!.service19Cd = data
    ? data.service19Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数19
  refValue.value!.kaisuu19 = data ? { value: data.kaisuu19 } : { value: '' }
  // サービス利用CD20 checkbox
  refValue.value!.service20Cd = data
    ? data.service20Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数20
  refValue.value!.kaisuu20 = data ? { value: data.kaisuu20 } : { value: '' }
  // サービス利用CD21 checkbox
  refValue.value!.service21Cd = data
    ? data.service21Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数21
  refValue.value!.kaisuu21 = data ? { value: data.kaisuu21 } : { value: '' }
  // サービス利用CD22 checkbox
  refValue.value!.service22Cd = data
    ? data.service22Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数22
  refValue.value!.kaisuu22 = data ? { value: data.kaisuu22 } : { value: '' }
  // サービス利用CD23 checkbox
  refValue.value!.service23Cd = data
    ? data.service23Cd === Or11207Const.checkBox_on
      ? true
      : false
    : false
  // 利用回数23
  refValue.value!.kaisuu23 = data ? { value: data.kaisuu23 } : { value: '' }
  // 利用施設種別
  refValue.value!.shisetsuShu = data ? data.shisetsuShu : ''
  // 施設名
  refValue.value!.shisetuKnj = data ? { value: data.shisetuKnj } : { value: '' }
  // 郵便番号
  refValue.value!.shisetsuZip = data ? data.shisetsuZip : ''
  // 住所
  refValue.value!.shisetsuAddressKnj = data ? { value: data.shisetsuAddressKnj } : { value: '' }
  // 電話番号
  refValue.value!.shisetsuTel = data ? { value: data.shisetsuTel } : { value: '' }
  // 内容特記事項 textarea
  refValue.value!.memoKnj.value = data ? data.memoKnj : ''
  // 家族状況
  refValue.value!.kazokuJokyo = data ? data.kazokuJokyo : ''

  if (Or30055Logic.data.get(props.onewayModelValue.parentId)) {
    const cpnTucCscList = Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]
      .cpnTucCscList
    const jisshiDateYmd = Or32374Logic.data
      .get(props.onewayModelValue.copyParentId)
      ?.jisshiDateYmd?.replaceAll('/', '')
    // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日＜”2018/04/01”)の場合、
    if (
      cpnTucCscList![0]?.dmyCho === Or11207Const.dmyCho_4 &&
      Number(jisshiDateYmd) < Or11207Const.ymd_20180401
    ) {
      if (CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_3) !== undefined)
        localOneway.or55080Oneway.radioItems = CmnSystemCodeRepository.filter(
          CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_3
        ) as radioItemsList[]
    } else {
      if (CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_4) !== undefined)
        // ・(調査票リスト．調査票改訂フラグ＝４且つ調査票リスト.実施日>=”2018/04/01”)或いは(調査票リスト．調査票改訂フラグ＝5)の場合、
        localOneway.or55080Oneway.radioItems = CmnSystemCodeRepository.filter(
          CmnMCdKbnId.M_CD_KBN_ID_FACILITY_TYPE_4
        ) as radioItemsList[]
    }
  }
}

/**
 * 保存
 */
const save = async () => {
  const data = Or30055Logic.data.get(props.onewayModelValue.parentId)
  const cpnTucCscList = [
    {
      // 計画期間ID
      sc1Id: data?.kikanInfo[0]?.sc1Id,
      /** 事業者ID */
      svjigyoId: systemCommonsStore.getSvJigyoId ?? '1',
      /** 法人ID */
      houjinId: systemCommonsStore.getHoujinId ?? '1',
      /** 利用者ID */
      userId: systemCommonsStore.getUserId ?? '1',
      /** 施設ID */
      shisetuId: systemCommonsStore.getShisetuId ?? '1',
      // 記入者コード
      chkShokuId: data?.rirekiOutData[0]?.cpnTucCscList[0]?.chkShokuId,
      // 実施日
      jisshiDateYmd: data?.rirekiOutData[0]?.cpnTucCscList[0]?.jisshiDateYmd,
      // 調査票改訂フラグ
      ninteiFlg:
        data?.rirekiOutData[0]?.cpnTucCscList[0]?.cschId === '0'
          ? props.onewayModelValue.dmyCho
          : data?.rirekiOutData[0]?.cpnTucCscList[0]?.dmyCho,
      // 調査票ID
      cschId: data?.rirekiOutData[0]?.cpnTucCscList[0]?.cschId,
      // 医師意見書ID
      ikenshoId: data?.rirekiOutData[0]?.cpnTucCscList[0]?.ikenshoId,
      // 更新回数
      modifiedCnt: data?.rirekiOutData[0]?.cpnTucCscList[0]?.modifiedCnt,
      // 履歴番号
      rirekiNo: data?.rirekiOutData[0]?.rirekiNo,
    },
  ]
  const cpnTucCsc12List = [
    {
      // 地域コード
      chiikiCd: refValue.value!.chiikiCd,
      /**
       * 調査対象者コード
       */
      taishoushaCd: refValue.value!.taishoushaCd,
      /**
       * 実施場所
       */
      whereCd: refValue.value!.whereCd,
      /**
       * 実施場所その他
       */
      whereMemoKnj: refValue.value!.whereMemoKnj.value,
      /**
       * 過去の認定
       */
      oldNintei: refValue.value!.oldNintei,
      /**
       * 前回認定日
       */
      oldNinteiYmd: refValue.value!.oldNinteiYmd.value,
      /**
       * 前回認定結果
       */
      oldNinteiDo1: refValue.value!.oldNinteiDo1,
      /**
       * 前回認定結果（要介護度）
       */
      oldNinteiDo2: refValue.value!.oldNinteiDo2.modelValue,
      /**
       * 関係者連番
       */
      kankeiId: refValue.value!.kankeiId.modelValue,
      /**
       * 連絡先氏名
       */
      renrakuNameKnj: refValue.value!.renrakuNameKnj.value,
      /**
       * 連絡先続柄
       */
      renrakuZcode: refValue.value!.renrakuZcode,
      /**
       * 連絡先住所
       */
      addressKnj: refValue.value!.addressKnj.value,
      /**
       * 連絡先〒
       */
      renrakuZip: refValue.value!.renrakuZip,
      /**
       * 連絡先ＴＥＬ
       */
      renrakuTel: refValue.value!.renrakuTel.value,
      /**
       * 県コード
       */
      kencode: refValue.value!.kencode,
      /**
       * 市町村コード
       */
      citycode: refValue.value!.citycode,
      /**
       * 地区コード
       */
      areacode: refValue.value!.areacode,
      /**
       * 障害老人ADL
       */
      adl1Id: refValue.value!.adl1Id,
      /**
       * 痴呆老人ADL
       */
      adl2Id: refValue.value!.adl2Id,
      /**
       * 利用施設種別
       */
      shisetsuShu: refValue.value!.shisetsuShu,
      /**
       * 利用施設ＩＤ
       */
      shisetsuId: refValue.value!.shisetsuId,
      /**
       * 施設名
       */
      shisetuKnj: refValue.value!.shisetuKnj.value,
      /**
       * 内容・特記事項
       */
      memoKnj: refValue.value!.memoKnj.value,
      /**
       * 要介護状態見込み
       */
      yokaiKbn1: refValue.value!.yokaiKbn1.modelValue,
      /**
       * 要介護状態確定
       */
      yokaiKbn2: refValue.value!.yokaiKbn2.modelValue,
      /**
       * 認定年月日
       */
      ninteiYmd: refValue.value!.ninteiYmd.value,
      /**
       * サービス事業者ID
       */
      svJigyoId: refValue.value!.svJigyoId,
      /**
       * 認定申請日
       */
      ninteiShinseiYmd: refValue.value!.ninteiShinseiYmd.value,
      /**
       * 利用施設名
       */
      shisetsuNameKnj: refValue.value!.shisetsuNameKnj,
      /**
       * 郵便番号
       */
      shisetsuZip: refValue.value!.shisetsuZip,
      /**
       * 施設住所
       */
      shisetsuAddressKnj: refValue.value!.shisetsuAddressKnj.value,
      /**
       * 電話番号
       */
      shisetsuTel: refValue.value!.shisetsuTel.value,
      /**
       * 前回認定結果（要支援度）
       */
      oldNinteiDo3: refValue.value!.oldNinteiDo3.modelValue,
      /**
       * 更新回数(概況調査)
       */
      modifiedCnt: data?.rirekiOutData[0]?.gaiList[0]?.gssList[0]?.modifiedCnt12,
    },
  ]
  const cpnTucCsc22List = [
    {
      /**
       * サービス利用ＣＤ１
       */
      service1Cd:
        refValue.value!.service1Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ２
       */
      service2Cd:
        refValue.value!.service2Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ３
       */
      service3Cd:
        refValue.value!.service3Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ４
       */
      service4Cd:
        refValue.value!.service4Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ５
       */
      service5Cd:
        refValue.value!.service5Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ６
       */
      service6Cd:
        refValue.value!.service6Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ７
       */
      service7Cd:
        refValue.value!.service7Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ８
       */
      service8Cd:
        refValue.value!.service8Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ９
       */
      service9Cd:
        refValue.value!.service9Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ１０
       */
      service10Cd:
        refValue.value!.service10Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ１１
       */
      service11Cd:
        refValue.value!.service11Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ１２
       */
      service12Cd:
        refValue.value!.service12Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ１３
       */
      service13Cd:
        refValue.value!.service13Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ１４
       */
      service14Cd:
        refValue.value!.service14Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ１５
       */
      service15Cd:
        refValue.value!.service15Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ１６
       */
      service16Cd:
        refValue.value!.service16Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * 利用回数１
       */
      kaisuu1: refValue.value!.kaisuu1.value,
      /**
       * 利用回数２
       */
      kaisuu2: refValue.value!.kaisuu2.value,
      /**
       * 利用回数３
       */
      kaisuu3: refValue.value!.kaisuu3.value,
      /**
       * 利用回数４
       */
      kaisuu4: refValue.value!.kaisuu4.value,
      /**
       * 利用回数５
       */
      kaisuu5: refValue.value!.kaisuu5.value,
      /**
       * 利用回数６
       */
      kaisuu6: refValue.value!.kaisuu6.value,
      /**
       * 利用回数７
       */
      kaisuu7: refValue.value!.kaisuu7.value,
      /**
       * 利用回数８
       */
      kaisuu8: refValue.value!.kaisuu8.value,
      /**
       * 利用回数９
       */
      kaisuu9: refValue.value!.kaisuu9.value,
      /**
       * 利用回数１０
       */
      kaisuu10: refValue.value!.kaisuu10.value,
      /**
       * 利用回数１１
       */
      kaisuu11: refValue.value!.kaisuu11.value,
      /**
       * 利用回数１２
       */
      kaisuu12: refValue.value!.kaisuu12.value,
      /**
       * 利用回数１３
       */
      kaisuu13: refValue.value!.kaisuu13.value,
      /**
       * 改修あり・なし
       */
      kaishuUmu: refValue.value!.kaishuUmu,
      /**
       * 特別給付
       */
      memo1Knj: refValue.value!.memo1Knj.value,
      /**
       * 給付外サービス
       */
      memo2Knj: refValue.value!.memo2Knj.value,
      /**
       * サービス利用ＣＤ１７
       */
      service17Cd:
        refValue.value!.service17Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ１８
       */
      service18Cd:
        refValue.value!.service18Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ１９
       */
      service19Cd:
        refValue.value!.service19Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ２０
       */
      service20Cd:
        refValue.value!.service20Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * 利用回数１７
       */
      kaisuu17: refValue.value!.kaisuu17.value,
      /**
       * 利用回数１８
       */
      kaisuu18: refValue.value!.kaisuu18.value,
      /**
       * 利用回数１９
       */
      kaisuu19: refValue.value!.kaisuu19.value,
      /**
       * 利用回数２０
       */
      kaisuu20: refValue.value!.kaisuu20.value,
      /**
       * 利用回数２１
       */
      kaisuu21: refValue.value!.kaisuu21.value,
      /**
       * サービス利用ＣＤ２1
       */
      service21Cd:
        refValue.value!.service21Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ２2
       */
      service22Cd:
        refValue.value!.service22Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * サービス利用ＣＤ２3
       */
      service23Cd:
        refValue.value!.service23Cd === true ? Or11207Const.checkBox_on : Or11207Const.checkBox_off,
      /**
       * 利用回数２2
       */
      kaisuu22: refValue.value!.kaisuu22.value,
      /**
       * 利用回数２3
       */
      kaisuu23: refValue.value!.kaisuu23.value,
      /**
       * 更新回数(サービス利用)
       */
      modifiedCnt: data?.rirekiOutData[0]?.gaiList[0]?.gssList[0]?.modifiedCnt22,
    },
  ]
  // 概況調査画面情報を保存する。
  const inputData: GeneralSituationSurveyDataUpdateInEntity = {
    cpnTucCscList: cpnTucCscList,
    kinouId: systemCommonsStore.getFunctionId ?? '1',
    deleteFlg:
      Or30055Logic.delFlg.get(props.onewayModelValue.parentId)?.delFlg ?? Or30055Const.DELFLG_TYPE0,
    cpnTucCsc12List: cpnTucCsc12List,
    cpnTucCsc22List: cpnTucCsc22List,
  }
  const resData: GeneralSituationSurveyDataUpdateOutEntity = await ScreenRepository.update(
    'generalSituationSurveyDataUpdate',
    inputData
  )
  if (resData?.data) {
    Or30055Logic.addFlg.set({
      uniqueCpId: props.onewayModelValue.parentId,
      state: { addFlg: false },
    })
    Or30055Logic.tabInfo.set({
      uniqueCpId: props.onewayModelValue.parentId,
      state: {
        tabId: Or30055Logic.tabInfo.get(props.onewayModelValue.parentId)?.tabId,
        state: Or30055Const.GET_DATA_AGAIN,
      },
    })
  }
}

/**
 *  ボタン押下時の処理(Or27626)
 */
function onClickOr27626() {
  // Or27626のダイアログ開閉状態を更新する
  Or27626Logic.state.set({
    uniqueCpId: or27626.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr27626 = computed(() => {
  // Or27626 cks_flg=1 のダイアログ開閉状態
  return Or27626Logic.state.get(or27626.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or27626)
 */
function onClickOr26323() {
  // Or26323のダイアログ開閉状態を更新する
  Or26323Logic.state.set({
    uniqueCpId: or26323.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr26323 = computed(() => {
  // Or26323 cks_flg=1 のダイアログ開閉状態
  return Or26323Logic.state.get(or26323.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 施設選択画面
 *
 * @param data - 施設選択画面
 */
const or27626Change = (data: Or27626Type) => {
  // 施設名
  refValue.value!.shisetuKnj = { value: data.facilityName }
  // 電話
  refValue.value!.shisetsuTel = { value: data.telNumber }
  // 郵便番号
  refValue.value!.shisetsuZip = data.zipCode
  // 住所
  refValue.value!.shisetsuAddressKnj = { value: data.addRess }
  /** 調査票施設種別*/
  refValue.value!.shisetsuShu = data.surveyFacilityKind
}

/**
 * 主治医意見書選択アイコンボタン押下
 *
 * @param data - 主治医意見書選択アイコンボタン押下
 */
const or26323Change = (data: Or26323Type2) => {
  // 記入日
  localOneway.createYmd.value = data.createYmd
  // 医師意見書ID
  const or30055Date = Or30055Logic.data.get(props.onewayModelValue.parentId)!
  if (or30055Date) {
    or30055Date.rirekiOutData[0].cpnTucCscList[0].ikenshoId = data.rirekiId
    or30055Date.rirekiOutData[0].gaiList[0].gssList[0].createYmd = data.createYmd
  }
  Or30055Logic.data.set({
    uniqueCpId: props.onewayModelValue.parentId,
    state: or30055Date,
  })
}

function onClickOr01271() {
  // 認定フラグ
  or26416OnewayType.value.ninteiFlg =
    Or32374Logic.data.get(props.onewayModelValue.copyParentId)?.ninteiFlg ?? '1'
  // 計画期間ID
  or26416OnewayType.value.sc1Id =
    Or30055Logic.data.get(props.onewayModelValue.parentId)?.kikanInfo[0]?.sc1Id ?? '1'
  // 調査票ID
  or26416OnewayType.value.cschId =
    Or30055Logic.data.get(props.onewayModelValue.parentId)?.rirekiOutData[0]?.cpnTucCscList[0]
      ?.cschId ?? '1'
  Or26416Logic.state.set({
    uniqueCpId: or26416.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function onClickOr27349() {
  Or27349Logic.state.set({
    uniqueCpId: or27349.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const showDialogOr27349 = computed(() => {
  return Or27349Logic.state.get(or27349.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01099_認定情報選択change
 *
 * @param data - data
 */
const or27349Change = (data: NinteiList) => {
  // 要介護度認定日
  refValue.value!.ninteiYmd = { value: data.ninteiYmd }
  // 要介護度確定
  refValue.value!.yokaiKbn2 = { modelValue: data.yokaiKbn }
}

/**
 * 監視
 */
watch(
  () => Or30055Logic.tabInfo.get(props.onewayModelValue.parentId),
  async (newValue) => {
    if (newValue) {
      // 初期化
      if (
        newValue.tabId === Or30055Const.TAB_1 &&
        (newValue.state === Or30055Const.GET_DATA ||
          newValue.state === Or30055Const.CHANGE_HIS ||
          newValue.state === Or30055Const.ADD_INIT)
      ) {
        getDate()
      } else if (
        newValue.tabId === Or30055Const.TAB_1 &&
        newValue.state === Or30055Const.SAVE_BUTTON
      ) {
        // 保存
        await save()
      }
    }
  }
)

/**
 * 複写監視
 */
watch(
  () => Or32374Logic.tabInfo.get(props.onewayModelValue.copyParentId),
  (newValue) => {
    if (newValue) {
      // 初期化
      if (
        newValue.tabId === Or32374Const.TAB_1 &&
        (newValue.state === Or32374Const.GET_INIT_DATA1 ||
          newValue.state === Or32374Const.GET_INIT_DATA2 ||
          newValue.state === Or32374Const.GET_INIT_DATA3)
      ) {
        getDate4Copy()
      }
    }
  }
)

watch(
  () => Or30055Logic.delFlg.get(props.onewayModelValue.parentId),
  (newValue) => {
    if (newValue) {
      localOneway.delFlg = newValue.delFlg!
    }
  }
)

// ダイアログ表示フラグ
const showDialogOr26416 = computed(() => {
  // Or26416のダイアログ開閉状態
  return Or26416Logic.state.get(or26416.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01271_要介護度の一次判定change
 *
 * @param data - data
 */
const or26416Change = (data: Or26416Type) => {
  // 要介護度確定
  refValue.value!.yokaiKbn2 = { modelValue: data.itiziHantei }
}

/**
 * 「家族等連絡先アイコンボタン」押下
 *
 * @param telCellFlg - 電話携帯フラグ
 */
function onClickOr55476(telCellFlg: string) {
  or55476OneWayType.value.userId = systemCommonsStore.getUserId ?? '1'
  or55476OneWayType.value.telCellFlg = telCellFlg
  or55476OneWayType.value.kinouId = Or11207Const.ID42020001
  or55476OneWayType.value.createYmd = systemCommonsStore.getSystemDate ?? ''
  Or55476Logic.state.set({
    uniqueCpId: or55476.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr55476 = computed(() => {
  // Or55476のダイアログ開閉状態
  return Or55476Logic.state.get(or55476.value.uniqueCpId)?.isOpen ?? false
})

const or55476Change = (info: RelatedPersonSelectResInfo) => {
  // 変数.関係者連番
  refValue.value!.kankeiId = { modelValue: info.id }
  // 画面.氏名
  refValue.value!.renrakuNameKnj = { value: info.nameKnj }
  // 画面.調査対象者との関係選択
  refValue.value!.kankeiId = { modelValue: info.zcode }
  // 画面.住所
  refValue.value!.addressKnj.value = info.addressKnj
  // 画面.連絡先〒
  refValue.value!.renrakuZip = info.zip
  // 画面.電話
  refValue.value!.renrakuTel = { value: info.tel ?? '' }
}

// 施設等利用label click
const shisetsuShuClick = (val: string) => {
  refValue.value!.shisetsuShu = val
}
</script>

<template>
  <div
    v-if="
      (Or30055Logic.data.get(props.onewayModelValue.parentId) != undefined &&
        localOneway.delFlg === Or30055Const.DELFLG_TYPE0) ||
      localOneway.copyFlg === true
    "
    class="bg-color"
  >
    <c-v-row
      no-gutters
      style="justify-content: center"
    >
      <c-v-col cols="12">
        <div
          ref="contentRef"
          class="container"
        >
          <c-v-row
            no-gutters
            class="text-center"
            style="margin-bottom: 16px"
          >
            <!--判定ラベル-->
            <c-v-col cols="auto">
              <base-mo00611
                :oneway-model-value="localOneway.mo00615Oneway1"
                class="mx-1 color0760e6Btn"
                style="margin-top: 28px; margin-left: 0px !important"
                @click="onClickOr01271()"
              />
            </c-v-col>

            <c-v-col
              cols="auto ml-20"
              align-self="center"
            >
              <!-- 要介護度見込 -->
              <base-mo00040
                :model-value="refValue!.yokaiKbn1"
                class="custom-input"
                :oneway-model-value="localOneway.mo00040Oneway"
              />
            </c-v-col>
            <c-v-col
              cols="auto ml-24"
              align-self="center"
            >
              <!-- 要介護度確定 -->
              <g-custom-or-x-0158
                v-model="refValue!.yokaiKbn2"
                class="custom-input"
                :oneway-model-value="localOneway.mo00040Oneway5"
                @on-click-edit-btn="onClickOr27349()"
              ></g-custom-or-x-0158>
            </c-v-col>
            <!-- 要介護度認定日 -->
            <c-v-col cols="auto ml-8px">
              <base-mo00020
                :model-value="refValue!.ninteiYmd"
                class="custom-input"
                :oneway-model-value="localOneway.createDateOneway1"
              />
            </c-v-col>
            <!-- 認定申請日 -->
            <c-v-col cols="auto ml-8px">
              <base-mo00020
                :model-value="refValue!.ninteiShinseiYmd"
                class="custom-input"
                :oneway-model-value="localOneway.createDateOneway2"
              />
            </c-v-col>

            <!--主治医意見書選択-->
            <c-v-col cols="auto ml-24">
              <base-mo00611
                :oneway-model-value="localOneway.mo00611Oneway1"
                class="mx-1 color0760e6Btn"
                style="margin-top: 28px"
                @click="onClickOr26323()"
              >
                {{ t('label.medical-doctor-opinion-selection')
                }}<base-at-icon
                  icon="info fill"
                  style="color: grey; margin-left: 8px"
                ></base-at-icon>
              </base-mo00611>
            </c-v-col>
            <!--記入日-->
            <c-v-col cols="auto ml-4px">
              <base-mo00045
                v-model="localOneway.createYmd"
                class="custom-input"
                :oneway-model-value="localOneway.or55022OnewayEntrydate"
              ></base-mo00045>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutters
            style="
              border-left: thin solid rgb(var(--v-theme-form));
              border-right: thin solid rgb(var(--v-theme-form));
              background-color: #ffffff !important;
            "
          >
            <c-v-col cols="12">
              <!-- Ⅰ調査実施者(記入者) -->
              <c-v-row
                no-gutters
                class="title"
              >
                <c-v-col cols="12">
                  <base-mo01338 :oneway-model-value="localOneway.row_large_title_1"></base-mo01338>
                </c-v-col>
              </c-v-row>
              <!-- 実施場所 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 実施場所left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338 :oneway-model-value="localOneway.row_title_1_1"></base-mo01338>
                    </c-v-col>
                    <!-- 実施場所right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00039
                        v-model="refValue!.whereCd"
                        class="ml-neg8 mr_2_div"
                        :oneway-model-value="localOneway.mo00039Oneway"
                      >
                        <base-at-radio
                          v-for="item in localOneway.or55021Oneway.radioItems"
                          :key="item.id"
                          :name="item.label"
                          :radio-label="item.label"
                          :value="item.value"
                          style="min-width: 100px; width: auto; margin-right: 0px !important"
                        />
                      </base-mo00039>
                      <base-mo00045
                        v-model="refValue!.whereMemoKnj"
                        :oneway-model-value="localOneway.or55022Oneway"
                      ></base-mo00045>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- Ⅱ調査対象者 -->
              <c-v-row
                no-gutters
                class="title"
              >
                <c-v-col cols="12">
                  <base-mo01338 :oneway-model-value="localOneway.row_large_title_2"></base-mo01338>
                </c-v-col>
              </c-v-row>
              <!-- 過去の認定 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 過去の認定left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338 :oneway-model-value="localOneway.row_title_2_1"></base-mo01338>
                    </c-v-col>
                    <!-- 過去の認定right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <base-mo00039
                        v-model="refValue!.oldNintei"
                        class="ml-neg8"
                        :oneway-model-value="localOneway.mo00039Oneway"
                      >
                        <base-at-radio
                          v-for="item in localOneway.or55023Oneway.radioItems"
                          :key="item.id"
                          :name="item.label"
                          :radio-label="item.label"
                          :value="item.value"
                          style="min-width: 100px; width: auto; margin-right: 0px !important"
                        />
                      </base-mo00039>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- 前回認定 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 前回認定left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338 :oneway-model-value="localOneway.row_title_2_2"></base-mo01338>
                    </c-v-col>
                    <!-- 前回認定right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <base-mo00020
                        :model-value="refValue!.oldNinteiYmd"
                        :oneway-model-value="localOneway.createDateOneway3"
                      />
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- 前回認定結果 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 前回認定結果left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338 :oneway-model-value="localOneway.row_title_2_3"></base-mo01338>
                    </c-v-col>
                    <!-- 前回認定結果right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <base-mo00039
                        v-model="refValue!.oldNinteiDo1"
                        class="ml-neg8 mr_2_div"
                        :oneway-model-value="localOneway.mo00039Oneway"
                      >
                        <div
                          v-for="(item, index) in localOneway.or55025Oneway.radioItems"
                          :key="item.id"
                          style="display: -webkit-inline-flex"
                        >
                          <base-at-radio
                            :key="item.id"
                            :name="item.label"
                            :radio-label="item.label"
                            :value="item.value"
                            style="min-width: 100px; width: auto; margin-right: 0px !important"
                          />
                          <!-- 要支援プルダウン -->
                          <base-mo00040
                            v-if="index === 1"
                            v-model="refValue!.oldNinteiDo3"
                            :oneway-model-value="localOneway.mo00040Oneway2"
                          />
                          <!-- 要介護プルダウン -->
                          <base-mo00040
                            v-if="index === 2"
                            v-model="refValue!.oldNinteiDo2"
                            :oneway-model-value="localOneway.mo00040Oneway3"
                          />
                        </div>
                      </base-mo00039>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- 家族等連絡先 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <c-v-col class="asection_left title_container subTitle_col">
                      <a
                        class="aLink"
                        @click="onClickOr55476('1')"
                        >{{ t('label.investigator-recorder-title2-row4-title') }}</a
                      >
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- 氏名 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 氏名left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.row_title_family_1_1"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 氏名right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <base-mo00045
                        v-model="refValue!.renrakuNameKnj"
                        :oneway-model-value="localOneway.or55028Oneway"
                      ></base-mo00045>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>

              <!-- 調査対象者との関係 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 調査対象者との関係left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.row_title_family_1_2"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 調査対象者との関係right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <base-mo00040
                        v-model="refValue!.kankeiId"
                        :oneway-model-value="localOneway.mo00040Oneway4"
                      />
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>

              <!-- 電話 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 電話left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.row_title_family_1_3"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 電話right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <base-mo00045
                        v-model="refValue!.renrakuTel"
                        :oneway-model-value="localOneway.or550305Oneway"
                      ></base-mo00045>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>

              <!-- 郵便番号 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 郵便番号left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.row_title_family_1_4"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 郵便番号right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <div class="input-field-container d-flex">
                        <base-at-text-field
                          v-model="refValue!.renrakuZip"
                          maxlength="7"
                          class="mo-input-field"
                        >
                          <template #prepend>
                            <label class="icon-edit-btn">{{ t('label.zip-sign-number') }}</label>
                          </template>
                        </base-at-text-field>
                      </div>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>

              <!-- 住所 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 住所left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.row_title_family_1_5"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 住所right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <base-mo00045
                        v-model="refValue!.addressKnj"
                        :oneway-model-value="localOneway.or550411Oneway_address"
                      ></base-mo00045>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>

              <!-- Ⅲ現在受けているサービスの状況についてチェック及び頻度を記入してください。 -->
              <c-v-row
                no-gutters
                class="title"
              >
                <c-v-col cols="12">
                  <c-v-row no-gutters>
                    <base-mo01338
                      :oneway-model-value="localOneway.row_large_title_3"
                    ></base-mo01338>
                  </c-v-row>
                </c-v-col>
              </c-v-row>

              <!-- 在宅利用      [認定調査を行った月のサービス利用回数を記入。(介護予防)福祉用具貸与は調査日時点の､特定(介護予防)福祉用具販売は過去6月の品目数を記載] -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <c-v-col class="asection_left title_container subTitle_col">
                      <base-mo01379
                        style="background-color: transparent"
                        :oneway-model-value="{
                          value: t('label.investigator-recorder-title3-subtitle'),
                          fontWeight: 'bold',
                        }"
                      />
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>

              <!-- サービス利用 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col
                  cols="12"
                  style="background: #ffffff"
                >
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="padding-top: 4px"
                  >
                    <!-- 訪問介護(ﾎｰﾑﾍﾙﾌﾟｻｰﾋﾞｽ) -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service1Cd"
                        :checkbox-label="localOneway.service1CdName"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu1"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_times"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- (介護予防)福祉用具貸与 -->
                    <c-v-col cols="4">
                      <base-at-checkbox
                        v-model="refValue!.service8Cd"
                        :checkbox-label="t('label.lender-for-welfare-equipment')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu8"
                        :oneway-model-value="localOneway.or55055Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.product_category"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- (介護予防)訪問入浴介護 -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service2Cd"
                        :checkbox-label="t('label.visit-bathing-care')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu2"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_times"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 特定(介護予防)福祉用具販売 -->
                    <c-v-col cols="4">
                      <base-at-checkbox
                        v-model="refValue!.service13Cd"
                        :checkbox-label="t('label.specific-nursing-care')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu13"
                        :oneway-model-value="localOneway.or55055Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.product_category"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- (介護予防)訪問看護 -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service3Cd"
                        :checkbox-label="t('label.home-visit-nursing2')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu3"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_times"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 住宅改修 -->
                    <c-v-col cols="4">
                      <base-at-checkbox
                        v-model="refValue!.service14Cd"
                        :checkbox-label="t('label.house-renovation')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col cols="2">
                      <base-mo00039
                        v-model="refValue!.kaishuUmu"
                        :oneway-model-value="localOneway.mo00039Oneway"
                      >
                        <base-at-radio
                          v-for="item in localOneway.or55077Oneway.radioItems"
                          :key="item.id"
                          :name="item.label"
                          :radio-label="item.label"
                          :value="item.value"
                        />
                      </base-mo00039>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- (介護予防)訪問ﾘﾊﾋﾞﾘﾃｰｼｮﾝ -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service4Cd"
                        :checkbox-label="t('label.home-rehabilitation')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu4"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_times"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 夜間対応型訪問介護 -->
                    <c-v-col cols="4">
                      <base-at-checkbox
                        v-model="refValue!.service17Cd"
                        :checkbox-label="t('label.night-visit-care')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu17"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_day"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- (介護予防)居宅療養管理指導 -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service5Cd"
                        :checkbox-label="t('label.home-care-management-guidance')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu5"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_times"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- (介護予防)認知症対応型通所介護 -->
                    <c-v-col cols="4">
                      <base-at-checkbox
                        v-model="refValue!.service18Cd"
                        :checkbox-label="t('label.dementia-correspondence-school-care')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu18"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_day"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- 通所介護(ﾃﾞｲｻｰﾋﾞｽ)･通所型ｻｰﾋﾞｽ -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service6Cd"
                        :checkbox-label="t('label.ambulatory-care')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu6"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_times"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- (介護予防)小規模多機能型居宅介護 -->
                    <c-v-col cols="4">
                      <base-at-checkbox
                        v-model="refValue!.service19Cd"
                        :checkbox-label="t('label.small-scale-multi-functional-residential-care')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu19"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_day"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- (介護予防)通所ﾘﾊﾋﾞﾘﾃｰｼｮﾝ(ﾃﾞｲｹｱ) -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service7Cd"
                        :checkbox-label="t('label.rehabilitation-center')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu7"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_times"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- (介護予防)認知症対応型共同生活介護 -->
                    <c-v-col cols="4">
                      <base-at-checkbox
                        v-model="refValue!.service11Cd"
                        :checkbox-label="t('label.cognitive-impairment-type-of-coliving-are')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu11"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_day"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- (介護予防)短期入所生活介護(ｼｮｰﾄｽﾃｲ) -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service9Cd"
                        :checkbox-label="localOneway.service9CdName"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu9"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_day"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 地域密着型特定施設入居者生活介護 -->
                    <c-v-col cols="4">
                      <base-at-checkbox
                        v-model="refValue!.service20Cd"
                        :checkbox-label="
                          t('label.community-based-specific-facility-residents-living-care')
                        "
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu20"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_day"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- (介護予防)短期入所療養介護(療養ｼｮｰﾄ) -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service10Cd"
                        :checkbox-label="localOneway.service10CdName"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu10"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_day"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 地域密着型介護老人福祉施設入所者生活介護 -->
                    <c-v-col cols="4">
                      <base-at-checkbox
                        v-model="refValue!.service21Cd"
                        :checkbox-label="
                          t('label.regional-intensive-care-for-elderly-welfare-facilities-care')
                        "
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu21"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_day"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- (介護予防)特定施設入居者生活介護 -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service12Cd"
                        :checkbox-label="t('label.specific-facilities-residents-living-care')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu12"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_day"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 定期巡回・随時対応型訪問介護看護 -->
                    <c-v-col cols="4">
                      <base-at-checkbox
                        v-model="refValue!.service22Cd"
                        :checkbox-label="t('label.regular-tours-targeted-visits')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu22"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_times"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- 看護小規模多機能型居宅介護 -->
                    <c-v-col
                      cols="4"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service23Cd"
                        :checkbox-label="localOneway.service23CdName"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col
                      cols="2"
                      style="display: -webkit-inline-box"
                    >
                      <base-mo00045
                        v-model="refValue!.kaisuu23"
                        :oneway-model-value="localOneway.or55034Oneway"
                      ></base-mo00045>
                      <base-mo01338
                        :oneway-model-value="localOneway.label_day"
                        class="right_label"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- 市町村特別給付 -->
                    <c-v-col
                      cols="3"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service15Cd"
                        :checkbox-label="t('label.municipal-special-benefit')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col cols="8">
                      <base-mo00045
                        v-model="refValue!.memo1Knj"
                        :oneway-model-value="localOneway.or55078Oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="asection"
                    style="border-top: 0px"
                  >
                    <!-- 介護保険給付外の在宅ｻｰﾋﾞｽ -->
                    <c-v-col
                      cols="3"
                      class="title_container"
                    >
                      <base-at-checkbox
                        v-model="refValue!.service16Cd"
                        :checkbox-label="t('label.home-care-outside-nursing-care-benefits')"
                        color="key"
                        class="chk-custom"
                      />
                    </c-v-col>
                    <c-v-col cols="8">
                      <base-mo00045
                        v-model="refValue!.memo2Knj"
                        :oneway-model-value="localOneway.or55079Oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- 施設等利用 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <c-v-col class="asection_left title_container subTitle_col">
                      <base-mo01379
                        class="facilities_use_left"
                        :oneway-model-value="{
                          value: t('label.investigator-recorder-title3-row2-title1'),
                          fontWeight: 'bold',
                        }"
                      />
                      <base-mo01379
                        class="facilities_use_right"
                        :oneway-model-value="{
                          value: t('label.abolition-of-facilities'),
                          fontWeight: 'bold',
                        }"
                      />
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 施設等利用 -->
                    <c-v-col cols="12">
                      <c-v-row no-gutters>
                        <c-v-col class="asection_left title_container">
                          <base-mo00039
                            v-model="refValue!.shisetsuShu"
                            :oneway-model-value="localOneway.mo00039Oneway"
                          >
                            <v-checkbox
                              v-for="item in localOneway.or55080Oneway.radioItems"
                              :key="item.value"
                              :value="refValue!.shisetsuShu"
                              :true-value="item.value"
                              :label="item.label"
                              color="key"
                              class="or11207CheckBox"
                            >
                              <template #label>
                                <label
                                  v-if="item.label.indexOf(Or11207Const.FACILITY_TYPE_12) > 0"
                                  style="position: relative; cursor: pointer"
                                  @click="shisetsuShuClick(item.value)"
                                  >{{ item.label.substring(0, item.label.length - 4)
                                  }}<label>{{ Or11207Const.FULL_SPACE3 }}</label
                                  ><label class="facility_type2">{{
                                    Or11207Const.FACILITY_TYPE_12
                                  }}</label></label
                                >
                                <label
                                  v-else-if="item.label.indexOf(Or11207Const.FACILITY_TYPE_1) > 0"
                                  style="position: relative; cursor: pointer"
                                  @click="shisetsuShuClick(item.value)"
                                  >{{ item.label.substring(0, item.label.length - 2)
                                  }}<label>{{ Or11207Const.FULL_SPACE2 }}</label
                                  ><label class="facility_type1">{{
                                    Or11207Const.FACILITY_TYPE_1
                                  }}</label></label
                                >
                                <label
                                  v-else
                                  style="cursor: pointer"
                                  @click="shisetsuShuClick(item.value)"
                                  >{{ item.label }}</label
                                >
                              </template>
                            </v-checkbox>
                          </base-mo00039>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>

              <!-- 施設連絡先 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <c-v-col class="asection_left title_container subTitle_col">
                      <a
                        class="aLink"
                        @click="onClickOr27626()"
                        >{{ t('label.investigator-recorder-title3-row3-title') }}</a
                      >
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>

              <!-- 施設等名 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 施設等名left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.row_title_facilities_1_1"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 施設等名right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <base-mo00045
                        v-model="refValue!.shisetuKnj"
                        :oneway-model-value="localOneway.or55085Oneway"
                      ></base-mo00045>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- 郵便番号 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 郵便番号left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.row_title_family_1_4"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 郵便番号right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <c-v-row no-gutters>
                        <c-v-col cols="3">
                          <div class="input-field-container d-flex">
                            <base-at-text-field
                              v-model="refValue!.shisetsuZip"
                              maxlength="7"
                              class="mo-input-field"
                            >
                              <template #prepend>
                                <label class="icon-edit-btn">{{
                                  t('label.zip-sign-number')
                                }}</label>
                              </template>
                            </base-at-text-field>
                          </div>
                        </c-v-col>
                        <c-v-col>
                          <!-- 電話 -->
                          <base-mo00045
                            v-model="refValue!.shisetsuTel"
                            class="tel"
                            :oneway-model-value="localOneway.or55088Oneway"
                          ></base-mo00045>
                        </c-v-col>
                      </c-v-row>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- 住所 -->
              <c-v-row
                no-gutters
                class="content"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <!-- 住所left -->
                    <c-v-col
                      cols="3"
                      class="asection_left title_container"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.row_title_facilities_1_2"
                      ></base-mo01338>
                    </c-v-col>
                    <!-- 住所right -->
                    <c-v-col
                      cols="9"
                      class="asection_right"
                    >
                      <base-mo00045
                        v-model="refValue!.shisetsuAddressKnj"
                        :oneway-model-value="localOneway.or55087Oneway"
                      ></base-mo00045>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <c-v-row
                v-if="local.dmyCho == Or11207Const.dmyCho_5"
                no-gutters
                class="content"
                style="padding-bottom: 8px; padding-left: 8px"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <c-v-col
                      class="asection_left title_container"
                      style="display: flex"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.contact_for_facilities_describe1"
                      ></base-mo01338>
                      <base-mo01338
                        :oneway-model-value="localOneway.contact_for_facilities_describe2"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>

              <!-- Ⅳ調査対象者の家族状況、調査対象者の居住環境（外出が困難になるほど日常生活に支障となるような環境の有無）、施設等における状況、日常的に使用する機器・器械の有無等について特記すべき事項を記入してください。 -->
              <c-v-row
                no-gutters
                class="title"
              >
                <c-v-col cols="12">
                  <base-mo01338
                    :oneway-model-value="localOneway.row_large_title_4_1"
                  ></base-mo01338>
                  <base-mo01338
                    class="row_large_title_4_2"
                    :oneway-model-value="localOneway.row_large_title_4_2"
                  ></base-mo01338>
                </c-v-col>
              </c-v-row>

              <!-- ※家族状況 -->
              <c-v-row
                no-gutters
                class="content"
                style="padding-bottom: 0px !important"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <c-v-col
                      class="asection_left title_container"
                      style="max-width: 140px !important"
                    >
                      <base-mo01338 :oneway-model-value="localOneway.row_title_4_1"></base-mo01338>
                    </c-v-col>
                    <c-v-col class="asection_right">
                      <base-mo00039
                        v-model="refValue!.kazokuJokyo"
                        :oneway-model-value="localOneway.mo00039Oneway"
                      >
                        <v-checkbox
                          v-for="item in localOneway.or62126Oneway.radioItems"
                          :key="item.id"
                          :value="refValue!.kazokuJokyo"
                          :true-value="item.value"
                          :label="item.label"
                          color="key"
                          class="or11207CheckBox"
                        />
                      </base-mo00039>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutters
                style="padding-left: 47px; margin-top: -14px"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <c-v-col class="asection_left title_container">
                      <base-mo01338
                        :oneway-model-value="localOneway.family_situation_describe"
                      ></base-mo01338>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
              <!-- Ⅳ注記 -->
              <c-v-row
                no-gutters
                class="content"
                style="padding-top: 16px; padding-left: 8px; padding-bottom: 24px"
              >
                <c-v-col cols="12">
                  <c-v-row
                    no-gutters
                    class="asection"
                  >
                    <c-v-col
                      class="asection_left title_container memoKnj"
                      style="padding-right: 32px"
                    >
                      <g-custom-or-x0156
                        v-model="refValue!.memoKnj"
                        :oneway-model-value="localOneway.orX0156Oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
        </div>
      </c-v-col>
    </c-v-row>
  </div>

  <g-custom-or-27626
    v-if="showDialogOr27626"
    v-bind="or27626"
    @update:model-value="or27626Change"
  />
  <g-custom-or-27349
    v-if="showDialogOr27349"
    v-bind="or27349"
    :oneway-model-value="local.startYmd"
    @confirm="or27349Change"
  />
  <g-custom-or-26323
    v-if="showDialogOr26323"
    v-bind="or26323"
    :oneway-model-value="localOneway.or26323Oneway"
    @update:model-value2="or26323Change"
  />

  <g-custom-or-26416
    v-if="showDialogOr26416"
    v-bind="or26416"
    :oneway-model-value="or26416OnewayType"
    @update:model-value2="or26416Change"
  />

  <g-custom-or-55476
    v-if="showDialogOr55476"
    v-bind="or55476"
    :oneway-model-value="or55476OneWayType"
    @update:model-value="or55476Change"
  />
</template>

<style scoped lang="scss">
:deep(.ml-4) {
  margin-left: 0px !important;
}

.container {
  //overflow-x: hidden;
  //overflow-y: auto;
  //max-height: 510px;

  :has(> .contentTitle) {
    font-size: 18px !important;
  }

  :has(.contentItem) {
    :deep(.item-label) {
      color: #800000;
    }
  }

  .title {
    background: #e6e6e6;
    border-top: thin solid rgb(var(--v-theme-form));
    border-bottom: thin solid rgb(var(--v-theme-form));
    padding-left: 14px;
    height: 48px;
    margin-bottom: 16px !important;
  }
  .title:not(:first-child) {
    margin-top: 8px !important;
  }

  .title .v-sheet {
    background: #e6e6e6;
  }

  .content {
    background: #ffffff;
    padding-bottom: 16px !important;
    .asection {
      .title_container {
        padding-left: 32px;
      }

      .asection_right {
        background: #ffffff;

        .asection_right_second {
          justify-content: end;

          :deep(.v-field__input) {
            min-height: 8px !important;
            height: 25px;
          }

          :has(> .toGray) {
            :first-child {
              background-color: #c0c0c0 !important;
            }
          }
        }
      }
    }
  }
}

.align-header {
  align-items: center;
  margin-top: 0px;
}

.section-header {
  max-width: 190px;
  min-width: 190px;
  padding: 0px;
  padding-left: 12px;
}
.padding-header {
  padding-top: 0px;
}

.divider-div {
  display: flex;
}

:deep(.label_transparent) {
  background: transparent;
}

:deep(.label_margin_left_8) {
  margin-left: 8px;
}

:deep(.label_margin_left_16) {
  margin-left: 16px;
}

:deep(.label_margin_left_28) {
  margin-left: 28px;
}

:deep(.row_large_title) {
  padding: 11px 0px 11px 0px;
}

:deep(.row_large_title label) {
  font-size: 16px;
}

:deep(.row_large_title2 label) {
  font-size: 16px;
}

:deep(.row_large_title3_subtitle) {
  padding: 0px 0px 8px 0px;
}

:deep(.row_title) {
  padding-left: 8px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

:deep(.title_container) {
  position: relative;
}

:deep(.edit_icon_right) {
  position: absolute;
  top: 50%;
  transform: translateY(-50%) translateX(50%);
  left: 50%;
}

:deep(.edit_icon_center) {
  position: absolute;
  top: 50%;
  transform: translateY(-50%) translateX(-50%);
  left: 50%;
}

:deep(.family_situation_row_left) {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
}

:deep(.family_situation_row_right) {
  border-left: 0px !important;
}

:deep(.label-val-title-width) {
  width: 27px;
}

:deep(.short_padding) {
  padding: 4px 0px 4px 0px;
}

.row_large_title_4_2 {
  margin-top: -3px;
  margin-left: 31px;
}

.bg-color {
  padding-top: 16px;
  background-color: rgb(var(--v-theme-background)) !important;
  position: relative;
  z-index: 1;
}

:deep(.or55034Oneway_mr) {
  margin-right: 4px;
  padding-top: 8px !important;
}

:deep(.or55034Oneway_ml) {
  margin-top: 5px;
  margin-left: -5px;
}

.v-sheet.custom-input {
  background-color: rgb(var(--v-theme-background));
}

.custom-input {
  :deep(.v-input__control) {
    background-color: rgb(var(--v-theme-secondaryBackground));
  }
}

.ml-24 {
  margin-left: 24px;
}

.ml-20 {
  margin-left: 20px;
}

.subTitle_col {
  margin-left: 40px !important;
  margin-right: 40px !important;
  padding-left: 8px !important;
  background-color: rgba(7, 96, 230, 0.08);
  height: 48px;
  padding-top: 13px !important;
  :deep(label) {
    color: rgb(var(--v-theme-text));
    font-weight: bolder !important;
  }
}

.aLink {
  text-decoration: underline;
  color: #214d97;
  font-weight: bold;
  cursor: pointer;
}

.facilities_use_left {
  background-color: transparent !important;
  float: left;
}

.facilities_use_right {
  background-color: transparent !important;
  float: right;
  padding-right: 8px;
}

.color0760e6Btn {
  border-color: #0760e6 !important;
  :deep(span) {
    color: #0760e6 !important;
  }
}

$border-radius: 4px;
$icon-edit-btn-width: 36px;
$icon-border-radius: 3px;
$border-color: #cdcdcd;
$icon-color: #869fca;
$edit-btn-background: #ebf2fd;
.input-field-container {
  border-radius: $border-radius;
  // 入力フィールドのアトミックベーススタイル
  .mo-input-field {
    margin-right: 0px !important;
    border-top-right-radius: $border-radius !important;
    border-end-end-radius: $border-radius !important;
    // 入力補助ボタンが存在する場合の左側境界線の角丸除去
    :deep(.v-field) {
      border-top-left-radius: 0px !important;
      border-end-start-radius: 0px !important;
    }
  }
}
.right-input :deep(input) {
  text-align: right !important;
}
.spin-btn-box {
  display: flex;
  flex-direction: column;
  width: 20px;
  height: 100%;
  margin-left: 4px;
}

.spin-btn {
  min-width: inherit;
  min-height: inherit !important;
  border-radius: 0;
  height: 50%;
}

:deep(.v-field) {
  padding-right: 0;
  border: 1px solid $border-color;
}
:deep(.v-field.v-field--focused) {
  border-color: rgb(var(--v-theme-key));
}
:deep(.v-field.v-field--focused.v-field--error) {
  border-color: rgb(var(--v-theme-error));
}
// 入力補助ボタンのスタイル
.icon-edit-btn {
  border: 1px solid $border-color;
  background: $edit-btn-background;
  color: $icon-color;
  width: $icon-edit-btn-width;
  height: 100%;
  border-top-left-radius: $icon-border-radius !important;
  border-end-start-radius: $icon-border-radius !important;
  border-top-right-radius: 0px !important;
  border-end-end-radius: 0px !important;
  border-right: none;
  font-size: 20px;
  padding-left: 8px;
  padding-top: 2px;
}
:deep(.v-field__outline__start) {
  border: none !important;
  opacity: unset !important;
}
:deep(.v-field__outline__end) {
  border: none !important;
  opacity: unset !important;
}
:deep(.v-input__prepend) {
  margin-inline-end: unset !important;
}

.d-flex {
  height: 40px;
  :deep(.v-input__control) {
    width: 138px;
  }
}

.tel {
  :deep(.label-val-title-width) {
    padding-top: 10px !important;
  }
}

.or11207CheckBox {
  margin-right: 15px !important;
  height: 40px;
}

:deep(.v-checkbox .v-selection-control) {
  min-height: 0px;
}

.right_label {
  background: transparent !important;
  padding-top: 8px !important;
  padding-left: 3px !important;
}

.facility_type1 {
  position: absolute;
  top: 8px;
  right: 15px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 9px;
  transform: translate(50%, -50%);
}
.facility_type2 {
  position: absolute;
  top: 8px;
  right: 22px;
  border-radius: 50%;
  width: 38px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 9px;
  transform: translate(50%, -50%);
}

:deep(.memoKnj .v-field) {
  border: 0px;
}

:deep(.ml-neg8) {
  margin-left: -5px !important;
}

.mr_2_div {
  margin-right: 0px !important;
}

.ml-4px {
  margin-left: 4px !important;
}

.ml-8px {
  margin-left: 8px !important;
}
</style>
