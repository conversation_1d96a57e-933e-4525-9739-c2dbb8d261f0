<script setup lang="ts">
/**
 * Or08207_基本情報画面入力フォーム
 * GUI01067_基本情報
 *
 * @description
 * 基本情報画面入力フォーム
 *
 *
 * <AUTHOR> HOANG SY TOAN
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27633Const } from '../Or27633/Or27633.constants'
import { Or27633Logic } from '../Or27633/Or27633.logic'
import { Or10883Logic } from '../Or10883/Or10883.logic'
import { Or10883Const } from '../Or10883/Or10883.constants'
import { Or27349Const } from '../Or27349/Or27349.constants'
import { Or27349Logic } from '../Or27349/Or27349.logic'
import type { NinteiList } from '../Or27349/Or27349.type'
import { OrX0157Const } from '../OrX0157/OrX0157.constants'
import type { Or08207OnewayType, Or08207StateType, ScreenData } from './Or08207.type'
import { Or08207Const } from './Or08207.constants'
import type { Or27633OnewayType } from '@/types/cmn/business/components/Or27633Type'
import { Or26491Const } from '~/components/custom-components/organisms/Or26491/Or26491.constants'
import { Or26491Logic } from '~/components/custom-components/organisms/Or26491/Or26491.logic'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { ConsultationUserInfo } from '~/repositories/cmn/entities/ConsultationUserInitSelectEntity'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo01298OnewayType } from '~/types/business/components/Mo01298Type'
import type { Mo01299OnewayType } from '~/types/business/components/Mo01299Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Or26491OnewayType } from '~/types/cmn/business/components/Or26491Type'
import type { OrX0157OnewayType } from '~/types/cmn/business/components/OrX0157Type'
import { CustomClass } from '~/types/CustomClassType'
import {
  dateUtils,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
  useCmnCom,
} from '#imports'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'
import type { Or27349OnewayType } from '~/types/cmn/business/components/Or27349Type'
import { useValidation } from '@/utils/useValidation'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'

interface Props {
  onewayModelValue: Or08207OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

const { byteLength } = useValidation()

const { t } = useI18n()
const systemCommonStore = useSystemCommonsStore()
const { convertDateToSeireki } = dateUtils()
const { isOverLimitByCharWidth } = useCmnCom()

const defaultOneway = {
  /**
   *基本情報ID
   */
  khn11Id: '1001',
  /**
   *コピーフラグ
   */
  isCopyFlag: false,
} as Or08207OnewayType

const localOneway = reactive({
  or08207: {
    ...defaultOneway,
    ...props.onewayModelValue,
  },
  mo01298OnewayConsultationRoute: {
    anchorPoint: 's-3',
    title: t('label.consultation-route'),
    color: 'rgb(var(--v-theme-black-100))',
  } as Mo01298OnewayType,
  mo00039OnewayConsultationMethod: {
    name: '',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  mo00045OnewayConsulationRouteOthers: {
    width: '150',
    maxLength: '20',
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00009OnewayConsulationRouteIcon: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00039HomeVisitKind: {
    name: '',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  Mo00020OnewayLastConsultationDate: {
    itemLabel: t('label.last_consultation_date'),
    showItemLabel: true,
    disabled: false,
    isVerticalLabel: true,
    width: '130',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'pa-0 mb-1' }),
  } as Mo00020OnewayType,

  mo01298OnewayPersonPresentCondition: {
    anchorPoint: 's-3',
    title: t('label.person_present_condition'),
    color: 'rgb(var(--v-theme-black-100))',
  } as Mo01298OnewayType,
  mo00018OnewayAtHome: {
    showItemLabel: false,
    checkboxLabel: t('label.at_home'),
  } as Mo00018OnewayType,
  Mo00018OnewayHospitalized: {
    showItemLabel: false,
    checkboxLabel: t('label.hospitalized'),
  } as Mo00018OnewayType,
  mo00018OnewayInstitutionalized: {
    showItemLabel: false,
    checkboxLabel: t('label.institutionalized'),
  } as Mo00018OnewayType,
  Mo00045OnewayPresentConditionOthers: {
    width: '300',
    minWidth: '500',
    maxLength: '40',
    showItemLabel: false,
    isVerticalLabel: true,
  } as Mo00045OnewayType,

  Mo00009OnewayPresentConditionOthersIcon: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,

  Mo01298OnewayEverydayLifeIndependenceLevel: {
    anchorPoint: 's-3',
    title: t('label.everydaylife-life-independence-level'),
    color: 'rgb(var(--v-theme-black-100))',
  } as Mo01298OnewayType,
  mo01299OnewayHandycapSeniorEverydayLifeIndependenceLevel: {
    title: t('label.degree_indep_daily_living_eld_disab'),
  } as Mo01299OnewayType,
  Mo00039OnewayHandycapSeniorEverydayLifeIndependenceLevel: {
    name: '',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  Mo01299OnewayDegreeIndepDailyLivingEldDem: {
    title: t('label.degree_indep_daily_living_eld_dem'),
  } as Mo01299OnewayType,
  Mo00039OnewayDegreeIndepDailyLivingEldDem: {
    name: '',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  Mo01298OnewayCertCompBusinessInfo: {
    anchorPoint: 's-3',
    title: t('label.cert_comp_business_info'),
    color: 'rgb(var(--v-theme-black-100))',
  } as Mo01298OnewayType,
  Mo00615OnewayManager: {
    itemLabel: t('label.manager'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1 ml-0' }),
  } as Mo00615OnewayType,
  Mo00615OnewayFamilyRelationship: {
    itemLabel: t('label.show-content-base-intention1'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1 ml-0' }),
  } as Mo00615OnewayType,
  Mo00009OnewayCertificationInfoIcon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo00040OnewayLevelOfCareRequired: {
    showItemLabel: false,
    width: '150px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  Mo00615OnewayExpirationDate: {
    itemLabel: t('label.expiration_date'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1 ml-0' }),
  } as Mo00615OnewayType,
  Mo00020OnewayValidTimeLimitStartDate: {
    showItemLabel: false,
    disabled: false,
    isVerticalLabel: false,
    width: '115',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'pa-0 mb-1' }),
  } as Mo00020OnewayType,
  Mo01338OnewayWaveDash: {
    itemLabel: '～',
    customClass: new CustomClass({ outerClass: 'mx-2', labelClass: 'ma-1' }),
  } as Mo01338OnewayType,
  Mo00020OnewayValidTimeLimitEndDate: {
    showItemLabel: false,
    isVerticalLabel: false,
    width: '115',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'pa-0 mb-1' }),
  } as Mo00020OnewayType,
  Mo00615OnewayPrevCareDegree: {
    itemLabel: t('label.prev_care_degree'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1 ml-0' }),
  } as Mo00615OnewayType,
  Mo00040OnewayPrevCareDegree: {
    showItemLabel: false,
    width: '150px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,

  Mo01299OnewayBasicChecklistEntryResults: {
    title: t('label.basic_checklist_entry_results'),
  } as Mo01299OnewayType,
  Mo00039OnewayBasicChecklistEntryResult: {
    name: '',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  Mo01299OnewayBasicChecklistEntryDate: {
    title: t('label.basic_checklist_entry_date'),
  } as Mo01299OnewayType,
  Mo00009OnewayBasicChecklistEntryHistorySelectIcon: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo00020OnewayBasicChecklistEntryDate: {
    showItemLabel: false,
    disabled: false,
    isVerticalLabel: false,
    width: '123',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'pa-0 mb-0' }),
  } as Mo00020OnewayType,

  Mo01298OnewayDisabCert: {
    anchorPoint: 's-3',
    title: t('label.disab_cert'),
    color: 'rgb(var(--v-theme-black-100))',
  } as Mo01298OnewayType,
  Mo00018OnewayDisability: {
    showItemLabel: false,
    checkboxLabel: t('label.disability'),
  } as Mo00018OnewayType,
  Mo00040OnewayDisabilityLevel: {
    showItemLabel: false,
    width: '90',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
    customClass: new CustomClass({ outerClass: 'mr-0', itemClass: 'hieu-test' }),
  } as Mo00040OnewayType,
  Mo00018OnewayUpbringing: {
    showItemLabel: false,
    checkboxLabel: t('label.upbringing'),
  } as Mo00018OnewayType,
  Mo00040OnewayRemedialEducationGrade: {
    showItemLabel: false,
    width: '90',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
    customClass: new CustomClass({ outerClass: 'mr-0', itemClass: 'hieu-test' }),
  } as Mo00040OnewayType,
  Mo00018OnewaySpirit: {
    showItemLabel: false,
    checkboxLabel: t('label.spirit'),
  } as Mo00018OnewayType,
  Mo00040OnewayMentalGrade: {
    showItemLabel: false,
    width: '90',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
    customClass: new CustomClass({ outerClass: 'mr-0', itemClass: 'hieu-test' }),
  } as Mo00040OnewayType,
  Mo00018OnewayIncurableDisease: {
    showItemLabel: false,
    checkboxLabel: t('label.incurable-disease'),
  } as Mo00018OnewayType,
  Mo00045OnewayMemo1: {
    width: '150',
    maxLength: '16',
    showItemLabel: false,
    isVerticalLabel: true,
  } as Mo00045OnewayType,
  Mo00009OnewayMemo1Icon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo00045OnewayMemo2: {
    width: '180',
    maxLength: '22',
    showItemLabel: false,
    isVerticalLabel: true,
  } as Mo00045OnewayType,
  Mo00009OnewayMemo2Icon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo01298OnewayPersonLivingEnv: {
    anchorPoint: 's-3',
    title: t('label.person_living_env'),
    color: 'rgb(var(--v-theme-black-100))',
  } as Mo01298OnewayType,
  Mo00018OnewayPersonalHome: {
    showItemLabel: false,
    checkboxLabel: t('label.home'),
  } as Mo00018OnewayType,
  Mo00018OnewayRentedHouse: {
    showItemLabel: false,
    checkboxLabel: t('label.rented_house'),
  } as Mo00018OnewayType,
  Mo00018OnewayDetachedHouse: {
    showItemLabel: false,
    checkboxLabel: t('label.detached_house'),
  } as Mo00018OnewayType,
  Mo00018OnewayApartment: {
    showItemLabel: false,
    checkboxLabel: t('label.apartment'),
  } as Mo00018OnewayType,
  Mo00039OnewayPrivateRoom: {
    name: 'private_room',
    showItemLabel: false,
    inline: true,
    items: [],
    customClass: new CustomClass({ outerClass: 'd-flex align-center' }),
  } as Mo00039OnewayType,
  Mo00045OnewayPrivateRoomFloor: {
    showItemLabel: false,
    width: '72px',
    maxLength: '3',
  } as Mo00045OnewayType,
  floorInputAppendLabel: { value: t('label.floor-input-append-label') },
  Mo00039OnewayHousingRepair: {
    name: 'housing-repair',
    showItemLabel: false,
    inline: true,
    items: [],
    customClass: new CustomClass({ outerClass: 'd-flex align-center' }),
  } as Mo00039OnewayType,

  Mo01298OnewayEconomicSituation: {
    anchorPoint: 's-3',
    title: t('label.economic_situation'),
    color: 'rgb(var(--v-theme-black-100))',
  } as Mo01298OnewayType,
  Mo00018OnewayNationalPension: {
    showItemLabel: false,
    checkboxLabel: t('label.national_pension'),
  } as Mo00018OnewayType,
  Mo00018OnewayWelfarePension: {
    showItemLabel: false,
    checkboxLabel: t('label.employee_pension'),
  } as Mo00018OnewayType,
  Mo00018OnewayDisabPension: {
    showItemLabel: false,
    checkboxLabel: t('label.disab_pension'),
  } as Mo00018OnewayType,
  Mo00018OnewayLifeProtection: {
    showItemLabel: false,
    checkboxLabel: t('label.life-protection'),
  } as Mo00018OnewayType,
  Mo00046OnewayEconomySituation: {
    showItemLabel: false,
    rows: '2',
    autoGrow: false,
    maxLength: '114',
    customClass: new CustomClass({ outerClass: 'w-75' }),
  } as Mo00046OnewayType,
  Mo00009OnewayEconomySituationIcon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,

  Mo01298OnewayVisitorConsulteeInfo: {
    anchorPoint: 's-3',
    title: t('label.visitor_consultee_info'),
    color: 'rgb(var(--v-theme-black-100))',
  } as Mo01298OnewayType,
  Mo00009OnewayVisitorConsulteeInfoIcon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo00045OnewayNameConsent: {
    itemLabel: t('label.name-consent'),
    isVerticalLabel: true,
    maxLength: Or08207Const.CHAR_LIMIT.NAME_CONSENT.toString(),
    customClass: new CustomClass({ outerClass: '', labelClass: 'mx-2 ml-0' }),
    rules: [byteLength(Or08207Const.CHAR_LIMIT.NAME_CONSENT)],
    width: '233px',
  } as Mo00045OnewayType,
  Mo00045OnewayRelationship: {
    itemLabel: t('label.relationship'),
    isVerticalLabel: true,
    maxLength: Or08207Const.CHAR_LIMIT.RELATIONSHIP.toString(),
    customClass: new CustomClass({ outerClass: '', labelClass: 'mx-2 ml-0' }),
    rules: [byteLength(Or08207Const.CHAR_LIMIT.RELATIONSHIP)],
    width: '128px',
  } as Mo00045OnewayType,
  Mo00045OnewayAddressContactInfo: {
    itemLabel: t('label.address_contact_info'),
    isVerticalLabel: true,
    maxLength: Or08207Const.CHAR_LIMIT.ADDRESS_CONTACT_INFO.toString(),
    customClass: new CustomClass({ outerClass: 'w-75', labelClass: 'mt-1 mr-2' }),
    rules: [byteLength(Or08207Const.CHAR_LIMIT.ADDRESS_CONTACT_INFO)],
    width: '953px',
  } as Mo00045OnewayType,

  Mo01298OnewayFamilyStructure: {
    anchorPoint: 's-3',
    title: t('label.family_structure'),
    color: 'rgb(var(--v-theme-black-100))',
  } as Mo01298OnewayType,
  familyStructureLabel: { value: t('label.family_structure') },
  Mo00009OnewayFamilyStructureIcon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  familyDiagram: {},
  familyDiagramZoomOutFlag: {},
  familyDiagramDescriptionLabel: {
    value:
      '◎＝本人、〇＝女性、□＝男性\n●■＝死亡、☆＝キーパーソン\n主介護者に「主」、副介護者に「副」\n（同居家族は〇で囲む）',
  },
  Mo00615OnewayFamilyRelSituation: {
    itemLabel: t('label.family_rel_situation'),
  } as Mo00615OnewayType,
  Mo00009OnewayFamilyRelSituationIcon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo00046OnewayFamilyRelationEtcSituation: {
    showItemLabel: false,
    rows: '3',
    autoGrow: false,
    maxLength: '320',
    customClass: new CustomClass({ outerClass: 'w-100', labelClass: 'mt-1 mr-2' }),
  } as Mo00046OnewayType,
  howToConsultMo01338Oneway: {
    value: t('label.how-to-consult'),
    valueFontWeight: 'normal',
    customClass: {
      itemClass: 'no-history ml-0',
    } as CustomClass,
  } as Mo01338OnewayType,
  numberOfConsultationsMo01338Oneway: {
    value: t('label.number-of-consultations'),
    valueFontWeight: 'normal',
    customClass: {
      itemClass: 'no-history ml-0',
    } as CustomClass,
  } as Mo01338OnewayType,
  degreeIndepDailyLivingEldDisabMo01338Oneway: {
    value: t('label.degree_indep_daily_living_eld_disab'),
    valueFontWeight: 'normal',
    customClass: {
      itemClass: 'no-history ml-0',
    } as CustomClass,
  } as Mo01338OnewayType,
  degreeIndepDailyLivingEldDemMo01338Oneway: {
    value: t('label.degree_indep_daily_living_eld_dem'),
    valueFontWeight: 'normal',
    customClass: {
      itemClass: 'no-history ml-0',
    } as CustomClass,
  } as Mo01338OnewayType,
  basicChecklistEntryResultsMo01338Oneway: {
    value: t('label.basic_checklist_entry_results'),
    valueFontWeight: 'normal',
    customClass: {
      itemClass: 'no-history ml-0',
    } as CustomClass,
  } as Mo01338OnewayType,
  basicChecklistEntryDateMo01338Oneway: {
    value: t('label.basic_checklist_entry_date'),
    valueFontWeight: 'bold',
    customClass: {
      itemClass: 'no-history ml-0',
      itemStyle: 'color: rgb(var(--v-theme-blue-700))',
    } as CustomClass,
  } as Mo01338OnewayType,
  typeOfResidenceMo01338Oneway: {
    value: t('label.type-of-residence'),
    valueFontWeight: 'normal',
    customClass: {
      itemClass: 'no-history ml-0',
    } as CustomClass,
  } as Mo01338OnewayType,
  roomAvailabilityMo01338Oneway: {
    value: t('label.room-availability'),
    valueFontWeight: 'normal',
    customClass: {
      itemClass: 'no-history ml-0',
    } as CustomClass,
  } as Mo01338OnewayType,
  homeRenovationMo01338Oneway: {
    value: t('label.home-renovation'),
    valueFontWeight: 'normal',
    customClass: {
      itemClass: 'no-history ml-0',
    } as CustomClass,
  } as Mo01338OnewayType,
  consulationRouteOthersOrX0157Oneway: {
    inputMode: 'TextOnly',
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        maxLength: Or08207Const.CHAR_LIMIT.CONSULTATION_ROUTE_OTHERS.toString(), // 半角20文字まで
        hideDetails: 'false',
        customClass: new CustomClass({
          labelClass: 'pb-1',
          labelStyle: 'background-color:rgb(var(--v-theme-background));',
        }),
        width: '342px',
      },
    },
  } as OrX0157OnewayType,
  presentConditionOthersOrX0157Oneway: {
    inputMode: 'TextOnly',
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        maxLength: Or08207Const.CHAR_LIMIT.PRESENT_CONDITION_OTHERS.toString(), // 半角40文字まで
        hideDetails: 'false',
        customClass: new CustomClass({
          labelClass: 'pb-1',
          labelStyle: 'background-color:rgb(var(--v-theme-background));',
        }),
        width: '342px',
      },
    },
  } as OrX0157OnewayType,
  memo1OrX0157Oneway: {
    inputMode: 'TextOnly',
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        maxLength: Or08207Const.CHAR_LIMIT.MEMO1.toString(), // 半角16文字まで
        hideDetails: 'false',
        customClass: new CustomClass({
          labelClass: 'pb-1',
          labelStyle: 'background-color:rgb(var(--v-theme-background));',
        }),
        width: '158px',
      },
    },
  } as OrX0157OnewayType,
  memo2OrX0157Oneway: {
    inputMode: 'TextOnly',
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        maxLength: Or08207Const.CHAR_LIMIT.MEMO2.toString(), // 半角22文字まで
        hideDetails: 'false',
        customClass: new CustomClass({
          labelClass: 'pb-1',
          labelStyle: 'background-color:rgb(var(--v-theme-background));',
        }),
        width: '158px',
      },
    },
  } as OrX0157OnewayType,
  economySituationOrX0157Oneway: {
    inputMode: 'TextOnly',
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        maxLength: Or08207Const.CHAR_LIMIT.ECONOMY_SITUATION.toString(), // 半角114文字まで
        hideDetails: 'false',
        customClass: new CustomClass({
          labelClass: 'pb-1',
          labelStyle: 'background-color:rgb(var(--v-theme-background));',
        }),
        width: '552px',
      },
    },
  } as OrX0157OnewayType,
  familyRelationshipX0156Oneway: {
    itemLabel: t('label.family_rel_situation'),
    showItemLabel: true,
    isVerticalLabel: true,
    noResize: true,
    autoGrow: false,
    maxlength: '4000',
    class: 'A12BTextArea',
    rows: 4,
    maxRows: '4',
    // disabled: !(props.onewayModelValue?.isAllowEdit ?? true),
    // showEditBtnFlg: props.onewayModelValue?.isAllowEdit ?? true,
    showDividerLineFlg: true,
    mo00009: {
      btnIcon: 'edit_square',
      color: 'rgb(var(--v-theme-light-blue-400))',
      variant: 'text',
      width: '32px',
      height: '32px',
      style: 'background: #EBF2FD;',
    },
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent; width: 100%;',
      labelClass: 'title-bar',
      labelStyle:
        'width: 100%; padding-left: 0px; border-radius: 4px; min-height: 48px; padding-top: 16px !important',
      itemStyle:
        'background: rgb(var(--v-theme-secondaryBackground)); border-radius: 4px; padding-top: 0;',
    }),
  } as OrX0156OnewayType,
})

const Or26491 = ref({ uniqueCpId: Or26491Const.CP_ID(1) })
const or10883 = ref({ uniqueCpId: '', editCpNo: '' })

const Or26491OnewayModel: Or26491OnewayType = {
  sc1Id: '1',
  itkJigyoId: '1',
  userid: '1',
}

const Or10883OnewayModel: Or10883OnewayType = {
  // 利用者ID
  userId: systemCommonStore.getUserId!,
  // 親画面.大分類ＣＤ
  t1Cd: '20',
  // 親画面.中分類ＣＤ
  t2Cd: '1',
  // 親画面.小分類ＣＤ
  t3Cd: '0',
  // 親画面.過去履歴用テーブル名
  historyTableName: 'kyc_tuc_plan11',
  // 過去履歴用カラム名
  historyColumnName: 'soudan_h_knj',
  // 親画面.選択セール文章内容
  inputContents: '',
  // タイトル
  title: t('label.opinion'),
}

const or27633 = ref({ uniqueCpId: Or27633Const.CP_ID(0) })
const or27349 = ref({ uniqueCpId: '' })
const orX0157 = ref({ uniqueCpId: '' })

const or27633Data: Or27633OnewayType = {
  /**
   * 利用者ID
   */
  alUserid: '1',
}

const defaultLocal = {
  data: {
    consulationMethod: '',
    consulationRouteOthers: { value: '' },
    homeVisitKind: '',
    lastConsultationDate: { value: '' },
    atHome: { modelValue: false },
    hospitalized: { modelValue: false },
    institutionalized: { modelValue: false },
    presentConditionOthers: { value: '' },
    handycapSeniorEverydayLifeIndependenceLevel: '',
    degreeIndepDailyLivingEldDem: '',
    levelOfCareRequired: { modelValue: '' },
    validTimeLimitStartDate: { value: '' },
    validTimeLimitEndDate: { value: '' },
    prevCareDegree: { modelValue: '' },
    basicChecklistEntryResult: '',
    basicChecklistEntryDate: { value: '' },
    disability: { modelValue: false },
    disabilityLevel: { modelValue: '' },
    upbringing: { modelValue: false },
    remedialEducationGrade: { modelValue: '' },
    spirit: { modelValue: false },
    mentalGrade: { modelValue: '' },
    incrudableDisease: { modelValue: false },
    memo1: { value: '' },
    memo2: { value: '' },
    personalHome: { modelValue: false },
    rentedHouse: { modelValue: false },
    detachedHouse: { modelValue: false },
    apartment: { modelValue: false },
    privateRoom: '',
    privateRoomFloor: { value: '' },
    housingRepair: '',
    nationalPension: { modelValue: false },
    welfarePension: { modelValue: false },
    disabPension: { modelValue: false },
    lifeProtection: { modelValue: false },
    economySituation: { value: '' },
    nameConsent: { value: '' },
    relationship: { value: '' },
    addressContactInfo: { value: '' },
    Or31675: {
      line1: {
        name: {
          value: '',
        },
        relationship: {
          value: '',
        },
        address: {
          value: '',
        },
      },
      line2: {
        name: {
          value: '',
        },
        relationship: {
          value: '',
        },
        address: {
          value: '',
        },
      },
      line3: {
        name: {
          value: '',
        },
        relationship: {
          value: '',
        },
        address: {
          value: '',
        },
      },
      line4: {
        name: {
          value: '',
        },
        relationship: {
          value: '',
        },
        address: {
          value: '',
        },
      },
    },
    familyDiagramZoomOutFlag: false,
    familyRelationEtcSituation: { value: '' },
  },
} as ScreenData

const { refValue } = useScreenTwoWayBind<Or08207StateType>({
  cpId: Or08207Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

const local = ref({
  ...defaultLocal,
  or10883Data: {
    /**
     * 入力支援内容
     */
    naiyo: '',
  } as Or10883TwowayType,
})

//COMPUTED

const isShowDialogOr26491 = computed(() => {
  return Or26491Logic.state.get(Or26491.value.uniqueCpId)?.isOpen ?? false
})

const isShowDialogOr10883 = computed(() => {
  return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr27349 = computed(() => {
  return Or27349Logic.state.get(or27349.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr27633 = computed(() => {
  // Or27633のダイアログ開閉状態
  const state = Or27633Logic.state.get(or27633.value.uniqueCpId) as { isOpen?: boolean }
  return state?.isOpen ?? false
})

useSetupChildProps(props.uniqueCpId, {
  [Or27349Const.CP_ID(1)]: or27349.value,
  [Or10883Const.CP_ID(1)]: or10883.value,
  [OrX0157Const.CP_ID(1)]: orX0157.value,
})

watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.or08207 = {
      ...defaultOneway,
      ...newValue,
    }
  },
  { deep: true }
)

watch(
  () => refValue.value,
  (newValue) => {
    if (!newValue) return

    initData(newValue)
  },
  { deep: true, immediate: true }
)

watch(
  () => local.value.or10883Data,
  (newValue) => {
    if (!newValue) return

    // Check editCpNo and process data
    if (or10883.value.editCpNo && newValue.naiyo) {
      const content = newValue.naiyo
      const localData = local.value.data

      switch (or10883.value.editCpNo) {
        case 'consulationRouteOthers':
          if (localData.consulationRouteOthers?.value !== undefined) {
            localData.consulationRouteOthers.value = content
          }
          break
        case 'presentConditionOthers':
          if (localData.presentConditionOthers?.value !== undefined) {
            localData.presentConditionOthers.value = content
          }
          break
        case 'memo1':
          if (localData.memo1?.value !== undefined) {
            localData.memo1.value = content
          }
          break
        case 'memo2':
          if (localData.memo2?.value !== undefined) {
            localData.memo2.value = content
          }
          break
        case 'economySituation':
          if (localData.economySituation?.value !== undefined) {
            localData.economySituation.value = content
          }
          break
        case 'familyRelationEtcSituation':
          if (localData.familyRelationEtcSituation?.value !== undefined) {
            localData.familyRelationEtcSituation.value = content
          }
          break
        default:
          break
      }
      or10883.value.editCpNo = ''
    }
  },
  { deep: true }
)

watch(
  () => localOneway.or08207.isCopyFlag,
  (newValue) => {
    if (newValue) {
      localOneway.mo00039OnewayConsultationMethod.disabled = true
      localOneway.mo00045OnewayConsulationRouteOthers.disabled = true
      localOneway.mo00009OnewayConsulationRouteIcon.disabled = true
      localOneway.mo00018OnewayAtHome.disabled = true
      localOneway.Mo00018OnewayHospitalized.disabled = true
      localOneway.mo00018OnewayInstitutionalized.disabled = true
      localOneway.Mo00045OnewayPresentConditionOthers.disabled = true
      localOneway.Mo00039OnewayHandycapSeniorEverydayLifeIndependenceLevel.disabled = true
      localOneway.Mo00039OnewayDegreeIndepDailyLivingEldDem.disabled = true
      localOneway.Mo00040OnewayLevelOfCareRequired.disabled = true
      localOneway.Mo00040OnewayPrevCareDegree.disabled = true
      localOneway.Mo00039OnewayBasicChecklistEntryResult.disabled = true
      localOneway.Mo00018OnewayDisability.disabled = true
      localOneway.Mo00040OnewayDisabilityLevel.disabled = true
      localOneway.Mo00018OnewayUpbringing.disabled = true
      localOneway.Mo00040OnewayRemedialEducationGrade.disabled = true
      localOneway.Mo00018OnewaySpirit.disabled = true
      localOneway.Mo00040OnewayMentalGrade.disabled = true
      localOneway.Mo00018OnewayIncurableDisease.disabled = true
      localOneway.Mo00045OnewayMemo1.disabled = true
      localOneway.Mo00045OnewayMemo2.disabled = true
      localOneway.Mo00020OnewayLastConsultationDate.disabled = true
      localOneway.Mo00020OnewayValidTimeLimitStartDate.disabled = true
      localOneway.Mo00020OnewayValidTimeLimitEndDate.disabled = true
      localOneway.Mo00020OnewayBasicChecklistEntryDate.disabled = true
      localOneway.Mo00018OnewayPersonalHome.disabled = true
      localOneway.Mo00018OnewayRentedHouse.disabled = true
      localOneway.Mo00018OnewayDetachedHouse.disabled = true
      localOneway.Mo00018OnewayApartment.disabled = true
      localOneway.Mo00039OnewayPrivateRoom.disabled = true
      localOneway.Mo00045OnewayPrivateRoomFloor.disabled = true
      localOneway.Mo00039OnewayHousingRepair.disabled = true
      localOneway.Mo00018OnewayNationalPension.disabled = true
      localOneway.Mo00018OnewayWelfarePension.disabled = true
      localOneway.Mo00018OnewayDisabPension.disabled = true
      localOneway.Mo00018OnewayLifeProtection.disabled = true
      localOneway.Mo00046OnewayEconomySituation.disabled = true
      localOneway.Mo00045OnewayNameConsent.disabled = true
      localOneway.Mo00045OnewayRelationship.disabled = true
      localOneway.Mo00045OnewayAddressContactInfo.disabled = true
      localOneway.Mo00046OnewayFamilyRelationEtcSituation.disabled = true
    }
  },
  { immediate: true }
)

watch(
  () => Or27349Logic.event.get(or27349.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.closeFlg) {
      Or27349Logic.event.set({
        uniqueCpId: or27349.value.uniqueCpId,
        events: { closeFlg: false },
      })
    }
    if (newValue.comfirmFlg) {
      Or27349Logic.event.set({
        uniqueCpId: or27349.value.uniqueCpId,
        events: { comfirmFlg: false },
      })
    }
  }
)

onMounted(async () => {
  await initCodes()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONSULTATIONROUTE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_VISITTYPE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DAILY_LIFE_INDEPENDENCE_CRITERIA_BEDRIDDEN },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DAILY_LIFE_INDEPENDENCE_CRITERIA_DEMENTIA },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BASICCHECKLISTENTRYRESULTS },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CAREDEGREECATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DISABILITYNOTEBOOK },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_HANDYCAP },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_WELFARENOTEBOOK },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRIVATEROOMPRESENCE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_HOMERENOVATIONPRESENCE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  localOneway.mo00039OnewayConsultationMethod.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CONSULTATIONROUTE
  )
  localOneway.mo00039HomeVisitKind.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_VISITTYPE
  )

  localOneway.Mo00039OnewayHandycapSeniorEverydayLifeIndependenceLevel.items =
    CmnSystemCodeRepository.filter(
      CmnMCdKbnId.M_CD_KBN_ID_DAILY_LIFE_INDEPENDENCE_CRITERIA_BEDRIDDEN
    )
  localOneway.Mo00039OnewayDegreeIndepDailyLivingEldDem.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DAILY_LIFE_INDEPENDENCE_CRITERIA_DEMENTIA
  )

  localOneway.Mo00039OnewayBasicChecklistEntryResult.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BASICCHECKLISTENTRYRESULTS
  )
  const careDegreeCategory = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CAREDEGREECATEGORY
  )
  const levelOfCareRequiredItems = careDegreeCategory.filter(
    (item) => item.value !== Or08207Const.DEFAULTS.NURSING_CARE_VALUE
  )
  localOneway.Mo00040OnewayLevelOfCareRequired.items = [
    Or08207Const.DEFAULTS.EMPTY_OPTION,
    ...levelOfCareRequiredItems,
  ]
  localOneway.Mo00040OnewayPrevCareDegree.items = [
    Or08207Const.DEFAULTS.EMPTY_OPTION,
    ...careDegreeCategory,
  ]

  localOneway.Mo00040OnewayDisabilityLevel.items = [
    Or08207Const.DEFAULTS.EMPTY_OPTION,
    ...CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_DISABILITYNOTEBOOK),
  ]
  localOneway.Mo00040OnewayRemedialEducationGrade.items = [
    Or08207Const.DEFAULTS.EMPTY_OPTION,
    ...CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_HANDYCAP),
  ]
  localOneway.Mo00040OnewayMentalGrade.items = [
    Or08207Const.DEFAULTS.EMPTY_OPTION,
    ...CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_WELFARENOTEBOOK),
  ]

  localOneway.Mo00039OnewayPrivateRoom.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRIVATEROOMPRESENCE
  )
  localOneway.Mo00039OnewayHousingRepair.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_HOMERENOVATIONPRESENCE
  )
}

function initData(retrive: Or08207StateType) {
  const ret = retrive.kihonList
  if (!ret) return
  //相談経路
  local.value.data.consulationMethod = ret.soudanH
  local.value.data.consulationRouteOthers.value = ret.soudanIn
  local.value.data.homeVisitKind = ret.soudanH2
  local.value.data.lastConsultationDate.value = ret.maesouInYmd

  //本人の現況
  local.value.data.atHome.modelValue = ret.ztkUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.hospitalized.modelValue = ret.nyuinUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.institutionalized.modelValue = ret.nyushoUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.presentConditionOthers.value = ret.genkyoKnj

  //日常生活自立度
  local.value.data.handycapSeniorEverydayLifeIndependenceLevel = ret.netaCd1
  local.value.data.degreeIndepDailyLivingEldDem = ret.netaCd2

  //認定・総合事業情報
  local.value.data.levelOfCareRequired.modelValue = ret.ninteiHyouji
  local.value.data.validTimeLimitStartDate.value = ret.startYmd1
  local.value.data.validTimeLimitEndDate.value = ret.endYmd1
  local.value.data.prevCareDegree.modelValue = ret.maeyokaiHyouji
  local.value.data.basicChecklistEntryResult = ret.chklistkekaSentaku
  local.value.data.basicChecklistEntryDate.value = ret.chklistYmdHyouji

  //障害等認定
  local.value.data.disability.modelValue = ret.sinshoUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.disabilityLevel.modelValue = ret.sinshoHyouji
  local.value.data.upbringing.modelValue = ret.ryoikuUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.remedialEducationGrade.modelValue = ret.ryoikuHyouji
  local.value.data.spirit.modelValue = ret.seisinUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.mentalGrade.modelValue = ret.seisinHyouji
  local.value.data.incrudableDisease.modelValue = ret.nanbyoUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.memo1.value = ret.nanbyoMemoknj1
  local.value.data.memo2.value = ret.nanbyoMemoknj2

  //本人の住居環境
  local.value.data.personalHome.modelValue = ret.jitaKu === Or08207Const.DEFAULTS.TRUE
  local.value.data.rentedHouse.modelValue = ret.shakuya === Or08207Const.DEFAULTS.TRUE
  local.value.data.detachedHouse.modelValue = ret.ikkodate === Or08207Const.DEFAULTS.TRUE
  local.value.data.apartment.modelValue = ret.shugou === Or08207Const.DEFAULTS.TRUE
  local.value.data.privateRoom = ret.jisiTu
  local.value.data.privateRoomFloor.value = ret.jisituKai
  local.value.data.housingRepair = ret.kaishu

  //経済状況
  local.value.data.nationalPension.modelValue = ret.kokuNen === Or08207Const.DEFAULTS.TRUE
  local.value.data.welfarePension.modelValue = ret.kouNen === Or08207Const.DEFAULTS.TRUE
  local.value.data.disabPension.modelValue = ret.shoNen === Or08207Const.DEFAULTS.TRUE
  local.value.data.lifeProtection.modelValue = ret.seikatuHogo === Or08207Const.DEFAULTS.TRUE
  local.value.data.economySituation.value = ret.keizai

  //来所者(相談者)情報
  local.value.data.nameConsent.value = ret.sdNameKnj
  local.value.data.relationship.value = ret.sdZokugaraKnj
  local.value.data.addressContactInfo.value = ret.sdAddrKnj

  //家族構成
  local.value.data.familyDiagramZoomOutFlag = ret.shukushoFlg === Or08207Const.DEFAULTS.TRUE
  local.value.data.familyRelationEtcSituation.value = ret.kazokuKankeiIn

  //緊急連絡先
  local.value.data.Or31675.line1.name.value = ret.sdNameKnj1
  local.value.data.Or31675.line1.relationship.value = ret.sdZokugaraKnj1
  local.value.data.Or31675.line1.address.value = ret.jyuusyoKnj1
  local.value.data.Or31675.line2.name.value = ret.sdNameKnj2
  local.value.data.Or31675.line2.relationship.value = ret.sdZokugaraKnj2
  local.value.data.Or31675.line2.address.value = ret.jyuusyoKnj2
  local.value.data.Or31675.line3.name.value = ret.sdNameKnj3
  local.value.data.Or31675.line3.relationship.value = ret.sdZokugaraKnj3
  local.value.data.Or31675.line3.address.value = ret.jyuusyoKnj3
  local.value.data.Or31675.line4.name.value = ret.sdNameKnj4
  local.value.data.Or31675.line4.relationship.value = ret.sdZokugaraKnj4
  local.value.data.Or31675.line4.address.value = ret.jyuusyoKnj4
}

//METHODS
function handleOpenConsultationRouteModal() {
  // AC024
  // GUI01109 入力支援【相談経路】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.consulationRouteOthers.value
  Or10883OnewayModel.userId = systemCommonStore.getUserId!
  Or10883OnewayModel.t1Cd = '20'
  Or10883OnewayModel.t2Cd = '1'
  Or10883OnewayModel.t3Cd = '1'
  Or10883OnewayModel.historyTableName = 'kyc_tuc_khn12'
  Or10883OnewayModel.historyColumnName = 'soudan_h_knj'
  Or10883OnewayModel.title = t('label.consultation-route')
  or10883.value.editCpNo = 'consulationRouteOthers'
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 全角=2, 半角=1 の幅で安全に文字列を切り詰めるユーティリティ
 *
 * @param ch - 文字
 */
// 全角/半角の幅を判定（ASCII 0x20-0x7E 以外を全角として2）
const getCharWidth = (ch: string): number => (/[^\x20-\x7E]/.test(ch) ? 2 : 1)

// 指定の半角換算長(maxLength)以内に収まるように切り詰め
const trimByCharWidth = (text: string, maxLength: number): string => {
  let acc = 0
  let out = ''
  for (const ch of text ?? '') {
    const w = getCharWidth(ch)
    if (acc + w > maxLength) break
    out += ch
    acc += w
  }
  return out
}

function handleOpenPersonalStatusModal() {
  // AC027
  // GUI01109 入力支援【本人の現況】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.presentConditionOthers.value
  Or10883OnewayModel.userId = systemCommonStore.getUserId!
  Or10883OnewayModel.t1Cd = '20'
  Or10883OnewayModel.t2Cd = '1'
  Or10883OnewayModel.t3Cd = '2'
  Or10883OnewayModel.historyTableName = 'kyc_tuc_khn12'
  Or10883OnewayModel.historyColumnName = 'genkyo_knj'
  Or10883OnewayModel.title = t('label.person_present_condition')
  or10883.value.editCpNo = 'presentConditionOthers'
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const Or27349Data: Or27349OnewayType = {
  // listData: [
  //   {
  //     id: '1',
  //     startDate: 1585699200000,
  //     endDate: 1585699200000,
  //     degreeOfCareRequired: '度護介要3',
  //     accreditationDate: 1583107200000,
  //     limit: 27048,
  //   },
  //   {
  //     id: '2',
  //     startDate: 1585699200000,
  //     endDate: 1585785600000,
  //     degreeOfCareRequired: '度護介要4',
  //     accreditationDate: 1583193600000,
  //     limit: 27048,
  //   },
  //   {
  //     id: '3',
  //     startDate: 1585699200000,
  //     endDate: 1585872000000,
  //     degreeOfCareRequired: '度護介要5',
  //     accreditationDate: 1583280000000,
  //     limit: 27048,
  //   },
  //   {
  //     id: '4',
  //     startDate: 1585699200000,
  //     endDate: 1585958400000,
  //     degreeOfCareRequired: '度護介要6',
  //     accreditationDate: 1583366400000,
  //     limit: 27048,
  //   },
  //   {
  //     id: '5',
  //     startDate: 1585699200000,
  //     endDate: 1586044800000,
  //     degreeOfCareRequired: '度護介要7',
  //     accreditationDate: 1583452800000,
  //     limit: 27048,
  //   },
  //   {
  //     id: '6',
  //     startDate: 1585699200000,
  //     endDate: 1586131200000,
  //     degreeOfCareRequired: '度護介要8',
  //     accreditationDate: 1583539200000,
  //     limit: 27048,
  //   },
  // ],
  startYmd1: '',
}

// function handleClickCertificationInfoIcon() {
//   // AC028
//   // GUI01099 認定情報選択画面をポップアップで起動する。
//   setChildCpBinds(props.uniqueCpId, {
//     Or27349: {
//       twoWayValue: {
//         // 現在の認定情報を渡す
//         selectedData: {
//           yokaiKbn: local.value.data.levelOfCareRequired.modelValue,
//           startYmd: local.value.data.validTimeLimitStartDate.value,
//           endYmd: local.value.data.validTimeLimitEndDate.value,
//         },
//       },
//     },
//   })
//   Or27349Logic.state.set({
//     uniqueCpId: or27349.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }

// //GUI01074 基本チェックリスト履歴選択画面をポップアップで起動する。
function handleClickBasicChecklistEntryHistorySelectIcon() {
  Or26491Logic.state.set({
    uniqueCpId: Or26491.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function handleConfirmOr26491(value: string) {
  local.value.data.basicChecklistEntryDate.value = value
}

function onClickMemo1Icon() {
  // AC036
  // GUI01109 入力支援【メモ１】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.memo1.value
  Or10883OnewayModel.userId = systemCommonStore.getUserId!
  Or10883OnewayModel.t1Cd = '20'
  Or10883OnewayModel.t2Cd = '1'
  Or10883OnewayModel.t3Cd = '3'
  Or10883OnewayModel.historyTableName = 'kyc_tuc_khn12'
  Or10883OnewayModel.historyColumnName = 'nanbyo_memo_knj'
  Or10883OnewayModel.title = t('label.disab_cert')
  or10883.value.editCpNo = 'memo1'

  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function onClickMemo2Icon() {
  // AC037
  // GUI01109 入力支援【メモ２】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.memo2.value
  Or10883OnewayModel.userId = systemCommonStore.getUserId!
  Or10883OnewayModel.t1Cd = '20'
  Or10883OnewayModel.t2Cd = '1'
  Or10883OnewayModel.t3Cd = '3'
  Or10883OnewayModel.historyTableName = 'kyc_tuc_khn12'
  Or10883OnewayModel.historyColumnName = 'nanbyo_memo2_knj'
  Or10883OnewayModel.title = t('label.disab_cert')
  or10883.value.editCpNo = 'memo2'

  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function handleClickEconomySituationIcon() {
  // AC038
  // GUI01109 入力支援【経済状況】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.economySituation.value ?? ''
  Or10883OnewayModel.userId = systemCommonStore.getUserId!
  Or10883OnewayModel.t1Cd = '20'
  Or10883OnewayModel.t2Cd = '1'
  Or10883OnewayModel.t3Cd = '4'
  Or10883OnewayModel.historyTableName = 'kyc_tuc_khn12'
  Or10883OnewayModel.historyColumnName = 'keizai_memo_knj'
  Or10883OnewayModel.title = t('label.economic_situation')
  or10883.value.editCpNo = 'economySituation'

  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/*
 *GUI01073 相談者選択画面をポップアップで起動する。
 */
function handleClickVisitorConsulteeInfoIcon() {
  Or27633Logic.state.set({
    uniqueCpId: or27633.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function handleConfirmOr27633(value: ConsultationUserInfo) {
  local.value.data.nameConsent.value = value.nameKnj
  local.value.data.relationship.value = value.zokugaraKnj ?? ''
  local.value.data.addressContactInfo.value = value.addressKnj
}

function handleConfirmOr27349(value: NinteiList) {
  // 認定情報選択画面から返されたデータを処理
  if (value) {
    // 要介護度を設定
    if (value.yokaiKbn) {
      local.value.data.levelOfCareRequired.modelValue = value.yokaiKbn
    }
    // 認定有効開始日を設定
    if (value.startYmd) {
      const startDate = new Date(Number(value.startYmd))
      local.value.data.validTimeLimitStartDate.value = convertDateToSeireki(startDate)
    }
    // 認定終了日を設定
    if (value.endYmd) {
      const endDate = new Date(Number(value.endYmd))
      local.value.data.validTimeLimitEndDate.value = convertDateToSeireki(endDate)
    }
  }
}

function handleClickFamilyStructureIcon() {
  // AC040
  // GUI00629 家族図の登録画面をポップアップで起動する。
  // TODO 画面未作成
}

function handleClickFamilyRelSituationIcon() {
  // AC041
  // GUI01109 入力支援【家族関係等の状況】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.familyRelationEtcSituation.value ?? ''
  or10883.value.editCpNo = 'familyRelationEtcSituation'

  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// 相談経路
watch(
  () => local.value.data.consulationRouteOthers.value,
  (newValue) => {
    if (
      newValue &&
      isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.CONSULTATION_ROUTE_OTHERS)
    ) {
      local.value.data.consulationRouteOthers.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.CONSULTATION_ROUTE_OTHERS
      )
    }
  }
)

// 本人の現況
watch(
  () => local.value.data.presentConditionOthers.value,
  (newValue) => {
    if (
      newValue &&
      isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.PRESENT_CONDITION_OTHERS)
    ) {
      local.value.data.presentConditionOthers.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.PRESENT_CONDITION_OTHERS
      )
    }
  }
)

// メモ2
watch(
  () => local.value.data.memo1.value,
  (newValue) => {
    if (newValue && isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.MEMO1)) {
      local.value.data.memo1.value = trimByCharWidth(newValue, Or08207Const.CHAR_LIMIT.MEMO1)
    }
  }
)

// メモ2
watch(
  () => local.value.data.memo2.value,
  (newValue) => {
    if (newValue && isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.MEMO2)) {
      local.value.data.memo2.value = trimByCharWidth(newValue, Or08207Const.CHAR_LIMIT.MEMO2)
    }
  }
)

// 経済状況
watch(
  () => local.value.data.economySituation.value,
  (newValue) => {
    if (newValue && isOverLimitByCharWidth(newValue, Or08207Const.CHAR_LIMIT.ECONOMY_SITUATION)) {
      local.value.data.economySituation.value = trimByCharWidth(
        newValue,
        Or08207Const.CHAR_LIMIT.ECONOMY_SITUATION
      )
    }
  }
)

watch(
  () => local.value.data,
  (newValue) => {
    // 画面上のデータが変更されたときにrefValueを更新する
    if (refValue.value?.kihonList) {
      refValue.value.kihonList = {
        ...refValue.value.kihonList,
        soudanH: newValue.consulationMethod,
        soudanIn: newValue.consulationRouteOthers.value,
        soudanH2: newValue.homeVisitKind,
        maesouInYmd: newValue.lastConsultationDate.value,
        ztkUmu: newValue.atHome.modelValue ? '1' : '0',
        nyuinUmu: newValue.hospitalized.modelValue ? '1' : '0',
        nyushoUmu: newValue.institutionalized.modelValue ? '1' : '0',
        genkyoKnj: newValue.presentConditionOthers.value,
        netaCd1: newValue.handycapSeniorEverydayLifeIndependenceLevel,
        netaCd2: newValue.degreeIndepDailyLivingEldDem,
        ninteiHyouji: newValue.levelOfCareRequired.modelValue ?? '',
        startYmd1: newValue.validTimeLimitStartDate.value,
        endYmd1: newValue.validTimeLimitEndDate.value,
        maeyokaiHyouji: newValue.prevCareDegree.modelValue ?? '',
        chklistkekaSentaku: newValue.basicChecklistEntryResult,
        chklistYmdHyouji: newValue.basicChecklistEntryDate.value,
        sinshoUmu: newValue.disability.modelValue ? '1' : '0',
        sinshoHyouji: newValue.disabilityLevel.modelValue ?? '',
        ryoikuUmu: newValue.upbringing.modelValue ? '1' : '0',
        ryoikuHyouji: newValue.remedialEducationGrade.modelValue ?? '',
        seisinUmu: newValue.spirit.modelValue ? '1' : '0',
        seisinHyouji: newValue.mentalGrade.modelValue ?? '',
        nanbyoUmu: newValue.incrudableDisease.modelValue ? '1' : '0',
        nanbyoMemoknj1: newValue.memo1.value,
        nanbyoMemoknj2: newValue.memo2.value,
        jitaKu: newValue.personalHome.modelValue ? '1' : '0',
        shakuya: newValue.rentedHouse.modelValue ? '1' : '0',
        ikkodate: newValue.detachedHouse.modelValue ? '1' : '0',
        shugou: newValue.apartment.modelValue ? '1' : '0',
        jisiTu: newValue.privateRoom,
        jisituKai: newValue.privateRoomFloor.value,
        kaishu: newValue.housingRepair,
        kokuNen: newValue.nationalPension.modelValue ? '1' : '0',
        kouNen: newValue.welfarePension.modelValue ? '1' : '0',
        shoNen: newValue.disabPension.modelValue ? '1' : '0',
        seikatuHogo: newValue.lifeProtection.modelValue ? '1' : '0',
        keizai: newValue.economySituation.value ?? '',
        sdNameKnj: newValue.nameConsent.value,
        sdZokugaraKnj: newValue.relationship.value,
        sdAddrKnj: newValue.addressContactInfo.value ?? '',
        shukushoFlg: newValue.familyDiagramZoomOutFlag ? '1' : '0',
        kazokuKankeiIn: newValue.familyRelationEtcSituation.value ?? '',
        sdNameKnj1: newValue.Or31675.line1.name.value,
        sdZokugaraKnj1: newValue.Or31675.line1.relationship.value,
        jyuusyoKnj1: newValue.Or31675.line1.address.value,
        sdNameKnj2: newValue.Or31675.line2.name.value,
        sdZokugaraKnj2: newValue.Or31675.line2.relationship.value,
        jyuusyoKnj2: newValue.Or31675.line2.address.value,
        sdNameKnj3: newValue.Or31675.line3.name.value,
        sdZokugaraKnj3: newValue.Or31675.line3.relationship.value,
        jyuusyoKnj3: newValue.Or31675.line3.address.value,
        sdNameKnj4: newValue.Or31675.line4.name.value,
        sdZokugaraKnj4: newValue.Or31675.line4.relationship.value,
        jyuusyoKnj4: newValue.Or31675.line4.address.value,
      }
    }
  },
  { deep: true }
)
</script>

<template>
  <c-v-sheet
    class="d-flex flex-column"
    style="background: white !important; width: 1080px"
  >
    <!-- 相談経路 -->
    <base-mo01298
      :oneway-model-value="localOneway.mo01298OnewayConsultationRoute"
      class="section-title"
    />
    <c-v-sheet>
      <c-v-row
        no-gutters
        class="border-y-sm pa-6"
      >
        <div class="d-flex align-end ml-6">
          <div>
            <base-mo01338
              :oneway-model-value="localOneway.howToConsultMo01338Oneway"
              class="mb-2"
            />
            <base-mo00039
              v-model="local.data.consulationMethod"
              :oneway-model-value="localOneway.mo00039OnewayConsultationMethod"
            ></base-mo00039>
          </div>

          <g-custom-orX0157
            v-bind="orX0157"
            id="or08207_input_content"
            v-model="local.data.consulationRouteOthers"
            :oneway-model-value="localOneway.consulationRouteOthersOrX0157Oneway"
            @on-click-edit-btn="handleOpenConsultationRouteModal"
          />
        </div>

        <v-divider class="my-6" />

        <div class="d-flex align-end gap-6 ml-6">
          <div class="d-flex flex-column">
            <base-mo01338
              :oneway-model-value="localOneway.numberOfConsultationsMo01338Oneway"
              class="mb-2"
            />
            <base-mo00039
              v-model="local.data.homeVisitKind"
              :oneway-model-value="localOneway.mo00039HomeVisitKind"
            />
          </div>
          <base-mo00020
            v-model="local.data.lastConsultationDate"
            :oneway-model-value="localOneway.Mo00020OnewayLastConsultationDate"
          ></base-mo00020>
        </div>
      </c-v-row>
    </c-v-sheet>

    <!-- 本人の現況 -->
    <base-mo01298
      :oneway-model-value="localOneway.mo01298OnewayPersonPresentCondition"
      class="section-title"
    />
    <c-v-row
      no-gutters
      class="border-y-sm ga-4 pa-6 d-flex align-center"
    >
      <div class="d-flex align-center pa-2 ml-6">
        <base-mo00018
          v-model="local.data.atHome"
          :oneway-model-value="localOneway.mo00018OnewayAtHome"
        ></base-mo00018>
        <base-mo00018
          v-model="local.data.hospitalized"
          :oneway-model-value="localOneway.Mo00018OnewayHospitalized"
        ></base-mo00018>
        <base-mo00018
          v-model="local.data.institutionalized"
          :oneway-model-value="localOneway.mo00018OnewayInstitutionalized"
        ></base-mo00018>
      </div>

      <g-custom-orX0157
        v-bind="orX0157"
        id="or08207_input_content"
        v-model="local.data.presentConditionOthers"
        :oneway-model-value="localOneway.presentConditionOthersOrX0157Oneway"
        @on-click-edit-btn="handleOpenPersonalStatusModal"
      />
    </c-v-row>

    <!-- 日常生活自立度 -->
    <base-mo01298
      :oneway-model-value="localOneway.Mo01298OnewayEverydayLifeIndependenceLevel"
      class="section-title"
    />
    <c-v-sheet class="border-y-sm py-6 ga-4 pa-6">
      <div class="ml-6">
        <base-mo01338
          :oneway-model-value="localOneway.degreeIndepDailyLivingEldDisabMo01338Oneway"
          class="mb-2"
        />
        <base-mo00039
          v-model="local.data.handycapSeniorEverydayLifeIndependenceLevel"
          :oneway-model-value="localOneway.Mo00039OnewayHandycapSeniorEverydayLifeIndependenceLevel"
        ></base-mo00039>
      </div>

      <v-divider class="my-6" />
      <div class="ml-6">
        <base-mo01338
          :oneway-model-value="localOneway.degreeIndepDailyLivingEldDemMo01338Oneway"
          class="mb-2"
        />
        <base-mo00039
          v-model="local.data.degreeIndepDailyLivingEldDem"
          :oneway-model-value="localOneway.Mo00039OnewayDegreeIndepDailyLivingEldDem"
        ></base-mo00039>
      </div>
    </c-v-sheet>

    <!-- 認定・総合事業情報 -->
    <base-mo01298
      :oneway-model-value="localOneway.Mo01298OnewayCertCompBusinessInfo"
      class="section-title"
    />
    <c-v-sheet>
      <c-v-row
        no-gutters
        class="border-y-sm pa-6"
      >
        <div class="d-flex flex-column ml-6">
          <base-mo00615
            :oneway-model-value="localOneway.Mo00615OnewayManager"
            class="mb-2"
          />
          <div class="d-flex align-center">
            <base-mo00040
              v-model="local.data.levelOfCareRequired"
              :oneway-model-value="localOneway.Mo00040OnewayLevelOfCareRequired"
            ></base-mo00040>
          </div>
        </div>
        <v-divider class="my-6" />
        <div class="d-flex flex-column ml-6">
          <base-mo00615
            :oneway-model-value="localOneway.Mo00615OnewayExpirationDate"
            class="mb-2"
          />
          <div class="d-flex align-center custom-input">
            <base-mo00020
              v-model="local.data.validTimeLimitStartDate"
              :oneway-model-value="localOneway.Mo00020OnewayValidTimeLimitStartDate"
            ></base-mo00020>
            <base-mo00615 :oneway-model-value="localOneway.Mo01338OnewayWaveDash" />
            <base-mo00020
              v-model="local.data.validTimeLimitEndDate"
              :oneway-model-value="localOneway.Mo00020OnewayValidTimeLimitEndDate"
            ></base-mo00020>
          </div>
        </div>
        <v-divider class="my-6" />
        <div class="d-flex flex-column ml-6">
          <base-mo00615
            :oneway-model-value="localOneway.Mo00615OnewayPrevCareDegree"
            class="mb-2"
          />
          <base-mo00040
            v-model="local.data.prevCareDegree"
            :oneway-model-value="localOneway.Mo00040OnewayPrevCareDegree"
          ></base-mo00040>
        </div>
        <v-divider class="my-6" />
        <div class="ml-6">
          <base-mo01338
            :oneway-model-value="localOneway.basicChecklistEntryResultsMo01338Oneway"
            class="mb-2"
          />
          <base-mo00039
            v-model="local.data.basicChecklistEntryResult"
            :oneway-model-value="localOneway.Mo00039OnewayBasicChecklistEntryResult"
          ></base-mo00039>
        </div>
        <c-v-col class="pa-2 px-6 custom-input pt-0">
          <base-mo01266
            :oneway-model-value="{
              to: '',
              minWidth: '',
            }"
            @click="handleClickBasicChecklistEntryHistorySelectIcon"
          >
            <base-mo01338 :oneway-model-value="localOneway.basicChecklistEntryDateMo01338Oneway" />
          </base-mo01266>
          <base-mo00020
            v-model="local.data.basicChecklistEntryDate"
            :oneway-model-value="localOneway.Mo00020OnewayBasicChecklistEntryDate"
            class="pl-1"
          ></base-mo00020>
        </c-v-col>
      </c-v-row>
    </c-v-sheet>

    <!-- GUI01074 基本チェックリスト履歴選択画面をポップアップで起動する。 -->
    <g-custom-or-26491
      v-if="isShowDialogOr26491"
      v-bind="Or26491"
      :oneway-model-value="Or26491OnewayModel"
      @on-confirm="(value: string) => handleConfirmOr26491(value)"
    />

    <!-- 障害等認定 -->
    <base-mo01298
      :oneway-model-value="localOneway.Mo01298OnewayDisabCert"
      class="section-title"
    />
    <c-v-sheet
      class="border-y-sm pa-6 pr-0 d-flex align-center"
      style="gap: 24px !important"
    >
      <div class="d-flex align-center ml-6">
        <base-mo00018
          v-model="local.data.disability"
          :oneway-model-value="localOneway.Mo00018OnewayDisability"
        ></base-mo00018>
        <base-mo00040
          v-model="local.data.disabilityLevel"
          :oneway-model-value="localOneway.Mo00040OnewayDisabilityLevel"
        ></base-mo00040>
      </div>
      <div class="d-flex align-center">
        <base-mo00018
          v-model="local.data.upbringing"
          :oneway-model-value="localOneway.Mo00018OnewayUpbringing"
        ></base-mo00018>
        <base-mo00040
          v-model="local.data.remedialEducationGrade"
          :oneway-model-value="localOneway.Mo00040OnewayRemedialEducationGrade"
        ></base-mo00040>
      </div>
      <div class="d-flex align-center">
        <base-mo00018
          v-model="local.data.spirit"
          :oneway-model-value="localOneway.Mo00018OnewaySpirit"
        ></base-mo00018>
        <base-mo00040
          v-model="local.data.mentalGrade"
          :oneway-model-value="localOneway.Mo00040OnewayMentalGrade"
        ></base-mo00040>
      </div>
      <div class="d-flex align-center">
        <base-mo00018
          v-model="local.data.incrudableDisease"
          :oneway-model-value="localOneway.Mo00018OnewayIncurableDisease"
        ></base-mo00018>
      </div>
      <g-custom-orX0157
        v-bind="orX0157"
        id="or08207_input_content"
        v-model="local.data.memo1"
        :oneway-model-value="localOneway.memo1OrX0157Oneway"
        @on-click-edit-btn="onClickMemo1Icon"
      />
      <g-custom-orX0157
        v-bind="orX0157"
        id="or08207_input_content"
        v-model="local.data.memo2"
        :oneway-model-value="localOneway.memo2OrX0157Oneway"
        @on-click-edit-btn="onClickMemo2Icon"
      />
    </c-v-sheet>

    <!-- 本人の住居環境 -->
    <base-mo01298
      :oneway-model-value="localOneway.Mo01298OnewayPersonLivingEnv"
      class="section-title"
    />
    <c-v-sheet class="border-y-sm pa-6 d-flex align-center ga-6">
      <div class="ml-6">
        <base-mo01338
          :oneway-model-value="localOneway.typeOfResidenceMo01338Oneway"
          class="mb-2"
        />
        <div class="d-flex ga-5">
          <base-mo00018
            v-model="local.data.personalHome"
            :oneway-model-value="localOneway.Mo00018OnewayPersonalHome"
          ></base-mo00018>
          <base-mo00018
            v-model="local.data.rentedHouse"
            :oneway-model-value="localOneway.Mo00018OnewayRentedHouse"
          ></base-mo00018>
          <base-mo00018
            v-model="local.data.detachedHouse"
            :oneway-model-value="localOneway.Mo00018OnewayDetachedHouse"
          ></base-mo00018>
          <base-mo00018
            v-model="local.data.apartment"
            :oneway-model-value="localOneway.Mo00018OnewayApartment"
          ></base-mo00018>
        </div>
      </div>

      <div class="d-flex align-end ga-6">
        <div class="d-flex flex-column">
          <base-mo01338
            :oneway-model-value="localOneway.roomAvailabilityMo01338Oneway"
            class="mb-2"
          />
          <base-mo00039
            v-model="local.data.privateRoom"
            :oneway-model-value="localOneway.Mo00039OnewayPrivateRoom"
          ></base-mo00039>
        </div>

        <div class="d-flex align-center pb-0">
          <base-mo00045
            v-model="local.data.privateRoomFloor"
            :oneway-model-value="localOneway.Mo00045OnewayPrivateRoomFloor"
          ></base-mo00045>
          <base-at-label v-bind="localOneway.floorInputAppendLabel"></base-at-label>
        </div>

        <div class="d-flex flex-column">
          <base-mo01338
            :oneway-model-value="localOneway.homeRenovationMo01338Oneway"
            class="mb-2"
          />
          <base-mo00039
            v-model="local.data.housingRepair"
            :oneway-model-value="localOneway.Mo00039OnewayHousingRepair"
          ></base-mo00039>
        </div>
      </div>
    </c-v-sheet>

    <!-- 経済状況 -->
    <base-mo01298
      :oneway-model-value="localOneway.Mo01298OnewayEconomicSituation"
      class="section-title"
    />
    <c-v-sheet class="border-y-sm pa-6 d-flex align-center ga-6">
      <div class="d-flex align-center justify-space-between ml-6">
        <base-mo00018
          v-model="local.data.nationalPension"
          :oneway-model-value="localOneway.Mo00018OnewayNationalPension"
        ></base-mo00018>
        <base-mo00018
          v-model="local.data.welfarePension"
          :oneway-model-value="localOneway.Mo00018OnewayWelfarePension"
        ></base-mo00018>
        <base-mo00018
          v-model="local.data.disabPension"
          :oneway-model-value="localOneway.Mo00018OnewayDisabPension"
        ></base-mo00018>
        <base-mo00018
          v-model="local.data.lifeProtection"
          :oneway-model-value="localOneway.Mo00018OnewayLifeProtection"
        ></base-mo00018>
      </div>
      <g-custom-orX0157
        v-bind="orX0157"
        id="or08207_input_content"
        v-model="local.data.economySituation"
        :oneway-model-value="localOneway.economySituationOrX0157Oneway"
        @on-click-edit-btn="handleClickEconomySituationIcon"
      />
    </c-v-sheet>

    <!-- 来所者(相談者)情報 -->
    <div
      style="cursor: pointer"
      class="section-title-clickable"
      @click="handleClickVisitorConsulteeInfoIcon"
    >
      <base-mo01298
        :oneway-model-value="localOneway.Mo01298OnewayVisitorConsulteeInfo"
        class="section-title"
      />
    </div>
    <c-v-sheet class="border-y-sm pa-6">
      <div class="d-flex flex-row ga-6 ml-6 mb-6">
        <base-mo00045
          v-model="local.data.nameConsent"
          :oneway-model-value="localOneway.Mo00045OnewayNameConsent"
        ></base-mo00045>
        <base-mo00045
          v-model="local.data.relationship"
          :oneway-model-value="localOneway.Mo00045OnewayRelationship"
        ></base-mo00045>
      </div>
      <div class="mx-6">
        <base-mo00045
          v-model="local.data.addressContactInfo"
          :oneway-model-value="localOneway.Mo00045OnewayAddressContactInfo"
        ></base-mo00045>
      </div>
    </c-v-sheet>

    <!-- GUI01073 相談者選択画面をポップアップで起動する。 -->
    <g-custom-or-27633
      v-if="showDialogOr27633"
      v-bind="or27633"
      :unique-cp-id="or27633.uniqueCpId"
      :oneway-model-value="or27633Data"
      @on-confirm="(value: ConsultationUserInfo) => handleConfirmOr27633(value)"
    />

    <!-- 家族構成 -->
    <div
      style="cursor: pointer"
      class="section-title-clickable"
      @click="handleClickFamilyStructureIcon"
    >
      <base-mo01298
        :oneway-model-value="localOneway.Mo01298OnewayFamilyStructure"
        class="section-title"
      />
    </div>
    <c-v-sheet
      class="border-y-sm border-redc"
      style="padding: 24px 48px 36px !important"
    >
      <div class="d-flex justify-end mt-0 mb-2">
        {{ t('label.family-structures-label') }}
      </div>
      <c-v-row
        no-gutters
        class="justify-center"
      >
        <div
          class="rounded-lg"
          style="height: 200px; width: 100%; background: rgb(var(--v-theme-black-50))"
        ></div>
      </c-v-row>
      <div class="ml-0">
        <g-custom-or-x0156
          v-model="local.data.familyRelationEtcSituation"
          :oneway-model-value="localOneway.familyRelationshipX0156Oneway"
          class="memoInputTextArea"
          @on-click-edit-btn.stop="handleClickFamilyRelSituationIcon"
        >
          <template #footer>
            <base-mo00615 :oneway-model-value="localOneway.Mo00615OnewayFamilyRelationship" />
          </template>
        </g-custom-or-x0156>
      </div>
    </c-v-sheet>

    <!-- 緊急連絡先 -->
    <g-custom-or-31675
      v-model="local.data.Or31675"
      :is-copy-flag="localOneway.or08207.isCopyFlag"
    ></g-custom-or-31675>

    <g-custom-or-10883
      v-if="isShowDialogOr10883"
      v-bind="or10883"
      v-model="local.or10883Data"
      :oneway-model-value="Or10883OnewayModel"
    />

    <g-custom-or-27349
      v-if="showDialogOr27349"
      v-bind="or27349"
      :oneway-model-value="Or27349Data"
      @confirm="handleConfirmOr27349"
    />
  </c-v-sheet>
</template>

<style scoped lang="scss">
:deep(.section-header) {
  max-width: 250px;
}

.familyDiagram {
  width: 100%;
  aspect-ratio: 2 / 1;
}
pre.familyDiagramDescriptionLabel {
  font-family: inherit;
}

:deep(.section-title .v-toolbar-title__placeholder) {
  padding-left: 16px;
}

:deep(.v-selection-control-group--inline .v-radio) {
  margin-right: 16px !important;
}

:deep(.section-title-clickable .v-toolbar__content .v-toolbar-title__placeholder) {
  color: rgb(var(--v-theme-blue-700));
  text-decoration: underline;
  cursor: pointer;
}

.custom-input {
  :deep(.v-field--appended) {
    padding-inline-end: 6px;
  }
  :deep(.v-field__input) {
    padding-inline: 12px 4px;
  }
}

:deep(.v-input__details) {
  min-width: 250px;
  max-width: 400px;
  white-space: normal;
  word-break: break-word;
}

:deep(.v-messages__message) {
  white-space: normal;
  word-break: break-word;
  width: 100%;
  max-width: 400px;
  display: block;
}
.hieu-test {
  :deep(.v-input__control) {
    :deep(.v-field--appended) {
      padding-inline-end: 2px !important;
    }
  }
}
</style>
