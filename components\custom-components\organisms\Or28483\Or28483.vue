<script setup lang="ts">
/**
 * Or28483:（利用者基本情報データ②－1日のタイムテーブル）1日のスケジュール一表
 * GUI01068_介護予防に関する事項
 *
 * @description
 * （利用者基本情報データ②－1日のタイムテーブル）1日のスケジュール一表
 *
 * <AUTHOR> PHAM TIEN THANH
 */
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28148Logic } from '../Or28148/Or28148.logic'
import { Or28148Const } from '../Or28148/Or28148.constants'
import { Or10883Const } from '../Or10883/Or10883.constants'
import { Or10883Logic } from '../Or10883/Or10883.logic'
import { Or28483Const } from './Or28483.constants'
import type { TableData } from './Or28483.type'
import { useSetupChildProps, useScreenUtils } from '#imports'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { CustomClass } from '~/types/CustomClassType'
import type {
  Or28483Type,
  Or28483OnewayType,
  dailyScheduleItem,
} from '~/types/cmn/business/components/Or28483Type'
import type { Or26317OnewayType, Or26317Type } from '~/types/cmn/business/components/Or26317Type'
import type { OrX0164OnewayType, OrX0164Type } from '~/types/cmn/business/components/OrX0164Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  DailyScheduleSelectInEntity,
  DailyScheduleSelectOutEntity,
} from '~/repositories/cmn/entities/DailyScheduleSelectEntity'
import type {
  Or28148OnewayType,
  Or28148TowwayType,
} from '~/types/cmn/business/components/Or28148Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { DIALOG_BTN } from '~/constants/classification-constants'
import type { Mo01376OnewayType, Mo01376Type } from '~/types/business/components/Mo01376Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'
import { useCmnCom } from '~/utils/useCmnCom'

const { t } = useI18n()

const { setChildCpBinds, getChildCpBinds } = useScreenUtils()

// 共通関数を取得
const { isOverLimitByCharWidth } = useCmnCom()

// 文字数制限チェック関数
const checkCharLimit = (value: string, maxLength: number): boolean => {
  if (maxLength === Or28483Const.ZERO) return true
  return !isOverLimitByCharWidth(value, maxLength)
}

// 前の値を保存するための変数
const previousValues = ref({
  honinKnj: '',
  kaigoshaKnj: '',
})

// 文字数制限付きの値更新処理
const updateValueWithLimit = async (
  field: keyof typeof previousValues.value,
  newValue: string,
  itemIndex: number
) => {
  // 編集が許可されていない場合は処理を停止
  if (!localOneway.Or28483.isAllowEdit) return false
  const maxLength = Or28483Const.FOUR_THOUSAND

  if (newValue && !checkCharLimit(newValue, maxLength)) {
    // 制限を超えた場合は、前の値に戻す
    await nextTick()
    if (field === Or28483Const.HONIN_KNJ) {
      tableData.value.dailyScheduleList[itemIndex].honinKnj.modelValue.value =
        previousValues.value.honinKnj
    } else if (field === Or28483Const.KAIGOSHA_KNJ) {
      tableData.value.dailyScheduleList[itemIndex].kaigoshaKnj.modelValue.value =
        previousValues.value.kaigoshaKnj
    }
    return false
  }

  // 制限内の場合は、前の値を更新してから新しい値を設定
  if (field === Or28483Const.HONIN_KNJ) {
    previousValues.value.honinKnj = newValue
    tableData.value.dailyScheduleList[itemIndex].honinKnj.modelValue.value = newValue
  } else if (field === Or28483Const.KAIGOSHA_KNJ) {
    previousValues.value.kaigoshaKnj = newValue
    tableData.value.dailyScheduleList[itemIndex].kaigoshaKnj.modelValue.value = newValue
  }
  return true
}

const columnMinWidth = ref<number[]>([50, 140, 320, 510])

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue?: Or28483Type
  onewayModelValue?: Or28483OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const or28148 = ref({ uniqueCpId: '' })

const or21814_1 = ref({ uniqueCpId: '' })

const or10883 = ref({ uniqueCpId: '' })

// Or10883のonewayモデルを初期化するための変数
const or10883OnewayModel = ref<Or10883OnewayType>({
  userId: '',
  t1Cd: '',
  t2Cd: '',
  t3Cd: '',
  inputContents: '',
  title: '',
  historyTableName: '',
  historyColumnName: '',
})

// 現在選択されている項目を示す変数
let itemSelected = 0

// Or10883Type用のref
const or10883Type = ref<Or10883TwowayType>({})

const mo01376Oneway = ref<Mo01376OnewayType>({
  inputMode: true,
  rangeInputMode: true,
  rangeHhFlg: false,
})

const localOneway = reactive({
  Or28483: {
    ...props.onewayModelValue,
    basicInfoId: '123',
    screenId: 'GUI01068',
  },
  mo00609Oneway: {
    btnLabel: t('label.display-order-delete'),
    variant: 'outlined',
  } as Mo00609OnewayType,
  // 行追加ボタン
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    tooltipText: t('tooltip.add-row'),
  } as Mo00611OnewayType,
  // 行挿入ボタン
  mo00611OnewayInsert: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    tooltipText: t('tooltip.insert-row'),
  } as Mo00611OnewayType,
  // 行複写ボタン
  mo00611OnewayDuplicate: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'content_copy',
    tooltipText: t('tooltip.duplicate-row'),
  } as Mo00611OnewayType,
  // 行削除ボタン
  mo00611OnewayDelete: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    tooltipText: t('tooltip.delete-row'),
    color: 'red-700',
    labelColor: 'rgb(var(--v-theme-red-700))',
  } as Mo00611OnewayType,
  // 表示順ボタン
  mo00611OnewayDisplayOrder: {
    btnLabel: t('label.display-order'),
    tooltipText: t('tooltip.display-order'),
    minWidth: '60px',
  } as Mo00611OnewayType,
  mo00009OnewaySchedule: {
    density: 'compact',
    rounded: 'sm',
    size: '14px',
    btnIcon: 'schedule',
  },

  mo01375Oneway: {
    rangeHhFlg: false,
  },
})

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

// 全選択状態
const selectAll = ref(false)

const tableData = ref<TableData>({
  dailyScheduleList: [],
})

const headers = [
  {
    title: '',
    key: 'checkbox',
    sortable: false,
    width: '48px',
  },
  {
    title: t('label.time'),
    key: 'time',
    sortable: false,
    width: '200px',
  },
  {
    title: t('label.person'),
    key: 'person',
    sortable: false,
    width: '300px',
  },
  {
    title: t('label.caregiver-family'),
    key: 'family',
    sortable: false,
    width: '300px',
  },
].filter((x) => !!x)

const startTime = ref<string>('')
const endTime = ref<string>('')

const mo01375 = ref({
  value: '',
  showMenu: false,
})

const mo01376 = ref<Mo01376Type>({
  value: startTime.value ?? '',
  valueHour: getValueHour(startTime.value ?? '', 'hour'),
  valueMinute: getValueHour(startTime.value ?? '', 'minutes'),
  valueTo: endTime.value ?? '',
  valueHourTo: getValueHour(endTime.value ?? '', 'hour'),
  valueMinuteTo: getValueHour(endTime.value ?? '', 'minutes'),
  mo00024: {
    isOpen: false,
  },
  mo00038HourTens: {
    mo00045: {
      value: '0',
    },
  },
  mo00038HourOnes: {
    mo00045: {
      value: '0',
    },
  },
  mo00038MinuteTens: {
    mo00045: {
      value: '0',
    },
  },
  mo00038MinuteOnes: {
    mo00045: {
      value: '0',
    },
  },
})

const showDialogMo01376 = computed(() => {
  return mo01376.value.mo00024.isOpen ?? false
})

watch(
  () => mo01376.value,
  (newValue) => {
    startTime.value = newValue.value
    endTime.value = newValue.valueTo ?? ''
  }
)

watch(
  () => endTime.value,
  (newValue) => {
    mo01376.value.valueTo = newValue ?? ''
    mo01376.value.valueHourTo = getValueHour(newValue ?? '', 'hour')
    mo01376.value.valueMinuteTo = getValueHour(newValue ?? '', 'minutes')
  }
)

watch(
  () => startTime.value,
  (newValue) => {
    mo01376.value.value = newValue ?? ''
    mo01376.value.valueHour = getValueHour(newValue ?? '', 'hour')
    mo01376.value.valueMinute = getValueHour(newValue ?? '', 'minutes')
  }
)

watch(
  () => mo01375.value,
  (newValue) => {
    // 編集が許可されていない場合は処理を停止
    if (!localOneway.Or28483.isAllowEdit) return
    // 選択中の行の時間値を更新する
    if (
      selectedItemIndex.value >= 0 &&
      tableData.value.dailyScheduleList[selectedItemIndex.value]
    ) {
      const selectedItem = tableData.value.dailyScheduleList[selectedItemIndex.value]
      selectedItem.jikanKnj.modelValue.value = newValue.value
    }
  }
)

watch(
  () => mo01376.value,
  (newValue) => {
    startTime.value = newValue.value
    endTime.value = newValue.valueTo ?? ''

    // 編集が許可されていない場合は処理を停止
    if (!localOneway.Or28483.isAllowEdit) return

    // 選択中の行の時間値を更新する（範囲ありの場合）
    if (
      selectedItemIndex.value >= 0 &&
      tableData.value.dailyScheduleList[selectedItemIndex.value]
    ) {
      const selectedItem = tableData.value.dailyScheduleList[selectedItemIndex.value]
      selectedItem.jikanKnj.modelValue.value = `${newValue.value}～${newValue.valueTo ?? ''}`
    }
  }
)

const isShowDialogOr28148 = computed(() => {
  return Or28148Logic.state.get(or28148.value.uniqueCpId)?.isOpen ?? false
})

// 部分選択状態（indeterminate）
const isIndeterminate = computed(() => {
  const selectedCount = tableData.value.dailyScheduleList.filter(
    (item) => item.checkbox?.modelValue?.modelValue
  ).length
  const totalCount = tableData.value.dailyScheduleList.length
  return selectedCount > 0 && selectedCount < totalCount
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

// Or10883 ダイアログを表示するかどうかを決定する計算プロパティ
const showDialog = computed(() => {
  return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or10883Const.CP_ID(0)]: or10883.value,
  [Or28148Const.CP_ID(0)]: or28148.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
})

const mo01338OnewaySelectionRow = ref<Mo01338OnewayType>({
  value: '',
  customClass: new CustomClass({
    itemClass: '',
  }),
})

onMounted(async () => {
  const response = await getDailyScheduleList()
  if (response && response.getDailyScheduleList?.length > 0) {
    for (const data of response.getDailyScheduleList) {
      const item = createDailyScheduleItem(data)
      tableData.value.dailyScheduleList.push(item)
    }
    selectRow(0)
  }
})

/**
 * 新しいレコードを追加
 *
 * @param data - 日のスケジュールモーダル
 *
 * @returns データ情報
 */
function createDailyScheduleItem(data?: dailyScheduleItem) {
  return {
    issuingId: data?.issuingId,
    seq: data?.seq,
    // チェックボックス
    checkbox: {
      modelValue: {
        modelValue: data?.checkbox ?? false,
      } as Mo00018Type,
      onewayModelValue: {
        checkboxLabel: '',
        showItemLabel: false,
        disabled: false,
        readonly: false,
      } as Mo00018OnewayType,
    },
    jikanKnj: {
      modelValue: {
        value: data?.jikanKnj ?? '',
      } as Or26317Type,
      onewayModelValue: {
        showOptionMenu: true,
        canClick: true,
        selectionMode: true,
        disabled: false,
        readonly: false,
        mo01376Oneway: {
          inputMode: true,
          rangeInputMode: true,
          rangeHhFlg: false,
          value: '',
          valueTo: '',
        },
        mo01375Oneway: {
          rangeHhFlg: false,
        },
      } as Or26317OnewayType,
    },
    // 本人
    honinKnj: {
      modelValue: {
        value: data?.honinKnj ?? '',
      } as OrX0164Type,
      onewayModelValue: {
        inputMode: 'TextOnly',
        showEditBtnFlg: true,
        disabled: false,
        readonly: false,
      } as OrX0164OnewayType,
    },
    // 介護者・家族
    kaigoshaKnj: {
      modelValue: {
        value: data?.kaigoshaKnj?.toString() ?? '',
      } as OrX0164Type,
      onewayModelValue: {
        inputMode: 'TextOnly',
        showEditBtnFlg: true,
        disabled: false,
        readonly: false,
      } as OrX0164OnewayType,
    },
  }
}

/**
 * Or10883の入力支援画面確定処理
 *
 * @description
 * 入力支援画面の呼び出し元コンポーネントに値を設定する。
 * またOr10883のボタン押下フラグをリセットする。
 */
watch(
  () => or10883Type.value,
  async () => {
    const naiyo = or10883Type.value.naiyo ?? ''

    switch (itemSelected) {
      case 1: {
        // 本人の場合
        if (selectedItemIndex.value >= 0) {
          if (naiyo && !(await updateValueWithLimit('honinKnj', naiyo, selectedItemIndex.value))) {
            return
          }
        }
        break
      }
      case 2: {
        // 介護者・家族の場合
        if (selectedItemIndex.value >= 0) {
          if (
            naiyo &&
            !(await updateValueWithLimit('kaigoshaKnj', naiyo, selectedItemIndex.value))
          ) {
            return
          }
        }
        break
      }
    }

    // 選択状態をリセット
    itemSelected = 0

    // Or10883の状態をリセット
    Or10883Logic.state.set({
      uniqueCpId: or10883.value.uniqueCpId,
      state: { isOpen: false },
    })
  }
)

watch([() => selectedItemIndex.value, () => tableData.value.dailyScheduleList.length], () => {
  mo01338OnewaySelectionRow.value.value =
    tableData.value.dailyScheduleList.length + Or28483Const.COUNT
})

// 個別選択時に全選択状態を同期
watch(
  () =>
    tableData.value.dailyScheduleList.map((item) => item.checkbox?.modelValue?.modelValue ?? false),
  (values) => {
    selectAll.value = values.length > 0 && values.every((v) => v === true)
  }
)

watch(
  () => tableData.value.dailyScheduleList,
  (newValue) => {
    const respData: Or28483Type = {
      getDailyScheduleList: [],
    }

    if (newValue && newValue.length > 0) {
      for (const item of newValue) {
        const data: dailyScheduleItem = {
          ...item,
          checkbox: item.checkbox?.modelValue?.modelValue,
          jikanKnj: item.jikanKnj?.modelValue?.value,
          honinKnj: item.honinKnj?.modelValue?.value,
          kaigoshaKnj: item.kaigoshaKnj?.modelValue?.value,
        }
        respData.getDailyScheduleList.push(data)
      }
    }

    emit('update:modelValue', respData)
  },
  { deep: true }
)

/**
 * API入力パラメータ「基本情報ID」より、「1日のスケジュールリスト」初期情報を取得する
 *
 *  @returns 返却データ
 */
async function getDailyScheduleList() {
  const inputData: DailyScheduleSelectInEntity = {
    // 基本情報ID
    khn11_id: localOneway.Or28483.basicInfoId,
  }

  // バックエンドAPIから初期情報取得
  const ret: DailyScheduleSelectOutEntity = await ScreenRepository.select(
    'getDailyScheduleSelect',
    inputData
  )
  return ret.data
}

/**
 * 行追加ボタン
 */
function addItem() {
  // 編集が許可されていない場合は処理を停止
  if (!localOneway.Or28483.isAllowEdit) return

  const item = createDailyScheduleItem()
  tableData.value.dailyScheduleList.push(item)
  selectRow(tableData.value.dailyScheduleList.length - 1)

  // 追加行の背景がハイライト表示
}

/**
 * 行挿入ボタン
 */
function insertItem() {
  // 編集が許可されていない場合は処理を停止
  if (!localOneway.Or28483.isAllowEdit) return

  if (tableData.value.dailyScheduleList.length === Or28483Const.DEFAULT.ZERO_VALUE) {
    // 1日のスケジュールリストがない場合、新規追加する
    const item = createDailyScheduleItem()
    tableData.value.dailyScheduleList.push(item)
    selectRow(tableData.value.dailyScheduleList.length - 1)
  } else {
    // 1日のスケジュールリストがある場合、選んだ行の上に追加する
    const newItem = createDailyScheduleItem()
    tableData.value.dailyScheduleList.splice(selectedItemIndex.value, 0, newItem)
  }

  // 追加行の背景がハイライト表示
}

/**
 * 行複写ボタン
 */
function duplicateItem() {
  // 編集が許可されていない場合は処理を停止
  if (!localOneway.Or28483.isAllowEdit) return

  // 1日のスケジュールリストがない場合、利用不可
  if (tableData.value.dailyScheduleList.length === Or28483Const.DEFAULT.ZERO_VALUE) return

  // 1日のスケジュールリストがある場合、選んだ行の下に選んだ内容をコピーする
  const item = tableData.value.dailyScheduleList[selectedItemIndex.value]
  const newItem = createDailyScheduleItem({
    issuingId: item.issuingId,
    seq: item.seq,
    checkbox: item.checkbox?.modelValue?.modelValue,
    jikanKnj: item.jikanKnj.modelValue.value,
    honinKnj: item.honinKnj.modelValue.value,
    kaigoshaKnj: item.kaigoshaKnj.modelValue.value,
  })
  tableData.value.dailyScheduleList.splice(selectedItemIndex.value + 1, 0, newItem)
  selectRow(selectedItemIndex.value + 1)

  // 追加行の背景がハイライト表示
}

/**
 * 行削除ボタン
 */
async function deleteItem() {
  if (!localOneway.Or28483.isAllowEdit) return
  if (tableData.value.dailyScheduleList.length === Or28483Const.DEFAULT.ZERO_VALUE) return

  // チェックボックスが選択されている場合、選択されている行を削除する
  const hasAnyChecked = tableData.value.dailyScheduleList.some(
    (r) => r.checkbox?.modelValue?.modelValue
  )
  if (hasAnyChecked) {
    await deleteSelectedItems()
    return
  }

  // 選択中の行を削除する前に確認ダイアログを表示
  const dialogResult = await showOr21814MsgTwoBtn(t('message.i-cmn-10878'))
  switch (dialogResult) {
    case DIALOG_BTN.YES: {
      tableData.value.dailyScheduleList.splice(selectedItemIndex.value, 1)
      if (tableData.value.dailyScheduleList.length === Or28483Const.DEFAULT.ZERO_VALUE) {
        selectRow(-1)
      }
      if (selectedItemIndex.value === tableData.value.dailyScheduleList.length) {
        selectRow(selectedItemIndex.value - 1)
      }
      break
    }
    case DIALOG_BTN.NO:
      break
  }
}

const or28148Data: Or28148OnewayType = {
  indexList: [],
  category: '',
}

/**
 * 表示順
 */
function openGUI01066() {
  // 編集が許可されていない場合は処理を停止
  if (!localOneway.Or28483.isAllowEdit) return

  or28148Data.indexList = []
  tableData.value.dailyScheduleList.forEach((item) => {
    or28148Data.indexList.push({
      id: item?.issuingId ?? null,
      sort: item?.seq ?? null,
      time: item.jikanKnj.modelValue?.value ?? '',
      person: item.honinKnj.modelValue?.value ?? '',
      caregiverFamily: item.kaigoshaKnj.modelValue?.value ?? '',
      checkbox: item.checkbox?.modelValue?.modelValue ?? false,
    })
  })

  setChildCpBinds(props.uniqueCpId, {
    Or28148: {
      twoWayValue: {
        indexList: or28148Data.indexList,
        category: or28148Data.category,
      },
    },
  })

  Or28148Logic.state.set({
    uniqueCpId: or28148.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援「本人」画面を起動し、当該行の（列内容）本人に戻り値を設定する
 *
 * @param action - 活動
 *
 * @param index - 行のインデックス（optional）
 *
 * @returns 画面が開いているかどうか、またはundefined
 */
function openOr10883(action: string, index?: number) {
  // 編集が許可されていない場合は処理を停止
  if (!localOneway.Or28483.isAllowEdit) return

  const rowIndex = index ?? selectedItemIndex.value
  if (rowIndex === Or28483Const.DEFAULT.UNSELECTED) return

  const selectedItem = tableData.value.dailyScheduleList[rowIndex]
  let inputContents = ''

  if (action === Or28483Const.PERSON) {
    // 本人の場合
    itemSelected = 1
    inputContents = selectedItem.honinKnj.modelValue.value || ''
    selectedItemIndex.value = rowIndex

    or10883OnewayModel.value = {
      userId: '123', // システム共有情報から取得する必要があります
      t1Cd: '20',
      t2Cd: '2',
      t3Cd: '6',
      inputContents: inputContents,
      title: t('label.person'),
      historyTableName: 'kyc_tuc_khn132',
      historyColumnName: 'honin_knj',
    }

    Or10883Logic.state.set({
      uniqueCpId: or10883.value.uniqueCpId,
      state: { isOpen: true },
    })

    return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
  } else if (action === Or28483Const.FAMILY) {
    // 介護者・家族の場合
    itemSelected = 2
    inputContents = selectedItem.kaigoshaKnj.modelValue.value || ''
    selectedItemIndex.value = rowIndex

    or10883OnewayModel.value = {
      userId: '123', // システム共有情報から取得する必要があります
      t1Cd: '20',
      t2Cd: '2',
      t3Cd: '7',
      inputContents: inputContents,
      title: t('label.caregiver-family'),
      historyTableName: 'kyc_tuc_khn132',
      historyColumnName: 'kaigosha_knj',
    }

    Or10883Logic.state.set({
      uniqueCpId: or10883.value.uniqueCpId,
      state: { isOpen: true },
    })

    return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
  }
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  // 編集が許可されていない場合は選択を無効化
  if (!localOneway.Or28483.isAllowEdit) return

  selectedItemIndex.value = index
}

/**
 * メッセージの開閉
 *
 * @param text - message
 *
 * @returns ダイアログの選択結果（yes, no）
 */
async function showOr21814MsgTwoBtn(text: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: text,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })

  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = DIALOG_BTN.NO as typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO

        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.thirdBtnClickFlg) {
          result = DIALOG_BTN.NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

watch(
  () => Or28148Logic.event.get(or28148.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.comfirmFlg) {
      const data = getChildCpBinds(props.uniqueCpId, {
        Or28148: { cpPath: 'Or28148', twoWayFlg: true },
      }).Or28148.twoWayBind?.value as Or28148TowwayType
      if (data.indexList && data.indexList?.length > 0) {
        tableData.value.dailyScheduleList = []
        for (const dataRow of data.indexList) {
          const item = createDailyScheduleItem({
            issuingId: dataRow?.id,
            seq: dataRow?.sort,
            checkbox: dataRow?.checkbox ?? false,
            jikanKnj: dataRow?.time,
            honinKnj: dataRow?.person,
            kaigoshaKnj: dataRow?.caregiverFamily,
          })
          tableData.value.dailyScheduleList.push(item)
        }
        selectRow(0)
      }

      Or28148Logic.event.set({
        uniqueCpId: or28148.value.uniqueCpId,
        events: { comfirmFlg: false },
      })
    }
  }
)

/**
 * 全選択・全解除アクション
 *
 * @param val - 全選択状態（true: 全選択, false: 全解除）
 */
const onCheckAll = (val: boolean) => {
  selectAll.value = val
  tableData.value.dailyScheduleList.forEach((item) => {
    if (item.checkbox?.modelValue) {
      item.checkbox.modelValue.modelValue = val
    }
  })
}

/**
 * 個別チェックボックス変更処理
 *
 * @param index - 行のインデックス
 *
 * @param value - チェック状態
 */
const onChangeCheckBox = (index: number, value: boolean) => {
  const row = tableData.value.dailyScheduleList[index]
  if (!row?.checkbox?.modelValue) return
  row.checkbox.modelValue.modelValue = value
}

/**
 * 取得値時間
 *
 * @param value - 値
 *
 * @param type - 型
 *
 * @returns type
 */
function getValueHour(value: string, type: string) {
  if (!value) return ''
  const [hour, minutes] = value.split(':')
  return type === Or28483Const.TYPES.MINUTES ? minutes : hour
}

/**
 * 選択した行を削除
 */
async function deleteSelectedItems() {
  // 編集不可は無視
  if (!localOneway.Or28483.isAllowEdit) return

  // チェックされたインデックスを取得
  const selectedIndexes = tableData.value.dailyScheduleList
    .map((it, idx) => (it.checkbox?.modelValue?.modelValue ? idx : -1))
    .filter((idx) => idx !== -1)

  if (selectedIndexes.length === 0) return

  // 確認
  const dialogResult = await showOr21814MsgTwoBtn(t('message.i-cmn-10878'))
  switch (dialogResult) {
    case DIALOG_BTN.YES: {
      for (let i = selectedIndexes.length - 1; i >= 0; i--) {
        tableData.value.dailyScheduleList.splice(selectedIndexes[i], 1)
      }
      if (tableData.value.dailyScheduleList.length === 0) {
        selectRow(-1)
      } else if (selectedItemIndex.value >= tableData.value.dailyScheduleList.length) {
        selectRow(tableData.value.dailyScheduleList.length - 1)
      }

      selectAll.value = false
      break
    }
    case DIALOG_BTN.NO:
      break
  }
}
</script>

<template>
  <div
    class="or28483-component"
    style="
      background-color: rgb(var(--v-theme-secondaryBackground));
      padding: 12px 12px 12px 24px;
      border-radius: 4px;
    "
  >
    <c-v-row no-gutters>
      <c-v-col
        cols="12"
        class="d-flex pa-0 mt-2"
        align-items="center"
      >
        <div>
          <!-- 行追加ボタン -->
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OnewayAdd"
            :disabled="
              localOneway.Or28483.screenId == Or28483Const.SCREEN_GUI01071 ||
              !localOneway.Or28483.isAllowEdit
            "
            class="mr-2"
            @click="addItem"
          >
          </base-mo00611>
          <!-- 行挿入ボタン -->
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OnewayInsert"
            :disabled="
              localOneway.Or28483.screenId == Or28483Const.SCREEN_GUI01071 ||
              !localOneway.Or28483.isAllowEdit
            "
            class="mr-2"
            @click="insertItem"
          >
          </base-mo00611>
          <!-- 行複写ボタン -->
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OnewayDuplicate"
            :disabled="
              localOneway.Or28483.screenId == Or28483Const.SCREEN_GUI01071 ||
              !localOneway.Or28483.isAllowEdit
            "
            class="mr-2"
            @click="duplicateItem"
          >
          </base-mo00611>
          <!-- 行削除ボタン -->
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OnewayDelete"
            :disabled="
              localOneway.Or28483.screenId == Or28483Const.SCREEN_GUI01071 ||
              !localOneway.Or28483.isAllowEdit
            "
            @click="deleteItem"
          >
          </base-mo00611>
        </div>
        <div class="d-flex align-items-center ml-auto mr-3">
          <!-- 表示順ボタン -->
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OnewayDisplayOrder"
            :disabled="
              localOneway.Or28483.screenId == Or28483Const.SCREEN_GUI01071 ||
              !localOneway.Or28483.isAllowEdit
            "
            class="mr-4 p-0"
            @click="openGUI01066"
          />
          <g-custom-or-28148
            v-if="isShowDialogOr28148"
            v-bind="or28148"
            :oneway-model-value="or28148Data"
          />
          <!-- 行選択上下ラベル -->
          <base-mo01338
            :oneway-model-value="mo01338OnewaySelectionRow"
            class="d-flex"
          />
        </div>
      </c-v-col>
    </c-v-row>
    <c-v-row no-gutters>
      <c-v-col
        cols="12"
        class="d-flex pa-0 mt-4"
        align-items="center"
      >
        <c-v-data-table
          v-resizable-grid="{ columnWidths: columnMinWidth }"
          :headers="headers"
          class="table-header table-wrapper list-wrapper"
          style="width: 100%"
          hide-default-footer
          fixed-header
          :items-per-page="-1"
          :items="tableData.dailyScheduleList"
        >
          <template #headers>
            <tr>
              <th
                v-for="(item, index) in headers"
                :key="index"
                :class="item.key === 'checkbox' ? 'table-checkbox' : ''"
                :style="
                  item.key === 'checkbox' ? 'width: 48px; padding: 0px 4px; height: 42px' : ''
                "
              >
                <base-at-checkbox
                  v-if="item.key === 'checkbox'"
                  :indeterminate="isIndeterminate"
                  :class="isIndeterminate ? 'indeterminate' : ''"
                  :model-value="selectAll"
                  checkbox-label=""
                  class="d-flex"
                  @update:model-value="onCheckAll"
                />
                <span v-else>{{ item.title }}</span>
              </th>
            </tr>
          </template>
          <template #item="{ item, index }">
            <tr
              :key="`row-${item.issuingId || index}-${item.seq || index}`"
              :class="{ 'select-row': selectedItemIndex === index }"
              @click="localOneway.Or28483.isAllowEdit ? selectRow(index) : null"
            >
              <!-- チェックボックス -->
              <td
                class="table-checkbox pa-0"
                style="padding: 0px 4px !important"
              >
                <base-at-checkbox
                  :model-value="item.checkbox?.modelValue?.modelValue"
                  checkbox-label=""
                  class="d-flex"
                  @update:model-value="(v: boolean) => onChangeCheckBox(index, v)"
                />
              </td>
              <!-- 時間 -->
              <td class="pa-0">
                <g-custom-or26317
                  :key="`time-${item.issuingId || index}-${item.seq || index}`"
                  v-model="item.jikanKnj.modelValue"
                  :oneway-model-value="{
                    ...item.jikanKnj.onewayModelValue,
                    disabled:
                      localOneway.Or28483.screenId == Or28483Const.SCREEN_GUI01071 ||
                      !localOneway.Or28483.isAllowEdit,
                  }"
                />
              </td>
              <!-- 本人 -->
              <td class="pa-0">
                <g-custom-or-x0164
                  v-model="item.honinKnj.modelValue"
                  :oneway-model-value="{
                    ...item.honinKnj.onewayModelValue,
                    disabled:
                      localOneway.Or28483.screenId == Or28483Const.SCREEN_GUI01071 ||
                      !localOneway.Or28483.isAllowEdit,
                  }"
                  @on-click-edit-btn="openOr10883(Or28483Const.PERSON, index)"
                  @click="localOneway.Or28483.isAllowEdit ? selectRow(index) : null"
                />
              </td>
              <!-- 介護者・家族 -->
              <td class="pa-0">
                <g-custom-or-x0164
                  v-model="item.kaigoshaKnj.modelValue"
                  :oneway-model-value="{
                    ...item.kaigoshaKnj.onewayModelValue,
                    disabled:
                      localOneway.Or28483.screenId == Or28483Const.SCREEN_GUI01071 ||
                      !localOneway.Or28483.isAllowEdit,
                  }"
                  @on-click-edit-btn="openOr10883(Or28483Const.FAMILY, index)"
                  @click="localOneway.Or28483.isAllowEdit ? selectRow(index) : null"
                />
              </td>
            </tr>
          </template>
        </c-v-data-table>
      </c-v-col>
    </c-v-row>
    <g-base-or21814 v-bind="or21814_1"> </g-base-or21814>
    <g-custom-or10883
      v-if="showDialog"
      v-bind="or10883"
      v-model="or10883Type"
      :oneway-model-value="or10883OnewayModel"
    />
  </div>

  <base-mo01376
    v-if="showDialogMo01376"
    v-model="mo01376"
    :oneway-model-value="mo01376Oneway"
  />
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';
@use '@/styles/cmn/page-data-table.scss';

:deep(.table-header tbody tr td) {
  vertical-align: middle;
}

:deep(.table-header th) {
  font-weight: normal !important;
}

.align-items-center {
  align-items: center;
}

:deep(.table-wrapper) {
  width: 100% !important;
  .v-table__wrapper {
    width: 100% !important;
    height: 280px;

    td {
      padding: 0 !important;
      height: 48px !important;
      textarea {
        padding: 0px 12px !important;
      }
    }
    th {
      background-color: rgb(var(--v-theme-blue-100)) !important;
      height: 32px !important;
    }
  }
}

.or28483-component {
  width: 100% !important;
  max-width: 100% !important;
}

:deep(.footer) {
  z-index: 99999 !important;
  position: fixed !important;
}

:deep(.footer-menu) {
  z-index: 99999 !important;
}

:deep(.table-wrapper td) {
  .table-field-container {
    height: 100% !important;
    min-height: 32px !important;
  }
}

:deep(.table-wrapper td:first-child) {
  .table-kikan-cell {
    justify-content: flex-start !important;
    margin-left: 0 !important;

    .table-kikan-content {
      padding: 0 16px 0 0 !important;
    }
  }
}

:deep(
  .table-wrapper
    .v-table__wrapper
    td:has(input:not([readonly]):not([disabled]):not([type='checkbox']):not([type='radio']))
    input
) {
  outline: none !important;
  border-radius: 0 !important;
}

:deep(
  .table-wrapper
    .v-table__wrapper
    td:has(input:not([readonly]):not([disabled]):not([type='checkbox']):not([type='radio']))
    input:focus
) {
  outline: 1px solid rgb(var(--v-theme-key)) !important;
  border-radius: 0 !important;
}
</style>
