/**
 * GUI00838_ ｱｾｽﾒﾝﾄ複写］「包括」画面ダイアログインティティ
 * 有機体_Or59423
 *
 * @description
 * ［ｱｾｽﾒﾝﾄ複写］「包括」画面ダイアログインティティ
 *
 * <AUTHOR>
 */

import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 初期情報取得リクエストパラメータタイプ
 */
export interface DuplicateAssessmentSelectInEntity extends InWebEntity {
  /**
   * 事業者IDリスト
   */
  svJigyoIdList: string
  /**
   * 利用者ID
   */
  userid: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * 期間管理フラグ
   */
  kikanFlg: string
}

/**
 * 初期情報取得レスポンスパラメータタイプ
 */
export interface DuplicateAssessmentSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 計画期間情報
     */
    planPeriodInfo: {
      /**
       * 期間ID
       */
      sc1Id: string
      /**
       * 期間総件数
       */
      periodCnt?: string
      /**
       * 開始日
       */
      startYmd?: string
      /**
       * 終了日
       */
      endYmd?: string
      /**
       * 更新回数
       */
      modifiedCnt?: string
      /**
       * 事業者名称ID
       */
      svJigyoId: string
      /**
       * 事業者名称(略称)
       */
      jigyoRyakuKnj: string
    }[]

    /**
     * 履歴情報リスト
     */
    rirekiInfo: {
      /**
       * ケアチェックID
       */
      cc1Id: string
      /**
       * 計画期間ID
       */
      sc1Id: string
      /**
       * 作成日
       */
      createYmd: string
      /**
       * 職員ID
       */
      shokuId: string
      /**
       * 作成者
       */
      shokuKnj: string
      /**
       * 事業者名称ID
       */
      svJigyoId: string
      /**
       * 事業者名称(略称)
       */
      jigyoRyakuKnj: string
    }[]

    /**
     * 複数リスト
     */
    multipleList: {
      /**
       * インデックス
       */
      [key: string]: string | undefined
      /**
       * dmySelAss1Flg
       */
      dmySelAss1Flg?: string
      /**
       * dmySelAss2Flg
       */
      dmySelAss2Flg?: string
      /**
       * dmySelAss3Flg
       */
      dmySelAss3Flg?: string
      /**
       * dmySelAss4Flg
       */
      dmySelAss4Flg?: string
      /**
       * dmySelAss5Flg
       */
      dmySelAss5Flg?: string
      /**
       * dmySelAss6Flg
       */
      dmySelAss6Flg?: string
      /**
       * dmySelAss7Flg
       */
      dmySelAss7Flg?: string
      /**
       * ＨＫ＿ケアチェック表（ヘッダ）の更新回数
       */
      hcc1ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（１．食事）の更新回数
       */
      hcc21ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（２．排泄）の更新回数
       */
      hcc22ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（３．入浴）の更新回数
       */
      hcc23ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（４．洗面）の更新回数
       */
      hcc24ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（５．基本）の更新回数
       */
      hcc25ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（６．医療）の更新回数
       */
      hcc26ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（７．心理）の更新回数
       */
      hcc27ModifiedCnt?: string
      /**
       * 第二ＨＫ＿ケアチェック表（ヘッダ）の更新回数
       */
      sechcc1ModifiedCnt?: string
    }[]
  }
}

/**
 * 複数情報取得リクエストパラメータタイプ
 */
export interface DuplicateHistoryChangeSelectInEntity extends InWebEntity {
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * ケアチェックID
   */
  defGdlId: string
}

/**
 * 複数情報取得レスポンスパラメータタイプ
 */
export interface DuplicateHistoryChangeSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 複数リスト
     */
    multipleList: {
      /**
       * インデックス
       */
      [key: string]: string | undefined
      /**
       * dmySelAss1Flg
       */
      dmySelAss1Flg?: string
      /**
       * dmySelAss2Flg
       */
      dmySelAss2Flg?: string
      /**
       * dmySelAss3Flg
       */
      dmySelAss3Flg?: string
      /**
       * dmySelAss4Flg
       */
      dmySelAss4Flg?: string
      /**
       * dmySelAss5Flg
       */
      dmySelAss5Flg?: string
      /**
       * dmySelAss6Flg
       */
      dmySelAss6Flg?: string
      /**
       * dmySelAss7Flg
       */
      dmySelAss7Flg?: string
      /**
       * ＨＫ＿ケアチェック表（ヘッダ）の更新回数
       */
      hcc1ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（１．食事）の更新回数
       */
      hcc21ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（２．排泄）の更新回数
       */
      hcc22ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（３．入浴）の更新回数
       */
      hcc23ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（４．洗面）の更新回数
       */
      hcc24ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（５．基本）の更新回数
       */
      hcc25ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（６．医療）の更新回数
       */
      hcc26ModifiedCnt?: string
      /**
       * ＨＫ＿ケアチェック表（７．心理）の更新回数
       */
      hcc27ModifiedCnt?: string
      /**
       * 第二ＨＫ＿ケアチェック表（ヘッダ）の更新回数
       */
      sechcc1ModifiedCnt?: string
    }[]
  }
}
