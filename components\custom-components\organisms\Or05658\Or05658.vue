<script setup lang="ts">
/**
 * Or05658:(状況の事実)要因
 * GUI00915_状況の事実
 *
 * @description
 * (状況の事実)要因
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { useI18n } from 'vue-i18n'
import { ref, reactive, computed, watch } from 'vue'
import { Or28285Logic } from '../Or28285/Or28285.logic'
import { Or28285Const } from '../Or28285/Or28285.constants'
import { Or26392Const } from '../Or26392/Or26392.constants'
import { Or26392Logic } from '../Or26392/Or26392.logic'
import { Or05658Const } from './Or05658.constants'
import { useScreenTwoWayBind, useSetupChildProps, useValidation } from '#imports'
import type { Or05658Type } from '~/types/cmn/business/components/Or05658Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or28285OnewayType, Or2828Type } from '~/types/cmn/business/components/Or28285Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { YouinMaster } from '~/components/custom-components/organisms/Or26392/Or26392.type'
const { byteLength } = useValidation()
/**
 * useI18n
 */
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or05658Type
  modelValue: Or05658Type
  uniqueCpId: string
  parentUniqueCpId: string
}

/**
 * Propsの定義
 */
const props = defineProps<Props>()

/**
 * Or28285
 */
const Or28285 = ref({ uniqueCpId: '' })
/**
 * or26392
 */
const or26392 = ref({ uniqueCpId: '' })

/**************************************************
 * Pinia
 **************************************************/
/**
 * useScreenTwoWayBind
 */
const { refValue } = useScreenTwoWayBind<Or05658Type>({
  cpId: Or05658Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or28285Const.CP_ID(0)]: Or28285.value,
  [Or26392Const.CP_ID(0)]: or26392.value,
})

/**
 * Or28285 一方向モデル値
 */
const onewayModel: Or28285OnewayType = {
  svJigyoId: '1',
}
/**
 * GUI00926_阻害要因入力支援モーダル
 */
const modelValueOr26392 = ref<YouinMaster>({
  houjinId: '',
  shisetuId: '',
  svJigyoId: '',
  youinCd: '',
  youinKnj: '',
  sort: '',
})
/**
 * GUI00925_自立した日常生活の阻害要因
 */
const modelValueOr28285 = ref<Or2828Type>({
  youin1Knj: '',
  youin2Knj: '',
  youin3Knj: '',
  youin4Knj: '',
  youin5Knj: '',
  youin6Knj: '',
})
/**
 * ラベル設定を作成するヘルパー関数
 *
 * @param labelKey - ラベルのキー
 *
 * @returns Mo01338OnewayTypeの設定
 */
const createLabelConfig = (labelKey: string): Mo01338OnewayType => ({
  value: t(`label.${labelKey}`),
  customClass: new CustomClass({
    outerClass: 'm-0 p-0',
    labelClass: 'm-0 p-0',
    itemClass: 'm-0 p-0',
  }),
})

/**
 * フォームフィールド設定を作成するヘルパー関数
 *
 * @returns Mo00045Typeの設定
 */
const createFormFieldConfig = (): Mo00045Type => ({ value: '' })

/**
 * ローカルのOnewayオブジェクト
 */
const localOneway = reactive({
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,

  mo01338OnewayObstacleFactors: {
    value: t('label.obstacle-factors-label'),
  } as Mo01338OnewayType,
  mo01338OnewayImport: {
    value: t('label.import'),
    customClass: new CustomClass({
      outerClass: 'm-0 p-0',
      labelClass: 'm-0 p-0',
      itemClass: 'm-0 p-0',
    }),
  } as Mo01338OnewayType,

  // 動的ラベルを作成する
  ...Object.fromEntries(
    Or05658Const.DEFAULT.LABEL_KEYS.map((key, index) => [
      `mo01338OnewayLabel${index + 1}`,
      createLabelConfig(key),
    ])
  ),

  mo00045Oneway: {
    hideDetails: true,
    class: 'index',
    customClass: new CustomClass({
      outerClass: 'mx-0 border-none',
    }),
    minWidth: '287px',
    showItemLabel: false,
    maxLength: '84',
  } as Mo00045OnewayType,

  mo00009OneWay: {
    btnIcon: 'edit_square',
    size: '24px',
    minWidth: '36px',
    minHeight: '48px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
})

/**
 * フォーム値のローカル状態
 */
const local = reactive(
  Object.fromEntries(
    Or05658Const.DEFAULT.YOUIN_FIELDS.map((field) => [field, createFormFieldConfig()])
  ) as Record<(typeof Or05658Const.DEFAULT.YOUIN_FIELDS)[number], Mo00045Type>
)

/**
 * テンプレート用のフォームデータを作成するcomputed
 */
const formData = computed(() =>
  Or05658Const.DEFAULT.YOUIN_FIELDS.map((field, index) => ({
    field,
    labelKey: `mo01338OnewayLabel${index + 1}`,
    model: local[field],
  }))
)

/**
 * refValueからデータを同期するためのwatch
 */
watch(
  () => refValue.value,
  (newVal) => {
    if (newVal) {
      Or05658Const.DEFAULT.YOUIN_FIELDS.forEach((field) => {
        local[field].value = newVal[field] ?? ''
      })
    }
  },
  { deep: true }
)

/**
 * Or28285のダイアログ開閉状態を更新する
 */
const onClickOr28285 = () => {
  modelValueOr28285.value = refValue.value as Or2828Type
  Or28285Logic.state.set({
    uniqueCpId: Or28285.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * GUI00926_阻害要因入力支援モーダル
 */
const activeField = ref<string>('')
/**
 * GUI00926_阻害要因入力支援モーダル
 *
 * @param field - 入力モーダルを開くために選択されたフィールド
 */
const onClickOr26392 = (field: (typeof Or05658Const.DEFAULT.YOUIN_FIELDS)[number]) => {
  activeField.value = field
  Or26392Logic.state.set({
    uniqueCpId: or26392.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * ダイアログ表示フラグ
 */
const showDialogOr28285 = computed(
  () => Or28285Logic.state.get(Or28285.value.uniqueCpId)?.isOpen ?? false
)
/**
 * GUI00926_阻害要因入力支援モーダル
 */
const showDialogOr26392 = computed(
  () => Or26392Logic.state.get(or26392.value.uniqueCpId)?.isOpen ?? false
)
/**
 * Mo00045の値が変更された時の処理
 *
 * @param field - 変更されたフィールド名
 */
const onChangeMo00045 = (field: (typeof Or05658Const.DEFAULT.YOUIN_FIELDS)[number]) => {
  if (refValue.value) {
    const formDataItem = formData.value.find((item) => item.field === field)
    if (formDataItem) {
      refValue.value[field] = formDataItem.model.value
    }
  }
}
watch(modelValueOr26392, (newVal) => {
  if (refValue.value) {
    const formDataItem = formData.value.find((item) => item.field === activeField.value)
    if (formDataItem && activeField.value in refValue.value) {
      refValue.value[activeField.value as keyof typeof refValue.value] = newVal.youinKnj
    }
  }
})
watch(modelValueOr28285, (newVal) => {
  if (refValue.value) {
    refValue.value = { ...refValue.value, ...newVal }
  }
})
</script>

<template>
  <div>
    <div class="obstacle-factors-container">
      <div
        class="obstacle-factors-box"
        @click.stop="onClickOr28285"
      >
        {{ localOneway.mo01338OnewayObstacleFactors.value }}
      </div>
      <!-- 中央列 -->
      <div class="table-box">
        <div
          v-for="rowIndex in 2"
          :key="rowIndex"
          class="row-flex"
        >
          <div
            v-for="(item, colIndex) in formData.slice((rowIndex - 1) * 3, rowIndex * 3)"
            :key="colIndex"
            class="cell-flex custom-class-input"
            :class="[
              colIndex !== formData.length - 4 ? 'last' : '',
              rowIndex % 2 === 0 ? 'last-row' : '',
            ]"
          >
            <div :class="['cell-number', rowIndex % 2 === 0 ? 'border-top-none' : '']">
              {{ (localOneway as any)[item.labelKey].value }}
            </div>
            <g-custom-or-x-0163
              v-model="item.model"
              :oneway-model-value="{
                readOnly: false,
                height: '46px',
                showEditBtnFlg: true,
                maxLength: '84',
                rules: [byteLength(84)],
              }"
              class="pt-1"
              @on-click-edit-btn="onClickOr26392(item.field)"
            ></g-custom-or-x-0163>
          </div>
        </div>
      </div>
    </div>
    <!-- ダイアログ -->
    <g-custom-or-28285
      v-if="showDialogOr28285"
      v-bind="Or28285"
      v-model="modelValueOr28285"
      :oneway-model-value="onewayModel"
    />
    <g-custom-or-26392
      v-if="showDialogOr26392"
      v-bind="or26392"
      v-model="modelValueOr26392"
      :oneway-model-value="onewayModel"
    />
  </div>
</template>

<style scoped lang="scss">
.bg-transparent {
  background-color: transparent !important;
}
:deep(.obstacle-factors-container) {
  display: flex;
  .obstacle-factors-box {
    height: 96px;
    width: 136px;
    font-size: 13px;
    display: flex;
    align-items: center;
    color: #333333;
    text-align: center;
    background: #dbeefe;
    border: 1px solid #ccc;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    white-space: pre-line;
    letter-spacing: -1px;
    border-right: none;
  }
  .row-flex {
    display: flex;
    &:last-child {
      border-bottom: none;
    }
  }
  .cell-flex {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
    height: 48px;
    &:last-child {
      border-right: none;
    }
  }
  .cell-number {
    background-color: #dbeefe;
    height: 48px;
    width: 36px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    border-left: none;
    &.border-top-none {
      border-top: none !important;
    }
  }
  button {
    border-top: none;
    border-left: none;
    background-color: #ebf2fd;
    // border: 1px solid #c1c6cc;
    height: 46px;
    width: 36px;
  }
  .cell-input-text {
    .v-field {
      border-radius: 0 !important;
      background-color: #fff;
      border: 1px solid #c1c6cc;
    }
    .v-field__input {
      height: 46px;
    }
    .v-field__outline {
      --v-field-border-width: 0 !important;
    }
  }
}

.custom-class-input {
  width: 355px;
  border: #cccccc 1px solid !important;
  background-color: white;
  &.last {
    border-right: none !important;
  }
  &.last-row {
    border-top: none !important;
  }

  :deep(.table-textarea-content-icon) {
    border: none !important;
    border-left: #cccccc 1px solid !important;
    border-right: #cccccc 1px solid !important;
  }
}

:deep(.table-textarea-content) {
  padding: 0 5px !important;
}
:deep(.table-textarea-content-icon) {
  border: #cccccc 1px solid !important;
}
</style>
