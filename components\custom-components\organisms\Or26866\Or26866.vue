<script setup lang="ts">
/**
 * Or26866:有機体:認定調査 特記事項選択
 * GUI01273_認定調査 特記事項選択
 *
 * @description
 * 認定調査 特記事項選択画面では、内容を選択し、呼び出し元の画面に反映します。
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { DataTableData, Or26866StateType, Or26866TableData } from './Or26866.Type'
import { Or26866Const } from './Or26866.constants'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or26866OnewayType, Or26866Type } from '~/types/cmn/business/components/Or26866Type'
import type { Or26323OnewayType, Or26323Type1 } from '~/types/cmn/business/components/Or26323Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { OrX0038Const } from '~/components/custom-components/organisms/OrX0038/OrX0038.constants'
import { OrX0038Logic } from '~/components/custom-components/organisms/OrX0038/OrX0038.logic'
import type { OrX0038StateType } from '~/components/custom-components/organisms/OrX0038/OrX0038.Type'
import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  CertificationSurveySpecialNoteMatterSelectInEntity,
  CertificationSurveySpecialNoteMatterSelectOutEntity,
} from '~/repositories/cmn/entities/CertificationSurveySpecialNoteMatterSelectEntity'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or26323Const } from '~/components/custom-components/organisms/Or26323/Or26323.constants'
import { Or26323Logic } from '~/components/custom-components/organisms/Or26323/Or26323.logic'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import { CustomClass } from '~/types/CustomClassType'
import type {
  Or21815EventType,
  Or21815StateType,
} from '~/components/base-components/organisms/Or21815/Or21815.type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or26866OnewayType
  uniqueCpId: string
  modelValue: Or26866Type
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or26866OnewayType = {
  // 親画面.事業所ID
  svJigyoId: '',
  /**
   * ユーザーID
   */
  userId: '',
  /**
   * メモ
   */
  memo: '',
  /**
   * 期間管理フラグ
   */
  dayVer: '',
  /**
   * 計画期間ID
   */
  sc1Id: '',
  /**
   * 項目番号
   */
  defNo: '',
  /**
   * 項目番号配列
   */
  defNoArray: [''],
  ninteiFlg: '',
}

const localOneWay = reactive({
  or26866: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
    // 履歴
    histExistsFlg: false,
  },
  // 履歴
  orX0038Oneway: {} as OrX0038StateType,
  // 履歴
  or26323Oneway: {
    svJigyoId: '',
    userId: '',
    sc1Id: '',
    mode: '',
    plan1Id: '',
  } as Or26323OnewayType,
  // 実施日
  mo01338_1Oneway: {
    value: '',
    customClass: new CustomClass({
      outerClass: 'mr-4',
      outerStyle: 'min-width: 100px;',
      labelClass: '',
      itemClass: 'mr-2 align-center ',
    }),
  } as Mo01338OnewayType,
  // 記入者名
  mo01338_2Oneway: {
    value: '',
    customClass: new CustomClass({
      outerClass: 'mr-4',
      outerStyle: 'min-width: 130px;',
      labelClass: '',
      itemClass: 'ms-2 align-center ',
    }),
  } as Mo01338OnewayType,
  mo01338_3Oneway: {
    value: t('label.history-no'),
    customClass: new CustomClass({
      outerClass: 'mr-2',
      labelClass: 'ma-1',
      itemClass: 'ml-4 align-center ',
      itemStyle: 'color:red;',
    }),
  } as Mo01338OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 追加コンポーネント
  mo00609AddOneway: {
    btnLabel: t('btn.add'),
  } as Mo00609OnewayType,
  // 上書コンポーネント
  mo00609OverwriteOneway: {
    btnLabel: t('btn.overwrite'),
  } as Mo00609OnewayType,
  // 履歴情報ダイアログ
  mo00024Oneway: {
    width: '840px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or26866',
      toolbarTitle: t('label.certification-survey-select'),
      toolbarName: 'Or26866ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
})

const local = reactive({
  //履歴
  orX0038: {
    createData: {
      currentIndex: 0,
      totalCount: 0,
    },
  } as OrX0038StateType,
  // 履歴選択戻り値
  or26323: { cschId: '' } as Or26323Type1,
  mo00018: { modelValue: false } as Mo00018Type,
  /** 調査票ID */
  cschId: '',
  /** 計画期間ID */
  sc1Id:
    localOneWay.or26866.dayVer === Or26866Const.DEFAULT.DAY_VER_0
      ? Or26866Const.DEFAULT.DAY_VER_0
      : localOneWay.or26866.sc1Id,
  /** 認定フラグ */
  ninteiFlg: localOneWay.or26866.ninteiFlg,
})

const or21815 = ref({ uniqueCpId: '' })
const orX0038 = ref({ uniqueCpId: '' })
const or26323 = ref({ uniqueCpId: '' })

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or26866StateType>({
  cpId: Or26866Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value: boolean | undefined) =>
      (mo00024.value.isOpen = value ?? Or26866Const.DEFAULT.IS_OPEN),
  },
})
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(1)]: or21815.value,
  [OrX0038Const.CP_ID(1)]: orX0038.value,
  [Or26323Const.CP_ID(1)]: or26323.value,
})

// ダイアログ表示フラグ
const showWarnDialog = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr26323 = computed(() => {
  // Or26323のダイアログ開閉状態
  return Or26323Logic.state.get(or26323.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
const selectedCol = ref<{ key: number; value: string }[]>([])

// 変数.履歴移動方式
const moveMode = ref('')

const mo00024 = ref<Mo00024Type>({
  isOpen: Or26866Const.DEFAULT.IS_OPEN,
})

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)
// 履歴情報初期情報
const tableData = ref<DataTableData>({
  or26866List: [],
})
// テーブルヘッダ
const headers = [
  { title: '', key: '', align: 'left', width: '10px', sortable: false },
  { title: t('label.special-no'), align: 'left', key: 'n1tCd', width: '100px', sortable: false },
  { title: t('label.number'), align: 'left', key: 'n2tCd', width: '80px', sortable: false },
  { title: t('label.content'), align: 'left', key: 'memoKnj', width: '300px', sortable: false },
]
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  void init()
})

/**
 * 履歴情報初期情報取得
 */
async function init() {
  const inputData: CertificationSurveySpecialNoteMatterSelectInEntity = {
    ...localOneWay.or26866,
    // 計画期間ID
    sc1Id: local.sc1Id,
    // 認定フラグ
    ninteiFlg: local.ninteiFlg,
    // 履歴移動方式
    moveMode: moveMode.value,
  }
  const ret: CertificationSurveySpecialNoteMatterSelectOutEntity = await ScreenRepository.select(
    'certificationSurveySpecialNoteMatterSelect',
    inputData
  )
  if (Number(ret.data.krirekiCnt) > 0) {
    localOneWay.or26866.histExistsFlg = true
    tableData.value.or26866List = []
    ret.data.tokkiList.forEach((item, index) => {
      tableData.value.or26866List.push({
        ...item,
        tableIndex: index,
        checked: { modelValue: false },
      })
    })
    localOneWay.mo01338_1Oneway.value = ret.data.jisshiDateYmd
    localOneWay.mo01338_2Oneway.value = ret.data.chkShoku
    // 調査票ID
    local.cschId = ret.data.cschId
    // 計画期間ID
    local.sc1Id = ret.data.sc1Id
    await nextTick()
    local.orX0038.createData.currentIndex = Number(ret.data.krirekiNo)
    local.orX0038.createData.totalCount = Number(ret.data.krirekiCnt)
  } else {
    localOneWay.or26866.histExistsFlg = false
  }
}

/**
 * 「履歴選択選択アイコンボタン」押下
 *
 */
function onClickHistIconBtn() {
  // GUI01011 履歴選択画面をポップアップで起動する。
  // 入力パラメータ: 処理モード：0(履歴選択)
  localOneWay.or26323Oneway.mode = Or26866Const.DEFAULT.FUNC_MODEL_0
  // 事業所ID：親画面.事業所ID
  localOneWay.or26323Oneway.svJigyoId = localOneWay.or26866.svJigyoId
  // 計画期間ID：変数.計画期間ID
  localOneWay.or26323Oneway.sc1Id = local.sc1Id
  // 利用者ID：親画面.ユーザーID
  localOneWay.or26323Oneway.userId = localOneWay.or26866.userId
  Or26323Logic.state.set({
    uniqueCpId: or26323.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「履歴-前へアイコンボタン」押下
 */
async function onClickPreHistBtn() {
  // 1件目の履歴データが表示されている状態
  if (local.orX0038.createData.currentIndex === 1) {
    // 処理終了にする。
    return
  }
  moveMode.value = Or26866Const.DEFAULT.PREV
  // TODO
  await init()
}
/**
 * 「履歴-次へアイコンボタン」押下
 */
async function onClickNextHistBtn() {
  // 最終件目の履歴データが表示されている状態
  if (local.orX0038.createData.currentIndex === local.orX0038.createData.totalCount) {
    // 処理終了にする。
    return
  }
  moveMode.value = Or26866Const.DEFAULT.NEXT
  // TODO
  await init()
}

/**
 * 「追加」ボタン押下
 */
async function add() {
  if (selectedCol.value.length === 0) {
    await showOr21815Msg(t('message.w-cmn-20791'))
  } else {
    // 戻り値
    const rtnContents =
      (localOneWay.or26866.memo ? localOneWay.or26866.memo + '\r\n' : '') +
      selectedCol.value.map((item) => item.value).join('\r\n')
    // 画面.履歴情報リストを親画面に戻る。
    emit('update:modelValue', rtnContents)
    close()
  }
}

/**
 * 「上書」ボタン押下
 */
async function overwrite() {
  if (selectedCol.value.length === 0) {
    await showOr21815Msg(t('message.w-cmn-20791'))
  } else {
    // 戻り値： 履歴情報リスト
    const rtnContents = selectedCol.value.map((item) => item.value).join('\r\n')
    // 画面.履歴情報リストを親画面に戻る。
    emit('update:modelValue', rtnContents)
    close()
  }
}

// ダイアログResolve
let resolvePromise: (value: Or21815EventType) => void

/**
 * 警告メッセージを開きます
 *
 * @param uniqueCpId -uniqueCpId
 *
 * @param state -Or21815StateType
 */
function openWarnDialog(uniqueCpId: string, state: Or21815StateType) {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21815EventType>((resolve) => {
    resolvePromise = resolve
  })
}

/**
 * 確定の開閉
 *
 * @param infomsg - Message
 */
async function showOr21815Msg(infomsg: string) {
  const rs = await openWarnDialog(or21815.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: infomsg,
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.ok'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
  return rs
}

/**
 * 行選択
 *
 * @param memoKnj - 特記事項内容
 *
 * @param index - 選択した行のindex
 */
function rowClick(item: { memoKnj: string }, index: number) {
  selectedItemIndex.value = index
  tableData.value.or26866List?.forEach((item: Or26866TableData) => {
    if (item.tableIndex === index) {
      item.checked = { modelValue: true }
    }
  })
  const keyExists = selectedCol.value.some((selectItem) => selectItem.key === index)
  if (!keyExists) {
    //選択情報行
    selectedCol.value.push({ key: index, value: item.memoKnj })
    selectedCol.value.sort((a, b) => a.key - b.key)
  }
}

/**
 * 行選択チェックボックスチェックした時の処理
 */
const toggleSelectRow = () => {
  selectedCol.value = []
  tableData.value.or26866List?.forEach((item: Or26866TableData) => {
    if (item.checked.modelValue === true) {
      selectedCol.value.push({ key: item.tableIndex, value: item.memoKnj })
    }
  })
  if (selectedCol.value.length === tableData.value.or26866List.length) {
    local.mo00018.modelValue = true
  } else {
    local.mo00018.modelValue = false
  }
}

/**
 * 全選択
 *
 * @param mo00018 - チェックボックス
 */
const allCheck = (mo00018: Mo00018Type) => {
  if (tableData.value.or26866List.length > 0) {
    selectedCol.value = []
    if (mo00018.modelValue) {
      tableData.value.or26866List.forEach((item: Or26866TableData) => {
        item.checked = { modelValue: true }
        selectedCol.value.push({ key: item.tableIndex, value: item.memoKnj })
      })
    } else {
      tableData.value.or26866List?.forEach((item: Or26866TableData) => {
        item.checked = { modelValue: false }
      })
    }
  } else {
    local.mo00018.modelValue = false
  }
}

/**
 * 履歴一覧に選択された行がない場合:非活性
 */
const btnDisabled = computed(() => {
  return selectedCol.value === null || selectedCol.value.length === 0 ? true : false
})

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  selectedCol.value = []
  // 本画面を閉じる。
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * Or21815のイベントを監視
 *
 * @description
 * またOr21815のボタン押下フラグをリセットする。
 */
watch(
  () => Or21815Logic.event.get(or21815.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    await nextTick()
    if (resolvePromise !== undefined && newValue !== undefined) {
      resolvePromise(newValue)
    }
    return
  }
)

// 「履歴選択」の監視
watch(
  () => OrX0038Logic.event.get(orX0038.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.iconBtnClickFlg) {
      // 「履歴選択選択アイコンボタン」押下
      onClickHistIconBtn()
    } else if (newValue?.prePageClickFlg) {
      // 「履歴-前へアイコンボタン」押下
      await onClickPreHistBtn()
    } else if (newValue?.nextPageClickFlg) {
      // 「履歴-次へアイコンボタン」押下
      await onClickNextHistBtn()
    }
  }
)

/**
 * 履歴選択確定後処理
 *
 * @param item - 戻り値
 */
async function historySelectChange(item: Or26323Type1) {
  if (item?.cschId === local.cschId) {
    return
  }

  // 調査票ID
  local.cschId = item.cschId
  // 認定フラグ
  local.ninteiFlg = item.kaiteiFlg ?? ''

  await init()
}

watch(
  () => local.or26323,
  async (newVal) => {
    await nextTick()
    if (newVal) {
      await init()
    }
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneWay.mo00024Oneway"
  >
    <template #cardItem>
      <div class="padding-0 pa-3">
        <c-v-row
          v-if="localOneWay.or26866.histExistsFlg"
          no-gutters
        >
          <c-v-col cols="auto">
            <!-- 実施日 -->
            <base-mo00615
              :oneway-model-value="{
                itemLabel: t('label.implementation-date'),
                showRequiredLabel: true,
              }"
            ></base-mo00615>
          </c-v-col>
          <c-v-col cols="auto">
            <base-mo01338 :oneway-model-value="localOneWay.mo01338_1Oneway" />
          </c-v-col>
          <c-v-col cols="auto">
            <!-- 記入者 -->
            <base-mo00615 :oneway-model-value="{ itemLabel: t('label.filler') }"> </base-mo00615>
          </c-v-col>
          <c-v-col cols="auto">
            <base-mo01338 :oneway-model-value="localOneWay.mo01338_2Oneway" />
          </c-v-col>
          <c-v-col cols="auto">
            <!-- 履歴 -->
            <g-custom-orX0038
              v-bind="orX0038"
              v-model="local.orX0038"
              :oneway-model-value="localOneWay.orX0038Oneway"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row v-else>
          <c-v-col>
            <base-mo01338 :oneway-model-value="localOneWay.mo01338_3Oneway" />
          </c-v-col>
        </c-v-row>
      </div>
      <c-v-row>
        <c-v-col>
          <c-v-data-table
            :headers="headers"
            class="table-wrapper"
            hide-default-footer
            :items="tableData.or26866List"
            fixed-header
            hover
            height="373px"
            :items-per-page="-1"
          >
            <!-- ヘッダ -->
            <template #headers>
              <tr>
                <!-- チェックボックス -->
                <th
                  v-if="localOneWay.or26866.histExistsFlg"
                  class="width-60"
                >
                  <base-mo00018
                    v-model="local.mo00018"
                    style="padding: 0px !important"
                    :oneway-model-value="{
                      showItemLabel: false,
                      indeterminate:
                        tableData.or26866List.length > 0 &&
                        selectedCol.length > 0 &&
                        selectedCol.length != tableData.or26866List.length,
                    }"
                    click.stop
                    @update:model-value="allCheck"
                  ></base-mo00018>
                </th>
                <th
                  v-else
                  class="width-60"
                >
                  {{ t('label.selected') }}
                </th>
                <th class="width-100">{{ t('label.special-no') }}</th>
                <th class="width-80">{{ t('label.number') }}</th>
                <th class="width-300">{{ t('label.content') }}</th>
              </tr>
            </template>
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="rowClick(item, index)"
              >
                <td>
                  <base-mo00018
                    v-model="item.checked"
                    class="ml-1"
                    :oneway-model-value="{
                      showItemLabel: false,
                      customClass: new CustomClass({ outerClass: '' }),
                    }"
                    @change="toggleSelectRow"
                    @click.stop
                  ></base-mo00018>
                </td>
                <!-- 特記番号 -->
                <td>
                  {{ item.n1tCd }}
                </td>
                <!-- 番号 -->
                <td>
                  {{ item.n2tCd }}
                </td>
                <!-- 内容 -->
                <td>
                  {{ item.memoKnj }}
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneWay.mo00611CloseBtnOneWay"
          @click="close"
          ><c-v-tooltip
            activator="parent"
            location="top"
            :text="t('tooltip.screen-close')"
        /></base-mo00611>
        <!-- 追加ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneWay.mo00609AddOneway"
          class="ml-2"
          :disabled="btnDisabled"
          @click="add"
          ><c-v-tooltip
            activator="parent"
            location="top"
            :text="t('tooltip.add')"
          />
        </base-mo00609>
        <!-- 上書ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneWay.mo00609OverwriteOneway"
          class="mx-2"
          :disabled="btnDisabled"
          @click="overwrite"
          ><c-v-tooltip
            activator="parent"
            location="top"
            :text="t('tooltip.overwrite')"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21815:有機体:確認ダイアログ -->
  <g-base-or21815
    v-if="showWarnDialog"
    v-bind="or21815"
  />
  <g-custom-or26323
    v-if="showDialogOr26323"
    v-bind="or26323"
    :oneway-model-value="localOneWay.or26323Oneway"
    @update:model-value1="historySelectChange"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
:deep(.v-checkbox .v-checkbox-btn) {
  min-height: 31px !important;
  height: 31px !important;
}
.padding-0 :deep(.v-col) {
  padding: 0px !important;
}

// 選択した行のCSS
.select-row {
  background: #dbeefe;
}

// 最小幅: 60px
.width-60 {
  width: 60px;
}

// 最小幅: 80px
.width-80 {
  width: 80px;
}

// 最小幅: 100px
.width-100 {
  width: 100px;
}

// 最小幅: 300px
.width-300 {
  width: 300px;
}
</style>
