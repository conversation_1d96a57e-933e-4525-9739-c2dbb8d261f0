<script setup lang="ts">
/**
 * Or31168: アセスメント(インターライ)画面U
 * GUI00785_アセスメント(インターライ)画面U
 *
 * @description
 * アセスメント(インターライ)画面U
 *
 * <AUTHOR> VO THANH PHONG
 */

/**************************************************
 * インポート
 **************************************************/
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10412Logic } from '../Or10412/Or10412.logic'
import { Or10412Const } from '../Or10412/Or10412.constants'
import { Or55697Const } from '../Or55697/Or55697.constant'
import { Or55699Const } from '../Or55699/Or55699.constant'
import { Or31168Const } from './Or31168.constant'
import type { Or31168StateType, Or31168TwowayType } from './Or31168.type'
// import { useColorUtils } from '~/utils/useColorUtils'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Or55697BtnInfo, Or55697OnewayType } from '~/types/cmn/business/components/Or55697Type'
import type { Or10412OnewayType, Or10412Type } from '~/types/cmn/business/components/Or10412Type'
import {
  useSetupChildProps,
  useSystemCommonsStore,
  useScreenOneWayBind,
  useScreenInitFlg,
  useScreenTwoWayBind,
  useScreenStore,
} from '#imports'
import type { Or55699BtnInfo, Or55699OnewayType } from '~/types/cmn/business/components/Or55699Type'
// import type { Mo00038Type } from '~/types/business/components/Mo00038Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type {
  AssessmentInterRAIUSelectInEntity,
  AssessmentInterRAIUSelectOutEntity,
  AssessmentInterRAIUUpdateInEntity,
  AssessmentInterRAIUUpdateOutEntity,
  SubUObject,
  SubInfoUItemEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIUEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or31168OnewayType } from '~/types/cmn/business/components/Or31168Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { TransmitParam } from '~/types/cmn/business/components/TeX0003Type'
import type {
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
  SubInfoBEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  AssessmentInterRAIAHistorySelectEntity,
  AssessmentInterRAIAHistorySelectOutEntity,
} from '~/repositories/cmn/entities/assessmentInterRAIAHistoryEntity'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'
import type { Or30981OnewayType } from '~/types/cmn/business/components/Or30981Type'
import type { Mo01355OnewayType } from '@/types/business/components/Mo01355Type'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { Or30981Const } from '~/components/custom-components/organisms/Or30981/Or30981.constants'

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or31168OnewayType
  uniqueCpId: string
}
/**
 * Propsの定義
 */
const props = defineProps<Props>()

/**************************************************
 * 定数(Constants)
 **************************************************/
/**
 * useI18nを使用して国際化のためのt関数を取得
 */
const { t } = useI18n()

/**
 * convertDecimalToHexとconvertHexToDecimalを使用して色の変換ユーティリティを取得
 */
// const { convertDecimalToHex } = useColorUtils()

/**
 * Or31168の初期化フラグを取得
 */
const isInit = useScreenInitFlg()

/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * 画面共通ルーティングコンポーネント
 */
const cmnRouteCom = useCmnRouteCom()
/**
 * 画面ストア
 */
const screenStore = useScreenStore()
/**
 * キーアクションを取得
 */
const inputRequestData: AssessmentInterRAIUUpdateInEntity = {
  /**
   * 法人ID
   */
  houjinId: '',

  /**
   * 施設ID
   */
  shisetuId: '',

  /**
   * 利用者ID
   */
  userId: '',

  /**
   * 事業者ID
   */
  svJigyoId: '',
  /**
   * 種別ID
   */
  syubetsuId: '',
  /**
   * サブ区分
   */
  subKbn: '',
  /**
   * 更新区分
   */
  updateKbn: '',
  /**
   * 更新区分
   */
  historyUpdateKbn: '',
  /**
   * 履歴更新回目
   */
  historyModifiedCnt: '',
  /**
   * 削除区分
   */
  deleteKbn: '',

  /**
   * 計画対象期間ID
   */
  sc1Id: '',
  /**
   * アセスメントID
   */
  raiId: '',
  /**
   * 基準日
   */
  kijunbiYMD: '',

  /**
   * 作成者ID
   */
  sakuseiId: '',

  /**
   * 調査アセスメント種別
   */
  assType: 0,

  /**
   * サブ情報（U）
   */
  subU: {} as SubUObject,
}
/**
 *システム共有情報取得
 */
const commomInfo: TransmitParam = {
  executeFlag: '',
  kikanKanriFlg: '',
  // houjinId: '',
  // shisetuId: '',
  userId: '',
  svJigyoId: '',
  kijunbiYmd: '',
  sakuseiId: '',
  // historyModifiedCnt: '',
  historyInfo: {} as HistoryInfoEntity,
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  deleteBtnValue: '',
  raiId: '',
  assType: '',
  yokaiKbn: '',
  syubetsuId: '',
  svJigyoKnj: '',
  updateKbn: '',
  historyUpdateKbn: '',
  tableCreateKbn: '',
  subInfoB: {} as SubInfoBEntity,
}

/**************************************************
 * Ref・Reactive・Data構造体 (重要: useSetupChildPropsより前に定義)
 **************************************************/
/**
 * or10412の初期値を保持するref
 */
const or10412 = ref({ uniqueCpId: '' })
/**
 * or55697の初期値を保持するref
 */
const or55697 = ref({ uniqueCpId: '' })
/**
 * or55699の初期値を保持するref
 */
const or55699 = ref({ uniqueCpId: '' })
/**
 *  or21814の初期値を保持するref
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 *  or51775の初期値を保持するref
 */
const or51775 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 30981_0
 */
const or30981_0 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 30981_1
 */
const or30981_1 = ref({ uniqueCpId: '' })
/**
 * 画面項目区分
 */
const vieTypeKbn = ref<string>('')

/**
 * Or31168の初期化フラグを取得
 */
const or10412Type = ref<Or10412Type>({
  selectItemNo: '',
  userId: '',
  fontColor: '',
  historyTable: '',
  historyTableColum: '',
  meMoContent: '',
  textSize: '',
  flag: '',
})

/**
 * U2セクションのテキストフィールドデータを格納するリアクティブオブジェクト
 */
// const mo00038TypeU2TextField = reactive<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

/**
 * U3セクションのテキストフィールドデータを格納するリアクティブオブジェクト
 */
// const mo00038TypeU3TextField = reactive<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

/**
 * ローカルデータを格納するリアクティブオブジェクト
 */
// const local = reactive({
//   mo00020U1EndDate: {
//     value: '',
//     showRequiredChip: false,
//   } as Mo00020Type,
// })

/**
 * Or31168の単方向バインドモデルのデフォルト値を定義
 */
const defaultOnewayModelValue: Or31168OnewayType = {
  historyInfo: {} as HistoryInfoEntity,
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  surveyAssessmentKind: '3',
}

/**
 * U2セクションのメモエリアデータを格納するリアクティブ変数
 */
// const mo0009U2Area = ref<Or30981Type>({
//   content: '',
//   fontSize: '14',
//   fontColor: '#FFFFFF',
// } as Or30981Type)
/**
 * U3セクションのメモエリアデータを格納するリアクティブ変数
 */
// const mo0009U3Area = ref<Or30981Type>({
//   content: '',
//   fontSize: '14',
//   fontColor: '#FFFFFF',
// } as Or30981Type)
/**
 * U2セクションのメモフォントサイズレスポンスを格納するリアクティブ変数
 */
// const u2MemoFontSizeResponse = ref('')

/**
 * U2セクションのメモフォントカラーレスポンスを格納するリアクティブ変数
 */
// const u2MemoFontColorResponse = ref('')

/**
 * U3セクションのメモフォントサイズレスポンスを格納するリアクティブ変数
 */
// const u3MemoFontSizeResponse = ref('')

/**
 * U3セクションのメモフォントカラーレスポンスを格納するリアクティブ変数
 */
// const u3MemoFontColorResponse = ref('')

/**
 * アセスメント種別コードリスト
 */
const assessmentKindList = ref<CodeType[]>([])

/**
 * 画像
 */
// const lastUsedMemoStyle = reactive({
//   U2: {
//     fontColor: '',
//     fontSize: '',
//   },
//   U3: {
//     fontColor: '',
//     fontSize: '',
//   },
// })

/**
 * 履歴情報
 */
const defaultOneway = reactive({
  or31168Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 調査アセスメント種別ラベル
  mo01338SurveyAssessmentKindLabelTypeOneway: {
    value: t('label.interRAI-method-care-assessment-table-J-tip'),
    customClass: {
      outerClass: 'background-transparent',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 利用終了ラベル
  mo01338TerminationOfUseOneway: {
    value: t('label.interRAI-method-care-assessment-table-U-termination-of-use-section-title'),
    valueFontWeight: '',
    customClass: { outerClass: '' } as CustomClass,
  } as Mo01338OnewayType,
  // 終了日ラベル
  mo01338U1EndDateOneway: {
    value: t('label.interRAI-method-care-assessment-table-U-termination-of-use-U1-label'),
    valueFontWeight: '',
    customClass: { outerClass: 'background-transparent' } as CustomClass,
  } as Mo01338OnewayType,
  // 今後の住居予定ラベル
  mo01338FuturePlaceOfResidence: {
    value: t('label.interRAI-method-care-assessment-table-U-termination-of-use-U2-label'),
    valueFontWeight: '',
    customClass: { outerClass: 'background-transparent' } as CustomClass,
  } as Mo01338OnewayType,
  // 退所後の在宅サービス利用予定ラベル
  mo01338PlanningToReceiveHomeCareServiceAfterLeavingFacility: {
    value: t('label.interRAI-method-care-assessment-table-U-termination-of-use-U3-label'),
    valueFontWeight: '',
    customClass: { outerClass: 'background-transparent' } as CustomClass,
  } as Mo01338OnewayType,
  // 終了日スタイル
  mo00020U1Oneway: {
    showRequiredChip: true,
    isVerticalLabel: false,
    showItemLabel: false,
    width: '124px',
  } as Mo00020OnewayType,
  // 今後の住居予定スタイル
  mo0009U2Oneway: {
    icon: true,
    btnIcon: 'edit_square',
    color: 'transparent',
  } as Mo00009OnewayType,
  // 退所後の在宅サービス利用予定スタイル
  mo00046U2Oneway: {
    showItemLabel: false,
    readonly: false,
    maxlength: '4000',
    rows: 16,
    maxRows: '16',
    class: 'U2TextArea',
  } as Mo00046OnewayType,
  // メモ入力ボタンスタイル
  mo00046U3Oneway: {
    showItemLabel: false,
    disabled: false,
    maxlength: '4000',
    rows: 2,
    maxRows: '2',
    class: 'U3TextArea',
  } as Mo00046OnewayType,
  // 今後の居住場所のボタン選択肢リスト
  or55697OnewayU2Btns: {
    btnItems: [
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn1'),
        value: '1',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn2'),
        value: '2',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn3'),
        value: '3',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn4'),
        value: '4',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn5'),
        value: '5',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn6'),
        value: '6',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn7'),
        value: '7',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn8'),
        value: '8',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn9'),
        value: '9',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn10'),
        value: '10',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn11'),
        value: '11',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn12'),
        value: '12',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn13'),
        value: '13',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn14'),
        value: '14',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn15'),
        value: '15',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn16'),
        value: '16',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn17'),
        value: '17',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-basic-info-section-U2-btn18'),
        value: '18',
      } as Or55697BtnInfo,
    ],
    layoutType: '1',
  } as Or55697OnewayType,
  // 退所後の在宅サービス利用予定のボタン選択肢リスト
  or55699OnewayU3Btns: {
    btnItems: [
      {
        label: t('label.interRAI-method-care-assessment-table-U-termination-of-use-U3-btn0'),
        value: '0',
      } as Or55697BtnInfo,
      {
        label: t('label.interRAI-method-care-assessment-table-U-termination-of-use-U3-btn1'),
        value: '1',
      } as Or55699BtnInfo,
    ],
    mo00045Style: {
      showItemLabel: false,
      disabled: false,
      maxLength: '1',
      inputmode: 'numeric',
    } as Mo00045OnewayType,
  } as Or55699OnewayType,
  // メモ用スタイル選択ダイアログ
  or10412Oneway: {
    // 選択項目番号
    selectItemNo: '',
    // 利用者ID
    userId: '',
    // 文字色
    fontColor: '',
    // 過去履歴用テーブル名
    historyTable: '-',
    // 過去履歴用カラム名
    historyTableColum: '-',
    // メモ内容
    meMoContent: '',
    // 文字サイズ
    textSize: '',
    // フラグ
    flag: '-',
  } as Or10412OnewayType,
  surveyAssessmentKindList: [] as CodeType[],
  mo00039Oneway: {
    showItemLabel: false,
    hideDetails: true,
    inline: true,
    checkOff: true,
  } as unknown as Mo00039OnewayType,
  orX0156OnewayTypeU2: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * モーダルでカラーピッカー
     */
    mo01355Oneway: {} as Mo01355OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
    disabled: false,
  } as Or30981OnewayType,
  orX0156OnewayTypeU3: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * モーダルでカラーピッカー
     */
    mo01355Oneway: {} as Mo01355OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
    disabled: false,
  } as Or30981OnewayType,
  or51775OnewayTypeOther: {
    title: '',
    classificationID: '2',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
  } as Or51775OnewayType,
})

/**
 * 画面.選定表・検討表作成区分
 */
const deleteKbn = ref('')

/**
 * レスポンス情報のサブ情報を格納するためのオブジェクト
 */
const { refValue } = useScreenTwoWayBind<Or31168TwowayType>({
  cpId: Or31168Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

console.log('refValue', refValue.value)
/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or31168StateType>({
  cpId: Or31168Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 画面初期化フラグ
     *
     * @param value - 画面初期化フラグ
     */
    param: (value) => {
      if (value) {
        commomInfo.kikanKanriFlg = value.kikanKanriFlg
        commomInfo.historyInfo = value.historyInfo
        commomInfo.planPeriodInfo = value.planPeriodInfo

        // inputRequestData.houjinId = value.houjinId
        // inputRequestData.shisetuId = value.shisetuId
        inputRequestData.userId = value.userId
        inputRequestData.svJigyoId = value.svJigyoId
        inputRequestData.executeFlag = value.executeFlag
        // inputRequestData.historyModifiedCnt = value.historyModifiedCnt
        inputRequestData.sc1Id = value.historyInfo.sc1Id
        inputRequestData.kijunbiYMD = value.kijunbiYmd
        inputRequestData.sakuseiId = value.sakuseiId
        inputRequestData.assType = parseInt(value.historyInfo.assType)
      }
      // 調査アセスメント種別設定

      switch (value?.executeFlag) {
        // 保存
        case 'save':
          inputRequestData.deleteKbn = 'U'
          inputRequestData.updateKbn = 'U'
          void onOpen21814()
          break
        // 新規
        case 'add':
          inputRequestData.deleteKbn = 'C'
          inputRequestData.updateKbn = 'C'
          void clearData()
          break
        // 複写
        case 'copy':
          inputRequestData.deleteKbn = 'C'
          inputRequestData.updateKbn = 'C'
          void onCopy()
          break
        // 削除
        case 'delete':
          inputRequestData.deleteKbn = 'D'
          inputRequestData.updateKbn = 'D'
          void onDel()
          break
        // データ再取得
        case 'getData':
          void getInitDataInfo()
          break
        default:
          break
      }
    },
  },
})

/**************************************************
 * useSetupChildProps
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or10412Const.CP_ID(0)]: or10412.value,
  [Or55697Const.CP_ID(0)]: or55697.value,
  [Or55699Const.CP_ID(0)]: or55699.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or30981Const.CP_ID(0)]: or30981_0.value,
  [Or30981Const.CP_ID(1)]: or30981_1.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * GUI00787 ［メモ入力］画面表示フラグ
 *
 *  @returns true:表示 false:非表示
 */
const showDialogOr10412 = computed(() => {
  // Or10412のダイアログ開閉状態
  return Or10412Logic.state.get(or10412.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * ウォッチャー(Watchers)
 **************************************************/
/**
 * メモ入力ダイアログからのデータ反映処理
 *
 * or10412Typeの変更を監視し、U2またはU3の選択に応じて
 * 対象のテキストエリアにメモ内容、フォントカラー、文字サイズを反映する。
 */
// watch(
//   () => or10412Type.value,
//   (newValue) => {
//     if (newValue.selectItemNo === 'U2') {
//       mo0009U2Area.value.value = newValue.meMoContent
//       const el = document.querySelector('.U2TextArea')
//       if (el) {
//         el.querySelectorAll('textarea').forEach((ta) => {
//           if (ta.value) {
//             ta.style.color = newValue.fontColor
//             ta.style.fontSize = newValue.textSize ?? ''
//           }
//         })
//       }
//     }
//     if (newValue.selectItemNo === 'U3') {
//       mo0009U3Area.value.value = newValue.meMoContent
//       const el = document.querySelector('.U3TextArea')
//       if (el) {
//         el.querySelectorAll('textarea').forEach((ta) => {
//           if (ta.value) {
//             ta.style.color = newValue.fontColor
//             ta.style.fontSize = newValue.textSize ?? ''
//           }
//         })
//       }
//     }
//   }
// )

/**
 * メモ入力ダイアログからのスタイルと内容を反映するウォッチャー
 *
 * or10412Type（メモ入力ダイアログの出力値）が変更された際に、
 * 対応するテキストエリア（U2 または U3）にメモの内容とスタイル（文字色・文字サイズ）を適用する。
 */
// watch(
//   () => or10412Type.value,
//   (newValue) => {
//     // U2/U3ごとのエリアRefをマッピング
//     const memoRefMap: Record<'U2' | 'U3', Ref<Mo00046Type>> = {
//       U2: mo0009U2Area,
//       U3: mo0009U3Area,
//     }

//     const selectNo = newValue.selectItemNo as keyof typeof memoRefMap
//     const targetRef = memoRefMap[selectNo]

//     if (targetRef) {
//       // メモ内容を反映
//       targetRef.value = { value: newValue.meMoContent ?? '' }

//       // スタイル適用対象のクラス名を取得（例: '.U2TextArea'）
//       const className = `.${selectNo}TextArea`
//       const element = document.querySelector(className)

//       // スタイル（文字色・文字サイズ）を適用
//       element?.querySelectorAll('textarea')?.forEach((item) => {
//         if (item.value) {
//           item.style.color = newValue.fontColor ?? ''
//           item.style.fontSize = newValue.textSize ? `${newValue.textSize}px` : ''
//         }
//       })
//     }
//   }
// )
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      void onSave()
    }
  }
)

/**************************************************
 * ライフサイクルフック(Lifecycle Hooks)
 **************************************************/
onMounted(async () => {
  if (isInit) {
    await getInitDataInfo()
  }
})

/**************************************************
 * メソッド(Methods)
 **************************************************/

/**
 * RGBカラー文字列をHEXカラー文字列に変換します。
 *
 * @param rgb - `rgb(r, g, b)`形式のRGBカラー文字列。
 *
 * @returns HEXカラー文字列（例：`#ffffff`）。
 */
// const rgbToHex = (rgb: string): string => {
//   let hexStr = ''
//   if (rgb) {
//     if (
//       rgb.length >= 10 &&
//       rgb.startsWith('rgb(') &&
//       rgb.endsWith(')') &&
//       rgb.split(',').length === 3
//     ) {
//       const tempArr = rgb.replace('rgb(', '').replace(')', '').trim().split(',')
//       if (tempArr.length === 3) {
//         hexStr = `#${((1 << 24) + (parseInt(tempArr[0]) << 16) + (parseInt(tempArr[1]) << 8) + parseInt(tempArr[2])).toString(16).slice(1)}`
//       }
//     }
//   }
//   return hexStr
// }

/**
 * メモ入力用スタイル選択ダイアログを開く
 *
 * @param value - 'U2' または 'U3' を指定し、対象のメモ領域を選択
 */
// const openMemoDialog = (value: 'U2' | 'U3') => {
//   // 対象のテキストエリアクラスとメモ内容を取得
//   const textareaClass =
//     value === 'U2' ? Or31168Const.TEXTAREA_CLASSES.U2 : Or31168Const.TEXTAREA_CLASSES.U3
//   const memoValue = value === 'U2' ? mo0009U2Area.value.value : mo0009U3Area.value.value

//   // 最後に使用されたスタイル、またはAPIレスポンスからフォントカラーを取得
//   const fontColor =
//     lastUsedMemoStyle[value].fontColor ||
//     (value === 'U2' ? u2MemoFontColorResponse.value || '' : u3MemoFontColorResponse.value || '')

//   // 最後に使用されたスタイル、またはAPIレスポンスからフォントサイズを取得
//   const fontSize =
//     lastUsedMemoStyle[value].fontSize ||
//     (value === 'U2' ? u2MemoFontSizeResponse.value : u3MemoFontSizeResponse.value)

//   // ダイアログに渡すパラメータを作成
//   const param = {
//     selectItemNo: value,
//     userId: systemCommonsStore.getUserId,
//     fontColor,
//     historyTable: '-',
//     historyTableColum: '-',
//     meMoContent: memoValue,
//     textSize: fontSize,
//     flag: '-',
//   } as Or10412OnewayType

//   // フォントカラーとサイズが空の場合、実際のtextareaからスタイルを取得
//   if (!fontColor && !fontSize) {
//     const textareaWrapper = document.querySelector(textareaClass)
//     if (textareaWrapper) {
//       const textareas = textareaWrapper.querySelectorAll('textarea')
//       textareas.forEach((item) => {
//         if (item.value) {
//           if (!param.fontColor) {
//             param.fontColor = rgbToHex(item.style.color)
//           }
//           param.textSize ??= item.style.fontSize
//         }
//       })
//     }
//   }

//   // 使用したスタイルを保存
//   lastUsedMemoStyle[value].fontColor = param.fontColor
//   lastUsedMemoStyle[value].fontSize = param.textSize ?? ''

//   // 双方向バインド用のデフォルト値をセット
//   defaultOneway.or10412Oneway = { ...param }

//   // メモ入力ダイアログを開く
//   Or10412Logic.state.set({
//     uniqueCpId: or10412.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }

/**
 * 指定されたクラス名のテキストエリアにスタイル（文字色・文字サイズ）を適用する
 *
 * @param className - スタイルを適用する対象のクラス名（例: '.U2TextArea'）
 *
 * @param fontColor - 適用する文字色（オプション）
 *
 * @param fontSize - 適用する文字サイズ（オプション）
 */
// const setTextareaStyle = (className: string, fontColor?: string, fontSize?: string) => {
//   const el = document.querySelector(className)
//   if (el) {
//     el.querySelectorAll('textarea').forEach((ta) => {
//       if (fontColor) ta.style.color = fontColor
//       if (fontSize) ta.style.fontSize = fontSize
//     })
//   }
// }

/**
 * アセスメント種別コードを初期化する非同期関数
 *
 * @returns assessmentKindList
 */
async function initCodes() {
  const selectCodeKbnList = [{ mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND }]
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  assessmentKindList.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND)
  return assessmentKindList.value
}

/**
 * コンポーネントの初期データを取得します。
 *
 * @returns データの取得と適用が完了したときに解決されるPromise。
 */
const getInitDataInfo = async () => {
  // API入力データの作成
  const inputData: AssessmentInterRAIUSelectInEntity = {
    raiId: commomInfo.historyInfo.raiId,
  }

  // 初期データ取得APIの呼び出し
  const resData: AssessmentInterRAIUSelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIUInitSelect',
    inputData
  )

  if (resData) {
    const subInfo = resData.data.subInfoU

    const assessType = defaultOneway.or31168Oneway.surveyAssessmentKind

    const initCodeList = await initCodes()

    const initCodeLabel: string =
      initCodeList.find((item) => item.value === defaultOneway.or31168Oneway.surveyAssessmentKind)
        ?.label ?? ''

    if (assessType === '1') {
      defaultOneway.mo01338SurveyAssessmentKindLabelTypeOneway.value = initCodeLabel ?? ''
      defaultOneway.orX0156OnewayTypeU3.disabled = true
    }
    if (assessType === '2') {
      defaultOneway.mo01338SurveyAssessmentKindLabelTypeOneway.value = initCodeLabel ?? ''
      defaultOneway.orX0156OnewayTypeU3.disabled = false
    }
    if (assessType === '3') {
      defaultOneway.mo01338SurveyAssessmentKindLabelTypeOneway.value = initCodeLabel ?? ''
      defaultOneway.orX0156OnewayTypeU3.disabled = false
    }

    // メモ内容（U2/U3）を反映
    // mo0009U2Area.value.content = subInfo.u2MemoKnj
    // mo0009U2Area.value.fontSize = subInfo.u2MemoFont
    // mo0009U2Area.value.fontColor = subInfo.u2MemoColor

    // mo0009U3Area.value.content = subInfo.u3MemoKnj
    // mo0009U3Area.value.fontSize = subInfo.u3MemoFont
    // mo0009U3Area.value.fontColor = subInfo.u3MemoColor

    // メモスタイル（文字色・文字サイズ）を反映用に保存
    // u2MemoFontColorResponse.value = convertDecimalToHex(Number(subInfo.u2MemoColor))
    // u2MemoFontSizeResponse.value = subInfo.u2MemoFont

    // u3MemoFontColorResponse.value = convertDecimalToHex(Number(subInfo.u3MemoColor))
    // u3MemoFontSizeResponse.value = subInfo.u3MemoFont

    // テキストエリアにスタイルを反映（U2/U3）
    // setTextareaStyle('.U2TextArea', u2MemoFontColorResponse.value, subInfo.u2MemoFont)
    // setTextareaStyle('.U3TextArea', u3MemoFontColorResponse.value, subInfo.u3MemoFont)

    // 終了日を反映
    // local.mo00020U1EndDate.value = subInfo.u1Ymd

    // 今後の居住場所（U2）・退所後に居宅サービスを受ける予定（U3）の選択肢値を反映
    // mo00038TypeU2TextField.mo00045.value = subInfo.u2
    // mo00038TypeU3TextField.mo00045.value = subInfo.u3

    setCpTwowayData(subInfo)
  }
}

/**
 * 履歴情報を取得する非同期関数
 *
 * @description
 * 履歴情報を取得するためのAPI呼び出しを行い、結果を返します。
 * 取得した履歴情報は、画面上での表示や処理に使用されます。
 *
 * @returns - 履歴情報を含むPromiseオブジェクト
 */
const getHistoryInfo = async () => {
  const inputData: AssessmentInterRAIAHistorySelectEntity = {
    raiId: commomInfo.historyInfo.raiId,
  }
  const resData: AssessmentInterRAIAHistorySelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIAHistorySelect',
    inputData
  )
  if (resData.statusCode === 'success') {
    return resData.data
  }
}

/**
 *  保存ボタン押下、取得したインターライ方式履歴情報
 *
 * @returns 履歴情報を含むPromise
 */
const onOpen21814 = async () => {
  const historyInfo = await getHistoryInfo()
  if (historyInfo) {
    // 選定アセスメント種別 > 0   AC003-2-2
    if (Number(historyInfo?.capType) > 0) {
      // 検討アセスメント種別 > 0
      if (Number(historyInfo?.plnType) > 0) {
        // msgDialog11267.value.mo00024.isOpen = true
        deleteKbn.value = '1'
        showOr21814Msg(t('message.i-cmn-11267'))
      }
      // 検討アセスメント種別 <= 0   AC003-2-3
      if (Number(historyInfo?.plnType) <= 0) {
        deleteKbn.value = '0'
        showOr21814Msg(t('message.i-cmn-11266'))
      }
    } else {
      deleteKbn.value = ''
      void onSave()
    }
  }
}

/**
 * 「保存ボタン」押下き確認ダイアログ
 *
 * @param message -情報を提示します
 */
const showOr21814Msg = (message: string): void => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: message,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}

/**
 * AC003_「保存ボタン」押下
 */
const onSave = async () => {
  const dataSubU = {
    raiId: commomInfo.historyInfo.raiId,
    u1Ymd: refValue.value!.u1Ymd.value || '',
    u2: refValue.value!.u2.mo00045.value || '',
    u3: refValue.value!.u3.mo00045.value || '',
    u2MemoKnj: refValue.value!.u2MemoKnj.content ?? '',
    u2MemoFont: refValue.value!.u2MemoKnj.fontSize || '',
    u2MemoColor: refValue.value!.u2MemoKnj.fontColor || '',
    u3MemoKnj: refValue.value!.u3MemoKnj.content ?? '',
    u3MemoFont: refValue.value!.u3MemoKnj.fontSize || '',
    u3MemoColor: refValue.value!.u3MemoKnj.fontColor || '',
  } as unknown as SubUObject

  setCpTwowayData(dataSubU)
  inputRequestData.subU = dataSubU

  const resData: AssessmentInterRAIUUpdateOutEntity = await ScreenRepository.update(
    'assessmentInterRAIUUpdate',
    inputRequestData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === 'success') {
    // 画面情報再取得
    console.log('assessmentInterRAIUUpdate', inputRequestData)
  }
}

/**
 * AC004_「新規ボタン」押下
 */
const clearData = () => {
  // メモ内容（U2/U3）をクリア
  refValue.value!.u2MemoKnj.content = ''
  refValue.value!.u2MemoKnj.fontSize = '14'
  refValue.value!.u2MemoKnj.fontColor = '#FFFFFF'

  refValue.value!.u3MemoKnj.content = ''
  refValue.value!.u3MemoKnj.fontSize = '14'
  refValue.value!.u3MemoKnj.fontColor = '#FFFFFF'

  // メモスタイル（U2/U3）をクリア
  // u2MemoFontColorResponse.value = ''
  // u2MemoFontSizeResponse.value = ''
  // u3MemoFontColorResponse.value = ''
  // u3MemoFontSizeResponse.value = ''

  // テキストエリアのスタイルを初期化
  // setTextareaStyle('.U2TextArea', 'rgb(var(--v-theme-text)', '14px')
  // setTextareaStyle('.U3TextArea', 'rgb(var(--v-theme-text)', '14px')

  // 終了日をクリア
  refValue.value!.u1Ymd.value = ''

  // 今後の居住場所・退所後サービス受給予定をクリア
  refValue.value!.u2.mo00045.value = ''
  refValue.value!.u3.mo00045.value = ''

  const dataSubU = {
    raiId: commomInfo.historyInfo.raiId,
    u1Ymd: refValue.value!.u1Ymd.value || '',
    u2: refValue.value!.u2.mo00045.value || '',
    u3: refValue.value!.u3.mo00045.value || '',
    u2MemoKnj: refValue.value!.u2MemoKnj.content ?? '',
    u2MemoFont: refValue.value!.u2MemoKnj.fontSize || '',
    u2MemoColor: refValue.value!.u2MemoKnj.fontColor || '',
    u3MemoKnj: refValue.value!.u3MemoKnj.content ?? '',
    u3MemoFont: refValue.value!.u3MemoKnj.fontSize || '',
    u3MemoColor: refValue.value!.u3MemoKnj.fontColor || '',
  } as unknown as SubUObject

  setCpTwowayData(dataSubU)
}

/**
 * AC005_「複写ボタン」押下
 */
const onCopy = async () => {
  const dataSubU = {
    raiId: commomInfo.historyInfo.raiId,
    u1Ymd: refValue.value!.u1Ymd.value || '',
    u2: refValue.value!.u2.mo00045.value || '',
    u3: refValue.value!.u3.mo00045.value || '',
    u2MemoKnj: refValue.value!.u2MemoKnj.content ?? '',
    u2MemoFont: refValue.value!.u2MemoKnj.fontSize || '',
    u2MemoColor: refValue.value!.u2MemoKnj.fontColor || '',
    u3MemoKnj: refValue.value!.u3MemoKnj.content ?? '',
    u3MemoFont: refValue.value!.u3MemoKnj.fontSize || '',
    u3MemoColor: refValue.value!.u3MemoKnj.fontColor || '',
  } as unknown as SubUObject

  setCpTwowayData(dataSubU)
  inputRequestData.subU = dataSubU

  const resData: AssessmentInterRAIUUpdateOutEntity = await ScreenRepository.insert(
    'assessmentInterRAIUInsert',
    inputRequestData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === 'success') {
    // 画面情報再取得
    console.log('assessmentInterRAIUInsert', inputRequestData)
  }
  //TODO: 実装する
}

/**
 * AC011_「削除」押下
 */
const onDel = async () => {
  const dataSubU = {
    raiId: commomInfo.historyInfo.raiId,
    u1Ymd: refValue.value!.u1Ymd.value || '',
    u2: refValue.value!.u2.mo00045.value || '',
    u3: refValue.value!.u3.mo00045.value || '',
    u2MemoKnj: refValue.value!.u2MemoKnj.content ?? '',
    u2MemoFont: refValue.value!.u2MemoKnj.fontSize || '',
    u2MemoColor: refValue.value!.u2MemoKnj.fontColor || '',
    u3MemoKnj: refValue.value!.u3MemoKnj.content ?? '',
    u3MemoFont: refValue.value!.u3MemoKnj.fontSize || '',
    u3MemoColor: refValue.value!.u3MemoKnj.fontColor || '',
  } as unknown as SubUObject

  setCpTwowayData(dataSubU)
  inputRequestData.subU = dataSubU

  const resData: AssessmentInterRAIUUpdateOutEntity = await ScreenRepository.delete_phys(
    'assessmentInterRAIUDelete',
    inputRequestData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === 'success') {
    // 画面情報再取得
    console.log('assessmentInterRAIUDelete', inputRequestData)
  }
  //TODO: 実装する
}

const careTargetInputSupportIconClick = (type: 'U2' | 'U3') => {
  vieTypeKbn.value = type
  defaultOneway.or51775OnewayTypeOther = {
    // タイトル
    title: t('label.memo'),
    // 画面ID
    screenId: 'GUI00785',
    // 分類ID
    bunruiId: '',
    // 大分類ＣＤ
    t1Cd: '610',
    // 中分類CD
    t2Cd: '21',
    // 小分類ＣＤ
    t3Cd: '1',
    // テーブル名
    tableName: 'cpn_tuc_rai_ass_u',
    // カラム名
    columnName: '',
    // アセスメント方式
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? '',
    // 文章内容
    inputContents: '',
    // 利用者ID
    userId: systemCommonsStore.getUserId,
    // モード
    mode: '',
  } as Or51775OnewayType
  switch (type) {
    case 'U2':
      defaultOneway.or51775OnewayTypeOther.columnName = 'u2_memo_knj'
      defaultOneway.or51775OnewayTypeOther.inputContents = ''
      break
    case 'U3':
      defaultOneway.or51775OnewayTypeOther.columnName = 'u3_memo_knj'
      defaultOneway.or51775OnewayTypeOther.inputContents = ''
      break
    default:
      break
  }
  // GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  // Or51775のダイアログを開く

  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    // 本文末に追加の場合
    if ('0' === data.type) {
      switch (vieTypeKbn.value) {
        case 'U2':
          refValue.value!.u2MemoKnj.content = refValue.value!.u2MemoKnj.content + data.value
          break
        case 'U3':
          refValue.value!.u3MemoKnj.content = refValue.value!.u3MemoKnj.content + data.value
          break
        default:
          break
      }
    }
    // 本文上書の場合
    else if ('1' === data.type) {
      switch (vieTypeKbn.value) {
        case 'U1':
          refValue.value!.u2MemoKnj.content = data.value
          break
        case 'U2':
          refValue.value!.u3MemoKnj.content = data.value
          break
        default:
          break
      }
    }
  }
}

/**
 * 初期化処理
 *
 * @param data - 画面初期情報
 */
const setCpTwowayData = (data: SubInfoUItemEntity | SubUObject) => {
  const items = data
  screenStore.setCpTwoWay({
    cpId: Or31168Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: {
      /**
       * 終了日
       */
      u1Ymd: {
        value: items.u1Ymd ?? systemCommonsStore.getSystemDate,
        mo01343: {
          value: items.u1Ymd ?? systemCommonsStore.getSystemDate,
          endValue: '',
          mo00024: {
            isOpen: false,
          },
        },
      },

      /**
       * 今後の居住場所
       */
      u2: {
        mo00045: {
          value: items.u2 ?? '',
        },
      },

      /**
       * サービスを受ける予定
       */
      u3: {
        mo00045: {
          value: items.u3 ?? '',
        },
      },

      /**
       * u2 メモ内容
       */
      u2MemoKnj: {
        content: items.u2MemoKnj ?? '',
        fontSize: items.u2MemoFont ?? '14',
        fontColor: items.u2MemoColor ?? '#FFFFFF',
      },

      /**
       * u3 メモ内容
       */
      u3MemoKnj: {
        content: items.u3MemoKnj ?? '',
        fontSize: items.u3MemoFont ?? '14',
        fontColor: items.u3MemoColor ?? '#FFFFFF',
      },
    },
    isInit: true,
  })
}
</script>

<template>
  <!-- レイアウト全体 -->
  <c-v-row
    no-gutters
    class="main-container"
  >
    <!-- 調査・評価区分 -->
    <c-v-col cols="12">
      <div class="type-care">
        <base-mo01338
          :oneway-model-value="defaultOneway.mo01338SurveyAssessmentKindLabelTypeOneway"
        />
      </div>
      <div class="skin-condition-section-title">
        <base-mo01338 :oneway-model-value="defaultOneway.mo01338TerminationOfUseOneway" />
      </div>
      <div class="container">
        <c-v-row no-gutters>
          <!-- 上部ラベル部分 -->
          <c-v-col>
            <!-- U1セクション -->
            <c-v-row no-gutters>
              <!-- U1左側：終了日ラベル -->
              <c-v-col cols="12">
                <c-v-row no-gutters>
                  <div class="asection_left_title_left">
                    <base-mo01338 :oneway-model-value="defaultOneway.mo01338U1EndDateOneway" />
                  </div>
                </c-v-row>
              </c-v-col>

              <!-- U1右側：終了日入力 -->
              <c-v-col
                cols="12"
                class="asection_right"
              >
                <c-v-row no-gutters>
                  <base-mo00020
                    v-model="refValue!.u1Ymd"
                    :oneway-model-value="defaultOneway.mo00020U1Oneway"
                  />
                </c-v-row>
              </c-v-col>
            </c-v-row>

            <!-- U2セクション -->
            <c-v-row no-gutters>
              <!-- U2左側 -->
              <c-v-col cols="12">
                <c-v-row no-gutters>
                  <!-- 今後の住居予定 -->
                  <div class="asection_left_title_left">
                    <base-mo01338
                      :oneway-model-value="defaultOneway.mo01338FuturePlaceOfResidence"
                    />
                  </div>
                </c-v-row>
              </c-v-col>

              <!-- U2右側：ボタン選択 -->
              <c-v-col
                cols="12"
                class="asection_right"
              >
                <c-v-row no-gutters>
                  <!-- U2右側：ボタン選択 -->
                  <c-v-col
                    cols="6"
                    class="pr-1"
                  >
                    <base-mo00039
                      v-model="refValue!.u2.mo00045.value"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                      :class="['custom-radio']"
                    >
                      <base-at-radio
                        v-for="radio in defaultOneway.or55697OnewayU2Btns.btnItems.slice(0, 9)"
                        :key="radio.value"
                        :radio-label="radio.label"
                        :value="radio.value"
                        :class="{ isChecked: refValue!.u2.mo00045.value === radio.value }"
                        :style="`width: 100%`"
                      />
                    </base-mo00039>
                  </c-v-col>

                  <!-- Right column -->
                  <c-v-col
                    cols="6"
                    class="pl-1"
                  >
                    <base-mo00039
                      v-model="refValue!.u2.mo00045.value"
                      :oneway-model-value="defaultOneway.mo00039Oneway"
                      :class="['custom-radio']"
                    >
                      <base-at-radio
                        v-for="radio in defaultOneway.or55697OnewayU2Btns.btnItems.slice(9)"
                        :key="radio.value"
                        :radio-label="radio.label"
                        :value="radio.value"
                        :class="{ isChecked: refValue!.u2.mo00045.value === radio.value }"
                        :style="`width: 100%`"
                      />
                    </base-mo00039>
                  </c-v-col>
                  <c-v-col class="pr-2">
                    <div style="margin-top: 24px">
                      <div class="mt-1 input-container">
                        <g-custom-or-30981
                          v-bind="or30981_1"
                          v-model="refValue!.u2MemoKnj"
                          :oneway-model-value="defaultOneway.orX0156OnewayTypeU2"
                          @on-click-edit-btn="careTargetInputSupportIconClick('U2')"
                        ></g-custom-or-30981>
                      </div>
                    </div>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>

            <!-- U3セクション -->
            <c-v-row no-gutters>
              <!-- U3左側 -->
              <c-v-col cols="12">
                <c-v-row no-gutters>
                  <div class="asection_left_title_left">
                    <base-mo01338
                      :oneway-model-value="
                        defaultOneway.mo01338PlanningToReceiveHomeCareServiceAfterLeavingFacility
                      "
                    />
                  </div>
                </c-v-row>
              </c-v-col>

              <!-- U3右側：ボタン選択 -->
              <c-v-col
                cols="12"
                class="asection_right pb-13"
              >
                <base-mo00039
                  v-model="refValue!.u3.mo00045.value"
                  :oneway-model-value="defaultOneway.mo00039Oneway"
                  :class="['custom-radio']"
                >
                  <base-at-radio
                    v-for="radio in defaultOneway.or55699OnewayU3Btns.btnItems"
                    :key="radio.value"
                    :radio-label="radio.label"
                    :value="radio.value"
                    :class="{
                      isChecked: refValue!.u3.mo00045.value === radio.value,
                    }"
                    :style="`width: 180px`"
                  />
                </base-mo00039>
                <div style="margin-top: 24px">
                  <div class="mt-1 input-container pr-2">
                    <g-custom-or-30981
                      v-bind="or30981_1"
                      v-model="refValue!.u3MemoKnj"
                      :oneway-model-value="defaultOneway.orX0156OnewayTypeU3"
                      @on-click-edit-btn="careTargetInputSupportIconClick('U3')"
                    ></g-custom-or-30981>
                  </div>
                </div>
              </c-v-col>
            </c-v-row>

            <!-- interRAIロゴ -->
            <c-v-row
              no-gutters
              class="pt-4"
            >
              <c-v-col cols="12">
                <c-v-img
                  width="129"
                  aspect-ratio="16/9"
                  fill
                  :src="InterRAI"
                  style="float: right"
                  class="custom-img"
                ></c-v-img>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </div>
    </c-v-col>
  </c-v-row>

  <!-- OR10412 メモダイアログ -->
  <g-custom-or-10412
    v-if="showDialogOr10412"
    v-bind="or10412"
    v-model="or10412Type"
    :oneway-model-value="defaultOneway.or10412Oneway"
  ></g-custom-or-10412>
  <g-base-or21814 v-bind="or21814" />

  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="defaultOneway.or51775OnewayTypeOther"
    @confirm="or51775Confirm"
  ></g-custom-or-51775>
</template>

<style scoped lang="scss">
// @use '@/styles/base.scss';
.main-container {
  padding-top: 16px;
  width: 1080px;
}
.skin-condition-section-title {
  display: flex;
  align-items: center;
  width: 1080px;
  background-color: #fff;
  padding-left: 24px;
  height: 73px;
  color: #333333;
  font-size: 18px;
  font-weight: 700;

  :deep(.item-label) {
    font-size: 18px;
  }
}
.type-care {
  width: 1080px;
  display: flex;
  justify-content: end;
  font-size: 24px;
  padding: 4px 0;

  :deep(.item-label) {
    font-size: 24px;
  }
}

.container {
  width: 1080px;

  .asection_left_title_left {
    width: 100%;
    display: flex;
    align-items: center;
    height: 48px;
    font-size: 17px;
    color: #333333;
    font-weight: 700;
    padding-left: 24px;
    background-color: #e6e6e6;

    :deep(.item-label) {
      font-size: 17px;
    }
    .asection_left_title_left_label {
      display: none !important;
    }
  }

  .asection_right {
    background: rgb(var(--v-theme-secondaryBackground));
    padding: 24px 12px 24px 48px;
  }
}

:deep(.custom-radio) {
  &.inline {
    .v-radio {
      min-width: 180px;
    }
  }
  .v-selection-control-group {
    gap: 16px;
  }
  .v-selection-control {
    margin-right: 0px !important;
  }
  .v-radio {
    padding: 0 8px;
    border: 1px solid #8a8a8a;
    border-radius: 4px;
    .v-label {
      width: 100%;
    }
    &.isChecked {
      background-color: #ecf3fd;
      border: 1px solid #0760e6;
    }
  }
}

:deep(.custom-img img) {
  object-fit: fill;
}

.border-left-none {
  border-left: none;
}

.border-top-none {
  border-top: none;
}

.background-transparent {
  background-color: transparent !important;
}

.gray-background {
  background-color: rgba(var(--v-theme-black-300));
}

:deep(.item-label) {
  white-space: pre-line;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: white;
}

:deep(.fontSizeItem) {
  width: 70px !important;
  max-width: 70px !important;
}
</style>
