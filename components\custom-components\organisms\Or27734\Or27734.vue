<script setup lang="ts">
/**
 *Or27734:有機体:居宅介護支援事業者選択画面モーダル
 *GUI01049_居宅介護支援事業者選択画面
 *
 * @description
 *居宅介護支援事業所以外の場合は、［月間取込］画面が表示される前に［居宅介護支援事業者選択］画面が表示される。該当の居宅介護支援事業者を選択する。
 *[居宅介護支援事業者選択］画面は、［ケアマネ］→［計画書］→［週間計画］→［月間取込］をクリックすると表示される。
 *
 * <AUTHOR> 李晨昊
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27734Const } from './Or27734.constants'
import type { DataTableData, Or27734StateType } from './Or27734.type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or27734Type } from '~/types/cmn/business/components/Or27734Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind } from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  OfficeNmInfoSelectInEntity,
  OfficeNmInfoSelectOutEntity,
} from '~/repositories/cmn/entities/OfficeNmInfoSelectEntity'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or27734Type
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const localOneway = reactive({
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  // 居宅介護支援事業者選択ダイアログ
  mo00024Oneway: {
    width: '480px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27734',
      toolbarTitle: t('label.office-select-info'),
      toolbarName: 'Or27734ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // 注意ラベル
  announcementMessages: {
    value: t('label.or27734-attention-label'),
  } as Mo01337OnewayType,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
// 親画面.法人ID
const commonInfoData = reactive({
  // 法人ID
  houjinId: props.modelValue.houjinId,
})
const mo00024 = ref<Mo00024Type>({
  isOpen: Or27734Const.DEFAULT.IS_OPEN,
})
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

// 事業者名一覧初期情報
const tableData = ref<DataTableData>({
  jigyoList: [],
})

// テーブルヘッダ
const headers = [
  //事業者名
  {
    title: t('label.bussiness-name'),
    width: '160px',
    align: 'left',
    sortable: false,
    key: 'serviceOfficeId',
    required: true,
  },
]

const tableDataFilter = computed(() => {
  const dataList = tableData.value.jigyoList
  return { dataList: dataList }
})

//"*"
const required: string = Or27734Const.DEFAULT.REQUIRED
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27734StateType>({
  cpId: Or27734Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or27734Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await init()
})

/**
 * 取込情報初期情報取得
 */
async function init() {
  //対象期間入力初期情報取得(IN)
  const inputData: OfficeNmInfoSelectInEntity = {
    houjinId: commonInfoData.houjinId,
  }

  //対象期間入力初期情報取得 officeNmInfoSelect
  const ret: OfficeNmInfoSelectOutEntity = await ScreenRepository.select(
    'homeCareSupportOfficeSelectInitSelect',
    inputData
  )

  // 戻り値はテーブルデータとして処理されます
  for (const outInfo of ret.data.jigyoList) {
    tableData.value.jigyoList.push({
      //選択された：サービス事業者ID
      serviceOfficeId: outInfo.serviceOfficeId,
      // 事業者名
      jigyoKnj: outInfo.jigyoKnj,
      // 表示順
      sort: outInfo.sort,
      // サービス事業者ID
      svJigyoId: outInfo.svJigyoId,
      // counter
      counter: outInfo.counter,
      //利用者ID
      userId: outInfo.userId,
      //当該年月
      tougaiYm: outInfo.tougaiYm,
      //モード
      model: outInfo.model,
      //週以外フラグ
      weekOtherThanFlag: outInfo.weekOtherThanFlag,
      //元画面有効期間ID
      orignValidId: outInfo.orignValidId,
    })
  }

  //取込情報一覧に1行目を選択する。
  if (tableData.value.jigyoList.length > 0) {
    onSelectRow(0)
  }
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function onSelectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 「確定」ボタン押下
 */
function confirm() {
  //選択行のデータ
  const selectData = tableData.value.jigyoList[selectedItemIndex.value]

  // 戻り値： 月間取込リスト
  const returnData: Or27734Type = {
    //選択された：サービス事業者ID
    serviceOfficeId: selectData.svJigyoId,
    houjinId : commonInfoData.houjinId,
  }
  // 画面.月間取込リストを親画面に戻る。
  emit('update:modelValue', returnData)
  close()
}

/**
 * 「事業者名」ダブルクリック
 */
function doubleClickSelectRow() {
  confirm()
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)
</script>
<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row>
        <c-v-col>
          <base-mo01337 class="label-padding" :oneway-model-value="localOneway.announcementMessages" />
        </c-v-col>
      </c-v-row>
      <c-v-row>
        <c-v-col>
          <c-v-data-table
            :headers="headers"
            class="table-wrapper"
            hide-default-footer
            :items="tableDataFilter.dataList"
            fixed-header
            hover
            height="360px"
            :items-per-page="-1"
          >
            <!-- ヘッダ Part -->
            <template #headers>
              <tr>
                <th>
                  <!--事業者名-->
                  <span style="color: red">{{ required }}</span>
                  {{ t('label.bussiness-name') }}
                </th>
              </tr>
            </template>
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="onSelectRow(index)"
                @dblclick="doubleClickSelectRow()"
              >
                <!-- 事業者名 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{ value: item.jigyoKnj }"
                    style="width: 100%"
                  />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="confirm"
        >
          <!--ツールチップ表示："設定を確定します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.table-wrapper .v-table__wrapper td {
  padding: 0;
}
:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: 0px;
  z-index: 2;
}

.table-wrapper :deep(.v-row.v-row--no-gutters > .v-col) {
  padding: 0px !important;
  font-size: 14px;
}
:deep(.label-padding .v-col){
  padding: 0px !important;
}
</style>
