<script setup lang="ts">
/**
 * Or58482:有機体:印刷設定
 * GUI00933_印刷設定
 *
 * @description
 * GUI00933_印刷設定
 *
 * <AUTHOR>
 */
import { isEqual } from 'lodash'
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { UserEntity } from '../Or35030/Or35030.type'
import { OrX0128Const } from '../OrX0128/OrX0128.constants'
import { OrX0128Logic } from '../OrX0128/OrX0128.logic'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import { Or58482Const } from './Or58482.constants'
import type { Or58482Param, Or58482StateType } from './Or58482.type'
import {
  useNuxtApp,
  useScreenOneWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00020OnewayType, Mo00020Type } from '@/types/business/components/Mo00020Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import { useCmnCom } from '@/utils/useCmnCom'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { DIALOG_BTN, SPACE_SPLIT_COLON, SPACE_WAVE } from '~/constants/classification-constants'
import { SYS_RYAKU } from '~/constants/constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import type {
  AssessmentPeriodHistoryEntity,
  IssuesAnalysisPeriodHistoryEntity,
  IssuesAnalysisPrintSettingsDetailListHistorySelectInEntity,
  IssuesAnalysisPrintSettingsDetailListHistorySelectOutEntity,
  IssuesAnalysisPrintSettingsHistorySelectInEntity,
  IssuesAnalysisPrintSettingsHistorySelectOutEntity,
  IssuesAnalysisPrintSettingsInitSelectInEntity,
  IssuesAnalysisPrintSettingsInitSelectOutEntity,
  IssuesAnalysisPrintSettingsListHistorySelectInEntity,
  IssuesAnalysisPrintSettingsListHistorySelectOutEntity,
  PrtEntity,
  SysIniInfoEntity,
} from '~/repositories/cmn/entities/IssuesAnalysisPrintSettingsInitEntity'
import type {
  AssessmentHistoryInfoEntity,
  IssuesAnalysisSelectInEntity,
  OutputLedgerPrintEntity,
} from '~/repositories/cmn/entities/IssuesAnalysisSelectEntity'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type {
  OrX0128Headers,
  OrX0128Items,
  OrX0128OnewayType,
} from '~/types/cmn/business/components/OrX0128Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import { CustomClass } from '~/types/CustomClassType'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { reportOutputType, useReportUtils } from '~/utils/useReportUtils'

/** 国際化 */
const { t } = useI18n()
/** 共通メソッド */
const { reportOutput } = useReportUtils()
/** システムstore */
const systemCommonsStore = useSystemCommonsStore()
const $log = useNuxtApp().$log as DebugLogPluginInterface

// route共有情報
const cmnRouteCom = useCmnRouteCom()

// 画面ID
const screenId = 'GUI00933'
// ルーティング
const routing = 'GUI00933/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: ''
}
/** props取得 */
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
// 初期読み込みのローディング
const isLoading = ref(false)

// ボタンの連続クリックを制限する
const pdfDownloadBtnPress = ref(false)

// 子コンポーネント用変数
const or21813 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0128_1 = ref({ uniqueCpId: '' })
const orX0145 = ref({ uniqueCpId: '' })

/** 初期化フラグ */
const initFlg = ref<boolean>(false)
/** 帳票選択または利用者ラジオボックス変更フラグ */
const changeFlg = ref<boolean>(false)
/** タイトルの一時保存変数 */
const tempTitle = ref('')

/** 帳票情報 */
const ledgerInfo = {
  /**
   * 選択された帳票のプロファイル
   */
  profile: '',
  /**
   * 選択された帳票番号
   */
  prtNo: '',
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: '',
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 出力帳票ID
   */
  reportId: '',
  /**
   * セクション番号
   */
  sectionNo: '',
  /**
   * 選択した利用者ID
   */
  selectedUserId: '',
  /**
   * 印刷設定情報リスト
   */
  prtList: [] as PrtEntity[],
  /**
   * 利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 履歴リスト
   */
  historyList: [] as OrX0128Items[],
  /**
   * アセスメント履歴リスト
   */
  assessmentHistoryList: [] as OrX0128Items[],
}

/** Oneway */
const localOneway = reactive({
  /** 親画面の情報 */
  or58482Param: {
    processYmd: '',
    sectionName: '',
    shisetuId: '',
    svJigyoKnj: '',
    svJigyoId: '',
    userId: '',
    assId: '',
    tantoId: '',
    kkjTantoFlg: '',
    houjinId: '',
    shokuId: '',
    selectedUserCounter: '',
    focusSettingInitial: [] as string[],
  },
  kikanFlg: '',
  /** 共通情報 */
  commonInfo: {
    // システム略称：共通情報.システム略称
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    // システムコード：共通情報.システムコード
    sysCd: systemCommonsStore.getSystemCode ?? '',
    // 法人ID：共通情報.法人ID
    houjinId: systemCommonsStore.getHoujinId ?? '',
    // 職員ID：共通情報.職員ID
    shokuId: systemCommonsStore.getStaffId ?? '',
    // 種別ID：共通情報.種別ID
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    // 適用事業所IDリスト：共通情報.適用事業所IDリスト
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList ?? [],
    // 共通情報.担当ケアマネ設定フラグ
    kkjTantoFlg: cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? '',
    // 共通情報.ケアプラン方式
    // cpnFlg: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? '1',
    cpnFlg: '1',
    // 共通情報.計画書様式
    cksFlg: cmnRouteCom.getInitialSettingMaster()?.cksFlg ?? '',
    // 共通情報.システム設定敬称を変更するフラグ
    keishoFlg: cmnRouteCom.getInitialSettingMaster()?.keishoFlg ?? '',
    // 共通情報.システム設定敬称の内容
    keishoKnj: cmnRouteCom.getInitialSettingMaster()?.keishoKnj ?? '',
  },
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * タイトル
   */
  mo01338OneWayTitle: {
    value: t('label.title'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * タイトルテキストフィールド
   */
  mo00045OneWay: {
    showItemLabel: false,
    width: '280',
  } as Mo00045OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWayDatePrint: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 指定日
   */
  mo00020OneWayDesignation: {
    showItemLabel: false,
  } as Mo00020OnewayType,
  /**
   * 敬称を変更するチェックボックス
   */
  mo00018OneWayHonorifics: {
    name: '',
    itemLabel: '',
    // '敬称を変更する'
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    customClass: new CustomClass({
      outerClass: 'honorifics-label',
    }),
  } as Mo00018OnewayType,
  /**
   * 敬称テキストフィールド
   */
  mo00045OneWayHonorifics: {
    showItemLabel: false,
    maxLength: '2',
    width: '62px',
  } as Mo00045OnewayType,
  /**
   * 記入用シートを印刷するチェックボックス
   */
  mo00018OneWayEntry: {
    name: '',
    itemLabel: '',
    // '記入用シートを印刷する'
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    customClass: new CustomClass({
      outerClass: 'entry-label',
    }),
  } as Mo00018OnewayType,
  /**
   * 空白の項目も印刷するチェックボックス
   */
  mo00018OneWayBlankPrint: {
    name: '',
    itemLabel: '',
    // '空白の項目も印刷する'
    checkboxLabel: t('label.blank-item-print'),
    isVerticalLabel: true,
    showItemLabel: true,
    customClass: new CustomClass({
      outerClass: 'blank-item-print-label',
    }),
  } as Mo00018OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    // '利用者選択'
    itemLabel: t('label.printer-user-select'),
    showItemLabel: true,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    // '履歴選択'
    itemLabel: t('label.printer-history-select'),
    showItemLabel: true,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020OneWayBasic: {
    // '基準日'
    itemLabel: t('label.base-date'),
    isVerticalLabel: true,
    showItemLabel: true,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,

  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: Or58482Const.DEFAULT.STR.EMPTY,
  } as OrX0145OnewayType,
})

/**
 * アセスメント一覧区域表示フラグ
 */
const assessmentAreaShowFlg = ref<boolean>(false)

// アセスメント種別コードリスト
let assessmentTypeCodeList: CodeType[] = []
// 改訂情報リスト ケアプラン方式＝2の場合
let revisionInfoCodeListTwo: CodeType[] = []
// 改訂情報リスト ケアプラン方式＝5の場合
let revisionInfoCodeListFive: CodeType[] = []
// 改訂情報リスト ケアプラン方式＝8の場合
let revisionInfoCodeListEight: CodeType[] = []

/** ダイアログOneway */
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or58482',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or58482ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or58482-content',
  } as Mo01344OnewayType,
})
/** ダイアログTwoway */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or58482Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧Oneway
 */
const mo01334OnewayLedger = ref<Mo01334OnewayType>({
  headers: [
    {
      // '帳票'
      title: t('label.report'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 576,
})
/**
 * 出力帳票名一覧Twoway
 */
const mo01334TypeLedger = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * タイトルテキストフィールドmodelValue
 */
const mo00045TitleType = ref<Mo00045Type>({
  value: '',
} as Mo00045Type)

/**
 * 日付印刷区分modelValue
 */
const mo00039DatePrintType = ref<string>('')

/**
 * 指定日modelValue
 */
const mo00020DesignationType = ref<Mo00020Type>({
  // 初期値：システム日付
  value: systemCommonsStore.getSystemDate ?? '',
} as Mo00020Type)
/**
 * 指定日非表示/表示フラグ
 */
const designationShowFlag = ref<boolean>(false)

/**
 * 敬称を変更するmodelValue
 */
const mo00018HonorificsType = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 敬称テキストフィールドmodelValue
 */
const mo00045HonorificsType = ref<Mo00045Type>({
  value: '',
} as Mo00045Type)

/**
 * 記入用シートを印刷するmodelValue
 */
const mo00018EntryType = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)
/**
 * 空白の項目も印刷するmodelValue
 */
const mo00018BlankPrintType = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 利用者選択modelValue
 */
const mo00039UserSelectType = ref<string>('')
/**
 * 履歴選択modelValue
 */
const mo00039HistorySelectType = ref<string>('')
/**
 * 基準日modelValue
 */
const mo00020BasicType = ref<Mo00020Type>({
  value: '',
} as Mo00020Type)

/**
 * 担当ケアマネプルダウンmodelValue
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
})

/**
 * 利用者列幅
 */
const userCols = ref<number>(4)
/**
 * 利用者一覧
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: Or58482Const.DEFAULT.STR.EMPTY,
  // 指定行選択
  userId: Or58482Const.DEFAULT.STR.EMPTY,
  /**
   * 複数選択時、50音の選択数を表示するか
   */
  showKanaSelectionCount: false,
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
})

/**
 * 履歴一覧
 */
const orX0128Oneway = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or58482Const.DEFAULT.STR.EMPTY,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: Or58482Const.DEFAULT.STR.EMPTY,
  tableStyle: Or58482Const.DEFAULT.STR.EMPTY,
  headers: [
    { title: t('label.create-date'), key: 'createYmd', width: '120px', sortable: false },
    { title: t('label.author'), key: 'shokuinKnj', sortable: false },
  ] as OrX0128Headers[],
  items: [],
})

/**
 * アセスメント一覧表示フラグ
 */
const assessmentTableShowFlg = ref<boolean>(false)

/**
 * アセスメント一覧
 */
const orX0128Oneway01 = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or58482Const.DEFAULT.STR.EMPTY,
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: Or58482Const.DEFAULT.STR.EMPTY,
  tableStyle: 'width:515px',
  headers: [] as OrX0128Headers[],
  items: [],
})

/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or58482Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**************************************************
 * Pinia
 **************************************************/
/** ダイヤログ状態 */
const { setState } = useScreenOneWayBind<Or58482StateType>({
  cpId: Or58482Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 開閉
     *
     * @param value -パラメータ
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or58482Const.DEFAULT.IS_OPEN
    },
    /**
     * パラメータ
     *
     * @param value -パラメータ
     */
    param: (value) => {
      if (value) {
        // 親画面のデータを取得
        localOneway.or58482Param.processYmd = value.processYmd
        localOneway.or58482Param.sectionName = value.sectionName
        localOneway.or58482Param.shisetuId = value.shisetuId
        localOneway.or58482Param.svJigyoId = value.svJigyoId
        localOneway.or58482Param.userId = value.userId
        localOneway.or58482Param.assId = value.assId
        localOneway.or58482Param.tantoId = value.tantoId
        localOneway.or58482Param.selectedUserCounter = value.selectedUserCounter
        localOneway.or58482Param.kkjTantoFlg = value.kkjTantoFlg
        localOneway.or58482Param.svJigyoKnj = value.svJigyoKnj
        localOneway.or58482Param.shokuId = value.shokuId
        localOneway.or58482Param.houjinId = value.houjinId
        localOneway.or58482Param.focusSettingInitial = value.focusSettingInitial
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0128Const.CP_ID(1)]: orX0128_1.value,
  [OrX0145Const.CP_ID(0)]: orX0145.value,
})

/**************************************************
 * 算出プロパティ
 **************************************************/

// 記入用シートを印刷するチェックボックス活性制御
/** 利用者選択方法が「単一」 また 履歴選択方法が「単一」の場合、活性表示。 */
const mo00018EntryDisabled = computed(
  () => !(mo00039UserSelectType.value === '0') || !(mo00039HistorySelectType.value === '0')
)

// 担当ケアマネプルダウンリスト活性制御
/** 親画面.担当ケアマネ設定フラグ ＝ 1：チェックする、且つ、親画面.担当者IDがあるの場合、非活性 */
const orx0145Disabled = computed(
  () =>
    Number(localOneway.or58482Param.kkjTantoFlg) === 1 && localOneway.or58482Param.tantoId !== ''
)

// 基準日表示制御
/** 利用者選択方法が「複数」、または「出力帳票名」選択はチェック項目利用者一覧表の場合、表示 */
const mo00020BasicShowFlg = computed(
  () => mo00039UserSelectType.value === '1' || mo01334TypeLedger.value.value === '3'
)

/** 印刷ダイアログ表示フラグ */
const showDialogOrX0117 = computed(() => {
  initPdfDownloadBtnPress()
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ライフサイクル
 **************************************************/
onUnmounted(() => {
  OrX0130Logic.event.set({
    uniqueCpId: orX0130.value.uniqueCpId,
    events: {
      clickFlg: false,
      userList: [],
    },
  })
})

/**************************************************
 * 関数
 **************************************************/
/**
 * 初期値に戻す
 */
const initPdfDownloadBtnPress = () => {
  pdfDownloadBtnPress.value = false
}

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // アセスメント種別コード
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    // 改訂情報リスト ケアプラン方式＝2の場合
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NINTEIFLG },
    // 改訂情報リスト ケアプラン方式＝5の場合
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_REVISION_TYPE },
    // 改訂情報リスト ケアプラン方式＝8の場合
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_REVISION_INFO_SHEET },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayDatePrint.items = list
  }

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039UserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039HistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }

  // アセスメント種別コード
  assessmentTypeCodeList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND)

  // 改訂情報リスト ケアプラン方式＝2の場合
  revisionInfoCodeListTwo = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_NINTEIFLG)
  // 改訂情報リスト ケアプラン方式＝5の場合
  revisionInfoCodeListFive = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_REVISION_TYPE)
  // 改訂情報リスト ケアプラン方式＝8の場合
  revisionInfoCodeListEight = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_REVISION_INFO_SHEET
  )
}

/**
 * 初期情報取得
 */
const getInitData = async () => {
  // 基準日初期値=親画面.処理年月日
  mo00020BasicType.value.value = localOneway.or58482Param.processYmd
  // バックエンドAPIから初期情報取得
  const inputData: IssuesAnalysisPrintSettingsInitSelectInEntity = {
    // システム略称：'3gk'
    sysRyaku: SYS_RYAKU,
    // システムコード：共通情報.システムコード
    sysCd: localOneway.commonInfo.sysCd,
    // メニュー２名称："[mnu2][3GK]ﾓﾆﾀﾘﾝｸﾞ"
    menu2Knj: Or58482Const.DEFAULT.MENU_2_NAME_3GK_ISSUES_ANALYSIS,
    // メニュー３名称："[mnu3][3GK]新評価表"
    menu3Knj: Or58482Const.DEFAULT.MENU_2_NAME_3GK_FREE_ISSUES_ANALYSIS,
    // セクション名：親画面.セクション名
    sectionName: localOneway.or58482Param.sectionName,
    // 職員ID：親画面.職員ID
    shokuId: localOneway.or58482Param.shokuId,
    // 法人ID：親画面.法人ID
    houjinId: localOneway.or58482Param.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.or58482Param.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or58482Param.svJigyoId,
    // 利用者ID：親画面.利用者ID
    userId: localOneway.or58482Param.userId,
    // インデックス：1
    index: Or58482Const.DEFAULT.INDEX_ONE,
    // 個人情報使用フラグ：0 ※0：不使用、1：使用
    kojinhogoUsedFlg: Or58482Const.DEFAULT.KOJINHOGO_USED_FLG,
    // 個人情報番号：0 ※0：主に日誌以外、1：主に日誌系
    sectionAddNo: Or58482Const.DEFAULT.SECTION_ADD_NO,
    // ケアプラン方式：共通情報.ケアプラン方式
    cpnFlg: localOneway.commonInfo.cpnFlg,
    // 計画書様式：共通情報.計画書様式
    cksFlg: localOneway.commonInfo.cksFlg,
  }
  systemCommonsStore.setSystemSnackBarDisabled(true)
  const res: IssuesAnalysisPrintSettingsInitSelectOutEntity = await ScreenRepository.update(
    'issuesAnalysisPrintSettingsUpdate',
    inputData
  )
  if (res.data) {
    ledgerInfo.prtList = res.data.prtList
    ledgerInfo.sysIniInfo = res.data.sysIniInfo
    const prtList: Mo01334Items[] = []
    // Onewayの期間管理フラグを設定
    localOneway.kikanFlg = res.data.kikanFlg
    // 履歴一覧の期間管理フラグを設定
    orX0128Oneway.kikanFlg = res.data.kikanFlg
    for (const item of res.data.prtList) {
      prtList.push({
        id: item.index,
        mo01337OnewayLedgerName: {
          value: item.defPrtTitle,
          unit: '',
        } as Mo01337OnewayType,
        prtTitle: item.prtTitle,
        prnDate: item.prnDate,
        sectionNo: item.sectionNo,
        selectable: true,
        profile: item.profile,
        index: item.index,
        prtNo: item.prtNo,
        honorificsCheckboxValue: item.param03,
        honorificsTextValue: item.param04,
        blankPrintCheckboxValue: item.param07,
      } as Mo01334Items)
    }
    initFlg.value = true
    // 帳票一覧テーブルにデータを追加
    mo01334OnewayLedger.value.items = prtList
    // 履歴一覧データ処理
    setHistoryData(res.data.issuesAnalysisPeriodHistoryList)
    // アセスメント一覧データ処理
    setAssessmentData(res.data.assessmentPeriodHistoryList)
    // 「出力帳票名」選択し、画面データ設定
    selectLedgerName(res.data.prtList[0]?.index)
    // 履歴情報リスト件数>0 且つ 親画面.ヘーダIDが存在する場合
    if (res.data.issuesAnalysisPeriodHistoryList.length > 0 && localOneway.or58482Param.assId) {
      // 履歴一覧明細に親画面.履歴IDに対するレコードが選択状態
      orX0128Oneway.initSelectId = (
        orX0128Oneway.items.findIndex((item) => item.rirekiId === localOneway.or58482Param.assId) +
        1
      ).toString()
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const selectLedgerName = (selectId: string) => {
  for (const item of mo01334OnewayLedger.value.items) {
    if (selectId === item.id && item.mo01337OnewayLedgerName) {
      // タイトルテキストフィールド設定
      mo00045TitleType.value.value = item.prtTitle as string
      // 「出力帳票名」選択行設定
      if (mo01334OnewayLedger.value.items.length > 0) {
        mo01334TypeLedger.value.value = item.id
      }
      // 日付表示有無を設定
      mo00039DatePrintType.value = item.prnDate as string
      // 敬称を変更するチェックボックスを設定
      mo00018HonorificsType.value.modelValue =
        (item.honorificsCheckboxValue as string) === '1' ? true : false
      // 敬称テキストボックスを設定
      mo00045HonorificsType.value.value = item.honorificsTextValue as string
      // 空白の項目も印刷するチェックボックスを設定
      mo00018BlankPrintType.value.modelValue =
        (item.blankPrintCheckboxValue as string) === '1' ? true : false
      // プロファイルを設定
      ledgerInfo.profile = item.profile as string
      // 帳票番号を設定
      ledgerInfo.prtNo = item.prtNo as string
      // 出力帳票名一覧の選択行番号を設定
      ledgerInfo.index = item.index as string
      // セクション番号を設定
      ledgerInfo.sectionNo = item.sectionNo as string
      // 出力帳票ID設定
      setReportId(item.id)
      break
    }
  }
}

/**
 * 出力帳票ID設定
 *
 * @param index - インデックス
 */
const setReportId = (index: string) => {
  switch (index) {
    case '1':
      ledgerInfo.reportId = Or58482Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.ISSUES_ANALYSIS_REPORT_TYPE
      break
    case '2':
      ledgerInfo.reportId =
        Or58482Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.ISSUES_ANALYSIS_ASSESSMENT_ALL_REPORT_TYPE
      break
    default:
      ledgerInfo.reportId = Or58482Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * アセスメント一覧表示項目制御
 */
const setAssessmentItems = () => {
  // 共通情報.ケアプラン方式 = 1:包括的自立支援プログラム
  if (localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.INDEPENDENCE_SUPPORT_PROGRAM) {
    orX0128Oneway01.headers = [
      { title: t('label.create-date'), key: 'createYmd', minWidth: '120px', sortable: false },
      { title: t('label.author'), key: 'shokuinKnj', minWidth: '100px', sortable: false },
    ] as unknown as OrX0128Headers[]
  }
  // 共通情報.ケアプラン方式 = 2:居宅サービス計画ガイドラインの場合:
  else if (localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES) {
    orX0128Oneway01.headers = [
      { title: t('label.create-date'), key: 'createYmd', minWidth: '120px', sortable: false },
      { title: t('label.author'), key: 'shokuinKnj', minWidth: '100px', sortable: false },
      { title: t('label.revision'), key: 'kaiteiFlg', minWidth: '100px', sortable: false },
    ] as unknown as OrX0128Headers[]
  }
  // 共通情報.ケアプラン方式 = 5:新型養護老人ホームパッケージプランの場合:
  else if (localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.ELDER_HOME_PACKAGE_PLAN) {
    orX0128Oneway01.headers = [
      {
        title: t('label.create-date'),
        key: 'createYmd',
        minWidth: '115px',
        width: '115',
        sortable: false,
      },
      {
        title: t('label.author'),
        key: 'shokuinKnj',
        minWidth: '140',
        width: '145',
        sortable: false,
      },
      {
        title: t('label.careNo'),
        key: 'caseNo',
        minWidth: '80px',
        width: '110px',
        sortable: false,
      },
      { title: t('label.revision'), key: 'kaiteiFlg', minWidth: '80px', sortable: false },
    ] as unknown as OrX0128Headers[]
  }
  // 共通情報.ケアプラン方式 = 7:フリーアセスメントの場合:
  else if (localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.FREE_ASSESSMENT) {
    orX0128Oneway01.headers = []
    assessmentTableShowFlg.value = false
  }
  // 共通情報.ケアプラン方式 = 8:情報収集・課題検討シートの場合:
  else if (
    localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.COLLECTION_CONSIDER_SHEET
  ) {
    orX0128Oneway01.headers = [
      { title: t('label.base-date'), key: 'createYmd', minWidth: '120px', sortable: false },
      { title: t('label.using-manager'), key: 'shokuinKnj', minWidth: '100px', sortable: false },
      { title: t('label.revision'), key: 'kaiteiFlg', minWidth: '100px', sortable: false },
    ] as unknown as OrX0128Headers[]
  }
  // 共通情報.ケアプラン方式 = 9:インターライ方式の場合:
  else if (localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.INTERRAI_METHOD) {
    orX0128Oneway01.headers = [
      { title: t('label.base-date'), key: 'createYmd', minWidth: '120px', sortable: false },
      { title: t('label.author'), key: 'shokuinKnj', minWidth: '100px', sortable: false },
      { title: t('label.assessment-kind'), key: 'assType', minWidth: '100px', sortable: false },
    ] as unknown as OrX0128Headers[]
  }
}

/**
 * 選択行のシステムINI情報を取得する
 */
const getSelectedRowSysInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    // プロファイル：画面.帳票一覧選択行.プロファイル
    profile: ledgerInfo.profile,
    // システムコード：共通情報.システムコード
    gsyscd: systemCommonsStore.getSystemCode ?? '',
    // 職員ID：親画面.職員ID
    shokuId: localOneway.or58482Param.shokuId,
    // 個人情報使用フラグ：0 ※0：不使用、1：使用
    kojinhogoUsedFlg: Or58482Const.DEFAULT.KOJINHOGO_USED_FLG,
    // 個人情報番号：0 ※0：主に日誌以外、1：主に日誌系
    sectionAddNo: Or58482Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity

  systemCommonsStore.setSystemSnackBarDisabled(true)
  const res: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (res.data) {
    ledgerInfo.sysIniInfo = res.data.sysIniInfo
  }
}

/**
 * 印刷設定履歴リスト取得
 */
const getPrintSettingsHistoryList = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IssuesAnalysisPrintSettingsHistorySelectInEntity = {
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or58482Param.svJigyoId,
    // 利用者ID：画面.利用者一覧に選択した利用者ID
    userId: ledgerInfo.selectedUserId,
    // 期間管理フラグ
    kikanFlg: localOneway.kikanFlg,
    // ケアプラン方式：共通情報.ケアプラン方式
    cpnFlg: localOneway.commonInfo.cpnFlg,
    // 計画書様式：共通情報.計画書様式
    cksFlg: localOneway.commonInfo.cksFlg,
  }
  const res: IssuesAnalysisPrintSettingsHistorySelectOutEntity = await ScreenRepository.select(
    'issuesAnalysisPrintSettingsHistorySelect',
    inputData
  )
  if (res.data) {
    // 履歴一覧データ処理
    setHistoryData(res.data.issuesAnalysisPeriodHistoryList)
    // アセスメント一覧データ処理
    setAssessmentData(res.data.assessmentPeriodHistoryList)
  }
}

/**
 * 履歴一覧データ処理
 *
 * @param issuesAnalysisPeriodHistoryList - 履歴リスト
 */
const setHistoryData = (issuesAnalysisPeriodHistoryList: IssuesAnalysisPeriodHistoryEntity[]) => {
  // 期間管理フラグが「1:管理する」の場合
  if (localOneway.kikanFlg === Or58482Const.DEFAULT.KIKAN_FLG_MANAGE) {
    // ソート
    issuesAnalysisPeriodHistoryList.sort((a, b) => {
      if (b.sc1Id !== a.sc1Id) {
        return Number(b.sc1Id) - Number(a.sc1Id)
      }
      return new Date(b.startYmd).getTime() - new Date(a.startYmd).getTime()
    })
  }

  // 履歴テーブルデータ
  const orX0128TableData: OrX0128Items[] = []
  // 期間Id一時保存変数
  let tempSc1Id = ''
  issuesAnalysisPeriodHistoryList.forEach((item) => {
    // 期間管理フラグが「1:管理する」の場合
    if (localOneway.kikanFlg === Or58482Const.DEFAULT.KIKAN_FLG_MANAGE) {
      if (tempSc1Id !== item.sc1Id) {
        orX0128TableData.push({
          id: Or58482Const.DEFAULT.STR.EMPTY,
          sc1Id: item.sc1Id,
          startYmd: item.startYmd,
          endYmd: item.endYmd,
          isPeriodManagementMergedRow: true,
          planPeriod:
            t('label.plan-period') + SPACE_SPLIT_COLON + item.startYmd + SPACE_WAVE + item.endYmd,
        } as OrX0128Items)
      }
      orX0128TableData.push({
        id: Or58482Const.DEFAULT.STR.EMPTY,
        sc1Id: item.sc1Id,
        startYmd: item.startYmd,
        endYmd: item.endYmd,
        sel: item.sel,
        rirekiId: item.rirekiId,
        createYmd: item.createYmd,
        shokuinKnj: item.shokuinKnj,
      } as OrX0128Items)

      // 期間Id一時保存
      tempSc1Id = item.sc1Id
    } else {
      orX0128TableData.push({
        id: Or58482Const.DEFAULT.STR.EMPTY,
        sc1Id: item.sc1Id,
        startYmd: item.startYmd,
        endYmd: item.endYmd,
        sel: item.sel,
        rirekiId: item.rirekiId,
        createYmd: item.createYmd,
        shokuinKnj: item.shokuinKnj,
      } as OrX0128Items)
    }
  })
  // 表IDの再設定
  orX0128TableData.forEach((item, index) => (item.id = String(index + 1)))
  // 履歴一覧テーブルを設定
  orX0128Oneway.items = orX0128TableData
}

/**
 * アセスメント一覧データ処理
 *
 * @param assessmentPeriodHistoryList -アセスメントリスト
 */
const setAssessmentData = (assessmentPeriodHistoryList: AssessmentPeriodHistoryEntity[]) => {
  // 期間管理フラグが「1:管理する」の場合
  if (localOneway.kikanFlg === Or58482Const.DEFAULT.KIKAN_FLG_MANAGE) {
    // ソート
    assessmentPeriodHistoryList.sort((a, b) => {
      if (b.sc1Id !== a.sc1Id) {
        return Number(b.sc1Id) - Number(a.sc1Id)
      }
      return new Date(b.startYmd).getTime() - new Date(a.startYmd).getTime()
    })
  }

  // 履歴テーブルデータ
  const orX0128TableData: OrX0128Items[] = []
  // 期間Id一時保存変数
  let tempSc1Id = ''
  assessmentPeriodHistoryList.forEach((item) => {
    // アセスメント種別処理
    const assessmentTypeCodes = assessmentTypeCodeList.find((el) => el.value === item.assType) ?? {
      label: Or58482Const.DEFAULT.STR.EMPTY,
      value: Or58482Const.DEFAULT.STR.EMPTY,
    }
    const assTypeLabel = assessmentTypeCodes.label
    // 改訂処理
    const kaiteiLabel = setKaiteiLabel(item.kaiteiFlg)
    // 期間管理フラグが「1:管理する」の場合
    if (localOneway.kikanFlg === Or58482Const.DEFAULT.KIKAN_FLG_MANAGE) {
      if (tempSc1Id !== item.sc1Id && Number(localOneway.commonInfo.cpnFlg) > 0) {
        orX0128TableData.push({
          id: Or58482Const.DEFAULT.STR.EMPTY,
          sc1Id: item.sc1Id,
          startYmd: item.startYmd,
          endYmd: item.endYmd,
          isPeriodManagementMergedRow: true,
          planPeriod:
            t('label.plan-period') + SPACE_SPLIT_COLON + item.startYmd + SPACE_WAVE + item.endYmd,
        } as OrX0128Items)
      }
      orX0128TableData.push({
        id: Or58482Const.DEFAULT.STR.EMPTY,
        sc1Id: item.sc1Id,
        startYmd: item.startYmd,
        endYmd: item.endYmd,
        sel: item.sel,
        rirekiId: item.rirekiId,
        createYmd: item.createYmd,
        shokuinKnj: item.shokuinKnj,
        kaiteiFlg: kaiteiLabel,
        caseNo: item.caseNo,
        assType: assTypeLabel,
      } as OrX0128Items)

      // 期間Id一時保存
      tempSc1Id = item.sc1Id
    } else {
      orX0128TableData.push({
        id: Or58482Const.DEFAULT.STR.EMPTY,
        sc1Id: item.sc1Id,
        startYmd: item.startYmd,
        endYmd: item.endYmd,
        sel: item.sel,
        rirekiId: item.rirekiId,
        createYmd: item.createYmd,
        shokuinKnj: item.shokuinKnj,
        kaiteiFlg: kaiteiLabel,
        caseNo: item.caseNo,
        assType: assTypeLabel,
      } as OrX0128Items)
    }
  })
  // 表IDの再設定
  orX0128TableData.forEach((item, index) => (item.id = String(index + 1)))
  // アセスメント一覧テーブルを設定
  orX0128Oneway01.items = orX0128TableData
}

/**
 * 改訂処理
 *
 * @param kaiteiFlg -改訂フラグ
 *
 * @returns 改訂フラグに対して改訂名称
 */
const setKaiteiLabel = (kaiteiFlg: string) => {
  let kaiteiLabel = ''
  if (localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES) {
    const revisionInfoCodesTwo = revisionInfoCodeListTwo.find(
      (item) => item.value === kaiteiFlg
    ) ?? { label: '', value: '' }
    kaiteiLabel = revisionInfoCodesTwo.label
  } else if (
    localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.ELDER_HOME_PACKAGE_PLAN
  ) {
    const revisionInfoCodesFive = revisionInfoCodeListFive.find(
      (item) => item.value === kaiteiFlg
    ) ?? { label: '', value: '' }
    kaiteiLabel = revisionInfoCodesFive.label
  } else if (
    localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.COLLECTION_CONSIDER_SHEET
  ) {
    const revisionInfoCodesEight = revisionInfoCodeListEight.find(
      (item) => item.value === kaiteiFlg
    ) ?? { label: '', value: '' }
    kaiteiLabel = revisionInfoCodesEight.label
  }
  return kaiteiLabel
}

/**
 * 単一印刷の場合、課題分析期間履歴情報の設定
 *
 * @returns -課題分析期間履歴情報
 */
const setSinglePrintHistory = (): {
  appYdd: string
  userId: string
  sc1Id: string
  rirekiId: string
} => {
  const historyInfo: { appYdd: string; userId: string; sc1Id: string; rirekiId: string } = {
    appYdd: '',
    userId: '',
    sc1Id: '',
    rirekiId: '',
  }
  const userInfoList = OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList
  const historyInfoList = OrX0128Logic.event.get(orX0128.value.uniqueCpId)?.orX0128DetList
  /** 基準日（appYdd）処理 */
  // 1.記入シートオプション＝チェックする
  if (mo00018EntryType.value.modelValue) {
    // 1-1.利用者選択する 且つ 課題分析選択する
    if (userInfoList && userInfoList.length > 0 && historyInfoList && historyInfoList.length > 0) {
      historyInfo.appYdd = historyInfoList[0].createYmd as string
    }
    // 1-2.利用者選択する 且つ 課題分析選択しない
    else if (
      userInfoList &&
      userInfoList.length > 0 &&
      historyInfoList &&
      historyInfoList.length === 0
    ) {
      historyInfo.appYdd = Or58482Const.DEFAULT.DATE_STATE_VALUE_2024
    }
  }

  /** 利用者ID（userid）処理 */
  // 1.記入シートオプション＝チェックする
  if (mo00018EntryType.value.modelValue) {
    historyInfo.userId = Or58482Const.DEFAULT.STR.ZERO
  }
  // 2.記入シートオプション＝チェックしない
  else {
    // 2-1.利用者選択する 且つ 課題分析選択する
    if (userInfoList && userInfoList.length > 0 && historyInfoList && historyInfoList.length > 0) {
      historyInfo.userId = userInfoList[0].userId
    }
  }

  /** 期間ID（sc1Id）処理 */
  // 1.記入シートオプション＝チェックする
  if (mo00018EntryType.value.modelValue) {
    historyInfo.sc1Id = Or58482Const.DEFAULT.STR.ZERO
  }
  // 2.記入シートオプション＝チェックしない
  else {
    // 2-1.アセスメント<>5の場合 且つ 利用者選択する 且つ 課題分析選択する
    if (
      localOneway.commonInfo.cpnFlg !== Or58482Const.CARE_PLAN_KIND.ELDER_HOME_PACKAGE_PLAN &&
      userInfoList &&
      userInfoList.length > 0 &&
      historyInfoList &&
      historyInfoList.length > 0
    ) {
      historyInfo.sc1Id = historyInfoList[0].sc1Id as string
    }
  }

  /** 履歴ID（rirekiId）処理 */
  // 1.記入シートオプション＝チェックする
  if (mo00018EntryType.value.modelValue) {
    historyInfo.rirekiId = Or58482Const.DEFAULT.STR.ZERO
  }
  // 2.記入シートオプション＝チェックしない
  else {
    // 2-1.利用者選択する 且つ 課題分析選択する場合
    if (userInfoList && userInfoList.length > 0 && historyInfoList && historyInfoList.length > 0) {
      historyInfo.rirekiId = historyInfoList[0].rirekiId as string
    }
  }

  return historyInfo
}

/**
 * 単一印刷の場合、アセスメント期間履歴情報の設定
 *
 * @returns -アセスメント期間履歴情報
 */
const setSinglePrintAssessmentHistory = (): AssessmentHistoryInfoEntity => {
  const assessmentHistoryInfo: AssessmentHistoryInfoEntity = {
    sc1Id: '',
    rirekiId: '',
    sparm: '',
    lparm: '',
    argI: '',
    aiRadio: '',
  }
  // 帳票タイトル＝課題分析の場合
  if (ledgerInfo.index === Or58482Const.DEFAULT.STR.ONE) {
    // 処理しない、戻る
    return assessmentHistoryInfo
  }
  const assessmentHistoryInfoList = OrX0128Logic.event.get(
    orX0128_1.value.uniqueCpId
  )?.orX0128DetList
  /** 期間ID（sc1Id）処理 */
  // 1.記入シートオプション＝チェックする場合
  if (mo00018EntryType.value.modelValue) {
    // 1-1.アセスメント＝2 且つ アセスメント期間履歴選択する場合
    if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length > 0
    ) {
      assessmentHistoryInfo.sc1Id = assessmentHistoryInfoList[0].sc1Id as string
    } else {
      assessmentHistoryInfo.sc1Id = Or58482Const.DEFAULT.STR.ZERO
    }
  }
  // 2.記入シートオプション＝チェックしない場合
  else {
    // 2-1.アセスメント＝1 且つ アセスメント期間履歴選択する
    // 2-2.アセスメント＝2 且つ アセスメント期間履歴選択する
    // 2-3.アセスメント＝8 且つ アセスメント期間履歴選択する
    // 2-4.アセスメント＝9 且つ アセスメント期間履歴選択する
    if (
      (localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.INDEPENDENCE_SUPPORT_PROGRAM ||
        localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES ||
        localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.COLLECTION_CONSIDER_SHEET ||
        localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.INTERRAI_METHOD) &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length > 0
    ) {
      // sc1_id = 選択したアセスメント期間履歴.期間ID(sc1_id)
      assessmentHistoryInfo.sc1Id = assessmentHistoryInfoList[0].sc1Id as string
    }
  }

  /** 履歴ID（rirekiId）処理 */
  // 1.記入シートオプション＝チェックする場合
  if (mo00018EntryType.value.modelValue) {
    // 1-1.アセスメント＝2 且つ アセスメント期間履歴選択する場合
    if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length > 0
    ) {
      assessmentHistoryInfo.rirekiId = assessmentHistoryInfoList[0].rirekiId as string
    } else {
      assessmentHistoryInfo.rirekiId = Or58482Const.DEFAULT.STR.ZERO
    }
  }
  // 2.記入シートオプション＝チェックしない場合
  else {
    // 2-1.アセスメント＝1 且つ アセスメント期間履歴選択する場合
    // 2-2.アセスメント＝2 且つ アセスメント期間履歴選択する
    // 2-3.アセスメント＝5 且つ アセスメント期間履歴選択する
    // 2-4.アセスメント＝8 且つ アセスメント期間履歴選択する
    // 2-5.アセスメント＝9 且つ アセスメント期間履歴選択する
    if (
      (localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.INDEPENDENCE_SUPPORT_PROGRAM ||
        localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES ||
        localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.ELDER_HOME_PACKAGE_PLAN ||
        localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.COLLECTION_CONSIDER_SHEET ||
        localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.INTERRAI_METHOD) &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length > 0
    ) {
      // rireki_id＝選択したアセスメント期間履歴.履歴ID(rireki_id)
      assessmentHistoryInfo.rirekiId = assessmentHistoryInfoList[0].rirekiId as string
    }
  }

  /** s_parm処理 */
  // 1.記入シートオプション＝チェックする場合
  if (mo00018EntryType.value.modelValue) {
    // 1-1.アセスメント＝2 且つ アセスメント期間履歴選択する
    if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length > 0
    ) {
      assessmentHistoryInfo.sparm = assessmentHistoryInfoList[0].createYmd as string
    }
    // 1-2.アセスメント＝2 且つ アセスメント期間履歴選択しない
    else if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length === 0
    ) {
      assessmentHistoryInfo.sparm = Or58482Const.DEFAULT.DATE_STATE_VALUE_2018
    }
  }
  // 2.記入シートオプション＝チェックしない場合
  else {
    // 2-1.アセスメント＝2 且つ アセスメント期間履歴選択する場合
    if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length > 0
    ) {
      // s_parm＝選択したアセスメント期間履歴.アセスメント実施日(as_jisshi_date_ymd)
      assessmentHistoryInfo.sparm = assessmentHistoryInfoList[0].createYmd as string
    }
  }

  /** l_parm処理 */
  // 1.記入シートオプション＝チェックする場合
  if (mo00018EntryType.value.modelValue) {
    // 1-1.アセスメント＝5
    if (localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.ELDER_HOME_PACKAGE_PLAN) {
      assessmentHistoryInfo.lparm = Or58482Const.DEFAULT.STR.TWO
    }
    // 1-1.アセスメント＝8
    else if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES
    ) {
      assessmentHistoryInfo.lparm = localOneway.commonInfo.cksFlg
    }
  }
  // 2.記入シートオプション＝チェックしない場合
  else {
    // 2-1.アセスメント＝2 且つ アセスメント期間履歴選択する場合
    if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.SERVICE_PLAN_GUIDELINES &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length > 0
    ) {
      // l_parm＝選択したアセスメント期間履歴.改定フラグ(kaitei_flg)
      assessmentHistoryInfo.lparm = assessmentHistoryInfoList[0].kaiteiFlg as string
    }
    // 2-2.アセスメント＝8 且つ アセスメント期間履歴選択する
    // 2-3.アセスメント＝9 且つ アセスメント期間履歴選択する
    else if (
      (localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.COLLECTION_CONSIDER_SHEET ||
        localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.INTERRAI_METHOD) &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length > 0
    ) {
      // l_parm＝選択したアセスメント期間履歴.改定フラグ(kaitei_flg)
      assessmentHistoryInfo.lparm = localOneway.commonInfo.cksFlg
    }
  }

  /** arg_i処理 */
  // 1.記入シートオプション＝チェックする場合
  if (mo00018EntryType.value.modelValue) {
    // 1-1.アセスメント＝8 且つ アセスメント期間履歴選択する
    if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.COLLECTION_CONSIDER_SHEET &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length > 0
    ) {
      assessmentHistoryInfo.argI = assessmentHistoryInfoList[0].kaiteiFlg as string
    }
    // 1-1.アセスメント＝8 且つ アセスメント期間履歴選択しない
    else if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.COLLECTION_CONSIDER_SHEET &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length === 0
    ) {
      assessmentHistoryInfo.argI = Or58482Const.DEFAULT.STR.ONE
    }
  }
  // 2.記入シートオプション＝チェックしない場合
  else {
    // 2-1.アセスメント＝8 且つ アセスメント期間履歴選択する場合
    if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.COLLECTION_CONSIDER_SHEET &&
      assessmentHistoryInfoList &&
      assessmentHistoryInfoList.length > 0
    ) {
      // arg_i＝選択したアセスメント期間履歴.改定フラグ(kaitei_kbn)
      assessmentHistoryInfo.argI = assessmentHistoryInfoList[0].kaiteiFlg as string
    }
  }

  /** ai_radio処理 */
  // 1.記入シートオプション＝チェックする場合
  if (mo00018EntryType.value.modelValue) {
    // 1-1.アセスメント＝9 且つ 共通情報.計画書様式(cks_flg)＝1
    if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.INTERRAI_METHOD &&
      localOneway.commonInfo.cksFlg === Or58482Const.DEFAULT.STR.ONE
    ) {
      assessmentHistoryInfo.aiRadio = Or58482Const.DEFAULT.STR.TWO
    }
    // 1-2.アセスメント＝9 且つ 共通情報.計画書様式(cks_flg)＝1以外
    else if (
      localOneway.commonInfo.cpnFlg === Or58482Const.CARE_PLAN_KIND.INTERRAI_METHOD &&
      localOneway.commonInfo.cksFlg !== Or58482Const.DEFAULT.STR.ONE
    ) {
      assessmentHistoryInfo.aiRadio = Or58482Const.DEFAULT.STR.ONE
    }
  }

  return assessmentHistoryInfo
}

/**
 * 画面印刷設定内容を保存
 *
 * @returns -保存出力エンティティ
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    // システム略称："3gk"
    sysRyaku: SYS_RYAKU,
    // セクション名：親画面.セクション名
    sectionName: localOneway.or58482Param.sectionName,
    // システムコード：共通情報.システムコード
    gsyscd: localOneway.commonInfo.sysCd,
    // 職員ID：親画面.職員ID
    shokuId: localOneway.or58482Param.shokuId,
    // 法人ID：親画面.法人ID
    houjinId: localOneway.or58482Param.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.or58482Param.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or58482Param.svJigyoId,
    // インデックス：画面.出力帳票一覧明細.選択行.インデックス
    index: ledgerInfo.index,
    // システムINI情報：画面.システムINI情報
    sysIniInfo: {
      amikakeFlg: ledgerInfo.sysIniInfo.amikakeFlg,
      iso9001Flg: ledgerInfo.sysIniInfo.iso9001Flg,
      kojinhogoFlg: ledgerInfo.sysIniInfo.kojinhogoFlg,
    },
    // 印刷設定情報リスト：画面.印刷設定情報リスト
    prtList: ledgerInfo.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity
  const res: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return res
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  // 画面の印刷設定情報を保存する
  await save()
  setState({
    isOpen: false,
    param: {} as Or58482Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // ボタンの連続クリックを制限する
  if (pdfDownloadBtnPress.value) {
    return
  }
  pdfDownloadBtnPress.value = true
  // 利用者選択が「単一」、且つ、履歴選択が「単一」の場合
  // 且つ 記入用シートを印刷するチェック入れるの場合
  if (
    mo00039UserSelectType.value === Or58482Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or58482Const.DEFAULT.TANI &&
    mo00018EntryType.value.modelValue
  ) {
    // 帳票出力
    await reportOutputPdf()
    initPdfDownloadBtnPress()
    return
  }

  // 利用者選択方法が「単一」、かつ、
  // 履歴選択方法が「単一」、かつ、記入用シートを印刷するチェック外す 且つ
  // 利用者一覧が0件選択
  if (
    mo00039UserSelectType.value === Or58482Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or58482Const.DEFAULT.TANI &&
    !mo00018EntryType.value.modelValue &&
    OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11393'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      initPdfDownloadBtnPress()
      // 処理終了
      return
    }
  }

  // 利用者選択が「複数」、利用者一覧が0件選択の場合
  if (
    mo00039UserSelectType.value === Or58482Const.DEFAULT.HUKUSUU &&
    OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11393'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      initPdfDownloadBtnPress()
      // 処理終了
      return
    }
  }

  // 利用者選択が「単一」且つ 履歴選択が「単一」
  // 且つ 記入用シートを印刷するチェック外す
  // 且つ 出力帳票名は課題分析(アセスメントの全てと一括印刷)を選択する
  // 且つ 課題分析履歴一覧が0件選択の場合
  if (
    mo00039UserSelectType.value === Or58482Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or58482Const.DEFAULT.TANI &&
    !mo00018EntryType.value.modelValue &&
    ledgerInfo.index === Or58482Const.DEFAULT.STR.TWO &&
    OrX0128Logic.event.get(orX0128.value.uniqueCpId)?.orX0128DetList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11455'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      initPdfDownloadBtnPress()
      // 処理終了
      return
    }
  }

  // 利用者選択が「単一」 且つ 履歴選択が「単一」
  // 且つ 記入用シートを印刷するチェック外す
  // 且つ 出力帳票名は課題分析(アセスメントの全てと一括印刷)を選択する
  // 且つ アセスメント履歴一覧が0件選択の場合
  if (
    mo00039UserSelectType.value === Or58482Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or58482Const.DEFAULT.TANI &&
    !mo00018EntryType.value.modelValue &&
    ledgerInfo.index === '2' &&
    OrX0128Logic.event.get(orX0128_1.value.uniqueCpId)?.orX0128DetList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11455'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      initPdfDownloadBtnPress()
      // 処理終了
      return
    }
  }

  // 履歴選択方法が「複数」、かつ、
  // 履歴一覧が0件選択の場合
  if (
    mo00039HistorySelectType.value === Or58482Const.DEFAULT.HUKUSUU &&
    OrX0128Logic.event.get(orX0128.value.uniqueCpId)?.orX0128DetList.length === 0
  ) {
    // メッセージを表示
    const dialogResult = await openConfirmDialog(t('message.i-cmn-11455'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      initPdfDownloadBtnPress()
      // 処理終了
      return
    }
  }

  // 画面の印刷設定を保存する
  await save()

  // 選択された帳票のプロファイルが''の場合
  if (!ledgerInfo.profile) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(t('message.e-cmn-40172'))
    // はい
    if (dialogResult === DIALOG_BTN.YES) {
      initPdfDownloadBtnPress()
      // 処理終了
      return
    }
  }

  // 利用者選択が「単一」 且つ 履歴選択方法が「単一」
  // 且つ 履歴一覧に選択されある場合
  if (
    mo00039UserSelectType.value === Or58482Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or58482Const.DEFAULT.TANI &&
    (OrX0128Logic.event.get(orX0128.value.uniqueCpId)?.orX0128DetList.length ?? 0) > 0
  ) {
    // 帳票出力
    await reportOutputPdf()
    initPdfDownloadBtnPress()
    return
  }

  // 利用者選択方法が「単一」、かつ、
  // 履歴選択方法が「複数」、かつ、
  // 履歴情報リストにデータを選択する場合
  if (
    mo00039UserSelectType.value === Or58482Const.DEFAULT.TANI &&
    mo00039HistorySelectType.value === Or58482Const.DEFAULT.HUKUSUU &&
    (OrX0128Logic.event.get(orX0128.value.uniqueCpId)?.orX0128DetList.length ?? 0) > 0
  ) {
    // 印刷設定情報リストを作成
    await getDetailListHistory()
  }

  // 利用者選択が「複数」且つ 画面.利用者情報リストにデータを選択する場合
  if (
    mo00039UserSelectType.value === Or58482Const.DEFAULT.HUKUSUU &&
    (OrX0130Logic.event.get(orX0130.value.uniqueCpId)?.userList.length ?? 0) > 0
  ) {
    // 印刷設定情報リストを再取得する
    await getListHistory()
  }

  // 上記の「AC014-5-2-2」と「AC014-5-3-2」の印刷用情報リスト＞0件
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷対象処理
 *
 * @param item -帳票エンティティ
 *
 * @returns 印刷対象
 */
const setPrintSubject = (item: PrtEntity): OutputLedgerPrintEntity => {
  return {
    shokuId: item.shokuId,
    sysRyaku: item.sysRyaku,
    defPrtTitle: item.defPrtTitle,
    prtTitle: item.prtTitle,
    section: item.sectionNo,
    prtNo: item.prtNo,
    choPro: item.profile,
    sectionName: localOneway.or58482Param.sectionName,
    dwObject: item.dwobject,
    prtOrient: item.prtOrient,
    prtSize: item.prtSize,
    listTitle: item.listTitle,
    mTop: item.mtop,
    mBottom: item.mbottom,
    mLeft: item.mleft,
    mRight: item.mright,
    ruler: item.ruler,
    prnDate: item.prnDate,
    prnShoku: item.prnshoku,
    serialFlg: item.serialFlg,
    modFlg: item.modFlg,
    secFlg: item.secFlg,
    serialHeight: item.serialHeight,
    serialPagelen: item.serialPagelen,
    zoomRate: item.zoomRate,
    param01: item.param01,
    param02: item.param02,
    param03: item.param03,
    param04: item.param04,
    param05: item.param05,
    param06: item.param06,
    param07: item.param07,
    param08: item.param08,
    param09: item.param09,
    param10: item.param10,
    param11: item.param11,
    param12: item.param12,
    param13: item.param13,
    param14: item.param14,
    param15: item.param15,
    param16: item.param16,
    param17: item.param17,
    param18: item.param18,
    param19: item.param19,
    param20: item.param20,
    param21: item.param21,
    param22: item.param22,
    param23: item.param23,
    param24: item.param24,
    param25: item.param25,
    param26: item.param26,
    param27: item.param28,
    param28: item.param28,
    param29: item.param29,
    param30: item.param30,
    param31: item.param31,
    param32: item.param32,
    param33: item.param33,
    param34: item.param34,
    param35: item.param35,
    param36: item.param36,
    param37: item.param37,
    param38: item.param38,
    param39: item.param39,
    param40: item.param40,
    param41: item.param41,
    param42: item.param42,
    param43: item.param43,
    param44: item.param44,
    param45: item.param45,
    param46: item.param46,
    param47: item.param47,
    param48: item.param48,
    param49: item.param49,
    param50: item.param50,
  }
}

/**
 * 印刷処理
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')
    // 単一印刷の場合、課題分析期間履歴情報の設定
    const historyInfo: {
      appYdd: string
      userId: string
      sc1Id: string
      rirekiId: string
    } = setSinglePrintHistory()
    // 単一印刷の場合、アセスメント期間履歴情報の設定
    const assessmentHistoryInfo: AssessmentHistoryInfoEntity = setSinglePrintAssessmentHistory()
    const reportData: IssuesAnalysisSelectInEntity = {
      // 事業者名
      svJigyoKnj: localOneway.or58482Param.svJigyoId,
      stKeishoFlg: localOneway.commonInfo.keishoFlg,
      stKeisho: localOneway.commonInfo.keishoKnj,
      sysCd: localOneway.commonInfo.sysCd,
      cpnFlg: localOneway.commonInfo.cpnFlg,
      appYdd: historyInfo.appYdd,
      assessmentInfo: assessmentHistoryInfo,
      // 印刷設定
      printSet: {
        shiTeiKubun: mo00039DatePrintType.value,
        shiTeiDate: mo00020DesignationType.value.value
          ? mo00020DesignationType.value.value.split('/').join('-')
          : '',
      },
      // 印刷オプション
      printOption: {
        emptyFlg: mo00018EntryType.value.modelValue
          ? Or58482Const.DEFAULT.STR.ONE
          : Or58482Const.DEFAULT.STR.ZERO,
        honorificFlg: mo00018HonorificsType.value.modelValue
          ? Or58482Const.DEFAULT.STR.ONE
          : Or58482Const.DEFAULT.STR.ZERO,
        keisho: mo00045HonorificsType.value.value,
        colorFlg: mo00018BlankPrintType.value.modelValue
          ? Or58482Const.DEFAULT.STR.ONE
          : Or58482Const.DEFAULT.STR.ZERO,
      },
      // 印刷対象履歴リスト
      printSubjectHistoryList: [],
    }

    const outputLedgerPrintList: OutputLedgerPrintEntity[] = []
    for (const item of ledgerInfo.prtList) {
      //  単一帳票
      if (item.index !== Or58482Const.DEFAULT.STR.TWO) {
        // 印刷対象処理
        const printSubject = setPrintSubject(item)
        outputLedgerPrintList.push(printSubject)
      }
      // 全て帳票
      else if (ledgerInfo.index === Or58482Const.DEFAULT.STR.TWO) {
        // 印刷対象処理
        const printSubject = setPrintSubject(item)
        outputLedgerPrintList.push(printSubject)
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userId
          : Or58482Const.DEFAULT.STR.EMPTY,
      userName:
        ledgerInfo.userList.length > 0
          ? ledgerInfo.userList[0].userName
          : Or58482Const.DEFAULT.STR.EMPTY,
      sc1Id: historyInfo.sc1Id,
      startYmd: Or58482Const.DEFAULT.STR.EMPTY,
      endYmd: Or58482Const.DEFAULT.STR.EMPTY,
      createYmd: Or58482Const.DEFAULT.STR.EMPTY,
      createUserId: Or58482Const.DEFAULT.STR.EMPTY,
      createUserName: Or58482Const.DEFAULT.STR.EMPTY,
      rirekiId: historyInfo.rirekiId,
      result: Or58482Const.DEFAULT.STR.EMPTY,
      choPrtList: outputLedgerPrintList,
    })

    // 帳票出力
    await reportOutput(ledgerInfo.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', ledgerInfo.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 印刷対象履歴リストを取得する 利用者選択が「単一」 履歴選択方法が「複数」
 */
const getDetailListHistory = async () => {
  const issuesAnalysisPeriodList: {
    sc1Id: string
    rirekiId: string
    createYmd: string
  }[] = []
  ledgerInfo.historyList.forEach((item) => {
    issuesAnalysisPeriodList.push({
      sc1Id: item.sc1Id as string,
      rirekiId: item.sc1Id as string,
      createYmd: item.sc1Id as string,
    })
  })
  // 印刷設定情報リストを作成する
  const inputData: IssuesAnalysisPrintSettingsDetailListHistorySelectInEntity = {
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or58482Param.svJigyoId,
    // 期間管理フラグ：画面.期間管理フラグ
    kikanFlg: localOneway.kikanFlg,
    // ケアプラン方式：共通情報.ケアプラン方式
    cpnFlg: localOneway.commonInfo.cpnFlg,
    // 計画書様式：共通情報.計画書様式
    cksFlg: localOneway.commonInfo.cksFlg,
    // 帳票番号：画面.帳票番号
    prtNo: ledgerInfo.prtNo,
    // 利用者リスト：画面.利用者リストに選択対象
    userList: ledgerInfo.userList,
    // 課題分析情報リスト:画面.課題分析情報リストの選択対象
    issuesAnalysisPeriodList: issuesAnalysisPeriodList,
  }
  const res: IssuesAnalysisPrintSettingsDetailListHistorySelectOutEntity =
    await ScreenRepository.select('issuesAnalysisPrintSettingsDetailListHistorySelect', inputData)

  const orX0117HistoryList: OrX0117History[] = []
  if (res.data) {
    for (const history of res.data.printSubjectHistoryList) {
      const reportData: IssuesAnalysisSelectInEntity = {
        // 事業者名
        svJigyoKnj: localOneway.or58482Param.svJigyoId,
        stKeishoFlg: localOneway.commonInfo.keishoFlg,
        stKeisho: localOneway.commonInfo.keishoKnj,
        sysCd: localOneway.commonInfo.sysCd,
        cpnFlg: localOneway.commonInfo.cpnFlg,
        appYdd: Or58482Const.DEFAULT.STR.EMPTY,
        assessmentInfo: {
          sc1Id: Or58482Const.DEFAULT.STR.EMPTY,
          rirekiId: Or58482Const.DEFAULT.STR.EMPTY,
          sparm: Or58482Const.DEFAULT.STR.EMPTY,
          lparm: Or58482Const.DEFAULT.STR.EMPTY,
          argI: Or58482Const.DEFAULT.STR.EMPTY,
          aiRadio: Or58482Const.DEFAULT.STR.EMPTY,
        },
        // 印刷設定
        printSet: {
          shiTeiKubun: mo00039DatePrintType.value,
          shiTeiDate: mo00020DesignationType.value.value,
        },
        // 印刷オプション
        printOption: {
          emptyFlg: mo00018EntryType.value.modelValue
            ? Or58482Const.DEFAULT.STR.ONE
            : Or58482Const.DEFAULT.STR.ZERO,
          honorificFlg: mo00018HonorificsType.value.modelValue
            ? Or58482Const.DEFAULT.STR.ONE
            : Or58482Const.DEFAULT.STR.ZERO,
          keisho: mo00045HonorificsType.value.value,
          colorFlg: mo00018BlankPrintType.value.modelValue
            ? Or58482Const.DEFAULT.STR.ONE
            : Or58482Const.DEFAULT.STR.ZERO,
        },
        // 印刷対象履歴リスト
        printSubjectHistoryList: [],
      }

      const outputLedgerPrintList: OutputLedgerPrintEntity[] = []
      for (const item of ledgerInfo.prtList) {
        //  単一帳票
        if (item.index !== Or58482Const.DEFAULT.STR.TWO) {
          // 印刷対象処理
          const printSubject = setPrintSubject(item)
          outputLedgerPrintList.push(printSubject)
        }
        // 全て帳票
        else if (ledgerInfo.index === Or58482Const.DEFAULT.STR.TWO) {
          // 印刷対象処理
          const printSubject = setPrintSubject(item)
          outputLedgerPrintList.push(printSubject)
        }
      }

      reportData.printSubjectHistoryList.push({
        userId:
          ledgerInfo.userList.length > 0
            ? ledgerInfo.userList[0].userId
            : Or58482Const.DEFAULT.STR.EMPTY,
        userName:
          ledgerInfo.userList.length > 0
            ? ledgerInfo.userList[0].userName
            : Or58482Const.DEFAULT.STR.EMPTY,
        sc1Id: history.issuesAnalysisPeriodHistoryInfo.sc1Id,
        startYmd: Or58482Const.DEFAULT.STR.EMPTY,
        endYmd: Or58482Const.DEFAULT.STR.EMPTY,
        createYmd: Or58482Const.DEFAULT.STR.EMPTY,
        createUserId: Or58482Const.DEFAULT.STR.EMPTY,
        createUserName: Or58482Const.DEFAULT.STR.EMPTY,
        rirekiId: history.issuesAnalysisPeriodHistoryInfo.rirekiId,
        result: Or58482Const.DEFAULT.STR.EMPTY,
        choPrtList: outputLedgerPrintList,
      })
      orX0117HistoryList.push({
        reportId: ledgerInfo.reportId,
        outputType: reportOutputType.DOWNLOAD,
        reportData: reportData,
        userName: history.userName,
        historyDate: Or58482Const.DEFAULT.STR.EMPTY,
        result: history.result,
      })
    }
  }
  orX0117Oneway.historyList = orX0117HistoryList
}

/**
 * 印刷設定情報リストを再取得する 利用者選択が「複数」
 */
const getListHistory = async () => {
  // 印刷設定情報リストを作成する
  const inputData: IssuesAnalysisPrintSettingsListHistorySelectInEntity = {
    // 基準日：画面.基準日
    kijunbi: mo00020BasicType.value.value,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.or58482Param.svJigyoId,
    // 期間管理フラグ：画面.期間管理フラグ
    kikanFlg: localOneway.kikanFlg,
    // ケアプラン方式：共通情報.ケアプラン方式
    cpnFlg: localOneway.commonInfo.cpnFlg,
    // 計画書様式：共通情報.計画書様式
    cksFlg: localOneway.commonInfo.cksFlg,
    // 帳票番号：画面.帳票番号
    prtNo: ledgerInfo.prtNo,
    // 利用者リスト：画面.利用者リストに選択対象
    userList: ledgerInfo.userList,
  }
  const res: IssuesAnalysisPrintSettingsListHistorySelectOutEntity = await ScreenRepository.select(
    'issuesAnalysisPrintSettingsListHistorySelect',
    inputData
  )

  const orX0117HistoryList: OrX0117History[] = []
  if (res.data) {
    for (const history of res.data.printSubjectHistoryList) {
      const reportData: IssuesAnalysisSelectInEntity = {
        // 事業者名
        svJigyoKnj: localOneway.or58482Param.svJigyoId,
        stKeishoFlg: localOneway.commonInfo.keishoFlg,
        stKeisho: localOneway.commonInfo.keishoKnj,
        sysCd: localOneway.commonInfo.sysCd,
        cpnFlg: localOneway.commonInfo.cpnFlg,
        appYdd: Or58482Const.DEFAULT.STR.EMPTY,
        assessmentInfo: {
          sc1Id: Or58482Const.DEFAULT.STR.EMPTY,
          rirekiId: Or58482Const.DEFAULT.STR.EMPTY,
          sparm: Or58482Const.DEFAULT.STR.EMPTY,
          lparm: Or58482Const.DEFAULT.STR.EMPTY,
          argI: Or58482Const.DEFAULT.STR.EMPTY,
          aiRadio: Or58482Const.DEFAULT.STR.EMPTY,
        },
        // 印刷設定
        printSet: {
          shiTeiKubun: mo00039DatePrintType.value,
          shiTeiDate: mo00020DesignationType.value.value,
        },
        // 印刷オプション
        printOption: {
          emptyFlg: mo00018EntryType.value.modelValue
            ? Or58482Const.DEFAULT.STR.ONE
            : Or58482Const.DEFAULT.STR.ZERO,
          honorificFlg: mo00018HonorificsType.value.modelValue
            ? Or58482Const.DEFAULT.STR.ONE
            : Or58482Const.DEFAULT.STR.ZERO,
          keisho: mo00045HonorificsType.value.value,
          colorFlg: mo00018BlankPrintType.value.modelValue
            ? Or58482Const.DEFAULT.STR.ONE
            : Or58482Const.DEFAULT.STR.ZERO,
        },
        // 印刷対象履歴リスト
        printSubjectHistoryList: [],
      }

      const outputLedgerPrintList: OutputLedgerPrintEntity[] = []
      for (const item of ledgerInfo.prtList) {
        //  単一帳票
        if (item.index !== Or58482Const.DEFAULT.STR.TWO) {
          // 印刷対象処理
          const printSubject = setPrintSubject(item)
          outputLedgerPrintList.push(printSubject)
        }
        // 全て帳票
        else if (ledgerInfo.index === Or58482Const.DEFAULT.STR.TWO) {
          // 印刷対象処理
          const printSubject = setPrintSubject(item)
          outputLedgerPrintList.push(printSubject)
        }
      }

      reportData.printSubjectHistoryList.push({
        userId:
          ledgerInfo.userList.length > 0
            ? ledgerInfo.userList[0].userId
            : Or58482Const.DEFAULT.STR.EMPTY,
        userName:
          ledgerInfo.userList.length > 0
            ? ledgerInfo.userList[0].userName
            : Or58482Const.DEFAULT.STR.EMPTY,
        sc1Id: history.issuesAnalysisPeriodHistoryInfo.sc1Id,
        startYmd: Or58482Const.DEFAULT.STR.EMPTY,
        endYmd: Or58482Const.DEFAULT.STR.EMPTY,
        createYmd: Or58482Const.DEFAULT.STR.EMPTY,
        createUserId: Or58482Const.DEFAULT.STR.EMPTY,
        createUserName: Or58482Const.DEFAULT.STR.EMPTY,
        rirekiId: history.issuesAnalysisPeriodHistoryInfo.rirekiId,
        result: history.result,
        choPrtList: outputLedgerPrintList,
      })
      orX0117HistoryList.push({
        reportId: ledgerInfo.reportId,
        outputType: reportOutputType.DOWNLOAD,
        reportData: reportData,
        userName: history.userName,
        historyDate: Or58482Const.DEFAULT.STR.EMPTY,
        result: history.result,
      })
    }
  }
  orX0117Oneway.historyList = orX0117HistoryList
}

/**
 * エラーダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
const openErrorDialog = async (
  paramDialogText: string
): Promise<typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO> => {
  // エラーダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // エラーダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result: typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO = DIALOG_BTN.YES

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
const openConfirmDialog = async (
  paramDialogText: string
): Promise<typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO = DIALOG_BTN.YES

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
const openWarnDialog = async (
  paramDialogText: string
): Promise<typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO> => {
  // 警告ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 警告ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result: typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO = DIALOG_BTN.YES

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }

        // 警告ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * タイトルのfocus一時保存
 */
const saveTitle = () => {
  tempTitle.value = mo00045TitleType.value.value
}
/**
 * タイトルのblur復元
 */
const recoveryTitle = async () => {
  // タイトルが空の場合
  if (!mo00045TitleType.value.value) {
    // メッセージを表示
    const dialogResult = await openWarnDialog(t('message.w-cmn-20845'))
    // はい
    if (dialogResult === DIALOG_BTN.YES || dialogResult === DIALOG_BTN.NO) {
      // タイトル復元
      mo00045TitleType.value.value = tempTitle.value
    }
  }
}

/**
 * 担当ケアマネプルダウン
 *
 * @param result - 戻り値
 */
const orx0145UpdateModelValue = (result: OrX0145Type) => {
  if (result) {
    if (result.value) {
      if (!Array.isArray(result.value) && 'chkShokuId' in result.value) {
        // TODO API疎通時に確認
        orX0130Oneway.tantouCareManager = result.value.chkShokuId
      }
    }

    // 画面.利用者選択が単一の場合
    if (Or58482Const.DEFAULT.TANI === mo00039UserSelectType.value && initFlg.value) {
      // 画面.利用者一覧明細の1件目レコードを選択状態にする
      orX0130Oneway.userId = Or58482Const.DEFAULT.STR.EMPTY
    }
  }
}

/**
 * 初期選択データ設定
 */
const selectRowDataSetting = () => {
  // フォーカス設定用イニシャル設定
  orX0130Oneway.focusSettingInitial = localOneway.or58482Param.focusSettingInitial
  orX0130Oneway.userId = localOneway.or58482Param.userId
  // 利用者一覧明細に親画面.利用者IDが存在する場合
  if (localOneway.or58482Param.userId) {
    // 利用者IDを対するレコードを選択状態にする
    orX0130Oneway.userId = localOneway.or58482Param.userId
  }
  // 親画面.利用者IDが存在しない場合
  else {
    orX0130Oneway.userId = Or58482Const.DEFAULT.STR.EMPTY
  }
  // 親画面.担当ケアマネ設定フラグ > 0、且つ、親画面.担当者IDが0以外の場合：親画面.担当者ID対応する担当者名
  if (
    Number(localOneway.or58482Param.kkjTantoFlg) > 0 &&
    localOneway.or58482Param.tantoId !== Or58482Const.DEFAULT.STR.ZERO
  ) {
    // 初期選択状態の担当者カウンタ値設定
    localOneway.orX0145Oneway.selectedUserCounter = localOneway.or58482Param.selectedUserCounter
  }
  // 以外の場合：""
  else {
    localOneway.orX0145Oneway.selectedUserCounter = Or58482Const.DEFAULT.STR.EMPTY
  }
}

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  isLoading.value = true
  // 汎用コード取得API実行
  await initCodes()
  // 初期情報取得
  await getInitData()
  // 初期選択データ設定
  selectRowDataSetting()
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 「出力帳票名」選択の変更監視
 */
watch(
  () => mo01334TypeLedger.value.value,
  async (newVal, oldValue) => {
    if (!newVal) return
    if (oldValue && newVal !== oldValue) {
      // 「出力帳票名」選択した、画面データを変更
      selectLedgerName(newVal)
      // 選択行のシステムINI情報を取得する
      await getSelectedRowSysInfo()
    }
    // アセスメント一覧制御切替
    // 画面.帳票名が「課題分析(アセスメントの全てと一括印刷)」の場合
    // 且つ 画面.利用者選択方法が「0：単一」の場合 且つ 画面.履歴選択方法が「0：単一」の場合、表示
    if (newVal === Or58482Const.DEFAULT.STR.TWO) {
      changeFlg.value = true
      if (mo00039HistorySelectType.value === OrX0128Const.DEFAULT.TANI) {
        orX0128Oneway.tableStyle = 'width:515px;height:486px'
      } else {
        orX0128Oneway.tableStyle = 'width:515px;height:448px'
      }
      if (
        mo00039UserSelectType.value === Or58482Const.DEFAULT.TANI &&
        mo00039HistorySelectType.value === Or58482Const.DEFAULT.TANI
      ) {
        assessmentAreaShowFlg.value = true
        if (localOneway.commonInfo.cpnFlg !== Or58482Const.CARE_PLAN_KIND.FREE_ASSESSMENT) {
          assessmentTableShowFlg.value = true
        }
        // アセスメント一覧表示項目制御
        setAssessmentItems()
        orX0128Oneway.tableStyle = 'width:515px;height:200px;'
        orX0128Oneway01.tableStyle = 'width:515px;height:262px'
      }
    } else {
      assessmentAreaShowFlg.value = false
      orX0128Oneway.tableStyle = 'width:515px;height:486px'
      assessmentTableShowFlg.value = false
    }
    // 日付印刷区分が2の場合
    if (Or58482Const.DEFAULT.STR.TWO === mo00039DatePrintType.value) {
      // 指定日を活性表示にする。
      designationShowFlag.value = true
    } else {
      // 指定日を非表示にする。
      designationShowFlag.value = false
    }
  }
)

/**
 * タイトルテキストフィールドの変更監視
 */
watch(
  () => mo00045TitleType.value.value,
  () => {
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    const val = ledgerInfo.prtList.find((item) => item.index === mo01334TypeLedger.value.value)
    val!.prtTitle = mo00045TitleType.value.value
  }
)

/**
 * 日付印刷区分の変更監視
 */
watch(
  () => mo00039DatePrintType.value,
  (newValue) => {
    const index = Number(ledgerInfo.index) - 1
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    if (ledgerInfo.prtList[index] && mo01334OnewayLedger.value.items[index]) {
      ledgerInfo.prtList[index].prnDate = mo00039DatePrintType.value
      mo01334OnewayLedger.value.items[index].prnDate = mo00039DatePrintType.value
    }
    // 日付印刷区分が2の場合
    if (Or58482Const.DEFAULT.STR.TWO === newValue) {
      // 指定日を活性表示にする。
      designationShowFlag.value = true
    } else {
      // 指定日を非表示にする。
      designationShowFlag.value = false
    }
  }
)

/**
 * 敬称を変更するチェックボックスの変更監視
 */
watch(
  () => mo00018HonorificsType.value.modelValue,
  (newValue) => {
    const index = Number(ledgerInfo.index) - 1
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    if (ledgerInfo.prtList[index] && mo01334OnewayLedger.value.items[index]) {
      ledgerInfo.prtList[index].param03 = mo00018HonorificsType.value.modelValue
        ? Or58482Const.DEFAULT.STR.ONE
        : Or58482Const.DEFAULT.STR.ZERO
      mo01334OnewayLedger.value.items[index].honorificsCheckboxValue = mo00018HonorificsType.value
        .modelValue
        ? Or58482Const.DEFAULT.STR.ONE
        : Or58482Const.DEFAULT.STR.ZERO
    }
    // 敬称を変更するチェックボックスが選択の場合、活性表示
    if (newValue) {
      localOneway.mo00045OneWayHonorifics.disabled = false
    } else {
      // 以外の場合、非活性表示
      localOneway.mo00045OneWayHonorifics.disabled = true
    }
  },
  { immediate: true }
)

/**
 * 敬称テキストボックスの変更監視
 */
watch(
  () => mo00045HonorificsType.value.value,
  () => {
    const index = Number(ledgerInfo.index) - 1
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    if (ledgerInfo.prtList[index] && mo01334OnewayLedger.value.items[index]) {
      ledgerInfo.prtList[index].param04 = mo00045HonorificsType.value.value
      mo01334OnewayLedger.value.items[index].honorificsTextValue = mo00045HonorificsType.value.value
    }
  }
)

/**
 * 記入用シートを印刷するチェックボックス算出プロパティの監視
 */
watch(
  () => mo00018EntryDisabled.value,
  (newValue) => {
    if (newValue) {
      // 記入用シートを印刷するをチェックオフにする
      mo00018EntryType.value.modelValue = false
      // 以外の場合、非活性表示
      localOneway.mo00018OneWayEntry.disabled = true
    } else {
      // 利用者選択方法が「単一」 また 履歴選択方法が「単一」の場合、活性表示。
      localOneway.mo00018OneWayEntry.disabled = false
    }
  },
  { immediate: true }
)

/**
 * 空白の項目も印刷するチェックボックスの変更監視
 */
watch(
  () => mo00018BlankPrintType.value.modelValue,
  () => {
    const index = Number(ledgerInfo.index) - 1
    // 画面項目変更した後、出力帳票印刷情報リストを更新
    if (ledgerInfo.prtList[index] && mo01334OnewayLedger.value.items[index]) {
      ledgerInfo.prtList[index].param07 = mo00018BlankPrintType.value.modelValue
        ? Or58482Const.DEFAULT.STR.ONE
        : Or58482Const.DEFAULT.STR.ZERO
      mo01334OnewayLedger.value.items[index].blankPrintCheckboxValue = mo00018BlankPrintType.value
        .modelValue
        ? Or58482Const.DEFAULT.STR.ONE
        : Or58482Const.DEFAULT.STR.ZERO
    }
  },
  { immediate: true }
)

/**
 * 利用者選択方法の変更監視
 */
watch(
  () => mo00039UserSelectType.value,
  (newValue, oldValue) => {
    // 利用者選択方法が「単一」 の場合
    if (newValue === OrX0130Const.DEFAULT.TANI) {
      orX0117Oneway.type = Or58482Const.DEFAULT.STR.TWO
      // 利用者一覧テーブルの幅は狭くなる
      userCols.value = 4
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width:204px;height:462px'
      if (
        ledgerInfo.index === Or58482Const.DEFAULT.STR.TWO &&
        mo00039UserSelectType.value === Or58482Const.DEFAULT.TANI &&
        mo00039HistorySelectType.value === Or58482Const.DEFAULT.TANI
      ) {
        assessmentAreaShowFlg.value = true
        assessmentTableShowFlg.value = true
        // アセスメント一覧表示項目制御
        setAssessmentItems()
        orX0128Oneway.tableStyle = 'width:515px;height:200px;'
        orX0128Oneway01.tableStyle = 'width:515px;height:262px'
      } else {
        assessmentAreaShowFlg.value = false
        if (ledgerInfo.index !== Or58482Const.DEFAULT.STR.TWO) {
          orX0128Oneway.tableStyle = 'width:515px;height:486px'
        } else {
          orX0128Oneway.tableStyle = 'width:515px;height:448px'
        }
        assessmentTableShowFlg.value = false
      }
      if (oldValue === OrX0130Const.DEFAULT.HUKUSUU) {
        changeFlg.value = true
      }
    } else {
      orX0117Oneway.type = Or58482Const.DEFAULT.STR.ONE
      // 利用者一覧テーブルの幅は広くなる
      userCols.value = 12
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width:450px;height:462px'
      if (ledgerInfo.index !== Or58482Const.DEFAULT.STR.TWO) {
        orX0128Oneway.tableStyle = 'width:515px;height:486px'
      }
    }
  }
)

/**
 * 履歴選択方法の変更監視
 */
watch(
  () => mo00039HistorySelectType.value,
  (newValue) => {
    // 履歴選択方法が「単一」 の場合
    if (newValue === OrX0128Const.DEFAULT.TANI) {
      orX0128Oneway.singleFlg = OrX0128Const.DEFAULT.TANI
      orX0128Oneway01.singleFlg = OrX0128Const.DEFAULT.TANI
      orX0117Oneway.type = Or58482Const.DEFAULT.STR.TWO

      if (
        ledgerInfo.index === Or58482Const.DEFAULT.STR.TWO &&
        mo00039UserSelectType.value === Or58482Const.DEFAULT.TANI &&
        mo00039HistorySelectType.value === Or58482Const.DEFAULT.TANI
      ) {
        assessmentAreaShowFlg.value = true
        assessmentTableShowFlg.value = true
        // アセスメント一覧表示項目制御
        setAssessmentItems()
        orX0128Oneway.tableStyle = 'width:515px;height:200px;'
        orX0128Oneway01.tableStyle = 'width:515px;height:262px'
        orX0128Oneway01.initSelectId = (
          orX0128Oneway01.items.findIndex(
            (item) => item.rirekiId === (ledgerInfo.assessmentHistoryList[0]?.rirekiId ?? '')
          ) + 1
        ).toString()
      } else {
        assessmentAreaShowFlg.value = false
        if (ledgerInfo.index === '2') {
          orX0128Oneway.tableStyle = 'width:515px;height:448px'
        }
        assessmentTableShowFlg.value = false
      }
    } else {
      orX0128Oneway.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
      orX0117Oneway.type = Or58482Const.DEFAULT.STR.ZERO
      if (mo01334TypeLedger.value.value === Or58482Const.DEFAULT.STR.TWO) {
        // アセスメント区域を隠す
        assessmentAreaShowFlg.value = false
        // 履歴表スタイル調整
        orX0128Oneway.tableStyle = 'width:515px;height:448px'
      }
    }
  }
)

/**
 * 担当ケアマネプルダウンリスト算出プロパティの監視
 */
watch(
  () => orx0145Disabled.value,
  (newValue) => {
    if (newValue) {
      localOneway.orX0145Oneway.disabled = true
    } else {
      localOneway.orX0145Oneway.disabled = false
    }
  },
  { immediate: true }
)

/**
 * 「利用者一覧」明細選択の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue, oldValue) => {
    if (!newValue) return
    if (newValue?.clickFlg) {
      // 利用者一覧選択行 >0 の場合
      if (newValue.userList.length > 0) {
        ledgerInfo.selectedUserId = newValue.userList[0].userId

        // 利用者リスト再設定
        ledgerInfo.userList = []
        for (const item of newValue.userList) {
          if (item) {
            ledgerInfo.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }
        // 履歴初期化選択行IDの再設定
        orX0128Oneway.initSelectId = ''
        // アセスメント履歴選択行再設定
        orX0128Oneway01.initSelectId = ''

        isLoading.value = true
        // 利用者選択方法が「単一」の場合
        if (Or58482Const.DEFAULT.TANI === mo00039UserSelectType.value) {
          if (
            initFlg.value &&
            oldValue?.userList &&
            (oldValue?.userList.length ?? 0) > 0 &&
            !isEqual(newValue.userList, oldValue?.userList)
          ) {
            // 印刷設定履歴リスト取得
            await getPrintSettingsHistoryList()
            // 履歴初期化選択行IDの再設定
            orX0128Oneway.initSelectId = ''
          }
          if (changeFlg.value) {
            // 印刷設定履歴リスト取得
            await getPrintSettingsHistoryList()
            // 履歴初期化選択行IDの再設定
            orX0128Oneway.initSelectId = ''
            changeFlg.value = false
          }
        }
        isLoading.value = false
      } else {
        ledgerInfo.userList = []
        orX0128Oneway.items = []
        orX0128Oneway01.items = []
      }
    } else {
      ledgerInfo.userList = []
      orX0128Oneway.items = []
      orX0128Oneway01.items = []
    }
  }
)

/**
 * 「履歴一覧」明細選択の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    if (newValue.historyDetClickFlg && newValue.orX0128DetList.length > 0) {
      ledgerInfo.historyList = []
      ledgerInfo.historyList = newValue.orX0128DetList
    }
  }
)

/**
 * 「アセスメン履歴一覧」明細選択の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128_1.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    if (newValue.historyDetClickFlg && newValue.orX0128DetList.length > 0) {
      ledgerInfo.assessmentHistoryList = []
      ledgerInfo.assessmentHistoryList = newValue.orX0128DetList
    }
  }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue) {
      void close()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <!-- ローディング -->
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <template #cardItem>
      <c-v-row
        class="or58482-row"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="table-header"
        >
          <!-- 帳票一覧テーブル -->
          <base-mo-01334
            v-model="mo01334TypeLedger"
            :oneway-model-value="mo01334OnewayLedger"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="3"
          class="content-center"
        >
          <c-v-row
            no-gutter
            class="printerOption custom-col or58482-row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pl-2"
            >
              <!-- '基本設定' -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                class="bk-color-transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="custom-col or58482-row"
          >
            <c-v-col
              cols="12"
              sm="3"
              class="label-left padding-bottom-none pl-2"
            >
              <!-- 'タイトル' -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="auto"
              class="label-right pa-2 w-100"
            >
              <!-- 指定された帳票名 -->
              <base-mo00045
                v-model="mo00045TitleType"
                :oneway-model-value="localOneway.mo00045OneWay"
                class="w-100"
                @focus="saveTitle"
                @blur="recoveryTitle"
              ></base-mo00045>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            no-gutter
            class="custom-col or58482-row"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-2 pl-0"
            >
              <!-- 日付印刷区分 -->
              <base-mo00039
                v-model="mo00039DatePrintType"
                :oneway-model-value="localOneway.mo00039OneWayDatePrint"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              class="date-area pa-0 py-2 pr-2"
            >
              <!-- 指定日 -->
              <base-mo00020
                v-if="designationShowFlag"
                v-model="mo00020DesignationType"
                :oneway-model-value="localOneway.mo00020OneWayDesignation"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption custom-col or58482-row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pl-2"
            >
              <!-- '印刷オプション' -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                class="bk-color-transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="custom-col or58482-row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 pt-2"
            >
              <div class="d-flex align-center">
                <!-- 敬称を変更するチェックボックス -->
                <base-mo00018
                  v-model="mo00018HonorificsType"
                  :oneway-model-value="localOneway.mo00018OneWayHonorifics"
                />
                <span>（</span>
                <!-- 敬称テキストフィールド -->
                <base-mo00045
                  v-model="mo00045HonorificsType"
                  :oneway-model-value="localOneway.mo00045OneWayHonorifics"
                  class="honorifics-margin"
                />
                <span>）</span>
              </div>
              <!-- 記入用シートを印刷するチェックボックス -->
              <base-mo00018
                v-model="mo00018EntryType"
                :oneway-model-value="localOneway.mo00018OneWayEntry"
              />
              <!-- 「空白の項目も印刷する」チェックボックス -->
              <base-mo00018
                v-model="mo00018BlankPrintType"
                :oneway-model-value="localOneway.mo00018OneWayBlankPrint"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="7"
          class="pa-0"
        >
          <c-v-row
            class="or58482-row height-81"
            no-gutter
          >
            <c-v-col
              cols="3"
              class="pl-0 py-2 pr-2"
            >
              <div>
                <!-- 利用者選択方法ラジオボタン -->
                <base-mo00039
                  v-model="mo00039UserSelectType"
                  :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                >
                </base-mo00039>
              </div>
            </c-v-col>
            <c-v-col
              v-if="mo00039UserSelectType === '0'"
              cols="3"
              class="pa-2 pl-0"
            >
              <div>
                <!-- 履歴選択方法ラジオボタン -->
                <base-mo00039
                  v-model="mo00039HistorySelectType"
                  :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                >
                </base-mo00039>
              </div>
            </c-v-col>
            <c-v-col
              v-if="mo00020BasicShowFlg"
              cols="4"
              class="pa-2"
            >
              <div class="basic-date-label">
                <!-- 年月日選択セクション -->
                <base-mo00020
                  v-model="mo00020BasicType"
                  :oneway-model-value="localOneway.mo00020OneWayBasic"
                />
              </div>
            </c-v-col>
            <c-v-col
              cols="4"
              class="pa-2"
            >
              <div v-if="localOneway.or58482Param.processYmd">
                <!-- 担当ケアマネプルダウン -->
                <g-custom-or-x-0145
                  v-bind="orX0145"
                  v-model="orX0145Type"
                  :oneway-model-value="localOneway.orX0145Oneway"
                  @update:model-value="orx0145UpdateModelValue"
                />
              </div>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            class="or58482-row"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              class="user-table-area pa-0 pl-2 pb-2 pt-2"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="initFlg"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo00039UserSelectType === '0'"
              style="height: 515px; overflow: hidden"
              cols="8"
              class="pa-2"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0128
                v-bind="orX0128"
                :oneway-model-value="orX0128Oneway"
              ></g-custom-or-x-0128>
              <div
                v-show="assessmentAreaShowFlg"
                class="pt-2"
              >
                <!-- 'アセスメント' -->
                <span class="font-weight-bold">
                  {{ t('label.assessment') }}
                </span>
                <!--アセスメント一覧 -->
                <g-custom-or-x-0128
                  v-show="assessmentTableShowFlg"
                  v-bind="orX0128_1"
                  :oneway-model-value="orX0128Oneway01"
                  class="pt-2"
                ></g-custom-or-x-0128>
              </div>
              <div
                v-if="mo00039HistorySelectType === '1' && mo01334TypeLedger.value === '2'"
                class="pt-2"
              >
                <!-- 説明文言ラベル -->
                <span class="white-space description-font">{{
                  t('label.issues-analysis-print-description')
                }}</span>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="ml-2 mr-0"
          @click="pdfDownload"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813 v-bind="or21813" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
</template>
<style>
.or58482-content {
  padding: 0px !important;
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
.or58482-row {
  margin: 0px !important;
}

.table-header {
  padding: 8px;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}
.content-center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label-left {
    padding-right: 0px;
  }

  .label-right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .custom-col {
    margin-left: 0px;
    margin-right: 0px;
  }
}
.padding-bottom-none {
  padding-bottom: 0px;
}
:deep(.honorifics-margin) {
  margin-left: 8px !important;
  margin-right: 8px !important;
}
.parentheses-class {
  text-align: center;
}

// 出力帳票名一覧ヘーダ
:deep(.v-data-table-header__content) {
  font-weight: bold;
  font-size: 14px;
}
:deep(.basic-date-label > .v-sheet > .text-date-area-style > .pt-1) {
  padding-left: 8px !important;
  padding-top: 0px !important;
  margin-top: 0px !important;
  margin-bottom: 5px !important;
}
// 敬称を変更するラベルスタイル
:deep(.honorifics-label .v-label) {
  font-weight: bold !important;
  color: rgb(var(--v-theme-black-700)) !important;
}
// 記入用シートを印刷するラベルスタイル
:deep(.entry-label .v-label) {
  font-weight: bold !important;
  color: rgb(var(--v-theme-black-700)) !important;
}
// 総括部分を印刷するラベルスタイル
:deep(.blank-item-print-label .v-label) {
  font-weight: bold !important;
  color: rgb(var(--v-theme-black-700)) !important;
}
// '基本設定'margin
:deep(.ml-4) {
  margin-left: 0px !important;
}
// アセスメント表
.assessment-area {
  height: 300px;
}
.white-space {
  white-space: pre;
}
// テーブルの高さ変化の遷移効果を消す
:deep(.v-table) {
  transition: height 0s !important;
}
// 説明文言ラベル
.description-font {
  font-weight: bold;
  color: rgb(var(--v-theme-red-600));
}
// 帳票情報表
:deep(.table-header .v-data-table__td) {
  height: 43px !important;
}
// 利用者一覧区域
:deep(.user-table-area > .v-row > .v-col) {
  padding: 0px 0px 0px 8px;
}
// 指定日
:deep(.date-area .v-field__input) {
  padding-left: 12px !important;
}
// '基本設定'と'印刷設定'
.bk-color-transparent {
  background-color: transparent;
}
.height-81 {
  height: 81px;
}
</style>
