<script lang="ts" setup>
/**
 * Or50148:タイトル
 * GUI01258_支援経過記録
 *
 * @description
 * タイトル
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */

/**************************************************
 * インポート(Imports)
 **************************************************/
import { reactive, onMounted, ref, nextTick, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import type { Or50148StateType, Or50148TwoWayType, TableDataList } from './Or50148.type'
import { Or50148Const } from './Or50148.constants'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or50148OnewayType } from '~/types/cmn/business/components/Or50148Type'
import {
  useSetupChildProps,
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useScreenStore,
} from '#imports'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00046OnewayType, Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo01352OnewayType } from '~/types/business/components/Mo01352Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Mo01376OnewayType, Mo01376Type } from '~/types/business/components/Mo01376Type'
import type { Mo01272Type, Mo01272OnewayType } from '~/types/business/components/Mo01272Type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import type { Or26257Type, Or26257OnewayType } from '~/types/cmn/business/components/Or26257Type'
import { Or28256Const } from '~/components/custom-components/organisms/Or28256/Or28256.constants'
import { Or28256Logic } from '~/components/custom-components/organisms/Or28256/Or28256.logic'
import type { Or28256OnewayType } from '~/components/custom-components/organisms/Or28256/Or28256.type'
import { Or11017Const } from '~/components/custom-components/organisms/Or11017/Or11017.constants'
import { Or11017Logic } from '~/components/custom-components/organisms/Or11017/Or11017.logic'
import { Or52335Const } from '~/components/custom-components/organisms/Or52335/Or52335.constants'
import { Or52335Logic } from '~/components/custom-components/organisms/Or52335/Or52335.logic'
import type {
  Or52335ModelValue,
  Or52335OnewayType,
} from '~/types/cmn/business/components/Or52335Type'
import { Or27011Const } from '~/components/custom-components/organisms/Or27011/Or27011.constants'
import { Or27011Logic } from '~/components/custom-components/organisms/Or27011/Or27011.logic'
import type {
  cpnSypKeikaUpdateInEntity,
  New_type_of_endowment_list,
  Kaigo_shiyen_list,
} from '~/repositories/cmn/entities/cpnSypKeikaUpdateEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or27220Const } from '~/components/custom-components/organisms/Or27220/Or27220.constants'
import { Or27220Logic } from '~/components/custom-components/organisms/Or27220/Or27220.logic'
import type { Or27220OnewayType } from '~/types/cmn/business/components/Or27220Type'
import { Or27592Const } from '~/components/custom-components/organisms/Or27592/Or27592.constants'
import { Or27592Logic } from '~/components/custom-components/organisms/Or27592/Or27592.logic'
import type { Or27592OnewayType } from '~/types/cmn/business/components/Or27592Type'
import { CustomClass } from '~/types/CustomClassType'
import { convHiraganaToHalfKanaMap } from '~/constants/KanaMap'
import { Or22951Const } from '~/components/custom-components/organisms/Or22951/Or22951.constants'
import { Or22951Logic } from '~/components/custom-components/organisms/Or22951/Or22951.logic'
import type { Or22951OnewayType } from '~/types/cmn/business/components/Or22951Type'
import type { ApplicableOfficeResponseInfo } from '~/repositories/cmn/entities/ApplicableOfficeConfirmSelectEntity'
import type { OrX0185OnewayType } from '~/types/cmn/business/components/OrX0185Type'
import { useValidation } from '@/utils/useValidation'
const { byteLength, required } = useValidation()

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or50148OnewayType
  uniqueCpId: string
}

/**
 * 親コンポーネントから受け取るプロパティ
 */
const props = defineProps<Props>()

/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * 画面状態管理用操作変数
 */
const screenStore = useScreenStore()
/**
 * Or21814コンポーネントのユニークID管理用
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * Or51775コンポーネントのユニークID管理用
 */
const or51775 = ref({ uniqueCpId: '' })
/**
 * Or26257コンポーネントのユニークID管理用
 */
const or26257 = ref({ uniqueCpId: '' })
/**
 * Or28256コンポーネントのユニークID管理用
 */
const or28256 = ref({ uniqueCpId: '' })
/**
 * Or11017コンポーネントのユニークID管理用
 */
const or11017 = ref({ uniqueCpId: '', ym: '' })
/**
 * Or52335コンポーネントのユニークID管理用
 */
const or52335 = ref({ uniqueCpId: '' })
/**
 * Or27011コンポーネントのユニークID管理用
 */
const or27011 = ref({ uniqueCpId: '' })
/**
 * Or27220コンポーネントのユニークID管理用
 */
const or27220 = ref({ uniqueCpId: '' })
/**
 * Or27592コンポーネントのユニークID管理用
 */
const Or27592 = ref({ uniqueCpId: '' })
/**
 * Or22951コンポーネントのユニークID管理用
 */
const or22951 = ref({ uniqueCpId: '' })
/**
 * バリデーション結果
 */
const valid = ref(false)

useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or26257Const.CP_ID(0)]: or26257.value,
  [Or28256Const.CP_ID(0)]: or28256.value,
  [Or11017Const.CP_ID(0)]: or11017.value,
  [Or52335Const.CP_ID(0)]: or52335.value,
  [Or27011Const.CP_ID(0)]: or27011.value,
  [Or27220Const.CP_ID(0)]: or27220.value,
  [Or27592Const.CP_ID(0)]: Or27592.value,
  [Or22951Const.CP_ID(0)]: or22951.value,
})

const { refValue } = useScreenTwoWayBind<Or50148TwoWayType>({
  cpId: Or50148Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**
 * 画面で使用するOneWay型のローカルデータ
 */
const localOneway = reactive({
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    width: '90px',
    tooltipText: t('tooltip.add-row'),
    tooltipLocation: 'bottom',
  } as Mo00611OnewayType,
  mo00611OnewayInsertRow: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    width: '90px',
    tooltipText: t('tooltip.insert-row'),
    tooltipLocation: 'bottom',
  } as Mo00611OnewayType,
  mo00611OnewayDisplayOder: {
    btnLabel: t('btn.display-order'),
    width: '65px',
    minWidth: '65px',
    tooltipText: t('tooltip.display-order-dialog'),
    tooltipLocation: 'bottom',
  } as Mo00611OnewayType,
  btnAddItemTooltip: {
    memoInputIconBtn: t('tooltip.add-row'),
    auxiliaryInputDialogBtn: t('tooltip.add-row'),
    memoInputIconBtnInserRow: t('tooltip.insert-row'),
    displayOrderBtn: t('tooltip.display-order-dialog'),
  },
  mo00611OnewayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    width: '90px',
    tooltipText: t('tooltip.duplicate-row'),
    tooltipLocation: 'bottom',
  } as Mo00611OnewayType,
  btnDuplicateRowTooltip: {
    memoInputIconBtn: t('tooltip.duplicate-row'),
    auxiliaryInputDialogBtn: t('tooltip.duplicate-row'),
  },
  mo01265OneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    tooltipText: t('tooltip.delete-row'),
    disabled: false,
  } as Mo01265OnewayType,
  mo00009OnewayUp: {
    class: 'fas fa-chevron-up',
    size: '15px',
  } as Mo00009OnewayType,
  mo00009OnewayDown: {
    class: 'fas fa-chevron-down',
    size: '15px',
  } as Mo00009OnewayType,
  mo00009OnewayEdit: {
    minWidth: '24px',
    minHeight: '24px',
    width: '24px',
    height: '24px',
    icon: true,
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  mo00615Oneway: {
    itemLabel: '',
    showItemLabel: true,
    itemLabelFontWeight: 'normal',
  } as Mo00615OnewayType,
  mo00020Oneway: {
    showItemLabel: false,
    width: '133',
    showSelectArrow: false,
    rules: [required],
  } as Mo00020OnewayType,
  mo01352Oneway: {
    showItemLabel: false,
    hideDetails: true,
    maxlength: '10',
    customClass: new CustomClass({ labelClass: 'ma-1' }),
    hiddenSelectArrow: true,
    width: '115px',
    showNavigationButtons: false,
    textFieldwidth: '115',
  } as Mo01352OnewayType,
  mo00046oneway: {
    itemLabel: '',
    showItemLabel: false,
    isRequired: true,
    maxlength: '4000',
    autoGrow: false,
    rows: 2,
  } as Mo00046OnewayType,
  mo00040Oneway: {
    itemTitle: 'naiyoKnj',
    itemValue: 'svTeikyoCd',
    width: '128px',
    hideDetails: true,
    showItemLabel: false,
    items: [],
    class: 'mr-0',
  } as Mo00040OnewayType,
  mo00046onewayText: {
    itemLabel: '',
    showItemLabel: false,
    isRequired: true,
    maxlength: '4000',
    autoGrow: false,
    rows: 3,
  } as Mo00046OnewayType,
  mo01272Oneway: {
    itemLabel: '',
    showItemLabel: false,
    isRequired: false,
    isVerticalLabel: false,
    hideDetails: 'auto',
    width: '100',
    appendLabel: '',
    selectionMode: true,
    hiddenIcon: true,
    mo01376Oneway: {
      inputMode: false,
      completeVisible: true,
      rangeHhFlg: false,
    },
    mo01375Oneway: {
      rangeHhFlg: false,
    },
  } as Mo01272OnewayType,
  mo01376Oneway: {
    inputMode: false,
    rangeInputMode: true,
    rangeHhFlg: false,
  } as Mo01376OnewayType,
  mo00009OnewaySchedule: {
    btnIcon: 'schedule',
    tooltipText: t('tooltip.display-time-entry'),
    minWidth: '24px',
    minHeight: '24px',
    width: '24px',
    height: '24px',
    icon: true,
    size: '24px',
    color: '#767676',
    labelColor: '#767676',
    density: 'default',
  } as Mo00009OnewayType,
  or51775Oneway: {
    title: 'タイトル',
    screenId: '',
    bunruiId: '',
    t1Cd: '1010',
    t2Cd: '2',
    t3Cd: '0',
    tableName: 'sho_tuc_casebiko',
    columnName: 'title_knj',
    assessmentMethod: '',
    inputContents: '',
    userId: '1',
    mode: '',
  } as Or51775OnewayType,
  or26257Oneway: {
    // システム略称
    sysCdKbn: '3GK',
    // アカウント設定
    secAccountAllFlg: '',
    // 適用事業所ＩＤリスト
    svJigyoIdList: [{ svJigyoId: '1' }],
    // 職員ID
    shokuinId: '',
    // システムコード
    gsysCd: '',
    // モード
    selectMode: '12',
    // 基準日
    kijunYmd: '2025/06/18',
    // 事業所ID
    defSvJigyoId: '1',
    // フィルターフラグ
    filterDwFlg: '1',
    // 雇用状態
    koyouState: '',
    // 地域フラグ
    areaFlg: '',
    // 表示名称リスト
    hyoujiColumnList: [],
    // 未設定フラグ
    misetteiFlg: '1',
    // 他職員参照権限
    otherRead: '',
    // 中止フラグ
    refStopFlg: '',
    // 処理フラグ
    syoriFlg: '',
    // メニュー１ID
    menu1Id: '',
    // 件数フラグ
    kensuFlg: '',
    /** 職員IDリスト */
    shokuinIdList: [],
  } as Or26257OnewayType,
  or28256Oneway: {
    kaisihi: {
      value: '2011/12/26',
    },
    syuuryouhi: {
      value: '2024/01/02',
    },
    houjinId: '1',
    sisetsuId: '1',
    bunruiCd: '',
    koumokuno: 0,
    userId: '1',
    sheetno: '1',
    targetItem: '長期目標',
    caseInformation: '親画面からのデータ',
  } as Or28256OnewayType,
  or27220Oneway: {
    shisetuId: '1',
    svJigyoId: '1',
    cpnFlg: '1',
    isNew: 0,
    saveAuthority: 'T',
  } as Or27220OnewayType,
  or27592OnewayType: {
    userList: [],
    kikanFlg: '1',
    userId: '31',
    sectionName: '1',
    choIndex: '2',
    historyId: '',
    careManagerInChargeSettingsFlag: '1',
    gojuonGyoBango: '2',
    gojuonBoin: '1',
    cpnFlg: '1',
    shosikiFlg: '1',
    tantoId: '1',
    svJigyoKnj: '',
    shienKeikaKirokuYoshiki: '',
    shokuId: '',
    processDate: '',
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
    sysCd: '',
    sysRyaku: '',
  } as Or27592OnewayType,
  or52335OnewayType: {
    cpnFlg: '',
    svJigyoId: '',
    syubetsuId: '',
    shisetuId: '',
    kaigiFlg: '',
    carePlanMethod: '',
    revision: '',
    content: '内容123',
  } as Or52335OnewayType,
  or22951Oneway: {
    /** システムコード */
    systemCode: Or22951Const.DEFAULT.SYS_CODE_60201,
    /** 機能ID */
    functionId: '',
    /** 選択リスト */
    selectList: [
      {
        /** 法人ID */
        houjinId: 'HJ001',
        /** 施設ID */
        shisetsuId: 'ST001',
      },
      {
        /** 法人ID */
        houjinId: 'HJ005',
        /** 施設ID */
        shisetsuId: 'ST005',
      },
      {
        /** 法人ID */
        houjinId: 'HJ008',
        /** 施設ID */
        shisetsuId: 'ST008',
      },
    ],
    /** 利用モード */
    useMode: '3',
    /** 自由パラメータvalL */
    freeparmValL: '1',
  } as Or22951OnewayType,
  orX0185Oneway: {
    height: '100%',
    readOnly: false,
    align: 'center',
    rules: [byteLength(4000)],
  } as OrX0185OnewayType,
})

/**
 * 画面で使用するローカル状態管理用変数
 */
const local = reactive({
  executeFlag: 'getData',
  /** ケアプラン方式フラグ */
  cpn_flg: '5',
  /** 視点 */
  view: '',
  /** 事業者ID */
  sv_jigyo_id: '',
  /** 職員ID */
  shoku_id: '',
  /** システムコード */
  systemCode: '',
  /** 機能ID */
  functionId: '',
  /** 電子カルテ連携フラグ */
  electronicMedicalRecordCooperationFlag: '',
  /** 処理期間開始日 */
  yymm_ymd_start: '',
  /** 処理期間終了日 */
  yymm_ymd_end: '',
  /** 利用者IDList */
  useridList: [],
  /** 支店区分 */
  shitenKbn: '',
  /** システム略称 */
  systemAbbr: '',
  /** 事業所コード */
  jigyoshoCd: '',
  /** 支援経過記録様式 */
  shienKeikaKirokuYoshiki: '',
  /** 編集フラグ */
  importFlag: true,
  /** 編集フラグ */
  visitConfirmFlag: true,
  /** 編集フラグ */
  importingMeetingMinutesFlag: true,
  /** 編集フラグ */
  importInquiryDetailsFlag: true,
})

/**
 * Or26257コンポーネントの型情報
 */
const or26257Type = ref<Or26257Type>({
  shokuin: {
    shokuin1Knj: 'no',
    shokuin2Knj: 'selected',
  },
} as Or26257Type)

/**
 * ボタン種別（0:担当者, 1:記録者）
 */
const isButton = ref<string>('0')
/**
 * ソート対象のカラムキー
 */
const sortKey = ref<string>('')
/**
 * ソート順（true:降順, false:昇順）
 */
const sortDesc = ref<boolean>(false)
/**
 * 時刻選択中の行インデックス
 */
const selectItemTime = ref<number>(0)
/**
 * Or52335コンポーネントのモデル値
 */
const or52335ModelValue = ref<Or52335ModelValue>({
  modelValue: '',
})

/**
 * 支援経過記録一覧データ
 */
const listShokuKnj = ref<{ value: string; label: string }[]>([])

/**
 * 支援経過記録項目一覧データ
 */
const listKoumokuKnj = ref<{ value: string; label: string }[]>([])

/**
 * 担当者一覧データ
 */
const listTantoName = ref<{ value: string; label: string }[]>([])

/**
 * 記録者一覧データ
 */
const listKirokuName = ref<{ value: string; label: string }[]>([])

/**
 * 画面モードタイトル
 */
const isModeTitle = ref<string>('')

/**
 * 選択中の行インデックス
 */
const selectedRowIndex = ref<number>(0)
/**
 * 支店区分
 */
const shitenKbn = ref(Or50148Const.DEFAULT.VALUE_SHITENKBN.MODE_1)
/**
 * システム略称
 */
const systemAbbr = ref(Or50148Const.DEFAULT.SYSCD)
/**
 * 事業所コード
 */
const jigyoshoCd = ref('xxxxx')
/**
 * 支援経過記録様式
 */
const shienKeikaKirokuYoshiki = ref(Or50148Const.DEFAULT.VALUE_SHIENKEIKAKIROKUYOSHIKI.MODE_1)
/**
 * ケアプラン方式が「5」以外かどうか（true: 5以外, false: 5）
 */
const isCarePlan5 = computed(() => local.cpn_flg !== Or50148Const.DEFAULT.VALUE_CPN_FLG)
/**
 * 支店区分が「2」かどうか（true: 2, false: それ以外）
 */
const isShitenKbn = computed(() => local.shitenKbn === Or50148Const.DEFAULT.VALUE_SHITENKBN.MODE_2)
/**
 * 画面モード（各種条件による表示切替用）
 */
const mode = computed(() => {
  // パターン1
  if (
    shitenKbn.value === Or50148Const.DEFAULT.VALUE_SHITENKBN.MODE_1 &&
    local.cpn_flg !== Or50148Const.DEFAULT.VALUE_CPN_FLG &&
    !(
      systemAbbr.value === Or50148Const.DEFAULT.SYSCD &&
      jigyoshoCd.value !== Or50148Const.DEFAULT.JIGYOSHOCD
    )
  ) {
    if (shienKeikaKirokuYoshiki.value === Or50148Const.DEFAULT.VALUE_SHIENKEIKAKIROKUYOSHIKI.MODE_1)
      return Or50148Const.DEFAULT.MODE_VALUE.MODE_1
    if (shienKeikaKirokuYoshiki.value === Or50148Const.DEFAULT.VALUE_SHIENKEIKAKIROKUYOSHIKI.MODE_2)
      return Or50148Const.DEFAULT.MODE_VALUE.MODE_2
  }
  // パターン2
  if (
    shitenKbn.value === Or50148Const.DEFAULT.VALUE_SHITENKBN.MODE_1 &&
    local.cpn_flg !== Or50148Const.DEFAULT.VALUE_CPN_FLG &&
    systemAbbr.value === Or50148Const.DEFAULT.SYSCD &&
    jigyoshoCd.value !== Or50148Const.DEFAULT.JIGYOSHOCD
  ) {
    if (shienKeikaKirokuYoshiki.value === Or50148Const.DEFAULT.VALUE_SHIENKEIKAKIROKUYOSHIKI.MODE_1)
      return Or50148Const.DEFAULT.MODE_VALUE.MODE_3
    if (shienKeikaKirokuYoshiki.value === Or50148Const.DEFAULT.VALUE_SHIENKEIKAKIROKUYOSHIKI.MODE_2)
      return Or50148Const.DEFAULT.MODE_VALUE.MODE_4
  }
  // パターン3 (mode 5)
  if (
    shitenKbn.value === Or50148Const.DEFAULT.VALUE_SHITENKBN.MODE_1 &&
    local.cpn_flg === Or50148Const.DEFAULT.VALUE_CPN_FLG
  ) {
    if (shienKeikaKirokuYoshiki.value === Or50148Const.DEFAULT.VALUE_SHIENKEIKAKIROKUYOSHIKI.MODE_2)
      return Or50148Const.DEFAULT.MODE_VALUE.MODE_6
    return Or50148Const.DEFAULT.MODE_VALUE.MODE_5
  }
  // パターン4
  if (
    shitenKbn.value === Or50148Const.DEFAULT.VALUE_SHITENKBN.MODE_2 &&
    local.cpn_flg !== Or50148Const.DEFAULT.VALUE_CPN_FLG &&
    !(
      systemAbbr.value === Or50148Const.DEFAULT.SYSCD &&
      jigyoshoCd.value !== Or50148Const.DEFAULT.JIGYOSHOCD
    )
  ) {
    if (shienKeikaKirokuYoshiki.value === Or50148Const.DEFAULT.VALUE_SHIENKEIKAKIROKUYOSHIKI.MODE_1)
      return Or50148Const.DEFAULT.MODE_VALUE.MODE_7
    if (shienKeikaKirokuYoshiki.value === Or50148Const.DEFAULT.VALUE_SHIENKEIKAKIROKUYOSHIKI.MODE_2)
      return Or50148Const.DEFAULT.MODE_VALUE.MODE_8
  }
  // パターン5
  if (
    shitenKbn.value === Or50148Const.DEFAULT.VALUE_SHITENKBN.MODE_2 &&
    local.cpn_flg !== Or50148Const.DEFAULT.VALUE_CPN_FLG &&
    systemAbbr.value === Or50148Const.DEFAULT.SYSCD &&
    jigyoshoCd.value !== Or50148Const.DEFAULT.JIGYOSHOCD
  ) {
    if (shienKeikaKirokuYoshiki.value === Or50148Const.DEFAULT.VALUE_SHIENKEIKAKIROKUYOSHIKI.MODE_1)
      return Or50148Const.DEFAULT.MODE_VALUE.MODE_9
    if (shienKeikaKirokuYoshiki.value === Or50148Const.DEFAULT.VALUE_SHIENKEIKAKIROKUYOSHIKI.MODE_2)
      return Or50148Const.DEFAULT.MODE_VALUE.MODE_10
  }
  // パターン5 (mode 10)
  if (
    shitenKbn.value === Or50148Const.DEFAULT.VALUE_SHITENKBN.MODE_2 &&
    local.cpn_flg === Or50148Const.DEFAULT.VALUE_CPN_FLG
  ) {
    return Or50148Const.DEFAULT.MODE_VALUE.MODE_10
  }
  return Or50148Const.DEFAULT.MODE_VALUE.MODE_0
})

/**
 * 年月日カラム定義
 */
const yearMonthDay = {
  key: 'yymmYmd',
  title: t('label.support-elapsed-record-table-date'),
  sortable: true,
  align: 'left',
  width: '145px',
  isButton: false,
  required: true,
  isCount: false,
  countContent: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * 時間カラム定義
 */
const timeColumn = {
  key: 'fromTimeHhmm',
  title: t('label.support-elapsed-record-time'),
  sortable: true,
  align: 'left',
  width: '81px',
  isButton: false,
  required: false,
  isCount: false,
  countContent: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * メモカラム定義
 */
const memoColumn = {
  key: 'titleKnj',
  title: t('label.memo'),
  sortable: true,
  align: 'left',
  width: '155px',
  isButton: false,
  required: false,
  isCount: false,
  countContent: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * 種別カラム定義
 */
const typeColumn = {
  key: '',
  title: t('label.support-elapsed-record-table-kind'),
  width: '144px',
  align: 'left',
  sortable: true,
  isButton: false,
  required: false,
  isCount: false,
  countContent: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}
/**
 * 内容カラム定義
 */
const contentColumn = {
  key: 'memoKnj',
  title: t('label.support-elapsed-record-table-content'),
  width: '367px',
  align: 'left',
  sortable: true,
  isButton: false,
  required: false,
  isCount: true,
  nameButton: 'content',
  countContent: true,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * 記録者カラム定義
 */
const recorderColumn = {
  key: 'shokuKnj',
  title: t('label.support-elapsed-record-table-recorder'),
  sortable: true,
  align: 'left',
  width: '143px',
  isButton: false,
  required: false,
  isCount: false,
  countContent: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * 項目カラム定義
 */
const projectColumn = {
  key: 'koumokuKnj',
  title: t('label.support-elapsed-record-table-project'),
  width: '150px',
  align: 'left',
  sortable: true,
  isButton: false,
  required: false,
  nameButton: 'project',
  countContent: false,
  isCount: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * 利用者名カラム定義
 */
const userNameColumn = {
  key: 'nameUser',
  title: t('label.support-elapsed-record-table-user-name'),
  sortable: true,
  align: 'left',
  width: '120px',
  isButton: false,
  required: true,
  isCount: false,
  countContent: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * 日付カラム定義
 */
const dateColumn = {
  key: 'yymmYmd',
  title: t('label.support-elapsed-record-table-date-column'),
  sortable: true,
  align: 'left',
  width: '150px',
  isButton: false,
  required: true,
  isCount: false,
  countContent: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * サービス提供種別カラム定義
 */
const serviceTypeColumn = {
  key: 'memoKnj',
  title: t('label.support-elapsed-record-table-service-type'),
  width: '150px',
  align: 'left',
  sortable: true,
  isButton: false,
  required: false,
  countContent: false,
  isCount: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * 担当者カラム定義
 */
const managerColumn = {
  key: 'tantoName',
  title: t('label.support-elapsed-record-table-manager'),
  width: '150px',
  align: 'left',
  sortable: true,
  isButton: false,
  required: false,
  isCount: false,
  countContent: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * 時刻／所要時間カラム定義
 */
// const travelTimeColumn = {
//   key: 'memoKnj',
//   title: t('label.support-elapsed-record-table-travel-time'),
//   sortable: true,
//   align: 'left',
//   width: '150px',
//   isButton: false,
//   required: false,
//   isCount: false,
//   countContent: false,
//   mode: mode.value,
//   colspan: '',
//   rowspan: '',
// }

/**
 * 対象帳票カラム定義
 */
const targetFormColumn = {
  key: '',
  title: t('label.support-elapsed-record-table-target-form'),
  sortable: true,
  align: 'left',
  width: '185px',
  isButton: false,
  required: false,
  isCount: false,
  countContent: false,
  mode: mode.value,
  colspan: '',
  rowspan: '',
}

/**
 * テーブル各カラムの最小幅定義（画面モードごとに切り替え）
 */
const columnMinWidth = computed<number[]>(() => {
  switch (mode.value) {
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_1:
      return [145, 82, 212, 153, 367, 143]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_2:
      return [145, 82, 155, 153, 154, 367, 130, 185]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_3:
      return [145, 82, 212, 153, 367, 130]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_4:
      return [145, 82, 155, 153, 154, 367, 130, 185]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_5:
      return [145, 82, 212, 153, 367, 150, 150]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_6:
      return [120, 145, 82, 212, 153, 367, 150]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_7:
      return [120, 145, 82, 155, 144, 150, 367, 150]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_8:
      return [120, 145, 82, 155, 144, 367, 150, 185]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_9:
      return [120, 145, 82, 155, 144, 150, 367, 150, 185]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_10:
      return [120, 145, 82, 155, 150, 367, 150, 150]
    default:
      return []
  }
})

/**
 * テーブルヘッダ定義（画面モードごとに切り替え）
 */
const headersItemsByMode = computed(() => {
  switch (mode.value) {
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_1:
      return [
        {
          isCheck: false,
          items: [
            yearMonthDay,
            timeColumn,
            memoColumn,
            { ...typeColumn, key: isCarePlan5.value ? 'caseCd' : 'svTeikyoCd' },
            contentColumn,
            recorderColumn,
          ],
          childrenItems: [],
        },
      ]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_2:
      return [
        {
          isCheck: false,
          items: [
            yearMonthDay,
            timeColumn,
            memoColumn,
            { ...typeColumn, key: isCarePlan5.value ? 'caseCd' : 'svTeikyoCd' },
            projectColumn,
            contentColumn,
            recorderColumn,
            targetFormColumn,
          ],
          childrenItems: [],
        },
      ]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_3:
      return [
        {
          isCheck: false,
          items: [
            yearMonthDay,
            timeColumn,
            memoColumn,
            { ...typeColumn, key: isCarePlan5.value ? 'caseCd' : 'svTeikyoCd' },
            contentColumn,
            recorderColumn,
          ],
          childrenItems: [],
        },
      ]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_4:
      return [
        {
          isCheck: false,
          items: [
            yearMonthDay,
            timeColumn,
            memoColumn,
            { ...typeColumn, key: isCarePlan5.value ? 'caseCd' : 'svTeikyoCd' },
            projectColumn,
            contentColumn,
            recorderColumn,
            targetFormColumn,
          ],
          childrenItems: [],
        },
      ]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_5:
      return [
        {
          isCheck: false,
          items: [
            yearMonthDay,
            timeColumn,
            memoColumn,
            serviceTypeColumn,
            {
              ...contentColumn,
              key: 'svTeikyoCd',
            },
            managerColumn,
            // travelTimeColumn,
            {
              ...recorderColumn,
              key: 'kirokuName',
            },
          ],
          childrenItems: [],
        },
      ]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_6:
      return [
        {
          isCheck: false,
          items: [
            userNameColumn,
            yearMonthDay,
            timeColumn,
            memoColumn,
            { ...typeColumn, key: isCarePlan5.value ? 'caseCd' : 'svTeikyoCd' },
            contentColumn,
            recorderColumn,
          ],
          childrenItems: [],
        },
      ]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_7:
      return [
        {
          isCheck: false,
          items: [
            userNameColumn,
            yearMonthDay,
            timeColumn,
            memoColumn,
            { ...typeColumn, key: isCarePlan5.value ? 'caseCd' : 'svTeikyoCd' },
            projectColumn,
            contentColumn,
            recorderColumn,
          ],
          childrenItems: [],
        },
      ]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_8:
      return [
        {
          isCheck: false,
          items: [
            userNameColumn,
            yearMonthDay,
            timeColumn,
            memoColumn,
            { ...typeColumn, key: isCarePlan5.value ? 'caseCd' : 'svTeikyoCd' },
            contentColumn,
            recorderColumn,
            targetFormColumn,
          ],
          childrenItems: [],
        },
      ]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_9:
      return [
        {
          isCheck: false,
          items: [
            userNameColumn,
            yearMonthDay,
            timeColumn,
            memoColumn,
            { ...typeColumn, key: isCarePlan5.value ? 'caseCd' : 'svTeikyoCd' },
            projectColumn,
            contentColumn,
            recorderColumn,
            targetFormColumn,
          ],
          childrenItems: [],
        },
      ]
    case Or50148Const.DEFAULT.MODE_VALUE.MODE_10:
      return [
        {
          isCheck: false,
          items: [
            {
              ...userNameColumn,
            },
            dateColumn,
            timeColumn,
            memoColumn,
            serviceTypeColumn,
            {
              ...contentColumn,
              key: 'svTeikyoCd',
            },
            managerColumn,
            // travelTimeColumn,
            {
              ...recorderColumn,
              key: 'kirokuName',
            },
          ],
          childrenItems: [],
        },
      ]
    default:
      return []
  }
})

/**
 * 選択中の行の内容（memoKnj）を取得・設定する計算プロパティ
 */
const selectItem = computed({
  get() {
    return refValue.value?.shiyen_list_mapping?.[selectedRowIndex.value]?.memoKnj
  },
  set(newValue) {
    const list = refValue.value?.shiyen_list_mapping ?? []
    if (list[selectedRowIndex.value]) {
      list[selectedRowIndex.value].memoKnj = newValue ?? ({ value: '' } as Mo00046Type)
    }
  },
})

/**
 *  Or21814ダイアログの表示状態を管理する計算プロパティ
 */
const showDialogOr21814 = computed(() => {
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Or51775ダイアログの表示状態を管理する計算プロパティ
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Or26257ダイアログの表示状態を管理する計算プロパティ
 */
const showDialogOr26257 = computed(() => {
  // Or26257のダイアログ開閉状態
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Or28256ダイアログの表示状態を管理する計算プロパティ
 */
const showDialogOr28256 = computed(() => {
  // Or28256のダイアログ開閉状態
  return Or28256Logic.state.get(or28256.value.uniqueCpId)?.isOpen ?? false
})

/**
 *Or11017ダイアログの表示状態を管理する計算プロパティ
 */
const showDialogOr11017 = computed(() => {
  return Or11017Logic.state.get(or11017.value.uniqueCpId)?.isOpen ?? false
})

/**
 *Or52335ダイアログの表示状態を管理する計算プロパティ
 */
const showDialogOr52335 = computed(() => {
  // Or52335のダイアログ開閉状態
  return Or52335Logic.state.get(or52335.value.uniqueCpId)?.isOpen ?? false
})

/**
 *Or27011ダイアログの表示状態を管理する計算プロパティ
 */
const showDialogOr27011 = computed(() => {
  return Or27011Logic.state.get(or27011.value.uniqueCpId)?.isOpen ?? false
})

/**
 *Or27220ダイアログの表示状態を管理する計算プロパティ
 */
const showDialogOr27220 = computed(() => {
  // Or27220のダイアログ開閉状態
  return Or27220Logic.state.get(or27220.value.uniqueCpId)?.isOpen ?? false
})

/**
 *Or27592ダイアログの表示状態を管理する計算プロパティ
 */
const isShowDialogOr27592 = computed(() => {
  return Or27592Logic.state.get(Or27592.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr22951 = computed(() => {
  // Or22951のダイアログ開閉状態
  return Or22951Logic.state.get(or22951.value.uniqueCpId)?.isOpen ?? false
})

/**
 * テーブル表示用データ（ソート・フィルタ済み）
 */
const displayDatatable = computed<TableDataList[]>(() => {
  const data = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)
  if (!sortKey.value) return [...data]

  return [...data].sort((a: TableDataList, b: TableDataList) => {
    if (
      sortKey.value === Or50148Const.DEFAULT.KEY_SORT.YYMMYMD ||
      sortKey.value === Or50148Const.DEFAULT.KEY_SORT.MEMO_KNJ
    ) {
      const keys: (keyof TableDataList)[] = ['yymmYmd', 'fromTimeHhmm', 'toTimeHhmm', 'titleKnj']

      for (const k of keys) {
        const aRaw = a[k]
        const bRaw = b[k]

        const aVal = (aRaw as { value?: string }).value ?? ''
        const bVal = (bRaw as { value?: string }).value ?? ''

        const aHas = aVal !== Or50148Const.DEFAULT.EMPTY
        const bHas = bVal !== Or50148Const.DEFAULT.EMPTY

        if (!aHas && !bHas) continue
        if (!aHas) return sortDesc.value ? 1 : -1
        if (!bHas) return sortDesc.value ? -1 : 1

        if (aVal !== bVal) {
          return sortDesc.value ? (aVal < bVal ? 1 : -1) : aVal > bVal ? 1 : -1
        }
      }

      return 0
    }
    const key = sortKey.value as keyof TableDataList
    let aValue = a[key]!
    let bValue = b[key]!

    if ((aValue as { value: string }).value) {
      aValue = (aValue as { value: string }).value
    }

    if ((bValue as { value: string }).value) {
      bValue = (bValue as { value: string }).value
    }

    if ((aValue as { modelValue: string }).modelValue) {
      aValue = (a[key] as { modelValue: string }).modelValue
    }

    if ((bValue as { modelValue: string }).modelValue) {
      bValue = (b[key] as { modelValue: string }).modelValue
    }

    if (aValue === bValue) return 0
    if (sortDesc.value) {
      return aValue < bValue ? 1 : -1
    } else {
      return aValue > bValue ? 1 : -1
    }
  })
})

/**
 * 現在のインデックスと総数を表示する値
 */
// const currentIndexDisplay = computed(() => {
//   const total = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)?.length ?? 0
//   return `${String(total === Or50148Const.DEFAULT.DEFALUT_LENGHT_0 ? 0 : selectedRowIndex.value + 1).padStart(2, '0')}/${String(total).padStart(2, '0')}`
// })

/**
 * 画面モードに応じた表示カラム数を返す計算プロパティ
 * 各モードごとに表示するカラム数を定義
 */
// const displayCollumMode = computed(() => {
//   switch (mode.value) {
//     case Or50148Const.DEFAULT.MODE_VALUE.MODE_1:
//       return 11
//     case Or50148Const.DEFAULT.MODE_VALUE.MODE_2:
//       return 11
//     case Or50148Const.DEFAULT.MODE_VALUE.MODE_3:
//       return 9
//     case Or50148Const.DEFAULT.MODE_VALUE.MODE_4:
//       return 11
//     case Or50148Const.DEFAULT.MODE_VALUE.MODE_5:
//       return 9
//     case Or50148Const.DEFAULT.MODE_VALUE.MODE_6:
//       return 10
//     case Or50148Const.DEFAULT.MODE_VALUE.MODE_7:
//       return 10
//     case Or50148Const.DEFAULT.MODE_VALUE.MODE_8:
//       return 9
//     case Or50148Const.DEFAULT.MODE_VALUE.MODE_9:
//       return 11
//     case Or50148Const.DEFAULT.MODE_VALUE.MODE_10:
//       return 9
//     default:
//       return 12
//   }
// })
/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or50148StateType>({
  cpId: Or50148Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      void (async () => {
        local.cpn_flg = value?.cpn_flg ?? ''
        shitenKbn.value = value?.shitenKbn ?? ''
        systemAbbr.value = value?.systemAbbr ?? ''
        jigyoshoCd.value = value?.jigyoshoCd ?? ''
        shienKeikaKirokuYoshiki.value = value?.shienKeikaKirokuYoshiki ?? ''

        Object.assign(local, value)
        // 調査アセスメント種別設定
        switch (value?.executeFlag) {
          // 保存
          case 'save':
            void onSave()
            break
          case 'saveAndInit': {
            const res = await onSave()
            if (res) {
              void value.initialFunc!()
              void getInitDataInfo()
            }
            break
          }
          case 'getData':
            void value.initialFunc!()
            void getInitDataInfo()
            break
          case 'master':
            void onMaster()
            break
          case 'printSetting':
            void onPrintSetting()
            break
          case 'import':
            void onImport()
            break
          case 'visitConfirm':
            void onVisitConfirm()
            break
          case 'importingMeetingMinutes':
            void onImportingMeetingMinutes()
            break
          case 'importInquiryDetails':
            void onImportInquiryDetails()
            break
          default:
            break
        }
      })()
    },
  },
})

/**
 * コンポーネントマウント時の初期処理
 * 画面初期情報を取得する
 */
onMounted(() => {
  getInitDataInfo()
})

/**
 * 支援経過記録の初期データを取得する
 */
watch(
  () => systemCommonsStore.getUserSelectFilterInitials(),
  (newValue) => {
    if (newValue) {
      if (shitenKbn.value !== Or50148Const.DEFAULT.VALUE_SHITENKBN.MODE_1) {
        refValue.value!.shiyen_list_mapping.forEach((item) => {
          if (Array.isArray(newValue) && newValue.length > 0) {
            if (newValue.includes(Or50148Const.DEFAULT.SELECT_ALL)) {
              item.visible = true
            } else if (newValue.includes(Or50148Const.DEFAULT.SELECT_HE)) {
              const regex = /^[ぁ-ん]/u
              item.visible = regex.test(item.nameKanaSei?.charAt(0))
            } else {
              item.visible = newValue.some((initialValue: string) =>
                yomiHitCheck(item.nameKanaSei, initialValue)
              )
            }
          } else {
            item.visible = true
          }
        })
      }
    }
  },
  { immediate: true }
)

/**
 * 時刻選択中の行のtimneControl変更時にfrom/toの値を同期するwatcher
 */
watch(
  () =>
    selectItemTime.value !== Or50148Const.DEFAULT.NULL && refValue.value?.shiyen_list_mapping
      ? refValue.value.shiyen_list_mapping[selectItemTime.value]
      : undefined,
  (newValue) => {
    if (newValue) {
      newValue.fromTimeHhmm.value = (newValue.timneControl as { value: string }).value
      newValue.toTimeHhmm.value = (newValue.timneControl as { valueTo: string }).valueTo
    }
  },
  { deep: true, immediate: true }
)

/**
 * Or26257ダイアログで職員選択後、担当者・記録者・記録者名を更新するwatcher
 */
watch(
  () => or26257Type.value,
  (newValue) => {
    if (newValue) {
      const data = refValue.value!.shiyen_list_mapping.filter((item) => item.visible) ?? []
      if (isCarePlan5.value) {
        data[selectedRowIndex.value].shokuKnj =
          `${newValue.shokuin?.shokuin1Knj ?? ''} ${newValue.shokuin?.shokuin2Knj ?? ''}`
        data[selectedRowIndex.value].staffid = newValue.shokuin?.chkShokuId ?? ''

        if (isButton.value === Or50148Const.DEFAULT.BUTTON_VALUE.VALUE_0) {
          console.log(data[selectedRowIndex.value].tantoName)
          if (refValue.value?.shiyen_list_mapping) {
            data[selectedRowIndex.value].tantoName =
              `${newValue.shokuin?.shokuin1Knj ?? ''} ${newValue.shokuin?.shokuin2Knj ?? ''}`
          }
        } else if (isButton.value === Or50148Const.DEFAULT.BUTTON_VALUE.VALUE_1) {
          data[selectedRowIndex.value].kirokuName =
            `${newValue.shokuin?.shokuin1Knj ?? ''} ${newValue.shokuin?.shokuin2Knj ?? ''}`
        }
      } else {
        if (isButton.value === Or50148Const.DEFAULT.BUTTON_VALUE.VALUE_0) {
          console.log(data[selectedRowIndex.value].tantoName)
          if (refValue.value?.shiyen_list_mapping) {
            data[selectedRowIndex.value].tantoName =
              `${newValue.shokuin?.shokuin1Knj ?? ''} ${newValue.shokuin?.shokuin2Knj ?? ''}`
          }
        } else if (isButton.value === Or50148Const.DEFAULT.BUTTON_VALUE.VALUE_1) {
          data[selectedRowIndex.value].kirokuName =
            `${newValue.shokuin?.shokuin1Knj ?? ''} ${newValue.shokuin?.shokuin2Knj ?? ''}`
        }
      }
    }
  }
)

/**
 * Or52335ダイアログで内容を編集した際にmemoKnjへ反映するwatcher
 */
watch(
  () => or52335ModelValue.value,
  (newValue) => {
    const value =
      refValue.value?.shiyen_list_mapping?.[selectedRowIndex.value].memoKnj ??
      ({ value: '' } as Mo00046Type)
    if (value.value) {
      value.value = newValue?.modelValue
    }
  }
)

/**
 * 読み仮名ヒットチェック
 * 対象ワードの1文字目と、検索文字の値が一致するか比較します。
 * 比較の際は、濁点・半濁点を無視するために半角カタカナで比較します。
 * true : 一致する
 * false : 一致しない
 *
 * @param targetWord - 対象ワード
 *
 * @param searchChar - 検索文字（50音ヘッドラインの1文字）
 */
function yomiHitCheck(targetWord: string, searchChar: string) {
  if (!(targetWord?.charAt(0) in convHiraganaToHalfKanaMap)) {
    return false
  }
  if (!(searchChar in convHiraganaToHalfKanaMap)) {
    return false
  }

  // 対象ワードの1文字目の半角カタカナ
  const targetWordKana = convHiraganaToHalfKanaMap[targetWord.charAt(0)]

  // 検索文字の半角カタカナ
  const searchCharKana = convHiraganaToHalfKanaMap[searchChar.charAt(0)]

  // 濁点・半濁点を考慮して、1文字目を対象に比較
  if (targetWordKana.startsWith(searchCharKana.charAt(0))) {
    return true
  } else {
    return false
  }
}

/**
 *  AC004_「印刷」アイコン押下
 */
const onPrintSetting = () => {
  //TODO: GUI01264_印刷設定画面を開く。
  const selectedItem = refValue.value?.shiyen_list_mapping[selectedRowIndex.value]
  localOneway.or27592OnewayType.cpnFlg = local.cpn_flg
  localOneway.or27592OnewayType.userId = selectedItem?.extraFields?.userid ?? ''

  Or27592Logic.state.set({
    uniqueCpId: Or27592.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  AC005_「マスタ」アイコン押下
 */
const onMaster = () => {
  //GUI01255_支援経過記録マスタ画面を開く。
  localOneway.or27220Oneway.isNew = 1
  Or27220Logic.state.set({
    uniqueCpId: or27220.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  AC008_「オプション」メニュー「ケース取込」押下
 */
const onImport = () => {
  if (
    systemAbbr.value === Or50148Const.DEFAULT.SYSCD &&
    jigyoshoCd.value === Or50148Const.DEFAULT.VALUE_JIGYOSHO_CD
  ) {
    //GUI02259_適用事業所の選択画面を開く。
    Or22951Logic.state.set({
      uniqueCpId: or22951.value.uniqueCpId,
      state: { isOpen: true },
    })
  } else {
    //GUI01016_ケース一覧選択画面を開く。
    const itemSelected = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)[
      selectedRowIndex.value
    ]
    if (itemSelected) {
      localOneway.or28256Oneway.houjinId = itemSelected?.extraFields?.houjinId ?? ''
      localOneway.or28256Oneway.sisetsuId = itemSelected?.extraFields?.sisetsuId ?? ''
    }
    Or28256Logic.state.set({
      uniqueCpId: or28256.value.uniqueCpId,
      state: {
        isOpen: true,
        items: [],
      },
    })
  }
}

/**
 *  データ返却処理
 *
 * @param retData - 返却データ
 */
function onChangeData(retData: ApplicableOfficeResponseInfo[]) {
  console.log(retData)
}

/**
 *  AC009_「オプション」メニュー「訪問確認」押下
 */
const onVisitConfirm = () => {
  //GUI01259_支援経過確認一覧画面を開く。
  if (local.yymm_ymd_start) {
    const [year, month] = local.yymm_ymd_start.split('/').slice(0, 2)
    or11017.value.ym = `${year}/${month}`
  }

  Or11017Logic.state.set({
    uniqueCpId: or11017.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  AC010_「オプション」メニュー「会議録取込」押下
 */
const onImportingMeetingMinutes = () => {
  //GUI01260_会議録取込画面を開く。
  const value = refValue.value?.shiyen_list_mapping?.[selectedRowIndex.value]?.memoKnj

  localOneway.or52335OnewayType.content = value?.value ?? ''
  localOneway.or52335OnewayType.carePlanMethod = local.cpn_flg
  localOneway.or52335OnewayType.kaigiFlg = shitenKbn.value
  localOneway.or52335OnewayType.revision = shienKeikaKirokuYoshiki.value

  Or52335Logic.state.set({
    uniqueCpId: or52335.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  AC011_「オプション」メニュー「照会内容取込」押下
 */
const onImportInquiryDetails = () => {
  //GUI01261_照会内容取込画面を開く。
  Or27011Logic.state.set({
    uniqueCpId: or27011.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  画面初期情報取得
 */
const getInitDataInfo = () => {
  if (refValue.value) {
    selectedRowIndex.value = 0
    if (isCarePlan5.value) {
      localOneway.mo00040Oneway.itemTitle = 'shuruiKnj'
      localOneway.mo00040Oneway.itemValue = 'shuruiCd'
      localOneway.mo00040Oneway.items = refValue.value.shubetsu_list
    } else {
      localOneway.mo00040Oneway.itemTitle = 'naiyoKnj'
      localOneway.mo00040Oneway.itemValue = 'svTeikyoCd'
      localOneway.mo00040Oneway.items = refValue.value.service_list
    }
  }

  listShokuKnj.value = [
    { value: 'SB01', label: 'サービス提供' },
    { value: 'SB02', label: '相談支援' },
  ]

  listKoumokuKnj.value = [
    { value: 'KM01', label: '面談' },
    { value: 'KM02', label: '電話' },
    { value: 'KM03', label: '訪問' },
  ]

  listTantoName.value = [
    { value: 'TN01', label: '山田 太郎' },
    { value: 'TN02', label: '鈴木 次郎' },
    { value: 'TN03', label: '高橋 三郎' },
  ]

  listKirokuName.value = [
    { value: 'KN01', label: '山田 太郎' },
    { value: 'KN02', label: '鈴木 次郎' },
    { value: 'KN03', label: '高橋 三郎' },
  ]
}

/**
 * 情報ダイアログを開く
 */
const openInfoDialog = async (): Promise<string> => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10219'),

      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),

      secondBtnType: 'blank',

      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })

  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        if (event?.thirdBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * AC016_行追加
 */
const onAddItems = async () => {
  const keys = Object.keys(
    isCarePlan5.value
      ? (refValue.value?.kaigo_shiyen_list?.[0] ?? {})
      : (refValue.value?.shogu_shiyen_list?.[0] ?? {})
  )

  const emptyItem = keys.reduce(
    (obj, key) => {
      obj[key] = ''
      return obj
    },
    {} as Record<string, string>
  )

  if (isShitenKbn.value) {
    //TODO: GUI02295_利用者選択画面を開く。
    return
  } else {
    const currentUser = systemCommonsStore.getCurrentUser
    refValue.value?.shiyen_list_mapping.push({
      extraFields: emptyItem,
      uniqueId: uuidv4(),
      staffid: '',
      recNo: '',
      svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
      timeStmp: systemCommonsStore.getSystemDate ?? '',
      userid: systemCommonsStore.getUserId ?? '',
      houjinId: systemCommonsStore.getHoujinId ?? '',
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      /** 利用者名 */
      nameKanaSei: '',
      nameUser: '',
      yymmYmd: {
        value: systemCommonsStore.getSystemDate,
        mo01343: {
          value: systemCommonsStore.getSystemDate,
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        } as Mo01343Type,
      } as Mo00020Type,
      fromTimeHhmm: {
        value: '00:00',
      } as Mo01272Type,
      toTimeHhmm: {
        value: '00:00',
      } as Mo01272Type,
      timneControl: {
        value: '00:00',
        valueHour: '00',
        valueMinute: '00',
        valueTo: '00:00',
        valueHourTo: '00',
        valueMinuteTo: '00',
        mo00024: {
          isOpen: false,
        },
        mo00038HourTens: {
          mo00045: {
            value: '0',
          },
        },
        mo00038HourOnes: {
          mo00045: {
            value: '0',
          },
        },
        mo00038MinuteTens: {
          mo00045: {
            value: '0',
          },
        },
        mo00038MinuteOnes: {
          mo00045: {
            value: '0',
          },
        },
      } as Mo01376Type,
      titleKnj: {
        value: '',
      } as Mo00046Type,
      /** ケース種別 */
      caseCd: {
        modelValue: '',
      } as Mo00040Type,
      /** サービス提供種別CD */
      svTeikyoCd: {
        modelValue: '',
      } as Mo00040Type,
      koumokuKnj: {
        value: '',
      } as Mo00046Type,
      memoKnj: {
        value: '',
      } as Mo00046Type,
      /** 記録者（文字列） */
      shokuKnj: currentUser?.shokuinKnj ?? '',
      /** 担当者 */
      tantoName: currentUser?.shokuinKnj ?? '',
      /** 記録者 */
      kirokuName: currentUser?.shokuinKnj ?? '',
      /** 訪問チェックフラグ */
      houmonFlg: {
        modelValue: false,
      } as Mo00018Type,
      /** 計画書（1）チェックフラグ */
      kkak1Flg: {
        modelValue: false,
      } as Mo00018Type,
      /** 計画書（2）チェックフラグ */
      kkak2Flg: {
        modelValue: false,
      } as Mo00018Type,
      /** 週間計画チェックフラグ */
      weekFlg: {
        modelValue: false,
      } as Mo00018Type,
      /** 利用票チェックフラグ */
      riyoFlg: {
        modelValue: false,
      } as Mo00018Type,
      /** 計画対象年月 */
      taishoYm: {
        value: '',
        mo01343: {
          value: '',
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        } as Mo01343Type,
      } as Mo00020Type,
      visible: true,
      updateKbn: 'C',
    })
  }

  const datas = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)
  selectedRowIndex.value = (datas?.length ?? 1) - 1

  await nextTick()
  const tableWrapper = document.querySelector('.table-wrapper .v-table__wrapper')
  if (tableWrapper) {
    tableWrapper.scrollTop = tableWrapper.scrollHeight
  }
}

/**
 * AC016_行挿入
 *
 * @param index - 挿入するインデックス
 */
const onInsertRow = (index: number) => {
  const keys = Object.keys(
    isCarePlan5.value
      ? (refValue.value?.kaigo_shiyen_list?.[0] ?? {})
      : (refValue.value?.shogu_shiyen_list?.[0] ?? {})
  )

  const emptyItem = keys.reduce(
    (obj, key) => {
      obj[key] = ''
      return obj
    },
    {} as Record<string, string>
  )

  if (isShitenKbn.value) {
    // TODO: GUI02295_利用者選択画面を開く。
    return
  } else {
    const currentUser = systemCommonsStore.getCurrentUser
    const newItem = {
      extraFields: emptyItem,
      uniqueId: uuidv4(),
      staffid: '',
      recNo: '',
      svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
      timeStmp: systemCommonsStore.getSystemDate ?? '',
      userid: systemCommonsStore.getUserId ?? '',
      houjinId: systemCommonsStore.getHoujinId ?? '',
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      nameKanaSei: '',
      nameUser: '',
      yymmYmd: {
        value: systemCommonsStore.getSystemDate,
        mo01343: {
          value: systemCommonsStore.getSystemDate,
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        } as Mo01343Type,
      } as Mo00020Type,
      fromTimeHhmm: {
        value: '00:00',
      } as Mo01272Type,
      toTimeHhmm: {
        value: '00:00',
      } as Mo01272Type,
      timneControl: {
        value: '00:00',
        valueHour: '00',
        valueMinute: '00',
        valueTo: '00:00',
        valueHourTo: '00',
        valueMinuteTo: '00',
        mo00024: {
          isOpen: false,
        },
        mo00038HourTens: {
          mo00045: {
            value: '0',
          },
        },
        mo00038HourOnes: {
          mo00045: {
            value: '0',
          },
        },
        mo00038MinuteTens: {
          mo00045: {
            value: '0',
          },
        },
        mo00038MinuteOnes: {
          mo00045: {
            value: '0',
          },
        },
      } as Mo01376Type,
      titleKnj: {
        value: '',
      } as Mo00046Type,
      caseCd: {
        modelValue: '',
      } as Mo00040Type,
      svTeikyoCd: {
        modelValue: '',
      } as Mo00040Type,
      koumokuKnj: {
        value: '',
      } as Mo00046Type,
      memoKnj: {
        value: '',
      } as Mo00046Type,
      shokuKnj: currentUser?.shokuinKnj ?? '',
      tantoName: currentUser?.shokuinKnj ?? '',
      kirokuName: currentUser?.shokuinKnj ?? '',
      houmonFlg: {
        modelValue: false,
      } as Mo00018Type,
      kkak1Flg: {
        modelValue: false,
      } as Mo00018Type,
      kkak2Flg: {
        modelValue: false,
      } as Mo00018Type,
      weekFlg: {
        modelValue: false,
      } as Mo00018Type,
      riyoFlg: {
        modelValue: false,
      } as Mo00018Type,
      taishoYm: {
        value: '',
        mo01343: {
          value: '',
          mo00024: {
            isOpen: false,
          } as Mo00024Type,
        } as Mo01343Type,
      } as Mo00020Type,
      visible: true,
      updateKbn: 'C',
    }

    refValue.value?.shiyen_list_mapping.splice(index, 0, newItem)
  }

  selectedRowIndex.value = index
}

/**
 * AC017_行複製
 */
const onCoppyItems = () => {
  const datas = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)
  if (
    selectedRowIndex.value !== Or50148Const.DEFAULT.NULL &&
    selectedRowIndex.value >= Or50148Const.DEFAULT.DEFALUT_LENGHT_0 &&
    datas &&
    datas.length > selectedRowIndex.value
  ) {
    const visibleIndexes = refValue
      .value!.shiyen_list_mapping.map((item, idx) =>
        item.visible ? idx : Or50148Const.DEFAULT.DEFAULT_INDEX
      )
      .filter((idx) => idx !== Or50148Const.DEFAULT.DEFAULT_INDEX)
    const idx = visibleIndexes[selectedRowIndex.value]
    if (idx === undefined) return

    const item = cloneDeep(refValue.value!.shiyen_list_mapping[idx])
    item.visible = true
    item.uniqueId = uuidv4()
    item.updateKbn = Or50148Const.DEFAULT.UPDATE_KBN.CREATE

    refValue.value!.shiyen_list_mapping.splice(idx + 1, 0, item)

    const newDatas = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)
    selectedRowIndex.value = newDatas.findIndex((v, i) => i === selectedRowIndex.value + 1)
  }
}

/**
 * AC018_行削除
 */
const onDeleteItems = async () => {
  const dialogResult = await openInfoDialog()
  const datas = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)
  switch (dialogResult) {
    case 'yes':
      if (
        selectedRowIndex.value !== Or50148Const.DEFAULT.NULL &&
        selectedRowIndex.value >= Or50148Const.DEFAULT.DEFALUT_LENGHT_0 &&
        datas.length > Or50148Const.DEFAULT.DEFALUT_LENGHT_0
      ) {
        const visibleIndexes = refValue
          .value!.shiyen_list_mapping.map((item, idx) =>
            item.visible ? idx : Or50148Const.DEFAULT.DEFAULT_INDEX
          )
          .filter((idx) => idx !== Or50148Const.DEFAULT.DEFAULT_INDEX)
        const idx = visibleIndexes[selectedRowIndex.value]
        if (idx === Or50148Const.DEFAULT.DEFAULT_UNDEFINED) return

        const item = refValue.value!.shiyen_list_mapping[idx]
        if (!item) return

        if (item.updateKbn === Or50148Const.DEFAULT.UPDATE_KBN.UPDATE) {
          item.visible = false
          item.updateKbn = Or50148Const.DEFAULT.UPDATE_KBN.DELETE
        } else if (item.updateKbn === Or50148Const.DEFAULT.UPDATE_KBN.CREATE) {
          refValue.value!.shiyen_list_mapping.splice(idx, 1)
        }

        const newDatas = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)
        if (newDatas.length === Or50148Const.DEFAULT.DEFALUT_LENGHT_0) {
          selectedRowIndex.value = Or50148Const.DEFAULT.DEFALUT_LENGHT_0
        } else if (selectedRowIndex.value >= newDatas.length) {
          selectedRowIndex.value = newDatas.length - 1
        }
      }
      break
    case 'no':
      Or21814Logic.state.set({
        uniqueCpId: or21814.value.uniqueCpId,
        state: {
          isOpen: false,
        },
      })
      break
  }
}

/**
 * 時刻ダイアログを開く処理
 *
 * @param item - 時刻コントロールオブジェクト
 *
 * @param index - 行インデックス
 */
const onClickDialogTime = (item: Mo01376Type, index: number) => {
  selectRow(index)
  selectItemTime.value = index
  item.mo00024.isOpen = true
}

/**
 * 時刻ダイアログの表示状態を判定する関数
 *
 * @param item - 時刻コントロールオブジェクト
 *
 * @returns true: ダイアログ表示, false: 非表示
 */
const showDialog = (item: Mo01376Type) => {
  return item.mo00024.isOpen ?? false
}

/**
 * AC013_上へ移動・下へ移動
 *
 * @description
 * 選択中の行を上または下に移動する
 *
 * @param key - 'up'の場合は上へ移動, それ以外は下へ移動
 */
// const onSelectUpDown = (key: string) => {
//   if (key === Or50148Const.DEFAULT.KEY_NEXT.UP) {
//     if (selectedRowIndex.value > 0) {
//       selectedRowIndex.value--
//     }
//   } else {
//     const datas = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)
//     if (datas && selectedRowIndex.value < datas?.length - 1) {
//       selectedRowIndex.value++
//     }
//   }
// }

/**
 * 行選択時の処理
 *
 * @param index - 選択された行のインデックス
 */
const selectRow = (index: number) => {
  const datas = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)
  if (datas && datas?.length === Or50148Const.DEFAULT.DEFAULT_LENGHT_1) {
    selectedRowIndex.value = 0
  } else {
    selectedRowIndex.value = index
  }
}
/**
 * 指定された行が選択されているかどうかを判定する関数
 *
 * @param index - 判定対象の行のインデックス
 *
 * @returns true: 選択されている, false: 選択されていない
 */
const isSelected = (index: number): boolean => {
  return selectedRowIndex.value === index
}

/**
 *
 * テーブルヘッダークリック時のソート処理
 *
 * @param sortable - ソート可能かどうか
 *
 * @param key - ソート可能かどうか
 */
const headerSort = async (sortable: boolean, key: string) => {
  if (!sortable) return

  // 同じカラムをクリックしたら昇順⇔降順をトグル
  if (sortKey.value === key) {
    sortDesc.value = !sortDesc.value
  } else {
    sortKey.value = key
    sortDesc.value = false
  }

  selectedRowIndex.value = 0
  await nextTick()
  const tableWrapper = document.querySelector('.table-wrapper .v-table__wrapper')
  if (tableWrapper) {
    tableWrapper.scrollTop = 0
  }
}

/**
 * AC014_「項目」入力補助アイコン押下
 *
 * @param index - 選択された行のインデックス
 */
const onSetProject = (index: number) => {
  const koumokuKnj = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)[index]
    .koumokuKnj?.value
  selectedRowIndex.value = index
  isModeTitle.value = 'project'
  localOneway.or51775Oneway.title = '項目'
  localOneway.or51775Oneway.t2Cd = '3'
  localOneway.or51775Oneway.columnName = 'koumoku_knj'
  localOneway.or51775Oneway.assessmentMethod = ''
  localOneway.or51775Oneway.inputContents = koumokuKnj!
  localOneway.or51775Oneway.userId = '1'
  localOneway.or51775Oneway.mode = ''

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * AC015_「内容」入力補助アイコン押下
 *
 * @param index - 選択された行のインデックス
 */
const onSetContent = (index: number) => {
  isModeTitle.value = 'content'
  localOneway.or51775Oneway.title = '内容'
  localOneway.or51775Oneway.screenId = ''
  localOneway.or51775Oneway.bunruiId = ''
  localOneway.or51775Oneway.t2Cd = '1'
  localOneway.or51775Oneway.t3Cd = '0'
  localOneway.or51775Oneway.assessmentMethod = ''
  localOneway.or51775Oneway.userId = '1'
  localOneway.or51775Oneway.mode = ''
  selectedRowIndex.value = index
  const memoKnj = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)[index].memoKnj
    .value

  if (isCarePlan5.value) {
    localOneway.or51775Oneway.t1Cd = '1010'
    localOneway.or51775Oneway.tableName = 'sho_tuc_casebiko'
    localOneway.or51775Oneway.columnName = 'case_knj'
    localOneway.or51775Oneway.inputContents = memoKnj!
  } else {
    localOneway.or51775Oneway.t1Cd = '2700'
    localOneway.or51775Oneway.tableName = 'cpn_tuc_syp_keika'
    localOneway.or51775Oneway.columnName = 'memo_knj'
    localOneway.or51775Oneway.inputContents = memoKnj!
  }

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * AC019_「タイトル」 入力支援 アイコン押下
 *
 * @param index - 選択された行のインデックス
 */
const onSelectTitle = (index: number) => {
  isModeTitle.value = 'title'
  selectRow(index)
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const handleConfirm = (data: Or51775ConfirmType) => {
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: false }, // ダイアログを開くフラグ
  })
  const datas = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)
  switch (isModeTitle.value) {
    case 'project':
      if (datas && selectedRowIndex.value >= 0 && selectedRowIndex.value < datas?.length) {
        datas[selectedRowIndex.value].koumokuKnj.value = data.value
      }
      break
    case 'content':
      if (datas && selectedRowIndex.value >= 0 && selectedRowIndex.value < datas?.length) {
        datas[selectedRowIndex.value].memoKnj.value = data.value
      }
      break
    case 'title':
      if (datas && selectedRowIndex.value >= 0 && selectedRowIndex.value < datas?.length) {
        datas[selectedRowIndex.value].titleKnj.value = data.value
      }
      break
    default:
      break
  }
}

/**
 * 訪問チェックボックスの変更時
 *
 * @param check - チェックボックスの状態
 *
 * @param index - 行インデックス
 */
const onChangeHoumonFlg = (check: Mo00018Type, index: number) => {
  selectRow(index)
  const datas = refValue.value!.shiyen_list_mapping.filter((item) => item.visible)
  if (check.modelValue) {
    if (datas && selectedRowIndex.value >= 0 && selectedRowIndex.value < datas?.length) {
      const time = datas[selectedRowIndex.value]?.yymmYmd
      if (time) {
        const date = new Date(time.value)
        const item = datas[selectedRowIndex.value]?.taishoYm
        if (!item.value) {
          item.value = `${date.getFullYear()}/${String(date.getMonth() + 2).padStart(2, '0')}`
        }
      }

      datas[selectedRowIndex.value].houmonFlg.modelValue = true
    }
  }
}

/**
 * AC020_「記録者」入力支援 アイコン押下
 *
 * @param isNum - 0: 担当者, 1: 記録者
 *
 * @param index - 選択された行のインデックス
 */
const onSelectTaro = (isNum: string, index: number) => {
  selectRow(index)
  isButton.value = isNum

  // Or26257のダイアログ開閉状態を更新する
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC003_「保存」ボタン押下
 */
const onSave = async () => {
  const inputParam: cpnSypKeikaUpdateInEntity = {
    /** ケアプラン方式フラグ */
    cpnFlg: local.cpn_flg,
    /**
     * 介護支援リスト
     */
    kaigoShiyenList: [],
    /**
     * 新型養老リスト
     */
    singataYoroList: [],
  }

  const listNewTypeOfEndowment = refValue.value?.shiyen_list_mapping.map((el) => {
    const item = {
      /** レコード番号 */
      recNo: el.recNo,
      /** 利用者ID */
      userid: el.userid,
      /** 法人ID */
      houjinId: el?.houjinId ?? '',
      /** 施設ID */
      shisetuId: el?.shisetuId ?? '',
      /** 事業者ID */
      svJigyoId: el?.svJigyoId ?? systemCommonsStore.getSvJigyoId,
      /** 記録日 */
      kirokuYmd: el?.yymmYmd.value ?? '',
      /** 開始時刻 */
      startTime: el?.timneControl?.value ?? '',
      /** 終了時刻 */
      endTime: el?.timneControl?.valueTo ?? '',
      /** 所用時間 */
      jikan: el?.titleKnj?.value ?? '',
      /** サービス提供種別 */
      svTeikyoCd: el?.svTeikyoCd.modelValue ?? '',
      /** 内容 */
      memoKnj: el?.memoKnj?.value ?? '',
      /** 担当者 */
      tantoId: '',
      /** 記録者 */
      kirokuid: '',
      /** 表示順 */
      seq: el?.extraFields?.seq ?? '',
      /** 契約者ID */
      keiyakushaId: '',
      /** 更新区分 */
      updateKbn: el.updateKbn,
    }

    return item
  }) as New_type_of_endowment_list[]

  const listKaigoShiyen = refValue.value?.shiyen_list_mapping.map((el) => {
    const item = {
      /** 備考区分 */
      bikoKbn: '47',
      /** 利用者ID */
      userid: el.userid,
      /** 法人ID */
      houjinId: el?.houjinId ?? '',
      /** 施設ID */
      shisetuId: el?.shisetuId ?? '',
      /** 記録日 */
      yymmYmd: el?.yymmYmd.value ?? '',
      /** レコード番号 */
      recNo: el?.recNo ?? '',
      /** 開始時間 */
      timeHh: el?.timneControl?.valueHour ?? '',
      /** 開始分 */
      timeMm: el?.timneControl?.valueMinute ?? '',
      /** 項目名称 */
      koumokuKnj: el.koumokuKnj?.value ?? '',
      /** ケース種別 */
      caseCd: el?.caseCd.modelValue ?? '',
      /** ケース事項 */
      caseKnj: el?.memoKnj?.value ?? '',
      /** 記入者 */
      staffid: el?.extraFields?.staffid ?? '',
      /** ｹｰｽ転記フラグ */
      caseFlg: el?.extraFields?.caseFlg ?? '',
      /** 申し送りフラグ */
      moushiokuriFlg: el?.extraFields?.moushiokuriFlg ?? '',
      /** 結果元履歴番号 */
      baseRecNo: el?.extraFields?.baseRecNo ?? '',
      /** システム別フラグ */
      systemFlg: '10',
      /** 指示フラグ */
      shijiFlg: el?.extraFields?.shijiFlg ?? '',
      /** 共有区分 */
      kyouyuBikoKbn: el?.extraFields?.kyouyuBikoKbn ?? '',
      /** 褥瘡フラグ */
      jyoFlg: el?.extraFields?.jyoFlg ?? '',
      /** ユニークID */
      uniqueId: el?.uniqueId ?? uuidv4(),
      /** 結果元ユニークID */
      baseUniqueId: el?.extraFields?.baseUniqueId ?? '',
      /** 更新日時 */
      timeStmp: el?.timeStmp ?? '',
      /** 事業者ID */
      svJigyoId: el?.svJigyoId ?? systemCommonsStore.getSvJigyoId,
      /** 表示順 */
      seqNo: el?.extraFields?.seqNo ?? '',
      /** 終了時間 */
      endHh: el?.timneControl?.valueHourTo ?? '',
      /** 終了分 */
      endMm: el?.timneControl?.valueMinuteTo ?? '',
      /** 所要時間 */
      totaltime: el?.extraFields?.totaltime ?? '',
      /** タイトル */
      titleKnj: el.titleKnj.value,
      /** 訪問チェックフラグ */
      houmonFlg: el.houmonFlg.modelValue ? '1' : '0',
      /** 計画書（１）チェックフラグ */
      kkak1Flg: el.kkak1Flg.modelValue ? '1' : '0',
      /** 計画書（２）チェックフラグ */
      kkak2Flg: el.kkak2Flg.modelValue ? '1' : '0',
      /** 週間計画チェックフラグ */
      weekFlg: el.weekFlg.modelValue ? '1' : '0',
      /** 利用票チェックフラグ */
      riyoFlg: el.riyoFlg.modelValue ? '1' : '0',
      /** 計画対象年月 */
      taishoYm: el.taishoYm.value ?? '',
      /** 担当者 */
      tantoId: el?.extraFields?.tantoId ?? '',
      /** CPSシステム内部ID */
      cpsKeika2Id: el?.extraFields?.cpsKeika2Id ?? '',
      /** 履歴変更フラグ */
      modifyFlg: el?.extraFields?.modifyFlg ?? '',
      /** 記録者（文字列） */
      shokuKnj: el?.shokuKnj ?? '',
      /** 地域支援事業所ＩＤ */
      chiJigyoId: el?.extraFields?.chiJigyoId ?? '',
      /** 種類CD */
      shuruiCd: el?.extraFields?.shuruiCd ?? '',
      /** 計画書(2)ID */
      ks21Id: '',
      /** 計画書(2)詳細ID */
      ks22Id: '',
      /** 実施モニタリング詳細ID */
      kjisshi2Id: '',
      /** 実施状況確認詳細ID */
      r4sKjisshi2Id: '',
      /** プロブレムID */
      problemId: '',
      /** 契約者ID */
      keiyakushaId: '',
      /** 更新区分 */
      updateKbn: el.updateKbn,
    }
    return item
  }) as Kaigo_shiyen_list[]

  inputParam.singataYoroList = listNewTypeOfEndowment
  inputParam.kaigoShiyenList = listKaigoShiyen

  // APIで月間・年間表詳細情報を取得する。
  const res = await ScreenRepository.update('cpnSypKeikaUpdate', inputParam)
  if (res.statusCode === '200' || res.statusCode === 'success') {
    screenStore.setCpTwoWay({
      cpId: Or50148Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: {
        kaigo_shiyen_list: [...(refValue.value?.kaigo_shiyen_list ?? [])],
        shubetsu_list: refValue.value?.shubetsu_list,
        shogu_shiyen_list: [...(refValue.value?.shogu_shiyen_list ?? [])],
        service_list: refValue.value?.service_list,
        shiyen_list_mapping: [...(refValue.value?.shiyen_list_mapping ?? [])],
        tekiyoJigyoList: refValue.value?.tekiyoJigyoList,
      },
      isInit: true,
    })
    return true
  }
  return false
}

/**
 * 「開始時刻」変更時の処理
 *
 * @param formTime - Mo01272Type型の開始時刻
 *
 * @param item - 対象のTableDataList行データ
 */
// const onChangeFromTime = (formTime: Mo01272Type, item: TableDataList) => {
//   if (item) {
//     item.fromTimeHhmm.value = formTime.value
//     item.timneControl.value = formTime.value

//     const { hour, minute } = getHourAndMinute(formTime.value)
//     item.timneControl.valueHour = hour
//     item.timneControl.valueMinute = minute
//   }
// }

/**
 * 「終了時刻」変更時の処理
 *
 * @param toTime - Mo01272Type型の終了時刻
 *
 * @param item - 対象のTableDataList行データ
 */
// const onChangeToTime = (toTime: Mo01272Type, item: TableDataList) => {
//   if (item) {
//     item.toTimeHhmm.value = toTime.value
//     item.timneControl.valueTo = toTime.value

//     const { hour, minute } = getHourAndMinute(toTime.value)
//     item.timneControl.valueHourTo = hour
//     item.timneControl.valueMinuteTo = minute
//   }
// }

/**
 * 時刻文字列（例: "12:34"）から「時」と「分」を抽出する関数
 *
 * @param value - "hh:mm"形式の時刻文字列
 *
 * @returns hour, minute - 時と分のオブジェクト
 */
// const getHourAndMinute = (value: string): { hour: string; minute: string } => {
//   if (!value) return { hour: '00', minute: '00' }
//   const [hour = '', minute = ''] = value.split(':')
//   return { hour, minute }
// }

/**
 * バリデーションチェックを行う関数
 *
 * @returns true: バリデーション成功, false: バリデーション失敗
 */
const isValidate = () => {
  return valid.value
}

// 非同期処理前に関数を公開
defineExpose({ isValidate })
</script>
<template>
  <c-v-sheet class="container">
    <c-v-row no-gutters>
      <c-v-col cols="auto">
        <c-v-row
          no-gutters
          class="align-center mb-4"
        >
          <c-v-col
            cols="6"
            class="btn-container"
          >
            <!-- 行追加ボタン -->
            <base-mo00611
              v-if="!isShitenKbn"
              :oneway-model-value="localOneway.mo00611OnewayAdd"
              class="ml-0 pl-0"
              @click="onAddItems"
            >
            </base-mo00611>

            <base-mo00611
              v-if="!isShitenKbn"
              :oneway-model-value="localOneway.mo00611OnewayInsertRow"
              class="ml-0 pl-0"
              @click="onInsertRow(selectedRowIndex)"
            >
            </base-mo00611>

            <base-mo00611
              :oneway-model-value="{
                ...localOneway.mo00611OnewayCopy,
                disabled: displayDatatable?.length <= 0,
              }"
              @click="onCoppyItems"
            >
            </base-mo00611>
            <base-mo01265
              v-bind="{
                ...localOneway.mo01265OneWay,
                disabled: displayDatatable?.length <= 0,
              }"
              @click="onDeleteItems"
            >
              <c-v-tooltip
                v-if="localOneway.mo01265OneWay.tooltipText"
                :text="localOneway.mo01265OneWay.tooltipText"
                location="bottom"
                activator="parent"
              />
            </base-mo01265>
          </c-v-col>
          <c-v-col
            cols="6"
            class="d-flex"
            style="justify-content: end"
          >
            <div class="row-layout">
              <!-- <div class="left-date mr-6">
                 {{ currentIndexDisplay }}
                <base-mo00611
                  :oneway-model-value="localOneway.mo00611OnewayDisplayOder"
                  class="ml-0 pl-0"
                >
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="localOneway.btnAddItemTooltip.displayOrderBtn"
                  />
                </base-mo00611>
              </div> -->
              <div class="right-column">
                <!-- <base-mo00009
                  :oneway-model-value="localOneway.mo00009OnewayUp"
                  @click="onSelectUpDown('up')"
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009OnewayDown"
                  @click="onSelectUpDown('down')"
                /> -->
                {{ `${displayDatatable?.length}件` }}
              </div>
            </div>
          </c-v-col>
        </c-v-row>
        <!-- table -->
        <c-v-row no-gutters>
          <c-v-col cols="auto">
            <c-v-data-table
              v-resizable-grid="{ columnWidths: columnMinWidth }"
              :headers="headersItemsByMode"
              class="table-wrapper"
              hide-default-footer
              item-selectable="selectable"
              :items="displayDatatable"
              fixed-header
              :items-per-page="-1"
              hover
              hide-no-data
            >
              <template #headers="{ columns }">
                <tr>
                  <th
                    v-for="column in columns[0]?.items"
                    :key="column.key"
                    class="table-header-cell sort-icon-wrapper"
                    :style="{
                      'min-width': column.width,
                      'max-width': column.width,
                      'text-align': column.align,
                      height: '42px',
                      padding: '0px 16px',
                    }"
                    :class="column.key"
                    style="cursor: pointer"
                    @click.stop="headerSort(column.sortable, column.key)"
                  >
                    <div
                      v-if="column.sortable"
                      style="cursor: pointer; align-items: center"
                      :style="{ 'justify-content': column.align }"
                      class="d-flex"
                      @click.stop="headerSort(column.sortable, column.key)"
                    >
                      <div
                        class="d-flex align-center"
                        @click.stop="headerSort(column.sortable, column.key)"
                      >
                        <div
                          v-if="column.required"
                          class="mr-1 required-asterisk"
                        >
                          *
                        </div>
                        <!-- <div
                          v-if="column.isButton"
                          class="d-flex"
                          style="align-items: center; justify-content: space-between"
                          :style="{ width: column.width }"
                        >
                          <template
                            v-if="
                              column?.nameButton &&
                              column.nameButton === Or50148Const.DEFAULT.KEY_CONTENT.CONTENT
                            "
                          >
                            <base-mo00009
                              :oneway-model-value="localOneway.mo00009OnewayEdit"
                              @click="onSetContent()"
                            />
                          </template>
                          <template
                            v-else-if="
                              column?.nameButton &&
                              column.nameButton === Or50148Const.DEFAULT.KEY_CONTENT.PROJECT
                            "
                          >
                            <base-mo00009
                              :oneway-model-value="localOneway.mo00009OnewayEdit"
                              @click="onSetProject()"
                            />
                          </template>

                          <base-mo00615
                            :oneway-model-value="{
                              ...localOneway.mo00615Oneway,
                              itemLabel: column.title,
                            }"
                            style="cursor: pointer"
                            class="custom-label"
                            @click.stop="headerSort(column.sortable, column.key)"
                          />

                          <base-mo00615
                            v-if="column.countContent"
                            :oneway-model-value="{
                              ...localOneway.mo00615Oneway,
                              itemLabel: `【${displayDatatable?.length}件】`,
                            }"
                            style="cursor: pointer;"
                            class="custom-label"
                            @click.stop="headerSort(column.sortable, column.key)"
                          />
                        </div> -->
                        <!-- カスタムヘッダー：列タイトル -->
                        <div>
                          <base-mo00615
                            :oneway-model-value="{
                              ...localOneway.mo00615Oneway,
                              itemLabel: column.title,
                              customClass: {
                                outerClass: 'mr-1',
                              },
                            }"
                            style="cursor: pointer"
                            class="custom-label"
                            @click.stop="headerSort(column.sortable, column.key)"
                          />
                        </div>
                        <div :class="{ 'is-sorted': sortKey === column.key }">
                          <base-at-icon
                            v-if="sortKey === column.key && !sortDesc"
                            color="#333333"
                            icon="arrow_upward"
                            size="18"
                          />
                          <base-at-icon
                            v-else-if="sortKey === column.key && sortDesc"
                            color="#333333"
                            icon="arrow_downward"
                            size="18"
                          />
                          <base-at-icon
                            v-else
                            color="#333333"
                            :icon="sortDesc ? 'arrow_downward' : 'arrow_upward'"
                            size="18"
                            class="hover-only"
                          />
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <div class="align-center">
                        <div
                          v-if="column.required"
                          class="mr-1 required-asterisk"
                        >
                          *
                        </div>
                        <!-- カスタムヘッダー：列タイトル -->
                        <base-mo00615
                          :oneway-model-value="{
                            ...localOneway.mo00615Oneway,
                            itemLabel: column.title,
                          }"
                          class="font-weight-bold"
                        />
                      </div>
                    </div>
                  </th>
                </tr>
              </template>
              <!-- テーブルの各行 -->
              <template #item="{ item, index }">
                <!-- 個別アイテム -->
                <tr
                  :class="{ 'row-selected': isSelected(index) }"
                  @click="selectRow(index)"
                >
                  <td
                    v-if="
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_6 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_7 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_8 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_9 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_10
                    "
                    class="custom-padding"
                    style="vertical-align: revert"
                  >
                    <base-mo00615
                      :oneway-model-value="{
                        ...localOneway.mo00615Oneway,
                        itemLabel: item.nameUser,
                      }"
                      class="mt-2"
                      style="cursor: pointer; font-weight: bold; background-color: transparent"
                    />
                  </td>

                  <td
                    class="custom-padding"
                    style="vertical-align: revert"
                  >
                    <base-mo00020
                      v-model="item.yymmYmd"
                      :oneway-model-value="localOneway.mo00020Oneway"
                      style="background-color: transparent"
                    />
                  </td>

                  <td
                    class="custom-padding"
                    style="vertical-align: revert"
                  >
                    <div style="display: flex; align-items: center; background-color: transparent">
                      <!-- <base-mo01272
                        v-model="item.fromTimeHhmm"
                        :oneway-model-value="localOneway.mo01272Oneway"
                        @update:model-value="onChangeFromTime($event, item)"
                      ></base-mo01272> -->
                      <div>
                        {{ item.fromTimeHhmm.value }}
                        <base-mo00615
                          class="ml-2"
                          :oneway-model-value="{
                            ...localOneway.mo00615Oneway,
                            itemLabel: '～',
                            customClass: {
                              outerStyle: 'margin-left: 0px !important',
                            },
                          }"
                          style="background-color: transparent"
                        />
                        {{ item.toTimeHhmm.value }}
                      </div>
                      <base-mo00009
                        :oneway-model-value="localOneway.mo00009OnewaySchedule"
                        class="floatTime"
                        @click="onClickDialogTime(item.timneControl, index)"
                      ></base-mo00009>
                      <!-- <div>
                        <base-mo00615
                          class="ml-2"
                          :oneway-model-value="{
                            ...localOneway.mo00615Oneway,
                            itemLabel: '～',
                          }"
                          style="background-color: transparent"
                        />
                        <base-mo00009
                          :oneway-model-value="localOneway.mo00009OnewaySchedule"
                          class="floatTime ml-1"
                          @click="onClickDialogTime(item.timneControl, index)"
                        ></base-mo00009>
                      </div> -->
                      <!-- <base-mo01272
                        v-model="item.toTimeHhmm"
                        :oneway-model-value="localOneway.mo01272Oneway"
                        @update:model-value="onChangeToTime($event, item)"
                      ></base-mo01272> -->

                      <base-mo01376
                        v-if="showDialog(item.timneControl)"
                        v-model="item.timneControl"
                        :oneway-model-value="localOneway.mo01376Oneway"
                      />
                    </div>
                  </td>

                  <td
                    class="custom-padding"
                    style="vertical-align: revert; padding: 1px !important"
                  >
                    <c-v-form
                      v-model="valid"
                      class="custom-height"
                    >
                      <g-custom-or-x-0185
                        v-model="item.titleKnj"
                        :oneway-model-value="localOneway.orX0185Oneway"
                        style="width: 155px; height: 100% !important"
                        class="custom-content"
                      >
                        <template #menu>
                          <div
                            class="orx0185-footer-menu-item"
                            @click="onSelectTitle(index)"
                          >
                            {{ t('label.orX0107-input') }}
                          </div>
                          <div
                            v-if="local.importFlag"
                            class="orx0185-footer-menu-item"
                            @click="onImport()"
                          >
                            {{ t('label.support-elapsed-record-import') }}
                          </div>
                        </template>
                      </g-custom-or-x-0185>
                    </c-v-form>
                    <!-- <div style="display: flex; align-items: center">
                      <div class="mr-2">
                        <base-mo00009
                          :oneway-model-value="localOneway.mo00009OnewayEdit"
                          @click="onSelectTitle(index)"
                        />
                      </div>
                      <base-mo00046
                        v-model="item.titleKnj"
                        :oneway-model-value="localOneway.mo00046oneway"
                        width="111px"
                      />
                    </div> -->
                  </td>

                  <td
                    class="custom-padding select-box-table"
                    style="vertical-align: revert"
                  >
                    <base-mo01282
                      v-if="isCarePlan5"
                      v-model="item.caseCd"
                      :oneway-model-value="{
                        ...localOneway.mo00040Oneway,
                        itemTitle: 'shuruiKnj',
                        itemValue: 'shuruiCd',
                        items: refValue?.shubetsu_list,
                      }"
                      style="margin-right: 0px !important"
                    />
                    <base-mo01282
                      v-else
                      v-model="item.svTeikyoCd"
                      :oneway-model-value="{
                        ...localOneway.mo00040Oneway,
                        itemTitle: 'naiyoKnj',
                        itemValue: 'svTeikyoCd',
                        items: refValue?.service_list,
                      }"
                      style="margin-right: 0px !important"
                    />
                  </td>

                  <td
                    v-if="
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_2 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_4 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_7 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_9
                    "
                    class="custom-padding select-box-table"
                    style="vertical-align: revert; padding: 1px !important"
                  >
                    <c-v-form
                      v-model="valid"
                      class="custom-height"
                    >
                      <g-custom-or-x-0185
                        v-model="item.koumokuKnj"
                        :oneway-model-value="localOneway.orX0185Oneway"
                        style="width: 155px; height: 100% !important"
                        class="custom-content"
                      >
                        <template #menu>
                          <div
                            class="orx0185-footer-menu-item"
                            @click="onSetProject(index)"
                          >
                            {{ t('label.orX0107-input') }}
                          </div>
                          <div
                            v-if="local.importFlag"
                            class="orx0185-footer-menu-item"
                            @click="onImport()"
                          >
                            {{ t('label.support-elapsed-record-import') }}
                          </div>
                        </template>
                      </g-custom-or-x-0185>
                    </c-v-form>

                    <!-- <base-mo01282
                      v-model="item.koumokuKnj"
                      :oneway-model-value="{
                        ...localOneway.mo00040Oneway,
                        itemTitle: 'label',
                        itemValue: 'value',
                        items: listKoumokuKnj,
                      }"
                      style="margin-right: 0px !important"
                    /> -->
                    <!-- <div style="display: flex; align-items: center">
                      <div class="mr-2">
                        <base-mo00009
                          :oneway-model-value="localOneway.mo00009OnewayEdit"
                          @click="onSetProject(index)"
                        />
                      </div>
                      <base-mo00046
                        v-model="item.koumokuKnj"
                        :oneway-model-value="{
                          ...localOneway.mo00046onewayText,
                          rows: 3,
                        }"
                        width="160px"
                        variant="outlined"
                        density="compact"
                      ></base-mo00046>
                    </div> -->
                  </td>

                  <td
                    class="custom-padding"
                    style="vertical-align: revert; padding: 1px !important"
                  >
                    <c-v-form
                      v-model="valid"
                      class="custom-height"
                    >
                      <g-custom-or-x-0185
                        v-model="item.memoKnj"
                        :oneway-model-value="localOneway.orX0185Oneway"
                        style="width: 367px; height: 100% !important"
                        class="custom-content"
                      >
                        <template #menu>
                          <div
                            class="orx0185-footer-menu-item"
                            @click="onSetContent(index)"
                          >
                            {{ t('label.orX0107-input') }}
                          </div>
                          <div
                            v-if="local.importFlag"
                            class="orx0185-footer-menu-item"
                            @click="onImport()"
                          >
                            {{ t('label.support-elapsed-record-import') }}
                          </div>
                          <div
                            v-if="local.importingMeetingMinutesFlag"
                            class="orx0185-footer-menu-item"
                            @click="onImportingMeetingMinutes()"
                          >
                            {{ t('label.support-elapsed-record-importing-meeting-minutes') }}
                          </div>
                          <div
                            v-if="local.importInquiryDetailsFlag"
                            class="orx0185-footer-menu-item"
                            @click="onImportInquiryDetails()"
                          >
                            {{ t('label.support-elapsed-record-import-inquiry-details') }}
                          </div>
                        </template>
                      </g-custom-or-x-0185>
                    </c-v-form>

                    <!-- <div style="display: flex; align-items: center">
                      <div class="mr-2">
                        <base-mo00009
                          :oneway-model-value="localOneway.mo00009OnewayEdit"
                          @click="onSetContent(index)"
                        />
                      </div>
                      <base-mo00046
                        v-model="item.memoKnj"
                        :oneway-model-value="localOneway.mo00046onewayText"
                        width="260px"
                        variant="outlined"
                        density="compact"
                      ></base-mo00046>
                    </div> -->
                  </td>

                  <td
                    v-if="
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_5 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_10
                    "
                    class="custom-padding"
                    style="vertical-align: revert"
                  >
                    <!-- <base-mo01282
                      v-model="item.tantoName"
                      :oneway-model-value="{
                        ...localOneway.mo00040Oneway,
                        itemTitle: 'label',
                        itemValue: 'value',
                        items: listTantoName,
                      }"
                      style="margin-right: 0px !important"
                    /> -->
                    <c-v-row
                      no-gutters
                      class="d-flex mb-1"
                      style="align-items: center"
                    >
                      <c-v-col cols="7">
                        <div style="display: flex; align-items: center">
                          <div class="mr-1">
                            <base-mo00009
                              class="mb-1"
                              :oneway-model-value="localOneway.mo00009OnewayEdit"
                              @click="onSelectTaro('0', index)"
                            />
                          </div>
                          <base-mo00615
                            :oneway-model-value="{
                              ...localOneway.mo00615Oneway,
                              itemLabel: item.tantoName ?? '太郎丸',
                            }"
                            style="background-color: transparent"
                          />
                        </div>
                      </c-v-col>
                    </c-v-row>
                  </td>

                  <td
                    v-if="
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_5 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_10
                    "
                    class="custom-padding"
                    style="vertical-align: revert"
                  >
                    <!-- <base-mo01282
                      v-model="item.kirokuName"
                      :oneway-model-value="{
                        ...localOneway.mo00040Oneway,
                        itemTitle: 'label',
                        itemValue: 'value',
                        items: listKirokuName,
                      }"
                      style="margin-right: 0px !important"
                    /> -->
                    <c-v-row
                      no-gutters
                      class="d-flex mb-1"
                      style="align-items: center"
                    >
                      <c-v-col cols="7">
                        <div style="display: flex; align-items: center">
                          <div class="mr-1">
                            <base-mo00009
                              class="mb-1"
                              :oneway-model-value="localOneway.mo00009OnewayEdit"
                              @click="onSelectTaro('1', index)"
                            />
                          </div>
                          <base-mo00615
                            :oneway-model-value="{
                              ...localOneway.mo00615Oneway,
                              itemLabel: item.kirokuName ?? '太郎丸',
                            }"
                            style="background-color: transparent"
                          />
                        </div>
                      </c-v-col>
                    </c-v-row>
                  </td>

                  <td
                    v-if="
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_1 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_2 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_3 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_4 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_6 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_7 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_8 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_9
                    "
                    class="custom-padding"
                    style="vertical-align: revert"
                  >
                    <c-v-row
                      no-gutters
                      class="d-flex mb-1"
                      style="align-items: center"
                    >
                      <!-- <base-mo01282
                        v-model="item.shokuKnj"
                        :oneway-model-value="{
                          ...localOneway.mo00040Oneway,
                          itemTitle: 'label',
                          itemValue: 'value',
                          items: listShokuKnj,
                        }"
                        style="margin-right: 0px !important"
                      /> -->
                      <c-v-col cols="7">
                        <div style="display: flex; align-items: center">
                          <div class="mr-1">
                            <base-mo00009
                              class="mb-1"
                              :oneway-model-value="localOneway.mo00009OnewayEdit"
                              @click="onSelectTaro('', index)"
                            />
                          </div>
                          <base-mo00615
                            :oneway-model-value="{
                              ...localOneway.mo00615Oneway,
                              itemLabel: item.shokuKnj ?? '太郎',
                            }"
                            style="background-color: transparent"
                          />
                        </div>
                      </c-v-col>
                    </c-v-row>
                  </td>

                  <td
                    v-if="
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_2 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_4 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_8 ||
                      mode === Or50148Const.DEFAULT.MODE_VALUE.MODE_9
                    "
                    class="custom-padding"
                    style="vertical-align: revert"
                  >
                    <c-v-row no-gutters>
                      <c-v-col cols="12">
                        <c-v-row
                          no-gutters
                          class="d-flex mb-1"
                          style="align-items: center"
                        >
                          <c-v-col cols="12">
                            <base-mo00018
                              v-model="item.houmonFlg"
                              :oneway-model-value="{
                                showItemLabel: false,
                                hideDetails: true,
                                checkboxLabel: t('label.support-elapsed-record-home-visit'),
                                isVerticalLabel: false,
                              }"
                            >
                            </base-mo00018>
                          </c-v-col>
                          <c-v-col cols="12">
                            <base-mo00018
                              v-model="item.kkak1Flg"
                              :oneway-model-value="{
                                showItemLabel: false,
                                hideDetails: true,
                                checkboxLabel: t('label.support-elapsed-record-care-plan1'),
                                isVerticalLabel: false,
                              }"
                              @change="onChangeHoumonFlg(item.kkak1Flg, index)"
                            >
                            </base-mo00018>
                          </c-v-col>
                          <c-v-col cols="12">
                            <base-mo00018
                              v-model="item.kkak2Flg"
                              :oneway-model-value="{
                                showItemLabel: false,
                                hideDetails: true,
                                checkboxLabel: t('label.support-elapsed-record-care-plan2'),
                                isVerticalLabel: false,
                              }"
                              @change="onChangeHoumonFlg(item.kkak2Flg, index)"
                            >
                            </base-mo00018>
                          </c-v-col>
                          <c-v-col cols="12">
                            <base-mo00018
                              v-model="item.weekFlg"
                              :oneway-model-value="{
                                showItemLabel: false,
                                hideDetails: true,
                                checkboxLabel: t('label.support-elapsed-record-week'),
                                isVerticalLabel: false,
                              }"
                              @change="onChangeHoumonFlg(item.weekFlg, index)"
                            >
                            </base-mo00018>
                          </c-v-col>
                          <c-v-col cols="12">
                            <base-mo00018
                              v-model="item.riyoFlg"
                              :oneway-model-value="{
                                showItemLabel: false,
                                hideDetails: true,
                                checkboxLabel: t('label.support-elapsed-record-utilization-ticket'),
                                isVerticalLabel: false,
                              }"
                              @change="onChangeHoumonFlg(item.riyoFlg, index)"
                            >
                            </base-mo00018>
                          </c-v-col>
                          <c-v-col cols="12">
                            <c-v-row
                              no-gutters
                              class="d-flex"
                              style="align-items: center"
                            >
                              <c-v-col cols="12">
                                <div style="display: flex; align-items: center; margin-left: 12px">
                                  <div class="mr-1">
                                    <base-mo01352
                                      v-model="item.taishoYm"
                                      :oneway-model-value="localOneway.mo01352Oneway"
                                      style="background-color: transparent"
                                    />
                                  </div>
                                  <base-mo00615
                                    :oneway-model-value="{
                                      ...localOneway.mo00615Oneway,
                                      itemLabel: t('label.support-elapsed-record-monthly-minute'),
                                    }"
                                    class="ml-1"
                                    style="background-color: transparent"
                                  />
                                </div>
                              </c-v-col>
                            </c-v-row>
                          </c-v-col>
                        </c-v-row>
                      </c-v-col>
                    </c-v-row>
                  </td>
                </tr>
              </template>
            </c-v-data-table>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <g-base-or21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    />
    <!-- GUI00937_入力支援画面をポップアップで起動する。 -->
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="handleConfirm"
    />
    <!-- GUI00220_職員検索画面をポップアップで起動する。 -->
    <g-custom-or-26257
      v-if="showDialogOr26257"
      v-bind="or26257"
      v-model="or26257Type"
      :oneway-model-value="localOneway.or26257Oneway"
    />
    <!-- GUI01016_ケース一覧選択画面を開く。 -->
    <g-custom-or-28256
      v-if="showDialogOr28256"
      v-bind="or28256"
      :oneway-model-value="localOneway.or28256Oneway"
    />
    <!-- GUI02259_適用事業所の選択画面を開く。 -->
    <g-custom-or22951
      v-if="showDialogOr22951"
      v-bind="or22951"
      :oneway-model-value="localOneway.or22951Oneway"
      @update:model-value="onChangeData"
    />
    <!-- GUI01259_支援経過確認一覧 -->
    <g-custom-or-11017
      v-if="showDialogOr11017"
      v-bind="or11017"
    />
    <!-- GUI01260_会議録取込 -->
    <g-custom-or-52335
      v-if="showDialogOr52335"
      v-bind="or52335"
      v-model="or52335ModelValue"
      :oneway-model-value="localOneway.or52335OnewayType"
    >
    </g-custom-or-52335>
    <!-- GUI01261_照会内容取込 -->
    <g-custom-or-27011
      v-if="showDialogOr27011"
      v-model="selectItem"
      v-bind="or27011"
    />
    <!-- GUI01255_支援経過記録マスタ -->
    <g-custom-or-27220
      v-if="showDialogOr27220"
      v-bind="or27220"
      :oneway-model-value="localOneway.or27220Oneway"
    >
    </g-custom-or-27220>
    <!-- GUI01264_印刷設定画面を開く。 -->
    <g-custom-or-27592
      v-if="isShowDialogOr27592"
      v-bind="Or27592"
      :oneway-model-value="localOneway.or27592OnewayType"
    >
    </g-custom-or-27592>
  </c-v-sheet>
</template>
<style lang="scss" scoped>
@use '@/styles/cmn/page-data-table.scss';

.required-asterisk {
  color: #ed6809;
  font-size: 18px;
}

.container {
  padding: 0 8px 8px 8px;
  background-color: transparent;
  // height: calc(100vh - 254px);
  // overflow-y: hidden;
  height: 100%;
  overflow: hidden;
}
.align-center {
  align-items: center !important;
}
.btn-container button {
  margin-right: 10px !important;
}

.row-layout {
  display: flex;
  gap: 4px;
  align-items: center;
}

.left-date {
  min-width: 40px;
}

.right-column {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-grow: 1;
}

// input textarea
.custom-input {
  height: 100%;

  :deep(.v-field__input) {
    cursor: pointer;
    height: 100%;
    padding-top: 16px !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  :deep(.v-field__outline) {
    display: none !important;
  }

  :deep(.v-field--focused) {
    color: unset !important;
    box-shadow: none !important;
  }

  .multiline-text :deep(.v-field__input) {
    white-space: pre-wrap !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
  }
}

.custom-label {
  :deep(.item-label) {
    cursor: pointer;
    white-space: pre-line;
  }

  :deep(.align-self-end) {
    background-color: #dbeefe;
  }
}

// .custom-select-background {
//   background-color: rgb(var(--v-theme-blue-100));
//   :deep(.text-date-area-style) {
//     background-color: rgb(var(--v-theme-blue-100));
//   }

//   :deep(.align-self-end) {
//     background-color: rgb(var(--v-theme-blue-100));
//   }
// }

:deep(.v-table__wrapper) {
  overflow-y: hidden !important;
  overflow-x: auto;
}

.table-wrapper
  :deep(.v-table__wrapper td:has(textarea) div:has(textarea:not([disabled]):not([readonly]))) {
  height: auto !important;
}

.table-wrapper {
  :deep(.v-table__wrapper tbody tr td) {
    padding: 12px !important;
  }
}

.sort-icon-wrapper {
  position: relative;

  .hover-only {
    display: none;
  }

  &:hover .hover-only {
    display: inline-block;
  }

  &.is-sorted .hover-only {
    display: none;
  }
}
:deep(.table-wrapper) {
  .v-table__wrapper {
    th {
      color: #333333;
      font-size: 13px;
      font-weight: 400;
      height: 40px !important;
      padding: 0px 12px !important;
      background-color: #dbeefe !important;
      &.first-head-cell {
        border-top: none !important;
      }
    }
    td {
      &.custom-td-border-top {
        border-top: 1px rgb(var(--v-theme-black-200)) dashed !important;
      }
      &.radio-cell {
        padding: 0 12px !important;
        .v-input__control {
          width: 100% !important;
          .v-selection-control-group {
            display: flex;
            width: 100% !important;
            .v-selection-control {
              flex: 1;
              display: flex;
              justify-content: center;
            }
          }
        }
      }
      &.text-area-cell {
        position: relative;
        .col-textarea {
          padding-left: 30px !important;
        }
        .btn-cell {
          position: absolute;
          left: 0;
          top: 15%;
        }
      }

      font-size: 13px;
      height: 53px !important;
      &.first-col {
        padding: 8px 12px;
        vertical-align: top;
      }
      &:has(input:not([readonly]):not([disabled]):not([type='checkbox']):not([type='radio'])) {
        padding: 12px !important;
      }
    }
  }
  .v-table__wrapper tr:not(.row-selected):not(.select-row) td:not(:has(input, textarea, select)) {
    background-color: #ffffff;
  }
  .v-table__wrapper .row-selected {
    background-color: rgb(var(--v-theme-blue-100)) !important;
  }

  .v-table__wrapper .row-selected td {
    background-color: rgb(var(--v-theme-blue-100)) !important;
  }
}

:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 40px !important;
}
:deep(
  .table-wrapper .v-table__wrapper td:has(textarea) .table-cell:not([disabled]):not([readonly])
) {
  outline: none !important;
}
:deep(.v-table--fixed-height > .v-table__wrapper) {
  overflow-y: hidden;
}
:deep(.select-box-table) {
  .v-field {
    background-color: transparent !important;
  }
}
.custom-height {
  height: 100% !important;
}
</style>
