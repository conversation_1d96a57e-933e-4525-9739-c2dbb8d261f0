/**
 * Or27831: 日課計画入力モーダル
 * GUI01058_日課計画入力
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import data1 from './data/data1.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { InWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * GUI01058_日課計画入力業務計画ライセンス取得APIモック
 *
 * @description
 * UI01058_日課計画入力業務計画ライセンス取得APIのメイン画面に表示されるデータを返却する。
 * dataName："BusinessPlanLicenseSelect"
 */
export function handler(inEntity: InWebEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...data1,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
