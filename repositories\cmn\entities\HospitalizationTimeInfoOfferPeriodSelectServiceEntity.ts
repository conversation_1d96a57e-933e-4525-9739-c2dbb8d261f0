import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * Or01300:有機体:API定義書_APINo(972)_計画期間変更
 * GUI01300_API定義書_APINo(972)_計画期間変更
 *
 * @description
 * API定義書_APINo(972)_計画期間変更 エンティティ
 *
 * <AUTHOR>
 */
/** 計画期間変更 入力エンティティ */
export interface HospitalizationTimeInfoOfferPeriodSelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** システムコード */
  gsysCd: string
  /** 利用者ID */
  userid: string
  /** 施設ID */
  shisetuId: string
  /** 職員ID */
  shokuId: string
  /** 期間ID */
  sc1Id: string
  /** 計画期間ページ区分 */
  pageKbn: string
  /**
   *メニュー２名称
   */
  menu2Name: string
  /**
   *メニュー３名称
   */
  menu3Name: string
}

/**
 * 計画期間変更 出力エンティティ
 */
export interface HospitalizationTimeInfoOfferPeriodSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * 権限期間情報
     */
    kikanKanriFlg: string // 期間管理フラグ
    basicInfoAuthorityFlag?: string // 基本情報権限フラグ
    nursingCareInsuranceAuthorityFlag?: string // 介護保険権限フラグ
    independenceLevelAuthorityFlag?: string // 自立度権限フラグ
    kinshipAuthorityFlag?: string // 親族関係者権限フラグ
    hobbyAndPreferenceAuthorityFlag?: string // 趣味嗜好権限フラグ
    lifeHistoryAuthorityFlag?: string // 生活歴権限フラグ
    carePlan1AuthorityFlag?: string // 計画書1権限フラグ
    attendingPhysicianOpinionAuthorityFlag?: string // 主治医意見書権限フラグ
    certificationSurveyAuthorityFlag?: string // 認定調査権限フラグ
    pastMedicalHistoryAuthorityFlag?: string // 既往歴権限フラグ
    /** 計画期間情報 */
    planPeriodInfo: PlanPeriodInfo
    /** 履歴情報 */
    historyInfo: HistoryInfo1
    /** 様式区分 */
    youshikiKbn: string
  }
}

/**
 *医療機関
 */
export interface HospitalizationTimeInfoOfferMedicalInfoSelectInEntity extends InWebEntity {
  /**
   *情報区分
   */
  jouhouKbn?: string
  /**
   *医師コード
   */
  hospDrCd?: string
}

/**
 *医療機関
 */
export interface HospitalizationTimeInfoOfferMedicalInfoSelectOutEntity extends OutWebEntity {
  /**
   * 医療機関情報リスト
   */
  comMscHospList: MedicalInstitutionInfo[]
  /**
   * 医師情報リスト
   */
  comMscDoctoList: DoctorInfo[]
  /**
   * 入院時情報
   */
  nyuHospInfo: HospitalizationInfo[]
}

/**
 * 医療機関情報リスト
 */
interface MedicalInstitutionInfo {
  /**
   * 医療機関コード - 必填
   */
  hospCd: string
  /**
   * 医療機関名
   */
  hospKnj?: string
  /**
   * TEL
   */
  tel?: string
  /**
   * 診療科情報リスト
   */
  comMscHospKaList?: HospKaInfo[]
}

/**
 * 診療科情報
 */
interface HospKaInfo {
  /**
   * 診療科コード - 必填
   */
  hospKaCd: string
  /**
   * 医療機関名
   */
  hospKnj?: string
  /**
   * TEL
   */
  tel?: string
}

/**
 * 医師情報リスト
 */
interface DoctorInfo {
  /**
   * 医師コード - 必填
   */
  hospDrCd: string
  /**
   * 医療機関名
   */
  hospKnj?: string
  /**
   * TEL
   */
  tel?: string
}

/**
 * 入院時情報
 */
interface HospitalizationInfo {
  /**
   * 医師名
   */
  doctorKnj?: string
  /**
   * フリガナ
   */
  doctorKana?: string
}

/**
 * 権限期間情報
 */
export interface AuthorityFlag {
  /**
   *期間管理フラグ
   */
  kikanKanriFlg: string //
  /**
   *基本情報権限フラグ
   */
  basicInfoAuthorityFlag: string //
  /**
   *介護保険権限フラグ
   */
  nursingCareInsuranceAuthorityFlag: string //
  /**
   *自立度権限フラグ
   */
  independenceLevelAuthorityFlag: string //
  /**
   *親族関係者権限フラグ
   */
  kinshipAuthorityFlag: string //
  /**
   *趣味嗜好権限フラグ
   */
  hobbyAndPreferenceAuthorityFlag: string //
  /**
   *生活歴権限フラグ
   */
  lifeHistoryAuthorityFlag: string //
  /**
   *計画書1権限フラグ
   */
  carePlan1AuthorityFlag: string //
  /**
   *主治医意見書権限フラグ
   */
  attendingPhysicianOpinionAuthorityFlag: string //
  /**
   *認定調査権限フラグ
   */
  certificationSurveyAuthorityFlag: string //
  /**
   *既往歴権限フラグ
   */
  pastMedicalHistoryAuthorityFlag: string //
}

/**
 * 計画期間情報
 */
export interface PlanPeriodInfo {
  /** 期間ID */
  sc1Id: string
  /** 期間総件数 */
  periodCnt?: string
  /** 期間番号 */
  periodNo?: string
  /** 開始日 */
  startYmd?: string
  /** 終了日 */
  endYmd?: string
}

/**
 * 履歴情報_1
 */
export interface HistoryInfo1 {
  /** 提供ID */
  teikyouId: string
  /** 期間ID */
  sc1Id: string
  /** 記入日 */
  createYmd?: string
  /** 職員ID */
  chkShokuId?: string
  /** 履歴総件数 */
  krirekiCnt?: string
  /** 履歴番号 */
  krirekiNo?: string
  /** 担当ケアマネID */
  tantoShokuId?: string
  /** 居宅介護支援事業所ID */
  shienJigyoId?: string
  /** 職員名 */
  chkShokuName?: string
  /** 様式区分 */
  youshikiKbn: string
}

/**
 * 履歴情報_2
 */
export interface HistoryInfo2 {
  /** 提供ID */
  teikyouId?: string
  /** 記入日 */
  createYmd?: string
  /** 職員ID */
  chkShokuId?: string
  /** 医療機関名 */
  hospKnj?: string
  /** 入院日 */
  sickYmd?: string
  /** 担当ケアマネID */
  tantoShokuId?: string
  /** 電話番号 */
  tel?: string
  /** 居宅介護支援事業所ID */
  shienJigyoId?: string
  /** FAX番号 */
  fax?: string
  /** 担当者名 */
  tantoKnj?: string
  /** 情報提供日 */
  teikyouYmd?: string
  /** 更新回数 */
  modifiedCnt?: string
}

/**
 *
 * @description
 * API定義書_APINo(973)_履歴変更 エンティティ
 *
 * <AUTHOR>
 */
/** _履歴変更 入力エンティティ */
export interface HospitalizationTimeInfoOfferHistorySelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userid: string
  /** 提供ID */
  cc1Id: string
  /** 期間ID */
  sc1Id: string
  /** 履歴変更区分 */
  kikanFlag: string
  /**
   *削除処理区分
   */
  deleteKbn: string
  /**
   *当履歴ページ番号
   */
  rrkNo: string
}

/** _履歴変更 出力エンティティ */
export interface HospitalizationTimeInfoOfferHistorySelectOutEntity extends OutWebEntity {
  /**
   *_履歴変更 出力エンティティ
   */
  data: {
    /** 履歴情報 */
    historyInfo: HistoryInfo1
  }
}

/**
 *
 * @description
 * API定義書_APINo(974)_初期情報取得 エンティティ
 *
 * <AUTHOR>
 */

/** _初期情報取得 入力エンティティ */
export interface HospitalizationTimeInfoOfferInitInfoSelectInEntity extends InWebEntity {
  /** 提供ID */
  teikyouId: string
  /** 基準日 */
  appYmd: string
  /** 利用者ID */
  userid: string
}

/** _初期情報取得 出力エンティティ */
export interface HospitalizationTimeInfoOfferInitInfoSelectOutEntity extends OutWebEntity {
  /**
   *_初期情報取得 出力エンティティ
   */
  data: HospitalizationTimeInfoOfferInitInfo
}

/**
 * _初期情報取得
 */
export interface HospitalizationTimeInfoOfferInitInfo {
  /** 確認情報 */
  confirmationInfo: ConfirmationInfo
  /** 続柄マスタ情報 */
  relationshipMasterInfoList: relationshipMasterInfoList[]
  /** 介護保険情報リスト */
  nursingCareInsuranceInfoList: NursingCareInsuranceInfo[]
  /** 担当情報リスト */
  tantoInfoList: TantoInfo[]
  /** 事業所情報リスト */
  jigyoInfoList: JigyoInfo[]
  /**  親族関係者情報リスト */
  kinshipInfoList: KinshipInfo[]
}

/**
 * 確認情報
 */
export interface ConfirmationInfo {
  /** 提供ID */
  teikyouId?: string
  /** 計画期間ID */
  sc1Id?: string
  /** 医療機関名ヘッダー */
  hospKnjHead?: string
  /** 入院日 */
  sickYmd?: string
  /** 担当ケアマネID */
  tantoShokuId?: string
  /** 電話番号ヘッダー */
  telHead?: string
  /** 居宅介護支援事業所ID */
  shienJigyoId?: string
  /** FAX番号 */
  fax?: string
  /** 担当者名 */
  tantoKnj?: string
  /** 情報提供日 */
  teikyouYmd?: string
  /** 住環境 */
  houseKbn?: string
  /** 住環境メモ */
  houseMemoKnj?: string
  /** 住まいに関する特記事項 */
  houseTokkiKnj?: string
  /** 要介護度 */
  yokaiKbn?: string
  /** 認知症高齢者の日常生活自立度 */
  ninchiJiritsuCd?: string
  /** 障害高齢者の日常生活自立度 */
  shogaiJiritsuCd?: string
  /** 障害など認定 */
  shogaiNintei?: string
  /** 年金種類（国民年金） */
  nenkin1Umu?: string
  /** 年金種類（厚生年金） */
  nenkin2Umu?: string
  /** 年金種類（障害年金） */
  nenkin3Umu?: string
  /** 年金種類（生活保護） */
  nenkin4Umu?: string
  /** 年金種類（その他） */
  nenkin5Umu?: string
  /** 年金種類（その他）メモ */
  nenkinMemoKnj?: string
  /** 主介護者（年齢） */
  kaigoMainAge?: string
  /** キーパーソン */
  keyPerson?: string
  /** 本人の性格/趣味関心領域など */
  userInfo?: string
  /** 本人の生活歴 */
  userSeikatureki?: string
  /** 入院前の本人の意向 */
  userIkouKnj?: string
  /** 入院前の家族の意向 */
  kazokuIkouKnj?: string
  /** 在宅生活に必要な要件 */
  zaitakuYokenKnj?: string
  /** 家族の介護力（独居） */
  kazokuKaigo1Umu?: string
  /** 家族の介護力（日中独居） */
  kazokuKaigo2Umu?: string
  /** 家族の介護力（高齢世帯） */
  kazokuKaigo3Umu?: string
  /** 家族の介護力（サポートできる家族や支援者が不在） */
  kazokuKaigo4Umu?: string
  /** 家族の介護力（その他） */
  kazokuKaigo6Umu?: string
  /** 家族の介護力（その他）メモ */
  kazokuKaigoMemoKnj?: string
  /** 特記事項 */
  tokkiKnj?: string
  /** 院内の多職種カンファレンスへの参加 */
  hospConfSanka?: string
  /** 退院前カンファレンスへの参加 */
  leavConfSanka?: string
  /** 退院前カンファレンスへの参加メモ */
  leavConfMemoKnj?: string
  /** キーパーソン（続柄） */
  keyPersonZcode?: string
  /** キーパーソン（年齢） */
  keyPersonAge?: string
  /** 住居階層 */
  houseFloor?: string
  /** 居室階 */
  houseRoomFloor?: string
  /** 認定有効開始日 */
  ninteiStartYmd?: string
  /** 認定終了日 */
  ninteiEndYmd?: string
  /** 認定申請日 */
  ninteiShinseiYmd?: string
  /** 医師の判断 */
  ishiHandanFlg?: string
  /** ケアマネジャーの判断 */
  careHandanFlg?: string
  /** 自己負担割合有無 */
  futanWariFlg?: string
  /** 自己負担割合 */
  futanWariai?: string
  /** 障害認定（身体） */
  shintaiShogaiKbn?: string
  /** 障害認定（知的） */
  chitekiShogaiKbn?: string
  /** 障害認定（精神） */
  seishinShogaiKbn?: string
  /** 世帯構成（独居） */
  setaiKousei1?: string
  /** 世帯構成（高齢者世帯） */
  setaiKousei2?: string
  /** 世帯構成（子と同居） */
  setaiKousei3?: string
  /** 世帯構成（その他） */
  setaiKousei4?: string
  /** 世帯構成（日中独居） */
  setaiKousei5?: string
  /** 世帯構成（その他）メモ */
  setaiKouseiMemoKnj?: string
  /** 主介護者（続柄） */
  kaigoMainZcode?: string
  /** 主介護者（TEL） */
  kaigoMainTel?: string
  /** キーパーソン（TEL） */
  keyPersonTel?: string
  /** キーパーソン（連絡先TEL） */
  keyPersonRenrakuTel?: string
  /** 入院前の本人の意向（計画書（1）参照有無） */
  userIkouCksFlg?: string
  /** 入院前の家族の意向（計画書（1）参照有無） */
  kazokuIkouCksFlg?: string
  /** 入院前の介護サービスの利用状況（居宅サービス計画書123表） */
  serviceRiyoKbn?: string
  /** 入院前の介護サービスの利用状況（その他メモ） */
  serviceRiyoMemoKnj?: string
  /** 家族の介護力（子と同居） */
  kazokuKaigo7Umu?: string
  /** 家族の介護力（子と同居）_家族構成員数 */
  kazokuKaigo7Ninzu?: string
  /** 世帯に対する配慮 */
  setaiHairyo?: string
  /** 世帯に対する配慮（必要メモ） */
  setaiHairyoMemoKnj?: string
  /** 退院後の主介護者 */
  taiingoKaigoMain?: string
  /** 退院後の主介護者（氏名） */
  taiingoKaigoMainName?: string
  /** 退院後の主介護者（続柄） */
  taiingoKaigoMainZcode?: string
  /** 退院後の主介護者（年齢） */
  taiingoKaigoMainAge?: string
  /** 家族の介護力（介護力が見込める） */
  kazokuKaigo8Umu?: string
  /** 介護力見込み */
  kazokuKaigo8Kbn?: string
  /** 虐待の疑い */
  gyakutaiUmu?: string
  /** 虐待の疑い（メモ） */
  gyakutaiUmuMemoKnj?: string
  /** 退院前訪問指導への同行 */
  leavHoumonDoukou?: string
  /** 申請中フラグ */
  ninteiShinseiFlg?: string
  /** 区分変更 */
  ninteiKbnHenkou?: string
  /** 区分変更申請日 */
  kbnHenkouYmd?: string
  /** 未申請フラグ */
  ninteiMishinseiFlg?: string
  /** 確定版主介護者氏名 */
  kaigoMainKakutei?: string
  /** 入院時の要介護度（確定版） */
  nyuuinYokaiKbnKakutei?: string
  /** 主介護者（同居別居） */
  kaigoMainKousei?: string
  /** 家族の介護力（介護力は見込めない） */
  kazokuKaigo9Umu?: string
  /** エレベーター有無 */
  elevatorUmu?: string
  /** 入院前の介護サービスの利用状況（その他） */
  serviceRiyoKbn2?: string
  /** 褥瘡の有無 */
  jyokusouUmu?: string
  /** ADL移動（自立） */
  adlIdou1?: string
  /** ADL移動（見守り） */
  adlIdou2?: string
  /** ADL移動（一部介助） */
  adlIdou3?: string
  /** ADL移乗（自立） */
  adlIjyou1?: string
  /** ADL移乗（見守り） */
  adlIjyou2?: string
  /** ADL移乗（一部介助） */
  adlIjyou3?: string
  /** ADL移乗（全介助） */
  adlIjyou4?: string
  /** ADL移動手段（杖） */
  adlIdouShudan1?: string
  /** ADL移動手段（歩行器） */
  adlIdouShudan2?: string
  /** ADL移動手段（車いす） */
  adlIdouShudan3?: string
  /** ADL移動手段（その他） */
  adlIdouShudan4?: string
  /** ADL更衣（自立） */
  adlKoui1?: string
  /** ADL更衣（見守り） */
  adlKoui2?: string
  /** ADL更衣（一部介助） */
  adlKoui3?: string
  /** ADL更衣（全介助） */
  adlKoui4?: string
  /** ADL起居動作（自立） */
  adlKikyo1?: string
  /** ADL起居動作（見守り） */
  adlKikyo2?: string
  /** ADL起居動作（一部介助） */
  adlKikyo3?: string
  /** ADL起居動作（全介助） */
  adlKikyo4?: string
  /** ADL整容（自立） */
  adlSeiyou1?: string
  /** ADL整容（見守り） */
  adlSeiyou2?: string
  /** ADL整容（一部介助） */
  adlSeiyou3?: string
  /** ADL整容（全介助） */
  adlSeiyou4?: string
  /** ADL入浴（自立） */
  adlNyuuyoku1?: string
  /** ADL入浴（見守り） */
  adlNyuuyoku2?: string
  /** ADL入浴（一部介助） */
  adlNyuuyoku3?: string
  /** ADL入浴（全介助） */
  adlNyuuyoku4?: string
  /** ADL食事（自立） */
  adlShokuji1?: string
  /** ADL食事（見守り） */
  adlShokuji2?: string
  /** ADL食事（一部介助） */
  adlShokuji3?: string
  /** ADL食事（全介助） */
  adlShokuji4?: string
  /** 食事回数（朝） */
  shokujiCntMorn?: string
  /** 食事回数（昼） */
  shokujiCntNoon?: string
  /** 食事回数（夜） */
  shokujiCntEven?: string
  /** 食事制限 */
  shokujiSeigen?: string
  /** 食事制限メモ */
  shokujiSeigenMemoKnj?: string
  /** 食事形態（普通） */
  shokujiKeitai1?: string
  /** 食事形態（きざみ） */
  shokujiKeitai2?: string
  /** 食事形態（嚥下障害食） */
  shokujiKeitai3?: string
  /** 食事形態（ミキサー） */
  shokujiKeitai4?: string
  /** 水分制限 */
  suibunSeigen?: string
  /** 水分制限メモ */
  suibunSeigenMemoKnj?: string
  /** 摂取方法（経口） */
  sesshuHouhou1?: string
  /** 摂取方法（経管栄養） */
  sesshuHouhou2?: string
  /** 水分とろみ */
  suibunToromi?: string
  /** UDF等の食形態区分 */
  udfShokujiKeitaiKbn?: string
  /** 嚥下機能 */
  engeUmu?: string
  /** 義歯 */
  gishiUmu?: string
  /** 口腔清潔 */
  koukuuCare?: string
  /** 口臭 */
  koushuuUmu?: string
  /** 排尿（自立） */
  hainyou1?: string
  /** 排尿（見守り） */
  hainyou2?: string
  /** 排尿（一部介助） */
  hainyou3?: string
  /** 排尿（全介助） */
  hainyou4?: string
  /** ポータブルトイレ */
  portableToiletUmu?: string
  /** 排便（自立） */
  haiben1?: string
  /** 排便（見守り） */
  haiben2?: string
  /** 排便（一部介助） */
  haiben3?: string
  /** 排便（全介助） */
  haiben4?: string
  /** オムツ/パッド */
  omutsuPadUmu?: string
  /** 睡眠の状態 */
  sleepJyoutai?: string
  /** 睡眠の状態メモ */
  sleepMemoKnj?: string
  /** 視力 */
  eyesightKbn?: string
  /** メガネ */
  glassesUmu?: string
  /** メガネメモ */
  glassesMemoKnj?: string
  /** 聴力 */
  hearingKbn?: string
  /** 補聴器 */
  hearingAidUmu?: string
  /** 言語 */
  languageAbility?: string
  /** 意思疎通 */
  comnKbn?: string
  /** コミュニケーションに関する特記事項 */
  comnTokkiKnj?: string
  /** 精神面における療養上の問題（なし） */
  ryouyou1Umu?: string
  /** 精神面における療養上の問題（幻視幻聴） */
  ryouyou2Umu?: string
  /** 精神面における療養上の問題（興奮） */
  ryouyou3Umu?: string
  /** 精神面における療養上の問題（焦燥不穏） */
  ryouyou4Umu?: string
  /** 精神面における療養上の問題（妄想） */
  ryouyou5Umu?: string
  /** 精神面における療養上の問題（暴力/攻撃性） */
  ryouyou6Umu?: string
  /** 精神面における療養上の問題（介護への抵抗） */
  ryouyou7Umu?: string
  /** 精神面における療養上の問題（不眠） */
  ryouyou8Umu?: string
  /** 精神面における療養上の問題（昼夜逆転） */
  ryouyou9Umu?: string
  /** 精神面における療養上の問題（徘徊） */
  ryouyou10Umu?: string
  /** 精神面における療養上の問題（危険行為） */
  ryouyou11Umu?: string
  /** 精神面における療養上の問題（不潔行為） */
  ryouyou12Umu?: string
  /** 精神面における療養上の問題（その他） */
  ryouyou13Umu?: string
  /** 精神面における療養上の問題（その他メモ） */
  ryouyouMemoKnj?: string
  /** 疾患歴（なし） */
  sick1Umu?: string
  /** 疾患歴（悪性腫瘍） */
  sick2Umu?: string
  /** 疾患歴（認知症） */
  sick3Umu?: string
  /** 疾患歴（急性呼吸器感染症） */
  sick4Umu?: string
  /** 疾患歴（脳血管障害） */
  sick5Umu?: string
  /** 疾患歴（骨折） */
  sick6Umu?: string
  /** 疾患歴（その他） */
  sick7Umu?: string
  /** 疾患歴（その他）メモ */
  sickMemoKnj?: string
  /** 最近半年間での入院 */
  nyuuinUmu?: string
  /** 最近半年間での入院開始日 */
  nyuuinStartYmd?: string
  /** 最近半年間での入院終了日 */
  nyuuinEndYmd?: string
  /** 入院頻度 */
  nyuuinHindo?: string
  /** 医療処置（なし） */
  shochi1Umu?: string
  /** 医療処置（点滴） */
  shochi2Umu?: string
  /** 医療処置（酸素療法） */
  shochi3Umu?: string
  /** 医療処置（喀痰吸引） */
  shochi4Umu?: string
  /** 医療処置（気管切開） */
  shochi5Umu?: string
  /** 医療処置（胃ろう） */
  shochi6Umu?: string
  /** 医療処置（経鼻栄養） */
  shochi7Umu?: string
  /** 医療処置（経腸栄養） */
  shochi8Umu?: string
  /** 医療処置（褥瘡） */
  shochi9Umu?: string
  /** 医療処置（尿道カテーテル） */
  shochi10Umu?: string
  /** 医療処置（尿路ストーマ） */
  shochi11Umu?: string
  /** 医療処置（消化管ストーマ） */
  shochi12Umu?: string
  /** 医療処置（痛みコントロール） */
  shochi13Umu?: string
  /** 医療処置（排便コントロール） */
  shochi14Umu?: string
  /** 医療処置（自己注射） */
  shochi15Umu?: string
  /** 医療処置（自己注射）メモ */
  shochi15MemoKnj?: string
  /** 医療処置（その他） */
  shochi16Umu?: string
  /** 医療処置（その他）メモ */
  shochi16MemoKnj?: string
  /** 内服薬 */
  drugUmu?: string
  /** 内服薬メモ */
  drugMemoKnj?: string
  /** 居宅療養管理指導 */
  ryouyouKanriUmu?: string
  /** 居宅療養管理指導メモ */
  ryouyouKanriMemoKnj?: string
  /** 薬剤管理 */
  drugKanriKbn?: string
  /** 薬剤管理（管理者） */
  drugKanriKanrisya?: string
  /** 薬剤管理（管理方法） */
  drugKanriHouhou?: string
  /** 服薬状況 */
  drugJyoukyou?: string
  /** お薬に関する特記事項 */
  drugTokkiKnj?: string
  /** 医療機関名 */
  hospKnj?: string
  /** 電話番号 */
  hospTel?: string
  /** 医師名フリガナ */
  doctorKana?: string
  /** 医師名 */
  doctorKnj?: string
  /** 診察方法 */
  hospHouhou?: string
  /** 診察頻度（回数） */
  hospKaisu?: string
  /** 喫煙量 */
  smoking?: string
  /** 飲酒量 */
  alcohol?: string
  /** ADL移動（室内）（杖） */
  adlIdouShitsunai1?: string
  /** ADL移動（室内）（歩行器） */
  adlIdouShitsunai2?: string
  /** ADL移動（室内）（車いす） */
  adlIdouShitsunai3?: string
  /** ADL移動（室内）（その他） */
  adlIdouShitsunai4?: string
  /** 褥瘡メモ */
  jyokusouMemoKnj?: string
  /** 義歯区分 */
  gishiKbn?: string
  /** 食事回数 */
  shokujiCnt?: string
  /** 喫煙有無 */
  smokingUmu?: string
  /** 飲酒有無 */
  alcoholUmu?: string
  /** 入院理由 */
  nyuuinRiyu?: string
  /** 眠剤の使用 */
  sleepDrugUmu?: string
  /** ADL移動（全介助） */
  adlIdou4?: string
  /** 麻痺の状況 */
  mahiKbn?: string
  /** 更新回数 */
  modifiedCnt?: string
  [key: string]: string | undefined | null
}

/**
 * 暫定情報
 */
export interface PreliminaryInfo {
  /** 提供ID */
  teikyouId?: string
  /** 計画期間ID */
  sc1Id?: string
  /** 医療機関名ヘッダー */
  hospKnjHead?: string
  /** 入院日 */
  sickYmd?: string
  /** 担当ケアマネID */
  tantoShokuId?: string
  /** 電話番号ヘッダー */
  telHead?: string
  /** 居宅介護支援事業所ID */
  shienJigyoId?: string
  /** FAX番号 */
  fax?: string
  /** 住環境 */
  houseKbn?: string
  /** 住環境メモ */
  houseMemoKnj?: string
  /** 住まいに関する特記事項 */
  houseTokkiKnj?: string
  /** エレベーター有無 */
  elevatorUmu?: string
  /** エレベーター有無メモ */
  elevatorMemoKnj?: string
  /** 入院時の要介護度 */
  nyuuinYokaiKbn?: string
  /** 要介護度 */
  yokaiKbn?: string
  /** 認知症高齢者の日常生活自立度 */
  ninchiJiritsuCd?: string
  /** 障害高齢者の日常生活自立度 */
  shogaiJiritsuCd?: string
  /** 介護保険の自己負担割合 */
  futanWari?: string
  /** 障害など認定 */
  shogaiNintei?: string
  /** 障害など認定メモ */
  shogaiNinteiKnj?: string
  /** 年金種類（国民年金） */
  nenkin1Umu?: string
  /** 年金種類（厚生年金） */
  nenkin2Umu?: string
  /** 年金種類（障害年金） */
  nenkin3Umu?: string
  /** 年金種類（生活保護） */
  nenkin4Umu?: string
  /** 年金種類（その他） */
  nenkin5Umu?: string
  /** 年金種類（その他）メモ */
  nenkinMemoKnj?: string
  /** 家族構成 */
  kazokuKousei?: string
  /** 家族構成メモ */
  kazokuMemoKnj?: string
  /** 主介護者 */
  kaigoMain?: string
  /** 主介護者（年齢） */
  kaigoMainAge?: string
  /** キーパーソン */
  keyPerson?: string
  /** キーパーソン（連絡先） */
  keyPersonRenraku?: string
  /** 本人の性格/趣味関心領域など */
  userInfo?: string
  /** 本人の生活歴 */
  userSeikatureki?: string
  /** 入院前の本人の意向 */
  userIkouKnj?: string
  /** 入院前の家族の意向 */
  kazokuIkouKnj?: string
  /** 在宅生活に必要な要件 */
  zaitakuYokenKnj?: string
  /** 家族の介護力（独居） */
  kazokuKaigo1Umu?: string
  /** 家族の介護力（日中独居） */
  kazokuKaigo2Umu?: string
  /** 家族の介護力（高齢世帯） */
  kazokuKaigo3Umu?: string
  /** 家族の介護力（サポートできる家族や支援者が不在） */
  kazokuKaigo4Umu?: string
  /** 家族の介護力（家族が要介護状態/認知症である） */
  kazokuKaigo5Umu?: string
  /** 家族の介護力（その他） */
  kazokuKaigo6Umu?: string
  /** 家族の介護力（その他）メモ */
  kazokuKaigoMemoKnj?: string
  /** 特記事項 */
  tokkiKnj?: string
  /** 院内の多職種カンファレンスへの参加 */
  hospConfSanka?: string
  /** 退院前カンファレンスへの参加 */
  leavConfSanka?: string
  /** 退院前カンファレンスへの参加メモ */
  leavConfMemoKnj?: string
  /** キーパーソン（続柄） */
  keyPersonZcode?: string
  /** キーパーソン（年齢） */
  keyPersonAge?: string
  /** 麻痺の状況 */
  mahiKbn?: string
  /** 褥瘡の有無 */
  jyokusouUmu?: string
  /** ADL移動（自立） */
  adlIdou1?: string
  /** ADL移動（見守り） */
  adlIdou2?: string
  /** ADL移動（一部介助） */
  adlIdou3?: string
  /** ADL移動（全介助） */
  adlIdou4?: string
  /** ADL移動メモ */
  adlIdouMemoKnj?: string
  /** ADL移乗（自立） */
  adlIjyou1?: string
  /** ADL移乗（見守り） */
  adlIjyou2?: string
  /** ADL移乗（一部介助） */
  adlIjyou3?: string
  /** ADL移乗（全介助） */
  adlIjyou4?: string
  /** ADL移動手段（杖） */
  adlIdouShudan1?: string
  /** ADL移動手段（歩行器） */
  adlIdouShudan2?: string
  /** ADL移動手段（車いす） */
  adlIdouShudan3?: string
  /** ADL移動手段（その他） */
  adlIdouShudan4?: string
  /** ADL更衣（自立） */
  adlKoui1?: string
  /** ADL更衣（見守り） */
  adlKoui2?: string
  /** ADL更衣（一部介助） */
  adlKoui3?: string
  /** ADL更衣（全介助） */
  adlKoui4?: string
  /** ADL起居動作（自立） */
  adlKikyo1?: string
  /** ADL起居動作（見守り） */
  adlKikyo2?: string
  /** ADL起居動作（一部介助） */
  adlKikyo3?: string
  /** ADL起居動作（全介助） */
  adlKikyo4?: string
  /** ADL整容（自立） */
  adlSeiyou1?: string
  /** ADL整容（見守り） */
  adlSeiyou2?: string
  /** ADL整容（一部介助） */
  adlSeiyou3?: string
  /** ADL整容（全介助） */
  adlSeiyou4?: string
  /** ADL整容メモ */
  adlSeiyouMemoKnj?: string
  /** ADL入浴（自立） */
  adlNyuuyoku1?: string
  /** ADL入浴（見守り） */
  adlNyuuyoku2?: string
  /** ADL入浴（一部介助） */
  adlNyuuyoku3?: string
  /** ADL入浴（全介助） */
  adlNyuuyoku4?: string
  /** ADL入浴メモ */
  adlNyuuyokuMemoKnj?: string
  /** ADL食事（自立） */
  adlShokuji1?: string
  /** ADL食事（見守り） */
  adlShokuji2?: string
  /** ADL食事（一部介助） */
  adlShokuji3?: string
  /** ADL食事（全介助） */
  adlShokuji4?: string
  /** ADL食事メモ */
  adlShokujiMemoKnj?: string
  /** 食事回数（朝） */
  shokujiCntMorn?: string
  /** 食事回数（昼） */
  shokujiCntNoon?: string
  /** 食事回数（夜） */
  shokujiCntEven?: string
  /** 食事制限 */
  shokujiSeigen?: string
  /** 食事制限メモ */
  shokujiSeigenMemoKnj?: string
  /** 食事形態（普通） */
  shokujiKeitai1?: string
  /** 食事形態（きざみ） */
  shokujiKeitai2?: string
  /** 食事形態（嚥下障害食） */
  shokujiKeitai3?: string
  /** 食事形態（ミキサー） */
  shokujiKeitai4?: string
  /** 水分制限 */
  suibunSeigen?: string
  /** 水分制限メモ */
  suibunSeigenMemoKnj?: string
  /** 摂取方法（経口） */
  sesshuHouhou1?: string
  /** 摂取方法（経管栄養） */
  sesshuHouhou2?: string
  /** 水分とろみ */
  suibunToromi?: string
  /** UDF等の食形態区分 */
  udfShokujiKeitaiKbn?: string
  /** 嚥下機能 */
  engeUmu?: string
  /** 義歯 */
  gishiUmu?: string
  /** 口腔清潔 */
  koukuuCare?: string
  /** 口臭 */
  koushuuUmu?: string
  /** 排尿（自立） */
  hainyou1?: string
  /** 排尿（見守り） */
  hainyou2?: string
  /** 排尿（一部介助） */
  hainyou3?: string
  /** 排尿（全介助） */
  hainyou4?: string
  /** ポータブルトイレ */
  portableToiletUmu?: string
  /** 排便（自立） */
  haiben1?: string
  /** 排便（見守り） */
  haiben2?: string
  /** 排便（一部介助） */
  haiben3?: string
  /** 排便（全介助） */
  haiben4?: string
  /** オムツ/パッド */
  omutsuPadUmu?: string
  /** 睡眠の状態 */
  sleepJyoutai?: string
  /** 睡眠の状態メモ */
  sleepMemoKnj?: string
  /** 眠剤の使用 */
  sleepDrugUmu?: string
  /** 視力 */
  eyesightKbn?: string
  /** メガネ */
  glassesUmu?: string
  /** メガネメモ */
  glassesMemoKnj?: string
  /** 聴力 */
  hearingKbn?: string
  /** 補聴器 */
  hearingAidUmu?: string
  /** 言語 */
  languageAbility?: string
  /** 意思疎通 */
  comnKbn?: string
  /** コミュニケーションに関する特記事項 */
  comnTokkiKnj?: string
  /** 精神面における療養上の問題（なし） */
  ryouyou1Umu?: string
  /** 精神面における療養上の問題（幻視幻聴） */
  ryouyou2Umu?: string
  /** 精神面における療養上の問題（興奮） */
  ryouyou3Umu?: string
  /** 精神面における療養上の問題（焦燥不穏） */
  ryouyou4Umu?: string
  /** 精神面における療養上の問題（妄想） */
  ryouyou5Umu?: string
  /** 精神面における療養上の問題（暴力/攻撃性） */
  ryouyou6Umu?: string
  /** 精神面における療養上の問題（介護への抵抗） */
  ryouyou7Umu?: string
  /** 精神面における療養上の問題（不眠） */
  ryouyou8Umu?: string
  /** 精神面における療養上の問題（昼夜逆転） */
  ryouyou9Umu?: string
  /** 精神面における療養上の問題（徘徊） */
  ryouyou10Umu?: string
  /** 精神面における療養上の問題（危険行為） */
  ryouyou11Umu?: string
  /** 精神面における療養上の問題（不潔行為） */
  ryouyou12Umu?: string
  /** 精神面における療養上の問題（その他） */
  ryouyou13Umu?: string
  /** 精神面における療養上の問題（その他メモ） */
  ryouyouMemoKnj?: string
  /** 疾患歴（なし） */
  sick1Umu?: string
  /** 疾患歴（悪性腫瘍） */
  sick2Umu?: string
  /** 疾患歴（認知症） */
  sick3Umu?: string
  /** 疾患歴（急性呼吸器感染症） */
  sick4Umu?: string
  /** 疾患歴（脳血管障害） */
  sick5Umu?: string
  /** 疾患歴（骨折） */
  sick6Umu?: string
  /** 疾患歴（その他） */
  sick7Umu?: string
  /** 疾患歴（その他）メモ */
  sickMemoKnj?: string
  /** 最近半年間での入院 */
  nyuuinUmu?: string
  /** 最近半年間での入院開始日 */
  nyuuinStartYmd?: string
  /** 最近半年間での入院終了日 */
  nyuuinEndYmd?: string
  /** 入院頻度 */
  nyuuinHindo?: string
  /** 医療処置（なし） */
  shochi1Umu?: string
  /** 医療処置（点滴） */
  shochi2Umu?: string
  /** 医療処置（酸素療法） */
  shochi3Umu?: string
  /** 医療処置（喀痰吸引） */
  shochi4Umu?: string
  /** 医療処置（気管切開） */
  shochi5Umu?: string
  /** 医療処置（胃ろう） */
  shochi6Umu?: string
  /** 医療処置（経鼻栄養） */
  shochi7Umu?: string
  /** 医療処置（経腸栄養） */
  shochi8Umu?: string
  /** 医療処置（褥瘡） */
  shochi9Umu?: string
  /** 医療処置（尿道カテーテル） */
  shochi10Umu?: string
  /** 医療処置（尿路ストーマ） */
  shochi11Umu?: string
  /** 医療処置（消化管ストーマ） */
  shochi12Umu?: string
  /** 医療処置（痛みコントロール） */
  shochi13Umu?: string
  /** 医療処置（排便コントロール） */
  shochi14Umu?: string
  /** 医療処置（自己注射） */
  shochi15Umu?: string
  /** 医療処置（自己注射）メモ */
  shochi15MemoKnj?: string
  /** 医療処置（その他） */
  shochi16Umu?: string
  /** 医療処置（その他）メモ */
  shochi16MemoKnj?: string
  /** 内服薬 */
  drugUmu?: string
  /** 内服薬メモ */
  drugMemoKnj?: string
  /** 居宅療養管理指導 */
  ryouyouKanriUmu?: string
  /** 居宅療養管理指導メモ */
  ryouyouKanriMemoKnj?: string
  /** 薬剤管理 */
  drugKanriKbn?: string
  /** 薬剤管理（管理者） */
  drugKanriKanrisya?: string
  /** 薬剤管理（管理方法） */
  drugKanriHouhou?: string
  /** 服薬状況 */
  drugJyoukyou?: string
  /** お薬に関する特記事項 */
  drugTokkiKnj?: string
  /** 医療機関名 */
  hospKnj?: string
  /** 電話番号 */
  hospTel?: string
  /** 医師名フリガナ */
  doctorKana?: string
  /** 医師名 */
  doctorKnj?: string
  /** 診察方法 */
  hospHouhou?: string
  /** 診察頻度（回数） */
  hospKaisu?: string
  /** 診察頻度（単位） */
  hospTani?: string
  /** 睡眠時間 */
  sleepTime?: string
  /** 喫煙量 */
  smoking?: string
  /** 飲酒量 */
  alcohol?: string
  /** 更新回数 */
  modifiedCnt?: string
}

/**
 * 続柄マスタ情報
 */
export interface relationshipMasterInfoList {
  /** 続柄コード */
  zcode?: string
  /** 続柄分類名 */
  zokugaraKnj?: string
}

/**
 * 介護保険情報リスト
 */
export interface NursingCareInsuranceInfo {
  /** 介護認定（4－2）認定有効開始日 */
  nstartYmd?: string
  /** 介護認定（4－2）認定終了日 */
  nendYmd?: string
  /** 介護認定（4－2）申請中フラグ */
  nshinseiflg?: string
}
/**
 * 担当情報リスト
 */
export interface TantoInfo {
  /** 担当ケアマネID */
  tantoShokuId?: string
  /** ケアマネジャー氏名 */
  tantoShokuKnj?: string
}
/**
 * 事業所情報リスト
 */
export interface JigyoInfo {
  /** 法人ID */
  houjinId?: string
  /** 施設ID */
  shisetuId?: string
  /** サービス事業者ID */
  svJigyoId?: string
  /** 事業名 */
  jigyoKnj?: string
  /** 電話番号 */
  tel?: string
  /** FAX */
  fax?: string
}
/**
 * 親族関係者情報リスト
 */
export interface KinshipInfo {
  /** 連番 */
  id?: string
  /** 関係区分2 */
  kankei2Kbn?: string
  /** その他TEL1 */
  other1Tel?: string
  /** 歳 */
  age?: string
}

/**
 *
 * @description
 * API定義書_APINo(975)_新規情報取得 エンティティ
 *
 * <AUTHOR>
 */
/** _新規情報取得 入力エンティティ */
export interface HospitalizationTimeInfoOfferNewInfoSelectInEntity extends InWebEntity {
  /** 利用者ID */
  userid: string
  /** 法人ID */
  hojinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 基準日 */
  appYmd: string
  /** 基本情報権限フラグ */
  basicInfoAuthorityFlag: string
  /** 介護保険権限フラグ */
  nursingCareInsuranceAuthorityFlag: string
  /** 自立度権限フラグ */
  independenceLevelAuthorityFlag: string
  /** 親族関係者権限フラグ */
  kinshipAuthorityFlag: string
  /** 趣味嗜好権限フラグ */
  hobbyAndPreferenceAuthorityFlag: string
  /** 生活歴権限フラグ */
  lifeHistoryAuthorityFlag: string
  /** 計画書1権限フラグ */
  carePlan1AuthorityFlag: string
  /** 主治医意見書権限フラグ */
  attendingPhysicianOpinionAuthorityFlag: string
  /** 認定調査権限フラグ */
  certificationSurveyAuthorityFlag: string
  /** 既往歴権限フラグ */
  pastMedicalHistoryAuthorityFlag: string
}

/** _新規情報取得 出力エンティティ */
export interface HospitalizationTimeInfoOfferNewInfoSelectOutEntity extends OutWebEntity {
  /**
   *_新規情報取得 出力エンティティ
   */
  data: {
    /** 新規情報 */
    newInfo: NewInfo
  }
}

/**
 * 新規情報
 */
export interface NewInfo {
  [key: string]: string | undefined | null
  /** 担当ケアマネID */
  tantoShokuId?: string
  /** ケアマネジャー氏名 */
  tantoShokuKnj?: string
  /** 居宅介護支援事業所ID */
  shienJigyoId?: string
  /** 事業所名 */
  jigyoKnj?: string
  /** 電話番号 */
  tel?: string
  /** FAX番号 */
  fax?: string
  /** 障害など認定 */
  shogaiNintei?: string
  /** 介護保険の自己負担割合 */
  futanWari?: string
  /** 入院時の要介護度 */
  nyuuinYokaiKbn?: string
  /** 要支援値 */
  support?: string
  /** 要介護値 */
  nursingCare?: string
  /** 区分変更 */
  ninteiKbnHenkou?: string
  /** 入院時の要介護度（確定版） */
  nyuuinYokaiKbnKakutei?: string
  /** 申請中フラグ */
  ninteiShinseiFlg?: string
  /** 未申請フラグ */
  ninteiMishinseiFlg?: string
  /** 認定有効開始日 */
  ninteiStartYmd?: string
  /** 認定終了日 */
  ninteiEndYmd?: string
  /** 自己負担割合有無 */
  futanWariFlg?: string
  /** 自己負担割合 */
  futanWariai?: string
  /** 認知症高齢者の日常生活自立度 */
  ninchiJiritsuCd?: string
  /** 障害高齢者の日常生活自立度 */
  shogaiJiritsuCd?: string
  /** 家族構成 */
  kazokuKousei?: string
  /** 主介護者 */
  kaigoMain?: string
  /** 主介護者（年齢） */
  kaigoMainAge?: string
  /** 世帯構成（独居） */
  setaiKousei1?: string
  /** 世帯構成（子と同居） */
  setaiKousei3?: string
  /** 確定版主介護者氏名 */
  kaigoMainKakutei?: string
  /** 主介護者（TEL） */
  kaigoMainTel?: string
  /** 主介護者（続柄） */
  kaigoMainZcode?: string
  /** 主介護者（同居別居） */
  kaigoMainKousei?: string
  /** 本人の性格/趣味関心領域など */
  userInfo?: string
  /** 本人の生活歴 */
  userSeikatureki?: string
  /** 入院前の本人の意向 */
  userIkouKnj?: string
  /** 入院前の家族の意向 */
  kazokuIkouKnj?: string
  /** 家族の介護力（独居） */
  kazokuKaigo1Umu?: string
  /** 家族の介護力（子と同居） */
  kazokuKaigo7Umu?: string
  /** 麻痺の状況 */
  mahiKbn?: string
  /** 褥瘡の有無 */
  jyokusouUmu?: string
  /** 食事回数（朝） */
  shokujiCntMorn?: string
  /** 食事回数（昼） */
  shokujiCntNoon?: string
  /** 食事回数（夜） */
  shokujiCntEven?: string
  /** ADL移動（自立） */
  adlIdou1?: string
  /** ADL移動（見守り） */
  adlIdou2?: string
  /** ADL移動（一部介助） */
  adlIdou3?: string
  /** ADL移動（全介助） */
  adlIdou4?: string
  /** ADL移乗（自立） */
  adlIjyou1?: string
  /** ADL移乗（見守り） */
  adlIjyou2?: string
  /** ADL移乗（一部介助） */
  adlIjyou3?: string
  /** ADL移乗（全介助） */
  adlIjyou4?: string
  /** ADL更衣（自立） */
  adlKoui1?: string
  /** ADL更衣（見守り） */
  adlKoui2?: string
  /** ADL更衣（一部介助） */
  adlKoui3?: string
  /** ADL更衣（全介助） */
  adlKoui4?: string
  /** ADL起居動作（自立） */
  adlKikyo1?: string
  /** ADL起居動作（見守り） */
  adlKikyo2?: string
  /** ADL起居動作（一部介助） */
  adlKikyo3?: string
  /** ADL起居動作（全介助） */
  adlKikyo4?: string
  /** ADL整容（自立） */
  adlSeiyou1?: string
  /** ADL整容（一部介助） */
  adlSeiyou3?: string
  /** ADL整容（全介助） */
  adlSeiyou4?: string
  /** ADL入浴（自立） */
  adlNyuuyoku1?: string
  /** ADL入浴（一部介助） */
  adlNyuuyoku3?: string
  /** ADL入浴（全介助） */
  adlNyuuyoku4?: string
  /** ADL食事（自立） */
  adlShokuji1?: string
  /** ADL食事（見守り） */
  adlShokuji2?: string
  /** ADL食事（一部介助） */
  adlShokuji3?: string
  /** ADL食事（全介助） */
  adlShokuji4?: string
  /** 排尿（自立） */
  hainyou1?: string
  /** 排尿（見守り） */
  hainyou2?: string
  /** 排尿（一部介助） */
  hainyou3?: string
  /** 排尿（全介助） */
  hainyou4?: string
  /** 排便（自立） */
  haiben1?: string
  /** 排便（見守り） */
  haiben2?: string
  /** 排便（一部介助） */
  haiben3?: string
  /** 排便（全介助） */
  haiben4?: string
  /** 視力 */
  eyesightKbn?: string
  /** 聴力 */
  hearingKbn?: string
  /** 意思疎通 */
  comnKbn?: string
  /** 精神面における療養上の問題（妄想） */
  ryouyou5Umu?: string
  /** 精神面における療養上の問題（暴力/攻撃性） */
  ryouyou6Umu?: string
  /** 精神面における療養上の問題（介護への抵抗） */
  ryouyou7Umu?: string
  /** 精神面における療養上の問題（昼夜逆転） */
  ryouyou9Umu?: string
  /** 精神面における療養上の問題（徘徊） */
  ryouyou10Umu?: string
  /** 医療処置（点滴） */
  shochi2Umu?: string
  /** 医療処置（酸素療法） */
  shochi3Umu?: string
  /** 医療処置（気管切開） */
  shochi5Umu?: string
  /** 医療処置（褥瘡） */
  shochi9Umu?: string
  /** 医療処置（その他） */
  shochi16Umu?: string
  /** 医療処置（その他）メモ */
  shochi16MemoKnj?: string
  /** 疾患歴（その他）メモ */
  sickMemoKnj?: string
  /** 最近半年間での入院 */
  nyuuinUmu?: string
  /** 最近半年間での入院開始日 */
  nyuuinStartYmd?: string
  /** 最近半年間での入院終了日 */
  nyuuinEndYmd?: string
}

/**
 *
 * @description
 * API定義書_APINo(976)_データ保存 エンティティ
 *
 * <AUTHOR>
 */
/** _新規情報取得 入力エンティティ*/
export interface HospitalizationTimeInfoOfferUpdateInEntity extends InWebEntity {
  /** 期間ID */
  sc1Id: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 種別ID */
  syubetsuId: string
  /** 作成日 */
  createYmd: string
  /** 更新区分 */
  updateKbn: string
  /** 履歴情報 */
  historyInfo: HistoryInfo2
  /** 保存情報 */
  saveInfo: SaveInfo
}

/** _新規情報取得 出力エンティティ*/
export interface HospitalizationTimeInfoOfferUpdateOutEntity extends OutWebEntity {
  /**
   *_新規情報取得 出力エンティティ
   */
  data: {
    /** 提供ID	 */
    cc1Id: string
    /** 期間ID */
    sc1Id: string
  }
}

/** 保存情報 */
export interface SaveInfo {
  /** 更新回数 */
  modifiedCnt?: string
  /** 提供ID */
  teikyouId?: string
  /** 住環境 */
  houseKbn?: string
  /** 住環境メモ */
  houseMemoKnj?: string
  /** 住まいに関する特記事項 */
  houseTokkiKnj?: string
  /** エレベーター有無 */
  elevatorUmu?: string
  /** エレベーター有無メモ */
  elevatorMemoKnj?: string
  /** 入院時の要介護度 */
  nyuuinYokaiKbn?: string
  /** 要介護度 */
  yokaiKbn?: string
  /** 認知症高齢者の日常生活自立度 */
  ninchiJiritsuCd?: string
  /** 障害高齢者の日常生活自立度 */
  shogaiJiritsuCd?: string
  /** 介護保険の自己負担割合 */
  futanWari?: string
  /** 障害など認定 */
  shogaiNintei?: string
  /** 障害など認定メモ */
  shogaiNinteiKnj?: string
  /** 年金種類（国民年金） */
  nenkin1Umu?: string
  /** 年金種類（厚生年金） */
  nenkin2Umu?: string
  /** 年金種類（障害年金） */
  nenkin3Umu?: string
  /** 年金種類（生活保護） */
  nenkin4Umu?: string
  /** 年金種類（その他） */
  nenkin5Umu?: string
  /** 年金種類（その他）メモ */
  nenkinMemoKnj?: string
  /** 家族構成 */
  kazokuKousei?: string
  /** 家族構成メモ */
  kazokuMemoKnj?: string
  /** 主介護者 */
  kaigoMain?: string
  /** 主介護者（年齢） */
  kaigoMainAge?: string
  /** キーパーソン */
  keyPerson?: string
  /** キーパーソン（続柄） */
  keyPersonZcode?: string
  /** キーパーソン（年齢） */
  keyPersonAge?: string
  /** キーパーソン（連絡先） */
  keyPersonRenraku?: string
  /** 本人の性格/趣味・関心領域など */
  userInfo?: string
  /** 本人の生活歴 */
  userSeikatureki?: string
  /** 入院前の本人の意向 */
  userIkouKnj?: string
  /** 入院前の家族の意向 */
  kazokuIkouKnj?: string
  /** 在宅生活に必要な要件 */
  zaitakuYokenKnj?: string
  /** 家族の介護力（独居） */
  kazokuKaigo1Umu?: string
  /** 家族の介護力（日中独居） */
  kazokuKaigo2Umu?: string
  /** 家族の介護力（高齢世帯） */
  kazokuKaigo3Umu?: string
  /** 家族の介護力（サポートできる家族や支援者が不在） */
  kazokuKaigo4Umu?: string
  /** 家族の介護力（家族が要介護状態/認知症である） */
  kazokuKaigo5Umu?: string
  /** 家族の介護力（その他） */
  kazokuKaigo6Umu?: string
  /** 家族の介護力（その他）メモ */
  kazokuKaigoMemoKnj?: string
  /** 特記事項 */
  tokkiKnj?: string
  /** 「院内の多職種カンファレンス」への参加 */
  hospConfSanka?: string
  /** 「退院前カンファレンス」への参加 */
  leavConfSanka?: string
  /** 「退院前カンファレンス」への参加メモ */
  leavConfMemoKnj?: string
  /** 麻痺の状況 */
  mahiKbn?: string
  /** 褥瘡の有無 */
  jyokusouUmu?: string
  /** ADL移動（自立） */
  adlIdou1?: string
  /** ADL移動（見守り） */
  adlIdou2?: string
  /** ADL移動（一部介助） */
  adlIdou3?: string
  /** ADL移動（全介助） */
  adlIdou4?: string
  /** ADL移動メモ */
  adlIdouMemoKnj?: string
  /** ADL移乗（自立） */
  adlIjyou1?: string
  /** ADL移乗（見守り） */
  adlIjyou2?: string
  /** ADL移乗（一部介助） */
  adlIjyou3?: string
  /** ADL移乗（全介助） */
  adlIjyou4?: string
  /** ADL移動手段（杖） */
  adlIdouShudan1?: string
  /** ADL移動手段（歩行器） */
  adlIdouShudan2?: string
  /** ADL移動手段（車いす） */
  adlIdouShudan3?: string
  /** ADL移動手段（その他） */
  adlIdouShudan4?: string
  /** ADL更衣（自立） */
  adlKoui1?: string
  /** ADL更衣（見守り） */
  adlKoui2?: string
  /** ADL更衣（一部介助） */
  adlKoui3?: string
  /** ADL更衣（全介助） */
  adlKoui4?: string
  /** ADL起居動作（自立） */
  adlKikyo1?: string
  /** ADL起居動作（見守り） */
  adlKikyo2?: string
  /** ADL起居動作（一部介助） */
  adlKikyo3?: string
  /** ADL起居動作（全介助） */
  adlKikyo4?: string
  /** ADL整容（自立） */
  adlSeiyou1?: string
  /** ADL整容（見守り） */
  adlSeiyou2?: string
  /** ADL整容（一部介助） */
  adlSeiyou3?: string
  /** ADL整容（全介助） */
  adlSeiyou4?: string
  /** ADL整容メモ */
  adlSeiyouMemoKnj?: string
  /** ADL入浴（自立） */
  adlNyuuyoku1?: string
  /** ADL入浴（見守り） */
  adlNyuuyoku2?: string
  /** ADL入浴（一部介助） */
  adlNyuuyoku3?: string
  /** ADL入浴（全介助） */
  adlNyuuyoku4?: string
  /** ADL入浴メモ */
  adlNyuuyokuMemoKnj?: string
  /** ADL食事（自立） */
  adlShokuji1?: string
  /** ADL食事（見守り） */
  adlShokuji2?: string
  /** ADL食事（一部介助） */
  adlShokuji3?: string
  /** ADL食事（全介助） */
  adlShokuji4?: string
  /** ADL食事メモ */
  adlShokujiMemoKnj?: string
  /** 食事回数（朝） */
  shokujiCntMorn?: string
  /** 食事回数（昼） */
  shokujiCntNoon?: string
  /** 食事回数（夜） */
  shokujiCntEven?: string
  /** 食事制限 */
  shokujiSeigen?: string
  /** 食事制限メモ */
  shokujiSeigenMemoKnj?: string
  /** 食事形態（普通） */
  shokujiKeitai1?: string
  /** 食事形態（きざみ） */
  shokujiKeitai2?: string
  /** 食事形態（嚥下障害食） */
  shokujiKeitai3?: string
  /** 食事形態（ミキサー） */
  shokujiKeitai4?: string
  /** 水分制限 */
  suibunSeigen?: string
  /** 水分制限メモ */
  suibunSeigenMemoKnj?: string
  /** 摂取方法（経口） */
  sesshuHouhou1?: string
  /** 摂取方法（経管栄養） */
  sesshuHouhou2?: string
  /** 水分とろみ */
  suibunToromi?: string
  /** UDF等の食形態区分 */
  udfShokujiKeitaiKbn?: string
  /** 嚥下機能 */
  engeUmu?: string
  /** 義歯 */
  gishiUmu?: string
  /** 口腔清潔 */
  koukuuCare?: string
  /** 口臭 */
  koushuuUmu?: string
  /** 排尿（自立） */
  hainyou1?: string
  /** 排尿（見守り） */
  hainyou2?: string
  /** 排尿（一部介助） */
  hainyou3?: string
  /** 排尿（全介助） */
  hainyou4?: string
  /** ポータブルトイレ */
  portableToiletUmu?: string
  /** 排便（自立） */
  haiben1?: string
  /** 排便（見守り） */
  haiben2?: string
  /** 排便（一部介助） */
  haiben3?: string
  /** 排便（全介助） */
  haiben4?: string
  /** オムツ/パッド */
  omutsuPadUmu?: string
  /** 睡眠の状態 */
  sleepJyoutai?: string
  /** 睡眠の状態メモ */
  sleepMemoKnj?: string
  /** 眠剤の使用 */
  sleepDrugUmu?: string
  /** 睡眠時間 */
  sleepTime?: string
  /** 喫煙量 */
  smoking?: string
  /** 飲酒量 */
  alcohol?: string
  /** 視力 */
  eyesightKbn?: string
  /** メガネ */
  glassesUmu?: string
  /** メガネメモ */
  glassesMemoKnj?: string
  /** 聴力 */
  hearingKbn?: string
  /** 補聴器 */
  hearingAidUmu?: string
  /** 言語 */
  languageAbility?: string
  /** 意思疎通 */
  comnKbn?: string
  /** コミュニケーションに関する特記事項 */
  comnTokkiKnj?: string
  /** 精神面における療養上の問題（なし） */
  ryouyou1Umu?: string
  /** 精神面における療養上の問題（幻視・幻聴） */
  ryouyou2Umu?: string
  /** 精神面における療養上の問題（興奮） */
  ryouyou3Umu?: string
  /** 精神面における療養上の問題（焦燥・不穏） */
  ryouyou4Umu?: string
  /** 精神面における療養上の問題（妄想） */
  ryouyou5Umu?: string
  /** 精神面における療養上の問題（暴力/攻撃性） */
  ryouyou6Umu?: string
  /** 精神面における療養上の問題（介護への抵抗） */
  ryouyou7Umu?: string
  /** 精神面における療養上の問題（不眠） */
  ryouyou8Umu?: string
  /** 精神面における療養上の問題（昼夜逆転） */
  ryouyou9Umu?: string
  /** 精神面における療養上の問題（徘徊） */
  ryouyou10Umu?: string
  /** 精神面における療養上の問題（危険行為） */
  ryouyou11Umu?: string
  /** 精神面における療養上の問題（不潔行為） */
  ryouyou12Umu?: string
  /** 精神面における療養上の問題（その他） */
  ryouyou13Umu?: string
  /** 精神面における療養上の問題（その他メモ） */
  ryouyouMemoKnj?: string
  /** 疾患歴（なし） */
  sick1Umu?: string
  /** 疾患歴（悪性腫瘍） */
  sick2Umu?: string
  /** 疾患歴（認知症） */
  sick3Umu?: string
  /** 疾患歴（急性呼吸器感染症） */
  sick4Umu?: string
  /** 疾患歴（脳血管障害） */
  sick5Umu?: string
  /** 疾患歴（骨折） */
  sick6Umu?: string
  /** 疾患歴（その他） */
  sick7Umu?: string
  /** 疾患歴（その他）メモ */
  sickMemoKnj?: string
  /** 最近半年間での入院 */
  nyuuinUmu?: string
  /** 最近半年間での入院開始日 */
  nyuuinStartYmd?: string
  /** 最近半年間での入院終了日 */
  nyuuinEndYmd?: string
  /** 入院頻度 */
  nyuuinHindo?: string
  /** 医療処置（なし） */
  shochi1Umu?: string
  /** 医療処置（点滴） */
  shochi2Umu?: string
  /** 医療処置（酸素療法） */
  shochi3Umu?: string
  /** 医療処置（喀痰吸引） */
  shochi4Umu?: string
  /** 医療処置（気管切開） */
  shochi5Umu?: string
  /** 医療処置（胃ろう） */
  shochi6Umu?: string
  /** 医療処置（経鼻栄養） */
  shochi7Umu?: string
  /** 医療処置（経腸栄養） */
  shochi8Umu?: string
  /** 医療処置（褥瘡） */
  shochi9Umu?: string
  /** 医療処置（尿道カテーテル） */
  shochi10Umu?: string
  /** 医療処置（尿路ストーマ） */
  shochi11Umu?: string
  /** 医療処置（消化管ストーマ） */
  shochi12Umu?: string
  /** 医療処置（痛みコントロール） */
  shochi13Umu?: string
  /** 医療処置（排便コントロール） */
  shochi14Umu?: string
  /** 医療処置（自己注射） */
  shochi15Umu?: string
  /** 医療処置（自己注射）メモ */
  shochi15MemoKnj?: string
  /** 医療処置（その他） */
  shochi16Umu?: string
  /** 医療処置（その他）メモ */
  shochi16MemoKnj?: string
  /** 内服薬 */
  drugUmu?: string
  /** 内服薬メモ */
  drugMemoKnj?: string
  /** 居宅療養管理指導 */
  ryouyouKanriUmu?: string
  /** 居宅療養管理指導メモ */
  ryouyouKanriMemoKnj?: string
  /** 薬剤管理 */
  drugKanriKbn?: string
  /** 薬剤管理（管理者） */
  drugKanriKanrisya?: string
  /** 薬剤管理（管理方法） */
  drugKanriHouhou?: string
  /** 服薬状況 */
  drugJyoukyou?: string
  /** お薬に関する、特記事項 */
  drugTokkiKnj?: string
  /** 医療機関名 */
  hospKnj?: string
  /** 電話番号 */
  hospTel?: string
  /** 医師名フリガナ */
  doctorKana?: string
  /** 医師名 */
  doctorKnj?: string
  /** 診察方法 */
  hospHouhou?: string
  /** 診察頻度（回数） */
  hospKaisu?: string
  /** 診察頻度（単位） */
  hospTani?: string
  /** 住居階層 */
  houseFloor?: string
  /** 居室階 */
  houseRoomFloor?: string
  /** 入院時の要介護度（確定版） */
  nyuuinYokaiKbnKakutei?: string
  /** 認定有効開始日 */
  ninteiStartYmd?: string
  /** 認定終了日 */
  ninteiEndYmd?: string
  /** 申請中フラグ */
  ninteiShinseiFlg?: string
  /** 認定申請日 */
  ninteiShinseiYmd?: string
  /** 区分変更 */
  ninteiKbnHenkou?: string
  /** 区分変更申請日 */
  kbnHenkouYmd?: string
  /** 未申請フラグ */
  ninteiMishinseiFlg?: string
  /** 医師の判断 */
  ishiHandanFlg?: string
  /** ケアマネジャーの判断 */
  careHandanFlg?: string
  /** 自己負担割合有無 */
  futanWariFlg?: string
  /** 自己負担割合 */
  futanWariai?: string
  /** 障害認定（身体） */
  shintaiShogaiKbn?: string
  /** 障害認定（精神） */
  seishinShogaiKbn?: string
  /** 障害認定（知的） */
  chitekiShogaiKbn?: string
  /** 世帯構成（独居） */
  setaiKousei1?: string
  /** 世帯構成（高齢者世帯） */
  setaiKousei2?: string
  /** 世帯構成（子と同居） */
  setaiKousei3?: string
  /** 世帯構成（その他） */
  setaiKousei4?: string
  /** 世帯構成（日中独居） */
  setaiKousei5?: string
  /** 世帯構成（その他）メモ */
  setaiKouseiMemoKnj?: string
  /** 確定版主介護者氏名 */
  kaigoMainKakutei?: string
  /** 主介護者（続柄） */
  kaigoMainZcode?: string
  /** 主介護者（同居・別居） */
  kaigoMainKousei?: string
  /** 主介護者（TEL） */
  kaigoMainTel?: string
  /** キーパーソン（連絡先TEL） */
  keyPersonRenrakuTel?: string
  /** キーパーソン（TEL） */
  keyPersonTel?: string
  /** 入院前の本人の意向（計画書（1）参照有無） */
  userIkouCksFlg?: string
  /** 入院前の家族の意向（計画書（1）参照有無） */
  kazokuIkouCksFlg?: string
  /** 入院前の介護サービスの利用状況（居宅サービス計画書123表） */
  serviceRiyoKbn?: string
  /** 入院前の介護サービスの利用状況（その他） */
  serviceRiyoKbn2?: string
  /** 入院前の介護サービスの利用状況（その他メモ） */
  serviceRiyoMemoKnj?: string
  /** 家族の介護力（子と同居） */
  kazokuKaigo7Umu?: string
  /** 家族の介護力（子と同居）_家族構成員数 */
  kazokuKaigo7Ninzu?: string
  /** 世帯に対する配慮 */
  setaiHairyo?: string
  /** 世帯に対する配慮（必要メモ） */
  setaiHairyoMemoKnj?: string
  /** 退院後の主介護者 */
  taiingoKaigoMain?: string
  /** 退院後の主介護者（氏名） */
  taiingoKaigoMainName?: string
  /** 退院後の主介護者（続柄） */
  taiingoKaigoMainZcode?: string
  /** 退院後の主介護者（年齢） */
  taiingoKaigoMainAge?: string
  /** 家族の介護力（介護力が見込める） */
  kazokuKaigo8Umu?: string
  /** 介護力見込み */
  kazokuKaigo8Kbn?: string
  /** 家族の介護力（介護力は見込めない） */
  kazokuKaigo9Umu?: string
  /** 虐待の疑い */
  gyakutaiUmu?: string
  /** 虐待の疑い（メモ） */
  gyakutaiUmuMemoKnj?: string
  /** 退院前訪問指導への同行 */
  leavHoumonDoukou?: string
  /** 褥瘡メモ */
  jyokusouMemoKnj?: string
  /** ADL移動（室内）（杖） */
  adlIdouShitsunai1?: string
  /** ADL移動（室内）（歩行器） */
  adlIdouShitsunai2?: string
  /** ADL移動（室内）（車いす） */
  adlIdouShitsunai3?: string
  /** ADL移動（室内）（その他） */
  adlIdouShitsunai4?: string
  /** 食事回数 */
  shokujiCnt?: string
  /** 義歯区分 */
  gishiKbn?: string
  /** 喫煙有無 */
  smokingUmu?: string
  /** 飲酒有無 */
  alcoholUmu?: string
  /** 入院理由 */
  nyuuinRiyu?: string
  [key: string]: string | undefined | null
}
