<script setup lang="ts">
/**
 * Or10355:(アセスメント履歴取込画面)ダイアログ
 * GUI00920_アセスメント履歴取込画面
 *
 * @description
 * (アセスメント履歴取込画面)ダイアログ
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or03291Const } from '../Or03291/Or03291.constants'
import { Or03292Const } from '../Or03292/Or03292.constants'
import { Or03293Const } from '../Or03293/Or03293.constants'
import { Or10355Const } from './Or10355.constants'
import type { AssessmentRirekiTorikomiSelect, Or10355StateType } from './Or10355.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or10355OnewayType, Or10355Type } from '~/types/cmn/business/components/Or10355Type'
import type {
  AssessmentRirekiTorikomiSelectInEntity,
  AssessmentRirekiTorikomiSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentRirekiTorikomiSelectEntity'
import type {
  RirekiKoumokuDetailSelectInEntity,
  RirekiKoumokuDetailSelectOutEntity,
} from '~/repositories/cmn/entities/RirekiKoumokuDetailSelectEntity'
import type {
  KoumokuDetailSelectInEntity,
  KoumokuDetailSelectOutEntity,
} from '~/repositories/cmn/entities/KoumokuDetailSelectEntity'
import type { Or03291OnewayType } from '~/types/cmn/business/components/Or03291Type'
import type { Or03292OnewayType } from '~/types/cmn/business/components/Or03292Type'
import type { Or03293OnewayType } from '~/types/cmn/business/components/Or03293Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * useI18n
 */
const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or10355Type
  onewayModelValue: Or10355OnewayType
  uniqueCpId: string
}
/**
 * defineProps
 */
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * Or10355OnewayType
 */
const defaultOnewayModelValue: Or10355OnewayType = {
  shisetuId: '',
  menu2Knj: '',
  menu3Knj: '',
  valS: '',
  kikanFlg: '1',
}
/**
 * AssessmentRirekiTorikomiSelect
 */
const defaultModelValue: AssessmentRirekiTorikomiSelect = {
  kikanFlg: '0',
  handanFlg: '1',
  kikanList: [],
  rirekiList: [],
  torikomiList: [],
}
/**
 * localOneway
 */
const localOneway = reactive({
  or10355: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '1200px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.import-assessment-history'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },

  // 閉じるボタン設置
  mo00611Oneway: {
    btnLabel: t('btn.close'),
    width: '80px',
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 確定ボタン設置
  mo00609Oneway: {
    btnLabel: t('btn.confirm'),
    width: '80px',
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
})
/** Or03291コンポーネントのRef */
const or03291 = ref({ uniqueCpId: Or03291Const.CP_ID(0) })
/** Or03292コンポーネントのRef */
const or03292 = ref({ uniqueCpId: Or03292Const.CP_ID(0) })
/** Or03293コンポーネントのRef */
const or03293 = ref({ uniqueCpId: Or03293Const.CP_ID(0) })
/** Or21814ダイアログのRef */
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
/**
 * local
 */
const local = reactive({
  or10355: {
    ...defaultModelValue,
  } as AssessmentRirekiTorikomiSelect,
  or03291: {
    dataTable: [],
    selectedItemIndex: -1,
  } as Or03291OnewayType,
  or03292: {
    dataTable: [],
  } as Or03292OnewayType,
  or03293: {
    dataTable: [],
    selectedItemIndex: -1,
  } as Or03293OnewayType,
})

/**
 * ダイアログ設置
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10355Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/
/**
 * setState
 */
const { setState } = useScreenOneWayBind<Or10355StateType>({
  cpId: Or10355Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * ダイアログの開閉状態を更新する
     *
     * @param value - ダイアログの開閉状態
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10355Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
/**
 * emit
 */
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 子コンポーネントのユニークIDを設定する
 */
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
})

onMounted(() => {
  void init()
})
/**
 * 初期化
 */
async function init() {
  await getInitAssessmentRirekiTorikomi()
}
/**
 * 初期情報取得
 */
async function getInitAssessmentRirekiTorikomi() {
  const inputData: AssessmentRirekiTorikomiSelectInEntity = {
    svJigyoId: localOneway.or10355.svJigyoId ?? '',
    userId: localOneway.or10355.userId ?? '',
    shisetuId: localOneway.or10355.shisetuId ?? '',
    syubetsuId: localOneway.or10355.syubetsuId ?? '',
    menu2Knj: localOneway.or10355.menu2Knj,
    menu3Knj: localOneway.or10355.menu3Knj,
    valS: localOneway.or10355.valS,
  }

  const resData: AssessmentRirekiTorikomiSelectOutEntity = await ScreenRepository.select(
    'assessmentRirekiTorikomiSelect',
    inputData
  )
  if (resData?.data) {
    await nextTick()
    local.or10355 = resData.data
    if (local.or10355.kikanFlg === Or10355Const.DEFAULT.ONE) {
      local.or03291.dataTable = resData.data.kikanList.map((item) => ({
        sc1Id: item.sc1Id,
        planPeriod: `${item.startYmd} ~ ${item.endYmd}`,
        numberOfWithinThePeriodHistory: local.or10355.rirekiList
          .filter((rireki) => rireki.raiId === item.sc1Id)
          .length.toString(),
      }))

      local.or03291.selectedItemIndex = local.or03291.dataTable.length ? 0 : -1
    } else {
      local.or03293.dataTable = resData.data.rirekiList
      local.or03293.selectedItemIndex = local.or03293.dataTable.length ? 0 : -1
    }
  }
}
/**
 * 期間IDに紐づく履歴項目詳細情報を取得する
 *
 * @param kikanId - 期間ID
 */
async function getInitrirekiKoumokuDetail(kikanId: string) {
  const inputData: RirekiKoumokuDetailSelectInEntity = {
    syubetuId: systemCommonsStore.getSyubetu ?? '',
    kikanId: kikanId,
    svJigyoId: localOneway.or10355.svJigyoId ?? '',
    userId: localOneway.or10355.userId ?? '',
    handanFlg: local.or10355.handanFlg,
  }
  const resData: RirekiKoumokuDetailSelectOutEntity = await ScreenRepository.select(
    'rirekiKoumokuDetailSelect',
    inputData
  )
  if (resData?.data) {
    local.or03293.dataTable = resData.data.rirekiList
    local.or03293.selectedItemIndex = local.or03293.dataTable.length ? 0 : -1
  }
}
/**
 * アセスメントIDに紐づく項目詳細情報を取得する
 *
 * @param raiId - アセスメントID
 */
async function getInikoumokuDetail(raiId: string) {
  const inputData: KoumokuDetailSelectInEntity = {
    raiId: raiId,
  }
  const resData: KoumokuDetailSelectOutEntity = await ScreenRepository.select(
    'koumokuDetailSelect',
    inputData
  )
  if (resData?.data) {
    local.or03292.dataTable = resData.data.torikomiList
  }
}
/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  if (local.or03293.selectedItemIndex !== Or10355Const.DEFAULT.MINUS_ONE) {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-10207'),
        dialogTitle: t('label.info'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'normal3',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'blank',
        iconName: 'info',
        iconColor: 'rgb(var(--v-theme-blue-700))',
        iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
      },
    })
  } else {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogTitle: t('label.info'),
        firstBtnType: 'blank',
        secondBtnLabel: t('btn.ok'),
        secondBtnType: 'normal1',
        thirdBtnType: 'blank',
        iconName: 'info',
        iconColor: 'rgb(var(--v-theme-blue-700))',
        iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
        dialogText: t('message.i-cmn-11289'),
      },
    })
  }
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      emit('update:modelValue', {
        raiId: local.or03293.dataTable[local.or03293.selectedItemIndex].raiId,
      })
      doClose()
    }
  }
)
/**
 * 「閉じる」処理
 */
function doClose() {
  setState({ isOpen: false })
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = () => {
  doClose()
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
watch(
  () => local.or03291.selectedItemIndex,
  async (newValue) => {
    if (newValue > -1) {
      await getInitrirekiKoumokuDetail(local.or03291.dataTable[newValue].sc1Id)
      await nextTick()
    } else {
      local.or03293.dataTable = []
      local.or03293.selectedItemIndex = -1
    }
  }
)
watch(
  () => local.or03293.selectedItemIndex,
  async (newValue) => {
    if (newValue > -1) {
      await getInikoumokuDetail(local.or03293.dataTable[newValue].raiId)
      await nextTick()
    } else {
      local.or03292.dataTable = []
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            v-if="
              local.or10355.kikanFlg === Or10355Const.DEFAULT.ONE &&
              localOneway.or10355.kikanFlg === Or10355Const.DEFAULT.ONE
            "
            cols="5"
            class="pr-2"
          >
            <!-- 履歴リスト表示コンポーネント -->
            <g-custom-or-03291
              v-bind="or03291"
              v-model="local.or03291"
              :parent-unique-cp-id="props.uniqueCpId"
            />
          </c-v-col>
          <c-v-col cols="7">
            <!-- 履歴リスト表示コンポーネント -->
            <g-custom-or-03293
              v-bind="or03293"
              v-model="local.or03293"
              :parent-unique-cp-id="props.uniqueCpId"
            />
          </c-v-col>
          <c-v-col cols="12 mt-2">
            <!-- 履歴リスト表示コンポーネント -->
            <g-custom-or-03292
              v-bind="or03292"
              v-model="local.or03292"
              :parent-unique-cp-id="props.uniqueCpId"
            />
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611Oneway"
          class="mx-2"
          @click="onClickCloseBtn"
        >
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          class="mx-2"
          :oneway-model-value="localOneway.mo00609Oneway"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21814 v-bind="or21814" />
</template>
