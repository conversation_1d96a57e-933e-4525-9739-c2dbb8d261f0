<script setup lang="ts">
import { computed, ref, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or28278Const } from '~/components/custom-components/organisms/Or28278/Or28278.constants'
import { Or28278Logic } from '~/components/custom-components/organisms/Or28278/Or28278.logic'
import type { Or28278Type, Or28278OnewayType } from '~/types/cmn/business/components/Or28278Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00646'
// ルーティング
const routing = 'GUI00646/pinia'
// 画面物理名
const screenName = 'GUI00646'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or28278 = ref({ uniqueCpId: Or28278Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00646' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27626Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or28278.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI0028278',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28278Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or28278Const.CP_ID(1)]: or28278.value,
})

// ダイアログ表示フラグ
const showDialogOr28278 = computed(() => {
  // Or27626のダイアログ開閉状態
  return Or28278Logic.state.get(or28278.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or27626)
 */
function onClickOr28278() {
  // Or27626のダイアログ開閉状態を更新する
  Or28278Logic.state.set({
    uniqueCpId: or28278.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or28278Type = ref<Or28278Type>({
  /** 保険区分コード */
  type: '',
  /** 保険者番号*/
  publicno: '',
  /** 記号・番号*/
  kigonoKnj: '',
  /** 有効期間開始日*/
  startdateYmd: '',
  /** 有効期間終了日*/
  enddateYmd: '',
  /** 保険名称*/
  nameKnj: '',
})

const or28278Data: Or28278OnewayType = {
  /** 利用者ID */
  userId: '1',
}

// watch(
//   () => or27626Type,
//   (newValue) => {
//     console.log(newValue)
//   },
//   { deep: true }
// )

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  userId: { value: '1' } as Mo00045Type,
})

/** GUI00646 疎通起動  */
const or28389OnClick = () => {
  or28278Data.userId = local.userId.value
  // Or27626のダイアログ開閉状態を更新する
  Or28278Logic.state.set({
    uniqueCpId: or28278.value.uniqueCpId,
    state: { isOpen: true },
  })
}


</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28278"
        >GUI00646_［主保険選択］画面
      </v-btn>
      <g-custom-or-28278
        v-if="showDialogOr28278"
        v-bind="or28278"
        v-model="or28278Type"
        :oneway-model-value="or28278Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="or28389OnClick"> GUI00646 疎通起動 </v-btn>
  </div>
  <div class="pt-5 pl-5">
    return ----- data

    <div>
      {{ or28278Type }}
    </div>
  </div>
</template>
