import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * OrX0218:有機体:実施計画①～③表用選択メニュー付き日付入力
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace OrX0218Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('OrX0218', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * カレンダーモード 0:単日指定
     */
    export const CALENDAR_MODE_SINGLE_DAY = '0'
    /**
     * カレンダーモード 2:範囲選択（期間指定）
     */
    export const CALENDAR_MODE_RANGE = '1'
    /**
     * カレンダーモード NONE
     */
    export const CALENDAR_MODE_NONE = ''
    /**
     * 前後に改行のある区切り線
     */
    export const LINE_BREAK_AND_WAVE = '\r\n～\r\n'
    /**
     * 年月で入力
     */
    export const SELECT_MODE_MONTH_YEAR = '3'

    /**
     * 要素を完全に非表示にするスタイル定義
     */
    export const HIDDEN_STYLE = `
      height:0 !important;
      visibility:hidden !important;
      overflow:hidden !important;
      position:absolute !important;
      z-index:-1000 !important;
      top:0 !important;
      right:0 !important
    `
    /**
     * テキスト計測に影響を与えるCSSプロパティの定義リスト
     */
    export const CONTEXT_STYLE = [
      'letter-spacing',
      'line-height',
      'padding-top',
      'padding-bottom',
      'font-family',
      'font-weight',
      'font-size',
      'text-rendering',
      'text-transform',
      'width',
      'text-indent',
      'padding-left',
      'padding-right',
      'border-width',
      'box-sizing',
    ]
    /**
     * スタイル: border-box
     */
    export const STYLE_BORDER_BOX = 'border-box'
    /**
     * スタイル: content-box
     */
    export const STYLE_CONTENT_BOX = 'content-box'
    /**
     * 文字列: style
     */
    export const STYLE = 'style'
    /**
     * 文字列: textarea
     */
    export const TEXTAREA = 'textarea'
    /**
     * スタイル: box-sizing
     */
    export const STYLE_BOX_SIZING = 'box-sizing'
    /**
     * スタイル: padding-bottom
     */
    export const STYLE_PADDING_BOTTOM = 'padding-bottom'
    /**
     * スタイル: padding-top
     */
    export const STYLE_PADDING_TOP = 'padding-top'
    /**
     * スタイル: border-bottom-width
     */
    export const STYLE_BORDER_BOTTOM_WIDTH = 'border-bottom-width'
    /**
     * スタイル: border-top-width
     */
    export const STYLE_BORDER_TOP_WIDTH = 'border-top-width'
    /**
     * セミコロン文字を表す定数
     */
    export const SEMICOLON = ';'
    /**
     * 月日で入力のフォーマット
     */
    export const MONTHDAY_FORMAT = 'MM/DD'
    /**
     * フォーマット
     */
    export const DEFAULT_FORMAT = 'YYYY/MM/DD'
  }
}
