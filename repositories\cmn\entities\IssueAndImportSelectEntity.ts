import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
/**
 * Or27604:(課題整理総括)課題取込モーダル
 * GUI00923_課題取込画面
 *
 * @description
 * 双方向バインドのデータ構造
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */

/** 課題と目標取込入力エンティティ */
export interface IssueAndImportSelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 種別ID */
  syubetsuId: string
  /** 施設ID */
  shisetuId: string
  /** 期間管理フラグ */
  kanriFlg: string
}

/**
 * 課題と目標取込出力エンティティ
 */
export interface IssueAndImportSelectOutEntity extends OutWebEntity {
  /** 計画期間情報リスト */
  krkComKikanList: KrkComKikan[]
  /** インターライ方式_履歴リスト */
  cpnKkak2RaiToriRirekiList: CpnKkak2RaiToriRireki[]
  /** インターライ方式_CAP検討_課題リスト */
  cpnKkak2RaiToriList: CpnKkak2RaiTori[]
}
/**
 * 計画期間情報
 */
export interface KrkComKikan {
  /** 日数カウント */
  dmyCnt: string
  /** 期間ID */
  sc1Id: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 種別ID */
  syubetsuId: string
  /** 開始日  */
  startYmd: string
  /** 終了日  */
  endYmd: string
  /** 事業名 */
  jigyoKnj: string
}
/**
 *  インターライ方式_履歴情報
 */
export interface CpnKkak2RaiToriRireki {
  /** アセスメントID */
  raiId: string
  /** 計画期間ID */
  sc1Id: string
  /** 調査アセスメント種別 */
  assType: string
  /** 調査日  */
  assDateYmd: string
  /** 調査者ID */
  assShokuId: string
  /** 検討日  */
  plnDateYmd: string
  /** 検討者ID */
  plnShokuId: string
  /** 検討アセスメント種別 */
  plnType: string
}

/**
 *  インターライ方式_CAP検討_課題情報
 */
export interface CpnKkak2RaiTori {
  /** アセスメントID */
  raiId: string
  /** CAPID */
  capId: string
  /** 課題ID */
  kadaiId: string
  /** 課題 */
  kadaiKnj: string
  /** 長期目標 */
  choukiKnj: string
  /** 短期目標 */
  tankiKnj: string
  /** ケア内容 */
  careKnj: string
  /** 表示順2 */
  plan2Sort: string
  /** 選択状態 */
  sel: string
  /** 表示順1 */
  plan1Sort: string
}
