/**
 * ［アセスメント］画面（居宅）モーダル
 *
 * @description
 * ［アセスメント］画面（居宅）モーダル
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { AssessmentDuplicateSelectInEntity } from '~/repositories/cmn/entities/AssessmentDuplicateSelectEntity'
/**
 * ［アセスメント］画面（居宅）
 *
 * @description
 * ［アセスメント］画面（居宅）初期情報データを返却する。
 * dataName："assessmentHomeTab61Select"
 */
export function handler(inEntity: AssessmentDuplicateSelectInEntity) {
  let result = {}
  if (inEntity.svJigyoId === '1' && inEntity.userId == '0000000123') {
    result = defaultData.res1
  }
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...result,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
