<script setup lang="ts">
import {
  computed,
  onMounted,
  reactive,
  ref,
  watch,
  useTemplateRef,
  type ComponentPublicInstance,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { isEmpty } from 'lodash'
import { useRouter } from 'vue-router'
import { OrX0006Const } from '../OrX0006/OrX0006.constants'
import { Or33787Logic } from '../Or33787/Or33787.logic'
import { Or33787Const } from '../Or33787/Or33787.constants'
import { Or32312Const } from './Or32312.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useUsersProfileView, useSetupChildProps, hasRegistAuth, hasPrintAuth } from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { OrX0006OnewayType } from '~/types/cmn/business/components/OrX0006Type'
import type {
  PaymentStatusInfoDelInEntity,
  PaymentStatusInfoDelOutEntity,
  PaymentStatusInfoEntityInEntity,
  PaymentStatusInfoEntityOutEntity,
} from '~/repositories/cmn/entities/PaymentStatusInfoEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  KyufuInfo,
  SortConfig,
} from '~/components/custom-components/organisms/Or32312/Or32312.type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or33787Type } from '~/types/cmn/business/components/Or33787Type'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { useJigyoList } from '~/utils/useJigyoList'
import { Or21828Const } from '~/components/base-components/organisms/Or21828/Or21828.constants'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import { CustomClass } from '~/types/CustomClassType'
import type { OrX0165OnewayType } from '~/types/cmn/business/components/OrX0165Type'
import { useUserListInfo } from '~/utils/useUserListInfo'
import type { Mo01266OnewayType } from '~/types/business/components/Mo01266Type'
import { Or36653Const } from '~/components/custom-components/organisms/Or36653/Or36653.constants'
import { Or36653Logic } from '~/components/custom-components/organisms/Or36653/Or36653.logic'
import type { Or36653OnewayType } from '~/types/cmn/business/components/Or36653Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { Or53953Const } from '~/components/custom-components/organisms/Or53953/Or53953.constants'
import { Or53953Logic } from '~/components/custom-components/organisms/Or53953/Or53953.logic'
import type { Or53953OneWayType } from '~/components/custom-components/organisms/Or53953/Or53953.type'

interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()
/**
 *利用者選択監視関数を実行
 */
const { syscomUserSelectWatchFunc } = useUserListInfo()
const { t } = useI18n()
const router = useRouter()

const systemCommonsStore = useSystemCommonsStore()
const or11871 = ref({ uniqueCpId: '' })
const or21828 = ref({ uniqueCpId: Or21828Const.CP_ID })
const or00248 = ref({ uniqueCpId: '' })
const orHeadLine = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const orX0006 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or33787 = ref({ uniqueCpId: '' })
const or41179 = ref({ uniqueCpId: '' })
const or36653 = ref({ uniqueCpId: Or36653Const.CP_ID(0) })
const or53953 = ref({ uniqueCpId: Or53953Const.CP_ID(1) })
const { jigyoListWatch } = useJigyoList()

// TODO mock一時権限 start
// 保存権限
let hasRegist: boolean = await hasRegistAuth()
hasRegist = true
// 印刷権限
let hasPrint: boolean = await hasPrintAuth()
hasPrint = true
// TODO mock一時権限 end

const tblKyufuRef = useTemplateRef<ComponentPublicInstance>('tblKyufu')
const tblKyufuSumRef = useTemplateRef<ComponentPublicInstance>('tblKyufuSum')

const or33787Type = ref<Or33787Type>({
  // 処理フラグ
  dummy: '0',
})

const local = reactive({
  paymentStatusList: [] as KyufuInfo[],
  orgPaymentStatusList: [] as KyufuInfo[],
})

const localOneway = reactive({
  // 処理年度
  yymmY: '',
  // 事業所ID
  svJigyoId: '',
  selectedPath: {
    modelValue: '/care-manager/payment-status',
  },
  // 予実一括
  mo00611Oneway: {
    name: 'editBtn',
    btnLabel: t('label.budget-performance-actual-performance-mult'),
    width: '70px',
    class: 'btn-edit',
  } as Mo00611OnewayType,
  orX0165Plan: {
    label: t('label.processing-year'),
    pageLabelFontSize: '20px',
  } as OrX0165OnewayType,
  // プルダウン
  mo01282Oneway: {
    showItemLabel: false,
    itemTitle: 'label',
    itemValue: 'value',
    items: [
      { label: t('label.list'), value: '/care-manager/payment-list' },
      {
        label: t('label.support-elapsed-record-select-individual'),
        value: '/care-manager/payment-status',
      },
    ],
    customClass: new CustomClass({ itemClass: 'ml-2' }),
  } as Mo00040OnewayType,
  Mo00045OnewayType: {
    showItemLabel: false,
    width: '90px',
    readonly: true,
  } as Mo00045OnewayType,
  // 処理年度-前へアイコンボタン
  mo00009Left: {
    // デフォルト値の設定
    btnIcon: 'chevron_left',
    density: 'compact',
  } as Mo00009OnewayType,
  // 処理年度-次へアイコンボタン
  mo00009Right: {
    // デフォルト値の設定
    btnIcon: 'chevron_right',
    density: 'compact',
  } as Mo00009OnewayType,
  // 該当件数
  sum: 0,
  //予定合計
  svTensuYSum: 0,
  //実績合計
  svTensuJSum: 0,
  //差分合計
  sabunSum: 0,
  //超過合計
  dmyOverSum: 0,
  //基準内合計
  dmyKijunSum: 0,
  //保険分合計
  hHknHutanSum: 0,
  //全額分合計
  hJihHutanSum: 0,
  //前月合計
  zengetuSum: 0,
  //当月合計
  tougetuSum: 0,
  //累計合計
  ruikeiSum: 0,
  //実績済合計
  yoteiZumiSum: 0,
  orX0006Oneway: {} as OrX0006OnewayType,
  selectRowIndex: 0,
  mo00009Oneway8: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01266Oneway: {
    width: '57px',
    minWidth: '57px',
    btnLabel: t('label.using-slip'),
    to: '',
  } as Mo01266OnewayType,
  // 利用票ダイアログ
  or36653Oneway: {} as Or36653OnewayType,
  // 利用票 予実一括設定
  or53953OneWay: {} as Or53953OneWayType,
})

onMounted(() => {
  // 処理年度
  localOneway.yymmY = String(new Date().getFullYear())
  localOneway.orX0165Plan.pageLabel = localOneway.yymmY + t('label.fiscal-year')

  // 事業所
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getSvJigyoId,
      },
    },
  })

  // ★事業所選択監視関数を実行
  jigyoListWatch(or41179.value.uniqueCpId, (newJigyoId: string) => {
    void callbackFuncJigyo(newJigyoId)
  })

  if (tblKyufuRef.value !== null) {
    const element = tblKyufuRef.value.$el as HTMLDivElement
    const tableWrapper = element.querySelector('.v-table__wrapper')
    if (tableWrapper !== null) {
      tableWrapper.addEventListener('scroll', (e: Event) => {
        syncScroll(e)
      })
    }
  }
})

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function callbackFuncJigyo(newJigyoId: string) {
  if (!isEmpty(newJigyoId) && !isEmpty(systemCommonsStore.getUserSelectSelfId())) {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const jigyoInfo = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)
    localOneway.svJigyoId = jigyoInfo?.svJigyoId ?? ''

    // 給付状況情報を取得する。
    const inputData: PaymentStatusInfoEntityInEntity = {
      // 事業所ID
      svJigyoId: localOneway.svJigyoId,
      // 適用事業所ＩＤリスト
      shienIdList:
        systemCommonsStore.getSvJigyoIdList.map((item) => {
          return { shienId: item }
        }) ?? [],
      // 利用者ID
      userId: systemCommonsStore.getUserSelectSelfId() ?? systemCommonsStore.getUserId ?? '',
      // 処理年度
      yymmY: localOneway.yymmY,
    }
    const resData: PaymentStatusInfoEntityOutEntity = await ScreenRepository.select(
      'benefitStatusInfoSelect',
      inputData
    )
    if (resData?.data) {
      // 画面レンダリング
      setDate(resData)

      // 事業所
      Or41179Logic.state.set({
        uniqueCpId: or41179.value.uniqueCpId,
        state: {
          searchCriteria: {
            selfId: jigyoInfo?.svJigyoId ?? '1',
          },
        },
      })
    }
  }
}

useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or21828Const.CP_ID]: or21828.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [OrX0006Const.CP_ID(1)]: orX0006.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
  [Or36653Const.CP_ID(0)]: or36653.value,
  [Or53953Const.CP_ID(1)]: or53953.value,
})

Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    showFavorite: false,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: false,
    showCreateBtn: false,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    disabledPrintBtn: !hasPrint,
    showMasterBtn: true,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
  },
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or33787Const.CP_ID(1)]: or33787.value,
})

useUsersProfileView({
  or00248UniqCpId: or00248.value.uniqueCpId,
  or00249UniqCpId: or00249.value.uniqueCpId,
  orHeadLineUniqCpId: orHeadLine.value.uniqueCpId,
})

/**
 * 初期化
 */
const init = async () => {
  if (!isEmpty(systemCommonsStore.getUserSelectSelfId())) {
    // 給付状況情報を取得する。
    const inputData: PaymentStatusInfoEntityInEntity = {
      // 事業所ID
      svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
      // 適用事業所ＩＤリスト
      shienIdList:
        systemCommonsStore.getSvJigyoIdList.map((item) => {
          return { shienId: item }
        }) ?? [],
      // 利用者ID
      userId: systemCommonsStore.getUserSelectSelfId() ?? systemCommonsStore.getUserId ?? '',
      // 処理年度
      yymmY: localOneway.yymmY,
    }
    const resData: PaymentStatusInfoEntityOutEntity = await ScreenRepository.select(
      'benefitStatusInfoSelect',
      inputData
    )
    if (resData?.data) {
      // 画面レンダリング
      setDate(resData)
    }
  }
}

/**
 * 画面レンダリング
 *
 * @param resData  - resData
 */
const setDate = (resData: PaymentStatusInfoEntityOutEntity) => {
  local.paymentStatusList = []
  local.orgPaymentStatusList = []
  const result = resData.data.kyufuInfoList
  // 該当件数
  localOneway.sum = resData.data.kyufuInfoList.length
  // 初期値0
  localOneway.svTensuYSum = Or32312Const.ZERO
  localOneway.svTensuJSum = Or32312Const.ZERO
  localOneway.sabunSum = Or32312Const.ZERO
  localOneway.dmyOverSum = Or32312Const.ZERO
  localOneway.dmyKijunSum = Or32312Const.ZERO
  localOneway.hHknHutanSum = Or32312Const.ZERO
  localOneway.hJihHutanSum = Or32312Const.ZERO
  localOneway.zengetuSum = Or32312Const.ZERO
  localOneway.tougetuSum = Or32312Const.ZERO
  localOneway.ruikeiSum = Or32312Const.ZERO
  localOneway.yoteiZumiSum = Or32312Const.ZERO
  result.map((item) => {
    //予定合計
    localOneway.svTensuYSum += item.svTensuY === '' ? Or32312Const.ZERO : Number(item.svTensuY)
    //実績合計
    localOneway.svTensuJSum += item.svTensuJ === '' ? Or32312Const.ZERO : Number(item.svTensuJ)
    //差分合計
    localOneway.sabunSum += item.sabun === '' ? Or32312Const.ZERO : Number(item.sabun)
    //超過合計
    localOneway.dmyOverSum += item.dmyOver === '' ? Or32312Const.ZERO : Number(item.dmyOver)
    //基準内合計
    localOneway.dmyKijunSum += item.dmyKijun === '' ? Or32312Const.ZERO : Number(item.dmyKijun)
    //保険分合計
    localOneway.hHknHutanSum += item.hhknHutan === '' ? Or32312Const.ZERO : Number(item.hhknHutan)
    //全額分合計
    localOneway.hJihHutanSum += item.hjihHutan === '' ? Or32312Const.ZERO : Number(item.hjihHutan)
    //前月合計
    localOneway.zengetuSum += item.zengetu === '' ? Or32312Const.ZERO : Number(item.zengetu)
    //当月合計
    localOneway.tougetuSum += item.tougetu === '' ? Or32312Const.ZERO : Number(item.tougetu)
    //累計合計
    localOneway.ruikeiSum += item.ruikei === '' ? Or32312Const.ZERO : Number(item.ruikei)
    //実績済合計
    localOneway.yoteiZumiSum +=
      item.yoteiZumi === Or32312Const.SVTENSUY_VAL ? Or32312Const.ONE : Or32312Const.ZERO
  })
  local.paymentStatusList = result
  local.orgPaymentStatusList = result
}

/**
 * select row
 *
 * @param index  - index
 */
const selectRow = (index: number) => {
  localOneway.selectRowIndex = index
}

/**
 * 千文字の追加
 *
 * @param data - data
 */
const addThousandSeparator = (data: string) => {
  if (data === '' || data === null) {
    return ''
  }
  return new Intl.NumberFormat('en-US').format(Number(data))
}

/**
 * 「前処理年度アイコンボタン」押下
 */
const mo00009LeftClick = async () => {
  sortConfig.value.direction = Or32312Const.SORT_INIT
  localOneway.yymmY = String(Number(localOneway.yymmY) - 1)
  localOneway.orX0165Plan.pageLabel = localOneway.yymmY + t('label.fiscal-year')
  // 給付状況情報を取得する。
  const inputData: PaymentStatusInfoEntityInEntity = {
    // 事業所ID
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    // 適用事業所ＩＤリスト
    shienIdList:
      systemCommonsStore.getSvJigyoIdList.map((item) => {
        return { shienId: item }
      }) ?? [],
    // 利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? systemCommonsStore.getUserId ?? '',
    // 処理年度
    yymmY: localOneway.yymmY,
  }
  const resData: PaymentStatusInfoEntityOutEntity = await ScreenRepository.select(
    'benefitStatusInfoSelect',
    inputData
  )
  if (resData?.data) {
    // 画面レンダリング
    setDate(resData)
  }
}

/**
 * 「次処理年度アイコンボタン」押下
 */
const mo00009RightClick = async () => {
  sortConfig.value.direction = Or32312Const.SORT_INIT
  localOneway.yymmY = String(Number(localOneway.yymmY) + 1)
  localOneway.orX0165Plan.pageLabel = localOneway.yymmY + t('label.fiscal-year')
  // 給付状況情報を取得する。
  const inputData: PaymentStatusInfoEntityInEntity = {
    // 事業所ID
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    // 適用事業所ＩＤリスト
    shienIdList:
      systemCommonsStore.getSvJigyoIdList.map((item) => {
        return { shienId: item }
      }) ?? [],
    // 利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? systemCommonsStore.getUserId ?? '',
    // 処理年度
    yymmY: localOneway.yymmY,
  }
  const resData: PaymentStatusInfoEntityOutEntity = await ScreenRepository.select(
    'benefitStatusInfoSelect',
    inputData
  )
  if (resData?.data) {
    // 画面レンダリング
    setDate(resData)
  }
}

/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 確認ダイアログOneWayBind領域用の構造
 */
const openInfoDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

// ダイアログ表示フラグ
const showDialog21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 利用票ダイアログ表示フラグ
const showDialogOr36653 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or36653Logic.state.get(or36653.value.uniqueCpId)?.isOpen ?? false
})

// 利用票 予実一括設定ダイアログ表示フラグ
const showDialogOr53953 = computed(() => {
  // OrX0097のダイアログ開閉状態
  return Or53953Logic.state.get(or53953.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 予実一括
 */
const button1Click = () => {
  // GUI00615 「利用票 予実一括設定」画面をポップアップで起動する。
  Or53953Logic.state.set({
    uniqueCpId: or53953.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 削除
 */
const button2Click = async () => {
  // 選択行の実績済フラグ＝1の場合
  if (local.paymentStatusList[localOneway.selectRowIndex].jissekiZumiFlg === Or32312Const.FLG_ONE) {
    const dialogResult = await openInfoDialog(or21814.value.uniqueCpId, {
      dialogText: t('message.i-cmn-10681'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
    })

    switch (dialogResult) {
      case 'yes':
        // はい
        return false
    }
  }
  // 選択行の予定済フラグ＝1の場合
  if (local.paymentStatusList[localOneway.selectRowIndex].yoteiZumiFlg === Or32312Const.FLG_ONE) {
    const dialogResult = await openInfoDialog(or21814.value.uniqueCpId, {
      dialogText: t('message.i-cmn-10682'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
    })

    switch (dialogResult) {
      case 'yes':
        // はい
        return false
    }
  }
  const dialogResult = await openInfoDialog(or21814.value.uniqueCpId, {
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11343', [t('label.using-slip-other-table-warning-message')]),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
  })

  switch (dialogResult) {
    case 'yes': {
      // 給付状況情報削除処理
      const inputData: PaymentStatusInfoDelInEntity = {
        // 事業所ID
        svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
        // 利用者ID
        userId: systemCommonsStore.getUserSelectSelfId() ?? systemCommonsStore.getUserId ?? '',
        // 削除月
        asYm: sortList.value[localOneway.selectRowIndex].yymmYmHid,
        // 削除日
        asYmD: sortList.value[localOneway.selectRowIndex].yymmD ?? Or32312Const.DAY_ONE,
        // 職員ID
        shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      }
      const resData: PaymentStatusInfoDelOutEntity = await ScreenRepository.delete_phys(
        'useSlipDataDelete',
        inputData
      )
      if (resData?.data) {
        // 給付状況情報を取得する。
        void init()
        if (sortList.value.length > 0) {
          // 削除した行は最終行の場合
          if (localOneway.selectRowIndex > sortList.value.length) {
            localOneway.selectRowIndex = sortList.value.length
          }
        } else {
          localOneway.selectRowIndex = Or32312Const.ZERO
        }
      }
      return false
    }
    case 'no':
      return false
  }
}

/**
 * ソートInfo
 */
const sortConfig = ref<SortConfig>({
  field: Or32312Const.COL_1,
  direction: Or32312Const.SORT_INIT,
})

/**
 * リストのソート
 */
const sortList = computed(() => {
  let result = local.orgPaymentStatusList
  if (sortConfig.value.direction !== Or32312Const.SORT_INIT) {
    result = [...local.paymentStatusList].sort((a, b) => {
      const field = sortConfig.value.field
      const valueA = a[field]
      const valueB = b[field]

      if (valueA < valueB) {
        return sortConfig.value.direction === Or32312Const.SORT_ASC ? -1 : 1
      }
      if (valueA > valueB) {
        return sortConfig.value.direction === Or32312Const.SORT_ASC ? 1 : -1
      }
      return 0
    })
  }
  return result
})

// ダイアログ表示フラグ
const showDialogOr33787 = computed(() => {
  // Or33787のダイアログ開閉状態
  return Or33787Logic.state.get(or33787.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 一覧/個別
 */
const changeSelectedPage = async () => {
  await router.push(localOneway.selectedPath.modelValue)
}

/**
 * 利用者選択監視関数に渡すコールバック関数（サブ01の例）
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param _newSelfId - 利用者
 *
 * @param _oldSelfId - 利用者
 */
const callbackFuncSub01 = (_newSelfId: string, _oldSelfId: string) => {
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })
  void init()
}
// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackFuncSub01)

/**
 * 利用票アイコン押下
 *
 * @param index -index
 */
const usingSlipClick = (index: number) => {
  const selectedItem = sortList.value[index]
  // 再検索前に選択した行をフォーカスする
  localOneway.or36653Oneway = {
    // 利用者ID：親画面.利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? systemCommonsStore.getUserId ?? '',
    // 事業所ID：選択行の支援事業者ID
    svJigyoId: selectedItem.shienId,
  } as Or36653OnewayType
  // GUI01149 利用票画面をポップアップで起動する。
  Or36653Logic.state.set({
    uniqueCpId: or36653.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 二つのテーブルのスクロールを同期
 *
 * @param e -Event
 */
const syncScroll = (e: Event) => {
  if (tblKyufuSumRef.value !== null) {
    const element = tblKyufuSumRef.value.$el as HTMLDivElement
    const tableWrapper = element.querySelector('.v-table__wrapper')
    if (tableWrapper !== null) {
      const scrollLeft = (e.target as HTMLDivElement).scrollLeft
      tableWrapper.scrollLeft = scrollLeft
    }
  }
}
</script>

<template>
  <c-v-sheet class="view pr-2">
    <c-v-row
      no-gutters
      class="pa-2 sticky-header"
    >
      <!-- 給付状況 画面タイトル -->
      <c-v-col class="title-area">
        <c-v-row
          align-center
          justify-start
          no-gutters
        >
          <h1 class="pl-3">
            {{ t('label.payment-status') }}
          </h1>
          <!-- Or21828：有機体：お気に入りアイコンボタン -->
          <g-base-or21828
            v-bind="or21828"
            class="px-2"
          />
          <base-mo00040
            v-model="localOneway.selectedPath"
            class="d-flex"
            style="background-color: #ffffff"
            :oneway-model-value="localOneway.mo01282Oneway"
            @update:model-value="changeSelectedPage"
          />
        </c-v-row>
      </c-v-col>
      <c-v-col cols="auto">
        <g-base-or11871
          v-bind="or11871"
          class="top-menu"
        >
          <template #customButtons>
            <!-- 予実一括ボタン -->
            <base-mo00611
              v-if="hasRegist"
              :oneway-model-value="localOneway.mo00611Oneway"
              @click="button1Click"
            />
          </template>
          <template #optionMenuItems>
            <c-v-list-item
              v-if="hasRegist"
              :title="t('btn.delete')"
              prepend-icon="delete"
              @click="button2Click"
            />
          </template>
        </g-base-or11871>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col
        cols="auto"
        class="hidden-scroll h-100 main-left pl-3 pt-4"
      >
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>

      <c-v-col class="main-right hidden-scroll h-100 pt-2 main-right">
        <c-v-row
          no-gutters
          class="top"
        >
          <c-v-col>
            <!-- 事業所 -->
            <c-v-row no-gutters>
              <c-v-col cols="auto">
                <g-base-or41179
                  v-bind="or41179"
                  class="office-dropdown"
                />
              </c-v-col>
              <!-- 処理年度 -->
              <c-v-col
                cols="auto"
                class="ml-4 mt-1"
              >
                <g-custom-or-x0165
                  :oneway-model-value="localOneway.orX0165Plan"
                  @on-click-pre-btn="mo00009LeftClick"
                  @on-click-post-btn="mo00009RightClick"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <!-- 一覧Title -->
        <c-v-row
          no-gutters
          class="mt-6"
        >
          <c-v-col cols="12">
            <!-- 給付状況一覧 -->
            <c-v-data-table
              ref="tblKyufu"
              class="table-header w-100 h-100 overflow-y-auto tbl-kyufu table-wrapper"
              hide-default-footer
              hide-no-data
              fixed-header
              hover
              :items-per-page="-1"
              :items="sortList"
            >
              <template #headers="{ columns, toggleSort, getSortIcon }">
                <tr>
                  <!-- 提供年月 -->
                  <th
                    rowspan="2"
                    style="min-width: 70px; font-weight: unset !important"
                  >
                    <div class="d-flex justify-start align-center pl-2">
                      <div>
                        {{ t('label.offer-ym').substring(0, 2) }}<br />
                        {{ t('label.offer-ym').substring(2, 4) }}
                      </div>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[0])" />
                      </template>
                    </div>
                  </th>
                  <!-- 保険者番号 -->
                  <th
                    rowspan="2"
                    style="min-width: 86px; font-weight: unset !important"
                    @click="toggleSort(columns[1])"
                  >
                    <div class="d-flex justify-start align-center pl-1">
                      <span>{{ t('label.insured-person-number') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[1])" />
                      </template>
                    </div>
                  </th>
                  <!-- 被保険者番号 -->
                  <th
                    rowspan="2"
                    style="min-width: 92px; font-weight: unset !important"
                    @click="toggleSort(columns[2])"
                  >
                    <div class="d-flex justify-start align-center pl-1">
                      <span>{{ t('label.insured-person-number-1') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[2])" />
                      </template>
                    </div>
                  </th>
                  <!-- 要介護度 -->
                  <th
                    style="min-width: 78px; font-weight: unset !important"
                    rowspan="2"
                    @click="toggleSort(columns[3])"
                  >
                    <div class="d-flex justify-start align-center pl-2">
                      <span>{{ t('label.yokai-knj') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[3])" />
                      </template>
                    </div>
                  </th>
                  <!-- 認定期間 -->
                  <th
                    style="
                      min-width: 147px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    colspan="2"
                  >
                    {{ t('label.certification-eriod') }}
                  </th>
                  <!-- 申請中 -->
                  <th
                    style="min-width: 38px; font-weight: unset !important"
                    rowspan="2"
                    @click="toggleSort(columns[6])"
                  >
                    <div class="d-flex justify-start align-center pl-1">
                      <div>
                        <span>{{ t('label.applying').substring(0, 2) }}</span>
                        <br />
                        <span>{{ t('label.lebel-middle') }}</span>
                      </div>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[6])" />
                      </template>
                    </div>
                  </th>
                  <!-- 保険変更 -->
                  <th
                    style="min-width: 38px; font-weight: unset !important"
                    rowspan="2"
                    @click="toggleSort(columns[7])"
                  >
                    <div class="d-flex justify-start align-center pl-1">
                      <span>{{ t('label.plan-insurance') }}<br />{{ t('label.change') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[7])" />
                      </template>
                    </div>
                  </th>
                  <!-- 利用票 -->
                  <th
                    class="text-center"
                    style="min-width: 57px; font-weight: unset !important"
                    rowspan="2"
                  ></th>
                  <!-- 支給限度基準額 -->
                  <th
                    style="min-width: 64px; font-weight: unset !important"
                    rowspan="2"
                    @click="toggleSort(columns[8])"
                  >
                    <div class="d-flex justify-start align-center pl-1">
                      <span>
                        {{ t('label.payment-limit') }}<br />
                        {{ t('label.benchmark-amount') }}
                      </span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[8])" />
                      </template>
                    </div>
                  </th>
                  <!-- 合計単位数 -->
                  <th
                    colspan="3"
                    style="
                      min-width: 178px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                  >
                    {{ t('label.total-unit-number') }}
                  </th>
                  <!-- 最終計算 -->
                  <th
                    style="min-width: 38px; font-weight: unset !important"
                    rowspan="2"
                    @click="toggleSort(columns[12])"
                  >
                    <div class="d-flex justify-center align-center">
                      <div class="text-center">
                        <span>{{ t('label.final-calculation').substring(0, 2) }}</span>
                        <br />
                        <span>{{ t('label.final-calculation').substring(2, 4) }}</span>
                      </div>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[12])" />
                      </template>
                    </div>
                  </th>
                  <!-- 支給限度 -->
                  <th
                    colspan="2"
                    style="
                      min-width: 119px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                  >
                    {{ t('label.payment-limit') }}
                  </th>
                  <!-- 利用者負担額 -->
                  <th
                    colspan="2"
                    style="
                      min-width: 119px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                  >
                    {{ t('label.user-burden-amount') }}
                  </th>
                  <!-- 短期入所利用日数 -->
                  <th
                    colspan="3"
                    style="
                      min-width: 130px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                  >
                    {{ t('label.short-termadmission-using-day') }}
                  </th>
                  <!-- 実績済 -->
                  <th
                    style="min-width: 38px; font-weight: unset !important"
                    rowspan="2"
                    @click="toggleSort(columns[20])"
                  >
                    <div class="d-flex justify-start align-center pl-1">
                      <div>
                        <span>{{ t('label.achievements') }}</span>
                        <br />
                        <span>{{ t('label.input-completed3') }}</span>
                      </div>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[20])" />
                      </template>
                    </div>
                  </th>
                  <!-- 利用票印刷 -->
                  <th
                    style="min-width: 92px; font-weight: unset !important"
                    rowspan="2"
                    @click="toggleSort(columns[21])"
                  >
                    <div class="d-flex justify-start align-center pl-2">
                      <span>{{ t('label.using-ticket-printing') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[21])" />
                      </template>
                    </div>
                  </th>
                  <!-- 提供票印刷 -->
                  <th
                    rowspan="2"
                    style="min-width: 92px; font-weight: unset !important"
                    @click="toggleSort(columns[22])"
                  >
                    <div class="d-flex justify-start align-center pl-2">
                      <span>{{ t('label.provide-ticket-printing') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[22])" />
                      </template>
                    </div>
                  </th>
                  <!-- メッセージ -->
                  <th
                    style="
                      min-width: 232px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    rowspan="2"
                    @click="toggleSort(columns[23])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.message') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[23])" />
                      </template>
                    </div>
                  </th>
                </tr>
                <tr>
                  <!-- 開始日 -->
                  <th
                    style="
                      min-width: 92px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[4])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span> {{ t('label.start-date') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[4])" />
                      </template>
                    </div>
                  </th>
                  <!-- 終了日 -->
                  <th
                    style="
                      min-width: 92px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[5])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.end-date') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[5])" />
                      </template>
                    </div>
                  </th>
                  <!-- 予定 -->
                  <th
                    style="
                      min-width: 60px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[9])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.schedule') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[9])" />
                      </template>
                    </div>
                  </th>
                  <!-- 実績 -->
                  <th
                    style="
                      min-width: 60px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[10])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.achievements') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[10])" />
                      </template>
                    </div>
                  </th>
                  <!-- 差分 -->
                  <th
                    style="
                      min-width: 60px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[11])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.finite-difference') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[11])" />
                      </template>
                    </div>
                  </th>
                  <!-- 超過 -->
                  <th
                    style="
                      min-width: 60px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[13])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.exceeded') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[13])" />
                      </template>
                    </div>
                  </th>
                  <!-- 基準内 -->
                  <th
                    style="
                      min-width: 60px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[14])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.criterion') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[14])" />
                      </template>
                    </div>
                  </th>
                  <!-- 保険分 -->
                  <th
                    style="
                      min-width: 60px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[15])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.insurance') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[15])" />
                      </template>
                    </div>
                  </th>
                  <!-- 全額分 -->
                  <th
                    style="
                      min-width: 60px;
                      padding-left: 8px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[16])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.full-amount') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[16])" />
                      </template>
                    </div>
                  </th>
                  <!-- 前月 -->
                  <th
                    style="
                      min-width: 45px;
                      padding-left: 4px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[17])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.last-mouth') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[17])" />
                      </template>
                    </div>
                  </th>
                  <!-- 当月 -->
                  <th
                    style="
                      min-width: 45px;
                      padding-left: 4px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[18])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.current-month') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[18])" />
                      </template>
                    </div>
                  </th>
                  <!-- 累計 -->
                  <th
                    style="
                      min-width: 45px;
                      border-right: none !important;
                      padding-left: 4px !important;
                      font-weight: unset !important;
                    "
                    @click="toggleSort(columns[19])"
                  >
                    <div class="d-flex justify-start align-center">
                      <span>{{ t('label.cumulative') }}</span>
                      <template v-if="false">
                        <base-at-icon :icon="getSortIcon(columns[19])" />
                      </template>
                    </div>
                  </th>
                </tr>
              </template>
              <template #item="{ item, index }">
                <tr
                  :class="{
                    'row-background-color1': item.kaigoDifFlg === '1',
                    'row-background-color2': item.kaigoDifFlg !== '1',
                    'select-row': localOneway.selectRowIndex === index,
                  }"
                  @click="selectRow(index)"
                >
                  <td style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important">
                    {{ item.yymmYm }}
                  </td>
                  <td style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important">
                    {{ item.khokenCd }}
                  </td>
                  <td style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important">
                    {{ item.hhokenNo }}
                  </td>
                  <td style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important">
                    {{ item.yokaiKnj }}
                  </td>
                  <td style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important">
                    {{ item.startYmd }}
                  </td>
                  <td style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important">
                    {{ item.endYmd }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important"
                    class="text-center"
                  >
                    {{ item.shinsei }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important"
                    class="text-center"
                  >
                    {{ item.hokenHenko }}
                  </td>
                  <td style="padding: 0 !important; border-left: 1px solid #d6d6d6 !important">
                    <base-mo01266
                      :oneway-model-value="localOneway.mo01266Oneway"
                      style="height: 100%"
                      @click="usingSlipClick(index)"
                    />
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ item.gendoGaku }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ item.svTensuY }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px dashed #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ item.svTensuJ }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px dashed #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ item.sabun }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important"
                    class="text-center"
                  >
                    {{ item.saishuuCalc }}
                  </td>
                  <td
                    class="font-color-red text-right"
                    style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important"
                  >
                    {{ item.dmyOver }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px dashed #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ item.dmyKijun }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ item.hhknHutan }}
                  </td>
                  <td
                    class="font-color-red text-right"
                    style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important"
                  >
                    {{ item.hjihHutan }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ item.zengetu }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px dashed #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ item.tougetu }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px dashed #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ item.ruikei }}
                  </td>
                  <td
                    style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important"
                    class="text-center"
                  >
                    {{ item.yoteiZumi }}
                  </td>
                  <td style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important">
                    {{ item.date1Ymd }}
                  </td>
                  <td style="padding: 0 8px !important; border-left: 1px solid #d6d6d6 !important">
                    {{ item.date2Ymd }}
                  </td>
                  <td
                    style="
                      padding: 0 8px !important;
                      border-left: 1px solid #d6d6d6 !important;
                      border-right: 1px solid #d6d6d6 !important;
                    "
                  >
                    <label class="pl-2px show-lines-2"
                      >{{ item.msg }}
                      <c-v-tooltip
                        activator="parent"
                        location="bottom"
                        :text="item.msg"
                      ></c-v-tooltip
                    ></label>
                  </td>
                </tr>
              </template>
            </c-v-data-table>
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          class="d-flex"
          style="margin-top: 426px"
        >
          <c-v-col class="total-row d-flex">
            <c-v-data-table
              ref="tblKyufuSum"
              class="tbl-kyufu-sum"
              style="background-color: transparent"
              fixed-header
              hide-default-footer
              :items-per-page="-1"
              hide-no-data
            >
              <template #body>
                <tr class="total-tr">
                  <td
                    colspan="2"
                    style="min-width: 156px"
                  >
                    {{ t('label.items-number-total', [localOneway.sum]) }}
                  </td>
                  <td style="min-width: 92px"></td>
                  <td style="min-width: 78px"></td>
                  <td style="min-width: 92px"></td>
                  <td style="min-width: 92px"></td>
                  <td style="min-width: 38px"></td>
                  <td style="min-width: 38px"></td>
                  <td style="min-width: 57px"></td>
                  <td style="min-width: 64px"></td>
                  <td
                    style="min-width: 60px"
                    class="text-right"
                  >
                    {{ addThousandSeparator(String(localOneway.svTensuYSum)) }}
                  </td>
                  <td
                    style="min-width: 60px; border-left: 1px dashed #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ addThousandSeparator(String(localOneway.svTensuJSum)) }}
                  </td>
                  <td
                    style="min-width: 60px; border-left: 1px dashed #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ addThousandSeparator(String(localOneway.sabunSum)) }}
                  </td>
                  <td style="min-width: 38px"></td>
                  <td
                    class="font-color-red text-right"
                    style="min-width: 60px"
                  >
                    {{ addThousandSeparator(String(localOneway.dmyOverSum)) }}
                  </td>
                  <td
                    style="min-width: 60px; border-left: 1px dashed #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ addThousandSeparator(String(localOneway.dmyKijunSum)) }}
                  </td>
                  <td
                    style="min-width: 60px"
                    class="text-right"
                  >
                    {{ addThousandSeparator(String(localOneway.hHknHutanSum)) }}
                  </td>
                  <td
                    class="font-color-red text-right"
                    style="min-width: 60px"
                  >
                    {{ addThousandSeparator(String(localOneway.hJihHutanSum)) }}
                  </td>
                  <td
                    style="min-width: 45px"
                    class="text-right"
                  >
                    {{ addThousandSeparator(String(localOneway.zengetuSum)) }}
                  </td>
                  <td
                    style="min-width: 45px; border-left: 1px dashed #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ addThousandSeparator(String(localOneway.tougetuSum)) }}
                  </td>
                  <td
                    style="min-width: 45px; border-left: 1px dashed #d6d6d6 !important"
                    class="text-right"
                  >
                    {{ addThousandSeparator(String(localOneway.ruikeiSum)) }}
                  </td>
                  <td
                    style="min-width: 38px"
                    class="text-right"
                  >
                    {{ localOneway.yoteiZumiSum }}
                  </td>
                  <td style="min-width: 92px"></td>
                  <td style="min-width: 92px"></td>
                  <td style="min-width: 232px; border-right: 1px solid #d6d6d6"></td>
                </tr>
              </template>
            </c-v-data-table>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="footer"
    >
      <c-v-col>
        <g-base-or00051 />
      </c-v-col>
    </c-v-row>
    <!-- メッセージ -->
    <g-base-or-21814
      v-if="showDialog21814"
      v-bind="or21814"
    />
    <g-custom-or-33787
      v-if="showDialogOr33787"
      v-bind="or33787"
      v-model="or33787Type"
    />
    <!-- 利用票 -->
    <g-custom-or-36653
      v-if="showDialogOr36653"
      v-bind="or36653"
      :oneway-model-value="localOneway.or36653Oneway"
    />
    <!-- 利用票 予実一括設定 -->
    <g-custom-or-53953
      v-if="showDialogOr53953"
      v-bind="or53953"
      :oneway-model-value="localOneway.or53953OneWay"
    />
  </c-v-sheet>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table-list.scss';
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

:deep(.v-sheet) {
  background-color: transparent !important;
}

:deep(.top-menu) {
  padding: 0 !important;
  height: 36px;
  align-items: center;
}

.list-div .v-row {
  margin: 0px !important;
}

.list-row .v-col {
  margin: 0px !important;
  padding: 0px !important;
}

.row-tl-border {
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.list-title-row .v-col {
  background-color: #dbeefe;
}

.col-br-border {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-b-border-r-transparent {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px transparent solid;
}
.col-r-border-b-transparent {
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
  border-bottom: 1px transparent solid;
}

.col-br-border-transparent {
  border-bottom: 1px transparent solid;
  border-right: 1px transparent solid;
}

.col-width-80 {
  max-width: 80px !important;
  min-width: 80px !important;
}
.col-width-90 {
  max-width: 90px !important;
  min-width: 90px !important;
}
.col-width-100 {
  max-width: 100px !important;
  min-width: 100px !important;
}
.col-width-120 {
  max-width: 120px !important;
  min-width: 120px !important;
}
.col-width-160 {
  max-width: 160px !important;
  min-width: 160px !important;
}

.col-width-200 {
  max-width: 200px !important;
  min-width: 200px !important;
}
.col-width-240 {
  max-width: 240px !important;
  min-width: 240px !important;
}

.label-justify-content-center {
  justify-content: center;
}

.label-justify-content-right {
  justify-content: right;
}

.label-align-items-center {
  display: flex;
  align-items: center;
}

.row-background-color1 {
  background-color: #ffa9b4;
}
.row-background-color2 {
  background-color: #ffffff;
}
.select-row {
  background-color: #96c8ff;
}

.list-body-row {
  max-height: 512px;
  overflow-x: auto;
  scrollbar-gutter: stable;
  cursor: pointer;
  label {
    cursor: pointer;
  }
}

.list-sum-row {
  height: 32px;
  overflow-x: auto;
  scrollbar-gutter: stable;
}

.pl-2px {
  padding-left: 2px;
}
.pr-2px {
  padding-right: 2px;
}

.font-color-red {
  color: #ff0000;
}

.show-lines-2 {
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
  box-orient: vertical;
  -webkit-line-clamp: 2;
  -moz-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

:deep(.processing_year .item-label) {
  margin: 4px !important;
}
:deep(.mo00009Btn) {
  margin-top: 2px !important;
}
.sort_icon {
  margin-top: -2px;
  font-size: 20px;
}

.pointer {
  cursor: pointer;
  label {
    cursor: pointer;
  }
}

.td-height-32 {
  height: 32px;
}

.office-dropdown {
  display: block;
}

.office-dropdown :deep(.v-col:first-child) {
  margin-top: 0 !important;
}

.office-dropdown :deep(.v-col:last-child > .v-sheet) {
  background-color: #ffffff !important;
}

.tbl-kyufu {
  min-height: 219px;
}

.tbl-kyufu :deep(.v-table__wrapper > table > thead > tr > th) {
  background-color: #dbeefe !important;
  padding: 0 !important;
  height: 30px;
}

.tbl-kyufu :deep(.v-table__wrapper > table > tbody > tr > td) {
  height: 36px !important;
}

:deep(.table-wrapper .v-table__wrapper th) {
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.total-tr {
  background-color: #e6e6e6;
}

.total-tr td {
  border: 1px solid #d6d6d6;
  border-right: none;
  padding: 0 8px !important;
  height: 32px !important;
  font-size: 14px;
}

:deep(.top-menu .v-divider:nth-child(3)) {
  display: none;
}

.sticky-header {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
}

.content-area {
  background: rgb(var(--v-theme-background));
}

:deep(.copyright) {
  background-color: rgb(var(--v-theme-background)) !important;
}

.tbl-kyufu-sum :deep(.v-table__wrapper) {
  overflow: hidden;
}
</style>
