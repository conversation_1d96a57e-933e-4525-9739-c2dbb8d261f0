import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 薬剤検索マスタ入力エンティティ
 */
export interface IMedicationSearchMasterInEntity extends InWebEntity {
  /**
   * 職員ID
   */
  staffId: string
}

/**
 * 薬剤検索マスタ出力エンティティ
 */
export interface IMedicationSearchMasterOutEntity extends OutWebEntity {
  /**
   * data
   */
  data: {
    initialSetupMasterInfoList: {
      /**  連番 */
      recNo: string
      /** 最大薬剤階層 */
      maxDrugLevel: string
      /** 最大文章階層 */
      maxBunshoLevel: string
      /** 食事タイプ */
      shokujiKbn: string
    }[]
    drugClassificationMasterInfoList: {
      /**
       * 階層
       */
      level: string
      /**
       * 親分類id
       */
      parentId: string
      /**
       * 薬剤分類id
       */
      bunruiId: string
      /**
       * 薬剤分類名
       */
      drugBunKnj?: string
      /**
       * 表示順
       */
      sort: string
    }[]
    drugMasterInfoList: {
      /**
       * 階層
       */
      level: string
      /**
       * 親分類id
       */
      parentId: string
      /**
       * 分類コード
       */
      bunruiId: string
      /**
       * 薬剤id
       */
      drugId: string
      /**
       * 医薬品名
       */
      drugKnj?: string
      /**
       * 医薬品カナ
       */
      drugKana?: string
      /**
       * 医薬品コード
       */
      drugCode?: string
      /**
       * 医薬品Rezeコード
       */
      drugReceCode?: string
      /**
       * 剤型
       */
      shape?: string
      /**
       * 作用・効能等
       */
      memoKnj?: string
      /**
       * 表示順
       */
      sort: string
    }[]
  }
}
