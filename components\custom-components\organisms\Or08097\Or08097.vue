<script setup lang="ts">
/**
 * Or08097: 介護予防に関する事項
 * GUI01068_介護予防に関する事項
 *
 * @description
 * 介護予防に関する事項
 *
 * <AUTHOR> LE VAN CUONG
 */
import { reactive, ref, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or08097Const } from './Or08097.constants'
import { useSetupChildProps, useSystemCommonsStore } from '#imports'

import type { OrX0156OnewayType, OrX0156Type } from '~/types/cmn/business/components/OrX0156Type'
import { CustomClass } from '~/types/CustomClassType'
import { Or00051Const } from '~/components/base-components/organisms/Or00051/Or00051.constants'
import type { Or08097OnewayType } from '~/types/cmn/business/components/Or08097Type'

import type {
  Or28483OnewayType,
  dailyScheduleItem,
  Or28483Type,
} from '~/types/cmn/business/components/Or28483Type'
import { Or28483Const } from '~/components/custom-components/organisms/Or28483/Or28483.constants'
import { Or08097Logic } from '~/components/custom-components/organisms/Or08097/Or08097.logic'
import { Or10883Logic } from '~/components/custom-components/organisms/Or10883/Or10883.logic'
import { Or10883Const } from '~/components/custom-components/organisms/Or10883/Or10883.constants'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'
import { useCmnCom } from '~/utils/useCmnCom'

/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

// 共通関数を取得
const { isOverLimitByCharWidth } = useCmnCom()

// 文字数制限チェック関数
const checkCharLimit = (value: string, maxLength: number): boolean => {
  if (maxLength === Or08097Const.DEFAULT.ZERO) return true
  return !isOverLimitByCharWidth(value, maxLength)
}

// 前の値を保存するための変数
const previousValues = ref({
  untilNowLife: '',
  hobbyPleasureSpecialSkill: '',
  friendCommunityRelation: '',
  dailyLifeHowToSpendYourTime: ''
})

// 文字数制限付きの値更新処理
const updateValueWithLimit = async (field: keyof typeof previousValues.value, newValue: string) => {
  const maxLength = Or08097Const.DEFAULT.FOUR_THOUSAND

  if (newValue && !checkCharLimit(newValue, maxLength)) {
    // 制限を超えた場合は、前の値に戻す
    await nextTick()
    if (field === Or08097Const.DEFAULT.UNTIL_NOW_LIFE) {
      local.untilNowLife.value = previousValues.value.untilNowLife
    } else if (field === Or08097Const.DEFAULT.HOBBY_PLEASURE_SPECIAL_SKILL) {
      local.hobbyPleasureSpecialSkill.value = previousValues.value.hobbyPleasureSpecialSkill
    } else if (field === Or08097Const.DEFAULT.FRIEND_COMMUNITY_RELATION) {
      local.friendCommunityRelation.value = previousValues.value.friendCommunityRelation
    } else if (field === Or08097Const.DEFAULT.DAILY_LIFE_HOW_TO_SPEND_YOUR_TIME) {
      local.dailyLifeHowToSpendYourTime.value = previousValues.value.dailyLifeHowToSpendYourTime
    }
    return false
  }

  // 制限内の場合は、前の値を更新してから新しい値を設定
  if (field === Or08097Const.DEFAULT.UNTIL_NOW_LIFE) {
    previousValues.value.untilNowLife = newValue
    local.untilNowLife.value = newValue
  } else if (field === Or08097Const.DEFAULT.HOBBY_PLEASURE_SPECIAL_SKILL) {
    previousValues.value.hobbyPleasureSpecialSkill = newValue
    local.hobbyPleasureSpecialSkill.value = newValue
  } else if (field === Or08097Const.DEFAULT.FRIEND_COMMUNITY_RELATION) {
    previousValues.value.friendCommunityRelation = newValue
    local.friendCommunityRelation.value = newValue
  } else if (field === Or08097Const.DEFAULT.DAILY_LIFE_HOW_TO_SPEND_YOUR_TIME) {
    previousValues.value.dailyLifeHowToSpendYourTime = newValue
    local.dailyLifeHowToSpendYourTime.value = newValue
  }
  return true
}

/**
 * I18n
 */
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue?: Or08097OnewayType
  uniqueCpId: string
}
/**************************************************
 * 変数定義
 **************************************************/
/**
 * 引継情報を取得する
 */
const props = defineProps<Props>()

/**
 * 双方向バインド用の内部変数
 */
const local = reactive({
  isAllowEdit: props.onewayModelValue?.isAllowEdit ?? true,
  untilNowLife: {
    value: props.onewayModelValue?.untilNowLife,
  } as OrX0156Type,
  hobbyPleasureSpecialSkill: {
    value: props.onewayModelValue?.hobbyPleasureSpecialSkill,
  } as OrX0156Type,
  friendCommunityRelation: {
    value: props.onewayModelValue?.friendCommunityRelation,
  } as OrX0156Type,
  dailyLifeHowToSpendYourTime: {
    value: props.onewayModelValue?.dailyLifeHowToSpendYourTime,
  } as OrX0156Type,
  dailySchedule: [] as dailyScheduleItem[],
  or28483: {} as Or28483Type,
})

/**
 * 片方向バインド用の内部変数
 */
const localOneway = reactive({
  // OrX0156: 今までの生活 用の片方向バインド
  untilNowLifeX0156Oneway: {
    itemLabel: t('label.until-now-life'),
    showItemLabel: true,
    isVerticalLabel: true,
    noResize: true,
    autoGrow: false,
    maxlength: '4000',
    class: 'A12BTextArea',
    rows: 4,
    maxRows: '4',
    disabled: !(props.onewayModelValue?.isAllowEdit ?? true),
    showEditBtnFlg: props.onewayModelValue?.isAllowEdit ?? true,
    showDividerLineFlg: false,
    mo00009: { btnIcon: 'edit_square', color: 'rgb(var(--v-theme-light-blue-400))', variant: 'text', width: '32px', height: '32px', style: 'background: #EBF2FD;' },
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent',
      labelClass: 'title-bar',
      labelStyle:
        'background: rgb(var(--v-theme-black-100)); width: 100%; padding: 6px 12px 4px 12px; border-radius: 4px; min-height: 48px; text-indent: 10px;',
      itemStyle:
        'background: rgb(var(--v-theme-secondaryBackground)); padding: 28px 36px 32px 36px; border-radius: 4px;'
    }),
  } as OrX0156OnewayType,
  // OrX0156: 趣味・楽しみ・特技 用の片方向バインド
  hobbyPleasureSpecialSkillX0156Oneway: {
    itemLabel: t('label.hobby-pleasure-special-skill'),
    showItemLabel: true,
    isVerticalLabel: true,
    noResize: true,
    autoGrow: false,
    maxlength: '4000',
    class: 'A12BTextArea',
    rows: 4,
    maxRows: '4',
    disabled: !(props.onewayModelValue?.isAllowEdit ?? true),
    showEditBtnFlg: props.onewayModelValue?.isAllowEdit ?? true,
    showDividerLineFlg: false,
    mo00009: { btnIcon: 'edit_square', color: 'rgb(var(--v-theme-light-blue-400))', variant: 'text', width: '32px', height: '32px', style: 'background: #EBF2FD;' },
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent',
      labelClass: 'title-bar',
      labelStyle:
        'background: rgb(var(--v-theme-black-100)); width: 100%; padding: 6px 12px 4px 12px; border-radius: 4px; min-height: 48px; text-indent: 10px;',
      itemStyle:
        'background: rgb(var(--v-theme-secondaryBackground)); padding: 28px 36px 32px 36px; border-radius: 4px;'
    }),
  } as OrX0156OnewayType,
  // OrX0156: 友人・地域との関係 用の片方向バインド
  friendCommunityRelationX0156Oneway: {
    itemLabel: t('label.friend-community-relation'),
    showItemLabel: true,
    isVerticalLabel: true,
    noResize: true,
    autoGrow: false,
    maxlength: '4000',
    class: 'A12BTextArea',
    rows: 4,
    maxRows: '4',
    disabled: !(props.onewayModelValue?.isAllowEdit ?? true),
    showEditBtnFlg: props.onewayModelValue?.isAllowEdit ?? true,
    showDividerLineFlg: false,
    mo00009: { btnIcon: 'edit_square', color: 'rgb(var(--v-theme-light-blue-400))', variant: 'text', width: '32px', height: '32px', style: 'background: #EBF2FD;' },
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent',
      labelClass: 'title-bar',
      labelStyle:
        'background: rgb(var(--v-theme-black-100)); width: 100%; padding: 6px 12px 4px 12px; border-radius: 4px; min-height: 48px; text-indent: 10px;',
      itemStyle:
        'background: rgb(var(--v-theme-secondaryBackground)); padding: 28px 36px 32px 36px; border-radius: 4px;'
    }),
  } as OrX0156OnewayType,
  // OrX0156: 1日の生活・すごし方 用の片方向バインド
  dailyLifeHowToSpendYourTimeX0156Oneway: {
    itemLabel: t('label.daily-life-how-to-spend-your-time'),
    showItemLabel: true,
    isVerticalLabel: true,
    noResize: true,
    autoGrow: false,
    maxlength: '4000',
    class: 'A12BTextArea',
    rows: 4,
    maxRows: '4',
    disabled: !(props.onewayModelValue?.isAllowEdit ?? true),
    showEditBtnFlg: props.onewayModelValue?.isAllowEdit ?? true,
    showDividerLineFlg: false,
    mo00009: { btnIcon: 'edit_square', color: 'rgb(var(--v-theme-light-blue-400))', variant: 'text', width: '32px', height: '32px', style: 'background: #EBF2FD;' },
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent',
      labelClass: 'title-bar',
      labelStyle:
        'background: rgb(var(--v-theme-black-100)); width: 100%; padding: 6px 12px 4px 12px; border-radius: 4px; min-height: 48px; text-indent: 10px;',
      itemStyle:
        'background: rgb(var(--v-theme-secondaryBackground)); padding: 28px 36px 32px 36px; border-radius: 4px;'
    }),
  } as OrX0156OnewayType,
  // 1日のスケジュール
  dailyScheduleX0156Oneway: {
    itemLabel: t('label.daily-schedule'),
    showItemLabel: true,
    isVerticalLabel: true,
    noResize: true,
    autoGrow: false,
    readonly: true,
    disabled: true,
    showEditBtnFlg: false,
    showDividerLineFlg: false,
    customClass: new CustomClass({
      outerStyle: 'background-color: transparent',
      labelClass: 'title-bar',
      labelStyle:
        'background: rgb(var(--v-theme-black-100)); width: 100%; padding: 6px 12px 4px 12px; border-radius: 4px; min-height: 48px; text-indent: 10px;',
      itemStyle: 'display: none;'
    }),
  } as OrX0156OnewayType,

})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * or00051
 */
const or00051 = ref({ uniqueCpId: '' })
/**
 * or28483
 */
const or28483 = ref({ uniqueCpId: '' })
/**
 * or10883
 */
const or10883 = ref({ uniqueCpId: '' })

/**
 * or10883Type
 */
const or10883Type = ref<Or10883TwowayType>({})

/**
 * Or28483のonewayモデル
 */
const or28483OnewayModel: Or28483OnewayType = {
  basicInfoId: '123',
  screenId: Or28483Const.SCREEN_GUI01068,
  isAllowEdit: local.isAllowEdit,
}


/**
 * Or10883 ダイアログを表示するかどうかを決定する計算プロパティ。
 */
const isShowDialogOr10883 = computed(() => {
  return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 現在選択されている項目を示す変数
 */
let itemSelected = 0

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00051Const.CP_ID]: or00051.value,
  [Or28483Const.CP_ID(0)]: or28483.value,
  [Or10883Const.CP_ID(0)]: or10883.value,
})

/**
 * Or10883のonewayモデルを初期化するための変数。
 */
const or10883OnewayModel: Or10883OnewayType = {
  userId: '',
  t1Cd: '',
  t2Cd: '',
  t3Cd: '',
  inputContents: '',
  title: '',
  historyTableName: '',
  historyColumnName: '',
}
/**
 * 「今までの生活アイコンボタン」押下
 */
function onUntilNowLifeClick() {
  itemSelected = 1
  or10883OnewayModel.userId = systemCommonsStore.getUserId ?? ''
  or10883OnewayModel.t1Cd = '20'
  or10883OnewayModel.t2Cd = '2'
  or10883OnewayModel.t3Cd = '1'
  or10883OnewayModel.title = t('label.until-now-life')
  or10883OnewayModel.tableName = 'kyc_tuc_khn13'
  or10883OnewayModel.columnName = 'imamade_knj'
  or10883OnewayModel.inputContents = local.untilNowLife.value ?? ''
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「趣味・楽しみ・特技アイコンボタン」押下
 */
function onHobbyPleasureSpecialSkillClick() {
  itemSelected = 2
  or10883OnewayModel.userId = systemCommonsStore.getUserId ?? ''
  or10883OnewayModel.t1Cd = '20'
  or10883OnewayModel.t2Cd = '2'
  or10883OnewayModel.t3Cd = '2'
  or10883OnewayModel.title = t('label.hobby-pleasure-special-skill')
  or10883OnewayModel.tableName = 'kyc_tuc_khn13'
  or10883OnewayModel.columnName = 'shumi_knj'
  or10883OnewayModel.inputContents = local.hobbyPleasureSpecialSkill.value ?? ''
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「友人・地域との関係アイコンボタン」押下
 */
function onFriendCommunityRelationClick() {
  itemSelected = 3
  or10883OnewayModel.userId = systemCommonsStore.getUserId ?? ''
  or10883OnewayModel.t1Cd = '20'
  or10883OnewayModel.t2Cd = '2'
  or10883OnewayModel.t3Cd = '3'
  or10883OnewayModel.title = t('label.friend-community-relation')
  or10883OnewayModel.tableName = 'kyc_tuc_khn13'
  or10883OnewayModel.columnName = 'kankei_knj'
  or10883OnewayModel.inputContents = local.friendCommunityRelation.value ?? ''
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *「1日の生活・すごし方アイコンボタン」押下
 */
function onDailyLifeHowToSpendYourTimeClick() {
  itemSelected = 4
  or10883OnewayModel.userId = systemCommonsStore.getUserId ?? ''
  or10883OnewayModel.t1Cd = '20'
  or10883OnewayModel.t2Cd = '2'
  or10883OnewayModel.t3Cd = '4'
  or10883OnewayModel.title = t('label.daily-life-how-to-spend-your-time')
  or10883OnewayModel.tableName = 'kyc_tuc_khn13'
  or10883OnewayModel.columnName = 'sugosikatai_knj'
  or10883OnewayModel.inputContents = local.friendCommunityRelation.value ?? ''
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}



/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => local.untilNowLife.value,
  async (newValue) => {
    if (newValue && !(await updateValueWithLimit('untilNowLife', newValue))) {
      return
    }
    Or08097Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        untilNowLife: newValue ?? '',
        hobbyPleasureSpecialSkill: local.hobbyPleasureSpecialSkill.value ?? '',
        friendCommunityRelation: local.friendCommunityRelation.value ?? '',
        dailyLifeHowToSpendYourTime: local.dailyLifeHowToSpendYourTime.value ?? '',
        dailySchedule: local.dailySchedule ?? [],
      },
    })
  }
)

watch(
  () => local.hobbyPleasureSpecialSkill.value,
  async (newValue) => {
    if (newValue && !(await updateValueWithLimit('hobbyPleasureSpecialSkill', newValue))) {
      return
    }
    Or08097Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        untilNowLife: local.untilNowLife.value ?? '',
        hobbyPleasureSpecialSkill: newValue ?? '',
        friendCommunityRelation: local.friendCommunityRelation.value ?? '',
        dailyLifeHowToSpendYourTime: local.dailyLifeHowToSpendYourTime.value ?? '',
        dailySchedule: local.dailySchedule ?? [],
      },
    })
  }
)

watch(
  () => local.friendCommunityRelation.value,
  async (newValue) => {
    if (newValue && !(await updateValueWithLimit('friendCommunityRelation', newValue))) {
      return
    }
    Or08097Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        untilNowLife: local.untilNowLife.value ?? '',
        hobbyPleasureSpecialSkill: local.hobbyPleasureSpecialSkill.value ?? '',
        friendCommunityRelation: newValue ?? '',
        dailyLifeHowToSpendYourTime: local.dailyLifeHowToSpendYourTime.value ?? '',
        dailySchedule: local.dailySchedule ?? [],
      },
    })
  }
)

watch(
  () => local.dailyLifeHowToSpendYourTime.value,
  async (newValue) => {
    if (newValue && !(await updateValueWithLimit('dailyLifeHowToSpendYourTime', newValue))) {
      return
    }
    Or08097Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        untilNowLife: local.untilNowLife.value ?? '',
        hobbyPleasureSpecialSkill: local.hobbyPleasureSpecialSkill.value ?? '',
        friendCommunityRelation: local.friendCommunityRelation.value ?? '',
        dailyLifeHowToSpendYourTime: newValue ?? '',
        dailySchedule: local.dailySchedule ?? [],
      },
    })
  }
)

watch(
  () => local.or28483,
  (newValue) => {
    Or08097Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        untilNowLife: local.untilNowLife.value ?? '',
        hobbyPleasureSpecialSkill: local.hobbyPleasureSpecialSkill.value ?? '',
        friendCommunityRelation: local.friendCommunityRelation.value ?? '',
        dailyLifeHowToSpendYourTime: local.dailyLifeHowToSpendYourTime.value ?? '',
        dailySchedule: newValue.getDailyScheduleList ?? [],
      },
    })
  },
  { deep: true }
)

watch(
  () => or10883Type.value,
  () => {
    const naiyo = or10883Type.value.naiyo ?? ''
    switch (itemSelected) {
      case 1: {
        local.untilNowLife.value = naiyo
        break
      }
      case 2: {
        local.hobbyPleasureSpecialSkill.value = naiyo
        break
      }
      case 3: {
        local.friendCommunityRelation.value = naiyo
        break
      }
      case 4: {
        local.dailyLifeHowToSpendYourTime.value = naiyo
        break
      }
    }
    itemSelected = 0
    Or10883Logic.state.set({
      uniqueCpId: or10883.value.uniqueCpId,
      state: { isOpen: false },
    })
  }
)

/**
 * Or08097Logic.dataの変更を監視してlocal変数を更新
 */
watch(
  () => Or08097Logic.data.get(props.uniqueCpId),
  (newValue) => {
    if (newValue) {
      local.untilNowLife.value = newValue.untilNowLife ?? ''
      local.hobbyPleasureSpecialSkill.value = newValue.hobbyPleasureSpecialSkill ?? ''
      local.friendCommunityRelation.value = newValue.friendCommunityRelation ?? ''
      local.dailyLifeHowToSpendYourTime.value = newValue.dailyLifeHowToSpendYourTime ?? ''
      local.dailySchedule = newValue.dailySchedule ?? []
    }
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <c-v-sheet
    class="pt-3 pr-2 view"
    :class="'view-w-1080'"
    style="background-color: transparent"
  >
    <c-v-row>
      <c-v-col>
        <c-v-row no-gutters>
          <!-- 今までの生活 -->
          <c-v-col
            cols="12"
            class="mb-0"
          >
            <g-custom-or-x0156
              v-model="local.untilNowLife"
              :oneway-model-value="localOneway.untilNowLifeX0156Oneway"
              class="memoInputTextArea"
              @on-click-edit-btn.stop="onUntilNowLifeClick"
            />
          </c-v-col>
          <!-- 趣味・楽しみ・特技 -->
          <c-v-col
            cols="12"
            class="mb-0"
          >
            <g-custom-or-x0156
              v-model="local.hobbyPleasureSpecialSkill"
              :oneway-model-value="localOneway.hobbyPleasureSpecialSkillX0156Oneway"
              class="memoInputTextArea"
              @on-click-edit-btn.stop="onHobbyPleasureSpecialSkillClick"
            />
          </c-v-col>
          <!-- 友人・地域との関係 -->
          <c-v-col
            cols="12"
            class="mb-0"
          >
            <g-custom-or-x0156
              v-model="local.friendCommunityRelation"
              :oneway-model-value="localOneway.friendCommunityRelationX0156Oneway"
              class="memoInputTextArea"
              @on-click-edit-btn.stop="onFriendCommunityRelationClick"
            />
          </c-v-col>
          <!-- 1日の生活・すごし方 -->
          <c-v-col
            cols="12"
            class="mb-0"
          >
            <g-custom-or-x0156
              v-model="local.dailyLifeHowToSpendYourTime"
              :oneway-model-value="localOneway.dailyLifeHowToSpendYourTimeX0156Oneway"
              class="memoInputTextArea"
              @on-click-edit-btn.stop="onDailyLifeHowToSpendYourTimeClick"
            />
          </c-v-col>
          <!-- 1日のスケジュール（変更なし） -->
          <c-v-col
            cols="12"
            class="mb-0"
          >
            <div class="schedule-container">
              <g-custom-or-x0156
                :oneway-model-value="localOneway.dailyScheduleX0156Oneway"
                class="memoInputTextArea"
              />
              <g-custom-or-28483
                v-model="local.or28483"
                v-bind="or28483"
                :oneway-model-value="or28483OnewayModel"
              />
            </div>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <g-custom-or-10883
    v-if="isShowDialogOr10883"
    v-bind="or10883"
    v-model="or10883Type"
    :oneway-model-value="or10883OnewayModel"
  />
</template>

<style scoped lang="scss">
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.view-w-1080 {
  width: 1080px;
}
.view-w-1309 {
  width: 1309px;
}
:deep(.title-bar .item-label) {
  font-weight: bold !important;
}

.schedule-container {
  background-color: rgb(var(--v-theme-secondaryBackground));
  padding-bottom: 32px;
}
</style>
