/**
 * GUI01235_評価表
 *
 * @description
 * 評価表
 *
 * <AUTHOR>
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'
import type { IReportInEntity } from '~/types/business/core/ReportRepositoryType'
/**
 * 初期情報取得リクエストパラメータ
 */
export interface EvaluationTableInitInfoSelectInEntity extends InWebEntity {
  /**
   * ヘッダID
   */
  cmoni1Id?: string
  /**
   * マスタID
   */
  free1Id?: string
}

/**
 * 計画期間取得リクエストパラメータ
 */
export interface EvaluationTablePlanPeriodSelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 種別ID */
  syubetsuId: string
  /** 施設ID */
  shisetuId: string
  /** 期間ID */
  sc1Id: string
  /** 計画期間ページ区分 */
  planPeriodPageCategory: string
  /** 期間管理フラグ */
  kikanKanriFlg: string
  /** 処理区分 */
  processCategory: string
  /** 職員ID */
  shokuinId: string
  /** アセスメント方式 */
  cpnFlg: string
  /** 計画書書式 */
  shosikiFlg: string
  /** メニュー２名称 */
  menu2Name: string
  /** メニュー３名称 */
  menu3Name: string
  /** 自由パラメータ */
  freeParameter: string
  /** 事業者IDリスト */
  svJigyoIdList: string
  /** システムコード */
  gsyscd: string
  /** ヘーダID */
  cmoni1Id: string
}

/**
 * 初期情報取得レスポンスパラメータタイプ
 */
export interface EvaluationTableInitInfoSelectOutEntity {
  /**
   * 評価表上段項目数
   */
  columnCount1: string
  /**
   * 評価表下段項目数
   */
  columnCount2: string
  /**
   * 評価表上段様式リスト
   */
  evaluationTableUpperTierStyleInfoList: EvaluationTableUpperTierStyleInfoListType[]
  /**
   * 評価表下段様式リスト
   */
  evaluationTableLowerTierStyleInfoList: EvaluationTableLowerTierStyleInfoListType[]
  /**
   * 評価表上段情報リスト
   */
  evaluationTableUpperTierInfoList: EvaluationTableUpperTierInfoListType[]
  /**
   * 評価表下段情報
   */
  evaluationTableLowerTierInfo: EvaluationTableLowerTierInfoType
}

/**
 * 評価表上段様式アイテムタイプ
 */
export interface EvaluationTableUpperTierStyleInfoListType {
  /**
   * 区分ID
   */
  kbnId: string
  /**
   * 項目ID
   */
  koumokuId: string
  /**
   * 項目名
   */
  nameKnj: string
  /**
   * 入力方法
   */
  inputKbn: string
  /**
   * 連動区分
   */
  rendouKbn: string
  /**
   * 文字数
   */
  widthCnt: string
}

/**
 * 評価表下段様式リスト
 */
export interface EvaluationTableLowerTierStyleInfoListType {
  /**
   * 区分ID
   */
  kbnId: string
  /**
   * 項目ID
   */
  koumokuId: string
  /**
   * 項目名
   */
  nameKnj: string
  /**
   * 入力方法
   */
  inputKbn: string
  /**
   * 連動区分
   */
  rendouKbn: string
  /**
   * 文字数
   */
  widthCnt: string
}

/**
 * 評価表上段情報リスト
 */
export interface EvaluationTableUpperTierInfoListType {
  /**
   * インデックス
   */
  [key: string]: string | undefined
  /**
   * ヘーダID
   */
  cmoni1Id?: string
  /**
   * データID
   */
  cmoni3Id?: string
  /**
   * 文章1
   */
  koumoku01Knj?: string
  /**
   * 文章2
   */
  koumoku02Knj?: string
  /**
   * 文章3
   */
  koumoku03Knj?: string
  /**
   * 文章4
   */
  koumoku04Knj?: string
  /**
   * 文章5
   */
  koumoku05Knj?: string
  /**
   * 文章6
   */
  koumoku06Knj?: string
  /**
   * 文章7
   */
  koumoku07Knj?: string
  /**
   * 文章8
   */
  koumoku08Knj?: string
  /**
   * 文章9
   */
  koumoku09Knj?: string
  /**
   * 文章10
   */
  koumoku10Knj?: string
  /**
   * 文章11
   */
  koumoku11Knj?: string
  /**
   * 文章12
   */
  koumoku12Knj?: string
  /**
   * 文章13
   */
  koumoku13Knj?: string
  /**
   * 文章14
   */
  koumoku14Knj?: string
  /**
   * 文章15
   */
  koumoku15Knj?: string
  /**
   * コード1
   */
  koumoku01Cod?: string
  /**
   * コード2
   */
  koumoku02Cod?: string
  /**
   * コード3
   */
  koumoku03Cod?: string
  /**
   * コード4
   */
  koumoku04Cod?: string
  /**
   * コード5
   */
  koumoku05Cod?: string
  /**
   * コード6
   */
  koumoku06Cod?: string
  /**
   * コード7
   */
  koumoku07Cod?: string
  /**
   * コード8
   */
  koumoku08Cod?: string
  /**
   * コード9
   */
  koumoku09Cod?: string
  /**
   * コード10
   */
  koumoku10Cod?: string
  /**
   * コード11
   */
  koumoku11Cod?: string
  /**
   * コード12
   */
  koumoku12Cod?: string
  /**
   * コード13
   */
  koumoku13Cod?: string
  /**
   * コード14
   */
  koumoku14Cod?: string
  /**
   * コード15
   */
  koumoku15Cod?: string
  /**
   * 日付1
   */
  koumoku01Ymd?: string
  /**
   * 日付2
   */
  koumoku02Ymd?: string
  /**
   * 日付3
   */
  koumoku03Ymd?: string
  /**
   * 日付4
   */
  koumoku04Ymd?: string
  /**
   * 日付5
   */
  koumoku05Ymd?: string
  /**
   * 日付6
   */
  koumoku06Ymd?: string
  /**
   * 日付7
   */
  koumoku07Ymd?: string
  /**
   * 日付8
   */
  koumoku08Ymd?: string
  /**
   * 日付9
   */
  koumoku09Ymd?: string
  /**
   * 日付10
   */
  koumoku10Ymd?: string
  /**
   * 日付11
   */
  koumoku11Ymd?: string
  /**
   * 日付12
   */
  koumoku12Ymd?: string
  /**
   * 日付13
   */
  koumoku13Ymd?: string
  /**
   * 日付14
   */
  koumoku14Ymd?: string
  /**
   * 日付15
   */
  koumoku15Ymd?: string
  /**
   * 表示順
   */
  sort?: string
  /**
   * 更新回数
   */
  modifiedCnt?: string
}

/**
 * 評価表下段情報
 */
export interface EvaluationTableLowerTierInfoType {
  /**
   * インデックス
   */
  [key: string]: string
  /**
   * データID
   */
  cmoni3Id: string
  /**
   * 文章1
   */
  koumoku01Knj: string
  /**
   * 文章2
   */
  koumoku02Knj: string
  /**
   * 文章3
   */
  koumoku03Knj: string
  /**
   * 更新回数
   */
  modifiedCnt: string
  /**
   * 再アセスメントの必要
   */
  retryChk: string
  /**
   * 再アセスメント予定日
   */
  yoteiYmd: string
  /**
   * 更新回数_履歴
   */
  historyModifiedCnt: string
}

/**
 * 計画期間レスポンスパラメータタイプ
 */
export interface EvaluationTablePlanPeriodSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 計画期間情報
     */
    planPeriodInfo: {
      /**
       * 期間ID
       */
      sc1Id?: string
      /**
       * 期間総件数
       */
      periodCnt?: string
      /**
       * 期間番号
       */
      periodNo?: string
      /**
       * 開始日
       */
      startYmd?: string
      /**
       * 終了日
       */
      endYmd?: string
    }
    /**
     * 履歴情報
     */
    historyInfo: {
      /**
       * マスタヘッダID
       */
      cmoni1Id?: string
      /**
       * 計画期間ID
       */
      sc1Id?: string
      /**
       * 作成日
       */
      createYmd?: string
      /**
       * 作成者
       */
      shokuId?: string
      /**
       * 履歴総件数
       */
      krirekiCnt?: string
      /**
       * 履歴番号
       */
      krirekiNo?: string
      /**
       * 作成者名
       */
      shokuinKnj?: string
    }
    /** 種別ID */
    syubetsuId: string
    /** 期間管理フラグ */
    kikanFlg: string
    /** ケース権限 */
    caseAuthority: string
    /** 実施計画～①権限 */
    implementationPlan1Authority: string
    /** 実施計画～②権限 */
    implementationPlan2Authority: string
    /** 実施計画～③権限 */
    implementationPlan3Authority: string
    /** 計画書権限 */
    planAuthority: string
    /** 実施ケース権限 */
    implementationCaseAuthority: string
    /** エラー区分 */
    errKbn: string
  }
}

/**
 * 保存APIリクエストパラメータタイプ
 */
export interface EvaluationTableUpdateInEntity extends InWebEntity {
  /** 履歴番号 */
  krirekiNo: string
  /** 期間管理フラグ */
  kikanFlg: string
  /** 期間番号 */
  periodNo: string
  /** 開始日 */
  startYmd: string
  /** 終了日 */
  endYmd: string
  /** 事業者名 */
  svJigyoName: string
  /** システム略称 */
  sysRyaku: string
  /** セクション名 */
  sectionName: string
  /** 職員ID */
  shokuId: string
  /** 機能ID */
  kinouId: string
  /** システムコード */
  gsyscd: string
  /** インデックス */
  index: string
  /** e文書用パラメータ */
  edocumentUseParam: IReportInEntity
  /** e文書削除用パラメータ */
  edocumentDeleteUseParam: IReportInEntity
  /** ヘーダID */
  cmoni1Id: string
  /** 計画期間ID */
  sc1Id: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 種別ID */
  syubetsuId: string
  /** 作成日 */
  createYmd: string
  /** 作成者 */
  createUserId: string
  /** マスタID */
  free1Id: string
  /** 更新区分 */
  updateKbn: string
  /** 再アセスメントの必要 */
  retryChk: string
  /** 再アセスメント予定日 */
  yoteiYmd: string
  /**
   * 評価表上段情報リスト
   */
  evaluationTableUpperTierInfoList: {
    /**     更新区分 */
    updateKbn: string
    /** データID */
    cmoni3Id: string
    /** 文章1 */
    koumoku01Knj: string
    /** 文章2 */
    koumoku02Knj: string
    /** 文章3 */
    koumoku03Knj: string
    /** 文章4 */
    koumoku04Knj: string
    /** 文章5 */
    koumoku05Knj: string
    /** 文章6 */
    koumoku06Knj: string
    /** 文章7 */
    koumoku07Knj: string
    /** 文章8 */
    koumoku08Knj: string
    /** 文章9 */
    koumoku09Knj: string
    /** 文章10 */
    koumoku10Knj: string
    /** 文章11 */
    koumoku11Knj: string
    /** 文章12 */
    koumoku12Knj: string
    /** 文章13 */
    koumoku13Knj: string
    /** 文章14 */
    koumoku14Knj: string
    /** 文章15 */
    koumoku15Knj: string
    /** コード1 */
    koumoku01Cod: string
    /** コード2 */
    koumoku02Cod: string
    /** コード3 */
    koumoku03Cod: string
    /** コード4 */
    koumoku04Cod: string
    /** コード5 */
    koumoku05Cod: string
    /** コード6 */
    koumoku06Cod: string
    /** コード7 */
    koumoku07Cod: string
    /** コード8 */
    koumoku08Cod: string
    /** コード9 */
    koumoku09Cod: string
    /** コード10 */
    koumoku10Cod: string
    /** コード11 */
    koumoku11Cod: string
    /** コード12 */
    koumoku12Cod: string
    /** コード13 */
    koumoku13Cod: string
    /** コード14 */
    koumoku14Cod: string
    /** コード15 */
    koumoku15Cod: string
    /** 日付1 */
    koumoku01Ymd: string
    /** 日付2 */
    koumoku02Ymd: string
    /** 日付3 */
    koumoku03Ymd: string
    /** 日付4 */
    koumoku04Ymd: string
    /** 日付5 */
    koumoku05Ymd: string
    /** 日付6 */
    koumoku06Ymd: string
    /** 日付7 */
    koumoku07Ymd: string
    /** 日付8 */
    koumoku08Ymd: string
    /** 日付9 */
    koumoku09Ymd: string
    /** 日付10 */
    koumoku10Ymd: string
    /** 日付11 */
    koumoku11Ymd: string
    /** 日付12 */
    koumoku12Ymd: string
    /** 日付13 */
    koumoku13Ymd: string
    /** 日付14 */
    koumoku14Ymd: string
    /** 日付15 */
    koumoku15Ymd: string
    /** 表示順 */
    sort: string
  }[]
  /** 評価表下段情報 */
  evaluationTableLowerTierInfo: {
    /** 文章1 */
    koumoku01Knj: string
    /** 文章2 */
    koumoku02Knj: string
    /** 文章3 */
    koumoku03Knj: string
  }
}

/**
 * 保存APIレスポンスパラメータタイプ
 */
export interface EvaluationTableUpdateOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** 計画期間ID */
    sc1Id: string
    /** ヘーダID */
    cmoni1Id: string
    /** エラー区分 */
    errKbn: string
  }
}
/**
 * 保存API評価表上段情報リスト
 */
export interface EvaluationTableUpperTierInfoListUpdateType {
  /**
   * 更新区分
   */
  updateKbn: string
  /**
   * データID
   */
  cmoni3Id: string
  /**
   * 文章1
   */
  koumoku01Knj: string
  /**
   * 文章2
   */
  koumoku02Knj: string
  /**
   * 文章3
   */
  koumoku03Knj: string
  /**
   * 文章4
   */
  koumoku04Knj: string
  /**
   * 文章5
   */
  koumoku05Knj: string
  /**
   * 文章6
   */
  koumoku06Knj: string
  /**
   * 文章7
   */
  koumoku07Knj: string
  /**
   * 文章8
   */
  koumoku08Knj: string
  /**
   * 文章9
   */
  koumoku09Knj: string
  /**
   * 文章10
   */
  koumoku10Knj: string
  /**
   * 文章11
   */
  koumoku11Knj: string
  /**
   * 文章12
   */
  koumoku12Knj: string
  /**
   * 文章13
   */
  koumoku13Knj: string
  /**
   * 文章14
   */
  koumoku14Knj: string
  /**
   * 文章15
   */
  koumoku15Knj: string
  /**
   * コード1
   */
  koumoku01Cod: string
  /**
   * コード2
   */
  koumoku02Cod: string
  /**
   * コード3
   */
  koumoku03Cod: string
  /**
   * コード4
   */
  koumoku04Cod: string
  /**
   * コード5
   */
  koumoku05Cod: string
  /**
   * コード6
   */
  koumoku06Cod: string
  /**
   * コード7
   */
  koumoku07Cod: string
  /**
   * コード8
   */
  koumoku08Cod: string
  /**
   * コード9
   */
  koumoku09Cod: string
  /**
   * コード10
   */
  koumoku10Cod: string
  /**
   * コード11
   */
  koumoku11Cod: string
  /**
   * コード12
   */
  koumoku12Cod: string
  /**
   * コード13
   */
  koumoku13Cod: string
  /**
   * コード14
   */
  koumoku14Cod: string
  /**
   * コード15
   */
  koumoku15Cod: string
  /**
   * 日付1
   */
  koumoku01Ymd: string
  /**
   * 日付2
   */
  koumoku02Ymd: string
  /**
   * 日付3
   */
  koumoku03Ymd: string
  /**
   * 日付4
   */
  koumoku04Ymd: string
  /**
   * 日付5
   */
  koumoku05Ymd: string
  /**
   * 日付6
   */
  koumoku06Ymd: string
  /**
   * 日付7
   */
  koumoku07Ymd: string
  /**
   * 日付8
   */
  koumoku08Ymd: string
  /**
   * 日付9
   */
  koumoku09Ymd: string
  /**
   * 日付10
   */
  koumoku10Ymd: string
  /**
   * 日付11
   */
  koumoku11Ymd: string
  /**
   * 日付12
   */
  koumoku12Ymd: string
  /**
   * 日付13
   */
  koumoku13Ymd: string
  /**
   * 日付14
   */
  koumoku14Ymd: string
  /**
   * 日付15
   */
  koumoku15Ymd: string
  /**
   * 表示順
   */
  sort: string
  /**
   * 更新回数
   */
  modifiedCnt: string
}

/**
 * 評価表下段情報
 */
export interface EvaluationTableLowerTierInfoUpdateType {
  /** インデックス */
  [key: string]: string
  /**
   * 文章1
   */
  koumoku01Knj: string
  /**
   * 文章2
   */
  koumoku02Knj: string
  /**
   * 文章3
   */
  koumoku03Knj: string
}

/**
 * 新規様式情報取得レスポンスパラメータタイプ
 */
export interface EvaluationTableNewSelectOutEntity {
  /**
   * 評価表上段項目数
   */
  columnCount1: string
  /**
   * 評価表下段項目数
   */
  columnCount2: string
  /**
   * 要介護度
   */
  yokaiKbn: string
  /**
   * 評価表上段様式リスト
   */
  evaluationTableUpperTierStyleInfoList: {
    /**
     * 区分ID
     */
    kbnId: string
    /**
     * 項目ID
     */
    koumokuId: string
    /**
     * 項目名
     */
    nameKnj: string
    /**
     * 入力方法
     */
    inputKbn: string
    /**
     * 連動区分
     */
    rendouKbn: string
    /**
     * 文字数
     */
    widthCnt: string
  }[]
  /**
   * 評価表下段様式リスト
   */
  evaluationTableLowerTierStyleInfoList: {
    /**
     * 区分ID
     */
    kbnId: string
    /**
     * 項目ID
     */
    koumokuId: string
    /**
     * 項目名
     */
    nameKnj: string
    /**
     * 入力方法
     */
    inputKbn: string
    /**
     * 連動区分
     */
    rendouKbn: string
    /**
     * 文字数
     */
    widthCnt: string
  }[]
}

/**
 * 歴史変更リクエストパラメータタイプ
 */
export interface EvaluationTableHistorySelectInEntity extends InWebEntity {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userid: string
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * ヘッダID
   */
  cmoni1Id: string
  /**
   * 履歴ページ区分
   */
  pageFlag: string
}

/**
 * 歴史変更レスポンスパラメータタイプ
 */
export interface EvaluationTableHistorySelectOutEntity {
  /**
   * 履歴情報
   */
  historyInfo: {
    /**
     * ヘッダID
     */
    cmoni1Id: string
    /**
     * 計画期間ID
     */
    sc1Id: string
    /**
     * 作成日
     */
    createYmd: string
    /**
     * 作成者
     */
    shokuId?: string
    /**
     * 作成者名
     */
    shokuinKnj?: string
    /**
     * 履歴総件数
     */
    krirekiCnt: string
    /**
     * 履歴番号
     */
    krirekiNo: string
    /**
     * 更新回数
     */
    modifiedCnt: string
  }
}

/**
 * 新規情報取得APIリクエストパラメータ
 */
export interface EvaluationTableNewSelectInEntity extends InWebEntity {
  /** マスタID */
  free1Id: string
  /** 利用者ID */
  userId: string
  /** 基準日 */
  kijunbiYmd: string
  /** アセスメント方式 */
  cpnFlg: string
}

/**
 * 新規情報取得APIレスポンスパラメータタイプ
 */
export interface EvaluationTableNewSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 評価表上段項目数
     */
    columnCount1: string
    /**
     * 評価表下段項目数
     */
    columnCount2: string
    /**
     * 要介護度
     */
    yokaiKbn: string
    /**
     * 評価表上段様式リスト
     */
    evaluationTableUpperTierStyleInfoList: {
      /**
       * 区分ID
       */
      kbnId: string
      /**
       * 項目ID
       */
      koumokuId: string
      /**
       * 項目名
       */
      nameKnj: string
      /**
       * 入力方法
       */
      inputKbn: string
      /**
       * 連動区分
       */
      rendouKbn: string
      /**
       * 文字数
       */
      widthCnt: string
    }[]
    /**
     * 評価表下段様式リスト
     */
    evaluationTableLowerTierStyleInfoList: {
      /**
       * 区分ID
       */
      kbnId: string
      /**
       * 項目ID
       */
      koumokuId: string
      /**
       * 項目名
       */
      nameKnj: string
      /**
       * 入力方法
       */
      inputKbn: string
      /**
       * 連動区分
       */
      rendouKbn: string
      /**
       * 文字数
       */
      widthCnt: string
    }[]
  }
}
