<script setup lang="ts">
/**
 * GUI00827_「アセスメント総括取込み設定」画面
 *
 * @description
 * 「アセスメント総括取込み設定」画面
 *
 * <AUTHOR> DAO DINH DUONG
 */
import _ from 'lodash'
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or56579Const } from '~/components/custom-components/organisms/Or56579/Or56579.constants'
import { Or56579Logic } from '~/components/custom-components/organisms/Or56579/Or56579.logic'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP［履歴選択］画面 「アセスメント総括取込み設定」画面
 * KMD DAO DINH DUONG 2025/05/09 ADD START
 **************************************************/
/**
 *画面ID
 */
const screenId = 'GUI00827'
/**
 *ルーティング
 */
const routing = 'GUI00827/pinia'
/**
 *画面物理名
 */
const screenName = 'GUI00827'

/**
 *画面状態管理用操作変数
 */
const screenStore = useScreenStore()
/**
 *画面状態管理用操作変数
 */
const or56579 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00827' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
/**
 *これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent
// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00827',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or56579Const.CP_ID(1) }],
})
// 自身のPinia領域をセットアップ
// コンポーネントの初期化処理を開始する
// Or56579Logic.initialize(or56579.value.uniqueCpId)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or56579Const.CP_ID(1)]: or56579.value,
})
Or56579Logic.initialize(or56579.value.uniqueCpId)
/**
 *ダイアログ表示フラグ
 */
const showDialogOr56579CksFlg1 = computed(() => {
  // Or56579 cks_flg=1 のダイアログ開閉状態
  return Or56579Logic.state.get(or56579.value.uniqueCpId)?.isOpen ?? false
})
/**
 *  ボタン押下時の処理(Or56579)
 *
 */
function onClickOr56579() {
  // Or56579 ダイアログ開閉状態を更新する
  Or56579Logic.state.set({
    uniqueCpId: or56579.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        syscd: '71101',
        shokuId: '1',
        houjinId: '1',
        shisetuId: '1',
        kinounameKnj: 'CMN',
        sectionKnj: 'TEST',
        svJigyoId: '1',
      },
    },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- GUI00827_困難度マスタ DAO DINH DUONG 2025/02/24 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr56579"
      >
        GUI00827_［履歴選択］画面 「アセスメント総括取込み設定」画面
      </v-btn>
      <g-custom-or-56579
        v-if="showDialogOr56579CksFlg1"
        v-bind="or56579"
      />
    </c-v-col>
  </c-v-row>
  <!-- GUI00827_［履歴選択］画面 「アセスメント総括取込み設定」画面 DAO DINH DUONG 2025/05/09 ADD END-->
</template>
