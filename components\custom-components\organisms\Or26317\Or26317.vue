<script setup lang="ts">
/**
 * Or26317：有機体：表用時間テキストフィールド（オプションメニュー付き）
 *
 * @description
 * 表の時間入力時に利用するコンポーネント（オプションメニュー機能付き）
 *
 * @remarks
 * vuetifyのテキストフィールドを利用するとスタイルの調整が困難になるのでHTMLタグを利用する
 * 時計アイコンボタン押下によって、オプションメニューまたは直接時間選択が表示される
 * -canClick:チェック処理用フラグ（true:時間選択のプルダウン/ダイアログ表示 / false:時間選択のプルダウン/ダイアログ非表示）
 * -showOptionMenu:オプションメニュー表示フラグ（true:メニュー選択 / false:直接選択）
 * -selectionMode:時間選択用フラグ（true:プルダウン / false:ダイアログ）
 *
 * <AUTHOR> PHAM TIEN THANH
 */
import { reactive, watch, ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Or26317Const } from './Or26317.constants'
import type {
  Or26317Type,
  Or26317OnewayType,
  TimeInputMode,
} from '@/types/cmn/business/components/Or26317Type'
import type { Mo01375OnewayType } from '~/types/business/components/Mo01375Type'
import type { Mo01376OnewayType } from '~/types/business/components/Mo01376Type'
import { useTableCpValidation } from '~/composables/useTableCpValidation'
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or26317Type
  onewayModelValue?: Or26317OnewayType
}

const props = defineProps<Props>()

const defaultOnewayModelValue = {
  or26317Oneway: {
    type: 'text',
    selectionMode: true,
    canClick: true,
    showOptionMenu: false,
    mo01376Oneway: {
      inputMode: true,
      completeVisible: true,
      rangeHhFlg: false,
    },
    mo01375Oneway: {
      rangeHhFlg: false,
    },
  } as Or26317OnewayType,
}

/**************************************************
 * 変数
 **************************************************/
const local = reactive({
  or26317: {
    ...props.modelValue,
  },
  mo01375: {
    value: props.modelValue.value,
    showMenu: false,
  },
  mo01376: {
    value: props.modelValue.value,
    valueTo: '',
    mo00024: {
      isOpen: false,
    },
    mo00038HourTens: {
      mo00045: {
        value: '0',
      },
    },
    mo00038HourOnes: {
      mo00045: {
        value: '0',
      },
    },
    mo00038MinuteTens: {
      mo00045: {
        value: '0',
      },
    },
    mo00038MinuteOnes: {
      mo00045: {
        value: '0',
      },
    },
  },
})

// アイコン:デフォルト（スケジュール）
const icon = ref('schedule')

// オプションメニューの表示状態
const showOptionMenu = ref(false)

// 選択されたモード
const selectedMode = ref<TimeInputMode>('NO_RANGE')

// メニューの位置
const menuPositionTop = ref<string>('')
const menuPositionLeft = ref<string>('')

// メニューのDOM参照
const optionMenuRef = ref<HTMLDivElement>()
const inputWrapperRef = ref<HTMLElement>()

/**
 * ドキュメントのマウスダウンイベントハンドラー
 *
 * @param e - ドキュメントのマウスダウンイベント
 */
const docMousedownHandler = (e: MouseEvent) => {
  const menuEl = optionMenuRef.value
  const inputEl = inputWrapperRef.value
  const target = e.target as Node | null
  if (!target) return

  const clickInMenu = !!menuEl && menuEl.contains(target)
  const clickInInput = !!inputEl && inputEl.contains(target)
  if (!clickInMenu && !clickInInput) {
    showOptionMenu.value = false
  }
}

/**
 * コンポーネントがマウントされたときに実行されます。
 *
 * @param e - キーボードイベント
 */
const onEscapeKey = (e: KeyboardEvent) => {
  if (e.key === Or26317Const.DEFAULT.ESCAPE) {
    showOptionMenu.value = false
  }
}

const localOneway = reactive({
  or26317Oneway: {
    ...defaultOnewayModelValue.or26317Oneway,
    ...props.onewayModelValue,
  },
  mo00009Oneway: {
    btnIcon: icon,
    disabled: props.onewayModelValue?.disabled,
  },
})

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    local.or26317 = {
      ...newValue,
    }

    // 新しい値に対して初期化処理を実行
    const value = newValue.value
    const rangeMatch = /^(\d{2}:\d{2})～(\d{2}:\d{2})$/.exec(value)
    if (rangeMatch) {
      // 範囲時間の場合
      local.mo01376.value = rangeMatch[1]
      local.mo01376.valueTo = rangeMatch[2]
    } else if (/^\d{2}:\d{2}$/.exec(value)) {
      // 単一時間の場合
      local.mo01375.value = value
      local.mo01376.value = value
    }
  },
  { deep: true }
)
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.or26317Oneway = {
      ...defaultOnewayModelValue.or26317Oneway,
      ...newValue,
    }
    if (newValue?.disabled) {
      localOneway.mo00009Oneway.disabled = newValue.disabled
    }
  },
  { deep: true }
)

/**
 * showOptionMenuの状態が変化したときに実行されます。
 */
watch(showOptionMenu, async (open) => {
  if (open) {
    await nextTick()
    optionMenuRef.value?.focus?.()
    document.addEventListener('mousedown', docMousedownHandler, true)
    document.addEventListener('keydown', onEscapeKey, true)
  } else {
    document.removeEventListener('mousedown', docMousedownHandler, true)
    document.removeEventListener('keydown', onEscapeKey, true)
  }
})

onUnmounted(() => {
  document.removeEventListener('mousedown', docMousedownHandler, true)
  document.removeEventListener('keydown', onEscapeKey, true)
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits([
  'update:modelValue',
  'blur',
  'click',
  'keydown',
  'change',
  'focus',
  'keypress',
  'keyup',
])
watch(
  () => local.or26317.value,
  () => {
    validValue.value = local.or26317.value
    emit('update:modelValue', local.or26317)
  }
)

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * コンポーネントがDOMにマウントされた後に実行されます。
 *
 * @remarks
 * - local.or26317に入力される文字列の制限
 * - 範囲時間フォーマットの解析
 */
onMounted(() => {
  initializeTimeValues()
})

/**
 * 時間値の初期化処理
 */
function initializeTimeValues() {
  const value = props.modelValue.value

  // 範囲時間フォーマット "HH:MM～HH:MM" の判定
  const rangeMatch = /^(\d{2}:\d{2})～(\d{2}:\d{2})$/.exec(value)
  if (rangeMatch) {
    // 範囲時間の場合
    local.or26317.value = value
    local.mo01376.value = rangeMatch[1]
    local.mo01376.valueTo = rangeMatch[2]
    return
  }

  // 単一時間フォーマット "HH:MM" の判定
  const singleMatch = /^\d{2}:\d{2}$/.exec(value)
  if (singleMatch) {
    // 単一時間の場合
    local.or26317.value = value
    local.mo01375.value = value
    local.mo01376.value = value
    return
  }

  // フォーマットが不正な場合は空にする
  if (value && !singleMatch && !rangeMatch) {
    local.or26317.value = ''
    emit('update:modelValue', local.or26317)
  }
}
watch(
  () => local.mo01375.value,
  (newValue) => {
    local.or26317.value = newValue
  }
)

watch([() => local.mo01376.value, () => local.mo01376.valueTo], ([newValue, newValueTo]) => {
  if (newValueTo && newValue) {
    // 範囲時間の場合: "09:00～17:00" format
    local.or26317.value = `${newValue}～${newValueTo}`
  } else if (newValue) {
    // 単一時間の場合
    local.or26317.value = newValue
  }
})

watch(
  () => local.mo01375.showMenu,
  (newValue) => {
    if (newValue) {
      icon.value = 'cancel'
    } else {
      icon.value = 'schedule'
    }
  }
)

/**
 * アイコン押下時の処理
 *
 * @remarks
 * スケジュールアイコン押下時
 * -showOptionMenuがtrueの場合、モード選択メニューを表示
 * -showOptionMenuがfalseの場合、selectionModeによってダイアログかプルダウンが開く
 *
 * ×ボタンアイコン押下時
 * -値を'00:00'に変更
 */
function handleIconClick() {
  if (localOneway.or26317Oneway.canClick) {
    if (icon.value === Or26317Const.DEFAULT.SCHEDULE_ICON) {
      // オプションメニューを表示する場合
      if (localOneway.or26317Oneway.showOptionMenu) {
        calculateMenuPosition()
        showOptionMenu.value = true
      } else {
        // 直接時間選択を開く
        openTimeSelector()
      }
    } else {
      local.or26317.value = '00:00'
      local.mo01375.value = '00:00'
      local.mo01376.value = '00:00'
      local.mo01376.valueTo = ''
    }
  }
}

/**
 * 時間選択を開く処理
 */
function openTimeSelector() {
  if (localOneway.or26317Oneway.selectionMode) {
    // プルダウンモード（単一時間のみ）
    local.mo01375.value = local.or26317.value
    local.mo01375.showMenu = true
  } else {
    // ダイアログモード（単一または範囲時間）
    const rangeMatch = /^(\d{2}:\d{2})～(\d{2}:\d{2})$/.exec(local.or26317.value)
    if (rangeMatch) {
      // 既存の範囲時間がある場合
      local.mo01376.value = rangeMatch[1]
      local.mo01376.valueTo = rangeMatch[2]
    } else {
      // 単一時間または空の場合
      const mo01376Oneway = localOneway.or26317Oneway.mo01376Oneway as Mo01376OnewayType
      if (mo01376Oneway.rangeInputMode) {
        local.mo01376.value = local.or26317.value || ''
        local.mo01376.valueTo = ''
      } else {
        local.mo01376.value = local.or26317.value || '00:00'
        local.mo01376.valueTo = ''
      }
    }
    local.mo01376.mo00024.isOpen = true
  }
}

/**
 * メニューの位置を計算する
 */
function calculateMenuPosition() {
  if (inputWrapperRef.value) {
    const rect = inputWrapperRef.value.getBoundingClientRect()
    menuPositionTop.value = `${rect.bottom + window.scrollY}px`
    menuPositionLeft.value = `${rect.left + window.scrollX}px`
  }
}

/**
 * オプションメニューでモードを選択した時の処理
 *
 * @param mode - 選択されたモード
 */
async function selectTimeInputMode(mode: TimeInputMode) {
  selectedMode.value = mode
  showOptionMenu.value = false

  if (mode === Or26317Const.DEFAULT.NO_RANGE) {
    // 範囲時間から単一時間に変更する場合、開始時間のみを保持
    const currentValue = local.or26317.value
    const rangeMatch = /^(\d{2}:\d{2})～(\d{2}:\d{2})$/.exec(currentValue)
    if (rangeMatch) {
      local.mo01376.value = rangeMatch[1]
      local.mo01376.valueTo = ''
      local.or26317.value = rangeMatch[1]
    }

    // 単一時間入力: プルダウンまたはダイアログ
    localOneway.or26317Oneway.selectionMode = true
    localOneway.or26317Oneway.mo01375Oneway = {
      ...localOneway.or26317Oneway.mo01375Oneway,
      rangeHhFlg: false,
    } as Mo01375OnewayType
    openTimeSelector()
  } else if (mode === Or26317Const.DEFAULT.WITH_RANGE) {
    // 範囲時間入力: ダイアログで範囲モード
    localOneway.or26317Oneway.selectionMode = false
    localOneway.or26317Oneway.mo01376Oneway = {
      ...localOneway.or26317Oneway.mo01376Oneway,
      rangeInputMode: true,
      rangeHhFlg: false,
    } as Mo01376OnewayType

    // nextTickで確実にselectionModeの変更がDOMに反映されてからダイアログを開く
    await nextTick()
    openTimeSelector()
  }
}

/**
 * メニューがフォーカスを失った時の処理
 */
function onOptionMenuLostFocus() {
  showOptionMenu.value = false
}

// バリデーションチェック用の値(ref)
const validValue = ref(local.or26317.value)
/**
 * バリデーションチェック用の共通処理
 */
const { isInvalid, handleBlur } = useTableCpValidation<string>({
  refValue: validValue,
  validateOn: localOneway.or26317Oneway?.validateOn,
  rules: (localOneway.or26317Oneway?.rules ?? []) as ((value: string) => string | boolean)[],
})
</script>
<template>
  <!-- vuetifyのテキストフィールドを利用するとスタイルの調整が困難になるのでHTMLタグを利用する -->
  <div
    ref="inputWrapperRef"
    class="input-wrapper full-height-cell"
  >
    <input
      v-model="local.or26317.value"
      v-bind="{ ...$attrs, ...localOneway.or26317Oneway }"
      :class="['full-width-field', 'txt', 'table-cell', { invalid: isInvalid }]"
      @change="$emit('change', $event)"
      @focus="$emit('focus', $event)"
      @blur="handleBlur"
      @keydown="$emit('keydown', $event)"
      @keypress="$emit('keypress', $event)"
      @keyup="$emit('keyup', $event)"
    />
    <base-mo00009
      class="icon-btn"
      :oneway-model-value="localOneway.mo00009Oneway"
      @click.stop="handleIconClick"
    />
    <base-mo01375
      v-if="localOneway.or26317Oneway.selectionMode"
      v-model="local.mo01375"
      :oneway-model-value="localOneway.or26317Oneway.mo01375Oneway"
    />
  </div>

  <!-- Option Menu -->
  <div
    v-if="localOneway.or26317Oneway.showOptionMenu"
    class="footer"
    :style="{ top: menuPositionTop, left: menuPositionLeft }"
    @click.stop
  >
    <v-slide-y-transition>
      <div
        v-if="showOptionMenu"
        ref="optionMenuRef"
        class="footer-menu d-flex"
        tabindex="0"
        @blur="onOptionMenuLostFocus"
        @mousedown.stop
      >
        <div
          class="footer-menu-item"
          @click="selectTimeInputMode('NO_RANGE')"
        >
          範囲なし
        </div>
        <div
          class="footer-menu-item"
          @click="selectTimeInputMode('WITH_RANGE')"
        >
          範囲あり
        </div>
      </div>
    </v-slide-y-transition>
  </div>

  <base-mo01376
    v-if="!localOneway.or26317Oneway.selectionMode"
    v-show="local.mo01376.mo00024.isOpen"
    v-model="local.mo01376"
    :oneway-model-value="localOneway.or26317Oneway.mo01376Oneway"
  />
</template>
<style scoped lang="scss">
.full-height-cell {
  height: 100% !important; /* 高さをセルいっぱいに広げる */
}

.full-width-field {
  width: 100% !important; /* 幅をセルいっぱいに広げる */
  height: 100% !important; /* 高さをセルいっぱいに広げる */
  padding: 0 10px !important; /* 不要なパディングを取り除く */
  margin: 0 !important; /* 不要なマージンを取り除く */
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  outline: none !important;
}

.icon-btn {
  position: absolute;
  right: 10px;
  background: transparent;
  border: none;
  cursor: pointer;
}
.txt:disabled {
  background: rgb(var(--v-theme-black-50));
}

/* OrX0159スタイルのオプションメニュー */
$footer-menu-item-color: #0760e6;
$footer-menu-bg-color: #fff;
$footer-menu-border-color: #a6b4c1;
$footer-menu-hover-color: #f6f6f6;

// メニューコンテナ
.footer {
  width: 100%;
  position: fixed;
  z-index: 9999;

  .footer-menu {
    position: fixed;
    border: 1px solid $footer-menu-border-color;
    border-radius: 4px;
    background: $footer-menu-bg-color;

    // メニューアイテム
    .footer-menu-item {
      padding: 4px 8px;
      cursor: pointer;
      color: $footer-menu-item-color;
      white-space: nowrap;
    }

    .footer-menu-item:hover {
      background: $footer-menu-hover-color;
    }

    .footer-menu-item + .footer-menu-item {
      border-left: 1px solid $footer-menu-border-color;
    }
  }
}

/* 編集可能なセルと編集不可のセルが混在する場合のスタイル */
.editability-mix-table {
  .full-width-field:not(:disabled) {
    margin: 1px !important;
  }

  .table-cell:not(:disabled) {
    outline-color: rgb(var(--v-theme-form));
    margin: 2px !important;
    width: calc(100% - 4px) !important;
    height: calc(100% - 4px) !important;
    border-radius: 2px;
  }

  .table-cell:not(:disabled):focus {
    border: solid 2px rgb(var(--v-theme-blue-100));
    outline: solid 1px rgb(var(--v-theme-key));
  }

  .table-cell:not(:disabled).invalid {
    border: solid 2px rgb(var(--v-theme-red-100));
    outline: solid 1px rgb(var(--v-theme-error));
  }
}
</style>
