<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import { definePageMeta, useScreenStore, useInitialize } from '#imports'
import { Or28543Const } from '~/components/custom-components/organisms/Or28543/Or28543.constants'
import { Or28543Logic } from '~/components/custom-components/organisms/Or28543/Or28543.logic'
import type { Or28543OnewayType, Or28543Type } from '~/types/cmn/business/components/Or28543Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01112'
// ルーティング
const routing = 'GUI01112/pinia'
// 画面物理名
const screenName = 'GUI01112'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or28543 = ref({ uniqueCpId: Or28543Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01112' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or28543Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or28543.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01112',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28543Const.CP_ID(0) }],
})

const or28543Type = ref<Or28543Type>({
  mode: '',
  sortList: [],
})

// ダイアログ表示フラグ
const showDialogOr28543 = computed(() => {
  // Or28543のダイアログ開閉状態
  return Or28543Logic.state.get(or28543.value.uniqueCpId)?.isOpen ?? false
})

const or28543Data = ref<Or28543OnewayType>()

/**
 *  ボタン押下時の処理(Or28543)
 *
 * @param mode - モード
 */
function onClickOr28543(mode: string) {
  // Or28543のダイアログ開閉状態を更新する
  if (mode === 'A4-3') {
    or28543DataA43.value.mode = mode.toString()
    or28543Data.value = or28543DataA43.value
  } else if (mode === 'A3' || mode === 'A4-2') {
    or28543DataA3.value.mode = mode.toString()
    or28543Data.value = or28543DataA3.value
  }

  Or28543Logic.state.set({
    uniqueCpId: or28543.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理(Or28543)
 *
 * @param mode - モード
 */
function onClickOr28543UT(mode: string) {
  // Or28543のダイアログ開閉状態を更新する
  if (mode === 'A4-3') {
    or28543DataA43.value.mode = mode.toString()
    or28543DataA43.value.isPeriodManagementFlag = local.isPeriodManagementFlag.value
    or28543DataA43.value.calendarImportByPeriod = local.calendarImportByPeriod.value
    or28543Data.value = or28543DataA43.value
    console.log('onClickOr28543UT A4-3', or28543Data.value);

  } else if (mode === 'A3' || mode === 'A4-2') {
    or28543DataA3.value.mode = mode.toString()
    or28543DataA3.value.isPeriodManagementFlag = local.isPeriodManagementFlag.value
    or28543DataA3.value.calendarImportByPeriod = local.calendarImportByPeriod.value
    or28543Data.value = or28543DataA3.value
  }

  Or28543Logic.state.set({
    uniqueCpId: or28543.value.uniqueCpId,
    state: { isOpen: true },
  })
}

watch(or28543Type, () => {
  console.log(or28543Type.value)
})

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 親画面.期間の管理フラグ
  isPeriodManagementFlag: { value: '0' } as Mo00045Type,
  // 親画面.期間のカレンダー取込
  calendarImportByPeriod: { value: '0' } as Mo00045Type,
})

/**
 * 親画面からの初期値
 */
const or28543DataA43 = ref<Or28543OnewayType>({
  mode: '',
  /**
   * 親画面.計画表様式フラグ
   */
  isPlanFormatFlag: '',
  /**
   * 親画面.期間の管理フラグ
   */
  isPeriodManagementFlag: '1',
  /**
   * 親画面.期間のカレンダー取込
   */
  calendarImportByPeriod: '2',
  indexList: [
    {
      /**
       * 親画面.ID
       */
      ID: '100',
      /**
       * 表示順
       */
      sort: 1,
      /**
       * ポイント
       */
      goalSupportPoints: '精神的に不安なる出来事があったようで（内容まだわからない状況）1',
      /**
       * インフォーマル
       */
      informalSupport: 'インフォーマル',
      /**
       * 介護保険
       */
      nursingCareInsurance: 1,
      /**
       * 本人の取組
       */
      selfEffort: 1,
      /**
       * 具体的な支援の内容（本人）
       */
      selfSupportContent: '一部介助が必要だが、自分から食事摂取 1',
      // /**
      //  * 家族・地域の支援、民間…
      //  */
      // familyCommunitySupport: 1,
      /**
       * 具体的な支援の内容（家族）
       */
      familySupportContent: '高血圧の援助',
      /**
       * 介護保険ｻｰﾋﾞｽ地域支援事業…
       */
      insuranceRegionalSupport: '介護保険ｻｰﾋﾞｽ地域支援事業…',
      /**
       * 具体的な支援の内容（保険・地域）
       */
      insuranceRegionalSupportContent: '高血圧の援助',
      /**
       * ※1（本人）
       */
      noteSelf: '○',
      /**
       * ※1（家族）
       */
      noteFamily: '○',
      /**
       * ※1（保険・地域）
       */
      noteInsuranceRegional: '○',
      /**
       * サービス種別
       */
      serviceType: 'サービス種別',
      /**
       * サービス種別（本人）
       */
      serviceTypeSelf: '介護予防訪問看護',
      /**
       * サービス種別（家族）
       */
      serviceTypeFamily: '介護予防訪問看護',
      /**
       * サービス種別（保険・地域）
       */
      serviceTypeInsuranceRegional: '介護予防訪問看護',
      /**
       * 事業所
       */
      office: '事業所',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（本人）
       */
      serviceProviderSelf: '介護ケアマネ',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（家族）
       */
      serviceProviderFamily: '介護ケアマネ',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（保険・地域）
       */
      serviceProviderInsuranceRegional: '予防看護いきいき',
      /**
       * 頻度（本人）
       */
      frequencySelf: '14日〜25日',
      /**
       * 頻度（家族）
       */
      frequencyFamily: '月・火',
      /**
       * 頻度（保険・地域）
       */
      frequencyInsuranceRegional: '月・火',
      /**
       * 期間
       */
      period: '',
      /**
       * 期間開始日
       */
      periodStartDate: '',
      /**
       * 期間終了日
       */
      periodEndDate: '',
      /**
       * 期間（本人）
       */
      periodSelf: '24Hシート',
      /**
       * 期間開始日（本人）
       */
      periodSelfStartDate: '2025/05/13',
      /**
       * 期間終了日（本人）
       */
      periodSelfEndDate: '2025/05/15',
      /**
       * 期間（家族）
       */
      periodFamily: '',
      /**
       * 期間開始日（家族）
       */
      periodFamilyStartDate: '2025/05/13',
      /**
       * 期間終了日（家族）
       */
      periodFamilyEndDate: '2025/05/15',
      /**
       * 期間（保険・地域）
       */
      periodInsuranceRegional: '24Hシート',
      /**
       * 期間開始日（保険・地域）
       */
      periodInsuranceRegionalStartDate: '',
      /**
       * 期間終了日（保険・地域）
       */
      periodInsuranceRegionalEndDate: '',
    },
    {
      /**
       * 親画面.ID
       */
      ID: '200',
      /**
       * 表示順
       */
      sort: 2,
      /**
       * ポイント
       */
      goalSupportPoints: '精神的に不安なる出来事があったようで（内容まだわからない状況）21',
      /**
       * インフォーマル
       */
      informalSupport: 'インフォーマル',
      /**
       * 介護保険
       */
      nursingCareInsurance: 1,
      /**
       * 本人の取組
       */
      selfEffort: 1,
      /**
       * 具体的な支援の内容（本人）
       */
      selfSupportContent: '一部介助が必要だが、自分から食事摂取 1',
      // /**
      //  * 家族・地域の支援、民間…
      //  */
      // familyCommunitySupport: 1,
      /**
       * 具体的な支援の内容（家族）
       */
      familySupportContent: '高血圧の援助',
      /**
       * 介護保険ｻｰﾋﾞｽ地域支援事業…
       */
      insuranceRegionalSupport: '介護保険ｻｰﾋﾞｽ地域支援事業…',
      /**
       * 具体的な支援の内容（保険・地域）
       */
      insuranceRegionalSupportContent: '高血圧の援助',
      /**
       * ※1（本人）
       */
      noteSelf: '○',
      /**
       * ※1（家族）
       */
      noteFamily: '○',
      /**
       * ※1（保険・地域）
       */
      noteInsuranceRegional: '○',
      /**
       * サービス種別
       */
      serviceType: 'サービス種別',
      /**
       * サービス種別（本人）
       */
      serviceTypeSelf: '介護予防訪問看護',
      /**
       * サービス種別（家族）
       */
      serviceTypeFamily: '介護予防訪問看護',
      /**
       * サービス種別（保険・地域）
       */
      serviceTypeInsuranceRegional: '介護予防訪問看護',
      /**
       * 事業所
       */
      office: '事業所',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（本人）
       */
      serviceProviderSelf: '介護ケアマネ',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（家族）
       */
      serviceProviderFamily: '介護ケアマネ',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（保険・地域）
       */
      serviceProviderInsuranceRegional: '予防看護いきいき',
      /**
       * 頻度（本人）
       */
      frequencySelf: '14日〜25日',
      /**
       * 頻度（家族）
       */
      frequencyFamily: '月・火',
      /**
       * 頻度（保険・地域）
       */
      frequencyInsuranceRegional: '月・火',
      /**
       * 期間
       */
      period: '',
      /**
       * 期間開始日
       */
      periodStartDate: '',
      /**
       * 期間終了日
       */
      periodEndDate: '',
      /**
       * 期間（本人）
       */
      periodSelf: '',
      /**
       * 期間開始日（本人）
       */
      periodSelfStartDate: '2025/05/13',
      /**
       * 期間終了日（本人）
       */
      periodSelfEndDate: '2025/05/15',
      /**
       * 期間（家族）
       */
      periodFamily: '',
      /**
       * 期間開始日（家族）
       */
      periodFamilyStartDate: '2025/05/13',
      /**
       * 期間終了日（家族）
       */
      periodFamilyEndDate: '2025/05/15',
      /**
       * 期間（保険・地域）
       */
      periodInsuranceRegional: '24Hシート',
      /**
       * 期間開始日（保険・地域）
       */
      periodInsuranceRegionalStartDate: '',
      /**
       * 期間終了日（保険・地域）
       */
      periodInsuranceRegionalEndDate: '',
    },
    {
      /**
       * 親画面.ID
       */
      ID: '300',
      /**
       * 表示順
       */
      sort: 3,
      /**
       * ポイント
       */
      goalSupportPoints: '精神的に不安なる出来事があったようで（内容まだわからない状況）3',
      /**
       * インフォーマル
       */
      informalSupport: 'インフォーマル',
      /**
       * 介護保険
       */
      nursingCareInsurance: 1,
      /**
       * 本人の取組
       */
      selfEffort: 1,
      /**
       * 具体的な支援の内容（本人）
       */
      selfSupportContent: '一部介助が必要だが、自分から食事摂取 1',
      // /**
      //  * 家族・地域の支援、民間…
      //  */
      // familyCommunitySupport: 1,
      /**
       * 具体的な支援の内容（家族）
       */
      familySupportContent: '高血圧の援助',
      /**
       * 介護保険ｻｰﾋﾞｽ地域支援事業…
       */
      insuranceRegionalSupport: '介護保険ｻｰﾋﾞｽ地域支援事業…',
      /**
       * 具体的な支援の内容（保険・地域）
       */
      insuranceRegionalSupportContent: '高血圧の援助',
      /**
       * ※1（本人）
       */
      noteSelf: '○',
      /**
       * ※1（家族）
       */
      noteFamily: '○',
      /**
       * ※1（保険・地域）
       */
      noteInsuranceRegional: '○',
      /**
       * サービス種別
       */
      serviceType: 'サービス種別',
      /**
       * サービス種別（本人）
       */
      serviceTypeSelf: '介護予防訪問看護',
      /**
       * サービス種別（家族）
       */
      serviceTypeFamily: '介護予防訪問看護',
      /**
       * サービス種別（保険・地域）
       */
      serviceTypeInsuranceRegional: '介護予防訪問看護',
      /**
       * 事業所
       */
      office: '事業所',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（本人）
       */
      serviceProviderSelf: '介護ケアマネ',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（家族）
       */
      serviceProviderFamily: '介護ケアマネ',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（保険・地域）
       */
      serviceProviderInsuranceRegional: '予防看護いきいき',
      /**
       * 頻度（本人）
       */
      frequencySelf: '14日〜25日',
      /**
       * 頻度（家族）
       */
      frequencyFamily: '月・火',
      /**
       * 頻度（保険・地域）
       */
      frequencyInsuranceRegional: '月・火',
      /**
       * 期間
       */
      period: '',
      /**
       * 期間開始日
       */
      periodStartDate: '',
      /**
       * 期間終了日
       */
      periodEndDate: '',
      /**
       * 期間（本人）
       */
      periodSelf: '24Hシート',
      /**
       * 期間開始日（本人）
       */
      periodSelfStartDate: '',
      /**
       * 期間終了日（本人）
       */
      periodSelfEndDate: '',
      /**
       * 期間（家族）
       */
      periodFamily: '',
      /**
       * 期間開始日（家族）
       */
      periodFamilyStartDate: '2025/05/13',
      /**
       * 期間終了日（家族）
       */
      periodFamilyEndDate: '2025/05/15',
      /**
       * 期間（保険・地域）
       */
      periodInsuranceRegional: '24Hシート',
      /**
       * 期間開始日（保険・地域）
       */
      periodInsuranceRegionalStartDate: '',
      /**
       * 期間終了日（保険・地域）
       */
      periodInsuranceRegionalEndDate: '',
    },
    {
      /**
       * 親画面.ID
       */
      ID: '400',
      /**
       * 表示順
       */
      sort: 4,
      /**
       * ポイント
       */
      goalSupportPoints: '精神的に不安なる出来事があったようで（内容まだわからない状況）1',
      /**
       * インフォーマル
       */
      informalSupport: 'インフォーマル',
      /**
       * 介護保険
       */
      nursingCareInsurance: 1,
      /**
       * 本人の取組
       */
      selfEffort: 1,
      /**
       * 具体的な支援の内容（本人）
       */
      selfSupportContent: '一部介助が必要だが、自分から食事摂取 1',
      // /**
      //  * 家族・地域の支援、民間…
      //  */
      // familyCommunitySupport: 1,
      /**
       * 具体的な支援の内容（家族）
       */
      familySupportContent: '高血圧の援助',
      /**
       * 介護保険ｻｰﾋﾞｽ地域支援事業…
       */
      insuranceRegionalSupport: '介護保険ｻｰﾋﾞｽ地域支援事業…',
      /**
       * 具体的な支援の内容（保険・地域）
       */
      insuranceRegionalSupportContent: '高血圧の援助',
      /**
       * ※1（本人）
       */
      noteSelf: '○',
      /**
       * ※1（家族）
       */
      noteFamily: '○',
      /**
       * ※1（保険・地域）
       */
      noteInsuranceRegional: '○',
      /**
       * サービス種別
       */
      serviceType: 'サービス種別',
      /**
       * サービス種別（本人）
       */
      serviceTypeSelf: '介護予防訪問看護',
      /**
       * サービス種別（家族）
       */
      serviceTypeFamily: '介護予防訪問看護',
      /**
       * サービス種別（保険・地域）
       */
      serviceTypeInsuranceRegional: '介護予防訪問看護',
      /**
       * 事業所
       */
      office: '事業所',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（本人）
       */
      serviceProviderSelf: '介護ケアマネ',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（家族）
       */
      serviceProviderFamily: '介護ケアマネ',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（保険・地域）
       */
      serviceProviderInsuranceRegional: '予防看護いきいき',
      /**
       * 頻度（本人）
       */
      frequencySelf: '14日〜25日',
      /**
       * 頻度（家族）
       */
      frequencyFamily: '月・火',
      /**
       * 頻度（保険・地域）
       */
      frequencyInsuranceRegional: '月・火',
      /**
       * 期間
       */
      period: '',
      /**
       * 期間開始日
       */
      periodStartDate: '',
      /**
       * 期間終了日
       */
      periodEndDate: '',
      /**
       * 期間（本人）
       */
      periodSelf: '24Hシート',
      /**
       * 期間開始日（本人）
       */
      periodSelfStartDate: '2025/05/13',
      /**
       * 期間終了日（本人）
       */
      periodSelfEndDate: '2025/05/15',
      /**
       * 期間（家族）
       */
      periodFamily: '',
      /**
       * 期間開始日（家族）
       */
      periodFamilyStartDate: '2025/05/13',
      /**
       * 期間終了日（家族）
       */
      periodFamilyEndDate: '2025/05/15',
      /**
       * 期間（保険・地域）
       */
      periodInsuranceRegional: '24Hシート',
      /**
       * 期間開始日（保険・地域）
       */
      periodInsuranceRegionalStartDate: '',
      /**
       * 期間終了日（保険・地域）
       */
      periodInsuranceRegionalEndDate: '',
    },
  ],
})

/**
 * 親画面からの初期値
 */
const or28543DataA3 = ref<Or28543OnewayType>({
  mode: '',
  /**
   * 親画面.計画表様式フラグ
   */
  isPlanFormatFlag: '',
  /**
   * 親画面.期間の管理フラグ
   */
  isPeriodManagementFlag: '1',
  /**
   * 親画面.期間のカレンダー取込
   */
  calendarImportByPeriod: '2',

  indexList: [
    {
      /**
       * 親画面.ID
       */
      ID: '100',
      /**
       * 表示順
       */
      sort: 1,
      /**
       * ポイント
       */
      goalSupportPoints: '精神的に不安なる出来事があったようで（内容まだわからない状況）1',
      /**
       * インフォーマル
       */
      informalSupport: 'インフォーマル 1',
      /**
       * 介護保険
       */
      nursingCareInsurance: 1,
      /**
       * 本人の取組
       */
      selfEffort: 1,
      /**
       * 具体的な支援の内容（本人）
       */
      selfSupportContent: '一部介助が必要だが、自分から食事摂取 1',
      // /**
      //  * 家族・地域の支援、民間…
      //  */
      // familyCommunitySupport: 1,
      /**
       * 具体的な支援の内容（家族）
       */
      familySupportContent: 'familySupportContent',
      /**
       * 介護保険ｻｰﾋﾞｽ地域支援事業…
       */
      insuranceRegionalSupport: '介護保険ｻｰﾋﾞｽ地域支援事業… 1',
      /**
       * 具体的な支援の内容（保険・地域）
       */
      insuranceRegionalSupportContent: '具体的な支援の内容（保険・地域）',
      /**
       * ※1（本人）
       */
      noteSelf: '○',
      /**
       * ※1（家族）
       */
      noteFamily: '○',
      /**
       * ※1（保険・地域）
       */
      noteInsuranceRegional: '○',
      /**
       * サービス種別
       */
      serviceType: 'サービス種別 1',
      /**
       * サービス種別（本人）
       */
      serviceTypeSelf: 'serviceTypeSelf',
      /**
       * サービス種別（家族）
       */
      serviceTypeFamily: 'serviceTypeFamily',
      /**
       * サービス種別（保険・地域）
       */
      serviceTypeInsuranceRegional: 'serviceTypeInsuranceRegional',
      /**
       * 事業所
       */
      office: '事業所 1',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（本人）
       */
      serviceProviderSelf: 'serviceProviderSelf',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（家族）
       */
      serviceProviderFamily: 'serviceProviderFamily',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（保険・地域）
       */
      serviceProviderInsuranceRegional: 'serviceProviderInsuranceRegional',
      /**
       * 頻度（本人）
       */
      frequencySelf: 'frequencySelf',
      /**
       * 頻度（家族）
       */
      frequencyFamily: 'frequencyFamily',
      /**
       * 頻度（保険・地域）
       */
      frequencyInsuranceRegional: 'frequencyInsuranceRegional',
      /**
       * 期間
       */
      period: '24Hシート',
      /**
       * 期間開始日
       */
      periodStartDate: '2025/05/01',
      /**
       * 期間終了日
       */
      periodEndDate: '2025/05/10',
      /**
       * 期間（本人）
       */
      periodSelf: '',
      /**
       * 期間開始日（本人）
       */
      periodSelfStartDate: '',
      /**
       * 期間終了日（本人）
       */
      periodSelfEndDate: '',
      /**
       * 期間（家族）
       */
      periodFamily: '',
      /**
       * 期間開始日（家族）
       */
      periodFamilyStartDate: '',
      /**
       * 期間終了日（家族）
       */
      periodFamilyEndDate: '',
      /**
       * 期間（保険・地域）
       */
      periodInsuranceRegional: '',
      /**
       * 期間開始日（保険・地域）
       */
      periodInsuranceRegionalStartDate: '',
      /**
       * 期間終了日（保険・地域）
       */
      periodInsuranceRegionalEndDate: '',
    },
    {
      /**
       * 親画面.ID
       */
      ID: '200',
      /**
       * 表示順
       */
      sort: 2,
      /**
       * ポイント
       */
      goalSupportPoints: '精神的に不安なる出来事があったようで（内容まだわからない状況）1',
      /**
       * インフォーマル
       */
      informalSupport: 'インフォーマル 2',
      /**
       * 介護保険
       */
      nursingCareInsurance: 1,
      /**
       * 本人の取組
       */
      selfEffort: 1,
      /**
       * 具体的な支援の内容（本人）
       */
      selfSupportContent: '一部介助が必要だが、自分から食事摂取 1',
      // /**
      //  * 家族・地域の支援、民間…
      //  */
      // familyCommunitySupport: 1,
      /**
       * 具体的な支援の内容（家族）
       */
      familySupportContent: 'familySupportContent',
      /**
       * 介護保険ｻｰﾋﾞｽ地域支援事業…
       */
      insuranceRegionalSupport: '介護保険ｻｰﾋﾞｽ地域支援事業… 2',
      /**
       * 具体的な支援の内容（保険・地域）
       */
      insuranceRegionalSupportContent: '具体的な支援の内容（保険・地域）',
      /**
       * ※1（本人）
       */
      noteSelf: '○',
      /**
       * ※1（家族）
       */
      noteFamily: '○',
      /**
       * ※1（保険・地域）
       */
      noteInsuranceRegional: '○',
      /**
       * サービス種別
       */
      serviceType: 'サービス種別 2',
      /**
       * サービス種別（本人）
       */
      serviceTypeSelf: 'serviceTypeSelf',
      /**
       * サービス種別（家族）
       */
      serviceTypeFamily: 'serviceTypeFamily',
      /**
       * サービス種別（保険・地域）
       */
      serviceTypeInsuranceRegional: 'serviceTypeInsuranceRegional',
      /**
       * 事業所
       */
      office: '事業所 2',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（本人）
       */
      serviceProviderSelf: 'serviceProviderSelf',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（家族）
       */
      serviceProviderFamily: 'serviceProviderFamily',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（保険・地域）
       */
      serviceProviderInsuranceRegional: 'serviceProviderInsuranceRegional',
      /**
       * 頻度（本人）
       */
      frequencySelf: 'frequencySelf',
      /**
       * 頻度（家族）
       */
      frequencyFamily: 'frequencyFamily',
      /**
       * 頻度（保険・地域）
       */
      frequencyInsuranceRegional: 'frequencyInsuranceRegional',
      /**
       * 期間
       */
      period: '',
      /**
       * 期間開始日
       */
      periodStartDate: '2025/05/01',
      /**
       * 期間終了日
       */
      periodEndDate: '2025/05/10',
      /**
       * 期間（本人）
       */
      periodSelf: '',
      /**
       * 期間開始日（本人）
       */
      periodSelfStartDate: '',
      /**
       * 期間終了日（本人）
       */
      periodSelfEndDate: '',
      /**
       * 期間（家族）
       */
      periodFamily: '',
      /**
       * 期間開始日（家族）
       */
      periodFamilyStartDate: '',
      /**
       * 期間終了日（家族）
       */
      periodFamilyEndDate: '',
      /**
       * 期間（保険・地域）
       */
      periodInsuranceRegional: '',
      /**
       * 期間開始日（保険・地域）
       */
      periodInsuranceRegionalStartDate: '',
      /**
       * 期間終了日（保険・地域）
       */
      periodInsuranceRegionalEndDate: '',
    },
    {
      /**
       * 親画面.ID
       */
      ID: '300',
      /**
       * 表示順
       */
      sort: 3,
      /**
       * ポイント
       */
      goalSupportPoints: '精神的に不安なる出来事があったようで（内容まだわからない状況）1',
      /**
       * インフォーマル
       */
      informalSupport: 'インフォーマル 3',
      /**
       * 介護保険
       */
      nursingCareInsurance: 1,
      /**
       * 本人の取組
       */
      selfEffort: 1,
      /**
       * 具体的な支援の内容（本人）
       */
      selfSupportContent: '一部介助が必要だが、自分から食事摂取 1',
      // /**
      //  * 家族・地域の支援、民間…
      //  */
      // familyCommunitySupport: 1,
      /**
       * 具体的な支援の内容（家族）
       */
      familySupportContent: 'familySupportContent',
      /**
       * 介護保険ｻｰﾋﾞｽ地域支援事業…
       */
      insuranceRegionalSupport: '介護保険ｻｰﾋﾞｽ地域支援事業… 3',
      /**
       * 具体的な支援の内容（保険・地域）
       */
      insuranceRegionalSupportContent: '具体的な支援の内容（保険・地域）',
      /**
       * ※1（本人）
       */
      noteSelf: '○',
      /**
       * ※1（家族）
       */
      noteFamily: '○',
      /**
       * ※1（保険・地域）
       */
      noteInsuranceRegional: '○',
      /**
       * サービス種別
       */
      serviceType: 'サービス種別 3',
      /**
       * サービス種別（本人）
       */
      serviceTypeSelf: 'serviceTypeSelf',
      /**
       * サービス種別（家族）
       */
      serviceTypeFamily: 'serviceTypeFamily',
      /**
       * サービス種別（保険・地域）
       */
      serviceTypeInsuranceRegional: 'serviceTypeInsuranceRegional',
      /**
       * 事業所
       */
      office: '事業所 3',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（本人）
       */
      serviceProviderSelf: 'serviceProviderSelf',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（家族）
       */
      serviceProviderFamily: 'serviceProviderFamily',
      /**
       * ｻｰﾋﾞｽ提供者（事業所）（保険・地域）
       */
      serviceProviderInsuranceRegional: 'serviceProviderInsuranceRegional',
      /**
       * 頻度（本人）
       */
      frequencySelf: 'frequencySelf',
      /**
       * 頻度（家族）
       */
      frequencyFamily: 'frequencyFamily',
      /**
       * 頻度（保険・地域）
       */
      frequencyInsuranceRegional: 'frequencyInsuranceRegional',
      /**
       * 期間
       */
      period: '24Hシート',
      /**
       * 期間開始日
       */
      periodStartDate: '',
      /**
       * 期間終了日
       */
      periodEndDate: '',
      /**
       * 期間（本人）
       */
      periodSelf: '',
      /**
       * 期間開始日（本人）
       */
      periodSelfStartDate: '',
      /**
       * 期間終了日（本人）
       */
      periodSelfEndDate: '',
      /**
       * 期間（家族）
       */
      periodFamily: '',
      /**
       * 期間開始日（家族）
       */
      periodFamilyStartDate: '',
      /**
       * 期間終了日（家族）
       */
      periodFamilyEndDate: '',
      /**
       * 期間（保険・地域）
       */
      periodInsuranceRegional: '',
      /**
       * 期間開始日（保険・地域）
       */
      periodInsuranceRegionalStartDate: '',
      /**
       * 期間終了日（保険・地域）
       */
      periodInsuranceRegionalEndDate: '',
    },
  ],
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28543('A4-3')"
        >GUI01112_表示順変更認定調査票特記事項 (A4-3)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28543('A3')"
        >GUI01112_表示順変更認定調査票特記事項 (A3)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-28543
    v-if="showDialogOr28543"
    v-bind="or28543"
    v-model="or28543Type"
    :oneway-model-value="or28543Data"
  />
  <c-v-row no-gutters>
      <c-v-col>
        <v-btn variant="plain"
          >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
        </v-btn>
      </c-v-col>
    </c-v-row>
    <div style="margin-left: 20px">期間の管理フラグ</div>
    <div style="margin-left: 30px"> 0:日付で管理</div>
    <div style="margin-left: 30px"> 1:文章で管理</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.isPeriodManagementFlag"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">期間のカレンダー取込</div>
    <div style="margin-left: 30px"> 0: ?/?～?/?</div>
    <div style="margin-left: 30px"> 1: ?月?日～?月?日</div>
    <div style="margin-left: 30px"> 2: ?/?</div>
    <div style="margin-left: 30px"> 3: ?月?日</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.calendarImportByPeriod"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div class="pt-5 w-25 pl-5">
      <v-btn @click="onClickOr28543UT('A4-3')"> GUI01112_(A4-3) </v-btn>
    </div>
    <div class="pt-5 w-25 pl-5">
      <v-btn @click="onClickOr28543UT('A3')"> GUI01112_(A3, A4-2) </v-btn>
    </div>
    <div class="pt-5 pl-5">
      <div> 戻り値 </div>
      <div>{{ or28543Type }}</div>
    </div>
</template>
