/**
 * Or26311: 有機体: 印刷順変更モーダル
 * GUI01010_印刷順変更
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DO DUC MANH
 */

/**
 * Or26311の状態を管理する型
 */
export interface Or26311StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * DataTableのデータ型
 */
export interface DataTableData {
  /**
   * DataTableコンポーネント用のデータリスト
   */
  listData: PrintOrderModifiedTableData[]
}

/**
 * 印刷順変更のテーブルデータ型
 */
export interface PrintOrderModifiedTableData {
  /**
   * 表示順の情報
   */
  displayOrder?: {
    /**
     * モデル値（文字列または数値）
     */
    modelValue?: { value: string | number }
  }
  /**
   * 計画書ID
   */
  carePlanId: string
  /**
   * 印刷する計画書のタイトル
   */
  carePlanPrintTitle: {
    onewayModelValue: { value: string },
    unit?: string
  }
}
