import type {
  kikanObj,
  rirekiObj,
  cks52List,
  cks54List,
  cks56List,
} from '~/types/cmn/business/components/Or05349Type'
/**
 * Or35921:週間計画テンプレート
 */
/**
 * 双方向バインドModelValue
 */
export interface Or35921Type {
  /**
   * 事業所ID
   */
  jigyoId?: string
  /**
   * 有効期間ID
   */
  orignValidId?: number
  /**
   * 計画対象期間ID
   */
  sc1Id?: number
  /**
   * 履歴ID
   */
  rirekiId?: number
  /**
   * 作成者ID
   */
  createUserId?: string
  /**
   * 作成日
   */
  createYmd?: string
  /**
   * 処理月
   */
  syoriYm?: string
  /**
   * 計画対象期間
   */
  kikanObj?: kikanObj[]
  /**
   * 週間計画履歴
   */
  rirekiObj?: rirekiObj[]
  /**
   * 週間計画詳細リスト
   */
  cks52List?: cks52List[]
  /**
   * 週間計画日常リスト
   */
  cks54List?: cks54List[]
  /**
   * 週間計画担当者リスト
   */
  cks56List?: cks56List[]
  /**
   * 計画対象期間表示データ
   */
  kikanObjDisp?: kikanObj
  /**
   * 週間計画履歴表示データ
   */
  rirekiObjDisp?: rirekiObj
  /**
   * 週単位以外サービス
   */
  shuUnitOtherService?: string
  /**
   * 保険サービス
   */
  hokenService?: string
}

/**
 * 単方向バインドModelValue
 */
export interface Or35921OnewayType {
  /** アセスメント方式 */
  assessmentMethod?: string
  /** 法人ID */
  houjinId?: string
  /** 施設ID */
  shisetuId?: string
  /** 事業者ID */
  svJigyoId?: string
  /** 利用者ID */
  userId?: string
  /** 種別ID */
  syubetsuId?: string
  /** 取込元 */
  torikomiMoto?: string
  /** Sys略称 */
  sys3Ryaku?: string
  /** 計画書様式 */
  cksFlg?: string
  /** 事業所CD */
  defSvJigyoCd?: string
  /** 事業所ID */
  defSvJigyoId?: string
  /** 職員ID */
  shokuinId?: string
  /** 自事業所 */
  jiOffice?: string
  /** サービス職種IDor種類表示フラグ */
  shuruihyoujiFlg?: boolean
  /** 事業者表示フラグ */
  jigyoShowFlg?: number
  /** サービス項目表示フラグ */
  komokuShowFlg?: boolean
  /** 内容表示フラグ */
  contentShowFlg?: boolean
  /** 使用フラグ */
  useFlg?: string
  /** フォントサイズ */
  fontsize?: string
  /** 表示モード */
  dispmode?: string
  /** 文字位置 */
  textPosition?: string
  /** 文字カラー */
  color?: string
  /** 背景カラー */
  backgroundColor?: string
  /** 時間表示区分 */
  timeDisplay?: string
}

/**
 * OrX0007Type：有機体：ヘッダ部品
 * 双方向バインド領域に保持するデータ構造
 */
export interface ComponentsType {
  /**
   *計画期間変更区分
   */
  PlanTargetPeriodUpdateFlg: string

  /**
   *計画期間ID
   */
  planTargetPeriodId: number
}

/**
 * 有機体：ヘッダ部品
 * OneWayBind領域に保持するデータ構造
 */
export interface ComponentsOnewayType {
  /**
   *計画対象期間data
   */
  planTargetPeriodData: PlanTargetPeriodDataType

  /**
   *履歴data
   */
  rirekiPeriodData: PlanCreateDataType

  /**
   *事業所ID
   */
  officeId: string

  /**
   *利用者ID
   */
  useId: string

  /**
   *計画書１ID
   */
  plan1Id: string

  /**
   *計画対象期間ID
   */
  sc1Id: string
}
