import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or27831: 日課計画入力モーダル
 * GUI01058_日課計画入力
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> 呉李彪
 */
export namespace Or27831Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or27831', seq)
  /**
   *CANCEL
   */
  export const DIALOG_RESULT_CANCEL = 'cancel'
  /**
   *YES
   */
  export const DIALOG_RESULT_YES = 'yes'
  /**
   *NO
   */
  export const DIALOG_RESULT_NO = 'no'
  /** 業務計画ライセンス有無フラグ 1:ライセンス有 */
  export const HAS_LICENSE_YES = '1';
  /** 業務計画ライセンス有無フラグ 0:ライセンス無 */
  export const HAS_LICENSE_NO = '0'
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false

    /**
     * フォーマットの遅れ
     */
    export const PLAN_STYLE = {
      COMMON_SERVICE: 1,
      INDIVIDUAL_SERVICE: 2,
      MAIN_DAILY_ACTIVITIES: 3,
    }

    /**
     * 加算
     */
    export const ADDITION_STATUS = {
      PRESENCE: '有',
      ABSENCE: '無',
    }

    /**
     * 最大コンテンツ長
     */
    export const MAX_CONTENT_LENGTH = 16

    /**
     * "*"
     */
    export const REQUIRED = '*'

    /**
     * 週間プラン デフォルト
     */
    export const DAILY_PLAN = {
      id: '',
      // 時間帯-開始時間
      time: {
        // 時間帯-開始時間
        start: '00:00',
        // 時間帯-終了時間
        end: '00:00',
        // 随時
        atAnyTime: {
          values: ['0'],
        },
      },
      outFrameDisplay: { values: [] as string[] },
      frequency: {
        frequency: {
          values: ['0', '0', '0', '0', '0', '0', '0'],
        },
      },
      commonService: '',
      individualService: '',
      mainDailyActivities: '',
      linkingItem: [],
      commonPerson: '',
      individualPerson: '',
      fontSize: {
        modelValue: '12',
      },
      fontSizeTitle: {
        value: '12',
      },
      textPosition: {
        modelValue: '0',
      },
      background: '#E6E6E6',
      color: '#333333',
      timeDisplay: {
        modelValue: '1',
      },
    }
  }
}
