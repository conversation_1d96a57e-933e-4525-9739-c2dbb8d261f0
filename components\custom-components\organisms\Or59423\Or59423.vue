<script setup lang="ts">
/**
 * GUI00838_ ｱｾｽﾒﾝﾄ複写］「包括」画面ダイアログ
 * 有機体_Or59423
 *
 * @description
 * ［ｱｾｽﾒﾝﾄ複写］「包括」画面ダイアログ
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import type { Or61005StateType } from '../Or61005/Or61005.type'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { TeX0008Logic } from '../../template/TeX0008/TeX0008.logic'
import { Or03243Const } from '../Or03243/Or03243.constants'
import { OrX0096Const } from '../OrX0096/OrX0096.constants'
import type { Or03243OnewayType } from '../Or03243/Or34243.type'
import type { IssuseWholeItemType } from '../OrX0096/OrX0096.type'
import { TeX0008Const } from '../../template/TeX0008/TeX0008.constants'
import { Or03243Logic } from '../Or03243/Or03243.logic'
import type { Or03249OnewayType } from '../Or03249/Or03249.type'
import { Or03249Const } from '../Or03249/Or03249.constants'
import { Or03249Logic } from '../Or03249/Or03249.logic'
import type { Or03244OnewayType } from '../Or03244/Or34244.type'
import { Or03244Const } from '../Or03244/Or03244.constants'
import { Or03244Logic } from '../Or03244/Or03244.logic'
import type { Or03250OnewayType } from '../Or03250/Or03250.type'
import { Or03250Const } from '../Or03250/Or03250.constants'
import { Or03250Logic } from '../Or03250/Or03250.logic'
import type { Or03240OnewayType } from '../Or03240/Or03240.type'
import { Or03240Const } from '../Or03240/Or03240.constants'
import { Or03240Logic } from '../Or03240/Or03240.logic'
import { Or03237Const } from '../Or03237/Or03237.constants'
import { Or03245Const } from '../Or03245/Or03245.constants'
import type { Or03237OnewayType } from '../Or03237/Or03237.type'
import type { Or03245OnewayType } from '../Or03245/Or03245.type'
import { Or03237Logic } from '../Or03237/Or03237.logic'
import { Or03245Logic } from '../Or03245/Or03245.logic'
import type { TeX0008IssuseWholeList } from '../../template/TeX0008/TeX0008.type'
import type { Mo00045Type } from '../Or00386/Or00386.type'
import { Or59423Const } from './Or59423.constants'
import { Or59423Logic } from './Or59423.logic'
import type { Or59423ConfirmType } from './Or59423.type'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type {
  Or59423EventType,
  Or59423OnewayType,
} from '~/types/cmn/business/components/Or59423Type'
import type { OrX0077OnewayType } from '~/types/cmn/business/components/OrX0077Type'
import { useScreenOneWayBind, useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type {
  Mo01334Headers,
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type {
  DuplicateAssessmentSelectInEntity,
  DuplicateAssessmentSelectOutEntity,
  DuplicateHistoryChangeSelectInEntity,
  DuplicateHistoryChangeSelectOutEntity,
} from '~/repositories/cmn/entities/DuplicateAssessmentSelectInEntity'
import type { TeX0008Type } from '~/types/cmn/business/components/TeX0008Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type {
  DuplicateAssessmentUpdateInEntity,
  DuplicateAssessmentUpdateOutEntity,
} from '~/repositories/cmn/entities/duplicateAssessmentUpdateInEntity'
import { DIALOG_BTN, SPACE_WAVE } from '~/constants/classification-constants'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or59423OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * emit
 **************************************************/
const _emit = defineEmits(['confirm'])

/**************************************************
 * 変数定義
 **************************************************/
/**
 * 共通情報ストア
 */
const _systemCommonStore = useSystemCommonsStore()

/**
 * ヘーダチェックボックスデータ
 */
const headerCheckBoxValue = ref(false)

/**
 * 利用者ID
 */
const userId = ref('')

/**
 * データありフラグ
 */
const isDataExist = ref(false)

/**
 * ロード状態制御
 */
const isLoading = ref(false)

const { t } = useI18n()

const orX0077 = ref({ uniqueCpId: '' })
const orX0096 = ref({ uniqueCpId: '' })
const or03243 = ref({ uniqueCpId: '' })
const or03250 = ref({ uniqueCpId: '' })
const or03249 = ref({ uniqueCpId: '' })
const or03240 = ref({ uniqueCpId: '' })
const or03244 = ref({ uniqueCpId: '' })
const or03245 = ref({ uniqueCpId: '' })
const or03237 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21814_2 = ref({ uniqueCpId: '' })

/**
 * 複数タブ用データ
 */
const multipleInfo = ref<DuplicateAssessmentSelectOutEntity['data']['multipleList']>([])

/**
 * 介護課題全般リスト
 * 国際化対応ため、定数ファイルから移行
 */
const issuseWholeList: TeX0008IssuseWholeList[] = [
  {
    label: t('label.assessment-comprehensive-common-problem1'),
    key: '1',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '6', '7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem2'),
    key: '2',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '6', '7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem3'),
    key: '3',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '6', '7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem4'),
    key: '4',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '6', '7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem5'),
    key: '5',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem6'),
    key: '6',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem7'),
    key: '7',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem8'),
    key: '8',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem9'),
    key: '9',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem10'),
    key: '10',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem11'),
    key: '11',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem12'),
    key: '12',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem13'),
    key: '13',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['2'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem14'),
    key: '14',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['2'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem15'),
    key: '15',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['2'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem16'),
    key: '16',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['2'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem17'),
    key: '17',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['2'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem18'),
    key: '18',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['3'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem19'),
    key: '19',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['3'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem20'),
    key: '20',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['3'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem21'),
    key: '21',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['3'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem22'),
    key: '109',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem23'),
    key: '22',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem24'),
    key: '23',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem25'),
    key: '24',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem26'),
    key: '25',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem27'),
    key: '26',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem28'),
    key: '27',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem29'),
    key: '28',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['4'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem30'),
    key: '29',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['5'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem31'),
    key: '30',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['5'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem32'),
    key: '31',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['5'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem33'),
    key: '32',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['5'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem34'),
    key: '33',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['5'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem35'),
    key: '34',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem36'),
    key: '35',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem37'),
    key: '36',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem38'),
    key: '37',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem39'),
    key: '38',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem40'),
    key: '39',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem41'),
    key: '40',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem42'),
    key: '41',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem43'),
    key: '42',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem44'),
    key: '43',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['6'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem45'),
    key: '44',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem46'),
    key: '45',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem47'),
    key: '46',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem48'),
    key: '47',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem49'),
    key: '48',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem50'),
    key: '49',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem51'),
    key: '50',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem52'),
    key: '51',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem53'),
    key: '52',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem54'),
    key: '53',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem55'),
    key: '54',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['7'],
  },
  {
    label: t('label.assessment-comprehensive-common-problem-other'),
    key: '55',
    isPlanningFlg: false,
    isHaveIssuesFlg: false,
    showScreenId: ['1', '2', '3', '4', '5', '6', '7'],
  },
]

/**
 * ロカールOneway
 */
const localOneway = reactive({
  orx0077Oneway: {
    // 複写ダイアログ
    mo00024Oneway: {
      maxWidth: '1680px',
      height: '880px',
      persistent: true,
      showCloseBtn: true,
      mo01344Oneway: {
        name: 'Or35672',
        toolbarTitle: t('label.assessment-duplicate'),
        toolbarName: 'Or35672ToolBar',
        // ツールバータイトルの左寄せ
        toolbarTitleCenteredFlg: false,
        showCardActions: true,
      },
    } as Mo00024OnewayType,
  } as OrX0077OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 追加ボタン設置
  mo00609AddBtnOneway: {
    btnLabel: t('btn.confirm'),
    width: '90px',
    tooltipText: t('tooltip.confirm'),
  } as Mo00609OnewayType,
  mo01334_1Oneway: {
    headers: [] as Mo01334Headers[],
    items: [] as Mo01334Items[],
    height: '135px',
  } as Mo01334OnewayType,
  mo01334_2Oneway: {
    headers: [] as Mo01334Headers[],
    items: [] as Mo01334Items[],
    height: '135px',
  } as Mo01334OnewayType,
  mo00043OnewayType: {} as Mo00043OnewayType,
  or03243Oneway: {
    screenMode: Or59423Const.DEFAULT.CHILDREN_SCREEN_MODE,
  } as Or03243OnewayType,
  or03250Oneway: {
    screenMode: Or59423Const.DEFAULT.CHILDREN_SCREEN_MODE,
  } as Or03250OnewayType,
  or03249Oneway: {
    screenMode: Or59423Const.DEFAULT.CHILDREN_SCREEN_MODE,
  } as Or03249OnewayType,
  or03240Oneway: {
    screenMode: Or59423Const.DEFAULT.CHILDREN_SCREEN_MODE,
  } as Or03240OnewayType,
  or03244Oneway: {
    screenMode: Or59423Const.DEFAULT.CHILDREN_SCREEN_MODE,
  } as Or03244OnewayType,
  or03245Oneway: {
    screenMode: Or59423Const.DEFAULT.CHILDREN_SCREEN_MODE,
  } as Or03245OnewayType,
  or03237Oneway: {
    screenMode: Or59423Const.DEFAULT.CHILDREN_SCREEN_MODE,
  } as Or03237OnewayType,
  mo01354Oneway: {
    headers: [] as Mo01354Headers[],
    height: '299',
    showSelect: false,
    selectStrategy: 'all',
    mandatory: false,
    showDragIndicatorFlg: false,
    useDefaultHeader: false,
    columnMinWidth: {
      columnWidths: [72, 160, 160],
    } as ResizableGridBinding,
  } as Mo01354OnewayType,
  mo01338Oneway: {
    /** 値のフォントの太さ */
    valueFontWeight: 'bold',
    /** 値 */
    value: t('label.assessment-duplicate-multiple-tab-description'),
    /** カスタムクラス */
    customClass: new CustomClass({
      outerClass: '',
      labelClass: 'd-none',
      itemClass: '',
      itemStyle: 'color: rgb(var(--v-theme-red-700));font-size: 16px;',
    }),
  } as Mo01338OnewayType,
})

/**
 * ロカール
 */
const local = reactive({
  mo00043: {
    id: '',
  } as Mo00043Type,
  commonInfo: {} as TeX0008Type,
  mo01334_1: {
    value: '',
  } as Mo01334Type,
  mo01334_2: {
    value: '',
  } as Mo01334Type,
  // 複数タブ一覧
  mo01354: {
    values: {
      selectedRowId: '',
      selectedRowIds: [] as string[],
      items: [
        {
          id: '1',
          section: {
            value: t('label.assessment-comprehensive-tab-name-1'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or59423Const.DEFAULT.NULL_DATA_DISPLAY,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '2',
          section: {
            value: t('label.assessment-comprehensive-tab-name-2'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or59423Const.DEFAULT.NULL_DATA_DISPLAY,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '3',
          section: {
            value: t('label.assessment-comprehensive-tab-name-3'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or59423Const.DEFAULT.NULL_DATA_DISPLAY,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '4',
          section: {
            value: t('label.assessment-comprehensive-tab-name-4'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or59423Const.DEFAULT.NULL_DATA_DISPLAY,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '5',
          section: {
            value: t('label.assessment-comprehensive-tab-name-5'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or59423Const.DEFAULT.NULL_DATA_DISPLAY,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '6',
          section: {
            value: t('label.assessment-comprehensive-tab-name-6'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or59423Const.DEFAULT.NULL_DATA_DISPLAY,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
        {
          id: '7',
          section: {
            value: t('label.assessment-comprehensive-tab-name-7'),
            unit: '',
          } as Mo01337OnewayType,
          historyRegisterCompleted: {
            value: Or59423Const.DEFAULT.NULL_DATA_DISPLAY,
            unit: '',
          } as Mo01337OnewayType,
          selectable: false,
        },
      ] as Mo01334Items[],
    },
  } as Mo01354Type,
})

/**************************************************
 * computed
 **************************************************/
/**
 * 警告ダイアログ表示フラグ
 */
const showWarningDialog = computed(() => {
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

const showInfoDialog = computed(() => {
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})

const showConfirmDialog = computed(() => {
  return Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 複数選択ヘッダーチェックボックス不確定状態
 */
const setIndeterminate = computed(() => {
  const some = local.mo01354.values.items
    .filter(
      (item) =>
        (item.historyRegisterCompleted as Mo00045Type).value !==
        Or59423Const.DEFAULT.NULL_DATA_DISPLAY
    )
    .some((item) => item.selectable)
  const every = local.mo01354.values.items
    .filter(
      (item) =>
        (item.historyRegisterCompleted as Mo00045Type).value !==
        Or59423Const.DEFAULT.NULL_DATA_DISPLAY
    )
    .every((item) => item.selectable)
  return some && !every
})

/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [OrX0077Const.CP_ID(1)]: orX0077.value,
  [Or03243Const.CP_ID(1)]: or03243.value,
  [Or03250Const.CP_ID(1)]: or03250.value,
  [Or03249Const.CP_ID(1)]: or03249.value,
  [Or03240Const.CP_ID(1)]: or03240.value,
  [Or03244Const.CP_ID(1)]: or03244.value,
  [Or03245Const.CP_ID(1)]: or03245.value,
  [Or03237Const.CP_ID(1)]: or03237.value,
  [OrX0096Const.CP_ID(1)]: orX0096.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
})

const { setState } = useScreenOneWayBind<Or61005StateType>({
  cpId: Or59423Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      OrX0077Logic.state.set({
        uniqueCpId: orX0077.value.uniqueCpId,
        state: { isOpen: value ?? Or59423Const.DEFAULT.IS_OPEN },
      })
    },
  },
})

/**************************************************
 * 関数定義
 **************************************************/
/**
 * ダイアログを閉じる関数
 */
const close = () => {
  setState({ isOpen: false })
}

/**
 * コントロール初期化
 */
const initControl = () => {
  getCommonInfo()
  setChildrenScreenData()
  // 計画期間一覧 ヘッダー
  localOneway.mo01334_1Oneway.headers = [
    {
      title: t('label.plan-period'),
      key: 'planPeriod',
      sortable: false,
      minWidth: '204',
    },
    {
      title: t('label.within-the-period-number-of-history'),
      key: 'numberOfWithinThePeriodHistory',
      sortable: false,
      minWidth: '72',
    },
    {
      title: t('label.office-name'),
      key: 'officeName',
      sortable: false,
      minWidth: '180',
    },
  ] as Mo01334Headers[]

  // 履歴一覧 ヘッダー
  localOneway.mo01334_2Oneway.headers = [
    {
      title: t('label.create-date'),
      key: 'createDate',
      sortable: false,
      minWidth: '100',
    },
    {
      title: t('label.author'),
      key: 'createUser',
      sortable: false,
      minWidth: '188',
    },
  ] as Mo01334Headers[]

  if (props.onewayModelValue.planPeriodFlg !== '1') {
    localOneway.mo01334_2Oneway.headers.push({
      title: t('label.office-name'),
      key: 'officeName',
      sortable: false,
      minWidth: '46',
    })
  }

  // タブを初期化
  localOneway.mo00043OnewayType.tabItems = [
    {
      id: '1',
      title: props.onewayModelValue.title,
      tooltipText: props.onewayModelValue.title,
      tooltipLocation: 'top',
    },
    {
      id: '2',
      title: t('label.multiple'),
      tooltipText: t('label.multiple'),
      tooltipLocation: 'top',
    },
  ]
  // 複数複写テーブルヘーダ初期化
  localOneway.mo01354Oneway.headers = [
    {
      title: t('label.section'),
      key: 'section',
      sortable: false,
    },
    {
      title: t('label.history-register-completed'),
      key: 'historyRegisterCompleted',
      sortable: false,
    },
  ] as Mo01334Headers[]

  // 各ダイアログを初期化
  // 警告ダイアログを初期化
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })

  // メッセージダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
    },
  })

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
    },
  })
}

/**
 * 共通情報取得
 */
const getCommonInfo = () => {
  const commonInfo = TeX0008Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo = commonInfo
  }
}

/**
 * 共通情報設定
 *
 * @param commonInfo - 共通情報
 */
const setCommonInfo = (commonInfo: TeX0008Type) => {
  const existedData = local.commonInfo
  Or59423Logic.data.set({
    uniqueCpId: props.uniqueCpId + Or59423Const.CP_ID(0),
    value: {
      duplicateInfo: {
        ninteiFormF: commonInfo?.ninteiFormF ?? existedData?.ninteiFormF,
        activeTabId: commonInfo?.activeTabId ?? existedData?.activeTabId,
        jigyoId: commonInfo?.jigyoId ?? existedData?.jigyoId,
        houjinId: commonInfo?.houjinId ?? existedData?.houjinId,
        shisetuId: commonInfo?.shisetuId ?? existedData?.shisetuId,
        userId: commonInfo?.userId ?? existedData?.userId,
        syubetsuId: commonInfo?.syubetsuId ?? existedData?.syubetsuId,
        createYmd: commonInfo?.createYmd ?? existedData?.createYmd,
        historyUpdateKbn: commonInfo?.historyUpdateKbn ?? existedData?.historyUpdateKbn,
        sc1Id: commonInfo?.sc1Id ?? existedData?.sc1Id,
        recId: commonInfo?.recId ?? existedData?.recId,
        cc1Id: commonInfo?.cc1Id ?? existedData?.cc1Id,
        createUserId: commonInfo?.createUserId ?? existedData?.createUserId,
        svJigyoId: commonInfo?.svJigyoId ?? existedData?.svJigyoId,
        updateKbn: commonInfo?.updateKbn ?? existedData?.updateKbn,
        planPeriodFlg: commonInfo?.planPeriodFlg ?? existedData?.planPeriodFlg,
      },
    },
  })
}

/**
 * イベント発火
 *
 * @param event - イベント
 */
const setEvent = (event: Or59423EventType) => {
  Or59423Logic.event.set({
    uniqueCpId: props.uniqueCpId + Or59423Const.CP_ID(0),
    events: {
      reloadEvent: event.reloadEvent,
    },
  })
}

/**
 * 初期情報取得APIを呼び出す
 */
const getInitDataInfo = async () => {
  const inputData: DuplicateAssessmentSelectInEntity = {
    svJigyoIdList: (_systemCommonStore.getSvJigyoIdList as string[]).join(',') ?? '',
    userid: _systemCommonStore.getUserSelectSelfId(orX0077.value.uniqueCpId) ?? '',
    syubetsuId: _systemCommonStore.getSyubetu ?? '',
    shisetuId: _systemCommonStore.getShisetuId ?? '',
    sc1Id: '',
    kikanFlg: local.commonInfo.planPeriodFlg ?? '1',
  }

  const resData: DuplicateAssessmentSelectOutEntity = await ScreenRepository.select(
    'duplicateAssessmentSelect',
    inputData
  )

  if (resData.data) {
    // 計画期間一覧データを作成
    if (local.commonInfo.planPeriodFlg === TeX0008Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
      localOneway.mo01334_1Oneway.items = resData.data.planPeriodInfo.map((item) => {
        return {
          id: item.sc1Id,
          numberOfWithinThePeriodHistory: item.periodCnt ?? Or59423Const.DEFAULT.NULL_DATA_DISPLAY,
          planPeriod: `${item.startYmd ?? ''}${SPACE_WAVE}${item.endYmd ?? ''}`,
          modifiedCnt: item.modifiedCnt,
          svJigyoId: item.svJigyoId,
          officeName: item.jigyoRyakuKnj,
        }
      })
      // 一行目を選択状態にする
      local.mo01334_1.value = localOneway.mo01334_1Oneway.items[0].id
    }
    // 歴史一覧データを作成
    localOneway.mo01334_2Oneway.items = resData.data.rirekiInfo.map((item) => {
      return {
        createDate: item.createYmd,
        createUser: item.shokuKnj,
        officeName: item.jigyoRyakuKnj,
        id: item.cc1Id,
        // 元データ保存
        cc1Id: item.cc1Id,
        sc1Id: item.sc1Id,
        createYmd: item.createYmd,
        shokuId: item.shokuId,
        shokuKnj: item.shokuKnj,
        svJigyoId: item.svJigyoId,
        jigyoRyakuKnj: item.jigyoRyakuKnj,
      }
    })
    if (localOneway.mo01334_2Oneway.items.length > 0) {
      // 一行目を選択状態にする
      local.mo01334_2.value = localOneway.mo01334_2Oneway.items[0].id
      // 該当歴史の期間ID、ケアチェックID保存
      setCommonInfo({
        sc1Id: localOneway.mo01334_2Oneway.items[0].sc1Id as string,
        cc1Id: localOneway.mo01334_2Oneway.items[0].cc1Id as string,
        activeTabId: props.onewayModelValue.tabId,
      })

      isDataExist.value = true
      setEvent({ reloadEvent: true })
    } else {
      isDataExist.value = false
    }

    // 複数タブデータを保存する
    multipleInfo.value = resData.data.multipleList
    const items = local.mo01354.values.items
    // Flg値が1の場合、●を表示、以外の場合 - を表示
    // 食事
    if (multipleInfo.value.length > 0) {
      ;(items[0].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss1Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 排泄
      ;(items[1].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss2Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 入浴
      ;(items[2].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss3Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 洗面
      ;(items[3].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss4Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 基本
      ;(items[4].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss5Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 医療
      ;(items[5].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss6Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 心理
      ;(items[6].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss7Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
    }
    // 編集アイコンを非表示にする
    const component = document.getElementById('or59423Component')
    if (component) {
      // 対象アイコン
      const iconTextList = ['edit_square']
      // 対象ラベル
      const labelTextList = [t('label.assessment-home-6-1-servey-ledger-import')]

      const iElements = component.querySelectorAll('i')
      iElements.forEach((el) => {
        if (el.textContent) {
          if (iconTextList.includes(el.textContent?.trim())) {
            el.style.display = Or59423Const.DEFAULT.ICON_DISPLAY_FLG_NONE
          }
        }
      })

      const serveyLedgerImportLabelElement = component.querySelectorAll('label')
      serveyLedgerImportLabelElement.forEach((el) => {
        if (el.textContent) {
          if (labelTextList.includes(el.textContent?.trim())) {
            el.style.display = Or59423Const.DEFAULT.ICON_DISPLAY_FLG_NONE
          }
        }
      })
    }
  }
}

/**
 * 画面IDより、介護課題リストを作成する
 *
 * @param screenId - 画面ID
 */
function createIssuseTabList(screenId: string): IssuseWholeItemType[] {
  let b4cd = 0
  if (screenId === Or03243Const.DEFAULT.TAB_ID) {
    b4cd = 1
  } else if (screenId === Or03250Const.DEFAULT.TAB_ID) {
    b4cd = 14
  } else if (screenId === Or03249Const.DEFAULT.TAB_ID) {
    b4cd = 25
  } else if (screenId === Or03245Const.DEFAULT.TAB_ID) {
    b4cd = 35
  } else if (screenId === Or03240Const.DEFAULT.TAB_ID) {
    b4cd = 49
  } else if (screenId === Or03237Const.DEFAULT.TAB_ID) {
    b4cd = 60
  } else if (screenId === Or03244Const.DEFAULT.TAB_ID) {
    b4cd = 76
  }
  const resultArr = issuseWholeList
    .filter((issuseItem) => issuseItem.showScreenId?.includes(screenId))
    .map((issuseItem, index): IssuseWholeItemType => {
      return {
        ...issuseItem,
        b4cd: String(index + b4cd),
        cc31Id: '',
        modifiedCnt: Or59423Const.DEFAULT.DATA_DEFAULT,
      }
    })
  return resultArr
}
/**
 * 画面IDより、タブリストを作成
 */
function setChildrenScreenData() {
  if (props.onewayModelValue.tabId === Or03243Const.DEFAULT.TAB_ID) {
    localOneway.or03243Oneway.questionList = createIssuseTabList(Or03243Const.DEFAULT.TAB_ID)
  } else if (props.onewayModelValue.tabId === Or03250Const.DEFAULT.TAB_ID) {
    localOneway.or03250Oneway.questionList = createIssuseTabList(Or03250Const.DEFAULT.TAB_ID)
  } else if (props.onewayModelValue.tabId === Or03249Const.DEFAULT.TAB_ID) {
    localOneway.or03249Oneway.questionList = createIssuseTabList(Or03249Const.DEFAULT.TAB_ID)
  } else if (props.onewayModelValue.tabId === Or03240Const.DEFAULT.TAB_ID) {
    localOneway.or03240Oneway.questionList = createIssuseTabList(Or03240Const.DEFAULT.TAB_ID)
  } else if (props.onewayModelValue.tabId === Or03244Const.DEFAULT.TAB_ID) {
    localOneway.or03244Oneway.questionList = createIssuseTabList(Or03244Const.DEFAULT.TAB_ID)
  } else if (props.onewayModelValue.tabId === Or03245Const.DEFAULT.TAB_ID) {
    localOneway.or03245Oneway.questionList = createIssuseTabList(Or03245Const.DEFAULT.TAB_ID)
  } else if (props.onewayModelValue.tabId === Or03237Const.DEFAULT.TAB_ID) {
    localOneway.or03237Oneway.questionList = createIssuseTabList(Or03237Const.DEFAULT.TAB_ID)
  }
}

/**
 * タブごとに処理を行う
 *
 * @description
 * 「複数」以外のタブの場合、確定ボタン押下時に、選択されている履歴に対するタブデータを上書する
 * 「複数」のタブの場合、確定ボタン押下時に、選択されている履歴に対する複数のタブデータを上書する
 */
const confirmClick = async () => {
  // 履歴が未選択の場合
  // メッセージID：i.cmn.11289
  // メッセージ内容："履歴を選択してください。"
  if (
    local.mo01334_2.value === undefined ||
    local.mo01334_2.value === '' ||
    isDataExist.value === false
  ) {
    setShowWarningDialog(t('message.i-cmn-11289'))
    // 処理終了
    return
  }

  // 各タブ選択時
  // 複写対象の履歴を選択済みの場合
  if (local.mo00043.id === Or59423Const.DEFAULT.EMIT_EVENT_FLG_SCREEN) {
    let copyData: object = {}
    const dialogResult = await getConfirmDialogResult()

    if (dialogResult === DIALOG_BTN.YES) {
      switch (local.commonInfo.activeTabId) {
        // 食事画面画面
        case Or03243Const.DEFAULT.TAB_ID:
          copyData = Or03243Logic.data.get(or03243.value.uniqueCpId) ?? {}
          break
        // 排泄画面
        case Or03250Const.DEFAULT.TAB_ID:
          copyData = Or03250Logic.data.get(or03250.value.uniqueCpId) ?? {}
          break
        // 入浴画面
        case Or03249Const.DEFAULT.TAB_ID:
          copyData = Or03249Logic.data.get(or03249.value.uniqueCpId) ?? {}
          break
        // 基本画面
        case Or03240Const.DEFAULT.TAB_ID:
          copyData = Or03240Logic.data.get(or03240.value.uniqueCpId) ?? {}
          break
        // 心理画面
        case Or03244Const.DEFAULT.TAB_ID:
          copyData = Or03244Logic.data.get(or03244.value.uniqueCpId) ?? {}
          break
        // 洗面画面
        case Or03245Const.DEFAULT.TAB_ID:
          copyData = Or03245Logic.data.get(or03245.value.uniqueCpId) ?? {}
          break
        // 医療画面
        case Or03237Const.DEFAULT.TAB_ID:
          copyData = Or03237Logic.data.get(or03237.value.uniqueCpId) ?? {}
          break
      }
      _emit('confirm', {
        copyData,
        duplicateMode: Or59423Const.DEFAULT.DIALOG_RESULT_TYPE_SECTION,
        careCheckId: local.mo01334_2.value,
        planPeriodId: local.mo01334_1.value,
      } as Or59423ConfirmType)
      close()
    } else {
      // 処理終了
      return
    }
  }
  // 複数複写画面
  else if (local.mo00043.id === Or59423Const.DEFAULT.EMIT_EVENT_FLG_HISTORY) {
    // "複数"タブ選択時
    // 複写対象の画面を未選択の場合
    const noSelectedFlg = local.mo01354.values.items.every((item) => !item.selectable)
    // ■以下のメッセージを表示
    // i.cmn.11289
    if (noSelectedFlg) {
      setShowWarningDialog(t('message.i-cmn-11289'))
      return
    } else {
      // "複数"タブ選択時
      // 複写対象の画面を1画面以上選択済みの場合
      // 情報収集複写複数画面確定APIを呼び出す
      const dialogResult = await getConfirmDialogResult()
      if (dialogResult === DIALOG_BTN.YES) {
        const result = await updateduplicateDataInfo()
        _emit('confirm', {
          duplicateMode: Or59423Const.DEFAULT.DIALOG_RESULT_TYPE_HISTORY,
          careCheckId: result?.gdlId ?? '',
          planPeriodId: result?.scId ?? '',
        } as Or59423ConfirmType)
        // ダイアログを閉じる
        close()
      } else {
        // 処理終了
      }
    }
  }
}

/**
 * 警告ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 */
const setShowWarningDialog = (paramDialogText: string) => {
  // 警告ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
}

/**
 * 確認ダイアログ結果取得
 */
const getConfirmDialogResult = (): Promise<'yes' | 'no'> => {
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      dialogText: t('message.i-cmn-10193', [t('label.assessment-home-6-5-column-assessment')]),
      isOpen: true,
    },
  })

  // 閉じるタイミングで監視を実行
  return new Promise((resolve) => {
    let result: 'yes' | 'no'
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (!newValue) {
          const event = Or21814Logic.event.get(or21814_2.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            result = DIALOG_BTN.YES
          }
          if (event?.secondBtnClickFlg) {
            result = DIALOG_BTN.NO
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814_2.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              closeBtnClickFlg: false,
            },
          })
          resolve(result)
        }
      },
      { once: true }
    )
  })
}

/**
 * ヘーダチェックボックス変更処理
 */
const headerCheckBoxChange = () => {
  // 「表頭列チェックボックス」チェックON
  // 複数セクション一覧の明細を全選択する。
  // 「表頭列チェックボックス」チェックOFF
  // 複数セクション一覧の明細を全解除する。
  local.mo01354.values.items.forEach((item) => {
    // 禁止されたチェックボックス抜く
    if (
      (item.historyRegisterCompleted as Mo01337OnewayType).value !==
      Or59423Const.DEFAULT.NULL_DATA_DISPLAY
    ) {
      item.selectable = headerCheckBoxValue.value
    }
  })
}

/**
 * 複数複写APIを呼び出す
 */
const updateduplicateDataInfo = async () => {
  const inputData: DuplicateAssessmentUpdateInEntity = {
    // 法人ID：親画面.法人ID
    houjinId: local.commonInfo.houjinId ?? '',
    // 施設ID：親画面.施設ID
    shisetuId: local.commonInfo.shisetuId ?? '',
    // 利用者ID：画面.利用者ID
    userId: userId.value,
    // 事業者ID：親画面.事業者ID
    svJigyoId: _systemCommonStore.getSvJigyoId ?? local.commonInfo.svJigyoId ?? '',
    // 期間管理フラグ：共通情報.期間管理フラグ
    kikanFlg: local.commonInfo.planPeriodFlg ?? '',
    // 複写先ケアチェックID：親画面.ケアチェックID
    tgtGdlId: local.commonInfo.cc1Id ?? '',
    // 複写先期間ID：親画面.期間ID
    tgtSc1Id: local.commonInfo.sc1Id ?? '',
    // 画面Noリスト
    //   画面No:選択されたセクションNo
    //   更新回数:選択されたセクション対応のＨＫ＿ケアチェック表の更新回数"
    objList: [],
    // 作成日：親画面.作成日
    createYmd: local.commonInfo.createYmd ?? '',
    // 記載者ID：親画面.記載者ID
    shokuId: local.commonInfo.createUserId ?? '',
    // 期間ID：選択計画期間ID
    defSc1Id: local.mo01334_1.value,
    // ケアチェックID：選択されたケアチェックID
    defGdlId: local.mo01334_2.value,
    /** 種別ID */
    syubetsuId: _systemCommonStore.getSyubetu ?? '',
  }
  // 画面Noリスト
  inputData.objList = local.mo01354.values.items
    .map((item, index) => {
      if (item.selectable) {
        return {
          // 画面No:選択されたセクションNo
          typeId: (index + 1).toString(),
        }
      } else {
        return null
      }
    })
    .filter((item) => item !== null)

  const resData: DuplicateAssessmentUpdateOutEntity = await ScreenRepository.update(
    'duplicateAssessmentUpdate',
    inputData
  )
  if (resData.data) {
    return resData.data
  }
}
/**
 * 利用者ID取得
 *
 * @param newUserId - 選択したユーザーID
 */
async function getUserId(newUserId: string) {
  console.log(newUserId, 'newUserId')
  console.log(userId, 'userId')
  if (isLoading.value || newUserId === '' || newUserId === userId.value) {
    return
  }

  userId.value = newUserId

  await getInitDataInfo()
}

/**
 * 歴史変更処理
 *
 * @param newCc1Id - ケアチェックID
 */
const historyChange = async (newCc1Id: string) => {
  // 歴史IDを共通情報に設定する
  setCommonInfo({ cc1Id: newCc1Id })
  // 複数複写画面のデータをクリア
  local.mo01354.values.items.forEach((item) => {
    item.selectable = false
  })
  // ヘーダチェックボックスデータクリア
  headerCheckBoxValue.value = false
  const inputData: DuplicateHistoryChangeSelectInEntity = {
    sc1Id: local.mo01334_1.value,
    defGdlId: newCc1Id,
  }

  const resData: DuplicateHistoryChangeSelectOutEntity = await ScreenRepository.select(
    'duplicateHistoryChangeSelect',
    inputData
  )

  if (resData.data) {
    multipleInfo.value = resData.data.multipleList
    const items = local.mo01354.values.items
    // Flg値が1の場合、●を表示、以外の場合“”を表示
    // 食事
    // データが存在する場合
    if (multipleInfo.value.length > 0) {
      ;(items[0].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss1Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 排泄
      ;(items[1].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss2Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 入浴
      ;(items[2].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss3Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 洗面
      ;(items[3].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss4Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 基本
      ;(items[4].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss5Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 医療
      ;(items[5].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss6Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 心理
      ;(items[6].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss7Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
    }
    setCommonInfo({
      cc1Id: local.mo01334_2.value,
      sc1Id: local.mo01334_1.value,
    })
    setEvent({ reloadEvent: true })
  }
}

/**
 * 計画期間ID変更処理
 *
 * @param planPeriodId - 計画期間ID
 */
const planPeriodIdChange = async (planPeriodId: string) => {
  localOneway.mo01334_2Oneway.items = []
  local.mo01334_2.value = ''
  // 複数複写画面のデータをクリア
  local.mo01354.values.items.forEach((item) => {
    item.selectable = false
  })
  // ヘーダチェックボックスデータクリア
  headerCheckBoxValue.value = false
  const inputData: DuplicateAssessmentSelectInEntity = {
    svJigyoIdList: (_systemCommonStore.getSvJigyoIdList as string[]).join(',') ?? '',
    userid: _systemCommonStore.getUserSelectSelfId(orX0077.value.uniqueCpId) ?? '',
    syubetsuId: _systemCommonStore.getSyubetu ?? '',
    shisetuId: _systemCommonStore.getShisetuId ?? '',
    sc1Id: planPeriodId,
    kikanFlg: local.commonInfo.planPeriodFlg ?? '1',
  }

  const resData: DuplicateAssessmentSelectOutEntity = await ScreenRepository.select(
    'duplicateAssessmentSelect',
    inputData
  )

  if (resData.data) {
    // 歴史一覧データを作成
    localOneway.mo01334_2Oneway.items = resData.data.rirekiInfo.map((item) => {
      return {
        createDate: item.createYmd,
        createUser: item.shokuKnj,
        officeName: item.jigyoRyakuKnj,
        id: item.cc1Id,
        // 元データ保存
        cc1Id: item.cc1Id,
        sc1Id: item.sc1Id,
        createYmd: item.createYmd,
        shokuId: item.shokuId,
        shokuKnj: item.shokuKnj,
        svJigyoId: item.svJigyoId,
        jigyoRyakuKnj: item.jigyoRyakuKnj,
      }
    })
    if (localOneway.mo01334_2Oneway.items.length > 0) {
      // 一行目を選択状態にする
      local.mo01334_2.value = localOneway.mo01334_2Oneway.items[0].id
      // 該当歴史の期間ID、ケアチェックID保存
      setCommonInfo({
        sc1Id: localOneway.mo01334_2Oneway.items[0].sc1Id as string,
        cc1Id: localOneway.mo01334_2Oneway.items[0].cc1Id as string,
        activeTabId: props.onewayModelValue.tabId,
      })

      isDataExist.value = true
    } else {
      isDataExist.value = false
    }

    // 複数タブデータを保存する
    multipleInfo.value = resData.data.multipleList
    const items = local.mo01354.values.items
    // Flg値が1の場合、●を表示、以外の場合“”を表示
    if (multipleInfo.value.length > 0) {
      // 食事
      ;(items[0].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss1Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 排泄
      ;(items[1].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss2Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 入浴
      ;(items[2].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss3Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 洗面
      ;(items[3].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss4Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 基本
      ;(items[4].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss5Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 医療
      ;(items[5].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss6Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
      // 心理
      ;(items[6].historyRegisterCompleted as Mo01337OnewayType).value =
        resData.data.multipleList[0].dmySelAss7Flg ===
        Or59423Const.DEFAULT.API_RESULT_HISTORY_INFO_HAVE
          ? Or59423Const.DEFAULT.TAB_MARK_DATA_EXISTS
          : Or59423Const.DEFAULT.NULL_DATA_DISPLAY
    }

    // 編集アイコンを非表示にする
    const component = document.getElementById('or59423Component')
    if (component) {
      // 対象アイコン
      const iconTextList = ['edit_square']
      // 対象ラベル
      const labelTextList = [t('label.assessment-home-6-1-servey-ledger-import')]

      const iElements = component.querySelectorAll('i')
      iElements.forEach((el) => {
        if (el.textContent) {
          if (iconTextList.includes(el.textContent?.trim())) {
            el.style.display = 'none'
          }
        }
      })

      const serveyLedgerImportLabelElement = component.querySelectorAll('label')
      serveyLedgerImportLabelElement.forEach((el) => {
        if (el.textContent) {
          if (labelTextList.includes(el.textContent?.trim())) {
            el.style.display = 'none'
          }
        }
      })
    }
  }
}

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // コントロール初期化
  initControl()

  if (_systemCommonStore.getUserSelectSelfId(orX0077.value.uniqueCpId)) {
    await getInitDataInfo()
    userId.value = _systemCommonStore.getUserSelectSelfId(orX0077.value.uniqueCpId) ?? ''
  }
})

/**************************************************
 * watch関数
 **************************************************/

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => OrX0077Logic.state.get(orX0077.value.uniqueCpId)?.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue) {
      close()
    }
  }
)
/**
 * 計画期間IDの変更を監視する
 */
watch(
  () => local.mo01334_1.value,
  async (newValue, oldValue) => {
    if (!newValue) return
    if (oldValue === '') return
    if (isLoading.value) return
    await planPeriodIdChange(newValue)
  }
)

/**
 * 歴史変更関数
 *
 * @description
 * 歴史変更時に情報再取得
 */
watch(
  () => local.mo01334_2.value,
  async (newValue, oldValue) => {
    if (!newValue) return
    if (oldValue === '') return
    if (isLoading.value) return
    await historyChange(newValue)
  }
)
</script>

<template>
  <g-custom-orX0077
    v-bind="orX0077"
    :oneway-model-value="localOneway.orx0077Oneway"
    @on-change-user-select="getUserId"
  >
    <template #filter>
      <c-v-row no-gutters>
        <c-v-col cols="auto table-header">
          <base-mo01334
            v-if="
              props.onewayModelValue.planPeriodFlg === TeX0008Const.DEFAULT.PLANNING_PERIOD_MANAGE
            "
            v-model="local.mo01334_1"
            class="list-wrapper"
            :oneway-model-value="localOneway.mo01334_1Oneway"
            hide-default-footer
          >
            <template #[`item.numberOfWithinThePeriodHistory`]="{ item }">
              <div class="d-flex justify-end">
                {{ item.numberOfWithinThePeriodHistory }}
              </div>
            </template>
          </base-mo01334>
        </c-v-col>
        <c-v-col cols="4 pl-4 table-header">
          <base-mo01334
            v-model="local.mo01334_2"
            class="list-wrapper"
            :oneway-model-value="localOneway.mo01334_2Oneway"
            hide-default-footer
          />
        </c-v-col>
      </c-v-row>
    </template>
    <template #copyMain>
      <div
        class="w-100 mt-2"
        style="height: 1px; background-color: #cccccc"
      ></div>
      <base-mo00043
        v-model="local.mo00043"
        :oneway-model-value="localOneway.mo00043OnewayType"
      />
      <c-v-window
        v-if="localOneway.mo01334_2Oneway.items.length > 0"
        v-model="local.mo00043.id"
      >
        <c-v-window-item value="1">
          <div
            class="d-flex flex-column ma-0"
            style="min-height: 532px; max-height: 532px"
          >
            <div
              id="or59423Component"
              class="flex-1-1 overflow-y-auto"
              style="min-height: 0"
            >
              <div
                id="tabContent"
                class="position-relative"
              >
                <!-- 編集できないようにマスクを付ける -->
                <div
                  id="mask"
                  class="position-absolute"
                  style="width: 100%; height: 100%; z-index: 999"
                ></div>
                <!-- 食事画面 -->
                <g-custom-or03243
                  v-if="props.onewayModelValue.tabId === Or03243Const.DEFAULT.TAB_ID"
                  v-bind="or03243"
                  :oneway-model-value="localOneway.or03243Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                />
                <!-- 排泄画面 -->
                <g-custom-or03250
                  v-if="props.onewayModelValue.tabId === Or03250Const.DEFAULT.TAB_ID"
                  v-bind="or03250"
                  :oneway-model-value="localOneway.or03250Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                />
                <!-- 入浴画面 -->
                <g-custom-or03249
                  v-if="props.onewayModelValue.tabId === Or03249Const.DEFAULT.TAB_ID"
                  v-bind="or03249"
                  :oneway-model-value="localOneway.or03249Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                />
                <!-- 洗面画面 -->
                <g-custom-or03245
                  v-if="props.onewayModelValue.tabId === Or03245Const.DEFAULT.TAB_ID"
                  v-bind="or03245"
                  :oneway-model-value="localOneway.or03245Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                />
                <!-- 基本画面 -->
                <g-custom-or03240
                  v-if="props.onewayModelValue.tabId === Or03240Const.DEFAULT.TAB_ID"
                  v-bind="or03240"
                  :oneway-model-value="localOneway.or03240Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                />
                <!-- 医療画面 -->
                <g-custom-or03237
                  v-if="props.onewayModelValue.tabId === Or03237Const.DEFAULT.TAB_ID"
                  v-bind="or03237"
                  :oneway-model-value="localOneway.or03237Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                />
                <!-- 心理画面 -->
                <g-custom-or03244
                  v-if="props.onewayModelValue.tabId === Or03244Const.DEFAULT.TAB_ID"
                  v-bind="or03244"
                  :oneway-model-value="localOneway.or03244Oneway"
                  :parent-unique-cp-id="props.uniqueCpId"
                />
              </div>
            </div>
          </div>
        </c-v-window-item>
        <c-v-window-item value="2">
          <c-v-row nogutters>
            <c-v-col cols="auto">
              <base-mo-01354
                v-model="local.mo01354"
                :oneway-model-value="localOneway.mo01354Oneway"
                class="list-wrapper"
              >
                <template #headers>
                  <tr>
                    <th>
                      <c-v-col
                        class="d-flex align-center pa-0"
                        cols="auto"
                      >
                        <base-at-checkbox
                          v-model="headerCheckBoxValue"
                          :indeterminate="setIndeterminate"
                          checkbox-label=""
                          @change="headerCheckBoxChange"
                        ></base-at-checkbox>
                      </c-v-col>
                    </th>
                    <th>
                      {{ localOneway.mo01354Oneway.headers[0].title }}
                    </th>
                    <th>{{ localOneway.mo01354Oneway.headers[1].title }}</th>
                  </tr>
                </template>
                <template #item="{ item }">
                  <tr
                    :class="[
                      {
                        'disabled-row':
                          item.historyRegisterCompleted.value ===
                          Or59423Const.DEFAULT.NULL_DATA_DISPLAY,
                      },
                    ]"
                  >
                    <td>
                      <c-v-col
                        class="d-flex align-center pa-0 pl-4 pr-4"
                        cols="auto"
                      >
                        <base-at-checkbox
                          v-model="item.selectable"
                          checkbox-label=""
                          :disabled="
                            item.historyRegisterCompleted.value ===
                            Or59423Const.DEFAULT.NULL_DATA_DISPLAY
                          "
                        ></base-at-checkbox>
                      </c-v-col>
                    </td>
                    <td>
                      <c-v-col cols="auto pa-0 pl-4 pr-4">
                        <base-mo01337 :oneway-model-value="item.section" />
                      </c-v-col>
                    </td>
                    <td>
                      <c-v-col cols="auto pa-0 pl-4 pr-4">
                        <base-mo01337 :oneway-model-value="item.historyRegisterCompleted" />
                      </c-v-col>
                    </td>
                  </tr>
                </template>
                <!-- ページングを非表示 -->
                <template #bottom />
              </base-mo-01354>
            </c-v-col>
          </c-v-row>
          <c-v-row nogutters>
            <c-v-col>
              <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
            </c-v-col>
          </c-v-row>
        </c-v-window-item>
      </c-v-window>
    </template>
    <template #footer>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="t('tooltip.screen-close')"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609AddBtnOneway"
          @click="confirmClick"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="t('tooltip.confirm')"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </g-custom-orX0077>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showInfoDialog"
    v-bind="or21814_1"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showConfirmDialog"
    v-bind="or21814_2"
  />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or21815
    v-if="showWarningDialog"
    v-bind="or21815"
  />
  <!-- ロードイング -->
  <v-overlay
    :model-value="isLoading"
    :persistent="true"
    class="align-center justify-center"
    style="z-index: 9999"
  >
    <v-progress-circular
      indeterminate
      color="primary"
    >
    </v-progress-circular
  ></v-overlay>
</template>

<style lang="scss" scoped>
@use '/styles/cmn/mo-data-table-list.scss';

.disabled-row {
  background-color: rgb(var(--v-theme-black-200));
  // イベント禁止
  pointer-events: none;
}

.select-checkbox-col {
  :deep(.v-selection-control) {
    justify-content: center;
  }
}

.red-description {
  color: rgb(var(--v-theme-red-700));
  font-size: 18px;
}
</style>
