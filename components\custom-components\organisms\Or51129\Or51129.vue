<script setup lang="ts">
/**
 * Or51129:有効期間登録
 * GUI01167_地域密着型介護サービス有効期間登録
 *
 * <AUTHOR> 畢文傑
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51129Const } from './Or51129.constants'
import type { Mi<PERSON>haku<PERSON>ist, Or51129StateType, Or51129TwoWayData } from './Or51129.type'
import { useScreenStore } from '~/stores/session/screen'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import type { Or51129OnewayType } from '~/types/cmn/business/components/Or51129Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo01415OnewayType } from '~/types/business/components/Mo01415Type'
import type {
  AreaCloseContactTypeNursingCareServiceValidPeriodInfoSelectEntity,
  AreaCloseContactTypeNursingCareServiceValidPeriodInfoSelectOutEntity,
} from '~/repositories/cmn/entities/AreaCloseContactTypeNursingCareServiceValidPeriodInfoSelectEntity'

import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import { CustomClass } from '~/types/CustomClassType'
import type { AreaCloseContactTypeNursingCareServiceValidPeriodInfoSaveUpdatetEntity } from '~/repositories/cmn/entities/AreaCloseContactTypeNursingCareServiceValidPeriodInfoSaveUpdatetEntity'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or51129OnewayType
  onewayModelValue: Or51129OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props: Props = defineProps<Props>()

const or21814 = ref({ uniqueCpId: '' })
// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: '' })
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)
// 権限フラグ
const isPermissionViewAuth = ref(false)
// 列幅
const columnMinWidth = ref<number[]>([80, 184, 184, 78])

const localOneway = reactive({
  mo00611AddBtnOneway: {
    tooltipText: t('tooltip.care-plan2-new-btn'),
    btnLabel: t('label.add-row'),
    width: '90px',
  } as Mo00611OnewayType,
  mo00611DeleteBtnOneway: {
    tooltipText: t('tooltip.care-plan2-deleteline-btn'),
    btnLabel: t('label.delete-row'),
    width: '90px',
    color: 'red',
    labelColor: 'red',
  } as Mo01265OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.save'),
    tooltipText: t('tooltip.confirm-btn'),
    // disabled: !isPermissionViewAuth.value,
  } as Mo00609OnewayType,
  //ダイアログ
  mo00024Oneway: {
    width: 'auto',
    maxWidth: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Gui01167',
      toolbarTitle: t('label.community-friendly-nursing-service-valid-period-registration'),
      toolbarName: 'Gui01167ToolBar',
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  mo01338Oneway1: {
    value: t('label.office-title'),
  } as Mo01338OnewayType,
  mo01338Oneway2: {
    value: t('label.insurer'),
  } as Mo01338OnewayType,
  // 有効開始月
  mo01415Oneway: {
    readonly: false,
  } as Mo01415OnewayType,
  // 提示文字2
  mo01338TextOneway2: {
    value: t('label.lifetime-registration-text2'),
  } as Mo01338OnewayType,
  // 選択チェックボックス
  mo00018CheckOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: '',
    disabled: true,
  } as Mo00018OnewayType,
  mo01415OnewayDate: {
    readonly: true,
    customClass: new CustomClass({ outerClass: 'bg-transparent' }),
  },
})

//一覧テーブルヘッダ
const headersWeekPlan = [
  {
    title: t('label.id'),
    width: '40px',
    align: 'left',
    sortable: false,
    key: 'mTermid',
  },
  {
    title: t('label.effective-start-month'),
    width: '120px',
    align: 'left',
    sortable: false,
    key: 'startdateYm',
    required: true,
  },
  {
    title: t('label.effective-end-month'),
    width: '120px',
    align: 'left',
    sortable: false,
    key: 'enddateYm',
  },
  {
    title: t('label.unit-number'),
    width: '50px',
    align: 'left',
    sortable: false,
    key: 'tankaFlg',
  },
]
const required: string = Or51129Const.DEFAULT.REQUIRED
const local = reactive({
  jigyoKnj: { value: '' }, //事業所名称
  kHokenKnj: { value: '' }, //保険者名称
  micchakuList: [] as MicchakuList[],
})
/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or51129Const.DEFAULT.IS_OPEN,
})
const isEdit = computed(() => {
  // console.error(props.uniqueCpId)
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or51129TwoWayData>({
  cpId: Or51129Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
const { setState } = useScreenOneWayBind<Or51129StateType>({
  cpId: Or51129Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or51129Const.DEFAULT.IS_OPEN
    },
  },
})
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  isPermissionViewAuth.value = await hasRegistAuth()
  // 初期情報取得
  void getData()
})

/** 初期情報取得 */
async function getData() {
  const inputData: AreaCloseContactTypeNursingCareServiceValidPeriodInfoSelectEntity = {
    // 単価有効期間ID
    tankaTermid: props.onewayModelValue.tankaTermid,
    // 有効期間ID
    termid: props.onewayModelValue.termid,
    // 事業所ID
    svJigyoId: props.onewayModelValue.svJigyoId,
    // 保険者ID
    kHokenCd: props.onewayModelValue.kHokenCd,
  }

  // バックエンドAPIから初期情報取得
  const res: AreaCloseContactTypeNursingCareServiceValidPeriodInfoSelectOutEntity =
    await ScreenRepository.select(
      'areaCloseContactTypeNursingCareServiceValidPeriodInfoSelect',
      inputData
    )
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: false,
    },
  })
  local.micchakuList = []
  if (res?.data !== null && res.data.micchakuList?.length > 0) {
    let count = 0
    // 明細リスト
    for (const micchaku of res.data.micchakuList) {
      local.micchakuList.push({
        index: count,
        ...micchaku,
        tankaFlgModelValue: {
          modelValue: micchaku.tankaFlg === Or51129Const.DEFAULT.ONE ? true : false,
        },
        startdateYmObj: { value: micchaku.startdateYm ?? '' },
        enddateYmObj: { value: micchaku.enddateYm ?? '' },
        updateKbn: 'U',
        startdateYmdB: micchaku.startdateYmd,
      })
      count++
    }
    // 該当期間内の行を選択する。
    const index = findIndexByDesignationDate(
      local.micchakuList,
      props.onewayModelValue.designationDate
    )
    if (index !== -1) {
      selectedItemIndex.value = index
    } else {
      selectedItemIndex.value = 0
    }
  }
  local.jigyoKnj.value =
    res.data.jigyoKnj !== ''
      ? (res.data.jigyoKnj ?? props.onewayModelValue.svJigyoId)
      : props.onewayModelValue.svJigyoId
  local.kHokenKnj.value =
    res.data.kHokenKnj !== ''
      ? (res.data.kHokenKnj ?? props.onewayModelValue.kHokenCd)
      : props.onewayModelValue.kHokenCd

  refValue.value = { micchakuList: local.micchakuList }
  useScreenStore().setCpTwoWay({
    cpId: Or51129Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

// 該当期間内の行を選択する。処理
function findIndexByDesignationDate(micchakuList: MicchakuList[], designationDate: string) {
  const targetDate = new Date(designationDate)

  return micchakuList.findIndex((item) => {
    const startDate = new Date(item.startdateYmd!)
    const endDate = new Date(item.enddateYmd!)

    // 目標日が［startDate，endDate］の範囲内であるかどうかを判断する（等しいを含む）
    return targetDate >= startDate && targetDate <= endDate
  })
}
/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  if (isEdit.value) {
    // 画面入力変更がある場合
    // 確認ダイアログ表示
    const result = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10155'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
    if (result === Or51129Const.DIALOG_RESULT_YES) {
      if (await saveFun()) {
        setState({ isOpen: false })
      }
    } else if (result === Or51129Const.DIALOG_RESULT_NO) {
      setState({ isOpen: false })
    }
  } else {
    setState({ isOpen: false })
  }
}

/**
 * 「確定」ボタン押下
 */
async function saveFun() {
  // return
  // 画面入力データ変更があるかどうかを判定する
  if (!isEdit.value) {
    // メッセージ内容：
    //「変更されている項目がないため、保存を行うことは出来ません。
    // 項目を入力変更してから、再度保存を行ってください。」
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-21800'),
    })
    return
  }
  // 有効終了日だけが入っていない行が２件以上の場合
  if (!checkEnddateYm(refValue.value!.micchakuList)) {
    await showOr21813Msg(t('message.e-cmn-41751'))
    return
  }
  // 有効期間の重複している行がある場合
  if (!validateDate(refValue.value!.micchakuList)) {
    await showOr21813Msg(t('message.e-cmn-41752'))
    return
  }
  // 有効終了日が有効開始日よりも前になっている行がある場合
  if (!validateDateRanges(refValue.value!.micchakuList)) {
    await showOr21813Msg(t('message.e-cmn-41753'))
    return
  }

  const param = {
    micchakuSaveList: refValue.value!.micchakuList,
  } as AreaCloseContactTypeNursingCareServiceValidPeriodInfoSaveUpdatetEntity

  const inputMockDataUpdate: AreaCloseContactTypeNursingCareServiceValidPeriodInfoSaveUpdatetEntity =
    param
  await ScreenRepository.update(
    'areaCloseContactTypeNursingCareServiceValidPeriodInfoUpdate',
    inputMockDataUpdate
  )

  await getData()

  return true
}

// 有効終了日だけが入っていない行が２件以上の場合
function checkEnddateYm(micchakuList: MicchakuList[]): boolean {
  const filteredList = micchakuList.filter((item: MicchakuList) => item.updateKbn !== 'D')
  let emptyCount = 0

  for (const item of filteredList) {
    if (!item.enddateYm) {
      emptyCount++
      if (emptyCount >= 2) {
        return false
      }
    }
  }

  return emptyCount < 2
}

// 有効期間の重複している行がある場合
function validateDate(micchakuList: MicchakuList[]) {
  const filteredList = micchakuList.filter((item: MicchakuList) => item.updateKbn !== 'D')
  // 条件1:2つのオブジェクトの期間が重複しているかどうかをチェックする
  const hasOverlap = filteredList.some((itemA, indexA) => {
    return filteredList.some((itemB, indexB) => {
      if (indexA === indexB) return false // 不和自己比

      const aStart = new Date(itemA.startdateYmd ?? '')
      const aEnd = new Date(itemA.enddateYmd ?? '')
      const bStart = new Date(itemB.startdateYmd ?? '')
      const bEnd = new Date(itemB.enddateYmd ?? '')

      // オーバーラップしているかどうかを判断する：Aの開始<=Bの終了かつAの終了>=Bの開始
      return aStart <= bEnd && aEnd >= bStart
    })
  })

  if (hasOverlap) {
    return false
  }

  // 条件2：mTermid>0のオブジェクトのみをフィルタし、startdateYmとenddateYmがすべて空であるかどうかを判断する
  const hasEmptyYm = filteredList.some((item) => {
    return (
      Number(item.mTermid) > 0 && item.startdateYm?.trim() === '' && item.enddateYm?.trim() === ''
    )
  })

  if (hasEmptyYm) {
    return false
  }

  // すべての条件が満たされていない→trueに戻る
  return true
}

// 有効終了日が有効開始日よりも前になっている行がある場合
function validateDateRanges(micchakuList: MicchakuList[]) {
  const filteredList = micchakuList.filter((item: MicchakuList) => item.updateKbn !== 'D')
  for (const item of filteredList) {
    const startDate = new Date(item.startdateYmd ?? '')
    const endDate = new Date(item.enddateYmd ?? '')
    if (startDate > endDate) {
      return false
    }
  }

  return true
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(mo00024, async () => {
  // 組織dialog自動クローズを手動判定に変更
  if (mo00024.value.emitType === 'closeBtnClick') {
    await close()
    mo00024.value.emitType = 'blank'
  }
})

/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 確認ダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise<string>((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = Or51129Const.DIALOG_RESULT_CANCEL
        if (event?.firstBtnClickFlg) {
          result = Or51129Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or51129Const.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * error開閉
 *
 * @param uniqueCpId
 *
 * @param errormsg - Message
 */

const showOr21813Msg = (errormsg: string) => {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 一覧の実際表示行数
 */
const showTableData = computed(() => {
  if (refValue.value!.micchakuList === undefined || refValue.value!.micchakuList.length === 0) {
    return { trueLastIndex: -1, showLastIndex: -1 }
  } else {
    const showList = refValue.value!.micchakuList.filter((item) => item.updateKbn !== 'D')

    if (showList === undefined || showList.length === 0) {
      return {
        trueLastIndex: refValue.value!.micchakuList.length - 1,
        showLastIndex: -1,
      }
    } else {
      return {
        trueLastIndex: refValue.value!.micchakuList.length - 1,
        showLastIndex: showList[showList.length - 1].index,
      }
    }
  }
})

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}
/**
 * 行追加
 */
function onAddItem() {
  const data = { ...Or51129Const.DEFAULT.ADD_LIST }

  data.svJigyoId = props.onewayModelValue.svJigyoId

  data.tankaTermid = props.onewayModelValue.tankaTermid
  data.termid = props.onewayModelValue.termid
  data.kHokenCd = props.onewayModelValue.kHokenCd
  data.shokuId = props.onewayModelValue.shokuId
  data.index = refValue.value!.micchakuList.length
  refValue.value!.micchakuList.push(data)

  selectRow(refValue.value!.micchakuList.length - 1)
}
/**
 * 行削除
 */
const onDelete = async () => {
  if (selectedItemIndex.value !== -1) {
    const item = refValue.value!.micchakuList[selectedItemIndex.value]
    if (item.tankaFlg === Or51129Const.DEFAULT.ONE) {
      const result = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10156'),
        firstBtnType: 'destroy1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'normal3',
        secondBtnLabel: t('btn.no'),
      })
      if (result === Or51129Const.DIALOG_RESULT_YES) {
        if (selectedItemIndex.value !== null) {
          if (item.updateKbn === 'C') {
            refValue.value!.micchakuList.splice(selectedItemIndex.value, 1)
          } else {
            item.updateKbn = 'D'
          }
        }
      } else if (result === Or51129Const.DIALOG_RESULT_CANCEL) {
        return
      }
    } else {
      if (selectedItemIndex.value !== null) {
        if (item.updateKbn === 'C') {
          refValue.value!.micchakuList.splice(selectedItemIndex.value, 1)
        } else {
          item.updateKbn = 'D'
        }
      }
    }

    if (selectedItemIndex.value <= showTableData.value.showLastIndex) {
      for (; selectedItemIndex.value <= showTableData.value.showLastIndex; ) {
        if (refValue.value!.micchakuList[selectedItemIndex.value].updateKbn !== 'D') {
          break
        } else {
          selectedItemIndex.value = selectedItemIndex.value + 1
        }
      }
    } else {
      selectedItemIndex.value = showTableData.value.showLastIndex
    }
  }
}

const onUpdateStartYm = (val: Mo00020Type, index: number) => {
  const nowYmd = val.value
  const termid = props.onewayModelValue.termid
  if (nowYmd) {
    const [year, month] = nowYmd.split('/')
    const yearMonth = `${year}/${month}`
    const yearMonthDay = `${year}/${month}/01`
    if (termid === '7') {
      if (new Date(yearMonthDay) >= new Date('2014/04/01')) {
        refValue.value!.micchakuList[index].startdateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].startdateYm = yearMonth
        refValue.value!.micchakuList[index].startdateYmd = yearMonthDay
      } else {
        refValue.value!.micchakuList[index].startdateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].startdateYm = yearMonth
        refValue.value!.micchakuList[index].startdateYmd = yearMonthDay
      }
    } else if (termid === '6') {
      if (new Date(yearMonthDay) >= new Date('2012/04/01')) {
        refValue.value!.micchakuList[index].startdateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].startdateYm = yearMonth
        refValue.value!.micchakuList[index].startdateYmd = yearMonthDay
      } else {
        refValue.value!.micchakuList[index].startdateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].startdateYm = yearMonth
        refValue.value!.micchakuList[index].startdateYmd = yearMonthDay
      }
    } else if (termid === '5') {
      if (new Date(yearMonthDay) >= new Date('2009/04/01')) {
        refValue.value!.micchakuList[index].startdateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].startdateYm = yearMonth
        refValue.value!.micchakuList[index].startdateYmd = yearMonthDay
      } else {
        refValue.value!.micchakuList[index].startdateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].startdateYm = yearMonth
        refValue.value!.micchakuList[index].startdateYmd = yearMonthDay
      }
    } else if (new Date(yearMonthDay) >= new Date('2007/04/01')) {
      refValue.value!.micchakuList[index].startdateYmObj!.value = yearMonth
      refValue.value!.micchakuList[index].startdateYm = yearMonth
      refValue.value!.micchakuList[index].startdateYmd = yearMonthDay
    } else {
      refValue.value!.micchakuList[index].startdateYmObj!.value = yearMonth
      refValue.value!.micchakuList[index].startdateYm = yearMonth
      refValue.value!.micchakuList[index].startdateYmd = yearMonthDay
    }
  }
}
const onUpdateEndYm = (val: Mo00020Type, index: number) => {
  const nowYmd = val.value
  const termid = props.onewayModelValue.termid
  if (nowYmd) {
    const [year, month] = nowYmd.split('/')
    const yearMonth = `${year}/${month}`
    const yearDay = parseInt(year, 10)
    const monthDay = parseInt(month, 10)
    const dayNum = new Date(yearDay, monthDay, 0).getDate()
    const yearMonthDay = `${year}/${month}/${dayNum}`
    if (termid === '7') {
      if (new Date(yearMonthDay) >= new Date('2014/04/01')) {
        refValue.value!.micchakuList[index].enddateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].enddateYm = yearMonth
        refValue.value!.micchakuList[index].enddateYmd = yearMonthDay
      } else {
        refValue.value!.micchakuList[index].enddateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].enddateYm = yearMonth
        refValue.value!.micchakuList[index].enddateYmd = yearMonthDay
      }
    } else if (termid === '6') {
      if (new Date(yearMonthDay) >= new Date('2012/04/01')) {
        refValue.value!.micchakuList[index].enddateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].enddateYm = yearMonth
        refValue.value!.micchakuList[index].enddateYmd = yearMonthDay
      } else {
        refValue.value!.micchakuList[index].enddateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].enddateYm = yearMonth
        refValue.value!.micchakuList[index].enddateYmd = yearMonthDay
      }
    } else if (termid === '5') {
      if (new Date(yearMonthDay) >= new Date('2009/04/01')) {
        refValue.value!.micchakuList[index].enddateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].enddateYm = yearMonth
        refValue.value!.micchakuList[index].enddateYmd = yearMonthDay
      } else {
        refValue.value!.micchakuList[index].enddateYmObj!.value = yearMonth
        refValue.value!.micchakuList[index].enddateYm = yearMonth
        refValue.value!.micchakuList[index].enddateYmd = yearMonthDay
      }
    } else if (new Date(yearMonthDay) >= new Date('2007/04/01')) {
      refValue.value!.micchakuList[index].enddateYmObj!.value = yearMonth
      refValue.value!.micchakuList[index].enddateYm = yearMonth
      refValue.value!.micchakuList[index].enddateYmd = yearMonthDay
    } else {
      refValue.value!.micchakuList[index].enddateYmObj!.value = yearMonth
      refValue.value!.micchakuList[index].enddateYm = yearMonth
      refValue.value!.micchakuList[index].enddateYmd = yearMonthDay
    }
  }
}
const onUpdatetankaFlg = (val: Mo00018Type) => {
  if (val.modelValue) {
    refValue.value!.micchakuList[selectedItemIndex.value].tankaFlg = '1'
  } else {
    refValue.value!.micchakuList[selectedItemIndex.value].tankaFlg = '0'
  }
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutters
        class="header-row"
        style="border-bottom: solid thin rgb(var(--v-theme-light))"
      >
        <c-v-col cols="auto">
          <base-mo01338
            class="label1"
            :oneway-model-value="localOneway.mo01338Oneway1"
          />
        </c-v-col>
        <c-v-col cols="auto">
          <!-- 事業名 -->
          <base-mo01338 :oneway-model-value="local.jigyoKnj" />
        </c-v-col>
        <c-v-col cols="auto">
          <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway2" />
        </c-v-col>
        <c-v-col cols="auto">
          <!-- 保険者名称 -->
          <base-mo01338 :oneway-model-value="local.kHokenKnj" />
        </c-v-col>
      </c-v-row>
      <div class="content-box">
        <c-v-row
          no-gutters
          style="padding: 16px 8px 8px 4px"
        >
          <base-mo00611
            :oneway-model-value="localOneway.mo00611AddBtnOneway"
            class="mx-1"
            prepend-icon="add"
            @click="onAddItem"
          />
          <base-mo01265
            :oneway-model-value="{
              ...localOneway.mo00611DeleteBtnOneway,
            }"
            class="mx-1"
            prepend-icon="delete"
            :disabled="selectedItemIndex == -1"
            @click="onDelete"
          />
        </c-v-row>

        <c-v-row style="margin: 8px 0px">
          <c-v-data-table
            v-resizable-grid="{ columnWidths: columnMinWidth }"
            :headers="headersWeekPlan"
            class="table-wrapper"
            hide-default-footer
            :items="refValue!.micchakuList"
            fixed-header
            hover
            height="334px"
            :items-per-page="-1"
          >
            <!-- ヘッダー：課題 -->
            <template #[`header.startdateYm`]>
              <div>
                <span style="color: red">{{ required }}</span>
                {{ t('label.effective-start-month') }}
              </div>
            </template>
            <template #item="{ item, index }">
              <tr
                v-if="item.updateKbn !== 'D'"
                :class="{ 'select-row': selectedItemIndex === item.index }"
                @click="selectRow(index)"
              >
                <!-- ID -->
                <td>
                  <base-mo01336
                    :oneway-model-value="{ value: item.mTermid }"
                    style="width: 100%"
                  >
                  </base-mo01336>
                </td>
                <!-- 開始年月 -->
                <td style="padding: 1px">
                  <base-mo01415
                    v-model="item.startdateYmObj"
                    class="start-date"
                    :oneway-model-value="localOneway.mo01415OnewayDate"
                    @update:model-value="onUpdateStartYm($event, index)"
                  />
                </td>
                <!-- 終了年月 -->
                <td style="padding: 1px">
                  <base-mo01415
                    v-model="item.enddateYmObj"
                    class="end-date"
                    :oneway-model-value="localOneway.mo01415OnewayDate"
                    @update:model-value="onUpdateEndYm($event, index)"
                  />
                </td>
                <!-- 単位数 -->
                <td style="align-items: center">
                  <base-mo00018
                    v-model="item.tankaFlgModelValue"
                    :oneway-model-value="localOneway.mo00018CheckOneway"
                    @update:model-value="onUpdatetankaFlg"
                  />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-row>
      </div>
      <!-- 提示文字1ラベル -->
      <c-v-row
        no-gutters
        style="display: flex; justify-content: center"
      >
        <c-v-col
          cols="auto"
          class="text-clo"
        >
          <base-mo01338
            v-if="Number(props.onewayModelValue.termid) <= Or51129Const.DEFAULT.NUMBER_FOUR"
            :oneway-model-value="{
              value: t('label.lifetime-registration-text1-1'),
              valueFontWeight: 'bold',
            }"
          />
          <base-mo01338
            v-else-if="Number(props.onewayModelValue.termid) === Or51129Const.DEFAULT.NUMBER_FIVE"
            :oneway-model-value="{
              value: t('label.lifetime-registration-text1-2'),
              valueFontWeight: 'bold',
            }"
          />
          <base-mo01338
            v-else-if="Number(props.onewayModelValue.termid) === Or51129Const.DEFAULT.NUMBER_SIX"
            :oneway-model-value="{
              value: t('label.lifetime-registration-text1-3'),
              valueFontWeight: 'bold',
            }"
          />
          <base-mo01338
            v-else
            :oneway-model-value="{
              value: t('label.lifetime-registration-text1-4'),
              valueFontWeight: 'bold',
            }"
          />
        </c-v-col>
      </c-v-row>
      <!-- 提示文字2ラベル -->
      <c-v-row
        no-gutters
        style="display: flex; justify-content: center"
      >
        <c-v-col
          cols="auto"
          class="text-clo"
        >
          <base-mo01338 :oneway-model-value="localOneway.mo01338TextOneway2" />
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン mo00609 -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="saveFun"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:情報ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';

.text-clo {
  padding: 0 !important;
}
.disp_style {
  background: none !important;
}

.header-row .v-col {
  padding: 0 !important;
}
:deep(.ma-1) {
  margin: 0px !important;
}
:deep(.label1 .ml-4) {
  margin-left: 0px !important;
}
:deep(.datecls .v-col-auto) {
  width: 100% !important;
}
:deep(.start-date) {
  padding: 0 13px !important;
}
:deep(.end-date) {
  padding: 0 13px !important;
}
</style>
