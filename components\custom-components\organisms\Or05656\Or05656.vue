<script setup lang="ts">
/**
 * Or05656:(状況の事実)状況の事実リスト出力
 * GUI00915_状況の事実
 *
 * @description
 * (状況の事実)状況の事実リスト出力
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { useI18n } from 'vue-i18n'
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { Or28412Logic } from '../Or28412/Or28412.logic'
import type { Or28412Type } from '../Or28412/Or28412.type'
import { Or28412Const } from '../Or28412/Or28412.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or05656Const } from './Or05656.constants'
import type { DataTableType, TooltipMap } from './Or05656.type'
import { useScreenTwoWayBind, useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { Or05656OnewayType } from '~/types/cmn/business/components/Or05656Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01360OnewayType } from '~/types/business/components/Mo01360Type'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
import type { Mo01332OnewayType } from '~/types/business/components/Mo01332Type'
import type { Mo01280OnewayType } from '~/types/business/components/Mo01280Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import { CustomClass } from '~/types/CustomClassType'
/**
 * useI18n
 */
const { t } = useI18n()
/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or05656OnewayType[]
  modelValue: Or05656OnewayType[]
  uniqueCpId: string
  parentUniqueCpId: string
}

/**
 * Propsの定義
 */
const props = defineProps<Props>()

/** Or28412子コンポーネントの状態 */
const or28412 = ref({ uniqueCpId: '' })
/**
 * Or21814用ref
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * Or21815用ref
 */
const or21815 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネントのプロパティを保持する変数
 * - uniqueCpId: 子コンポーネントの一意のID
 */
const or51775 = ref({ uniqueCpId: '' })
/**
 * 文章内容
 */
const modelValueOr51775 = ref<Or51775Type>({ modelValue: '' } as Or51775Type)
/** テーブルの参照 */
const dataTableRef = ref()
/**************************************************
 * Pinia
 **************************************************/
/**
 * useScreenTwoWayBind
 */
const { refValue } = useScreenTwoWayBind<Or05656OnewayType[][]>({
  cpId: Or05656Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or28412Const.CP_ID(0)]: or28412.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
})
/**
 * 選択行のID (groupIndex-itemIndex の形式)
 */
const selectedRowId = ref<string>('')
/**
 * 選択行のアイテム
 */
const selectedItem = ref<DataTableType | null>(null)
/**
 * 選択行のテーブル内インデックス
 */
const selectedRowIndex = ref<number>(-1)
/**
 * ローカルのOnewayオブジェクト
 */
const localOneway = reactive({
  mo01332OnewayType: {
    showItemLabel: false,
    items: [{ label: '', value: '1' }],
    customClass: new CustomClass({ outerClass: 'pt-2 pa-0' }),
  } as Mo01332OnewayType,
  // 選択肢詳細アイコン
  selectOptionsIcon: {
    btnIcon: 'info',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    minWidth: '24px',
    minHeight: '24px',
    height: '24px',
    width: '24px',
  } as Mo00009OnewayType,
  // カラム2
  mo01360TekiyouFlg1OneWay: {
    maxLength: 17,
    items: [],
  } as Mo01360OnewayType,
  mo01360TekiyouFlg2OneWay: {
    items: [],
  } as Mo01360OnewayType,
  mo01274KoumokuKnjOneWay: {
    maxLength: 28,
  } as Mo01274OnewayType,
  mo01274TekiyouFlg3OneWay: {
    maxLength: 40,
  } as Mo01274OnewayType,
  mo00009OneWay: {
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  // カラム3
  mo01332AtAnyTime: {
    items: Or05656Const.VARIABLE.TEKIYOU_FLG_LIST,
    showItemLabel: true,
  } as Mo01332OnewayType,
  // カラム4
  mo01360PotentialOneWay: {
    items: [],
  } as Mo01360OnewayType,
  //カラム5
  mo01280NoteOneWay: {
    maxLength: 4000,
  } as Mo01280OnewayType,
  /**
   * ローカル変数
   */
  or51775Oneway: {
    title: t('label.remarks'),
    screenId: '',
    bunruiId: '2',
    t1Cd: '750',
    t2Cd: '1',
    t3Cd: '0',
    tableName: Or05656Const.VARIABLE.TABLE_NAME,
    columnName: Or05656Const.VARIABLE.COLUMN_NAME,
    assessmentMethod: systemCommonsStore.getAuthzOtherCaremanager?.assessmentMethod ?? '',
    inputContents: '',
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
})

/**
 * ヘッダーの設定
 */
const headers = [
  {
    title: t('label.situation_fact_note1'),
    key: 'koumoku1Id',
    width: '246px',
    sortable: false,
    colspan: 2,
    rowspan: 2,
  },
  {
    title: t('label.current_note2'),
    key: 'koumoku1Id2',
    width: '335px',
    sortable: false,
    colspan: 1,
    rowspan: 1,
  },
  {
    title: t('label.factor_note3'),
    key: 'koumoku2Id',
    width: '142px',
    sortable: false,
    colspan: 1,
    rowspan: 1,
  },
  {
    title: t('label.possibility_note4'),
    key: 'youshikiId2',
    width: '198px',
    sortable: false,
    colspan: 1,
    rowspan: 1,
  },
  {
    title: t('label.remarks_detail'),
    key: 'youshikiId',
    width: '270px',
    sortable: false,
    colspan: 1,
    rowspan: 1,
  },
]
const tooltip = ref<TooltipMap>({
  koumoku1Id: t('label.notes_*1'),
  koumoku1Id2: t('label.notes_*2'),
  koumoku2Id: t('label.notes_*3'),
  youshikiId: t('label.notes_*4'),
  youshikiId2: t('label.notes_*5'),
})
/**
 * 削除されていないアイテムのみを表示する
 *
 * @type {ComputedRef<DataTableType[][]>}
 *
 * @returns {DataTableType[][]} - 削除されていないアイテムのグループ配列
 */
const visibleGroupedItems = computed(() => {
  if (!refValue.value) return []
  return (
    refValue.value
      ?.map((group) => group.filter((item) => item.updateKbn !== UPDATE_KBN.DELETE))
      .filter((group) => group.length > 0) ?? []
  ) // 空のグループは除外
})

/**
 * テーブル内の総アイテム数
 *
 * @type {ComputedRef<number>}
 *
 * @returns {number} - 表示可能なアイテムの総数
 */
const totalItemCount = computed(() => {
  return visibleGroupedItems.value.reduce((total, group) => total + group.length, 0)
})

/**
 * 画面固有の状態（v-model用）
 *
 * @type {Ref<Or28412Type>}
 */
const modelValueOr28412 = ref<Or28412Type>({ value: '' })
/**
 * ダイアログ表示フラグ
 * Or28412 のダイアログ開閉状態を取得
 *
 * @type {ComputedRef<boolean>}
 *
 * @returns {boolean} - ダイアログ開閉状態
 */
const showDialogOr28412 = computed(() => {
  return Or28412Logic.state.get(or28412.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ダイアログ表示フラグ
 */
const showDialogOr21814 = computed(() => {
  const data = Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
  // Or21814のダイアログ開閉状態
  return data
})
/**
 * ダイアログ表示フラグ
 * - Or51775のダイアログ開閉状態を取得
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ダイアログ表示フラグ
 */
const showDialogOr21815 = computed(() => {
  const data = Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
  // Or21815のダイアログ開閉状態
  return data
})
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  await init()
})
/**
 * 初期化処理
 *
 * @function init
 *
 * @description コンポーネントの初期化を行い、最初のアイテムを選択する
 *
 * @returns 初期化完了後のPromise
 *
 * @async
 */
async function init() {
  await initCodeCommon()
  selectFirstItem()
}
/**
 * 共通コード初期化処理
 *
 * @function initCodeCommon
 *
 * @description システムコードを取得し、各コンボボックス用のアイテムを設定する
 *
 * @returns 初期化完了後のPromise
 *
 * @async
 */
async function initCodeCommon() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 278
    {
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BASIC_SURVEY_2_APPLICABLE_12,
      targetDate: systemCommonsStore.getSystemDate,
      addBlank: false,
    },
    // 499
    {
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_POSSIBILITY_OF_IMPROVEMENT_MAINTENANCE_4,
      targetDate: systemCommonsStore.getSystemDate,
      addBlank: false,
    },
    // 500
    {
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CURRENT_STATUS,
      targetDate: systemCommonsStore.getSystemDate,
      addBlank: false,
    },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({
    selectCodeKbnList,
  })
  // Filter
  // 278
  localOneway.mo01360TekiyouFlg1OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BASIC_SURVEY_2_APPLICABLE_12
  )
  // 499
  localOneway.mo01360PotentialOneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_POSSIBILITY_OF_IMPROVEMENT_MAINTENANCE_4
  )
  // 500
  localOneway.mo01360TekiyouFlg2OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CURRENT_STATUS
  )
}

/**
 * 行選択時の処理
 *
 * @param groupIndex - グループのインデックス
 *
 * @param itemIndex - グループ内のアイテムインデックス
 */
function onSelectRow(groupIndex: number, itemIndex: number) {
  selectVisibleItem(groupIndex, itemIndex)
}

/**
 * テーブルの最下部にスムーズスクロールする
 *
 * @function scrollToNewItem
 *
 * @description 新しいアイテムが追加された際にテーブルの最下部へスムーズにスクロールする
 *
 * @returns スクロール完了後のPromise
 *
 * @async
 */
async function scrollToNewItem() {
  await nextTick()

  const tableComponent = dataTableRef.value as { $el: HTMLElement }
  if (tableComponent?.$el) {
    const tableWrapper = tableComponent.$el.querySelector(
      Or05656Const.VARIABLE.TABLE_SCROLL_CONTAINER
    )
    if (tableWrapper) {
      // スムーズスクロールで最下部に移動
      tableWrapper.scrollTo({
        top: tableWrapper.scrollHeight,
        behavior: 'smooth',
      })
    }
  }
}

/**
 * visibleItemから対応するgroupedItemsのitemを見つける
 *
 * @function findRealItemFromVisible
 *
 * @description 表示用データから実際のデータアイテムを検索する
 *
 * @param visibleItem - 表示用アイテム
 *
 * @returns 対応する実際のアイテム、見つからない場合はnull
 */
function findRealItemFromVisible(visibleItem: DataTableType): DataTableType | null {
  if (!refValue.value) return null

  for (const [_gIdx, group] of refValue.value.entries()) {
    const realIndex = group.findIndex((item) => item === visibleItem)
    if (realIndex !== -1) {
      return group[realIndex]
    }
  }
  return null
}

/**
 * テーブル内の実際のインデックスを計算する
 *
 * @function calculateRowIndex
 *
 * @description groupIndexとitemIndexからテーブル内の実際のインデックスを計算する
 *
 * @param groupIndex - グループのインデックス
 *
 * @param itemIndex - アイテムのインデックス
 *
 * @returns テーブル内の実際のインデックス
 */
const calculateRowIndex = (groupIndex: number, itemIndex: number): number => {
  const visible = visibleGroupedItems.value
  let rowIndex = 0

  // 指定されたグループまでの行数を計算
  for (let i = 0; i < groupIndex; i++) {
    rowIndex += visible[i]?.length || 0
  }

  // 指定されたアイテムの位置を加算
  rowIndex += itemIndex

  return rowIndex
}

/**
 * 指定されたvisible位置にアイテムを選択する
 *
 * @function selectVisibleItem
 *
 * @description 表示位置を指定してアイテムを選択し、選択状態を更新する
 *
 * @param groupIndex - グループのインデックス
 *
 * @param itemIndex - アイテムのインデックス
 */
function selectVisibleItem(groupIndex: number, itemIndex: number) {
  selectedRowId.value = `${groupIndex}-${itemIndex}`
  const visibleItem = visibleGroupedItems.value[groupIndex]?.[itemIndex]
  selectedItem.value = visibleItem ? findRealItemFromVisible(visibleItem) : null

  // テーブル内の実際のインデックスを計算
  selectedRowIndex.value = calculateRowIndex(groupIndex, itemIndex)
}

/**
 * 最初のアイテムを選択する
 *
 * @function selectFirstItem
 *
 * @description 表示可能なアイテムの最初のアイテムを選択する
 */
function selectFirstItem() {
  const visible = visibleGroupedItems.value

  if (visible.length > 0 && visible[0].length > 0) {
    selectVisibleItem(0, 0)
  } else {
    // アイテムがない場合はリセット
    selectedRowId.value = ''
    selectedItem.value = null
    selectedRowIndex.value = -1
  }
}

/**
 * 新しいアイテムを選択する
 *
 * @function selectNewItem
 *
 * @description 新規作成されたアイテムを自動的に選択する
 */
function selectNewItem() {
  const visible = visibleGroupedItems.value

  if (visible.length === 0) {
    // アイテムがない場合はリセット
    selectedRowId.value = ''
    selectedItem.value = null
    selectedRowIndex.value = -1
    return
  }

  // 最後のグループの最後のアイテムを選択（新しく追加されたアイテム）
  const lastGroupIndex = visible.length - 1
  const lastGroup = visible[lastGroupIndex]

  if (lastGroup && lastGroup.length > 0) {
    const lastItemIndex = lastGroup.length - 1
    selectVisibleItem(lastGroupIndex, lastItemIndex)
  }
}

/**
 * アイテム作成処理
 *
 * @function createItem
 *
 * @description 新しいアイテムを作成するためのダイアログを開く
 */
function createItem() {
  Or28412Logic.state.set({
    uniqueCpId: or28412.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * テーブルのインデックスからrefValue内のデータを探してupdateKbnを更新する
 *
 * @param groupIndex - テーブルのグループインデックス
 *
 * @param itemIndex - グループ内のアイテムインデックス
 *
 * @param updateKbn - 設定したいupdateKbnの値
 */
function updateItemInRefValueByIndex(groupIndex: number, itemIndex: number, updateKbn: string) {
  // この位置に表示されているアイテムを取得
  const visibleItem = visibleGroupedItems.value[groupIndex]?.[itemIndex]
  if (!visibleItem) return

  // refValue内の実データを探す
  const realItem = findRealItemFromVisible(visibleItem)
  if (realItem) {
    realItem.updateKbn = updateKbn
  }
}

/**
 * 選択されたアイテムを削除する（ソフト削除）
 *
 * @function executeDeleteItem
 *
 * @description 選択されたアイテムをソフト削除し、次の選択行を決定する
 */
function executeDeleteItem() {
  if (!selectedRowId.value) return
  const [groupIndex, itemIndex] = selectedRowId.value.split('-').map(Number)
  updateItemInRefValueByIndex(groupIndex, itemIndex, UPDATE_KBN.DELETE)
  moveSelectionAfterDelete()
}

/**
 * 削除後の選択行を移動する
 *
 * @function moveSelectionAfterDelete
 *
 * @description アイテム削除後に適切な次の選択行を決定し、選択状態を更新する
 */
function moveSelectionAfterDelete() {
  const visible = visibleGroupedItems.value
  if (visible.length === 0) {
    selectedRowId.value = ''
    selectedItem.value = null
    return
  }

  const [currentGroupIndex, currentItemIndex] = selectedRowId.value.split('-').map(Number)

  // 削除後、現在のpositionに別のアイテムが来ているかチェック
  if (visible[currentGroupIndex]?.[currentItemIndex]) {
    selectVisibleItem(currentGroupIndex, currentItemIndex)
    return
  }

  // 次のアイテムを探す（同じグループまたは次のグループ）
  for (let gIdx = currentGroupIndex; gIdx < visible.length; gIdx++) {
    const group = visible[gIdx]
    const startIdx = gIdx === currentGroupIndex ? currentItemIndex + 1 : 0

    if (group.length > startIdx) {
      selectVisibleItem(gIdx, startIdx)
      return
    }
  }

  // 前のアイテムを探す（最後のアイテム = 新しい最後）
  for (let gIdx = visible.length - 1; gIdx >= 0; gIdx--) {
    const group = visible[gIdx]
    if (group.length > 0) {
      selectVisibleItem(gIdx, group.length - 1)
      return
    }
  }
}
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.secondBtnClickFlg) {
      // 削除実行
      executeDeleteItem()
    } else {
      return
    }
    Or21814Logic.event.set({
      uniqueCpId: or21814.value.uniqueCpId,
      events: {
        secondBtnClickFlg: false,
        firstBtnClickFlg: false,
      },
    })
  }
)
watch(modelValueOr28412, async (newVal: Or28412Type) => {
  if (newVal) {
    const newItem: DataTableType = {
      /** 連番 */
      kss2Id: '',
      /** ヘッダID */
      kss1Id: '',
      /** 状況の事実（入力用） */
      koumokuKnj: {
        value: '',
      },
      /** 現在欄の適用フラグ */
      tekiyouFlg: newVal.value,
      /** 現在フラグ1 */
      genzai1Flg: {
        value: '',
      },
      /** 要因1〜6 */
      youinFlg: {
        values: [],
      },
      /** 改善/維持の可能性 */
      joukyouFlg: {
        value: '',
      },
      /** 備考 */
      bikoKnj: {
        value: '',
      },
      /** 項目名1・2 */
      koumoku1Knj: '',
      /** 項目名1・2 */
      koumoku2Knj: '',
      /** 表示フラグ */
      showFlg: '',
      /** 中止フラグ */
      stopFlg: '',
      /** 項目ID・ID2 */
      koumoku1Id: '',
      /** 項目ID・ID2 */
      koumoku2Id: '',
      /** 現在フラグ2 */
      genzai2Flg: '',
      /** 現在（入力用） */
      genzaiKnj: '',
      /** 表示順 */
      sort: '',
      /** 更新回数 */
      modifiedCnt: '',
      /** 更新区分 */
      updateKbn: UPDATE_KBN.CREATE,
    }
    if (refValue.value) {
      refValue.value.push([newItem])
    }

    // 新しいアイテムを選択（nextTickでreactive updateを待つ）
    await nextTick()
    selectNewItem()

    // 新しいアイテムにスクロール
    await scrollToNewItem()

    modelValueOr28412.value.value = ''
  }
})

/**
 * アイテム削除処理
 *
 * @function deleteItem
 *
 * @description 選択されたアイテムの削除確認ダイアログを表示する
 *
 * @returns 選択されていない場合はnull、それ以外はvoid
 */
function deleteItem() {
  if (!selectedRowId.value || !selectedItem.value) return null

  // selectedItemはすでに正しく設定されているはず（onSelectRowで設定済み）
  if (selectedItem.value.koumoku1Knj !== null && selectedItem.value.koumoku1Knj !== '') {
    // メッセージ表示（警告）
    showOr21815Msg(t('message.w-cmn-20723'))
  } else {
    showOr21814Msg(t('message.i-cmn-10219'))
  }
}
/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @function showOr21814Msg
 *
 * @description 情報メッセージダイアログを表示する
 *
 * @param infomsg - メッセージ内容
 */
function showOr21814Msg(infomsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.info'),
      // ダイアログテキスト
      dialogText: infomsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
      isOpen: true,
    },
  })
}
/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @function showOr21815Msg
 *
 * @description 警告メッセージダイアログを表示する
 *
 * @param errormsg - メッセージ内容
 */
function showOr21815Msg(errormsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}

/**
 * Or51775ダイアログを開く処理
 *
 * @function Or51775OnClick
 *
 * @description ボタン押下時に呼び出され、子コンポーネントのダイアログを開く
 *
 * @param groupIndex - グループのインデックス
 *
 * @param itemIndex - アイテムのインデックス
 */
function Or51775OnClick(groupIndex: number, itemIndex: number) {
  onSelectRow(groupIndex, itemIndex)
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}
/**
 * Or51775のダイアログ確認処理
 *
 * @function handleConfirm51775
 *
 * @description ダイアログからの確認データを受け取り、備考欄の値を更新する
 *
 * @param data - 確認データ（タイプと値）
 */
const handleConfirm51775 = (data: Or51775ConfirmType) => {
  if (selectedItem.value?.bikoKnj) {
    // 現在の選択位置を保存
    const currentRowId = selectedRowId.value

    // タイプによって処理を分ける
    if (data.type === Or05656Const.VARIABLE.STRING_1) {
      // タイプが1の場合：完全に置き換え
      selectedItem.value.bikoKnj.value = data.value
    } else {
      // タイプが1以外の場合：既存の値に追加
      selectedItem.value.bikoKnj.value = selectedItem.value.bikoKnj.value + data.value
    }

    // データが変更されたことを示すため、updateKbnを更新
    if (selectedItem.value.updateKbn !== UPDATE_KBN.CREATE) {
      updateItemInRefValueByIndex(
        Number(selectedRowId.value.split('-')[0]),
        Number(selectedRowId.value.split('-')[1]),
        UPDATE_KBN.UPDATE
      )
    }

    // 選択状態を再同期
    if (currentRowId) {
      const [groupIndex, itemIndex] = currentRowId.split('-').map(Number)
      selectVisibleItem(groupIndex, itemIndex)
    }
  }
}
/**
 * 更新区分を設定する
 *
 * @param item - 対象のアイテム
 *
 * @param isFlag - フラグ
 */
function handleUpdateKbn(item: DataTableType, isFlag = false) {
  if (isFlag) {
    item.bikoKnj!.value = item.bikoKnj!.value
  }
  if (item.updateKbn !== UPDATE_KBN.CREATE) {
    item.updateKbn = UPDATE_KBN.UPDATE
  }
}

/**
 * 次の選択位置を計算する（循環なし）
 *
 * @function getNextPosition
 *
 * @description 現在の位置から次の位置を計算する（リストの端に達したらnullを返す）
 *
 * @param currentGroupIndex - 現在のグループインデックス
 *
 * @param currentItemIndex - 現在のアイテムインデックス
 *
 * @param isUp - 上に移動するかどうか
 *
 * @returns 次の位置、見つからない場合はnull
 */
function getNextPosition(
  currentGroupIndex: number,
  currentItemIndex: number,
  isUp: boolean
): { groupIndex: number; itemIndex: number } | null {
  const visible = visibleGroupedItems.value
  const direction = isUp ? -1 : 1

  // 現在グループ取得
  const currentGroup = visible[currentGroupIndex]
  if (!currentGroup) return null

  // 同じグループ内で移動
  const nextItemIndex = currentItemIndex + direction
  if (nextItemIndex >= 0 && nextItemIndex < currentGroup.length) {
    return { groupIndex: currentGroupIndex, itemIndex: nextItemIndex }
  }

  // 隣のグループに移動（同じ方向）
  const nextGroupIndex = currentGroupIndex + direction
  if (nextGroupIndex >= 0 && nextGroupIndex < visible.length) {
    const nextGroup = visible[nextGroupIndex]
    if (nextGroup && nextGroup.length > 0) {
      const itemIndex = isUp ? nextGroup.length - 1 : 0
      return { groupIndex: nextGroupIndex, itemIndex }
    }
  }

  // 最後または最初の要素だった場合 → 移動不可
  return null
}

/**
 * 選択行を上に移動する
 *
 * @function moveUp
 *
 * @description 現在の選択行を1行上に移動し、自動的にスクロールする
 */
function moveUp() {
  if (!selectedRowId.value) return

  const [currentGroupIndex, currentItemIndex] = selectedRowId.value.split('-').map(Number)
  const nextPosition = getNextPosition(currentGroupIndex, currentItemIndex, true)

  if (nextPosition) {
    selectVisibleItem(nextPosition.groupIndex, nextPosition.itemIndex)
    scrollToSelectedItem()
  }
}

/**
 * 選択行を下に移動する
 *
 * @function moveDown
 *
 * @description 現在の選択行を1行下に移動し、自動的にスクロールする
 */
function moveDown() {
  if (!selectedRowId.value) return

  const [currentGroupIndex, currentItemIndex] = selectedRowId.value.split('-').map(Number)
  const nextPosition = getNextPosition(currentGroupIndex, currentItemIndex, false)

  if (nextPosition) {
    selectVisibleItem(nextPosition.groupIndex, nextPosition.itemIndex)
    scrollToSelectedItem()
  }
}

/**
 * 選択されたアイテムまでスクロールする
 *
 * @function scrollToSelectedItem
 *
 * @description 現在選択されているアイテムの位置までスムーズにスクロールする
 */
function scrollToSelectedItem() {
  void nextTick(() => {
    const tableComponent = dataTableRef.value as { $el: HTMLElement }
    if (tableComponent?.$el) {
      const tableWrapper = tableComponent.$el.querySelector(
        Or05656Const.VARIABLE.TABLE_SCROLL_CONTAINER
      )
      if (tableWrapper) {
        // 選択された行の要素を取得
        const selectedRow = tableWrapper.querySelector('.highlight-row')
        if (selectedRow) {
          // 選択された行までスクロール
          selectedRow.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
          })
        }
      }
    }
  })
}
/**
 * 表示制御用に文字列を最大34文字＋[…]に省略する
 *
 * @param text - 入力文字列
 *
 * @param numPerLine - 1行あたりの文字数
 *
 * @param numberLine - 行数
 *
 * @returns 表示用文字列（最大34文字、超過部分は[…]で省略）
 */
function formatForDisplay(text: string, numPerLine: number, numberLine: number): string {
  if (!text?.length) return ''

  const cleaned = text.replace(/\r?\n/g, '').trim()
  const lines: string[] = []
  let line = '',
    i = 0

  while (i < cleaned.length && lines.length < numberLine) {
    line += cleaned[i]
    if (line.length === numPerLine) {
      lines.push(line)
      line = ''
    }
    i++
  }

  if (line) lines.push(line)

  if (i < cleaned.length && lines.length === numberLine) {
    lines[numberLine - 1] = lines[numberLine - 1].slice(0, numPerLine - 1) + '…'
  }

  return lines.join('\n')
}

defineExpose({
  init,
  createItem,
  deleteItem,
  moveUp,
  moveDown,
  selectedRowIndex,
  totalItemCount,
})
// Or51775OnClick
</script>

<template>
  <c-v-form ref="tableForm">
    <c-v-data-table
      ref="dataTableRef"
      fixed-header
      :headers="headers"
      :items="visibleGroupedItems"
      hide-default-footer
      :items-per-page="-1"
      class="table-wrapper"
      style="width: 1249px !important"
    >
      <template #headers="{ columns }">
        <tr>
          <th
            rowspan="2"
            colspan="2"
            class="first-head-cell"
            :style="{ minWidth: '200px' }"
          >
            {{ columns[0].title }}
            <base-mo00009
              class="custom-info"
              :oneway-model-value="localOneway.selectOptionsIcon"
            >
              <c-v-tooltip
                activator="parent"
                location="right"
                interactive
              >
                <div class="tooltip-content">
                  {{ tooltip.koumoku1Id }}
                </div>
              </c-v-tooltip>
            </base-mo00009>
          </th>

          <th
            colspan="4"
            class="text-center"
            style="border-left: none !important"
          ></th>
        </tr>
        <tr>
          <th
            v-for="(column, index) in columns.slice(1)"
            :key="column.key"
            :style="{ minWidth: column.width }"
          >
            <div class="d-flex justify-space-between align-center">
              <span>
                {{ column.title }}
                <base-mo00009
                  v-if="index !== columns.length - 2"
                  class="custom-info"
                  :oneway-model-value="localOneway.selectOptionsIcon"
                >
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="tooltip[column.key as keyof TooltipMap]"
                  />
                </base-mo00009>
              </span>
            </div>
          </th>
        </tr>
      </template>

      <template #item="{ item, index }">
        <template
          v-for="(i, idx) in item"
          :key="i.koumoku2Id"
        >
          <tr
            :class="{ 'highlight-row': selectedRowId === `${index}-${idx}` }"
            @click="onSelectRow(index, idx)"
          >
            <!-- col1 -->
            <td
              v-if="
                idx === Or05656Const.VARIABLE.NUMBER_0 &&
                i.showFlg === Or05656Const.VARIABLE.STRING_1
              "
              :rowspan="item?.length"
              :colspan="i.koumoku2Knj === null || i.koumoku2Knj === '' ? 2 : 1"
              class="align-center first-col"
              :class="{
                'custom-td-first-col ': item?.length === 1,
              }"
              width="106px"
              max-width="106px"
              style="position: relative"
            >
              <base-mo01337
                :oneway-model-value="{ value: i.koumoku1Knj }"
                class="custom-mo01337"
              />
            </td>
            <!-- col2 -->
            <td
              v-if="i.koumoku1Id === null || i.koumoku1Id === ''"
              :rowspan="item?.length"
              width="231px"
              max-width="231px"
              :colspan="2"
            >
              <div class="d-flex flex-row align-center custom-input-mo01274">
                <!-- <div class="custom-checkbox-border">
                  <base-mo01332
                    class="w-100"
                    v-model="item.checkbox"
                    :oneway-model-value="localOneway.mo01332OnewayType"
                  />
                </div> -->
                <div class="custom-input-mo01274">
                  <base-mo01274
                    v-model="i.koumokuKnj"
                    :oneway-model-value="localOneway.mo01274KoumokuKnjOneWay"
                    class="w-100"
                  />
                </div>
              </div>
            </td>
            <!-- col2 normal -->
            <td
              v-if="
                i.koumoku2Knj !== null &&
                i.koumoku2Knj !== '' &&
                i.showFlg === Or05656Const.VARIABLE.STRING_1
              "
              width="141px"
              max-width="141px"
              :class="{
                'custom-td-border-top ': idx > 0,
              }"
              class="custom-td"
            >
              <base-mo01337
                :oneway-model-value="{ value: i.koumoku2Knj }"
                class="custom-mo01337"
              />
            </td>
            <!-- col3 -->
            <td
              style="padding: 0px !important"
              width="347px"
              max-width="347px"
              :class="[
                {
                  'custom-td-border-top ': idx > 0,
                },
                'radio-cell',
              ]"
            >
              <base-mo01360
                v-if="i.tekiyouFlg === Or05656Const.VARIABLE.TEKIYOU_FLG_1"
                v-model="i.genzai1Flg"
                :oneway-model-value="localOneway.mo01360TekiyouFlg1OneWay"
                class="col-radio"
                @click="handleUpdateKbn(i)"
              />
              <div
                v-if="i.tekiyouFlg === Or05656Const.VARIABLE.TEKIYOU_FLG_2"
                style="padding-left: 90px; padding-right: 65px"
              >
                <base-mo01360
                  v-model="i.genzai1Flg"
                  :oneway-model-value="localOneway.mo01360TekiyouFlg2OneWay"
                  class="col-radio"
                  @click="handleUpdateKbn(i)"
                />
              </div>
              <div
                class="custom-input-mo01274 w-auto ml-2 mr-2"
                v-if="i.tekiyouFlg === Or05656Const.VARIABLE.TEKIYOU_FLG_3"
              >
                <base-mo01274
                  v-model="i.genzai1Flg"
                  :oneway-model-value="localOneway.mo01274TekiyouFlg3OneWay"
                />
              </div>
            </td>
            <!-- col4 -->
            <td
              width="154px"
              max-width="154px"
              style="padding-left: 8px !important"
              :class="{
                'custom-td-border-top ': idx > 0,
              }"
            >
              <base-mo01332
                v-model="i.youinFlg"
                class="col-checkbox"
                :oneway-model-value="localOneway.mo01332AtAnyTime"
                @click="handleUpdateKbn(i)"
              />
            </td>
            <!-- col5 -->
            <td
              width="220px"
              max-width="220px"
              :class="[
                {
                  'custom-td-border-top ': idx > 0,
                },
                'radio-cell',
              ]"
            >
              <base-mo01360
                v-model="i.joukyouFlg"
                :oneway-model-value="localOneway.mo01360PotentialOneWay"
                class="col-radio"
                :check-off="true"
                @click="handleUpdateKbn(i)"
              />
            </td>
            <!-- col6 -->
            <td
              :class="[
                {
                  'custom-td-border-top ': idx > 0,
                },
                'text-area-cell',
              ]"
            >
              <g-custom-or-x-0163
                v-model="i.bikoKnj"
                :oneway-model-value="{
                  height: '40px',
                  readOnly: false,
                  align: 'center',
                  showEditBtnFlg: true,
                  rows: 2,
                }"
                @on-click-edit-btn="Or51775OnClick(index, idx)"
              ></g-custom-or-x-0163>
            </td>
          </tr>
        </template>
      </template>
    </c-v-data-table>
    <g-custom-or-28412
      v-if="showDialogOr28412"
      v-bind="or28412"
      v-model="modelValueOr28412"
    />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    />
    <g-base-or21815
      v-if="showDialogOr21815"
      v-bind="or21815"
    />
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="modelValueOr51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="handleConfirm51775"
    />
  </c-v-form>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';
.col-radio {
  display: flex !important;
  justify-content: center;
  :deep(div) {
    display: flex !important;
    align-items: center;
    gap: 4px;
  }
}

.col-textarea {
  overflow: hidden;
  white-space: pre-line;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  border: none !important;
}

.col-checkbox {
  display: grid;
  grid-template-columns: repeat(3, 0.3fr);
  :deep(div) {
    padding: 2px !important;
  }
  :deep(label) {
    height: 14px !important;
    width: 14px !important;
    border: 1px solid rgb(var(--v-theme-black-900));
    border-radius: 90px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 8px !important;
  }
}

.grid-3-1 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}
.border {
  border: 1px solid rgb(var(--v-theme-black-100));
}
.border-bottom {
  border-bottom: 1px solid rgb(var(--v-theme-black-100));
}
.border-right {
  border-right: 1px dashed rgb(var(--v-theme-black-100));
}

.custom-td {
  :deep(.v-col) {
    padding: 0 !important;
  }
  .custom-mo01337 {
    margin-right: 0 !important;
  }
}
.list-wrapper :deep(.v-table__wrapper) {
  overflow-y: auto; // 縦スクロールを有効化
}

:deep(.table-wrapper) {
  .v-table__wrapper {
    th {
      color: #333333;
      font-size: 13px;
      font-weight: 400;
      height: 40px !important;
      background-color: #dbeefe !important;
    }
    td {
      &.custom-td-border-top {
        border-top: 1px rgb(var(--v-theme-black-200)) dashed !important;
      }
      &.radio-cell {
        .v-input__control {
          width: 100% !important;
          .v-selection-control-group {
            display: flex;
            width: 100% !important;
            .v-selection-control {
              flex: 1;
              display: flex;
              justify-content: center;
            }
          }
        }
      }
      &.text-area-cell {
        position: relative;
        .col-textarea {
          padding-left: 30px !important;
        }
        .btn-cell {
          position: absolute;
          left: 0;
          top: 15%;
        }
      }

      font-size: 13px;
      height: 53px !important;
      &.first-col {
        padding: 8px 12px;
        vertical-align: top;
      }
    }
  }
  .v-table__wrapper tr:not(.row-selected):not(.select-row) td:not(:has(input, textarea, select)) {
    background-color: #dbeefe;
  }
}
:deep(.table-wrapper) {
  .v-table__wrapper {
    td {
      &.custom-td-first-col {
        vertical-align: middle !important;
      }
    }
  }
}

:deep(.v-radio) {
  .v-label {
    margin-left: 8px !important;
    letter-spacing: -1px !important;
  }
}

:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 14px !important;
}
:deep(
  .table-wrapper .v-table__wrapper td:has(textarea) .table-cell:not([disabled]):not([readonly])
) {
  outline: none !important;
}
.custom-input-mo01274 {
  height: 48px;
  padding: 0 4px !important;
  width: 100%;
  // margin-left: 8px;
}
.custom-checkbox-border {
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  height: 64px;
  width: 58px;
  padding-left: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.head-cell-label {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}
.tooltip-content {
  white-space: pre;
}
:deep(.custom-info) {
  .material-symbols-rounded {
    font-variation-settings:
      'FILL' 1,
      'wght' 400,
      'GRAD' 0,
      'opsz' 20;
    opacity: 0.7;
    font-size: 20px;
  }
}
</style>
