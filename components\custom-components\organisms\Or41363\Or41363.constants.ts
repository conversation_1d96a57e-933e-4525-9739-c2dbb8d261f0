import { getSequencedCpId } from '~/utils/useScreenUtils'
/**
 * Or41363:有機体:（職員管理）検索結果表示用一覧
 */
export namespace Or41363Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or41363', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）ヘッダー
     */
    export const HEADERS = []
    /**
     * （初期値）一覧のデータリスト
     */
    export const ITEMS = []

    /**
     * ログイン日時の許容する差分日付
     */
    export const LOGIN_DATE_ACCEPT_DIFF = 7

    /**
     * （初期値）1ページあたりの表示数
     */
    export const LIMIT = '20'

    /**
     * （初期値）ページ
     */
    export const PAGE = 1
  }
}
