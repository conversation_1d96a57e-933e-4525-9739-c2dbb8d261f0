<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { OrX0007Const } from '../OrX0007/OrX0007.constants'
import { OrX0008Const } from '../OrX0008/OrX0008.constants'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { OrX0010Const } from '../OrX0010/OrX0010.constants'
import { Or15843Const } from '../Or15843/Or15843.constants'
import { OrX0107Const } from '../OrX0107/OrX0107.constants'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0008Logic } from '../OrX0008/OrX0008.logic'
import { OrX0010Logic } from '../OrX0010/OrX0010.logic'
import { OrX0009Logic } from '../OrX0009/OrX0009.logic'
import { Or15843Logic } from '../Or15843/Or15843.logic'
import { Or27294Logic } from '../Or27294/Or27294.logic'
import { Or27557Const } from '../Or27557/Or27557.constants'
import { Or27557Logic } from '../Or27557/Or27557.logic'
import { Or31915Const } from '../Or31915/Or31915.constants'
import { Or31915Logic } from '../Or31915/Or31915.logic'
import { OrX0107Logic } from '../OrX0107/OrX0107.logic'
import { Or27294Const } from '../Or27294/Or27294.constants'
import { Or28256Const } from '../Or28256/Or28256.constants'
import { Or28256Logic } from '../Or28256/Or28256.logic'
import type { Or28256OnewayType, Or28256DataType } from '../Or28256/Or28256.type'
import type {
  GamenRirekiOtherInfo,
  OrX0107OnewayType,
} from '../../../../types/cmn/business/components/OrX0107Type'
import { Or31484Logic } from '../Or31484/Or31484.logic'
import { Or31484Const } from '../Or31484/Or31484.constants'
import type { Or31308StateType, OrX0007RefFunc, OrX0107RefFunc } from './Or31308.type'
import { Or31308Const } from './Or31308.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import type {
  ImplementationPlan3InitMasterInfo,
  ImplementationPlan3SelectOutEntity,
  IHistory,
  IDataInfo,
  ImplementationPlan3SelectOutData,
} from '~/repositories/cmn/entities/ImplementationPlan3SelectEntity'
import type { GyoumuComUpdateOutEntity } from '~/repositories/cmn/entities/GyoumuComUpdateEntity'
import type {
  Or21814OnewayType,
  Or21814EventType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'

import type { Or27557OnewayType, userList } from '~/types/cmn/business/components/Or27557Type'
import type {
  ImplementationPlan3CopyReturnSelectInEntity,
  ImplementationPlan3CopyReturnSelectOutEntity,
} from '~/repositories/cmn/entities/ImplementationPlan3CopyReturnSelectEntity'
import type { Or31915OnewayType, Or31915Type } from '~/types/cmn/business/components/Or31915Type'
import type { OrX0007Type, OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import type {
  OrX0008Type,
  OrX0008OnewayType,
  RirekiInfo,
} from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009Type, OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0010Type, OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import type { Or15843Type, Or15843OnewayType } from '~/types/cmn/business/components/Or15843Type'
import type { GamenDataInfo, OrX0107Type } from '~/types/cmn/business/components/OrX0107Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import {
  useCommonProps,
  useGyoumuCom,
  useScreenInitFlg,
  useScreenStore,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
  useJigyoList,
} from '#imports'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { SPACE_WAVE, UPDATE_KBN } from '~/constants/classification-constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type {
  ImplementationPlan3UpdateInEntity,
  IDataSave,
} from '~/repositories/cmn/entities/ImplementationPlan3UpdateEntity'
import type {
  JigyoList,
  Or27294OnewayType,
  Or27294Type,
} from '~/types/cmn/business/components/Or27294Type'
import type {
  IKikanComInfo,
  GyoumuComSelectInEntity,
} from '~/repositories/cmn/entities/GyoumuComSelectEntity'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { hasRegistAuth, hasPrintAuth } from '~/utils/useCmnAuthz'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type { Or31484Type, Or31484OnewayType } from '~/types/cmn/business/components/Or31484Type'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type {
  Or21815StateType,
  Or21815EventType,
} from '~/components/base-components/organisms/Or21815/Or21815.type'
/**
 * Or31308:有機体:実施計画～③メイン（画面/特殊コンポーネント）
 * GUI00964_実施計画～③
 *
 * @description
 * 実施計画～③メイン画面の処理
 *
 * <AUTHOR>
 */
/**
 *国際化対応の翻訳関数（i18n）を取得
 */
const { t } = useI18n()
// route共有情報
const cmnRouteCom = useCmnRouteCom()
/**
 *システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 *共有処理
 */
const gyoumuCom = useGyoumuCom()
/** props */
const props = defineProps(useCommonProps())
/**
 *利用者選択監視関数を実行
 */
const { syscomUserSelectWatchFunc } = useUserListInfo()
/**
 *子コンポーネント
 */
const { setChildCpBinds, searchUniqCpId } = useScreenUtils()
/**
 * jigyoListWatch
 */
const { jigyoListWatch } = useJigyoList()
/**
 *変数定義
 */
const defaultModelValue = {
  or31308: {
    operaFlg: Or31308Const.OPERA_FLG_0,
    svJigyoRyakuKnj: '',
    kesuTorikomiFlg: Or31308Const.KESU_TORIKOMI_0,
    kadaiTorikomiFlg: Or31308Const.KADAI_TORIKOMI_0,
    kaiteiFlg: '',
    kikanFlg: '',
    kikanObj: {} as IKikanComInfo,
    rirekiObj: {} as IHistory,
    dataList: [] as IDataInfo[],
    syubetuId: '',
    moveSaveConfirmFlg: Or31308Const.COMFIRM_SAVE_0,
    rirekiPagingNo: '',
    rirekiId: '0',
    //初期設定マスタの情報
    initMasterObj: {
      //パッケージプラン改訂フラグ
      pkaiteiFlg: '1',
      //実施計画～③：期間のカレンダー取込み※0:?/?～?/?、1:?月?日～?月?日、2:?/?、3:?月?日
      pkkak23YmdFlg: '0',
      //ケアプラン方式
      cpnFlg: '0',
    } as ImplementationPlan3InitMasterInfo,
    /** 初回フラグ  */
    onMountedOpenFlg: Or31308Const.ONMOUNTED_OPEN_0,
  } as Or31308StateType,
}

/**
 *変数定義
 */
const defaultComponents = {
  // 期間データ
  orX0007: { PlanTargetPeriodUpdateFlg: '' } as OrX0007Type,
  // 履歴
  orX0008: {
    createId: '0',
    createUpateFlg: '',
  } as OrX0008Type,
  // 作成者
  orX0009: { staffId: '', staffName: '' } as OrX0009Type,
  // 作成日
  orX0010: { value: '' } as OrX0010Type,
  // ケース番号
  or15843: {
    value: '',
  } as Or15843Type,
  // 初回作成日
  or31915: { value: '' } as Or31915Type,
  // サブセクション(総合的課題・目標・支援計画)、支援内容一覧
  orX0107: { rirekiOtherData: {} as GamenRirekiOtherInfo } as OrX0107Type,
  // GUI00965 実施計画～③複写画面
  or31484: { rirekiId: '' } as Or31484Type,
}

/**
 *変数定義
 */
const local = reactive({
  or31308: {
    ...defaultModelValue.or31308,
  } as Or31308StateType,
  // GUI01016 ケース一覧画面をポップアップで起動する
  or28256: {} as Or28256DataType,
  or27294: {} as Or27294Type,
})

/**
 *変数定義
 */
const localComponents = reactive({
  orX0007: {
    ...defaultComponents.orX0007,
  } as OrX0007Type,
  orX0008: {
    ...defaultComponents.orX0008,
  } as OrX0008Type,
  orX0009: {
    ...defaultComponents.orX0009,
  } as OrX0009Type,
  orX0010: {
    ...defaultComponents.orX0010,
  } as OrX0010Type,
  or15843: {
    ...defaultComponents.or15843,
  } as Or15843Type,
  or31915: {
    ...defaultComponents.or31915,
  } as Or31915Type,
  orX0107: {
    ...defaultComponents.orX0107,
  } as OrX0107Type,
  or31484: {
    ...defaultComponents.or31484,
  } as Or31484Type,
})

/**
 *各コンポーネントに渡すonewayデータ
 */
const defaultOneway = reactive({
  // 期間データ
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as OrX0007OnewayType,
  // 履歴
  orX0008Oneway: {
    createData: {} as RirekiInfo,
    screenID: 'GUI00964',
  } as OrX0008OnewayType,
  // 作成者
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
    isDisabled: false,
  } as OrX0009OnewayType,
  // 作成日
  orX0010Oneway: {} as OrX0010OnewayType,
  // ケース番号
  or15843Oneway: {
    isRequired: false,
    showItemLabel: true,
    width:'99px',
  } as Or15843OnewayType,
  //初回作成日
  or31915Oneway: { isRequired: false} as Or31915OnewayType,
  // サブセクション(総合的課題・目標・支援計画)、支援内容一覧
  orX0107Oneway: {} as OrX0107OnewayType,
  // GUI00965 実施計画～③複写画面
  or31484Oneway: {} as Or31484OnewayType,
  // 印刷設定画面
  or27557Oneway: {} as Or27557OnewayType,
})

/**
 *各コンポーネントに渡すonewayデータ
 */
const localOneway = reactive({
  orX0007Oneway: {
    ...defaultOneway.orX0007Oneway,
  } as OrX0007OnewayType,
  orX0008Oneway: {
    ...defaultOneway.orX0008Oneway,
  } as OrX0008OnewayType,
  orX0009Oneway: {
    ...defaultOneway.orX0009Oneway,
  } as OrX0009OnewayType,
  orX0010Oneway: {
    ...defaultOneway.orX0010Oneway,
  } as OrX0010OnewayType,
  or15843Oneway: {
    ...defaultOneway.or15843Oneway,
  } as Or15843OnewayType,
  or31915Oneway: {
    ...defaultOneway.or31915Oneway,
  } as Or31915OnewayType,
  or27294Oneway: {} as Or27294OnewayType,
  orX0107Oneway: {
    ...defaultOneway.orX0107Oneway,
  } as OrX0107OnewayType,
  // GUI01016 ケース一覧画面をポップアップで起動する
  or28256Oneway: {} as Or28256OnewayType,
  or31484Oneway: {
    ...defaultOneway.or31484Oneway,
  } as Or31484OnewayType,
  or27557Oneway: {
    ...defaultOneway.or27557Oneway,
  } as Or27557OnewayType,
})

/**
 *orX0107Ref
 */
const orX0107Ref = ref<OrX0107RefFunc>()
const orX0007Ref = ref<OrX0007RefFunc | null>(null)
const or15843Ref = ref<{ isValid: () => Promise<boolean>}>()

const or11871 = ref({ uniqueCpId: '' })
const or00248_1 = ref({ uniqueCpId: '' })
const orX0007_1 = ref({ uniqueCpId: '' })
const orX0008_1 = ref({ uniqueCpId: '' })
const orX0009_1 = ref({ uniqueCpId: '' })
const orX0010_1 = ref({ uniqueCpId: '' })
const or15843_1 = ref({ uniqueCpId: '' })
const or31915_1 = ref({ uniqueCpId: '' })
const orX0107_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or27294_1 = ref({ uniqueCpId: Or27294Const.CP_ID(1) })
const or28256_1 = ref({ uniqueCpId: '' })
const or31484_1 = ref({ uniqueCpId: '' })
const or41179_1 = ref({ uniqueCpId: '' })
const or27557_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })

/**
 *ダイアログ表示フラグ
 */
const showDialogOr27294 = computed(() => {
  return Or27294Logic.state.get(or27294_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr28256 = computed(() => {
  // Or28256のダイアログ開閉状態
  return Or28256Logic.state.get(or28256_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 *ダイアログ表示フラグ
 */
const showDialogOr31484 = computed(() => {
  return Or31484Logic.state.get(or31484_1.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr27557 = computed(() => {
  return Or27557Logic.state.get(or27557_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const isInit = useScreenInitFlg()

onMounted(async () => {
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      disabledSaveBtn: !(await hasRegistAuth(Or31308Const.LINK_AUTH)), //共通処理の保存権限チェックを行う
      disabledPrintBtn: !(await hasPrintAuth(Or31308Const.LINK_AUTH)), //共通処理の印刷権限チェックを行う
    },
  })

  // 利用者を全選択です。
  const uniqueCpId248 = searchUniqCpId(props.uniqueCpId, Or00248Const.CP_ID(1), 0)
  if (uniqueCpId248) {
    const uniqueCpId94 = searchUniqCpId(uniqueCpId248, Or00094Const.CP_ID(0), 0)
    if (uniqueCpId94) {
      Or00094Logic.state.set({
        uniqueCpId: uniqueCpId94,
        state: {
          dispSettingBtnDisplayFlg: true,
          focusSettingFlg: true,
          focusSettingInitial: [Or31308Const.STR_ALL],
        },
      })
    }
  }
  local.or31308.onMountedOpenFlg = Or31308Const.ONMOUNTED_OPEN_0
  // 事業所選択プルダウンの検索条件設定
  // 検索条件未指定だと、ヘッダーで選択した適用事業所が表示される
  // 検索条件が変更されると、共通処理にて事業所の情報が更新される
  // 更新後やプルダウンの値変更後、変更後の事業所に基づいて処理を行う場合、
  // 関数「 jigyoListWatch 」にコールバック関数を渡す
  local.or31308.searchSelfUserID = systemCommonsStore.getUserSelectSelfId() ?? ''
  // 初期情報取得
  if (isInit) {
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        verticalLayout: true,
        labelClassVertical: 'mb-1',
        selectClassVertical: 'mt-1',
        searchCriteria: {
          selfId: local.or31308.searchSelfUserID,
        },
      },
    })
    await initOr31308()
  }
  local.or31308.onMountedOpenFlg = Or31308Const.ONMOUNTED_OPEN_1
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248_1.value,
  [Or41179Const.CP_ID(1)]: or41179_1.value,
  [OrX0007Const.CP_ID(1)]: orX0007_1.value,
  [OrX0008Const.CP_ID(1)]: orX0008_1.value,
  [OrX0009Const.CP_ID(1)]: orX0009_1.value,
  [OrX0010Const.CP_ID(1)]: orX0010_1.value,
  [Or15843Const.CP_ID(1)]: or15843_1.value,
  [Or31915Const.CP_ID(1)]: or31915_1.value,
  [OrX0107Const.CP_ID(1)]: orX0107_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or27294Const.CP_ID(1)]: or27294_1.value,
  [Or28256Const.CP_ID(1)]: or28256_1.value,
  [Or31484Const.CP_ID(1)]: or31484_1.value,
  [Or27557Const.CP_ID(1)]: or27557_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
})

const or00249 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(or00248_1.value.uniqueCpId, {
  [Or00249Const.CP_ID(0)]: or00249.value,
})

Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.implementation-plan-three'),
    showFavorite: true,
    showViewSelect: false,
    showCreateBtn: true,
    tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    viewSelectItems: [],
    showMasterBtn: true,
    showOptionMenuDelete: true,
    showSaveBtn: true,
    tooltipTextSaveBtn: t('tooltip.care-plan2-save-btn'),
  },
})

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }

    // お気に入りアイコンボタン
    if (newValue.favoriteEventFlg) {
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { favoriteEventFlg: false },
      })
    }
    // 保存ボタンが押下された場合、保存処理を実行する
    if (newValue.saveEventFlg) {
      await _save()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { saveEventFlg: false },
      })
    }
    // 新規ボタンが押下された場合、新規作成処理を実行する
    if (newValue.createEventFlg) {
      await _create()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { createEventFlg: false },
      })
    }
    // 印刷ボタンが押下された場合、印刷設定画面を表示する
    if (newValue.printEventFlg) {
      await _print()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { printEventFlg: false },
      })
    }
    // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    if (newValue.masterEventFlg) {
      await _master()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { masterEventFlg: false },
      })
    }
    // 削除ボタンが押下された場合、削除処理を実行する
    if (newValue.deleteEventFlg) {
      await _delete()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { deleteEventFlg: false },
      })
    }
    // 複写ボタンが押下された場合、複写処理を実行する
    if (newValue.copyEventFlg) {
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { copyEventFlg: false },
      })
    }
  }
)

/**
 * 事業所選択更新の監視
 */
// ★事業所選択監視関数を実行
jigyoListWatch(or41179_1.value.uniqueCpId, (newJigyoId: string) => {
  void callbackFuncJigyo(newJigyoId)
})
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function callbackFuncJigyo(newJigyoId: string) {
  const oldJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  await gyoumuCom.doComLogicChangeSvJigyo(
    newJigyoId,
    oldJigyoId,
    local.or31308,
    isEdit.value,
    await hasRegistAuth(Or31308Const.LINK_AUTH),
    showConfirmMessageBox,
    showWarnMessageBox,
    _save,
    getImplementationPlan3Data,
    setCommonPlanPeriod,
    setSvJigyoId,
    setSelectSelUserIndex
  )
}

/**
 * 事業所設定
 *
 * @param jigyoId - 設定の事業者ID
 */
const setSvJigyoId = (jigyoId: string) => {
  systemCommonsStore.setSvJigyoId(jigyoId)
  Or41179Logic.data.set({
    uniqueCpId: or41179_1.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    } as Mo00040Type,
  })
}

/**
 * 利用者選択監視関数に渡すコールバック関数（サブ01の例）
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 利用者
 */
const callbackFuncUser = (newSelfId: string) => {
  local.or31308.newSelfUserID = newSelfId
  if (newSelfId !== '') {
    // 事業所選択プルダウンの検索条件を更新
    // 検索条件を変更すると、変更を検知して事業所情報の更新が行われる
    // 更新が完了すると、関数「 jigyoListWatch 」に指定したコールバック関数が実行されます
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: newSelfId,
        },
      },
    })
  }
}

/**
 * 利用者設定
 *
 * @param index - 利用者IDのインデックス
 */
const setSelectSelUserIndex = (index: number) => {
  //利用者設定
  Or00249Logic.data.set({
    uniqueCpId: or00249.value.uniqueCpId,
    value: {
      selectUserIndex: index,
    },
  })
}

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackFuncUser)

/**
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007_1.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    // 計画期間変更区分
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg
    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }
    // 計画期間ID
    const planID = newValue.planTargetPeriodId
    // ポップアップ画面で「確定ボタン」押下、ポップアップ画面での返回値.計画期間IDが0より大きい場合
    if (planUpdateFlg === Or31308Const.ACTION_UPDATE_0 && planID !== '' && Number(planID) > 0) {
      // 下記の項目を設定する
      setCommonPlanPeriod({ ...local.or31308.kikanObj, sc1Id: planID })
      local.or31308.rirekiId = '0'
      local.or31308.operaFlg = Or31308Const.OPERA_FLG_K3
      //データ再取得フラグ設定
      await getImplementationPlan3Data()
    } else if (planUpdateFlg === Or31308Const.ACTION_UPDATE_1) {
      //「期間-前へ アイコンボタン」押下
      // 下記の項目を設定する
      local.or31308.operaFlg = Or31308Const.OPERA_FLG_K1
      local.or31308.rirekiId = '0'
      //遷移保存確認区分
      local.or31308.moveSaveConfirmFlg = isEdit.value
        ? Or31308Const.COMFIRM_SAVE_1
        : Or31308Const.COMFIRM_SAVE_0
      //データ再取得フラグ設定
      await getImplementationPlan3Data()
    } else if (planUpdateFlg === Or31308Const.ACTION_UPDATE_2) {
      //「期間-次へ アイコンボタン」押下
      local.or31308.operaFlg = Or31308Const.OPERA_FLG_K2
      local.or31308.rirekiId = '0'
      //遷移保存確認区分
      local.or31308.moveSaveConfirmFlg = isEdit.value
        ? Or31308Const.COMFIRM_SAVE_1
        : Or31308Const.COMFIRM_SAVE_0
      //データ再取得フラグ設定
      await getImplementationPlan3Data()
    }
  },
  { deep: true }
)

/**
 * 履歴変更の監視
 * GUI00968 ［履歴選択］画面 実施計画～③画面をポップアップで起動する
 */
watch(
  () => OrX0008Logic.data.get(orX0008_1.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }

    const createUpateFlg = newValue.createUpateFlg
    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }

    // ポップアップ画面で「確定ボタン」押下、
    if (createUpateFlg === Or31308Const.ACTION_UPDATE_0) {
      //「履歴-選択確認後 アイコンボタン」押下
      const rirekiId = String(newValue.createId)
      // 返回値.履歴IDが共通情報.履歴IDが違い場合
      if (rirekiId !== local.or31308.rirekiId) {
        local.or31308.rirekiId = rirekiId
        local.or31308.operaFlg = Or31308Const.OPERA_FLG_R3

        // 表示用「実施計画③履歴」情報を設定する
        local.or31308.rirekiObj.rirekiId = rirekiId
        local.or31308.rirekiObj.shokuId = newValue.rirekiObj?.staffId ?? ''
        local.or31308.rirekiObj.shokuKnj = newValue.rirekiObj?.staffName ?? ''
        local.or31308.rirekiObj.createYmd = newValue.rirekiObj?.createDate ?? ''
        local.or31308.rirekiObj.caseNo = newValue.rirekiObj?.caseNo ?? ''
        local.or31308.rirekiObj.shokaiYmd = newValue.rirekiObj?.shokaiYmd ?? ''
        local.or31308.rirekiObj.kaiteiFlg = newValue.rirekiObj?.kaiteiFlg ?? ''
        local.or31308.rirekiObj.pagingNo = String(newValue.rirekiObj?.currentIndex ?? 0)
        local.or31308.rirekiObj.pagingCnt = String(newValue.rirekiObj?.totalCount ?? 0)

        //データ再取得フラグ設定
        await getImplementationPlan3Data()
      }
    } else if (createUpateFlg === Or31308Const.ACTION_UPDATE_1) {
      //「履歴-前へ アイコンボタン」押下
      local.or31308.operaFlg = Or31308Const.OPERA_FLG_R1
      //遷移保存確認区分
      local.or31308.moveSaveConfirmFlg = isEdit.value
        ? Or31308Const.COMFIRM_SAVE_1
        : Or31308Const.COMFIRM_SAVE_0
      //データ再取得フラグ設定
      await getImplementationPlan3Data()
    } else if (createUpateFlg === Or31308Const.ACTION_UPDATE_2) {
      //「履歴-次へ アイコンボタン」押下
      local.or31308.operaFlg = Or31308Const.OPERA_FLG_R2
      //遷移保存確認区分
      local.or31308.moveSaveConfirmFlg = isEdit.value
        ? Or31308Const.COMFIRM_SAVE_1
        : Or31308Const.COMFIRM_SAVE_0
      //データ再取得フラグ設定
      await getImplementationPlan3Data()
    }
  },
  { deep: true }
)

/**
 * 「マスタアイコンボタン」押下
 * GUI00963 実施計画③マスタ画面閉じる場合
 */
watch(
  () => Or27294Logic.state.get(or27294_1.value.uniqueCpId),
  async (newValue) => {
    //Undefinedの時戻す
    if (isUndefined(newValue)) {
      return
    }
    // ポップアップ画面が画面閉じる場合
    if (!newValue.isOpen) {
      // ・共通情報.計画期間ID=0
      setCommonPlanPeriod({ ...local.or31308.kikanObj, sc1Id: '0' })
      // ・共通情報.履歴ID＝0
      local.or31308.rirekiId = '0'
      // ・操作区分＝0:初期化
      local.or31308.operaFlg = Or31308Const.OPERA_FLG_0
      // 画面項目を再検索する
      await getImplementationPlan3Data()
    }
  },
  { deep: true }
)

/**
 * GUI01016 ケース一覧画面の監視
 *
 * @param newValue - 返回値
 */
function doCaseImport(newValue: string) {
  if (newValue && orX0107Ref.value) {
    orX0107Ref.value?.doCaseImport(newValue)
  }
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * メッセージを表示する
 *
 * @param messageId - メッセージ
 */
const showMessageBox = async (messageId: string) => {
  await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t(messageId),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.ok'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
}

/**
 * メッセージを表示する
 *
 * @returns 返回値
 */
const showConfirmMessageBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * メッセージを表示する
 */
const showWarnMessageBox = async () => {
  return await openWarnDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.w-com-10006'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
}

/**
 * メッセージを表示する
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
async function openWarnDialog(state: Or21815StateType): Promise<Or21815EventType | undefined> {
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      ...state,

      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * 共通情報.計画期間を設定
 *
 * @param kikanObj - 計画期間
 */
const setCommonPlanPeriod = (kikanObj: IKikanComInfo) => {
  cmnRouteCom.setPlanPeriod({
    kikanId: kikanObj.sc1Id,
    kikanIndex: kikanObj.pagingNo ?? '',
    startYmd: kikanObj.startYmd ?? '',
    endYmd: kikanObj.endYmd ?? '',
  })
}

/**
 * 初期化処理
 */
const initOr31308 = async () => {
  local.or31308.operaFlg = Or31308Const.OPERA_FLG_0
  //データ取得
  await getImplementationPlan3Data()
}

/**
 * 画面表示のデータを取得
 *
 */
const getImplementationPlan3Data = async () => {
  const inputParam: GyoumuComSelectInEntity = {
    ...gyoumuCom.getGyoumuComSelectInEntity(
      /** 操作区分 */
      local.or31308.operaFlg ?? '',
      /** 遷移保存確認区分 */
      local.or31308.moveSaveConfirmFlg ?? '',
      /** 削除再検索用履歴ページング番号 */
      local.or31308.rirekiPagingNo ?? '',
      /** 計画期間ID */
      cmnRouteCom.getPlanPeriod()?.kikanId
    ),
    /** 履歴 */
    rirekiId:
      local.or31308.rirekiId && local.or31308.rirekiId !== '' ? local.or31308.rirekiId : '0',
  }
  const ret: ImplementationPlan3SelectOutEntity = await ScreenRepository.select(
    'implementationPlan3Select',
    inputParam
  )
  //API戻り値の設定前の処理
  if (
    !(await gyoumuCom.doComLogicSelectApi(
      ret,
      await hasRegistAuth(Or31308Const.LINK_AUTH),
      showMessageBox,
      showConfirmMessageBox,
      showWarnMessageBox,
      _save,
      getImplementationPlan3Data,
      local.or31308,
      {
        dataList: ret.data.dataList ?? [],
        dataHeadObj: ret.data.dataHeadObj ?? {},
        initMasterObj: ret.data.initMasterObj ?? {},
      } as ImplementationPlan3SelectOutData,
      setCommonPlanPeriod
    ))
  ) {
    return
  }

  // 画面初期化のComponents表示フラグ設定、画面初期化のデータ設定
  setFormData(ret)

  setChildCpBinds(props.uniqueCpId, {
    [OrX0007Const.CP_ID(1)]: {
      twoWayValue: {
        planTargetPeriodId: localComponents.orX0007.planTargetPeriodId,
        PlanTargetPeriodUpdateFlg: localComponents.orX0007.PlanTargetPeriodUpdateFlg,
      },
    },
    [OrX0008Const.CP_ID(1)]: {
      twoWayValue: {
        createId: localComponents.orX0008.createId,
        createUpdateFlg: localComponents.orX0008.createUpateFlg,
      },
    },
    [OrX0009Const.CP_ID(1)]: {
      twoWayValue: {
        staffId: localComponents.orX0009.staffId,
        staffName: localComponents.orX0009.staffName,
      },
      oneWayState: {
        isDisabled: local.or31308.operaFlg === Or31308Const.OPERA_FLG_3,
      },
    },
    [OrX0010Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.orX0010.value,
        mo01343: {},
      },
      oneWayState: {
        isDisabled: local.or31308.operaFlg === Or31308Const.OPERA_FLG_3,
      },
    },
    [Or15843Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or15843.value,
      },
      oneWayState: {
        isDisabled: local.or31308.operaFlg === Or31308Const.OPERA_FLG_3,
      },
    },
    [Or31915Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.or31915.value,
        mo01343: {},
      },
      oneWayState: {
        isDisabled: local.or31308.operaFlg === Or31308Const.OPERA_FLG_3,
      },
    },
    [OrX0107Const.CP_ID(1)]: {
      twoWayValue: localComponents.orX0107,
      oneWayState: {
        operaFlg: local.or31308.operaFlg,
        pagingFlg: local.or31308.kikanObj.pagingFlg,
        syubetuId: local.or31308.syubetuId,
        kikanFlg: local.or31308.kikanFlg,
        kadaiTorikomiFlg: local.or31308.kadaiTorikomiFlg,
        initMasterObj: local.or31308.initMasterObj,
      },
    },
  })
  //選定の利用者INDEXを一時保存
  local.or31308.searchSelUserIndex =
    Or00249Logic.data.get(or00249.value.uniqueCpId)?.selectUserIndex ?? 0
}

/**
 *
 * API戻り値を本画面に設定する
 *
 * @param ret - 初期情報
 */
function setFormData(ret: ImplementationPlan3SelectOutEntity) {
  // API戻り値
  const retData = ret.data
  //ケース取込セキュリティチェック
  local.or31308.kesuTorikomiFlg = retData.kesuTorikomiFlg
  //課題取込セキュリティチェック
  local.or31308.kadaiTorikomiFlg = retData.kadaiTorikomiFlg

  const kikanObj = local.or31308.kikanObj
  const rirekiObj = local.or31308.rirekiObj
  localOneway.orX0007Oneway.kindId = local.or31308.syubetuId
  if (kikanObj.pagingFlg !== Or31308Const.PAGING_0) {
    // ページング番号
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = kikanObj.pagingNo
      ? Number(kikanObj.pagingNo)
      : 0
    // ページング総数
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = kikanObj.pagingCnt
      ? Number(kikanObj.pagingCnt)
      : 0
    //表示用「計画対象期間」情報.開始日 + " ～ " + 表示用「計画対象期間」情報.終了日
    if (kikanObj.startYmd) {
      localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriod = kikanObj.startYmd
        .concat(SPACE_WAVE)
        .concat(kikanObj.endYmd ?? '')
    }
    localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId = Number(kikanObj.sc1Id)
    // 計画期間ID
    localComponents.orX0007.planTargetPeriodId = kikanObj.sc1Id
  } else {
    //表示用「計画対象期間」情報.ページング区分が0:なしの場合
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = 0
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = 0
    localComponents.orX0007.planTargetPeriodId = '0'
  }

  //orX0008 実施計画③履歴
  localOneway.orX0008Oneway.sc1Id = kikanObj.sc1Id
  localOneway.orX0008Oneway.officeId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.orX0008Oneway.useId = systemCommonsStore.getUserSelectSelfId() ?? ''

  localOneway.orX0008Oneway.createData.createDate = rirekiObj.createYmd
  localOneway.orX0008Oneway.createData.createId = rirekiObj.rirekiId ?? '0'
  localOneway.orX0008Oneway.createData.currentIndex = rirekiObj.pagingNo
    ? Number(rirekiObj.pagingNo)
    : 0
  localOneway.orX0008Oneway.createData.staffId = rirekiObj.shokuId ?? '0'
  localOneway.orX0008Oneway.createData.staffName = rirekiObj.shokuKnj
  localOneway.orX0008Oneway.createData.totalCount = rirekiObj.pagingCnt
    ? Number(rirekiObj.pagingCnt)
    : 0
  localComponents.orX0008.createId = rirekiObj.rirekiId ?? '0'

  //orX0009 作成者
  localOneway.orX0009Oneway.createData = {
    createDate: rirekiObj.createYmd,
    createId: Number(rirekiObj.rirekiId),
    currentIndex: Number(rirekiObj.pagingNo),
    staffId: Number(rirekiObj.shokuId),
    staffName: rirekiObj.shokuKnj,
    totalCount: Number(rirekiObj.pagingCnt),
  }

  // 作成者
  localComponents.orX0009.staffId = rirekiObj.shokuId
  localComponents.orX0009.staffName = rirekiObj.shokuKnj

  //orX0010 作成日
  localComponents.orX0010.value = rirekiObj.createYmd
  // or15843 ケース番号設定
  localComponents.or15843.value = rirekiObj.caseNo
  //or31915 初回作成日
  localComponents.or31915.value = rirekiObj.shokaiYmd

  // 下記の項目を初期化する。
  setCommonPlanPeriod(kikanObj)
  local.or31308.rirekiId = rirekiObj.rirekiId

  // 表示用「支援内容」情報
  let dataList = [] as GamenDataInfo[]
  // 「支援内容」リスト
  if (local.or31308.dataList?.length > 0) {
    dataList = local.or31308.dataList.map((item, index) => ({
      id: item.id,
      bangou: { value: item.bangou },
      svNaiyoKnj: { value: item.svNaiyoKnj },
      svShuKnj: { value: item.svShuKnj },
      jigyoNameKnj: { value: item.jigyoNameKnj },
      kikanKnj: { value: item.kikanKnj },
      modifiedCnt: item.modifiedCnt,
      seq: index + 1,
      updateKbn: UPDATE_KBN.NONE,
    }))
  }
  localComponents.orX0107 = {
    // 履歴の関連情報
    rirekiOtherData: {
      kadaiUndoKnj: local.or31308.dataHeadObj.kadaiUndoKnj,
      kadaiNichijoKnj: local.or31308.dataHeadObj.kadaiNichijoKnj,
      kadaiShakaiKnj: local.or31308.dataHeadObj.kadaiShakaiKnj,
      kadaiKenkoKnj: local.or31308.dataHeadObj.kadaiKenkoKnj,
      mokuhyoKnj: local.or31308.dataHeadObj.mokuhyoKnj,
      shienKeikakuKnj: local.or31308.dataHeadObj.shienKeikakuKnj,
      ryuiKnj: local.or31308.dataHeadObj.ryuiKnj,
      shogaiKasanFlg: local.or31308.dataHeadObj.shogaiKasanFlg,
      kaiteiFlg: rirekiObj.kaiteiFlg,
    },
    // 表示用「支援内容」リスト
    dataList: dataList,
  } as OrX0107Type
}

/**
 * 新規作成時処理
 */
async function _create() {
  // 操作区分が3:削除の場合、処理終了
  if (local.or31308.operaFlg === Or31308Const.OPERA_FLG_3) {
    return
  }

  // 期間管理フラグが1:期間管理する、かつ、表示用「計画対象期間」情報.ページング区分が0:なしの場合、メッセージを表示(i.cmn.11300)
  if (
    local.or31308.kikanFlg === Or31308Const.KIKAN_1 &&
    local.or31308.kikanObj.pagingFlg === Or31308Const.PAGING_0
  ) {
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11300'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // GUI00070 対象期間画面をポップアップで起動する。
    if (orX0007Ref.value?.onClickDialog) {
      await orX0007Ref.value.onClickDialog()
    }
    return
  } else if (!local.or31308.rirekiId || local.or31308.rirekiId === '0') {
    // 共通情報.履歴IDが空白または0の場合 i.cmn.11265
    await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.implementation-plan3')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  } else {
    //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
    if (
      !(await checkEditBySave())
    ) {
      return
    }
    //共通情報.履歴ID ＝ 0
    local.or31308.rirekiId = '0'
    //操作区分 ＝ 1:新規
    local.or31308.operaFlg = Or31308Const.OPERA_FLG_1
    // 画面項目を再検索する
    await getImplementationPlan3Data()
  }
}

/**
 * 複写ボタン
 */
function onCreateCopy() {
  // 下記の条件がいずれか満たす場合、
  //・操作区分 = 3:削除の場合
  //・表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    local.or31308.operaFlg === Or31308Const.OPERA_FLG_3 ||
    local.or31308.kikanObj.pagingFlg === Or31308Const.PAGING_0
  ) {
    return
  }

  // GUI00965 実施計画～③複写画面をポップアップで起動する
  localOneway.or31484Oneway = {
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList as string[],
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    syubetuId: local.or31308.syubetuId ?? '',
    kikanFlg: local.or31308.kikanFlg ?? '',
    pkaiteiFlg: local.or31308.initMasterObj?.pkaiteiFlg ?? '',
    operaFlg: Or31308Const.OPERA_FLG_2,
    pagingFlg: local.or31308.kikanObj.pagingFlg ?? '',
    kadaiTorikomiFlg: local.or31308.kadaiTorikomiFlg,
    initMasterObj: local.or31308.initMasterObj,
  }
  Or31484Logic.state.set({
    uniqueCpId: or31484_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * GUI00965 実施計画～③複写返却情報がある場合
 *
 * @param newValue - ポップアップ返却情報
 */
async function handleOr31484Confirm(newValue: Or31484Type) {
  if (newValue?.rirekiId) {
    // ポップアップ画面での返回値がある場合、複写情報を取得する。
    const ret: ImplementationPlan3CopyReturnSelectOutEntity = await ScreenRepository.select(
      'implementationPlan3CopyReturnSelect',
      { rirekiId: newValue.rirekiId } as ImplementationPlan3CopyReturnSelectInEntity
    )

    // 複写用「実施計画③履歴」情報がない場合、処理終了
    if (!ret.data.rirekiObj) {
      return
    }
    // API戻り値
    const retData = ret.data
    //複写用情報を本画面に設定する。
    local.or31308.rirekiObj.kaiteiFlg = retData.rirekiObj.kaiteiFlg

    // 複写用情報を本画面に設定する
    // 表示用「総合的課題・目標・支援計画」情報
    const rirekiOtherData = {
      // 総合的課題（運動・移動）
      kadaiUndoKnj: retData.rirekiObj.kadaiUndoKnj,
      // 総合的課題（日常生活）
      kadaiNichijoKnj: retData.rirekiObj.kadaiNichijoKnj,
      // 総合的課題（社会参加対人関係）
      kadaiShakaiKnj: retData.rirekiObj.kadaiShakaiKnj,
      // 総合的課題（健康管理）
      kadaiKenkoKnj: retData.rirekiObj.kadaiKenkoKnj,
      // 目標
      mokuhyoKnj: retData.rirekiObj.mokuhyoKnj,
      // 支援計画
      shienKeikakuKnj: retData.rirekiObj.shienKeikakuKnj,
      // 留意点
      ryuiKnj: retData.rirekiObj.ryuiKnj,
      // 障害加算フラグ
      shogaiKasanFlg: retData.rirekiObj.shogaiKasanFlg ?? '',
      kaiteiFlg: local.or31308.rirekiObj.kaiteiFlg,
    } as GamenRirekiOtherInfo

    // 「支援内容」リスト
    const copyDataArr = retData.dataList.map((item, index) => {
      return {
        id: String(-index),
        bangou: { value: item.bangou },
        svNaiyoKnj: { value: item.svNaiyoKnj },
        svShuKnj: { value: item.svShuKnj },
        jigyoNameKnj: { value: item.jigyoNameKnj },
        kikanKnj: { value: item.kikanKnj },
        modifiedCnt: '',
        seq: index + 1,
        updateKbn: UPDATE_KBN.CREATE,
      } as GamenDataInfo
    }) ?? []

    orX0107Ref.value?.updateCopyValue(copyDataArr, rirekiOtherData)
  }
}

  /**
   * 保存時処理(checkEdit含めて保存処理)
   * False: 処理中止（取消、いいえ）と保存権限がない時の取消操作、
   * True：処理継続（保存と保存しない）と保存権限がない時の処理継続
   */
  async function checkEditBySave(): Promise<boolean> {
    //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
    if (
      !(await gyoumuCom.checkEdit(
        isEdit.value,
        await hasRegistAuth(Or31308Const.LINK_AUTH),
        showConfirmMessageBox,
        showWarnMessageBox,
        _save
      ))
    ) {
      return false
    }
    return true
  }

  /**
   * 保存時処理
   */
  async function _save() {
    if (!(await or15843Ref.value?.isValid()) || !(await orX0107Ref.value?.isValid())) {
      return false
    }
    //保存処理を行う。
    await gyoumuCom.doComLogicSave(
      local.or31308,
      isEdit.value,
      showMessageBox,
      getImplementationPlan3Data,
      saveApiData,
      setCommonPlanPeriod
    )
    return true
  }

  /**
   * 保存処理を行う。
   *
   * @returns 画面情報の保存処理
   */
  async function saveApiData() {
    // 表示用「実施計画③履歴」情報
    const rirekiObj = local.or31308.rirekiObj
    // 表示用「計画対象期間」情報
    const kikanObj = local.or31308.kikanObj
    // 履歴更新区分の設定
    let rireki_updateKbn = ''
    // 操作区分が3:削除の場合、保存用「実施計画③履歴」情報.履歴IDが空白以外の場合
    if (local.or31308.operaFlg === Or31308Const.OPERA_FLG_3 && rirekiObj.rirekiId) {
      rireki_updateKbn = UPDATE_KBN.DELETE
    } else if (
      local.or31308.operaFlg === Or31308Const.OPERA_FLG_1 ||
      !rirekiObj.rirekiId ||
      rirekiObj.rirekiId === '0'
    ) {
      rireki_updateKbn = UPDATE_KBN.CREATE
    } else {
      rireki_updateKbn = UPDATE_KBN.UPDATE
    }

    const orX0107Data = OrX0107Logic.data.get(orX0107_1.value.uniqueCpId)
    const orX0009Data = OrX0009Logic.data.get(orX0009_1.value.uniqueCpId)
    const inputParam: ImplementationPlan3UpdateInEntity = {
      ...gyoumuCom.getGyoumuComUpdateInEntity(
        // 操作区分
        local.or31308.operaFlg,
        // 期間管理フラグ（0:期間管理しない、1:期間管理する）
        local.or31308.kikanFlg,
        // 初期情報.種別ID
        local.or31308.syubetuId,
        // 共通情報.計画期間ID
        cmnRouteCom.getPlanPeriod()?.kikanId
      ),
      rirekiId: local.or31308.rirekiId,
      kaiteiFlg: rirekiObj.kaiteiFlg,
      //履歴データ
      rirekiObj: {
        rirekiId: rirekiObj.rirekiId,
        shokuId: orX0009Data?.staffId ?? '',
        shokuKnj: orX0009Data?.staffName ?? '',
        createYmd: String(OrX0010Logic.data.get(orX0010_1.value.uniqueCpId)?.value),
        caseNo: String(Or15843Logic.data.get(or15843_1.value.uniqueCpId)?.value),
        shokaiYmd: String(Or31915Logic.data.get(or31915_1.value.uniqueCpId)?.value),
        kadaiUndoKnj: orX0107Data?.rirekiOtherData.kadaiUndoKnj ?? '',
        kadaiNichijoKnj: orX0107Data?.rirekiOtherData.kadaiNichijoKnj ?? '',
        kadaiShakaiKnj: orX0107Data?.rirekiOtherData.kadaiShakaiKnj ?? '',
        kadaiKenkoKnj: orX0107Data?.rirekiOtherData.kadaiKenkoKnj ?? '',
        mokuhyoKnj: orX0107Data?.rirekiOtherData.mokuhyoKnj ?? '',
        shienKeikakuKnj: orX0107Data?.rirekiOtherData.shienKeikakuKnj ?? '',
        ryuiKnj: orX0107Data?.rirekiOtherData.ryuiKnj ?? '',
        shogaiKasanFlg: orX0107Data?.rirekiOtherData.shogaiKasanFlg ?? '',
        kaiteiFlg: rirekiObj.kaiteiFlg,
        modifiedCnt: rirekiObj.modifiedCnt,
        sc1Id: kikanObj.sc1Id,
        updateKbn: rireki_updateKbn,
      },
      dataList:
        orX0107Data?.dataList
          .filter((x) => Number(x.id) > 0 || (Number(x.id) <= 0 && x.updateKbn !== UPDATE_KBN.DELETE))
          .map((x) => {
            return {
              /** 履歴ID */
              rirekiId: rirekiObj.rirekiId,
              /** ID */
              id: x.id,
              /** 行番号（表示用） */
              bangou: x.bangou.value,
              /** サービス内容 */
              svNaiyoKnj: x.svNaiyoKnj.value,
              /** サービス種別 */
              svShuKnj: x.svShuKnj.value,
              /** 提供事業所名 */
              jigyoNameKnj: x.jigyoNameKnj.value,
              /** 頻度及び期間 */
              kikanKnj: x.kikanKnj.value,
              /** 更新回数 */
              modifiedCnt: x.modifiedCnt,
              /** 更新区分 */
              updateKbn: x.updateKbn,
              /** 表示順 */
              seq: String(x.seq),
            } as IDataSave
          }) ?? [],
      //初期設定マスタの情報
      initMasterObj: local.or31308.initMasterObj,
    }

    //期間管理フラグが0:期間管理しない場合
    if (local.or31308.kikanFlg === Or31308Const.KIKAN_0) {
      //表示用「計画対象期間」情報.計画期間IDが空白の場合
      if (!kikanObj.sc1Id) {
        inputParam.kikanObj = {
          sc1Id: kikanObj?.sc1Id ?? '',
          startYmd: kikanObj?.startYmd ?? '',
          endYmd: kikanObj?.endYmd ?? '',
          modifiedCnt: kikanObj?.modifiedCnt ?? '',
          updateKbn: UPDATE_KBN.CREATE,
        }
      }
    }

    //画面情報の保存処理を行う。
    const ret: GyoumuComUpdateOutEntity = await ScreenRepository.update(
      'implementationPlan3Update',
      inputParam
    )

    return ret
  }

  /**
   * 「印刷設定アイコンボタン」押下の処理
   */
  async function _print() {
    if (
      !(await checkEditBySave())
    ) {
      return
    }
    // GUI00969［印刷設定］画面をポップアップで起動する
    openPrintSetting()
  }

  /**
   * GUI00969［印刷設定］画面をポップアップで起動する
   */
  function openPrintSetting() {
    // 共通情報.法人ID
    localOneway.or27557Oneway.houjinId = systemCommonsStore.getHoujinId ?? ''
    // 共通情報.施設ID
    localOneway.or27557Oneway.shisetuId = systemCommonsStore.getShisetuId ?? ''
    // 共通情報.事業所ID
    localOneway.or27557Oneway.svJigyoId = systemCommonsStore.getSvJigyoId ?? ''
    // 共通情報.担当ケアマネID
    localOneway.or27557Oneway.tantoId = '0'
    // TODO担当ケアマネ設定フラグ
    localOneway.or27557Oneway.careManagerInChargeSettingsFlag = 0
    // 共通情報.職員ID ※ログイン情報
    localOneway.or27557Oneway.shokuId = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
    // 共通情報.利用者ID ※リストに格納する
    localOneway.or27557Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
    // 共通情報.システム年月日(YYYY/MM/DD) ⇒ 処理年月日
    localOneway.or27557Oneway.appYmd = systemCommonsStore.getSystemDate ?? ''
    // 共通情報.ユーザリスト
    localOneway.or27557Oneway.userList =
      systemCommonsStore.getUserSelectUserList()?.map((x) => {
        return {
          userId: x.selfId,
          nameKnj: x.nameSei + ' ' + x.nameKanaMei,
          userNumber: x.selfId,
          sex: String(x.gender),
        } as userList
      }) ?? []
    // 共通情報.50音（※リスト） ⇒ フォーカス設定用イニシャル
    localOneway.or27557Oneway.focusSettingInitial =
      systemCommonsStore.getUserSelectFilterInitials() as string[]
    // TODO 初期設定マスタの情報
    // セクション名: "（Ⅱ）実施計画～③"
    localOneway.or27557Oneway.sectionName = Or31308Const.SECTION_NAME
    // システムコード
    localOneway.or27557Oneway.sysCd = Or31308Const.SYSTEM_CODE
    // 計画期間管理フラグ
    localOneway.or27557Oneway.kikanFlg = local.or31308.kikanFlg
    // 選択帳票番号
    localOneway.or27557Oneway.choIndex = '1'
    // システム略称
    localOneway.or27557Oneway.sysRyaku = systemCommonsStore.getSystemAbbreviation ?? ''

    // GUI00969［印刷設定］画面
    Or27557Logic.state.set({
      uniqueCpId: or27557_1.value.uniqueCpId,
      state: { isOpen: true },
    })
  }

  /**
   *
   * 削除処理
   *
   */
  async function _delete() {
    // 下記の条件がいずれか満たす場合、
    //・操作区分 = 3:削除の場合
    //・表示用「計画対象期間」情報.ページング区分が0:なしの場合
    if (
      local.or31308.operaFlg === Or31308Const.OPERA_FLG_3 ||
      local.or31308.kikanObj.pagingFlg === Or31308Const.PAGING_0
    ) {
      return
    }

    //削除確認（）
    const dialogResult = await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11326', [
        OrX0010Logic.data.get(orX0010_1.value.uniqueCpId)?.value ?? '',
        t('label.implementation-plan-three'),
      ]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    })
    if (dialogResult?.secondBtnClickFlg === true) {
      return
    }
    //・操作区分3:削除
    local.or31308.operaFlg = Or31308Const.OPERA_FLG_3

    // 表示用「支援内容」リストをクリア
    orX0107Ref.value?.deleteAllRow()

    // 画面項目の非活性・非表示
    setChildCpBinds(props.uniqueCpId, {
      // 作成者
      [OrX0009Const.CP_ID(1)]: {
        oneWayState: {
          isDisabled: true,
        },
      },
      // 作成日
      [OrX0010Const.CP_ID(1)]: {
        oneWayState: {
          isDisabled: true,
        },
      },
      // ケース番号
      [Or15843Const.CP_ID(1)]: {
        oneWayState: {
          isDisabled: true,
        },
      },
      // 初回作成日
      [Or31915Const.CP_ID(1)]: {
        oneWayState: {
          isDisabled: true,
        },
      },
      //「支援内容」
      [OrX0107Const.CP_ID(1)]: {
        oneWayState: {
          operaFlg: local.or31308.operaFlg,
        },
      },
    })
  }

  /**
   * 「マスタ他設定アイコンボタン」押下の処理
   */
  async function _master() {
    if (
      !(await checkEditBySave())
    ) {
      return
    }

    // 施設ID
    localOneway.or27294Oneway.shisetuId = systemCommonsStore.getShisetuId
      ? Number(systemCommonsStore.getShisetuId)
      : 0
    // 事業所ID
    localOneway.or27294Oneway.jigyoId = systemCommonsStore.getSvJigyoId
      ? Number(systemCommonsStore.getSvJigyoId)
      : 0
    // 適用事業所IDリスト
    localOneway.or27294Oneway.jigyoList =
      systemCommonsStore.getSvJigyoIdList?.map((x) => {
        return {
          jigyoId: x,
        } as JigyoList
      }) ?? []
    // GUI00963 実施計画③マスタ画面をポップアップで起動する
    Or27294Logic.state.set({
      uniqueCpId: or27294_1.value.uniqueCpId,
      state: { isOpen: true },
    })
  }

  /**
   *ログを押下
   */
  function onClickLog() {
    // TODO 共通処理 e-文書法対象機能の電子ファイル保存設定を取得する。
    // TODO 共通処理 e-文章法対象の帳票のXPSフォルダをエクスプローラで開く。
  }

  /**
   * 「ケース取込」押下：GUI01016 ケース一覧画面
   */
  async function onCaseImportClick() {
    // 下記の条件がいずれか満たす場合、
    //・操作区分 = 3:削除の場合
    //・表示用「計画対象期間」情報.ページング区分が0:なしの場合
    if (
      local.or31308.operaFlg === Or31308Const.OPERA_FLG_3 ||
      local.or31308.kikanObj.pagingFlg === Or31308Const.PAGING_0
    ) {
      return
    }

    // TODO 共通処理の閲覧権限チェックを行う
    if (local.or31308.kesuTorikomiFlg === Or31308Const.KESU_TORIKOMI_0) {
      await openInfoDialog({
        // ダイアログタイトル
        dialogTitle: t('label.top-btn-title'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10423'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.ok'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      return
    }

    // 選択されている項目がケース取込対象外の場合
    const orX0107Data = OrX0107Logic.data.get(orX0107_1.value.uniqueCpId)
    if (!orX0107Data?.selectItemName) {
      await openInfoDialog({
        // ダイアログタイトル
        dialogTitle: t('label.top-btn-title'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10427'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.ok'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      return
    }
    // ケース取込対象が画面.支援内容一覧ですが、画面.支援内容一覧に1件データもない場合
    if (orX0107Data?.dataList.length === 0) {
      await openInfoDialog({
        // ダイアログタイトル
        dialogTitle: t('label.top-btn-title'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10427_1'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.ok'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      return
    }

    // 共通情報.法人ID
    localOneway.or28256Oneway.houjinId = systemCommonsStore.getHoujinId ?? ''
    // 共通情報.施設ID
    localOneway.or28256Oneway.sisetsuId = systemCommonsStore.getShisetuId ?? ''
    // 共通情報.システムコード
    localOneway.or28256Oneway.syscd = Or31308Const.SYSTEM_CODE
    // 処理期間開始日:共通情報.システム年月日(YYYY/MM/DD)
    localOneway.or28256Oneway.kaisihi = {
      value: systemCommonsStore.getSystemDate ?? '',
    }
    // 処理期間終了日:共通情報.システム年月日(YYYY/MM/DD)
    localOneway.or28256Oneway.syuuryouhi = {
      value: systemCommonsStore.getSystemDate ?? '',
    }
    // 対象項目ラベル:上記のテキストタイトル
    localOneway.or28256Oneway.targetItem = orX0107Data?.selectItemName ?? ''
    // 対象項目値:上記の①～⑫がケース取込対象の入力値
    localOneway.or28256Oneway.caseInformation = orX0107Data?.selectItemValue ?? ''
    // 共通情報.利用者ID
    localOneway.or28256Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? '0'
    // システム年月日:共通情報.システム年月日(YYYY/MM/DD)
    localOneway.or28256Oneway.systemYmd = {
      value: systemCommonsStore.getSystemDate ?? '',
    }
    // 共通情報.適用事業所ID
    localOneway.or28256Oneway.defSvJigyoId = systemCommonsStore.getSvJigyoId ?? ''

    Or28256Logic.state.set({
      uniqueCpId: or28256_1.value.uniqueCpId,
      state: {
        isOpen: true,
        items: [],
      },
    })
  }

  /**
   * エラーダイアログをオープンする
   *
   * @param state - エラーダイアログOneWayBind領域用の構造
   *
   * @returns - 確認ダイアログを閉じたタイミングで結果を返却
   */
  function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
    // 確認ダイアログを開く
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        ...state,

        isOpen: true,
      },
    })

    // 確認ダイアログを閉じたタイミングで結果を返却
    return new Promise((resolve) => {
      watch(
        () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
        () => {
          const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
          // 確認ダイアログのフラグをOFF
          Or21814Logic.event.set({
            uniqueCpId: or21814_1.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              thirdBtnClickFlg: false,
            },
          })

          resolve(event)
        },
        { once: true }
      )
    })
  }

  /**
   *ナビゲーション制御領域のいずれかの編集フラグがON
   */
const isEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})

/**
 * 作成情報表示制御
 * 期間管理フラグが1:期間管理する、かつ、表示用「計画対象期間」情報.ページング区分が0:なしの場合、非表示
 * 以外の場合、表示
 */
const creatShowFlg = computed(() => {
  if (
    local.or31308.kikanFlg === Or31308Const.KIKAN_1 &&
    local.or31308.kikanObj.pagingFlg === Or31308Const.PAGING_0
  ) {
    return false
  }
  return true
})

</script>

<template>
  <c-v-sheet class="view">
    <!-- Or11871：有機体：画面メニューエリア -->
    <div class="sticky-header">
      <g-base-or11871 v-bind="or11871">
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            @click="onCreateCopy()"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-copy-btn')"
            />
          </c-v-list-item>
        </template>
        <template v-if="local.or31308.kesuTorikomiFlg !== Or31308Const.KESU_TORIKOMI_0" #customButtons>
          <!--「ケース取込」-->
          <base-at-button
              class="action-button"
              @click="onCaseImportClick()"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.care-plan2-case-import-icon-btn')"
              ></c-v-tooltip>
              {{ t('btn.case-import') }}
          </base-at-button
            >
        </template>
        <template #optionMenuItems>
          <!--「ログ」-->
          <c-v-list-item
            v-show="systemCommonsStore.getEBunshoKbn === Or31308Const.E_BUNSHO_KBN_1"
            :title="t('btn.log')"
            prepend-icon="open_in_browser"
            @click="onClickLog()"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-show-log-btn')"
            />
          </c-v-list-item>
        </template>
      </g-base-or11871>
    </div>
    <c-v-row
      no-gutters
      class="content-area pl-6 pt-4"
    >
      <!-- （利用者基本）利用者選択の表示 -->
      <c-v-col
        cols="2"
        class="hidden-scroll h-100"
      >
        <g-base-or00248 v-bind="or00248_1" />
      </c-v-col>
      <c-v-col class="hidden-scroll h-100" style="padding-bottom:52px;">
        <div class="d-flex flex-column h-100">
          <div class="pl-6">
          <!-- コンテンツエリア -->
          <c-v-row
            no-gutters
            class="view-row"
          >
            <c-v-col cols="auto">
              <!-- 事業所 -->
              <g-base-or-41179 v-bind="or41179_1" width="203"/>
            </c-v-col>
            <!-- 計画対象期間 -->
            <c-v-col
              v-show="local.or31308.kikanFlg === Or31308Const.KIKAN_1"
              cols="auto"
            >
              <g-custom-orX0007
                ref="orX0007Ref"
                v-bind="orX0007_1"
                :oneway-model-value="localOneway.orX0007Oneway"
                :unique-cp-id="orX0007_1.uniqueCpId"
                :parent-method="checkEditBySave"
              />
            </c-v-col>
            <c-v-col
              v-show="creatShowFlg"
              cols="auto"
            >
              <!-- 作成日 -->
              <g-custom-orX0010
                v-bind="orX0010_1"
                :oneway-model-value="localOneway.orX0010Oneway"
                :unique-cp-id="orX0010_1.uniqueCpId"
              />
            </c-v-col>
            <c-v-col
              v-show="creatShowFlg"
              cols="auto"
            >
              <!-- 作成者 -->
              <g-custom-orX0009
                v-bind="orX0009_1"
                :oneway-model-value="localOneway.orX0009Oneway"
                :unique-cp-id="orX0009_1.uniqueCpId"
              />
            </c-v-col>
            <c-v-col
              v-show="creatShowFlg"
              cols="auto"
            >
              <!-- 履歴 -->
              <g-custom-orX0008
                v-bind="orX0008_1"
                :oneway-model-value="localOneway.orX0008Oneway"
                :unique-cp-id="orX0008_1.uniqueCpId"
                :parent-method="checkEditBySave"
              />
            </c-v-col>
            <c-v-col
              v-show="creatShowFlg"
              cols="auto"
            >
              <!-- ケース番号 -->
              <g-custom-or15843
                ref="or15843Ref"
                v-bind="or15843_1"
                :oneway-model-value="localOneway.or15843Oneway"
                :unique-cp-id="or15843_1.uniqueCpId"
                />
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-show="creatShowFlg"
            no-gutters
            class="view-row pt-4"
          >
            <!-- 初回作成日 -->
              <g-custom-or31915
                v-bind="or31915_1"
                :oneway-model-value="localOneway.or31915Oneway"
                :unique-cp-id="or31915_1.uniqueCpId"
              />
          </c-v-row>
          </div>
          <div class="py-6">
            <c-v-divider class="divider-class" />
          </div>
          <g-custom-or-x-0107
            ref="orX0107Ref"
            v-bind="orX0107_1"
            :unique-cp-id="orX0107_1.uniqueCpId"
          />
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>

  <!-- データ変更確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or21815 v-bind="or21815_1" />

  <!-- GUI00963 実施計画③マスタ画面 -->
  <g-custom-or-27294
    v-if="showDialogOr27294"
    v-bind="or27294_1"
    v-model="local.or27294"
    :oneway-model-value="localOneway.or27294Oneway"
    :unique-cp-id="or27294_1.uniqueCpId"
  />

  <!-- GUI01016 ケース一覧画面-->
  <g-custom-or-28256
    v-if="showDialogOr28256"
    v-bind="or28256_1"
    :model-value="local.or28256"
    :oneway-model-value="localOneway.or28256Oneway"
    @update:model-value="doCaseImport"
  />

  <!--GUI00965 実施計画～③複写画面-->
  <g-custom-or-31484
    v-if="showDialogOr31484"
    v-bind="or31484_1"
    :oneway-model-value="localOneway.or31484Oneway"
    :unique-cp-id="or31484_1.uniqueCpId"
    @update:model-value="handleOr31484Confirm"
  />
  <!-- GUI00969_印刷設定画面 -->
  <g-custom-or-27557
    v-if="showDialogOr27557"
    v-bind="or27557_1"
    :oneway-model-value="localOneway.or27557Oneway"
    :unique-cp-id="or27557_1.uniqueCpId"
  />
</template>

<style scoped lang="scss">
$margin-organism: 24px; // オーガニズム間のマージン
$margin-common: 48px; // 一般的なマージン
.view {
  display: flex;
  flex-direction: column;
  // 背景色：テーマの背景色を使用
  background-color: rgb(var(--v-theme-background)) !important;
  // 幅：内容で広く
  min-height: max-content !important;
  // 画面トップの固定ヘッダー
  .sticky-header {
    position: sticky; // スクロール時に固定
    top: $margin-common; // トップからの距離
    z-index: 999; // 他の要素よりも上に表示
    background: rgb(var(--v-theme-background)); // テーマの背景色
    width: 100%; // 全幅
  }
}

.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}

.view-row {
  align-items: baseline;
  gap: 24px;

  :deep(.v-sheet) {
    background-color: transparent !important;
  }
}

.divider-class {
  border-width: thin;
}

.v-btn {
  color: rgb(118, 118, 118) !important;
  background-color: transparent !important;
  border: 1px solid rgb(118, 118, 118) !important;
}
.action-button {
  background-color: #fff !important;
  width: 92px !important;
  padding: 0px !important;
  min-width: auto !important;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
:deep(.v-input__control) {
  background-color: #fff;
}
</style>
