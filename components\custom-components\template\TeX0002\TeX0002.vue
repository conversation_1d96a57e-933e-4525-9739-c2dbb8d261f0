<script setup lang="ts">
/**
 * TeX0002:［アセスメント］画面（居宅）テンプレート
 *
 * @description
 * ［アセスメント］画面（居宅）テンプレート、以下のコンポーネントをタブで表示する。
 * GUI00794_［アセスメント］画面（居宅）（1）
 * GUI00795_［アセスメント］画面（居宅）（2）
 * GUI00796_［アセスメント］画面（居宅）（3）
 * GUI00797_［アセスメント］画面（居宅）（4）
 * GUI00798_［アセスメント］画面（居宅）（5）
 * GUI00799_［アセスメント］画面（居宅）（6①）
 * GUI00800_［アセスメント］画面（居宅）（6②）
 * GUI00801_［アセスメント］画面（居宅）（6③-④）
 * GUI00802_［アセスメント］画面（居宅）（6⑤）
 * GUI00803_［アセスメント］画面（居宅）（6⑥）
 * GUI00804_［アセスメント］画面（居宅）（6医）
 * GUI00805_［アセスメント］画面（居宅）（7まとめ）
 * GUI00806_［アセスメント］画面（居宅）（7スケジュール）
 *
 * <AUTHOR>
 */

import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { Or31535Const } from '../../organisms/Or31535/Or31535.constants'
import { Or29241Const } from '../../organisms/Or29241/Or29241.constants'
import { Or29242Const } from '../../organisms/Or29242/Or29242.constants'
import { OrD2002Const } from '../../organisms/OrD2002/OrD2002.constants'
import { Or28992Const } from '../../organisms/Or28992/Or28992.constants'
import { Or11131Const } from '../../organisms/Or11131/Or11131.constants'
import { Or00386Const } from '../../organisms/Or00386/Or00386.constants'
import { Or01997Const } from '../../organisms/Or01997/Or01997.constants'
import { Or13850Const } from '../../organisms/Or13850/Or13850.constants'
import { Or13844Const } from '../../organisms/Or13844/Or13844.constants'
import { Or13844Logic } from '../../organisms/Or13844/Or13844.logic'
import { Or13872Logic } from '../../organisms/Or13872/Or13872.logic'
import { Or13872Const } from '../../organisms/Or13872/Or13872.constants'
import { Or33097Const } from '../../organisms/Or33097/Or33097.constants'
import { Or30732Const } from '../../organisms/Or30732/Or30732.constants'
import { Or29958Const } from '../../organisms/Or29958/Or29958.constants'
import { Or30149Const } from '../../organisms/Or30149/Or30149.constants'
import type { Or30149OnewayType } from '../../organisms/Or30149/Or30149.type'
import { Or32081Const } from '../../organisms/Or32081/Or32081.constants'
import { Or32643Const } from '../../organisms/Or32643/Or32643.constants'
import { OrX0001Const } from '../../organisms/OrX0001/OrX0001.constants'
import { OrX0001Logic } from '../../organisms/OrX0001/OrX0001.logic'
import { Or27562Logic } from '../../organisms/Or27562/Or27562.logic'
import { Or27562Const } from '../../organisms/Or27562/Or27562.constants'
import { OrX0009Const } from '../../organisms/OrX0009/OrX0009.constants'
import { Or35672Logic } from '../../organisms/Or35672/Or35672.logic'
import { Or35672Const } from '../../organisms/Or35672/Or35672.constants'
import { OrX0115Logic } from '../../organisms/OrX0115/OrX0115.logic'
import { OrX0115Const } from '../../organisms/OrX0115/OrX0115.constants'
import { Or28326Logic } from '../../organisms/Or28326/Or28326.logic'
import { Or28326Const } from '../../organisms/Or28326/Or28326.constants'
import { Or26257Logic } from '../../organisms/Or26257/Or26257.logic'
import { Or26257Const } from '../../organisms/Or26257/Or26257.constants'
import { Or57082Const } from '../../organisms/Or57082/Or57082.constants'
import { Or57082Logic } from '../../organisms/Or57082/Or57082.logic'
import type { Or57082Param } from '../../organisms/Or57082/Or57082.type'
import { TeX0002Logic } from './TeX0002.logic'
import type { TeX0002StateType } from './TeX0002.type'
import { TeX0002Const } from './TeX0002.constants'
import {
  useCmnRouteCom,
  useJigyoList,
  useNuxtApp,
  useScreenInitFlg,
  useScreenOneWayBind,
  useScreenStore,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Or13844OnewayType } from '~/types/cmn/business/components/Or13844Type'
import type { Or13872OnewayType } from '~/types/cmn/business/components/Or13872Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00028OnewayType, Mo00028Type } from '~/types/business/components/Mo00028Type'
import type { Or28992OnewayType } from '~/types/cmn/business/components/Or28992Type'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import type { TeX0002OnewayType, TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
import type {} from '~/repositories/cmn/entities/CpnTucGdlComInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or33097OnewayType } from '~/types/cmn/business/components/Or33097Type'
import type { Or29241OnewayType } from '~/types/cmn/business/components/Or29241Type'
import type { Or29242OnewayType } from '~/types/cmn/business/components/Or29242Type'
import type { Or00386OnewayType } from '~/types/cmn/business/components/Or00386Type'
import type { Or32081OnewayType } from '~/types/cmn/business/components/Or32081Type'
import type { Or31535OnewayType } from '~/types/cmn/business/components/Or31535Type'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import type {
  AssessmentHomePlanningPeriodChangeSelectInEntity,
  AssessmentHomePlanningPeriodChangeSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentHomePlanningPeriodChangeSelectEntity'
import type {
  AssessmentHomeHistoryChangeSelectInEntity,
  AssessmentHomeHistoryChangeSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentHomeHistoryChangeSelectEntity'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import type { OrX0001OnewayType, OrX0001Type } from '~/types/cmn/business/components/OrX0001Type'
import type {
  AssessmentHomeCommonInfoSelectInEntity,
  AssessmentHomeCommonInfoSelectOutEntity,
} from '~/repositories/cmn/entities/assessmentHomeCommonInfoSelectEntity'
import type { Mo01280Type } from '~/types/business/components/Mo01280Type'
import type { Or32643OnewayType } from '~/types/cmn/business/components/Or32643Type'
import type { Or11131OnewayType } from '~/types/cmn/business/components/Or11131Type'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or27562OnewayType } from '~/types/cmn/business/components/Or27562Type'
import type { Or29958OnewayType } from '~/types/cmn/business/components/Or29958Type'
import type { Or35672OnewayType } from '~/types/cmn/business/components/Or35672Type'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Or28326OnewayType, Or28326Type } from '~/types/cmn/business/components/Or28326Type'
import type { OrX0157OnewayType, OrX0157Type } from '~/types/cmn/business/components/OrX0157Type'
import type {
  Or26257OnewayType,
  Or26257Type,
  Shokuin,
} from '~/types/cmn/business/components/Or26257Type'
import type {
  KadaiMokuhyoInfoSelectInEntity,
  KadaiMokuhyoInfoSelectOutEntity,
} from '~/repositories/cmn/entities/KadaiMokuhyoInfoSelectEntity'
import type { OrX0163Type } from '~/types/cmn/business/components/OrX0163Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type {
  IssuesAndGoalListItem,
  OrX0209OnewayType,
  OrX0209Type,
} from '~/types/cmn/business/components/OrX0209Type'
import { OrX0209Const } from '../../organisms/OrX0209/OrX0209.constants'

const $log = useNuxtApp().$log as DebugLogPluginInterface

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: TeX0002OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// route共有情報
const cmnRouteCom = useCmnRouteCom()

const { getChildCpBinds } = useScreenUtils()

// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()

// 事業所変更監視
const { jigyoListWatch } = useJigyoList()

const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or35672 = ref({ uniqueCpId: '' }) // GUI00807_アセスメント複写
const or27562 = ref({ uniqueCpId: '' }) // GUI00626_アセスメントマスタ
const or41179 = ref({ uniqueCpId: '' }) // 事業所選択
const orX0115 = ref({ uniqueCpId: '' }) // 計画対象期間選択
const or28326 = ref({ uniqueCpId: '' }) // 履歴選択
const orX0009 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' }) // エラーダイアログ(ok)
const or21814_1 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes, no, cancel)
const or21814_2 = ref({ uniqueCpId: '' }) // 確認ダイアログ(ok)
const or21814_3 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes,no)
const or31535 = ref({ uniqueCpId: Or31535Const.CP_ID(0), parentUniqueCpId: props.uniqueCpId })
const or29241 = ref({ uniqueCpId: Or29241Const.CP_ID(0), parentUniqueCpId: props.uniqueCpId })
const or29242 = ref({ uniqueCpId: Or29242Const.CP_ID(0), parentUniqueCpId: props.uniqueCpId })
const or00386 = ref({ uniqueCpId: Or00386Const.CP_ID(0), parentUniqueCpId: props.uniqueCpId })
const orD2002 = ref({ uniqueCpId: '' })
const or28992 = ref({
  uniqueCpId: '',
  parentUniqueCpId: props.uniqueCpId,
})
const or11131 = ref({
  uniqueCpId: Or11131Const.CP_ID(0),
  parentUniqueCpId: props.uniqueCpId,
})
const or13850 = ref({ uniqueCpId: '' })
const or13844 = ref({ uniqueCpId: '' })
const or13872 = ref({ uniqueCpId: '' })
const or30732 = ref({
  uniqueCpId: Or30732Const.CP_ID(0),
  parentUniqueCpId: props.uniqueCpId,
})
const or29958 = ref({ uniqueCpId: Or29958Const.CP_ID(0), parentUniqueCpId: props.uniqueCpId })
const or32081 = ref({ uniqueCpId: Or32081Const.CP_ID(0), parentUniqueCpId: props.uniqueCpId })
const or33097 = ref({ uniqueCpId: Or33097Const.CP_ID(0), parentUniqueCpId: props.uniqueCpId })
const or32643 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or01997 = ref({ uniqueCpId: Or01997Const.CP_ID(0), parentUniqueCpId: props.uniqueCpId })
const orX0001 = ref({ uniqueCpId: '' })
const or30149 = ref({ uniqueCpId: Or30149Const.CP_ID(0), parentUniqueCpId: props.uniqueCpId })
const or26257 = ref({ uniqueCpId: '' })
const or57082 = ref({ uniqueCpId: '' })

// 画面上部変更フラグ
const isTopEdit = ref(false)

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  let editFlg = false
  switch (local.mo00043.id) {
    case Or30732Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or30732.value.uniqueCpId)
      break
    case Or31535Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or31535.value.uniqueCpId)
      break
    case Or32643Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or32643.value.uniqueCpId)
      break
    case Or00386Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or00386.value.uniqueCpId)
      break
    case Or29958Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or29958.value.uniqueCpId)
      break
    case Or28992Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or28992.value.uniqueCpId)
      break
    case Or29241Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or29241.value.uniqueCpId)
      break
    case Or29242Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or29242.value.uniqueCpId)
      break
    case Or33097Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or33097.value.uniqueCpId)
      break
    case Or30149Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or30149.value.uniqueCpId)
      break
    case Or32081Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or32081.value.uniqueCpId)
      break
    case Or01997Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or01997.value.uniqueCpId)
      break
    case Or11131Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(or11131.value.uniqueCpId)
      break
  }

  return editFlg
})

// ローカルTwoway
const local = reactive({
  // 作成日
  createDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // 作成者選択
  or26257: {
    shokuin: {
      chkShokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
    } as Shokuin,
  } as Or26257Type,
  // タブ
  mo00043: {
    id: '',
  } as Mo00043Type,
  mo00028: {
    panel: ref(1),
  } as Mo00028Type,
  // 履歴選択ダイアログ
  or28326: {
    ks21Id: '',
  } as Or28326Type,
  // 作成者選択
  orX0157: {
    value: '',
  } as OrX0157Type,
  // 課題と目標リスト
  issuesAndGoalsList: {
    items: [] as IssuesAndGoalListItem[],
  } as OrX0209Type,
  // 削除ダイアログ
  orX0001Type: {
    deleteSyubetsu: '',
  } as OrX0001Type,
})

// ローカルOneway
const localOneway = reactive({
  // アセスメント複写
  or35672Oneway: {
    tabItems: [],
    periodManagementFlg: '',
  } as Or35672OnewayType,
  // アセスメントマスタ
  or27562Oneway: {
    /** 事業者ID */
    svJigyoId: '',
    /** 分類1 */
    bunrui1Id: '2',
    /** 分類2 */
    bunrui2Id: '12',
    /** 施設ID */
    shisetuId: '',
  } as Or27562OnewayType,
  // 計画対象期間フラグ
  plainningPeriodManageFlg: false,
  // 計画対象期間選択
  planningPeriodSelectOneway: {
    plainningPeriodManageFlg: '',
    planningPeriodList: [],
    pageBtnAutoDisabled: true,
    showLabelMode: false,
  } as Or13844OnewayType,
  // 事業所選択画面
  orX0115Oneway: {
    kindId: '',
    sc1Id: '',
  } as OrX0115OnewayType,
  // 履歴選択
  hisotrySelectOneway: {
    /** 履歴情報 */
    historyInfo: {
      /** アセスメントID */
      gdlId: '',
      /** 履歴番号 */
      krirekiNo: '',
      /** 履歴総件数 */
      krirekiCnt: '',
    },
    pageBtnAutoDisabled: true,
    showLabelMode: false,
  } as Or13872OnewayType,
  // 履歴選択ダイアログ
  or28326Oneway: {
    /**事業者ID*/
    svJigyoId: '',
    /**計画期間ID*/
    sc1Id: '',
    /**利用者ID*/
    userId: '',
    /**履歴ID*/
    ks21Id: '',
  } as Or28326OnewayType,
  // 作成者選択
  orX0157Oneway: {
    inputMode: 'TextOnly',
    showEditBtnFlg: true,
    inputReadonly: true,
    text: {
      orX0157InputOneway: {
        disabled: false,
        isVerticalLabel: true,
        showItemLabel: true,
        itemLabel: t('label.author'),
        customClass: new CustomClass({
          outerClass: 'background-transparent',
          labelClass: 'mb-1',
        }),
        width: '160px',
      },
    },
  } as OrX0157OnewayType,
  // 作成者履歴選択
  or26257Oneway: {
    // システム略称
    sysCdKbn: systemCommonsStore.getSystemAbbreviation,
    // アカウント設定
    secAccountAllFlg: '-',
    // 適用事業所ＩＤリスト
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList as [],
    // 職員ID
    shokuinId: '',
    // システムコード
    gsysCd: '',
    // モード
    selectMode: TeX0002Const.DEFAULT.SELECT_MODE_12,
    // 基準日
    kijunYmd: systemCommonsStore.getSystemDate,
    // 事業所ID
    defSvJigyoId: '',
    // フィルターフラグ
    filterDwFlg: TeX0002Const.DEFAULT.AUTHOR_SELECT_FILTER_FLG_1,
    // 雇用状態
    koyouState: '-',
    // 地域フラグ
    areaFlg: '-',
    // 表示名称リスト
    hyoujiColumnList: [],
    // 未設定フラグ
    misetteiFlg: TeX0002Const.DEFAULT.MISETTEI_FLG_OK,
    // 他職員参照権限
    otherRead: '',
    // 中止フラグ
    refStopFlg: '-',
    // 処理フラグ
    syoriFlg: '',
    // メニュー１ID
    menu1Id: '',
    // 件数フラグ
    kensuFlg: '-',
    // 職員IDリスト
    shokuinIdList: [],
  } as Or26257OnewayType,
  // 作成日
  createDateOneway: {
    itemLabel: t('label.create-date'),
    isRequired: true,
    isVerticalLabel: true,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass',
      labelClass: 'mb-1',
      itemStyle: 'background: rgb(var(--v-theme-surface)) !important;',
    }),
    width: '140',
  } as Mo00020OnewayType,
  // タブ
  mo00043OnewayType: {
    tabItems: [],
  } as Mo00043OnewayType,
  // 解決すべき課題と目標
  mo00028OnewayType: {
    panelTitle: t('label.should-solution-issues-and-goal'),
    customClass: new CustomClass({
      itemClass: 'bold',
    }),
  } as Mo00028OnewayType,
  or28992Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_NORMAL,
  } as Or28992OnewayType,
  or11131Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_NORMAL,
  } as Or11131OnewayType,
  or32081Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_NORMAL,
  } as Or32081OnewayType,
  or32643Oneway: {
    assessmentHome: {
      title: '',
    },
  } as Or32643OnewayType,
  or33097Oneway: {
    assessmentHome: {
      title: '',
    },
  } as Or33097OnewayType,
  or29241Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_NORMAL,
  } as Or29241OnewayType,
  or29242Oneway: {
    assessmentHome: {
      title: '',
    },
  } as Or29242OnewayType,
  or00386Oneway: {
    assessmentHome: {
      title: '',
    },
  } as Or00386OnewayType,
  or31535Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_NORMAL,
  } as Or31535OnewayType,
  or29958Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_NORMAL,
  } as Or29958OnewayType,
  or30149Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_NORMAL,
  } as Or30149OnewayType,
  or30732Oneway: {
    mode: TeX0002Const.DEFAULT.MODE_NORMAL,
  } as Or28992OnewayType,
  issuesAndGoalsList: {
    assessmentName: '',
  } as OrX0209OnewayType,
  // 削除ダイアログ
  orX0001Oneway: {
    createYmd: '',
    kinouKnj: '',
    selectTabName: '',
    startTabName: '',
    endTabName: '',
  } as OrX0001OnewayType,
})

// 権限フラグ
const permission = ref(
  {} as {
    register: boolean
    print: boolean
  }
)

// 各種フラグ
const localFlg = reactive(
  {} as {
    visioFlg: string
    summaryShow: string
    overrallSummary: string
  }
)

// 画面情報の取得中かどうか
let isLoading = false

// 画面初期化完了フラグ
let isInitCompleted = false

// 選択中利用者ID
const userId = ref('')

// 変更タブIDを保持
const tabIdBk = ref('')

// 期間管理フラグ
const plannningPeriodManageFlg = ref(TeX0002Const.DEFAULT.PLANNING_PERIOD_NO_MANAGE)

// 改定フラグ
const ninteiFormF = ref<string | undefined>('')

// 履歴更新回数
const historyModifiedCnt = ref('0')

// 履歴表示フラグ
const isHistoryShow = ref(true)

// 画面情報領域のsupplement内のisInitの取得およびonMountedにfalseへ更新する処理を登録
const isInit = useScreenInitFlg()

// 複写コンポネントキー
const copyComponentKey = ref('')

// 削除区分
const deleteKbn = ref(TeX0002Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL)

// イベント情報（各アクション保存用）
const eventInfo = ref({
  kbn: TeX0002Const.DEFAULT.EVENT_KBN_NORMAL,
  pageFlg: '',
} as {
  kbn: string
  pageFlg: string
})

// オプションメニュー状態
const optionMenuState = ref({
  copyBtnDisabled: false,
  deleteBtnDisabled: false,
} as {
  copyBtnDisabled: boolean
  deleteBtnDisabled: boolean
})

// 事業所情報バックアップ
const jigyoInfo = ref({
  svJigyoId: '',
  svjigyoName: '',
  shisetuId: '',
  houjinId: '',
} as {
  svJigyoId: string
  svjigyoName: string
  shisetuId: string
  houjinId: string
})

// 種別ID
const syubetuId = ref('')

// 履歴IDバックアップ
const gdlIdOld = ref('')

// 入力作成日バックアップ
const createDateOld = ref('')

// 作成者情報
const authorInfo = ref(
  {} as {
    id?: string
    name?: string
  }
)

// 複写元情報
const copyFInfo = ref(
  {} as {
    userId: string
    createdYmd: string
    ninteiFormF: string
    sc1Id: string
    gdlId: string
  }
)

// 更新区分
const historyUpdateKbn = ref(TeX0002Const.DEFAULT.UPDATE_KBN_U)

// アセスメント複写ダイアログ表示フラグ
const showDialogOr35672 = computed(() => {
  return Or35672Logic.state.get(or35672.value.uniqueCpId)?.isOpen ?? false
})

// アセスメントマスタダイアログ表示フラグ
const showDialogOrX0001 = computed(() => {
  // Or27562のダイアログ開閉状態
  return OrX0001Logic.state.get(orX0001.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr57082 = computed(() => {
  // Or57082のダイアログ開閉状態
  return Or57082Logic.state.get(or57082.value.uniqueCpId)?.isOpen ?? false
})

// アセスメントマスタダイアログ表示フラグ
const showDialogOr27562 = computed(() => {
  // Or27562のダイアログ開閉状態
  return Or27562Logic.state.get(or27562.value.uniqueCpId)?.isOpen ?? false
})

// 計画対象期間ダイアログ表示フラグ
const showDialogOrX0115 = computed(() => {
  // OrX0115 のダイアログ開閉状態
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr28326 = computed(() => {
  // Or28326 のダイアログ開閉状態
  return Or28326Logic.state.get(or28326.value.uniqueCpId)?.isOpen ?? false
})

// 職員検索ダイアログ表示フラグ
const showDialogOr26257 = computed(() => {
  // Or26257のダイアログ開閉状態
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  // ブラウザの「戻る」「進む」ボタンからの遷移時に以前の値を復元したい画面の場合は
  // 画面情報領域のsupplement内のisInitで処理を分岐させて初回表示時のみ画面データの取得を行なう
  // ※値の復元が不要な画面では分岐は不要
  isInitCompleted = false
  if (isInit) {
    // コントロール設定
    initControls()

    local.mo00043.id = Or30732Const.DEFAULT.TAB_ID
  }
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<TeX0002StateType>({
  cpId: TeX0002Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    // 保存完了処理
    isSaveCompleted: (value) => {
      if (value) {
        void saveCompletedProcess()

        // 保存完了フラグをリセット
        setState({ isSaveCompleted: false })
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(0)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or35672Const.CP_ID(0)]: or35672.value,
  [Or27562Const.CP_ID(0)]: or27562.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [OrX0115Const.CP_ID(0)]: orX0115.value,
  [Or28326Const.CP_ID(0)]: or28326.value,
  [OrX0009Const.CP_ID(0)]: orX0009.value,
  [OrD2002Const.CP_ID(0)]: orD2002.value,
  [Or26257Const.CP_ID(0)]: or26257.value,
  [Or21813Const.CP_ID(0)]: or21813.value, // エラーダイアログ(ok)
  [Or21814Const.CP_ID(1)]: or21814_1.value, // 確認ダイアログ(yes, no, cancel)
  [Or21814Const.CP_ID(2)]: or21814_2.value, // 確認ダイアログ(yes)
  [Or21814Const.CP_ID(3)]: or21814_3.value, // 確認ダイアログ(yes, no)
  [Or28992Const.CP_ID(0)]: or28992.value,
  [Or11131Const.CP_ID(0)]: or11131.value,
  [OrX0001Const.CP_ID(0)]: orX0001.value,
  [Or29241Const.CP_ID(0)]: or29241.value,
  [Or29242Const.CP_ID(0)]: or29242.value,
  [Or00386Const.CP_ID(0)]: or00386.value,
  [Or31535Const.CP_ID(0)]: or31535.value,
  [Or32081Const.CP_ID(0)]: or32081.value,
  [Or13850Const.CP_ID(0)]: or13850.value,
  [Or13844Const.CP_ID(0)]: or13844.value,
  [Or13872Const.CP_ID(0)]: or13872.value,
  [Or30732Const.CP_ID(1)]: or30732.value,
  [Or29958Const.CP_ID(0)]: or29958.value,
  [Or33097Const.CP_ID(0)]: or33097.value,
  [Or30149Const.CP_ID(0)]: or30149.value,
  [Or32643Const.CP_ID(0)]: or32643.value,
  [Or01997Const.CP_ID(0)]: or01997.value,
  [Or57082Const.CP_ID(0)]: or57082.value,
})

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.assessment'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    tooltipTextSaveBtn: t('tooltip.save'),
    showCreateBtn: true,
    tooltipTextCreateBtn: t('tooltip.create'),
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: true,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
  },
})

// ★利用者一覧詳細表示(Or00248)の「領域キー」を設定
Or00248Logic.state.set({
  uniqueCpId: or00248.value.uniqueCpId,
  state: {
    regionKey: props.uniqueCpId, // 「領域キー」を設定
  },
})

systemCommonsStore.setUserSelectZenkaiSvJigyoIdList([''])
systemCommonsStore.setUserSelectSelfId('')

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange, props.uniqueCpId)

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 新しいデータの値をチェックし、全falseの場合は処理を中断する
    const isAllFalseFlg = Object.values(newValue).every((item) => item === false)
    if (isAllFalseFlg) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      $log.debug('お気に入り')
      setOr11871Event({ favoriteEventFlg: false })
      return
    }
    if (newValue.saveEventFlg) {
      $log.debug('保存')
      // 保存ボタンが押下された場合、保存処理を実行する
      setOr11871Event({ saveEventFlg: false })

      // 保存処理
      _save()
      return
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      $log.debug('新規')
      setOr11871Event({ createEventFlg: false })
      await createNew()
      return
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      _delete()
      setOr11871Event({ deleteEventFlg: false })
      return
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      $log.debug('複写')
      _copy()
      return
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
      $log.debug('印刷')
      await _print()
      setOr11871Event({ printEventFlg: false })
      return
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      $log.debug('マスタ他')
      await _master()
      setOr11871Event({ masterEventFlg: false })
      return
    }
  }
)

/**
 * 複写画面のイベントを監視
 */
watch(
  () => Or35672Logic.event.get(Or35672Const.CP_ID(0) + or35672.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue?.confirm === false && newValue?.confirm_multiple === false) {
      return
    }

    setOr35672Event({ confirm: false, confirm_multiple: false })

    // 該当タブで確定を押下する場合
    if (newValue.confirm) {
      // 複写データを取得
      const copyData = Or35672Logic.data.get(or35672.value.uniqueCpId)
      copyFInfo.value = {
        userId: copyData?.resData?.userId ?? '',
        createdYmd: copyData?.resData?.createdYmd ?? '',
        ninteiFormF: copyData?.resData?.ninteiFormF ?? '',
        sc1Id: copyData?.resData?.sc1Id ?? '',
        gdlId: copyData?.resData?.gdlId ?? '',
      }

      // 複写元の課題と目標取得し、画面の課題と目標リストに追加
      await getKadaiList(copyFInfo.value.sc1Id, copyFInfo.value.gdlId, local.mo00043.id)

      setEvent({ copyEventFlg: true })
    }

    // 複数タブ上書の場合
    if (newValue.confirm_multiple) {
      // 履歴情報再取得
      void getHistoryInfo(
        localOneway.hisotrySelectOneway.historyInfo?.rirekiId ?? '',
        TeX0002Const.DEFAULT.HISTORY_KBN_OPEN
      ).then(() => {
        // タブ情報再取得
        reload()
      })
    }
  }
)

/**
 * タブ変更監視
 */
watch(
  () => showDialogOr27562.value,
  () => {
    localOneway.or27562Oneway = {
      /** 事業者ID */
      svJigyoId: jigyoInfo.value.svJigyoId,
      /** 分類1 */
      bunrui1Id: '2',
      /** 分類2 */
      bunrui2Id: '12',
      /** 施設ID */
      shisetuId: jigyoInfo.value.shisetuId,
    }
  }
)

/**
 * 計画対象期間履歴チェンジワッチャー
 */
watch(
  () => Or13844Logic.event.get(or13844.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.preBtnClickFlg === false && newValue.nextBtnClickFlg === false) {
      return
    }

    setOr13844Event({ preBtnClickFlg: false, nextBtnClickFlg: false })

    eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_PLAN_PERIOD_CHANGE

    const sc1Id = localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id ?? ''

    // 画面変更チェック
    switch (await checkChange(true)) {
      case 0: {
        // 画面入力変更なし
        // 計画期間切替処理、計画期間情報、履歴情報を再取得
        let isGetPlanningPeriodInfo = false
        if (newValue?.preBtnClickFlg) {
          isGetPlanningPeriodInfo = await getPlanningPeriodInfo(
            sc1Id,
            TeX0002Const.DEFAULT.PERIOD_KBN_PREV
          )
        }
        if (newValue?.nextBtnClickFlg) {
          isGetPlanningPeriodInfo = await getPlanningPeriodInfo(
            sc1Id,
            TeX0002Const.DEFAULT.PERIOD_KBN_NEXT
          )
        }

        if (isGetPlanningPeriodInfo) {
          // 画面情報再取得
          reload()
        }

        break
      }
      case 1: {
        // 画面入力変更あり、編集を破棄して処理を続行
        // 計画期間切替処理、計画期間情報、履歴情報を再取得
        let isGetPlanningPeriodInfo = false
        if (newValue?.preBtnClickFlg) {
          isGetPlanningPeriodInfo = await getPlanningPeriodInfo(
            sc1Id,
            TeX0002Const.DEFAULT.PERIOD_KBN_PREV
          )
        }
        if (newValue?.nextBtnClickFlg) {
          isGetPlanningPeriodInfo = await getPlanningPeriodInfo(
            sc1Id,
            TeX0002Const.DEFAULT.PERIOD_KBN_NEXT
          )
        }

        if (isGetPlanningPeriodInfo) {
          // 画面情報再取得
          reload()
        }
        break
      }
      case 2: {
        // 画面入力変更あり、編集を保存して処理を続行
        // 計画期間切替処理、計画期間情報、履歴情報を再取得
        if (newValue?.preBtnClickFlg) {
          eventInfo.value.pageFlg = TeX0002Const.DEFAULT.PERIOD_KBN_PREV
        }
        if (newValue?.nextBtnClickFlg) {
          eventInfo.value.pageFlg = TeX0002Const.DEFAULT.PERIOD_KBN_NEXT
        }

        // 保存
        setEvent({ saveEventFlg: true })

        break
      }
      case 3: {
        // キャンセル、何もしない
        break
      }
    }
  },
  { deep: true }
)

/**
 * 作成者変更ワッチャー
 */
watch(
  () => local.or26257,
  (newValue) => {
    if (newValue?.shokuin) {
      authorInfo.value.id = newValue?.shokuin.chkShokuId
      authorInfo.value.name = newValue?.shokuin.shokuin1Knj + ' ' + newValue?.shokuin.shokuin2Knj
      local.orX0157.value = newValue?.shokuin.shokuin1Knj + ' ' + newValue?.shokuin.shokuin2Knj
    }
  },
  { deep: true }
)

/**
 * 履歴チェンジワッチャー
 */
watch(
  () => Or13872Logic.event.get(or13872.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.preBtnClickFlg === false && newValue.nextBtnClickFlg === false) {
      return
    }

    setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })

    eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_HISTORY_CHANGE

    const gdlId = localOneway.hisotrySelectOneway.historyInfo?.rirekiId ?? ''

    // 画面変更チェック
    switch (await checkChange(true)) {
      case 0: {
        // 画面入力変更なし
        // 履歴変更処理、履歴情報を再取得
        if (newValue?.preBtnClickFlg) {
          await getHistoryInfo(gdlId, TeX0002Const.DEFAULT.HISTORY_KBN_PREV)
        }
        if (newValue?.nextBtnClickFlg) {
          await getHistoryInfo(gdlId, TeX0002Const.DEFAULT.HISTORY_KBN_NEXT)
        }

        // 画面情報再取得
        reload()

        break
      }
      case 1: {
        // 画面入力変更あり、編集を破棄して処理を続行
        // 履歴情報を再取得
        if (newValue?.preBtnClickFlg) {
          await getHistoryInfo(gdlId, TeX0002Const.DEFAULT.HISTORY_KBN_PREV)
        }
        if (newValue?.nextBtnClickFlg) {
          await getHistoryInfo(gdlId, TeX0002Const.DEFAULT.HISTORY_KBN_NEXT)
        }

        // 画面情報再取得
        reload()
        break
      }
      case 2: {
        // 画面入力変更あり、編集を保存して処理を続行
        if (newValue?.preBtnClickFlg) {
          eventInfo.value.pageFlg = TeX0002Const.DEFAULT.HISTORY_KBN_PREV
        }
        if (newValue?.nextBtnClickFlg) {
          eventInfo.value.pageFlg = TeX0002Const.DEFAULT.HISTORY_KBN_NEXT
        }

        // 保存
        setEvent({ saveEventFlg: true })
        break
      }
      case 3: {
        // キャンセル、何もしない
        break
      }
    }
  },
  { deep: true }
)

// 計画対象期間選択変更ワッチャー
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  async (newValue) => {
    if (newValue) {
      // 計画対象期間情報を再取得
      await getPlanningPeriodInfo(newValue.kikanId, TeX0002Const.DEFAULT.PERIOD_KBN_OPEN)

      // 画面情報再取得
      reload()
    }
  }
)

/**
 * タブ変更監視
 */
watch(
  () => local.mo00043.id,
  (newValue) => {
    if (newValue === undefined || isLoading) {
      return
    }

    // 画面情報再取得
    if (userId.value.length > 0) {
      reload()
    }
  }
)

/**************************************************
 * 関数
 **************************************************/
/**
 *  コントロール初期化
 */
const initControls = () => {
  permission.value.register = true
  permission.value.print = true

  // 登録権限がない場合、非活性
  if (!permission.value.register) {
    Or11871Logic.state.set({
      uniqueCpId: or11871.value.uniqueCpId,
      state: {
        disabledSaveBtn: true,
      },
    })
  }

  // 印刷権限がない場合、非活性
  if (!permission.value.print) {
    Or11871Logic.state.set({
      uniqueCpId: or11871.value.uniqueCpId,
      state: {
        disabledMasterBtn: true,
      },
    })
  }

  // 改訂フラグ TODO 共通情報から取得
  ninteiFormF.value = TeX0002Const.DEFAULT.KAITEI_FLG_H21

  local.createDate.value =
    systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
  createDateOld.value = local.createDate.value

  // 初期設定マスタからフラグを取得し保持
  // VISIOフラグ
  localFlg.visioFlg = useCmnRouteCom().getInitialSettingMaster()?.visioFlg ?? ''

  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
      verticalLayout: true,
    },
  })

  // エラーダイアログを初期化(ok)
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
    },
  })

  // 確認ダイアログを初期化(yes, no, cancel)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを初期化(ok)
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
    },
  })

  // 確認ダイアログを初期化(yes, no)
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
    },
  })

  localOneway.mo00043OnewayType.tabItems = [
    {
      id: Or30732Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-1'),
      tooltipText: t('label.assessment-home-tab-name-1'),
    },
    {
      id: Or31535Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-2'),
      tooltipText: t('label.assessment-home-tab-name-2'),
    },
    {
      id: Or32643Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-3'),
      tooltipText: t('label.assessment-home-tab-name-3'),
    },
    {
      id: Or00386Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-4'),
      tooltipText: t('label.assessment-home-tab-name-4'),
    },
    {
      id: Or29958Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-5'),
      tooltipText: t('label.assessment-home-tab-name-5'),
    },
    {
      id: Or28992Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-6-1'),
      tooltipText: t('label.assessment-home-tab-name-6-1'),
    },
    {
      id: Or29241Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-6-2'),
      tooltipText: t('label.assessment-home-tab-name-6-2'),
    },
    {
      id: Or29242Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-6-3-4'),
      tooltipText: t('label.assessment-home-tab-name-6-3-4'),
    },
    {
      id: Or33097Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-6-5'),
      tooltipText: t('label.assessment-home-tab-name-6-5'),
    },
    {
      id: Or30149Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-6-6'),
      tooltipText: t('label.assessment-home-tab-name-6-6'),
    },
    {
      id: Or32081Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-6-7'),
      tooltipText: t('label.assessment-home-tab-name-6-7'),
    },
    {
      id: Or01997Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-7-1'),
      tooltipText: t('label.assessment-home-tab-name-7-1'),
    },
    {
      id: Or11131Const.DEFAULT.TAB_ID,
      title: t('label.assessment-home-tab-name-7-2'),
      tooltipText: t('label.assessment-home-tab-name-7-2'),
    },
  ]
}

/**
 * 監視を起動
 *
 */
function setupWatch() {
  /**
   * 上部情報変更監視
   */
  watch(
    () => [local.createDate.value, authorInfo.value.id],
    () => {
      isTopEdit.value = true
    },
    { deep: true }
  )
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
function setOr11871Event(event: Record<string, boolean>) {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
function setOr35672Event(event: Record<string, boolean>) {
  Or35672Logic.event.set({
    uniqueCpId: Or35672Const.CP_ID(0) + or35672.value.uniqueCpId,
    events: event,
  })
}

/**
 * イベント発火
 *
 * @param event - イベント
 */
function setEvent(event: Record<string, boolean>) {
  // 複写の場合
  if (event.copyEventFlg === true) {
    const copyData = Or35672Logic.data.get(or35672.value.uniqueCpId)
    copyFInfo.value = {
      userId: copyData?.resData?.userId ?? '',
      createdYmd: copyData?.resData?.createdYmd ?? '',
      ninteiFormF: copyData?.resData?.ninteiFormF ?? '',
      sc1Id: copyData?.resData?.sc1Id ?? '',
      gdlId: copyData?.resData?.gdlId ?? '',
    }
  }

  // 課題と目標リストを取得
  const issuesAndGoalsList = [] as IssuesAndGoalListItem[]
  local.issuesAndGoalsList?.items.forEach((item) => {
    issuesAndGoalsList.push(item)
  })

  // 画面共通情報を設定
  setCommonInfo({
    /** 期間管理フラグ */
    kikanKanriFlg: localOneway.planningPeriodSelectOneway.plainningPeriodManageFlg,
    /** 改定フラグ */
    ninteiFormF: ninteiFormF.value,
    /** 種別ID */
    syubetuId: syubetuId.value,
    /** 履歴更新区分 */
    historyUpdateKbn: historyUpdateKbn.value,
    /** 履歴更新回数 */
    historyModifiedCnt: historyModifiedCnt.value,
    /** 選択中タブ */
    activeTabId: local.mo00043.id,
    /** 事業所ID */
    jigyoId: jigyoInfo.value.svJigyoId,
    /** 事業所名 */
    jigyoKnj: jigyoInfo.value.svjigyoName,
    /** 施設ID */
    shisetuId: jigyoInfo.value.shisetuId,
    /** 法人ID */
    houjinId: jigyoInfo.value.houjinId,
    /** 利用者ID */
    userId: userId.value,
    /** 計画対象期間ID */
    sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id,
    /** 計画対象期間番号 */
    sc1No: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodNo,
    /** 計画対象期間開始日 */
    periodStartYmd: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodStartYmd,
    /** 計画対象期間終了日 */
    periodEndYmd: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodEndYmd,
    /** アセスメントID */
    gdlId: localOneway.hisotrySelectOneway.historyInfo?.rirekiId,
    /** 履歴番号 */
    historyNo: localOneway.hisotrySelectOneway.historyInfo?.krirekiNo,
    /** 作成者ID */
    createUserId: authorInfo.value.id,
    /** 作成者名 */
    createUserName: authorInfo.value.name,
    /** 作成日 */
    createYmd: local.createDate.value,
    /** 複写元 利用者ID */
    copyUserId: copyFInfo.value.userId,
    /** 複写元 作成日 */
    copyCreateYmd: copyFInfo.value.createdYmd,
    /** 複写元 認定フラグ */
    copyNinteiFormF: copyFInfo.value.ninteiFormF,
    /** 複写元 計画期間ID */
    copySc1Id: copyFInfo.value.sc1Id,
    /** 複写元 アセスメントID */
    copyGdlId: copyFInfo.value.gdlId,
    /** 削除区分 */
    deleteKbn: deleteKbn.value,
    /** 課題と目標リスト */
    issuesAndGoalsList: issuesAndGoalsList,
  })

  TeX0002Logic.event.set({
    uniqueCpId: props.uniqueCpId,
    events: {
      /** 再表示発火フラグ */
      isRefresh: event.isRefresh,
      /** 作成日変更フラグ */
      isCreateDateChanged: event.isCreateDateChanged,
      /** お気に入りイベント発火フラグ */
      favoriteEventFlg: event.favoriteEventFlg,
      /** 保存イベント発火フラグ */
      saveEventFlg: event.saveEventFlg,
      /** 保存のみイベント発火フラグ */
      saveOnlyEventFlg: event.saveOnlyEventFlg,
      /** 新規イベント発火フラグ */
      createEventFlg: event.createEventFlg,
      /** 複写イベント発火フラグ */
      copyEventFlg: event.copyEventFlg,
      /** 保存のみイベント発火フラグ */
      tabChangeSaveEventFlg: event.tabChangeSaveEventFlg,
      /** 印刷イベント発火フラグ */
      printEventFlg: event.printEventFlg,
      /** マスタ他イベント発火フラグ */
      masterEventFlg: event.masterEventFlg,
      /** 削除イベント発火フラグ */
      deleteEventFlg: event.deleteEventFlg,
    },
  })
}

/**
 * 各タブの課題と目標一覧を取得
 *
 */
function getIssuesAndGoalList() {
  let uniqueCpId = ''
  let issuesAndGoalsList = [] as IssuesAndGoalListItem[]

  switch (local.mo00043.id) {
    case Or30732Const.DEFAULT.TAB_ID:
      uniqueCpId = or30732.value.uniqueCpId
      break
    case Or31535Const.DEFAULT.TAB_ID:
      break
    case Or32643Const.DEFAULT.TAB_ID:
      break
    case Or00386Const.DEFAULT.TAB_ID:
      break
    case Or29958Const.DEFAULT.TAB_ID:
      break
    case Or28992Const.DEFAULT.TAB_ID:
      break
    case Or29241Const.DEFAULT.TAB_ID:
      break
    case Or29242Const.DEFAULT.TAB_ID:
      break
    case Or33097Const.DEFAULT.TAB_ID:
      break
    case Or30149Const.DEFAULT.TAB_ID:
      break
    case Or32081Const.DEFAULT.TAB_ID:
      break
    case Or01997Const.DEFAULT.TAB_ID:
      break
    case Or11131Const.DEFAULT.TAB_ID:
      break
  }

  if (uniqueCpId) {
    const childCpBindsData = getChildCpBinds(uniqueCpId, {
      // 課題と目標リスト
      [OrX0209Const.CP_ID(0)]: { cpPath: OrX0209Const.CP_ID(0), twoWayFlg: true },
    })

    // 課題と目標リスト
    const changedIssuesAndGoalsList = childCpBindsData[OrX0209Const.CP_ID(0)].twoWayBind
      ?.value as OrX0209Type

    issuesAndGoalsList = changedIssuesAndGoalsList.items
  }

  return issuesAndGoalsList
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
function setOr13844Event(event: Record<string, boolean>) {
  Or13844Logic.event.set({
    uniqueCpId: or13844.value.uniqueCpId,
    events: event,
  })
}

/**
 * Or13872イベント発火
 *
 * @param event - イベント
 */
function setOr13872Event(event: Record<string, boolean>) {
  Or13872Logic.event.set({
    uniqueCpId: or13872.value.uniqueCpId,
    events: event,
  })
}

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */
function callbackUserChange(newSelfId: string) {
  if (newSelfId !== '' && userId.value === '') {
    // 利用者変更処理
    void userChange(newSelfId)
    return
  }

  // 変更がない場合、スキップ
  if (newSelfId === userId.value) {
    return
  }

  eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_USER_CHANGE

  // 利用者変更処理
  void userChange(newSelfId)
}

/**
 *  画面初期情報再取得
 */
function reload() {
  // 計画対象期間がない場合、リロードしない
  if (
    plannningPeriodManageFlg.value === TeX0002Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
    (localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id === undefined ||
      localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id === '' ||
      !localOneway.planningPeriodSelectOneway.planningPeriodInfo)
  ) {
    eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_NORMAL

    void nextTick(() => {
      isTopEdit.value = false
    })

    return
  }

  // 新規、印刷ボタン制御
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      disabledCreateBtn: false,
      disabledPrintBtn: false,
    },
  })

  // 複写ボタン制御
  optionMenuState.value.copyBtnDisabled = false
  // 削除ボタン制御
  optionMenuState.value.deleteBtnDisabled = false

  // 作成者選択アイコンボタンを活性
  if (localOneway.orX0157Oneway.text) {
    localOneway.orX0157Oneway.text.orX0157InputOneway.disabled = false
  }
  // 作成日を活性
  localOneway.createDateOneway.disabled = false
  // 削除区分を設定
  deleteKbn.value = TeX0002Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL

  eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_NORMAL

  // 更新区分を設定
  historyUpdateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_N

  void nextTick(() => {
    isTopEdit.value = false
  })

  // 画面情報再取得
  setEvent({ isRefresh: true })
}

/**
 *  画面初期情報取得
 *
 */
async function getInitDataInfo() {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentHomeCommonInfoSelectInEntity = {
    /** 事業所ID */
    svJigyoId: jigyoInfo.value.svJigyoId,
    /** 利用者ID */
    userid: userId.value,
    /** 施設ID */
    shisetuId: jigyoInfo.value.shisetuId,
  }
  const resData: AssessmentHomeCommonInfoSelectOutEntity = await ScreenRepository.select(
    'assessmentHomeCommonInfoSelect',
    inputData
  )

  /** =============画面情報を設定============= */
  if (resData?.data) {
    /** ------------計画対象期間を設定------------ */
    // 計画期間管理フラグ設定
    if (resData?.data?.kikanFlg) {
      localOneway.planningPeriodSelectOneway.plainningPeriodManageFlg = resData?.data?.kikanFlg
      plannningPeriodManageFlg.value = resData?.data?.kikanFlg
    }

    // 種別ID
    syubetuId.value = resData?.data?.syubetsuId

    // 計画対象期間管理フラグが「1:管理する」場合、計画対象期間情報を設定
    if (plannningPeriodManageFlg.value === TeX0002Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
      if (resData?.data?.planPeriodInfo?.sc1Id) {
        localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
          id: resData.data.planPeriodInfo.sc1Id,
          period: resData.data.planPeriodInfo.startYmd + '～' + resData.data.planPeriodInfo.endYmd,
          periodStartYmd: resData.data.planPeriodInfo.startYmd,
          periodEndYmd: resData.data.planPeriodInfo.endYmd,
          periodNo: resData.data.planPeriodInfo.periodNo ?? '0',
          periodCnt: resData.data.planPeriodInfo.periodCnt ?? '0',
        }
        isHistoryShow.value = true
      } else {
        localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
          id: '',
          period: '',
          periodStartYmd: '',
          periodEndYmd: '',
          periodNo: '0',
          periodCnt: '0',
        }
        isHistoryShow.value = false
      }
    } else {
      localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
        id: '0',
        period: '',
        periodStartYmd: '',
        periodEndYmd: '',
        periodNo: '',
        periodCnt: '',
      }
      isHistoryShow.value = true
    }

    /** ------------履歴情報を設定------------ */
    // 表示中計画対象期間の履歴情報取得
    if (resData?.data?.historyInfo?.gdlId) {
      localOneway.hisotrySelectOneway.historyInfo = {
        /** アセスメントID */
        rirekiId: resData.data.historyInfo.gdlId,
        /** 履歴番号 */
        krirekiNo: resData.data.historyInfo.krirekiNo ?? '1',
        /** 履歴総件数 */
        krirekiCnt: resData.data.historyInfo.krirekiCnt ?? '1',
      }
      // 改定フラグ
      ninteiFormF.value =
        resData.data.historyInfo.ninteiFormF ?? TeX0002Const.DEFAULT.KAITEI_FLG_R34
      // 履歴更新回数
      historyModifiedCnt.value = resData.data.historyInfo.modifiedCnt

      // 作成者を設定
      authorInfo.value.id = resData.data.historyInfo.shokuId
      authorInfo.value.name = resData.data.historyInfo.shokuinName
      local.orX0157.value = resData.data.historyInfo.shokuinName ?? ''
      // 作成日を設定
      local.createDate.value = resData.data.historyInfo.asJisshiDateYmd ?? ''
      createDateOld.value = local.createDate.value

      /** ------------課題と目標リストを設定------------ */
      // 課題と目標リスト
      local.issuesAndGoalsList.items = []
      resData?.data?.gdlKadaiList?.forEach((item) => {
        local.issuesAndGoalsList.items.push({
          /** id */
          id: uuidv4(),
          /** データID */
          dataId: item.id ?? '',
          /** アセスメントID */
          gdlId: item.gdlId,
          /** 計画期間ID */
          sc1Id: item.sc1Id,
          /** アセスメント番号 */
          assNo: item.assNo,
          /** アセスメント名 */
          assessmentName: {
            value: item.assName?.replaceAll('~r~n', '\r\n') ?? '',
          } as Mo01337OnewayType,
          /** 生活全般の解決すべき課題 */
          wholeLifeShouldSolutionIssues: {
            value: item.kadaiKnj,
          } as OrX0163Type,
          /** 長期目標 */
          longtermGoal: {
            value: item.choukiKnj,
          } as OrX0163Type,
          /** 短期目標 */
          shorttermGoal: {
            value: item.tankiKnj,
          } as OrX0163Type,
          /** シーケンス */
          seq: Number(item.seq) || 0,
          /** 更新回数 */
          modifiedCnt: item.modifiedCnt,
          /** 更新区分 */
          updateKbn: TeX0002Const.DEFAULT.UPDATE_KBN_N,
        })
      })
    } else {
      localOneway.hisotrySelectOneway.historyInfo = {
        /** アセスメントID */
        rirekiId: TeX0002Const.DEFAULT.HISTORY_ID_DEFAULT,
        /** 履歴番号 */
        krirekiNo: '0',
        /** 履歴総件数 */
        krirekiCnt: '0',
      }

      if (localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id) {
        // 新規処理
        historyNew()
      }
    }
  } else {
    localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
      id: '',
      period: '',
      periodStartYmd: '',
      periodEndYmd: '',
      periodNo: '0',
      periodCnt: '0',
    }
    isHistoryShow.value = false
  }

  eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_NORMAL

  // 監視を起動
  setTimeout(() => {
    setupWatch()
  }, 0)
}

/**
 *  画面共通情報を設定
 *
 * @param data - 設定情報
 */
function setCommonInfo(data: TeX0002Type) {
  const existedData = TeX0002Logic.data.get(props.uniqueCpId)
  const newData = {
    /** 期間管理フラグ */
    kikanKanriFlg: data.kikanKanriFlg ?? existedData?.kikanKanriFlg,
    /** 改定フラグ */
    ninteiFormF: data.ninteiFormF ?? existedData?.ninteiFormF,
    /** 種別ID */
    syubetuId: data.syubetuId ?? existedData?.syubetuId,
    /** 履歴更新区分 */
    historyUpdateKbn: data.historyUpdateKbn ?? existedData?.historyUpdateKbn,
    /** 履歴更新回数 */
    historyModifiedCnt: data.historyModifiedCnt ?? existedData?.historyModifiedCnt,
    /** 選択中タブ */
    activeTabId: data.activeTabId ?? existedData?.activeTabId,
    /** 事業所ID */
    jigyoId: data.jigyoId ?? existedData?.jigyoId,
    /** 事業所名 */
    jigyoKnj: data.jigyoKnj ?? existedData?.jigyoKnj,
    /** 施設ID */
    shisetuId: data.shisetuId ?? existedData?.shisetuId,
    /** 法人ID */
    houjinId: data.houjinId ?? existedData?.houjinId,
    /** 計画期間ID */
    sc1Id: data.sc1Id ?? existedData?.sc1Id,
    /** 利用者ID */
    userId: data.userId ?? existedData?.userId,
    /** 計画対象期間番号 */
    sc1No: data.sc1No ?? existedData?.sc1No,
    /** 計画対象期間開始日 */
    periodStartYmd: data.periodStartYmd ?? existedData?.periodStartYmd,
    /** 計画対象期間終了日 */
    periodEndYmd: data.periodEndYmd ?? existedData?.periodEndYmd,
    /** アセスメントID */
    gdlId: data.gdlId ?? existedData?.gdlId,
    /** 履歴番号 */
    historyNo: data.historyNo ?? existedData?.historyNo,
    /** 作成者ID */
    createUserId: data.createUserId ?? existedData?.createUserId,
    /** 作成者名 */
    createUserName: data.createUserName ?? existedData?.createUserName,
    /** 履歴作成日 */
    createYmd: data.createYmd ?? existedData?.createYmd,
    /** 複写元 利用者ID */
    copyUserId: data.copyUserId ?? existedData?.copyUserId,
    /** 複写元 作成日 */
    copyCreateYmd: data.copyCreateYmd ?? existedData?.copyCreateYmd,
    /** 複写元 認定フラグ */
    copyNinteiFormF: data.copyNinteiFormF ?? existedData?.copyNinteiFormF,
    /** 複写元 計画期間ID */
    copySc1Id: data.copySc1Id ?? existedData?.copySc1Id,
    /** 複写元 アセスメントID */
    copyGdlId: data.copyGdlId ?? existedData?.copyGdlId,
    /** 削除区分 */
    deleteKbn: data.deleteKbn ?? existedData?.deleteKbn,
    /** 課題と目標リスト */
    issuesAndGoalsList: data.issuesAndGoalsList ?? existedData?.issuesAndGoalsList,
  } as TeX0002Type

  TeX0002Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      /** 期間管理フラグ */
      kikanKanriFlg: newData.kikanKanriFlg,
      /** 改定フラグ */
      ninteiFormF: newData.ninteiFormF,
      /** 種別ID */
      syubetuId: newData.syubetuId,
      /** 履歴更新区分 */
      historyUpdateKbn: newData.historyUpdateKbn,
      /** 履歴更新回数 */
      historyModifiedCnt: newData.historyModifiedCnt,
      /** 選択中タブ */
      activeTabId: newData.activeTabId,
      /** 事業所ID */
      jigyoId: newData.jigyoId,
      /** 事業所名 */
      jigyoKnj: newData.jigyoKnj,
      /** 施設ID */
      shisetuId: newData.shisetuId,
      /** 法人ID */
      houjinId: newData.houjinId,
      /** 利用者ID */
      userId: newData.userId,
      /** 作成者名 */
      createUserName: newData.createUserName,
      /** 計画期間ID */
      sc1Id: newData.sc1Id,
      /** 計画対象期間番号 */
      sc1No: newData.sc1No,
      /** 計画対象期間開始日 */
      periodStartYmd: newData.periodStartYmd,
      /** 計画対象期間終了日 */
      periodEndYmd: newData.periodEndYmd,
      /** アセスメントID */
      gdlId: newData.gdlId,
      /** 履歴番号 */
      historyNo: newData.historyNo,
      /** 作成者ID */
      createUserId: newData.createUserId,
      /** 履歴作成日 */
      createYmd: newData.createYmd,
      /** 複写元 利用者ID */
      copyUserId: newData.copyUserId,
      /** 複写元 作成日 */
      copyCreateYmd: newData.copyCreateYmd,
      /** 複写元 認定フラグ */
      copyNinteiFormF: newData.copyNinteiFormF,
      /** 複写元 計画期間ID */
      copySc1Id: newData.copySc1Id,
      /** 複写元 アセスメントID */
      copyGdlId: newData.copyGdlId,
      /** 削除区分 */
      deleteKbn: newData.deleteKbn,
      /** 課題と目標リスト */
      issuesAndGoalsList: cloneDeep(newData.issuesAndGoalsList),
    },
  })
}

/**
 *  計画対象期間チェンジ設定
 *
 * @param sc1Id - 計画対象期間ID
 *
 * @param kikanKbn - 期間処理区分
 */
async function getPlanningPeriodInfo(sc1Id: string, kikanKbn: string): Promise<boolean> {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentHomePlanningPeriodChangeSelectInEntity = {
    /** 事業所ID */
    svJigyoId: jigyoInfo.value.svJigyoId,
    /** 利用者ID */
    userid: userId.value,
    /** 種別ID */
    syubetsuId: syubetuId.value,
    /** 施設ID */
    shisetuId: jigyoInfo.value.shisetuId,
    /** 計画期間ID */
    sc1Id: sc1Id,
    /** 計画期間ページ区分 */
    kikanKbn: kikanKbn,
  }
  const resData: AssessmentHomePlanningPeriodChangeSelectOutEntity = await ScreenRepository.select(
    'assessmentHomePlanningPeriodChangeSelect',
    inputData
  )

  // 画面情報を設定
  if (resData?.data) {
    // エラー区分を判定
    if (resData.data.errKbn === TeX0002Const.DEFAULT.PLAN_PERIOD_ERROR_KBN_1) {
      // 確認ダイアログ表示
      openConfirmDialog2(t('message.w-com-11262'))
      return false
    } else if (resData.data.errKbn === TeX0002Const.DEFAULT.PLAN_PERIOD_ERROR_KBN_2) {
      // 確認ダイアログ表示
      openConfirmDialog2(t('message.w-com-11263'))
      return false
    }

    // ------------計画対象期間を設定------------
    // 計画対象期間管理フラグが「1:管理する」場合、計画対象期間情報を設定
    if (plannningPeriodManageFlg.value === TeX0002Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
      if (resData?.data?.planPeriodInfo?.sc1Id) {
        localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
          id: resData.data.planPeriodInfo.sc1Id,
          period: resData.data.planPeriodInfo.startYmd + '～' + resData.data.planPeriodInfo.endYmd,
          periodStartYmd: resData.data.planPeriodInfo.startYmd,
          periodEndYmd: resData.data.planPeriodInfo.endYmd,
          periodNo: resData.data.planPeriodInfo.periodNo ?? '0',
          periodCnt: resData.data.planPeriodInfo.periodCnt ?? '0',
        }
      } else {
        localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
          id: '',
          period: '',
          periodStartYmd: '',
          periodEndYmd: '',
          periodNo: '1',
          periodCnt: '1',
        }
        isHistoryShow.value = false
      }
    }

    if (resData?.data?.historyInfo?.gdlId) {
      // 表示中計画対象期間の履歴情報取得
      localOneway.hisotrySelectOneway.historyInfo = {
        /** アセスメントID */
        rirekiId: resData.data.historyInfo.gdlId,
        /** 履歴番号 */
        krirekiNo: resData.data.historyInfo.krirekiNo ?? '1',
        /** 履歴総件数 */
        krirekiCnt: resData.data.historyInfo.krirekiCnt ?? '1',
      }
      // 改定フラグ
      ninteiFormF.value = resData.data.historyInfo.ninteiFormF
      // 履歴更新回数
      historyModifiedCnt.value = resData.data.historyInfo.modifiedCnt

      // 作成者を設定
      authorInfo.value.id = resData.data.historyInfo.shokuId
      authorInfo.value.name = resData.data.historyInfo.shokuinName
      local.orX0157.value = resData.data.historyInfo.shokuinName ?? ''
      // 作成日を設定
      local.createDate.value = resData.data.historyInfo.asJisshiDateYmd ?? ''
      createDateOld.value = local.createDate.value

      // 課題と目標リスト
      local.issuesAndGoalsList.items = []
      resData?.data?.gdlKadaiList?.forEach((item) => {
        local.issuesAndGoalsList.items.push({
          /** id */
          id: uuidv4(),
          /** データID */
          dataId: item.id ?? '',
          /** アセスメントID */
          gdlId: item.gdlId,
          /** 計画期間ID */
          sc1Id: item.sc1Id,
          /** アセスメント番号 */
          assNo: item.assNo,
          /** アセスメント名 */
          assessmentName: {
            value: item.assName?.replaceAll('~r~n', '\r\n') ?? '',
          } as Mo01337OnewayType,
          /** 生活全般の解決すべき課題 */
          wholeLifeShouldSolutionIssues: {
            value: item.kadaiKnj,
          } as Mo01280Type,
          /** 長期目標 */
          longtermGoal: {
            value: item.choukiKnj,
          } as Mo01280Type,
          /** 短期目標 */
          shorttermGoal: {
            value: item.tankiKnj,
          } as Mo01280Type,
          /** シーケンス */
          seq: Number(item.seq) || 0,
          /** 更新回数 */
          modifiedCnt: item.modifiedCnt,
          /** 更新区分 */
          updateKbn: TeX0002Const.DEFAULT.UPDATE_KBN_N,
        })
      })
    } else {
      localOneway.hisotrySelectOneway.historyInfo = {
        /** アセスメントID */
        rirekiId: TeX0002Const.DEFAULT.HISTORY_ID_DEFAULT,
        /** 履歴番号 */
        krirekiNo: '1',
        /** 履歴総件数 */
        krirekiCnt: '1',
      }
      // 履歴更新回数
      historyModifiedCnt.value = ''

      // 作成者を設定
      authorInfo.value.id = systemCommonsStore.getCurrentUser.chkShokuId
      authorInfo.value.name = systemCommonsStore.getCurrentUser.shokuinKnj
      local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
      // 作成日を設定
      local.createDate.value =
        systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
      createDateOld.value = local.createDate.value

      // 課題と目標リスト
      local.issuesAndGoalsList.items.splice(0)
    }
  }

  return true
}

/**
 * 履歴選択変更処理
 *
 * @param gdlId - 履歴ID
 */
async function historySelectChange(gdlId: string) {
  if (gdlIdOld.value === gdlId) {
    // 履歴IDが変更されていない場合は何もしない
    return
  }

  // 履歴情報を再取得
  await getHistoryInfo(gdlId, TeX0002Const.DEFAULT.HISTORY_KBN_OPEN)

  // 画面情報再取得
  reload()
}

/**
 *  履歴情報を取得
 *
 * @param gdlId - 履歴ID
 *
 * @param rrkKbn - 履歴処理区分
 */
async function getHistoryInfo(gdlId: string, rrkKbn: string) {
  try {
    // バックエンドAPIから初期情報取得
    const inputData: AssessmentHomeHistoryChangeSelectInEntity = {
      /** 事業所ID */
      svJigyoId: jigyoInfo.value.svJigyoId,
      /** 利用者ID */
      userid: userId.value,
      /** 計画期間ID */
      sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id,
      /** アセスメントID */
      gdlId: gdlId,
      /** 計画期間ページ区分 */
      rrkKbn: rrkKbn,
    }
    const resData: AssessmentHomeHistoryChangeSelectOutEntity = await ScreenRepository.select(
      'assessmentHomeHistoryChangeSelect',
      inputData
    )

    // 画面情報を設定
    if (resData?.data) {
      if (resData?.data?.historyInfo?.gdlId) {
        // 履歴IDをバックアップ
        gdlIdOld.value = resData?.data?.historyInfo.gdlId

        // 表示中計画対象期間の履歴情報取得
        localOneway.hisotrySelectOneway.historyInfo = {
          /** アセスメントID */
          rirekiId: resData.data.historyInfo.gdlId,
          /** 履歴番号 */
          krirekiNo: resData.data.historyInfo.krirekiNo ?? '1',
          /** 履歴総件数 */
          krirekiCnt: resData.data.historyInfo.krirekiCnt ?? '1',
        }
        // 改定フラグ
        ninteiFormF.value = resData.data.historyInfo.ninteiFormF
        // 履歴更新回数
        historyModifiedCnt.value = resData.data.historyInfo.modifiedCnt

        // 作成者を設定
        authorInfo.value.id = resData.data.historyInfo.shokuId
        authorInfo.value.name = resData.data.historyInfo.shokuinName
        local.orX0157.value = resData.data.historyInfo.shokuinName ?? ''
        // 作成日を設定
        local.createDate.value = resData.data.historyInfo.asJisshiDateYmd ?? ''
        createDateOld.value = local.createDate.value

        // 課題と目標リスト
        local.issuesAndGoalsList.items = []
        resData?.data?.gdlKadaiList?.forEach((item) => {
          local.issuesAndGoalsList.items.push({
            /** id */
            id: uuidv4(),
            /** データID */
            dataId: item.id ?? '',
            /** アセスメントID */
            gdlId: item.gdlId,
            /** 計画期間ID */
            sc1Id: item.sc1Id,
            /** アセスメント番号 */
            assNo: item.assNo,
            /** アセスメント名 */
            assessmentName: {
              value: item.assName?.replaceAll('~r~n', '\r\n') ?? '',
            } as Mo01337OnewayType,
            /** 生活全般の解決すべき課題 */
            wholeLifeShouldSolutionIssues: {
              value: item.kadaiKnj,
            } as Mo01280Type,
            /** 長期目標 */
            longtermGoal: {
              value: item.choukiKnj,
            } as Mo01280Type,
            /** 短期目標 */
            shorttermGoal: {
              value: item.tankiKnj,
            } as Mo01280Type,
            /** シーケンス */
            seq: Number(item.seq) || 0,
            /** 更新回数 */
            modifiedCnt: item.modifiedCnt,
            /** 更新区分 */
            updateKbn: TeX0002Const.DEFAULT.UPDATE_KBN_N,
          })
        })
      } else {
        localOneway.hisotrySelectOneway.historyInfo = {
          /** アセスメントID */
          rirekiId: TeX0002Const.DEFAULT.HISTORY_ID_DEFAULT,
          /** 履歴番号 */
          krirekiNo: '1',
          /** 履歴総件数 */
          krirekiCnt: '1',
        }
        // 履歴更新回数
        historyModifiedCnt.value = ''

        // 作成者を設定
        authorInfo.value.id = systemCommonsStore.getCurrentUser.chkShokuId
        authorInfo.value.name = systemCommonsStore.getCurrentUser.shokuinKnj
        local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
        // 作成日を設定
        local.createDate.value =
          systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
        createDateOld.value = local.createDate.value

        // 課題と目標リスト
        local.issuesAndGoalsList.items.splice(0)
      }
    }
  } catch (e: unknown) {
    console.log(e)
  }
}

/**
 *  履歴情報を取得
 *
 * @param sc1Id - 計画期間ID
 *
 * @param gdlId - アセスメントID
 *
 * @param tabId - タブID
 */
async function getKadaiList(sc1Id: string, gdlId: string, tabId: string) {
  // バックエンドAPIから初期情報取得
  const inputData: KadaiMokuhyoInfoSelectInEntity = {
    /** 計画期間ID */
    sc1Id: sc1Id,
    /** アセスメントID */
    gdlId: gdlId,
    /** タブID */
    tabId: tabId,
  }
  const resData: KadaiMokuhyoInfoSelectOutEntity = await ScreenRepository.select(
    'kadaiMokuhyoInfoSelect',
    inputData
  )

  // 画面情報を設定
  if (resData?.data) {
    // 課題と目標リスト
    const issuesAndGoalsList = getIssuesAndGoalList()

    resData?.data?.gdlKadaiList?.forEach((item) => {
      issuesAndGoalsList.push({
        /** id */
        id: uuidv4(),
        /** データID */
        dataId: item.id ?? '',
        /** アセスメントID */
        gdlId: localOneway.hisotrySelectOneway?.historyInfo?.rirekiId ?? '',
        /** 計画期間ID */
        sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id ?? '',
        /** アセスメント番号 */
        assNo: item.assNo ?? '',
        /** アセスメント名 */
        assessmentName: {
          value: item.assName?.replaceAll('~r~n', '\r\n') ?? '',
        } as Mo01337OnewayType,
        /** 生活全般の解決すべき課題 */
        wholeLifeShouldSolutionIssues: {
          value: item.kadaiKnj,
        } as Mo01280Type,
        /** 長期目標 */
        longtermGoal: {
          value: item.choukiKnj,
        } as Mo01280Type,
        /** 短期目標 */
        shorttermGoal: {
          value: item.tankiKnj,
        } as Mo01280Type,
        /** シーケンス */
        seq: 0,
        /** 更新回数 */
        modifiedCnt: '',
        /** 更新区分 */
        updateKbn: TeX0002Const.DEFAULT.UPDATE_KBN_C,
      })
    })

    local.issuesAndGoalsList.items = issuesAndGoalsList
  }
}

/**
 *  データ変更チェック
 *
 * @param isTopCheck - 共通部分変更フラグ
 */
async function checkChange(isTopCheck?: boolean): Promise<number> {
  let result = 0

  // 更新区分を設定
  if (isTopCheck) {
    if (isTopEdit.value) {
      if (historyUpdateKbn.value === TeX0002Const.DEFAULT.UPDATE_KBN_N) {
        historyUpdateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_U
      }
    }
  }

  // 画面変更チェック
  if (isEdit.value || (isTopCheck ? isTopEdit.value : false)) {
    if (!permission.value.register) {
      // 画面入力変更あり、かつ保存権限がない場合
      // 確認ダイアログ表示
      openConfirmDialog2(t('message.w-com-10006'))
      // 編集を破棄して処理を続く
      result = 1
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case 'yes': {
          // 編集を保存して処理を続く
          result = 2
          break
        }
        case 'no':
          // 編集を破棄して処理を続く
          result = 1
          break
        case 'cancel':
          // キャンセル選択時は何もしない
          result = 3
          break
      }
    }
  } else {
    // 変更なしの場合は何もしないまま処理を続く
    result = 0
  }

  return result
}

/**
 * 利用者変更処理
 *
 * @param newUserId - 新しい利用者ID
 */
async function userChange(newUserId: string) {
  isLoading = true

  // 初期化の場合、チェックなしで処理を続行
  if (isInitCompleted === false) {
    userId.value = newUserId
    systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
    // 初期情報を取得
    await getInitDataInfo()

    // 画面情報再取得
    reload()

    isInitCompleted = true
    isLoading = false
    return
  }

  // 計画期間がない場合、入力変更チェックをしない
  if (!localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id) {
    userId.value = newUserId
    systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
    // 初期情報を取得
    await getInitDataInfo()

    // 画面情報再取得
    reload()

    isLoading = false
    return
  }

  // 画面変更チェック
  switch (await checkChange(true)) {
    case 0: {
      // 画面入力変更なし
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getInitDataInfo()

      // 画面情報再取得
      reload()

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getInitDataInfo()

      // 画面情報再取得
      reload()
      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存して処理を続行
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)

      eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_USER_CHANGE

      // 保存
      setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }

  isLoading = false
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
  const callBackJigyoInfo = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)

  if (!callBackJigyoInfo) {
    return
  }

  if (!jigyoInfo.value.svJigyoId) {
    jigyoInfo.value.svJigyoId = callBackJigyoInfo.svJigyoId
    jigyoInfo.value.svjigyoName = callBackJigyoInfo.jigyoRyakuKnj
    jigyoInfo.value.shisetuId = callBackJigyoInfo.shisetuId
    jigyoInfo.value.houjinId = callBackJigyoInfo.houjinId
    return
  }

  // 事業所変更がない場合、スキップ
  if (newJigyoId === jigyoInfo.value.svJigyoId) {
    return
  }

  eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_OFFICE_CHANGE

  // まず変更前の事業所を保持
  void setJigyo(jigyoInfo.value.svJigyoId)

  // 事業所変更処理
  void jigyoChange(callBackJigyoInfo)
}

/**
 * 事業所を選択
 *
 * @param jigyoId - 事業所ID
 */
async function setJigyo(jigyoId: string) {
  await nextTick()

  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    },
  })
}

/**
 * 事業所変更処理
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function jigyoChange(newJigyoInfo: {
  /** サービス事業所ID */
  svJigyoId: string
  /**事業所略称 */
  jigyoRyakuKnj: string
  /** 施設ID */
  shisetuId: string
  /** 法人ID */
  houjinId: string
}) {
  // 計画期間がない場合、入力変更チェックをしない
  if (!localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id) {
    jigyoInfo.value = {
      svJigyoId: newJigyoInfo.svJigyoId,
      svjigyoName: newJigyoInfo.jigyoRyakuKnj,
      shisetuId: newJigyoInfo.shisetuId,
      houjinId: newJigyoInfo.houjinId,
    }

    // 変更後の事業所に設定
    await setJigyo(newJigyoInfo.svJigyoId)
    // 画面情報再取得
    await getInitDataInfo()
    // 画面情報再取得
    reload()

    return
  }

  // 画面変更チェック
  switch (await checkChange(true)) {
    case 0: {
      // 画面入力変更なし
      // 変更後事業所情報を保持
      jigyoInfo.value = {
        svJigyoId: newJigyoInfo.svJigyoId,
        svjigyoName: newJigyoInfo.jigyoRyakuKnj,
        shisetuId: newJigyoInfo.shisetuId,
        houjinId: newJigyoInfo.houjinId,
      }

      // 変更後の事業所に設定
      await setJigyo(newJigyoInfo.svJigyoId)
      // 画面情報再取得
      await getInitDataInfo()
      // 画面情報再取得
      reload()

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      // 変更後事業所情報を保持
      jigyoInfo.value = {
        svJigyoId: newJigyoInfo.svJigyoId,
        svjigyoName: newJigyoInfo.jigyoRyakuKnj,
        shisetuId: newJigyoInfo.shisetuId,
        houjinId: newJigyoInfo.houjinId,
      }
      // 変更後の事業所に設定
      await setJigyo(newJigyoInfo.svJigyoId)

      // 画面情報再取得
      await getInitDataInfo()
      // 画面情報再取得
      reload()

      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存して処理を続行
      // 変更後事業所情報を保持
      jigyoInfo.value = {
        svJigyoId: newJigyoInfo.svJigyoId,
        svjigyoName: newJigyoInfo.jigyoRyakuKnj,
        shisetuId: newJigyoInfo.shisetuId,
        houjinId: newJigyoInfo.houjinId,
      }
      // 変更後の事業所に設定
      await setJigyo(newJigyoInfo.svJigyoId)

      eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_OFFICE_CHANGE

      // はい選択時は入力内容を保存する
      setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog1(paramDialogText: string): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = 'cancel' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 */
function openConfirmDialog2(paramDialogText: string) {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
async function openConfirmDialog3(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_3.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_3.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログ表示
 *
 * @param paramDialogText - メッセージ
 */
function openErrorDialog(paramDialogText: string) {
  // エラーダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
}

/**
 * 複写ボタンクリック
 *
 */
function copyBtnClick() {
  // 複写ボタンが押下された場合、複写処理を実行する
  setOr11871Event({ copyEventFlg: true })
}

/**
 * 削除ボタンクリック
 *
 */
function deleteBtnClick() {
  // 削除ボタンが押下された場合、削除処理を実行する
  setOr11871Event({ deleteEventFlg: true })
}

/**
 * 保存後処理
 *
 */
async function saveCompletedProcess() {
  const updateData = TeX0002Logic.data.get(props.uniqueCpId)?.updateData

  if (updateData?.errKbn === TeX0002Const.DEFAULT.UPDATE_ERR_KBN_1) {
    // エラー区分が「1」の場合
    openErrorDialog(t('message.e-cmn-40019'))
    return
  } else if (updateData?.errKbn === TeX0002Const.DEFAULT.UPDATE_ERR_KBN_2) {
    // エラー区分が「2」の場合 TODO メッセージ未定
    openErrorDialog(t('message.i-cmn-XXXXX'))
    return
  }

  // 新規、印刷ボタン制御
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      disabledCreateBtn: false,
      disabledPrintBtn: false,
    },
  })

  // 複写ボタン制御
  optionMenuState.value.copyBtnDisabled = false
  // 削除ボタン制御
  optionMenuState.value.deleteBtnDisabled = false

  // 画面処理区分
  switch (eventInfo.value.kbn) {
    case TeX0002Const.DEFAULT.EVENT_KBN_NORMAL:
      // 新規保存の場合、更新後の計画対象期間ID、履歴IDを再設定
      if (localOneway.hisotrySelectOneway.historyInfo) {
        if (localOneway.planningPeriodSelectOneway.planningPeriodInfo) {
          localOneway.planningPeriodSelectOneway.planningPeriodInfo.id = updateData?.sc1Id ?? ''
        }
        localOneway.hisotrySelectOneway.historyInfo.rirekiId = updateData?.gdlId ?? ''

        // 履歴情報再取得
        void getHistoryInfo(
          localOneway.hisotrySelectOneway.historyInfo?.rirekiId ?? '',
          TeX0002Const.DEFAULT.HISTORY_KBN_OPEN
        ).then(() => {
          // タブ情報再取得
          reload()
        })
      }

      break
    case TeX0002Const.DEFAULT.EVENT_KBN_CREATE:
      // 履歴件数 + 1
      historyNew()
      break
    case TeX0002Const.DEFAULT.EVENT_KBN_DELETE:
      // 初期情報を取得
      await getInitDataInfo()

      // タブ情報再取得
      reload()
      break
    case TeX0002Const.DEFAULT.EVENT_KBN_USER_CHANGE:
      // 初期情報を取得
      await getInitDataInfo()

      // タブ情報再取得
      reload()
      break
    case TeX0002Const.DEFAULT.EVENT_KBN_OFFICE_CHANGE:
      // 画面情報再取得
      await getInitDataInfo()

      // タブ情報再取得
      reload()
      break
    case TeX0002Const.DEFAULT.EVENT_KBN_PLAN_PERIOD_CHANGE: {
      const sc1Id = localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id ?? ''
      if (eventInfo.value.pageFlg === TeX0002Const.DEFAULT.PERIOD_KBN_PREV) {
        await getPlanningPeriodInfo(sc1Id, TeX0002Const.DEFAULT.PERIOD_KBN_PREV)
      } else if (eventInfo.value.pageFlg === TeX0002Const.DEFAULT.PERIOD_KBN_NEXT) {
        await getPlanningPeriodInfo(sc1Id, TeX0002Const.DEFAULT.PERIOD_KBN_NEXT)
      }

      // タブ情報再取得
      reload()
      break
    }
    case TeX0002Const.DEFAULT.EVENT_KBN_HISTORY_CHANGE: {
      const gdlId = localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id ?? ''
      if (eventInfo.value.pageFlg === TeX0002Const.DEFAULT.HISTORY_KBN_PREV) {
        await getHistoryInfo(gdlId, TeX0002Const.DEFAULT.HISTORY_KBN_PREV)
      } else if (eventInfo.value.pageFlg === TeX0002Const.DEFAULT.HISTORY_KBN_NEXT) {
        await getHistoryInfo(gdlId, TeX0002Const.DEFAULT.HISTORY_KBN_NEXT)
      }

      // タブ情報再取得
      reload()
      break
    }
    case TeX0002Const.DEFAULT.EVENT_KBN_PRINT:
      // 印刷画面を開く
      openPrintDialog()
      break
    case TeX0002Const.DEFAULT.EVENT_KBN_MASTER:
      // マスタ画面を開く
      openMasterDialog()
      break
    case TeX0002Const.DEFAULT.EVENT_KBN_TAB_CHANGE:
      // 新規保存の場合、更新後の計画対象期間ID、履歴IDを再設定
      if (localOneway.planningPeriodSelectOneway.planningPeriodInfo) {
        localOneway.planningPeriodSelectOneway.planningPeriodInfo.id = updateData?.sc1Id ?? ''
      }
      if (localOneway.hisotrySelectOneway.historyInfo) {
        localOneway.hisotrySelectOneway.historyInfo.rirekiId = updateData?.gdlId ?? ''
      }

      // 履歴情報再取得
      void getHistoryInfo(
        localOneway.hisotrySelectOneway.historyInfo?.rirekiId ?? '',
        TeX0002Const.DEFAULT.HISTORY_KBN_OPEN
      ).then(() => {
        // タブ情報再取得
        reload()
      })
      break
  }
}

/**
 * 保存処理
 *
 */
function _save() {
  // 画面入力データの変更がない場合
  // ■以下のメッセージを表示
  // i.cmn.21800
  // 画面変更チェック
  if (eventInfo.value.kbn !== TeX0002Const.DEFAULT.EVENT_KBN_DELETE && !isEdit.value) {
    openConfirmDialog2(t('message.i-cmn-21800'))
    return
  }

  if (historyUpdateKbn.value === TeX0002Const.DEFAULT.UPDATE_KBN_N) {
    historyUpdateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_U
  }

  setEvent({ saveEventFlg: true })

  eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_NORMAL
}

/**
 * 新規処理
 *
 */
async function createNew() {
  // 計画期間情報がない場合
  if (plannningPeriodManageFlg.value === TeX0002Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
    if (!localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id) {
      // 確認ダイアログ表示
      openConfirmDialog2(t('message.i-cmn-11300'))
      return
    }

    if (eventInfo.value.kbn === TeX0002Const.DEFAULT.EVENT_KBN_CREATE) {
      // 計画期間情報がある、当該画面データが保存されない場合
      // 確認ダイアログ表示
      openConfirmDialog2(t('message.i-cmn-11265', [t('label.assessment')]))

      return
    }
  }

  // 画面変更チェック
  switch (await checkChange(true)) {
    case 0: {
      // 画面入力変更なし
      // 履歴件数 + 1
      historyNew()
      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      // 履歴件数 + 1
      historyNew()

      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存
      eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_CREATE

      // 保存
      setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 * 複写処理
 *
 */
function _copy() {
  copyComponentKey.value = Date.now().toString()
  const tabItems = [] as {
    /** id */
    id: string
    /** id */
    name: string
  }[]
  localOneway.mo00043OnewayType.tabItems.forEach((item) => {
    tabItems.push({
      id: item.id,
      name: item.title,
    })
  })

  const svJigyoList = [jigyoInfo.value.svJigyoId]

  localOneway.or35672Oneway = {
    /** タブリスト */
    tabItems: tabItems,
    /** 改定フラグ */
    ninteiFormF: ninteiFormF.value,
    /** 選択中タブ */
    activeTabId: local.mo00043.id,
    /** 事業所ID */
    jigyoId: jigyoInfo.value.svJigyoId,
    /** 法人ID */
    houjinId: jigyoInfo.value.houjinId,
    /** 施設ID */
    shisetuId: jigyoInfo.value.shisetuId,
    /** 利用者ID */
    userId: userId.value,
    /** 計画対象期間ID */
    sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id,
    /** アセスメントID */
    gdlId: localOneway.hisotrySelectOneway.historyInfo?.rirekiId,
    /** 作成者ID */
    createUserId: authorInfo.value.id,
    /** 作成日 */
    createYmd: local.createDate.value,
    /** 種別ID */
    syubetsuId: syubetuId.value,
    /** アセスメント番号 */
    accessmentNo: local.mo00043.id,
    /** 適用事業所IDリスト */
    svJigyoIdList: svJigyoList,
    /** 期間管理フラグ */
    kikanFlg: plannningPeriodManageFlg.value,
  }

  // 複写画面を開く
  Or35672Logic.state.set({
    uniqueCpId: or35672.value.uniqueCpId,
    state: { isOpen: true },
  })
  setOr11871Event({ copyEventFlg: false })
}

/**
 * 印刷処理
 *
 */
async function _print() {
  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      // 印刷画面を開く
      openPrintDialog()
      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      // 印刷画面を開く
      openPrintDialog()

      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存
      eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_PRINT

      // 保存
      setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 * 印刷画面を開く
 *
 */
function openPrintDialog() {
  // セクション名
  let sectionName = ''
  // VISIOフラグ
  if (localFlg.visioFlg === TeX0002Const.DEFAULT.VISIO_FLG_VALID) {
    sectionName = t('label.assessment-home-section-name-h21-4-visio')
  } else {
    sectionName = t('label.assessment-home-section-name-h21-4')
  }

  // 帳票番号
  let prtNo = ''

  // TODO 共通情報.e-文書の履歴保存フラグ
  const eHistorySaveFlg = TeX0002Const.DEFAULT.E_DOCUMENT_SAVE_FLG
  if (eHistorySaveFlg === TeX0002Const.DEFAULT.E_DOCUMENT_SAVE_FLG) {
    prtNo = local.mo00043.id
  }

  // Or57082のダイアログ開閉状態を更新する
  Or57082Logic.state.set({
    uniqueCpId: or57082.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        kikanFlg: '',
        sectionNo: 'U06026',
        prtNo: prtNo,
        svJigyoId: jigyoInfo.value.svJigyoId,
        shisetuId: jigyoInfo.value.shisetuId,
        tantoId: '',
        syubetsuId: syubetuId.value ?? '',
        sectionName: sectionName,
        userId: userId.value,
        assessmentId: '',
        svJigyoKnj: '',
        processYmd: '',
        parentUserIdSelectDataFlag: false,
        focusSettingInitial: ['や', 'ゆ', 'よ'],
        selectedUserCounter: '2',
      } as Or57082Param,
    },
  })
}

/**
 * マスタボタンクリック処理
 *
 */
async function _master() {
  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      // マスタ画面を開く
      openMasterDialog()
      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      // マスタ画面を開く
      openMasterDialog()

      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存
      eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_PRINT

      // 保存
      setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 * マスタ画面を開く
 *
 */
function openMasterDialog() {
  // アセスメントマスタのダイアログを開く
  Or27562Logic.state.set({
    uniqueCpId: or27562.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * マスタ画面を閉じる時の処理
 *
 */
function closeMasterDialog() {
  // VISIOフラグ
  localFlg.visioFlg = useCmnRouteCom().getInitialSettingMaster()?.visioFlg ?? ''

  // 画面情報再取得
  reload()
}

/**
 * 履歴を更新
 *
 */
function historyNew() {
  // 履歴件数 + 1
  let rirekiCnt = localOneway.hisotrySelectOneway.historyInfo?.krirekiCnt
    ? parseInt(localOneway.hisotrySelectOneway.historyInfo?.krirekiCnt)
    : 0
  rirekiCnt = rirekiCnt + 1
  localOneway.hisotrySelectOneway.historyInfo = {
    rirekiId: TeX0002Const.DEFAULT.HISTORY_ID_DEFAULT,
    krirekiNo: rirekiCnt.toString(),
    krirekiCnt: rirekiCnt.toString(),
  }

  // 作成者を設定
  authorInfo.value.id = systemCommonsStore.getCurrentUser.chkShokuId
  authorInfo.value.name = systemCommonsStore.getCurrentUser.shokuinKnj
  local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''

  // 作成日を設定
  local.createDate.value =
    systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
  createDateOld.value = local.createDate.value

  // 改訂フラグ
  ninteiFormF.value = cmnRouteCom.getInitialSettingMaster()?.ninteiFlg

  // 課題と目標リストをリセット
  local.issuesAndGoalsList.items.splice(0)

  eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_CREATE

  // 更新区分を設定
  historyUpdateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_C

  void nextTick(() => {
    isTopEdit.value = false
  })

  setEvent({ createEventFlg: true })
}

/**
 * 削除処理
 *
 */
function _delete() {
  // メッセージを表示 i.cmn.11348
  let tabName = ''
  switch (local.mo00043.id) {
    case Or30732Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-1')
      break
    case Or31535Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-2')
      break
    case Or32643Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-3')
      break
    case Or00386Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-4')
      break
    case Or29958Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-5')
      break
    case Or28992Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-6-1')
      break
    case Or29241Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-6-2')
      break
    case Or29242Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-6-3-4')
      break
    case Or33097Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-6-5')
      break
    case Or30149Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-6-6')
      break
    case Or32081Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-6-7')
      break
    case Or01997Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-7-1')
      break
    case Or11131Const.DEFAULT.TAB_ID:
      tabName = t('label.assessment-home-tab-name-7-2')
      break
  }
  // メッセージ 11348
  // 現在表示している[{0}］の{1}データを削除します。よろしいですか？\r\n現在表示している画面のみ削除する。\r\n※{2}画面を削除します。\r\n表示している画面を履歴ごと削除する。\r\n※{3}までの全ての項目を削除します。
  const msg11348 = t('message.i-cmn-11348', [
    local.createDate.value,
    t('label.assessment'),
    tabName,
    '1~7' + t('label.schedule-k'),
  ])
  const msgList = msg11348.split(/[\r\n]+/)
  if (msgList.length > 2) {
    localOneway.orX0001Oneway.createYmd = local.createDate.value
    localOneway.orX0001Oneway.kinouKnj = t('label.assessment')
    localOneway.orX0001Oneway.selectTabName = tabName
    localOneway.orX0001Oneway.startTabName = '1'
    localOneway.orX0001Oneway.endTabName = '7' + t('label.schedule-k')

    // OrX0001のダイアログ開閉状態を更新する
    OrX0001Logic.state.set({
      uniqueCpId: orX0001.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * 作成日変更処理
 *
 * @param mo00020 - 作成日
 */
async function createDateChange(mo00020: Mo00020Type) {
  if (mo00020.value === createDateOld.value) {
    // 作成日が変更されていない場合は何もしない
    return
  }

  if (createDateOld.value === '' || mo00020.value === '') {
    // 履歴に作成日がない、または入力されない場合は何もしない
    return
  }

  // H21/4改訂版（改訂フラグ＝4）AND  作成日が2018年3月31日以前（平成30年区分=0）AND 入力した作成日>= '2018/04/01'の場合
  if (
    ninteiFormF.value === TeX0002Const.DEFAULT.KAITEI_FLG_H21 &&
    parseInt(createDateOld.value.replaceAll('/', '')) < 20180331 &&
    parseInt(mo00020.value.replaceAll('/', '')) >= 20180401
  ) {
    let dialogResult = ''
    if (localOneway.hisotrySelectOneway.historyInfo?.rirekiId) {
      // アセスメントIDが存在する場合
      dialogResult = await openConfirmDialog3(t('message.i-cmn-11324'))
    } else {
      // アセスメントIDが存在しない場合
      dialogResult = await openConfirmDialog3(t('message.i-cmn-11325'))
    }
    switch (dialogResult) {
      case 'yes': {
        createDateOld.value = mo00020.value
        // はい選択時は入力内容を保存する
        if (localOneway.hisotrySelectOneway.historyInfo?.rirekiId) {
          // アセスメントIDが存在する場合、保存
          setEvent({ saveEventFlg: true })
        } else {
          // アセスメントIDが存在しない場合、タブが「1」、「2」、「3」、「4」、「5」、「6」、「8」、「12」、「13」の場合、タブの初期処理を実行する
          if (
            local.mo00043.id === Or30732Const.DEFAULT.TAB_ID ||
            local.mo00043.id === Or31535Const.DEFAULT.TAB_ID ||
            local.mo00043.id === Or32643Const.DEFAULT.TAB_ID ||
            local.mo00043.id === Or00386Const.DEFAULT.TAB_ID ||
            local.mo00043.id === Or29958Const.DEFAULT.TAB_ID ||
            local.mo00043.id === Or28992Const.DEFAULT.TAB_ID ||
            local.mo00043.id === Or29242Const.DEFAULT.TAB_ID ||
            local.mo00043.id === Or01997Const.DEFAULT.TAB_ID ||
            local.mo00043.id === Or11131Const.DEFAULT.TAB_ID
          ) {
            reload()
          }
        }

        // 作成日変更処理
        setEvent({ isCreateDateChanged: true })
        break
      }
      case 'no': {
        // いいえ選択時入力前の値に戻る
        mo00020.value = createDateOld.value
        break
      }
    }
  }

  // H21/４改訂版（改訂フラグ＝4）AND  作成日が2018年4月1日以降（平成30年区分=1）AND 入力した作成日< '2018/04/01'の場合
  if (
    ninteiFormF.value === TeX0002Const.DEFAULT.KAITEI_FLG_H21 &&
    parseInt(createDateOld.value.replaceAll('/', '')) >= 20180401 &&
    parseInt(mo00020.value.replaceAll('/', '')) < 20180401
  ) {
    // 確認ダイアログ表示
    const dialogResult = await openConfirmDialog3(t('message.i-cmn-11324'))
    switch (dialogResult) {
      case 'yes': {
        createDateOld.value = mo00020.value
        // はい選択時は入力内容を保存する
        if (isEdit.value) {
          setEvent({ saveEventFlg: true })
        }

        // 作成日変更処理
        setEvent({ isCreateDateChanged: true })
        break
      }
      case 'no': {
        // いいえ選択時入力前の値に戻る
        mo00020.value = createDateOld.value
        break
      }
    }
  }
}

/**
 * 計画対象期間選択開くボタンクリック
 *
 */
async function planningPeriodSelectClick() {
  localOneway.orX0115Oneway.kindId = syubetuId.value
  localOneway.orX0115Oneway.sc1Id =
    localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id ?? ''

  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      // 計画対象期間選択画面を開く
      OrX0115Logic.state.set({
        uniqueCpId: orX0115.value.uniqueCpId,
        state: { isOpen: true },
      })

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      // 計画対象期間選択画面を開く
      OrX0115Logic.state.set({
        uniqueCpId: orX0115.value.uniqueCpId,
        state: { isOpen: true },
      })

      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存
      // 計画対象期間選択画面を開く
      OrX0115Logic.state.set({
        uniqueCpId: orX0115.value.uniqueCpId,
        state: { isOpen: true },
      })

      // 保存
      setEvent({ saveOnlyEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 * 履歴選択開くボタンクリック
 *
 */
async function historySelectClick() {
  // 履歴選択画面パラメータを作成
  localOneway.or28326Oneway = {
    /**事業者ID*/
    svJigyoId: jigyoInfo.value.svJigyoId,
    /**計画期間ID*/
    sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id ?? '',
    /**利用者ID*/
    userId: userId.value,
    /**履歴ID*/
    ks21Id: localOneway.hisotrySelectOneway.historyInfo?.rirekiId ?? '',
  }
  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      // 履歴選択画面を開く
      Or28326Logic.state.set({
        uniqueCpId: or28326.value.uniqueCpId,
        state: { isOpen: true },
      })

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      // 履歴選択画面を開く
      Or28326Logic.state.set({
        uniqueCpId: or28326.value.uniqueCpId,
        state: { isOpen: true },
      })

      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存
      // 履歴選択画面を開く
      Or28326Logic.state.set({
        uniqueCpId: or28326.value.uniqueCpId,
        state: { isOpen: true },
      })

      // 保存
      setEvent({ saveOnlyEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 * 作成者選択画面クリック
 *
 */
function authorSelectClick() {
  // 事業所ID
  localOneway.or26257Oneway.defSvJigyoId = jigyoInfo.value.svJigyoId

  // 選択画面を開く
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * タブ変更処理
 *
 * @param mo00043 - タブ情報
 */
async function tabChange(mo00043: Mo00043Type) {
  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      local.mo00043.id = mo00043.id

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      local.mo00043.id = mo00043.id
      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存して処理を続行
      eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_TAB_CHANGE

      // 保存
      setEvent({ saveEventFlg: true })
      tabIdBk.value = mo00043.id
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 * 削除ダイアログ選択変更処理
 *
 * @param orX0001Value - 戻り値
 */
function deleteDialogChange(orX0001Value: OrX0001Type) {
  if ('0' !== orX0001Value.deleteSyubetsu) {
    // 新規、印刷ボタン制御
    Or11871Logic.state.set({
      uniqueCpId: or11871.value.uniqueCpId,
      state: {
        disabledCreateBtn: true,
        disabledPrintBtn: true,
      },
    })

    // 複写ボタン制御
    optionMenuState.value.copyBtnDisabled = true
    // 削除ボタン制御
    optionMenuState.value.deleteBtnDisabled = true

    // 確定ボタン押下の場合
    deleteKbn.value = orX0001Value.deleteSyubetsu
    // 削除実施後、作成者選択アイコンボタンを非活性
    if (localOneway.orX0157Oneway.text) {
      localOneway.orX0157Oneway.text.orX0157InputOneway.disabled = true
    }
    // 削除実施後、作成日を非活性
    localOneway.createDateOneway.disabled = true

    eventInfo.value.kbn = TeX0002Const.DEFAULT.EVENT_KBN_DELETE

    // 更新区分を設定
    historyUpdateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_D

    // アセスメント(インターライ)画面履歴の最新情報を取得する
    switch (local.mo00043.id) {
      case Or30732Const.DEFAULT.TAB_ID:
        setEvent({ deleteEventFlg: true })
        break
      case Or31535Const.DEFAULT.TAB_ID:
        break
      case Or32643Const.DEFAULT.TAB_ID:
        break
      case '4':
        break
      case Or29958Const.DEFAULT.TAB_ID:
        break
      case Or28992Const.DEFAULT.TAB_ID:
        setEvent({ deleteEventFlg: true })
        break
      case Or29241Const.DEFAULT.TAB_ID:
        break
      case Or29242Const.DEFAULT.TAB_ID:
        break
      case Or33097Const.DEFAULT.TAB_ID:
        break
      case Or30149Const.DEFAULT.TAB_ID:
        break
      case '11':
        break
      case Or01997Const.DEFAULT.TAB_ID:
        break
      case '13':
        break
    }
  } else {
    // キャンセル
    // 何もしない
  }
}

function tabMounted() {
  setTimeout(() => {
    reload()
  }, 0)
}
</script>

<template>
  <c-v-sheet class="common-layout">
    <!-- 操作ボタンエリア -->
    <div class="sticky-header">
      <g-base-or11871 v-bind="or11871">
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            prepend-icon="file_copy"
            :disabled="optionMenuState.copyBtnDisabled"
            @click="copyBtnClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="t('tooltip.duplicate')"
            ></c-v-tooltip>
          </c-v-list-item>
        </template>
        <template #optionMenuItems>
          <c-v-list-item
            :title="t('btn.delete')"
            prepend-icon="delete"
            :disabled="optionMenuState.deleteBtnDisabled"
            @click="deleteBtnClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="t('tooltip.delete')"
            ></c-v-tooltip>
          </c-v-list-item>
        </template>
      </g-base-or11871>
    </div>

    <c-v-row
      no-gutters
      class="main-Content d-flex flex-0-1 h-100 overflow-hidden"
    >
      <!-- ナビゲーションエリア -->
      <c-v-col
        cols="auto"
        class="hidden-scroll h-100 pl-3 user-select"
      >
        <!-- 利用者選択一覧 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <!-- コンテンツエリア -->
      <c-v-col class="main-right d-flex flex-column overflow-hidden h-100">
        <!-- 上段 -->
        <c-v-row no-gutters>
          <c-v-col>
            <c-v-row
              no-gutters
              class="form-content"
            >
              <c-v-col cols="auto common-select pl-6">
                <!-- 事業所選択画面 -->
                <g-base-or-41179 v-bind="or41179" />
              </c-v-col>
              <c-v-col
                v-show="plannningPeriodManageFlg === TeX0002Const.DEFAULT.PLANNING_PERIOD_MANAGE"
                cols="auto common-select"
              >
                <!-- 計画対象期間 -->
                <g-custom-or13844
                  v-bind="or13844"
                  :oneway-model-value="localOneway.planningPeriodSelectOneway"
                  @open-btn-click="planningPeriodSelectClick"
                ></g-custom-or13844>
                <!-- 計画対象期間選択画面 -->
                <g-custom-or-x-0115
                  v-if="showDialogOrX0115"
                  v-bind="orX0115"
                  :oneway-model-value="localOneway.orX0115Oneway"
                />
              </c-v-col>

              <c-v-col
                v-show="isHistoryShow"
                cols="auto common-select"
              >
                <!-- 作成日 -->
                <base-mo00020
                  v-model="local.createDate"
                  :oneway-model-value="localOneway.createDateOneway"
                  @update:model-value="createDateChange"
                />
              </c-v-col>
              <c-v-col
                v-show="isHistoryShow"
                cols="auto common-select"
              >
                <!-- 作成者 -->
                <g-custom-or-x-0157
                  v-model="local.orX0157"
                  :oneway-model-value="localOneway.orX0157Oneway"
                  @on-click-edit-btn="authorSelectClick"
                />
                <!-- 職員検索画面 -->
                <g-custom-or-26257
                  v-if="showDialogOr26257"
                  v-bind="or26257"
                  v-model="local.or26257"
                  :oneway-model-value="localOneway.or26257Oneway"
                />
              </c-v-col>
              <c-v-col
                v-show="isHistoryShow"
                cols="auto common-select"
              >
                <!-- 履歴 -->
                <g-custom-or13872
                  v-bind="or13872"
                  :oneway-model-value="localOneway.hisotrySelectOneway"
                  @open-btn-click="historySelectClick"
                ></g-custom-or13872>
                <!-- 履歴選択画面 -->
                <g-custom-or-28326
                  v-if="showDialogOr28326"
                  v-bind="or28326"
                  v-model="local.or28326"
                  :oneway-model-value="localOneway.or28326Oneway"
                  @update:model-value="historySelectChange"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row no-gutters>
              <c-v-col>
                <base-mo00043
                  :model-value="local.mo00043"
                  :oneway-model-value="localOneway.mo00043OnewayType"
                  @update:model-value="tabChange"
                >
                </base-mo00043>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <!-- 中段 -->

        <c-v-row
          no-gutters
          class="middleContent flex-1-1 h-100"
        >
          <c-v-window
            v-show="
              (plannningPeriodManageFlg === TeX0002Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
                localOneway.planningPeriodSelectOneway?.planningPeriodInfo?.id !== '') ||
              plannningPeriodManageFlg === TeX0002Const.DEFAULT.PLANNING_PERIOD_NO_MANAGE
            "
            id="tabWindow"
            v-model="local.mo00043.id"
            class="h-100"
          >
            <!-- タブ1 -->
            <c-v-window-item
              id="assessmenthome1"
              class="h-100"
              :value="Or30732Const.DEFAULT.TAB_ID"
            >
              <g-custom-or-30732
                v-bind="or30732"
                :oneway-model-value="localOneway.or30732Oneway"
                :parent-unique-cp-id="props.uniqueCpId"
              />
            </c-v-window-item>
            <!-- タブ2 -->
            <!-- <c-v-window-item
              id="assessmenthome2"
              :value="Or31535Const.DEFAULT.TAB_ID"
              class="h-100"
            >
              <g-custom-or-31535
                v-bind="or31535"
                :oneway-model-value="localOneway.or31535Oneway"
                :parent-unique-cp-id="props.uniqueCpId"
                @vue:mounted="tabMounted"
              />
            </c-v-window-item> -->
            <!-- タブ3 -->
            <!-- <c-v-window-item
              id="assessmenthome3"
              :value="Or32643Const.DEFAULT.TAB_ID"
              class="h-100"
            >
              <g-custom-or-32643
                v-bind="or32643"
                :oneway-model-value="localOneway.or32643Oneway"
                :parent-unique-cp-id="props.uniqueCpId"
              />
            </c-v-window-item> -->
            <!-- タブ4 -->
            <!-- <c-v-window-item
              id="assessmenthome4"
              :value="Or00386Const.DEFAULT.TAB_ID"
              class="h-100"
            >
              <g-custom-or-00386
                v-bind="or00386"
                :oneway-model-value="localOneway.or00386Oneway"
              />
            </c-v-window-item> -->
            <!-- タブ5 -->
            <!-- <c-v-window-item
              id="assessmenthome5"
              :value="Or29958Const.DEFAULT.TAB_ID"
              class="h-100"
            >
              <g-custom-or-29958
                v-bind="or29958"
                :oneway-model-value="localOneway.or29958Oneway"
                :parent-unique-cp-id="props.uniqueCpId"
                @vue:mounted="tabMounted"
              />
            </c-v-window-item> -->
            <!-- タブ6① -->
            <!-- <c-v-window-item
              id="assessmenthome6-1"
              :value="Or28992Const.DEFAULT.TAB_ID"
              class="h-100 overflow-hidden"
            >
              <g-custom-or-28992
                v-bind="or28992"
                :oneway-model-value="localOneway.or28992Oneway"
                :parent-unique-cp-id="props.uniqueCpId"
                @vue:mounted="tabMounted"
              ></g-custom-or-28992>
            </c-v-window-item> -->
            <!-- タブ6② -->
            <!-- <c-v-window-item
              :value="Or29241Const.DEFAULT.TAB_ID"
              class="h-100"
            >
              <g-custom-or-29241
                v-bind="or29241"
                :oneway-model-value="localOneway.or29241Oneway"
              />
            </c-v-window-item> -->
            <!-- タブ6➂-④ -->
            <!-- <c-v-window-item
              :value="Or29242Const.DEFAULT.TAB_ID"
              class="h-100"
            >
              <g-custom-or-29242
                v-bind="or29242"
                :oneway-model-value="localOneway.or29242Oneway"
              />
            </c-v-window-item> -->
            <!-- タブ6⑤ -->
            <!-- <c-v-window-item
              id="assessmenthome6-5"
              :value="Or33097Const.DEFAULT.TAB_ID"
              class="h-100"
            >
              <g-custom-or-33097
                v-bind="or33097"
                :oneway-model-value="localOneway.or33097Oneway"
                :parent-unique-cp-id="props.uniqueCpId"
              ></g-custom-or-33097>
            </c-v-window-item> -->
            <!-- タブ6⑥ -->
            <!-- <c-v-window-item
              id="assessmenthome6-6"
              :value="Or30149Const.DEFAULT.TAB_ID"
              class="h-100"
            >
              <g-custom-or-30149
                v-bind="or30149"
                :oneway-model-value="localOneway.or30149Oneway"
                :parent-unique-cp-id="props.uniqueCpId"
                @vue:mounted="tabMounted"
              ></g-custom-or-30149>
            </c-v-window-item> -->
            <!-- タブ6医 -->
            <!-- <c-v-window-item
              id="assessmenthome6-7"
              :value="Or32081Const.DEFAULT.TAB_ID"
              class="h-100"
            >
              <g-custom-or-32081
                v-bind="or32081"
                :oneway-model-value="localOneway.or32081Oneway"
                @vue:mounted="tabMounted"
              />
            </c-v-window-item> -->
            <!-- タブ7まとめ -->
            <!-- <c-v-window-item
              :value="Or01997Const.DEFAULT.TAB_ID"
              class="h-100"
            >
              <g-custom-or-01997 v-bind="or01997" />
            </c-v-window-item>
            <c-v-window-item
              id="assessmenthome7-2"
              :value="Or11131Const.DEFAULT.TAB_ID"
              class="h-100 overflow-y-auto"
            >
              <g-custom-or-11131
                v-bind="or11131"
                :oneway-model-value="localOneway.or11131Oneway"
              ></g-custom-or-11131
            ></c-v-window-item> -->
          </c-v-window>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <g-custom-or-35672
      v-if="showDialogOr35672"
      v-bind="or35672"
      :key="copyComponentKey"
      :oneway-model-value="localOneway.or35672Oneway"
      :parent-unique-cp-id="props.uniqueCpId"
    />
    <!-- 印刷画面 -->
    <g-custom-or-57082
      v-if="showDialogOr57082"
      v-bind="or57082"
    />
    <!-- アセスメントマスタ画面 -->
    <g-custom-or-27562
      v-if="showDialogOr27562"
      v-bind="or27562"
      :oneway-model-value="localOneway.or27562Oneway"
      @on-click-close="closeMasterDialog"
    />
    <!-- Or21813:有機体:エラーダイアログ -->
    <g-base-or21813 v-bind="or21813" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_1" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_2" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_3" />
    <!--  削除確認画面 -->
    <g-custom-or-x-0001
      v-if="showDialogOrX0001"
      v-bind="orX0001"
      v-model="local.orX0001Type"
      :oneway-model-value="localOneway.orX0001Oneway"
      @update:model-value="deleteDialogChange"
    ></g-custom-or-x-0001>
  </c-v-sheet>
</template>

<style scoped lang="scss">
$margin-organism: 24px; // オーガニズム間のマージン
$margin-common: 48px; // 一般的なマージン

.main-Content {
  .main-left {
    max-width: 20%;
  }

  .main-right {
    .middleContent {
      min-height: 0;

      .v-window {
        width: 100%;
      }
    }
  }
}

.background-transparent {
  background-color: transparent !important;
}

:deep(.v-input .v-input__control .v-field--disabled) {
  background-color: rgb(var(--v-theme-secondaryBackground)) !important;
}

.common-layout {
  // 背景色：テーマの背景色を使用
  background-color: rgb(var(--v-theme-background)) !important;
  // 幅：内容で広く
  min-height: max-content !important;
  display: flex;
  flex-direction: column;
  // 画面トップの固定ヘッダー
  .sticky-header {
    position: sticky; // スクロール時に固定
    top: $margin-common; // トップからの距離
    z-index: 999; // 他の要素よりも上に表示
    background: rgb(var(--v-theme-background)); // テーマの背景色
    width: 100%; // 全幅
  }
}
// 利用者一覧選択領域領域
:deep(.user-select > .v-card) {
  padding: 0px 0px 0px 0px !important; // パディング設定
  width: max-content !important; // 幅を自動調整
  margin-top: 16px !important; // 上部マージン
  // パンディング設定
  .v-card-actions {
    width: max-content !important; // 幅を自動調整
    padding: 8px !important; // パディング設定
  }
  // 選択領域の幅設定
  .user-info-section-container-style {
    width: 260px; // 幅を260pxに設定
  }
}
/***************************************************************
 * 分子：「Mo00043_タブ選択」スタイル 補足
 ***************************************************************/
:deep(.v-slide-group) {
  height: 36px !important; // 高さを36pxに設定
  padding: 0px 24px !important; // パディングを解除
  margin-top: $margin-organism; // 上部マージン
  margin-bottom: $margin-organism; // 下部マージン
}
// 選択タブアイテム設定
:deep(.v-slide-group__content > .v-tab) {
  height: 36px !important; // 高さを36pxに設定
  padding: 0px 30px !important; // パディング設定
  width: max-content !important; // 幅を自動調整
  min-width: 44px !important; // 最小幅を自動調整
}
.form-content {
  display: flex; // フレキシブルボックス
  flex-wrap: nowrap; // 子要素を改行せずに横に並べる
  // 事業所等々画面共通領域のパンディング
  .common-select {
    &:not(:last-child) {
      margin-right: $margin-organism; // 最後の要素以外は右に $margin-organismのマージン
    }
  }
  // 作成日スタイル
  .createDateOuterClass {
    width: auto !important; // 幅を自動調整
    background: rgb(var(--v-theme-background)); // テーマの背景色

    :deep(.must-badge) {
      margin-left: unset; // 左側のマージンを解除
    }

    :deep(.v-input__details) {
      display: none; // 詳細情報を非表示
    }
    :deep(.v-input__control) {
      background-color: #fff; // 白色の背景
    }
  }
}
// サブタイトルコンテンツ
:deep(*.c-sub-title) {
  padding: 0px $margin-organism; // パディングを設定
  display: flex; // フレキシブルボックスを使用
  flex-wrap: nowrap; // 子要素を改行せずに横に並べる
  white-space: pre; // 空白文字を保持する
  align-items: center; // 垂直方向の中央揃え
  width: 100%; // 全幅
  font-weight: bolder; // 太字
  font-size: 16px; // フォントサイズ
  line-height: $margin-common; // 行間
  background-color: rgb(230, 230, 230); // 背景色
  box-sizing: border-box !important; // ボックスサイズをborder-boxに設定
  border: rgba(0, 0, 0, 0.1) 1px solid;
  &.action {
    background-color: rgba(7, 96, 230, 0.08);
    border: rgb(197, 214, 229) 1px solid;
    span {
      color: rgba(33, 77, 151, 1);
      text-decoration: underline;
      cursor: pointer;
    }
  }
}

// タブ情報入力領域
:deep(*.c-sub-content) {
  padding: $margin-organism $margin-common; // パディング設定
  width: 100% !important; // 全幅
  background-color: #fff; // 白色の背景
  // 領域内有機体ボックススタイル
  *.or-content {
    display: flex; // フレキシブルボックスを使用
    flex-wrap: nowrap; // 子要素を改行せずに横に並べる
    &:not(:last-child) {
      margin-bottom: 16px; // 最後の要素以外は下に16pxのマージン
    }
    &.show-border {
      border-bottom: rgba(0, 0, 0, 0.12) 1px solid;
    }
  }
}

/***************************************************************
 * 分子：「Mo00039_ラジオボタングループ」スタイル 補足
 ***************************************************************/
:deep(.v-selection-control .v-label) {
  margin-left: 8px !important;
}

:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 24px !important;
}

:deep(.v-radio:not(:last-child)) {
  margin-right: $margin-organism !important;
}
</style>
