<script setup lang="ts">
/**
 * GUI00923_課題取込画面
 *
 * @description
 * 課題取込画面
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or27604Logic } from '~/components/custom-components/organisms/Or27604/Or27604.logic'
import type { Or27604OnewayType, Or27604Type } from '~/types/cmn/business/components/Or27604Type'
import { Or27604Const } from '~/components/custom-components/organisms/Or27604/Or27604.constants'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * 課題取込画面
 * KMD NGUYEN MANH HUNG 2025/06/06 ADD START
 **************************************************/
/**
 * 画面ID
 */
const screenId = 'GUI00923'
/**
 * ルーティング
 */
const routing = 'GUI00923/pinia'
/**
 * 画面物理名
 */
const screenName = 'GUI00923'

/**
 * 画面状態管理用操作変数
 */
const screenStore = useScreenStore()

/**
 * or27604
 */
const or27604 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
/**
 *  piniaの画面領域を初期化する
 */
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00923' },
})

/**************************************************
 * Props
 **************************************************/
/**
 * piniaから最上位の画面コンポーネント情報を取得する
 * これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
}
// 子コンポーネントのユニークIDを設定する
or27604.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00923',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27604Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27604Const.CP_ID(1)]: or27604.value,
})

/**************************************************
 * Props
 **************************************************/

/**
 *  ダイアログ表示フラグ
 */
const showDialogOr27604 = computed(() => {
  // Or27604のダイアログ開閉状態
  return Or27604Logic.state.get(or27604.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * コンポーネント固有処理
 */
const or27604Data: Or27604OnewayType = {
  /** 事業者ID */
  svJigyoId: '147',
  /** 利用者ID */
  userId: '775',
  /** 種別ID */
  syubetsuId: '2',
  /** 施設ID */
  shisetuId: '33',
  /** 期間管理フラグ */
  kanriFlg: '1',
  /** 期間ID */
  sc1Id: '2',
}

/**
 * Or27604Type
 */
const or27604Type = ref<Or27604Type[]>([])

/**
 *  ボタン押下時の処理
 *
 * @param manageflag - string
 */
function Or27604OnClick(manageflag: string) {
  // 親画面.期間管理フラグを設定する。
  or27604Data.kanriFlg = manageflag
  // Or27604のダイアログ開閉状態を更新する
  Or27604Logic.state.set({
    uniqueCpId: or27604.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**************************************************
 * 課題取込画面
 * KMD NGUYEN MANH HUNG 2025/06/06 ADD END
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >モックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- GUI00923_課題取込画面 KMD NGUYEN MANH HUNG 2025/06/06 ADD START -->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="Or27604OnClick('1')"
        >1. 初期表示(共通情報.計画期間管理フラグ が 「管理する」の場合)1 !
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="Or27604OnClick('0')"
        >2. 初期表示(共通情報.計画期間管理フラグ が 「管理しない」の場合)0 !
      </v-btn>
    </c-v-col>
    <g-custom-or-27604
      v-if="showDialogOr27604"
      v-bind="or27604"
      v-model="or27604Type"
      :oneway-model-value="or27604Data"
    />
  </c-v-row>
  <!-- GUI00923_課題取込画面 KMD NGUYEN MANH HUNG 2025/06/06 ADD END-->
</template>
