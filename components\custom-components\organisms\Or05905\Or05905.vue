<script setup lang="ts">
/**
 * Or05905: (選定表(アセスメント(インターライ)))選定表
 * GUI00848: 選定表(アセスメント(インターライ))
 *
 * @description
 * (選定表(アセスメント(インターライ)))選定表
 *
 * <AUTHOR> DAM XUAN HIEU
 */
import { reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or05905Const } from './Or05905.constants'
import type { cpnTucRaiList } from './Or05905.type'
import { useScreenTwoWayBind } from '#imports'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { OrCpGroupDefinitionInputFormDeleteDialogOnewayType } from '~/types/cmn/business/generator-components/OrCpGroupDefinitionInputFormDeleteDialog'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: {
    surveyAssessmentKind: string
  }
  uniqueCpId: string
}

// 引継情報を取得する
const props: Props = defineProps<Props>()
const emit = defineEmits(['reload'])

// i18nの翻訳関数
const { t } = useI18n()
const { refValue: cpnTucRaiListData } = useScreenTwoWayBind<cpnTucRaiList>({
  cpId: Or05905Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// フッターの注釈データを定義
const footer = reactive({
  annotation: {
    value: t('label.footer-annotation'),
    customClass: new CustomClass({
      outerClass: 'm-0 pa-2',
      labelClass: '',
      itemClass: '',
    }),
  } as Mo01338OnewayType,
})

const defaultModelValue = {
  assessmentKindList: [] as CodeType[],
}

const defaultOneway = reactive({
  mo01338SurveyAssessmentTypeOneway: {
    value: '',
    customClass: {
      outerClass: 'mr-4',
      outerStyle: 'background: transparent',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentTitle',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // General
  mo00039Oneway: {
    name: '',
    itemLabel: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
})
const local = reactive({
  officeId: '',
  planPeriodId: '',
  historyId: '',
  // General
  msgDialog10180: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
  msgDialogOneway41717: {
    msg: t('message.e-cmn-41717'),
    btnNoDisplay: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '360px',
    } as Mo00024OnewayType,
  } as OrCpGroupDefinitionInputFormDeleteDialogOnewayType,
})

const assessmentKind = ref<number>(1)
// テーブルラベルを設定する
const tableLabel = computed(() =>
  assessmentKind.value === 1
    ? t('label.interRai-method-cap-selection-table-type-1')
    : assessmentKind.value === 2
      ? t('label.interRai-method-cap-selection-table-type-2')
      : assessmentKind.value === 3
        ? t('label.interRai-method-cap-selection-table-type-3')
        : t('label.interRai-method-cap-selection-table-type-0')
)

// msgDialog10180のemitTypeを監視し、選択された値に応じて処理を実行する処理
watch(
  () => local.msgDialog10180.emitType,
  (newValue) => {
    if (!newValue) return
    // はい
  }
)
const mo00609ReloadOneWay = ref({
  btnLabel: t('再選定'),
  color: '#fff',
  labelColor: '#767676',
  minWidth: '65px',
})
</script>

<template>
  <div class="mt-5 ml-5 label-title">
    {{ tableLabel }}
    <base-mo00609
      @click="emit('reload')"
      class="btn-reload"
      :oneway-model-value="mo00609ReloadOneWay"
    />
  </div>
  <div class="ml-5 mt-4 pa-6 selection-table-container">
    <g-custom-or05906 :cpn-tuc-rai-list-data="cpnTucRaiListData" />
    <g-custom-or05907
      class="mt-4"
      :cpn-tuc-rai-list-data="cpnTucRaiListData"
    />
    <c-v-row class="row-footer">
      <c-v-col
        cols="12"
        class="px-2 py-0 footer"
      >
        <base-mo01338
          :oneway-model-value="footer.annotation"
          class="annotation"
        />
        <c-v-img
          width="128"
          aspect-ratio="16/9"
          cover
          :src="InterRAI"
          style="float: right"
          class="px-2"
        />
      </c-v-col>
    </c-v-row>
  </div>
</template>
<style scoped lang="scss">
.btn-reload {
  border: 1px solid #767676;
}
.label-title {
  display: flex;
  justify-content: space-between;
  width: 807px !important;
  font-size: 24px;
  font-weight: 400;
}
.selection-table-container {
  width: 807px !important;
  background-color: white;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 0px 4px 4px 0px #00000014;
}
.row-footer {
  padding-bottom: 112px;
  .annotation {
    background-color: transparent !important;
  }
}
</style>
