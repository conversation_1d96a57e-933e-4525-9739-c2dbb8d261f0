<script setup lang="ts">
import { watch } from 'vue';
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or27734Const } from '~/components/custom-components/organisms/Or27734/Or27734.constants'
import type { Or27734Type } from '~/types/cmn/business/components/Or27734Type'
import { Or27734Logic } from '~/components/custom-components/organisms/Or27734/Or27734.logic'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01049'
// ルーティング
const routing = 'GUI01049/pinia'
// 画面物理名
const screenName = 'GUI01049'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27734 = ref({ uniqueCpId: Or27734Const.CP_ID(1) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01049' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27734Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27734.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01049',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27734Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27734Const.CP_ID(1)]: or27734.value,
})

const or27734Type = ref<Or27734Type>({
  //選択された：サービス事業者ID
  serviceOfficeId: '',
  houjinId: '1'
})

// ダイアログ表示フラグ
const showDialogOr27734 = computed(() => {
  // Or27734のダイアログ開閉状態
  return Or27734Logic.state.get(or27734.value.uniqueCpId)?.isOpen ?? false
})

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 法人ID
  houjinId: { value: '' } as Mo00045Type,
  // 事業者ID
  serviceOfficeId: { value: '' } as Mo00045Type,
})


watch(
  or27734Type,
  () => {
    local.serviceOfficeId.value =  or27734Type.value.serviceOfficeId
  },
  { deep: true }
);
/**
 *
 *  ボタン押下時の処理(Or27734)
 */
function onClickOr27734() {
  // Or27734のダイアログ開閉状態を更新する
  Or27734Logic.state.set({
    uniqueCpId: or27734.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/** GUI01049 疎通起動  */
function onClick() {
  or27734Type.value.houjinId = local.houjinId.value
  Or27734Logic.state.set({
    uniqueCpId: or27734.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const jsonString = computed(() => {
  return JSON.stringify(or27734Type.value, null, 2);
});
</script>

<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27734"
        >GUI01049_居宅介護支援事業者選択
      </v-btn>
      <g-custom-or-27734
        v-if="showDialogOr27734"
        v-bind="or27734"
        v-model="or27734Type"
      />
    </c-v-col>
  </c-v-row>
    <c-v-row no-gutters>
      <c-v-col>
        <v-btn variant="plain"
          >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
        </v-btn>
      </c-v-col>
    </c-v-row>
    <div style="margin-left: 20px">法人ID</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.houjinId"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div class="pt-5 w-25 pl-5">
      <v-btn @click="onClick"> GUI01049 疎通起動 </v-btn>
    </div>
    <c-v-row no-gutters>
      <c-v-col>
        <v-btn variant="plain"
          >-----------------------------------------------------------------------------------------------------------------↓ 戻り値--------------------------------------------------------------------------------------------------
        </v-btn>
      </c-v-col>
    </c-v-row>
    <div style="margin-left: 20px">サービス事業者ID</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.serviceOfficeId"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
      <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↓前画面へ返却値--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
    <div>
      <pre>{{ jsonString }}</pre>
    </div>
</template>
