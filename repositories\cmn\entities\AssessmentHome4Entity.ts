import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'
import type { IReportInEntity } from '~/types/business/core/ReportRepositoryType'

/**
 * Or00386:有機体:［アセスメント］画面（居宅）（4）
 * GUI00797_［アセスメント］画面（居宅）（4）
 *
 * @description
 * ［アセスメント］画面（居宅）（4）
 *
 * <AUTHOR>
 */

/**
 * 初期情報取得API入力エンティティ
 */
export interface assessmentHomeTab4SelectInEntity extends InWebEntity {
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * アセスメントID
   */
  gdlId: string
  /**
   * 改定フラグ
   */
  ninteiFormF: string
  /**
   * 作成日
   */
  kijunbiYmd: string
}

/**
 * 初期情報取得API出力エンティティ
 */
export interface assessmentHomeTab4SelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 住宅等の状況（Ｈ３０改訂）情報
     */
    gdl4HouH30Info: Gdl4HouH30Info

    /**
     * 住宅等の状況（Ｈ２１改訂）情報
     */
    gdl4HouH21Info: Gdl4HouH21Info

    /**
     * 住宅等の状況（R3/4改訂）情報
     */
    gdl5HouInfo: Gdl5HouInfo
  }
}

/**
 * 住宅等の状況（Ｈ３０改訂）情報
 */
export interface Gdl4HouH30Info {
  /**   アセスメントID*/
  gdlId: string
  /** 計画期間ID*/
  sc1Id: string
  /** 1戸建て集合住宅*/
  houseShu: string
  /** 賃貸所有給与住宅等*/
  shoyuShu: string
  /** その他（ﾒﾓ）*/
  shoyuMemoKnj: string
  /** 平屋建て*/
  hiraya: string
  /** 階建て*/
  kaidate: string
  /** 集合住宅*/
  shugou: string
  /** 階建て階数*/
  kaisu1: string
  /** 集合住宅階数*/
  kaisu2: string
  /** 専用居室の有無*/
  kyoUmu: string
  /** 1階*/
  kyoKai1: string
  /** 2階*/
  kyoKai2: string
  /** その他（2階以上）*/
  kyoKai3: string
  /** 階数*/
  kyoKaisu: string
  /** エレベーターの有無*/
  elevatorUmu: string
  /** 寝具*/
  sleepWhere: string
  /** ベッドの種類1（固定）*/
  bedShu1: string
  /** ベッドの種類2（ギャッチ）*/
  bedShu2: string
  /** ベッドの種類3（電動）*/
  bedShu3: string
  /** ベッドの種類4（その他）*/
  bedShu4: string
  /** その他（ﾒﾓ）*/
  bedMemoKnj: string
  /** 陽あたり*/
  hiatari: string
  /** 暖房*/
  heater: string
  /** 冷房*/
  airCooling: string
  /** 和式*/
  toilShu1: string
  /** 洋式*/
  toilShu2: string
  /** その他*/
  toilShu3: string
  /** その他（ﾒﾓ）*/
  toilMemoKnj: string
  /** 手すりの有無（トイレ）*/
  toilTesuriUmu: string
  /** 段差の有無*/
  toilDansaUmu: string
  /** （自宅に）浴槽の有無*/
  bathUmu: string
  /** 手すりの有無（浴槽）*/
  bathTesuriUmu: string
  /** 段差の有無（浴槽）*/
  bathDansaUmu: string
  /** 福祉機器（室外）使用状態*/
  hukuOutUse: string
  /** 福祉機器（室外）1*/
  hukuOut1: string
  /** 福祉機器（室外）2*/
  hukuOut2: string
  /** 福祉機器（室外）3*/
  hukuOut3: string
  /** 福祉機器（室外）4*/
  hukuOut4: string
  /** 福祉機器（室外）5*/
  hukuOut5: string
  /** その他（ﾒﾓ）*/
  outMemoKnj: string
  /** 福祉機器（室内）使用状態*/
  hukuInUse: string
  /** 福祉機器（室内）1*/
  hukuIn1: string
  /** 福祉機器（室内）2*/
  hukuIn2: string
  /** 福祉機器（室内）3*/
  hukuIn3: string
  /** 福祉機器（室内）4*/
  hukuIn4: string
  /** 福祉機器（室内）5*/
  hukuIn5: string
  /** その他（ﾒﾓ）*/
  inMemoKnj: string
  /** 洗濯機*/
  setsubi1: string
  /** 湯沸器*/
  setsubi2: string
  /** 冷蔵庫*/
  setsubi3: string
  /** 立地環境上の問題の有*/
  mondaiUmu1: string
  /** 立地環境上の問題の無*/
  mondaiUmu2: string
  /** 立地環境上の問題（ﾒﾓ）*/
  mondaiKnj: string
  /** 特記事項*/
  memoKnj: string
  /** 調理器具*/
  setsubi4: string
  /** 暖房器具（ガス）*/
  setsubi5Shu1: string
  /** 暖房器具（電気）*/
  setsubi5Shu2: string
  /** 暖房器具（灯油）*/
  setsubi5Shu3: string
  /** 暖房器具（その他）*/
  setsubi5Shu4: string
  /** その他（メモ）*/
  setsubi5Shu4MemoKnj: string
}

/**
 * 住宅等の状況（Ｈ２１改訂）情報
 */
export interface Gdl4HouH21Info {
  /**
   * アセスメントID
   */
  gdlId: string

  /**
   * 計画期間ID
   */
  sc1Id: string

  /**
   * 1戸建て集合住宅
   */
  houseShu: string

  /**
   * 賃貸所有給与住宅等
   */
  shoyuShu: string

  /**
   * その他（ﾒﾓ）
   */
  shoyuMemoKnj: string

  /**
   * 平屋建て
   */
  hiraya: string

  /**
   * 階建て
   */
  kaidate: string

  /**
   * 集合住宅
   */
  shugou: string

  /**
   * 階建て階数
   */
  kaisu1: string

  /**
   * 集合住宅階数
   */
  kaisu2: string

  /**
   * 専用居室の有無
   */
  kyoUmu: string

  /**
   * 1階
   */
  kyoKai1: string

  /**
   * 2階
   */
  kyoKai2: string

  /**
   * その他（2階以上）
   */
  kyoKai3: string

  /**
   * 階数
   */
  kyoKaisu: string

  /**
   * エレベーターの有無
   */
  elevatorUmu: string

  /**
   * 寝具
   */
  sleepWhere: string

  /**
   * ベッドの種類1（固定）
   */
  bedShu1: string

  /**
   * ベッドの種類2（ギャッチ）
   */
  bedShu2: string

  /**
   * ベッドの種類3（電動）
   */
  bedShu3: string

  /**
   * ベッドの種類4（その他）
   */
  bedShu4: string

  /**
   * その他（ﾒﾓ）
   */
  bedMemoKnj: string

  /**
   * 陽あたり
   */
  hiatari: string

  /**
   * 暖房
   */
  heater: string

  /**
   * 冷房
   */
  airCooling: string

  /**
   * 和式
   */
  toilShu1: string

  /**
   * 洋式
   */
  toilShu2: string

  /**
   * その他
   */
  toilShu3: string

  /**
   * その他（ﾒﾓ）
   */
  toilMemoKnj: string

  /**
   * 手すりの有無（トイレ）
   */
  toilTesuriUmu: string

  /**
   * 段差の有無
   */
  toilDansaUmu: string

  /**
   * （自宅に）浴槽の有無
   */
  bathUmu: string

  /**
   * 手すりの有無（浴槽）
   */
  bathTesuriUmu: string

  /**
   * 段差の有無（浴槽）
   */
  bathDansaUmu: string

  /**
   * 福祉機器（室外）使用状態
   */
  hukuOutUse: string

  /**
   * 福祉機器（室外）1
   */
  hukuOut1: string

  /**
   * 福祉機器（室外）2
   */
  hukuOut2: string

  /**
   * 福祉機器（室外）3
   */
  hukuOut3: string

  /**
   * 福祉機器（室外）4
   */
  hukuOut4: string

  /**
   * 福祉機器（室外）5
   */
  hukuOut5: string

  /**
   * その他（ﾒﾓ）
   */
  outMemoKnj: string

  /**
   * 福祉機器（室内）使用状態
   */
  hukuInUse: string

  /**
   * 福祉機器（室内）1
   */
  hukuIn1: string

  /**
   * 福祉機器（室内）2
   */
  hukuIn2: string

  /**
   * 福祉機器（室内）3
   */
  hukuIn3: string

  /**
   * 福祉機器（室内）4
   */
  hukuIn4: string

  /**
   * 福祉機器（室内）5
   */
  hukuIn5: string

  /**
   * その他（ﾒﾓ）
   */
  inMemoKnj: string

  /**
   * 洗濯機
   */
  setsubi1: string

  /**
   * 湯沸器
   */
  setsubi2: string

  /**
   * 冷蔵庫
   */
  setsubi3: string

  /**
   * 立地環境上の問題の有
   */
  mondaiUmu1: string

  /**
   * 立地環境上の問題の無
   */
  mondaiUmu2: string

  /**
   * 立地環境上の問題（ﾒﾓ）
   */
  mondaiKnj: string

  /**
   * 特記事項
   */
  memoKnj: string

  /**
   * 更新回数
   */
  modifiedCnt: string
}

/**
 * 住宅等の状況（R3/4改訂）情報
 */
export interface Gdl5HouInfo {
  /**
   * アセスメントID
   */
  gdlId: string

  /**
   * 計画期間ID
   */
  sc1Id: string

  /**
   * 1戸建て集合住宅
   */
  houseShu: string

  /**
   * 賃貸所有給与住宅等
   */
  shoyuShu: string

  /**
   * その他（ﾒﾓ）
   */
  shoyuMemoKnj: string

  /**
   * 平屋建て
   */
  hiraya: string

  /**
   * 〇階建て
   */
  kaidate: string

  /**
   * 集合住宅
   */
  shugou: string

  /**
   * 〇階建て階数
   */
  kaisu1: string

  /**
   * 集合住宅階数
   */
  kaisu2: string

  /**
   * 専用居室の有無
   */
  kyoUmu: string

  /**
   * 1階
   */
  kyoKai1: string

  /**
   * 2階
   */
  kyoKai2: string

  /**
   * その他（2階以上）
   */
  kyoKai3: string

  /**
   * 階数
   */
  kyoKaisu: string

  /**
   * エレベーターの有無
   */
  elevatorUmu: string

  /**
   * 寝具
   */
  sleepWhere: string

  /**
   * ベッドの種類1（固定）
   */
  bedShu1: string

  /**
   * ベッドの種類2（ギャッチ）
   */
  bedShu2: string

  /**
   * ベッドの種類3（電動）
   */
  bedShu3: string

  /**
   * ベッドの種類4（その他）
   */
  bedShu4: string

  /**
   * その他（ﾒﾓ）
   */
  bedMemoKnj: string

  /**
   * 陽あたり
   */
  hiatari: string

  /**
   * 暖房
   */
  heater: string

  /**
   * 冷房
   */
  airCooling: string

  /**
   * 和式
   */
  toilShu1: string

  /**
   * 洋式
   */
  toilShu2: string

  /**
   * その他
   */
  toilShu3: string

  /**
   * その他（ﾒﾓ）
   */
  toilMemoKnj: string

  /**
   * 手すりの有無（トイレ）
   */
  toilTesuriUmu: string

  /**
   * 段差の有無
   */
  toilDansaUmu: string

  /**
   * （自宅に）浴槽の有無
   */
  bathUmu: string

  /**
   * 手すりの有無（浴槽）
   */
  bathTesuriUmu: string

  /**
   * 段差の有無（浴槽）
   */
  bathDansaUmu: string

  /**
   * 福祉機器（室外）使用状態
   */
  hukuOutUse: string

  /**
   * 福祉機器（室外）1
   */
  hukuOut1: string

  /**
   * 福祉機器（室外）2
   */
  hukuOut2: string

  /**
   * 福祉機器（室外）3
   */
  hukuOut3: string

  /**
   * 福祉機器（室外）4
   */
  hukuOut4: string

  /**
   * 福祉機器（室外）5
   */
  hukuOut5: string

  /**
   * その他（ﾒﾓ）
   */
  outMemoKnj: string

  /**
   * 福祉機器（室内）使用状態
   */
  hukuInUse: string

  /**
   * 福祉機器（室内）1
   */
  hukuIn1: string

  /**
   * 福祉機器（室内）2
   */
  hukuIn2: string

  /**
   * 福祉機器（室内）3
   */
  hukuIn3: string

  /**
   * 福祉機器（室内）4
   */
  hukuIn4: string

  /**
   * 福祉機器（室内）5
   */
  hukuIn5: string

  /**
   * その他（ﾒﾓ）
   */
  inMemoKnj: string

  /**
   * 洗濯機
   */
  setsubi1: string

  /**
   * 湯沸器
   */
  setsubi2: string

  /**
   * 冷蔵庫
   */
  setsubi3: string

  /**
   * 立地環境上の問題の有
   */
  mondaiUmu1: string

  /**
   * 立地環境上の問題の無
   */
  mondaiUmu2: string

  /**
   * 立地環境上の問題（ﾒﾓ）
   */
  mondaiKnj: string

  /**
   * 特記事項
   */
  memoKnj: string

  /**
   * 調理器具
   */
  setsubi4: string

  /**
   * 暖房器具（ガス）
   */
  setsubi5Shu1: string

  /**
   * 暖房器具（電気）
   */
  setsubi5Shu2: string

  /**
   * 暖房器具（灯油）
   */
  setsubi5Shu3: string

  /**
   * 暖房器具（その他）
   */
  setsubi5Shu4: string

  /**
   * その他（メモ）
   */
  setsubi5Shu4MemoKnj: string

  /**
   * 更新回数
   */
  modifiedCnt: string
}

/**
 * 課題と目標リスト
 */
export interface IssuesGoalListType {
  /** インデックス署名 */
  [key: string]: string | undefined

  /**
   * ID
   */
  id?: string

  /**
   * アセスメントID
   */
  gdlId?: string

  /**
   * 計画期間ID
   */
  sc1Id?: string

  /**
   * アセスメント番号
   */
  assNo?: string

  /**
   * 課題
   */
  kadaiKnj?: string

  /**
   * 長期
   */
  choukiKnj?: string

  /**
   * 短期
   */
  tankiKnj?: string

  /**
   * 連番
   */
  seq?: string

  /**
   * アセスメント名称
   */
  assName?: string
}

/**
 * 更新用リクエストパラメータタイプ
 */
export interface AssessmentHomeTab4UpdateInEntity extends InWebEntity {
  /**
   * タブID
   */
  tabId: string
  /**
   * 機能ID
   */
  kinoId: string
  /**
   * 当履歴ページ番号
   */
  krirekiNo: string
  /**
   * e文書用パラメータ
   */
  edocumentUseParam: IReportInEntity
  /**
   * e文書削除用パラメータ
   */
  edocumentDeleteUseParam: IReportInEntity
  /**
   * 期間対象フラグ
   */
  kikanFlg: string
  /**
   * 計画対象期間番号
   */
  planningPeriodNo: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * ガイドラインまとめ
   */
  matomeFlg: string
  /**
   * ログインID
   */
  loginId: string
  /**
   * システム略称
   */
  sysRyaku: string
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * システムコード
   */
  sysCd: string
  /**
   * 事業者名
   */
  svJigyoKnj: string
  /**
   * 作成者名
   */
  createUserName: string
  /**
   * 利用者名
   */
  userName: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 更新区分
   */
  updateKbn: string
  /**
   * 履歴更新区分
   */
  historyUpdateKbn: string
  /**
   * 削除処理区分
   */
  deleteKbn: string
  /**
   * 計画対象期間ID
   */
  sc1Id: string
  /**
   * アセスメントID
   */
  gdlId: string
  /**
   * 作成日
   */
  kijunbiYmd: string
  /**
   * 作成者ID
   */
  sakuseiId: string
  /**
   * 改定フラグ
   */
  ninteiFormF: string
  /**
   * 住居等の状況情報
   */
  houInfo: {
    /**
     * １戸建て・集合住宅
     */
    houseShu: string
    /**
     * 賃貸・所有・給与住宅等
     */
    shoyuShu: string
    /**
     * その他（ﾒﾓ）
     */
    shoyuMemoKnj: string
    /**
     * 専用居室の有無
     */
    kyoUmu: string
    /**
     * １階
     */
    kyoKai1: string
    /**
     * ２階
     */
    kyoKai2: string
    /**
     * その他（２階以上）
     */
    kyoKai3: string
    /**
     * 階数
     */
    kyoKaisu: string
    /**
     * エレベーターの有無
     */
    elevatorUmu: string
    /**
     * 寝具
     */
    sleepWhere: string
    /**
     * ベッドの種類１（固定）
     */
    bedShu1: string
    /**
     * ベッドの種類2（ギャッチ）
     */
    bedShu2: string
    /**
     * ベッドの種類3（電動）
     */
    bedShu3: string
    /**
     * ベッドの種類4（その他）
     */
    bedShu4: string
    /**
     * その他（ﾒﾓ）（寝具）
     */
    bedMemoKnj: string
    /**
     * 陽あたり
     */
    hiatari: string
    /**
     * 暖房
     */
    heater: string
    /**
     * 冷房
     */
    airCooling: string
    /**
     * 和式
     */
    toilShu1: string
    /**
     * 洋式
     */
    toilShu2: string
    /**
     * その他（トイレ）
     */
    toilShu3: string
    /**
     * その他（ﾒﾓ）（トイレ）
     */
    toilMemoKnj: string
    /**
     * 手すりの有無（トイレ）
     */
    toilTesuriUmu: string
    /**
     * 段差の有無（トイレ）
     */
    toilDansaUmu: string
    /**
     * （自宅に）浴槽の有無
     */
    bathUmu: string
    /**
     * 手すりの有無（浴槽）
     */
    bathTesuriUmu: string
    /**
     * 段差の有無（浴槽）
     */
    bathDansaUmu: string
    /**
     * 福祉機器（室外）使用状態
     */
    hukuOutUse: string
    /**
     * 車いす（室外）
     */
    hukuOut1: string
    /**
     * 電動車いす（室外）
     */
    hukuOut2: string
    /**
     * 杖（室外）
     */
    hukuOut3: string
    /**
     * 歩行器（室外）
     */
    hukuOut4: string
    /**
     * その他（室外）
     */
    hukuOut5: string
    /**
     * その他（ﾒﾓ）（室外）
     */
    outMemoKnj: string
    /**
     * 福祉機器（室内）使用状態
     */
    hukuInUse: string
    /**
     * 車いす（室内）
     */
    hukuIn1: string
    /**
     * 電動車いす（室内）
     */
    hukuIn2: string
    /**
     * 杖（室内）
     */
    hukuIn3: string
    /**
     * 歩行器（室内）
     */
    hukuIn4: string
    /**
     * その他（室内）
     */
    hukuIn5: string
    /**
     * その他（ﾒﾓ）（室内）
     */
    inMemoKnj: string
    /**
     * 洗濯機
     */
    setsubi1: string
    /**
     * 湯沸器
     */
    setsubi2: string
    /**
     * 冷蔵庫
     */
    setsubi3: string
    /**
     * 特記事項
     */
    memoKnj: string
    /**
     * 見取り図
     */
    mitoriDraw: string
    /**
     * 見取り図縮小フラグ 0:通常、0<:縮小率
     */
    shukushoFlg: string
    /**
     * 調理器具
     */
    setsubi4: string
    /**
     * 暖房器具（ガス）
     */
    setsubi5Shu1: string
    /**
     * 暖房器具（電気）
     */
    setsubi5Shu2: string
    /**
     * 暖房器具（灯油）
     */
    setsubi5Shu3: string
    /**
     * 暖房器具（その他）
     */
    setsubi5Shu4: string
    /**
     * その他（メモ）（暖房器具）
     */
    setsubi5Shu4MemoKnj: string
  }
  /**
   * 課題と目標情報リスト
   */
  kadaiList: {
    /**
     * id
     */
    id: string
    /**
     * アセスメント番号
     */
    assNo: string
    /**
     * 課題
     */
    kadaiKnj: string
    /**
     * 長期
     */
    choukiKnj: string
    /**
     * 短期
     */
    tankiKnj: string
    /**
     * 連番
     */
    seq: string
    /**
     * 更新区分
     */
    updateKbn: string
  }[]
}

/**
 * 更新API出力エンティティ
 */
export interface AssessmentHomeTab4UpdateOutEnity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 計画対象期間ID
     */
    sc1Id: string
    /**
     * アセスメントID
     */
    gdlId: string
    /**
     * エラー区分
     */
    errKbn?: string
  }
}
