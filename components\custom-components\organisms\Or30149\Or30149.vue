<script setup lang="ts">
/**
 * Or30149:有機体:［アセスメント］画面（居宅）（6⑥）
 *
 * @description
 * アイコンボタン付けラベルを表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { ref, reactive, onMounted, nextTick, watch, computed } from 'vue'
import { cloneDeep } from 'lodash'
import { Or54682Const } from '../Or54682/Or54682.constants'
import { Or30221Const } from '../Or30221/Or30221.constants'
import { Or30149Const } from '../Or30149/Or30149.constants'
import { TeX0002Logic } from '../../template/TeX0002/TeX0002.logic'
import { Or30233Const } from '../Or30233/Or30233.constants'
import { Or30269Const } from '../Or30269/Or30269.constants'
import { Or35672Const } from '../Or35672/Or35672.constants'
import { Or26866Const } from '../Or26866/Or26866.constants'
import { Or26866Logic } from '../Or26866/Or26866.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or35672Logic } from '../Or35672/Or35672.logic'
import { TeX0002Const } from '../../template/TeX0002/TeX0002.constants'
import { OrX0209Const } from '../OrX0209/OrX0209.constants'
import { Or28500Logic } from '../Or28500/Or28500.logic'
import { Or28500Const } from '../Or28500/Or28500.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import type { SaveData } from '../Or29241/Or29241.type'
import type { Or30233Type } from '../Or30233/Or30233.type'
import type { Or30149OnewayType } from './Or30149.type'
import { Or30149Logic } from './Or30149.logic'
import type {
  AssessmentHomeChosaCntInEntity,
  AssessmentHomeChosaCntOutEntity,
  AssessmentHomeChosaInfoSelectInEntity,
  AssessmentHomeChosaInfoSelectOutEntity,
  AssessmentHomeChosaUpdateInEntity,
} from '~/repositories/cmn/entities/AssessmentHomeChosaEntity'

import type { Or28500OnewayType, Or28500Type } from '~/types/cmn/business/components/Or28500Type'

import type {
  Or54682OnewayType,
  Or54682Type,
  SupportListHeader,
  SupportListItem,
} from '~/types/cmn/business/components/Or54682Type'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { CustomClass } from '~/types/CustomClassType'
import type { Or26866OnewayType, Or26866Type } from '~/types/cmn/business/components/Or26866Type'

import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import {
  useNuxtApp,
  useCmnCom,
  useSystemCommonsStore,
  useSetupChildProps,
  useScreenTwoWayBind,
  useScreenUtils,
  useScreenStore,
} from '#imports'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  AssessmentHomeTab66SelectInEntity,
  AssessmentHomeTab66SelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentHome66Entity'
import type { TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  AssessmentHome66UpdateInEntity,
  AssessmentHome66UpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentHome66Entity.ts'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { OrX0201OnewayType } from '~/types/cmn/business/components/OrX0201Type'
import type {
  IssuesAndGoalListItem,
  OrX0209Type,
  OrX0209OnewayType,
} from '~/types/cmn/business/components/OrX0209Type'
import type { Or30149Type } from '~/types/cmn/business/components/Or30149Type'
import { UPDATE_KBN } from '~/constants/classification-constants'

const cmnRouteCom = useCmnRouteCom()
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or30149OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const $log = useNuxtApp().$log as DebugLogPluginInterface
const { t } = useI18n()
const { getChildCpBinds } = useScreenUtils()

const or54682 = ref({ uniqueCpId: '' })
const or30221 = ref({ uniqueCpId: '' })
const or30233 = ref({ uniqueCpId: '' })
const or30269 = ref({ uniqueCpId: '' })

const OrX0209 = ref({ uniqueCpId: '' })
const or28500 = ref({ uniqueCpId: '' })
const or26866 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })

/** 解決すべき課題と目標Ref */
const issuesAndGoalListRef = ref<HTMLElement | null>(null)

// 平成30年区分（作成日が2018年4月1日以降フラグ）
const heisei30Kbn = ref(true)

const localDefault = reactive({
  healthRelationList: {
    careCertificationList: {
      sectionList: [
        {
          value: '1',
          sectionTitle: t('label.treatment-contents'),
          sectionItems: [
            {
              itemType: '1',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango61',
              tooltip: '',
              label: t('label.assessment-home-6-6-care-required-certification-item-label1'),
            },
            {
              itemType: '1',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango62',
              tooltip: '',
              label: t('label.assessment-home-6-6-care-required-certification-item-label2'),
            },
            {
              itemType: '1',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango63',
              tooltip: '',
              label: t('label.assessment-home-6-6-care-required-certification-item-label3'),
            },
            {
              itemType: '1',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango64',
              tooltip: '',
              label: t('label.assessment-home-6-6-care-required-certification-item-label4'),
            },
            {
              itemType: '1',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango65',
              tooltip: '',
              label: t('label.assessment-home-6-6-care-required-certification-item-label5'),
            },
            {
              itemType: '1',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango66',
              tooltip: '',
              label: t('label.assessment-home-6-6-care-required-certification-item-label6'),
            },
            {
              itemType: '1',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango67',
              tooltip: '',
              label: t('label.assessment-home-6-6-care-required-certification-item-label7'),
            },
            {
              itemType: '1',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango68',
              tooltip: '',
              label: t('label.assessment-home-6-6-care-required-certification-item-label8'),
            },
            {
              itemType: '1',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango69',
              tooltip: '',
              label: t('label.assessment-home-6-6-care-required-certification-item-label9'),
            },
          ],
        },
        {
          value: '2',
          sectionTitle: t('label.special-correspondence'),
          sectionItems: [
            {
              itemType: '2',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango610',
              label: t('label.assessment-home-6-6-care-required-certification-item-label10'),
            },
            {
              itemType: '2',
              check: { modelValue: false },
              value: '0',
              itemName: 'ango611',
              label: t('label.assessment-home-6-6-care-required-certification-item-label11'),
            },
            {
              itemType: '2',
              check: { modelValue: false },
              value: '0',
              itemName: 'bango612',
              label: t('label.assessment-home-6-6-care-required-certification-item-label12'),
            },
          ],
        },
      ],
    },
  } as Or30233Type,
  // 援助の現状セクション一覧
  assistanceCurrentSituationList: {
    headers: [] as SupportListHeader[],
    items: [
      {
        /** 6-①1-10関係 */
        relation: t('label.assessment-home-6-6-current-situation-of-assistance-label-1'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-①1-10関係 */
        relation: t('label.assessment-home-6-6-current-situation-of-assistance-label-2'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-①1-10関係 */
        relation: t('label.assessment-home-6-6-current-situation-of-assistance-label-3'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-①1-10関係 */
        relation: t('label.assessment-home-6-6-current-situation-of-assistance-label-4'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-①1-10関係 */
        relation: t('label.assessment-home-6-6-current-situation-of-assistance-label-5'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
      {
        /** 6-①1-10関係 */
        relation: t('label.assessment-home-6-6-current-situation-of-assistance-label-6'),
        /** 援助の現状 家族実施 */
        familyImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 援助の現状 サービス実施 */
        serverImplementation: {
          modelValue: '',
        } as Mo00040Type,
        /** 希望 */
        hope: {
          modelValue: '',
        } as Mo00040Type,
        /** 要援助→計画 */
        needAssistanceToPlan: {
          modelValue: '',
        } as Mo00040Type,
      },
    ] as SupportListItem[],
    isSpeechBallonShow: false,
  } as Or54682Type,
  // 具体的内容一覧
  concreteContentsCheckList: [
    {
      items: [
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-1'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-2'),
        },
      ],
    },
    {
      items: [
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-3'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-4'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-5'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-6'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-7'),
        },
      ],
    },
    {
      items: [
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-8'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-9'),
        },
      ],
    },
    {
      items: [
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-10'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-11'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-12'),
        },

        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-13'),
        },

        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-14'),
        },

        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-15'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-16'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-17'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-18'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-19'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-20'),
        },
        {
          planCheck: {
            modelValue: false,
          } as Mo00018Type,
          statusCheck: {
            modelValue: false,
          } as Mo00018Type,
          planValue: '',
          statusValue: '',
          label: t('label.assessment-home-6-6-concrete-contents-label-21'),
        },
      ],
    },
  ],
  specialNoteShouldSolutionIssues: {
    inputContent: '',
  },
})
const local = reactive({
  // 共通情報
  commonInfo: {} as TeX0002Type,
  healthRelationList: {
    ...localDefault.healthRelationList,
  },
  // 援助の現状一覧
  assistanceCurrentSituationList: {
    ...localDefault.assistanceCurrentSituationList,
  } as Or54682Type,
  // 具体的内容一覧
  concreteContentsCheckList: [...localDefault.concreteContentsCheckList],
  specialNoteShouldSolutionIssues: {
    ...localDefault.specialNoteShouldSolutionIssues,
  },
  issuesAndGoalsList: {
    items: [] as IssuesAndGoalListItem[],
  } as OrX0209Type,
  // GUI00937 入力支援［ケアマネ］画面
  or51775: { modelValue: '' } as Or51775Type,
  or26866: { rtnContents: '' } as Or26866Type,
})
const localOneway = reactive({
  assistanceCurrentSituationListOneway: {
    relationTitle: '',
    familyImplementationItems: [],
    serverImplementationItems: [],
    hopeItems: [],
    needAssistanceToPlanItems: [],
  } as Or54682OnewayType,
  // 取込選択ボタン
  ImportBtnLabelOneway: {
    btnLabel: t('label.assessment-home-6-6-servey-ledger-import'),
  } as Mo00009OnewayType,
  subTitleOneway: {
    /** 値のフォントの太さ */
    valueFontWeight: 'bold',
    /** 値 */
    value: t('label.assessment-home-6-6-medical-care'),
    /** カスタムクラス */
    customClass: new CustomClass({
      outerClass: 'background-color',
      labelClass: 'd-none',
      itemClass: 'v-toolbar-title',
      outerStyle: 'background:transparent',
      itemStyle: 'fontSize:18px',
    }),
  } as Mo00009OnewayType,
  commonOrX0156Oneway: {
    persistentCounter: false,
    showItemLabel: false,
    maxlength: '4000',
    rows: '4',
    maxRows: '4',
    noResize: true,
    showDividerLineFlg: true,
    iconBtnDisplayFlg: true,
    showEditBtnFlg: true,
    customClass: new CustomClass({ outerClass: 'special-note' }),
  } as OrX0156OnewayType,
  // 解決すべき課題と目標一覧
  issuesAndGoalsListOneway: {
    assessmentName:
      t('label.assessment-home-issuesAndGoals-no-1') +
      '\r\n' +
      t('label.assessment-home-issuesAndGoals-name-1'),
  } as OrX0209OnewayType,
  orX0201Oneway: {
    /** タイトル数字 */
    tabNo: '6',
    /** タイトル名 */
    title: t('label.assessment-home-6-1-title'),
  } as OrX0201OnewayType,
  or30269Oneway: {
    heisei30Kbn: heisei30Kbn.value,
  },
  mo00609Oneway: {
    btnLabel: t('label.sentence-master'),
    variant: 'flat',
    color: 'transparent',
    width: '106',
  } as Mo00609OnewayType,
  or28500: {
    sc1Id: '',
    svJigyoId: '',
    userId: '',
    kikanFlg: '',
    ninteiFlg: '',
  } as Or28500OnewayType,
  // GUI00937 入力支援［ケアマネ］画面
  or51775Oneway: {} as Or51775OnewayType,
  or26866Oneway: {} as Or26866OnewayType,
})
const or28500ModelValue = ref<Or28500Type>({
  isBundleImport: { modelValue: false } as Mo00018Type,
  questionnaireRevisionFlg: '1',
  historySelectedList: [],
})

const isLoading = ref(false)

// リサイズ監視
// const observer = ref<ResizeObserver | null>(null)

// コンポーネントRef
const componentRefOr30149 = ref<HTMLDivElement | null>(null)

// コンポーネント表示フラグ
const isComponentVisible = ref(true)

// フォーカス中項目ID
const updateKbn = ref(TeX0002Const.DEFAULT.UPDATE_KBN_N)

// Or51775ダイアログの表示状態を監視するcomputedプロパティ
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

// 26866 ダイアログ表示フラグ
const showDialogOr26866 = computed(() => {
  // Or26866 cks_flg=1 のダイアログ開閉状態
  return Or26866Logic.state.get(or26866.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or30149Type>({
  cpId: Or30149Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニックIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or54682Const.CP_ID(0)]: or54682.value,
  [Or30221Const.CP_ID(0)]: or30221.value,
  [Or30233Const.CP_ID(0)]: or30233.value,
  [Or30269Const.CP_ID(0)]: or30269.value,
  [OrX0209Const.CP_ID(0)]: OrX0209.value,
  [Or28500Const.CP_ID(0)]: or28500.value,
  [Or26866Const.CP_ID(0)]: or26866.value,

  [Or51775Const.CP_ID(0)]: or51775.value,
})
// ダイアログ表示フラグ
const showDialogOr28500 = computed(() => {
  // Or28500のダイアログ開閉状態
  return Or28500Logic.state.get(or28500.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Or35672ステート発火
 *
 * @param state - ステート
 */
function setTex0002State(state: Record<string, boolean>) {
  TeX0002Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

onMounted(() => {
  $log.debug(`★[onMounted] [cpId]${Or30149Const.CP_ID(0)} [uId]${props.uniqueCpId}`)
  // initControls()
  void initCodes()
  // 課題と目標一覧スクロール先のIDを設定
  // localOneway.orX0201Oneway.scrollToUniqueCpId = OrX0209.value.uniqueCpId
  // getCommonInfo()
  // コントロール初期化
  //
  // void reload()
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 利用者一覧における利用者の変更を監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue === undefined) {
      return
    }
  },
  { deep: true }
)

watch(
  () => local.healthRelationList.careCertificationList.sectionList,
  (newValue) => {
    console.log('newValue', newValue)
  },
  { deep: true }
)
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0002Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    console.log(newValue, '88888')
    if (!newValue) return

    // 画面共通情報を取得
    getCommonInfo(newValue)

    // 画面コントロールデータを設定
    setControlsData()

    // 本画面が表示画面ではない場合、スキップ
    console.log('local.commonInfo', local.commonInfo)

    if (local.commonInfo.activeTabId !== Or30149Const.DEFAULT.TAB_ID) {
      return
    }

    if (newValue.isRefresh) {
      isComponentVisible.value = true
      // 画面情報再取得
      await reload()
    }
    if (newValue.isCreateDateChanged) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await reload()
    }

    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      if ((await _save(local.commonInfo.deleteKbn)).saveStatus) {
        setTex0002State({ isSaveCompleted: true })
      }
    }
    if (newValue.saveOnlyEventFlg) {
      // 保存のみの場合
      void _save(local.commonInfo.deleteKbn)
    }

    if (newValue.tabChangeSaveEventFlg) {
      // タブ変更保存の場合
      if ((await _save(local.commonInfo.deleteKbn)).saveStatus) {
        setTex0002State({ tabChangeSaveCompleted: true })
      }
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      createNew()
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      isComponentVisible.value = false

      updateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_D

      if (local.commonInfo.deleteKbn === TeX0002Const.DEFAULT.DELETE_PROCESS_KBN_TAB) {
        localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_DELETE_TAB
      } else if (local.commonInfo.deleteKbn === TeX0002Const.DEFAULT.DELETE_PROCESS_KBN_ALL) {
        localOneway.issuesAndGoalsListOneway.mode = OrX0209Const.DEFAULT.MODE_DELETE_ALL
      }
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      await _copy()
    }
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or35672Logic.event.get(Or35672Const.CP_ID(0) + props.parentUniqueCpId),
  async (newValue) => {
    if (!newValue?.reload) return

    setOr35672Event({ reload: false })

    // 複写画面から共通情報を取得
    getCommonInfoFromCopy()

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or30149Const.DEFAULT.TAB_ID) {
      return
    }

    // 画面再表示
    if (newValue?.reload) {
      // 画面情報再取得
      await reload()
      // 次の描画までまつ（ターゲットエレメントのサイズが取得できるため）
      // await nextTick()
      // 関係線を作成
      // drawRelationLines()

      // window.addEventListener('resize', drawRelationLines)
    }
  }
)

watch(
  () => heisei30Kbn,
  () => {
    localOneway.or30269Oneway.heisei30Kbn = heisei30Kbn.value
  },
  { deep: true }
)

/**************************************************
 * 関数
 **************************************************/

/**
 *  画面コントロールデータを設定
 */
function setControlsData() {
  // 平成30年区分を判断
  if (local.commonInfo.createYmd) {
    if (parseInt(local.commonInfo.createYmd.replaceAll('/', '') ?? '0') >= 20180401) {
      heisei30Kbn.value = true
    } else {
      heisei30Kbn.value = false
    }
  } else {
    heisei30Kbn.value = false
  }

  // R３/４改訂版 または (H21/４改訂版 かつ 作成日がH30/4/1以降)の場合
  if (
    local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_R34 ||
    (local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21 && heisei30Kbn.value)
  ) {
    // localOneway.section3.orX0186Oneway.title =
    //   t('label.assessment-home-6-1-basic-motion-special-note') +
    //   t('label.left-black-lenticular-bracket') +
    //   t('label.special-note-should-solution-issues') +
    //   t('label.right-black-lenticular-bracket')
  }

  // 解決すべき課題と目標一覧
  local.issuesAndGoalsList.items =
    cloneDeep(local.commonInfo.issuesAndGoalsList) ?? ([] as IssuesAndGoalListItem[])
}

/**
 * Or35672イベント発火
 *
 * @param event - イベント
 */
function setOr35672Event(event: Record<string, boolean>) {
  Or35672Logic.event.set({
    uniqueCpId: Or35672Const.CP_ID(0) + props.parentUniqueCpId,
    events: event,
  })
}

/**
 * RefValueをリセット
 *
 */
function setRefValue() {
  // RefValueを更新する
  useScreenStore().setCpTwoWay({
    cpId: Or30149Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/**
 * 複写処理
 *
 */
async function _copy() {
  // パラメータ
  const param: AssessmentHomeTab66SelectInEntity = {
    /** 計画期間ID */
    sc1Id: local.commonInfo.copySc1Id ?? '',
    /** 履歴ID */
    gdlId: local.commonInfo.copyGdlId ?? '',
    /** 改定フラグ */
    ninteiFlg: local.commonInfo.copyNinteiFormF ?? '',
  }

  const res: AssessmentHomeTab66SelectOutEntity = await ScreenRepository.select(
    'assessmentHomeTab66Select',
    param
  )

  // 画面情報を設定
  if (res.data) {
    setFormData(res)
  }
}

/**
 * Or35672ステート発火
 *
 * @param state - ステート
 */
function setOr35672State(state: Record<string, boolean>) {
  Or35672Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 * 指定された認定調査票情報を取得する。
 *
 * @param paramId -返却情報.調査票ID
 */
async function getAssessmentHomeChosaInfo(paramId: string) {
  const inputData: AssessmentHomeChosaInfoSelectInEntity = {
    cschId: paramId,
  }
  const resData: AssessmentHomeChosaInfoSelectOutEntity = await ScreenRepository.select(
    'assessmentHomeChosaInfoSelect',
    inputData
  )

  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    const { cpnTucCsc3H21Info } = resData.data
    local.healthRelationList.careCertificationList.sectionList.forEach((item) => {
      item.sectionItems.forEach((subItem) => {
        subItem.check.modelValue = cpnTucCsc3H21Info?.[subItem.itemName] === '1' ? true : false
      })
    })
  }
}

/**
 * 調査票一括取込処理を行う。
 *
 * @param param -返却情報
 */
async function updateAssessmentHomeChosaInfo(param: {
  cschId: string
  gdlId: string
  sc1Id: string
}) {
  const inputData: AssessmentHomeChosaUpdateInEntity = {
    ...param,
    ninteiFlg: local.commonInfo?.ninteiFormF ?? '',
    tabId: local.commonInfo?.activeTabId ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: local.commonInfo?.userId ?? '',
    svJigyoId: local.commonInfo?.jigyoId ?? '',
  }
  await ScreenRepository.update('assessmentHomeChosaUpdate', inputData)
}

/**
 * ［GUI00632_期間内履歴選択］返却情報取得
 *
 * @param rtnData -返却情報
 */
async function onGetOr28500Value(rtnData: Or28500Type) {
  if (!rtnData.historySelectedList.length) return
  const cschId = rtnData.historySelectedList[0]?.cschId?.toString() ?? ''
  await getAssessmentHomeChosaInfo(cschId)
  if (rtnData.isBundleImport.modelValue) {
    // 返却情報.一括取込フラグ＝1の場合 調査票一括取込処理を行う。
    // save
    // データを再取得します
    // if (!_savetabData) return
    // const { gdlId = '', sc1Id = '' } = await _savetabData('0')
    void _save(local.commonInfo.deleteKbn)
    void updateAssessmentHomeChosaInfo({
      cschId,
      gdlId: local.commonInfo.gdlId ?? '',
      sc1Id: local.commonInfo.sc1Id ?? '',
    })
  }
}

/**
 * Or51775の確認ダイアログのOK時の処理
 *
 * @param resData - 入力支援選択情報
 */
function onGetOr51775Value(resData: Or51775ConfirmType) {
  // 返却情報.上書きフラグが0:本文末に追加の場合
  if (Or30149Const.DEFAULT.IMPORT_TYPE_ADD === resData.type) {
    // 画面.特記事項に「画面.特記事項 + 返却情報.内容」を設定する。
    local.specialNoteShouldSolutionIssues.inputContent =
      (local.specialNoteShouldSolutionIssues.inputContent
        ? local.specialNoteShouldSolutionIssues.inputContent
        : '') + resData.value
  }
  // 返却情報.上書きフラグが1:本文上書の場合
  else if (Or30149Const.DEFAULT.IMPORT_TYPE_COVER === resData.type) {
    // 画面.特記事項に「返却情報.内容」を設定する。
    local.specialNoteShouldSolutionIssues.inputContent = resData.value
  }

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * 認定調査 特定事項選択ダイアログ戻り値処理
 *
 * @param resData - 入力支援選択情報
 */
function onGetOr26866Value(resData: string) {
  //  画面.特記事項に返却情報を設定する。
  local.specialNoteShouldSolutionIssues.inputContent = resData
}

/**
 *  画面初期情報取得
 */
async function getInitDataInfo() {
  const startF: number = performance.now()

  const start: number = performance.now()

  // バックエンドAPIから初期情報取得
  const inputData: AssessmentHomeTab66SelectInEntity = {
    /** 計画期間ID：画面.計画対象期間ID */
    sc1Id: local.commonInfo.sc1Id ?? '',
    /** アセスメントID：画面.アセスメントID */
    gdlId: local.commonInfo.gdlId ?? '',
    /** 改訂フラグ：画面.改訂フラグ */
    ninteiFlg: local.commonInfo.ninteiFormF ?? '',
  }
  const resData: AssessmentHomeTab66SelectOutEntity = await ScreenRepository.select(
    'assessmentHomeTab66Select',
    inputData
  )

  const end: number = performance.now()
  console.log(`assessmentHomeTab61Select API実行時間: ${(end - start).toFixed(3)} ミリ秒`)

  // 画面情報を設定
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    setFormData(resData)
  } else {
    if (props.onewayModelValue?.mode === Or30149Const.DEFAULT.MODE_COPY) {
      setOr35672State({ noData: true })
    }
  }

  const endF: number = performance.now()
  console.log(`getInitDataInfo メソッド内実行時間: ${(endF - startF).toFixed(3)} ミリ秒`)
}

/**
 *  コントロール初期化
 */
// function initControls() {
//   void getInitDataInfo()
//   void initCodes()
// }

/**
 *  画面データを設定
 *
 * @param resData - 設定データ
 */
function setFormData(resData: AssessmentHomeTab66SelectOutEntity) {
  let start = 0
  let end = 0
  start = performance.now()

  // 複写モードの場合、APIから取得のデータを保持
  if (props.onewayModelValue?.mode === Or30149Const.DEFAULT.MODE_COPY) {
    Or30149Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        copyData: resData,
      },
    })
  }

  const tabData = resData.data.cpnTucGdlKan16Info

  if (tabData === undefined) {
    if (props.onewayModelValue?.mode === Or30149Const.DEFAULT.MODE_COPY) {
      setOr35672State({ noData: true })
    }

    return
  }

  setOr35672State({ noData: false })
  // 要介護認定項目を設定
  local.healthRelationList.careCertificationList.sectionList[0].sectionItems[0].check.modelValue =
    tabData.bango61 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[0].sectionItems[1].check.modelValue =
    tabData.bango62 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[0].sectionItems[2].check.modelValue =
    tabData.bango63 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[0].sectionItems[3].check.modelValue =
    tabData.bango64 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[0].sectionItems[4].check.modelValue =
    tabData.bango65 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[0].sectionItems[5].check.modelValue =
    tabData.bango66 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[0].sectionItems[6].check.modelValue =
    tabData.bango67 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[0].sectionItems[7].check.modelValue =
    tabData.bango68 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[0].sectionItems[8].check.modelValue =
    tabData.bango69 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[1].sectionItems[0].check.modelValue =
    tabData.bango610 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[1].sectionItems[1].check.modelValue =
    tabData.bango611 === '1' ? true : false
  local.healthRelationList.careCertificationList.sectionList[1].sectionItems[2].check.modelValue =
    tabData.bango612 === '1' ? true : false

  end = performance.now()
  console.log(`基本動作情報を設定 実行時間: ${(end - start).toFixed(3)} ミリ秒`)

  start = performance.now()
  // 援助の現状セクション一覧設定
  local.assistanceCurrentSituationList.items[0].familyImplementation.modelValue = tabData.famJisshi1
  local.assistanceCurrentSituationList.items[0].serverImplementation.modelValue = tabData.serJisshi1
  local.assistanceCurrentSituationList.items[0].hope.modelValue = tabData.kibo1
  local.assistanceCurrentSituationList.items[0].needAssistanceToPlan.modelValue = tabData.keikaku1
  local.assistanceCurrentSituationList.items[1].familyImplementation.modelValue = tabData.famJisshi2
  local.assistanceCurrentSituationList.items[1].serverImplementation.modelValue = tabData.serJisshi2
  local.assistanceCurrentSituationList.items[1].hope.modelValue = tabData.kibo2
  local.assistanceCurrentSituationList.items[1].needAssistanceToPlan.modelValue = tabData.keikaku2
  local.assistanceCurrentSituationList.items[2].familyImplementation.modelValue = tabData.famJisshi3
  local.assistanceCurrentSituationList.items[2].serverImplementation.modelValue = tabData.serJisshi3
  local.assistanceCurrentSituationList.items[2].hope.modelValue = tabData.kibo3
  local.assistanceCurrentSituationList.items[2].needAssistanceToPlan.modelValue = tabData.keikaku3
  local.assistanceCurrentSituationList.items[3].familyImplementation.modelValue = tabData.famJisshi4
  local.assistanceCurrentSituationList.items[3].serverImplementation.modelValue = tabData.serJisshi4
  local.assistanceCurrentSituationList.items[3].hope.modelValue = tabData.kibo4
  local.assistanceCurrentSituationList.items[3].needAssistanceToPlan.modelValue = tabData.keikaku4
  local.assistanceCurrentSituationList.items[4].familyImplementation.modelValue = tabData.famJisshi5
  local.assistanceCurrentSituationList.items[4].serverImplementation.modelValue = tabData.serJisshi5
  local.assistanceCurrentSituationList.items[4].hope.modelValue = tabData.kibo5
  local.assistanceCurrentSituationList.items[4].needAssistanceToPlan.modelValue = tabData.keikaku5
  local.assistanceCurrentSituationList.items[5].familyImplementation.modelValue = tabData.famJisshi6
  local.assistanceCurrentSituationList.items[5].serverImplementation.modelValue = tabData.serJisshi6
  local.assistanceCurrentSituationList.items[5].hope.modelValue = tabData.kibo6
  local.assistanceCurrentSituationList.items[5].needAssistanceToPlan.modelValue = tabData.keikaku6

  end = performance.now()
  console.log(
    `体位変換・起居一覧設定、リハビリの必要性設定 実行時間: ${(end - start).toFixed(3)} ミリ秒`
  )

  start = performance.now()

  local.concreteContentsCheckList[0].items[0].statusCheck.modelValue =
    tabData.genjo1 === '1' ? true : false
  local.concreteContentsCheckList[0].items[1].statusCheck.modelValue =
    tabData.genjo2 === '1' ? true : false

  local.concreteContentsCheckList[1].items[0].statusCheck.modelValue =
    tabData.genjo3 === '1' ? true : false
  local.concreteContentsCheckList[1].items[1].statusCheck.modelValue =
    tabData.genjo4 === '1' ? true : false
  local.concreteContentsCheckList[1].items[2].statusCheck.modelValue =
    tabData.genjo5 === '1' ? true : false
  local.concreteContentsCheckList[1].items[3].statusCheck.modelValue =
    tabData.genjo6 === '1' ? true : false
  local.concreteContentsCheckList[1].items[4].statusCheck.modelValue =
    tabData.genjo7 === '1' ? true : false

  local.concreteContentsCheckList[2].items[0].statusCheck.modelValue =
    tabData.genjo8 === '1' ? true : false
  local.concreteContentsCheckList[2].items[1].statusCheck.modelValue =
    tabData.genjo9 === '1' ? true : false
  local.concreteContentsCheckList[3].items[0].statusCheck.modelValue =
    tabData.genjo10 === '1' ? true : false
  local.concreteContentsCheckList[3].items[1].statusCheck.modelValue =
    tabData.genjo11 === '1' ? true : false
  local.concreteContentsCheckList[3].items[2].statusCheck.modelValue =
    tabData.genjo12 === '1' ? true : false
  local.concreteContentsCheckList[3].items[3].statusCheck.modelValue =
    tabData.genjo13 === '1' ? true : false
  local.concreteContentsCheckList[3].items[4].statusCheck.modelValue =
    tabData.genjo14 === '1' ? true : false
  local.concreteContentsCheckList[3].items[5].statusCheck.modelValue =
    tabData.genjo15 === '1' ? true : false
  local.concreteContentsCheckList[3].items[6].statusCheck.modelValue =
    tabData.genjo16 === '1' ? true : false
  local.concreteContentsCheckList[3].items[7].statusCheck.modelValue =
    tabData.genjo17 === '1' ? true : false
  local.concreteContentsCheckList[3].items[8].statusCheck.modelValue =
    tabData.genjo18 === '1' ? true : false
  local.concreteContentsCheckList[3].items[9].statusCheck.modelValue =
    tabData.genjo19 === '1' ? true : false
  local.concreteContentsCheckList[3].items[10].statusCheck.modelValue =
    tabData.genjo20 === '1' ? true : false
  local.concreteContentsCheckList[3].items[11].statusCheck.modelValue =
    tabData.genjo21 === '1' ? true : false
  local.concreteContentsCheckList[0].items[0].planCheck.modelValue =
    tabData.kkeikaku1 === '1' ? true : false
  local.concreteContentsCheckList[0].items[1].planCheck.modelValue =
    tabData.kkeikaku2 === '1' ? true : false
  local.concreteContentsCheckList[1].items[0].planCheck.modelValue =
    tabData.kkeikaku3 === '1' ? true : false
  local.concreteContentsCheckList[1].items[1].planCheck.modelValue =
    tabData.kkeikaku4 === '1' ? true : false
  local.concreteContentsCheckList[1].items[2].planCheck.modelValue =
    tabData.kkeikaku5 === '1' ? true : false
  local.concreteContentsCheckList[1].items[3].planCheck.modelValue =
    tabData.kkeikaku6 === '1' ? true : false
  local.concreteContentsCheckList[1].items[4].planCheck.modelValue =
    tabData.kkeikaku7 === '1' ? true : false
  local.concreteContentsCheckList[2].items[0].planCheck.modelValue =
    tabData.kkeikaku8 === '1' ? true : false
  local.concreteContentsCheckList[2].items[1].planCheck.modelValue =
    tabData.kkeikaku9 === '1' ? true : false
  local.concreteContentsCheckList[3].items[0].planCheck.modelValue =
    tabData.kkeikaku10 === '1' ? true : false
  local.concreteContentsCheckList[3].items[1].planCheck.modelValue =
    tabData.kkeikaku11 === '1' ? true : false
  local.concreteContentsCheckList[3].items[2].planCheck.modelValue =
    tabData.kkeikaku12 === '1' ? true : false
  local.concreteContentsCheckList[3].items[3].planCheck.modelValue =
    tabData.kkeikaku13 === '1' ? true : false
  local.concreteContentsCheckList[3].items[4].planCheck.modelValue =
    tabData.kkeikaku14 === '1' ? true : false
  local.concreteContentsCheckList[3].items[5].planCheck.modelValue =
    tabData.kkeikaku15 === '1' ? true : false
  local.concreteContentsCheckList[3].items[6].planCheck.modelValue =
    tabData.kkeikaku16 === '1' ? true : false
  local.concreteContentsCheckList[3].items[7].planCheck.modelValue =
    tabData.kkeikaku17 === '1' ? true : false
  local.concreteContentsCheckList[3].items[8].planCheck.modelValue =
    tabData.kkeikaku18 === '1' ? true : false
  local.concreteContentsCheckList[3].items[9].planCheck.modelValue =
    tabData.kkeikaku19 === '1' ? true : false
  local.concreteContentsCheckList[3].items[10].planCheck.modelValue =
    tabData.kkeikaku20 === '1' ? true : false
  local.concreteContentsCheckList[3].items[11].planCheck.modelValue =
    tabData.kkeikaku21 === '1' ? true : false
  local.specialNoteShouldSolutionIssues.inputContent = tabData.memo1Knj!

  end = performance.now()
  console.log(`基本動作情報を設定 実行時間: ${(end - start).toFixed(3)} ミリ秒`)
}

/**
 *  画面共通情報を取得
 *
 * @param event - イベント
 */
function getCommonInfo(event: Record<string, boolean>) {
  const commonInfo = TeX0002Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo.kikanKanriFlg = commonInfo.kikanKanriFlg
    local.commonInfo.ninteiFormF = commonInfo.ninteiFormF
    local.commonInfo.syubetuId = commonInfo.syubetuId
    local.commonInfo.historyUpdateKbn = commonInfo.historyUpdateKbn
    local.commonInfo.historyModifiedCnt = commonInfo.historyModifiedCnt
    local.commonInfo.activeTabId = commonInfo.activeTabId
    local.commonInfo.jigyoId = commonInfo.jigyoId
    local.commonInfo.jigyoKnj = commonInfo.jigyoKnj
    local.commonInfo.shisetuId = commonInfo.shisetuId
    local.commonInfo.houjinId = commonInfo.houjinId
    local.commonInfo.userId = commonInfo.userId
    local.commonInfo.sc1Id = commonInfo.sc1Id
    local.commonInfo.sc1No = commonInfo.sc1No
    local.commonInfo.periodStartYmd = commonInfo.periodStartYmd
    local.commonInfo.periodEndYmd = commonInfo.periodEndYmd
    local.commonInfo.gdlId = commonInfo.gdlId
    local.commonInfo.historyNo = commonInfo.historyNo
    local.commonInfo.createUserId = commonInfo.createUserId
    local.commonInfo.createUserName = commonInfo.createUserName
    local.commonInfo.createYmd = commonInfo.createYmd
    local.commonInfo.copyUserId = commonInfo.copyUserId
    local.commonInfo.copyCreateYmd = commonInfo.copyCreateYmd
    local.commonInfo.copyNinteiFormF = commonInfo.copyNinteiFormF
    local.commonInfo.copySc1Id = commonInfo.copySc1Id
    local.commonInfo.copyGdlId = commonInfo.copyGdlId
    local.commonInfo.deleteKbn = commonInfo.deleteKbn
    local.commonInfo.issuesAndGoalsList = commonInfo.issuesAndGoalsList
  }

  // 解決すべき課題と目標一覧パラメータ設定
  if (!event.saveEventFlg) {
    localOneway.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
    localOneway.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
    localOneway.issuesAndGoalsListOneway.assNo = local.commonInfo.activeTabId
    localOneway.issuesAndGoalsListOneway.userId = local.commonInfo.userId

    local.issuesAndGoalsList.items =
      local.commonInfo.issuesAndGoalsList ?? ([] as IssuesAndGoalListItem[])
  } // 平成30年区分を判断
  if (local.commonInfo.createYmd) {
    if (parseInt(local.commonInfo.createYmd.replaceAll('/', '') ?? '0') >= 20180401) {
      heisei30Kbn.value = true
    } else {
      heisei30Kbn.value = false
    }
  } else {
    heisei30Kbn.value = false
  }
}

/**
 *  複写画面から共通情報を取得
 */
function getCommonInfoFromCopy() {
  const commonInfo = Or35672Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    /** 改定フラグ */
    local.commonInfo.ninteiFormF = commonInfo.params?.ninteiFormF
    /** 選択中タブID */
    local.commonInfo.activeTabId = commonInfo.params?.activeTabId
    /** 事業所ID */
    local.commonInfo.jigyoId = commonInfo.params?.jigyoId
    /** 法人ID */
    local.commonInfo.houjinId = commonInfo.params?.houjinId
    /** 施設ID */
    local.commonInfo.shisetuId = commonInfo.params?.shisetuId
    /** 利用者ID */
    local.commonInfo.userId = commonInfo.params?.userId
    /** 計画対象期間ID */
    local.commonInfo.sc1Id = commonInfo.params?.sc1Id
    /** アセスメントID */
    local.commonInfo.gdlId = commonInfo.params?.gdlId
    /** 作成者ID */
    local.commonInfo.createUserId = commonInfo.params?.createUserId
    /** 作成日 */
    local.commonInfo.createYmd = commonInfo.params?.createdYmd
    /** 種別ID */
    local.commonInfo.syubetuId = commonInfo.params?.syubetsuId
    /** 期間管理フラグ */
    local.commonInfo.kikanKanriFlg = commonInfo.params?.kikanFlg
    /** 課題と目標リスト */
    local.commonInfo.issuesAndGoalsList = commonInfo.params?.issuesAndGoalsList
  }

  // 解決すべき課題と目標一覧パラメータ設定
  localOneway.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
  localOneway.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
  localOneway.issuesAndGoalsListOneway.assNo = local.commonInfo.activeTabId
  localOneway.issuesAndGoalsListOneway.userId = local.commonInfo.userId

  local.issuesAndGoalsList.items =
    local.commonInfo.issuesAndGoalsList ?? ([] as IssuesAndGoalListItem[])
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 相談形態
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_003 },
    // 性別
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_001 },
    // 相談者の関係区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_002 },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得

  // コード取得
  // 家族実施
  // M_CD_KBN_ID_DROPDOWN_LIST_002
  const familyImplementationCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_002
  )
  localOneway.assistanceCurrentSituationListOneway.familyImplementationItems = []
  familyImplementationCodeList.forEach((item) => {
    // 体位変換・起居
    localOneway.assistanceCurrentSituationListOneway.familyImplementationItems?.push({
      displayName: item.label,
      value: item.value,
    })
  })

  // サービス実施
  const serverImplementationCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_001
  )
  localOneway.assistanceCurrentSituationListOneway.serverImplementationItems = []
  serverImplementationCodeList.forEach((item) => {
    // 体位変換・起居
    localOneway.assistanceCurrentSituationListOneway.serverImplementationItems?.push({
      displayName: item.label,
      value: item.value,
    })
  })

  // 希望
  const hopeCodeList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_001)
  localOneway.assistanceCurrentSituationListOneway.hopeItems = []
  hopeCodeList.forEach((item) => {
    // 体位変換・起居
    localOneway.assistanceCurrentSituationListOneway.hopeItems?.push({
      displayName: item.label,
      value: item.value,
    })
  })

  // 要援助→計画
  const needAssistanceToPlanCodeList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DROPDOWN_LIST_003
  )
  localOneway.assistanceCurrentSituationListOneway.needAssistanceToPlanItems = []
  needAssistanceToPlanCodeList.forEach((item) => {
    // 体位変換・起居
    localOneway.assistanceCurrentSituationListOneway.needAssistanceToPlanItems?.push({
      displayName: item.label,
      value: item.value,
    })
  })
}

/**
 *  コントロール初期化
 */
async function reload() {
  isLoading.value = true
  let start: number = performance.now()
  // 汎用コードマスタからコード情報を取得
  // void initCodes()
  clearData()
  let end: number = performance.now()
  console.log(`initCodes実行時間: ${(end - start).toFixed(3)} ミリ秒`)

  start = performance.now()
  // 画面初期情報取得
  await getInitDataInfo()
  end = performance.now()
  console.log(`getInitDataInfo実行時間: ${(end - start).toFixed(3)} ミリ秒`)

  // 改訂バージョンをチェック
  // checkVersion()
  // refValueを更新する
  // setRefValue()

  await nextTick()
  // 複写モードの場合、Tabを制御
  if (props.onewayModelValue?.mode === Or30149Const.DEFAULT.MODE_COPY) {
    if (componentRefOr30149.value) {
      disableTab(componentRefOr30149.value)
    }
  }

  isLoading.value = false
}

/**
 * 調査票のデータ件数を取得する。
 *
 */
async function getChosaCnt() {
  const inputData: AssessmentHomeChosaCntInEntity = {
    sc1Id: local.commonInfo?.sc1Id ?? '',
    userId: local.commonInfo?.userId ?? '',
    svJigyoId: local.commonInfo?.jigyoId ?? '',
  }
  const resData: AssessmentHomeChosaCntOutEntity = await ScreenRepository.select(
    'assessmentHomeChosaCntSelect',
    inputData
  )
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    return Number(resData.data.chosaCnt2)
  }
  return 0
}

/**
 *  取込ボタンクリック処理
 *
 * ［GUI00632_期間内履歴選択］ ダイアログをポップアップで起動する
 */
async function importBtnClick() {
  // 取得したデータ件数2が0件の場合 処理を終了する。
  if ((await getChosaCnt()) <= 0) {
    return
  }

  localOneway.or28500 = {
    sc1Id: local.commonInfo?.sc1Id ?? '',
    svJigyoId: local.commonInfo?.jigyoId ?? '',
    userId: local.commonInfo?.userId ?? '',
    kikanFlg: local.commonInfo?.kikanKanriFlg ?? '',
    ninteiFlg: local.commonInfo?.ninteiFormF ?? '',
  }
  Or28500Logic.state.set({
    uniqueCpId: or28500.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  保存処理
 *
 * @param deleteProcessKbn - 削除処理区分
 */
async function _save(deleteProcessKbn: string | undefined): Promise<SaveData> {
  if (!deleteProcessKbn) {
    return { saveStatus: false }
  }
  isLoading.value = true
  // 更新区分：画面.更新区分
  if (updateKbn.value === TeX0002Const.DEFAULT.UPDATE_KBN_N) {
    updateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_U
  }

  //  "履歴更新区分：画面.履歴更新区分
  let historyUpdateKbn = ''
  // ※ただし、画面.履歴更新区分≠""D""の場合、以下を設定する
  // ・画面.アセスメントID＝0の場合、 ""C""
  // ・画面.アセスメントID<>0の場合、""U"""
  // 画面.履歴更新区分≠"D"の場合、以下の設定を行う
  if (local.commonInfo.historyUpdateKbn !== UPDATE_KBN.DELETE) {
    // 画面.アセスメントID＝0の場合、 画面.履歴更新区分＝"C"
    if (local.commonInfo.gdlId === '0') {
      historyUpdateKbn = UPDATE_KBN.CREATE
    } else if (local.commonInfo.gdlId !== '0') {
      historyUpdateKbn = UPDATE_KBN.UPDATE
    }
  }

  const childCpBindsData = getChildCpBinds(props.uniqueCpId, {
    // 基本動作 要介護認定項目
    [Or30233Const.CP_ID(0)]: { cpPath: Or30233Const.CP_ID(0), twoWayFlg: true },
    // 課題と目標リスト
    [OrX0209Const.CP_ID(0)]: { cpPath: OrX0209Const.CP_ID(0), twoWayFlg: true },
  })

  console.log(888, childCpBindsData[Or30233Const.CP_ID(0)].twoWayBind?.value)

  // 基本動作 要介護認定項目データ
  const healthRelationListData = childCpBindsData[Or30233Const.CP_ID(0)].twoWayBind
    ?.value as Or30233Type

  const tabData = {
    // 要介護認定項目を設定
    bango61: healthRelationListData.careCertificationList.sectionList[0].sectionItems[0].check
      .modelValue
      ? '1'
      : '0',
    bango62: healthRelationListData.careCertificationList.sectionList[0].sectionItems[1].check
      .modelValue
      ? '1'
      : '0',
    bango63: healthRelationListData.careCertificationList.sectionList[0].sectionItems[2].check
      .modelValue
      ? '1'
      : '0',
    bango64: healthRelationListData.careCertificationList.sectionList[0].sectionItems[3].check
      .modelValue
      ? '1'
      : '0',
    bango65: healthRelationListData.careCertificationList.sectionList[0].sectionItems[4].check
      .modelValue
      ? '1'
      : '0',
    bango66: healthRelationListData.careCertificationList.sectionList[0].sectionItems[5].check
      .modelValue
      ? '1'
      : '0',
    bango67: healthRelationListData.careCertificationList.sectionList[0].sectionItems[6].check
      .modelValue
      ? '1'
      : '0',
    bango68: healthRelationListData.careCertificationList.sectionList[0].sectionItems[7].check
      .modelValue
      ? '1'
      : '0',
    bango69: healthRelationListData.careCertificationList.sectionList[0].sectionItems[8].check
      .modelValue
      ? '1'
      : '0',
    bango610: healthRelationListData.careCertificationList.sectionList[1].sectionItems[0].check
      .modelValue
      ? '1'
      : '0',
    bango611: healthRelationListData.careCertificationList.sectionList[1].sectionItems[1].check
      .modelValue
      ? '1'
      : '0',
    bango612: healthRelationListData.careCertificationList.sectionList[1].sectionItems[2].check
      .modelValue
      ? '1'
      : '0',

    // 援助の現状セクション一覧設定
    famJisshi1: local.assistanceCurrentSituationList.items[0].familyImplementation.modelValue,
    serJisshi1: local.assistanceCurrentSituationList.items[0].serverImplementation.modelValue,
    kibo1: local.assistanceCurrentSituationList.items[0].hope.modelValue,
    keikaku1: local.assistanceCurrentSituationList.items[0].needAssistanceToPlan.modelValue,
    famJisshi2: local.assistanceCurrentSituationList.items[1].familyImplementation.modelValue,
    serJisshi2: local.assistanceCurrentSituationList.items[1].serverImplementation.modelValue,
    kibo2: local.assistanceCurrentSituationList.items[1].hope.modelValue,
    keikaku2: local.assistanceCurrentSituationList.items[1].needAssistanceToPlan.modelValue,
    famJisshi3: local.assistanceCurrentSituationList.items[2].familyImplementation.modelValue,
    serJisshi3: local.assistanceCurrentSituationList.items[2].serverImplementation.modelValue,
    kibo3: local.assistanceCurrentSituationList.items[2].hope.modelValue,
    keikaku3: local.assistanceCurrentSituationList.items[2].needAssistanceToPlan.modelValue,
    famJisshi4: local.assistanceCurrentSituationList.items[3].familyImplementation.modelValue,
    serJisshi4: local.assistanceCurrentSituationList.items[3].serverImplementation.modelValue,
    kibo4: local.assistanceCurrentSituationList.items[3].hope.modelValue,
    keikaku4: local.assistanceCurrentSituationList.items[3].needAssistanceToPlan.modelValue,
    famJisshi5: local.assistanceCurrentSituationList.items[4].familyImplementation.modelValue,
    serJisshi5: local.assistanceCurrentSituationList.items[4].serverImplementation.modelValue,
    kibo5: local.assistanceCurrentSituationList.items[4].hope.modelValue,
    keikaku5: local.assistanceCurrentSituationList.items[4].needAssistanceToPlan.modelValue,
    famJisshi6: local.assistanceCurrentSituationList.items[5].familyImplementation.modelValue,
    serJisshi6: local.assistanceCurrentSituationList.items[5].serverImplementation.modelValue,
    kibo6: local.assistanceCurrentSituationList.items[5].hope.modelValue,
    keikaku6: local.assistanceCurrentSituationList.items[5].needAssistanceToPlan.modelValue,
    genjo1: local.concreteContentsCheckList[0].items[0].statusCheck.modelValue ? '1' : '0',
    genjo2: local.concreteContentsCheckList[0].items[1].statusCheck.modelValue ? '1' : '0',
    genjo3: local.concreteContentsCheckList[1].items[0].statusCheck.modelValue ? '1' : '0',
    genjo4: local.concreteContentsCheckList[1].items[1].statusCheck.modelValue ? '1' : '0',
    genjo5: local.concreteContentsCheckList[1].items[2].statusCheck.modelValue ? '1' : '0',
    genjo6: local.concreteContentsCheckList[1].items[3].statusCheck.modelValue ? '1' : '0',
    genjo7: local.concreteContentsCheckList[1].items[4].statusCheck.modelValue ? '1' : '0',
    genjo8: local.concreteContentsCheckList[2].items[0].statusCheck.modelValue ? '1' : '0',
    genjo9: local.concreteContentsCheckList[2].items[1].statusCheck.modelValue ? '1' : '0',
    genjo10: local.concreteContentsCheckList[3].items[0].statusCheck.modelValue ? '1' : '0',
    genjo11: local.concreteContentsCheckList[3].items[1].statusCheck.modelValue ? '1' : '0',
    genjo12: local.concreteContentsCheckList[3].items[2].statusCheck.modelValue ? '1' : '0',
    genjo13: local.concreteContentsCheckList[3].items[3].statusCheck.modelValue ? '1' : '0',
    genjo14: local.concreteContentsCheckList[3].items[4].statusCheck.modelValue ? '1' : '0',
    genjo15: local.concreteContentsCheckList[3].items[5].statusCheck.modelValue ? '1' : '0',
    genjo16: local.concreteContentsCheckList[3].items[6].statusCheck.modelValue ? '1' : '0',
    genjo17: local.concreteContentsCheckList[3].items[7].statusCheck.modelValue ? '1' : '0',
    genjo18: local.concreteContentsCheckList[3].items[8].statusCheck.modelValue ? '1' : '0',
    genjo19: local.concreteContentsCheckList[3].items[9].statusCheck.modelValue ? '1' : '0',
    genjo20: local.concreteContentsCheckList[3].items[10].statusCheck.modelValue ? '1' : '0',
    genjo21: local.concreteContentsCheckList[3].items[11].statusCheck.modelValue ? '1' : '0',
    kkeikaku1: local.concreteContentsCheckList[0].items[0].planCheck.modelValue ? '1' : '0',
    kkeikaku2: local.concreteContentsCheckList[0].items[1].planCheck.modelValue ? '1' : '0',
    kkeikaku3: local.concreteContentsCheckList[1].items[0].planCheck.modelValue ? '1' : '0',
    kkeikaku4: local.concreteContentsCheckList[1].items[1].planCheck.modelValue ? '1' : '0',
    kkeikaku5: local.concreteContentsCheckList[1].items[2].planCheck.modelValue ? '1' : '0',
    kkeikaku6: local.concreteContentsCheckList[1].items[3].planCheck.modelValue ? '1' : '0',
    kkeikaku7: local.concreteContentsCheckList[1].items[4].planCheck.modelValue ? '1' : '0',
    kkeikaku8: local.concreteContentsCheckList[2].items[0].planCheck.modelValue ? '1' : '0',
    kkeikaku9: local.concreteContentsCheckList[2].items[1].planCheck.modelValue ? '1' : '0',
    kkeikaku10: local.concreteContentsCheckList[3].items[0].planCheck.modelValue ? '1' : '0',
    kkeikaku11: local.concreteContentsCheckList[3].items[1].planCheck.modelValue ? '1' : '0',
    kkeikaku12: local.concreteContentsCheckList[3].items[2].planCheck.modelValue ? '1' : '0',
    kkeikaku13: local.concreteContentsCheckList[3].items[3].planCheck.modelValue ? '1' : '0',
    kkeikaku14: local.concreteContentsCheckList[3].items[4].planCheck.modelValue ? '1' : '0',
    kkeikaku15: local.concreteContentsCheckList[3].items[5].planCheck.modelValue ? '1' : '0',
    kkeikaku16: local.concreteContentsCheckList[3].items[6].planCheck.modelValue ? '1' : '0',
    kkeikaku17: local.concreteContentsCheckList[3].items[7].planCheck.modelValue ? '1' : '0',
    kkeikaku18: local.concreteContentsCheckList[3].items[8].planCheck.modelValue ? '1' : '0',
    kkeikaku19: local.concreteContentsCheckList[3].items[9].planCheck.modelValue ? '1' : '0',
    kkeikaku20: local.concreteContentsCheckList[3].items[10].planCheck.modelValue ? '1' : '0',
    kkeikaku21: local.concreteContentsCheckList[3].items[11].planCheck.modelValue ? '1' : '0',
    memo1Knj: local.specialNoteShouldSolutionIssues.inputContent,
  }

  // 更新データ作成
  const inputData: AssessmentHome66UpdateInEntity = {
    /** タブID:10 */
    tabId: Or30149Const.DEFAULT.TAB_ID,
    /** 機能ID：共通情報.機能ID */
    kinoId: systemCommonsStore.getFunctionId ?? '',
    /** 当履歴ページ番号：画面.当履歴ページ番号 */
    krirekiNo: local.commonInfo.historyNo ?? '',
    /** e文書用パラメータ */
    edocumentUseParam: useCmnCom().getEDocumentParam(),
    /** e文書削除用パラメータ */
    edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
    /** 期間対象フラグ */
    kikanFlg: local.commonInfo.kikanKanriFlg ?? '',
    /** 計画対象期間番号 */
    planningPeriodNo: local.commonInfo.sc1No ?? '',
    /** 開始日 */
    startYmd: local.commonInfo.periodStartYmd ?? '',
    /** 終了日 */
    endYmd: local.commonInfo.periodEndYmd ?? '',
    /** ガイドラインまとめ：共通情報.ガイドラインまとめ */
    matomeFlg: useCmnRouteCom().getInitialSettingMaster()?.gdlMatomeFlg ?? '',
    /** ログインID：共通情報.ログインID */
    loginId: systemCommonsStore.getCurrentUser.loginId ?? '',
    /** システム略称：共通情報.システム略称 */
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    /** 職員ID：共通情報.職員ID */
    shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
    /** システムコード：共通情報.システムコード */
    sysCd: systemCommonsStore.getSystemCode ?? '',
    /** 事業者名：画面.事業者名 */
    svJigyoKnj: local.commonInfo.jigyoKnj ?? '',
    /** 作成者名：画面.作成者名 */
    createUserName: local.commonInfo.createUserName ?? '',
    /** 利用者名：共通情報.利用者名 */
    userName:
      systemCommonsStore.getUserSelectUserInfo()?.nameSei +
      ' ' +
      systemCommonsStore.getUserSelectUserInfo()?.nameMei,
    /** 法人ID：共通情報.法人ID */
    houjinId: systemCommonsStore.getHoujinId ?? '',
    /** 施設ID：共通情報.施設ID */
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    /** 利用者ID：共通情報.利用者ID */
    userId: local.commonInfo.userId ?? '',
    /** 事業者ID：共通情報.事業者ID */
    svJigyoId: local.commonInfo.jigyoId ?? '',

    /** 種別ID：共通情報.種別ID */
    syubetsuId: local.commonInfo.syubetuId ?? '',
    /** 更新区分 */
    updateKbn: updateKbn.value,
    /** 履歴更新区分 */
    historyUpdateKbn: historyUpdateKbn,
    /** 削除処理区分 */
    deleteKbn: local.commonInfo.deleteKbn ?? UPDATE_KBN.NONE,
    /** 計画対象期間ID */
    sc1Id: local.commonInfo.sc1Id ?? '',
    /** アセスメントID */
    gdlId: local.commonInfo.gdlId ?? '',
    /** 作成者ID */
    createdUser: local.commonInfo.createUserId ?? '',
    /** 作成者日 */
    createdYmd: local.commonInfo.createYmd ?? '',
    /** 改訂フラグ */
    niteiFlg: local.commonInfo.ninteiFormF ?? '',

    cpnTucGdlKan16Info: tabData,
    kadaiList: [],
  }

  const resData: AssessmentHome66UpdateOutEntity = await ScreenRepository.update(
    'assessmentHomeTab66Update',
    inputData
  )

  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    console.log(inputData, 'inputData')
    // 更新後データを取得し設定
    TeX0002Logic.data.set({
      uniqueCpId: props.parentUniqueCpId,
      value: {
        updateData: {
          errKbn: historyUpdateKbn,
          sc1Id: resData.data.sc1Id,
          gdlId: resData.data.gdlId,
        },
      },
    })
    isLoading.value = false
    return { saveStatus: true, sc1Id: resData.data.sc1Id, gdlId: resData.data.gdlId }
  }
  isLoading.value = false
  return { saveStatus: false }
}

/**
 *  新規処理
 */
function createNew() {
  // 画面データをクリア
  clearData()
  updateKbn.value = TeX0002Const.DEFAULT.UPDATE_KBN_C

  // RefValueをリセット
  setTimeout(() => {
    setRefValue()
  }, 0)
}

/**
 *  画面データをクリア
 */
function clearData() {
  // 要介護認定項目を設定

  local.healthRelationList.careCertificationList.sectionList.forEach((element) => {
    element.sectionItems.map((item) => {
      item.check.modelValue = false
    })
  })

  // 援助の現状セクション一覧設定
  local.assistanceCurrentSituationList.items.forEach((item) => {
    item.familyImplementation.modelValue = ''
    item.serverImplementation.modelValue = ''
    item.hope.modelValue = ''
    item.needAssistanceToPlan.modelValue = ''
  })
  local.concreteContentsCheckList.forEach((item) => {
    item.items.map((it) => {
      it.statusCheck.modelValue = false
      it.planCheck.modelValue = false
    })
  })
  local.specialNoteShouldSolutionIssues.inputContent = ''
}

function openGUI00937() {
  localOneway.or51775Oneway = {
    // 認定調査票様式が
    //   タイトル："文章マスタ"
    title: '文章マスタ',
    // 画面ID："GUI00803"
    screenId: 'GUI00803',
    // 分類ID：-
    bunruiId: '-',
    // 大分類CD："602"
    t1Cd: '602',
    // 中分類CD："30"
    t2Cd: '30',
    // 小分類CD："0"
    t3Cd: '0',
    // テーブル名："cpn_tuc_gdl4_kan16_h21"
    tableName:
      local.commonInfo.ninteiFormF === TeX0002Const.DEFAULT.KAITEI_FLG_H21
        ? 'cpn_tuc_gdl4_kan16_h21'
        : 'cpn_tuc_gdl5_kan16_r3',
    // カラム名："memo1_knj"
    columnName: 'memo1_knj',
    // アセスメント方式：共通情報.ケアプラン方式
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? '',
    // 文章内容：画面.共通情報.特記事項
    inputContents: local.specialNoteShouldSolutionIssues.inputContent ?? '',
    // 利用者ID：共通情報.利用者ID
    userId: local.commonInfo.userId ?? '',
  }

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

function openGUI01273() {
  localOneway.or26866Oneway = {
    /**事業所ID：共通情報.事業所ID */
    svJigyoId: local.commonInfo.jigyoId ?? '',
    /** 認定フラグ：画面.改訂フラグ */
    ninteiFlg: local.commonInfo.ninteiFormF ?? '',
    /** 期間管理フラグ：画面.期間管理フラグ */
    dayVer: local.commonInfo.kikanKanriFlg ?? '',
    /** 計画期間ID：共通情報.計画期間ID */
    sc1Id: local.commonInfo.sc1Id ?? '',
    /** ユーザーID：共通情報.利用者ID */
    userId: local.commonInfo.userId ?? '',
    /** 項目番号:0 */
    defNo: '0',
    /** 項目番号配列：[6] */
    defNoArray: ['6'],
    /** メモ：画面.医療・健康関係特記事項セクションの特記事項 */
    memo: local.specialNoteShouldSolutionIssues.inputContent ?? '',
  }
  Or26866Logic.state.set({
    uniqueCpId: or26866.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * tabを制御
 *
 * @param component - コンポーネント
 */
function disableTab(component: HTMLElement) {
  const focusableSelectors = [
    'a[href]',
    'area[href]',
    'input',
    'select',
    'textarea',
    'button',
    'iframe',
    '[tabindex]',
    '[contenteditable]',
  ]

  const focusables = component.querySelectorAll(focusableSelectors.join(','))
  focusables.forEach((el) => {
    el.setAttribute('tabindex', '-1')
  })
}
</script>

<template>
  <div
    v-show="isComponentVisible"
    ref="componentRefOr30149"
    class="background-color"
  >
    <c-v-sheet
      id="Or30149Component"
      class="container"
      style="width: 1080px"
    >
      <!-- ローディング -->
      <v-overlay
        :model-value="isLoading"
        :persistent="false"
        class="align-center justify-center"
        ><v-progress-circular
          indeterminate
          color="primary"
        ></v-progress-circular
      ></v-overlay>
      <div class="mb-6">
        <g-custom-or-x-0201 :oneway-model-value="localOneway.orX0201Oneway" />
      </div>
      <!-- 上段 -->
      <c-v-row
        no-gutters
        class="radio-title"
      >
        <!-- 見出し ●６-①基本（身体機能・起居）動作-->
        <c-v-col
          cols="7"
          class="d-flex flex-row align-center"
        >
          <base-mo01338 :oneway-model-value="localOneway.subTitleOneway"> </base-mo01338>
        </c-v-col>
        <!-- 調査票取込 -->
        <c-v-col class="d-flex flex-row align-center justify-end">
          <base-mo00611
            :oneway-model-value="localOneway.ImportBtnLabelOneway"
            class="import-btn"
            @click="importBtnClick"
          />
        </c-v-col>
      </c-v-row>
      <!-- 中段 -->
      <c-v-row
        no-gutters
        class="pb-6 bg-white"
      >
        <c-v-col auto>
          <g-custom-or-30233
            v-bind="or30233"
            v-model="local.healthRelationList"
          ></g-custom-or-30233>
        </c-v-col>
        <c-v-col
          auto
          style="width: 698px"
        >
          <g-custom-or-54682
            v-bind="or54682"
            :model-value="local.assistanceCurrentSituationList"
            :oneway-model-value="localOneway.assistanceCurrentSituationListOneway"
          >
          </g-custom-or-54682>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col>
          <g-custom-or-30269
            v-bind="or30269"
            v-model="local.concreteContentsCheckList"
            :oneway-model-value="localOneway.or30269Oneway"
          ></g-custom-or-30269>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="bg-white"
      >
        <c-v-col class="mt-6">
          <c-v-row
            no-gutters
            class="c-sub-title"
          >
            {{ t('label.special-note-life-up-attention-issues') }}
          </c-v-row>
          <c-v-row
            no-gutters
            class="c-sub-content"
          >
            <c-v-col>
              <g-custom-orX0156
                :model-value="{ value: local.specialNoteShouldSolutionIssues.inputContent }"
                :oneway-model-value="localOneway.commonOrX0156Oneway"
                @on-click-edit-btn="openGUI01273"
              >
                <!-- slot -->
                <template #footer>
                  <base-mo-00609
                    class="special-note-font"
                    :oneway-model-value="localOneway.mo00609Oneway"
                    @click="openGUI00937"
                  ></base-mo-00609>
                </template>
              </g-custom-orX0156>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </c-v-sheet>
    <!-- 線 -->
    <c-v-row
      no-gutters
      style="margin: 0 -24px"
      class="py-6"
    >
      <c-v-divider></c-v-divider>
    </c-v-row>
    <!-- フッター -->
    <c-v-row
      no-gutters
      class="pb-6"
    >
      <div
        ref="issuesAndGoalListRef"
        class="w-100"
      >
        <g-custom-or-x-0209
          v-bind="OrX0209"
          :model-value="local.issuesAndGoalsList"
          :oneway-model-value="localOneway.issuesAndGoalsListOneway"
        >
        </g-custom-or-x-0209>
      </div>
    </c-v-row>
  </div>
  <g-custom-or-28500
    v-if="showDialogOr28500"
    v-bind="or28500"
    v-model="or28500ModelValue"
    :oneway-model-value="localOneway.or28500"
    @on-confirm="onGetOr28500Value"
  />
  <!-- GUI00937 入力支援［ケアマネ］画面 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="local.or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="onGetOr51775Value"
  />

  <!-- GUI01273_認定調査特記事項選択画面		 -->
  <g-custom-or-26866
    v-if="showDialogOr26866"
    v-bind="or26866"
    v-model="local.or26866"
    :oneway-model-value="localOneway.or26866Oneway"
    @update:model-value="onGetOr26866Value"
  />
</template>

<style scoped lang="scss">
.background-color {
  background-color: transparent;
}
.radio-title {
  background-color: #fff;
  padding: 16px 24px;
}
.import-btn {
  width: 92px;
  min-height: 32px !important;
  height: 32px !important;
  padding: 0px !important;
}
.container {
  background-color: transparent;
  .header {
    padding: 8px 0;
  }
  .title {
    font-size: 18px;
    font-weight: bold;
  }
}
.c-sub-title.white {
  background-color: #fff;
  line-height: 1;
  padding-top: 24px;
  padding-bottom: 16px;
  border: none;
}
.c-sub-content :deep(.v-input__control) {
  .v-field__input {
    min-height: auto !important;
  }
}
:deep(.item-label) {
  font-weight: bold !important;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
// テキストエリアの下部分
:deep(.special-note .tag-container) {
  gap: 16px !important;
}
:deep(.special-note .split-line) {
  width: 1px;
}
:deep(.special-note-font .v-btn__content) {
  opacity: 0.5;
}
</style>
