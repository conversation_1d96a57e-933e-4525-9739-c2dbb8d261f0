<script setup lang="ts">
/**
 * Or30425:(インターライ方式_CAP検討_課題と目標)課題と目標-表2
 * GUI00864_［検討表］画面（［アセスメント（インターライ）］画面）
 *
 * @description
 * (インターライ方式_CAP検討_課題と目標)課題と目標-表2
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
// 必要なライブラリをインポート
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import type { FocusRowKey, TableData } from './Or30425.type'
import { Or30425Const } from './Or30425.constants'
import type { Or30425OnewayType, Or30425Type } from '~/types/cmn/business/components/Or30425Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useScreenTwoWayBind } from '~/composables/useComponentVue'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { useSystemCommonsStore, useValidation } from '#imports'
import type { OrX0173OnewayType } from '~/types/cmn/business/components/OrX0173Type'
const { byteLength } = useValidation()
/**
 * or51775ダイアログ用ユニークID
 */
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })

/**
 * i18n
 */
const { t } = useI18n()
/**************************************************
 * Props（親コンポーネントから受け取る値の型定義）
 **************************************************/
/**
 * 親コンポーネントから受け取るプロパティ
 */
interface Props {
  onewayModelValue: Or30425OnewayType // 単方向バインディング値
  modelValue: Or30425Type // 双方向バインディング値
  uniqueCpId: string // ユニークなコンポーネントID
  parentUniqueCpId: string // 親コンポーネントのユニークID
}

/**
 * 親コンポーネントから受け取るプロパティを定義
 */
const props = defineProps<Props>()

/**
 * 初期値の定義
 */
/**
 * 初期値：課題と目標リスト空
 */
const defaultOnewayModelValue: Or30425OnewayType = {
  kentohyoKadaiList: [],
  raiId: '',
}

/**
 * 初期値：削除ボタン非活性・課題と目標リスト空
 */
const defaultModelValue: Or30425Type = {
  delBtnDisabled: false,
  kentohyoKadaiList: [],
}
/**
 * systemCommonsStore
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * 各種コンポーネント用の初期値を設定
 */
const localOneWay = reactive({
  or30425: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo01280Oneway: {
    maxlength: '4000',
  } as Mo01274OnewayType,
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  or51775Oneway: {
    /**
     * タイトル
     */
    title: '',
    /**
     * 大分類ＣＤ
     */
    t1Cd: '820',
    /**
     * 中分類ＣＤ
     */
    t2Cd: '',
    /**
     * 小分類ＣＤ
     */
    t3Cd: '0',
    /**
     * テーブル名
     */
    tableName: 'cpn_tuc_rai_plan2',
    /**
     * カラム名
     */
    columnName: '',
    /**
     * アセスメント方式
     */
    assessmentMethod: '',
    /**
     * 文章内容
     */
    inputContents: '',
    /**
     * 利用者ID
     */
    shokuinId: systemCommonsStore.getUserId ?? '',
  } as unknown as Or51775OnewayType,
})
/**
 * dataTable（テーブルデータ）
 */
const dataTable = ref<TableData[]>([])
/**
 * 双方向ローカル状態
 */
const local = reactive({
  or30425: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})
/**************************************************
 * Pinia（双方向バインディング用のカスタムフック）
 **************************************************/
/**
 * テーブルデータの双方向バインディング
 */
const { refValue } = useScreenTwoWayBind<Or30425OnewayType>({
  cpId: Or30425Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**************************************************
 * 変数定義
 **************************************************/
/**
 * 確認ダイアログ用のユニークID
 */
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
/**
 * ダイアログ表示フラグ
 * Or21814のダイアログが開いているかどうか
 */
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
/**
 * テーブル用フォーム参照
 */
const tableForm = ref<VForm>()
/**
 * focusRow
 */
const focusRow = ref<string>('')
/**
 * 選択した行のindex
 */
const selectedItemIndex = ref<number>(-1)
/**
 * テーブルヘッダ
 */
const headers = [
  { title: t('label.issues'), key: 'kadaiKnj', width: '222px', sortable: false, required: true },
  {
    title: t('label.long-term-goal'),
    key: 'choukiKnj',
    width: '260px',
    sortable: false,
    required: true,
  },
  {
    title: t('label.short-term-goal'),
    key: 'tankiKnj',
    width: '260px',
    sortable: false,
    required: true,
  },
  {
    title: t('label.care-details'),
    key: 'careKnj',
    width: '260px',
    sortable: false,
    required: true,
  },
]
const columnMinWidth = ref<number[]>([260, 260, 260, 260]) // 各列の最小幅を指定
const orX0173OnewayModelValue = reactive({
  showItemLabel: false,
  rows: 5,
  maxRows: '5',
  maxlength: '4000',
  autoGrow: true,
  rules: [byteLength(4000)],
} as OrX0173OnewayType)
/**************************************************
 * Emit（親へのイベント通知）
 **************************************************/
/**
 * emit
 * モデル値更新イベント
 */
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// マウント時の初期化処理
onMounted(async () => {
  await init()
})

/**
 * テーブルデータ初期化処理
 *
 * テーブルデータを初期化し、親コンポーネントにバインドする
 */
async function init() {
  // 戻り値はテーブルデータとして処理されます
  let tmpArr = []
  tmpArr = []
  for (const item of refValue.value?.kentohyoKadaiList ?? []) {
    // changeFがDELETEのデータは初期表示しない
    if (item.updateKbn === UPDATE_KBN.DELETE) continue
    tmpArr.push({
      raiId: item.raiId,
      capId: item.capId ?? 0,
      kadaiId: item.kadaiId ?? 0,
      careKnj: { value: item.careKnj ?? '' },
      kadaiKnj: { value: item.kadaiKnj ?? '' },
      choukiKnj: { value: item.choukiKnj ?? '' },
      tankiKnj: { value: item.tankiKnj ?? '' },
      sort: item.sort ?? 0,
      tableIndex: tmpArr.length,
      updateKbn: UPDATE_KBN.NONE,
    })
  }
  dataTable.value = tmpArr
  await updateTableIndexesAndSelect(tmpArr.length > 0 ? 0 : -1)
  if (tmpArr.length > 0) {
    handleUpdateData()
  }
}

/**
 * 行選択処理
 *
 * @param index - 選択した行のindex
 */
async function selectRow(index: number) {
  await updateTableIndexesAndSelect(index)
}

/**
 * 新規行追加処理
 * 新しい行を追加
 */
async function createRow() {
  await addRow({})
  local.or30425.delBtnDisabled = true
  handleUpdateData()
}
/**
 * Insert a new row into the table at the selected index or at the end if no row is selected.
 */
async function insertRow() {
  const idx =
    selectedItemIndex.value !== Or30425Const.NUMBER_MINUS_1
      ? selectedItemIndex.value
      : dataTable.value.length
  await addRow({}, idx)
  handleUpdateData()
}
/**
 * 行複写処理
 * 選択行を複写して新規追加
 */
async function copyRow() {
  if (selectedItemIndex.value === Or30425Const.NUMBER_MINUS_1) return
  const srcRow = dataTable.value[selectedItemIndex.value]
  if (!srcRow) return
  await addRow(
    {
      careKnj: { value: srcRow.careKnj?.value ?? '' },
      kadaiKnj: { value: srcRow.kadaiKnj?.value ?? '' },
      choukiKnj: { value: srcRow.choukiKnj?.value ?? '' },
      tankiKnj: { value: srcRow.tankiKnj?.value ?? '' },
    },
    selectedItemIndex.value + 1
  )
  handleUpdateData()
}
/**
 * 行複写（特定フィールドのみ）処理
 * Stores the deleted rows from the table for later processing.
 */
async function copyRow2Field() {
  if (selectedItemIndex.value === Or30425Const.NUMBER_MINUS_1) return
  const srcRow = dataTable.value[selectedItemIndex.value]
  if (!srcRow) return
  await addRow(
    {
      careKnj: { value: '' },
      kadaiKnj: { value: srcRow.kadaiKnj?.value ?? '' },
      choukiKnj: { value: srcRow.choukiKnj?.value ?? '' },
      tankiKnj: { value: '' },
    },
    selectedItemIndex.value + 1
  )
  handleUpdateData()
}

/**
 * 行削除処理（確認ダイアログ表示）
 * 行削除ボタン押下
 * 行削除の確認ダイアログ表示
 */
function deleteRow() {
  if (selectedItemIndex.value !== Or30425Const.NUMBER_MINUS_1) {
    // メッセージを表示
    // 確認ダイアログのpiniaを設定
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.confirm-dialog-title-info'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10219'),
        // 第1ボタンタイプ
        firstBtnType: 'normal1',
        // 第1ボタンラベル
        firstBtnLabel: t('btn.yes'),
        // 第2ボタンタイプ
        secondBtnType: 'destroy1',
        // 第2ボタンラベル
        secondBtnLabel: t('btn.no'),
        // 第3ボタンタイプ
        thirdBtnType: 'blank',
        iconColor: 'rgb(var(--v-theme-blue-700))',
        iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
      },
    })
    // 確認ダイアログ表示
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * テーブルバリデーション
 *
 * @returns バリデーションが成功した場合はtrue、失敗した場合はfalseを返す
 */
async function tableValidation() {
  return (await tableForm.value!.validate()).valid
}

/**
 * 削除ダイアログイベント監視
 * Or21814のイベントを監視
 *
 * @description
 * Or21814のボタン押下時に行削除処理を実行
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (!newValue) return
    if (newValue.firstBtnClickFlg) {
      // 選択行のchangeFをDELETEに設定し、deletedRowsに追加、画面からは非表示
      if (selectedItemIndex.value !== Or30425Const.NUMBER_MINUS_1 && dataTable.value.length > 0) {
        const row = dataTable.value[selectedItemIndex.value]
        if (row) {
          row.updateKbn = UPDATE_KBN.DELETE
          deletedRows.value.push({ ...row })
        }
        dataTable.value = dataTable.value.filter((r) => r.updateKbn !== UPDATE_KBN.DELETE)
        await updateTableIndexesAndSelect(selectedItemIndex.value)
        handleUpdateData()
      }
    }
  }
)

/**
 * Or51775ダイアログを開く処理
 *
 * @param title - ダイアログのタイトル
 *
 * @param field - フォーカスするフィールド名
 *
 * @param t2Cd -string
 *
 * @param index - index
 */
async function onClickOr51775(title: string, field: string, t2Cd: string, index: number) {
  await selectRow(index)
  if (!dataTable.value?.length) return
  focusRow.value = field
  const idx = selectedItemIndex.value
  const item = (dataTable.value ?? [])[idx]
  const targetCell = item[focusRow.value as FocusRowKey]
  localOneWay.or51775Oneway.title = title

  localOneWay.or51775Oneway.t2Cd = t2Cd
  localOneWay.or51775Oneway.t3Cd = String(item.capId ?? '')
  localOneWay.or51775Oneway.columnName =
    field === Or30425Const.KADAI_KNJ_FIELD
      ? Or30425Const.KADAI_KNJ
      : field === Or30425Const.CHOUKI_KNJ_FIELD
        ? Or30425Const.CHOUKI_KNJ
        : field === Or30425Const.KADAI_KNJ_FIELD
          ? Or30425Const.TANKI_KNJ
          : Or30425Const.CARE_KNJ
  localOneWay.or51775Oneway.inputContents = targetCell.value
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * Or51775ダイアログ表示フラグ
 * Or51775のダイアログが開いているかどうか
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Or51775ダイアログからの確認イベント処理
 *
 * @param data - Or51775ダイアログからの確認データ
 */
const handleConfirm51775 = (data: Or51775ConfirmType) => {
  if (!dataTable.value?.length) return

  const idx = selectedItemIndex.value
  const item = (dataTable.value ?? [])[idx]

  if (idx === Or30425Const.NUMBER_MINUS_1 || !item) return

  const key = focusRow.value as FocusRowKey
  const targetCell = item[key] as { value: string } | undefined
  if (targetCell) {
    targetCell.value =
      data.type === Or30425Const.NUMBER_1 ? data.value : targetCell.value + data.value

    item.updateKbn = UPDATE_KBN.UPDATE
  }
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
  handleUpdateData()
}

/**
 * テーブルインデックス更新・行選択処理
 * テーブルのtableIndexを更新し、指定行を選択・スクロール
 *
 * @param selectIdx - 選択する行のインデックス
 */
async function updateTableIndexesAndSelect(selectIdx: number) {
  dataTable.value.forEach((row, idx) => {
    row.tableIndex = idx
  })
  if (!dataTable.value.length) {
    selectedItemIndex.value = -1
  } else if (selectIdx >= dataTable.value.length) {
    selectedItemIndex.value = dataTable.value.length - 1
  } else {
    selectedItemIndex.value = selectIdx
  }
  await nextTick()
  if (selectedItemIndex.value !== Or30425Const.NUMBER_MINUS_1) {
    // rowEl?.scrollIntoView({
    //   behavior: 'smooth',
    //   block: 'center',
    //   inline: 'nearest',
    // })
  }
}

/**
 * 行追加・インデックス更新処理
 * 任意の位置に行を追加し、tableIndexを更新・選択
 *
 * @param rowData - 追加する行データ
 *
 * @param insertIndex - 挿入位置
 */
async function addRow(rowData: Partial<TableData>, insertIndex?: number) {
  const idx = insertIndex ?? dataTable.value.length
  const data: TableData = {
    raiId: Number(refValue.value?.raiId) || 0,
    capId: rowData.capId ?? 0,
    kadaiId: rowData.kadaiId ?? 0,
    careKnj: { value: '' },
    kadaiKnj: { value: '' },
    choukiKnj: { value: '' },
    tankiKnj: { value: '' },
    tableIndex: idx,
    sort: rowData.sort ?? 0,
    updateKbn: UPDATE_KBN.CREATE,
    ...rowData,
  }
  dataTable.value.splice(idx, 0, data)
  await updateTableIndexesAndSelect(idx)
}

/**
 * 削除済み行データ
 */
const deletedRows = ref<TableData[]>([])
/**
 * データ更新・親コンポーネントへemit
 */
function handleUpdateData() {
  const tmpArr = []
  // 画面上のデータ
  for (const item of dataTable.value) {
    tmpArr.push({
      raiId: item.raiId,
      capId: item.capId ?? 0,
      kadaiId: item.kadaiId ?? 0,
      careKnj: item.careKnj?.value ?? '',
      kadaiKnj: item.kadaiKnj?.value ?? '',
      choukiKnj: item.choukiKnj?.value ?? '',
      tankiKnj: item.tankiKnj?.value ?? '',
      sort: item.sort ?? 0,
      updateKbn: item.updateKbn,
    })
  }
  // 削除済みデータもマージ
  for (const item of deletedRows.value) {
    tmpArr.push({
      raiId: item.raiId,
      capId: item.capId ?? 0,
      kadaiId: item.kadaiId ?? 0,
      careKnj: item.careKnj?.value ?? '',
      kadaiKnj: item.kadaiKnj?.value ?? '',
      choukiKnj: item.choukiKnj?.value ?? '',
      tankiKnj: item.tankiKnj?.value ?? '',
      sort: item.sort ?? 0,
      updateKbn: item.updateKbn,
    })
  }
  local.or30425.kentohyoKadaiList = tmpArr
  emit('update:modelValue', local.or30425)
}
defineExpose({
  tableValidation,
  createRow,
  insertRow,
  copyRow,
  copyRow2Field,
  deleteRow,
  init,
})
</script>

<template>
  <!-- タイトルコンテナ -->
  <div class="title-container">
    <!-- 予定マスタ一覧フォーム -->
    <c-v-form ref="tableForm">
      <c-v-data-table
        v-resizable-grid="{ columnWidths: columnMinWidth }"
        fixed-header
        :headers="headers"
        :items="dataTable"
        class="table-header mt-2 table-wrapper"
        hide-default-footer
        :items-per-page="-1"
      >
        <template #headers>
          <tr>
            <th
              v-for="header in headers"
              :key="header.id"
              :width="header.width"
              class="table-header-cell"
            >
              <span class="thead-text">
                {{ header.title }}
              </span>
            </th>
          </tr>
        </template>
        <!-- テーブルデータ -->
        <template #item="{ item }">
          <tr
            :id="'row-' + item.tableIndex"
            :class="{ 'highlight-row': selectedItemIndex === item.tableIndex }"
            @click="selectRow(item.tableIndex)"
          >
            <!-- 課題 -->
            <td class="text-padding">
              <g-custom-or-x-0173
                v-model="item.kadaiKnj"
                :oneway-model-value="orX0173OnewayModelValue"
                @click="
                  onClickOr51775(
                    t('label.issues'),
                    Or30425Const.KADAI_KNJ_FIELD,
                    Or30425Const.NUMBER_1,
                    item.tableIndex
                  )
                "
              >
              </g-custom-or-x-0173>
            </td>

            <!-- 長期目標 -->
            <td class="text-padding">
              <g-custom-or-x-0173
                v-model="item.choukiKnj"
                :oneway-model-value="orX0173OnewayModelValue"
                @click="
                  onClickOr51775(
                    t('label.long-term-goal'),
                    Or30425Const.CHOUKI_KNJ_FIELD,
                    Or30425Const.NUMBER_2,
                    item.tableIndex
                  )
                "
              >
              </g-custom-or-x-0173>
            </td>
            <!-- 短期目標 -->
            <td class="text-padding">
              <g-custom-or-x-0173
                v-model="item.tankiKnj"
                :oneway-model-value="orX0173OnewayModelValue"
                @click="
                  onClickOr51775(
                    t('label.short-term-goal'),
                    Or30425Const.TANKI_KNJ_FIELD,
                    Or30425Const.NUMBER_4,
                    item.tableIndex
                  )
                "
              >
              </g-custom-or-x-0173>
            </td>
            <!-- *ケア内容 -->
            <td class="text-padding">
              <g-custom-or-x-0173
                v-model="item.careKnj"
                :oneway-model-value="orX0173OnewayModelValue"
                @click="
                  onClickOr51775(
                    t('label.care-details'),
                    Or30425Const.CARE_KNJ_FIELD,
                    Or30425Const.NUMBER_6,
                    item.tableIndex
                  )
                "
              >
              </g-custom-or-x-0173>
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-form>
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>

  <!-- Or51775:有機体:入力ダイアログ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="localOneWay.or51775Oneway"
    @confirm="handleConfirm51775"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';

:deep(.v-col) {
  padding: 0px !important;
}

// 右寄せのCSS
.text-align-right {
  text-align: right;
}

.text-padding {
  padding: 0px !important;
}
:deep(.v-table--fixed-header) {
  position: sticky;
  // ジッタ除去
  top: -0.5px;
  z-index: 2;
}

//区分番号幅
.number-width {
  width: 50px !important;
}

//内容幅
.content-width {
  width: 120px;
}

//区分番号
.body-text-category {
  margin-top: 50px;
  margin-left: 20px;
}

.font-size-text {
  font-size: 12px;
}

:deep(.table-header .v-data-table-rows-no-data) {
  display: none !important;
}
.table-header :deep(.v-table__wrapper th) {
  background-color: #dbeefe !important;
}

:deep(button.button-style) {
  border: none;
  background: transparent;
}
/* テーブルラッパーのスタイル */
:deep(.table-wrapper) {
  /* テーブル内のセル共通スタイル */
  .v-table__wrapper {
    td {
      /* textareaを含むセルのスタイル */
      &:has(textarea) {
        div:has(textarea:not([disabled]):not([readonly])) {
          height: 70px !important;
        }
      }
    }
  }
}
:deep(.v-field__outline__start),
:deep(.v-field__outline__end) {
  opacity: 0 !important;
}
:deep(.v-sheet .v-text-field .v-field) {
  background-color: transparent !important;
  border: 0px solid #cdcdcd;
}

.table-header-cell {
  font-weight: 400 !important;
  height: 32px !important;
  padding: 0px 12px !important;
}
</style>
