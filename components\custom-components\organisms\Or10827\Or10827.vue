<script setup lang="ts">
/**
 * Or10827:有機体:日課表マスタパターン設定モダール
 * GUI00987_日課表マスタパターン設定
 *
 * @description
 * 日課表マスタパターン設定
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { AsyncFunction, Or10827StateType } from '../Or10827/Or10827.type'
import { Or00734Const } from '../Or00734/Or00734.constants'
import { Or00725Const } from '../Or00725/Or00725.constants'
import { Or10827Const } from './Or10827.constants'
import { hasRegistAuth, useScreenOneWayBind, useSetupChildProps } from '#build/imports'
import { useGyoumuCom } from '~/utils/useGyoumuCom'
import type { Or10827OnewayType } from '~/types/cmn/business/components/Or10827Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Or00734OnewayType } from '~/types/cmn/business/components/Or00734Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or21814OnewayType, Or21814EventType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Or21815StateType, Or21815EventType } from '~/components/base-components/organisms/Or21815/Or21815.type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or10827OnewayType
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
// ローカルTwoway
const local = reactive({
  // 本画面ダイアログ
  mo00024: {
    isOpen: Or10827Const.DEFAULT.IS_OPEN,
  } as Mo00024Type,
  mo00043: { id: Or10827Const.TAB.TAB_ID_DAILY_TABLE } as Mo00043Type,
  mo00043Transfer: { id: Or10827Const.TAB.TAB_ID_DAILY_TABLE } as Mo00043Type,
  mo00043Change: { id: Or10827Const.TAB.TAB_ID_DAILY_TABLE } as Mo00043Type,
})

// ローカルOneway
const localOneway = reactive({
  or00734OneWay: {
    svJigyoId: props.onewayModelValue.svJigyoId,
    shisetuId: props.onewayModelValue.shisetuId,
    svJigyoIdList: props.onewayModelValue.svJigyoIdList,
  } as Or00734OnewayType,
  or10827OneWay: {
    ...props.onewayModelValue,
  },
  // 本画面ダイアログOneway
  mo00024Oneway: {
    width: '900px',
    height: '1100px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: Or10827Const.CP_ID(0),
      toolbarTitle: t('label.daily-table-master'),
      toolbarTitleCenteredFlg: false,
      toolbarName: '',
      showCardActions: true,
      cardTextClass: 'card-text pa-0',
    },
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    width: '70px',
    minWidth: '70px',
  } as Mo00611OnewayType,
  // 確定ボタン
  mo00609ConfirmOneway: {
    btnLabel: t('btn.save'),
    width: '70px',
    minWidth: '70px',
    // disabled: !(await hasRegistAuth(Or10827Const.LINK_AUTH_DAILY_TABLE)),
  } as Mo00609OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or10827Const.TAB.TAB_ID_DAILY_TABLE, title: t('label.daily-table') },
      { id: Or10827Const.TAB.TAB_ID_CONTENT, title: t('label.content') },
      {
        id: Or10827Const.TAB.TAB_ID_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES,
        title: t('label.daily-life-and-nursing-care-services-examples'),
      },
    ],
  } as Mo00043OnewayType,
  mo01338Threeway: {
    value: t('label.save-by-bussiness-unit'),
    customClass: new CustomClass({ itemClass: 'copyright-text' , itemStyle: 'color:  rgb(var(--v-theme-black-500));',}),
  } as Mo01338OnewayType,
})
const or21814_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
const or00734 = ref({ uniqueCpId: Or00734Const.CP_ID(1) })
const or00725 = ref({ uniqueCpId: Or00725Const.CP_ID(1) })
const or00725Ref = ref<{ save: AsyncFunction; _cleanEditFlag: () => void; _getGetEditFlgFlag: AsyncFunction }>()
const or00734Ref = ref<{ save: AsyncFunction; _cleanEditFlag: () => void; _getGetEditFlgFlag: AsyncFunction }>()
/**
 * gyoumuCom
 */
const gyoumuCom = useGyoumuCom()
/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00734Const.CP_ID(1)]: or00734.value,
  [Or00725Const.CP_ID(1)]: or00725.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
})

onMounted(() => {
  // データ変更確認ダイアログを初期化(i.cmn.10430)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
})
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10827StateType>({
  cpId: Or10827Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or10827Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * 関数
 **************************************************/
// 画面開閉フラグ
watch(
  () => local.mo00024.isOpen,
  async (newValue) => {
    if (!newValue) {
      local.mo00024.isOpen = true
      await close()
    }
  }
)
// メニュー切替
watch(
  () => local.mo00043Transfer.id,
  async (newValue) => {
    if (local.mo00043.id !== newValue) {
        local.mo00043Transfer.id = local.mo00043.id
        local.mo00043Change.id = newValue
        let auth = Or10827Const.LINK_AUTH_CONTENT
        if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_CONTENT) {
          auth = Or10827Const.LINK_AUTH_CONTENT
        } else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_DAILY_TABLE) {
          auth = Or10827Const.LINK_AUTH_DAILY_TABLE
        } else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_WEEK_TABLE) {
          auth = Or10827Const.LINK_AUTH_WEEK_TABLE
        }else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES) {
          auth = Or10827Const.LINK_AUTH_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES
        }
      if (
        !(await gyoumuCom.checkEdit(
          await editFlg(),
          await hasRegistAuth(auth),
          showConfirmMessageBox,
          showWarnMessageBox,
          insert,
          cancel
        ))
      ) {
        return
      }
      local.mo00043.id = newValue
      local.mo00043Transfer.id = local.mo00043.id
      localOneway.mo00024Oneway.mo01344Oneway.name = local.mo00043Transfer.id
      cleanEditFlag()
      switchTitle();
      localOneway.mo00609ConfirmOneway.disabled = !await hasRegistAuth(auth)
    }
  }
)
function switchTitle() {
  if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_WEEK_TABLE) {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.week-table')
  } else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_DAILY_TABLE) {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.daily-table')
    localOneway.mo01338Threeway.value = t('label.save-by-bussiness-unit')
  } else if (
    local.mo00043Transfer.id ===
    Or10827Const.TAB.TAB_ID_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES
  ) {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.content-master')
    localOneway.mo01338Threeway.value = t('label.system-common-save')
  } else {
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t(
      'label.daily-life-and-nursing-care-services-examples'
    )
  }
}
function cancel() {
  // キャンセル選択時は複写データの作成を行わずに終了する
  local.mo00043Change.id =  local.mo00043.id
  local.mo00043Transfer.id = local.mo00043Change.id
  localOneway.mo00024Oneway.mo01344Oneway.name = local.mo00043Transfer.id
  switchTitle()
}
/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  let auth = Or10827Const.LINK_AUTH_CONTENT
  if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_CONTENT) {
    auth = Or10827Const.LINK_AUTH_CONTENT
  } else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_DAILY_TABLE) {
    auth = Or10827Const.LINK_AUTH_DAILY_TABLE
  } else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_WEEK_TABLE) {
    auth = Or10827Const.LINK_AUTH_WEEK_TABLE
  }else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES) {
    auth = Or10827Const.LINK_AUTH_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES
  }
  if (
    !(await gyoumuCom.checkEdit(
      await editFlg(),
      await hasRegistAuth(auth),
      showConfirmMessageBox,
      showWarnMessageBox,
      insert
    ))
  ) {
    return
  }
  cleanEditFlag()
  setState({ isOpen: false })
}

/**
 * 保存ボタン押下
 */
async function insert() {
  if (local.mo00043.id === Or10827Const.TAB.TAB_ID_CONTENT) {
    return await or00725Ref.value?.save() ?? true
  } else if (local.mo00043.id === Or10827Const.TAB.TAB_ID_DAILY_TABLE) {
    return await or00734Ref.value?.save() ?? true
  }
  return true
}


function cleanEditFlag() {
  if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_CONTENT) {
    or00725Ref.value?._cleanEditFlag()
  } else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_DAILY_TABLE) {
    or00734Ref.value?._cleanEditFlag()
  }
}
/**
 * ナビゲーション制御領域のいずれかの編集フラグがON
 */
const editFlg = async function getGetEditFlgFlag() {
  if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_CONTENT) {
    return await or00725Ref.value?._getGetEditFlgFlag() ?? false
  } else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_DAILY_TABLE) {
    return await or00734Ref.value?._getGetEditFlgFlag() ?? false
  } else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_WEEK_TABLE) {
    return false;
  }else if (local.mo00043Transfer.id === Or10827Const.TAB.TAB_ID_DAILY_LIFE_AND_NURSING_CARE_SERVICES_EXAMPLES) {
    return false;
  }
  return false
}

/**
 * メッセージを表示する
 */
const showConfirmMessageBox = async () => {
  return await openInfoDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * 確認ダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}
/**
 * メッセージを表示する
 */
const showWarnMessageBox = async () => {
  return await openWarnDialog({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.w-com-10006'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
}

/**
 * メッセージを表示する
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openWarnDialog(state: Or21815StateType): Promise<Or21815EventType | undefined> {
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043Transfer"
        :oneway-model-value="localOneway.mo00043OneWay"
      ></base-mo00043>
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item value="GUI00987" >
          <g-custom-or00734
            v-if="local.mo00043.id === Or10827Const.TAB.TAB_ID_DAILY_TABLE"
            v-bind="or00734"
            ref="or00734Ref"
            :oneway-model-value="localOneway.or00734OneWay"
            :parent-unique-cp-id="props.uniqueCpId"
          >
          </g-custom-or00734>
        </c-v-window-item>
        <c-v-window-item value="GUI00935">
          <c-v-sheet class="content">
            <c-v-row class="ma-0">
              <c-v-col class="v-col pa-0">
                <g-custom-or00725
                  v-if="local.mo00043.id === Or10827Const.TAB.TAB_ID_CONTENT"
                  v-bind="or00725"
                  ref="or00725Ref"
                  class="intentionList"
                  :parent-unique-cp-id="props.uniqueCpId"
                >
                </g-custom-or00725></c-v-col
            ></c-v-row>
          </c-v-sheet>
        </c-v-window-item>
        <c-v-window-item value="GUI00988" class="windows-height">
          {{ t('label.daily-life-and-nursing-care-services-examples') }}</c-v-window-item
        >
      </c-v-window>
  <c-v-row no-gutters class="label-comment">
    <c-v-col style="padding: 8px 8px 8px 8px !important">
      <base-mo01338 :oneway-model-value="localOneway.mo01338Threeway" />
    </c-v-col>
  </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="insert"
        >
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_1" />
    <g-base-or21815 v-bind="or21815_1" />
  </base-mo00024>
</template>

<style scoped>
.windows-height{
  height:475px
}
.label-comment {
  color: rgb(var(--v-theme-black-536)) !important;
  margin: 0px 0px 0px 0px;
  height: 10px;
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: 82px;
}
</style>
