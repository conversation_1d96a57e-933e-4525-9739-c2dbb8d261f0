import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
  GuiStaffList select入力エンティティ
 */
export interface IGuiStaffListSelectInEntity extends InWebEntity {
  /**
   * 職員番号
   */
  staffNumber: string

  /**
   * 職員名
   */
  staffName: string

  /**
   * ログインID
   */
  loginId: string

  /**
   * 勤務形態
   */
  workingStyle: string

  /**
   * 権限
   */
  permission: string

  /**
   * アカウント
   */
  account: string

  /**
   * リミット（取得する最大件数）
   */
  limit: number

  /**
   * オフセット（取得開始位置。何件目より後ろを取得するか。）
   */
  offset: number
}
/**
  GuiStaffList select出力エンティティ
 */
export interface IGuiStaffListSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    total?: number // 総件数
    list?: staffManagementItemType[] // 職員管理のアイテムリスト
  }
}

/**
 * 職員管理のアイテム
 */
export interface staffManagementItemType {
  /**
   * 職員番号
   */
  staffNumber: string

  /**
   * 職員画像
   */
  staffImage: string

  /**
   * 職員名
   */
  staffName: string

  /**
   * ログインID
   */
  loginId: string

  /**
   * 職員ID
   */
  staffId: string

  /**
   * 勤務形態（区分値）
   */
  workingStyle: number

  /**
   * 権限（区分値）
   */
  permission: number

  /**
   * アカウント（区分値）
   */
  account: number

  /**
   * 登録日
   */
  created: string
}
