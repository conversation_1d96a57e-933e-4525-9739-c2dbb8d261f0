<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or41345Const } from '~/components/custom-components/organisms/Or41345/Or41345.constants'
import { Or41345Logic } from '~/components/custom-components/organisms/Or41345/Or41345.logic'
import type { Or41345OnewayType } from '~/types/cmn/business/components/Or41345Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * ケアの提供場所マスタ
 * KMD PHAM HO HAI DANG 2025/04/08 ADD START
 **************************************************/

/**
 *画面ID
 */
const screenId = 'GUI00831'

/**
 *ルーティング
 */
const routing = 'GUI00831/pinia'

/**
 *画面物理名
 */
const screenName = 'GUI00831'

/**
 *画面状態管理用操作変数
 */
const screenStore = useScreenStore()

/**
 *ユニークID管理用のリアクティブ参照
 */
const or41345 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/

/**
 *piniaの画面領域を初期化する
 */
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00831' },
})

/**************************************************
 * Props
 **************************************************/
/**
 *piniaから最上位の画面コンポーネント情報を取得する
 *これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or41345Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or41345.value.uniqueCpId = pageComponent.uniqueCpId

/**
 *自身のPinia領域をセットアップ
 */
const init = useInitialize({
  cpId: 'GUI00831',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or41345Const.CP_ID(0) }],
})
Or41345Logic.initialize(init.childCpIds.Or41345.uniqueCpId)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or41345Const.CP_ID(0)]: or41345.value,
})
/**
 *  or41345 onewayModelValue
 */
const or41345OnewayModel: Or41345OnewayType = {
  sysRyaku: '',
  isNull: false,
  kbnFlg: '5',
  tabId: Or41345Const.TAB.TAB_ID_CARE_OFFER_LOCATION,
}

/**
 *ダイアログ表示フラグ
 */
const showDialogOr41345DispFlg1 = computed(() => {
  // Or41345 のダイアログ開閉状態
  return Or41345Logic.state.get(or41345.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or41345)
 */
function onClickOr41345() {
  // Or41345のダイアログ開閉状態を更新する
  Or41345Logic.state.set({
    uniqueCpId: or41345.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- KMD PHAM HO HAI DANG 2025/04/16 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr41345()"
        >GUI00831_ケアの提供場所マスタ
      </v-btn>
      <g-custom-or-41345
        v-if="showDialogOr41345DispFlg1"
        v-bind="or41345"
        :oneway-model-value="or41345OnewayModel"
      />
    </c-v-col>
  </c-v-row>
  <!-- KMD PHAM HO HAI DANG 2025/04/16 ADD END-->
</template>
