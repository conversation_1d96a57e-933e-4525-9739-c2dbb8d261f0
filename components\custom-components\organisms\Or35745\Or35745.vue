<script setup lang="ts">
/**
 * Or35745:計画複写モーダル
 * GUI01150_計画複写
 *
 * @description
 * 「GUI01150_計画複写」モーダルを表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { isNaN } from 'lodash'
import { computed, nextTick, onBeforeMount, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import type { userListItemType } from '~/components/base-components/organisms/Or00249/Or00249.type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or35745Const } from '~/components/custom-components/organisms/Or35745/Or35745.constants'
import { Or35750Const } from '~/components/custom-components/organisms/Or35750/Or35750.constants'
import { Or35750Logic } from '~/components/custom-components/organisms/Or35750/Or35750.logic'
import { Or35760Const } from '~/components/custom-components/organisms/Or35760/Or35760.constants'
import { Or35761Const } from '~/components/custom-components/organisms/Or35761/Or35761.constants'
import { Or35763Const } from '~/components/custom-components/organisms/Or35763/Or35763.constants'
import { Or35765Const } from '~/components/custom-components/organisms/Or35765/Or35765.constants'
import { Or35765Logic } from '~/components/custom-components/organisms/Or35765/Or35765.logic'
import { Or52895Const } from '~/components/custom-components/organisms/Or52895/Or52895.constants'
import { Or52895Logic } from '~/components/custom-components/organisms/Or52895/Or52895.logic'
import { Or54307Const } from '~/components/custom-components/organisms/Or54307/Or54307.constants'
import { Or54307Logic } from '~/components/custom-components/organisms/Or54307/Or54307.logic'
import { OrX0115Const } from '~/components/custom-components/organisms/OrX0115/OrX0115.constants'
import { OrX0115Logic } from '~/components/custom-components/organisms/OrX0115/OrX0115.logic'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { OrX0155Const } from '~/components/custom-components/organisms/OrX0155/OrX0155.constants'
import { OrX0155Logic } from '~/components/custom-components/organisms/OrX0155/OrX0155.logic'
import { useCommonProps } from '~/composables/useCommonProps'
import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  PlanDuplicateCarePlanBundleUpdateInEntity,
  PlanDuplicateCarePlanBundleUpdateOutEntity,
} from '~/repositories/cmn/entities/PlanDuplicateCarePlanBundleUpdateEntity'
import type {
  PlanDuplicateCarePlanEtcBundleCheckUpdateInEntity,
  PlanDuplicateCarePlanEtcBundleCheckUpdateOutEntity,
} from '~/repositories/cmn/entities/PlanDuplicateCarePlanEtcBundleCheckUpdateEntity'
import type {
  PlanDuplicateCarePlanEtcDuplicateInfoSelectInEntity,
  PlanDuplicateCarePlanEtcDuplicateInfoSelectOutEntity,
} from '~/repositories/cmn/entities/PlanDuplicateCarePlanEtcDuplicateInfoSelectEntity'
import type {
  PlanDuplicateCarePlanIndividualCheckUpdateInEntity,
  PlanDuplicateCarePlanIndividualCheckUpdateOutEntity,
} from '~/repositories/cmn/entities/PlanDuplicateCarePlanIndividualCheckUpdateEntity'
import type {
  PlanDuplicateCarePlanNewUpdateInEntity,
  PlanDuplicateCarePlanNewUpdateOutEntity,
} from '~/repositories/cmn/entities/PlanDuplicateCarePlanNewUpdateEntity'
import type { PlanDuplicateEndUpdateInEntity } from '~/repositories/cmn/entities/PlanDuplicateEndUpdateEntity'
import type {
  KeikakushoUserInfo,
  PlanDuplicateInitialInfoSelectInEntity,
  PlanDuplicateInitialInfoSelectOutEntity,
  UserInfo,
} from '~/repositories/cmn/entities/PlanDuplicateInitialInfoSelectEntity'
import type {
  PlanDuplicateInsuranceiInvalidDuplicateUpdateInEntity,
  PlanDuplicateInsuranceiInvalidDuplicateUpdateOutEntity,
} from '~/repositories/cmn/entities/PlanDuplicateInsuranceiInvalidDuplicateUpdateEntity'
import type {
  PlanDuplicateInsuranceiInvalidUserInfoSelectInEntity,
  PlanDuplicateInsuranceiInvalidUserInfoSelectOutEntity,
} from '~/repositories/cmn/entities/PlanDuplicateInsuranceiInvalidUserInfoSelectEntity'
import type {
  PlanDuplicateUseSlipDuplicateUpdateInEntity,
  PlanDuplicateUseSlipDuplicateUpdateOutEntity,
} from '~/repositories/cmn/entities/PlanDuplicateUseSlipDuplicateUpdateEntity'
import type {
  PlanDuplicateUseSlipUserInfoSelectInEntity,
  PlanDuplicateUseSlipUserInfoSelectOutEntity,
} from '~/repositories/cmn/entities/PlanDuplicateUseSlipUserInfoSelectEntity'
import type {
  YearMonthFromValidPeriodIDSelectInEntity,
  YearMonthFromValidPeriodIDSelectOutEntity,
} from '~/repositories/cmn/entities/YearMonthFromValidPeriodIDSelectEntity'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo01352OnewayType } from '~/types/business/components/Mo01352Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { Or35745InParam, Or35745StateType } from '~/types/cmn/business/components/Or35745Type'
import {
  type Or35750OnewayType,
  type Or35750Type,
  Or35750Mode,
} from '~/types/cmn/business/components/Or35750Type.d'
import type { Or35760OnewayType, Or35760Type } from '~/types/cmn/business/components/Or35760Type'
import type { Or35761OnewayType, Or35761Type } from '~/types/cmn/business/components/Or35761Type'
import type { Or35763OnewayType, Or35763Type } from '~/types/cmn/business/components/Or35763Type'
import type { Or52895OnewayType } from '~/types/cmn/business/components/Or52895Type'
import type { Or54307OnewayType } from '~/types/cmn/business/components/Or54307Type'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import { CustomClass } from '~/types/CustomClassType'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import { useUserListInfo } from '~/utils/useUserListInfo'

const { t } = useI18n()
// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()
const { syscomUserSelectWatchFunc } = useUserListInfo()

/**************************************************
 * Props
 **************************************************/
// ①インターフェース定義
const props = defineProps(useCommonProps())

// ②デフォルト定義
const defaultOneWay = reactive({
  /** タブ */
  mo00043: {
    tabItems: [
      {
        id: Or35745Const.TAB_NUM_1,
        title: t('label.use-slip'),
        tooltipText: t('label.use-slip'),
        tooltipLocation: 'bottom',
      },
      {
        id: Or35745Const.TAB_NUM_2,
        title: t('label.insurance-other-than'),
        tooltipText: t('label.insurance-other-than'),
        tooltipLocation: 'bottom',
      },
      {
        id: Or35745Const.TAB_NUM_3,
        title: t('label.care-plan-etc'),
        tooltipText: t('label.care-plan-etc'),
        tooltipLocation: 'bottom',
      },
    ],
    minWidth: '44px',
    tabClass: 'pa-0',
    disabledTo: true,
  } as Mo00043OnewayType,
  /** ラベル */
  mo00615: {
    showItemLabel: true,
    showRequiredLabel: false,
    customClass: {
      outerStyle: 'background: none;',
    },
  } as Mo00615OnewayType,
  /** ラジオボタン */
  mo00039: {
    showItemLabel: false,
    customClass: new CustomClass({ outerClass: '' }),
  } as Mo00039OnewayType,
  /** セレクトフィールド */
  mo00040: {
    showItemLabel: false,
    customClass: {
      outerClass: '',
    },
  } as Mo00040OnewayType,
  /** 年月選択 */
  mo01352: {
    textFieldwidth: '112px',
  } as Mo01352OnewayType,
})

const defaultLocal = reactive({
  // ーーーーラベル
  /** 複写先年月に有効な要介護度が無い場合 */
  mo00039MissingCareLevel: '0',
  /** 複写を許可する保険外ｻｰﾋﾞｽ */
  mo00039AllowedNonInsuredServices: '1',
  /** 福祉用具貸与 設定単位数 */
  mo00039WelfareEquipmentRentalUnits: '1',
  /** 複写を許可する利用票 */
  mo00039AllowedUsageSlips: '1',
  /** 短期入所サービスの複写 */
  mo00039ShortTermStayServiceCopy: '1',
  /** 複写方法 */
  mo00039CopyMethod: Or35745Const.COPY_METHOD_ONE_WEEK,
  // ーーーーラベル
  // ーーーー年月選択
  mo01352CopySrc: {
    value: '',
  },
  mo01352CopyTargetFrom: {
    value: '',
  },
  mo01352CopyTargetTo: {
    value: '',
  },
  // ーーーー年月選択
  // －－－－カレンダー
  or35750CopySrc: {
    selectedDate: [] as string[],
  } as Or35750Type,
  or35750CopyTarget: {
    selectedDate: [] as string[],
  } as Or35750Type,
  // －－－－カレンダー
  // ーーーー計画書等複写元/先情報一覧
  or35760: {
    selectedItemId: '',
  } as Or35760Type,
  or35761: {
    selectedItemId: '',
  } as Or35761Type,
  // ーーーー計画書等複写元/先情報一覧
  /** 利用者一覧 */
  or35763: {
    selectedItemIds: [],
    filterText: [],
  } as Or35763Type,
  /** 担当ケアマネ */
  orx0145: { value: null } as OrX0145Type,
})

// ③双方向バインド用の内部変数
const local = reactive({
  ...defaultLocal,
})

// ④片方向バインド用の内部変数
const { setState } = useScreenOneWayBind<Or35745StateType>({
  cpId: Or35745Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    // ステート領域に更新があった場合、ローカル変数を更新
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or35745Const.DEFAULT.IS_OPEN

      if (value) {
        //初期化
        void nextTick(async () => {
          await doInit()
        })
      }
    },
    inParam: (value) => {
      inParam.value = value
    },
  },
})

const localOneWay = reactive({
  /** ダイアログ */
  mo00024: {
    width: '1280px',
    minWidth: '1280px',
    maxWidth: '100%',
    height: '95vh',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Or35745',
      toolbarTitle: t('label.care-plan-copy'),
      toolbarName: 'Or35745ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'card-text pa-0',
      cardActionClass: 'pa-0',
    },
  } as Mo00024OnewayType,
  /** 閉じるボタン */
  mo00611: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /** 複写ボタン */
  mo00609: {
    btnLabel: t('btn.copy'),
  } as Mo00609OnewayType,
  /** 一括複写ボタン */
  mo00609CopyAll: {
    btnLabel: t('btn.duplicate-all'),
  } as Mo00609OnewayType,
  /** 個別複写ボタン */
  mo00609CopyIndividual: {
    btnLabel: t('btn.duplicate-individual'),
  } as Mo00609OnewayType,
  /** タブ */
  mo00043: {
    ...defaultOneWay.mo00043,
  } as Mo00043OnewayType,
  // ーーーーラベル
  /** 複写先年月に有効な要介護度が無い場合 */
  mo00615MissingCareLevel: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.missing-care-level-at-destination'),
    itemLabelFontWeight: 'bold',
    itemLabelCustomClass: {
      labelStyle: 'white-space: break-spaces;',
    },
  } as Mo00615OnewayType,
  /** 複写元 */
  mo00615CopySrc: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.copy-source'),
    itemLabelFontWeight: 'bold',
    itemLabelCustomClass: {
      labelStyle: 'font-size: 16px;',
    },
  } as Mo00615OnewayType,
  /** 複写先 */
  mo00615CopyTarget: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.copy-target'),
    itemLabelFontWeight: 'bold',
    itemLabelCustomClass: {
      labelStyle: 'font-size: 16px;',
    },
  } as Mo00615OnewayType,
  /** 複写元情報 */
  mo00615CopySrcInfo: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.copy-source-info'),
    itemLabelFontWeight: 'bold',
    itemLabelCustomClass: {
      labelStyle: 'font-size: 16px;',
    },
  } as Mo00615OnewayType,
  /** 複写先情報 */
  mo00615CopyTargetInfo: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.copy-target-info'),
    itemLabelFontWeight: 'bold',
    itemLabelCustomClass: {
      labelStyle: 'font-size: 16px;',
    },
  } as Mo00615OnewayType,
  /** 複写を許可する保険外ｻｰﾋﾞｽ */
  mo00615AllowedNonInsuredServices: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.allowed-non-insured-services'),
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  /** 福祉用具貸与 設定単位数 */
  mo00615WelfareEquipmentRentalUnits: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.welfare-equipment-rental-units'),
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  /** 複写を許可する利用票 */
  mo00615AllowedUsageSlips: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.allowed-usage-slips'),
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  /** 短期入所サービスの複写 */
  mo00615ShortTermStayServiceCopy: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.short-term-stay-service-copy'),
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  /** 複写方法 */
  mo00615CopyMethod: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.copy-method'),
    itemLabelFontWeight: 'bold',
  } as Mo00615OnewayType,
  /** 複写期間 */
  mo00615CopyPeriod: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.copy-period'),
  } as Mo00615OnewayType,
  /** ～ */
  mo00615Wavy: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.wavy-withoutblank'),
  } as Mo00615OnewayType,
  /** 複写元となる一週間を選択してください。 */
  mo00615CopySourceInfoMsg: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.copy-source-info-msg'),
    itemLabelCustomClass: {
      labelStyle: 'line-height: 21px;',
    },
  } as Mo00615OnewayType,
  /** 複写先となる日を選択してください。 */
  mo00615CopyTargetInfoMsg: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.copy-target-info-msg'),
    itemLabelCustomClass: new CustomClass({
      labelStyle: 'white-space: break-spaces;',
    }),
  } as Mo00615OnewayType,
  /** 担当ケアマネ */
  mo00615Earrings: {
    ...defaultOneWay.mo00615,
    itemLabel: t('label.earrings'),
  } as Mo00615OnewayType,
  // ーーーーラベル
  // －－－－ラジオボタン
  /** 複写先年月に有効な要介護度が無い場合 */
  mo00039MissingCareLevel: {
    ...defaultOneWay.mo00039,
    name: 'missing-care-level',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  /** 複写を許可する保険外ｻｰﾋﾞｽ */
  mo00039AllowedNonInsuredServices: {
    ...defaultOneWay.mo00039,
    name: 'allowed-non-insured-services',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  /** 福祉用具貸与 設定単位数 */
  mo00039WelfareEquipmentRentalUnits: {
    ...defaultOneWay.mo00039,
    name: 'welfare-equipment-rental-units',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  /** 複写を許可する利用票 */
  mo00039AllowedUsageSlips: {
    ...defaultOneWay.mo00039,
    name: 'allowed-usage-slips',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  /** 短期入所サービスの複写 */
  mo00039ShortTermStayServiceCopy: {
    ...defaultOneWay.mo00039,
    name: 'short-term-stay-service-copy',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  /** 複写方法 */
  mo00039CopyMethod: {
    ...defaultOneWay.mo00039,
    name: 'copy-method',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  // －－－－ラジオボタン
  // －－－－カレンダー
  or35750CopySrc: {
    targetYM: '',
    mode: Or35750Mode.WEEK,
  } as Or35750OnewayType,
  or35750CopyTarget: {
    targetYM: '',
    mode: Or35750Mode.MULTI_DAY,
  } as Or35750OnewayType,
  // －－－－カレンダー
  // ーーーー計画書等複写元/先情報一覧
  or35760: {
    kikanItems: [],
    jigyoItems: [],
  } as unknown as Or35760OnewayType,
  or35761: {
    kikanItems: [],
    jigyoItems: [],
  } as unknown as Or35761OnewayType,
  // ーーーー計画書等複写元/先情報一覧
  // ーーーー利用者一覧
  or35763: {
    fromYM: '',
    toYMs: [],
    items: [],
  } as unknown as Or35763OnewayType,
  // ーーーー利用者一覧
  /** 担当ケアマネ */
  orx0145: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: false,
    customClass: {
      labelClass: 'mt-1',
      itemClass: 'ga-2',
    },
  } as OrX0145OnewayType,
  /** 要介護度の変更利用者一覧画面 */
  or54307: {} as Or54307OnewayType,
  /** 対象期間 */
  orx0115: {} as OrX0115OnewayType,
  /** 個別複写 履歴選択画面 */
  or52895: {} as Or52895OnewayType,
})

// ⑤ウォッチャー

/**************************************************
 * Emit
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or54307 = ref({ uniqueCpId: '' }) // GUI00620_計画複写：要介護度の変更利用者一覧画面
const orx0115 = ref({ uniqueCpId: '' }) // GUI00070_対象期間
const orx0145 = ref({ uniqueCpId: '' }) // 担当ケアマネ
const orX0155 = ref({ uniqueCpId: '' }) // 使用者
const or00249 = ref({ uniqueCpId: '' }) // 利用者選択
const or00094 = ref({ uniqueCpId: '' }) // 五十音ヘッドライン
const or21813 = ref({ uniqueCpId: '' }) // エラーダイアログ
const or21814 = ref({ uniqueCpId: '' }) // インフォメーションダイアログ
const or35750_1 = ref({ uniqueCpId: '' }) // カレンダー(利用票複写元)
const or35750_2 = ref({ uniqueCpId: '' }) // カレンダー(利用票複写先)
const or35750_3 = ref({ uniqueCpId: '' }) // カレンダー(保険外複写元)
const or35750_4 = ref({ uniqueCpId: '' }) // カレンダー(保険外複写先)
const or35760 = ref({ uniqueCpId: '' }) // 計画書等複写元情報一覧
const or35761 = ref({ uniqueCpId: '' }) // 計画書等複写先情報一覧
const or35763_1 = ref({ uniqueCpId: '' }) // 利用者一覧(利用票)
const or35763_2 = ref({ uniqueCpId: '' }) // 利用者一覧(保険外)
const or35765 = ref({ uniqueCpId: '' }) // 計画書等複写確認モーダル
const or52895 = ref({ uniqueCpId: '' }) // 個別複写 履歴選択画面

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or54307Const.CP_ID(1)]: or54307.value,
  [OrX0115Const.CP_ID(1)]: orx0115.value,
  [OrX0145Const.CP_ID(1)]: orx0145.value,
  [OrX0155Const.CP_ID(1)]: orX0155.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or35750Const.CP_ID(1)]: or35750_1.value,
  [Or35750Const.CP_ID(2)]: or35750_2.value,
  [Or35750Const.CP_ID(3)]: or35750_3.value,
  [Or35750Const.CP_ID(4)]: or35750_4.value,
  [Or35760Const.CP_ID(1)]: or35760.value,
  [Or35761Const.CP_ID(1)]: or35761.value,
  [Or35763Const.CP_ID(1)]: or35763_1.value,
  [Or35763Const.CP_ID(2)]: or35763_2.value,
  [Or35765Const.CP_ID(1)]: or35765.value,
  [Or52895Const.CP_ID(1)]: or52895.value,
})
useSetupChildProps(orX0155.value.uniqueCpId, {
  [Or00249Const.CP_ID(0)]: or00249.value,
  [Or00094Const.CP_ID(0)]: or00094.value,
})

// （利用者基本）利用者一覧詳細表示の初期設定
OrX0155Logic.state.set({
  uniqueCpId: orX0155.value.uniqueCpId,
  state: {
    regionKey: props.uniqueCpId, // メイン画面と連動しないようにする
    displayUserInfoSectionFlg: false, // 利用者情報セクションを表示しない
  },
})

// ------------- ダイアログ(自画面) -------------
/** ダイアログ */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or35745Const.DEFAULT.IS_OPEN,
})
watch(mo00024, async () => {
  if (mo00024.value.emitType === 'closeBtnClick') {
    mo00024.value.emitType = 'blank'
    await doCloseAction(false)
  }
})
// ------------- ダイアログ(自画面) -------------

// ------ エラーダイアログ ------
/** エラーダイアログの表示フラグ */
const showOr21813 = computed(() => {
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
/**
 * エラーダイアログのResolve
 */
let resolveErrorDialog: (value: string | PromiseLike<string>) => void
/**
 * エラーダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = (state: Or21813StateType) => {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise<string>((resolve) => {
    resolveErrorDialog = resolve
  })
}
/**
 * エラーダイアログ閉じる時の処理
 */
watch(
  () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
  (newValue) => {
    if (newValue) {
      return
    }

    const event = Or21813Logic.event.get(or21813.value.uniqueCpId)
    let result = Or35745Const.DIALOG_RESULT_CANCEL
    if (event?.firstBtnClickFlg) {
      result = Or35745Const.DIALOG_RESULT_YES
    }
    if (event?.secondBtnClickFlg) {
      result = Or35745Const.DIALOG_RESULT_NO
    }

    // 確認ダイアログのフラグをOFF
    Or21813Logic.event.set({
      uniqueCpId: or21813.value.uniqueCpId,
      events: {
        firstBtnClickFlg: false,
        secondBtnClickFlg: false,
        thirdBtnClickFlg: false,
        closeBtnClickFlg: false,
      },
    })

    if (resolveErrorDialog) {
      resolveErrorDialog(result)
    }
  }
)
// ------ エラーダイアログ ------

// ------ インフォメーションダイアログ ------
/** インフォメーションダイアログの表示フラグ */
const showOr21814 = computed(() => {
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 確認ダイアログのResolve
 */
let resolveInfoDialog: (value: string | PromiseLike<string>) => void
/**
 * 確認ダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openInfoDialog = (state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise<string>((resolve) => {
    resolveInfoDialog = resolve
  })
}
/**
 * 確認ダイアログ閉じる時の処理
 */
watch(
  () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
  (newValue) => {
    if (newValue) {
      return
    }

    const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
    let result = Or35745Const.DIALOG_RESULT_CANCEL
    if (event?.firstBtnClickFlg) {
      result = Or35745Const.DIALOG_RESULT_YES
    }
    if (event?.secondBtnClickFlg) {
      result = Or35745Const.DIALOG_RESULT_NO
    }

    // 確認ダイアログのフラグをOFF
    Or21814Logic.event.set({
      uniqueCpId: or21814.value.uniqueCpId,
      events: {
        firstBtnClickFlg: false,
        secondBtnClickFlg: false,
        thirdBtnClickFlg: false,
        closeBtnClickFlg: false,
      },
    })

    if (resolveInfoDialog) {
      resolveInfoDialog(result)
    }
  }
)
// ------ インフォメーションダイアログ ------

/** 要介護度の変更利用者一覧画面の表示フラグ */
const showOr54307 = computed(() => {
  return Or54307Logic.state.get(or54307.value.uniqueCpId)?.isOpen ?? false
})
/** 対象期間の表示フラグ */
const showOrX0115 = computed(() => {
  return OrX0115Logic.state.get(orx0115.value.uniqueCpId)?.isOpen ?? false
})
/** 計画書等複写確認モーダルの表示フラグ */
const showOr35765 = computed(() => {
  return Or35765Logic.state.get(or35765.value.uniqueCpId)?.isOpen ?? false
})
/**
 * 計画書等複写確認モーダルのResolve
 */
let resolveOr35765: (value: string | PromiseLike<string>) => void
/** 個別複写 履歴選択画面の表示フラグ */
const showOr52895 = computed(() => {
  return Or52895Logic.state.get(or52895.value.uniqueCpId)?.isOpen ?? false
})
/** Loadingダイアログ */
const showLoading = ref(false)

/** INパラメータ */
const inParam = ref<Or35745InParam | undefined>()
/** 登録権限 */
const regAuth = ref<boolean>(await hasRegistAuth())
/** 職員ID */
const shokuId = ref<string>('')
/** 期間管理フラグ */
const kikanFlg = ref<string>('')
/** 複写方法_更新回数 */
const copyModeModifiedCnt = ref<string>('')
/** 複写方法保険外_更新回数 */
const copyModeGaiModifiedCnt = ref<string>('')
/** 短期入所複写方法_更新回数 */
const copyTnkiModifiedCnt = ref<string>('')
/** 複写先利用票状況_更新回数 */
const copyJokyoModifiedCnt = ref<string>('')
/** 複写先保険外ｻｰﾋﾞｽ状況_更新回数 */
const copyJokyoGaiModifiedCnt = ref<string>('')
/** 福祉用具単位数_更新回数 */
const copyFygTaniModifiedCnt = ref<string>('')
/** タブ */
const mo00043 = ref<Mo00043Type>({ id: Or35745Const.DEFAULT.TAB_ID })
/** 表示中のタブID */
const currentTabId = ref<string>('')
/** 初期化中フラグ */
const initFlag = ref<boolean>(false)
/** タブデータ再取得フラグ */
const refreshTabFlag = ref<boolean>(false)

/**
 * AC001
 * 初期表示
 */
const doInit = async () => {
  if (inParam.value === undefined) {
    // 引継情報がオブジェクトではない場合、またはインスタンス化されていないオブジェクトの場合
    await openInfoDialog({
      dialogText: t('message.i-cmn-10402'),
    })
    await doCloseAction(true)
    return
  }
  initFlag.value = true

  // 職員ID = 共通情報.NDS共通初期データ.ユーザログイン情報.職員ID
  shokuId.value = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
  // 親画面情報.複写フラグ = 0
  inParam.value.copyFlg = '0'
  // 変数.複写元年月 = 親画面情報.複写元年月の左7桁
  local.mo01352CopySrc.value = inParam.value.sysYmd.substring(
    0,
    Math.min(7, inParam.value.sysYmd.length)
  )
  if (!inParam.value.tantoId || inParam.value.tantoId === '0') {
    // 変数.担当ケアマネIDが0またはNULLの場合
    inParam.value.tantoId = null
  }
  localOneWay.orx0145.selectedUserCounter = inParam.value.tantoId ?? ''
  if (local.mo01352CopySrc.value === '') {
    // 変数.複写元年月が空白の場合
    local.mo01352CopySrc.value = (systemCommonsStore.getSystemDate ?? '').substring(
      0,
      Math.min(7, inParam.value.sysYmd.length)
    )
  }

  // 親画面情報.50音フィルタにより、画面で共通部品.50音絞込設定
  local.or35763.filterText = inParam.value.gojuonFilter
  systemCommonsStore.setUserSelectFilterInitials(inParam.value.gojuonFilter, props.uniqueCpId)
  const gojuuOn = convertFilterText()

  // 計画複写初期情報を取得
  const req: PlanDuplicateInitialInfoSelectInEntity = {
    // ・画面名称 = 親画面情報.画面名称
    shoriName: inParam.value.screenName1,
    // ・機能ID = 親画面情報.機能ID
    kinouId: inParam.value.kinouId,
    // ・システムコード = 親画面情報.システムCD
    gsysCd: inParam.value.sysCd,
    // ・事業所CD = 親画面情報.事業所CD
    svJigyoCd: inParam.value.svJigyoCd,
    // ・ライセンスリスト = 親画面情報.ライセンスリスト
    glLicense: inParam.value.licenses,
    // ・職員ID = 親画面情報.職員ID
    shokuinId: shokuId.value,
    // ・引継画面名 = 親画面情報.画面名
    kinouName: inParam.value.screenName2,
    // ・複写元事業者ID = 親画面情報.複写元事業者ID
    shienId: inParam.value.shienId,
    // ・複写元年月 = 変数.複写元年月
    fromYm: local.mo01352CopySrc.value,
    // ・担当ケアマネID = 変数.担当ケアマネID
    tantoId: inParam.value.tantoId ?? undefined,
    // ・50音行番号 = 画面.50音の50音行番号
    gojuuOnRowNo: gojuuOn.gojuuOnRowNo,
    // ・50音母音 = 画面.50音の50音母音
    gojuuOnKana: gojuuOn.gojuuOnKana,
    // ・事業所ID = 親画面情報.事業所ＩＤ
    svJigyoId: inParam.value.svJigyoId,
    // ・施設ID = 親画面情報.施設ＩＤ
    shisetuId: inParam.value.shisetuId,
    // ・システム年月日 = 親画面情報.システム年月日
    appYmd: inParam.value.sysYmd,
    // ・利用者IDリスト = 親画面情報.利用者リスト
    userids: inParam.value.userIds,
  }
  const res: PlanDuplicateInitialInfoSelectOutEntity = await ScreenRepository.select(
    'planDuplicateInitialInfoSelect',
    req
  )

  if (!res.data) {
    return
  }
  const result = res.data

  // 初期リスト.タブータイトル = { "利用票" }の場合
  // 画面項目「利用票タブ」のみを表示する
  // 初期リスト.タブータイトル = { "保険外" }の場合
  // 画面項目「保険外タブ」のみを表示する
  // ...
  if (result.tabTitle.length > 0) {
    localOneWay.mo00043.tabItems = defaultOneWay.mo00043.tabItems.filter((item) =>
      result.tabTitle.includes(item.title)
    )
  }

  // 初期リスト.タブー選択区分が1の場合
  // 初期リスト.タブー選択区分が2の場合
  if (result.selKbnFlg === Or35745Const.TAB_NUM_1 || result.selKbnFlg === Or35745Const.TAB_NUM_2) {
    // 初期リスト.複写元休日情報により、複写元カレンダーを設定する
    localOneWay.or35750CopySrc.holidays = Or35750Logic.getHolidays(result.fromHolidayList)
    // ・複写元年月の最初の日曜日を初期選択
    const selectedDate = Or35750Logic.getFirstSundayOfMonth(local.mo01352CopySrc.value)
    if (selectedDate) {
      local.or35750CopySrc.selectedDate = [selectedDate]
    }

    // 初期リスト.複写先休日情報により、複写先カレンダーを設定する
    localOneWay.or35750CopyTarget.holidays = Or35750Logic.getHolidays(result.toHolidayList)
    // ・すべての日付を選択する。
    local.mo01352CopyTargetFrom.value = result.startYm
    local.mo01352CopyTargetTo.value = result.endYm
    const selectedDates = Or35750Logic.getAllDayOfMonth(result.startYm)
    if (selectedDates.length > 0) {
      local.or35750CopyTarget.selectedDate = [...selectedDates]
    }
    await nextTick()

    // 初期リスト.利用者リストの件数が0により大きい場合
    // 該当の利用者を選択
    localOneWay.or35763.fromYM = local.mo01352CopySrc.value
    localOneWay.or35763.toYMs = result.toYm ?? []
    localOneWay.or35763.items =
      result.selKbnFlg === Or35745Const.TAB_NUM_1
        ? (result.riyouUserList ?? [])
        : (result.hokengaiUserList ?? [])
    if (localOneWay.or35763.items.length > 0 && inParam.value.userId) {
      const targetUsers = localOneWay.or35763.items.filter((item) => {
        return item.id === inParam.value?.userId
      })
      if (targetUsers.length > 0) {
        local.or35763.selectedItemIds = [inParam.value.userId]
      }
    } else {
      local.or35763.selectedItemIds = []
    }

    // 他の初期値を設定
    local.mo00039AllowedNonInsuredServices =
      result.copyJokyoGai ?? defaultLocal.mo00039AllowedNonInsuredServices
    local.mo00039WelfareEquipmentRentalUnits =
      result.copyFygTani ?? defaultLocal.mo00039WelfareEquipmentRentalUnits
    local.mo00039AllowedUsageSlips = result.copyJokyo ?? defaultLocal.mo00039AllowedUsageSlips
    local.mo00039ShortTermStayServiceCopy =
      result.copyTnki ?? defaultLocal.mo00039ShortTermStayServiceCopy
    if (result.selKbnFlg === Or35745Const.TAB_NUM_1) {
      local.mo00039CopyMethod = result.copyMode ?? defaultLocal.mo00039CopyMethod
    } else {
      local.mo00039CopyMethod = result.copyModeGai ?? defaultLocal.mo00039CopyMethod
    }
  }
  // 初期リスト.タブー選択区分が3の場合
  else if (result.selKbnFlg === Or35745Const.TAB_NUM_3) {
    // 他の初期値を設定
    local.or35760.selectedItemId = ''
    localOneWay.or35760.kikanItems = result.kikanCopyFromList ?? []
    localOneWay.or35760.jigyoItems = result.jigyoCopyFromList ?? []
    local.or35761.selectedItemId = ''
    localOneWay.or35761.kikanItems = result.kikanCopyToList ?? []
    localOneWay.or35761.jigyoItems = result.jigyoCopyToList ?? []
  }

  // タブ3のユーザーリストを設定
  const keikakushoUserList = convertKeikakushoUserList(result.keikakushoUserList)
  OrX0155Logic.state.set({
    uniqueCpId: orX0155.value.uniqueCpId,
    state: {
      userList: keikakushoUserList,
    },
  })

  kikanFlg.value = result.kikanFlg
  copyModeModifiedCnt.value = result.copyMode_modifiedCnt
  copyModeGaiModifiedCnt.value = result.copyModeGai_modifiedCnt
  copyTnkiModifiedCnt.value = result.copyTnki_modifiedCnt
  copyJokyoModifiedCnt.value = result.copyJokyo_modifiedCnt
  copyJokyoGaiModifiedCnt.value = result.copyJokyoGai_modifiedCnt
  copyFygTaniModifiedCnt.value = result.copyFygTani_modifiedCnt

  mo00043.value.id = result.selKbnFlg ?? localOneWay.mo00043.tabItems[0].id
  currentTabId.value = mo00043.value.id

  initFlag.value = false
}

/**
 * AC002
 * 「利用票タブ」を選択
 */
const selectTab1 = async () => {
  // 計画複写利用票利用者情報検索処理を行う
  const gojuuOn = convertFilterText()
  const req: PlanDuplicateUseSlipUserInfoSelectInEntity = {
    // ・複写元事業者ID = 親画面情報.複写元事業者ID
    shienId: inParam.value?.shienId ?? '',
    // ・複写元年月 = 変数.複写元年月
    fromYm: local.mo01352CopySrc.value,
    // ・複写先開始年月 = 画面.期間開始年月
    startYm: local.mo01352CopyTargetFrom.value,
    // ・複写先終了年月 = 画面.期間終了年月
    endYm: local.mo01352CopyTargetTo.value,
    // ・担当ケアマネID = 変数.担当ケアマネID
    tantoId: inParam.value?.tantoId ?? undefined,
    // ・50音行番号 = 画面.50音の50音行番号
    gojuuOnRowNo: gojuuOn.gojuuOnRowNo,
    // ・50音母音 = 画面.50音の50音母音
    gojuuOnKana: gojuuOn.gojuuOnKana,
  }
  const res: PlanDuplicateUseSlipUserInfoSelectOutEntity = await ScreenRepository.select(
    'planDuplicateUseSlipUserInfoSelect',
    req
  )

  if (!res.data) {
    return
  }
  const result = res.data

  // 「画面項目定義書」シートにより、取得したデータを表示する
  localOneWay.or35763.fromYM = local.mo01352CopySrc.value
  localOneWay.or35763.toYMs = result.toYm ?? []
  localOneWay.or35763.items = result.riyouUserList ?? []
  if (localOneWay.or35763.items.length > 0 && inParam.value?.userId) {
    const targetUsers = localOneWay.or35763.items.filter((item) => {
      return item.id === inParam.value?.userId
    })
    if (targetUsers.length > 0) {
      local.or35763.selectedItemIds = [inParam.value.userId]
    }
  } else {
    local.or35763.selectedItemIds = []
  }
}

/**
 * AC003
 * 「保険外タブ」を選択
 */
const selectTab2 = async () => {
  // 計画複写保険外利用者情報検索処理を行う
  const gojuuOn = convertFilterText()
  const req: PlanDuplicateInsuranceiInvalidUserInfoSelectInEntity = {
    // ・複写元事業者ID = 親画面情報.複写元事業者ID
    shienId: inParam.value?.shienId ?? '',
    // ・複写元年月 = 変数.複写元年月
    fromYm: local.mo01352CopySrc.value,
    // ・複写先開始年月 = 画面.期間開始年月
    startYm: local.mo01352CopyTargetFrom.value,
    // ・複写先終了年月 = 画面.期間終了年月
    endYm: local.mo01352CopyTargetTo.value,
    // ・担当ケアマネID = 変数.担当ケアマネID
    tantoId: inParam.value?.tantoId ?? undefined,
    // ・50音行番号 = 画面.50音の50音行番号
    gojuuOnRowNo: gojuuOn.gojuuOnRowNo,
    // ・50音母音 = 画面.50音の50音母音
    gojuuOnKana: gojuuOn.gojuuOnKana,
  }
  const res: PlanDuplicateInsuranceiInvalidUserInfoSelectOutEntity = await ScreenRepository.select(
    'planDuplicateInsuranceiInvalidUserInfoSelect',
    req
  )

  if (!res.data) {
    return
  }
  const result = res.data

  // 「画面項目定義書」シートにより、取得したデータを表示する
  localOneWay.or35763.fromYM = local.mo01352CopySrc.value
  localOneWay.or35763.toYMs = result.toYm ?? []
  localOneWay.or35763.items = result.riyouUserList ?? []
  if (localOneWay.or35763.items.length > 0 && inParam.value?.userId) {
    const targetUsers = localOneWay.or35763.items.filter((item) => {
      return item.id === inParam.value?.userId
    })
    if (targetUsers.length > 0) {
      local.or35763.selectedItemIds = [inParam.value.userId]
    }
  } else {
    local.or35763.selectedItemIds = []
  }
}

/**
 * AC021
 * 「計画書等タブ」を選択
 */
const selectTab3 = async () => {
  // await syscomUserListFunc(false, props.uniqueCpId)

  let userId = undefined
  // 「計画書等タブ」は一回選択の場合
  // 変数.利用者ID = 画面.計画書等利用者一覧一行目の利用者ID
  // 「計画書等タブ」は二回以上選択、担当者ID変更されたの場合
  // 変数.利用者ID = 画面.計画書等利用者一覧一行目の利用者ID
  // 「計画書等タブ」は二回以上選択、担当者ID変更されない場合
  // 変数.利用者ID = 画面.計画書等利用者一覧選択行の利用者ID
  const or00249Data = Or00249Logic.data.get(or00249.value.uniqueCpId)
  const or00249State = Or00249Logic.state.get(or00249.value.uniqueCpId)
  if (or00249Data?.selectUserIndex !== undefined) {
    userId = or00249State?.userList?.[or00249Data.selectUserIndex]?.selfId
  } else {
    userId = or00249State?.userList?.[0]?.selfId
    Or00249Logic.data.set({
      uniqueCpId: or00249.value.uniqueCpId,
      value: {
        selectUserIndex: 0,
      },
    })
  }

  // 計画複写計画書等複写情報検索処理を行う
  const req: PlanDuplicateCarePlanEtcDuplicateInfoSelectInEntity = {
    // ・期間管理フラグ = 初期リスト.期間管理フラグ
    kikanFlg: kikanFlg.value,
    // ・事業所ID = 親画面情報.事業所ＩＤ
    svJigyoId: inParam.value?.svJigyoId ?? '',
    // ・利用者ID = 変数.利用者ID
    userId: userId ?? '',
  }
  const res: PlanDuplicateCarePlanEtcDuplicateInfoSelectOutEntity = await ScreenRepository.select(
    'planDuplicateCarePlanEtcDuplicateInfoSelect',
    req
  )

  if (!res.data) {
    return
  }
  const result = res.data

  // 「画面項目定義書」シートにより、取得したデータを表示する
  local.or35760.selectedItemId = ''
  localOneWay.or35760.kikanItems = result.kikanCopyFromList ?? []
  localOneWay.or35760.jigyoItems = result.jigyoCopyFromList ?? []
  local.or35761.selectedItemId = ''
  localOneWay.or35761.kikanItems = result.kikanCopyToList ?? []
  localOneWay.or35761.jigyoItems = result.jigyoCopyToList ?? []
}

/**
 * AC004
 * 「複写元年月前へアイコン」押下
 * AC005
 * 「複写元」変換
 * AC006
 * 「複写元年月次へアイコン」押下
 */
watch(
  () => local.mo01352CopySrc.value,
  (newValue, oldValue) => {
    // 範囲外の値から復旧した場合、何もしない
    const { year: oldYear, month: oldMonth } = Or35750Logic.getYearAndMonth(oldValue)
    if (oldYear === Or35745Const.YEAR_MIN && oldMonth < Or35745Const.MONTH_MIN) {
      return
    } else if (oldYear > Or35745Const.YEAR_MAX) {
      return
    }

    const { year, month } = Or35750Logic.getYearAndMonth(newValue)
    if (year === Or35745Const.YEAR_MIN && month < Or35745Const.MONTH_MIN) {
      // 画面.複写元年月が"1868/07"の場合,何も変更しない
      // ->"1868/07"より小さい場合、変更前の値に戻す
      local.mo01352CopySrc.value = oldValue
      return
    } else if (year > Or35745Const.YEAR_MAX) {
      // 画面.複写元年月が"2116/12"の場合,何も変更しない
      // ->"2116/12""より大きい場合、変更前の値に戻す
      local.mo01352CopySrc.value = oldValue
      return
    }

    // 複写元年月の最初の日曜日を初期選択
    const selectedDate = Or35750Logic.getFirstSundayOfMonth(newValue)
    if (selectedDate) {
      local.or35750CopySrc.selectedDate = [selectedDate]
    }

    // 情報検索処理を行う
    refreshTabFlag.value = true
  }
)

/**
 * AC007
 * 「複写方法」を選択
 */
const showCopyFromCalender = computed(() => {
  return local.mo00039CopyMethod === Or35745Const.COPY_METHOD_ONE_WEEK
})

/**
 * AC008
 * 「期間開始年月前へアイコン」押下
 * AC009
 * 「期間開始年月」変換
 * AC010
 * 「期間開始年月次へアイコン」押下
 */
watch(
  () => local.mo01352CopyTargetFrom.value,
  (newValue, oldValue) => {
    // 範囲外の値から復旧した場合、何もしない
    const { year: oldYear, month: oldMonth } = Or35750Logic.getYearAndMonth(oldValue)
    if (oldYear === Or35745Const.YEAR_MIN && oldMonth < Or35745Const.MONTH_MIN) {
      return
    } else if (oldYear > Or35745Const.YEAR_MAX) {
      return
    }

    const { year, month } = Or35750Logic.getYearAndMonth(newValue)
    if (year === Or35745Const.YEAR_MIN && month < Or35745Const.MONTH_MIN) {
      // 画面.期間開始年月が"1868/07"の場合,何も変更しない
      // ->"1868/07"より小さい場合、変更前の値に戻す
      local.mo01352CopyTargetFrom.value = oldValue
      return
    } else if (year > Or35745Const.YEAR_MAX) {
      // 画面.期間開始年月が"2116/12"の場合,何も変更しない
      // ->"2116/12""より大きい場合、変更前の値に戻す
      local.mo01352CopyTargetFrom.value = oldValue
      return
    }

    // 画面.期間開始年月が画面.期間終了年月より大きい場合
    // 画面.期間終了年月も同じ値に変更する
    const { year: toYear, month: toMonth } = Or35750Logic.getYearAndMonth(
      local.mo01352CopyTargetTo.value
    )
    if (year > toYear || (year === toYear && month > toMonth)) {
      local.mo01352CopyTargetTo.value = newValue
    }

    // 画面.期間開始年月から画面.期間終了年月の期間が24ヶ月より大きい場合
    // 画面.期間終了年月を画面.期間開始年月からの第24ヶ月に変更
    const maxToDate = new Date(year, month - 1, 1)
    maxToDate.setMonth(maxToDate.getMonth() + Or35745Const.MONTH_MAX_DIFF)
    const currentToDate = new Date(toYear, toMonth - 1, 1)
    if (currentToDate > maxToDate) {
      local.mo01352CopyTargetTo.value = Or35750Logic.formatYM(maxToDate)
    }

    // 画面.複写先カレンダー一覧ですべての日付を選択する
    const selectedDates = Or35750Logic.getAllDayOfMonth(newValue)
    if (selectedDates.length > 0) {
      local.or35750CopyTarget.selectedDate = [...selectedDates]
    }

    // 情報検索処理を行う
    refreshTabFlag.value = true
  }
)

/**
 * AC011
 * 「期間終了年月前へアイコン」押下
 * AC012
 * 「期間終了年月」変換
 * AC013
 * 「期間終了年月次へアイコン」押下
 */
watch(
  () => local.mo01352CopyTargetTo.value,
  (newValue, oldValue) => {
    // 範囲外の値から復旧した場合、何もしない
    // 変更前の期間終了年月
    const { year: oldYear, month: oldMonth } = Or35750Logic.getYearAndMonth(oldValue)
    // 期間開始年月
    const { year: fromYear, month: fromMonth } = Or35750Logic.getYearAndMonth(
      local.mo01352CopyTargetFrom.value
    )
    // 最大の期間終了年月日
    const maxToDate = new Date(fromYear, fromMonth - 1, 1)
    maxToDate.setMonth(maxToDate.getMonth() + Or35745Const.MONTH_MAX_DIFF)
    // 変更前の期間終了年月日
    const oldCurrentToDate = new Date(oldYear, oldMonth - 1, 1)
    if (oldYear === Or35745Const.YEAR_MIN && oldMonth < Or35745Const.MONTH_MIN) {
      return
    } else if (oldYear > Or35745Const.YEAR_MAX) {
      return
    } else if (oldYear < fromYear || (oldYear === fromYear && oldMonth < fromMonth)) {
      return
    } else if (oldCurrentToDate > maxToDate) {
      return
    }

    // 変更後の期間終了年月
    const { year, month } = Or35750Logic.getYearAndMonth(newValue)

    if (year === Or35745Const.YEAR_MIN && month < Or35745Const.MONTH_MIN) {
      // 画面.期間終了年月が"1868/07"の場合,何も変更しない
      // ->"1868/07"より小さい場合、変更前の値に戻す
      local.mo01352CopyTargetTo.value = oldValue
      return
    } else if (year > Or35745Const.YEAR_MAX) {
      // 画面.期間終了年月が"2116/12"の場合,何も変更しない
      // ->"2116/12""より大きい場合、変更前の値に戻す
      local.mo01352CopyTargetTo.value = oldValue
      return
    }

    // 画面.期間終了年月が画面.期間開始年月により小さい場合
    // 変換前の期間終了年月を戻し、何も変更しない
    if (year < fromYear || (year === fromYear && month < fromMonth)) {
      local.mo01352CopyTargetTo.value = oldValue
      return
    }

    // 画面.期間開始年月から画面.期間終了年月の期間が24ヶ月より大きい場合
    // 変換前の期間終了年月を戻し、何も変更しない
    const currentToDate = new Date(year, month - 1, 1)
    if (currentToDate > maxToDate) {
      local.mo01352CopyTargetTo.value = oldValue
      return
    }

    // 情報検索処理を行う
    refreshTabFlag.value = true
  }
)

/**
 * AC015
 * TAB 1,2
 * 「50音選択」押下
 */
watch(
  () => local.or35763.filterText,
  () => {
    // 情報検索処理を行う
    refreshTabFlag.value = true
  }
)
/**
 * AC015
 * TAB 3
 * 利用者変更
 */
syscomUserSelectWatchFunc(() => {
  // 情報検索処理を行う
  refreshTabFlag.value = true
}, props.uniqueCpId)

/**
 * AC017
 * 「閉じるボタン」押下
 * AC020
 * 「×ボタン」押下
 *
 * @param isForced - 強制閉じるかどうか
 */
const doCloseAction = async (isForced: boolean) => {
  if (!isForced) {
    let req: PlanDuplicateEndUpdateInEntity = {}

    if (currentTabId.value === Or35745Const.TAB_NUM_1) {
      // ・職員ID = 親画面情報.職員ID
      // ・引継画面名 = 親画面情報.画面名称
      // ・システムコード = 親画面情報.システムコード
      // ・複写方法 = 画面.複写方法
      // ・複写方法保険外 = NULL
      // ・短期入所複写方法 = 画面.短期入所サービスの複写
      // ・複写先利用票状況 = 画面.複写を許可する利用票
      // ・複写先保険外サービス状況 = NULL
      // ・福祉用具単位数 = 画面.福祉用具貸与 設定単位数
      req = {
        shokuinId: shokuId.value,
        kinouName: inParam.value?.screenName1,
        gsysCd: inParam.value?.sysCode,
        copyMode: local.mo00039CopyMethod,
        copyMode_modifiedCnt: copyModeModifiedCnt.value,
        copyTnki: local.mo00039ShortTermStayServiceCopy,
        copyTnki_modifiedCnt: copyTnkiModifiedCnt.value,
        copyJokyo: local.mo00039AllowedUsageSlips,
        copyJokyo_modifiedCnt: copyJokyoModifiedCnt.value,
        copyFygTani: local.mo00039WelfareEquipmentRentalUnits,
        copyFygTani_modifiedCnt: copyFygTaniModifiedCnt.value,
      }
    } else if (currentTabId.value === Or35745Const.TAB_NUM_2) {
      // ・職員ID = 親画面情報.職員ID
      // ・引継画面名 = 親画面情報.画面名称
      // ・システムコード = 親画面情報.システムコード
      // ・複写方法 = NULL
      // ・複写方法保険外 = 画面.複写方法
      // ・短期入所複写方法 = NULL
      // ・複写先利用票状況 = NULL
      // ・複写先保険外サービス状況 = 画面.複写を許可する保険外サービス
      // ・福祉用具単位数 = NULL
      req = {
        shokuinId: shokuId.value,
        kinouName: inParam.value?.screenName1,
        gsysCd: inParam.value?.sysCode,
        copyModeGai: local.mo00039CopyMethod,
        copyModeGai_modifiedCnt: copyModeGaiModifiedCnt.value,
        copyJokyoGai: local.mo00039AllowedNonInsuredServices,
        copyJokyoGai_modifiedCnt: copyJokyoGaiModifiedCnt.value,
      }
    } else if (currentTabId.value === Or35745Const.TAB_NUM_3) {
      // ・期間管理フラグ = 初期リスト.期間管理フラグ
      // ・事業所ID = 親画面情報.事業所ＩＤ
      // ・利用者ID = 変数.利用者ID
    }
    await ScreenRepository.update('planDuplicateEndUpdate', req)
  }

  setState({ isOpen: false })
}

/**
 * AC018
 * 「利用票タブ」の場合、「複写ボタン」押下
 * AC019
 * 「保険外タブ」の場合、「複写ボタン」押下
 */
const doSaveAction = async () => {
  const { year, month } = Or35750Logic.getYearAndMonth(local.mo01352CopySrc.value)
  const { year: fromYear, month: fromMonth } = Or35750Logic.getYearAndMonth(
    local.mo01352CopyTargetFrom.value
  )
  const { year: toYear, month: toMonth } = Or35750Logic.getYearAndMonth(
    local.mo01352CopyTargetTo.value
  )

  // 画面.複写元年月が画面.期間開始年月以上 かつ 画面.複写元年月が画面.期間終了年月 以下の場合
  if (
    ((year === fromYear && month >= fromMonth) || year > fromYear) &&
    ((year === toYear && month <= toMonth) || year < toYear)
  ) {
    // 以下の確認ダイアログを表示する
    await openInfoDialog({
      dialogText: t('message.i-cmn-11352'),
    })
    return
  }

  // 画面.複写元年月が"2000/01"により小さい場合
  if (year < Or35745Const.COPY_SRC_YEAR_MIN) {
    // 以下の確認ダイアログを表示する
    await openInfoDialog({
      dialogText: t('message.i-cmn-11353', [local.mo01352CopySrc.value]),
    })
    return
  }

  // 画面.期間開始年月が"2000/01"により小さい場合
  if (fromYear < Or35745Const.COPY_SRC_YEAR_MIN) {
    // 以下の確認ダイアログを表示する
    await openInfoDialog({
      dialogText: t('message.i-cmn-11356', [local.mo01352CopyTargetFrom.value]),
    })
    return
  }

  if (currentTabId.value === Or35745Const.TAB_NUM_1) {
    // 「利用票タブ」の場合

    // 変数.大きい年月が"2003/04"以上 かつ 変数.小さい年月が"2003/04"により小さい場合
    const date = new Date(year, month - 1, 1)
    const fromDate = new Date(fromYear, fromMonth - 1, 1)
    const sYear = date < fromDate ? year : fromYear
    const sMonth = date < fromDate ? month : fromMonth
    const lYear = date >= fromDate ? year : fromYear
    const lMonth = date >= fromDate ? month : fromMonth
    if (
      ((lYear === Or35745Const.USER_SLIP_COPY_YEAR &&
        lMonth >= Or35745Const.USER_SLIP_COPY_MONTH) ||
        lYear > Or35745Const.USER_SLIP_COPY_YEAR) &&
      ((sYear === Or35745Const.USER_SLIP_COPY_YEAR && sMonth < Or35745Const.USER_SLIP_COPY_MONTH) ||
        sYear < Or35745Const.USER_SLIP_COPY_YEAR)
    ) {
      // 以下の確認ダイアログを表示する
      await openErrorDialog({
        dialogText: t('message.e-cmn-41745', [
          local.mo01352CopySrc.value,
          local.mo01352CopyTargetFrom.value,
        ]),
      })
      return
    }

    // 年月から有効期間IDを取得する
    const ymReq: YearMonthFromValidPeriodIDSelectInEntity = {
      // ・複写元年月 = 画面.複写元年月
      fromYm: local.mo01352CopySrc.value,
      // ・複写先開始年月 = 画面.期間開始年月
      startYm: local.mo01352CopyTargetFrom.value,
      // ・複写先終了年月 = 画面.期間終了年月
      endYm: local.mo01352CopyTargetTo.value,
    }

    const ymRes: YearMonthFromValidPeriodIDSelectOutEntity = await ScreenRepository.select(
      'yearMonthFromValidPeriodIDSelect',
      ymReq
    )

    // 上記取得した複写元年月有効期間IDが複写先開始年月有効期間IDにより大きい
    // または 複写元年月有効期間IDが複写先終了年月有効期間IDにより大きい場合
    let copySrcTermId = undefined
    let copyTargetFromTermId = undefined
    let copyTargetToTermId = undefined
    if (ymRes.data) {
      const result = ymRes.data
      copySrcTermId = result.fromYmTermid
      copyTargetFromTermId = result.startYmTermid
      copyTargetToTermId = result.endYmTermid
    }
    if (
      !copySrcTermId ||
      !copyTargetFromTermId ||
      !copyTargetToTermId ||
      copySrcTermId > copyTargetFromTermId ||
      copySrcTermId > copyTargetToTermId
    ) {
      // 以下の確認ダイアログを表示する
      await openInfoDialog({
        dialogText: t('message.i-cmn-11355'),
      })
      return
    }
  }

  // 変数.利用者ID
  const targetUserIds: string[] = []
  // 変数.複写可能利用者リスト
  const targetUserInfos: UserInfo[] = []

  // 画面.利用票利用者一覧セクションのデータでループし、以下の処理を実行する
  localOneWay.or35763.items.forEach((item) => {
    if (!local.or35763.selectedItemIds.includes(item.id)) {
      // ループ行.利用票選択が1以外の場合
      // 次のループへ
      return
    }

    if (Number(item.id) <= 0) {
      // ループ行.利用票利用者IDが0以下の場合
      // 次のループへ
      return
    }

    if (!item.plan0 || Number(item.plan0) === 0) {
      // ループ行.利用票（複写元）がNULL
      // または ループ行.利用票（複写元）が0の場合
      // 次のループへ
      return
    }

    // ループ行.利用票（１）からループ行.利用票（２４）（複写先期間内のみ）までループし、以下の処理を実行する。
    // 変数.作成状況 = 0
    let createStatus = 0
    let index = Or35745Const.PLAN_START
    while (index <= Or35745Const.PLAN_END) {
      const key = ('plan' + index) as keyof UserInfo
      const tempStatus = item[key]
      if (tempStatus && Number(tempStatus) > createStatus) {
        createStatus = Number(tempStatus)
      }

      index++
    }

    if (currentTabId.value === Or35745Const.TAB_NUM_1) {
      // 「利用票タブ」の場合
      // (画面.複写を許可する利用票が1 かつ 変数.作成状況が0)
      // または (画面.複写を許可する利用票が2 かつ 変数.作成状況が1以下)
      // または (画面.複写を許可する利用票が3 かつ 変数.作成状況が2以下)の場合
      if (createStatus < Number(local.mo00039AllowedUsageSlips)) {
        // ・変数.複写可能な利用者件数カウントアップ
        // ...
        targetUserIds.push(item.id)
        targetUserInfos.push(item)
      }
    } else if (currentTabId.value === Or35745Const.TAB_NUM_2) {
      // 「保険外タブ」の場合
      // (画面.複写を許可する保険外サービスが1 かつ 変数.作成状況が0)
      // または 画面.複写を許可する保険外サービスが2 の場合
      if (createStatus < Number(local.mo00039AllowedNonInsuredServices)) {
        // ・変数.複写可能な利用者件数カウントアップ
        // ...
        targetUserIds.push(item.id)
        targetUserInfos.push(item)
      }
    }
  })

  // 変数.複写可能な利用者件数が0以下の場合
  if (targetUserIds.length <= 0) {
    // 以下の確認ダイアログを表示する
    await openInfoDialog({
      dialogText: t('message.i-cmn-11354'),
    })
    return
  }

  // 変数.複写元スタート日
  let copyStartDate = undefined
  if (local.mo00039CopyMethod === Or35745Const.COPY_METHOD_ONE_WEEK) {
    // 画面.複写方法が1の場合
    // ・変数.複写元スタート日 = 画面.複写元選択カレンダー一覧で選択した開始日の日
    copyStartDate = local.or35750CopySrc.selectedDate[0].slice(-2)
  }

  // 変数.複写先日付選択リスト
  // ・変数.複写先日付選択リスト = 画面.複写先カレンダー一覧の情報
  const copyTargetDates: string[] = []
  local.or35750CopyTarget.selectedDate.forEach((selectedDate) => {
    copyTargetDates.push(selectedDate.slice(-2))
  })

  const gojuuOn = convertFilterText()
  if (currentTabId.value === Or35745Const.TAB_NUM_1) {
    // 「利用票タブ」の場合
    // 計画複写利用票複写処理を行う
    const req: PlanDuplicateUseSlipDuplicateUpdateInEntity = {
      // ・システム年月日 ＝ 親画面情報.システム年月日
      appYmd: inParam.value?.sysYmd,
      // ・複写元事業者ID ＝ 親画面情報.複写元事業者ID
      shienId: inParam.value?.svJigyoId,
      // ・複写元年月 ＝ 画面.複写元年月
      fromYm: local.mo01352CopySrc.value,
      // ・複写先開始年月 ＝ 画面.期間開始年月
      startYm: local.mo01352CopyTargetFrom.value,
      // ・複写先終了年月 ＝ 画面.期間終了年月
      endYm: local.mo01352CopyTargetTo.value,
      // ・週間パターン時の開始日 ＝ 変数.複写元スタート日
      stDay: copyStartDate,
      // ・複写先月の複写対象日 ＝ 変数.複写先日付選択リスト
      days: copyTargetDates,
      // ・複写モード ＝ 画面.利用票複写モード
      flags: local.mo00039MissingCareLevel,
      // ・複写方法 ＝ 画面.複写方法
      copyMode: local.mo00039CopyMethod,
      // ・短期入所複写方法 ＝ 画面.短期入所サービスの複写
      copyTnki: local.mo00039ShortTermStayServiceCopy,
      // ・複写先利用票状況 ＝ 画面.複写を許可する利用票
      copyJokyo: local.mo00039AllowedUsageSlips,
      // ・福祉用具単位数 ＝ 画面.福祉用具貸与 設定単位数
      copyFygTani: local.mo00039WelfareEquipmentRentalUnits,
      // ・利用者IDリスト ＝ 変数.利用者ID
      userId: targetUserIds,
      // 複写可能利用者リスト ＝ 変数.複写可能利用者リスト
      copyUserList: targetUserInfos,
      // 担当ケアマネID ＝ 画面.担当者ID
      tantoId: '',
      // 50音行番号 ＝ 画面.50音の50音行番号
      gojuuOnRowNo: gojuuOn.gojuuOnRowNo,
      // 50音母音 ＝ 画面.50音の50音母音
      gojuuOnKana: gojuuOn.gojuuOnKana,
    }
    const res: PlanDuplicateUseSlipDuplicateUpdateOutEntity = await ScreenRepository.update(
      'planDuplicateUseSlipDuplicateUpdate',
      req
    )
    let successCount = 0
    let difCnt = 0
    let calCount = 0
    if (res.data) {
      if (!isNaN(Number(res.data.trueCnt))) {
        successCount = Number(res.data.trueCnt)
      }
      if (!isNaN(Number(res.data.difCnt))) {
        difCnt = Number(res.data.difCnt)
      }
      calCount = res.data.calcSetList?.length ?? 0
    }

    if (successCount > 0 && calCount > 0) {
      // AC018-6.成功行数が0により大きい
      // かつ AC018-6.計算リストの件数0により大きい場合

      // Loadingダイアログを表示する。
      showLoading.value = true

      // TODO APIなし
      // 算定利用者一括計算処理を行う

      // Loadingダイアログを非表示する。
      showLoading.value = false
    } else if (successCount <= 0) {
      // 成功行数が0以下の場合
      // 以下の確認ダイアログを表示する
      await openInfoDialog({ dialogText: t('message.i-cmn-10523') })
      return
    }

    if (difCnt <= 0) {
      // 変更行数が0以下の場合 処理終了
      return
    }

    // 画面.複写方法が1の場合	-	変数.複写元開始日付 = 画面.複写元年月 + "/" + 変数.複写元スタート日
    // 画面.複写方法が1の場合	-	変数.複写元終了日付 = 画面.複写元年月 + "/" + (変数.複写元スタート日 + 6)
    // 画面.複写方法が 2 または 3 の場合	-	変数.複写元開始日付 = 画面.複写元年月 + "/01"
    // 画面.複写方法が 2 または 3 の場合	-	変数.複写元終了日付 = 画面.複写元年月 + "/31"
    // -	-	変数.複写先開始日付 = 画面.期間開始年月 + "/01"
    // -	-	変数.複写先終了日付 = 画面.期間終了年月 + "/31"

    // GUI00620_計画複写：要介護度の変更利用者一覧画面をポップアップで起動する
    // ・複写元の開始日付(fs_ymd) ＝ 変数.複写元開始日付
    localOneWay.or54307.fsYmd = local.mo01352CopySrc.value + '/' + (copyStartDate ?? '01')
    // ・複写元の終了日付(fe_ymd) ＝ 変数.複写元終了日付
    localOneWay.or54307.feYmd =
      local.mo01352CopySrc.value +
      '/' +
      (copyStartDate !== undefined ? String(Number(copyStartDate + 6)).padStart(2, '0') : '31')
    // ・複写先の開始日付(ts_ymd) ＝ 変数.複写先開始日付
    localOneWay.or54307.tsYmd = local.mo01352CopyTargetFrom.value + '/01'
    // ・複写先の終了日付(te_ymd) ＝ 変数.複写先終了日付
    localOneWay.or54307.teYmd = local.mo01352CopyTargetTo.value + '/31'
    // ・法人ID(str_parm.def_houjin_id) ＝ 親画面情報.法人ID
    localOneWay.or54307.houjinId = inParam.value?.houjinId ?? ''
    // ・施設ID(str_parm.def_shisetu_id) ＝ 親画面情報.施設ＩＤ
    localOneWay.or54307.shisetuId = inParam.value?.shisetuId ?? ''
    // ・事業所ID(str_parm.def_sv_jigyo_id) ＝ 親画面情報.事業所ＩＤ
    localOneWay.or54307.svJigyoId = inParam.value?.svJigyoId ?? ''
    // ・ログイン職員ID(str_parm.shoku_id) ＝ 親画面情報.担当ケアマネID
    localOneWay.or54307.shokuId = '' // TBD
    // ・利用者id(str_parm.userid) ＝ 親画面情報.利用者ID
    // INパラメータなし
    // ・利用者IDリスト(userid) ＝ AC018-6のOUTPUT情報.変更した利用者IDのリスト
    const userIds: { userId: string }[] = []
    res.data.difUserid.forEach((diffUser) => {
      userIds.push({
        userId: diffUser,
      })
    })
    localOneWay.or54307.userIdList = userIds
    // ・複写先の年月リスト(yymm_ym) ＝ AC018-6のOUTPUT情報.複写した年月のリスト
    const yymmYms: { yymmYm: string }[] = []
    res.data.cpUsermonth.forEach((cpUsermonthItem) => {
      yymmYms.push({
        yymmYm: cpUsermonthItem,
      })
    })
    localOneWay.or54307.yymmYmList = yymmYms

    Or54307Logic.state.set({
      uniqueCpId: or54307.value.uniqueCpId,
      state: { isOpen: true },
    })
  } else if (currentTabId.value === Or35745Const.TAB_NUM_2) {
    // 「保険外タブ」の場合
    // 計画複写保険外複写処理を行う
    const req: PlanDuplicateInsuranceiInvalidDuplicateUpdateInEntity = {
      // ・複写元事業者ID ＝ 親画面情報.複写元事業者ID
      shienId: inParam.value?.svJigyoId,
      // ・複写元年月 ＝ 画面.複写元年月
      fromYm: local.mo01352CopySrc.value,
      // ・複写先開始年月 ＝ 画面.期間開始年月
      startYm: local.mo01352CopyTargetFrom.value,
      // ・複写先終了年月 ＝ 画面.期間終了年月
      endYm: local.mo01352CopyTargetTo.value,
      // ・週間パターン時の開始日 ＝ 変数.複写元スタート日
      stDay: copyStartDate,
      // ・複写先月の複写対象日 ＝ 変数.複写先日付選択リスト
      days: copyTargetDates,
      // ・複写方法保険外 ＝ 画面.複写方法
      copyModeGai: local.mo00039CopyMethod,
      // 複写先保険外サービス状況 ＝ 画面.複写を許可する保険外サービス
      copyJokyoGai: local.mo00039AllowedNonInsuredServices,
      // ・利用者IDリスト ＝ 変数.利用者ID
      userId: targetUserIds,
      // 複写可能利用者リスト ＝ 変数.複写可能利用者リスト
      copyUserList: targetUserInfos,
      // 担当ケアマネID ＝ 画面.担当者ID
      tantoId: '',
      // 50音行番号 ＝ 画面.50音の50音行番号
      gojuuOnRowNo: gojuuOn.gojuuOnRowNo,
      // 50音母音 ＝ 画面.50音の50音母音
      gojuuOnKana: gojuuOn.gojuuOnKana,
    }
    const res: PlanDuplicateInsuranceiInvalidDuplicateUpdateOutEntity =
      await ScreenRepository.update('planDuplicateInsuranceiInvalidDuplicateUpdate', req)
    let successCount = 0
    if (res.data && !isNaN(Number(res.data.trueCnt))) {
      successCount = Number(res.data.trueCnt)
    }

    if (successCount > 0) {
      // 成功行数が0により大きい場合
      // 親画面情報.複写フラグ = 1に設定
      inParam.value!.copyFlg = '1'

      // 以下の確認ダイアログを表示する
      await openInfoDialog({ dialogText: t('message.i-cmn-10520') })

      // OUTPUT情報.利用者リストにより、画面を再表示する
      localOneWay.or35763.items = res.data.userList ?? []
      return
    } else {
      // 成功行数が0以下の場合
      // 以下の確認ダイアログを表示する
      await openInfoDialog({ dialogText: t('message.i-cmn-10522') })
      return
    }
  }
}

/**
 * AC022
 * 計画対象期間検索アイコンボタン押下
 */
const clickPlanPeriod = () => {
  // GUI00070_対象期間をポップアップで起動する。
  const selectedItemId = local.or35761.selectedItemId
  const selectedItem = localOneWay.or35761.kikanItems.find((item) => item.sc1Id === selectedItemId)
  localOneWay.orx0115.kindId = selectedItem?.syubetsuId ?? ''
  localOneWay.orx0115.sc1Id = selectedItem?.sc1Id ?? ''

  OrX0115Logic.state.set({
    uniqueCpId: orx0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * GUI00070_対象期間閉じる後の処理
 */
watch(
  () => OrX0115Logic.state.get(orx0115.value.uniqueCpId)?.isOpen,
  (newValue, oldValue) => {
    if (newValue) {
      return
    }

    if (oldValue) {
      // タブデータの再取得
      refreshTabFlag.value = true
    }
  }
)

/**
 * AC023
 * 一括複写ボタン押下
 * AC024
 * 個別複写ボタン押下
 *
 * @param copyAll - true:一括複写 / false:個別複写
 */
const doCopyEtcAction = async (copyAll: boolean) => {
  // 複写元セクションデータなしの場合,処理終了
  if (!local.or35760.selectedItemId) {
    return
  }

  // 複写元情報取得
  const copyFromItemId = local.or35760.selectedItemId
  const copyFromKikanItem = localOneWay.or35760.kikanItems.find(
    (item) => item.sc1Id === copyFromItemId
  )
  const copyFromJigyoItem = localOneWay.or35760.jigyoItems.find(
    (item) => item.svJigyoId === copyFromItemId
  )
  // 複写先情報取得
  const copyToItemId = local.or35761.selectedItemId
  let copyToKikanItem = localOneWay.or35761.kikanItems.find((item) => item.sc1Id === copyToItemId)
  const copyToJigyoItem = localOneWay.or35761.jigyoItems.find(
    (item) => item.svJigyoId === copyToItemId
  )

  // Loadingダイアログを表示する
  showLoading.value = true

  // チェック処理結果
  let checkRes:
    | PlanDuplicateCarePlanEtcBundleCheckUpdateOutEntity
    | PlanDuplicateCarePlanIndividualCheckUpdateOutEntity
    | undefined = undefined
  if (copyAll) {
    // 一括複写

    // 計画複写計画書等一括複写チェック処理を行う
    // ・複写元事業者ID = 変数.複写元事業者ID
    // ・複写先事業者ID = 親画面情報.事業所ＩＤ
    // ・職員ID = 親画面情報.職員ID
    // ・システムコード = 親画面情報.システムCD
    // ・機能情報List = 親画面情報.機能情報List
    const checkReq: PlanDuplicateCarePlanEtcBundleCheckUpdateInEntity = {
      /** 複写元事業者ID */
      svJigyoIdFrom: copyFromKikanItem?.svJigyoId ?? copyFromJigyoItem?.svJigyoId ?? '',
      /** 複写先事業者ID */
      svJigyoIdTo: inParam.value?.svJigyoId ?? '',
      /** 職員ID */
      shokuinId: shokuId.value,
      /** システムコード */
      gsysCd: inParam.value?.sysCd,
      /** 機能情報List */
      gdsKinouName: inParam.value?.kinouNames,
    }
    checkRes = await ScreenRepository.update<PlanDuplicateCarePlanEtcBundleCheckUpdateOutEntity>(
      'planDuplicateCarePlanEtcBundleCheckUpdate',
      checkReq
    )
  } else {
    // 個別複写

    // 計画複写計画書等個別複写チェック処理を行う
    // ・複写元事業者ID ＝ 変数.複写元事業者ID
    // ・複写先事業者ID ＝ 親画面情報.事業所ＩＤ
    // ・職員ID ＝ 親画面情報.職員ID
    // ・システムコード ＝ 親画面情報.システムCD
    // ・機能情報List ＝ 親画面情報.機能情報List
    const checkReq: PlanDuplicateCarePlanIndividualCheckUpdateInEntity = {
      /** 複写元事業者ID */
      svJigyoIdFrom: copyFromKikanItem?.svJigyoId ?? copyFromJigyoItem?.svJigyoId ?? '',
      /** 複写先事業者ID */
      svJigyoIdTo: inParam.value?.svJigyoId ?? '',
      /** 職員ID */
      shokuinId: shokuId.value,
      /** システムコード */
      gsysCd: inParam.value?.sysCd,
      /** 機能情報List */
      gdsKinouName: inParam.value?.kinouNames,
    }
    checkRes = await ScreenRepository.update<PlanDuplicateCarePlanIndividualCheckUpdateOutEntity>(
      'planDuplicateCarePlanIndividualCheckUpdate',
      checkReq
    )
  }
  // Loadingダイアログを非表示する
  showLoading.value = false

  // チェック結果
  const checkResult = checkRes.data?.chkResult ?? ''
  // メッセージID
  const messageId = checkRes.data?.messageId ?? ''

  if (checkResult === Or35745Const.CHK_RESULT_NG) {
    // チェック結果が-1 : NGの場合
    if (messageId === Or35745Const.MSG_ID_E_CMN_40447) {
      // メッセージIDが"e.cmn.40447"の場合
      // 以下の確認ダイアログを表示する
      await openErrorDialog({
        dialogText: t('message.e-cmn-40447'),
      })
      return
    } else if (messageId === Or35745Const.MSG_ID_E_CMN_40448) {
      // メッセージIDが"e.cmn.40448"の場合
      // 以下の確認ダイアログを表示する
      await openErrorDialog({
        dialogText: t('message.e-cmn-40448'),
      })
      return
    } else if (messageId === Or35745Const.MSG_ID_E_CMN_40449) {
      // メッセージIDが"e.cmn.40449"の場合
      // 以下の確認ダイアログを表示する
      await openErrorDialog({
        dialogText: t('message.e-cmn-40449'),
      })
      return
    }
  }
  if (checkResult !== Or35745Const.CHK_RESULT_OK) {
    // チェック結果が1 : OKの場合,処理続き
    return
  }

  // 変数.利用者ID
  let selectedUser = undefined
  let userId = undefined
  const or00249Data = Or00249Logic.data.get(or00249.value.uniqueCpId)
  const or00249State = Or00249Logic.state.get(or00249.value.uniqueCpId)
  if (or00249Data?.selectUserIndex) {
    selectedUser = or00249State?.userList?.[or00249Data.selectUserIndex]
  } else {
    selectedUser = or00249State?.userList?.[0]
  }
  userId = selectedUser?.selfId

  // 複写先セクションデータ件数が1により小さい場合
  // 期間管理フラグが"1":期間管理するの場合
  if (!local.or35761.selectedItemId && kikanFlg.value === Or35745Const.PERIOD_MANAGE_YES) {
    // 以下の確認ダイアログを表示する
    const result = await openInfoDialog({
      dialogText: t('message.i-cmn-10198'),
      firstBtnLabel: t('btn.yes'),
      secondBtnLabel: t('btn.no'),
      secondBtnType: 'normal3',
    })
    if (result !== Or35745Const.DIALOG_RESULT_YES) {
      return
    }

    // 計画複写計画書等計画期間新規処理を行う
    // ・法人ID = 親画面情報.法人ID
    // ・施設ID = 親画面情報.施設ＩＤ
    // ・事業者ID = 親画面情報.事業所ＩＤ
    // ・利用者ID = 画面.計画書等利用者一覧選択行の利用者ID
    // ・計画期間開始日 = 変数.複写元開始日
    // ・計画期間終了日 = 変数.複写元終了日
    const newReq: PlanDuplicateCarePlanNewUpdateInEntity = {
      /** 法人ID */
      houjinId: inParam.value?.houjinId ?? '',
      /** 施設ID */
      shisetuId: inParam.value?.shisetuId ?? '',
      /** 事業者ID */
      svJigyoId: inParam.value?.svJigyoId ?? '',
      /** 利用者ID */
      userId: userId ?? '',
      /** 計画期間開始日 */
      startYmd: copyFromKikanItem?.startYmd ?? '',
      /** 計画期間終了日 */
      endYmd: copyFromKikanItem?.endYmd ?? '',
    }

    const newRes: PlanDuplicateCarePlanNewUpdateOutEntity = await ScreenRepository.update(
      'planDuplicateCarePlanNewUpdate',
      newReq
    )

    // OUTPUT情報により、画面.複写先セクションを再表示する
    localOneWay.or35761.kikanItems = newRes.data?.kikanCopyToList ?? []
    local.or35761.selectedItemId = localOneWay.or35761.kikanItems[0]?.sc1Id ?? ''
    copyToKikanItem = localOneWay.or35761.kikanItems[0]
  }

  if (copyAll) {
    // 一括複写

    // 変数 介護予防支援複写可能
    const canCopy =
      (checkRes as PlanDuplicateCarePlanEtcBundleCheckUpdateOutEntity).data?.copyKyc ?? ''

    // 複写確認メッセージ画面(計画書等複写確認モーダル)を呼び出す
    Or35765Logic.state.set({
      uniqueCpId: or35765.value.uniqueCpId,
      state: {
        isOpen: true,
        inParam: {
          kikanFlg: kikanFlg.value,
          copyFromStartYmd: copyFromKikanItem?.startYmd ?? '',
          copyFromEndYmd: copyFromKikanItem?.endYmd ?? '',
          copyToJigyoKnj: copyToJigyoItem?.jigyoKnj ?? '',
        },
      },
    })

    const latestOnlyFlag = await new Promise<string>((resolve) => {
      resolveOr35765 = resolve
    })

    // メッセージ画面に「キャンセル」ボタン押下
    if (!latestOnlyFlag) {
      // 処理終了
      return
    }

    // Loadingダイアログを表示する
    showLoading.value = true

    // 計画複写計画書等一括複写処理を行う
    // ・複写元期間ID ＝ 変数.複写元期間ID
    // ・複写先期間ID ＝ 変数.複写先期間ID
    // ・法人ID ＝ 親画面情報.法人ID
    // ・施設ID ＝ 親画面情報.施設ＩＤ
    // ・事業者ID ＝ 親画面情報.事業所ＩＤ
    // ・利用者ID ＝ 画面.計画書等利用者一覧選択行の利用者ID
    // ・最新のみフラグ ＝ 変数.最新のみフラグ
    // ・複写元事業者ID ＝ 変数.複写元事業者ID
    // ・複写元開始日 ＝ 変数.複写元開始日
    // ・複写元終了日 ＝ 変数.複写元終了日
    // ・複写先開始日 ＝ 変数.複写先開始日
    // ・介護予防支援複写可能 ＝ 変数.介護予防支援複写可能
    const req: PlanDuplicateCarePlanBundleUpdateInEntity = {
      /** 複写元期間ID */
      sc1IdM: copyFromKikanItem?.sc1Id,
      /** 複写先期間ID */
      sc1IdS: copyToKikanItem?.sc1Id,
      /** 法人ID */
      houjinId: inParam.value?.houjinId,
      /** 施設ID */
      shisetuId: inParam.value?.shisetuId,
      /** 事業者ID */
      svJigyoId: inParam.value?.svJigyoId,
      /** 利用者ID */
      userId: userId,
      /** 最新のみフラグ */
      newOnly: latestOnlyFlag,
      /** 複写元事業者ID */
      svJigyoIdM: copyFromKikanItem?.svJigyoId ?? copyFromJigyoItem?.svJigyoId ?? '',
      /** 複写元開始日 */
      motoStartYmd: copyFromKikanItem?.startYmd,
      /** 複写元終了日 */
      motoEndYmd: copyFromKikanItem?.endYmd,
      /** 複写先開始日 */
      sakiStartYmd: copyToKikanItem?.startYmd,
      /** 介護予防支援複写可能 */
      copyKyc: canCopy,
    }
    const res: PlanDuplicateCarePlanBundleUpdateOutEntity = await ScreenRepository.update(
      'planDuplicateCarePlanBundleUpdate',
      req
    )

    // Loadingダイアログを非表示する
    showLoading.value = false

    const updateResult = res.data?.copyRet ?? Or35745Const.CHK_RESULT_NG
    if (updateResult === Or35745Const.CHK_RESULT_NG) {
      // 判断結果コードが-1：失敗の場合
      // 以下の確認ダイアログを表示する
      await openErrorDialog({
        dialogText: t('message.e-cmn-40772'),
      })
    } else {
      // 判断結果コードが1：成功の場合
      // 以下の確認ダイアログを表示する
      await openInfoDialog({
        dialogText: t('message.i-cmn-11085'),
      })
    }
  } else {
    // 個別複写
    const checkResult = checkRes as PlanDuplicateCarePlanIndividualCheckUpdateOutEntity

    // GUI00619_個別複写 履歴選択画面をポップアップで起動する。
    // 利用者番号(selfId) ＝ 画面.計画書等利用者一覧選択行の利用者番号(selfId)
    // 利用者ID(userId) ＝ 画面.計画書等利用者一覧選択行の利用者ID(id)
    localOneWay.or52895.userId = userId ?? ''
    // 利用者名(userName) ＝ 画面.計画書等利用者一覧選択行の利用者名(compute2)
    localOneWay.or52895.userName = selectedUser
      ? selectedUser.nameSei + ' ' + selectedUser.nameMei
      : ''
    // 複写元事業所ID(svJigyoIdM) ＝ 変数.複写元事業者ID
    localOneWay.or52895.svJigyoMId =
      copyFromKikanItem?.svJigyoId ?? copyFromJigyoItem?.svJigyoId ?? ''
    // 複写先事業所ID(svJigyoIdS) ＝ 親画面情報.事業所ＩＤ
    localOneWay.or52895.svJigyoSId = inParam.value?.svJigyoId ?? ''
    // 複写元計画期間開始日(startYmdM) ＝ 変数.複写元開始日
    localOneWay.or52895.sc1IdYmdMS = copyFromKikanItem?.startYmd ?? ''
    // 複写元計画期間終了日(endYmdM) ＝ 変数.複写元終了日
    localOneWay.or52895.sc1IdYmdMN = copyFromKikanItem?.endYmd ?? ''
    // 複写先計画期間開始日(startYmdS) ＝ 変数.複写先開始日
    localOneWay.or52895.sc1IdYmdSS = copyToKikanItem?.startYmd ?? ''
    // 複写先計画期間終了日(endYmdS) ＝ 変数.複写先終了日
    localOneWay.or52895.sc1IdYmdSN = copyToKikanItem?.endYmd ?? ''
    // 複写元計画期間ID(sc1IdM) ＝ 変数.複写元期間ID
    localOneWay.or52895.sc1IdM = copyFromKikanItem?.sc1Id ?? ''
    // 複写先計画期間ID(sc1IdS) ＝ 変数.複写先期間ID
    localOneWay.or52895.sc1IdS = copyToKikanItem?.sc1Id ?? ''
    // 期間管理フラグ(sc1Flg) ＝ 変数.期間フラグ
    localOneWay.or52895.kikanFlg = kikanFlg.value
    // 引継機能IDリスト
    //  機能ID(kinouId[]) ＝ AC024-3-2のOUTPUT情報.機能ID配列
    localOneWay.or52895.parmKinouIdList = (checkResult.data?.kinouId ?? []) as unknown as []
    // 機能ID毎の使用権限リスト
    //  機能ID毎の使用権限(kinouSecUse[]) ＝ AC024-3-2のOUTPUT情報.機能ID毎の使用権限
    localOneWay.or52895.parmKinouSecUseList = (checkResult.data?.kinouSecUse ?? []) as unknown as []
    // 機能ID毎の更新権限リスト
    //  機能ID毎の更新権限(kinouSecUpd[]) ＝ AC024-3-2のOUTPUT情報.機能ID毎の更新権限
    localOneWay.or52895.parmKinouSecUpdList = (checkResult.data?.kinouSecUpd ?? []) as unknown as []

    Or52895Logic.state.set({
      uniqueCpId: or52895.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}
/**
 * 計画書等複写確認モーダル閉じる後の処理
 */
watch(
  () => Or35765Logic.state.get(or35765.value.uniqueCpId)?.isOpen,
  (newValue, oldValue) => {
    if (newValue) {
      return
    }

    if (oldValue) {
      const latestOnlyFlag = Or35765Logic.state.get(or35765.value.uniqueCpId)?.outParam
        ?.latestOnlyFlag
      resolveOr35765(latestOnlyFlag ?? '')
    }
  }
)

/**
 * タブ変更時の処理
 */
const changeTab = async () => {
  if (mo00043.value.id === Or35745Const.TAB_NUM_1) {
    await selectTab1()
  } else if (mo00043.value.id === Or35745Const.TAB_NUM_2) {
    await selectTab2()
  } else if (mo00043.value.id === Or35745Const.TAB_NUM_3) {
    await selectTab3()
  }

  currentTabId.value = mo00043.value.id
}

/**
 * タブデータの再取得要否のチェック
 */
watch(refreshTabFlag, async () => {
  if (!refreshTabFlag.value) {
    return
  }

  if (initFlag.value) {
    refreshTabFlag.value = false
    return
  }

  await refreshTab().finally(() => {
    refreshTabFlag.value = false
  })
})

/**
 * タブデータの再取得
 */
const refreshTab = async () => {
  // 情報検索処理を行う&取得したデータを表示する
  if (currentTabId.value === Or35745Const.TAB_NUM_1) {
    await selectTab1()
  } else if (currentTabId.value === Or35745Const.TAB_NUM_2) {
    await selectTab2()
  } else if (currentTabId.value === Or35745Const.TAB_NUM_3) {
    await selectTab3()
  }
}

/**
 * 画面表示用の五十音フィルターをWEBAPI用の設定値に変更
 */
const convertFilterText = (): { gojuuOnRowNo: string; gojuuOnKana: string } => {
  const filterTextStr = JSON.stringify(local.or35763.filterText)
  if (filterTextStr === JSON.stringify([Or35745Const.GOJUUON_ALL])) {
    // 全
    return {
      gojuuOnRowNo: Or35745Const.GOJUUON_ALL_INDEX,
      gojuuOnKana: Or35745Const.GOJUUON_KANA_DEFAULT,
    }
  } else if (filterTextStr === JSON.stringify([Or35745Const.GOJUUON_OTHER])) {
    // 他
    return {
      gojuuOnRowNo: Or35745Const.GOJUUON_OTHER_INDEX,
      gojuuOnKana: Or35745Const.GOJUUON_KANA_DEFAULT,
    }
  } else {
    let gojuuOnRowNo = ''
    let gojuuOnKana = ''

    const gojuuOnData = Or00094Logic.state.get(or35763_1.value.uniqueCpId)
    const valueCharArray = gojuuOnData?.valueCharArray ?? []
    out: for (let index = 0; index < valueCharArray.length; index++) {
      if (filterTextStr === JSON.stringify(valueCharArray[index])) {
        gojuuOnRowNo = String(index + 1)
        gojuuOnKana = Or35745Const.GOJUUON_KANA_DEFAULT
        break
      }

      for (let j = 0; j < valueCharArray[index].length; j++) {
        if (local.or35763.filterText.includes(valueCharArray[index][j])) {
          gojuuOnRowNo = String(index + 1)
          gojuuOnKana = String(j + 1)
          break out
        }
      }
    }

    return {
      gojuuOnRowNo: gojuuOnRowNo,
      gojuuOnKana: gojuuOnKana,
    }
  }
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 複写を許可する保険外ｻｰﾋﾞｽ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ALLOWED_NON_INSURED_SERVICES },
    // 福祉用具貸与 設定単位数
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_WELFARE_EQUIPMENT_RENTAL_UNITS },
    // 複写を許可する利用票
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ALLOWED_USAGE_SLIPS },
    // 短期入所サービスの複写
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SHORT_TERM_STAY_SERVICE_COPY },
    // 複写方法
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_COPY_METHOD },
    // 複写先年月に有効な要介護度が無い場合
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MISSING_CARE_LEVEL_AT_DESTINATION },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 複写を許可する保険外ｻｰﾋﾞｽ
  localOneWay.mo00039AllowedNonInsuredServices.items?.splice(0)
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ALLOWED_NON_INSURED_SERVICES).forEach(
    (item) => {
      localOneWay.mo00039AllowedNonInsuredServices.items?.push({
        label: item.label,
        value: item.value as unknown as number,
      })
    }
  )

  // 福祉用具貸与 設定単位数
  localOneWay.mo00039WelfareEquipmentRentalUnits.items?.splice(0)
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_WELFARE_EQUIPMENT_RENTAL_UNITS).forEach(
    (item) => {
      localOneWay.mo00039WelfareEquipmentRentalUnits.items?.push({
        label: item.label,
        value: item.value as unknown as number,
      })
    }
  )

  // 複写を許可する利用票
  localOneWay.mo00039AllowedUsageSlips.items?.splice(0)
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ALLOWED_USAGE_SLIPS).forEach((item) => {
    localOneWay.mo00039AllowedUsageSlips.items?.push({
      label: item.label,
      value: item.value as unknown as number,
    })
  })

  // 短期入所サービスの複写
  localOneWay.mo00039ShortTermStayServiceCopy.items?.splice(0)
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_SHORT_TERM_STAY_SERVICE_COPY).forEach(
    (item) => {
      localOneWay.mo00039ShortTermStayServiceCopy.items?.push({
        label: item.label,
        value: item.value as unknown as number,
      })
    }
  )

  // 複写方法
  localOneWay.mo00039CopyMethod.items?.splice(0)
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_COPY_METHOD).forEach((item) => {
    localOneWay.mo00039CopyMethod.items?.push({
      label: item.label,
      value: item.value as unknown as number,
    })
  })

  // 複写先年月に有効な要介護度が無い場合
  localOneWay.mo00039MissingCareLevel.items?.splice(0)
  CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_MISSING_CARE_LEVEL_AT_DESTINATION).forEach(
    (item) => {
      localOneWay.mo00039MissingCareLevel.items?.push({
        label: item.label,
        value: item.value as unknown as number,
      })
    }
  )
}

/**
 * 計画書等利用者リストをコンポネント画面用の情報に変換
 *
 * @param userList - 計画書等利用者リスト
 */
const convertKeikakushoUserList = (userList?: KeikakushoUserInfo[]): userListItemType[] => {
  const out: userListItemType[] = []

  userList?.forEach((user) => {
    out.push({
      /**
       * 利用者ID
       */
      userId: Number(user.id ?? 0),
      /**
       * 写真パス
       */
      picturePath: '',
      /**
       * ふりがな（姓）
       */
      nameKanaSei: user.name1Kana,
      /**
       * ふりがな（名）
       */
      nameKanaMei: user.name2Kana,
      /**
       * 氏名（姓）
       */
      nameSei: user.name1Knj,
      /**
       * 氏名（名）
       */
      nameMei: user.name2Knj,
      /**
       * 同名識別子
       */
      genericIdentifier: user.douseiKnj,
      /**
       * 性別
       */
      gender: Number(user.sex ?? 0),
      /**
       * 年齢
       */
      age: 0,
      /**
       * 要介護度
       */
      levelOfCareRequired: 0,
      /**
       * 利用者番号
       */
      selfId: user.selfId,
      /**
       * 認定状態
       */
      ninteiJoutai: 0,
      /**
       * 前月退所
       */
      zengetuFlg: 0,
      /**
       * 利用開始前
       */
      riyoumaeFlg: 0,
      /**
       * KBN3
       */
      kbn3: 0,
      /**
       * KBN4
       */
      kbn4: 0,
      /**
       * 事業所情報一覧
       */
      jigyoInfoList: [],
      /**
       * 利用者ID
       */
      id: user.id,
    })
  })

  return out
}

/**************************************************
 * ライフサイクルフック
 **************************************************/
onBeforeMount(async () => {
  // 汎用コード取得
  await initCodes()
})
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneWay.mo00024"
    class="or35745-wrapper"
  >
    <template #cardItem>
      <c-v-sheet
        v-if="!initFlag"
        class="container h-100 d-flex flex-column position-relative"
      >
        <c-v-row class="flex-0-0 flex-nowrap align-end pt-4">
          <!-- タブ -->
          <c-v-col
            cols="auto"
            class="w-100 pa-0 custom-tabs d-flex flex-column justify-end"
          >
            <base-mo00043
              v-model="mo00043"
              :oneway-model-value="localOneWay.mo00043"
              @update:model-value="changeTab"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row class="flex-1-1 overflow-y-auto">
          <c-v-window
            v-model="currentTabId"
            class="w-100 h-100"
          >
            <!-- 利用票 -->
            <c-v-window-item
              :value="Or35745Const.TAB_NUM_1"
              class="overflow-y-auto flex-fill"
            >
              <c-v-sheet class="h-100 d-flex flex-column">
                <!-- 複写元サブセクション -->
                <!-- 複写先サブセクション -->
                <c-v-row>
                  <c-v-col
                    cols="auto"
                    class="w-45 pa-0"
                  >
                    <!-- 複写元サブセクションラベル -->
                    <c-v-row class="custom-section bg-default px-6">
                      <base-mo00615 :oneway-model-value="localOneWay.mo00615CopySrc" />
                    </c-v-row>
                  </c-v-col>
                  <c-v-col
                    cols="auto"
                    class="w-55 pa-0"
                  >
                    <!-- 複写先サブセクションラベル -->
                    <c-v-row
                      class="custom-section bordered-blue border-t-0 border-e-0 border-b-0 bg-default px-6"
                    >
                      <base-mo00615 :oneway-model-value="localOneWay.mo00615CopyTarget" />
                    </c-v-row>
                  </c-v-col>
                  <c-v-col
                    cols="auto"
                    class="w-45 pa-0"
                  >
                    <!-- 複写元サブセクションコンテンツ -->
                    <c-v-container
                      class="h-100 bordered-blue border-t-0 border-s-0 border-e-0 pa-4 px-6"
                    >
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <!-- 複写元年月 -->
                          <base-mo01352
                            v-model="local.mo01352CopySrc"
                            :oneway-model-value="defaultOneWay.mo01352"
                            class="calender-input-custom"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="pt-4 flex-nowrap ga-8">
                        <c-v-col
                          cols="auto"
                          class="pa-0 d-flex flex-column ga-4"
                        >
                          <c-v-row class="flex-0-1 d-flex flex-column">
                            <!-- 複写方法ラベル -->
                            <base-mo00615 :oneway-model-value="localOneWay.mo00615CopyMethod" />
                            <!-- 複写方法 -->
                            <base-mo00039
                              v-model="local.mo00039CopyMethod"
                              :oneway-model-value="localOneWay.mo00039CopyMethod"
                              class="radio-custom"
                            />
                          </c-v-row>
                          <c-v-row class="flex-0-1 d-flex flex-column">
                            <!-- 複写先年月に有効な要介護度が無い場合ラベル -->
                            <base-mo00615
                              :oneway-model-value="localOneWay.mo00615MissingCareLevel"
                            />
                            <!-- 複写先年月に有効な要介護度が無い場合 -->
                            <base-mo00039
                              v-model="local.mo00039MissingCareLevel"
                              :oneway-model-value="localOneWay.mo00039MissingCareLevel"
                            />
                          </c-v-row>
                        </c-v-col>
                        <c-v-col
                          v-if="showCopyFromCalender"
                          cols="auto"
                          class="pa-0 d-flex flex-column ga-8"
                        >
                          <!-- 複写元カレンダー -->
                          <base-mo00615
                            :oneway-model-value="localOneWay.mo00615CopySourceInfoMsg"
                          />
                          <g-custom-or35750
                            v-bind="or35750_1"
                            v-model="local.or35750CopySrc"
                            :oneway-model-value="{
                              ...localOneWay.or35750CopySrc,
                              targetYM: local.mo01352CopySrc.value,
                            }"
                          />
                        </c-v-col>
                      </c-v-row>
                    </c-v-container>
                  </c-v-col>
                  <c-v-col
                    cols="auto"
                    class="w-55 pa-0"
                  >
                    <!-- 複写先サブセクションコンテンツ -->
                    <c-v-container class="h-100 bordered-blue border-t-0 border-e-0 pa-4 px-6">
                      <c-v-row>
                        <c-v-col
                          cols="auto"
                          class="pa-0 d-flex align-center ga-2"
                        >
                          <base-mo00615
                            :oneway-model-value="localOneWay.mo00615CopyPeriod"
                            class="mr-2"
                          />
                          <!-- 利用票期間開始年月 -->
                          <base-mo01352
                            v-model="local.mo01352CopyTargetFrom"
                            :oneway-model-value="defaultOneWay.mo01352"
                            class="calender-input-custom"
                          />
                          <base-mo00615 :oneway-model-value="localOneWay.mo00615Wavy" />
                          <!-- 利用票期間終了年月 -->
                          <base-mo01352
                            v-model="local.mo01352CopyTargetTo"
                            :oneway-model-value="defaultOneWay.mo01352"
                            class="calender-input-custom"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="pt-4 flex-nowrap ga-8">
                        <c-v-col
                          cols="auto"
                          class="pa-0 d-flex flex-column ga-2"
                        >
                          <c-v-row class="flex-0-1 d-flex flex-column">
                            <!-- 短期入所サービスの複写 -->
                            <base-mo00615
                              :oneway-model-value="localOneWay.mo00615ShortTermStayServiceCopy"
                            />
                            <base-mo00039
                              v-model="local.mo00039ShortTermStayServiceCopy"
                              :oneway-model-value="localOneWay.mo00039ShortTermStayServiceCopy"
                              class="radio-custom"
                            />
                          </c-v-row>
                          <c-v-row class="flex-0-1 d-flex flex-column">
                            <!-- 複写を許可する利用票 -->
                            <base-mo00615
                              :oneway-model-value="localOneWay.mo00615AllowedUsageSlips"
                            />
                            <base-mo00039
                              v-model="local.mo00039AllowedUsageSlips"
                              :oneway-model-value="localOneWay.mo00039AllowedUsageSlips"
                              class="radio-custom"
                            />
                          </c-v-row>
                          <c-v-row class="flex-0-1 d-flex flex-column">
                            <!-- 福祉用具貸与 設定単位数 -->
                            <base-mo00615
                              :oneway-model-value="localOneWay.mo00615WelfareEquipmentRentalUnits"
                            />
                            <base-mo00039
                              v-model="local.mo00039WelfareEquipmentRentalUnits"
                              :oneway-model-value="localOneWay.mo00039WelfareEquipmentRentalUnits"
                              class="radio-custom"
                            />
                          </c-v-row>
                        </c-v-col>
                        <c-v-col
                          cols="auto"
                          class="pa-0 d-flex flex-column ga-2"
                        >
                          <!-- 複写先カレンダー -->
                          <base-mo00615
                            :oneway-model-value="localOneWay.mo00615CopyTargetInfoMsg"
                          />
                          <c-v-row>
                            <c-v-col
                              cols="auto"
                              class="pa-0"
                            >
                              <g-custom-or35750
                                v-bind="or35750_2"
                                v-model="local.or35750CopyTarget"
                                :oneway-model-value="{
                                  ...localOneWay.or35750CopyTarget,
                                  targetYM: local.mo01352CopyTargetFrom.value,
                                }"
                              />
                            </c-v-col>
                          </c-v-row>
                        </c-v-col>
                      </c-v-row>
                    </c-v-container>
                  </c-v-col>
                </c-v-row>
                <!-- 担当ケアマネセクション -->
                <!-- 利用者セクション -->
                <c-v-row>
                  <g-custom-or35763
                    v-bind="or35763_1"
                    v-model="local.or35763"
                    :oneway-model-value="{
                      ...localOneWay.or35763,
                      mode: Or35763Const.Or35763Mode.USE_SLIP,
                    }"
                  />
                </c-v-row>
              </c-v-sheet>
            </c-v-window-item>
            <!-- 保険外 -->
            <c-v-window-item
              :value="Or35745Const.TAB_NUM_2"
              class="overflow-y-auto flex-fill"
            >
              <c-v-sheet class="h-100 d-flex flex-column">
                <!-- 複写元サブセクション -->
                <!-- 複写先サブセクション -->
                <c-v-row>
                  <c-v-col
                    cols="auto"
                    class="w-45 pa-0"
                  >
                    <!-- 複写元サブセクションラベル -->
                    <c-v-row class="custom-section bg-default px-6">
                      <base-mo00615 :oneway-model-value="localOneWay.mo00615CopySrc" />
                    </c-v-row>
                  </c-v-col>
                  <c-v-col
                    cols="auto"
                    class="w-55 pa-0"
                  >
                    <!-- 複写先サブセクションラベル -->
                    <c-v-row
                      class="custom-section bordered-blue border-t-0 border-e-0 border-b-0 bg-default px-6"
                    >
                      <base-mo00615 :oneway-model-value="localOneWay.mo00615CopyTarget" />
                    </c-v-row>
                  </c-v-col>
                  <c-v-col
                    cols="auto"
                    class="w-45 pa-0"
                  >
                    <!-- 複写元サブセクションコンテンツ -->
                    <c-v-container
                      class="h-100 bordered-blue border-t-0 border-s-0 border-e-0 pa-4 px-6"
                    >
                      <c-v-row>
                        <c-v-col class="pa-0">
                          <!-- 複写元年月 -->
                          <base-mo01352
                            v-model="local.mo01352CopySrc"
                            :oneway-model-value="defaultOneWay.mo01352"
                            class="calender-input-custom"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="pt-4 flex-nowrap ga-8">
                        <c-v-col
                          cols="auto"
                          class="pa-0 d-flex flex-column ga-4"
                        >
                          <!-- 複写方法ラベル -->
                          <base-mo00615 :oneway-model-value="localOneWay.mo00615CopyMethod" />
                          <!-- 複写方法 -->
                          <base-mo00039
                            v-model="local.mo00039CopyMethod"
                            :oneway-model-value="localOneWay.mo00039CopyMethod"
                            class="radio-custom"
                          />
                        </c-v-col>
                        <c-v-col
                          v-if="showCopyFromCalender"
                          cols="auto"
                          class="pa-0 d-flex flex-column ga-8"
                        >
                          <!-- 複写元カレンダー -->
                          <base-mo00615
                            :oneway-model-value="localOneWay.mo00615CopySourceInfoMsg"
                          />
                          <g-custom-or35750
                            v-bind="or35750_3"
                            v-model="local.or35750CopySrc"
                            :oneway-model-value="{
                              ...localOneWay.or35750CopySrc,
                              targetYM: local.mo01352CopySrc.value,
                            }"
                          />
                        </c-v-col>
                      </c-v-row>
                    </c-v-container>
                  </c-v-col>
                  <c-v-col
                    cols="auto"
                    class="w-55 pa-0"
                  >
                    <!-- 複写先サブセクションコンテンツ -->
                    <c-v-container class="h-100 bordered-blue border-t-0 border-e-0 pa-4 px-6">
                      <c-v-row>
                        <c-v-col
                          cols="auto"
                          class="pa-0 d-flex align-center ga-2"
                        >
                          <base-mo00615
                            :oneway-model-value="localOneWay.mo00615CopyPeriod"
                            class="mr-2"
                          />
                          <!-- 利用票期間開始年月 -->
                          <base-mo01352
                            v-model="local.mo01352CopyTargetFrom"
                            :oneway-model-value="defaultOneWay.mo01352"
                            class="calender-input-custom"
                          />
                          <base-mo00615 :oneway-model-value="localOneWay.mo00615Wavy" />
                          <!-- 利用票期間終了年月 -->
                          <base-mo01352
                            v-model="local.mo01352CopyTargetTo"
                            :oneway-model-value="defaultOneWay.mo01352"
                            class="calender-input-custom"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row class="pt-4 flex-nowrap ga-8">
                        <c-v-col
                          cols="auto"
                          class="pa-0 d-flex flex-column ga-2"
                        >
                          <c-v-row class="flex-0-1 d-flex flex-column">
                            <!-- 複写を許可する保険外ｻｰﾋﾞｽ -->
                            <base-mo00615
                              :oneway-model-value="localOneWay.mo00615AllowedNonInsuredServices"
                            />
                            <base-mo00039
                              v-model="local.mo00039AllowedNonInsuredServices"
                              :oneway-model-value="localOneWay.mo00039AllowedNonInsuredServices"
                              class="radio-custom"
                            />
                          </c-v-row>
                        </c-v-col>
                        <c-v-col
                          cols="auto"
                          class="pa-0 d-flex flex-column ga-2"
                        >
                          <!-- 複写先カレンダー -->
                          <base-mo00615
                            :oneway-model-value="localOneWay.mo00615CopyTargetInfoMsg"
                          />
                          <c-v-row>
                            <c-v-col
                              cols="auto"
                              class="pa-0"
                            >
                              <g-custom-or35750
                                v-bind="or35750_2"
                                v-model="local.or35750CopyTarget"
                                :oneway-model-value="{
                                  ...localOneWay.or35750CopyTarget,
                                  targetYM: local.mo01352CopyTargetFrom.value,
                                }"
                              />
                            </c-v-col>
                          </c-v-row>
                        </c-v-col>
                      </c-v-row>
                    </c-v-container>
                  </c-v-col>
                </c-v-row>
                <!-- 担当ケアマネセクション -->
                <!-- 利用者セクション -->
                <c-v-row>
                  <g-custom-or35763
                    v-bind="or35763_2"
                    v-model="local.or35763"
                    :oneway-model-value="{
                      ...localOneWay.or35763,
                      mode: Or35763Const.Or35763Mode.OUT_INSURANCE,
                    }"
                  />
                </c-v-row>
              </c-v-sheet>
            </c-v-window-item>
            <!-- 計画書等 -->
            <c-v-window-item
              :value="Or35745Const.TAB_NUM_3"
              class="overflow-y-auto flex-fill"
            >
              <c-v-sheet class="h-100 d-flex flex-column">
                <c-v-row class="flex-0-0 bordered-blue border-s-0 border-e-0 border-t-0">
                  <c-v-col
                    cols="auto"
                    class="pa-4 px-6 d-flex align-center"
                  >
                    <!-- 担当ケアマネ -->
                    <g-custom-orX0145
                      v-bind="orx0145"
                      v-model="local.orx0145"
                      :oneway-model-value="localOneWay.orx0145"
                    />
                  </c-v-col>
                </c-v-row>
                <c-v-row class="flex-1-1-0 min-h-0">
                  <c-v-col
                    cols="auto"
                    class="bg-default pa-0 bordered-blue border-s-0 border-t-0 border-b-0 h-100 overflow-auto pa-4 px-6"
                  >
                    <!-- 利用者選択一覧 -->
                    <g-custom-orX0155
                      v-bind="orX0155"
                      class="user-list"
                    />
                  </c-v-col>
                  <c-v-col class="flex-1-1-0 pa-0 px-4 pr-6">
                    <c-v-row class="h-100">
                      <c-v-col
                        cols="7"
                        class="pa-0 pr-4"
                      >
                        <!-- 複写元サブセクションラベル -->
                        <c-v-row class="custom-section-2 bg-default px-4">
                          <base-mo00615 :oneway-model-value="localOneWay.mo00615CopySrcInfo" />
                        </c-v-row>
                        <!-- 複写元セクション -->
                        <c-v-row class="pt-4">
                          <g-custom-or35760
                            v-bind="or35760"
                            v-model="local.or35760"
                            :oneway-model-value="{
                              ...localOneWay.or35760,
                              kikanFlg: kikanFlg,
                            }"
                          />
                        </c-v-row>
                      </c-v-col>
                      <c-v-col
                        cols="auto"
                        class="custom-buttom-row bg-default pa-6 h-100 d-flex flex-column justify-center ga-4"
                      >
                        <!-- 一括複写ボタン -->
                        <base-mo00609
                          v-bind="localOneWay.mo00609CopyAll"
                          @click="doCopyEtcAction(true)"
                        >
                          <c-v-tooltip
                            activator="parent"
                            location="bottom"
                            :max-width="600"
                            :text="t('tooltip.duplicate-all')"
                            open-delay="200"
                          />
                        </base-mo00609>
                        <!-- 個別複写ボタン -->
                        <base-mo00609
                          v-bind="localOneWay.mo00609CopyIndividual"
                          @click="doCopyEtcAction(false)"
                        >
                          <c-v-tooltip
                            activator="parent"
                            location="bottom"
                            :max-width="600"
                            :text="t('tooltip.duplicate-individual')"
                            open-delay="200"
                          />
                        </base-mo00609>
                      </c-v-col>
                      <c-v-col class="pa-0 pl-4">
                        <!-- 複写先サブセクションラベル -->
                        <c-v-row class="custom-section-2 bg-default px-4">
                          <base-mo00615 :oneway-model-value="localOneWay.mo00615CopyTargetInfo" />
                        </c-v-row>
                        <!-- 複写先セクション -->
                        <c-v-row class="pt-4">
                          <g-custom-or35761
                            v-bind="or35761"
                            v-model="local.or35761"
                            :oneway-model-value="{
                              ...localOneWay.or35761,
                              kikanFlg: kikanFlg,
                            }"
                            @click:plan-period="clickPlanPeriod"
                          />
                        </c-v-row>
                      </c-v-col>
                    </c-v-row>
                  </c-v-col>
                </c-v-row>
              </c-v-sheet>
            </c-v-window-item>
          </c-v-window>
        </c-v-row>
        <!-- Loadingダイアログ -->
        <v-overlay
          v-model="showLoading"
          class="align-center justify-center"
          contained
          persistent
          opacity="0"
        >
          <c-v-sheet class="bg-default px-4 py-5">
            <v-progress-circular
              color="primary"
              indeterminate
            />
          </c-v-sheet>
        </v-overlay>
      </c-v-sheet>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        class="custom-action-bar"
      >
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneWay.mo00611"
          :class="{
            'mx-2':
              regAuth &&
              (currentTabId === Or35745Const.TAB_NUM_1 || currentTabId === Or35745Const.TAB_NUM_2),
          }"
          @click="doCloseAction(false)"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-if="
            regAuth &&
            (currentTabId === Or35745Const.TAB_NUM_1 || currentTabId === Or35745Const.TAB_NUM_2)
          "
          v-bind="localOneWay.mo00609"
          @click="doSaveAction()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.duplicate')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>

  <!-- エラーダイアログ -->
  <g-base-or21813
    v-if="showOr21813"
    v-bind="or21813"
  />

  <!-- インフォメーションダイアログ -->
  <g-base-or21814
    v-if="showOr21814"
    v-bind="or21814"
  />

  <!-- GUI00620_計画複写：要介護度の変更利用者一覧画面 -->
  <g-custom-or54307
    v-if="showOr54307"
    v-bind="or54307"
    :oneway-model-value="localOneWay.or54307"
  />

  <!-- GUI00070_対象期間 -->
  <g-custom-or-x0115
    v-if="showOrX0115"
    v-bind="orx0115"
    :oneway-model-value="localOneWay.orx0115"
  />

  <!-- 計画書等複写確認モーダル -->
  <g-custom-or35765
    v-if="showOr35765"
    v-bind="or35765"
  />

  <!-- GUI00619_個別複写 履歴選択画面 -->
  <g-custom-or52895
    v-if="showOr52895"
    v-bind="or52895"
    :oneway-model-value="localOneWay.or52895"
  />
</template>

<style lang="scss">
.or35745-wrapper {
  .v-toolbar__content > .v-toolbar-title {
    margin-inline-start: 16px;
  }

  > .v-overlay__content > .v-card {
    border-radius: 12px;
  }
}
</style>
<style lang="scss" scoped>
@use '@/styles/base.scss';

.container {
  :deep(.v-row) {
    margin: 0px;
  }

  :deep(.tabs) {
    border-bottom: thin solid rgb(var(--v-theme-screenDivider));
  }

  .user-list {
    width: auto !important;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: unset;

    :deep(.v-row) {
      margin: 0px;
    }

    :deep(> br) {
      display: none;
    }

    :deep(div[name='userSelectorList']) {
      max-height: none !important;
    }

    // 利用者リスト
    :deep(> div:last-of-type) {
      padding: 0px;
      padding-top: 0px;
      width: auto !important;
      flex: 1 1 0%;

      > div:first-of-type {
        height: 100%;

        > div {
          padding: 0px !important;
          flex: 0 0 auto;
          width: auto;
          max-width: 100%;
        }

        > div:first-of-type {
          position: relative;
          z-index: 1;
        }

        > div:last-of-type {
          height: 100%;
          overflow-y: auto;

          > div:first-of-type {
            transform: unset !important;
          }
        }
      }
    }
  }
}

.bordered-blue {
  border: thin solid rgb(var(--v-theme-screenDivider));
}

.bg-default {
  background-color: rgb(var(--v-theme-background));
}

.radio-custom {
  :deep(.v-selection-control__wrapper) {
    height: 32px;
  }
}

.custom-section {
  min-height: 38px;
  display: flex;
  align-items: center;
}

.custom-section-2 {
  min-height: 48px;
  display: flex;
  align-items: center;
}

.calender-input-custom {
  :deep(.v-btn:first-of-type),
  :deep(.v-btn:last-of-type) {
    background-color: #ebf2fd;
    border: thin solid #cccccc;
    border-radius: 0;

    i {
      color: #0b66ed;
    }
  }

  :deep(.v-btn:first-of-type) {
    border-right: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  :deep(.v-btn:last-of-type) {
    border-left: 0;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  :deep(.v-field:not(:hover) .v-field__outline) {
    --v-field-border-opacity: 0.2;
  }

  :deep(.v-field.v-field--focused .v-field__outline) {
    --v-field-border-opacity: 1;
  }

  :deep(.v-field__outline__start),
  :deep(.v-field__outline__end) {
    border-radius: 0;
  }
}

.custom-tabs {
  min-height: 36px;

  :deep(.v-slide-group__content) {
    gap: 24px;
  }

  :deep(.tabs) {
    height: 35px;
    border-bottom: 1px solid #b4c5dc;
    padding-left: 24px !important;
    padding-right: 24px !important;

    .v-tab.v-tab.v-btn {
      height: 35px;
    }
  }
}

.custom-buttom-row {
  :deep(.v-btn) {
    --v-btn-height: 32px;
    min-height: 32px !important;
    min-width: auto !important;
    padding: 6px 12px;
  }
}

.custom-action-bar {
  min-height: 60px;
  padding: 0 16px;

  align-items: center;
  justify-content: flex-end;
  gap: 8px;

  :deep(.v-btn) {
    --v-btn-height: 32px;
    min-height: 32px !important;
    min-width: auto !important;
    padding: 6px 12px;
  }
}

.min-h-0 {
  min-height: 0;
}

.w-45 {
  width: 45%;
}
.w-55 {
  width: 55%;
}
</style>
