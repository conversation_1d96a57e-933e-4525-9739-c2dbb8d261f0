import { getSequencedCpId } from '#imports'

/**
 * Or35957:静的データ
 * GUI01154_日割算定確認
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> 李晨昊
 */
export namespace Or35957Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or35957', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false

    /**
     * 土曜日
     */
    export const SATURDAY = '土'

    /**
     * 日曜日
     */
    export const SUNDAY = '日'
  }

  /**
   * yDay
   */
  export const Y_DAY = 'yDay'

  /**
   * jDay
   */
  export const J_DAY = 'jDay'

  /**
   * (
   */
  export const LEFT_BRACKET = '('

  /**
   * )
   */
  export const RIGHT_BRACKET = ')'

  /**
   * /
   */
  export const SLASH = '/'

  /**
   * サービス時間 99:99
   */
  export const SERVICE_TIME_99 = '99:99'

  /**
   * サービス時間 88:88
   */
  export const SERVICE_TIME_88 = '88:88'

  /**
   * サービス時間 77:77
   */
  export const SERVICE_TIME_77 = '77:77'
}
