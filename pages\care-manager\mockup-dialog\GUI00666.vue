<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or27447Const } from '~/components/custom-components/organisms/Or27447/Or27447.constants'
import { Or27447Logic } from '~/components/custom-components/organisms/Or27447/Or27447.logic'
import type { Or27447OnewayType, Or27447Type } from '~/types/cmn/business/components/Or27447Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 * SH 2025/04/01 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI00666'
// ルーティング
const routing = 'GUI00666/pinia'
// 画面物理名
const screenName = 'GUI00666'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27447 = ref({ uniqueCpId: '' })
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType
/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00666' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
or27447.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI00666',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27447Const.CP_ID(0) }],
})
Or27447Logic.initialize(init.childCpIds.Or27447.uniqueCpId)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27447Const.CP_ID(0)]: or27447.value,
})

// ダイアログ表示フラグ
const showDialogOr27447 = computed(() => {
  // Or27447 cks_flg=1 のダイアログ開閉状態
  return Or27447Logic.state.get(or27447.value.uniqueCpId)?.isOpen ?? false
})

const or27447OnewayModel: Or27447OnewayType = {
  /**
   * 事業所ID
   */
  svJigyoId: '0',
}
const or27447Type = ref<Or27447Type>({
  konnanKnj: '',
})
const local = reactive({
  // 事業所ID
  svJigyoId: { value: '0' } as Mo00045Type,
})
/**
 *  ボタン押下時の処理(Or27447)
 *
 * @param svJigyoId  - 事業所ID
 */
function onClickOr27447(svJigyoId: string) {
  // 引継情報.事業所IDを設定する。
  or27447OnewayModel.svJigyoId = svJigyoId
  // Or27447のダイアログ開閉状態を更新する
  Or27447Logic.state.set({
    uniqueCpId: or27447.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function onClickOr27447_1() {
  or27447OnewayModel.svJigyoId = local.svJigyoId.value
  Or27447Logic.state.set({
    uniqueCpId: or27447.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/04/27 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27447('1')"
        >GUI000666_［困難度入力支援］画面
      </v-btn>
      <g-custom-or-27447
        v-if="showDialogOr27447"
        v-bind="or27447"
        v-model="or27447Type"
        :oneway-model-value="or27447OnewayModel"
      />
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/04/27 ADD END-->
  <!-- POP画面ポップアップ SH 2025/10/29 ADD END-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">事業所ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.svJigyoId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr27447_1"> GUI00666 疎通起動 </v-btn>
  </div>
</template>
