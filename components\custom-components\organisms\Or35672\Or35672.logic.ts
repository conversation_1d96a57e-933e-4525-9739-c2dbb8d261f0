import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { Or28992Const } from '../Or28992/Or28992.constants'
import { Or28992Logic } from '../Or28992/Or28992.logic'
import { Or31535Const } from '../Or31535/Or31535.constants'
import { Or31535Logic } from '../Or31535/Or31535.logic'
import { Or29958Const } from '../Or29958/Or29958.constants'
import { Or29958Logic } from '../Or29958/Or29958.logic'
import { Or33097Const } from '../Or33097/Or33097.constants'
import { Or33097Logic } from '../Or33097/Or33097.logic'
import { Or29242Const } from '../Or29242/Or29242.constants'
import { Or29242Logic } from '../Or29242/Or29242.logic'
import { Or00386Const } from '../Or00386/Or00386.constants'
import { Or00386Logic } from '../Or00386/Or00386.logic'
import { Or01997Const } from '../Or01997/Or01997.constants'
import { Or01997Logic } from '../Or01997/Or01997.logic'
import { Or29241Const } from '../Or29241/Or29241.constants'
import { Or29241Logic } from '../Or29241/Or29241.logic'
import { Or30732Const } from '../Or30732/Or30732.constants'
import { Or30732Logic } from '../Or30732/Or30732.logic'
import { Or30149Const } from '../Or30149/Or30149.constants'
import { Or30149Logic } from '../Or30149/Or30149.logic'
import { Or32081Const } from '../Or32081/Or32081.constants'
import { Or32081Logic } from '../Or32081/Or32081.logic'
import { Or11131Const } from '../Or11131/Or11131.constants'
import { Or11131Logic } from '../Or11131/Or11131.logic'
import { Or35672Const } from './Or35672.constants'
import type { Or35672EventType, Or35672StateType } from './Or35672.type'
import {
  useEventStatusAccessor,
  useInitialize,
  useOneWayBindAccessor,
  useTwoWayBindAccessor,
} from '~/composables/useComponentLogic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Or35672Type } from '~/types/cmn/business/components/Or35672Type'

/**
 * Or35672:アセスメント複写モーダル
 * GUI00807_アセスメント複写
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or35672Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or35672Const.CP_ID(0),
      uniqueCpId,
      initOneWayState: {
        isOpen: Or35672Const.DEFAULT.IS_CLOSE,
        noData: false
      },
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or21814Const.CP_ID(0) },
        { cpId: Or21815Const.CP_ID(0) },
        { cpId: OrX0077Const.CP_ID(0) },
        { cpId: Or28992Const.CP_ID(0) },
        { cpId: Or29241Const.CP_ID(0) },
        { cpId: Or33097Const.CP_ID(0) },
        { cpId: Or29242Const.CP_ID(0) },
        { cpId: Or32081Const.CP_ID(0) },
        { cpId: Or11131Const.CP_ID(0) },
        { cpId: Or00386Const.CP_ID(0) },
        { cpId: Or01997Const.CP_ID(0) },
        { cpId: Or30732Const.CP_ID(0) },
        { cpId: Or31535Const.CP_ID(0) },
        { cpId: Or29958Const.CP_ID(0) },
        { cpId: Or30149Const.CP_ID(0) },
      ],
      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(0)].uniqueCpId)
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(0)].uniqueCpId)
    OrX0077Logic.initialize(childCpIds[OrX0077Const.CP_ID(0)].uniqueCpId)
    // Or28992Logic.initialize(childCpIds[Or28992Const.CP_ID(0)].uniqueCpId)
    // Or29241Logic.initialize(childCpIds[Or29241Const.CP_ID(0)].uniqueCpId)
    // Or33097Logic.initialize(childCpIds[Or33097Const.CP_ID(0)].uniqueCpId)
    // Or29242Logic.initialize(childCpIds[Or29242Const.CP_ID(0)].uniqueCpId)
    // Or32081Logic.initialize(childCpIds[Or32081Const.CP_ID(0)].uniqueCpId)
    // Or11131Logic.initialize(childCpIds[Or11131Const.CP_ID(0)].uniqueCpId)
    // Or00386Logic.initialize(childCpIds[Or00386Const.CP_ID(0)].uniqueCpId)
    // Or01997Logic.initialize(childCpIds[Or01997Const.CP_ID(0)].uniqueCpId)
    Or30732Logic.initialize(childCpIds[Or30732Const.CP_ID(0)].uniqueCpId)
    // Or31535Logic.initialize(childCpIds[Or31535Const.CP_ID(0)].uniqueCpId)
    // Or29958Logic.initialize(childCpIds[Or29958Const.CP_ID(0)].uniqueCpId)
    // Or30149Logic.initialize(childCpIds[Or30149Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
    }
  }

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or35672Type>(Or35672Const.CP_ID(0))

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or35672StateType>(Or35672Const.CP_ID(0))

  /**
   * 自身のEventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useEventStatusAccessor<Or35672EventType>(Or35672Const.CP_ID(0))
}
