<script setup lang="ts">
/**
 * GUI01235_評価表
 *
 * @description
 * 評価表テーブル有機体
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep, isEqual } from 'lodash'
import { TeX0013Logic } from '../../template/TeX0013/TeX0013.logic'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or51548Logic } from '../Or51548/Or51548.logic'
import { Or51548Const } from '../Or51548/Or51548.constants'
import type { Or54951HeadersType, Or54951TableDataListType } from './Or54951.type'
import { Or54951Const } from './Or54951.constants'
import { Or54951Logic } from './Or54951.logic'
import { useScreenStore, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21736Const } from '~/components/base-components/organisms/Or21736/Or21736.constants'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import { CustomClass } from '~/types/CustomClassType'
import type { TeX0013Type } from '~/types/cmn/business/components/TeX0013Type'
import type { Or54951OnewayType } from '~/types/cmn/business/components/Or54951Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01276OnewayType } from '~/types/business/components/Mo01276Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { Or21735Logic } from '~/components/base-components/organisms/Or21735/Or21735.logic'
import { Or21736Logic } from '~/components/base-components/organisms/Or21736/Or21736.logic'
import { Or21737Logic } from '~/components/base-components/organisms/Or21737/Or21737.logic'
import { Or21738Logic } from '~/components/base-components/organisms/Or21738/Or21738.logic'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { OrX0159OnewayType } from '~/types/cmn/business/components/OrX0159Type'
import type {
  Mo01354Headers,
  Mo01354Items,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
  onewayModelValue: Or54951OnewayType
}

const props = defineProps<Props>()
/**************************************************
 * emit
 **************************************************/

const emit = defineEmits(['update:copy'])

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

const or21735 = ref({ uniqueCpId: '' })
const or21736 = ref({ uniqueCpId: '' })
const or21737 = ref({ uniqueCpId: '' })
const or21738 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21814_2 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })

/**
 * テーブルヘーダリスト
 */
const headList = ref<Or54951HeadersType[]>([])

const tableRef = ref<HTMLDivElement | null>(null)
/**
 * 計画書ボタン表示フラグ
 */
const planBookDisplayFlg = ref(true)

/**
 * 選択したデータのインデックス
 */
const currentIndex = ref(0)

/**
 * コンポーネントRef
 */
const componentRef = ref<HTMLDivElement | null>()

/**
 * 選択した列インデックス
 */
const currentColumnIndex = ref(0)

/**
 * デフォルトOneway
 */
const defaultOneway = reactive({
  or54951Oneway: {
    maxRows: '7',
    tableDataList: [],
    tableStyle: [],
  } as Or54951OnewayType,
})

/**
 * チェック用データ
 */
const checkList = ref<{ id: string; check: Mo00018Type }[]>([])

/**
 * 共通入力支援画面モーダル
 */
const input = ref({ modelValue: '' })

/**
 * 現在フォーカスした項目
 */
let focusItemInputKbn: Or54951TableDataListType = {} as Or54951TableDataListType

/**
 * 保存チェック用データ
 */
const beforeCheckSaveData = {
  tableData: [] as Or54951TableDataListType[][] | undefined,
}

/**
 * 画面更新区分
 */
const screenUpadateKbn = ref<'' | 'U' | 'D' | 'C'>('')

/**
 * 注釈画面表示データ
 */
const defualtNotesList = [
  {
    notes: t('label.evaluation-table-notes-title-1'),
  },
  {
    notes: t('label.evaluation-table-notes-title-2'),
    children: [
      {
        notes: t('label.evaluation-table-notes-title-2-info-1'),
      },
      {
        notes: t('label.evaluation-table-notes-title-2-info-2'),
      },
      {
        notes: t('label.evaluation-table-notes-title-2-info-3'),
      },
      {
        notes: t('label.evaluation-table-notes-title-2-info-4'),
      },
      {
        notes: t('label.evaluation-table-notes-title-2-info-5'),
      },
    ],
  },
]

/**
 * ヘッダーチェックボックス
 */
const headerCheckBoxValue = ref<boolean>(true)

/**
 * ロカールOneway
 */
const localOneway = reactive({
  caseMo00615Oneway: {
    itemLabel: t('label.case-import'),
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-row',
      labelClass: 'ma-1',
    }),
  },
  caseMo0009Oneway: {
    btnIcon: 'edit_square',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  displayOrderMo00615Oneway: {
    itemLabel: t('label.display-order'),
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-row',
      labelClass: 'ma-1',
    }),
  },
  displayOrderMo0009Oneway: {
    btnIcon: 'edit_square',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  planMo00615Oneway: {
    itemLabel: t('label.care-plan-title'),
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-row',
      labelClass: 'ma-1',
    }),
  },
  planMo0009Oneway: {
    btnIcon: 'edit_square',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  mo00611Oneway: {
    btnLabel: t('label.notes'),
  },
  foucsRowChangeOneway: {
    btnIcon: 'chevron_left',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  rowHeightOneway: {
    btnIcon: 'height',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  },
  or54951Oneway: {
    ...defaultOneway.or54951Oneway,
    ...props.onewayModelValue,
  },
  mo01282oneway: {
    items: [] as object[],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo01282OnewayType,
  mo00024Oneway: {
    width: '650px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or29175',
      toolbarTitle: t('label.notes'),
      toolbarName: 'Or10862ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo01276Oneway: {
    disabled: false,
  } as Mo01276OnewayType,
  or51775Oneway: {
    title: t('label.remarks'),
    screenId: '',
    bunruiId: '2',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    shokuinId: '1',
    userId: '',
    mode: '',
  },
  mo00018Oneway: {
    showItemLabel: false,
  } as Mo00018OnewayType,
  orX0159Oneway: {
    readonly: true,
  } as OrX0159OnewayType,
  mo01354Oneway: {
    headers: [] as Mo01354Headers[],
    height: '100%',
    useDefaultHeader: false,
    itemsPerPage: -1,
    sticky: false,
  } as Mo01354OnewayType,
})

/**
 * 削除したデータを保存する
 */
const deletedDataList: Or54951TableDataListType[][] = []

/**
 * ロカール
 */
const local = reactive({
  commonInfo: {} as TeX0013Type,
  mo01354: {
    values: {
      items: [] as Mo01354Items[],
      selectedRowId: '',
    },
  } as Mo01354Type,
})

/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or21735Const.CP_ID(1)]: or21735.value,
  [Or21736Const.CP_ID(1)]: or21736.value,
  [Or21737Const.CP_ID(1)]: or21737.value,
  [Or21738Const.CP_ID(1)]: or21738.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
})

const { refValue } = useScreenTwoWayBind<Or54951TableDataListType[][]>({
  cpId: Or54951Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 確認ダイアログを初期化
Or21814Logic.state.set({
  uniqueCpId: or21814_1.value.uniqueCpId,
  state: {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'blank',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  },
})

// 削除ダイアログ初期化
Or21814Logic.state.set({
  uniqueCpId: or21814_2.value.uniqueCpId,
  state: {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  },
})

/**************************************************
 * computed
 **************************************************/
const childrenList = computed(() => {
  const childrens: Or54951HeadersType[] = []
  headList.value.forEach((item) => {
    if (item.children) {
      childrens.push(...item.children)
    }
  })
  return childrens
})
// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr21814_2 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen ?? false
})
// ナビゲーション制御領域のいずれかの編集フラグがON
const _isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
/**
 * ダイアログ表示フラグ
 * - Or51775のダイアログ開閉状態を取得
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ヘッダチェック状態
 */
const isIndeterminate = computed(() => {
  const allTrue = checkList.value.every((item) => item.check.modelValue)
  const someTrue = checkList.value.some((item) => item.check.modelValue)
  return !allTrue && someTrue
})
/**************************************************
 * 関数定義
 **************************************************/
/**
 * 共通情報取得
 */
function getCommonInfo() {
  const commonInfo = TeX0013Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo = commonInfo
  }
}

/**
 * 複写画面共通情報取得
 */
const getCommonInfoFromCopyMode = () => {
  const commonInfo = Or51548Logic.data.get(props.parentUniqueCpId + Or51548Const.CP_ID(0))
  if (commonInfo) {
    local.commonInfo = commonInfo.copyData
  }
}

/**
 * テーブルヘーダ作成
 */
const createTableHeader = () => {
  // 評価表上段様式リスト
  const tableStyleList = props.onewayModelValue.tableStyle
  // 最大ループ数
  const maxLoop = parseInt(props.onewayModelValue.maxRows)
  // ヘッダリスト
  const tableHeaderList: Or54951HeadersType[] = []
  for (let i = 1; i < maxLoop; i++) {
    // i ループした項目数

    if (parseInt(tableStyleList[i - 1].koumokuId) === i) {
      if (i > tableStyleList.length) return
      const { nameKnj, kbnId, koumokuId, inputKbn, widthCnt, rendouKbn } = tableStyleList[i - 1]
      // const displayFlg = setIconAndYmdAndCalenderDisplayFlg(rendouKbn, inputKbn)

      tableHeaderList.push({
        headerName: nameKnj,
        headerId: koumokuId,
        kbnId: kbnId,
        inputKbn,
        widthCount: widthCnt,
        fontSize: '14px',
        iconDisplayFlg: i === 5 || i === 6,
        calenderDisplayFlg: false,
        ymdAndMdDisplayFlg: false,
        rendouKbn: rendouKbn,
        // 16pt * 評価表上段様式リスト.文字数 - 1
        width: '',
      })
    }
  }
  // 援助内容タイトル作成
  const serviceContentHead: Or54951HeadersType = {
    headerName: '援助内容',
    headerId: '',
    kbnId: '',
    inputKbn: '',
    widthCount: '',
    fontSize: '12px',
    iconDisplayFlg: false,
    calenderDisplayFlg: false,
    ymdAndMdDisplayFlg: false,
    rendouKbn: '',
    width: 'auto',
  }
  // 幅設定
  tableHeaderList.forEach((item, i) => {
    let width = ''
    if (i === 0) {
      width = '200px'
    } else if (i === 1) {
      width = '155px'
    } else if (i === 2) {
      width = '200px'
    } else if (i === 3 || i === 4) {
      width = '140px'
    } else if (i === 5) {
      width = '100px'
    } else if (i === 6) {
      width = '300px'
    } else {
      width = '140px'
    }
    item.width = width
  })
  if (tableHeaderList.length >= 5) {
    const removeDatas = tableHeaderList.splice(2, 3)
    serviceContentHead.children = removeDatas
  } else {
    const removeDatas = tableHeaderList.splice(2)
    serviceContentHead.children = removeDatas
  }

  tableHeaderList.splice(2, 0, serviceContentHead)

  // 計画書ボタン表示フラグ一時設定
  let planBook = true

  // ③計画書ボタンの表示設定：
  // 計画書ボタンを非表示する。
  // 評価表上段様式リスト.連動区分が100 ~ 103 , 120 ~ 126 , 200 ~ 201の場合
  //  事業所のアセスメント方式=5の場合、
  //   ・｢実施計画～①権限、実施計画～②権限、実施計画～③権限｣いずれ権限有るの場合、計画書ボタンを表示する。
  //   ・以外の場合、計画書ボタンを非表示する。
  //  以外の場合、
  //   ・計画書権限有るの場合、計画書ボタンを表示する。
  //   ・以外の場合、計画書ボタンを非表示する。
  tableHeaderList.forEach((item) => {
    // 評価表上段様式リスト.連動区分が100 ~ 103 , 120 ~ 126 , 200 ~ 201の場合
    if (Or54951Const.DEFAULT.PLAN_BOOK_HIDE_RENDOUKBN_LIST.includes(item.rendouKbn)) {
      if (local.commonInfo.planBookDisplayFlg === false) {
        // 事業所のアセスメント方式=5の場合
        if (local.commonInfo.assessmentFormat === '5') {
          // ・｢実施計画～①権限、実施計画～②権限、実施計画～③権限｣いずれ権限有るの場合、計画書ボタンを表示する。
          if (
            local.commonInfo.implementationPlan1Authority ||
            local.commonInfo.implementationPlan2Authority ||
            local.commonInfo.implementationPlan2Authority
          ) {
            planBook = true
          } else {
            planBook = false
          }
        } else {
          if (local.commonInfo.planBookAuth !== undefined) {
            planBook = local.commonInfo.planBookAuth === '1'
          }
        }
      }
    } else {
      planBook = true
    }
  })

  planBookDisplayFlg.value = planBook
  headList.value = tableHeaderList
  localOneway.mo01354Oneway.headers = tableHeaderList as unknown as Mo01354Headers[]
}

/**
 * 基本動作コード取得
 */
const initCodes = async () => {
  try {
    const selectCodeKbnList = [{ mCdKbnId: CmnMCdKbnId.EVALUATION_RESULT }]
    await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
    localOneway.mo01282oneway.items = CmnSystemCodeRepository.filter(
      CmnMCdKbnId.EVALUATION_RESULT
    ).map((item) => {
      return {
        label: item.label,
        value: item.value,
      }
    })
  } catch (e) {
    console.log(e)
  }
}

/**
 * テーブルリスト作成
 */
const createTableDataList = () => {
  refValue.value = []
  // 新規の場合は処理を中断する
  if (screenUpadateKbn.value === UPDATE_KBN.CREATE) {
    return
  }
  // ヘッダリストから項目ID取得
  const headerIds = [
    // 同じデータを排除する
    ...new Set(
      [...headList.value, ...childrenList.value]
        .flatMap((item) => {
          return {
            id: item.headerId,
            inputKbn: item.inputKbn,
            ymdDisplayFlg: item.ymdAndMdDisplayFlg,
            rendouKbn: item.rendouKbn,
          }
        })
        .filter((item) => item.id !== '')
        .sort((a, b) => parseInt(a.id) - parseInt(b.id))
    ),
  ]

  const maxId = Math.max(...headerIds.map((item) => parseInt(item.id)))
  // データ取得
  const tableDataList = props.onewayModelValue.tableDataList
  for (const data of tableDataList) {
    const komokuArray: Or54951TableDataListType[] = []

    for (let i = 1; i <= maxId; i++) {
      const index = i < 10 ? `0${i}` : `${i}`
      const styleItem = headerIds.find((item) => parseInt(item.id) === i)
      if (styleItem) {
        const komoku: Or54951TableDataListType = {
          id: index,
          inputKbn: styleItem?.inputKbn,
          knj: { value: data[`koumoku${index}Knj`]! },
          cod: { modelValue: data[`koumoku${index}Cod`]! },
          startYmd: { value: data[`koumoku${index}Ymd`]! },
          endYmd: { value: data[`koumoku${index}Ymd`]! },
          modifiedCnt: data.modifiedCnt,
          cmoni1Id: local.commonInfo.cmoni1Id ?? '',
          cmoni3Id: data.cmoni3Id!,
          colSpan: styleItem?.ymdDisplayFlg ? '2' : '1',
          rendouKbn: styleItem.rendouKbn,
          omittedBorderFlg: false,
          omittedContentFlg: false,
          updateKbn: '',
          checkBoxValue: { modelValue: true },
          periodInputSupportIconDisplayFlg:
            Or54951Const.DEFAULT.PERIOD_SUPPORT_MENU_DISPLAY_LIST.includes(styleItem.rendouKbn),
        }
        komokuArray.push(komoku)
      }
    }
    refValue.value ??= []
    refValue.value.push(komokuArray)
    // チェックボックスリスト作成
    checkList.value = Array.from({ length: refValue.value.length }, (_, index) => {
      return {
        id: refValue.value?.[index][0].cmoni3Id ?? '',
        check: { modelValue: false },
      }
    })
  }
  local.mo01354.values.items = refValue.value as unknown as Mo01354Items[]

  // 全処理済み、Piniaに保存する
  useScreenStore().setCpTwoWay({
    value: cloneDeep(refValue.value),
    uniqueCpId: props.uniqueCpId,
    cpId: Or54951Const.CP_ID(0),
    isInit: true,
  })
}

/**
 * 罫線省略チェック
 */
const checkOmitBorderFlg = () => {
  // 複写モードの場合、処理終了
  if (props.onewayModelValue.mode === Or54951Const.DEFAULT.SCREEN_DISPLAY_MODE_COPY) return
  /**
   * 内容が空白は罫線を省略が「1:する」の場合
   * 評価表上段様式リスト.連動区分が「100：課題（計画書(2)）」、または、「101：長期目標（計画書(2)）」、
   * または、「102：短期目標（計画書(2)）」、または、「121：短期目標期間（計画書(2)）」の場合
   * 項目2の場合、
   * 同じ行目の項目1内容が空白の場合、項目2の上罫線を省略する
   * 以外の場合、
   * 項目内容が空白の場合、該当項目の上罫線を省略する
   */
  // データが存在しない場合、処理を中断する
  if (!refValue.value) return
  if (local.commonInfo.omittedSameBorderFlg === '1') {
    refValue.value.forEach((item, index) => {
      item.forEach((sItem, sIndex) => {
        if (index === 0) return
        // 連動区分チェック
        if (!Or54951Const.DEFAULT.BORDER_SKIP_IF_EMPTY_LIST.includes(sItem.rendouKbn)) return
        // 項目2の場合
        if (sIndex === 1) {
          // 同じ行目の項目1内容が空白の場合、項目2の上罫線を省略する
          // 入力区分より、同じ行目の項目1内容を取得する
          const firstItemValue = item[0].knj?.value
          // 同じ行目の項目1内容が空白チェック
          if (firstItemValue === '') {
            sItem.omittedBorderFlg = true
          } else {
            sItem.omittedBorderFlg = false
          }
        }
        // 以外の場合
        else {
          // 項目内容が空白の場合、該当項目の上罫線を省略する
          if (index === 0) return
          if (sItem.knj?.value === '') {
            sItem.omittedBorderFlg = true
          } else {
            sItem.omittedBorderFlg = false
          }
        }
      })
    })
  }
  /**
   * 内容が同じは罫線を省略が「1:する」の場合
   * 評価表上段様式リスト.連動区分が「100：課題（計画書(2)）」、または、「101：長期目標（計画書(2)）」、
   * または、「102：短期目標（計画書(2)）」、または、「121：短期目標期間（計画書(2)）」の場合
   * 項目2の場合、
   * 同じ行目の項目1内容が空白以外、かつ、同じ行目の項目1内容と前の行目の項目1内容が同じの場合、項目2の上罫線を省略する
   * 項目内容と前の行目の項目内容が同じ、かつ、同じ行目の項目1内容と前の行目の項目1内容が同じの場合、該当項目内容を省略する
   * 以外の場合、
   * 項目内容が空白以外、かつ、項目内容と前の行目の項目内容が同じの場合、該当項目の上罫線を省略する
   * 項目内容と前の行目の項目内容が同じの場合、該当項目内容を省略する
   */
  if (local.commonInfo.omittedSameBorderFlg === '1') {
    refValue.value.forEach((item, index) => {
      item.forEach((sItem, sIndex) => {
        if (index === 0) return
        // 連動区分チェック
        if (!Or54951Const.DEFAULT.BORDER_SKIP_IF_SAME_LIST.includes(sItem.rendouKbn)) return
        if (sIndex === 1) {
          // 項目2の場合
          // 同じ行目のデータを取得する
          const itemValue = item[0].knj?.value
          if (itemValue === '') return
          // 前行目項目1のデータを取得する
          const prevRowFirstItem = refValue.value![index - 1][0]
          // 前行目項目2のデータを取得する
          const prevRowSecondItem = refValue.value![index - 1][1]
          // 同じ行目の項目1内容と前の行目の項目1内容が同じの場合、項目2の上罫線を省略する
          if (itemValue === prevRowFirstItem.knj?.value) {
            sItem.omittedBorderFlg = true
            // 項目内容と前の行目の項目内容が同じ、かつ、同じ行目の項目1内容と前の行目の項目1内容が同じの場合、該当項目内容を省略する
            if (sItem.knj?.value === prevRowSecondItem.knj?.value) {
              sItem.omittedContentFlg = true
            } else {
              sItem.omittedContentFlg = false
            }
          } else {
            sItem.omittedBorderFlg = false
            sItem.omittedContentFlg = false
          }
        } else {
          // 以外の場合
          if (index === 0) return
          // 該当項目内容を取得
          const currentItemValue = sItem.knj?.value
          // 項目内容が空白、処理終了
          if (currentItemValue === '') return
          // 前行目項目のデータを取得
          const prevRowItemValue = refValue.value![index - 1][sIndex].knj?.value
          if (currentItemValue === prevRowItemValue) {
            sItem.omittedBorderFlg = true
            sItem.omittedContentFlg = true
          } else {
            sItem.omittedBorderFlg = false
            sItem.omittedContentFlg = false
          }
        }
      })
    })
  }
}

/**
 * データ変更処理
 *
 * @param index - インデックス
 */
const currentIndexChange = (index: number) => {
  checkOmitBorderFlg()
  currentIndex.value = index
  // 変更処理
  if (!refValue.value) return
  refValue.value.forEach((item, refIndex) => {
    item.forEach((sItem) => {
      if (index === refIndex) {
        sItem.omittedBorderFlg = false
        sItem.omittedContentFlg = false
      }
    })
  })
}

/**
 * データ変更かどうかチェック
 */
const getIsDataChanged = () => {
  return isEqual(beforeCheckSaveData.tableData, refValue.value)
}

/**
 * 画面データ再取得
 */
const reload = () => {
  createTableHeader()
  createTableDataList()
  if (props.onewayModelValue.mode !== Or54951Const.DEFAULT.SCREEN_DISPLAY_MODE_COPY) {
    checkOmitBorderFlg()
  }
  if (refValue.value) {
    beforeCheckSaveData.tableData = cloneDeep(refValue.value)
  }
  Or54951Logic.data.set({ uniqueCpId: props.uniqueCpId, value: refValue.value! })
  if (props.onewayModelValue?.mode === Or54951Const.DEFAULT.SCREEN_DISPLAY_MODE_COPY) {
    if (componentRef.value) {
      disableTab(componentRef.value)
    }
  }
}

/**
 * 新規情報取得API取得したデータより、テーブルデータ再設定「パッケージ」
 */
const reSetTableDataListPackage = () => {
  // 存在しない、または長さ不足の場合は処理を中断する、テーブルボディーを空白にする
  if (!local.commonInfo.implementationPlanImportInfoList) return
  const careLevel = parseInt(local.commonInfo.yokaiKbn!)

  // ヘッダリストから項目ID取得
  const headerIds = [
    // 同じデータを排除する
    ...new Set(
      [...headList.value, ...childrenList.value]
        .flatMap((item) => {
          return {
            id: item.headerId,
            inputKbn: item.inputKbn,
            ymdDisplayFlg: item.ymdAndMdDisplayFlg,
            rendouKbn: item.rendouKbn,
          }
        })
        .filter((item) => item.id !== '')
        .sort((a, b) => parseInt(a.id) - parseInt(b.id))
    ),
  ]
  // 現在テーブルの長さを取得
  const maxId = Math.max(...headerIds.map((item) => parseInt(item.id)))
  // データ取得
  for (const data of local.commonInfo.implementationPlanImportInfoList) {
    const komokuArray: Or54951TableDataListType[] = []

    for (let i = 1; i <= maxId; i++) {
      const index = i < 10 ? `0${i}` : `${i}`
      const styleItem = headerIds.find((item) => parseInt(item.id) === i)
      if (!styleItem) return

      // テキストエリア取得
      let textareaValue = ''
      // 画面.項目1テキストエリアに返回情報.短期を設定する。
      if (i === 1) {
        textareaValue = data.tanki
      }
      // 要介護度 < 11(要支援１) の場合
      // 画面.項目2テキストエリアに返回情報.短期期間を設定する。
      else if (i === 2 && careLevel < 11) {
        textareaValue = data.tankiKikan
      }
      // 画面.項目3テキストエリアに返回情報.介護を設定する。s
      else if (i === 3) {
        textareaValue = data.kaigo
      }
      // 画面.項目4テキストエリアに返回情報.担当を設定する。
      else if (i === 4) {
        textareaValue = data.tanto
      }
      // 画面.項目5テキストエリアに返回情報.事業所名称を設定する。
      else if (i === 5) {
        textareaValue = data.jigyoName
      }

      const komoku: Or54951TableDataListType = {
        id: index,
        inputKbn: styleItem?.inputKbn,
        knj: { value: textareaValue },
        cod: { modelValue: '' },
        startYmd: { value: '' },
        endYmd: { value: '' },
        modifiedCnt: '',
        cmoni1Id: '',
        cmoni3Id: '',
        colSpan: styleItem?.ymdDisplayFlg ? '2' : '1',
        rendouKbn: styleItem.rendouKbn,
        omittedBorderFlg: false,
        omittedContentFlg: false,
        updateKbn: '',
      }
      komokuArray.push(komoku)
    }
    refValue.value ??= []
    refValue.value.push(komokuArray)
  }
}

/**
 * 新規情報取得API取得したデータより、テーブルデータ再設定「計画書(2)取込」
 */
const reSetTableDataListPlanImport = () => {
  // 存在しない、または長さ不足の場合は処理を中断する、テーブルボディーを空白にする
  if (!local.commonInfo.carePlan2ImportInfoList) return
  // 評価表上段様式リスト.連動区分が100 ~ 103, 120 ~ 126, 129, 200, 201の場合
  const isShowPlanImportFlg = headList.value.find((item) =>
    Or54951Const.DEFAULT.ICON_BUTTON_DISPLAY_LIST.some((planItem) => planItem === item.rendouKbn)
  )
  if (isShowPlanImportFlg) {
    // ヘッダリストから項目ID取得
    const headerIds = [
      // 同じデータを排除する
      ...new Set(
        [...headList.value, ...childrenList.value]
          .flatMap((item) => {
            return {
              id: item.headerId,
              inputKbn: item.inputKbn,
              ymdDisplayFlg: item.ymdAndMdDisplayFlg,
              rendouKbn: item.rendouKbn,
            }
          })
          .filter((item) => item.id !== '')
          .sort((a, b) => parseInt(a.id) - parseInt(b.id))
      ),
    ]
    // 現在テーブルの長さを取得
    const maxId = Math.max(...headerIds.map((item) => parseInt(item.id)))

    for (const data of local.commonInfo.carePlan2ImportInfoList) {
      const komokuArray: Or54951TableDataListType[] = []
      for (let i = 1; i <= maxId; i++) {
        const index = i < 10 ? `0${i}` : `${i}`
        const styleItem = headerIds.find((item) => parseInt(item.id) === i)
        if (!styleItem) return
        let textareaValue = ''
        // 評価表上段様式リスト.連動区分が102の場合 画面.新規行の該当項目[文章]に返回情報.短期を設定する。
        if (styleItem.rendouKbn === Or54951Const.DEFAULT.RENDOUKBN_PLAN_IMPORT_MAP.TANKI) {
          textareaValue = data.tanki
        }
        // 評価表上段様式リスト.連動区分が103の場合 画面.新規行の該当項目[文章]に返回情報.介護を設定する。
        else if (styleItem.rendouKbn === Or54951Const.DEFAULT.RENDOUKBN_PLAN_IMPORT_MAP.KAIGO) {
          textareaValue = data.kaigo
        }
        // 評価表上段様式リスト.連動区分が121の場合 画面.新規行の該当項目[文章]に返回情報.短期期間を設定する。
        else if (
          styleItem.rendouKbn === Or54951Const.DEFAULT.RENDOUKBN_PLAN_IMPORT_MAP.TAN_KIKAN_KNJ
        ) {
          textareaValue = data.tanKikanKnj
        }
        // 評価表上段様式リスト.連動区分が123の場合 画面.新規行の該当項目[文章]に返回情報.担当を設定する。
        else if (styleItem.rendouKbn === Or54951Const.DEFAULT.RENDOUKBN_PLAN_IMPORT_MAP.TANTO) {
          textareaValue = data.tanto
        }
        // 評価表上段様式リスト.連動区分が124の場合 画面.新規行の該当項目[文章]に返回情報.事業所名称を設定する。
        else if (
          styleItem.rendouKbn === Or54951Const.DEFAULT.RENDOUKBN_PLAN_IMPORT_MAP.JIGYO_NAME
        ) {
          textareaValue = data.jigyoName
        }
        const komoku: Or54951TableDataListType = {
          id: index,
          inputKbn: styleItem?.inputKbn,
          knj: { value: textareaValue },
          cod: { modelValue: '' },
          startYmd: { value: '' },
          endYmd: { value: '' },
          modifiedCnt: '',
          cmoni1Id: '',
          cmoni3Id: '',
          colSpan: styleItem?.ymdDisplayFlg ? '2' : '1',
          rendouKbn: styleItem.rendouKbn,
          omittedBorderFlg: false,
          omittedContentFlg: false,
          updateKbn: '',
        }
        komokuArray.push(komoku)
      }
      refValue.value ??= []
      refValue.value.push(komokuArray)
    }
  }
}

/**
 * Or21814_2ダイアログを呼び出す、返却値を取得する
 *
 * @param dialogText -ダイアログ内容
 */
const getOr21814_2DialogResult = (dialogText: string): Promise<'yes' | 'no' | 'cancel'> => {
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: dialogText,
    },
  })
  // 閉じるタイミングで監視を実行
  return new Promise((resolve) => {
    let result: 'yes' | 'no' | 'cancel'
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (!newValue) {
          const event = Or21814Logic.event.get(or21814_2.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            result = 'yes'
          }
          if (event?.secondBtnClickFlg) {
            result = 'no'
          }
          if (event?.closeBtnClickFlg) {
            result = 'cancel'
          }
          if (event?.thirdBtnClickFlg) {
            result = 'cancel'
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814_2.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              closeBtnClickFlg: false,
              thirdBtnClickFlg: false,
            },
          })
          resolve(result)
        }
      },
      { once: true }
    )
  })
}

/**
 * 「行追加」ボタン押下処理
 */
const addRowLine = () => {
  // 既にデータがあるの場合
  if (refValue.value?.length) {
    // 行データの長さを取得
    const colData = refValue.value[0]
    // 新しいデータを作成
    const newValue = Array.from({ length: colData.length }, (_, index) => {
      return {
        id: index.toString(),
        inputKbn: colData[index].inputKbn,
        knj: { value: '' },
        cod: { modelValue: '' },
        startYmd: { value: '' },
        endYmd: { value: '' },
        modifiedCnt: '',
        cmoni1Id: '',
        cmoni3Id: '',
        colSpan: colData[index].colSpan,
        rendouKbn: colData[index].rendouKbn,
        omittedBorderFlg: false,
        omittedContentFlg: false,
        updateKbn: UPDATE_KBN.CREATE,
      }
    })
    refValue.value.push(newValue)
  } else {
    // 新しいデータを作成
    // ヘッダリストから項目ID取得
    const headerIds = [
      // 同じデータを排除する
      ...new Set(
        [...headList.value, ...childrenList.value]
          .flatMap((item) => {
            return {
              id: item.headerId,
              inputKbn: item.inputKbn,
              ymdDisplayFlg: item.ymdAndMdDisplayFlg,
              rendouKbn: item.rendouKbn,
            }
          })
          .filter((item) => item.id !== '')
          .sort((a, b) => parseInt(a.id) - parseInt(b.id))
      ),
    ]
    // 現在テーブルの長さを取得
    const maxId = Math.max(...headerIds.map((item) => parseInt(item.id)))
    const komokuArray: Or54951TableDataListType[] = []
    for (let i = 1; i <= maxId; i++) {
      const index = i < 10 ? `0${i}` : `${i}`
      const styleItem = headerIds.find((item) => parseInt(item.id) === i)
      if (!styleItem) return
      const komoku: Or54951TableDataListType = {
        id: index,
        inputKbn: styleItem?.inputKbn,
        knj: { value: '' },
        cod: { modelValue: '' },
        startYmd: { value: '' },
        endYmd: { value: '' },
        modifiedCnt: '',
        cmoni1Id: '',
        cmoni3Id: '',
        colSpan: styleItem?.ymdDisplayFlg ? '2' : '1',
        rendouKbn: styleItem.rendouKbn,
        omittedBorderFlg: false,
        omittedContentFlg: false,
        updateKbn: UPDATE_KBN.CREATE,
      }
      komokuArray.push(komoku)
    }
    refValue.value ??= []
    refValue.value.push(komokuArray)
  }
}

/**
 * 「行挿入ボタン」押下処理
 */
const insertRowLine = () => {
  if (!refValue.value?.length) return
  // 行データの長さを取得
  const colData = refValue.value[0]
  // 新しいデータを作成
  const newValue = Array.from({ length: colData.length }, (_, index) => {
    return {
      id: index.toString(),
      inputKbn: colData[index].inputKbn,
      knj: { value: '' },
      cod: { modelValue: '' },
      startYmd: { value: '' },
      endYmd: { value: '' },
      modifiedCnt: '',
      cmoni1Id: '',
      cmoni3Id: '',
      colSpan: colData[index].colSpan,
      rendouKbn: colData[index].rendouKbn,
      omittedBorderFlg: false,
      omittedContentFlg: false,
      updateKbn: UPDATE_KBN.CREATE,
    }
  })
  refValue.value.splice(currentIndex.value, 0, newValue)
}

/**
 * 「行複写ボタン」押下
 */
const copyRowLine = () => {
  if (!refValue.value?.length) return
  // 要複写データを取得
  const colData = refValue.value[currentIndex.value].map((item) => {
    return {
      ...item,
      updateKbn: UPDATE_KBN.CREATE,
    }
  })
  refValue.value.splice(currentIndex.value + 1, 0, colData)
}
/**
 * 「行削除ボタン」押下
 */
const deleteRowLine = async () => {
  if (!refValue.value?.length) return

  const result = await getOr21814_2DialogResult(t('message.i-cmn-10219'))
  if (result === 'yes') {
    const deletedListItem = refValue.value.splice(currentIndex.value, 1)
    if (deletedListItem) {
      // 更新区分を D に設定する
      deletedListItem[0].forEach((item) => {
        item.updateKbn = UPDATE_KBN.DELETE
      })
      deletedDataList.push([...deletedListItem[0]])
    }
  } else {
    // 処理終了
  }
}

/**
 * ダイアログを呼び出す
 *
 * @param dialogText - ダイアログテキスト
 */
const setShowDialog = (dialogText: string) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText,
    },
  })
}

/**
 * ケース取込データを取得
 */
const setCaseImportDialog = () => {
  // 画面の更新区分 = D:削除の場合
  if (screenUpadateKbn.value === UPDATE_KBN.DELETE) return

  // 評価表上段様式リスト.フォーカス項目の入力方法が1：文章 OR 4：マスタ＋文章の場合
  if (
    focusItemInputKbn.inputKbn === Or54951Const.DEFAULT.INPUT_KBN_TEXT ||
    focusItemInputKbn.inputKbn === Or54951Const.DEFAULT.INPUT_KBN_MASTER_TEXT
  ) {
    // 実施ケース権限チェック
    if (local.commonInfo.caseAuth === '0') {
      setShowDialog(t('message.i-cmn-10423'))
      return
    }
  } else {
    // 以外の場合、処理終了
    return
  }
}

/**
 * 入力区分取得
 *
 * @param tableCell - フォーカスしたセールのデータ
 */
const getInputKbnFromTableCell = (tableCell: Or54951TableDataListType) => {
  focusItemInputKbn = tableCell
}

/**
 * 共通入力支援画面を呼び出す
 *
 * @param cellItem - クリックしたテーブルヘッダ
 *
 * @param cellIndex - カラムインデックス
 *
 * @param rowIndex - 行インデックス
 */
const setShowCommonInputDialog = (
  cellItem: Or54951HeadersType,
  cellIndex: number,
  rowIndex: number
) => {
  // 評価表リスト未選択状態の場合、処理終了
  if (currentIndex.value < 0 || !refValue.value?.length) return
  currentIndex.value = rowIndex
  // 全ヘッダーを取得する
  const fullHeaderList = [...headList.value, ...childrenList.value]
    // 連動区分なしのヘッダーを排除する
    .filter((item) => item.rendouKbn !== '')
    .sort((a, b) => parseInt(a.headerId) - parseInt(b.headerId))
  const cellHeader = fullHeaderList[cellIndex]
  // カラムインデックス設定
  currentColumnIndex.value = cellIndex

  // // 共通入力支援画面をポップアップで起動する
  // // 中分類CD取得
  let t2Cd = ''
  switch (cellHeader.rendouKbn) {
    // 評価表上段様式リスト.連動区分が0の場合、
    // 中分類CD: 6
    case '0':
      t2Cd = '6'
      break
    // 評価表上段様式リスト.連動区分が102の場合、
    // 中分類CD: 1
    case '102':
      t2Cd = '1'
      break
    // 評価表上段様式リスト.連動区分が103の場合、
    // 中分類CD: 3
    case '103':
      t2Cd = '3'
      break
    // 評価表上段様式リスト.連動区分が121の場合、
    // 中分類CD: 2
    case '121':
      t2Cd = '2'
      break
    // 評価表上段様式リスト.連動区分が123の場合、
    // 中分類CD: 4
    case '123':
      t2Cd = '4'
      break
    // 評価表上段様式リスト.連動区分が124の場合、
    // 中分類CD: 5
    case '124':
      t2Cd = '5'
      break
  }

  localOneway.or51775Oneway = {
    userId: local.commonInfo.userId ?? '',
    t1Cd: '1040',
    t2Cd,
    t3Cd: '0',
    title: cellHeader.headerName,
    tableName: 'cpn_tuc_cmoni3',
    columnName: `koumoku${cellHeader.headerId}_knj`,
    screenId: 'GUI01235',
    assessmentMethod: local.commonInfo.assessmentFormat ?? '5',
    bunruiId: '2',
    mode: '',
    shokuinId: '',
    inputContents: refValue.value?.[currentIndex.value][currentColumnIndex.value].knj?.value ?? '',
  }
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 共通入力支援画面取得したデータを画面に設定する
 *
 * @param inputInfo - 返却情報
 */
const setScreenTextAreaData = (inputInfo: Or51775ConfirmType) => {
  if (!refValue.value) return
  console.log(inputInfo)
  if (inputInfo.type === Or54951Const.DEFAULT.INPUT_SUPPORT_TYPE_ADD) {
    const textareaValue =
      refValue.value?.[currentIndex.value][currentColumnIndex.value].knj?.value ?? ''
    refValue.value[currentIndex.value][currentColumnIndex.value].knj = {
      value: textareaValue + inputInfo.value,
    }
  } else if (inputInfo.type === Or54951Const.DEFAULT.INPUT_SUPPORT_TYPE_OVERWRITE) {
    refValue.value[currentIndex.value][currentColumnIndex.value].knj = {
      value: inputInfo.value,
    }
  }
  local.mo01354.values.items = refValue.value as unknown as Mo01354Items[]
}
/**
 * tabを制御
 *
 * @param component - コンポーネント
 */
const disableTab = (component: HTMLElement) => {
  const focusableSelectors = [
    'a[href]',
    'area[href]',
    'input',
    'select',
    'textarea',
    'button',
    'iframe',
    '[tabindex]',
    '[contenteditable]',
  ]
  const focusables = component.querySelectorAll(focusableSelectors.join(','))
  focusables.forEach((el) => {
    el.setAttribute('tabindex', '-1')
  })
}

// /**
//  * 複写モードで表示したラベル
//  *
//  * @param code - コード
//  */
// const setCopyModeCodeLabel = (code: string) => {
//   const codeLabel = (localOneway.mo01282oneway.items as { value: string; label: string }[]).find(
//     (item) => item.value === code
//   )
//   return codeLabel?.label ?? ''
// }

/**
 * ヘッダーチェックボックス変更処理
 *
 * @param $event - エレメントイベント
 */
function changeHeaderCheckBoxValue($event: InputEvent) {
  const target = $event.target as HTMLInputElement
  if (target) {
    checkList.value.forEach((item) => {
      item.check.modelValue = target.checked
    })
  }
}

/**
 * 複写データ取得
 */
const getTableListFromCopyData = () => {
  // 新規の場合は処理を中断する
  if (screenUpadateKbn.value === UPDATE_KBN.CREATE) {
    return
  }
  // ヘッダリストから項目ID取得
  const headerIds = [
    // 同じデータを排除する
    ...new Set(
      [...headList.value, ...childrenList.value]
        .flatMap((item) => {
          return {
            id: item.headerId,
            inputKbn: item.inputKbn,
            ymdDisplayFlg: item.ymdAndMdDisplayFlg,
            rendouKbn: item.rendouKbn,
          }
        })
        .filter((item) => item.id !== '')
        .sort((a, b) => parseInt(a.id) - parseInt(b.id))
    ),
  ]

  const maxId = Math.max(...headerIds.map((item) => parseInt(item.id)))
  // データ取得
  const tableDataList = local.commonInfo.copyData!
  for (const data of tableDataList) {
    const komokuArray: Or54951TableDataListType[] = []

    for (let i = 1; i <= maxId; i++) {
      const index = i < 10 ? `0${i}` : `${i}`
      const styleItem = headerIds.find((item) => parseInt(item.id) === i)
      if (styleItem) {
        const komoku: Or54951TableDataListType = {
          id: index,
          inputKbn: styleItem?.inputKbn,
          knj: { value: data[`koumoku${index}Knj`] },
          cod: { modelValue: data[`koumoku${index}Cod`] },
          startYmd: { value: data[`koumoku${index}Ymd`] },
          endYmd: { value: data[`koumoku${index}Ymd`] },
          modifiedCnt: data.modifiedCnt,
          cmoni1Id: local.commonInfo.cmoni1Id ?? '',
          cmoni3Id: data.cmoni3Id,
          colSpan: styleItem?.ymdDisplayFlg ? '2' : '1',
          rendouKbn: styleItem.rendouKbn,
          omittedBorderFlg: false,
          omittedContentFlg: false,
          updateKbn: UPDATE_KBN.CREATE,
          checkBoxValue: { modelValue: true },
        }
        komokuArray.push(komoku)
      }
    }
    refValue.value ??= []
    refValue.value.push(komokuArray)
    // チェックボックスリスト作成
    checkList.value = Array.from({ length: refValue.value.length }, (_, index) => {
      return {
        id: refValue.value?.[index][0].cmoni3Id ?? '',
        check: { modelValue: true },
      }
    })
  }
}

/**
 * 選択した行をテーブルトップに設定する
 *
 * @param currentIndex - 現在フォーカステーブルデータのインデックス
 */
const setSelectedRowInTableTop = (currentIndex: number) => {
  if (!tableRef.value) return

  // 表頭の高さを取得
  const headerHeight = tableRef.value.querySelector('thead')?.offsetHeight ?? 0

  // 選択した行の要素を取得
  const rowElement = tableRef.value.querySelector(`tbody tr:nth-child(${currentIndex + 1})`)
  if (!rowElement) return

  // 行の位置とサイズを取得
  const rowRect = rowElement.getBoundingClientRect()
  const tableRect = tableRef.value.getBoundingClientRect()

  // 行が完全に表示されているか確認
  const isRowFullyVisible =
    rowRect.top >= tableRect.top + headerHeight && rowRect.bottom <= tableRect.bottom

  if (!isRowFullyVisible) {
    // 行が完全に表示されていない場合、行をビューの上部にスクロール
    rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

/**
 * チェックボックスデータ変更処理関数
 */
const checkBoxItemValueChange = () => {
  headerCheckBoxValue.value = checkList.value.every((item) => item.check.modelValue)
}
/**
 * 初期化処理
 */
onMounted(async () => {
  // テーブルのDOMを取得する
  if (props.onewayModelValue.mode === Or54951Const.DEFAULT.SCREEN_DISPLAY_MODE_COPY) {
    tableRef.value = document.querySelector(Or54951Const.DEFAULT.TABLE_WRAPPER_SELECTOR_DUPLICATE)
  } else {
    tableRef.value = document.querySelector(Or54951Const.DEFAULT.TABLE_WRAPPER_SELECTOR)
  }
  getCommonInfo()
  await initCodes()
})

/**
 * 画面イベント監視
 */
watch(
  () => TeX0013Logic.event.get(props.parentUniqueCpId),
  (newValue) => {
    if (!newValue) return

    getCommonInfo()

    // 再表示処理
    if (newValue.isRefresh) {
      screenUpadateKbn.value = UPDATE_KBN.NONE
      reload()
    }
    // 新規処理
    if (newValue.createNewFlg) {
      // 画面更新区分を C にする
      screenUpadateKbn.value = UPDATE_KBN.CREATE
      reload()
      // 新規データを取得
      if (local.commonInfo.assessmentFormat === '5') {
        reSetTableDataListPackage()
      } else {
        reSetTableDataListPlanImport()
      }
    }
    if (newValue.saveEvent) {
      Or54951Logic.data.set({
        uniqueCpId: props.uniqueCpId,
        value: [...(refValue.value ?? []), ...deletedDataList],
      })
    }
    // 削除処理
    if (newValue.deleteFlg) {
      screenUpadateKbn.value = UPDATE_KBN.DELETE
    }
    // 複写イベント発火
    if (newValue.copyEventFlg) {
      if (local.commonInfo.copyMode === Or51548Const.DEFAULT.DUPLICATE_OVERWRITE) {
        // 返回情報.モード = 0：上書の場合
        // 画面.評価表リストを削除する。
        refValue.value = []
      }
      getTableListFromCopyData()
    }
    // データ更新後に、再度省略枠フラグをチェックする
    checkOmitBorderFlg()
  }
)

/***********************************
 * watch関数
 ***********************************/
// 「行追加ボタン」押下
watch(
  () => Or21735Logic.event.get(or21735.value.uniqueCpId),
  () => {
    addRowLine()
  }
)

// 「行挿入ボタン」押下
watch(
  () => Or21736Logic.event.get(or21736.value.uniqueCpId),
  () => {
    insertRowLine()
  }
)

// 「行複写ボタン」押下
watch(
  () => Or21737Logic.event.get(or21737.value.uniqueCpId),
  () => {
    copyRowLine()
  }
)

// 「行削除ボタン」押下
watch(
  () => Or21738Logic.event.get(or21738.value.uniqueCpId),
  async () => {
    await deleteRowLine()
  }
)
/**
 * 選択した行変更処理
 */
watch(
  () => currentIndex.value,
  (newValue) => {
    setSelectedRowInTableTop(newValue)
  }
)
/**
 * テーブルデータ変更検知
 */
watch(
  () => local.mo01354.values.items,
  (newValue) => {
    console.log(newValue)
    refValue.value = newValue as unknown as Or54951TableDataListType[][]
  },
  { deep: true }
)

/**
 * 複写モードイベント監視
 */
watch(
  () => Or51548Logic.event.get(props.parentUniqueCpId + Or51548Const.CP_ID(0)),
  (newValue) => {
    getCommonInfoFromCopyMode()

    if (newValue?.screenRenderEvent) {
      reload()
      // ヘーダチェックボックスをチェックオンにする
      headerCheckBoxValue.value = true
    }
    if (newValue?.copyEvent) {
      emit('update:copy', checkList.value)
    }
    // クリアイベント監視
    if (newValue?.clearEvent) {
      refValue.value = []
    }
  }
)
</script>

<template>
  <div
    ref="componentRef"
    class="h-100 w-100 d-flex flex-column"
  >
    <!-- アクションボタン -->
    <c-v-col v-if="props.onewayModelValue.mode !== Or54951Const.DEFAULT.SCREEN_DISPLAY_MODE_COPY">
      <!-- アクションボタン組 -->
      <c-v-col
        cols="12 d-flex align-center"
        class="pb-3 border-top"
        style="width: 1300px"
      >
        <c-v-col cols="auto d-flex table-action-button">
          <!-- 行追加ボタン -->
          <div class="w-100">
            <g-base-or-21735 v-bind="or21735"></g-base-or-21735>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.add-row')"
            ></c-v-tooltip>
          </div>
          <!-- 行挿入ボタン -->
          <div class="ml-2">
            <g-base-or-21736 v-bind="or21736"></g-base-or-21736>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.insert-row')"
            ></c-v-tooltip>
          </div>
          <!-- 行複写ボタン: Or21737 -->
          <div class="ml-2">
            <g-base-or-21737 v-bind="or21737"></g-base-or-21737>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.duplicate-row')"
            ></c-v-tooltip>
          </div>
          <!-- 行削除ボタン: Or21738 -->
          <div class="ml-2">
            <g-base-or-21738 v-bind="or21738"></g-base-or-21738>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.delete-row')"
            ></c-v-tooltip>
          </div>
        </c-v-col>
        <c-v-col cols="auto d-flex ml-auto case-action-button pr-2">
          <!-- ケース取込エリア -->
          <c-v-col
            v-if="local.commonInfo.caseAuth === '1'"
            cols="auto d-flex align-center ml-4"
          >
            <base-mo00611 @click="setCaseImportDialog">
              {{ t('label.case-import') }}
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('ケース取り込み画面を表示します')"
              ></c-v-tooltip>
            </base-mo00611>
          </c-v-col>
          <!-- 表示順変更エリア -->
          <c-v-col cols="auto d-flex align-center ml-4">
            <base-mo00611>
              {{ t('label.display-order') }}
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.display-order')"
              ></c-v-tooltip>
            </base-mo00611>
          </c-v-col>
          <!-- 計画書エリア -->
          <c-v-col
            v-if="planBookDisplayFlg"
            cols="auto d-flex align-center ml-4"
          >
            <base-mo00611>
              {{ t('label.care-plan-title') }}
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.care-plan')"
              ></c-v-tooltip>
            </base-mo00611>
          </c-v-col>
          <c-v-col class="d-flex align-center ml-6"> {{ refValue?.length }}{{ t('件') }} </c-v-col>
        </c-v-col>
      </c-v-col>
    </c-v-col>
    <div style="width: 1300px">
      <div class="h-100 table-header">
        <base-mo01354
          class="list-wrapper"
          hide-default-footer
          :oneway-model-value="localOneway.mo01354Oneway"
          :model-value="local.mo01354"
        >
          <template #headers>
            <tr v-if="headList.length">
              <!-- 複写専用チェックボックス -->
              <th
                v-if="props.onewayModelValue.mode === Or54951Const.DEFAULT.SCREEN_DISPLAY_MODE_COPY"
                rowspan="2"
                style="width: 48px !important"
              >
                <c-v-col
                  class="d-flex align-center justify-center w-100"
                  style="width: 48px"
                >
                  <v-checkbox
                    v-model="headerCheckBoxValue"
                    color="primary"
                    hide-details
                    :indeterminate="isIndeterminate"
                    @change="changeHeaderCheckBoxValue"
                  ></v-checkbox>
                </c-v-col>
              </th>
              <th
                v-for="(item, index) in headList"
                :key="index"
                :class="[
                  { 'table-cell-date': item.inputKbn === '5' },
                  { 'table-cell-code-num': item.inputKbn === '2' },
                  { 'top-header': !item.ymdAndMdDisplayFlg },
                ]"
                :colspan="item.children?.length || 1"
                :rowspan="item.children ? 1 : 2"
                :style="{ width: item.width, fontSize: item.fontSize }"
              >
                <c-v-col>
                  <div class="pl-3 d-flex align-center">
                    <span style="margin-top: 1px"
                      >{{ item.headerName }}
                      <span
                        v-if="index === 4"
                        class="d-block"
                        >{{ t('label.evaluation-table-coment') }}</span
                      >
                    </span>
                    <div
                      v-if="item.iconDisplayFlg"
                      class="icon"
                    >
                      <base-at-icon :icon="'info'"> </base-at-icon>
                      <c-v-tooltip
                        activator="parent"
                        location="top"
                        interactive
                      >
                        <div class="tooltip-content">
                          {{ defualtNotesList[1].notes }}
                        </div>
                        <div
                          v-for="(notesItem, notesIndex) in defualtNotesList[1].children"
                          :key="notesIndex"
                          class="ml-3"
                        >
                          {{ notesItem.notes }}
                        </div>
                      </c-v-tooltip>
                    </div>
                  </div>
                </c-v-col>
              </th>
            </tr>
            <tr>
              <th
                v-for="item in childrenList"
                :key="item.headerId"
                style="white-space: nowrap"
                :class="[
                  { 'table-cell-date': item.inputKbn === '5' },
                  { 'table-cell-code-num': item.inputKbn === '2' },
                  { 'bottom-header': !item.classKbn },
                ]"
                :style="{ width: item.width, fontSize: item.fontSize }"
              >
                <c-v-col class="d-flex align-center pl-3">
                  <div class="d-flex align-center">
                    <span style="margin-top: 1px">{{ item.headerName }}</span>
                    <div
                      v-if="item.iconDisplayFlg"
                      class="icon"
                    >
                      <base-at-icon :icon="'info'"></base-at-icon>
                      <c-v-tooltip
                        activator="parent"
                        location="top"
                        interactive
                      >
                        <div class="tooltip-content">
                          {{ defualtNotesList[0].notes }}
                        </div>
                      </c-v-tooltip>
                    </div>
                  </div>
                </c-v-col>
              </th>
            </tr>
          </template>
          <template #item="{ item, index }">
            <tr
              v-if="props.onewayModelValue.mode !== Or54951Const.DEFAULT.SCREEN_DISPLAY_MODE_COPY"
              @click="currentIndexChange(index)"
            >
              <td
                v-for="(cellItem, cellIndex) in item"
                :key="cellIndex"
              >
                <div
                  v-if="cellItem.inputKbn === '1' && !cellItem.periodInputSupportIconDisplayFlg"
                  class="text-table-cell h-100"
                >
                  <g-custom-or-x-0163
                    v-model="cellItem.knj"
                    @on-click-edit-btn="setShowCommonInputDialog(cellItem, cellIndex, index)"
                  />
                </div>
                <div
                  v-if="cellItem.inputKbn === '1' && cellItem.periodInputSupportIconDisplayFlg"
                  class="calendar-table-cell"
                >
                  <g-custom-or-x-0159 v-model="cellItem.knj" />
                </div>
                <div
                  v-if="cellItem.inputKbn === '5'"
                  class="calendar-table-cell"
                >
                  <base-mo01276 v-model="cellItem.startYmd" />
                </div>
                <div
                  v-if="cellItem.inputKbn === '3'"
                  class="advancement-select"
                >
                  <base-mo01282
                    v-model="cellItem.cod"
                    :oneway-model-value="localOneway.mo01282oneway"
                    @focus="getInputKbnFromTableCell(cellItem)"
                  />
                </div>
              </td>
            </tr>
            <tr
              v-if="props.onewayModelValue.mode === Or54951Const.DEFAULT.SCREEN_DISPLAY_MODE_COPY"
              @click="currentIndexChange(index)"
            >
              <td>
                <div class="d-flex w-100 justify-center">
                  <base-mo00018
                    v-model="checkList[index].check"
                    :oneway-model-value="localOneway.mo00018Oneway"
                    @change="checkBoxItemValueChange"
                  ></base-mo00018>
                </div>
              </td>
              <td
                v-for="(cellItem, cellIndex) in item"
                :key="cellIndex"
                class="px-3"
              >
                <span v-if="cellItem.inputKbn === '1'">
                  {{ cellItem.knj.value }}
                </span>
                <span v-if="cellItem.inputKbn === '5'">{{ cellItem.startYmd.value }}</span>
                <span v-if="cellItem.inputKbn === '3'">{{ cellItem.cod.modelValue }}</span>
              </td>
            </tr>
          </template>
        </base-mo01354>
      </div>
    </div>
    <!-- ハイボタンダイアログ -->
    <g-base-or21814
      v-if="showDialog"
      v-bind="or21814_1"
    />
    <!-- はい、いいえボタンダイアログ -->
    <g-base-or21814
      v-if="showDialogOr21814_2"
      v-bind="or21814_2"
    />
    <!-- 共通入力支援画面 -->
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="input"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="setScreenTextAreaData"
    />
  </div>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
.v-col {
  padding: 0;
}
.action-button {
  .row-top {
    transform: rotate(90deg);
  }
  .row-bottom {
    transform: rotate(-90deg);
  }
}
.table-header :deep(.list-wrapper .v-table__wrapper th) {
  font-weight: bold;
  box-shadow: none !important;
  white-space: nowrap;
  padding: 0 0px 0 !important;
  background-color: rgba(219, 238, 254, 1) !important;
  height: 32px !important;
  font-size: 14px !important;
  font-weight: normal !important;
}

.table-header :deep(.list-wrapper .v-table__wrapper td) {
  height: 115px !important;
  background-color: #fff !important;
  // border-bottom: none !important;
  &:first-child {
    padding-left: 1px;
  }
  &:last-child {
    padding-right: 1px;
  }
  .calendar-table-cell {
    height: 100%;
  }
  .mr-2 {
    margin: 0px !important;
  }
}
:deep(.table-kikan-cell .table-kikan-content) {
  padding: 0 12px !important;
}
.icon :deep(.material-symbols-rounded) {
  cursor: pointer;
  font-variation-settings:
    'FILL' 1,
    'wght' 400,
    'GRAD' 0,
    'opsz' 20;
  opacity: 0.5;
  font-size: 20px;
}
:deep(.list-wrapper .v-table__wrapper tr:last-child th:last-child) {
  border-right: 1px solid rgba(var(--v-theme-black-200)) !important;
}
.tooltip-content {
  white-space: pre;
}
// テーブル操作ボタン
.table-action-button {
  .v-btn {
    height: 32px;
    min-width: 89px !important;
    padding: 0px !important;
  }
}
.case-action-button {
  margin-left: 500px;
  .v-btn {
    height: 32px;
    min-width: auto !important;
    padding: 0px 11px !important;
  }
}
.advancement-select {
  height: 100%;
  :deep(.v-field__field) {
    .v-field__input {
      height: 100% !important;
    }
  }
}
</style>
