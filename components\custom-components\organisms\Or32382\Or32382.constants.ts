import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or32382:週間表テンプレート
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or32382Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or32382', seq)

  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     *CANCEL
     */
    export const DIALOG_RESULT_CANCEL = 'cancel'
    /**
     *YES
     */
    export const DIALOG_RESULT_YES = 'yes'
    /**
     *NO
     */
    export const DIALOG_RESULT_NO = 'no'
    /**
     * 計画対象期間管理フラグ 0:管理しない
     */
    export const PLANNING_PERIOD_NO_MANAGE = '0'
    /**
     * 計画対象期間管理フラグ 1:管理する
     */
    export const PLANNING_PERIOD_MANAGE = '1'

    /**
     * 更新区分 新規
     */
    export const UPDATE_KBN_C = 'C'

    /**
     * 更新区分 更新
     */
    export const UPDATE_KBN_U = 'U'

    /**
     * 更新区分 削除
     */
    export const UPDATE_KBN_D = 'D'

    /**
     * 改行コード
     */
    export const LINE_BREAK = '\r\n'
    /**
     * 時間00:00
     */
    export const TIME_ZERO = '00:00'
    /**
     * 週単位
     */
    export const WEEK_UNIT = '9'
    /**
     * 週単位以外のサービス
     */
    export const WEEK_UNIT_OTHER_SERVICE = '99999999'
    /**
     * 時間表示区分: 表示なし
     */
    export const TIMD_DISP_KBN_BLANK = '0'

    /**
     * 時間表示区分: 先頭表示
     */
    export const TIMD_DISP_KBN_FIRST = '1'

    /**
     * 時間表示区分: 最後表示
     */
    export const TIMD_DISP_KBN_LAST = '2'
    /**
     * 曜日選択
     */
    export const YOUBI_SEL = '1'
    /**
     * 左
     */
    export const LEFT = 'left'
    /**
     * 右
     */
    export const RIGHT = 'right'
    /**
     * 中央揃え
     */
    export const CENTER = 'center'
    /**
     * 大分類CD
     */
    export const T1CD = '850'
    /**
     * 中分類CD
     */
    export const T2CD = '0'
    /**
     * 小分類CD
     */
    export const T3CD = '0'
    /**
     * カラム名
     */
    export const COLUMNNAME = 'w_igai_knj'
    /**
     * テーブル名
     */
    export const TABLENAME = 'cpn_tuc_cks51'
    /**
     * 本文末に追加
     */
    export const ADD_END_TEXT_IMPORT_TYPE = '0'

    /**
     * 本文上書
     */
    export const OVERWRITE_TEXT_IMPORT_TYPE = '1'
  }
}
