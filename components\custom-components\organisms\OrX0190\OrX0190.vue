<script setup lang="ts">
/**
 * OrX0190：有機体：（確定版）利用者(患者)基本情報について
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or27349Logic } from '../Or27349/Or27349.logic'
import { Or27349Const } from '../Or27349/Or27349.constants'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import { OrX0157Const } from '../OrX0157/OrX0157.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import type { NinteiList } from '../Or27349/Or27349.type'
import { OrX0190Const } from './OrX0190.constants'
import type { OrX0190OneWayType, OrX0190ValuesType } from './OrX0190.Type'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo01343OnewayType, Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
import type { Mo01267OnewayType } from '~/types/business/components/Mo01267Type'
import type { OrX0160Type } from '~/types/cmn/business/components/OrX0160Type'
/**************************************************
 * Props
 **************************************************/
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()

const { refValue } = useScreenTwoWayBind<OrX0190ValuesType>({
  cpId: OrX0190Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<OrX0190ValuesType> }
useScreenOneWayBind<OrX0190OneWayType>({
  cpId: OrX0190Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value!
      if (refValue.value.orX0190Values.taiYokaiKbn === OrX0190Const.DEFAULT.VALUE_0) {
        local.orX0190.yokaiKbn1.modelValue = refValue.value.orX0190Values.yokaiKbnFlg
        local.orX0190.yokaiKbn2.modelValue = ''
      } else if (refValue.value.orX0190Values.taiYokaiKbn === OrX0190Const.DEFAULT.VALUE_1) {
        local.orX0190.yokaiKbn1.modelValue = ''
        local.orX0190.yokaiKbn2.modelValue = refValue.value.orX0190Values.yokaiKbnFlg
      }
    },
  },
})

const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル
const or27349 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or27349Const.CP_ID(1)]: or27349.value,
})
/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  commonInfo: {} as TeX0012Type,
  orX0160Type: {
    startKikan: '',
    endKikan: '',
  },
  or51775: { modelValue: '' } as Or51775Type,
  or51775Value: '',
  mo01343: {
    value: '',
    endValue: '',
    mo00024: { isOpen: false },
  },
  orX0190: {
    yokaiKbn1: {
      modelValue: '',
    },
    yokaiKbn2: {
      modelValue: '',
    },
  },
})
const localOneway = reactive({
  codeListOneway: {} as Record<string, CodeType[]>,
  orX0160Oneway: {
    showItemLabel: false,
    itemLabel: t('label.validity_period_label'),
    isRequired: false,
    showEditBtnFlg: true,
  },
  or27349Oneway: {
    startYmd1: '',
  },
  // GUI00937 共通入力支援画面
  or51775Oneway: {
    screenId: OrX0190Const.DEFAULT.GUI,
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: OrX0190Const.DEFAULT.ASSESS_MENT_METHOD, // アセスメント方式 TBD
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  mo01343Oneway: {
    selectMode: OrX0190Const.DEFAULT.VALUE_1,
  } as Mo01343OnewayType,
  mo00020Oneway1: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
    maxlength: '10',
    width: '140px',
  } as Mo00020OnewayType,
  mo00020Oneway2: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: true,
    itemLabel: t('label.application_in_progress_date_label'),
    showSelectArrow: false,
    maxlength: '10',
    width: '140px',
    customClass: { outerClass: 'mx-2' } as CustomClass,
  } as Mo00020OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Oneway: {
    valueFontWeight: 'blod',
    value: t('label.user_basic_information_label'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
      outerClass: 'content-title',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039Oneway: {
    itemLabel: t('label.residence_type_label'),
    showItemLabel: true,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,
  mo00039Oneway2: {
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,

  // 文字数入力
  mo00038Oneway1: {
    mo00045Oneway: {
      appendLabel: t('label.building_floors_label'),
      showItemLabel: false,
      maxLength: '999',
      customClass: new CustomClass({
        outerClass: 'input-container',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  // 文字数入力
  mo00038Oneway2: {
    mo00045Oneway: {
      itemLabel: t('label.room_label'),
      appendLabel: t('label.floor_label'),
      showItemLabel: true,
      isVerticalLabel: false,
      maxLength: '20',
      customClass: new CustomClass({
        outerClass: 'input-container',
        labelClass: 'd-flex align-center mr-2',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  // 文字数入力
  mo00038Oneway3: {
    mo00045Oneway: {
      appendLabel: t('label.percentage_label'),
      showItemLabel: false,
      maxLength: '20',
      customClass: new CustomClass({
        outerClass: 'input-container',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  mo00040Oneway1: {
    itemTitle: 'label',
    itemValue: 'value',
    showItemLabel: false,
    itemLabelFontWeight: 'bold',
    width: '77px',
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,
  mo00040Oneway2: {
    itemTitle: 'label',
    itemValue: 'value',
    showItemLabel: false,
    itemLabelFontWeight: 'bold',
    width: '77px',
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,
  mo00018Oneway1: {
    name: t('label.application_in_progress_flag'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.application_in_progress_flag'),
  } as Mo00018OnewayType,
  mo00018Oneway2: {
    name: t('label.classification_change'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.classification_change'),
  } as Mo00018OnewayType,
  mo00018Oneway3: {
    name: t('label.unapplied_flag'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.unapplied_flag'),
  } as Mo00018OnewayType,
  mo00018Oneway4: {
    name: t('label.physician_judgment'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.physician_judgment'),
  } as Mo00018OnewayType,
  mo00018Oneway5: {
    name: t('label.care_manager_judgment'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.care_manager_judgment'),
  } as Mo00018OnewayType,
  orX0157Oneway: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: true,
        itemLabel: t('label.special_notes'),
        width: '620px',
        maxlength: '44',
      },
    },
  },
  orX0157Oneway2: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: false,
        width: '381px',
        maxlength: '10',
      },
    },
  },
  mo01267Oneway: {
    btnLabel: t('label.care-level-at-discharge'),
    to: '',
  } as Mo01267OnewayType,
  pensionDataList: [
    {
      label: t('label.national_pension'),
      key: 'nenkin1Umu',
    },
    {
      label: t('label.employee_pension'),
      key: 'nenkin2Umu',
    },
    {
      label: t('label.disability_pension'),
      key: 'nenkin3Umu',
    },
    {
      label: t('label.welfare_pension'),
      key: 'nenkin4Umu',
    },
    {
      label: t('label.other_pension'),
      key: 'nenkin5Umu',
    },
  ],
  shogaiKbnDataList: [
    {
      label: t('label.physical_disability_recognition'),
      key: 'shintaiShogaiKbn',
    },
    {
      label: t('label.mental_disability_recognition'),
      key: 'chitekiShogaiKbn',
    },
    {
      label: t('label.intellectual_disability_recognition'),
      key: 'seishinShogaiKbn',
    },
  ],
})

// ダイアログ表示フラグ
const showDialogOr27349 = computed(() => {
  // Or27349のダイアログ開閉状態
  return Or27349Logic.state.get(or27349.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === OrX0190Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === OrX0190Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  ;(refValue.value.orX0190Values[local.or51775Value] as unknown as Mo00045Type).value =
    setOrAppendValue(
      (refValue.value.orX0190Values[local.or51775Value] as unknown as Mo00045Type).value ?? '',
      data
    )
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleNenkinMemoKnj = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  localOneway.or51775Oneway.title = t('label.pension_type_label')
  localOneway.or51775Oneway.t2Cd = OrX0190Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = OrX0190Const.DEFAULT.VALUE_5
  localOneway.or51775Oneway.columnName = 'nenkin_memo_knj'
  localOneway.or51775Oneway.inputContents = t('label.pension_type_label')
  local.or51775Value = 'nenkinMemoKnj'
  local.or51775.modelValue = refValue.value.orX0190Values.nenkinMemoKnj?.value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleCare = () => {
  Or27349Logic.state.set({
    uniqueCpId: or27349.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * or27349の値変更を監視
 *
 * @param or27349 - Or27349Type
 */
const handleOr27349 = (or27349: NinteiList) => {
  console.log(or27349, 12331)
}

/**
 * mo00039の値変更を監視
 *
 * @param mo00039 - Mo00039Type
 */
const handleMo00039 = (mo00039: string) => {
  if (mo00039 === OrX0190Const.DEFAULT.VALUE_0) {
    local.orX0190.yokaiKbn2.modelValue = ''
    refValue.value.orX0190Values.yokaiKbnFlg = local.orX0190.yokaiKbn1.modelValue
  } else if (mo00039 === OrX0190Const.DEFAULT.VALUE_1) {
    local.orX0190.yokaiKbn1.modelValue = ''
    refValue.value.orX0190Values.yokaiKbnFlg = local.orX0190.yokaiKbn2.modelValue
  }
}

/**
 * mo00039の値変更を監視
 *
 * @param mo00039 - Mo00039Type
 */
const handleShogaiNintei = (mo00039: string) => {
  if (mo00039 === OrX0190Const.DEFAULT.VALUE_0) {
    refValue.value.orX0190Values.shintaiShogaiKbn.modelValue = false
    refValue.value.orX0190Values.chitekiShogaiKbn.modelValue = false
    refValue.value.orX0190Values.seishinShogaiKbn.modelValue = false
  }
}

/**
 * mo00018の値変更を監視
 *
 * @param mo00018 - mo00018
 */
const handleMo00018 = (mo00018: Mo00018Type) => {
  if (mo00018.modelValue) {
    refValue.value.orX0190Values.shogaiTechouUmu = OrX0190Const.DEFAULT.VALUE_1
  }
}

/**
 * mo01343の値変更を監視
 *
 * @param mo01343 - Mo01343Type
 */
const handleMo01343 = (mo01343: Mo01343Type) => {
  refValue.value.orX0190Values.ninteiStartYmd.value = mo01343.value
  refValue.value.orX0190Values.ninteiEndYmd.value = mo01343.endValue
  local.orX0160Type.startKikan = mo01343.value
  local.orX0160Type.endKikan = mo01343.endValue
}

/**
 * OrX0160Typeの値変更を監視
 *
 * @param OrX0160 - OrX0160Type
 */
const handleOrX0160 = (OrX0160: OrX0160Type) => {
  refValue.value.orX0190Values.ninteiStartYmd.value = OrX0160.startKikan
  refValue.value.orX0190Values.ninteiEndYmd.value = OrX0160.endKikan
  local.mo01343.value = OrX0160.startKikan
  local.mo01343.endValue = OrX0160.endKikan
}
/**
 * codeListOnewayの値変更を監視
 *
 */
watch(
  () => localOneway.codeListOneway,
  (newVal) => {
    local.mo01343.value = refValue.value.orX0190Values.ninteiStartYmd.value
    local.mo01343.endValue = refValue.value.orX0190Values.ninteiEndYmd.value
    local.orX0160Type.startKikan = refValue.value.orX0190Values.ninteiStartYmd.value
    local.orX0160Type.endKikan = refValue.value.orX0190Values.ninteiEndYmd.value
    localOneway.mo00040Oneway1.items = newVal.SUPPORT_LEVEL_VALUE
    localOneway.mo00040Oneway2.items = newVal.CARE_LEVEL_VALUE
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0012Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    if (newValue?.teikyouId) {
      local.commonInfo = newValue
      localOneway.mo01267Oneway.disabled =
        local.commonInfo.nursingCareInsuranceAuthorityFlag === OrX0190Const.DEFAULT.VALUE_0
      localOneway.or51775Oneway.userId = newValue.userId ?? ''
    }
  },
  { deep: true }
)
</script>

<template>
  <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
  <div
    v-if="refValue.orX0190Values"
    class="box"
  >
    <!--退居（所）時の要介護度-->
    <div class="title">
      <base-mo01267
        :oneway-model-value="localOneway.mo01267Oneway"
        @click="handleCare"
      />
      <g-custom-or-27349
        v-if="showDialogOr27349"
        v-bind="or27349"
        :oneway-model-value="localOneway.or27349Oneway"
        @confirm="handleOr27349"
      />
    </div>
    <div class="care-level">
      <div class="d-flex align-center data-cell">
        <base-mo00039
          v-model="refValue.orX0190Values.taiYokaiKbn"
          :oneway-model-value="localOneway.mo00039Oneway2"
          @update:model-value="handleMo00039"
        >
          <div
            v-for="(item, index) in localOneway.codeListOneway
              .CONFIRMATION_INFO_CARE_LEVEL_AT_ADMISSION_CONFIRMED"
            :key="'radio' + '_' + index"
            class="d-flex mr-2"
          >
            <base-at-radio
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
            <base-mo00040
              v-if="index === 0"
              v-model="local.orX0190.yokaiKbn1"
              class="ml-1"
              :disabled="refValue.orX0190Values.taiYokaiKbn === OrX0190Const.DEFAULT.VALUE_1"
              :oneway-model-value="localOneway.mo00040Oneway1"
            />
            <base-mo00040
              v-if="index === 1"
              v-model="local.orX0190.yokaiKbn2"
              class="ml-1"
              :disabled="refValue.orX0190Values.taiYokaiKbn === OrX0190Const.DEFAULT.VALUE_0"
              :oneway-model-value="localOneway.mo00040Oneway2"
            />
          </div>
        </base-mo00039>
        <!-- 有効期限 -->
        <div class="d-flex align-center ml-12">
          {{ t('label.validity_period_label') }}
          <g-custom-or-x-0160
            v-model="local.orX0160Type"
            class="ml-4"
            :oneway-model-value="localOneway.orX0160Oneway"
            @on-click-edit-btn="local.mo01343.mo00024.isOpen = true"
            @update:model-value="handleOrX0160"
          ></g-custom-or-x-0160>
          <base-mo01343
            v-model="local.mo01343"
            :oneway-model-value="localOneway.mo01343Oneway"
            @update:model-value="handleMo01343"
          />
        </div>
      </div>
      <div class="d-flex">
        <div class="d-flex align-center">
          <base-mo00018
            v-model="refValue.orX0190Values.ninteiShinseiFlg"
            :oneway-model-value="localOneway.mo00018Oneway1"
          />(
          <base-mo00020
            v-model="refValue.orX0190Values.ninteiShinseiYmd"
            :oneway-model-value="localOneway.mo00020Oneway2"
          />)
        </div>
        <div class="d-flex align-center ml-12 mr-12">
          <base-mo00018
            v-model="refValue.orX0190Values.ninteiKbnHenkouFlg"
            :oneway-model-value="localOneway.mo00018Oneway2"
          />(
          <base-mo00020
            v-model="refValue.orX0190Values.ninteiKbnHenkouYmd"
            :oneway-model-value="localOneway.mo00020Oneway2"
          />)
        </div>
        <base-mo00018
          v-model="refValue.orX0190Values.ninteiMishinseiFlg"
          :oneway-model-value="localOneway.mo00018Oneway3"
        />
      </div>
    </div>
    <div class="title">
      {{ t('label.everydaylife-life-independence-level') }}
    </div>
    <div class="mt-6 mb-6">
      <!-- 障害高齢者の日常生活自立度 -->
      <div class="data-cell">
        <p>{{ t('label.degree_indep_daily_living_eld_disab') }}</p>
        <base-mo00039
          v-model="refValue.orX0190Values.shogaiJiritsuCd"
          :oneway-model-value="localOneway.mo00039Oneway2"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway
              .CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DISAB"
            :key="index"
            :name="'radio' + '-' + index"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
      </div>
      <!-- 認知症高齢者の日常生活自立度 -->
      <div class="data-cell">
        <p>{{ t('label.dementia_daily_living_independence_degree') }}</p>
        <base-mo00039
          v-model="refValue.orX0190Values.ninchiJiritsuCd"
          :oneway-model-value="localOneway.mo00039Oneway2"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway
              .CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DEM"
            :key="index"
            :name="'radio' + '-' + index"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
      </div>
    </div>
    <!-- 介護保険、障害、年金など -->
    <div class="title">
      {{ t('label.long-term-care-insurance-disability-pension-etc') }}
    </div>
    <div class="mt-6 mb-6">
      <div class="data-cell">
        <p>{{ t('label.self_payment_ratio_label') }}</p>
        <base-mo00039
          v-model="refValue.orX0190Values.futanWariFlg"
          :oneway-model-value="localOneway.mo00039Oneway2"
        >
          <div
            v-for="(item, index) in localOneway.codeListOneway
              .CONFIRMATION_INFO_OWN_BURDEN_RATIO_BEDSORE"
            :key="'radio' + '_' + index"
            class="d-flex"
          >
            <base-at-radio
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
            <base-mo00038
              v-if="index === 0"
              v-model="refValue.orX0190Values.futanWariai"
              :oneway-model-value="localOneway.mo00038Oneway3"
            ></base-mo00038>
          </div>
        </base-mo00039>
      </div>
      <!-- 障害手帳の有無 -->
      <div class="data-cell">
        <p>{{ t('label.shogai-techou-umu') }}</p>
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.orX0190Values.shogaiTechouUmu"
            :oneway-model-value="localOneway.mo00039Oneway2"
            @update:model-value="handleShogaiNintei"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="index"
              :name="'radio' + '-' + index"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
          <div class="d-flex align-center">
            (
            <base-mo00018
              v-for="(item, index) in localOneway.shogaiKbnDataList"
              :key="index"
              v-model="refValue.orX0190Values[item.key]"
              :oneway-model-value="
                {
                  name: item.label,
                  hideDetails: true,
                  showItemLabel: false,
                  itemLabel: '',
                  checkboxLabel: item.label,
                } as Mo00018OnewayType
              "
              @update:model-value="handleMo00018"
            />
            )
          </div>
        </div>
      </div>
      <!-- 年金などの種類 -->
      <div>
        <p>{{ t('label.pension_type_label') }}</p>
        <div class="d-flex">
          <base-mo00018
            v-for="(item, index) in localOneway.pensionDataList"
            :key="index"
            v-model="refValue.orX0190Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: false,
              itemLabel: '',
              checkboxLabel: item.label,
            }"
          />
          <div class="d-flex align-center ml-6">
            <g-custom-or-x-0157
              v-model="refValue.orX0190Values.nenkinMemoKnj"
              :oneway-model-value="localOneway.orX0157Oneway2"
              @on-click-edit-btn="handleNenkinMemoKnj"
            ></g-custom-or-x-0157>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="local.or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleOr51775Confirm"
  />
</template>

<style scoped lang="scss">
.content-title {
  padding: 11px 24px;
  background-color: #e6e6e6;
  height: 48px;
}
.title {
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 48px;
  background-color: #0760e614;
  font-weight: Bold;
  font-size: 14px;
  :deep(.v-btn__content) {
    font-weight: bold;
    color: #214d97; /* 文字颜色 */
  }
}
.box {
  margin: 24px 50px 24px 48px;
}
:deep(.v-selection-control-group) {
  align-items: center !important;
}
:deep(.v-row--no-gutters) {
  margin: 0 !important;
}
.data-cell {
  margin-bottom: 16px;
}
:deep(.v-btn--density-default) {
  height: 100% !important;
  min-height: 0 !important;
}
:deep(.kikan-range-container) {
  .d-flex {
    align-items: center;
  }
}
.care-level {
  padding: 11px 0 24px 0;
}
:deep(.v-input__append) {
  margin-inline-start: 8px !important ;
}
:deep(.input-container) {
  .v-field {
    width: 48px !important;
  }
}
</style>
