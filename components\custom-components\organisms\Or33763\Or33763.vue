<script setup lang="ts">
/**
 * Or33763:（アセスメント(インターライ) ）T画面
 * GUI00784_アセスメント(インターライ)画面T
 *
 * @description
 * （アセスメント(インターライ) ）T画面
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */

import { onMounted, computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or33763Const } from './Or33763.constants'
import type { Or33763StateType, Tjyouho, Or33763TwowayType } from './Or33763.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import {
  useSetupChildProps,
  useScreenOneWayBind,
  // useScreenInitFlg,
  useScreenTwoWayBind,
  useScreenStore,
} from '#imports'
import {} from '~/composables/useComponentVue'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type {
  // Mo00046Type,
  Mo00046OnewayType,
} from '~/types/business/components/Mo00046Type'
import type { Or33740OnewayType } from '~/types/cmn/business/components/Or33740Type'
import type {
  // Mo00038Type,
  Mo00038OnewayType,
} from '~/types/business/components/Mo00038Type'
// import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import { Or10412Const } from '~/components/custom-components/organisms/Or10412/Or10412.constants'
import type { Or10412Type, Or10412OnewayType } from '~/types/cmn/business/components/Or10412Type'
import { Or10412Logic } from '~/components/custom-components/organisms/Or10412/Or10412.logic'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type { Or33763OnewayType } from '~/types/cmn/business/components/Or33763Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  AssessmentInterRAITUpdateOutEntity,
  AssessmentInterRAITInEntity,
  AssessmentInterRAITInsertInEntity,
  AssessmentInterRAITOutEntity,
  SubInfoT,
} from '~/repositories/cmn/entities/AssessmentInterRAITEntity'
import type {
  AssessmentInterRAIAHistorySelectEntity,
  AssessmentInterRAIAHistorySelectOutEntity,
} from '~/repositories/cmn/entities/assessmentInterRAIAHistoryEntity'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { TransmitParam } from '~/types/cmn/business/components/TeX0003Type'
import type {
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Or30981OnewayType } from '~/types/cmn/business/components/Or30981Type'
import type { Mo01355OnewayType } from '@/types/business/components/Mo01355Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
// import { useColorUtils } from '~/utils/useColorUtils'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { Or30981Const } from '~/components/custom-components/organisms/Or30981/Or30981.constants'

// const { convertDecimalToHex } = useColorUtils()
/**************************************************
 * Props
 **************************************************/
/**
 * Propsの定義
 */
interface Props {
  onewayModelValue: Or33763OnewayType
  uniqueCpId: string
}
/**
 * Props
 */
const props: Props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * 変数の定義
 */
const { t } = useI18n()

/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * 画面共通ルーティングコンポーネント
 */
const cmnRouteCom = useCmnRouteCom()
/**
 * 画面ストア
 */
const screenStore = useScreenStore()
/**
 * 子コンポーネント用変数 or10412
 */
const or10412 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント用変数 or21814
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント用変数 or51775
 */
const or51775 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネント 30981
 */
const or30981 = ref([
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
])
/**
 * 画面項目区分
 */
const vieTypeKbn = ref<string>('')

/**
 * 画面初期化フラグを管理するPiniaストアの値
 */
// const isInit = useScreenInitFlg()

/**
 * Or33763の単方向バインドモデルのデフォルト値を定義
 */
const defaultOnewayModelValue: Or33763OnewayType = {
  periodManageFlag: '0',
  shisetuId: '',
  userId: '',
  officeId: '',
  syubetsuId: '',
  applicableOfficeIdList: [],
  OfficeId: '',
  applicableOfficeGroupId: '',
  planPeriodId: '',
  historyId: '',
  surveyAssessmentKind: '1',
}

/**
 * Or33763のモデル値のデフォルト値を定義
 */
const defaultModelValue = {
  // アセスメント種別リスト
  surveyAssessmentKindList: [] as CodeType[],
}

//セクションキーの型定義
type SectionKey = `t${1 | 2 | 3 | 4 | 5}`

/**
 *セクションの設定情報を定義
 */
const sectionConfig: {
  key: SectionKey
  titleLabel: string
  subtitleLabel?: string
  buttonLabels: { value: string; label: string }[]
  checkboxLabels?: { key: string; value: boolean; label: string }[]
  max: number
  layoutType: string
  rows: number
  isCheckBox: boolean
  isNote?: boolean
  width?: string
  inline?: boolean
  widthItems?: string
}[] = [
  {
    key: 't1',
    titleLabel: 'label.interRAI-method-care-assessment-situation-t1',
    checkboxLabels: [],
    buttonLabels: [
      {
        value: '0',
        label: 'label.T-situation-section-T1-btn0',
      },
      {
        value: '1',
        label: 'label.T-situation-section-T1-btn1',
      },
    ],
    max: 1,
    layoutType: '3',
    rows: 2,
    isCheckBox: false,
    isNote: false,
    width: '377px',
    inline: true,
    widthItems: '180px',
  },
  {
    key: 't2',
    titleLabel: 'label.interRAI-method-care-assessment-situation-t2',
    checkboxLabels: [],
    buttonLabels: [
      {
        value: '0',
        label: 'label.T-situation-section-T2-btn0',
      },
      {
        value: '1',
        label: 'label.T-situation-section-T2-btn1',
      },
      {
        value: '2',
        label: 'label.T-situation-section-T2-btn2',
      },
    ],
    max: 2,
    layoutType: '3',
    rows: 2,
    isCheckBox: false,
    isNote: false,
    inline: false,
  },
  {
    key: 't2',
    titleLabel: 'label.interRAI-method-care-assessment-situation-note',
    checkboxLabels: [],
    buttonLabels: [],
    max: 2,
    layoutType: '3',
    rows: 2,
    isCheckBox: false,
    isNote: true,
  },
  {
    key: 't3',
    titleLabel: 'label.interRAI-method-care-assessment-situation-t3',
    subtitleLabel: 'label.interRAI-method-care-assessment-situation-t3-subtitle',
    buttonLabels: [],
    checkboxLabels: [
      {
        key: 't3G2aFlg',
        value: false,
        label: 'label.T-situation-section-T3-check0',
      },
      {
        key: 't3G2bFlg',
        value: false,
        label: 'label.T-situation-section-T3-check1',
      },
      {
        key: 't3G2cFlg',
        value: false,
        label: 'label.T-situation-section-T3-check2',
      },
      {
        key: 't3G2dFlg',
        value: false,
        label: 'label.T-situation-section-T3-check3',
      },
      {
        key: 't3G2eFlg',
        value: false,
        label: 'label.T-situation-section-T3-check4',
      },
      {
        key: 't3G2fFlg',
        value: false,
        label: 'label.T-situation-section-T3-check5',
      },
      {
        key: 't3G2gFlg',
        value: false,
        label: 'label.T-situation-section-T3-check6',
      },
      {
        key: 't3G2hFlg',
        value: false,
        label: 'label.T-situation-section-T3-check7',
      },
      {
        key: 't3G2iFlg',
        value: false,
        label: 'label.T-situation-section-T3-check8',
      },
      {
        key: 't3G2jFlg',
        value: false,
        label: 'label.T-situation-section-T3-check9',
      },
    ],
    max: 10,
    layoutType: '2',
    rows: 2,
    isCheckBox: true,
    isNote: false,
  },
  {
    key: 't4',
    titleLabel: 'label.interRAI-method-care-assessment-situation-t4',
    subtitleLabel: 'label.interRAI-method-care-assessment-situation-t4-subtitle',
    checkboxLabels: [
      {
        key: 't4G1aFlg',
        value: false,
        label: 'label.T-situation-section-T4-check0',
      },
      {
        key: 't4G1bFlg',
        value: false,
        label: 'label.T-situation-section-T4-check1',
      },
      {
        key: 't4G1cFlg',
        value: false,
        label: 'label.T-situation-section-T4-check2',
      },
      {
        key: 't4G1dFlg',
        value: false,
        label: 'label.T-situation-section-T4-check3',
      },
      {
        key: 't4G1eFlg',
        value: false,
        label: 'label.T-situation-section-T4-check4',
      },
      {
        key: 't4G1fFlg',
        value: false,
        label: 'label.T-situation-section-T4-check5',
      },
      {
        key: 't4G1gFlg',
        value: false,
        label: 'label.T-situation-section-T4-check6',
      },
      {
        key: 't4G1hFlg',
        value: false,
        label: 'label.T-situation-section-T4-check7',
      },
    ],
    buttonLabels: [],
    max: 8,
    layoutType: '2',
    rows: 2,
    isCheckBox: true,
    isNote: false,
  },
  {
    key: 't5',
    titleLabel: 'label.interRAI-method-care-assessment-situation-t5',
    buttonLabels: [
      {
        value: '0',
        label: 'label.T-situation-section-T5-btn0',
      },
      {
        value: '3',
        label: 'label.T-situation-section-T5-btn3',
      },
      {
        value: '1',
        label: 'label.T-situation-section-T5-btn1',
      },
      {
        value: '4',
        label: 'label.T-situation-section-T5-btn4',
      },
      {
        value: '2',
        label: 'label.T-situation-section-T5-btn2',
      },
      {
        value: '8',
        label: 'label.T-situation-section-T5-btn8',
      },
    ],
    max: 8,
    layoutType: '1',
    rows: 4,
    isCheckBox: false,
    isNote: false,
    width: '628px',
    inline: true,
    widthItems: '306px',
  },
]

/**
 * セクションのローカルモデルを定義
 */
const localModelOneway = sectionConfig.map((config) => {
  const key = config.key
  return {
    key,
    min: 0,
    max: config.max,
    isCheckBox: config.isCheckBox,
    isNote: config.isNote,
    width: config?.width ?? '',
    inline: config?.inline ?? false,
    widthItems: config?.widthItems ?? '',
    title: {
      value: t(config.titleLabel),
      valueFontWeight: 'bolder',
      customClass: {
        labelClass: 'asection_left_title_left_label',
        itemClass: '',
      },
    } as Mo01338OnewayType,
    subtitle: config.subtitleLabel
      ? ({
          value: t(config.subtitleLabel),
          customClass: {
            labelClass: 'ml-0',
          } as CustomClass,
        } as Mo01338OnewayType)
      : undefined,
    icon: {
      btnIcon: 'edit_square',
      size: '36px',
      color: '#859FC9',
      labelColor: '#EBF2FD',
      density: 'default',
    } as Mo00009OnewayType,
    textarea: {
      class: `${key}TextArea`,
      maxlength: '4000',
      showItemLabel: false,
      noResize: true,
      autoGrow: false,
      rows: config.rows,
      style: '',
    } as Mo00046OnewayType,
    buttons: {
      btnItems: config.buttonLabels.map((item) => ({
        label: t(item.label),
        value: item.value,
      })),
      layoutType: config.layoutType,
    } as Or33740OnewayType,
    textField: {
      mo00045Oneway: {
        width: '60px',
        maxLength: '1',
        showItemLabel: false,
        readonly: config.isCheckBox,
        class: '',
      },
    } as Mo00038OnewayType,
    checkBox: {
      mo00018Oneway: {
        showItemLabel: false,
        checkboxLabel: '',
        hideDetails: true,
        class: 'custom-label-checkbox',
      } as Mo00018OnewayType,
      checkBoxItems: config.checkboxLabels?.map((item) => ({
        label: t(item.label),
        value: item.value,
        key: item.key,
      })) as { key: string; value: boolean; label: string }[],
    },
    or30981OnewayType: {
      /**
       * アイテム
       */
      items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
      /**
       * メモテキストエリア
       */
      orX0156OnewayType: {
        itemLabel: t('label.memo'),
        showItemLabel: true,
        showDividerLineFlg: true,
        showEditBtnFlg: true,
        isVerticalLabel: true,
        customClass: {
          labelClass: 'pa-0',
        } as CustomClass,
        maxRows: '3',
        rows: '3',
        noResize: true,
        autoGrow: false,
      } as OrX0156OnewayType,
      /**
       * ラベル
       */
      mo13380OnewayType: {
        value: t('label.letter-size'),
        valueFontWeight: 'normal',
        customClass: {
          labelStyle: 'display: none',
          itemClass: 'align-center',
        } as CustomClass,
      } as Mo01338OnewayType,
      /**
       * モーダルでカラーピッカー
       */
      mo01355Oneway: {} as Mo01355OnewayType,
      /**
       * 色の設定アイコンボタン
       */
      orX0169OnewayType: {
        scaleSize: '24px',
        useDefaultColorPicker: true,
      } as OrX0169OnewayType,
      disabled: false,
    } as Or30981OnewayType,
  }
})

/**
 * Or33763のローカルデータを格納するリアクティブオブジェクト
 */
const localOneway = reactive({
  or33763Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 入力対象外のアセスメント項目をグレーで表示します
  mo01338OnewayInputTip: {
    value: '',
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: 'mr-4',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentItem',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  or10412Oneway: {
    selectItemNo: '',
    userId: '',
    fontColor: '',
    historyTable: '',
    historyTableColum: '',
    meMoContent: '',
    textSize: '',
    flag: '',
  } as Or10412OnewayType,
  mo00039Oneway: {
    showItemLabel: false,
    hideDetails: true,
    checkOff: true,
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as unknown as Mo00039OnewayType,
  or30981OnewayType: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * モーダルでカラーピッカー
     */
    mo01355Oneway: {} as Mo01355OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
    disabled: false,
  } as Or30981OnewayType,
  or51775OnewayTypeOther: {
    title: '',
    classificationID: '2',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
  } as Or51775OnewayType,
})

/**
 * 削除区分を管理するリアクティブ変数
 */
const deleteKbn = ref('')

/**
 * システム共有情報取得
 */
const commomInfo: TransmitParam = {
  executeFlag: '',
  kikanKanriFlg: '',
  houjinId: '',
  shisetuId: '',
  userId: '',
  svJigyoId: '',
  kijunbiYmd: '',
  sakuseiId: '',
  historyModifiedCnt: '',
  historyInfo: {} as HistoryInfoEntity,
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  deleteBtnValue: '',
  raiId: '',
  pageFlag: '',
}

/**
 * レスポンス情報を格納するオブジェクト
 */
const respInfo = {
  executeFlag: '',
  kikanKanriFlg: '',
  houjinId: '1',
  shisetuId: '1',
  userId: '1',
  svJigyoId: '1',
  kijunbiYmd: '',
  sakuseiId: '1',
  historyModifiedCnt: '',
  // 計画期間情報
  planPeriodInfo: {
    sc1Id: 0,
    periodNo: 0,
    periodCnt: 0,
    startYmd: '',
    endYmd: '',
  } as unknown as PlanPeriodInfoEntity,
  // 履歴情報
  historyInfo: {
    createId: 0,
    sc1Id: '',
    raiId: '',
    assType: '',
    capType: '',
    plnType: '',
    shokuinId: '',
    assDateYmd: '',
    krirekiNo: 0,
    krirekiCnt: 0,
  } as unknown as HistoryInfoEntity,
  // サブ情報
  subInfo: {
    raiId: '',
    t1: '',
    t2: '',
    t3: '',
    t3G2aFlg: '',
    t3G2bFlg: '',
    t3G2cFlg: '',
    t3G2dFlg: '',
    t3G2eFlg: '',
    t3G2fFlg: '',
    t3G2gFlg: '',
    t3G2hFlg: '',
    t3G2iFlg: '',
    t3G2jFlg: '',
    t4: '',
    t4G1aFlg: '',
    t4G1bFlg: '',
    t4G1cFlg: '',
    t4G1dFlg: '',
    t4G1eFlg: '',
    t4G1fFlg: '',
    t4G1gFlg: '',
    t4G1hFlg: '',
    t5: '',
    t1MemoKnj: '',
    t1MemoFont: '',
    t1MemoColor: '',
    t2MemoKnj: '',
    t2MemoFont: '',
    t2MemoColor: '',
    t3MemoKnj: '',
    t3MemoFont: '',
    t3MemoColor: '',
    t4MemoKnj: '',
    t4MemoFont: '',
    t4MemoColor: '',
    t5MemoKnj: '',
    t5MemoFont: '',
    t5MemoColor: '',
  } as Tjyouho,
}

// /**
//  * 過去90日間(または前回アセスメント以降)におけるケア目標の達成メモ
//  */
// const mo00046TypeT1Area = ref<Mo00046Type>({
//   value: '',
// })

// /**
//  * 過去90日間(または前回アセスメント以降)におけるケア目標の達成区分
//  */
// const mo00038TypeT1TextField = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 90日前(または前回アセスメント時)と比較した全体の自立度の変化メモ
//  */
// const mo00046TypeT2Area = ref<Mo00046Type>({
//   value: '',
// })

// /**
//  * 90日前(または前回アセスメント時)と比較した全体の自立度の変化区分
//  */
// const mo00038TypeT2TextField = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * 自立していたADLの数
//  */
// const mo00046TypeT3Area = ref<Mo00046Type>({
//   value: '',
// })

// /**
//  * 自立していたADLの数ラベル
//  */
// const mo00038TypeT3TextField = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * T3_ADL_入浴
//  */
// const mo00018TypeT3G2aFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T3_ADL_個人衛生
//  */
// const mo00018TypeT3G2bFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T3_ADL_上半身の更衣
//  */
// const mo00018TypeT3G2cFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T3_ADL_下半身の更衣
//  */
// const mo00018TypeT3G2dFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T3_ADL_歩行
//  */
// const mo00018TypeT3G2eFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T3_ADL_移動
//  */
// const mo00018TypeT3G2fFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T3_ADL_トイレへの移乗
//  */
// const mo00018TypeT3G2gFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T3_ADL_トイレの使用
//  */
// const mo00018TypeT3G2hFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T3_ADL_ベッド上の可動性
//  */
// const mo00018TypeT3G2iFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T3_ADL_食事
//  */
// const mo00018TypeT3G2jFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * t4 自立していたIADLの数
//  */
// const mo00046TypeT4Area = ref<Mo00046Type>({
//   value: '',
// })

// /**
//  * 自立していたIADLの数
//  */
// const mo00038TypeT4TextField = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

// /**
//  * T4_IADL_食事の用意
//  */
// const mo00018TypeT4G1aFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T4_IADL_家事一般
//  */
// const mo00018TypeT4G1bFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T4_IADL_金銭管理
//  */
// const mo00018TypeT4G1cFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T4_IADL_薬の管理
//  */
// const mo00018TypeT4G1dFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T4_IADL_電話の利用
//  */
// const mo00018TypeT4G1eFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T4_IADL_階段
//  */
// const mo00018TypeT4G1fFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T4_IADL_買い物
//  */
// const mo00018TypeT4G1gFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

// /**
//  * T4_IADL_外出
//  */
// const mo00018TypeT4G1hFlg = ref<Mo00018Type>({
//   modelValue: false,
// })

/**
 * 増悪原因の起こった時期
 */
// const mo00046TypeT5Area = ref<Mo00046Type>({
//   value: '',
// })

/**
 * T5セクションの入力値を管理します
 */
// const mo00038TypeT5TextField = ref<Mo00038Type>({
//   mo00045: {
//     value: '',
//   } as Mo00045Type,
// })

/**
 * GUI00787 ［メモ入力］画面の双方向バインドModelValue
 */
const or10412Type = ref<Or10412Type>({
  selectItemNo: '',
  userId: '',
  fontColor: '',
  historyTable: '',
  historyTableColum: '',
  meMoContent: '',
  textSize: '',
  flag: '',
})

//T3セクションのサブキーの型定義
type SectionKeyT3 = `t3G2${'a' | 'b' | 'c' | 'd' | 'e' | 'f' | 'g' | 'h' | 'i' | 'j'}Flg`

//T4セクションのサブキーの型定義
type SectionKeyT4 = `t4G1${'a' | 'b' | 'c' | 'd' | 'e' | 'f' | 'g' | 'h'}Flg`

/**
 * T3およびT4セクションのチェックボックスデータをマッピング
 */
// const mo00018TextFieldT34Maping: Record<SectionKeyT3 | SectionKeyT4, Ref<Mo00018Type>> = {
//   t3G2aFlg: mo00018TypeT3G2aFlg,
//   t3G2bFlg: mo00018TypeT3G2bFlg,
//   t3G2cFlg: mo00018TypeT3G2cFlg,
//   t3G2dFlg: mo00018TypeT3G2dFlg,
//   t3G2eFlg: mo00018TypeT3G2eFlg,
//   t3G2fFlg: mo00018TypeT3G2fFlg,
//   t3G2gFlg: mo00018TypeT3G2gFlg,
//   t3G2hFlg: mo00018TypeT3G2hFlg,
//   t3G2iFlg: mo00018TypeT3G2iFlg,
//   t3G2jFlg: mo00018TypeT3G2jFlg,
//   t4G1aFlg: mo00018TypeT4G1aFlg,
//   t4G1bFlg: mo00018TypeT4G1bFlg,
//   t4G1cFlg: mo00018TypeT4G1cFlg,
//   t4G1dFlg: mo00018TypeT4G1dFlg,
//   t4G1eFlg: mo00018TypeT4G1eFlg,
//   t4G1fFlg: mo00018TypeT4G1fFlg,
//   t4G1gFlg: mo00018TypeT4G1gFlg,
//   t4G1hFlg: mo00018TypeT4G1hFlg,
// }

/**
 * 各セクションのテキストフィールドデータをマッピング
 */
// const mo00038TextFieldMaping: Record<SectionKey, Ref<Mo00038Type>> = {
//   t1: mo00038TypeT1TextField,
//   t2: mo00038TypeT2TextField,
//   t3: mo00038TypeT3TextField,
//   t4: mo00038TypeT4TextField,
//   t5: mo00038TypeT5TextField,
// }

/**
 * 各セクションのメモエリアデータをマッピング
 */
// const mo00046AreaMap: Record<SectionKey, Ref<Mo00046Type>> = {
//   t1: mo00046TypeT1Area,
//   t2: mo00046TypeT2Area,
//   t3: mo00046TypeT3Area,
//   t4: mo00046TypeT4Area,
//   t5: mo00046TypeT5Area,
// }

/**
 * T3およびT4セクションのチェックボックス参照を取得
 */
// const memoT34Refs = computed(() => mo00018TextFieldT34Maping)

/**
 * 各セクションのテキストフィールド参照を取得
 */
// const memoRefs = computed(() => mo00038TextFieldMaping)

/**
 * 各セクションのメモエリア参照を取得
 */
// const memoAreaRefs = computed(() => mo00046AreaMap)

/**
 * セクションが有効かどうかを管理するリアクティブ変数
 */
const isEnableSection = ref<boolean>(false)

/**
 * 前回のアセスメント値を保持するリアクティブオブジェクト
 */
const focusOldValues = reactive({
  t1: '',
  t2: '',
  t3: '',
  t4: '',
  t5: '',
})

/**
 * GUI00787 ［メモ入力］画面表示フラグ
 */
const showDialogOr10412 = computed(() => {
  // Or10412のダイアログ開閉状態
  return Or10412Logic.state.get(or10412.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Or21814ダイアログの表示状態を管理する計算プロパティ
 */
const showDialogOr21814 = computed(() => {
  // Or10412のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * モードの値を保持するリアクティブ変数
 */
const modeValue = ref('1')

/**
 * レスポンス情報のサブ情報を格納するためのオブジェクト
 */
const { refValue } = useScreenTwoWayBind<Or33763TwowayType>({
  cpId: Or33763Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or33763StateType>({
  cpId: Or33763Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * モード変更時の処理
     *
     * @param value - モードの値
     */
    param: (value) => {
      if (value) {
        Object.assign(commomInfo, value)
        Object.assign(respInfo, value)
      }
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          void save()
          break
        // 新規
        case 'add':
          void add()
          break
        // 複写
        case 'copy':
          void copy()
          break
        // 削除
        case 'delete':
          void del()
          break
        // データ再取得
        case 'getData':
          void getData()
          break
        default:
          break
      }
    },
  },
})

/**
 * 子コンポーネントのユニークIDを設定する
 */
useSetupChildProps(props.uniqueCpId, {
  [Or10412Const.CP_ID(0)]: or10412.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or30981Const.CP_ID(0)]: or30981.value[0],
  [Or30981Const.CP_ID(1)]: or30981.value[1],
  [Or30981Const.CP_ID(2)]: or30981.value[2],
  [Or30981Const.CP_ID(3)]: or30981.value[3],
  [Or30981Const.CP_ID(4)]: or30981.value[4],
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * モード変更時の処理
 */
watch(
  () => modeValue.value,
  (newValue) => {
    if (newValue) {
      onChangeMode(newValue)
    }
  }
)

/**
 * Or21814のイベントを監視
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      void onSave()
    }
  }
)

/**
 * AC023_各「メモ入力支援アイコンボタン」押下_GUI00787 ［メモ入力］画面-戻り値変更
 *
 * @param respData - 戻り値
 */
// watch(
//   () => or10412Type.value,
//   (newValue) => {
//     const memoRefMap = mo00046AreaMap
//     const selectNo = newValue.selectItemNo as keyof typeof memoRefMap
//     const targetRef = memoRefMap[selectNo]

//     if (targetRef) {
//       targetRef.value.value = newValue.meMoContent
//       const className = `.${selectNo}TextArea`
//       const element = document.querySelector(className)
//       element?.querySelectorAll('textarea')?.forEach((item) => {
//         if (item.value) {
//           item.style.color = newValue.fontColor
//           item.style.fontSize = toPx(newValue.textSize)
//         }
//       })
//       const key = selectNo.toLowerCase() as SectionKey

//       respInfo.subInfo[`${key}MemoKnj` as keyof Tjyouho] = newValue.meMoContent
//       respInfo.subInfo[`${key}MemoFont` as keyof Tjyouho] = newValue.textSize ?? '12'
//       respInfo.subInfo[`${key}MemoColor` as keyof Tjyouho] = newValue.fontColor
//     }
//   }
// )

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 初期化処理
onMounted(() => {
  // 汎用コードマスタデータを取得し初期化
  // if (isInit) {
  void systemCodeSelect()
  // }
})

/**************************************************
 * 関数定義
 **************************************************/

/**
 * AC027_「本人のケアの目標入力支援アイコンボタン」押下
 *
 * @param type - 画面項目区分
 */
const careTargetInputSupportIconClick = (type: SectionKey) => {
  vieTypeKbn.value = type
  localOneway.or51775OnewayTypeOther = {
    // タイトル
    title: t('label.memo'),
    // 画面ID
    screenId: 'GUI00784',
    // 分類ID
    bunruiId: '',
    // 大分類ＣＤ
    t1Cd: '610',
    // 中分類CD
    t2Cd: '20',
    // 小分類ＣＤ
    t3Cd: '1',
    // テーブル名
    tableName: 'cpn_tuc_rai_ass_t',
    // カラム名
    columnName: '',
    // アセスメント方式
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? '',
    // 文章内容
    inputContents: '',
    // 利用者ID
    userId: systemCommonsStore.getUserId,
    // モード
    mode: '',
  } as Or51775OnewayType
  switch (type) {
    case 't1':
      localOneway.or51775OnewayTypeOther.columnName = 't1_memo_knj'
      localOneway.or51775OnewayTypeOther.inputContents = ''
      break
    case 't2':
      localOneway.or51775OnewayTypeOther.columnName = 't2_memo_knj'
      localOneway.or51775OnewayTypeOther.inputContents = ''
      break
    case 't3':
      localOneway.or51775OnewayTypeOther.columnName = 't3_memo_knj'
      localOneway.or51775OnewayTypeOther.inputContents = ''
      break
    case 't4':
      localOneway.or51775OnewayTypeOther.columnName = 't4_memo_knj'
      localOneway.or51775OnewayTypeOther.inputContents = ''
      break
    case 't5':
      localOneway.or51775OnewayTypeOther.columnName = 't5_memo_knj'
      localOneway.or51775OnewayTypeOther.inputContents = ''
      break
    default:
      break
  }
  // GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  // Or51775のダイアログを開く

  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    // 本文末に追加の場合
    if ('0' === data.type) {
      switch (vieTypeKbn.value) {
        case 't1':
          refValue.value!.t1MemoKnj.content = refValue.value!.t1MemoKnj.content + data.value
          break
        case 't2':
          refValue.value!.t2MemoKnj.content = refValue.value!.t2MemoKnj.content + data.value
          break
        case 't3':
          refValue.value!.t3MemoKnj.content = refValue.value!.t3MemoKnj.content + data.value
          break
        case 't4':
          refValue.value!.t4MemoKnj.content = refValue.value!.t4MemoKnj.content + data.value
          break
        case 't5':
          refValue.value!.t5MemoKnj.content = refValue.value!.t5MemoKnj.content + data.value
          break
        default:
          break
      }
    }
    // 本文上書の場合
    else if ('1' === data.type) {
      switch (vieTypeKbn.value) {
        case 't1':
          refValue.value!.t1MemoKnj.content = data.value
          break
        case 't2':
          refValue.value!.t2MemoKnj.content = data.value
          break
        case 't3':
          refValue.value!.t3MemoKnj.content = data.value
          break
        case 't4':
          refValue.value!.t4MemoKnj.content = data.value
          break
        case 't5':
          refValue.value!.t5MemoKnj.content = data.value
          break
        default:
          break
      }
    }
  }
}

/**
 * AC026_各「チェックボックス」押下
 *
 * @param key - セクションキー
 */
const onChangeCheckBox = (key: SectionKey) => {
  if (key === Or33763Const.DEFAULT.SECTION_KEY.T3) {
    const countT3Checked = Object.entries(refValue.value!)
      .filter(([key]) => key.startsWith(Or33763Const.DEFAULT.SECTION_KEY.T3))
      .filter(([, value]) => (value as Mo00018Type)?.modelValue === true).length

    refValue.value![key].mo00045.value = countT3Checked + ''
  }

  if (key === Or33763Const.DEFAULT.SECTION_KEY.T4) {
    const countT4Checked = Object.entries(refValue.value!)
      .filter(([key]) => key.startsWith(Or33763Const.DEFAULT.SECTION_KEY.T4))
      .filter(([, value]) => (value as Mo00018Type)?.modelValue === true).length
    console.log('onChangeCheckBox', key)
    console.log('countT4Checked', countT4Checked)
    console.log('refValue.value![key]', refValue.value![key].mo00045.value)
    refValue.value![key].mo00045.value = countT4Checked + ''
  }
}

/**
 * 汎用コード取得API実行
 */
const systemCodeSelect = async () => {
  const cmnMCdKbnId = CmnMCdKbnId as { M_CD_KBN_ID_ASSESSMENT_KIND: number }
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
  ]

  // コード取得
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // アセスメント種別
  defaultModelValue.surveyAssessmentKindList = CmnSystemCodeRepository.filter(
    cmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )
  // 親情報.調査アセスメント種別により、コードマスタの区分名称を表示する
  for (const item of defaultModelValue.surveyAssessmentKindList) {
    if (localOneway.or33763Oneway.surveyAssessmentKind === item.value) {
      localOneway.mo01338OnewayInputTip.value = item.label
      modeValue.value = item.value
      break
    }
  }
  //モードによってインターフェースを変更する
  onChangeMode(modeValue.value)
}

/**
 * モードによってインターフェースを変更する
 *
 * @param value - モードの値
 */
const onChangeMode = (value: string) => {
  // モードによってスタイルを変更
  localModelOneway.forEach((item) => {
    if (value === '1') {
      // 調査アセスメント種別 = 1: 居宅版
      item.textarea.style = 'background: rgb(var(--v-theme-black-536)) !important;'
      item.textField.mo00045Oneway!.class = 'toGray'
      item.or30981OnewayType.disabled = true
    } else {
      // 上記以外の場合
      item.textarea.style = 'background: transparent'
      item.textField.mo00045Oneway!.class = ''
      item.or30981OnewayType.disabled = false
    }
  })
}

/**
 * AC023_各「メモ入力支援アイコンボタン」押下
 *
 * @param value - メモ入力カテゴリ
 */
// const memoInputCategoryIconClick = (value: SectionKey) => {
//   const memoContentMap = mo00046AreaMap
//   const param: Or10412OnewayType = {
//     selectItemNo: value,
//     userId: systemCommonsStore.getUserId ?? '',
//     fontColor: '',
//     historyTable: '-',
//     historyTableColum: '-',
//     meMoContent: memoContentMap[value].value.value ?? '',
//     textSize: '',
//     flag: '-',
//   }
//   const lastElement = document.querySelector(`.${value}TextArea`)
//   if (lastElement) {
//     const textareas = lastElement.querySelectorAll('textarea')
//     textareas.forEach((item) => {
//       if (item.value) {
//         param.fontColor = rgbToHex(item.style.color)
//         param.textSize = item.style.fontSize
//       }
//     })
//   }
//   localOneway.or10412Oneway = { ...param }
//   Or10412Logic.state.set({
//     uniqueCpId: or10412.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }

/**
 * 色変換
 *
 * @param rgb - rbg値
 *
 * @returns 16進数の色コード
 */
// const rgbToHex = (rgb: string): string => {
//   let hexStr = ''
//   if (rgb) {
//     if (
//       rgb.length >= 10 &&
//       rgb.startsWith('rgb(') &&
//       rgb.endsWith(')') &&
//       rgb.split(',').length === 3
//     ) {
//       const tempArr = rgb.replace('rgb(', '').replace(')', '').trim().split(',')
//       if (tempArr.length === 3) {
//         hexStr = `#${((1 << 24) + (parseInt(tempArr[0]) << 16) + (parseInt(tempArr[1]) << 8) + parseInt(tempArr[2])).toString(16).slice(1)}`
//       }
//     }
//   }
//   return hexStr
// }

/**
 * 数値をピクセル単位の文字列に変換する関数
 *
 * @param value - 数値または文字列
 *
 * @returns ピクセル単位の文字列
 */
// const toPx = (value: number | string | null | undefined): string => {
//   const num = Number(value)
//   return isNaN(num) ? '12px' : `${num}px`
// }

/**
 * AC004_「新規ボタン」押下
 */
const add = () => {
  // アセスメント(インターライ)画面履歴の最新情報を取得
  void inputProject()
}

/**
 * 画面項目の初期化
 */
const inputProject = () => {
  // // セクションキーのリスト
  // const sectionKeys: SectionKey[] = Or33763Const.DEFAULT.DEFAULT_SECCTION_KEY as SectionKey[]

  // // 各セクションの値をクリア
  // sectionKeys.forEach((key) => {
  //   // メモエリアをクリア
  //   mo00046AreaMap[key].value.value = ''

  //   // テキストフィールドをクリア
  //   mo00038TextFieldMaping[key].value.mo00045.value = ''

  //   // スタイルが存在する場合、テキストエリアのスタイルをリセット
  //   const el = document.querySelector(`.${key}TextArea`)
  //   el?.querySelectorAll('textarea')?.forEach((ta) => {
  //     if (ta.value) {
  //       ta.style.color = Or33763Const.DEFAULT.DEFAULT_COLOR
  //       ta.style.fontSize = Or33763Const.DEFAULT.DEFAULT_SIZE
  //     }
  //   })
  // })
  refValue.value!.t1MemoKnj.content = ''
  refValue.value!.t1MemoKnj.fontSize = '14'
  refValue.value!.t1MemoKnj.fontColor = ''

  refValue.value!.t2MemoKnj.content = ''
  refValue.value!.t2MemoKnj.fontSize = '14'
  refValue.value!.t2MemoKnj.fontColor = ''

  refValue.value!.t3MemoKnj.content = ''
  refValue.value!.t3MemoKnj.fontSize = '14'
  refValue.value!.t3MemoKnj.fontColor = ''

  refValue.value!.t4MemoKnj.content = ''
  refValue.value!.t4MemoKnj.fontSize = '14'
  refValue.value!.t4MemoKnj.fontColor = ''

  refValue.value!.t5MemoKnj.content = ''
  refValue.value!.t5MemoKnj.fontSize = '14'
  refValue.value!.t5MemoKnj.fontColor = ''

  refValue.value![Or33763Const.DEFAULT.SECTION_KEY.T1 as SectionKey].mo00045.value = ''
  refValue.value![Or33763Const.DEFAULT.SECTION_KEY.T2 as SectionKey].mo00045.value = ''
  refValue.value![Or33763Const.DEFAULT.SECTION_KEY.T3 as SectionKey].mo00045.value = ''
  refValue.value![Or33763Const.DEFAULT.SECTION_KEY.T4 as SectionKey].mo00045.value = ''
  refValue.value![Or33763Const.DEFAULT.SECTION_KEY.T5 as SectionKey].mo00045.value = ''

  // T3のサブ項目（G2a〜G2j）をクリア
  Object.keys(refValue.value!)
    .filter((key) => key.startsWith('t3G2'))
    .forEach((key) => {
      refValue.value![key as SectionKeyT3].modelValue = false
    })

  // T4のサブ項目（G1a〜G1h）をクリア
  Object.keys(refValue.value!)
    .filter((key) => key.startsWith('t4G1'))
    .forEach((key) => {
      refValue.value![key as SectionKeyT4].modelValue = false
    })
}

// watch(
//   () => refValue.value!.t2.mo00045.value,
//   (newValue, oldValue) => {
//     focusOldValues.t2 = oldValue
//   }
// )

/**
 * AC024_各「アセスメント項目ボタン」押下
 *
 * @param key - セクションキー
 *
 * @param e - 選択された値
 */
const improvementBtnClick = async (key: SectionKey, e: Event) => {
  const value = (e.target as HTMLInputElement).value
  const ref = refValue.value![key]
  if (ref) {
    if (key === Or33763Const.DEFAULT.SECTION_KEY.T2) {
      if (value === Or33763Const.DEFAULT.DEFAULT_VALUE_T2.Key_2) {
        isEnableSection.value = false
      }

      if (
        value === Or33763Const.DEFAULT.DEFAULT_VALUE_T2.Key_0 ||
        value === Or33763Const.DEFAULT.DEFAULT_VALUE_T2.Key_1
      ) {
        const dialogResult = await openInfoDialog()
        switch (dialogResult) {
          case Or33763Const.DEFAULT.DIALOG_RESULT.YES:
            isEnableSection.value = true
            refValue.value![key].mo00045.value = value
            focusOldValues[key] = value
            clearDataDisableSection()
            break
          case Or33763Const.DEFAULT.DIALOG_RESULT.CANCEL:
            refValue.value![key].mo00045.value = '2'
            Or21814Logic.state.set({
              uniqueCpId: or21814.value.uniqueCpId,
              state: {
                isOpen: false,
              },
            })
            break
        }
      }
    }
  }
}

/**
 * 確認ダイアログを開く関数
 *
 * @returns ユーザーが選択した結果（yes または cancel）
 */
const openInfoDialog = async (): Promise<string> => {
  // データ変更確認ダイアログを初期化(i.cmn.10181)
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10181'),

      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),

      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
      secondBtnType: 'blank',
    },
  })

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = Or33763Const.DEFAULT.DIALOG_RESULT.CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or33763Const.DEFAULT.DIALOG_RESULT.YES
        }

        if (event?.thirdBtnClickFlg) {
          result = Or33763Const.DEFAULT.DIALOG_RESULT.CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * セクションが有効かどうかをチェックする関数
 *
 * @param key - セクションキー
 *
 * @returns セクションが有効であれば true、無効であれば false
 */
const checkEnableSection = (key: string) => {
  if (isEnableSection.value) {
    if (
      key === Or33763Const.DEFAULT.SECTION_KEY.T3 ||
      key === Or33763Const.DEFAULT.SECTION_KEY.T4 ||
      key === Or33763Const.DEFAULT.SECTION_KEY.T5
    ) {
      return true
    }
  }
  return false
}

/**
 * T3, T4, T5およびそのサブ項目のデータをクリアする関数
 */
const clearDataDisableSection = () => {
  // T3, T4, T5のメモエリアをクリア
  refValue.value!.t3MemoKnj.content = ''
  refValue.value!.t3MemoKnj.fontSize = '14'
  refValue.value!.t3MemoKnj.fontColor = ''

  refValue.value!.t4MemoKnj.content = ''
  refValue.value!.t4MemoKnj.fontSize = '14'
  refValue.value!.t4MemoKnj.fontColor = ''

  refValue.value!.t5MemoKnj.content = ''
  refValue.value!.t5MemoKnj.fontSize = '14'
  refValue.value!.t5MemoKnj.fontColor = ''

  // T3, T4, T5のテキストフィールドをクリア
  refValue.value![Or33763Const.DEFAULT.SECTION_KEY.T3 as SectionKey].mo00045.value = ''
  refValue.value![Or33763Const.DEFAULT.SECTION_KEY.T4 as SectionKey].mo00045.value = ''
  refValue.value![Or33763Const.DEFAULT.SECTION_KEY.T5 as SectionKey].mo00045.value = ''

  // T3のサブ項目（G2a〜G2j）をクリア
  Object.keys(refValue.value!)
    .filter((key) => key.startsWith('t3G2'))
    .forEach((key) => {
      refValue.value![key as SectionKeyT3].modelValue = false
    })

  // T4のサブ項目（G1a〜G1h）をクリア
  Object.keys(refValue.value!)
    .filter((key) => key.startsWith('t4G1'))
    .forEach((key) => {
      refValue.value![key as SectionKeyT4].modelValue = false
    })
}

/**
 * 画面初期情報取得
 *
 * @returns 画面初期情報
 */
const getHistoryInfo = async () => {
  const inputData: AssessmentInterRAIAHistorySelectEntity = {
    raiId: commomInfo.historyInfo.raiId,
  }
  const resData: AssessmentInterRAIAHistorySelectOutEntity = await ScreenRepository.select(
    Or33763Const.STATUS_CODE.NAME_ACTION_SELECT_H,
    inputData
  )
  if (resData.statusCode === Or33763Const.STATUS_CODE.SUCCESS) {
    return resData.data
  }
}

/**
 * v-modelでバインドされたメモ情報と文字種別情報を
 * respInfo.subInfo にマッピングする関数です。
 *
 * @description
 * 各レベル（t1〜t5）に対応する textarea の内容、文字種別、フォントサイズ、文字色を
 * DOM から取得して、それぞれのフィールドに設定します。
 */
const mapFieldToSubInfo = () => {
  // const mapping: {
  //   key: keyof typeof respInfo.subInfo
  //   memoRef: Ref<{ value?: string }>
  //   textRef: Ref<{ mo00045: { value: string } }>
  //   fontKey: keyof typeof respInfo.subInfo
  //   colorKey: keyof typeof respInfo.subInfo
  //   className: string
  //   situationKey: keyof typeof respInfo.subInfo
  // }[] = [
  //   {
  //     key: 't1MemoKnj',
  //     memoRef: mo00046TypeT1Area,
  //     textRef: mo00038TypeT1TextField,
  //     className: 't1TextArea',
  //     fontKey: 't1MemoFont',
  //     colorKey: 't1MemoColor',
  //     situationKey: 't1',
  //   },
  //   {
  //     key: 't2MemoKnj',
  //     memoRef: mo00046TypeT2Area,
  //     textRef: mo00038TypeT2TextField,
  //     className: 't2TextArea',
  //     fontKey: 't2MemoFont',
  //     colorKey: 't2MemoColor',
  //     situationKey: 't2',
  //   },
  //   {
  //     key: 't3MemoKnj',
  //     memoRef: mo00046TypeT3Area,
  //     textRef: mo00038TypeT3TextField,
  //     className: 't3TextArea',
  //     fontKey: 't3MemoFont',
  //     colorKey: 't3MemoColor',
  //     situationKey: 't3',
  //   },
  //   {
  //     key: 't4MemoKnj',
  //     memoRef: mo00046TypeT4Area,
  //     textRef: mo00038TypeT4TextField,
  //     className: 't4TextArea',
  //     fontKey: 't4MemoFont',
  //     colorKey: 't4MemoColor',
  //     situationKey: 't4',
  //   },
  //   {
  //     key: 't5MemoKnj',
  //     memoRef: mo00046TypeT5Area,
  //     textRef: mo00038TypeT5TextField,
  //     className: 't5TextArea',
  //     fontKey: 't5MemoFont',
  //     colorKey: 't5MemoColor',
  //     situationKey: 't5',
  //   },
  // ]

  // mapping.forEach(({ key, memoRef, textRef, fontKey, colorKey, className, situationKey }) => {
  //   respInfo.subInfo[key] = memoRef.value.value ?? ''

  //   respInfo.subInfo[situationKey] = textRef.value.mo00045.value ?? ''

  //   const el = document.querySelector(`.${className}`)
  //   const textarea = el?.querySelector('textarea') as HTMLTextAreaElement | null
  //   if (textarea) {
  //     respInfo.subInfo[fontKey] = textarea.style.fontSize || ''
  //     respInfo.subInfo[colorKey] = textarea.style.color || ''
  //   }
  // })

  // // mo00018のデータをマッピング
  // Object.entries(mo00018TextFieldT34Maping).forEach(([key, ref]) => {
  //   const dataKey = `${key}Flg` as keyof typeof respInfo.subInfo
  //   respInfo.subInfo[dataKey] = ref.value.modelValue ? '1' : '0' // チェックされている場合は '1'、そうでない場合は '0'
  // })

  respInfo.subInfo.t1 = refValue.value!.t1.mo00045.value
  respInfo.subInfo.t2 = refValue.value!.t2.mo00045.value
  respInfo.subInfo.t3 = refValue.value!.t3.mo00045.value
  respInfo.subInfo.t4 = refValue.value!.t4.mo00045.value
  respInfo.subInfo.t5 = refValue.value!.t5.mo00045.value

  respInfo.subInfo.t1MemoKnj = refValue.value!.t1MemoKnj.content
  respInfo.subInfo.t1MemoFont = refValue.value!.t1MemoKnj.fontSize
  respInfo.subInfo.t1MemoColor = refValue.value!.t1MemoKnj.fontColor

  respInfo.subInfo.t2MemoKnj = refValue.value!.t2MemoKnj.content
  respInfo.subInfo.t2MemoFont = refValue.value!.t2MemoKnj.fontSize
  respInfo.subInfo.t2MemoColor = refValue.value!.t2MemoKnj.fontColor

  respInfo.subInfo.t3MemoKnj = refValue.value!.t3MemoKnj.content
  respInfo.subInfo.t3MemoFont = refValue.value!.t3MemoKnj.fontSize
  respInfo.subInfo.t3MemoColor = refValue.value!.t3MemoKnj.fontColor

  respInfo.subInfo.t4MemoKnj = refValue.value!.t4MemoKnj.content
  respInfo.subInfo.t4MemoFont = refValue.value!.t4MemoKnj.fontSize
  respInfo.subInfo.t4MemoColor = refValue.value!.t4MemoKnj.fontColor

  respInfo.subInfo.t5MemoKnj = refValue.value!.t5MemoKnj.content
  respInfo.subInfo.t5MemoFont = refValue.value!.t5MemoKnj.fontSize
  respInfo.subInfo.t5MemoColor = refValue.value!.t5MemoKnj.fontColor

  // T3のサブ項目（G2a〜G2j）をマッピング
  Object.keys(refValue.value!)
    .filter((key) => key.startsWith('t3G2'))
    .forEach((key) => {
      const subKey = key as SectionKeyT3
      const dataKey = `${subKey}Flg` as keyof typeof respInfo.subInfo
      respInfo.subInfo[dataKey] = refValue.value![subKey].modelValue ? '1' : '0'
    })

  // T4のサブ項目（G1a〜G1h）をマッピング
  Object.keys(refValue.value!)
    .filter((key) => key.startsWith('t4G1'))
    .forEach((key) => {
      const subKey = key as SectionKeyT4
      const dataKey = `${subKey}Flg` as keyof typeof respInfo.subInfo
      respInfo.subInfo[dataKey] = refValue.value![subKey].modelValue ? '1' : '0'
    })
}

/**
 * 「保存ボタン」押下き確認ダイアログ
 *
 * @param message -情報を提示します
 */
const showOr21814Msg = (message: string): void => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: message,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.cancel'),
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}

/**
 * AC003_「保存ボタン」押下
 */
const save = async () => {
  // 取得したインターライ方式履歴情報<>NULL
  const historyInfo = await getHistoryInfo()
  if (historyInfo) {
    // 選定アセスメント種別 > 0
    if (Number(historyInfo?.capType) > 0) {
      // 検討アセスメント種別 > 0
      if (Number(historyInfo?.capType) > 0) {
        deleteKbn.value = Or33763Const.DEFAULT.DEFAULT_DELETE_KBN.kbn_1
        showOr21814Msg(t('message.i-cmn-11267'))
      }
      // 検討アセスメント種別 <= 0
      if (Number(historyInfo?.capType) <= 0) {
        deleteKbn.value = Or33763Const.DEFAULT.DEFAULT_DELETE_KBN.kbn_0
        showOr21814Msg(t('message.i-cmn-11266'))
      }
    } else {
      deleteKbn.value = ''
      void onSave()
    }
  }
}

/**
 * 保存ボタン押下
 */
const onSave = async () => {
  // 入力値処理です
  const inputData: AssessmentInterRAITInsertInEntity = {
    houjinId: systemCommonsStore?.getHoujinId ?? '',
    shisetuId: systemCommonsStore?.getShisetuId ?? '',
    userId: commomInfo.userId || '',
    svJigyoId: commomInfo.userId,
    subKbn: Or33763Const.DEFAULT.DEFAULT_SUB_KBN,
    updateKbn: '',
    historyUpdateKbn: '',
    deleteKbn: Or33763Const.DEFAULT.DEFAULT_DELETE_KBN.kbn_0,
    sc1Id: commomInfo.historyInfo.sc1Id || '',
    kijunbiYMD: parseInt(commomInfo.kijunbiYmd) || 0,
    syubetsuId: '',
    Tjyouho: {} as Tjyouho,
  }

  mapFieldToSubInfo()
  inputData.Tjyouho = respInfo.subInfo

  const resData: AssessmentInterRAITUpdateOutEntity = await ScreenRepository.update(
    Or33763Const.STATUS_CODE.NAME_ACTION_UPDATE,
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === Or33763Const.STATUS_CODE.SUCCESS) {
    // 画面情報再取得
  }
}

/**
 * AC005_「複写ボタン」押下
 */
const copy = async () => {
  // 入力値処理です
  const inputData: AssessmentInterRAITInsertInEntity = {
    houjinId: systemCommonsStore?.getHoujinId ?? '',
    shisetuId: systemCommonsStore?.getShisetuId ?? '',
    userId: commomInfo.userId || '',
    svJigyoId: commomInfo.userId,
    subKbn: Or33763Const.DEFAULT.DEFAULT_SUB_KBN,
    updateKbn: '',
    historyUpdateKbn: '',
    deleteKbn: Or33763Const.DEFAULT.DEFAULT_DELETE_KBN.kbn_0,
    sc1Id: commomInfo.historyInfo.sc1Id || '',
    kijunbiYMD: parseInt(commomInfo.kijunbiYmd) || 0,
    syubetsuId: '',
    Tjyouho: {} as Tjyouho,
  }

  mapFieldToSubInfo()
  inputData.Tjyouho = respInfo.subInfo
  const resData: AssessmentInterRAITUpdateOutEntity = await ScreenRepository.insert(
    Or33763Const.STATUS_CODE.NAME_ACTION_ADD,
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === Or33763Const.STATUS_CODE.SUCCESS) {
    // 画面情報再取得
  }
  // TODO GUI00807 アセスメント複写画面未作成
}

/**
 * AC011_「削除」押下
 */
const del = async () => {
  // 入力値処理です
  const inputData: AssessmentInterRAITInsertInEntity = {
    houjinId: systemCommonsStore?.getHoujinId ?? '',
    shisetuId: systemCommonsStore?.getShisetuId ?? '',
    userId: commomInfo.userId || '',
    svJigyoId: commomInfo.userId,
    subKbn: Or33763Const.DEFAULT.DEFAULT_SUB_KBN,
    updateKbn: '',
    historyUpdateKbn: '',
    deleteKbn: Or33763Const.DEFAULT.DEFAULT_DELETE_KBN.kbn_1,
    sc1Id: commomInfo.historyInfo.sc1Id || '',
    kijunbiYMD: parseInt(commomInfo.kijunbiYmd) || 0,
    syubetsuId: '',
    Tjyouho: {} as Tjyouho,
  }

  mapFieldToSubInfo()
  inputData.Tjyouho = respInfo.subInfo
  const resData: AssessmentInterRAITUpdateOutEntity = await ScreenRepository.delete_phys(
    Or33763Const.STATUS_CODE.NAME_ACTION_DELETE,
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === Or33763Const.STATUS_CODE.SUCCESS) {
    // 画面情報再取得
  }
  // TODO 画面「T」タブに対し、更新区分を「D:削除」にする。
}

/**
 * AC003-2-1_アセスメント(インターライ)画面履歴の最新情報を取得
 */
const getData = async () => {
  console.log('getData', commomInfo.historyInfo.raiId)
  const inputData: AssessmentInterRAITInEntity = {
    raiId: commomInfo.historyInfo.raiId ?? '',
  }

  const resData: AssessmentInterRAITOutEntity = await ScreenRepository.select(
    Or33763Const.STATUS_CODE.NAME_ACTION_SELECT,
    inputData
  )

  if (
    (resData.statusCode === Or33763Const.STATUS_CODE.SUCCESS ||
      resData.statusCode === Or33763Const.STATUS_CODE.SUCCESS_CODE) &&
    resData.data
  ) {
    const data = resData.data
    const subInfoT = data.subInfoT[0]
    if (subInfoT) {
      setCpTowayData(subInfoT)

      if (
        refValue.value!.t2.mo00045.value === Or33763Const.DEFAULT.DEFAULT_VALUE_T2.Key_0 ||
        refValue.value!.t2.mo00045.value === Or33763Const.DEFAULT.DEFAULT_VALUE_T2.Key_1
      ) {
        isEnableSection.value = true
      }
      // フォーカスアウト時の旧値を設定
      focusOldValues.t1 = subInfoT.t1 ?? ''
      focusOldValues.t2 = subInfoT.t2 ?? ''
      focusOldValues.t3 = subInfoT.t3 ?? ''
      focusOldValues.t4 = subInfoT.t4 ?? ''
      focusOldValues.t5 = subInfoT.t5 ?? ''

      onChangeCheckBox(Or33763Const.DEFAULT.SECTION_KEY.T3 as SectionKey)
      onChangeCheckBox(Or33763Const.DEFAULT.SECTION_KEY.T4 as SectionKey)
    }
  }
  // const mapping = [
  //   {
  //     key: 't1MemoKnj',
  //     memoRef: mo00046TypeT1Area,
  //     textRef: mo00038TypeT1TextField,
  //     className: 't1TextArea',
  //     fontKey: 't1MemoFont',
  //     colorKey: 't1MemoColor',
  //   },
  //   {
  //     key: 't2MemoKnj',
  //     memoRef: mo00046TypeT2Area,
  //     textRef: mo00038TypeT2TextField,
  //     className: 't2TextArea',
  //     fontKey: 't2MemoFont',
  //     colorKey: 't2MemoColor',
  //   },
  //   {
  //     key: 't3MemoKnj',
  //     memoRef: mo00046TypeT3Area,
  //     textRef: mo00038TypeT3TextField,
  //     className: 't3TextArea',
  //     fontKey: 't3MemoFont',
  //     colorKey: 't3MemoColor',
  //   },
  //   {
  //     key: 't4MemoKnj',
  //     memoRef: mo00046TypeT4Area,
  //     textRef: mo00038TypeT4TextField,
  //     className: 't4TextArea',
  //     fontKey: 't4MemoFont',
  //     colorKey: 't4MemoColor',
  //   },
  //   {
  //     key: 't5MemoKnj',
  //     memoRef: mo00046TypeT5Area,
  //     textRef: mo00038TypeT5TextField,
  //     className: 't5TextArea',
  //     fontKey: 't5MemoFont',
  //     colorKey: 't5MemoColor',
  //   },
  // ]

  // mapping.forEach(({ key, memoRef, textRef, className, fontKey, colorKey }, index) => {
  //   memoRef.value.value = subInfoT[key as keyof SubInfoT] ?? ''

  // void nextTick(() => {
  //   const el = document.querySelector(`.${className}`)
  //   el?.querySelectorAll('textarea')?.forEach((item) => {
  //     if (item.value) {
  //       item.style.fontSize = subInfoT[fontKey as keyof SubInfoT]
  //         ? `${subInfoT[fontKey as keyof SubInfoT]}px`
  //         : ''
  //       item.style.color = subInfoT[colorKey as keyof SubInfoT]
  //         ? convertDecimalToHex(Number(subInfoT[colorKey as keyof SubInfoT]))
  //         : ''
  //     }
  //   })
  // })

  // const situationKey = `t${index + 1}` as keyof SubInfoT
  // textRef.value.mo00045.value = subInfoT[situationKey] ?? ''

  // if (situationKey === Or33763Const.DEFAULT.SECTION_KEY.T2) {
  //   if (
  //     textRef.value.mo00045.value === Or33763Const.DEFAULT.DEFAULT_VALUE_T2.Key_0 ||
  //     textRef.value.mo00045.value === Or33763Const.DEFAULT.DEFAULT_VALUE_T2.Key_1
  //   ) {
  //     isEnableSection.value = true
  //   }
  // }

  // focusOldValues[situationKey as keyof typeof focusOldValues] = subInfoT[situationKey] ?? ''
  // })

  // mo00018のデータをマッピング
  // Object.entries(mo00018TextFieldT34Maping).forEach(([key, ref]) => {
  //   const dataKey = `${key}Flg` as keyof SubInfoT
  //   ref.value.modelValue = subInfoT[dataKey] === '1'
  // })
}

/**
 * 初期化処理
 *
 * @param data - 画面初期情報
 */
const setCpTowayData = (data: SubInfoT) => {
  const items = data
  screenStore.setCpTwoWay({
    cpId: Or33763Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: {
      t1: {
        mo00045: {
          value: items?.t1 ?? '',
        },
      },
      t2: {
        mo00045: {
          value: items?.t2 ?? '',
        },
      },
      t3: {
        mo00045: {
          value: items?.t3 ?? '',
        },
      },
      t3G2aFlg: {
        modelValue: items?.t3G2aFlg === '1',
      },
      t3G2bFlg: {
        modelValue: items?.t3G2bFlg === '1',
      },
      t3G2cFlg: {
        modelValue: items?.t3G2cFlg === '1',
      },
      t3G2dFlg: {
        modelValue: items?.t3G2dFlg === '1',
      },
      t3G2eFlg: {
        modelValue: items?.t3G2eFlg === '1',
      },
      t3G2fFlg: {
        modelValue: items?.t3G2fFlg === '1',
      },
      t3G2gFlg: {
        modelValue: items?.t3G2gFlg === '1',
      },
      t3G2hFlg: {
        modelValue: items?.t3G2hFlg === '1',
      },
      t3G2iFlg: {
        modelValue: items?.t3G2iFlg === '1',
      },
      t3G2jFlg: {
        modelValue: items?.t3G2jFlg === '1',
      },
      t4: {
        mo00045: {
          value: items?.t4 ?? '',
        },
      },
      t4G1aFlg: {
        modelValue: items?.t4G1aFlg === '1',
      },
      t4G1bFlg: {
        modelValue: items?.t4G1bFlg === '1',
      },
      t4G1cFlg: {
        modelValue: items?.t4G1cFlg === '1',
      },
      t4G1dFlg: {
        modelValue: items?.t4G1dFlg === '1',
      },
      t4G1eFlg: {
        modelValue: items?.t4G1eFlg === '1',
      },
      t4G1fFlg: {
        modelValue: items?.t4G1fFlg === '1',
      },
      t4G1gFlg: {
        modelValue: items?.t4G1gFlg === '1',
      },
      t4G1hFlg: {
        modelValue: items?.t4G1hFlg === '1',
      },
      t5: {
        mo00045: {
          value: items?.t5 ?? '',
        },
      },
      /**
       * t1_メモ
       */
      t1MemoKnj: {
        content: items?.t1MemoKnj,
        fontSize: items?.t1MemoFont,
        fontColor: items?.t1MemoColor,
      },
      /**
       * t2_メモ
       */
      t2MemoKnj: {
        content: items?.t2MemoKnj,
        fontSize: items?.t2MemoFont,
        fontColor: items?.t2MemoColor,
      },
      /**
       * t3_メモ
       */
      t3MemoKnj: {
        content: items?.t3MemoKnj,
        fontSize: items?.t3MemoFont,
        fontColor: items?.t3MemoColor,
      },
      /**
       * t4_メモ
       */
      t4MemoKnj: {
        content: items?.t4MemoKnj,
        fontSize: items?.t4MemoFont,
        fontColor: items?.t4MemoColor,
      },
      /**
       * t5_メモ
       */
      t5MemoKnj: {
        content: items?.t5MemoKnj,
        fontSize: items?.t5MemoFont,
        fontColor: items?.t5MemoColor,
      },
    },
    isInit: true,
  })

  console.log('setCpTwoWay', screenStore.getCpTwoWayInitialValue(props.uniqueCpId))
}
</script>

<template>
  <c-v-row
    no-gutters
    class="main-container pt-4"
  >
    <div class="main-container_left">
      <div class="type-care">{{ localOneway.mo01338OnewayInputTip.value }}</div>
      <div class="skin-condition-section-title">
        {{ t('label.interRAI-method-care-assessment-situation-title') }}
      </div>
      <div class="container">
        <!-- コンテンツ -->
        <c-v-row
          no-gutters
          style="border-bottom: thin solid rgb(var(--v-theme-form))"
        >
          <c-v-col cols="12">
            <!-- 各セクションの内容 -->
            <c-v-row
              no-gutters
              class="content"
            >
              <!-- セクションをループして描画 -->
              <c-v-row
                v-for="(section, index) in localModelOneway"
                :key="section.key"
                no-gutters
                class="asection"
                :class="{ 'readonly-secsion__enable': checkEnableSection(section.key) }"
              >
                <c-v-col
                  v-if="section.isNote"
                  class="pt-6 pb-1"
                >
                  <base-mo01338
                    :oneway-model-value="section.title"
                    style="background: transparent; white-space: pre-line"
                  />
                </c-v-col>
                <!-- 左側のセクション -->
                <div
                  v-else
                  class="w-100"
                >
                  <div class="asection_left_title_left">{{ section.title.value }}</div>
                  <div
                    :class="`asection_right_${index}`"
                    class="asection_right"
                  >
                    <!-- サブタイトル -->
                    <c-v-row
                      v-if="section.subtitle"
                      no-gutters
                      style="margin-bottom: 8px"
                      class="ml-3 my-2 sub-title"
                    >
                      {{ section.subtitle.value }}
                    </c-v-row>
                    <!-- ボタンまたはチェックボックス -->
                    <div v-if="!section.isCheckBox">
                      <base-mo00039
                        v-model="refValue![section.key].mo00045.value"
                        :oneway-model-value="{
                          inline: section?.inline,
                          ...localOneway.mo00039Oneway,
                        }"
                        :class="['custom-radio']"
                        :style="`width: ${section?.width ?? ''}`"
                      >
                        <base-at-radio
                          v-for="radio in section.buttons.btnItems"
                          :key="radio.value"
                          :radio-label="radio.label"
                          :value="radio.value"
                          :class="{
                            isChecked: refValue![section.key].mo00045.value === radio.value,
                          }"
                          style="margin-right: 0px !important"
                          :style="`width: ${section?.widthItems ?? ''}`"
                          @change="improvementBtnClick(section.key, $event)"
                        />
                      </base-mo00039>
                    </div>
                    <!-- チェックボックス -->
                    <div v-if="section.isCheckBox">
                      <c-v-row
                        v-for="item in section.checkBox.checkBoxItems"
                        :key="item.key"
                        no-gutters
                        style="margin-bottom: 10px"
                      >
                        <c-v-col cols="auto">
                          <base-mo00018
                            v-model="refValue![item.key as SectionKeyT4 | SectionKeyT3]"
                            :oneway-model-value="{
                              ...section.checkBox.mo00018Oneway,
                              checkboxLabel: item.label,
                            }"
                            @change="onChangeCheckBox(section.key)"
                          />
                        </c-v-col>
                      </c-v-row>
                      <c-v-row
                        no-gutters
                        class="asection_right_second mt-2"
                      >
                        <!-- 読み取り専用のテキスト -->
                        <div class="sub-title">
                          {{
                            index === 3
                              ? t('label.T-situation-section-T3-sub-title-1')
                              : t('label.T-situation-section-T3-sub-title-2')
                          }}
                        </div>
                        <div
                          class="custom-readonly__text"
                          :class="section.textField.mo00045Oneway?.class"
                        >
                          {{ refValue![section.key].mo00045.value }}
                        </div>
                      </c-v-row>
                    </div>
                    <div class="mt-3 input-container">
                      <g-custom-or-30981
                        v-bind="or30981[index]"
                        v-model="refValue![(section.key + 'MemoKnj') as keyof typeof refValue]"
                        :oneway-model-value="section.or30981OnewayType"
                        @on-click-edit-btn="careTargetInputSupportIconClick(section.key)"
                      />
                    </div>
                  </div>
                </div>
              </c-v-row>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <!-- interRAIロゴ -->
        <c-v-row
          no-gutters
          class="pt-4"
        >
          <c-v-col cols="12">
            <c-v-img
              width="129"
              aspect-ratio="16/9"
              fill
              :src="InterRAI"
              style="float: right"
              class="custom-img"
            ></c-v-img>
          </c-v-col>
        </c-v-row>
      </div>
    </div>
  </c-v-row>
  <!-- GUI00787 ［メモ入力］画面 -->
  <g-custom-or-10412
    v-if="showDialogOr10412"
    v-bind="or10412"
    v-model="or10412Type"
    :oneway-model-value="localOneway.or10412Oneway"
  ></g-custom-or-10412>
  <!-- 確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="localOneway.or51775OnewayTypeOther"
    @confirm="or51775Confirm"
  ></g-custom-or-51775>
</template>

<style scoped lang="scss">
:deep(.custom-img img) {
  object-fit: fill;
}
.right-input :deep(input) {
  text-align: right !important;
}
.skin-condition-section-title {
  display: flex;
  align-items: center;
  width: 1080px;
  background-color: #fff;
  padding-left: 24px;
  height: 73px;
  color: #333333;
  font-size: 18px;
  font-weight: 700;
}
.asection_left_title_left {
  display: flex;
  align-items: center;
  height: 48px;
  font-size: 17px;
  color: #333333;
  font-weight: 700;
  padding-left: 24px;
  background-color: #e6e6e6;
}

:deep(.custom-radio) {
  padding-top: 2px;
  &.inline {
    .v-radio {
      min-width: 180px;
    }
  }
  .v-selection-control-group {
    gap: 16px;
  }
  .v-radio {
    padding: 0 8px;
    border: 1px solid #8a8a8a;
    border-radius: 4px;
    .v-label {
      width: 100%;
    }
    &.isChecked {
      background-color: #ecf3fd;
      border: 1px solid #0760e6;
    }
  }
}
.input-container {
  position: relative;
  .button-square {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 9;
  }
  textarea {
    height: 127px !important;
  }
}
:deep(.custom-button-options .gridContent .v-btn > span span) {
  padding-left: 8px;
}

:deep(.custom-button-options .verticalContent .v-btn > span span) {
  padding-left: 8px;
}

:deep(.custom-button-options .v-col-12 .v-btn > span span) {
  padding-left: 8px;
}

:deep(.custom-label-checkbox .v-input__control .v-selection-control > .v-label) {
  white-space: pre-line;
}

.readonly-secsion__enable {
  pointer-events: none;
  background-color: #ece9e1;
  opacity: 0.5;
}

.custom-readonly__text {
  width: 72px;
  height: 36px;
  background-color: #edf1f7;
  border: 1px solid #ccc;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-container {
  padding-top: 8px;
}

.main-container_left {
  :has(> .contentItem) {
    text-align: right;
    :deep(.item-label) {
      font-size: 18px !important;
    }
  }
}
.container {
  width: 1080px;
  :has(.contentItem) {
    :deep(.item-label) {
      font-weight: bolder;
    }
  }
  .content {
    .asection {
      border-top: thin solid rgb(var(--v-theme-form));
      width: 100%;
      .asection_right {
        background: #ffffff;
        padding: 24px 24px 24px 48px;
        .asection_right_second {
          align-items: center;
          gap: 46px;
          margin-top: 8px;
          padding-right: 16px;
          :deep(.v-field__input) {
            min-height: 8px !important;
            height: 25px;
          }
          :has(> .toGray) {
            :first-child {
              background-color: #edf1f7 !important;
            }
          }
        }
      }

      .asection_right_5 {
        padding: 24px 24px 60px 48px !important;
      }
    }
  }
}
.sub-title {
  color: #333333;
  font-size: 14px;
  font-weight: 700;
}
.type-care {
  width: 1080px;
  display: flex;
  justify-content: end;
  font-size: 24px;
  padding: 4px 0;
}
</style>
