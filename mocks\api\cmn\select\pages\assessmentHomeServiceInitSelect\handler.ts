/**
 * ［アセスメント］画面（居宅）（3）モーダル
 *
 * @description
 * ［アセスメント］画面（居宅）（3）モーダル
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import defaultData2 from './data/default2.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { AssessmentHomeServiceInitSelectInEntity } from '~/repositories/cmn/entities/AssessmentHomeServiceInitEntity'
/**
 * ［アセスメント］画面（居宅）（3）
 *
 * @description
 * ［アセスメント］画面（居宅）（3）初期情報データを返却する。
 * dataName："assessmentHomeTab65Select"
 */
export function handler(inEntity: AssessmentHomeServiceInitSelectInEntity) {
  let result
  // 4:H21/4改訂;5:R3/4改訂
  if (inEntity.ninteiFormF === '4') {
    result = {
      cpnTucGdl4SerInfo:
        defaultData.cpnTucGdl4SerInfo.find(
          (item) => item.gdlId === inEntity.gdlId && inEntity.sc1Id === item.sc1Id
        ) ?? {},
      shisetsuDataList: defaultData.shisetsuDataList,
    }
  } else {
    result = {
      cpnTucGdl5SerInfo:
        defaultData2.cpnTucGdl5SerInfo.find(
          (item) => item.gdlId === inEntity.gdlId && inEntity.sc1Id === item.sc1Id
        ) ?? {},
      shisetsuDataList: defaultData.shisetsuDataList,
    }
  }

  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...result,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
