import { useRoute } from 'vue-router'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { AuthzRepository } from '~/repositories/business/core/authz/AuthzRepository'
import type {
  ICheckAuthzKinouInEntity,
  ICheckAuthzKinouOutEntity,
} from '~/repositories/business/core/authz/entities/CheckAuthzKinouEntity'
import { useCmnRouteCom } from '#imports'
/**
 * 閲覧権限有無判定(利用権限有無)
 * 閲覧権限ない = 全ての利用権限ない
 *
 * @param path - URLパス (現在表示画面のパスなら、指定不要)
 * 例：
 *  計画書（１）の場合は、/care-manager/care-plan1
 *  現在表示画面のパスなら、指定不要
 */
export const hasViewAuth = async (path?: string): Promise<boolean> => {
  const route = useRoute()
  // route共有情報
  const cmnRouteCom = useCmnRouteCom()
  let currentPath = path
  if (route) {
    // routeがundefinedでない場合
    currentPath = path ?? route.path
  }
  // currentPathを取得でなければ、getAuthzPath共通関数を利用して権限チェックを行う
  if (!currentPath || currentPath === '') {
    currentPath = cmnRouteCom.getAuthzPath() ?? ''
  }
  // システム共有情報ストア
  const systemCommonsStore = useSystemCommonsStore()
  // 権限設定検索
  const authzKinou = systemCommonsStore.searchAuthzKinou(systemCommonsStore.getSvJigyoId ?? '')
  if (
    authzKinou.authFlg !== '1' &&
    authzKinou.authFlg !== '2' &&
    authzKinou.authFlg !== '3' &&
    authzKinou.authFlg !== '9'
  ) {
    return false
  }
  // 画面固有の権限チェック処理
  const input: ICheckAuthzKinouInEntity = {
    keys: [{ path: currentPath }],
  }
  const res: ICheckAuthzKinouOutEntity = await AuthzRepository.checkAuthzKinou(input)
  // 機能別権限設定検索
  const authzKinouFlg = res.data?.authzKinou
    .filter((authz) => authz.path === path)
    .every(
      (authz) =>
        (authz.authFlg === '1' ||
          authz.authFlg === '2' ||
          authz.authFlg === '3' ||
          authz.authFlg === '9') &&
        authz.use1
    )

  // 機能別権限設定有無判定
  if (!authzKinouFlg) {
    return false
  }
  return true
}

/**
 * 印刷権限有無判定
 *
 * @param path - URLパス (現在表示画面のパスなら、指定不要)
 * 例：
 *  計画書（１）の場合は、/care-manager/care-plan1
 *  現在表示画面のパスなら、指定不要
 */
export const hasPrintAuth = async (path?: string): Promise<boolean> => {
  // const route = useRoute()
  // // route共有情報
  // const cmnRouteCom = useCmnRouteCom()
  // let currentPath = path
  // if (route) {
  //   // routeがundefinedでない場合
  //   currentPath = path ?? route.path
  // }
  // // currentPathを取得でなければ、getAuthzPath共通関数を利用して権限チェックを行う
  // if (!currentPath || currentPath === '') {
  //   currentPath = cmnRouteCom.getAuthzPath() ?? ''
  // }
  // // システム共有情報ストア
  // const systemCommonsStore = useSystemCommonsStore()
  // // 権限設定検索
  // const authzKinou = systemCommonsStore.searchAuthzKinou(systemCommonsStore.getSvJigyoId ?? '')
  // if (authzKinou.authFlg !== '2' && authzKinou.authFlg !== '3' && authzKinou.authFlg !== '9') {
  //   return false
  // }
  // // 画面固有の権限チェック処理
  // const input: ICheckAuthzKinouInEntity = {
  //   keys: [{ path: currentPath }],
  // }
  // const res: ICheckAuthzKinouOutEntity = await AuthzRepository.checkAuthzKinou(input)
  // // 機能別権限設定検索
  // const authzKinouFlg = res.data?.authzKinou
  //   .filter((authz) => authz.path === path)
  //   .every(
  //     (authz) =>
  //       (authz.authFlg === '2' || authz.authFlg === '3' || authz.authFlg === '9') && authz.use2
  //   )

  // // 機能別権限設定有無判定
  // if (!authzKinouFlg) {
  //   return false
  // }
  return true
}

/**
 * 外部出力権限有無判定
 *
 * @param path - URLパス (現在表示画面のパスなら、指定不要)
 * 例：
 *  計画書（１）の場合は、/care-manager/care-plan1
 *  現在表示画面のパスなら、指定不要
 */
export const hasOutputAuth = async (path?: string): Promise<boolean> => {
  const route = useRoute()
  // route共有情報
  const cmnRouteCom = useCmnRouteCom()
  let currentPath = path
  if (route) {
    // routeがundefinedでない場合
    currentPath = path ?? route.path
  }
  // currentPathを取得でなければ、getAuthzPath共通関数を利用して権限チェックを行う
  if (!currentPath || currentPath === '') {
    currentPath = cmnRouteCom.getAuthzPath() ?? ''
  }
  // システム共有情報ストア
  const systemCommonsStore = useSystemCommonsStore()
  // 権限設定検索
  const authzKinou = systemCommonsStore.searchAuthzKinou(systemCommonsStore.getSvJigyoId ?? '')
  if (authzKinou.authFlg !== '3' && authzKinou.authFlg !== '9') {
    return false
  }
  // 画面固有の権限チェック処理
  const input: ICheckAuthzKinouInEntity = {
    keys: [{ path: currentPath }],
  }
  const res: ICheckAuthzKinouOutEntity = await AuthzRepository.checkAuthzKinou(input)
  // 機能別権限設定検索
  const authzKinouFlg = res.data?.authzKinou
    .filter((authz) => authz.path === path)
    .every((authz) => (authz.authFlg === '3' || authz.authFlg === '9') && authz.use3)

  // 機能別権限設定有無判定
  if (!authzKinouFlg) {
    return false
  }
  return true
}

/**
 * 登録権限有無判定
 *
 * @param path - URLパス (現在表示画面のパスなら、指定不要)
 * 例：
 *  計画書（１）の場合は、/care-manager/care-plan1
 *  現在表示画面のパスなら、指定不要
 */
export const hasRegistAuth = async (path?: string): Promise<boolean> => {
  // const route = useRoute()
  // // route共有情報
  // const cmnRouteCom = useCmnRouteCom()
  // let currentPath = path
  // if (route) {
  //   // routeがundefinedでない場合
  //   currentPath = path ?? route.path
  // }
  // // currentPathを取得でなければ、getAuthzPath共通関数を利用して権限チェックを行う
  // if (!currentPath || currentPath === '') {
  //   currentPath = cmnRouteCom.getAuthzPath() ?? ''
  // }
  // // システム共有情報ストア
  // const systemCommonsStore = useSystemCommonsStore()
  // // 権限設定検索
  // const authzKinou = systemCommonsStore.searchAuthzKinou(systemCommonsStore.getSvJigyoId ?? '')
  // if (authzKinou.authFlg !== '9') {
  //   return false
  // }
  // // 画面固有の権限チェック処理
  // const input: ICheckAuthzKinouInEntity = {
  //   keys: [{ path: currentPath }],
  // }
  // const res: ICheckAuthzKinouOutEntity = await AuthzRepository.checkAuthzKinou(input)
  // // 機能別権限設定検索
  // const authzKinouFlg = res.data?.authzKinou
  //   .filter((authz) => authz.path === path)
  //   .every((authz) => authz.authFlg === '9' && authz.use9)

  // // 機能別権限設定有無判定
  // if (!authzKinouFlg) {
  //   return false
  // }
  return true
}
