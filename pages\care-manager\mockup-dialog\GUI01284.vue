<script setup lang="ts">
import { computed, ref, watch, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or27626Const } from '~/components/custom-components/organisms/Or27626/Or27626.constants'
import { Or27626Logic } from '~/components/custom-components/organisms/Or27626/Or27626.logic'
import type { Or27626Type, Or27626OnewayType } from '~/types/cmn/business/components/Or27626Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01284'
// ルーティング
const routing = 'GUI01284/pinia'
// 画面物理名
const screenName = 'GUI01284'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27626 = ref({ uniqueCpId: Or27626Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01284' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27626Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27626.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01284',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27626Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27626Const.CP_ID(1)]: or27626.value,
})

// ダイアログ表示フラグ
const showDialogOr27626 = computed(() => {
  // Or27626のダイアログ開閉状態
  return Or27626Logic.state.get(or27626.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or27626)
 */
function onClickOr27626() {
  // Or27626のダイアログ開閉状態を更新する
  Or27626Logic.state.set({
    uniqueCpId: or27626.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or27626Type = ref<Or27626Type>({
  /** 施設ID*/
  facilityId: '',
  /** 調査票施設種別*/
  surveyFacilityKind: '',
  /** 法制コード*/
  facilityName: '',
  /** 保険者番号*/
  zipCode: '',
  /** 記号・番号*/
  addRess: '',
  /** 期間*/
  telNumber: '',
})

const or27626Data: Or27626OnewayType = {
  /** 利用者ID */
  surveyFacilityKind: '1',
  /** ボタン名 */
  certificationSurvey: '2',
}

watch(
  () => or27626Type,
  (newValue) => {
    console.log(newValue)
  },
  { deep: true }
)

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 調査票施設種別
  surveyFacilityKind: { value: '1' } as Mo00045Type,
})

// GUI01284 疎通起動
const or27626onClick = () => {
  or27626Data.surveyFacilityKind = local.surveyFacilityKind.value
  // Or27626のダイアログ開閉状態を更新する
  Or27626Logic.state.set({
    uniqueCpId: or27626.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27626"
        >GUI01284_施設選択画面
      </v-btn>
      <g-custom-or-27626
        v-if="showDialogOr27626"
        v-bind="or27626"
        v-model="or27626Type"
        :oneway-model-value="or27626Data"
      />
    </c-v-col>
  </c-v-row>

  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>

  <div style="margin-left: 20px">調査票施設種別</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.surveyFacilityKind"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>

  <div class="pt-5 w-25 pl-5">
    <v-btn @click="or27626onClick"> GUI01284 疎通起動 </v-btn>
  </div>

  <div class="pt-5 w-100 pl-5">
    return ------ data
    <div>
      {{ or27626Type }}
    </div>
  </div>
</template>
