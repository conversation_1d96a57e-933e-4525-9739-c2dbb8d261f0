<script setup lang="ts">
/**
 * Or29210:備考欄複写画面モーダル
 * GUI01164_備考欄複写画面
 *
 * <AUTHOR> 畢文傑
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Gui00038Logic } from '../Gui00038/Gui00038.logic'
import { Gui00038Const } from '../Gui00038/Gui00038.constants'
import { Or29139Const } from '../Or29139/Or29139.constants'
import { Or29210Const } from './Or29210.constants'
import type { UserListInfoList, Or29210StateType, SexItems, ItemsList } from './Or29210.type'
import { useScreenStore } from '~/stores/session/screen'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useScreenUtils,
  useSetupChildProps,
} from '#imports'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import type {
  Or29210OnewayType,
  Or29210DataType,
} from '~/types/cmn/business/components/Or29210Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo01299OnewayType } from '~/types/business/components/Mo01299Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Or55021OnewayType, radioItemsList } from '~/types/cmn/business/components/Or55021Type'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import type {
  RemarksColumnDuplicateSelectInEntity,
  RemarksColumnDuplicateSelectOutEntity,
} from '~/repositories/cmn/entities/RemarksColumnDuplicateSelectEntity'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import { CustomClass } from '~/types/CustomClassType'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Mo01352OnewayType } from '~/types/business/components/Mo01352Type'
import type {
  BikoListInfo,
  RemarksColumnDuplicateUpdateInEntity,
} from '~/repositories/cmn/entities/RemarksColumnDuplicateUpdateEntity'
import { convHiraganaToHalfKanaMap } from '~/constants/KanaMap'
import { useValidation } from '@/utils/useValidation'

const { byteLength } = useValidation()
const { setChildCpBinds } = useScreenUtils()

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or29210OnewayType
  onewayModelValue: Or29210OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props: Props = defineProps<Props>()
const or00094 = ref({ uniqueCpId: '' }) // 五十音ヘッドライン
const or21814 = ref({ uniqueCpId: '' })
// Or21815_有機体:エラーダイアログ
const or21815 = ref({ uniqueCpId: '' })
// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: '' })
const defaultOneway = reactive({
  or55021Oneway: {
    radioItems: [],
  } as Or55021OnewayType,
  shoriYmd: {
    value: '',
  },
})
const local = reactive({
  radioValue: '1', //帳票区分
  selectionRadio: '1', //処理選択
  userList: [] as UserListInfoList[],
  yymmYm: {
    //処理年月
    value: '',
  },
  modifiedCnt: '',
  ...defaultOneway,
})
const localOneway = reactive({
  mo00039Oneway: {
    // デフォルト値の設定
    name: 'mo00039Oneway',
    itemLabel: '',
    showItemLabel: false,
    hideDetails: true,
  } as Mo00039OnewayType,
  // はいボタン
  mo01265OnewayModelValue: {
    btnLabel: t('label.clear'),
    tooltipText: t('tooltip.clear-the-contents-of-the-note'),
  } as Mo01265OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 備考反映
  mo00611ReflectionOneway: {
    btnLabel: t('btn.reflection'),
    disabled: local.selectionRadio === Or29210Const.DEFAULT.RADIO_TWO ? true : false,
  } as Mo00611OnewayType,
  // 備考取得
  mo00611AcquisitionOneway: {
    btnLabel: t('btn.acquisition'),
    disabled: local.selectionRadio === Or29210Const.DEFAULT.RADIO_ONE ? true : false,
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.save'),
    tooltipText: t('tooltip.save'),
  } as Mo00609OnewayType,
  // 「履歴選択」ダイアログ
  mo00024Oneway: {
    width: '1100px',
    maxWidth: '1800px',
    height: '820px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Gui01164',
      toolbarTitle: t('label.remarks-copy'),
      toolbarName: 'Gui01164ToolBar',
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  textOption1: {
    showItemLabel: false,
    noResize: true,
    autoGrow: false,
    maxlength: '360',
    rules: [byteLength(360)],
    rows: 3,
    maxRows: '3',
  } as Mo00046OnewayType,
  or55021Oneway: {
    ...defaultOneway.or55021Oneway,
  } as Or55021OnewayType,
  or55021Oneway2: {
    ...defaultOneway.or55021Oneway,
  } as Or55021OnewayType,
  // カレンダー
  mo00020Oneway: {
    itemLabel: t('label.offer-ym'),
    showItemLabel: true,
    hideDetails: true,
    maxlength: '10',
    customClass: new CustomClass({ labelClass: 'ma-1' }),
    showSelectArrow: true,
    width: '140',
    mo00009OnewayBack: {
      tooltipText: t('tooltip.display-before-day'),
    },
    mo00009OnewayForward: {
      tooltipText: t('tooltip.display-next-day'),
    },
  } as Mo00020OnewayType,
  sexItems: {
    items: [],
  } as SexItems,
})
const mo01352Oneway = ref<Mo01352OnewayType>({
  textFieldwidth: '115px',
  disabled: false,
  width: '250px',
})
/**
 * 利用者一覧
 */
const organizingIssuesDataTable = reactive({
  headers: [
    // チェックフラグ
    {
      title: '',
      key: 'checkbox',
      sortable: false,
      width: '80px',
      align: 'left',
    },
    // 利用者番号
    {
      title: t('label.plan-user-id'),
      align: 'left',
      key: 'selfId',
      sortable: true,
      width: '140px',
    },
    // 利用者名
    {
      title: t('label.plan-user-name'),
      align: 'left',
      key: 'nameKnj',
      sortable: true,
      width: '160px',
    },
    // 性別
    {
      title: t('label.gender'),
      align: 'left',
      key: 'sex',
      sortable: true,
      width: '90px',
    },
    // 年齢
    {
      title: t('label.age'),
      align: 'left',
      key: 'age',
      sortable: true,
      width: '90px',
    },
    // 備考
    {
      title: t('label.remarks'),
      align: 'left',
      key:
        local.radioValue === Or29139Const.DEFAULT.RADIO_ONE
          ? 'bikouRiyouKnj'
          : local.radioValue === 'bikouTeikyouKnj'
            ? ''
            : 'bikouCalenderKnj',
      sortable: true,
      width: '589px',
    },
  ],
})

/**
 * 全選択チェックフラグ 0:全データチェック 1:チェックのみ
 */
const chkFlg = ref(0)
/**
 * 課題整理総括データ情報選択行データ設定
 */
const selectedOrganizingIssuesData = ref<UserListInfoList[]>([])

const lastDate = ref('') //old処理年月
const staffList = ref([] as UserListInfoList[])
const tableRadioValue = ref('')
/**
 * Mo00018の初期値を設定します。
 */
const mo00018 = ref<Mo00018Type>({
  modelValue: false,
})
/**
 * Mo00018の初期値を設定します。
 */
const mo00018Oneway2 = ref<Mo00018OnewayType>({
  name: '',
  itemLabel: '',
  checkboxLabel: '',
  showItemLabel: false,
  customClass: new CustomClass({ outerClass: '', labelClass: 'ma-1' }),
})
const mo01299Oneway = ref<Mo01299OnewayType>({
  anchorPoint: 'ss-1',
  title: t('label.process-select'),
})
const mo01299Oneway2 = ref<Mo01299OnewayType>({
  anchorPoint: 'ss-1',
  title: t('label.copy-source-notes'),
})
const mo01299Oneway3 = ref<Mo01299OnewayType>({
  anchorPoint: 'ss-1',
  title: t('label.remarks'),
})

/**
 * gui00038
 */
const gui00038 = ref({ uniqueCpId: Gui00038Const.CP_ID(0) })
/**
 * isPermissionRegist
 */
const isPermissionRegist = ref<boolean>(true)
/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or29210Const.DEFAULT.IS_OPEN,
})
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})
// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue: or29210 } = useScreenTwoWayBind<Or29210DataType>({
  cpId: Or29210Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
const { setState } = useScreenOneWayBind<Or29210StateType>({
  cpId: Or29210Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or29210Const.DEFAULT.IS_OPEN
    },
  },
})
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Gui00038Const.CP_ID(1)]: gui00038.value,
  [Or00094Const.CP_ID(1)]: or00094.value, // 五十音ヘッドライン
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  isPermissionRegist.value = await hasRegistAuth()
  // 50音ボタンを初期化
  Or00094Logic.data.set({
    uniqueCpId: or00094.value.uniqueCpId,
    value: { selectValueArray: [Or29210Const.DEFAULT.ALL] },
  })
  or29210.value!.bikouRiyouKnj = { value: props.onewayModelValue.bikouRiyouKnj ?? '' }
  or29210.value!.bikouTeikyouKnj = { value: props.onewayModelValue.bikouTeikyouKnj ?? '' }
  or29210.value!.bikouCalenderKnj = { value: props.onewayModelValue.bikouCalenderKnj ?? '' }
  local.radioValue = props.onewayModelValue.sel ?? ''
  local.yymmYm.value = props.onewayModelValue.yymmYm ?? ''
  lastDate.value = props.onewayModelValue.yymmYm ?? ''
  // 初期情報取得
  void getData()
  // code取得
  await initCodes()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 帳票区分
    { mCdKbnId: CmnMCdKbnId.CLASSIFICATION_OF_ACCOUNTS_AND_INAOICES },
    // 処理選択
    { mCdKbnId: CmnMCdKbnId.PROCESSING_SELECTION },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // 帳票区分
  localOneway.or55021Oneway.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.CLASSIFICATION_OF_ACCOUNTS_AND_INAOICES
  ) as radioItemsList[]
  // 処理選択
  localOneway.or55021Oneway2.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.PROCESSING_SELECTION
  ) as radioItemsList[]
  // 性別区分
  localOneway.sexItems.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY
  ) as ItemsList[]
}
/** 初期情報取得 */
async function getData() {
  mo00018.value.modelValue = false
  selectedOrganizingIssuesData.value = []
  tableRadioValue.value = ''
  or29210.value!.bikouRiyouKnj = { value: props.onewayModelValue.bikouRiyouKnj ?? '' }
  or29210.value!.bikouTeikyouKnj = { value: props.onewayModelValue.bikouTeikyouKnj ?? '' }
  or29210.value!.bikouCalenderKnj = { value: props.onewayModelValue.bikouCalenderKnj ?? '' }

  const inputData: RemarksColumnDuplicateSelectInEntity = {
    // 支援事業者ID
    shienId: props.onewayModelValue.shienId,
    // 利用者ID
    userId: props.onewayModelValue.userId,
    // サービス提供年月
    yymmYm: local.yymmYm.value,
    // サービス提供年月（変更日）
    yymmD: props.onewayModelValue.yymmD,
  }

  // バックエンドAPIから初期情報取得
  const res: RemarksColumnDuplicateSelectOutEntity = await ScreenRepository.select(
    'remarksColumnDuplicateSelect',
    inputData
  )
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: false,
    },
  })
  if (res?.data !== null && res.data.userList.length !== Or29210Const.DEFAULT.ZERO) {
    // 明細リスト
    local.modifiedCnt = res.data.modifiedCnt
    staffList.value = structuredClone(res.data.userList)
    local.userList = structuredClone(res.data.userList)
    staffList.value.forEach((item, index) => {
      item.checkbox = { modelValue: false }
      item.index = index
      item.sex = getSexName(item.sex)
    })
    local.userList.forEach((item, index) => {
      item.checkbox = { modelValue: false }
      item.index = index
      item.sex = getSexName(item.sex)
    })
    or29210.value!.userList = local.userList
  } else {
    showOr21815MsgOneBtn(t('message.w-cmn-20439'))
    local.yymmYm.value = lastDate.value ? lastDate.value : local.yymmYm.value
  }

  useScreenStore().setCpTwoWay({
    cpId: Or29210Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: or29210.value,
    isInit: true,
  })
}

/**
 * 性別名称取得処理
 *
 * @param cd - 性別コード
 */
function getSexName(cd: string) {
  const ret = localOneway.sexItems.items.filter((item) => item.value === cd)
  if (ret.length > 0) {
    return ret[0].label
  }
  return ''
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  if (isEdit.value) {
    const result = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10092'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
    if (result === Or29210Const.DIALOG_RESULT_YES) {
      if (await saveFun()) {
        setState({ isOpen: false })
      }
    } else if (result === Or29210Const.DIALOG_RESULT_NO) {
      setState({ isOpen: false })
    }
  } else {
    setState({ isOpen: false })
  }
}

/**
 * 「確定」ボタン押下
 */
async function saveFun() {
  if (isPermissionRegist.value) {
    // メッセージ内容：情報を保存する権限がありません。入力内容は破棄されますが、処理を続けますか？
    // showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
    const result = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10006'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
    })
    if (result === Or29210Const.DIALOG_RESULT_NO) {
      return
    }
  }
  // 画面入力データ変更があるかどうかを判定する
  if (!isEdit.value) {
    // メッセージ内容：
    //「変更されている項目がないため、保存を行うことは出来ません。
    // 項目を入力変更してから、再度保存を行ってください。」
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-21800'),
    })
    return
  }

  or29210.value!.userList.forEach((data) => {
    local.userList.forEach((item) => {
      data.checkbox.modelValue = false
      if (data.index === item.index) {
        data.bikouRiyouKnj = item.bikouRiyouKnj
        data.bikouTeikyouKnj = item.bikouTeikyouKnj
        data.bikouCalenderKnj = item.bikouCalenderKnj
        data.updateKbn = Or29210Const.UPD_FLAG_U
      }
      // } else {
      //   data.updateKbn = Or29210Const.UPD_FLAG_D
      // }
    })
  })
  const Arry: BikoListInfo[] = []
  or29210.value!.userList.forEach((item) => {
    const obj = {} as BikoListInfo
    obj.userId = item.userId
    obj.bikouRiyouKnj = item.bikouRiyouKnj
    obj.bikouTeikyouKnj = item.bikouTeikyouKnj
    obj.bikouCalenderKnj = item.bikouCalenderKnj
    obj.updateKbn = item.updateKbn
    obj.modifiedCnt = item.modifiedCnt
    Arry.push(obj)
  })
  const param = {
    sel: local.radioValue,
    shienId: props.onewayModelValue.shienId,
    userId: props.onewayModelValue.userId,
    yymmYm: local.yymmYm.value,
    yymmD: props.onewayModelValue.yymmD,
    bikouRiyouKnj:
      local.radioValue === Or29210Const.DEFAULT.RADIO_ONE
        ? or29210.value!.bikouRiyouKnj.value
        : props.onewayModelValue.bikouRiyouKnj,
    bikouTeikyouKnj:
      local.radioValue === Or29210Const.DEFAULT.RADIO_TWO
        ? or29210.value!.bikouTeikyouKnj.value
        : props.onewayModelValue.bikouTeikyouKnj,
    bikouCalenderKnj:
      local.radioValue === Or29210Const.DEFAULT.RADIO_THREE
        ? or29210.value!.bikouCalenderKnj.value
        : props.onewayModelValue.bikouCalenderKnj,
    updateKbn: props.onewayModelValue.updateKbn,
    modifiedCnt: local.modifiedCnt,
    bikoList: Arry,
  } as RemarksColumnDuplicateUpdateInEntity

  const inputMockDataUpdate: RemarksColumnDuplicateUpdateInEntity = param
  const resData = await ScreenRepository.update('remarksColumnDuplicateUpdate', inputMockDataUpdate)
  if (resData.statusCode === Or29210Const.SUCCESS) {
    mo00018.value.modelValue = false
    selectedOrganizingIssuesData.value = []
    tableRadioValue.value = ''
    await getData()
  }
  return true
}

// 「帳票区分」選択
async function radioClick(oldVal: string, value: string) {
  if (value !== local.radioValue) {
    if (isEdit.value) {
      const result = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10092'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'normal3',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })
      if (result === Or29210Const.DIALOG_RESULT_YES) {
        await saveFun()
      } else if (result === Or29210Const.DIALOG_RESULT_NO) {
        selectedOrganizingIssuesData.value = []
        tableRadioValue.value = ''
        local.userList.forEach((item) => {
          item.checkbox.modelValue = false
        })
        local.userList = cloneDeep(staffList.value)
        if (oldVal === Or29139Const.DEFAULT.RADIO_ONE) {
          or29210.value!.bikouRiyouKnj.value = props.onewayModelValue.bikouRiyouKnj
        } else if (oldVal === Or29139Const.DEFAULT.RADIO_TWO) {
          or29210.value!.bikouTeikyouKnj.value = props.onewayModelValue.bikouTeikyouKnj
        } else if (oldVal === Or29139Const.DEFAULT.RADIO_THREE) {
          or29210.value!.bikouCalenderKnj.value = props.onewayModelValue.bikouCalenderKnj
        }
      } else {
        local.radioValue = oldVal
      }
    }
  }
}
// 「処理選択」選択
async function selectionRadioClick(oldVal: string, value: string) {
  if (value !== local.selectionRadio) {
    if (isEdit.value) {
      const result = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10092'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'normal3',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })
      if (result === Or29210Const.DIALOG_RESULT_YES) {
        await saveFun()
      } else if (result === Or29210Const.DIALOG_RESULT_NO) {
        selectedOrganizingIssuesData.value = []
        tableRadioValue.value = ''
        local.userList = cloneDeep(staffList.value)
        local.userList.forEach((item) => {
          item.checkbox.modelValue = false
        })
        if (local.radioValue === Or29139Const.DEFAULT.RADIO_ONE) {
          or29210.value!.bikouRiyouKnj.value = props.onewayModelValue.bikouRiyouKnj
        } else if (local.radioValue === Or29139Const.DEFAULT.RADIO_TWO) {
          or29210.value!.bikouTeikyouKnj.value = props.onewayModelValue.bikouTeikyouKnj
        } else if (oldVal === Or29139Const.DEFAULT.RADIO_THREE) {
          or29210.value!.bikouCalenderKnj.value = props.onewayModelValue.bikouCalenderKnj
        }
      } else {
        local.selectionRadio = oldVal
        selectedOrganizingIssuesData.value.forEach((val) => {
          local.userList.forEach((item) => {
            if (item.index === val.index) {
              item.checkbox.modelValue = true
            }
          })
        })
      }
    }
  }
}
/**
 * 処理選択change
 */
function radioChange() {
  mo00018.value.modelValue = false
  selectedOrganizingIssuesData.value = []
  tableRadioValue.value = ''
  local.userList.forEach((item) => {
    item.checkbox.modelValue = false
  })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(mo00024, async () => {
  // 組織dialog自動クローズを手動判定に変更
  if (mo00024.value.emitType === 'closeBtnClick') {
    await close()
    mo00024.value.emitType = 'blank'
  }
})
/**
 * 入力支援画面確定処理
 *
 * @description
 * 入力支援画面の呼び出し元コンポーネントに値を設定する。
 * またGui00038のボタン押下フラグをリセットする。
 */

watch(
  () => Gui00038Logic.event.get(gui00038.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 文字数の上限
    const maxLength = Or29210Const.DEFAULT.MAXLENGTH1
    const maxLength3 = Or29210Const.DEFAULT.MAXLENGTH2
    if (newValue.confirmFlg && newValue.confirmParams) {
      if (local.radioValue === Or29210Const.DEFAULT.RADIO_ONE) {
        or29210.value!.bikouRiyouKnj.value = newValue.confirmParams.text.slice(0, maxLength)
      } else if (local.radioValue === Or29210Const.DEFAULT.RADIO_TWO) {
        or29210.value!.bikouTeikyouKnj.value = newValue.confirmParams.text.slice(0, maxLength)
      } else if (local.radioValue === Or29210Const.DEFAULT.RADIO_THREE) {
        or29210.value!.bikouCalenderKnj.value = newValue.confirmParams.text.slice(0, maxLength3)
      }
      // 入力支援画面のeventStatusの値をリセット
      setChildCpBinds(props.uniqueCpId, {
        [Gui00038Const.CP_ID(1)]: {
          eventStatusEvents: {
            confirmFlg: false,
            confirmParams: undefined,
          },
        },
      })
    }
  }
)

/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 確認ダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = (uniqueCpId: string, state: Or21814OnewayType) => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      ...state,
      isOpen: true,
    },
  })
  return new Promise<string>((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = Or29210Const.DIALOG_RESULT_CANCEL
        if (event?.firstBtnClickFlg) {
          result = Or29210Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or29210Const.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * 警告ダイアログの開閉
 *
 * @param errormsg - Message
 */
function showOr21815MsgOneBtn(errormsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
  // 確認ダイアログをオープン
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 五十音を変更した場合の処理
 */
watch(
  () => Or00094Logic.event.get(or00094.value.uniqueCpId),
  async (newValue) => {
    if (isEdit.value) {
      const result = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10092'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'normal3',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })
      if (result === Or29210Const.DIALOG_RESULT_YES) {
        await saveFun()
      } else if (result === Or29210Const.DIALOG_RESULT_NO) {
        or29210.value!.bikouRiyouKnj.value = props.onewayModelValue.bikouRiyouKnj ?? ''
        or29210.value!.bikouTeikyouKnj.value = props.onewayModelValue.bikouTeikyouKnj ?? ''
        or29210.value!.bikouCalenderKnj.value = props.onewayModelValue.bikouCalenderKnj ?? ''
        local.radioValue = props.onewayModelValue.sel ?? ''
        local.yymmYm.value = props.onewayModelValue.yymmYm ?? ''
        lastDate.value = props.onewayModelValue.yymmYm ?? ''
        mo00018.value.modelValue = false
        selectedOrganizingIssuesData.value = []
        tableRadioValue.value = ''
        local.userList = cloneDeep(staffList.value)
        if (newValue?.charBtnClickFlg) {
          // 50音ヘッドラインの文字ボタン押下イベントを検知
          const selectValueArray = Or00094Logic.data.get(or00094.value.uniqueCpId)?.selectValueArray
          if (selectValueArray === undefined || selectValueArray.length === 0) {
            return
          }
          // 親ボタン押下
          if (selectValueArray.length === 1) {
            const inputData = selectValueArray[0]
            if (inputData !== Or29210Const.DEFAULT.ALL) {
              local.userList = local.userList.filter(
                (item) => item.name1Kana.substr(0, 1) === convHiraganaToHalfKanaMap[inputData]
              )
            }
          } else {
            // 子ボタン押下
            const inputData = selectValueArray[0]
            if (inputData === Or29210Const.DEFAULT.KANA_A) {
              local.userList = local.userList.filter(
                (item) =>
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_A) >=
                    0 &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_KA) < 0
              )
            } else if (inputData === Or29210Const.DEFAULT.KANA_KA) {
              local.userList = local.userList.filter(
                (item) =>
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_KA) >=
                    0 &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_SA) < 0
              )
            } else if (inputData === Or29210Const.DEFAULT.KANA_SA) {
              local.userList = local.userList.filter(
                (item) =>
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_SA) >=
                    0 &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_TA) < 0
              )
            } else if (inputData === Or29210Const.DEFAULT.KANA_TA) {
              local.userList = local.userList.filter(
                (item) =>
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_TA) >=
                    0 &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_NA) < 0
              )
            } else if (inputData === Or29210Const.DEFAULT.KANA_NA) {
              local.userList = local.userList.filter(
                (item) =>
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_NA) >=
                    0 &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_HA) < 0
              )
            } else if (inputData === Or29210Const.DEFAULT.KANA_HA) {
              local.userList = local.userList.filter(
                (item) =>
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_HA) >=
                    0 &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_MA) < 0
              )
            } else if (inputData === Or29210Const.DEFAULT.KANA_MA) {
              local.userList = local.userList.filter(
                (item) =>
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_MA) >=
                    0 &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_YA) < 0
              )
            } else if (inputData === Or29210Const.DEFAULT.KANA_YA) {
              local.userList = local.userList.filter(
                (item) =>
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_YA) >=
                    0 &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_RA) < 0
              )
            } else if (inputData === Or29210Const.DEFAULT.KANA_RA) {
              local.userList = local.userList.filter(
                (item) =>
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_RA) >=
                    0 &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_WA) < 0
              )
            } else if (inputData === Or29210Const.DEFAULT.KANA_WA) {
              local.userList = local.userList.filter(
                (item) =>
                  [
                    Or29210Const.DEFAULT.HALF_KANA_WA,
                    Or29210Const.DEFAULT.HALF_KANA_WO,
                    Or29210Const.DEFAULT.HALF_KANA_EN,
                  ].includes(item.name1Kana.substr(0, 1)) &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_WA) < 0
              )
            } else if (inputData === Or29210Const.DEFAULT.KANA_SONOTA) {
              local.userList = local.userList.filter(
                (item) =>
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_A) < 0 &&
                  item.name1Kana.substr(0, 1) !== Or29210Const.DEFAULT.HALF_KANA_WO &&
                  item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_EN) > 0
              )
            }
          }
        }
      } else {
        Or00094Logic.state.set({
          uniqueCpId: or00094.value.uniqueCpId,
          state: {
            focusSettingFlg: true,
            focusSettingInitial: [Or29210Const.DEFAULT.ALL],
          },
        })
        local.userList = cloneDeep(staffList.value)
      }
    } else {
      mo00018.value.modelValue = false
      selectedOrganizingIssuesData.value = []
      tableRadioValue.value = ''
      local.userList = cloneDeep(staffList.value)
      if (newValue?.charBtnClickFlg) {
        // 50音ヘッドラインの文字ボタン押下イベントを検知
        const selectValueArray = Or00094Logic.data.get(or00094.value.uniqueCpId)?.selectValueArray
        if (selectValueArray === undefined || selectValueArray.length === 0) {
          return
        }
        // 親ボタン押下
        if (selectValueArray.length === 1) {
          const inputData = selectValueArray[0]
          if (inputData !== Or29210Const.DEFAULT.ALL) {
            local.userList = local.userList.filter(
              (item) => item.name1Kana.substr(0, 1) === convHiraganaToHalfKanaMap[inputData]
            )
          }
        } else {
          // 子ボタン押下
          const inputData = selectValueArray[0]
          if (inputData === Or29210Const.DEFAULT.KANA_A) {
            local.userList = local.userList.filter(
              (item) =>
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_A) >= 0 &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_KA) < 0
            )
          } else if (inputData === Or29210Const.DEFAULT.KANA_KA) {
            local.userList = local.userList.filter(
              (item) =>
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_KA) >= 0 &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_SA) < 0
            )
          } else if (inputData === Or29210Const.DEFAULT.KANA_SA) {
            local.userList = local.userList.filter(
              (item) =>
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_SA) >= 0 &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_TA) < 0
            )
          } else if (inputData === Or29210Const.DEFAULT.KANA_TA) {
            local.userList = local.userList.filter(
              (item) =>
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_TA) >= 0 &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_NA) < 0
            )
          } else if (inputData === Or29210Const.DEFAULT.KANA_NA) {
            local.userList = local.userList.filter(
              (item) =>
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_NA) >= 0 &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_HA) < 0
            )
          } else if (inputData === Or29210Const.DEFAULT.KANA_HA) {
            local.userList = local.userList.filter(
              (item) =>
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_HA) >= 0 &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_MA) < 0
            )
          } else if (inputData === Or29210Const.DEFAULT.KANA_MA) {
            local.userList = local.userList.filter(
              (item) =>
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_MA) >= 0 &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_YA) < 0
            )
          } else if (inputData === Or29210Const.DEFAULT.KANA_YA) {
            local.userList = local.userList.filter(
              (item) =>
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_YA) >= 0 &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_RA) < 0
            )
          } else if (inputData === Or29210Const.DEFAULT.KANA_RA) {
            local.userList = local.userList.filter(
              (item) =>
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_RA) >= 0 &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_WA) < 0
            )
          } else if (inputData === Or29210Const.DEFAULT.KANA_WA) {
            local.userList = local.userList.filter(
              (item) =>
                [
                  Or29210Const.DEFAULT.HALF_KANA_WA,
                  Or29210Const.DEFAULT.HALF_KANA_WO,
                  Or29210Const.DEFAULT.HALF_KANA_EN,
                ].includes(item.name1Kana.substr(0, 1)) &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_WA) < 0
            )
          } else if (inputData === Or29210Const.DEFAULT.KANA_SONOTA) {
            local.userList = local.userList.filter(
              (item) =>
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_A) < 0 &&
                item.name1Kana.substr(0, 1) !== Or29210Const.DEFAULT.HALF_KANA_WO &&
                item.name1Kana.substr(0, 1).localeCompare(Or29210Const.DEFAULT.HALF_KANA_EN) > 0
            )
          }
        }
      }
    }
  }
)

// 全選択チェック
watch(
  () => mo00018.value.modelValue,
  (newValue) => {
    if (chkFlg.value === 0) {
      local.userList?.forEach((item: UserListInfoList) => {
        item.checkbox.modelValue = newValue
        const indexInSelected = selectedOrganizingIssuesData.value.findIndex(
          (val) => val.index === item.index
        )
        if (newValue) {
          if (indexInSelected === -1) {
            selectedOrganizingIssuesData.value.push(item)
          }
        } else {
          if (indexInSelected > -1) {
            selectedOrganizingIssuesData.value.splice(indexInSelected, 1)
          }
        }
      })
    }
  },
  { deep: true, immediate: true }
)
/**
 * 行選択チェックボックスチェックした時の処理
 *
 * @param index - index
 */
const organizingIssuesDataCheckRow = async (index: number) => {
  chkFlg.value = 1
  let isAllChecked = true
  selectedOrganizingIssuesData.value = []
  if (local.selectionRadio === Or29210Const.DEFAULT.RADIO_TWO) {
    local.userList?.forEach((item: UserListInfoList, indx: number) => {
      if (index === indx && item.checkbox.modelValue) {
        item.checkbox.modelValue = true
        selectedOrganizingIssuesData.value.push(item)
      } else {
        item.checkbox.modelValue = false
      }
    })
  } else {
    local.userList?.forEach((item: UserListInfoList) => {
      if (item.checkbox.modelValue === true) {
        selectedOrganizingIssuesData.value.push(item)
      }
      if (item.checkbox.modelValue === false) {
        isAllChecked = false
      }
    })
  }
  if (isAllChecked) {
    mo00018.value.modelValue = true
  } else {
    mo00018.value.modelValue = false
  }

  await nextTick()
  chkFlg.value = 0
}

const tableRadioChange = (index: number) => {
  tableRadioValue.value = index.toString()
  selectedOrganizingIssuesData.value = []
  if (local.selectionRadio === Or29210Const.DEFAULT.RADIO_TWO) {
    local.userList?.forEach((item: UserListInfoList, indx: number) => {
      if (index === indx && item.checkbox.modelValue) {
        item.checkbox.modelValue = true
        selectedOrganizingIssuesData.value.push(item)
      } else {
        item.checkbox.modelValue = false
      }
    })
  }
}
// 選択した行
const handleRow = async (item: UserListInfoList) => {
  chkFlg.value = 1
  let isAllChecked = true
  if (local.selectionRadio === Or29210Const.DEFAULT.RADIO_TWO) {
    tableRadioValue.value = item.index.toString()
    const selectedItem = selectedOrganizingIssuesData.value[0]
    if (selectedItem && selectedItem.index === item.index) {
      selectedOrganizingIssuesData.value = []
      item.checkbox.modelValue = false
      tableRadioValue.value = ''
      mo00018.value.modelValue = false
    } else {
      selectedOrganizingIssuesData.value = []

      local.userList?.forEach((userItem) => {
        if (userItem.checkbox && userItem.index !== item.index) {
          userItem.checkbox.modelValue = false
        }
      })

      item.checkbox.modelValue = true
      selectedOrganizingIssuesData.value.push(item)

      mo00018.value.modelValue = false
    }
  } else {
    if (item.checkbox.modelValue) {
      const index = selectedOrganizingIssuesData.value.findIndex(
        (data) => data.index === item.index
      )

      if (index > -1) {
        selectedOrganizingIssuesData.value.splice(index, 1)
      }

      item.checkbox.modelValue = false
      isAllChecked = false
    } else {
      selectedOrganizingIssuesData.value.push(item)
      item.checkbox.modelValue = true
    }
  }
  if (isAllChecked) {
    mo00018.value.modelValue = true
  } else {
    mo00018.value.modelValue = false
  }
  await nextTick()
  chkFlg.value = 0
}
/**
 * 「備考反映」ボタン押下
 *
 */
const reflectionFun = async () => {
  if (selectedOrganizingIssuesData.value.length === Or29210Const.DEFAULT.ZERO) {
    const dialogResult = await showOr21813Msg(t('message.e-cmn-41313'))
    if (dialogResult === Or29139Const.DIALOG_RESULT_YES) {
      return false
    }
  } else {
    if (local.radioValue === Or29210Const.DEFAULT.RADIO_ONE) {
      selectedOrganizingIssuesData.value.forEach((item) => {
        local.userList.forEach((val: UserListInfoList) => {
          if (item.index === val.index) {
            val.bikouRiyouKnj = or29210.value!.bikouRiyouKnj.value ?? ''
            val.checkbox.modelValue = item.checkbox.modelValue
          }
        })
        item.bikouRiyouKnj = or29210.value!.bikouRiyouKnj.value ?? ''
      })
    }
    if (local.radioValue === Or29210Const.DEFAULT.RADIO_TWO) {
      selectedOrganizingIssuesData.value.forEach((item) => {
        local.userList.forEach((val: UserListInfoList) => {
          if (item.index === val.index) {
            val.bikouTeikyouKnj = or29210.value!.bikouTeikyouKnj.value ?? ''
            val.checkbox.modelValue = item.checkbox.modelValue
          }
        })
        item.bikouTeikyouKnj = or29210.value!.bikouTeikyouKnj.value ?? ''
      })
    }
    if (local.radioValue === Or29210Const.DEFAULT.RADIO_THREE) {
      selectedOrganizingIssuesData.value.forEach((item) => {
        local.userList.forEach((val: UserListInfoList) => {
          if (item.index === val.index) {
            val.bikouCalenderKnj = or29210.value!.bikouCalenderKnj.value ?? ''
            val.checkbox.modelValue = item.checkbox.modelValue
          }
        })
        item.bikouCalenderKnj = or29210.value!.bikouCalenderKnj.value ?? ''
      })
    }
  }
}
/**
 * 「備考取得」ボタン押下
 *
 */
const acquisitionFun = async () => {
  if (selectedOrganizingIssuesData.value.length === Or29210Const.DEFAULT.ZERO) {
    const dialogResult = await showOr21813Msg(t('message.e-cmn-41313'))
    if (dialogResult === Or29139Const.DIALOG_RESULT_YES) {
      return false
    }
  } else {
    if (local.radioValue === Or29210Const.DEFAULT.RADIO_ONE) {
      or29210.value!.bikouRiyouKnj.value = selectedOrganizingIssuesData.value[0].bikouRiyouKnj
    }
    if (local.radioValue === Or29210Const.DEFAULT.RADIO_TWO) {
      or29210.value!.bikouTeikyouKnj.value = selectedOrganizingIssuesData.value[0].bikouTeikyouKnj
    }
    if (local.radioValue === Or29210Const.DEFAULT.RADIO_THREE) {
      or29210.value!.bikouCalenderKnj.value = selectedOrganizingIssuesData.value[0].bikouCalenderKnj
    }
  }
}

/**
 * 最後のリスニング提供年月
 *
 */
watch(
  () => local.yymmYm,
  (newValue, oldValue) => {
    if (oldValue?.value) {
      lastDate.value = oldValue?.value
    }
  },
  { deep: true, immediate: true }
)
/**
 * 「提供年月」へんかん
 *
 */
const dateYmUpdate = async () => {
  if (isEdit.value) {
    const result = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10092'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
    if (result === Or29210Const.DIALOG_RESULT_YES) {
      await saveFun()
    } else if (result === Or29210Const.DIALOG_RESULT_NO) {
      or29210.value!.bikouRiyouKnj.value = props.onewayModelValue.bikouRiyouKnj ?? ''
      or29210.value!.bikouTeikyouKnj.value = props.onewayModelValue.bikouTeikyouKnj ?? ''
      or29210.value!.bikouCalenderKnj.value = props.onewayModelValue.bikouCalenderKnj ?? ''
      await getData()
    }
  } else {
    await getData()
  }
}

/**
 * error開閉
 *
 * @param uniqueCpId
 *
 * @param errormsg - Message
 */

const showOr21813Msg = (errorMsg: string) => {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: errorMsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutters
        style="padding: 0 8px; margin-top: 0px"
      >
        <c-v-col>
          <base-mo01299
            :oneway-model-value="mo01299Oneway"
            class="content1"
          >
            <template #content>
              <div>
                <base-mo00039
                  v-model="local.radioValue"
                  :oneway-model-value="localOneway.mo00039Oneway"
                >
                  <base-at-radio
                    v-for="item in localOneway.or55021Oneway.radioItems"
                    :key="item.id"
                    :name="item.label"
                    :radio-label="item.label"
                    :value="item.value"
                    @click="radioClick(local.radioValue, $event.target.value)"
                  />
                </base-mo00039>
              </div>
            </template>
          </base-mo01299>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        style="margin-top: 8px; padding: 0 8px"
      >
        <c-v-col
          style="
            min-width: 300px !important;
            max-width: 300px !important;
            border-bottom: solid thin rgb(var(--v-theme-light));
            border-left: solid thin rgb(var(--v-theme-light));
          "
        >
          <!-- 処理選択 -->
          <base-mo00039
            v-model="local.selectionRadio"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="item in localOneway.or55021Oneway2.radioItems"
              :key="item.id"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
              @click="selectionRadioClick(local.selectionRadio, $event.target.value)"
              @change="radioChange"
            />
          </base-mo00039>
        </c-v-col>
        <c-v-col>
          <base-mo01299
            :oneway-model-value="
              local.selectionRadio === Or29210Const.DEFAULT.RADIO_ONE
                ? mo01299Oneway2
                : mo01299Oneway3
            "
            class="content2"
          >
            <template #content>
              <div>
                <!-- 備考欄（利用票） -->
                <base-mo00046
                  v-if="local.radioValue === Or29210Const.DEFAULT.RADIO_ONE"
                  v-model="or29210!.bikouRiyouKnj"
                  :oneway-model-value="localOneway.textOption1"
                  variant="outlined"
                  class="memoInputTextArea"
                ></base-mo00046>
                <!-- 備考欄（提供票） -->
                <base-mo00046
                  v-if="local.radioValue === Or29210Const.DEFAULT.RADIO_TWO"
                  v-model="or29210!.bikouTeikyouKnj"
                  :oneway-model-value="localOneway.textOption1"
                  variant="outlined"
                  class="memoInputTextArea"
                ></base-mo00046>
                <!-- 備考欄（カレンダー） -->
                <base-mo00046
                  v-if="local.radioValue === Or29210Const.DEFAULT.RADIO_THREE"
                  v-model="or29210!.bikouCalenderKnj"
                  :oneway-model-value="localOneway.textOption1"
                  variant="outlined"
                  class="memoInputTextArea"
                ></base-mo00046>
              </div>
            </template>
          </base-mo01299>
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        style="margin-top: 8px; padding: 0 8px"
      >
        <c-v-col
          cols="1"
          style="line-height: 32px"
          >{{ t('label.offer-ym') }}</c-v-col
        >
        <c-v-col cols="3">
          <!-- 提供年月 -->
          <base-mo01352
            v-model="local.yymmYm"
            style="display: flex; align-items: center"
            :oneway-model-value="mo01352Oneway"
            @update:model-value="dateYmUpdate"
          />
        </c-v-col>
        <c-v-col
          cols="8"
          align-self="center"
        >
          <div style="width: 100%; display: flex; justify-content: center">
            <base-mo00611
              style="margin-right: 4px"
              :oneway-model-value="{
                btnLabel: t('btn.reflection'),
                tooltipText: t('tooltip.reflection-title'),
                disabled: local.selectionRadio === Or29210Const.DEFAULT.RADIO_TWO ? true : false,
              }"
              @click="reflectionFun"
            />
            <base-mo00611
              style="margin-left: 4px"
              :oneway-model-value="{
                btnLabel: t('btn.acquisition'),
                tooltipText: t('tooltip.acquisition-title'),
                disabled: local.selectionRadio === Or29210Const.DEFAULT.RADIO_ONE ? true : false,
              }"
              @click="acquisitionFun"
            />
          </div>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col
          cols="12"
          class="d-flex"
          style="max-height: 460px; min-height: 460px"
        >
          <div style="width: 30px">
            <!-- 五十音ヘッドライン -->
            <g-base-or-00094 v-bind="or00094" />
          </div>
          <!-- 明細一覧 -->
          <c-v-data-table
            style="height: 480px"
            :headers="organizingIssuesDataTable.headers"
            :items="local.userList"
            class="table-wrapper"
            :hide-default-footer="true"
            fixed-header
            hover
          >
            <template #[`header.checkbox`]>
              <!-- チェックボックス -->
              <base-mo00018
                v-if="local.selectionRadio === Or29210Const.DEFAULT.RADIO_ONE ? true : false"
                v-model="mo00018"
                :class="
                  selectedOrganizingIssuesData &&
                  selectedOrganizingIssuesData.length > Or29210Const.DEFAULT.ZERO &&
                  selectedOrganizingIssuesData.length !== local.userList.length
                    ? 'allckeck'
                    : ''
                "
                :oneway-model-value="{
                  name: '',
                  itemLabel: '',
                  checkboxLabel: '',
                  showItemLabel: false,
                  indeterminate:
                    selectedOrganizingIssuesData &&
                    selectedOrganizingIssuesData.length > Or29210Const.DEFAULT.ZERO &&
                    selectedOrganizingIssuesData.length !== local.userList.length
                      ? true
                      : false,
                  customClass: new CustomClass({ outerClass: '', labelClass: 'ma-1' }),
                }"
                click.stop
              ></base-mo00018>
            </template>
            <template #item="{ item, index }">
              <tr
                height="32px"
                :class="{ 'highlight-row': item.checkbox.modelValue }"
                @click="handleRow(item)"
              >
                <td width="50px">
                  <base-mo00018
                    v-if="local.selectionRadio === Or29210Const.DEFAULT.RADIO_ONE ? true : false"
                    v-model="item.checkbox"
                    :oneway-model-value="mo00018Oneway2"
                    @change="organizingIssuesDataCheckRow(index)"
                    @click.stop
                  ></base-mo00018>
                  <base-at-radio
                    v-else
                    v-model="tableRadioValue"
                    class="table-radio"
                    name="radio-28500"
                    :value="item.index.toString()"
                    radio-label=""
                    readonly
                    @click.prevent="tableRadioChange(index)"
                  />
                </td>
                <td>
                  <!-- 利用者番号 -->
                  <base-mo01336
                    :oneway-model-value="{ value: item.selfId }"
                    style="width: 100%"
                  >
                  </base-mo01336>
                </td>
                <td>
                  <!-- 利用者名 -->
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.nameKnj,
                    }"
                  ></base-mo01337>
                </td>
                <td>
                  <base-mo01337
                    style="text-align: center"
                    :oneway-model-value="{
                      value: item.sex,
                      valueFontColor:
                        item.sex === Or29210Const.DEFAULT.GIRL ? '#FF0000' : '#0000FF',
                      textAlign: 'center',
                    }"
                  ></base-mo01337>
                </td>
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.age ? item.age + t('label.age-year') : '',
                    }"
                  ></base-mo01337>
                </td>
                <td>
                  <!-- 備考欄 -->
                  <span class="overflowText">{{
                    local.radioValue === Or29210Const.DEFAULT.RADIO_ONE
                      ? item.bikouRiyouKnj
                      : local.radioValue === Or29210Const.DEFAULT.RADIO_TWO
                        ? item.bikouTeikyouKnj
                        : local.radioValue === Or29210Const.DEFAULT.RADIO_THREE
                          ? item.bikouCalenderKnj
                          : ''
                  }}</span>
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :max-width="350"
                    :text="
                      local.radioValue === Or29210Const.DEFAULT.RADIO_ONE
                        ? item.bikouRiyouKnj
                        : local.radioValue === Or29210Const.DEFAULT.RADIO_TWO
                          ? item.bikouTeikyouKnj
                          : local.radioValue === Or29210Const.DEFAULT.RADIO_THREE
                            ? item.bikouCalenderKnj
                            : ''
                    "
                    open-delay="200"
                  />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
      <g-custom-gui00038
        v-if="Gui00038Logic.state.get(gui00038.uniqueCpId)?.isOpen"
        v-bind="gui00038"
      />
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン mo00609 -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609ConfirmOneway"
          :disabled="isPermissionRegist"
          class="mx-2"
          @click="saveFun"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
  <!-- Or21814:有機体:情報ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21815:有機体:エラーダイアログ -->
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.content2 {
  position: relative;
}
:deep(.content2 .section-label) {
  padding-left: 50px;
  text-align: center;
}
.edit {
  position: absolute;
  top: 41px;
  left: 110px;
  background: none;
}

.sectionContent {
  padding: 0 !important;
  margin-bottom: 80px;
}
:deep(.content2) {
  border-top: none;
}
:deep(.content1 .section-header) {
  min-width: 300px !important;
  max-width: 300px !important;
}
:deep(.section-header) {
  padding: 0 !important;
  border-right: 1px solid rgb(var(--v-theme-light));
}
:deep(.section-content) {
  padding: 0 !important;
}
.date-sty {
  display: flex;
}
:deep(.v-checkbox-btn) {
  min-height: 20px !important;
  height: 20px !important;
}
:deep(.v-data-table__th .v-selection-control__input) {
  margin-right: 16px;
}
:deep(.allckeck .v-selection-control__input > .v-icon) {
  opacity: var(--v-medium-emphasis-opacity);
}
:deep(.v-table[data-v-78b49f53] i.fill) {
  color: unset !important;
}
:deep(.allckeck .text-key) {
  color: unset !important;
}
.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
:deep(.radio-group) {
  margin-top: 0;
}
:deep(.table-radio) {
  min-height: 32px !important;
  height: 32px !important;
  --v-selection-control-size: 0;
  i {
    margin-left: 40px;
  }
}
</style>
