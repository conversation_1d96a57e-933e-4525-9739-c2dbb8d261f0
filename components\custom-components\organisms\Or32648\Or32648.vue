<script setup lang="ts">
/**
 * Or32648:有機体:制度利用状況
 * ［アセスメント］画面（居宅）（３）
 *
 * @description
 * ページタイトルを表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { onMounted, reactive, watch, nextTick } from 'vue'
import { cloneDeep } from 'lodash'
import { Or32648Const } from './Or32648.constants'
import { useNuxtApp } from '#app'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Or32648OnewayType, Or32648Type } from '~/types/cmn/business/components/Or32648Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import { CustomClass } from '~/types/CustomClassType'
import { useScreenStore, useScreenTwoWayBind } from '#imports'
const $log = useNuxtApp().$log as DebugLogPluginInterface
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  modelValue: Or32648Type
  onewayModelValue: Or32648OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/** One-way */
const localOneway = reactive({
  // 年金ラベル
  pensionLabel: '',
  // 制度利用ﾒﾓ
  systemUseOneway: {
    showItemLabel: false,
    maxlength: '18',
    width: '198px',
    isVerticalLabel: false,
    customClass: new CustomClass({ outerStyle: 'align-content:center' }),
  } as Mo00045OnewayType,
  // 成年後見人等
  adultObserverOnewayType: {
    itemLabel: '',
    width: '307px',
    maxlength: '24',
    showItemLabel: true,
    isVerticalLabel: true,
    /** カスタムクラス */
    customClass: new CustomClass({
      labelClass: 'pr-3',
      labelStyle: 'line-height:26px',
    }),
  } as Mo00045OnewayType,
  // その他または老人保健事業の区分 'other':その他  'elder':老人保健事業
  itemKbn: '',
  // 健康保険ラベル
  healthInsuranceLable: '',
  // その他ラベル
  otherLable: '',
  // 老人保健事業ラベル
  elderlyHealthProjectLabel: '',
  // 制度利用ﾒﾓ(労災保険)
  systemUseOneway01: {
    itemLabel: '',
    maxlength: '32',
    width: '198px',
    isVerticalLabel: false,
  } as Mo00045OnewayType,
  // 制度利用ﾒﾓ(その他)
  systemUseOneway02: {
    showItemLabel: false,
    maxlength: '32',
    width: '266px',
    isVerticalLabel: false,
    /** カスタムクラス */
    customClass: new CustomClass({
      outerClass: 'mr-0',
    }),
  },
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or32648Type>({
  cpId: Or32648Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  $log.debug(`★[onMounted] [cpId]${Or32648Const.CP_ID(0)} [uId]${props.uniqueCpId}`)
})

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    refValue.value = cloneDeep(newValue)

    if (!newValue.refValueInitDisabled) {
      // RefValue初期化
      useScreenStore().setCpTwoWay({
        cpId: Or32648Const.CP_ID(0),
        uniqueCpId: props.uniqueCpId,
        value: refValue.value,
        isInit: true,
      })
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.pensionLabel = newValue?.pensionLabel ?? ''
    localOneway.adultObserverOnewayType.itemLabel = newValue?.textLabel ?? ''
    localOneway.itemKbn = newValue?.itemKbn ?? ''
    localOneway.healthInsuranceLable = newValue?.healthInsuranceLable ?? ''
    localOneway.otherLable = newValue.otherLable
    localOneway.elderlyHealthProjectLabel = newValue.elderlyHealthProjectLabel
  },
  { deep: true, immediate: true }
)

/**************************************************
 * 関数
 **************************************************/
/**
 * radio制御
 */
async function changeRadio() {
  await nextTick()
  if (!refValue.value!.adultObserverSystemItem.checkboxValue.modelValue) {
    refValue.value!.adultObserverSystemItem.raidoValue = '1'
  } else {
    refValue.value!.adultObserverSystemItem.raidoValue = ''
  }
}
/**
 * checkbox制御
 */
async function changeCheckBox() {
  await nextTick()
  if (refValue.value!.adultObserverSystemItem.raidoValue) {
    refValue.value!.adultObserverSystemItem.checkboxValue.modelValue = true
  } else {
    refValue.value!.adultObserverSystemItem.checkboxValue.modelValue = false
  }
}
</script>

<template>
  <c-v-row no-gutters>
    <!-- '制度利用状況' -->
    <div class="c-sub-title system-use-situation">
      <span> {{ t('label.system-use-situation') }}</span>
    </div>
  </c-v-row>
  <div class="or32648Wrapper">
    <c-v-row
      no-gutters
      class="block-area top-area-height ma-0 pb-7"
    >
      <c-v-col
        cols="12"
        class="padding-x padding-t"
      >
        <c-v-row no-gutters>
          <!-- 年金ラベル -->
          <div class="c-sub-title second-level-title pl-3 pr-1">
            {{ localOneway.pensionLabel }}
          </div>
        </c-v-row>
        <c-v-row
          no-gutters
          class="pb-4"
        >
          <c-v-col>
            <!-- 年金各項目 -->
            <c-v-row
              no-gutters
              class="pl-4 d-flex justify-space-between"
            >
              <c-v-col
                v-for="(sub, subIndex) in refValue!.pensionItem.pensionCheckItems"
                :key="subIndex"
                cols="auto"
              >
                <div class="d-flex padding-t">
                  <base-mo00018
                    v-model="sub.checkboxValue"
                    :oneway-model-value="sub.onewayModelValue"
                  />
                  <base-mo00045
                    v-model="refValue!.pensionItem.systemUseTextValue[subIndex]"
                    :oneway-model-value="localOneway.systemUseOneway"
                  />
                </div>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <c-v-divider></c-v-divider>
        <c-v-row
          no-gutters
          class="pt-4"
        >
          <c-v-col class="d-flex flex-wrap pl-4">
            <!-- その他チェックボックス -->
            <div
              v-for="(sub, subIndex) in refValue!.otherCheckBoxItem.otherCheckItems"
              :key="subIndex"
              class="pr-15 pb-4"
            >
              <base-mo00018
                v-model="sub.checkboxValue"
                class=""
                :oneway-model-value="sub.onewayModelValue"
                @click.stop
              />
            </div>
          </c-v-col>
        </c-v-row>
        <div class="d-flex">
          <!-- 成年後見制度 -->
          <div>
            <div class="pl-4">
              <base-mo00018
                v-model="refValue!.adultObserverSystemItem.checkboxValue"
                :oneway-model-value="refValue!.adultObserverSystemItem.checkboxOnewayModelValue"
                @click="changeRadio"
              />
            </div>
            <div class="pl-15">
              <base-mo00039
                v-model="refValue!.adultObserverSystemItem.raidoValue"
                class="adult-observer-radio"
                :oneway-model-value="refValue!.adultObserverSystemItem.radioOneway"
                @click="changeCheckBox"
              />
            </div>
          </div>
          <!-- 成年後見人等 -->
          <div class="pt-1">
            <base-mo00045
              v-model="refValue!.adultObserverInputValue"
              :oneway-model-value="localOneway.adultObserverOnewayType"
            />
          </div>
        </div>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="block-area mid-area-height ma-0 pb-6"
    >
      <c-v-col
        cols="12"
        class="padding-x"
      >
        <c-v-row no-gutters>
          <!-- 健康保険ラベル -->
          <div class="c-sub-title second-level-title pl-3 pr-1">
            {{ localOneway.healthInsuranceLable }}
          </div>
        </c-v-row>
        <c-v-row
          no-gutters
          class="pt-4"
        >
          <c-v-col class="d-flex flex-wrap pl-4">
            <!-- 健康保険チェックボックス -->
            <div
              v-for="(sub, subIndex) in refValue!.healthInsurance.insuranceCheckItems"
              :key="subIndex"
              class="pr-13 pb-4"
            >
              <base-mo00018
                v-model="sub.checkboxValue"
                class=""
                :oneway-model-value="sub.onewayModelValue"
                @click.stop
              />
            </div>
          </c-v-col>
        </c-v-row>
        <c-v-divider></c-v-divider>
        <div class="d-flex pt-5">
          <!-- 労災保険 -->
          <div class="pl-4 d-flex">
            <base-mo00018
              v-model="refValue!.compensationInsurance.insuranceCheckItems.checkboxValue"
              :oneway-model-value="
                refValue!.compensationInsurance.insuranceCheckItems.onewayModelValue
              "
              @click.stop
            />
          </div>
          <div class="pl-6">
            <base-mo00045
              v-model="refValue!.compensationInsurance.systemUseTextValue"
              :oneway-model-value="localOneway.systemUseOneway01"
            />
          </div>
        </div>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="block-area bottom-area-height ma-0 pb-10"
    >
      <c-v-col
        cols="12"
        class="padding-x"
      >
        <c-v-row no-gutters>
          <!-- その他ラベル -->
          <div
            v-if="localOneway.itemKbn === Or32648Const.DEFAULT.OTHER_KBN"
            class="c-sub-title second-level-title pl-3 pr-1"
          >
            {{ localOneway.otherLable }}
          </div>
          <!-- 老人保健事業ラベル -->
          <div
            v-if="localOneway.itemKbn === Or32648Const.DEFAULT.ELDER_KBN"
            class="c-sub-title second-level-title pl-3 pr-1"
          >
            {{ localOneway.elderlyHealthProjectLabel }}
          </div>
        </c-v-row>
        <c-v-row
          no-gutters
          class="pb-4"
        >
          <c-v-col>
            <!-- 各項目 -->
            <c-v-row
              v-if="localOneway.itemKbn === Or32648Const.DEFAULT.OTHER_KBN"
              no-gutters
              class="pl-4 d-flex justify-space-between"
            >
              <!-- その他の場合 -->
              <c-v-col
                v-for="(sub, subIndex) in refValue!.otherItem.otherCheckItems"
                :key="subIndex"
                cols="auto"
              >
                <div class="d-flex padding-t">
                  <base-mo00018
                    v-model="sub.checkboxValue"
                    :oneway-model-value="sub.onewayModelValue"
                  />
                  <base-mo00045
                    v-model="refValue!.otherItem.systemUseTextValue[subIndex]"
                    :oneway-model-value="localOneway.systemUseOneway02"
                  />
                </div>
              </c-v-col>
            </c-v-row>
            <!-- 各項目 -->
            <c-v-row
              v-if="localOneway.itemKbn === Or32648Const.DEFAULT.ELDER_KBN"
              no-gutters
              class="pl-4 d-flex"
            >
              <!-- 老人保健事業の場合 -->
              <div
                v-for="(sub, subIndex) in refValue!.elderlyHealthProject.checkItems"
                :key="subIndex"
                class="pt-4 pr-15"
                no-gutters
              >
                <base-mo00018
                  v-model="sub.checkboxValue"
                  class="flex-0-0"
                  :oneway-model-value="sub.onewayModelValue"
                  @click.stop
                />
              </div>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </div>
</template>

<style scoped lang="scss">
.c-sub-title.system-use-situation {
  font-size: 14px;
  height: 35px;
}
.block-area {
  background-color: rgb(var(--v-theme-surface));

  .c-sub-title.second-level-title {
    font-size: 14px;
    border-radius: 4px;
    height: 48px;
  }

  .padding-x {
    padding-left: 40px;
    padding-right: 40px;
  }
  .padding-t {
    padding-top: 18px;
  }
  :deep(.adult-observer-radio .v-selection-control) {
    height: 24px !important;
    min-height: 24px !important;
  }
}
.justify-space-between {
  justify-content: space-between;
}
.top-area-height {
  height: 372px;
}
.mid-area-height {
  height: 252px;
}
.bottom-area-height {
  height: 142px;
}
</style>
