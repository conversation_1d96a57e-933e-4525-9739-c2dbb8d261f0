<script setup lang="ts">
/**
 * Or29115:有機体:計画書1印刷設定モーダル
 * GUI01003_印刷設定POP画面
 *
 * @description
 * GUI01003_印刷設定POP画面の処理
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or18615Const } from '../Or18615/Or18615.constants'
import { Or18615Logic } from '../Or18615/Or18615.logic'
import { Or26326Const } from '../Or26326/Or26326.constants'
import { Or26328Const } from '../Or26328/Or26328.constants'
import { Or26328Logic } from '../Or26328/Or26328.logic'
import { Or26331Const } from '../Or26331/Or26331.constants'
import { Or26331Logic } from '../Or26331/Or26331.logic'
import { Or28780Const } from '../Or28780/Or28780.constants'
import { Or28780Logic } from '../Or28780/Or28780.logic'
import { OrX0130Const } from '../OrX0130/OrX0130.constants'
import { OrX0130Logic } from '../OrX0130/OrX0130.logic'
import { OrX0143Const } from '../OrX0143/OrX0143.constants'
import { OrX0143Logic } from '../OrX0143/OrX0143.logic'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import type { OrX0143TableData } from '../OrX0143/OrX0143.type'
import { Or31319Const } from '../Or31319/Or31319.constants'
import { Or31319Logic } from '../Or31319/Or31319.logic'
import { Or10016Const } from '../Or10016/Or10016.constants'
import { Or10016Logic } from '../Or10016/Or10016.logic'
import { OrX0135Const } from '../OrX0135/OrX0135.constants'
import { OrX0135Logic } from '../OrX0135/OrX0135.logic'
import { OrX0117Logic } from '../OrX0117/OrX0117.logic'
import { OrX0117Const } from '../OrX0117/OrX0117.constants'
import type { Or29115ScreenType } from './Or29115.type'
import { Or29115Const } from './Or29115.constants'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenUtils,
  usePrint,
  type HistoryInfo,
} from '#imports'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or29115StateType } from '~/types/cmn/business/components/Or29115Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  Plan1PrintSettingsInitUpdateInEntity,
  Plan1PrintSettingsInitUpdateOutEntity,
  KikanRirekiData,
} from '~/repositories/cmn/entities/Plan1PrintSettingsInitUpdateEntity'
import type { Mo01334Items, Mo01334OnewayType } from '~/types/business/components/Mo01334Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Or18615OnewayType, Or18615Type } from '~/types/cmn/business/components/Or18615Type'
import type { Or26326Type } from '~/types/cmn/business/components/Or26326Type'
import type { Or28780Type } from '~/types/cmn/business/components/Or28780Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Or26331OnewayType, Or26331Type } from '~/types/cmn/business/components/Or26331Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { OrX0143OnewayType } from '~/types/cmn/business/components/OrX0143Type'
import type { Or26328OnewayType, Or26328Type } from '~/types/cmn/business/components/Or26328Type'
import type { IPrintInfo, PrintOnewayEntity } from '~/repositories/cmn/entities/PrintSelectEntity'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import type { Plan1InitMasterInfo } from '~/repositories/cmn/entities/Plan1SelectEntity'
import type { Or31319OnewayType } from '~/types/cmn/business/components/Or31319Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import type {
  Plan1PrintSettingsUserChangeSelectInEntity,
  Plan1PrintSettingsUserChangeSelectOutEntity,
} from '~/repositories/cmn/entities/Plan1PrintSettingsUserChangeSelectEntity'
import type { PrintSubjectSelectInEntity } from '~/repositories/cmn/entities/PrintSubjectSelectEntity'
import type {
  IPrtHistoryInfo,
  Plan1PrintSettingsSubjectSelectOutEntity,
} from '~/repositories/cmn/entities/Plan1PrintSettingsSubjectSelectEntity'
import type { Plan1PrintSettingsInfoUpdateInEntity } from '~/repositories/cmn/entities/Plan1PrintSettingsInfoUpdateEntity'
import type { OrX0135OnewayType } from '~/types/cmn/business/components/OrX0135Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import { reportOutputType } from '~/utils/useReportUtils'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'

const { t } = useI18n()
const { setChildCpBinds } = useScreenUtils()

// 印刷共通処理
const printCom = usePrint()
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: PrintOnewayEntity<Plan1InitMasterInfo>
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
// 利用者列幅
const userCols = ref<number>(6)
const localOneway = reactive({
  or29115Oneway: {
    ...props.onewayModelValue,
  } as PrintOnewayEntity<Plan1InitMasterInfo>,
  or26326Oneway: {
    headers: [
      {
        title: t('label.report'),
        key: 'prtTitle',
        sortable: false,
        minWidth: '100',
      },
    ],
    items: [],
    height: 655,
  } as Mo01334OnewayType,
  or26328Oneway: {
    maxLength: '128',
  } as Or26328OnewayType,
  mo00018OneWay1: {
    checkboxLabel: t('label.name-censored'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
  } as Mo00018OnewayType,
  mo00018OneWay2: {
    checkboxLabel: t('label.print-kojinhogo'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
  } as Mo00018OnewayType,
  // 日付印刷
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  // 作成年月日印刷
  or31319Oneway: {
    // 作成年月日印刷区分
    mo00039OneWayPrintDateCreation: {
      name: '',
      showItemLabel: false,
      inline: false,
    } as Mo00039OnewayType,
    // 作成年月日
    mo01338OneWayPrintDateCreation: {
      value: '',
      customClass: {
        labelStyle: 'display: none',
      } as CustomClass,
    } as Mo01338OnewayType,
  } as Or31319OnewayType,
  //印刷オプション
  or18615OneWay: {
    mo00018OneWayChangeTitle: {
      isVerticalLabel: false,
    } as Mo00018OnewayType,
    mo00045OnewayTextInput: {
      isVerticalLabel: false,
      maxLength: '2',
      hideDetails: true,
    } as Mo00045OnewayType,
  } as Or18615OnewayType,
  //利用者選択方法セクション
  or26331OneWay: {
    mo00039OneWayHistorySelectType: {} as Mo00039OnewayType,
    mo00039OneWayUserSelectType: {} as Mo00039OnewayType,
  } as Or26331OnewayType,
  mo00024Oneway: {
    maxWidth: '1420px',
    height: '800px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or29115',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or29115ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
    disabledCloseBtnEvent: true,
  } as Mo00024OnewayType,
  orX0130Oneway: {
    selectMode: Or29115Const.DEFAULT.TANI,
    tableStyle: 'width:270px;',
  } as OrX0130OnewayType,
  orX0143Oneway: {
    // 期間管理フラグ
    kikanFlg: '1',
    // 単一複数フラグ(0:単一,1:複数)
    singleFlg: Or29115Const.DEFAULT.TANI,
    tableStyle: 'width:335px;',
    kikanRirekiTitleList: [
      // 作成日
      { title: 'create-date', width: '93', key: 'createYmd' },
      // 作成者
      { title: 'author', width: '171', key: 'shokuKnj' },
    ],
    // 履歴情報
    rirekiList: [] as OrX0143TableData[],
  } as OrX0143OnewayType,
  // 記入用シートを印刷するチェックボックス
  mo00018OneWayPrintTheForm: {
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param05OneWay: {
    checkboxLabel: t('label.confirm-form-copying'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param06OneWay: {
    checkboxLabel: t('label.printing-frame-height-auto-resize'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param07OneWay: {
    checkboxLabel: t('label.office-short-name-print'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param08OneWay: {
    checkboxLabel: t('label.est-level-of-case-print'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo01338Oneway: {
    value: t('label.printing-in-others-section'),
    customClass: new CustomClass({
      outerClass: 'ml-10 mt-0',
      itemStyle: 'color: grey;fontSize: 12px',
    }),
  } as Mo01338OnewayType,
  // 閉じるボタン設置
  mo00611Oneway: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00611Oneway2: {
    btnLabel: t('btn.seal-column'),
    labelColor: 'rgb(var(--v-theme-key))',
    borderColor: 'rgb(var(--v-theme-key))',
  } as Mo00611OnewayType,
  mo00611Oneway3: {
    btnLabel: t('btn.confirm-form-registration‌'),
  } as Mo00611OnewayType,
  // PDFダウンロードボタン設置
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    //共通処理の印刷権限チェックを行う
    disabled: !(await hasPrintAuth()),
  } as Mo00609OnewayType,
  or10016Oneway: {} as Or10016OnewayType,
  orX0135Oneway: {} as OrX0135OnewayType,
  orX0117Oneway: {
    reportId: '',
    type: '1',
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
})

const local = reactive({
  mo00024: {
    isOpen: Or29115Const.DEFAULT.IS_OPEN,
  },
  or29115: {} as Or29115ScreenType,
  //出力帳票名一覧
  or26326Type: {
    mo01334Type: {},
  } as Or26326Type,
  or26328Type: { titleInput: {} } as Or26328Type,
  or28780Type: { mo00020Type: {} } as Or28780Type,
  or18615Type: {
    mo00018TypeChangeTitle: {} as Mo00018Type,
    mo00045Type: {},
  } as Or18615Type,
  or26331Type: {
    mo00020TypeKijunbi: { value: props.onewayModelValue.sysYmd },
    mo00039OneWayUserSelectType: Or29115Const.DEFAULT.TANI,
    mo00039OneWayHistorySelectType: Or29115Const.DEFAULT.TANI,
  } as Or26331Type,
  amikakeFlg: { modelValue: false } as Mo00018Type,
  kojinhogoFlg: { modelValue: false } as Mo00018Type,
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeParam05: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeParam06: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeParam07: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeParam08: {
    modelValue: false,
  } as Mo00018Type,
  // 作成年月日印刷区分
  mo00039TypePrintDateCreation: '',
  orX0135Type: {},
  orX0145: {} as OrX0145Type,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or29115StateType>({
  cpId: Or29115Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or29115Const.DEFAULT.IS_OPEN
    },
  },
})
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or10016_1 = ref({ uniqueCpId: '' })
const or18615_1 = ref({ uniqueCpId: '' })
const or26326_1 = ref({ uniqueCpId: '' })
const or26328_1 = ref({ uniqueCpId: '' })
const or26331_1 = ref({ uniqueCpId: '' })
const or28780_1 = ref({ uniqueCpId: '' })
const or31319_1 = ref({ uniqueCpId: '' })
const orX0117_1 = ref({ uniqueCpId: '' })
const orX0130_1 = ref({ uniqueCpId: '' })
const orX0135_1 = ref({ uniqueCpId: '' })
const orX0143_1 = ref({ uniqueCpId: '' })
const orX0145_1 = ref({ uniqueCpId: '' })
const or26328Ref = ref<{
  isValid: () => Promise<boolean>
}>()
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or10016Const.CP_ID(1)]: or10016_1.value,
  [Or18615Const.CP_ID(1)]: or18615_1.value,
  [Or26326Const.CP_ID(1)]: or26326_1.value,
  [Or26328Const.CP_ID(1)]: or26328_1.value,
  [Or26331Const.CP_ID(1)]: or26331_1.value,
  [Or28780Const.CP_ID(1)]: or28780_1.value,
  [Or31319Const.CP_ID(1)]: or31319_1.value,
  [OrX0117Const.CP_ID(1)]: orX0117_1.value,
  [OrX0130Const.CP_ID(1)]: orX0130_1.value,
  [OrX0135Const.CP_ID(1)]: orX0135_1.value,
  [OrX0143Const.CP_ID(1)]: orX0143_1.value,
  [OrX0145Const.CP_ID(1)]: orX0145_1.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await initCodes()
  // 初期情報取得
  await getInitDataInfo()
})

/**
 * 利用者選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331_1.value.uniqueCpId)?.mo00039OneWayUserSelectType,
  (newValue) => {
    if (newValue) {
      local.or26331Type.mo00039OneWayUserSelectType = newValue
      if (newValue === Or29115Const.DEFAULT.TANI) {
        // 選択モート
        localOneway.orX0130Oneway.selectMode = Or29115Const.DEFAULT.TANI
        localOneway.orX0130Oneway.tableStyle = 'width:270px;'
        userCols.value = 6
        //画面.記入用シートを印刷するを活性にする。
        localOneway.mo00018OneWayPrintTheForm.disabled = false
      } else if (newValue === Or29115Const.DEFAULT.HUKUSUU) {
        // 選択モート
        localOneway.orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
        localOneway.orX0130Oneway.tableStyle = ''
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
        localOneway.mo00018OneWayPrintTheForm.disabled = true
        userCols.value = 11
      }
    }
  }
)

/**
 * 履歴選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331_1.value.uniqueCpId)?.mo00039OneWayHistorySelectType,
  (newValue) => {
    if (newValue) {
      // 履歴選択方法が「単一」の場合
      if (newValue === Or29115Const.DEFAULT.TANI) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.TANI
        //画面.記入用シートを印刷するを活性にする。
        localOneway.mo00018OneWayPrintTheForm.disabled = false
      } else if (newValue === Or29115Const.DEFAULT.HUKUSUU) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.HUKUSUU
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
        localOneway.mo00018OneWayPrintTheForm.disabled = true
      }
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130_1.value.uniqueCpId),
  async (newValue, oldValue) => {
    if (
      !oldValue?.userList?.at(0)?.userId ||
      newValue?.userList?.at(0)?.userId === oldValue?.userList?.at(0)?.userId
    ) {
      return
    }
    if (newValue?.clickFlg && localOneway.orX0130Oneway.selectMode === Or29115Const.DEFAULT.TANI) {
      // 利用者選択
      // 利用者選択方法が「単一」の場合
      await getHistoricalInfoList(newValue.userList[0].userId)
    }
  }
)

/**
 * 「履歴一覧」明細選択の監視
 */
watch(
  () => OrX0143Logic.event.get(orX0143_1.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    // 画面.利用者選択が「複数」選択、又は、画面.履歴選択が「複数」選択の場合
    if (
      localOneway.orX0143Oneway.singleFlg === OrX0143Const.DEFAULT.HUKUSUU ||
      localOneway.orX0130Oneway.selectMode === OrX0130Const.DEFAULT.HUKUSUU
    ) {
      // 空を設定
      localOneway.or31319Oneway.mo01338OneWayPrintDateCreation.value = ''
    }
    // 以外の場合
    else {
      // 画面.履歴一覧.選択行.作成日を設定
      localOneway.or31319Oneway.mo01338OneWayPrintDateCreation.value =
        newValue.orX0143DetList.at(0)?.createYmd ?? ''
    }
  }
)

/**
 * 「記入用シートを印刷するチェックボックス」変更
 */
watch(
  () => local.mo00018TypePrintTheForm.modelValue,
  (newValue) => {
    localOneway.param06OneWay.disabled = newValue
    localOneway.param07OneWay.disabled = newValue
    //チェックONの場合
    if (newValue) {
      //下記画面項目を非活性とする
      local.mo00018TypeParam06.modelValue = false
      local.mo00018TypeParam07.modelValue = false
    }
  }
)

/**
 * 担当ケアマネ選択の監視
 *
 * @param newValue - 選択値
 */
function handleChangeTantou(newValue: OrX0145Type) {
  if (newValue.value) {
    // 画面.担当ケアマネIDに戻り値.担当ケアマネIDを設定する
    localOneway.orX0130Oneway.tantouCareManager = (newValue.value as TantoCmnShokuin).shokuinKnj
  }
}

// ダイアログ表示フラグ
const showDialogOr10016 = computed(() => {
  // Or10016のダイアログ開閉状態
  return Or10016Logic.state.get(or10016_1.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117_1.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOrX0135 = computed(() => {
  // OrX0135のダイアログ開閉状態
  return OrX0135Logic.state.get(orX0135_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 印刷設定情報設定
 */
const setPrintData = () => {
  const selectedRow = local.or29115.prtList.find(
    (x) => x.prtNo === local.or26326Type.mo01334Type.value
  )
  //印刷設定情報リスト.帳票名
  local.or26326Type.mo01334Type.value = selectedRow?.prtNo ?? ''
  //印刷設定情報リストから選択行.帳票タイトル
  local.or26328Type.titleInput.value = selectedRow?.prtTitle ?? ''
  //氏名等を伏字にするチェックボックス
  local.amikakeFlg.modelValue = local.or29115.sysIniInfoList.at(0)?.amikakeFlg === '1'
  //個人情報を印刷しないチェックボックス
  local.kojinhogoFlg.modelValue = local.or29115.sysIniInfoList.at(0)?.kojinhogoFlg === '1'
  //印刷設定情報リストから選択行.日付表示有無
  local.or28780Type.mo00039Type = selectedRow?.prnDate ?? ''
  //システム日付
  local.or28780Type.mo00020Type.value = props.onewayModelValue.sysYmd
  //印刷設定情報リスト.パラメータ09
  local.mo00039TypePrintDateCreation = selectedRow?.param09 ?? ''
  //印刷設定情報リストから選択行.パラメータ03
  local.or18615Type.mo00018TypeChangeTitle.modelValue = selectedRow?.param03 === '1'
  //印刷設定情報リストから選択行.パラメータ04
  local.or18615Type.mo00045Type.value = selectedRow?.param04 ?? ''
  //印刷設定情報リストから選択行.パラメータ05
  local.mo00018TypeParam05.modelValue = selectedRow?.param05 === '1'
  //印刷設定情報リスト.パラメータ06
  local.mo00018TypeParam06.modelValue = selectedRow?.param06 === '1'
  //印刷設定情報リストから選択行.パラメータ07
  local.mo00018TypeParam07.modelValue = selectedRow?.param07 === '1'
  //印刷設定情報リストから選択行.パラメータ08
  local.mo00018TypeParam08.modelValue = selectedRow?.param08 === '1'
  //「単一」を選択
  local.or26331Type.mo00039OneWayUserSelectType = Or29115Const.DEFAULT.TANI
  local.or26331Type.mo00039OneWayHistorySelectType = Or29115Const.DEFAULT.TANI
  setChildCpBinds(props.uniqueCpId, {
    //出力帳票名一覧
    [Or26326Const.CP_ID(1)]: {
      twoWayValue: {
        mo01334Type: local.or26326Type.mo01334Type,
      },
    },
    //帳票タイトル
    [Or26328Const.CP_ID(1)]: {
      twoWayValue: {
        titleInput: local.or26328Type.titleInput,
      },
    },
    //日付印刷
    [Or28780Const.CP_ID(1)]: {
      twoWayValue: {
        mo00039Type: local.or28780Type.mo00039Type,
        mo00020Type: local.or28780Type.mo00020Type,
      },
    },
    //印刷オプション
    [Or18615Const.CP_ID(1)]: {
      twoWayValue: {
        mo00018TypeChangeTitle: local.or18615Type.mo00018TypeChangeTitle,
        mo00045Type: local.or18615Type.mo00045Type,
      },
    },
    //利用者選択
    [Or26331Const.CP_ID(1)]: {
      twoWayValue: {
        mo00039OneWayUserSelectType: local.or26331Type.mo00039OneWayUserSelectType,
        mo00020TypeKijunbi: local.or26331Type.mo00020TypeKijunbi,
        mo00039OneWayHistorySelectType: local.or26331Type.mo00039OneWayHistorySelectType,
      },
    },
    //作成年月日印刷
    [Or31319Const.CP_ID(1)]: {
      twoWayValue: {
        mo00039TypePrintDateCreation: local.mo00039TypePrintDateCreation,
      },
    },
  })
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({
    selectCodeKbnList: [
      {
        mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY,
        addBlank: false,
        targetDate: props.onewayModelValue.sysYmd,
      },
      {
        mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY,
        addBlank: false,
        targetDate: props.onewayModelValue.sysYmd,
      },
      {
        mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE,
        addBlank: false,
        targetDate: props.onewayModelValue.sysYmd,
      },
    ],
  })

  // コード取得
  // 日付印刷区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  // 作成年月日印刷区分
  localOneway.or31319Oneway.mo00039OneWayPrintDateCreation.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE
  )
  //単複数選択区分
  localOneway.or26331OneWay.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  //単複数選択区分
  localOneway.or26331OneWay.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
}

/**
 * 初期情報取得
 */
const getInitDataInfo = async () => {
  const inputData: Plan1PrintSettingsInitUpdateInEntity = {
    // セクション名
    sectionName: localOneway.or29115Oneway.sectionName,
    // 職員ID
    shokuId: localOneway.or29115Oneway.shokuId,
    // 法人ID
    houjinId: localOneway.or29115Oneway.houjinId,
    //施設ID
    shisetuId: localOneway.or29115Oneway.shisetuId,
    // 事業者ID
    svJigyoId: localOneway.or29115Oneway.svJigyoId,
    // システムコード
    gsyscd: localOneway.or29115Oneway.systemCode,
    // 利用者ID
    userId: localOneway.or29115Oneway.userId,
    cksFlg: localOneway.or29115Oneway.initMasterObj.cksFlg,
  }
  // 印刷設定画面初期情報を取得する。
  const ret: Plan1PrintSettingsInitUpdateOutEntity = await printCom.doPrintGet<
    Plan1PrintSettingsInitUpdateInEntity,
    IPrintInfo,
    KikanRirekiData,
    Plan1PrintSettingsInitUpdateOutEntity
  >(inputData, 'plan1PrintSettingsInitUpdate')
  // 印刷設定情報リスト、期間履歴情報リスト、期間管理フラグを取得
  printCom.doGetPrintData<Plan1PrintSettingsInitUpdateOutEntity, IPrintInfo, KikanRirekiData>(
    ret,
    local.or29115
  )
  local.or29115.sysIniInfoList = ret.data.sysIniInfoList
  //印刷設定情報に一件目の印刷設定情報リストを設定する。
  local.or26326Type.mo01334Type.value = ret.data.prtList.at(0)?.prtNo ?? ''
  if (local.or29115.prtList) {
    localOneway.or26326Oneway.items = local.or29115.prtList.map((item) => {
      return {
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.defPrtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items
    })
    setPrintData()
  }
  localOneway.orX0143Oneway.kikanFlg = props.onewayModelValue.kikanFlg
  //履歴一覧情報を表示する。
  localOneway.orX0143Oneway.rirekiList = ret.data.kikanRirekiList
  //画面.履歴一覧に親画面.履歴ID対応するレコードを選択する
  localOneway.orX0143Oneway.rirekiId = props.onewayModelValue.rirekiId
}

/**
 * 履歴情報を取得する
 *
 * @param userId - 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  localOneway.orX0143Oneway.rirekiList = []
  // バックエンドAPIから初期情報取得
  const kikanRirekiList = await printCom.doUserClick<
    Plan1PrintSettingsUserChangeSelectInEntity,
    KikanRirekiData,
    Plan1PrintSettingsUserChangeSelectOutEntity
  >(
    {
      // 事業所ＩＤ:親画面.事業所ＩＤ
      svJigyoId: props.onewayModelValue.svJigyoId,
      // 利用者ID:画面.利用者一覧に選択行の利用者ID
      userId: userId,
      //初期取得.期間管理フラグ
      kikanFlg: props.onewayModelValue.kikanFlg,
    },
    'plan1PrintSettingsUserChangeSelect'
  )
  //履歴一覧情報を表示する。
  localOneway.orX0143Oneway.rirekiList = kikanRirekiList
}

/**
 * エラーダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 *
 * @param isError - エラーかどうか
 *
 * @returns ダイアログの選択結果（yes）
 */
const showMessageBox = async (text: string, btn: string, isError = true) => {
  if (isError) {
    return await openErrorDialog(text, btn)
  } else {
    return await openInfoDialog(text)
  }
}

/**
 * ダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 */
function openErrorDialog(text: string, btn: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t(text),
      firstBtnType: 'normal1',
      firstBtnLabel: t(btn) ?? t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813_1.value.uniqueCpId)

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(event?.firstBtnClickFlg ? Or29115Const.YES : undefined)
      },
      { once: true }
    )
  })
}
/**
 * ダイアログの開閉
 *
 * @param text - メッセージ
 */
function openInfoDialog(text: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t(text),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(event?.firstBtnClickFlg ? Or29115Const.YES : undefined)
      },
      { once: true }
    )
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
async function print() {
  //利用者一覧
  const userList = OrX0130Logic.event.get(orX0130_1.value.uniqueCpId)?.userList
  //履歴一覧
  const rirekiList = OrX0143Logic.event.get(orX0143_1.value.uniqueCpId)?.orX0143DetList
  //業務チェックを行う。
  if (
    !printCom.doCheckBeforePrint(
      Or26328Logic.data.get(or26328_1.value.uniqueCpId)?.titleInput.value ?? '',
      local.mo00018TypePrintTheForm.modelValue,
      userList?.length ?? 0,
      rirekiList?.length ?? 0,
      showMessageBox
    )
  ) {
    return
  }
  const resSubjectData = await printCom.doRetryRirekiData<
    PrintSubjectSelectInEntity,
    IPrtHistoryInfo,
    Plan1PrintSettingsSubjectSelectOutEntity
  >(
    {
      // 利用者リスト：画面.利用者一覧で選択した利用者リスト
      userList: userList!.map((x) => {
        return {
          userId: x.selfId,
          userName: x.nameKnj,
        }
      }),
      // 事業所ID：親画面.事業所ID
      svJigyoId: props.onewayModelValue.svJigyoId,
      // 基準日：画面.基準日
      kijunbiYmd: Or26331Logic.data.get(or26331_1.value.uniqueCpId)?.mo00020TypeKijunbi.value ?? '',
    },
    localOneway.orX0130Oneway.selectMode,
    'plan1PrintSettingsSubjectSelect'
  )
  //画面の印刷設定情報を保存する。
  if (!(await save())) {
    return
  }
  // 業務共通化処理の設定
  const selectedPrtData = local.or29115.prtList.find(
    (x) => x.prtNo === local.or26326Type.mo01334Type.value
  )
  if (selectedPrtData) {
    // 利用者選択が「単一」、且つ、履歴選択が「単一」の場合
    if (
      localOneway.orX0130Oneway.selectMode === Or29115Const.DEFAULT.TANI &&
      localOneway.orX0143Oneway.singleFlg === Or29115Const.DEFAULT.TANI
    ) {
      void printCom.doReportOutput(
        selectedPrtData,
        {
          userId: userList![0].selfId,
          rirekiId: rirekiList![0].rirekiId,
        },
        {
          // 初期設定マスタの情報
          initMasterObj: props.onewayModelValue.initMasterObj,
          // 事業者名
          jigyoKnj: props.onewayModelValue.jigyoKnj,
        },
        {
          // 記入用シートを印刷するフラグ
          emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
          // 指定日
          selectDate: Or28780Logic.data.get(or28780_1.value.uniqueCpId)?.mo00020Type.value ?? '',
          //職員ID
          shokuinId: props.onewayModelValue.shokuId,
        },
        // システム日付
        props.onewayModelValue.sysYmd
      )
    } else {
      const historyData: HistoryInfo[] = []
      if (localOneway.orX0130Oneway.selectMode === Or29115Const.DEFAULT.TANI) {
        rirekiList!.forEach((x) => {
          historyData.push({
            userId: userList![0].selfId,
            rirekiId: x.rirekiId,
          })
        })
      } else {
        resSubjectData.forEach((x) => {
          historyData.push({
            userId: x.userId,
            rirekiId: x.rirekiId,
          })
        })
      }
      // 画面.印刷対象履歴リストを作成する
      printCom.doHukusuuSetting(
        selectedPrtData,
        historyData,
        localOneway.orX0117Oneway,
        localOneway.orX0130Oneway.selectMode,
        userList!,
        {
          // 初期設定マスタの情報
          initMasterObj: props.onewayModelValue.initMasterObj,
          // 事業者名
          jigyoKnj: props.onewayModelValue.jigyoKnj,
        },
        {
          // 指定日
          // 印刷設定情報リスト選択行.日付表示有無が2の場合、画面の指定日
          selectDate:
            selectedPrtData.prnDate === '2'
              ? (Or28780Logic.data.get(or28780_1.value.uniqueCpId)?.mo00020Type.value ?? '')
              : '',
          // 記入用シートを印刷するフラグ
          emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
          //職員ID
          shokuinId: props.onewayModelValue.shokuId,
        },
        // システム日付
        props.onewayModelValue.sysYmd,
        resSubjectData,
        rirekiList!
      )
      // PDFダウンロードを行う
      OrX0117Logic.state.set({
        uniqueCpId: orX0117_1.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
    }
  }
}

/**
 * 「タイトルテキストフィールド」変更
 */
const handleChangeTitle = async () => {
  await checkTitleInput()
}

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  const titleInput = Or26328Logic.data.get(or26328_1.value.uniqueCpId)?.titleInput
  if (titleInput?.value) return
  await openErrorDialog('message.e-cmn-20845', 'btn.ok')
  // 変更前の帳票タイトルに戻す。
  Or26328Logic.data.set({
    uniqueCpId: or26328_1.value.uniqueCpId,
    value: local.or26328Type,
  })
}

/**
 * 印刷設定情報に選択した印刷設定情報リストを設定する。
 */
const setInputValue = () => {
  if (!local.or29115.sysIniInfoList) {
    local.or29115.sysIniInfoList = [{}]
  }
  const sysIniInfo = local.or29115.sysIniInfoList[0]
  //氏名等を伏字にするチェックボックス
  sysIniInfo.amikakeFlg = local.amikakeFlg.modelValue ? '1' : '0'
  //個人情報を印刷しないチェックボックス
  sysIniInfo.kojinhogoFlg = local.kojinhogoFlg.modelValue ? '1' : '0'
  const printData = local.or29115.prtList.at(0)
  if (printData) {
    //帳票タイトル
    printData.prtTitle = Or26328Logic.data.get(or26328_1.value.uniqueCpId)?.titleInput.value ?? ''
    //日付表示有無
    printData.prnDate = Or28780Logic.data.get(or28780_1.value.uniqueCpId)?.mo00039Type ?? ''
    //パラメータ03
    printData.param03 = Or18615Logic.data.get(or18615_1.value.uniqueCpId)?.mo00018TypeChangeTitle
      .modelValue
      ? '1'
      : '0'
    //パラメータ04
    printData.param04 = Or18615Logic.data.get(or18615_1.value.uniqueCpId)?.mo00045Type.value ?? ''
    //パラメータ05
    printData.param05 = local.mo00018TypeParam05.modelValue ? '1' : '0'
    //パラメータ06
    printData.param06 = local.mo00018TypeParam06.modelValue ? '1' : '0'
    //パラメータ07
    printData.param07 = local.mo00018TypeParam07.modelValue ? '1' : '2'
    //パラメータ08
    printData.param08 = local.mo00018TypeParam08.modelValue ? '1' : '0'
    //パラメータ09
    printData.param09 =
      Or31319Logic.data.get(or31319_1.value.uniqueCpId)?.mo00039TypePrintDateCreation ?? ''
  }
}

/**
 * 画面印刷設定内容を保存
 */
const save = async () => {
  if (!(await or26328Ref.value?.isValid())) {
    return false
  }
  setInputValue()
  const inputData: Plan1PrintSettingsInfoUpdateInEntity = {
    // セクション名:親画面.セクション名
    sectionName: props.onewayModelValue.sectionName,
    // システムコード：親画面.システムコード
    gsyscd: props.onewayModelValue.systemCode,
    // 職員ID：親画面.職員ID
    shokuId: props.onewayModelValue.shokuId,
    // 法人ID：親画面.法人ID
    houjinId: props.onewayModelValue.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: props.onewayModelValue.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: props.onewayModelValue.svJigyoId,
    // 印刷設定情報リスト：印刷設定情報リスト
    prtList: local.or29115.prtList,
    //システムINI情報リスト
    sysIniInfoList: local.or29115.sysIniInfoList,
  }
  await printCom.doPrintUpdate(inputData, 'plan1PrintSettingsInfoUpdate', showMessageBox)
  return true
}

/**
 * 「GUI01110_印鑑欄設定」画面をポップアップ表示する。
 */
const openGUI01110 = () => {
  // 法人ID:親画面.法人ID
  localOneway.or10016Oneway.houjinId = props.onewayModelValue.houjinId
  // 施設ID:親画面.施設ID
  localOneway.or10016Oneway.shisetsuId = props.onewayModelValue.shisetuId
  // 事業所ID:親画面.事業所ID
  localOneway.or10016Oneway.jigyoshoId = props.onewayModelValue.svJigyoId
  // 帳票セクション番号:"3GKU0081"
  localOneway.or10016Oneway.reportSectionNumber = '3GKU0081'
  // 会議録フラグ:FALSE
  localOneway.or10016Oneway.conferenceFlag = false
  // ケアプラン方式:親画面.初期設定マスタの情報.ケアプラン方式
  localOneway.or10016Oneway.assessment = props.onewayModelValue.initMasterObj.cpnFlg

  Or10016Logic.state.set({
    uniqueCpId: or10016_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00617_承認欄登録画面をポップアップで起動する。
 */
const openGUI00617 = () => {
  // 承認欄情報：親画面.承認欄情報（0：共有する、1：帳票毎保持する）TODO
  // 事業所ID：親画面.事業所ID
  localOneway.orX0135Oneway.svJigyoId = props.onewayModelValue.svJigyoId
  // セクション：印刷設定情報リスト.プロファイル
  // 法人ID：親画面.法人ID
  localOneway.orX0135Oneway.houjinId = props.onewayModelValue.houjinId
  // 施設ID：親画面.施設ID
  localOneway.orX0135Oneway.shisetuId = props.onewayModelValue.shisetuId

  OrX0135Logic.state.set({
    uniqueCpId: orX0135_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 閉じるボタン押下時
 */
const close = async (): Promise<void> => {
  if (!(await or26328Ref.value?.isValid())) {
    setState({ isOpen: false })
    return
  }
  setInputValue()
  // 画面の印刷設定情報を保存する
  await printCom.doPrintClose(
    Or26328Logic.data.get(or26328_1.value.uniqueCpId)?.titleInput,
    {
      // セクション名:親画面.セクション名
      sectionName: props.onewayModelValue.sectionName,
      // システムコード：親画面.システムコード
      gsyscd: props.onewayModelValue.systemCode,
      // 職員ID：親画面.職員ID
      shokuId: props.onewayModelValue.shokuId,
      // 法人ID：親画面.法人ID
      houjinId: props.onewayModelValue.houjinId,
      // 施設ID：親画面.施設ID
      shisetuId: props.onewayModelValue.shisetuId,
      // 事業者ID：親画面.事業者ID
      svJigyoId: props.onewayModelValue.svJigyoId,
      // 印刷設定情報リスト：印刷設定情報リスト
      prtList: local.or29115.prtList,
      //システムINI情報リスト
      sysIniInfoList: local.or29115.sysIniInfoList,
    },
    'plan1PrintSettingsInfoUpdate',
    localOneway.or26326Oneway.items,
    local.or26326Type.mo01334Type.value,
    props.uniqueCpId,
    showMessageBox,
    () => setState({ isOpen: false })
  )
}

/**
 * 閉じる処理
 *
 * @param newValue - ダイアログ
 */
async function closeBtnClick(newValue: Mo00024Type) {
  if (newValue.emitType === 'closeBtnClick') {
    await close()
  }
}
</script>

<template>
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
    @update:model-value="closeBtnClick"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or29115_screen"
      >
        <!-- 帳票 -->
        <g-custom-or-26326
          v-bind="or26326_1"
          :oneway-model-value="localOneway.or26326Oneway"
        />
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 content_center"
        >
          <base-mo00611
            class="ml-2 my-2 border-blue"
            :oneway-model-value="localOneway.mo00611Oneway2"
            @click.stop="openGUI01110"
          />
          <c-v-divider class="my-0" />
          <!-- タイトル -->
          <g-custom-or-26328
            ref="or26328Ref"
            v-bind="or26328_1"
            :oneway-model-value="localOneway.or26328Oneway"
            @update:model-value="handleChangeTitle"
          />
          <c-v-col
            cols="12"
            sm="12"
            class="pa-2"
          >
            <!--氏名等を伏字にするチェックボックス-->
            <base-mo00018
              v-if="local.or29115.sysIniInfoList?.at(0)?.amikakeDisplayFlg === '1'"
              v-model="local.amikakeFlg"
              :oneway-model-value="localOneway.mo00018OneWay1"
            />
            <!--個人情報を印刷しないチェックボックス-->
            <base-mo00018
              v-if="local.or29115.sysIniInfoList?.at(0)?.kojinhogoDisplayFlg === '1'"
              v-model="local.kojinhogoFlg"
              :oneway-model-value="localOneway.mo00018OneWay2"
            />
          </c-v-col>
          <c-v-divider class="my-0" />
          <!-- 日付印刷 -->
          <g-custom-or-28780
            v-bind="or28780_1"
            :oneway-model-value="localOneway.mo00039OneWay"
          />
          <c-v-divider class="my-0" />
          <!-- 作成年月日印刷 -->
          <g-custom-or-31319
            v-bind="or31319_1"
            :oneway-model-value="localOneway.or31319Oneway"
          />
          <c-v-divider class="my-0"></c-v-divider>
          <!-- 印刷オプション -->
          <g-custom-or-18615
            v-bind="or18615_1"
            :oneway-model-value="localOneway.or18615OneWay"
          >
            <template #optionPrintItems>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!--印刷枠の高さを自動調整するチェックボックス-->
                <base-mo00018
                  v-model="local.mo00018TypeParam06"
                  :oneway-model-value="localOneway.param06OneWay"
                />
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <div class="d-flex">
                  <!--承認欄を印刷する-->
                  <base-mo00018
                    v-model="local.mo00018TypeParam05"
                    :oneway-model-value="localOneway.param05OneWay"
                  />
                  <base-mo00611
                    :oneway-model-value="localOneway.mo00611Oneway3"
                    @click.stop="openGUI00617"
                  />
                </div>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!--記入用シートを印刷するチェックボックス-->
                <base-mo00018
                  v-model="local.mo00018TypePrintTheForm"
                  :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
                />
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!--事業名略称を印刷するチェックボックス-->
                <base-mo00018
                  v-model="local.mo00018TypeParam07"
                  :oneway-model-value="localOneway.param07OneWay"
                />
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!--要支援、事業対象者等の介護度を印刷するチェックボックス -->
                <base-mo00018
                  v-model="local.mo00018TypeParam08"
                  :oneway-model-value="localOneway.param08OneWay"
                />
                <base-mo01338
                  v-if="props.onewayModelValue.initMasterObj.cksFlg !== '2'"
                  class="ml-6"
                  :oneway-model-value="localOneway.mo01338Oneway"
                />
              </c-v-col>
            </template>
          </g-custom-or-18615>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
        >
          <c-v-row
            no-gutter
            class="or29115_row align-center"
          >
            <!-- 利用者選択 -->
            <g-custom-or-26331
              v-bind="or26331_1"
              :oneway-model-value="localOneway.or26331OneWay"
            />
            <!--担当ケアマネ選択-->
            <g-custom-or-x-0145
              v-bind="orX0145_1"
              v-model="local.orX0145"
              :oneway-model-value="localOneway.orX0145Oneway"
              @update:model-value="handleChangeTantou"
            />
          </c-v-row>
          <c-v-row
            no-gutter
            class="or29115_row grid-width"
          >
            <c-v-col
              :cols="userCols"
              style="overflow-x: auto"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="localOneway.orX0130Oneway.selectMode"
                v-bind="orX0130_1"
                :oneway-model-value="localOneway.orX0130Oneway"
              />
            </c-v-col>
            <c-v-col
              v-if="local.or26331Type.mo00039OneWayUserSelectType === Or29115Const.DEFAULT.TANI"
              cols="6"
              sm="6"
              class="pl-2 pr-0"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0143
                v-bind="orX0143_1"
                :oneway-model-value="localOneway.orX0143Oneway"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611Oneway"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mr-2"
          @click="print"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813 v-bind="or21813_1" />
  <!-- 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117_1"
    :oneway-model-value="localOneway.orX0117Oneway"
  />
  <!--「GUI01110_印鑑欄設定」画面-->
  <g-custom-or-10016
    v-if="showDialogOr10016"
    v-bind="or10016_1"
    :oneway-model-value="localOneway.or10016Oneway"
  />
  <!--GUI00617_承認欄登録画面-->
  <g-custom-or-x0135
    v-if="showDialogOrX0135"
    v-bind="orX0135_1"
    v-model="local.orX0135Type"
    :oneway-model-value="localOneway.orX0135Oneway"
    :parent-unique-cp-id="props.uniqueCpId"
  />
</template>

<style scoped lang="scss">
.or29115_screen {
  margin: -8px !important;
}

.or29115_row {
  margin: 0px !important;
}
.border-blue {
  border-color: rgb(var(--v-theme-key));
}
.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.grid-width {
  min-width: 694px;
}
</style>
