<script setup lang="ts">
/**
 * Or35052:有機体:印刷設定モーダル
 * GUI01211_印刷設定画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import type {
  Or35052StateType,
  Or35052TwoWayData,
  CheckboxType,
  userList,
  jigyouInfoList,
  svJigyoIdList,
  kHokenList,
  userIdList,
} from './Or35052.type'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenStore,
  useScreenTwoWayBind,
} from '#imports'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or35052OnewayType } from '~/types/cmn/business/components/Or35052Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or35052Const } from '~/components/custom-components/organisms/Or35052/Or35052.constants'

import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Headers,
} from '~/types/business/components/Mo01334Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  LedgerInitializeDataComSelectOutEntity,
  LedgerInitializeDataComSelectInEntity,
} from '~/repositories/cmn/entities/ledgerInitializeDataComSelect'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { InWebEntity } from '@/repositories/AbstructWebRepository'
import { reportOutputType, useReportUtils } from '~/utils/useReportUtils'
import { Or26315Logic } from '~/components/custom-components/organisms/Or26315/Or26315.logic'
import { Or26315Const } from '~/components/custom-components/organisms/Or26315/Or26315.constants'
import type { Or26315OneWayType } from '~/types/cmn/business/components/Or26315Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { convHiraganaToHalfKanaMap } from '~/constants/KanaMap'
import type {
  printSettingsScreenInitialInfoSelectGUI01211InEntity,
  printSettingsScreenInitialInfoSelectGUI01211OutEntity,
  choPrtList,
} from '~/repositories/cmn/entities/printSettingsScreenInitialInfoSelectGUI01211'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  GetPrinterSettingsListInfoSelectInEntity,
  GetPrinterSettingsListInfoSelectOutEntity,
} from '~/repositories/cmn/entities/getPrinterSettingsListInfoSelect'
import type { PrintWriteSettingsSelectInEntity } from '~/repositories/cmn/entities/printWriteSettingsSelect'
import type { printSettingsInfoUpdateGUI01211InEntity } from '~/repositories/cmn/entities/printSettingsInfoUpdateGUI01211'
import { Or28349Logic } from '~/components/custom-components/organisms/Or28349/Or28349.logic'
import { Or28349Const } from '~/components/custom-components/organisms/Or28349/Or28349.constants'
import type { Or28349OnewayType } from '~/types/cmn/business/components/Or28349Type'
import type { Mo01352OnewayType, Mo01352Type } from '~/types/business/components/Mo01352Type'
// import type { Mo01408OnewayType } from '~/types/business/components/Mo01408Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import type { TeikyoReportEntity } from '~/repositories/cmn/entities/TeikyoReportEntity'
import type { OfficeUserListReportInEntity } from '~/repositories/cmn/entities/OfficeUserListReportEntity'
import type { ITeikyoSoufuReportSelectEntity } from '~/repositories/cmn/TeikyoSoufuReportSelectEntity'
import type { TeikyohyoReportEntity } from '~/repositories/cmn/entities/TeikyohyoReportEntity'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { useCmnCom } from '@/utils/useCmnCom'
import { useValidation } from '@/utils/useValidation'
const { byteLength } = useValidation()

const { t } = useI18n()
const { reportOutput } = useReportUtils()
// route共有情報
const cmnRouteCom = useCmnRouteCom()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or35052OnewayType
  uniqueCpId: string
  parentCpId: string
}

const props = defineProps<Props>()

// 引継情報を取得する

const or21813 = ref({ uniqueCpId: '' })

const or21814 = ref({ uniqueCpId: '' })

const or21815 = ref({ uniqueCpId: '' })

const or00094 = ref({ uniqueCpId: '' })
// 50音選択情報
const or00094SelectInfo = ref<string[]>([])
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or26315 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const or28349 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })

// プロファイル
const choPro = ref('')
// プリントナンバー
const prtNo = ref('')

// 個人情報保護設定表示フラグ
const kojinhogoFlg = ref(false)

// 伏字設定表示フラグ
const amikakeFlg = ref(false)

// 文書管理設定表示フラグ
const monjiNoFlg = ref(false)

//画面.出力帳票名
const defPrtTitle = ref('')
//キー
const keyVal = ref('')
//帳票セクション名
const sectionName = ref('')

const userInfoListClone = ref<userList[]>([])

const userInfoList = ref<userList[]>([])

const svJigyoList = ref<svJigyoIdList[]>([])

const userIdList1 = ref<userIdList[]>([])

//事業所選択情報リスト
const jigyouInfo = ref<jigyouInfoList[]>([])

const hokenjaList = ref<kHokenList[]>([])

const focusSettingInitial = ref<string[]>([])

const chropList = ref<string[]>([])

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [Or26315Const.CP_ID(0)]: or26315.value,
  [Or00094Const.CP_ID(0)]: or00094.value,
  [Or28349Const.CP_ID(0)]: or28349.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

// 帳票イニシャライズデータを取得する
const reportInitData = ref([
  {
    // 氏名伏字印刷
    prtName: '',
    // 文書番号印刷
    prtBng: '',
    // 個人情報印刷
    prtKojin: '',
  },
])

// ダイアログ表示フラグ
const showDialogOr26315 = computed(() => {
  // Or26315のダイアログ開閉状態
  return Or26315Logic.state.get(or26315.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr28349 = computed(() => {
  // Or28349のダイアログ開閉状態
  return Or28349Logic.state.get(or28349.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

/**
 * 要介護度区分
 */
let yokaiKbnList: {
  /**
   * value
   */
  value: string
  /**
   * title
   */
  title: string
}[]

// ローカル双方向bind
const local = reactive({
  mo01334TypeUser: {
    value: '',
    values: [],
  } as unknown as Mo01334Type,
  mo01334Type: {
    value: '',
    values: [],
  } as unknown as Mo01334Type,
  mo00040: { modelValue: '' } as Mo00040Type,
  mo00039Type: '',
  mo00039TargetType: '',
  /** 担当ケアマネ */
  mo01408modelCare: {
    value: '',
  },
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  taishoYm: {
    value: '',
  } as Mo01352Type,
  titleInput: {
    value: '',
  } as Mo00045Type,
  mo00018TypeAmikake: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeBunsyokanri: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeKojinhogo: {
    modelValue: false,
  } as Mo00018Type,
  /**
   * 帳票ID
   */
  reportId: Or35052Const.DEFAULT.EMPTY,
  /**
   * 帳票番号
   */
  prtNo: Or35052Const.DEFAULT.EMPTY,
  /**
   * 帳票セクション番号
   */
  sectionNo: Or35052Const.DEFAULT.EMPTY,
  /**
   * 帳票タイトル
   */
  title: Or35052Const.DEFAULT.EMPTY,
})

const headers1 = [
  // 事業所名
  { title: t('label.jigyo_knj'), key: 'jigyoKnj', sortable: true, minWidth: '285px' },
  // 送付状
  { title: t('label.table-cover-letter'), key: 'letter', sortable: false, minWidth: '75px' },
  // FAX番号
  { title: t('label.fax_number'), key: 'faxNumber', sortable: true, minWidth: '156px' },
  // 電話番号
  { title: t('label.telno'), key: 'tel', sortable: true, minWidth: '156px' },
  // ｻｰﾋﾞｽ種類名
  { title: t('label.sevice-type-name'), key: 'ruakuKnj', sortable: true, minWidth: '156px' },
  // 事業所番号
  { title: t('label.plan-business-name'), key: 'jigyoNumber', sortable: true, minWidth: '156px' },
] as Mo01334Headers[]
const headers2 = [
  // 事業所名
  { title: t('label.jigyo_knj'), key: 'jigyoKnj', sortable: true, minWidth: '360px' },
  // FAX番号
  { title: t('label.fax_number'), key: 'faxNumber', sortable: true, minWidth: '156px' },
  // 電話番号
  { title: t('label.telno'), key: 'tel', sortable: true, minWidth: '156px' },
  // ｻｰﾋﾞｽ種類名
  { title: t('label.sevice-type-name'), key: 'ruakuKnj', sortable: true, minWidth: '156px' },
  // 事業所番号
  { title: t('label.plan-business-name'), key: 'jigyoNumber', sortable: true, minWidth: '156px' },
] as Mo01334Headers[]

const localOneway = reactive({
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /** '運用対象ラベル */
  target: {
    value: t('label.application-object'),
  } as Mo01338OnewayType,
  // 利用者一覧
  mo01334OnewayUserHukusuu: {
    headers: [
      // 利用者番号
      { title: '', key: 'selfId', sortable: true },
      // フリガナ
      { title: '', key: 'nameKana', sortable: true },
      // 保険者名
      { title: '', key: 'kHokenCd', sortable: true },
      // 要介護度
      { title: '', key: 'yokaiKbn', sortable: true },
    ] as Mo01334Headers[],
    items: [],
    height: 460,
    showSelect: true,
    selectStrategy: 'all',
    mandatory: false,
  } as Mo01334OnewayType,
  // 事業所一覧セクション
  mo01334Oneway: {
    headers: headers2,
    items: [],
    height: 210,
    showSelect: true,
    selectStrategy: 'all',
    mandatory: false,
  } as Mo01334OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00039OneWayTarget: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: true,
    width: '132px',
  } as Mo00020OnewayType,
  Or35052: {
    ...props.onewayModelValue,
  },
  mo01338OneWayTitle: {
    value: t('label.print-settings-title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayPrinterOption: {
    value: t('label.printer-office-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    // hideDetails: true,
    disabled: false,
    maxLength: '50',
    rules: [byteLength(50)],
  } as Mo00045OnewayType,
  mo00018OneWayAmikake: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.name-censored'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayBunsyokanri: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-bunsyokanri'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayKojinhogo: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-kojinhogo'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayList: [] as CheckboxType[],
  mo01338OneWayDateTitle: {
    value: t('label.label-for-amikake-print'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayCareManagerInCharge: {
    value: t('label.care-manager-in-charge'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo01352Oneway: {
    textFieldwidth: '110px',
  } as Mo01352OnewayType,
  /**
   * 印刷設定帳票出力状態リスト
   */
  orX0117Oneway: {
    reportId: '',
    type: '1',
    reportData: {} as InWebEntity,
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    width: '1300px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  } as Mo00024OnewayType,
  /**
   * 特記事項登録
   */
  mo00611OneWay: {
    btnLabel: t('label.special-remarks-registration'),
    disabled: false,
  } as Mo00611OnewayType,
  /**
   * オプション
   */
  mo00611OneWayCopy: {
    btnLabel: t('btn.option'),
    disabled: false,
  } as Mo00611OnewayType,
  // 利用者合計
  mo01338OnewaySelectedCount: {
    value: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none;',
      itemClass: 'mo01338-footer',
      itemStyle: 'padding-left: 8px',
    } as CustomClass,
  } as Mo01338OnewayType,
  SelectedCount: {
    value: t('label.day-total'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none;',
      itemClass: 'mo01338-footer',
      itemStyle: 'padding-left: 8px',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
  } as OrX0145OnewayType,
})

/**
 * 担当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or35052Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<Or35052StateType>({
  cpId: Or35052Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or35052Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 画面ID
const screenId = 'GUI01211'
// ルーティング
const routing = 'GUI01211/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})
const or28349Data: Or28349OnewayType = {
  //提供事業所id
  svJigyoId: localOneway.Or35052.svJigyoId,
  //利用者ID
  userId: localOneway.Or35052.userId,
  selfId: '',
  userName: '',
  //提供年月
  objectYm: '',
  //自事業所ID
  shienId: localOneway.Or35052.svJigyoId,
  sc1Id: '',
  //サービス事業者IDリスト
  svJigyoIdList: [{ svJigyoId: localOneway.Or35052.shienId }],
  hyouka1Id: '',
  hHokenNo: '',
  userList:[],
}

const or26315Data: Or26315OneWayType = {
  //システムコード
  sysCd: systemCommonsStore.getSystemCode ?? '',
  //職員ID
  shokuId: systemCommonsStore.getStaffId ?? '',
  //法人ID
  houjinId: systemCommonsStore.getHoujinId ?? '',
  //施設ID
  shisetuId: systemCommonsStore.getShisetuId ?? '',
  //事業所ID
  svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
  //印刷日
  printDate: '',
  //選択No
  selectNo: Or35052Const.DEFAULT.ZERO,
  //事業者コード
  svJigyoshaCd: '',
}

/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'defPrtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 855,
})
/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 担当ケアマネ
 */
// const mo01408OnewayCare = reactive({
//   itemLabel: '',
//   showItemLabel: false,
//   width: '200px',
//   items: [],
// }) as Mo01408OnewayType

/**
 * 「担当ケアマネ」の監視
 */
watch(
  () => local.mo01408modelCare.value,
  async () => {
    await getPrintSettingsUserInfo()
  }
)

/**
 * 「送付状」アイコン押下
 */
function editBtnClick() {
  // TODO GUI01142_送付状内容登録をポップアップで起動する。
}

/**
 * 変更データを取得する
 *
 * @returns 出力帳票印刷情報リスト
 */
async function getDataTable() {
  const choPrtList = mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => {
    if (id === mo01334TypeReport.value.value) {
      return {
        ...rest,
        prtTitle: local.titleInput.value,
        prndate: local.mo00039Type,
      }
    }
    return {
      ...rest,
    }
  }) as choPrtList[]

  refValue.value = { choPrtList: choPrtList }

  await nextTick()

  return choPrtList
}

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  if (local.titleInput.value || local.title === local.titleInput.value) return false

  const dialogResult = await showOr21815MsgOneBtn()
  switch (dialogResult) {
    case 'ok': {
      let label = ''
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (mo01334TypeReport.value.value === item.id && item.mo01337OnewayReport) {
            const data = item.mo01337OnewayReport as Mo01337OnewayType
            label = data.value
          }
        }
      }
      local.titleInput.value = label
      break
    }
  }
  return true
}
/**
 * 確認ダイアログの開閉
 *
 * @returns ダイアログの選択結果（OK）
 */
async function showOr21814MsgOneBtn() {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10547'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * 確認ダイアログの開閉
 *
 * @returns ダイアログの選択結果（OK）
 */
async function showOr21814MsgTwoBtn() {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10550'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * 確認ダイアログの開閉
 *
 * @returns ダイアログの選択結果（OK）
 */
async function showOr21814MsgThreeBtn() {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10551'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
// /**
//  * 確認メッセージを表示する
//  *
//  * @param errorMsg - メッセージ内容
//  */
// function showOr21814Msg(errorMsg: string) {
//   Or21814Logic.state.set({
//     uniqueCpId: or21814.value.uniqueCpId,
//     state: {
//       dialogTitle: t('label.confirm'),
//       dialogText: errorMsg,
//       firstBtnType: 'blank',
//       secondBtnType: 'blank',
//       thirdBtnType: 'normal1',
//       thirdBtnLabel: t('btn.yes'),
//     },
//   })
//   Or21814Logic.state.set({
//     uniqueCpId: or21814.value.uniqueCpId,
//     state: {
//       isOpen: true,
//     },
//   })
// }
/**
 * エラーダイアログの開閉
 *
 * @param errorMsg - メッセージ内容
 */
function showOr21813Msg(errorMsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: errorMsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * エラーダイアログの開閉
 *
 * @returns ダイアログの選択結果（ok）
 */
async function showOr21813MsgOneBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログの開閉 利用者が選択されていません
 *
 * @returns ダイアログの選択結果（ok）
 */
async function showOr21813MsgTwoBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40458'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログの開閉
 *
 * @returns ダイアログの選択結果（ok）
 */
async function showOr21813MsgThreeBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40460'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログの開閉 印刷用帳票セクションのデータ全てチェックオフの場合
 *
 * @returns ダイアログの選択結果（ok）
 */
async function showOr21813MsgFourBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40459'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログの開閉
 *
 * @returns ダイアログの選択結果（ok）
 */
async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
const { refValue } = useScreenTwoWayBind<Or35052TwoWayData>({
  cpId: Or35052Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/** 対象人数 */
const targetCount = computed(() => {
  return String(localOneway.mo01334OnewayUserHukusuu.items.length)
})
/** 選択人数 */
const selectedCount = computed(() => {
  return String(local.mo01334TypeUser.values.length)
})
/** 事業所対象人数 */
const targetJigCount = computed(() => {
  return String(localOneway.mo01334Oneway.items.length)
})
/** 事業所選択人数 */
const selectedJigCount = computed(() => {
  return String(local.mo01334Type.values.length)
})

onMounted(async () => {
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  //親画面.電子保存の3原則適用フラグがtrueの場合
  if (localOneway.Or35052.h0ldFlg) {
    setState({ isOpen: false })
  }
  // 50音選択情報初期化
  or00094SelectInfo.value = []
  // 50音ヘッドラインの表示設定ボタンを表示
  Or00094Logic.state.set({
    uniqueCpId: or00094.value.uniqueCpId,
    state: {
      dispSettingBtnDisplayFlg: false,
      focusSettingFlg: true,
      focusSettingInitial: localOneway.Or35052.focusSettingInitial,
    },
  })
  await initCodes()
  await getPrintSettingList()
  openOr00094()
})

/**
 * 50音ヘッドラインの表示設定ボタンを表示
 */
function openOr00094() {
  if (
    localOneway.Or35052.focusSettingInitial &&
    Array.isArray(localOneway.Or35052.focusSettingInitial) &&
    localOneway.Or35052.focusSettingInitial.length > 0
  ) {
    Or00094Logic.state.set({
      uniqueCpId: or00094.value.uniqueCpId,
      state: {
        dispSettingBtnDisplayFlg: false,
        focusSettingFlg: true,
        focusSettingInitial: localOneway.Or35052.focusSettingInitial,
      },
    })
    focusSettingInitial.value = localOneway.Or35052.focusSettingInitial
    calcAppliedFilterUserList(localOneway.Or35052.focusSettingInitial)
  } else {
    Or00094Logic.state.set({
      uniqueCpId: or00094.value.uniqueCpId,
      state: {
        dispSettingBtnDisplayFlg: false,
        focusSettingFlg: true,
        focusSettingInitial: [Or35052Const.STR_ALL],
      },
    })
    focusSettingInitial.value = [Or35052Const.STR_ALL]
  }
}

/**
 * 50音ヘッドラインのフィルター適用後の利用者一覧を算出し、ローカル変数に設定します。
 * また、システム共有領域．利用者選択領域の更新も行います。
 *
 * @param or00094Select - 50音
 */
function calcAppliedFilterUserList(or00094Select: string[]) {
  or00094SelectInfo.value = or00094Select

  userInfoList.value = []

  // システム共有領域．利用者選択領域から、50音ヘッドラインの選択値を取得
  const workFilterInitials = ref([] as string[])
  const workGetUserSelectFilterInitials = or00094Select
  if (workGetUserSelectFilterInitials !== undefined) {
    workFilterInitials.value = workGetUserSelectFilterInitials // readonlyを外すためにasが必要
  }
  if (workFilterInitials.value === undefined || workFilterInitials.value?.length === 0) {
    // 50音ヘッドラインのフィルターが設定値なしの場合、
    // 利用者一覧は全量を設定する
    if (userInfoListClone.value !== undefined) {
      userInfoList.value = userInfoListClone.value
      getUserInfoListClone()
    }
  } else if (
    workFilterInitials.value.length > 0 &&
    workFilterInitials.value[0] === OrX0130Const.DEFAULT.STR_ALL
  ) {
    // 50音ヘッドラインで「全」が選択された場合
    userInfoList.value = userInfoListClone.value
    getUserInfoListClone()
  } else if (
    workFilterInitials.value.length > 0 &&
    workFilterInitials.value[0] === OrX0130Const.DEFAULT.STR_OTHER
  ) {
    // 50音ヘッドラインで「他」が選択された場合
    const workUserList = userInfoListClone.value.filter((workUser) => {
      // 読み仮名の1文字目がひらがなではなければtrue
      const regex = /^[ぁ-ん]+$/u
      if (!regex.test(workUser.nameKana.charAt(0))) {
        return true
      }
    })

    // ヒットしたユーザーを"50音ヘッドラインのフィルター適用後の利用者一覧"に追加
    workUserList.forEach((workUser) => {
      userInfoList.value.push(workUser)
    })
    getUserInfoListClone()
  } else {
    // 50音ヘッドラインのフィルターにヒットする利用者を設定する
    if (userInfoListClone.value !== undefined) {
      workFilterInitials.value.forEach((initialValue) => {
        const workUserList = userInfoListClone.value.filter((workUser) => {
          // 読み仮名がヒットすればtrue
          if (yomiHitCheck(workUser.nameKana, initialValue)) {
            return true
          }
        })

        // ヒットしたユーザーを"50音ヘッドラインのフィルター適用後の利用者一覧"に追加
        workUserList.forEach((workUser) => {
          userInfoList.value.push(workUser)
        })
      })
      getUserInfoListClone()
    }
  }
}

/**
 * 読み仮名ヒットチェック
 * 対象ワードの1文字目と、検索文字の値が一致するか比較します。
 * 比較の際は、濁点・半濁点を無視するために半角カタカナで比較します。
 * true : 一致する
 * false : 一致しない
 *
 * @param targetWord - 対象ワード
 *
 * @param searchChar - 検索文字（50音ヘッドラインの1文字）
 */
function yomiHitCheck(targetWord: string, searchChar: string) {
  if (!(targetWord.charAt(0) in convHiraganaToHalfKanaMap)) {
    return false
  }
  if (!(searchChar in convHiraganaToHalfKanaMap)) {
    return false
  }

  // 対象ワードの1文字目の半角カタカナ
  const targetWordKana = convHiraganaToHalfKanaMap[targetWord.charAt(0)]

  // 検索文字の半角カタカナ
  const searchCharKana = convHiraganaToHalfKanaMap[searchChar.charAt(0)]

  // 濁点・半濁点を考慮して、1文字目を対象に比較
  if (targetWordKana.startsWith(searchCharKana.charAt(0))) {
    return true
  } else {
    return false
  }
}

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CAREDEGREECATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TARGET },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  //日付印刷区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  //運用対象区分
  localOneway.mo00039OneWayTarget.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TARGET
  )
  //要介護度区分
  yokaiKbnList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_CAREDEGREECATEGORY).map(
    (item) => {
      return { value: item.value, title: item.label }
    }
  )
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: printSettingsScreenInitialInfoSelectGUI01211InEntity = {
    sysCd: localOneway.Or35052.syscd,
    sysRyaku: Or35052Const.DEFAULT.SYSRYAKU,
    shokuId: localOneway.Or35052.shokuId,
    houjinId: localOneway.Or35052.houjinId,
    shisetuId: localOneway.Or35052.shisetuId,
    svJigyoId: localOneway.Or35052.svJigyoId,
    sectionName: localOneway.Or35052.sectionName,
    yymmYm: localOneway.Or35052.teiYm,
    choIndex: localOneway.Or35052.choIndex,
    tantoId: localOneway.Or35052.tantoId,
    shienId: localOneway.Or35052.shienId,
  }

  // バックエンドAPIから初期情報取得
  const ret: printSettingsScreenInitialInfoSelectGUI01211OutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoSelectGUI01211',
    inputData
  )

  // 印刷設定初期情報.画面表示フラグがfalseの場合
  if (ret.data.gameHyojiFlg === Or35052Const.DEFAULT.FALSE) {
    await showOr21814MsgOneBtn()
    setState({ isOpen: false })
  }
  hokenjaList.value = ret.data.kHokenList || []
  local.taishoYm.value = localOneway.Or35052.teiYm
  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of ret.data.choPrtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.defPrtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
      if (item.prtNo === localOneway.Or35052.choIndex) {
        mo01334TypeReport.value.value = item.prtNo
      }
    }
  }

  mo01334OnewayReport.value.items = mo01334OnewayList

  refValue.value = { choPrtList: ret.data.choPrtList }

  useScreenStore().setCpTwoWay({
    cpId: Or35052Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })

  //事業者毎帳票リスト outPrintList
  if (ret.data.outPrintList.length > 0) {
    for (const item of ret.data.outPrintList) {
      localOneway.mo00018OneWayList.push({
        check: {
          modelValue: item.choNo === '1' ? true : false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: item.choKnj,
          hideDetails: true,
        } as Mo00018OnewayType,
      })
    }
  }
  //事業所選択情報リスト
  jigyouInfo.value = ret.data.jigyouInfoList
  //事業所選択情報リスト
  if (ret.data.jigyouInfoList.length > 0) {
    for (const [index, item] of ret.data.jigyouInfoList.entries()) {
      const tmpItem = {
        id: String(index),
        ...item,
      }
      localOneway.mo01334Oneway.items.push(tmpItem)
    }
  }
  //利用者リスト
  if (ret.data.riyoushaInfoList.length > 0) {
    for (const [index, item] of ret.data.riyoushaInfoList.entries()) {
      let kHokenCd = ''
      for (const e of hokenjaList.value) {
        if (e.kHokenCd === item.kHokenCd) {
          kHokenCd = e.kHokenKnj
          break
        }
      }
      //要介護度区分
      let yokaiKbn = ''
      for (const e of yokaiKbnList) {
        if (e.value === item.yokaiKbn) {
          yokaiKbn = e.title
          break
        }
      }
      const tmpItem = {
        id: String(index),
        dmySel: ref(
          localOneway.Or35052.userId === item.userid
            ? Or35052Const.DEFAULT.ONE
            : Or35052Const.DEFAULT.ZERO
        ),
        selfId: ref(item.selfId),
        nameKana: ref({
          nameKana: item.name1Kana,
          sex:
            item.sex === Or35052Const.DEFAULT.ONE
              ? Or35052Const.DEFAULT.MAN
              : Or35052Const.DEFAULT.WOMAN,
          nameKnj: item.name1Knj,
        }),
        kHokenCd: ref({
          kHokenCd: kHokenCd,
          hHokenNo: item.hHokenNo,
        }),
        yokaiKbn: ref(yokaiKbn),
        userId: ref(item.userid),
        yymmD: ref(item.yymmD),
        shienId: ref(item.shienId),
      }
      // localOneway.mo01334OnewayUserHukusuu.items.push(tmpItem)
      userInfoListClone.value.push({
        id: tmpItem.id,
        dmySel: tmpItem.dmySel.value,
        selfId: tmpItem.selfId.value,
        nameKana: tmpItem.nameKana.value.nameKana,
        sex: tmpItem.nameKana.value.sex,
        nameKnj: tmpItem.nameKana.value.nameKnj,
        kHokenCd: tmpItem.kHokenCd.value.kHokenCd,
        hHokenNo: tmpItem.kHokenCd.value.hHokenNo,
        yokaiKbn: tmpItem.yokaiKbn.value,
        userId: tmpItem.userId.value,
        yymmD: tmpItem.yymmD.value,
        shienId: tmpItem.shienId.value,
      })
      // if (
      //   localOneway.Or35052.userId &&
      //   parseInt(localOneway.Or35052.userId) > 0 &&
      //   localOneway.Or35052.userId === item.userId
      // ) {
      //   local.mo01334TypeUser.values.push(tmpItem.id)
      // }
    }
  }

  //①印刷設定初期情報.伏字フラグ=1の場合：表示
  if (ret.data.amikakeFlg === Or35052Const.DEFAULT.ONE) amikakeFlg.value = true
  //①印刷設定初期情報.文書番号フラグ=1の場合：表示
  if (ret.data.monjiNoFlg === Or35052Const.DEFAULT.ONE) monjiNoFlg.value = true
  //①印刷設定初期情報.個人保護フラグ=1の場合：表示
  if (ret.data.kojinHogoFlg === Or35052Const.DEFAULT.ONE) kojinhogoFlg.value = true
  local.mo00039TargetType = Or35052Const.DEFAULT.ZERO
}

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataComSelectInEntity = {
    sysCd: localOneway.Or35052.syscd,
    kinounameKnj: Or35052Const.DEFAULT.KINOU_NAMEKNJ,
    shokuId: localOneway.Or35052.shokuId,
    sectionKnj: choPro.value,
    kojinhogoFlg: Or35052Const.DEFAULT.ZERO,
    sectionAddNo: Or35052Const.DEFAULT.ZERO,
  }
  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataComSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataComSelect',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject
  //帳票イニシャライズデータ.氏名伏字印刷
  if (ret.data.iniDataObject[0].prtName === Or35052Const.DEFAULT.ONE)
    local.mo00018TypeAmikake.modelValue = true
  //'帳票イニシャライズデータ.文書番号印刷
  if (ret.data.iniDataObject[0].prtBng === Or35052Const.DEFAULT.ONE)
    local.mo00018TypeBunsyokanri.modelValue = true
  //'帳票イニシャライズデータ. 個人情報印刷
  if (ret.data.iniDataObject[0].prtKojin === Or35052Const.DEFAULT.ONE)
    local.mo00018TypeKojinhogo.modelValue = true
}

/**
 * 出力帳票名切替
 */
watch(
  () => mo01334TypeReport.value.value,
  async (newValue, oldValue) => {
    if (oldValue) {
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.帳票タイトルが空白以外の場合
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            // if (local.titleInput.value) item.prtTitle = local.titleInput.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prndate = local.mo00039Type
          }
        }
      }
    }
    for (const item of mo01334OnewayReport.value.items) {
      if (item) {
        if (newValue === item.id) {
          defPrtTitle.value = item?.defPrtTitle as string
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          local.title = item?.prtTitle as string
          //選択した画面.出力帳票名="サービス提供依頼書" 又は "サービス提供利用者一覧" 又は ”事業所別利用者一覧”の場合のみ、活性表示にする
          if (
            defPrtTitle.value === t('label.service-offer-request') ||
            defPrtTitle.value === t('label.service-offer-using-request') ||
            defPrtTitle.value === t('label.office-user-page')
            // defPrtTitle.value === t('label.office-provide-page') ||
            // defPrtTitle.value === t('label.office-provide-page-request')
          ) {
            localOneway.mo00045OnewayTitleInput.disabled = false
          } else {
            localOneway.mo00045OnewayTitleInput.disabled = true
          }
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prndate as string

          choPro.value = item?.choPro as string
          prtNo.value = item?.prtNo as string
          // 帳票番号
          local.prtNo = item.prtNo as string
          // 帳票セクション番号
          local.sectionNo = item.sectionNo as string

          await getReportInfoDataList()
        }
      }
    }
    localOneway.mo01334Oneway.headers =
      defPrtTitle.value === t('label.request-table-cover-letter') ? headers1 : headers2
  }
)

/**
 * 印刷設定利用者情報を取得する
 */
async function getPrintSettingsUserInfo() {
  const inputData: GetPrinterSettingsListInfoSelectInEntity = {
    tantoId: localOneway.Or35052.tantoId,
    shienId: localOneway.Or35052.shienId,
    yymmYm: local.taishoYm.value,
    kensakuFlg: Or35052Const.DEFAULT.ZERO,
    jigyouInfoList: jigyouInfo.value,
  }
  // バックエンドAPIから初期情報取得
  const ret: GetPrinterSettingsListInfoSelectOutEntity = await ScreenRepository.select(
    'getPrinterSettingsListInfoSelect',
    inputData
  )
  // localOneway.mo01334OnewayUserHukusuu.items = []
  local.mo01334TypeUser.values = []
  //利用者リスト
  if (ret.data.riyoushaInfoList.length > 0) {
    userInfoListClone.value = []
    for (const [index, item] of ret.data.riyoushaInfoList.entries()) {
      let kHokenCd = ''
      if (hokenjaList.value !== undefined && hokenjaList.value.length > 0) {
        for (const e of hokenjaList.value) {
          if (e.kHokenCd === item.kHokenCd) {
            kHokenCd = e.kHokenKnj
            break
          }
        }
      }
      //要介護度区分
      let yokaiKbn = ''
      for (const e of yokaiKbnList) {
        if (e.value === item.yokaiKbn) {
          yokaiKbn = e.title
          break
        }
      }
      const tmpItem = {
        id: String(index),
        dmySel: ref(
          localOneway.Or35052.userId === item.userid
            ? Or35052Const.DEFAULT.ONE
            : Or35052Const.DEFAULT.ZERO
        ),
        selfId: ref(item.selfId),
        nameKana: ref({
          nameKana: item.name1Kana,
          sex:
            item.sex === Or35052Const.DEFAULT.ONE
              ? Or35052Const.DEFAULT.MAN
              : Or35052Const.DEFAULT.WOMAN,
          nameKnj: item.name1Knj,
        }),
        kHokenCd: ref({
          kHokenCd: kHokenCd,
          hHokenNo: item.hHokenNo,
        }),
        yokaiKbn: ref(yokaiKbn),
        userId: ref(item.userid),
        yymmD: ref(item.yymmD),
        shienId: ref(item.shienId),
      }
      userInfoListClone.value.push({
        id: tmpItem.id,
        dmySel: tmpItem.dmySel.value,
        selfId: tmpItem.selfId.value,
        nameKana: tmpItem.nameKana.value.nameKana,
        sex: tmpItem.nameKana.value.sex,
        nameKnj: tmpItem.nameKana.value.nameKnj,
        kHokenCd: tmpItem.kHokenCd.value.kHokenCd,
        hHokenNo: tmpItem.kHokenCd.value.hHokenNo,
        yokaiKbn: tmpItem.yokaiKbn.value,
        userId: tmpItem.userId.value,
        yymmD: tmpItem.yymmD.value,
        shienId: tmpItem.shienId.value,
      })
      // localOneway.mo01334OnewayUserHukusuu.items.push(tmpItem)
      if (
        localOneway.Or35052.userId &&
        parseInt(localOneway.Or35052.userId) > 0 &&
        localOneway.Or35052.userId === item.userid
      ) {
        local.mo01334TypeUser.values.push(tmpItem.id)
      }
    }
    calcAppliedFilterUserList(focusSettingInitial.value)
  }
}

/**
 * 「事業者毎帳票選択」変更時
 */
const onChangeOffice = async () => {
  for (const item of localOneway.mo00018OneWayList) {
    if (item.check.modelValue) {
      switch (item.onewayModelValue.checkboxLabel) {
        case Or35052Const.DEFAULT.CHECKONE:
          keyVal.value = Or35052Const.DEFAULT.KEYONE
          break
        case Or35052Const.DEFAULT.CHECKTWO:
          keyVal.value = Or35052Const.DEFAULT.KEYTWO
          break
        case Or35052Const.DEFAULT.CHECKTHREE:
          keyVal.value = Or35052Const.DEFAULT.KEYTHREE
          break
        case Or35052Const.DEFAULT.CHECKFOUR:
          keyVal.value = Or35052Const.DEFAULT.KEYFOUR
          break
        case Or35052Const.DEFAULT.CHECKFIVE:
          keyVal.value = Or35052Const.DEFAULT.KEYFIVE
          break
        case Or35052Const.DEFAULT.CHECKSIX:
          keyVal.value = Or35052Const.DEFAULT.KEYSIX
          break
      }
    }
  }
  await printWriteSettings()
}

/**
 * 設定の書込（NEXのパソコン単位の設定）を取得する
 *
 */
async function printWriteSettings() {
  const inputData: PrintWriteSettingsSelectInEntity = {
    shokuinId: localOneway.Or35052.shokuId,
    houjinId: Or35052Const.DEFAULT.ZERO,
    shisetuId: Or35052Const.DEFAULT.ZERO,
    svJigyoId: Or35052Const.DEFAULT.ZERO,
    screenNm: Or35052Const.DEFAULT.SCREENNM,
    section: Or35052Const.DEFAULT.SECTION,
    parameter: '',
    keyVal: keyVal.value,
    gsyscd: localOneway.Or35052.syscd,
  }

  // バックエンドAPIから初期情報取得
  // const ret: PrintWriteSettingsSelectOutEntity =
  await ScreenRepository.select('printWriteSettingsSelect', inputData)
}

/**
 *  提供年月-前へアイコンクリック 提供年月選択アイコンクリック 提供年月-次へアイコンクリック
 */
const onChangeDate = async () => {
  if (!local.taishoYm.value) {
    return
  } else {
    await getPrintSettingsUserInfo()
  }
  // 提供年月
}

/**
 * 「事業所選択」チェック
 */
const onChangeTarget = () => {
  localOneway.mo01334Oneway.items = []
  if (local.mo00039TargetType === Or35052Const.DEFAULT.ONE) {
    if (jigyouInfo.value.length > 0) {
      for (const [index, item] of jigyouInfo.value.entries()) {
        if (item.inoutKbn === Or35052Const.DEFAULT.ONE) {
          const tmpItem = {
            id: String(index),
            ...item,
          }
          localOneway.mo01334Oneway.items.push(tmpItem)
        }
      }
    }
  } else if (local.mo00039TargetType === Or35052Const.DEFAULT.TWO) {
    if (jigyouInfo.value.length > 0) {
      for (const [index, item] of jigyouInfo.value.entries()) {
        if (item.inoutKbn === Or35052Const.DEFAULT.ZERO || item.inoutKbn === null) {
          const tmpItem = {
            id: String(index),
            ...item,
          }
          localOneway.mo01334Oneway.items.push(tmpItem)
        }
      }
    }
  } else if (local.mo00039TargetType === Or35052Const.DEFAULT.ZERO) {
    if (jigyouInfo.value.length > 0) {
      for (const [index, item] of jigyouInfo.value.entries()) {
        const tmpItem = {
          id: String(index),
          ...item,
        }
        localOneway.mo01334Oneway.items.push(tmpItem)
      }
    }
  }
}

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

watch(
  () => local.mo01334Type.values,
  () => {
    svJigyoList.value = []
    if (jigyouInfo.value.length > 0) {
      for (const [index, item] of jigyouInfo.value.entries()) {
        for (const e of local.mo01334Type.values) {
          if (String(index) === e) {
            const tmpItem = {
              jigyoId: item.svJigyoId,
              jigyoKnj: item.jigyoKnj,
              jigyoRyakuKnj: item.jigyoRyakuKnj,
            }
            svJigyoList.value.push(tmpItem)
          }
        }
      }
    }
  }
)
watch(
  () => local.mo01334TypeUser.values,
  () => {
    userIdList1.value = []
    if (userInfoListClone.value.length > 0) {
      for (const [index, item] of userInfoListClone.value.entries()) {
        for (const e of local.mo01334TypeUser.values) {
          if (String(index) === e) {
            const tmpItem = {
              userId: item.selfId,
              userName: item.nameKnj,
              yymmd: item.yymmD,
              shienid: item.shienId,
            }
            userIdList1.value.push(tmpItem)
          }
        }
      }
    }
  }
)

/**
 * 「閉じるボタン」押下
 */
async function close() {
  await checkTitleInput()
  const choPrtList = await getDataTable()
  // if (isEdit.value)
  await savePrintSettingInfo([choPrtList[parseInt(mo01334TypeReport.value.value) - 1]])
  setState({ isOpen: false })
}

/**
 * 「特記事項登録」ボタン押下
 */
async function openSpe() {
  if (targetJigCount.value === Or35052Const.DEFAULT.ZERO) {
    await showOr21814MsgTwoBtn()
    return
  }
  if (targetCount.value === Or35052Const.DEFAULT.ZERO) {
    await showOr21814MsgThreeBtn()
    return
  }
  //提供年月
  or28349Data.objectYm = local.taishoYm.value
  // Or28349のダイアログ開閉状態を更新する
  Or28349Logic.state.set({
    uniqueCpId: or28349.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「オプション」ボタン押下
 */
async function open() {
  if (!prtNo.value) {
    await showOr21813MsgOneBtn()
    return
  }
  //職員ID
  or26315Data.shokuId = localOneway.Or35052.shokuId
  //法人ID
  or26315Data.houjinId = localOneway.Or35052.houjinId
  //施設ID
  or26315Data.shisetuId = localOneway.Or35052.shisetuId
  //事業所ID
  or26315Data.svJigyoId = localOneway.Or35052.svJigyoId
  or26315Data.printDate = local.mo00020Type.value
  // Or26315のダイアログ開閉状態を更新する
  Or26315Logic.state.set({
    uniqueCpId: or26315.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
async function pdfDownload() {
  if (await checkTitleInput()) return
  if (!choPro.value) {
    await showOr21813MsgOneBtn()
    return
  }
  // if (local.mo01334TypeUser.values.length === 0) {
  //   showOr21814Msg(t('message.i-cmn-11393'))
  //   return
  // }
  if (local.mo01334Type.values.length === 0) {
    showOr21813Msg(t('message.e-cmn-11412'))
    return
  }
  if (
    mo01334TypeReport.value.value !== Or35052Const.DEFAULT.SEVEN &&
    mo01334TypeReport.value.value !== Or35052Const.DEFAULT.EIGHT
  ) {
    if (local.mo01334TypeUser.values.length === 0) {
      await showOr21813MsgTwoBtn()
      return
    }
    //印刷用帳票セクションを表示する
    if (defPrtTitle.value === t('label.office-print')) {
      let flg = '' as string
      //印刷用帳票セクションのデータ全てチェックオフの場合
      for (const item of localOneway.mo00018OneWayList) {
        if (item.check.modelValue) {
          flg = Or35052Const.DEFAULT.TRUE
          break
        }
      }
      if (flg !== Or35052Const.DEFAULT.TRUE) {
        await showOr21813MsgFourBtn()
        return
      }
    }
    if (local.mo01334Type.values.length === 0) {
      showOr21813Msg(t('message.e-cmn-11412'))
      return
    }
  }

  //印刷設定情報保存
  const choPrtList = await getDataTable()
  sectionName.value = choPrtList[parseInt(mo01334TypeReport.value.value) - 1].choPro

  if (!sectionName.value) {
    await showOr21813MsgThreeBtn()
    return
  }
  // if (isEdit.value)
  await savePrintSettingInfo([choPrtList[parseInt(mo01334TypeReport.value.value) - 1]])

  // 事業所別利用者一覧
  if (defPrtTitle.value === t('label.office-user-page')) {
    const teikyouSvJigyoIdList = localOneway.mo01334Oneway.items
      .filter((item, index) => local.mo01334Type.values.includes(index.toString()))
      .map((item) => {
        return {
          jigyoId: item.svJigyoId as string,
        }
      })
    const userIdList = localOneway.mo01334OnewayUserHukusuu.items
      .filter((item, index) => local.mo01334TypeUser.values.includes(index.toString()))
      .map((item) => {
        return {
          userId: item.userId as string,
        }
      })
    const inputData: OfficeUserListReportInEntity = {
      printSet: {
        shiTeiKubun: local.mo00039Type,
        shiTeiDate: local.mo00020Type.value,
        fusejiFlg: amikakeFlg.value ? Or35052Const.DEFAULT.ONE : Or35052Const.DEFAULT.ZERO,
      },
      shienId: localOneway.Or35052.shienId,
      teikyouYm: localOneway.Or35052.teiYm,
      teikyouSvJigyoIdList: teikyouSvJigyoIdList,
      userIdList: userIdList,
      titleName: local.titleInput.value,
      shokuinId: localOneway.Or35052.shokuId,
      syscd: localOneway.Or35052.syscd,
      teikyouSvJigyoMax: selectedCount.value,
      /** 承認欄保持フラグ */
      shoninFlg: cmnRouteCom.getInitialSettingMaster()?.shoninFlg ?? Or35052Const.DEFAULT.EMPTY,
      /** 敬称使用フラグ */
      keishoFlg: cmnRouteCom.getInitialSettingMaster()?.keishoFlg ?? Or35052Const.DEFAULT.EMPTY,
      /** 敬称文字列 */
      keishoKnj: cmnRouteCom.getInitialSettingMaster()?.keishoKnj ?? Or35052Const.DEFAULT.EMPTY,
    }
    await reportOutput(
      Or35052Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.OFFICE_USER_LIST,
      inputData,
      reportOutputType.DOWNLOAD
    )
  } else if (defPrtTitle.value === t('label.office-provide-page')) {
    const teikyouSvJigyoIdList = localOneway.mo01334Oneway.items
      .filter((item, index) => local.mo01334Type.values.includes(index.toString()))
      .map((item) => {
        return {
          jigyoId: item.svJigyoId as string,
        }
      })
    const userIdList = localOneway.mo01334OnewayUserHukusuu.items
      .filter((item, index) => local.mo01334TypeUser.values.includes(index.toString()))
      .map((item) => {
        return {
          //userId: item.userId as string,
          userId: '1',
          yymmd: item.yymmD as string, //todo選択した.利用者一覧明細セクション[i](利用者情報リスト).サービス提供年月(変更日)
          shienid: '1', //todo選択した.利用者一覧明細セクション[i](利用者情報リスト).支援事業者id
        }
      })
    const inputData: TeikyohyoReportEntity = {
      printSet: {
        shiTeiKubun: local.mo00039Type,
        shiTeiDate: local.mo00020Type.value,
        fusejiFlg: amikakeFlg.value ? Or35052Const.DEFAULT.ONE : Or35052Const.DEFAULT.ZERO,
      },
      shienId: localOneway.Or35052.shienId,
      teikyouYm: localOneway.Or35052.teiYm,
      teikyouSvJigyoIdList: teikyouSvJigyoIdList,
      userIdList: userIdList,
      appYmd: localOneway.Or35052.appYmd,
      gbeBunshoFlg: localOneway.Or35052.gbeBunshoFlg,
      userMax: localOneway.Or35052.userMax,
      shokuinId: localOneway.Or35052.shokuId,
      syscd: localOneway.Or35052.syscd,
      teikyouSvJigyoMax: targetJigCount.value,
      /** 承認欄保持フラグ */
      shoninFlg: cmnRouteCom.getInitialSettingMaster()?.shoninFlg ?? Or35052Const.DEFAULT.EMPTY,
      /** 敬称使用フラグ */
      keishoFlg: cmnRouteCom.getInitialSettingMaster()?.keishoFlg ?? Or35052Const.DEFAULT.EMPTY,
      /** 敬称文字列 */
      keishoKnj: cmnRouteCom.getInitialSettingMaster()?.keishoKnj ?? Or35052Const.DEFAULT.EMPTY,
    }

    await reportOutput(
      Or35052Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.TEIKYOHO_REPORT,
      inputData,
      reportOutputType.DOWNLOAD
    )
  } else {
    await downloadPdf()
  }

  // 提供票送付状
  if (defPrtTitle.value === t('label.request-table-cover-letter')) {
    const teikyouSvJigyoIdList = localOneway.mo01334Oneway.items
      .filter((item, index) => local.mo01334Type.values.includes(index.toString()))
      .map((item) => {
        return {
          jigyoId: item.svJigyoId as string,
        }
      })
    const inputData: ITeikyoSoufuReportSelectEntity = {
      printSet: {
        shiTeiKubun: local.mo00039Type,
        shiTeiDate: local.mo00020Type.value,
      },
      shinId: localOneway.Or35052.shienId,
      teikyouYm: localOneway.Or35052.teiYm,
      teikyouSvJigyoIdList: teikyouSvJigyoIdList,
      shokuinId: localOneway.Or35052.shokuId,
      syscd: localOneway.Or35052.syscd,
      faxFlg: 'False',
      /**
       * FAX用事業所名
       */
      faxJigyoKnj: '',
      /**
       * FAX用FAX
       */
      faxFaxno: '',
      /**
       * セクション情報
       */
      isSection: Or35052Const.DEFAULT.IS_SECTION_241,
      /**
       * 送付状のページカウント
       */
      pageCnt: '',
      appYmd: systemCommonsStore.getSystemDate!,
      /** 承認欄保持フラグ */
      shoninFlg: cmnRouteCom.getInitialSettingMaster()?.shoninFlg ?? Or35052Const.DEFAULT.EMPTY,
      /** 敬称使用フラグ */
      keishoFlg: cmnRouteCom.getInitialSettingMaster()?.keishoFlg ?? Or35052Const.DEFAULT.EMPTY,
      /** 敬称文字列 */
      keishoKnj: cmnRouteCom.getInitialSettingMaster()?.keishoKnj ?? Or35052Const.DEFAULT.EMPTY,
    }
    await reportOutput(
      Or35052Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.COVER_LETTER,
      inputData,
      reportOutputType.DOWNLOAD
    )
  }

  // OrX0117のダイアログ開閉状態を更新する
  OrX0117Logic.state.set({
    uniqueCpId: orX0117.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * pdf download
 *
 */
const downloadPdf = async () => {
  chropList.value = []
  for (const item of localOneway.mo00018OneWayList) {
    if (item.check.modelValue) {
      chropList.value.push('1')
    } else {
      chropList.value.push('0')
    }
  }
  const jigyoIdList = []
  for (const item of svJigyoList.value) {
    jigyoIdList.push(item.jigyoId)
  }
  const teikyouSvJigyoIdList = []
  if (jigyouInfo.value.length > 0) {
    for (const item of jigyouInfo.value) {
      teikyouSvJigyoIdList.push({
        jigyoId: item.svJigyoId,
        jigyoKnj: item.jigyoKnj,
        jigyoRyakuKnj: item.jigyoRyakuKnj,
      })
    }
  }

  const inputData: TeikyoReportEntity = {
    printSet: {
      shiTeiKubun: local.mo00039Type,
      shiTeiDate: local.mo00020Type.value,
      fusejiFlg: amikakeFlg.value ? Or35052Const.DEFAULT.ONE : Or35052Const.DEFAULT.ZERO,
    },
    shienId: localOneway.Or35052.shienId,
    teikyouYm: localOneway.Or35052.teiYm,
    teikyouSvJigyoIdList: teikyouSvJigyoIdList,
    svJigyoIdList: jigyoIdList,
    userIdList: userIdList1.value,
    tantoId: localOneway.Or35052.tantoId,
    userMax: targetCount.value,
    titleName: local.titleInput.value,
    yymmYm: local.taishoYm.value,
    shokuinId: localOneway.Or35052.shokuId,
    syscd: localOneway.Or35052.syscd,
    svJigyoId: localOneway.Or35052.svJigyoId,
    tantoName: Or35052Const.DEFAULT.EMPTY,
    userInfoDto: userIdList1.value,
    appYmd: localOneway.Or35052.appYmd,
    gbeBunshoFlg: localOneway.Or35052.gbeBunshoFlg,
    teikyouSvJigyoMax: targetJigCount.value,
    isSection: local.sectionNo,
    cholist: chropList.value,
    faxFlg: Or35052Const.DEFAULT.FALSE,
    shinId: localOneway.Or35052.shienId,
    /** 承認欄保持フラグ */
    shoninFlg: cmnRouteCom.getInitialSettingMaster()?.shoninFlg ?? Or35052Const.DEFAULT.EMPTY,
    /** 敬称使用フラグ */
    keishoFlg: cmnRouteCom.getInitialSettingMaster()?.keishoFlg ?? Or35052Const.DEFAULT.EMPTY,
    /** 敬称文字列 */
    keishoKnj: cmnRouteCom.getInitialSettingMaster()?.keishoKnj ?? Or35052Const.DEFAULT.EMPTY,
  } as TeikyoReportEntity
  switch (local.prtNo) {
    case Or35052Const.PRINT_NO_1:
      local.reportId = Or35052Const.PDF_DOWNLOAD_REPORT_ID.TEIKYO_1
      break
    case Or35052Const.PRINT_NO_2:
      local.reportId = Or35052Const.PDF_DOWNLOAD_REPORT_ID.TEIKYO_2
      break
    case Or35052Const.PRINT_NO_3:
      local.reportId = Or35052Const.PDF_DOWNLOAD_REPORT_ID.TEIKYO_3
      break
    case Or35052Const.PRINT_NO_4:
      local.reportId = Or35052Const.PDF_DOWNLOAD_REPORT_ID.TEIKYO_4
      break
    case Or35052Const.PRINT_NO_5:
      local.reportId = Or35052Const.PDF_DOWNLOAD_REPORT_ID.TEIKYO_5
      break
    case Or35052Const.PRINT_NO_6:
      local.reportId = Or35052Const.PDF_DOWNLOAD_REPORT_ID.TEIKYO_6
      break
    case Or35052Const.PRINT_NO_7:
      local.reportId = Or35052Const.PDF_DOWNLOAD_REPORT_ID.TEIKYO_7
      break
    case Or35052Const.PRINT_NO_8:
      local.reportId = Or35052Const.PDF_DOWNLOAD_REPORT_ID.TEIKYO_8
      break
    case Or35052Const.PRINT_NO_9:
      local.reportId = Or35052Const.PDF_DOWNLOAD_REPORT_ID.TEIKYO_9
      break
    default:
      local.reportId = Or35052Const.DEFAULT.EMPTY
      break
  }
  // 帳票出力
  await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
  return
}

/**
 * 印刷設定情報を保存する
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
async function savePrintSettingInfo(choPrtList: choPrtList[]) {
  console.log(choPrtList)
  const inputData: printSettingsInfoUpdateGUI01211InEntity = {
    shokuId: localOneway.Or35052.shokuId,
    houjinId: localOneway.Or35052.houjinId,
    shisetuId: localOneway.Or35052.shisetuId,
    svJigyoId: localOneway.Or35052.svJigyoId,
    syscd: localOneway.Or35052.syscd,
    sysRyaku: Or35052Const.DEFAULT.SYSRYAKU,
    amikakeChk: local.mo00018TypeAmikake.modelValue
      ? Or35052Const.DEFAULT.ONE
      : Or35052Const.DEFAULT.ZERO,
    monjiNoChk: local.mo00018TypeBunsyokanri.modelValue
      ? Or35052Const.DEFAULT.ONE
      : Or35052Const.DEFAULT.ZERO,
    kojinHogoChk: local.mo00018TypeKojinhogo.modelValue
      ? Or35052Const.DEFAULT.ONE
      : Or35052Const.DEFAULT.ZERO,
    kojinHogoFlg: kojinhogoFlg.value ? Or35052Const.DEFAULT.ONE : Or35052Const.DEFAULT.ZERO,
    sectionPrtNo: prtNo.value,
    // iniDataObject: { ...reportInitData.value },
    choPrtList: choPrtList,
  }

  // バックエンドAPIから初期情報取得
  await ScreenRepository.update('printSettingsInfoUpdateGUI01211', inputData)
}

/**
 * 利用者一覧明細データのフィルタを行う
 */
watch(
  () => Or00094Logic.event.get(or00094.value.uniqueCpId),
  (newValue) => {
    if (newValue?.charBtnClickFlg) {
      // 50音ヘッドラインの文字ボタン押下イベントを検知
      const workFilterInitials = Or00094Logic.data.get(or00094.value.uniqueCpId)
      if (workFilterInitials?.selectValueArray !== undefined) {
        // システム共有領域．利用者選択領域に50音ヘッドラインの選択値を設定
        // 50音ヘッドラインのフィルター適用後の利用者一覧を算出
        focusSettingInitial.value = workFilterInitials.selectValueArray
        calcAppliedFilterUserList(workFilterInitials.selectValueArray)
      }
    }
  }
)

function getUserInfoListClone() {
  localOneway.mo01334OnewayUserHukusuu.items = []
  local.mo01334TypeUser.values = []
  for (const [index, item] of userInfoList.value.entries()) {
    const tmpItem = {
      id: String(index),
      dmySel: ref(item.dmySel),
      selfId: ref(item.selfId),
      nameKana: ref({
        nameKana: item.nameKana,
        sex: item.sex,
        nameKnj: item.nameKnj,
      }),
      kHokenCd: ref({
        kHokenCd: item.kHokenCd,
        hHokenNo: item.hHokenNo,
      }),
      yokaiKbn: ref(item.yokaiKbn),
      userId: ref(item.userId),
      yymmD: ref(item.yymmD),
    }
    localOneway.mo01334OnewayUserHukusuu.items.push(tmpItem)
    if (
      localOneway.Or35052.userId &&
      parseInt(localOneway.Or35052.userId) > 0 &&
      localOneway.Or35052.userId === item.userId
    ) {
      local.mo01334TypeUser.values.push(tmpItem.id)
    }
  }
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or35052_screen"
      >
        <c-v-col
          cols="12"
          sm="2"
          class="pa-0 pt-2 px-2 or35052_border_right"
        >
          <!-- 帳票 -->
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- 出力帳票名  -->
            <template #[`item.defPrtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
          <!--特記事項登録ボタン-->
          <div class="container">
            <base-mo00611
              class="mr-2"
              :oneway-model-value="localOneway.mo00611OneWay"
              @click="openSpe()"
            />
          </div>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 or35052_border_right content_center"
        >
          <c-v-row
            no-gutter
            class="printerOption customCol or35052_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="or35052_row flex-center"
          >
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-2"
            >
              <!-- タイトル:   -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="8"
              class="pa-2"
              style="padding-left: 0 !important"
            >
              <!-- 帳票タイトル -->
              <base-mo00045
                v-model="local.titleInput"
                :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                @keyup.enter="checkTitleInput"
              />
            </c-v-col>
            <c-v-col
              v-if="
                ((defPrtTitle === t('label.service-offer-request') ||
                  defPrtTitle === t('label.service-offer-using-request') ||
                  defPrtTitle === t('label.office-user-page') ||
                  defPrtTitle === t('label.office-provide-page') ||
                  defPrtTitle === t('label.office-provide-page-request')) &&
                  amikakeFlg) ||
                defPrtTitle === t('label.service-offer-request') ||
                defPrtTitle === t('label.service-offer-using-request') ||
                (defPrtTitle === t('label.office-user-page') && kojinhogoFlg)
              "
              cols="12"
              sm="8"
              class="pa-2"
            >
              <!-- 氏名等を伏字にする -->
              <base-mo00018
                v-model="local.mo00018TypeAmikake"
                :oneway-model-value="localOneway.mo00018OneWayAmikake"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              v-if="
                (defPrtTitle === t('label.service-offer-request') ||
                  defPrtTitle === t('label.service-offer-using-request') ||
                  defPrtTitle === t('label.office-user-page') ||
                  defPrtTitle === t('label.office-provide-page') ||
                  defPrtTitle === t('label.office-provide-page-request')) &&
                monjiNoFlg
              "
              cols="12"
              sm="8"
              class="pa-2"
            >
              <!-- 文書番号を印字する -->
              <base-mo00018
                v-model="local.mo00018TypeBunsyokanri"
                :oneway-model-value="localOneway.mo00018OneWayBunsyokanri"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              v-if="
                (defPrtTitle === t('label.office-provide-page') ||
                  defPrtTitle === t('label.office-provide-page-request')) &&
                kojinhogoFlg
              "
              cols="12"
              sm="8"
              class="pa-2"
            >
              <!-- 個人情報を印刷しない -->
              <base-mo00018
                v-model="local.mo00018TypeKojinhogo"
                :oneway-model-value="localOneway.mo00018OneWayKojinhogo"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            v-if="
              defPrtTitle !== t('label.office-seal3x6') && defPrtTitle !== t('label.office-seal2x6')
            "
            no-gutter
            class="customCol or35052_row"
            style="height: 133px"
          >
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-2"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo01338
                v-if="defPrtTitle === t('label.office-print')"
                :oneway-model-value="localOneway.mo01338OneWayDateTitle"
              ></base-mo01338>
              <base-mo00039
                v-if="
                  defPrtTitle !== t('label.office-print') &&
                  defPrtTitle !== t('label.office-seal3x6') &&
                  defPrtTitle !== t('label.office-seal2x6')
                "
                v-model="local.mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
                @change="checkTitleInput"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              v-if="
                defPrtTitle !== t('label.office-seal3x6') ||
                defPrtTitle !== t('label.office-seal2x6')
              "
              id="test"
              cols="12"
              sm="5"
              class="pa-2"
              style="padding-left: 0 !important"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-if="defPrtTitle === t('label.office-print')"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
                @mousedown="checkTitleInput"
              >
              </base-mo00020>
              <base-mo00020
                v-else
                v-show="local.mo00039Type == '2'"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
                @mousedown="checkTitleInput"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-divider
            v-if="defPrtTitle === t('label.office-print')"
            class="my-0"
          ></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or35052_row"
            style="height: 345px"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <c-v-row
                style="
                  margin-right: 8px;
                  margin-top: 8px;
                  margin-bottom: 8px;
                  flex-flow: row-reverse;
                "
              >
                <!--オプションボタン-->
                <base-mo00611
                  class="mr-2"
                  :oneway-model-value="{ ...localOneway.mo00611OneWayCopy, labelColor: '#0000FF' }"
                  @click="open()"
                />
              </c-v-row>
              <c-v-row
                no-gutter
                class="printerOption customCol or35052_row"
              >
                <c-v-col
                  v-if="defPrtTitle === t('label.office-print')"
                  cols="12"
                  sm="12"
                >
                  <!-- 『事業者毎印刷』で印刷する帳票 -->
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                v-if="defPrtTitle === t('label.office-print')"
                no-gutter
                class="customCol or35052_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  class="pa-2"
                >
                  <!-- 事業者毎帳票選択 -->
                  <base-mo00018
                    v-for="(item, index) in localOneway.mo00018OneWayList"
                    :key="index"
                    v-model="item.check"
                    :oneway-model-value="item.onewayModelValue"
                    @update:model-value="onChangeOffice"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- defPrtTitle === t('label.office-seal3x6') ||
              defPrtTitle === t('label.office-seal2x6') ||
              defPrtTitle === t('label.office-print') -->

          <!-- <c-v-divider
            v-if="
              defPrtTitle === t('label.office-seal2x6') || defPrtTitle === t('label.office-print')
            "
            class="my-0"
          ></c-v-divider> -->
          <c-v-row
            no-gutter
            class="customCol or35052_row"
            :class="[
              defPrtTitle === t('label.office-seal3x6') || defPrtTitle === t('label.office-seal2x6')
                ? 'table-or35052-margin'
                : '',
              defPrtTitle === t('label.office-print') ? 'table-or35052-margin1' : '',
              defPrtTitle === t('label.request-table-cover-letter') ? 'table-or35052-margin2' : '',
            ]"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2"
            >
              <c-v-row class="or35052_row">
                <!-- 運用対象 -->
                <base-mo01338
                  :oneway-model-value="localOneway.target"
                  class="lbl1"
                />
              </c-v-row>
              <c-v-row class="or35052_row">
                <base-mo00039
                  v-model="local.mo00039TargetType"
                  :oneway-model-value="localOneway.mo00039OneWayTarget"
                  @change="onChangeTarget"
                >
                </base-mo00039>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <!-- <c-v-divider class="my-0 divider"></c-v-divider> -->
          <c-v-row
            class="customCol or35052_row"
            no-gutter
          >
            <c-v-col
              cols="12"
              sm="12"
              class="d-flex pl-2 pt-0"
            >
              <!-- 事業所一覧 -->
              <div class="pl-0 bg-w">
                <base-mo-01334
                  v-model="local.mo01334Type"
                  :oneway-model-value="localOneway.mo01334Oneway"
                  class="list-wrapper"
                  hide-default-footer
                  style="width: 1055px"
                >
                  <!-- ヘッダ -->
                  <template #[`item.jigyoKnj`]="{ value }">
                    {{ value }}
                  </template>
                  <template #[`item.letter`]>
                    <!-- 送付状 -->
                    <base-mo00009
                      :oneway-model-value="localOneway.mo00009OnewayType"
                      style="background-color: rgba(0, 0, 0, 0); margin-left: 5px"
                      variant="flat"
                      density="compact"
                      @click="editBtnClick"
                    ></base-mo00009>
                  </template>
                  <template #[`item.faxNumber`]="{ value }">
                    <base-mo01337
                      :oneway-model-value="{
                        value: value,
                        customClass: {
                          outerStyle: 'background-color: rgba(0, 0, 0, 0);',
                          itemClass: 'text-start',
                        },
                      }"
                    />
                  </template>
                  <template #[`item.tel`]="{ value }">
                    <base-mo01337
                      :oneway-model-value="{
                        value: value,
                        customClass: {
                          outerStyle: 'background-color: rgba(0, 0, 0, 0);',
                          itemClass: 'text-start',
                        },
                      }"
                    />
                  </template>
                  <template #[`item.ruakuKnj`]="{ value }">
                    <base-mo01337
                      :oneway-model-value="{
                        value: value,
                        customClass: {
                          outerStyle: 'background-color: rgba(0, 0, 0, 0);',
                          itemClass: 'text-start',
                        },
                      }"
                    />
                  </template>
                  <template #[`item.jigyoNumber`]="{ value }">
                    <base-mo01337
                      :oneway-model-value="{
                        value: value,
                        customClass: {
                          outerStyle: 'background-color: rgba(0, 0, 0, 0);',
                          itemClass: 'text-start',
                        },
                      }"
                    />
                  </template>
                </base-mo-01334>
                <c-v-row
                  class="or35052_row"
                  style="background-color: rgb(var(--v-theme-black-536))"
                >
                  <tr
                    colspan="4"
                    style="padding: 0px !important"
                    class="d-flex align-center ga-2"
                  >
                    <base-mo01338
                      class="item-color"
                      style="background-color: rgb(var(--v-theme-black-536))"
                      :oneway-model-value="localOneway.SelectedCount"
                    />
                    <base-mo01338
                      class="item-color"
                      style="background-color: rgb(var(--v-theme-black-536)); margin-left: 50px"
                      :oneway-model-value="{
                        ...localOneway.mo01338OnewaySelectedCount,
                        value: selectedJigCount + ' / ' + targetJigCount,
                      }"
                    />
                  </tr>
                </c-v-row>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
          style="height: 660px"
        >
          <c-v-row
            class="or35052_row"
            no-gutter
            style="align-items: center"
          >
            <c-v-col
              sm="12"
              class="pa-0 flex-center"
              style="margin-bottom: 8px"
            >
              <c-v-row class="or35052_row">
                <!-- 担当ケアマネラベル -->
                <!-- <base-mo01338
                  :oneway-model-value="localOneway.mo01338OneWayCareManagerInCharge"
                  style="background-color: transparent; float: left"
                >
                </base-mo01338> -->
                <!-- 担当ケアマネ-->
                <!-- <base-mo01408
                  v-model="local.mo01408modelCare"
                  :oneway-model-value="mo01408OnewayCare"
                /> -->
                <!-- 担当ケアマネプルダウン -->
                <g-custom-or-x-0145
                  v-bind="orx0145"
                  v-model="orX0145Type"
                  :oneway-model-value="localOneway.orX0145Oneway"
                  class="search-tack"
                  style="display: flex"
                ></g-custom-or-x-0145>
              </c-v-row>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <c-v-row class="or35052_row">
                <!-- 提供年月 -->
                <base-mo01352
                  v-model="local.taishoYm"
                  :oneway-model-value="localOneway.mo01352Oneway"
                  @update:model-value="onChangeDate"
                />
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <c-v-row
            class="or35052_row pr-2"
            no-gutter
          >
            <c-v-col
              cols="12"
              sm="11"
              class="pl-0 pt-2 d-flex"
            >
              <!-- 50音選択 -->
              <div style="width: 30px; z-index: 3; margin-right: 8px">
                <g-base-or-00094 v-bind="or00094" />
              </div>
              <div class="pl-0 bg-w">
                <base-mo-01334
                  v-model="local.mo01334TypeUser"
                  :oneway-model-value="localOneway.mo01334OnewayUserHukusuu"
                  class="list-wrapper"
                  hide-default-footer
                  style="width: 589px"
                >
                  <!-- ヘッダ -->
                  <template #[`headers`]="{ allSelected, selectAll, someSelected }">
                    <tr class="th-custom">
                      <th class="col-select v-data-table-column--no-padding">
                        <base-at-checkbox
                          :indeterminate="someSelected && !allSelected"
                          :model-value="allSelected"
                          checkbox-label=""
                          @update:model-value="selectAll(!allSelected)"
                        />
                      </th>
                      <!--  利用者番号 -->
                      <th class="col-selfId pa-0 px-2">
                        {{ t('label.plan-user-id') }}
                      </th>
                      <!-- フリガナ 性別 利用者名 -->
                      <th class="col-nameKana pa-0">
                        <c-v-container class="pa-0 h-100 d-flex flex-column justify-center">
                          <c-v-row
                            class="or35052_row"
                            style="height: 40px"
                          >
                            <c-v-col
                              cols="7"
                              class="px-2 d-flex align-center border-e"
                              style="height: 40px"
                            >
                              {{ t('label.frigana') }}
                            </c-v-col>
                            <c-v-col
                              cols="5"
                              class="px-2 d-flex align-center"
                              style="height: 40px"
                            >
                              {{ t('label.gender') }}
                            </c-v-col>
                          </c-v-row>
                          <c-v-row
                            class="or35052_row px-2 d-flex align-center border-t"
                            style="height: 40px"
                          >
                            {{ t('label.full_name') }}
                          </c-v-row>
                        </c-v-container>
                      </th>
                      <!-- 保険者名  被保険者番号 -->
                      <th class="col-kHokenCd pa-0">
                        <c-v-container class="pa-0 h-100 d-flex flex-column justify-center">
                          <c-v-row class="or35052_row px-2 d-flex align-center">
                            {{ t('label.insurer-name') }}
                          </c-v-row>
                          <c-v-row class="or35052_row px-2 d-flex align-center">
                            {{ t('label.h_hoken_no') }}
                          </c-v-row>
                        </c-v-container>
                      </th>
                      <!-- 要介護度 -->
                      <th class="col-yokaiKbn pa-0 px-2">
                        {{ t('label.yokai-knj') }}
                      </th>
                    </tr>
                  </template>
                  <!--  利用者番号 -->
                  <template #[`item.selfId`]="{ value }">
                    <c-v-container class="td-custom pa-0 d-flex flex-column justify-center">
                      <c-v-row class="or35052_row">
                        <c-v-col
                          cols="12"
                          class="px-2 d-flex flex-column justify-center"
                        >
                          <span class="body-text-s text-right"> {{ value }}</span>
                        </c-v-col>
                      </c-v-row>
                    </c-v-container>
                  </template>
                  <!-- フリガナ -->
                  <!-- 性別 -->
                  <!-- 利用者名 -->
                  <template #[`item.nameKana`]="{ value }">
                    <c-v-container class="td-custom pa-0 d-flex flex-column justify-center">
                      <c-v-row class="or35052_row">
                        <c-v-col
                          cols="7"
                          class="pl-4 pr-2 d-flex flex-column justify-center"
                        >
                          <span class="caption"> {{ value.nameKana }}</span>
                          <span class="body-text-s"> {{ value.nameKnj }}</span>
                        </c-v-col>
                        <c-v-col
                          cols="5"
                          class="pl-2 pr-2 d-flex align-center"
                        >
                          <base-mo01337
                            :oneway-model-value="{
                              value: value.sex,
                              valueFontColor:
                                Or35052Const.DEFAULT.MAN === value.sex ? 'blue' : 'red',
                            }"
                            class="text-right"
                          />
                        </c-v-col>
                      </c-v-row>
                    </c-v-container>
                  </template>
                  <!-- 保険者名 -->
                  <!-- 被保険者番号 -->
                  <template #[`item.kHokenCd`]="{ value }">
                    <c-v-container class="td-custom pa-0 d-flex flex-column justify-center">
                      <c-v-row class="or35052_row">
                        <c-v-col
                          cols="12"
                          class="px-2 d-flex flex-column justify-center"
                        >
                          <span class="caption"> {{ value.kHokenCd }}</span>
                          <span class="body-text-s"> {{ value.hHokenNo }}</span>
                        </c-v-col>
                      </c-v-row>
                    </c-v-container>
                  </template>
                  <!-- 要介護度 -->
                  <template #[`item.yokaiKbn`]="{ value }">
                    <c-v-container class="td-custom pa-0 d-flex flex-column justify-center">
                      <c-v-row class="or35052_row">
                        <c-v-col
                          cols="12"
                          class="px-2 d-flex flex-column justify-center"
                        >
                          <span class="body-text-s"> {{ value }}</span>
                        </c-v-col>
                      </c-v-row>
                    </c-v-container>
                  </template>
                </base-mo-01334>
                <c-v-row
                  class="or35052_row"
                  style="background-color: rgb(var(--v-theme-black-536))"
                >
                  <tr
                    colspan="4"
                    style="padding: 0px !important"
                    class="d-flex align-center ga-2"
                  >
                    <base-mo01338
                      class="item-color"
                      style="background-color: rgb(var(--v-theme-black-536)); margin-left: 50px"
                      :oneway-model-value="{
                        ...localOneway.mo01338OnewaySelectedCount,
                        value: selectedCount + ' / ' + targetCount,
                      }"
                    />
                  </tr>
                </c-v-row>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          @click="pdfDownload()"
        >
          <!--ツールチップ表示："PDFをダウンロードします"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.pdf-download')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  >
  </g-base-or21815>
  <!-- GUI01146_特記事項登録を起動する -->
  <g-custom-or-28349
    v-if="showDialogOr28349"
    v-bind="or28349"
    :oneway-model-value="or28349Data"
  />
  <!-- GUI01157_印刷オプションを起動する -->
  <g-custom-or-26315
    v-if="showDialogOr26315"
    v-bind="or26315"
    :oneway-model-value="or26315Data"
  />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117"
    :oneway-model-value="localOneway.orX0117Oneway"
  ></g-custom-or-x-0117>
</template>

<style scoped lang="scss">
.or35052_screen {
  margin: -8px !important;
}

.or35052_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or35052_row {
  margin: 0px !important;
}

.table-or35052-margin {
  margin-top: 185px !important;
}

.table-or35052-margin1 {
  margin-top: 51px !important;
}
.table-or35052-margin2 {
  margin-top: 52px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.customCol {
  margin-left: 0px;
  margin-right: 0px;
}
.th-custom {
  height: 45px;
  width: auto;
}
.td-custom {
  min-height: 60px;
  margin-left: -16px;
  margin-right: -16px;
  width: auto;
}
.col-select {
  min-width: 56px;
}
.col-selfId {
  min-width: 92px;
}
.col-nameKana {
  min-width: 205px;
}
.col-kHokenCd {
  min-width: 130px;
}
.col-yokaiKbn {
  min-width: 91px;
}
:deep(.item-color) {
  .item-label {
    color: #ffffff !important;
  }
}
.container {
  display: flex;
  justify-content: center;
  align-items: center;
}
.divider {
  width: 1070px;
}
:deep(.search-tack) {
  .ma-1 {
    margin: 0 !important;
  }
  margin-left: 8px;
  .v-input__control {
    width: 200px;
  }
}
</style>
