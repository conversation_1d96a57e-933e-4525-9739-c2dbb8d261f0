<script setup lang="ts">
/**
 * Or51795:コンテンツエリア
 * GUI00831_ケアの提供場所マスタ
 *
 * @description
 * 予定マスタ：ダイアログ
 *
 * <AUTHOR> PHAM HO HAI DANG
 */
import { onMounted, reactive, ref, watch, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or50242Const } from '../Or50242/Or50242.constants'
import { Or51794Const } from '../Or51794/Or51794.constants'
import { Or51795Const } from './Or51795.constants'
import type { Or51795StateType, AsyncFunction } from './Or51795.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or51795OnewayType } from '~/types/cmn/business/components/Or51795Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  CareProvisionLocationMasterSelectInEntity,
  CareProvisionLocationMasterSelectOutEntity,
} from '~/repositories/cmn/entities/CareProvisionLocationMasterSelectInEntity'
import { useScreenOneWayBind, useSetupChildProps, useScreenStore } from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { Or51794Type } from '~/types/cmn/business/components/Or51794Type'
import type { careProvisionLocationMasterUpdateInEntity } from '~/repositories/cmn/entities/CareProvisionLocationMasterUpdateEntity'
// import type {
//   ICheckAuthzKinouInEntity,
//   ICheckAuthzKinouOutEntity,
// } from '~/repositories/business/core/authz/entities/CheckAuthzKinouEntity'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
/**
 *子コンポーネントの双方向バインド取得・設定ユーティリティ
 */
/**
 * 多言語対応の翻訳関数
 */
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or51795OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}
/**
 *引継情報を取得する
 */
const props = defineProps<Props>()

/**
 *デフォルトの一方向バインド値
 */
const defaultOnewayModelValue: Or51795OnewayType = {
  viewAuthority: 'T',
  saveAuthority: 'T',
  dispTabFlg: '',
  sysRyaku: 'CPN',
}
/**
 *(予定マスタ) 行のアクションボタン
 */
const or50242 = ref({ uniqueCpId: Or50242Const.CP_ID(0) })
/**
 *(予定マスタ)アセスメント（包括）マスタリスト
 */
const or51794 = ref({ uniqueCpId: Or51794Const.CP_ID(0) })
/**
 *Or21814_有機体:確認ダイアログ
 */
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
/**
 *Or21813_有機体:エラーダイアログ
 */
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
/**
 *タブ項目の定義（画面上部の各タブ）
 */
const tabItemsTab = ref([
  {
    id: Or51795Const.TAB.TAB_ID_CARE_CHECK,
    title: t('label.care-check'),
    tooltipText: t('label.care-check'),
    tooltipLocation: 'bottom',
  },
  {
    id: Or51795Const.TAB.TAB_ID_OFFER,
    title: t('label.offer'),
    tooltipText: t('label.offer'),
    tooltipLocation: 'bottom',
  },
  {
    id: Or51795Const.TAB.TAB_ID_FAMILY,
    title: t('label.family'),
    tooltipText: t('label.family'),
    tooltipLocation: 'bottom',
  },
  {
    id: Or51795Const.TAB.TAB_ID_SCHEDULE,
    title: t('label.schedule'),
    tooltipText: t('label.schedule'),
    tooltipLocation: 'bottom',
  },
  {
    id: Or51795Const.TAB.TAB_ID_CARE_OFFER_LOCATION,
    title: t('label.care-offer-location'),
    tooltipText: t('label.care-offer-location'),
    tooltipLocation: 'bottom',
  },
])
/**
 * ローカル状態（OneWayバインド用）
 */
const localOneway = reactive({
  or51795: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 予定マスタダイアログ
  mo00024Oneway: {
    width: '721px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or51795',
      //toolbarTitle: t('label.care-offer-location'),
      toolbarName: 'Or51795ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      toolbarTitle: computed(
        () => tabItemsTab.value.find((i) => i.id === local.mo00043.id)?.title + 'マスタ'
      ),
    },
  } as unknown as Mo00024OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: tabItemsTab.value,
  } as Mo00043OnewayType,
  // 閉じる
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 保存
  mo00609OneWay: {
    btnLabel: t('btn.save'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.display-row-save'),
    disabled: false,
  } as Mo00609OnewayType,
})

/**
 *ローカル状態管理
 */
const local = reactive({
  mo00043: { id: Or51795Const.TAB.TAB_ID_CARE_OFFER_LOCATION } as Mo00043Type,
  or51794: {
    editFlg: false,
    delBtnDisabled: false,
    stringInputAssistList: [],
    saveResultYoTeiList: [],
  } as Or51794Type,
})

/**************************************************
 * 変数定義
 **************************************************/
/**
 *モーダル状態管理
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or51795Const.DEFAULT.IS_OPEN,
})
/**
 *Or51794 Ref
 */
const or51794Ref = ref<{
  tableValidation(): AsyncFunction
  createRow(): AsyncFunction
  copyRow(): AsyncFunction
  deleteRow(): AsyncFunction
  init(): AsyncFunction
}>()
/**
 *Or50242 Ref
 */
const or50242Ref = ref<{ delBtnDisable(disabled: boolean): AsyncFunction }>()

/**
 * 権限チェック入力値
 */
// const input: ICheckAuthzKinouInEntity = {
//   keys: [{ path: '/components/custom-components/organisms/Or51795/Or51795' }],
// }

/**
 * 保存権限の有無を示すフラグ
 */
const isPermissionSave = ref<boolean>(true)
/**************************************************
 * Pinia
 **************************************************/
/**
 *ワンウェイバインドの状態設定
 */
useScreenOneWayBind<Or51795StateType>({
  cpId: Or51795Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 開閉フラグ更新時
     *
     * @param value - 開閉状態
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or51795Const.DEFAULT.IS_OPEN
    },
  },
})
/**
 *ダイアログ表示フラグ
 */
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
/**
 *ダイアログ表示フラグ
 */
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

/**
 *ナビゲーション制御領域のいずれかの編集フラグがON
 */
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
/**
 * フラグ: 閉じるかどうか
 */
// const isClose =  ref('')
/**
 * フラグ: 変更なしかどうか
 */
const isNoChange = ref('')
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or50242Const.CP_ID(0)]: or50242.value,
  [Or51794Const.CP_ID(0)]: or51794.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
})

onMounted(async () => {
  await init()
})

/**
 * 予定マスタ初期処理
 */
async function init() {
  isPermissionSave.value = await hasRegistAuth('/care-manager/mockup-dialog/GUI00831')
  if (!isPermissionSave.value) {
    localOneway.or51795.saveAuthority = 'T'
  }
  // 区分フラグ
  let kbnFlg: string
  //引継情報. sys略称 が "CPN" の場合、１
  if (localOneway.or51795.sysRyaku === Or51795Const.CPN) {
    kbnFlg = '1'
  } else {
    // 以外の場合、２
    kbnFlg = '2'
  }
  // 予定マスタ情報取得(IN)
  const inputData: CareProvisionLocationMasterSelectInEntity = {
    kbnFlg: kbnFlg,
    cf1Kbn: '6',
  }
  // 予定マスタ初期情報取得
  const ret: CareProvisionLocationMasterSelectOutEntity = await ScreenRepository.select(
    'careProvisionLocationMasterSelect',
    inputData
  )
  local.or51794.stringInputAssistList = []
  local.or51794.stringInputAssistList = ret.data.stringInputAssistList.map((x) => ({
    cf1Id: x.cf1Id,
    cf1Kbn: x.cf1Kbn,
    kbnCd: x.kbnCd,
    kbnFlg: x.kbnFlg ?? kbnFlg,
    textKnj: x.textKnj,
    changeF: x.changeF,
    updateKbn: UPDATE_KBN.NONE,
  }))

  // 登録権限がない場合、
  if (localOneway.or51795.saveAuthority === 'F') {
    // 保存ボタンを非活性とする
    localOneway.mo00609OneWay.disabled = true
  } else {
    localOneway.mo00609OneWay.disabled = false
  }
  // 親画面.表示タブフラグが「予定」の場合
  if (localOneway.or51795.dispTabFlg === Or51795Const.TAB.TAB_ID_CARE_OFFER_LOCATION) {
    // 予定タブを選択する
    local.mo00043.id = Or51795Const.TAB.TAB_ID_CARE_OFFER_LOCATION
  }
  or51794Ref.value?.init()
}

/**
 * 保存処理を行う。
 *
 * @returns 保存成功時は true、バリデーションエラーや変更なし等で保存しない場合は false。
 */
async function save() {
  // 画面入力データ変更があるかどうかを判定する
  if (isEdit.value) {
    // 変更がある場合、処理継続
    const valid = or51794Ref.value?.tableValidation()
    // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
    const dataArray = local.or51794.stringInputAssistList
    //予定一覧.区分番号、予定一覧.内容の何れかがブランクの場合
    const blankArray = dataArray.filter(
      (data) => (data.kbnCd === '' || data.textKnj === '') && data.updateKbn !== UPDATE_KBN.DELETE
    )
    if (blankArray.length > 0) {
      showOr21813Msg(t('message.e-cmn-41708'))
      return false
    }
    //予定マスタ一覧に、重複した区分番号が存在する場合
    const nameSet = new Set<string>()
    for (const item of dataArray) {
      if (nameSet.has(item.kbnCd)) {
        showOr21813Msg(t('message.e-cmn-41713', [item.kbnCd]))
        return false
      }
      nameSet.add(item.kbnCd)
    }
    if (!valid) {
      return false
    }

    const param: careProvisionLocationMasterUpdateInEntity = {
      stringInputAssistList: dataArray.filter((x) => x.updateKbn !== UPDATE_KBN.NONE),
    }
    // 予定マスタ情報保存
    await ScreenRepository.update('careProvisionLocationMasterUpdate', param)

    await nextTick()
    await init()
    return true
  } else {
    isNoChange.value = 'true'
    showOr21814Msg(t('message.i-cmn-21800'))
  }
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
// function close() {
//   isClose.value = 'true'
//   if (!isEdit.value) {
//     // 画面を閉じる。
//     setState({ isOpen: false })
//   } else {
//     if ('F' === localOneway.or51795.saveAuthority) {
//       // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
//       showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
//     } else {
//       // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
//       showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
//     }
//   }
// }

/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
// function showOr21814MsgTwoBtn(errormsg: string) {
//   Or21814Logic.state.set({
//     uniqueCpId: or21814.value.uniqueCpId,
//     state: {
//       // ダイアログタイトル
//       dialogTitle: t('label.confirm'),
//       // ダイアログテキスト
//       dialogText: errormsg,
//       firstBtnType: 'blank',
//       secondBtnType: 'normal1',
//       secondBtnLabel: t('btn.yes'),
//       thirdBtnType: 'normal3',
//       thirdBtnLabel: t('btn.no'),
//     },
//   })
//   // 確認ダイアログをオープン
//   Or21814Logic.state.set({
//     uniqueCpId: or21814.value.uniqueCpId,
//     state: {
//       isOpen: true,
//     },
//   })
// }
/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
// function showOr21814MsgThreeBtn(errormsg: string) {
//   Or21814Logic.state.set({
//     uniqueCpId: or21814.value.uniqueCpId,
//     state: {
//       // ダイアログタイトル
//       dialogTitle: t('label.confirm'),
//       // ダイアログテキスト0
//       dialogText: errormsg,
//       firstBtnType: 'normal1',
//       firstBtnLabel: t('btn.yes'),
//       secondBtnType: 'destroy1',
//       secondBtnLabel: t('btn.no'),
//       thirdBtnType: 'normal3',
//       thirdBtnLabel: t('btn.cancel'),
//     },
//   })
//   // 確認ダイアログをオープン
//   Or21814Logic.state.set({
//     uniqueCpId: or21814.value.uniqueCpId,
//     state: {
//       isOpen: true,
//     },
//   })
// }
/**
 * 保存ボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21813Msg(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 保存ボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814Msg(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 行追加ボタン
 */
function onAddItem() {
  or51794Ref.value?.createRow()
}
/**
 * 行複写ボタン
 */
function onCloneItem() {
  or51794Ref.value?.copyRow()
}
/**
 * 行削除ボタン
 */
function onDelete() {
  or51794Ref.value?.deleteRow()
}

/**
 * 行削除ボタン活性状態を監視
 *
 * @description
 * 活性状態を監視
 */
watch(
  () => local.or51794.delBtnDisabled,
  (newValue) => {
    or50242Ref.value?.delBtnDisable(!newValue)
  }
)

defineExpose({
  save,
  init,
})
</script>

<template>
  <div class="mt-2">
    <g-custom-or-50242
      ref="or50242Ref"
      v-bind="or50242"
      @on-add-item="onAddItem"
      @on-clone-item="onCloneItem"
      @on-delete="onDelete"
    />
  </div>
  <!-- (予定マスタ)アセスメント（包括）マスタリスト -->
  <g-custom-or-51794
    ref="or51794Ref"
    v-bind="or51794"
    v-model="local.or51794"
    :parent-unique-cp-id="props.uniqueCpId"
  >
  </g-custom-or-51794>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
$table-row-height: 32px;

.col-padding {
  padding: 8px !important;
}

.v-row {
  margin: -8px;
}

.content-head {
  margin-bottom: 8px;
}

.head-btn-margin {
  margin-left: 150px !important;
}

:deep(.title-container) {
  margin: 0 !important;
}
</style>
