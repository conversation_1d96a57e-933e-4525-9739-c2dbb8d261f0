<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or27635Const } from '~/components/custom-components/organisms/Or27635/Or27635.constants'
import { Or27635Logic } from '~/components/custom-components/organisms/Or27635/Or27635.logic'
import type { Or27635Type, Or27635OnewayType } from '~/types/cmn/business/components/Or27635Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**
 * GUI01168_保険者選択
 *
 * @description
 * 「保険者選択」画面を表示
 *
 * <AUTHOR> 劉顕康
 */

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01168'
// ルーティング
const routing = 'GUI01168/pinia'
// 画面物理名
const screenName = 'GUI01168'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27635 = ref({ uniqueCpId: Or27635Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01168' },
})
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType
/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27635Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or27635.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01168',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27635Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27635Const.CP_ID(1)]: or27635.value,
})

// ダイアログ表示フラグ
const showDialogOr27635 = computed(() => {
  // Or27635のダイアログ開閉状態
  return Or27635Logic.state.get(or27635.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or27635) 項目区分=30
 */
function onClickOr27635() {
  // Or27635のダイアログ開閉状態を更新する
  Or27635Logic.state.set({
    uniqueCpId: or27635.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function onClickOr27635_1() {
  or27635Data.offerDay = localData.offerDay.value
  const parsed = JSON.parse(jsonString.value)
  or27635Data.historyList = parsed
  // Or27635のダイアログ開閉状態を更新する
  Or27635Logic.state.set({
    uniqueCpId: or27635.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or27635Type = ref<Or27635Type>({
  // 変更日
  modifiedDay: '',
})

const or27635Data: Or27635OnewayType = {
  // 提供日
  offerDay: '2025/05/30',
  // 利用票履歴リスト
  historyList: [
    {
      // 変更日
      modifiedDay: '2025/05/27',
      // 保険者番号
      insurerNo: '1403',
      // 保険者
      insurer: 'ほのぼの市',
      // 被保険者番号
      insuredPersonNo: '985439859',
      // 連番
      serialNo: '3',
    },
    {
      // 変更日
      modifiedDay: '2025/05/26',
      // 保険者番号
      insurerNo: '1404',
      // 保険者
      insurer: 'ほのぼの市',
      // 被保険者番号
      insuredPersonNo: '985439860',
      // 連番
      serialNo: '4',
    },
    {
      // 変更日
      modifiedDay: '2025/05/30',
      // 保険者番号
      insurerNo: '1405',
      // 保険者
      insurer: 'ほのぼの市',
      // 被保険者番号
      insuredPersonNo: '985439861',
      // 連番
      serialNo: '5',
    },
    {
      // 変更日
      modifiedDay: '2025/05/30',
      // 保険者番号
      insurerNo: '1406',
      // 保険者
      insurer: 'ほのぼの市',
      // 被保険者番号
      insuredPersonNo: '985439862',
      // 連番
      serialNo: '6',
    },
    {
      // 変更日
      modifiedDay: '2025/05/24',
      // 保険者番号
      insurerNo: '1407',
      // 保険者
      insurer: 'ほのぼの市',
      // 被保険者番号
      insuredPersonNo: '985439863',
      // 連番
      serialNo: '7',
    },
  ],
}
const localData = reactive({
  offerDay: { value: '2015/08' } as Mo00045Type,
  historyList: [
    {
      // 変更日
      modifiedDay: '2025/05/27',
      // 保険者番号
      insurerNo: '1403',
      // 保険者
      insurer: 'ほのぼの市',
      // 被保険者番号
      insuredPersonNo: '985439859',
      // 連番
      serialNo: '3',
    },
    {
      // 変更日
      modifiedDay: '2025/05/26',
      // 保険者番号
      insurerNo: '1404',
      // 保険者
      insurer: 'ほのぼの市',
      // 被保険者番号
      insuredPersonNo: '985439860',
      // 連番
      serialNo: '4',
    },
  ],
})
const jsonString = ref(JSON.stringify(localData.historyList, null, 2))
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27635"
        >GUI01168_［保険者選択］画面
      </v-btn>
      <g-custom-or-27635
        v-if="showDialogOr27635"
        v-bind="or27635"
        v-model="or27635Type"
        :oneway-model-value="or27635Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">提供日</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="localData.offerDay"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用票履歴リスト</div>
  <div style="margin-left: 20px; width: 400px">
    <base-at-textarea
      v-model="jsonString"
      hide-details="true"
      :oneway-model-value="{ showItemLabel: false }"
    ></base-at-textarea>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr27635_1"> GUI01168_［保険者選択］画面 疎通起動 </v-btn>
  </div>
</template>
