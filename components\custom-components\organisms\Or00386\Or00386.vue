<script lang="ts" setup>
/**
 * Or00386:有機体:［アセスメント］画面（居宅）（4）
 * GUI00797_［アセスメント］画面（居宅）（4）
 *
 * @description
 * ［アセスメント］画面（居宅）（4）
 *
 * <AUTHOR>
 *
 * ToDo 家屋(居室を含む)見取図未実装 削除未実装 複写未実装 印刷未実装
 */

import { ref, reactive, watch, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { TeX0002Logic } from '../../template/TeX0002/TeX0002.logic'
import { Or30736Const } from '../Or30736/Or30736.constants'
import { Or40370Const } from '../Or40370/Or40370.constants'
import type { Or40370OnewayType } from '../Or40370/Or40370.type'
import { Or35672Const } from '../Or35672/Or35672.constants'
import { Or35672Logic } from '../Or35672/Or35672.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { OrX0209Const } from '../OrX0209/OrX0209.constants'
import { Or00386Const } from './Or00386.constants'
import type { CheckboxType, Or00386Type } from './Or00386.type'
import { Or00386Logic } from './Or00386.logic'
import {
  useCmnCom,
  useCmnRouteCom,
  useScreenStore,
  useScreenTwoWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'

import type { OrCD001OnewayType } from '~/types/cmn/business/components/OrCD001Type'
import type { TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
import type { Mo00039Items, Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type {
  assessmentHomeTab4SelectInEntity,
  assessmentHomeTab4SelectOutEntity,
  AssessmentHomeTab4UpdateInEntity,
  AssessmentHomeTab4UpdateOutEnity,
} from '~/repositories/cmn/entities/AssessmentHome4Entity'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { Or00386OnewayType } from '~/types/cmn/business/components/Or00386Type'
import type { OrX0201OnewayType } from '~/types/cmn/business/components/OrX0201Type'
import type {
  IssuesAndGoalListItem,
  OrCD006OnewayType,
  OrCD006Type,
} from '~/types/cmn/business/components/OrCD006Type'
import type { OrX0209Type } from '~/types/cmn/business/components/OrX0209Type'

/****************************************************
 * Props
 ****************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
  onewayModelValue?: Or00386OnewayType
}
const props = defineProps<Props>()

/****************************************************
 * システム共通領域
 ****************************************************/
const systemCommonsStore = useSystemCommonsStore()

/****************************************************
 * 変数定義
 ****************************************************/
const { t } = useI18n()

// 表示パターン制御
const showPattern = ref<boolean>()

// ロード状態制御
const isLoadingRef = ref<boolean>()
// 住居等ラジオボタングループ選択領域
const homeRadioGroup = ref<CodeType[]>([])
// 居室等の状況
const liveingRoomSituationRadioGroup = ref<CodeType[]>([])
// 寝具種類
const beddingTypes = ref<CodeType[]>([])
// エレベーター有無
const hierarchyRadioGroup = ref<CodeType[]>([])
// 陽当り
const sunlightRadioGroup = ref<CodeType[]>([])
// 表示モード区分 冷房 暖房 手すり有無 段差有無
const displayModeCategory = ref<CodeType[]>([])
// (自宅に）浴槽の有無
const bathtubStatus = ref<CodeType[]>([])
// 調理器具の種類
const cookingAppliancesRadio = ref<CodeType[]>([])
/** 住居状況詳細情報 2018年4月1日以前の場合 */
const homeInfoBeforeH21RadioGroup = ref<CodeType[]>([])
/** 住居状況詳細情報 2018年4月1日以降の場合 */
const homeInfoAfterH21RadioGroup = ref<CodeType[]>([])

const or30736 = ref({ uniqueCpId: '' })
const or40370 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })
const orX0209 = ref({ uniqueCpId: '' })

/** 解決すべき課題と目標Ref */
const issuesAndGoalListRef = ref<HTMLElement | null>(null)

/** 画面初期化フラグ */
const initFlg = ref(false)

/**
 * one-way
 */
const localOneway = reactive({
  pageTitle: {
    /** タイトル数字 */
    titleNumber: 4,
    /** タイトル名 */
    title: t('label.family-status'),
  } as OrCD001OnewayType,
  // 住居タイプ
  hometypeSection: {
    familyHome: {
      showItemLabel: false,
      checkOff: true,
    } as Mo00039OnewayType,
  },
  // 全その他入力欄オプション
  mo00045Config: {
    showItemLabel: false,
    width: '206px',
  } as Mo00045OnewayType,
  // 居室等の状況サブセクション
  liveingRoomSituation: {
    // 専用居室の有無
    exclusiveUselivingRoomStatus: {
      showItemLabel: false,
      checkOff: true,
    } as Mo00039OnewayType,
    // 階数チェックボックスグループ
    hierarchy: {
      showItemLabel: false,
    } as Mo00018OnewayType,
  },
  // 寝具種類
  beddingTypes: {
    showItemLabel: false,
    items: [] as Mo00039Items[],
    checkOff: true,
  } as Mo00039OnewayType,
  // 階段数入力欄
  hierarchyInput: {
    appendLabel: t('label.floor-input-append-label'),
    showItemLabel: false,
    width: '80px',
  } as Mo00045OnewayType,
  // 階段数ラジオボタン
  hierarchyRadioGroup: {
    showItemLabel: false,
    checkOff: true,
  } as Mo00039OnewayType,
  // 単なるその他入力欄
  beddingTypeInput: {
    showItemLabel: false,
    width: '209px',
  } as Mo00045OnewayType,
  // トイレ式
  toiletSectionList: [
    {
      check: {
        modelValue: false,
      } as Mo00018Type,
      onewayModelValue: {
        showItemLabel: false,
        checkboxLabel: t('label.toilet-washitsu-label'),
        hideDetails: true,
      } as Mo00018OnewayType,
    },
    {
      check: {
        modelValue: false,
      } as Mo00018Type,
      onewayModelValue: {
        showItemLabel: false,
        checkboxLabel: t('label.toilet-yoshiki-label'),
        hideDetails: true,
      } as Mo00018OnewayType,
    },
    {
      check: {
        modelValue: false,
      } as Mo00018Type,
      onewayModelValue: {
        showItemLabel: false,
        checkboxLabel: t('label.others-note'),
        hideDetails: true,
      } as Mo00018OnewayType,
    },
  ] as CheckboxType[],
  toiletSectionInput: {
    showItemLabel: false,
    width: '209px',
  } as Mo00045OnewayType,
  // (自宅に）浴槽の有無
  bathtubStatus: {
    showItemLabel: false,
    items: [] as Mo00039Items[],
    checkOff: true,
  } as Mo00039OnewayType,
  welfareDevicesUseStatus: {
    showItemLabel: false,
    inline: false,
    items: [] as Mo00039Items[],
    checkOff: true,
  } as Mo00039OnewayType,
  // 移動手段
  welfareDevicesSection: {
    // 室内
    in: [
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.wheelchair-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.electric-wheelchair-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.cane-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.walker-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.others-note'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
    ] as CheckboxType[],
    // 室外
    out: [
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.wheelchair-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.electric-wheelchair-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.cane-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.walker-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.others-note'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
    ] as CheckboxType[],
  },
  welfareDevicesSectionInput: {
    showItemLabel: false,
    width: '80px',
  } as Mo00045OnewayType,
  // 諸設備
  variousFacilities: {
    // ラジオボタングループ パターン①
    radioGroup_1: {
      showItemLabel: false,
      items: [] as Mo00039Items[],
      inline: true,
      checkOff: true,
    } as Mo00039OnewayType,
    checkboxContentList: [
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.gas-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.electric-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.kerosene-label'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
      {
        check: {
          modelValue: false,
        } as Mo00018Type,
        onewayModelValue: {
          showItemLabel: false,
          checkboxLabel: t('label.others-note'),
          hideDetails: true,
        } as Mo00018OnewayType,
      },
    ] as CheckboxType[],
    variousFacilitiesInput: {
      showItemLabel: false,
      width: '175px',
    } as Mo00045OnewayType,
  },
  verticalTextSpecialInputBtn: {
    btnIcon: 'edit_square',
    name: 'specialNoteShouldSolutionIssuesBtn',
    density: 'compact',
  },
  btnTooltip: {
    specialNoteShouldSolutionIssuesBtn: t('tooltip.special-note-should-solution-issues'),
    sentenceMasterBtn: t('tooltip.assessment-home-6-1-sentence-master'),
  },
  or40370Config: {
    maxRows: '9',
    title: t('label.speacil-tab4-label'),
  } as Or40370OnewayType,
  // 表示モード区分 冷房 暖房 手すり有無 段差有無
  displayModeCategory: {
    showItemLabel: false,
    items: [] as Mo00039Items[],
    checkOff: true,
  } as Mo00039OnewayType,
  // 陽当りラジオボタングループ
  sunlightRadioGroup: {
    showItemLabel: false,
    items: [] as Mo00039Items[],
    checkOff: true,
  } as Mo00039OnewayType,
  // 階段数チェックボックス
  hierarchyList: [
    {
      check: {
        modelValue: false,
      } as Mo00018Type,
      onewayModelValue: {
        showItemLabel: false,
        checkboxLabel: t('label.floor1-label'),
        hideDetails: true,
      } as Mo00018OnewayType,
    },
    {
      check: {
        modelValue: false,
      } as Mo00018Type,
      onewayModelValue: {
        showItemLabel: false,
        checkboxLabel: t('label.floor2-label'),
        hideDetails: true,
      } as Mo00018OnewayType,
    },
    {
      check: {
        modelValue: false,
      } as Mo00018Type,
      onewayModelValue: {
        showItemLabel: false,
        checkboxLabel: t('label.others-note'),
        hideDetails: true,
      } as Mo00018OnewayType,
    },
  ] as CheckboxType[],
  // 寝具種類チェックボックス
  beddingTypeList: [
    {
      check: {
        modelValue: false,
      } as Mo00018Type,
      onewayModelValue: {
        showItemLabel: false,
        checkboxLabel: t('label.bedding-fixed'),
        hideDetails: true,
      } as Mo00018OnewayType,
    },
    {
      check: {
        modelValue: false,
      } as Mo00018Type,
      onewayModelValue: {
        showItemLabel: false,
        checkboxLabel: t('label.bedding-hoist'),
        hideDetails: true,
      } as Mo00018OnewayType,
    },
    {
      check: {
        modelValue: false,
      } as Mo00018Type,
      onewayModelValue: {
        showItemLabel: false,
        checkboxLabel: t('label.bedding-electric'),
        hideDetails: true,
      } as Mo00018OnewayType,
    },
    {
      check: {
        modelValue: false,
      } as Mo00018Type,
      onewayModelValue: {
        showItemLabel: false,
        checkboxLabel: t('label.others-note'),
        hideDetails: true,
      } as Mo00018OnewayType,
    },
  ] as CheckboxType[],
  // 特記事項テキストエリア設定
  orX0156Oneway: {
    rows: '7',
    autoGrow: false,
    maxRows: '7',
  } as OrX0156OnewayType,
  // 入力支援画面
  or51775Oneway: {
    mode: '',
  } as Or51775OnewayType,
  // タブタイトル
  orX0201Oneway: {
    title: t('label.family-status'),
    tabNo: '4',
    scrollToUniqueCpId: '',
  } as OrX0201OnewayType,
  // 解決すべき課題と目標一覧
  issuesAndGoalsListOneway: {
    assessmentName:
      t('label.assessment-home-issuesAndGoals-no-1') +
      '\r\n' +
      t('label.assessment-home-issuesAndGoals-name-1'),
  } as OrCD006OnewayType,
})

/**
 * two-way
 */
const local = reactive({
  commonInfo: {} as TeX0002Type,
  issuesAndGoalsList: {
    items: [] as IssuesAndGoalListItem[],
  } as OrCD006Type,
})

/****************************************************
 * computed
 ****************************************************/
const showDialog51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or30736Const.CP_ID(0)]: or30736.value,
  [Or40370Const.CP_ID(0)]: or40370.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
  [OrX0209Const.CP_ID(0)]: orX0209.value,
})

const { refValue } = useScreenTwoWayBind<Or00386Type>({
  cpId: Or00386Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
// 初期値設定
refValue.value = {
  houseShu: '',
  shoyuShu: '',
  shoyuMemoKnj: { value: '' },
  kyoUmu: '',
  kyoKaisuKnj: { value: '' },
  elevatorUmu: '',
  sleepWhere: '',
  bedMemoKnj: { value: '' },
  hiatari: '',
  heater: '',
  airCooling: '',
  toilMemoKnj: { value: '' },
  toilTesuriUmu: '',
  toilDansaUmu: '',
  bathUmu: '',
  bathTesuriUmu: '',
  bathDansaUmu: '',
  hukuOutUse: '',
  outMemoKnj: { value: '' },
  hukuInUse: '',
  inMemoKnj: { value: '' },
  setsubi1: '',
  setsubi2: '',
  setsubi3: '',
  setsubi4: '',
  setsubi5Shu4MemoKnj: { value: '' },
  memoKnj: { value: '' },
  kyoKai1: '',
  kyoKai2: '',
  kyoKai3: '',
  bedShu1: '',
  bedShu2: '',
  bedShu3: '',
  bedShu4: '',
  toilShu1: '',
  toilShu2: '',
  toilShu3: '',
  hukuOut1: '',
  hukuOut2: '',
  hukuOut3: '',
  hukuOut4: '',
  hukuIn1: '',
  hukuIn2: '',
  hukuIn3: '',
  hukuIn4: '',
  hukuIn5: '',
  mondaiUmu1: '',
  mondaiUmu2: '',
  setsubi5Shu1: '',
  setsubi5Shu2: '',
  setsubi5Shu3: '',
  setsubi5Shu4: '',
}

/****************************************************
 * 関数定義
 ****************************************************/
/**
 * 基本動作汎用コード取得
 */
const initCodes = async () => {
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FAMILY_HOME },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LEASE_OWNERSHIP_HOME },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_EXCLUSIVE_USE_LIVING_ROOM_STATUS },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BEDDING_TYPES },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SUNLIGHT },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DISPLAY_MODE_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BATHTUB_UMU },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_WELFARE_DEVICES_USE_STATUS },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_VARIOUS_FACILITIES },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LEASE_OWNERSHIP_HOME_PATTERN2 },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NECESSITY_STATUS },
  ]

  // 汎用コードAPIを呼び出し
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // ラジオボタングループ
  homeRadioGroup.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_FAMILY_HOME)

  // 賃貸・所有・社宅等・公営住宅等
  homeInfoBeforeH21RadioGroup.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_LEASE_OWNERSHIP_HOME_PATTERN2
  )
  homeInfoAfterH21RadioGroup.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_LEASE_OWNERSHIP_HOME
  )

  /**
   * 居室の状況
   */
  // 専用居室の有無
  liveingRoomSituationRadioGroup.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_EXCLUSIVE_USE_LIVING_ROOM_STATUS
  )
  // 寝具種類
  beddingTypes.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_BEDDING_TYPES)
  // エレベーター有無
  hierarchyRadioGroup.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_NECESSITY_STATUS
  )

  // 陽当りラジオボタングループ
  sunlightRadioGroup.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_SUNLIGHT)

  // 表示モード区分 冷房 暖房 手すり有無 段差有無
  displayModeCategory.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DISPLAY_MODE_CATEGORY
  )

  // (自宅に）浴槽の有無
  bathtubStatus.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_BATHTUB_UMU)

  // 福祉機器（室外）（室内）使用状態
  localOneway.welfareDevicesUseStatus.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_WELFARE_DEVICES_USE_STATUS
  )

  // 調理器具
  cookingAppliancesRadio.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_VARIOUS_FACILITIES
  )

  initFlg.value = true
}

const initControl = () => {
  localOneway.orX0201Oneway.scrollToUniqueCpId = orX0209.value.uniqueCpId
}

/**
 * チェックボックス関連値初期化
 *
 * @param arr - チェックボックス配列
 *
 * @param findIndexArr - インデックス
 */
const initCheckboxData = (arr: CheckboxType[], findIndexArr: (string | undefined)[]) => {
  // 対応するインデックスに変更
  findIndexArr.forEach((item, index) => {
    if (item === '1') {
      arr[index].check.modelValue = true
    }
  })
}

/**
 * 削除処理
 */
const _delete = () => {}

/**
 * 共通情報取得
 */
const getCommonInfo = () => {
  const commonInfo = TeX0002Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo.activeTabId = commonInfo.activeTabId
    local.commonInfo.jigyoId = commonInfo.jigyoId
    local.commonInfo.sc1Id = commonInfo.sc1Id
    local.commonInfo.gdlId = commonInfo.gdlId
    local.commonInfo.createUserId = commonInfo.createUserId
    local.commonInfo.createYmd = commonInfo.createYmd
    local.commonInfo.ninteiFormF = commonInfo.ninteiFormF
    local.commonInfo.copyData = commonInfo.copyData

    // 作成時間をチェックし、表示パターンフラグを設定する
    const createTime = new Date('2018/04/01').getTime()
    const commonCreateYmd = new Date(commonInfo.createYmd!).getTime()

    // 解決すべき課題と目標一覧パラメータ設定
    localOneway.issuesAndGoalsListOneway.gdlId = local.commonInfo.gdlId
    localOneway.issuesAndGoalsListOneway.sc1Id = local.commonInfo.sc1Id
    localOneway.issuesAndGoalsListOneway.assNo = Or00386Const.DEFAULT.TAB_ID
    localOneway.issuesAndGoalsListOneway.userId = local.commonInfo.userId

    local.issuesAndGoalsList.items =
      cloneDeep(commonInfo.issuesAndGoalsList) ?? ([] as IssuesAndGoalListItem[])
    if (local.commonInfo.ninteiFormF === '4') {
      if (commonCreateYmd >= createTime) {
        showPattern.value = true
      } else {
        showPattern.value = false
      }
    } else if (local.commonInfo.ninteiFormF === '5') {
      showPattern.value = true
    }
  }
}

// 初期情報取得
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: assessmentHomeTab4SelectInEntity = {
    /** 計画期間ID */
    sc1Id: local.commonInfo.sc1Id ?? '',
    /** アセスメントID */
    gdlId: local.commonInfo.gdlId ?? '',
    /** 改訂フラグ */
    ninteiFormF: local.commonInfo.ninteiFormF ?? '',
    /** 作成日 */
    kijunbiYmd: local.commonInfo.createYmd ?? '',
  }

  const resData: assessmentHomeTab4SelectOutEntity = await ScreenRepository.select(
    'assessmentHomeHousingInitSelect',
    inputData
  )

  if (resData) {
    setFormData(resData)
  }
}
/**
 * フォームデータ設定
 *
 * @param resData - APIまたは複写画面から取得したデータ
 */
const setFormData = (resData: assessmentHomeTab4SelectOutEntity) => {
  // 複写モードの場合、APIから取得のデータを保持
  if (props.onewayModelValue!.mode === Or00386Const.DEFAULT.MODE_COPY) {
    Or00386Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        copyData: resData,
      },
    })
  }

  // 改定フラグチェック
  if (local.commonInfo.ninteiFormF === '4') {
    // 作成日チェック
    // H30改訂版
    if (showPattern.value) {
      const {
        houseShu,
        shoyuShu,
        shoyuMemoKnj,
        hiraya,
        kaidate,
        shugou,
        kaisu1,
        kaisu2,
        kyoUmu,
        kyoKai1,
        kyoKai2,
        kyoKai3,
        kyoKaisu,
        elevatorUmu,
        sleepWhere,
        bedShu1,
        bedShu2,
        bedShu3,
        bedShu4,
        bedMemoKnj,
        hiatari,
        heater,
        airCooling,
        toilShu1,
        toilShu2,
        toilShu3,
        toilMemoKnj,
        toilTesuriUmu,
        toilDansaUmu,
        bathUmu,
        bathTesuriUmu,
        bathDansaUmu,
        hukuOutUse,
        hukuOut1,
        hukuOut2,
        hukuOut3,
        hukuOut4,
        hukuOut5,
        outMemoKnj,
        hukuInUse,
        hukuIn1,
        hukuIn2,
        hukuIn3,
        hukuIn4,
        hukuIn5,
        inMemoKnj,
        setsubi1,
        setsubi2,
        setsubi3,
        mondaiUmu1,
        mondaiUmu2,
        mondaiKnj,
        memoKnj,
        setsubi4,
        setsubi5Shu1,
        setsubi5Shu2,
        setsubi5Shu3,
        setsubi5Shu4,
        setsubi5Shu4MemoKnj,
      } = resData.data.gdl4HouH30Info
      // テキストフィールド初期化
      refValue.value = {
        houseShu,
        shoyuShu,
        shoyuMemoKnj: { value: shoyuMemoKnj },
        hiraya,
        kaidate,
        shugou,
        kaisu1,
        kaisu2,
        kyoUmu,
        kyoKai1,
        kyoKai2,
        kyoKai3,
        kyoKaisuKnj: { value: kyoKaisu },
        elevatorUmu,
        sleepWhere,
        bedShu1,
        bedShu2,
        bedShu3,
        bedShu4,
        bedMemoKnj: { value: bedMemoKnj },
        hiatari,
        heater,
        airCooling,
        toilShu1,
        toilShu2,
        toilShu3,
        toilMemoKnj: { value: toilMemoKnj },
        toilTesuriUmu,
        toilDansaUmu,
        bathUmu,
        bathTesuriUmu,
        bathDansaUmu,
        hukuOutUse,
        hukuOut1,
        hukuOut2,
        hukuOut3,
        hukuOut4,
        hukuOut5,
        outMemoKnj: { value: outMemoKnj },
        hukuInUse,
        hukuIn1,
        hukuIn2,
        hukuIn3,
        hukuIn4,
        hukuIn5,
        inMemoKnj: { value: inMemoKnj },
        setsubi1,
        setsubi2,
        setsubi3,
        mondaiUmu1,
        mondaiUmu2,
        mondaiKnj,
        memoKnj: { value: memoKnj },
        setsubi4,
        setsubi5Shu1,
        setsubi5Shu2,
        setsubi5Shu3,
        setsubi5Shu4,
        setsubi5Shu4MemoKnj: { value: setsubi5Shu4MemoKnj },
      }
      initCheckboxData(localOneway.hierarchyList, [kyoKai1, kyoKai2, kyoKai3])
      // 寝具種類
      initCheckboxData(localOneway.beddingTypeList, [bedShu1, bedShu2, bedShu3, bedShu4])
      // トイレ式
      initCheckboxData(localOneway.toiletSectionList, [toilShu1, toilShu2, toilShu3])
      // 室外福祉機械
      initCheckboxData(localOneway.welfareDevicesSection.out, [
        hukuOut1,
        hukuOut2,
        hukuOut3,
        hukuOut4,
        hukuOut5,
      ])
      // 室内福祉機械
      initCheckboxData(localOneway.welfareDevicesSection.in, [
        hukuIn1,
        hukuIn2,
        hukuIn3,
        hukuIn4,
        hukuIn5,
      ])
      // 暖房器具
      initCheckboxData(localOneway.variousFacilities.checkboxContentList, [
        setsubi5Shu1,
        setsubi5Shu2,
        setsubi5Shu3,
        setsubi5Shu4,
      ])
    } else {
      const {
        houseShu,
        shoyuShu,
        shoyuMemoKnj,
        hiraya,
        kaidate,
        shugou,
        kaisu1,
        kaisu2,
        kyoUmu,
        kyoKai1,
        kyoKai2,
        kyoKai3,
        kyoKaisu,
        elevatorUmu,
        sleepWhere,
        bedShu1,
        bedShu2,
        bedShu3,
        bedShu4,
        bedMemoKnj,
        hiatari,
        heater,
        airCooling,
        toilShu1,
        toilShu2,
        toilShu3,
        toilMemoKnj,
        toilTesuriUmu,
        toilDansaUmu,
        bathUmu,
        bathTesuriUmu,
        bathDansaUmu,
        hukuOutUse,
        hukuOut1,
        hukuOut2,
        hukuOut3,
        hukuOut4,
        hukuOut5,
        outMemoKnj,
        hukuInUse,
        hukuIn1,
        hukuIn2,
        hukuIn3,
        hukuIn4,
        hukuIn5,
        inMemoKnj,
        setsubi1,
        setsubi2,
        setsubi3,
        mondaiUmu1,
        mondaiUmu2,
        mondaiKnj,
        memoKnj,
      } = resData.data.gdl4HouH21Info

      // テキストフィールド初期化
      refValue.value = {
        houseShu,
        shoyuShu,
        shoyuMemoKnj: { value: shoyuMemoKnj },
        hiraya,
        kaidate,
        shugou,
        kaisu1,
        kaisu2,
        kyoUmu,
        kyoKai1,
        kyoKai2,
        kyoKai3,
        kyoKaisuKnj: { value: kyoKaisu },
        elevatorUmu,
        sleepWhere,
        bedShu1,
        bedShu2,
        bedShu3,
        bedShu4,
        bedMemoKnj: { value: bedMemoKnj },
        hiatari,
        heater,
        airCooling,
        toilShu1,
        toilShu2,
        toilShu3,
        toilMemoKnj: { value: toilMemoKnj },
        toilTesuriUmu,
        toilDansaUmu,
        bathUmu,
        bathTesuriUmu,
        bathDansaUmu,
        hukuOutUse,
        hukuOut1,
        hukuOut2,
        hukuOut3,
        hukuOut4,
        hukuOut5,
        outMemoKnj: { value: outMemoKnj },
        hukuInUse,
        hukuIn1,
        hukuIn2,
        hukuIn3,
        hukuIn4,
        hukuIn5,
        inMemoKnj: { value: inMemoKnj },
        setsubi1,
        setsubi2,
        setsubi3,
        mondaiUmu1,
        mondaiUmu2,
        mondaiKnj,
        memoKnj: { value: memoKnj },
        setsubi4: '',
        setsubi5Shu1: '',
        setsubi5Shu2: '',
        setsubi5Shu3: '',
        setsubi5Shu4: '',
        setsubi5Shu4MemoKnj: { value: '' },
      }
    }
  } else if (local.commonInfo.ninteiFormF === '5') {
    const {
      houseShu,
      shoyuShu,
      shoyuMemoKnj,
      hiraya,
      kaidate,
      shugou,
      kaisu1,
      kaisu2,
      kyoUmu,
      kyoKai1,
      kyoKai2,
      kyoKai3,
      kyoKaisu,
      elevatorUmu,
      sleepWhere,
      bedShu1,
      bedShu2,
      bedShu3,
      bedShu4,
      bedMemoKnj,
      hiatari,
      heater,
      airCooling,
      toilShu1,
      toilShu2,
      toilShu3,
      toilMemoKnj,
      toilTesuriUmu,
      toilDansaUmu,
      bathUmu,
      bathTesuriUmu,
      bathDansaUmu,
      hukuOutUse,
      hukuOut1,
      hukuOut2,
      hukuOut3,
      hukuOut4,
      hukuOut5,
      outMemoKnj,
      hukuInUse,
      hukuIn1,
      hukuIn2,
      hukuIn3,
      hukuIn4,
      hukuIn5,
      inMemoKnj,
      setsubi1,
      setsubi2,
      setsubi3,
      mondaiUmu1,
      mondaiUmu2,
      mondaiKnj,
      memoKnj,
      setsubi4,
      setsubi5Shu1,
      setsubi5Shu2,
      setsubi5Shu3,
      setsubi5Shu4,
      setsubi5Shu4MemoKnj,
    } = resData.data.gdl5HouInfo
    // テキストフィールド初期化
    refValue.value = {
      houseShu,
      shoyuShu,
      shoyuMemoKnj: { value: shoyuMemoKnj },
      hiraya,
      kaidate,
      shugou,
      kaisu1,
      kaisu2,
      kyoUmu,
      kyoKai1,
      kyoKai2,
      kyoKai3,
      kyoKaisuKnj: { value: kyoKaisu },
      elevatorUmu,
      sleepWhere,
      bedShu1,
      bedShu2,
      bedShu3,
      bedShu4,
      bedMemoKnj: { value: bedMemoKnj },
      hiatari,
      heater,
      airCooling,
      toilShu1,
      toilShu2,
      toilShu3,
      toilMemoKnj: { value: toilMemoKnj },
      toilTesuriUmu,
      toilDansaUmu,
      bathUmu,
      bathTesuriUmu,
      bathDansaUmu,
      hukuOutUse,
      hukuOut1,
      hukuOut2,
      hukuOut3,
      hukuOut4,
      hukuOut5,
      outMemoKnj: { value: outMemoKnj },
      hukuInUse,
      hukuIn1,
      hukuIn2,
      hukuIn3,
      hukuIn4,
      hukuIn5,
      inMemoKnj: { value: inMemoKnj },
      setsubi1,
      setsubi2,
      setsubi3,
      mondaiUmu1,
      mondaiUmu2,
      mondaiKnj,
      memoKnj: { value: memoKnj },
      setsubi4,
      setsubi5Shu1,
      setsubi5Shu2,
      setsubi5Shu3,
      setsubi5Shu4,
      setsubi5Shu4MemoKnj: { value: setsubi5Shu4MemoKnj },
    }
    initCheckboxData(localOneway.hierarchyList, [kyoKai1, kyoKai2, kyoKai3])
    // 寝具種類
    initCheckboxData(localOneway.beddingTypeList, [bedShu1, bedShu2, bedShu3, bedShu4])
    // トイレ式
    initCheckboxData(localOneway.toiletSectionList, [toilShu1, toilShu2, toilShu3])
    // 室外福祉機械
    initCheckboxData(localOneway.welfareDevicesSection.out, [
      hukuOut1,
      hukuOut2,
      hukuOut3,
      hukuOut4,
      hukuOut5,
    ])
    // 室内福祉機械
    initCheckboxData(localOneway.welfareDevicesSection.in, [
      hukuIn1,
      hukuIn2,
      hukuIn3,
      hukuIn4,
      hukuIn5,
    ])
    // 暖房器具
    initCheckboxData(localOneway.variousFacilities.checkboxContentList, [
      setsubi5Shu1,
      setsubi5Shu2,
      setsubi5Shu3,
      setsubi5Shu4,
    ])
  }
  // 全処理済み、Piniaに初期値を設定する
  useScreenStore().setCpTwoWay({
    cpId: Or00386Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

// 保存するためのチェックボックス値処理
const formatCheckboxValue = (value: boolean) => {
  if (value) {
    return '1'
  }
  return '0'
}

/**
 * 各チェックボックスデータ変更用関数
 *
 * @param key - 該当チェックボックス対応するキー
 *
 * @param value - データ
 */
const checkBoxDataChange = (key: string, value: boolean) => {
  console.log(key, value)
  const keys = Object.keys(refValue.value!)
  if (keys.includes(key)) {
    refValue.value![key] = value ? '1' : '0'
  }
}

/**
 * 保存ボタン押下
 */
const userSave = async () => {
  isLoadingRef.value = true
  try {
    // 画面.履歴更新区分<>"D"の場合、以下の設定を行う
    // ・画面.アセスメントID＝0の場合、 履歴更新区分＝"C"
    // ・画面.アセスメントID<>0の場合、履歴更新区分＝"U"
    let updateKbn = ''
    if (local.commonInfo.updateKbn !== UPDATE_KBN.DELETE) {
      if (local.commonInfo.gdlId === '0') {
        updateKbn = UPDATE_KBN.CREATE
      } else {
        updateKbn = UPDATE_KBN.UPDATE
      }
    } else {
      updateKbn = UPDATE_KBN.DELETE
    }
    const userName =
      (systemCommonsStore.getUserSelectUserInfo()?.nameSei ?? '管理者') +
      (systemCommonsStore.getUserSelectUserInfo()?.nameMei ?? '太郎')
    // 課題リストを取得する
    const childrenRefValue = useScreenUtils().getChildCpBinds(props.uniqueCpId, {
      [OrX0209Const.CP_ID(0)]: { cpPath: OrX0209Const.CP_ID(0), twoWayFlg: true },
    })
    let historyUpdateKbn = UPDATE_KBN.UPDATE
    if (local.commonInfo.gdlId === '0') {
      historyUpdateKbn = UPDATE_KBN.CREATE
    } else if (local.commonInfo.deleteKbn === '2') {
      historyUpdateKbn = UPDATE_KBN.DELETE
    } else if (local.commonInfo.deleteKbn === '1') {
      updateKbn = UPDATE_KBN.DELETE
    }

    const kadaiList = childrenRefValue[OrX0209Const.CP_ID(0)].twoWayBind?.value as OrX0209Type

    const inputData: AssessmentHomeTab4UpdateInEntity = {
      tabId: Or00386Const.DEFAULT.TAB_ID,
      gdlId: local.commonInfo.gdlId ?? '',
      sc1Id: local.commonInfo.sc1Id ?? '',
      ninteiFormF: local.commonInfo.ninteiFormF ?? '',
      kikanFlg: local.commonInfo.kikanKanriFlg ?? '0',
      updateKbn,
      deleteKbn: local.commonInfo.deleteKbn ?? '0',
      planningPeriodNo: local.commonInfo.historyNo ?? '',
      edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
      edocumentUseParam: useCmnCom().getEDocumentParam(),
      startYmd: local.commonInfo.periodStartYmd ?? '',
      endYmd: local.commonInfo.periodEndYmd ?? '',
      krirekiNo: local.commonInfo.historyNo ?? '2',
      kinoId: '1',
      matomeFlg: useCmnRouteCom().getInitialSettingMaster()?.gdlMatomeFlg ?? '',
      loginId: systemCommonsStore.getCurrentUser.loginId ?? '',
      sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '3GK',
      shokuId: systemCommonsStore.getCurrentUser.chkShokuId ?? '',
      sysCd: systemCommonsStore.getSystemCode ?? '0',
      svJigyoKnj: local.commonInfo.jigyoKnj ?? '事業所名称A',
      createUserName: local.commonInfo.createUserName ?? '管理者　太郎',
      userName: userName,
      houjinId: systemCommonsStore.getHoujinId ?? '',
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      userId: local.commonInfo.userId ?? '00000001',
      svJigyoId: local.commonInfo.jigyoId ?? '',
      syubetsuId: systemCommonsStore.getSyubetu ?? '',
      historyUpdateKbn: historyUpdateKbn,
      kijunbiYmd: local.commonInfo.createYmd ?? '',
      sakuseiId: local.commonInfo.createUserId ?? '',
      houInfo: {
        shoyuShu: refValue.value?.shoyuShu ?? '',
        shoyuMemoKnj: refValue.value?.shoyuMemoKnj.value ?? '',
        memoKnj: refValue.value?.memoKnj.value ?? '',
        inMemoKnj: refValue.value?.inMemoKnj.value ?? '',
        outMemoKnj: refValue.value?.outMemoKnj.value ?? '',
        bedMemoKnj: refValue.value?.bedMemoKnj.value ?? '',
        toilMemoKnj: refValue.value?.toilMemoKnj.value ?? '',
        setsubi5Shu4MemoKnj: refValue.value?.setsubi5Shu4MemoKnj.value ?? '',
        houseShu: refValue.value?.houseShu ?? '',
        kyoUmu: refValue.value?.kyoUmu ?? '',
        kyoKai1: formatCheckboxValue(localOneway.hierarchyList[0].check.modelValue),
        kyoKai2: formatCheckboxValue(localOneway.hierarchyList[1].check.modelValue),
        kyoKai3: formatCheckboxValue(localOneway.hierarchyList[2].check.modelValue),
        kyoKaisu: refValue.value?.kyoKaisuKnj.value ?? '',
        elevatorUmu: refValue.value?.elevatorUmu ?? '',
        sleepWhere: refValue.value?.sleepWhere ?? '',
        bedShu1: formatCheckboxValue(localOneway.beddingTypeList[0].check.modelValue),
        bedShu2: formatCheckboxValue(localOneway.beddingTypeList[1].check.modelValue),
        bedShu3: formatCheckboxValue(localOneway.beddingTypeList[2].check.modelValue),
        bedShu4: formatCheckboxValue(localOneway.beddingTypeList[3].check.modelValue),
        hiatari: refValue.value?.hiatari ?? '',
        heater: refValue.value?.heater ?? '',
        airCooling: refValue.value?.airCooling ?? '',
        toilShu1: formatCheckboxValue(localOneway.toiletSectionList[0].check.modelValue),
        toilShu2: formatCheckboxValue(localOneway.toiletSectionList[1].check.modelValue),
        toilShu3: formatCheckboxValue(localOneway.toiletSectionList[2].check.modelValue),
        toilTesuriUmu: refValue.value?.toilTesuriUmu ?? '',
        toilDansaUmu: refValue.value?.toilDansaUmu ?? '',
        bathUmu: refValue.value?.bathUmu ?? '',
        bathTesuriUmu: refValue.value?.bathTesuriUmu ?? '',
        bathDansaUmu: refValue.value?.bathDansaUmu ?? '',
        hukuOutUse: refValue.value?.hukuOutUse ?? '',
        hukuOut1: formatCheckboxValue(localOneway.welfareDevicesSection.out[0].check.modelValue),
        hukuOut2: formatCheckboxValue(localOneway.welfareDevicesSection.out[1].check.modelValue),
        hukuOut3: formatCheckboxValue(localOneway.welfareDevicesSection.out[2].check.modelValue),
        hukuOut4: formatCheckboxValue(localOneway.welfareDevicesSection.out[3].check.modelValue),
        hukuOut5: formatCheckboxValue(localOneway.welfareDevicesSection.out[4].check.modelValue),
        hukuInUse: refValue.value?.hukuInUse ?? '',
        hukuIn1: formatCheckboxValue(localOneway.welfareDevicesSection.in[0].check.modelValue),
        hukuIn2: formatCheckboxValue(localOneway.welfareDevicesSection.in[1].check.modelValue),
        hukuIn3: formatCheckboxValue(localOneway.welfareDevicesSection.in[2].check.modelValue),
        hukuIn4: formatCheckboxValue(localOneway.welfareDevicesSection.in[3].check.modelValue),
        hukuIn5: formatCheckboxValue(localOneway.welfareDevicesSection.in[4].check.modelValue),
        setsubi1: refValue.value?.setsubi1 ?? '',
        setsubi2: refValue.value?.setsubi2 ?? '',
        setsubi3: refValue.value?.setsubi3 ?? '',
        setsubi4: refValue.value?.setsubi4 ?? '',
        mitoriDraw: '',
        shukushoFlg: '',
        setsubi5Shu1: formatCheckboxValue(
          localOneway.variousFacilities.checkboxContentList[0].check.modelValue
        ),
        setsubi5Shu2: formatCheckboxValue(
          localOneway.variousFacilities.checkboxContentList[1].check.modelValue
        ),
        setsubi5Shu3: formatCheckboxValue(
          localOneway.variousFacilities.checkboxContentList[2].check.modelValue
        ),
        setsubi5Shu4: formatCheckboxValue(
          localOneway.variousFacilities.checkboxContentList[3].check.modelValue
        ),
      },
      kadaiList: kadaiList.items.map((item) => {
        const assNo =
          item.updateKbn === UPDATE_KBN.CREATE ? Or00386Const.DEFAULT.TAB_ID : item.assNo
        return {
          kadaiKnj: item.wholeLifeShouldSolutionIssues.value,
          tankiKnj: item.shorttermGoal.value,
          choukiKnj: item.longtermGoal.value,
          updateKbn: item.updateKbn ?? '',
          seq: item.seq.toString(),
          id: item.dataId ?? '',
          assNo,
        }
      }),
    }
    const resData: AssessmentHomeTab4UpdateOutEnity = await ScreenRepository.update(
      'assessmentHomeHousingUpdate',
      inputData
    )
    if (resData) {
      TeX0002Logic.data.set({
        uniqueCpId: props.parentUniqueCpId,
        value: {
          updateData: {
            errKbn: resData.data.errKbn ?? '',
            gdlId: resData.data.gdlId,
            sc1Id: resData.data.sc1Id,
          },
        },
      })
      TeX0002Logic.state.set({
        uniqueCpId: props.parentUniqueCpId,
        state: {
          isSaveCompleted: true,
        },
      })
    }
    isLoadingRef.value = false
  } catch (e: unknown) {
    console.log(e)
    isLoadingRef.value = false
  }
}

/**
 * 新規イベント
 */
const createNew = () => {
  // 全項目クリア
  for (const key in local) {
    if (key.includes('Knj')) {
      // テキストフィールド
      ;(refValue.value![key] as Mo00045Type).value = ''
    } else if (key !== 'commonInfo') {
      // ラジオボタン
      refValue.value![key] = ''
    }
  }
  // チェックボックス
  localOneway.hierarchyList.forEach((item) => {
    item.check.modelValue = false
  })
  localOneway.beddingTypeList.forEach((item) => {
    item.check.modelValue = false
  })
  localOneway.toiletSectionList.forEach((item) => {
    item.check.modelValue = false
  })
  localOneway.welfareDevicesSection.in.forEach((item) => {
    item.check.modelValue = false
  })
  localOneway.welfareDevicesSection.out.forEach((item) => {
    item.check.modelValue = false
  })
  localOneway.variousFacilities.checkboxContentList.forEach((item) => {
    item.check.modelValue = false
  })
}

/**
 * 「特記事項アイコンボタン」押下
 */
const setShowOr51775Dialog = () => {
  // GUI00937 ［入力支援［ケアマネ］］画面をポップアップで起動する。
  // 起動パラメータ
  // タイトル : "立地環境上の問題"
  localOneway.or51775Oneway.title = '立地環境上の問題'
  // 画面ID:"GUI00797"
  localOneway.or51775Oneway.screenId = 'GUI00797'
  // 分類ID:””
  localOneway.or51775Oneway.bunruiId = ''
  // 大分類CD：602
  localOneway.or51775Oneway.t1Cd = '602'
  // 中分類CD：4
  localOneway.or51775Oneway.t2Cd = '4'
  // 小分類CD：0
  localOneway.or51775Oneway.t3Cd = '0'
  // テーブル名："cpn_tuc_gdl_hou11"
  localOneway.or51775Oneway.tableName = 'cpn_tuc_gdl_hou11'
  // カラム名："mondai_knj"
  localOneway.or51775Oneway.columnName = 'mondai_knj'
  // アセスメント方式：共通情報.アセスメント方式
  localOneway.or51775Oneway.assessmentMethod =
    useCmnRouteCom()?.getInitialSettingMaster()?.cpnFlg ?? ''
  // 文章内容：画面.特記事項
  localOneway.or51775Oneway.inputContents = refValue.value?.memoKnj.value ?? ''
  // 利用者ID：親画面.利用者ID
  localOneway.or51775Oneway.userId = local.commonInfo.userId ?? ''

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 入力支援画面返却値設定
 *
 * @param result - 返却値
 */
const or51775ConfirmResult = (result: Or51775ConfirmType) => {
  if (result.type === Or00386Const.DEFAULT.INPUT_SUPPORT_ADD) {
    refValue.value!.memoKnj.value += result.value
    return
  }
  if (result.type === Or00386Const.DEFAULT.INPUT_SUPPORT_OVERWRITE) {
    refValue.value!.memoKnj.value = result.value
    return
  }
}

/**
 * コントロール初期化
 */
const reload = async (): Promise<void> => {
  isLoadingRef.value = true
  // 画面初期情報取得
  await initCodes()
  await getInitDataInfo()
  isLoadingRef.value = false
}

/**
 * Or35672イベント発火
 *
 * @param event - イベント
 */
const setOr35672Event = (event: Record<string, boolean>) => {
  Or35672Logic.event.set({
    uniqueCpId: Or35672Const.CP_ID(0) + props.parentUniqueCpId,
    events: event,
  })
}

/**
 *  複写画面から共通情報を取得
 */
const getCommonInfoFromCopy = () => {
  const commonInfo = Or35672Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo.ninteiFormF = commonInfo.params!.ninteiFormF
    local.commonInfo.activeTabId = commonInfo.params!.activeTabId
    local.commonInfo.jigyoId = commonInfo.params!.jigyoId
    local.commonInfo.sc1Id = commonInfo.params!.sc1Id
    local.commonInfo.gdlId = commonInfo.params!.gdlId
    local.commonInfo.createUserId = commonInfo.params!.createUserId
    local.commonInfo.createYmd = commonInfo.params!.createYmd

    // 作成時間をチェックし、表示パターンフラグを設定する
    const createTime = new Date('2018/04/01').getTime()
    const commonCreateYmd = new Date(commonInfo.params!.createYmd ?? '2018/04/01').getTime()
    if (commonCreateYmd >= createTime) {
      showPattern.value = true
    } else {
      showPattern.value = false
    }
  }
}

/**
 * 解決すべき課題と目標にスクロール関数
 */
const scrollToIssues = () => {
  if (issuesAndGoalListRef.value) {
    const scrollTop = issuesAndGoalListRef.value.offsetTop
    window.scroll({ top: scrollTop, behavior: 'smooth' })
  }
}

/**
 * 画面初期化処理
 */
onMounted(() => {
  initControl()
})

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0002Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    // 画面共通情報を取得
    getCommonInfo()

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or00386Const.DEFAULT.TAB_ID) {
      return
    }

    // 再表示発火フラグ
    if (newValue.isRefresh) {
      await reload()
    }

    if (newValue.isCreateDateChanged) {
      await reload()
    }

    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await userSave()
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      createNew()
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      const copyData = local.commonInfo.copyData as assessmentHomeTab4SelectOutEntity
      console.log(copyData)
      setFormData(copyData)
    }
  },
  { immediate: true, deep: true }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or35672Logic.event.get(Or35672Const.CP_ID(0) + props.parentUniqueCpId),
  async (newValue) => {
    if (newValue?.reload === false) {
      return
    }

    setOr35672Event({ reload: false })

    // 複写画面から共通情報を取得
    getCommonInfoFromCopy()

    // 本画面が表示画面ではない場合、スキップ
    if (local.commonInfo.activeTabId !== Or00386Const.DEFAULT.TAB_ID) {
      return
    }

    // 画面再表示
    if (newValue?.reload) {
      // 画面情報再取得
      await reload()
    }
  }
)
</script>

<template>
  <c-v-sheet
    id="Or00386Component"
    class="background-color d-flex flex-column"
    style="width: 1080px"
  >
    <v-overlay
      :model-value="isLoadingRef"
      :persistent="true"
      class="align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      >
      </v-progress-circular
    ></v-overlay>
    <!-- 画面タイトル -->
    <div class="mb-4">
      <g-custom-orX0201
        :oneway-model-value="localOneway.orX0201Oneway"
        @on-click-link-btn="scrollToIssues"
      />
    </div>
    <!-- 住居 -->
    <c-v-col class="c-sub-content family-home">
      <div
        v-if="initFlg"
        class="or-content align-center"
      >
        <!--  -->
        <base-mo00039
          v-model="refValue!.houseShu"
          :oneway-model-value="localOneway.hometypeSection.familyHome"
        >
          <div
            v-for="(item, index) in homeRadioGroup"
            :key="index"
            class="radio-item mr-4"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
        <base-mo00039
          v-if="!showPattern"
          v-model="refValue!.shoyuShu"
          :oneway-model-value="localOneway.hometypeSection.familyHome"
        >
          <div
            v-for="(item, index) in homeInfoBeforeH21RadioGroup"
            :key="index"
            class="radio-item mr-4"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
        <base-mo00039
          v-if="showPattern"
          v-model="refValue!.shoyuShu"
          :oneway-model-value="localOneway.hometypeSection.familyHome"
        >
          <div
            v-for="(item, index) in homeInfoAfterH21RadioGroup"
            :key="index"
            class="radio-item mr-4"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
        <base-mo00045
          v-model="refValue!.shoyuMemoKnj"
          :oneway-model-value="localOneway.mo00045Config"
        />
      </div>
    </c-v-col>

    <!-- 居室等の状況タイトル -->
    <c-v-col class="c-sub-title">
      {{ t('label.living-room-status') }}
    </c-v-col>
    <!-- 居室等の状況コンテンツエリア -->
    <c-v-col class="c-sub-content">
      <!-- あ選択肢 -->
      <c-v-col
        v-if="initFlg"
        class="or-content show-border align-center pb-4"
      >
        <span class="mr-2">{{ t('label.assessment-home-6-4-vision-a') }}</span>
        <base-mo00039
          v-model="refValue!.kyoUmu"
          :oneway-model-value="localOneway.liveingRoomSituation.exclusiveUselivingRoomStatus"
        >
          <div
            v-for="(item, index) in liveingRoomSituationRadioGroup"
            :key="index"
            style="height: 36px; margin-right: 40px"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
      </c-v-col>
      <!-- 階数 -->
      <c-v-col
        v-if="initFlg"
        class="or-content show-border align-center pb-4"
      >
        <span class="mr-2">{{ t('label.assessment-home-6-4-vision-i') }}</span>
        <div
          v-for="(item, index) in localOneway.hierarchyList"
          :key="index"
          :class="{ 'mr-12': index < 2 }"
        >
          <base-mo00018
            v-model="item.check"
            :oneway-model-value="item.onewayModelValue"
            @change="
              checkBoxDataChange(
                `kyoKai${index + 1}`,
                localOneway.hierarchyList[index].check.modelValue
              )
            "
          />
        </div>
        <div class="mr-13 ml-9">
          <base-mo00045
            v-model="refValue!.kyoKaisuKnj"
            :oneway-model-value="localOneway.hierarchyInput"
          />
        </div>
        <!-- エレベーター有無 -->
        <span class="mr-10">{{ t('label.floor-elevator-label') }}</span>
        <base-mo00039
          v-model="refValue!.elevatorUmu"
          :oneway-model-value="localOneway.hierarchyRadioGroup"
        >
          <div
            v-for="(item, index) in hierarchyRadioGroup"
            :key="index"
            class="radio-item"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
      </c-v-col>
      <!-- 寝具種類 -->
      <c-v-col
        v-if="initFlg"
        class="or-content show-border align-center pb-4"
      >
        <span class="mr-2">{{ t('label.assessment-home-6-4-vision-u') }}</span>
        <base-mo00039
          v-model="refValue!.sleepWhere"
          :oneway-model-value="localOneway.beddingTypes"
        >
          <div
            v-for="(item, index) in beddingTypes"
            :key="index"
            style="margin-right: 52px; height: 36px"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
        <div
          class="d-flex align-center"
          style="margin-left: -22px"
        >
          <div
            v-for="(item, index) in localOneway.beddingTypeList.slice(0, 3)"
            :key="index"
            class="pr-9"
          >
            <base-mo00018
              v-model="item.check"
              :oneway-model-value="item.onewayModelValue"
              @change="checkBoxDataChange(`bedShu${index + 1}`, item.check.modelValue)"
            />
          </div>
        </div>
        <!-- 単なるその他と入力欄 -->
        <c-v-col class="d-flex">
          <base-mo00018
            v-for="(item, index) in localOneway.beddingTypeList.slice(3, 4)"
            :key="index"
            v-model="item.check"
            :oneway-model-value="item.onewayModelValue"
            @change="checkBoxDataChange(`bedShu4`, item.check.modelValue)"
          />
          <base-mo00045
            v-model="refValue!.bedMemoKnj"
            :oneway-model-value="localOneway.beddingTypeInput"
          />
        </c-v-col>
      </c-v-col>
      <!-- 陽当り -->
      <c-v-col
        v-if="initFlg"
        class="or-content show-border align-center pb-4"
      >
        <span>{{ t('label.assessment-home-6-4-vision-e') }}</span>
        <span class="ml-3 mr-14">{{ t('label.hiatari-label') }}</span>
        <base-mo00039
          v-model="refValue!.hiatari"
          :oneway-model-value="localOneway.sunlightRadioGroup"
        >
          <div
            v-for="(item, index) in sunlightRadioGroup"
            :key="index"
            style="margin-right: 52px; height: 36px"
            :style="{ 'margin-right': index === 0 ? '67px' : '54px' }"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
      </c-v-col>
      <!-- 暖房 -->
      <c-v-col
        v-if="initFlg"
        class="or-content show-border align-center pb-4"
      >
        <span>{{ t('label.assessment-home-6-4-vision-o') }}</span>
        <span class="ml-3 mr-21">{{ t('label.heater-label') }}</span>
        <base-mo00039
          v-model="refValue!.heater"
          :oneway-model-value="localOneway.displayModeCategory"
        >
          <div
            v-for="(item, index) in displayModeCategory"
            :key="index"
            style="margin-right: 52px; height: 36px"
            :style="{ 'margin-right': index === 0 ? '53px' : '54px' }"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
      </c-v-col>
      <!-- 冷房 -->
      <c-v-col
        v-if="initFlg"
        class="or-content align-center"
      >
        <span>{{ t('label.assessment-home-6-4-vision-ca') }}</span>
        <span class="ml-3 mr-21">{{ t('label.air-conditioning-label') }}</span>
        <base-mo00039
          v-model="refValue!.airCooling"
          :oneway-model-value="localOneway.displayModeCategory"
        >
          <div
            v-for="(item, index) in displayModeCategory"
            :key="index"
            style="margin-right: 52px; height: 36px"
            :style="{ 'margin-right': index === 0 ? '53px' : '54px' }"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
      </c-v-col>
    </c-v-col>

    <!-- トイレタイトル -->
    <c-v-col class="c-sub-title">
      {{ t('label.toilet-label') }}
    </c-v-col>
    <!-- トイレコンテンツエリア -->
    <c-v-col class="c-sub-content">
      <!-- トイレ式 -->
      <c-v-col
        v-if="initFlg"
        class="or-content show-border pb-4 align-center"
      >
        <span class="mr-2">{{ t('label.assessment-home-6-4-vision-a') }}</span>
        <div
          v-for="(item, index) in localOneway.toiletSectionList"
          :key="index"
          class="mr-12"
        >
          <base-mo00018
            :key="index"
            v-model="item.check"
            :oneway-model-value="item.onewayModelValue"
            @change="checkBoxDataChange(`toilShu${index + 1}`, item.check.modelValue)"
          />
        </div>
        <base-mo00045
          v-model="refValue!.toilMemoKnj"
          :oneway-model-value="localOneway.toiletSectionInput"
        />
      </c-v-col>
      <!-- 手すり有無 -->
      <c-v-col
        v-if="initFlg"
        class="or-content show-border pb-4 align-center"
      >
        <span>{{ t('label.assessment-home-6-4-vision-i') }}</span>
        <span
          class="ml-3"
          style="margin-right: 70px"
          >{{ t('label.handrail-label') }}</span
        >
        <base-mo00039
          v-model="refValue!.toilTesuriUmu"
          :oneway-model-value="localOneway.displayModeCategory"
        >
          <div
            v-for="(item, index) in displayModeCategory"
            :key="index"
            style="margin-right: 52px; height: 36px"
            :style="{ 'margin-right': index === 0 ? '53px' : '54px' }"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
      </c-v-col>
      <!-- トイレまでの段差 -->
      <c-v-col
        v-if="initFlg"
        class="or-content align-center"
      >
        <span>{{ t('label.assessment-home-6-4-vision-u') }}</span>
        <span class="ml-3 mr-13">{{ t('label.dansa-toilet') }}</span>
        <base-mo00039
          v-model="refValue!.toilDansaUmu"
          :oneway-model-value="localOneway.displayModeCategory"
        >
          <div
            v-for="(item, index) in displayModeCategory"
            :key="index"
            style="margin-right: 52px; height: 36px"
            :style="{ 'margin-right': index === 0 ? '53px' : '54px' }"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
      </c-v-col>
    </c-v-col>

    <!-- 浴室タイトル -->
    <c-v-col class="c-sub-title">
      <span>{{ t('label.bathroom-label') }}</span>
    </c-v-col>
    <!-- 浴室コンテンツエリア -->
    <c-v-col class="c-sub-content">
      <!-- 浴槽有無 -->
      <c-v-col
        v-if="initFlg"
        class="or-content align-center show-border pb-4"
      >
        <span class="mr-2">{{ t('label.assessment-home-6-4-vision-a') }}</span>
        <base-mo00039
          v-model="refValue!.bathUmu"
          :oneway-model-value="localOneway.bathtubStatus"
        >
          <div
            v-for="(item, index) in bathtubStatus"
            :key="index"
            class="custom-radio mr-10"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
      </c-v-col>
      <!-- 手すり有無（浴室） -->
      <c-v-col
        v-if="initFlg"
        class="or-content align-center show-border pb-4"
      >
        <span>{{ t('label.assessment-home-6-4-vision-i') }}</span>
        <span
          class="ml-3"
          style="margin-right: 67px"
          >{{ t('label.handrail-label') }}</span
        >
        <base-mo00039
          v-model="refValue!.bathTesuriUmu"
          :oneway-model-value="localOneway.displayModeCategory"
        >
          <div
            v-for="(item, index) in displayModeCategory"
            :key="index"
            class="custom-radio mr-11"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
      </c-v-col>
      <!-- 段差有無（浴室） -->
      <c-v-col
        v-if="initFlg"
        class="or-content align-center"
      >
        <span>{{ t('label.assessment-home-6-4-vision-u') }}</span>
        <span class="ml-3 mr-16">
          {{ t('label.dansa-bathroom') }}
        </span>
        <base-mo00039
          v-model="refValue!.bathDansaUmu"
          :oneway-model-value="localOneway.displayModeCategory"
        >
          <div
            v-for="(item, index) in displayModeCategory"
            :key="index"
            class="custom-radio mr-11"
          >
            <base-at-radio
              :radio-label="item.label"
              :value="item.value"
            />
          </div>
        </base-mo00039>
      </c-v-col>
    </c-v-col>

    <!-- 移動手段タイトル -->
    <c-v-col class="c-sub-title">
      <span>{{ t('label.mode-of-transportation') }}</span>
    </c-v-col>
    <!-- 移動手段コンテンツエリア -->
    <c-v-col class="c-sub-content">
      <!-- 室外 -->
      <c-v-col class="or-content">
        <div class="c-sub-title third-header">{{ t('label.out-door-label') }}</div>
      </c-v-col>
      <c-v-col
        v-if="initFlg"
        class="or-content align-center"
      >
        <c-v-col cols="2">
          <span class="mb-4"> {{ t('label.assistive-technology') }}</span>
          <div style="margin-left: -2px">
            <base-mo00039
              v-model="refValue!.hukuOutUse"
              :oneway-model-value="localOneway.welfareDevicesUseStatus"
            />
          </div>
        </c-v-col>
        <div
          class="d-flex align-center"
          style="margin-top: -17px"
        >
          <c-v-col
            v-for="(item, index) in localOneway.welfareDevicesSection.out"
            :key="index"
            class="mr-6"
            cols="auto"
          >
            <base-mo00018
              v-model="item.check"
              :oneway-model-value="item.onewayModelValue"
              @change="checkBoxDataChange(`hukuOut${index + 1}`, item.check.modelValue)"
            />
          </c-v-col>
          <base-mo00045
            v-model="refValue!.outMemoKnj"
            :oneway-model-value="localOneway.mo00045Config"
          />
        </div>
      </c-v-col>
      <!-- 室内 -->
      <c-v-col class="or-content">
        <div class="c-sub-title third-header">{{ t('label.in-door-label') }}</div>
      </c-v-col>
      <c-v-col
        v-if="initFlg"
        class="or-content align-center"
      >
        <c-v-col cols="2">
          <span class="mb-4"> {{ t('label.assistive-technology') }}</span>
          <div style="margin-left: -2px">
            <base-mo00039
              v-model="refValue!.hukuInUse"
              :oneway-model-value="localOneway.welfareDevicesUseStatus"
            />
          </div>
        </c-v-col>
        <div
          class="d-flex align-center"
          style="margin-top: -17px"
        >
          <c-v-col
            v-for="(item, index) in localOneway.welfareDevicesSection.in"
            :key="index"
            class="mr-6"
            cols="auto"
          >
            <base-mo00018
              v-model="item.check"
              :oneway-model-value="item.onewayModelValue"
              @change="checkBoxDataChange(`hukuIn${index + 1}`, item.check.modelValue)"
            />
          </c-v-col>
          <base-mo00045
            v-model="refValue!.inMemoKnj"
            :oneway-model-value="localOneway.mo00045Config"
          />
        </div>
      </c-v-col>
    </c-v-col>

    <!-- 諸設備タイトル -->
    <c-v-col class="c-sub-title">
      <span>{{ t('label.various-facilities') }}</span>
    </c-v-col>
    <!-- 諸設備コンテンツエリア -->
    <c-v-col
      v-if="initFlg"
      class="c-sub-content"
    >
      <c-v-col
        v-if="showPattern"
        class="or-content"
      >
        <!-- 調理器具 -->
        <c-v-col cols="3">
          <span class="mb-1 d-block">{{ t('label.cooking-appliances-label') }}</span>
          <div style="margin-left: -2px">
            <base-mo00039
              v-model="refValue!.setsubi4"
              :oneway-model-value="localOneway.variousFacilities.radioGroup_1"
            >
              <div
                v-for="(item, index) in cookingAppliancesRadio"
                :key="index"
                class="mr-4 custom-radio"
              >
                <base-at-radio
                  :value="item.value"
                  :radio-label="item.label"
                ></base-at-radio>
              </div>
            </base-mo00039>
          </div>
        </c-v-col>
        <!-- 暖房器具 -->
        <c-v-col cols="auto">
          <span class="mb-1 d-block">{{ t('label.heating-appliances-label') }}</span>
          <div
            class="d-flex"
            style="margin-left: -2px"
          >
            <div
              v-for="(item, index) in localOneway.variousFacilities.checkboxContentList"
              :key="index"
              class="d-flex mr-4"
            >
              <base-mo00018
                v-model="item.check"
                :oneway-model-value="item.onewayModelValue"
                @change="checkBoxDataChange(`setsubi5Shu${index + 1}`, item.check.modelValue)"
              />
            </div>
            <base-mo00045
              v-model="refValue!.setsubi5Shu4MemoKnj"
              :oneway-model-value="localOneway.variousFacilities.variousFacilitiesInput"
            />
          </div>
        </c-v-col>
      </c-v-col>
      <c-v-col
        v-else
        class="or-content"
      >
        <c-v-col cols="3">
          <span class="d-block mb-1">{{ t('label.washing-machine-label') }}</span>
          <div style="margin-left: -2px">
            <base-mo00039
              v-model="refValue!.setsubi1"
              :oneway-model-value="localOneway.displayModeCategory"
            >
              <div
                v-for="(item, index) in displayModeCategory"
                :key="index"
                class="mr-4 custom-radio"
              >
                <base-at-radio
                  :value="item.value"
                  :radio-label="item.label"
                ></base-at-radio>
              </div>
            </base-mo00039>
          </div>
        </c-v-col>
        <c-v-col cols="3">
          <span class="d-block mb-1">{{ t('label.water-heater-label') }}</span>
          <div style="margin-left: -2px">
            <base-mo00039
              v-model="refValue!.setsubi2"
              :oneway-model-value="localOneway.displayModeCategory"
            >
              <div
                v-for="(item, index) in displayModeCategory"
                :key="index"
                class="mr-4 custom-radio"
              >
                <base-at-radio
                  :value="item.value"
                  :radio-label="item.label"
                ></base-at-radio>
              </div>
            </base-mo00039>
          </div>
        </c-v-col>
        <c-v-col cols="3">
          <span class="d-block mb-1">{{ t('label.refrigerator-label') }}</span>
          <div style="margin-left: -2px">
            <base-mo00039
              v-model="refValue!.setsubi3"
              :oneway-model-value="localOneway.displayModeCategory"
            >
              <div
                v-for="(item, index) in displayModeCategory"
                :key="index"
                class="mr-4 custom-radio"
              >
                <base-at-radio
                  :value="item.value"
                  :radio-label="item.label"
                ></base-at-radio>
              </div>
            </base-mo00039>
          </div>
        </c-v-col>
      </c-v-col>
    </c-v-col>

    <!-- 家屋(居室を含む)見取図タイトル -->
    <c-v-col class="c-sub-title action">
      <span>{{ t('label.floor-plan-of-the-house-including-living-spaces') }}</span>
      <p class="ml-10">{{ t('label.floor-plan-of-the-house-including-living-spaces-record') }}</p>
    </c-v-col>
    <!-- 家屋(居室を含む)見取図コンテンツエリア -->
    <c-v-col class="c-sub-content">
      <div
        v-if="initFlg"
        class="w-100 live-room d-flex align-center"
      >
        アップロードしたファイルを表示
      </div>
    </c-v-col>

    <!-- 特記事項タイトル -->
    <c-v-col class="c-sub-title">
      <span>{{ t('label.speacil-tab4-label') }}</span>
    </c-v-col>
    <!-- 特記事項コンテンツエリア -->
    <c-v-col class="c-sub-content">
      <g-custom-or-x-0156
        v-model="refValue!.memoKnj"
        :oneway-model-value="localOneway.orX0156Oneway"
        @on-click-edit-btn="setShowOr51775Dialog"
      />
    </c-v-col>
    <!-- 入力支援画面 -->
    <g-custom-or51775
      v-if="showDialog51775"
      v-bind="or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="or51775ConfirmResult"
    />
  </c-v-sheet>
  <!-- 線 -->
  <c-v-row
    no-gutters
    style="margin: 0 -24px"
    class="py-6"
  >
    <c-v-divider></c-v-divider>
  </c-v-row>

  <!-- フッター -->
  <c-v-row
    no-gutters
    class="pb-6"
  >
    <div
      ref="issuesAndGoalListRef"
      class="w-100"
    >
      <g-custom-or-x-0209
        v-bind="orX0209"
        :model-value="local.issuesAndGoalsList"
        :oneway-model-value="localOneway.issuesAndGoalsListOneway"
      />
    </div>
  </c-v-row>
</template>

<style lang="scss" scoped>
.v-col {
  padding: 0px;
}
:deep(.radio-group) {
  margin: 0px !important;
}
.c-sub-content.family-home {
  padding: 16px 38px;
  padding-right: 31px;
}
.radio-item {
  padding-right: 8px;
  height: 36px;
}
.mr-21 {
  margin-right: 84px;
}
// カスタムラジオボタン高さ
.custom-radio {
  height: 36px;
}
.c-sub-title.third-header {
  font-size: 14px;
  border-radius: 4px;
  padding-left: 12px;
}
.live-room {
  background-color: rgba(240, 240, 240, 1);
  height: 240px;
  border-radius: 4px;
  color: #333333;
  justify-content: center;
}
:deep(.split-line) {
  display: none;
}
.live-sub-title {
  width: 109px;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
</style>
