import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'

/**
 * OrCD002:有機体:介護認定項目
 * OneWayBind領域に保持するデータ構造
 */
export interface Or30233Type {
  /**
   * データリスト
   */
  careCertificationList: CareCertificationList
}

/**
 * 介護認定一覧
 */
export interface CareCertificationList {
  /** セクション */
  sectionList: {
    value: string
    /** セクションタイトル */
    sectionTitle: string
    sectionItems: CareCertificationItem[]
  }[]
}

/**
 * 介護認定項目
 */
export interface CareCertificationItem {
  /**
   *itemType
   */
  itemType: string
  /**
   *check
   */
  check: Mo00018Type
  /**
   *value
   */
  value: string
  /**
   *itemName
   */
  itemName: string
  /**
   *tooltip
   */
  tooltip: string
  /**
   *label
   */
  label: string
}

/**
 * 介護認定一覧
 */
export interface SectionList {
  /**
   *sectionList
   */
  sectionList: {
    /**
     * value
     */
    value: string
    /**
     * sectionTitle
     */
    sectionTitle: string
    /**
     * sectionItems
     */
    sectionItems: {
      itemType: string
      check: { modelValue: boolean }
      value: string
      tooltip: string
      label: string
      itemName: string
    }
  }[]
}

/**
 * SectionItem
 */
export interface SectionItem {
  /**
   * itemType
   */
  itemType: string
  /**
   * check
   */
  check: {
    /**
     * modelValue
     */
    modelValue: boolean
  }
  /**
   * value
   */
  value: string
  /**
   * itemName
   */
  itemName: string
  /**
   * tooltip
   */
  tooltip?: string // 可选属性
  /**
   * label
   */
  label: string
}

/**
 * Section
 */
export interface Section {
  /**
   * value
   */
  value: string
  /**
   * sectionTitle
   */
  sectionTitle: string
  /**
   * sectionItems
   */
  sectionItems: SectionItem[]
}
