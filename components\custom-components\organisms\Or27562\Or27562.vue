<script setup lang="ts">
/**
 * Or27562:アセスメントマスタモーダル
 * GUI00626_アセスメントマスタ
 *
 * @description
 * アセスメントマスタモーダル
 *
 * <AUTHOR>
 */

import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27405Const } from '../Or27405/Or27405.constants'
import { Or27405Logic } from '../Or27405/Or27405.logic'
import { Or27562Const } from './Or27562.constants'
import type { Or27562StateType } from './Or27562.type'
import { useJigyoList } from '~/utils/useJigyoList'
import {
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#build/imports'
import type { Or27562OnewayType, Or27562Type } from '~/types/cmn/business/components/Or27562Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type {
  AssessmentMasterSelectInEntity,
  AssessmentMasterSelectOutEntity,
  AssessmentMasterUpdateInEntity,
  AssessmentMasterUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentMasterEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01351OnewayType } from '~/types/business/components/Mo01351Type'
import type {
  Mo01354Items,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { Or27405OnewayType, Or27405Type } from '~/types/cmn/business/components/Or27405Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { ResBodyStatusCode } from '~/constants/api-constants'

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue?: Or27562OnewayType
}

const props = defineProps<Props>()
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['onClickClose'])

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const { jigyoListWatch } = useJigyoList()

const or21814_1 = ref({ uniqueCpId: '' }) // 確認ダイアログ（はい、いいえ、キャンセル）
const or21814_2 = ref({ uniqueCpId: '' }) // 確認ダイアログ（はい、いいえ）
const or21814_3 = ref({ uniqueCpId: '' }) // 確認ダイアログ（はい）

const or41179 = ref({ uniqueCpId: '' }) // 事業所選択
const or27405 = ref({ uniqueCpId: '' }) // 状況マスタ

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})

const defaultOnewayModelValue: Or27562OnewayType = {
  /** 事業者ID */
  svJigyoId: '1',
  /** 分類1 */
  bunrui1Id: '2',
  /** 分類2 */
  bunrui2Id: '12',
  /** 施設ID */
  shisetuId: '1',
}

// ローカル双方向bind
const local = reactive({
  mo00024: {
    isOpen: true,
  },
  // Visio選択肢
  visioRadioList: [] as CodeType[],
  // 障害等の部位の摘要表示選択肢
  handycapSummaryShowRadioList: [] as CodeType[],
  // 全体のまとめ選択肢
  overrallSummaryRadioList: [] as CodeType[],
  // 状況マスタ
  or27405: {
    listValue: {
      values: {
        selectedRowId: '-1',
        selectedRowIds: [],
        items: [] as Mo01354Items[],
      },
    } as Mo01354Type,
  } as Or27405Type,
})

// ローカルoneway
const localOneway = reactive({
  Or27562: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: 'auto',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27562',
      toolbarTitle: t('label.assessment-master'),
      toolbarTitleCenteredFlg: false,
      toolbarName: 'Or27562ToolBar',
      showCardActions: true,
    },
  },
  masterBtnOneway: {
    btnIcon: 'database',
    name: 'officeSelectIconBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  // ******Visioラジオグループセクション******
  visioRaidoGroupOneway: {
    name: 'visoRadioGroup',
    showItemLabel: false,
    hideDetails: true,
  },
  // ******障害等の部位の摘要表示ラジオグループセクション******
  handycapSummaryShowGroupOneway: {
    name: 'summaryShowRadioGroup',
    showItemLabel: false,
    hideDetails: true,
  },
  // ******全体のまとめラジオグループセクション******
  overrallSummaryGroupOneway: {
    name: 'overrallSummaryRadioGroup',
    showItemLabel: false,
    hideDetails: true,
  },
  // 閉じるボタン
  footerClosebtnOneway: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 保存ボタン
  footerSavebtnOneway: {
    btnLabel: t('btn.save'),
    tooltipText: t('tooltip.save'),
  } as Mo00609OnewayType,
  masterDialogOneway: {
    value: '',
  },
  // エラーメッセージ
  mo01351Oneway: {
    mo00002Oneway: {
      visible: false,
    },
  } as Mo01351OnewayType,
  // 状況マスタ
  or27405Oneway: {
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
  } as Or27405OnewayType,
})

// 事業所IDバックアップ
const jigyoIdOld = ref('')

// Visio選択値
const visoRadioValue = ref('')
// Visio更新区分
const visioUpdateKbn = ref('')
// 障害等の部位の摘要表示選択値
const summaryShowRadioValue = ref('')
// 障害等の部位の摘要表示更新区分
const summaryShowUpdateKbn = ref('')
// 全体のまとめ選択値
const overrallSummaryRadioValue = ref('')
// 全体のまとめ更新区分
const overrallSummaryUpdateKbn = ref('')

// 初期情報バックアップ
const originData = ref<AssessmentMasterSelectOutEntity>()

// 権限フラグ
const isPermissionViewAuth = ref(false)

// 状況マスタダイアログ表示フラグ
const showDialogOr27405 = computed(() => {
  // Or27405のダイアログ開閉状態
  return Or27405Logic.state.get(or27405.value.uniqueCpId)?.isOpen ?? false
})

// イベント変更
const eventKbn = ref('')
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // 処理設定
  await init()
  // 初期情報取得
  await getInitDataInfo()

  // 値変更ワッチャーを起動
  setupDataWacth()
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or41179Const.CP_ID(1)]: or41179.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or21814Const.CP_ID(3)]: or21814_3.value,
  [Or27405Const.CP_ID(1)]: or27405.value,
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or27562Type>({
  cpId: Or27562Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

const { setState } = useScreenOneWayBind<Or27562StateType>({
  cpId: Or27562Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or27562Const.DEFAULT.IS_OPEN
    },
    isSaveCompleted: (value) => {
      if (value) {
        if (eventKbn.value === Or27562Const.DEFAULT.EVENT_KBN_CLOSE) {
          // 閉じる場合
          setState({ isOpen: false })
        } else {
          // 画面情報再取得
          void getInitDataInfo()
        }
      }

      setState({ isSaveCompleted: false })
    },
  },
})

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**************************************************
 * ウォッチャー
 **************************************************/
// 画面開閉フラグ
watch(
  () => local.mo00024.isOpen,
  (newValue) => {
    setState({ isOpen: local.mo00024.isOpen })
    if (!newValue) {
      emit('onClickClose')
    }
  }
)

/**************************************************
 * イベント処理
 **************************************************/
/**
 *  画面初期情報取得
 */
async function getInitDataInfo() {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentMasterSelectInEntity = {
    /** 事業者ID */
    svJigyoId: localOneway.Or27562.svJigyoId,
    /** 分類1 */
    bunrui1Id: localOneway.Or27562.bunrui1Id,
    /** 分類2 */
    bunrui2Id: localOneway.Or27562.bunrui2Id,
    /** 施設ID */
    shisetuId: localOneway.Or27562.shisetuId,
  }
  const resData: AssessmentMasterSelectOutEntity = await ScreenRepository.select(
    'assessmentMasterSelect',
    inputData
  )
  // 画面情報を設定
  if (resData.statusCode === Or27562Const.DEFAULT.STATUS_CODE_SUCCESS) {
    // 初期情報バックアップ
    originData.value = resData

    visoRadioValue.value = resData.data.visioValue ?? ''
    summaryShowRadioValue.value = resData.data.syougaiValue ?? ''
    overrallSummaryRadioValue.value = resData.data.zenntaiValue ?? ''

    // RefValueを設定
    setRefValue(resData)
  }

  eventKbn.value = Or27562Const.DEFAULT.EVENT_KBN_NORMAL
}

/**
 *  RefValueを設定
 *
 * @param resData - 初期取得された情報
 */
function setRefValue(resData: AssessmentMasterSelectOutEntity) {
  const newRefValue = {
    visioValue: resData.data.visioValue ?? '',
    summaryShowValue: resData.data.syougaiValue ?? '',
    overrallSummaryValue: resData.data.zenntaiValue ?? '',
  } as Or27562Type

  // APIから取得されたデータでRefValueを更新する
  useScreenStore().setCpTwoWay({
    cpId: Or27562Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: newRefValue,
    isInit: true,
  })
}

/**
 *  値変更ワッチャー
 */
function setupDataWacth() {
  // Visio入力値
  watch(
    () => visoRadioValue.value,
    (newValue) => {
      if (refValue.value !== undefined) {
        refValue.value.visioValue = newValue
        // Visioの入力変更あり、かつ、初期取得情報.Visio更新区分がないの場合、Visio更新区分＝1
        if (
          originData.value?.data.visioValue === refValue.value.visioValue &&
          (originData.value?.data.visioUpdateKbn === undefined ||
            originData.value?.data.visioUpdateKbn === '')
        ) {
          visioUpdateKbn.value = Or27562Const.DEFAULT.UPDATE_KBN_YES
        }
      }
    }
  )

  // 障害等の部位の摘要表示入力値
  watch(
    () => summaryShowRadioValue.value,
    (newValue) => {
      if (refValue.value !== undefined) {
        refValue.value.summaryShowValue = newValue
        // 障害等の部位の摘要表示の入力変更あり、かつ、初期取得情報.障害更新区分がないの場合、障害更新区分＝1
        if (
          originData.value?.data.syougaiValue === refValue.value.summaryShowValue &&
          (originData.value?.data.syougaiUpdateKbn === undefined ||
            originData.value?.data.syougaiUpdateKbn === '')
        ) {
          summaryShowUpdateKbn.value = Or27562Const.DEFAULT.UPDATE_KBN_YES
        }
      }
    }
  )

  // 全体のまとめ入力値
  watch(
    () => overrallSummaryRadioValue.value,
    (newValue) => {
      if (refValue.value !== undefined) {
        refValue.value.overrallSummaryValue = newValue
        // 全体のまとめの入力変更あり、かつ、初期取得情報.全体更新区分がないの場合、全体更新区分＝1
        if (
          originData.value?.data.zenntaiValue === refValue.value.overrallSummaryValue &&
          (originData.value?.data.zenntaiUpdateKbn === undefined ||
            originData.value?.data.zenntaiUpdateKbn === '')
        ) {
          overrallSummaryUpdateKbn.value = Or27562Const.DEFAULT.UPDATE_KBN_YES
        }
      }
    }
  )
}

/**
 *  閉じる/Xボタン押下時の処理
 */
async function onClick_Close() {
  // 画面変更チェック
  if (isEdit.value) {
    if (!isPermissionViewAuth.value) {
      // 画面入力変更あり、かつ保存権限がない場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog2(t('message.i-cmn-10006'))
      switch (dialogResult) {
        case 'yes': {
          setState({ isOpen: false })
          emit('onClickClose')
        }
      }
      return
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case 'yes': {
          // はい選択時は入力内容を保存する
          eventKbn.value = Or27562Const.DEFAULT.EVENT_KBN_CLOSE
          await _save()
          break
        }
        case 'no':
          // いいえ選択時は編集内容を破棄するので何もしない
          setState({ isOpen: false })
          emit('onClickClose')
          break
        case 'cancel':
          // キャンセル選択時は一覧の選択を戻す
          break
      }

      return
    }
  } else {
    setState({ isOpen: false })
    emit('onClickClose')
  }
}

/**
 *  保存ボタン押下時の処理
 */
async function onClick_Save() {
  localOneway.footerSavebtnOneway.disabled = true

  // 画面入力変更なしの場合
  // ■以下のメッセージを表示し、処理を終了する。
  // メッセージID：i-cmn-21800
  // メッセージ内容：
  // 「変更されている項目がないため、保存を行うことは出来ません。「改行」
  //  項目を入力変更してから、再度保存を行ってください。」
  if (!isEdit.value) {
    openConfirmDialog3(t('message.i-cmn-21800'))
    localOneway.footerSavebtnOneway.disabled = false
    return
  }

  await _save()
  localOneway.footerSavebtnOneway.disabled = false
}

/**
 *  保存処理
 */
async function _save() {
  // 更新データ作成
  const inputData: AssessmentMasterUpdateInEntity = {
    /** 施設ID */
    shisetuId: localOneway.Or27562.shisetuId,
    /** 事業所ID */
    svJigyoId: localOneway.Or27562.svJigyoId,
    /** 分類１ */
    bunrui1Id: localOneway.Or27562.bunrui1Id,
    /** 分類2 */
    bunrui2Id: localOneway.Or27562.bunrui2Id,
    /** Visio */
    visioValue: visoRadioValue.value,
    /** Visio分類３ */
    visioBunrui3: originData.value?.data.visioBunrui3 ?? Or27562Const.DEFAULT.BUNRUI_3_VISIO,
    /** Visio更新区分 */
    visioUpdateKbn: visioUpdateKbn.value,
    /** 障害等の部位の摘要表示 */
    syougaiValue: summaryShowRadioValue.value,
    /** 障害分類３ */
    syougaiBunrui3: originData.value?.data.syougaiBunrui3 ?? Or27562Const.DEFAULT.BUNRUI_3_SYOGAI,
    /** 障害更新区分 */
    syougaiUpdateKbn: summaryShowUpdateKbn.value,
    /** 全体のまとめ */
    zenntaiValue: overrallSummaryRadioValue.value,
    /** 全体分類３ */
    zenntaiBunrui3: originData.value?.data.zenntaiBunrui3 ?? Or27562Const.DEFAULT.BUNRUI_3_ZENNTAI,
    /** 全体更新区分 */
    zenntaiUpdateKbn: overrallSummaryUpdateKbn.value,
  }
  const resData: AssessmentMasterUpdateOutEntity = await ScreenRepository.update(
    'assessmentMasterUpdate',
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (
    resData.statusCode === Or27562Const.DEFAULT.STATUS_CODE_SUCCESS ||
    resData.statusCode === ResBodyStatusCode.SUCCESS
  ) {
    setState({ isSaveCompleted: true })
  }
}

/**
 *  マスタボタン押下時の処理
 */
function onClick_master() {
  localOneway.or27405Oneway = {
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: localOneway.Or27562.shisetuId,
    svJigyoId: localOneway.Or27562.svJigyoId,
  } as Or27405OnewayType

  // マスタダイアログ開閉状態を更新する
  Or27405Logic.state.set({
    uniqueCpId: or27405.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**************************************************
 * 関数
 **************************************************/
// 初期処理
async function init() {
  // TODO 権限取得 一時trueに設定する
  isPermissionViewAuth.value = await hasRegistAuth()
  isPermissionViewAuth.value = true

  // 権限によって保存ボタンを制御
  if (!isPermissionViewAuth.value) {
    localOneway.footerSavebtnOneway.disabled = true
  }

  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
    },
  })

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
    },
  })

  // 汎用コードマスタデータを取得し初期化
  await initCodes()

  // 選択肢の表示順を調整
  let validFlgIndex = local.visioRadioList.findIndex((item) => item.value === '1')
  if (validFlgIndex > -1) {
    local.visioRadioList.unshift(...local.visioRadioList.splice(validFlgIndex, 1))
  }

  validFlgIndex = local.handycapSummaryShowRadioList.findIndex((item) => item.value === '1')
  if (validFlgIndex > -1) {
    local.handycapSummaryShowRadioList.unshift(
      ...local.handycapSummaryShowRadioList.splice(validFlgIndex, 1)
    )
  }
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // Visioの選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_VISIO },
    // 障害等の部位の摘要表示の選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_HANDYCAPSUMMARYSHOW },
    // 全体のまとめの選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_OVERRALLSUMMARY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // Visio
  local.visioRadioList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_VISIO)
  // 障害等の部位の摘要表示
  local.handycapSummaryShowRadioList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_HANDYCAPSUMMARYSHOW
  )
  // 全体のまとめ
  local.overrallSummaryRadioList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_OVERRALLSUMMARY
  )
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog1(paramDialogText: string): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = 'cancel' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
async function openConfirmDialog2(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_2.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_2.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 */
function openConfirmDialog3(paramDialogText: string) {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId === jigyoIdOld.value) {
    return
  }

  // まず変更前の事業所を保持
  void setJigyo(jigyoIdOld.value)

  // 事業所変更処理
  void jigyoChange(newJigyoId)
}

/**
 * 事業所変更処理
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function jigyoChange(newJigyoId: string) {
  // 画面変更チェック
  if (isEdit.value) {
    if (!isPermissionViewAuth.value) {
      // 画面入力変更あり、かつ保存権限がない場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog2(t('message.i-com-10006'))
      switch (dialogResult) {
        case 'yes': {
          localOneway.Or27562.svJigyoId = newJigyoId
          jigyoIdOld.value = newJigyoId
          // 変更後の事業所に設定
          await setJigyo(newJigyoId)
          // 画面情報再取得
          await getInitDataInfo()
        }
      }
      return
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case 'yes': {
          localOneway.Or27562.svJigyoId = newJigyoId
          jigyoIdOld.value = newJigyoId
          // 変更後の事業所に設定
          await setJigyo(newJigyoId)

          // はい選択時は入力内容を保存する
          await _save()

          // 画面情報再取得
          await getInitDataInfo()
          break
        }
        case 'no':
          // いいえ選択時は編集内容を破棄するので何もしない

          localOneway.Or27562.svJigyoId = newJigyoId
          jigyoIdOld.value = newJigyoId
          // 変更後の事業所に設定
          await setJigyo(newJigyoId)

          // 画面情報再取得
          await getInitDataInfo()
          break
        case 'cancel':
          // キャンセル選択時は一覧の選択を戻す
          break
      }

      return
    }
  } else {
    localOneway.Or27562.svJigyoId = newJigyoId
    jigyoIdOld.value = newJigyoId
    // 変更後の事業所に設定
    await setJigyo(newJigyoId)

    // 画面情報再取得
    await getInitDataInfo()
  }
}

/**
 * 事業所を選択
 *
 * @param jigyoId - 事業所ID
 */
async function setJigyo(jigyoId: string) {
  await nextTick()

  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    },
  })
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet class="view d-flex flex-column">
        <c-v-row class="operationArea flex-0-0">
          <c-v-col>
            <!-- 事業所選択画面 -->
            <g-base-or-41179 v-bind="or41179" />
          </c-v-col>
          <c-v-col>
            <!-- マスタ他 -->
            <base-mo00009
              :oneway-model-value="localOneway.masterBtnOneway"
              @click="onClick_master"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.others-function')"
              />
            </base-mo00009>
            <!-- 状況マスタ -->
            <g-custom-or-27405
              v-if="showDialogOr27405"
              v-bind="or27405"
              v-model="local.or27405"
              :oneway-model-value="localOneway.or27405Oneway"
              :parent-cp-id="props.uniqueCpId"
            />
          </c-v-col>
        </c-v-row>
        <base-mo01351
          :oneway-model-value="localOneway.mo01351Oneway"
          class="ma-1"
          style="white-space: pre-line"
        />
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader d-flex flex-column justify-center align-center pa-0">
            <span>{{ t('label.visio') }}</span>
          </c-v-col>
          <c-v-col class="sectionContent w-100 d-flex align-center">
            <base-mo00039
              v-model="visoRadioValue"
              :oneway-model-value="localOneway.visioRaidoGroupOneway"
            >
              <span class="label-font"></span>
              <base-at-radio
                v-for="(item, index) in local.visioRadioList"
                :key="index"
                :name="'radio' + index"
                :value="item.value"
                :radio-label="item.label"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader d-flex flex-column justify-center align-center pa-0">
            <span>{{ t('label.handycap-summary-show') }}</span>
            <small>{{ t('label.after-h18-valid') }}</small>
          </c-v-col>
          <c-v-col class="sectionContent w-100 d-flex align-center">
            <base-mo00039
              v-model="summaryShowRadioValue"
              :oneway-model-value="localOneway.handycapSummaryShowGroupOneway"
            >
              <span class="label-font"></span>
              <base-at-radio
                v-for="(item, index) in local.handycapSummaryShowRadioList"
                :key="index"
                :name="'radio' + index"
                :value="item.value"
                :radio-label="item.label"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader d-flex flex-column justify-center align-center pa-0">
            <span>{{ t('label.overrall-summary') }}</span>
            <small>{{ t('label.after-h21-valid') }}</small>
          </c-v-col>
          <c-v-col class="sectionContent w-100 d-flex align-center">
            <base-mo00039
              v-model="overrallSummaryRadioValue"
              :oneway-model-value="localOneway.overrallSummaryGroupOneway"
            >
              <span class="label-font"></span>
              <base-at-radio
                v-for="(item, index) in local.overrallSummaryRadioList"
                :key="index"
                :name="'radio' + index"
                :value="item.value"
                :radio-label="item.label"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <!-- 閉じる -->
      <base-mo00611
        :oneway-model-value="localOneway.footerClosebtnOneway"
        @click="onClick_Close"
      />
      <!-- 保存 -->
      <base-mo00609
        class="ml-2"
        :oneway-model-value="localOneway.footerSavebtnOneway"
        @click="onClick_Save"
      />
    </template>
    <!-- Or21814:有機体:確認ダイアログ（はい、いいえ、キャンセル） -->
    <g-base-or21814 v-bind="or21814_1" />
    <!-- Or21814:有機体:確認ダイアログ（はい、いいえ） -->
    <g-base-or21814 v-bind="or21814_2" />
    <!-- Or21814:有機体:確認ダイアログ（はい） -->
    <g-base-or21814 v-bind="or21814_3" />
  </base-mo00024>
</template>

<style scoped lang="scss">
.view {
  width: 906px;

  .v-row {
    margin: unset;
  }

  .operationArea {
    .v-col {
      padding-left: 0;
      padding-right: 0;
      padding-top: 0;
    }

    .v-col:last-child {
      text-align: right;
    }
  }

  .subSection {
    height: 80px;
    border-top: solid thin rgb(var(--v-theme-light));
    border-left: solid thin rgb(var(--v-theme-light));
    border-right: solid thin rgb(var(--v-theme-light));
    .sectionHeader {
      min-width: 152px;
      max-width: 200px;
      --v-theme-overlay-multiplier: var(--v-theme-background-overlay-multiplier);
      background-color: rgb(var(--v-theme-background)) !important;
      color: rgb(var(--v-theme-on-background)) !important;
      font-weight: bold;

      span {
        width: 100%;
        padding-left: 8px;
        font-size: 14px;
      }

      small {
        width: 100%;
        padding-left: 8px;
        font-weight: normal;
      }
    }

    .sectionContent {
      .v-sheet {
        width: 100%;

        :deep(.radio-group) {
          width: 100%;

          .v-col-auto {
            width: 100%;
          }
        }
      }

      :deep(.v-selection-control--inline) {
        flex: 1 1 auto;
        max-width: 35%;
      }
    }
  }

  .subSection:last-child {
    border-bottom: solid thin rgb(var(--v-theme-light));
  }
}
</style>
