<script setup lang="ts">
import { computed, ref, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or28389Const } from '~/components/custom-components/organisms/Or28389/Or28389.constants'
import { Or28389Logic } from '~/components/custom-components/organisms/Or28389/Or28389.logic'
import type { Or28389Type, Or28389OnewayType } from '~/types/cmn/business/components/Or28389Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**
 * GUI00642_親族・関係者選択
 *
 * @description
 * 「親族・関係者選択」画面を表示
 *
 * <AUTHOR> 劉顕康
 */

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00642'
// ルーティング
const routing = 'GUI00642/pinia'
// 画面物理名
const screenName = 'GUI00642'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or28389 = ref({ uniqueCpId: Or28389Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00642' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or28389Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or28389.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00642',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28389Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or28389Const.CP_ID(1)]: or28389.value,
})

// ダイアログ表示フラグ
const showDialogOr28389 = computed(() => {
  // Or28389のダイアログ開閉状態
  return Or28389Logic.state.get(or28389.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or28389)
 */
function onClickOr28389() {
  // Or28389のダイアログ開閉状態を更新する
  Or28389Logic.state.set({
    uniqueCpId: or28389.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or28389Type = ref<Or28389Type>({
  /** 関係者リスト */
  kankeishaList: [],
})

const or28389Data: Or28389OnewayType = {
  /** 利用者ID */
  userId: '1',
}

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  userId: { value: '1' } as Mo00045Type,
})

/** GUI00642 疎通起動  */
const or28389OnClick = () => {
  or28389Data.userId = local.userId.value
  // Or28389のダイアログ開閉状態を更新する
  Or28389Logic.state.set({
    uniqueCpId: or28389.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28389"
        >GUI00642_［親族・関係者選択］画面
      </v-btn>
      <g-custom-or-28389
        v-if="showDialogOr28389"
        v-bind="or28389"
        v-model="or28389Type"
        :oneway-model-value="or28389Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="or28389OnClick"> GUI00642 疎通起動 </v-btn>
  </div>
  <div class="pt-5 pl-5">
    return ----- data

    <div>
      {{ or28389Type }}
    </div>
  </div>
</template>
