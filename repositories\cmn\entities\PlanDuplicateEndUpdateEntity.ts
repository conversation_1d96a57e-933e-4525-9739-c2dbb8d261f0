import type { InWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 更新入力エンティティ
 */
export interface PlanDuplicateEndUpdateInEntity extends InWebEntity {
  /** 職員ID */
  shokuinId?: string
  /** 引継画面名 */
  kinouName?: string
  /** システムコード */
  gsysCd?: string
  /** 複写方法 */
  copyMode?: string
  /** 複写方法_更新回数 */
  copyMode_modifiedCnt?: string
  /** 複写方法保険外 */
  copyModeGai?: string
  /** 複写方法保険外_更新回数 */
  copyModeGai_modifiedCnt?: string
  /** 短期入所複写方法 */
  copyTnki?: string
  /** 短期入所複写方法_更新回数 */
  copyTnki_modifiedCnt?: string
  /** 複写先利用票状況 */
  copyJokyo?: string
  /** 複写先利用票状況_更新回数 */
  copyJokyo_modifiedCnt?: string
  /** 複写先保険外ｻｰﾋﾞｽ状況 */
  copyJokyoGai?: string
  /** 複写先保険外ｻｰﾋﾞｽ状況_更新回数 */
  copyJokyoGai_modifiedCnt?: string
  /** 福祉用具単位数 */
  copyFygTani?: string
  /** 福祉用具単位数_更新回数 */
  copyFygTani_modifiedCnt?: string
}
