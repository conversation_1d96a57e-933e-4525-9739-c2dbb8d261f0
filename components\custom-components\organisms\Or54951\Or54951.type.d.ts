/**
 * GUI01235_評価表
 *
 * @description
 * 評価表テーブル有機体
 * データタイプ
 *
 * <AUTHOR>
 */

import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo01276Type } from '~/types/business/components/Mo01276Type'
import type { Mo01280Type } from '~/types/business/components/Mo01280Type'
import type { Mo01282Type } from '~/types/business/components/Mo01282Type'

/**
 * ヘッダタイプ
 */
export interface Or54951HeadersType {
  /**
   * アイコンボタン表示フラグ
   */
  iconDisplayFlg: boolean
  /**
   * 年月日と月日表示フラグ
   */
  ymdAndMdDisplayFlg: boolean
  /**
   * カレンダー表示フラグ
   */
  calenderDisplayFlg: boolean
  /**
   * 項目名
   */
  headerName: string
  /**
   * 項目ID
   */
  headerId: string
  /**
   * 文字数
   */
  widthCount: string
  /**
   * フォントサイズ
   */
  fontSize: string
  /**
   * 区分ID
   */
  kbnId: string
  /**
   * 入力区分
   */
  inputKbn: string
  /**
   * 子項目
   */
  children?: Or54951HeadersType[]
  /**
   * 連動区分
   */
  rendouKbn: string
  /**
   * クラス設定区分
   */
  classKbn?: boolean
  /**
   * 幅
   */
  width: string
}

/**
 * テーブルデータリストタイプ
 */
export interface Or54951TableDataListType {
  /**
   * ID
   */
  id: string
  /**
   * 入力区分
   */
  inputKbn?: string
  /**
   * 文章
   */
  knj?: Mo01280Type
  /**
   * コード
   */
  cod?: Mo01282Type
  /**
   * 日付スタート
   */
  startYmd?: Mo01276Type
  /**
   * 日付エンド
   */
  endYmd?: Mo01276Type
  /**
   * 更新回数
   */
  modifiedCnt?: string
  /**
   * データID
   */
  cmoni3Id: string
  /**
   * ヘッダID
   */
  cmoni1Id: string
  /**
   * colSpan
   */
  colSpan: string
  /**
   * 連動区分
   */
  rendouKbn: string
  /**
   * ボーダー表示フラグ
   */
  omittedBorderFlg: boolean
  /**
   * 内容表示フラグ
   */
  omittedContentFlg: boolean
  /**
   * 更新区分
   */
  updateKbn?: string
  /**
   * チェックボックスデータ
   */
  checkBoxValue?: Mo00018Type
  /**
   * 期間入力支援アイコン表示フラグ
   */
  periodInputSupportIconDisplayFlg?: boolean
}
