<script setup lang="ts">
/**
 * Or11752:選定表(アセスメント(インターライ))
 * GUI00848: 選定表(アセスメント(インターライ))
 *
 * @description
 *選定表(アセスメント(インターライ))
 *
 * <AUTHOR> DAM XUAN HIEU
 */

import { onMounted, reactive, ref, watch, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { Or11752Const } from './Or11752.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useSetupChildProps, useScreenStore, useScreenTwoWayBind } from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { OrX0009Logic } from '~/components/custom-components/organisms/OrX0009/OrX0009.logic'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or11752OnewayType } from '~/types/cmn/business/components/Or11752Type'
import { Or05905Logic } from '~/components/custom-components/organisms/Or05905/Or05905.logic'
import { Or05905Const } from '~/components/custom-components/organisms/Or05905/Or05905.constants'
import type { Or10279OneWayType } from '~/types/cmn/business/components/Or10279Type'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import type { OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import { OrX0007Logic } from '~/components/custom-components/organisms/OrX0007/OrX0007.logic'
import { OrX0010Const } from '~/components/custom-components/organisms/OrX0010/OrX0010.constants'
import { OrX0010Logic } from '~/components/custom-components/organisms/OrX0010/OrX0010.logic'
import type { OrX0008OnewayType } from '~/types/cmn/business/components/OrX0008Type'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import { OrX0008Const } from '~/components/custom-components/organisms/OrX0008/OrX0008.constants'
import { OrX0008Logic } from '~/components/custom-components/organisms/OrX0008/OrX0008.logic'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import type { PlanTargetPeriodDataType } from '~/types/cmn/PlanTargetPeriodDataType'
import type {
  selectionTableAssessmentInterRaiPeriodSelectInEntity,
  selectionTableAssessmentInterRaiPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/selectionTableAssessmentInterRaiPeriodSelectEntity'
import type {
  selectionTableAssessmentInterRaiHistorySelectInEntity,
  selectionTableAssessmentInterRaiHistorySelectOutEntity,
} from '~/repositories/cmn/entities/selectionTableAssessmentInterRaiHistorySelectEntity'
import type {
  SelectionTableAssessmentInterRaiInitSelectInEntity,
  SelectionTableAssessmentInterRaiInitSelectOutEntity,
  taishokikan,
  rireki,
  cpnTucRai,
  ParamComfirm,
} from '~/repositories/cmn/entities/selectionTableAssessmentInterRaiInitSelectEntity'
import type { selectionTableAssessmentInterRaiUpdateInEntity } from '~/repositories/cmn/entities/selectionTableAssessmentInterRaiUpdateEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or11752OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

const or00248 = ref({ uniqueCpId: '' })
const or11871 = ref({ uniqueCpId: '' })
const orHeadLine = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const or10279 = ref({ uniqueCpId: '' })
const orX0007 = ref({ uniqueCpId: '' })
const orX0008 = ref({ uniqueCpId: '' })
const orX0009 = ref({ uniqueCpId: '' })
const or41179 = ref({ uniqueCpId: '' })
const orX0010 = ref({ uniqueCpId: '' })
const or05905 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return (
    screenStore.getCpNavControl(orX0009.value.uniqueCpId) ??
    screenStore.getCpNavControl(orX0010.value.uniqueCpId)
  )
})

// 親情報
const _commonInfo = {
  // 基準日
  baseDate: '',
  // ログイン情報.職員名
  loginUserName: '管理者　太郎',
  // 計画対象期間ID
  sc1Id: '',
  // アセスメントID
  assessmentId: '',
  // 作成者ID
  shokuId: '',
  // 調査アセスメント種別
  surveyAssessmentKind: '',
}

// 計画期間
const planPeriodShow = ref<boolean>(true)
// 履歴
const historyShow = ref<boolean>(true)
// 作成者
const authorShow = ref<boolean>(true)
// 基準日
const baseDateShow = ref<boolean>(true)
// 入力フーム
const inputBoomShow = ref<boolean>(true)

// ローカルTwoway
const _local = reactive({
  // 作成日
  createDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
})

// ローカルOneway
const localOneway = reactive({
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as OrX0007OnewayType,
  // 履歴
  orX0008Oneway: {} as OrX0008OnewayType,
  //作成者
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
  } as OrX0009OnewayType,
  // 基準日
  createDateOneway: {
    itemLabel: t('label.base-date'),
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({ outerClass: 'createDateOuterClass', labelClass: 'ma-1' }),
  } as Mo00020OnewayType,
  Or10279Model: {
    staffId: systemCommonsStore.getStaffId,
    baseDate: systemCommonsStore.getSystemDate,
    officeId: systemCommonsStore.getStaffId,
  } as Or10279OneWayType,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
// 画面状態管理用操作変数
const screenStore = useScreenStore()

// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
or05905.value.uniqueCpId = pageComponent?.uniqueCpId ?? ''

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent?.uniqueCpId, {
  [Or05905Const.CP_ID(1)]: or05905.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [Or11871Const.CP_ID]: or11871.value,
  [OrX0010Const.CP_ID(1)]: orX0010.value,
})
// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.selection-table'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: false,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: false,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
  },
})
/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      void _update()
      setOr11871Event({ saveEventFlg: false })
    }
  }
)
onMounted(async () => {
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or11752Const.STR_ALL })
  })
  // コントロール設定
  await getCommonData()
})
const listPeriod = ref<taishokikan[]>([])
const listHistory = ref<rireki[]>([])
const isFirstInit = ref(true)
const isUsePlanPeriod = ref<boolean>(true)
const getCommonData = async () => {
  const param: SelectionTableAssessmentInterRaiInitSelectInEntity = {
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
  }
  const res: SelectionTableAssessmentInterRaiInitSelectOutEntity = await ScreenRepository.select(
    'SelectionTableAssessmentInterRaiInitSelect',
    param
  )
  if (isFirstInit.value) {
    const selectedSvJigyoId = res.data.tekiyoJigyoList.find(
      (item) => item.tekiyoFlg === '1'
    )?.svJigyoId
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        jigyoInfoList: res.data.tekiyoJigyoList.map((item) => ({
          houjinId: item.houjinId,
          shisetuId: item.shisetuId,
          svJigyoId: item.svJigyoId,
          svJigyoCd: item.svJigyoId, // Using svJigyoId as svJigyoCd - verify if this mapping is correct
          jigyoRyakuKnj: item.jigyoNameKnj,
        })),
      },
    })
    Or41179Logic.data.set({
      uniqueCpId: or41179.value.uniqueCpId,
      value: { modelValue: selectedSvJigyoId },
    })
  }
  if (res.data.kikanFlg === '1') {
    isUsePlanPeriod.value = true
  } else {
    isUsePlanPeriod.value = false
  }
  if (res.data.taishokikanList?.length) {
    listPeriod.value = res.data.taishokikanList
    handleLogicx0007(res.data.taishokikanList.length - 1)
  } else {
    listPeriod.value = []
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = 0
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = 0
  }
  listHistory.value = res.data.rirekiList
  handleMapHistoryData(res, listHistory.value.length - 1)
  isFirstInit.value = false
}
/**
 * 画面コントロール表示設定
 *
 * @param index - 期間リストのインデックス
 */
const handleLogicx0007 = (index: number) => {
  localOneway.orX0007Oneway.planTargetPeriodData = {
    orX0115Oneway: {
      sc1Id: Number(listPeriod.value[index].sc1Id),
    },
    planTargetPeriodId: Number(listPeriod.value[index].sc1Id),
    planTargetPeriod: listPeriod.value[index]?.startYmd + ' ~ ' + listPeriod.value[index]?.endYmd,
    currentIndex: index + 1,
    totalCount: listPeriod.value.length,
  } as PlanTargetPeriodDataType
  OrX0007Logic.data.set({
    uniqueCpId: orX0007.value.uniqueCpId,
    value: {
      planTargetPeriodId: listPeriod.value[index].sc1Id,
      PlanTargetPeriodUpdateFlg: '',
    },
  })
}
const handleLogicx0008 = (index: number) => {
  localOneway.orX0008Oneway.createData = {
    createId: listHistory.value[index]?.raiId ?? '',
    createDate: listHistory.value[index]?.capDateYmd ?? '',
    staffId: listHistory.value[index]?.capShokuId ?? '',
    staffName: listHistory.value[index]?.capShokuNm ?? '',
    currentIndex: index + 1,
    totalCount: listHistory.value.length,
  }
  localOneway.orX0008Oneway.plan1Id = listHistory.value[index]?.raiId ?? ''
  localOneway.orX0008Oneway.officeId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.orX0008Oneway.sc1Id =
    OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? ''
  localOneway.orX0008Oneway.useId = systemCommonsStore.getUserId ?? ''
  OrX0008Logic.data.set({
    uniqueCpId: orX0008.value.uniqueCpId,
    value: {
      createId: listHistory.value[index]?.raiId,
      createUpateFlg: '',
      rirekiObj: {
        createId: listHistory.value[index]?.raiId,
        createDate: listHistory.value[index]?.capDateYmd ?? '',
        staffId: listHistory.value[index]?.capShokuId ?? '',
        staffName: listHistory.value[index]?.capShokuNm ?? '',
        currentIndex: index + 1,
        totalCount: listHistory.value.length,
      },
    },
    isInit: true,
  })
  OrX0009Logic.data.set({
    uniqueCpId: orX0009.value.uniqueCpId,
    value: {
      staffId: listHistory.value[index]?.capShokuId ?? '',
      staffName: listHistory.value[index]?.capShokuNm ?? '',
    },
    isInit: true,
  })
}
const handleMapHistoryData = (
  res: selectionTableAssessmentInterRaiHistorySelectOutEntity,
  index: number
) => {
  handleLogicx0008(index)
  void handleMapDetailForm(res)
}
const handleMapDetailForm = (res: selectionTableAssessmentInterRaiHistorySelectOutEntity) => {
  listHistory.value = res.data.rirekiList
  const selectedRireki = listHistory.value.find(
    (item) => item.raiId === OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId
  )
  localOneway.orX0009Oneway.createData = {
    createDate: selectedRireki?.capDateYmd ?? '',
    createId: Number(selectedRireki?.raiId),
    totalCount: listHistory.value.length,
    currentIndex: listHistory.value.indexOf(selectedRireki!),
    staffName: selectedRireki?.capShokuNm ?? '',
    staffId: Number(selectedRireki?.capShokuId),
  }
  OrX0010Logic.data.set({
    uniqueCpId: orX0010.value.uniqueCpId,
    value: { value: selectedRireki?.capDateYmd ?? '', mo01343: {} as unknown as Mo01343Type },
    isInit: true,
  })
  setDataContent(res.data.cpnTucRaiList)
}
const setDataContent = (data: cpnTucRai, isInit = true) => {
  Or05905Logic.data.set({
    uniqueCpId: or05905.value.uniqueCpId,
    value: data,
    isInit,
  })
}
const periodSelectHandle = async (RirekiId?: string) => {
  const param: selectionTableAssessmentInterRaiPeriodSelectInEntity = {
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    kikanFlg: isUsePlanPeriod.value ? '1' : '0',
    sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
    rirekiId: RirekiId ?? '0',
  }
  const res: selectionTableAssessmentInterRaiPeriodSelectOutEntity = await ScreenRepository.select(
    'selectionTableAssessmentInterRaiPeriodSelect',
    param
  )
  if (res.statusCode === '200') {
    listHistory.value = res.data.rirekiList
    const indexHistory = listHistory.value.findIndex((item) => item.raiId === RirekiId)
    const fallbackIndex = indexHistory === -1 ? listHistory.value.length - 1 : indexHistory
    handleMapHistoryData(res, fallbackIndex)
    return true
  } else {
    return false
  }
}
const historySelectHandle = async () => {
  const param: selectionTableAssessmentInterRaiHistorySelectInEntity = {
    sc1Id: OrX0007Logic.data.get(orX0007.value.uniqueCpId)?.planTargetPeriodId ?? '',
    rirekiId: OrX0008Logic.data.get(orX0008.value.uniqueCpId)?.createId ?? '0',
    kikanFlg: isUsePlanPeriod.value ? '1' : '0',
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
  }
  const res: selectionTableAssessmentInterRaiHistorySelectOutEntity = await ScreenRepository.select(
    'selectionTableAssessmentInterRaiHistorySelect',
    param
  )
  void handleMapDetailForm(res)
}
/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(1)]: or00248.value,
  [OrX0007Const.CP_ID(1)]: orX0007.value,
  [OrX0008Const.CP_ID(1)]: orX0008.value,
  [OrX0009Const.CP_ID(1)]: orX0009.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 削除確認ダイアログを表示する
 *
 * @param param - パラメータ
 */
const openPopupComfirm = (param: ParamComfirm) => {
  return new Promise((resolve) => {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        isOpen: true,
        dialogText: param.message,
        dialogTitle: t('label.confirm'),
        firstBtnType: 'normal1',
        firstBtnLabel: param?.firstBtnLabel ?? t('btn.yes'),
        secondBtnType: param?.secondBtnType ?? 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnLabel: t('btn.cancel'),
        thirdBtnType: param?.thirdBtnType ?? 'blank',
      },
    })
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        if (event?.firstBtnClickFlg) {
          if (param.excuteFunction) {
            void param.excuteFunction()
          }
          resolve(true)
        }
        if (event?.secondBtnClickFlg) {
          if (param.excuteFunction1) {
            void param.excuteFunction1()
          }
        }
        if (event?.thirdBtnClickFlg || event?.closeBtnClickFlg) {
          if (param.excuteFunction2) {
            void param.excuteFunction2()
          }
        }
        resolve(false)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
      },
      { once: true }
    )
  })
}
/**
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg
    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }
    const index = listPeriod.value.findIndex((item) => item.sc1Id === newValue.planTargetPeriodId)
    const isLast = index === listPeriod.value.length - 1
    const isFirst = index === 0
    const handleChangePreiod = async () => {
      const openErrMess = (message: string) => {
        const param: ParamComfirm = {
          message,
          firstBtnLabel: 'OK',
          secondBtnType: 'blank',
        }
        void openPopupComfirm(param)
      }
      if (planUpdateFlg === '0') {
        handleLogicx0007(index)
      } else if (planUpdateFlg === '1') {
        if (isFirst) {
          openErrMess(t('message.i-cmn-11262'))
          return
        }
        handleLogicx0007(index - 1)
      } else if (planUpdateFlg === '2') {
        if (isLast) {
          openErrMess(t('message.i-cmn-11263'))
          return
        }
        handleLogicx0007(index + 1)
      }
      await periodSelectHandle()
    }
    if (isEdit.value && planUpdateFlg !== '0') {
      if ((isLast && planUpdateFlg === '2') || (isFirst && planUpdateFlg === '1')) return
      const saveAndNext = async () => {
        await _update()
        void handleChangePreiod()
      }
      const param: ParamComfirm = {
        message: t('message.i-ldg-10430'),
        excuteFunction1: () => void handleChangePreiod(),
        excuteFunction: () => void saveAndNext(),
        thirdBtnType: 'normal3',
      }
      void openPopupComfirm(param)
    } else {
      void handleChangePreiod()
    }
  },
  { deep: true }
)
/**
 * AC017_「履歴-前へアイコンボタン」押下
 * AC018_「履歴-次へアイコンボタン」押下
 * 履歴変更の監視
 */
watch(
  () => OrX0008Logic.data.get(orX0008.value.uniqueCpId),
  (newValue) => {
    if (isUndefined(newValue)) return
    const createId = String(newValue.createId)
    const createUpateFlg = newValue.createUpateFlg
    if (!createUpateFlg || (listHistory.value.length === 1 && !listHistory.value[0].raiId)) return
    const index = listHistory.value.findIndex((item) => item.raiId === createId)
    const isLast = index === listHistory.value.length - 1
    const isFirst = index === 0
    const handleChangeHistory = () => {
      if (createUpateFlg === '0') {
        handleLogicx0008(index)
      } else if (createUpateFlg === '1') {
        if (isFirst) {
          return
        }
        handleLogicx0008(index - 1)
      } else if (createUpateFlg === '2') {
        if (isLast) {
          return
        }
        handleLogicx0008(index + 1)
      }
      void historySelectHandle()
    }
    if (isEdit.value && createUpateFlg !== '0') {
      if ((isLast && createUpateFlg === '2') || (isFirst && createUpateFlg === '1')) return
      const saveAndNext = async () => {
        await _update()
        void handleChangeHistory()
      }

      const param: ParamComfirm = {
        message: t('message.i-ldg-10430'),
        excuteFunction1: () => void handleChangeHistory(),
        excuteFunction: () => void saveAndNext(),
        thirdBtnType: 'normal3',
      }
      void openPopupComfirm(param)
    } else {
      void handleChangeHistory()
    }
  },
  { deep: true }
)
const _update = async () => {
  if (!isEdit.value) {
    const param: ParamComfirm = {
      message: t('message.i-cmn-21800'),
      firstBtnLabel: t('OK'),
      secondBtnType: 'blank',
    }
    void openPopupComfirm(param)
    return false
  }
  const param: selectionTableAssessmentInterRaiUpdateInEntity = {
    updateKbn: 'U',
    rirekiList: listHistory.value.map((item) => ({
      raiId: item.raiId,
      capType: item.capType,
      capDateYmd: item.capDateYmd,
      capShokuId: item.capShokuId,
    })),
  }
  await ScreenRepository.update('selectionTableAssessmentInterRaiUpdate', param)
}
</script>

<template>
  <c-v-sheet class="view">
    <g-base-or11871 v-bind="or11871" />
    <c-v-row
      no-gutters
      class="main-Content"
    >
      <!-- ナビゲーションエリア -->
      <c-v-col
        cols="2"
        class="hidden-scroll h-100 pa-2"
      >
        <!-- 利用者選択一覧 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <!-- コンテンツエリア -->
      <c-v-col
        cols="10"
        class="main-right hidden-scroll h-100 px-2"
      >
        <!-- 上段 -->
        <c-v-row
          no-gutters
          class="top"
        >
          <c-v-col>
            <!-- 事業所 -->
            <c-v-row
              class="ml-4"
              no-gutters
            >
              <c-v-col cols="auto">
                <g-base-or-41179
                  class="jigyo"
                  v-bind="or41179"
                />
              </c-v-col>
              <!-- 計画対象期間 -->
              <c-v-col
                v-if="planPeriodShow || 1"
                cols="auto"
              >
                <g-custom-orX0007
                  v-bind="orX0007"
                  :parent-method="_update"
                  :oneway-model-value="localOneway.orX0007Oneway"
                  :unique-cp-id="orX0007.uniqueCpId"
                />
              </c-v-col>
              <!-- 基準日 -->
              <c-v-col
                v-if="baseDateShow || 1"
                cols="auto ml-4"
              >
                <g-custom-or-x0010 v-bind="orX0010" />
              </c-v-col>
              <!-- 作成者 -->
              <c-v-col
                v-if="authorShow || 1"
                cols="auto ml-4"
              >
                <!-- TODO GUI00220 職員検索画面未作成 -->
                <g-custom-orX0009
                  v-bind="orX0009"
                  :oneway-model-value="localOneway.orX0009Oneway"
                  :unique-cp-id="orX0009.uniqueCpId"
                />
              </c-v-col>
              <!-- 履歴 -->
              <c-v-col
                v-if="historyShow || 1"
                cols="auto ml-4"
              >
                <g-custom-orX0008
                  v-bind="orX0008"
                  :is-edit="isEdit"
                  :parent-method="_update"
                  :oneway-model-value="localOneway.orX0008Oneway"
                  :unique-cp-id="orX0008.uniqueCpId"
                />
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <!-- 中段 -->
        <c-v-row
          v-show="inputBoomShow"
          no-gutters
          class="middleContent mb-4"
        >
          <v-divider class="mt-4" />
          <g-custom-or05905 v-bind="or05905" />
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <!-- メッセージ -->
  <g-base-or-21814 v-bind="or21814" />
  <g-custom-or-10279
    v-bind="or10279"
    :oneway-model-value="localOneway.Or10279Model"
  />
</template>

<style scoped lang="scss">
:deep(.pl-4) {
  padding-left: 0px !important;
}

.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.main-Content {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;

  .main-right {
    height: 100%;
    display: flex;
    flex-direction: column;

    .top {
      flex: 0 1 auto;

      :deep(.v-sheet) {
        background-color: transparent !important;
      }
    }

    .middleContent {
      display: unset;
      .v-window {
        width: 100%;
      }
    }

    .footer {
      flex: 0 1 auto;
    }
  }
}
.createDateOuterClass {
  background: rgb(var(--v-theme-background));
}

.tabItems {
  border-top: thin solid rgb(var(--v-theme-form));
  margin-top: 8px;
}
:deep(.jigyo) {
  flex-direction: column;
  .v-input {
    width: 204px !important;
  }
  .v-field.v-field--appended {
    background-color: #fff;
  }
  .ma-2 {
    margin-top: 2px !important;
    margin-bottom: 2px !important;
  }
}
:deep(.kikan-label) {
  min-width: 184px !important;
}
</style>
