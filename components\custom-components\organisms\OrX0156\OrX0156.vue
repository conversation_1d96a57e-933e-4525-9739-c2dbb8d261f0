<script setup lang="ts">
/**
 * OrX0156:有機体:入力補助付きテキストエリア
 *
 * @description
 * 入力補助付きテキストエリア
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch } from 'vue'
import type { OrX0156OnewayType, OrX0156Type } from '~/types/cmn/business/components/OrX0156Type'
import { CustomClass } from '~/types/CustomClassType'

/**************************************************
 * Props
 **************************************************/
interface Props {
  /**
   * 双方向バインド用のmodelValue
   */
  modelValue?: OrX0156Type
  /**
   * 単方向バインド用のmodelValue
   */
  onewayModelValue: OrX0156OnewayType
}

const props = defineProps<Props>()
const defaultModelValue = {
  orX0156: {
    value: '',
  } as OrX0156Type,
}

const defaultOnewayModelValue = {
  orX0156Oneway: {
    name: '',
    itemLabel: '',
    isRequired: false,
    mo00037: {},
    isVerticalLabel: false,
    rules: undefined,
    counter: 0,
    persistentCounter: true,
    showItemLabel: false,
    customClass: new CustomClass({ labelClass: 'pa-1' }),
    style: '',
    hintTooltipText: '',
    hideDetails: 'auto',
    // アイコンボタン表示フラグ true:表示する  false:表示しない
    iconBtnDisplayFlg: false,
    // アイコンボタン名
    iconBtnName: 'open_in_new',
    // サイズ維持 true : サイズ変更不可 false:サイズ変更可
    noResize: true,
    // 区切り線表示・非表示フラグ
    showDividerLineFlg: true,
    // 入力支援ボタン表示・非表示フラグ
    showEditBtnFlg: true,
    // アイコンボタン単方向モデル
    mo00009: {
      btnIcon: 'edit_square',
      name: 'serveyLedgerImportBtn',
      density: 'compact',
      class: 'icon-btn',
    },
  } as OrX0156OnewayType,
}

const local = reactive({
  orX0156: {
    ...defaultModelValue.orX0156,
    ...props.modelValue,
  } as OrX0156Type,
})

const localOneway = reactive({
  orX0156Oneway: {
    ...defaultOnewayModelValue.orX0156Oneway,
    ...props.onewayModelValue,
    customClass: {
      ...defaultOnewayModelValue.orX0156Oneway?.customClass,
      ...props.onewayModelValue?.customClass,
    } as CustomClass,
  } as OrX0156OnewayType,
})

watch(
  () => props.modelValue,
  (newValue) => {
    local.orX0156 = {
      ...defaultModelValue.orX0156,
      ...newValue,
    }
  },
  { deep: true }
)
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.orX0156Oneway = {
      ...defaultOnewayModelValue.orX0156Oneway,
      ...newValue,
      customClass: {
        ...defaultOnewayModelValue.orX0156Oneway?.customClass,
        ...newValue?.customClass,
      } as CustomClass,
    }
  },
  { deep: true }
)

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue', 'click', 'blur', 'focus', 'onClickEditBtn'])
watch(
  () => local.orX0156.value,
  () => {
    emit('update:modelValue', local.orX0156)
  }
)

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * アイコンボタンのOnewayを算出する
 */
const mo00009Oneway = computed(() => {
  const workBtnIconName = ref('')

  if (localOneway.orX0156Oneway?.iconBtnName !== undefined) {
    workBtnIconName.value = localOneway.orX0156Oneway?.iconBtnName
  }

  const workObj = ref({
    btnIcon: workBtnIconName.value,
    name: 'OrX0156IconBtn',
    density: 'compact',
    rounded: 'sm',
    size: 'medium',
  })

  return workObj.value
})

/**
 * リサイズの設定を算出する
 */
const resizeStyle = computed(() => {
  const workResizeStyle = ref('vertical') // 初期値：縦方向を許可

  if (
    localOneway.orX0156Oneway?.noResize !== undefined &&
    localOneway.orX0156Oneway?.noResize === true
  ) {
    workResizeStyle.value = 'none' // リサイズ不可
  } else if (localOneway.orX0156Oneway?.resizeStyle !== undefined) {
    workResizeStyle.value = localOneway.orX0156Oneway?.resizeStyle
  }

  return workResizeStyle.value
})
</script>

<template>
  <c-v-sheet
    :class="localOneway.orX0156Oneway.customClass?.outerClass"
    :style="localOneway.orX0156Oneway.customClass?.outerStyle"
  >
    <!-- 縦型ラベル -->
    <c-v-row
      v-if="localOneway.orX0156Oneway.isVerticalLabel && localOneway.orX0156Oneway.showItemLabel"
      no-gutters
      :class="localOneway.orX0156Oneway.customClass?.labelClass"
      :style="localOneway.orX0156Oneway.customClass?.labelStyle"
      class="align-center"
    >
      <!-- 項目名あり -->
      <c-v-col
        v-if="localOneway.orX0156Oneway.itemLabel"
        cols="auto"
      >
        <base-at-label
          class="item-label"
          :value="localOneway.orX0156Oneway.itemLabel"
        />
      </c-v-col>
      <!-- 項目名なし -->
      <c-v-col
        v-else
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 必須バッチ -->
      <c-v-col
        v-if="localOneway.orX0156Oneway.isRequired"
        cols="auto"
      >
        <base-mo00037 :oneway-model-value="localOneway.orX0156Oneway.mo00037" />
      </c-v-col>
      <!-- ヒント -->
      <c-v-col
        v-if="localOneway.orX0156Oneway.hintTooltipText !== ''"
        cols="auto"
      >
        <c-v-tooltip :text="localOneway.orX0156Oneway.hintTooltipText">
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>

      <!-- アイコンボタン -->
      <c-v-col
        v-if="localOneway.orX0156Oneway.iconBtnDisplayFlg === true"
        cols="auto"
      >
        <!-- アイコンボタンを表示 -->
        <base-mo00009
          :oneway-model-value="mo00009Oneway"
          @click="$emit('click', $event)"
        />
      </c-v-col>

      <!-- スロット -->
      <c-v-col v-if="$slots.appendItemSlot">
        <slot name="appendItemSlot"> </slot>
      </c-v-col>
    </c-v-row>

    <c-v-row
      no-gutters
      :class="localOneway.orX0156Oneway.customClass?.itemClass"
      :style="localOneway.orX0156Oneway.customClass?.itemStyle"
    >
      <!-- 横型ラベル:項目名あり -->
      <c-v-col
        v-if="
          !localOneway.orX0156Oneway.isVerticalLabel &&
          localOneway.orX0156Oneway.showItemLabel &&
          localOneway.orX0156Oneway.itemLabel
        "
        class="item-label px-0 pt-0 pb-6 ma-0"
        :class="localOneway.orX0156Oneway.customClass?.labelClass"
        :style="localOneway.orX0156Oneway.customClass?.labelStyle"
        cols="auto"
      >
        <base-at-label
          class="item-label"
          :value="localOneway.orX0156Oneway.itemLabel"
        />
      </c-v-col>
      <!-- 横型ラベル:項目名なし -->
      <c-v-col
        v-else-if="
          !localOneway.orX0156Oneway.isVerticalLabel &&
          localOneway.orX0156Oneway.showItemLabel &&
          !localOneway.orX0156Oneway.itemLabel
        "
        cols="auto"
      >
        <base-at-label
          class="item-label"
          value="&emsp;"
        />
      </c-v-col>
      <!-- 横型:必須バッチ -->
      <c-v-col
        v-if="
          !localOneway.orX0156Oneway.isVerticalLabel &&
          localOneway.orX0156Oneway.showItemLabel &&
          localOneway.orX0156Oneway.isRequired
        "
        class="pa-0 ma-0"
        cols="auto"
      >
        <base-mo00037 :oneway-model-value="localOneway.orX0156Oneway.mo00037" />
      </c-v-col>
      <!-- 横型:ヒント -->
      <c-v-col
        v-if="
          !localOneway.orX0156Oneway.isVerticalLabel &&
          localOneway.orX0156Oneway.showItemLabel &&
          localOneway.orX0156Oneway.hintTooltipText !== ''
        "
        cols="auto"
      >
        <c-v-tooltip :text="localOneway.orX0156Oneway.hintTooltipText">
          <template #activator="{ props: activatorProps }">
            <base-at-icon
              v-bind="activatorProps"
              icon="help fill"
              color="subText"
              size="18"
            />
          </template>
        </c-v-tooltip>
      </c-v-col>

      <!-- 横型:アイコンボタン -->
      <c-v-col
        v-if="
          !localOneway.orX0156Oneway.isVerticalLabel &&
          localOneway.orX0156Oneway.showItemLabel &&
          localOneway.orX0156Oneway.iconBtnDisplayFlg === true
        "
        cols="auto"
      >
        <!-- アイコンボタンを表示 -->
        <base-mo00009
          :oneway-model-value="mo00009Oneway"
          @click="$emit('click', $event)"
        />
      </c-v-col>
      <!-- 横型:スロット -->
      <c-v-col
        v-if="
          !localOneway.orX0156Oneway.isVerticalLabel &&
          localOneway.orX0156Oneway.showItemLabel &&
          $slots.appendItemSlot
        "
      >
        <slot name="appendItemSlot"> </slot>
      </c-v-col>

      <c-v-col class="pa-0 ma-0">
        <!-- テキストエリア -->
        <div
          :class="[
            'd-flex',
            'pa-2',
            'flex-column',
            'root-textarea-container',
            localOneway.orX0156Oneway.contentClass,
          ]"
          :style="localOneway.orX0156Oneway.contentStyle"
        >
          <!-- テキストエリア -->
          <div class="textarea-container">
            <base-at-textarea
              v-model="local.orX0156.value"
              v-bind="{ ...$attrs, ...localOneway.orX0156Oneway }"
              class="text-style"
              :style="{ height: localOneway.orX0156Oneway.height }"
              @blur="$emit('blur', $event)"
              @focus="$emit('focus', $event)"
            >
              <!-- slot -->
              <template v-for="(_, slotName) in $slots">
                <slot :name="slotName" />
              </template>
            </base-at-textarea>
          </div>
          <!-- tag & btn area -->
          <div class="tag-container d-flex ga-2 pt-2">
            <!-- アイコンボタン -->
            <base-mo00009
              v-if="localOneway.orX0156Oneway.showEditBtnFlg"
              :oneway-model-value="localOneway.orX0156Oneway.mo00009"
              class="icon-edit-btn"
              :disabled="localOneway.orX0156Oneway.disabled"
              :readonly="localOneway.orX0156Oneway.readonly"
              @click="$emit('onClickEditBtn', $event)"
            ></base-mo00009>
            <!-- 区切り線 -->
            <div
              v-if="localOneway.orX0156Oneway.showDividerLineFlg"
              class="split-line"
            ></div>
            <slot name="footer" />
          </div>
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
</template>

<style scoped lang="scss">
$icon-width-height: 36px;
$tag-line-min-height: 36px;
$edit-btn-background: #ebf2fd;
$icon-color: #a7bada;
$border-radius: 4px;

// テキストエリアContainer
.root-textarea-container {
  border: 1px solid #c9cdd2;
  border-radius: $border-radius;
  background: #fff;
  // パディングを除去する
  :deep(.v-field__input) {
    padding: 0px;
  }
  // 枠線の除去
  :deep(.v-field__outline__end) {
    border: none !important;
  }
  :deep(.v-field__outline__start) {
    border: none !important;
  }
  :deep(.v-field--focused) {
    box-shadow: none !important;
  }
  :deep(.v-textarea) {
    min-height: 12px;
    overflow: auto;
  }
  :deep(.v-field__input) {
    -webkit-mask-image: none !important;
    mask-image: none !important;
  }
  .tag-container:has(.icon-edit-btn) {
    min-height: $tag-line-min-height;
  }
  .tag-container:empty {
    padding-top: 0px !important;
  }
}
// アイコンボタンのスタイル
.icon-btn {
  width: $icon-width-height;
  height: $icon-width-height;
  min-width: $icon-width-height;
  min-height: $icon-width-height;
  background: $edit-btn-background;
  color: $icon-color;
}
// 区切り線
.split-line {
  width: 2px;
  height: $tag-line-min-height;
  background: $icon-color;
}
:deep(.text-style .v-field) {
  border-radius: 4px 4px 4px 4px;
  resize: v-bind('resizeStyle');
  overflow: hidden;
  flex: 1;
}
:deep(.v-field--error) {
  background-color: rgb(var(--v-theme-red-50)) !important;
}
.root-textarea-container:has(.v-field--focused) {
  box-shadow: 0 0 5px rgb(var(--v-theme-key)) !important;
  color: rgb(var(--v-theme-key));
  border-color: rgb(var(--v-theme-key));
}
.root-textarea-container:has(.v-field--focused.v-field--error) {
  box-shadow: 0 0 5px rgb(var(--v-theme-error)) !important;
  color: rgb(var(--v-theme-error));
  border-color: rgb(var(--v-theme-error));
}
.textarea-container:deep(.v-input__control) {
  .v-field__input {
    min-height: auto !important;
  }
}
</style>
