<script setup lang="ts">
/**
 * Or56579:画面設計書-アセスメント総括取込み設定モーダル
 * GUI00827_「アセスメント総括取込み設定」画面
 *
 * @description
 * 画面設計書-アセスメント総括取込み設定モーダル
 *
 * <AUTHOR> DAO DINH DUONG
 */
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import type { Or56579StateType, Or56579TwoWayType } from './Or56579.type'
import { Or56579Const } from './Or56579.constants'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { Or27301Const } from '~/components/custom-components/organisms/Or27301/Or27301.constants'
import type {
  AssessmentSummaryImportSettingSelectInEntity,
  AssessmentSummaryImportSettingSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentSummaryImportSettingSelectEntity'
import type { AssessmentSummaryImportSettingUpdateInEntity } from '~/repositories/cmn/entities/AssessmentSummaryImportSettingUpdateEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useValidation } from '@/utils/useValidation'

const { byteLength } = useValidation()
/**
 * 国際化関数の取得
 */
const { t } = useI18n()
interface Props {
  uniqueCpId: string
}
/**
 * 親コンポーネントから渡されたプロパティを定義
 */
const props = defineProps<Props>()
/**
 * イベントのemit定義（ダイアログクローズ用）
 */
defineEmits(['confirm'])
/**
 * ユニークなCP IDを保持するリアクティブなオブジェクト。
 */
const or27301 = ref({ uniqueCpId: '' })
/**
 * ユニークなCP IDを保持するリアクティブなオブジェクト。
 */
const or21814 = ref({ uniqueCpId: '' })

useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or27301Const.CP_ID(0)]: or27301.value,
})
/**
 * Mo00024Type 型のリアクティブ変数
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or56579Const.DEFAULT.IS_OPEN,
})
/**
 *  初期状態で使用するデフォルトデータオブジェクト
 */
const defaultData: Or56579TwoWayType = {
  SI012: { value: { modelValue: false }, modifiedCnt: '0' },
  SI013: { value: { modelValue: true }, modifiedCnt: '0' },
  SI014: { value: { modelValue: true }, modifiedCnt: '0' },
  SI017: { value: { modelValue: true }, modifiedCnt: '0' },
  SI020: { value: { value: '【' }, modifiedCnt: '0' },
  SI022: { value: { value: '】' }, modifiedCnt: '0' },
  SI023: { value: { value: '【' }, modifiedCnt: '0' },
  SI025: { value: { value: '】' }, modifiedCnt: '0' },
  SI026: { value: { value: '【' }, modifiedCnt: '0' },
  SI028: { value: { value: '】' }, modifiedCnt: '0' },
  SI031: {
    value: '1',
    options: [
      { value: '1', label: t('label.first-character-only') },
      { value: '2', label: t('label.include-area-name') },
    ],
    modifiedCnt: '0',
  },
  SI032: {
    value: '1',
    options: [
      { value: '1', label: t('label.first-character-only') },
      { value: '2', label: t('label.include-category-name') },
    ],
    modifiedCnt: '0',
  },
  SI033: {
    value: '1',
    options: [
      { value: '1', label: t('label.first-character-only') },
      { value: '2', label: t('label.include-item-name-1') },
    ],
    modifiedCnt: '0',
  },
  SI034: { value: { modelValue: true }, modifiedCnt: '0' },
  SI035: { value: { modelValue: true }, modifiedCnt: '0' },
  SI036: { value: { modelValue: true }, modifiedCnt: '0' },
  SI037: { value: { modelValue: true }, modifiedCnt: '0' },
  SI040: { value: { value: '-' }, modifiedCnt: '0' },
  SI041: { value: { value: '-' }, modifiedCnt: '0' },
  SI042: {
    value: '1',
    options: [
      { value: '1', label: t('label.line-break') },
      { value: '2', label: t('label.text') },
    ],
    modifiedCnt: '0',
  },
  SI043: { value: { value: '-' }, modifiedCnt: '0' },
  SI044: {
    value: '1',
    options: [
      { value: '1', label: t('label.line-break') },
      { value: '2', label: t('label.text') },
    ],
    modifiedCnt: '0',
  },
  SI045: { value: { value: '-' }, modifiedCnt: '0' },
}
/**
 * デフォルトデータのクローンを保持するリアクティブなローカルモデル
 */
const defaultDataEmpty: Or56579TwoWayType = {
  SI012: { value: { modelValue: false }, modifiedCnt: '0' },
  SI013: { value: { modelValue: false }, modifiedCnt: '0' },
  SI014: { value: { modelValue: false }, modifiedCnt: '0' },
  SI017: { value: { modelValue: false }, modifiedCnt: '0' },
  SI020: { value: { value: '' }, modifiedCnt: '0' },
  SI022: { value: { value: '' }, modifiedCnt: '0' },
  SI023: { value: { value: '' }, modifiedCnt: '0' },
  SI025: { value: { value: '' }, modifiedCnt: '0' },
  SI026: { value: { value: '' }, modifiedCnt: '0' },
  SI028: { value: { value: '' }, modifiedCnt: '0' },
  SI031: {
    value: '',
    options: [
      { value: '1', label: t('label.first-character-only') },
      { value: '2', label: t('label.include-area-name') },
    ],
    modifiedCnt: '0',
  },
  SI032: {
    value: '',
    options: [
      { value: '1', label: t('label.first-character-only') },
      { value: '2', label: t('label.include-category-name') },
    ],
    modifiedCnt: '0',
  },
  SI033: {
    value: '',
    options: [
      { value: '1', label: t('label.first-character-only') },
      { value: '2', label: t('label.include-item-name-1') },
    ],
    modifiedCnt: '0',
  },
  SI034: { value: { modelValue: false }, modifiedCnt: '0' },
  SI035: { value: { modelValue: false }, modifiedCnt: '0' },
  SI036: { value: { modelValue: false }, modifiedCnt: '0' },
  SI037: { value: { modelValue: false }, modifiedCnt: '0' },
  SI040: { value: { value: '' }, modifiedCnt: '0' },
  SI041: { value: { value: '' }, modifiedCnt: '0' },
  SI042: {
    value: '',
    options: [
      { value: '1', label: t('label.line-break') },
      { value: '2', label: t('label.text') },
    ],
    modifiedCnt: '0',
  },
  SI043: { value: { value: '' }, modifiedCnt: '0' },
  SI044: {
    value: '',
    options: [
      { value: '1', label: t('label.line-break') },
      { value: '2', label: t('label.text') },
    ],
    modifiedCnt: '0',
  },
  SI045: { value: { value: '' }, modifiedCnt: '0' },
}
/**
 *  デフォルトデータのクローンを保持するリアクティブなローカルモデル
 */
const localModelValue = ref<Or56579TwoWayType>(cloneDeep(defaultDataEmpty))
/**
 *  修正タイプのコードリストを保持するリアクティブ変数
 */
const revitionType = ref<CodeType[]>([])
/**
 *  コード一覧を初期化する非同期関数
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_REVISION_TYPE },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // コード取得
  // 回数区分
  revitionType.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_REVISION_TYPE)
}
/**
 *  評価サマリーインポート設定のリクエストパラメータを保持するリアクティブ変数
 */
const paramRequest = ref<AssessmentSummaryImportSettingSelectInEntity>()
/**
 * OneWayバインドの状態管理
 */
const { setState } = useScreenOneWayBind<Or56579StateType>({
  cpId: Or56579Const.CP_ID(0), // CP IDを取得
  uniqueCpId: props.uniqueCpId, // ユニークなCP ID
  onUpdate: {
    /**
     * isOpen の状態を更新します。
     *
     * @param value - 更新する開閉状態。未定義の場合はデフォルト値を設定します。
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or56579Const.DEFAULT.IS_OPEN // isOpenの状態を更新
    },
    /**
     * パラメータを受け取り処理する関数
     *
     * @param param - 更新する開閉状態。未定義の場合はデフォルト値を設定します。
     */
    param: (param) => {
      paramRequest.value = { ...param!, keyKnjList: Or56579Const.DEFAULT.List_Key_Knj }
    },
  },
})
/**
 *  初期化処理を行う非同期関数
 */
const init = async () => {
  const res: AssessmentSummaryImportSettingSelectOutEntity = await ScreenRepository.select(
    'assessmentSummaryImportSettingSelect',
    paramRequest.value!
  )
  const parameterList = res.data.paramList
  localModelValue.value = {
    SI012: {
      value: {
        modelValue: parameterList[0].paramKnj === Or56579Const.PARAM.PARAM_KNJ_TRUE ? true : false,
      },
      modifiedCnt: parameterList[0].modifiedCnt,
    },
    SI013: {
      value: {
        modelValue: parameterList[1].paramKnj === Or56579Const.PARAM.PARAM_KNJ_TRUE ? true : false,
      },
      modifiedCnt: parameterList[1].modifiedCnt,
    },
    SI014: {
      value: {
        modelValue: parameterList[2].paramKnj === Or56579Const.PARAM.PARAM_KNJ_TRUE ? true : false,
      },
      modifiedCnt: parameterList[2].modifiedCnt,
    },
    SI017: {
      value: {
        modelValue: parameterList[3].paramKnj === Or56579Const.PARAM.PARAM_KNJ_TRUE ? true : false,
      },
      modifiedCnt: parameterList[3].modifiedCnt,
    },
    SI020: {
      value: { value: parameterList[4].paramKnj },
      modifiedCnt: parameterList[4].modifiedCnt,
    },
    SI022: {
      value: { value: parameterList[5].paramKnj },
      modifiedCnt: parameterList[5].modifiedCnt,
    },
    SI023: {
      value: { value: parameterList[6].paramKnj },
      modifiedCnt: parameterList[6].modifiedCnt,
    },
    SI025: {
      value: { value: parameterList[7].paramKnj },
      modifiedCnt: parameterList[7].modifiedCnt,
    },
    SI026: {
      value: { value: parameterList[8].paramKnj },
      modifiedCnt: parameterList[8].modifiedCnt,
    },
    SI028: {
      value: { value: parameterList[9].paramKnj },
      modifiedCnt: parameterList[9].modifiedCnt,
    },
    SI031: {
      value: parameterList[10].paramKnj,
      options: [
        { value: '1', label: t('label.first-character-only') },
        { value: '2', label: t('label.include-area-name') },
      ],
      modifiedCnt: parameterList[10].modifiedCnt,
    },
    SI032: {
      value: parameterList[11].paramKnj,
      options: [
        { value: '1', label: t('label.first-character-only') },
        { value: '2', label: t('label.include-category-name') },
      ],
      modifiedCnt: parameterList[11].modifiedCnt,
    },
    SI033: {
      value: parameterList[12].paramKnj,
      options: [
        { value: '1', label: t('label.first-character-only') },
        { value: '2', label: t('label.include-item-name-1') },
      ],
      modifiedCnt: parameterList[12].modifiedCnt,
    },
    SI034: {
      value: {
        modelValue: parameterList[13].paramKnj === Or56579Const.PARAM.PARAM_KNJ_TRUE ? true : false,
      },
      modifiedCnt: parameterList[13].modifiedCnt,
    },
    SI035: {
      value: {
        modelValue: parameterList[15].paramKnj === Or56579Const.PARAM.PARAM_KNJ_TRUE ? true : false,
      },
      modifiedCnt: parameterList[15].modifiedCnt,
    },
    SI036: {
      value: {
        modelValue: parameterList[14].paramKnj === Or56579Const.PARAM.PARAM_KNJ_TRUE ? true : false,
      },
      modifiedCnt: parameterList[14].modifiedCnt,
    },
    SI037: {
      value: {
        modelValue: parameterList[16].paramKnj === Or56579Const.PARAM.PARAM_KNJ_TRUE ? true : false,
      },
      modifiedCnt: parameterList[16].modifiedCnt,
    },
    SI040: {
      value: { value: parameterList[17].paramKnj },
      modifiedCnt: parameterList[17].modifiedCnt,
    },
    SI041: {
      value: { value: parameterList[18].paramKnj },
      modifiedCnt: parameterList[18].modifiedCnt,
    },
    SI042: {
      value: parameterList[21].paramKnj,
      options: [
        { value: '1', label: t('label.line-break') },
        { value: '2', label: t('label.text') },
      ],
      modifiedCnt: parameterList[21].modifiedCnt,
    },
    SI043: {
      value: { value: parameterList[19].paramKnj },
      modifiedCnt: parameterList[19].modifiedCnt,
    },
    SI044: {
      value: parameterList[22].paramKnj,
      options: [
        { value: '1', label: t('label.line-break') },
        { value: '2', label: t('label.text') },
      ],
      modifiedCnt: parameterList[22].modifiedCnt,
    },
    SI045: {
      value: { value: parameterList[20].paramKnj },
      modifiedCnt: parameterList[20].modifiedCnt,
    },
  }
}
/**
 *  一方向バインディング用のリアクティブなローカルデータ
 */
const localOneway = reactive({
  // 認定情報入力ダイアログの設定
  mo00024Oneway: {
    width: '1300px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or56579',
      toolbarTitle: t('label.assessment-summary-import-setting'),
      toolbarName: 'Or56579ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00018OneWay: {
    showItemLabel: false,
  } as Mo00018OnewayType,
  mo00611InitBtnOneWay: {
    tooltipText: t('tooltip.frame-Width-modified-default-value'),
    btnLabel: t('label.initial-value'),
  } as Mo00611OnewayType,
  mo00611CloseBtnOneWay: {
    tooltipText: t('tooltip.screen-close'),
    btnLabel: t('label.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    tooltipText: t('tooltip.confirm-btn'),
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  mo00045Oneway: {
    maxLength: '2',
    rules: [byteLength(2)],
    width: '30px',
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00039Oneway: {
    showItemLabel: false,
    hideDetails: true,
    checkOff: false,
  } as Mo00039OnewayType,
})
/**
 *  モーダルやドロップダウンを閉じるための関数
 */
const close = () => {
  setState({ isOpen: false })
}
onMounted(async () => {
  await initCodes()
  await nextTick()
  await init()
})
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      close()
    }
  }
)
/**
 *  ユーザーインターフェースに表示するテキストを算出するプロパティ
 */
const textShow1 = computed(() => {
  let string = ''
  if (localModelValue.value.SI012.value.modelValue) {
    if (localModelValue.value.SI031.value === '2') {
      string += `A ${t('label.body')}${localModelValue.value.SI040.value.value}`
    } else {
      string += `A${localModelValue.value.SI040.value.value}`
    }
  }
  if (localModelValue.value.SI013.value.modelValue) {
    if (localModelValue.value.SI032.value === '2') {
      string += `a ${t('label.basic-information')}${localModelValue.value.SI041.value.value}`
    } else {
      string += `a${localModelValue.value.SI041.value.value}`
    }
  }
  if (localModelValue.value.SI014.value.modelValue) {
    string += `(${t('label.a-1')})`
    if (localModelValue.value.SI033.value === '2' && localModelValue.value.SI042.value === '1') {
      string += `${t('label.a-height-weight-bmi')}`
    } else if (
      localModelValue.value.SI033.value === '2' &&
      localModelValue.value.SI042.value === '2'
    ) {
      string += `${t('label.a-height-weight-bmi')}`
    }
    if (localModelValue.value.SI042.value === '1') {
      string += '\n'
    } else {
      string += `${localModelValue.value.SI043.value.value}`
    }
  }
  if (localModelValue.value.SI034.value.modelValue) {
    string += `${localModelValue.value.SI020.value.value}${t('label.choices')}${localModelValue.value.SI022.value.value}`
  }
  if (localModelValue.value.SI035.value.modelValue) {
    string += '〇〇〇〇〇〇'
  }
  return string
})
/**
 *  条件に応じて表示するテキストを計算する算出プロパティ
 */
const textShow2 = computed(() => {
  let string = ''
  if (localModelValue.value.SI036.value.modelValue) {
    string += `${localModelValue.value.SI023.value.value}${t('label.choices')}${localModelValue.value.SI025.value.value}\n`
  }
  if (localModelValue.value.SI037.value.modelValue) {
    if (localModelValue.value.SI044.value === '1') {
      string += '〇〇〇〇〇〇\n'
    } else {
      string += `〇〇〇〇〇〇${localModelValue.value.SI045.value.value}`
    }
  }
  if (localModelValue.value.SI017.value.modelValue) {
    string += `${localModelValue.value.SI026.value.value}${t('label.plan-priority')}${localModelValue.value.SI028.value.value}`
  }
  return string
})
/**
 *  初期状態にデータをリセットする非同期関数
 */
const resetDataToInit = () => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      isOpen: true,
      dialogText: t('message.i-cmn-10894'),
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
    },
  })
  watch(
    () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
    () => {
      const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
      if (event?.firstBtnClickFlg) {
        localModelValue.value = cloneDeep(defaultData)
      }
      // 確認ダイアログのフラグをOFF
      Or21814Logic.event.set({
        uniqueCpId: or21814.value.uniqueCpId,
        events: {
          firstBtnClickFlg: false,
          secondBtnClickFlg: false,
        },
      })
    },
    { once: true }
  )
}
const isLoading = ref(false)
/**
 *  ユーザーの操作を確認し、結果に応じて処理を実行する非同期関数
 */
const confirm = async () => {
  isLoading.value = true
  const inputData: AssessmentSummaryImportSettingUpdateInEntity = {
    ...paramRequest.value!,
    paramList: [
      {
        keyKnj: t('label.item-region'),
        paramKnj: localModelValue.value.SI012.value.modelValue ? '1' : '0',
        modifiedCnt: localModelValue.value.SI012.modifiedCnt,
      },
      {
        keyKnj: t('label.category-name'),
        paramKnj: localModelValue.value.SI013.value.modelValue ? '1' : '0',
        modifiedCnt: localModelValue.value.SI013.modifiedCnt,
      },
      {
        keyKnj: t('label.item-name'),
        paramKnj: localModelValue.value.SI014.value.modelValue ? '1' : '0',
        modifiedCnt: localModelValue.value.SI014.modifiedCnt,
      },
      {
        keyKnj: t('label.plan-priority'),
        paramKnj: localModelValue.value.SI017.value.modelValue ? '1' : '0',
        modifiedCnt: localModelValue.value.SI017.modifiedCnt,
      },
      {
        keyKnj: t('label.frame-1'),
        paramKnj: localModelValue.value.SI020.value.value,
        modifiedCnt: localModelValue.value.SI020.modifiedCnt,
      },
      {
        keyKnj: t('label.frame-2'),
        paramKnj: localModelValue.value.SI022.value.value,
        modifiedCnt: localModelValue.value.SI022.modifiedCnt,
      },
      {
        keyKnj: t('label.frame-3'),
        paramKnj: localModelValue.value.SI023.value.value,
        modifiedCnt: localModelValue.value.SI023.modifiedCnt,
      },
      {
        keyKnj: t('label.frame-4'),
        paramKnj: localModelValue.value.SI025.value.value,
        modifiedCnt: localModelValue.value.SI025.modifiedCnt,
      },
      {
        keyKnj: t('label.frame-5'),
        paramKnj: localModelValue.value.SI026.value.value,
        modifiedCnt: localModelValue.value.SI026.modifiedCnt,
      },
      {
        keyKnj: t('label.frame-6'),
        paramKnj: localModelValue.value.SI028.value.value,
        modifiedCnt: localModelValue.value.SI028.modifiedCnt,
      },
      {
        keyKnj: t('label.domain-name-check'),
        paramKnj: localModelValue.value.SI031.value,
        modifiedCnt: localModelValue.value.SI031.modifiedCnt,
      },
      {
        keyKnj: t('label.category-name-check'),
        paramKnj: localModelValue.value.SI032.value,
        modifiedCnt: localModelValue.value.SI032.modifiedCnt,
      },
      {
        keyKnj: t('label.item-name-check'),
        paramKnj: localModelValue.value.SI033.value,
        modifiedCnt: localModelValue.value.SI033.modifiedCnt,
      },
      {
        keyKnj: t('label.select-1'),
        paramKnj: localModelValue.value.SI034.value.modelValue ? '1' : '0',
        modifiedCnt: localModelValue.value.SI034.modifiedCnt,
      },
      {
        keyKnj: t('label.select-2'),
        paramKnj: localModelValue.value.SI036.value.modelValue ? '1' : '0',
        modifiedCnt: localModelValue.value.SI036.modifiedCnt,
      },
      {
        keyKnj: t('label.text-1'),
        paramKnj: localModelValue.value.SI035.value.modelValue ? '1' : '0',
        modifiedCnt: localModelValue.value.SI035.modifiedCnt,
      },
      {
        keyKnj: t('label.text-2'),
        paramKnj: localModelValue.value.SI037.value.modelValue ? '1' : '0',
        modifiedCnt: localModelValue.value.SI037.modifiedCnt,
      },
      {
        keyKnj: t('label.separator-1'),
        paramKnj: localModelValue.value.SI040.value.value,
        modifiedCnt: localModelValue.value.SI040.modifiedCnt,
      },
      {
        keyKnj: t('label.separator-2'),
        paramKnj: localModelValue.value.SI041.value.value,
        modifiedCnt: localModelValue.value.SI041.modifiedCnt,
      },
      {
        keyKnj: t('label.separator-3'),
        paramKnj: localModelValue.value.SI043.value.value,
        modifiedCnt: localModelValue.value.SI043.modifiedCnt,
      },
      {
        keyKnj: t('label.separator-4'),
        paramKnj: localModelValue.value.SI045.value.value,
        modifiedCnt: localModelValue.value.SI045.modifiedCnt,
      },
      {
        keyKnj: t('label.line-break-1'),
        paramKnj: localModelValue.value.SI042.value,
        modifiedCnt: localModelValue.value.SI042.modifiedCnt,
      },
      {
        keyKnj: t('label.line-break-2'),
        paramKnj: localModelValue.value.SI044.value,
        modifiedCnt: localModelValue.value.SI044.modifiedCnt,
      },
    ],
  }
  await ScreenRepository.update('assessmentSummaryImportSettingUpdate', inputData)
  isLoading.value = false
  close()
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet class="container">
        <c-v-row
          no-gutters
          justify="end"
        >
          <base-mo00611
            :oneway-model-value="localOneway.mo00611InitBtnOneWay"
            @click="resetDataToInit"
          />
        </c-v-row>
        <c-v-row
          class="mt-2"
          no-gutters
        >
          <c-v-col>
            <table class="table-wrapper table-custom">
              <thead>
                <tr>
                  <th style="width: 8%"></th>
                  <th style="width: 62%">{{ t('label.import-to-specific-situation') }}</th>
                  <th style="width: auto">{{ t('label.import-to-user-intention') }}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="px-4">
                    {{ t('label.item-1') }}
                  </td>
                  <td>
                    <c-v-row no-gutters>
                      <c-v-col
                        class="border-r"
                        cols="3"
                      >
                        <base-mo00018
                          v-model="localModelValue.SI012.value"
                          :oneway-model-value="{
                            ...localOneway.mo00018OneWay,
                            checkboxLabel: t('label.area-name'),
                          }"
                        />
                      </c-v-col>
                      <c-v-col
                        class="border-r"
                        cols="3"
                      >
                        <base-mo00018
                          v-model="localModelValue.SI013.value"
                          :oneway-model-value="{
                            ...localOneway.mo00018OneWay,
                            checkboxLabel: t('label.category-name'),
                          }"
                        />
                      </c-v-col>
                      <c-v-col
                        class="border-r"
                        cols="3"
                      >
                        <base-mo00018
                          v-model="localModelValue.SI014.value"
                          :oneway-model-value="{
                            ...localOneway.mo00018OneWay,
                            checkboxLabel: t('label.item-name'),
                          }"
                        />
                      </c-v-col>
                      <c-v-col
                        class="border-r item-center border-none"
                        cols="3"
                      >
                        {{ t('label.judgment-result') }}
                      </c-v-col>
                    </c-v-row>
                  </td>
                  <td>
                    <c-v-row no-gutters>
                      <c-v-col
                        class="border-r item-center"
                        cols="6"
                      >
                        {{ t('label.resident-preference') }}
                      </c-v-col>
                      <c-v-col
                        class="border-r border-none"
                        cols="6"
                      >
                        <base-mo00018
                          v-model="localModelValue.SI017.value"
                          :oneway-model-value="{
                            ...localOneway.mo00018OneWay,
                            checkboxLabel: t('label.plan-priority'),
                          }"
                        />
                      </c-v-col>
                    </c-v-row>
                  </td>
                </tr>
                <tr>
                  <td class="px-4">
                    {{ t('label.enclosure') }}
                  </td>
                  <td>
                    <c-v-row no-gutters>
                      <c-v-col
                        class="border-r"
                        cols="3"
                      />
                      <c-v-col
                        class="border-r"
                        cols="3"
                      />
                      <c-v-col
                        class="border-r"
                        cols="3"
                      />
                      <c-v-col
                        class="border-r border-none"
                        cols="3"
                      >
                        <div class="row-data-input">
                          <base-mo00045
                            v-model="localModelValue.SI020.value"
                            :oneway-model-value="localOneway.mo00045Oneway"
                          />
                          {{ t('label.choices') }}
                          <base-mo00045
                            v-model="localModelValue.SI022.value"
                            :oneway-model-value="localOneway.mo00045Oneway"
                          />
                        </div>
                      </c-v-col>
                    </c-v-row>
                  </td>
                  <td>
                    <c-v-row no-gutters>
                      <c-v-col
                        class="border-r"
                        cols="6"
                      >
                        <div class="row-data-input">
                          <base-mo00045
                            v-model="localModelValue.SI023.value"
                            :oneway-model-value="localOneway.mo00045Oneway"
                          />
                          {{ t('label.choices') }}
                          <base-mo00045
                            v-model="localModelValue.SI025.value"
                            :oneway-model-value="localOneway.mo00045Oneway"
                          />
                        </div>
                      </c-v-col>
                      <c-v-col
                        class="border-r border-none"
                        cols="6"
                      >
                        <div class="row-data-input">
                          <base-mo00045
                            v-model="localModelValue.SI026.value"
                            :oneway-model-value="localOneway.mo00045Oneway"
                          />
                          {{ t('label.plan-priority') }}
                          <base-mo00045
                            v-model="localModelValue.SI028.value"
                            :oneway-model-value="localOneway.mo00045Oneway"
                          />
                        </div>
                      </c-v-col>
                    </c-v-row>
                  </td>
                </tr>
                <tr>
                  <td class="px-4">
                    {{ t('label.item-detail') }}
                  </td>
                  <td>
                    <c-v-row no-gutters>
                      <c-v-col
                        class="border-r"
                        cols="3"
                      >
                        <base-mo00039
                          v-model="localModelValue.SI031.value"
                          style="margin-right: 0 !important; display: flex; align-items: center"
                          :oneway-model-value="localOneway.mo00039Oneway"
                        >
                          <base-at-radio
                            v-for="item in localModelValue.SI031.options"
                            :key="item.value"
                            :radio-label="item.label"
                            :value="item.value"
                          />
                        </base-mo00039>
                      </c-v-col>
                      <c-v-col
                        class="border-r"
                        cols="3"
                      >
                        <base-mo00039
                          v-model="localModelValue.SI032.value"
                          style="margin-right: 0 !important; display: flex; align-items: center"
                          :oneway-model-value="localOneway.mo00039Oneway"
                        >
                          <base-at-radio
                            v-for="item in localModelValue.SI032.options"
                            :key="item.value"
                            :radio-label="item.label"
                            :value="item.value"
                          />
                        </base-mo00039>
                      </c-v-col>
                      <c-v-col
                        class="border-r"
                        cols="3"
                      >
                        <base-mo00039
                          v-model="localModelValue.SI033.value"
                          style="margin-right: 0 !important; display: flex; align-items: center"
                          :oneway-model-value="localOneway.mo00039Oneway"
                        >
                          <base-at-radio
                            v-for="item in localModelValue.SI033.options"
                            :key="item.value"
                            :radio-label="item.label"
                            :value="item.value"
                          />
                        </base-mo00039>
                      </c-v-col>
                      <c-v-col
                        class="border-r border-none flex-col"
                        cols="3"
                      >
                        <base-mo00018
                          v-model="localModelValue.SI034.value"
                          :oneway-model-value="{
                            ...localOneway.mo00018OneWay,
                            checkboxLabel: t('label.choices'),
                          }"
                        />
                        <base-mo00018
                          v-model="localModelValue.SI035.value"
                          :oneway-model-value="{
                            ...localOneway.mo00018OneWay,
                            checkboxLabel: t('label.text'),
                          }"
                        />
                      </c-v-col>
                    </c-v-row>
                  </td>
                  <td>
                    <c-v-row no-gutters>
                      <c-v-col
                        class="border-r flex-col"
                        cols="6"
                      >
                        <base-mo00018
                          v-model="localModelValue.SI036.value"
                          :oneway-model-value="{
                            ...localOneway.mo00018OneWay,
                            checkboxLabel: t('label.choices'),
                          }"
                        />
                        <base-mo00018
                          v-model="localModelValue.SI037.value"
                          :oneway-model-value="{
                            ...localOneway.mo00018OneWay,
                            checkboxLabel: t('label.text'),
                          }"
                        />
                      </c-v-col>
                    </c-v-row>
                  </td>
                </tr>
                <tr>
                  <td class="px-4">
                    {{ t('label.delimiter-1') }}
                  </td>
                  <td>
                    <c-v-row no-gutters>
                      <c-v-col
                        class="border-r pl-2"
                        cols="3"
                      >
                        <base-mo00045
                          v-model="localModelValue.SI040.value"
                          :oneway-model-value="localOneway.mo00045Oneway"
                        />
                      </c-v-col>
                      <c-v-col
                        class="border-r pl-2"
                        cols="3"
                      >
                        <base-mo00045
                          v-model="localModelValue.SI041.value"
                          :oneway-model-value="localOneway.mo00045Oneway"
                        />
                      </c-v-col>
                      <c-v-col
                        class="border-r"
                        cols="3"
                      >
                        <base-mo00039
                          v-model="localModelValue.SI042.value"
                          style="margin-right: 0 !important; display: flex; align-items: center"
                          :oneway-model-value="localOneway.mo00039Oneway"
                        >
                          <base-at-radio
                            v-for="item in localModelValue.SI042.options"
                            :key="item.value"
                            :radio-label="item.label"
                            :value="item.value"
                          />
                        </base-mo00039>
                        <base-mo00045
                          v-model="localModelValue.SI043.value"
                          :oneway-model-value="localOneway.mo00045Oneway"
                        />
                      </c-v-col>
                    </c-v-row>
                  </td>
                  <td>
                    <c-v-row no-gutters>
                      <c-v-col
                        class="border-r"
                        cols="6"
                      >
                        <base-mo00039
                          v-model="localModelValue.SI044.value"
                          style="margin-right: 0 !important; display: flex; align-items: center"
                          :oneway-model-value="localOneway.mo00039Oneway"
                        >
                          <base-at-radio
                            v-for="item in localModelValue.SI044.options"
                            :key="item.value"
                            :radio-label="item.label"
                            :value="item.value"
                          />
                        </base-mo00039>
                        <base-mo00045
                          v-model="localModelValue.SI045.value"
                          :oneway-model-value="localOneway.mo00045Oneway"
                        />
                      </c-v-col>
                    </c-v-row>
                  </td>
                </tr>
              </tbody>
            </table>
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mt-2 pb-2"
        >
          <c-v-col cols="9">
            <table class="table-custom">
              <thead>
                <tr>
                  <th style="width: 100%">{{ t('label.import-image') }}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <c-v-row no-gutters>
                      <c-v-col cols="9">
                        <c-v-row
                          class="header-style"
                          no-gutters
                        >
                          {{ t('label.planner-or-care-manager-judgment') }}
                        </c-v-row>
                        <c-v-row no-gutters>
                          <c-v-col
                            class="header-style border-bottom-none"
                            cols="4"
                          >
                            {{ t('label.specific-situation') }}
                          </c-v-col>
                          <c-v-col cols="8">
                            <c-v-row no-gutters>
                              <c-v-col
                                class="header-style border-bottom-none"
                                cols="4"
                              >
                                {{ t('label.causes-and-factors') }}
                              </c-v-col>
                              <c-v-col
                                class="header-style border-bottom-none"
                                cols="4"
                              >
                                {{ t('label.affected-area') }}
                              </c-v-col>
                              <c-v-col
                                class="header-style border-bottom-none"
                                cols="4"
                              >
                                {{ t('label.worsening-or-improvement-outlook') }}
                              </c-v-col>
                            </c-v-row>
                          </c-v-col>
                        </c-v-row>
                      </c-v-col>
                      <c-v-col
                        class="header-style border-bottom-none height-full"
                        cols="3"
                      >
                        {{ t('label.user-intention-and-perception') }}
                      </c-v-col>
                    </c-v-row>
                  </td>
                </tr>
                <tr>
                  <td>
                    <c-v-row no-gutters>
                      <c-v-row no-gutters>
                        <c-v-col
                          style="align-items: start"
                          class="border-r px-3"
                          cols="3"
                        >
                          {{ textShow1 }}
                        </c-v-col>
                        <c-v-col
                          class="border-r disabled"
                          cols="2"
                        />
                        <c-v-col
                          class="border-r disabled"
                          cols="2"
                        />
                        <c-v-col
                          class="border-r disabled"
                          cols="2"
                        />
                        <c-v-col
                          style="align-items: start"
                          class="border-r px-3 border-none"
                          cols="3"
                        >
                          {{ textShow2 }}
                        </c-v-col>
                      </c-v-row>
                    </c-v-row>
                  </td>
                </tr>
              </tbody>
            </table>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        />
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          class="ml-2"
          :oneway-model-value="{...localOneway.mo00609ConfirmOneway, disabled: isLoading}"
          @click="confirm"
        />
      </c-v-row>
    </template>
    <g-base-or21814 v-bind="or21814" />
  </base-mo00024>
</template>
<style lang="scss" scoped>
:deep(.table-custom) {
  width: 100%;
  border-collapse: collapse;

  .v-field__input {
    padding: 0;
    text-align: center;
  }

  th {
    background-color: rgb(var(--v-theme-black-100)) !important;
    height: 38px;
    text-align: left;
    padding-left: 12px;
    border: 1px rgb(var(--v-theme-black-200)) solid !important;
    font-size: 14px;
    font-weight: bold;
  }

  td {
    transition-duration: 0.28s;
    border-color: rgb(var(--v-theme-black-100)) !important;
    border: 1px rgb(var(--v-theme-black-200)) solid !important;
    font-size: 14px;
  }

  .border-r {
    min-height: 48px;
    align-items: center;
    display: flex;
    border-right: 1px rgb(var(--v-theme-black-200)) solid;
    padding: 0;

    &.item-center {
      justify-content: center;
    }

    &.border-none {
      border-right: none !important;
    }

    &.flex-col {
      flex-direction: column;
      align-items: start;
    }

    &.disabled {
      border-right: 1px rgb(var(--v-theme-black-300)) solid;
      background-color: rgb(var(--v-theme-black-200)) !important;
      min-height: 100px;
    }
  }

  .row-data-input {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    gap: 4px;
  }

  .header-style {
    background-color: rgb(var(--v-theme-black-100)) !important;
    height: 38px;
    text-align: left;
    padding-left: 12px;
    display: flex;
    align-items: center;
    border: 1px rgb(var(--v-theme-black-200)) solid !important;
    border-top: none !important;
    border-left: none !important;

    &.border-bottom-none {
      border-bottom: none !important;
    }

    &.height-full {
      height: auto;
      border-right: none !important;
    }

    font-size: 14px;
    font-weight: bold;
  }
}

.container {
  height: 60vh;
  background-color: transparent;
  overflow-y: auto;
}
</style>
