<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28206Const } from './Or28206.constants'
import type { Or28206StateType, TableData } from './Or28206.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01278Type, Mo01278OnewayType } from '~/types/business/components/Mo01278Type'
import type {
  Or28206Type,
  Or28206OnewayType,
  ShowData,
} from '~/types/cmn/business/components/Or28206Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'

/**
 * Or28206:有機体:表示順変更実施計画～①(処遇内容)モーダル
 * GUI00951_表示順変更実施計画～①(処遇内容)
 *
 * @description
 *［表示順変更:実施計画～①(処遇内容)］画面では、呼び出し元の画面の項目の表示順を並べ替えます。［表示順変更］と［表示順移動］で、並べ替える場合の操作性が異なります。
 *［表示順変更:実施計画～①(処遇内容)］画面は、［ケアマネ］→［計画書］→［実施計画～①］画面→［表示順］ボタンをクリックすると表示されます。
 *※アセスメント方式（新型養護老人ホーム)
 *
 * <AUTHOR> 李晨昊
 */

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or28206Type
  onewayModelValue: Or28206OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const localOneway = reactive({
  Or28206: {
    ...props.onewayModelValue,
  },
  // 情報ダイアログ
  mo00024Oneway: {
    width: '1500px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or28206',
      toolbarTitle: t('label.sort_chg_plan_one'),
      toolbarName: 'Or28206ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00043OneWay: {
    tabItems: [
      { id: 'change', title: t('label.display-order-modified') },
      { id: 'move', title: t('label.display-order-move') },
    ],
  } as Mo00043OnewayType,
  // はいボタン
  mo01265OnewayModelValue: {
    btnLabel: t('btn.delete-display-order'),
    tooltipText: t('tooltip.display-order-delete-success'),
  } as Mo01265OnewayType,
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  mo00009Oneway: {
    btnIcon: 'dialpad',
    labelColor: 'red',
    minHeight: '31px',
    maxHeight: '31px',
  } as Mo00009OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28206StateType>({
  cpId: Or28206Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28206Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28206Const.DEFAULT.IS_OPEN,
})

const or21814 = ref({ uniqueCpId: '' })
const selectedItemIndex = ref<number>(-1)
const local = reactive({
  mo00043: { id: 'change' } as Mo00043Type,
})

// 表示順変更テーブル情報
const tableData = ref<TableData[]>([])

// 表示順変更テーブルヘッダ
const changeTableHeaders = [
  { title: t('label.display-order'), key: 'sort', align: 'start', width: '80px', sortable: false },
  {
    title: t('label.concrete_issues'),
    key: 'concreteIssues',
    align: 'start',
    sortable: false,
    width: '200px',
  },
  {
    title: t('label.life_goal'),
    key: 'lifeGoal',
    align: 'start',
    width: '300px',
    sortable: false,
  },
  {
    title: t('label.goal_period'),
    key: 'goalPeriod',
    align: 'start',
    width: '200px',
    sortable: false,
  },
  {
    title: t('label.treatment_contents'),
    key: 'treatmentContents',
    align: 'start',
    width: '260px',
    sortable: false,
  },
  {
    title: t('label.weekly-plan-manager'),
    key: 'manager',
    align: 'start',
    width: '200px',
    sortable: false,
  },
]

// 表示順移動テーブルヘッダ
const moveTableHeaders = [
  { title: '', key: 'action1', align: 'start', width: '60px', sortable: false },
  {
    title: t('label.display-order'),
    key: 'sort1',
    align: 'start',
    width: '90px',
    sortable: false,
  },
  {
    title: t('label.concrete_issues'),
    key: 'concreteIssues1',
    align: 'start',
    width: '200px',
    sortable: false,
  },
  {
    title: t('label.life_goal'),
    key: 'lifeGoal1',
    align: 'start',
    width: '250px',
    sortable: false,
  },
  {
    title: t('label.goal_period'),
    key: 'goalPeriod1',
    align: 'start',
    width: '240px',
    sortable: false,
  },
  {
    title: t('label.treatment_contents'),
    key: 'treatmentContents1',
    align: 'start',
    width: '300px',
    sortable: false,
  },
  {
    title: t('label.weekly-plan-manager'),
    key: 'manager1',
    align: 'start',
    width: '220px',
    sortable: false,
  },
]

// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  init()
})

/**
 * AC001_初期情報取得
 */
function init() {
  // 「表示順変更」タブ初期表示
  // AC001-1取得した一覧リストデータを一覧に設定する。
  if (props.onewayModelValue.indexList && props.onewayModelValue.indexList.length > 0) {
    const tempArray = props.onewayModelValue.indexList
    let index = 1
    for (const data of tempArray) {
      const item = {
        action: {
          onewayModelValue: {
            icon: 'dialpad',
            color: 'red',
            density: 'compact',
          },
        } as Mo00009OnewayType,
        sort: {
          modelValue: {
            value: String(index),
          } as Mo01278Type,
          onewayModelValue: {
            max: 999,
            min: 1,
          } as Mo01278OnewayType,
        },
        concreteIssues: {
          onewayModelValue: {
            value: data.concreteIssues,
            unit: '',
          } as Mo01337OnewayType,
        },
        lifeGoal: {
          onewayModelValue: {
            value: data.lifeGoal,
            unit: '',
          } as Mo01337OnewayType,
        },
        goalPeriod: {
          onewayModelValue: {
            value: data.goalPeriod,
            unit: '',
          } as Mo01337OnewayType,
        },
        treatmentContents: {
          onewayModelValue: {
            value: data.treatmentContents,
            unit: '',
          } as Mo01337OnewayType,
        },
        manager: {
          onewayModelValue: {
            value: data.manager,
            unit: '',
          } as Mo01337OnewayType,
        },
        sortBackup: data.sortBackup + '',
      } as TableData
      tableData.value.push(item)
      index++
    }
  }
}
/**
 * AC007-2ポップアップウィンドウで選択確認ダイアログを表示し
 *
 * @param errormsg - Message
 */
function showOr21814Msg(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * AC007-2_Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.secondBtnClickFlg) {
      // 注意ダイアログの確認ボタン押下時
      await nextTick()
      await confirmOk()
    }
    if (newValue.thirdBtnClickFlg) {
      // 注意ダイアログの閉じるボタン押下時
      confirmCancle()
    }
  }
)

/**
 * 注意ダイアログの確認ボタン押下時
 */
async function confirmOk() {
  const elementA = tableData.value.splice(dragState.start - 1, 1)[0]
  tableData.value.splice(dragState.end - 1, 0, elementA)
  let index = 1
  for (const item of tableData.value) {
    item.sort.modelValue.value = String(index)
    index++
  }
  await nextTick()
  cssCallBack(dragState.start)
}

/**
 * 注意ダイアログの閉じるボタン押下時
 */
function confirmCancle() {
  cssCallBack(dragState.start)
}

/**
 * AC006_「表示順位削除」押下
 */
function sortDeleteClick() {
  // 表示順リスト表示順リスト内の順序列の内容をクリアする
  // リストのレコードを取得し、ループを使用してレコードの順序に基づいてソートする
  if (tableData.value && tableData.value.length > 0) {
    for (const item of tableData.value) {
      item.sort.modelValue.value = ''
    }
  }
}

const dragState = {
  // 開始索引
  start: -1,
  // 移動時に上書きされるインデックス
  end: -1,
  // 移動中ですか
  dragging: false,
  // 移動方向
  direction: '',
  // 上の浮遊する行
  lastSort: -1,
}

/**
 * AC007_「空白ラベル」ドラッグする_1
 *
 * @param e - $event
 *
 * @param sort - 表示順
 */
function sortBtnMousedown(e: MouseEvent, sort: Mo01278Type) {
  // 選択するとソートが成功します
  // 選択しない場合は記録位置をロールバックします
  dragState.dragging = true
  dragState.start = parseInt(sort.value)

  const htmls = document.getElementsByClassName('suspension' + sort.value)
  if (htmls) {
    for (const item of htmls) {
      if (item) {
        const html = item.parentElement?.parentElement
        if (html) {
          html.style.background = Or28206Const.DEFAULT.COLOR_F2F2F2
        }
      }
    }
  }
}

/**
 * AC007_「空白ラベル」ドラッグする_2
 *
 * @param sort - 表示順
 */
function sortBtnMouseup(sort: Mo01278Type) {
  if (
    dragState.start === dragState.end ||
    dragState.start === parseInt(sort.value) ||
    -1 === parseInt(sort.value)
  ) {
    if (-1 !== parseInt(sort.value)) {
      cssCallBack(dragState.start)
    }
    return
  }
  dragState.end = parseInt(sort.value)

  // ポップアップウィンドウで選択確認ダイアログを表示し
  showOr21814Msg(t('message.i-cmn-10678', [dragState.start, dragState.end]))
}

/**
 * AC007_「空白ラベル」ドラッグする_3
 *
 * @param e - $event
 *
 * @param sort - 表示順
 */
function sortBtnMousemove(e: MouseEvent, sort: Mo01278Type) {
  if (
    dragState.dragging &&
    parseInt(sort.value) !== dragState.lastSort &&
    parseInt(sort.value) !== dragState.start
  ) {
    if (-1 !== dragState.lastSort) {
      const lastHtmls = document.getElementsByClassName('suspension' + dragState.lastSort)
      if (lastHtmls) {
        for (const item of lastHtmls) {
          if (item) {
            const html = item.parentElement?.parentElement
            if (html) {
              html.style.background = ''
            }
          }
        }
      }
    }
    const htmls = document.getElementsByClassName('suspension' + sort.value)
    if (htmls) {
      for (const item of htmls) {
        if (item) {
          const html = item.parentElement?.parentElement
          if (html) {
            html.style.background = Or28206Const.DEFAULT.COLOR_0760e652
            dragState.lastSort = parseInt(sort.value)
          }
        }
      }
    }
  }
}

/**
 * AC007_「空白ラベル」ドラッグする_4
 *
 */
function tdMousemove() {
  if (dragState.dragging) {
    if (-1 === dragState.end) {
      const lastSort = dragState.lastSort
      cssCallBack(dragState.start)
      if (-1 !== lastSort) {
        cssCallBack(lastSort)
      }
    }
  }
}

/**
 * AC002_「×ボタン」押下
 * AC009_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 元素パターン回復
 *
 * @param sort - 表示順
 */
function cssCallBack(sort: number) {
  const element = document.querySelector('.suspension' + sort)
  if (element) {
    const html = element.parentElement?.parentElement
    if (html) {
      html.style.background = 'transparent'
    }
  }

  const lastElement = document.querySelector('.suspension' + dragState.lastSort)
  if (lastElement) {
    const html = lastElement.parentElement?.parentElement
    if (html) {
      html.style.background = 'transparent'
    }
  }

  dragState.start = -1
  dragState.end = -1
  dragState.dragging = false
  dragState.direction = ''
  dragState.lastSort = -1
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * AC008_「確定ボタン」押下
 * 「確定」ボタン押下
 */
function confirm() {
  // 表示されているタブ画面の表示順欄の値で課題データ情報の内容をソートする
  // ※表示順欄に値のない行は順次に最後に移動する
  const respData: Or28206Type = {
    sortList: [],
  }

  if (tableData.value && tableData.value.length > 0) {
    const tempList = new Array<ShowData>()
    for (const item of tableData.value) {
      const data: ShowData = {
        sort: Number(item.sort.modelValue.value),
        concreteIssues: item.concreteIssues.onewayModelValue.value,
        lifeGoal: item.lifeGoal.onewayModelValue.value,
        goalPeriod: item.goalPeriod.onewayModelValue.value,
        treatmentContents: item.treatmentContents.onewayModelValue.value,
        manager: item.manager.onewayModelValue.value,
        sortBackup: item.sortBackup,
      }
      tempList.push(data)
    }
    tempList.sort((a, b) => a.sort - b.sort)
    let index = 1
    for (const item of tempList) {
      item.sort = index
      index++
    }
    respData.sortList = tempList
  }
  // 返却情報.課題データ情報 = ソート後の課題データ情報の内容
  emit('update:modelValue', respData)
  // 本画面を閉じ、親画面に返却する。
  close()
}
/**
 * 「表示順」マウスダウン
 *
 * @param reSetIndex - reSetIndex
 */
async function sortReSetProc(reSetIndex: number) {
  const sortVal = {
    maxSort: 0,
  }
  for (const item of tableData.value) {
    if (
      item &&
      item.sort.modelValue.value !== '' &&
      Number(item.sort.modelValue.value) > sortVal.maxSort
    ) {
      sortVal.maxSort = Number(item.sort.modelValue.value)
    }
  }
  if (tableData.value[reSetIndex].sort.modelValue.value === '') {
    tableData.value[reSetIndex].sort.modelValue.value = String(sortVal.maxSort + 1)
  }
  await nextTick()
}
/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}
/**
 * 数値入力イベント
 *
 *@param reSetIndex - reSetIndex
 */
async function onInputNumber(reSetIndex: number) {
  // 数値以外の文字を削除
  const value = tableData.value[reSetIndex].sort.modelValue.value
  tableData.value[reSetIndex].sort.modelValue.value = value.replace(/[^1-9]/g, '')
  await nextTick()
}

// メニュー切替
watch(
  () => local.mo00043.id,
  async (newValue) => {
    // 変更がない場合、画面データあるかどうかを判定する。
    if (newValue !== t('label.display-order-modified')) {
      let index = 1
      for (const item of tableData.value) {
        item.sort.modelValue.value = String(index)
        index++
      }
    }
    await nextTick()
  }
)
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043"
        :oneway-model-value="localOneway.mo00043OneWay"
        style="padding-left: 0px !important"
      >
      </base-mo00043>
      <!-- タブ switch -->
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item value="change">
          <c-v-card>
            <c-v-card-text class="div-padding">
              <!-- タブ：表示順変更 -->
              <div
                id="sortCard"
                class="padding-bottom"
              >
                <base-mo01265
                  :oneway-model-value="localOneway.mo01265OnewayModelValue"
                  color="#ff0000"
                  label-color="#ff0000"
                  @click="sortDeleteClick"
                >
                </base-mo01265>
              </div>
              <c-v-data-table
                :headers="changeTableHeaders"
                class="table-wrapper"
                hide-default-footer
                :items="tableData"
                fixed-header
                hover
                :items-per-page="-1"
                style="width: 1500px; height: 523px"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedItemIndex === index }"
                    @click="selectRow(index)"
                  >
                    <td style="padding: 0 !important">
                      <base-mo01278
                        v-model="item.sort.modelValue"
                        :oneway-model-value="item.sort.onewayModelValue"
                        style="width: 60px"
                        @click.stop="sortReSetProc(index)"
                        @input="onInputNumber(index)"
                      >
                      </base-mo01278>
                    </td>
                    <td>
                      <div class="d-flex ellipsis align-start h-96">
                        <span class="four white-space">
                          {{ item.concreteIssues.onewayModelValue.value }}</span
                        >
                        <c-v-tooltip
                          activator="parent"
                          location="bottom"
                          :max-width="350"
                          :text="item.concreteIssues.onewayModelValue.value"
                          open-delay="200"
                        />
                      </div>
                    </td>
                    <td>
                      <div class="d-flex ellipsis align-start h-96">
                        <span class="four white-space">
                          {{ item.lifeGoal.onewayModelValue.value }}</span
                        >
                        <c-v-tooltip
                          activator="parent"
                          location="bottom"
                          :max-width="350"
                          :text="item.lifeGoal.onewayModelValue.value"
                          open-delay="200"
                        />
                      </div>
                    </td>
                    <td>
                      <div class="d-flex ellipsis align-start h-96">
                        <span class="four white-space">
                          {{ item.goalPeriod.onewayModelValue.value }}</span
                        >
                        <c-v-tooltip
                          activator="parent"
                          location="bottom"
                          :max-width="350"
                          :text="item.goalPeriod.onewayModelValue.value"
                          open-delay="200"
                        />
                      </div>
                    </td>
                    <td>
                      <div class="d-flex ellipsis align-start h-96">
                        <span class="four white-space">
                          {{ item.treatmentContents.onewayModelValue.value }}</span
                        >
                        <c-v-tooltip
                          activator="parent"
                          location="bottom"
                          :max-width="350"
                          :text="item.treatmentContents.onewayModelValue.value"
                          open-delay="200"
                        />
                      </div>
                    </td>
                    <td>
                      <div class="d-flex ellipsis align-start h-96">
                        <span class="four white-space">
                          {{ item.manager.onewayModelValue.value }}</span
                        >
                        <c-v-tooltip
                          activator="parent"
                          location="bottom"
                          :max-width="350"
                          :text="item.manager.onewayModelValue.value"
                          open-delay="200"
                        />
                      </div>
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
        <c-v-window-item value="move">
          <!-- タブ：表示順移動 -->
          <c-v-card @mouseup="sortBtnMouseup({ value: '-1' } as Mo01278Type)">
            <c-v-card-text
              id="customCard"
              class="div-padding"
            >
              <c-v-data-table
                :headers="moveTableHeaders"
                class="table-wrapper"
                hide-default-footer
                :items="tableData"
                fixed-header
                hover
                :items-per-page="-1"
                style="width: 1500px; height: 523px"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedItemIndex === index }"
                    @click="selectRow(index)"
                  >
                    <td
                      class="bg-col-n"
                      @mousedown="sortBtnMousedown($event, item.sort.modelValue)"
                      @mouseup="sortBtnMouseup(item.sort.modelValue)"
                      @mousemove="sortBtnMousemove($event, item.sort.modelValue)"
                    >
                      <base-mo00009
                        icon="mdi-plus"
                        :oneway-model-value="localOneway.mo00009Oneway"
                        :class="'suspension' + (index + 1)"
                      >
                      </base-mo00009>
                    </td>
                    <td @mousemove="tdMousemove">
                      <base-mo01278
                        style="width: 60px"
                        :model-value="item.sort.modelValue"
                        :oneway-model-value="item.sort.onewayModelValue"
                        :disabled="true"
                      >
                      </base-mo01278>
                    </td>
                    <td @mousemove="tdMousemove">
                      <div class="d-flex ellipsis align-start h-96">
                        <span class="four white-space">
                          {{ item.concreteIssues.onewayModelValue.value }}</span
                        >
                        <c-v-tooltip
                          activator="parent"
                          location="bottom"
                          :max-width="350"
                          :text="item.concreteIssues.onewayModelValue.value"
                          open-delay="200"
                        />
                      </div>
                    </td>
                    <td @mousemove="tdMousemove">
                      <div class="d-flex ellipsis align-start h-96">
                        <span class="four white-space">
                          {{ item.lifeGoal.onewayModelValue.value }}</span
                        >
                        <c-v-tooltip
                          activator="parent"
                          location="bottom"
                          :max-width="350"
                          :text="item.lifeGoal.onewayModelValue.value"
                          open-delay="200"
                        />
                      </div>
                    </td>
                    <td @mousemove="tdMousemove">
                      <div class="d-flex ellipsis align-start h-96">
                        <span class="four white-space">
                          {{ item.goalPeriod.onewayModelValue.value }}</span
                        >
                        <c-v-tooltip
                          activator="parent"
                          location="bottom"
                          :max-width="350"
                          :text="item.goalPeriod.onewayModelValue.value"
                          open-delay="200"
                        />
                      </div>
                    </td>
                    <td @mousemove="tdMousemove">
                      <div class="d-flex ellipsis align-start h-96">
                        <span class="four white-space">
                          {{ item.treatmentContents.onewayModelValue.value }}</span
                        >
                        <c-v-tooltip
                          activator="parent"
                          location="bottom"
                          :max-width="350"
                          :text="item.treatmentContents.onewayModelValue.value"
                          open-delay="200"
                        />
                      </div>
                    </td>
                    <td @mousemove="tdMousemove">
                      <div class="d-flex ellipsis align-start h-96">
                        <span class="four white-space">
                          {{ item.manager.onewayModelValue.value }}</span
                        >
                        <c-v-tooltip
                          activator="parent"
                          location="bottom"
                          :max-width="350"
                          :text="item.manager.onewayModelValue.value"
                          open-delay="200"
                        />
                      </div>
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
      </c-v-window>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="confirm"
        >
          <!--ツールチップ表示："設定を確定します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialog"
    v-bind="or21814"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
:deep(.txt:disabled) {
  background: inherit;
}
:deep(.material-symbols-rounded) {
  color: red;
}

.div-padding {
  padding: 8px 0 0 0 !important;
}

.padding-bottom {
  padding-bottom: 8px !important;
}

.table-wrapper :deep(.v-row.v-row--no-gutters > .v-col) {
  padding: 0px !important;
  font-size: 14px;
}

:deep(.v-card-text) {
  padding-left: 0 !important;
  padding-right: 0 !important;
  padding-bottom: 0 !important;
  padding-top: 8px !important;
}
.bg-col-n {
  background-color: unset !important;
}
:deep(.v-card-item) {
  padding-left: 0 !important;
  padding-top: 8px !important;
  padding-bottom: 0 !important;
}

.d-flex {
  padding: 16px 0px 0px !important;
  white-space: pre;
}
.h-96 {
  height: 96px;
}
.ellipsis {
  .four {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-wrap;
  }
  .two {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-wrap;
  }
}
</style>
