<script setup lang="ts">
/**
 * OrX0193：有機体：（確定版）利用者(患者)基本情報について
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { fa } from 'vuetify/iconsets/fa'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or28287Logic } from '../Or28287/Or28287.logic'
import { Or28287Const } from '../Or28287/Or28287.constants'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import { OrX0157Const } from '../OrX0157/OrX0157.constants'
import { OrX0193Const } from './OrX0193.constants'
import type { OrX0193OneWayType, OrX0193ValuesType } from './OrX0193.Type'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo01343OnewayType } from '~/types/business/components/Mo01343Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { Or28287Type } from '~/types/cmn/business/components/Or28287Type'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
import type { Mo01267OnewayType } from '~/types/business/components/Mo01267Type'
/**************************************************
 * Props
 **************************************************/
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()

const { refValue } = useScreenTwoWayBind<OrX0193ValuesType>({
  cpId: OrX0193Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<OrX0193ValuesType> }
useScreenOneWayBind<OrX0193OneWayType>({
  cpId: OrX0193Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value!
    },
  },
})

const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル
const or28287 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or28287Const.CP_ID(1)]: or28287.value,
})
/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  commonInfo: {} as TeX0012Type,
  orX0160Type: {
    startKikan: '',
    endKikan: '',
  },
  or28287: {
    /** 保険者*/
    hokensya: '',
    /** 被保険者番号*/
    hHokenNo: '',
    /** 認定有効開始日（表示用）*/
    certificationValidityStartDate: '',
    /**  認定有効終了日（表示用）*/
    certificationValidityEndDate: '',
    /** 要介護度*/
    yokaiKnj: '',
    /** 限度額*/
    limit: '',
  },
  or51775: { modelValue: '' } as Or51775Type,
  mo01343: {
    value: '',
    endValue: '',
    mo00024: { isOpen: false },
  },
  orX0193: {
    yokaiKbn1: {
      modelValue: '',
    },
    yokaiKbn2: {
      modelValue: '',
    },
  },
})
const localOneway = reactive({
  codeListOneway: {} as Record<string, CodeType[]>,
  orX0156Oneway: {
    itemLabel: t('label.discussion-content-about-medical-care'),
    showItemLabel: true,
    showDividerLineFlg: true,
    showEditBtnFlg: true,
    isVerticalLabel: true,
    customClass: {
      labelClass: 'pa-0',
      outerClass: 'w-982',
    } as CustomClass,
    maxRows: '4',
    rows: '4',
    noResize: true,
    autoGrow: false,
    maxlength: '4000',
  },
  orX0156Oneway2: {
    itemLabel: t(
      'label.additional-end-of-life-care-information-to-share-with-medical-institutions'
    ),
    showItemLabel: true,
    showDividerLineFlg: true,
    showEditBtnFlg: true,
    isVerticalLabel: true,
    customClass: {
      labelClass: 'pa-0',
      outerClass: 'w-982',
    } as CustomClass,
    maxRows: '4',
    rows: '4',
    noResize: true,
    autoGrow: false,
    maxlength: '4000',
  },
  orX0160Oneway: {
    showItemLabel: false,
    itemLabel: t('label.validity_period_label'),
    isRequired: false,
    showEditBtnFlg: true,
  },
  or28287Oneway: {
    /** 利用者ID */
    userId: local.commonInfo.userId,
  },
  // GUI00937 共通入力支援画面
  or51775Oneway: {
    screenId: OrX0193Const.DEFAULT.GUI,
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: OrX0193Const.DEFAULT.ASSESS_MENT_METHOD, // アセスメント方式 TBD
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  mo01343Oneway: {
    selectMode: OrX0193Const.DEFAULT.VALUE_1,
  } as Mo01343OnewayType,
  mo00020Oneway: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: true,
    itemLabel: t('label.final-implementation-date'),
    showSelectArrow: false,
    width: '140px',
  } as Mo00020OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Oneway: {
    valueFontWeight: 'blod',
    value: t('label.about-medications'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
      outerClass: 'content-title',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338Oneway2: {
    valueFontWeight: 'blod',
    value: t('label.end_of_life_care_label'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
      outerClass: 'content-title',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039Oneway: {
    itemLabel: t('label.residence_type_label'),
    showItemLabel: true,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,
  mo00039Oneway2: {
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,

  // 文字数入力
  mo00038Oneway1: {
    mo00045Oneway: {
      appendLabel: t('label.building_floors_label'),
      showItemLabel: false,
      maxLength: '999',
      width: '107px',
      customClass: new CustomClass({
        outerClass: 'mx-4',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  // 文字数入力
  mo00038Oneway2: {
    mo00045Oneway: {
      itemLabel: t('label.room_label'),
      appendLabel: t('label.floor_label'),
      showItemLabel: true,
      isVerticalLabel: false,
      maxLength: '20',
      width: '80px',
      customClass: new CustomClass({
        labelClass: 'd-flex align-center mr-2',
        labelStyle: 'padding:0 !important',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  // 文字数入力
  mo00038Oneway3: {
    mo00045Oneway: {
      appendLabel: t('label.percentage_label'),
      showItemLabel: false,
      maxLength: '20',
      width: '77px',
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  mo00040Oneway1: {
    itemTitle: 'label',
    itemValue: 'value',
    width: '203px',
    showItemLabel: false,
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,
  mo00040Oneway2: {
    itemTitle: 'label',
    itemValue: 'value',
    itemLabel: t('label.relationship'),
    width: '77px',
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,
  mo00018Oneway1: {
    name: t('label.application_in_progress_flag'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.application_in_progress_flag'),
  } as Mo00018OnewayType,
  mo00018Oneway2: {
    name: t('label.classification_change'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.classification_change'),
  } as Mo00018OnewayType,
  mo00018Oneway3: {
    name: t('label.unapplied_flag'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.unapplied_flag'),
  } as Mo00018OnewayType,
  mo00018Oneway4: {
    name: t('label.physician_judgment'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.physician_judgment'),
  } as Mo00018OnewayType,
  mo00018Oneway5: {
    name: t('label.care_manager_judgment'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.care_manager_judgment'),
  } as Mo00018OnewayType,
  orX0157Oneway: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        isVerticalLabel: false,
        showItemLabel: true,
        itemLabel: t('label.job-type'),
        width: '253px',
        maxlength: '40',
      },
    },
  },
  orX0157Oneway7: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        isVerticalLabel: false,
        showItemLabel: true,
        itemLabel: t('label.assistance-content'),
        width: '548px',
        maxlength: '60',
      },
    },
  },
  orX0157Oneway2: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        isVerticalLabel: false,
        showItemLabel: true,
        itemLabel: t('label.medication_management_method_label'),
        width: '544px',
        maxlength: '60',
      },
    },
  },
  orX0157Oneway3: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: false,
        width: '253px',
        maxlength: '40',
      },
    },
  },
  orX0157Oneway4: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        isVerticalLabel: false,
        showItemLabel: true,
        itemLabel: t('label.documents-recorded-during-admission'),
        width: '253px',
        maxlength: '40',
      },
    },
  },
  orX0157Oneway5: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: false,
    text: {
      orX0157InputOneway: {
        isVerticalLabel: false,
        showItemLabel: true,
        itemLabel: t('label.name-consent') + '：',
        width: '295px',
        maxlength: '40',
      },
    },
  },
  orX0157Oneway6: {
    inputMode: OrX0157Const.INPUT_MODE.TEXT_ONLY,
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        showItemLabel: false,
        width: '548px',
        maxlength: '60',
      },
    },
  },
  mo01267Oneway: {
    btnLabel: t('label.care-level-at-discharge'),
    to: '',
  } as Mo01267OnewayType,
  honinnKazokuDataList: [
    {
      label: t('label.please-refer-below'),
      key: 'honinnKazokuIkouKbn1',
    },
    {
      label: t('label.refer-to-attachment'),
      key: 'honinnKazokuIkouKbn2',
    },
  ],
  drugKaijoDataList: [
    {
      label: t('label.adl_independent'),
      key: 'drugKaijo1',
    },
    {
      label: t('label.adl_partial_assistance'),
      key: 'drugKaijo2',
    },
    {
      label: t('label.adl_full_assistance'),
      key: 'drugKaijo3',
    },
  ],
  sankaKazokuDataList: [
    {
      label: t('label.person'),
      key: 'sankaHonnin',
    },
    {
      label: t('label.famMemoKnj-label'),
      key: 'sankaKazoku',
    },
    {
      label: t('label.medical-care-team'),
      key: 'sankaIryouCare',
    },
    {
      label: t('label.others'),
      key: 'sankaSonota',
    },
  ],
})

// ダイアログ表示フラグ
const showDialogOr28287 = computed(() => {
  // Or28287のダイアログ開閉状態
  return Or28287Logic.state.get(or28287.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleNenkinMemoKnj = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  localOneway.or51775Oneway.title = t('label.pension_type_label')
  localOneway.or51775Oneway.t2Cd = OrX0193Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = OrX0193Const.DEFAULT.VALUE_5
  localOneway.or51775Oneway.columnName = 'nenkin_memo_knj'
  localOneway.or51775Oneway.inputContents = t('label.pension_type_label')
  // local.or51775.modelValue = refValue.value.orX0193Values.nenkinMemoKnj?.value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleCare = () => {
  Or28287Logic.state.set({
    uniqueCpId: or28287.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * or28287の値変更を監視
 *
 * @param or28287 - Or28287Type
 */
const handleOr28287 = (or28287: Or28287Type) => {
  console.log(or28287, 12331)
}

/**
 * mo00039の値変更を監視
 *
 * @param mo00039 - Mo00039Type
 */
const handleMo00039 = (mo00039: string) => {
  if (mo00039 === OrX0193Const.DEFAULT.VALUE_0) {
    local.orX0193.yokaiKbn2.modelValue = ''
    refValue.value.orX0193Values.yokaiKbnFlg = local.orX0193.yokaiKbn1.modelValue
  } else if (mo00039 === OrX0193Const.DEFAULT.VALUE_1) {
    local.orX0193.yokaiKbn1.modelValue = ''
    refValue.value.orX0193Values.yokaiKbnFlg = local.orX0193.yokaiKbn2.modelValue
  }
}

/**
 * mo00039の値変更を監視
 *
 * @param mo00039 - Mo00039Type
 */
const handleShogaiNintei = (mo00039: string) => {
  // if (mo00039 === OrX0193Const.DEFAULT.VALUE_0) {
  //   refValue.value.orX0193Values.shintaiShogaiKbn.modelValue = false
  //   refValue.value.orX0193Values.chitekiShogaiKbn.modelValue = false
  //   refValue.value.orX0193Values.seishinShogaiKbn.modelValue = false
  // }
}

/**
 * mo00018の値変更を監視
 *
 * @param mo00018 - mo00018
 */
const handleMo00018 = (mo00018: Mo00018Type) => {
  if (mo00018.modelValue) {
    refValue.value.orX0193Values.shogaiTechouUmu = OrX0193Const.DEFAULT.VALUE_1
  }
}

/**
 * codeListOnewayの値変更を監視
 *
 */
watch(
  () => localOneway.codeListOneway,
  (newVal) => {
    localOneway.mo00040Oneway1.items = newVal.SUPPORT_LEVEL_VALUE
    localOneway.mo00040Oneway2.items = newVal.CARE_LEVEL_VALUE
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0012Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    if (newValue?.teikyouId) {
      local.commonInfo = newValue
      localOneway.mo01267Oneway.disabled =
        local.commonInfo.nursingCareInsuranceAuthorityFlag === OrX0193Const.DEFAULT.VALUE_0
      localOneway.or51775Oneway.userId = newValue.userId ?? ''
    }
  },
  { deep: true }
)
</script>

<template>
  <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
  <div
    v-if="refValue.orX0193Values"
    class="box"
  >
    <!-- 内服薬 -->
    <div class="data-cell">
      <p>{{ t('label.oral_medication') }}</p>
      <base-mo00039
        v-model="refValue.orX0193Values.drugUmu"
        :oneway-model-value="localOneway.mo00039Oneway2"
      >
        <base-at-radio
          v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_HOUSEHOLD"
          :key="index"
          :name="'radio' + '-' + index"
          :radio-label="item.label"
          :value="item.value"
        />
      </base-mo00039>
    </div>
    <!-- 居宅療養管理指導 -->
    <div class="data-cell d-flex align-end">
      <div class="mr-2">
        <p>{{ t('label.home_healthcare_management') }}</p>
        <base-mo00039
          v-model="refValue.orX0193Values.ryouyouKanriUmu"
          :oneway-model-value="localOneway.mo00039Oneway2"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_HOUSEHOLD"
            :key="index"
            :name="'radio' + '-' + index"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
      </div>
      <div class="d-flex align-center">
        (
        <g-custom-or-x-0157
          v-model="refValue.orX0193Values.ryouyouKanriShokushuKnj"
          :oneway-model-value="localOneway.orX0157Oneway"
        ></g-custom-or-x-0157>
        )
      </div>
    </div>
    <!-- 薬剤管理 -->
    <div class="d-flex data-cell align-end">
      <div class="mr-2">
        <p>{{ t('label.medicine-management') }}</p>
        <base-mo00039
          v-model="refValue.orX0193Values.drugKanri"
          :oneway-model-value="localOneway.mo00039Oneway2"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_HOUSEHOLD"
            :key="index"
            :name="'radio' + '-' + index"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
      </div>
      <div class="d-flex align-center">
        (
        <g-custom-or-x-0157
          v-model="refValue.orX0193Values.drugKanriKnj"
          :oneway-model-value="localOneway.orX0157Oneway2"
        ></g-custom-or-x-0157>
        )
      </div>
    </div>
    <!-- 服薬介助 -->
    <div class="data-cell d-flex align-end">
      <div
        v-for="(item, index) in localOneway.drugKaijoDataList"
        :key="index"
        class="d-flex align-end"
      >
        <div>
          <p v-if="index === 0">{{ t('label.medication-assistance') }}</p>
          <base-mo00018
            v-model="refValue.orX0193Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: false,
              itemLabel: '',
              checkboxLabel: item.label,
            }"
          />
        </div>
        <div
          v-if="index === 1"
          class="d-flex align-center"
        >
          (
          <g-custom-or-x-0157
            v-model="refValue.orX0193Values.drugKaijoKnj"
            :oneway-model-value="localOneway.orX0157Oneway7"
          ></g-custom-or-x-0157>
          )
        </div>
      </div>
    </div>
    <!-- 薬剤アレルギー -->
    <div class="data-cell d-flex align-end">
      <div class="mr-2">
        <p>{{ t('label.medication-allergy') }}</p>
        <base-mo00039
          v-model="refValue.orX0193Values.drugAllergy"
          :oneway-model-value="localOneway.mo00039Oneway2"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_HOUSEHOLD"
            :key="index"
            :name="'radio' + '-' + index"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
      </div>
      <div class="d-flex align-center">
        (
        <g-custom-or-x-0157
          v-model="refValue.orX0193Values.drugAllergyKnj"
          class="ml-1"
          :oneway-model-value="localOneway.orX0157Oneway3"
        ></g-custom-or-x-0157>
        )
      </div>
    </div>
    <div class="d-flex align-end">
      <div class="mr-2">
        <p>{{ t('label.special-note-matter-support') }}</p>
        <base-mo00039
          v-model="refValue.orX0193Values.drugTokkiUmu"
          :oneway-model-value="localOneway.mo00039Oneway2"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_HOUSEHOLD"
            :key="index"
            :name="'radio' + '-' + index"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
      </div>
      <div class="d-flex align-center">
        (
        <g-custom-or-x-0157
          v-model="refValue.orX0193Values.drugTokkiKnj"
          class="ml-1"
          :oneway-model-value="localOneway.orX0157Oneway3"
        ></g-custom-or-x-0157>
        )
      </div>
    </div>
  </div>
  <!-- ６．人生の最終段階における医療・ケアに関する情報  -->
  <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway2"></base-mo01338>
  <div
    v-if="refValue.orX0193Values"
    class="box"
  >
    <div class="title">
      {{ t('label.note-verify-latest-intent') }}
    </div>
    <!-- 意向の話し合い -->
    <div class="mt-6">
      <base-mo00039
        v-model="refValue.orX0193Values.ikouHanashiai"
        :oneway-model-value="localOneway.mo00039Oneway2"
      >
        <div class="d-flex flex-column">
          <div
            v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_HOUSEHOLD"
            :key="index"
          >
            <div class="d-flex flex-column">
              <p v-if="index === 0">{{ t('label.home_healthcare_management') }}</p>
              <div class="d-flex">
                <base-at-radio
                  :name="'radio' + '-' + index"
                  :radio-label="item.label"
                  :value="item.value"
                />
                <div
                  v-if="index === 0"
                  class="d-flex align-center"
                >
                  （
                  <base-mo00020
                    v-model="refValue.orX0193Values.ikouHanashiaiLastYmd"
                    class="mr-1"
                    :oneway-model-value="localOneway.mo00020Oneway"
                  />
                  ）
                </div>
                <div
                  v-if="index === 1"
                  class="d-flex align-center"
                >
                  （
                  <base-mo00039
                    v-model="refValue.orX0193Values.ikouHanashiaiKbn"
                    :oneway-model-value="localOneway.mo00039Oneway2"
                  >
                    <base-at-radio
                      v-for="(itm, idx) in localOneway.codeListOneway.CONFIRMATION_INFO_HOUSEHOLD"
                      :key="idx"
                      :name="'radio' + '-' + idx"
                      :radio-label="itm.label"
                      :value="itm.value"
                    />
                  </base-mo00039>
                  ）
                </div>
              </div>
            </div>
          </div>
        </div>
      </base-mo00039>
    </div>
  </div>

  <div
    v-if="refValue.orX0193Values"
    class="box"
  >
    <div class="title">
      {{ t('label.note-only-if-discussed-with-patient-family') }}
    </div>
    <!-- 本人・家族の意向 -->
    <div class="data-cell d-flex mt-6">
      <div
        v-for="(item, index) in localOneway.honinnKazokuDataList"
        :key="index"
        class="d-flex align-end"
      >
        <div>
          <p v-if="index === 0">{{ t('label.individual-family-intent') }}</p>
          <base-mo00018
            v-model="refValue.orX0193Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: false,
              itemLabel: '',
              checkboxLabel: item.label,
            }"
          />
        </div>
        <div
          v-if="index === 1"
          class="d-flex align-center ml-1"
        >
          （
          <g-custom-or-x-0157
            v-model="refValue.orX0193Values.honinnKazokuIkouShoruiKnj"
            :oneway-model-value="localOneway.orX0157Oneway4"
          ></g-custom-or-x-0157>
          ）
        </div>
      </div>
    </div>

    <!-- 話し合いへの参加者 -->
    <div class="mb-6">
      {{ t('label.participants-in-discussion') }}
      <div class="d-flex flex-column">
        <div
          v-for="(item, index) in localOneway.sankaKazokuDataList"
          :key="index"
          class="d-flex"
        >
          <base-mo00018
            v-model="refValue.orX0193Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: false,
              itemLabel: '',
              checkboxLabel: item.label,
            }"
          />
          <div
            v-if="index === 1"
            class="ml-6"
          >
            <div class="d-flex data-cell align-center">
              （
              <g-base-or-12002 class="icon-edit-btn mx-1" />
              <div class="d-flex align-center">
                （
                <g-custom-or-x-0157
                  v-model="refValue.orX0193Values.sankaKazoku1Knj"
                  :oneway-model-value="localOneway.orX0157Oneway5"
                ></g-custom-or-x-0157>
              </div>
              <div class="d-flex align-center ml-3">
                {{ t('label.relationship') }}：
                <base-mo00040
                  v-model="refValue.orX0193Values.sankaKazoku1Zcode"
                  :oneway-model-value="localOneway.mo00040Oneway1"
                />
              </div>
              ）
            </div>
            <div class="d-flex align-center">
              （
              <g-base-or-12002 class="icon-edit-btn mx-1" />
              <div class="d-flex align-center">
                （
                <g-custom-or-x-0157
                  v-model="refValue.orX0193Values.sankaKazoku2Knj"
                  :oneway-model-value="localOneway.orX0157Oneway5"
                ></g-custom-or-x-0157>
              </div>
              <div class="d-flex align-center ml-3">
                {{ t('label.relationship') }}：
                <base-mo00040
                  v-model="refValue.orX0193Values.sankaKazoku2Zcode"
                  :oneway-model-value="localOneway.mo00040Oneway1"
                />
              </div>
              ）
            </div>
          </div>
          <div v-if="index === 3">
            <div class="d-flex align-center">
              （
              <g-custom-or-x-0157
                v-model="refValue.orX0193Values.sankaSonotaKnj"
                class="ml-1"
                :oneway-model-value="localOneway.orX0157Oneway6"
              ></g-custom-or-x-0157>
              ）
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="mb-6">
      <g-custom-or-x-0156
        v-model="refValue.orX0193Values.hanashiaiKnj"
        :oneway-model-value="localOneway.orX0156Oneway"
      ></g-custom-or-x-0156>
    </div>
    <div class="data-cell">
      <g-custom-or-x-0156
        v-model="refValue.orX0193Values.sonotaKyouyuuKnj"
        :oneway-model-value="localOneway.orX0156Oneway2"
      ></g-custom-or-x-0156>
    </div>
  </div>
</template>

<style scoped lang="scss">
$border-radius: 4px;
$icon-edit-btn-width: 36px;
$icon-border-radius: 3px;
$border-color: #cdcdcd;
$icon-color: #869fca;
$edit-btn-background: #ebf2fd;
.content-title {
  padding: 11px 24px;
  background-color: #e6e6e6;
  height: 48px;
}
.title {
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 48px;
  background-color: #0760e614;
  font-weight: Bold;
  font-size: 14px;
  :deep(.v-btn__content) {
    font-weight: bold;
    color: #214d97; /* 文字颜色 */
  }
}
.box {
  margin: 24px 50px 24px 48px;
}
:deep(.v-selection-control-group) {
  align-items: center !important;
}
:deep(.v-row--no-gutters) {
  margin: 0 !important;
}
.data-cell {
  margin-bottom: 16px;
}
:deep(.v-btn--density-default) {
  height: 100% !important;
  min-height: 0 !important;
}
:deep(.kikan-range-container) {
  .d-flex {
    align-items: center;
  }
}
.care-level {
  padding: 11px 0 24px 0;
}
:deep(.v-input__append) {
  margin-inline-start: 8px !important ;
}
:deep(.input-container) {
  .v-field {
    width: 48px !important;
  }
}
:deep(.split-line) {
  display: none;
}
.w-982 {
  width: 982px;
}
// 入力補助ボタンのスタイル
.icon-edit-btn {
  border: 1px solid $border-color;
  background: $edit-btn-background;
  color: $icon-color;
  width: $icon-edit-btn-width;
  height: 34px;
  border-top-left-radius: $icon-border-radius !important;
  border-end-start-radius: $icon-border-radius !important;
  border-top-right-radius: $icon-border-radius !important;
  border-end-end-radius: $icon-border-radius !important;
}
</style>
