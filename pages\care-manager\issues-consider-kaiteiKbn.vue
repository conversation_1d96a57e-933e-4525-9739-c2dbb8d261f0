<script setup lang="ts">
/**
 * Or27754:コンポーネント
 * GUI00653: ［課題検討］画面
 *
 * @description
 *［課題検討］画面
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { definePageMeta, ref, useScreenStore } from '#imports'
import { Or27754Const } from '~/components/custom-components/template/Or27754/Or27754.constants'
import { Or27754Logic } from '~/components/custom-components/template/Or27754/Or27754.logic'
import type { Or27754OnewayType } from '~/types/cmn/business/components/Or27754Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useCmnCom } from '~/utils/useCmnCom'

// ページメタ情報を定義
definePageMeta({
  middleware: ['auth', 'cache-handler'], // 認証とキャッシュ処理のミドルウェアを設定
  layout: 'web-main', // 使用するレイアウトを指定
})

/**************************************************
 * 変数定義
 **************************************************/

// 画面ID
const screenId = 'GUI00653'
// ルーティングパス
const routing = 'care-manager/issues-consider-kaiteiKbn'
// 画面物理名
const screenName = 'issues-consider'

// 画面状態管理用のPiniaストア
const screenStore = useScreenStore()
// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

// 子コンポーネントのユニークIDを保持する変数
const gui00653 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// Piniaの画面領域を初期化
screenStore.initialize({
  screenId, // 画面ID
  routing, // ルーティングパス
  screenName, // 画面物理名
  pageCp: { cpId: Or27754Const.CP_ID(0) }, // ページコンポーネントIDを設定
})

// OneWayBind用の初期データを定義
const or27754OnewayType = {
  officeId: '1', // 事業所ID
  officeGroupApplyId: '', // 事業者グループ適用ID
  planPeriodId: 1, // 計画期間ID
  issuesConsiderId: '', // 課題検討ID
  itemId: 0, // 項目ID
  event: '2', // イベント
  periodProcessKbn: '', // 期間処理区分
  historyProcessKbn: '', // 履歴処理区分
  userId: systemCommonsStore.getUserId ?? '', // 利用者ID
  businessOperatorId: '', // 事業者ID
  masterKbn: '', // マスタ区分
  revisionKbn: '', // 改訂区分
  cpyFlg: false, // コピーフラグ
} as Or27754OnewayType
// Piniaから最上位の画面コンポーネント情報を取得
// これにより、ブラウザバックで画面遷移した場合でもユニークコンポーネントIDが利用可能
const pageComponent = screenStore.screen().supplement.pageComponent

// コンポーネントの初期化処理を開始
Or27754Logic.initialize(pageComponent.uniqueCpId)

// 子コンポーネントのユニークIDを設定
gui00653.value.uniqueCpId = pageComponent.uniqueCpId

// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})
</script>
<template>
  <!-- 子コンポーネントg-custom-or-27754をレンダリング -->
  <g-custom-or-27754
    :="gui00653"
    :oneway-model-value="or27754OnewayType"
  ></g-custom-or-27754>
</template>
