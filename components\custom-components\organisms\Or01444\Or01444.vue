<script setup lang="ts">
/**
 * Or01444:（会議録）コンテンツエリア
 * GUI01122_会議録
 *
 * @description
 * （会議録）コンテンツエリア
 *
 * <AUTHOR> LE VAN CUONG
 */
import { useI18n } from 'vue-i18n'
import { OrX0014Const } from '../OrX0014/OrX0014.constants'
import { OrX0014Logic } from '../OrX0014/OrX0014.logic'
import type { OrX0014StateType } from '../OrX0014/OrX0014.type'
import { OrX0004Const } from '../OrX0004/OrX0004.constants'
import { OrX0004Logic } from '../OrX0004/OrX0004.logic'
import { OrX0010Const } from '../OrX0010/OrX0010.constants'
import { OrX0010Logic } from '../OrX0010/OrX0010.logic'
import { Or28824Const } from '../Or28824/Or28824.constants'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0008Const } from '../OrX0008/OrX0008.constants'
import { Or01444Const } from './Or01444.constants'
import { nextTick, onMounted, reactive, ref, watch, useScreenUtils, computed } from '#imports'
import { Or21828Const } from '~/components/base-components/organisms/Or21828/Or21828.constants'
import { Or21830Const } from '~/components/base-components/organisms/Or21830/Or21830.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or21830Logic } from '~/components/base-components/organisms/Or21830/Or21830.logic'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import type { OrHeadLineType } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.type'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or21832Logic } from '~/components/base-components/organisms/Or21832/Or21832.logic'
import { Or21832Const } from '~/components/base-components/organisms/Or21832/Or21832.constants'
import { Or21833Logic } from '~/components/base-components/organisms/Or21833/Or21833.logic'
import { Or21833Const } from '~/components/base-components/organisms/Or21833/Or21833.constants'
import type { OrX0007OnewayType, OrX0007Type } from '~/types/cmn/business/components/OrX0007Type'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import type { OrX0009Type, OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import { useScreenStore } from '~/stores/session/screen'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { useJigyoList } from '~/utils/useJigyoList'
import type { Or01444OnewayType } from '~/types/cmn/business/components/Or01444Type'
import { Or31869Const } from '~/components/custom-components/organisms/Or31869/Or31869.constants'
import type { OrX0038StateType } from '~/components/custom-components/organisms/OrX0038/OrX0038.Type'
import type { Or28824OnewayType, Or28824Type } from '~/types/cmn/business/components/Or28824Type'
import { Or52870Const } from '~/components/custom-components/organisms/Or52870/Or52870.constants'
import { Or52870Logic } from '~/components/custom-components/organisms/Or52870/Or52870.logic'
import type { Or52870OnewayType } from '~/types/cmn/business/components/Or52870Type'
import type {
  MeetingMinutesInitSelectInEntity,
  MeetingMinutesInitSelectOutEntity,
} from '~/repositories/cmn/entities/MeetingMinutesInitSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { SPACE_WAVE } from '~/constants/classification-constants'
import type {
  AttendeesHeaderInfo,
  ContentsInfo,
  HeldInfo,
  MeetingMinutesDetailPtn1UpdateInEntity,
} from '~/repositories/cmn/entities/MeetingMinutesDetailPtn1UpdateEntity'
import type { MeetingMinutesDetailPtn2UpdateInEntity } from '~/repositories/cmn/entities/MeetingMinutesDetailPtn2UpdateEntity'
import type { MeetingMinutesDetailPkgUpdateInEntity } from '~/repositories/cmn/entities/MeetingMinutesDetailPkgUpdateEntity'
import type {
  MeetingMinutesHistorySelectInEntity,
  MeetingMinutesHistorySelectOutEntity,
} from '~/repositories/cmn/entities/MeetingMinutesHistorySelectEntity'
import type {
  MeetingMinutesHistoryPkgSelectInEntity,
  MeetingMinutesHistoryPkgSelectOutEntity,
} from '~/repositories/cmn/entities/MeetingMinutesHistoryPkgSelectEntity'
import type {
  MeetingMinutesPeriodSelectInEntity,
  MeetingMinutesPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/MeetingMinutesPeriodSelectEntity'
import type { Or31869Type } from '~/types/cmn/business/components/Or31869Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { OrX0009Logic } from '~/components/custom-components/organisms/OrX0009/OrX0009.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { hasRegistAuth, hasPrintAuth } from '~/utils/useCmnAuthz'
import type { Or32377OnewayType } from '~/types/cmn/business/components/Or32377Type'
import { Or32377Logic } from '~/components/custom-components/organisms/Or32377/Or32377.logic'
import { Or32377Const } from '~/components/custom-components/organisms/Or32377/Or32377.constants'
import type { Or10444OnewayType } from '~/types/cmn/business/components/Or10444Type'
import { Or10444Logic } from '~/components/custom-components/organisms/Or10444/Or10444.logic'
import { Or10444Const } from '~/components/custom-components/organisms/Or10444/Or10444.constants'
import { Or31928Logic } from '~/components/custom-components/organisms/Or31928/Or31928.logic'
import { Or31928Const } from '~/components/custom-components/organisms/Or31928/Or31928.constants'
import type { Or31928OnewayType } from '~/types/cmn/business/components/Or31928Type'
import type { OrX0008OnewayType } from '~/types/cmn/business/components/OrX0008Type'
import { CustomClass } from '~/types/CustomClassType'

/**
 * I18n
 */
const { t } = useI18n()

/**
 * jigyoListWatch
 */
const { jigyoListWatch } = useJigyoList()

/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
  onewayModelValue: Or01444OnewayType
}
/** props */
const props = defineProps<Props>()

/**
 * or21828
 */
const or21828 = ref({ uniqueCpId: '' })
/**
 * or21830
 */
const or21830 = ref({ uniqueCpId: '' })
/**
 * or00248
 */
const or00248 = ref({ uniqueCpId: '' })
/**
 * or00249
 */
const or00249 = ref({ uniqueCpId: '' })
/**
 * orHeadLine
 */
const orHeadLine = ref({ uniqueCpId: '' })
/**
 * orX0004
 */
const orX0004 = ref({ uniqueCpId: '' })

/**
 * or21832
 */
const or21832 = ref({ uniqueCpId: '' })

/**
 * or21833
 */
const or21833 = ref({ uniqueCpId: '' })
/**
 * orX0014
 */
const orX0014 = ref({ uniqueCpId: '' })
/**
 * or41179
 */
const or41179 = ref({ uniqueCpId: '' })
/**
 * orX0007
 */
const orX0007 = ref({ uniqueCpId: '' })
/**
 * orX0009
 */
const orX0009 = ref({ uniqueCpId: '' })
/**
 * orX0010
 */
const orX0010 = ref({ uniqueCpId: '' })

/**
 * or21814
 */
const or21814 = ref({ uniqueCpId: '' })

/**
 * or31869
 */
const or31869 = ref({ uniqueCpId: '' })

/**
 * orX0038
 */
// const orX0038 = ref({ uniqueCpId: '' })

/**
 * orX0008
 */
const orX0008 = ref({ uniqueCpId: '' })
/**
 * or28824
 */
const or28824 = ref({ uniqueCpId: '' })

/**
 * or52870
 */
const or52870 = ref({ uniqueCpId: '' })

/**
 * Or32377
 */
const Or32377 = ref({ uniqueCpId: '' })

/**
 * Or10444
 */
const Or10444 = ref({ uniqueCpId: '' })

/**
 * or31928
 */
const or31928 = ref({ uniqueCpId: '' })
/**
 * 確認ダイアログのPromise
 */
let or21814ResolvePromise: (value: Or21814EventType) => void

/**
 * defaultComponents
 */
const defaultComponents = {
  orX0038: {
    createData: {
      currentIndex: 0,
      totalCount: 0,
    },
  } as OrX0038StateType,
}
/**
 * defaultOneway
 */
const defaultOneway = reactive({
  orX0038Oneway: {} as OrX0038StateType,
  orX0008Oneway: {} as OrX0008OnewayType,
})
/**
 * localComponents
 */
const localComponents = reactive({
  orX0038: {
    ...defaultComponents.orX0038,
  } as OrX0038StateType,
})
/**
 * 双方向バインド用の内部変数
 */
const local = reactive({
  or31869: {
    pattern: props.onewayModelValue.pattern,
    heldInfo: {
      kaigi1Id: '',
      sc1Id: '',
      kaigiYmd: '',
      whereKnj: '',
      timeHm: '',
      kaisuu: '',
      honninKnj: '',
      kazokuKnj: '',
      zokugaraKnj: '',
      bikoKnj: '',
      riyuKnj: '',
      kentoKnj: '',
      memoKnj: '',
      ketsuronKnj: '',
      kadaiKnj: '',
      jikaiYmd: '',
      //modifiedCnt: '',
      sankaKbn: '',
      sankaKnj: '',
      naiyoknj: '',
    } as HeldInfo,
    attendeesList: [],
    contentsInfo: {
      kaigi1Id: '',
      riyuKnj: '',
      kentoKnj: '',
      memoKnj: '',
      ketsuronKnj: '',
      kadaiKnj: '',
      jikaiYmd: '',
      //modifiedCnt: '',
      naiyoKnj: '',
      sogoHenkoKnj: '',
      keikakuHenkoKnj: '',
      kessekiTaioKnj: '',
      reasonAbsenceDisplay: '',
    } as ContentsInfo,
    attendeesHeaderInfo: {
      khn13Id: '',
      sankaKbn: '',
      sankaKnj: '',
      // modifiedCnt: '',
      kinship: '',
    } as AttendeesHeaderInfo,
    attendanceMode: [] as { value: number; label: string }[],
    isCopy: false,
    screenCategory: props.onewayModelValue.screenCategory,
    periodControlFlag: props.onewayModelValue.periodControlFlag,
    cpnFlg: props.onewayModelValue.cpnFlg,
    svJigyoId: props.onewayModelValue.svJigyoId,
    svJigyoCd: '',
    kikanFlag: '',
    sc1Id: '',
    createDate: '',
    heldDate: '',
    attendeesListResult: [],
  } as unknown as Or31869Type,
  or01444: props.onewayModelValue,
  caseNumber: {
    value: '',
  },
  heldDate: {
    value: '',
  },
  orX0007: {
    PlanTargetPeriodUpdateFlg: '',
    planTargetPeriodId: '0',
  } as OrX0007Type,
  orX0009: {} as OrX0009Type,
  orX0038: {
    createData: {
      currentIndex: 0,
      totalCount: 0,
    },
  } as OrX0038StateType,
  or28824: {} as Or28824Type,
  kikanFlag: 'false', // 期間管理フラグ
  showHistoryFlg: false,
  showAuthorFlg: false,
  showCreateDateFlg: false,
  showFormFlg: false,
  historyUpdateCategory: '',
  jigyoInfo: {},
  khn11Id: '0',
})
/**
 * 片方向バインド用の内部変数
 */
const localOneway = reactive({
  orX0007Oneway: {} as OrX0007OnewayType,
  orX0038Oneway: {
    ...defaultOneway.orX0038Oneway,
  } as OrX0038StateType,
  orX0008Oneway: { ...defaultOneway.orX0008Oneway } as OrX0008OnewayType,
  orX0009Oneway: {} as OrX0009OnewayType,
  orX0010Oneway: { isRequired: true } as OrX0010OnewayType,
  or28824Oneway: {} as Or28824OnewayType,
  mo00020OnewayCreateDate: {
    itemLabel: t('label.create-date'),
    showItemLabel: true,
    isVerticalLabel: true,
    isRequired: true,
    hideDetails: true,
    disabled: false,
    width: '135',
    customClass: new CustomClass({ labelClass: 'ml-0 mt-1 mb-1 mr-1' }),
  } as Mo00020OnewayType,
  mo00020Oneway: {
    itemLabel: t('label.held-date'),
    showItemLabel: true,
    isVerticalLabel: true,
    isRequired: true,
    hideDetails: true,
    disabled: false,
    width: '135',
  } as Mo00020OnewayType,
  mo00045Oneway: {
    itemLabel: t('label.case-number'),
    showItemLabel: true,
    hideDetails: true,
    isVerticalLabel: true,
    width: '100px',
    maxLength: '10',
  } as Mo00045OnewayType,
  mo01338Oneway: {
    value: t('label.case-number'),
    customClass: {
      outerStyle: 'margin-right: 38px',
      labelClass: 'ma-1',
      itemClass: 'ml-1 align-center',
    },
  },
})
/**
 * Or52870Data
 */
const Or52870Data: Or52870OnewayType = {
  // listData: [
  //   {
  //     id: '3',
  //     meetingPlace: {
  //       value: '第1会議室',
  //     },
  //     expressSmoothness: {
  //       value: 1,
  //     },
  //     updateType: 'U',
  //   },
  //   {
  //     id: '4',
  //     meetingPlace: {
  //       value: '第2会議室',
  //     },
  //     expressSmoothness: {
  //       value: 2,
  //     },
  //     updateType: 'U',
  //   },
  //   {
  //     id: '5',
  //     meetingPlace: {
  //       value: 'ほのぼの公民館',
  //     },
  //     expressSmoothness: {
  //       value: 3,
  //     },
  //     updateType: 'U',
  //   },
  // ],
  minuteTab: {
    svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
    shisetuId: (local.jigyoInfo as { shisetuId?: string })?.shisetuId ?? '',
    bunrui1Id: '1',
    bunrui2Id: '1',
    absentPersonAndReasonValue: '1',
  },
}

/**
 * Or32377OnewayModel
 */
const Or32377OnewayModel: Or32377OnewayType = {
  userList: [
    {
      userId: '2',
      name1Knj: '佐藤',
      name2Knj: '涼子',
      name1Kana: 'さとう',
      name2Kana: 'りょうこ',
      sex: '0',
      selfId: '0000000002',
    },
    {
      userId: '3',
      name1Knj: '鈴木',
      name2Knj: '一郎',
      name1Kana: 'すずき',
      name2Kana: 'いちろう',
      sex: '1',
      selfId: '0000000003',
    },
    {
      userId: '4',
      name1Knj: '高橋',
      name2Knj: '由紀',
      name1Kana: 'たかはし',
      name2Kana: 'ゆき',
      sex: '0',
      selfId: '0000000004',
    },
    {
      userId: '5',
      name1Knj: '田中',
      name2Knj: '健',
      name1Kana: 'たなか',
      name2Kana: 'けん',
      sex: '1',
      selfId: '0000000005',
    },
    {
      userId: '6',
      name1Knj: '伊藤',
      name2Knj: 'さくら',
      name1Kana: 'いとう',
      name2Kana: 'さくら',
      sex: '0',
      selfId: '0000000006',
    },
    {
      userId: '7',
      name1Knj: '渡辺',
      name2Knj: '正雄',
      name1Kana: 'わたなべ',
      name2Kana: 'まさお',
      sex: '1',
      selfId: '0000000007',
    },
    {
      userId: '8',
      name1Knj: '山本',
      name2Knj: '久美子',
      name1Kana: 'やまもと',
      name2Kana: 'くみこ',
      sex: '0',
      selfId: '0000000008',
    },
    {
      userId: '9',
      name1Knj: '中村',
      name2Knj: '光',
      name1Kana: 'なかむら',
      name2Kana: 'ひかる',
      sex: '2',
      selfId: '0000000009',
    },
    {
      userId: '10',
      name1Knj: '小林',
      name2Knj: '直人',
      name1Kana: 'こばやし',
      name2Kana: 'なおと',
      sex: '1',
      selfId: '0000000010',
    },
    {
      userId: '11',
      name1Knj: '加藤',
      name2Knj: 'めぐみ',
      name1Kana: 'かとう',
      name2Kana: 'めぐみ',
      sex: '0',
      selfId: '0000000011',
    },
    {
      userId: '12',
      name1Knj: '吉田',
      name2Knj: '浩二',
      name1Kana: 'よしだ',
      name2Kana: 'こうじ',
      sex: '1',
      selfId: '0000000012',
    },
    {
      userId: '13',
      name1Knj: '山崎',
      name2Knj: '真理子',
      name1Kana: 'やまざき',
      name2Kana: 'まりこ',
      sex: '0',
      selfId: '0000000013',
    },
    {
      userId: '14',
      name1Knj: '清水',
      name2Knj: '竜也',
      name1Kana: 'しみず',
      name2Kana: 'たつや',
      sex: '1',
      selfId: '0000000014',
    },
    {
      userId: '15',
      name1Knj: '斎藤',
      name2Knj: '千代',
      name1Kana: 'さいとう',
      name2Kana: 'ちよ',
      sex: '0',
      selfId: '0000000015',
    },
    {
      userId: '16',
      name1Knj: '山口',
      name2Knj: '翼',
      name1Kana: 'やまぐち',
      name2Kana: 'つばさ',
      sex: '2',
      selfId: '0000000016',
    },
    {
      userId: '17',
      name1Knj: '松本',
      name2Knj: '麻衣',
      name1Kana: 'まつもと',
      name2Kana: 'まい',
      sex: '0',
      selfId: '0000000017',
    },
    {
      userId: '18',
      name1Knj: '井上',
      name2Knj: '大樹',
      name1Kana: 'いのうえ',
      name2Kana: 'たいき',
      sex: '1',
      selfId: '0000000018',
    },
    {
      userId: '19',
      name1Knj: '木村',
      name2Knj: '桜',
      name1Kana: 'きむら',
      name2Kana: 'さくら',
      sex: '2',
      selfId: '0000000019',
    },
    {
      userId: '20',
      name1Knj: '林',
      name2Knj: '太郎',
      name1Kana: 'はやし',
      name2Kana: 'たろう',
      sex: '1',
      selfId: '0000000020',
    },
    {
      userId: '21',
      name1Knj: '森',
      name2Knj: '愛子',
      name1Kana: 'もり',
      name2Kana: 'あいこ',
      sex: '0',
      selfId: '0000000021',
    },
  ],
  kikanFlg: '0',
  userId: '31',
  sectionName: '1',
  choIndex: '1',
  historyId: '3',
  sysCd: '',
  sysRyaku: '',
  houjinId: '',
  shisetuId: '',
  svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
  shokuId: '',
  managerId: '',
  startYmd: '',
  jigyoshoId: '',
  loginNumber: '',
  loginUserType: '',
  emrLinkFlag: '',
}

/**
 * ダイアログ表示フラグ
 */
const isShowDialogOr32377 = computed(() => {
  return Or32377Logic.state.get(Or32377.value.uniqueCpId)?.isOpen ?? false
})
/**
 * showDialogOr52870
 */
const showDialogOr52870 = computed(() => {
  return Or52870Logic.state.get(or52870.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Pinia
 */
const { setChildCpBinds } = useScreenUtils()
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21828Const.CP_ID]: or21828.value,
  [Or21830Const.CP_ID]: or21830.value,
  // [OrX0038Const.CP_ID(0)]: orX0038.value,
  [OrX0008Const.CP_ID(0)]: orX0008.value,
  [Or00248Const.CP_ID(0)]: or00248.value,
  [OrX0004Const.CP_ID(0)]: orX0004.value,
  [Or21832Const.CP_ID]: or21832.value,
  [Or21833Const.CP_ID]: or21833.value,
  [OrX0014Const.CP_ID(0)]: orX0014.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [OrX0007Const.CP_ID(0)]: orX0007.value,
  [OrX0009Const.CP_ID(0)]: orX0009.value,
  [OrX0010Const.CP_ID(0)]: orX0010.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or31869Const.CP_ID(0)]: or31869.value,
  [Or28824Const.CP_ID(0)]: or28824.value,
  [Or52870Const.CP_ID(0)]: or52870.value,
  [Or32377Const.CP_ID(0)]: Or32377.value,
  [Or10444Const.CP_ID(0)]: Or10444.value,
  [Or31928Const.CP_ID(0)]: or31928.value,
})
useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(0)]: or00249.value,
})

/**
 * ダイアログ起動時実行
 */
onMounted(async () => {
  local.or01444.userId = '31' //systemCommonsStore.getUserId!
  if (systemCommonsStore.getUserSelectSelfId()) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }
  Or21833Logic.state.set({
    uniqueCpId: or21833.value.uniqueCpId,
    state: { tooltipText: t('tooltip.regular-master-icon') },
  })
  Or21832Logic.state.set({
    uniqueCpId: or21832.value.uniqueCpId,
    state: {
      tooltipText: t('tooltip.care-plan2-print-setting-btn'),
      disabled: !(await hasPrintAuth('/care-manager/metting-minutes')),
    },
  })
  Or21830Logic.state.set({
    uniqueCpId: or21830.value.uniqueCpId,
    state: {
      tooltipText: t('tooltip.care-plan2-save-btn'),
      disabled: !(await hasRegistAuth('/care-manager/metting-minutes')),
    },
  })
  OrX0014Logic.state.set({
    uniqueCpId: orX0014.value.uniqueCpId,
    state: {
      showLogFlg: false,
    } as OrX0014StateType,
  })
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or01444Const.STR_ALL })
  })
  await initCodes()
  await init()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [{ mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SET_ATTEND_MODE }]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  local.or31869.attendanceMode = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SET_ATTEND_MODE
  ).map((item) => ({
    ...item,
    value: Number(item.value),
  }))
}
/**
 * 必要なマスター データを取得して設定し、コンポーネントを初期化します。
 */
async function init() {
  const svJigyoIdList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
  if (Array.isArray(svJigyoIdList) && svJigyoIdList.length > 0) {
    if (svJigyoIdList.find((item) => item.svJigyoId === local.or01444.svJigyoId)) {
      Or41179Logic.data.set({
        uniqueCpId: or41179.value.uniqueCpId,
        value: {
          modelValue: local.or01444.svJigyoId,
        } as Mo00040Type,
      })
      const data = svJigyoIdList?.find(
        (jigyoInfo) => jigyoInfo.svJigyoId === local.or01444.svJigyoId
      )
      if (data) {
        local.jigyoInfo = data
      }
    } else {
      Or41179Logic.data.set({
        uniqueCpId: or41179.value.uniqueCpId,
        value: {
          modelValue: svJigyoIdList[0].svJigyoId,
        } as Mo00040Type,
      })
      local.jigyoInfo = svJigyoIdList[0]
    }
  }
  local.or31869.svJigyoCd = (local.jigyoInfo as { svJigyoCd?: string })?.svJigyoCd ?? ''
  local.or31869.svJigyoId = (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? ''
  const inputData: MeetingMinutesInitSelectInEntity = {
    syubetuId: '1', //systemCommonsStore.getSyubetu ?? '',
    shisetuId: '2', //(local.jigyoInfo as { shisetuId?: string })?.shisetuId ?? '',
    userId: local.or01444.userId,
    svJigyoId: '1', //(local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
    cpnFlg: '1', //local.or01444.cpnFlg,
    shosiki: '1', //local.or01444.shosiki,
  }
  await fetchInitData(inputData)
}
/**
 * 会議議事録初期化選択
 *
 * @param inputData - 入力データ 会議議事録
 */
async function fetchInitData(inputData: MeetingMinutesInitSelectInEntity) {
  const resData: MeetingMinutesInitSelectOutEntity = await ScreenRepository.select(
    'meetingMinutesInitSelect',
    inputData
  )
  if (resData) {
    local.kikanFlag = resData.data.kikanFlag
    local.or31869.kikanFlag = local.kikanFlag
    localOneway.orX0007Oneway.kindId = resData.data.syubetuId
    local.or01444.syubetsuId = resData.data.syubetuId
    await nextTick()
    if (resData.data.kikanOutD !== null && resData.data.kikanOutD !== undefined) {
      if (
        resData.data.kikanOutD.kikanInfo !== null &&
        resData.data.kikanOutD.kikanInfo.length > 0
      ) {
        const kikanInfo = resData.data.kikanOutD.kikanInfo[0]
        localOneway.orX0007Oneway.planTargetPeriodData = {
          // 計画対象期間
          planTargetPeriod:
            String(kikanInfo.startYmd ?? '') + SPACE_WAVE + String(kikanInfo.endYmd ?? ''),
          // 計画対象期間期間ID
          planTargetPeriodId: Number(kikanInfo.sc1Id),
          // 表示中の番号
          currentIndex: Number(kikanInfo.kikanIndex),
          // 登録数
          totalCount: Number(kikanInfo.kikanTotal),
        }
        local.orX0007.planTargetPeriodId = kikanInfo.sc1Id
        local.or31869.sc1Id = kikanInfo.sc1Id
        local.showHistoryFlg = true
        local.showAuthorFlg = true
        local.showCreateDateFlg = true
        local.showFormFlg = true
        OrX0007Logic.data.set({
          uniqueCpId: orX0007.value.uniqueCpId,
          value: {
            planTargetPeriodId: kikanInfo.sc1Id,
            PlanTargetPeriodUpdateFlg: '0',
          },
        })
        local.or31869.startYmd = kikanInfo.startYmd ?? ''
        local.or31869.endYmd = kikanInfo.endYmd ?? ''
      } else {
        localOneway.orX0007Oneway.planTargetPeriodData = {
          currentIndex: 0,
          totalCount: 0,
          planTargetPeriod: '',
          planTargetPeriodId: -1,
        }
        local.showHistoryFlg = false
        local.showAuthorFlg = false
        local.showCreateDateFlg = false
        local.showFormFlg = false
      }
      await nextTick()
      if (
        resData.data.kikanOutD.historyObj.historyInfo !== null &&
        resData.data.kikanOutD.historyObj.historyInfo.length > 0
      ) {
        local.showHistoryFlg = true
        local.showCreateDateFlg = true
        local.showFormFlg = true
        local.showAuthorFlg = true
        const historyInfo = resData.data.kikanOutD.historyObj.historyInfo[0]
        localComponents.orX0038.createData.currentIndex = Number(historyInfo.indexOfHistory)
        localComponents.orX0038.createData.totalCount = Number(historyInfo.numberOfHistory)
        local.khn11Id = historyInfo.khn11Id
        local.heldDate.value = historyInfo.createYmd
        local.caseNumber.value = historyInfo.caseNo
        OrX0010Logic.data.set({
          uniqueCpId: orX0010.value.uniqueCpId,
          value: {
            value: historyInfo.createYmd,
          },
          isInit: true,
        })
        localOneway.orX0009Oneway.createData = {
          createId: 0,
          createDate: '',
          staffId: Number(historyInfo.shokuId),
          staffName: historyInfo.shokuKnj,
          currentIndex: 0,
          totalCount: 0,
        }
      } else {
        local.khn11Id = '0'
        local.historyUpdateCategory = Or01444Const.UPDATE_KBN_C
        local.heldDate.value = systemCommonsStore.getSystemDate ?? ''
        local.caseNumber.value = ''
        OrX0010Logic.data.set({
          uniqueCpId: orX0010.value.uniqueCpId,
          value: {
            value: systemCommonsStore.getSystemDate ?? '',
          },
          isInit: true,
        })
        localOneway.orX0009Oneway.createData = {
          createId: 0,
          createDate: '',
          staffId: Number(systemCommonsStore.getStaffId),
          staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
          currentIndex: 0,
          totalCount: 0,
        }
        await callGetMeetingMinutesHistory({
          userId: local.or01444.userId,
          svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
          sc1Id: local.orX0007.planTargetPeriodId,
          ks51Id: local.khn11Id,
          shosiki: local.or01444.shosiki,
          historyPage: '0',
        })
        localComponents.orX0038.createData.totalCount += 1
        localComponents.orX0038.createData.currentIndex =
          localComponents.orX0038.createData.totalCount

        createEmptyScreen()
      }

      if (
        resData.data.kikanOutD.historyObj.detailObj.heldInfo !== null &&
        resData.data.kikanOutD.historyObj.detailObj.heldInfo.length > 0
      ) {
        local.or31869.heldInfo = resData.data.kikanOutD.historyObj.detailObj.heldInfo[0]
      }
      if (
        resData.data.kikanOutD.historyObj.detailObj.contentsList !== null &&
        resData.data.kikanOutD.historyObj.detailObj.contentsList.length > 0
      ) {
        local.or31869.contentsInfo = resData.data.kikanOutD.historyObj.detailObj.contentsList[0]
      }
      if (resData.data.kikanOutD.historyObj.detailObj.attendeesList !== null) {
        local.or31869.attendeesList = resData.data.kikanOutD.historyObj.detailObj.attendeesList
      }
    }
    setChildCpBinds(props.uniqueCpId, {
      [Or31869Const.CP_ID(0)]: {
        twoWayValue: {
          ...local.or31869,
          heldInfo: local.or31869.heldInfo,
          contentsInfo: local.or31869.contentsInfo,
          attendeesList: local.or31869.attendeesList,
          kikanFlag: local.kikanFlag,
          createDate: local.heldDate.value,
          heldDate: local.heldDate.value,
          attendeesListResult: [],
        },
      },
    })
  }
}
/**
 * refValue
 */
const { refValue } = useScreenTwoWayBind<Or31869Type>({
  cpId: Or31869Const.CP_ID(0),
  uniqueCpId: or31869.value.uniqueCpId,
})
/**
 * 議事録パターン1の更新
 */
async function updateMettingMinutesPattern1() {
  const heldInfo = [] as HeldInfo[]
  if (refValue.value?.heldInfo) {
    heldInfo.push(refValue.value?.heldInfo)
  }
  const param: MeetingMinutesDetailPtn1UpdateInEntity = {
    historyUpdateKbn: local.historyUpdateCategory,
    /**
     * 会議録ID
     */
    kaigi1Id: local.khn11Id,
    /**
     * 計画対象期間ID
     */
    sc1Id: local.orX0007.planTargetPeriodId,
    /**
     * 法人ID
     */
    houjinId: (local.jigyoInfo as { houjinId?: string })?.houjinId ?? '',
    /**
     * 施設ID
     */
    shisetuId: (local.jigyoInfo as { shisetuId?: string })?.shisetuId ?? '',
    /**
     * 事業者ID
     */
    svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
    /**
     * 利用者ID
     */
    userId: local.or01444.userId,
    /**
     * 種別ID
     */
    syubetsuId: local.or01444.syubetsuId,

    /**
     * 作成日
     */
    createYmd: OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? '',
    /**
     * 作成者（職員ID）
     */
    shokuId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '0',
    /**
     * 開催情報
     */
    heldInfo: heldInfo,
    /**
     * 出席者リスト
     */
    attendeesList: refValue.value?.attendeesListResult ?? [],
  }

  await ScreenRepository.update('meetingMinutesDetailPtn1Update', param)
}
/**
 * 議事録パターン2の更新
 */
async function updateMettingMinutesPattern2() {
  const heldInfo = [] as HeldInfo[]
  if (refValue.value?.heldInfo) {
    heldInfo.push(refValue.value?.heldInfo)
  }
  const param: MeetingMinutesDetailPtn2UpdateInEntity = {
    historyUpdateKbn: local.historyUpdateCategory,
    /**
     * 会議録ID
     */
    kaigi1Id: local.khn11Id,
    /**
     * 計画対象期間ID
     */
    sc1Id: local.orX0007.planTargetPeriodId,
    /**
     * 法人ID
     */
    houjinId: (local.jigyoInfo as { houjinId?: string })?.houjinId ?? '',
    /**
     * 施設ID
     */
    shisetuId: (local.jigyoInfo as { shisetuId?: string })?.shisetuId ?? '',
    /**
     * 事業者ID
     */
    svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
    /**
     * 利用者ID
     */
    userId: local.or01444.userId,
    /**
     * 種別ID
     */
    syubetsuId: local.or01444.syubetsuId,

    /**
     * 作成日
     */
    createYmd: OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? '',
    /**
     * 作成者（職員ID）
     */
    shokuId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '0',
    /**
     * 開催情報
     */
    heldInfo: heldInfo,
    /**
     * 出席者リスト
     */
    attendeesList: refValue.value?.attendeesListResult ?? [],
  }
  await ScreenRepository.update('meetingMinutesDetailPtn2Update', param)
}
/**
 * 議事録パターン3の更新
 */
async function updateMettingMinutesPattern3() {
  const heldInfo = [] as HeldInfo[]
  if (refValue.value?.heldInfo) {
    heldInfo.push(refValue.value?.heldInfo)
  }
  const param: MeetingMinutesDetailPkgUpdateInEntity = {
    historyUpdateKbn: local.historyUpdateCategory,
    /**
     * 会議録ID
     */
    kaigi1Id: local.khn11Id,
    /**
     * 計画対象期間ID
     */
    sc1Id: local.orX0007.planTargetPeriodId,
    /**
     * 法人ID
     */
    houjinId: (local.jigyoInfo as { houjinId?: string })?.houjinId ?? '',
    /**
     * 施設ID
     */
    shisetuId: (local.jigyoInfo as { shisetuId?: string })?.shisetuId ?? '',
    /**
     * 事業者ID
     */
    svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
    /**
     * 利用者ID
     */
    userId: local.or01444.userId,
    /**
     * 種別ID
     */
    syubetsuId: local.or01444.syubetsuId,

    /**
     * 作成日
     */
    createYmd: local.heldDate.value,
    /**
     * ケース番号
     */
    caseNo: local.caseNumber.value,
    /**
     * 作成者（職員ID）
     */
    shokuId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '0',
    /**
     * 開催情報
     */
    heldInfo: heldInfo,
    /**
     * 出席者リスト
     */
    attendeesList: refValue.value?.attendeesListResult ?? [],
  }
  await ScreenRepository.update('meetingMinutesDetailPkgUpdate', param)
}

/**
 * 会議議事録の履歴を取得する
 */
async function callGetMeetingMinutesHistory(data: {
  userId: string
  svJigyoId: string
  sc1Id: string
  ks51Id: string
  shosiki: string
  historyPage: string
}) {
  if (local.or01444.pattern === Or01444Const.PATTERN_ID_3) {
    const inputData: MeetingMinutesHistoryPkgSelectInEntity = {
      /**
       * 利用者ID
       */
      userId: data.userId,
      /**
       * 事業者ID
       */
      svJigyoId: '1', //data.svJigyoId,
      /**
       * 計画対象期間ID
       */
      sc1Id: data.sc1Id,
      /**
       * 会議録ID
       */
      ks51Id: data.ks51Id,
      /**
       * 会議録書式
       */
      shosiki: '1', //data.shosiki,
      /**
       * 履歴ページ区分
       */
      historyPage: data.historyPage,
    }

    await getMeetingMinutesHistoryPkg(inputData)
  } else {
    const inputData: MeetingMinutesHistorySelectInEntity = {
      /**
       * 利用者ID
       */
      userId: data.userId,
      /**
       * 事業者ID
       */
      svJigyoId: '1', //data.svJigyoId,
      /**
       * 計画対象期間ID
       */
      sc1Id: data.sc1Id,
      /**
       * 会議録ID
       */
      ks51Id: data.ks51Id,
      /**
       * 会議録書式
       */
      shosiki: '2', //data.shosiki,
      /**
       * 履歴ページ区分
       */
      historyPage: data.historyPage,
    }
    await getMeetingMinutesHistory(inputData)
  }
}

/**
 * 会議議事録の履歴を取得する
 *
 * @param inputData - 入力データ 議事録 履歴 選択
 */
async function getMeetingMinutesHistory(inputData: MeetingMinutesHistorySelectInEntity) {
  const resData: MeetingMinutesHistorySelectOutEntity = await ScreenRepository.select(
    'meetingMinutesHistorySelect',
    inputData
  )
  if (resData) {
    if (resData.data.historyInfo !== null && resData.data.historyInfo.length > 0) {
      const historyInfo = resData.data.historyInfo[0]
      localComponents.orX0038.createData.currentIndex = Number(historyInfo.indexOfHistory)
      localComponents.orX0038.createData.totalCount = Number(historyInfo.numberOfHistory)
      local.khn11Id = historyInfo.khn11Id
    } else {
      local.khn11Id = '0'
      localComponents.orX0038.createData.currentIndex = 0
      localComponents.orX0038.createData.totalCount = 0
    }
    if (resData.data.detailObj.heldInfo !== null && resData.data.detailObj.heldInfo.length > 0) {
      local.or31869.heldInfo = resData.data.detailObj.heldInfo[0]
    }
    if (
      resData.data.detailObj.contentsList !== null &&
      resData.data.detailObj.contentsList.length > 0
    ) {
      local.or31869.contentsInfo = resData.data.detailObj.contentsList[0]
    }
    if (resData.data.detailObj.attendeesList !== null) {
      local.or31869.attendeesList = resData.data.detailObj.attendeesList
    }
    setChildCpBinds(props.uniqueCpId, {
      [Or31869Const.CP_ID(0)]: {
        twoWayValue: {
          ...local.or31869,
          heldInfo: local.or31869.heldInfo,
          contentsInfo: local.or31869.contentsInfo,
          attendeesList: local.or31869.attendeesList,
          attendeesListResult: [],
        },
      },
    })
  }
}

/**
 * 会議録履歴取得(新型養護)
 *
 * @param inputData - 入力データ
 */
async function getMeetingMinutesHistoryPkg(inputData: MeetingMinutesHistoryPkgSelectInEntity) {
  const resData: MeetingMinutesHistoryPkgSelectOutEntity = await ScreenRepository.select(
    'meetingMinutesHistoryPkgSelect',
    inputData
  )
  if (resData) {
    if (resData.data.historyInfo !== null && resData.data.historyInfo.length > 0) {
      const historyInfo = resData.data.historyInfo[0]
      localComponents.orX0038.createData.currentIndex = Number(historyInfo.indexOfHistory)
      localComponents.orX0038.createData.totalCount = Number(historyInfo.numberOfHistory)
      local.khn11Id = historyInfo.khn11Id
    } else {
      local.khn11Id = '0'
      localComponents.orX0038.createData.currentIndex = 0
      localComponents.orX0038.createData.totalCount = 0
    }
    if (resData.data.detailObj.heldInfo !== null && resData.data.detailObj.heldInfo.length > 0) {
      local.or31869.heldInfo = resData.data.detailObj.heldInfo[0]
    }
    if (
      resData.data.detailObj.contentsList !== null &&
      resData.data.detailObj.contentsList.length > 0
    ) {
      local.or31869.contentsInfo = resData.data.detailObj.contentsList[0]
    }
    if (resData.data.detailObj.attendeesList !== null) {
      local.or31869.attendeesList = resData.data.detailObj.attendeesList
    }
    if (
      resData.data.detailObj.attendeesHeaderInfo !== null &&
      resData.data.detailObj.attendeesHeaderInfo.length > 0
    ) {
      local.or31869.attendeesHeaderInfo = resData.data.detailObj.attendeesHeaderInfo[0]
    }
    setChildCpBinds(props.uniqueCpId, {
      [Or31869Const.CP_ID(0)]: {
        twoWayValue: {
          ...local.or31869,
          heldInfo: local.or31869.heldInfo,
          contentsInfo: local.or31869.contentsInfo,
          attendeesList: local.or31869.attendeesList,
          attendeesHeaderInfo: local.or31869.attendeesHeaderInfo,
          attendeesListResult: [],
        },
      },
    })
  }
}

/**
 * 会議録対象期間変更
 *
 * @param inputData - 入力データ
 */
async function getMettingMinutesPeriod(inputData: MeetingMinutesPeriodSelectInEntity) {
  const resData: MeetingMinutesPeriodSelectOutEntity = await ScreenRepository.select(
    'meetingMinutesPeriodSelect',
    inputData
  )
  if (resData) {
    await nextTick()
    if (resData.data.kikanInfo !== null && resData.data.kikanInfo.length > 0) {
      const kikanInfo = resData.data.kikanInfo[0]
      localOneway.orX0007Oneway.planTargetPeriodData = {
        // 計画対象期間
        planTargetPeriod:
          String(kikanInfo.startYmd ?? '') + SPACE_WAVE + String(kikanInfo.endYmd ?? ''),
        // 計画対象期間期間ID
        planTargetPeriodId: Number(kikanInfo.sc1Id),
        // 表示中の番号
        currentIndex: Number(kikanInfo.kikanIndex),
        // 登録数
        totalCount: Number(kikanInfo.kikanTotal),
      }
      local.orX0007.planTargetPeriodId = kikanInfo.sc1Id
      local.or31869.sc1Id = kikanInfo.sc1Id
      local.or31869.startYmd = kikanInfo.startYmd
      local.or31869.endYmd = kikanInfo.endYmd
      OrX0007Logic.data.set({
        uniqueCpId: orX0007.value.uniqueCpId,
        value: {
          planTargetPeriodId: kikanInfo.sc1Id,
          PlanTargetPeriodUpdateFlg: '0',
        },
      })
    }
    if (
      resData.data.historyObj.historyInfo !== null &&
      resData.data.historyObj.historyInfo.length > 0
    ) {
      const historyInfo = resData.data.historyObj.historyInfo[0]
      localComponents.orX0038.createData.currentIndex = Number(historyInfo.indexOfHistory)
      localComponents.orX0038.createData.totalCount = Number(historyInfo.numberOfHistory)
      local.khn11Id = historyInfo.khn11Id
    } else {
      local.khn11Id = '0'
      localComponents.orX0038.createData.currentIndex = 1
      localComponents.orX0038.createData.totalCount = 1
      await callGetMeetingMinutesHistory({
        userId: local.or01444.userId,
        svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
        sc1Id: local.orX0007.planTargetPeriodId,
        ks51Id: local.khn11Id,
        shosiki: local.or01444.shosiki,
        historyPage: '0',
      })
    }
    if (
      resData.data.historyObj.detailObj.heldInfo !== null &&
      resData.data.historyObj.detailObj.heldInfo.length > 0
    ) {
      local.or31869.heldInfo = resData.data.historyObj.detailObj
        .heldInfo[0] as typeof local.or31869.heldInfo
    }
    if (
      resData.data.historyObj.detailObj.contentsList !== null &&
      resData.data.historyObj.detailObj.contentsList.length > 0
    ) {
      local.or31869.contentsInfo = resData.data.historyObj.detailObj
        .contentsList[0] as typeof local.or31869.contentsInfo
    }
    if (resData.data.historyObj.detailObj.attendeesList !== null) {
      local.or31869.attendeesList = resData.data.historyObj.detailObj
        .attendeesList as typeof local.or31869.attendeesList
    }
    setChildCpBinds(props.uniqueCpId, {
      [Or31869Const.CP_ID(0)]: {
        twoWayValue: {
          ...local.or31869,
          heldInfo: local.or31869.heldInfo,
          contentsInfo: local.or31869.contentsInfo,
          attendeesList: local.or31869.attendeesList,
          attendeesListResult: [],
        },
      },
    })
  }
}
/**
 * 変更されたリスニングのコンポーネントIDリスト
 */
const watchedComponents = ref<string[]>([
  props.uniqueCpId,
  or31869.value.uniqueCpId,
  orX0009.value.uniqueCpId,
  orX0010.value.uniqueCpId,
])
/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
function isEditNavControl(components: string[]) {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()
  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }

  return false
}

/**
 * 新規空白画面を作成
 */
function createEmptyScreen() {
  local.or31869 = {
    ...local.or31869,
    heldInfo: {
      kaigi1Id: '',
      sc1Id: '',
      kaigiYmd: '',
      whereKnj: '',
      timeHm: '',
      kaisuu: '',
      honninKnj: '',
      kazokuKnj: '',
      zokugaraKnj: '',
      bikoKnj: '',
      riyuKnj: '',
      kentoKnj: '',
      memoKnj: '',
      ketsuronKnj: '',
      kadaiKnj: '',
      jikaiYmd: '',
      // modifiedCnt: '',
      sankaKbn: '',
      sankaKnj: '',
      naiyoknj: '',
    } as HeldInfo,
    attendeesList: [],
    contentsInfo: {
      kaigi1Id: '',
      riyuKnj: '',
      kentoKnj: '',
      memoKnj: '',
      ketsuronKnj: '',
      kadaiKnj: '',
      jikaiYmd: '',
      // modifiedCnt: '',
      naiyoKnj: '',
      sogoHenkoKnj: '',
      keikakuHenkoKnj: '',
      kessekiTaioKnj: '',
      reasonAbsenceDisplay: '',
    } as ContentsInfo,
    attendeesHeaderInfo: {
      khn13Id: '',
      sankaKbn: '',
      sankaKnj: '',
      // modifiedCnt: '',
      kinship: '',
    } as AttendeesHeaderInfo,
    attendanceMode: [] as { value: number; label: string }[],
    attendeesListResult: [],
  } as Or31869Type
}

/**
 * 保存ボタン押下
 * 変更されている項目がないため、保存を行うことは出来ません。「改行」項目を入力変更してから、再度保存を行ってください。
 *
 * @param errormsg - Message
 */
function showOr21814MsgOneBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      thirdBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
/**
 * 確認メッセージダイアログ開く(i-cmn-11265)
 */
async function showMessageICmn11265() {
  await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11265', [t('label.meeting-minutes')]),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'blank',
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  } as Or21814OnewayType)
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11300)
 */
async function showMessageICmn11300() {
  await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11300'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'blank',
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  } as Or21814OnewayType)
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11262)
 */
async function showMessageICmn11262() {
  await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11262'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'blank',
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  } as Or21814OnewayType)
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11263)
 */
async function showMessageICmn11263() {
  await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11263'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'blank',
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  } as Or21814OnewayType)
}

const isShowDialogOr10444 = computed(() => {
  return Or10444Logic.state.get(Or10444.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 確認ダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - 確認ダイアログOneWayBind領域
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    or21814ResolvePromise = resolve
  })
}

/**
 * 確認メッセージダイアログ開く(i-cmn-10430)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn10430() {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'destroy1',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'normal3',
    // 第3ボタンラベル
    thirdBtnLabel: t('btn.cancel'),
  })
  return rs
}

/**
 * 確認メッセージダイアログ開く(i-cmn-10430)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn11326() {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11326', [
      OrX0010Logic.data.get(orX0010.value.uniqueCpId)?.value ?? '',
      t('label.meeting-minutes'),
    ]),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'destroy1',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  })
  return rs
}

/**
 * 「保存ボタン」押下
 *
 * @param reInit - 初期表示フラグ
 */
async function onClickSave(reInit?: boolean) {
  const isEdit = isEditNavControl(watchedComponents.value)
  if (local.historyUpdateCategory === Or01444Const.UPDATE_KBN_D) {
    switch (local.or01444.pattern) {
      case 1:
        await updateMettingMinutesPattern1()
        break
      case 2:
        await updateMettingMinutesPattern2()
        break
      case 3:
        await updateMettingMinutesPattern3()
        break
    }
    local.historyUpdateCategory = ''
    await init()
  } else {
    if (!isEdit) {
      showOr21814MsgOneBtn(t('message.i-cmn-21800'))
      return
    } else {
      local.historyUpdateCategory = Or01444Const.UPDATE_KBN_U
      switch (local.or01444.pattern) {
        case 1:
          await updateMettingMinutesPattern1()
          break
        case 2:
          await updateMettingMinutesPattern2()
          break
        case 3:
          await updateMettingMinutesPattern3()
          break
      }
      if (reInit) {
        await init()
      }
    }
  }
}

/**
 * 「新規ボタン」押下
 */
async function onClickCreate() {
  if (localOneway.orX0007Oneway.planTargetPeriodData.currentIndex === 0) {
    await showMessageICmn11300()
    return
  } else {
    const isEdit = isEditNavControl(watchedComponents.value)
    if (isEdit) {
      if (local.historyUpdateCategory === Or01444Const.UPDATE_KBN_C) {
        await showMessageICmn11265()
        return
      } else {
        const rs = await showMessageICmn10430()
        if (rs.firstBtnClickFlg) {
          await onClickSave()
        } else if (rs.thirdBtnClickFlg || rs.closeBtnClickFlg) {
          return
        }
      }
    }
  }
  local.historyUpdateCategory = Or01444Const.UPDATE_KBN_C
  local.heldDate.value = systemCommonsStore.getSystemDate ?? ''
  local.caseNumber.value = ''
  OrX0010Logic.data.set({
    uniqueCpId: orX0010.value.uniqueCpId,
    value: {
      value: systemCommonsStore.getSystemDate ?? '',
    },
    isInit: true,
  })
  localOneway.orX0009Oneway.createData = {
    createId: 0,
    createDate: '',
    staffId: Number(systemCommonsStore.getStaffId),
    staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
    currentIndex: 0,
    totalCount: 0,
  }
  await callGetMeetingMinutesHistory({
    userId: local.or01444.userId,
    svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
    sc1Id: local.orX0007.planTargetPeriodId,
    ks51Id: local.khn11Id,
    shosiki: local.or01444.shosiki,
    historyPage: '0',
  })
  localComponents.orX0038.createData.totalCount += 1
  localComponents.orX0038.createData.currentIndex = localComponents.orX0038.createData.totalCount
  createEmptyScreen()
}

// ダイアログ表示フラグ
const showDialogOr31928 = computed(() => {
  // Or31928のダイアログ開閉状態
  return Or31928Logic.state.get(or31928.value.uniqueCpId)?.isOpen ?? false
})

// Or31928
const or31928OnewayType: Or31928OnewayType = {
  svJigyoIdList: [],
  svJigyoId: '',
  houjinId: '',
  cpnFlg: '5',
  userId: '',
  shosikiFlag: '',
  kikanFlag: '',
  syubetsuID: '',
  shisetuID: '',
}
/**
 * 「複写ボタン」押下
 */
function onClickCpy() {
  // GUI01124_会議録複写画面をポップアップで起動する。
  or31928OnewayType.kikanFlag = '1'
  or31928OnewayType.cpnFlg = '1'
  // Or31928のダイアログ開閉状態を更新する
  Or31928Logic.state.set({
    uniqueCpId: or31928.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「オプションア」＞「削除」押下
 */
async function onOrX0014Delete() {
  const rs = await showMessageICmn11326()
  if (rs.firstBtnClickFlg) {
    local.historyUpdateCategory = Or01444Const.UPDATE_KBN_D
  } else if (rs.secondBtnClickFlg) {
    return
  }
}

/**
 * 「オプションア」＞「ログ」押下
 */
function onOrX0014Log() {
  console.log('onOrX0014Log')
}
// 「保存ボタン」押下
watch(
  () => Or21830Logic.event.get(or21830.value.uniqueCpId),
  (newVal) => {
    if (newVal?.clickEventFlg) {
      void onClickSave(true)
    }
  }
)
// 新規イベント監視
watch(
  () => OrX0004Logic.event.get(orX0004.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.createEventFlg) {
      // 「新規ボタン」押下
      if (local.historyUpdateCategory !== Or01444Const.UPDATE_KBN_D) {
        await onClickCreate()
      }
    } else if (newValue?.copyEventFlg) {
      // 「複写ボタン」押下
      if (local.historyUpdateCategory !== Or01444Const.UPDATE_KBN_D) {
        onClickCpy()
      }
    }
  }
)

/**
 * Or21832(印刷ボタン)押下時の処理
 *
 * @description
 * 印刷画面を表示する
 */
watch(
  () => Or21832Logic.event.get(or21832.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.clickEventFlg) {
      const isEdit = isEditNavControl(watchedComponents.value)
      if (isEdit) {
        const rs = await showMessageICmn10430()
        if (rs.firstBtnClickFlg) {
          await onClickSave()
        } else if (rs.thirdBtnClickFlg || rs.closeBtnClickFlg) {
          return
        }
      }
      if (local.historyUpdateCategory !== Or01444Const.UPDATE_KBN_D) {
        if (local.or01444.pattern === Or01444Const.PATTERN_ID_3) {
          // GUI01132 印刷設定画面をポップアップで起動する。
          Or32377OnewayModel.kikanFlg = local.kikanFlag
          Or32377Logic.state.set({
            uniqueCpId: Or32377.value.uniqueCpId,
            state: { isOpen: true },
          })
        } else {
          // GUI01131 印刷設定画面をポップアップで起動する。
          Or10444Logic.state.set({
            uniqueCpId: Or10444.value.uniqueCpId,
            state: { isOpen: true },
          })
        }
      }

      Or21832Logic.event.set({
        uniqueCpId: or21832.value.uniqueCpId,
        events: { clickEventFlg: false },
      })
    }
  }
)

/**
 * Or21833(マスタ他ボタン)押下時の処理
 *
 * @description
 * その他の機能画面を表示する
 */
watch(
  () => Or21833Logic.event.get(or21833.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.clickEventFlg) {
      const isEdit = isEditNavControl(watchedComponents.value)
      if (isEdit) {
        const rs = await showMessageICmn10430()
        if (rs.firstBtnClickFlg) {
          await onClickSave()
        } else if (rs.thirdBtnClickFlg || rs.closeBtnClickFlg) {
          return
        }
      }
      if (local.or01444.cpnFlg === '5') {
        // GUI01120 会議場所マスタ画面をポップアップで起動する。
        Or52870Logic.state.set({
          uniqueCpId: or52870.value.uniqueCpId,
          state: { isOpen: true },
        })
      } else {
        // GUI01121 会議録マスタ画面をポップアップで起動する。
        Or52870Logic.state.set({
          uniqueCpId: or52870.value.uniqueCpId,
          state: { isOpen: true },
        })
      }

      Or21833Logic.event.set({
        uniqueCpId: or21833.value.uniqueCpId,
        events: { clickEventFlg: false },
      })
    }
  }
)

// 「削除」押下イベント監視
watch(
  () => OrX0014Logic.event.get(orX0014.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.deleteEventFlg) {
      // 「削除」押下
      if (local.historyUpdateCategory !== Or01444Const.UPDATE_KBN_D) {
        await onOrX0014Delete()
      }
    } else if (newValue?.logEventFlg) {
      // 「ログ」押下
      onOrX0014Log()
    }
  }
)
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21814ResolvePromise !== undefined && newValue !== undefined) {
      or21814ResolvePromise(newValue)
    }
    return
  }
)

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, (newJigyoId: string) => {
  void callbackFuncJigyo(newJigyoId)
})
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function callbackFuncJigyo(newJigyoId: string) {
  const old = (local.jigyoInfo as { svJigyoCd?: string })?.svJigyoCd ?? ''
  if (newJigyoId !== '' && newJigyoId !== old) {
    const isEdit = isEditNavControl(watchedComponents.value)
    if (isEdit) {
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        await onClickSave()
      } else if (rs.closeBtnClickFlg) {
        return
      } else if (rs.thirdBtnClickFlg) {
        Or41179Logic.data.set({
          uniqueCpId: or41179.value.uniqueCpId,
          value: {
            modelValue: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
          } as Mo00040Type,
        })
        return
      }
    }
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const data = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)
    if (data) {
      local.jigyoInfo = data
    }

    local.or31869.svJigyoCd = (local.jigyoInfo as { svJigyoCd?: string })?.svJigyoCd ?? ''
    local.or31869.svJigyoId = (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? ''
    const inputData: MeetingMinutesInitSelectInEntity = {
      syubetuId: '1', //systemCommonsStore.getSyubetu ?? '',
      shisetuId: '2', //(local.jigyoInfo as { shisetuId?: string })?.shisetuId ?? '',
      userId: local.or01444.userId,
      svJigyoId: '1', //(local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
      cpnFlg: '1', //local.or01444.cpnFlg,
      shosiki: '1', //local.or01444.shosiki,
    }
    await fetchInitData(inputData)
  }
}
// 「履歴選択」の監視
// watch(
//   () => OrX0038Logic.event.get(orX0038.value.uniqueCpId),
//   async (newValue) => {
//     if (local.historyUpdateCategory !== Or01444Const.UPDATE_KBN_D) {
//       if (newValue?.iconBtnClickFlg) {
//         // 「履歴選択選択アイコンボタン」押下
//         await onClickHistIconBtn()
//       } else if (newValue?.prePageClickFlg) {
//         // 「履歴-前へアイコンボタン」押下
//         await onClickPreHistBtn()
//       } else if (newValue?.nextPageClickFlg) {
//         // 「履歴-次へアイコンボタン」押下
//         await onClickNextHistBtn()
//       }
//     }
//   }
// )

/**
 * 「履歴選択選択アイコンボタン」押下
 *
 * @param skipEditFlg - 画面入力データ変更判定スキップフラグ
 */
// async function onClickHistIconBtn(skipEditFlg?: boolean) {
//   // 画面入力データに変更がある場合
//   const isEdit = isEditNavControl(watchedComponents.value)
//   if (isEdit && (skipEditFlg === undefined || skipEditFlg === false)) {
//     // AC004-2と同じ
//     const rs = await showMessageICmn10430()
//     if (rs.firstBtnClickFlg) {
//       await onClickSave()
//     } else if (rs.thirdBtnClickFlg || rs.closeBtnClickFlg) {
//       return
//     }
//   }
//   // GUI01011 履歴選択画面をポップアップで起動する。
//   // 入力パラメータ: 共通情報.事業所ID, 画面.計画対象期間ID, 共通情報.利用者ID, 画面.計画書ID
//   localOneway.or28824Oneway.svJigyoId = Number(
//     (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '0'
//   )
//   localOneway.or28824Oneway.sc1Id = local.orX0007.planTargetPeriodId
//   localOneway.or28824Oneway.userId = local.or01444.userId
//   localOneway.or28824Oneway.plan1Id = local.khn11Id
//   Or28824Logic.state.set({
//     uniqueCpId: or28824.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }

/**
 * 「履歴-前へアイコンボタン」押下
 */
// async function onClickPreHistBtn() {
//   // 1件目の履歴データが表示されている状態
//   if (localComponents.orX0038.createData.currentIndex === 1) {
//     // 処理終了にする。
//     return
//   }
//   const isEdit = isEditNavControl(watchedComponents.value)
//   // 画面入力データに変更がある場合
//   if (isEdit) {
//     const rs = await showMessageICmn10430()
//     if (rs.firstBtnClickFlg) {
//       await onClickSave()
//     } else if (rs.thirdBtnClickFlg || rs.closeBtnClickFlg) {
//       return
//     }
//   }
//   await callGetMeetingMinutesHistory({
//     userId: local.or01444.userId,
//     svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
//     sc1Id: local.orX0007.planTargetPeriodId,
//     ks51Id: local.khn11Id,
//     shosiki: local.or01444.shosiki,
//     historyPage: '1',
//   })
// }
/**
 * 「履歴-次へアイコンボタン」押下
 */
// async function onClickNextHistBtn() {
//   // 最終件目の履歴データが表示されている状態
//   if (
//     localComponents.orX0038.createData.currentIndex ===
//     localComponents.orX0038.createData.totalCount
//   ) {
//     // 処理終了にする。
//     return
//   }
//   const isEdit = isEditNavControl(watchedComponents.value)
//   // 画面入力データに変更がある場合
//   if (isEdit) {
//     // AC004-2と同じ
//     const rs = await showMessageICmn10430()
//     if (rs.firstBtnClickFlg) {
//       await onClickSave()
//     } else if (rs.thirdBtnClickFlg || rs.closeBtnClickFlg) {
//       return
//     }
//   }
//   await callGetMeetingMinutesHistory({
//     userId: local.or01444.userId,
//     svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
//     sc1Id: local.orX0007.planTargetPeriodId,
//     ks51Id: local.khn11Id,
//     shosiki: local.or01444.shosiki,
//     historyPage: '2',
//   })
// }

// 履歴ID選択変更の監視
watch(
  () => local.or28824,
  async (newValue) => {
    await callGetMeetingMinutesHistory({
      userId: local.or01444.userId,
      svJigyoId: (local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
      sc1Id: local.orX0007.planTargetPeriodId,
      ks51Id: newValue.plan1Id,
      shosiki: local.or01444.shosiki,
      historyPage: '0',
    })
  }
)

watch(
  () => Or52870Logic.event.get(or52870.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.closeFlg) {
      Or52870Logic.event.set({
        uniqueCpId: or52870.value.uniqueCpId,
        events: { closeFlg: false },
      })
    }
    if (newValue.comfirmFlg) {
      Or52870Logic.event.set({
        uniqueCpId: or52870.value.uniqueCpId,
        events: { comfirmFlg: false },
      })
    }
  }
)
watch([() => local.orX0007.planTargetPeriodId, () => local.kikanFlag], () => {
  local.or31869.sc1Id = local.orX0007.planTargetPeriodId
  local.or31869.kikanFlag = local.kikanFlag
})

watch(
  () => local.orX0007,
  async (newValue) => {
    if (local.historyUpdateCategory !== Or01444Const.UPDATE_KBN_D) {
      if (newValue.PlanTargetPeriodUpdateFlg === '1') {
        await onClickPrePlanPeriodBtn()
      } else if (newValue.PlanTargetPeriodUpdateFlg === '2') {
        await onClickNextPlanPeriodBtn()
      } else if (newValue.PlanTargetPeriodUpdateFlg === '0') {
        await onClickPlanPeriodIconBtn()
      }
    }
  }
)

/**
 * 会議議事録を取得する期間を呼び出す
 *
 * @param kikanPage - 計画期間ページ区分
 */
async function callGetMeetingMinutesPeriod(kikanPage: string) {
  const inputData: MeetingMinutesPeriodSelectInEntity = {
    /**
     * 事業者ID
     */
    svJigyoId: '1', //(local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
    /**
     * 施設ID
     */
    shisetuId: '2', //(local.jigyoInfo as { shisetuId?: string })?.shisetuId ?? '',
    /**
     * 利用者ID
     */
    userId: local.or01444.userId,
    /**
     * 種別ID
     */
    syubetsuId: '1', //local.or01444.syubetsuId,
    /**
     * 計画対象期間ID
     */
    sc1Id: local.orX0007.planTargetPeriodId,
    /**
     * 計画期間ページ区分
     */
    kikanPage: kikanPage,
    /**
     * 期間管理フラグ
     */
    kikanFlag: 'true',
    /**
     * アセスメント方式
     */
    cpnFlg: '1', // local.or01444.cpnFlg,
    /**
     * 会議録書式
     */
    shosiki: '1', //local.or01444.shosiki,
  }
  await getMettingMinutesPeriod(inputData)
}
/**
 * プラン期間をクリック
 */
async function onClickPlanPeriodIconBtn() {
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      await onClickSave()
    } else if (rs.thirdBtnClickFlg || rs.closeBtnClickFlg) {
      return
    }
  }
  await callGetMeetingMinutesPeriod('0')
}
/**
 * 事前計画期間をクリック
 */
async function onClickPrePlanPeriodBtn() {
  if (localOneway.orX0007Oneway.planTargetPeriodData.currentIndex === 1) {
    await showMessageICmn11262()
    return
  }
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      await onClickSave()
    } else if (rs.thirdBtnClickFlg || rs.closeBtnClickFlg) {
      return
    }
  }
  localOneway.orX0007Oneway.planTargetPeriodData.currentIndex -= 1
  await callGetMeetingMinutesPeriod('1')
}

/**
 * 次の計画期間をクリック
 */
async function onClickNextPlanPeriodBtn() {
  if (
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex >=
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount
  ) {
    await showMessageICmn11263()

    return
  }
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      await onClickSave()
    } else if (rs.thirdBtnClickFlg || rs.closeBtnClickFlg) {
      return
    }
  }
  localOneway.orX0007Oneway.planTargetPeriodData.currentIndex += 1
  await callGetMeetingMinutesPeriod('2')
}

/**
 * ウォッチャー
 */
watch(
  () => local.historyUpdateCategory,
  (newValue) => {
    switch (newValue) {
      case Or01444Const.UPDATE_KBN_D: {
        local.or31869.isCopy = true
        localOneway.mo00045Oneway.disabled = true
        localOneway.mo00020Oneway.disabled = true
        OrX0010Logic.state.set({
          uniqueCpId: orX0010.value.uniqueCpId,
          state: {
            isDisabled: true,
          },
        })
        OrX0009Logic.state.set({
          uniqueCpId: orX0009.value.uniqueCpId,
          state: {
            isDisabled: true,
          },
        })
        break
      }
      default: {
        local.or31869.isCopy = false
        localOneway.mo00045Oneway.disabled = false
        localOneway.mo00020Oneway.disabled = false
        OrX0010Logic.state.set({
          uniqueCpId: orX0010.value.uniqueCpId,
          state: {
            isDisabled: false,
          },
        })
        OrX0009Logic.state.set({
          uniqueCpId: orX0009.value.uniqueCpId,
          state: {
            isDisabled: false,
          },
        })
        break
      }
    }
  }
)

/**
 * 利用者選択
 */
watch(
  () => systemCommonsStore.getUserSelectSelfId(),
  async (oldValue, newValue) => {
    const userSelectSelfId = newValue
    if (userSelectSelfId !== null && userSelectSelfId !== undefined) {
      const isEdit = isEditNavControl(watchedComponents.value)
      if (isEdit) {
        const rs = await showMessageICmn10430()
        if (rs.firstBtnClickFlg) {
          await onClickSave()
        } else if (rs.closeBtnClickFlg) {
          return
        } else if (rs.thirdBtnClickFlg) {
          return
        }
      }
      Or41179Logic.state.set({
        uniqueCpId: or41179.value.uniqueCpId,
        state: {
          searchCriteria: {
            selfId: systemCommonsStore.getUserSelectSelfId(),
          },
        },
      })
      local.or01444.userId = userSelectSelfId
      const inputData: MeetingMinutesInitSelectInEntity = {
        syubetuId: '1', //systemCommonsStore.getSyubetu ?? '',
        shisetuId: '20', // (local.jigyoInfo as { shisetuId?: string })?.shisetuId ?? '',
        userId: local.or01444.userId,
        svJigyoId: '1', //(local.jigyoInfo as { svJigyoId?: string })?.svJigyoId ?? '',
        cpnFlg: '1', //local.or01444.cpnFlg,
        shosiki: '1', //local.or01444.shosiki,
      }
      await fetchInitData(inputData)
    }
  }
)

const Or10444OnewayModel: Or10444OnewayType = {
  kikanFlg: '1',
  userId: '2',
  sectionName: '1',
  choIndex: '1',
  historyId: '3',
  careManagerInChargeSettingsFlag: 2,
  svJigyoId: '',
  svJigyoIdList: [],
  tantoShokuId: '',
  kinounameKnj: '',
  kojinhogoFlg: '',
  sectionAddNo: '',
  local: '',
  kaigiFlg: '',
  shosikiFlg: '',
  gamenKbn: '',
  mode: '',
  kessekiRiyuu: '',
  attendance1List: [],
  attendance2List: [],
  startYmd: '',
  endYmd: '',
  sectionNo: '',
  prtNo: '',
  sysCd: '',
  sysRyaku: '',
  shokuId: ''
}
</script>

<template>
  <c-v-sheet class="common-layout">
    <c-v-row
      no-gutters
      class="pa-2 sticky-header"
    >
      <c-v-col class="title-area">
        <c-v-row
          align-center
          justify-start
          no-gutters
        >
          <h1 class="pl-3">
            {{ t('label.meeting-minutes') }}
          </h1>
          <g-base-or21828
            v-bind="or21828"
            class="px-2"
          />
        </c-v-row>
      </c-v-col>
      <c-v-col cols="auto">
        <c-v-row
          no-gutters
          class="align-center"
        >
          <c-v-col
            cols="auto"
            class="pr-2"
          >
            <div>
              <g-base-or21830 v-bind="or21830" />
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.care-plan2-save-btn')"
              ></c-v-tooltip>
            </div>
          </c-v-col>
          <c-v-col cols="auto">
            <g-custom-or-x-0004 v-bind="orX0004"></g-custom-or-x-0004>
          </c-v-col>
          <c-v-divider
            vertical
            class="mx-2"
            inset
          />
          <c-v-col
            cols="auto"
            class="ml-2"
          >
            <g-base-or-21832 v-bind="or21832" />
          </c-v-col>
          <c-v-col
            cols="auto"
            class="ml-2"
          >
            <g-base-or-21833 v-bind="or21833" />
          </c-v-col>
          <c-v-col cols="auto">
            <g-custom-or-x-0014 v-bind="orX0014"></g-custom-or-x-0014>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col
        cols="2"
        class="hidden-scroll h-100"
      >
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <c-v-col class="hidden-scroll h-100 ml-6">
        <c-v-sheet class="content">
          <c-v-container class="pl-0 pr-0 mr-0 ml-0">
            <c-v-row no-gutters>
              <c-v-col cols="auto">
                <g-base-or-41179
                  v-bind="or41179"
                  class="jigyo mr-6"
                />
              </c-v-col>
              <c-v-col
                v-if="local.kikanFlag === 'true'"
                cols="auto"
                class="mr-6"
              >
                <g-custom-orX0007
                  v-bind="orX0007"
                  v-model="local.orX0007"
                  :oneway-model-value="localOneway.orX0007Oneway"
                  :unique-cp-id="orX0007.uniqueCpId"
                />
              </c-v-col>
              <c-v-col
                v-show="local.showCreateDateFlg"
                cols="auto"
                class="mr-6 custom-field"
              >
                <!-- <c-v-row no-gutters>
                  <g-custom-orX0010
                    v-bind="orX0010"
                    :oneway-model-value="localOneway.orX0010Oneway"
                    :unique-cp-id="orX0010.uniqueCpId"
                  />
                </c-v-row> -->
                <c-v-row no-gutters>
                  <base-mo00020
                    v-model="local.heldDate"
                    :oneway-model-value="localOneway.mo00020OnewayCreateDate"
                  />
                </c-v-row>
              </c-v-col>
              <c-v-col
                v-if="local.showAuthorFlg"
                cols="auto"
                class="mr-6"
              >
                <g-custom-orX0009
                  v-bind="orX0009"
                  :oneway-model-value="localOneway.orX0009Oneway"
                />
              </c-v-col>
              <c-v-col
                v-if="local.showHistoryFlg"
                cols="auto"
                class="mr-6"
              >
                <!-- <g-custom-orX0038
                  v-bind="orX0038"
                  v-model="localComponents.orX0038"
                  :oneway-model-value="localOneway.orX0038Oneway"
                /> -->
                <g-custom-orX0008
                  v-bind="orX0008"
                  :oneway-model-value="localOneway.orX0008Oneway"
                  :unique-cp-id="orX0008.uniqueCpId"
                />
              </c-v-col>
              <c-v-col
                v-if="local.or01444.pattern === 3"
                cols="auto"
                class="mr-6 custom-field"
              >
                <base-mo00045
                  v-model="local.caseNumber"
                  :oneway-model-value="localOneway.mo00045Oneway"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row class="mt-0">
              <c-v-col class="pt-0 pr-0">
                <g-custom-or-31869
                  v-if="local.showFormFlg"
                  v-model="local.or31869"
                  :unique-cp-id="or31869.uniqueCpId"
                  :parent-unique-cp-id="props.uniqueCpId"
                />
              </c-v-col>
            </c-v-row>
          </c-v-container>
        </c-v-sheet>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <g-base-or21814
    v-bind="or21814"
    :unique-cp-id="or21814.uniqueCpId"
  />
  <g-custom-or-28824
    v-bind="or28824"
    v-model="local.or28824"
    :oneway-model-value="localOneway.or28824Oneway"
    :unique-cp-id="or28824.uniqueCpId"
  />
  <g-custom-or-52870
    v-if="showDialogOr52870"
    v-bind="or52870"
    :oneway-model-value="Or52870Data"
    :unique-cp-id="or52870.uniqueCpId"
  />
  <g-custom-or-32377
    v-if="isShowDialogOr32377"
    v-bind="Or32377"
    :oneway-model-value="Or32377OnewayModel"
    :unique-cp-id="Or32377.uniqueCpId"
  />
  <g-custom-or-10444
    v-if="isShowDialogOr10444"
    v-bind="Or10444"
    :oneway-model-value="Or10444OnewayModel"
    :unique-cp-id="Or10444.uniqueCpId"
  />
  <g-custom-or-31928
    v-if="showDialogOr31928"
    v-bind="or31928"
    :oneway-model-value="or31928OnewayType"
    :unique-cp-id="or31928.uniqueCpId"
  />
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';
$margin-common: 48px; // 一般的なマージン
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}
.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}
.content {
  overflow-x: auto;
}
.section {
  border: solid thin rgb(var(--v-theme-light), 0) !important;
}
:deep(.section-content) {
  padding: 0 !important;
}

:deep(.v-container) {
  max-width: 1338px;
}

:deep(.hidden-scroll) {
  overflow: visible !important;
}

:deep(.v-sheet) {
  background-color: transparent !important;
  height: 100%;
}
:deep(.v-textarea .v-field__input) {
  background-color: white !important;
  mask-image: none !important;
  -webkit-mask-image: none !important;
}
:deep(.v-field__overlay) {
  background-color: white !important;
}
:deep(.v-table__wrapper table thead tr th) {
  font-size: 14px;
  font-weight: bold;
  padding-left: 12px;
}
:deep(.v-table__wrapper table tbody tr td) {
  font-size: 14px;
}

:deep(.jigyo) {
  flex-direction: column;
  width: 203px !important;
  // position: relative;
  .ma-2 {
    margin-top: 2px !important;
    margin-bottom: 2px !important;
    margin-left: 0px !important;
  }
  .v-field.v-field--appended {
    background-color: #fff;
  }
  .v-input {
      width: 203px !important;
    }
}

.custom-field {
  margin-top: -4px;
}

.common-layout {
  // 背景色：テーマの背景色を使用
  background-color: rgb(var(--v-theme-background)) !important;
  // 幅：内容で広く
  min-height: max-content !important;
  display: flex;
  flex-direction: column;
  // 画面トップの固定ヘッダー
  .sticky-header {
    position: sticky; // スクロール時に固定
    top: $margin-common; // トップからの距離
    z-index: 999; // 他の要素よりも上に表示
    background: rgb(var(--v-theme-background)); // テーマの背景色
    width: 100%; // 全幅
  }
}

</style>
