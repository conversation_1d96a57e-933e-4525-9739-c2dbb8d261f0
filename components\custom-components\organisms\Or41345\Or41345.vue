<script setup lang="ts">
/**
 * Or41345:提供マスタモーダル
 * GUI00832_提供マスタ
 *
 * @description
 * 提供マスタモーダル
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */

/**************************************************
 * インポート
 **************************************************/
import { onMounted, reactive, ref, watch, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or50242Const } from '../Or50242/Or50242.constants'
import { Or51785Const } from '../Or51785/Or51785.constants'
import { Or41345Const } from './Or41345.constants'
import type { Or41345StateType } from './Or41345.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or41345OnewayType } from '~/types/cmn/business/components/Or41345Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { useScreenOneWayBind, useSetupChildProps, useScreenStore } from '#imports'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Or51785Type } from '~/types/cmn/business/components/Or51785Type'
import type {
  ICheckAuthzKinouInEntity,
  ICheckAuthzKinouOutEntity,
} from '~/repositories/business/core/authz/entities/CheckAuthzKinouEntity'
import { AuthzRepository } from '~/repositories/business/core/authz/AuthzRepository'
import { Or10472Const } from '~/components/custom-components/organisms/Or10472/Or10472.constants'
import type { Or10472OnewayType } from '~/types/cmn/business/components/Or10472Type'
import type { Or10895OnewayType } from '~/types/cmn/business/components/Or10895Type'
import { Or51786Const } from '~/components/custom-components/organisms/Or51786/Or51786.constants'
import { Or51795Const } from '~/components/custom-components/organisms/Or51795/Or51795.constants'
import { Or10895Const } from '~/components/custom-components/organisms/Or10895/Or10895.constants'
import { Or27825Const } from '~/components/custom-components/organisms/Or27825/Or27825.constants'
import { Or25627Const } from '~/components/custom-components/organisms/Or25627/Or25627.constants'
import { Or51794Const } from '~/components/custom-components/organisms/Or51794/Or51794.constants'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or41345OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}
/**
 * Propsの定義
 */
const props = defineProps<Props>()

/**************************************************
 * 定数(Constants
 **************************************************/
/**
 * i18n翻訳関数
 */
const { t } = useI18n()
/**
 * 閉じるフラグ
 */
let isClose = ''
/**
 * 新規タブ制御用
 */
const isNewTab = { newTabFlg: false, newTab: '' }
/**
 * タブ切替時の一時フラグ
 */
const noFlg = ref(false)
/**
 * 51786
 */
const or51786 = ref({ uniqueCpId: '' })

/**************************************************
 * Ref・Reactive・Data構造体 (重要: useSetupChildPropsより前に定義)
 **************************************************/
/**
 * デフォルトのonewayモデル値
 */
const defaultOnewayModelValue: Or41345OnewayType = {
  sysRyaku: '',
  kbnFlg: '',
  viewAuthority: 'T',
  saveAuthority: 'T',
  dispTabFlg: 'family',
}

/**
 * 権限チェック入力値
 */
const input: ICheckAuthzKinouInEntity = {
  keys: [{ path: '/components/custom-components/organisms/Or41345/Or41345' }],
}

/**
 * 権限チェック結果
 */
const res: ICheckAuthzKinouOutEntity = await AuthzRepository.checkAuthzKinou(input)

/**
 * 保存権限の有無を示すフラグ
 */
const isPermissionSave = ref<boolean>(
  Array.isArray(res.data?.authzKinou)
    ? res.data.authzKinou
        .filter((authz) => authz.path === '/components/custom-components/organisms/Or41345/Or41345')
        .every((authz) => authz.use9)
    : false
)
/**
 * Or50242用ref
 */
const or50242 = ref({ uniqueCpId: '' })
/**
 * Or10472用ref
 */
const or10472 = ref({ uniqueCpId: '' })
/**
 *ユニークID管理用のリアクティブ参照
 */
const or51794 = ref({ uniqueCpId: '' })
/**
 *ユニークID管理用のリアクティブ参照
 */
const or51795 = ref({ uniqueCpId: '' })
/**
 *ユニークID管理用のリアクティブ参照
 */
const or10895 = ref({ uniqueCpId: '' })
/**
 *ユニークID管理用のリアクティブ参照
 */
const or27825 = ref({ uniqueCpId: '' })

/**
 * Or51785用ref
 */
const or51785 = ref({ uniqueCpId: '' })
/**
 * or25627用ref
 */
const or25627 = ref({ uniqueCpId: '' })
/**
 * Or21814用ref
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * Or21813用ref
 */
const or21813 = ref({ uniqueCpId: '' })
/**
 * タブ項目リスト
 */

const tabItemsTab = ref([
  {
    id: Or41345Const.TAB.TAB_ID_CARE_CHECK,
    title: t('label.care-check'),
    tooltipText: t('label.care-check'),
  },
  {
    id: Or41345Const.TAB.TAB_ID_OFFER,
    title: t('label.offer'),
    tooltipText: t('label.offer'),
  },
  {
    id: Or41345Const.TAB.TAB_ID_FAMILY,
    title: t('label.family'),
    tooltipText: t('label.family'),
  },
  {
    id: Or41345Const.TAB.TAB_ID_SCHEDULE,
    title: t('label.schedule'),
    tooltipText: t('label.schedule'),
  },
  {
    id: Or41345Const.TAB.TAB_ID_CARE_OFFER_LOCATION,
    title: t('label.care-offer-location'),
    tooltipText: t('label.care-offer-location'),
  },
])
/**
 * onewayバインド用ローカルデータ
 */
const localOneway = reactive({
  or41345: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  or27825: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  or10472: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as Or10472OnewayType,
  or51795: {
    parentUniqueCpId: '',
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as Or10472OnewayType,
  or10895: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as Or10895OnewayType,
  mo00024Oneway: {
    width: '721px',
    height: '680px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or41345',
      toolbarTitle: computed(() => {
        switch (local.mo00043.id) {
          case Or41345Const.TAB.TAB_ID_CARE_CHECK:
            return t('label.care-check-master-select-title')
          case Or41345Const.TAB.TAB_ID_FAMILY:
            return t('label.family-master-title')
          case Or41345Const.TAB.TAB_ID_OFFER:
            return t('label.service-provide-master')
          case Or41345Const.TAB.TAB_ID_SCHEDULE:
            return t('label.schedule-master-title')
          case Or41345Const.TAB.TAB_ID_CARE_OFFER_LOCATION:
            return t('label.care-location-master')
          default:
            return ''
        }
      }),
      toolbarName: 'Or41345ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as unknown as Mo00024OnewayType,
  mo00043OneWay: {
    tabItems: tabItemsTab.value,
  } as Mo00043OnewayType,
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  mo00609OneWay: {
    btnLabel: t('btn.save'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.display-row-save'),
    disabled: false,
  } as Mo00609OnewayType,
})
/**
 * ローカルデータ
 */
const local = reactive({
  mo00043: { id: '' } as Mo00043Type,
  or51785: {
    editFlg: false,
    delBtnDisabled: false,
    stringInputAssistList: [],
    saveResultstringInputAssistList: [],
  } as Or51785Type,
})
/**
 * モーダル用ref
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or41345Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/
/**
 * ピニアストア関連
 */
const { setState } = useScreenOneWayBind<Or41345StateType>({
  cpId: Or41345Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 画面状態更新時のコールバック
     *
     * @param value - 画面状態
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or41345Const.DEFAULT.IS_OPEN
    },
  },
})
// 画面状態管理用操作変数
const screenStore = useScreenStore()
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent
/**************************************************
 * useSetupChildProps
 **************************************************/
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or51786Const.CP_ID(0)]: or51786.value,
  [Or10472Const.CP_ID(0)]: or10472.value,
  [Or50242Const.CP_ID(0)]: or50242.value,
  [Or51785Const.CP_ID(0)]: or51785.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or51795Const.CP_ID(0)]: or51795.value,
  [Or10895Const.CP_ID(0)]: or10895.value,
  [Or27825Const.CP_ID(0)]: or27825.value,
  [Or25627Const.CP_ID(0)]: or25627.value,
  [Or51794Const.CP_ID(0)]: or51794.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * Or51786コンポーネントref
 */
const or27825Ref = ref<{ save: () => Promise<boolean>; isValidate: () => boolean } | null>(null)
const or51786Ref = ref<{ save: () => Promise<boolean>; isValidate: () => boolean } | null>(null)
const or10472Ref = ref<{ save: () => Promise<boolean> } | null>(null)
const or51795Ref = ref<{ save: () => Promise<boolean> } | null>(null)
const or10895Ref = ref<{ save: () => Promise<boolean> } | null>(null)
/**
 * ダイアログ表示フラグ
 */
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
/**
 *ダイアログ表示フラグ
 */
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ショートカット用コメント
 */
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
/**************************************************
 * ウォッチャー(Watchers)
 **************************************************/
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    mo00024.value.isOpen = true
    if (!newValue) {
      void close()
    }
  }
)
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (!isPermissionSave.value) {
      if ('true' === isClose) {
        if (newValue.secondBtnClickFlg) {
          setState({ isOpen: false })
        } else {
          return
        }
      } else {
        if (newValue.secondBtnClickFlg) {
          noFlg.value = true
          local.mo00043.id = isNewTab.newTab
        }
      }
    } else {
      if ('true' === isClose) {
        if (newValue.firstBtnClickFlg) {
          const res = await handleSaveTab()
          if (res) {
            setState({ isOpen: false })
          }
        } else if (newValue.secondBtnClickFlg) {
          setState({ isOpen: false })
        } else {
          return
        }
      } else {
        if (newValue.firstBtnClickFlg) {
          await handleSaveTab()
          return
        } else if (newValue.secondBtnClickFlg) {
          noFlg.value = true
          local.mo00043 = { id: oldTab }
        } else {
          return
        }
      }
    }
    Or21814Logic.event.set({
      uniqueCpId: or21814.value.uniqueCpId,
      events: {
        secondBtnClickFlg: false,
        firstBtnClickFlg: false,
      },
    })
  }
)
/**
 * 現在のタブIDを保持
 */
let oldTab = Or41345Const.TAB.TAB_ID_CARE_CHECK
let newtab = Or41345Const.TAB.TAB_ID_CARE_CHECK
/**
 * タブ切り替え処理
 *
 * @param value - 選択されたタブ情報
 */
const handleChangeTab = (value: Mo00043Type) => {
  newtab = value.id
  if (isEdit.value) {
    local.mo00043.id = oldTab
    if (!isPermissionSave.value) {
      showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
    } else {
      showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
    }
  } else {
    local.mo00043 = { id: value.id }
    oldTab = value.id
  }
}

async function handleSaveTab() {
  await nextTick()
  let res = false
  switch (local.mo00043.id) {
    case Or41345Const.TAB.TAB_ID_OFFER:
      if (!or51786Ref.value?.isValidate()) {
        return
      }
      res = (await or51786Ref.value?.save()) ?? false
      if (res) {
        local.mo00043 = { id: newtab }
        oldTab = newtab
      } else {
        local.mo00043 = { id: oldTab }
      }
      break
    case Or41345Const.TAB.TAB_ID_FAMILY:
      res = (await or10472Ref.value?.save()) ?? false
      if (res) {
        local.mo00043 = { id: newtab }
        oldTab = newtab
      } else {
        local.mo00043 = { id: oldTab }
      }
      break
    case Or41345Const.TAB.TAB_ID_CARE_OFFER_LOCATION:
      res = (await or51795Ref.value?.save()) ?? false
      if (res) {
        local.mo00043 = { id: newtab }
        oldTab = newtab
      } else {
        local.mo00043 = { id: oldTab }
      }
      break
    case Or41345Const.TAB.TAB_ID_CARE_CHECK:
      if (!or27825Ref.value?.isValidate()) {
        return
      }
      res = (await or27825Ref.value?.save()) ?? false
      if (res) {
        local.mo00043 = { id: newtab }
        oldTab = newtab
      } else {
        local.mo00043 = { id: oldTab }
      }
      break
    case Or41345Const.TAB.TAB_ID_SCHEDULE:
      res = (await or10895Ref.value?.save()) ?? false
      if (res) {
        local.mo00043 = { id: newtab }
        oldTab = newtab
      } else {
        local.mo00043 = { id: oldTab }
      }
      break
  }
  return res
}
/**************************************************
 * ライフサイクルフック(Lifecycle Hooks)
 **************************************************/
onMounted(async () => {
  if (!localOneway.or41345.isNull) {
    await init()
  }
  local.mo00043.id = ''
  setTimeout(() => {
    local.mo00043.id = oldTab
  })
})

/**************************************************
 * メソッド(Methods)
 **************************************************/
/**
 * 予定マスタ初期処理
 */
async function init() {
  // 登録権限がない場合、
  if (!isPermissionSave.value) {
    // 保存ボタンを非活性とする
    localOneway.mo00609OneWay.disabled = true
  } else {
    localOneway.mo00609OneWay.disabled = false
  }
  await nextTick()
  // setTimeout(() => {
  oldTab = local.mo00043.id = props.onewayModelValue?.tabId ?? ''
  // }, 20)
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  isClose = 'true'
  if (!isEdit.value) {
    // 画面を閉じる。
    setState({ isOpen: false })
  } else {
    if (!isPermissionSave.value) {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
      showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
    } else {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
      showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
    }
  }
}

/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgThreeBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
    style="padding: 8px !important"
  >
    <template #cardItem>
      <c-v-row>
        <c-v-col class="table-wrapper col-padding">
          <base-mo00043
            :model-value="local.mo00043"
            :oneway-model-value="localOneway.mo00043OneWay"
            @update:model-value="handleChangeTab"
          ></base-mo00043>
          <!-- @update:model-value="handleChangeTab" -->
          <!-- タブ switch -->
          <c-v-window v-model="local.mo00043.id">
            <c-v-window-item value="careCheck">
              <!-- タブ：ケアチェック -->
              <g-custom-or-27825
                v-if="local.mo00043.id === Or41345Const.TAB.TAB_ID_CARE_CHECK"
                ref="or27825Ref"
                v-bind="or27825"
                :oneway-model-value="localOneway.or27825"
              />
            </c-v-window-item>
            <c-v-window-item value="offer">
              <!-- タブ：提供 -->
              <g-custom-or-51786
                v-if="local.mo00043.id === Or41345Const.TAB.TAB_ID_OFFER"
                ref="or51786Ref"
                v-bind="or51786"
                :oneway-model-value="localOneway.or41345"
              />
            </c-v-window-item>
            <c-v-window-item value="family">
              <g-custom-or-10472
                v-if="local.mo00043.id === Or41345Const.TAB.TAB_ID_FAMILY"
                ref="or10472Ref"
                v-bind="or10472"
                :oneway-model-value="localOneway.or10472"
              />
            </c-v-window-item>
            <c-v-window-item value="schedule">
              <!-- タブ：予定 -->
              <g-custom-or-10895
                v-if="local.mo00043.id === Or41345Const.TAB.TAB_ID_SCHEDULE"
                ref="or10895Ref"
                v-bind="or10895"
                :oneway-model-value="localOneway.or10895"
              />
            </c-v-window-item>
            <c-v-window-item value="careOfferLocation">
              <!-- タブ：ケアの提供場所 -->
              <g-custom-or-51795
                v-if="local.mo00043.id === Or41345Const.TAB.TAB_ID_CARE_OFFER_LOCATION"
                ref="or51795Ref"
                v-bind="or51795"
                :oneway-model-value="localOneway.or51795"
              />
            </c-v-window-item>
          </c-v-window>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <!-- 予定コンテンツエリア-フッターアクションバー -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00611OneWay.tooltipText"
            :text="localOneway.mo00611OneWay.tooltipText"
            :location="localOneway.mo00611OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00611>
        <!-- 保存ボタン -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="handleSaveTab"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00609OneWay.tooltipText"
            :text="localOneway.mo00609OneWay.tooltipText"
            :location="localOneway.mo00609OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
$table-row-height: 32px;

.col-padding {
  padding: 8px !important;
}

.v-row {
  margin: -8px;
}

.content-head {
  margin-bottom: 8px;
}

:deep(.title-container) {
  margin: 0 !important;
}
</style>
