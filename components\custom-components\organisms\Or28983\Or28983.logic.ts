import { Or28983Const } from './Or28983.constants'
import type { Or28983StateType } from './Or28983.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or28983:有機体:事業所検索
 * GUI01177__事業所検索
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or28983Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or28983Const.CP_ID(0),
      uniqueCpId,
      childCps: [],
    })
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or28983StateType>(Or28983Const.CP_ID(0))
}
