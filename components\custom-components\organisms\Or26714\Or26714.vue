<script setup lang="ts">
/**
 * Or26714:（週間計画表（詳細データ））週間計画入力情報一覧
 * GUI00983_週間表入力
 *
 * <AUTHOR> HOANG SY TOAN
 */

import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isEmpty } from 'lodash'
import { Or50259Const } from '../Or50259/Or50259.constants'
import { Or50077Const } from '../Or50077/Or50077.constants'
import { Or50246Const } from '../Or50246/Or50246.constants'
import { Or51042Const } from '../Or51042/Or51042.constants'
import { Or26714Const } from './Or26714.constants'
import type {
  additionInfo,
  Or26714OnewayType,
  Or26714StateType,
  selectedDataType,
  WeeklyPlanInput,
  WeeklyScheduleInput,
  ServiceFeeOfficialName,
  ServiceType,
  ServiceProvider,
  AdditionalInfoPoints,
} from './Or26714.type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { OrX0021OnewayType } from '~/types/cmn/business/components/OrX0021Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Or26273TreeItem } from '~/types/cmn/business/components/Or26273Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or26714OnewayType
  uniqueCpId: string
}

// 引継情報を取得する
const props = defineProps<Props>()

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const or50259 = ref({ uniqueCpId: '' })
const or50077 = ref({ uniqueCpId: '' })
const or50246 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const or51042 = ref({ uniqueCpId: '' })

const valid = ref(false)

const mo00024 = ref<Mo00024Type>({
  isOpen: Or26714Const.DEFAULT.IS_OPEN,
})

const or26184 = ref({
  selectedIndex: 0,
  totalLine: 0,
})

const orConfirmDeleteInputDialog = ref({
  emitType: '',
  mo00024: {
    isOpen: false,
  } as Mo00024Type,
})

const moDialogConfirm = ref({
  mo00024: {
    isOpen: false,
  } as Mo00024Type,
})

// 選択した行のindex
const selectedItemIndex = ref<number>(0)

const defaultOnewayModelValue: Or26714OnewayType = {
  newMode: '',
  week1Id: undefined,
  termid: undefined,
  weekDay: '',
  startTime: '',
  endTime: '',
  weeklyScheduleInputListSelect: [],
  weeklyContentInforListSelect: [],
  serviceTypeListSelect: [],
  servicepProviderNameListSelect: [],
  serviceFeeOfficialNameListSelect: [],
  additionalInfoPointsListSelect: [],
  additionalInforServiceNameListSelect: [],
  additionalServiceTypeCodeListSelect: [],
}

const localOneway = reactive({
  or26714: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as Or26714OnewayType,

  orX0021ErrorDialogOneWay: {
    message: t('message.e-cmn-40740'),
    btnNoDisplay: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '350px',
    } as Mo00024OnewayType,
    mo00609OnewayOk: {
      class: 'mr-1',
      name: '',
      color: 'red',
    } as Mo00609OnewayType,
  } as OrX0021OnewayType,
  // 加算情報ダイアログ
  mo00024Oneway: {
    width: '1250px',
    maxWidth: '1900px',
    height: '730px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or26714',
      toolbarTitle: t('label.week-plan-title'),
      toolbarName: 'Or26714ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.save'),
  } as Mo00609OnewayType,
  mo01265DeleteBtnOneWay: {
    btnLabel: t('btn.delete'),
  } as Mo01265OnewayType,
  mo00610NewBtnOneWay: {
    btnLabel: t('btn.new'),
  } as Mo00610OnewayType,

  mo00039OneWayCharPosition: {
    name: 'charPosition',
    items: [],
    showItemLabel: false,
    customClass: new CustomClass({ outerClass: 'my-2' }),
  } as Mo00039OnewayType,
  mo00039OneWayOmitted: {
    name: 'omitted',
    items: [],
    showItemLabel: false,
    customClass: new CustomClass({ outerClass: 'my-2' }),
  } as Mo00039OnewayType,
  mo00039OneWayTimeDisplay: {
    name: 'timeDisplay',
    items: [],
    showItemLabel: false,
    customClass: new CustomClass({ outerClass: 'my-2' }),
  } as Mo00039OnewayType,
  mo00024DeleteInputDialogOneway: {
    name: '',
    width: '300px',
    message: t('message.i-cmn-11275'),
    mo01344Oneway: {
      showToolbar: false,
      cardTitle: t('label.confirm'),
      showCardActions: true,
      name: '',
      toolbarName: '',
    },
  } as Mo00024OnewayType,
  mo00039OneWayLetterSize: {
    name: 'letterSize',
    items: [],
    showItemLabel: false,
    customClass: new CustomClass({ outerClass: 'my-2' }),
  } as Mo00039OnewayType,
  mo00045OneWayLetterSize: {
    name: 'letterSize',
    hideDetails: true,
    customClass: new CustomClass({ outerClass: 'ma-2' }),
    showItemLabel: false,
    maxLength: Or26714Const.DEFAULT.LETTER_SIZE_MAX_LENGTH,
  } as Mo00045OnewayType,
})

const local = reactive({
  dataList: [] as WeeklyPlanInput[],
  dataService: {
    serviceType: [] as ServiceType[],
    servicepProviderName: [] as ServiceProvider[],
    serviceFeeOfficialName: [] as ServiceFeeOfficialName[],
  },
  dataAddition: [] as additionInfo[],
})

//
const getDataList = (dataList?: WeeklyPlanInput[]): WeeklyPlanInput[] => {
  if (Array.isArray(dataList) && dataList.length) return [...dataList]
  if (localOneway.or26714.weekDay !== '') {
    return [
      {
        ...Or26714Const.DEFAULT.WEEKLY_PLAN,
        startHour: { value: localOneway.or26714.startTime },
        endHour: { value: localOneway.or26714.endTime },
        ['dayOfWeek' + localOneway.or26714.weekDay]: true,
        dayOfWeekOther: false,
      },
    ]
  } else {
    return [
      {
        ...Or26714Const.DEFAULT.WEEKLY_PLAN,
        dayOfWeekOther: true,
      },
    ]
  }
}

const selectedData = ref<selectedDataType>({
  weeklyData: {} as WeeklyPlanInput,
  serviceData: {
    serviceType: {} as ServiceType,
    servicepProviderName: {} as ServiceProvider,
    serviceFeeOfficialName: {} as ServiceFeeOfficialName,
  },
  additionData: {} as additionInfo,
})

const Or50246Type = ref({
  item: {
    id: selectedData.value.weeklyData?.content ?? '',
    naiyoCd: selectedData.value.weeklyData?.naiyoCd ?? '',
  },
} as Or26273TreeItem)

const valueLetterSize = ref({
  value: selectedData.value.weeklyData?.letterSize,
})

const isNewModeAndWeekOther = (isWeekOther: boolean, isFrequency: boolean) => {
  if (isFrequency) return isWeekOther

  if (localOneway.or26714.newMode === Or26714Const.DEFAULT.NEW_MODE_FLAG) {
    return true
  } else {
    return isWeekOther
  }
}

/************************************************
 * ウォッチャー
 ************************************************/
watch(
  () => valueLetterSize.value,
  (newValue) => {
    selectedData.value.weeklyData.letterSize = newValue.value
  }
)

watch(
  () => selectedData.value.weeklyData?.letterSize,
  (newValue) => {
    valueLetterSize.value.value = newValue
  }
)
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClose()
    }
  }
)

watch(
  () => selectedItemIndex.value,
  (newVal) => {
    selectedData.value.weeklyData = local.dataList[newVal]
    selectedData.value.serviceData.serviceType = local.dataService.serviceType[newVal]
    selectedData.value.serviceData.servicepProviderName =
      local.dataService.servicepProviderName[newVal]
    selectedData.value.serviceData.serviceFeeOfficialName =
      local.dataService.serviceFeeOfficialName[newVal]

    selectedData.value.additionData = {
      ...local.dataAddition[newVal],
      name: {
        value:
          localOneway.or26714.additionalInforServiceNameListSelect?.[newVal]?.formalnameKnj ?? '',
      },
      value: {
        modelValue:
          localOneway.or26714.additionalInforServiceNameListSelect?.[newVal]?.svtype ?? '',
      },
    }

    or26184.value.selectedIndex = newVal
  }
)

watch(
  () => local.dataList,
  (newVal) => {
    or26184.value.totalLine = newVal.length
  },
  { deep: true }
)

/**
 * 選択された週間計画詳細データを削除する
 */
watch(
  () => orConfirmDeleteInputDialog.value.emitType,
  () => {
    if (orConfirmDeleteInputDialog.value.emitType === 'ok') {
      local.dataList.splice(selectedItemIndex.value, 1)
      local.dataService.serviceType.splice(selectedItemIndex.value, 1)
      local.dataService.servicepProviderName.splice(selectedItemIndex.value, 1)
      local.dataService.serviceFeeOfficialName.splice(selectedItemIndex.value, 1)
      local.dataAddition.splice(selectedItemIndex.value, 1)
      if (selectedItemIndex.value > 0) {
        selectIndex(selectedItemIndex.value - 1)
      } else {
        selectedData.value.weeklyData = { ...local.dataList[selectedItemIndex.value] }
        selectedData.value.serviceData.serviceType = {
          ...local.dataService.serviceType[selectedItemIndex.value],
        }
        selectedData.value.serviceData.servicepProviderName = {
          ...local.dataService.servicepProviderName[selectedItemIndex.value],
        }
        selectedData.value.serviceData.serviceFeeOfficialName = {
          ...local.dataService.serviceFeeOfficialName[selectedItemIndex.value],
        }
        selectedData.value.additionData = { ...local.dataAddition[selectedItemIndex.value] }
      }

      if (local.dataList.length === 0) {
        selectIndex(selectedItemIndex.value - 1)
      }
      if (props.onewayModelValue.weekDay !== '') {
        setState({ isOpen: false })
        //TODO update list after close dialog
      }

      orConfirmDeleteInputDialog.value.mo00024.isOpen = false
    }
  }
)

watch(
  () => selectedData,
  () => {
    handleUpdateIntoList()
  },
  { deep: true }
)

/**
 * 検証処理
 */
function checkValidate() {
  let error1 = false
  let error2 = false
  let error3 = false
  let error4 = false
  for (const weekplan of local.dataList) {
    // 開始時が「24」、且つ、開始分が「29」より後ろの場合
    if (
      '24' === weekplan.startHour.value?.slice(0, 2) &&
      Number(weekplan.startHour.value?.slice(-2)) > 29
    ) {
      error1 = true
    }
    //開始時が「24」、且つ、終了分が「00」より後ろ、且つ、開始分が終了分以前の場合
    if (
      '24' === weekplan.startHour.value?.slice(0, 2) &&
      '00' === weekplan.endHour.value?.slice(0, 2) &&
      Number(weekplan.startHour.value?.slice(-2)) < Number(weekplan.endHour.value?.slice(-2))
    ) {
      error2 = true
    }
    // 終了時が「24」、且つ、終了分が「30」より後ろの場合
    if (
      '24' === weekplan.endHour.value?.slice(0, 2) &&
      Number(weekplan.endHour.value?.slice(-2)) > 30
    ) {
      error3 = true
    }
    // 曜日に「他」,「月」,「火」,「水」,「木」,「金」,「土」,「日」一つも選択していない場合
    if (
      weekplan.dayOfWeek1 === false &&
      weekplan.dayOfWeek2 === false &&
      weekplan.dayOfWeek3 === false &&
      weekplan.dayOfWeek4 === false &&
      weekplan.dayOfWeek5 === false &&
      weekplan.dayOfWeek6 === false &&
      weekplan.dayOfWeek7 === false &&
      weekplan.dayOfWeekOther === false
    ) {
      error4 = true
    }
  }
  if (error1) {
    showOr21813MsgOneBtn(t('message.e-cmn-41728'))
  } else if (error2) {
    showOr21813MsgOneBtn(t('message.e-cmn-41729'))
  } else if (error3) {
    showOr21813MsgOneBtn(t('message.e-cmn-41730'))
  } else if (error4) {
    showOr21813MsgOneBtn(t('message.e-cmn-40740'))
  } else {
    return true
  }
  return false
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function onClose() {
  setState({ isOpen: false })
}
/**
 * ユーザーが確認ボタンを押して親画面にデータを送信したときの処理
 */
function onConfirm() {
  if (valid.value === Or26714Const.DEFAULT.NOT_VALID) {
    // バリデーションエラー
    return
  }
  handleUpdateIntoList()
  if (!checkValidate()) return
  // 親画面へデータを送信
  emit('update:modelValue', local)

  setState({ isOpen: false })
}

/**
 * 次へアイコン
 */
function actionUp() {
  if (selectedItemIndex.value < local.dataList.length - 1) {
    handleUpdateIntoList()
    selectIndex(selectedItemIndex.value + 1)
  }
}

/**
 * 前へアイコン
 */
function actionDown() {
  if (selectedItemIndex.value > 0) {
    handleUpdateIntoList()

    selectIndex(selectedItemIndex.value - 1)
  }
}
/**
 * リストへの更新を処理する
 */
function handleUpdateIntoList() {
  local.dataList[selectedItemIndex.value] = selectedData.value.weeklyData
  local.dataService.serviceType[selectedItemIndex.value] =
    selectedData.value.serviceData.serviceType
  local.dataService.servicepProviderName[selectedItemIndex.value] =
    selectedData.value.serviceData.servicepProviderName
  local.dataService.serviceFeeOfficialName[selectedItemIndex.value] =
    selectedData.value.serviceData.serviceFeeOfficialName
  local.dataAddition[selectedItemIndex.value] = selectedData.value.additionData
}
/**
 * ハンドルクリック削除ボタン
 */
function handleClickDeleteBtn() {
  if (local.dataList.length === 0) return
  orConfirmDeleteInputDialog.value.mo00024.isOpen = true
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectIndex(index: number) {
  selectedItemIndex.value = index
}

/**
 * 新規追加ボタンのハンドラ
 */
function handleAddNew() {
  local.dataList.push({
    ...Or26714Const.DEFAULT.WEEKLY_PLAN,
    id: (local.dataList.length + 1).toString(),
  })
  local.dataService.serviceType.push({ ruakuKnj: '' })
  local.dataService.servicepProviderName.push({
    svJigyoCd: '',
    jigyoKnj: '',
    jigyoRyakuKnj: '',
  })
  local.dataService.serviceFeeOfficialName.push({
    formalnameKnj: '',
  })
  local.dataAddition.push({ name: { value: '' }, value: { modelValue: undefined } })
  selectIndex(local.dataList.length - 1)
}
/**
 *
 * チェック曜日
 *
 * @param value - 選択された値
 *
 * @param day - 曜日
 */
function checkDay(value: string, day: string) {
  if (day === Or26714Const.OTHER_WEEKLY) {
    return value.slice(-1) === Or26714Const.DEFAULT.OTHER_THAN_WEEKLY_VALUE
  }
  const valueDay = value[Number(day) - 1]
  return valueDay === Or26714Const.DEFAULT.CHECKED_VALUE
}

/**
 * すべて削除ボタンのハンドラ
 */
function handleDeleteAll() {
  selectedData.value.serviceData.serviceType = {} as ServiceType
  selectedData.value.serviceData.servicepProviderName = {} as ServiceProvider
  selectedData.value.serviceData.serviceFeeOfficialName = {} as ServiceFeeOfficialName
  selectedData.value.additionData = {} as additionInfo
  handleUpdateIntoList()
}

/**
 * 事業所削除ボタンのハンドラ
 */
function handleDeleteOperator() {
  selectedData.value.serviceData.servicepProviderName = {} as ServiceProvider
  selectedData.value.serviceData.serviceFeeOfficialName = {} as ServiceFeeOfficialName
  selectedData.value.additionData = {} as additionInfo
  handleUpdateIntoList()
}

/**
 * サービス削除ボタンのハンドラ
 */
function handleDeleteServices() {
  selectedData.value.serviceData.serviceFeeOfficialName = {} as ServiceFeeOfficialName
  handleUpdateIntoList()
}

/**
 * 初期化処理
 */
function init() {
  const {
    weeklyScheduleInputListSelect,
    serviceTypeListSelect,
    servicepProviderNameListSelect,
    serviceFeeOfficialNameListSelect,
    additionalInfoPointsListSelect,
    additionalInforServiceNameListSelect,
  } = localOneway.or26714

  const weeklyList = weeklyScheduleInputListSelect?.map((item: WeeklyScheduleInput) => {
    return {
      /**
       * データID
       */
      id: item.week2Id,
      /**
       *開始時間
       */
      startHour: { value: item.startHour },

      /**
       *終了時間
       */
      endHour: { value: item.endHour },

      /**
       *月曜日
       */
      dayOfWeek1: checkDay(item.youbi, Or26714Const.MONDAY),
      /**
       *火曜日
       */
      dayOfWeek2: checkDay(item.youbi, Or26714Const.TUESDAY),
      /**
       *水曜日
       */
      dayOfWeek3: checkDay(item.youbi, Or26714Const.WEDNESDAY),
      /**
       *木曜日
       */
      dayOfWeek4: checkDay(item.youbi, Or26714Const.THURSDAY),
      /**
       *金曜日
       */
      dayOfWeek5: checkDay(item.youbi, Or26714Const.FRIDAY),
      /**
       *土曜日
       */
      dayOfWeek6: checkDay(item.youbi, Or26714Const.SATURDAY),
      /**
       *日曜日
       */
      dayOfWeek7: checkDay(item.youbi, Or26714Const.SUNDAY),
      /**
       *週単位以外(他)
       */
      dayOfWeekOther: checkDay(item.youbi, Or26714Const.OTHER_WEEKLY),
      /** 内容CD */
      naiyoCd: item.naiyoCd,
      /**
       *内容
       */
      content: item.naiyoKnj,

      /**
       *メモ
       */
      memo: { value: item.memoKnj },

      /**
       *頻度
       *TODO
       */
      frequency: '',

      /**
       *表示設定セクション
       */
      letterSize: item.fontSize,

      /**
       *文字位置セクション
       */
      charPosition: item.alignment,

      /**
       *省略セクション
       */
      omitted: item.timeKbn,

      /**
       *文字色セクション
       */
      letterColor: item.fontColor.toString(),

      /**
       *背景色セクション
       */
      backgroundColor: item.backColor.toString(),

      /**
       *時間表示セクション
       */
      timeDisplay: item.timeKbn.toString() ?? '0',

      /**
       *週単位以外ｻｰﾋﾞｽ区分
       */
      igaiKbn: item.igaiKbn.toString(),

      /**
       *週単位以外ｻｰﾋﾞｽ（日付指定）
       */
      igaiDate: item.igaiDate.toString(),

      /**
       *週単位以外ｻｰﾋﾞｽ（日付指定）
       */
      igaiWeek: item.igaiWeek.toString(),

      /**
       *福祉用具貸与の単価
       */
      svTani: item.svTani.toString(),

      /**
       *福祉用具貸与マスタID
       */
      fygId: item.fygId.toString(),
    } as WeeklyPlanInput
  })

  local.dataList = getDataList(weeklyList)
  local.dataService.serviceType = serviceTypeListSelect ?? []
  local.dataService.servicepProviderName = servicepProviderNameListSelect ?? []
  local.dataService.serviceFeeOfficialName = serviceFeeOfficialNameListSelect ?? []
  local.dataAddition = (additionalInfoPointsListSelect ?? []).map(
    (item: AdditionalInfoPoints, index: number) => {
      return {
        name: { value: additionalInforServiceNameListSelect?.[index]?.formalnameKnj ?? '' },
        value: { modelValue: item.kaisuu.toString() },
      }
    }
  )
  selectedData.value.weeklyData = { ...local.dataList[selectedItemIndex.value] }
  selectedData.value.serviceData.serviceType = {
    ...local.dataService.serviceType[selectedItemIndex.value],
  }
  selectedData.value.serviceData.servicepProviderName = {
    ...local.dataService.servicepProviderName[selectedItemIndex.value],
  }
  selectedData.value.serviceData.serviceFeeOfficialName = {
    ...local.dataService.serviceFeeOfficialName[selectedItemIndex.value],
  }

  selectedData.value.additionData = {
    ...local.dataAddition[selectedItemIndex.value],
    name: {
      value: additionalInforServiceNameListSelect?.[selectedItemIndex.value]?.formalnameKnj ?? '',
    },
    value: {
      modelValue: additionalInforServiceNameListSelect?.[selectedItemIndex.value]?.svtype ?? '',
    },
  }

  or26184.value.totalLine = local.dataList.length
}

async function initCode() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LETTER_SIZE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LETTER_POSITION },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_OMITTED },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TIME_DISPLAY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  localOneway.mo00039OneWayLetterSize.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_LETTER_SIZE
  )
  localOneway.mo00039OneWayCharPosition.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_LETTER_POSITION
  )
  localOneway.mo00039OneWayTimeDisplay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TIME_DISPLAY
  )
  localOneway.mo00039OneWayOmitted.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_OMITTED
  )
}

useSetupChildProps(props.uniqueCpId, {
  [Or50259Const.CP_ID(0)]: or50259.value,
  [Or50077Const.CP_ID(1)]: or50077.value,
  [Or50246Const.CP_ID(1)]: or50246.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or51042Const.CP_ID(0)]: or51042.value,
})

onMounted(async () => {
  await initCode()
  init()
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or26714StateType>({
  cpId: Or26714Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or26714Const.DEFAULT.IS_OPEN
    },
  },
})

/**
 * エラーダイアログの開閉
 *
 * @param errormsg - Message
 */
function showOr21813MsgOneBtn(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}
watch(
  () => Or50246Type.value,
  (newVal) => {
    selectedData.value.weeklyData.content = newVal.id ?? ''
    selectedData.value.weeklyData.naiyoCd = newVal.naiyoCd ?? ''
  },
  { deep: true }
)

watch(
  () => [selectedData.value.weeklyData?.content, selectedData.value.weeklyData?.naiyoCd],
  ([newContent, newNaiyoCd]) => {
    Or50246Type.value.id = newContent ?? ''
    Or50246Type.value.naiyoCd = newNaiyoCd ?? ''
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-form v-model="valid">
        <c-v-row
          v-if="props.onewayModelValue.weekDay === '0'"
          no-gutters
          class="border-b-sm pb-2"
        >
          <c-v-col>
            <base-mo00610
              v-bind="localOneway.mo00610NewBtnOneWay"
              class="mr-2"
              @click="handleAddNew"
            ></base-mo00610>
            <g-custom-or-26184
              v-model="or26184"
              @up="actionUp"
              @down="actionDown"
            />
          </c-v-col>
        </c-v-row>
        <template v-if="!isEmpty(selectedData.weeklyData)">
          <c-v-row
            :key="selectedData.weeklyData.id"
            no-gutters
            class="ga-2"
          >
            <c-v-col>
              <!-- 時間帯セクション -->
              <g-custom-or-51034
                v-model:data-start="selectedData.weeklyData.startHour"
                v-model:data-end="selectedData.weeklyData.endHour"
              ></g-custom-or-51034>
              <!-- 曜日セクション -->
              <g-custom-or-51035 v-model="selectedData.weeklyData"></g-custom-or-51035>
              <!-- 内容セクション -->
              <g-custom-or-50246
                v-bind="or50246"
                v-model="Or50246Type"
              ></g-custom-or-50246>
              <!-- メモセクション -->
              <g-custom-or-50077
                v-bind="or50077"
                v-model="selectedData.weeklyData.memo"
              ></g-custom-or-50077>

              <!-- 頻度セクション -->
              <g-custom-or-50259
                v-if="isNewModeAndWeekOther(selectedData.weeklyData.dayOfWeekOther, true)"
                v-bind="or50259"
                v-model="selectedData.weeklyData.frequency"
              ></g-custom-or-50259>

              <div v-if="isNewModeAndWeekOther(!selectedData.weeklyData.dayOfWeekOther, true)">
                <!-- 表示設定セクション -->
                <c-v-row
                  no-gutters
                  class="border-sm"
                >
                  <c-v-col
                    cols="4"
                    class="bg-grey-lighten-3 pa-2 d-flex align-center ga-2 border-e-sm"
                  >
                    <base-at-label
                      :value="t('label.letter-size')"
                      font-weight="bolder"
                      class="title"
                    />
                  </c-v-col>
                  <c-v-col class="d-flex justify-space-between align-center">
                    <base-mo00039
                      v-model="selectedData.weeklyData.letterSize"
                      :oneway-model-value="localOneway.mo00039OneWayLetterSize"
                    ></base-mo00039>
                    <base-mo00045
                      v-model="valueLetterSize"
                      :oneway-model-value="localOneway.mo00045OneWayLetterSize"
                    ></base-mo00045>
                  </c-v-col>
                </c-v-row>
                <!-- 文字位置セクション -->
                <c-v-row
                  no-gutters
                  class="border-sm"
                >
                  <c-v-col
                    cols="4"
                    class="bg-grey-lighten-3 pa-2 d-flex align-center ga-2 border-e-sm"
                  >
                    <base-at-label
                      :value="t('label.char-position')"
                      font-weight="bolder"
                      class="title"
                    />
                  </c-v-col>
                  <c-v-col class="d-flex justify-space-between align-center">
                    <base-mo00039
                      v-model="selectedData.weeklyData.charPosition"
                      :oneway-model-value="localOneway.mo00039OneWayCharPosition"
                    ></base-mo00039>
                  </c-v-col>
                </c-v-row>
                <!-- 省略セクション -->
                <c-v-row
                  no-gutters
                  class="border-sm"
                >
                  <c-v-col
                    cols="4"
                    class="bg-grey-lighten-3 pa-2 d-flex align-center ga-2 border-e-sm"
                  >
                    <base-at-label
                      :value="t('label.omitted')"
                      font-weight="bolder"
                      class="title"
                    />
                  </c-v-col>
                  <c-v-col class="d-flex justify-space-between align-center">
                    <base-mo00039
                      v-model="selectedData.weeklyData.omitted"
                      :oneway-model-value="localOneway.mo00039OneWayOmitted"
                    ></base-mo00039>
                  </c-v-col>
                </c-v-row>
                <!-- 文字色セクション -->
                <c-v-row
                  no-gutters
                  class="border-sm"
                >
                  <c-v-col
                    cols="4"
                    class="bg-grey-lighten-3 pa-2 d-flex align-center ga-2 border-e-sm"
                  >
                    <base-at-label
                      :value="t('label.letter-color')"
                      font-weight="bolder"
                      class="title"
                    />
                  </c-v-col>
                  <c-v-col class="align-center d-flex ma-2">
                    <g-custom-or-26281
                      :key="selectedItemIndex"
                      v-model="selectedData.weeklyData.letterColor"
                    ></g-custom-or-26281>
                  </c-v-col>
                </c-v-row>
                <!-- 背景色セクション -->
                <c-v-row
                  no-gutters
                  class="border-sm"
                >
                  <c-v-col
                    cols="4"
                    class="bg-grey-lighten-3 pa-2 d-flex align-center ga-2 border-e-sm"
                  >
                    <base-at-label
                      :value="t('label.background-color')"
                      font-weight="bolder"
                      class="title"
                    />
                  </c-v-col>
                  <c-v-col class="align-center d-flex ma-2">
                    <g-custom-or-26281
                      :key="selectedItemIndex"
                      v-model="selectedData.weeklyData.backgroundColor"
                    ></g-custom-or-26281>
                  </c-v-col>
                </c-v-row>
              </div>
              <div v-else>
                <c-v-row
                  no-gutters
                  class="border-sm"
                  style="height: 190px"
                >
                  <c-v-col
                    cols="4"
                    class="bg-grey-lighten-3 pa-2 d-flex align-center ga-2 border-e-sm"
                  >
                  </c-v-col>
                  <c-v-col class="align-center d-flex ma-2"> </c-v-col>
                </c-v-row>
              </div>

              <!-- 時間表示セクション -->
              <c-v-row
                no-gutters
                class="border-sm"
              >
                <c-v-col
                  cols="4"
                  class="bg-grey-lighten-3 pa-2 d-flex align-center ga-2 border-e-sm"
                >
                  <base-at-label
                    :value="t('weekly-plan.label.time-display')"
                    font-weight="bolder"
                    class="title"
                  />
                </c-v-col>
                <c-v-col class="d-flex justify-space-between align-center">
                  <base-mo00039
                    v-model="selectedData.weeklyData.timeDisplay"
                    :oneway-model-value="localOneway.mo00039OneWayTimeDisplay"
                  ></base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <!-- Righ-Col -->
            <c-v-col>
              <g-custom-or-51042
                v-model="selectedData"
                :new-mode="localOneway.or26714.newMode"
                :unique-cp-id="or51042.uniqueCpId"
                @on-delete-all="handleDeleteAll()"
                @on-delete-operator="handleDeleteOperator()"
                @on-delete-services="handleDeleteServices()"
              ></g-custom-or-51042>
            </c-v-col>
          </c-v-row>
        </template>
      </c-v-form>
    </template>
    <template #cardActionLeft>
      <c-v-row no-gutters>
        <base-mo01265
          v-bind="localOneway.mo01265DeleteBtnOneWay"
          @click="handleClickDeleteBtn()"
        />
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <g-custom-or-x-0016
          @close="onClose"
          @confirm="onConfirm"
        ></g-custom-or-x-0016>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- 選択行削除確認ダイアログ -->
  <g-custom-or-x-0002
    v-model="orConfirmDeleteInputDialog"
    :oneway-model-value="localOneway.mo00024DeleteInputDialogOneway"
  >
  </g-custom-or-x-0002>

  <!-- 確認ダイアログ -->
  <g-custom-or-x-0021
    v-model="moDialogConfirm"
    :oneway-model-value="localOneway.orX0021ErrorDialogOneWay"
    class="icon-container"
  >
  </g-custom-or-x-0021>

  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813 v-bind="or21813" />
</template>

<style scoped lang="scss"></style>
