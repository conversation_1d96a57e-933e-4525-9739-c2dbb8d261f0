<!-- eslint-disable jsdoc/require-param-description -->
<script setup lang="ts">
/**
 * GUI01233_評価表マスタ
 *
 * @description
 * 評価表
 *
 * <AUTHOR> 李想
 */

import {
  definePageMeta,
  ref,
  useScreenStore,
  useSetupChildProps,
  computed,
  useInitialize,
} from '#imports'
import { Or53098Const } from '~/components/custom-components/organisms/Or53098/Or53098.constants'
import { Or53098Logic } from '~/components/custom-components/organisms/Or53098/Or53098.logic'
import type { Or53098OnewayType } from '~/types/cmn/business/components/Or53098Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

// 画面ID
const screenId = 'GUI01233'
// ルーティング
const routing = 'GUI01233/pinia'
// 画面物理名
const screenName = 'GUI01233'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or53098 = ref({ uniqueCpId: '' })

const mo00039OneWay = ref({
  name: '',
  showItemLabel: false,
  inline: false,
  items: [
    {
      label: 'GUI01233_評価表マスタ',
      value: '0',
    },
  ],
} as Mo00039OnewayType)

const mo00039Type = ref<string>('0')

const mo00046OneWayType = ref({
  showItemLabel: false,
} as Mo00046OnewayType)

const or53098Oneway = ref({
  /** 施設ID */
  shisetuId: '1',
  /** 事業者ID */
  svJigyoId: '1',
  /** 分類1 */
  bunrui1Id: '2',
  /** 分類2 */
  bunrui2Id: '29',
}as Or53098OnewayType)


const info = ref<{ value: string }>({
  value: JSON.stringify(or53098Oneway.value, null, 2),
})

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01233' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or53098.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01233',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or53098Const.CP_ID(0) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or53098Const.CP_ID(0)]: or53098.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or53098Logic.initialize(or53098.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr53098 = computed(() => {
  // Or53098のダイアログ開閉状態
  return Or53098Logic.state.get(or53098.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「確定ボタン」押下
 *
 * @param value - 結果
 */
function onConfirm(value: string) {
  console.log('onConfirm', value)
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 *  ボタン押下時の処理
 *
 */
function onClick53098() {
  Or53098Logic.state.set({
    uniqueCpId: or53098.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * ボタン押下時の処理
 */
function onClick() {
  or53098Oneway.value = JSON.parse(info.value.value) as  Or53098OnewayType
  Or53098Logic.state.set({
    uniqueCpId: or53098.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClick53098()"
        >GUI01233_評価表マスタ
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-53098
    v-if="showDialogOr53098"
    v-bind="or53098"
    :oneway-model-value="or53098Oneway"
    :unique-cp-id="or53098.uniqueCpId"
    @on-confirm="onConfirm"
  />
<c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row
    no-gutters
    class="pb-2 pl-2"
  >
    <base-mo00039
      v-model="mo00039Type"
      :oneway-model-value="mo00039OneWay"
      class="bg-transparent"
    >
    </base-mo00039>
  </c-v-row>
  <c-v-row
    no-gutters
    class="content pb-2"
  >
    <div class="w-25 h-75 pl-2">
      <div>データ</div>
      <base-mo00046
        v-model="info"
        :oneway-model-value="mo00046OneWayType"
      />
    </div>
    <div class="pl-2 pt-5">
      <v-btn @click="onClick()"> GUI01233_ 疎通起動 </v-btn>
    </div>
  </c-v-row>
</template>
<style scoped lang="scss">
.content {
  background: rgb(var(--v-theme-background));
}
.bg-transparent {
  background: transparent !important;
}
</style>

