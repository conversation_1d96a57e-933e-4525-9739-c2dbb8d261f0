<script setup lang="ts">
/**
 * Or27831: 日課計画入力モーダル
 * GUI01058_日課計画入力
 *
 * <AUTHOR> 呉李彪
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0043Const } from '../OrX0043/OrX0043.constants'
import { OrX0044Const } from '../OrX0044/OrX0044.constants'
import { Or01087Const } from '../Or01087/Or01087.constants'
import { Or01088Const } from '../Or01088/Or01088.constants'
import { Or01089Const } from '../Or01089/Or01089.constants'
import { OrX0047Const } from '../OrX0047/OrX0047.constants'
import { OrX0045Const } from '../OrX0045/OrX0045.constants'
import { OrX0046Const } from '../OrX0046/OrX0046.constants'
import { Or27831Const } from './Or27831.constants'
import type { DailyPlan, DataTableData, Or27831StateType } from './Or27831.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind, useSetupChildProps} from '#imports'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or27831Type, Or27831OnewayType ,DailyPlanInfo} from '~/types/cmn/business/components/Or27831Type'
import type { Mo01280OnewayType } from '~/types/business/components/Mo01280Type'
import type { Mo01332OnewayType, Mo01332Type } from '~/types/business/components/Mo01332Type'
import type { OrX0044Type ,OrX0044OneWayType} from '~/types/cmn/business/components/OrX0044Type'
import type { OrX0043Type } from '~/types/cmn/business/components/OrX0043Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CustomClass } from '~/types/CustomClassType'
import type { Or21814OnewayType, Or21814EventType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import type { Mo01354OnewayType } from '~/components/base-components/molecules/Mo01354/Mo01354Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or27831OnewayType
  modelValue: Or27831Type
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const orX0043 = ref({ uniqueCpId: '' })
const orX0044 = ref({ uniqueCpId: '' })
const orX0045 = ref({ uniqueCpId: '' })
const orX0046 = ref({ uniqueCpId: '' })
const orX0047 = ref({ uniqueCpId: '' })
const or01087 = ref({ uniqueCpId: '' })
const or01088 = ref({ uniqueCpId: '' })
const or01089 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const defaultOnewayModelValue: Or27831OnewayType = {
  // 期間管理フラグ
  carePlanStyle: Or27831Const.DEFAULT.PLAN_STYLE.COMMON_SERVICE,
  // パターンモード
  patternMode: false,
  // 新規モード
  newMode: false,
  index: -1,
}
const local = reactive({
  or27831: {
    ...props.modelValue,
  } as Or27831Type,
  mo00024: {
    isOpen: Or27831Const.DEFAULT.IS_OPEN,
  } as Mo00024Type,
})

const localOneway = reactive({
  or27831: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as Or27831OnewayType,
  mo00024Oneway: {
    width: '1250px',
    height: '700px',
    persistent: false,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27831',
      toolbarTitle: t('label.daily-care-plan-import'),
      toolbarName: 'Or27831ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  si016OneWay: {
    showItemLabel: false,
    items: [{ label: '', value: '1' }],
    isVertical: false,
    customClass: new CustomClass({
      itemStyle: 'color:  rgb(var(--v-theme-black-500));',
    }),
  } as Mo01332OnewayType,
  si039OneWay: {
    items: Or27831Const.DEFAULT.FONT_SIZE,
    itemTitle: 'name',
    itemValue: 'Kbn',
  },
  si040OneWay: {
    readonly: false,
    itemLabel: '',
  } as Mo01280OnewayType,
  si041OneWay: {
    items: Or27831Const.DEFAULT.POTITION,
    itemTitle: 'name',
    itemValue: 'Kbn',
  },
  si042OneWay: {
    items: Or27831Const.DEFAULT.OMISSION,
    itemTitle: 'name',
    itemValue: 'Kbn',
  },
  si047OneWay: {
    items: Or27831Const.DEFAULT.TIME_DISPLAY,
    itemTitle: 'name',
    itemValue: 'Kbn',
  },
  mo00609BtnOkOneWay: {
    btnLabel: t('btn.ok'),
  } as Mo00611OnewayType,
  orX0044OneWay: {
    startDisabled: true,
    endDisabled: true,
    showAtAnyTime: true,
  } as OrX0044OneWayType,
  mo01354Oneway: {
    headers: [],
  } as Mo01354OnewayType,
})
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or27831StateType>({
  cpId: Or27831Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or27831Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
  [OrX0043Const.CP_ID(0)]: orX0043.value,
  [OrX0044Const.CP_ID(0)]: orX0044.value,
  [OrX0045Const.CP_ID(0)]: orX0045.value,
  [OrX0046Const.CP_ID(0)]: orX0046.value,
  [OrX0047Const.CP_ID(0)]: orX0047.value,
  [Or01087Const.CP_ID(0)]: or01087.value,
  [Or01088Const.CP_ID(0)]: or01088.value,
  [Or01089Const.CP_ID(0)]: or01089.value,
})

/**************************************************
 * 変数定義
 **************************************************/
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

// テーブルヘッダ
const headers = ref<unknown[]>([])

// 日課計画パターンタイトル情報
const tableData = ref<DataTableData>({
  dataList: [],
})

const or26184 = ref({
  selectedIndex: 0,
  totalLine: 0,
})

// テーブルヘッダ
const codeTypes = ref<CodeType[]>([])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await initCodes()
  init()
})
/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日課名称
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LINKING_ITEM },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  codeTypes.value =  CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_LINKING_ITEM
  )
}
/************************************************
 * ウォッチャー
 ************************************************/
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => local.mo00024.isOpen,
  (newValue) => {
    if (!newValue) {
      onClose()
    }
  }
)

/**
 * 選択された行番号
 */
watch(
  () => selectedItemIndex.value,
  (newValue) => {
    or26184.value.selectedIndex = newValue
  }
)

/**
 * 総行数
 */
watch(
  () => tableData.value.dataList,
  (newValue) => {
    or26184.value.totalLine = newValue.length
  },
  { deep: true }
)

/**
 * 日課計画パターンタイトル情報取得
 */
function init() {
  //一覧表示区分が1:共通サービスの場合
  if (localOneway.or27831.carePlanStyle === Or27831Const.DEFAULT.PLAN_STYLE.COMMON_SERVICE) {
    headers.value = [
      {
        title: t('label.timezone'),
        key: 'startTime',
        minWidth: '320px',
        width: '320px',
        sortable: false,
      },
      {
        title: t('label.outside-frame-display'),
        key: 'outSide',
        maxWidth: '65px',
        minWidth: '65px',
        width: '65px',
        sortable: false,
        required: true,
      },
      {
        title: t('label.frequency'),
        key: 'frequency',
        minWidth: '265px',
        width: '265px',
        sortable: false,
      },
      {
        title: t('label.svKnj'),
        children: [
          {
            title: t('label.input'),
            key: 'svKnj',
            minWidth: '168px',
            width: '168px',
            sortable: false,
          },
        ],
      },
      {
        title: t('label.linking-item'),
        children: [
          {
            title: t('label.input'),
            key: 'linkingItem',
            minWidth: '120px',
            width: '120px',
            sortable: false,
          },
        ],
      },
      {
        title: t('label.common-person'),
        children: [
          {
            title: t('label.input'),
            key: 'linkingItem',
            minWidth: '120px',
            width: '120px',
            sortable: false,
          },
        ],
      },
      {
        title: t('label.letter-size'),
        minWidth: '100px',
        width: '100px',
        align: 'center',
        sortable: false,
      },
      {
        title: t('label.letter-position'),
        key: 'textPosition',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
      {
        title: t('label.letter-color'),
        key: 'color',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
      {
        title: t('label.background-color'),
        key: 'background',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
      {
        title: t('label.time-display'),
        key: 'timeDisplay',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
    ]
    // 初期表示 一覧表示区分が2:個別サービスの場合
  } else if (
    localOneway.or27831.carePlanStyle === Or27831Const.DEFAULT.PLAN_STYLE.INDIVIDUAL_SERVICE
  ) {
    headers.value = [
      {
        title: t('label.timezone'),
        key: 'startTime',
        minWidth: '320px',
        width: '320px',
        sortable: false,
      },
      {
        title: t('label.outside-frame-display'),
        key: 'outSide',
        maxWidth: '65px',
        minWidth: '65px',
        width: '65px',
        sortable: false,
        required: true,
      },
      {
        title: t('label.frequency'),
        key: 'frequency',
        minWidth: '265px',
        width: '265px',
        sortable: false,
      },
      {
        title: t('label.individual-service'),
        key: 'svKnj',
        minWidth: '168px',
        width: '168px',
        sortable: false,
      },
      {
        title: t('label.linking-item'),
        children: [
          {
            title: t('label.input'),
            key: 'linkingItem',
            minWidth: '120px',
            width: '120px',
            sortable: false,
          },
        ],
      },
      {
        title: t('label.individual-person'),
        children: [
          {
            title: t('label.input'),
            key: 'linkingItem',
            minWidth: '120px',
            width: '120px',
            sortable: false,
          },
        ],
      },
      {
        title: t('label.letter-size'),
        minWidth: '74px',
        width: '74px',
        align: 'center',
        sortable: false,
      },
      {
        title: t('label.letter-position'),
        key: 'textPosition',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
      {
        title: t('label.letter-color'),
        key: 'color',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
      {
        title: t('label.background-color'),
        key: 'background',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
      {
        title: t('label.time-display'),
        key: 'timeDisplay',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
    ]
  } else if (
    localOneway.or27831.carePlanStyle === Or27831Const.DEFAULT.PLAN_STYLE.MAIN_DAILY_ACTIVITIES
  ) {
    headers.value = [
      {
        title: t('label.timezone'),
        key: 'startTime',
        minWidth: '320px',
        width: '320px',
        sortable: false,
      },
      {
        title: t('label.outside-frame-display'),
        key: 'outSide',
        maxWidth: '65px',
        minWidth: '65px',
        width: '65px',
        sortable: false,
        required: true,
      },
      {
        title: t('label.frequency'),
        key: 'frequency',
        minWidth: '265px',
        width: '265px',
        sortable: false,
      },
      {
        title: t('label.main-daily-activities'),
        key: 'linkingItem',
        minWidth: '120px',
        width: '120px',
        sortable: false,
      },
      {
        title: t('label.letter-size'),
        minWidth: '74px',
        width: '74px',
        align: 'center',
        sortable: false,
      },
      {
        title: t('label.letter-position'),
        key: 'textPosition',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
      {
        title: t('label.letter-color'),
        key: 'color',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
      {
        title: t('label.background-color'),
        key: 'background',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
      {
        title: t('label.time-display'),
        key: 'timeDisplay',
        minWidth: '70px',
        width: '70px',
        sortable: false,
      },
    ]
  }

  // 戻り値はテーブルデータとして処理されます
  for (const data of local.or27831.dailyPlanDetailList) {
    const values = []
    if (data.frequency1 === '1') {
      values.push('1')
    }
    if (data.frequency2 === '1') {
      values.push('2')
    }
    if (data.frequency3 === '1') {
      values.push('3')
    }
    if (data.frequency4 === '1') {
      values.push('4')
    }
    if (data.frequency5 === '1') {
      values.push('5')
    }
    if (data.frequency6 === '1') {
      values.push('6')
    }
    if (data.frequency7 === '1') {
      values.push('7')
    }

    tableData.value.dataList.push({
      id: data.id,
      // 時間帯-開始時間
      time: {
        // 時間帯-開始時間
        start: data.startTime,
        // 時間帯-終了時間
        end: data.endTime,
        //随時
        atAnyTime: {
          values: [data.startTime === '00:00' && data.endTime === '00:00' ? '1' : '0'],
        } as Mo01332Type,
      } as OrX0044Type,
      outFrameDisplay: { values: data.outFrameDisplay ? ['1'] : [] },
      frequency: {
        frequency: {
          values: values,
        } as Mo01332Type,
      } as OrX0043Type,
      commonService: data.commonService,
      individualService: data.individualService,
      mainDailyActivities: data.mainDailyActivities,
      linkingItem: data.linkingItem,
      commonPerson: data.commonPerson,
      individualPerson: data.individualPerson,
      fontSize: { modelValue: data.fontSize },
      fontSizeTitle: { value: data.fontSize },
      textPosition: { modelValue: data.textPosition },
      color: data.color,
      background: data.background,
      timeDisplay: { modelValue: data.timeDisplay },
    })
  }

  //引継情報.新規モード =TRUEの場合
  if (localOneway.or27831.newMode) {
    // 日課計画入力情報一覧の最後に新規行を追加する
    tableData.value.dataList.push({ ...Or27831Const.DEFAULT.DAILY_PLAN })
    // 新規行を選択状態にする
    selectRow(tableData.value.dataList.length - 1)
  } else {
    // 呼び出し元から指定されたID（引継情報.選択された日課計画詳細データID）に該当する行を選択状態とする
    const detailIndexSelected = tableData.value.dataList.findIndex(
      (x) => x.id === localOneway.or27831.selectWeekPlanDetailDataId
    )
    if (detailIndexSelected !== -1) selectRow(detailIndexSelected)
  }
}

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function onClose() {
  setState({ isOpen: false })
}

/**
 * 確認する
 */
function onConfirm() {
  const confirmDataList = [] as DailyPlanInfo[]
  tableData.value.dataList.forEach((itme) => {
    confirmDataList.push({
      // id
      id: itme.id,
      // 開始時間
      startTime: itme.time.start,
      // 終了時間
      endTime: itme.time.end,
      // 随時
      atAnyTime: itme.time.atAnyTime.values.includes('1') ? '1' : '0',
      // 枠外表示
      outFrameDisplay: itme.outFrameDisplay.values.includes('1') ? true : false,
      // 月曜日
      frequency1: itme.frequency.frequency.values.includes('1') ? '1' : '0',
      // 火曜日
      frequency2: itme.frequency.frequency.values.includes('2') ? '1' : '0',
      // 水曜日
      frequency3: itme.frequency.frequency.values.includes('3') ? '1' : '0',
      // 木曜日
      frequency4: itme.frequency.frequency.values.includes('4') ? '1' : '0',
      // 金曜日
      frequency5: itme.frequency.frequency.values.includes('5') ? '1' : '0',
      // 土曜日
      frequency6: itme.frequency.frequency.values.includes('6') ? '1' : '0',
      // 日曜日
      frequency7: itme.frequency.frequency.values.includes('7') ? '1' : '0',
      // 共通サービス
      commonService: itme.commonService,
      // 個別サービス
      individualService: itme.individualService,
      // 主な日常生活上の活動
      mainDailyActivities: itme.mainDailyActivities,
      // 連動項目
      linkingItem: itme.linkingItem,
      // 共通担当者
      commonPerson: itme.commonPerson,
      // 個別担当者
      individualPerson: itme.individualPerson,
      // 文字サイズ
      fontSize: itme.fontSize.modelValue ?? '',
      // 文字位置
      textPosition: itme.textPosition.modelValue ?? '',
      // 文字色
      color: itme.color,
      // 背景
      background: itme.background,
      // 時間表示
      timeDisplay: itme.timeDisplay.modelValue ?? '',
    })
  })
  emit('update:modelValue', { dailyPlanDetailList: confirmDataList })
  setState({ isOpen: false })
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * チェックを無効にします 枠外表示
 *
 * @param item - WeeklyPlan item datas
 */
function isDisableOutSide(item: DailyPlan) {
  if (item.time.atAnyTime.values.length > 0 && item.time.atAnyTime.values[0] === '1') {
    return true
  }
  const range = timeDifference(item.time.start, item.time.end)

  if (range >= 2 * 60) {
    if (item.outFrameDisplay.values.length >= 0 ) {
      item.outFrameDisplay.values[0] = '1'
    }
    return true
  }

  return false
}

/**
 * 時間差を計算する
 *
 * @param time1 - 開始時間
 *
 * @param time2 - 終了時間
 */
function timeDifference(time1: string, time2: string) {
  // 「HH:mm」を分に変換
  const [h1, m1] = time1.split(':').map(Number)
  const [h2, m2] = time2.split(':').map(Number)

  const minutes1 = h1 * 60 + m1
  const minutes2 = h2 * 60 + m2

  // 絶対的な違い
  const diff = Math.abs(minutes1 - minutes2)
  return diff
}

/**
 * onChangeFontSize
 *
 * @param event - data
 *
 * @param index - data
 */
function onChangeFontSize(event: unknown, index: number) {
  tableData.value.dataList[index].fontSizeTitle = {
    value: tableData.value.dataList[index].fontSize.modelValue ?? '',
  }
}

/**
 * isCheckWeekOther
 *
 * @param value - value
 */
function isCheckWeekOther(value: boolean) {
  return value
}

/**
 * 次へアイコン
 */
function moveUp() {
  if (selectedItemIndex.value > 0) selectRow(selectedItemIndex.value - 1)
}

/**
 * 前へアイコン
 */
function moveDown() {
  if (selectedItemIndex.value < tableData.value.dataList.length - 1)
    selectRow(selectedItemIndex.value + 1)
}

/**
 * 行追加
 */
function onAddItem() {
  tableData.value.dataList.push({ ...Or27831Const.DEFAULT.DAILY_PLAN })
  selectRow(tableData.value.dataList.length - 1)
}

/**
 * 行複写
 */
function onCloneItem() {
  // 最終に新しい行を追加し、追加行を選択状態とする。
  const newRow = {
     ...tableData.value.dataList[selectedItemIndex.value],
    dataId:'',
    id: '',
    modifiedCnt: '',
  }
  tableData.value.dataList.push(newRow)
  selectRow(tableData.value.dataList.length - 1)
}

/**
 * 行削除
 */
async function onDelete() {
  if (selectedItemIndex.value !== -1) {
    const dialogResult =  await openInfoDialog({
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11275'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      thirdBtnLabel: t('btn.cancel'),
    })

    if (dialogResult?.firstBtnClickFlg) {
      tableData.value.dataList = tableData.value.dataList.splice(selectedItemIndex.value-1, 1)
    }
  }
}
/**
 * ダイアログ表示フラグ
 */
 const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ダイアログ表示フラグ
 */
 const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ダイアログ表示フラグ
 */
 const showDialogOr21813 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * エラーダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <div class="mo-1">
        <!-- （日課計画表（詳細データ））日課計画入力情報一覧 -->
        <div class="title-container">
          <c-v-row no-gutters>
            <c-v-col>
              <!-- 行追加ボタン -->
              <g-custom-or-26168 @click="onAddItem" />
              <!-- 行複写ボタン -->
              <g-custom-or-26170
                class="ml-1"
                :disabled="selectedItemIndex == -1"
                @click="onCloneItem"
              />
              <!-- 削除ボタン -->
              <g-custom-or-26171
                class="ml-1"
                :disabled="selectedItemIndex == -1"
                @click="onDelete"
              />
            </c-v-col>

            <c-v-col
              cols="1"
              class="flex-end"
            >
              <g-custom-or-26184
                v-model="or26184"
                @up="moveUp"
                @down="moveDown"
              />
            </c-v-col>

            <c-v-col cols="12">
              <!-- データテーブル -->
              <c-v-data-table
                fixed-header
                :headers="headers"
                :items="tableData.dataList"
                class="table-header table-wrapper"
                hide-default-footer
                items-per-page="-1"
                height="500px"
              >
                <!-- *表示順 -->
                <template #[`header.startTime`]>
                  <span>
                    <span style="color: red">{{ Or27831Const.DEFAULT.REQUIRED }}</span>
                    {{ t('weekly-plan.label.timezone') }}
                  </span>
                </template>

                <template #[`header.dayOfWeek`]>
                  <span>
                    <span style="color: red">{{ Or27831Const.DEFAULT.REQUIRED }}</span>
                    {{ t('weekly-plan.label.frequency') }}
                  </span>
                </template>

                <!-- 一覧 -->
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedItemIndex === index }"
                    @click="selectRow(index)"
                  >
                    <td>
                      <div class="flex-space-between">
                        <g-custom-orX-0044 v-bind="orX0044" v-model="tableData.dataList[index].time" :oneway-model-value="localOneway.orX0044OneWay" />
                      </div>
                    </td>
                    <td :class="{ 'cell-disabled': isDisableOutSide(item),'pa-0':true}">
                      <!-- 枠外表示 -->
                      <base-mo01332
                        v-model="item.outFrameDisplay"
                        class="ml-3 mr-0 mt-0"
                        :oneway-model-value="{
                          ...localOneway.si016OneWay,
                          disabled: isDisableOutSide(item),
                        }"
                      />
                    </td>
                    <td class="pa-0" >
                      <div class="d-flex pa-0" >
                        <!-- 頻度選択ボタン -->
                        <g-custom-orX-0043
                          v-bind="orX0043"
                          v-model="tableData.dataList[index].frequency"
                          :parent-unique-cp-id="props.uniqueCpId"
                          :oneway-model-value="{ ...localOneway.or27831, index }"
                        />
                      </div>
                    </td>
                    <!-- 共通サービス -->
                    <td
                      v-if="
                        localOneway.or27831.carePlanStyle ===
                        Or27831Const.DEFAULT.PLAN_STYLE.COMMON_SERVICE
                      "
                      class="pa-0"
                    >
                      <div class="flex-space-between">
                        <g-custom-or-01087
                          v-bind="or01087"
                          v-model="item.commonService"
                          :unique-cp-id="or01087.uniqueCpId"
                          :index="index"
                        />
                      </div>
                    </td>
                    <!-- 個別サービス -->
                    <td
                      v-if="
                        localOneway.or27831.carePlanStyle ===
                        Or27831Const.DEFAULT.PLAN_STYLE.INDIVIDUAL_SERVICE
                      "
                      class="pa-0"
                    >
                      <div class="flex-space-between">
                        <g-custom-or-01088
                          v-bind="or01088"
                          v-model="item.individualService"
                          :unique-cp-id="or01088.uniqueCpId"
                          :index="index"
                        />
                      </div>
                    </td>
                    <!-- 主な日常生活上の活動の場合 -->
                    <td
                      v-if="
                        localOneway.or27831.carePlanStyle ===
                        Or27831Const.DEFAULT.PLAN_STYLE.MAIN_DAILY_ACTIVITIES
                      "
                      class="pa-0"
                    >
                      <div class="flex-space-between">
                        <g-custom-or-01089
                          v-bind="or01089"
                          v-model="item.mainDailyActivities"
                          :unique-cp-id="or01089.uniqueCpId"
                          :index="index"
                        />
                      </div>
                    </td>
                    <td
                      v-if="
                        localOneway.or27831.carePlanStyle ===
                          Or27831Const.DEFAULT.PLAN_STYLE.COMMON_SERVICE ||
                        localOneway.or27831.carePlanStyle ===
                          Or27831Const.DEFAULT.PLAN_STYLE.INDIVIDUAL_SERVICE
                      "
                      class="pa-0"
                    >
                      <div class="flex-space-between">
                        <!-- 連動項目 -->
                        <g-custom-orX-0047
                          v-bind="orX0047"
                          v-model="tableData.dataList[index].linkingItem"
                          :index="index"
                          :oneway-model-value="codeTypes"
                        />
                      </div>
                    </td>
                    <!-- 共通担当者 -->
                    <td
                      v-if="
                        localOneway.or27831.carePlanStyle ===
                        Or27831Const.DEFAULT.PLAN_STYLE.COMMON_SERVICE
                      "
                      class="pa-0"
                    >
                      <div class="flex-space-between">
                        <g-custom-orX-0045
                          v-model="item.commonPerson"
                          v-bind="orX0045"
                          :unique-cp-id="orX0045.uniqueCpId"
                          :index="index"
                        />
                      </div>
                    </td>
                    <!-- 個別担当者 -->
                    <td
                      v-if="
                        localOneway.or27831.carePlanStyle ===
                        Or27831Const.DEFAULT.PLAN_STYLE.INDIVIDUAL_SERVICE
                      "
                      class="pa-0"
                    >
                      <div class="flex-space-between">
                        <g-custom-orX-0046
                          v-model="item.individualPerson"
                          v-bind="orX0046"
                          :unique-cp-id="orX0046.uniqueCpId"
                          :index="index"/>
                      </div>
                    </td>
                    <td :class="{ 'cell-disabled': isCheckWeekOther(item.dayOfWeekOther) }">
                      <div class="d-flex">
                        <!-- 文字サイズセレクトボックス -->
                        <base-mo01282
                          v-model="item.fontSize"
                          :oneway-model-value="localOneway.si039OneWay"
                          :disabled="isCheckWeekOther(item.dayOfWeekOther)"
                          class="cell-select cell-input w-50"
                          @change="onChangeFontSize($event, index)"
                        ></base-mo01282>

                        <!-- 文字サイズテキストボックス -->
                        <base-mo01280
                          v-model="item.fontSizeTitle"
                          :oneway-model-value="localOneway.si040OneWay"
                          :disabled="isCheckWeekOther(item.dayOfWeekOther)"
                          class="cell-input deviders"
                        ></base-mo01280>
                      </div>
                    </td>
                    <td :class="{ 'cell-disabled': isCheckWeekOther(item.dayOfWeekOther) }">
                      <!-- 文字位置セレクトボックス -->
                      <base-mo01282
                        v-model="item.textPosition"
                        :oneway-model-value="localOneway.si041OneWay"
                        :disabled="isCheckWeekOther(item.dayOfWeekOther)"
                        class="cell-select"
                      ></base-mo01282>
                    </td>
                    <td :class="{ 'cell-disabled': isCheckWeekOther(item.dayOfWeekOther) }">
                      <!-- 文字色ラベル -->
                      <g-custom-or-26281
                        v-model="item.color"
                        :oneway-model-value="{ disabled: isCheckWeekOther(item.dayOfWeekOther) }"
                      />
                    </td>
                    <td :class="{ 'cell-disabled': isCheckWeekOther(item.dayOfWeekOther) }">
                      <!-- 背景色ラベル -->
                      <g-custom-or-26281
                        v-model="item.background"
                        :oneway-model-value="{ disabled: isCheckWeekOther(item.dayOfWeekOther) }"
                      />
                    </td>
                    <td>
                      <!-- 時間表示セレクトボックス -->
                      <base-mo01282
                        v-model="item.timeDisplay"
                        :oneway-model-value="localOneway.si047OneWay"
                        class="cell-select"
                      ></base-mo01282>
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
          </c-v-row>
        </div>
      </div>
    </template>
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <g-custom-or-x-0016
          @close="onClose"
          @confirm="onConfirm"
        ></g-custom-or-x-0016>
      </c-v-row>
    </template>
  </base-mo00024>
    <!-- スロットの使用例 -->
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815_1"
  ></g-base-or21815>
    <!-- スロットの使用例 -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814_1"
  >
  </g-base-or21814>
    <!-- スロットの使用例 -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813_1"
  ></g-base-or21813>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
.mo-1 {
  margin: 0px !important;
}

.color-picker {
  .view {
    width: 25px;
    height: 25px;
    border: 1px solid rgb(var(--v-theme-black-700));
    border-radius: 3px;
  }
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

.flex-space-between {
  display: flex;
  align-items: center;
}

.flex-end {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

.flex-col {
  flex-direction: column;
}

.devider {
  width: 1px !important;
  height: 40px !important;
  background-color: rgb(var(--v-theme-black-200));
}

:deep(.text-center) {
  input {
    text-align: center;
  }
}

textarea {
  &.table-cell {
    &:focus {
      border: none;
      outline: none;
    }
  }
}

.dialog-title {
  margin-bottom: 10px;
  font-size: 18px;
  display: flex;
  column-gap: 10px;
  align-items: center;

  .icon-container {
    width: 35px;
    height: 35px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgb(var(--v-theme-red-100));
    color: rgb(var(--v-theme-error));
  }
}

.cell-disabled {
  background-color: rgb(var(--v-theme-black-50));

  .content {
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
  }
}
// 選択した行のCSS
.select-row {
  background: rgb(var(--v-theme-blue-100));
}

// 右寄せのCSS
.text-align-right {
  text-align: right;
}

.cell-select {
  padding: 0 !important;
}

.cell-input {
  &.deviders {
    border-left: 1px solid rgb(var(--v-theme-black-200)) !important;
  }
}

.w-50 {
  width: 55px !important;
}
</style>
