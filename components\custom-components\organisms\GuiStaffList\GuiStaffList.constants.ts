import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * GuiStaffList:有機体:職員管理画面（画面コンポーネント）
 * 静的データ
 */
export namespace GuiStaffListConst {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('GuiStaffList', seq)

  /** 初期値 */
  export const DEFAULT = {
    /** ログイン日時の許容する差分日付 */
    LOGIN_DATE_ACCEPT_DIFF: 7,
  }

  /** 検索条件 */
  export const SEARCH_CRITERIA = {
    /** 表示対象 */
    TARGET: {
      /** 事業所の職員 */
      SHOKUIN: 3,
      /** システム管理者 */
      ADMIN: 2,
    },
  }
}
