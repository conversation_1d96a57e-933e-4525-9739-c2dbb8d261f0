<script setup lang="ts">
/**
 * Or29825:有機体:主治医複写モーダル
 * GUI01287_主治医複写
 *
 * @description
 * 主治医複写
 *
 * <AUTHOR>
 */
import { reactive, ref, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { Or29757Const } from '../Or29757/Or29757.constants'
import { Or29826Const } from '../Or29826/Or29826.constants'
import { Or29826Logic } from '../Or29826/Or29826.logic'
import { Or29827Const } from '../Or29827/Or29827.constants'
import { Or29827Logic } from '../Or29827/Or29827.logic'
import { Or29825Const } from './Or29825.constants'
import type { Or29825StateType } from './Or29825.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import { useScreenUtils } from '~/utils/useScreenUtils'
import type { Or29825OnewayType } from '~/types/cmn/business/components/Or29825Type'
import type { Or29826OnewayType } from '~/types/cmn/business/components/Or29826Type'
import type { Or29827OnewayType } from '~/types/cmn/business/components/Or29827Type'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { OrX0077OnewayType } from '~/types/cmn/business/components/OrX0077Type'
import type { Or29757Type } from '~/types/cmn/business/components/Or29757Type'
import type {
  AttendingPhysicianStatementCopySelectInEntity,
  AttendingPhysicianStatementCopySelectOutEntity,
} from '~/repositories/cmn/entities/AttendingPhysicianStatementCopySelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  Ikensho2Obj,
  Ikensho3Obj,
} from '~/repositories/cmn/entities/AttendingPhysicianStatementSelectEntity'
import type { OrX0140Type } from '~/types/cmn/business/components/OrX0140Type'
import type { OrX0141Type } from '~/types/cmn/business/components/OrX0141Type'
import { CustomClass } from '~/types/CustomClassType'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or29825OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const orX0077_1 = ref({ uniqueCpId: OrX0077Const.CP_ID(1) })
const or29757_1 = ref({ uniqueCpId: Or29757Const.CP_ID(1) })
const or29826_1 = ref({ uniqueCpId: Or29826Const.CP_ID(1) })
const or29827_1 = ref({ uniqueCpId: Or29827Const.CP_ID(1) })
const or29757Ref = ref({
  updateRefValue: (_newValue: Or29757Type) => Promise,
})

// ローカル双方向bind
const local = reactive({
  or29825: {
    // 操作区分
    operaFlg: Or29825Const.ACTION_TYPE.INIT,
    // 選択用の利用者ＩＤ
    userId: props.onewayModelValue.userId,
    // 選択用の計画期間ＩＤ
    sc1Id: '',
    // 選択用の事業所ＩＤ
    svJigyoId: '',
    // 選択用の履歴ＩＤ
    rirekiId: '',
    // 選択行.改定フラグ
    kaiteiFlg: '',
    // 選択行.作成日
    createYmd: '',
  },
})

// ローカルOneway
const localOneway = reactive({
  // 複写テンプレート
  orX0077Oneway: {
    // 複写ダイアログ
    mo00024Oneway: {
      height: '850px',
      maxWidth: '1480px',
      persistent: true,
      showCloseBtn: true,
      mo01344Oneway: {
        name: 'Or29825',
        toolbarTitle: t('label.attending-physician-copy'),
        toolbarName: 'Or29825ToolBar',
        // ツールバータイトルの左寄せ
        toolbarTitleCenteredFlg: false,
        showCardActions: true,
      },
    } as Mo00024OnewayType,
  } as OrX0077OnewayType,

  // 閉じるボタン設置
  mo00611Oneway: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,

  // 確定ボタン設置
  mo00609Oneway: {
    btnLabel: t('btn.confirm'),
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
  or29826Oneway: {
    kikanList: [],
  } as Or29826OnewayType,
  or29827Oneway: {
    kikanFlg: props.onewayModelValue.kikanFlg,
    rirekiList: [],
  } as Or29827OnewayType,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0077Const.CP_ID(1)]: orX0077_1.value,
  [Or29757Const.CP_ID(1)]: or29757_1.value,
  [Or29826Const.CP_ID(1)]: or29826_1.value,
  [Or29827Const.CP_ID(1)]: or29827_1.value,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or29825StateType>({
  cpId: Or29825Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      OrX0077Logic.state.set({
        uniqueCpId: orX0077_1.value.uniqueCpId,
        state: { isOpen: value },
      })
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await getAttendingPhysicianStatementCopy()
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => OrX0077Logic.state.get(orX0077_1.value.uniqueCpId),
  (newValue) => {
    if (!newValue?.isOpen) {
      close()
    }
  }
)

/**
 * 「計画期間一覧」の行切替
 */
watch(
  () => Or29826Logic.data.get(or29826_1.value.uniqueCpId),
  async (newValue) => {
    if (local.or29825.sc1Id !== newValue?.seletedRowId) {
      const selectKikan = localOneway.or29826Oneway.kikanList.find(
        (item) => item.sc1Id === newValue?.seletedRowId
      )
      if (selectKikan) {
        local.or29825.sc1Id = selectKikan.sc1Id
        local.or29825.svJigyoId = selectKikan.svJigyoId
        local.or29825.operaFlg = Or29825Const.ACTION_TYPE.KIKAN_SELECT
        await getAttendingPhysicianStatementCopy()
      }
    }
  }
)

/**
 * 「履歴情報一覧」の行切替
 */
watch(
  () => Or29827Logic.data.get(or29827_1.value.uniqueCpId),
  async (newValue) => {
    if (local.or29825.rirekiId !== newValue?.seletedRowId) {
      const selectRireki = localOneway.or29827Oneway.rirekiList.find(
        (item) => item.rirekiId === newValue?.seletedRowId
      )
      if (selectRireki) {
        local.or29825.rirekiId = selectRireki.rirekiId
        local.or29825.operaFlg = Or29825Const.ACTION_TYPE.RIREKI_SELECT
        await getAttendingPhysicianStatementCopy()
      }
    }
  }
)

/**
 * 画面表示のデータを取得
 */
const getAttendingPhysicianStatementCopy = async () => {
  const inputParam: AttendingPhysicianStatementCopySelectInEntity = {
    operaFlg: local.or29825.operaFlg,
    kikanFlg: props.onewayModelValue.kikanFlg,
    shisetuId: props.onewayModelValue.shisetuId,
    userId: local.or29825.userId,
    syubetuId: props.onewayModelValue.syubetsuId,
    svJigyoIdList: props.onewayModelValue.svJigyoIdList,
    sc1Id: local.or29825.sc1Id,
    svJigyoId: local.or29825.svJigyoId,
    rirekiId: local.or29825.rirekiId,
  }
  const ret: AttendingPhysicianStatementCopySelectOutEntity = await ScreenRepository.select(
    'attendingPhysicianStatementCopySelect',
    inputParam
  )
  if (local.or29825.operaFlg === Or29825Const.ACTION_TYPE.INIT) {
    localOneway.or29826Oneway.kikanList = ret.data.kikanList
    localOneway.or29827Oneway.rirekiList = ret.data.rirekiList
    local.or29825.sc1Id = ret.data.kikanList[0].sc1Id ?? ''
    local.or29825.svJigyoId = ret.data.kikanList[0].svJigyoId ?? ''
    local.or29825.rirekiId = ret.data.rirekiList[0].rirekiId ?? ''
    local.or29825.kaiteiFlg = ret.data.rirekiList[0].kaiteiFlg ?? ''
    local.or29825.createYmd = ret.data.rirekiList[0].createYmd ?? ''
  } else if (local.or29825.operaFlg === Or29825Const.ACTION_TYPE.KIKAN_SELECT) {
    localOneway.or29827Oneway.rirekiList = ret.data.rirekiList
    local.or29825.rirekiId = ret.data.rirekiList[0].rirekiId ?? ''
    local.or29825.kaiteiFlg = ret.data.rirekiList[0].kaiteiFlg ?? ''
    local.or29825.createYmd = ret.data.rirekiList[0].createYmd ?? ''
  }
  setChildCpBinds(props.uniqueCpId, {
    [Or29757Const.CP_ID(1)]: {
      twoWayValue: {
        ikensho2Obj: getOrX0140Data(ret.data.ikensho2Obj),
        ikensho3Obj: getOrX0141Data(ret.data.ikensho3Obj),
        rirekiObj: {
          kaiteiFlg: local.or29825.kaiteiFlg,
          rirekiId: local.or29825.rirekiId,
          /** 作成者ID */
          shokuId: '',
          /** 作成者 */
          shokuKnj: '',
          /** 作成日 */
          createYmd: local.or29825.createYmd,
          /** ページング番号 */
          pagingNo: '',
          /** ページング総数 */
          pagingCnt: '',
          /** 更新回数 */
          modifiedCnt: '',
        },
      },
      oneWayState: {
        operaFlg: local.or29825.operaFlg,
        disabled: true,
        customClass: new CustomClass({
          outerStyle: 'height: 500px !important',
        }),
      },
    },
  })
}

/**
 * 「閉じるボタン」押下
 */
const close = () => {
  // 本画面を閉じる
  setState({ isOpen: false })
}

/**
 * 「確定ボタン」押下
 */
const confirm = () => {
  if (local.or29825.rirekiId) {
    emit('update:modelValue', local.or29825.rirekiId)
  }
  // 本画面を閉じる
  setState({ isOpen: false })
}

/**
 * 意見書①セクションタブデータを取得する
 *
 * @param data - 表示用「医師意見書データ①」情報
 */
const getOrX0140Data = (data: Ikensho2Obj): OrX0140Type => {
  return {
    // ＩＤ
    iks2Id: data.iks2Id,
    // 医師意見
    ishiDoui: data.ishiDoui,
    // 医師名前
    ishiNameKnj: { value: data.ishiNameKnj },
    // 医療機関TEL
    ishiTel: { value: data.ishiTel },
    // 医療機関名
    iryouKikanKnj: { value: data.iryouKikanKnj },
    // 医療機関FAX
    ishiFax: { value: data.ishiFax },
    // 医療機関所在地
    iryouKikanAddress: { value: data.iryouKikanAddress },
    // 最終診察日付
    sinsatuYmd: {
      value: data.sinsatuYmd,
      mo01343: {
        value: '',
        endValue: '',
      },
    },
    // 意見書回数
    ikenshoKaisuu: data.ikenshoKaisuu,
    // 他科受診
    elseJusinFlg: data.elseJusinFlg,
    // 内科選択フラッグ
    elseJusin1Flg: {
      modelValue: data.elseJusin1Flg === '1',
    },
    // 精神科選択フラッグ
    elseJusin2Flg: {
      modelValue: data.elseJusin2Flg === '1',
    },
    // 外科選択フラッグ
    elseJusin3Flg: {
      modelValue: data.elseJusin3Flg === '1',
    },
    // 整形外科選択フラッグ
    elseJusin4Flg: {
      modelValue: data.elseJusin4Flg === '1',
    },
    // 脳神精外科選択フラッグ
    elseJusin5Flg: {
      modelValue: data.elseJusin5Flg === '1',
    },
    // 皮膚科選択フラッグ
    elseJusin6Flg: {
      modelValue: data.elseJusin6Flg === '1',
    },
    // 泌尿器科選択フラッグ
    elseJusin7Flg: {
      modelValue: data.elseJusin7Flg === '1',
    },
    // 婦人科選択フラッグ
    elseJusin8Flg: {
      modelValue: data.elseJusin8Flg === '1',
    },
    // 眼科選択フラッグ
    elseJusin9Flg: {
      modelValue: data.elseJusin9Flg === '1',
    },
    // 耳鼻咽喉科選択フラッグ
    elseJusin10Flg: {
      modelValue: data.elseJusin10Flg === '1',
    },
    // リハビリテーション科選択フラッグ
    elseJusin11Flg: {
      modelValue: data.elseJusin11Flg === '1',
    },
    // 歯科選択フラッグ
    elseJusin12Flg: {
      modelValue: data.elseJusin12Flg === '1',
    },
    // その他選択フラッグ
    elseJusin13Flg: {
      modelValue: data.elseJusin13Flg === '1',
    },
    // その他科の名前
    elseJusinKnj: {
      value: data.elseJusinKnj,
    },
    // 診断名前1
    sindanName1Knj: {
      value: data.sindanName1Knj,
    },
    // 診断1の日付
    sindan1Ymd: {
      value: data.sindan1Ymd,
      mo01343: {
        value: '',
        endValue: '',
      },
    },
    // 診断名前2
    sindanName2Knj: {
      value: data.sindanName2Knj,
    },
    // 診断2の日付
    sindan2Ymd: {
      value: data.sindan2Ymd,
      mo01343: {
        value: '',
        endValue: '',
      },
    },
    // 診断名前3
    sindanName3Knj: {
      value: data.sindanName3Knj,
    },
    // 診断3の日付
    sindan3Ymd: {
      value: data.sindan3Ymd,
      mo01343: {
        value: '',
        endValue: '',
      },
    },
    // 安定
    anteiFlg: data.anteiFlg,
    // 不安定内容
    huanteiKnj: data.huanteiKnj,
    // 生活機能低下原因
    reasonMemoKnj: { value: data.reasonMemoKnj },
    // 特別医療関連行為1
    tokubetu1Flg: {
      modelValue: data.tokubetu1Flg === '1',
    },
    // 特別医療関連行為2
    tokubetu2Flg: {
      modelValue: data.tokubetu2Flg === '1',
    },
    // 特別医療関連行為3
    tokubetu3Flg: {
      modelValue: data.tokubetu3Flg === '1',
    },
    // 特別医療関連行為4
    tokubetu4Flg: {
      modelValue: data.tokubetu4Flg === '1',
    },
    // 特別医療関連行為5
    tokubetu5Flg: {
      modelValue: data.tokubetu5Flg === '1',
    },
    // 特別医療関連行為6
    tokubetu6Flg: {
      modelValue: data.tokubetu6Flg === '1',
    },
    // 特別医療関連行為7
    tokubetu7Flg: {
      modelValue: data.tokubetu7Flg === '1',
    },
    // 特別医療関連行為8
    tokubetu8Flg: {
      modelValue: data.tokubetu8Flg === '1',
    },
    // 特別医療関連行為9
    tokubetu9Flg: {
      modelValue: data.tokubetu9Flg === '1',
    },
    // 特別医療関連行為10
    tokubetu10Flg: {
      modelValue: data.tokubetu10Flg === '1',
    },
    // 特別医療関連行為11
    tokubetu11Flg: {
      modelValue: data.tokubetu11Flg === '1',
    },
    // 特別医療関連行為12
    tokubetu12Flg: {
      modelValue: data.tokubetu12Flg === '1',
    },
    // 障害高齢者の日常生活自立度
    neta1Cd: data.neta1Cd,
    // 認知症高齢者の日常生活自立度
    neta2Cd: data.neta2Cd,
    // 短期記憶
    chukaku1Flg: data.chukaku1Flg,
    // 認知能力
    chukaku2Flg: data.chukaku2Flg,
    // 伝達能力
    chukaku3Flg: data.chukaku3Flg,
    // 周辺症状フラッグ
    shuhenFlg: data.shuhenFlg,
    // 周辺症状1選択フラッグ
    shuhen1Flg: {
      modelValue: data.shuhen1Flg === '1',
    },
    // 周辺症状2選択フラッグ
    shuhen2Flg: {
      modelValue: data.shuhen2Flg === '1',
    },
    // 周辺症状3選択フラッグ
    shuhen3Flg: {
      modelValue: data.shuhen3Flg === '1',
    },
    // 周辺症状4選択フラッグ
    shuhen4Flg: {
      modelValue: data.shuhen4Flg === '1',
    },
    // 周辺症状5選択フラッグ
    shuhen5Flg: {
      modelValue: data.shuhen5Flg === '1',
    },
    // 周辺症状6選択フラッグ
    shuhen6Flg: {
      modelValue: data.shuhen6Flg === '1',
    },
    // 周辺症状7選択フラッグ
    shuhen7Flg: {
      modelValue: data.shuhen7Flg === '1',
    },
    // 周辺症状8選択フラッグ
    shuhen8Flg: {
      modelValue: data.shuhen8Flg === '1',
    },
    // 周辺症状9選択フラッグ
    shuhen9Flg: {
      modelValue: data.shuhen9Flg === '1',
    },
    // 周辺症状10選択フラッグ
    shuhen10Flg: {
      modelValue: data.shuhen10Flg === '1',
    },
    // 周辺症状11選択フラッグ
    shuhen11Flg: {
      modelValue: data.shuhen11Flg === '1',
    },
    // 周辺症状12その他選択フラッグ
    shuhen12Flg: {
      modelValue: data.shuhen12Flg === '1',
    },
    // 周辺症状12その他
    shuhen12Knj: {
      value: data.shuhen12Knj,
    },
    // 精神症状フラッグ
    seisinFlg: data.seisinFlg,
    //症状名前
    seisinKnj: {
      value: data.seisinKnj,
    },
    // 受診フラグ
    seisinJusinFlg: data.seisinJusinFlg,
    // 受診科の名前
    seisinJusinKnj: {
      value: data.seisinJusinKnj,
    },
    // 更新回数
    modifiedCnt: data.modifiedCnt,
  } as OrX0140Type
}

/**
 *
 * 意見書②セクションタブデータを取得する
 *
 * @param data - 表示用「医師意見書データ②」情報
 */
const getOrX0141Data = (data: Ikensho3Obj): OrX0141Type => {
  return {
    // ＩＤ
    iks3Id: data.iks3Id,
    // 利き腕
    kikiudeFlg: data.kikiudeFlg,
    // 身長
    height: {
      mo00045: { value: data.height },
    },
    // 体重
    weight: {
      mo00045: { value: data.weight },
    },
    // 体重の変化
    weightFlg: data.weightFlg,
    // 四肢欠損
    sin1Flg: {
      modelValue: data.sin1Flg === '1',
    },
    // 四肢欠損部位の名前
    sin1BuiKnj: { value: data.sin1BuiKnj },
    // 麻痺
    sin2Flg: { modelValue: data.sin2Flg === '1' },
    // 右上肢
    sin2R1Flg: { modelValue: data.sin2R1Flg === '1' },
    // 右上肢程度
    sin2R1TeidoFlg: data.sin2R1TeidoFlg,
    // 左上肢
    sin2L1Flg: { modelValue: data.sin2L1Flg === '1' },
    // 左上肢程度
    sin2L1TeidoFlg: data.sin2L1TeidoFlg,
    // 右下肢
    sin2R2Flg: { modelValue: data.sin2R2Flg === '1' },
    // 右下肢程度
    sin2R2TeidoFlg: data.sin2R2TeidoFlg,
    // 左下肢
    sin2L2Flg: { modelValue: data.sin2L2Flg === '1' },
    // 左下肢程度
    sin2L2TeidoFlg: data.sin2L2TeidoFlg,
    // その他選択フラッグ
    sin2ElseFlg: { modelValue: data.sin2ElseFlg === '1' },
    // その他部位の名前
    sin2ElseBuiKnj: { value: data.sin2ElseBuiKnj },
    // その他程度選択フラッグ
    sin2ElseTeidoFlg: data.sin2ElseTeidoFlg,
    // 筋力の低下選択フラグ
    sin3Flg: { modelValue: data.sin3Flg === '1' },
    // 筋力の低下部位の名前
    sin3BuiKnj: { value: data.sin3BuiKnj },
    // 筋力の低下程度選択フラッグ
    sin3TeidoFlg: data.sin3TeidoFlg,
    // 関節の拘縮選択フラグ
    sin4Flg: { modelValue: data.sin4Flg === '1' },
    // 関節の拘縮部位の名前
    sin4BuiKnj: { value: data.sin4BuiKnj },
    // 関節の拘縮程度選択フラッグ
    sin4TeidoFlg: data.sin4TeidoFlg,
    // 関節の痛み選択フラグ
    sin5Flg: { modelValue: data.sin5Flg === '1' },
    // 関節の痛み部位の名前
    sin5BuiKnj: { value: data.sin5BuiKnj },
    // 関節の痛み程度選択フラッグ
    sin5TeidoFlg: data.sin5TeidoFlg,
    // 失調不随意運動選択フラグ
    sin6Flg: { modelValue: data.sin6Flg === '1' },
    // 失調不随意運動右上肢フラグ
    sin6JosiRightFlg: { modelValue: data.sin6JosiRightFlg === '1' },
    // 失調不随意運動左上肢フラグ
    sin6JosiLeftFlg: { modelValue: data.sin6JosiLeftFlg === '1' },
    // 失調不随意運動右下肢フラグ
    sin6KasiRightFlg: { modelValue: data.sin6KasiRightFlg === '1' },
    // 失調不随意運動左下肢フラグ
    sin6KasiLeftFlg: { modelValue: data.sin6KasiLeftFlg === '1' },
    // 失調不随意運動右体幹フラグ
    sin6TaikanRightFlg: { modelValue: data.sin6TaikanRightFlg === '1' },
    // 失調不随意運動左体幹フラグ
    sin6TaikanLeftFlg: { modelValue: data.sin6TaikanLeftFlg === '1' },
    // 褥瘡選択フラグ
    sin7Flg: { modelValue: data.sin7Flg === '1' },
    // 褥瘡部位の名前
    sin7BuiKnj: { value: data.sin7BuiKnj },
    // 褥瘡程度選択フラッグ
    sin7TeidoFlg: data.sin7TeidoFlg,
    // その他皮膚疾患選択フラグ
    sin8Flg: { modelValue: data.sin8Flg === '1' },
    // その他皮膚疾患部位の名前
    sin8BuiKnj: { value: data.sin8BuiKnj },
    // その他皮膚疾患程度選択フラッグ
    sin8TeidoFlg: data.sin8TeidoFlg,
    // 屋外歩行選択フラッグ
    idou1Flg: data.idou1Flg,
    // 車いすの使用選択フラッグ
    idou2Flg: data.idou2Flg,
    // 歩行補助具・装具の使用選択フラッグ1
    idou3Flg1: { modelValue: data.idou3Flg1 === '1' },
    // 歩行補助具・装具の使用選択フラッグ2
    idou3Flg2: { modelValue: data.idou3Flg2 === '1' },
    // 歩行補助具・装具の使用選択フラッグ3
    idou3Flg3: { modelValue: data.idou3Flg3 === '1' },
    // 食事行為選択フラッグ
    eiyo1Flg: data.eiyo1Flg,
    // 現在の栄養状態選択フラッグ
    eiyoFlg: data.eiyoFlg,
    // 留意点の内容
    eiyoPointKnj: { value: data.eiyoPointKnj },
    // 可能の状態1
    jotai1Flg: { modelValue: data.jotai1Flg === '1' },
    // 可能の状態2
    jotai2Flg: { modelValue: data.jotai2Flg === '1' },
    // 可能の状態3
    jotai3Flg: { modelValue: data.jotai3Flg === '1' },
    // 可能の状態4
    jotai4Flg: { modelValue: data.jotai4Flg === '1' },
    // 可能の状態5
    jotai5Flg: { modelValue: data.jotai5Flg === '1' },
    // 可能の状態6
    jotai6Flg: { modelValue: data.jotai6Flg === '1' },
    // 可能の状態7
    jotai7Flg: { modelValue: data.jotai7Flg === '1' },
    // 可能の状態8
    jotai8Flg: { modelValue: data.jotai8Flg === '1' },
    // 可能の状態9
    jotai9Flg: { modelValue: data.jotai9Flg === '1' },
    // 可能の状態10
    jotai10Flg: { modelValue: data.jotai10Flg === '1' },
    // 可能の状態11
    jotai11Flg: { modelValue: data.jotai11Flg === '1' },
    // 可能の状態12
    jotai12Flg: { modelValue: data.jotai12Flg === '1' },
    // 可能の状態13
    jotai13Flg: { modelValue: data.jotai13Flg === '1' },
    // 可能の状態14
    jotai14Flg: { modelValue: data.jotai14Flg === '1' },
    // 可能の状態14その他の内容
    jotai14Knj: { value: data.jotai14Knj },
    // 方針の内容
    housinKnj: { value: data.housinKnj },
    // 生活機能の維持･改善の見通し選択フラッグ
    kitaiFlg: data.kitaiFlg,
    // 医学的管理の必要性1
    kanri1Flg: { modelValue: data.kanri1Flg === '1' },
    // 医学的管理の必要性1（必要性が特に高い）
    kanri1TokFlg: { modelValue: data.kanri1TokFlg === '1' },
    // 医学的管理の必要性2
    kanri2Flg: { modelValue: data.kanri2Flg === '1' },
    // 医学的管理の必要性2（必要性が特に高い）
    kanri2TokFlg: { modelValue: data.kanri2TokFlg === '1' },
    // 医学的管理の必要性3
    kanri3Flg: { modelValue: data.kanri3Flg === '1' },
    // 医学的管理の必要性3（必要性が特に高い）
    kanri3TokFlg: { modelValue: data.kanri3TokFlg === '1' },
    // 医学的管理の必要性4
    kanri4Flg: { modelValue: data.kanri4Flg === '1' },
    // 医学的管理の必要性4（必要性が特に高い）
    kanri4TokFlg: { modelValue: data.kanri4TokFlg === '1' },
    // 医学的管理の必要性5
    kanri5Flg: { modelValue: data.kanri5Flg === '1' },
    // 医学的管理の必要性5（必要性が特に高い）
    kanri5TokFlg: { modelValue: data.kanri5TokFlg === '1' },
    // 医学的管理の必要性6
    kanri6Flg: { modelValue: data.kanri6Flg === '1' },
    // 医学的管理の必要性6（必要性が特に高い）
    kanri6TokFlg: { modelValue: data.kanri6Flg === '1' },
    // 医学的管理の必要性7
    kanri7Flg: { modelValue: data.kanri7Flg === '1' },
    // 医学的管理の必要性7（必要性が特に高い）
    kanri7TokFlg: { modelValue: data.kanri7TokFlg === '1' },
    // 医学的管理の必要性8
    kanri8Flg: { modelValue: data.kanri8Flg === '1' },
    // 医学的管理の必要性8（必要性が特に高い）
    kanri8TokFlg: { modelValue: data.kanri8TokFlg === '1' },
    // 医学的管理の必要性9
    kanri9Flg: { modelValue: data.kanri9Flg === '1' },
    // 医学的管理の必要性9（必要性が特に高い）
    kanri9TokFlg: { modelValue: data.kanri9TokFlg === '1' },
    // 医学的管理の必要性10
    kanri10Flg: { modelValue: data.kanri10Flg === '1' },
    // 医学的管理の必要性10（必要性が特に高い）
    kanri10TokFlg: { modelValue: data.kanri10TokFlg === '1' },
    // 医学的管理の必要性11
    kanri11Flg: { modelValue: data.kanri11Flg === '1' },
    // 医学的管理の必要性11（必要性が特に高い）
    kanri11TokFlg: { modelValue: data.kanri11TokFlg === '1' },
    // 医学的管理の必要性12
    kanri12Flg: { modelValue: data.kanri12Flg === '1' },
    // 医学的管理の必要性12（必要性が特に高い）
    kanri12TokFlg: { modelValue: data.kanri12TokFlg === '1' },
    // 医学的管理の必要性13
    kanri13Flg: { modelValue: data.kanri13Flg === '1' },
    // 医学的管理の必要性13（必要性が特に高い）
    kanri13TokFlg: { modelValue: data.kanri13TokFlg === '1' },
    // 医学的管理の必要性14
    kanri14Flg: { modelValue: data.kanri14Flg === '1' },
    // その他の医療系サービスの内容
    kanri10Knj: { value: data.kanri10Knj },
    // 留意事項1フラッグ
    ryui1Flg: { modelValue: data.ryui1Flg === '1' },
    // 留意事項1の内容
    ryui1Knj: { value: data.ryui1Knj },
    // 留意事項2フラッグ
    ryui2Flg: { modelValue: data.ryui2Flg === '1' },
    // 留意事項2の内容
    ryui2Knj: { value: data.ryui2Knj },
    // 留意事項3フラッグ
    ryui3Flg: { modelValue: data.ryui3Flg === '1' },
    // 留意事項3の内容
    ryui3Knj: { value: data.ryui3Knj },
    // 留意事項4フラッグ
    ryui4Flg: { modelValue: data.ryui4Flg === '1' },
    // 留意事項4の内容
    ryui4Knj: { value: data.ryui4Knj },
    // 留意事項5フラッグ
    ryui5Flg: { modelValue: data.ryui5Flg === '1' },
    // 留意事項5の内容
    ryui5Knj: { value: data.ryui5Knj },
    // 留意事項6フラッグ
    ryui6Flg: { modelValue: data.ryui6Flg === '1' },
    // 留意事項6その他の内容
    ryui6Knj: { value: data.ryui6Knj },
    // 留意事項7フラッグ
    ryui7Flg: { modelValue: data.ryui7Flg === '1' },
    // 感染症の有無のフラッグ
    kansenFlg: data.kansenFlg,
    // 感染の内容
    kansenKnj: { value: data.kansenKnj },
    // 特記すべき事項
    ikenKnj: { value: data.ikenKnj },
    // 更新回数
    modifiedCnt: data.modifiedCnt,
  } as OrX0141Type
}

/**
 * 「利用者一覧」の行切替
 *
 * @param userSelfId - 利用者ID
 */
const onChangeUserSelect = async (userSelfId: string) => {
  local.or29825.operaFlg = Or29825Const.ACTION_TYPE.INIT
  local.or29825.userId = userSelfId
  // 選択用の計画期間ＩＤ
  local.or29825.sc1Id = ''
  // 選択用の事業所ＩＤ
  local.or29825.svJigyoId = ''
  // 選択用の履歴ＩＤ
  local.or29825.rirekiId = ''
  await getAttendingPhysicianStatementCopy()
}
</script>

<template>
  <!-- 複写テンプレート -->
  <g-custom-or-x-0077
    v-bind="orX0077_1"
    :oneway-model-value="localOneway.orX0077Oneway"
    @on-change-user-select="onChangeUserSelect"
  >
    <template #filter>
      <c-v-row no-gutters>
        <g-custom-or-29826
          v-if="props.onewayModelValue.kikanFlg === '1'"
          :oneway-model-value="localOneway.or29826Oneway"
          v-bind="or29826_1"
          class="mr-2"
        />
        <g-custom-or-29827
          :oneway-model-value="localOneway.or29827Oneway"
          v-bind="or29827_1"
          class="mb-2"
        />
      </c-v-row>
    </template>

    <!-- 複写body -->
    <template #copyMain>
      <div class="main-view">
        <!--主治医意見書コンテンツタブ-->
        <g-custom-or-29757
          v-bind="or29757_1"
          ref="or29757Ref"
        /></div
    ></template>

    <!-- フッターボタン -->
    <template #footer>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611Oneway"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 確定ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mr-2"
          @click="confirm()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </g-custom-or-x-0077>
</template>

<style scoped lang="scss">
.main-view {
  max-height: 550px;
  overflow: hidden;
}
</style>
