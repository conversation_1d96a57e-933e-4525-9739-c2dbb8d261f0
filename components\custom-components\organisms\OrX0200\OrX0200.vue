<script setup lang="ts">
/**
 * OrX0200:有機体:介護認定項目
 * GUI00799_［アセスメント］画面（居宅）（6①）
 *
 * @description
 * 介護認定項目を表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch, toRefs } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import type { Or10362OnewayType, Or10362Type } from '../Or10362/Or10362.type'
import type {
  Mo00018OnewayType,
  Mo00018Type,
} from '../../../../types/business/components/Mo00018Type'
import { Or28500Logic } from '../Or28500/Or28500.logic'
import { Or28500Const } from '../Or28500/Or28500.constants'
import type { SaveData } from '../Or29241/Or29241.type'
import { OrX0200Const } from './OrX0200.constants'
import { useNuxtApp } from '#app'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { OrX0200OnewayType, OrX0200Type } from '~/types/cmn/business/components/OrX0200Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import {
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or28500OnewayType, Or28500Type } from '~/types/cmn/business/components/Or28500Type'
import type {
  AssessmentHomeChosaCntInEntity,
  AssessmentHomeChosaCntOutEntity,
  AssessmentHomeChosaInfoSelectInEntity,
  AssessmentHomeChosaInfoSelectOutEntity,
  AssessmentHomeChosaUpdateInEntity,
} from '~/repositories/cmn/entities/AssessmentHomeChosaEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { ResBodyStatusCode } from '~/constants/api-constants'
import type { TeX0002Type } from '~/types/cmn/business/components/TeX0002Type'
const $log = useNuxtApp().$log as DebugLogPluginInterface
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  modelValue: OrX0200Type
  onewayModelValue?: OrX0200OnewayType
  commonInfo?: TeX0002Type
  onSavetabData?: (param: string) => Promise<SaveData>
}

const props = defineProps<Props>()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const { onSavetabData } = props

const { commonInfo } = toRefs(props)

/**************************************************
 * 変数定義
 **************************************************/

//  現在選択されている行のkeyです
const currentSelectedKey = ref<number>(-1)

const or28500 = ref({ uniqueCpId: '' })

// ドラッグ可能なダイアログボックス
const or10362ModelValue = ref<Or10362Type>({ isOpen: false, title: '' })

/** デフォルトOneway */
const defaultOneway = reactive({
  OrX0200Oneway: {
    /** サブタイトル */
    subTitle: '',
    /** 取込ボタン名 */
    importBtnToolTip: '',
    importBtnDisplayFlg: true,
    titleDisplayRows: 'max',
    titleWidth: '300px',
  } as OrX0200OnewayType,
})

/** One-way */
const localOneway = reactive({
  or10362Oneway: {
    width: '500px',
    height: '600px',
  } as Or10362OnewayType,
  dialogTitleOneway: {
    /** 値のフォントの太さ */
    valueFontWeight: 'bold',
    /** 値 */
    value: props.onewayModelValue?.subTitle,
    /** カスタムクラス */
    customClass: new CustomClass({
      outerClass: 'background-color',
      labelClass: 'd-none',
      itemClass: 'v-toolbar-title',
    }),
  } as Mo01338OnewayType,
  OrX0200Oneway: {
    ...defaultOneway.OrX0200Oneway,
    ...props.onewayModelValue,
  } as OrX0200OnewayType,
  // サブタイトル
  subTitleOneway: {
    /** 値のフォントの太さ */
    valueFontWeight: 'bold',
    /** 値 */
    value: props.onewayModelValue?.subTitle,
    /** カスタムクラス */
    customClass: new CustomClass({
      outerClass: 'background-color',
      labelClass: 'd-none',
      itemClass: 'v-toolbar-title',
      outerStyle: 'background:transparent',
      itemStyle: 'fontSize:18px',
    }),
  } as Mo01338OnewayType,
  // 取込ボタン
  ImportBtnLabelOneway: {
    /** 値 */
    btnLabel: props.onewayModelValue?.importBtnName,
  } as Mo00611OnewayType,
  // 取込選択ボタン
  importBtnOneway: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  // 基本動作詳細ボタン
  detailBtnOneway: {
    btnIcon: 'comment',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  btnTooltip: {
    importBtn: props.onewayModelValue?.importBtnToolTip,
  },
  mo00609CloseBtnOneway: {
    btnLabel: t('btn.close'),
    width: '90px',
  } as Mo00609OnewayType,
  // 選択肢詳細アイコン
  selectOptionsIcon: {
    btnIcon: 'info',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    minWidth: '24px',
    minHeight: '24px',
    height: '24px',
    width: '24px',
  } as Mo00009OnewayType,
  or28500: {
    sc1Id: '',
    svJigyoId: '',
    userId: '',
    kikanFlg: '',
    ninteiFlg: '',
  } as Or28500OnewayType,
})

const or28500ModelValue = ref<Or28500Type>({
  isBundleImport: { modelValue: false } as Mo00018Type,
  questionnaireRevisionFlg: '1',
  historySelectedList: [],
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or28500Const.CP_ID(0)]: or28500.value,
})

/**************************************************
 * 計算プロパティ
 **************************************************/
const titleHeight = computed(() => {
  const row = parseInt(localOneway.OrX0200Oneway.titleDisplayRows)
  if (isNaN(row)) {
    return 'auto'
  }
  const height = row * 48

  return `${height}px`
})

// ダイアログ表示フラグ
const showDialogOr28500 = computed(() => {
  // Or28500のダイアログ開閉状態
  return Or28500Logic.state.get(or28500.value.uniqueCpId)?.isOpen ?? false
})

const radioGroupInfoComputed = computed(() => {
  if (!localOneway.OrX0200Oneway.codeMasterList) {
    return Array<string>(20).fill('')
  }

  // 正規表現
  const reg = /^\d-/
  const codeList = localOneway.OrX0200Oneway.codeMasterList

  // 結果を格納する空の配列
  const result: string[] = []

  // ラベルを抽出し、文字列配列に変換する
  const labelList = codeList.map((item) => item.label)

  // 対応するインデックスを取得する
  const indexList = codeList
    .map((item, index) => (reg.test(item.label) ? index : null))
    .filter((item) => item !== null)

  // 結果を生成
  labelList.reduce((acc, item, index) => {
    if (indexList.includes(index)) {
      acc.push(item + '\r\n')
    } else {
      acc[acc.length - 1] += `      ${item}\r\n`
    }
    return acc
  }, result)

  return result
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<OrX0200Type>({
  cpId: OrX0200Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  $log.debug(`★[onMounted] [cpId]${OrX0200Const.CP_ID(0)} [uId]${props.uniqueCpId}`)
})

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    refValue.value = cloneDeep(newValue)

    // RefValueを更新する
    useScreenStore().setCpTwoWay({
      cpId: OrX0200Const.CP_ID(0),
      uniqueCpId: props.uniqueCpId,
      value: refValue.value,
      isInit: true,
    })
  },
  { deep: true }
)

watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.OrX0200Oneway = {
      ...defaultOneway.OrX0200Oneway,
      ...newValue,
    }
    localOneway.subTitleOneway.value = localOneway.OrX0200Oneway.subTitle ?? ''
    localOneway.btnTooltip.importBtn = localOneway.OrX0200Oneway.importBtnToolTip ?? ''
    localOneway.ImportBtnLabelOneway.btnLabel = localOneway.OrX0200Oneway.importBtnName ?? ''
  },
  { deep: true }
)

/**************************************************
 * 関数
 **************************************************/
/**
 *  取込ボタンクリック処理
 *
 * ［GUI00632_期間内履歴選択］ ダイアログをポップアップで起動する
 */
async function importBtnClick() {
  // 取得したデータ件数2が0件の場合 処理を終了する。
  if ((await getChosaCnt()) <= 0) {
    return
  }

  localOneway.or28500 = {
    sc1Id: commonInfo.value?.sc1Id ?? '',
    svJigyoId: commonInfo.value?.jigyoId ?? '',
    userId: commonInfo.value?.userId ?? '',
    kikanFlg: commonInfo.value?.kikanKanriFlg ?? '',
    ninteiFlg: commonInfo.value?.ninteiFormF ?? '',
  }
  Or28500Logic.state.set({
    uniqueCpId: or28500.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * ［GUI00632_期間内履歴選択］返却情報取得
 *
 * @param rtnData -返却情報
 */
async function onGetOr28500Value(rtnData: Or28500Type) {
  if (!rtnData.historySelectedList.length) return
  const cschId = rtnData.historySelectedList[0]?.cschId?.toString() ?? ''
  await getAssessmentHomeChosaInfo(cschId)
  if (rtnData.isBundleImport.modelValue) {
    // 返却情報.一括取込フラグ＝1の場合 調査票一括取込処理を行う。
    // save
    // データを再取得します
    if (!onSavetabData) return
    const { gdlId = '', sc1Id = '' } = await onSavetabData('0')
    void updateAssessmentHomeChosaInfo({
      cschId,
      gdlId,
      sc1Id,
    })
  }
}

/**
 * 調査票のデータ件数を取得する。
 *
 */
async function getChosaCnt() {
  const inputData: AssessmentHomeChosaCntInEntity = {
    sc1Id: commonInfo.value?.sc1Id ?? '',
    userId: commonInfo.value?.userId ?? '',
    svJigyoId: commonInfo.value?.jigyoId ?? '',
  }
  const resData: AssessmentHomeChosaCntOutEntity = await ScreenRepository.select(
    'assessmentHomeChosaCntSelect',
    inputData
  )
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    return Number(resData.data.chosaCnt2)
  }
  return 0
}

/**
 * 指定された認定調査票情報を取得する。
 *
 * @param paramId -返却情報.調査票ID
 */
async function getAssessmentHomeChosaInfo(paramId: string) {
  const inputData: AssessmentHomeChosaInfoSelectInEntity = {
    cschId: paramId,
  }
  const resData: AssessmentHomeChosaInfoSelectOutEntity = await ScreenRepository.select(
    'assessmentHomeChosaInfoSelect',
    inputData
  )

  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    const { cpnTucCsc3H21Info } = resData.data
    refValue.value?.careCertificationList.sectionList.forEach((item) => {
      item.sectionItems.forEach((subItem) => {
        subItem.raidoValue = cpnTucCsc3H21Info?.[subItem.name] ?? ''
      })
    })
  }
}

/**
 * 調査票一括取込処理を行う。
 *
 * @param param -返却情報
 */
async function updateAssessmentHomeChosaInfo(param: {
  cschId: string
  gdlId: string
  sc1Id: string
}) {
  const inputData: AssessmentHomeChosaUpdateInEntity = {
    ...param,
    ninteiFlg: commonInfo.value?.ninteiFormF ?? '',
    tabId: commonInfo.value?.activeTabId ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: commonInfo.value?.userId ?? '',
    svJigyoId: commonInfo.value?.jigyoId ?? '',
  }
  await ScreenRepository.update('assessmentHomeChosaUpdate', inputData)
}

/**
 *  取込ボタンクリック処理
 *
 * @param selectedKey -行を選択しますkey
 */
function onOpenDialog(selectedKey: number) {
  currentSelectedKey.value = selectedKey
  or10362ModelValue.value = {
    ...or10362ModelValue.value,
    isOpen: true,
  }
  scrollToIndex(selectedKey)
}

/**
 * 所定の位置までスクロールします
 *
 * @param index -行内のindexです
 */
function scrollToIndex(index: number) {
  const element = document.getElementById(`item-${index}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

function checkChange(
  index: number,
  checkboxItems: {
    /** 順番 */
    no: string
    /** 詳細情報 */
    detail: string
    /** チェックモデル */
    check: Mo00018Type
    /** onewayモデル */
    onewayModelValue: Mo00018OnewayType
  }[]
) {
  if (index === 0) {
    // 最初のチェックボックスが変更された場合、他のチェックボックスを全てオフにする
    checkboxItems.forEach((item, i) => {
      if (i !== index) {
        item.check.modelValue = false
      }
    })
  } else {
    // 最初のチェックボックス以外が変更された場合、最初のチェックボックスをオフにする
    checkboxItems[0].check.modelValue = false
  }
}
</script>

<template>
  <div class="OrX0200Wrapper">
    <c-v-row
      no-gutters
      class="radio-title"
    >
      <!-- 見出し ●６-①基本（身体機能・起居）動作-->
      <c-v-col
        cols="7"
        class="d-flex flex-row align-center"
      >
        <base-mo01338 :oneway-model-value="localOneway.subTitleOneway"> </base-mo01338>
      </c-v-col>
      <c-v-col
        v-if="localOneway.OrX0200Oneway.importBtnDisplayFlg"
        class="d-flex flex-row align-center justify-end"
      >
        <base-mo00611
          :oneway-model-value="localOneway.ImportBtnLabelOneway"
          class="import-btn"
          @click="importBtnClick"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row
      v-for="(sub, subIndex) in refValue?.careCertificationList?.sectionList"
      :key="subIndex"
      :class="{ dataListNoTitle: sub.sectionTitle === '' }"
      class="dataList"
      no-gutters
    >
      <c-v-col
        cols="auto"
        :class="[
          'sectionTitle d-flex align-center justify-center',
          { 'show-bottom-border': titleHeight !== 'auto' },
        ]"
        :style="{ height: titleHeight }"
      >
        <!-- 要介護認定項目 -->
        <div class="vertical-text">
          {{ sub.sectionTitle }}
        </div>
      </c-v-col>
      <c-v-col class="leftSubContent">
        <!-- 要介護認定項目 項目名と選択肢 -->
        <c-v-row
          v-for="(row, rowIndex) in sub.sectionItems"
          :key="rowIndex"
          no-gutters
          class="select-options"
        >
          <c-v-col
            cols="auto"
            class="radio-group-title"
            :style="{ width: localOneway.OrX0200Oneway.titleWidth }"
          >
            <div
              class="pl-3"
              @dblclick="onOpenDialog(rowIndex)"
            >
              {{ row.itemName }}
            </div>
            <div>
              <base-mo00009 :oneway-model-value="localOneway.selectOptionsIcon">
                <c-v-tooltip
                  activator="parent"
                  location="right"
                  interactive
                >
                  <div class="tooltip-content">
                    {{ radioGroupInfoComputed[rowIndex] }}
                  </div>
                </c-v-tooltip>
              </base-mo00009>
            </div>
          </c-v-col>
          <c-v-col
            :id="props.uniqueCpId + rowIndex"
            class="itemRow d-flex align-center flex-wrap"
          >
            <div
              v-if="row.itemType === '0'"
              class="d-flex flex-row flex-wrap"
            >
              <div
                v-for="(item, index) in row.checkboxItems"
                :key="index"
                class="d-flex justify-center ml-7 align-center"
              >
                <!-- チェックボックス -->
                <base-mo00018
                  v-model="item.check"
                  class="flex-0-0"
                  :oneway-model-value="item.onewayModelValue"
                  :item-label="item.no"
                  @change="checkChange(index, row.checkboxItems!)"
                  @click.stop
                >
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :text="item.detail"
                  />
                </base-mo00018>
                <div class="text-center ml-2">{{ item.no }}</div>
              </div>
            </div>
            <div
              v-if="row.itemType === '1'"
              class="d-flex flex-row flex-wrap"
            >
              <base-mo00039
                v-model="row.raidoValue"
                class="flex-1-1"
                :oneway-model-value="{ ...row.radioOneway, checkOff: true }"
              >
                <div
                  v-for="(item, index) in row.raidoItems"
                  :key="index"
                  class="d-flex flex-column justify-center align-center radio-item"
                >
                  <!-- <div class="text-center mb-n2">{{ item.value }}</div> -->
                  <base-at-radio
                    class="flex-1-1 ml-7"
                    :name="'radio' + rowIndex + index"
                    :value="item.value"
                    :radio-label="item.value"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :text="item.label"
                    />
                  </base-at-radio>
                </div>
              </base-mo00039>
            </div>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </div>
  <g-custom-or-28500
    v-if="showDialogOr28500"
    v-bind="or28500"
    v-model="or28500ModelValue"
    :oneway-model-value="localOneway.or28500"
    @on-confirm="onGetOr28500Value"
  />
</template>

<style scoped lang="scss">
.v-col {
  padding: 0px;
}
.sectionTitle {
  background-color: rgb(219, 238, 254);
  width: 48px !important;
  height: 100%;
  &.show-bottom-border {
    border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
  }
}
.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: upright;
  font-size: 14px;
  text-align: center;
}
.radio-group-title {
  background-color: rgb(219, 238, 254);
  display: flex;
  align-items: center;
  font-size: 14px;
  button {
    margin-top: -1px;
  }
  :deep(.material-symbols-rounded) {
    font-variation-settings:
      'FILL' 1,
      'wght' 400,
      'GRAD' 0,
      'opsz' 20;
    opacity: 0.5;
    font-size: 20px;
  }
}
.select-options {
  height: 48px;
}
.radio-title {
  background-color: #fff;
  padding: 16px 24px;
}
.import-btn {
  width: 92px;
  min-height: 32px !important;
  height: 32px !important;
  padding: 0px !important;
}
.radio-item {
  :deep(.v-label.v-label--clickable) {
    margin-left: 8px !important;
  }
}
.tooltip-content {
  white-space: pre;
}
.OrX0200Wrapper {
  .dataListNoTitle {
    background: rgba(var(--v-theme-surface));
  }

  .dataList {
    border: 1px rgb(var(--v-theme-black-200)) solid !important;
    background-color: #fff;

    .v-row {
      min-height: 36px;
    }

    .leftSubContent {
      background: rgba(var(--v-theme-surface));

      .v-row {
        .v-col {
          border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
          border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
        }
      }

      .v-row:last-child {
        .v-col {
          border-bottom: unset !important;
        }
      }

      .itemRow :deep(.v-checkbox-btn) {
        min-height: 32px;
        height: 32px;
      }

      .itemRow :deep(.radio-group) {
        margin-top: unset !important;
      }

      .itemRow :deep(.v-radio) {
        min-height: 32px;
        height: 32px;
        flex: 0 0 auto !important;
        align-self: center;
      }

      .itemRow {
        .v-sheet {
          margin-right: 0 !important;
        }
      }

      .itemRow :deep(.v-selection-control--density-default) {
        --v-selection-control-size: 30px;
      }

      .itemRow :deep(.v-selection-control) {
        margin-right: unset !important;
      }
    }
  }

  .dataList:not(:last-child) {
    border-bottom: unset !important;
  }
}
.row-selected {
  background: rgb(var(--v-theme-blue-100));
}
</style>
