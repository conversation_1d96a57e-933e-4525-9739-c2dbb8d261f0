<script setup lang="ts">
/**
 * GUI00830_ケアチェックマスタ
 *
 * @description
 * ケアチェックマスタ
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or27825Const } from '~/components/custom-components/organisms/Or27825/Or27825.constants'
import { Or27825Logic } from '~/components/custom-components/organisms/Or27825/Or27825.logic'
import { Or41345Const } from '~/components/custom-components/organisms/Or41345/Or41345.constants'
import type { Or41345OnewayType } from '~/types/cmn/business/components/Or41345Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**
 * 画面ID
 */
const screenId = 'GUI00830'
/**
 * ルーティング
 */
const routing = 'GUI00830/pinia'
/**
 * 画面物理名
 */
const screenName = 'GUI00830'
/**
 * 画面状態管理用操作変数
 */
const screenStore = useScreenStore()
/* 変数定義 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * Or27825子コンポーネントのユニークIDを保持するref
 */
const Or27825 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
/**
 * 画面領域を初期化する
 */
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00830' },
})

/**************************************************
 * Props
 **************************************************/
/**
 * piniaから最上位の画面コンポーネント情報を取得する
 * これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
 */
const pageComponent = screenStore.screen().supplement.pageComponent

/*
 * 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
 */
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or27825Logic.initialize(pageComponent.uniqueCpId)
}

/* 子コンポーネントのユニークIDを設定する */
Or27825.value.uniqueCpId = pageComponent.uniqueCpId

/**
 * 自身のPinia領域をセットアップ
 */
const init = useInitialize({
  cpId: 'GUI00830',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27825Const.CP_ID(0) }],
})
Or27825Logic.initialize(init.childCpIds.Or27825.uniqueCpId)

/* 子コンポーネントのユニークIDを設定する */
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27825Const.CP_ID(0)]: Or27825.value,
})

/**
 * Or27825 onewayModelValue
 */
const Or41345OnewayModel: Or41345OnewayType = {
  shisetuId: systemCommonsStore.getShisetuId ?? '1',
  svJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
  tabId: Or41345Const.TAB.TAB_ID_CARE_CHECK,
  sysRyaku: '',
  kbnFlg: '1',
}

/**
 * ダイアログ表示フラグ
 */
const showDialogOr41345 = computed(() => {
  // Or27825 のダイアログ開閉状態
  return Or27825Logic.state.get(Or27825.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or27825)
 */
function onClickOr27825() {
  // Or27825のダイアログ開閉状態を更新する
  Or27825Logic.state.set({
    uniqueCpId: Or27825.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27825()"
        >GUI00830_ケアチェックマスタ
      </v-btn>
      <g-custom-or-41345
        v-if="showDialogOr41345"
        v-bind="Or27825"
        :oneway-model-value="Or41345OnewayModel"
      />
    </c-v-col>
  </c-v-row>
</template>
