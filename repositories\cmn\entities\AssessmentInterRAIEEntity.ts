import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
import type { IReportInEntity } from '~/types/business/core/ReportRepositoryType'
/**
 * アセスメント(インターライ)画面E
 *
 * @description
 * アセスメント(インターライ)画面E API用エンティティ
 *
 * <AUTHOR>
 */
export interface AssessmentInterRAIESelectEntity extends InWebEntity {
  /**
   * 履歴ID
   */
  raiId: string
}

/**
 * アセスメント(インターライ)画面E取得出力エンティティ
 */
export interface AssessmentInterRAIESelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    // サブ情報（E）
    subInfoE: SubInfoEItemType
  }
}

/**
 * サブ情報（E）-Item
 */
export interface SubInfoEItemType {
  /**
   * インデックス署名を追加します
   */
  [key: string]: string
  /**
   * アセスメントID
   */
  raiId: string

  /**
   * 計画期間ID
   */
  sc1Id: string

  /**
   * 法人ID
   */
  houjinId: string

  /**
   * 施設ID
   */
  shisetuId: string

  /**
   * 事業者ID
   */
  svJigyoId: string

  /**
   * 利用者ID
   */
  userid: string

  /**
   * 調査アセスメント種別
   */
  assType: string

  /**
   * 調査日
   */
  assDateYmd: string

  /**
   * 調査者
   */
  assShokuId: string

  /**
   * 気分の兆候_否定
   */
  e1A: string

  /**
   * 気分の兆候_怒り
   */
  e1B: string

  /**
   * 気分の兆候_恐れ
   */
  e1C: string

  /**
   * 気分の兆候_不調
   */
  e1D: string

  /**
   * 気分の兆候_不安
   */
  e1E: string

  /**
   * 気分の兆候_悲しみ苦悩心配
   */
  e1F: string

  /**
   * 気分の兆候_泣く
   */
  e1G: string

  /**
   * 気分の兆候_ひどいことが起こりそう
   */
  e1H: string

  /**
   * 気分の兆候_興味
   */
  e1I: string

  /**
   * 気分の兆候_社会的交流の減少
   */
  e1J: string

  /**
   * 気分の兆候_快感喪失
   */
  e1K: string

  /**
   * 利用者自身_興味や喜び
   */
  e2A: string

  /**
   * 利用者自身_不安
   */
  e2B: string

  /**
   * 利用者自身_絶望
   */
  e2C: string

  /**
   * 行動の問題_徘徊
   */
  e3A: string

  /**
   * 行動の問題_暴言
   */
  e3B: string

  /**
   * 行動の問題_暴行
   */
  e3C: string

  /**
   * 行動の問題_社会的迷惑行為
   */
  e3D: string

  /**
   * 行動の問題_性的行動や脱衣
   */
  e3E: string

  /**
   * 行動の問題_抵抗
   */
  e3F: string

  /**
   * 行動の問題_退居家出
   */
  e3G: string

  /**
   * 生活満足度
   */
  e4: string

  /**
   * e1_メモ
   */
  e1MemoKnj: string

  /**
   * e1_メモフォント
   */
  e1MemoFont: string

  /**
   * e1_メモ色
   */
  e1MemoColor: string

  /**
   * e2_メモ
   */
  e2MemoKnj: string

  /**
   * e2_メモフォント
   */
  e2MemoFont: string

  /**
   * e2_メモ色
   */
  e2MemoColor: string

  /**
   * e3_メモ
   */
  e3MemoKnj: string

  /**
   * e3_メモフォント
   */
  e3MemoFont: string

  /**
   * e3_メモ色
   */
  e3MemoColor: string

  /**
   * e4_メモ
   */
  e4MemoKnj: string

  /**
   * e4_メモフォント
   */
  e4MemoFont: string

  /**
   * e4_メモ色
   */
  e4MemoColor: string
}

/**
 * アセスメント(インターライ)画面保存入力エンティティ
 */
export interface AssessmentInterRAIEUpdateEntity extends InWebEntity {
  /**
   * システム略称
   */
  sysRyaku: string

  /**
   * セクション名
   */
  sectionName: string

  /**
   * システムコード
   */
  gsyscd: string

  /**
   * 機能ID
   */
  kinouId: string

  /**
   * 職員ID
   */
  shokuId: string

  /**
   * 期間管理フラグ
   */
  kikanKanriFlg: string

  /**
   * 履歴番号
   */
  historyNo: string

  /**
   * 事業者名
   */
  svJigyoName: string

  /**
   * 期間番号
   */
  periodNo: string

  /**
   * 開始日
   */
  startYmd: string

  /**
   * 終了日
   */
  endYmd: string

  /**
   * インデックス
   */
  index: string

  /**
   * タブ名
   */
  tabName: string

  /**
   * e文書用パラメータ
   */
  edocumentUseParam: IReportInEntity

  /**
   * e文書削除用パラメータ
   */
  edocumentDeleteUseParam: IReportInEntity
  /**
   * 法人ID
   */
  houjinId: string

  /**
   * 施設ID
   */
  shisetuId: string

  /**
   * 事業者ID
   */
  svJigyoId: string

  /**
   * 利用者ID
   */
  userId: string

  /**
   * 種別ID
   */
  syubetsuId: string

  /**
   * サブ区分
   */
  subKbn: string

  /**
   * 期間対象フラグ
   */
  lbKikan: string

  /**
   * 更新区分
   */
  updateKbn: string

  /**
   * 履歴更新区分
   */
  historyUpdateKbn: string

  /**
   * 選定表・検討表作成区分
   */
  tableCreateKbn: string

  /**
   * 計画対象期間ID
   */
  sc1Id: string

  /**
   * 基準日
   */
  kijunbiYmd: string

  /**
   * 作成者ID
   */
  sakuseiId: string

  /**
   * 調査アセスメント種別
   */
  assType: string

  /**
   * アセスメントID
   */
  raiId: string
  /**
   * e
   */
  subInfoE: {
    /**
     * key
     */
    [key: string]: string
    /**
     * 気分の兆候_否定
     */
    e1A: string

    /**
     * 気分の兆候_怒り
     */
    e1B: string

    /**
     * 気分の兆候_恐れ
     */
    e1C: string

    /**
     * 気分の兆候_不調
     */
    e1D: string

    /**
     * 気分の兆候_不安
     */
    e1E: string

    /**
     * 気分の兆候_悲しみ、苦悩、心配
     */
    e1F: string

    /**
     * 気分の兆候_泣く
     */
    e1G: string

    /**
     * 気分の兆候_ひどいことが起こりそう
     */
    e1H: string

    /**
     * 気分の兆候_興味
     */
    e1I: string

    /**
     * 気分の兆候_社会的交流の減少
     */
    e1J: string

    /**
     * 気分の兆候_快感喪失
     */
    e1K: string

    /**
     * 利用者自身_興味や喜び
     */
    e2A: string

    /**
     * 利用者自身_不安
     */
    e2B: string

    /**
     * 利用者自身_絶望
     */
    e2C: string

    /**
     * 行動の問題_徘徊
     */
    e3A: string

    /**
     * 行動の問題_暴言
     */
    e3B: string

    /**
     * 行動の問題_暴行
     */
    e3C: string

    /**
     * 行動の問題_社会的迷惑行為
     */
    e3D: string

    /**
     * 行動の問題_性的行動や脱衣
     */
    e3E: string

    /**
     * 行動の問題_抵抗
     */
    e3F: string

    /**
     * 行動の問題_退居・家出
     */
    e3G: string

    /**
     * 生活満足度
     */
    e4: string

    /**
     * e1_メモ
     */
    e1memoknj: string

    /**
     * e1_メモフォント
     */
    e1memofont: string

    /**
     * e1_メモ色
     */
    e1memocolor: string

    /**
     * e2_メモ
     */
    e2memoknj: string

    /**
     * e2_メモフォント
     */
    e2memofont: string

    /**
     * e2_メモ色
     */
    e2memocolor: string

    /**
     * e3_メモ
     */
    e3memoknj: string

    /**
     * e3_メモフォント
     */
    e3memofont: string

    /**
     * e3_メモ色
     */
    e3memocolor: string

    /**
     * e4_メモ
     */
    e4memoknj: string

    /**
     * e4_メモフォント
     */
    e4memofont: string

    /**
     * e4_メモ色
     */
    e4memocolor: string
  }
}

/**
 * アセスメントマスタ保存出力エンティティ
 */
export interface AssessmentInterRAIEUpdateOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * 計画対象期間ID
     */
    sc1Id: string
    /**
     * アセスメントID
     */
    raiId: string
    /**
     * エラー区分(1:e-文書の履歴保存のチェックエラー, 2:e文書出力エラー, 空：エラーなし)
     */
    errKbn: string
  }
}
