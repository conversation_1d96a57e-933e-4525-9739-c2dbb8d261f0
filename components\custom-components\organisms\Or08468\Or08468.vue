<script setup lang="ts">
/**
 * Or08468:現病歴・既往歴と経過
 * GUI01069_現病歴・既往歴と経過
 *
 * @description
 * 現病歴・既往歴と経過
 *
 * <AUTHOR> KMD DO DUC THANH
 */
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { UPDATE_KBN, DIALOG_BTN, SPACE_WAVE } from '~/constants/classification-constants'
import { Or10883Const } from '~/components/custom-components/organisms/Or10883/Or10883.constants'
import { CustomClass } from '~/types/CustomClassType'
import { Or08468Const } from '~/components/custom-components/organisms/Or08468/Or08468.constants'
import { Or27786Const } from '~/components/custom-components/organisms/Or27786/Or27786.constants'
import { Or27786Logic } from '~/components/custom-components/organisms/Or27786/Or27786.logic'
import type {
  Or08468Type,
  GamenDataInfo,
  Or08468OnewayType,
} from '~/types/cmn/business/components/Or08468Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { OrX0002OnewayType } from '~/types/cmn/business/components/OrX0002Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { useScreenTwoWayBind, useSetupChildProps } from '#imports'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { Or10883Logic } from '~/components/custom-components/organisms/Or10883/Or10883.logic'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Or06962OnewayType, Or06962Type } from '~/types/cmn/business/components/Or06962Type'
import type { Or06962SelectTableDataItem } from '~/components/custom-components/organisms/Or06962/Or06962.type.d'
import type { Or27786Type } from '~/types/cmn/business/components/Or27786Type'
import { dateUtils } from '~/utils/dateUtils'
const { convertSeirekiToWareki } = dateUtils()
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or08468OnewayType
}

const props = defineProps<Props>()

const defaultOneway = reactive({
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: !(props.onewayModelValue.isAllowEdit ?? true),
  } as Mo00009OnewayType,
})

const localOneway = reactive({
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    disabled: !(props.onewayModelValue.isAllowEdit ?? true),
    tooltipText: t('tooltip.care-plan2-newline-btn'),
  } as Mo00611OnewayType,
  mo00611OnewayInsert: {
    btnLabel: t('btn.insert-row'),
    prependIcon: 'add',
    disabled: !(props.onewayModelValue.isAllowEdit ?? true),
    tooltipText: t('tooltip.care-plan2-insertline-btn'),
  } as Mo00611OnewayType,
  mo00611OnewayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    disabled: !(props.onewayModelValue.isAllowEdit ?? true),
    tooltipText: t('tooltip.care-plan2-cpyline-btn'),
  } as Mo00611OnewayType,
  mo00611OnewayDelete: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    disabled: !(props.onewayModelValue.isAllowEdit ?? true),
    color: 'error',
    labelColor: 'error',
    tooltipText: t('tooltip.care-plan2-deleteline-btn'),
  } as Mo00611OnewayType,
  mo00009Oneway: {
    ...defaultOneway.mo00009Oneway,
  } as Mo00009OnewayType,
  // 行削除の確認ダイアログ
  orX0002DeleteLineOneway: {
    message: t('message.i-cmn-10219'),
  } as OrX0002OnewayType,
  // 表示順
  mo00615OnewayOrder: {
    itemLabel: t('label.display-order'),
  } as Mo00615OnewayType,
  mo00039Oneway: {
    name: '',
    itemLabel: '',
    showItemLabel: false,
    inline: true,
    disabled: !(props.onewayModelValue.isAllowEdit ?? true),
    customClass: new CustomClass({ outerClass: '', labelClass: '' }),
  } as Mo00039OnewayType,
  mo01343Oneway: {
    selectMode: '0',
    disabled: !(props.onewayModelValue.isAllowEdit ?? true),
  } as { selectMode: string; disabled: boolean },
  mo00020Oneway: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '125px',
  } as Mo00020OnewayType,
  mo01282Oneway: {
    items: [] as { label: string; value: string }[],
    itemTitle: 'label',
    itemValue: 'value',
  },
  mo00009EditBtn: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    class: 'icon-btn',
  },
  Or08468Oneway: {
    itakuKkakPrtFlg: '',
    operaFlg: '',
  } as Or06962OnewayType,
  or10883Oneway: {} as Or10883OnewayType,
  mo00611OnewayDisplayOrder: {
    btnLabel: t('label.display-order'),
    tooltipText: t('tooltip.display-order'),
    minWidth: '65px',
  },
})

const or10883 = ref({ uniqueCpId: '' })
const or27786 = ref({ uniqueCpId: '' })
const or27786Type = ref<Or27786Type>({
  preventionBasicList: [],
})
const onewayModelValueX0159 = {
  showKikanBtnFlg: false,
  showYmdBtnFlg: true,
  showMonthDayBtnFlg: false,
  showYmBtnFlg: true,
  disabled: false,
  readonly: false,
  customWidth: true,
}
const mo01338OnewaySelectionRow = ref({
  value: '',
  customClass: new CustomClass({
    itemClass: '',
  }),
})

const Or10883OnewayModel: Or10883OnewayType = {
  // 親画面.利用者ID
  userId: '',
  // 親画面.大分類ＣＤ
  t1Cd: '20',
  // 親画面.中分類ＣＤ
  t2Cd: '3',
  // 親画面.小分類ＣＤ
  t3Cd: '0',
  // 親画面.過去履歴用テーブル名
  tableName: 'kyc_tuc_khn133',
  // 項目名
  columnName: 'naiyo_knj',
  // 親画面.選択セール文章内容
  inputContents: '',
  // タイトル
  title: '',
  /**
   * 過去履歴用テーブル名
   */
  historyTableName: '',
  /**
   * 過去履歴用カラム名
   */
  historyColumnName: '',
}

const local = reactive({
  Or08468: {
    items: [] as Or06962SelectTableDataItem[],
    selectRowIndex: -1,
  } as Or06962Type,
  isAllowEdit: props.onewayModelValue.isAllowEdit ?? true,
  orX0002DeleteLineDialog: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
  or08468: { selectedYmdColKbn: '', ymdKbn: '' } as { selectedYmdColKbn: string; ymdKbn: string },
  centerIkenKnj: {
    value: props.onewayModelValue.shubetsuId,
  },
  mo01343: {
    value: '',
    endValue: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  } as Mo01343Type,
  attendingPhysicianOpinionAuthor: {} as Record<string, Mo00040Type>,
})

const selectedItemIndex = ref<number>(-1)
const or10883Type = ref<Or10883TwowayType>({})
const selectRow = ref<{ type: string; index: number }>({ type: '', index: 0 })
const columnMinWidth = ref<number[]>([48, 120, 162, 316, 117, 267, 295])
useSetupChildProps(props.uniqueCpId, {
  [Or10883Const.CP_ID(0)]: or10883.value,
  [Or27786Const.CP_ID(0)]: or27786.value,
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or08468Type>({
  cpId: 'Or08468',
  uniqueCpId: props.uniqueCpId,
})

const tableDataFilter = computed(() => {
  return (
    refValue.value?.dataList?.filter(
      (i: GamenDataInfo) => i.updateKbn2 !== UPDATE_KBN.DELETE && i.updateKbn3 !== UPDATE_KBN.DELETE
    ) ?? []
  )
})

const isShowDialogOr10883 = computed(() => {
  return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
})

// 一覧に表示レコードの表示順の再設定
const updateSeq = () => {
  tableDataFilter.value.forEach((item, index) => {
    const data = refValue.value?.dataList.find((i) => i.tableIndex === item.tableIndex)
    if (data) {
      // 新しいseq
      const newSeq = index + 1
      // 対象は未初期データ、未変更、seqが変更されたの場合、更新にする
      if (
        data.updateKbn2 === UPDATE_KBN.NONE &&
        data.updateKbn3 === UPDATE_KBN.NONE &&
        data.seq !== newSeq
      ) {
        data.updateKbn2 = UPDATE_KBN.UPDATE
      }
      // 対象のseqを設定
      data.seq = newSeq
    }
  })
}

// 行挿入,行複写,行削除の活性制御
const disableButtons = computed(() => {
  return tableDataFilter.value.length === 0 || selectedItemIndex.value < 0
})

/**
 * 期間範囲のフォーマット
 *
 * @param formatType - フォーマット種別
 *
 * @param startDate - 開始日
 *
 * @param endDate - 終了日
 *
 * @param keepYear - 年を保持するかどうか
 */
function getFormatRangeDate(
  formatType: string,
  startDate: string,
  endDate: string,
  keepYear: boolean
) {
  switch (formatType) {
    case '0':
      return formatDate(startDate, formatType, keepYear)
        .concat(SPACE_WAVE)
        .concat(formatDate(endDate, formatType, keepYear))
    case '1':
      return formatDate(startDate, formatType, keepYear)
        .concat(SPACE_WAVE)
        .concat(formatDate(endDate, formatType, keepYear))
    case '2':
      return formatDate(startDate, formatType, keepYear)
    case '3':
      return formatDate(startDate, formatType, keepYear)
    default:
      return startDate.concat(endDate)
  }
}

/**
 * 日付をフォーマット
 *
 * @param date - 日付
 *
 * @param formatType - フォーマット種別
 *
 * @param keepYear - 年を保持するかどうか
 */
function formatDate(date: string | undefined, formatType: string, keepYear = false): string {
  if (!date) {
    return ''
  }
  const [_year, month, day] = date.split('/')
  switch (formatType) {
    case '0':
    case '2':
      if (keepYear) {
        return date
      } else {
        return `${month}/${day}`
      }
    case '1':
    case '3':
      if (keepYear) {
        return convertSeirekiToWareki(date) ?? ''
      } else {
        return `${month}月${day}日`
      }
    default:
      return date
  }
}

const isShowDialogOr27786 = computed(() => {
  return Or27786Logic.state.get(or27786.value.uniqueCpId)?.isOpen ?? false
})

// 一覧の監視
watch(
  tableDataFilter,
  (newData) => {
    if (selectedItemIndex.value >= newData.length || !newData[selectedItemIndex.value]) {
      if (newData.length > 0) {
        selectedItemIndex.value = 0
      } else {
        selectedItemIndex.value = -1
      }
    }
    mo01338OnewaySelectionRow.value.value = newData.length + Or08468Const.COUNT
  },
  { immediate: true }
)

// 「行削除ボタン」押下メッセージの場合
watch(
  () => local.orX0002DeleteLineDialog.emitType,
  (value) => {
    if (value === DIALOG_BTN.OK) {
      if (selectedItemIndex.value < 0 || !refValue.value) return

      const tableIndex = tableDataFilter.value[selectedItemIndex.value].tableIndex
      const data = refValue.value.dataList.find((item) => item.tableIndex === tableIndex)

      if (data) {
        data.updateKbn2 = UPDATE_KBN.DELETE
        data.updateKbn3 = UPDATE_KBN.DELETE
      }

      const currentIndex = selectedItemIndex.value

      let newIndex: number
      if (tableDataFilter.value.length === 0) {
        newIndex = -1
      } else if (currentIndex >= tableDataFilter.value.length) {
        newIndex = tableDataFilter.value.length - 1
      } else {
        newIndex = currentIndex
      }
      selectedItemIndex.value = newIndex

      // 表示順再設定
      updateSeq()

      local.orX0002DeleteLineDialog.mo00024.isOpen = false
    }
  }
)
watch(
  () => or27786Type.value,
  () => {
    if (!refValue.value?.dataList) return
    refValue.value.dataList.forEach((item, index) => {
      if (item.updateKbn2 === UPDATE_KBN.DELETE || item.updateKbn3 === UPDATE_KBN.DELETE) return
      const data = or27786Type.value.preventionBasicList[index]
      if (!data || !item) return
      const passage = options584.value.find((e) => e.label === `${data.passage}`)?.value ?? ''
      item.kikanKnj = { value: `${data.date}` }
      item.kadaiKnj = { value: `${data.diseaseName}` }
      item.mokuhyoKnj = { value: `${data.hospitalDoctorFirstName}` }
      item.attendingPhysicianOpinionAuthor = data.attendingPhysicianOpinionAuthor ? true : false
      item.kadaiNo = { value: `${data.hospitalTel}` }
      item.passage = Number(passage)
      item.tantoShokuKnj = { value: `${data.therapyMedium}` }
    })
  }
)
onMounted(async () => {
  await initCodes()
  initData()
})

/**
 * 行選択
 *
 * @param index - 選択行インデックス
 */
function handleRowClick(index: number) {
  selectedItemIndex.value = index
}

// 行追加
function addRow() {
  const newRow: GamenDataInfo = {
    tableIndex: refValue.value?.dataList?.length ?? 0,
    kadaiKnj: { value: '' },
    mokuhyoKnj: { value: '' },
    kikanKnj: { value: '' },
    naiyoKnj: { value: '' },
    tantoShokuKnj: { value: '' },
    kadaiNo: { value: '' },
    id2: { value: '' },
    id3: { value: '' },
    modifiedCnt2: { value: '' },
    modifiedCnt3: { value: '' },
    updateKbn2: UPDATE_KBN.CREATE,
    updateKbn3: UPDATE_KBN.CREATE,
    seq: tableDataFilter.value.length + 1,
    passage: 0,
    attendingPhysicianOpinionAuthor: false,
  }
  refValue.value?.dataList.push(newRow)
  const newIndex = tableDataFilter.value.length - 1
  selectedItemIndex.value = newIndex
}

// 行挿入
function insertRow() {
  if (selectedItemIndex.value < 0 || !refValue.value) {
    return
  }

  const selectedItem = tableDataFilter.value[selectedItemIndex.value]
  const newRow: GamenDataInfo = {
    tableIndex: refValue.value?.dataList?.length ?? 0,
    kadaiKnj: { value: '' },
    mokuhyoKnj: { value: '' },
    kikanKnj: { value: '' },
    naiyoKnj: { value: '' },
    tantoShokuKnj: { value: '' },
    kadaiNo: { value: '' },
    id2: { value: '' },
    id3: { value: '' },
    modifiedCnt2: { value: '' },
    modifiedCnt3: { value: '' },
    updateKbn2: UPDATE_KBN.CREATE,
    updateKbn3: UPDATE_KBN.CREATE,
    seq: 0,
    passage: 0,
    attendingPhysicianOpinionAuthor: false,
  }

  const currentIndex = refValue.value.dataList.findIndex(
    (i) => i.tableIndex === selectedItem.tableIndex
  )
  if (currentIndex >= 0) {
    refValue.value.dataList.splice(currentIndex + 1, 0, newRow)
  } else {
    refValue.value.dataList.push(newRow)
  }

  updateSeq()

  const newIndex = tableDataFilter.value.findIndex((i) => i.tableIndex === newRow.tableIndex)
  if (newIndex >= 0) {
    selectedItemIndex.value = newIndex
  }
}

// 行複写
function copyRow() {
  if (selectedItemIndex.value < 0 || !refValue.value) {
    return
  }

  const selectedItem = tableDataFilter.value[selectedItemIndex.value]
  const sourceLine = refValue.value.dataList.find((i) => i.tableIndex === selectedItem.tableIndex)

  if (!sourceLine) return
  const clone = JSON.parse(JSON.stringify(sourceLine)) as GamenDataInfo

  const cpyLine: GamenDataInfo = {
    ...clone,
    tableIndex: refValue.value?.dataList?.length ?? 0,
    updateKbn2: UPDATE_KBN.CREATE,
    updateKbn3: UPDATE_KBN.CREATE,
    seq: 0,
  }

  const insertPosition =
    refValue.value.dataList.findIndex((i) => i.tableIndex === selectedItem.tableIndex) + 1
  refValue.value.dataList.splice(insertPosition, 0, cpyLine)

  updateSeq()

  const newIndex = tableDataFilter.value.findIndex((i) => i.tableIndex === cpyLine.tableIndex)
  if (newIndex >= 0) {
    selectedItemIndex.value = newIndex
  }
}

// 行削除
function deleteRow() {
  if (selectedItemIndex.value < 0) {
    return
  }

  local.orX0002DeleteLineDialog.mo00024.isOpen = true
}
function handleChangeAttendingPhysicianOpinionAuthor(index: number, item: GamenDataInfo) {
  if (index < 0 || !item) return
  if (
    !local.attendingPhysicianOpinionAuthor?.[index]?.modelValue ||
    local.attendingPhysicianOpinionAuthor[index].modelValue === '1'
  ) {
    tableDataFilter.value[index].attendingPhysicianOpinionAuthor = false
  } else {
    tableDataFilter.value[index].attendingPhysicianOpinionAuthor = true
  }
  handleChange(item)
}
/**
 * 更新区分を設定
 *
 * @param item - 対象データ
 */
function handleChange(item: GamenDataInfo) {
  if (item.updateKbn2 === UPDATE_KBN.NONE) {
    item.updateKbn2 = UPDATE_KBN.UPDATE
  }
  if (item.updateKbn3 === UPDATE_KBN.NONE) {
    item.updateKbn3 = UPDATE_KBN.UPDATE
  }
}

const options584 = ref<CodeType[]>([])
async function initCodes() {
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONTINUITY_WHISTLING },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ATTENDING_PHYSICIAN_OPINION_WRITER },
  ]
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  options584.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_CONTINUITY_WHISTLING)
  localOneway.mo01282Oneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ATTENDING_PHYSICIAN_OPINION_WRITER
  )
}
function initData() {
  tableDataFilter.value.forEach((item, index) => {
    if (!local.attendingPhysicianOpinionAuthor[index]) {
      local.attendingPhysicianOpinionAuthor[index] = { modelValue: '1' }
    }
    local.attendingPhysicianOpinionAuthor[index].modelValue = item.attendingPhysicianOpinionAuthor
      ? '2'
      : '1'
  })
}
async function openDialog(type: string, index: number, title = '') {
  selectRow.value = { type, index }
  Or10883OnewayModel.title = title
  await nextTick()
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 表示順
 */
async function openGUI01077() {
  or27786Type.value.preventionBasicList = []
  if (!refValue.value?.dataList) return
  refValue.value.dataList.forEach((item, index) => {
    if (item.updateKbn2 === UPDATE_KBN.DELETE || item.updateKbn3 === UPDATE_KBN.DELETE) return
    const passage = options584.value.find((e) => e.value === `${item.passage}`)?.label ?? ''
    or27786Type.value.preventionBasicList.push({
      displayOrder: `${item.tableIndex}`,
      date: item.kikanKnj.value ?? '',
      diseaseName: item.kadaiKnj.value ?? '',
      hospitalDoctorFirstName: item.mokuhyoKnj.value ?? '',
      attendingPhysicianOpinionAuthor: item.attendingPhysicianOpinionAuthor ? '☆' : '',
      hospitalTel: item.kadaiNo.value ?? '',
      passage: passage,
      therapyMedium: item.tantoShokuKnj.value ?? '',
      index: index,
    })
  })
  await nextTick()
  Or27786Logic.state.set({
    uniqueCpId: or27786.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// Or10883の戻り値を監視
watch(
  () => or10883Type.value,
  (newValue) => {
    const { type, index } = selectRow.value
    if (!selectRow.value.type || !newValue || !refValue.value?.dataList?.length) return
    const item = refValue.value.dataList[index]
    const target = item[type as keyof GamenDataInfo]

    if (target && typeof target === 'object' && 'value' in target) {
      if (newValue.confirmation === Or08468Const.APPEN_TEXT) {
        ;(target as { value: string }).value += `\n${newValue.naiyo}`
      } else {
        ;(target as { value: string }).value = `${newValue.naiyo}`
      }
    }
    Or10883Logic.state.set({
      uniqueCpId: or10883.value.uniqueCpId,
      state: { isOpen: false },
    })
  },
  { immediate: true }
)

/**
 * 共通部品カレンダー選択（年月日）ポップアップ画面での返回値を監視
 */
watch(
  () => [local.mo01343, local.mo01343.mo00024?.isOpen],
  ([newMo01343, newIsOpen], [_oldMo01343, oldIsOpen]) => {
    if (!oldIsOpen || newIsOpen! || !newMo01343) {
      return
    }

    if (selectedItemIndex.value < 0) {
      // 一覧で行が選択されていない場合
      return
    }

    // 日付を取得
    const retValue = newMo01343 as Mo01343Type
    if (retValue.value) {
      const tableIndex = tableDataFilter.value[selectedItemIndex.value].tableIndex
      const data = refValue.value!.dataList.find((item) => item.tableIndex === tableIndex)

      if (data) {
        let value = ''
        if (local.or08468.ymdKbn === '2') {
          const date = new Date(retValue.value)
          const formatter = new Intl.DateTimeFormat('ja-JP-u-ca-japanese', {
            year: 'numeric',
            month: '2-digit',
          })
          value = formatter.format(date)
        } else {
          value = getFormatRangeDate(
            '1',
            retValue.value,
            retValue.endValue,
            local.or08468.ymdKbn === '1'
          )
        }
        data.kikanKnj.value = value
      }
    }
  }
)

// UI上で各行の選択状態を保持するフラグ配列
const uiSelectedFlags = ref<boolean[]>([])

// 「全選択」状態かどうかを判定（全ての行が選択されている場合 true）
const uiAllChecked = computed(
  () => uiSelectedFlags.value.length > 0 && uiSelectedFlags.value.every(Boolean)
)

// 一部だけ選択されている場合に中間状態（インデターミネート）を判定
const uiIndeterminate = computed(() => !uiAllChecked.value && uiSelectedFlags.value.some((v) => v))

// リストが変更されたときにフラグ配列を同期する（新しい行は false で初期化）
watch(
  tableDataFilter,
  (rows) => {
    const prev = uiSelectedFlags.value
    uiSelectedFlags.value = rows.map((_, i) => prev[i] ?? false)
  },
  { immediate: true }
)

// ヘッダのチェックボックスをクリックしたときに全ての行を一括で選択／解除する
function onHeaderCheckboxChange(val: boolean) {
  uiSelectedFlags.value = uiSelectedFlags.value.map(() => !!val)
}

// 各行のチェックボックスをクリックしたときにその行だけ選択状態を切り替える
function onRowCheckboxChange(index: number, val: boolean) {
  uiSelectedFlags.value[index] = !!val
}
</script>

<template>
  <div class="content-container mt-4">
    <c-v-row
      no-gutters
      class="btn-group-class"
    >
      <c-v-col class="btn-left-group">
        <!-- 行追加ボタン -->
        <base-mo00611
          v-if="local.isAllowEdit"
          :oneway-model-value="localOneway.mo00611OnewayAdd"
          @click="addRow"
        />

        <!-- 行挿入ボタン -->
        <base-mo00611
          v-if="local.isAllowEdit"
          :oneway-model-value="localOneway.mo00611OnewayInsert"
          :disabled="disableButtons"
          @click="insertRow"
        />

        <!-- 行複写ボタン -->
        <base-mo00611
          v-if="local.isAllowEdit"
          :oneway-model-value="localOneway.mo00611OnewayCopy"
          :disabled="disableButtons"
          @click="copyRow"
        />

        <!-- 行削除ボタン -->
        <base-mo00611
          v-if="local.isAllowEdit"
          :oneway-model-value="localOneway.mo00611OnewayDelete"
          :disabled="disableButtons"
          @click="deleteRow"
        />
      </c-v-col>

      <c-v-col cols="auto">
        <c-v-row no-gutters>
          <div class="flex flex-row content-center">
            <base-mo00611
              :oneway-model-value="localOneway.mo00611OnewayDisplayOrder"
              class="mr-2"
              @click="openGUI01077"
            />
            <base-mo01338
              :oneway-model-value="mo01338OnewaySelectionRow"
              class="d-flex align-center content-center"
            />
          </div>
        </c-v-row>
      </c-v-col>
    </c-v-row>

    <c-v-row
      no-gutters
      class="middleContent flex-1-1 h-100 justify-center"
    >
      <c-v-col
        cols="auto"
        class="d-flex pl-0 pt-2 h-100 w-100"
      >
        <c-v-data-table
          v-resizable-grid="{ columnWidths: columnMinWidth }"
          class="table-wrapper"
          :items="tableDataFilter"
          :hide-default-footer="true"
          :items-per-page="-1"
          fixed-header
          hide-no-data
        >
          <!-- ヘッダ -->
          <template #headers>
            <tr>
              <!-- チェックボックス (Select All) -->
              <th>
                <div class="checkbox-class">
                  <base-at-checkbox
                    :model-value="uiAllChecked"
                    :indeterminate="uiIndeterminate"
                    @update:model-value="onHeaderCheckboxChange"
                  />
                </div>
              </th>
              <!-- 年月日 -->
              <th>
                <div class="title-btn-class">
                  <div>{{ t('label.yyyy-mm-dd') }}</div>
                </div>
              </th>
              <!-- 月日 -->
              <th>
                <div class="title-btn-class">
                  <div>{{ t('label.disease-name') }}</div>
                </div>
              </th>
              <th>
                <div class="title-btn-class">
                  <div>{{ t('label.name-of-medical-institution-and-doctor') }}</div>
                </div>
              </th>
              <th>
                <div class="title-btn-class">
                  <div>{{ t('label.contact-address') }}</div>
                </div>
              </th>
              <th>
                <div class="title-btn-class">
                  <div>{{ t('label.passage') }}</div>
                </div>
              </th>
              <th>
                <div class="title-btn-class">
                  <div>{{ t('label.therapy-medium') }}</div>
                </div>
              </th>
            </tr>
          </template>

          <!-- Rows -->
          <template #item="{ item, index }">
            <tr
              :class="{ 'select-row': selectedItemIndex === index }"
              style="height: 80px"
              @click="handleRowClick(index)"
            >
              <!-- チェックボックス (Row) -->
              <td>
                <div
                  class="checkbox-class"
                  @click.stop
                >
                  <base-at-checkbox
                    :model-value="uiSelectedFlags[index]"
                    @update:model-value="(v: any) => onRowCheckboxChange(index, v)"
                  />
                </div>
              </td>

              <!--目標期間(達成時期)テキストエリア-->
              <td>
                <div class="pl-2">
                  <g-custom-or-x0159
                    :key="`box-date-time-${index}`"
                    v-model="item.kikanKnj"
                    :oneway-model-value="onewayModelValueX0159"
                  />
                </div>
              </td>

              <!--具体的な課題テキストエリア-->
              <td>
                <div class="edit-col">
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009EditBtn"
                    class="icon-edit-btn"
                    @click="openDialog('kadaiKnj', index, t('label.disease-name'))"
                  />
                  <base-mo01280
                    v-model="item.kadaiKnj"
                    max-length="32000"
                    :readonly="localOneway.Or08468Oneway.isCopyMode"
                    style="padding: 0px !important; margin-left: 8px !important"
                    @change="handleChange(item)"
                  />
                </div>
              </td>

              <!--生活目標テキストエリア-->
              <td>
                <div class="flex flex-row justify-between content-center mr-4">
                  <div>
                    <base-mo01280
                      v-if="local.isAllowEdit"
                      v-model="item.mokuhyoKnj"
                      max-length="32000"
                      @change="handleChange(item)"
                    />
                  </div>
                  <div>
                    <base-mo01282
                      v-model="local.attendingPhysicianOpinionAuthor[index]"
                      :oneway-model-value="localOneway.mo01282Oneway"
                      @change="handleChangeAttendingPhysicianOpinionAuthor(index, item)"
                    />
                  </div>
                </div>
              </td>

              <td>
                <base-mo01274 v-model="item.kadaiNo" />
              </td>

              <!--処遇の内容テキストエリア-->
              <td>
                <base-mo00039
                  v-if="local.isAllowEdit"
                  v-model="tableDataFilter[index].passage"
                  :oneway-model-value="localOneway.mo00039Oneway"
                >
                  <base-at-radio
                    v-for="itemSelect in options584"
                    :key="itemSelect"
                    :name="'radio-' + itemSelect"
                    :radio-label="itemSelect.label"
                    :value="itemSelect.value"
                  />
                </base-mo00039>
              </td>

              <!--担当者テキストエリア-->
              <td>
                <div class="edit-col">
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009EditBtn"
                    class="icon-edit-btn"
                    @click="openDialog('tantoShokuKnj', index, t('label.therapy-medium'))"
                  />
                  <base-mo01280
                    v-model="item.tantoShokuKnj"
                    max-length="32000"
                    :readonly="localOneway.Or08468Oneway.isCopyMode"
                    style="padding: 0px !important; margin-left: 8px !important"
                    @change="handleChange(item)"
                  />
                </div>
              </td>
            </tr>
          </template>
        </c-v-data-table>
      </c-v-col>
    </c-v-row>
  </div>

  <!-- 「行削除ボタン」押下のメッセージ -->
  <g-custom-or-x-0002
    v-model="local.orX0002DeleteLineDialog"
    :oneway-model-value="localOneway.orX0002DeleteLineOneway"
  ></g-custom-or-x-0002>

  <!-- 年月選択 -->
  <base-mo01343
    v-model="local.mo01343"
    :oneway-model-value="localOneway.mo01343Oneway"
  />
  <g-custom-or-10883
    v-if="isShowDialogOr10883"
    v-bind="or10883"
    v-model="or10883Type"
    :oneway-model-value="Or10883OnewayModel"
  />
  <g-custom-or-27786
    v-if="isShowDialogOr27786"
    v-bind="or27786"
    v-model="or27786Type"
  />
</template>

<style scoped lang="scss">
.select-row {
  background: rgb(var(--v-theme-blue-100)) !important;
  .v-field--appended {
    background: rgb(var(--v-theme-blue-100)) !important;
  }
}
.table-kikan-content {
  padding: 0 !important;
}

.btn-area-class {
  justify-content: space-between;
  align-items: baseline;

  .btn-group-class {
    display: flex;
    gap: 8px;
  }
}

.divider-class {
  border-width: thin;
  margin-bottom: 8px;
  margin-top: 8px;
}

:deep(.table-wrapper) {
  background-color: transparent;
  width: 100%;
}

.table-wrapper :deep(.v-table__wrapper) {
  overflow-y: auto;

  > table > tbody {
    background-color: rgb(var(--v-theme-surface));
  }
}

.table-wrapper :deep(.v-table__wrapper th) {
  background-color: #DBEEFE !important;
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  font-weight: bold;
  white-space: break-spaces;
}

.table-wrapper .v-table__wrapper td {
  padding: 0px !important;
}

.table-wrapper .v-table__wrapper td {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  padding: 0;
  font-size: 14px;
}

:deep(.v-table--fixed-header > .v-table__wrapper > table > thead) {
  position: sticky;
  top: 0.5px !important;
  z-index: 2;
}

.title-btn-class {
  display: flex;
  background-color: #DBEEFE !important;
  justify-content: space-between;
  align-items: center;
  font-weight: normal !important;
}
.middleContent {
  min-height: 0;
}
.width-container {
  width: 960px;
  margin: 0 auto;
}

:deep(.v-sheet) {
  background-color: transparent !important;
}

.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
  flex-direction: row;
  flex-wrap: nowrap;
}
.justify-end {
  margin-left: auto;
}
.btn-col-spacing {
  margin-right: 6px;
}
.btn-col-spacing:last-child {
  margin-right: 0;
}

.btn-group-class {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 8px;
}

.btn-left-group {
  display: flex;
  gap: 6px;
  align-items: center;
}
.flex {
  display: flex;
}
.flex-row {
  flex-direction: row;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.content-center {
  align-content: center;
}
.edit-col {
  display: flex;
  flex-direction: row;
  padding: 0px 8px;
  align-items: center;
}
textarea {
  align-content: center;
}
.icon-btn {
  width: '24px';
  height: '24px';
  min-width: '24px';
  min-height: '24px';
  color: #a7bada;
}
th,
td {
  padding: 0 0 0 8px !important;
}

.checkbox-class {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(th:first-child),
:deep(td:first-child) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  padding: 0 !important;
}

:deep(.v-data-table) {
  th:first-child,
  td:first-child {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
  }
}
</style>
