<script setup lang="ts">
/**
 * Or53926：有機体：(備考) ケース情報表入力
 *
 * @description
 * (備考) ケース情報表入力
 *
 * <AUTHOR>
 */
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or53928Const } from '../Or53928/Or53928.constants'
import { Or53926Const } from './Or53926.constants'
import type {
  Or53926EventType,
  Or53926ItemsType,
  Or53926StateType,
  Or53926TransmitParam,
} from './Or53926.type'
import { useSystemCommonsStore } from '#imports'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21833Const } from '~/components/base-components/organisms/Or21833/Or21833.constants'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { useCommonProps } from '~/composables/useCommonProps'
import {
  useScreenEventStatus,
  useScreenOneWayBind,
  useSetupChildProps,
} from '~/composables/useComponentVue'
import { DIALOG_BTN, UPDATE_KBN } from '~/constants/classification-constants'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'
import type { CareInfo } from '~/repositories/cmn/entities/PlanImplementationAchievementsRegistEntity'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import type { Or26257OnewayType, Or26257Type } from '~/types/cmn/business/components/Or26257Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { OrX0161OnewayType, OrX0161Type } from '~/types/cmn/business/components/OrX0161Type'
import type { OrX0162OnewayType } from '~/types/cmn/business/components/OrX0162Type'
import { useColorUtils } from '~/utils/useColorUtils'
import { useValidation } from '~/utils/useValidation'
const { convertDecimalToHex } = useColorUtils()
const validation = useValidation()

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())

/**************************************************
 * 変数定義
 **************************************************/
const or21814 = ref({ uniqueCpId: '' }) // 確認ダイアログ
const or21833 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })

// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()
/**
 * 分子：表
 * 単方向バインドモデルのローカル変数
 */
const mo01354Oneway = ref<Mo01354OnewayType>({
  /** 初期値：ヘッダー情報 */
  headers: [
    {
      title: t('label.date'),
      key: 'date',
      minWidth: '132',
      sortable: true,
      required: true,
      sortRaw(a: Or53926ItemsType, b: Or53926ItemsType) {
        // カスタムソートロジック
        if (!a || !b) {
          return 0
        } else {
          return a.date.value.localeCompare(b.date.value)
        }
      },
    },
    {
      title: t('label.time'),
      key: 'time',
      minminWidth: '91',
      sortable: true,
      required: true,
      sortRaw(a: Or53926ItemsType, b: Or53926ItemsType) {
        // カスタムソートロジック
        if (!a || !b) {
          return 0
        } else {
          return a.time.value.localeCompare(b.time.value)
        }
      },
    },
    {
      title: t('label.case-type'),
      key: 'type',
      minWidth: '106',
      sortable: true,
      sortRaw(a: Or53926ItemsType, b: Or53926ItemsType) {
        // カスタムソートロジック
        if (!a || !b) {
          return 0
        } else {
          return a.type.modelValue.localeCompare(b.type.modelValue)
        }
      },
    },
    {
      title: t('label.base-case'),
      key: 'contents',
      minWidth: '403',
      sortable: true,
      sortRaw(a: Or53926ItemsType, b: Or53926ItemsType) {
        // カスタムソートロジック
        if (!a || !b) {
          return 0
        } else {
          return a.contents.value.localeCompare(b.contents.value)
        }
      },
    },
    {
      title: t('label.recorder'),
      key: 'recorder',
      minWidth: '160',
      sortable: true,
      sortRaw(a: Or53926ItemsType, b: Or53926ItemsType) {
        // カスタムソートロジック
        if (!a || !b) {
          return 0
        } else {
          return a.recorder.value.localeCompare(b.recorder.value)
        }
      },
    },
    {
      title: t('label.comment') + t('label.applicable'),
      key: 'commentApplicable',
      minWidth: '130',
      sortable: false,
    },
    {
      title: t('label.image'),
      key: 'image',
      minWidth: '95',
      sortable: false,
    },
  ] as Mo01354Headers[],
  height: Or53926Const.DEFAULT.TABLE_HEIGHT,
  showSelect: false,
  selectStrategy: 'single',
  // Mo01354 表コンポーネントが提供する、デフォルトのヘッダーを利用しない（カスタマイズ可能になる）
  columnMinWidth: {
    columnWidths: Or53926Const.DEFAULT.COLUMN_WIDTHS,
  } as ResizableGridBinding,
})

/** テーブル情報 */
const tableData = ref({
  values: {
    items: [] as Or53926ItemsType[],
    selectedRowId: '',
  },
})

/** テーブルIndex */
const tableIndex = ref<number>(-1)

const currentItem = computed(() => {
  return tableData.value.values.items[tableIndex.value]
})

// 削除予定の項目
const deletedItem = ref<Or53926ItemsType[]>([])

const mo01282Oneway = ref<Mo01282OnewayType>({
  items: [],
  itemTitle: 'title',
  itemValue: 'value',
  width: '100%',
  bgColor: 'none',
})

const currentFlag = ref<boolean>(false)
const current = ref({
  date: '',
  time: '',
  type: '',
  contents: '',
})

// データ部
/** ケース */
const or51775Type = ref({
  modelValue: '',
})
const orX0161Oneway = ref<OrX0161OnewayType>({
  hideDetails: 'auto',
  rules: [validation.required],
})
const orX0162Oneway = ref<OrX0162OnewayType>({
  selectionMode: false,
})
const caseCheckBoxOneway = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.base-case'),
  showItemLabel: false,
})
const handoverCheckBoxOneway = ref<Mo00018OnewayType>({
  checkboxLabel: t('label.handover'),
  showItemLabel: false,
})

const localOneway = reactive({
  param: {} as Or53926TransmitParam,
  // 行追加ボタン
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    tooltipText: t('tooltip.add-row'),
  } as Mo00611OnewayType,
  // 行複写ボタン
  mo00611OnewayDuplicate: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'content_copy',
    tooltipText: t('tooltip.duplicate-row'),
  } as Mo00611OnewayType,
  // 行削除ボタン
  mo00611OnewayDelete: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    color: 'error',
    labelColor: 'error',
    tooltipText: t('tooltip.delete-row'),
  } as Mo00611OnewayType,
  // ケースデータ件数
  mo00615CaseDataCountOneway: {
    itemLabel: '',
    showItemLabel: true,
    customClass: { outerClass: '' },
  } as Mo00615OnewayType,
  // アイコンボタン
  mo00009Oneway: {
    minWidth: '24px',
    minHeight: '24px',
    width: '24px',
    height: '24px',
    icon: true,
    btnIcon: 'edit_square',
    color: 'rgb(var(--v-theme-sub))',
  } as Mo00009OnewayType,
  /**
   * GUI00937_入力支援［ケアマネ］
   */
  or51775Oneway: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
  or26257Oneway: {
    // システム略称
    sysCdKbn: Or53926Const.DEFAULT.SYS_RYAKU_3GK,
    // アカウント設定
    secAccountAllFlg: '',
    // 適用事業所ＩＤリスト
    svJigyoIdList: [{ svJigyoId: '' }],
    // 職員ID
    shokuinId: '',
    // システムコード
    gsysCd: '',
    // モード
    selectMode: '',
    // 基準日
    kijunYmd: '',
    // 事業所ID
    defSvJigyoId: '',
    // フィルターフラグ
    filterDwFlg: '',
    // 雇用状態
    koyouState: '',
    // 地域フラグ
    areaFlg: '',
    // 表示名称リスト
    hyoujiColumnList: [
      { hyoujiColumn: Or53926Const.DEFAULT.STR.ONE },
      { hyoujiColumn: Or53926Const.DEFAULT.STR.TWO },
      { hyoujiColumn: Or53926Const.DEFAULT.STR.THREE },
    ],
    // 未設定フラグ
    misetteiFlg: '',
    // 他職員参照権限
    otherRead: '',
    // 中止フラグ
    refStopFlg: '',
    // 処理フラグ
    syoriFlg: '',
    // メニュー１ID
    menu1Id: '',
    // 件数フラグ
    kensuFlg: '',
  } as Or26257OnewayType,
})
const local = reactive({
  or26257: { shokuin: { chkShokuId: '' } } as Or26257Type,
})

/**************************************************
 * Pinia
 **************************************************/
/** OneWayBind管理用共通処理 */
useScreenOneWayBind<Or53926StateType>({
  cpId: Or53926Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (!value) {
        return
      }
      localOneway.param = value
      setTableData()
    },
  },
})

// ■共通処理の登録権限チェックを行う
// const editFlg: boolean = await hasRegistAuth()

/** EventSatatus管理用共通処理 */
const { setEvent } = useScreenEventStatus<Or53926EventType>({
  cpId: Or53926Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value, // 確認ダイアログ
  [Or21833Const.CP_ID]: or21833.value,
  [Or26257Const.CP_ID(1)]: or26257.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
})

/**
 * 職員検索ポップアップの表示状態を返すComputed
 */
const showDialogOr26257 = computed(() => {
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 選択行クラス付与関数
 *
 * @param row - 行情報
 */
const getItemClass = (row: { item: { id: string; kanryouFlg: string } }) => {
  // 該当行.完了フラグ = 1の場合
  if (row.item.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1) {
    return tableData.value.values.selectedRowId === row.item.id
      ? { class: { 'row-selected-style': true } }
      : { class: { 'row-unselected-style': true } }
  }
  // 該当行.完了フラグ <> 1
  else {
    // 選択行の場合「selected-row」のクラスを付与
    return tableData.value.values.selectedRowId === row.item.id
      ? { class: { 'selected-row': true } }
      : { class: {} }
  }
}

/**
 * 「種別セレクト」クリック
 *
 * @param item - 該当行
 */
const onTypeFocus = (item: Or53926ItemsType) => {
  current.value = {
    date: item.date.value,
    time: item.time.value,
    type: item.type.modelValue,
    contents: item.contents.value,
  }
}

/**
 * 種別セレクトを設定
 *
 * @param item - 該当行
 */
const onTypeChange = async (item: Or53926ItemsType) => {
  // 該当行.完了フラグ = 1の場合
  if (item.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1) {
    // i.cmn.11287
    // 該当行は、老健カルテの対応結果として関連付いており、[改行]
    // その指示が完了となっているため変更できません。
    const dialogResult = await openConfirmDialog1(t('message.i-cmn-11287'))
    switch (dialogResult) {
      case DIALOG_BTN.OK:
        Or21814Logic.state.set({
          uniqueCpId: or21814.value.uniqueCpId,
          state: {
            isOpen: false,
          },
        })
        // OK：処理終了、値を元に戻す。
        item.type.modelValue = current.value.type
        current.value.type = ''
        return
    }
  }
  // 該当行.完了フラグ <> 1
  else {
    const careKind = localOneway.param.careKindList?.find(
      (data) => data.caseCd === item.type.modelValue
    )
    const color = careKind?.color ?? Or53926Const.DEFAULT.STR.ZERO
    item.color = convertDecimalToHex(parseInt(color, 10))
    if (item.updateKbn === UPDATE_KBN.NONE) {
      item.updateKbn = UPDATE_KBN.UPDATE
    }
    setCareInfoList()
  }
}

/**
 *  画面初期情報取得
 */
function setTableData() {
  // 「ケース種別」の選択肢の更新
  if (localOneway.param.careKindList) {
    mo01282Oneway.value.items = []
    for (const item of localOneway.param.careKindList) {
      mo01282Oneway.value.items?.push({
        title: item.shubetuKnj,
        value: item.caseCd,
      })
    }
  }
  deletedItem.value = []
  const list: Or53926ItemsType[] = []
  tableData.value.values.items.splice(0)
  const count = localOneway.param?.careList?.length ?? Or53926Const.DEFAULT.ZERO
  localOneway.mo00615CaseDataCountOneway.itemLabel = count + t('label.item')
  if (localOneway.param?.careList && count > Or53926Const.DEFAULT.ZERO) {
    localOneway.param.careList.forEach((data, index) => {
      let image
      if (data.computeGazouId && Number(data.computeGazouId) > Or53926Const.DEFAULT.ZERO) {
        image = {
          prependIcon: 'check',
          color: 'rgb(var(--v-theme-success))',
          labelColor: 'rgb(var(--v-theme-text))',
          btnLabel: t('label.having'),
          class: 'icon-style1',
        }
      } else {
        image = {
          prependIcon: 'close',
          color: 'rgb(var(--v-theme-subText))',
          labelColor: 'rgb(var(--v-theme-text))',
          btnLabel: t('label.none'),
          class: 'icon-style2',
        }
      }
      const careKind = localOneway.param.careKindList?.find((item) => item.caseCd === data.caseCd)
      const color = careKind?.color ?? Or53926Const.DEFAULT.STR.ZERO
      const item = {
        ...data,
        id: index + 1 + '',
        bikoKbn: data.bikoKbn,
        date: { value: data.yymmYmd },
        time: { value: data.timeHh + Or53926Const.DEFAULT.STR.SPLIT_COLON + data.timeMm },
        type: { modelValue: data.caseCd },
        contents: { value: data.caseKnj },
        color: convertDecimalToHex(parseInt(color, 10)),
        recorder: { value: data.staffName, unit: '' },
        isCase: { modelValue: data.caseFlg === Or53926Const.DEFAULT.STR.ZERO ? false : true },
        handOver: {
          modelValue: data.moushiokuriFlg === Or53926Const.DEFAULT.STR.ZERO ? false : true,
        },
        image: image,
        staffid: data.staffid,
        uniqueId: data.uniqueId,
        ks21Id: data.ks21Id,
        ks22Id: data.ks22Id,
        kjisshi2Id: data.kjisshi2Id,
        recNo: data.recNo,
        kanryouFlg: data.kanryouFlg,
        baseUniqueId: data.baseUniqueId,
        computeGazouId: data.computeGazouId,
        problemId: data.problemId,
        updateKbn: UPDATE_KBN.NONE,
        casebikoModifiedCnt: data.casebikoModifiedCnt,
      } as Or53926ItemsType
      list.push(item)
    })
    tableData.value.values.items = list
    // 一行目を選択状態にする
    tableData.value.values.selectedRowId = tableData.value.values.items[0].id
    mo01354Oneway.value.rowProps = getItemClass
  }
}

/** ケースリスト（保存用）を設定 */
function setCareInfoList() {
  const list = [] as CareInfo[]
  const tempArray = [...tableData.value.values.items, ...deletedItem.value] as Or53926ItemsType[]
  for (const data of tempArray) {
    const time = data.time.value?.split(Or53926Const.DEFAULT.STR.SPLIT_COLON)
    const item = {
      bikoKbn: data.bikoKbn,
      yymmYmd: data.date.value,
      timeHh: time[0],
      timeMm: time[1],
      caseCd: data.type.modelValue,
      caseKnj: data.contents.value,
      staffid: data.staffid,
      staffName: data.recorder.value,
      caseFlg: data.isCase.modelValue
        ? Or53926Const.DEFAULT.STR.ONE
        : Or53926Const.DEFAULT.STR.ZERO,
      moushiokuriFlg: data.handOver.modelValue
        ? Or53926Const.DEFAULT.STR.ONE
        : Or53926Const.DEFAULT.STR.ZERO,
      uniqueId: data.uniqueId,
      ks21Id: data.ks21Id,
      ks22Id: data.ks22Id,
      kjisshi2Id: data.kjisshi2Id,
      recNo: data.recNo,
      kanryouFlg: data.kanryouFlg,
      baseUniqueId: data.baseUniqueId,
      computeGazouId: data.computeGazouId,
      problemId: data.problemId,
      updateKbn: data.updateKbn,
      casebikoModifiedCnt: data.casebikoModifiedCnt ?? Or53926Const.DEFAULT.STR.ZERO,
    } as CareInfo
    list.push(item)
  }
  setEvent({
    editFlg: true,
    careInfoList: list,
  })
}

/**
 * 「行追加」ボタン押下
 */
function createRow() {
  // テーブルデータ長さを取得
  const currentLength = tableData.value.values.items.length
  let maxId = Or53926Const.DEFAULT.ZERO
  if (currentLength > Or53926Const.DEFAULT.ZERO) {
    maxId = Number(tableData.value.values.items[currentLength - 1].id)
  }
  const image = {
    prependIcon: 'close',
    color: 'rgb(var(--v-theme-subText))',
    labelColor: 'rgb(var(--v-theme-text))',
    btnLabel: t('label.none'),
    class: 'icon-style2',
  }
  const newItem = {
    id: maxId + 1 + '',
    date: { value: localOneway.param.yymmYmd },
    time: { value: Or53926Const.DEFAULT.STR.TIME },
    type: { modelValue: '' },
    contents: { value: '' },
    color: convertDecimalToHex(0),
    staffid: systemCommonsStore.getCurrentUser.chkShokuId,
    recorder: { value: systemCommonsStore.getCurrentUser.shokuinKnj, unit: '' },
    isCase: { modelValue: false },
    handOver: {
      modelValue: localOneway.param.mouTenkiFlg === Or53926Const.DEFAULT.STR.ZERO ? false : true,
    },
    image: image,
    ks21Id: localOneway.param.ks21Id,
    ks22Id: localOneway.param.ks22Id,
    kjisshi2Id: localOneway.param.kjisshi2Id,
    computeGazouId: Or53926Const.DEFAULT.STR.ZERO,
    updateKbn: UPDATE_KBN.CREATE,
  } as Or53926ItemsType
  tableData.value.values.items.push(newItem)
  // ■追加行を選択状態とする
  tableData.value.values.selectedRowId = newItem.id
  onScrollIntoView(newItem.id)
  // ■該当行の行数により、画面.ケースデータ件数を変更
  localOneway.mo00615CaseDataCountOneway.itemLabel =
    tableData.value.values.items.length + t('label.item')
  setCareInfoList()
}

/**
 * 「行複写」ボタン押下
 */
function copyRow() {
  // テーブルデータ長さを取得
  const currentLength = tableData.value.values.items.length
  if (currentLength === Or53926Const.DEFAULT.ZERO) {
    return
  }
  const maxId = Number(tableData.value.values.items[currentLength - 1].id)
  const selectedItem = tableData.value.values.items.find(
    (item) => item.id === tableData.value.values.selectedRowId
  )
  const newItem = {
    id: maxId + 1 + '',
    date: selectedItem?.date,
    time: selectedItem?.time,
    type: selectedItem?.type,
    contents: selectedItem?.contents,
    color: selectedItem?.color,
    recorder: selectedItem?.recorder,
    isCase: selectedItem?.isCase,
    handOver: selectedItem?.handOver,
    image: selectedItem?.image,
    staffid: selectedItem?.staffid,
    ks21Id: selectedItem?.ks21Id,
    ks22Id: selectedItem?.ks22Id,
    kjisshi2Id: selectedItem?.kjisshi2Id,
    recNo: Or53926Const.DEFAULT.STR.ZERO,
    kanryouFlg: Or53926Const.DEFAULT.STR.ZERO,
    baseUniqueId: undefined,
    computeGazouId: Or53926Const.DEFAULT.STR.ZERO,
    problemId: Or53926Const.DEFAULT.STR.ZERO,
    updateKbn: UPDATE_KBN.CREATE,
  } as Or53926ItemsType
  tableData.value.values.items.push(newItem)
  // ■コピー先の行を選択状態とする
  tableData.value.values.selectedRowId = newItem.id
  onScrollIntoView(newItem.id)
  // ■該当行の行数により、画面.ケースデータ件数を変更
  localOneway.mo00615CaseDataCountOneway.itemLabel =
    tableData.value.values.items.length + t('label.item')
  setCareInfoList()
}

/**
 * 「行削除」ボタン押下
 */
async function deleteRow() {
  // テーブルデータ長さを取得
  const currentLength = tableData.value.values.items.length
  if (currentLength === Or53926Const.DEFAULT.ZERO) {
    return
  }
  const selectedItem = tableData.value.values.items.find(
    (item) => item.id === tableData.value.values.selectedRowId
  ) as Or53926ItemsType
  if (!selectedItem) {
    return
  }
  // 該当行の完了フラグ=１の場合、■以下のメッセージを表示
  if (selectedItem.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1) {
    // i.cmn.11286
    // 選択行は老健カルテの対応結果として関連付いており、[改行]
    // その指示が完了となっているため削除できません。
    const dialogResult = await openConfirmDialog1(t('message.i-cmn-11286'))
    switch (dialogResult) {
      case DIALOG_BTN.OK:
        Or21814Logic.state.set({
          uniqueCpId: or21814.value.uniqueCpId,
          state: {
            isOpen: false,
          },
        })
        // OK：処理終了。
        return
    }
  }
  // 上記以外の場合、■以下のメッセージを表示
  else {
    // i.cmn.10219
    // 現在行のデータを削除します。[改行]
    // よろしいですか。
    const dialogResult = await openConfirmDialog2(t('message.i-cmn-10219'))
    switch (dialogResult) {
      case DIALOG_BTN.YES:
        // はい：処理続き
        break
      case DIALOG_BTN.NO:
        // いいえ：選択行を削除せず、処理を終了する。
        return
    }
    // ①選択された行のユニークIDの値がある場合、
    if (selectedItem.uniqueId) {
      // 選択された行の更新区分が'U'の場合、画面項目の値を初期値に再設定
      if (selectedItem.updateKbn === UPDATE_KBN.UPDATE) {
        selectedItem.date.value = selectedItem?.yymmYmd
        selectedItem.time.value =
          selectedItem.timeHh + Or53926Const.DEFAULT.STR.SPLIT_COLON + selectedItem.timeMm
        selectedItem.type.modelValue = selectedItem.caseCd
        selectedItem.contents.value = selectedItem.caseKnj
        selectedItem.recorder.value = selectedItem.staffName
        selectedItem.isCase.modelValue =
          selectedItem.caseFlg === Or53926Const.DEFAULT.STR.ZERO ? false : true
        selectedItem.handOver.modelValue =
          selectedItem.moushiokuriFlg === Or53926Const.DEFAULT.STR.ZERO ? false : true
      }
      // ・選択された行の更新区分を'D'で設定する。
      selectedItem.updateKbn = UPDATE_KBN.DELETE
      // ・画面に非表示する。
      deletedItem.value.push(selectedItem)
    }
    // ②選択された行のユニークIDの値がない場合、
    else {
      // ・当該行を廃棄する。
    }
    for (let i = tableData.value.values.items.length - 1; i >= 0; i--) {
      if (tableData.value.values.items[i].id === selectedItem.id) {
        tableData.value.values.items.splice(i, 1)
        if (currentLength === Or53926Const.DEFAULT.ONE) {
          break
        } else {
          if (i === Or53926Const.DEFAULT.ZERO) {
            // 一行目を選択状態にする
            tableData.value.values.selectedRowId = tableData.value.values.items[0].id
          } else {
            // 削除行の上の行目を選択する
            tableData.value.values.selectedRowId = tableData.value.values.items[i - 1].id
          }
        }
        break
      }
    }
    // ③該当行の行数により、画面.ケースデータ件数を変更する
    localOneway.mo00615CaseDataCountOneway.itemLabel =
      tableData.value.values.items.length + t('label.item')
    setCareInfoList()
  }
}

/**
 * 確認ダイアログ表示（ok）
 *
 * @param paramDialogText - メッセージ
 */
async function openConfirmDialog1(paramDialogText: string): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(DIALOG_BTN.OK)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no）
 */
async function openConfirmDialog2(paramDialogText: string): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = DIALOG_BTN.NO

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「年月日テキストボックス」フォーカス
 *
 * @param item - 該当行
 */
const onFocusEvent = (item: Or53926ItemsType) => {
  current.value = {
    date: item.date.value,
    time: item.time.value,
    type: item.type.modelValue,
    contents: item.contents.value,
  }
}

/**
 * 「年月日テキストボックス」チェンジ
 *
 * @param item - 該当行
 */
const onChangeEvent = async (item: Or53926ItemsType) => {
  // 該当行.完了フラグ = 1の場合
  if (item.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1) {
    // i.cmn.11287
    // 該当行は、老健カルテの対応結果として関連付いており、[改行]
    // その指示が完了となっているため変更できません。
    const dialogResult = await openConfirmDialog1(t('message.i-cmn-11287'))
    switch (dialogResult) {
      case DIALOG_BTN.OK:
        Or21814Logic.state.set({
          uniqueCpId: or21814.value.uniqueCpId,
          state: {
            isOpen: false,
          },
        })
        // OK：処理終了、値を元に戻す。
        item.date.value = current.value.date
        return
    }
  }
  // 該当行.完了フラグ <> 1
  else {
    // 入力した年月日が日付フォーマットに合ってない場合
    if (!isValidDateFormat(item.date.value)) {
      // 値を元に戻す。
      item.date.value = current.value.date
    }
    // 入力する年月日が日付フォーマットに合って場合
    else {
      formatDate(item.date)
      if (item.updateKbn === UPDATE_KBN.NONE) {
        item.updateKbn = UPDATE_KBN.UPDATE
      }
      setCareInfoList()
    }
  }
}

/**
 * 「年月日カレンダ」押下
 *
 * @param item - 該当行
 */
const onDateClick = async (item: Or53926ItemsType) => {
  tableData.value.values.selectedRowId = item.id
  current.value = {
    date: item.date.value,
    time: item.time.value,
    type: item.type.modelValue,
    contents: item.contents.value,
  }
  tableIndex.value = tableData.value.values.items.findIndex((data) => data.id === item.id)
  // 該当行.完了フラグ = 1の場合
  if (item.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1) {
    // i.cmn.11287
    // 該当行は、老健カルテの対応結果として関連付いており、[改行]
    // その指示が完了となっているため変更できません。
    const dialogResult = await openConfirmDialog1(t('message.i-cmn-11287'))
    switch (dialogResult) {
      case DIALOG_BTN.OK:
        Or21814Logic.state.set({
          uniqueCpId: or21814.value.uniqueCpId,
          state: {
            isOpen: false,
          },
        })
        // OK：処理終了。
        return
    }
  }
}

/**
 * 「時間選択」押下
 *
 * @param item - 該当行
 */
const onTimeClick = async (item: Or53926ItemsType) => {
  tableData.value.values.selectedRowId = item.id
  current.value = {
    date: item.date.value,
    time: item.time.value,
    type: item.type.modelValue,
    contents: item.contents.value,
  }
  tableIndex.value = tableData.value.values.items.findIndex((data) => data.id === item.id)
  // 該当行.完了フラグ = 1の場合
  if (item.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1) {
    // i.cmn.11287
    // 該当行は、老健カルテの対応結果として関連付いており、[改行]
    // その指示が完了となっているため変更できません。
    const dialogResult = await openConfirmDialog1(t('message.i-cmn-11287'))
    switch (dialogResult) {
      case DIALOG_BTN.OK:
        Or21814Logic.state.set({
          uniqueCpId: or21814.value.uniqueCpId,
          state: {
            isOpen: false,
          },
        })
        // OK：処理終了。
        return
    }
  }
}

/**
 * 文字列の形式の判定
 * YYYY/MM/DD または YYYY/MM の形式であるか照合する
 * 月や日が1桁の場合も許容する
 *
 * @param date -照合対象の文字列
 */
function isValidDateFormat(date: string): boolean {
  // 正規表現(YYYY/M/D or YYYY/MM/DD)
  const regex = /^(\d{4})\/([1-9]|0[1-9]|1[0-2])(\/([1-9]|0[1-9]|[12][0-9]|3[01]))$/

  // 正規表現と指定された文字列を照合
  if (regex.test(date)) {
    const [year, month, day] = date.split('/').map(Number)
    // 月份と日付の有効性をチェックする
    if (month === 2) {
      const isLeapYear = (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0
      return (isLeapYear && day <= 29) || (!isLeapYear && day <= 28)
    } else if ([4, 6, 9, 11].includes(month)) {
      return day <= 30
    } else {
      return day <= 31
    }
  }

  return false
}

/**
 * YYYY/M/D形式をYYYY/MM/DD形式に変更
 *
 * @param date -照合対象の文字列
 */
function formatDate(date: OrX0161Type) {
  const [year, month, day] = date.value.split('/').map(String)
  date.value = `${year}/${month.padStart(2, Or53926Const.DEFAULT.STR.ZERO)}/${day.padStart(2, Or53926Const.DEFAULT.STR.ZERO)}`
}

/**
 * 「ケース入力支援アイコンボタン」押下
 *
 * @param item - 該当行
 */
const onCareInputSupportIconClick = async (item: Or53926ItemsType) => {
  tableData.value.values.selectedRowId = item.id
  // 該当行.完了フラグ = 1の場合
  if (item.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1) {
    // i.cmn.11287
    // 該当行は、老健カルテの対応結果として関連付いており、[改行]
    // その指示が完了となっているため変更できません。
    const dialogResult = await openConfirmDialog1(t('message.i-cmn-11287'))
    switch (dialogResult) {
      case DIALOG_BTN.OK:
        Or21814Logic.state.set({
          uniqueCpId: or21814.value.uniqueCpId,
          state: {
            isOpen: false,
          },
        })
        // OK：処理終了。
        return
    }
  }
  tableIndex.value = tableData.value.values.items.findIndex((data) => data.id === item.id)
  // GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  localOneway.or51775Oneway = {
    // タイトル
    title: t('label.remarks'),
    // 画面ID
    screenId: Or53928Const.DEFAULT.STR.SCREEN_ID,
    // 分類ID
    bunruiId: Or53928Const.DEFAULT.STR.EMPTY,
    // 大分類ＣＤ
    t1Cd: Or53928Const.DEFAULT.STR.HYPHEN,
    // 中分類CD
    t2Cd: Or53928Const.DEFAULT.STR.HYPHEN,
    // 小分類ＣＤ
    t3Cd: Or53928Const.DEFAULT.STR.HYPHEN,
    // テーブル名
    tableName: Or53928Const.DEFAULT.STR.TABLE_NAME,
    // カラム名
    columnName: Or53928Const.DEFAULT.STR.COLUMN_NAME,
    // アセスメント方式
    assessmentMethod: Or53928Const.DEFAULT.STR.EMPTY,
    // 文章内容
    inputContents: item.contents.value,
    // 利用者ID
    userId: systemCommonsStore.getUserId,
    // モード
    mode: Or53928Const.DEFAULT.STR.PROCESS_MODE,
  } as Or51775OnewayType
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    // 本文末に追加の場合
    if (Or53928Const.ADD_END_TEXT_IMPORT_TYPE === data.type) {
      tableData.value.values.items[tableIndex.value].contents.value += data.value
    }
    // 本文上書の場合
    else if (Or53928Const.OVERWRITE_TEXT_IMPORT_TYPE === data.type) {
      tableData.value.values.items[tableIndex.value].contents.value = data.value
    }
    if (tableData.value.values.items[tableIndex.value].updateKbn === UPDATE_KBN.NONE) {
      tableData.value.values.items[tableIndex.value].updateKbn = UPDATE_KBN.UPDATE
    }
    setCareInfoList()
  }
}

/**
 * ケース内容を変更時
 *
 * @param item - 該当行
 */
const onContentsFocus = (item: Or53926ItemsType) => {
  current.value = {
    date: item.date.value,
    time: item.time.value,
    type: item.type.modelValue,
    contents: item.contents.value,
  }
}

/**
 * ケース内容を変更時
 *
 * @param item - 該当行
 */
const onContentsChange = async (item: Or53926ItemsType) => {
  // 該当行.完了フラグ = 1の場合
  if (item.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1) {
    // i.cmn.11287
    // 該当行は、老健カルテの対応結果として関連付いており、[改行]
    // その指示が完了となっているため変更できません。
    const dialogResult = await openConfirmDialog1(t('message.i-cmn-11287'))
    switch (dialogResult) {
      case DIALOG_BTN.OK:
        Or21814Logic.state.set({
          uniqueCpId: or21814.value.uniqueCpId,
          state: {
            isOpen: false,
          },
        })
        // OK：処理終了、値を元に戻す。
        item.contents.value = current.value.contents
        return
    }
  }
  // 該当行.完了フラグ <> 1
  else {
    if (item.updateKbn === UPDATE_KBN.NONE) {
      item.updateKbn = UPDATE_KBN.UPDATE
    }
    setCareInfoList()
  }
}

/**
 *  ボタン押下時の処理(Or26257)
 *
 * @param item - 該当行
 */
const onClickOr26257 = async (item: Or53926ItemsType) => {
  tableData.value.values.selectedRowId = item.id
  // 該当行.完了フラグ = 1の場合
  if (item.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1) {
    // i.cmn.11287
    // 該当行は、老健カルテの対応結果として関連付いており、[改行]
    // その指示が完了となっているため変更できません。
    const dialogResult = await openConfirmDialog1(t('message.i-cmn-11287'))
    switch (dialogResult) {
      case DIALOG_BTN.OK:
        Or21814Logic.state.set({
          uniqueCpId: or21814.value.uniqueCpId,
          state: {
            isOpen: false,
          },
        })
        // OK：処理終了。
        return
    }
  }
  tableIndex.value = tableData.value.values.items.findIndex((data) => data.id === item.id)

  localOneway.or26257Oneway.selectMode = Or26257Const.DEFAULT.SELECT_MODE_12
  localOneway.or26257Oneway.kijunYmd =
    systemCommonsStore.getSystemDate ?? Or53926Const.DEFAULT.STR.EMPTY
  localOneway.or26257Oneway.defSvJigyoId =
    localOneway.param.svJigyoId ?? Or53926Const.DEFAULT.STR.EMPTY
  localOneway.or26257Oneway.svJigyoIdList =
    localOneway.param.svJigyoIdList?.map((data) => ({
      svJigyoId: data,
    })) ?? []
  localOneway.or26257Oneway.misetteiFlg = Or26257Const.DEFAULT.MISETTEI_FLG_OK
  // Or26257のダイアログ開閉状態を更新する
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 職員変更処理関数
 *
 * @param staffInfo - 職員詳細
 */
function getStaffInfo(staffInfo: Or26257Type) {
  if (staffInfo) {
    tableData.value.values.items[tableIndex.value].recorder.value =
      staffInfo.shokuin.shokuin1Knj + Or53926Const.DEFAULT.STR.SPACE + staffInfo.shokuin.shokuin2Knj
    if (tableData.value.values.items[tableIndex.value].updateKbn === UPDATE_KBN.NONE) {
      tableData.value.values.items[tableIndex.value].updateKbn = UPDATE_KBN.UPDATE
    }
    setCareInfoList()
  }
}

/**
 * ケースチェックボックスを変更時
 *
 * @param item - 該当行
 */
const onIsCaseChange = (item: Or53926ItemsType) => {
  if (item.updateKbn === UPDATE_KBN.NONE) {
    item.updateKbn = UPDATE_KBN.UPDATE
  }
  setCareInfoList()
}

/**
 * 申し送りチェックボックスを変更時
 *
 * @param item - 該当行
 */
const onHandOverChange = (item: Or53926ItemsType) => {
  if (item.updateKbn === UPDATE_KBN.NONE) {
    item.updateKbn = UPDATE_KBN.UPDATE
  }
  setCareInfoList()
}

/**
 * 指定された行までスクロールする
 *
 * @param id -指定行のID
 */
const onScrollIntoView = (id: string) => {
  void nextTick(() => {
    const targetRow = document.getElementById(id)
    targetRow?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  })
}

/**
 * 現在の項目を監視
 */
watch(
  () => currentItem.value,
  (newValue) => {
    if (!newValue) {
      return
    }
    if (currentFlag.value) {
      currentFlag.value = false
      return
    }
    if (newValue.date.value !== current.value.date) {
      currentFlag.value = true
      if (currentItem.value.updateKbn === UPDATE_KBN.NONE) {
        currentItem.value.updateKbn = UPDATE_KBN.UPDATE
      }
      setCareInfoList()
    }
  },
  { deep: true }
)
</script>
<template>
  <div style="margin: 0 24px 21px 24px">
    <c-v-row style="margin-bottom: -9px !important">
      <c-v-col
        cols="4"
        class="d-flex align-items-center"
        style="padding: 0 !important"
      >
        <!-- 行追加ボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAdd"
          class="row-btn"
          @click="createRow"
        />
        <!-- 行複写ボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayDuplicate"
          class="mx-2 row-btn"
          @click="copyRow"
        />
        <!-- 行削除ボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayDelete"
          class="row-btn"
          @click="deleteRow"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row
      class="d-flex align-end justify-end"
      :style="{ width: Or53926Const.DEFAULT.TABLE_WIDTH + 'px' }"
    >
      <!-- ケースデータ件数 -->
      <base-mo00615
        style="padding-right: 15px !important"
        :oneway-model-value="localOneway.mo00615CaseDataCountOneway"
      />
    </c-v-row>
    <!-- 分子：表 -->
    <c-v-row
      class="table-header main-table"
      style="margin-top: 2px !important"
    >
      <base-mo-01354
        v-model="tableData"
        :oneway-model-value="mo01354Oneway"
        class="list-wrapper"
        :style="{ width: Or53926Const.DEFAULT.TABLE_WIDTH + 'px' }"
      >
        <!-- * 年月日 -->
        <template #[`item.date`]="{ item }">
          <!-- 表用分子日付テキストフィールド -->
          <g-custom-or-X0161
            v-if="item.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1"
            :id="item.id"
            v-model="item.date"
            :oneway-model-value="{
              ...orX0161Oneway,
              canClick: false,
            }"
            @focus="onFocusEvent(item)"
            @change="onChangeEvent(item)"
            @click="onDateClick(item)"
          />
          <g-custom-or-X0161
            v-else
            :id="item.id"
            v-model="item.date"
            :oneway-model-value="orX0161Oneway"
            @focus="onFocusEvent(item)"
            @change="onChangeEvent(item)"
            @click="onDateClick(item)"
          />
        </template>
        <!-- 時間 -->
        <template #[`item.time`]="{ item }">
          <!-- 表用分子時間テキストフィールド -->
          <g-custom-or-X0162
            v-if="item.kanryouFlg === Or53926Const.DEFAULT.KBN.KANRYOU_FLG_1"
            v-model="item.time"
            :oneway-model-value="{
              ...orX0162Oneway,
              canClick: false,
            }"
            @click="onTimeClick(item)"
          />
          <g-custom-or-X0162
            v-else
            v-model="item.time"
            :oneway-model-value="orX0162Oneway"
            @click="onTimeClick(item)"
          />
        </template>
        <!-- 種別 -->
        <template #[`item.type`]="{ item }">
          <base-mo01282
            v-model="item.type"
            :oneway-model-value="mo01282Oneway"
            @focus="onTypeFocus(item)"
            @change="onTypeChange(item)"
          />
        </template>
        <!-- 内容 -->
        <template #[`item.contents`]="{ item }">
          <c-v-row
            class="d-flex align-center h-100"
            no-gutters
          >
            <div style="width: 32px">
              <!-- 内容の入力支援 -->
              <base-mo00009
                :oneway-model-value="localOneway.mo00009Oneway"
                @click="onCareInputSupportIconClick(item)"
              />
            </div>
            <c-v-col
              class="h-100"
              style="padding: 8px 0 !important"
            >
              <!-- 内容 -->
              <base-mo01280
                v-model="item.contents"
                :style="{ color: item.color }"
                @focus="onContentsFocus(item)"
                @change="onContentsChange(item)"
              />
            </c-v-col>
          </c-v-row>
        </template>
        <!-- 記録者 -->
        <template #[`item.recorder`]="{ item }">
          <c-v-row
            class="d-flex align-center"
            no-gutters
          >
            <div style="width: 32px">
              <!-- 記録者入力支援 -->
              <base-mo00009
                :oneway-model-value="localOneway.mo00009Oneway"
                @click="onClickOr26257(item)"
              />
            </div>
            <c-v-col>
              <!-- 記録者 -->
              <base-mo01337 :oneway-model-value="item.recorder" />
            </c-v-col>
          </c-v-row>
        </template>
        <!-- コメント適用 -->
        <template #[`item.commentApplicable`]="{ item }">
          <!-- 記録者チェックボックス（ケース） -->
          <c-v-row
            style="margin-left: -8px !important"
            no-gutters
          >
            <base-mo00018
              v-model="item.isCase"
              :oneway-model-value="caseCheckBoxOneway"
              @change="onIsCaseChange(item)"
            />
          </c-v-row>
          <c-v-row
            style="margin-left: -8px !important"
            no-gutters
          >
            <!-- 記録者チェックボックス（申し送り） -->
            <base-mo00018
              v-model="item.handOver"
              :oneway-model-value="handoverCheckBoxOneway"
              @change="onHandOverChange(item)"
            />
          </c-v-row>
        </template>
        <!-- 画像 -->
        <template #[`item.image`]="{ item }">
          <c-v-row class="image-btn">
            <base-mo-00009
              :oneway-model-value="item.image"
              :disabled="true"
            />
          </c-v-row>
          <c-v-row style="height: 36px" />
        </template>
        <!-- ページングを非表示 -->
        <template #bottom />
      </base-mo-01354>
    </c-v-row>
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- GUI00937_入力支援［ケアマネ］ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Type"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="or51775Confirm"
  />
  <!--GUI00220 職員検索画面-->
  <g-custom-or-26257
    v-if="showDialogOr26257"
    v-bind="or26257"
    v-model="local.or26257"
    :oneway-model-value="localOneway.or26257Oneway"
    @update:model-value="getStaffInfo"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

.table-header {
  :deep(.v-table__wrapper .row-selected-style td) {
    background-color: rgb(160, 160, 160) !important;
  }
  :deep(.v-table__wrapper .row-unselected-style td) {
    background-color: rgb(219, 219, 219) !important;
  }
  :deep(.v-table__wrapper .selected-row td) {
    background-color: rgb(var(--v-theme-blue-100)) !important;
  }
  :deep(.v-col) {
    padding: 0 !important;
  }
  :deep(.table-cell) {
    border: none !important;
  }
  :deep(.table-cell .v-field--focused) {
    border: none !important;
    outline: none !important;
    color: rgb(var(--v-theme-key));
    box-shadow: 0 0 10px rgb(var(--v-theme-key));
  }
  :deep(.v-input--density-compact) {
    --v-input-padding-top: 0px !important;
    --v-field-padding-bottom: 0px !important;
  }
  :deep(.v-table__wrapper) {
    overflow: auto !important;
    border: none !important;
  }
  :deep(.v-table__wrapper .v-field__input) {
    .v-autocomplete__selection-text {
      color: rgb(var(--v-theme-text));
    }
    min-height: 28px !important;
    padding: 0px 0px 0px 16px !important;
  }
  :deep(.v-table__wrapper th) {
    height: 32px;
    white-space: nowrap !important;
    border-top: 1px solid rgb(var(--v-theme-black-200)) !important;
  }
  :deep(.v-table__wrapper th:first-child) {
    border-left: 1px solid rgb(var(--v-theme-black-200)) !important;
  }
  :deep(.v-table__wrapper th:last-child) {
    border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
    text-decoration: underline;
    color: rgb(var(--v-theme-sub));
    // 非活性
    span {
      opacity: 0.6 !important;
    }
  }
  :deep(.v-table__wrapper tbody tr td) {
    height: 76px !important;
    padding: 0px 16px !important;
  }
  :deep(.v-table__wrapper td:first-child) {
    border-left: 1px solid rgb(var(--v-theme-black-200)) !important;
    padding: 0 !important;

    .full-width-field {
      height: 32px !important;
      padding: 0px 16px !important;
    }
  }
  :deep(.v-table__wrapper td:nth-child(2)) {
    padding: 0 !important;

    .full-width-field {
      height: 32px !important;
      padding: 0px 16px !important;
    }
  }
  :deep(.v-table__wrapper td:nth-child(3)) {
    padding: 0 !important;

    .full-width-field-select {
      height: 32px !important;
      padding: 0 12px !important;
    }

    .v-field__field {
      align-items: center;
    }
  }
  :deep(.v-table__wrapper td:last-child) {
    border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
    padding: 0 !important;
  }
  :deep(.v-table__wrapper td:last-child .v-btn__content .v-icon) {
    display: none !important;
  }
}

:deep(.v-table__wrapper .table-cell:focus) {
  box-shadow: 0 0 10px rgb(var(--v-theme-key));
}

:deep(.full-width-field) {
  padding: 0px !important;
}

.row-btn {
  height: 32px !important;
  width: 89px !important;
  min-width: 89px !important;
}

.icon-style1 {
  height: 32px;
  width: 62px;
  min-width: 62px;
  padding: 4px 8px !important;
  background-color: rgb(var(--v-theme-green-200));
  border: 1px solid rgb(var(--v-theme-success)) !important;
}

.icon-style2 {
  height: 32px;
  width: 62px;
  min-width: 62px;
  padding: 4px 8px !important;
  background-color: rgb(var(--v-theme-black-200));
}

.image-btn {
  height: 40px;
  padding: 4px 16px;
  border-bottom: 1px solid rgb(var(--v-theme-black-200)) !important;

  :deep(.v-btn__prepend) {
    margin-inline: 0;
  }
}
</style>
