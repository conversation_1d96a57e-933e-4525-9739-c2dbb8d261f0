<script setup lang="ts">
import { ref, reactive, computed, onBeforeMount, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or29104Const } from '../Or29104/Or29104.constants'
import { Or57737Const } from '../Or57737/Or57737.constants'
import { Or57737Logic } from '../Or57737/Or57737.logic'
import { Gui00070Logic } from '../Gui00070/Gui00070.logic'
import { Gui00070Const } from '../Gui00070/Gui00070.constants'
import { OrX0073Const } from '../OrX0073/OrX0073.constants'
import type { jigyoInfo } from '../OrX0042/OrX0042.type'
import type {
  CarePlan2SelectOutData,
  Keikasyo2,
  Tan<PERSON>,
  Yo<PERSON>,
} from '~/components/custom-components/organisms/OrX0042/OrX0042.type'
import { Or29104Logic } from '~/components/custom-components/organisms/Or29104/Or29104.logic'
import { OrX0007Logic } from '~/components/custom-components/organisms/OrX0007/OrX0007.logic'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import { OrX0009Logic } from '~/components/custom-components/organisms/OrX0009/OrX0009.logic'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import { OrX0014Logic } from '~/components/custom-components/organisms/OrX0014/OrX0014.logic'
import type { OrX0014StateType } from '~/components/custom-components/organisms/OrX0014/OrX0014.type'
import { OrX0026Const } from '~/components/custom-components/organisms/OrX0026/OrX0026.constants'
import { OrX0027Const } from '~/components/custom-components/organisms/OrX0027/OrX0027.constants'
import type { OrX0027StateType } from '~/components/custom-components/organisms/OrX0027/OrX0027.Type'
import type { Or21828StateType } from '~/components/base-components/organisms/Or21828/Or21828.type'
import type { Or21830StateType } from '~/components/base-components/organisms/Or21830/Or21830.type'
import { UPDATE_KBN, SPACE_WAVE } from '~/constants/classification-constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  Careplan2HistorySelectInEntity,
  Careplan2HistorySelectOutEntity,
} from '~/repositories/cmn/entities/Careplan2HistorySelectEntity'
import type {
  Careplan2PlanPeriodChangeSelectInEntity,
  Careplan2PlanPeriodChangeSelectOutEntity,
} from '~/repositories/cmn/entities/Careplan2PlanPeriodChangeSelectEntity'
import type {
  CarePlan2SelectInEntity,
  CarePlan2SelectOutEntity,
} from '~/repositories/cmn/entities/CarePlan2SelectEntity'
import type { CarePlan2TermIdSelectEntity } from '~/repositories/cmn/entities/CarePlan2TermIdSelectEntity'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { OrX0007OnewayType, OrX0007Type } from '~/types/cmn/business/components/OrX0007Type'
import type { OrX0009Type, OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0026OnewayType, OrX0026Type } from '~/types/business/components/OrX0026Type'
import { OrX0042Const } from '~/components/custom-components/organisms/OrX0042/OrX0042.constants'
import type { OrX0004StateType } from '~/components/custom-components/organisms/OrX0004/OrX0004.type'
import type { OrX0005StateType } from '~/components/custom-components/organisms/OrX0005/OrX0005.type'
import type { Or29104OnewayType, Or29104Type } from '~/types/cmn/business/components/Or29104Type'
import type {
  Careplan2InsertInEntity,
  Keikasyo2Insert,
} from '~/repositories/cmn/entities/CarePlan2InsertEntity'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
import { useScreenStore } from '~/stores/session/screen'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useScreenUtils } from '~/utils/useScreenUtils'
import {
  useScreenInitFlg,
  useScreenTwoWayBind,
  useSetupChildProps,
} from '~/composables/useComponentVue'
import type { OrX0073OnewayType, OrX0073Type } from '~/types/cmn/business/components/OrX0073Type'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import type {
  Or21813EventType,
  Or21813StateType,
} from '~/components/base-components/organisms/Or21813/Or21813.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or10583OnewayType } from '~/types/cmn/business/components/Or10583Type'
import { Or10583Const } from '~/components/custom-components/organisms/Or10583/Or10583.constants'
import { Or10583Logic } from '~/components/custom-components/organisms/Or10583/Or10583.logic'
import type { Or26610OnewayType } from '~/types/cmn/business/components/Or26610Type'
import { Or26610Const } from '~/components/custom-components/organisms/Or26610/Or26610.constants'
import { Or26610Logic } from '~/components/custom-components/organisms/Or26610/Or26610.logic'
import type { Or57737OnewayType } from '~/types/cmn/business/components/Or57737Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { useJigyoList } from '~/utils/useJigyoList'
import type { OrX0165OnewayType } from '~/types/cmn/business/components/OrX0165Type'
import { OrX0159Const } from '~/components/custom-components/organisms/OrX0159/OrX0159.constants'
import { useUserListInfo } from '~/utils/useUserListInfo'
import type { AuthzOtherCaremanagerType } from '~/types/business/stores/SystemCommonsType'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'

/**
 * OrX0042:有機体:計画書（２）（画面コンポーネント）
 * GUI01014_計画書（２）
 *
 * @description
 * 計画書（２）（画面コンポーネント）
 *
 * <AUTHOR>
 */
// システム共有領域の状態管理
const systemCommonsStore = useSystemCommonsStore()
const { syscomUserSelectWatchFunc } = useUserListInfo()
const { setChildCpBinds } = useScreenUtils()

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  orx0014UniqueCpId: string
  parentUniqueCpId: string
}
/** props */
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/

const orX0007 = ref({ uniqueCpId: OrX0007Const.CP_ID(0) })
const orX0009 = ref({ uniqueCpId: OrX0009Const.CP_ID(0) })
const orX0026 = ref({ uniqueCpId: OrX0026Const.CP_ID(0) })
const orX0027 = ref({ uniqueCpId: OrX0027Const.CP_ID(0) })
const or29104 = ref({ uniqueCpId: Or29104Const.CP_ID(0) })
const or57737 = ref({ uniqueCpId: Or57737Const.CP_ID(0) })
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const orX0073 = ref({ uniqueCpId: OrX0073Const.CP_ID(0) })
const gui00070 = ref({ uniqueCpId: Gui00070Const.CP_ID(0) })
const or10583 = ref({ uniqueCpId: Or10583Const.CP_ID(0) })
const or26610 = ref({ uniqueCpId: Or26610Const.CP_ID(0) })
// const or10878 = ref({ uniqueCpId: Or10878Const.CP_ID(0) })
const or41179 = ref({ uniqueCpId: '' })
// TODO 共通情報
const commonInfoData = reactive({
  //事業者グループ適用ID
  officeGroupId: 'G0002',
  eFileSaveKbn: '1',
  // サービスＫＢＮ
  serviceType: '50010',
  // 計画書様式フラグ = 2(居宅)
  // cpStyleFlg: '2',
  // 計画書(2)マスタ
  carePlan2Mastr: {
    // 期間の管理
    periodManager: OrX0042Const.PERIOD_MANAGER.DATE,
    // 番号フラグ
    numberFlg: '0',
    // 日付フラグ
    dateFlg: '1',
    rendouFlag: '0',
  },
  // e-文書法対象機能
  eDocumentLawTgtFunc: {
    // 電子ファイル保存設定区分
    elecFileSaveType: '0',
  },
  // setInputComponentsFlg
  setInputComponentsFlg: false,
  // 利用者情報リスト
  userList: [],
  // 変数.セクション名
  sectionName: '1',
  // 選択帳票番号
  choIndex: '1',
})

const defaultComponents = {
  // 新規操作メニュー
  orX0004: {
    showCreateBtn: true,
    showCreateMenuCopy: true,
  } as OrX0004StateType,
  orX0005: {
    showPrintConfig: true,
    showBatchPrintOfPlans: true,
  } as OrX0005StateType,
  // 計画書オプションメニュー
  orX0014: {
    showLogFlg: true,
    showDeleteFlg: true,
  } as OrX0014StateType,
  //作成者
  orX0009: {} as OrX0009Type,
  // お気に入りアイコンボタン
  or21828: {} as Or21828StateType,
  // 保存ボタン
  or21830: {} as Or21830StateType,
}

const defaultOneway = reactive({
  //期間データ
  orX0007Oneway: {} as OrX0007OnewayType,
  //作成者
  orX0009Oneway: {} as OrX0009OnewayType,
  //作成日
  orX0026Oneway: {
    itemLabel: t('label.create-date'),
    showItemLabel: true,
    isVerticalLabel: true,
    isRequired: false,
    width: '135',
    customClass: {
      outerClass: '',
      labelClass: 'mb-1',
    },
    calendarStyle: 'width: 135px;height: 100%',
  } as OrX0026OnewayType,
  // 選択列一括上書ボタン
  mo00611BatchOverriteScOneway: {
    prependIcon: 'file_copy',
    btnLabel: t('btn.select-columns-bundle-overrite'),
    class: 'btn-selected-columns-bundle-overrite',
    style: 'white-space: break-spaces !important',
  } as Mo00611OnewayType,
  // 生活全般の解決すべき課題(ニーズ)アイコンボタン
  mo00009OnewayGutaitekiKnj: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 長期目標
  mo00009OnewaylongTermGoal: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 長期目標期間年月日
  mo00009OnewaylongTermGoalYmd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 長期目標期間月日
  mo00009OnewaylongTermGoalMd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 長期目標期間
  mo00009OnewaylongTermGoalPeriod: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 短期目標
  mo00009OnewayShortTermGoal: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 短期目標期間
  mo00009OnewayShortTermGoalPeriod: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 短期目標期間年月日
  mo00009OnewayShortTermGoalPeriodYmd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 短期目標期間月日
  mo00009OnewayShortTermGoalPeriodMd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // サービス内容
  mo00009OnewayServiceContents: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,

  // サービス種別
  mo00009OnewayServiceKbn: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 保険サービス
  mo00009OnewayInsService: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 利用票取込
  mo00009OnewayUseSlipImport: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // ※2
  mo00009OnewayStar2: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 担当者
  mo00009OnewayManager: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 担当者取込
  mo00009OnewayManagerImport: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 頻度
  mo00009OnewayFrequency: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 頻度曜日
  mo00009OnewayFrequencyDayOfWeek: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 頻度（取込）
  mo00009OnewayFrequencyImport: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 期間
  mo00009OnewayPeriod: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 期間年月日
  mo00009OnewayPeriodYmd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 間月日
  mo00009OnewayPeriodMd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 画面メニューマスタ他設定
  mo00009OnewayMasterConfig: {
    density: 'compact',
    rounded: 'sm',
    size: 'medium',
    btnIcon: 'database',
  } as Mo00009OnewayType,
  // ～
  mo01337OnewayWaveDash: {
    value: t('label.wave-dash'),
  } as Mo01337OnewayType,
  // 〇
  mo01337OnewayCircle: {
    value: t('label.circle'),
  } as Mo01337OnewayType,
  // 1編集入力アイコンボタン
  mo00009OnewayStart1Edit: {
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 週間日課
  mo00009OnewayWeekDailyRoutineEdit: {
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 週間日課有無
  mo01337OnewayPreOrAb: {
    value: t('label.presence'),
  } as Mo01337OnewayType,
  // 計画書一括印刷画面
  or10583Oneway: {
    carePlanPrintAllInData: {
      legalPersonId: '',
      shisetuId: '',
      carePlanStyle: '',
      employeeId: '',
      officeData: {
        officeId: '',
        officeName: '',
      },
      jigyoTypeCd: '',
    },
    setInputComponentsFlg: false,
  } as Or10583OnewayType,
  // 印刷設定画面
  or26610Oneway: {
    userList: [],
    kikanFlg: '',
    userId: '',
    sectionName: '',
    historyId: '',
    choIndex: '',
    sysCd: '',
    sysRyaku: '',
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
    shokuId: '',
    managerId: '',
    careManagerInChargeSettingsFlag: 0,
    startYmd: '',
    jigyoshoId: '',
    loginNumber: '',
    loginUserType: '',
    emrLinkFlag: '',
    shosikiFlg: '',
    cksFlg: '',
    location: '',
  } as Or26610OnewayType,
  // 保険サービス登録画面
  // or10878Oneway: {
  //   houjinId: '',
  //   shokuinId: '',
  //   shisetuId: '',
  //   userId: '',
  //   svJigyoCd: '',
  //   svJigyoId: '',
  //   sysYmd: '',
  //   termid: '',
  //   functionId: '',
  //   callerFlag: '',
  //   hsJigyoFlg: '',
  //   hindoFlg: '',
  //   hokenSvList: [],
  //   hokenYmdList: [],
  // } as Or10878OnewayType,
})

const localComponents = reactive({
  orX0004: {
    ...defaultComponents.orX0004,
  } as OrX0004StateType,
  orX0005: {
    ...defaultComponents.orX0005,
  } as OrX0005StateType,
  orX0009: {
    ...defaultComponents.orX0009,
  } as OrX0009Type,
  or21828: {
    ...defaultComponents.or21828,
  } as Or21828StateType,
  or21830: {
    ...defaultComponents.or21830,
  } as Or21830StateType,
  orX0014: {
    ...defaultComponents.orX0014,
  } as OrX0014StateType,
})

const localOneway = reactive({
  orX0007Oneway: {
    ...defaultOneway.orX0007Oneway,
  } as OrX0007OnewayType,
  // 履歴
  orX0165History: {
    label: t('label.history'),
  } as OrX0165OnewayType,
  orX0009Oneway: {
    ...defaultOneway.orX0009Oneway,
  } as OrX0009OnewayType,
  orX0026Oneway: {
    ...defaultOneway.orX0026Oneway,
  } as OrX0026OnewayType,
  mo00611BatchOverriteScOneway: {
    ...defaultOneway.mo00611BatchOverriteScOneway,
  } as Mo00611OnewayType,
  mo00009OnewayGutaitekiKnj: {
    ...defaultOneway.mo00009OnewayGutaitekiKnj,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoal: {
    ...defaultOneway.mo00009OnewaylongTermGoal,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoalYmd: {
    ...defaultOneway.mo00009OnewaylongTermGoalYmd,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoalMd: {
    ...defaultOneway.mo00009OnewaylongTermGoalMd,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoalPeriod: {
    ...defaultOneway.mo00009OnewaylongTermGoalPeriod,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoal: {
    ...defaultOneway.mo00009OnewayShortTermGoal,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoalPeriod: {
    ...defaultOneway.mo00009OnewayShortTermGoalPeriod,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoalPeriodYmd: {
    ...defaultOneway.mo00009OnewayShortTermGoalPeriodYmd,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoalPeriodMd: {
    ...defaultOneway.mo00009OnewayShortTermGoalPeriodMd,
  } as Mo00009OnewayType,
  mo00009OnewayServiceContents: {
    ...defaultOneway.mo00009OnewayServiceContents,
  } as Mo00009OnewayType,
  mo00009OnewayServiceKbn: {
    ...defaultOneway.mo00009OnewayServiceKbn,
  } as Mo00009OnewayType,
  mo00009OnewayInsService: {
    ...defaultOneway.mo00009OnewayInsService,
  } as Mo00009OnewayType,
  mo00009OnewayUseSlipImport: {
    ...defaultOneway.mo00009OnewayUseSlipImport,
  } as Mo00009OnewayType,
  mo00009OnewayStar2: {
    ...defaultOneway.mo00009OnewayStar2,
  } as Mo00009OnewayType,
  mo00009OnewayManager: {
    ...defaultOneway.mo00009OnewayManager,
  } as Mo00009OnewayType,
  mo00009OnewayManagerImport: {
    ...defaultOneway.mo00009OnewayManagerImport,
  } as Mo00009OnewayType,
  mo00009OnewayFrequency: {
    ...defaultOneway.mo00009OnewayFrequency,
  } as Mo00009OnewayType,
  mo00009OnewayFrequencyDayOfWeek: {
    ...defaultOneway.mo00009OnewayFrequencyDayOfWeek,
  } as Mo00009OnewayType,
  mo00009OnewayFrequencyImport: {
    ...defaultOneway.mo00009OnewayFrequencyImport,
  } as Mo00009OnewayType,
  mo00009OnewayPeriod: {
    ...defaultOneway.mo00009OnewayPeriod,
  } as Mo00009OnewayType,
  mo00009OnewayPeriodYmd: {
    ...defaultOneway.mo00009OnewayPeriodYmd,
  } as Mo00009OnewayType,
  mo00009OnewayPeriodMd: {
    ...defaultOneway.mo00009OnewayPeriodMd,
  } as Mo00009OnewayType,
  mo00009OnewayMasterConfig: {
    ...defaultOneway.mo00009OnewayMasterConfig,
  } as Mo00009OnewayType,
  mo01337OnewayWaveDash: {
    ...defaultOneway.mo01337OnewayWaveDash,
  } as Mo01337OnewayType,
  mo01337OnewayCircle: {
    ...defaultOneway.mo01337OnewayCircle,
  } as Mo01337OnewayType,
  mo00009OnewayStart1Edit: {
    ...defaultOneway.mo00009OnewayStart1Edit,
  } as Mo00009OnewayType,
  mo01337OnewayPreOrAb: {
    ...defaultOneway.mo01337OnewayPreOrAb,
  } as Mo01337OnewayType,
  mo00009OnewayWeekDailyRoutineEdit: {
    ...defaultOneway.mo00009OnewayWeekDailyRoutineEdit,
  } as Mo00009OnewayType,
  //履歴画面
  or29104Oneway: {} as Or29104OnewayType,
  orX0073Oneway: {
    columnFlag: {},
    tableItem: [] as Keikasyo2[],
    periodManageFlag: '',
    planTargetPeriodId: '',
  } as OrX0073OnewayType,
  or57737Oneway: {} as Or57737OnewayType,
  or10583Oneway: {
    ...defaultOneway.or10583Oneway,
  } as Or10583OnewayType,
  or26610Oneway: {
    ...defaultOneway.or26610Oneway,
  } as Or26610OnewayType,
  // or10878Oneway: {
  //   ...defaultOneway.or10878Oneway,
  // } as Or10878OnewayType,
})

const local = reactive({
  orX0026: {
    value: '',
  } as OrX0026Type,
  orX0027: {
    labelText: '',
  } as OrX0027StateType,
  orX0002HistoryUpdKbnC: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
  or29104: {} as Or29104Type,
  orX0007: {
    PlanTargetPeriodUpdateFlg: '',
    planTargetPeriodId: '0',
  } as OrX0007Type,
  orX0073: {
    selectedItemIndex: 0,
    selectedItemTableIndex: -1,
    selectedColKey: '',
    tableItem: [],
    selectedItem: [],
  } as OrX0073Type,
  // or10878Type: {
  //   processingMode: '',
  //   hokenSvList: [],
  //   hokenYmdList: [],
  //   insuranceServiceImportList: [],
  //   validityPeriodId: '',
  // } as Or10878Type,
  // 履歴
  rireki: {
    index: 0,
    cnt: 0,
  },
})

// エラーダイアログのPromise
let or21813ResolvePromise: (value: Or21813EventType) => void
// 確認ダイアログのPromise
let or21814ResolvePromise: (value: Or21814EventType) => void

//  履歴更新区分
const historyUpdKbn = ref<string>('')
// 履歴更新区分保存
const histUpdFlg = ref<string>('')
// 表示フラグ
const localFlg = ref({
  // 計画対象期間表示表示フラグ
  showPlanningPeriodFlg: true,
  // 履歴表示フラグ
  showHistoryFlg: true,
  // 作成者表示フラグ
  showAuthorFlg: true,
  // 作成日表示フラグ
  showCreateDateFlg: true,
  //'期間月日表示フラグ
  showPeriodMdFlg: true,
  // 番号表示フラグ
  showNumberFlg: true,
  // ※1ラベル表示フラグ
  showStar1Flg: false,
  // サービス種別表示フラグ
  showServiceType: false,
  // (サービス種別)保険サービス
  showServiceTypeInsServiceFlg: false,
  // (サービス種別)利用票取込
  showServiceTypeUseSlipImportFlg: false,
  // ※2
  showStar2Flg: false,
  // 担当者
  showManagerFlg: true,
  // 担当者（取込）
  showManagerImportFlg: true,
  // 曜日
  showDayOfWeekFlg: false,
  // 頻度（取込）
  showFreqImportFlg: true,
  // 週間日課
  showWeekDailyRoutingFlg: true,
  // 長期目標期間開始年月日テキストボックス
  showPeriodStartYmdFlg: true,
  // 期間～
  showPeriodLabelFlg: true,
  // 期間終了年月日
  showPeriodEndYmdFlg: true,
})
/** 保険サービス表示フラグ */
const showInsServiceFlg = computed(() => {
  if (
    carePlan2Data.value.kikanFlg === OrX0042Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT &&
    !carePlan2Data.value.kikanObj
  ) {
    return false
  } else {
    const cpStyleFlg =
      (
        systemCommonsStore.searchSubsystemSettings(
          'cmn',
          systemCommonsStore.getSvJigyoId ?? ''
        ) as AuthzOtherCaremanagerType
      )?.planForm ?? OrX0042Const.CARE_PLAN_STYLE_FLG.HOME
    // 共通情報.計画書様式フラグ=2(居宅) 且つ
    // 事業者コードは"30010", "50010", "23031", "43031", "61084", "71084","61104"の場合表示
    // 、以外は非表示
    if (
      cpStyleFlg === OrX0042Const.CARE_PLAN_STYLE_FLG.HOME &&
      OrX0042Const.OFFICE_CD_LIST.includes(systemCommonsStore.getSvJigyoCd ?? '')
    ) {
      return true
    } else {
      return false
    }
  }
})
// 計画書（２）の初期情報を取得する。
const carePlan2Data = ref<CarePlan2SelectOutData>({
  // 計画書ID
  ks21Id: '',
  // PL取込フラグ
  plFlg: '',
  // 期間管理フラグ
  kikanFlg: '',
  // 計画期間情報
  kikanObj: null,
  // 履歴情報
  rirekiObj: null,
  // 計画書（２）リスト
  keikasyo2List: [],
  // 適用事業所一覧情報
  jigyoList: [],
})

// 有効期間ID
const termId = ref<string>('')

const orX0073Ref = ref({
  resetMo01338OnewayItemLabel: Function,
  initData: (_orX0073Oneway: OrX0073OnewayType) => {},
  onClickAssessment: () => {},
  onClickIssues: () => {},
  onClickSdCare: () => {},
})

// ダイアログ表示フラグ
const showDialogOr57737 = computed(() => {
  // Or57737のダイアログ開閉状態
  return Or57737Logic.state.get(or57737.value.uniqueCpId)?.isOpen ?? false
})
// ページアクション
const action = ref<string>('')
/** 初回初期化を示すフラグ */
const isFirstTimeInit = ref<boolean>(true)
/** 初期化処理トリガー用のフラグ */
const initOr32525Flg = ref<boolean>(false)
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<CarePlan2SelectOutData>({
  cpId: OrX0042Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = {
  ks21Id: '',
  plFlg: '',
  kikanFlg: '',
  kikanObj: null,
  rirekiObj: null,
  keikasyo2List: [],
  jigyoList: [],
}

/**
 * 事業所リスト監視
 */
const { updateJigyoList, jigyoListWatch } = useJigyoList()
/**
 *事業所情報
 */
const jigyo = ref<jigyoInfo>()
/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0026Const.CP_ID(0)]: orX0026.value,
  [OrX0027Const.CP_ID(0)]: orX0027.value,
  [Or57737Const.CP_ID(0)]: or57737.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0073Const.CP_ID(0)]: orX0073.value,
  [Gui00070Const.CP_ID(0)]: gui00070.value,
  [OrX0009Const.CP_ID(0)]: orX0009.value,
  [OrX0007Const.CP_ID(0)]: orX0007.value,
  [Or10583Const.CP_ID(0)]: or10583.value,
  [Or26610Const.CP_ID(0)]: or26610.value,
  // [Or10878Const.CP_ID(0)]: or10878.value,
  [Or29104Const.CP_ID(0)]: or29104.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
})

const isInit = useScreenInitFlg()
onBeforeMount(() => {
  // 初期情報取得
  if (isInit) {
    updateJigyoList(or41179.value.uniqueCpId)
    initOr32525Flg.value = true
  }

  if (systemCommonsStore.getUserSelectSelfId()) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }
})
/**
 * 利用者選択監視
 */
syscomUserSelectWatchFunc((newSelfId) => {
  if (newSelfId) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: newSelfId,
        },
      },
    })

    void nextTick(() => {
      initOr32525Flg.value = true
    })
  }
})
// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)
/**
 * 事業所変更時のコールバック
 *
 * @param newJigyoId - 新しい事業所ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId) {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    jigyo.value = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)

    void nextTick(() => {
      initOr32525Flg.value = true
    })
  }
}
// 変更されたリスニングのコンポーネントIDリスト
const watchedComponents = ref<string[]>([
  props.uniqueCpId,
  orX0026.value.uniqueCpId,
  orX0009.value.uniqueCpId,
  orX0027.value.uniqueCpId,
])

/**
 * 計画書（２）の初期情報を取得する。
 */
async function getCarePlan2InitData() {
  // パラメータ
  const inParam: CarePlan2SelectInEntity = {
    // 事業者ID
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    // 施設ID
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    // 利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    // 種別ID
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    // 計画書様式
    cksflg:
      (
        systemCommonsStore.searchSubsystemSettings(
          'cmn',
          systemCommonsStore.getSvJigyoId ?? ''
        ) as AuthzOtherCaremanagerType
      )?.planForm ?? '2',
    // 記録との連携
    kirokuRenkeiFlg: useCmnRouteCom().getInitialSettingMaster()?.cbKrkRenkeiFlg ?? '0',
    // 法人ID
    houjinId: systemCommonsStore.getHoujinId ?? '',
    // サービス事業者IDリスト
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList.slice() ?? [],
    // 事業者CDリスト
    svJigyoCdList: systemCommonsStore.getSvJigyoCdList.slice() ?? [],
  }
  // 計画書（２）の初期情報を取得する。
  const ret: CarePlan2SelectOutEntity = await ScreenRepository.select(
    'carePlan2InitSelect',
    inParam
  )
  // PL取込フラグ
  carePlan2Data.value.plFlg = ret.data.plFlg
  // 期間管理フラグ
  carePlan2Data.value.kikanFlg =
    ret.data.kikanFlg === OrX0042Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT_SERVER
      ? OrX0042Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT
      : OrX0042Const.PERIOD_MANAGEMENT_FLG.NO_MANAGEMENT
  await setScreenData(ret)
}
/**
 * 画面項目を取得したデータで設定する。
 *
 * @param ret - 初期情報 | 計画期間変更情報 | 履歴変更情報
 */
async function setScreenData(
  ret:
    | CarePlan2SelectOutEntity
    | Careplan2PlanPeriodChangeSelectOutEntity
    | Careplan2HistorySelectOutEntity
) {
  // 計画期間情報
  if ('kikanObj' in ret.data) {
    if (Array.isArray(ret.data.kikanObj)) {
      carePlan2Data.value.kikanObj = ret.data.kikanObj?.[0]
    } else {
      carePlan2Data.value.kikanObj = ret.data.kikanObj
    }
  }
  // 履歴情報
  if (Array.isArray(ret.data.rirekiObj)) {
    carePlan2Data.value.rirekiObj = ret.data.rirekiObj?.[0]
  } else {
    carePlan2Data.value.rirekiObj = ret.data.rirekiObj
  }
  if (carePlan2Data.value.rirekiObj) {
    // 計画書（２）ID
    carePlan2Data.value.ks21Id = carePlan2Data.value.rirekiObj.ks21Id
    // 有効期間ID
    termId.value = carePlan2Data.value.rirekiObj.termid
  } else {
    carePlan2Data.value.ks21Id = '0'
    termId.value = '0'
  }
  // 適用事業所一覧情報
  if ('jigyoList' in ret.data) {
    carePlan2Data.value.jigyoList = ret.data.jigyoList ?? []
  }
  carePlan2Data.value.keikasyo2List = []
  // 計画書（２）リスト
  for (const cp2Data of ret.data.keikasyo2List) {
    // // 計画書（２）ID
    carePlan2Data.value.keikasyo2List.push({
      // テーブルインデックス
      tableIndex: carePlan2Data.value.keikasyo2List.length,
      // カウンター
      ks22Id: cp2Data.ks22Id,
      // 計画書（２）ID
      ks21Id: carePlan2Data.value.ks21Id,
      //  通番
      seq: cp2Data.seq,
      //  給付対象
      hkyuKbn: cp2Data.hkyuKbn,
      //  サービス事業者ＣＤ
      jigyouId: cp2Data.jigyouId,
      //  サービス曜日リスト
      yobiList: cp2Data.yobiList,
      //  担当者リスト
      tantoList: cp2Data.tantoList,
      // チェックボックス選択フラグ
      checked: { modelValue: false },
      updateKbn: UPDATE_KBN.UPDATE,
      //  長期期間
      choKikan: {
        value: cp2Data.choKikanKnj
          ? cp2Data.choKikanKnj
          : cp2Data.choSYmd && cp2Data.choEYmd
            ? cp2Data.choSYmd + OrX0159Const.DEFAULT.LINE_BREAK_AND_WAVE + cp2Data.choEYmd
            : '',
        startValue: cp2Data.choKikanKnj ? '' : cp2Data.choSYmd,
        endValue: cp2Data.choKikanKnj ? '' : cp2Data.choEYmd,
      },
      //  短期期間
      tanKikan: {
        value: cp2Data.tanKikanKnj
          ? cp2Data.tanKikanKnj
          : cp2Data.tanSYmd && cp2Data.tanEYmd
            ? cp2Data.tanSYmd + OrX0159Const.DEFAULT.LINE_BREAK_AND_WAVE + cp2Data.tanEYmd
            : '',
        startValue: cp2Data.tanKikanKnj ? '' : cp2Data.tanSYmd,
        endValue: cp2Data.tanKikanKnj ? '' : cp2Data.tanEYmd,
      },
      //  期間
      kikan: {
        value: cp2Data.kikanKnj
          ? cp2Data.kikanKnj
          : cp2Data.kikanSYmd && cp2Data.kikanEYmd
            ? cp2Data.kikanSYmd + OrX0159Const.DEFAULT.LINE_BREAK_AND_WAVE + cp2Data.kikanEYmd
            : '',
        startValue: cp2Data.kikanKnj ? '' : cp2Data.kikanSYmd,
        endValue: cp2Data.kikanKnj ? '' : cp2Data.kikanEYmd,
      },
      /** 具体的 */
      gutaiteki: { value: cp2Data.gutaitekiKnj ?? '' },
      /** 長期 */
      chouki: { value: cp2Data.choukiKnj ?? '' },
      /** 短期 */
      tanki: { value: cp2Data.tankiKnj ?? '' },
      /** 介護 */
      kaigo: { value: cp2Data.kaigoKnj ?? '' },
      /** サービス種 */
      svShu: { value: cp2Data.svShuKnj ?? '' },
      /** 頻度 */
      hindo: { value: cp2Data.hindoKnj ?? '' },
      //  サービス事業者名
      jigyoName: { value: cp2Data.jigyoNameKnj ?? '' },
      // 週間日課
      // 「28-23 計画書（２）担当者（施設様式で使用）」又は「28-22 計画書（２）サービス、曜日」登録された場合「有」
      weekDailyRoute: {
        value:
          cp2Data.tantoList !== null || cp2Data.yobiList !== null
            ? t('label.presence')
            : t('label.absence'),
      },
      /** 課題番号 */
      kadaiNum: {
        value: cp2Data.kadaiNo ?? '',
      },
      /** 介護番号 */
      kaigoNum: {
        value: cp2Data.kaigoNo ?? '',
      },
      /** 給付文字 */
      hkyu: {
        modelValue: cp2Data.hkyuKnj ?? '',
      },
      /** 日課連動項目ID */
      nikkaId: { modelValue: cp2Data.nikkaId ?? '' },
    })
  }
  // 保険サービスリスト
  if (ret.data.hokenList) {
    carePlan2Data.value.hokenList = ret.data.hokenList
  } else {
    carePlan2Data.value.hokenList = []
  }
  // 月日指定リスト
  if (ret.data.tukihiList) {
    carePlan2Data.value.tukihiList = ret.data.tukihiList
  } else {
    carePlan2Data.value.tukihiList = []
  }

  // 計画対象期間
  // 計画期間が登録されている場合、計画期間リスト1件目.開始日 + " ～ " + 計画期間リスト1件目.終了日
  localOneway.orX0007Oneway.kindId = systemCommonsStore.getSyubetu ?? ''
  if (carePlan2Data.value.kikanObj) {
    localOneway.orX0007Oneway.planTargetPeriodData = {
      // 計画対象期間
      planTargetPeriod:
        carePlan2Data.value.kikanObj.startYmd + SPACE_WAVE + carePlan2Data.value.kikanObj.endYmd,
      // 計画対象期間期間ID
      planTargetPeriodId: Number(carePlan2Data.value.kikanObj.sc1Id),
      // 表示中の番号
      currentIndex: Number(carePlan2Data.value.kikanObj.kikanIndex),
      // 登録数
      totalCount: Number(carePlan2Data.value.kikanObj.kikanTotal),
    }
    local.orX0007.planTargetPeriodId = carePlan2Data.value.kikanObj.sc1Id
  } else {
    localOneway.orX0007Oneway.planTargetPeriodData = {
      planTargetPeriod: '',
      planTargetPeriodId: 0,
      currentIndex: 0,
      totalCount: 0,
    }
    local.orX0007.planTargetPeriodId = '0'
  }
  // 履歴
  if (carePlan2Data.value.rirekiObj) {
    // 計画書_履歴リストデータが存在する場合、計画書_履歴リスト.順番No + " / " + 計画書_履歴リストの総件数
    local.rireki.index = Number(carePlan2Data.value.rirekiObj.rirekiIndex)
    local.rireki.cnt = Number(carePlan2Data.value.rirekiObj.rirekiCnt)
  } else {
    // 上記以外の場合、初期値に戻す
    local.rireki.index = 0
    local.rireki.cnt = 0
  }
  if (!carePlan2Data.value.rirekiObj) {
    // 作成者名: 初期表示で計画書_履歴リストデータが存在しない、または、新規の場合、ログイン情報.職員名
    localOneway.orX0009Oneway.createData = {
      staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
      staffId: Number(systemCommonsStore.getCurrentUser.chkShokuId ?? '0'),
      createId: 0,
      createDate: systemCommonsStore.getSystemDate ?? '',
      currentIndex: 1,
      totalCount: 1,
      ks21Id: '0',
    }
    localComponents.orX0009 = {
      staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
      staffId: systemCommonsStore.getCurrentUser.chkShokuId ?? '0',
    }
    // 作成日: 初期表示で計画書_履歴リストデータが存在しない、または、新規の場合、引継情報.基準日
    local.orX0026.value = systemCommonsStore.getSystemDate ?? ''
  } else {
    // 上記以外の場合、計画書_履歴リスト1件目.職員番号の職員名
    localOneway.orX0009Oneway.createData = {
      staffName: carePlan2Data.value.rirekiObj.shokuKnj,
      staffId: Number(carePlan2Data.value.rirekiObj.shokuId),
      createId: Number(carePlan2Data.value.rirekiObj.ks21Id),
      createDate: carePlan2Data.value.rirekiObj.createYmd,
      currentIndex: Number(carePlan2Data.value.rirekiObj.rirekiIndex),
      totalCount: Number(carePlan2Data.value.rirekiObj.rirekiCnt),
      ks21Id: carePlan2Data.value.rirekiObj.ks21Id,
    }
    localComponents.orX0009 = {
      staffName: carePlan2Data.value.rirekiObj.shokuKnj,
      staffId: carePlan2Data.value.rirekiObj.shokuId ?? '0',
    }
    // 上記以外の場合、計画書_履歴リスト1件目.作成日
    local.orX0026.value = carePlan2Data.value.rirekiObj.createYmd
  }
  // 保険サービス表示
  // 保険サービスデータ件数＞０の場合、「あり」に表示する、以外の場合「なし」に表示する
  const insServiceCnts: number = carePlan2Data.value.hokenList.length || 0
  if (insServiceCnts > 0) {
    local.orX0027.labelText = t('label.having')
  } else {
    local.orX0027.labelText = t('label.none')
  }
  // データ-ページング
  orX0073Ref.value?.resetMo01338OnewayItemLabel()
  // 計画書_履歴リストが0件
  if (!carePlan2Data.value.rirekiObj) {
    // AC004-4を実行
    await createEmptyScreen()
  } else {
    historyUpdKbn.value = ''
    action.value = ''
  }
  localOneway.orX0165History.pageLabel =
    local.rireki.index + t('label.slash-with-space') + local.rireki.cnt
  await nextTick()
  setChildCpBinds(props.uniqueCpId, {
    OrX0026: {
      twoWayValue: local.orX0026,
    },
    OrX0009: {
      twoWayValue: localComponents.orX0009,
    },
  })
  refValue.value = JSON.parse(JSON.stringify(carePlan2Data.value)) as CarePlan2SelectOutData
  setChildCpBinds(props.parentUniqueCpId, {
    OrX0042: {
      twoWayValue: JSON.parse(JSON.stringify(carePlan2Data.value)),
    },
  })
}

/**
 * 初期表示
 */
async function initOr32525() {
  // 共通情報.メッセージ表示フラグが「1:表示する」、かつ、共通情報.サービス種類が「50010:介護予防支援」
  if (
    isFirstTimeInit.value &&
    useCmnRouteCom().getInitialSettingMaster()?.msgFlg === OrX0042Const.MESSAGE_SHOW_FLG.SHOW &&
    commonInfoData.serviceType === '50010'
  ) {
    // 以下のメッセージを表示: i.cmn.11276
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11276'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'blank',
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
  }
  isFirstTimeInit.value = false

  // 計画書（２）の初期情報を取得する。
  await getCarePlan2InitData()
  // オプションアイコンボタン: ログ: 初期表示：共通情報.e-文書法対象機能の電子ファイル保存設定区分が「1:適用する」の場合、表示
  if (
    commonInfoData.eDocumentLawTgtFunc.elecFileSaveType !== OrX0042Const.ELEC_FILE_SAVE_TYPE.APPLY
  ) {
    // 以外非表示
    OrX0014Logic.state.set({
      uniqueCpId: props.orx0014UniqueCpId,
      state: {
        showLogFlg: false,
      } as OrX0014StateType,
    })
  }
  // 計画対象期間: 期間管理フラグが「0:管理しない」の場合、非表示
  localFlg.value.showPlanningPeriodFlg =
    carePlan2Data.value.kikanFlg !== OrX0042Const.PERIOD_MANAGEMENT_FLG.NO_MANAGEMENT
  // 期間管理フラグが「1:管理する」、かつ、計画期間が登録されていない場合

  if (
    carePlan2Data.value.kikanFlg === OrX0042Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT &&
    !carePlan2Data.value.kikanObj
  ) {
    // 履歴非表示
    localFlg.value.showHistoryFlg = false
    // 作成者非表示
    localFlg.value.showAuthorFlg = false
    // 作成日非表示
    localFlg.value.showCreateDateFlg = false
  } else {
    // 履歴表示
    localFlg.value.showHistoryFlg = true
    // 作成者表示
    localFlg.value.showAuthorFlg = true
    // 作成日表示
    localFlg.value.showCreateDateFlg = true
  }
}

/**
 * 計画書（２）履歴変更処理
 *
 * @param rirekiPage - 履歴変更区分
 */
async function carePlan2HistUpdate(rirekiPage: string) {
  const param: Careplan2HistorySelectInEntity = {
    // 利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    // 事業者ID
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    // 計画対象期間ID
    sc1Id: local.orX0007.planTargetPeriodId || '0',
    // 履歴ID
    ks21Id: carePlan2Data.value.ks21Id,
    // 履歴インデックス
    rirekiIndex: String(local.rireki.index),
    // 履歴ページ区分
    rirekiPage: rirekiPage,
    /**
     * 計画書様式
     */
    cksflg:
      (
        systemCommonsStore.searchSubsystemSettings(
          'cmn',
          systemCommonsStore.getSvJigyoId ?? ''
        ) as AuthzOtherCaremanagerType
      )?.planForm ?? '2',
    // 記録との連携
    kirokuRenkeiFlg: useCmnRouteCom().getInitialSettingMaster()?.cbKrkRenkeiFlg ?? '0',
  }
  // 計画書（２）計画期間変更処理を行う。
  const ret: Careplan2HistorySelectOutEntity = await ScreenRepository.select(
    'carePlan2HistorySelect',
    param
  )
  histUpdFlg.value = UPDATE_KBN.UPDATE
  await setScreenData(ret)
}

/**
 * 計画書（２）計画期間変更
 *
 * @param kikanPage - 計画期間変更区分
 */
async function carePlan2PlanPeriodUpdate(kikanPage: string) {
  const param: Careplan2PlanPeriodChangeSelectInEntity = {
    /**
     * 事業者ID
     */
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    /**
     * 施設ID
     */
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    /**
     * 利用者ID
     */
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    /**
     * 種別ID
     */
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    /**
     * 法人ID
     */
    houjinId: systemCommonsStore.getHoujinId ?? '',
    /**
     * 計画対象期間ID
     */
    sc1Id: local.orX0007.planTargetPeriodId || '0',
    /**
     * 計画対象期間インデックス
     */
    kikanIndex: String(localOneway.orX0007Oneway.planTargetPeriodData.currentIndex),
    /**
     * 計画期間ページ区分
     */
    kikanPage: kikanPage,
    /**
     * 計画書様式
     */
    cksflg:
      (
        systemCommonsStore.searchSubsystemSettings(
          'cmn',
          systemCommonsStore.getSvJigyoId ?? ''
        ) as AuthzOtherCaremanagerType
      )?.planForm ?? '2',
    // 記録との連携
    kirokuRenkeiFlg: useCmnRouteCom().getInitialSettingMaster()?.cbKrkRenkeiFlg ?? '0',
  }
  // 計画書（２）計画期間変更処理を行う。
  const ret: Careplan2PlanPeriodChangeSelectOutEntity = await ScreenRepository.select(
    'carePlan2PlanPeriodSelect',
    param
  )
  histUpdFlg.value = UPDATE_KBN.UPDATE
  await setScreenData(ret)
}

/**
 * 「新規ボタン」押下
 */
async function onClickCreate() {
  // 期間管理フラグが「管理する」の場合、かつ、計画対象期間リストが0件
  if (
    refValue.value?.kikanFlg === OrX0042Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT &&
    (refValue.value?.kikanObj === null || refValue.value?.kikanObj === undefined)
  ) {
    // 以下のメッセージを表示: e.cmn.40980
    const rs = await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40980'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンタイプ
      secondBtnType: 'blank',
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    // はい：AC014を実行
    if (rs.firstBtnClickFlg) {
      await planTargetPeriodIconClick()
    }
  } else {
    // 画面入力データの変更がある場合
    const isEdit = isEditNavControl(watchedComponents.value)
    if (isEdit) {
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        // はいの場合
        // AC003を実行し、処理続き
        await onClickSave()
      } else if (rs.secondBtnClickFlg) {
        // いいえの場合
        //  履歴更新区分="C"(新規)の場合
        if (historyUpdKbn.value === OrX0042Const.HISTORY_UPD_KBN.C) {
          // 以下のメッセージを表示: i.cmn.11265
          await showMessageICmn11265()
          return
        }
      } else {
        return
      }
    } else {
      //  履歴更新区分="C"(新規)の場合
      if (historyUpdKbn.value === OrX0042Const.HISTORY_UPD_KBN.C) {
        // 以下のメッセージを表示: i.cmn.11265
        await showMessageICmn11265()
        return
      }
    }
    await createEmptyScreen()
  }
}
/**
 * 新規空白画面を作成
 */
async function createEmptyScreen() {
  // 新規空白画面を作成し、デーフォルトデータを画面対応の項目に設定する。
  refValue.value!.keikasyo2List = []
  refValue.value!.kikanFlg = ''
  refValue.value!.rirekiObj = null
  refValue.value!.jigyoList = []
  localComponents.orX0004 = defaultComponents.orX0004
  localComponents.orX0005 = defaultComponents.orX0005
  localComponents.orX0009 = defaultComponents.orX0009
  localComponents.or21828 = defaultComponents.or21828
  localComponents.or21830 = defaultComponents.or21830
  localComponents.orX0014 = defaultComponents.orX0014
  // ・作成日 ＝ 引継情報.基準日(画面項目定義書第2階層の日付)
  local.orX0026.value = systemCommonsStore.getSystemDate ?? ''
  // ・作成者 ＝ 共通情報.ログイン職員情報.職員ID対応の職員を設定する。
  localComponents.orX0009 = {
    staffName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
    staffId: systemCommonsStore.getCurrentUser.chkShokuId ?? '0',
  }
  // ・履歴ページングは履歴の最大値+1/履歴の最大値+1を設定する。
  local.rireki.index = local.rireki.index + 1
  local.rireki.cnt = local.rireki.cnt + 1
  localOneway.orX0165History.pageLabel =
    local.rireki.index + t('label.slash-with-space') + local.rireki.cnt
  // ・履歴更新区分 = "C"
  historyUpdKbn.value = OrX0042Const.HISTORY_UPD_KBN.C
  action.value = OrX0042Const.PAGE_ACTION.NEW
  // データ-ページング
  orX0073Ref.value?.resetMo01338OnewayItemLabel()
  await nextTick()
  setChildCpBinds(props.uniqueCpId, {
    OrX0026: {
      twoWayValue: local.orX0026,
    },
    OrX0009: {
      twoWayValue: localComponents.orX0009,
    },
  })
  refValue.value = JSON.parse(JSON.stringify(carePlan2Data.value)) as CarePlan2SelectOutData
  setChildCpBinds(props.parentUniqueCpId, {
    OrX0042: {
      twoWayValue: carePlan2Data.value,
    },
  })
  localOneway.orX0073Oneway.tableItem = []
  orX0073Ref.value.initData(localOneway.orX0073Oneway)
  // データ-ページング
  orX0073Ref.value?.resetMo01338OnewayItemLabel()

  // 有効期間IDを取得する
  const retData: CarePlan2TermIdSelectEntity = await ScreenRepository.select(
    'validPeriodIdSelect',
    {}
  )
  termId.value = retData.data.termId
}
/**
 * 「複写ボタン」押下
 */
function onClickCpy() {
  // 種別ID
  localOneway.or57737Oneway.serviceType = systemCommonsStore.getSyubetu ?? ''
  // 期間管理フラグ
  localOneway.or57737Oneway.periodManagementFlg = carePlan2Data.value.kikanFlg
  // 連動フラグ
  localOneway.or57737Oneway.linkageFlg = commonInfoData.carePlan2Mastr.rendouFlag
  // 計画書様式フラグ
  localOneway.or57737Oneway.cksFlg =
    (
      systemCommonsStore.searchSubsystemSettings(
        'cmn',
        systemCommonsStore.getSvJigyoId ?? ''
      ) as AuthzOtherCaremanagerType
    )?.planForm ?? '2'
  // 記録との連携フラグ
  localOneway.or57737Oneway.kirokuRenkeiFlg =
    useCmnRouteCom().getInitialSettingMaster()?.cbKrkRenkeiFlg ?? '0'

  // 一覧複写画面をポップアップで起動する。
  Or57737Logic.state.set({
    uniqueCpId: or57737.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
function isEditNavControl(components: string[]) {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()

  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }

  return false
}

/**
 * 「計画書一括印刷」押下
 */
async function onClickBatchPrintOfPlans() {
  // AC004-2と同じ
  // 画面入力データの変更がある場合
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      await onClickSave()
    }
  }
  // GUI00936 計画書一括印刷画面をポップアップで起動する。
  // 共通情報.法人ID
  localOneway.or10583Oneway.carePlanPrintAllInData.legalPersonId =
    systemCommonsStore.getHoujinId ?? ''
  // 共通情報.施設ID
  localOneway.or10583Oneway.carePlanPrintAllInData.shisetuId = systemCommonsStore.getShisetuId ?? ''
  // 共通情報.事業所ID
  localOneway.or10583Oneway.carePlanPrintAllInData.officeData.officeId =
    systemCommonsStore.getSvJigyoId ?? ''
  // 共通情報.計画書様式
  localOneway.or10583Oneway.carePlanPrintAllInData.carePlanStyle =
    (
      systemCommonsStore.searchSubsystemSettings(
        'cmn',
        systemCommonsStore.getSvJigyoId ?? ''
      ) as AuthzOtherCaremanagerType
    )?.planForm ?? '2'
  // 共通情報.職員ID
  localOneway.or10583Oneway.carePlanPrintAllInData.employeeId = localComponents.orX0009.staffId
  // 共通情報.機能拡張権限フラグ
  localOneway.or10583Oneway.setInputComponentsFlg = commonInfoData.setInputComponentsFlg
  // 共通情報.事業所CD
  localOneway.or10583Oneway.carePlanPrintAllInData.jigyoTypeCd =
    systemCommonsStore.getSvJigyoCd ?? ''
  // 入力支援ポップアップを開く
  Or10583Logic.state.set({
    uniqueCpId: or10583.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr10583 = computed(() => {
  return Or10583Logic.state.get(or10583.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「印刷設定」押下
 */
async function onClickPrintConfig() {
  // AC004-2と同じ
  // 画面入力データの変更がある場合
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      await onClickSave()
    }
  }
  // GUI01034_印刷設定画面をポップアップで起動する。
  // 共通情報.利用者情報リスト
  localOneway.or26610Oneway.userList = commonInfoData.userList
  // 共通情報.利用者ID
  localOneway.or26610Oneway.userId = localComponents.orX0009.staffId
  // 変数.セクション名
  localOneway.or26610Oneway.sectionName = commonInfoData.sectionName
  // 共通情報.選択帳票番号
  localOneway.or26610Oneway.choIndex = commonInfoData.choIndex
  // 共通情報.期間管理フラグ
  localOneway.or26610Oneway.kikanFlg = carePlan2Data.value.kikanFlg
  // 画面が選択した履歴のID
  localOneway.or26610Oneway.historyId = carePlan2Data.value.ks21Id
  // 入力支援ポップアップを開く
  Or26610Logic.state.set({
    uniqueCpId: or26610.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr26610 = computed(() => {
  return Or26610Logic.state.get(or26610.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「計画書（２）マスタアイコンボタン」押下
 */
async function onCarePlan2MasterClick() {
  // 画面入力データに変更がある場合	-
  // AC004-2と同じ
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      await onClickSave()
    }
  }
  // TODO GUI01012_計画書（2）マスタ画面をポップアップで起動する。
}

/**
 * 「オプションア」＞「削除」押下
 */
async function onOrX0014Delete() {
  // 以下のメッセージを表示: i.cmn.11326
  // 現在表示している{0}の{1}のデータを削除してもよろしいですか？
  // {0}:画面.作成日
  // {1}:計画書（２）
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11326', [local.orX0026.value, t('label.full-jp-care-plan2')]),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'normal3',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  })
  if (rs.firstBtnClickFlg) {
    action.value = OrX0042Const.PAGE_ACTION.DELETE

    // AC003（更新区分をD:削除に設定）を実行
    if (refValue.value?.keikasyo2List) {
      refValue.value.keikasyo2List.forEach((item) => {
        item.updateKbn = UPDATE_KBN.DELETE
      })
      localOneway.orX0073Oneway.tableItem = [...refValue.value.keikasyo2List]
      orX0073Ref.value.initData(localOneway.orX0073Oneway)
    }
  }
}
/**
 * 「オプションア」＞「ログ」押下
 */
function onOrX0014Log() {
  // TODO 共通処理 e-文章法対象の帳票のXPSフォルダをエクスプローラで開く。
}

/**
 * 「履歴選択選択アイコンボタン」押下
 *
 * @param skipEditFlg - 画面入力データ変更判定スキップフラグ
 */
async function onClickHistIconBtn(skipEditFlg?: boolean) {
  // 画面入力データに変更がある場合
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit && (skipEditFlg === undefined || skipEditFlg === false)) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // はいの場合
      // AC003を実行し、処理続き
      await onClickSave()
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }
  // GUI01011 履歴選択画面をポップアップで起動する。
  // 入力パラメータ: 共通情報.事業所ID, 画面.計画対象期間ID, 共通情報.利用者ID, 画面.計画書ID
  localOneway.or29104Oneway.historySelectionsInfoType = {
    sc1Id: String(local.orX0007.planTargetPeriodId),
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
  }
  localOneway.or29104Oneway.carePlanStyle = useSystemCommonsStore().getPlanNumber ?? '1' // TBD パラメータなし
  localOneway.or29104Oneway.carePlanId = carePlan2Data.value.ks21Id
  Or29104Logic.state.set({
    uniqueCpId: or29104.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr29104 = computed(() => {
  return Or29104Logic.state.get(or29104.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「履歴-前へアイコンボタン」押下
 */
async function onClickPreHistBtn() {
  // 1件目の履歴データが表示されている状態
  if (local.rireki.index === 1) {
    // 処理終了にする。
    return
  }
  const isEdit = isEditNavControl(watchedComponents.value)
  // 画面入力データに変更がある場合
  if (isEdit) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // はいの場合
      // AC003を実行し、処理続き
      await onClickSave()
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }
  // 計画書（２）計画期間履歴処理を行う。
  await carePlan2HistUpdate(OrX0042Const.HISTORY_CHANGE_KBN.BEFORE_SELECTED_ID)
}
/**
 * 「履歴-次へアイコンボタン」押下
 */
async function onClickNextHistBtn() {
  // 最終件目の履歴データが表示されている状態
  if (local.rireki.index === local.rireki.cnt) {
    // 処理終了にする。
    return
  }
  const isEdit = isEditNavControl(watchedComponents.value)
  // 画面入力データに変更がある場合
  if (isEdit) {
    // AC004-2と同じ
    const rs = await showMessageICmn10430()
    if (rs.firstBtnClickFlg) {
      // はいの場合
      // AC003を実行し、処理続き
      await onClickSave()
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }
  // 計画書（２）計画期間履歴処理を行う。
  await carePlan2HistUpdate(OrX0042Const.HISTORY_CHANGE_KBN.AFTER_SELECTED_ID)
}

/**
 * 「保険サービス登録入力支援アイコンボタ」押下
 */
// #159464 画面の遷移は、一覧明細部の「 保険サービスで入力 」入力補助アイコンに実装
// async function onClickInsServiceInputHelp() {
//   // システム基準日を取得する
//   const param: ValidPeriodIdSelectInEntity = {
//     // 法人ID
//     houjinId: commonInfoData.houjinId,
//     // 施設ID
//     shisetuId: commonInfoData.shisetsuId,
//     // 共通情報.利用者ID
//     userId: systemCommonsStore.getUserSelectSelfId() ?? '',
//     svJigyoId: commonInfoData.svJigyoId,
//     // 期間管理ID: 期間管理フラグが「管理する」の場合:  期間管理ID 上記以外: 0
//     sc1Id:
//       refValue.value?.kikanFlg === OrX0042Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT
//         ? (refValue.value?.kikanObj?.sc1Id ?? '')
//         : '0',
//     // 作成日：期間管理フラグが「管理する」の場合: 作成日 上記以外: 画面取得作成日
//     createYmd:
//       refValue.value?.kikanFlg === OrX0042Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT
//         ? (refValue.value?.rirekiObj?.createYmd ?? '')
//         : local.orX0026.value,
//   }
//   const ret: ValidPeriodIdSelectOutEntity = await ScreenRepository.select(
//     'validPeriodIdSelect',
//     param
//   )
//   systemBasicDt.value = ret.data.sysKijunbi
//   // GUI01091_保険サービス登録画面をポップアップで起動する。
//   // 共通情報.利用者ID
//   localOneway.or10878Oneway.userId = localComponents.orX0009.staffId
//   // 共通情報.事業所CD
//   localOneway.or10878Oneway.svJigyoCd = commonInfoData.svJigyoCd
//   // 共通情報.事業所ID
//   localOneway.or10878Oneway.svJigyoId = commonInfoData.svJigyoId
//   // AC023で取得したシステム基準日
//   localOneway.or10878Oneway.sysYmd = systemBasicDt.value
//   // 画面.有効期間ID
//   localOneway.or10878Oneway.termid = carePlan2Data.value.rirekiObj?.termid ?? ''
//   // 機能ID=0
//   localOneway.or10878Oneway.functionId = OrX0042Const.FUNCTION_ID_ZERO
//   // 呼出元フラグ：0 画面上部ボタンから
//   localOneway.or10878Oneway.callerFlag = OrX0042Const.CALLER_FLAG_ZERO
//   // 入力支援ポップアップを開く
//   Or10878Logic.state.set({
//     uniqueCpId: or10878.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
// #159464 画面の遷移は、一覧明細部の「 保険サービスで入力 」入力補助アイコンに実装
// const showDialogOr10878 = computed(() => {
//   return Or10878Logic.state.get(or10878.value.uniqueCpId)?.isOpen ?? false
// })

/**
 * 「保存ボタン」押下
 *
 * @param reInit - 初期表示フラグ
 */
async function onClickSave(reInit?: boolean) {
  // 画面入力データに変更がない場合, 処理終了にする。
  const isEdit = isEditNavControl(watchedComponents.value)
  if (action.value !== OrX0042Const.PAGE_ACTION.DELETE && !isEdit) {
    return
  }

  // 共通情報.記録との連携＝1(記録との連携を行う)且つ、
  // 入力内容詳細表の課題番号、番号、連動項目のいずれが未入力の場合
  if (
    useCmnRouteCom().getInitialSettingMaster()?.cbKrkRenkeiFlg ===
    OrX0042Const.KIROKU_RENKEI_FLG.TRUE
  ) {
    const missingItems: string[] = []
    let hasEmptyKadaiNum = false
    let hasEmptyKaigoNum = false
    let hasEmptyNikkaId = false

    for (const data of refValue.value?.keikasyo2List ?? []) {
      if (data.updateKbn === UPDATE_KBN.DELETE) {
        continue
      }

      if (!data.kadaiNum.value) {
        hasEmptyKadaiNum = true
      }
      if (!data.kaigoNum.value) {
        hasEmptyKaigoNum = true
      }
      if (!data.nikkaId.modelValue) {
        hasEmptyNikkaId = true
      }
    }
    if (hasEmptyKadaiNum) {
      missingItems.push(t('label.issues-number-2'))
    }
    if (hasEmptyKaigoNum) {
      missingItems.push(t('label.number'))
    }
    if (hasEmptyNikkaId) {
      missingItems.push(t('label.linking-item'))
    }

    if (missingItems.length > 0) {
      await openErrorDialog(or21813.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.error'),
        // ダイアログテキスト
        dialogText: t('message.e-com-10036', [missingItems.join(t('label.comma'))]),
        // 第1ボタンタイプ
        firstBtnType: 'normal1',
        // 第1ボタンラベル
        firstBtnLabel: t('btn.yes'),
        // 第2ボタンタイプ
        secondBtnType: 'blank',
        // 第3ボタンタイプ
        thirdBtnType: 'blank',
      })
      return
    }
  }

  // 計画対象期間更新区分
  let periodUpdFlg = UPDATE_KBN.NONE
  // 履歴更新区分
  if (action.value === OrX0042Const.PAGE_ACTION.NEW) {
    periodUpdFlg = UPDATE_KBN.CREATE
    histUpdFlg.value = UPDATE_KBN.CREATE
  } else if (action.value === OrX0042Const.PAGE_ACTION.DELETE) {
    periodUpdFlg = UPDATE_KBN.UPDATE
    histUpdFlg.value = UPDATE_KBN.DELETE
  } else {
    periodUpdFlg = UPDATE_KBN.UPDATE
    histUpdFlg.value = UPDATE_KBN.UPDATE
  }

  // パラメータ
  const param: Careplan2InsertInEntity = {
    // 計画対象期間更新区分
    updateKbn: periodUpdFlg,
    // 計画対象期間ID
    sc1Id: String(local.orX0007.planTargetPeriodId),
    // 法人ID
    houjinId: systemCommonsStore.getHoujinId ?? '',
    // 施設ID
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    // 利用者ID
    userId: systemCommonsStore.getUserSelectSelfId() ?? '',
    // 事業者ID
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    // 種別ID
    syubetuId: systemCommonsStore.getSyubetu ?? '',
    // 履歴更新区分
    historyUpdateKbn: histUpdFlg.value,
    // 計画書（２）ID
    ks21Id: carePlan2Data.value.ks21Id,
    // 作成日
    createYmd: local.orX0026.value,
    // 作成者ID
    shokuId: String(localComponents.orX0009.staffId ?? ''),
    // 有効期間ID
    termid: termId.value,
    // 計画書様式フラグ
    cksflg:
      (
        systemCommonsStore.searchSubsystemSettings(
          'cmn',
          systemCommonsStore.getSvJigyoId ?? ''
        ) as AuthzOtherCaremanagerType
      )?.planForm ?? '2',
    // 計画書（２）リスト
    keikasyo2List: [],
    /** 保険サービスリ */
    hokenList: [],
    /** 月日指定リスト */
    tukihiList: [],
    // 記録との連携
    kirokuRenkeiFlg: useCmnRouteCom().getInitialSettingMaster()?.cbKrkRenkeiFlg ?? '0',
  }
  const keikasyo2List: Keikasyo2Insert[] = []
  // edit 計画書（２）リスト
  for (const data of refValue.value?.keikasyo2List ?? []) {
    // サービス曜日リスト
    let yobiList: Yobi[] = []
    if (Array.isArray(data.yobiList)) {
      yobiList = [...data.yobiList]
    }
    // 担当者リスト
    let tantoList: Tanto[] = []
    if (Array.isArray(data.tantoList)) {
      tantoList = [...data.tantoList]
    }
    // 計画書（２）リスト
    keikasyo2List.push({
      // 計画書（２）更新区分
      updateKbn: data.updateKbn,
      // カウンター
      ks22Id: data.ks22Id,
      // 計画書（２）ID
      ks21Id: carePlan2Data.value.ks21Id,
      // 具体的
      gutaitekiKnj: data.gutaiteki.value,
      // 長期
      choukiKnj: data.chouki.value,
      // 短期
      tankiKnj: data.tanki.value,
      // 介護
      kaigoKnj: data.kaigo.value,
      // サービス種 */
      svShuKnj: data.svShu.value,
      //  頻度 */
      hindoKnj: data.hindo.value,
      //  期間 */
      kikanKnj: data.kikan.value,
      /** 通番 */
      seq: data.seq,
      /** 課題番号 */
      kadaiNo: data.kadaiNum.value,
      /** 介護番号 */
      kaigoNo: data.kaigoNum.value,
      /** 長期期間 */
      choKikanKnj: data.choKikan.value,
      /** 短期期間 */
      tanKikanKnj: data.tanKikan.value,
      /** 給付対象 */
      hkyuKbn: data.hkyuKbn,
      /** サービス事業者ＣＤ */
      jigyouId: data.jigyouId,
      /** サービス事業者名 */
      jigyoNameKnj: data.jigyoName.value,
      /** 給付文字 */
      hkyuKnj: data.hkyu.modelValue,
      /** 長期期間開始日 */
      choSYmd: data.choKikan.value ? '' : (data.choKikan.startValue ?? ''), // #158755 空白で登録
      /** 長期期間終了日 */
      choEYmd: data.choKikan.value ? '' : (data.choKikan.endValue ?? ''), // #158755 空白で登録
      /** 短期期間開始日 */
      tanSYmd: data.tanKikan.value ? '' : (data.tanKikan.startValue ?? ''), // #158755 空白で登録
      /** 短期期間終了日 */
      tanEYmd: data.tanKikan.value ? '' : (data.tanKikan.endValue ?? ''), // #158755 空白で登録
      /** 期間開始日 */
      kikanSYmd: data.kikan.value ? '' : (data.kikan.startValue ?? ''), // #158755 空白で登録
      /** 期間終了日 */
      kikanEYmd: data.kikan.value ? '' : (data.kikan.endValue ?? ''), // #158755 空白で登録
      /** 日課連動項目ID */
      nikkaId: data.nikkaId.modelValue,
      // サービス曜日リスト
      yobiList: yobiList,
      // 担当者リスト
      tantoList: tantoList,
    })
  }
  param.keikasyo2List = keikasyo2List

  // 保険サービスリ
  param.hokenList = carePlan2Data.value.hokenList ?? []
  // 月日指定リスト
  param.tukihiList = carePlan2Data.value.tukihiList ?? []

  await ScreenRepository.insert('carePlan2Update', param)
  //AC001-2,3,4と同じ: 初期表示
  if (reInit) {
    void carePlan2HistUpdate(OrX0042Const.HISTORY_CHANGE_KBN.SELECTED_ID)
  }
}

/**
 * 追加ボタン押下
 *
 * @param keikasyo2List - 選択したの計画書(2)データ
 */
function onCpyClickAppend(keikasyo2List: Keikasyo2[]) {
  if (!refValue.value?.keikasyo2List) {
    refValue.value!.keikasyo2List = []
  }
  keikasyo2List.forEach((item) => {
    refValue.value!.keikasyo2List.push(item)
  })
  // データ-ページング
  orX0073Ref.value?.resetMo01338OnewayItemLabel()
  localOneway.orX0073Oneway.tableItem = [...refValue.value!.keikasyo2List]
  orX0073Ref.value.initData(localOneway.orX0073Oneway)
}
/**
 * 上書ボタン押下
 *
 * @param keikasyo2List - 選択したの計画書(2)データ
 */
async function onCpyClickOverwrite(keikasyo2List: Keikasyo2[]) {
  await nextTick()
  refValue.value!.keikasyo2List = keikasyo2List
  // データ-ページング
  orX0073Ref.value?.resetMo01338OnewayItemLabel()
  localOneway.orX0073Oneway.tableItem = [...keikasyo2List]
  orX0073Ref.value.initData(localOneway.orX0073Oneway)
}

/**
 * 確認ダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - 確認ダイアログOneWayBind領域
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    or21814ResolvePromise = resolve
  })
}
/**
 * エラーダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - エラーダイアログOneWayBind領域
 */
function openErrorDialog(uniqueCpId: string, state: Or21813StateType) {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21813EventType>((resolve) => {
    or21813ResolvePromise = resolve
  })
}

/**
 * 確認メッセージダイアログ開く(i-cmn-10430)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn10430() {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'destroy1',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'normal3',
    // 第3ボタンラベル
    thirdBtnLabel: t('btn.cancel'),
  })
  return rs
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11265)
 */
async function showMessageICmn11265() {
  await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11265', [t('label.full-jp-care-plan2')]),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'blank',
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  } as Or21814OnewayType)
}

/**
 * AC014_「計画対象期間選択アイコンボタン」押下
 */
async function planTargetPeriodIconClick() {
  // 画面入力データに変更がある場合
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    // AC004-2と同じ
    const dialogResult = await showMessageICmn10430()
    if (dialogResult.firstBtnClickFlg) {
      // AC004-2と同じ
      await onClickSave()
    } else if (dialogResult.thirdBtnClickFlg) {
      return
    }
    // GUI00070 対象期間画面をポップアップで起動する
    Gui00070Logic.state.set({
      uniqueCpId: gui00070.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}
/**
 * GUI01091_保険サービス登録画面「確定」ボタン押下
 *
 * @param data - GUI01091_保険サービス登録画面情報
 */
// #159464 画面の遷移は、一覧明細部の「 保険サービスで入力 」入力補助アイコンに実装
// function onClickInsServiceConfirm(data: Or10878Type) {
//   //登録サービスデータのサイズ＞０の場合
//   if (data && data.insuranceServiceImportList.length > 0) {
//     //「あり」
//     local.orX0027.labelText = t('label.having')
//   } else {
//     //以外の場合「ない」で設定する。
//     local.orX0027.labelText = t('label.none')
//   }
// }
/**
 * 「アセスメントの入力支援アイコンボタン」押下
 */
function onClickAssessment() {
  orX0073Ref.value?.onClickAssessment()
}
/**
 * 「課題整理総括の入力支援アイコンボタン」押下
 */
function onClickIssues() {
  orX0073Ref.value?.onClickIssues()
}
/**
 * 「SDケアの入力支援アイコンボタン」押下
 */
function onClickSdCare() {
  orX0073Ref.value?.onClickSdCare()
}

defineExpose({
  onClickCreate,
  onClickCpy,
  onOrX0014Delete,
  onOrX0014Log,
  onClickSave,
  onClickBatchPrintOfPlans,
  onClickPrintConfig,
  onCarePlan2MasterClick,
  onClickAssessment,
  onClickIssues,
  onClickSdCare,
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * Or21813のイベントを監視
 *
 * @description
 * またOr21813のボタン押下フラグをリセットする。
 */
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21813ResolvePromise !== undefined && newValue !== undefined) {
      or21813ResolvePromise(newValue)
    }
    return
  }
)
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21814ResolvePromise !== undefined && newValue !== undefined) {
      or21814ResolvePromise(newValue)
    }
    return
  }
)

// 履歴ID選択変更の監視
watch(
  () => local.or29104,
  () => {
    carePlan2Data.value.ks21Id = local.or29104.ks21Id
    void carePlan2HistUpdate(OrX0042Const.HISTORY_CHANGE_KBN.SELECTED_ID)
  }
)

// 「保険サービス登録入力支援アイコンボタ」押下
// #159464 画面の遷移は、一覧明細部の「 保険サービスで入力 」入力補助アイコンに実装
// watch(
//   () => OrX0027Logic.event.get(orX0027.value.uniqueCpId),
//   (value) => {
//     if (value?.eventFlg) {
//       // 「保険サービス登録入力支援アイコンボタ」押下
//       void onClickInsServiceInputHelp()
//     }
//   }
// )
watch(
  () => local.orX0073.tableItem,
  async (newVal) => {
    await nextTick()
    if (JSON.stringify(newVal) !== JSON.stringify(refValue.value?.keikasyo2List)) {
      refValue.value!.keikasyo2List = newVal
    }
  },
  { deep: true, immediate: true }
)
watch(
  () => carePlan2Data.value.keikasyo2List,
  async (newVal) => {
    await nextTick()
    localOneway.orX0073Oneway.tableItem = newVal ?? []
    orX0073Ref.value?.initData(localOneway.orX0073Oneway)
  }
)
watch([() => local.orX0007.planTargetPeriodId, () => refValue.value?.kikanFlg], () => {
  localOneway.orX0073Oneway.planTargetPeriodId = String(local.orX0007.planTargetPeriodId)
  localOneway.orX0073Oneway.periodManageFlag = refValue.value?.kikanFlg ?? ''
  orX0073Ref.value?.initData(localOneway.orX0073Oneway)
})
// GUI01091_保険サービス登録画面「確定」ボタンで戻った場合
// #159464 画面の遷移は、一覧明細部の「 保険サービスで入力 」入力補助アイコンに実装
// watch(
//   () => local.or10878Type,
//   (newValue) => {
//     onClickInsServiceConfirm(newValue)
//   },
//   { deep: true, immediate: true }
// )

/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    const planID = newValue.planTargetPeriodId
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg

    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }

    //更新フラグINIT
    OrX0007Logic.data.set({
      uniqueCpId: orX0007.value.uniqueCpId,
      value: {
        planTargetPeriodId: planID,
        PlanTargetPeriodUpdateFlg: '',
      },
    })

    // 事前チェック
    if (planUpdateFlg === '0') {
      //「期間-選択確認前 アイコンボタン」押下
      if (carePlan2Data.value.kikanObj?.sc1Id === planID) {
        // 選択前の対象期間から変更がない場合
        return
      }
    } else if (planUpdateFlg === '1') {
      //「期間-前へ アイコンボタン」押下
      if ((Number(carePlan2Data.value.kikanObj?.kikanIndex) || 0) <= 1) {
        // 1件目の計画対象期間データが表示されている状態の場合
        await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11262'),
          // 第1ボタンタイプ
          firstBtnType: 'normal1',
          // 第1ボタンラベル
          firstBtnLabel: t('btn.yes'),
          // 第2ボタンボタンタイプ
          secondBtnType: 'blank',
          // 第3ボタンタイプ
          thirdBtnType: 'blank',
        })
        return
      }
    } else if (planUpdateFlg === '2') {
      //「期間-次へ アイコンボタン」押下
      if (carePlan2Data.value.kikanObj?.kikanIndex === carePlan2Data.value.kikanObj?.kikanTotal) {
        // 最終件目の計画対象期間データが表示されている状態の場合
        await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11263'),
          // 第1ボタンタイプ
          firstBtnType: 'normal1',
          // 第1ボタンラベル
          firstBtnLabel: t('btn.yes'),
          // 第2ボタンボタンタイプ
          secondBtnType: 'blank',
          // 第3ボタンタイプ
          thirdBtnType: 'blank',
        })
        return
      }
    }

    // 画面入力データの変更がある場合
    const isEdit = isEditNavControl(watchedComponents.value)
    if (isEdit) {
      // AC004-2と同じ
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        // はいの場合
        // AC003を実行し、処理続き
        await onClickSave()
      } else if (rs.thirdBtnClickFlg) {
        return
      }
    }

    if (planUpdateFlg === '0') {
      //「期間-選択確認前 アイコンボタン」押下
      local.orX0007.planTargetPeriodId = planID
      await carePlan2PlanPeriodUpdate('0')
    } else if (planUpdateFlg === '1') {
      //「期間-前へ アイコンボタン」押下
      await carePlan2PlanPeriodUpdate('1')
    } else if (planUpdateFlg === '2') {
      //「期間-次へ アイコンボタン」押下
      await carePlan2PlanPeriodUpdate('2')
    }
  },
  { deep: true }
)
/**
 * 初期化フラグの監視
 */
watch(initOr32525Flg, (newValue) => {
  if (newValue) {
    void initOr32525().finally(() => {
      initOr32525Flg.value = false
    })
  }
})
/**
 * ページアクションの監視
 */
watch(action, () => {
  if (action.value === OrX0042Const.PAGE_ACTION.DELETE) {
    // 履歴削除の場合、ボタンを非活性
    localOneway.orX0026Oneway.disabled = true // 作成者
    OrX0009Logic.state.set({
      uniqueCpId: orX0009.value.uniqueCpId,
      state: {
        isDisabled: true,
      },
    }) // 作成日
    local.orX0027.labelText = '' // 保険サービス
  } else {
    localOneway.orX0026Oneway.disabled = false
    OrX0009Logic.state.set({
      uniqueCpId: orX0009.value.uniqueCpId,
      state: {
        isDisabled: false,
      },
    })
    const insServiceCnts: number = carePlan2Data.value.hokenList?.length ?? 0
    if (insServiceCnts > 0) {
      local.orX0027.labelText = t('label.having')
    } else {
      local.orX0027.labelText = t('label.none')
    }
  }
})
</script>
<template>
  <c-v-col class="hidden-scroll">
    <!-- コンテンツエリア -->
    <c-v-sheet class="content d-flex flex-column">
      <c-v-row
        no-gutters
        class="px-6 ga-6 flex-0-0"
      >
        <!-- 事業所 -->
        <c-v-col
          cols="auto"
          class="pa-0"
        >
          <g-base-or41179
            v-bind="or41179"
            width="203"
            class="custom-jigyo"
          />
        </c-v-col>
        <!-- 計画対象期間 -->
        <c-v-col
          v-show="localFlg.showPlanningPeriodFlg"
          cols="auto"
          class="custom-plan"
        >
          <g-custom-orX0007
            v-bind="orX0007"
            :oneway-model-value="localOneway.orX0007Oneway"
            :parent-method="
              () => {
                return true
              }
            "
          />
        </c-v-col>
        <!-- 作成日 -->
        <c-v-col
          v-if="localFlg.showCreateDateFlg"
          cols="auto"
        >
          <g-custom-or-x-0026
            v-bind="orX0026"
            v-model="local.orX0026"
            :oneway-model-value="localOneway.orX0026Oneway"
          />
        </c-v-col>
        <!-- 作成者  -->
        <!-- orX0009バグあり、onewayを先に設定、その後、v-ifをtrueに変更する場合、データが反映されない -->
        <c-v-col
          v-show="localFlg.showAuthorFlg"
          cols="auto"
          class="custom-author"
        >
          <g-custom-orX0009
            v-bind="orX0009"
            :oneway-model-value="localOneway.orX0009Oneway"
          />
        </c-v-col>
        <!-- 履歴 -->
        <c-v-col
          v-if="localFlg.showHistoryFlg"
          cols="auto"
        >
          <g-custom-or-x0165
            :oneway-model-value="localOneway.orX0165History"
            @on-click-edit-btn="onClickHistIconBtn"
            @on-click-pre-btn="onClickPreHistBtn"
            @on-click-post-btn="onClickNextHistBtn"
          />
        </c-v-col>
        <!-- 保険サービス -->
        <!-- TEMP forced shown for figma review -->
        <c-v-col
          v-if="showInsServiceFlg || true"
          cols="auto"
        >
          <g-custom-or-x-0027
            v-bind="orX0027"
            v-model="local.orX0027"
          />
        </c-v-col>
      </c-v-row>
      <c-v-row
        no-gutters
        class="divider-class flex-0-0"
      />
      <g-custom-or-x-0073
        v-bind="orX0073"
        ref="orX0073Ref"
        v-model="local.orX0073"
        :oneway-model-value="{
          disabled: action === OrX0042Const.PAGE_ACTION.DELETE,
          createYmd: local.orX0026.value,
          termId: termId,
          ks21Id: carePlan2Data.ks21Id,
        }"
      />
    </c-v-sheet>
  </c-v-col>
  <!-- GUI01011 履歴選択画面のポップアップ -->
  <g-custom-or-29104
    v-if="showDialogOr29104"
    v-bind="or29104"
    v-model="local.or29104"
    :oneway-model-value="localOneway.or29104Oneway"
  />
  <!-- ［計画書複写］（計画書（2）） -->
  <g-custom-or-57737
    v-if="showDialogOr57737"
    v-bind="or57737"
    :oneway-model-value="localOneway.or57737Oneway"
    @on-click-append="onCpyClickAppend"
    @on-click-overwrite="onCpyClickOverwrite"
  >
  </g-custom-or-57737>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813 v-bind="or21813" />
  <!-- GUI00936 計画書一括印刷画面のポップアップ -->
  <g-custom-or-10583
    v-if="showDialogOr10583"
    v-bind="or10583"
    :oneway-model-value="localOneway.or10583Oneway"
  />
  <!-- GUI01034_印刷設定画面のポップアップ -->
  <g-custom-or-26610
    v-if="showDialogOr26610"
    v-bind="or26610"
    :oneway-model-value="localOneway.or26610Oneway"
  />
  <!-- GUI01091_保険サービス登録画面のポップアップ -->
  <!-- #159464 画面の遷移は、一覧明細部の「 保険サービスで入力 」入力補助アイコンに実装 -->
  <!-- <g-custom-or-10878
    v-if="showDialogOr10878"
    v-bind="or10878"
    v-model="local.or10878Type"
    :oneway-model-value="localOneway.or10878Oneway"
  /> -->
</template>
<style scoped lang="scss">
@use '@/styles/base.scss';

.divider-class {
  margin: 24px 0px;
  border-bottom: 1px solid #b4c5dc;
}

.content {
  background-color: transparent;

  :deep(.v-sheet) {
    background-color: transparent;
  }

  :deep(.v-sheet .v-input) {
    background-color: rgb(var(--v-theme-secondaryBackground));
  }

  .custom-jigyo {
    flex-direction: column;

    :deep(> div:first-child) {
      margin: 0px !important;
      padding: 0px !important;
      margin-bottom: 4px !important;
    }

    :deep(> div:last-child > div) {
      margin: 0px !important;
    }
  }

  .custom-plan {
    :deep(> div:first-child > div:first-child > div:first-child > div:first-child) {
      margin: 0px !important;
      margin-bottom: 4px !important;
    }

    :deep(> div:last-child) {
      background-color: #ebf2fd;
    }
  }

  .custom-author {
    :deep(> div:first-child > div:first-child > div:first-child > div:first-child) {
      margin: 0px !important;
      margin-bottom: 4px !important;
    }

    :deep(> div:last-child) {
      background-color: #ebf2fd;
    }
  }
}
// 上の2行目のスタイル
.second-row {
  margin-top: 0px;
  align-items: baseline;
}
</style>
