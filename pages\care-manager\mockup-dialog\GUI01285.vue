<script setup lang="ts">
import { computed, ref, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or26454Const } from '~/components/custom-components/organisms/Or26454/Or26454.constants'
import { Or26454Logic } from '~/components/custom-components/organisms/Or26454/Or26454.logic'
import type { Or26454Type, Or26454OnewayType } from '~/types/cmn/business/components/Or26454Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**
 * GUI01285_前回基本調査履歴
 *
 * @description
 * 「前回基本調査履歴」画面を表示
 *
 * <AUTHOR> 劉顕康
 */

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01285'
// ルーティング
const routing = 'GUI01285/pinia'
// 画面物理名
const screenName = 'GUI01285'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or26454 = ref({ uniqueCpId: Or26454Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01285' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or26454Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or26454.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01285',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or26454Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or26454Const.CP_ID(1)]: or26454.value,
})

// ダイアログ表示フラグ
const showDialogOr26454 = computed(() => {
  // Or26454のダイアログ開閉状態
  return Or26454Logic.state.get(or26454.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or26454)
 *
 * @param tabIndex - タブインデックス
 */
function onClickOr26454(tabIndex: number) {
  // タブインデックス
  or26454Data.tabIndex = tabIndex

  // Or26454のダイアログ開閉状態を更新する
  Or26454Logic.state.set({
    uniqueCpId: or26454.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or26454Type = ref<Or26454Type>({})

const or26454Data: Or26454OnewayType = {
  // 利用者ID
  userId: '9',
  // 実施日
  implDate: '2024/6/31',
  // アセスメント方式
  assessmentMethod: '',
  // 事業所ID
  svJigyoId: '1',
  // タブインデックス
  tabIndex: 2,
}

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  userId: {
    value: '',
  },
  implDate: {
    value: '',
  },
  assessmentMethod: {
    value: '',
  },
  svJigyoId: {
    value: '',
  },
  tabIndex: {
    value: '',
  },
})

function onClickOr26454Test() {
  or26454Data.userId = local.userId.value
  or26454Data.implDate = local.implDate.value
  or26454Data.assessmentMethod = local.assessmentMethod.value
  or26454Data.svJigyoId = local.svJigyoId.value
  or26454Data.tabIndex = local.tabIndex.value
  Or26454Logic.state.set({
    uniqueCpId: or26454.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26454(2)"
        >GUI01285_［前回基本調査履歴］画面 タブインデックス = 2
      </v-btn>
      <g-custom-or-26454
        v-if="showDialogOr26454"
        v-bind="or26454"
        v-model="or26454Type"
        :oneway-model-value="or26454Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26454(3)"
        >GUI01285_［前回基本調査履歴］画面 タブインデックス = 3
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26454(4)"
        >GUI01285_［前回基本調査履歴］画面 タブインデックス = 4
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26454(5)"
        >GUI01285_［前回基本調査履歴］画面 タブインデックス = 5
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr26454(6)"
        >GUI01285_［前回基本調査履歴］画面 タブインデックス = 6
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
      <c-v-col>
        <v-btn variant="plain"
          >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
        </v-btn>
      </c-v-col>
    </c-v-row>
    <div style="margin-left: 20px">利用者ID</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.userId"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">実施日</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.implDate"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">アセスメント方式</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.assessmentMethod"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">事業所ID</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.svJigyoId"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">タブインデックス</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.tabIndex"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div class="pt-5 w-25 pl-5">
      <v-btn @click="onClickOr26454Test"> GUI01285 疎通起動 </v-btn>
    </div>
    <div class="pt-5 w-25 pl-5">疎通Result: {{ or26454Type }}</div>
</template>
