<script setup lang="ts">
/**
 * OrX0054:有機体:パターン（タイトル）タブ：タイトル
 * GUI00996_パターン（タイトル）
 *
 * @description
 * パターン（タイトル）タブ：タイトル
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/components'
import type { String } from 'lodash'
import { OrX0054Const } from '../OrX0054/OrX0054.constants'
import type { Style, TableData } from './OrX0054.type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type {
  OrX0054OnewayType,
  OrX0054Type,
  Title,
} from '~/types/cmn/business/components/OrX0054Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  PatternTitleInfoSelectInEntity,
  PatternTitleInfoSelectOutEntity,
} from '~/repositories/cmn/entities/PatternTitleInfoSelectEntity'
import type {
  Or21814FirstBtnType,
  Or21814SecondBtnType,
  Or21814ThirdBtnType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { useValidation } from '~/utils/useValidation'
import type { PatternTitleInfoUpdateInEntity } from '~/repositories/cmn/entities/PatternTitleInfoUpdateEntity'
import { useScreenStore } from '#build/imports'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'

const { setChildCpBinds } = useScreenUtils()
const { byteLength } = useValidation()

const { t } = useI18n()

const validation = useValidation()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0054OnewayType
  modelValue: OrX0054Type
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

const defaultOnewayModelValue: OrX0054OnewayType = {
  mstKbn: '2',
  kaiteiFlg: '1',
}
const defaultModelValue: OrX0054Type = {
  editFlg: false,
  titleList: [],
  initFlg: false,
}

const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

// ダイアログ表示フラグ
const showInfoDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
const showErrDialog = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const localOneWay = reactive({
  orX0054: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 新規
  mo00611OneWay: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
  } as Mo00611OnewayType,
  // 行削除
  mo01265OneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    disabled: true,
  } as Mo01265OnewayType,
  // グループ: 表用セレクトフィールド
  mo01282OneWay: {
    items: [],
    itemTitle: 'groupKnj',
    itemValue: 'groupCD',
  } as Mo01282OnewayType,
  // 表示順: 表用テキストフィールド
  mo01274SeqInputOneWay: {
    length: '3',
    rules: [validation.integer, validation.required, validation.minValue(1)],
  } as Mo01274OnewayType,
  // タイトル: 表用テキストフィールド
  mo01274TitleInputOneWay: {
    maxlength: '64',
    hideDetails: false,
    rules: [validation.required, byteLength(64)],
  } as Mo01274OnewayType,
})

const local = reactive({
  orX0054: {
    ...defaultModelValue,
    ...props.modelValue,
  },
  // 戻り値はテーブルデータとして処理されます
  tmpArr: [] as TableData[],
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<TableData[]>({
  cpId: OrX0054Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []
/**************************************************
 * 変数定義
 **************************************************/
// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})

// 画面.画面タイトル
let titleId: unknown
switch (props.onewayModelValue.mstKbn) {
  case OrX0054Const.DEFAULT.MSTKBN_LIST.DAILY_PARTTEN:
    titleId = t('label.daily-table-pattern')
    break

  case OrX0054Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN:
    titleId = t('label.week-table-pattern')
    break
  case OrX0054Const.DEFAULT.MSTKBN_LIST.MONTHYEAR_PARTTEN:
    titleId = t('label.monthly-yearly-table-pattern')
    break
  default:
    break
}
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)
// テーブルヘッダ
const headers = [
  { title: t('label.id'), key: 'ks51Id', width: '100px', sortable: false },
  { title: t('label.title'), key: 'nameKnj', sortable: false, required: true },
  { title: t('label.level-of-care-required'), key: 'youkaiCd', width: '200px', sortable: false },
  { title: t('label.valid-period'), key: 'termName', width: '200px', sortable: false, cksFlg: '3' },
  { title: t('label.display-order'), key: 'seq', width: '140px', sortable: false },
  { title: t('label.style'), key: 'style', width: '140px', sortable: false },
]
const headersFilter = computed(() => {
  if (localOneWay.orX0054.mstKbn !== OrX0054Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN) {
    // 共通情報.計画書様式フラグ(cks_flg)が施設「1」の場合、メインセクションの有効期間表示しない。
    return headers.filter((item) => item.cksFlg !== localOneWay.orX0054.mstKbn)
  }
  return headers
})
// "*"
const required: string = OrX0054Const.DEFAULT.REQUIRED

// 元のテーブルデータ
const orgTableData = ref<string>('')
const tableForm = ref<VForm>()

const tableDataFilter = computed(() => {
  const titleList = refValue.value
  return titleList!.filter((i: TableData) => i.updateKbn !== UPDATE_KBN.DELETE)
})
const refs = ref<Record<string, HTMLInputElement>>({})
const setRef = (el: HTMLInputElement | null, tableIndex: string) => {
  if (el) {
    refs.value[tableIndex] = el
  }
}
// ポスト最小幅
const columnMinWidth = ref<number[]>([143, 563, 290, 288])
const columnMinSixWidth = ref<number[]>([143, 463, 190, 188, 150, 150])
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
})
onMounted(async () => {
  await init()
})

/**
 * パターンタイトル情報取得
 */
async function init() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 有効期間
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_VALID_PERIOD },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // パターンタイトル情報取得(IN)
  const param: PatternTitleInfoSelectInEntity = {
    // マスタ区分: 引継情報.マスタ区分
    mstKbn: localOneWay.orX0054.mstKbn,
  }
  // 週間計画パターンタイトル情報取得
  const ret: PatternTitleInfoSelectOutEntity = await ScreenRepository.select(
    'patternTitleInfoSelect',
    param
  )
  let tableIndex = 0
  // 戻り値はテーブルデータとして処理されます
  switch (props.onewayModelValue.mstKbn) {
    case OrX0054Const.DEFAULT.MSTKBN_LIST.DAILY_PARTTEN:
      if (ret.data.nikkaList !== undefined && ret.data.nikkaList.length > 0) {
        local.tmpArr = ret.data.nikkaList.map((item) => ({
          titleId: item.day1Id,
          titleKnj: { value: item.day1TitleKnj },
          groupCd: { modelValue: item.day1GroupCD },
          termId: OrX0054Const.DEFAULT.EMPTY,
          termKnj: OrX0054Const.DEFAULT.EMPTY,
          seq: { value: item.day1Seq },
          kaiteiFlg: OrX0054Const.DEFAULT.EMPTY,
          kaiteiKnj: OrX0054Const.DEFAULT.EMPTY,
          tableIndex: tableIndex++,
          modifiedCnt: item.day1ModifiedCnt,
          updateKbn: UPDATE_KBN.NONE,
        }))
      }
      break

    case OrX0054Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN:
      if (ret.data.syukanList !== undefined && ret.data.syukanList.length > 0) {
        local.tmpArr = ret.data.syukanList.map((item) => ({
          titleId: item.week1Id,
          titleKnj: { value: item.week1TitleKnj },
          groupCd: { modelValue: item.week1GroupCD },
          termId: item.termid,
          termKnj:
            CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_VALID_PERIOD).find(
              (x) => x.value === item.termid
            )?.label ?? '',
          seq: { value: item.week1Seq },
          kaiteiFlg: item.kaiteiFlg,
          kaiteiKnj: item.kaiteiKnj,
          tableIndex: tableIndex++,
          modifiedCnt: item.week1ModifiedCnt,
          updateKbn: UPDATE_KBN.NONE,
        }))
      }
      break
    case OrX0054Const.DEFAULT.MSTKBN_LIST.MONTHYEAR_PARTTEN:
      if (ret.data.nenkanList !== undefined && ret.data.nenkanList.length > 0) {
        local.tmpArr = ret.data.nenkanList.map((item) => ({
          titleId: item.nenkan1Id,
          titleKnj: { value: item.nenkan1TitleKnj },
          groupCd: { modelValue: item.nenkan1GroupCD },
          termId: OrX0054Const.DEFAULT.EMPTY,
          termKnj: OrX0054Const.DEFAULT.EMPTY,
          seq: { value: item.nenkan1Seq },
          kaiteiFlg: OrX0054Const.DEFAULT.EMPTY,
          kaiteiKnj: OrX0054Const.DEFAULT.EMPTY,
          tableIndex: tableIndex++,
          modifiedCnt: item.nenkan1ModifiedCnt,
          updateKbn: UPDATE_KBN.NONE,
        }))
      }
      break
    default:
      break
  }
  // グループリスト
  localOneWay.mo01282OneWay.items = ret.data.groupList

  // 元のテーブルデータの設定
  orgTableData.value = JSON.stringify(refValue.value)
  setChildCpBinds(props.parentUniqueCpId, {
    [OrX0054Const.CP_ID(1)]: {
      twoWayValue: local.tmpArr,
    },
  })
  // 画面.パターンタイトル一覧の1行目を選択する。
  selectRow(0)
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
  // 行削除活性
  localOneWay.mo01265OneWay.disabled = false
}

/**
 * 様式取得
 *
 * @param kaiteiFlg - 様式CD
 */
function getStyleName(kaiteiFlg: string) {
  const styleList: Style[] = JSON.parse(OrX0054Const.DEFAULT.STYLE_LIST) as Style[]
  const style: Style[] = styleList.filter((item: Style) => item.kaiteiFlg === kaiteiFlg)
  if (style !== undefined && style.length > 0) {
    return style[0].kaiteiKnj
  }
  return ''
}
/**
 * 「新規」押下
 */
async function createRow() {
  let lastSeq = 0
  if (refValue.value!.length > 0) {
    for (const data of refValue.value!) {
      if (data.updateKbn !== UPDATE_KBN.DELETE) {
        lastSeq = Math.max(lastSeq, Number(data.seq.value))
      }
    }
  }
  // 表示順最大値＜999の場合
  if (lastSeq < 999) {
    // 表示順最大値＋1を設定
    lastSeq += 1
  } else {
    // 上記以外の場合、999を設定
    lastSeq = 999
  }
  // 週間計画パターンのタイトル一覧の最終に新しい行を追加する。
  const data = {
    titleId: '',
    // タイトル：空白
    titleKnj: { value: '' },
    // グループ区分：空白
    groupCd: { modelValue: '' },
    // 有効期間：有効期間リストに最後の有効期間を設定
    termId: refValue.value![refValue.value!.length - 1].termId,
    termKnj: refValue.value![refValue.value!.length - 1].termKnj,
    // 表示順
    seq: { value: String(lastSeq) },
    // 様式：引継情報.計画書様式
    kaiteiFlg: localOneWay.orX0054.kaiteiFlg,
    kaiteiKnj: getStyleName(localOneWay.orX0054.kaiteiFlg),
    // テーブルINDEX(行固有ID)
    tableIndex: refValue.value!.length,
    // 更新回数
    modifiedCnt: '0',
    // 更新区分
    updateKbn: UPDATE_KBN.CREATE,
  }
  refValue.value!.push(data)
  await nextTick()
  const lastInput = refs.value[`input-${data.tableIndex}`]
  if (lastInput) {
    lastInput.focus()
  }
}

/**
 * 行削除ボタン押下
 */
async function deleteRowMsg() {
  if (selectedItemIndex.value !== -1) {
    // 以下のメッセージを表示
    const dialogResult = await openInfoDialog(
      t('message.i-cmn-10878'),
      t('btn.yes'),
      'normal1',
      'destroy1',
      'blank'
    )
    switch (dialogResult) {
      case OrX0054Const.DEFAULT.DIALOG_RESULT_YES:
        deleteRow()
        break
      case OrX0054Const.DEFAULT.DIALOG_RESULT_NO:
        break
    }
  }
}

/**
 * 行削除
 */
function deleteRow() {
  if (selectedItemIndex.value !== null) {
    refValue.value!.forEach((item: { tableIndex: number; updateKbn: string }) => {
      if (item.tableIndex === selectedItemIndex.value) {
        item.updateKbn = UPDATE_KBN.DELETE
        selectedItemIndex.value = -1
        // 行削除非活性
        localOneWay.mo01265OneWay.disabled = true
      }
    })
  }
}

/**
 * コンテンツの更新
 */
function onUpdate() {
  refValue.value!.forEach((item: TableData) => {
    if (item.tableIndex === selectedItemIndex.value && item.updateKbn !== UPDATE_KBN.CREATE) {
      item.updateKbn = UPDATE_KBN.UPDATE
      selectedItemIndex.value = -1
      // 行削除非活性
      localOneWay.mo01265OneWay.disabled = true
    }
  })
}

watch(
  () => refValue.value,
  () => {
    local.orX0054.editFlg = orgTableData.value !== JSON.stringify(refValue.value)
    // タイトルのリスト
    const titleList: Title[] = []
    for (const title of refValue.value!) {
      titleList.push({
        ...title,
        titleKnj: title.titleKnj.value,
        groupCd: title.groupCd.modelValue,
        seq: title.seq.value,
      })
    }
    local.orX0054.titleList = titleList
    emit('update:modelValue', local.orX0054)
  },
  { deep: true }
)

/**
 * 表データの設定
 */
watch(
  () => local.orX0054.saveResultTitleList,
  () => {
    if (Array.isArray(local.orX0054.saveResultTitleList)) {
      const titleList = []
      for (const item of local.orX0054.saveResultTitleList) {
        titleList.push({
          // 週間計画ID
          titleId: item.titleId,
          // 名称
          titleKnj: { value: item.titleKnj },
          // グループ区分
          groupCd: { modelValue: item.groupCd },
          // 有効期間ID
          termId: item.termId,
          // 有効期間
          termKnj: item.termKnj,
          // 表示順
          seq: { value: item.seq },
          // 様式CD
          kaiteiFlg: item.kaiteiFlg,
          // 様式
          kaiteiKnj: getStyleName(item.kaiteiFlg),
          // テーブルINDEX(行固有ID)
          tableIndex: titleList.length,
          // 更新回数
          modifiedCnt: item.modifiedCnt,
          // 更新区分
          updateKbn: UPDATE_KBN.NONE,
        })
      }
      refValue.value = titleList
      // 元のテーブルデータの設定
      orgTableData.value = JSON.stringify(refValue.value)
    }
  }
)
/**
 * 初期表示フラグを傍受する
 */
watch(
  () => local.orX0054.initFlg,
  async (newVal) => {
    if (newVal) {
      await init()
      local.orX0054.initFlg = false
      emit('update:modelValue', local.orX0054)
    }
  }
)

/**
 * 「閉じるボタン」押下
 */
async function close() {
  if (isEdit.value) {
    // 画面.表示順が数値以外の場合
    // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
    const dataArray = local.orX0054.titleList.filter(
      (data) => (data.groupCd === '' && data.updateKbn !== UPDATE_KBN.DELETE) || data.groupCd !== ''
    )
    let isTitleEmpty = false
    let isSeqNotNum = false
    let isSeqRightNum = false
    dataArray.forEach((element) => {
      if (element.titleKnj === null || element.titleKnj === undefined || element.titleKnj === '') {
        isTitleEmpty = true
      }
      if (Number.isNaN(Number(element.seq))) {
        isSeqNotNum = true
      }
      if (!Number.isNaN(Number(element.seq)) && Number(element.seq) <= 0) {
        isSeqRightNum = true
      }
    })
    // 画面.表示順が数値以外の場合
    if (isSeqNotNum) {
      const dialogResult = await openInfoDialog(
        t('message.i-cmn-11452', [titleId]),
        t('btn.yes'),
        'normal1',
        'destroy1',
        'blank'
      )
      switch (dialogResult) {
        // はい：AC008-1-2～AC008-1-3、AC008-2～AC008-3を実行して、処理続き
        case OrX0054Const.DEFAULT.DIALOG_RESULT_YES:
          break
        case OrX0054Const.DEFAULT.DIALOG_RESULT_NO:
          return {
            error: false,
            info: false,
            msg: '',
            isBreak: true,
          }
      }
    }

    // 画面.タイトルが空白の場合 画面.表示順が空白、又は、「0」以下の場合
    if (isTitleEmpty || isSeqRightNum) {
      const dialogResult = await openInfoDialog(
        t('message.i-cmn-10430'),
        t('btn.yes'),
        'normal1',
        'destroy1',
        'normal3'
      )
      switch (dialogResult) {
        // はい：AC008-1-2～AC008-1-3、AC008-2～AC008-3を実行して、処理続き
        case OrX0054Const.DEFAULT.DIALOG_RESULT_YES:
          await save()
          break
        case OrX0054Const.DEFAULT.DIALOG_RESULT_NO:
          break
        case OrX0054Const.DEFAULT.DIALOG_RESULT_CANCEL:
          return {
            error: false,
            info: false,
            msg: '',
            isBreak: true,
          }
      }
    }
  }
  return {
    error: false,
    info: false,
    msg: '',
    isBreak: false,
  }
}

/**
 * タイトル保存
 */
async function save() {
  if (!isEdit.value) {
    await openInfoDialog(t('message.i-cmn-21800'), t('btn.ok'), 'normal1', 'blank', 'blank')
    return
  }
  // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
  const dataArray = local.orX0054.titleList.filter(
    (data) => (data.groupCd === '' && data.updateKbn !== UPDATE_KBN.DELETE) || data.groupCd !== ''
  )
  let isTitleEmpty = false
  let isSeqNotNum = false
  let isSeqRightNum = false
  dataArray.forEach((element) => {
    if (element.titleKnj === null || element.titleKnj === undefined || element.titleKnj === '') {
      isTitleEmpty = true
    }
    if (Number.isNaN(Number(element.seq))) {
      isSeqNotNum = true
    }
    if (!Number.isNaN(Number(element.seq)) && Number(element.seq) <= 0) {
      isSeqRightNum = true
    }
  })
  // 画面.表示順が数値以外の場合
  if (isSeqNotNum) {
    return
  }
  // 画面.タイトルが空白の場合
  if (isTitleEmpty) {
    await openErrorDialog(t('message.e-cmn-41708'))
    return
  }
  // 画面.表示順が空白、又は、「0」以下の場合
  if (isSeqRightNum) {
    await openErrorDialog(t('message.e-cmn-40792'))
    return
  }

  const nikkaList: PatternTitleInfoUpdateInEntity['nikkaList'] = []
  const syukanList: PatternTitleInfoUpdateInEntity['syukanList'] = []
  const nenkanList: PatternTitleInfoUpdateInEntity['nenkanList'] = []
  switch (props.onewayModelValue.mstKbn) {
    case OrX0054Const.DEFAULT.MSTKBN_LIST.DAILY_PARTTEN:
      for (const item of dataArray) {
        nikkaList.push({
          day1Id: item.titleId,
          day1TitleKnj: item.titleKnj,
          day1GroupCD: item.groupCd,
          day1Seq: item.seq,
          day1ModifiedCnt: item.modifiedCnt,
          updateKbn: item.updateKbn,
        })
      }
      break
    case OrX0054Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN:
      for (const item of dataArray) {
        syukanList.push({
          week1Id: item.titleId,
          week1TitleKnj: item.titleKnj,
          week1GroupCD: item.groupCd,
          termid: item.termId,
          termKnj: item.termKnj,
          week1Seq: item.seq,
          kaiteiFlg: item.kaiteiFlg,
          kaiteiKnj: item.kaiteiKnj,
          week1ModifiedCnt: item.modifiedCnt,
          updateKbn: item.updateKbn,
        })
      }
      break
    case OrX0054Const.DEFAULT.MSTKBN_LIST.MONTHYEAR_PARTTEN:
      for (const item of dataArray) {
        nenkanList.push({
          nenkan1Id: item.titleId,
          nenkan1TitleKnj: item.titleKnj,
          nenkan1GroupCD: item.groupCd,
          nenkan1Seq: item.seq,
          nenkan1ModifiedCnt: item.modifiedCnt,
          updateKbn: item.updateKbn,
        })
      }
      break
    default:
      break
  }
  const param: PatternTitleInfoUpdateInEntity = {
    nikkaList: nikkaList,
    syukanList: syukanList,
    nenkanList: nenkanList,
  }
  // パターン（グループ）タイトル情報保存
  await ScreenRepository.update('patternTitleInfoUpdate', param)
  await nextTick()
  await init()
}

/**
 * EditFlag初期化処理（isInit: true）
 *
 */
function _cleanEditFlag() {
  useScreenStore().setCpNavControl({
    cpId: OrX0054Const.CP_ID(1),
    uniqueCpId: props.uniqueCpId,
    editFlg: false,
  })
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msg - msg
 *
 * @param firstLabel - firstLabel
 *
 * @param firstBtn - firstBtn
 *
 * @param secondBtn - secondBtn
 *
 * @param thirdBtn - thirdBtn
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(
  msg: string,
  firstLabel: string,
  firstBtn: Or21814FirstBtnType,
  secondBtn: Or21814SecondBtnType,
  thirdBtn: Or21814ThirdBtnType
): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: firstBtn,
      firstBtnLabel: firstLabel,
      secondBtnType: secondBtn,
      secondBtnLabel: t('btn.no'),
      thirdBtnType: thirdBtn,
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = OrX0054Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = OrX0054Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = OrX0054Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = OrX0054Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * errorダイアログを閉じたタイミングで結果を返却
 *
 * @param msg - msg
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openErrorDialog(msg: string): Promise<string> {
  // 選択行削除確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: 'blank',
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'blank',
    },
  })

  /**
   * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
   *
   * @returns ダイアログの選択結果（yes, no）
   */
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = OrX0054Const.DEFAULT.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = OrX0054Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
defineExpose({
  save,
  close,
  _cleanEditFlag,
})
</script>

<template>
  <c-v-row
    no-gutters
    class="mt-0"
  >
    <c-v-col>
      <!-- 新規ボタン -->
      <base-mo00611
        v-bind="localOneWay.mo00611OneWay"
        @click="createRow"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.add-row')"
        />
      </base-mo00611>
      <!-- 削除ボタン -->
      <base-mo01265
        v-bind="localOneWay.mo01265OneWay"
        class="mx-2"
        @click="deleteRowMsg"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="t('tooltip.display-order-delete-success')"
        />
      </base-mo01265>
    </c-v-col>
  </c-v-row>
  <c-v-form ref="tableForm">
    <!-- パターン一覧 -->
    <c-v-row no-gutters>
      <c-v-data-table
        v-resizable-grid="{
          columnWidths:
            localOneWay.orX0054.mstKbn === OrX0054Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN
              ? columnMinSixWidth
              : columnMinWidth,
        }"
        fixed-header
        :headers="headersFilter"
        :items="tableDataFilter"
        class="table-wrapper mt-2 mb-2 ml-2 mr-2"
        hover
        height="480px"
        hide-default-footer
        :items-per-page="-1"
      >
        <!-- ヘッダ Part -->
        <template #headers>
          <tr>
            <!-- ID -->
            <th>{{ t('label.id') }}</th>
            <!-- *タイトル -->
            <th class="width-200">
              <span style="color: red">{{ required }}</span
              >{{ t('label.title') }}
            </th>
            <!-- グループ -->
            <th class="width-190">{{ t('label.group') }}</th>
            <!-- 有効期間 -->
            <th
              v-if="localOneWay.orX0054.mstKbn == OrX0054Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN"
              class="width-190"
            >
              {{ t('label.valid-period') }}
            </th>
            <!-- *表示順 -->
            <th class="width-150">
              <span style="color: red">{{ required }}</span
              >{{ t('label.display-order') }}
            </th>
            <!-- style -->
            <th
              v-if="localOneWay.orX0054.mstKbn == OrX0054Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN"
              class="width-150"
            >
              {{ t('label.style') }}
            </th>
          </tr>
        </template>
        <!-- 一覧 -->
        <template #item="{ item, index }">
          <tr
            :class="{ 'select-row': selectedItemIndex === item.tableIndex }"
            @click="selectRow(item.tableIndex)"
          >
            <!-- ID: 右寄せ -->
            <td class="text-align-right">{{ item.titleId }}</td>
            <!-- タイトル: テキストフィールド -->
            <td class="input-padding-none">
              <base-mo01274
                v-model="tableDataFilter[index].titleKnj"
                :re-ref="(el: HTMLInputElement | null) => setRef(el, `input-${item.tableIndex}`)"
                :oneway-model-value="localOneWay.mo01274TitleInputOneWay"
                @change="onUpdate"
              ></base-mo01274>
            </td>
            <!-- 要介護度: セレクトフィールド -->
            <td class="input-padding-none">
              <base-mo01282
                v-model="tableDataFilter[index].groupCd"
                :oneway-model-value="localOneWay.mo01282OneWay"
                @change="onUpdate"
              ></base-mo01282>
            </td>
            <td v-if="localOneWay.orX0054.mstKbn == OrX0054Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN">
              {{ item.termKnj }}
            </td>
            <!-- 表示順: テキストフィールド -->
            <td class="input-padding-none">
              <base-mo01274
                v-model="tableDataFilter[index].seq"
                :oneway-model-value="localOneWay.mo01274SeqInputOneWay"
                :reverse="true"
                @change="onUpdate"
              ></base-mo01274>
            </td>
            <!-- 様式 -->
            <td v-if="localOneWay.orX0054.mstKbn == OrX0054Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN">
              {{ getStyleName(item.kaiteiFlg) }}
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-row>
  </c-v-form>
  <!-- 選択行削除確認ダイアログ -->
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21813
    v-if="showErrDialog"
    v-bind="or21813"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showInfoDialog"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
// 右寄せのCSS
.text-align-right {
  text-align: right;
}
.input-padding-none {
  padding-left: 0px !important;
  padding-right: 0px !important;
}
// 選択した行のCSS
.select-row {
  background: #dbeefe;
}
</style>
