/**
 * Or26426のエンティティ
 * GUI01272_認定調査票特記事項
 *
 * <AUTHOR>
 */
import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 特記事項の初期情報を取得する出力エンティティ
 */
export interface CertificationSurveySpecialMatterInitSelectOutEntity extends OutWebEntity {
  /** data */
  data: {
    /**
     * 特記事項リスト
     */
    tokkiList: TokkiList[]
  }
}

/**
 * 特記事項の初期情報を取得する入力エンティティ
 */
export interface CertificationSurveySpecialMatterInitSelectInEntity extends InWebEntity {
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * 調査票ID
   */
  cschId: string
}

/**
 * 特記事項リスト
 */
export interface TokkiList {
  /**
   * 特記事項タブ名称
   */
  tokkiTab: string
  /**
   * 特記事項タイトル
   */
  tokkiTitle: string
  /**
   * 特記事項(固定)リスト
   */
  tokkiTextList: TokkiTextList[]
  /**
   * 特記事項内容リスト
   */
  nTokkiList: NTokkiList[]
}

/**
 * 特記事項内容リスト
 */
export interface NTokkiList {
  /**
   * カウンター
   */
  counter: string
  /**
   * 認定（特記：大）
   */
  n1tCd: string
  /**
   * 認定（特記：小）
   */
  n2tCd: string
  /**
   * 特記事項
   */
  memoKnj: string
  /**
   * 表示順
   */
  seqNo: string
}

/**
 * 特記事項(固定)リスト
 */
export interface TokkiTextList {
  /**
   * 詳細リスト
   */
  tokkiDetailList: TokkiDetailList[]
}

/**
 * 詳細リスト
 */
export interface TokkiDetailList {
  /**
   * 特記事項(固定)ID
   */
  tokkiId: string
  /**
   * 特記事項(固定)
   */
  tokkiText: string
}
