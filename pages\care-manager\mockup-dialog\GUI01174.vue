<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  watch,
} from '#imports'
import { Or00233Const } from '~/components/custom-components/organisms/Or00233/Or00233.constants'
import { Or00233Logic } from '~/components/custom-components/organisms/Or00233/Or00233.logic'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
import type {
  Or00233SelectInfoType,
  Or00233Type,
} from '~/types/cmn/business/components/Or00233Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01174'
// ルーティング
const routing = 'GUI01174/pinia'
// 画面物理名
const screenName = 'GUI01174'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or00233 = ref({ uniqueCpId: Or00233Const.CP_ID(1) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01174' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or00233Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or00233.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01174',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or00233Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or00233Const.CP_ID(1)]: or00233.value,
})

// ダイアログ表示フラグ
const showDialogOr00233 = computed(() => {
  // Or00233のダイアログ開閉状態
  return Or00233Logic.state.get(or00233.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or00233Data: Or00233SelectInfoType = {
  sysYmd: '',
  userId: '1',
}

const or00233Type = ref<Or00233Type>({
  reSearchFlg: '',
})
watch(or00233Type, () => {
  console.log(or00233Type.value)
})
const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType
const local = reactive({
  // システム年月日
  sysYmd: { value: '2024/12/31' } as Mo00045Type,
  // 利用者ID
  userId: { value: '1' } as Mo00045Type,
})

/**
 *  ボタン押下時の処理
 *
 * @param sysYmd - システム年月日
 */
function or00233OnClick(sysYmd: string) {
  or00233Data.sysYmd = sysYmd
  // Or00233のダイアログ開閉状態を更新する
  Or00233Logic.state.set({
    uniqueCpId: or00233.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function onClickOr00233() {
  or00233Data.sysYmd = local.sysYmd.value
  or00233Data.userId = local.userId.value
  // Or00233のダイアログ開閉状態を更新する
  Or00233Logic.state.set({
    uniqueCpId: or00233.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or00233OnClick('2024/12/31')"
        >GUI01174_希望負担額登録(引継情報.システム年月日 > "2001/12/31"の場合)
      </v-btn>
      <g-custom-or-00233
        v-if="showDialogOr00233"
        v-bind="or00233"
        v-model="or00233Type"
        :oneway-model-value="or00233Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or00233OnClick('2001/12/31')"
        >GUI01174_希望負担額登録
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">システム年月日</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.sysYmd"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr00233"> GUI01174 疎通起動 </v-btn>
  </div>
</template>
