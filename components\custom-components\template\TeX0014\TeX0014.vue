<script setup lang="ts">
/**
 * TeX0014:退居・退所時情報提供書
 *
 * @description
 * 退居・退所時情報提供書、以下のコンポーネントをタブで表示する。
 * GUI00794_退居・退所時情報提供書（1）
 * GUI00795_退居・退所時情報提供書（2）
 *
 * <AUTHOR>
 */

import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { OrD2002Const } from '../../organisms/OrD2002/OrD2002.constants'
import { Or28992Const } from '../../organisms/Or28992/Or28992.constants'
import { OrX0008Logic } from '../../organisms/OrX0008/OrX0008.logic'
import { OrX0008Const } from '../../organisms/OrX0008/OrX0008.constants'
import { OrX0188Const } from '../../organisms/OrX0188/OrX0188.constants'
import { OrX0195Const } from '../../organisms/OrX0195/OrX0195.constants'
import { OrX0009Logic } from '../../organisms/OrX0009/OrX0009.logic'
import { OrX0001Const } from '../../organisms/OrX0001/OrX0001.constants'
import { Or27562Logic } from '../../organisms/Or27562/Or27562.logic'
import { Or27562Const } from '../../organisms/Or27562/Or27562.constants'
import { Or26257Const } from '../../organisms/Or26257/Or26257.constants'
import { OrX0009Const } from '../../organisms/OrX0009/OrX0009.constants'
import { Or17391Logic } from '../../organisms/Or17391/Or17391.logic'
import { Or17391Const } from '../../organisms/Or17391/Or17391.constants'
import { OrX0007Logic } from '../../organisms/OrX0007/OrX0007.logic'
import { OrX0189Const } from '../../organisms/OrX0189/OrX0189.constants'
import { OrX0190Const } from '../../organisms/OrX0190/OrX0190.constants'
import { OrX0198Const } from '../../organisms/OrX0198/OrX0198.constants'
import { OrX0197Const } from '../../organisms/OrX0197/OrX0197.constants'
import { OrX0199Const } from '../../organisms/OrX0199/OrX0199.constants'
import { OrX0191Const } from '../../organisms/OrX0191/OrX0191.constants'
import { OrX0007Const } from '../../organisms/OrX0007/OrX0007.constants'
import { OrX0196Const } from '../../organisms/OrX0196/OrX0196.constants'
import { OrX0192Const } from '../../organisms/OrX0192/OrX0192.constants'
import { OrX0193Const } from '../../organisms/OrX0193/OrX0193.constants'
import { Or56886Logic } from '../../organisms/Or56886/Or56886.logic'
import { Or56886Const } from '../../organisms/Or56886/Or56886.constants'
import type { OrX0191ValuesType } from '../../organisms/OrX0191/OrX0191.Type'
import type { OrX0192ValuesType } from '../../organisms/OrX0192/OrX0192.Type'
import type { OrX0193ValuesType } from '../../organisms/OrX0193/OrX0193.Type'
import type { OrX0190ValuesType } from '../../organisms/OrX0190/OrX0190.Type'
import type { OrX0189ValuesType } from '../../organisms/OrX0189/OrX0189.Type'
import type { OrX0197ValuesType } from '../../organisms/OrX0197/OrX0197.type'
import type { OrX0198ValuesType } from '../../organisms/OrX0198/OrX0198.type'
import type { OrX0196ValuesType } from '../../organisms/OrX0196/OrX0196.type'
import type { OrX0199ValuesType } from '../../organisms/OrX0199/OrX0199.type'
import type { Or56886Param } from '../../organisms/Or56886/Or56886.type'
import { TeX0014Logic } from './TeX0014.logic'
import type { TeX0014StateType } from './TeX0014.type'
import { TeX0014Const } from './TeX0014.constants'
import {
  hasPrintAuth,
  useJigyoList,
  useNuxtApp,
  useScreenInitFlg,
  useScreenOneWayBind,
  useScreenStore,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00028Type } from '~/types/business/components/Mo00028Type'
import type { Or28992OnewayType } from '~/types/cmn/business/components/Or28992Type'
import type { TeX0014OnewayType, TeX0014Type } from '~/types/cmn/business/components/TeX0014Type'
import type {} from '~/repositories/cmn/entities/CpnTucGdlComInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import type { OrX0001OnewayType, OrX0001Type } from '~/types/cmn/business/components/OrX0001Type'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or27562OnewayType } from '~/types/cmn/business/components/Or27562Type'
import type { Or17391OnewayType } from '~/types/cmn/business/components/Or17391Type'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import type {
  CpnTucTaiInfoTeikyouData,
  MovingOutLeavingInfoOfferInitInfoSelectInEntity,
  MovingOutLeavingInfoOfferInitInfoSelectOutEntity,
} from '~/repositories/cmn/entities/MovingOutLeavingInfoOfferInitInfoSelectEntity'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type {
  AuthorityFlag,
  HistoryInfo1,
  HospitalizationTimeInfoOfferHistorySelectInEntity,
  HospitalizationTimeInfoOfferHistorySelectOutEntity,
  HospitalizationTimeInfoOfferNewInfoSelectInEntity,
  HospitalizationTimeInfoOfferNewInfoSelectOutEntity,
  HospitalizationTimeInfoOfferPeriodSelectInEntity,
  HospitalizationTimeInfoOfferPeriodSelectOutEntity,
  HospitalizationTimeInfoOfferUpdateInEntity,
  HospitalizationTimeInfoOfferUpdateOutEntity,
  SaveInfo,
} from '~/repositories/cmn/entities/HospitalizationTimeInfoOfferPeriodSelectServiceEntity'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00038Type } from '~/types/business/components/Mo00038Type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00030Type } from '~/types/business/components/Mo00030Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo01408Type } from '~/types/business/components/Mo01408Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { OrX0007OnewayType, OrX0007Type } from '~/types/cmn/business/components/OrX0007Type'
import type {
  OrX0008OnewayType,
  OrX0008Type,
  RirekiInfo,
} from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'

const $log = useNuxtApp().$log as DebugLogPluginInterface

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: TeX0014OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

const { setChildCpBinds, getChildCpBinds } = useScreenUtils()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()
// route共有情報
// 事業所変更監視
const { jigyoListWatch } = useJigyoList()
const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or17391 = ref({
  uniqueCpId: '',
  parentUniqueCpId: props.uniqueCpId,
  or00249UniqueCpId: '',
  or00094UniqueCpId: '',
}) // GUI00807_アセスメント複写
const or27562 = ref({ uniqueCpId: '' }) // GUI00626_アセスメントマスタ
const or41179 = ref({ uniqueCpId: '' }) // 事業所選択
const orX0009 = ref({ uniqueCpId: '' })
const or56886 = ref({ uniqueCpId: '' }) // GUI1304_印刷
const or21814_1 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes, no, cancel)
const or21814_2 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes)
const or21814_3 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes,no)
const orHeadLine = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const or00094 = ref({ uniqueCpId: '' })
const orD2002 = ref({ uniqueCpId: '' })
const or28992 = ref({
  uniqueCpId: Or28992Const.CP_ID(0),
  parentUniqueCpId: props.uniqueCpId,
})

const orX0007 = ref({ uniqueCpId: OrX0007Const.CP_ID(0) })
const orX0008 = ref({ uniqueCpId: OrX0008Const.CP_ID(0) })
const orX0188 = ref({
  uniqueCpId: OrX0188Const.CP_ID(0),
  parentUniqueCpId: props.uniqueCpId,
})
const OrX0195 = ref({
  uniqueCpId: OrX0195Const.CP_ID(0),
  parentUniqueCpId: props.uniqueCpId,
})
const orX0001 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' })

// // ナビゲーション制御領域のいずれかの編集フラグがON
// const isEdit = computed(() => {
//   return useScreenStore().isEditNavControl()
// })

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  let editFlg = false
  switch (local.mo00043.id) {
    case OrX0188Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(orX0188.value.uniqueCpId)
      break
    case OrX0195Const.DEFAULT.TAB_ID:
      editFlg = useScreenStore().isEditByUniqueCpId(OrX0195.value.uniqueCpId)
      break
  }
  console.log(editFlg, '1133333222')
  return editFlg
})

// ローカルTwoway
const local = reactive({
  orX0007: {
    PlanTargetPeriodUpdateFlg: '0',
    planTargetPeriodId: '1',
  } as OrX0007Type,
  orX0008: {
    /**
     *計画書ID（履歴Id）
     */
    createId: '0',
    /**
     *履歴更新フラグ
     */
    createUpateFlg: '0',
  } as OrX0008Type,
  periodInfo: {},
  listDataGroup: {} as Record<string, CodeType[]>,
  // 作成日
  createDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // タブ
  mo00043: {
    id: '',
  } as Mo00043Type,
  mo00028: {
    panel: ref(1),
  } as Mo00028Type,
  // 削除ダイアログ
  orX0001Type: {
    deleteSyubetsu: '',
  } as OrX0001Type,
  copyDisabled: false,
})

// 権限フラグ
const isPermissionViewAuth = ref(true)

// 画面情報の取得中かどうか
const isLoading = ref(false)

// 画面初期化完了フラグ
let isInitCompleted = false

// 選択中利用者ID
const userId = ref('')

// 変更タブIDを保持
const tabIdBk = ref('')

// 期間管理フラグ
const plannningPeriodManageFlg = ref(TeX0014Const.DEFAULT.PLANNING_PERIOD_MANAGE)

// 履歴更新回数
const historyModifiedCnt = ref('0')

// 履歴表示フラグ
const isHistoryShow = ref(true)

// 画面情報領域のsupplement内のisInitの取得およびonMountedにfalseへ更新する処理を登録
const isInit = useScreenInitFlg()

// 画面処理パターン
const clickMode = ref('')

// 複写コンポネントキー
const copyComponentKey = ref('')

// 複写コンポネントキー
const deleteKbn = ref(TeX0014Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL)

// 事業所IDバックアップ
const jigyoId = ref('')

// 履歴IDバックアップ
const teikyouIdOld = ref('')

// 入力作成日バックアップ
const createDateOld = ref('')

// ローカルOneway
const localOneway = reactive({
  // 計画対象期間選択
  orX0007Oneway: {
    kindId: systemCommonsStore.getSyubetu,
  } as OrX0007OnewayType,
  // tex0014Oneway: {
  //   /** 続柄マスタ情報 */
  //   relationshipMasterInfoList: [],
  //   /** 介護保険情報リスト */
  //   nursingCareInsuranceInfoList: [],
  //   /** 担当情報リスト */
  //   tantoInfoList: [],
  //   /** 事業所情報リスト */
  //   jigyoInfoList: [],
  //   /**  親族関係者情報リスト */
  //   kinshipInfoList: [],
  //   cpnTucTaiInfoTeikyouData: {},
  // } as HospitalizationTimeInfoOfferInitInfo,
  // 作成者選択
  orX0009Oneway: {
    showId: '',
    // モード
    mode: TeX0014Const.DEFAULT.SELECT_MODE_12,
    isDisabled: false,
  } as OrX0009OnewayType,

  // アセスメント複写
  or17391Oneway: {
    periodManagementFlg: '',
  } as Or17391OnewayType,
  // アセスメントマスタ
  or27562Oneway: {
    /** 事業者ID */
    svJigyoId: '',
    /** 分類1 */
    bunrui1Id: '2',
    /** 分類2 */
    bunrui2Id: '12',
    /** 施設ID */
    shisetuId: '',
  } as Or27562OnewayType,
  // 計画対象期間フラグ
  plainningPeriodManageFlg: false,

  // 期间選択
  periodSelectOneway: {
    /** 期間ID  */
    sc1Id: '',
  },
  // 履歴選択
  orX0008Oneway: {
    createData: {} as RirekiInfo,
  } as OrX0008OnewayType,
  // 履歴選択
  hisotrySelectOneway: {
    /** 履歴情報 */
    historyInfo: {
      /** 提供ID  */
      teikyouId: '',
      /** 期間ID  */
      sc1Id: '',
      /** 記入日  */
      createYmd: '',
      /** 職員ID  */
      chkShokuId: '',
      /** 履歴総件数  */
      krirekiCnt: '',
      /** 履歴番号  */
      krirekiNo: '',
      /** 担当ケアマネID  */
      tantoShokuId: '',
      /** ケアマネジャー氏名  */
      tantoShokuKnj: '',
      /** 居宅介護支援事業所ID  */
      shienJigyoId: '',
      /** 事業所名  */
      jigyoKnj: '',
    } as HistoryInfo1,
    pageBtnAutoDisabled: true,
  },

  // 作成日
  createDateOneway: {
    itemLabel: t('label.create-date'),
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-row',
      labelClass: 'ma-1',
    }),
  } as Mo00020OnewayType,
  // タブ
  mo00043OnewayType: {
    tabItems: [],
  } as Mo00043OnewayType,
  orX0188Oneway: {
    mode: TeX0014Const.DEFAULT.MODE_NORMAL,
  } as Or28992OnewayType,
  OrX0195Oneway: {
    mode: TeX0014Const.DEFAULT.MODE_NORMAL,
  } as Or28992OnewayType,
  mo01338Oneway: {
    value: t('label.medica-fee-for-discharge-support'),
    valueFontWeight: 'blod',
    customClass: { outerStyle: 'background: rgb(var(--v-theme-background))' } as CustomClass,
  } as Mo01338OnewayType,
  // 削除ダイアログ
  orX0001Oneway: {
    createYmd: '',
    kinouKnj: '',
    selectTabName: '',
    startTabName: '',
    endTabName: '',
  } as OrX0001OnewayType,
})

// アセスメント複写ダイアログ表示フラグ
const showDialogOr17391 = computed(() => {
  return Or17391Logic.state.get(or17391.value.uniqueCpId)?.isOpen ?? false
})

// アセスメントマスタダイアログ表示フラグ
const showDialogOr27562 = computed(() => {
  // Or27562のダイアログ開閉状態
  return Or27562Logic.state.get(or27562.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr56886 = computed(() => {
  // Or56886のダイアログ開閉状態
  return Or56886Logic.state.get(or56886.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // 各コードグループの初期化を行います。
  await initCodes([
    // 住環境
    {
      key: 'CONFIRMATION_INFO_LIVING_ENVIRONMENT',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_LIVING_ENVIRONMENT,
    },
    // 省略の選択コード区分
    { key: 'M_CD_KBN_ID_OMITTED', mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_OMITTED },
    // 入院時の要介護度（確定版）
    {
      key: 'CONFIRMATION_INFO_CARE_LEVEL_AT_ADMISSION_CONFIRMED',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_CARE_LEVEL_AT_ADMISSION_CONFIRMED,
    },
    //要支援値
    {
      key: 'SUPPORT_LEVEL_VALUE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SUPPORT_LEVEL_VALUE,
    },
    //要介護値
    {
      key: 'CARE_LEVEL_VALUE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CARE_LEVEL_VALUE,
    },
    //障害高齢者の日常生活自立度
    {
      key: 'CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DISAB',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DISAB,
    },
    //認知症高齢者の日常生活自立度
    {
      key: 'CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DEM',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DEM,
    },
    //自己負担割合有無
    {
      key: 'CONFIRMATION_INFO_OWN_BURDEN_RATIO_BEDSORE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_OWN_BURDEN_RATIO_BEDSORE,
    },
    //服薬状況
    {
      key: 'TAKING_MEDICALTION_SITUATION',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAKING_MEDICALTION_SITUATION,
    },
    //薬剤管理
    {
      key: 'MEDICINE_MANAGEMENT',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEDICINE_MANAGEMENT,
    },
    //診察方法
    {
      key: 'MEDICAL_EXAMINATION_METHOD',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEDICAL_EXAMINATION_METHOD,
    },
    //主介護者（同居別居）
    {
      key: 'CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION,
    },
    //世帯に対する配慮
    {
      key: 'CONFIRMATION_INFO_HOUSEHOLD',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_HOUSEHOLD,
    },
    //退院後の主介護者
    {
      key: 'CONFIRMATION_INFO_PRIMARY_CAREGIVER_AFTER_DISCHARGE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_PRIMARY_CAREGIVER_AFTER_DISCHARGE,
    },
    //介護力見込み
    {
      key: 'CONFIRMATION_INFO_NURSING_CARE_PROSPECT',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_NURSING_CARE_PROSPECT,
    },
    {
      key: 'CONFIRMATION_INFO_PARALYSIS_STATUS',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_PARALYSIS_STATUS,
    },
    {
      key: 'MEAL_FREQUENCY',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEAL_FREQUENCY,
    },
    {
      key: 'MEAL_WATER_RESTRICTION',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEAL_WATER_RESTRICTION,
    },
    {
      key: 'CONFIRMATION_INFO_SWALLOWING_FUNCTION',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_SWALLOWING_FUNCTION,
    },
    {
      key: 'CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY,
    },
    {
      key: 'CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS,
    },
    {
      key: 'CONFIRMATION_INFO_PORTABLE_TOILETS',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_PORTABLE_TOILETS,
    },
    {
      key: 'SLEEPING_STATE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SLEEPING_STATE,
    },
    {
      key: 'CONFIRMATION_INFO_CODE',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONFIRMATION_INFO_CODE,
    },
    {
      key: 'HOSPITALIZATION_FREQUENCY',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_HOSPITALIZATION_FREQUENCY,
    },
    {
      key: 'NOT_LIST_FIFTEEN_SITUA_ITEMS',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_NOT_LIST_FIFTEEN_SITUA_ITEMS,
    },
    {
      key: 'FREQUENCY_FHOSPITALIZATION_OVER_THE_PAST_SIX_MONTHS',
      mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FREQUENCY_FHOSPITALIZATION_OVER_THE_PAST_SIX_MONTHS,
    },
  ])
  // ブラウザの「戻る」「進む」ボタンからの遷移時に以前の値を復元したい画面の場合は
  // 画面情報領域のsupplement内のisInitで処理を分岐させて初回表示時のみ画面データの取得を行なう
  // ※値の復元が不要な画面では分岐は不要
  isInitCompleted = false
  if (isInit) {
    // コントロール設定
    await initControls()
    local.mo00043.id = OrX0188Const.DEFAULT.TAB_ID
  }
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<TeX0014StateType>({
  cpId: Or17391Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    // 保存完了処理
    isSaveCompleted: (value) => {
      if (value) {
        // 更新後の計画対象期間ID、履歴IDを再設定
        if (localOneway.periodSelectOneway.sc1Id) {
          localOneway.periodSelectOneway.sc1Id =
            TeX0014Logic.data.get(or28992.value.uniqueCpId)?.updateData?.sc1Id ?? ''
        }
        if (localOneway.orX0008Oneway.createData) {
          localOneway.orX0008Oneway.createData.createId =
            TeX0014Logic.data.get(or28992.value.uniqueCpId)?.updateData?.teikyouId ?? ''
        }

        // タブ情報再取得
        void reload()
        // 保存完了フラグをリセット
        setState({ isSaveCompleted: false })
      }
    },
    // タブ変更保存完了フラグ
    tabChangeSaveCompleted: (value) => {
      if (value) {
        // 更新後の計画対象期間ID、履歴IDを再設定
        if (localOneway.periodSelectOneway.sc1Id) {
          localOneway.periodSelectOneway.sc1Id =
            TeX0014Logic.data.get(or28992.value.uniqueCpId)?.updateData?.sc1Id ?? ''
        }
        if (localOneway.orX0008Oneway.createData) {
          localOneway.orX0008Oneway.createData.createId =
            TeX0014Logic.data.get(or28992.value.uniqueCpId)?.updateData?.teikyouId ?? ''
        }

        // タブを変更
        local.mo00043.id = tabIdBk.value

        // 保存完了フラグをリセット
        setState({ tabChangeSaveCompleted: false })
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(0)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or17391Const.CP_ID(1)]: or17391.value,
  [Or27562Const.CP_ID(0)]: or27562.value,
  [Or56886Const.CP_ID(0)]: or56886.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [OrX0009Const.CP_ID(0)]: orX0009.value,
  [OrD2002Const.CP_ID(0)]: orD2002.value,
  [Or26257Const.CP_ID(0)]: or26257.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value, // 確認ダイアログ(yes, no, cancel)
  [Or21814Const.CP_ID(2)]: or21814_2.value, // 確認ダイアログ(yes)
  [Or21814Const.CP_ID(3)]: or21814_3.value, // 確認ダイアログ(yes, no)
  [OrX0001Const.CP_ID(0)]: orX0001.value,
  [OrX0188Const.CP_ID(1)]: orX0188.value,
  [OrX0195Const.CP_ID(1)]: OrX0195.value,
  [OrX0007Const.CP_ID(0)]: orX0007.value,
  [OrX0008Const.CP_ID(0)]: orX0008.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(0)]: or00249.value,
  [Or00094Const.CP_ID(0)]: or00094.value,
})

// ★利用者一覧詳細表示(Or00248)の「領域キー」を設定
Or00248Logic.state.set({
  uniqueCpId: or00248.value.uniqueCpId,
  state: {
    regionKey: props.uniqueCpId, // 「領域キー」を設定
  },
})

systemCommonsStore.setUserSelectZenkaiSvJigyoIdList([''])
systemCommonsStore.setUserSelectSelfId('')

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange, props.uniqueCpId)

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 新しいデータの値をチェックし、全falseの場合は処理を中断する
    const isAllFalseFlg = Object.values(newValue).every((item) => item === false)
    if (isAllFalseFlg) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      $log.debug('お気に入り')
      setOr11871Event({ favoriteEventFlg: false })
      return
    }
    if (newValue.saveEventFlg) {
      $log.debug('保存')
      // 保存ボタンが押下された場合、保存処理を実行する
      setOr11871Event({ saveEventFlg: false })

      // 削除区分を設定
      deleteKbn.value = TeX0014Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL

      await setEvent({ saveEventFlg: true })
      return
    }
    if (newValue.createEventFlg) {
      // 新規ボタンが押下された場合、新規作成処理を実行する
      $log.debug('新規')
      setOr11871Event({ createEventFlg: false })
      clickMode.value = 'createNew'
      await createNew()
      return
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
      $log.debug('印刷')
      setOr11871Event({ printEventFlg: false })
      clickMode.value = 'print'
      print()
      return
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      $log.debug('マスタ他')
      // アセスメントマスタのダイアログを開く
      Or27562Logic.state.set({
        uniqueCpId: or27562.value.uniqueCpId,
        state: { isOpen: true },
      })
      setOr11871Event({ masterEventFlg: false })
      return
    }
    if (newValue.deleteEventFlg) {
      // 削除ボタンが押下された場合、削除処理を実行する
      clickMode.value = 'delete'
      await _delete()
      setOr11871Event({ deleteEventFlg: false })
      return
    }
    if (newValue.copyEventFlg) {
      // 複写ボタンが押下された場合、複写処理を実行する
      $log.debug('複写')
      _copy()
      return
    }
  }
)

/**
 * 複写画面のイベントを監視
 */
watch(
  () => Or17391Logic.event.get(Or17391Const.CP_ID(1) + or17391.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue?.confirm === false && newValue?.confirm_multiple === false) {
      return
    }

    setOr17391Event({ confirm: false, confirm_multiple: false })

    // 該当タブで確定を押下する場合
    if (newValue.confirm) {
      // 複写データを取得
      const copyData = Or17391Logic.data.get(or17391.value.uniqueCpId)
      if (copyData) {
        await setEvent({ copyEventFlg: true })
      }
    }

    // 複数タブ上書の場合
    if (newValue.confirm_multiple) {
      // タブを再表示
      await setEvent({ isRefresh: true })
    }
  }
)

/**
 * タブ変更監視
 */
watch(
  () => showDialogOr27562.value,
  () => {
    localOneway.or27562Oneway = {
      /** 事業者ID */
      svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
      /** 分類1 */
      bunrui1Id: '2',
      /** 分類2 */
      bunrui2Id: '12',
      /** 施設ID */
      shisetuId: systemCommonsStore.getShisetuId ?? '',
    }
  }
)

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 利用者一覧における利用者の変更を監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue === undefined) {
      return
    }
  }
)

/**************************************************
 * 関数
 **************************************************/
/**
 * 指定されたコードグループのコードを初期化します。
 *
 * @param codeGroups - コードを取得およびフィルタリングするためのキーとmCdKbnIdを含むオブジェクトの配列。
 */
async function initCodes(codeGroups: { key: string; mCdKbnId: number }[]) {
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList: [...codeGroups] })
  for (const group of codeGroups) {
    local.listDataGroup[group.key] = CmnSystemCodeRepository.filter(group.mCdKbnId) ?? []
  }
}
/**
 *  コントロール初期化
 */
const initControls = async () => {
  isPermissionViewAuth.value = true
  // 子コンポーネントに対して初期設定を行う
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      screenTitleLabel: t('label.hospitalization-info'),
      tooltipTextSaveBtn: t('tooltip.save'),
      tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
      tooltipTextPrintBtn: t('tooltip.care-plan2-print-setting-btn'),
      tooltipTextOptionMenuBtn: t('tooltip.regular-master-icon'),
      showFavorite: true,
      showViewSelect: false,
      viewSelectItems: [],
      showSaveBtn: true,
      disabledSaveBtn: !isPermissionViewAuth.value, //共通処理の保存権限チェックを行う
      disabledPrintBtn: await hasPrintAuth(TeX0014Const.DEFAULT.LINK_AUTH), //共通処理の印刷権限チェックを行う
      showCreateBtn: true,
      showCreateMenuCopy: false,
      showPrintBtn: true,
      showMasterBtn: false,
      showOptionMenuBtn: true,
      showOptionMenuDelete: true,
    },
  })
  clickMode.value = ''

  local.createDate.value =
    systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
  createDateOld.value = local.createDate.value

  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを初期化(yes, no, cancel)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを初期化(yes)
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
    },
  })

  // 確認ダイアログを初期化(yes, no)
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
    },
  })

  localOneway.mo00043OnewayType.tabItems = [
    {
      id: OrX0188Const.DEFAULT.TAB_ID,
      title: t('label.outer'),
    },
    {
      id: OrX0195Const.DEFAULT.TAB_ID,
      title: t('label.inner'),
    },
  ]

  TeX0014Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      /**
       * 事業所ID
       */
      jigyoId: systemCommonsStore.getSvJigyoId,
      /**
       * 計画対象期間ID
       */
      sc1Id: localOneway.periodSelectOneway.sc1Id,
      /**
       * アセスメントID
       */
      teikyouId: localOneway.orX0008Oneway.createData.createId,
      /**
       * 作成者ID
       */
      createUserId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '',
      /**
       * 作成日
       */
      createYmd: local.createDate.value,
    },
  })
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
function setOr11871Event(event: Record<string, boolean>) {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * Or17391イベント発火
 *
 * @param event - イベント
 */
function setOr17391Event(event: Record<string, boolean>) {
  Or17391Logic.event.set({
    uniqueCpId: Or17391Const.CP_ID(1) + or17391.value.uniqueCpId,
    events: event,
  })
}

/**
 * イベント発火
 *
 * @param event - イベント
 */
async function setEvent(event: Record<string, boolean>) {
  // 複写の場合
  let copyData: object = {}
  if (event.copyEventFlg === true) {
    const or17391Data = Or17391Logic.data.get(or17391.value.uniqueCpId)
    copyData = or17391Data?.resData?.tabData ?? {}
  }

  // 画面共通情報を設定
  setCommonInfo({
    /** 履歴更新回数 */
    historyModifiedCnt: historyModifiedCnt.value,
    /** 選択中タブ */
    activeTabId: local.mo00043.id,
    /** 事業所ID */
    jigyoId: systemCommonsStore.getSvJigyoId,
    /** 計画対象期間ID */
    sc1Id: localOneway.periodSelectOneway.sc1Id,
    /** アセスメントID */
    teikyouId: localOneway.orX0008Oneway.createData.createId,
    /** 作成者ID */
    createUserId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '',
    /** 作成日 */
    createYmd: local.createDate.value,
    /** 複写データ */
    copyData: copyData,
    /** 複写データ */
    deleteKbn: deleteKbn.value,
  })

  if (event.isRefresh) {
    // 画面情報再取得
    void getInitDataInfo()
    // 次の描画までまつ（ターゲットエレメントのサイズが取得できるため）
    //  nextTick()
  }
  if (event.isCreateDateChanged) {
    // 保存ボタンが押下された場合、保存処理を実行する
    // 改訂バージョンをチェック
  }

  if (event.favoriteEventFlg) {
    // お気に入りボタンが押下された場合、お気に入り処理を実行する
  }
  if (event.saveEventFlg) {
    // 保存ボタンが押下された場合、保存処理を実行する
    // 画面変更チェック
    await save()
  }
  if (event.createEventFlg) {
    // 新規ボタンが押下された場合、新規作成処理を実行する
    void handleCreate()
  }
  if (event.printEventFlg) {
    // 印刷ボタンが押下された場合、印刷設定画面を表示する
  }
  if (event.masterEventFlg) {
    // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
  }
  if (event.deleteEventFlg) {
    // 削除ボタンが押下された場合、削除処理を実行する
  }
  if (event.copyEventFlg) {
    // 複写ボタンが押下された場合、複写処理を実行する
    // const copyData = local.commonInfo.copyData as IAssessmentFaceInfoSelectOutEntity
  }
}
/**
 * 新規
 *
 */
async function handleCreate() {
  try {
    // バックエンドAPIから初期情報取得
    const inputData: HospitalizationTimeInfoOfferNewInfoSelectInEntity = {
      /** 利用者ID */
      userid: userId.value,
      /** 法人ID */
      hojinId: systemCommonsStore.getHoujinId ?? '',
      /** 施設ID */
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      /** 事業者ID */
      svJigyoId: jigyoId.value,
      /** 基準日 */
      appYmd: systemCommonsStore.getSystemDate ?? '',
      ...(local.periodInfo as AuthorityFlag),
    }

    const resData: HospitalizationTimeInfoOfferNewInfoSelectOutEntity =
      await ScreenRepository.select('hospitalizationTimeInfoOfferNewInfoSelect', inputData)
    if (resData?.data) {
      // setInit({
      //   ...localOneway.tex0014Oneway,
      //   cpnTucTaiInfoTeikyouData: resData.data.newInfo,
      // })
    }
  } finally {
    isLoading.value = false
  }
}

/**
 * 保存
 *
 */
async function save() {
  const orX0188Data = getChildCpBinds(orX0188.value.uniqueCpId, {
    orX0189: { cpPath: OrX0189Const.CP_ID(1) },
    orX0190: { cpPath: OrX0190Const.CP_ID(1) },
    orX0191: { cpPath: OrX0191Const.CP_ID(1) },
    orX0192: { cpPath: OrX0192Const.CP_ID(1) },
    orX0193: { cpPath: OrX0193Const.CP_ID(1) },
  })
  const OrX0195Data = getChildCpBinds(OrX0195.value.uniqueCpId, {
    OrX0196: { cpPath: OrX0196Const.CP_ID(1) },
    OrX0198: { cpPath: OrX0198Const.CP_ID(1) },
    OrX0197: { cpPath: OrX0197Const.CP_ID(1) },
    OrX0199: { cpPath: OrX0199Const.CP_ID(1) },
  })
  const orX0189 = orX0188Data.orX0189.twoWayBind?.value as OrX0189ValuesType
  const orX0190 = orX0188Data.orX0190.twoWayBind?.value as OrX0190ValuesType
  const orX0191 = orX0188Data.orX0191.twoWayBind?.value as OrX0191ValuesType
  const orX0192 = orX0188Data.orX0192.twoWayBind?.value as OrX0192ValuesType
  const orX0193 = orX0188Data.orX0193.twoWayBind?.value as OrX0193ValuesType
  const OrX0197 = OrX0195Data.OrX0197.twoWayBind?.value as OrX0197ValuesType
  const OrX0198 = OrX0195Data.OrX0198.twoWayBind?.value as OrX0198ValuesType
  const OrX0199 = OrX0195Data.OrX0199.twoWayBind?.value as OrX0199ValuesType
  const OrX0196 = OrX0195Data.OrX0196.twoWayBind?.value as OrX0196ValuesType
  // バックエンドAPIから初期情報取得
  const inputData: HospitalizationTimeInfoOfferUpdateInEntity = {
    /** 期間ID */
    sc1Id: '',
    /** 法人ID */
    houjinId: '',
    /** 施設ID */
    shisetuId: '',
    /** 事業者ID */
    svJigyoId: '',
    /** 利用者ID */
    userId: '',
    /** 種別ID */
    syubetsuId: '',
    /** 作成日 */
    createYmd: '',
    /** 更新区分 */
    updateKbn: '',
    historyInfo: {},
    saveInfo: {
      ...getValuesData(orX0189.orX0189Values),
      ...getValuesData(orX0190.orX0190Values),
      ...getValuesData(orX0191.orX0191Values),
      ...getValuesData(orX0192.orX0192Values),
      ...getValuesData(orX0193.orX0193Values),
      ...getValuesData(OrX0196.OrX0196Values),
      ...getValuesData(OrX0197.OrX0197Values),
      ...getValuesData(OrX0198.OrX0198Values),
      ...getValuesData(OrX0199.OrX0199Values),
    },
  }
  const resData: HospitalizationTimeInfoOfferUpdateOutEntity = await ScreenRepository.update(
    'hospitalizationTimeInfoOfferUpdate',
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === TeX0014Const.DEFAULT.SUCCESS) {
    localOneway.periodSelectOneway.sc1Id = resData.data.sc1Id
    localOneway.orX0008Oneway.createData.createId = resData.data.cc1Id
    // 画面情報再取得
    await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
    // 初期情報を取得
    await reload()
  }
}
// 根据字段名类型转换对象
function getValuesData(
  fieldObj: Record<
    string,
    | string
    | Mo00038Type
    | Mo00040Type
    | Mo00030Type
    | Mo00018Type
    | Mo00020Type
    | Mo00045Type
    | Mo00046Type
    | Mo01408Type
  >
): SaveInfo {
  const obj: SaveInfo = {}
  for (const key in fieldObj) {
    switch (getFieldType(key)) {
      case 'Mo00038Type':
        obj[key] = (fieldObj[key] as Mo00038Type)?.mo00045?.value ?? ''
        break
      case 'Mo00045Type':
        obj[key] = (fieldObj[key] as Mo00045Type)?.value ?? ''
        break
      case 'Mo00020Type':
        obj[key] = (fieldObj[key] as Mo00020Type)?.value ?? ''
        break
      case 'Mo00030Type':
        obj[key] = (fieldObj[key] as Mo00030Type)?.mo00045?.value ?? ''
        break
      case 'Mo00018Type':
        obj[key] = (fieldObj[key] as Mo00018Type)?.modelValue ? '1' : '0'
        break
      case 'Mo00040Type':
        obj[key] = (fieldObj[key] as Mo00040Type)?.modelValue
        break
      case 'Mo00046Type':
        obj[key] = (fieldObj[key] as Mo00046Type)?.value ?? ''
        break
      case 'Mo01408Type':
        obj[key] =
          (fieldObj[key] as Mo00045Type)?.value === ''
            ? '-1'
            : (fieldObj[key] as Mo00045Type)?.value
        break
      default:
        obj[key] = fieldObj[key] === '' ? '0' : (fieldObj[key] as string)
        break
    }
  }
  return obj
}

// 根据字段名获取其类型
function getFieldType(fieldName: string): string {
  return TeX0014Const.typeMap[fieldName]
}

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */
function callbackUserChange(newSelfId: string) {
  if (newSelfId !== '' && userId.value === '') {
    // 利用者変更処理
    void userChange(newSelfId)
    return
  }

  // 変更がない場合、スキップ
  if (newSelfId === userId.value) {
    return
  }

  // 利用者変更処理
  void userChange(newSelfId)
}

/**
 *  画面初期情報再取得
 */
async function reload() {
  // 計画対象期間がない場合、リロードしない
  if (
    plannningPeriodManageFlg.value === TeX0014Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
    (localOneway.periodSelectOneway.sc1Id === undefined ||
      localOneway.periodSelectOneway.sc1Id === '')
  ) {
    return
  }

  // 作成者選択アイコンボタンを活性
  localOneway.orX0009Oneway.isDisabled = false
  // 作成日を活性
  localOneway.createDateOneway.disabled = false
  // 削除区分を設定
  deleteKbn.value = TeX0014Const.DEFAULT.DELETE_PROCESS_KBN_NORMAL

  // 画面共通情報を設定
  setCommonInfo({
    /** 履歴更新回数 */
    historyModifiedCnt: historyModifiedCnt.value,
    /** 選択中タブ */
    activeTabId: local.mo00043.id,
    /** 事業所ID */
    jigyoId: systemCommonsStore.getSvJigyoId,
    /** 利用者ID */
    userId: userId.value,
    /** 計画対象期間ID */
    sc1Id: localOneway.periodSelectOneway.sc1Id,
    /** teikyouIdID */
    teikyouId: localOneway.orX0008Oneway.createData.createId,
    /** 作成者ID */
    createUserId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '',
    /** 作成日 */
    createYmd: local.createDate.value,
  })

  // 画面情報再取得
  await setEvent({ isRefresh: true })
}

/**
 *  画面初期情報取得
 *
 */
async function getInitDataInfo() {
  isLoading.value = true
  useScreenStore().setCpNavControl({
    cpId: TeX0014Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    editFlg: false,
  })
  if (!localOneway.orX0008Oneway.createData.createId) {
    await handleCreate()
  } else {
    try {
      // バックエンドAPIから初期情報取得
      const inputData: MovingOutLeavingInfoOfferInitInfoSelectInEntity = {
        /** 提供ID */
        recId: localOneway.orX0008Oneway.createData.createId,
      }
      const resData: MovingOutLeavingInfoOfferInitInfoSelectOutEntity =
        await ScreenRepository.select('movingOutLeavingInfoOfferInitInfoSelect', inputData)
      if (resData?.data) {
        // localOneway.tex0014Oneway = resData.data // cpnTucTaiInfoTeikyouData
        setInit(resData)
      }
    } finally {
      isLoading.value = false
    }
  }
}
/**
 *  画面情報を設定
 *
 * @param resData - 設定情報
 */
function setInit(resData: MovingOutLeavingInfoOfferInitInfoSelectOutEntity) {
  const data: { cpnTucTaiInfoTeikyouData: CpnTucTaiInfoTeikyouData } = resData.data as {
    cpnTucTaiInfoTeikyouData: CpnTucTaiInfoTeikyouData
  }
  // 画面情報を設定
  setChildCpBinds(orX0188.value.uniqueCpId, {
    [OrX0189Const.CP_ID(1)]: {
      twoWayValue: {
        orX0189Values: {
          shisetuKnj: { value: data.cpnTucTaiInfoTeikyouData.shisetuKnj },
          tantoShokuId: data.cpnTucTaiInfoTeikyouData.tantoShokuId,
          teikyouYmd: {
            value: data.cpnTucTaiInfoTeikyouData.teikyouYmd,
            mo01343: {
              value: data.cpnTucTaiInfoTeikyouData.teikyouYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          taiYmd: {
            value: data.cpnTucTaiInfoTeikyouData.taiYmd,
            mo01343: {
              value: data.cpnTucTaiInfoTeikyouData.taiYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          shisetuSeikatuYmd: {
            value: data.cpnTucTaiInfoTeikyouData.shisetuSeikatuYmd,
            mo01343: {
              value: data.cpnTucTaiInfoTeikyouData.shisetuSeikatuYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          hospKnj: {
            value: data.cpnTucTaiInfoTeikyouData.hospKnj,
          },
          hospTantoKnj: {
            value: data.cpnTucTaiInfoTeikyouData.hospTantoKnj,
          },
          tel: { mo00045: { value: data.cpnTucTaiInfoTeikyouData.tel } },
          fax: { mo00045: { value: data.cpnTucTaiInfoTeikyouData.fax } },
        },
      },
      oneWayState: {
        codeList: {
          // jigyoInfoList: data.jigyoInfoList,
          // tantoInfoList: data.tantoInfoList,
        },
      },
    },
    [OrX0190Const.CP_ID(1)]: {
      twoWayValue: {
        orX0190Values: {
          yokaiKbnFlg: data.cpnTucTaiInfoTeikyouData.yokaiKbnFlg ?? '0',
          taiYokaiKbn: data.cpnTucTaiInfoTeikyouData.taiYokaiKbn ?? '0',
          ninteiStartYmd: {
            value: data.cpnTucTaiInfoTeikyouData.ninteiStartYmd,
            mo01343: {
              value: data.cpnTucTaiInfoTeikyouData.ninteiStartYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          ninteiEndYmd: {
            value: data.cpnTucTaiInfoTeikyouData.ninteiEndYmd,
            mo01343: {
              value: data.cpnTucTaiInfoTeikyouData.ninteiEndYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          ninteiShinseiFlg: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninteiShinseiFlg),
          },
          ninteiShinseiYmd: {
            value: data.cpnTucTaiInfoTeikyouData.ninteiShinseiYmd,
            mo01343: {
              value: data.cpnTucTaiInfoTeikyouData.ninteiShinseiYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          ninteiKbnHenkouFlg: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninteiKbnHenkouFlg),
          },
          ninteiKbnHenkouYmd: {
            value: data.cpnTucTaiInfoTeikyouData.ninteiKbnHenkouYmd,
            mo01343: {
              value: data.cpnTucTaiInfoTeikyouData.ninteiKbnHenkouYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          ninteiMishinseiFlg: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninteiMishinseiFlg),
          },
          shogaiJiritsuCd: data.cpnTucTaiInfoTeikyouData.shogaiJiritsuCd ?? '-1',
          ninchiJiritsuCd: data.cpnTucTaiInfoTeikyouData.ninchiJiritsuCd ?? '-1',
          futanWariFlg: data.cpnTucTaiInfoTeikyouData.futanWariFlg ?? '0',
          futanWariai: { mo00045: { value: data.cpnTucTaiInfoTeikyouData.futanWariai } },
          shogaiTechouUmu: data.cpnTucTaiInfoTeikyouData.shogaiTechouUmu ?? '0',
          shintaiShogaiKbn: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.shintaiShogaiKbn),
          },
          chitekiShogaiKbn: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.chitekiShogaiKbn),
          },
          seishinShogaiKbn: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.seishinShogaiKbn),
          },
          nenkin1Umu: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.nenkin1Umu) },
          nenkin2Umu: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.nenkin2Umu) },
          nenkin3Umu: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.nenkin3Umu) },
          nenkin4Umu: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.nenkin4Umu) },
          nenkin5Umu: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.nenkin5Umu) },
          nenkinMemoKnj: { value: data.cpnTucTaiInfoTeikyouData.nenkinMemoKnj },
        },
      },
      oneWayState: {
        codeList: {
          CONFIRMATION_INFO_LIVING_ENVIRONMENT:
            local.listDataGroup.CONFIRMATION_INFO_LIVING_ENVIRONMENT,
          M_CD_KBN_ID_OMITTED: local.listDataGroup.M_CD_KBN_ID_OMITTED,
          CONFIRMATION_INFO_CARE_LEVEL_AT_ADMISSION_CONFIRMED:
            local.listDataGroup.CONFIRMATION_INFO_CARE_LEVEL_AT_ADMISSION_CONFIRMED,
          SUPPORT_LEVEL_VALUE: local.listDataGroup.SUPPORT_LEVEL_VALUE,
          CARE_LEVEL_VALUE: local.listDataGroup.CARE_LEVEL_VALUE,
          CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DISAB:
            local.listDataGroup.CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DISAB,
          CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DEM:
            local.listDataGroup.CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DEM,
          CONFIRMATION_INFO_OWN_BURDEN_RATIO_BEDSORE:
            local.listDataGroup.CONFIRMATION_INFO_OWN_BURDEN_RATIO_BEDSORE,
        },
      },
    },
    [OrX0191Const.CP_ID(1)]: {
      twoWayValue: {
        orX0191Values: {
          kaigoMainKnj: { value: data.cpnTucTaiInfoTeikyouData.kaigoMainKnj },
          kaigoMainZcode: {
            // modelValue: kaigoMainZcode ? kaigoMainZcode.zcode : null,
          },
          kaigoMainAge: { mo00045: { value: data.cpnTucTaiInfoTeikyouData.kaigoMainAge } },
          kaigoMainKousei: data.cpnTucTaiInfoTeikyouData.kaigoMainKousei ?? '0',
          kaigoMainTel: { value: data.cpnTucTaiInfoTeikyouData.kaigoMainTel },
          decisionSupporterKnj: { value: data.cpnTucTaiInfoTeikyouData.decisionSupporterKnj },
          decisionSupporterZcode: {
            // modelValue: decisionSupporterZcode ? decisionSupporterZcode.zcode : null,
          },
          decisionSupporterAge: {
            mo00045: { value: data.cpnTucTaiInfoTeikyouData.decisionSupporterAge },
          },
          decisionSupporterKousei: data.cpnTucTaiInfoTeikyouData.decisionSupporterKousei ?? '0',
          decisionSupporterTel: { value: data.cpnTucTaiInfoTeikyouData.decisionSupporterTel },
          ishisotuuShiryoku: data.cpnTucTaiInfoTeikyouData.ishisotuuShiryoku,
          ishisotuuChouryoku: data.cpnTucTaiInfoTeikyouData.ishisotuuChouryoku,
          ishisotuuMegane: data.cpnTucTaiInfoTeikyouData.ishisotuuMegane,
          ishisotuuHochouki: data.cpnTucTaiInfoTeikyouData.ishisotuuHochouki,
          ishisotuu1: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ishisotuu1),
          },
          ishisotuu2: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ishisotuu2),
          },
          ishisotuu3: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ishisotuu3),
          },
          ishisotuu4: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ishisotuu4),
          },
          ishisotuu5: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ishisotuu5),
          },
        },
      },
      oneWayState: {
        codeList: {
          // kinshipInfoList: data.kinshipInfoList,
          // relationshipMasterInfoList: data.relationshipMasterInfoList,
          CONFIRMATION_INFO_CODE: local.listDataGroup.CONFIRMATION_INFO_CODE,
          M_CD_KBN_ID_OMITTED: local.listDataGroup.M_CD_KBN_ID_OMITTED,
          CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION:
            local.listDataGroup.CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION,
        },
      },
    },
    [OrX0192Const.CP_ID(1)]: {
      twoWayValue: {
        orX0192Values: {
          sesshuHouhouKeikou: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.sesshuHouhouKeikou),
          },
          sesshuHouhouKeikan: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.sesshuHouhouKeikan),
          },
          sesshuHouhouJoumyaku: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.sesshuHouhouJoumyaku),
          },
          foodAllergy: data.cpnTucTaiInfoTeikyouData.foodAllergy ?? '',
          foodAllergyKnj: { value: data.cpnTucTaiInfoTeikyouData.foodAllergyKnj ?? '' },
          sesshokuEngeShougai: data.cpnTucTaiInfoTeikyouData.sesshokuEngeShougai ?? '',
          suibun: data.cpnTucTaiInfoTeikyouData.suibun ?? '',
          suibunKbn1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.suibunKbn1) },
          suibunKbn2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.suibunKbn2) },
          suibunKbn3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.suibunKbn3) },
          shushokuGohan: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.shushokuGohan) },
          shushokuNanhan: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.shushokuNanhan) },
          shushokuKayu: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.shushokuKayu) },
          shushokuSonota: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.shushokuSonota) },
          shushokuSonotaKnj: { value: data.cpnTucTaiInfoTeikyouData.shushokuSonotaKnj ?? '' },
          fukushokuHutuu: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.fukushokuHutuu) },
          fukushokuNansai: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.fukushokuNansai) },
          fukushokuSonota: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.fukushokuSonota) },
          fukushokuSonotaKnj: { value: data.cpnTucTaiInfoTeikyouData.fukushokuSonotaKnj ?? '' },
          gishiUmu: data.cpnTucTaiInfoTeikyouData.gishiUmu ?? '',
          gishiKbn: data.cpnTucTaiInfoTeikyouData.gishiKbn ?? '',
          kamiawase: data.cpnTucTaiInfoTeikyouData.kamiawase ?? '',
          koukuuCare: data.cpnTucTaiInfoTeikyouData.koukuuCare ?? '',
          koukuuCareShiniku: data.cpnTucTaiInfoTeikyouData.koukuuCareShiniku ?? '',
          koukuuCareShinikuKnj: { value: data.cpnTucTaiInfoTeikyouData.koukuuCareShinikuKnj ?? '' },
          koukuuEiyoTokkiKnj: { value: data.cpnTucTaiInfoTeikyouData.koukuuEiyoTokkiKnj ?? '' },
        },
      },
      oneWayState: {
        codeList: {
          M_CD_KBN_ID_OMITTED: local.listDataGroup.M_CD_KBN_ID_OMITTED,
          CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY:
            local.listDataGroup.CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY,
          NOT_LIST_FIFTEEN_SITUATION_ITEMS: local.listDataGroup.NOT_LIST_FIFTEEN_SITUATION_ITEMS,
        },
      },
    },
    [OrX0193Const.CP_ID(1)]: {
      twoWayValue: {
        orX0193Values: {
          kaigoMainKnj: { value: data.cpnTucTaiInfoTeikyouData.kaigoMainKnj ?? '' },
          kaigoMainZcode: { modelValue: data.cpnTucTaiInfoTeikyouData.kaigoMainZcode ?? null },
          kaigoMainAge: { mo00045: { value: data.cpnTucTaiInfoTeikyouData.kaigoMainAge ?? '' } },
          kaigoMainKousei: data.cpnTucTaiInfoTeikyouData.kaigoMainKousei ?? '0',
          kaigoMainTel: { value: data.cpnTucTaiInfoTeikyouData.kaigoMainTel ?? '' },
          decisionSupporterKnj: { value: data.cpnTucTaiInfoTeikyouData.decisionSupporterKnj ?? '' },
          decisionSupporterZcode: {
            modelValue: data.cpnTucTaiInfoTeikyouData.decisionSupporterZcode ?? null,
          },
          decisionSupporterAge: {
            mo00045: { value: data.cpnTucTaiInfoTeikyouData.decisionSupporterAge ?? '' },
          },
          decisionSupporterKousei: data.cpnTucTaiInfoTeikyouData.decisionSupporterKousei ?? '0',
          decisionSupporterTel: { value: data.cpnTucTaiInfoTeikyouData.decisionSupporterTel ?? '' },
          ishisotuuShiryoku: data.cpnTucTaiInfoTeikyouData.ishisotuuShiryoku ?? '',
          ishisotuuChouryoku: data.cpnTucTaiInfoTeikyouData.ishisotuuChouryoku ?? '',
          ishisotuuMegane: data.cpnTucTaiInfoTeikyouData.ishisotuuMegane ?? '',
          ishisotuuHochouki: data.cpnTucTaiInfoTeikyouData.ishisotuuHochouki ?? '',
          ishisotuu1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ishisotuu1) },
          ishisotuu2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ishisotuu2) },
          ishisotuu3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ishisotuu3) },
          ishisotuu4: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ishisotuu4) },
          ishisotuu5: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ishisotuu5) },
          sesshuHouhouKeikou: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.sesshuHouhouKeikou),
          },
          sesshuHouhouKeikan: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.sesshuHouhouKeikan),
          },
          sesshuHouhouJoumyaku: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.sesshuHouhouJoumyaku),
          },
          foodAllergy: data.cpnTucTaiInfoTeikyouData.foodAllergy ?? '',
          foodAllergyKnj: { value: data.cpnTucTaiInfoTeikyouData.foodAllergyKnj ?? '' },
          sesshokuEngeShougai: data.cpnTucTaiInfoTeikyouData.sesshokuEngeShougai ?? '',
          suibun: data.cpnTucTaiInfoTeikyouData.suibun ?? '',
          suibunKbn1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.suibunKbn1) },
          suibunKbn2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.suibunKbn2) },
          suibunKbn3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.suibunKbn3) },
          shushokuGohan: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.shushokuGohan) },
          shushokuNanhan: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.shushokuNanhan) },
          shushokuKayu: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.shushokuKayu) },
          shushokuSonota: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.shushokuSonota) },
          shushokuSonotaKnj: { value: data.cpnTucTaiInfoTeikyouData.shushokuSonotaKnj ?? '' },
          fukushokuHutuu: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.fukushokuHutuu) },
          fukushokuNansai: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.fukushokuNansai) },
          fukushokuSonota: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.fukushokuSonota) },
          fukushokuSonotaKnj: { value: data.cpnTucTaiInfoTeikyouData.fukushokuSonotaKnj ?? '' },
          gishiUmu: data.cpnTucTaiInfoTeikyouData.gishiUmu ?? '',
          gishiKbn: data.cpnTucTaiInfoTeikyouData.gishiKbn ?? '',
          kamiawase: data.cpnTucTaiInfoTeikyouData.kamiawase ?? '',
          koukuuCare: data.cpnTucTaiInfoTeikyouData.koukuuCare ?? '',
          koukuuCareShiniku: data.cpnTucTaiInfoTeikyouData.koukuuCareShiniku ?? '',
          koukuuCareShinikuKnj: { value: data.cpnTucTaiInfoTeikyouData.koukuuCareShinikuKnj ?? '' },
          koukuuEiyoTokkiKnj: { value: data.cpnTucTaiInfoTeikyouData.koukuuEiyoTokkiKnj ?? '' },
          drugUmu: data.cpnTucTaiInfoTeikyouData.drugUmu ?? '',
          ryouyouKanriUmu: data.cpnTucTaiInfoTeikyouData.ryouyouKanriUmu ?? '',
          ryouyouKanriShokushuKnj: {
            value: data.cpnTucTaiInfoTeikyouData.ryouyouKanriShokushuKnj ?? '',
          },
          drugKanri: data.cpnTucTaiInfoTeikyouData.drugKanri ?? '',
          drugKanriKnj: { value: data.cpnTucTaiInfoTeikyouData.drugKanriKnj ?? '' },
          drugKaijo1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.drugKaijo1) },
          drugKaijo2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.drugKaijo2) },
          drugKaijo3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.drugKaijo3) },
          drugKaijoKnj: { value: data.cpnTucTaiInfoTeikyouData.drugKaijoKnj ?? '' },
          drugAllergy: data.cpnTucTaiInfoTeikyouData.drugAllergy ?? '',
          drugAllergyKnj: { value: data.cpnTucTaiInfoTeikyouData.drugAllergyKnj ?? '' },
          drugTokkiUmu: data.cpnTucTaiInfoTeikyouData.drugTokkiUmu ?? '',
          drugTokkiKnj: { value: data.cpnTucTaiInfoTeikyouData.drugTokkiKnj ?? '' },
          ikouHanashiai: data.cpnTucTaiInfoTeikyouData.ikouHanashiai ?? '',
          ikouHanashiaiLastYmd: { value: data.cpnTucTaiInfoTeikyouData.ikouHanashiaiLastYmd ?? '' },
          ikouHanashiaiKbn: data.cpnTucTaiInfoTeikyouData.ikouHanashiaiKbn ?? '',
          honinnKazokuIkouKbn1: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.honinnKazokuIkouKbn1),
          },
          honinnKazokuIkouKbn2: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.honinnKazokuIkouKbn2),
          },
          honinnKazokuIkouShoruiKnj: {
            value: data.cpnTucTaiInfoTeikyouData.honinnKazokuIkouShoruiKnj ?? '',
          },
          sankaHonnin: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.sankaHonnin) },
          sankaKazoku: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.sankaKazoku) },
          sankaKazoku1Knj: { value: data.cpnTucTaiInfoTeikyouData.sankaKazoku1Knj ?? '' },
          sankaKazoku1Zcode: { modelValue: data.cpnTucTaiInfoTeikyouData.sankaKazoku1Zcode ?? '' },
          sankaKazoku2Knj: { value: data.cpnTucTaiInfoTeikyouData.sankaKazoku2Knj ?? '' },
          sankaKazoku2Zcode: { modelValue: data.cpnTucTaiInfoTeikyouData.sankaKazoku2Zcode ?? '' },
          sankaIryouCare: data.cpnTucTaiInfoTeikyouData.sankaIryouCare ?? '',
          sankaSonota: data.cpnTucTaiInfoTeikyouData.sankaSonota ?? '',
          sankaSonotaKnj: { value: data.cpnTucTaiInfoTeikyouData.sankaSonotaKnj ?? '' },
          hanashiaiKnj: { value: data.cpnTucTaiInfoTeikyouData.hanashiaiKnj ?? '' },
          sonotaKyouyuuKnj: { value: data.cpnTucTaiInfoTeikyouData.sonotaKyouyuuKnj ?? '' },
        },
      },
      oneWayState: {
        codeList: {
          // kinshipInfoList: data.kinshipInfoList,
          CONFIRMATION_INFO_HOUSEHOLD: local.listDataGroup.CONFIRMATION_INFO_HOUSEHOLD,
          M_CD_KBN_ID_OMITTED: local.listDataGroup.M_CD_KBN_ID_OMITTED,
          CONFIRMATION_INFO_PRIMARY_CAREGIVER_AFTER_DISCHARGE:
            local.listDataGroup.CONFIRMATION_INFO_PRIMARY_CAREGIVER_AFTER_DISCHARGE,
          CONFIRMATION_INFO_NURSING_CARE_PROSPECT:
            local.listDataGroup.CONFIRMATION_INFO_NURSING_CARE_PROSPECT,
          // relationshipMasterInfoList: data.relationshipMasterInfoList,
        },
      },
    },
  })

  setChildCpBinds(OrX0195.value.uniqueCpId, {
    [OrX0196Const.CP_ID(1)]: {
      twoWayValue: {
        OrX0196Values: {
          // 麻痺の状況
          mahi1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.mahi1) },
          mahi2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.mahi2) },
          mahi3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.mahi3) },
          mahi4: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.mahi4) },
          // 褥瘡の有無
          jyokusouUmu: data.cpnTucTaiInfoTeikyouData.jyokusouUmu ?? '0',
          /** 褥瘡：部位・深度・大きさ等  */
          jyokusouKnj: { value: data.cpnTucTaiInfoTeikyouData.jyokusouKnj },
          /**褥瘡への対応：エアーマット */
          jyokusouTaiouAirmat: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.jyokusouTaiouAirmat),
          },

          /**褥瘡への対応：クッション */
          jyokusouTaiouCushion: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.jyokusouTaiouCushion),
          },
          /**褥瘡への対応：体位変換 */
          jyokusouTaiouTaii: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.jyokusouTaiouTaii),
          },
          /**褥瘡への対応：体位変換欄（時間） */
          jyokusouTaiouTaiiKnj: data.cpnTucTaiInfoTeikyouData.jyokusouTaiouTaiiKnj,

          /**褥瘡への対応：その他 */
          jyokusouTaiouSonota: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.jyokusouTaiouSonota),
          },
          /**褥瘡への対応：その他内容 */

          jyokusouTaiouSonotaKnj: data.cpnTucTaiInfoTeikyouData.jyokusouTaiouSonotaKnj,
          /**褥瘡への対応：なし */

          jyokusouTaiouNashi: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.jyokusouTaiouNashi),
          },
          // ADL移乗
          adlIjyou1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIjyou1) },
          adlIjyou2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIjyou2) },
          adlIjyou3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIjyou3) },
          adlIjyou4: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIjyou4) },

          // ADL移動
          adlIdou1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdou1) },
          adlIdou2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdou2) },
          adlIdou3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdou3) },
          adlIdou4: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdou4) },

          //移動（屋外）
          adlIdouOkugai1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdouOkugai1) },
          adlIdouOkugai2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdouOkugai2) },
          adlIdouOkugai3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdouOkugai3) },
          adlIdouOkugai4: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdouOkugai4) },

          //  移動（屋内）
          adlIdouOkunai1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdouOkunai1) },
          adlIdouOkunai2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdouOkunai2) },
          adlIdouOkunai3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdouOkunai3) },
          adlIdouOkunai4: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIdouOkunai4) },

          // 食事
          adlShokuji1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlShokuji1) },
          adlShokuji2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlShokuji2) },
          adlShokuji3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlShokuji3) },
          adlShokuji4: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlShokuji4) },

          // *排泄
          adlHaisetu1: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlHaisetu1) },
          adlHaisetu2: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlHaisetu2) },
          adlHaisetu3: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlHaisetu3) },
          adlHaisetu4: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlHaisetu4) },

          //アセスメントシート（フェイスシート）
          adlIadlAssessment: {
            modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIadlAssessment),
          },
          //その他
          adlIadlSonota: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.adlIadlSonota) },
          //その他内容
          adlIadlSonotaKnj: data.cpnTucTaiInfoTeikyouData.adlIadlSonotaKnj,

          //直近2週間以内の変化
          adlIadlTwoweek: data.cpnTucTaiInfoTeikyouData.adlIadlTwoweek ?? '0',
          //直近2週間以内の変化欄
          adlIadlTwoweekKnj: data.cpnTucTaiInfoTeikyouData.adlIadlTwoweekKnj,

          // みまもりの必要性
          ninchiJoukyou11: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou11) },
          ninchiJoukyou12: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou12) },
          ninchiJoukyou13: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou13) },
          ninchiJoukyou14: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou14) },
          ninchiJoukyou15: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou15) },

          //見当識1
          ninchiJoukyou21: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou21) },
          ninchiJoukyou22: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou22) },
          ninchiJoukyou23: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou23) },
          ninchiJoukyou24: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou24) },
          ninchiJoukyou25: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou25) },

          //近時記憶
          ninchiJoukyou31: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou31) },
          ninchiJoukyou32: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou32) },
          ninchiJoukyou33: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou33) },
          ninchiJoukyou34: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou34) },
          ninchiJoukyou35: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou35) },

          //遂行能力
          ninchiJoukyou41: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou41) },
          ninchiJoukyou42: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou42) },
          ninchiJoukyou43: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou43) },
          ninchiJoukyou44: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou44) },
          ninchiJoukyou45: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.ninchiJoukyou45) },
          // 過去半年間における入院
          kakoNyuuin: data.cpnTucTaiInfoTeikyouData.kakoNyuuin ?? '0',
          //入院頻度
          nyuuinHindo: data.cpnTucTaiInfoTeikyouData.nyuuinHindo ?? '0',
          //入院理由
          nyuuinRiyuKnj: data.cpnTucTaiInfoTeikyouData.nyuuinRiyuKnj,
          //入院期間
          nyuuinKikanStartYmd: {
            value: data.cpnTucTaiInfoTeikyouData.nyuuinKikanStartYmd,
            mo01343: {
              value: data.cpnTucTaiInfoTeikyouData.nyuuinKikanStartYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          nyuuinKikanEndYmd: {
            value: data.cpnTucTaiInfoTeikyouData.nyuuinKikanEndYmd,
            mo01343: {
              value: data.cpnTucTaiInfoTeikyouData.nyuuinKikanEndYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
        },
      },
      oneWayState: {
        codeList: {
          CONFIRMATION_INFO_PARALYSIS_STATUS:
            local.listDataGroup.CONFIRMATION_INFO_PARALYSIS_STATUS,
          MEAL_FREQUENCY: local.listDataGroup.MEAL_FREQUENCY,
          MEAL_WATER_RESTRICTION: local.listDataGroup.MEAL_WATER_RESTRICTION,
          CONFIRMATION_INFO_SWALLOWING_FUNCTION:
            local.listDataGroup.CONFIRMATION_INFO_SWALLOWING_FUNCTION,
          CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY:
            local.listDataGroup.CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY,
          CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS:
            local.listDataGroup.CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS,
          CONFIRMATION_INFO_PORTABLE_TOILETS:
            local.listDataGroup.CONFIRMATION_INFO_PORTABLE_TOILETS,
          SLEEPING_STATE: local.listDataGroup.SLEEPING_STATE,
          CONFIRMATION_INFO_CODE: local.listDataGroup.CONFIRMATION_INFO_CODE,
          HOSPITALIZATION_FREQUENCY: local.listDataGroup.HOSPITALIZATION_FREQUENCY,
          FREQUENCY_FHOSPITALIZATION_OVER_THE_PAST_SIX_MONTHS:
            local.listDataGroup.FREQUENCY_FHOSPITALIZATION_OVER_THE_PAST_SIX_MONTHS,
          M_CD_KBN_ID_OMITTED: local.listDataGroup.M_CD_KBN_ID_OMITTED.map((item) => {
            return {
              ...item,
              value: item.value.toString(),
            }
          }),
        },
      },
    },
    // [OrX0197Const.CP_ID(1)]: {
    //   twoWayValue: {
    //     OrX0197Values: {
    //       /**施設サービス計画(1)～(3) */
    //       kaigoIryoShisetuService: {
    //         modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.kaigoIryoShisetuService),
    //       },
    //       /**アセスメントシート（フェースシート） */
    //       kaigoIryoAssessment: {
    //         modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.kaigoIryoAssessment),
    //       },
    //       /**その他 */
    //       kaigoIryoSonota: { modelValue: !!Number(data.cpnTucTaiInfoTeikyouData.kaigoIryoSonota) },
    //       /**その他内容 */
    //       kaigoIryoSonotaKnj: data.cpnTucTaiInfoTeikyouData.kaigoIryoSonotaKnj,
    //       /**特記事項（介護/医療の状況） */
    //       kaigoIryoTokkiKnj: data.cpnTucTaiInfoTeikyouData.kaigoIryoTokkiKnj,
    //     },
    //   },
    //   oneWayState: {},
    // },
    // [OrX0198Const.CP_ID(1)]: {
    //   twoWayValue: {
    //     OrX0198Values: {
    //       /** かかりつけ医療機関１ */
    //       iryoKikan1Knj: data.cpnTucTaiInfoTeikyouData.iryoKikan1Knj,
    //       /** 医師名１ */
    //       ishi1Knj: data.cpnTucTaiInfoTeikyouData.ishi1Knj,
    //       /** かかりつけ医療機関２ */
    //       iryoKikan2Knj: data.cpnTucTaiInfoTeikyouData.iryoKikan2Knj,
    //       /** 医師名２ */
    //       ishi2Knj: data.cpnTucTaiInfoTeikyouData.ishi2Knj,
    //       /** かかりつけ歯科医療機関 */
    //       shikaIryoKikanKnj: data.cpnTucTaiInfoTeikyouData.shikaIryoKikanKnj,
    //       /** 歯科医師名 */
    //       shikaIshiKnj: data.cpnTucTaiInfoTeikyouData.shikaIshiKnj,
    //       /** かかりつけ薬局 */
    //       yakkyokuKnj: data.cpnTucTaiInfoTeikyouData.yakkyokuKnj,
    //     },
    //   },
    //   oneWayState: {
    //     codeList: {
    //       MEDICAL_EXAMINATION_METHOD: local.listDataGroup.MEDICAL_EXAMINATION_METHOD,
    //     },
    //   },
    // },
    // [OrX0199Const.CP_ID(1)]: {
    //   twoWayValue: {
    //     OrX0199Values: {
    //       /**「退院前カンファレンス」への参加 */
    //       conferenceSanka: data.cpnTucTaiInfoTeikyouData.conferenceSanka,
    //       /** 具体的な要望 */
    //       youbouKnj: data.cpnTucTaiInfoTeikyouData.youbouKnj,
    //     },
    //   },
    //   oneWayState: {
    //     codeList: {
    //       MEDICAL_EXAMINATION_METHOD: local.listDataGroup.MEDICAL_EXAMINATION_METHOD,
    //     },
    //   },
    // },
  })
}

/**
 *  画面共通情報を設定
 *
 * @param data - 設定情報
 */
function setCommonInfo(data: TeX0014Type) {
  const existedData = TeX0014Logic.data.get(props.uniqueCpId)
  const newData = {
    /** 履歴更新回数 */
    historyModifiedCnt: historyModifiedCnt.value,
    /** 選択中タブ */
    activeTabId: data.activeTabId ?? existedData?.activeTabId,
    /** 事業所ID */
    jigyoId: data.jigyoId ?? existedData?.jigyoId,
    /** 計画期間ID */
    sc1Id: data.sc1Id ?? existedData?.sc1Id,
    /** アセスメントID */
    teikyouId: data.teikyouId ?? existedData?.teikyouId,
    /** 作成者ID */
    createUserId: data.createUserId ?? existedData?.createUserId,
    /** 履歴作成日 */
    createYmd: data.createYmd ?? existedData?.createYmd,
    /** 複写データ */
    copyData: data.copyData ?? existedData?.copyData,
    /** 削除区分 */
    deleteKbn: deleteKbn.value ?? existedData?.deleteKbn,
    /** 介護保険権限フラグ */
    nursingCareInsuranceAuthorityFlag:
      data.nursingCareInsuranceAuthorityFlag ?? existedData?.nursingCareInsuranceAuthorityFlag,
    /** 親族関係者権限フラグ */
    kinshipAuthorityFlag: data.kinshipAuthorityFlag ?? existedData?.kinshipAuthorityFlag,
    /** 既往歴権限フラグ */
    pastMedicalHistoryAuthorityFlag:
      data.pastMedicalHistoryAuthorityFlag ?? existedData?.pastMedicalHistoryAuthorityFlag,
  } as TeX0014Type

  TeX0014Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      /** 履歴更新回数 */
      historyModifiedCnt: newData.historyModifiedCnt,
      /** 選択中タブ */
      activeTabId: newData.activeTabId,
      /** 事業所ID */
      jigyoId: newData.jigyoId,
      /** 計画期間ID */
      sc1Id: newData.sc1Id,
      /** アセスメントID */
      teikyouId: newData.teikyouId,
      /** 作成者ID */
      createUserId: newData.createUserId,
      /** 履歴作成日 */
      createYmd: newData.createYmd,
      /** 複写データ */
      copyData: newData.copyData,
      /** 削除区分 */
      deleteKbn: newData.deleteKbn,
      /** 介護保険権限フラグ */
      nursingCareInsuranceAuthorityFlag: newData.nursingCareInsuranceAuthorityFlag,
      /** 親族関係者権限フラグ */
      kinshipAuthorityFlag: newData.kinshipAuthorityFlag,
      /** 既往歴権限フラグ */
      pastMedicalHistoryAuthorityFlag: newData.pastMedicalHistoryAuthorityFlag,
    },
  })
}

/**
 *  計画対象期間チェンジ設定
 *
 * @param sc1Id - 計画対象期間ID
 *
 * @param pageKbn - 期間処理区分
 */
async function getPlanningPeriodInfo(sc1Id: string, pageKbn: string) {
  try {
    // バックエンドAPIから初期情報取得
    const inputData: HospitalizationTimeInfoOfferPeriodSelectInEntity = {
      /** 事業所ID */
      svJigyoId: jigyoId.value,
      /** システムコード */
      gsysCd: systemCommonsStore.getSystemCode ?? '1',
      /** 利用者ID */
      userid: userId.value,
      /** 種別ID */
      syubetsuId: systemCommonsStore.getSyubetu ?? '',
      /** 施設ID */
      shisetuId: systemCommonsStore.getShisetuId ?? '',
      /** 職員ID  */
      shokuId: systemCommonsStore.getStaffId ?? '1',
      /** 期間ID  */
      sc1Id: sc1Id ? sc1Id : '1',
      /** 計画期間ページ区分  */
      pageKbn: pageKbn ?? '0',
    }
    const resData: HospitalizationTimeInfoOfferPeriodSelectOutEntity =
      await ScreenRepository.select('hospitalizationTimeInfoOfferPeriodSelect', inputData)
    /** =============画面情報を設定============= */
    if (resData?.data) {
      local.periodInfo = {
        ...resData.data,
        /** 計画期間情報 */
        planPeriodInfo: undefined,
        /** 履歴情報 */
        historyInfo: undefined,
      }
      // 画面共通情報を設定
      setCommonInfo({
        // 介護保険権限
        nursingCareInsuranceAuthorityFlag: resData.data.nursingCareInsuranceAuthorityFlag,
        // 親族関係者権限フラグ
        kinshipAuthorityFlag: resData.data.kinshipAuthorityFlag,
        // 既往歴権限フラグ
        pastMedicalHistoryAuthorityFlag: resData.data.pastMedicalHistoryAuthorityFlag,
      })
      /** ------------計画対象期間を設定------------ */
      // 計画期間管理フラグ設定
      plannningPeriodManageFlg.value = resData.data.kikanKanriFlg

      // 計画対象期間管理フラグが「1:管理する」場合、計画対象期間情報を設定
      if (
        plannningPeriodManageFlg.value === TeX0014Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
        resData?.data?.planPeriodInfo?.sc1Id &&
        resData.data.planPeriodInfo.periodCnt !== '0'
      ) {
        localOneway.periodSelectOneway.sc1Id = resData.data.planPeriodInfo.sc1Id
        localOneway.orX0007Oneway.planTargetPeriodData = {
          planTargetPeriodId: Number(resData.data.planPeriodInfo.sc1Id),
          planTargetPeriod:
            resData.data.planPeriodInfo.startYmd + '～' + resData.data.planPeriodInfo.endYmd,
          currentIndex: Number(resData.data.planPeriodInfo.periodNo),
          totalCount: Number(resData.data.planPeriodInfo.periodCnt),
        }
        isHistoryShow.value = true
      } else if (
        plannningPeriodManageFlg.value === TeX0014Const.DEFAULT.PLANNING_PERIOD_NO_MANAGE &&
        resData?.data?.planPeriodInfo?.sc1Id &&
        resData.data.planPeriodInfo.periodCnt !== '0'
      ) {
        localOneway.periodSelectOneway.sc1Id = resData.data.planPeriodInfo.sc1Id ?? ''
        localOneway.orX0007Oneway.planTargetPeriodData = {
          planTargetPeriodId: 0,
          planTargetPeriod: '',
          currentIndex: 0,
          totalCount: 0,
        }
        isHistoryShow.value = true
      } else {
        localOneway.periodSelectOneway.sc1Id = resData.data.planPeriodInfo.sc1Id ?? ''
        localOneway.orX0007Oneway.planTargetPeriodData = {
          planTargetPeriodId: Number(resData.data.planPeriodInfo.sc1Id ?? 0),
          planTargetPeriod: '',
          currentIndex: 0,
          totalCount: 0,
        }
        isHistoryShow.value = false
      }
      /** ------------履歴情報を設定------------ */
      // 表示中計画対象期間の履歴情報取得
      if (resData?.data?.historyInfo.teikyouId) {
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: resData.data.historyInfo.teikyouId,
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: Number(resData.data.historyInfo.krirekiNo ?? 1),
            /** 履歴総件数 */
            totalCount: Number(resData.data.historyInfo.krirekiCnt ?? 1),
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: TeX0014Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }
        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: parseInt(resData.data.historyInfo.chkShokuId ?? '0'),
          staffName: resData.data.historyInfo.chkShokuName ?? '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value = resData.data.historyInfo.createYmd ?? ''
        createDateOld.value = local.createDate.value
      } else {
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: resData.data.historyInfo.teikyouId ?? '',
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: 1,
            /** 履歴総件数 */
            totalCount: 1,
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: TeX0014Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }
        // 履歴更新回数
        historyModifiedCnt.value = ''

        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: 0,
          staffName: '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value =
          systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
        createDateOld.value = local.createDate.value
      }
    } else {
      localOneway.orX0007Oneway.planTargetPeriodData = {
        planTargetPeriodId: 0,
        planTargetPeriod: '',
        currentIndex: 0,
        totalCount: 0,
      }
      isHistoryShow.value = false
    }
  } catch (e: unknown) {
    console.log(e)
  }
}

/**
 *  履歴情報を取得
 *
 * @param teikyouId - 履歴ID
 *
 * @param kikanFlag - 履歴処理区分
 */
async function getHistoryInfo(teikyouId: string, kikanFlag: string) {
  try {
    // バックエンドAPIから初期情報取得
    const inputData: HospitalizationTimeInfoOfferHistorySelectInEntity = {
      /** 事業所ID */
      svJigyoId: jigyoId.value,
      /** 利用者ID */
      userid: userId.value,
      /** 提供ID  */
      cc1Id: teikyouId ? teikyouId : '',
      /** 計画期間ID */
      sc1Id: localOneway.periodSelectOneway.sc1Id ?? '',
      /** 履歴変更区分 */
      kikanFlag: kikanFlag ?? '0',
    }
    const resData: HospitalizationTimeInfoOfferHistorySelectOutEntity =
      await ScreenRepository.select('hospitalizationTimeInfoOfferHistorySelect', inputData)

    // 画面情報を設定
    if (resData?.data) {
      if (resData?.data?.historyInfo?.teikyouId) {
        // 履歴IDをバックアップ
        teikyouIdOld.value = resData?.data?.historyInfo.teikyouId

        // 表示中計画対象期間の履歴情報取得
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: resData.data.historyInfo.teikyouId,
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: Number(resData.data.historyInfo.krirekiNo ?? 1),
            /** 履歴総件数 */
            totalCount: Number(resData.data.historyInfo.krirekiCnt ?? 1),
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: TeX0014Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }

        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: parseInt(resData.data.historyInfo.chkShokuId ?? '0'),
          staffName: resData.data.historyInfo.chkShokuName ?? '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value = resData.data.historyInfo.createYmd ?? ''
        createDateOld.value = local.createDate.value
      } else {
        localOneway.orX0008Oneway = {
          createData: {
            /** 提供ID */
            createId: '',
            /** 作成日 */
            createDate: resData.data.historyInfo.createYmd ?? '',
            /** 職員ID */
            staffId: resData.data.historyInfo.chkShokuId ?? '',
            /** 作成者名（職員名） */
            staffName: resData.data.historyInfo.chkShokuName ?? '',
            /** 履歴番号 */
            currentIndex: Number(resData.data.historyInfo.krirekiNo ?? 1),
            /** 履歴総件数 */
            totalCount: Number(resData.data.historyInfo.krirekiCnt ?? 1),
          },
          /**
           *事業所ID
           */
          officeId: resData.data.historyInfo.shienJigyoId ?? '',
          /**
           *画面ID
           */
          screenID: TeX0014Const.DEFAULT.GUI,
          /**
           *利用者ID
           */
          useId: userId.value,
          /**
           *計画対象期間ID
           */
          sc1Id: resData.data.historyInfo.sc1Id ?? '',
          /**
           *計画書１ID
           */
          plan1Id: '',
          /**
           * モード
           */
          mode: '',
        }

        // 履歴更新回数
        historyModifiedCnt.value = ''

        // 作成者を設定
        localOneway.orX0009Oneway.createData = {
          staffId: 0,
          staffName: '',
        } as OrX0009OnewayType['createData']
        // 作成日を設定
        local.createDate.value =
          systemCommonsStore.getSystemDate ?? new Date().toLocaleDateString().replace(/-/g, '/')
        createDateOld.value = local.createDate.value
      }
    }
  } catch (e: unknown) {
    console.log(e)
  }
}

/**
 *  データ変更チェック
 */
async function checkChange(): Promise<number> {
  let result = 0
  // 画面変更チェック
  if (isEdit.value) {
    if (!isPermissionViewAuth.value) {
      // 画面入力変更あり、かつ保存権限がない場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog2(t('message.w-com-10006'))
      switch (dialogResult) {
        case 'yes': {
          // 編集を破棄して処理を続く
          result = 1
        }
      }
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case 'yes': {
          // 編集を保存して処理を続く
          result = 2
          break
        }
        case 'no':
          // 編集を破棄して処理を続く
          result = 1
          break
        case 'cancel':
          // キャンセル選択時は何もしない
          result = 3
          break
      }
    }
  } else {
    // 変更なしの場合は何もしないまま処理を続く
    result = 0
  }
  return result
}

/**
 * 利用者変更処理
 *
 * @param newUserId - 新しい利用者ID
 */
async function userChange(newUserId: string) {
  isLoading.value = true

  // 初期化の場合、チェックなしで処理を続行
  if (isInitCompleted === false) {
    userId.value = newUserId
    // 初期情報を取得
    await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
    // 画面情報再取得
    void reload()

    isInitCompleted = true
    isLoading.value = false
    return
  }

  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()
      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存して処理を続行
      userId.value = newUserId
      systemCommonsStore.setUserSelectSelfId(newUserId, props.uniqueCpId)
      // 初期情報を取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 保存
      await setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }

  isLoading.value = false
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (jigyoId.value === '') {
    jigyoId.value = newJigyoId
    return
  }

  // 事業所変更がない場合、スキップ
  if (newJigyoId === jigyoId.value) {
    return
  }

  // まず変更前の事業所を保持
  void setJigyo(jigyoId.value)

  // 事業所変更処理
  void jigyoChange(newJigyoId)
}

/**
 * 事業所を選択
 *
 * @param jigyoId - 事業所ID
 */
async function setJigyo(jigyoId: string) {
  await nextTick()

  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    },
  })
}

/**
 * 事業所変更処理
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function jigyoChange(newJigyoId: string) {
  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      jigyoId.value = newJigyoId
      // 変更後の事業所に設定
      await setJigyo(newJigyoId)
      // 画面情報再取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      jigyoId.value = newJigyoId
      // 変更後の事業所に設定
      await setJigyo(newJigyoId)

      // 画面情報再取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // 画面情報再取得
      void reload()

      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存して処理を続行
      jigyoId.value = newJigyoId
      // 変更後の事業所に設定
      await setJigyo(newJigyoId)

      // 画面情報再取得
      await getPlanningPeriodInfo(localOneway.periodSelectOneway.sc1Id ?? '', '0')
      // はい選択時は入力内容を保存する
      await setEvent({ saveEventFlg: true })
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog1(paramDialogText: string): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog2(paramDialogText: string) {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise(() => {
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      () => {
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_2.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
async function openConfirmDialog3(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_3.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_3.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * 複写ボタンクリック
 *
 */
function copyBtnClick() {
  // 保存ボタンが押下された場合、保存処理を実行する
  setOr11871Event({ copyEventFlg: true })
}

/**
 * 新規処理
 *
 */
async function createNew() {
  if (plannningPeriodManageFlg.value === TeX0014Const.DEFAULT.PLANNING_PERIOD_MANAGE) {
    if (localOneway.periodSelectOneway.sc1Id) {
      if (localOneway.orX0008Oneway.createData.createId) {
        // 画面変更チェック
        if (isEdit.value) {
          // 画面入力変更がある場合
          // 確認ダイアログ表示
          const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
          switch (dialogResult) {
            case 'yes': {
              // はい選択時は入力内容を保存する
              await setEvent({ saveEventFlg: true })

              // 履歴件数 + 1
              void historyNew()
              break
            }
            case 'no': {
              // いいえ選択時は編集内容を破棄するので何もしない
              // 履歴件数 + 1
              void historyNew()
              break
            }
            case 'cancel':
              // キャンセル
              break
          }

          return
        } else {
          // 履歴件数 + 1
          void historyNew()
        }
      } else {
        // 計画期間情報がある、当該画面データが保存されない場合
        // 確認ダイアログ表示
        await openConfirmDialog2(t('message.i-cmn-11265', [t('label.hospitalization-info')]))

        return
      }
    } else {
      // 計画期間情報がない場合
      // 確認ダイアログ表示
      await openConfirmDialog2(t('message.i-cmn-11300'))

      return
    }
  }
}

/**
 * 印刷処理
 *
 */
function print() {
  // Or56886のダイアログ開閉状態を更新する
  Or56886Logic.state.set({
    uniqueCpId: or56886.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        prtNo: '1',
        houjinId: '1',
        shokuId: '1',
        svJigyoId: '1',
        shisetuId: '1',
        tantoId: '1',
        tantoFlg: '0',
        sectionName: 'インターライ方式ケアアセスメント表',
        userId: '41',
        historyId: '3',
        svJigyoKnj: '1',
        processYmd: '2025/07/02',
        focusSettingInitial: ['や', 'ゆ', 'よ'],
        selectedUserCounter: '2',
      } as Or56886Param,
    },
  })
}

/**
 * 複写処理
 *
 */
function _copy() {
  // 複写画面パラメータを設定
  or17391.value.or00249UniqueCpId = or00249.value.uniqueCpId
  or17391.value.or00094UniqueCpId = or00094.value.uniqueCpId
  localOneway.or17391Oneway.periodManagementFlg = plannningPeriodManageFlg.value
  copyComponentKey.value = Date.now().toString()
  const tabItems = [] as {
    /** id */
    id: string
    /** id */
    name: string
  }[]
  localOneway.mo00043OnewayType.tabItems.forEach((item) => {
    tabItems.push({
      id: item.id,
      name: item.title,
    })
  })
  localOneway.or17391Oneway = {
    /** 期間管理フラグ */
    periodManagementFlg: plannningPeriodManageFlg.value,
    /** タブリスト */
    tabItems: tabItems,
    /** 選択中タブ */
    activeTabId: local.mo00043.id,
    /** 事業所IDリスト*/
    svJigyoIdList: Array.from(systemCommonsStore.getSvJigyoIdList) ?? [],
    /** 種別ID */
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    /** 利用者ID */
    userId: userId.value,
    /** 基準日 */
    createYmd: systemCommonsStore.getSystemDate ?? '',
    /** 施設ID */
    shisetuId: systemCommonsStore.getShisetuId ?? '',
  }
  // 複写画面を開く
  Or17391Logic.state.set({
    uniqueCpId: or17391.value.uniqueCpId,
    state: { isOpen: true },
  })
  setOr11871Event({ copyEventFlg: false })
}

/**
 * 履歴を更新
 *
 */
async function historyNew() {
  // 履歴件数 + 1
  let rirekiCnt: number = localOneway.orX0008Oneway.createData?.totalCount
    ? (localOneway.orX0008Oneway.createData?.totalCount ?? 0)
    : 0
  rirekiCnt = rirekiCnt + 1
  localOneway.orX0008Oneway.createData = {
    /** 提供ID */
    createId: '',
    /** 作成日 */
    createDate: '',
    /** 職員ID */
    staffId: '',
    /** 作成者名（職員名） */
    staffName: '',
    /** 履歴番号 */
    currentIndex: rirekiCnt,
    /** 履歴総件数 */
    totalCount: rirekiCnt,
  }
  await setEvent({ createEventFlg: true })
}

/**
 * 削除処理
 *
 */
function _delete() {
  // メッセージ 11326
  // 現在表示している[{0}］の{1}データを削除します。よろしいですか？\r\n現在表示している画面のみ削除する。\r\n※{2}画面を削除します。\r\n表示している画面を履歴ごと削除する。\r\n※{3}までの全ての項目を削除します。
  // 削除確認ダイアログを初期化(i.cmn.11326)
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11326', [
        local.createDate.value,
        t('label.hospitalization-info'),
      ]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_3.value.uniqueCpId)?.isOpen,
      async () => {
        const event = Or21814Logic.event.get(or21814_3.value.uniqueCpId)

        let result = TeX0014Const.DEFAULT.DIALOG_RESULT_CANCEL
        if (event?.firstBtnClickFlg) {
          result = TeX0014Const.DEFAULT.DIALOG_RESULT_YES
          // 削除実施後、作成者選択アイコンボタンを非活性
          localOneway.orX0009Oneway.isDisabled = true
          // 削除実施後、作成日を非活性
          localOneway.createDateOneway.disabled = true
          //共通処理の复写権限チェックを行う
          local.copyDisabled = true
          Or11871Logic.state.set({
            uniqueCpId: or11871.value.uniqueCpId,
            state: {
              disabledCreateBtn: true, //共通処理の新规権限チェックを行う
              disabledPrintBtn: true, //共通処理の印刷権限チェックを行う
              disabledOptionMenuDelete: true, //共通処理の削除権限チェックを行う
            },
          })
          await setEvent({ deleteEventFlg: true })
        }
        if (event?.secondBtnClickFlg) {
          result = TeX0014Const.DEFAULT.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_3.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 作成日変更処理
 *
 * @param mo00020 - 作成日
 */
async function createDateChange(mo00020: Mo00020Type) {
  if (mo00020.value === createDateOld.value) {
    // 作成日が変更されていない場合は何もしない
    return
  }

  if (createDateOld.value === '' || mo00020.value === '') {
    // 履歴に作成日がない、または入力されない場合は何もしない
    return
  }

  // H21/4改訂版（改訂フラグ＝4）AND  作成日が2018年3月31日以前（平成30年区分=0）AND 入力した作成日>= '2018/04/01'の場合
  if (
    parseInt(createDateOld.value.replaceAll('/', '')) < 20180331 &&
    parseInt(mo00020.value.replaceAll('/', '')) >= 20180401
  ) {
    // 確認ダイアログ表示
    const dialogResult = await openConfirmDialog3(t('message.i-cmn-11324'))
    switch (dialogResult) {
      case 'yes': {
        createDateOld.value = mo00020.value
        // はい選択時は入力内容を保存する
        if (isEdit.value) {
          await setEvent({ saveEventFlg: true })
        }

        // 作成日変更処理
        await setEvent({ isCreateDateChanged: true })
        break
      }
      case 'no': {
        // いいえ選択時入力前の値に戻る
        mo00020.value = createDateOld.value
        break
      }
    }
  }

  // H21/４改訂版（改訂フラグ＝4）AND  作成日が2018年4月1日以降（平成30年区分=1）AND 入力した作成日< '2018/04/01'の場合
  if (
    parseInt(createDateOld.value.replaceAll('/', '')) >= 20180401 &&
    parseInt(mo00020.value.replaceAll('/', '')) < 20180401
  ) {
    // 確認ダイアログ表示
    const dialogResult = await openConfirmDialog3(t('message.i-cmn-11324'))
    switch (dialogResult) {
      case 'yes': {
        createDateOld.value = mo00020.value
        // はい選択時は入力内容を保存する
        if (isEdit.value) {
          await setEvent({ saveEventFlg: true })
        }

        // 作成日変更処理
        await setEvent({ isCreateDateChanged: true })
        break
      }
      case 'no': {
        // いいえ選択時入力前の値に戻る
        mo00020.value = createDateOld.value
        break
      }
    }
  }
}

async function handleSave() {
  await setEvent({ saveEventFlg: true })
}

/**
 * タブ変更処理
 *
 * @param mo00043 - タブ情報
 */
function tabChange(mo00043: Mo00043Type) {
  // 画面変更チェック
  local.mo00043.id = mo00043.id
}

/**
 * 削除ダイアログ選択変更処理
 *
 * @param orX0001Value - 戻り値
 */
function deleteDialogChange(orX0001Value: OrX0001Type) {
  if ('0' !== orX0001Value.deleteSyubetsu) {
    // 確定ボタン押下の場合
    deleteKbn.value = orX0001Value.deleteSyubetsu
    // 削除実施後、作成者選択アイコンボタンを非活性
    localOneway.orX0009Oneway.isDisabled = true
    // 削除実施後、作成日を非活性
    localOneway.createDateOneway.disabled = true

    // アセスメント(インターライ)画面履歴の最新情報を取得する
    switch (local.mo00043.id) {
      case OrX0188Const.DEFAULT.TAB_ID:
        break
      case OrX0195Const.DEFAULT.TAB_ID:
        break
    }
  } else {
    // キャンセル
    // 何もしない
  }
}

watch(
  () => isEdit.value,
  () => {
    console.log(isEdit.value, '11111111111111111111')
  }
)

/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    const { currentIndex, totalCount } = localOneway.orX0007Oneway.planTargetPeriodData
    const sc1Id =
      newValue.planTargetPeriodId !== '0'
        ? newValue.planTargetPeriodId
        : (localOneway.periodSelectOneway.sc1Id ?? '')
    if (newValue?.PlanTargetPeriodUpdateFlg === '0') {
      await getPlanningPeriodInfo(sc1Id, TeX0014Const.DEFAULT.PERIOD_KBN_OPEN)
      // 画面情報再取得
      void reload()
    } else if (newValue?.PlanTargetPeriodUpdateFlg === '1') {
      if (currentIndex === 1) {
        await openConfirmDialog2(t('message.i-cmn-11262'))
        // 処理終了
        return
      }
      await getPlanningPeriodInfo(sc1Id, TeX0014Const.DEFAULT.PERIOD_KBN_PREV)
      // 画面情報再取得
      void reload()
    } else if (newValue?.PlanTargetPeriodUpdateFlg === '2') {
      if (currentIndex === totalCount) {
        await openConfirmDialog2(t('message.i-cmn-11263'))
        // 処理終了
        return
      }
      await getPlanningPeriodInfo(sc1Id, TeX0014Const.DEFAULT.PERIOD_KBN_NEXT)
      // 画面情報再取得
      void reload()
    }
  },
  { deep: true }
)

watch(
  () => OrX0008Logic.data.get(orX0008.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    const { currentIndex, totalCount } = localOneway.orX0008Oneway.createData
    const { createId, createUpateFlg } = newValue
    const teikyouId =
      createId !== '' ? createId : (localOneway.orX0008Oneway.createData.createId ?? '')
    if (createUpateFlg === '0') {
      // 画面入力変更あり、編集を保存して処理を続行
      // 履歴情報を再取得
      await getHistoryInfo(teikyouId, TeX0014Const.DEFAULT.HISTORY_KBN_OPEN)
      // 画面情報再取得
      void reload()
    } else if (createUpateFlg === '1') {
      if (currentIndex === 1) {
        // 処理終了
        return
      }
      await getHistoryInfo(teikyouId, TeX0014Const.DEFAULT.HISTORY_KBN_PREV)
      // 画面情報再取得
      void reload()
    } else if (createUpateFlg === '2') {
      if (currentIndex === totalCount) {
        // 処理終了
        return
      }
      await getHistoryInfo(teikyouId, TeX0014Const.DEFAULT.HISTORY_KBN_NEXT)
      // 画面情報再取得
      void reload()
    }
  },
  { deep: true }
)
/**
 * 作成者変更監視
 */
watch(
  () => OrX0009Logic.data.get(orX0009.value.uniqueCpId),
  async (newValue) => {
    if (newValue) {
      // ・親情報を設定 ※親情報が、すべてのタブ間で共有する
      // 親情報.作成者ID = 画面.作成者ID
      setCommonInfo({ createUserId: newValue.staffId })
      await getInitDataInfo()
    }
  },
  { deep: true }
)
/**
 * 複写
 *
 * @param teikyouId -teikyouId
 */
function aceeptDataInfo(teikyouId: string) {
  /** 提供ID */
  localOneway.orX0008Oneway.createData.createId = teikyouId
  /** 基準日 */
  void getInitDataInfo()
}
</script>

<template>
  <!-- Or11871：有機体：画面メニューエリア -->
  <!-- ローディング -->
  <v-overlay
    :model-value="isLoading"
    :persistent="false"
    class="align-center justify-center"
    ><v-progress-circular
      indeterminate
      color="primary"
    ></v-progress-circular
  ></v-overlay>
  <c-v-sheet class="view d-flex flex-column h-100">
    <!-- 操作ボタンエリア -->
    <div class="action-sticky">
      <g-base-or11871 v-bind="or11871">
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            prepend-icon="file_copy"
            :disabled="local.copyDisabled"
            @click="copyBtnClick"
          />
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="t('tooltip.care-plan2-copy-btn')"
          ></c-v-tooltip>
        </template>
      </g-base-or11871>
    </div>
    <!-- アクションエリア -->
    <div class="action-content">
      <c-v-row
        no-gutters
        class="main-Content d-flex flex-0-1 overflow-y-auto h-100"
      >
        <!-- ナビゲーションエリア -->
        <c-v-col
          cols="auto"
          class="hidden-scroll h-100 pa-3"
        >
          <!-- 利用者選択一覧 -->
          <g-base-or00248 v-bind="or00248" />
        </c-v-col>
        <!-- コンテンツエリア -->
        <c-v-col class="main-right d-flex flex-column overflow-hidden h-100">
          <!-- 上段 -->
          <c-v-row
            no-gutters
            class="d-flex align-end"
          >
            <c-v-col
              cols="auto ml-6 mr-6 office-select"
              style="width: 220px"
            >
              <!-- 事業所選択画面 -->
              <g-base-or-41179 v-bind="or41179" />
            </c-v-col>
            <c-v-col
              v-show="plannningPeriodManageFlg === TeX0014Const.DEFAULT.PLANNING_PERIOD_MANAGE"
              cols="auto mr-6 top-bg"
            >
              <!-- 計画対象期間 -->
              <g-custom-orX0007
                v-bind="orX0007"
                v-model="local.orX0007"
                :is-edit="isEdit"
                :oneway-model-value="localOneway.orX0007Oneway"
                :parent-method="handleSave"
              />
            </c-v-col>
            <c-v-col
              v-show="isHistoryShow"
              cols="auto mr-6"
              style="width: 200px"
            >
              <!-- 作成日 -->
              <base-mo00020
                v-model="local.createDate"
                :oneway-model-value="localOneway.createDateOneway"
                @update:model-value="createDateChange"
              />
            </c-v-col>
            <c-v-col
              v-show="isHistoryShow"
              cols="auto mt-2 top-bg mr-6"
            >
              <!-- 作成者 -->
              <g-custom-orX0009
                v-bind="orX0009"
                :oneway-model-value="localOneway.orX0009Oneway"
              />
            </c-v-col>
            <c-v-col
              v-show="isHistoryShow"
              cols="auto top-bg"
            >
              <!-- 履歴 -->
              <g-custom-orX0008
                v-bind="orX0008"
                v-model="local.orX0008"
                :is-edit="isEdit"
                :oneway-model-value="localOneway.orX0008Oneway"
                :unique-cp-id="orX0008.uniqueCpId"
                :parent-method="handleSave"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutters
            class="mt-6"
          >
            <c-v-col>
              <base-mo00043
                :model-value="local.mo00043"
                :oneway-model-value="localOneway.mo00043OnewayType"
                @update:model-value="tabChange"
              >
              </base-mo00043>
            </c-v-col>
          </c-v-row>
          <!-- 中段 -->
          <c-v-row
            no-gutters
            class="middleContent flex-1-1 h-100"
          >
            <c-v-window
              id="tabWindow"
              v-model="local.mo00043.id"
              class="h-100"
            >
              <!-- タブ1 -->
              <c-v-window-item :value="OrX0188Const.DEFAULT.TAB_ID">
                <div v-if="!localOneway.orX0009Oneway.isDisabled">
                  <g-custom-or-X0188
                    v-bind="orX0188"
                    :oneway-model-value="localOneway.orX0188Oneway"
                    :parent-unique-cp-id="props.uniqueCpId"
                  />
                </div>
              </c-v-window-item>
              <!-- タブ2 -->
              <c-v-window-item :value="OrX0195Const.DEFAULT.TAB_ID">
                <div v-if="!localOneway.orX0009Oneway.isDisabled">
                  <g-custom-or-X0195
                    v-bind="OrX0195"
                    :oneway-model-value="localOneway.OrX0195Oneway"
                    :parent-unique-cp-id="props.uniqueCpId"
                  />
                </div>
              </c-v-window-item>
            </c-v-window>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </div>
    <g-custom-or-17391
      v-if="showDialogOr17391"
      v-bind="or17391"
      :key="copyComponentKey"
      :oneway-model-value="localOneway.or17391Oneway"
      :parent-unique-cp-id="props.uniqueCpId"
      @aceept-data="aceeptDataInfo"
    />
    <!-- アセスメントマスタ画面 -->
    <g-custom-or-27562
      v-if="showDialogOr27562"
      v-bind="or27562"
      :oneway-model-value="localOneway.or27562Oneway"
    />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_1" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_2" />
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814_3" />
    <!--  印刷画面 -->
    <g-custom-or-56886
      v-if="showDialogOr56886"
      v-bind="or56886"
    />
    <!--  削除確認画面 -->
    <g-custom-or-x-0001
      v-bind="orX0001"
      v-model="local.orX0001Type"
      :oneway-model-value="localOneway.orX0001Oneway"
      @update:model-value="deleteDialogChange"
    ></g-custom-or-x-0001>
  </c-v-sheet>
</template>

<style scoped lang="scss">
.main-Content {
  .main-left {
    max-width: 20%;
  }

  .main-right {
    .middleContent {
      min-height: 0;

      .v-window {
        width: 100%;
      }
    }
  }
}
.v-btn {
  color: rgb(118, 118, 118) !important;
  background-color: transparent !important;
  border: 1px solid rgb(118, 118, 118) !important;
}
.view {
  background-color: transparent;
  height: max-content !important;
}
.createDateOuterClass {
  width: auto !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }

  :deep(.v-input__details) {
    display: none;
  }
  :deep(.v-input__control) {
    background-color: #fff;
  }
}
:deep(.rounded.item-label) {
  min-width: 180px;
  padding-left: 8px !important;
}
.action-button {
  background-color: #fff !important;
  width: 79px !important;
  padding: 0px !important;
  min-width: auto !important;
  &:first-child {
    width: 54px !important;
  }
}
.action-sticky {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
  button {
    height: 32px !important;
  }
}
:deep(.action-sticky .v-btn) {
  height: 32px !important;
  min-height: 32px !important;
  &:nth-child(4) {
    width: 76px !important;
  }
}
.action-content {
  background: rgb(var(--v-theme-background));
  height: max-content;
  z-index: 888;
}
.top-bg {
  :deep(.v-sheet) {
    background-color: rgb(var(--v-theme-background)) !important;
  }
}

// 利用者一覧選択領域領域
:deep(.user-select > .v-card) {
  padding: 0px 0 0px 24px !important; // パディング設定
  width: max-content !important; // 幅を自動調整
  max-height: 1110px !important;
  margin-top: 24px !important; // 上部マージン
  // パンディング設定
  .v-card-actions {
    width: max-content !important; // 幅を自動調整
    padding: 8px !important; // パディング設定
  }
  // 選択領域の幅設定
  .user-info-section-container-style {
    width: 260px; // 幅を260pxに設定
  }
}
:deep(.v-list) {
  max-height: 1110px !important;
}
</style>
