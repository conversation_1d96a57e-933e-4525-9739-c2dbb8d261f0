import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 帳票名称エンティティ
 */
export interface PrtEntity {
  /**
   * インデックス
   */
  index: string
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * システム略称
   */
  sysRyaku: string
  /**
   * 帳票リスト名
   */
  listName: string
  /**
   * 帳票名
   */
  defPrtTitle: string
  /**
   * 帳票タイトル
   */
  prtTitle: string
  /**
   * セクション番号
   */
  sectionNo: string
  /**
   * 帳票番号
   */
  prtNo: string
  /**
   * プロファイル
   */
  profile: string
  /**
   * 日付表示有無
   */
  prnDate: string
  /**
   * 職員表示有無
   */
  prnshoku: string
  /**
   * オブジェクト名
   */
  dwobject: string
  /**
   * 用紙向き
   */
  prtOrient: string
  /**
   * 用紙サイズ
   */
  prtSize: string
  /**
   * 帳票リストタイトル
   */
  listTitle: string
  /**
   * 上余白
   */
  mtop: string
  /**
   * 下余白
   */
  mbottom: string
  /**
   * 左余白
   */
  mleft: string
  /**
   * 右余白
   */
  mright: string
  /**
   * ルーラ表示有無
   */
  ruler: string
  /**
   * シリアルフラグ
   */
  serialFlg: string
  /**
   * モードフラグ
   */
  modFlg: string
  /**
   * セクションフラグ
   */
  secFlg: string
  /**
   * 高さ
   */
  serialHeight: string
  /**
   * 印刷行数
   */
  serialPagelen: string
  /**
   * 表示内拡大率
   */
  zoomRate: string
  /**
   * パラメータ01
   */
  param01: string
  /**
   * パラメータ02
   */
  param02: string
  /**
   * パラメータ03
   */
  param03: string
  /**
   * パラメータ04
   */
  param04: string
  /**
   * パラメータ05
   */
  param05: string
  /**
   * パラメータ06
   */
  param06: string
  /**
   * パラメータ07
   */
  param07: string
  /**
   * パラメータ08
   */
  param08: string
  /**
   * パラメータ09
   */
  param09: string
  /**
   * パラメータ10
   */
  param10: string
  /**
   * パラメータ11
   */
  param11: string
  /**
   * パラメータ12
   */
  param12: string
  /**
   * パラメータ13
   */
  param13: string
  /**
   * パラメータ14
   */
  param14: string
  /**
   * パラメータ15
   */
  param15: string
  /**
   * パラメータ16
   */
  param16: string
  /**
   * パラメータ17
   */
  param17: string
  /**
   * パラメータ18
   */
  param18: string
  /**
   * パラメータ19
   */
  param19: string
  /**
   * パラメータ20
   */
  param20: string
  /**
   * パラメータ21
   */
  param21: string
  /**
   * パラメータ22
   */
  param22: string
  /**
   * パラメータ23
   */
  param23: string
  /**
   * パラメータ24
   */
  param24: string
  /**
   * パラメータ25
   */
  param25: string
  /**
   * パラメータ26
   */
  param26: string
  /**
   * パラメータ27
   */
  param27: string
  /**
   * パラメータ28
   */
  param28: string
  /**
   * パラメータ29
   */
  param29: string
  /**
   * パラメータ30
   */
  param30: string
  /**
   * パラメータ31
   */
  param31: string
  /**
   * パラメータ32
   */
  param32: string
  /**
   * パラメータ33
   */
  param33: string
  /**
   * パラメータ34
   */
  param34: string
  /**
   * パラメータ35
   */
  param35: string
  /**
   * パラメータ36
   */
  param36: string
  /**
   * パラメータ37
   */
  param37: string
  /**
   * パラメータ38
   */
  param38: string
  /**
   * パラメータ39
   */
  param39: string
  /**
   * パラメータ40
   */
  param40: string
  /**
   * パラメータ41
   */
  param41: string
  /**
   * パラメータ42
   */
  param42: string
  /**
   * パラメータ43
   */
  param43: string
  /**
   * パラメータ44
   */
  param44: string
  /**
   * パラメータ45
   */
  param45: string
  /**
   * パラメータ46
   */
  param46: string
  /**
   * パラメータ47
   */
  param47: string
  /**
   * パラメータ48
   */
  param48: string
  /**
   * パラメータ49
   */
  param49: string
  /**
   * パラメータ50
   */
  param50: string
  /**
   * 更新回数
   */
  modifiedCnt: string
}

/**
 * 印刷設定情報エンティティ
 */
export interface SysIniInfoEntity {
  /**
   * 氏名伏字設定フラグ
   */
  amikakeFlg: string
  /**
   * 氏名伏字更新回数
   */
  amikakeModifiedCnt: string
  /**
   * 文章管理設定フラグ
   */
  iso9001Flg: string
  /**
   * 文章管理更新回数
   */
  iso9001ModifiedCnt: string
  /**
   * 個人情報設定フラグ
   */
  kojinhogoFlg: string
  /**
   * 個人情報更新回数
   */
  kojinhogoModifiedCnt: string
}

/**
 * 期間履歴情報エンティティ
 */
export interface PeriodHistoryEntity {
  /** 期間ID */
  sc1Id: string
  /** 開始日 */
  startYmd: string
  /** 終了日 */
  endYmd: string
  /** 選択 */
  sel: string
  /** アセスメントID */
  gdlId: string
  /** 利用者ID */
  userid: string
  /** アセセスメント実施日 */
  asJisshiDateYmd: string
  /** 記載者ID */
  shokuId: string
  /** 改定フラグ */
  ninteiFormF: string
  /** 作成者 */
  shokuinKnj: string
}

/**
 * 利用者エンティティ
 */
export interface UserEntity {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 利用者名
   */
  userName: string
}

/**
 * 印刷対象履歴エンティティ
 */
export interface PrintSubjectHistoryEntity {
  /**
   * 利用者ID
   */
  userid: string
  /**
   * 利用者名
   */
  userName: string
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * アセスメントID
   */
  gdlId: string
  /**
   * アセスメント実施日
   */
  asJisshiDateYmd: string
  /**
   * 記載者ID
   */
  shokuId: string
  /**
   * 改定フラグ
   */
  ninteiFormF: string
  /**
   * 結果
   */
  result: string
}

/**
 * 印刷設定初期情報取得入力エンティティ
 */
export interface IAssessmentHomePrintSettingsInitialUpdateInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId?: string
  /** システム略称 */
  sysRyaku: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** メニュー２名称 */
  menu2Knj: string
  /** メニュー３名称 */
  menu3Knj: string
  /** セクション名 */
  sectionName: string
  /** システムコード */
  gsysCd: string
  /** 職員ID */
  shokuId: string
  /** インデックス */
  index: string
  /** 個人情報使用フラグ */
  kojinhogoUsedFlg: string
  /** 個人情報番号 */
  sectionAddNo: string
  /** 改訂範囲 */
  revisionRange: string
  /** 履歴選択フラグ */
  historySelectFlag: string
}

/**
 * 印刷設定初期情報取得出力エンティティ
 */
export interface IAssessmentHomePrintSettingsInitialUpdateOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 印刷設定情報リスト
     */
    prtList: PrtEntity[]
    /**
     * システムINI情報
     */
    sysIniInfo: SysIniInfoEntity
    /**
     * アセスメント履歴リスト
     */
    periodHistoryList: PeriodHistoryEntity[]
    /**
     * 期間管理フラグ
     */
    kikanFlg: string
  }
}

/**
 * 印刷設定対象一覧情報取得入力エンティティ
 */
export interface IAssessmentHomePrintSettingsHistorySelectInEntity extends InWebEntity {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * システムコード
   */
  gsysCd: string
  /**
   * システム略称
   */
  sysRyaku: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * 基準日
   */
  kijunbiYmd: string
  /**
   * インデックス
   */
  index: string
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: string
  /**
   * 個人情報番号
   */
  sectionAddNo: string
  /**
   * 利用者リスト
   */
  userList: UserEntity[]
}

/**
 * 印刷設定対象一覧情報取得出力エンティティ
 */
export interface IAssessmentHomePrintSettingsHistorySelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 印刷設定情報リスト
     */
    prtList: PrtEntity[]
    /**
     * システムINI情報
     */
    sysIniInfo: SysIniInfoEntity
    /**
     * 印刷対象履歴リスト
     */
    printSubjectHistoryList: PrintSubjectHistoryEntity[]
  }
}

/**
 * 印刷設定履歴リスト取得入力エンティティ
 */
export interface IAssessmentHomePrintSettinguserSwitchingSelectInEntity extends InWebEntity {
  /** 利用者ID */
  userId: string
  /** システム略称 */
  sysRyaku: string
  /** システムコード */
  gsysCd: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 期間管理フラグ */
  kikanFlg: string
  /** 職員ID */
  shokuId: string
  /** 事業者ID */
  svJigyoId: string
  /** インデックス */
  index: string
  /** 個人情報使用フラグ */
  kojinhogoUsedFlg: string
  /** 個人情報番号 */
  sectionAddNo: string
  /** 改訂範囲 */
  revisionRange: string
  /** 履歴選択フラグ */
  historySelectFlag: string
}

/**
 * 印刷設定履歴リスト取得出力エンティティ
 */
export interface IAssessmentHomePrintSettinguserSwitchingSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * アセスメント履歴リスト
     */
    periodHistoryList: PeriodHistoryEntity[]
    /**
     * 調査票改訂フラグ
     */
    surveyRevisionFlag: string
  }
}
