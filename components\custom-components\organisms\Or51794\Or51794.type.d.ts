/**
 * Or51794:有機体:(予定マスタ)アセスメント（包括）マスタリスト
 * GUI00831_予定マスタ
 *
 * @description
 * DataTableのデータ
 *
 * <AUTHOR> PHAM HO HAI DANG
 */
/**
 * Tableのデータ
 */
export interface TableData {
  /**
   * 入力ID
   */
  cf1Id: string
  /**
   * 入力区分
   */
  cf1Kbn: string
  /**
   * 区分番号
   */
  kbnCd: {
    /**
     * 値
     */
    value: string
  }
  /**
   * 内容
   */
  textKnj: {
    /**
     * 値
     */
    value: string
  }
  /**
   * 更新区分
   */
  changeF: string
  /**
   * 更新区分
   */
  updateKbn: string
  /**
   * テーブルINDEX(行固有ID)
   */
  tableIndex: number
  /**
   * 区分フラグ
   */
  kbnFlg: string
}
/**
 * DataTableのデータ
 */
export interface DataTableData {
  /**
   * 予定一覧
   */
  stringInputAssistList: TableData[]
}
