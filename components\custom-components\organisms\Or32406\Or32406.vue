<script setup lang="ts">
/**
 * Or32406:支援経過記録画面
 * GUI01258_支援経過記録
 *
 * @description
 * 支援経過記録画面
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */

import { onMounted, reactive, ref, watch, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { Or32406Const } from './Or32406.constants'
import { Or50148Logic } from '~/components/custom-components/organisms/Or50148/Or50148.logic'
import { Or50148Const } from '~/components/custom-components/organisms/Or50148/Or50148.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import {
  useUsersProfileView,
  useSetupChildProps,
  useScreenStore,
  useScreenTwoWayBind,
} from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type {
  CpnSypKeikaSelectInEntity,
  CpnSypKeikaSelectOutEntity,
} from '~/repositories/cmn/entities/CpnSypKeikaSelectEntity'
import { CustomClass } from '~/types/CustomClassType'
import type { Or32406OnewayType } from '~/types/cmn/business/components/Or32406Type'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import { Or10929Const } from '~/components/custom-components/organisms/Or10929/Or10929.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo01272Type } from '~/types/business/components/Mo01272Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo01376Type } from '~/types/business/components/Mo01376Type'
import { Or65700Const } from '~/components/custom-components/organisms/Or65700/Or65700.constants'
import { Or65700Logic } from '~/components/custom-components/organisms/Or65700/Or65700.logic'
import type { Or65700OnewayType } from '~/types/cmn/business/components/Or65700Type'
import type { Mo01343OnewayType, Mo01343Type } from '~/types/business/components/Mo01343Type'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { useJigyoList } from '~/utils/useJigyoList'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or32406OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

/*
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()
/*
 * ユーザーリスト情報のウォッチ関数を取得
 */
const { syscomUserSelectWatchFunc } = useUserListInfo()
/*
 * 事業所リストのウォッチ関数を取得
 */
const { jigyoListWatch } = useJigyoList()
/*
 * Or32406のOneWayバインド用ローカルデータ
 */
const or21814 = ref({ uniqueCpId: '' })
/*
 * 各子コンポーネントのユニークIDを管理するref
 */
const or11871 = ref({ uniqueCpId: '' })
/*
 * 利用者一覧コンポーネントのユニークID
 */
const or00248 = ref({ uniqueCpId: '' })
/*
 * ヘッドラインコンポーネントのユニークID
 */
const orHeadLine = ref({ uniqueCpId: '' })
/*
 * 利用者詳細コンポーネントのユニークID
 */
const or00249 = ref({ uniqueCpId: '' })
/*
 * 支援経過記録コンポーネントのユニークID
 */
const or50148 = ref({ uniqueCpId: '' })
/*
 * サブコンポーネントのユニークID
 */
const or10929 = ref({ uniqueCpId: '' })
/*
 * 警告ダイアログコンポーネントのユニークID
 */
const or21815 = ref({ uniqueCpId: '' })
/*
 * 事業所選択コンポーネントのユニークID
 */
const or65700 = ref({ uniqueCpId: '' })
/*
 * 事業所リストコンポーネントのユニークID
 */
const or41179 = ref({ uniqueCpId: '' })
/*
 * 事業所選択ダイアログコンポーネントのユニークID
 */
const or00094 = ref({ uniqueCpId: '' })
/*
 * ナビゲーション制御領域のいずれかの編集フラグがON
 */
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(or50148.value.uniqueCpId)
})

/*
 * データ再取得フラグ
 */
const retrieveCmCp1DataFlg = ref<boolean>(false)

/*
 * 事業所選択の変更監視を無視するフラグ
 */
const shitenKbnCode = ref<string>('1')

/*
 * or50148コンポーネントのref
 */
const or50148Ref = ref<{ isValidate: () => boolean } | null>(null)

/**
 * 初期化処理
 */
interface jigyoInfo {
  houjinId: string
  shisetuId: string
  svJigyoId: string
  svJigyoCd: string
  jigyoRyakuKnj: string
}
/*
 * 事業所情報リスト
 */
const jigyoInfoList = ref<jigyoInfo[]>([])

/*
 * Or32406画面のOneWayバインド用ローカルデータ
 */
const localOnewayOr32406 = reactive<Or32406OnewayType>({
  /** ケアプラン方式フラグ */
  cpn_flg: '5',
  /** 視点 */
  view: '',
  /** 事業者ID */
  sv_jigyo_id: '',
  /** 職員ID */
  shoku_id: '',
  /** システムコード */
  systemCode: '',
  /** 機能ID */
  functionId: '',
  /** 電子カルテ連携フラグ */
  electronicMedicalRecordCooperationFlag: '',
  /** 処理期間開始日 */
  yymm_ymd_start: '',
  /** 処理期間終了日 */
  yymm_ymd_end: '',
  /** 利用者IDList */
  useridList: [{ userid: '**********' }],
  /** 支店区分 */
  shitenKbn: '',
  /** システム略称 */
  systemAbbr: '',
  /** 事業所コード */
  jigyoshoCd: '',
  /** 支援経過記録様式 */
  shienKeikaKirokuYoshiki: '',
  /** 編集フラグ */
  importFlag: true,
  /** 編集フラグ */
  visitConfirmFlag: true,
  /** 編集フラグ */
  importingMeetingMinutesFlag: true,
  /** 編集フラグ */
  importInquiryDetailsFlag: true,
  /** 適用事業所IDリスト */
  jigyoList: [],
  /** 事業者グループ適用ID */
  jigyoGpId: '0',
})

/*
 * 画面用ローカルデータ
 */
const local = reactive({
  // 作成日
  or32406: {
    ...localOnewayOr32406,
    ...props.onewayModelValue,
  },
  yymm_ymd_start: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  yymm_ymd_end: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  mo01343: {
    value: '',
    endValue: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  } as Mo01343Type,
})

/*
 * 各種OneWayバインド用ローカルデータ
 */
const localOneway = reactive({
  //事業所
  or65700Oneway: {} as Or65700OnewayType,
  mo00615Oneway: {
    itemLabel: t('label.processing-period'),
    showItemLabel: true,
    itemLabelFontWeight: 'normal',
  } as Mo00615OnewayType,
  mo00009OnewayProcess: {
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  mo01338Oneway: {
    value: t('label.wave-dash'),
    customClass: new CustomClass({
      outerClass: '',
      labelClass: '',
      itemClass: 'align-center',
    }),
  } as Mo00038OnewayType,
  mo00020Oneway: {
    showItemLabel: false,
    width: '133',
    showSelectArrow: true,
  } as Mo00020OnewayType,
  mo01343Oneway: {
    selectMode: '1',
  } as Mo01343OnewayType,
  mo00611OnewayVisitConfirm: {
    btnLabel: t('label.support-elapsed-record-visit-confirm'),
    prependIcon: '',
    width: '79px',
    tooltipText: t('tooltip.support-elapsed-record-visit-confirm'),
    tooltipLocation: 'bottom',
  } as Mo00611OnewayType,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
/*
 * 画面状態管理用操作変数
 */
const screenStore = useScreenStore()

/*
 * piniaから最上位の画面コンポーネント情報を取得する
 */
const pageComponent = screenStore.screen().supplement.pageComponent

/*
 * 子コンポーネントのユニークIDを設定する
 */
or50148.value.uniqueCpId = pageComponent.uniqueCpId

onMounted(async () => {
  if (systemCommonsStore.getUserSelectSelfId) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        verticalLayout: true,
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or32406Const.STR_ALL })
  })

  // コントロール設定
  init()
})

/**************************************************
 * Pinia
 **************************************************/
/*
 * 子コンポーネントのユニークIDを設定する
 */
useSetupChildProps(props.uniqueCpId, {
  [Or50148Const.CP_ID(0)]: or50148.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or10929Const.CP_ID(1)]: or10929.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or65700Const.CP_ID(0)]: or65700.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or00094Const.CP_ID(0)]: or00094.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(0)]: or00249.value,
})

/*
 * 利用者選択用の共通処理を呼び出す
 */
useUsersProfileView({
  or00248UniqCpId: or00248.value.uniqueCpId,
  or00249UniqCpId: or00249.value.uniqueCpId,
  orHeadLineUniqCpId: orHeadLine.value.uniqueCpId,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/*
 * 子コンポーネントに対して初期設定を行う
 */
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.support-elapsed-record-title'),
    showFavorite: true,
    showViewSelect: true,
    viewSelectItems: [
      {
        title: t('label.support-elapsed-record-select-individual'),
        value: '1',
      },
      {
        title: t('label.support-elapsed-record-select-list'),
        value: '2',
      },
    ],
    selectedItemViewSelect: local.or32406.shitenKbn ?? '1',
    showSaveBtn: true,
    showCreateBtn: false,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: true,
    showOptionMenuBtn: false,
    showOptionMenuDelete: false,
    tooltipTextSaveBtn: t('tooltip.save'),
  },
})
/*
 * 画面共通パラメータ
 */
const secsionParam = computed(() => {
  return {
    /** ケアプラン方式フラグ */
    cpn_flg: local.or32406.cpn_flg,
    /** 視点 */
    view: local.or32406.shitenKbn,
    /** 事業者ID */
    sv_jigyo_id: systemCommonsStore?.getSvJigyoId ?? '0',
    /** 職員ID */
    shoku_id: systemCommonsStore?.getStaffId ?? '0',
    /** システムコード */
    systemCode: systemCommonsStore?.getSystemCode ?? '0',
    /** 機能ID */
    functionId: systemCommonsStore?.getFunctionId ?? '0',
    /** 電子カルテ連携フラグ */
    electronicMedicalRecordCooperationFlag:
      systemCommonsStore?.getElectronicMedicalRecordCooperationFlag?.toString() ?? '0',
    /** 処理期間開始日 */
    yymm_ymd_start: local.yymm_ymd_start.value,
    /** 処理期間終了日 */
    yymm_ymd_end: local.yymm_ymd_end.value,
    /** 利用者IDList */
    useridList: local.or32406.useridList,
    /** 支店区分 */
    shitenKbn: local.or32406.shitenKbn,
    /** システム略称 */
    systemAbbr: local.or32406.systemAbbr ?? systemCommonsStore?.getSystemAbbreviation,
    /** 事業所コード */
    jigyoshoCd: local.or32406.jigyoshoCd ?? systemCommonsStore?.getSvJigyoCd,
    /** 支援経過記録様式 */
    shienKeikaKirokuYoshiki: local.or32406.shienKeikaKirokuYoshiki ?? '改訂前',
    /** 適用事業所IDリスト */
    jigyoList: systemCommonsStore.getSvJigyoIdList?.map((value) => ({ jigyoId: value })) ?? [],
    /** 事業者グループ適用ID */
    jigyoGpId: '0',

    /** 編集フラグ */
    importFlag: local.or32406?.importFlag ?? false,
    /** 編集フラグ */
    visitConfirmFlag: local.or32406?.visitConfirmFlag ?? false,
    /** 編集フラグ */
    importingMeetingMinutesFlag: local.or32406?.importingMeetingMinutesFlag ?? false,
    /** 編集フラグ */
    importInquiryDetailsFlag: local.or32406?.importInquiryDetailsFlag ?? false,
  }
})

/*
 * 画面共通パラメータをOr50148に設定
 */
const visitConfirmFlag = computed(() => {
  return (
    secsionParam.value.systemAbbr === Or32406Const.DEFAULT.SYSCD &&
    secsionParam.value.jigyoshoCd !== Or32406Const.DEFAULT.JIGYOSHOCD
  )
})

/*

 * 支店区分
 */
const checkShitenKbn = computed(() => {
  return secsionParam.value.shitenKbn === Or32406Const.DEFAULT.DEFAULT_1
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  (newValue, oldValue) => {
    if (newValue === Or32406Const.DEFAULT.DEFAULT_UNDEFINED) {
      return
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      void saveButtonClick()
      setOr11871Event({ saveEventFlg: false })
    }
    if (newValue.printEventFlg) {
      // 印刷ボタンが押下された場合、印刷設定画面を表示する
      void printSettingIconClick()
      setOr11871Event({ printEventFlg: false })
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      void masterIconClick()
      setOr11871Event({ masterEventFlg: false })
    }

    if (newValue.viewSelectEventFlg) {
      if (ignoreCompanySelectWatch.value) return
      void onChangeMode(
        newValue?.viewSelectEventParams?.selectedItemValue ?? '1',
        oldValue?.viewSelectEventParams?.selectedItemValue ?? '1'
      )
      setOr11871Event({ viewSelectEventFlg: false })
    }
  }
)

/**
 * 事業所選択更新の監視
 */
watch(
  () => Or65700Logic.data.get(or65700.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    //Undefinedの時戻す
    if (isUndefined(newValue)) {
      return
    }
    const optionType = newValue?.emitType
    if (isEdit.value) {
      // AC004-2と同じ
      const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
        dialogTitle: t('label.confirm'),
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case 'yes':
          void _update()
          break
        case 'no':
          //事業所変更の場合
          getCommonData()
          break
        case 'cancel':
          return
      }
    } else {
      if (optionType === Or32406Const.DEFAULT.DEFAULT_1) {
        localOneway.or65700Oneway.officeId = newValue.officeId
        localOneway.or65700Oneway.officeName = newValue.officeName

        retrieveCmCp1DataFlg.value = true

        Or65700Logic.data.set({
          uniqueCpId: or65700.value.uniqueCpId,
          value: { officeId: newValue.officeId, officeName: newValue.officeName, emitType: '' },
        })
      }
    }
  },
  { deep: true }
)

/**
 * データ再取得フラグの監視
 */
watch(
  () => retrieveCmCp1DataFlg.value,
  (newValue) => {
    if (newValue) {
      // 共通情報取得
      getCommonData()
      Or50148Logic.state.set({
        uniqueCpId: or50148.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'getData',
            ...secsionParam.value,
          },
        },
      })
      retrieveCmCp1DataFlg.value = false
    }
  }
)
/**
 * 処理期間（開始・終了日）の変更を監視
 */
watch(
  () => [local.yymm_ymd_start, local.yymm_ymd_end],
  async ([newStart, newEnd], [oldStart, oldEnd]) => {
    if (newStart !== oldStart || newEnd !== oldEnd) {
      if (isEdit.value) {
        // AC004-2と同じ
        const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
          dialogTitle: t('label.confirm'),
          dialogText: t('message.i-cmn-10430'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'destroy1',
          secondBtnLabel: t('btn.no'),
          thirdBtnType: 'normal3',
          thirdBtnLabel: t('btn.cancel'),
        })

        switch (dialogResult) {
          case 'yes':
            void _update()
            // await getCommonData()
            break
          case 'no':
            getCommonData()
            break
          case 'cancel':
            return
        }
      } else {
        getCommonData()
      }
    }
  }
)
/**
 * mo01343.value の変更を監視し、開始日に反映
 */
watch(
  () => local.mo01343.value,
  (newValue) => {
    if (newValue) {
      local.yymm_ymd_start.value = newValue
      if (local.yymm_ymd_start.mo01343) {
        local.yymm_ymd_start.mo01343.value = newValue
      }
    }
  }
)
/**
 * mo01343.endValue の変更を監視し、終了日に反映
 */
watch(
  () => local.mo01343.endValue,
  (newValue) => {
    if (newValue) {
      local.yymm_ymd_end.value = newValue
      if (local.yymm_ymd_end.mo01343) {
        local.yymm_ymd_end.mo01343.endValue = newValue
      }
    }
  }
)

/**
 * 事業所選択
 */
watch(
  () => Or41179Logic.data.get(or41179.value.uniqueCpId),
  async () => {
    if (isEdit.value) {
      // AC004-2と同じ
      await callbackGroup()
    } else {
      // await getCommonData()
    }
  }
)

/**
 * 五十音を変更した場合の処理
 */
watch(
  () => Or00094Logic.event.get(or00094.value.uniqueCpId),
  (newValue) => {
    if (newValue === Or32406Const.DEFAULT.DEFAULT_UNDEFINED) {
      return
    }
    if (newValue.charBtnClickFlg) {
      // 子コンポーネントのflgをリセットする
      const workFilterInitials = Or00094Logic.data.get(or00094.value.uniqueCpId)
      if (workFilterInitials?.selectValueArray !== Or32406Const.DEFAULT.DEFAULT_UNDEFINED) {
        // システム共有領域．利用者選択領域に50音ヘッドラインの選択値を設定
        systemCommonsStore.setUserSelectFilterInitials(workFilterInitials.selectValueArray)
      }

      Or00094Logic.event.set({
        uniqueCpId: or00094.value.uniqueCpId,
        events: { charBtnClickFlg: false },
      })
    }
  }
)
/**************************************************
 * 関数
 **************************************************/

/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}
/*
 * 会社選択監視を一時的に無効化するフラグ
 */
const ignoreCompanySelectWatch = ref(false)
/*
 * ユーザー選択監視を一時的に無効化するフラグ
 */
const ignoreUserSelectWatch = ref(false)
/*
 * 利用者一覧データ
 */
const listUser = ref(systemCommonsStore.getUserSelectUserList())

/*
 * ダイアログ表示フラグ
 */
const showDialog21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 利用者選択変更時のコールバック関数
 *
 * @param userId - 新しく選択された利用者ID
 */
const callbackUserChange = async (userId: string) => {
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()
        void updateUserId(userId)
        break
      case 'no': {
        ignoreUserSelectWatch.value = true
        systemCommonsStore.setUserId(userId)
        void updateUserId(userId)
        // const indexSelectUser = listUser.value?.findIndex((user) => user.selfId === userId) ?? 0
        // Or00249Logic.data.set({
        //   uniqueCpId: or00249.value.uniqueCpId,
        //   value: { selectUserIndex: indexSelectUser },
        // })
        getCommonData()
        await nextTick(() => {
          ignoreUserSelectWatch.value = false
        })
        break
      }
      case 'cancel':
        // 処理終了
        return
    }
  } else {
    void updateUserId(userId)
  }
}
/**
 * 利用者ID更新時のコールバック関数
 *
 * @param userId - 更新する利用者ID
 */
const updateUserId = (userId: string) => {
  local.or32406.useridList = [{ userid: userId }]
  const listUserId = listUser.value?.map((item) => ({ userid: item.selfId })) ?? []
  if (local.or32406.shitenKbn !== Or32406Const.DEFAULT.DEFAULT_1) {
    local.or32406.useridList = listUserId
  }
  systemCommonsStore.setStaffId(userId)
  systemCommonsStore.setUserId(userId)
  if (!isEdit.value) {
    getCommonData()
  }
}

/**
 * 事業所選択変更時のコールバック関数
 *
 * @param newJigyoId - 新しく選択された事業所ID
 */
const callbackFuncJigyo = async (newJigyoId: string) => {
  console.log('callbackFuncJigyo', newJigyoId)
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()
        break
      case 'no':
        getCommonData()
        break
      case 'cancel':
        // 処理終了
        return
    }
  } else {
    systemCommonsStore.setSvJigyoId(newJigyoId)
    getCommonData()
  }
}
/*
 * 事業所リストのウォッチ関数
 */
jigyoListWatch(or41179.value.uniqueCpId, (newJigyoId: string) => {
  console.log('newJigyoId', newJigyoId)
  void callbackFuncJigyo(newJigyoId)
})
/*
 * ユーザーリスト選択のウォッチ関数
 */
syscomUserSelectWatchFunc((userId: string) => {
  if (ignoreUserSelectWatch.value) return
  void callbackUserChange(userId)
})
/*
 * グループ選択時のコールバック関数
 */
const callbackGroup = async () => {
  const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
    dialogTitle: t('label.confirm'),
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })

  switch (dialogResult) {
    case 'yes':
      void _update()
      // await getCommonData()
      break
    case 'no':
      getCommonData()
      break
    case 'cancel':
      return
  }
}

/**
 *  AC001_初期表示
 */
const init = () => {
  // 事業所を設定
  localOneway.or65700Oneway.officeId = '1'
  localOneway.or65700Oneway.officeName = '居宅支援(介)ほのぼの'

  const systemDate = systemCommonsStore.getSystemDate
    ? new Date(systemCommonsStore.getSystemDate)
    : new Date()
  const resultKobetsu = getDefaultPeriod(systemDate, local.or32406.shitenKbn)

  local.yymm_ymd_start.value = resultKobetsu.yymm_ymd_start
  local.yymm_ymd_end.value = resultKobetsu.yymm_ymd_end

  const sortedUserList = [...(listUser.value ?? [])].sort((a, b) =>
    (a.nameKanaSei + a.nameKanaMei).localeCompare(b.nameKanaSei + b.nameKanaMei, 'ja')
  )
  const listUserId = sortedUserList.map((item) => ({ userid: item.selfId })) ?? []
  if (local.or32406.shitenKbn !== Or32406Const.DEFAULT.DEFAULT_1) {
    local.or32406.useridList = listUserId
  } else {
    const userId = listUserId[0]?.userid ?? '**********'
    local.or32406.useridList = [{ userid: userId }]
    systemCommonsStore.setUserId(userId)
    systemCommonsStore.setStaffId(userId)
  }

  getCommonData()

  const defaultSelect = jigyoInfoList.value[0]?.svJigyoId ?? '1'
  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: { modelValue: defaultSelect },
  })

  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      labelClassVertical: 'margin-bottom-custom',
    },
  })
}

/**
 * 日付を"yyyy/MM/dd"形式の文字列に変換する関数
 *
 * @param  date - 変換する日付
 *
 * @returns "yyyy/MM/dd"形式の日付文字列
 */
const formatDate = (date: Date): string => {
  const y = date.getFullYear()
  const m = String(date.getMonth() + 1).padStart(2, '0')
  const d = String(date.getDate()).padStart(2, '0')
  return `${y}/${m}/${d}`
}

/**
 * 処理期間のデフォルト値を取得する関数
 *
 * @param  systemDate - システム日付
 *
 * @param shitenKbn - 支店区分
 *
 * @returns 開始日・終了日
 */
const getDefaultPeriod = (systemDate: Date, shitenKbn: string) => {
  // 個別
  if (shitenKbn === Or32406Const.DEFAULT.DEFAULT_1) {
    const start = new Date(systemDate.getFullYear(), systemDate.getMonth() - 2, 1)
    const end = new Date(systemDate.getFullYear(), systemDate.getMonth() + 1, 0)
    return {
      yymm_ymd_start: formatDate(start),
      yymm_ymd_end: formatDate(end),
    }
  } else {
    // 一覧
    const sysDateStr = formatDate(systemDate)
    return {
      yymm_ymd_start: sysDateStr,
      yymm_ymd_end: sysDateStr,
    }
  }
}
const initial = async () => {
  const inputParam: CpnSypKeikaSelectInEntity = {
    /** ケアプラン方式フラグ */
    cpnFlg: secsionParam.value.cpn_flg,
    /** 視点 */
    view: secsionParam.value.view,
    /** 事業者ID */
    svJigyoId: secsionParam.value.sv_jigyo_id,
    /** 職員ID */
    shokuId: systemCommonsStore?.getStaffId ?? '',
    /** システムコード */
    systemCode: secsionParam.value.systemCode,
    /** 機能ID */
    functionId: secsionParam.value.functionId,
    /** 電子カルテ連携フラグ */
    electronicMedicalRecordCooperationFlag:
      secsionParam.value.electronicMedicalRecordCooperationFlag,
    /** 処理期間開始日 */
    yymmYmdStart: secsionParam.value.yymm_ymd_start,
    /** 処理期間終了日 */
    yymmYmdEnd: secsionParam.value.yymm_ymd_end,
    /** 利用者IDList */
    useridList: secsionParam.value.useridList,
    /** 適用事業所IDリスト */
    jigyoList: secsionParam.value.jigyoList,
    /** 事業者グループ適用ID */
    jigyoGpId: secsionParam.value.jigyoGpId,
  }
  // APIで月間・年間表詳細情報を取得する。
  const response: CpnSypKeikaSelectOutEntity = await ScreenRepository.select(
    'cpnSypKeikaSelect',
    inputParam
  )
  if (response.data) {
    let dataMapping = []
    if (secsionParam.value.cpn_flg !== Or32406Const.DEFAULT.VALUE_CPN_FLG) {
      dataMapping = response.data.kaigoShiyenList.map((el) => {
        const username = listUser.value?.find((user) => Number(user.selfId) === Number(el.userid))
        const item = {
          ...el,
          /** レコード番号 */
          recNo: el.recNo,
          /** 事業者ID */
          svJigyoId: el.svJigyoId,
          /** 利用者名 */
          uniqueId: uuidv4(),
          userid: el.userid,
          houjinId: el.houjinId ?? '',
          shisetuId: el.shisetuId ?? '',
          timeStmp: el.timeStmp ?? '',
          nameKanaSei: username?.nameKanaSei,
          nameUser: `${username?.nameSei ?? ''} ${username?.nameMei ?? ''}`,
          yymmYmd: {
            value: el.yymmYmd,
            mo01343: {
              value: el.yymmYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          fromTimeHhmm: {
            value: `${el.timeHh?.padStart(2, '0') ?? '00'}:${el.timeMm?.padStart(2, '0') ?? '00'}`,
          } as Mo01272Type,
          toTimeHhmm: {
            value: `${el.endHh?.padStart(2, '0') ?? '00'}:${el.endMm?.padStart(2, '0') ?? '00'}`,
          } as Mo01272Type,
          timneControl: {
            value: `${el.timeHh?.padStart(2, '0') ?? '00'}:${el.timeMm?.padStart(2, '0') ?? '00'}`,
            valueHour: el.timeHh?.padStart(2, '0') ?? '00',
            valueMinute: el.timeMm?.padStart(2, '0') ?? '00',
            valueTo: `${el.endHh?.padStart(2, '0') ?? '00'}:${el.endMm?.padStart(2, '0') ?? '00'}`,
            valueHourTo: el.endHh?.padStart(2, '0') ?? '00',
            valueMinuteTo: el.endMm?.padStart(2, '0') ?? '00',
            mo00024: {
              isOpen: false,
            },
            mo00038HourTens: {
              mo00045: {
                value: '0',
              },
            },
            mo00038HourOnes: {
              mo00045: {
                value: '0',
              },
            },
            mo00038MinuteTens: {
              mo00045: {
                value: '0',
              },
            },
            mo00038MinuteOnes: {
              mo00045: {
                value: '0',
              },
            },
          } as Mo01376Type,
          titleKnj: {
            value: el.titleKnj,
          } as Mo00046Type,
          /** ケース種別 */
          caseCd: {
            modelValue: el.caseCd,
          } as Mo00040Type,
          /** サービス提供種別CD */
          svTeikyoCd: {
            modelValue: '',
          } as Mo00040Type,
          koumokuKnj: {
            value: el.koumokuKnj,
          } as Mo00046Type,
          memoKnj: {
            value: el.caseKnj,
          } as Mo00046Type,
          /** 記録者（文字列） */
          shokuKnj: el.shokuKnj,
          /** 担当者 */
          tantoName: '',
          /** 記録者 */
          kirokuName: '',
          /** 訪問チェックフラグ */
          houmonFlg: {
            modelValue: el.houmonFlg === Or32406Const.DEFAULT.DEFAULT_0 ? false : true,
          } as Mo00018Type,
          /** 計画書（1）チェックフラグ */
          kkak1Flg: {
            modelValue: el.kkak1Flg === Or32406Const.DEFAULT.DEFAULT_0 ? false : true,
          } as Mo00018Type,
          /** 計画書（2）チェックフラグ */
          kkak2Flg: {
            modelValue: el.kkak2Flg === Or32406Const.DEFAULT.DEFAULT_0 ? false : true,
          } as Mo00018Type,
          /** 週間計画チェックフラグ */
          weekFlg: {
            modelValue: el.weekFlg === Or32406Const.DEFAULT.DEFAULT_0 ? false : true,
          } as Mo00018Type,
          /** 利用票チェックフラグ */
          riyoFlg: {
            modelValue: el.riyoFlg === Or32406Const.DEFAULT.DEFAULT_0 ? false : true,
          } as Mo00018Type,
          /** 計画対象年月 */
          taishoYm: {
            value: el.taishoYm,
            mo01343: {
              value: el.taishoYm,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          visible: true,
          updateKbn: 'U',
        }
        return item
      })
    } else {
      dataMapping = response.data.shoguShiyenList.map((el) => {
        const username = listUser.value?.find((user) => Number(user.selfId) === Number(el.userid))
        const item = {
          ...el,
          /** レコード番号 */
          recNo: '',
          /** 事業者ID */
          svJigyoId: el.svJigyoId,
          /** 利用者名 */
          uniqueId: uuidv4(),
          userid: el.userid,
          houjinId: el.houjinId ?? '',
          shisetuId: el.shisetuId ?? '',
          timeStmp: '',
          nameKanaSei: username?.nameKanaSei,
          nameUser: `${username?.nameSei ?? ''} ${username?.nameMei ?? ''}`,
          /** 年月日 */
          yymmYmd: {
            value: el.kirokuYmd,
            mo01343: {
              value: el.kirokuYmd,
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          fromTimeHhmm: {
            value: `${el.startHh?.padStart(2, '0') ?? '00'}:${el.startMm?.padStart(2, '0') ?? '00'}`,
          } as Mo01272Type,
          toTimeHhmm: {
            value: `${el.endHh?.padStart(2, '0') ?? '00'}:${el.endMm?.padStart(2, '0') ?? '00'}`,
          } as Mo01272Type,
          timneControl: {
            value: `${el.startHh?.padStart(2, '0') ?? '00'}:${el.startMm?.padStart(2, '0') ?? '00'}`,
            valueHour: el.startHh?.padStart(2, '0') ?? '00',
            valueMinute: el.startMm?.padStart(2, '0') ?? '00',
            valueTo: `${el.endHh?.padStart(2, '0') ?? '00'}:${el.endMm?.padStart(2, '0') ?? '00'}`,
            valueHourTo: el.endHh?.padStart(2, '0') ?? '00',
            valueMinuteTo: el.endMm?.padStart(2, '0') ?? '00',
            mo00024: {
              isOpen: false,
            },
            mo00038HourTens: {
              mo00045: {
                value: '0',
              },
            },
            mo00038HourOnes: {
              mo00045: {
                value: '0',
              },
            },
            mo00038MinuteTens: {
              mo00045: {
                value: '0',
              },
            },
            mo00038MinuteOnes: {
              mo00045: {
                value: '0',
              },
            },
          } as Mo01376Type,
          titleKnj: {
            value: el.jikan,
          } as Mo00046Type,
          caseCd: {
            modelValue: '',
          } as Mo00040Type,
          /** サービス提供種別CD */
          svTeikyoCd: {
            modelValue: el.svTeikyoCd,
          } as Mo00040Type,
          koumokuKnj: {
            value: '',
          } as Mo00046Type,
          memoKnj: {
            value: el.memoKnj,
          } as Mo00046Type,
          shokuKnj: '',
          /** 担当者 */
          tantoName: el.tantoName,
          /** 記録者 */
          kirokuName: el.kirokuName,
          /** 訪問チェックフラグ */
          houmonFlg: {
            modelValue: false,
          } as Mo00018Type,
          /** 計画書（1）チェックフラグ */
          kkak1Flg: {
            modelValue: false,
          } as Mo00018Type,
          /** 計画書（2）チェックフラグ */
          kkak2Flg: {
            modelValue: false,
          } as Mo00018Type,
          /** 週間計画チェックフラグ */
          weekFlg: {
            modelValue: false,
          } as Mo00018Type,
          /** 利用票チェックフラグ */
          riyoFlg: {
            modelValue: false,
          } as Mo00018Type,
          /** 計画対象年月 */
          taishoYm: {
            value: '',
            mo01343: {
              value: '',
              mo00024: {
                isOpen: false,
              } as Mo00024Type,
            } as Mo01343Type,
          } as Mo00020Type,
          visible: true,
          updateKbn: 'U',
        }
        return item
      })
    }

    const jigyoInfoListData =
      response.data.tekiyoJigyoList?.map((item) => ({
        houjinId: item.houjinId,
        shisetuId: item.shisetuId,
        svJigyoId: item.svJigyoId,
        svJigyoCd: item.svJigyoId,
        jigyoRyakuKnj: item.jigyoNameKnj,
      })) ?? []

    jigyoInfoList.value = jigyoInfoListData

    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        jigyoInfoList: jigyoInfoListData,
      },
    })

    screenStore.setCpTwoWay({
      cpId: Or50148Const.CP_ID(0),
      uniqueCpId: or50148.value.uniqueCpId,
      value: {
        kaigo_shiyen_list: response.data.kaigoShiyenList,
        shubetsu_list: response.data.shubetsuList,
        shogu_shiyen_list: response.data.shoguShiyenList,
        service_list: response.data.serviceList,
        shiyen_list_mapping: dataMapping,
        tekiyoJigyoList: response.data.tekiyoJigyoList,
      },
      isInit: true,
    })
  }
}
/**
 * 共通情報取得
 */
const getCommonData = () => {
  Or50148Logic.state.set({
    uniqueCpId: or50148.value.uniqueCpId,
    state: {
      param: {
        executeFlag: 'getData',
        initialFunc: initial,
        ...secsionParam.value,
      },
    },
  })
}

/**
 * AC022_「視点切替」プルダウン選択
 *
 * @param value - 選択された視点の値
 *
 * @param oldValue - 以前の視点の値
 */
const onChangeMode = async (value: string, oldValue: string) => {
  const sortedUserList = [...(listUser.value ?? [])].sort((a, b) =>
    (a.nameKanaSei + a.nameKanaMei).localeCompare(b.nameKanaSei + b.nameKanaMei, 'ja')
  )
  const listUserId = sortedUserList.map((item) => ({ userid: item.selfId })) ?? []
  const indexSelectUser =
    listUserId?.findIndex((user) => user.userid === listUserId[0]?.userid) ?? 0

  if (value !== Or32406Const.DEFAULT.DEFAULT_1) {
    local.or32406.useridList = listUserId
  } else {
    local.or32406.useridList = [{ userid: listUserId[0]?.userid ?? '**********' }]
    systemCommonsStore.setUserId(listUserId[0]?.userid ?? '**********')
    systemCommonsStore.setStaffId(listUserId[0]?.userid ?? '**********')
    Or00249Logic.data.set({
      uniqueCpId: or00249.value.uniqueCpId,
      value: { selectUserIndex: indexSelectUser },
    })
  }
  shitenKbnCode.value = value

  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        ignoreCompanySelectWatch.value = true
        void _update()
        await nextTick(() => {
          if (ignoreCompanySelectWatch.value) {
            local.or32406.shitenKbn = shitenKbnCode.value
            getCommonData()
          }
          ignoreCompanySelectWatch.value = false
        })
        break
      case 'no':
        // 子コンポーネントに対して初期設定を行う
        local.or32406.shitenKbn = oldValue
        Or11871Logic.state.set({
          uniqueCpId: or11871.value.uniqueCpId,
          state: {
            selectedItemViewSelect: oldValue,
          },
        })
        ignoreCompanySelectWatch.value = true
        local.or32406.useridList = [{ userid: listUserId[0]?.userid }]
        systemCommonsStore.setUserId(listUserId[0]?.userid)
        systemCommonsStore.setStaffId(listUserId[0]?.userid)
        Or00249Logic.data.set({
          uniqueCpId: or00249.value.uniqueCpId,
          value: { selectUserIndex: indexSelectUser },
        })
        getCommonData()
        await nextTick(() => {
          ignoreCompanySelectWatch.value = false
        })
        break
      case 'cancel':
        // 処理終了
        return
    }
  } else {
    local.or32406.shitenKbn = value
    getCommonData()
  }
}

/**
 * AC003_「保存ボタン」押下
 */
const _update = () => {
  // 画面入力データに変更がない場合
  if (!isEdit.value) {
    // 処理終了にする
    return
  }

  // アセスメント(インターライ)画面履歴の最新情報を取得する
  Or50148Logic.state.set({
    uniqueCpId: or50148.value.uniqueCpId,
    state: {
      param: {
        executeFlag: 'saveAndInit',
        initialFunc: initial,
        ...secsionParam.value,
      },
    },
  })
}

/**
 * AC008_「オプション」メニュー「ケース取込」押下
 */
// const onImport = () => {
//   //GUI01016_ケース一覧選択画面を開く。
//   Or50148Logic.state.set({
//     uniqueCpId: or50148.value.uniqueCpId,
//     state: {
//       param: {
//         executeFlag: 'import',
//         ...secsionParam.value,
//       },
//     },
//   })
// }

/**
 * AC009_「オプション」メニュー「訪問確認」押下
 */
const onVisitConfirm = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()
        break
      case 'no':
        getCommonData()
        break
      case 'cancel':
        // 処理終了
        return
    }
  } else {
    //GUI01259_支援経過確認一覧画面を開く。
    Or50148Logic.state.set({
      uniqueCpId: or50148.value.uniqueCpId,
      state: {
        param: {
          executeFlag: 'visitConfirm',
          ...secsionParam.value,
        },
      },
    })
  }
}

/**
 * AC010_「オプション」メニュー「会議録取込」押下
 */
// const onImportingMeetingMinutes = () => {
//   //GUI01260_会議録取込画面を開く。
//   Or50148Logic.state.set({
//     uniqueCpId: or50148.value.uniqueCpId,
//     state: {
//       param: {
//         executeFlag: 'importingMeetingMinutes',
//         ...secsionParam.value,
//       },
//     },
//   })
// }
/**
 * AC011_「オプション」メニュー「照会内容取込」押下
 */
// const onImportInquiryDetails = () => {
//   //GUI01261_照会内容取込画面を開く。
//   Or50148Logic.state.set({
//     uniqueCpId: or50148.value.uniqueCpId,
//     state: {
//       param: {
//         executeFlag: 'importInquiryDetails',
//         ...secsionParam.value,
//       },
//     },
//   })
// }
/**
 * AC021_「処理期間-前後遷移」アイコン押下
 */
const onProcessingPeriod = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()
        // await getCommonData()
        break
      case 'no':
        onOpenDateTime()
        break
      case 'cancel':
        // 処理終了
        return
    }
  } else {
    onOpenDateTime()
  }
}

/**
 * 日付選択ダイアログを開く処理
 */
const onOpenDateTime = () => {
  local.mo01343.value = local.yymm_ymd_start.value
  local.mo01343.endValue = local.yymm_ymd_end.value

  if (local.mo01343.mo00024) {
    local.mo01343.mo00024.isOpen = true
  }
}

/**
 * AC003_「保存」ボタン押下
 */
const saveButtonClick = () => {
  // 画面入力データに変更がある場合
  if (!isEdit.value) {
    // メッセージ「i.cmn.21800」
    showOr21815MsgOneBtn(t('message.i-cmn-21800'))
    return
  }
  // 入力チェックエラーがある場合
  if (!or50148Ref.value?.isValidate()) {
    return
  }
  void _update()
}

/**
 * 確認ダイアログの開閉
 *
 * @param errormsg - エラーメッセージ
 */
function showOr21815MsgOneBtn(errormsg: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: errormsg,
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.ok'),
      // 第2ボタンタイプ
      secondBtnType: 'blank',
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * AC004_「印刷」アイコン押下
 */
const printSettingIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()
        break
      case 'no':
        getCommonData()
        break
      case 'cancel':
        // 処理終了
        return
    }
  } else {
    Or50148Logic.state.set({
      uniqueCpId: or50148.value.uniqueCpId,
      state: {
        param: {
          executeFlag: 'printSetting',
          ...secsionParam.value,
        },
      },
    })
  }
}

/**
 * AC005_「マスタ」アイコン押下
 */
const masterIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        void _update()
        break
      case 'no':
        getCommonData()
        break
      case 'cancel':
        // 処理終了
        return
    }
  } else {
    //GUI01255_支援経過記録マスタ画面を開く。
    Or50148Logic.state.set({
      uniqueCpId: or50148.value.uniqueCpId,
      state: {
        param: {
          executeFlag: 'master',
          ...secsionParam.value,
        },
      },
    })
  }
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21815StateType) => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)

        let result = 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <c-v-sheet class="view">
    <g-base-or11871 v-bind="or11871">
      <template
        v-if="visitConfirmFlag"
        #customButtons
      >
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayVisitConfirm"
          class="ml-0 pl-0"
          @click="onVisitConfirm"
        >
        </base-mo00611>
      </template>
      <!-- <template #optionMenuItems> -->
      <!-- <c-v-list-item
          v-if="local.or32406.importFlag"
          :title="t('label.support-elapsed-record-import')"
          prepend-icon="open_in_browser"
          @click="onImport"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.support-elapsed-record-import')"
          />
        </c-v-list-item> -->
      <!-- <c-v-list-item
          v-if="visitConfirmFlag"
          :title="t('label.support-elapsed-record-visit-confirm')"
          prepend-icon=" "
          @click="onVisitConfirm"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.support-elapsed-record-visit-confirm')"
          />
        </c-v-list-item> -->
      <!-- <c-v-list-item
          v-if="local.or32406.importingMeetingMinutesFlag"
          :title="t('label.support-elapsed-record-importing-meeting-minutes')"
          prepend-icon=" "
          @click="onImportingMeetingMinutes"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.support-elapsed-record-importing-meeting-minutes')"
          />
        </c-v-list-item>
        <c-v-list-item
          v-if="local.or32406.importInquiryDetailsFlag"
          :title="t('label.support-elapsed-record-import-inquiry-details')"
          prepend-icon=" "
          @click="onImportInquiryDetails"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.support-elapsed-record-import-inquiry-details')"
          />
        </c-v-list-item> -->
      <!-- </template> -->
    </g-base-or11871>
    <c-v-row
      no-gutters
      class="main-Content main-custom-scroll"
    >
      <!-- ナビゲーションエリア -->
      <c-v-col
        v-if="checkShitenKbn"
        cols="2"
        class="hidden-scroll h-100"
      >
        <!-- 利用者選択一覧 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <!-- コンテンツエリア -->
      <c-v-col
        :cols="checkShitenKbn ? 10 : 12"
        style="padding-left: 16px !important"
        class="main-right h-100 px-2"
      >
        <!-- 上段 -->
        <c-v-row
          no-gutters
          :class="{ 'top': checkShitenKbn, 'top-mode-2': !checkShitenKbn }"
        >
          <c-v-col
            class="d-flex"
            style="gap: 32px; align-items: center"
          >
            <!-- 事業所 -->
            <div>
              <g-base-or-41179
                v-bind="or41179"
                :width="'203'"
                class="custom-select-business"
              />
            </div>
            <div>
              <div>
                <c-v-row
                  no-gutters
                  class="mb-2"
                >
                  <!--履歴タイトルラベル-->
                  <c-v-col cols="auto">
                    <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway" />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="align-center button-calender"
                >
                  <div class="rireki-field d-flex align-center">
                    <base-mo00009
                      :class="['icon-edit-btn']"
                      :oneway-model-value="localOneway.mo00009OnewayProcess"
                      @click.stop="onProcessingPeriod"
                    />
                    <base-mo00020
                      v-model="local.yymm_ymd_start"
                      class="custom-date-start"
                      :oneway-model-value="localOneway.mo00020Oneway"
                    />
                  </div>
                  <div class="mx-1">〜</div>
                  <div class="rireki-field">
                    <base-mo00020
                      v-model="local.yymm_ymd_end"
                      class="custom-date-end"
                      :oneway-model-value="localOneway.mo00020Oneway"
                    />
                  </div>
                </c-v-row>
              </div>
            </div>
          </c-v-col>
        </c-v-row>
        <!-- <c-v-divider class="mt-4 mb-4" /> -->
        <c-v-row no-gutters class="pb-6">
          <c-v-col
            cols="12"
            :style="{ display: !checkShitenKbn ? 'flex' : '' }"
          >
            <div
              v-if="!checkShitenKbn"
              style="margin-top: 63px; width: 2%"
            >
              <g-base-or00094 v-bind="or00094"></g-base-or00094>
            </div>
            <div :style="{ width: !checkShitenKbn ? '98%' : '100%' }">
              <g-custom-or-50148
                v-bind="or50148"
                ref="or50148Ref"
              />
            </div>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <!-- <c-v-row
      no-gutters
      class="footer"
    >
      <c-v-col>
        <g-base-or00051 />
      </c-v-col>
    </c-v-row> -->
  </c-v-sheet>
  <!-- メッセージ -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- mo01343 -->
  <base-mo01343
    v-model="local.mo01343"
    :oneway-model-value="localOneway.mo01343Oneway"
  />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialog21814"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
:deep(.pl-4) {
  padding-left: 0px !important;
}

.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.main-Content {
  display: flex;
  flex-grow: 1;
  overflow: auto;
  overflow-x: hidden;
  height: 100%;

  .main-right {
    height: 100%;
    display: flex;
    flex-direction: column;

    .top {
      flex: 0 1 auto;
      padding: 8px 8px 24px 8px;

      :deep(.v-sheet) {
        background-color: transparent !important;
      }
    }

    .top-mode-2 {
      flex: 0 1 auto;
      padding: 8px 8px 24px 41px;

      :deep(.v-sheet) {
        background-color: transparent !important;
      }
    }

    .footer {
      flex: 0 1 auto;
    }
  }
}

.createDateOuterClass {
  background: rgb(var(--v-theme-background));
}

.tabItems {
  border-top: thin solid rgb(var(--v-theme-form));
  margin-top: 8px;
}
.custom-select-business {
  padding: 0px !important;
}

:deep(.margin-bottom-custom) {
  margin-bottom: 7px !important;
}

$border-color: rgb(var(--v-theme-form));
$icon-edit-btn-width: 36px;
$icon-border-radius: 3px;
$default-height: 36px;
.rireki-field {
  // border: 1px solid $border-color;
  border-top-left-radius: $icon-border-radius !important;
  border-end-start-radius: $icon-border-radius !important;
  border-top-right-radius: $icon-border-radius !important;
  border-end-end-radius: $icon-border-radius !important;
}
.icon-edit-btn {
  color: rgb(var(--v-theme-light-blue-400));
  width: $icon-edit-btn-width;
  height: $default-height;
}
.rireki-label {
  padding: 0 16px;
  background-color: rgb(var(--v-theme-black-100));
  height: $default-height;
  border-left: 1px solid $border-color;
}
.icon-page-btn {
  border-left: 1px solid $border-color;
  border-radius: 0;
  height: $default-height;
  width: $icon-edit-btn-width;
  color: rgb(var(--v-theme-blue-900));
}
:deep(.button-calender) {
  button {
    background-color: #ebf2fd;
    border: 1px solid #c1c6cc;
    border-right: none;
    border-radius: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  .v-field {
    &:nth-child(1) {
      border-radius: 0;
    }
    &:nth-child(2) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .custom-date-start {
    button {
      i {
        color: #214d97;
      }
      &:nth-child(1) {
        border-radius: 0;
      }
      &:nth-child(3) {
        border-radius: 0;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-left: none;
        border-right: 1px solid #c1c6cc;
      }
    }
  }
  .custom-date-end {
    button {
      i {
        color: #214d97;
      }
      &:nth-child(3) {
        border-radius: 0;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-left: none;
        border-right: 1px solid #c1c6cc;
      }
    }
  }
}
:deep(.v-divider--inset.v-divider--vertical) {
  margin-bottom: 4px !important;
  margin-top: 4px !important;
  max-height: calc(100% - 16px);
}

.main-custom-scroll {
  overflow: auto !important;
  scrollbar-width: auto;
}
</style>
