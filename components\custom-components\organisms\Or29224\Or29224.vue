<script setup lang="ts">
/**
 * Or29224:状況の事実入力
 * GUI00915_状況の事実
 *
 * @description
 * 状況の事実入力
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { useI18n } from 'vue-i18n'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { Or05658Const } from '../Or05658/Or05658.constants'
import type { DataTableType, JyokyoSyosaiType } from '../Or05656/Or05656.type'
import { Or10355Logic } from '../Or10355/Or10355.logic'
import { Or10355Const } from '../Or10355/Or10355.constants'
import { Or05656Const } from '../Or05656/Or05656.constants'
import { Or05657Const } from '../Or05657/Or05657.constants'
import { Or05657Logic } from '../Or05657/Or05657.logic'
import { Or29224Const } from './Or29224.constants'
import type { AsyncFunction } from './Or29224.type'
import { useScreenTwoWayBind, useSetupChildProps, useScreenUtils } from '#imports'
import type { Or29224Type, TransmitParam } from '~/types/cmn/business/components/Or29224Type'
import type { Or05658Type } from '~/types/cmn/business/components/Or05658Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or05656OnewayType } from '~/types/cmn/business/components/Or05656Type'
import type { Or10355OnewayType, Or10355Type } from '~/types/cmn/business/components/Or10355Type'
import { UPDATE_KBN } from '~/constants/classification-constants'

/**
 * useI18n
 */
const { t } = useI18n()
/**
 * Or05658用ref
 */
const or05658 = ref({ uniqueCpId: '' })
/**
 * 画面状態管理用操作変数
 */
const or10355 = ref({ uniqueCpId: '' })
/**
 * Or05656用ref
 */
const or05656 = ref({ uniqueCpId: '' })
/**
 * or05657
 */
const or05657 = ref({ uniqueCpId: '' })
const { setChildCpBinds } = useScreenUtils()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or29224Type
  uniqueCpId: string
  parentUniqueCpId: string
}

/**
 * Propsの定義
 */
const props = defineProps<Props>()

/**
 * DataTableTypeをOr05656OnewayType形式に変換する（逆変換）
 *
 * @function transformFromDataTableType
 *
 * @description DataTableType形式からフラットなデータ形式に変換する
 *
 * @param item - 変換対象のDataTableType
 *
 * @returns 変換されたJyokyoSyosaiType
 */
function transformFromDataTableType(item: DataTableType): JyokyoSyosaiType {
  const { youinFlg, ...rest } = item

  const data = {
    ...rest,
    bikoKnj: item.bikoKnj?.value
      .replace(/\u2026/g, '')
      .replace(/\.\.\./g, '')
      .replace(/\r?\n/g, '')
      .trim(),
    joukyouFlg: item.joukyouFlg?.value,
    genzai1Flg: item.genzai1Flg?.value,
    youin1Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_1)
      ? Or05656Const.VARIABLE.STRING_1
      : Or05656Const.VARIABLE.STRING_0,
    youin2Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_2)
      ? Or05656Const.VARIABLE.STRING_2
      : Or05656Const.VARIABLE.STRING_0,
    youin3Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_3)
      ? Or05656Const.VARIABLE.STRING_3
      : Or05656Const.VARIABLE.STRING_0,
    youin4Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_4)
      ? Or05656Const.VARIABLE.STRING_4
      : Or05656Const.VARIABLE.STRING_0,
    youin5Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_5)
      ? Or05656Const.VARIABLE.STRING_5
      : Or05656Const.VARIABLE.STRING_0,
    youin6Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_6)
      ? Or05656Const.VARIABLE.STRING_6
      : Or05656Const.VARIABLE.STRING_0,
    updateKbn: item.updateKbn ?? '0',
    koumokuKnj: item.koumokuKnj?.value,
  }
  return data
}

/**
 * グループ化されたデータをフラットな配列に変換する（逆変換）
 *
 * @function ungroupItems
 *
 * @description 2次元配列のグループ化されたデータをフラットな1次元配列に変換する
 *
 * @param groupedItems - グループ化されたデータ配列
 *
 * @returns フラット化されたデータ配列
 */
function ungroupItems(groupedItems: Or05656OnewayType[][]): JyokyoSyosaiType[] {
  if (!groupedItems) return []

  const flatItems: JyokyoSyosaiType[] = []

  groupedItems.forEach((group) => {
    group.forEach((item) => {
      // 削除されていないアイテムのみを含める
      flatItems.push(transformFromDataTableType(item))
    })
  })

  // ソート順でソート
  return flatItems.sort((a, b) => (Number(a.sort) || 0) - (Number(b.sort) || 0))
}

/**
 * Or05656コンポーネントref
 */
const or05656Ref = ref<{
  init(): AsyncFunction
  createItem(): AsyncFunction
  deleteItem(): AsyncFunction
  moveUp(): AsyncFunction
  moveDown(): AsyncFunction
  selectedRowIndex: number
  totalItemCount: number
}>()

/**
 * useScreenTwoWayBind
 */
const { refValue } = useScreenTwoWayBind<Or29224Type>({
  cpId: Or29224Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or05658Const.CP_ID(0)]: or05658.value,
  [Or10355Const.CP_ID(0)]: or10355.value,
  [Or05656Const.CP_ID(0)]: or05656.value,
  [Or05657Const.CP_ID(0)]: or05657.value,
})

/**
 * ローカルのOnewayオブジェクト
 */
const localOneway = reactive({
  mo00009OnewayAssessment: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-assessment-import-screen'),
  } as Mo00009OnewayType,
  or28285Oneway: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-notes-screen'),
  } as Mo00009OnewayType,
  /**
   * Or10030用の単方向バインド値
   */
  or10355OnewayModel: {
    shisetuId: '',
    menu2Knj: '',
    menu3Knj: '',
    valS: '',
    kikanFlg: '',
  } as Or10355OnewayType,
})
/**
 * フォーム値のローカル状態
 */
const local = reactive({
  transmitParam: {
    executeFlag: '',
    deleteBtnValue: '',
    kikanKanriFlg: '',
    houjinId: '',
    shisetuId: '',
    userId: '',
    svJigyoId: '',
    kijunbiYmd: '',
    sakuseiId: '',
    historyModifiedCnt: '',
    historyInfo: {},
  } as TransmitParam,
  or05658: {} as Or05658Type,
  /**
   * Or10355コンポーネントの型参照（出力項目付き）
   */
  or10355Value: {
    raiId: '',
  } as Or10355Type,
})

watch(
  () => refValue.value,
  async (newVal) => {
    if (newVal) {
      local.or05658 = newVal.rirekiObj

      setChildCpBinds(props.uniqueCpId, {
        Or05658: {
          twoWayValue: local.or05658,
        },
      })
      const data = initGroupedItems(newVal.jyokyoSyosaiList)

      setChildCpBinds(props.uniqueCpId, {
        Or05656: {
          twoWayValue: data,
        },
      })
      await nextTick()
      or05656Ref.value?.init()
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

/**
 * 行追加ボタン
 */
const onAddItem = () => {
  or05656Ref.value?.createItem()
}
/**
 * 行削除ボタン
 */
const onDeleteItem = () => {
  or05656Ref.value?.deleteItem()
}

/**
 *  ダイアログ表示フラグ
 */
const showDialogOr10355 = computed(() => {
  // Or10355のダイアログ開閉状態
  return Or10355Logic.state.get(or10355.value.uniqueCpId)?.isOpen ?? false
})
/**
 *  ダイアログ表示フラグ
 */
const showDialogOr05657 = computed(() => {
  // Or05657のダイアログ開閉状態
  return Or05657Logic.state.get(or05657.value.uniqueCpId)?.isOpen ?? false
})
/**
 * データをkoumoku1Idでグループ化してソートする
 *
 * @function initGroupedItems
 *
 * @description JyokyoSyosaiTypeの配列をkoumoku1Idでグループ化し、ソートして返す
 *
 * @param value - 処理対象のデータ配列
 *
 * @returns グループ化されたデータ配列
 */
function initGroupedItems(value: JyokyoSyosaiType[]): Or05656OnewayType[][] {
  if (!value) return []

  // koumoku1Idでグループ化する
  const groups: Record<string, { sort: number; items: DataTableType[] }> = {}

  value.forEach((item: JyokyoSyosaiType) => {
    // データを変換してDataTableType形式にする
    const koumokuItem = transformToDataTableType(item)

    const key = item.koumoku1Id ?? ''
    const sortVal = Number(item.sort) || 0

    if (!groups[key]) {
      groups[key] = { sort: sortVal, items: [] }
    }
    groups[key].items.push(koumokuItem as DataTableType)
  })
  // グループをソートし、各グループ内のアイテムもソートする
  const data = Object.values(groups)
    .sort((a, b) => a.sort - b.sort)
    .map((group) =>
      group.items.slice().sort((a, b) => (Number(a.sort) || 0) - (Number(b.sort) || 0))
    )
  return data as Or05656OnewayType[][]
}
/**
 * Or05656OnewayTypeをDataTableType形式に変換する
 *
 * @function transformToDataTableType
 *
 * @description JyokyoSyosaiTypeをDataTableType形式に変換する
 *
 * @param item - 変換対象のJyokyoSyosaiType
 *
 * @returns 変換されたDataTableType
 */
function transformToDataTableType(item: JyokyoSyosaiType) {
  return {
    ...item,
    bikoKnj: { value: item.bikoKnj ?? '' },
    joukyouFlg: { value: item.joukyouFlg ?? '' },
    genzai1Flg: { value: item.genzai1Flg ?? '' },
    youinFlg: {
      values: [
        item.youin1Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_1
          : undefined,
        item.youin2Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_2
          : undefined,
        item.youin3Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_3
          : undefined,
        item.youin4Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_4
          : undefined,
        item.youin5Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_5
          : undefined,
        item.youin6Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_6
          : undefined,
      ].filter((value): value is string => value !== undefined),
    },
    updateKbn: UPDATE_KBN.NONE,
  }
}
defineExpose({
  ungroupItems,
  or05658: or05658.value.uniqueCpId,
  or05656: or05656.value.uniqueCpId,
})
</script>

<template>
  <c-v-row>
    <c-v-col
      class="mt-6"
      cols="12"
    >
      <g-custom-or-05658
        ref="or05658Ref"
        v-bind="or05658"
        :parent-unique-cp-id="props.uniqueCpId"
      />
    </c-v-col>
    <div class="line"></div>
    <c-v-col
      cols="12"
      class="px-3"
    >
      <div class="d-flex justify-space-between align-center">
        <div>
          <!-- 行追加ボタン -->
          <g-custom-or-26168 @click="onAddItem" />

          <!-- 削除ボタン -->
          <g-custom-or-26171
            class="ml-2"
            @click="onDeleteItem"
          />
        </div>
      </div>
    </c-v-col>
    <c-v-col
      cols="12"
      class="pt-1"
    >
      <g-custom-or-05656
        ref="or05656Ref"
        v-bind="or05656"
        :parent-unique-cp-id="props.uniqueCpId"
      />
    </c-v-col>
    <g-custom-or-10355
      v-if="showDialogOr10355"
      v-bind="or10355"
      v-model="local.or10355Value"
      :oneway-model-value="localOneway.or10355OnewayModel"
    />
    <!-- 注釈モーダル画面をポップアップで起動する。 -->
    <g-custom-or-05657
      v-if="showDialogOr05657"
      v-bind="or05657"
    />
  </c-v-row>
</template>

<style scoped lang="scss">
.line {
  width: 100%;
  height: 1px;
  background-color: #b4c5dc;
  margin-top: 12px;
  margin-bottom: 6px;
}
</style>
