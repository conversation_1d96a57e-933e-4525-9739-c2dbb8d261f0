/**
 * GUI01235_評価表
 *
 * @description
 * 評価表
 *
 * <AUTHOR>
 */

import type { InWebEntity, OutWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 計画期間変更リクエストパラメータタイプ
 */
export interface EvaluationTablePlanPeriodSelectInEntity extends InWebEntity {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 計画期間ページ区分
   */
  planPeriodPageCategory: string
  /**
   * 期間管理フラグ
   */
  kikanKanriFlg: string
  /**
   * 初期フラグ
   */
  initialFlg: string
  /**
   * 職員ID
   */
  shokuinId: string
  /**
   * アセスメント方式
   */
  cpnFlg: string
  /**
   * 計画書書式
   */
  shosikiFlg: string
  /**
   * メニュー２名称
   */
  menu2Name: string
  /**
   * メニュー３名称
   */
  menu3Name: string
  /**
   * 自由パラメータ
   */
  freeParameter: string
  /**
   * 事業者IDリスト
   */
  svJigyoIdList: string
  /**
   * システムコード
   */
  gsyscd: string
  /**
   * ヘーダID
   */
  cmoni1Id: string
}

/**
 * 計画期間変更レスポンスパラメータタイプ
 */
export interface EvaluationTablePlanPeriodSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 計画期間情報
     */
    planPeriodInfo: {
      /**
       * 期間ID
       */
      sc1Id: string
      /**
       * 期間総件数
       */
      periodCnt: string
      /**
       * 期間番号
       */
      periodNo: string
      /**
       * 開始日
       */
      startYmd: string
      /**
       * 終了日
       */
      endYmd: string
    }
    /**
     * 履歴情報
     */
    historyInfo: {
      /**
       * マスタヘッダID
       */
      cmoni1Id: string
      /**
       * 計画期間ID
       */
      sc1Id: string
      /**
       * 作成日
       */
      createYmd: string
      /**
       * 作成者
       */
      shokuId: string
      /**
       * 作成者名
       */
      shokuinKnj: string
      /**
       * 履歴総件数
       */
      krirekiCnt: string
      /**
       * 履歴番号
       */
      krirekiNo: string
      /**
       * 更新回数
       */
      modifiedCnt: string
    }
    /**
     * 種別ID
     */
    syubetsuId: string
    /**
     * 期間管理フラグ
     */
    kikanFlg: string
    /**
     * ケース権限
     */
    caseAuthority: string
    /**
     * 実施計画～①権限
     */
    implementationPlan1Authority: string
    /**
     * 実施計画～②権限
     */
    implementationPlan2Authority: string
    /**
     * 実施計画～③権限
     */
    implementationPlan3Authority: string
    /**
     * 計画書権限
     */
    planAuthority: string
    /**
     * 実施ケース権限
     */
    implementationCaseAuthority: string
  }
}

/**
 * 歴史変更リクエストパラメータタイプ
 */
export interface EvaluationTableHistorySelectInEntity extends InWebEntity {
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userId: string
  /** 期間ID */
  sc1Id: string
  /** ヘッダID */
  cmoni1Id: string
  /** 履歴ページ区分 */
  histPageCategory: string
}

/**
 * 歴史変更レスポンスパラメータタイプ
 */
export interface EvaluationTableHistorySelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    historyInfo: {
      /**
       * ヘッダID
       */
      cmoni1Id: string
      /**
       * 計画期間ID
       */
      sc1Id: string
      /**
       * 作成日
       */
      createYmd: string
      /**
       * 作成者
       */
      shokuId: string
      /**
       * 作成者名
       */
      shokuinKnj: string
      /**
       * 履歴総件数
       */
      krirekiCnt: string
      /**
       * 履歴番号
       */
      krirekiNo: string
      /**
       * 更新回数
       */
      modifiedCnt: string
    }
  }
}

/**
 * 複写データ取得リクエストパラメータタイプ
 */
export interface EvaluationTableDuplicateSelectInEntity extends InWebEntity {
  /**
   * ヘッダID
   */
  cmoni1Id: string
  /**
   * マスタID
   */
  kbnId: string
}

/**
 * 複写データ取得レスポンスパラメータタイプ
 */
export interface EvaluationTableDuplicateSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 評価表上段項目数
     */
    columnCount: string
    /**
     * 評価表上段情報リスト
     */
    evaluationTableUpperTierInfoList: {
      /**
       * インデックス
       */
      [key: string]: string
      /**
       * データID
       */
      cmoni3Id: string
      /**
       * 文章1
       */
      koumoku01Knj: string
      /**
       * 文章2
       */
      koumoku02Knj: string
      /**
       * 文章3
       */
      koumoku03Knj: string
      /**
       * 文章4
       */
      koumoku04Knj: string
      /**
       * 文章5
       */
      koumoku05Knj: string
      /**
       * 文章6
       */
      koumoku06Knj: string
      /**
       * 文章7
       */
      koumoku07Knj: string
      /**
       * 文章8
       */
      koumoku08Knj: string
      /**
       * 文章9
       */
      koumoku09Knj: string
      /**
       * 文章10
       */
      koumoku10Knj: string
      /**
       * 文章11
       */
      koumoku11Knj: string
      /**
       * 文章12
       */
      koumoku12Knj: string
      /**
       * 文章13
       */
      koumoku13Knj: string
      /**
       * 文章14
       */
      koumoku14Knj: string
      /**
       * 文章15
       */
      koumoku15Knj: string
      /**
       * コード1
       */
      koumoku01Cod: string
      /**
       * コード2
       */
      koumoku02Cod: string
      /**
       * コード3
       */
      koumoku03Cod: string
      /**
       * コード4
       */
      koumoku04Cod: string
      /**
       * コード5
       */
      koumoku05Cod: string
      /**
       * コード6
       */
      koumoku06Cod: string
      /**
       * コード7
       */
      koumoku07Cod: string
      /**
       * コード8
       */
      koumoku08Cod: string
      /**
       * コード9
       */
      koumoku09Cod: string
      /**
       * コード10
       */
      koumoku10Cod: string
      /**
       * コード11
       */
      koumoku11Cod: string
      /**
       * コード12
       */
      koumoku12Cod: string
      /**
       * コード13
       */
      koumoku13Cod: string
      /**
       * コード14
       */
      koumoku14Cod: string
      /**
       * コード15
       */
      koumoku15Cod: string
      /**
       * 日付1
       */
      koumoku01Ymd: string
      /**
       * 日付2
       */
      koumoku02Ymd: string
      /**
       * 日付3
       */
      koumoku03Ymd: string
      /**
       * 日付4
       */
      koumoku04Ymd: string
      /**
       * 日付5
       */
      koumoku05Ymd: string
      /**
       * 日付6
       */
      koumoku06Ymd: string
      /**
       * 日付7
       */
      koumoku07Ymd: string
      /**
       * 日付8
       */
      koumoku08Ymd: string
      /**
       * 日付9
       */
      koumoku09Ymd: string
      /**
       * 日付10
       */
      koumoku10Ymd: string
      /**
       * 日付11
       */
      koumoku11Ymd: string
      /**
       * 日付12
       */
      koumoku12Ymd: string
      /**
       * 日付13
       */
      koumoku13Ymd: string
      /**
       * 日付14
       */
      koumoku14Ymd: string
      /**
       * 日付15
       */
      koumoku15Ymd: string
      /**
       * 更新回数
       */
      modifiedCnt: string
    }[]
    /**
     * 評価表下段情報
     */
    evaluationTableLowerTierInfo: {
      /**
       * 評価表下段項目数
       */
      columnCount2: string
      /**
       * 文章1
       */
      koumoku01Knj: string
      /**
       * 文章2
       */
      koumoku02Knj: string
      /**
       * 文章3
       */
      koumoku03Knj: string
    }
  }
}
