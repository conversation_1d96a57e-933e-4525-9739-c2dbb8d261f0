import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or30341:有機体:アセスメント(インターライ)画面I-1
 * GUI00772_アセスメント(インターライ)画面I-1
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or30341Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or30341', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * 本文末に追加
     */
    export const ADD_END_TEXT_IMPORT_TYPE = '0'
    /**
     * 本文上書
     */
    export const OVERWRITE_TEXT_IMPORT_TYPE = '1'
  }
  /**
   * テーブル名
   */
  export const TABLENAME = 'cpn_tuc_rai_ass_i'
  /**
   * カラム名
   */
  export const COLUMNNAME = 'i1_memo_knj'
  /**
   * 画面ID
   */
  export const SCREENID = 'GUI00772'
  /**
   * 大分類ＣＤ
   */
  export const T1CD = '610'
  /**
   * 大分類ＣＤ
   */
  export const T2CD = '9'

  /**
   * 大分類ＣＤ
   */
  export const T3CD = '0'
  /**
   * インデックス
   */
  export const INDEX = '10'
  /**
   * タブ名
   */
  export const TABNAME = 'I'
  /**
   * サブ区分
   */
  export const TABKBN_1 = 'I1'
}
