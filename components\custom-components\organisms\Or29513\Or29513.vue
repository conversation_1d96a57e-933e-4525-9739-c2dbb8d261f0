<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or29513Const } from '../Or29513/Or29513.constants'
import { Or51773Const } from '../Or51773/Or51773.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import type {
  RirekiInfo,
  InfoCollectionInfoType,
} from '~/types/cmn/business/components/TeX0005Type'
import type { Or29513OnewayType, Or29513Type } from '~/types/cmn/business/components/Or29513Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import { useScreenTwoWayBind, useSetupChildProps, useValidation } from '#imports'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { OrX0163OnewayType } from '~/types/cmn/business/components/OrX0163Type'
const { byteLength } = useValidation()

/**
 * Or29513：有機体：GUI00673_［情報収集］画面（1）画面
 *
 * @description
 * ［情報収集］画面（1）
 *
 * <AUTHOR>
 */

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or29513OnewayType
  uniqueCpId: string
  modelValue: Or29513Type
}

const props: Props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/

const { t } = useI18n()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(1) })
const contentRef = ref<HTMLDivElement | null>(null)
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Record<string, InfoCollectionInfoType[]>>({
  cpId: Or29513Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
})

const defaultOnewayModelValue: Or29513OnewayType = {
  periodManageFlag: '0',
  rirekiInfo: {} as RirekiInfo,
  copyParentId: '',
}

const localOneway = reactive({
  or29513Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // アセスメント表タイトル
  mo01338Oneway: {
    value: '',
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentTitle',
      itemStyle: 'font-size: 18px',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 全て[〇]コンポーネント
  mo00611OnewayAllRound: {
    btnLabel: t('btn.all-round-figma'),
    width: '86px',
    height: '29px',
  },
  // 全て[×]コンポーネント
  mo00611OnewayAllWrong: {
    btnLabel: t('btn.all-wrong-figma'),
    width: '86px',
    height: '29px',
  },
  // 全解除コンポーネント
  mo00611OnewayCancelAll: {
    btnLabel: t('btn.full-release'),
    width: '86px',
    height: '29px',
  },

  //1行入力最大文字数：
  //情報収集画面詳細情報の第３階層書式１が「3」以外の場合、26
  //情報収集画面詳細情報の第３階層書式１が「3」の場合、44
  // ******Visioラジオグループセクション******
  cnsiderOneway: {
    customClass: new CustomClass({ outerClass: 'radio-style' }),
    showItemLabel: false,
    itemLabel: '',
    name: 'body',
    inline: false,
    items: [],
  } as Mo00039OnewayType,
  or51775OnewayTypeOther: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '1',
    t3Cd: '',
    tableName: 'cpn_tuc_myg_ass2',
    columnName: '',
    assessmentMethod: '2',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
  orX0163Oneway: {
    height: '75px',
    readOnly: false,
    maxlength: '4000',
    rules: [byteLength(4000)],
    contentStyle:
      'padding: 11.5px 11.5px;line-height: 17px;letter-spacing: -0.04em;font-weight: 400;font-style: normal;',
  } as OrX0163OnewayType,
})

const local = reactive({
  or29513: {
    ...props.modelValue,
  } as Or29513Type,
  //期間管理フラグが「1:管理する」、かつ、計画期間が登録されていない場合
  disableFlg: false,
  // 計画期間登録有無
  hasKikanObj: true,
  // ソート後の画面データ
  sortList: {} as Record<string, Record<string, InfoCollectionInfoType[]>>,
})

/**
 *  ラジオボタン初期化
 */
function initCodes() {
  localOneway.cnsiderOneway.items?.push({
    label: t('label.wrong'),
    value: Or29513Const.DEFAULT.WRONGSELECTED,
  })
  localOneway.cnsiderOneway.items?.push({
    label: t('label.circle'),
    value: Or29513Const.DEFAULT.RIGHTSELECTED,
  })
}

/**************************************************
 * Emit
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  // 初期情報取得
  init()

  //期間管理フラグが「1:管理する」の場合登録されていない場合
  if (props.onewayModelValue.periodManageFlag === Or29513Const.DEFAULT.MANAGE_FLG_ONE) {
    local.disableFlg = true
  }
})

/**
 * 初期化
 */
const init = () => {
  // ラジオボタン初期化
  void initCodes()
  // 情報収集画面の情報を取得する。
  groupByLevel2IdAndKoumokuNo({ ...refValue.value })
}

// 書式2のデータをグループ化する
function groupByLevel2IdAndKoumokuNo(infoList: Record<string, InfoCollectionInfoType[]>) {
  const retList = Object.values(infoList).flat()
  // 階層1タイトルラベル(情報収集画面詳細情報の「第1階層ID」の値、情報収集画面詳細情報の「第1階層名称」の値の組合文字列)
  if (retList !== null && retList.length > 0) {
    localOneway.mo01338Oneway.value = retList[0].level1Id + '　' + retList[0].level1Knj
    local.sortList = retList.reduce(
      (acc, current) => {
        const level2Key = current.level2Id
        const koumokuKey = Number(current.koumokuNo)

        if (!acc[level2Key]) {
          acc[level2Key] = {} as Record<string, InfoCollectionInfoType[]>
        }
        if (!acc[level2Key][koumokuKey]) {
          acc[level2Key][koumokuKey] = []
        }
        acc[level2Key][koumokuKey].push(current)
        return acc
      },
      {} as Record<string, Record<string, InfoCollectionInfoType[]>>
    )
  }
}

function getFirstLevel2Knj(group: Record<string, InfoCollectionInfoType[]>) {
  const firstKey = Object.keys(group)[0]
  const firstArray = group[firstKey]

  if (firstArray && firstArray.length > 0) {
    return firstArray[0].level2Knj
  }
  return ''
}

/**
 * 全て[〇]ボタン押下
 *
 */
function onAllSelect1Click() {
  for (const level2Id in local.sortList) {
    for (const koumokuNo in local.sortList[level2Id]) {
      local.sortList[level2Id][koumokuNo].forEach((item) => {
        item.kentoFlg = Or29513Const.DEFAULT.RIGHTSELECTED
      })
    }
  }
}

/**
 * 全て[×]ボタン押下
 *
 */
function onAllSelect2Click() {
  for (const level2Id in local.sortList) {
    for (const koumokuNo in local.sortList[level2Id]) {
      local.sortList[level2Id][koumokuNo].forEach((item) => {
        item.kentoFlg = Or29513Const.DEFAULT.WRONGSELECTED
      })
    }
  }
}

/**
 * 全解除ボタン押下
 *
 */
function onNoSelectClick() {
  for (const level2Id in local.sortList) {
    for (const koumokuNo in local.sortList[level2Id]) {
      local.sortList[level2Id][koumokuNo].forEach((item) => {
        item.kentoFlg = Or29513Const.DEFAULT.UNSELECTED
      })
    }
  }
}

//その他1課題t入力
const or51775Other = ref({ modelValue: '' })

// 選択した行の第１階層Index
const selectedItemIndex = ref<string>('')

// 選択した行の第２階層Index
const selectedItemSubIndex = ref<number>(-1)

// 選択した行の番号Index
const selectedItemKoumokuIndex = ref<number>(-1)

/**
 * 「ケアマネ入力支援アイコンボタン」押下onClickOther
 *
 * @param title - id
 *
 * @param index - 第１階層Index
 *
 * @param subIndex - 第２階層Index
 *
 * @param koumokuIndex - 番号Index
 */
function onClickOther(
  title: string,
  index: string,
  subIndex: string,
  koumokuIndex: number
) {
  // その他1の入力支援ポップアップを開く
  or51775Other.value.modelValue = ''

  selectedItemIndex.value = index
  selectedItemSubIndex.value = Number(subIndex)
  selectedItemKoumokuIndex.value = koumokuIndex

  // タイトルを「その他1」または「その他2」に設定
  localOneway.or51775OnewayTypeOther.title = title

  // 入力支援ポップアップを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or51773Const.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or51773Const.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  // メモ１の場合
  if (
    local.sortList[selectedItemIndex.value][selectedItemSubIndex.value][
      selectedItemKoumokuIndex.value
    ].memo1Knj
  ) {
    local.sortList[selectedItemIndex.value][selectedItemSubIndex.value][
      selectedItemKoumokuIndex.value
    ].memo1Knj.value = setOrAppendValue(
      local.sortList[selectedItemIndex.value][selectedItemSubIndex.value][
        selectedItemKoumokuIndex.value
      ].memo1Knj.value ?? '',
      data
    )
  }

  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

watch(
  () => refValue.value,
  () => {
    groupByLevel2IdAndKoumokuNo({ ...refValue.value })
  },
  { deep: true }
)
/**
 * スクロールバーのスクロール
 *
 * @param event - WheelEvent
 */
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()

  const delta = Math.sign(event.deltaY)

  const contentElement = contentRef.value
  if (!contentElement) return

  const currentScroll = contentElement.scrollTop
  const maxScroll = contentElement.scrollHeight - contentElement.clientHeight

  let newScroll = currentScroll + delta * 50

  if (newScroll < 0) newScroll = 0
  if (newScroll > maxScroll) newScroll = maxScroll

  if (newScroll !== currentScroll) {
    contentElement.scrollTo({
      top: newScroll,
      behavior: 'auto',
    })
  }
}
</script>
<template>
  <c-v-row
    v-show="
      props.onewayModelValue.periodManageFlag !== Or29513Const.PLANNING_PERIOD_NO_MANAGE ||
      props.onewayModelValue.copyFlg === true
    "
    no-gutters
    style="padding: 8px"
  >
    <c-v-col
      cols="6"
      class="h-100 px-2"
    >
      <c-v-row
        no-gutters
        style="padding-top: 8px">
        <base-mo01338
          :oneway-model-value="localOneway.mo01338Oneway"
          style="background: transparent"
        ></base-mo01338>
      </c-v-row>
    </c-v-col>
    <!-- 3ボタン -->
    <c-v-col
      cols="6"
      class="h-100 px-2"
    >
      <c-v-row
        no-gutters
        class="top-button"
        justify="end"
      >
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAllRound"
          class="mx-1"
          @click="onAllSelect1Click"
        />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayAllWrong"
          class="mx-1"
          @click="onAllSelect2Click"
        />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayCancelAll"
          class="mx-1"
          @click="onNoSelectClick"
        />
      </c-v-row>
    </c-v-col>
    <!-- タイトル -->
    <c-v-row no-gutters>
      <c-v-col class="h-100">
        <!-- テーブル固定へーた -->
        <c-v-row
          no-gutters
          class="row-tl-border"
          style="width: calc(100% - 15px);height: 32px;"
        >
          <!-- No -->
          <c-v-col
            cols="auto"
            class="col-br-border align-items-center-title tbl-title-bg col1"
            ><label class="label-style">{{ t('label.no') }}</label></c-v-col
          >
          <!-- 情報項目 -->
          <c-v-col class="col-br-border align-items-center-title tbl-title-bg col2"
            ><label class="label-style">{{ t('label.info-collection-info-item') }}</label></c-v-col
          >
          <!-- 計画書様式が【居宅】、改定区分が【初版】の場合 -->
          <!-- 検討 -->
          <c-v-col
            v-if="
              props.onewayModelValue.rirekiInfo &&
              props.onewayModelValue.rirekiInfo.kaiteiKbn ===
                Or29513Const.KAITEI_KBN_0
            "
            cols="auto"
            class="col-br-border tbl-title-bg align-items-center-title col4"
            ><label class="label-style">{{ t('label.info-collection-consider') }}</label></c-v-col
          >
          <!-- 具体的状況 -->
          <c-v-col
            cols="auto"
            class="col-br-border tbl-title-bg align-items-center-title col3"
            ><label class="label-style">{{
              t('label.info-collection-concrete-situation')
            }}</label></c-v-col
          >
          <!-- 検討 -->
          <c-v-col
            v-if="
              !props.onewayModelValue.rirekiInfo ||
              (props.onewayModelValue.rirekiInfo &&
              props.onewayModelValue.rirekiInfo.kaiteiKbn !== Or29513Const.KAITEI_KBN_0)
            "
            cols="auto"
            style="text-align: center"
            class="col-br-border tbl-title-bg align-items-center-title col4"
            ><label class="label-style">{{ t('label.info-collection-consider') }}</label></c-v-col
          >
        </c-v-row>
        <!-- 一覧 -->
        <div
          ref="contentRef"
          class="or33109-main-div"
        >
          <template
            v-for="(group, index) in local.sortList!"
            :key="index"
          >
            <div
              v-if="!props.onewayModelValue.deleteFlg"
              no-gutters
              class="row-l-border"
            >
              <c-v-row no-gutters>
                <c-v-col
                  cols="12"
                  style="height: 32px;"
                  class="col-br-border tbl-title-bg align-items-center label-style font-bold"
                ><div class="pl-3">{{ getFirstLevel2Knj(group) }}</div>
                </c-v-col>
              </c-v-row>
              <c-v-row
                v-for="(item, subIndex) in group"
                :key="subIndex"
                no-gutters
                class="or33109-row"
              >
                <!-- No -->
                 <c-v-col
                  v-show="item[0].shosiki1Flg !== Or29513Const.DEFAULT.FORMAT_THREE"
                  cols="auto"
                  class="col-br-border align-items-center col1 text-bg"
                  style="justify-content: center"
                  ><label
                    class="label-style"
                    >{{ item[0].koumokuNo }}</label
                  >
                </c-v-col>
                <div class="body-content">
                <div
                    v-for="(koumokuItem, koumokuIndex) in item"
                    :key="koumokuIndex"
                    class="body-item">

                <!-- 情報項目 -->
                <c-v-col
                  v-show="koumokuItem.shosiki1Flg !== Or29513Const.DEFAULT.FORMAT_THREE"
                  cols="auto"
                  class="col-br-border align-items-center col22 text-bg pa-0"
                ><div
                    class="pl-3 label-style" style="width: 213px"
                    >{{ koumokuItem.level3Knj }}</div
                  >
                </c-v-col>
                <!--検討1ラジオボタン-->
                <!--改訂区分が「1：H31/1」の場合、非表示-->
                <c-v-col
                  v-show="
                    props.onewayModelValue.rirekiInfo &&
                    props.onewayModelValue.rirekiInfo.kaiteiKbn ===
                      Or29513Const.KAITEI_KBN_0 &&
                    koumokuItem.shosiki1Flg !== Or29513Const.DEFAULT.FORMAT_THREE
                  "
                  cols="auto"
                  class="col-br-border radio-center text-area-bg col44 pa-0"
                  ><div class="radio1">
                    <base-mo00039
                      v-model="koumokuItem.kentoFlg"
                      style="background: inherit"
                      :oneway-model-value="localOneway.cnsiderOneway"
                    /></div
                ></c-v-col>
                <c-v-col
                  v-show="koumokuItem.shosiki1Flg === Or29513Const.DEFAULT.FORMAT_THREE"
                  cols="auto"
                  class="col-br-border align-items-center col_item_3 text-bg"
                  ><div class="pl-1 label-style" style="width: 213px">{{ koumokuItem.level3Knj }}</div></c-v-col
                >
                <!-- 具体的状況 -->
                <c-v-col
                  v-show="koumokuItem.shosiki1Flg !== Or29513Const.DEFAULT.FORMAT_THREE"
                  cols="auto"
                  class="col-br-border text-area-bg col33 pa-0">
                  <div style="display: flex">
                    <g-custom-or-x-0163
                      v-model="koumokuItem.memo1Knj"
                      :oneway-model-value="localOneway.orX0163Oneway"
                      @on-click-edit-btn="onClickOther(koumokuItem.level3Knj, index, subIndex,koumokuIndex)"
                    ></g-custom-or-x-0163>
                  </div
                ></c-v-col>
                <c-v-col
                  v-show="koumokuItem.shosiki1Flg === Or29513Const.DEFAULT.FORMAT_THREE"
                  cols="auto"
                  class="col-br-border text-area-bg col_status_3 pa-0"
                >
                  <div style="display: flex">
                    <g-custom-or-x-0163
                      v-model="koumokuItem.memo1Knj"
                      :oneway-model-value="localOneway.orX0163Oneway"
                      @on-click-edit-btn="onClickOther(koumokuItem.level3Knj, index, subIndex,koumokuIndex)"
                    ></g-custom-or-x-0163>
                  </div>
                </c-v-col>
                <!--検討2ラジオボタン-->
                <!--・改訂区分が「0：初版」の場合、非表示-->
                <c-v-col
                  v-show="
                    props.onewayModelValue.rirekiInfo &&
                    props.onewayModelValue.rirekiInfo.kaiteiKbn !==
                      Or29513Const.KAITEI_KBN_0 &&
                    koumokuItem.shosiki1Flg !== Or29513Const.DEFAULT.FORMAT_THREE
                  "
                  cols="auto"
                  class="col-br-border radio-center text-area-bg col44 pa-0"
                  ><div class="radio1 pl-1">
                    <base-mo00039
                      v-model="koumokuItem.kentoFlg"
                      style="background: inherit"
                      :oneway-model-value="localOneway.cnsiderOneway"
                    /></div
                ></c-v-col>
                </div>
                </div>
              </c-v-row>
            </div>
          </template>
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-row>
  <div
    v-if="props.onewayModelValue.copyFlg === true"
    class="overlay"
    @wheel="handleWheel"
  ></div>
  <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Other"
    :oneway-model-value="localOneway.or51775OnewayTypeOther"
    @confirm="handleOr51775Confirm"
  ></g-custom-or-51775>
</template>

<style lang="scss" scoped>
@use '@/styles/base-data-table-list.scss';

:deep(.ml-4) {
  margin-left: 0px !important;
}

:deep(.v-selection-control-group--inline) {
  flex-direction: column !important;
}

:deep(.v-selection-control--density-default) {
  --v-selection-control-size: 36px !important;
}

.radio1 {
  :deep(.v-selection-control-group > div) {
    margin-bottom: -6px;
    margin-top: -8px;
    margin-right: 10px !important;
  }
}
:deep(.v-selection-control-group) {
  flex-direction: row-reverse !important;
}
.contentTitle {
  margin-bottom: 18px !important;
}

.row-tl-border {
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.row-l-border {
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-br-border {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}

.col-pl {
  padding-left: 4px !important;
}

.tbl-title-bg {
  background-color: #dbeefe;
}

.top-button {
  padding-bottom: 16px;
  padding-right: 3px;
}

.font-bold {
  font-weight: bold !important;
}

.align-items-center {
  display: flex;
  align-items: center;
}

.align-items-center-title {
  display: flex;
  align-items: center;
  justify-content: center;
}
.radio-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.or33109-main-div {
  overflow-y: hidden;
  // max-height: 490px;
  scrollbar-gutter: stable;
  position: relative;
  z-index: 999;
}

.or33109-row {
  flex-wrap: nowrap;
}

.or33109-title-row {
  height: 21px;
}

:deep(.col1) {
  width: 4%;
  flex-shrink: 0;
}

:deep(.col2) {
  width: 28%;
  flex-shrink: 0;
}

:deep(.col22) {
  width: 29.17%;
  flex-shrink: 0;
}

:deep(.col15) {
  width: 26%;
  flex-shrink: 0;
}

:deep(.col3) {
  width: 55%;
  flex-shrink: 0;
}
:deep(.col33) {
  width: 57.33%;
  flex-shrink: 0;
}

:deep(.col4) {
  width: 13%;
  flex-shrink: 0;
}
:deep(.col44) {
  width: 13.54%;
  flex-shrink: 0;
}
:deep(.col_item_3) {
  width: 32%;
  flex-shrink: 0;
}

:deep(.col_status_3) {
  width: 68%;
  flex-shrink: 0;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 1000;
  pointer-events: auto;
  cursor: not-allowed;
}
.body-content{
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.body-item{
  display: flex;
  width: 100%;
  height: 76px;
}
.text-bg {
  background-color: rgb(219, 238, 254);
}
.text-area-bg {
  background-color: rgb(255, 255, 255);
}
.full-width-field {
  padding: 0 3px !important;
}

.label-style {
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 16px;
  letter-spacing: -0.04em;
}
</style>
