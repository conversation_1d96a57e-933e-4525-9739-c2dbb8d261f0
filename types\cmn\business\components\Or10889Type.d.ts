/**
 * Or10889:有機体:薬剤検索マスタ
 * GUI00670_薬剤検索マスタ
 *
 * 単方向バインドのデータ構造
 */
export interface Or10889OnewayType {
  /**
   * 職員ID
   */
  staffId: string
}

/**
 * 双方向バインドのデータ構造
 */
export interface Or10889Type {
  /**
   * 薬剤id
   */
  drugId: string
}

/**
 * 各一覧明細情報取得
 */
export interface RespDataInfoType {
  /**
   * 薬剤分類マスタ情報リスト
   */
  drugClassificationMasterInfoList: DrugClassificationMasterInfo[]
  /**
   * 薬剤マスタ情報リスト
   */
  drugMasterInfoList: DrugMasterInfo[]
  /**
   * 初期設定マスタ情報リスト
   */
  initialSetupMasterInfoList: InitialSetupMasterInfo[]
}

/**
 * 初期設定マスタ情報
 */
export interface InitialSetupMasterInfo {
  /**
   *  連番
   */
  recNo: string
  /**
   *  最大薬剤階層
   */
  maxDrugLevel: string
  /**
   *  最大文章階層
   */
  maxBunshoLevel: string
  /**
   *  食事タイプ
   */
  shokujiKbn: string
}


/**
 * 薬剤分類マスタ情報
 */
export interface DrugClassificationMasterInfo {
  /**
   * 階層
   */
  level: string
  /**
   * 親分類id
   */
  parentId: string
  /**
   * 薬剤分類id
   */
  bunruiId: string
  /**
   * 薬剤分類名
   */
  drugBunKnj?: string
  /**
   * 表示順
   */
  sort: string
  /**
   * 項目の子の配列
   */
  children?: DrugClassificationMasterInfo[]
}

/**
 * 薬剤マスタ情報
 */
export interface DrugMasterInfo {
  /**
   * 階層
   */
  level: string
  /**
   * 親分類id
   */
  parentId: string
  /**
   * 分類コード
   */
  bunruiId: string
  /**
   * 薬剤id
   */
  drugId: string
  /**
   * 医薬品名
   */
  drugKnj?: string
  /**
   * 医薬品カナ
   */
  drugKana?: string
  /**
   * 医薬品コード
   */
  drugCode?: string
  /**
   * 医薬品Rezeコード
   */
  drugReceCode?: string
  /**
   * 剤型
   */
  shape?: string
  /**
   * 作用・効能等
   */
  memoKnj?: string
  /**
   * 表示順
   */
  sort: string
}
