<script setup lang="ts">
import { computed, ref, reactive } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or28333Const } from '~/components/custom-components/organisms/Or28333/Or28333.constants'
import { Or28333Logic } from '~/components/custom-components/organisms/Or28333/Or28333.logic'
import type { Or28333Type, Or28333OnewayType } from '~/types/cmn/business/components/Or28333Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00641'
// ルーティング
const routing = 'GUI00641/pinia'
// 画面物理名
const screenName = 'GUI00641'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or28333 = ref({ uniqueCpId: Or28333Const.CP_ID(0) })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00641' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or28333Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or28333.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00641',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or28333Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or28333Const.CP_ID(1)]: or28333.value,
})

// ダイアログ表示フラグ
const showDialogOr28333 = computed(() => {
  // Or28333のダイアログ開閉状態
  return Or28333Logic.state.get(or28333.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or28333)
 */
function onClickOr28333() {
  or28333Data.userId = '1'
  or28333Data.botanName = 'b_kenko_hoken_2'
  // Or28333のダイアログ開閉状態を更新する
  Or28333Logic.state.set({
    uniqueCpId: or28333.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or28333Type = ref<Or28333Type>({
  /** 保険名称*/
  nameknj: '',
  /** 保険者番号*/
  publicno: '',
  /** 記号・番号*/
  kigonoKnj: '',
  /** 有効期間開始日*/
  startdateYmd: '',
  /** 有効期間終了日*/
  enddateYmd: '',
})

const or28333Data: Or28333OnewayType = {
  /** 利用者ID */
  userId: '',
  /** ボタン名 */
  botanName: '',
}

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType

const local = reactive({
  // 利用者ID
  userId: { value: '1' } as Mo00045Type,
  // ボタン名
  botanName: { value: 'b_kenko_hoken_2' } as Mo00045Type,
})

/** GUI00641 疎通起動  */
function onClickOr00641() {
  or28333Data.userId = local.userId.value
  or28333Data.botanName = local.botanName.value
  // Or28349のダイアログ開閉状態を更新する
  Or28333Logic.state.set({
    uniqueCpId: or28333.value.uniqueCpId,
    state: { isOpen: true },
  })
}


</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr28333"
        >GUI00641_［保険選択］画面
      </v-btn>
      <g-custom-or-28333
        v-if="showDialogOr28333"
        v-bind="or28333"
        v-model="or28333Type"
        :oneway-model-value="or28333Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn variant="plain"
        >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
      </v-btn>
    </c-v-col>
  </c-v-row>
  <div style="margin-left: 20px">利用者ID</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.userId"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div style="margin-left: 20px">ボタン名</div>
  <div style="margin-left: 20px; width: 200px">
    <base-mo00045
      v-model="local.botanName"
      :oneway-model-value="mo00045OneWay"
    ></base-mo00045>
  </div>
  <div class="pt-5 w-25 pl-5">
    <v-btn @click="onClickOr00641"> GUI00641 疎通起動 </v-btn>
  </div>
  <div class="pt-5 pl-5">
    return ----- data

    <div>
      {{ or28333Type }}
    </div>
  </div>
</template>
