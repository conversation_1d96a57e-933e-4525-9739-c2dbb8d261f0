<script setup lang="ts">
/**
 * GUI00895:有機体:印刷設定
 * GUI00895_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or58240Const } from '~/components/custom-components/organisms/Or58240/Or58240.constants'
import { Or58240Logic } from '~/components/custom-components/organisms/Or58240/Or58240.logic'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00895'
// ルーティング
const routing = 'GUI00895/pinia'
// 画面物理名
const screenName = 'GUI00895'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or58240 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00895' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or58240.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00895',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or58240Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or58240Const.CP_ID(1)]: or58240.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or58240Logic.initialize(or58240.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr58240 = computed(() => {
  // Or58240のダイアログ開閉状態
  return Or58240Logic.state.get(or58240.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or58240)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr58240() {
  // Or58240のダイアログ開閉状態を更新する
  Or58240Logic.state.set({
    uniqueCpId: or58240.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        /**
         * 処理年月日
         */
        processYmd: '2024/11/11',
        /**
         * 基準日
         */
        basicDate: '2024/10/11',
        /**
         * セクション名
         */
        sectionName: 'フリーアセスメントチェック項目',
        /**
         * 施設ID
         */
        shisetuId: '1',
        /**
         * 事業者ID
         */
        svJigyoId: '1',
        /**
         * 利用者ID
         */
        userId: '41',
        /**
         * 履歴ID
         */
        assId: '2',
        /**
         * 担当者ID
         */
        tantoId: '2',
        /**
         * フォーカス設定用イニシャル
         */
        focusSettingInitial: ['や', 'ゆ', 'よ'],
        /**
         * 担当者カウンタ値
         */
        selectedUserCounter: '2',
      },
    },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr58240"
        >GUI00895_印刷設定
      </v-btn>
      <g-custom-or-58240
        v-if="showDialogOr58240"
        v-bind="or58240"
      />
    </c-v-col>
  </c-v-row>
</template>
