<script setup lang="ts">
/**
 * Or57082:有機体:印刷設定
 * GUI00815_印刷設定
 *
 * @description
 * （アセスメント）印刷設定モーダル
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  watch,
} from '#imports'
import { Or57082Const } from '~/components/custom-components/organisms/Or57082/Or57082.constants'
import { Or57082Logic } from '~/components/custom-components/organisms/Or57082/Or57082.logic'
import type { Or57082Param } from '~/components/custom-components/organisms/Or57082/Or57082.type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import { CustomClass } from '~/types/CustomClassType'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00815'
// ルーティング
const routing = 'GUI00815/pinia'
// 画面物理名
const screenName = 'GUI00815'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or57082 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00815' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or57082.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00815',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or57082Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or57082Const.CP_ID(1)]: or57082.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or57082Logic.initialize(or57082.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr57082 = computed(() => {
  // Or57082のダイアログ開閉状態
  return Or57082Logic.state.get(or57082.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  /**
   * 処理年月日
   */
  processYmd: { value: '2024/11/11' },
  /**
   * セクション番号
   */
  sectionNo: { value: 'U06026' },
  /**
   * セクション名
   */
  sectionName: { value: '居宅アセスメント表' },
  /**
   * 施設ID
   */
  shisetuId: { value: '1' },
  /**
   * 種別ID
   */
  syubetsuId: { value: '2' },
  /**
   * 事業者ID
   */
  svJigyoId: { value: '1' },
  /**
   * 事業所名
   */
  svJigyoKnj: { value: '居宅介護支援事業所' },
  /**
   * 職員ID
   */
  shokuId: { value: '1' },
  /**
   * 利用者ID
   */
  userId: { value: '1' },
  /**
   * アセスメントID
   */
  assessmentId: { value: '2' },
  /**
   * 選択帳票番号
   */
  prtNo: { value: '2' },
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: { value: ['わ', 'を', 'ん'] },
  /**
   * 担当者カウンタ値
   */
  selectedUserCounter: { value: '2' },
  /**
   * 担当者ID
   */
  tantoId: { value: '1' },
  /**
   * フォーカス設定用イニシャル
   */
  mo00040Type: {
    modelValue: '',
  },
})

const localOneway = reactive({
  processYmd: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '処理年月日',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  sectionNo: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: 'セクション番号',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  sectionName: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: 'セクション名',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  shisetuId: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '施設ID',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  syubetsuId: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '種別ID',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  svJigyoId: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '事業者ID',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  svJigyoKnj: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '事業所名',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  shokuId: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '職員ID',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  userId: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '利用者ID',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  assessmentId: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: 'アセスメントID',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  prtNo: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '選択帳票番号',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  focusSettingInitial: {
    showItemLabel: true,
    isVerticalLabel: true,
    readonly: true,
    itemLabel: 'フォーカス設定用イニシャル',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  selectedUserCounter: {
    showItemLabel: true,
    isVerticalLabel: true,
    itemLabel: '担当者カウンタ値',
    customClass: new CustomClass({
      labelClass: 'background-color',
    }),
  } as Mo00045OnewayType,
  mo00040OnewayType: {
    itemLabel: '50音設定',
    showItemLabel: true,
    class: 'ml-2',
    customClass: new CustomClass({
      labelClass: 'background-color ml-2',
    }),
    itemTitle: 'label',
    itemValue: 'value',
    items: [
      { label: '空白', value: '' },
      { label: 'あ行', value: ['あ', 'い', 'う', 'え', 'お'] },
      { label: 'か行', value: ['か', 'き', 'く', 'け', 'こ'] },
      { label: 'さ行', value: ['さ', 'し', 'す', 'せ', 'そ'] },
      { label: 'た行', value: ['た', 'ち', 'つ', 'て', 'と'] },
      { label: 'な行', value: ['な', 'に', 'ぬ', 'ね', 'の'] },
      { label: 'は行', value: ['は', 'ひ', 'ふ', 'へ', 'ほ'] },
      { label: 'ま行', value: ['ま', 'み', 'む', 'め', 'も'] },
      { label: 'や行', value: ['や', 'ゆ', 'よ'] },
      { label: 'ら行', value: ['ら', 'り', 'る', 'れ', 'ろ'] },
      { label: 'わ行', value: ['わ', 'を', 'ん'] },
    ],
  },
})

/**
 *  ボタン押下時の処理(Or57082)の場合
 *
 */
function onClickOr57082_1() {
  // Or57082のダイアログ開閉状態を更新する
  Or57082Logic.state.set({
    uniqueCpId: or57082.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        sectionNo: 'U06026',
        prtNo: '2',
        svJigyoId: '1',
        shisetuId: '1',
        tantoId: '1',
        syubetsuId: '2',
        sectionName: '居宅アセスメント表（H21/4改訂）',
        userId: '1',
        assessmentId: '3',
        svJigyoKnj: '1',
        processYmd: '2025/08/15',
        focusSettingInitial: ['や', 'ゆ', 'よ'],
        selectedUserCounter: '2'
      } as Or57082Param,
    },
  })
}

/**
 *  ボタン押下時の処理(Or57082)の場合
 *
 */
function onClickOr57082_2() {
  // Or57082のダイアログ開閉状態を更新する
  Or57082Logic.state.set({
    uniqueCpId: or57082.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        sectionNo: local.sectionNo.value,
        prtNo: local.prtNo.value,
        svJigyoId: local.svJigyoId.value,
        shisetuId: local.shisetuId.value,
        tantoId: local.shokuId.value,
        syubetsuId: local.syubetsuId.value,
        sectionName: local.sectionName.value,
        userId: local.userId.value,
        assessmentId: local.assessmentId.value,
        svJigyoKnj: local.svJigyoKnj.value,
        processYmd: local.processYmd.value,
        focusSettingInitial: [...local.focusSettingInitial.value],
        selectedUserCounter: local.selectedUserCounter.value,
      } as Or57082Param,
    },
  })
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/

watch(
  () => local.mo00040Type.modelValue,
  (newValue) => {
    local.focusSettingInitial.value = newValue as unknown as string[]
  }
)
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr57082_1"
        >GUI00815_印刷設定</v-btn
      >
      <g-custom-or-57082
        v-if="showDialogOr57082"
        v-bind="or57082"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <span>{{ '-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------'}}
    </span>
  </c-v-row>
  <c-v-row class="background-color">
    <c-v-col cols="4">
      <!-- 処理年月日 -->
      <base-mo00045
        v-model="local.processYmd"
        :oneway-model-value="localOneway.processYmd"
      ></base-mo00045>
      <!-- セクション名 -->
      <base-mo00045
        v-model="local.sectionName"
        :oneway-model-value="localOneway.sectionName"
      ></base-mo00045>
      <!-- セクション番号 -->
      <base-mo00045
        v-model="local.sectionNo"
        :oneway-model-value="localOneway.sectionNo"
      ></base-mo00045>
      <!-- 施設ID -->
      <base-mo00045
        v-model="local.shisetuId"
        :oneway-model-value="localOneway.shisetuId"
      ></base-mo00045>
      <!-- 種別ID -->
      <base-mo00045
        v-model="local.syubetsuId"
        :oneway-model-value="localOneway.syubetsuId"
      ></base-mo00045>
      <!-- 事業者ID -->
      <base-mo00045
        v-model="local.svJigyoId"
        :oneway-model-value="localOneway.svJigyoId"
      ></base-mo00045>
      <!-- 事業所名 -->
      <base-mo00045
        v-model="local.svJigyoKnj"
        :oneway-model-value="localOneway.svJigyoKnj"
      ></base-mo00045>
      <!-- 職員ID -->
      <base-mo00045
        v-model="local.shokuId"
        :oneway-model-value="localOneway.shokuId"
      ></base-mo00045>
      <!-- 利用者ID -->
      <base-mo00045
        v-model="local.userId"
        :oneway-model-value="localOneway.userId"
      ></base-mo00045>
      <!-- アセスメントID -->
      <base-mo00045
        v-model="local.assessmentId"
        :oneway-model-value="localOneway.assessmentId"
      ></base-mo00045>
      <!-- 選択帳票番号 -->
      <base-mo00045
        v-model="local.prtNo"
        :oneway-model-value="localOneway.prtNo"
      ></base-mo00045>
      <div class="d-flex background-color">
        <!-- フォーカス設定用イニシャル -->
        <base-mo00045
          v-model="local.focusSettingInitial"
          :oneway-model-value="localOneway.focusSettingInitial"
        ></base-mo00045>
        <base-mo-00040
          v-model="local.mo00040Type"
          :oneway-model-value="localOneway.mo00040OnewayType"
        />
        <div
          style="width: 16px"
          class="background-color"
        ></div>
      </div>
      <!-- 担当者カウンタ値 -->
      <base-mo00045
        v-model="local.selectedUserCounter"
        :oneway-model-value="localOneway.selectedUserCounter"
      ></base-mo00045>
    </c-v-col>
    <c-v-col
      cols="4"
      class="pt-8"
    >
      <v-btn
        variant="elevated"
        @click="onClickOr57082_2"
        >こちらをクリック 疎通起動
      </v-btn>
    </c-v-col>
  </c-v-row>
</template>
