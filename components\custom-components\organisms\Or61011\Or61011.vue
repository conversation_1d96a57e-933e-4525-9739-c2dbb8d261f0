<script setup lang="ts">
/**
 * Or61011:課題分析画面
 * GUI00930_課題分析
 *
 * @description
 * 課題分析画面
 *
 * <AUTHOR> PHAM TIEN THANH
 */
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { OrX0004Const } from '../OrX0004/OrX0004.constants'
import { OrX0014Const } from '../OrX0014/OrX0014.constants'
import { OrX0005Const } from '../OrX0005/OrX0005.constants'
import { OrX0007Const } from '../OrX0007/OrX0007.constants'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { Or52234Const } from '../Or52234/Or52234.constants'
import { Or52572Const } from '../Or52572/Or52572.constants'
import { OrX0115Const } from '../OrX0115/OrX0115.constants'
import { Or52653Const } from '../Or52653/Or52653.constants'
import { Or52653Logic } from '../Or52653/Or52653.logic'
import type { OrX0004StateType } from '../OrX0004/OrX0004.type'
import type { OrX0014StateType } from '../OrX0014/OrX0014.type'
import { OrX0004Logic } from '../OrX0004/OrX0004.logic'
import { OrX0014Logic } from '../OrX0014/OrX0014.logic'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0115Logic } from '../OrX0115/OrX0115.logic'
import { OrX0042Const } from '../OrX0042/OrX0042.constants'
import { OrX0010Const } from '../OrX0010/OrX0010.constants'
import { Or52570Const } from '../Or52570/Or52570.constants'
import { Or52570Logic } from '../Or52570/Or52570.logic'
import { Or61011Const } from './Or61011.constants'
import type { IssueAnalysisInitSelectOutData, ShousaiInfoType } from './Or61011.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or21832Const } from '~/components/base-components/organisms/Or21832/Or21832.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or21830Const } from '~/components/base-components/organisms/Or21830/Or21830.constants'
import { Or21828Const } from '~/components/base-components/organisms/Or21828/Or21828.constants'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import {
  useSystemCommonsStore,
  useScreenUtils,
  useCommonProps,
  ref,
  useSetupChildProps,
  reactive,
  useScreenInitFlg,
  onMounted,
  nextTick,
  useScreenTwoWayBind,
  hasRegistAuth,
  hasPrintAuth,
  watch,
  useScreenStore,
  computed,
} from '#imports'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import type { OrX0009OnewayType, OrX0009Type } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0007OnewayType, OrX0007Type } from '~/types/cmn/business/components/OrX0007Type'
import type { Or61011OnewayType } from '~/types/cmn/business/components/Or61011Type'
import type { Or52572OnewayType, Or52572Type } from '~/types/cmn/business/components/Or52572Type'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import { Or21830Logic } from '~/components/base-components/organisms/Or21830/Or21830.logic'
import { Or21832Logic } from '~/components/base-components/organisms/Or21832/Or21832.logic'
import { SPACE_WAVE, UPDATE_KBN } from '~/constants/classification-constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type {
  IssuesAnalysisInitSelectInEntity,
  IssuesAnalysisInitSelectOutEntity,
} from '~/repositories/cmn/entities/IssuesAnalysisInitSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type {
  IssuesAnalysisHistorySelectInEntity,
  IssuesAnalysisHistorySelectOutEntity,
} from '~/repositories/cmn/entities/IssuesAnalysisHistorySelectEntity'
import type {
  IssuesAnalysisDetailSelectInEntity,
  IssuesAnalysisDetailSelectOutEntity,
} from '~/repositories/cmn/entities/IssuesAnalysisDetailSelectEntity'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { IssueAnalysisUpdateInEntity } from '~/repositories/cmn/entities/IssuesAnalysisUpdateEntity'
import type {
  IssuesAnalysisPlanPeriodSelectInEntity,
  IssuesAnalysisPlanPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/IssuesAnalysisPlanPeriodSelectEntity'
import type { OrX0010OnewayType, OrX0010Type } from '~/types/cmn/business/components/OrX0010Type'
import type { Or52653OneWayModelValue } from '~/types/cmn/business/components/Or52653Type'
import type { Or52570OnewayType, Or52570Type } from '~/types/cmn/business/components/Or52570Type'
import type { Or52234OnewayType } from '~/types/cmn/business/components/Or52234Type'

const { t } = useI18n()

/**
 *  システム共有領域の状態管理
 */
const systemCommonsStore = useSystemCommonsStore()

/**
 * 画面のサイズを設定する
 */
const { setChildCpBinds } = useScreenUtils()

/**************************************************
 * Props
 **************************************************/
/**
 * Props
 */
const props = defineProps(useCommonProps())

/**
 * ヘッダー
 */
const orHeadLine = ref({ uniqueCpId: '' })
/**
 * or21828
 */
const or21828 = ref({ uniqueCpId: '' })
/**
 * or21830
 */
const or21830 = ref({ uniqueCpId: '' })
/**
 * or00248
 */
const or00248 = ref({ uniqueCpId: '' })
/**
 * orX0004
 */
const orX0004 = ref({ uniqueCpId: '' })
/**
 * orX0014
 */
const orX0014 = ref({ uniqueCpId: '' })
/**
 * orX0005
 */
const orX0005 = ref({ uniqueCpId: '' })
/**
 * or00249
 */
const or00249 = ref({ uniqueCpId: '' })
/**
 * or21832
 */
const or21832 = ref({ uniqueCpId: '' })
/**
 * orX0007
 */
const orX0007 = ref({ uniqueCpId: '' })
/**
 * or52570_1
 */
const or52570_1 = ref({ uniqueCpId: '' })
/**
 * orX0009_1
 */
const orX0009_1 = ref({ uniqueCpId: '' })
/**
 * orX0010_1
 */
const orX0010_1 = ref({ uniqueCpId: '' })
/**
 * or52234
 */
const or52234 = ref({ uniqueCpId: '' })
/**
 * or52572_1
 */
const or52572_1 = ref({ uniqueCpId: '' })
/**
 * or21814_1
 */
const or21814_1 = ref({ uniqueCpId: '' })
/**
 * or61011
 */
const or61011 = ref({ uniqueCpId: '' })
/**
 * or41179
 */
const or41179 = ref({ uniqueCpId: '' })
/**
 * orX0115
 */
const orX0115 = ref({ uniqueCpId: '' })
/**
 * or52653
 */
const or52653 = ref({ uniqueCpId: '' })

/**
 * 汎用コードマスタデータ
 */
const codeCommon = ref<CodeType[]>([])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21828Const.CP_ID]: or21828.value,
  [Or21830Const.CP_ID]: or21830.value,
  [Or00248Const.CP_ID(0)]: or00248.value,
  [OrX0004Const.CP_ID(0)]: orX0004.value,
  [OrX0014Const.CP_ID(0)]: orX0014.value,
  [OrX0005Const.CP_ID(0)]: orX0005.value,
  [Or21832Const.CP_ID]: or21832.value,
  [OrX0007Const.CP_ID(1)]: orX0007.value,
  [Or61011Const.CP_ID(0)]: or61011.value,
  [Or52570Const.CP_ID(1)]: or52570_1.value,
  [OrX0009Const.CP_ID(1)]: orX0009_1.value,
  [OrX0010Const.CP_ID(1)]: orX0010_1.value,
  [Or52572Const.CP_ID(1)]: or52572_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [OrX0115Const.CP_ID(0)]: orX0115.value,
  [Or52653Const.CP_ID(0)]: or52653.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(0)]: or00249.value,
})

useSetupChildProps(or52570_1.value.uniqueCpId, {
  [Or52234Const.CP_ID(0)]: or52234.value,
})

/**
 * 変更されたリスニングのコンポーネントIDリスト
 */
const watchedComponents = ref<string[]>([
  or52570_1.value.uniqueCpId,
  orX0009_1.value.uniqueCpId,
  orX0010_1.value.uniqueCpId,
  or52572_1.value.uniqueCpId,
])

/**
 * コンポーネントのデフォルト値
 */
const defaultComponents = {
  // 新規操作メニュー
  orX0004: {
    showCreateBtn: true,
    showCreateMenuCopy: true,
  } as OrX0004StateType,
  //作成者
  orX0009: {
    staffId: '0',
    staffName: '',
  } as OrX0009Type,
  // 作成日
  orX0010: {
    value: '',
  } as OrX0010Type,
  // 計画書オプションメニュー
  orX0014: {
    showLogFlg: false,
    showDeleteFlg: true,
  } as OrX0014StateType,
  // 計画対象期間
  orX0007: {
    planTargetPeriodId: '',
    PlanTargetPeriodUpdateFlg: '',
  } as OrX0007Type,
  // 履歴
  or52570: {
    createId: '',
    createUpateFlg: '',
  } as Or52570Type,
}

/**
 * コンポーネントのデフォルト値
 */
const defaultOneway = reactive({
  or61011Oneway: {} as Or61011OnewayType,
  // 事業所
  // 期間データ
  orX0007Oneway: {} as OrX0007OnewayType,
  // 履歴
  or52570Oneway: {
    officeId: systemCommonsStore.getSvJigyoId ?? '',
    userId: systemCommonsStore.getUserId ?? '',
    sc1Id: '',
    createData: {
      createId: 0,
      currentIndex: 0,
      totalCount: 0,
    },
  } as Or52570OnewayType,
  // 作成者
  orX0009Oneway: {
    isDisabled: false,
    createData: {
      staffName: '',
      staffId: 0,
      createId: null,
      createDate: '',
      currentIndex: 0,
      totalCount: 0,
      ks21Id: '',
    },
  } as OrX0009OnewayType,
  // 作成日
  orX0010Oneway: {
    isDisabled: false,
  } as OrX0010OnewayType,
})

/**
 * コンポーネントのデフォルト値
 */
const localComponents = reactive({
  orX0004: {
    ...defaultComponents.orX0004,
  } as OrX0004StateType,
  orX0009: {
    ...defaultComponents.orX0009,
  } as OrX0009Type,
  orX0010: {
    ...defaultComponents.orX0010,
  } as OrX0010Type,
  orX0007: {
    ...defaultComponents.orX0007,
  } as OrX0007Type,
  or52570: {
    ...defaultComponents.or52570,
  } as Or52570Type,
})

/**
 * コンポーネントのデフォルト値
 */
const localOneway = reactive({
  or61011Oneway: {
    ...defaultOneway.or61011Oneway,
  } as Or61011OnewayType,
  orX0007Oneway: {
    ...defaultOneway.orX0007Oneway,
  } as OrX0007OnewayType,
  or52570Oneway: {
    ...defaultOneway.or52570Oneway,
  } as Or52570OnewayType,
  orX0009Oneway: {
    ...defaultOneway.orX0009Oneway,
  } as OrX0009OnewayType,
  orX0010Oneway: {
    ...defaultOneway.orX0010Oneway,
  } as OrX0010OnewayType,
  or52572Oneway: {
    shousaiData: [] as ShousaiInfoType[],
    periodManageFlag: '',
    planTargetPeriodId: '',
    cpyFlg: false,
  } as Or52572OnewayType,
  orX0115Oneway: {
    kindId: systemCommonsStore.getSyubetu ?? '',
    sc1Id: '',
  } as OrX0115OnewayType,
  or52653Oneway: {
    periodManagementFlg: '',
    shisetuId: '',
    svJigyoId: '',
    userid: '',
    syubetsuId: '',
    currentShousaiData: [] as ShousaiInfoType[],
  } as Or52653OneWayModelValue,
  or52234Oneway: {
    sc1Id: '',
    svJigyoId: '',
    userId: '',
  } as Or52234OnewayType,
})

/**
 * コンポーネントのデフォルト値
 */
const local = reactive({
  orX0007: {
    PlanTargetPeriodUpdateFlg: '',
    planTargetPeriodId: '',
  } as OrX0007Type,
  orX0010: {
    value: '',
  } as OrX0010Type,
  or52572: {
    shousaiData: [] as ShousaiInfoType[],
  } as Or52572Type,
  or52234: {
    rirekiId: '',
    createYmd: '',
    shokuId: '',
    shokuinKnj: '',
  },
})

/**
 * 確認ダイアログのPromise
 */
let or21814ResolvePromise: (value: Or21814EventType) => void

/**
 * 共通情報
 */
const commonInfoData = reactive({
  //事業所ID
  svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
  //利用者ID
  userId: systemCommonsStore.getUserId ?? '',
  //施設ID
  shisetsuId: systemCommonsStore.getShisetuId ?? '',
  //作成者ID
  shokuId: systemCommonsStore.getStaffId ?? '',
  //種別ID
  syubetsuId: systemCommonsStore.getSyubetu ?? '',
  //事業者グループ適用ID
  officeGroupId: 'G0002',
  //共通情報.基準日
  commonDate: '2025/02/26',
  //ログイン情報.職員名
  loginUserName: 'LOGIN 職員名',
  //サービス事業者IDリスト
  svJigyoIdList: ['1', '2', '3'],
  // 法人ID
  houjinId: systemCommonsStore.getHoujinId ?? '',
  // 計画期間ID
  kikanId: '',
  // 履歴ID
  historyId: '0',
  // 履歴変更区分
  createUpdateFlg: '',
})

/**
 * 履歴更新区分
 */
const historyUpdKbn = ref<string>('')

/**
 * 表示フラグ
 */
const localFlg = ref({
  // 計画対象期間表示表示フラグ
  showPlanningPeriodFlg: true,
  // 履歴表示フラグ
  showHistoryFlg: true,
  // 作成者表示フラグ
  showAuthorFlg: true,
  // 記入日表示フラグ
  showEntryDateFlg: true,
  // 課題分析入力フォーム表示フラグ
  showShousaiFormFlg: true,
})

/**
 * 権限を保存する
 */
const hasRegistAuthFlag = ref<boolean>(false)

/**
 * 権限を印刷する
 */
const hasPrintAuthFlag = ref<boolean>(false)

/**
 * 総合計画データ
 */
const issueAnalysisData = ref<IssueAnalysisInitSelectOutData>({
  rirekiId: '',
  kikanFlg: '',
  planPeriodInfo: null,
  historyInfo: null,
  shousaiData: [] as ShousaiInfoType[],
})

/**
 * or52572
 */
const or52572Ref = ref({
  /**
   * 総合計画データを初期化する
   *
   * @param _or52572Oneway - 総合計画データ
   */
  initData: (_or52572Oneway: Or52572OnewayType) => {},
})

/**
 * 初期化フラグ
 */
const isInit = useScreenInitFlg()
onMounted(async () => {
  if (systemCommonsStore.getUserSelectSelfId) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        verticalLayout: true,
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or61011Const.STR_ALL })
  })

  hasRegistAuthFlag.value = await hasRegistAuth(Or61011Const.DEFAULT.LINK_AUTH)
  hasPrintAuthFlag.value = await hasPrintAuth(Or61011Const.DEFAULT.LINK_AUTH)

  // 共通処理の登録権限チェックを行う
  Or21830Logic.state.set({
    uniqueCpId: or21830.value.uniqueCpId,
    state: {
      disabled: !hasRegistAuthFlag.value,
    },
  })
  //共通処理の印刷権限チェックを行う
  Or21832Logic.state.set({
    uniqueCpId: or21832.value.uniqueCpId,
    state: {
      tooltipText: t('tooltip.interest-checksheet-print-setting-btn'),
      disabled: !hasPrintAuthFlag.value,
    },
  })

  // 初期情報取得
  if (isInit) {
    void initOr61011()
  }
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 新規イベント監視
 */
watch(
  () => OrX0004Logic.event.get(orX0004.value.uniqueCpId),
  async (newValue) => {
    // 新規イベント
    if (newValue?.createEventFlg) {
      //「削除」押下時に、新規、複写のボタンは実行無効にする
      if (historyUpdKbn.value === Or61011Const.HISTORY_UPD_KBN.D) {
        return
      }
      // 期間管理フラグが「1:管理する」、かつ、計画期間情報.期間総件数 = 0（期間なし）
      if (
        issueAnalysisData.value?.kikanFlg === Or61011Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT &&
        (issueAnalysisData.value?.planPeriodInfo === Or61011Const.DEFAULT.NULL_VALUE ||
          issueAnalysisData.value?.planPeriodInfo === undefined)
      ) {
        // i.cmn.11300メッセージを表示
        const rs = await showMessageICmn11300()

        if (rs.firstBtnClickFlg) {
          // AC012を実行
          await planTargetPeriodIconClick()
        }
      }
      // 期間管理フラグが「0:管理しない」、かつ、計画期間情報.期間総件数 = 0（期間なし）の場合
      if (
        issueAnalysisData.value?.kikanFlg === Or61011Const.PERIOD_MANAGEMENT_FLG.NO_MANAGEMENT &&
        (issueAnalysisData.value?.planPeriodInfo === Or61011Const.DEFAULT.NULL_VALUE ||
          issueAnalysisData.value?.planPeriodInfo === undefined)
      ) {
        // AC012-4（GUI00070_対象期間画面を表示）を実行し、処理続き
        await handleChangeKikan('', Or61011Const.KIKAN_PAGE_KBN.FIXED)
      }
      const isEdit = isEditNavControl(watchedComponents.value)
      // 画面データ変更かどうかを判断する
      if (isEdit) {
        // 共通処理の権限チェックを行う
        if (!hasRegistAuthFlag.value) {
          // 保存権限がない場合 - i.cmn.10006を表示
          const rs = await showMessageICmn10006()
          if (rs.firstBtnClickFlg) {
            // はい：処理続き
            await createEmptyScreen()
          } else {
            // いいえ：メッセージダイアログを閉じて処理終了
            return
          }
        } else {
          // 保存権限がある場合 - i.cmn.10430を表示
          const rs = await showMessageICmn10430()
          if (rs.firstBtnClickFlg) {
            // はい：AC003(保存処理)を実行し、処理続き
            await _save()
            // 新規空白画面を作成します
            await createEmptyScreen()
          } else if (rs.secondBtnClickFlg) {
            // いいえ：処理続き
            await createEmptyScreen()
          } else if (rs.thirdBtnClickFlg) {
            // キャンセル：処理終了
            return
          }
        }
      } else {
        // 履歴更新区分="C"(新規)の場合
        if (historyUpdKbn.value === Or61011Const.HISTORY_UPD_KBN.C) {
          // 以下のメッセージを表示: i.cmn.11265
          await showMessageICmn11265()
        } else {
          // 新規空白画面を作成します
          await createEmptyScreen()
        }
      }
    } else if (newValue?.copyEventFlg) {
      //「削除」押下時に、新規、複写のボタンは実行無効にする
      if (historyUpdKbn.value === Or61011Const.HISTORY_UPD_KBN.D) {
        return
      }
      // 複写イベント
      _copy()
    }
  }
)

/**
 * 計画対象期間変更の監視（OrX0007）
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    const { currentIndex, totalCount } = localOneway.orX0007Oneway.planTargetPeriodData
    const sc1Id =
      newValue.planTargetPeriodId !== '0'
        ? newValue.planTargetPeriodId
        : (localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId ?? '')

    if (newValue?.PlanTargetPeriodUpdateFlg === Or61011Const.UPDATE_CATEGORY_SELECT) {
      // 選択処理
      await handleChangeKikan(String(sc1Id), Or61011Const.KIKAN_PAGE_KBN.FIXED)
    } else if (newValue?.PlanTargetPeriodUpdateFlg === Or61011Const.UPDATE_CATEGORY_PREVIOUS) {
      // 前へボタン
      if (currentIndex === 1) {
        await showMessageICmn11262()
        return
      }
      await handleChangeKikan(String(sc1Id), Or61011Const.KIKAN_PAGE_KBN.BEFORE)
    } else if (newValue?.PlanTargetPeriodUpdateFlg === Or61011Const.UPDATE_CATEGORY_NEXT) {
      // 次へボタン
      if (currentIndex === totalCount) {
        await showMessageICmn11263()
        return
      }
      await handleChangeKikan(String(sc1Id), Or61011Const.KIKAN_PAGE_KBN.AFTER)
    }
  },
  { deep: true }
)

/**
 * 履歴変更の監視（Or52570）
 */
watch(
  () => Or52570Logic.data.get(or52570_1.value.uniqueCpId),
  async (newValue, oldValue) => {
    if (isUndefined(newValue)) {
      return
    }

    const { currentIndex, totalCount } = localOneway.or52570Oneway.createData
    const { createId, createUpateFlg } = newValue
    const rirekiId =
      createId !== '' ? createId : (localOneway.or52570Oneway.createData.createId ?? '')

    if (createUpateFlg === undefined || createUpateFlg === Or61011Const.DEFAULT.EMPTY_VALUE) {
      return
    }

    //「履歴-選択確認後 アイコンボタン」を押す時は総合計画の履歴変更処理を行って、処理終了
    if (createUpateFlg === Or61011Const.UPDATE_CATEGORY_CERTIFICATION) {
      // 選択前の履歴から変更がない場合、処理を終了する。
      if (newValue.createId === oldValue?.createId) return
      // 更新フラグINIT
      Or52570Logic.data.set({
        uniqueCpId: or52570_1.value.uniqueCpId,
        value: {
          createId: Or52570Logic.data.get(or52570_1.value.uniqueCpId)!.createId,
          createUpateFlg: '',
        },
      })
      // 履歴変更処理を行う
      await handleChangeRireki(createId, Or61011Const.HISTORY_CHANGE_KBN.SELECTED_ID)
      return
    }

    // 履歴変更処理
    if (createUpateFlg === Or61011Const.UPDATE_CATEGORY_SELECT) {
      // 選択処理
      await handleChangeRireki(rirekiId.toString(), Or61011Const.RIREKI_PAGE_KBN.FIXED)
    } else if (createUpateFlg === Or61011Const.UPDATE_CATEGORY_PREVIOUS) {
      // 前へボタン
      if (currentIndex === 1) {
        await showMessageICmn11262()
        return
      }
      await handleChangeRireki(rirekiId.toString(), Or61011Const.RIREKI_PAGE_KBN.BEFORE)
    } else if (createUpateFlg === Or61011Const.UPDATE_CATEGORY_NEXT) {
      // 次へボタン
      if (currentIndex === totalCount) {
        await showMessageICmn11263()
        return
      }
      await handleChangeRireki(rirekiId.toString(), Or61011Const.RIREKI_PAGE_KBN.AFTER)
    }
  },
  { deep: true }
)

/**
 * 「削除」押下イベント監視
 */
watch(
  () => OrX0014Logic.event.get(orX0014.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.deleteEventFlg) {
      //「削除」押下時に、印刷のボタンは実行無効にする
      if (historyUpdKbn.value === Or61011Const.HISTORY_UPD_KBN.D) {
        return
      }

      // 以下のメッセージを表示: i.cmn.11326
      const rs = await openConfirmDialog(or21814_1.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11326', [
          localComponents.orX0010.value,
          t('label.issue-analysis-master'),
        ]),
        // 第1ボタンタイプ
        firstBtnType: 'normal1',
        // 第1ボタンラベル
        firstBtnLabel: t('btn.yes'),
        // 第2ボタンタイプ
        secondBtnType: 'normal3',
        // 第2ボタンラベル
        secondBtnLabel: t('btn.no'),
        // 第3ボタンタイプ
        thirdBtnType: 'blank',
      })
      if (rs.firstBtnClickFlg) {
        // 履歴更新区分を「D:削除」にする。
        // 入力フォームのヘッダー部以外を非表示する
        historyUpdKbn.value = Or61011Const.HISTORY_UPD_KBN.D

        // shousaiDataを空の配列にする
        localOneway.or52572Oneway.shousaiData = []
        localOneway.or52572Oneway.updateKbn = UPDATE_KBN.DELETE
        or52572Ref.value.initData(localOneway.or52572Oneway)

        // local.or52572のデータも空の配列にする
        local.or52572.shousaiData = []

        // refValueのshousaiDataも空の配列にする
        refValue.value!.shousaiData = []

        // 作成者選択アイコンボタン、作成日、作成日カレンダを非活性にする。
        localOneway.orX0009Oneway.isDisabled = true
        localOneway.orX0010Oneway.isDisabled = true
      }
    }
  }
)

/**
 * 「保存ボタン」押下
 */
watch(
  () => Or21830Logic.event.get(or21830.value.uniqueCpId),
  async (newVal) => {
    if (newVal?.clickEventFlg) {
      const isEdit = isEditNavControl(watchedComponents.value)
      // 画面入力データに変更がない場合
      if (!isEdit) {
        await showMessageICmn21800()
      } else {
        // 「興味・関心チェックシート」情報を保存する
        await _save()
      }

      Or21830Logic.event.set({
        uniqueCpId: or21830.value.uniqueCpId,
        events: {
          clickEventFlg: false,
        },
      })
    }
  }
)

/**
 *「印刷設定アイコンボタン」押下イベント監視
 */
watch(
  () => Or21832Logic.event.get(or21832.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickEventFlg) {
      //「削除」押下時に、印刷のボタンは実行無効にする
      if (historyUpdKbn.value === Or61011Const.HISTORY_UPD_KBN.D) {
        return
      }

      const isEdit = isEditNavControl(watchedComponents.value)
      // 画面データ変更かどうかを判断する
      if (isEdit) {
        const rs = await showMessageICmn10006()
        if (rs.firstBtnClickFlg) {
          await _save()
        } else if (rs.thirdBtnClickFlg) {
          // キャンセル選択時は複写データの作成を行わずに終了する
          return
        }
      }

      // TODO 未作成
      // GUI00953［印刷設定］画面をポップアップで起動する
      // セクション名："興味・関心チェックシート"
      // 選択帳票番号：-
      // 計画期間管理フラグ： 親画面.計画対象期間管理フラグ
      // 親画面.法人ＩＤ
      // 親画面.施設ＩＤ
      // 親画面.事業所ＩＤ
      // 親画面.職員ＩＤ
      // 親画面.利用者ＩＤ
      // 親画面.開始日(YYYY/MM/DD)
      // 親画面.終了日(YYYY/MM/DD)
      // 親画面.システム年月日(YYYY/MM/DD)
      // 親画面.計画期間ＩＤ
      // 親画面.履歴ＩＤ
      // 親画面.ユーザリスト
      // 50音行番号：親画面.50音行番号
      // 50音母音：親画面.50音母音

      Or21832Logic.event.set({
        uniqueCpId: or21832.value.uniqueCpId,
        events: {
          clickEventFlg: false,
        },
      })
    }
  }
)

/**
 * 事業所選択更新の監視
 */
watch(
  () => Or41179Logic.data.get(or41179.value.uniqueCpId),
  async (newValue, oldValue) => {
    // Undefinedの時戻す
    if (isUndefined(newValue) || !oldValue?.modelValue) {
      return
    }

    const isEdit = isEditNavControl(watchedComponents.value)
    // 画面データ変更かどうかを判断する
    if (isEdit) {
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        await _save()
      } else if (rs.thirdBtnClickFlg) {
        // キャンセル選択時は複写データの作成を行わずに終了する
        return
      }
    }
    // 事業所切替時、選択した事業所の事業所IDを設定する
    commonInfoData.svJigyoId = newValue.modelValue ?? ''
    commonInfoData.kikanId = ''
    commonInfoData.historyId = '0'

    // 画面を初期化する
    await initOr61011()
  },
  {
    deep: true,
  }
)

/**
 * 利用者選択更新の監視
 */
watch(
  () => systemCommonsStore.getUserSelectSelfId(),
  async (newValue, oldValue) => {
    // Undefinedの時戻す
    if (isUndefined(newValue) || !oldValue) {
      return
    }

    const isEdit = isEditNavControl(watchedComponents.value)
    // 画面データ変更かどうかを判断する
    if (isEdit) {
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        await _save()
      } else if (rs.thirdBtnClickFlg) {
        // キャンセル選択時は複写データの作成を行わずに終了する
        return
      }
    }
    // 利用者切替時、選択した利用者の利用者IDを設定する
    commonInfoData.userId = newValue ?? ''
    commonInfoData.kikanId = ''
    commonInfoData.historyId = '0'

    // 画面を初期化する
    await initOr61011()
  }
)

/**
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }

    const planID = newValue.planTargetPeriodId
    local.orX0007.planTargetPeriodId = planID
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg

    if (planUpdateFlg === undefined || planUpdateFlg === Or61011Const.DEFAULT.EMPTY_VALUE) {
      return
    }

    const isEdit = isEditNavControl(watchedComponents.value)
    // 画面データ変更かどうかを判断する
    if (isEdit) {
      const rs = await showMessageICmn10430()
      if (rs.firstBtnClickFlg) {
        await _save()
      } else if (rs.thirdBtnClickFlg) {
        // キャンセル選択時は複写データの作成を行わずに終了する
        return
      }
    }

    //「期間-前へ アイコンボタン」押下
    if (planUpdateFlg === Or61011Const.UPDATE_CATEGORY_PREVIOUS) {
      local.orX0007.PlanTargetPeriodUpdateFlg = planUpdateFlg

      // 計画期間変更処理を行う
      await handleChangeKikan(local.orX0007.planTargetPeriodId, Or61011Const.KIKAN_PAGE_KBN.BEFORE)
    }
    //「期間-次へ アイコンボタン」押下
    else if (planUpdateFlg === Or61011Const.UPDATE_CATEGORY_NEXT) {
      local.orX0007.PlanTargetPeriodUpdateFlg = planUpdateFlg

      // 計画期間変更処理を行う
      await handleChangeKikan(local.orX0007.planTargetPeriodId, Or61011Const.KIKAN_PAGE_KBN.AFTER)
    }
  }
)

/**
 * 対象期間ダイアログの期間更新の監視
 */
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  async (newValue, oldValue) => {
    if (newValue?.kikanId === oldValue?.kikanId) return
    local.orX0007.planTargetPeriodId = newValue?.kikanId ?? ''
    // 更新フラグINIT
    OrX0007Logic.data.set({
      uniqueCpId: orX0007.value.uniqueCpId,
      value: {
        planTargetPeriodId: local.orX0007.planTargetPeriodId,
        PlanTargetPeriodUpdateFlg: '',
      },
    })

    // 計画期間変更処理を行う
    await handleChangeKikan(local.orX0007.planTargetPeriodId, Or61011Const.KIKAN_PAGE_KBN.FIXED)
  }
)

function handleShousaiChange(payload: { index: number; value: string }) {
  if (!payload) return
  const { index, value } = payload
  const list = refValue.value?.shousaiData
  if (Array.isArray(list) && list[index]) {
    list[index].naiyoKnj = value
  }
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814_1.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21814ResolvePromise !== undefined && newValue !== undefined) {
      or21814ResolvePromise(newValue)
    }
    return
  }
)

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 初期表示
 */
async function initOr61011() {
  historyUpdKbn.value = ''
  // 汎用コードマスタデータを取得
  await initCode()
  // 課題分析の初期情報を取得する。
  await getIssuesAnalysisInitData()
}

/**
 * 興味・関心チェックシートの初期情報を取得する。
 */
async function getIssuesAnalysisInitData() {
  // パラメータ
  const inParam: IssuesAnalysisInitSelectInEntity = {
    // 事業者ID
    svJigyoId: commonInfoData.svJigyoId,
    // 施設ID
    shisetuId: commonInfoData.shisetsuId,
    // 利用者ID
    userid: commonInfoData.userId,
    // 種別ID
    syubetsuId: commonInfoData.syubetsuId,
  }

  // 興味・関心チェックシートの初期情報を取得する。
  const ret: IssuesAnalysisInitSelectOutEntity = await ScreenRepository.select(
    'issuesAnalysisInitSelect',
    inParam
  )
  // 画面項目を取得したデータで設定する。
  await setScreenData(ret)
}

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCode() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 課題検討項目
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ISSUE_REVIEW_ITEM },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  codeCommon.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ISSUE_REVIEW_ITEM)
}

/**
 * 共通関数より汎用コード（324：課題検討項目）を取得し、内容情報リスト.マスタIDをコードマスタ.名称に変換する。
 *
 * @param koumokuMstId - マスタID
 *
 * @returns koumokuKnj - 項目名称
 */
function getKoumokuKnj(koumokuMstId: string): string {
  const match = codeCommon.value.find((k) => k.value === koumokuMstId)
  return match ? match.label : ''
}

/**
 * 画面項目を取得したデータで設定する。
 *
 * @param ret - 初期情報 | 計画期間変更情報
 */
async function setScreenData(ret: IssuesAnalysisInitSelectOutEntity) {
  // 期間管理フラグを設定する
  issueAnalysisData.value.kikanFlg = ret.data.kikanFlg

  // 計画期間情報を設定する
  if (ret.data.planPeriodInfo) {
    // APIから返された期間情報を設定（objectとして直接設定）
    issueAnalysisData.value.planPeriodInfo = ret.data.planPeriodInfo
  }

  // 計画対象期間
  // 計画期間が登録されている場合、計画期間情報.開始日 + " ～ " + 計画期間情報.終了日
  if (
    issueAnalysisData.value.planPeriodInfo !== Or61011Const.DEFAULT.NULL_VALUE &&
    issueAnalysisData.value.planPeriodInfo !== undefined
  ) {
    localOneway.orX0007Oneway.planTargetPeriodData = {
      // 計画対象期間
      planTargetPeriod:
        issueAnalysisData.value.planPeriodInfo.startYmd +
        SPACE_WAVE +
        issueAnalysisData.value.planPeriodInfo.endYmd,
      // 計画対象期間期間ID
      planTargetPeriodId: Number(issueAnalysisData.value.planPeriodInfo.syubetsuId),
      // 表示中の番号
      currentIndex: Number(issueAnalysisData.value.planPeriodInfo.periodNo),
      // 登録数
      totalCount: Number(issueAnalysisData.value.planPeriodInfo.periodCnt),
    }
    localComponents.orX0007.planTargetPeriodId = issueAnalysisData.value.planPeriodInfo.syubetsuId
    localComponents.orX0007.PlanTargetPeriodUpdateFlg = ''
  } else {
    localOneway.orX0007Oneway.planTargetPeriodData = {
      // 計画対象期間
      planTargetPeriod: '',
      // 計画対象期間期間ID
      planTargetPeriodId: 0,
      // 表示中の番号
      currentIndex: 0,
      // 登録数
      totalCount: 0,
    }
    localComponents.orX0007.planTargetPeriodId = ''
    localComponents.orX0007.PlanTargetPeriodUpdateFlg = ''
  }

  // 計画対象期間: 期間管理フラグが「0:管理しない」の場合、非表示
  localFlg.value.showPlanningPeriodFlg =
    issueAnalysisData.value.kikanFlg !== Or61011Const.PERIOD_MANAGEMENT_FLG.NO_MANAGEMENT

  // 期間管理フラグが「1:管理する」、かつ、計画期間が登録されていない場合
  if (
    issueAnalysisData.value.kikanFlg === Or61011Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT &&
    !localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId
  ) {
    // 履歴非表示
    localFlg.value.showHistoryFlg = false
    // 作成者非表示
    localFlg.value.showAuthorFlg = false
    // 作成日非表示
    localFlg.value.showEntryDateFlg = false
    // 課題分析入力フォーム非表示
    localFlg.value.showShousaiFormFlg = false
  }

  setRirekiData(ret)

  await nextTick()
  setChildCpBinds(props.uniqueCpId, {
    [OrX0010Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.orX0010.value,
        mo01343: {},
      } as Mo00020Type,
    },
    [OrX0009Const.CP_ID(1)]: {
      twoWayValue: localComponents.orX0009,
    },
    [OrX0007Const.CP_ID(1)]: {
      twoWayValue: localComponents.orX0007,
    },
    [Or52570Const.CP_ID(1)]: {
      twoWayValue: localComponents.or52570,
    },
  })
  refValue.value = JSON.parse(
    JSON.stringify(issueAnalysisData.value)
  ) as IssueAnalysisInitSelectOutData
  setChildCpBinds(props.uniqueCpId, {
    [Or52572Const.CP_ID(1)]: {
      twoWayValue: JSON.parse(JSON.stringify(issueAnalysisData.value)),
    },
  })

  localOneway.orX0009Oneway.isDisabled = false
  localOneway.orX0010Oneway.isDisabled = false

  // 課題分析入力フォームを表示する（通常モード）
  localFlg.value.showShousaiFormFlg = true

  localOneway.or52572Oneway.shousaiData = refValue.value?.shousaiData
  localOneway.or52572Oneway.updateKbn = ''
  or52572Ref.value?.initData(localOneway.or52572Oneway)
}

/**
 * apiから履歴情報を取得して、画面にデータを設定する
 *
 * @param ret - レスポンス
 */
const setRirekiData = (
  ret: IssuesAnalysisHistorySelectOutEntity | IssuesAnalysisInitSelectOutEntity
) => {
  historyUpdKbn.value = ''
  if (ret.data.historyInfo) {
    // 履歴情報を設定（objectとして直接設定）
    issueAnalysisData.value.historyInfo = ret.data.historyInfo
  }
  // 履歴情報が存在する場合
  if (
    issueAnalysisData.value.historyInfo !== Or61011Const.DEFAULT.NULL_VALUE &&
    issueAnalysisData.value.historyInfo !== undefined
  ) {
    issueAnalysisData.value.rirekiId = issueAnalysisData.value.historyInfo.rirekiId
  }
  // 問題分析情報設定 - 安全にデータを取得する
  let shousaiData: ShousaiInfoType[] = []
  if (ret.data.shousaiList?.length) {
    shousaiData = ret.data.shousaiList.map((item) => ({
      ...item,
      id: '',
    }))
  }
  issueAnalysisData.value.shousaiData = shousaiData

  // 履歴
  // 履歴情報が存在する場合
  if (issueAnalysisData.value.historyInfo !== Or61011Const.DEFAULT.NULL_VALUE) {
    localOneway.or52570Oneway.createData.currentIndex = Number(
      issueAnalysisData.value.historyInfo.krirekiNo
    )
    localOneway.or52570Oneway.createData.totalCount = Number(
      issueAnalysisData.value.historyInfo.krirekiCnt
    )
    localOneway.or52570Oneway.createData.createId = Number(
      issueAnalysisData.value.historyInfo.rirekiId
    )
    // 履歴ID
    localComponents.or52570.createId = issueAnalysisData.value.historyInfo.rirekiId

    // 計画書_履歴情報.職員番号の職員名
    localOneway.orX0009Oneway.createData = {
      staffName: issueAnalysisData.value.historyInfo.shokuNameKnj,
      staffId: Number(issueAnalysisData.value.historyInfo.shokuId),
      createId: null,
      createDate: '',
      currentIndex: 0,
      totalCount: 0,
      ks21Id: '',
    }

    // 作成日: 履歴情報.作成日
    localComponents.orX0010.value = issueAnalysisData.value.historyInfo.createYmd
  } else {
    // 作成者名: 初期表示で履歴データが存在しない場合、ログイン情報.職員名
    localOneway.orX0009Oneway.createData = {
      staffName: '職員 一郎 ',
      staffId: 0,
      createId: null,
      createDate: '',
      currentIndex: 0,
      totalCount: 0,
      ks21Id: '',
    }
    // 履歴ID
    // localComponents.or52570.createId = ''
    localComponents.or52570.createId = ''

    // 作成日: 初期表示で履歴データが存在しない場合、親画面.作成日
    localComponents.orX0010.value = systemCommonsStore.getSystemDate ?? ''
  }
}

async function onConfirm(selectedData: { shousaiData: ShousaiInfoType[] }, rirekiId: string) {
  // selectedDataから複写データとrirekiIdを取得
  const copyData = selectedData.shousaiData ?? []
  const copyRirekiId = rirekiId ?? ''

  // 複写元の履歴IDを使用して詳細情報を取得（DB最新データを取得）
  if (copyRirekiId) {
    try {
      // 複写元の履歴IDでissuesAnalysisDetailSelectを呼び出してDB最新データを取得
      const detailData = await getIssuesAnalysisDetailData(copyRirekiId)

      // 選択されたデータ（copyData）の項目のみを処理
      copyData.forEach((copyItem) => {
        // DBから取得したデータで同じkoumokuMstIdを持つ項目を検索
        const detailItem = detailData.data.shousaiList.find(
          (detail) => detail.koumokuMstId === copyItem.koumokuMstId
        )

        if (detailItem) {
          // 現在の画面データで同じkoumokuMstIdを持つ項目を検索
          const targetIndex = refValue.value?.shousaiData.findIndex(
            (data) => data.koumokuMstId === copyItem.koumokuMstId
          )

          if (targetIndex !== undefined && targetIndex >= 0 && refValue.value?.shousaiData) {
            // DBから取得した最新データを使用（選択された項目のみ）
            refValue.value.shousaiData[targetIndex] = {
              ...refValue.value.shousaiData[targetIndex],
              naiyoKnj: detailItem.naiyoKnj, // DBから取得した最新データを使用
              koumokuKnj: detailItem.koumokuKnj, // system code 324から取得した項目名称
            }
          }
        }
      })
    } catch {
      // エラーの場合は複写データを使用（fallback）
      copyData.forEach((item: ShousaiInfoType) => {
        if (item.koumokuMstId && item.naiyoKnj) {
          const targetIndex = refValue.value?.shousaiData.findIndex(
            (data) => data.koumokuMstId === item.koumokuMstId
          )

          if (targetIndex !== undefined && targetIndex >= 0 && refValue.value?.shousaiData) {
            refValue.value.shousaiData[targetIndex] = {
              ...refValue.value.shousaiData[targetIndex],
              naiyoKnj: item.naiyoKnj,
              koumokuKnj: item.koumokuKnj || getKoumokuKnj(item.koumokuMstId),
            }
          }
        }
      })
    }
  } else {
    // rirekiIdがない場合は複写データを使用
    copyData.forEach((item: ShousaiInfoType) => {
      if (item.koumokuMstId && item.naiyoKnj) {
        const targetIndex = refValue.value?.shousaiData.findIndex(
          (data) => data.koumokuMstId === item.koumokuMstId
        )

        if (targetIndex !== undefined && targetIndex >= 0 && refValue.value?.shousaiData) {
          refValue.value.shousaiData[targetIndex] = {
            ...refValue.value.shousaiData[targetIndex],
            naiyoKnj: item.naiyoKnj,
            koumokuKnj: item.koumokuKnj || getKoumokuKnj(item.koumokuMstId),
          }
        }
      }
    })
  }

  // localOneway.or52572Oneway.shousaiDataも更新
  localOneway.or52572Oneway.shousaiData = refValue.value?.shousaiData ?? []

  // or52572Refのデータを再初期化
  or52572Ref.value?.initData(localOneway.or52572Oneway)
}

/**
 * 確認メッセージダイアログ開く(i-cmn-10430)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn10430() {
  const rs = await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'destroy1',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'normal3',
    // 第3ボタンラベル
    thirdBtnLabel: t('btn.cancel'),
  })
  return rs
}

async function showMessageICmn10006() {
  const rs = await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10006'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンタイプ
    secondBtnType: 'destroy1',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
  })
  return rs
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11300)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn11300() {
  const rs = await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11300'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
  })
  return rs
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11300)
 *
 * @returns メッセージPromise
 */
async function showMessageICmn21800() {
  const rs = await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-21800'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
  })
  return rs
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11265)
 */
async function showMessageICmn11265() {
  await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11265', [t('label.issue-analysis-master')]),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'blank',
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  } as Or21814OnewayType)
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11262) - 前へボタン制限
 */
async function showMessageICmn11262() {
  await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11262'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'blank',
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  } as Or21814OnewayType)
}

/**
 * 確認メッセージダイアログ開く(i-cmn-11263) - 次へボタン制限
 */
async function showMessageICmn11263() {
  await openConfirmDialog(or21814_1.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-11263'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'blank',
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  } as Or21814OnewayType)
}

/**
 * 確認ダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - 確認ダイアログOneWayBind領域
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    or21814ResolvePromise = resolve
  })
}

/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
function isEditNavControl(components: string[]) {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()

  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }

  return false
}

/**
 * 保存時処理
 */
async function _save() {
  // 更新データ作成

  const inputData: IssueAnalysisUpdateInEntity = {
    defHoujinId: commonInfoData.houjinId,
    defShisetuId: commonInfoData.shisetsuId,
    defSvJigyoId: commonInfoData.svJigyoId,
    userId: commonInfoData.userId,
    syubetsuId: commonInfoData.syubetsuId,
    createYmd: systemCommonsStore.getSystemDate ?? '',
    shokuId: localComponents.orX0009.staffId,
    kikanFlg: issueAnalysisData.value.kikanFlg,
    updateKbn: 'C',
    kikanId: localComponents.orX0007.planTargetPeriodId,
    rirekiId: localComponents.or52570.createId,
    gamenList:
      refValue.value?.shousaiData.map((item, index) => ({
        id: (index + 1).toString(), // indexベースでIDを生成
        koumokuMstCd: item.koumokuMstId,
        naiyoKnj: item.naiyoKnj,
      })) ?? [],
  }

  const resData = await ScreenRepository.update('issuesAnalysisUpdate', inputData)

  /**
   * 保存成功の場合、編集フラグをリセット
   */
  if (resData.statusCode === Or61011Const.STATUS_CODE.SUCCESS) {
    // 監視対象のコンポーネントのedit flagを全てfalseにリセット
    const screenStore = useScreenStore()
    watchedComponents.value.forEach((uniqueCpId) => {
      const cpId = uniqueCpId.split('-')[0] // uniqueCpIdからcpIdを抽出
      screenStore.setCpNavControl({
        cpId,
        uniqueCpId,
        editFlg: false,
      })
    })

    // システム共有情報の編集破棄ダイアログ表示フラグを更新
    useSystemCommonsStore().setShowEditDiscardDialog(false)

    // 画面情報再取得
    await handleChangeKikan(local.orX0007.planTargetPeriodId, Or61011Const.KIKAN_PAGE_KBN.FIXED)
  }
}

/**
 * 計画期間変更処理を行う
 *
 * @param kikanId - 期間ID
 *
 * @param kikanPageKbn - 計画期間ページ区分
 */
async function handleChangeKikan(kikanId: string, kikanPageKbn: string) {
  const param: IssuesAnalysisPlanPeriodSelectInEntity = {
    svJigyoId: commonInfoData.svJigyoId,
    userId: commonInfoData.userId,
    shisetuId: commonInfoData.shisetsuId,
    syubetsuId: commonInfoData.syubetsuId,
    sc1Id: kikanId,
    kikanPageKbn: kikanPageKbn,
    rirekiPageKbn: '',
    rirekiId: '',
  }
  // 計画期間変条件を取得する
  const ret: IssuesAnalysisPlanPeriodSelectOutEntity = await ScreenRepository.select(
    'issuesAnalysisPlanPeriodSelect',
    param
  )

  await setScreenData(ret)
}

/**
 * 履歴変更処理を行う
 *
 * @param rirekiId - 履歴ID
 *
 * @param rirekiPageKbn - 履歴変更区分
 */
async function handleChangeRireki(rirekiId: string, rirekiPageKbn: string) {
  const param: IssuesAnalysisHistorySelectInEntity = {
    sc1Id: localComponents.orX0007.planTargetPeriodId,
    rirekiId: rirekiId,
    svJigyoId: commonInfoData.svJigyoId,
    userId: commonInfoData.userId,
    rirekiPageKbn: rirekiPageKbn,
  }

  // 履歴IDを取得する
  const ret: IssuesAnalysisHistorySelectOutEntity = await ScreenRepository.select(
    'issuesAnalysisHistorySelect',
    param
  )

  // APIから返された履歴情報を直接使用（backendが適切な履歴を返す）
  setRirekiData(ret)

  setChildCpBinds(props.uniqueCpId, {
    [OrX0010Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.orX0010.value,
        mo01343: {},
      } as Mo00020Type,
    },
    [OrX0009Const.CP_ID(1)]: {
      twoWayValue: localComponents.orX0009,
    },
    [Or52570Const.CP_ID(1)]: {
      twoWayValue: localComponents.or52570,
    },
  })

  refValue.value = JSON.parse(
    JSON.stringify(issueAnalysisData.value)
  ) as IssueAnalysisInitSelectOutData
  setChildCpBinds(props.uniqueCpId, {
    [Or52572Const.CP_ID(1)]: {
      twoWayValue: JSON.parse(JSON.stringify(issueAnalysisData.value)),
    },
  })

  localOneway.orX0009Oneway.isDisabled = false
  localOneway.orX0010Oneway.isDisabled = false

  localOneway.or52572Oneway.shousaiData = refValue.value?.shousaiData
  localOneway.or52572Oneway.updateKbn = ''
  or52572Ref.value.initData(localOneway.or52572Oneway)
}

/**
 * 新規空白画面を作成
 */
async function createEmptyScreen() {
  // 新規空白画面を作成し、デーフォルトデータを画面対応の項目に設定する。
  localComponents.orX0004 = defaultComponents.orX0004
  // 画面.作成日 = 親画面.基準日
  localComponents.orX0010.value = systemCommonsStore.getSystemDate ?? ''
  // TODO 画面.作成者は親画面.ログイン職員情報.職員ID対応の職員を設定する
  localComponents.orX0009 = {
    staffName: '職員 一郎 ',
    staffId: '0',
  }
  localOneway.orX0009Oneway.createData = {
    staffName: '職員 一郎 ',
    staffId: 0,
    createId: null,
    createDate: '',
    currentIndex: 0,
    totalCount: 0,
    ks21Id: '',
  }
  // 画面.履歴ページングは履歴の最大値+1/履歴の最大値+1を設定する
  localOneway.or52570Oneway.createData.currentIndex =
    Number(issueAnalysisData.value.historyInfo?.krirekiCnt ?? 0) + 1
  localOneway.or52570Oneway.createData.totalCount =
    Number(issueAnalysisData.value.historyInfo?.krirekiCnt ?? 0) + 1
  localOneway.or52570Oneway.createData.createId = 0
  // ・履歴更新区分 = "C"
  historyUpdKbn.value = OrX0042Const.HISTORY_UPD_KBN.C
  action.value = OrX0042Const.PAGE_ACTION.NEW
  // データ-ページング
  await nextTick()

  setChildCpBinds(props.uniqueCpId, {
    [OrX0010Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.orX0010.value,
        mo01343: {},
      } as Mo00020Type,
    },
    [OrX0009Const.CP_ID(1)]: {
      twoWayValue: localComponents.orX0009,
    },
  })

  // 課題分析入力フォームを表示する（新規モード）
  localFlg.value.showShousaiFormFlg = true

  // 既存のshousaiDataの構造を保持し、naiyoKnj（textareaの内容）のみを空にする
  if (issueAnalysisData.value.shousaiData && issueAnalysisData.value.shousaiData.length > 0) {
    issueAnalysisData.value.shousaiData.forEach((item: ShousaiInfoType) => {
      item.naiyoKnj = ''
    })

    // 更新されたデータを各所に反映
    refValue.value = JSON.parse(
      JSON.stringify(issueAnalysisData.value)
    ) as IssueAnalysisInitSelectOutData

    localOneway.or52572Oneway.shousaiData = [...issueAnalysisData.value.shousaiData]
    local.or52572.shousaiData = [...issueAnalysisData.value.shousaiData]

    setChildCpBinds(props.uniqueCpId, {
      [Or52572Const.CP_ID(1)]: {
        twoWayValue: JSON.parse(JSON.stringify(issueAnalysisData.value)),
      },
    })

    localOneway.or52572Oneway.updateKbn = ''
    or52572Ref.value.initData(localOneway.or52572Oneway)
  }
}

/**
 * AC014_「計画対象期間選択アイコンボタン」押下
 */
async function planTargetPeriodIconClick() {
  // 画面入力データに変更がある場合
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    const dialogResult = await showMessageICmn10430()
    if (dialogResult.firstBtnClickFlg) {
      await _save()
    } else if (dialogResult.thirdBtnClickFlg) {
      return
    }
  }
  // GUI00070 対象期間画面をポップアップで起動する
  OrX0115Logic.state.set({
    uniqueCpId: orX0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ページアクション
const action = ref<string>('')

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<IssueAnalysisInitSelectOutData>({
  cpId: Or52572Const.CP_ID(1),
  uniqueCpId: or52572_1.value.uniqueCpId,
})
refValue.value = {
  rirekiId: '',
  kikanFlg: '',
  shousaiData: [] as ShousaiInfoType[],
  planPeriodInfo: null,
  historyInfo: null,
}

/**
 * 課題分析詳細情報を取得する
 *
 * @param rirekiId - 履歴ID
 *
 * @returns 課題分析詳細情報
 */
async function getIssuesAnalysisDetailData(rirekiId: string) {
  const inParam: IssuesAnalysisDetailSelectInEntity = {
    rirekiId: rirekiId,
  }

  const ret: IssuesAnalysisDetailSelectOutEntity = await ScreenRepository.select(
    'issuesAnalysisDetailSelect',
    inParam
  )

  // koumokuMstIdを使って項目名称を取得し、データを加工
  const processedShousaiList = ret.data.shousaiList.map((item) => ({
    ...item,
    koumokuKnj: getKoumokuKnj(item.koumokuMstId), // System code 324から項目名称を取得
  }))

  return {
    ...ret,
    data: {
      ...ret.data,
      shousaiList: processedShousaiList,
    },
  }
}

/**
 * 複写画面を表示する
 */
function _copy() {
  localOneway.or52653Oneway.periodManagementFlg =
    issueAnalysisData.value.kikanFlg === Or61011Const.PERIOD_MANAGEMENT_FLG.MANAGEMENT ? '1' : '0'
  localOneway.or52653Oneway.shisetuId = commonInfoData.shisetsuId
  localOneway.or52653Oneway.svJigyoId = commonInfoData.svJigyoId
  localOneway.or52653Oneway.userid = commonInfoData.userId
  localOneway.or52653Oneway.syubetsuId = commonInfoData.syubetsuId

  localOneway.or52653Oneway.currentShousaiData = refValue.value?.shousaiData
  Or52653Logic.state.set({
    uniqueCpId: or52653.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr52653 = computed(() => {
  // Or52653のダイアログ開閉状態
  return Or52653Logic.state.get(or52653.value.uniqueCpId)?.isOpen ?? false
})
</script>
<template>
  <c-v-sheet class="view common-layout">
    <c-v-row
      no-gutters
      class="pa-2 sticky-header"
    >
      <!-- 興味・関心チェックシート画面タイトル -->
      <c-v-col class="title-area">
        <c-v-row
          align-center
          justify-start
          no-gutters
        >
          <h1 class="pl-3">
            {{ t('label.issue-analysis-master') }}
          </h1>
          <!-- Or21828：有機体：お気に入りアイコンボタン -->
          <g-base-or21828
            v-bind="or21828"
            class="px-2"
          />
        </c-v-row>
      </c-v-col>
      <c-v-col cols="auto">
        <c-v-row
          no-gutters
          class="align-center"
        >
          <c-v-col
            cols="auto"
            class="pr-2"
          >
            <div>
              <!-- Or21830：有機体：画面メニュー保存ボタン -->
              <g-base-or21830 v-bind="or21830" />
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.issue-analysis-save-btn')"
              ></c-v-tooltip>
            </div>
          </c-v-col>
          <c-v-col cols="auto">
            <!-- OrX0004: 新規操作メニュー -->
            <g-custom-or-x-0004 v-bind="orX0004"></g-custom-or-x-0004>
          </c-v-col>
          <c-v-divider
            vertical
            class="mx-2"
            inset
          />
          <c-v-col cols="auto">
            <!-- 画面メニュー印刷設定アイコンボタン -->
            <g-base-or21832 v-bind="or21832" />
          </c-v-col>
          <c-v-col cols="auto">
            <!-- 計画書オプションメニュー -->
            <g-custom-or-x-0014 v-bind="orX0014"></g-custom-or-x-0014>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="content-area"
    >
      <div
        class="hidden-scroll h-100 custom-user-select ml-5"
        style="width: 274px;"
      >
        <!-- Or00248：有機体：（利用者基本）利用者一覧詳細表示 -->
        <g-base-or00248 v-bind="or00248" />
      </div>
      <c-v-col
        cols="-1"
        class="hidden-scroll h-100 px-2"
      >
        <!-- コンテンツエリア -->
        <!-- <c-v-row>
              <c-v-col>
                <g-base-or41179 v-bind="or41179" />
              </c-v-col>
            </c-v-row> -->
        <c-v-row no-gutters class="second-row ml-6" >
          <c-v-col cols="auto">
            <!-- 事業所 -->
            <g-base-or41179
              class="custom-select-business"
              v-bind="or41179"
            />
          </c-v-col>
          <c-v-col
            v-show="localFlg.showPlanningPeriodFlg"
            cols="auto"
          >
            <!-- 計画対象期間 -->
            <g-custom-or-X0007
              v-bind="orX0007"
              :oneway-model-value="localOneway.orX0007Oneway"
              :parent-method="async () => await _save()"
            />
          </c-v-col>
          <c-v-col
            v-show="localFlg.showEntryDateFlg"
            cols="auto"
          >
            <!-- 記入日選択 -->
            <g-custom-orX0010
              v-bind="orX0010_1"
              v-model="localComponents.orX0010"
              :oneway-model-value="{ ...localOneway.orX0010Oneway }"
              class="custom-required"
            />
          </c-v-col>
          <c-v-col
            v-show="localFlg.showAuthorFlg"
            cols="auto"
            class="tanto-col"
          >
            <!-- 作成者 -->
            <g-custom-orX0009
              v-bind="orX0009_1"
              v-model="localComponents.orX0009"
              :oneway-model-value="localOneway.orX0009Oneway"
            />
          </c-v-col>
          <c-v-col
            v-show="localFlg.showHistoryFlg"
            cols="auto"
          >
            <!-- 履歴 -->
            <g-custom-or52570
              v-bind="or52570_1"
              :oneway-model-value="localOneway.or52570Oneway"
              :is-edit="isEditNavControl(watchedComponents)"
              :parent-method="async () => await _save()"
            />
          </c-v-col>
        </c-v-row>
        <c-v-divider class="divider-class" />
        <!-- 入力フォーム -->
        <div
          v-show="localFlg.showShousaiFormFlg"
          class="mt-5 ml-6"
        >
          <g-custom-or52572
            v-bind="or52572_1"
            ref="or52572Ref"
            v-model="local.or52572"
            :oneway-model-value="localOneway.or52572Oneway"
            @shousai-change="handleShousaiChange"
          />
        </div>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <!-- Or21814 確認１ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <!--Or52653 課題分析複写-->
  <g-custom-or52653
    v-if="showDialogOr52653"
    v-bind="or52653"
    :oneway-model-value="localOneway.or52653Oneway"
    @on-confirm="onConfirm"
  />
</template>
<style scoped lang="scss">
@use '@/styles/base.scss';
$margin-common: 48px;
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}
.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}

.divider-class {
  border-width: thin;
  margin: 12px 0px;
}

.content {
  padding: 5px 0px;
}
// 上の2行目のスタイル
.second-row {
  margin-top: 0px;
  gap: 25px;
  align-items: baseline;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
:deep(.v-field__overlay) {
  background-color: rgb(var(--v-theme-secondaryBackground));
}

.custom-select-business {
  padding: 0px !important;
}

:deep(.custom-select-business .v-row) {
  margin-bottom: 4px !important;
}
:deep(.custom-select-business .v-sheet) {
  width: 203px !important;
  margin-right: 0px !important;
}
:deep(.custom-select-business .v-input) {
  width: 203px !important;
}
:deep(.tanto-col .shoku-field .shoku-label) {
  min-width: 112px !important;
}
:deep(.custom-required) {
  .item-label::after {
    content: '必須';
    color: #ed6809;
    font-size: 12px;
    padding-left: 4px;
  }
}
:deep(.custom-user-select) {
  display: flex;
  justify-content: end;
}

.common-layout {
  // 背景色：テーマの背景色を使用
  background-color: rgb(var(--v-theme-background)) !important;
  // 幅：内容で広く
  min-height: max-content !important;
  display: flex;
  flex-direction: column;
  // 画面トップの固定ヘッダー
  .sticky-header {
    position: sticky; // スクロール時に固定
    top: $margin-common; // トップからの距離
    z-index: 999; // 他の要素よりも上に表示
    background: rgb(var(--v-theme-background)); // テーマの背景色
    width: 100%; // 全幅
  }
}
</style>
