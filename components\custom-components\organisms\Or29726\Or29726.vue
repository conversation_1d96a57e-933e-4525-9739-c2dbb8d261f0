<script setup lang="ts">
/**
 * Or29726:課題検討複写モーダル
 * GUI00654_課題検討複写
 *
 * @description
 * 課題検討複写モーダル
 *
 * <AUTHOR> HOANG SY TOAN
 */

// ==============================
// インポート
// ==============================
import { reactive, ref, watch, onMounted, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { Or29726Const } from './Or29726.constants'
import type { Or29726StateType, Or27754RefType } from './Or29726.type'
import { useScreenStore, useScreenOneWayBind, useSetupChildProps } from '#imports'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type {
  Or29726OnewayType,
  HistType,
  TableDataType,
  PlanPeriodType,
} from '~/types/cmn/business/components/Or29726Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  IssueReviewCopyHistorySelectInEntity,
  IssueReviewCopyHistorySelectOutEntity,
} from '~/repositories/cmn/entities/IssueReviewCopyHistorySelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'

import type { OrX0077OnewayType } from '~/types/cmn/business/components/OrX0077Type'
import type { Or27754OnewayType } from '~/types/cmn/business/components/Or27754Type'
import type {
  ReviewTableCopyHistoryInfoSelectInEntity,
  ReviewTableCopyHistoryInfoSelectOutEntity,
} from '~/repositories/cmn/entities/ReviewTableCopyHistoryInfoSelectEntity'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'

// ==============================
// 変数定義
// ==============================

// ----------- i18n・ユーティリティ -----------
// i18nやユーティリティ関数の初期化
const { t } = useI18n()

// ----------- emit・props -----------
// 親コンポーネントとのイベント・プロパティ定義
const emit = defineEmits(['copyData'])
interface Props {
  onewayModelValue: Or29726OnewayType
  uniqueCpId: string
}

const screenStore = useScreenStore()
const pageComponent = screenStore.screen().supplement.pageComponent

const props = defineProps<Props>()

// ----------- OneWay初期値 -----------
// OneWayデータの初期値設定
const defaultOnewayModelValue: Or29726OnewayType = {
  kikanFlg: '1',
  svJigyoId: '',
  userId: '',
  syubetsuId: '',
  shisetuId: '',
  mstKbn: '',
  kaiteiKbn: '',
}

// ----------- ローカル状態 -----------
// ローカル状態管理（reactiveで管理）
const localOneway = reactive({
  Or29726: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 複写テンプレート
  orx0077Oneway: {
    mo00024Oneway: {
      maxWidth: '1680px',
      persistent: true,
      showCloseBtn: true,
      tooltipTextCloseBtn: t('tooltip.screen-close'),
      mo01344Oneway: {
        name: 'Or29726',
        toolbarTitle: t('label.issues-consider-duplicate'),
        toolbarName: 'Or29726ToolBar',
        toolbarTitleCenteredFlg: false,
        showCardActions: true,
      },
    } as Mo00024OnewayType,
  } as OrX0077OnewayType,
  tableData: {
    histList: [],
    planPeriodList: [],
    planPeriodId: '0',
    comprehensivePlanInfoList: [],
    ...props.onewayModelValue,
  } as TableDataType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('label.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  mo00609OverwriteBtnOneway: {
    btnLabel: t('label.confirm'),
    width: '90px',
    tooltipText: t('tooltip.confirm'),
  } as Mo00609OnewayType,
  or27754Oneway: {} as Or27754OnewayType,
})

// ----------- 子コンポーネント用ユニークID -----------
// 子コンポーネントのユニークID管理
const orx0077 = ref({ uniqueCpId: OrX0077Const.CP_ID(0) })
const or27754 = ref({ uniqueCpId: '' })
const or27754Ref = ref<Or27754RefType | null>(null)

// ----------- OneWayBind用初期データ -----------
// 子コンポーネントへの初期データ
const or27754OnewayType = {
  officeId: '1',
  officeGroupApplyId: '',
  planPeriodId: 1,
  issuesConsiderId: '',
  itemId: 0,
  event: 1,
  periodProcessKbn: '',
  historyProcessKbn: '',
  userId: localOneway.Or29726.userId,
  businessOperatorId: '',
  masterKbn: '',
  revisionKbn: '',
  cpyFlg: true,
} as Or27754OnewayType

// ----------- その他状態 -----------
// その他の状態変数
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const reversion = ref<string>('')
const showOffice = ref<boolean>(false)
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(0) })
const filteredHeaders = ref()
const periodSelectedRowId = ref<string | null>(null)
const rirekiSelectedRowId = ref<HistType | null>(null)
const userSelectedId = ref<string>(localOneway.Or29726.userId)

// ----------- テーブルヘッダ -----------
// テーブルのヘッダー定義
const headers = [
  {
    title: t('label.plan-period'),
    value: 'select',
    key: 'plainDate',
    width: '90px',
    sortable: false,
  },
  {
    title: t('label.within-the-period-number-of-history'),
    value: '1',
    key: 'numberHis',
    width: '120px',
    sortable: false,
  },
  {
    title: t('label.office-name'),
    value: '1',
    key: 'nameEst',
    width: '80px',
    sortable: false,
  },
]
const header2 = reactive({
  headers: [
    {
      title: t('label.history-selection-assessment-date'),
      value: 'select',
      key: 'soudanYmd',
      width: '190px',
      sortable: false,
    },
    {
      title: t('label.author'),
      value: '1',
      key: 'shokuKnj',
      width: '120px',
      sortable: false,
    },
    {
      title: t('label.office-name'),
      value: '1',
      key: 'nameEst',
      width: '150px',
      sortable: false,
    },
    {
      title: t('label.revision'),
      value: '1',
      key: 'cstName',
      width: '90px',
      sortable: false,
    },
  ],
})

// ==============================
// 計算プロパティ
// ==============================

// ----------- ダイアログ表示フラグ -----------
// ダイアログの表示状態を計算
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

// ==============================
// Pinia・OneWayBind
// ==============================
/**
 * 子コンポーネントのユニークIDを設定する
 */
// 子コンポーネントのプロパティ設定
useSetupChildProps(props.uniqueCpId, {
  [OrX0077Const.CP_ID(1)]: orx0077.value,
})

const { setState } = useScreenOneWayBind<Or29726StateType>({
  cpId: Or29726Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      OrX0077Logic.state.set({
        uniqueCpId: orx0077.value.uniqueCpId,
        state: { isOpen: value ?? Or29726Const.DEFAULT.IS_OPEN },
      })
    },
  },
})

// ==============================
// ウォッチャー
// ==============================

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
// ダイアログの開閉状態を監視
watch(
  () => OrX0077Logic.state.get(orx0077.value.uniqueCpId),
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue?.isOpen) {
      close()
    }
  }
)

// ==============================
// ライフサイクル
// ==============================

/**
 * マウント時初期化処理
 */
// マウント時の初期化処理
onMounted(async () => {
  or27754.value.uniqueCpId = pageComponent.uniqueCpId
  if (localOneway.Or29726.kikanFlg === Or29726Const.FLAG.UNMANAGE_PERIOD) {
    filteredHeaders.value = header2.headers
    showOffice.value = true
  } else {
    filteredHeaders.value = header2.headers.filter((header) => header.key !== Or29726Const.NAME_EST)
  }
  await initCode()
})

// ==============================
// 関数定義
// ==============================

/**
 * 初期化処理
 */
// 初期化処理
async function init() {
  await initByUserId()
}

/**
 * 汎用コード取得APIの初期化
 */
// 汎用コード取得APIの初期化
async function initCode() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [{ mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_REVISION_INFO_SHEET }]
  // 汎用コード取得API実行
  const _ret = await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  const reactiveReversion = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_REVISION_INFO_SHEET
  )
  reversion.value = reactiveReversion[1].label
}

/**
 * APIから取得したデータをTableDataTypeにマッピング
 *
 * @description 利用者IDで初期化
 */
// 利用者IDでデータ初期化
async function initByUserId() {
  const inputData: IssueReviewCopyHistorySelectInEntity = {
    svJigyoIdList: [''],
    userId: userSelectedId.value,
    syubetsuId: localOneway.Or29726.syubetsuId,
    shisetuId: localOneway.Or29726.shisetuId,
    mstKbn: localOneway.Or29726.mstKbn,
  }
  const ret: IssueReviewCopyHistorySelectOutEntity = await ScreenRepository.select(
    'issueReviewCopyHistorySelect',
    inputData
  )

  localOneway.tableData.planPeriodList = ret.data.kknInfo
  await periodSelectRow(localOneway.tableData.planPeriodList[0])
}

/**
 * 履歴データ取得
 */
// 履歴データ取得処理
async function getHistoryData() {
  const inputData: ReviewTableCopyHistoryInfoSelectInEntity = {
    svJigyoIdList: [''],
    userId: userSelectedId.value,
    mstKbn: localOneway.Or29726.mstKbn,
    kaiteiKbn: localOneway.Or29726.kaiteiKbn,
    sc1Id: periodSelectedRowId.value!,
  }

  const ret: ReviewTableCopyHistoryInfoSelectOutEntity = await ScreenRepository.select(
    'reviewTableCopyHistoryInfoSelect',
    inputData
  )

  localOneway.tableData.histList = ret.data.rirekiInfo
  rirekiSelectRow(localOneway.tableData.histList[0])
}

/**
 * 画面を閉じる処理
 */
// 画面を閉じる処理
function close() {
  setState({ isOpen: false })
}

/**
 * 警告ダイアログ表示 (ユーザーのOKクリックを待つ)
 *
 * @param title - ダイアログタイトル
 *
 * @param message - メッセージ
 */
async function showWarningDialog(title: string, message: string): Promise<void> {
  // 警告ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogTitle: title,
      dialogText: message,
      firstBtnLabel: t(Or29726Const.DIALOG_LABEL.OK),
      secondBtnType: Or29726Const.DIALOG_BTN_TYPE.BLANK,
      thirdBtnType: Or29726Const.DIALOG_BTN_TYPE.BLANK,
    },
  })

  // ユーザーがOKをクリックするまで待つ
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      (isOpen) => {
        if (!isOpen) {
          // ダイアログが閉じられたら resolve
          resolve()
        }
      },
      { once: true }
    )
  })
}

/**
 * AC007 確定ボタン押下時の処理
 */
async function onClickConfirm() {
  const histList = localOneway.tableData.histList || []
  const selectedRows = or27754Ref.value?.selectedRows ?? []

  // 取得變數
  const parentKaiteiKbn = localOneway.Or29726.kaiteiKbn // 親画面の改訂区分
  const parentMstKbn = localOneway.Or29726.mstKbn // 親画面のマスタ区分
  const historyKaiteiKbn = rirekiSelectedRowId.value?.kaiteiKbn // 履歴選択行の改訂区分
  const historyMstKbn = rirekiSelectedRowId.value?.mstKbn // 履歴選択行のマスタ区分

  // ==========================================
  // 1. 画面.複写履歴リストの履歴行１行も選択していない場合
  // ==========================================
  if (!histList.length || !rirekiSelectedRowId.value) {
    Or21815Logic.state.set({
      uniqueCpId: or21815.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogTitle: t(Or29726Const.DIALOG_LABEL.WARNING),
        dialogText: t(Or29726Const.MESSAGE_CODE.W_CMN_20831),
        firstBtnLabel: t(Or29726Const.DIALOG_LABEL.OK),
        secondBtnType: Or29726Const.DIALOG_BTN_TYPE.BLANK,
        thirdBtnType: Or29726Const.DIALOG_BTN_TYPE.BLANK,
      },
    })
    return
  }

  // ==========================================
  // 2. 画面.課題検討詳細リストの行が１行も選択されていない場合
  // ==========================================
  if (!selectedRows.length) {
    Or21815Logic.state.set({
      uniqueCpId: or21815.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogTitle: t(Or29726Const.DIALOG_LABEL.WARNING),
        dialogText: t(Or29726Const.MESSAGE_CODE.I_CMN_11302),
        firstBtnLabel: t(Or29726Const.DIALOG_LABEL.OK),
        secondBtnType: Or29726Const.DIALOG_BTN_TYPE.BLANK,
        thirdBtnType: Or29726Const.DIALOG_BTN_TYPE.BLANK,
      },
    })
    return
  }

  // ==========================================
  // 3. 親画面の改訂区分が0:初版 の場合の条件分岐
  // ==========================================
  if (parentKaiteiKbn === Or29726Const.REVISION_KBN.INITIAL) {
    // 3-1. かつ 履歴選択行の改訂区分が1:H31/1 かつ マスタ区分が違う場合
    if (historyKaiteiKbn === Or29726Const.REVISION_KBN.H31_1 && parentMstKbn !== historyMstKbn) {
      await showWarningDialog(
        t(Or29726Const.DIALOG_LABEL.WARNING),
        t(Or29726Const.MESSAGE_CODE.I_CMN_20878)
      )
      close()
      return
    }

    // 3-2. かつ 履歴選択行の改訂区分が1:H31/1 の場合
    if (historyKaiteiKbn === Or29726Const.REVISION_KBN.H31_1) {
      await showWarningDialog(
        t(Or29726Const.DIALOG_LABEL.WARNING),
        t(Or29726Const.MESSAGE_CODE.I_CMN_20800)
      )
      close()
      return
    }

    // 3-3. かつ 履歴選択行の改訂区分が0:初版 かつ マスタ区分が違う場合
    if (historyKaiteiKbn === Or29726Const.REVISION_KBN.INITIAL && parentMstKbn !== historyMstKbn) {
      await showWarningDialog(
        t(Or29726Const.DIALOG_LABEL.WARNING),
        t(Or29726Const.MESSAGE_CODE.I_CMN_20878)
      )
      close()
      return
    }
  }

  // ==========================================
  // 4. 親画面の改訂区分が1:H31/1 の場合の条件分岐
  // ==========================================
  if (parentKaiteiKbn === Or29726Const.REVISION_KBN.H31_1) {
    // 4-1. かつ 履歴選択行の改訂区分が2:R5/10 の場合
    if (historyKaiteiKbn === Or29726Const.REVISION_KBN.R5_10) {
      await showWarningDialog(
        t(Or29726Const.DIALOG_LABEL.CONFIRM),
        t(Or29726Const.MESSAGE_CODE.I_CMN_20875)
      )
      close()
      return
    }

    // 4-2. かつ 履歴選択行の改訂区分が2:R5/10以外 かつ マスタ区分が違う場合
    if (historyKaiteiKbn !== Or29726Const.REVISION_KBN.R5_10 && parentMstKbn !== historyMstKbn) {
      await showWarningDialog(
        t(Or29726Const.DIALOG_LABEL.WARNING),
        t(Or29726Const.MESSAGE_CODE.I_CMN_20878)
      )
      close()
      return
    }
  }

  // ==========================================
  // 5. 親画面の改訂区分が2:R5/10 の場合の条件分岐
  // ==========================================
  if (parentKaiteiKbn === Or29726Const.REVISION_KBN.R5_10) {
    // 5-1. かつ 履歴選択行の改訂区分が1:H31/1 の場合
    if (historyKaiteiKbn === Or29726Const.REVISION_KBN.H31_1) {
      await showWarningDialog(
        t(Or29726Const.DIALOG_LABEL.CONFIRM),
        t(Or29726Const.MESSAGE_CODE.I_CMN_20876)
      )
      close()
      return
    }

    // 5-2. かつ 履歴選択行の改訂区分が0:初版 の場合
    if (historyKaiteiKbn === Or29726Const.REVISION_KBN.INITIAL) {
      await showWarningDialog(
        t(Or29726Const.DIALOG_LABEL.CONFIRM),
        t(Or29726Const.MESSAGE_CODE.I_CMN_20877)
      )
      close()
      return
    }
  }

  // ==========================================
  // 6. 全ての条件をパスした場合 - 通常の確認ダイアログ
  // ==========================================
  const dialogResult = await openConfirmDialog(
    t(Or29726Const.MESSAGE_CODE.I_CMN_10193, [t(Or29726Const.DIALOG_LABEL.COMPREHENSIVE_PLAN)])
  )

  if (dialogResult === Or29726Const.YES) {
    emit('copyData', {
      selectedRows: or27754Ref.value?.selectedRows,
      sc1Id: periodSelectedRowId.value,
      kky1Id: rirekiSelectedRowId.value?.kky1Id,
    })
    close()
    return
  }
  // いいえ: 何もしない
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no）
 */
// 確認ダイアログ表示処理
async function openConfirmDialog(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
      dialogTitle: t('btn.execute-copy'),
      thirdBtnType: 'blank',
      secondBtnLabel: t('btn.no'),
      firstBtnLabel: t('btn.yes'),
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 期間選択
 *
 * @param item - 期間情報
 */
// 期間選択時の処理
async function periodSelectRow(item: PlanPeriodType) {
  periodSelectedRowId.value = item.sc1Id
  await getHistoryData()
}

/**
 * 履歴選択
 *
 * @param item - 履歴情報
 */
// 履歴選択時の処理
function rirekiSelectRow(item: HistType) {
  rirekiSelectedRowId.value = item
  if (or27754Ref.value) {
    or27754Ref.value.local.sc1Id = item.sc1Id ?? ''
    or27754Ref.value.local.kky1Id = item.kky1Id ?? ''
    or27754Ref.value.local.kknSyoriKbn = ''
    or27754Ref.value.local.rirekiSyoriKbn = ''
    or27754Ref.value.local.event = '2'
    or27754Ref.value.commonInfo.svJigyoIdList = [item.svJigyoId ?? '']
    or27754Ref.value.commonInfo.officeId = item.svJigyoId ?? ''
    or27754Ref.value.commonInfo.officeGroupId = ''
    or27754Ref.value.commonInfo.userId = item.userId ?? ''
  }
}

// 監視: 履歴選択時にテーブルデータ取得を実行
watch(
  () => rirekiSelectedRowId.value,
  async (val) => {
    if (
      val &&
      or27754Ref.value &&
      typeof or27754Ref.value.getDataTable === Or29726Const.FUNCTION_TYPE
    ) {
      await nextTick()
      await or27754Ref.value.getDataTable()
    }
  }
)

const isFirstMount = ref(true)

/**
 * 利用者選択
 *
 * @param userSelfId - 利用者ID
 */
// 利用者選択時の処理
async function onChangeUserSelect(userSelfId: string) {
  if (isFirstMount.value) {
    isFirstMount.value = false
    return
  }
  userSelectedId.value = userSelfId
  await init()
}

/**
 * 計画期間表示用
 *
 * @param item - 期間情報
 *
 * @returns 期間文字列
 */
// 計画期間の表示用関数
function getPlanPeriod(item: PlanPeriodType): string {
  return `${item.startYmd ?? ''} ~ ${item.endYmd ?? ''}`
}
</script>

<template>
  <!-- 複写テンプレート -->
  <!-- 複写用テンプレートコンポーネント -->
  <g-custom-or-x-0077
    v-bind="orx0077"
    :oneway-model-value="localOneway.orx0077Oneway"
    @on-change-user-select="onChangeUserSelect"
  >
    <!-- テーブル -->
    <!-- 期間・履歴テーブル表示 -->
    <template #filter>
      <c-v-row
        class="table-data ga-2"
        cols="12"
      >
        <!-- 期間テーブル -->
        <c-v-col
          v-if="localOneway.Or29726.kikanFlg === Or29726Const.FLAG.MANAGE_PERIOD"
          class="pa-0"
        >
          <c-v-data-table
            :items="localOneway.tableData.planPeriodList"
            :headers="headers"
            :hide-default-footer="true"
            class="table-header table-wrapper"
            height="194px"
            :items-per-page="-1"
            hover
            fixed-header
          >
            <!-- 一覧 -->
            <template #item="{ item }">
              <tr
                :class="{ 'row-selected': periodSelectedRowId === item.sc1Id }"
                @click="periodSelectRow(item)"
              >
                <!-- 計画期間 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: getPlanPeriod(item),
                    }"
                  ></base-mo01337>
                </td>
                <!-- 期間内履歴数 -->
                <td class="text-align-right">
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.historyCnt,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 事業所名 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.jigyoRyakuKnj,
                    }"
                  ></base-mo01337>
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
        <!-- 履歴テーブル -->
        <c-v-col :cols="localOneway.Or29726.kikanFlg === Or29726Const.FLAG.UNMANAGE_PERIOD ? 8 : 5">
          <c-v-data-table
            :items="localOneway.tableData.histList"
            :headers="filteredHeaders"
            :hide-default-footer="true"
            class="table-header table-wrapper"
            height="194px"
            :items-per-page="-1"
            hover
            fixed-header
          >
            <template #item="{ item }: { item: HistType }">
              <tr
                :class="{ 'row-selected': rirekiSelectedRowId === item }"
                @click="rirekiSelectRow(item)"
              >
                <!-- 相談日 -->
                <td>
                  <base-mo01337 :oneway-model-value="{ value: item.createYmd }" />
                </td>
                <!-- 作成者 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.plnShokuKnj,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 事業所名 -->
                <td v-if="showOffice">
                  <base-mo01337
                    :oneway-model-value="{
                      value: item.jigyoRyakuKnj,
                    }"
                  ></base-mo01337>
                </td>
                <!-- 改訂 -->
                <td>
                  <base-mo01337
                    :oneway-model-value="{
                      value: reversion,
                    }"
                  ></base-mo01337>
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- 複写body -->
    <!-- 複写メイン部分 -->
    <template #copyMain>
      <c-v-row style="height: 580px; overflow-y: auto; max-height: 580px">
        <c-v-col
          class="copy-body"
          style="overflow-y: auto; max-height: 580px"
        >
          <g-custom-or-27754
            v-if="or27754.uniqueCpId"
            :key="or27754.uniqueCpId"
            ref="or27754Ref"
            :unique-cp-id="or27754.uniqueCpId"
            :oneway-model-value="or27754OnewayType"
            style="max-height: 100%; overflow-y: auto"
          ></g-custom-or-27754>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- フッターボタン -->
    <!-- フッターボタン（閉じる・確定） -->
    <template #footer>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609OverwriteBtnOneway"
          class="mx-2"
          @click="onClickConfirm()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </g-custom-or-x-0077>
  <!-- Or21814:有機体:確認ダイアログ -->
  <!-- 確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21815:有機体:警告ダイアログ -->
  <!-- 警告ダイアログ -->
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
@use '@/styles/base.scss';
@use '@/styles/cmn/dialog-base.scss';

.v-row {
  margin: -8px;
}

:deep(.table-gui653) {
  overflow-y: auto !important;
  max-height: 528px !important;
}

.text-align-right {
  text-align: right;
}

:deep(.copy-body .main-right) {
  margin: 0 !important;
  padding: 0 !important;
  align-self: stretch !important;
}

/* チェックボックス列の幅を調整 */
:deep(.table-checkbox > div),
:deep(.table-checkbox .v-input),
:deep(.table-checkbox) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
}
:deep(.table-checkbox),
:deep(.table-checkbox .v-col) {
  padding: 0 !important;
}
:deep(.table-header .table-checkbox) {
  padding: 0px !important;
}
</style>
