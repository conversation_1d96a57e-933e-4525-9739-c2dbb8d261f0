import type {
  KakeizuElementType,
  KakeizuUserKankeiInfo,
  KazokuzuToolbarItem,
} from '~/components/custom-components/organisms/OrX0211/OrX0211.type'
import type { Kakeizu2Type } from '~/components/custom-components/organisms/OrX0212/OrX0212.type'

/**
 * OrX213：有機体：家族図（表示モード）
 *
 * 単方向バインドのデータ構造
 *
 * <AUTHOR>
 */
export interface OrX0213OnewayType {
  /**
   * ツールバー項目（レンダリング用）
   */
  toolBarItems?: KazokuzuToolbarItem[]
  /**
   * 家族図(簡易モード プレビューとひな形用)
   */
  detailListSimple?: Kakeizu2Type[]
  /**
   * 家族図(詳細モード 情報登録とメイ画面用)
   */
  detailListNormal?: KakeizuElementType[]
  /**
   * 家族情報
   */
  kazokuInfo?: KakeizuUserKankeiInfo[]
  /**
   * 本人情報
   */
  selfInfo?: KakeizuUserKankeiInfo
  /**
   * 表示モード
   * 'dialog'(デフォルト),'main', 'temp(ひな形)','preview'
   */
  displayMode?: string
  /**
   * 幅(メイン画面のみ)
   */
  width?: number
  /**
   * 高さ(メイン画面のみ)
   */
  height?: number
  /**
   * 縮小率(メイン画面のみ)
   */
  shukushouritsu?: string
}
