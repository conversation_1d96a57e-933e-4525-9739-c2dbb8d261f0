<script setup lang="ts">
/**
 * Or31806: ｱｾｽﾒﾝﾄ(ｲﾝﾀｰﾗｲ)L画面
 * GUI00776_［アセスメント(インターライ)画面L
 *
 * @description
 * アセスメント(インターライ)画面タブL
 *
 * <AUTHOR> BUI HAI KIEN
 */
import { onMounted, computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or31806Const } from './Or31806.constants'
import { Or31806Logic } from './Or31806.logic'
import type { Or31806StateType, SubInfo, Or31806Twoway } from './Or31806.type'
import {
  useSetupChildProps,
  useScreenOneWayBind,
  useScreenInitFlg,
  useScreenTwoWayBind,
  useValidation,
} from '#imports'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '@/types/business/components/Mo00009Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Or31793OnewayType } from '~/types/cmn/business/components/Or31793Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Or30981OnewayType } from '~/types/cmn/business/components/Or30981Type'
import { Or10412Const } from '~/components/custom-components/organisms/Or10412/Or10412.constants'
import type { Or10412Type, Or10412OnewayType } from '~/types/cmn/business/components/Or10412Type'
import { Or10412Logic } from '~/components/custom-components/organisms/Or10412/Or10412.logic'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type { Or31806OnewayType } from '~/types/cmn/business/components/Or31806Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  AssessmentInterRAILUpdateOutEntity,
  AssessmentInterRAILInEntity,
  AssessmentInterRAILInsertInEntity,
  AssessmentInterRAILOutEntity,
  Ljyouho,
} from '~/repositories/cmn/entities/AssessmentInterRAILEntity'
import type {
  AssessmentInterRAIAHistorySelectEntity,
  AssessmentInterRAIAHistorySelectOutEntity,
} from '~/repositories/cmn/entities/assessmentInterRAIAHistoryEntity'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { TransmitParam } from '~/types/cmn/business/components/TeX0003Type'
import type {
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or30981Const } from '~/components/custom-components/organisms/Or30981/Or30981.constants'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { Mo01355OnewayType } from '@/types/business/components/Mo01355Type'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or31806OnewayType
  uniqueCpId: string
}

/**
 * 親コンポーネントから受け取るプロパティの定義
 */
const props: Props = defineProps<Props>()
const { byteLength } = useValidation()
/**************************************************
 * 変数定義
 **************************************************/

/**
 * 国際化対応のため、i18n の翻訳関数を取得
 */
const { t } = useI18n()

/**
 * 画面初期表示フラグを取得
 */
const isInit = useScreenInitFlg()

/**
 * 子コンポーネント（Or10412）用の一意ID変数
 */
const or10412 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })
const or30981 = ref([
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
])

/**
 * 子コンポーネント（Or21814）用の一意ID変数（定数で初期化）
 */
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

/**
 * Or31806の単方向バインドモデルのデフォルト値を定義
 */
const defaultOnewayModelValue: Or31806OnewayType = {
  periodManageFlag: '0',
  shisetuId: '',
  userId: '',
  officeId: '',
  syubetsuId: '',
  applicableOfficeIdList: [],
  OfficeId: '',
  applicableOfficeGroupId: '',
  planPeriodId: '',
  historyId: '',
  surveyAssessmentKind: '3',
}

/**
 * Or31806のモデル値のデフォルト値を定義
 */
const defaultModelValue = {
  // アセスメント種別リスト
  surveyAssessmentKindList: [] as CodeType[],
}

//セクションキーの型定義
type SectionKey = `L${1 | 2 | 3 | 4 | 5 | 6 | 7}`

/**
 * セクションの設定情報を定義
 */
const sectionConfig: {
  key: SectionKey
  titleLabel: string
  subtitleLabel?: string
  buttonLabels: string[]
  max: number
  layoutType: string
  contentWidth?: string
  radioWidth?: string
  rows: number
}[] = [
  {
    key: 'L1',
    titleLabel: 'label.L-skin-condition-section-L1-title',
    buttonLabels: ['0', '3', '1', '4', '2', '5'],
    max: 5,
    radioWidth: '494px',
    contentWidth: '1016px',
    layoutType: '3',
    rows: 5,
  },
  {
    key: 'L2',
    titleLabel: 'label.L-skin-condition-section-L2-title',
    buttonLabels: ['0', '1'],
    max: 1,
    radioWidth: '180px',
    contentWidth: '386px',
    layoutType: '2',
    rows: 2,
  },
  {
    key: 'L3',
    titleLabel: 'label.L-skin-condition-section-L3-title',
    subtitleLabel: 'label.L-skin-condition-section-L3-subtitle',
    buttonLabels: ['0', '1'],
    max: 1,
    radioWidth: '180px',
    contentWidth: '386px',
    layoutType: '2',
    rows: 2,
  },
  {
    key: 'L4',
    titleLabel: 'label.L-skin-condition-section-L4-title',
    subtitleLabel: 'label.L-skin-condition-section-L4-subtitle',
    buttonLabels: ['0', '1'],
    max: 1,
    radioWidth: '180px',
    contentWidth: '386px',
    layoutType: '2',
    rows: 2,
  },
  {
    key: 'L5',
    titleLabel: 'label.L-skin-condition-section-L5-title',
    buttonLabels: ['0', '1'],
    max: 1,
    radioWidth: '180px',
    contentWidth: '386px',
    layoutType: '2',
    rows: 2,
  },
  {
    key: 'L6',
    titleLabel: 'label.L-skin-condition-section-L6-title',
    subtitleLabel: 'label.L-skin-condition-section-L6-subtitle',
    buttonLabels: ['0', '1'],
    max: 1,
    radioWidth: '180px',
    contentWidth: '386px',
    layoutType: '2',
    rows: 2,
  },
  {
    key: 'L7',
    titleLabel: 'label.L-skin-condition-section-L7-title',
    subtitleLabel: 'label.L-skin-condition-section-L7-subtitle',
    buttonLabels: ['0', '1', '2', '3', '4'],
    max: 4,
    radioWidth: '642px',
    contentWidth: '645px',
    layoutType: '3',
    rows: 5,
  },
]

/**
 * セクション設定（sectionConfig）に基づいて片方向バインド用ローカルモデルを生成
 */
const localModelOneway = sectionConfig.map((config) => {
  const key = config.key
  return {
    key,
    min: 0,
    max: config.max,
    title: {
      value: t(config.titleLabel),
      valueFontWeight: 'bolder',
      customClass: {
        labelClass: 'asection_left_title_left_label',
        itemClass: '',
      },
    } as Mo01338OnewayType,
    subtitle: config.subtitleLabel
      ? ({
          value: t(config.subtitleLabel),
          customClass: {
            labelClass: 'ml-0',
          } as CustomClass,
        } as Mo01338OnewayType)
      : undefined,
    icon: {
      icon: true,
      btnIcon: 'edit_square',
      width: '24px',
      height: '24px',
    } as Mo00009OnewayType,
    textarea: {
      class: `${key}TextArea`,
      maxlength: '4000',
      showItemLabel: false,
      readonly: true,
      noResize: true,
      autoGrow: false,
      rows: config.rows,
    } as Mo00046OnewayType,
    buttons: {
      btnItems: config.buttonLabels.map((v) => ({
        label: t(`label.L-skin-condition-section-${key}-btn${v}`),
        value: v,
      })),
      layoutType: config.layoutType,
    } as Or31793OnewayType,
    textField: {
      mo00045Oneway: {
        width: '60px',
        maxLength: '1',
        showItemLabel: false,
      },
    } as Mo00038OnewayType,
    contentWidth: config.contentWidth,
    radioWidth: config.radioWidth,
  }
})

/**
 * 各セクション設定に基づき、片方向バインド用ローカルモデルを生成
 */
const localOneway = reactive({
  or31806Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },

  mo00039Oneway: {
    name: '',
    showItemLabel: false,
    hideDetails: true,
    inline: true,
    checkOff: true,
  } as Mo00039OnewayType,

  // 入力対象外のアセスメント項目をグレーで表示します
  mo01338OnewayInputTip: {
    value: '',
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: 'mr-4',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentItem',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // L.皮膚の状態セクション
  mo01338OnewaySkinConditionSectionTitle: {
    value: t('label.L-skin-condition-section-title'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: 'custom-header_title',
      outerStyle: '',
      labelClass: '',
      labelStyle: '',
      itemClass: 'pl-2 mr-4 custom-header_label',
      itemStyle: 'font-size: 14px;',
    } as CustomClass,
  } as Mo01338OnewayType,
  // GUI00787 ［メモ入力］画面の単方向バインドModelValue
  or10412Oneway: {
    selectItemNo: '',
    userId: '',
    fontColor: '',
    historyTable: '',
    historyTableColum: '',
    meMoContent: '',
    textSize: '',
    flag: '',
  } as Or10412OnewayType,
  or30981OnewayType: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    /**
     * 活性/非活性制御(デフォルト: false)
     */
    disabled: false,
    /**
     * メモテキストエリア
     */
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      maxLength: '4000',
      rules: [byteLength(4000)],
      noResize: true,
      autoGrow: false,
    } as OrX0156OnewayType,
    /**
     * ラベル
     */
    mo13380OnewayType: {
      value: t('label.letter-size'),
      valueFontWeight: 'normal',
      customClass: {
        labelStyle: 'display: none',
        itemClass: 'align-center',
      } as CustomClass,
    } as Mo01338OnewayType,
    /**
     * モーダルでカラーピッカー
     */
    mo01355Oneway: {} as Mo01355OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  } as Or30981OnewayType,
  or51775Oneway: {
    title: t('label.memo'),
    screenId: 'GUI00769',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
const currentTypeMemo = ref()
const careTargetInputSupportIconClick = (type: string) => {
  currentTypeMemo.value = type
  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}
const handleConfirmText = (data: Or51775ConfirmType) => {
  if (data.type === '0') {
    refValue.value!.find((item) => item.key === currentTypeMemo.value)!.memoInput.content +=
      data.value
  } else if (data.type === '1') {
    refValue.value!.find((item) => item.key === currentTypeMemo.value)!.memoInput.content =
      data.value
  }
}
/**
 * 削除区分（未選択状態で初期化）
 */
const deleteKbn = ref('')

/**
 * 画面遷移時のパラメータ（システム共通情報）
 */
const commomInfo: TransmitParam = {
  executeFlag: 'getData',
  kikanKanriFlg: '',
  houjinId: '',
  shisetuId: '',
  userId: '',
  svJigyoId: '',
  kijunbiYmd: '',
  sakuseiId: '',
  historyModifiedCnt: '',
  historyInfo: {} as HistoryInfoEntity,
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  deleteBtnValue: '',
  raiId: '',
  pageFlag: '',
}

/**
 * レスポンス情報の格納用オブジェクト
 */
const respInfo = {
  executeFlag: '',
  kikanKanriFlg: '',
  houjinId: '1',
  shisetuId: '1',
  userId: '1',
  svJigyoId: '1',
  kijunbiYmd: '',
  sakuseiId: '1',
  historyModifiedCnt: '',
  // 計画期間情報
  planPeriodInfo: {
    sc1Id: '0',
    periodNo: '0',
    periodCnt: '0',
    startYmd: '',
    endYmd: '',
  } as PlanPeriodInfoEntity,
  // 履歴情報
  historyInfo: {} as HistoryInfoEntity,
  // サブ情報
  subInfo: {
    raiId: '',
    l1: '',
    l2: '',
    l3: '',
    l4: '',
    l5: '',
    l6: '',
    l7: '',
    l1MemoKnj: '',
    l1MemoFont: '',
    l1MemoColor: '',
    l2MemoKnj: '',
    l2MemoFont: '',
    l2MemoColor: '',
    l3MemoKnj: '',
    l3MemoFont: '',
    l3MemoColor: '',
    l4MemoKnj: '',
    l4MemoFont: '',
    l4MemoColor: '',
    l5MemoKnj: '',
    l5MemoFont: '',
    l5MemoColor: '',
    l6MemoKnj: '',
    l6MemoFont: '',
    l6MemoColor: '',
    l7MemoKnj: '',
    l7MemoFont: '',
    l7MemoColor: '',
  } as SubInfo,
}
/**
 * 画面の双方向バインド用ref
 */
const { refValue } = useScreenTwoWayBind<Or31806Twoway[]>({
  cpId: Or31806Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

/**
 * GUI00787［メモ入力］画面の双方向バインド用データモデル
 */
const or10412Type = ref<Or10412Type>({
  selectItemNo: '',
  userId: '',
  fontColor: '',
  historyTable: '',
  historyTableColum: '',
  meMoContent: '',
  textSize: '',
  flag: '',
})
/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or31806StateType>({
  cpId: Or31806Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 画面項目変更時のパラメータ受け取り処理
     *
     * @param value - 画面から渡される更新パラメータ
     */
    param: (value) => {
      if (value) {
        Object.assign(commomInfo, value)
        Object.assign(respInfo, value)
      }
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          void save()
          break
        // 新規
        case 'add':
          void add()
          break
        // 複写
        case 'copy':
          void copy()
          break
        // 削除
        case 'delete':
          void del()
          break
        // データ再取得
        case 'getData':
          void getData()
          break
        default:
          break
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or10412Const.CP_ID(1)]: or10412.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or30981Const.CP_ID(0)]: or30981.value[0],
  [Or30981Const.CP_ID(1)]: or30981.value[1],
  [Or30981Const.CP_ID(2)]: or30981.value[2],
  [Or30981Const.CP_ID(3)]: or30981.value[3],
  [Or30981Const.CP_ID(4)]: or30981.value[4],
  [Or30981Const.CP_ID(5)]: or30981.value[5],
  [Or30981Const.CP_ID(6)]: or30981.value[6],
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * GUI00787［メモ入力］画面の表示可否（Or10412ダイアログの開閉状態）を判定するフラグ
 */
const showDialogOr10412 = computed(() => {
  // Or10412のダイアログ開閉状態
  return Or10412Logic.state.get(or10412.value.uniqueCpId)?.isOpen ?? false
})

onMounted(() => {
  if (isInit) {
    // 汎用コードマスタデータを取得し初期化
    void systemCodeSelect()
  }
})

/**
 * 汎用コード取得API実行
 */
const systemCodeSelect = async () => {
  const cmnMCdKbnId = CmnMCdKbnId as { M_CD_KBN_ID_ASSESSMENT_KIND: number }
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
  ]

  // コード取得
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // アセスメント種別
  defaultModelValue.surveyAssessmentKindList = CmnSystemCodeRepository.filter(
    cmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )
  // 親情報.調査アセスメント種別により、コードマスタの区分名称を表示する
  for (const item of defaultModelValue.surveyAssessmentKindList) {
    if (localOneway.or31806Oneway.surveyAssessmentKind === item.value) {
      localOneway.mo01338OnewayInputTip.value = item.label
      break
    }
  }
}

/**
 * AC004_「新規ボタン」押下
 */
const add = () => {}

/**
 * 画面初期表示時に使用するアセスメント履歴情報を取得する処理
 *
 * @returns 取得した履歴情報データ（正常時のみ）
 */
const getHistoryInfo = async () => {
  const inputData: AssessmentInterRAIAHistorySelectEntity = {
    raiId: commomInfo.historyInfo.raiId,
  }
  const resData: AssessmentInterRAIAHistorySelectOutEntity = await ScreenRepository.select(
    Or31806Const.STATUS_CODE.NAME_ACTION_SELECT_H,
    inputData
  )
  if (resData.statusCode === Or31806Const.STATUS_CODE.SUCCESS) {
    return resData.data
  }
}

/**
 * 「保存ボタン」押下き確認ダイアログ
 *
 * @param message -情報を提示します
 */
const showOr21814Msg = (message: string): void => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: message,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.cancel'),
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}

/**
 * AC003_「保存ボタン」押下
 */
const save = async () => {
  // 取得したインターライ方式履歴情報<>NULL
  const historyInfo = await getHistoryInfo()
  if (historyInfo) {
    // 選定アセスメント種別 > 0
    if (Number(historyInfo?.capType) > 0) {
      // 検討アセスメント種別 > 0
      if (Number(historyInfo?.capType) > 0) {
        deleteKbn.value = '1'
        showOr21814Msg(t('message.i-cmn-11267'))
      }
      // 検討アセスメント種別 <= 0
      if (Number(historyInfo?.capType) <= 0) {
        deleteKbn.value = '0'
        showOr21814Msg(t('message.i-cmn-11266'))
      }
    } else {
      deleteKbn.value = ''
      void onSave()
    }
  }
}

/**
 * 保存ボタン押下
 */
const onSave = async () => {
  // 入力値処理です
  const inputData: AssessmentInterRAILInsertInEntity = {
    houjinId: parseInt(commomInfo.houjinId) || 0,
    shisetuId: parseInt(commomInfo.shisetuId) || 0,
    userId: parseInt(commomInfo.userId) || 0,
    svJigyoId: parseInt(commomInfo.userId) || 0,
    subKbn: 'L',
    updateKbn: '',
    historyUpdateKbn: '',
    deleteKbn: 0,
    sc1Id: parseInt(commomInfo.historyInfo.sc1Id) || 0,
    kijunbiYMD: commomInfo.kijunbiYmd || '',
    sakuseiId: parseInt(commomInfo.sakuseiId) || 0,
    assType: parseInt(commomInfo.historyInfo.assType) || 0,
    Ljyouho: {} as Ljyouho,
  }

  inputData.Ljyouho = respInfo.subInfo

  const resData: AssessmentInterRAILUpdateOutEntity = await ScreenRepository.insert(
    Or31806Const.STATUS_CODE.NAME_ACTION_SAVE,
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === Or31806Const.STATUS_CODE.SUCCESS) {
    // 画面情報再取得
  }
}

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      void onSave()
    }
  }
)

/**
 * AC005_「複写ボタン」押下
 */
const copy = async () => {
  // 入力値処理です
  const inputData: AssessmentInterRAILInsertInEntity = {
    houjinId: parseInt(commomInfo.houjinId) || 0,
    shisetuId: parseInt(commomInfo.shisetuId) || 0,
    userId: parseInt(commomInfo.userId) || 0,
    svJigyoId: parseInt(commomInfo.userId) || 0,
    subKbn: 'L',
    updateKbn: '',
    historyUpdateKbn: '',
    deleteKbn: 0,
    sc1Id: parseInt(commomInfo.historyInfo.sc1Id) || 0,
    kijunbiYMD: commomInfo.kijunbiYmd || '',
    sakuseiId: parseInt(commomInfo.sakuseiId) || 0,
    assType: parseInt(commomInfo.historyInfo.assType) || 0,
    Ljyouho: {} as Ljyouho,
  }

  inputData.Ljyouho = respInfo.subInfo

  const resData: AssessmentInterRAILUpdateOutEntity = await ScreenRepository.insert(
    Or31806Const.STATUS_CODE.NAME_ACTION_ADD,
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === Or31806Const.STATUS_CODE.SUCCESS) {
    // 画面情報再取得
  }
  // TODO GUI00807 アセスメント複写画面未作成
}

/**
 * AC011_「削除」押下
 */
const del = async () => {
  // 入力値処理です
  const inputData: AssessmentInterRAILInsertInEntity = {
    houjinId: parseInt(commomInfo.houjinId) || 0,
    shisetuId: parseInt(commomInfo.shisetuId) || 0,
    userId: parseInt(commomInfo.userId) || 0,
    svJigyoId: parseInt(commomInfo.userId) || 0,
    subKbn: 'L',
    updateKbn: '',
    historyUpdateKbn: '',
    deleteKbn: 0,
    sc1Id: parseInt(commomInfo.historyInfo.sc1Id) || 0,
    kijunbiYMD: commomInfo.kijunbiYmd || '',
    sakuseiId: parseInt(commomInfo.sakuseiId) || 0,
    assType: parseInt(commomInfo.historyInfo.assType) || 0,
    Ljyouho: {} as Ljyouho,
  }
  inputData.Ljyouho = respInfo.subInfo

  const resData: AssessmentInterRAILUpdateOutEntity = await ScreenRepository.insert(
    Or31806Const.STATUS_CODE.NAME_ACTION_DELETE,
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === Or31806Const.STATUS_CODE.SUCCESS) {
    // 画面情報再取得
  }
  // TODO 画面「L」タブに対し、更新区分を「D:削除」にする。
}

/**
 * AC003-2-1_アセスメント(インターライ)画面履歴の最新情報を取得
 */
const getData = async () => {
  const inputData: AssessmentInterRAILInEntity = {
    raiId: commomInfo.historyInfo.raiId,
  }
  const resData: AssessmentInterRAILOutEntity = await ScreenRepository.select(
    Or31806Const.STATUS_CODE.NAME_ACTION_SELECT,
    inputData
  )
  if (resData.statusCode === Or31806Const.STATUS_CODE.SUCCESS && resData.data) {
    const data = resData.data
    const subInfoList = data.subInfoL
    const subInfo = subInfoList
    respInfo.subInfo = subInfo
    const dataMapped = refValue.value!.map((item) => {
      const memoFontSize = `${subInfo[item.key + 'MemoFont']}`
      const memoColor = subInfo[item.key + 'MemoColor']
      item.memoInput = {
        content: subInfo[item.key + 'MemoKnj'],
        fontSize: memoFontSize,
        fontColor: memoColor,
      }
      item.radio = subInfo[item.key]
      return item
    })
    Or31806Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: cloneDeep(dataMapped),
      isInit: true,
    })
  }
}
</script>

<template>
  <c-v-row
    no-gutters
    style="padding-top: 16px"
    class="main-container"
  >
    <div class="main-container_left">
      <!-- ヘッダ -->
      <c-v-row
        class="assessment-type-title justify-end"
        no-gutters
      >
        {{ localOneway.mo01338OnewayInputTip.value }}
      </c-v-row>
      <!-- 活動タイトル行 -->
      <div class="section-title">
        {{ localOneway.mo01338OnewaySkinConditionSectionTitle.value }}
      </div>
      <div>
        <div
          v-for="(section, index) in localModelOneway"
          :key="section.key"
        >
          <div class="sub-title">{{ section.title.value }}</div>
          <div class="f-container">
            <div
              v-if="section.subtitle"
              class="sub-f-title mb-4"
            >
              {{ section.subtitle.value }}
            </div>
            <base-mo00039
              v-model="refValue![index].radio"
              :oneway-model-value="localOneway.mo00039Oneway"
              class="custom-radio"
              :style="`width: ${section?.contentWidth ?? ''}`"
            >
              <base-at-radio
                v-for="item in section.buttons.btnItems"
                :key="item.value"
                :value="item.value"
                :label="item.label"
                :class="{
                  isChecked: refValue![index].radio === item.value,
                }"
                :style="`width: ${section?.radioWidth ?? ''}`"
              ></base-at-radio>
            </base-mo00039>
            <div class="mt-3">
              <g-custom-or-30981
                v-model="refValue![index].memoInput"
                v-bind="or30981[index]"
                :oneway-model-value="localOneway.or30981OnewayType"
                @on-click-edit-btn="careTargetInputSupportIconClick(refValue![index].key)"
              />
            </div>
          </div>
        </div>
        <!-- interRAIロゴ -->
        <c-v-row
          class="mt-4"
          no-gutters
        >
          <c-v-col cols="12">
            <c-v-img
              width="133"
              aspect-ratio="16/9"
              cover
              :src="InterRAI"
              style="float: right"
            ></c-v-img>
          </c-v-col>
        </c-v-row>
      </div>
    </div>
  </c-v-row>
  <!-- GUI00787 ［メモ入力］画面 -->
  <g-custom-or-10412
    v-if="showDialogOr10412"
    v-bind="or10412"
    v-model="or10412Type"
    :oneway-model-value="localOneway.or10412Oneway"
  ></g-custom-or-10412>
  <g-base-or21814 v-bind="or21814" />
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleConfirmText"
  />
</template>

<style scoped lang="scss">
.assessment-type-title {
  font-size: 24px;
}
.section-title {
  display: flex;
  align-items: center;
  width: 1080px;
  background-color: #fff;
  padding-left: 24px;
  height: 73px;
  color: #333333;
  font-size: 18px;
  font-weight: 700;
}
.sub-title {
  display: flex;
  align-items: center;
  height: 48px;
  font-size: 17px;
  color: #333333;
  font-weight: 700;
  padding-left: 24px;
  background-color: #e6e6e6;
}
.f-container {
  background-color: #fff;
  padding: 24px 24px 24px 48px;
  color: #333333;
  .sub-f-title {
    font-weight: 700;
  }
  .info-select {
    border: #333333 1px solid;
    padding: 8px 16px;
    font-weight: 700;
    margin-top: 20px;
    line-height: 16px;
  }
  .select-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.right-input :deep(input) {
  text-align: right !important;
}

.main-container {
  padding-top: 16px;
}

.main-container_left {
  width: 1080px;
  :has(> .contentItem) {
    text-align: right;
    :deep(.item-label) {
      font-size: 18px !important;
    }
  }
}

.title {
  :has(> .custom-header_label) {
    font-size: 16px;
    margin-right: 16px;
  }
}

.container {
  overflow-y: auto;
  max-height: 582px;

  :has(.contentItem) {
    :deep(.item-label) {
      font-weight: bolder;
    }
  }
}

.width-160 {
  width: 160px;
}
:deep(.custom-radio) {
  &.inline {
    .v-radio {
      min-width: 180px;
    }
  }
  .v-selection-control-group {
    gap: 16px;
  }
  .v-radio {
    padding: 0 8px;
    border: 1px solid #8a8a8a;
    border-radius: 4px;
    .v-label {
      width: 100%;
    }
    &.isChecked {
      background-color: #ecf3fd;
      border: 1px solid #0760e6;
    }
  }
}
</style>
