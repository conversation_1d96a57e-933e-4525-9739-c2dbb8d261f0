import type { Mo01274Type } from '@/types/business/components/Mo01274Type'
/**
 * Or26426：有機体：認定調査票特記事項ダイアログモーダル
 * GUI01272_認定調査票特記事項
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or26426StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * Pinia
 */
export interface Or26426DataValue {
  /**
   * 項目1 ~ 項目7
   */
  showItemList: ShowItemList[]
}

/**
 * 項目1 ~ 項目7
 */
export interface ShowItemList {
  /**
   * 特記事項内容リスト
   */
  nTokkiList: NTokkiList[]
}

/**
 * 特記事項内容リスト
 */
export interface NTokkiList {
  /**
   * カウンター
   */
  counter: string
  /**
   * 認定（特記：大）
   */
  n1tCd: string
  /**
   * 認定（特記：小）
   */
  n2tCd: Mo01274Type
  /**
   * 特記事項
   */
  memoKnj: Mo01274Type
  /**
   * 表示順
   */
  seqNo: string
  /**
   * 更新区分
   */
  updateKbn: string
}
