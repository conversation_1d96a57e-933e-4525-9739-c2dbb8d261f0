<script setup lang="ts">
/**
 * Or31909:有機体:アセスメント(インターライ)画面E
 * GUI00768_アセスメント(インターライ)画面E
 *
 * @description
 * GUI00768_アセスメント(インターライ)画面E
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, onMounted, ref, computed } from 'vue'

import { Or30981Const } from '../Or30981/Or30981.constants'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { TeX0003Const } from '../../template/Tex0003/TeX0003.constants'
import type { Or31909StateType, Or31909TwoWayType, RenderDataListType } from './Or31909.type'
import { Or31909Const } from './Or31909.constants'
import type { Or31909OnewayType } from '~/types/cmn/business/components/Or31909Type'
import { CustomClass } from '~/types/CustomClassType'
import {
  useCmnCom,
  useCmnRouteCom,
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import type {
  AssessmentInterRAIESelectEntity,
  AssessmentInterRAIESelectOutEntity,
  AssessmentInterRAIEUpdateEntity,
  AssessmentInterRAIEUpdateOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEEntity'

import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { ResBodyStatusCode } from '~/constants/api-constants'
import type {
  HistoryInfoEntity,
  PlanPeriodInfoEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import type { OrX0156OnewayType } from '~/types/cmn/business/components/OrX0156Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { OrX0169OnewayType } from '~/types/cmn/business/components/OrX0169Type'
const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or31909OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

const cmnRouteCom = useCmnRouteCom()
// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * 変数定義
 **************************************************/

const { TABLENAME, COLUMNNAME, SCREENID, T1CD, T2CD, T3CD, INDEX, TABNAME } = Or31909Const

// 子コンポーネント用変数
const or51775 = ref({ uniqueCpId: '' })
const or30981ObjMap = reactive([
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
  { uniqueCpId: '' },
])

// 現在選択中のキー
let currentKey = -1

// ロード状態です
const isLoading = ref<boolean>(false)

/** 更新区分 */
let updateKbn = UPDATE_KBN.UPDATE

const locaModelValue: Or31909TwoWayType[] = [
  {
    key: 'e1',
    memoInputvalue: {
      content: '',
      fontSize: '',
      fontColor: '',
    },
    subFormList: [
      {
        key: 'a',
        value: '',
      },
      {
        value: '',
        key: 'b',
      },
      {
        value: '',
        key: 'c',
      },
      {
        value: '',
        key: 'd',
      },
      {
        value: '',
        key: 'e',
      },
      {
        value: '',
        key: 'f',
      },
      {
        value: '',
        key: 'g',
      },
      {
        value: '',
        key: 'h',
      },
      {
        value: '',
        key: 'i',
      },
      {
        value: '',
        key: 'j',
      },
      {
        value: '',
        key: 'k',
      },
    ],
  },
  {
    key: 'e2',
    memoInputvalue: {
      content: '',
      fontSize: '',
      fontColor: '',
    },
    subFormList: [
      {
        value: '',
        key: 'a',
      },
      {
        value: '',
        key: 'b',
      },
      {
        value: '',
        key: 'c',
      },
    ],
  },
  {
    key: 'e3',
    memoInputvalue: {
      content: '',
      fontSize: '',
      fontColor: '',
    },
    subFormList: [
      {
        value: '',
        key: 'a',
      },
      {
        value: '',
        key: 'a',
      },
      {
        value: '',
        key: 'c',
      },
      {
        value: '',
        key: 'd',
      },
      {
        value: '',
        key: 'e',
      },
      {
        value: '',
        key: 'f',
      },
      {
        value: '',
        key: 'g',
      },
    ],
  },
  {
    key: 'e4',
    memoInputvalue: {
      content: '',
      fontSize: '',
      fontColor: '',
    },
    subFormList: [
      {
        value: '',
        key: '',
      },
    ],
  },
]

const renderDataList = reactive<RenderDataListType[]>([
  {
    leftContentLabel: t(
      'label.interRAI-method-care-assessment-table-E-basic-info-section-E1-title'
    ),
    key: 'e1',
    memoInputSetting: { class: 'E1TextArea' },
    rightContentSubTitle: t(
      'label.interRAI-method-care-assessment-table-E-basic-info-section-E1-subtitle'
    ),
    selectOptionsList: [],
    subFormList: [
      {
        label: t('label.E-emotions-and-actions-E1-label-a'),
        key: 'a',
      },
      {
        label: t('label.E-emotions-and-actions-E1-label-b'),
        key: 'b',
      },
      {
        label: t('label.E-emotions-and-actions-E1-label-c'),
        key: 'c',
      },
      {
        label: t('label.E-emotions-and-actions-E1-label-d'),
        key: 'd',
      },
      {
        label: t('label.E-emotions-and-actions-E1-label-e'),
        key: 'e',
      },
      {
        label: t('label.E-emotions-and-actions-E1-label-f'),
        key: 'f',
      },
      {
        label: t('label.E-emotions-and-actions-E1-label-g'),
        key: 'g',
      },
      {
        label: t('label.E-emotions-and-actions-E1-label-h'),
        key: 'h',
      },
      {
        label: t('label.E-emotions-and-actions-E1-label-i'),
        key: 'i',
      },
      {
        label: t('label.E-emotions-and-actions-E1-label-j'),
        key: 'j',
      },
      {
        label: t('label.E-emotions-and-actions-E1-label-k'),
        key: 'k',
      },
    ],
  },
  {
    leftContentLabel: t(
      'label.interRAI-method-care-assessment-table-E-basic-info-section-E2-title'
    ),
    key: 'e2',
    memoInputSetting: { class: 'E2TextArea' },
    rightContentSubTitle: t(
      'label.interRAI-method-care-assessment-table-E-basic-info-section-E2-subtitle'
    ),
    selectOptionsList: [],
    subFormList: [
      {
        label: t('label.E-emotions-and-actions-E2-label-a'),
        key: 'a',
      },
      {
        label: t('label.E-emotions-and-actions-E2-label-b'),
        key: 'b',
      },
      {
        label: t('label.E-emotions-and-actions-E2-label-c'),
        key: 'c',
      },
    ],
  },
  {
    leftContentLabel: t(
      'label.interRAI-method-care-assessment-table-E-basic-info-section-E3-title'
    ),
    key: 'e3',
    memoInputSetting: { class: 'E3TextArea' },
    rightContentSubTitle: t(
      'label.interRAI-method-care-assessment-table-E-basic-info-section-E3-subtitle'
    ),
    selectOptionsList: [],
    subFormList: [
      {
        label: t('label.E-emotions-and-actions-E3-label-a'),
        key: 'a',
      },
      {
        label: t('label.E-emotions-and-actions-E3-label-b'),
        key: 'b',
      },
      {
        label: t('label.E-emotions-and-actions-E3-label-c'),
        key: 'c',
      },
      {
        label: t('label.E-emotions-and-actions-E3-label-d'),
        key: 'd',
      },
      {
        label: t('label.E-emotions-and-actions-E3-label-e'),
        key: 'e',
      },
      {
        label: t('label.E-emotions-and-actions-E3-label-f'),
        key: 'f',
      },
      {
        label: t('label.E-emotions-and-actions-E3-label-g'),
        key: 'g',
      },
    ],
  },
  {
    leftContentLabel: t(
      'label.interRAI-method-care-assessment-table-E-basic-info-section-E4-title'
    ),
    key: 'e4',
    componentType: 'radio',
    memoInputSetting: { rows: 2, maxRows: '2', class: 'E4TextArea' },
    rightContentSubTitle: '',
    selectOptionsList: [],
    subFormList: [
      {
        label: '',
        key: '',
      },
    ],
  },
])

// システム共有情報取得
const commonInfo = {
  executeFlag: '',
  kikanKanriFlg: '',
  kijunbiYmd: '',
  sakuseiId: '',
  historyInfo: {} as HistoryInfoEntity,
  planPeriodInfo: {} as PlanPeriodInfoEntity,
  deleteBtnValue: '',
  assType: '',
  yokaiKbn: '',
  syubetsuId: '',
  tableCreateKbn: '',
  svJigyoKnj: '',
  historyUpdateKbn: '',
  svJigyoId: '',
  userId: '',
  raiId: '',
  subInfoB: {},
  updateKbn: '',
}

const localOneway = reactive({
  isShow: true,
  // アセスメント種別
  assessmentKinds: [] as CodeType[],
  // 気分と行動タイトル
  emotionsAndActionsTitle: {
    /** 値のフォントの太さ */
    valueFontWeight: 'bold',
    /** 値 */
    value: t('label.mood-and-action'),
    /** カスタムクラス */
    customClass: new CustomClass({
      itemClass: 'align-center',
      itemStyle: 'font-size: 18px',
      outerClass: 'py-6 pl-6',
    }),
  },
  // 調査アセスメント種別ラベル
  surveyAssessmentTypeOneway: '',
  subTitleMo01338Oneway: {
    value: '',
    valueFontWeight: 'bolder',
    customClass: {
      itemStyle: ' font-size: 16px',
      itemClass: 'align-center',
    } as CustomClass,
  },
  // メモ入力内容
  mo00046MemoInputOnewayType: {
    /**
     * アイテム
     */
    items: ['5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15'],
    orX0156OnewayType: {
      itemLabel: t('label.memo'),
      showItemLabel: true,
      showDividerLineFlg: true,
      showEditBtnFlg: true,
      isVerticalLabel: true,
      customClass: {
        labelClass: 'pa-0',
      } as CustomClass,
      maxRows: '3',
      rows: '3',
      noResize: true,
      autoGrow: false,
      maxlength: '32000',
    } as OrX0156OnewayType,
    /**
     * 色の設定アイコンボタン
     */
    orX0169OnewayType: {
      scaleSize: '24px',
      useDefaultColorPicker: true,
    } as OrX0169OnewayType,
  },
  // GUI00937 ［メモ入力］画面の単方向バインドModelValue
  or51775Oneway: {} as Or51775OnewayType,
  mo00039Oneway: {
    showItemLabel: false,
    inline: false,
    items: [],
  } as unknown as Mo00039OnewayType,
})

// GUI00787 ［メモ入力］画面の双方向バインドModelValue
const or51775Type = ref<Or51775Type>({
  modelValue: '',
})

/**************************************************
 * Pinia
 **************************************************/

const { refValue } = useScreenTwoWayBind<Or31909TwoWayType[]>({
  cpId: Or31909Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = { ...locaModelValue }

useScreenOneWayBind<Or31909StateType>({
  cpId: Or31909Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (value) {
        Object.assign(commonInfo, value)
      }
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          void onSave()
          break
        // 新規
        case 'add':
          void onAdd()
          break
        // 複写
        case 'copy':
          updateKbn = UPDATE_KBN.CREATE
          void onCopy()
          break
        // 削除
        case 'delete':
          updateKbn = UPDATE_KBN.DELETE
          void onDel()
          break
        // データ再取得
        case 'getData':
          void getInitDataInfo(commonInfo.historyInfo.raiId)
          break
        default:
          break
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or30981Const.CP_ID(1)]: or30981ObjMap[0],
  [Or30981Const.CP_ID(2)]: or30981ObjMap[1],
  [Or30981Const.CP_ID(3)]: or30981ObjMap[2],
  [Or30981Const.CP_ID(4)]: or30981ObjMap[3],
})

/**************************************************
 * Emit
 **************************************************/
/**
 * emit saveEndイベントを定義
 */
const emit = defineEmits(['saveEnd'])

// GUI00787 ［メモ入力］画面表示フラグ
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await initCode()
})

/**
 *  画面データをクリア
 */
const onAdd = () => {
  inputProject()
  useScreenStore().setCpTwoWay({
    cpId: Or31909Const.CP_ID(1),
    uniqueCpId: props.uniqueCpId,
    value: [...locaModelValue],
    isInit: true,
  })
  updateKbn = UPDATE_KBN.CREATE
}

/**
 * 画面項目の初期化
 */
const inputProject = () => {
  locaModelValue.forEach((item) => {
    item.memoInputvalue = {
      content: '',
      fontColor: getDefaultFontColor(),
      fontSize: getDefaultFontSize(),
    }

    item.subFormList = item.subFormList.map((subItem) => {
      return {
        ...subItem,
        value: '',
      }
    })
  })
}
/**
 *  画面初期情報取得
 *
 *   @param raiId - アセスメントID
 */
const getInitDataInfo = async (raiId: string) => {
  updateKbn = UPDATE_KBN.UPDATE
  localOneway.isShow = true
  isLoading.value = true
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentInterRAIESelectEntity = {
    raiId,
  }
  try {
    const resData: AssessmentInterRAIESelectOutEntity = await ScreenRepository.select(
      'assessmentInterRAIESelect',
      inputData
    )
    // 画面情報を設定
    if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
      const subInfoEInfo = resData.data.subInfoE
      if (!subInfoEInfo?.raiId) {
        // 画面の入力フォームの各項目を空白「項目一覧」の初期情報設定でセットする。
        // 画面.更新区分が"C"（新規）に設定する。
        updateKbn = UPDATE_KBN.CREATE
        inputProject()
      } else {
        locaModelValue.forEach((item) => {
          item.memoInputvalue = {
            content: subInfoEInfo[item.key + Or31909Const.DEFAULT.MEMOKNJSTR],
            fontColor: subInfoEInfo[item.key + Or31909Const.DEFAULT.MEMOCOLORSTR],
            fontSize: subInfoEInfo[item.key + Or31909Const.DEFAULT.MEMOFONTSTR],
          }

          item.subFormList = item.subFormList.map((subItem) => {
            return {
              ...subItem,
              value: subInfoEInfo[item.key + subItem.key.toLocaleUpperCase()],
            }
          })
        })
      }
      console.log(locaModelValue,'locaModelValue');

      // 初期値に設定する
      useScreenStore().setCpTwoWay({
        cpId: Or31909Const.CP_ID(1),
        uniqueCpId: props.uniqueCpId,
        value: [...locaModelValue],
        isInit: true,
      })
    }
    isLoading.value = false
  } catch (error) {
    console.log(error)
    isLoading.value = false
  }
}

/**
 * デフォルト文字サイズ
 */
const getDefaultFontSize = (): string => {
  // 初期設定マスタ.アセスメント(インターライ)：文字サイズ
  const fontSize = cmnRouteCom.getInitialSettingMaster()?.assCharSize
  // 文字サイズが「0：小さい」の場合
  if ('0' === fontSize) {
    return '9'
  }
  // 文字サイズが「2：大きい」の場合
  else if ('2' === fontSize) {
    return '15'
  }
  // 上記以外の場合
  else {
    // 12：デフォルト値
    return '12'
  }
}

/**
 * デフォルト文字色
 */
const getDefaultFontColor = (): string => {
  // 初期設定マスタ.アセスメント(インターライ)：文字色
  const fontSize = cmnRouteCom.getInitialSettingMaster()?.assCharColor
  // 初期設定マスタ.アセスメント(インターライ).文字サイズがある場合
  if (fontSize) {
    // 初期設定マスタ.アセスメント(インターライ).文字色
    return fontSize
  }
  // 上記以外の場合
  else {
    // #000000：デフォルト値
    return '0'
  }
}

/**
 *  汎用コード取得API実行
 */
const initCode = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    // うつ、不安、悲しみの 気分の兆候
    { mCdKbnId: CmnMCdKbnId.M_CD_CMN_ID_EMOTION },
    // 利用者自身が応えた気分
    { mCdKbnId: CmnMCdKbnId.M_CD_CMN_ID_USERSELF_EMOTION },
    // 行動の問題
    { mCdKbnId: CmnMCdKbnId.M_CD_CMN_ID_ACTION_ISSUES },
    // 最近3日間における 生活満足度
    { mCdKbnId: CmnMCdKbnId.M_CD_CMN_ID_LIFE_SATISFACTION },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  localOneway.assessmentKinds = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )
  const temData = [
    CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_CMN_ID_EMOTION),
    CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_CMN_ID_USERSELF_EMOTION),
    CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_CMN_ID_ACTION_ISSUES),
    [],
  ]
  renderDataList.map((item, index) => {
    item.selectOptionsList = temData[index].map((el) => {
      return {
        btnValue: el.value,
        btnLabel: el.label,
      }
    })
  })
  localOneway.mo00039Oneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_CMN_ID_LIFE_SATISFACTION
  )
  // アセスメント種別
  localOneway.surveyAssessmentTypeOneway =
    localOneway.assessmentKinds.find((item) => item.value === commonInfo.historyInfo.assType)
      ?.label ?? localOneway.assessmentKinds[0].label
}

/**
 * 保存ボタン押下
 */
const onSave = async () => {
  // 更新データ作成
  const inputData: AssessmentInterRAIEUpdateEntity = {
    houjinId: systemCommonsStore.getHoujinId ?? '',
    shisetuId: systemCommonsStore.getShisetuId ?? '',
    userId: systemCommonsStore.getUserId ?? '',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    syubetsuId: commonInfo.syubetsuId,
    lbKikan: commonInfo.kikanKanriFlg,
    updateKbn,
    historyUpdateKbn: commonInfo.historyUpdateKbn ? commonInfo.historyUpdateKbn : UPDATE_KBN.UPDATE,
    kijunbiYmd: commonInfo.kijunbiYmd,
    sakuseiId: commonInfo.sakuseiId,
    raiId: commonInfo.historyInfo.raiId,
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '',
    sectionName: TeX0003Const.SECTION_NAME,
    gsyscd: systemCommonsStore.getSystemCode ?? '',
    kinouId: systemCommonsStore.getFunctionId ?? '',
    shokuId: systemCommonsStore.getStaffId ?? '',
    kikanKanriFlg: commonInfo.kikanKanriFlg,
    historyNo: commonInfo.historyInfo.krirekiNo,
    svJigyoName: commonInfo.svJigyoKnj,
    periodNo: commonInfo.planPeriodInfo.periodNo,
    startYmd: commonInfo.planPeriodInfo.startYmd ?? '',
    endYmd: commonInfo.planPeriodInfo.endYmd ?? '',
    index: INDEX,
    tabName: TABNAME,
    edocumentUseParam: useCmnCom().getEDocumentParam(),
    edocumentDeleteUseParam: useCmnCom().getEDocumentParam(),
    subKbn: TABNAME,
    tableCreateKbn: commonInfo.tableCreateKbn,
    sc1Id: commonInfo.planPeriodInfo.sc1Id,
    assType: commonInfo.historyInfo.assType,
    subInfoE: {
      e1A: '',
      e1B: '',
      e1C: '',
      e1D: '',
      e1E: '',
      e1F: '',
      e1G: '',
      e1H: '',
      e1I: '',
      e1J: '',
      e1K: '',
      e2A: '',
      e2B: '',
      e2C: '',
      e3A: '',
      e3B: '',
      e3C: '',
      e3D: '',
      e3E: '',
      e3F: '',
      e3G: '',
      e4: '',
      e1memoknj: '',
      e1memofont: '',
      e1memocolor: '',
      e2memoknj: '',
      e2memofont: '',
      e2memocolor: '',
      e3memoknj: '',
      e3memofont: '',
      e3memocolor: '',
      e4memoknj: '',
      e4memofont: '',
      e4memocolor: '',
    },
  }

  // 入力値処理です
  refValue.value!.forEach((item) => {
    item.subFormList.forEach((subItem) => {
      inputData.subInfoE[item.key + subItem.key.toLocaleUpperCase()] = subItem.value
    })
    inputData.subInfoE[item.key + Or31909Const.DEFAULT.MEMOKNJSTR] = item.memoInputvalue.content
    inputData.subInfoE[item.key + Or31909Const.DEFAULT.MEMOCOLORSTR] = item.memoInputvalue.fontColor
    inputData.subInfoE[item.key + Or31909Const.DEFAULT.MEMOFONTSTR] = item.memoInputvalue.fontSize
  })

  const resData: AssessmentInterRAIEUpdateOutEntity = await ScreenRepository.insert(
    'assessmentInterRAIEInsert',
    inputData
  )
  /****************************************
   * 保存成功の場合
   ****************************************/
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    emit('saveEnd', resData.data.errKbn)
  }
}

/**
 * AC005_「複写ボタン」押下
 */
const onCopy = () => {
  void getInitDataInfo(commonInfo.raiId)
}

/**
 * AC011_「削除」押下
 */
const onDel = () => {
  //  画面「A」タブに対し、更新区分を「D:削除」にする。
  localOneway.isShow = false
}

/**
 *  メモ入力選択ボタンクリック
 *
 * @param itemKey -現在フォーカスしている部分のkeyです
 *
 * @param index -
 *
 * @param inputVal -
 */
const memoInputIconBtnClick = (itemKey: string, index: number, inputVal: string) => {
  currentKey = index
  // GUI00787 ［メモ入力］画面をポップアップで起動
  localOneway.or51775Oneway = {
    // タイトル
    title: t('label.memo'),
    // 画面ID
    screenId: SCREENID,
    // 分類ID
    bunruiId: '',
    // 大分類ＣＤ
    t1Cd: T1CD,
    // 中分類CD
    t2Cd: T2CD,
    // 小分類ＣＤ
    t3Cd: T3CD,
    // テーブル名
    tableName: TABLENAME,
    // カラム名
    columnName: itemKey + COLUMNNAME,
    // アセスメント方式
    assessmentMethod: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? '',
    // 文章内容
    inputContents: inputVal,
    // 利用者ID
    userId: systemCommonsStore.getUserId,
    // モード
    mode: '1',
  } as Or51775OnewayType
  // GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    // ユニークコンポーネントID
    uniqueCpId: or51775.value.uniqueCpId,
    // ダイアログを開くフラグ
    state: { isOpen: true },
  })
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    // 本文末に追加の場合
    if (Or31909Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE === data.type) {
      refValue.value![currentKey].memoInputvalue.content += data.value
    }
    // 本文上書の場合
    else if (Or31909Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE === data.type) {
      refValue.value![currentKey].memoInputvalue.content = data.value
    }
  }
}
</script>

<template>
  <c-v-sheet class="or31909Container">
    <v-overlay
      :model-value="isLoading"
      :persistent="true"
      class="align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      >
      </v-progress-circular>
    </v-overlay>
    <c-v-row
      no-gutters
      class="pt-6"
    >
      <c-v-col
        cols="auto"
        class="content"
      >
        <!-- アセスメント種別タイトル -->
        <c-v-row class="mb-2">
          <c-v-spacer />
          <div class="heading-text-l">
            {{ localOneway.surveyAssessmentTypeOneway }}
          </div>
        </c-v-row>
        <!-- マトリクス領域です -->
        <div class="content-area">
          <!--  気分と行動タイトル -->
          <base-mo01338 :oneway-model-value="localOneway.emotionsAndActionsTitle" />
          <c-v-row
            v-for="(item, key) in renderDataList"
            v-show="localOneway.isShow"
            :key="key"
            no-gutters
            class="mt-1"
          >
            <!-- タイトル部分 -->
            <c-v-col cols="12 title-style">
              <base-mo01338
                class="background-transparent"
                :oneway-model-value="{
                  ...localOneway.subTitleMo01338Oneway,
                  value: item.leftContentLabel,
                }"
              />
            </c-v-col>
            <!-- 内容部分 -->
            <c-v-col
              col="12"
              class="pl-12 pr-6"
            >
              <!--ボタンgroupです  -->
              <c-v-row
                class="mb-4"
                no-gutters
              >
                <c-v-col
                  cols="12"
                  class="pl-0 pt-6 pb-4"
                >
                  {{ item.rightContentSubTitle }}
                </c-v-col>
                <c-v-col
                  v-if="item.componentType !== 'radio'"
                  class="bordered py-2 px-4 d-flex"
                >
                  <div class="w-100">
                    <c-v-row
                      v-for="(subItem, subKey) in item.selectOptionsList"
                      :key="subKey"
                      no-gutters
                      class="w-100"
                    >
                      <div class="fontStyle">{{ subItem.btnLabel }}</div>
                    </c-v-row>
                  </div>
                </c-v-col>
                <!--componentType=radioの場合  -->
                <c-v-col
                  v-else
                  class="mt-2"
                  style="width: 653px !important"
                >
                  <base-mo00039
                    v-model="refValue![key].subFormList[0].value"
                    :oneway-model-value="{
                      name: '',
                      items: [],
                      showItemLabel: false,
                      checkOff: true,
                    }"
                    style="width: 653px !important"
                    class="d-flex"
                  >
                    <template #prepend>
                      <base-at-radio
                        v-for="(it, index) in localOneway.mo00039Oneway.items"
                        :key="index"
                        :value="it.value"
                        :name="it.label"
                        :radio-label="it.label"
                        radio-class="mr-0"
                        class="radioItem"
                      />
                    </template>
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
              <!--フォーム部分です -->
              <template v-if="item.componentType !== 'radio'">
                <c-v-row
                  v-for="(subListItem, subListItemKey) in item.subFormList"
                  :key="subListItemKey"
                  no-gutters
                  class="mb-4 justify-space-between"
                >
                  <c-v-col
                    cols="auto"
                    class="text-style"
                  >
                    {{ subListItem.label }}
                  </c-v-col>
                  <c-v-col cols="auto">
                    <div class="select-style">
                      <base-at-select
                        v-model="refValue![key].subFormList[subListItemKey].value"
                        name=""
                        :items="item.selectOptionsList"
                        item-title="btnLabel"
                        item-value="btnValue"
                        hide-details="true"
                      >
                      </base-at-select>
                    </div>
                  </c-v-col>
                </c-v-row>
              </template>
              <c-v-row
                no-gutters
                :class="[key === refValue!.length - 1 ? 'mb-12' : 'mb-4']"
              >
                <c-v-col class="pa-0">
                  <g-custom-or-30981
                    v-bind="or30981ObjMap[key]"
                    v-model="refValue![key].memoInputvalue"
                    :oneway-model-value="localOneway.mo00046MemoInputOnewayType"
                    @on-click-edit-btn="
                      memoInputIconBtnClick(item.key, key, refValue![key].memoInputvalue.content)
                    "
                  ></g-custom-or-30981>
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
        </div>
        <!-- interRAIロゴ -->
        <c-v-row
          no-gutters
          class="mt-4"
        >
          <c-v-col
            cols="12"
            class="pa-0"
          >
            <c-v-img
              width="129"
              aspect-ratio="16/9"
              cover
              :src="InterRAI"
              style="float: right"
            ></c-v-img>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>

  <!-- GUI00937_入力支援［ケアマネ］ -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="or51775Type"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="or51775Confirm"
  />
</template>

<style scoped lang="scss">
.or31909Container {
  background-color: transparent;
}
.content {
  width: 1080px;
  .heading-text-l {
    font-weight: 400 !important;
  }
  .content-area {
    width: 100%;
    background-color: #fff;
  }
}
.fontStyle {
  font-size: 14px;
  font-weight: bold;
}
.button-style {
  display: flex;
  justify-content: flex-start;
  padding-left: 8px;
}
.title-style {
  border: 1px rgb(var(--v-theme-black-200)) solid;
  background: #e6e6e6;
  padding: 13.5px 24px !important;
  height: 48px !important;
}
.text-style {
  line-height: 36px;
}
.select-style {
  width: 381px !important;
}
.divider {
  height: 16px;
}
.bordered {
  border: 1px #3333 solid;
}

.multiline {
  white-space: pre-line;
}
.v-row {
  margin: 0px;
}
.v-col {
  padding: 8px;
}
.sticky {
  position: sticky;
  top: 0;
  z-index: 999;
}
.background-transparent {
  background-color: transparent;
}
:deep(.radio-group) {
  width: 653px !important;
  display: flex;
  .v-selection-control-group {
    justify-content: space-between;
  }
}

.radioItem {
  border: 1px solid #cdcdcd;
  border-radius: 5px;
  height: 36px;
  line-height: 36px;
  width: 249px !important;
  margin-top: 16px;
}
:deep(.v-selection-control--dirty) {
  background: #dbeefe !important;
}
:deep(.v-label--clickable) {
  width: 100% !important;
}
</style>
