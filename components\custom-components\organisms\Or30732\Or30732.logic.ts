// import { Or30798Logic } from '../Or30798/Or30798.logic'
import type { TeX0002EventType } from '../../template/TeX0002/TeX0002.type'
import { Or55476Const } from '../Or55476/Or55476.constants'
import { Or55476Logic } from '../Or55476/Or55476.logic'
import { OrX0209Logic } from '../../organisms/OrX0209/OrX0209.logic'
import { OrX0209Const } from '../../organisms/OrX0209/OrX0209.constants'
import { Or26257Const } from '../Or26257/Or26257.constants'
import { Or26257Logic } from '../Or26257/Or26257.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Gui00059Const } from '../Gui00059/Gui00059.constants'
import { Gui00059Logic } from '../Gui00059/Gui00059.logic'
import { Or27349Const } from '../Or27349/Or27349.constants'
import { Or27349Logic } from '../Or27349/Or27349.logic'
import { Or27210Const } from '../Or27210/Or27210.constants'
import { Or27210Logic } from '../Or27210/Or27210.logic'
import { Or27339Const } from '../Or27339/Or27339.constants'
import { Or27339Logic } from '../Or27339/Or27339.logic'
import { Or30732Const } from './Or30732.constants'
import type { Or30732CopyDataType } from './Or30732.type'
import {
  useEventStatusAccessor,
  useInitialize,
  useTwoWayBindAccessor,
} from '~/composables/useComponentLogic'
import type { Or30732Type } from '~/types/cmn/business/components/Or30732Type'

/**
 * Or30732Const:有機体:［アセスメント］画面（居宅）（1）
 * 処理ロジック
 *
 * @description
 * initialize処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or30732Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize<Or30732Type>({
      cpId: Or30732Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {
        section0: {
          memo1Knj: { value: '' },
          soudanUketukeYmd: { value: '' },
          soudanKeitai: '',
          keitaiSonotaKnj: { value: '' },
          soudanTantoId: '',
          shokuinName: { value: '' },
        },
        section1: {
          kNameKnj: { value: '' },
          kSex: '',
          kNenrei: { value: '' },
          kZcode: { modelValue: '' },
          kAddressKnj: { value: '' },
          kTel: { value: '' },
          kKeitaitel: { value: '' },
        },
        section2: {
          adName2Knj: { value: '' },
          sdSex2: '',
          sdNenrei2: { value: '' },
          sdZcode: { modelValue: '' },
          sodanshaAddKnj: { value: '' },
          sdTel2: { value: '' },
          sdKeitaitel2: { value: '' },
        },
        section3: {
          keiro2Knj: { value: '' },
        },
        section4: {
          requestDateYmd: { value: '' },
        },
        section5: {
          soudanNaiyouKnj: { value: '' },
          soudanNaiyou2Knj: { value: '' },
        },
        section6: {
          seikatuKeikaKnj: { value: '' },
        },
        section7: {
          hutanWariai: '',
          hutanKin: '',
          kKaigo: '',
        },
        section8: {
          yokaiKbn1: { modelValue: '' },
          yokaiKbn2: { modelValue: '' },
          ninteiYmd: { value: '' },
        },
        section9: {
          techoUmu1: '',
          sinshouShu: { value: '' },
          tTokyu1: { modelValue: '' },
          biko1Knj: { value: '' },
          get1Ymd: { value: '' },
        },
        section10: {
          techoUmu2: '',
          tTokyu2: { modelValue: '' },
          biko2Knj: { value: '' },
          get2Ymd: { value: '' },
        },
        section11: {
          techoUmu3: '',
          tTokyu3: { modelValue: '' },
          biko3Knj: { value: '' },
          get3Ymd: { value: '' },
        },
        section12: {
          sienJukyuUmu: '',
          shogaiJukyuUmu: '',
          sienJukyuKubunKnj: { value: '' },
        },
        section13: {
          adl1: '',
          adl2: '',
          adl1TantoKnj: { value: '' },
          adl2TantoKnj: { value: '' },
          adl1HospKnj: { value: '' },
          adl2HospKnj: { value: '' },
          adl1DateYmd: { value: '' },
          adl2DateYmd: { value: '' },
        },
        section14: {
          memo2Knj: { value: '' },
          jisshiShokaiYmd: { value: '' },
        },
      },
      childCps: [
        { cpId: Or26257Const.CP_ID(0) },
        { cpId: Or55476Const.CP_ID(0) },
        { cpId: Or27349Const.CP_ID(0) },
        { cpId: Or27210Const.CP_ID(0) },
        { cpId: Or27339Const.CP_ID(0) },
        { cpId: OrX0209Const.CP_ID(0) },
        { cpId: Or51775Const.CP_ID(0) },
        { cpId: Or51775Const.CP_ID(0) },
        { cpId: Gui00059Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or26257Logic.initialize(childCpIds[Or26257Const.CP_ID(0)].uniqueCpId)
    Or55476Logic.initialize(childCpIds[Or55476Const.CP_ID(0)].uniqueCpId)
    Or27349Logic.initialize(childCpIds[Or27349Const.CP_ID(0)].uniqueCpId)
    Or27210Logic.initialize(childCpIds[Or27210Const.CP_ID(0)].uniqueCpId)
    Or27339Logic.initialize(childCpIds[Or27339Const.CP_ID(0)].uniqueCpId)
    OrX0209Logic.initialize(childCpIds[OrX0209Const.CP_ID(0)].uniqueCpId)
    Or51775Logic.initialize(childCpIds[Or51775Const.CP_ID(0)].uniqueCpId)
    Gui00059Logic.initialize(childCpIds[Gui00059Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or30732CopyDataType>(Or30732Const.CP_ID(0))
  /**
   * 自身のEventStatus領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useEventStatusAccessor<TeX0002EventType>(Or30732Const.CP_ID(1))
}
