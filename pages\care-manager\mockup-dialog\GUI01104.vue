<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue'
import { definePageMeta, useInitialize, useScreenStore, useSetupChildProps } from '#imports'
import { Or27035Const } from '~/components/custom-components/organisms/Or27035/Or27035.constants'
import { Or27035Logic } from '~/components/custom-components/organisms/Or27035/Or27035.logic'
import type { Or27035OnewayType, Or27035Type } from '~/types/cmn/business/components/Or27035Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { CustomClass } from '~/types/CustomClassType'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 * SH 2025/04/01 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI01104'
// ルーティング
const routing = 'GUI01104/pinia'
// 画面物理名
const screenName = 'GUI01104'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or27035 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01104' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
or27035.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
const init = useInitialize({
  cpId: 'GUI01104',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or27035Const.CP_ID(0) }],
})
Or27035Logic.initialize(init.childCpIds.Or27035.uniqueCpId)

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27035Const.CP_ID(0)]: or27035.value,
})

// ダイアログ表示フラグ
const showDialogOr27035 = computed(() => {
  // Or27035 cks_flg=1 のダイアログ開閉状態
  return Or27035Logic.state.get(or27035.value.uniqueCpId)?.isOpen ?? false
})

const or27035OnewayModel: Or27035OnewayType = {
  /**
   * 事業所ID
   */
  svJigyoId: '1',
  /**
   * ユーザーID
   */
  userId: '0',
  /**
   * 起動パターン
   */
  pattern: '',
  /**
   * 職員id
   */
  shokuinId: '2',
  /**
   * システムコード
   */
  gsyscd: '71101',
}
const or27035Type = ref<Or27035Type>({
  program1Flg: { modelValue: true },
  program2Flg: { modelValue: false },
  program3Flg: { modelValue: true },
  program4Flg: { modelValue: false },
  program5Flg: { modelValue: true },
  program6Flg: { modelValue: false },
  program1Cnt: {
    mo00045: {
      value: '1',
    },
  },
  program2Cnt: {
    mo00045: {
      value: '2',
    },
  },
  program3Cnt: {
    mo00045: {
      value: '3',
    },
  },
  program4Cnt: {
    mo00045: {
      value: '4',
    },
  },
  program5Cnt: {
    mo00045: {
      value: '5',
    },
  },
  program6Cnt: {
    mo00045: {
      value: '6',
    },
  },
})

const mo00045OneWay = {
  customClass: {
    outerClass: '',
    outerStyle: '',
    labelClass: '',
    labelStyle: 'display: none',
    itemClass: '',
    itemStyle: '',
  } as CustomClass,
} as Mo00045OnewayType
/**
 * GUI01104
 *
 * @param data - GUI01104
 */
const Or27035Change = (data: Or27035Type) => {

  local.selectData = data
}
const local = reactive({
  // 運動不足
  program1Flg: { value: 'true' } as Mo00045Type,
  // 栄養改善
  program2Flg: { value: 'false' } as Mo00045Type,
  //口腔内ケア
  program3Flg: { value: 'true' } as Mo00045Type,
  //閉じこもり予防
  program4Flg: { value: 'false' } as Mo00045Type,
  //物忘れ予防
  program5Flg: { value: 'true' } as Mo00045Type,
  //うつ予防
  program6Flg: { value: 'false' } as Mo00045Type,
  //運動不足（チェック数）
  program1Cnt: { value: '1' } as Mo00045Type,
  //栄養改善（チェック数）
  program2Cnt: { value: '2' } as Mo00045Type,
  //口腔内ケア（チェック数）
  program3Cnt: { value: '3' } as Mo00045Type,
  // 閉じこもり予防（チェック数）
  program4Cnt: { value: '4' } as Mo00045Type,
  // 物忘れ予防（チェック数）
  program5Cnt: { value: '5' } as Mo00045Type,
  // うつ予防（チェック数）
  program6Cnt: { value: '6' } as Mo00045Type,
  // 起動パターン
  pattern: { value: '1' } as Mo00045Type,
  // 職員ＩＤ
  shokuinId: { value: '2' } as Mo00045Type,
  // システムコード
  gsyscd: { value: '71101' } as Mo00045Type,
  // 事業所ID
  svJigyoId: { value: '1' } as Mo00045Type,
  // 戻り値
  selectData:{} as Or27035Type,
})

watch(
  or27035Type,
  () => {
    console.log(or27035Type)
      local.program1Flg.value =  or27035Type.value.program1Flg.modelValue === true ? 'true' : 'false'
      local.program2Flg.value =  or27035Type.value.program2Flg.modelValue === true ? 'true' : 'false'
      local.program3Flg.value =  or27035Type.value.program3Flg.modelValue === true ? 'true' : 'false'
      local.program4Flg.value =  or27035Type.value.program4Flg.modelValue === true ? 'true' : 'false'
      local.program5Flg.value =  or27035Type.value.program5Flg.modelValue === true ? 'true' : 'false'
      local.program6Flg.value =  or27035Type.value.program6Flg.modelValue === true ? 'true' : 'false'
      local.program1Cnt.value =  or27035Type.value.program1Cnt.mo00045.value
      local.program2Cnt.value =  or27035Type.value.program2Cnt.mo00045.value
      local.program3Cnt.value =  or27035Type.value.program3Cnt.mo00045.value
      local.program4Cnt.value =  or27035Type.value.program4Cnt.mo00045.value
      local.program5Cnt.value =  or27035Type.value.program5Cnt.mo00045.value
      local.program6Cnt.value =  or27035Type.value.program6Cnt.mo00045.value
  },
  { deep: true }
);

/**
 *  ボタン押下時の処理(Or27035)
 *
 * @param id  - ID
 */
function onClickOr27035(id: string) {
  or27035OnewayModel.pattern = ''
  or27035OnewayModel.histDataNoneFlg = false
  if (id === '1') {
    or27035OnewayModel.pattern = '1'
  }
  if (id === '2') {
    or27035OnewayModel.histDataNoneFlg = true
  }
  // Or27035のダイアログ開閉状態を更新する
  Or27035Logic.state.set({
    uniqueCpId: or27035.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/** GUI01104 疎通起動  */
function onClick() {
  or27035Type.value.program1Flg.modelValue = local.program1Flg.value === 'true' ? true : false
  or27035Type.value.program2Flg.modelValue = local.program2Flg.value === 'true' ? true : false
  or27035Type.value.program3Flg.modelValue = local.program3Flg.value === 'true' ? true : false
  or27035Type.value.program4Flg.modelValue = local.program4Flg.value === 'true' ? true : false
  or27035Type.value.program5Flg.modelValue = local.program5Flg.value === 'true' ? true : false
  or27035Type.value.program6Flg.modelValue = local.program6Flg.value === 'true' ? true : false
  or27035Type.value.program1Cnt.mo00045.value = local.program1Cnt.value
  or27035Type.value.program2Cnt.mo00045.value = local.program2Cnt.value
  or27035Type.value.program3Cnt.mo00045.value = local.program3Cnt.value
  or27035Type.value.program4Cnt.mo00045.value = local.program4Cnt.value
  or27035Type.value.program5Cnt.mo00045.value = local.program5Cnt.value
  or27035Type.value.program6Cnt.mo00045.value = local.program6Cnt.value
  or27035OnewayModel.pattern = local.pattern.value
  or27035OnewayModel.shokuinId = local.shokuinId.value
  or27035OnewayModel.gsyscd= local.gsyscd.value
  or27035OnewayModel.svJigyoId= local.svJigyoId.value
  Or27035Logic.state.set({
    uniqueCpId: or27035.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- POP画面ポップアップ SH 2025/04/27 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27035('0')"
        >GUI001104_必要な事業プログラム
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27035('1')"
        >GUI001104_必要な事業プログラム 複写
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27035('2')"
        >GUI001104_必要な事業プログラム 履歴データがない場合
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-27035
    v-if="showDialogOr27035"
    v-bind="or27035"
    v-model="or27035Type"
    :oneway-model-value="or27035OnewayModel"
    @update:model-value="Or27035Change"
  />
  <!-- POP画面ポップアップ SH 2025/04/27 ADD END-->
    <c-v-row no-gutters>
      <c-v-col>
         <v-btn variant="plain"
          >-----------------------------------------------------------------------------------------------------------------↑Mock画面、↓疎通画面--------------------------------------------------------------------------------------------------
        </v-btn>
      </c-v-col>
    </c-v-row>
    <div style="margin-left: 20px">運動不足</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program1Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">栄養改善</div>
     <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program2Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">口腔内ケア</div>
     <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program3Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">閉じこもり予防</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program4Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">物忘れ予防</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program5Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">うつ予防</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program6Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">運動不足（チェック数）</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program1Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">栄養改善（チェック数）</div>
     <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program2Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">口腔内ケア（チェック数）</div>
     <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program3Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">閉じこもり予防（チェック数）</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program4Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">物忘れ予防（チェック数）</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program5Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">うつ予防（チェック数）</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program6Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
        <div style="margin-left: 20px">起動パターン</div>
     <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.pattern"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">職員ＩＤ</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.shokuinId"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">システムコード</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.gsyscd"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">事業所ID</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.svJigyoId"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div class="pt-5 w-25 pl-5">
      <v-btn @click="onClick"> GUI01104 疎通起動 </v-btn>
    </div>
    <c-v-row no-gutters>
      <c-v-col>
         <v-btn variant="plain"
          >------------------------------------------------------------------------------------------------------------------↓ 戻り値--------------------------------------------------------------------------------------------------
        </v-btn>
      </c-v-col>
    </c-v-row>
    <div style="margin-left: 20px">運動不足</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program1Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">栄養改善</div>
     <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program2Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">口腔内ケア</div>
     <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program3Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">閉じこもり予防</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program4Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">物忘れ予防</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program5Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">うつ予防</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program6Flg"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">運動不足（チェック数）</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program1Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">栄養改善（チェック数）</div>
     <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program2Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">口腔内ケア（チェック数）</div>
     <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program3Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">閉じこもり予防（チェック数）</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program4Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">物忘れ予防（チェック数）</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program5Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div style="margin-left: 20px">うつ予防（チェック数）</div>
    <div style="margin-left: 20px; width: 200px">
      <base-mo00045
        v-model="local.program6Cnt"
        :oneway-model-value="mo00045OneWay"
      ></base-mo00045>
    </div>
    <div class="pt-5 w-25 pl-5">
    <div> 戻り値 </div>
    <div> {{local.selectData}} </div>
  </div>
</template>
