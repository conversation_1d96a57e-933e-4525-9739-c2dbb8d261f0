<script setup lang="ts">
/**
 * TeX0003:アセスメント(インターライ)画面テンプレート
 *
 * @description
 * アセスメント(インターライ)画面テンプレート
 *
 * <AUTHOR>
 */

import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { TeX0003Const } from '~/components/custom-components/template/Tex0003/TeX0003.constants'
import { Or31946Logic } from '~/components/custom-components/organisms/Or31946/Or31946.logic'
import { Or31946Const } from '~/components/custom-components/organisms/Or31946/Or31946.constants'
import { Or31995Logic } from '~/components/custom-components/organisms/Or31995/Or31995.logic'
import { Or31995Const } from '~/components/custom-components/organisms/Or31995/Or31995.constants'
import { Or30341Const } from '~/components/custom-components/organisms/Or30341/Or30341.constants'
import { Or30341Logic } from '~/components/custom-components/organisms/Or30341/Or30341.logic'
import { Or31909Logic } from '~/components/custom-components/organisms/Or31909/Or31909.logic'
import { Or31791Logic } from '~/components/custom-components/organisms/Or31791/Or31791.logic'
import { Or31791Const } from '~/components/custom-components/organisms/Or31791/Or31791.constants'
import { Or31432Logic } from '~/components/custom-components/organisms/Or31432/Or31432.logic'
import { Or31432Const } from '~/components/custom-components/organisms/Or31432/Or31432.constants'
import { Or32625Const } from '~/components/custom-components/organisms/Or32625/Or32625.constants'
import { Or32625Logic } from '~/components/custom-components/organisms/Or32625/Or32625.logic'
import { Or31909Const } from '~/components/custom-components/organisms/Or31909/Or31909.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useSetupChildProps, useScreenStore, dateUtils, useScreenTwoWayBind } from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { TransmitParam } from '~/types/cmn/business/components/TeX0003Type'
import { Or31000Logic } from '~/components/custom-components/organisms/Or31000/Or31000.logic'
import { Or31000Const } from '~/components/custom-components/organisms/Or31000/Or31000.constants'
import { Or32240Logic } from '~/components/custom-components/organisms/Or32240/Or32240.logic'
import { Or32240Const } from '~/components/custom-components/organisms/Or32240/Or32240.constants'
import { Or11162Logic } from '~/components/custom-components/organisms/Or11162/Or11162.logic'
import { Or11162Const } from '~/components/custom-components/organisms/Or11162/Or11162.constants'
import { Or31972Logic } from '~/components/custom-components/organisms/Or31972/Or31972.logic'
import { Or31972Const } from '~/components/custom-components/organisms/Or31972/Or31972.constants'
import { Or30621Logic } from '~/components/custom-components/organisms/Or30621/Or30621.logic'
import { Or30621Const } from '~/components/custom-components/organisms/Or30621/Or30621.constants'
import { Or11158Logic } from '~/components/custom-components/organisms/Or11158/Or11158.logic'
import { Or11158Const } from '~/components/custom-components/organisms/Or11158/Or11158.constants'
import { Or31124Logic } from '~/components/custom-components/organisms/Or31124/Or31124.logic'
import { Or31124Const } from '~/components/custom-components/organisms/Or31124/Or31124.constants'
import { Or31369Logic } from '~/components/custom-components/organisms/Or31369/Or31369.logic'
import { Or31369Const } from '~/components/custom-components/organisms/Or31369/Or31369.constants'
import type { Or53417OnewayType } from '~/types/cmn/business/components/Or53417Type'
import { Or53417Logic } from '~/components/custom-components/organisms/Or53417/Or53417.logic'
import { Or53417Const } from '~/components/custom-components/organisms/Or53417/Or53417.constants'
import type { Or10279OneWayType } from '~/types/cmn/business/components/Or10279Type'
import { Or10279Logic } from '~/components/custom-components/organisms/Or10279/Or10279.logic'
import { Or10279Const } from '~/components/custom-components/organisms/Or10279/Or10279.constants'
import type { OrX0001Type, OrX0001OnewayType } from '~/types/cmn/business/components/OrX0001Type'
import { OrX0001Const } from '~/components/custom-components/organisms/OrX0001/OrX0001.constants'
import { OrX0001Logic } from '~/components/custom-components/organisms/OrX0001/OrX0001.logic'
import type {
  HistorySelectInfoType,
  Or10929Type,
} from '~/types/cmn/business/components/Or10929Type'
import { Or10929Logic } from '~/components/custom-components/organisms/Or10929/Or10929.logic'
import { Or10929Const } from '~/components/custom-components/organisms/Or10929/Or10929.constants'
import type { HistorySelectTableDataItem } from '~/components/custom-components/organisms/Or10929/Or10929.type'
import { Or32004Logic } from '~/components/custom-components/organisms/Or32004/Or32004.logic'
import { Or32004Const } from '~/components/custom-components/organisms/Or32004/Or32004.constants'
import { Or31806Const } from '~/components/custom-components/organisms/Or31806/Or31806.constants'
import { Or31806Logic } from '~/components/custom-components/organisms/Or31806/Or31806.logic'
import { Or31168Const } from '~/components/custom-components/organisms/Or31168/Or31168.constant'
import { Or31168Logic } from '~/components/custom-components/organisms/Or31168/Or31168.logic'
import { Or31524Const } from '~/components/custom-components/organisms/Or31524/Or31524.constants'
import { Or31524Logic } from '~/components/custom-components/organisms/Or31524/Or31524.logic'
import { Or32289Logic } from '~/components/custom-components/organisms/Or32289/Or32289.logic'
import { Or32289Const } from '~/components/custom-components/organisms/Or32289/Or32289.constants'
import { Or31654Logic } from '~/components/custom-components/organisms/Or31654/Or31654.logic'
import { Or31654Const } from '~/components/custom-components/organisms/Or31654/Or31654.constants'
import { Or28348Logic } from '~/components/custom-components/organisms/Or28348/Or28348.logic'
import { Or28348Const } from '~/components/custom-components/organisms/Or28348/Or28348.constants'
import type {
  SubInfoBEntity,
  IAssessmentInterRAINewSelectInEntity,
  IAssessmentInterRAINewSelectOutEntity,
  PlanPeriodInfoEntity,
  HistoryInfoEntity,
  IAssessmentInterRAIPeriodChangeSelectInEntity,
  IAssessmentInterRAIPeriodChangeSelectOutEntity,
  IAssessmentInterRAIHistoryChangeSelectInEntity,
  IAssessmentInterRAIHistoryChangeSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import { Or33763Const } from '~/components/custom-components/organisms/Or33763/Or33763.constants'
import { Or33763Logic } from '~/components/custom-components/organisms/Or33763/Or33763.logic'
import { OrX0115Logic } from '~/components/custom-components/organisms/OrX0115/OrX0115.logic'
import { OrX0115Const } from '~/components/custom-components/organisms/OrX0115/OrX0115.constants'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or10269Const } from '~/components/custom-components/organisms/Or10269/Or10269.constants'
import { Or10269Logic } from '~/components/custom-components/organisms/Or10269/Or10269.logic'
import type { Or10269Param } from '~/components/custom-components/organisms/Or10269/Or10269.type'
import { OrX0134Const } from '~/components/custom-components/organisms/OrX0134/OrX0134.constants'
import { OrX0134Logic } from '~/components/custom-components/organisms/OrX0134/OrX0134.logic'
import { Or13872Const } from '~/components/custom-components/organisms/Or13872/Or13872.constants'
import { Or13872Logic } from '~/components/custom-components/organisms/Or13872/Or13872.logic'
import type { OrX0134OnewayType, OrX0134Type } from '~/types/cmn/business/components/OrX0134Type'
import type { Or13844OnewayType } from '~/types/cmn/business/components/Or13844Type'
import { Or13844Const } from '~/components/custom-components/organisms/Or13844/Or13844.constants'
import { Or13844Logic } from '~/components/custom-components/organisms/Or13844/Or13844.logic'
import type { Or13872OnewayType } from '~/types/cmn/business/components/Or13872Type'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import type {
  SvJigyo,
  Or26257Type,
  Or26257OnewayType,
} from '~/types/cmn/business/components/Or26257Type'
import type { OrX0157OnewayType, OrX0157Type } from '~/types/cmn/business/components/OrX0157Type'
import { hasRegistAuth, hasPrintAuth, hasOutputAuth } from '~/utils/useCmnAuthz'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { useJigyoList } from '~/utils/useJigyoList'
import {
  DIALOG_BTN,
  MID_LINE,
  SUCCESS_STATUS_CODE,
  SPACE_WAVE,
  UPDATE_KBN,
} from '~/constants/classification-constants'
import type {
  AssessmentInterRAIAHistorySelectEntity,
  AssessmentInterRAIAHistorySelectOutEntity,
} from '~/repositories/cmn/entities/assessmentInterRAIAHistoryEntity'
import type { TeX0003TwoWayType } from '~/components/custom-components/template/Tex0003/TeX0003.type'
const { jigyoListWatch } = useJigyoList()
const { convertDateToSeireki } = dateUtils()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()

/**
 * 初期読み込みのローディング
 */
const isLoading = ref(false)

const or11871 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or00248 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or53417 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or10279 = ref({ uniqueCpId: Or10279Const.CP_ID(1) })
const orX0001 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31909 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or30341 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31000 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or11162 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or32240 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31972 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or30621 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or11158 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31124 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31369 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or32625 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31791 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31432 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or10929 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or32004 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31946 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31806 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31168 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31524 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or32289 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31995 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or31654 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or28348 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or21813 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or21814 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or33763 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const orx0115 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or41179 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or13844 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or10269 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const orX0134 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or13872 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })
const or26257 = ref({ uniqueCpId: TeX0003Const.STR.EMPTY })

/**
 * 共通入力の変更
 */
const isCommonDataEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})

/**
 * タブ入力の変更
 */
const isTabDataEdit = computed(() => {
  let editFlg = false
  switch (local.mo00043.id) {
    case TeX0003Const.TAB.A:
      editFlg = useScreenStore().isEditByUniqueCpId(or31000.value.uniqueCpId)
      break
    case TeX0003Const.TAB.B:
      editFlg = useScreenStore().isEditByUniqueCpId(or31369.value.uniqueCpId)
      break
    case TeX0003Const.TAB.C:
      editFlg = useScreenStore().isEditByUniqueCpId(or31124.value.uniqueCpId)
      break
    case TeX0003Const.TAB.D:
      editFlg = useScreenStore().isEditByUniqueCpId(or31654.value.uniqueCpId)
      break
    case TeX0003Const.TAB.E:
      editFlg = useScreenStore().isEditByUniqueCpId(or31909.value.uniqueCpId)
      break
    case TeX0003Const.TAB.F:
      editFlg = useScreenStore().isEditByUniqueCpId(or31791.value.uniqueCpId)
      break
    case TeX0003Const.TAB.G:
      editFlg = useScreenStore().isEditByUniqueCpId(or31432.value.uniqueCpId)
      break
    case TeX0003Const.TAB.H:
      editFlg = useScreenStore().isEditByUniqueCpId(or28348.value.uniqueCpId)
      break
    case TeX0003Const.TAB.I1:
      editFlg = useScreenStore().isEditByUniqueCpId(or30341.value.uniqueCpId)
      break
    case TeX0003Const.TAB.I2:
      editFlg = useScreenStore().isEditByUniqueCpId(or31995.value.uniqueCpId)
      break
    case TeX0003Const.TAB.J:
      editFlg = useScreenStore().isEditByUniqueCpId(or11158.value.uniqueCpId)
      break
    case TeX0003Const.TAB.K:
      editFlg = useScreenStore().isEditByUniqueCpId(or32240.value.uniqueCpId)
      break
    case TeX0003Const.TAB.L:
      editFlg = useScreenStore().isEditByUniqueCpId(or31806.value.uniqueCpId)
      break
    case TeX0003Const.TAB.M:
      editFlg = useScreenStore().isEditByUniqueCpId(or31524.value.uniqueCpId)
      break
    case TeX0003Const.TAB.N:
      editFlg = useScreenStore().isEditByUniqueCpId(or32004.value.uniqueCpId)
      break
    case TeX0003Const.TAB.O:
      editFlg = useScreenStore().isEditByUniqueCpId(or11162.value.uniqueCpId)
      break
    case TeX0003Const.TAB.P:
      editFlg = useScreenStore().isEditByUniqueCpId(or31946.value.uniqueCpId)
      break
    case TeX0003Const.TAB.Q:
      editFlg = useScreenStore().isEditByUniqueCpId(or32625.value.uniqueCpId)
      break
    case TeX0003Const.TAB.R:
      editFlg = useScreenStore().isEditByUniqueCpId(or30621.value.uniqueCpId)
      break
    case TeX0003Const.TAB.S:
      editFlg = useScreenStore().isEditByUniqueCpId(or31972.value.uniqueCpId)
      break
    case TeX0003Const.TAB.T:
      editFlg = useScreenStore().isEditByUniqueCpId(or33763.value.uniqueCpId)
      break
    case TeX0003Const.TAB.U:
      editFlg = useScreenStore().isEditByUniqueCpId(or31168.value.uniqueCpId)
      break
    case TeX0003Const.TAB.V:
      editFlg = useScreenStore().isEditByUniqueCpId(or32289.value.uniqueCpId)
      break
  }
  return editFlg
})

/**
 * すべての入力の変更
 */
const isEdit = computed(() => {
  return isTabDataEdit.value || isCommonDataEdit.value || local.updateKbn === UPDATE_KBN.DELETE || local.historyUpdateKbn === UPDATE_KBN.DELETE
})

/**
 * 計画期間
 */
const planPeriodShow = ref<boolean>(true)
/**
 * 履歴
 */
const historyShow = ref<boolean>(true)
/**
 * 作成者
 */
const authorShow = ref<boolean>(true)
/**
 * 基準日
 */
const baseDateShow = ref<boolean>(true)

/**
 * 削除フラグ
 */
const deleteFlag = ref<boolean>(false)

/**
 * CSV出力ボタン活性/非活性フラゲ
 */
const csvOutputBtnDisabledFlg = ref<boolean>(false)

/**
 * CSV出力ボタン表示/非表示フラゲ
 */
const logBtnShowFlg = ref<boolean>(true)

/**
 * 一時変数:アセスメントID
 */
let history: HistoryInfoEntity = {} as HistoryInfoEntity

/**
 * ローカルTwoway
 */
const local = reactive({
  /**
   * 作成日
   */
  kijunbiYmd: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  /**
   * 作成者
   */
  orX0157: {
    value: '',
  } as OrX0157Type,
  /**
   * 期間タブ
   */
  mo00043: {
    id: TeX0003Const.STR.EMPTY,
  } as Mo00043Type,
  /**
   * 機関フラグ
   */
  kikanKanriFlg: '0',
  /**
   * 種別ID
   */
  syubetsuId: '',
  /**
   * 二回目新規ボタン押下State
   */
  addBtnState: false,
  /**
   * 計画対象期間情報
   */
  planPeriod: {} as PlanPeriodInfoEntity,
  /**
   * 履歴情報
   */
  history: {} as HistoryInfoEntity,
  /**
   * 利用者ID
   */
  userId: TeX0003Const.STR.EMPTY,
  /**
   * アセスメントID
   */
  raiId: TeX0003Const.STR.ZERO,
  /**
   * 作成者ID
   */
  sakuseiId: TeX0003Const.STR.EMPTY,
  /**
   * 事業所Id
   */
  svJigyoId: TeX0003Const.STR.EMPTY,
  /**
   * 事業所名
   */
  svJigyoKnj: TeX0003Const.STR.EMPTY,
  /**
   * 履歴番号
   */
  krirekiNo: TeX0003Const.STR.ZERO,
  /**
   * 選定表・検討表作成区分
   */
  tableCreateKbn: TeX0003Const.STR.EMPTY,
  /**
   * 更新区分
   */
  updateKbn: TeX0003Const.STR.EMPTY,
  /**
   * 履歴更新区分
   */
  historyUpdateKbn: TeX0003Const.STR.EMPTY,
  /**
   * 調査アセスメント種別
   */
  assType: TeX0003Const.STR.EMPTY,
  /**
   * 要介護度
   */
  yokaiKbn: TeX0003Const.STR.EMPTY,
  /**
   * サブ情報（B）
   */
  subInfoB: {} as SubInfoBEntity,
})

/**
 * ローカルOneway
 */
const localOneway = reactive({
  /**
   * 計画対象期間選択
   */
  planningPeriodSelectOneway: {
    plainningPeriodManageFlg: '',
    planningPeriodInfo: {},
    pageBtnAutoDisabled: false,
    showLabelMode: false,
    labelAutoDisabled: true,
  } as Or13844OnewayType,
  /**
   * 対象期間
   */
  orX0115Oneway: {
    kindId: TeX0003Const.STR.EMPTY,
    sc1Id: TeX0003Const.STR.EMPTY,
  } as OrX0115OnewayType,
  /**
   * 履歴選択
   */
  hisotrySelectOneway: {
    /** 履歴情報 */
    historyInfo: {
      /** アセスメントID */
      rirekiId: '',
      /** 履歴番号 */
      krirekiNo: '',
      /** 履歴総件数 */
      krirekiCnt: '',
      /** ケアチェックID */
      cc1Id: '',
    },
    pageBtnAutoDisabled: false,
    showLabelMode: false,
  } as Or13872OnewayType,
  /**
   * 作成者選択
   */
  authorSelectOneway: {
    inputMode: 'TextOnly',
    showEditBtnFlg: true,
    text: {
      orX0157InputOneway: {
        isVerticalLabel: true,
        showItemLabel: true,
        itemLabel: t('label.author'),
        customClass: new CustomClass({
          labelClass: 'pb-1',
          labelStyle: 'background-color:rgb(var(--v-theme-background));',
        }),
        width: '148px',
      },
    },
    inputReadonly: true,
  } as OrX0157OnewayType,
  /**
   * 基準日
   */
  createDateOneway: {
    itemLabel: t('label.base-date'),
    isRequired: true,
    isVerticalLabel: true,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass',
      labelClass: 'mb-1',
    }),
    width: '140',
  } as Mo00020OnewayType,
  /**
   * タブ
   */
  mo00043OnewayType: {
    tabItems: [],
    minWidth: '58px',
  } as Mo00043OnewayType,
  /**
   * or53417単方向バインド
   */
  or53417Oneway: {
    shisetuId: TeX0003Const.STR.EMPTY,
    svJigyoId: TeX0003Const.STR.EMPTY,
    type: TeX0003Const.STR.THREE,
  } as Or53417OnewayType,
  /**
   * ［アセスメント（インターライ）CSV出力］画面
   */
  Or10279Oneway: {} as Or10279OneWayType,
  /**
   * ［履歴選択］画面
   */
  or10929Oneway: {} as HistorySelectInfoType,
  /**
   * アセスメント（インターライ）複写
   */
  orX0134OnewayModel: {} as OrX0134OnewayType,
  /**
   * 職員検索画面
   */
  or26257OnewayModel: {
    /**
     * システム略称
     */
    sysCdKbn: systemCommonsStore.getSystemAbbreviation,
    /**
     * アカウント設定
     */
    secAccountAllFlg: '',
    /**
     * 適用事業所ＩＤリスト
     */
    svJigyoIdList: [],
    /**
     * 職員ID
     */
    shokuinId: systemCommonsStore.getStaffId,
    /**
     * システムコード
     */
    gsysCd: systemCommonsStore.getSystemCode,
    /**
     * モード
     */
    selectMode: '12',
    /**
     * 基準日
     */
    kijunYmd: systemCommonsStore.getSystemDate,
    /**
     * 事業所ID
     */
    defSvJigyoId: systemCommonsStore.getSvJigyoId,
    /**
     * フィルターフラグ
     */
    filterDwFlg: '1',
    /**
     * 雇用状態
     */
    koyouState: '',
    /**
     * 地域フラグ
     */
    areaFlg: '',
    /**
     * 表示名称リスト
     */
    hyoujiColumnList: [],
    /**
     * 未設定フラグ
     */
    misetteiFlg: '1',
    /**
     * 他職員参照権限
     */
    otherRead: '',
    /**
     * 中止フラグ
     */
    refStopFlg: '',
    /**
     * 処理フラグ
     */
    syoriFlg: '',
    /**
     * メニュー１ID
     */
    menu1Id: '',
    /**
     * 件数フラグ
     */
    kensuFlg: '1',
    /**
     * 職員IDリスト
     */
    shokuinIdList: [],
  } as Or26257OnewayType,
})

/**
 * メッセージ「i-cmn-11260」 - 削除確認画面
 */
const orX0001Type = ref<OrX0001Type>({
  deleteSyubetsu: TeX0003Const.STR.EMPTY,
})

/**
 * OrX0001単方向バインド -  削除確認画面
 * メッセージ「i-cmn-11260」
 */
const orX0001Oneway = ref<OrX0001OnewayType>({
  createYmd: TeX0003Const.STR.EMPTY,
  kinouKnj: TeX0003Const.STR.EMPTY,
  selectTabName: TeX0003Const.STR.EMPTY,
  startTabName: TeX0003Const.STR.EMPTY,
  endTabName: TeX0003Const.STR.EMPTY,
})

/**
 * GUI00792_［履歴選択］画面 双方向バインドModelValue
 */
const or10929Type = ref<Or10929Type>({
  historySelectDataList: [],
} as Or10929Type)

/**
 * GUI04512_アセスメント（インターライ）複写画面 - 双方向バインドModelValue
 */
const orX0134Type = ref<OrX0134Type>({
  sc1Id: TeX0003Const.STR.EMPTY,
  raiId: TeX0003Const.STR.EMPTY,
  copyFlg: TeX0003Const.STR.EMPTY,
} as OrX0134Type)

/**
 * GUI00220_職員検索画面 - 双方向バインドModelValue
 */
const or26257Type = ref<Or26257Type>({
  shokuin: {
    shokuin1Knj: '',
    shokuin2Knj: '',
  },
} as Or26257Type)

/**
 * 初期化のフラグ
 */
let initFlag = false

/**
 * ダイアログ表示フラグ
 */
const showDialogOr10269 = computed(() => {
  // Or10269のダイアログ開閉状態
  return Or10269Logic.state.get(or10269.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr53417 = computed(() => {
  // Or53417のダイアログ開閉状態
  return Or53417Logic.state.get(or53417.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr10279 = computed(() => {
  // Or10279のダイアログ開閉状態
  return Or10279Logic.state.get(or10279.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOrX0115 = computed(() => {
  // OrX0115 cks_flg=1 のダイアログ開閉状態
  return OrX0115Logic.state.get(orx0115.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOrX0001 = computed(() => {
  // OrX00005 cks_flg=1 のダイアログ開閉状態
  return OrX0001Logic.state.get(orX0001.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr10929 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or10929Logic.state.get(or10929.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOrX0134 = computed(() => {
  /**
   * Or00100のダイアログ開閉状態
   */
  return OrX0134Logic.state.get(orX0134.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr26257 = computed(() => {
  // Or26257のダイアログ開閉状態
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // コントロール設定
  await initControl()
  // 初期表示
  local.krirekiNo = TeX0003Const.STR.ZERO
})

/**
 * 変数を初期状態に戻す
 */
const initVariable = () => {
  local.addBtnState = false
  deleteFlag.value = false
  localOneway.createDateOneway.disabled = false
  localOneway.authorSelectOneway.text!.orX0157InputOneway.disabled = false
  local.updateKbn = TeX0003Const.STR.EMPTY
  local.historyUpdateKbn = TeX0003Const.STR.EMPTY
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
const callbackFuncJigyo = (newJigyoId: string) => {
  if (newJigyoId) {
    // 「事業所選択」変更がある
    if (local.svJigyoId !== newJigyoId) {
      // 画面入力データに変更がある場合
      if (isEdit.value) {
        // AC004-2と同じ
        openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-10430'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'destroy1',
          secondBtnLabel: t('btn.no'),
          thirdBtnType: 'normal3',
          thirdBtnLabel: t('btn.cancel'),
        })
          .then(async (dialogResult) => {
            switch (dialogResult) {
              case 'yes':
                // AC003(保存処理)を実行し
                await _update()
                break
              case 'no':
                // 処理続き
                initVariable()
                break
              case 'cancel':
                // 処理終了
                return
            }
          })
          .catch((error) => {
            console.error(error)
          })
      }
      const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
      const jigyoInfo = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)

      local.svJigyoId = newJigyoId
      local.svJigyoKnj = jigyoInfo!.jigyoRyakuKnj
      // 画面共通情報.計画対象期間ID（ゼロに設定）
      local.planPeriod.sc1Id = TeX0003Const.STR.ZERO
      // 画面共通情報.アセスメントID（ゼロに設定）
      local.history.raiId = TeX0003Const.STR.ZERO

      // AC001と同じ
      local.krirekiNo = TeX0003Const.STR.ZERO
      if (initFlag) {
        void init()
      }
    }
  }
}

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<TeX0003TwoWayType>({
  cpId: TeX0003Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or31946Const.CP_ID(1)]: or31946.value,
  [Or31995Const.CP_ID(1)]: or31995.value,
  [Or10929Const.CP_ID(1)]: or10929.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [OrX0001Const.CP_ID(1)]: orX0001.value,
  [Or53417Const.CP_ID(1)]: or53417.value,
  [Or31000Const.CP_ID(1)]: or31000.value,
  [Or11158Const.CP_ID(1)]: or11158.value,
  [Or31124Const.CP_ID(1)]: or31124.value,
  [Or31369Const.CP_ID(1)]: or31369.value,
  [Or31909Const.CP_ID(1)]: or31909.value,
  [Or30341Const.CP_ID(1)]: or30341.value,
  [Or32625Const.CP_ID(1)]: or32625.value,
  [Or32240Const.CP_ID(1)]: or32240.value,
  [Or11162Const.CP_ID(1)]: or11162.value,
  [Or31972Const.CP_ID(1)]: or31972.value,
  [Or30621Const.CP_ID(1)]: or30621.value,
  [Or31791Const.CP_ID(1)]: or31791.value,
  [Or31432Const.CP_ID(1)]: or31432.value,
  [Or32004Const.CP_ID(1)]: or32004.value,
  [Or31806Const.CP_ID(1)]: or31806.value,
  [Or31168Const.CP_ID(1)]: or31168.value,
  [Or31995Const.CP_ID(1)]: or31995.value,
  [Or31524Const.CP_ID(1)]: or31524.value,
  [Or32289Const.CP_ID(1)]: or32289.value,
  [Or31654Const.CP_ID(1)]: or31654.value,
  [Or28348Const.CP_ID(1)]: or28348.value,
  [Or33763Const.CP_ID(1)]: or33763.value,
  [OrX0115Const.CP_ID(1)]: orx0115.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
  [Or13844Const.CP_ID(1)]: or13844.value,
  [Or10269Const.CP_ID(1)]: or10269.value,
  [OrX0134Const.CP_ID(1)]: orX0134.value,
  [Or13872Const.CP_ID(1)]: or13872.value,
  [Or26257Const.CP_ID(1)]: or26257.value,
})

// ★利用者一覧詳細表示(Or00248)の「領域キー」を設定
Or00248Logic.state.set({
  uniqueCpId: or00248.value.uniqueCpId,
  state: {
    regionKey: Or00248Const.DEFAULT.REGION_KEY, // 「領域キー」を設定
    displayUserInfoSectionFlg: true,
  },
})

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.interRAI-method-care-assessment-table-title'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: true,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
    tooltipTextSaveBtn: t('tooltip.save'),
    tooltipTextPrintBtn: t('tooltip.care-plan2-print-setting-btn'),
    tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
    tooltipTextMasterBtn: t('tooltip.regular-master-icon'),
  },
})

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */
const callbackUserChange = (newSelfId: string) => {
  if (newSelfId) {
    // 「利用者」変更がある
    if (local.userId !== newSelfId) {
      // 画面入力データに変更がある場合
      if (isEdit.value) {
        // AC004-2と同じ
        openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-10430'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'destroy1',
          secondBtnLabel: t('btn.no'),
          thirdBtnType: 'normal3',
          thirdBtnLabel: t('btn.cancel'),
        })
          .then(async (dialogResult) => {
            switch (dialogResult) {
              case 'yes':
                // AC003(保存処理)を実行し
                await _update()
                break
              case 'no':
                // 処理続き
                initVariable()
                break
              case 'cancel':
                // 処理終了
                return
            }
          })
          .catch((error) => {
            console.error(error)
          })
      }
      // 画面.利用者ID = 返回情報.利用者ID
      local.userId = newSelfId
      systemCommonsStore.setUserSelectSelfId(newSelfId, props.uniqueCpId)
      // 画面共通情報.計画対象期間ID（ゼロに設定）
      local.planPeriod.sc1Id = TeX0003Const.STR.ZERO
      // 画面共通情報.アセスメントID（ゼロに設定）
      local.history.raiId = TeX0003Const.STR.ZERO
      void init()
    }
  }
}

systemCommonsStore.setUserSelectZenkaiSvJigyoIdList([''])
systemCommonsStore.setUserSelectSelfId('')

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange)

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * refValueを初期化
 */
const refValueInit = () => {
  refValue.value = {
    kijunbiYmd: local.kijunbiYmd.value,
    sakuseiId: local.sakuseiId,
  } as TeX0003TwoWayType
  // 初期値に設定する
  useScreenStore().setCpTwoWay({
    cpId: TeX0003Const.CP_ID(1),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}
/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // 基盤処理 - お気に入りボタンが押下された場合、お気に入り処理を実行する
      setOr11871Event({ favoriteEventFlg: false })
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      await _update()
      setOr11871Event({ saveEventFlg: false })
    }
    if (newValue.createEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 新規ボタンが押下された場合、新規作成処理を実行する
        await addBtnClick()
        setOr11871Event({ createEventFlg: false })
      }
    }
    if (newValue.printEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 印刷ボタンが押下された場合、印刷設定画面を表示する
        await printSettingIconClick()
        setOr11871Event({ printEventFlg: false })
      }
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      await masterIconClick()
      setOr11871Event({ masterEventFlg: false })
    }
  }
)

/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => Or13844Logic.event.get(or13844.value.uniqueCpId),
  async (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    if (!newValue?.preBtnClickFlg && !newValue.nextBtnClickFlg) {
      return
    }
    // 画面入力データに変更がある場合
    if (isEdit.value) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case 'yes':
          // AC003(保存処理)を実行し
          await _update()
          break
        case 'no':
          // 処理続き
          initVariable()
          break
        case 'cancel':
          // 処理終了
          return
      }
    }

    setOr13844Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
    local.krirekiNo = TeX0003Const.STR.ZERO
    local.history = {} as HistoryInfoEntity

    if (newValue?.preBtnClickFlg) {
      await periodChangeSelect(TeX0003Const.PERIOD_KBN_PREV)
    } else if (newValue.nextBtnClickFlg) {
      await periodChangeSelect(TeX0003Const.PERIOD_KBN_NEXT)
    }
  },
  { deep: true }
)

/**
 * AC017_「履歴-前へアイコンボタン」押下
 * AC018_「履歴-次へアイコンボタン」押下
 * 履歴変更の監視
 */
watch(
  () => Or13872Logic.event.get(or13872.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }
    if (!newValue?.preBtnClickFlg && !newValue.nextBtnClickFlg) {
      return
    }
    // 画面入力データに変更がある場合
    if (isEdit.value) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case 'yes':
          // AC003(保存処理)を実行し
          await _update()
          break
        case 'no':
          // 処理続き
          initVariable()
          break
        case 'cancel':
          // 処理終了
          return
      }
    }

    setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })

    if (newValue?.preBtnClickFlg) {
      //「履歴-前へ アイコンボタン」押下
      await assessmentInterRAIAHistoryChangeSelect(TeX0003Const.PERIOD_KBN_PREV)

      // 履歴情報がNULLの場合
      if (!local.history) {
        // 処理終了にする。
        return
      }

      await setFormData()
    } else if (newValue?.nextBtnClickFlg) {
      //「履歴-次へ アイコンボタン」押下
      await assessmentInterRAIAHistoryChangeSelect(TeX0003Const.PERIOD_KBN_NEXT)

      // 履歴情報がNULLの場合
      if (!local.history) {
        // 処理終了にする。
        return
      }

      await setFormData()
    }
  },
  { deep: true }
)

/**
 * AC011_「削除」押下_AC011
 */
watch(
  () => orX0001Type.value,
  (newValue) => {
    if (newValue) {
      // 返す削除種別が0（キャンセル）の場合
      if (TeX0003Const.STR.ZERO === newValue.deleteSyubetsu) {
        // 処理終了にする
        return
      }

      // 削除確認画面ダイアログに「現在表示している画面のみ削除する。」を選択する場合
      if (TeX0003Const.STR.ONE === newValue.deleteSyubetsu) {
        // 更新区分を「D:削除」
        local.updateKbn = UPDATE_KBN.DELETE
        // 履歴更新区分を「U:更新」
        local.historyUpdateKbn = UPDATE_KBN.UPDATE
        // アセスメント(インターライ)画面履歴の最新情報を取得する
        deleteCurrentTabSubInfo(newValue.deleteSyubetsu)
      }
      // 削除確認画面ダイアログに「表示している画面を履歴ごと削除する」を選択する場合
      else if (TeX0003Const.STR.TWO === newValue.deleteSyubetsu) {
        // 更新区分を「D:削除」
        local.updateKbn = UPDATE_KBN.DELETE
        // 履歴更新区分を「D:削除」
        local.historyUpdateKbn = UPDATE_KBN.DELETE
        deleteCurrentTabSubInfo(newValue.deleteSyubetsu)
      }

      // 作成者選択アイコンボタン
      localOneway.authorSelectOneway.text!.orX0157InputOneway.disabled = true
      // 基準日、基準日カレンダを非活性
      localOneway.createDateOneway.disabled = true

      // ボタンは実行無効(新規、複写、印刷、削除)
      deleteFlag.value = true
    }
  }
)

/**
 * 職員検索画面変更ワッチャー
 */
watch(
  () => or26257Type.value,
  (newValue) => {
    // 返す職員IDが空ではなく、または返す職員IDが「0」ではない場合
    if (newValue?.shokuin && TeX0003Const.STR.ZERO !== newValue?.shokuin.chkShokuId) {
      local.orX0157.value = newValue?.shokuin.shokuin1Knj + ' ' + newValue.shokuin.shokuin2Knj
      local.sakuseiId = newValue?.shokuin.chkShokuId
      refValue.value!.sakuseiId = newValue?.shokuin.chkShokuId
    }
    // 上記以外の場合
    else {
      // 画面項目をクリア
      // 画面.作成者ID
      local.sakuseiId = TeX0003Const.STR.EMPTY
      // 画面.作成者名
      local.orX0157.value = TeX0003Const.STR.EMPTY
    }
  },
  { deep: true }
)

/**
 * 基準日の変更
 */
watch(
  () => local.kijunbiYmd,
  () => {
    refValue.value!.kijunbiYmd = local.kijunbiYmd.value
  }
)

/**
 * 計画対象期間選択変更ワッチャー
 */
watch(
  () => OrX0115Logic.event.get(orx0115.value.uniqueCpId),
  async (newValue) => {
    // 返す期間IDが空ではなく、かつ、返す期間IDが「0」ではない場合
    if(newValue?.kikanId && newValue?.kikanId !== TeX0003Const.STR.ZERO) {
      // 画面共通情報.アセスメントID（ゼロに設定）
      local.history.raiId = TeX0003Const.STR.ZERO
      local.planPeriod.sc1Id = newValue?.kikanId
      // AC001と同じ
      await init()
    }
})
/**************************************************
 * 関数
 **************************************************/
/**
 * 現在のタブページのデータを削除する
 *
 * @param param - 削除ボタンの選択値
 */
const deleteCurrentTabSubInfo = (param: string) => {
  allTabParamSet({
    executeFlag: 'delete',
    deleteBtnValue: param,
    kikanKanriFlg: local.kikanKanriFlg,
    raiId: local.raiId,
    kijunbiYmd: local.kijunbiYmd.value,
    sakuseiId: local.sakuseiId,
    historyInfo: local.history,
    planPeriodInfo: local.planPeriod,
    assType: local.assType,
    yokaiKbn: local.yokaiKbn,
    subInfoB: local.subInfoB,
    syubetsuId: local.syubetsuId,
    svJigyoKnj: local.svJigyoKnj,
    updateKbn: local.updateKbn,
    historyUpdateKbn: local.historyUpdateKbn,
    tableCreateKbn: local.tableCreateKbn,
    userId: local.userId,
    svJigyoId: local.svJigyoId,
  } as TransmitParam)
}

/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * メニューエベントを設定
 *
 * @param state - イベント
 */
const setOr11871State = (state: Record<string, boolean>) => {
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: state,
  })
}

/**
 * 画面コントロール表示設定
 */
const setFormData = async () => {
  localOneway.planningPeriodSelectOneway.plainningPeriodManageFlg = local.kikanKanriFlg
  // 期間管理フラグが「1:管理する」の場合
  if (TeX0003Const.STR.ONE === local.kikanKanriFlg) {
    // 計画対象期間 を表示にする
    planPeriodShow.value = true
    // 計画期間情報がNULLではない場合
    if (local.planPeriod) {
      // 履歴表示
      historyShow.value = true
      // 基準日表示
      baseDateShow.value = true
      // 作成者表示
      authorShow.value = true
      // 計画対象期間をセットする
      localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
        id: local.planPeriod.sc1Id,
        period: local.planPeriod.startYmd + SPACE_WAVE + local.planPeriod.endYmd,
        periodNo: local.planPeriod.periodNo ?? '0',
        periodCnt: local.planPeriod.periodCnt ?? '0',
      }

      // 履歴情報設定
      await historySet()
    }
    // 計画期間情報がNULLの場合
    else {
      // 計画対象期間-ページングを"0 / 0"で表示にする
      // 計画対象期間を固定文字「計画対象期間アイコンボタンから（改行）期間登録を行ってください」で表示※太字、赤色
      localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
        id: '0',
        period: '',
        periodNo: '0',
        periodCnt: '0',
      }
      // コンテンツエリア＞作成情報＞履歴、作成者、基準日、および、入力フォームを非表示にする
      // 履歴非表示
      historyShow.value = false
      // 基準日非表示
      baseDateShow.value = false
      // 作成者非表示
      authorShow.value = false
      // 入力フォームを非表示
      allTabParamSet({
        executeFlag: 'hidden',
        deleteBtnValue: TeX0003Const.STR.EMPTY,
        kikanKanriFlg: local.kikanKanriFlg,
        raiId: local.raiId,
        kijunbiYmd: local.kijunbiYmd.value,
        sakuseiId: local.sakuseiId,
        historyInfo: local.history,
        planPeriodInfo: local.planPeriod,
        assType: local.assType,
        yokaiKbn: local.yokaiKbn,
        subInfoB: local.subInfoB,
        syubetsuId: local.syubetsuId,
        svJigyoKnj: local.svJigyoKnj,
        updateKbn: local.updateKbn,
        historyUpdateKbn: local.historyUpdateKbn,
        tableCreateKbn: local.tableCreateKbn,
        userId: local.userId,
        svJigyoId: local.svJigyoId,
      } as TransmitParam)

      local.addBtnState = false
    }
  }
  // 期間管理フラグが「0:管理しない」の場合
  else if (TeX0003Const.STR.ZERO === local.kikanKanriFlg) {
    // 計画対象期間 を非表示にする
    planPeriodShow.value = false

    // 履歴情報設定
    await historySet()
  }
  refValueInit()
  isLoading.value = false
}

/**
 * 履歴情報設定
 */
const historySet = async () => {
  // 履歴情報がNULLではない場合
  if (local.history) {
    local.krirekiNo = local.history.krirekiNo
    // 基準日 ＝ 履歴情報.基準日
    local.kijunbiYmd.value = local.history.assDateYmd
    if (local.kijunbiYmd.mo01343) {
      local.kijunbiYmd.mo01343.value = local.history.assDateYmd ?? ''
    }

    // 作成者 ＝ 履歴情報.職員名
    local.orX0157.value = local.history.shokuinName
    local.sakuseiId = local.history.shokuinId

    // 履歴-ページング = 履歴情報.履歴番号＋" / "＋履歴情報.履歴総件数
    localOneway.hisotrySelectOneway.historyInfo = {
      /** アセスメントID */
      rirekiId: local.history.raiId ?? TeX0003Const.STR.EMPTY,
      /** 履歴番号 */
      krirekiNo: local.history.krirekiNo ?? '1',
      /** 履歴総件数 */
      krirekiCnt: local.history.krirekiCnt ?? '1',
    }

    // 画面共通情報.タブインデックスが[4][5][6]であり、かつ履歴情報.認知能力情報が5（認識できる意識がない）の場合
    if (
      (TeX0003Const.TAB.D === local.mo00043.id ||
        TeX0003Const.TAB.E === local.mo00043.id ||
        TeX0003Const.TAB.F === local.mo00043.id) &&
      TeX0003Const.STR.FIVE === local.history.c1
    ) {
      await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10183'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // 画面.タブインデックスを3（C）に設定する
      local.mo00043.id = TeX0003Const.TAB.C
    }

    getTabsData()
  }
  // 履歴情報情報がNULLの場合
  else {
    // 画面.計画対象期間IDが「0」に設定する
    // ・履歴-ページング = "1 / 1"
    localOneway.hisotrySelectOneway.historyInfo = {
      /** アセスメントID */
      rirekiId: TeX0003Const.STR.ZERO,
      /** 履歴番号 */
      krirekiNo: TeX0003Const.STR.ONE,
      /** 履歴総件数 */
      krirekiCnt: TeX0003Const.STR.ONE,
    }
    // 基準日を設定
    local.kijunbiYmd.value = systemCommonsStore.getSystemDate ?? ''

    // 作成者を設定
    // 作成者 ＝ ログイン情報.職員名
    local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? TeX0003Const.STR.EMPTY

    // 「AC004」の処理を行
    await addBtnClick()
  }
}

/**
 * コントロール初期化
 */
const initControl = async () => {
  localOneway.mo00043OnewayType.tabItems = [
    {
      id: TeX0003Const.TAB.A,
      title: TeX0003Const.TAB.A,
      tooltipText: TeX0003Const.TAB.A,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.B,
      title: TeX0003Const.TAB.B,
      tooltipText: TeX0003Const.TAB.B,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.C,
      title: TeX0003Const.TAB.C,
      tooltipText: TeX0003Const.TAB.C,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.D,
      title: TeX0003Const.TAB.D,
      tooltipText: TeX0003Const.TAB.D,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.E,
      title: TeX0003Const.TAB.E,
      tooltipText: TeX0003Const.TAB.E,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.F,
      title: TeX0003Const.TAB.F,
      tooltipText: TeX0003Const.TAB.F,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.G,
      title: TeX0003Const.TAB.G,
      tooltipText: TeX0003Const.TAB.G,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.H,
      title: TeX0003Const.TAB.H,
      tooltipText: TeX0003Const.TAB.H,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.I1,
      title: TeX0003Const.TAB.I1,
      tooltipText: TeX0003Const.TAB.I1,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.I2,
      title: TeX0003Const.TAB.I2,
      tooltipText: TeX0003Const.TAB.I2,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.J,
      title: TeX0003Const.TAB.J,
      tooltipText: TeX0003Const.TAB.J,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.K,
      title: TeX0003Const.TAB.K,
      tooltipText: TeX0003Const.TAB.K,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.L,
      title: TeX0003Const.TAB.L,
      tooltipText: TeX0003Const.TAB.L,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.M,
      title: TeX0003Const.TAB.M,
      tooltipText: TeX0003Const.TAB.M,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.N,
      title: TeX0003Const.TAB.N,
      tooltipText: TeX0003Const.TAB.N,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.O,
      title: TeX0003Const.TAB.O,
      tooltipText: TeX0003Const.TAB.O,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.P,
      title: TeX0003Const.TAB.P,
      tooltipText: TeX0003Const.TAB.P,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.Q,
      title: TeX0003Const.TAB.Q,
      tooltipText: TeX0003Const.TAB.Q,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.R,
      title: TeX0003Const.TAB.R,
      tooltipText: TeX0003Const.TAB.R,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.S,
      title: TeX0003Const.TAB.S,
      tooltipText: TeX0003Const.TAB.S,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.T,
      title: TeX0003Const.TAB.T,
      tooltipText: TeX0003Const.TAB.T,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.U,
      title: TeX0003Const.TAB.U,
      tooltipText: TeX0003Const.TAB.U,
      tooltipLocation: 'bottom',
    },
    {
      id: TeX0003Const.TAB.V,
      title: TeX0003Const.TAB.V,
      tooltipText: TeX0003Const.TAB.V,
      tooltipLocation: 'bottom',
    },
  ]

  // 「A」タブが選択状態
  local.mo00043.id = TeX0003Const.TAB.A

  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
      verticalLayout: true,
    },
  })
  // 利用者を全選択です。
  const filter = systemCommonsStore.getUserSelectHistoryFilterInitials()
  if (filter === undefined || filter.length === 0) {
    systemCommonsStore.setUserSelectFilterInitials([TeX0003Const.STR_ALL])
  }

  // 共通情報.事業所IDが事業所情報リストに存在する場合
  if (systemCommonsStore.getSvJigyoIdList && systemCommonsStore.getSvJigyoIdList.length > 0) {
    // 基盤作成 共通情報.事業所IDに対する事業所を選択
  }
  // 上記以外の場合
  else {
    // 基盤作成 事業所情報リスト.1件目を選択
  }

  // 共通処理の登録権限チェックを行う
  // if (await hasRegistAuth()) {
  //   // 権限有る：活性
  //   setOr11871State({ disabledSaveBtn: false })
  // } else {
  //   // 権限無し：非活性
  //   setOr11871State({ disabledSaveBtn: true })
  // }

  // 共通処理の印刷権限チェックを行う
  // if (await hasPrintAuth()) {
  //   // 権限有る：活性
  //   setOr11871State({ disabledPrintBtn: false })
  // } else {
  //   // 権限無し：非活性
  //   setOr11871State({ disabledPrintBtn: true })
  // }

  // 共通情報.e-文書法対象機能の電子ファイル保存設定区分が「1:適用する」の場合
  if (TeX0003Const.STR.ONE === systemCommonsStore.getEBunshoKbn) {
    logBtnShowFlg.value = true
  } else {
    // その他場合
    logBtnShowFlg.value = false
  }

  // 共通処理の外部出力権限チェックを行う
  // if (await hasOutputAuth()) {
  //   // 権限有る：活性
  //   csvOutputBtnDisabledFlg.value = false
  // } else {
  //   // 権限無し：非活性
  //   csvOutputBtnDisabledFlg.value = true
  // }
}

/**
 *  AC001_初期表示
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  await periodChangeSelect(TeX0003Const.PERIOD_KBN_OPEN)
  initFlag = true
}

/**
 * 計画期間変更【共通処理】/初期情報取得
 *
 * @param pageFlag - 0:選択している期間ID画面 | 1:選択している期間ID画面の前画面 | 2:選択している期間ID画面の後画面
 */
const periodChangeSelect = async (pageFlag: string) => {
  isLoading.value = true
  // 計画期間変更【共通処理】/初期情報取得
  const inputData: IAssessmentInterRAIPeriodChangeSelectInEntity = {
    svJigyoId: local.svJigyoId ?? TeX0003Const.STR.EMPTY,
    userId: local.userId ?? TeX0003Const.STR.EMPTY,
    shisetuId: systemCommonsStore.getShisetuId ?? TeX0003Const.STR.EMPTY,
    sc1Id: local.planPeriod?.sc1Id ?? TeX0003Const.STR.ZERO,
    krirekiNo: local.krirekiNo,
    raiId: local.history?.raiId ?? TeX0003Const.STR.ZERO,
    pageFlag: pageFlag,
    menu2Name: MID_LINE,
    menu3Name: TeX0003Const.MENU_3_KNJ,
    freeParameter: MID_LINE,
  }

  const resp: IAssessmentInterRAIPeriodChangeSelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIPeriodChangeSelect',
    inputData
  )
  if (SUCCESS_STATUS_CODE === resp.statusCode || 'success' === resp.statusCode) {
    // 上記で戻るエラー区分が「1」の場合
    if (resp.data.errKbn === TeX0003Const.STR.ONE) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11262'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })

      switch (dialogResult) {
        case 'yes':
          isLoading.value = false
          // 処理終了
          return
      }
    }
    // 上記で戻るエラー区分が「2」の場合
    else if (resp.data.errKbn === TeX0003Const.STR.TWO) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11263'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })

      switch (dialogResult) {
        case 'yes':
          isLoading.value = false
          // 処理終了
          return
      }
    }

    local.planPeriod = resp.data.planPeriodInfo
    local.history = resp.data.historyInfo
    local.kikanKanriFlg = resp.data.kikanKanriFlg
    local.syubetsuId = resp.data.syubetsuId
  }

  await setFormData()
}

/**
 * 画面履歴の最新情報を取得
 */
const getHistoryInfo = async () : Promise<boolean> => {
  let keepFlag = true
  const inputData: AssessmentInterRAIAHistorySelectEntity = {
    raiId: local.history?.raiId ?? TeX0003Const.STR.ZERO,
  }
  const resData: AssessmentInterRAIAHistorySelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIHistorySelect',
    inputData
  )
  if (SUCCESS_STATUS_CODE === resData.statusCode || 'success' === resData.statusCode) {
    // 取得したインターライ方式履歴情報<>NULL
    if (resData.data) {
      // 選定アセスメント種別 > 0
      if (parseInt(resData.data?.capType) > 0) {
        // 検討アセスメント種別 > 0
        if (parseInt(resData.data?.plnType) > 0) {
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11267'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'normal3',
            thirdBtnLabel: t('btn.cancel'),
          })
          switch (dialogResult) {
            // はい
            case 'yes':
              // 画面.選定表・検討表作成区分 = "1"、処理続き
              local.tableCreateKbn = TeX0003Const.STR.ONE
              keepFlag = true
              break
            // キャンセル
            case 'cancel':
              // 処理終了
              keepFlag = false
              break
          }
        }
        // 検討アセスメント種別 <= 0
        else {
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11266'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'normal3',
            thirdBtnLabel: t('btn.cancel'),
          })
          switch (dialogResult) {
            // はい
            case 'yes':
              // 画面.選定表・検討表作成区分 = "0"、処理続き
              local.tableCreateKbn = TeX0003Const.STR.ZERO
              keepFlag = true
              break
            // キャンセル
            case 'cancel':
              // 処理終了
              keepFlag = false
              break
          }
        }
      }
    }
  }
  return keepFlag
}

/**
 * AC003_「保存ボタン」押下
 */
const _update = async () => {
  // 画面入力データに変更がない場合
  if (!isEdit.value || TeX0003Const.STR.ZERO === local.history.raiId || local.updateKbn === UPDATE_KBN.DELETE || local.historyUpdateKbn === UPDATE_KBN.DELETE) {
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-21800'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // 変数の初期化
    initVariable()
    // 画面共通情報.履歴情報 = 前回値
    local.history = history
    // 履歴情報を取得する
    await assessmentInterRAIAHistoryChangeSelect(TeX0003Const.PERIOD_KBN_OPEN)

    // 履歴情報がNULLの場合
    if (local.history) {
      await setFormData()
    }

    // 処理終了にする
    return
  }
  // アセスメント(インターライ)画面履歴の最新情報を取得する
  if(!await getHistoryInfo()) {
    // キャンセル：処理終了
    return
  }

  // 画面タブ入力タブデータに変更がある場合
  if (isTabDataEdit.value) {
    // 更新区分がNULLの場合
    if (!local.updateKbn) {
      // 更新区分 = U
      local.updateKbn = UPDATE_KBN.UPDATE
    }
  }

  // 画面共通入力データに変更がある場合
  if (isCommonDataEdit.value) {
    // 履歴更新区分がNULLの場合
    if (!local.historyUpdateKbn) {
      // 履歴更新区分 = U
      local.historyUpdateKbn = UPDATE_KBN.UPDATE
    }
  }

  // 各タブのパラメータを作成する
  allTabParamSet({
    executeFlag: 'save',
    deleteBtnValue: TeX0003Const.STR.EMPTY,
    kikanKanriFlg: local.kikanKanriFlg,
    raiId: local.raiId,
    kijunbiYmd: local.kijunbiYmd.value,
    sakuseiId: local.sakuseiId,
    historyInfo: local.history,
    planPeriodInfo: local.planPeriod,
    assType: local.assType,
    yokaiKbn: local.yokaiKbn,
    subInfoB: local.subInfoB,
    syubetsuId: local.syubetsuId,
    svJigyoKnj: local.svJigyoKnj,
    updateKbn: local.updateKbn,
    historyUpdateKbn: local.historyUpdateKbn,
    tableCreateKbn: local.tableCreateKbn,
    userId: local.userId,
    svJigyoId: local.svJigyoId,
  } as TransmitParam)
}

/**
 * AC004_「新規ボタン」押下
 */
const addBtnClick = async () => {
  // 期間管理フラグが「1:管理する」
  if (local.kikanKanriFlg === TeX0003Const.STR.ONE) {
    // 計画期間情報.期間総件数 = 0（期間なし）
    if (
      localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodCnt === TeX0003Const.STR.ZERO
    ) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11300'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.ok'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if ('yes' === dialogResult) {
        // AC013を実行
        await planningPeriodSelectClick()
      }
    }
  }

  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }
  // 二回目新規ボタン押下する場合
  if (local.addBtnState) {
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.attention'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.assessment')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  }
  // アセスメント(インターライ)画面の新規情報を取得する
  await getCreateSubInfo()

  // 画面共通情報.アセスメントID = ゼロ
  if(local.history) {
    history = local.history
    local.history.raiId = TeX0003Const.STR.ZERO
  }

  // 画面共通情報.作成者ID = 共通情報.職員ID
  local.sakuseiId = systemCommonsStore.getStaffId ?? ''

  // 画面.履歴番号 = 履歴の最大値+1
  // 画面.履歴-ページング = 画面.履歴番号/画面.履歴番号
  localOneway.hisotrySelectOneway.historyInfo = {
    /** アセスメントID */
    rirekiId: TeX0003Const.STR.ZERO,
    /** 履歴番号 */
    krirekiNo: String(Number(local.history.krirekiCnt ?? TeX0003Const.STR.ZERO) + 1),
    /** 履歴総件数 */
    krirekiCnt: String(Number(local.history.krirekiCnt ?? TeX0003Const.STR.ZERO) + 1),
  }
  local.krirekiNo = localOneway.hisotrySelectOneway.historyInfo.krirekiNo

  // ログイン情報.職員名
  local.orX0157.value = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''

  // 共通情報.基準日
  local.kijunbiYmd.value = systemCommonsStore.getSystemDate ?? ''
  if (local.kijunbiYmd.mo01343) {
    local.kijunbiYmd.mo01343.value = systemCommonsStore.getSystemDate ?? ''
  }

  local.addBtnState = true

  // 画面.履歴更新区分 = "C"（新規）
  local.historyUpdateKbn = UPDATE_KBN.CREATE

  // 画面.更新区分 = "C"（新規）
  local.updateKbn = UPDATE_KBN.CREATE

  // それぞれサブ画面の新規操作
  AllSubAdd()
}

/**
 * 新規情報を取得する
 */
const getCreateSubInfo = async () => {
  isLoading.value = true
  // バックエンドAPIから初期情報取得
  const inputData: IAssessmentInterRAINewSelectInEntity = {
    userId: local.userId ?? '',
    lsYmd: systemCommonsStore.getSystemDate ?? '',
    svJigyoId: local.svJigyoId ?? '',
    subKbn: local.mo00043.id,
  }

  const resp: IAssessmentInterRAINewSelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAINewSelect',
    inputData
  )
  if (SUCCESS_STATUS_CODE === resp.statusCode || 'success' === resp.statusCode) {
    local.assType = resp.data.assType
    local.yokaiKbn = resp.data.yokaiKbn
    local.subInfoB = resp.data.subInfoB
  }
  isLoading.value = false
}

/**
 * サブ画面の新規操作
 */
const AllSubAdd = () => {
  // アセスメント(インターライ)画面「A ~ V」の新規情報を取得する。
  allTabParamSet({
    executeFlag: 'add',
    deleteBtnValue: TeX0003Const.STR.EMPTY,
    kikanKanriFlg: local.kikanKanriFlg,
    raiId: local.raiId,
    kijunbiYmd: local.kijunbiYmd.value,
    sakuseiId: local.sakuseiId,
    historyInfo: local.history,
    planPeriodInfo: local.planPeriod,
    assType: local.assType,
    yokaiKbn: local.yokaiKbn,
    subInfoB: local.subInfoB,
    syubetsuId: local.syubetsuId,
    svJigyoKnj: local.svJigyoKnj,
    updateKbn: local.updateKbn,
    historyUpdateKbn: local.historyUpdateKbn,
    tableCreateKbn: local.tableCreateKbn,
    userId: local.userId,
    svJigyoId: local.svJigyoId,
  } as TransmitParam)
}

/**
 * アセスメント(インターライ)画面履歴の最新情報を取得する
 */
const getTabsData = () => {
  if (!historyShow.value && !authorShow.value && !baseDateShow.value) {
    return
  }
  allTabParamSet({
    executeFlag: 'getData',
    deleteBtnValue: TeX0003Const.STR.EMPTY,
    kikanKanriFlg: local.kikanKanriFlg,
    raiId: local.raiId,
    kijunbiYmd: local.kijunbiYmd.value,
    sakuseiId: local.sakuseiId,
    historyInfo: local.history,
    planPeriodInfo: local.planPeriod,
    assType: local.assType,
    yokaiKbn: local.yokaiKbn,
    subInfoB: local.subInfoB,
    syubetsuId: local.syubetsuId,
    svJigyoKnj: local.svJigyoKnj,
    updateKbn: local.updateKbn,
    historyUpdateKbn: local.historyUpdateKbn,
    tableCreateKbn: local.tableCreateKbn,
    userId: local.userId,
    svJigyoId: local.svJigyoId,
  } as TransmitParam)
}

/**
 * AC005_「複写ボタン」押下
 */
const copyBtnClick = () => {
  // 実行無効制御
  if (deleteFlag.value) {
    return
  }

  orX0134Type.value.raiId = local.planPeriod.sc1Id
  orX0134Type.value.sc1Id = local.history.raiId

  localOneway.orX0134OnewayModel.syubetsuId =
    systemCommonsStore.getSyubetu ?? TeX0003Const.STR.EMPTY
  localOneway.orX0134OnewayModel.shisetuId =
    systemCommonsStore.getShisetuId ?? TeX0003Const.STR.EMPTY
  localOneway.orX0134OnewayModel.userId = local.userId ?? TeX0003Const.STR.EMPTY
  localOneway.orX0134OnewayModel.kikanFlg = local.kikanKanriFlg
  localOneway.orX0134OnewayModel.currentScreenId = local.mo00043.id

  // Or0134のダイアログ開閉状態を更新する
  OrX0134Logic.state.set({
    uniqueCpId: orX0134.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC006_「印刷設定アイコンボタン」押下
 */
const printSettingIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // GUI00793 印刷設定画面をポップアップで起動する
  // Or10269のダイアログ開閉状態を更新する
  Or10269Logic.state.set({
    uniqueCpId: or10269.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        prtNo: getPrtNo(local.mo00043.id),
        svJigyoId: local.svJigyoId,
        shisetuId: systemCommonsStore.getShisetuId,
        tantoId: systemCommonsStore.getManagerId,
        syubetsuId: systemCommonsStore.getSyubetu,
        sectionName: TeX0003Const.SECTION_NAME,
        userId: local.userId,
        svJigyoKnj: local.svJigyoKnj,
        processYmd: convertDateToSeireki(undefined),
        assessmentId: local.history?.raiId ?? TeX0003Const.STR.ZERO,
        parentUserIdSelectDataFlag: false,
        focusSettingInitial: [MID_LINE],
        selectedUserCounter: MID_LINE,
      } as Or10269Param,
    },
  })
}

/**
 * 帳票番号取得
 *
 * @param tabId - タブ選択ID
 */
const getPrtNo = (tabId: string) => {
  switch (tabId) {
    case TeX0003Const.TAB.A:
      return TeX0003Const.STR.TWO
    case TeX0003Const.TAB.B:
      return TeX0003Const.STR.THREE
    case TeX0003Const.TAB.C:
      return TeX0003Const.STR.FOUR
    case TeX0003Const.TAB.D:
      return TeX0003Const.STR.FIVE
    case TeX0003Const.TAB.E:
      return TeX0003Const.STR.SIX
    case TeX0003Const.TAB.F:
      return TeX0003Const.STR.SEVEN
    case TeX0003Const.TAB.G:
      return TeX0003Const.STR.EIGHT
    case TeX0003Const.TAB.H:
      return TeX0003Const.STR.NINE
    case TeX0003Const.TAB.I1:
      return TeX0003Const.STR.TEN
    case TeX0003Const.TAB.I2:
      return TeX0003Const.STR.TEN
    case TeX0003Const.TAB.J:
      return TeX0003Const.STR.ELEVEN
    case TeX0003Const.TAB.K:
      return TeX0003Const.STR.TWELVE
    case TeX0003Const.TAB.L:
      return TeX0003Const.STR.THIRTEEN
    case TeX0003Const.TAB.M:
      return TeX0003Const.STR.FOURTEEN
    case TeX0003Const.TAB.N:
      return TeX0003Const.STR.FIFTEEN
    case TeX0003Const.TAB.O:
      return TeX0003Const.STR.SIXTEEN
    case TeX0003Const.TAB.P:
      return TeX0003Const.STR.SEVENTEEN
    case TeX0003Const.TAB.Q:
      return TeX0003Const.STR.EIGHTEEN
    case TeX0003Const.TAB.R:
      return TeX0003Const.STR.NINETEEN
    case TeX0003Const.TAB.S:
      return TeX0003Const.STR.TEENTY
    case TeX0003Const.TAB.T:
      return TeX0003Const.STR.TWENTI_ONE
    case TeX0003Const.TAB.U:
      return TeX0003Const.STR.TWENTI_TWO
    case TeX0003Const.TAB.V:
      return TeX0003Const.STR.TWENTI_THREE
  }
}

/**
 * AC007_「マスタ他設定アイコンボタン」押下
 */
const masterIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // Or53417のダイアログ開閉状態を更新する
  localOneway.or53417Oneway = {
    shisetuId: systemCommonsStore.getShisetuId,
    svJigyoId: local.svJigyoId,
    type: TeX0003Const.STR.THREE
  } as Or53417OnewayType
  Or53417Logic.state.set({
    uniqueCpId: or53417.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC009_「ログ」押下
 */
const logClick = () => {
  // TODO
  void openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('WEB化後ログボタン機能未定'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
}

/**
 * AC010_「CSV」押下
 */
const csvClick = () => {
  // GUI00788 ［アセスメント（インターライ）CSV出力］画面をポップアップで起動する
  // 获取当前日期
  const now = new Date()
  // 获取当前月的开始日期
  const startDate = new Date(now.getFullYear(), now.getMonth(), 1)
  // 获取当前月的结束日期
  const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
  localOneway.Or10279Oneway = {
    staffId: systemCommonsStore.getStaffId,
    baseDate: systemCommonsStore.getSystemDate,
    officeId: systemCommonsStore.getStaffId,
    kinouName: TeX0003Const.MENU_3_KNJ,
    startScYmd: getYmdStr(startDate),
    endScYmd: getYmdStr(endDate),
  } as Or10279OneWayType

  // Or10279のダイアログ開閉状態を更新する
  Or10279Logic.state.set({
    uniqueCpId: or10279.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 日付(yyyy/MM/dd)を取得する
 *
 * @param date - 日付
 */
const getYmdStr = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}${month}${day}`
}

/**
 * AC011_「削除」押下
 */
const deleteClick = () => {
  // 画面.計画期間情報が空の場合
  if (!localOneway.planningPeriodSelectOneway.planningPeriodInfo) {
    // 処理終了にする
    return
  }
  // 二回目削除ボタン押下する場合
  if (deleteFlag.value) {
    // 処理終了にする
    return
  }
  // GUI04471_［削除確認］画面をポップアップで起動する
  orX0001Oneway.value.createYmd = local.kijunbiYmd.value ?? TeX0003Const.STR.EMPTY
  orX0001Oneway.value.kinouKnj = t('label.assessment')
  orX0001Oneway.value.selectTabName = local.mo00043.id
  orX0001Oneway.value.startTabName = '選定表、検討表、A'
  orX0001Oneway.value.endTabName = 'V'

  // OrX0001のダイアログ開閉状態を更新する
  OrX0001Logic.state.set({
    uniqueCpId: orX0001.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC022_タブ選択
 *
 * @param param - タブ選択ID
 */
const updateModelValue = async (param: Mo00043Type) => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // 切替先タブが「D、E、F」中の１つ
  if (
    TeX0003Const.TAB.D === param.id ||
    TeX0003Const.TAB.E === param.id ||
    TeX0003Const.TAB.F === param.id
  ) {
    // 日常の意思決定の認知能力 = 5「認識できる意識がない、昏睡」の場合
    if (local.history.c1 === TeX0003Const.STR.FIVE) {
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10183'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if ('yes' === dialogResult) {
        // 処理終了
        return
      }
    } else {
      local.mo00043.id = param.id
    }
  } else {
    local.mo00043.id = param.id
  }
  getTabsData()
}

/**
 * AC013_「計画対象期間選択アイコンボタン」押下
 */
const planningPeriodSelectClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // GUI00070 対象期間画面をポップアップで起動する
  localOneway.orX0115Oneway = {
    kindId: local.syubetsuId,
    sc1Id: local.planPeriod.sc1Id,
    regionKey: props.uniqueCpId
  } as OrX0115OnewayType
  OrX0115Logic.state.set({
    uniqueCpId: orx0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「履歴選択アイコンボタン」押下
 */
const historySelectClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case 'yes':
        // AC003(保存処理)を実行し
        await _update()
        break
      case 'no':
        // 処理続き
        initVariable()
        break
      case 'cancel':
        // 処理終了
        return
    }
  }

  // GUI00792 ［履歴選択］画面 アセスメント(インターライ)をポップアップで起動
  localOneway.or10929Oneway = {
    svJigyoId: local.svJigyoId ?? TeX0003Const.STR.EMPTY,
    userId: local.userId ?? TeX0003Const.STR.EMPTY,
    sc1Id: local.planPeriod.sc1Id ?? TeX0003Const.STR.EMPTY,
    raiId: local.history.raiId ?? TeX0003Const.STR.EMPTY,
  } as HistorySelectInfoType
  // Or10929のダイアログ開閉状態を更新する
  Or10929Logic.state.set({
    uniqueCpId: or10929.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 作成者選択画面クリック
 *
 */
const authorSelectClick = () => {
  const list: SvJigyo[] = []
  for (const item of systemCommonsStore.getSvJigyoIdList) {
    if (item) {
      list.push({
        svJigyoId: item,
      } as SvJigyo)
    }
  }
  localOneway.or26257OnewayModel.svJigyoIdList = list
  // GUI00220 職員検索画面をポップアップで起動
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = (
  uniqueCpId: string,
  state: Or21813StateType
): Promise<typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO | typeof DIALOG_BTN.CANCEL> => {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(uniqueCpId)

        let result = DIALOG_BTN.YES as
          | typeof DIALOG_BTN.YES
          | typeof DIALOG_BTN.NO
          | typeof DIALOG_BTN.CANCEL

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }
        if (event?.thirdBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 確認ダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = (
  uniqueCpId: string,
  state: Or21814OnewayType
): Promise<typeof DIALOG_BTN.YES | typeof DIALOG_BTN.NO | typeof DIALOG_BTN.CANCEL> => {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(uniqueCpId)

        let result = DIALOG_BTN.YES as
          | typeof DIALOG_BTN.YES
          | typeof DIALOG_BTN.NO
          | typeof DIALOG_BTN.CANCEL

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }
        if (event?.thirdBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = DIALOG_BTN.CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * AC016_選択前の履歴から変更
 *
 * @param selectItem - 選択の履歴
 */
const historySelectChange = async (selectItem: HistorySelectTableDataItem | undefined) => {
  // 選択前の履歴から変更がなく
  if (
    selectItem?.raiId === localOneway.hisotrySelectOneway.historyInfo?.rirekiId &&
    !selectItem?.sc1Id &&
    TeX0003Const.STR.ZERO === selectItem?.sc1Id
  ) {
    // 処理終了にする。
    return
  }
  // 上記以外の場合
  // 選択前の履歴から変更がある場合
  else {
    // アセスメント(インターライ)画面履歴変更処理
    local.history.raiId = selectItem ? selectItem.raiId : TeX0003Const.STR.EMPTY

    await assessmentInterRAIAHistoryChangeSelect(TeX0003Const.PERIOD_KBN_OPEN)

    // 履歴情報がNULLの場合
    if (!local.history) {
      // 処理終了にする。
      return
    }

    await setFormData()
  }
}

/**
 * 保存後
 *
 * @param errKbn - エラー区分
 *
 * @param sc1Id - 計画対象期間ID
 *
 * @param raiId - アセスメントID
 */
const saveEnd = async (errKbn: string, sc1Id: string, raiId: string) => {
  // 上記で戻るエラー区分が「1」の場合
  if ('1' === errKbn) {
    await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40019'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
  }
  // 上記で戻るエラー区分が「2」の場合
  else if ('2' === errKbn) {
    // 以下のメッセージを表示
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11457'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
  }
  // 保存再表示の場合
  // 変数の初期化
  initVariable()

  local.planPeriod.sc1Id = sc1Id
  local.history.raiId = raiId
  // AC001と同じ
  await init()
}

/**
 * 戻り値
 *
 * @param newValue - 新しい値
 */
const handleReturn = async (newValue: OrX0134Type) => {
  if (newValue) {
    // 複写画面の戻り値.履歴IDが空の場合
    if (!newValue.raiId) {
      // 処理終了
      return
    }
    // 複写画面の戻り値.履歴IDが空ではない、かつ、複写画面の戻り値.複数セクション複写フラグが「1：該当」の場合
    if (newValue.copyFlg === TeX0003Const.STR.ONE) {
      // 複写画面の戻り値.履歴IDを元に、サブ情報を再取得し、画面に表示する。※AC001-1のAPIを利用。(画面項目の値のみ再設定する。アセスメントIDと更新回数は上書きしない)
      allTabParamSet({
        executeFlag: 'copy',
        deleteBtnValue: TeX0003Const.STR.EMPTY,
        kikanKanriFlg: local.kikanKanriFlg,
        raiId: newValue.raiId,
        kijunbiYmd: local.kijunbiYmd.value,
        sakuseiId: local.sakuseiId,
        historyInfo: local.history,
        planPeriodInfo: local.planPeriod,
        assType: local.assType,
        yokaiKbn: local.yokaiKbn,
        subInfoB: local.subInfoB,
        syubetsuId: local.syubetsuId,
        svJigyoKnj: local.svJigyoKnj,
        updateKbn: local.updateKbn,
        historyUpdateKbn: local.historyUpdateKbn,
        tableCreateKbn: local.tableCreateKbn,
        userId: local.userId,
        svJigyoId: local.svJigyoId,
      } as TransmitParam)
    }
    // 複写画面の戻り値.履歴IDが空ではない、かつ、複写画面の戻り値.複数セクション複写フラグが「0：非該当」の場合
    else {
      // ・画面.アセスメントIDに複写画面の戻り値.履歴IDをセットする。
      // 新規の場合
      if (local.addBtnState) {
        local.planPeriod.sc1Id = newValue.sc1Id
        local.history.raiId = newValue.raiId
        // 複写きされた計画期間データを取得する
        await periodChangeSelect(TeX0003Const.PERIOD_KBN_OPEN)
        // 複写された履歴情報を取得する
        await assessmentInterRAIAHistoryChangeSelect(TeX0003Const.PERIOD_KBN_OPEN)
      }
      // その他の場合
      else {
        // 履歴情報とサブ情報を再取得し、画面に表示する。※「履歴選択アイコンボタン」押下のAC016-4以降の処理を行う。
        allTabParamSet({
          executeFlag: 'getData',
          deleteBtnValue: TeX0003Const.STR.EMPTY,
          kikanKanriFlg: local.kikanKanriFlg,
          raiId: TeX0003Const.STR.EMPTY,
          kijunbiYmd: local.kijunbiYmd.value,
          sakuseiId: local.sakuseiId,
          historyInfo: local.history,
          planPeriodInfo: local.planPeriod,
          assType: local.assType,
          yokaiKbn: local.yokaiKbn,
          subInfoB: local.subInfoB,
          syubetsuId: local.syubetsuId,
          svJigyoKnj: local.svJigyoKnj,
          updateKbn: local.updateKbn,
          historyUpdateKbn: local.historyUpdateKbn,
          tableCreateKbn: local.tableCreateKbn,
          userId: local.userId,
          svJigyoId: local.svJigyoId,
        } as TransmitParam)
      }
    }
  }
}

/**
 * 各タブのパラメータを作成する
 *
 * @param param - パラメータ
 */
const allTabParamSet = (param: TransmitParam) => {
  switch (local.mo00043.id) {
    // タブA
    case TeX0003Const.TAB.A:
      // Or31000のダイアログ状態を更新する
      Or31000Logic.state.set({
        uniqueCpId: or31000.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.B:
      // Or31369のダイアログ状態を更新する
      Or31369Logic.state.set({
        uniqueCpId: or31369.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.C:
      // Or31124のダイアログ状態を更新する
      Or31124Logic.state.set({
        uniqueCpId: or31124.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.D:
      // Or31654のダイアログ状態を更新する
      Or31654Logic.state.set({
        uniqueCpId: or31654.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.E:
      // Or31909のダイアログ状態を更新する
      Or31909Logic.state.set({
        uniqueCpId: or31909.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.F:
      // Or31791のダイアログ状態を更新する
      Or31791Logic.state.set({
        uniqueCpId: or31791.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.G:
      // Or31432のダイアログ状態を更新する
      Or31432Logic.state.set({
        uniqueCpId: or31432.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.H:
      // Or28348のダイアログ状態を更新する
      Or28348Logic.state.set({
        uniqueCpId: or28348.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.I1:
      // Or30341のダイアログ状態を更新する
      Or30341Logic.state.set({
        uniqueCpId: or30341.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.I2:
      // Or31995:有機体:アセスメント(インターライ)画面I-2
      Or31995Logic.state.set({
        uniqueCpId: or31995.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.J:
      // Or11158のダイアログ状態を更新する
      Or11158Logic.state.set({
        uniqueCpId: or11158.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.K:
      Or32240Logic.state.set({
        uniqueCpId: or32240.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.L:
      // Or31806:有機体:アセスメント(インターライ)画面N
      Or31806Logic.state.set({
        uniqueCpId: or31806.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.M:
      // Or31524:有機体:アセスメント(インターライ)画面M
      Or31524Logic.state.set({
        uniqueCpId: or31524.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.N:
      // Or32004:有機体:アセスメント(インターライ)画面N
      Or32004Logic.state.set({
        uniqueCpId: or32004.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.O:
      // 本タブ担当実装コードを追加します
      Or11162Logic.state.set({
        uniqueCpId: or11162.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.P:
      // Or31946:有機体:アセスメント(インターライ)画面P
      Or31946Logic.state.set({
        uniqueCpId: or31946.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.Q:
      // Or32625のダイアログ状態を更新する
      Or32625Logic.state.set({
        uniqueCpId: or32625.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.R:
      Or30621Logic.state.set({
        uniqueCpId: or30621.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.S:
      Or31972Logic.state.set({
        uniqueCpId: or31972.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.T:
      // Or33763:有機体:アセスメント(インターライ)画面T
      Or33763Logic.state.set({
        uniqueCpId: or33763.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.U:
      //  Or31168:アセスメント(インターライ)画面U
      Or31168Logic.state.set({
        uniqueCpId: or31168.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    case TeX0003Const.TAB.V:
      // Or32289
      Or32289Logic.state.set({
        uniqueCpId: or32289.value.uniqueCpId,
        state: {
          param: param,
        },
      })
      break
    default:
      break
  }
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
const setOr13844Event = (event: Record<string, boolean>) => {
  Or13844Logic.event.set({
    uniqueCpId: or13844.value.uniqueCpId,
    events: event,
  })
}

/**
 * Or13872イベント発火
 *
 * @param event - イベント
 */
const setOr13872Event = (event: Record<string, boolean>) => {
  Or13844Logic.event.set({
    uniqueCpId: or13872.value.uniqueCpId,
    events: event,
  })
}
/**
 * AC017_「履歴-前へアイコンボタン」押下
 * AC018_「履歴-次へアイコンボタン」押下
 *
 * @param pageFlag - 0:選択している期間ID画面 | 1:選択している期間ID画面の前画面 | 2:選択している期間ID画面の後画面
 */
const assessmentInterRAIAHistoryChangeSelect = async (pageFlag: string) => {
  isLoading.value = true
  // バックエンドAPIから初期情報取得
  const inputData: IAssessmentInterRAIHistoryChangeSelectInEntity = {
    svJigyoId: local.svJigyoId ?? TeX0003Const.STR.EMPTY,
    userId: local.userId ?? TeX0003Const.STR.EMPTY,
    sc1Id: local.planPeriod?.sc1Id ?? TeX0003Const.STR.ZERO,
    raiId: local.history?.raiId ?? TeX0003Const.STR.ZERO,
    kikanFlag: pageFlag,
  }

  const resp: IAssessmentInterRAIHistoryChangeSelectOutEntity = await ScreenRepository.select(
    'assessmentInterRAIHistoryChangeSelect',
    inputData
  )

  if (SUCCESS_STATUS_CODE === resp.statusCode || 'success' === resp.statusCode) {
    if(resp.data.historyInfo) {
      local.history = resp.data.historyInfo
    }
  }
  isLoading.value = false
}
</script>

<template>
  <c-v-sheet class="view d-flex flex-column h-100">
    <!-- ローディング -->
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <!-- 操作ボタンエリア -->
    <div class="action-sticky">
      <g-base-or11871 v-bind="or11871">
        <template #createItems>
          <c-v-list-item
            :title="t('btn.copy')"
            @click="copyBtnClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-copy-btn')"
            />
          </c-v-list-item>
        </template>
        <template #optionMenuItems>
          <c-v-list-item
            v-if="logBtnShowFlg"
            :title="
              t('label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-log')
            "
            prepend-icon="open_in_browser"
            @click="logClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-show-log-btn')"
            />
          </c-v-list-item>
          <c-v-list-item
            :title="
              t('label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-CSV')
            "
            prepend-icon="open_in_browser"
            :disabled="csvOutputBtnDisabledFlg"
            @click="csvClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.csv-output')"
            />
          </c-v-list-item>
          <c-v-list-item
            :title="
              t(
                'label.interRAI-method-care-assessment-table-A-basic-info-section-title-menyu-delete'
              )
            "
            prepend-icon="delete"
            @click="deleteClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.delete-data')"
            />
          </c-v-list-item>
        </template>
      </g-base-or11871>
    </div>
    <!-- アクションエリア -->
    <div class="action-content">
      <c-v-row
        no-gutters
        class="main-Content d-flex flex-0-1 h-100 overflow-y-auto position-relative"
      >
        <!-- ナビゲーションエリア -->
        <c-v-col
          cols="auto"
          class="hidden-scroll h-100 mt-1 ml-6 main-left"
        >
          <!-- 利用者選択一覧 -->
          <g-base-or00248
            v-bind="or00248"
            class="userList"
          />
        </c-v-col>
        <!-- コンテンツエリア -->
        <c-v-col class="main-right d-flex flex-column overflow-hidden h-100">
          <!-- 上段 -->
          <c-v-row
            no-gutters
            class="d-flex align-center"
          >
            <!-- 事業所 -->
            <c-v-col
              cols="auto mr-6 office-select"
              style="width: 220px"
            >
              <g-base-or41179 v-bind="or41179" />
            </c-v-col>
            <c-v-col
              v-if="planPeriodShow"
              cols="auto mr-6"
            >
              <!-- 計画対象期間 -->
              <g-custom-or13844
                v-bind="or13844"
                :oneway-model-value="localOneway.planningPeriodSelectOneway"
                @open-btn-click="planningPeriodSelectClick"
              ></g-custom-or13844>
            </c-v-col>
            <!-- 基準日 -->
            <c-v-col
              v-if="baseDateShow"
              cols="auto mr-6"
            >
              <base-mo00020
                :model-value="local.kijunbiYmd"
                :oneway-model-value="localOneway.createDateOneway"
              />
            </c-v-col>
            <!-- 作成者 -->
            <c-v-col
              v-if="authorShow"
              cols="auto mr-6"
              style="width: 160px"
            >
              <g-custom-or-x-0157
                v-model="local.orX0157"
                :oneway-model-value="localOneway.authorSelectOneway"
                @on-click-edit-btn="authorSelectClick"
              />
            </c-v-col>
            <!-- 履歴 -->
            <c-v-col
              v-if="historyShow"
              cols="auto"
            >
              <g-custom-or13872
                v-bind="or13872"
                :oneway-model-value="localOneway.hisotrySelectOneway"
                @open-btn-click="historySelectClick"
              ></g-custom-or13872>
            </c-v-col>
          </c-v-row>
          <!-- コンテンツエリアタブ -->
          <c-v-row
            no-gutters
            class="mt-6"
          >
            <c-v-col class="tabItems">
              <base-mo00043
                :model-value="local.mo00043"
                :oneway-model-value="localOneway.mo00043OnewayType"
                style="padding-left: 0px !important; padding-right: 0px !important"
                @update:model-value="updateModelValue"
              >
              </base-mo00043>
            </c-v-col>
          </c-v-row>
          <!-- 中段 -->
          <c-v-row
            no-gutters
            class="middleContent flex-1-1 h-100"
          >
            <c-v-window
              v-model="local.mo00043.id"
              class="h-100"
            >
              <c-v-window-item value="A">
                <g-custom-or31000
                  v-bind="or31000"
                  @save-end="saveEnd"
                ></g-custom-or31000>
              </c-v-window-item>
              <c-v-window-item value="B">
                <g-custom-or31369
                  v-bind="or31369"
                  @save-end="saveEnd"
                ></g-custom-or31369>
              </c-v-window-item>
              <c-v-window-item value="C">
                <g-custom-or31124
                  v-bind="or31124"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="D">
                <g-custom-or31654
                  v-bind="or31654"
                  :oneway-model-value="{ surveyAssessmentKind: TeX0003Const.STR.ONE }"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="E">
                <g-custom-or-31909
                  v-bind="or31909"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="F">
                <g-custom-or-31791
                  v-bind="or31791"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="G">
                <g-custom-or-31432
                  v-bind="or31432"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="H">
                <g-custom-or-28348
                  v-bind="or28348"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="I-1">
                <g-custom-or-30341
                  v-bind="or30341"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="I-2">
                <g-custom-or31995
                  v-bind="or31995"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="J">
                <g-custom-or11158
                  v-bind="or11158"
                  @save-end="saveEnd"
                ></g-custom-or11158>
              </c-v-window-item>
              <c-v-window-item value="K">
                <g-custom-or32240
                  v-bind="or32240"
                  @save-end="saveEnd"
                ></g-custom-or32240
              ></c-v-window-item>
              <c-v-window-item value="L">
                <g-custom-or-31806
                  v-bind="or31806"
                  :oneway-model-value="{ surveyAssessmentKind: TeX0003Const.STR.ONE }"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="M">
                <g-custom-or-31524
                  v-bind="or31524"
                  :oneway-model-value="{ surveyAssessmentKind: TeX0003Const.STR.THREE }"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="N">
                <g-custom-or-32004
                  v-bind="or32004"
                  :oneway-model-value="{ surveyAssessmentKind: TeX0003Const.STR.THREE }"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="O">
                <g-custom-or11162
                  v-bind="or11162"
                  @save-end="saveEnd"
                ></g-custom-or11162
              ></c-v-window-item>
              <c-v-window-item value="P">
                <g-custom-or-31946
                  v-bind="or31946"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="Q">
                <g-custom-or32625
                  v-bind="or32625"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="R"
                ><g-custom-or30621
                  v-bind="or30621"
                  @save-end="saveEnd"
                ></g-custom-or30621
              ></c-v-window-item>
              <c-v-window-item value="S">
                <g-custom-or31972
                  v-bind="or31972"
                  @save-end="saveEnd"
                ></g-custom-or31972>
              </c-v-window-item>
              <c-v-window-item value="T">
                <g-custom-or-33763
                  v-bind="or33763"
                  :oneway-model-value="{ surveyAssessmentKind: TeX0003Const.STR.ONE }"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="U">
                <g-custom-or-31168
                  v-bind="or31168"
                  :oneway-model-value="{ surveyAssessmentKind: TeX0003Const.STR.TWO }"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
              <c-v-window-item value="V">
                <g-custom-or-32289
                  v-bind="or32289"
                  :oneway-model-value="{ surveyAssessmentKind: TeX0003Const.STR.ONE }"
                  @save-end="saveEnd"
                />
              </c-v-window-item>
            </c-v-window>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </div>
  </c-v-sheet>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813 v-bind="or21813" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />

  <!-- GUI00627 アセスメント（インターライ）マスタ -->
  <!-- GUI00420 病名マスタ -->
  <!-- GUI00671 薬剤マスタ -->
  <!-- GUI02353 薬剤単位マスタ -->
  <g-custom-or-53417
    v-if="showDialogOr53417"
    v-bind="or53417"
    :oneway-model-value="localOneway.or53417Oneway"
  />

  <!-- GUI00788 ［アセスメント（インターライ）CSV出力］画面-->
  <g-custom-or-10279
    v-if="showDialogOr10279"
    v-bind="or10279"
    :oneway-model-value="localOneway.Or10279Oneway"
  />

  <!-- GUI00070 対象期間画面 -->
  <g-custom-or-x-0115
    v-if="showDialogOrX0115"
    v-bind="orx0115"
    :oneway-model-value="localOneway.orX0115Oneway"
  ></g-custom-or-x-0115>

  <!-- メッセージ「i-cmn-11260」 -->
  <!--  削除確認画面 -->
  <g-custom-or-x-0001
    v-if="showDialogOrX0001"
    v-bind="orX0001"
    v-model="orX0001Type"
    :oneway-model-value="orX0001Oneway"
  ></g-custom-or-x-0001>

  <!-- GUI00792 ［履歴選択］画面 -->
  <g-custom-or-10929
    v-if="showDialogOr10929"
    v-bind="or10929"
    v-model="or10929Type"
    :oneway-model-value="localOneway.or10929Oneway"
    @update:model-value="historySelectChange"
  />
  <!-- GUI00793 印刷設定画面 -->
  <g-custom-or-10269
    v-if="showDialogOr10269"
    v-bind="or10269"
  />
  <!-- GUI04512_アセスメント（インターライ）複写画面 -->
  <g-custom-or-x-0134
    v-if="showDialogOrX0134"
    v-bind="orX0134"
    v-model="orX0134Type"
    :oneway-model-value="localOneway.orX0134OnewayModel"
    @update:model-value="handleReturn"
  />
  <!-- GUI00220_職員検索画面 -->
  <g-custom-or-26257
    v-if="showDialogOr26257"
    v-bind="or26257"
    v-model="or26257Type"
    :oneway-model-value="localOneway.or26257OnewayModel"
  />
</template>

<style scoped lang="scss">
.view {
  background-color: transparent;
  height: max-content !important;
}

.main-Content {
  .main-left {
    max-width: 20%;

    .userList {
      :deep(.v-list) {
        max-height: 1019px !important;
      }
    }
  }

  .main-right {
    .middleContent {
      min-height: 0;

      .v-window {
        width: 100%;
      }
    }
  }
}

:deep(.rounded.item-label) {
  min-width: 180px;
  padding-left: 8px !important;
}
:deep(.office-select .v-sheet) {
  background-color: transparent !important;
  .v-input__control {
    background-color: #fff;
  }
}
.action-sticky {
  position: sticky;
  top: 48px;
  z-index: 999;
  background: rgb(var(--v-theme-background));
  width: 100%;
}
.action-content {
  background: rgb(var(--v-theme-background));
  height: max-content;
  z-index: 888;
  padding-bottom: 104px;
}
.createDateOuterClass {
  width: auto !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }

  :deep(.v-input__details) {
    display: none;
  }
  :deep(.v-input__control) {
    background-color: #fff;
  }
}
.tabItems {
  :deep(.v-slide-group__prev) {
    display: none;
  }

  :deep(.v-slide-group__next) {
    display: none;
  }

  :deep(.v-btn--variant-text),
  :deep(.v-slide-group__content),
  :deep(.v-slide-group__container),
  :deep(.v-tabs) {
    height: 36px !important;
  }
}
</style>
