<script lang="ts" setup>
/**
 * Or05019:有機体:検討表
 * GUI00872_検討表
 *
 * @description
 * GUI00872_検討表
 *
 * <AUTHOR> DAO DINH DUONG
 */
/**************************************************
 * インポート(Imports)
 **************************************************/
import { reactive, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Or05019TwoWayType } from './Or05019.type'
import { Or05019Const } from './Or05019.constants'
import { Or28409Const } from '~/components/custom-components/organisms/Or28409/Or28409.constants'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type { TextAreaType } from '~/components/custom-components/organisms/Or05019/Or05019.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or05019OnewayType } from '~/types/cmn/business/components/Or05019Type'
import { useSetupChildProps, useValidation, useScreenTwoWayBind } from '#imports'

const { t } = useI18n()
const { byteLength } = useValidation()
const systemCommonsStore = useSystemCommonsStore()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or05019OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()
const or51775 = ref({ uniqueCpId: '' })
const or28409 = ref({ uniqueCpId: '' })
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or28409Const.CP_ID(0)]: or28409.value,
})

const { refValue } = useScreenTwoWayBind<Or05019TwoWayType>({
  cpId: Or05019Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
const localOneway = reactive({
  mo00039Oneway: {
    showItemLabel: false,
    hideDetails: true,
    checkOff: false,
  } as Mo00039OnewayType,
  mo00046: {
    showItemLabel: false,
    hideDetails: true,
    maxlength: '4000',
    rules: [byteLength(4000)],
  } as Mo00046OnewayType,
  mo00038Oneway: {
    mo00045Oneway: {
      showItemLabel: false,
      width: '42px',
      maxLength: '2',
    },
  } as Mo00038OnewayType,
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    size: '36px',
    color: '#859FC9',
    labelColor: '#EBF2FD',
    density: 'default',
  } as Mo00009OnewayType,
  or51775Oneway: {
    title: t('label.special-note-matter-support'),
    screenId: 'GUI00872',
    bunruiId: '',
    t1Cd: '501',
    t2Cd: '',
    t3Cd: '0',
    tableName: 'cpn_tuc_hkd2',
    columnName: 'tokki"',
    assessmentMethod: systemCommonsStore.getAuthzOtherCaremanager?.assessmentMethod ?? '',
    inputContents: '',
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
})
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
let selectTextFiled: TextAreaType
const openGUI937 = (textFiled: TextAreaType, t2Cd: string) => {
  localOneway.or51775Oneway = {
    ...localOneway.or51775Oneway,
    t2Cd,
  }
  selectTextFiled = textFiled
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}
const handleSetText = (data: Or51775ConfirmType) => {
  if (data.type === '1') {
    selectTextFiled.value.value = data.value
  } else {
    selectTextFiled.value.value += data.value
  }
}
const options = ref([
  { value: '0', label: t('btn.week') },
  { value: '1', label: t('btn.month') },
  { value: '2', label: t('label.year') },
])

/**************************************************
 * Pinia
 **************************************************/
/**
 *  画面初期情報取得
 */
const socialActivityParticipation = reactive([
  {
    title1: t('趣味'),
    field11: 'hobbyExists',
    field12: 'hobbyNone',
    title2: t('老人クラブの参加'),
    field21: 'seniorClubParticipationExists',
    field22: 'seniorClubParticipationNone',
  },
  {
    title1: t('散歩'),
    field11: 'walkingExists',
    field12: 'walkingNone',
    title2: t('買物'),
    field21: 'shoppingExists',
    field22: 'shoppingNone',
  },
  {
    title1: t('付き合い（近所、友人）'),
    field11: 'socializingWithNeighborsFriendsExists',
    field12: 'socializingWithNeighborsFriendsNone',
    title2: t('その他の活動'),
    field21: 'otherActivitiesExists',
    field22: 'otherActivitiesNone',
  },
])
const availableSocialResources = reactive([
  {
    hide1: false,
    title1: t('訪問介護（ホームヘルプサービス）'),
    field11: 'homeHelpServiceInLocalArea',
    field12: 'homeHelpServiceWillingToUse',
    hide2: false,
    title2: t('福祉用具貸与'),
    field21: 'welfareEquipmentRentalInLocalArea',
    field22: 'welfareEquipmentRentalWillingToUse',
  },
  {
    hide1: false,
    title1: t('訪問入浴介護'),
    field11: 'homeBathingServiceInLocalArea',
    field12: 'homeBathingServiceWillingToUse',
    hide2: false,
    title2: t('短期入所生活介護（特養）'),
    field21: 'shortStayCareFacilityInLocalArea',
    field22: 'shortStayCareFacilityWillingToUse',
  },
  {
    hide1: false,
    title1: t('label.home-visit-with-nursing'),
    field11: 'homeNursingServiceInLocalArea',
    field12: 'homeNursingServiceWillingToUse',
    hide2: false,
    title2: t('短期入所生活介護（老健等）'),
    field21: 'shortStayRehabFacilityInLocalArea',
    field22: 'shortStayRehabFacilityWillingToUse',
  },
  {
    hide1: false,
    title1: t('訪問リハビリテーション'),
    field11: 'homeRehabilitationServiceInLocalArea',
    field12: 'homeRehabilitationServiceWillingToUse',
    hide2: false,
    title2: t('認識症対応型共同生活介護'),
    field21: 'dementiaCareGroupHomeInLocalArea',
    field22: 'dementiaCareGroupHomeWillingToUse',
  },
  {
    hide1: false,
    title1: t('居宅療養管理指導'),
    field11: 'homeMedicalManagementInLocalArea',
    field12: 'homeMedicalManagementWillingToUse',
    hide2: false,
    title2: t('特定施設入所者生活介護'),
    field21: 'specifiedFacilityResidentCareInLocalArea',
    field22: 'specifiedFacilityResidentCareWillingToUse',
  },
  {
    hide1: false,
    title1: t('通所介護（デイサービス）'),
    field11: 'dayCareServiceInLocalArea',
    field12: 'dayCareServiceWillingToUse',
    hide2: false,
    title2: t('福祉用具購入'),
    field21: 'welfareEquipmentPurchaseInLocalArea',
    field22: 'welfareEquipmentPurchaseWillingToUse',
  },
  {
    hide1: false,
    title1: t('通所リハビリテーション（デイケア）'),
    field11: 'dayRehabilitationServiceInLocalArea',
    field12: 'dayRehabilitationServiceWillingToUse',
    hide2: false,
    title2: t('住宅改修'),
    field21: 'housingRenovationInLocalArea',
    field22: 'housingRenovationWillingToUse',
  },
  {
    hide1: true,
    title1: '',
    field11: null,
    field12: null,
    hide2: false,
    title2: t('その他'),
    field21: 'otherService1InLocalArea',
    field22: 'otherService1WillingToUse',
  },
  {
    hide1: false,
    title1: t('介護老人福祉施設'),
    field11: 'elderlyWelfareFacilityInLocalArea',
    field12: 'elderlyWelfareFacilityWillingToUse',
    hide2: true,
    title2: '',
    field21: null,
    field22: null,
  },
  {
    hide1: false,
    title1: t('介護老人保健施設'),
    field11: 'elderlyRehabFacilityInLocalArea',
    field12: 'elderlyRehabFacilityWillingToUse',
    hide2: true,
    title2: '',
    field21: null,
    field22: null,
  },
  {
    hide1: false,
    title1: t('介護療養型医療施設'),
    field11: 'medicalCareFacilityInLocalArea',
    field12: 'medicalCareFacilityWillingToUse',
    hide2: true,
    title2: '',
    field21: null,
    field22: null,
  },
  {
    hide1: false,
    title1: t('その他'),
    field11: 'otherService2InLocalArea',
    field12: 'otherService2WillingToUse',
    hide2: true,
    title2: '',
    field21: null,
    field22: null,
  },
])
const familyGuidance = reactive([
  {
    title: t('介護方法の指導'),
    field1: 'careMethodInstructionReceived',
    field2: 'careMethodInstructionNotReceived',
    option1: t('受けた'),
    option2: t('受けていない'),
  },
  {
    title: t('社会資源の利用方法'),
    field1: 'socialResourceUsageKnown',
    field2: 'socialResourceUsageUnknown',
    option1: t('知っている'),
    option2: t('知らない'),
  },
  {
    title: t('社会資源の利用費用'),
    field1: 'socialResourceCostKnown',
    field2: 'socialResourceCostUnknown',
    option1: t('知っている'),
    option2: t('label.radio-no'),
  },
])
</script>
<template>
  <c-v-sheet class="container mt-5">
    <c-v-row
      no-gutters
      class="justify-center"
    >
      <c-v-col cols="12">
        <div class="title-main">１. {{ t('label.info-collection-nursing-care-ability') }}</div>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('label.info-collection-main-caregiver'),
              }"
            >
              <template #content>
                <c-v-row
                  class="pl-3"
                  no-gutters
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.mainCaregiverExists.value"
                    v-model:model-value2="refValue!.noMainCaregiver.value"
                    :title1="t('label.radio-yes')"
                    :title2="t('label.radio-no')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width"
              :oneway-model-value="{
                title: t('label.info-collection-auxiliary-caregiver'),
              }"
            >
              <template #content>
                <c-v-row
                  class="pl-3"
                  no-gutters
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.hasSubCaregiver.value"
                    v-model:model-value2="refValue!.hasNoSubCaregiver.value"
                    :title1="t('label.radio-yes')"
                    :title2="t('label.radio-no')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('介護意欲'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-3"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.careMotivationActive.value"
                    v-model:model-value2="refValue!.careMotivationPassive.value"
                    :title1="t('積極的')"
                    :title2="t('消極的')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col
            class="center-block"
            cols="6"
          >
            <base-mo01299
              class="half-title-width"
              :oneway-model-value="{
                title: t('介護期間'),
              }"
            >
              <template #content>
                <c-v-row
                  align="center"
                  class="pl-3"
                  style="gap: 8px"
                  no-gutters
                >
                  (
                  <base-mo00038
                    v-model="refValue!.carePeriodYear.value"
                    class="input-number"
                    :oneway-model-value="localOneway.mo00038Oneway"
                  />
                  {{ t('年') }}
                  <base-mo00038
                    v-model="refValue!.carePeriodMonth.value"
                    class="input-number"
                    :oneway-model-value="localOneway.mo00038Oneway"
                  />
                  {{ t('月') }}
                  )
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('主介護者の健康状態'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-3"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.mainCaregiverHealthGood.value"
                    v-model:model-value2="refValue!.mainCaregiverHealthPoor.value"
                    :title1="t('よい')"
                    :title2="t('悪い')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width"
              :oneway-model-value="{
                title: t('主介護者との家族関係'),
              }"
            >
              <template #content>
                <c-v-row
                  class="pl-3"
                  no-gutters
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.mainCaregiverFamilyRelationGood.value"
                    v-model:model-value2="refValue!.mainCaregiverFamilyRelationPoor.value"
                    :title1="t('よい')"
                    :title2="t('悪い')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('主介護者の就労'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-3"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.mainCaregiverEmploymentNone.value"
                    v-model:model-value2="refValue!.mainCaregiverEmploymentExists.value"
                    :title1="t('label.radio-no')"
                    :title2="t('label.radio-yes')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width"
              :oneway-model-value="{
                title: t('主介護者の育児'),
              }"
            >
              <template #content>
                <c-v-row
                  class="pl-3"
                  no-gutters
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.mainCaregiverChildcareNone.value"
                    v-model:model-value2="refValue!.mainCaregiverChildcareExists.value"
                    :title1="t('label.radio-no')"
                    :title2="t('label.radio-yes')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col
            class="center-block"
            cols="6"
          >
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('介護可能時間'),
              }"
            >
              <template #content>
                <c-v-row
                  align="center"
                  style="gap: 8px"
                  class="pl-3"
                  no-gutters
                >
                  (
                  <base-mo00038
                    v-model="refValue!.careAvailableTime.value"
                    class="input-number"
                    :oneway-model-value="localOneway.mo00038Oneway"
                  />
                  )
                  {{ t('時間／日') }}
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width"
              :oneway-model-value="{
                title: t('身体的負担感'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-3"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.physicalBurdenNone.value"
                    v-model:model-value2="refValue!.physicalBurdenExists.value"
                    :title1="t('label.radio-no')"
                    :title2="t('label.radio-yes')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('精神的負担感'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-3"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.mentalBurdenNone.value"
                    v-model:model-value2="refValue!.mentalBurdenExists.value"
                    :title1="t('label.radio-no')"
                    :title2="t('label.radio-yes')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width"
              :oneway-model-value="{
                title: t('経済的負担感'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-3"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.financialBurdenNone.value"
                    v-model:model-value2="refValue!.financialBurdenExists.value"
                    :title1="t('label.radio-no')"
                    :title2="t('label.radio-yes')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col
            class="center-block"
            cols="6"
          >
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('面会の頻度'),
              }"
            >
              <template #content>
                <div
                  class="px-2"
                  style="width: 100%"
                >
                  <c-v-row
                    align="center"
                    style="gap: 8px"
                    no-gutters
                  >
                    (
                    <base-mo00038
                      v-model="refValue!.visitFrequency.value"
                      class="input-number"
                      :oneway-model-value="localOneway.mo00038Oneway"
                    />
                    )
                    {{ t('回') }}／
                  </c-v-row>
                  <c-v-row
                    align="center"
                    justify="end"
                    no-gutters
                  >
                    <base-mo00039
                      v-model="refValue!.visitFrequencyUnit.value"
                      style="margin-right: 0 !important; display: flex; align-items: center"
                      :oneway-model-value="localOneway.mo00039Oneway"
                    >
                      <base-at-radio
                        v-for="item in options"
                        :radio-label="item.label"
                        :value="item.value"
                      />
                    </base-mo00039>
                  </c-v-row>
                </div>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width"
              :oneway-model-value="{
                title: t('外出＋外泊の頻度'),
              }"
            >
              <template #content>
                <div
                  class="px-2"
                  style="width: 100%"
                >
                  <c-v-row
                    align="center"
                    style="gap: 8px"
                    no-gutters
                  >
                    (
                    <base-mo00038
                      v-model="refValue!.outingOvernightFrequency.value"
                      class="input-number"
                      :oneway-model-value="localOneway.mo00038Oneway"
                    />
                    )
                    {{ t('回') }}／
                  </c-v-row>
                  <c-v-row
                    align="center"
                    justify="end"
                    no-gutters
                  >
                    <base-mo00039
                      v-model="refValue!.outingOvernightFrequencyUnit.value"
                      style="margin-right: 0 !important; display: flex; align-items: center"
                      :oneway-model-value="localOneway.mo00039Oneway"
                    >
                      <base-at-radio
                        v-for="item in options"
                        :radio-label="item.label"
                        :value="item.value"
                      />
                    </base-mo00039>
                  </c-v-row>
                </div>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <base-mo01299
          class="half-title-width text-section border-b-none"
          :oneway-model-value="{
            title: t('label.special-note-matter-support'),
          }"
        >
          <template #content>
            <c-v-row
              style="height: 100%; padding: 16px 8px !important"
              align="center"
              no-gutters
            >
              <g-custom-or-x-0163
                v-model="refValue!.careAbilitySpecialNotes.value"
                @on-click-edit-btn="openGUI937(refValue!.careAbilitySpecialNotes, '1')"
                :oneway-model-value="localOneway.mo00046"
                class="text-area h-100 w-100"
                v-bind="{ ...$attrs }"
              />
            </c-v-row>
          </template>
        </base-mo01299>
        <div class="title-main">２. {{ t('label.living-environment') }}</div>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('福祉用具の利用'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-7"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.welfareEquipmentUseExists.value"
                    v-model:model-value2="refValue!.welfareEquipmentUseNone.value"
                    :title1="t('label.radio-yes')"
                    :title2="t('label.radio-no')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-b-none"
              :oneway-model-value="{
                title: t('福祉用具の充足度'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-3"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.welfareEquipmentSufficiencyAdequate.value"
                    v-model:model-value2="refValue!.welfareEquipmentSufficiencyInadequate.value"
                    :title1="t('充足')"
                    :title2="t('不足')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('住宅改修の必要性'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-7"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.housingRenovationNeedNone.value"
                    v-model:model-value2="refValue!.housingRenovationNeedRequired.value"
                    :title1="t('不要')"
                    :title2="t('必要')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col
            class="bg-white custom-border"
            cols="6"
          >
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <base-mo01299
            class="half-title-width width-20"
            :oneway-model-value="{
              title: t('住宅環境'),
            }"
          >
            <template #content>
              <c-v-row
                align="center"
                no-gutters
                class="pl-7"
              >
                <g-custom-or-05020
                  v-model:model-value1="refValue!.housingOwnershipOwned.value"
                  v-model:model-value2="refValue!.housingOwnershipRented.value"
                  :title1="t('持ち家')"
                  :title2="t('借家')"
                  :cols="'auto'"
                />
                <g-custom-or-05020
                  v-model:model-value1="refValue!.housingTypeDetached.value"
                  v-model:model-value2="refValue!.housingTypeApartment.value"
                  :title1="t('一戸建て')"
                  :title2="t('集合住宅')"
                  :cols="'auto'"
                />
                <g-custom-or-05020
                  v-model:model-value1="refValue!.hasPrivateRoom.value"
                  v-model:model-value2="refValue!.hasNoPrivateRoom.value"
                  :title1="t('自室あり')"
                  :title2="t('自室なし')"
                  :cols="'auto'"
                />
                <g-custom-or-05020
                  v-model:model-value1="refValue!.isFirstFloor.value"
                  v-model:model-value2="refValue!.isSecondFloorOrAbove.value"
                  :title1="t('１階')"
                  :title2="t('２階以上')"
                  :cols="'auto'"
                />
              </c-v-row>
            </template>
          </base-mo01299>
        </c-v-row>
        <base-mo01299
          class="half-title-width text-section border-b-none"
          :oneway-model-value="{
            title: t('label.special-note-matter-support'),
          }"
        >
          <template #content>
            <c-v-row
              style="height: 100%; padding: 16px 8px !important"
              align="center"
              no-gutters
            >
              <g-custom-or-x-0163
                v-model="refValue!.livingEnvironmentSpecialNotes.value"
                @on-click-edit-btn="openGUI937(refValue!.livingEnvironmentSpecialNotes, '2')"
                :oneway-model-value="localOneway.mo00046"
                class="text-area w-100"
                v-bind="{ ...$attrs }"
              />
            </c-v-row>
          </template>
        </base-mo01299>
        <div class="title-main">３. {{ t('label.available-social-resources') }}</div>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <base-mo01299 class="half-title-width no-padding border-r-none">
              <template #content>
                <c-v-row
                  class="h-100"
                  no-gutters
                >
                  <c-v-col
                    cols="6"
                    class="status-label custom-border-l"
                    >{{ t('居住地域にある') }}</c-v-col
                  >
                  <c-v-col
                    cols="6"
                    class="status-label custom-border-l"
                    >{{ t('利用する意志あり') }}</c-v-col
                  >
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col cols="6">
            <base-mo01299 class="half-title-width no-padding">
              <template #content>
                <c-v-row
                  class="h-100"
                  no-gutters
                >
                  <c-v-col
                    cols="6"
                    class="status-label custom-border-l"
                    >{{ t('居住地域にある') }}</c-v-col
                  >
                  <c-v-col
                    cols="6"
                    class="status-label custom-border-l"
                    >{{ t('利用する意志あり') }}</c-v-col
                  >
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <c-v-row
          v-for="(item, index) in availableSocialResources"
          no-gutters
        >
          <c-v-col
            :class="{
              'bg-white': item.hide1,
              'custom-border-l': item.hide1,
              'custom-border-b': item.hide1,
            }"
            cols="6"
          >
            <base-mo01299
              v-if="!item.hide1"
              class="half-title-width"
              :class="{
                'border-r-none': !item.hide2,
              }"
              :oneway-model-value="{
                title: item.title1,
              }"
            >
              <template #content>
                <c-v-row
                  class="h-100"
                  no-gutters
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue![item.field11!].value"
                    v-model:model-value2="refValue![item.field12!].value"
                    :is-radio-type="false"
                    class-name="bg-white center-container"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col
            :class="{
              'bg-white': item.hide2,
              'custom-border-r': item.hide2,
              'custom-border-b': index === availableSocialResources.length - 1,
            }"
            cols="6"
          >
            <base-mo01299
              v-if="!item.hide2"
              class="half-title-width"
              :oneway-model-value="{
                title: item.title2,
              }"
            >
              <template #content>
                <c-v-row
                  class="h-100"
                  no-gutters
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue![item.field21!].value"
                    v-model:model-value2="refValue![item.field22!].value"
                    :is-radio-type="false"
                    class-name="bg-white center-container"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <base-mo01299
          class="half-title-width text-section border-b-none"
          :oneway-model-value="{
            title: t('label.special-note-matter-support'),
          }"
        >
          <template #content>
            <c-v-row
              style="height: 100%; padding: 16px 8px !important"
              align="center"
              no-gutters
            >
              <g-custom-or-x-0163
                v-model="refValue!.availableSocialResourcesSpecialNotes.value"
                @on-click-edit-btn="openGUI937(refValue!.availableSocialResourcesSpecialNotes, '3')"
                :oneway-model-value="localOneway.mo00046"
                class="text-area w-100"
                v-bind="{ ...$attrs }"
              />
            </c-v-row>
          </template>
        </base-mo01299>
        <div class="title-main">４. {{ t('label.participation-in-social-activities') }}</div>
        <c-v-row
          v-for="(item, index) in socialActivityParticipation"
          :key="index"
          no-gutters
        >
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: item.title1,
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-7"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue![item.field11].value"
                    v-model:model-value2="refValue![item.field12].value"
                    :title1="t('label.radio-yes')"
                    :title2="t('label.radio-no')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width"
              :oneway-model-value="{
                title: item.title2,
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-3"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue![item.field21].value"
                    v-model:model-value2="refValue![item.field22].value"
                    :title1="t('label.radio-yes')"
                    :title2="t('label.radio-no')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <base-mo01299
          class="half-title-width text-section border-b-none"
          :oneway-model-value="{
            title: t('label.special-note-matter-support'),
          }"
        >
          <template #content>
            <c-v-row
              style="height: 100%; padding: 16px 8px !important"
              align="center"
              no-gutters
            >
              <g-custom-or-x-0163
                v-model="refValue!.socialParticipationSpecialNotes.value"
                @on-click-edit-btn="openGUI937(refValue!.socialParticipationSpecialNotes, '4')"
                :oneway-model-value="localOneway.mo00046"
                class="text-area w-100"
                v-bind="{ ...$attrs }"
              />
            </c-v-row>
          </template>
        </base-mo01299>
        <div class="title-main">５. {{ t('label.emergency-response') }}</div>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('主治医'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-7"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.attendingPhysicianExists.value"
                    v-model:model-value2="refValue!.attendingPhysicianNone.value"
                    :title1="t('label.radio-yes')"
                    :title2="t('label.radio-no')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width"
              :oneway-model-value="{
                title: t('入院受け入れ先'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-3"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.hospitalAdmissionDestinationExists.value"
                    v-model:model-value2="refValue!.hospitalAdmissionDestinationNone.value"
                    :title1="t('label.radio-yes')"
                    :title2="t('label.radio-no')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col cols="6">
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: t('家族間の連絡網'),
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-7"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue!.familyCommunicationNetworkExists.value"
                    v-model:model-value2="refValue!.familyCommunicationNetworkNone.value"
                    :title1="t('label.radio-yes')"
                    :title2="t('label.radio-no')"
                    cols="5"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col style="background-color: #fff; border:#ccc solid 1px; border-top: none;" cols="6">
          </c-v-col>
        </c-v-row>
        <base-mo01299
          class="half-title-width text-section border-b-none"
          :oneway-model-value="{
            title: t('label.special-note-matter-support'),
          }"
        >
          <template #content>
            <c-v-row
              style="height: 100%; padding: 16px 8px !important"
              align="center"
              no-gutters
            >
              <g-custom-or-x-0163
                v-model="refValue!.emergencyResponseSpecialNotes.value"
                @on-click-edit-btn="openGUI937(refValue!.emergencyResponseSpecialNotes, '5')"
                :oneway-model-value="localOneway.mo00046"
                class="text-area w-100"
                v-bind="{ ...$attrs }"
              />
            </c-v-row>
          </template>
        </base-mo01299>
        <div class="title-main">６. {{ t('label.guidance-for-family') }}</div>
        <c-v-row
          v-for="(item, index) in familyGuidance"
          :key="index"
          no-gutters
        >
          <c-v-col cols="6" >
            <base-mo01299
              class="half-title-width border-r-none"
              :oneway-model-value="{
                title: item.title,
              }"
            >
              <template #content>
                <c-v-row
                  no-gutters
                  class="pl-7"
                >
                  <g-custom-or-05020
                    v-model:model-value1="refValue![item.field1].value"
                    v-model:model-value2="refValue![item.field2].value"
                    :title1="item.option1"
                    :title2="item.option2"
                  />
                </c-v-row>
              </template>
            </base-mo01299>
          </c-v-col>
          <c-v-col
            class="bg-white custom-border-l custom-border-r"
            :class="index === familyGuidance.length -1 ? 'custom-border-b' : ''"
            cols="6"
          />
        </c-v-row>
        <base-mo01299
          class="half-title-width text-section"
          :oneway-model-value="{
            title: t('label.special-note-matter-support'),
          }"
        >
          <template #content>
            <c-v-row
              style="height: 100%; padding: 16px 8px !important"
              align="center"
              no-gutters
            >
              <g-custom-or-x-0163
                v-model="refValue!.familyInstructionSpecialNotes.value"
                @on-click-edit-btn="openGUI937(refValue!.familyInstructionSpecialNotes, '6')"
                :oneway-model-value="localOneway.mo00046"
                class="text-area w-100"
                v-bind="{ ...$attrs }"
              />
            </c-v-row>
          </template>
        </base-mo01299>
      </c-v-col>
    </c-v-row>
    <g-custom-or-51775
      v-if="showDialogOr51775"
      @confirm="handleSetText"
      :oneway-model-value="localOneway.or51775Oneway"
      v-bind="or51775"
    />
  </c-v-sheet>
</template>
<style lang="scss" scoped>
.container {
  background-color: transparent;
  padding-bottom: 16px;
  width: 1080px;
}
.title-main {
  height: 48px;
  font-weight: 700;
  padding: 8px 24px;
  color: #333333;
  font-size: 16px;
  background-color: #e6e6e6;
  display: flex;
  align-items: center;
  border: 0.5px solid #0000001a;
}
:deep(.half-title-width) {
  border-top: none;
  border-color: #cccccc;
  min-height: 48px;
  min-height: 48px;
  &.border-r-none {
    border-right: none;
  }
  &.border-b-none {
    border-bottom: none;
  }
  .section-header {
    max-width: 44% !important;
  }
  &.text-section {
    .section-header {
      height: 109px;
      max-width: 22% !important;
    }
  }
  &.width-20 {
    .section-header {
      max-width: 22% !important;
    }
  }
  .section-label {
    background-color: #dbeefe !important;
    padding: 0 8px;
    label {
      font-weight: 400 !important;
      font-size: 13px !important;
    }
  }
  .radio-group {
    padding: 0 34px !important;
  }
  &.no-padding {
    .section-content {
      padding: 0 !important;
    }
  }
}
:deep(.input-number) {
  .v-field__field {
    height: 30px;
    .v-field__input {
      padding: 0;
      min-height: 30px !important;
      height: 100% !important;
      text-align: center !important;
    }
  }
}
:deep(.center-block) {
  .section-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.text-title {
  font-weight: bold;
  color: rgb(var(--v-theme-text));
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: -0.64px;
}
.custom-border {
  min-height: 48px;
  border: solid 1px #cccccc;
}
.custom-border-l {
  border-left: solid 1px #cccccc;
}
.custom-border-r {
  border-right: solid 1px #cccccc;
}
.custom-border-b {
  border-bottom: solid 1px #cccccc;
}
.title-contaner {
  display: flex;
  align-items: center;
  height: 48px;
  border: solid 1px #cccccc;
  font-weight: 400;
  font-size: 13px;
  color: #333333;
  border-top: none;
  padding-left: 8px !important;
  &.width-20 {
    width: 182px !important;
  }
  &.border-r-none {
    border-right: none;
  }
  &.border-b-none {
    border-bottom: none;
  }
}
.status-label {
  background-color: #dbeefe !important;
  display: flex;
  justify-content: center;
  align-items: center;
}
:deep(.center-container) {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  .v-selection-control__input {
    position: relative;
    right: 20px;
  }
}
</style>
