import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'
import type {
  kikanObj,
  rirekiObj,
  cks52List,
  cks54List,
} from '~/types/cmn/business/components/Or05349Type'

/** エンティティ */
export interface WeekPlanKikanEntity extends InWebEntity {
  /** 取込権限フラグ */
  toriAutuFlg: string
  /** 施設ID */
  shisetuId?: string
  /** 種別ID */
  syubetsuId?: string
  /** 事業者ID */
  svJigyoId: string
  /** 法人ID */
  houjinId?: string
  /** 利用者ID */
  userId: string
  /** 取込元 */
  torikomiMoto: string
  /** Sys略称 */
  sys3Ryaku: string
  /** 計画書仕様 */
  cksFlg: string
  /** 事業所CD */
  defSvJigyoCd: string
  /** 事業所ID */
  defSvJigyoId: string
  /** 職員ID */
  shokuinId: string
  /** 週間計画ID */
  ks51Id?: string
  /** 計画対象期間ID */
  sc1Id: string
  /** 計画期間ページ区分 */
  kikanPage?: string
  /** 履歴ID */
  rirekiId?: string
  /** 履歴ページ区分 */
  rirekiPage?: string
  /** 現在有効期間ID */
  termid?: string
  /** 期間管理フラグ */
  kikanFlag?: string
  /** 作成日 */
  kijunbiYmd?: string
  /** 処理月 */
  syoriYm?: string
}

/**
 * 取得出力エンティティ
 */
export interface WeekPlanKikanOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /** 計画期間情報リスト */
    kikanObj: kikanObj[]
    /** 履歴変更情報 */
    rirekiOutD: [
      {
        /** 新規用有効期間ID */
        termNewId: string
        /** 週間計画履歴リスト */
        rirekiObj: rirekiObj[]
        /** 詳細情報 */
        meisaiOutD: [
          {
            /** モード */
            mode: string
            /** 週間計画詳細リスト */
            cks52List: cks52List[]
            /** 週間計画日常リスト */
            cks54List: cks54List[]
          },
        ]
      },
    ]
  }
}
