/**
 * ［アセスメント］画面（居宅）モーダル
 *
 * @description
 * ［アセスメント］画面（居宅）モーダル
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import defaultData2 from './data/default2.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { assessmentHomeTab4SelectInEntity } from '~/repositories/cmn/entities/AssessmentHome4Entity'
/**
 * ［アセスメント］画面（居宅）
 *
 * @description
 * ［アセスメント］画面（居宅）初期情報データを返却する。
 * dataName："assessmentHomeTab63Select"
 */

export function handler(inEntity: assessmentHomeTab4SelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }

  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
